package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C120S01SDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C120S01S;

/** 個金客戶報表綜合資訊檔 **/
@Repository
public class C120S01SDaoImpl extends LMSJpaDao<C120S01S, String> implements C120S01SDao {

	@Override
	public C120S01S findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C120S01S> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C120S01S> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public C120S01S findByUniqueKey(String mainId, String custId, String dupNo, String dataType, String fileSeq) {
		ISearch search = createSearchTemplete();
		if (mainId != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		}
		if (custId != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		}
		if (dupNo != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		}
		if (dataType != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "dataType", dataType);
		}
		if (fileSeq != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "fileSeq", fileSeq);
		}
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}
	
	@Override
	public List<C120S01S> findByMainIdAndCustIdAndDupNo(String mainId, String custId, String dupNo) {
		
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		return createQuery(search).getResultList();
	}
	
	@Override
	public List<C120S01S> findByList(String mainId, String custId, String dupNo) {
		
		ISearch search = createSearchTemplete();
		if (mainId != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		}
		if (custId != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		}
		if (dupNo != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		}
		List<C120S01S> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C120S01S> findByIdDupDataType(String mainId, String custId, String dupNo, String dataType){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "dataType", dataType);
		if(true){
			search.addOrderBy("fileSeq");	
		}
		List<C120S01S> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C120S01S.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}
}