<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:wicket="http://wicket.apache.org/">
<body>
	<wicket:extend>
		<div class="button-menu funcContainer" id="buttonPanel">
			<button id="btnFilter" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <wicket:message key="button.filter">篩選</wicket:message>
           	</button>
			<button id="btnAdd" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <wicket:message key="button.add">新增</wicket:message>
           	</button>
			<button id="btnView" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <wicket:message key="button.view">調閱</wicket:message>
           	</button>
			<button id="btnDelete" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <wicket:message key="button.delete">刪除</wicket:message>
           	</button>
			<button id="btnExit" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <wicket:message key="button.exit">離開</wicket:message>
           	</button>
		</div>
		<!-- 新增報表 -->
		<div id="add" style="display:none;" >
			<div id="addDiv" >
				<form id="addForm" >
					<table class="tb2" width="100%">
						<tr>
							<td class="hd2" width="30%" rowspan="3"><span><b><wicket:message key="C004M01A.rptType">報送類型</wicket:message></b></span></td>
							<td><label>
								<input type="radio" id="rptType" name="rptType" checked="checked" value="S1"/>
								<wicket:message key="C004M01A.S1">S1</wicket:message>
							</label></td>
						</tr>
						<tr>
							<td><label>
								<input type="radio" id="rptType" name="rptType" checked="checked" value="S2"/>
								<wicket:message key="C004M01A.S2">S2</wicket:message>
							</label></td>
						</tr>
						<tr>
							<td><label>
								<input type="radio" id="rptType" name="rptType" checked="checked" value="S3"/>
								<wicket:message key="C004M01A.S3">S3</wicket:message>
							</label></td>
						</tr>
					</table>
				</form>
			</div>
		</div>
		<!-- 篩選 -->
		<div id="filter" style="display:none;" >
			<div id="filterDiv" >
				<form id="filterForm" >
					<table class="tb2" width="100%">
						<tr>
							<td class="hd2" width="30%"><span><b><wicket:message key="C004M01A.rptType">報送類型</wicket:message></b></span></td>
							<td>
								<input type="checkbox" id="rptType" name="rptType" value="S1" checked="checked"/><wicket:message key="C004M01A.S1">S1</wicket:message>
								<input type="checkbox" id="rptType" name="rptType" value="Q1" checked="checked"/><wicket:message key="C004M01A.Q1">Q1</wicket:message>
								<input type="checkbox" id="rptType" name="rptType" value="S2" checked="checked"/><wicket:message key="C004M01A.S2">S2</wicket:message>
								<input type="checkbox" id="rptType" name="rptType" value="Q2" checked="checked"/><wicket:message key="C004M01A.Q2">Q2</wicket:message>
								<input type="checkbox" id="rptType" name="rptType" value="S3" checked="checked"/><wicket:message key="C004M01A.S3">S3</wicket:message>
								<input type="checkbox" id="rptType" name="rptType" value="Q3" checked="checked"/><wicket:message key="C004M01A.Q3">Q3</wicket:message>
							</td>
						</tr>
						<tr>
							<td class="hd2" width="30%"><span><b><wicket:message key="C004M01A.creator">建立人員編號</wicket:message></b></span></td>
							<td><input type="text" id="creator" name="creator"/></td>
						</tr>
						<tr>
							<td class="hd2" width="30%"><span><b><wicket:message key="C004M01A.createTime">建立日期</wicket:message></b></span></td>
							<td>
								<input id="bgnDate" name="bgnDate" type="text" class="date" />~
								<input id="endDate" name="endDate" type="text" class="date" />
							</td>
						</tr>
					</table>
				</form>
			</div>
		</div>
		<!-- 報送資料 -->
		<div id="detail" style="display:none;" >
			<div id="detailDiv" >
				<form id="detailForm" >
					<input type="hidden" id="oid" name="oid"/>
					<input type="hidden" id="mainId" name="mainId"/>
					<input type="hidden" id="rptType" name="rptType"/>
					<b><wicket:message key="C004M01A.createTime">建立日期</wicket:message>:</b>
					<input type="text" disabled="true" id="createTime" name="createTime"/><br/>
					<b><wicket:message key="C004M01A.date">資料日期</wicket:message>:</b>
					<input type="text" disabled="true" id="bgnDate" name="bgnDate" size="7"/>~<input type="text" disabled="true" id="endDate" name="endDate" size="7"/><br/>
					<b><wicket:message key="C004M01A.creator">建立人員</wicket:message>:</b>
					<input type="text" disabled="true" id="creator" name="creator"/><br/>
					<b><wicket:message key="C004M01A.rptDate">確定報送日期</wicket:message>:</b>
					<input type="text" disabled="true" id="rptDate" name="rptDate"/>
					<hr/>
					<div class="funcContainer" style="display:none;" >
                    	<button id="uploadFile" type="button">
                        	<span class="text-only"><wicket:message key="uploadTxt"><!-- 選擇附加檔案--></wicket:message></span>
                        </button>
                        <button id="deleteFile" type="button">
                            <span class="text-only"><wicket:message key="deleteTxt"><!-- 刪除--></wicket:message></span>
                        </button>
						<div id="fileGrid" name="fileGrid"/>
                    </div>
				</form>
			</div>
		</div>
		<!-- Q畫面 -->
		<div id="Q1" style="display:none;">
			<div id="Q1Div" >
				<form id="Q1Form" >
					<input type="hidden" id="mainId" name="mainId"/>
					<b><wicket:message key="C004M01A.createTime">建立日期</wicket:message>:</b>
					<input type="text" disabled="true" id="createTime" name="createTime"/><br/>
					<b><wicket:message key="C004M01A.date">資料日期</wicket:message>:</b>
					<input type="text" disabled="true" id="bgnDate" name="bgnDate" size="7"/>~<input id="endDate" disabled="true" name="endDate" size="7"/><br/>
					<b><wicket:message key="C004M01A.creator">建立人員</wicket:message>:</b>
					<input type="text" disabled="true" id="creator" name="creator"/><br/>
					<b><wicket:message key="C004M01A.rptDate">確定報送日期</wicket:message>:</b>
					<input type="text" disabled="true" id="rptDate" name="rptDate"/>
					<hr/>
					<div id="Q1Grid" name="Q1Grid"></div>
				</form>
			</div>
		</div>
		<!-- Q3畫面 -->
		<div id="Q3" style="display:none;">
			<div id="Q3Div" >
				<form id="Q3Form" >
					<input type="hidden" id="mainId" name="mainId"/>
					<b><wicket:message key="C004M01A.createTime">建立日期</wicket:message>:</b>
					<input type="text" disabled="true" id="createTime" name="createTime"/><br/>
					<b><wicket:message key="C004M01A.date">資料日期</wicket:message>:</b>
					<input type="text" disabled="true" id="bgnDate" name="bgnDate" size="7"/>~<input id="endDate" disabled="true" name="endDate" size="7"/><br/>
					<b><wicket:message key="C004M01A.creator">建立人員</wicket:message>:</b>
					<input type="text" disabled="true" id="creator" name="creator"/><br/>
					<b><wicket:message key="C004M01A.rptDate">確定報送日期</wicket:message>:</b>
					<input type="text" disabled="true" id="rptDate" name="rptDate"/>
					<hr/>
					<div id="Q3Grid" name="Q3Grid"></div>
				</form>
			</div>
		</div>
		
		<div id="div_S3_chooseDataYm" style="display:none;">
			<form id="S3_chooseDataYmForm" >
				<table>
					<tr>
						<td><wicket:message key="C004M01A.bgnDate">資料起日</wicket:message></td>
						<td>
							<input type="text" name="dataYearB" id="dataYearB" class="digits required" maxlength="4" size="4" />
							─
							<input type="text" name="dataMmB"   id="dataMmB" class="digits required" maxlength="2" size="2" />
						</td>
						<td rowspan='2'>
							<span class="text-red">(YYYY-MM)</span>
						</td>
					</tr>
					<tr>
						<td><wicket:message key="C004M01A.endDate">資料迄日</wicket:message></td>
						<td>
							<input type="text" name="dataYearE" id="dataYearE" class="digits required" maxlength="4" size="4" />
							─
							<input type="text" name="dataMmE"   id="dataMmE" class="digits required" maxlength="2" size="2" />
						</td>
					</tr>
				</table>
			</form>
		</div>
		
		<div id="gridview" name="gridview"></div>
        <script type="text/javascript" src="pagejs/fms/CLS9041M01Page.js"></script>
	</wicket:extend>
</body>
</html>
