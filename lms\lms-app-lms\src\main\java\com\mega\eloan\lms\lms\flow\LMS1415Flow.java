package com.mega.eloan.lms.lms.flow;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L141M01ADao;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L141M01A;

/**
 * <pre>
 * 聯行額度明細表流程
 * </pre>
 * 
 * @since 2011/12/6
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/6,REX,new
 *          </ul>
 */
@Component
public class LMS1415Flow extends AbstractFlowHandler {

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L141M01ADao l141m01aDao;
	@Resource
	LMSService lmsService;

	@Transition(node = "決策", value = "to核定")
	public void check(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L141M01A l141m01a = l141m01aDao.findByOid(instanceId);
		if (l141m01a != null) {
			L120M01A l120m01a = l120m01aDao.findByMainId(l141m01a
					.getSrcMainId());
			if (l120m01a != null) {
				// set 改為 list
				lmsService.upLoadMIS447OnlyforL141M01A(l120m01a, l141m01a.getL141M01D());
			}
		}

	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L141M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}
}