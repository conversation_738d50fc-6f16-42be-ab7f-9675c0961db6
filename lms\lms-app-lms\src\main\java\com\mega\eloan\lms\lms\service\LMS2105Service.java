package com.mega.eloan.lms.lms.service;

/* 
 * LMS2105Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
import java.util.List;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L210M01A;
import com.mega.eloan.lms.model.L210S01A;
import com.mega.eloan.lms.model.L210S01B;

import tw.com.iisi.cap.model.GenericBean;

/**
 * <pre>
 * 修改資料特殊流程
 * </pre>
 * 
 * @since 2012/01/10
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/01/10,REX,new
 *          </ul>
 */
public interface LMS2105Service extends AbstractService {

	/**
	 * 儲存新建檔案
	 * 
	 * @param l210s01as
	 *            儲存自行聯貸攤貸比率檔
	 * @param l210s01bs
	 *            儲存同業聯貸攤貸比率檔
	 * @param l210m01a
	 *            修改資料特殊流程主檔
	 */
	public void saveNewL210m01a(List<L210S01A> l210s01as,
			List<L210S01B> l210s01bs, L210M01A l210m01a, L120M01A l120m01a);

	/**
	 * 儲存自行聯貸攤貸比率檔
	 * 
	 * @param l210s01as
	 *            自行聯貸攤貸比率List
	 */
	public void saveL210s01as(List<L210S01A> l210s01as);

	/**
	 * 儲存同業聯貸攤貸比率檔
	 * 
	 * @param l210s01bs
	 *            同業聯貸攤貸比率List
	 */
	public void saveL210s01bs(List<L210S01B> l210s01bs);

	/**
	 * 取出同業聯貸攤貸比率檔 最大的seq
	 * 
	 * @param mainId
	 *            文件編號
	 * @return 最大seq+1
	 */
	public int findL210s01bMaxSeq(String mainId);

	/**
	 * 以 mainId 抓出主檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @return L210M01A主檔
	 */
	public L210M01A findL210M01AByMainId(String mainId);

	/**
	 * 刪除by oids
	 * 
	 * @param clazz
	 *            model class
	 * @param oids
	 *            文件編號陣列
	 * @return boolean
	 */
	@SuppressWarnings("rawtypes")
	public boolean deleteListByOid(Class clazz, String[] oids);

	/**
	 * 查詢 自行聯貸攤貸比率檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @param chgFlag
	 *            1.變更前, 2.變更後
	 * @return List<L210S01A>
	 */
	public List<L210S01A> findL210s01saByMainIdAndChgFlag(String mainId,
			String chgFlag);

	/**
	 * 查詢 同業聯貸攤貸比率檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @param chgFlag
	 *            1.變更前, 2.變更後
	 * @return List<L210S01B>
	 */
	public List<L210S01B> findL210s01bsByMainIdAndChgFlag(String mainId,
			String chgFlag);

	/**
	 * 
	 * 查詢 自行聯貸攤貸比率檔
	 * @param mainId
	 *            文件編號
	 * @param chgFlag
	 *            1.變更前, 2.變更後
	 * @param shareBrId
	 *            攤貸分行
	 * @return
	 */
	public L210S01A findL210s01saByMainIdAndChgFlagAndBrid(String mainId,
			String chgFlag, String shareBrId);

	/**
	 * flow
	 * 
	 * @param mainOid
	 *            文件編號
	 * @param model
	 *            主檔
	 * @param setResult
	 *            是否有動作
	 * @param action
	 *            動作
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, String action) throws Throwable;
}