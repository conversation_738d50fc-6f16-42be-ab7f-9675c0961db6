/*
 * Thickbox 3.1 - One Box To Rule Them All.
 * By <PERSON> (http://www.codylindley.com)
 * Copyright (c) 2007 cody lindley
 * Licensed under the MIT License: http://www.opensource.org/licenses/mit-license.php
 */

/* modified : richard
 * 1.修改關閉為顯示圖片
 * 2.修改css設定(修正firefox標題列顯示問題)
 * 3.修改modal模式顯示標題列
 * 4.整合jQuery並增加彈窗function與button列
 * 5.修正與loading.js一起使用時，顯示loading的問題
 * 6.修改為可見層次 by fantasy 2011/04/06
 * 7.修飾為圓角 by fantasy 2011/04/07
 * 8.修正css亂套問題 by fantasy 2011/04/07
 * 9.加入buttonbar的位置設定(水平align[*left,center,right],重直valign[*top,bottom]) *為預設 by fantasy 2011/04/07
 *10.修正按鈕列bottom css, 以文字跑馬燈模擬loading,避免圖片抓不到
 *11.按鈕套上CSS.懶得參數化..可空再改~ by 2011/04/07
 *12.修正iframe無法限定width,height問題 by 2011/04/26
 *13.修改jquery ui於iframe顯示問題 by 2011/04/26
 *15.新增button i18n參數 by fantasy 2011/04/28
 *14.增加return thickbox dom & 增加 open event
 *15.修正modal = false ESC 不動作問題
 *16.加上打開thickbox後focus第一個button
 *17.新增全域readOnly屬性,預設button,自訂button
 * $().thickbox({	// 使用選取的內容進行彈窗
 *   title : '',
 *   width : 600,
 *   height : 400,
 *   modal : false,
 *   align : 'right',
 *   valign: 'bottom',
 *   buttons : {
 *     name : function() or { image:"",click:function() }
 *     ...
 *   }
 * })
 *
 * $.thickbox({		// 使用Ajax取得內容進行彈窗
 *   href : '',
 *   iframe : false,
 *   title : '',
 *   width : 600,
 *   height : 400,
 *   modal : false,
 *   buttons : {
 *     name : function() or { image:"",click:function() }
 *     ...
 *   }
 * })
 *
 * $.thickbox.close()	// 關閉彈窗
 */

var tb_pathToImage = webroot + "/img/ajax-loader.gif"// ;
// var tb_pathToImage = "../../img/ajax-loader.gif";

var tb_top_label = "<b class='TB_b1f'></b><b class='TB_b2f TB_head'></b><b class='TB_b3f TB_head'></b><b class='TB_b4f TB_head'></b>";
var tb_floor_label = "<b class='TB_b4f'></b><b class='TB_b3f'></b><b class='TB_b2f'></b><b class='TB_b1f'></b>";

var tb_loading = "<center><span>" + "Now Loading..." + "<br><MARQUEE style='background-color:#FFFFFF; border:2px GROOVE #c0c0c0;' SCROLLAMOUNT=10 width=150 DIRECTION='RIGHT' >"
    + "<font size=1 color=#00FF00 >■</font>" + "<font size=1 color=#00DD00 >■</font>" + "<font size=1 color=#00BB00 >■</font>" + "<font size=1 color=#009900 >■</font>" + "</MARQUEE>"
    + "</span></center>";

// default options add by fantasy 2012/04/17
var thickboxOptions = {
  readOnly : false,
  defaultButton : [ 'close', i18n && i18n.def && i18n.def.cancel || 'cancel', i18n && i18n.def && i18n.def.sure || 'Yes', 'cancel', 'sure', i18n && i18n.def && i18n.def.close || 'close', 'print',
      i18n && i18n.def && i18n.def.print || 'print', i18n && i18n.def && i18n.def.uploadbutton || "上傳" ],
  customButton : []
};
var TB_COUNT;
/* !!!!!!!!!!!!!!!!! edit below this line at your own risk !!!!!!!!!!!!!!!!!!!!!!! */
// on page load call tb_init
$(function() {
  TB_COUNT = window["TB_COUNT"] || 0;
  tb_init('a.thickbox, area.thickbox, input.thickbox');// pass where to apply thickbox
  var imgLoader = new Image();// preload image
  imgLoader.src = tb_pathToImage;
});

// add tikbox to href & area elements that have a class of .thickbox
function tb_init(domChunk) {
  $(domChunk).click(function() {
    var t = this.title || this.name || null;
    var a = this.href || this.alt;
    var g = this.rel || false;
    tb_show(t, a, g);
    this.blur();
    return false;
  });
}

function tb_show(caption, url, imageGroup, buttons, data, _i18n, open, readOnly) {// function called when the user clicks on a thickbox link
  if (TB_COUNT > 0) {
    tb_toggle(TB_COUNT, false);
  }
  ++TB_COUNT;
  try {
    readOnly = (readOnly == null ? thickboxOptions.readOnly : readOnly);

    if (typeof document.body.style.maxHeight === "undefined") {// if IE 6
      $("body", "html").css({
        height : "100%",
        width : "100%"
      });
      $("html").css("overflow", "hidden");
      if (document.getElementById("TB_HideSelect" + TB_COUNT) === null) {// iframe to hide select elements in ie6
        $("body").append(
            "<iframe id='TB_HideSelect" + TB_COUNT + "' class='TB_HideSelect' ></iframe><div id='TB_overlay" + TB_COUNT + "' class='TB_overlay'></div><div id='TB_window" + TB_COUNT
                + "' class='TB_window'></div>");
        // $("#TB_overlay"+TB_COUNT).click(tb_remove);
      }
    } else {// all others
      if (document.getElementById("TB_overlay" + TB_COUNT) === null) {
        $("body").append("<div id='TB_overlay" + TB_COUNT + "' class='TB_overlay'></div><div id='TB_window" + TB_COUNT + "' class='TB_window'></div>");
        // $("#TB_overlay"+TB_COUNT).click(tb_remove);
      }
    }

    if (tb_detectMacXFF()) {
      $("#TB_overlay" + TB_COUNT).addClass("TB_overlayMacFFBGHack");// use png overlay so hide flash
    } else {
      $("#TB_overlay" + TB_COUNT).addClass("TB_overlayBG");// use background and opacity
    }

    if (caption === null) {
      caption = "";
    }
    // $("body").append("<div id='TB_load"+TB_COUNT+"' class='TB_load'><img src='"+imgLoader.src+"' /></div>");//add loader to the page
    $("body").append("<div id='TB_load" + TB_COUNT + "' class='TB_load'>" + tb_loading + "</div>");// add loader to the page
    $('#TB_load' + TB_COUNT).show();// show loader

    var baseURL;
    if (url.indexOf("?") !== -1) { // ff there is a query string involved
      baseURL = url.substr(0, url.indexOf("?"));
    } else {
      baseURL = url;
    }

    var urlString = /\.jpg$|\.jpeg$|\.png$|\.gif$|\.bmp$/;
    var urlType = baseURL.toLowerCase().match(urlString);
    // var closeDiv = "<div id='TB_closeAjaxWindow"+TB_COUNT+"' class='TB_closeAjaxWindow'><a href='#' id='TB_closeWindowButton"+TB_COUNT+"' class='TB_closeWindowButton' title='關閉視窗'><font
    var closeDiv = "<div id='TB_closeAjaxWindow" + TB_COUNT + "' class='" + (/msie|trident/i.test(navigator.userAgent) ? "TB_closeAjaxWindowFixIE" : "TB_closeAjaxWindow")
    // color='#FFFFFF' size=6>×</font></a></div>";
    + "'><a href='#' id='TB_closeWindowButton" + TB_COUNT + "' class='TB_closeWindowButton' title='關閉視窗'><font color='#FFFFFF' size=6>×</font></a></div>";

    if (urlType == '.jpg' || urlType == '.jpeg' || urlType == '.png' || urlType == '.gif' || urlType == '.bmp') {// code to show images

      TB_PrevCaption = "";
      TB_PrevURL = "";
      TB_PrevHTML = "";
      TB_NextCaption = "";
      TB_NextURL = "";
      TB_NextHTML = "";
      TB_imageCount = "";
      TB_FoundURL = false;
      if (imageGroup) {
        TB_TempArray = $("a[@rel=" + imageGroup + "]").get();
        for (TB_Counter = 0; ((TB_Counter < TB_TempArray.length) && (TB_NextHTML === "")); TB_Counter++) {
          var urlTypeTemp = TB_TempArray[TB_Counter].href.toLowerCase().match(urlString);
          if (!(TB_TempArray[TB_Counter].href == url)) {
            if (TB_FoundURL) {
              TB_NextCaption = TB_TempArray[TB_Counter].title;
              TB_NextURL = TB_TempArray[TB_Counter].href;
              TB_NextHTML = "<span id='TB_next" + TB_COUNT + "' class='TB_next'>&nbsp;&nbsp;<a href='#'>Next &gt;</a></span>";
            } else {
              TB_PrevCaption = TB_TempArray[TB_Counter].title;
              TB_PrevURL = TB_TempArray[TB_Counter].href;
              TB_PrevHTML = "<span id='TB_prev" + TB_COUNT + "' class='TB_prev'>&nbsp;&nbsp;<a href='#'>&lt; Prev</a></span>";
            }
          } else {
            TB_FoundURL = true;
            TB_imageCount = "Image " + (TB_Counter + 1) + " of " + (TB_TempArray.length);
          }
        }
      }

      imgPreloader = new Image();
      imgPreloader.onload = function() {
        imgPreloader.onload = null;

        // Resizing large images - orginal by Christian Montoya edited by me.
        var pagesize = tb_getPageSize();
        var x = pagesize[0] - 150;
        var y = pagesize[1] - 150;
        var imageWidth = imgPreloader.width;
        var imageHeight = imgPreloader.height;
        if (imageWidth > x) {
          imageHeight = imageHeight * (x / imageWidth);
          imageWidth = x;
          if (imageHeight > y) {
            imageWidth = imageWidth * (y / imageHeight);
            imageHeight = y;
          }
        } else if (imageHeight > y) {
          imageWidth = imageWidth * (y / imageHeight);
          imageHeight = y;
          if (imageWidth > x) {
            imageHeight = imageHeight * (x / imageWidth);
            imageWidth = x;
          }
        }
        // End Resizing

        TB_WIDTH = imageWidth + 30;
        TB_HEIGHT = imageHeight + 60;

        $("#TB_window" + TB_COUNT).append(
            "<a href='' id='TB_ImageOff" + TB_COUNT + "' class='TB_ImageOff' title='Close'><img id='TB_Image" + TB_COUNT + "' class='TB_Image' src='" + url + "' width='" + imageWidth + "' height='"
                + imageHeight + "' alt='" + caption + "'/></a>" + "<div id='TB_caption" + TB_COUNT + "' class='TB_caption'>" + caption + "<div id='TB_secondLine" + TB_COUNT
                + "' class='TB_secondLine'>" + TB_imageCount + TB_PrevHTML + TB_NextHTML + "</div></div>" + closeDiv);
        $("#TB_closeWindowButton" + TB_COUNT).click(tb_remove);

        if (!(TB_PrevHTML === "")) {
          function goPrev() {
            if ($(document).off("click", goPrev)) {
              $(document).off("click", goPrev);
            }
            $("#TB_window" + TB_COUNT).remove();
            $("body").append("<div id='TB_window" + TB_COUNT + "' class='TB_window'></div>");
            tb_show(TB_PrevCaption, TB_PrevURL, imageGroup);
            return false;
          }
          $("#TB_prev" + TB_COUNT).click(goPrev);
        }

        if (!(TB_NextHTML === "")) {
          function goNext() {
            $("#TB_window" + TB_COUNT).remove();
            $("body").append("<div id='TB_window" + TB_COUNT + "' class='TB_window'></div>");
            tb_show(TB_NextCaption, TB_NextURL, imageGroup);
            return false;
          }
          $("#TB_next" + TB_COUNT).click(goNext);

        }

        document.onkeydown = function(e) {
          if (e == null) { // ie
            keycode = event.keyCode;
          } else { // mozilla
            keycode = e.which;
          }
          if (keycode == 27) { // close
            // tb_remove();
          } else if (keycode == 190) { // display previous image
            if (!(TB_NextHTML == "")) {
              document.onkeydown = "";
              goNext();
            }
          } else if (keycode == 188) { // display next image
            if (!(TB_PrevHTML == "")) {
              document.onkeydown = "";
              goPrev();
            }
          }
        };

        tb_position();
        tb_removeLoad();
        $("#TB_ImageOff" + TB_COUNT).click(tb_remove);
        $("#TB_window" + TB_COUNT).css({
          display : "block"
        }); // for safari using css instead of show
      };

      imgPreloader.src = url;
    } else {// code to show html
      var queryString = url.replace(/^[^\?]+\??/, '');
      var params = tb_parseQuery(queryString);
      var tb_window = $("#TB_window" + TB_COUNT);
      var buttonBar = "";

      if (buttons) {
        var arr = [ "<div id='TB_buttonBar" + TB_COUNT + "' class='TB_buttonBar" + (params['valign'] == 'bottom' ? '_bottom' : '') + "' align='" + params['align'] + "'>" ];
        for ( var name in buttons) {
          if (!readOnly || $.inArray(name, thickboxOptions.defaultButton) != -1 || $.inArray(name, thickboxOptions.customButton) != -1) {
            var btn = buttons[name];
            var btnName = ((typeof _i18n == "undefined") ? (i18n && i18n.def ? i18n.def[name] || name : name) : _i18n[name] || name);
            if (typeof btn === "function") {
              arr.push("<button type='button' id='" + name + "' name='" + name + "' class='TB_buttons forview' ><span class='ui-button-text'><span class='text-only'>" + btnName + "</span></span></button>");
            } else {
              arr.push("<button type='button' id='" + name + "' name='" + name + "' class='TB_buttons forview' ><span class='ui-button-text'><span class='text-only'>");
              if (btn.image)
                arr.push("<span><img src='" + btn.image + "'/></span>");
              arr.push(name + "</span></span></button>");
            }
          }
        }
        arr.push("</div>");
        buttonBar = arr.join("");
      }

      TB_WIDTH = (params['width'] * 1) || 600; // defaults to 600 if no paramaters were added to URL
      TB_HEIGHT = (params['height'] * 1) || 400; // defaults to 400 if no paramaters were added to URL

      ajaxContentW = TB_WIDTH - 30;
      ajaxContentH = TB_HEIGHT - 45;

      if (url.indexOf('TB_iframe') != -1) {// either iframe or ajax window
        tb_window.css({
          display : "block"
        });

        urlNoQuery = url.split('TB_');
        $("#TB_iframeContent" + TB_COUNT).remove();
        if (params['modal'] == "true") {
          closeDiv = "";
          $("#TB_overlay" + TB_COUNT).off();
        }

        if (params['valign'] == 'bottom') {
          tb_window.append(tb_top_label + "<div id='TB_title" + TB_COUNT + "' class='TB_title'><div id='TB_ajaxWindowTitle" + TB_COUNT + "' class='TB_ajaxWindowTitle'>" + caption + "</div>"
              + closeDiv + "</div>" + "<iframe frameborder='0' hspace='0' src='" + urlNoQuery[0] + "' id='TB_iframeContent" + TB_COUNT + "' class='TB_iframeContent' name='TB_iframeContent"
              + Math.round(Math.random() * 1000) + "' onload='tb_showIframe()' style='width:" + (ajaxContentW + 28) + "px;height:" + (ajaxContentH + 17) + "px;' > </iframe>" + buttonBar
              + tb_floor_label);
        } else {
          tb_window.append(tb_top_label + "<div id='TB_title" + TB_COUNT + "' class='TB_title'><div id='TB_ajaxWindowTitle" + TB_COUNT + "' class='TB_ajaxWindowTitle'>" + caption + "</div>"
              + closeDiv + "</div>" + buttonBar + "<iframe frameborder='0' hspace='0' src='" + urlNoQuery[0] + "' id='TB_iframeContent" + TB_COUNT
              + "' class='TB_iframeContent' name='TB_iframeContent" + Math.round(Math.random() * 1000) + "' onload='tb_showIframe()' style='width:" + (ajaxContentW + 28) + "px;height:"
              + (ajaxContentH + 17) + "px;' > </iframe>" + tb_floor_label);
        }
      } else {// not an iframe, ajax
        if (tb_window.css("display") != "block") {
          if (params['modal'] == "true") {
            closeDiv = "";
            $("#TB_overlay" + TB_COUNT).off();
          }

          if (params['valign'] == 'bottom') {
            tb_window.append(tb_top_label + "<div id='TB_title" + TB_COUNT + "' class='TB_title'><div id='TB_ajaxWindowTitle" + TB_COUNT + "' class='TB_ajaxWindowTitle'>" + caption + "</div>"
                + closeDiv + "</div>" + "<div id='TB_ajaxContent" + TB_COUNT + "' class='TB_ajaxContent' style='height:" + ajaxContentH + "px'></div>" + buttonBar + tb_floor_label);
          } else {
            tb_window.append(tb_top_label + "<div id='TB_title" + TB_COUNT + "' class='TB_title'><div id='TB_ajaxWindowTitle" + TB_COUNT + "' class='TB_ajaxWindowTitle'>" + caption + "</div>"
                + closeDiv + "</div>" + buttonBar + "<div id='TB_ajaxContent" + TB_COUNT + "' class='TB_ajaxContent' style='height:" + ajaxContentH + "px'></div>" + tb_floor_label);
          }
        } else {// this means the window is already up, we are just loading new content via ajax
          // $("#TB_ajaxContent"+TB_COUNT)[0].style.width = ajaxContentW +"px";
          tb_window.find("#TB_ajaxContent" + TB_COUNT)[0].style.height = ajaxContentH + "px";
          tb_window.find("#TB_ajaxContent" + TB_COUNT)[0].scrollTop = 0;
          tb_window.find("#TB_ajaxWindowTitle" + TB_COUNT).html(caption);
        }
      }
      // 按鈕列套CSS add by fantasy 2011/04/11
      try {
        tb_window.find("button").button().addClass("fg-button").wrap("<span class=\"fg-buttonset\" />").wrap("<span class=\"fg-child\" />");
        (data.mode == 'msg') && tb_window.find("button").button().addClass("forview");
      } catch (e) {
      }

      if (buttons) {
        tb_window.find("#TB_buttonBar" + TB_COUNT + " button").each(function() {
          var func;
          var btn = buttons[this.name];
          if (typeof btn === "function")
            func = btn;
          else if (btn.click)
            func = btn.click;

          if (func)
            $(this).click(function() {
              func.apply(tb_window, arguments);
            });
        });
      }

      $("#TB_overlay" + TB_COUNT + ",#TB_window" + TB_COUNT + ",#TB_load" + TB_COUNT + ",#TB_HideSelect" + TB_COUNT).each(function(i, elem) {
        // $(this).css("zIndex", parseInt($(this).css("zIndex")) + 5 * TB_COUNT);
        $(this).css("zIndex", TB_COUNT * 100); // fix firefox
      });

      tb_window.find("#TB_closeWindowButton" + TB_COUNT).click(tb_remove);
      if (url.indexOf('TB_inline') != -1) {
        // richard:
        // if thickbox is inline mode and not in another thickbox
        // move TB_window to the position of the inline block
        var $inline = $('#' + params['inlineId']);
        if (!$inline.closest("div[id^='TB_window']")) {
          tb_window.insertAfter($inline);
        }
        var css = DOMPurify.sanitize($inline.attr("class"));

        // IE 6 and IE 7 can't set input value back to DOMElement when DOMElement is changed
        if (parseInt(navigator.userAgent.toLowerCase().split('msie')[1]) < 8) {
          function ie6MoveTo(src, dest) {
            // cache values
            var $src = $(src);
            var disabledMap = {};
            // var inputs = $src.find(":input");
            // var checks = inputs.filter(":radio,:checkbox").filter(":checked");
            var checks = $src.find("input:radio").filter(":checked");
            checks.filter(":disabled").each(function() {
              var $item = $(this);
              disabledMap[$item.attr("name") + $item.attr("value") + ''] = true;
              $item.attr("disabled", false);
            });
            var checkMap = checks.serializeArray();

            var $dest = $(dest);
            $dest.append($src.children());
            $.each(checkMap, function(i, o) {
              var $item = $dest.find(":input[name='" + o.name + "'][value='" + o.value + "']");
              $item.attr("checked", true);
              $item.attr("disabled", disabledMap[o.name + o.value + '']);
            });
          }
          ;

          $("#TB_ajaxContent" + TB_COUNT).addClass(css);
          ie6MoveTo($inline, "#TB_ajaxContent" + TB_COUNT);
          $("#TB_window" + TB_COUNT).on('unload', function() {
            ie6MoveTo("#TB_ajaxContent" + TB_COUNT, '#' + params['inlineId']);
          });
        } else {
          $("#TB_ajaxContent" + TB_COUNT).addClass(css).append($inline.children());
          $("#TB_window" + TB_COUNT).on('unload', function() {
            $('#' + params['inlineId']).append($("#TB_ajaxContent" + DOMPurify.sanitize(TB_COUNT)).children());
          });
        }
        tb_position();
        tb_removeLoad();
        $("#TB_window" + TB_COUNT).css({
          display : "block"
        });
        $("#TB_ajaxContent" + TB_COUNT).height($("#TB_ajaxContent" + TB_COUNT).height() - $("#TB_buttonBar" + TB_COUNT).height());
        open && open.apply(tb_window);
        try {
          $.thickbox.invoke(TB_COUNT, "ready");
        } catch (e) {

        }
      } else if (url.indexOf('TB_iframe') != -1) {
        tb_position();
        if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {// safari needs help because it will not fire iframe onload
          tb_removeLoad();
          $("#TB_window" + TB_COUNT).css({
            display : "block"
          });
        }
        open && open.apply(tb_window);
        try {
          $.thickbox.invoke(TB_COUNT, "ready");
        } catch (e) {

        }
      } else {
        $("#TB_ajaxContent" + TB_COUNT).load(url += "&random=" + (new Date().getTime()), data, function() {// to do a post change this load method
          tb_position();
          tb_removeLoad();
          tb_init("#TB_ajaxContent" + TB_COUNT + " a.thickbox");
          $("#TB_window" + TB_COUNT).css({
            display : "block"
          });
          $("#TB_ajaxContent" + TB_COUNT).height($("#TB_ajaxContent" + TB_COUNT).height() - $("#TB_buttonBar" + TB_COUNT).height());
          open && open.apply(tb_window);
          $.thickbox.invoke(TB_COUNT, "ready");
        });
      }
      $("#TB_window" + TB_COUNT).find("button:last").focus();
      tb_window.find('form:first').focusFirstChilden(); /* focus在第一個可輸入欄位 */
    }
    if (params['modal'] != true && params['modal'] != "true") {
      document.onkeyup = function(e) {
        if (e == null) { // ie
          keycode = event.keyCode;
        } else { // mozilla
          keycode = e.which;
        }
        if (keycode == 27) { // close
          // tb_remove();
        }
      };
    }
    // set thickbox content readOnly by fantasy 2012/05/05 thick content readonly
    // try{
    // //if (readOnly) $("#TB_window"+TB_COUNT).readOnlyChilds(true);
    //			
    // if (readOnly) $("#TB_window"+TB_COUNT).readOnlyChilds(true).find("button").not(".forview").hide();
    // }catch(e){
    //			
    // }
    // alert("readOnly="+readOnly);
    if (readOnly) {
		_openerLockDoc && tb_window.lockDoc(1) || _docReadonly && tb_window.lockDoc(0);
    }

    // add by fantasy 2012/7/2
    tb_window.find('.preview').each(function() {
      if (EditorAction)
        EditorAction.setPrevew($(this));
    });

    // add draggable by fantasy 2013/07/16
    tb_window.draggable({
      containment : '.TB_overlay',
      handle : '.TB_title'
    });

    return $("#TB_window" + TB_COUNT);
  } catch (e) {
    // nothing here
    // alert("thickbox error!\n"+e);
  }
}

// helper functions below
function tb_showIframe() {
  tb_removeLoad();
  $("#TB_window" + TB_COUNT).css({
    display : "block"
  });
}

function tb_removeLoad() {
  $("#TB_load" + TB_COUNT).remove();
  if ($.loading)
    $.loading.hide();
}

function tb_remove() {
  if (TB_COUNT > 0) {
    $.thickbox.onClose();
    $("#TB_imageOff" + TB_COUNT).off("click");
    $("#TB_closeWindowButton" + TB_COUNT).off("click");
    // $("#TB_window"+TB_COUNT).fadeOut("fast",function(){$('#TB_window,#TB_overlay,#TB_HideSelect').trigger("unload").unbind().remove();});
    $("#TB_window" + TB_COUNT).hide();
    $("#TB_window" + TB_COUNT + ",#TB_overlay" + TB_COUNT + ",#TB_HideSelect" + TB_COUNT).trigger("unload").off().remove();
    tb_removeLoad();
    if (typeof document.body.style.maxHeight == "undefined") {// if IE 6
      $("body", "html").css({
        height : "auto",
        width : "auto"
      });
      $("html").css("overflow", "");
    }
    document.onkeydown = "";
    document.onkeyup = "";
    --TB_COUNT;
  }
  if (TB_COUNT > 0) {
    tb_toggle(TB_COUNT, true);
  }
  return false;
}

function tb_position() {
  $("#TB_window" + TB_COUNT).css({
    marginLeft : '-' + parseInt((TB_WIDTH / 2), 10) + 'px',
    width : TB_WIDTH + 'px'
  });
  if (!(parseInt(navigator.userAgent.toLowerCase().split('msie')[1]) < 7)) { // take away IE6
    $("#TB_window" + TB_COUNT).css({
      marginTop : '-' + parseInt((TB_HEIGHT / 2), 10) + 'px'
    });
  }
}

function tb_parseQuery(query) {
  var Params = {};
  if (!query) {
    return Params;
  }// return empty object
  var Pairs = query.split(/[;&]/);
  for (var i = 0; i < Pairs.length; i++) {
    var KeyVal = Pairs[i].split('=');
    if (!KeyVal || KeyVal.length != 2) {
      continue;
    }
    var key = unescape(KeyVal[0]);
    var val = unescape(KeyVal[1]);
    val = val.replace(/\+/g, ' ');
    Params[key] = val;
  }
  return Params;
}

function tb_getPageSize() {
  var de = document.documentElement;
  var w = window.innerWidth || self.innerWidth || (de && de.clientWidth) || document.body.clientWidth;
  var h = window.innerHeight || self.innerHeight || (de && de.clientHeight) || document.body.clientHeight;
  arrayPageSize = [ w, h ];
  return arrayPageSize;
}

function tb_detectMacXFF() {
  var userAgent = navigator.userAgent.toLowerCase();
  if (userAgent.indexOf('mac') != -1 && userAgent.indexOf('firefox') != -1) {
    return true;
  }
}

function tb_toggle(num, show) {
  // mark by fantasy 2011/04/06
  // $("#TB_overlay"+num+",#TB_window"+num)[show ? "show" : "hide"]();
}

(function($) {
  $.fn.thickbox = function(option) {
    if (!this.length)
      return;
    var option = option || {}, id = this.attr("id");
    var title = option.title || this.attr("title") || "", width = option.width || 500, height = option.height || 300, modal = ((typeof option.modal == "undefined") ? true : option.modal), // false
    align = ((typeof option.align == "undefined") ? "left" : option.align), valign = ((typeof option.valign == "undefined") ? "top" : option.valign), data = option.data || "";
    $.thickbox.onClose = option.close || $.noop;
    var dialog = tb_show.call(this, title, "#TB_inline?inlineId=" + id + "&width=" + width + "&height=" + height + "&modal=" + modal + "&align=" + align + "&valign=" + valign, false, option.buttons,
        data, option.i18n, option.open, option.readOnly);
    return dialog;
  };
  $.thickbox = function(option) {
    if (!(option && option.href))
      return;
    var title = option.title || "", width = option.width || 500, height = option.height || 300, modal = ((typeof option.modal == "undefined") ? true : option.modal), // false
    align = ((typeof option.align == "undefined") ? "left" : option.align), valign = ((typeof option.valign == "undefined") ? "top" : option.valign), iframe = option.iframe || false, data = option.data
        || "";
    var href = option.href + (option.href.indexOf('?') == -1 ? '?' : '&');
    if (iframe) {
      href += "KeepThis=true&TB_iframe=true";
      // width = $(window).width() - 20;
      // height = $(window).height() - 20;
      width = option.width || 500;
      height = option.height || 300;
    }
    $.thickbox.onClose = option.close || $.noop;
    tb_show(title, href + "&height=" + height + "&width=" + width + "&modal=" + modal + "&align=" + align + "&valign=" + valign, false, option.buttons, data, option.i18n, option.open, option.readOnly);
  };
  $.thickbox.close = tb_remove;
})(jQuery);
