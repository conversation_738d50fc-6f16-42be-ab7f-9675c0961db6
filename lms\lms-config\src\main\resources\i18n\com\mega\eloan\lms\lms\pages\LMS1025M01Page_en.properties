#panel
doc.title01=Individual Credit Scorecard
doc.title02=Australia
doc.title03=Other Areas
tab.01=Document Info.
tab.02=Case Related Parties
tab.03=Collateral Info.
tab.04=Veda Report Info.
tab.05=Override
tab.06=Credit Grade

label.caseBorrower=Relevant
label.custId=MEGA ID
label.custName=Name
label.custPos=Related Identity
label.noticeItem=Note

print.rptName=Report Name
printItem1=Basic Information
printItem2=Individual Credit Scorecard
#panel-1
tab01.lnPeriod=Loan Tenor
tab01.lnYear=year
tab01.lnMonth=month
tab01.lnYearMonth={0}year{1}month
tab01.days=days
tab01.repaymentSch=Repayment Schedule
tab01.repaymentSchFmt=Loan tenor is equal to tenor(Repayment Schedule) of Credit Facility Report
tab01.repaymentSchDays.invalid=Repayment Schedule should not longer than Loan Tenor
#panel-2
tab02.btnImport=Import
tab02.btnSetCustPosCustRlt=Change Related Identity  
tab02.desc01=\u203bPlease update rating and import Credit Facility Report again if there is any change in the Basic Information Sheet.
tab02.desc02=\u203bM.Borrower, C.Co-borrower\u3002

#panel-3
#\u70ba\u4e86\u8b93\u984d\u5ea6\u660e\u7d30\u8868\u4e5f\u5448\u73fe\u300c\u64d4\u4fdd\u54c1\u300d\uff0c\u628a property \u4e0b\u653e\u5230 panel \u5c64

#panel-4
tab04.desc01=Rating summary and suggested Standalone Rating
tab04.desc02=Except Veda Score, other information of Veda Report ought to be included in Scorecard overlays. The credit handling officer should key in correct information in Enquiries sheet according their Veda Report. Rating upgrade (1 level) and downgrade (maximum 6 levels) will depend on the final Veda Report Info. (upgrading and downgrading criteria such as the following).
tab04.desc03=Downgrade Logic
tab04.desc04=\u7121\u9808\u964d\u7b49
tab04.desc05=Downgrade 1
tab04.desc06=Downgrade 2
tab04.desc07=Downgrade 3
tab04.desc08=Note\uff1aIf there is no Veda Report, the trigger records display\u201cN.A\u201d. The preliminary rating don\u2019t  be adjusted.
tab04.desc09=No.	
tab04.desc10=\u9805\u6b21\u5167\u5bb9	
tab04.desc11=Trigger or not
tab04.desc12=Points
tab04.desc13=Accumulated points
tab04.desc14=Rating after considering Veda Report Info.
tab04.desc15=General Warning Signals Description
tab04.desc16=Special Warning Signals Description
tab04.desc17=Other Information
tab04.desc18=Upgrade Logic
tab04.desc19=Upgrade 1
tab04.desc20=Trigger other information
tab04.desc21=General Warning Signals
tab04.desc22=Special Warning Signals
tab04.subItem=Item
tab04.mortgage=Mortgage
tab04.non-mortgage=non-Mortgage
#panel-5
tab05.desc01=Before determining the final rating\uff0e\uff0e\uff0e\uff0e\uff0e\uff0e please think over if following factors are taken into consideration.
tab05.desc02=Summary of rating output
tab05.desc03=Whether standalone rating is consistent with professional judgment after considering Veda Report Info.?
tab05.desc04=Note: if Yes, then do not need to override and further explain.
tab05.desc05=Override
tab05.desc06=Upgrade
tab05.desc07=Downgrade
tab05.desc08=level
tab05.desc09=Override upgrade is maximum two levels, but no limits for downgrade. Please clearly explain the reason of override.
tab05.desc10=\u203bNote: If override, be sure to check the reason boxes below and describe in details.
tab05.desc11=Upgrade reason\u3010Net Asset\u3011
tab05.desc12=(Please describe precisely with specific figures, Such as yearly(monthly) income, deposit amount, rental income, other assets, or debit ratio, etc.)
tab05.desc13=Upgrade reason\u3010Occupation\u3011
tab05.desc14=(Please describe clearly, Such as job title, employer company size, occupational categories, seniority, directorship, job prospect, etc)
tab05.desc15=Upgrade reason\u3010Others\u3011
tab05.desc16=(Such as relationship with banks, general comments on customers, or other reasons not belonging to the first two)
tab05.desc17=The rating is selected from borrower or co-borrower with one of these grades. Thus, borrower or co-borrower with well credit cannot be the reason for upgrade.
tab05.desc17.v2=As per Article 3 of \u201cOperation Directions for Personal Banking Credit Rating\u201d of Our bank, Borrower, Joint Guarantor or Co-borrower (if available) shall be graded seperately and one of these grades will be selected as credit rating of the Personal Banking Case Report. Moreover, to avoid circumstances such as the Borrower\u2019s rating is upgraded due to well credit worthiness of the Joint Guarantor/Co-borrower therefore the Borrower\u2019s final rating becomes even better than Joint Guarantor/Co-borrower\u2019s final rating, please note that it is forbidden to upgrade a relevant\u2019s credit rating with the reason of well credit of the Borrower, Joint Guarantor, or Co-borrower other than himself/herself. 
tab05.desc18=The rating can\u2019t be upgraded for the reason of \u201cwell credit of guarantor\u201d, \u201cwith insurance project\u201d, or \u201clocation and guarantee rate of the collateral\u201d.
tab05.subItem=Item
tab05.mortgage=Mortgage
tab05.non-mortgage=non-Mortgage
#panel-6
tab06.note.sRating=After Veda Report Info.
tab06.note.fRating=After Override

#msg(extend)
l120m01a.error24=$\{colName\}Borrower can not leave blank!
l120s01a.custid=Unified Business Number
l120s01a.custname=Name
l120s01a.custrlt=Relationship With The Principal Borrower
l120s01a.custpos=Related Identity
l120s01a.other16=UBN input Category Description: <br/>Natural persons-<br/>(1)&nbsp;Taiwan ID-&nbsp;\u24d0Taiwan ID No.(Ex:A123456789)<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\u24d1Ministry of Interior UBN(Ex: GC00367938)<br/>(2) Non Taiwan ID-AD date of birth + The first two characters in the English name(Ex:20120202DU)<br/><br/>Non-Natural persons -<br/>(1)&nbsp;Taiwan ID-&nbsp;\u24d0Number issued by Ministry of Economic Affairs/Revenue Service Office (Ex:********)<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\u24d1In the Bank Coding Mega&nbsp;Id(Ex:AUZ0034022)<br/>(2)&nbsp;Non Taiwan ID-Query by English company before take a number
l120s02.other13=Relationship Category
l120s02.alert26=UBN, Name Select Alternative input or import
l120s02.alert27=Please input UBN or Name!
#msg
msg.001={0} not finish Final Rating
msg.002=Override upgrade is maximum two level.
msg.003=
msg.004=Incomplete\uff1a{0}
msg.005=if Supported Rating is DEFAULT, can not Upgrade
msg.006=[Please switch between relevants by using the dropdown list menu]
msg.007={0} after {1} should between Grade 1~10
#fmt
fmt.upgrade=Upgrade {0} Level
fmt.downgrade=Downgrade {0} Level 
#========================
#C121M01A
C121M01A.custId=Principal Borrower\u2019s Mega ID(UBN) 
C121M01A.custName=Principal Borrower
C121M01A.ratingDate=Rating Date
C121M01A.caseNo=Rating Doc. No.
C121M01A.varVer=Rating Version

#C121M01C
C121M01C.pRating=Preliminary Rating
C121M01C.sRating=Standalone Rating
C121M01C.fRating=Final Rating
C121M01C.chkItemAUG1=Court Writs or Default Judgements
C121M01C.chkItemAUG2=Credit Enquiries \u2267 5 times over last 12 months
C121M01C.chkItemAUG3=Type of credit providers include store finance provider, hire-purchase or utility/telecom company for credit, which may have unfavorable impact to credit.
C121M01C.chkItemAUS1=Adverse on File
C121M01C.chkItemAUS2=Total Value of Outstanding Defaults
C121M01C.chkItemAUO1=No warning signals were triggered, and age of credit file \u2267 7 years(upgrade 1 level when meet this standard)
C121M01C.adjustFlag.1=Net Asset
C121M01C.adjustFlag.2=Occupation
C121M01C.adjustFlag.3=Others

#C121S01A
C121S01A.cmsType=Type of Collateral
C121S01A.location=Location
C121S01A.region=\u5ea7\u843d\u5730\u5340
C121S01A.houseAge=Age of House
C121S01A.houseArea=Construction Area

#selectBossBox
c121m01e.error1=Please Select
c121m01e.message00=Submit For Supervisor's Approval
c121m01e.message01=Whether to submit for supervisor's approval
c121m01e.message02=Supervisor's name duplicated; please select again
c121m01e.selectBoss=Number Of Credit Supervisors
c121m01e.bossId=Credit Supervisor
c121m01e.no=No.
c121m01e.site= th Persons
c121m01e.managerId=Unit/Authorizer

#
c121m01e.title=Approval Result & Decision
c121m01e.tag1=Business Unit
lmss01.legend1=Branch
c121m01e.appr=Handling Officer\uff1a
c121m01e.boss=Supervisor\uff1a
c121m01e.approver=Approver\uff1a
c121m01e.manager=SVP&GM/VP&DGM/AVP\uff1a
#\u5831\u8868\u5448\u73fe\u6642\uff0c\u4e2d\u6587\u4e0d\u7528\u52a0\u7a7a\u767d\uff0c\u4f46\u82f1\u6587\u9700\u8981
report.snrDesc_year={0} Years
report.snrDesc_year_month={0} Years {1} Months