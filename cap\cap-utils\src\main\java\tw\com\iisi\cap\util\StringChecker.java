package tw.com.iisi.cap.util;

import java.io.File;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <pre>
 * 字串檢核
 * </pre>
 */
public class StringChecker {

    // 檢查 HTML tags 參考來源 : OWASP XSS Filter Evasion Cheat Sheet、Cross Site Scripting Prevention Cheat Sheet
    private static final Pattern XSS_PATTERN = Pattern.compile("<(\\s*)(script|style|meta|html|body|form|div|table|td|input|button|title|br|img|svg|frame|frameset|video|bgsound|a|link|iframe|source|embed|applet|math|isindex|object|param|base\\w+)(\\s|>)", Pattern.CASE_INSENSITIVE);

    /**
     * 檢查 userNo, userId, xxxId 等常見 business logic 上的 key
     * 
     * @param paramValue
     * @throws IllegalArgumentException
     *             if <code>paramValue</code> is invalid
     */
    public static String checkUserNoString(String paramValue) {
        return checkXssString(paramValue, 32, false);
    }

    /**
     * 檢查 ID, OID, 流水號等常見主 key 代號
     * 
     * @param paramValue
     * @throws IllegalArgumentException
     *             if <code>paramValue</code> is invalid
     */
    public static String checkCommonId(String paramValue) {
        return checkXssString(paramValue, 64, false);
    }

    /**
     * 檢核可能含有的有害字元
     * 
     * @see #checkXssString(String, int, boolean)
     * @param paramValue
     *            需檢核字串
     * @return
     */
    public static String checkXssString(String paramValue) {
        return checkXssString(paramValue, 0, false);
    }

    /**
     * 檢查是否含有可能引發常見網頁風險的不安全 HTML tags, 主要針對 Cross Site Scripting.
     * <p>
     * 
     * @param paramValue
     *            需檢核字串
     * @param maxLen
     *            最大長度
     * @param dropIllegalChar
     *            是否跳脫有害字元
     * @return
     */
    public static String checkXssString(String paramValue, int maxLen, boolean dropIllegalChar) {

        if (paramValue == null)
            return null;

        if (paramValue.length() == 0)
            return "";

        if (maxLen > 0 && paramValue.length() > maxLen)
            throw new IllegalArgumentException("parameter [" + paramValue + "] is too long");

        // 檢查是否含有不安全的 HTML tags
        if (!dropIllegalChar && XSS_PATTERN.matcher(paramValue).find())
            throw new IllegalArgumentException("parameter [" + paramValue + "] is invalid");

        return paramValue;
    }

    /**
     * 檢核非法字元
     * 
     * @param paramValue
     * @param illegalChars
     * @param maxLen
     * @param dropIllegalChar
     * @return
     */
    public static String checkIllegalChars(String paramValue, char[] illegalChars, int maxLen, boolean dropIllegalChar) {

        if (paramValue == null)
            return null;

        if (paramValue.length() == 0)
            return "";

        if (maxLen > 0 && paramValue.length() > maxLen)
            throw new IllegalArgumentException("parameter [" + paramValue + "] is too long");

        if (illegalChars == null || illegalChars.length == 0)
            return paramValue;

        CharBuffer buff = ByteBuffer.allocate(paramValue.length() < 1000 ? 4096 : paramValue.length() * 4).asCharBuffer();
        for (int i = 0; i < paramValue.length(); i++) {
            char c = paramValue.charAt(i);

            // 檢查有危害的字元
            boolean found = false;
            for (int j = 0; j < illegalChars.length; j++) {

                char x = illegalChars[j];
                if (c == x) {
                    found = true;
                    break;
                }
            }

            if (found) {
                if (dropIllegalChar)
                    break; // drop char only
                else
                    throw new IllegalArgumentException("invalid char [" + c + "]");
            } else
                buff.put(c);
        }

        buff.flip();
        char[] chars = new char[buff.limit()];
        buff.get(chars);
        return new String(chars);
    }

    /**
     * 檢查是否有傳入值
     * 
     * @param s
     * @return
     */
    public static boolean hasText(CharSequence s) {
        return s != null && s.length() > 0;
    }

    /**
     * 檢查路徑字串
     * 
     * @param path
     *            路徑
     * @return
     */
    public static String checkPathString(String path) {
        return checkPathString(path, false);
    }

    /**
     * 檢查路徑字串
     * 
     * @see #checkPathString(String, boolean, String[])
     */
    public static String checkPathString(String path, boolean allowFromRoot) throws IllegalArgumentException {
        return checkPathString(path, allowFromRoot, PATH_WHITE_LIST);
    }

    /**
     * 檢查路徑字串, 是否需要在尾端加上"/"<br>
     * 
     * 預設白名單<br>
     * 
     * @param path
     *            要檢查的路徑字串
     * @param allowFromRoot
     *            是否允許由根目錄開始
     * @param padding
     *            是否需要判斷加上"/"
     * @return 檢查/修正後的路徑
     * @throws IllegalArgumentException
     *             如果不在允許的清單內
     */
    public static String checkPathString(String path, boolean allowFromRoot, boolean padding) throws IllegalArgumentException {
        if (padding) {
            return checkPathString(paddingSeparator(path), allowFromRoot, PATH_WHITE_LIST);
        }
        return checkPathString(path, allowFromRoot, PATH_WHITE_LIST);
    }

    /**
     * 檢查路徑結尾是否包含"/"，若無則補上
     * 
     * @param path
     *            需要檢查的路徑
     * @return path
     */
    public static String paddingSeparator(String path) {
        if (!CapString.isEmpty(path) && !path.endsWith(File.separator)) {
            path += File.separator;
        }
        return path;
    }

    /**
     * 檢查路徑
     * 
     * @param path
     *            要檢查的路徑字串
     * @param allowFromRoot
     *            是否允許由根目錄開始
     * @param validPaths
     *            如果允許由根目錄開始, 只允許本清單以內
     * @return 檢查/修正後的路徑
     * @throws IllegalArgumentException
     *             如果不在允許的清單內
     */
    public static String checkPathString(String path, boolean allowFromRoot, String[] validPaths) throws IllegalArgumentException {

        if (path == null)
            return null;

        //
        String s1 = path.trim().replace("\\", "/");

        // 不允許使用上層目錄, 以免存取意料以外的資源
        String s2 = s1.replace("..", "").replaceAll("/{2,}", "/");

        // 如果路徑是由根目錄開始
        boolean startWithUnixRoot = s2.startsWith("/");
        boolean startWithWindowsRoot = s2.matches("^[a-zA-Z]+[:][/].*");
        if (startWithUnixRoot || startWithWindowsRoot) {

            if (allowFromRoot) {
                // 如果允許由根目錄開始, 也得是清單中的目錄
                boolean ok = false;
                for (int i = 0; validPaths != null && i < validPaths.length; i++) {
                    String validPath = validPaths[i];
                    if (s2.startsWith(validPath)) {
                        ok = true;
                        break;
                    }
                }

                // 不符合的, 一律擋掉, 要開放就改程式中的 PATH_WHITE_LIST
                if (!ok)
                    throw new IllegalArgumentException("path [" + path + "] does not exist in PATH_WHITE_LIST");

            } else {
                // 如果不允許由根目錄開始, 直接移掉
                // 這是為了減少現行程式的修改.
                if (startWithWindowsRoot)
                    s2 = s2.replaceAll("[a-zA-Z]+[:][/]", "").replaceAll("/{2,}", "/");
                else
                    s2 = s2.replaceAll("^/+", "");
            }
        }

        // 如果要變回 windows 路徑 (\), 可用 File or Path 相關 method, 這裡先不用
        return s2;
    }

    /*
     * 2023/10/23 add DEB FTP upload/download path:/dw/ftpdata/ftpucc1/ftpout/ IN config.properties
     */
    private static final String[] PATH_WHITE_LIST = { "/elnfs/", "/eloan/", "/tmp/", "/elnfs", "/eloan", "/tmp", "/dw" };

}
