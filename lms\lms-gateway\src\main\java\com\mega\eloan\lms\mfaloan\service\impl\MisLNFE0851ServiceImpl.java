package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisLNFE0851Service;

@Service
public class MisLNFE0851ServiceImpl extends AbstractMF<PERSON>loanJdbc implements
		MisLNFE0851Service {
	public List<Map<String,Object>> findLNFE0851ByCustId(String custid, String dupno,
			String branch) {
		return this.getJdbc().queryForList("LNFE0851.selByCustId",
				new Object[] { custid, dupno, branch });
	}
}
