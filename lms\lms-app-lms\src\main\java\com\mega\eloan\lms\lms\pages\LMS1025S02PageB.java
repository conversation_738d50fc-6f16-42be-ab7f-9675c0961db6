
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.panels.L120S01MPanel;
import com.mega.eloan.lms.lms.panels.LMS1025S02PanelB1;
import com.mega.eloan.lms.lms.panels.LMS1115S02PanelB5;

/**
 * <pre>
 * 消金信用評等模型
 * </pre>
 * 
 * @since 2015/8/18
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/8/18,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1025s02b/{page}")
public class LMS1025S02PageB extends AbstractOverSeaCLSPage {

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		renderJsI18N(AbstractOverSeaCLSPage.class);

		model.addAttribute("_PanelB1_visible", true);
		new LMS1025S02PanelB1("PanelB1", true, false).processPanelData(model, params);

		model.addAttribute("_PanelB5_visible", true);
		new LMS1115S02PanelB5("PanelB5").processPanelData(model, params);

		Panel panel = new L120S01MPanel("l120s01mPanel");
		panel.processPanelData(model, params);
	}
}
