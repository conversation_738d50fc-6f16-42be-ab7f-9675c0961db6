package com.mega.eloan.lms.cls.panels;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.gwclient.Brmp002I;
import com.mega.eloan.common.gwclient.Brmp002O;
import com.mega.eloan.common.gwclient.Brmp002O.Brmp002O_result_policyFactorObj;
import com.mega.eloan.common.gwclient.Brmp002O.Brmp002O_result_policyObj;
import com.mega.eloan.common.gwclient.Brmp002O.Brmp002O_result_quotationResultObj;
import com.mega.eloan.common.gwclient.Brmp002O.Brmp002O_result_quotationResult_advancedRedemptionObj;
import com.mega.eloan.common.gwclient.Brmp002O.Brmp002O_result_quotationResult_calProcessObj;
import com.mega.eloan.common.gwclient.Brmp002O.Brmp002O_result_quotationResult_interestRateObj;
import com.mega.eloan.common.html.DataView;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.mfaloan.service.MisMislnratService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.L120M01I;
import com.mega.eloan.lms.model.L120S19A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01Y;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02D;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 決策平台 creditCheck平台回傳電文呈現
 * </pre>
 *
 * <AUTHOR>
 * @version <ul>
 * <li>2021/07/01, EL07625, new
 * </ul>
 * @since 2021/07/01
 */
public class CLS1201S27Panel extends Panel {


	private static final long serialVersionUID = 1162611959647309267L;

	private String mainId;

	@Autowired
	CLS1141Service cls1141Service;
	@Autowired
	CLSService clsService;
	@Autowired
	MisMislnratService mislnratService;
	@Autowired
	CodeTypeService codeTypeService;

	public CLS1201S27Panel(String id) throws CapException {
		super(id);
	}

	public CLS1201S27Panel(String id, boolean updatePanelName, String mainId) {
		super(id, updatePanelName);
		this.mainId = mainId;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		ObjectMapper objectMapper = new ObjectMapper();
//		List<L120S19A> l120s19as = (List<L120S19A>) cls1141Service.findListByMainId(L120S19A.class, mainId);

		JSONObject inJs = new JSONObject();
		JSONObject outJs = new JSONObject();
		L120S19A l120s19aOutJs = null;
		
		String l140mainId = "";
//		if (CollectionUtils.isNotEmpty(l120s19as)) {
//			for (L120S19A l120s19a : l120s19as) {
//				String itemType = l120s19a.getItemType();
//				if (ClsConstants.L120S19A_ItemTypeCode.BRMP_creditCheck_input.equals(itemType)) {
//					inJs = JSONObject.fromObject(l120s19a_latestInput.getJsonData());
//				} else if (ClsConstants.L120S19A_ItemTypeCode.BRMP_creditCheck_output.equals(itemType)) {
//					outJs = JSONObject.fromObject(l120s19a_latestOutput.getJsonData());
//					l140mainId = l120s19a.getRef140MainId();
//					l120s19aOutJs = l120s19a;
//				}
//			}
//		}
		//J-111-0160 改抓最新版本決策結果
		L120S19A l120s19a_latestInput = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(mainId, ClsConstants.L120S19A_ItemTypeCode.BRMP_creditCheck_input);
		L120S19A l120s19a_latestOutput = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(mainId, ClsConstants.L120S19A_ItemTypeCode.BRMP_creditCheck_output);
		if (Util.isNotEmpty(l120s19a_latestInput)) {
			inJs = JSONObject.fromObject(l120s19a_latestInput.getJsonData());
		}
		if (Util.isNotEmpty(l120s19a_latestOutput)) {
			outJs = JSONObject.fromObject(l120s19a_latestOutput.getJsonData());
			l140mainId = l120s19a_latestOutput.getRef140MainId();
			l120s19aOutJs = l120s19a_latestOutput;
		}

		Brmp002I brmp002I = new Brmp002I();
		Brmp002O brmp002O = new Brmp002O();
		try {
			brmp002I = objectMapper.readValue(inJs.toString(), Brmp002I.class);
			brmp002O = objectMapper.readValue(outJs.toString(), Brmp002O.class);
		} catch (IOException e) {
			e.printStackTrace();
		}

		List<L140S02D> l140s02ds = null;
		List<L140S02A> l140s02as = cls1141Service.findl140s02aByl140m01aMainid(l140mainId);
		if (CollectionUtils.isNotEmpty(l140s02as)) {
			L140S02A l140s02a = l140s02as.get(0);
			// 基礎利率因為output沒有回傳，所以從 分段利率明細檔 抓當時的基礎利率
			l140s02ds = clsService.findL140S02D_orderByPhase(l140mainId, l140s02a.getSeq(), "Y");
		}


		List<Brmp002O_result_policyObj> policyResult = brmp002O.getResult().getPolicyResult();
		DataView<JSONObject> griPolicyResultView = this.getPolicyResultView("_grid_policyResult", policyResult);
		griPolicyResultView.processData(model, params);

		List<Brmp002O_result_quotationResult_interestRateObj> interestRate = brmp002O.getResult()
				.getQuotationResult()
				.getInterestRate();


		List<String> interestRateList = new ArrayList<String>();
		for (int i = 0; i < interestRate.size(); i++) {
			Brmp002O_result_quotationResult_interestRateObj obj = interestRate.get(i);
			//起始期間
			String startPeriod = Util.trim(obj.getStartPeriod());
			//結束期間
			String endPeriod = Util.trim(obj.getEndPeriod());
			//基礎利率代碼
			String baseRateCode = Util.trim(obj.getBaseRateCode());
			//固定利率註記
			String fixRateFlag = Util.trim(obj.getFixRateFlag());
			//階段利率
			String stageRate = Util.trim(obj.getStageRate());

			BigDecimal baseRate = null;
			String rateFlag = null;
			L140S02D l140s02d = null;
			int size = l140s02ds == null ? 0 : l140s02ds.size();
			if ((i + 1) <= size) {
				l140s02d = l140s02ds.get(i);
				baseRate = l140s02d.getBaseRate();
				rateFlag = l140s02d.getRateFlag();
			}

			if ("1".equals(rateFlag)) {
				String pattern = "第{0}期~第{1}期自訂利率【{2}%】，固定利率";

				//第1期~第6期自訂利率【1％】
				String txt = MessageFormat.format(pattern, startPeriod, endPeriod,
						CapMath.getBigDecimal(stageRate).multiply(BigDecimal.valueOf(100)));

				interestRateList.add(txt);

			} else if ("2".equals(rateFlag) || "3".equals(rateFlag)) {
				Map<String, Object> lnRate = mislnratService.findLrCodeByCurr("TWD", baseRateCode);
				String baseRateName = MapUtils.getString(lnRate, "LR_RATE_CNAME", "");

				String _baseRate = baseRate.toString(); //%
				BigDecimal finalRate = CapMath.getBigDecimal(stageRate).multiply(BigDecimal.valueOf(100));

				//第7期~第84期月變動消費金融指標利率加1.84％(目前為【2.68％】)，定期浮動每月調整一次。
				//現況，行員不能做歡喜信貸，所以現況不會有「機動利率」。但若未來的某一天，突然放寬，這裡在呈現時，不應該只有「定期浮動」而已
				String pattern = "第{0}期~第{1}期{2}加{3}％(目前為【{4}％】)"
						+(Util.equals(l140s02d.getRateFlag(), "2")?"，機動利率":"")
						+(Util.equals(l140s02d.getRateFlag(), "3")?"，定期浮動每月調整一次":"");

				String txt = MessageFormat.format(pattern, startPeriod, endPeriod, baseRateName,
						finalRate.subtract(CapMath.getBigDecimal(_baseRate)), finalRate);
				interestRateList.add(txt);
			}
		}

		// UPGRADE: 前端須配合改Thymeleaf的樣式
//		add(new Label("systemComputedRate", StringUtils.join(interestRateList, ";<br>") + (interestRateList.size() == 0 ? "" : "。")).setEscapeModelStrings(false));
		model.addAttribute("systemComputedRate", StringUtils.join(interestRateList, ";<br>") + (interestRateList.size() == 0 ? "" : "。"));


		List<Brmp002O_result_quotationResult_advancedRedemptionObj> advancedRedemption = brmp002O.getResult()
				.getQuotationResult()
				.getAdvancedRedemption();


		List<String> messagesList = new ArrayList<String>();
		for (int i = 0; i < advancedRedemption.size(); i++) {
			Brmp002O_result_quotationResult_advancedRedemptionObj advancedRedemptionObj = advancedRedemption.get(i);
			//起始期間
			String startPeriod = Util.trim(advancedRedemptionObj.getStartPeriod());
			//結束期間
			String endPeriod = Util.trim(advancedRedemptionObj.getEndPeriod());
			//懲罰利率
			String punishRate = Util.trim(advancedRedemptionObj.getPunishRate());

			String message = MessageFormat.format("第{0}期~第{1}期：{2}％", startPeriod, endPeriod,
					CapMath.getBigDecimal(punishRate).multiply(BigDecimal.valueOf(100)));
			messagesList.add(message);
		}
		String messageStr = "無";
		if (messagesList.size() > 0) {
			messageStr = StringUtils.join(messagesList.toArray(new String[1]), ";<br>");
		}
		// UPGRADE: 前端須配合改Thymeleaf的樣式
//		add(new Label("advancedRedemptionDesc", messageStr).setEscapeModelStrings(false));
		model.addAttribute("advancedRedemptionDesc", messageStr);

		model.addAttribute("stat", brmp002O.getStat());
		model.addAttribute("systemUuid", brmp002O.getSystemUuid());
		model.addAttribute("uuid", brmp002O.getUuid());
		model.addAttribute("packageVersion", brmp002O.getPackageVersion());
		model.addAttribute("errorCode", brmp002O.getErrorCode());
		model.addAttribute("errorMsg", brmp002O.getErrorMsg());

		//JSONObject result = outJs.getJSONObject("result");
		//add(new Label("headAccountScore", result.optString("headAccountScore")));

		Brmp002O_result_quotationResultObj quotationResult = brmp002O.getResult().getQuotationResult();
		BigDecimal duration = quotationResult.getDuration() == null ? BigDecimal.ZERO : quotationResult.getDuration();
		int durationYear = duration.intValue() / 12;
		int durationMonth = duration.intValue() % 12;
		//系統計算期間
		model.addAttribute("systemComputedPeriod", durationYear + "年" + durationMonth + "月");

		//JSONObject quotationResult = result.containsKey("quotationResult") ? result.getJSONObject(
		//		"quotationResult") : new JSONObject();
		//add(new Label("resultStatus", quotationResult.optString("resultStatus")));
		//add(new Label("duration", quotationResult.optString("duration")));
		//add(new Label("finalAmt", quotationResult.optString("finalAmt")));

		//開辦費
		model.addAttribute("organizationCost",
				"TWD " + this.expressNumber(Util.trim(quotationResult.getOrganizationCost())));
		//信用查詢費
		model.addAttribute("creditCheckFee",
				"TWD " + this.expressNumber(Util.trim(quotationResult.getCreditCheckFee())));
		//add(new Label("amtChangeFlag", quotationResult.optString("amtChangeFlag")));

		//JSONObject calProcessResult = quotationResult.containsKey("calProcessResult") ? quotationResult
		// .getJSONObject(
		//		"calProcessResult") : new JSONObject();
		Brmp002O_result_quotationResult_calProcessObj calProcessResult = quotationResult.getCalProcessResult();
		Brmp002O_result_policyFactorObj policyFactor = brmp002O.getResult().getPolicyFactor();

		// Aum金額_dw
		String aumAmt = Util.trim(calProcessResult.getAumAmt());
		if (CapMath.getBigDecimal(aumAmt).compareTo(BigDecimal.valueOf(1000000)) >= 0) {
			model.addAttribute("aumAmt", "Y");
		} else {
			model.addAttribute("aumAmt", "N");
		}
		//add(new Label("mortgageUserFlag", calProcessResult.optString("mortgageUserFlag")));

		//卡友註記
		model.addAttribute("creditCardUserFlag", this.expressValue(Util.trim(calProcessResult.getCreditCardUserFlag())));
		//add(new Label("unaccumalativePlusRate", calProcessResult.optString("unaccumalativePlusRate")));
		//add(new Label("accumalativeMinusRate", calProcessResult.optString("accumalativeMinusRate")));
		//add(new Label("loanScorePlusRate", calProcessResult.optString("loanScorePlusRate")));
		//add(new Label("revolvingDebtPlusRate", calProcessResult.optString("revolvingDebtPlusRate")));
		//add(new Label("totalRevolvingDebtAmt", calProcessResult.optString("totalRevolvingDebtAmt")));
		//add(new Label("dratePlusRate", calProcessResult.optString("dratePlusRate")));

		//線上進件註記_eloan
		model.addAttribute("applyOnlineFlag", this.expressValue(Util.trim(calProcessResult.getApplyOnlineFlag())));
		//add(new Label("internalConsumerLoanLimitAmt", calProcessResult.optString("internalConsumerLoanLimitAmt")));
		//客群額度上限
		model.addAttribute("termGroupLimitAmt", this.expressNumber(Util.trim(calProcessResult.getTermGroupLimitAmt())));
		//DBR額度上限計算
		model.addAttribute("dbrLimitAmt", this.expressNumber(Util.trim(calProcessResult.getDbrLimitAmt())));
		//個人負債比額度上限
		model.addAttribute("drateLimitAmt", this.expressNumber(Util.trim(calProcessResult.getDrateLimitAmt())));
		//add(new Label("guarantorEmployeeFlag", calProcessResult.optString("guarantorEmployeeFlag")));
		//月付金額度上限
		String termGroup = brmp002I.getTermGroup();
		model.addAttribute("monthlyPaymentLimitAmt",
				!"E".equals(termGroup) ? "N.A." :this.expressNumber(Util.trim(calProcessResult.getMonthlyPaymentLimitAmt())));

		String bankStakeholderFlag = Util.trim(policyFactor.getBankStakeholderFlag());
		//銀行法利害關係人下消貸額度上限
		String bankStakeholderLimitAmtDesc = this.expressNumber(Util.trim(calProcessResult.getBankStakeholderLimitAmt()));
		// 非利害關係人時，不參考此資料
		if ("0".equals(bankStakeholderFlag)) {
			bankStakeholderLimitAmtDesc = "N.A.";
			//設成null，讓計算可用額度上限可以排除掉
			calProcessResult.setBankStakeholderLimitAmt(null);
		}
		model.addAttribute("bankStakeholderLimitAmt", bankStakeholderLimitAmtDesc);
		//本行信用貸款上限
		model.addAttribute("megaUnsecuredLoanLimitAmt",
				this.expressNumber(Util.trim(calProcessResult.getMegaUnsecuredLoanLimitAmt())));
		//本行消費性貸款上限(營業單位授權)
		model.addAttribute("megaConsumerLoanLimitAmt",
				this.expressNumber(Util.trim(calProcessResult.getMegaConsumerLoanLimitAmt())));


		//JSONObject policyFactor = result.containsKey("policyFactor") ? result.getJSONObject(
		//		"policyFactor") : new JSONObject();

		//申貸總月份_eloan
		//add(new Label("applyDuration", Util.trim(policyFactor.getApplyDuration()) + "期"));

		//預借現金循環+信用卡循環
		model.addAttribute("revolvingAmt", this.expressNumber(CapMath.getBigDecimal(Util.trim(policyFactor.getCashRevolvingAmt()))
				.add(CapMath.getBigDecimal(Util.trim(policyFactor.getCardRevolvingAmt())))
				.toString()));
		//本行消費性貸款總金額_ods
		//add(new Label("megaConsumerLoanAmt", NumConverter.addComma(Util.trim(policyFactor.getMegaConsumerLoanAmt())
		// )));
		////本行信用貸款總金額_ods
		//add(new Label("megaUnsecuredLoanAmt", NumConverter.addComma(Util.trim(policyFactor.getMegaUnsecuredLoanAmt()
		// ))));

		BigDecimal megaTotalLoanAmt = policyFactor.getMegaTotalLoanAmt() == null ? BigDecimal.ZERO :
				policyFactor.getMegaTotalLoanAmt();
		BigDecimal otherBankTotalLoanAmt = policyFactor.getOtherBankTotalLoanAmt() == null ? BigDecimal.ZERO :
				policyFactor.getOtherBankTotalLoanAmt();
		BigDecimal applyAmt = brmp002I.getApplyAmt() == null ? BigDecimal.ZERO : brmp002I.getApplyAmt();
		BigDecimal compensationAmt = brmp002I.getCompensationAmt() == null ? BigDecimal.ZERO :
				brmp002I.getCompensationAmt();
		BigDecimal otherAdjustAmt = brmp002I.getOtherAdjustAmt() == null ? BigDecimal.ZERO :
				brmp002I.getOtherAdjustAmt();

		L140M01A l140m01a = clsService.findL140M01A_mainId(l140mainId);

		String custId = Util.trim(brmp002I.getCustId());
		String custIdDupNo = Util.trim(brmp002I.getCustIdDupNo());
		String custName = "";
		String compJobTitle = "";
		String avgMonthIncome = "";
		String cntrNo = "";
		C120S01C c120s01c = null;

		if (l140m01a != null) {
			cntrNo = Util.trim(l140m01a.getCntrNo());

			List<L140M01Y> l140m01y_list = clsService
					.findL140M01YOrderDefault(
							l140m01a.getMainId(),
							UtilConstants.L140M01Y_refType_docCode1.ELF459_SRCFLAG_1);
			String usePlan = "";
			if (l140m01y_list.size() > 0) {
				L140M01Y l140m01y = l140m01y_list.get(0);
				usePlan = l140m01y.getUsePlan();
				if (Util.equals("C122M01A", l140m01y.getRefModel())) {
					CodeType codetype = codeTypeService.findByCodeTypeAndCodeValue(
							"ploan_plan", usePlan);
					if (codetype!=null) {
						model.remove("usePlan");
						model.addAttribute("usePlan", usePlan + " " + codetype.getCodeDesc());
					}else if (Util.equals(usePlan,"N")) {
						model.remove("usePlan");
						model.addAttribute("usePlan", "無");
					}
				}
			}
		}

		C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(mainId, custId, custIdDupNo);
		if (c120m01a != null) {
			custName = Util.trim(c120m01a.getCustName());

			C120S01B c120s01b = clsService.findC120S01B(c120m01a);
			c120s01c = clsService.findC120S01C(c120m01a);

			String comName = Util.trim(c120s01b.getComName());
			String jobTitle = c120s01b.getJobTitle();
			Map<String, String> lms1205s01_jobTitle = codeTypeService.findByCodeType("lms1205s01_jobTitle");
			jobTitle = lms1205s01_jobTitle.get(jobTitle);
			compJobTitle = comName + "-" + jobTitle;
			String rateData = c120s01c.getRateData();

			avgMonthIncome = this.expressNumber(LMSUtil.pretty_numStr(ClsUtility.brmp_avgMonthIncome(rateData)));

		}

		//J-112-0205 全體總額須加上信用卡循環與分期未償還金額
		BigDecimal creditLoan = BigDecimal.ZERO;
		if (Util.isNotEmpty(c120s01c)) {
			JSONObject json = JSONObject.fromObject(c120s01c.getRateData());
			creditLoan = creditLoan.add(Util.parseBigDecimal(json.get("jcic_credit_017")));
			creditLoan = creditLoan.add(Util.parseBigDecimal(json.get("jcic_credit_not_017")));
			creditLoan = creditLoan.add(Util.parseBigDecimal(json.get("jcic_credit_unpay")));
		}

		// 本行貸款總計 megaTotalLoanAmt + applyAmt + otherAdjustAmt
		model.addAttribute("megaTotalLoanAmt", this.expressNumber(megaTotalLoanAmt.add(applyAmt).add(otherAdjustAmt).toString()));
		//全體金融機構貸款總計：megaTotalLoanAmt+applyAmt - compensationAmt+ otherAdjustAmt+otherBankTotalLoanAmt
		model.addAttribute("allFinInsTotalLoanAmt", this.expressNumber(megaTotalLoanAmt.add(applyAmt).subtract(compensationAmt).add(otherAdjustAmt).add(otherBankTotalLoanAmt).add(creditLoan).toString()));


		//---------------------------------------以下是in的電文資料
		
		//申貸額度(單位:元)
		String loanAmt = this.expressNumber(Util.trim(brmp002I.getApplyAmt()));
		L120S19A l120s19a_first = clsService.findL120S19A_byMainId_firstInput(mainId);
		ObjectMapper objectMapper_first = new ObjectMapper();
		String loanAmt_orig = "";
		String ln_orig = "";
		try {
			Brmp002I brmp002I_first = objectMapper_first.readValue(JSONObject.fromObject(l120s19a_first.getJsonData()).toString(), Brmp002I.class);
			loanAmt_orig = this.expressNumber(Util.trim(brmp002I_first.getApplyAmt()));

			Integer lnYear = brmp002I_first.getLnYear();
			Integer lnMonth = brmp002I_first.getLnMonth();
			lnYear = lnYear == null ? 0 : lnYear;
			lnMonth = lnMonth == null ? 0 : lnMonth;

			ln_orig= lnYear + "年" + lnMonth + "月";

		} catch (IOException e) {
			e.printStackTrace();
		}

		if(Util.isNotEmpty(loanAmt_orig)){
			loanAmt = loanAmt_orig;
		}
		model.addAttribute("applyQuota", "TWD " + loanAmt);
		
		//add(new Label("applyAmt_", NumConverter.addComma(Util.trim(brmp002I.getApplyAmt()))));
		//申貸年限(年)*12+申貸年限(月)
		Integer lnYear = brmp002I.getLnYear();
		Integer lnMonth = brmp002I.getLnMonth();
		lnYear = lnYear == null ? 0 : lnYear;
		lnMonth = lnMonth == null ? 0 : lnMonth;
		String ln = lnYear + "年" + lnMonth + "月";
		if(Util.isNotEmpty(ln_orig)){
			ln = ln_orig;
		}
		model.addAttribute("applyPeriod", ln);

		//客戶名稱
		model.addAttribute("applyCustName", custName);
		//客戶統編
		model.addAttribute("applyCustId", custId);
		//公司職稱
		model.addAttribute("compJobTitle", compJobTitle);
		//平均月收入
		model.addAttribute("avgMonthIncome", "TWD " + avgMonthIncome);
		//額度序號
//		add(new Label("cntrNo", cntrNo));

		//薪轉戶註記
		model.addAttribute("ptaFlag", this.expressValue(Util.trim(brmp002I.getPtaFlag())));
		//非房貸最終評等
		model.addAttribute("modelKindGrade3", Util.trim(brmp002I.getModelKindGrade3()));
		//本行房貸戶註記
		model.addAttribute("mortgageUserFlag", this.expressValue(Util.trim(brmp002I.getMortgageUserFlag())));
		//利率分段數
		model.addAttribute("stageCount", Util.trim(brmp002I.getStageCount()));
		//綁約狀態
		model.addAttribute("advanceRedemptionFlag", this.expressValue(Util.trim(brmp002I.getAdvanceRedemptionFlag())));
		//ESG分數
		model.addAttribute("esgScore", this.expressNumber(Util.trim(brmp002I.getEsgScore())));
		//個人負債比
		BigDecimal drate = brmp002I.getDrate();
		model.addAttribute("drate", drate == null ? "N.A." : this.expressNumber(
				drate.multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString()) + "%");
		//聯名卡友類別  01:海悅聯名卡
		model.addAttribute("coBrandedCardUserType",
				Util.trim(brmp002I.getCoBrandedCardUserType()).equals("01") ? "Y" : "N");
		//代償金額
		model.addAttribute("compensationAmt", this.expressNumber(Util.trim(brmp002I.getCompensationAmt())));
		// 其他調整
		model.addAttribute("otherAdjustAmt", this.expressNumber(Util.trim(brmp002I.getOtherAdjustAmt())));

		Integer seniorityYear = brmp002I.getSeniorityYear() == null ? 0 : brmp002I.getSeniorityYear();
		Integer seniorityMonth = brmp002I.getSeniorityMonth() == null ? 0 : brmp002I.getSeniorityMonth();
		model.addAttribute("seniority", seniorityYear + "年" + seniorityMonth + "月");
		//---------------------------------------------

		boolean bool_strongIndicator = ClsUtility.headAccount_strongIndex_match(brmp002O);
		boolean bool_haWeakIndicator = ClsUtility.headAccount_weakIndex_match(brmp002O, 33);
		boolean bool_haUnauthorizeIndicator = ClsUtility.headAccount_HaUnauthorizeIndicator_cross_AuthLvl_1_Threshold(brmp002O, 40);

		// UPGRADE: 前端須配合改Thymeleaf的樣式	start
		//文字顏色
//		SimpleAttributeModifier color_red = new SimpleAttributeModifier("style", "color:red");
//		SimpleAttributeModifier color_blue = new SimpleAttributeModifier("style", "color:blue");

//		add(new Label("headAccountStrongFactor", bool_strongIndicator ? "已達" : "未達").add(
//				bool_strongIndicator ? color_red : color_blue));
//		add(new Label("headAccountWeakFactor", bool_haWeakIndicator ? "已達" : "未達").add(
//				bool_haWeakIndicator ? color_red : color_blue));
//		add(new Label("headAuthorizeFactor", bool_haUnauthorizeIndicator ? "已達" : "未達").add(
//				bool_haUnauthorizeIndicator ? color_red : color_blue));

//		TextArea<String> indicatorReaon = new TextArea<String>("indicatorReaon");
//		indicatorReaon.add(new SimpleAttributeModifier("name", "indicatorReaon"));
//		indicatorReaon.setMarkupId("indicatorReaon").setOutputMarkupId(true);
//		if (!bool_strongIndicator && !bool_haWeakIndicator && !bool_haUnauthorizeIndicator) {
//			indicatorReaon.add(new SimpleAttributeModifier("readOnly", "readOnly"));
//		}
//		add(indicatorReaon);
		// UPGRADE: 前端須配合改Thymeleaf的樣式	end

		List<String> phList = new ArrayList<String>();
		if (bool_strongIndicator || bool_haWeakIndicator || bool_haUnauthorizeIndicator) {
			for (Brmp002O_result_policyObj obj : policyResult) {
				String policyCode = Util.trim(obj.getPolicyCode());
				String status = obj.getStatus();
				if (policyCode.startsWith("PH") && "1".equals(status)) {
					phList.add(Util.trim(obj.getPolicyDescription()));
				}
			}
		}
		// UPGRADE: 前端須配合改Thymeleaf的樣式
//		add(new Label("ph_policyDesc", phList.size() > 0 ? "警示態樣：" + StringUtils.join(phList, "、") : "").add(color_red));

		//可貸金額上限
		model.addAttribute("systemComputedQuota",  this.clsService.getCeilingAmountOfBrmpResult(calProcessResult, brmp002O));

		L120M01I l120m01i = clsService.findL120M01I_mainId(mainId);
		String creditLoanPurpose = "";
		if (l120m01i != null) {
			creditLoanPurpose = Util.trim(l120m01i.getCreditLoanPurpose());
		}
		if (!"".equals(creditLoanPurpose)) {
			Map<String, String> creditLoanPurposeCodeType = codeTypeService.findByCodeType(
					"L120M01I_creditLoanPurpose");
			creditLoanPurpose = Util.trim(creditLoanPurposeCodeType.get(creditLoanPurpose));
		}
		
		//申貸用途
		model.addAttribute("creditLoanPurpose", creditLoanPurpose);
		
		String aoUpdAmountStr = l120s19aOutJs.getAoUpdAmount() != null 
								? this.cls1141Service.formatContentString(UtilConstants.PaperlessActingType.QUOTA, l120s19aOutJs.getAoUpdAmountCurr(), l120s19aOutJs.getAoUpdAmount().toPlainString()) 
								: "";
		//AO修改額度
		model.addAttribute("aoModifyAmount", aoUpdAmountStr);
		//備註(AO修改額度)
		model.addAttribute("aoUpdAmountRemark", StringUtils.isNotBlank(l120s19aOutJs.getAoUpdAmountRemark()) ? l120s19aOutJs.getAoUpdAmountRemark() : "");
		
		String rvUpdAmountStr = l120s19aOutJs.getRvUpdAmount() != null 
								? this.cls1141Service.formatContentString(UtilConstants.PaperlessActingType.QUOTA, l120s19aOutJs.getRvUpdAmountCurr(), l120s19aOutJs.getRvUpdAmount().toPlainString()) 
								: "";
		//審查修改額度
		model.addAttribute("rvModifyAmount",  rvUpdAmountStr);
		//備註(審查修改額度)
		model.addAttribute("rvUpdAmountRemark", StringUtils.isNotBlank(l120s19aOutJs.getRvUpdAmountRemark()) ? l120s19aOutJs.getRvUpdAmountRemark() : "");
		
		String aoUpdPeriodStr = l120s19aOutJs.getAoUpdYear() != null 
								? this.cls1141Service.formatContentString(UtilConstants.PaperlessActingType.TERM, "", l120s19aOutJs.getAoUpdYear() + "-" + l120s19aOutJs.getAoUpdMonth()) 
								: "";
		//AO修改期間
		model.addAttribute("aoModifyPeriod",  aoUpdPeriodStr);
		//備註(AO修改期間)
		model.addAttribute("aoUpdPeriodRemark", StringUtils.isNotBlank(l120s19aOutJs.getAoUpdPeriodRemark()) ? l120s19aOutJs.getAoUpdPeriodRemark() : "");
		
		String rvUpdPeriodStr = l120s19aOutJs.getRvUpdYear() != null 
								? this.cls1141Service.formatContentString(UtilConstants.PaperlessActingType.TERM, "", l120s19aOutJs.getRvUpdYear() + "-" + l120s19aOutJs.getRvUpdMonth()) 
								: "";
		//審查修改期間
		model.addAttribute("rvModifyPeriod",  rvUpdPeriodStr);
		//備註(審查修改期間)
		model.addAttribute("rvUpdPeriodRemark", StringUtils.isNotBlank(l120s19aOutJs.getRvUpdPeriodRemark()) ? l120s19aOutJs.getRvUpdPeriodRemark() : "");
		// UPGRADE: 前端須配合改Thymeleaf的樣式	start
		//AO修改利率_DB存的轉過br
//		add(new Label("aoModifyRate",  StringUtils.isNotBlank(l120s19aOutJs.getAoUpdRate()) ? l120s19aOutJs.getAoUpdRate() : "").setEscapeModelStrings(false));
		model.addAttribute("aoModifyRate",  StringUtils.isNotBlank(l120s19aOutJs.getAoUpdRate()) ? l120s19aOutJs.getAoUpdRate() : "");
		//備註(AO修改利率)_DB存的保持\n
//		add(new Label("aoUpdRateRemark",  StringUtils.isNotBlank(l120s19aOutJs.getAoUpdRateRemark()) ? l120s19aOutJs.getAoUpdRateRemark().replaceAll("\n", "<br/>") : "").setEscapeModelStrings(false));
		model.addAttribute("aoUpdRateRemark",  StringUtils.isNotBlank(l120s19aOutJs.getAoUpdRateRemark()) ? l120s19aOutJs.getAoUpdRateRemark().replaceAll("\n", "<br/>") : "");
		//審查修改利率_DB存的轉過br
//		add(new Label("rvModifyRate",  StringUtils.isNotBlank(l120s19aOutJs.getRvUpdRate()) ? l120s19aOutJs.getRvUpdRate() : "").setEscapeModelStrings(false));
		model.addAttribute("rvModifyRate",  StringUtils.isNotBlank(l120s19aOutJs.getRvUpdRate()) ? l120s19aOutJs.getRvUpdRate() : "");
		//備註(審查修改利率)_DB存的保持\n
//		add(new Label("rvUpdRateRemark",  StringUtils.isNotBlank(l120s19aOutJs.getRvUpdRateRemark()) ? l120s19aOutJs.getRvUpdRateRemark().replaceAll("\n", "<br/>") : "").setEscapeModelStrings(false));
		model.addAttribute("rvUpdRateRemark",  StringUtils.isNotBlank(l120s19aOutJs.getRvUpdRateRemark()) ? l120s19aOutJs.getRvUpdRateRemark().replaceAll("\n", "<br/>") : "");
		// UPGRADE: 前端須配合改Thymeleaf的樣式	end
		
		try {
			Map<String, String> m = this.cls1141Service.formatDisplayDisplayStringForL120S19C(mainId, UtilConstants.PaperlessActingRole.AO);
			String content = m.get("CONTENT");
			String remark = m.get("REMARK");
			//AO修改費用
			model.addAttribute("aoModifyFee", content);
			//備註(AO修改費用)
			model.addAttribute("aoUpdFeeRemark", remark);
			
			m = this.cls1141Service.formatDisplayDisplayStringForL120S19C(mainId, UtilConstants.PaperlessActingRole.RV);
			content = m.get("CONTENT");
			remark = m.get("REMARK");
			//審查修改費用
			model.addAttribute("rvModifyFee", content);
			//備註(審查修改費用)
			model.addAttribute("rvUpdFeeRemark", remark);
			//寬限期
			model.addAttribute("gracePeriod", "");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@SuppressWarnings("unchecked")
	private DataView<JSONObject> getPolicyResultView(final String id, List<Brmp002O_result_policyObj> lists) {
		final Map<String, String> statusDesc = new HashMap<String, String>();
		statusDesc.put("0", "符合");
		statusDesc.put("1", "未符合");
		statusDesc.put("M", "Missing");
		statusDesc.put("S", "不適用");

		// 未符合>Missing>不適用>正常符合
		final Map<String, String> newStatusOrder = new HashMap<String, String>();
		newStatusOrder.put("0", "4");
		newStatusOrder.put("1", "1");
		newStatusOrder.put("M", "2");
		newStatusOrder.put("S", "3");

		//消金處有定義排序順序
		Collections.sort(lists, new Comparator<Brmp002O_result_policyObj>() {
			@Override
			public int compare(Brmp002O_result_policyObj o1, Brmp002O_result_policyObj o2) {
				// 未符合>Missing>不適用>正常符合
				if (newStatusOrder.get(o1.getStatus()).compareTo(newStatusOrder.get(o2.getStatus())) == 0) {
					// 再依代碼排序
					return o1.getPolicyCode().compareTo(o2.getPolicyCode());
				} else {
					return newStatusOrder.get(o1.getStatus()).compareTo(newStatusOrder.get(o2.getStatus()));
				}
			}
		});

		// UPGRADETODO: 待確認如何調整
		return null;
		/*DataView dataView = new DataView(id, new ListDataProvider(lists)) {
			int count = 0;

			@Override
			protected void populateItem(Item item) {
				count++;
				Brmp002O_result_policyObj modelObject = (Brmp002O_result_policyObj) item.getModelObject();
				String policyCode = modelObject.getPolicyCode();
				String policyDescription = modelObject.getPolicyDescription();
				String subDescription = modelObject.getSubDescription();
				String status = modelObject.getStatus();
				String actionType = modelObject.getActionType();

				Label policyCodeLabel = new Label("policyCode", policyCode);
				Label policyDescriptionLabel = new Label("policyDescription", policyDescription);
				Label subDesciptionLabel = new Label("subDescription", subDescription);

				String statusDescription = "";
				if ("0".equals(status)) {
					statusDescription = "<input type='radio' disabled='disabled' checked />符合 <input type='radio' disabled='disabled' />未符合";
				} else if ("1".equals(status) && "A".equals(actionType)) {
					statusDescription = "<input type='radio' disabled='disabled' />符合 <input type='radio' disabled='disabled' checked /><span style='color:red'>未符合(請留意辦理)</>";
				} else if ("1".equals(status) && "R".equals(actionType)) {
					statusDescription = "<input type='radio' disabled='disabled' />符合 <input type='radio' disabled='disabled' checked /><span style='color:red'>未符合(非經報總處不得辦理)</>";
				} else {
					statusDescription = statusDesc.containsKey(status) ? statusDesc.get(status) : status;
				}

				Label statusLabel = new Label("status", statusDescription);
				statusLabel.setEscapeModelStrings(false);
				Label actionTypeLabel = new Label("actionType", actionType);


				//item.add(policyCodeLabel.add(new SimpleAttributeModifier("id", policyCode)));
				item.add(policyCodeLabel.setMarkupId(policyCode).setOutputMarkupId(true));
				item.add(policyDescriptionLabel);
				item.add(subDesciptionLabel);
				item.add(statusLabel);

				if (policyCode.startsWith("PH")) {
					item.setVisible(false);
				}

			}
		};
		return dataView;*/
	}

	private String expressValue(String result) {
		if ("1".equals(result)) {
			return "Y";
		} else if ("0".equals(result)) {
			return "N";
		}

		return result;
	}

	private String expressNumber(String result) {
		if (Util.isEmpty(result)) {
			return "N.A.";
		} else {
			return NumConverter.addComma(result);
		}
	}
}
