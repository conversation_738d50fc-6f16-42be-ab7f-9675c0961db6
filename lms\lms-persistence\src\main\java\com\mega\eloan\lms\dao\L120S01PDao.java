/* 
 * L120S01PDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S01P;

/** 相關人資料檔 **/
public interface L120S01PDao extends IGenericDao<L120S01P> {

	L120S01P findByOid(String oid);

	List<L120S01P> findByMainIdWithRType(String mainId, String rType);

	public List<L120S01P> findByMainIdWithoutRType(String mainId);

	L120S01P findByUniqueKey(String mainId, String custId, String dupNo,
			String rId, String rDupNo, String rType, String rName);

	List<L120S01P> findByMainIdAndCustIdWithRType(String mainId, String custId,
			String dupNo, String rType);

	public List<L120S01P> findByMainIdAndCustIdWithoutRType(String mainId,
			String custId, String dupNo);

	public L120S01P findByRNameWithRType(String mainId, String custId,
			String dupNo, String rType, String rName);

	public List<L120S01P> findL120S01PListByOids(String[] oids);

	public L120S01P findMaxQDateByMainIdAndCustIdWithRType(String mainId,
			String custId, String dupNo, String rType);

	public L120S01P findMaxSeqNumByMainIdAndCustIdWithRType(String mainId,
			String custId, String dupNo, String rType);

	public List<L120S01P> findByMainIdAndCustIdWithRTypeForBuildStr(
			String mainId, String custId, String dupNo, String rType);
}