package com.mega.eloan.lms.base.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanApi;

/**
 * <pre>
 * 共用功能頁面
 * </pre>
 * 
 * @since 2013/1/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/7,REX,new
 *          </ul>
 */

@Controller
@RequestMapping("/lms/lgdcommompage")
public class LMSLgdCommomPage extends AbstractEloanApi {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	@Override
	protected void execute(ModelMap model, PageParameters params) throws Exception {
		// TODO Auto-generated method stub
		
	}

}
