package com.mega.eloan.lms.obsdb.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface MisELF001Service
{
	@SuppressWarnings("rawtypes")
	public abstract List queryAvgAmtTById(String s, String s1);
    
    @SuppressWarnings("rawtypes")
    public abstract Map findELF003ProfitContributeById(String custId, String dupNo, String branch);
    
    @SuppressWarnings("rawtypes")
    public abstract Map findELF003ProfitContributeByIdDate(String custId, String dupNo, String branch,String startDate,String endDate);
}