package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF506;

/**
 * <pre>
 * 對大陸地區授信業務控管註記
 * </pre>
 * 
 * @since 2013/7/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/7/15,007625,new
 *          </ul>
 */
public interface MisELF506Service {

	public ELF506 findByCntrNo(String cntrNo);

	Map<String, Object> getByCntrNo(String cntrNo);

	List<Map<String, Object>> getByCustId(String custId, String dupNo);

	List<Map<String, Object>> getByAdcCaseNo(String adcCaseNo);

	public List<Map<String, Object>> getExceptIsEmpty(String modifyTime);

	int deleteByCntrNo(String cntrNo);

	void insert(String cntrNo, String cnLoanFg, String directFg,
			String stRadeFg, BigDecimal guar1rate, BigDecimal guar2rate,
			BigDecimal guar3rate, BigDecimal coll1rate, BigDecimal coll2rate,
			BigDecimal coll3rate, BigDecimal coll4rate, BigDecimal coll5rate,
			Timestamp modifyTime, Timestamp createTime, String createUnit,
			String modifyUnit, String documentNo, String iGolFlag,
			String cnTMUFg, String cnBusKind, String custId, String is722Flag,
			String modUnit, String docNo, Timestamp modTime, String sDate,
			String isBuy, String exItem, String loanTarget, String isType,
			String grntType, String grntClass, String othCrdType,
			String unionArea3, String nCnSblcFg, String isInstalment,
            String prodKind, String adcCaseNo, String exceptFlag, 
			String exceptFlagQAisY, String exceptFlagQAPlus);

	void delete(String cntrNo);

	List<Map<String, Object>> findLoanTargetISEmpty();

	void updateLoanTargetByCntrNo(String loanTarget, String cntrNo);

	void updateCntrNoByCntrNo(String newCntrNo, String oldCntrNo,
			String modifyUnit);

	void updateExcpetByCntrNo(String except, String exceptQAIsY,
			String exceptQAPlus, String cntrNo);

	void update72_2markFromDW(String cntrNo, String dw_FLAG_722,
			String dw_IS_BUY_722, String dw_EX_ITEM_722);
}
