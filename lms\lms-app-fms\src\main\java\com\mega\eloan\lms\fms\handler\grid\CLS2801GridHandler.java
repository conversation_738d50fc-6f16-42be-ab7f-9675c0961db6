package com.mega.eloan.lms.fms.handler.grid;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.fms.pages.CLS2801V01Page;
import com.mega.eloan.lms.fms.service.CLS2801Service;
import com.mega.eloan.lms.mfaloan.bean.ELF508;
import com.mega.eloan.lms.mfaloan.service.MisELF508Service;
import com.mega.eloan.lms.model.L140MM2A;
import com.mega.eloan.lms.model.L140S02L;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("cls2801gridhandler")
public class CLS2801GridHandler extends AbstractGridHandler {

	@Resource
	CLS2801Service service;

	@Resource
	UserInfoService userInfoService;
	
	@Resource
	CodeTypeService codetypeservice;
	
	@Resource
	BranchService branchService;
	
	@Resource
	MisELF508Service misELF508Service;
	
	Properties prop = MessageBundleScriptCreator.getComponentResource(CLS2801V01Page.class);

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryMain(ISearch pageSetting,
			PageParameters params) throws CapException {
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());
		
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
		FlowDocStatusEnum docStatusEnum = FlowDocStatusEnum.getEnum(docStatus);
		if (docStatusEnum != null) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);	
		} 
		

		List<L140MM2A> src_list = (List<L140MM2A>) service.findPage(
				L140MM2A.class, pageSetting).getContent();
		for (L140MM2A model : src_list) {
			Map<String, Object> row = new HashMap<String, Object>();
			LMSUtil.meta_to_map(row, model, new String[] { "oid", "mainId", "seq", "custId", "custName", "cntrNo", "hold_no", "owner_id"});
			L140S02L l140s02l = model.getL140s02l();
			String hold_no = "";
			String owner_id = "";			
			if(l140s02l!=null){
				hold_no = l140s02l.getHold_no();
				owner_id = l140s02l.getOwner_id();
			}
			row.put(EloanConstants.MAIN_OID, model.getOid());
			row.put(EloanConstants.MAIN_DOC_STATUS, model.getDocStatus());
			row.put("oid", model.getOid());
			row.put("mainId", model.getMainId());
			row.put("seq", model.getSeq());
			row.put("custId", model.getCustId());
			row.put("custName", model.getCustName());
			row.put("cntrNo", model.getCntrNo());			
			row.put("hold_no", hold_no);
			row.put("owner_id", owner_id);

			list.add(row);
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
	
	public CapMapGridResult queryGetCntrno(ISearch pageSetting,
			PageParameters params) throws CapException {
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String branch = Util.trim(user.getUnitNo());

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		List<ELF508> elf508Data = null;
		boolean queryELF508 = false;
		
		Set<String> elf508CntrNoSet = new HashSet<String>();
		if(custId.length()==10){
			queryELF508 = true;
		}else{
			queryELF508 = true;
		}
		
		if (queryELF508){
			elf508Data = misELF508Service.findByCustidDupno(custId, dupNo);
			for (ELF508 elf508 : elf508Data) {
				Map<String, Object> row = new HashMap<String, Object>();

				String tBrn = Util.trim(elf508.getElf508_cntrno()).substring(0, 3);
				if (!tBrn.equals(branch)) {
					// 避免重覆加入一筆空值
					continue;
				}
				String cntrNo = elf508.getElf508_cntrno();
				elf508CntrNoSet.add(cntrNo);
				row.put("cntrNo", cntrNo);
				row.put("sDate", "");
				list.add(row);
			}
		}
		
		return new CapMapGridResult(list, list.size());
	}
}
