/* 
 * CLS8011V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;

/**
 * <pre>
 * 個金個人資料清冊
 * </pre>
 * 
 * @since 2014/04/01
 * <AUTHOR> @version <ul>
 *          
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls8011v01")
public class CLS8011V01Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CLSDocStatusEnum.編製中);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.Filter, LmsButtonEnum.Add,
				LmsButtonEnum.Delete, LmsButtonEnum.FCheck,
				LmsButtonEnum.Print);
		// build i18n
		renderJsI18N(CLS8011V01Page.class);

		// UPGRADE: 待確認畫面是否正常
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript","loadScript('pagejs/cls/CLS8011V01Page');");
	}

}
