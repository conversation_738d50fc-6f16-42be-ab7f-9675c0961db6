

if (!window.LMS140M01QAction) {
    window.LMS140M01QAction = {
        fhandle: "lmscommonformhandler",
        gridhandler: 'lmscommongridhandler',
        mainId: "",
        formId: "LMS140M01QForm",
        isInit: false,
        isChinaLoanInit: false,
        formData: {},
        // 是否為塞值
        isInject: false,
        ELF442Grid: null, // 帳號資料表
        /**
         * 清空欄位值並隱藏
         *
         * @param {Object}
         *            $obj jquery 物件
         */
        cleanTrHideInput: function($obj){
            $obj.hide();
            $obj.find(["input", "select", "span.field"]).each(function(){
                var $this = $(this);
                $this.each(function(){
                    switch (this.nodeName.toLowerCase()) {
                        case 'input':
                            var item = $(this).attr("type");
                            switch (item.toLowerCase()) {
                                case "text":
                                case "hidden":
                                case "password":
                                    $(this).val("");
                                    break;
                                case "radio":
                                case "checkbox":
                                    $(this).removeAttr("checked");
                                    break;
                                default:
                                    $(this).val("");
                                    break;
                            }
                            break
                        case 'select':
                            $this.val("").trigger("change");
                            break;
                        case "span":
                            $this.html("");
                            break;
                    }
                });
            });
        },
        initChinaLoanEvent: function($formObject){
            var $cnLoanFg = $formObject.find("input[name='cnLoanFg']");
            $cnLoanFg.click(function(){
                var cnLoanFg = $(this).val();
                if (cnLoanFg == "Y") {
                    $formObject.find(".chinaLoan").show();
					//J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
			        $('#directFg').change();
					$('#bdirectFg').change();
                }
                else {
					$formObject.find(".chinaLoan").hide();
                    $formObject.find("input[name='iGolFlag']").attr("checked", false);
                    $formObject.find("#directFg").val('');
					$formObject.find("#cnBusKind").val('');
                    $formObject.find("input[name='sTradeFg']").attr("checked", false);
                    $formObject.find("input[name='rickTrFg']").attr("checked", false);
                    $formObject.find("#guar1Rate,#guar2Rate,#guar3Rate,#coll1Rate,#coll2Rate,#coll3Rate,#coll4Rate,#coll5Rate").val('0');
					
					//J-105-0074-001 Web e-Loan 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
					$formObject.find("input[name='nCnSblcFg']").attr("checked", false);
					
                }
            });
            
            var $sTradeFg = $formObject.find("input[name='sTradeFg']");
            $sTradeFg.click(function(){
                var sTradeFg = $(this).val();
                if (sTradeFg == "N") {
                    $formObject.find(".sTrade").show();
                }
                else {
                    $formObject.find(".sTrade").hide();
                    $formObject.find("input[name='rickTrFg']").attr("checked", false);
                    $formObject.find("#guar1Rate,#guar2Rate,#guar3Rate,#coll1Rate,#coll2Rate,#coll3Rate,#coll4Rate,#coll5Rate").val('0');
                }
            });
            
            var $rickTrFg = $formObject.find("input[name='rickTrFg']");
            $rickTrFg.click(function(){
                var rickTrFg = $(this).val();
                if (rickTrFg == "Y") {
                    $formObject.find(".rickTr").show();
                }
                else {
                    $formObject.find(".rickTr").hide();
                    $formObject.find("#guar1Rate,#guar2Rate,#guar3Rate,#coll1Rate,#coll2Rate,#coll3Rate,#coll4Rate,#coll5Rate").val('0');
                }
            });
			
			//J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
			//授信對象別代碼切換
		    $('#directFg').change(function(){
		        var value = $(this).val();
				switch (value) {
		            case "12":
		            case "14":
		                $("#showCnBusKind").show();
						//$("#showBcnBusKind").show();
		                break;
		            default:
		                $("#showCnBusKind").hide();
						//$("#showBcnBusKind").hide();
		                break;
		        }
		    });
            $('#bdirectFg').change(function(){
		        var value = $(this).val();
				 switch (value) {
		            case "12":
		            case "14":
					    //$("#showCnBusKind").show();
						$("#showBcnBusKind").show();
		                break;
		            default:
					    //$("#showCnBusKind").hide();
						$("#showBcnBusKind").hide();
		                break;
		        }
		    });
			
			//J-104-0073-001 Web e-Loan授信系統修改大陸地區控管註記授信對象別檢核。			
            $('#cnBusKind').click(function(){
				var value = $("#directFg").val();
				switch (value) {
		            case "12":
					    $("#cnBusKind option[value='A']").attr('disabled', false);
				        $("#cnBusKind option[value='B']").attr('disabled', false);
		                break;
		            case "14":
					    $("#cnBusKind option[value='A']").attr('disabled', true);
						$("#cnBusKind option[value='B']").attr('disabled', true);
		                break;
		            default:   
					    $("#cnBusKind option[value='A']").attr('disabled', false);
				        $("#cnBusKind option[value='B']").attr('disabled', false);
		                break;
		        }
				
				
		    }); 
			//J-104-0073-001 Web e-Loan授信系統修改大陸地區控管註記授信對象別檢核。
			$('#directFg').click(function(){
				var value = $("#directFg").val();
				switch (value) {
		            case "12":
					    $("#cnBusKind option[value='A']").attr('disabled', false);
				        $("#cnBusKind option[value='B']").attr('disabled', false);
		                break;
		            case "14":
					    $("#cnBusKind option[value='A']").attr('disabled', true);
						$("#cnBusKind option[value='B']").attr('disabled', true);
						break;
		            default:   
					    $("#cnBusKind option[value='A']").attr('disabled', false);
				        $("#cnBusKind option[value='B']").attr('disabled', false);
		                break;
		        }
				
		    }); 
			 
        },
        /**
         * 對大陸地區授信業務控管註記
         *
         * @param {Object}
         *            cntrNoMainId
         * @param {Object}
         *            isforQuery
         */
        openChinaLoan: function(cntrNoMainId, isforQuery){
            var $form = $("#LMS140M01QForm");
            $.ajax({
                handler: LMS140M01MAction.fhandle,
                action: "queryL140M01Q",
                data: {
                    cntrNoMainId: cntrNoMainId,
                    noOpenDoc: true
                },
                success: function(obj){
                    LMS140M01MAction.initChinaLoan($form);
                    $form.injectData(obj);
                    if (obj.showBefore) {
                        $form.find(".showBefore").show();
                    }
                    else {
                        $form.find(".showBefore").hide();
                    }
                    
                    if (obj.showDerv) {
                        $form.find(".showDerv").show();
                    }
                    else {
                        $form.find(".showDerv").hide();
                    }
					
					//J-105-0074-001 Web e-Loan 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
					if(obj.showForCNCntrno){
						$form.find(".showForCNCntrno").show();
					}else{
						$form.find(".showForCNCntrno").hide();
					}
                    
                    $form.readOnlyChilds(isforQuery, ".noEdit");
                    
                    
                    $("#cnTMUFg option[value='-']").attr('disabled', true);
                    
                    $('#cnTMUFg').each(function(){
                        this.rejectDisabled = function(){
                            if (this.options[this.selectedIndex].disabled) {
                                if (this.lastSelectedIndex) {
                                    this.selectedIndex = this.lastSelectedIndex;
                                }
                                else {
                                    var first_enabled = $(this).children('option:not(:disabled)').get(0);
                                    this.selectedIndex = first_enabled ? first_enabled.index : 0;
                                }
                            }
                            else {
                                this.lastSelectedIndex = this.selectedIndex;
                            }
                        };
                        this.rejectDisabled();
                        this.lastSelectedIndex = this.selectedIndex;
                        $(this).children('option[disabled]').each(function(){
                            $(this).css('color', '#CCC');
                        });
                        $(this).change(function(){
                            this.rejectDisabled();
                        });
                    });
					
					//J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
					$('#directFg').change();
					$('#bdirectFg').change();
                    
                },
                error: function(obj){
                    $.thickbox.close();
                }
            });
            
            var btn = {
                "saveData": function(){
                    var $rickTrFg = $form.find("input[name='rickTrFg']");
                    if ($form.find("input[name='rickTrFg']:checked").val() == "Y") {
                        var guar1Rate = $form.find("#guar1Rate").val();
                        guar1Rate = isNaN(guar1Rate) || guar1Rate == "" ? 0 : parseInt(guar1Rate, 10);
                        var guar2Rate = $form.find("#guar2Rate").val();
                        guar2Rate = isNaN(guar2Rate) || guar2Rate == "" ? 0 : parseInt(guar2Rate, 10);
                        var guar3Rate = $form.find("#guar3Rate").val();
                        guar3Rate = isNaN(guar3Rate) || guar3Rate == "" ? 0 : parseInt(guar3Rate, 10);
                        var coll1Rate = $form.find("#coll1Rate").val();
                        coll1Rate = isNaN(coll1Rate) || coll1Rate == "" ? 0 : parseInt(coll1Rate, 10);
                        var coll2Rate = $form.find("#coll2Rate").val();
                        coll2Rate = isNaN(coll2Rate) || coll2Rate == "" ? 0 : parseInt(coll2Rate, 10);
                        var coll3Rate = $form.find("#coll3Rate").val();
                        coll3Rate = isNaN(coll3Rate) || coll3Rate == "" ? 0 : parseInt(coll3Rate, 10);
                        var coll4Rate = $form.find("#coll4Rate").val();
                        coll4Rate = isNaN(coll4Rate) || coll4Rate == "" ? 0 : parseInt(coll4Rate, 10);
                        var coll5Rate = $form.find("#coll5Rate").val();
                        coll5Rate = isNaN(coll5Rate) || coll5Rate == "" ? 0 : parseInt(coll5Rate, 10);
                        
                        var total = (guar1Rate + guar2Rate + guar3Rate +
                        coll1Rate +
                        coll2Rate +
                        coll3Rate +
                        coll4Rate +
                        coll5Rate);
                        
                        if (!(total == 100)) {
                            API.showMessage(i18n.lmsl140m01m['L140M01Q.MSG001']);
                            return false;
                        }
                    }
                    
					var directFg = $form.find("#directFg").val();
					if(directFg =="12" || directFg == "14"){
						
						//J-104-0073-001 Web e-Loan授信系統修改大陸地區控管註記授信對象別檢核。	
						if (directFg == "14") {
							var cnBusKindValue = $form.find("#cnBusKind").val();
							switch (cnBusKindValue) {
					            case "A":
								case "B":
								    $("#cnBusKind").val("");
					                break;
					            default:   
					                break;
					        } 
						}
						
						var cnBusKind = $form.find("#cnBusKind").val();
						if(cnBusKind == "" || cnBusKind == "undefined"){
							API.showMessage("「"+i18n.lmsl140m01m['L140M01Q.CNBUSKIND']+"」"+i18n.def['val.required']);
                            return false;
						}					
					}
					
                    if ($form.valid()) {
                        $.ajax({
                            handler: LMS140M01MAction.fhandle,
                            formId: LMS140M01MAction.formId,
                            action: "saveL140M01Q",
                            data: {
                                cntrNoMainId: cntrNoMainId,
                                LMS140M01QForm: JSON.stringify($("#LMS140M01QForm").serializeData())
                            },
                            success: function(obj){
                            
                            }
                        });
                    }
                },
                "reQuery": function(){
                    $.ajax({
                        handler: LMS140M01MAction.fhandle,
                        action: "requeryHistoryToL140M01Q",
                        data: {
                            cntrNoMainId: cntrNoMainId,
                            LMS140M01QForm: JSON.stringify($("#LMS140M01QForm").serializeData()),
                            noOpenDoc: true
                        },
                        success: function(obj){
							
							////J-105-0074-001 Web e-Loan 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
                            $form.find("input[name='bcnLoanFg'],input[name='biGolFlag'],input[name='bsTradeFg'],input[name='brickTrFg'],input[name='bnCnSblcFg']").attr("checked", false);
                            $form.find("#bdirectFg").val('');
							$form.find("#bcnBusKind").val('');
                            $form.find("#bguar1Rate,#bguar2Rate,#bguar3Rate,#bcoll1Rate,#bcoll2Rate,#bcoll3Rate,#bcoll4Rate,#bcoll5Rate").val('');
							
							//BGN J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
							$form.find("#bisType").val('');
							$form.find("#bgrntType").val('');
							$form.find("#bgrntClass").val('');
							//$form.find("#bothCrdType").val('');
							$( "[name=bothCrdType]"). removeAttr("checked" );
							$form.find("#bloanTarget").val('');
							$form.find("#bloanTargetDscr").val('');
							//END J-104-0279-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別

                            LMS140M01MAction.initChinaLoan($form);
                            $form.injectData(obj);
                            if (obj.showBefore) {
                                $form.find(".showBefore").show();
                            }
                            else {
                                $form.find(".showBefore").hide();
                            }
                            
                            if (obj.showDerv) {
                                $form.find(".showDerv").show();
                            }
                            else {
                                $form.find(".showDerv").hide();
                            }
							
							//J-105-0074-001 Web e-Loan 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
							if(obj.showForCNCntrno){
								$form.find(".showForCNCntrno").show();
							}else{
								$form.find(".showForCNCntrno").hide();
							}
                            
							//J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
					        $('#directFg').change();
							$('#bdirectFg').change();
                        },
                        error: function(obj){
                            $.thickbox.close();
                        }
                    });
                },
                "close": function(){
                    $.thickbox.close();
                }
            };
            if (isforQuery) {
                delete btn["saveData"];
                delete btn["reQuery"];
            }
            $("#openBox_L140M01Q").thickbox({
                title: i18n.def['common.L140M01Q'],
                width: 850,
                height: 530,
                modal: true,
                readOnly: _openerLockDoc == "1",
                i18n: i18n.def,
                buttons: btn
            });
        }
    };
    
    
    
    function changCityValue(tCity, tArea, tSite3, tSite4, defaultSelect, formDataArr, isClear){
        var value = tCity.val();
        
        var obj = QueryCityCode.getCode("2", value);
        
        tArea.setItems({
            item: obj,
            value: LMS140M01MAction.formData[formDataArr[0]] || ""
        });
        
        if (isClear) {
            tSite3.html(defaultSelect);
            tSite4.html(defaultSelect);
            
            for (var i = 0; i < formDataArr.length; i++) {
                LMS140M01MAction.formData[formDataArr[i]] = "";
            }
        }
    }
    
    function changAreaValue(tCity, tArea, tSite3, tSite4, defaultSelect, formDataArr, isClear){
        if (isClear) {
            for (var i = 1; i < formDataArr.length; i++) {
                LMS140M01MAction.formData[formDataArr[i]] = "";
            }
        }
        
        tSite3.setItems({
            item: LMS140M01MAction.getSITE3(tCity.find(":selected").text(), tArea.find(":selected").text(), "N"),
            value: util.addZeroBefore($.trim(LMS140M01MAction.formData[formDataArr[1]]).replace(",", "") ||
            "", 4)
        });
        
        tSite4.setItems({
            item: LMS140M01MAction.getSITE4(tCity.find(":selected").text(), tArea.find(":selected").text(), "N"),
            value: LMS140M01MAction.formData[formDataArr[2]] || ""
        });
        
    }
}
