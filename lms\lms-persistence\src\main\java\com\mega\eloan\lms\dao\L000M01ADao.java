/* 
 * L000M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L000M01A;

/** 近期已收案件檔 **/
public interface L000M01ADao extends IGenericDao<L000M01A> {

	L000M01A findByOid(String oid);

	List<L000M01A> findByMainId(String mainId);

	List<L000M01A> findBySrcMainId(String srcMainId);
	
	List<L000M01A> findByCustIdDupId(String custId,String DupNo);
	

}