package com.mega.eloan.lms.dao.impl;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L250M01CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L250M01C;

/** 模擬動審版本資料 **/
@Repository
public class L250M01CDaoImpl extends LMSJpaDao<L250M01C, String> implements
		L250M01CDao {

	@Override
	public L250M01C findLatestVersion(String rptId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "rptId", rptId);
		search.addOrderBy("version", true);
		return findUniqueOrNone(search);
	}

}