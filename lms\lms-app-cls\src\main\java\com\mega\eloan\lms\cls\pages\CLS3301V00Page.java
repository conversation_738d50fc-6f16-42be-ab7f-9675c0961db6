package com.mega.eloan.lms.cls.pages;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.cls.panels.CLS3301V00FilterPanel;

/**
 * <pre>
 * IVR電話錄音grid畫面(個金)
 * </pre>
 * 
 * @since 2011/10/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/21,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls3301v00")
public class CLS3301V00Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.Filter);
		// 套用哪個i18N檔案
		renderJsI18N(CLS3301V00Page.class);
		setupIPanel(new CLS3301V00FilterPanel(PANEL_ID), model, params);

		model.addAttribute("hasHtml", false);
	}
}
