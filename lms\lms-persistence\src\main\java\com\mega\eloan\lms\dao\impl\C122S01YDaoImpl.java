/* 
 * C122S01YDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.sql.Timestamp;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.C122S01YDao;
import com.mega.eloan.lms.model.C122S01Y;

/** 個金進件管理地政士名單 **/
@Repository
public class C122S01YDaoImpl extends LMSJpaDao<C122S01Y, String> implements
		C122S01YDao {

	@Override
	public C122S01Y findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C122S01Y> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C122S01Y> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C122S01Y> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<C122S01Y> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C122S01Y> findMatchLaaList(String mainId, String[] laaName_arr) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (Util.isNotEmpty(laaName_arr) && laaName_arr.length > 0) {
			search.addSearchModeParameters(SearchMode.IN, "laaName",
					laaName_arr);
		}
		List<C122S01Y> list = createQuery(C122S01Y.class, search)
				.getResultList();

		return list;
	}

	// J-112-0006 撈取前一案/後一案之有相同地政士的房貸案件(引介來源為地政士引介才可輸入地政士)
	@Override
	public C122S01Y findLaaCaseBy_brNo(String ownBrId, String mainId,
			Timestamp applyTS, String type, String laaName) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "laaName", laaName);
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "c122m01a.ownBrId",
				ownBrId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "c122m01a.deletedTime", null);
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "c122m01a.docStatus", "F00");// 排除作廢案件:F00
		search.addSearchModeParameters(SearchMode.EQUALS, "c122m01a.applyKind",	"E");// 只撈房貸主借人案件
		if (Util.equals(type, "last")) {// 本案前
			search.addSearchModeParameters(SearchMode.LESS_THAN,
					"c122m01a.applyTS", CapDate.parseToString(applyTS));
		}
		if (Util.equals(type, "next")) {// 本案後
			search.addSearchModeParameters(SearchMode.GREATER_THAN,
					"c122m01a.applyTS", CapDate.parseToString(applyTS));
		}

		search.setFirstResult(0).setMaxResults(1);

		if (Util.equals(type, "last")) {
			search.addOrderBy("c122m01a.applyTS", true);
		}
		if (Util.equals(type, "next")) {
			search.addOrderBy("c122m01a.applyTS", false);
		}

		List<C122S01Y> list = createQuery(C122S01Y.class, search)
				.getResultList();
		C122S01Y c122s01y = null;

		if (Util.isNotEmpty(list) && list.size() > 0) {
			return c122s01y = list.get(0);
		}

		return c122s01y;
	}

}