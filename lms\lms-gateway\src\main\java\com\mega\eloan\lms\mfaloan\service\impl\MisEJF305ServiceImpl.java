package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisEJF305Service;

@Service
public class MisEJF305ServiceImpl extends AbstractMFAloanJdbc implements
		MisEJF305Service {
	@Override
	public List<Map<String, Object>> getByCustId_LNF022_AVL_FAMT_T(
			String custId, BigDecimal famt) {
		return getJdbc().queryForList("MIS.EJF305.001",
				new Object[] { custId, custId, custId, famt });
	}
	
	@Override
	public List<Map<String, Object>> getByCustId(
			String custId) {
		return getJdbc().queryForList("MIS.EJF305.002",
				new Object[] { custId});
	}
}
