package com.mega.eloan.lms.dc.action;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;

import org.apache.commons.io.IOUtils;
import org.w3c.dom.Document;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.base.XMLHandler;
import com.mega.eloan.lms.dc.bean.L140M01BBean;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * Parser140M01B
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          <li>2013-03-26 Modify by Bang:Clob檔案改為直接寫至loadDB2下
 *          </ul>
 */
public class Parser140M01B extends AbstractLMSCustParser {

	/**
	 * @param pid
	 * @param doViewName
	 * @param formGroup
	 */
	public Parser140M01B(String pid, String doViewName, String formGroup) {
		super(pid, doViewName, formGroup);
	}

	/**
	 * 讀取,處理及轉換
	 * 
	 * @param dxlPath
	 *            String : .dxl檔存放路徑
	 * @param dxlName
	 *            :.dxl列表中的.dxl檔名
	 * @param strBrn
	 *            String:分行名稱
	 * @param domDoc
	 *            DOM Document:已轉為DOM Document的.dxl檔
	 */
	@SuppressWarnings("unused")
	protected void transferDXL(String dxlPath, String dxlName, String strBrn,
			Document domDoc, String dxlXml) {
		long t1 = System.currentTimeMillis();
		XMLHandler xml = new XMLHandler();
		try {
			String[] k1 = dxlName.split(TextDefine.ATTACH_DXL);// EX:{FLMS120M01_2E3761E1BB971A2B48257A7D00143D9C,.dxl}
			String[] k2 = k1[0].split("_");// EX:{FLMS120M01,2E3761E1BB971A2B48257A7D00143D9C}
			String tmpMainId = "";

			if (k2.length == 2) {
				tmpMainId = k2[1];// 主檔
			} else {
				tmpMainId = k2[2];// 明細檔之UNID
			}

			// 20130417
			// Sandra若CNTRDOCID有值，以CNTRDOCID為140開頭所有的table的mainid；若無值，則取unid為mainid
			// 20130419 Sandra因CNTRDOCID仍會有重覆，建霖mail通知調整使用WEBELOANMAINID
			String cntrDocId = getItemValue(domDoc, "WEBELOANMAINID");
			tmpMainId = cntrDocId.isEmpty() ? tmpMainId : cntrDocId;

			char[] itemTypeAry = new char[] { '1', '2', '3', '4', '5', '6',
					'7', '8', '9', 'A' };
			for (char itValue : itemTypeAry) {
				L140M01BBean L140b = new L140M01BBean();
				String pageValue = "", dscrValue = "", aloanValue = "";

				L140b.setOid(Util.getOID());
				L140b.setMainId(tmpMainId);
				L140b.setItemType(String.valueOf(itValue));
				// TODO:ItemDscr為CLOB型態,但不是每一個對應到ItemDscr的dxl檔欄位,其值都會有<break/>這個tag
				// 避免有lost的欄位,故在取得ItemDscr之值時一律用 getItemValueByMup 此Method
				// 2013-02-06 Modify by Bang
				switch (itValue) {
				case '1':
					pageValue = getItemValue(domDoc, "PrintAtch1");
					L140b.setPageNum(pageValue);
					dscrValue = xml
							.getItemValueByRichTextType2(domDoc, "Terms");
					// L140b.setItemDscr(dscrValue);
					break;
				case '2':
					pageValue = getItemValue(domDoc, "PrintAtch2");
					L140b.setPageNum(pageValue);
					dscrValue = xml.getItemValueByRichTextType2(domDoc,
							"InteRate");
					// L140b.setItemDscr(dscrValue);
					break;
				case '3':
					pageValue = getItemValue(domDoc, "PrintAtch3");
					L140b.setPageNum(pageValue);
					dscrValue = xml.getItemValueByRichText(domDoc, "Guarantee");
					// L140b.setItemDscr(dscrValue);
					break;
				case '4':
					pageValue = getItemValue(domDoc, "PrintAtch4");
					L140b.setPageNum(pageValue);
					dscrValue = xml.getItemValueByRichText(domDoc,
							"AnotherTerms");
					// L140b.setItemDscr(dscrValue);
					break;
				case '5':
					pageValue = getItemValue(domDoc, "PrintAtch5");
					L140b.setPageNum(pageValue);
					dscrValue = xml.getItemValueByRichText(domDoc,
							"ChangeReason");
					break;
				case '6':
					String colNm1 = "Descr1";
					dscrValue = this.processRTF(dxlName, domDoc,
							TextDefine.SCHEMA_LMS,
							this.getRichText(dxlXml, colNm1), colNm1);
					break;
				case '7':
					String colNm2 = "Descr2";
					dscrValue = this.processRTF(dxlName, domDoc,
							TextDefine.SCHEMA_LMS,
							this.getRichText(dxlXml, colNm2), colNm2);
					break;
				case '8':
					String colNm3 = "Descr3";
					dscrValue = this.processRTF(dxlName, domDoc,
							TextDefine.SCHEMA_LMS,
							this.getRichText(dxlXml, colNm3), colNm3);
					break;
				case '9':
					dscrValue = xml.getItemValueByRichText(domDoc, "ToALoan_1");
					aloanValue = getItemValue(domDoc, "ToALoan");
					L140b.setToALoan(aloanValue);
					break;
				case 'A':
					//dscrValue = xml.getItemValueByRichText(domDoc, "HQComm");
					dscrValue = xml.getItemValueByRichText(domDoc, "HQComm_1");
					aloanValue = getItemValue(domDoc, "HQMemo");
					L140b.setToALoan(aloanValue);
					break;

				default:
					break;
				}

				PrintWriter clobWrite = null;
				// 當pageNum、itemDscr、toALoan欄位均為空白時不需產生資料
				String pageNum = L140b.getPageNum();
				// String itemDscr =L140b.getItemDscr();
				String itemDscr = dscrValue;
				String toALoan = L140b.getToALoan();
				if (!pageNum.trim().equals(TextDefine.EMPTY_STRING)
						|| !itemDscr.trim().equals(TextDefine.EMPTY_STRING)
						|| !toALoan.trim().equals(TextDefine.EMPTY_STRING)) {

					// 2013-02-06 Add by Bang 寫出文字檔(讀入時為 big5 所以這裡必需用ms950)
					if (!itemDscr.trim().equals(TextDefine.EMPTY_STRING)) {
						// 在L140M01B.txt中ItemDscr欄位需放稍早DXLParser時所產生的htm所在之路徑
						// Ex:004/FLMS740M01_48256C5500297C9548256D75002F259F_Aterms_Attch.htm
						// TODO:Notes欄位Aterms_Attch雖是richText格式但此Table
						// 並不需要該欄之值,因此L140M01B.txt不需寫入其htm之路徑
						// ItemDscr之值若是由多欄位或是非richText型態的text組成,則需另寫成一個htm檔
						// 在L140M01B.txt中ItemDscr欄位值則為此htm檔之路徑
						// EX:004/004_0C93BFA8318B7F6C48256D340014973B_ItemDscr_1.htm
						String clobOutFile = strBrn + "_" + L140b.getMainId()
								+ "_ItemDscr_" + itValue + ".htm";
						// 2013-03-26 Modify by Bang:改為直接寫至loadDB2下
						clobWrite = new PrintWriter(
								new BufferedWriter(
										new OutputStreamWriter(
												new FileOutputStream(
														new File(
																this.loadDB2ClobPath
																		+ File.separator
																		+ clobOutFile)),
												this.configData.isOnlineMode() ? TextDefine.ENCODING_UTF8
														: TextDefine.ENCODING_MS950)),
								true);

						clobWrite.println(itemDscr);
						L140b.setItemDscr(strBrn + "/" + clobOutFile);
					}

					this.txtWrite.println(L140b.toString());
					this.parserTotal++;
				}

				IOUtils.closeQuietly(clobWrite);
			}// end of for(char itValue : itemTypeAry)
		} catch (Exception e) {
			String errmsg = "【" + strBrn
					+ "】分行執行Parser140M01B 之transferDXL時產生錯誤,dxl檔名:" + dxlName
					+ ",dxlPath=" + dxlPath;
			throw new DCException(errmsg, e);
		} finally {
			if (DEBUG && logger.isDebugEnabled()) {
				logger.debug("@@@@@@@@ TOTAL_COST="
						+ (System.currentTimeMillis() - t1));
			}
		}
	}

}
