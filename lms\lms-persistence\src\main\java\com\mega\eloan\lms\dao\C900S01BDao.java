/* 
 * C900S01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900S01B;

/** 檢附資訊檔 **/
public interface C900S01BDao extends IGenericDao<C900S01B> {

	C900S01B findByOid(String oid);

	List<C900S01B> findByMainId(String mainId);
	
	List<C900S01B> getAll();

	C900S01B findByUniqueKey(String oid);

	List<C900S01B> findByIndex01(String oid);

	C900S01B findByItemCode(String itemCode);
}