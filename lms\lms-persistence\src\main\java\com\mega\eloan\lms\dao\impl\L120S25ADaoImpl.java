/* 
 * L120S25ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.L120S25ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S25A;

/** BIS評估表 **/
@Repository
public class L120S25ADaoImpl extends LMSJpaDao<L120S25A, String> implements
		L120S25ADao {

	@Override
	public L120S25A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S25A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S25A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S25A> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L120S25A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S25A> findByIndex02(String mainId, String bisCntrNo_s25a) {
		ISearch search = createSearchTemplete();
		List<L120S25A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (bisCntrNo_s25a != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "bisCntrNo_s25a",
					bisCntrNo_s25a);
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S25A> findByMainIdAndCustId(String mainId,
			String bisCustId_s25a, String bisDupNo_s25a) {
		ISearch search = createSearchTemplete();
		List<L120S25A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (bisCustId_s25a != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "bisCustId_s25a",
					bisCustId_s25a);
		if (bisDupNo_s25a != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "bisDupNo_s25a",
					bisDupNo_s25a);
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

}