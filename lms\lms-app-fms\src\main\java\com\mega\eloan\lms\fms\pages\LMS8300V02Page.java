package com.mega.eloan.lms.fms.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;


/**
 * <pre>
 *AO帳務管理原維護 - 待覆核
 * </pre>
 * 
 * @since 2018
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller@RequestMapping(path = "/fms/lms8300v02")
public class LMS8300V02Page extends AbstractEloanInnerView {

	public LMS8300V02Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_待覆核);
		// 加上Button
		List<EloanPageFragment> btns = new ArrayList<>();
		btns.add(LmsButtonEnum.View);
//		btns.add(CreditButtonEnum.FCheck); //覆核

		addToButtonPanel(model, btns);
		
		renderJsI18N(LMS8300V01Page.class);
		renderJsI18N(LMS8300M01Page.class);
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS8300V02Page.js" };
	}
}
