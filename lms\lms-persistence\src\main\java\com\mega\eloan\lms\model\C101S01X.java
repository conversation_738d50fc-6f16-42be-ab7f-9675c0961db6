/* 
 * C101S01X.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 勞工紓困4.0簡易信用評分檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C101S01X", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo"}))
public class C101S01X extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 是否有其他財力或資產證明 **/
	@Size(max=1)
	@Column(name="ISOTHERASSETS", length=1, columnDefinition="CHAR(1)")
	private String isOtherAssets;

	/** 申請額度 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="APPLYQUOTA", columnDefinition="DECIMAL(2,0)")
	private Integer applyQuota;

	/** 申請日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="APPLYDATE", columnDefinition="DATE")
	private Date applyDate;

	/** 投保月數 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="INSUREDMONTH", columnDefinition="DECIMAL(2,0)")
	private Integer insuredMonth;

	/** 月投保薪資達23800月數 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="UPTO23800MONTH", columnDefinition="DECIMAL(2,0)")
	private Integer upTo23800Month;

	/** A1得分 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="A1SCORE", columnDefinition="DECIMAL(3,0)")
	private Integer a1Score;

	/** B1得分 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="B1SCORE", columnDefinition="DECIMAL(3,0)")
	private Integer b1Score;

	/** C1得分 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="C1SCORE", columnDefinition="DECIMAL(3,0)")
	private Integer c1Score;

	/** 總分 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="TOTALSCORE", columnDefinition="DECIMAL(3,0)")
	private Integer totalScore;

	/** 
	 * 不核可項目1<p/>
	 * 聯徵之債務協商註記
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="NOTALLOWA", columnDefinition="DECIMAL(1,0)")
	private Integer notAllowA;

	/** 
	 * 不核可項目2<p/>
	 * 聯徵任一筆貸款最近一期授信繳款狀況≧1
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="NOTALLOWB", columnDefinition="DECIMAL(1,0)")
	private Integer notAllowB;

	/** 
	 * 不核可項目3<p/>
	 * 拒往紀錄
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="NOTALLOWC", columnDefinition="DECIMAL(1,0)")
	private Integer notAllowC;

	/** 
	 * 不核可項目4<p/>
	 * 退票未註銷達3張
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="NOTALLOWD", columnDefinition="DECIMAL(1,0)")
	private Integer notAllowD;

	/** 
	 * 不核可項目5<p/>
	 * 聯徵之信用卡強制停卡註記
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="NOTALLOWE", columnDefinition="DECIMAL(1,0)")
	private Integer notAllowE;

	/** 是否已核可 **/
	@Size(max=1)
	@Column(name="ISAPPROVED", length=1, columnDefinition="CHAR(1)")
	private String isApproved;

	/** 
	 * 流程註記<p/>
	 * A-授權內,B-授權外,C-一般流程
	 * A、B - 人工審核
	 * C - RPA自動化流程
	 */
	@Size(max=1)
	@Column(name="FLOWFLAG", length=1, columnDefinition="CHAR(1)")
	private String flowFlag;

	/** 是否為薪轉戶
	 *  from C101S01B.ptaFlag ** /
	 */
	@Size(max=1)
	@Column(name="ISPTAFLAG", length=1, columnDefinition="CHAR(1)")
	private String isPtaFlag;

	/** 是否為利害關係人 **/
	@Size(max=1)
	@Column(name="ISSTAKEHOLDER", length=1, columnDefinition="CHAR(1)")
	private String isStakeHolder;

	/** 其它補充註記 **/
	@Size(max=1)
	@Column(name="ISOTHERADDMARK", length=1, columnDefinition="CHAR(1)")
	private String isOtherAddMark;

	/** 身分證號更改紀錄 **/
	@Size(max=1)
	@Column(name="ISIDCHANGEDMARK", length=1, columnDefinition="CHAR(1)")
	private String isIdChangedMark;

	/** 案件通報紀錄 **/
	@Size(max=1)
	@Column(name="ISCASENOTIFIEDMARK", length=1, columnDefinition="CHAR(1)")
	private String isCaseNotifiedMark;

	/** Ip **/
	@Size(max=20)
	@Column(name="IP", length=20, columnDefinition="VARCHAR(20)")
	private String Ip;

	/** 相同Ip數量 **/
	@Digits(integer=4, fraction=0, groups = Check.class)
	@Column(name="IPCOUNT", columnDefinition="DECIMAL(4,0)")
	private Integer ipCount;

	/** 電話 **/
	@Size(max=15)
	@Column(name="TEL", length=15, columnDefinition="VARCHAR(15)")
	private String tel;

	/** 相同電話數量 **/
	@Digits(integer=4, fraction=0, groups = Check.class)
	@Column(name="TELCOUNT", columnDefinition="DECIMAL(4,0)")
	private Integer telCount;

	/** 是否符合不核可項目5附帶條件 **/
	@Size(max=1)
	@Column(name="ISEATTACHEDPASS", length=1, columnDefinition="CHAR(1)")
	private String isEAttachedPass;

	/** 文件建立者 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(06)")
	private String creator;

	/** 文件建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 資料修改人(行編) **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(06)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;
	
	/** 是否聯徵之債務協商註記 **/
	@Size(max=1)
	@Column(name="ISDEBTAGREEMENT", length=1, columnDefinition="CHAR(1)")
	private String isDebtAgreement;
	
	/** 是否聯徵之信用卡強制停卡註記 **/
	@Size(max=1)
	@Column(name="ISFORCEDSTOPRECORD", length=1, columnDefinition="CHAR(1)")
	private String isForcedStopRecord;
	
	/** 是否其他不良紀錄 **/
	@Size(max=1)
	@Column(name="ISOTHERBADRECORD", length=1, columnDefinition="CHAR(1)")
	private String isOtherBadRecord;
	
	/** 是否有聯徵BAM210逾期催收及呆帳資料 **/
	@Size(max=1)
	@Column(name="ISBAM210DATA", length=1, columnDefinition="CHAR(1)")
	private String isBam210Data;
	
	/** 是否有聯徵BAS001授信異常紀錄 **/
	@Size(max=1)
	@Column(name="ISBAS001RECORD", length=1, columnDefinition="CHAR(1)")
	private String isBas001Record;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得是否有其他財力或資產證明 **/
	public String getIsOtherAssets() {
		return this.isOtherAssets;
	}
	/** 設定是否有其他財力或資產證明 **/
	public void setIsOtherAssets(String value) {
		this.isOtherAssets = value;
	}

	/** 取得申請額度 **/
	public Integer getApplyQuota() {
		return this.applyQuota;
	}
	/** 設定申請額度 **/
	public void setApplyQuota(Integer value) {
		this.applyQuota = value;
	}

	/** 取得申請日期 **/
	public Date getApplyDate() {
		return this.applyDate;
	}
	/** 設定申請日期 **/
	public void setApplyDate(Date value) {
		this.applyDate = value;
	}

	/** 取得投保月數 **/
	public Integer getInsuredMonth() {
		return this.insuredMonth;
	}
	/** 設定投保月數 **/
	public void setInsuredMonth(Integer value) {
		this.insuredMonth = value;
	}

	/** 取得月投保薪資達23800月數 **/
	public Integer getUpTo23800Month() {
		return this.upTo23800Month;
	}
	/** 設定月投保薪資達23800月數 **/
	public void setUpTo23800Month(Integer value) {
		this.upTo23800Month = value;
	}

	/** 取得A1得分 **/
	public Integer getA1Score() {
		return this.a1Score;
	}
	/** 設定A1得分 **/
	public void setA1Score(Integer value) {
		this.a1Score = value;
	}

	/** 取得B1得分 **/
	public Integer getB1Score() {
		return this.b1Score;
	}
	/** 設定B1得分 **/
	public void setB1Score(Integer value) {
		this.b1Score = value;
	}

	/** 取得C1得分 **/
	public Integer getC1Score() {
		return this.c1Score;
	}
	/** 設定C1得分 **/
	public void setC1Score(Integer value) {
		this.c1Score = value;
	}

	/** 取得總分 **/
	public Integer getTotalScore() {
		return this.totalScore;
	}
	/** 設定總分 **/
	public void setTotalScore(Integer value) {
		this.totalScore = value;
	}

	/** 
	 * 取得不核可項目1<p/>
	 * 聯徵之債務協商註記
	 */
	public Integer getNotAllowA() {
		return this.notAllowA;
	}
	/**
	 *  設定不核可項目1<p/>
	 *  聯徵之債務協商註記
	 **/
	public void setNotAllowA(Integer value) {
		this.notAllowA = value;
	}

	/** 
	 * 取得不核可項目2<p/>
	 * 聯徵任一筆貸款最近一期授信繳款狀況≧1
	 */
	public Integer getNotAllowB() {
		return this.notAllowB;
	}
	/**
	 *  設定不核可項目2<p/>
	 *  聯徵任一筆貸款最近一期授信繳款狀況≧1
	 **/
	public void setNotAllowB(Integer value) {
		this.notAllowB = value;
	}

	/** 
	 * 取得不核可項目3<p/>
	 * 拒往紀錄
	 */
	public Integer getNotAllowC() {
		return this.notAllowC;
	}
	/**
	 *  設定不核可項目3<p/>
	 *  拒往紀錄
	 **/
	public void setNotAllowC(Integer value) {
		this.notAllowC = value;
	}

	/** 
	 * 取得不核可項目4<p/>
	 * 退票未註銷達3張
	 */
	public Integer getNotAllowD() {
		return this.notAllowD;
	}
	/**
	 *  設定不核可項目4<p/>
	 *  退票未註銷達3張
	 **/
	public void setNotAllowD(Integer value) {
		this.notAllowD = value;
	}

	/** 
	 * 取得不核可項目5<p/>
	 * 聯徵之信用卡強制停卡註記
	 */
	public Integer getNotAllowE() {
		return this.notAllowE;
	}
	/**
	 *  設定不核可項目5<p/>
	 *  聯徵之信用卡強制停卡註記
	 **/
	public void setNotAllowE(Integer value) {
		this.notAllowE = value;
	}

	/** 取得是否已核可 **/
	public String getIsApproved() {
		return this.isApproved;
	}
	/** 設定是否已核可 **/
	public void setIsApproved(String value) {
		this.isApproved = value;
	}

	/** 
	 * 取得流程註記<p/>
	 * A-授權內,B-授權外,C-一般流程
	 */
	public String getFlowFlag() {
		return this.flowFlag;
	}
	/**
	 *  設定流程註記<p/>
	 *  A-授權內,B-授權外,C-一般流程
	 **/
	public void setFlowFlag(String value) {
		this.flowFlag = value;
	}

	/** 取得是否為薪轉戶 **/
	public String getIsPtaFlag() {
		return this.isPtaFlag;
	}
	/** 設定是否為薪轉戶 **/
	public void setIsPtaFlag(String value) {
		this.isPtaFlag = value;
	}

	/** 取得是否為利害關係人 **/
	public String getIsStakeHolder() {
		return this.isStakeHolder;
	}
	/** 設定是否為利害關係人 **/
	public void setIsStakeHolder(String value) {
		this.isStakeHolder = value;
	}

	/** 取得其它補充註記 **/
	public String getIsOtherAddMark() {
		return this.isOtherAddMark;
	}
	/** 設定其它補充註記 **/
	public void setIsOtherAddMark(String value) {
		this.isOtherAddMark = value;
	}

	/** 取得身分證號更改紀錄 **/
	public String getIsIdChangedMark() {
		return this.isIdChangedMark;
	}
	/** 設定身分證號更改紀錄 **/
	public void setIsIdChangedMark(String value) {
		this.isIdChangedMark = value;
	}

	/** 取得案件通報紀錄 **/
	public String getIsCaseNotifiedMark() {
		return this.isCaseNotifiedMark;
	}
	/** 設定案件通報紀錄 **/
	public void setIsCaseNotifiedMark(String value) {
		this.isCaseNotifiedMark = value;
	}

	/** 取得Ip **/
	public String getIp() {
		return this.Ip;
	}
	/** 設定Ip **/
	public void setIp(String value) {
		this.Ip = value;
	}

	/** 取得相同Ip數量 **/
	public Integer getIpCount() {
		return this.ipCount;
	}
	/** 設定相同Ip數量 **/
	public void setIpCount(Integer value) {
		this.ipCount = value;
	}

	/** 取得電話 **/
	public String getTel() {
		return this.tel;
	}
	/** 設定電話 **/
	public void setTel(String value) {
		this.tel = value;
	}

	/** 取得相同電話數量 **/
	public Integer getTelCount() {
		return this.telCount;
	}
	/** 設定相同電話數量 **/
	public void setTelCount(Integer value) {
		this.telCount = value;
	}

	/** 取得是否符合不核可項目5附帶條件 **/
	public String getIsEAttachedPass() {
		return this.isEAttachedPass;
	}
	/** 設定是否符合不核可項目5附帶條件 **/
	public void setIsEAttachedPass(String value) {
		this.isEAttachedPass = value;
	}

	/** 取得文件建立者 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定文件建立者 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得文件建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定文件建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得資料修改人(行編) **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定資料修改人(行編) **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定資料修改日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	public String getIsDebtAgreement() {
		return isDebtAgreement;
	}
	public void setIsDebtAgreement(String isDebtAgreement) {
		this.isDebtAgreement = isDebtAgreement;
	}
	public String getIsForcedStopRecord() {
		return isForcedStopRecord;
	}
	public void setIsForcedStopRecord(String isForcedStopRecord) {
		this.isForcedStopRecord = isForcedStopRecord;
	}
	public String getIsOtherBadRecord() {
		return isOtherBadRecord;
	}
	public void setIsOtherBadRecord(String isOtherBadRecord) {
		this.isOtherBadRecord = isOtherBadRecord;
	}
	
	public String getIsBam210Data() {
		return isBam210Data;
	}
	public void setIsBam210Data(String isBam210Data) {
		this.isBam210Data = isBam210Data;
	}
	
	public String getIsBas001Record() {
		return isBas001Record;
	}
	public void setIsBas001Record(String isBas001Record) {
		this.isBas001Record = isBas001Record;
	}
}
