package com.mega.eloan.lms.lrs.panels;

import org.apache.commons.lang.StringUtils;
import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.lms.model.L170M01A;

/**
 * <pre>
 * [企金]-覆審報告表  文件資訊
 * </pre>
 */
public class LMS1700S01Panel extends Panel {
	private L170M01A meta;

	public LMS1700S01Panel(String id, boolean updatePanelName, L170M01A meta) {
		super(id, updatePanelName);
		this.meta = meta;
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		model.addAttribute("show_nckdInfo", StringUtils.isNotBlank(meta.getNCkdFlag()));
		new DocLogPanel("_docLog").processPanelData(model, params);
	}
		
	private static final long serialVersionUID = 1L;
}
