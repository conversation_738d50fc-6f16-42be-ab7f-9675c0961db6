/* 
 *ObsdbELF383Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 擔保品額度ELF346
 * </pre>
 * 
 * @since 2012/10/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/30,ICE,new
 *          </ul>
 */
public interface ObsdbELFFORLRSService {

	/** 取得ELF345及ELF346的資料(利用branchId,cntrNo)
	 * @param branchId 銀行代碼
	 * @param custId 身份證字號
	 * @param dupNo 重複序號
	 * @param cntrNo 額度序號
	 */
	List<Map<String, Object>> listAMTDataByCustIdDupNoBranchCntrNo(String branchId, String cntrNo);

	/** 取得ELF348及ELF349的資料(利用custId,dupNo,collNo,branchId)
	 * @param custId 身份證字號
	 * @param dupNo 重複序號
	 * @param collNo 擔保品編號
	 * @param branchId 銀行代碼
	 */
	List<Map<String, Object>> listColDataByCustIdDupNoCollNoBranch(String custId,String dupNo,String collNo,String branchId);

	/** 取得ELF347資料
	 * @param branchId 銀行代碼
	 * @param typCd typCd
	 * @param custId 身份證字號
	 * @param dupNo 重複序號
	 * @param collNo 擔保品編號
	 * @return
	 */
	public List<Map<String, Object>> listELF347Data(
			String branchId,String typCd,String custId, String dupNo , String collNo);
			
	/** 取得ELF349的資料(利用custId,dupNo,collNo,branchId)
	 * @param custId 身份證字號
	 * @param dupNo 重複序號
	 * @param collNo 擔保品編號
	 * @param branchId 銀行代碼
	 */
	List<Map<String, Object>> list349ColDataByCustIdDupNoCollNoBranch(String custId,String dupNo,String collNo,String branchId);

	/** 取得特定幾個欄位在特定TABLE的資料
	 * @param branchId 分行
	 * @param selectField 欄位
	 * @param table TABLE
	 * @param condition 條件
	 * @param orderBy 排序
	 * @return
	 */
	List<Map<String, Object>> listCMSDataByCondition(String branchId,String selectField,String table,String condition,
													 String orderBy,String tCustID, String tDupNo, String tCollno);

}
