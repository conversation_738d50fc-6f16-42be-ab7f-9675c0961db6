package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LmsExcelUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.pages.CLS1220M03Page;
import com.mega.eloan.lms.cls.report.CLS1220R07RptService;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.write.WriteException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * 勞工紓困XLS
 */
@Service("cls1220r07rptservcie")
public class CLS1220R07RptServiceImpl implements FileDownloadService, CLS1220R07RptService {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS1220R07RptServiceImpl.class);

	@Resource
	BranchService branchService;
	
	@Resource
	CLS1220Service cls1220Service;

	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Override
	public byte[] getContent(PageParameters params) throws FileNotFoundException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateXls(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	private ByteArrayOutputStream generateXls(PageParameters params)
	throws IOException, Exception {
		String mode = Util.trim(params.getString("mode"));
		
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		String bailout_2020 = "2020-04-01 00:00:00";
		String bailout_2021 = ClsUtility.get_labor_bailout_2021_since_ts();
		if (Util.equals("1", mode)) { //線上進件2.0客戶在2020-05-22後線上同意查詢聯徵清單
			genXls_mode_1(outputStream);
		} else if (Util.equals("2", mode)) { //產出「臨櫃進件已簽報」與「線上進件2.0之後」清單
			genXls_mode_2(outputStream, bailout_2020);
		} else if (Util.equals("2b", mode)) { //產出2021-06之後「臨櫃進件已簽報」與「線上進件」清單
			genXls_mode_2(outputStream, bailout_2021);
		} else if (Util.equals("3", mode)) { //產出「已撥款未建擔保品」、「已建擔保品尚未撥款」與「已建額度尚未撥款」清單
			genXls_mode_3(outputStream, bailout_2020);
		} else if (Util.equals("3b", mode)) { //產出2021-06之後「已撥款未建擔保品」、「已建擔保品尚未撥款」與「已建額度尚未撥款」清單
			genXls_mode_3(outputStream, bailout_2021);
		}
		
		if (outputStream != null) {
			outputStream.flush();
		}
		return outputStream;
	}

	private void genXls_mode_1(ByteArrayOutputStream outputStream) throws IOException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		if (true) {
			HSSFWorkbook workbook = new HSSFWorkbook();
	        HSSFSheet sheet1 = workbook.createSheet("清單");
			
	     // ======
	        HSSFFont headFont10 = workbook.createFont();
	        {
		        headFont10.setFontName("標楷體");
		        headFont10.setFontHeightInPoints((short)10);
	        }
	        
	        HSSFCellStyle cellFormatL_10 = workbook.createCellStyle();
	        {
		        cellFormatL_10.setFont(headFont10);
		        cellFormatL_10.setAlignment(HorizontalAlignment.LEFT);
		        cellFormatL_10.setWrapText(true);
	        }
	        
	        HSSFFont headFont12 = workbook.createFont();
	        {
		        headFont12.setFontName("標楷體");
		        headFont12.setFontHeightInPoints((short)12);
	        }
	        
	        HSSFCellStyle cellFormatL = workbook.createCellStyle();
	        {
		        cellFormatL.setFont(headFont12);
		        cellFormatL.setAlignment(HorizontalAlignment.LEFT);
		        cellFormatL.setWrapText(true);
	        }
	        
	        HSSFCellStyle cellFormatR = workbook.createCellStyle();
	        {
		        cellFormatR.setFont(headFont12);
		        cellFormatR.setAlignment(HorizontalAlignment.RIGHT);
		        cellFormatR.setWrapText(true);
	        }

	        HSSFCellStyle cellFormatL_Border = workbook.createCellStyle();
	        {
		        cellFormatL_Border.cloneStyleFrom(cellFormatL);
		        cellFormatL_Border.setBorderTop(BorderStyle.THIN);
		        cellFormatL_Border.setBorderBottom(BorderStyle.THIN);
		        cellFormatL_Border.setBorderLeft(BorderStyle.THIN);
		        cellFormatL_Border.setBorderRight(BorderStyle.THIN);
	        }

	        HSSFCellStyle cellFormatR_Border = workbook.createCellStyle();
	        {
		        cellFormatR_Border.cloneStyleFrom(cellFormatR);
		        cellFormatR_Border.setBorderTop(BorderStyle.THIN);
		        cellFormatR_Border.setBorderBottom(BorderStyle.THIN);
		        cellFormatR_Border.setBorderLeft(BorderStyle.THIN);
		        cellFormatR_Border.setBorderRight(BorderStyle.THIN);
	        }

	        HSSFFont headFont14 = workbook.createFont();
	        {
		        headFont14.setFontName("標楷體");
		        headFont14.setFontHeightInPoints((short)14);
	        }
	        HSSFCellStyle cellFormatC_14 = workbook.createCellStyle();
	        {
		        cellFormatC_14.setFont(headFont14);
		        cellFormatC_14.setAlignment(HorizontalAlignment.CENTER);
		        cellFormatC_14.setWrapText(true);
	        }
	        
	     // ======
			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
			headerMap.put("分行代碼", 12);
			headerMap.put("分行別", 25);
			headerMap.put("統編", 20);
			headerMap.put("姓名", 20);
			headerMap.put("線上進件日期", 18);
			headerMap.put("線上同意查聯徵註記", 25);
			headerMap.put("線上同意查聯徵日期", 25);
			headerMap.put("備註", 40);

			int totalColSize = headerMap.size();

			List<String[]> rows = new ArrayList<String[]>();
			
			String brNo = user.getUnitNo();
			Map<String, String> cacheMap = new HashMap<String, String>();
			for (Map<String, Object> map_row : eloandbBASEService.findCLS1220R07_1(brNo)) {				
				// ---
				String[] arr = new String[totalColSize];
				for (int i_col = 0; i_col < totalColSize; i_col++) {
					arr[i_col] = "";
				}
				String ownBrId = Util.trim(MapUtils.getString(map_row, "OWNBRID"));
				arr[0] = ownBrId;// 之後顯示 count
				arr[1] = getBrName(cacheMap, ownBrId);
				arr[2] = Util.trim(MapUtils.getString(map_row, "CUSTID"));
				arr[3] = Util.trim(MapUtils.getString(map_row, "CUSTNAME"));
				arr[4] = Util.trim(TWNDate.toAD((Timestamp)MapUtils.getObject(map_row, "APPLYTS")));
				arr[5] = Util.trim(MapUtils.getString(map_row, "AGREEQUERYEJ"));
				arr[6] = Util.trim(TWNDate.toAD((Timestamp)MapUtils.getObject(map_row, "UPLOADTIME")));
				arr[7] = Util.trim(MapUtils.getString(map_row, "NOTIFYMEMO"));
				// ---
				rows.add(arr);
			}

			//標題列
			int rowIdx = 0;
			
			if(true) {
				int colIdx = 0;
				HSSFRow headerRow = sheet1.createRow(rowIdx);

				for (String h : headerMap.keySet()) {
				    int colWidth = headerMap.get(h);
				    sheet1.setColumnWidth(colIdx, colWidth * 256); 
				    LmsExcelUtil.addCell(headerRow, colIdx, h, cellFormatL_Border);
				    colIdx++;
				}
			}
			
			//資料列
			if(true) {
				rowIdx = 1; // 從下一列開始
				int i_row = 0;
				
				for (String[] arr : rows) {
				    HSSFRow row = sheet1.createRow(rowIdx + i_row);
				    for (int i_col = 0; i_col < totalColSize; i_col++) {
				        LmsExcelUtil.addCell(row, i_col, arr[i_col], cellFormatL_Border);
				    }
				    i_row++;
				}
				
			}
			
	      //---
			workbook.write(outputStream);
			workbook.close();
		}
		
	}
	
	
	private String getBrName(Map<String, String> cacheMap, String brNo){
		if(!cacheMap.containsKey(brNo)){
			IBranch obj = branchService.getBranch(brNo);
			if(obj!=null){
				cacheMap.put(brNo, Util.trim(obj.getBrName()));
			}
		}
		return Util.trim(cacheMap.get(brNo));
	}
	
	private void genXls_mode_2(ByteArrayOutputStream outputStream, String createTimeSince) 
	throws IOException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		if(true) {
			Properties prop_cls1220m03 = MessageBundleScriptCreator.getComponentResource(CLS1220M03Page.class);
		// ======	
			HSSFWorkbook workbook = new HSSFWorkbook();
	        HSSFSheet sheet1 = workbook.createSheet("清單");
	     // ======	   
	        HSSFFont headFont10 = workbook.createFont();
	        {
		        headFont10.setFontName("標楷體");
		        headFont10.setFontHeightInPoints((short)10);
	        }
	        
	        HSSFCellStyle cellFormatL_10 = workbook.createCellStyle();
	        {
		        cellFormatL_10.setFont(headFont10);
		        cellFormatL_10.setAlignment(HorizontalAlignment.LEFT);
		        cellFormatL_10.setWrapText(true);
	        }

	        HSSFFont headFont12 = workbook.createFont();
	        {
		        headFont12.setFontName("標楷體");
		        headFont12.setFontHeightInPoints((short)12);
	        }

	        HSSFCellStyle cellFormatL = workbook.createCellStyle();
	        {
		        cellFormatL.setFont(headFont12);
		        cellFormatL.setAlignment(HorizontalAlignment.LEFT);
		        cellFormatL.setWrapText(true);
	        }

	        HSSFCellStyle cellFormatR = workbook.createCellStyle();
	        {
		        cellFormatR.setFont(headFont12);
		        cellFormatR.setAlignment(HorizontalAlignment.RIGHT);
		        cellFormatR.setWrapText(true);
	        }
	        
	        HSSFCellStyle cellFormatL_Border = workbook.createCellStyle();
	        {
		        cellFormatL_Border.cloneStyleFrom(cellFormatL); // 先複製基本格式
		        cellFormatL_Border.setBorderTop(BorderStyle.THIN);
		        cellFormatL_Border.setBorderBottom(BorderStyle.THIN);
		        cellFormatL_Border.setBorderLeft(BorderStyle.THIN);
		        cellFormatL_Border.setBorderRight(BorderStyle.THIN);
	        }

	        HSSFCellStyle cellFormatR_Border = workbook.createCellStyle();
	        {
		        cellFormatR_Border.cloneStyleFrom(cellFormatR);
		        cellFormatR_Border.setBorderTop(BorderStyle.THIN);
		        cellFormatR_Border.setBorderBottom(BorderStyle.THIN);
		        cellFormatR_Border.setBorderLeft(BorderStyle.THIN);
		        cellFormatR_Border.setBorderRight(BorderStyle.THIN);
	        }
	        
	        HSSFFont headFont14 = workbook.createFont();
	        {
		        headFont14.setFontName("標楷體");
		        headFont14.setFontHeightInPoints((short)14);
	        }

	        HSSFCellStyle cellFormatC_14 = workbook.createCellStyle();
	        {
		        cellFormatC_14.setFont(headFont14);
		        cellFormatC_14.setAlignment(HorizontalAlignment.CENTER);
		        cellFormatC_14.setWrapText(true);
	        }
	        
	     // ======	
			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
			headerMap.put("分行代碼", 12);
			headerMap.put("分行別", 25);
			headerMap.put("統編", 20);
			headerMap.put("姓名", 20);
			headerMap.put("進件種類", 36);
			headerMap.put("線上進件日期", 18);
			headerMap.put("線上同意查聯徵註記", 25);
			headerMap.put("申貸狀態", 25);
			headerMap.put("手機", 25);
			headerMap.put("備註", 40);
			headerMap.put("E-mail", 40);
			headerMap.put("貸款利息通知方式", 20);
			
			int totalColSize = headerMap.size();
		
			List<String[]> rows = new ArrayList<String[]>();
			
			String brNo = user.getUnitNo();
			Map<String, String> cacheMap = new HashMap<String, String>();
			Map<String, String> applyKindMap = new HashMap<String, String>();
			applyKindMap.put("B", "「線上進件」案件");
			applyKindMap.put("D", "非屬「線上進件」案件");
			
			Map<String, String> statFlagMap = cls1220Service.get_ApplyKindB_statFlagDescMap();
			
			Map<String, String> notifyWayMap = new HashMap<String, String>();
			notifyWayMap.put("1", prop_cls1220m03.getProperty("C122M01A.notifyWay.1")); //郵寄
			notifyWayMap.put("2", prop_cls1220m03.getProperty("C122M01A.notifyWay.2")); //e-mail
			notifyWayMap.put("3", prop_cls1220m03.getProperty("C122M01A.notifyWay.3")); //不通知
			
			for (Map<String, Object> map_row : eloandbBASEService.findCLS1220R07_2(brNo, createTimeSince)) {				
				// ---
				String[] arr = new String[totalColSize];
				for (int i_col = 0; i_col < totalColSize; i_col++) {
					arr[i_col] = "";
				}
				String ownBrId = Util.trim(MapUtils.getString(map_row, "OWNBRID"));
				arr[0] = ownBrId;// 之後顯示 count
				arr[1] = getBrName(cacheMap, ownBrId);
				arr[2] = Util.trim(MapUtils.getString(map_row, "CUSTID"));
				arr[3] = Util.trim(MapUtils.getString(map_row, "CUSTNAME"));
				arr[4] = LMSUtil.getDesc(applyKindMap, Util.trim(MapUtils.getString(map_row, "APPLYKIND")));
				arr[5] = Util.trim(TWNDate.toAD((Timestamp)MapUtils.getObject(map_row, "APPLYTS")));
				arr[6] = Util.trim(MapUtils.getString(map_row, "AGREEQUERYEJ"));
				arr[7] = LMSUtil.getDesc(statFlagMap, Util.trim(MapUtils.getString(map_row, "STATFLAG")));
				arr[8] = Util.trim(MapUtils.getString(map_row, "MTEL"));
				arr[9] = Util.trim(MapUtils.getString(map_row, "NOTIFYMEMO"));
				arr[10] = Util.trim(MapUtils.getString(map_row, "EMAIL"));
				arr[11] = LMSUtil.getDesc(notifyWayMap, Util.trim(MapUtils.getString(map_row, "NOTIFYWAY")));
				// ---
				rows.add(arr);
			}
		
			int rowIdx = 0;
			// ==============================		
			//標題列
			if(true){
				HSSFRow headerRow = sheet1.createRow(rowIdx);
				int colIdx = 0;
				for (String h : headerMap.keySet()) {
				    int colWidth = headerMap.get(h);
				    sheet1.setColumnWidth(colIdx, colWidth * 256); // 記得乘 256
				    LmsExcelUtil.addCell(headerRow, colIdx, h, cellFormatL_Border);
				    colIdx++;
				}
			}
			// ==============================
			if(true){
				rowIdx = 1;
				for (int i_row = 0; i_row < rows.size(); i_row++) {
				    HSSFRow row = sheet1.createRow(rowIdx + i_row);
				    String[] arr = rows.get(i_row);
				    
				    for (int i_col = 0; i_col < totalColSize; i_col++) {
				        LmsExcelUtil.addCell(row, i_col, arr[i_col], cellFormatL_Border);
				    }
				}
			}
	        	        
			// ---
			workbook.write(outputStream);
			workbook.close();
		}	
	}	

	/**
	 * 產出「已撥款未建擔保品」、「已建擔保品尚未撥款」與「已建額度尚未撥款」清單
	 */
	private void genXls_mode_3(ByteArrayOutputStream outputStream, String createTimeSince) 
	throws IOException{
		HSSFWorkbook workbook = new HSSFWorkbook();
		
		CreatSubSheet1(workbook ,"已撥款未建擔保品" , 0, createTimeSince);
		CreatSubSheet2(workbook ,"已建擔保品尚未撥款" , 1, createTimeSince);
		CreatSubSheet3(workbook ,"已建額度尚未撥款" , 2, createTimeSince);
		
		workbook.write(outputStream);
		workbook.close();
	}
	
	/**
	 * 已撥款未建擔保品
	 * @param workbook
	 * @param sheetName
	 * @param indexSheet
	 * @throws IOException
	 * @throws WriteException
	 */
	private void CreatSubSheet1(HSSFWorkbook workbook, String sheetName , int indexSheet, String createTimeSince) throws IOException{
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		HSSFSheet sheet1 = workbook.createSheet(sheetName);
		workbook.setSheetOrder(sheetName, indexSheet);//設定頁籤順序
		
		// ======
		HSSFFont headFont10 = workbook.createFont();
		{
			headFont10.setFontName("標楷體");
			headFont10.setFontHeightInPoints((short)10);
		}
		
		HSSFCellStyle cellFormatL_10 = workbook.createCellStyle();
		{
			cellFormatL_10.setFont(headFont10);
			cellFormatL_10.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL_10.setWrapText(true);
		}
		
		HSSFFont headFont12 = workbook.createFont();
		{
			headFont12.setFontName("標楷體");
			headFont12.setFontHeightInPoints((short)12);
		}
		
		HSSFCellStyle cellFormatL = workbook.createCellStyle();
		{
			cellFormatL.setFont(headFont12);
			cellFormatL.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL.setWrapText(true);
		}

		HSSFCellStyle cellFormatR = workbook.createCellStyle();
		{
			cellFormatR.setFont(headFont12);
			cellFormatR.setAlignment(HorizontalAlignment.RIGHT);
			cellFormatR.setWrapText(true);
		}

		HSSFCellStyle cellFormatL_Border = workbook.createCellStyle();
		{
			cellFormatL_Border.setFont(headFont12);
			cellFormatL_Border.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL_Border.setWrapText(true);
			cellFormatL_Border.setBorderTop(BorderStyle.THIN);
			cellFormatL_Border.setBorderBottom(BorderStyle.THIN);
			cellFormatL_Border.setBorderLeft(BorderStyle.THIN);
			cellFormatL_Border.setBorderRight(BorderStyle.THIN);
		}

		HSSFCellStyle cellFormatR_Border = workbook.createCellStyle();
		{
			cellFormatR_Border.setFont(headFont12);
			cellFormatR_Border.setAlignment(HorizontalAlignment.RIGHT);
			cellFormatR_Border.setWrapText(true);
			cellFormatR_Border.setBorderTop(BorderStyle.THIN);
			cellFormatR_Border.setBorderBottom(BorderStyle.THIN);
			cellFormatR_Border.setBorderLeft(BorderStyle.THIN);
			cellFormatR_Border.setBorderRight(BorderStyle.THIN);
		}

		HSSFFont headFont14 = workbook.createFont();
		{
			headFont14.setFontName("標楷體");
			headFont14.setFontHeightInPoints((short)14);
		}
		
		HSSFCellStyle cellFormatC_14 = workbook.createCellStyle();
		{
			cellFormatC_14.setFont(headFont14);
			cellFormatC_14.setAlignment(HorizontalAlignment.CENTER);
			cellFormatC_14.setWrapText(true);
		}
		// ======	
		Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
		headerMap.put("分行代碼", 12);
		headerMap.put("分行別", 25);
		headerMap.put("統編", 20);
		headerMap.put("姓名", 20);
		headerMap.put("額度序號", 20);
		headerMap.put("核准額度", 20);
		headerMap.put("已用額度", 20);
		headerMap.put("動用起日", 20);
		headerMap.put("動用迄日", 20);
	
		int totalColSize = headerMap.size();
	
		List<String[]> rows = new ArrayList<String[]>();
		
		String brNo = user.getUnitNo();
		Map<String, String> cacheMap = new HashMap<String, String>();	
				
		for (Map<String, Object> map_row : misdbBASEService.findCLS1220R07_3_1(brNo, createTimeSince)) {				
			// ---
			String[] arr = new String[totalColSize];
			for (int i_col = 0; i_col < totalColSize; i_col++) {
				arr[i_col] = "";
			}
			String ownBrId = Util.trim(MapUtils.getString(map_row, "LNF020_LN_BR_NO"));
			arr[0] = ownBrId;// 之後顯示 count
			arr[1] = getBrName(cacheMap, ownBrId);
			arr[2] = Util.trim(MapUtils.getString(map_row, "LNF020_CUST_ID"));
			arr[3] = Util.trim(MapUtils.getString(map_row, "CNAME"));
			arr[4] = Util.trim(MapUtils.getString(map_row, "LNF020_CONTRACT"));
			arr[5] = Util.trim(MapUtils.getString(map_row, "LNF020_FACT_AMT"));
			arr[6] = Util.trim(MapUtils.getString(map_row, "LNF020_USED_AMT"));
			arr[7] = Util.trim(TWNDate.toAD(CapDate.getDate(MapUtils.getString(map_row, "LNF020_BEG_DATE"), "yyyy-MM-dd")));
			arr[8] = Util.trim(TWNDate.toAD(CapDate.getDate(MapUtils.getString(map_row, "LNF020_END_DATE"), "yyyy-MM-dd")));
			// ---
			rows.add(arr);
		}
	
		int rowIdx = 0;
		// ==============================
		//標題列
		HSSFRow headerRow = sheet1.createRow(rowIdx);
		int colIdx = 0;
		for (String h : headerMap.keySet()) {
		    int colWidth = headerMap.get(h);
		    sheet1.setColumnWidth(colIdx, colWidth * 256); 
		    LmsExcelUtil.addCell(headerRow, colIdx, h, cellFormatL_Border); 
		    colIdx++;
		}
		// ==============================
		//資料列
		rowIdx = 1;
		for (int i_row = 0; i_row < rows.size(); i_row++) {
		    HSSFRow row = sheet1.createRow(rowIdx + i_row);
		    String[] arr = rows.get(i_row);
		    for (int i_col = 0; i_col < totalColSize; i_col++) {
		        LmsExcelUtil.addCell(row, i_col, arr[i_col], cellFormatL_Border); 
		    }
		}
		
	}
	
	private void CreatSubSheet2(HSSFWorkbook workbook, String sheetName , int indexSheet, String createTimeSince) throws IOException{
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		//Properties prop_cls1220m03 = MessageBundleScriptCreator.getComponentResource(CLS1220M03Page.class);
		
		HSSFSheet sheet1 = workbook.createSheet(sheetName);
		workbook.setSheetOrder(sheetName, indexSheet);//設定頁籤順序
		
		// ======
		HSSFFont headFont10 = workbook.createFont();
		{
			headFont10.setFontName("標楷體");
			headFont10.setFontHeightInPoints((short) 10);
		}
		
		HSSFCellStyle cellFormatL_10 = workbook.createCellStyle();
		{
			cellFormatL_10.setFont(headFont10);
			cellFormatL_10.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL_10.setWrapText(true);
		}

		HSSFFont headFont12 = workbook.createFont();
		{
			headFont12.setFontName("標楷體");
			headFont12.setFontHeightInPoints((short) 12);
		}
		
		HSSFCellStyle cellFormatL = workbook.createCellStyle();
		{
			cellFormatL.setFont(headFont12);
			cellFormatL.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL.setWrapText(true);
		}

		HSSFCellStyle cellFormatR = workbook.createCellStyle();
		{
			cellFormatR.setFont(headFont12);
			cellFormatR.setAlignment(HorizontalAlignment.RIGHT);
			cellFormatR.setWrapText(true);
		}

		HSSFCellStyle cellFormatL_Border = workbook.createCellStyle();
		{
			cellFormatL_Border.setFont(headFont12);
			cellFormatL_Border.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL_Border.setWrapText(true);
			cellFormatL_Border.setBorderTop(BorderStyle.THIN);
			cellFormatL_Border.setBorderBottom(BorderStyle.THIN);
			cellFormatL_Border.setBorderLeft(BorderStyle.THIN);
			cellFormatL_Border.setBorderRight(BorderStyle.THIN);
		}

		HSSFCellStyle cellFormatR_Border = workbook.createCellStyle();
		{
			cellFormatR_Border.setFont(headFont12);
			cellFormatR_Border.setAlignment(HorizontalAlignment.RIGHT);
			cellFormatR_Border.setWrapText(true);
			cellFormatR_Border.setBorderTop(BorderStyle.THIN);
			cellFormatR_Border.setBorderBottom(BorderStyle.THIN);
			cellFormatR_Border.setBorderLeft(BorderStyle.THIN);
			cellFormatR_Border.setBorderRight(BorderStyle.THIN);
		}

		HSSFFont headFont14 = workbook.createFont();
		{
			headFont14.setFontName("標楷體");
			headFont14.setFontHeightInPoints((short) 14);
		}
		
		HSSFCellStyle cellFormatC_14 = workbook.createCellStyle();
		{
			cellFormatC_14.setFont(headFont14);
			cellFormatC_14.setAlignment(HorizontalAlignment.CENTER);
			cellFormatC_14.setWrapText(true);
		}
		
		// ======	
		Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
		headerMap.put("分行代碼", 12);
		headerMap.put("分行別", 25);
		headerMap.put("統編", 20);
		headerMap.put("姓名", 20);
		headerMap.put("擔保品編號", 20);
		headerMap.put("額度序號", 20);
		headerMap.put("保證項目", 20);
		headerMap.put("保證案號", 20);
	
		int totalColSize = headerMap.size();
	
		List<String[]> rows = new ArrayList<String[]>();
		
		String brNo = user.getUnitNo();
		Map<String, String> cacheMap = new HashMap<String, String>();	
				
		for (Map<String, Object> map_row : misdbBASEService.findCLS1220R07_3_2(brNo, createTimeSince)) {				
			// ---
			String[] arr = new String[totalColSize];
			for (int i_col = 0; i_col < totalColSize; i_col++) {
				arr[i_col] = "";
			}
			String ownBrId = Util.trim(MapUtils.getString(map_row, "BRANCH"));
			arr[0] = ownBrId;// 之後顯示 count
			arr[1] = getBrName(cacheMap, ownBrId);
			arr[2] = Util.trim(MapUtils.getString(map_row, "CUSTID"));
			arr[3] = Util.trim(MapUtils.getString(map_row, "CUSTNAME"));
			arr[4] = Util.trim(MapUtils.getString(map_row, "COLLNO"));
			arr[5] = Util.trim(MapUtils.getString(map_row, "CNTRNO"));
			arr[6] = Util.trim(MapUtils.getString(map_row, "GRTITEM"));
			arr[7] = Util.trim(MapUtils.getString(map_row, "GRTNO"));
			// ---
			rows.add(arr);
		}
	
		int rowIdx = 0;
		// ==============================
		//標題列
		int colIdx = 0;
		HSSFRow headerRow = sheet1.createRow(rowIdx);
		for (String h : headerMap.keySet()) {
		    int colWidth = headerMap.get(h);
		    sheet1.setColumnWidth(colIdx, colWidth * 256); 
		    LmsExcelUtil.addCell(headerRow, colIdx, h, cellFormatL_Border); 
		    colIdx++;
		}
		// ==============================
		//資料列
		rowIdx = 1;
		for (int i_row = 0; i_row < rows.size(); i_row++) {
		    HSSFRow row = sheet1.createRow(rowIdx + i_row);
		    String[] arr = rows.get(i_row);
		    for (int i_col = 0; i_col < totalColSize; i_col++) {
		        LmsExcelUtil.addCell(row, i_col, arr[i_col], cellFormatL_Border);
		    }
		}
			
	}
	
	/**
	 * 已建額度尚未撥款
	 * @param workbook
	 * @param sheetName
	 * @param indexSheet
	 * @throws IOException
	 * @throws WriteException
	 */
	private void CreatSubSheet3(HSSFWorkbook workbook, String sheetName , int indexSheet, String createTimeSince) throws IOException{
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		//Properties prop_cls1220m03 = MessageBundleScriptCreator.getComponentResource(CLS1220M03Page.class);
		
		HSSFSheet sheet1 = workbook.createSheet(sheetName);
		workbook.setSheetOrder(sheetName, indexSheet);//設定頁籤順序
		// ======
		HSSFFont headFont10 = workbook.createFont();
		{
			headFont10.setFontName("標楷體");
			headFont10.setFontHeightInPoints((short) 10);
		}
		
		HSSFCellStyle cellFormatL_10 = workbook.createCellStyle();
		{
			cellFormatL_10.setFont(headFont10);
			cellFormatL_10.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL_10.setWrapText(true);
		}
		
		HSSFFont headFont12 = workbook.createFont();
		{
			headFont12.setFontName("標楷體");
			headFont12.setFontHeightInPoints((short) 12);
		}

		HSSFCellStyle cellFormatL = workbook.createCellStyle();
		{
			cellFormatL.setFont(headFont12);
			cellFormatL.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL.setWrapText(true);
		}

		HSSFCellStyle cellFormatR = workbook.createCellStyle();
		{
			cellFormatR.setFont(headFont12);
			cellFormatR.setAlignment(HorizontalAlignment.RIGHT);
			cellFormatR.setWrapText(true);
		}

		HSSFCellStyle cellFormatL_Border = workbook.createCellStyle();
		{
			cellFormatL_Border.setFont(headFont12);
			cellFormatL_Border.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL_Border.setWrapText(true);
			cellFormatL_Border.setBorderTop(BorderStyle.THIN);
			cellFormatL_Border.setBorderBottom(BorderStyle.THIN);
			cellFormatL_Border.setBorderLeft(BorderStyle.THIN);
			cellFormatL_Border.setBorderRight(BorderStyle.THIN);
		}

		HSSFCellStyle cellFormatR_Border = workbook.createCellStyle();
		{
			cellFormatR_Border.setFont(headFont12);
			cellFormatR_Border.setAlignment(HorizontalAlignment.RIGHT);
			cellFormatR_Border.setWrapText(true);
			cellFormatR_Border.setBorderTop(BorderStyle.THIN);
			cellFormatR_Border.setBorderBottom(BorderStyle.THIN);
			cellFormatR_Border.setBorderLeft(BorderStyle.THIN);
			cellFormatR_Border.setBorderRight(BorderStyle.THIN);
		}

		HSSFFont headFont14 = workbook.createFont();
		{
			headFont14.setFontName("標楷體");
			headFont14.setFontHeightInPoints((short) 14);
		}

		HSSFCellStyle cellFormatC_14 = workbook.createCellStyle();
		{
			cellFormatC_14.setFont(headFont14);
			cellFormatC_14.setAlignment(HorizontalAlignment.CENTER);
			cellFormatC_14.setWrapText(true);
		}
		
		// ======
		Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
		headerMap.put("分行代碼", 12);
		headerMap.put("分行別", 25);
		headerMap.put("統編", 20);
		headerMap.put("姓名", 20);
		headerMap.put("額度序號", 20);
		headerMap.put("核准額度", 20);
		headerMap.put("已用額度", 20);
		headerMap.put("動用起日", 20);
		headerMap.put("動用迄日", 20);
	
		int totalColSize = headerMap.size();
	
		List<String[]> rows = new ArrayList<String[]>();
		
		String brNo = user.getUnitNo();
		Map<String, String> cacheMap = new HashMap<String, String>();	
				
		for (Map<String, Object> map_row : misdbBASEService.findCLS1220R07_3_3(brNo, createTimeSince)) {				
			// ---
			String[] arr = new String[totalColSize];
			for (int i_col = 0; i_col < totalColSize; i_col++) {
				arr[i_col] = "";
			}
			String ownBrId = Util.trim(MapUtils.getString(map_row, "LNF020_LN_BR_NO"));
			arr[0] = ownBrId;// 之後顯示 count
			arr[1] = getBrName(cacheMap, ownBrId);
			arr[2] = Util.trim(MapUtils.getString(map_row, "LNF020_CUST_ID"));
			arr[3] = Util.trim(MapUtils.getString(map_row, "CNAME"));
			arr[4] = Util.trim(MapUtils.getString(map_row, "LNF020_CONTRACT"));
			arr[5] = Util.trim(MapUtils.getString(map_row, "LNF020_FACT_AMT"));
			arr[6] = Util.trim(MapUtils.getString(map_row, "LNF020_USED_AMT"));
			arr[7] = Util.trim(TWNDate.toAD(CapDate.getDate(MapUtils.getString(map_row, "LNF020_BEG_DATE"), "yyyy-MM-dd")));
			arr[8] = Util.trim(TWNDate.toAD(CapDate.getDate(MapUtils.getString(map_row, "LNF020_END_DATE"), "yyyy-MM-dd")));
			// ---
			rows.add(arr);
		}
	
		int rowIdx = 0;
		// ==============================
		//標題列
		HSSFRow headerRow = sheet1.createRow(rowIdx);
		int colIdx = 0;
		for (String h : headerMap.keySet()) {
		    int colWidth = headerMap.get(h);
		    sheet1.setColumnWidth(colIdx, colWidth * 256); 
		    LmsExcelUtil.addCell(headerRow, colIdx, h, cellFormatL_Border); 
		    colIdx++;
		}
		// ==============================
		//資料列
		rowIdx = 1;
		for (int i_row = 0; i_row < rows.size(); i_row++) {
		    HSSFRow row = sheet1.createRow(rowIdx + i_row);
		    String[] arr = rows.get(i_row);
		    for (int i_col = 0; i_col < totalColSize; i_col++) {
		        LmsExcelUtil.addCell(row, i_col, arr[i_col], cellFormatL_Border); 
		    }
		}
		
	}

}
