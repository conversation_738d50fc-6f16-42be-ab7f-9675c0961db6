var dfd41 = new $.Deferred(), dfd42 = new $.Deferred(), dfd43 = new $.Deferred(), dfd44 = new $.Deferred();
if (!window.RateAction) {
    window.RateAction = {
        isLoad: false,
        showSecondLimitRate:false,
        init: function(){
            if (!this.isLoad) {
                //一次取得所有的下拉選單值
                $.ajax({
                    async: false,
                    handler: inits.fhandle,
                    action: "getInitData",
                    data: {},
                    success: function(obj){
                        RateAction.isLoad = true;
                        RateAction.baseSelect = obj.baseSelect;
                        RateAction.baseSelectHis = obj.baseSelectHis;
                        RateAction.prRateSelect = obj.prRateSelect;
                        RateAction.marketRateSelect = obj.marketRateSelect;
                        RateAction.showSecondLimitRate = obj.showSecondLimitRate;
                    }
                });
                //變更利率選擇的顏色
                $("[name=tempRateBass]").on("click", function(){
                    var $this = $(this);
                    if ($this.prop("checked")) {
                        $this.closest("td").css("background", "#C0C0C0");
                    }
                    else {
                        $this.closest("td").css("background", "#FFFFFF");
                    }
                });
                //自訂利率產生
                var item = API.loadCombos("lms1401s0204_ratePeriod")["lms1401s0204_ratePeriod"];
                $("#ratePeriod").setItems({
                    size: "6",
                    item: item,
                    fn: function(v, k){
                        var value = $(this).val()
                        //借款同天期和無
                        if (value == "B001" || value == "B000") {
                            var $target = (value == "B001") ? $("[name=ratePeriod][value!=B001]") : $("[name=ratePeriod][value!=B000]")
                            if (this.checked) {
                                $target.removeAttr("checked").prop("disabled", true);
                            }
                            else {
                                $target.removeAttr("disabled");
                            }
                        }
                    }
                })
                
            }
        },
        //儲存基礎利率下拉選單值
        baseSelect: {},
        //儲存自訂利率下拉選單值
        prRateSelect: {},
        //儲存美金市場利率下拉選單值
        marketRateSelect: {},
        //J-111-0024_05097_B1001 Web e-Loan國內企金額度明細表之利/費率欄位中，新增EURIBOR得以借款同天期顯示文字之欄位。
        //
        GroupName: ["ＥＵＲＩＢＯＲ","ＣＮＨ　ＨＩＢＯＲ","ＳＩＢＯＲ", "ＬＩＢＯＲ", "ＴＡＩＢＯＲ", "ＴＡＩＦＸ　ＯＦＦＥＲ", "ＴＡＩＦＸ", "ＥＵＲＯＹＥＮ　ＴＩＢＯＲ", "ＴＩＢＯＲ", "ＳＨＩＢＯＲ", "ＨＩＢＯＲ", "路透（股）公司６１６５頁ＣＰ", "路透（股）公司５１３２８頁", "ＨＩＢＯＲ","ＴＡＩＢＩＲ０１初級","ＴＡＩＢＩＲ０２次級","定存機動利率大額","ＴＥＲＭ　ＳＯＦＲ","ＳＯＦＲ","ＥＳＴＲ","ＴＯＮＡ"],
        /**
         * 檢查是否為群組
         * @param {String } value 傳入字串
         * return 空白 或 ["ＳＩＢＯＲ", "ＬＩＢＯＲ", "ＴＡＩＢＯＲ", "ＴＡＩＦＸ", "ＴＩＢＯＲ"]
         */
        checkGroupName: function(value){
            for (var key in RateAction.GroupName) {
                var checkData = RateAction.GroupName[key];
                if (value.indexOf(checkData) > -1) {
                    return checkData;
                }
            }
            return "";
        },
        
        RateMainFormId: "L140M01FForm",
        CurrFormId: "L140M01NForm",
        RateFormId: "L140M01HForm",
        titleName: "",
        openType: "",
        txtPrimeRatePlan3: "",
        setOpenType: function(value){
            this.openType = value;
            $("#" + this.CurrFormId).find("#rateType").val(value);
        },
        //利率主檔L140M01F oid 
        rateId: "",
        //利率結構化檔 L140M01N oid
        rateCurrId: "",
        rateSeq: "",
        setRateSeq: function(value){
            this.rateSeq = value;
            $("#" + this.CurrFormId).find("#rateSeq").val(value);
        },
        /**
         * 儲存並同時組成文字
         */
        todoSaveRate: function(){
        
        },
        /**
         * 清除幣別欄位組字
         */
        cleanRateWord: function(){
            var $form = $("#" + this.CurrFormId);
            $form.reset();
            //J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率
            //J-110-0399_05097_B1001 Web eloan額度明細表登錄新台幣利率條件增加「適用新台幣七百億優惠利率檢核」及「適用新台幣競爭性利率檢核」檢核功能
            $form.find("#rateBaseOpTb,#rateTaxSpan,#selfOpTb,#forRateUSD,#forRateTWD,#secMonSpan,#secDateSpan,#rateChgKindTr,#rateLimitTypeTable,#rateLimitCountPrSpan,#rateLimitCodeGroup,#rateBaseGroupTb,#rateLimitTypeTable2,#rateLimitCountPrSpan2,#rateLimitCodeGroup2").hide();
            $form.find("#showRateText").html("")
            
        },
        /**
         * 重新引進利率
         */
        reoladtRate: function(){
            this.setMaxAndMinRate($("#rateBase").val().split("|"));
        },
        /**
         * 更新利率grid
         */
        reloadGird: function(){
            $("#gridviewRate").trigger("reloadGrid");
        },
		/**
         * 更新查詢利率grid
         */
        reloadQueryRateBaseGird: function(){
			var curr = RateAction.getNowTypeCurr();
            $("#gridviewQueryRateBase").jqGrid("setGridParam", {
                postData: {
                    formAction: "queryMisLnratByCurr",
                    tabFormMainId: $("#tabFormMainId").val(),
                    curr:curr
                }
            }).trigger("reloadGrid");
        },
		/**
         * 更新All-in掛牌利率grid
         */
        reloadAllInSelRateBaseGird: function(){
			var curr = RateAction.getNowTypeCurr();
            $("#gridviewQueryRateBaseAllIn").jqGrid("setGridParam", {
                postData: {
                    formAction: "queryMisLnratByCurr",
                    tabFormMainId: $("#tabFormMainId").val(),
                    curr:curr
                }
            }).trigger("reloadGrid");
        },
        /**
         *更新幣別grid
         */
        resetRateGrid: function(){
            $("#gridviewRate02").jqGrid("setGridParam", {
                sortname: 'rateType,secNo,secNoOp,createTime,rateDscr',
                sortorder: 'asc,asc,asc,asc,asc',
                postData: {
                    formAction: "queryL140m01n",
                    tabFormMainId: $("#tabFormMainId").val(),
                    rateSeq: this.rateSeq || 0
                },
                page: 1,
                search: true
            }).trigger("reloadGrid");
        },
		
        /**
         *輸入修改利率thickBox
         * @param {Object} type  要修改的欄位種類 max|min
         */
        enterRateBox: function(type){
            var $form = $("#" + RateAction.CurrFormId);
            
            var srcValue = "";
            if ("max" == type) {
                srcValue = $form.find("#tRateMax").val();
            }
            else {
                srcValue = $form.find("#tRateMin").val();
            }
            var $inputform = $("#enterRateForm");
            $inputform.reset();
            $inputform.find("#enterRateInupt,#enterRateInuptNow").val(srcValue);
            $("#enterRateBox").thickbox({
                //l1401s02p04.051=修改欄位值
                title: i18n.lms1401s0204["l1401s02p04.051"],
                width: 330,
                height: 180,
                modal: true,
                align: "center",
                valign: "bottom",
                readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        if (!$inputform.valid()) {
                            return false;
                        }
                        var targetValue = parseFloat($.trim($inputform.find("#enterRateInupt").val())).toFixed(5);
                        if ("max" == type) {
                            $form.find("#ctRateMax").val(targetValue);
                        }
                        else {
                            $form.find("#ctRateMin").val(targetValue);
                        }
                        RateAction.calcRate();
                        $.thickbox.close();
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        },
        /**
         * 檢查授信科目是否輸入
         * return true | false
         */
        checkItem: function(){
            if (!RateAction.getSelectItem()) {
                //L140M01a.error07=請選擇 L782M01A.loanTP=科目
                API.showMessage(i18n.lms1401s02["L140M01a.error07"] + i18n.lms1401s02["L782M01A.loanTP"]);
                return false;
            }
            return true;
        },
        /**
         * 取得選擇的授信科目
         * return XXX|XXX
         */
        getSelectItem: function(){
            var data = [];
            $("#itemSpan_loanTPList").find("[name=loanTPList]:checked").each(function(v, k){
                data.push($(k).val());
            });
            return data.join("|");
        },
        /**
         * 修改利費率說明敘述
         */
        setPage2RateDrc: function(str){
            $("#itemDscr2").val(str);
        },
        /**
         * 開啟利率主檔 L140M01F
         * @param {Object} bb
         * @param {Object} oid
         * @param {Object} data
         */
        newRate: function(bb, oid, data){
            //下拉選單
            dfd42.resolve();
            $("#" + RateAction.RateMainFormId).reset();
            $("#" + RateAction.CurrFormId).reset();
            RateAction.setRateSeq(data.rateSeq);
            RateAction.resetRateGrid();
            //根據第二頁籤的現請額度幣別判斷出現非美元、新台幣、日幣、歐元只出現雜幣
            var $allButton = $("#twBT,#euBT,#usBT,#jpBT,#auBT,#hkBT,#cnBT,#otherBT");
            if ($("#otherCurr").val() == "") {
                $allButton.parent().parent().hide();
                switch ($("#currentApplyCurr").val()) {
                    case 'TWD':
                        $("#twBT").parent().parent().show();
                        break;
                    case 'USD':
                        $("#usBT").parent().parent().show();
                        break;
                    case 'JPY':
                        $("#jpBT").parent().parent().show();
                        break;
                    case 'EUR':
                        $("#euBT").parent().parent().show();
                        break;
                    case 'AUD':
                        $("#auBT").parent().parent().show();
                        break;
                    case 'HKD':
                        $("#hkBT").parent().parent().show();
                        break;
                    case 'CNY':
                    case 'RMB':
                        $("#cnBT").parent().parent().show();
                        break;
                    default:
                        $("#otherBT").parent().parent().show();
                        break;
                }
            } else if ($("#otherCurr").val() == "4") {
                $allButton.parent().parent().hide();
                $("#twBT").parent().parent().show();
            }
            else {
                $allButton.parent().parent().show();
            }
            $("#rateBT").parent().parent().show();
            if (inits.toreadOnly) {
                $allButton.parent().parent().hide();
            }
            $.ajax({
                handler: inits.fhandle,
                action: "queryL140m01f",
                data: {
                    tabFormMainId: $("#tabFormMainId").val(),
                    oid: data.oid || "",
                    noOpenDoc: true
                },
                success: function(jsonObj){
                    if (data.oid) {
                        var list = jsonObj.loanTPListAll.split("|");
                        $("[name=loanTPList]").val(list);
                        $("#RateDrc").val(jsonObj.RateDrc);
                    }
                    RateAction.rateId = jsonObj.rateId;
                    $("input[name=loanTPList]").removeAttr("disabled");
                    
                    RateAction.openNewRateBox();
                }//close success function
            });//close ajax 
        },
        /**
         * 利率主檔 L140M01F thickbox
         * @param {Object} bb
         * @param {Object} oid
         * @param {Object} data
         */
        openNewRateBox: function(){
            $("#newRateBox").thickbox({
                //title.16=登錄利(費)率
                title: i18n.lms1401s02["title.16"],
                width: 900,
                height: 500,
                modal: true,
                readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                i18n: i18n.def,
                open: function(){
                    $("[name=loanTPList]").readOnly(_openerLockDoc == "1" || inits.toreadOnly);
                },
                buttons: {
                    "saveData": function(){
                        //檢查是否已經選擇科目
                        if (!RateAction.checkItem()) {
                            return false;
                        }
                        FormAction.open = true;
                        $.ajax({
                            handler: inits.fhandle,
                            action: "saveL140m01f",
                            formId: RateAction.RateMainFormId,
                            data: {
                                tabFormMainId: $("#tabFormMainId").val(),
                                rateSeq: RateAction.rateSeq || 0,
                                loanTPListAll: RateAction.getSelectItem()
                            },
                            success: function(obj){
                                FormAction.open = false;
                                if (obj && obj.drc) {
                                    RateAction.setPage2RateDrc(obj.drc);
                                }
                                RateAction.reloadGird();
                                $.thickbox.close();
                            }
                        });
                    },
                    "close": function(){
                        $.thickbox.close();
                        RateAction.reloadGird();
                    }
                }
            });
        },
        
        /**
         * 打開幣別利率的box
         * 當data 有值表示為gird 開啟 ，幣別的種類要從data抓
         * @param {Object} type 幣別種類 1新台幣、2美金、3日幣、4歐元、5人民幣、6澳幣、7港幣、Z雜幣
         * @param {Object} type2
         * @param {Object} data grid上的資料
         */
        openRateBox: function(type, type2, data){
            var typCd = $("#L140M01AForm1").find("#typCd").html();
			var cntrNo = $("#L140M01AForm2").find("#cntrNo").val();
			if(cntrNo != "" && cntrNo != 'undefined'){
				if (type == "1" && cntrNo.substring(3, 4) == "4") {
	                //L140M01a.error42=OBU額度明細表不得申請台幣額度！
	                API.showMessage(i18n.lms1401s02['L140M01a.error42']);
	                return false;
            	}
			}
			
            //檢查是否已經選擇授信科目
            if (!RateAction.checkItem()) {
                return false;
            }
            
            RateAction.cleanRateWord();//;進來先清除欄位
            RateAction.rateCurrId = data ? data.oid : "";
            RateAction.init();
            
            var $form = $("#" + RateAction.CurrFormId);
            if (data) {
                $.ajax({
                    handler: inits.fhandle,
                    action: "queryL140m01n",
                    data: {
                        rateCurrId: RateAction.rateCurrId
                    },
                    success: function(obj){
                        RateAction.setOpenType(obj.rateType);
                        
                        $form.injectData(obj);
                        if (obj.ratePeriod) {
                            var ratePeriodVal = obj.ratePeriod.split("|")
                            for (var key in ratePeriodVal) {
                                $("[name=ratePeriod][value=" + ratePeriodVal[key] + "]").prop("checked", true).trigger('click').prop("checked", true);
                            }
                        }
                        RateAction.showRequire(obj.rateBase ? obj.rateBase.split("|") : "");
                        if (obj.rateBase == "@1") {
                            $("#disYearOpOth").val(obj.disYearOp);
                            $("#disYearRateOth").val(obj.disYearRate);
                        }
                        else 
                            if (obj.rateBase == "01") {
                                $("#disYearOp2").val(obj.disYearOp);
                                $("#disYearRate2").val(obj.disYearRate);
                            }
                        $form.find("select").trigger("change", "init");
                        RateAction.openRateThickBox();
                    }
                });
            }
            else {
                //如果是新增只會有type
                RateAction.setOpenType(type);
                RateAction.showRequire("");
                $form.find("select").trigger("change", "init");
                RateAction.openRateThickBox();
            }
        },
        /**
         * 儲存利率結構化或組字
         * @param {Object} needSave 是否儲存
         */
        saveL140M01N: function(needSave){
            var $form = $("#" + RateAction.CurrFormId);
            if (!$form.valid()) {
                //common.001=欄位檢核未完成，請填妥後再送出
                API.showMessage(i18n.def['common.001']);
                return false;
            }
            var rateBase = $form.find("#rateBase").val();
            
            if (rateBase == "@1") {
                $("#disYearOp").val($("#disYearOpOth").val());
                $("#disYearRate").val($("#disYearRateOth").val());
            }
            else 
                if (rateBase == "01") {
                    $("#disYearOp").val($("#disYearOp2").val());
                    $("#disYearRate").val($("#disYearRate2").val());
                }
            if (rateBase == "") {
                //l1401s02p04.044=請選擇利率條件
                API.showMessage(i18n.lms1401s0204["l1401s02p04.044"]);
                return false;
            }
            //TODO 要檢查期限起迄大小 、 月份的起迄大小 、利率 的 區間值。
            var prRateSelectVal = $("#prRateSelect").val();
            if (RateAction.openType == "2" && $("#rateBase").val() == "01" &&
            (prRateSelectVal == "U01" || prRateSelectVal == "U02")) {
                if ($("#rateTax").val() == "1") {
                    if ($("#usdRateTax").val() != "1") {
                        //l1401s02p04.047=欄位【E】外加之稅負為由借款人負擔，U01/U02內含稅負必須也為借款人負擔!!
                        API.showMessage(i18n.lms1401s0204["l1401s02p04.047"]);
                        return false;
                    }
                    if ($("#rateTaxCode").val() != $("#usdInsideRate").val()) {
                        //l1401s02p04.048=U01/U02內含稅負(欄位【C】)必須與欄位【E】外加之扣稅負擔數值一致(皆為0.946 或 0.95)
                        API.showMessage(i18n.lms1401s0204["l1401s02p04.048"]);
                        return false;
                    }
                }
            }
            
            
            RateAction.calcRate();
            FormAction.open = true;
            $.ajax({
                handler: inits.fhandle,
                action: "saveL140m01n",
                formId: [RateAction.CurrFormId, RateAction.RateMainFormId],
                data: {//把資料轉成json
                    rateType: RateAction.openType,
                    rateCurrId: RateAction.rateCurrId,
                    rateSeq: RateAction.rateSeq,
                    tabFormMainId: $("#tabFormMainId").val(),
                    loanTPList: RateAction.getSelectItem(),
                    needSave: needSave
                },
                success: function(obj){
                    $("#RateDrc").val(obj.RateDrc);
                    RateAction.rateId = obj.rateId;
                    RateAction.rateCurrId = obj.rateCurrId;
                    RateAction.setRateSeq(obj.rateSeq);
                    RateAction.resetRateGrid();
                    if (obj && obj.drc) {
                        RateAction.setPage2RateDrc(obj.drc);
                    }
                    FormAction.open = false;
                    $form.find("#rateDscr").val(obj.L140M01NDrc);
                    $form.find("#upRateDscr").val(obj.upRateDscr);
                    if (needSave) {
                        //saveSuccess=儲存成功
                        API.showMessage(i18n.def['saveSuccess']);
                    }
                }
            });
        },
        /**
         * 開啟thickBox
         */
        openRateThickBox: function(){
            $("#showforUsd").hide();
            //1新台幣、2美金、3日幣、4歐元、5人民幣、6澳幣、7港幣、Z雜幣
            switch (RateAction.openType) {
                case "1":
                    RateAction.titleName = i18n.lms1401s02["L140M01f.TWD"];
                    break;
                case "2":
                    $("#showforUsd").show();
                    RateAction.titleName = i18n.lms1401s02["L140M01f.USD"];
                    break;
                case "3":
                    RateAction.titleName = i18n.lms1401s02["L140M01f.JPY"];
                    break;
                case "4":
                    RateAction.titleName = i18n.lms1401s02["L140M01f.EUR"];
                    break;
                case "5":
                    //人民幣
                    RateAction.titleName = i18n.lms1401s0204["l1401s02p04.003"];
                    break;
                case "6":
                    //澳幣
                    RateAction.titleName = i18n.lms1401s0204["l1401s02p04.001"];
                    break;
                case "7":
                    //港幣
                    RateAction.titleName = i18n.lms1401s0204["l1401s02p04.002"];
                    break;
                case "Z":
                    RateAction.titleName = i18n.lms1401s02["L140M01f.Other"];
                    RateAction.showForOthRule();
                    break;
                    
            }
            
    
           //J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率
            RateAction.showSecondLimitRate == "Y" ? $(".showRateLimitMemo").show() : $(".showRateLimitMemo").hide();


            //J-110-0399_05097_B1001 Web eloan額度明細表登錄新台幣利率條件增加「適用新台幣七百億優惠利率檢核」及「適用新台幣競爭性利率檢核」檢核功能
            if(RateAction.openType=="1"){
            	$(".forRateTWD").show();
            }else{
            	$(".forRateTWD").hide();
            }
            
            var $form = $("#" + this.CurrFormId);
            $form.readOnlyChilds(_openerLockDoc == "1" || inits.toreadOnly);
            if (_openerLockDoc == "1" || inits.toreadOnly) {
                $form.find("#loginRateBase").hide();
            }
            else {
                $form.find("#loginRateBase").show();
            }

			// J-109-0179_09301_B1001 不得適用「本行各項優惠利率」
            RateAction.showPrimeRatePlan(RateAction.openType, (_openerLockDoc == "1" || inits.toreadOnly));

			//J-105-0187-001 Web e-Loan 國內企金授信結構化利率扣稅負擔碼新增選項由帳務系統建入
			$("#rateTaxCodeDecideFuture").change();
			
            $("#rateBox").thickbox({
                //other.login=登錄、L140M01g.rate=利率
                title: i18n.lms1401s02["other.login"] + RateAction.titleName + i18n.lms1401s02["L140M01g.rate"],
                width: 960,
                height: 500,
                readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                i18n: i18n.def,
                modal: true,
                buttons: {
                    "saveData": function(){
                        RateAction.saveL140M01N(true);
                    },
                    "close": function(){
                        $.thickbox.close();
                    }
                }
            });
        },
        /**
         * 取得目前開起視窗對應幣別
         * return  幣別種類 1新台幣-TWD、2美金-USD、3日幣-JPY、4歐元-EUR、5人民幣(CNY)、6澳幣-AUD 、7港幣-HKD、Z雜幣
         
         */
        getNowTypeCurr: function(){
            var curr = "";
            switch (RateAction.openType) {
                case "1":
                    curr = "TWD";
                    break;
                case "2":
                    curr = "USD";
                    break;
                case "3":
                    curr = "JPY";
                    break;
                case "4":
                    curr = "EUR";
                    break;
                case "5":
                    curr = "CNY";
                    break;
                case "6":
                    curr = "AUD";
                    break;
                case "7":
                    curr = "HKD";
                    break;
                    
                    
                case "Z":
                    curr = "Z";
                    break;
            }
            
            return curr;
        },
        
		/**
         *取得All-in最大利率與最小利率(自訂利率用)
         @param {Array} code 利率代碼陣列 [M1,M2,......]
         */
        setMaxAndMinAllInRate: function(codes,disYearOpVal,disValueIn){
            var result = {};
            $.ajax({
                async: false,
                handler: inits.fhandle,
                action: "getRateMaxAndMin",
                data: {
                    curr: RateAction.getNowTypeCurr(),
                    codes: codes
                },
                success: function(obj){
                	
                	//L-107-0170_05097_B1001 Web e-Loan國內企金授信配合BTT作業系統0190查詢利率作業調整，屬SHIBOR相關之利率選項MI、MJ、MK、ML、MM、MN、MO等，暫不提供利率數值。
                	if(obj.hasShibor == "Y"){
                		alert("系統暫不提供SHIBOR利率查詢，請先至路透社等相關網站查詢後再手動調整");
                	}
                	
                    var $formData = $("#" + RateAction.CurrFormId);
					
					//var disValueIn = $("#disYearRateOthAllIn").val();
					var disValue = parseFloat(disValueIn, 10);
                    
					var max = parseFloat(obj.RateMax, 10);
		            var min = parseFloat(obj.RateMin, 10);
 
		            //加減碼
		            //var disYearOpVal = $("#disYearOpOthAllIn").val();
					
					
		            if (disYearOpVal == "1") {
		                max += disValue;
		                min += disValue;
		            }
		            else if (disYearOpVal == "2") {
		                    max -= disValue;
		                    min -= disValue;
		            }
				    $("#allInRateMinBfT").val(min.toFixed(5) );
                    $("#allInRateMaxBfT").val(max.toFixed(5) );
					
					//var rateBase = $formData.find("#rateBase").val();
					var $hasMaxShow = $(".hasMaxShowAllInT");
					$hasMaxShow.show();
					$("#allInRateMaxBfT").show();
					$("#allInRateMaxAfT").show();
					
					if (isNaN($("#allInRateMaxBfT").val())) {
						$("#allInRateMaxBfT").val("");
						$("#allInRateMaxAfT").val("");
					}
					
					/*
					  if (!$.isNaN($("#allInRateMaxBfT").val())) {
						   	$hasMaxShow.show();
						    $("#allInRateMaxBfT").show();
							$("#allInRateMaxAfT").show();
						}else{
							$hasMaxShow.hide();
						    $("#allInRateMaxBfT").hide();
							$("#allInRateMaxAfT").hide();
						}	
					*/	
					
						
					RateAction.calcRateAllIn();
					
                }
            });
        },
        tAllInRateMinBfT: 0,
        tAllInRateMaxBfT: 0,
        tAllInRateMinAfT: 0,
        tAllInRateMaxAfT: 0,
        /**
         *取得All-in最大利率與最小利率(自訂利率用)
         @param {Array} code 利率代碼陣列 [M1,M2,......]
         */
        getMaxAndMinAllInRate: function(codes,disYearOpVal,disValueIn){
            var result = {};
            $.ajax({
                async: false,
                handler: inits.fhandle,
                action: "getRateMaxAndMin",
                data: {
                    curr: RateAction.getNowTypeCurr(),
                    codes: codes
                },
                success: function(obj){
                	
                	//L-107-0170_05097_B1001 Web e-Loan國內企金授信配合BTT作業系統0190查詢利率作業調整，屬SHIBOR相關之利率選項MI、MJ、MK、ML、MM、MN、MO等，暫不提供利率數值。
                	if(obj.hasShibor == "Y"){
                		alert("系統暫不提供SHIBOR利率查詢，請先至路透社等相關網站查詢後再手動調整");
                	}
                	
                    var $formData = $("#" + RateAction.CurrFormId);
					
					//var disValueIn = $("#disYearRateOthAllIn").val();
					var disValue = parseFloat(disValueIn, 10);
                    
					var max = parseFloat(obj.RateMax, 10);
		            var min = parseFloat(obj.RateMin, 10);
 
		            //加減碼
		            //var disYearOpVal = $("#disYearOpOthAllIn").val();
					
					
		            if (disYearOpVal == "1") {
		                max += disValue;
		                min += disValue;
		            }
		            else if (disYearOpVal == "2") {
		                    max -= disValue;
		                    min -= disValue;
		            }
				    $("#allInRateMinBfT").val(min.toFixed(5) );
                    $("#allInRateMaxBfT").val(max.toFixed(5) );
                    
                    RateAction.tAllInRateMinBfT = min.toFixed(5) ;
                    RateAction.tAllInRateMaxBfT = max.toFixed(5) ;
					
                }
            });
        },
		/**
         *取得最大利率與最小利率
         @param {Array} code 利率代碼陣列 [M1,M2,......]
         */
        setMaxAndMinRate: function(codes){
            var result = {};
            $.ajax({
                async: false,
                handler: inits.fhandle,
                action: "getRateMaxAndMin",
                data: {
                    curr: RateAction.getNowTypeCurr(),
                    codes: codes
                },
                success: function(obj){
                	//L-107-0170_05097_B1001 Web e-Loan國內企金授信配合BTT作業系統0190查詢利率作業調整，屬SHIBOR相關之利率選項MI、MJ、MK、ML、MM、MN、MO等，暫不提供利率數值。
                	if(obj.hasShibor == "Y"){
                		alert("系統暫不提供SHIBOR利率查詢，請先至路透社等相關網站查詢後再手動調整");
                	}
                    var $form = $("#" + RateAction.CurrFormId);
                    $form.find("#tRateMax").val(obj.RateMax);
                    $form.find("#tRateMin").val(obj.RateMin);
                    RateAction.calcRate();
                    
                }
            });
        },
        /**
         * 修改利率顯示訊息
         * @param {Array} code 利率代碼陣列 [M1,M2,......]
         */
        getShowText: function(code){
            var itemOpt = RateAction.getRate();
            var itemOptHis = RateAction.baseSelectHis; //{"S5":"TEST"};
            var showText = "";
            if ($.trim(code) && code.length > 0 && RateAction.openType != "Z") {
                for (var key in code) {
                    showText += (showText.length > 0 ? "<br/>" : "") + code[key] + ":" + (itemOpt[code[key]] || itemOptHis["00" + code[key]] || itemOptHis["01" + code[key]] || itemOptHis["39" + code[key]] || itemOptHis["99" + code[key]]);
                }
            }
            $("#showRateText").html(showText);
        },
        
        
        
        /**
         * 利率基礎顯示選項規則
         * @param {Array} data [利率代碼 M1,M2,......]
         * @param {Object} reloadRate 是否重新取得最大利率 最小利率
         */
        showRequire: function(data, reloadRate){
            RateAction.getShowText(data)
            $(".othRateHide,#loginRateBase").show();
            $("#selfOpTb,#selectOtherBase,#forRateUSD,#rateBaseOpTb,#rateBaseGroupTb,#otherRateDrc,#attRateTr,#rateChgKindTr,#ByOtherRate1,#disYearOpDiv2").hide();
            
			//ALL-IN
			for(i=1;i<4;i++){
				if (isNaN($("#allInRateMinBf"+i).val())) {
					//一開始什麼值都沒有時
				    $(".hasMaxShowAllIn"+i).hide();
				    $("#allInRateMaxBf"+i).hide();
					
				}else{
					if (!isNaN($("#allInRateMaxBf"+i).val())) {
					   	$(".hasMaxShowAllIn"+i).show();
					    $("#allInRateMaxBf"+i).show();
					}else{
						$(".hasMaxShowAllIn"+i).hide();
					    $("#allInRateMaxBf"+i).hide();								
					}	
				}		
			} 
					
			if (data) {
                //$("#disYearRateTb").show();
                //自訂利率
                if (data == "01") {
                    $("#selfOpTb,#ratePeriodTr,#disYearOpDiv2").show();
                    RateAction.setPrRate();
                    RateAction.showforUsdRule();
					
                }else {

					if (RateAction.openType == "Z") {
						//雜幣
                        RateAction.showForOthRule();
                    }
                    else {
						//一般利率條件
						
                        $("#rateBaseOpTb").show();
						var $rateBaseGroupTb = $("#rateBaseGroupTb");
                        if ($.trim($rateBaseGroupTb.find("#groupName").html()) && data.length == 1) {
                            $rateBaseGroupTb.show();
                        }
                        else {
                            CntrNoAPI.cleanTrHideInput($rateBaseGroupTb);
                        }
                        if (reloadRate) {
                            RateAction.setMaxAndMinRate(data);
                        }
                    }
				}
                    
            }
            
        },
        /**
         * 開啟登錄利率基礎box
         */
        openRateBaseBox: function(){
            var itemOpt = RateAction.getRate();
            $("#tempRateBass").setItems({
                size: "1",
                item: itemOpt,
                format: "{value}:{key} ",
                fn: function(v, k){
                    if ($(this).val() == "01") {
                        if (this.checked) {
                            $("[name=tempRateBass][value!=01]").removeAttr("checked").prop("disabled", true);
                        }
                        else {
                            $("[name=tempRateBass][value!=01]").removeAttr("disabled");
                        }
                    }
                }
            });
            //註解的這段是幫他帶上預設值
            //            var value = $("#rateBase").val();
            //            $("[name=tempRateBass]").val(value.split("|")).removeAttr("disabled");
            //           
            //            if (value == "01") {
            //                $("[name=tempRateBass][value!=01]").removeAttr("checked").attr("disabled", "disabled");
            //            }
            $("[name=tempRateBass]").removeAttr("disabled");
            $("[name=tempRateBass]").closest("td").css("background", "#FFF");
            $("#rateBaseBox").thickbox({
                //l1401s02p04.010=利率基礎
                title: i18n.lms1401s0204["l1401s02p04.010"],
                width: 550,
                height: 300,
                align: "center",
                valign: "bottom",
                modal: true,
                readOnly: _openerLockDoc == "1",
                i18n: i18n.def,
                open: function(){
                
                },
                buttons: {
                    "sure": function(){
                        var data = [];
                        var groupName = "";
                        var showNotSameGroup = "";
                        var haveNotSameGroup = false;
                        var count = 1;
                        $("[name=tempRateBass]:checked").each(function(v, k){
                            var key = $(k).val();
                            var text = $(k).parent("label").text();
                            //先將第一個選項帶入 groupName 當有一筆與原本 groupName 不相同則錯誤訊息
                            showNotSameGroup = RateAction.checkGroupName(text);
                            if (count == 1) {
                                groupName = RateAction.checkGroupName(text);
                            }
                            else 
                                if (groupName != showNotSameGroup) {
                                    haveNotSameGroup = true;
                                    return false;
                                }
                            count++;
                            data.push(key);
                        });
                        
                        if (data.length == 0) {
                            //grid_selector=請選擇資料
                            return API.showMessage(i18n.def["grid_selector"]);
                        }
                        
                        if (haveNotSameGroup) {
                            //l1401s02p04.043=不同貨幣利率群組不能複選(例如: {0} 與 {1} )不能共簽同一利率文件
                            return API.showMessage(i18n.lms1401s0204["l1401s02p04.043"].replace("{0}", groupName).replace("{1}", showNotSameGroup));
                        }
                        if (data == "01") {
                            //當選擇自訂利率要初始化自訂天期的選單
                            $("[name=ratePeriod]").removeAttr("disabled").removeAttr("checked");
                        }
                        if (groupName == "") {
                            $("#rateBaseGroupTb").find("#groupName").html("");
                        }
                        else {
                            $("#rateBaseGroupTb").find("#groupName").html(groupName);
                        }
                        var tempData = [];
                        var tempData1 = [];
                        var tempData2 = [];
						var tempData3 = [];
						var tempData4 = [];
                        //Start 2013/06/27,Rex,針對SO,SP,SN 和SH,ST,SJ,SL利率作排序
                        for (var key in data) {
                            var value = data[key];
                            if (value == "SN" || value == "SO" || value == "SP") {
                                tempData1.push(value);
                            }
                            else if (value == "SH" || value == "SJ" || value == "SL" || value == "ST") {
								tempData2.push(value);
							}
							else if (value == "PE" || value == "PF" || value == "PI" || value == "KX" || value == "N1" || value == "QX") {
								//路透（股）公司６１６５頁ＣＰ排序
									tempData3.push(value);
							}
							else if (value == "MP" || value == "MQ" || value == "MR" || value == "MY" || value == "MS" || value == "MT" || value == "MU" || value == "MV") {
								//人民幣 HIBOR
								tempData4.push(value);
							}
							else {
									tempData.push(value);
							}
                        }
                        var sort1 = {
                            SO: 1,
                            SP: 2,
                            SN: 3
                        };
                        tempData1.sort(function(a, b){
                            return sort1[b] - sort1[a];
                        });
                        for (var key in tempData1) {
                            tempData.unshift(tempData1[key]);
                        }
                        var sort2 = {
                            SH: 1,
                            ST: 2,
                            SJ: 3,
                            SL: 4
                        };
                        tempData2.sort(function(a, b){
                            return sort2[b] - sort2[a];
                        });
                        for (var key in tempData2) {
                            tempData.unshift(tempData2[key]);
                        }
						
						
						var sort3 = {
                            PE: 1,
                            PF: 2,
							KX: 3,
                            N1: 4,
                            QX: 5,
							PI: 6
                        };
                        tempData3.sort(function(a, b){
                            return sort3[b] - sort3[a];
                        });
                        for (var key in tempData3) {
                            tempData.unshift(tempData3[key]);
                        }
						
						var sort4 = {
                            MP: 1,
                            MQ: 2,
							MR: 3,
                            MY: 4,
                            MS: 5,
							MT: 6,
							MU: 7,
							MV: 8
                        };
                        tempData4.sort(function(a, b){
                            return sort4[b] - sort4[a];
                        });
                        for (var key in tempData4) {
                            tempData.unshift(tempData4[key]);
                        }
                        
                        //end 2013/06/27,Rex,針對SO,SP,SN 和SH,ST,SJ,SL利率作排序
                        $("#rateBase").val(tempData.join("|"));
                        RateAction.showRequire(tempData, true);
                        $.thickbox.close();
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        },
		/**
         * 開啟查詢利率基礎box
         */
        openQueryRateBaseBox: function(){
		   RateAction.reloadQueryRateBaseGird();
		   $("#queryRateBaseBox").thickbox({
                //L140M01a.collectPay=借款收付彙計數 
                title: "查詢利率", //i18n.lms1401s02['L140M01a.collectPay'],
                width: 640,
                height: 370,
                modal: true,
                readOnly: false,
                i18n: i18n.def,
                buttons: {
                    "close": function(){
                        $.thickbox.close();
                    }
                }
            });
        },
		/**
         * 查詢All-in 掛牌利率box
         */
        openComputeAllInBox: function(fId){
		    var $form = $("#" + RateAction.CurrFormId);
 
            var rateBase = $form.find("#rateBase").val();
            
            if (rateBase == "") {
                //l1401s02p04.044=請選擇利率條件
                API.showMessage(i18n.lms1401s0204["l1401s02p04.044"]);
                return false;
            }
			
			if (rateBase == "01" || rateBase == "@1") {
                $("#selCurrentRate").hide();
            }else{
				$("#selCurrentRate").show();
			}
			
			//J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率
			if($form.find("#rateLimitType").val()=="0" || $form.find("#rateLimitType").val()==""){
				$("#selLimitRate").hide();	
			}else{
				$("#selLimitRate").show();
			}
			
			//J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率
//			if($form.find("#rateLimitType2").val()=="0" || $form.find("#rateLimitType2").val()==""){
//				$("#selLimitRate2").hide();	
//			}else{
//				$("#selLimitRate2").show();
//			}
			
            $("#allInRateMinBfT").val("");
			$("#allInRateMaxBfT").val("");
			$("#rateTaxCodeAllIn").val("");
			$("#allInRateMinAfT").val("");
			$("#allInRateMaxAfT").val("");
			
            $("#computeAllInBox").thickbox({
                //l1401s02p04.051=修改欄位值
                title: "試算利率",
                width: 600,
                height: 300,
                modal: true,
                align: "center",
                valign: "bottom",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        $form.find("#allInRateMinBf"+fId).val($("#allInRateMinAfT").val());
						$form.find("#allInRateMaxBf"+fId).val($("#allInRateMaxAfT").val());
						if(!isNaN($("#allInRateMaxAfT").val())){
							$form.find("#allInRateMaxBf"+fId).show();
							$form.find(".hasMaxShowAllIn"+fId).show();
							
						}else{
							$form.find("#allInRateMaxBf"+fId).hide();
							$form.find(".hasMaxShowAllIn"+fId).hide();
						}
                        $.thickbox.close();
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
		},   
		/**
         * 查詢All-in 掛牌利率box
         */
        openAllInSelRateBaseBox: function(){
		   RateAction.reloadAllInSelRateBaseGird();
		   
		   $("#disYearOpOthAllIn option[value=" + $("#disYearOp2").find(":selected").val() + "]").prop('selected', true);
 
		   $("#disYearRateOthAllIn").val($("#disYearRate2").val());
		   
		   $("#selAllInRateBaseBox").thickbox({
                //L140M01a.collectPay=借款收付彙計數 
                title: "查詢利率", //i18n.lms1401s02['L140M01a.collectPay'],
                width: 640,
                height: 370,
                modal: true,
                readOnly: false,
                i18n: i18n.def,
                buttons: {
					"sure": function(){
						$.thickbox.close();
						
						var $gridviewAllIn = $("#gridviewQueryRateBaseAllIn");
				        var ids = $gridviewAllIn.getGridParam('selarrrow');
				        var codes = [];
				        if (ids == "") {
				            //action_005=請先選取一筆以上之資料列
				            return CommonAPI.showErrorMessage(i18n.def['action_005']);
				        }
				        for (var i in ids) {
				            codes.push($gridviewAllIn.getRowData(ids[i]).LR_CODE);
				        }

						var disYearOpVal = $("#disYearOpOthAllIn").val();
                        var disValueIn = $("#disYearRateOthAllIn").val();

			            if($("#rateTaxCodeAllIn").val() == ""){
							if ($("#rateTax").val() == "1") {
								$("#rateTaxCodeAllIn").val($("#rateTaxCode").val());
							}
							else {
							    $("#rateTaxCodeAllIn").val("");
							}	
						}
						
						RateAction.setMaxAndMinAllInRate(codes,disYearOpVal,disValueIn);
		
					},	
                    "close": function(){
                        $.thickbox.close();
                    }
                }
            });
        },
		/**
         * 查詢All-in 目前敘作利率box
         */
        applyCurrentRate: function(){
			if (!isNaN($("#reRateMin").val())) {
			   $("#allInRateMinBfT").val($("#reRateMin").val());
			}else{
			   $("#allInRateMinBfT").val("");
			}	
			$("#allInRateMaxBfT").val($("#reRateMax").val());
			if ($("#rateTax").val() == "1") {
				$("#rateTaxCodeAllIn").val($("#rateTaxCode").val());
			}
			else {
			    $("#rateTaxCodeAllIn").val("");
			}	
			
			RateAction.calcRateAllIn();
			
			/*
				if (!$.isNaN($("#reRateMax").val())) {
				   $("#allInRateMaxBfT").show();
				   $("#allInRateMaxAfT").show();
				   $("#allInRateMaxBfT").val($("#reRateMax").val());
				   $("#allInRateMaxAfT").val("");
				   $(".hasMaxShowAllInT").show();
				}else{
				   $("#allInRateMaxBfT").hide();
				   $("#allInRateMaxAfT").hide();
				   $("#allInRateMaxBfT").val("");
				   $("#allInRateMaxAfT").val("");
				   $(".hasMaxShowAllInT").hide();
				}				
			*/
			
		},	
		/**
         * 查詢All-in 下限利率box
         */
        applyLimitRate_old: function(){
        	
        	if($("#rateLimitType").val()== "13"){
				//惟不得低於「XX」利率加「XX」％除以「XX」
				if($("#rateLimitCodeSelect").val()== "01"){
					//自訂利率
					$("#allInRateMinBfT").val($("#rateLimitCountPr").val());
					$("#allInRateMaxBfT").val("");
				}else{
					//掛牌利率
					var codes = $("#rateLimitCodeSelect").val();
					var disYearOpVal = $("#rateLimitCountType").val();
					var disValueIn = $("#rateLimitCountRate").val();
					RateAction.setMaxAndMinAllInRate(codes,disYearOpVal,disValueIn);
				}
				
				if ($("#rateLimitTax").val() == "1") {
					$("#rateTaxCodeAllIn").val($("#rateLimitTaxRate").val());
				}
				else {
				    $("#rateTaxCodeAllIn").val("");
				}	
			
				RateAction.calcRateAllIn();
				
			
			}else{
				
				return API.showMessage(i18n.def["grid.emptyrecords"]);
			}
		},	
		/**
         * 查詢All-in 下限利率box
         */
        applyLimitRate: function(){
        	
        	var hasLimit1 = false;
        	var tAllInRateMinBfT_1 = "";
        	var tAllInRateMaxBfT_1 = "";
        	var tRateTaxCodeAllIn_1 = "";
			if($("#rateLimitType").val()== "13"){
				hasLimit1 = true;
				//惟不得低於「XX」利率加「XX」％除以「XX」
				if($("#rateLimitCodeSelect").val()== "01"){
					//自訂利率
					tAllInRateMinBfT_1 = $("#rateLimitCountPr").val() ;
					tAllInRateMaxBfT_1 = "";
				}else{
					//掛牌利率
					var codes = $("#rateLimitCodeSelect").val();
					var disYearOpVal = $("#rateLimitCountType").val();
					var disValueIn = $("#rateLimitCountRate").val();
					RateAction.getMaxAndMinAllInRate(codes,disYearOpVal,disValueIn);
					
					tAllInRateMinBfT_1 = isNaN(RateAction.tAllInRateMinBfT) ? "" : RateAction.tAllInRateMinBfT ;
					tAllInRateMaxBfT_1 = isNaN(RateAction.tAllInRateMaxBfT) ? "" : RateAction.tAllInRateMaxBfT ;
				}
				
				if ($("#rateLimitTax").val() == "1") {
					tRateTaxCodeAllIn_1 = $("#rateLimitTaxRate").val();
				}
				else {
					tRateTaxCodeAllIn_1 = "";
				}	
			}
			
			var hasLimit2 = false;
			var tAllInRateMinBfT_2 = "";
        	var tAllInRateMaxBfT_2 = "";
        	var tRateTaxCodeAllIn_2 = "";
			
        	//J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率
			if($("#rateLimitType2").val()== "13"){
				hasLimit2 = true;
				//惟不得低於「XX」利率加「XX」％除以「XX」
				if($("#rateLimitCodeSelect2").val()== "01"){
					//自訂利率
					tAllInRateMinBfT_2 = $("#rateLimitCountPr2").val() ;
					tAllInRateMaxBfT_2 = "";
				}else{
					//掛牌利率
					var codes = $("#rateLimitCodeSelect2").val();
					var disYearOpVal = $("#rateLimitCountType2").val();
					var disValueIn = $("#rateLimitCountRate2").val();
					RateAction.getMaxAndMinAllInRate(codes,disYearOpVal,disValueIn);
					tAllInRateMinBfT_2 = isNaN(RateAction.tAllInRateMinBfT) ? "" : RateAction.tAllInRateMinBfT ;
					tAllInRateMaxBfT_2 = isNaN(RateAction.tAllInRateMaxBfT) ? "" : RateAction.tAllInRateMaxBfT ;
				}
				
				if ($("#rateLimitTax2").val() == "1") {
					tRateTaxCodeAllIn_2 = $("#rateLimitTaxRate2").val();
				}
				else {
				    tRateTaxCodeAllIn_2 = "";
				}	
			}
			
//			alert("tAllInRateMinBfT_1="+tAllInRateMinBfT_1);
//			alert("tAllInRateMaxBfT_1="+tAllInRateMaxBfT_1);
//			alert("tAllInRateMinBfT_2="+tAllInRateMinBfT_2);
//			alert("tAllInRateMaxBfT_2="+tAllInRateMaxBfT_2);
//			
//			alert("tRateTaxCodeAllIn_1="+tRateTaxCodeAllIn_1);
//			alert("tRateTaxCodeAllIn_2="+tRateTaxCodeAllIn_2);
//			
			
			var limitRateArr  = new Array();
			var i = 0;
			if(!isNaN(tAllInRateMinBfT_1)){
				limitRateArr[i] =tAllInRateMinBfT_1;
				i=i+1;
			}
			if(!isNaN(tAllInRateMaxBfT_1)){
				limitRateArr[i] =tAllInRateMaxBfT_1;
				i=i+1;
			}
			if(!isNaN(tAllInRateMinBfT_2)){
				limitRateArr[i] =tAllInRateMinBfT_2;
				i=i+1;
			}
			if(!isNaN(tAllInRateMaxBfT_2)){
				limitRateArr[i] =tAllInRateMaxBfT_2;
				i=i+1;
			}
			
			
			var limitRateTaxCodeAllIn  = new Array();
			var i = 0;
			if(!isNaN(tRateTaxCodeAllIn_1)){
				limitRateTaxCodeAllIn[i] =tRateTaxCodeAllIn_1;
				i=i+1;
			}
			if(!isNaN(tRateTaxCodeAllIn_2)){
				limitRateTaxCodeAllIn[i] =tRateTaxCodeAllIn_2;
				i=i+1;
			}
			
			
			
			
			if(hasLimit1 == true || hasLimit2 == true ){
				var min = Math.min.apply(null,limitRateArr);
                min = isNaN(min) ? "" : min ;
                var max = Math.max.apply(null,limitRateArr);
                max = isNaN(max) ? "" : max ;
                var tax = Math.max.apply(null,limitRateTaxCodeAllIn);
                tax = isNaN(tax) ? "" : tax ;
                
                if(max == min){
                	max = "";
                }

				$("#allInRateMinBfT").val(min);
				$("#allInRateMaxBfT").val(max);
				$("#rateTaxCodeAllIn").val(tax);
				RateAction.calcRateAllIn();
				
			}else{
				return API.showMessage(i18n.def["grid.emptyrecords"]);
			}
		},			
        /**
         * 取得利率選項
         * return code:value
         */
        getRate: function(){
            var itemOpt = {};
            var curr = RateAction.getNowTypeCurr();
            itemOpt = RateAction.baseSelect[curr];
            //下限利率-利率代碼
            $("#rateLimitCodeSelect").setItems({
                item: itemOpt,
                value: $("#rateLimitCode").val() || "",
                format: "{value}:{key}",
                fn: function(e, data){
                    $("#rateLimitCountRateSpan1_1,#rateLimitCountPrSpan,#rateLimitCodeGroup,#rateLimitCountRateSpan1_2").hide();
                    if (!data) {
                        $("#rateLimitCountRateSpan1_1,#rateLimitCountPrSpan,#rateLimitCodeGroup").find("select,input:text,span.field").each(function(){
                            if (this.nodeName == "SELECT") {
                                $(this).val($(this).find("option").first().val());
                            }
                            else 
                                if (this.nodeName == "SPAN") {
                                    $(this).html("");
                                }
                                else {
                                    $(this).val("");
                                }
                        });
                    }
                    
                    $("#rateLimitCode").val($(this).val());
                    switch ($(this).val()) {
                        case "":
                            break;
                        case "01":
                            $("#rateLimitCountPrSpan").show();
                            break;
                        default:
                            var value = RateAction.checkGroupName($(this).find("option:selected").text());
                            if (value) {
                                $("#rateLimitCodeGroup").show().find("#rateLimitMarket").html(value);
                            }
                            $("#rateLimitCountRateSpan1_1").show();
                            break;
                    }
                }
            });
            
            //J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率
            //下限利率-利率代碼
            $("#rateLimitCodeSelect2").setItems({
                item: itemOpt,
                value: $("#rateLimitCode2").val() || "",
                format: "{value}:{key}",
                fn: function(e, data){
                	$("#rateLimitCountRateSpan2_1,#rateLimitCountPrSpan2,#rateLimitCodeGroup2,#rateLimitCountRateSpan2_2").hide();
                    if (!data) {
                        $("#rateLimitCountRateSpan2_1,#rateLimitCountPrSpan2,#rateLimitCodeGroup2").find("select,input:text,span.field").each(function(){
                            if (this.nodeName == "SELECT") {
                                $(this).val($(this).find("option").first().val());
                            }
                            else 
                                if (this.nodeName == "SPAN") {
                                    $(this).html("");
                                }
                                else {
                                    $(this).val("");
                                }
                        });
                    }
                    
                   $("#rateLimitCode2").val($(this).val());
                   
                    switch ($(this).val()) {
                        case "":
                            break;
                        case "01":
                            $("#rateLimitCountPrSpan2").show();
                            break;
                        default:
                            var value = RateAction.checkGroupName($(this).find("option:selected").text());
                            if (value) {
                                $("#rateLimitCodeGroup2").show().find("#rateLimitMarket2").html(value);
                            }
                            $("#rateLimitCountRateSpan2_1").show();
                            break;
                    }
                }
            });
            
            return itemOpt;
        },
        /**
         * 取得市場利率選項
         * @param {String} type
         * SIBOR | 1
         * LIBOR | 2
         *
         */
        setMarketRate: function(type){
            var option = RateAction.marketRateSelect["A" + type];
            if (option) {
                $("#usdMarketRateSelect").setItems({
                    space: false,
                    size: "4",
                    value: $("#usdMarketRate").val() ? $("#usdMarketRate").val().split("|") : "",
                    item: option,
                    format: "{value}-{key}",
                    fn: function(){
                        var data = [];
                        $("[name=usdMarketRateSelect]:checked").each(function(){
                            data.push($(this).val());
                        });
                        $("#usdMarketRate").val(data.join("|"));
                    }
                });
                
            }
            
            
        },
        /**
         * 設定自定利率下拉選單
         *
         */
        setPrRate: function(){
            var itemOpt = {};
            var $attRateTr = $("#attRateTr");
            var curr = RateAction.getNowTypeCurr();
            if (RateAction.prRateSelect[curr]) {
                itemOpt = RateAction.prRateSelect[curr];
                $("#prRateSelect").setItems({
                    space: true,
                    item: itemOpt,
                    value: $("#prRate").val(),
                    format: "{value}-{key}",
                    fn: function(){
                        $("#prRate").val($(this).val());
                        RateAction.showforUsdRule();
                        
                        $attRateTr.hide();
                        if ($(this).val() == "C01" || $(this).val() == "U09") {
							//N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改   U09為固定利率比照C01
                            $attRateTr.show();
                        }
                    }
                });
                
                var sysdate = CommonAPI.getToday();
                if (sysdate > "2013-12-31") {
                    $("#prRateSelect").find("option[value=U03]").prop("disabled", true);
					$("#usdMarket").find("option[value=1]").prop("disabled", true);
                }
                
            }
            
        },
        /**
         * 美金自訂利率顯示規則
         *
         */
        showforUsdRule: function(){
            if (RateAction.openType == "2") {
                var $ratePeriodTr = $("#ratePeriodTr");
                var value = $("#prRate").val();
                if (value != "") {
                    if (value == "U01" || value == "U02") {
                        $("#forRateUSD").show();
                        CntrNoAPI.cleanTrHideInput($ratePeriodTr);
                    }
                    else {
                        $("#selfOpTb,#ratePeriodTr").show();
                        //初始化欄位
                        CntrNoAPI.cleanTrHideInput($("#forRateUSD"));
                    }
                }
                else {
                    CntrNoAPI.cleanTrHideInput($("#forRateUSD,#ratePeriodTr"));
                }
                
            }
        },
        /**
         * 雜幣利率顯示規則
         *
         */
        showForOthRule: function(){
            if (RateAction.openType == "Z") {
                var data = $("#rateBase").val();
                $("#selectOtherBase").show().val(data);
                $("#otherRateDrc,#loginRateBase,.othRateHide,#ByOtherRate1").hide();
                if (data == "@1") {
                    $(".othRateHide").show();
                    $("#ByOtherRate1").show();
                }
                else 
                    if (data == "@2") {
                        $("#otherRateDrc").show();
                        //清掉下限利率
                     
                        var $rateLimitTypeTable = $("#rateLimitTypeTable");
                        $rateLimitTypeTable.find("select,input:text").each(function(){
                            if (this.nodeName == "SELECT") {
                                $(this).val($(this).find("option").first().val());
                            }
                            else {
                                $(this).val("");
                            }
                        });
                        $("#rateLimitType option[value='0']").prop("selected", true); //設置Select的Text值為jQuery的項選中
            			$("#rateLimitType").change();
            			$(".showRateLimit").hide();
                    }
                    else {
                    
                    }
            }
            
        },
        /**
         * 算出目前敘作利率
         */
        calcRate: function(){
            var formData = $("#" + RateAction.CurrFormId).serializeData();
            var disValue = $.trim(formData.disYearRate);
            if (disValue == "") {
                disValue = 0;
            }
            disValue = parseFloat(disValue, 10);
            var max = parseFloat(formData.tRateMax, 10);
            var min = parseFloat(formData.tRateMin, 10);
            
            if (formData.ctRateMax != "") {
                max = parseFloat(formData.ctRateMax, 10);
            }
            if (formData.ctRateMin != "") {
                min = parseFloat(formData.ctRateMin, 10);
            }
            //加減碼
            var disYearOpVal = $("#disYearOp").val();
            if (disYearOpVal == "1") {
                max += disValue;
                min += disValue;
            }
            else 
                if (disYearOpVal == "2") {
                    max -= disValue;
                    min -= disValue;
                }
            
            var maxResult = "";
            var $hasMaxShow = $(".hasMaxShow");
            $hasMaxShow.hide();
            if (!isNaN(max)) {
                maxResult = max.toFixed(5) * 10000 / 10000;
                $hasMaxShow.show();
            }
            if (inits.toreadOnly) {
                $("#tRateMaxEdit").hide();				
            }
			
			$("#reRateMax").html(maxResult);
	        $("#reRateMin").html(min.toFixed(5) * 10000 / 10000);

        },
		
		/**
         * 算出目前all-in最小試算利率
         */
        compareAllIn: function(){
			var minArr = [];
			var maxArr = [];
			var hasMax = "N";
			for(i=1;i<4;i++ ){
			   if(!isNaN($("#allInRateMinBf"+i).val())){
			   	  minArr.push($("#allInRateMinBf"+i).val()); 
			   }
			   if(!isNaN($("#allInRateMaxBf"+i).val())){
			   	  maxArr.push($("#allInRateMaxBf"+i).val());
				  hasMax = "Y";		  
			   }
			}	
			
			var minVal = Math.min.apply(Math, minArr);
			$("#allInRateMinAf").val(minVal);
			
			if(hasMax == "Y"){
				var maxVal = Math.min.apply(Math, maxArr);
				$("#allInRateMaxAf").val(maxVal);
			}else{
				$("#allInRateMaxAf").val("");
			}	

				
		},	
		/**
         * 算出目前all-in最高試算利率
         */
		compareAllInMax: function(){
			var minArr = [];
			var maxArr = [];
			var hasMax = "N";
			for(i=1;i<4;i++ ){
			   if(!isNaN($("#allInRateMinBf"+i).val())){
			   	  minArr.push($("#allInRateMinBf"+i).val()); 
			   }
			   if(!isNaN($("#allInRateMaxBf"+i).val())){
			   	  maxArr.push($("#allInRateMaxBf"+i).val());
				  hasMax = "Y";		  
			   }
			}	
			
			var minVal = Math.max.apply(Math, minArr);
			$("#allInRateMinAf").val(minVal);
			
			if(hasMax == "Y"){
				var maxVal = Math.max.apply(Math, maxArr);
				if(maxVal > minVal){
					$("#allInRateMaxAf").val(maxVal);
				}else{
					$("#allInRateMaxAf").val("");
				} 
			}else{
				$("#allInRateMaxAf").val("");
			}
		},
		/**
         * 算出目前all-in敘作利率
         */
        calcRateAllIn: function(){

            var formData = $("#" + RateAction.CurrFormId).serializeData();

            var max = parseFloat($("#allInRateMaxBfT").val(), 10);
            var min = parseFloat($("#allInRateMinBfT").val(), 10);
			
			var rateTaxCode = parseFloat($("#rateTaxCodeAllIn").val(), 10);
			
            var maxResult = "";
            var $hasMaxShow = $(".hasMaxShowAllInT");
            $hasMaxShow.hide();
            if (!isNaN(max)) {
				if (!isNaN(rateTaxCode)) {
				   maxResult = max.toFixed(5) / rateTaxCode * 10000 / 10000 ;
				   maxResult = maxResult.toFixed(5);
				}else{
				   maxResult = max.toFixed(5) 
				}	
                
                $hasMaxShow.show();
            }
             
			var minResult = ""; 
			if (!isNaN(rateTaxCode)) {
				minResult = min.toFixed(5) / rateTaxCode * 10000 / 10000;
				minResult = minResult.toFixed(5);			    
			}else{
				minResult = min.toFixed(5) 
			}
			
			if (!isNaN(minResult)) {
				$("#allInRateMinAfT").val(minResult);	
			}else{
				$("#allInRateMinAfT").val("");	
			}	
			
			if (!isNaN(maxResult)) {
				$("#allInRateMaxAfT").val(maxResult);
			}else{
				$("#allInRateMaxAfT").val("");	
			}	

        },
        showPrimeRatePlan: function(openType, readonly){
            // J-109-0179_09301_B1001 適用優惠利率
            //1新台幣、2美金、3日幣、4歐元、5人民幣、6澳幣、7港幣、Z雜幣
            if(readonly){
                $("#inapplicability").prop("disabled", true);
                $("#primeRatePlan").prop("disabled", true);
                if(openType == "2") {
                    $("#primeRatePlan option[value=3]").text("本行美元二十億元優惠利率");
                } else {
                    $("#primeRatePlan option[value=3]").text("本行競爭性利率");
                }
            } else {
                var chk = $("#inapplicability").is(":checked");
                if(chk){
                    $("#primeRatePlan").prop("disabled", true);
                } else {
                    $("#primeRatePlan").prop("disabled", false);
                }
                switch (openType) {
                    case "1":
                        $("#primeRatePlan option[value=1]").prop("disabled", false);
                        $("#primeRatePlan option[value=2]").prop("disabled", false);
                        break;
                    case "2":
                        $("#primeRatePlan option[value=1]").prop("disabled", true);
                        $("#primeRatePlan option[value=2]").prop("disabled", true);
                        break;
                    default:
                        $("#primeRatePlan").val("").prop("disabled", true);
                }
                if(openType == "2") {
                    $("#primeRatePlan option[value=3]").text("本行美元二十億元優惠利率");
                } else {
                    $("#primeRatePlan option[value=3]").text("本行競爭性利率");
                }
            }
        },
        chkForPrimeRate_700e: function(openType, readonly){
            // J-110-0399_05097_B1001 Web eloan額度明細表登錄新台幣利率條件增加「適用新台幣七百億優惠利率檢核」及「適用新台幣競爭性利率檢核」檢核功能
//        	1.借款人之企業模型評等(含各類模型評等)等級8級(含)以上、企業資信評等C級（含）以上，或提供十足擔保之授信案件。--本項條件由系統直接判斷；若不符合，則直接顯示不符合
//            2.符合前述(1)且利潤貢獻度需大於等於0.75%，否則直接顯示不符合，其相關說明如下:
//               (1)利潤貢獻度計算:分子：最近一年客戶利潤(提供系統引進--可開放維護) / 分母：適用額度--客戶可適用七百億優惠利率之短期額度合計數(由分行輸入)
//               (2)利潤貢獻度大於等於1%時，提示符合可動用適用額度之100%。
//               (3)利潤貢獻度不足1%但大於等於0.75%時，提示符合可動用適用額度之50%。
        	
        	$.ajax({
                handler: inits.fhandle,
                action: "chkForPrimeRate700e",
                data: {
                	rptMainId : responseJSON.mainId,
                    tabFormMainId: $("#tabFormMainId").val() 
                },
                success: function(obj){
                    if(obj.isOk == "N"){
                    	//借款人之企業模型評等(含各類模型評等)等級8級(含)以上、企業資信評等C級（含）以上，或提供十足擔保之授信案件。--本項條件由系統直接判斷；若不符合，則直接顯示不符合
                    	return API.showMessage(obj.showMsg);   //不符合
                    }else{
                    	//空白代表初步檢核通過，後續還要再判斷利潤貢獻度
                    	//開啟利潤貢獻度輸入畫面
                    	$("#chkForPrimeRate700eForm").find("#chkForPrimeRate700eMsg1").val(obj.showMsg);
                    	$("#chkForPrimeRate700eForm").find("#chkForPrimeRate700eAmt1").val(obj.chkForPrimeRate700eAmt1);   //最近一年客戶利潤貢獻
                    	$("#chkForPrimeRate700eForm").find("#chkForPrimeRate700eBgnDate").val(obj.chkForPrimeRate700eBgnDate);
                    	$("#chkForPrimeRate700eForm").find("#chkForPrimeRate700eEndDate").val(obj.chkForPrimeRate700eEndDate);
                    	
                    	$("#chkForPrimeRate700eBox").thickbox({
                            title: "適用新台幣七百億優惠利率之檢核條件，須符合下列條件",
                            width: 650,
                            height: 320,
                            align: "center",
                            valign: "bottom",
                            readOnly: _openerLockDoc == "1",
                            i18n: i18n.def,
                            buttons: {
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });              	
                    }
                }//close success function
            });//close ajax
   
        },
        chkForPrimeRate700eCompute: function(openType, readonly){
            // J-110-0399_05097_B1001 Web eloan額度明細表登錄新台幣利率條件增加「適用新台幣七百億優惠利率檢核」及「適用新台幣競爭性利率檢核」檢核功能
        	
        	var $inputform = $("#chkForPrimeRate700eForm");
        	if (!$inputform.valid()) {
                return false;
            }
        	
        	$("#chkForPrimeRate700eForm").find("#chkForPrimeRate700eAmt3").val("");
            $("#chkForPrimeRate700eForm").find("#chkForPrimeRate700eMsg2").val(""); 
            
        	$.ajax({
                handler: inits.fhandle,
                action: "chkForPrimeRate700eCompute",
                data: {
                	rptMainId : responseJSON.mainId,
                    tabFormMainId: $("#tabFormMainId").val(), 
                    chkForPrimeRate700eAmt1:$("#chkForPrimeRate700eForm").find("#chkForPrimeRate700eAmt1").val(),
                    chkForPrimeRate700eAmt2:$("#chkForPrimeRate700eForm").find("#chkForPrimeRate700eAmt2").val()
                },
                success: function(obj){
                	if( obj.hasError == "Y"){
                    	return API.showMessage(obj.chkForPrimeRate700eMsg2);
                    }else{
                    	$("#chkForPrimeRate700eForm").find("#chkForPrimeRate700eAmt3").val(obj.chkForPrimeRate700eAmt3);
                        $("#chkForPrimeRate700eForm").find("#chkForPrimeRate700eMsg2").val(obj.chkForPrimeRate700eMsg2); 
                    }
                	 
                }//close success function
        	});//close ajax 
        },
        chkForPrimeRate_competitive: function(openType, readonly){
            // J-110-0399_05097_B1001 Web eloan額度明細表登錄新台幣利率條件增加「適用新台幣七百億優惠利率檢核」及「適用新台幣競爭性利率檢核」檢核功能
        	// 適用新台幣競爭性利率之檢核條件：借款人之企業之大型企業模型評等等級8級（含）、其餘企業模型評等等級9級（含）以上、企業資信評等級C級(含)以上，或提供十足擔保之授信案件。--本項條件由系統直接判斷；若不符合，則直接顯示不符合
        	$.ajax({
                handler: inits.fhandle,
                action: "chkForPrimeRateCompetitive",
                data: {
                	rptMainId : responseJSON.mainId,
                    tabFormMainId: $("#tabFormMainId").val() 
                },
                success: function(obj){
                    return API.showMessage(obj.showMsg);
                }//close success function
            });//close ajax
        }
		
        
    };
    
}
initAll.done(function(inits){
    dfd41.done(gridviewRate);//利費率
    dfd42.done(initSelect);//下拉選單
    dfd43.done(gridviewRate02);
	dfd44.done(gridviewQueryRateBase);
    //====================button event ================================
    $("#usdMarket").change(function(){
        RateAction.setMarketRate($(this).val());
    });
    
    /**
     * 雜幣選項
     */
    $("#selectOtherBase").change(function(e, init){
        var value = $(this).val();
        if (!init) {
            $("#rateBase").val(value);
        }
        
        RateAction.showForOthRule();
    });
    /**
     * 與借款人敘作之市場利率代碼
     * 是否以借款同天期顯示文字選【是】的時候，不用選  (A)與借款人敘作之市場利率
     */
    var $usdMarketRateTr = $("#usdMarketRateTr");
    $("#usdSetAll").change(function(){
        if ($(this).val() == "Y") {
            $usdMarketRateTr.hide();
            $("#usdMarketRate").val("");
        }
        else {
            $usdMarketRateTr.show();
            RateAction.setMarketRate($("#usdMarket").val());
        }
    });
    /**
     * 美金自定利率稅賦負擔
     */
    $("#usdRateTax").change(function(){
        if ($(this).val() == "1") {
            $("#usdRateTaxSpan").show();
        }
        else {
            $("#usdRateTaxSpan").hide();
        }
    });
    /**
     * 稅賦負擔
     */
    $("#rateTax").change(function(){
        if ($(this).val() == "1") {
            $("#rateTaxSpan").show();
        }
        else {
            $("#rateTaxSpan").hide();
        }
    });
	/**
	 * 俟帳務系統起帳時再依客戶繳息方式建入
     * J-105-0187-001 Web e-Loan 國內企金授信結構化利率扣稅負擔碼新增選項由帳務系統建入
     */
    $("#rateTaxCodeDecideFuture").change(function(){
		
        var rateTaxCodeDecideFuture = $('input:checkbox:checked[name="rateTaxCodeDecideFuture"]').val()
        if (rateTaxCodeDecideFuture == "Y") {
            $("#rateTaxCode").hide();
			$("#rateTaxCode").val("");
        }
        else {
            $("#rateTaxCode").show();
        }
    });
    /**
     * 下限利率稅賦負擔
     */
    $("#rateLimitTax").change(function(){
        if ($(this).val() == "1") {
            $("#rateLimitTaxSpan").show();
        }
        else {
            $("#rateLimitTaxSpan").hide();
        }
    });
    
    /**
     * 下限利率稅賦負擔
     */
    //J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率
    $("#rateLimitTax2").change(function(){
        if ($(this).val() == "1") {
            $("#rateLimitTaxSpan2").show();
        }
        else {
            $("#rateLimitTaxSpan2").hide();
        }
    });
    
    /**雜幣的 @1的 加/減年利率 **/
    var $disYearRateSpanOth = $("#disYearRateSpanOth");
    $("#disYearOpOth").change(function(){
        if ($(this).val() == "") {
            $disYearRateSpanOth.hide();
            $disYearRateSpanOth.find("#disYearRateOth").val("");
        }
        else {
            $disYearRateSpanOth.show();
        }
    });
    
    /** 加/減年利率 **/
    var $disYearRateSpan2 = $("#disYearRateSpan2");
    $("#disYearOp2").change(function(){
        if ($(this).val() == "") {
            $disYearRateSpan2.hide();
            $disYearRateSpan2.find("#disYearRate2").val("");
        }
        else {
            $disYearRateSpan2.show();
        }
    });
    
    
    /** 當加/減年利率 要重算出其目前利率**/
    var $disYearRateSpan = $("#disYearRateSpan");
    $("#disYearOp").change(function(){
        if ($(this).val() == "") {
            $disYearRateSpan.hide();
            $disYearRateSpan.find("#disYearRate").val("");
        }
        else {
            $disYearRateSpan.show();
        }
        RateAction.calcRate();
    });
    /** 限制條件/說明 當加/減年利率 為空白則不需輸入**/
    var $rateLimitCountRateSpan1_2 = $("#rateLimitCountRateSpan1_2");
    $("#rateLimitCountType").change(function(){
        if ($(this).val() == "") {
            $rateLimitCountRateSpan1_2.hide();
            $rateLimitCountRateSpan1_2.find("#rateLimitCountRate").val("");
        }
        else {
            $rateLimitCountRateSpan1_2.show();
        }
    });
    
    //J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率
    /** 限制條件/說明 當加/減年利率 為空白則不需輸入**/
    var $rateLimitCountRateSpan2_2 = $("#rateLimitCountRateSpan2_2");
    $("#rateLimitCountType2").change(function(){
        if ($(this).val() == "") {
            $rateLimitCountRateSpan2_2.hide();
            $rateLimitCountRateSpan2_2.find("#rateLimitCountRate2").val("");
        }
        else {
            $rateLimitCountRateSpan2_2.show();
        }
    });
    
    
    /** 當加減年率欄位失去焦點後 要算出其目前利率**/
    $("#disYearRate").blur(function(){
        RateAction.calcRate();
    });
    
    /** 限制條件/說明 */
    $("#rateLimitType").change(function(e, data){
        var $rateLimitTypeTable = $("#rateLimitTypeTable");
        $rateLimitTypeTable.hide();
        if ($(this).val() == "13") {
            $rateLimitTypeTable.show();
			
			//J-105-0088-001 Web e-Loan 企金授信系統利率條件無下限利率時，新增a-Loan是否需建置下限利率欄位並上傳a-Loan檢核。 
			$("#rateLimitNeedBuild").val('');
            $("#rateLimitCountRateSpan1_1,#rateLimitCountPrSpan,#rateLimitTaxSpan,#showRateLimitNeedBuild").hide();
            
            //J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率
            RateAction.showSecondLimitRate == "Y" ? $(".showRateLimit2").show() : $(".showRateLimit2").hide();
        }else{
			
			//J-105-0088-001 Web e-Loan 企金授信系統利率條件無下限利率時，新增a-Loan是否需建置下限利率欄位並上傳a-Loan檢核。 
			$("#showRateLimitNeedBuild").show();
			//J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率
			//清除第二組下限利率(要有第一組才能有第二組)
			var $rateLimitTypeTable2 = $("#rateLimitTypeTable2");
			$rateLimitTypeTable2.find("select,input:text").each(function(){
                if (this.nodeName == "SELECT") {
                    $(this).val($(this).find("option").first().val());
                }
                else {
                    $(this).val("");
                }
            });
			$("#rateLimitType2 option[value='0']").prop("selected", true); //設置Select的Text值為jQuery的項選中
			$("#rateLimitType2").change();
			$(".showRateLimit2").hide();
			
		}
			
        if (!data) {
            //初始化欄位
            $rateLimitTypeTable.find("select,input:text").each(function(){
                if (this.nodeName == "SELECT") {
                    $(this).val($(this).find("option").first().val());
                }
                else {
                    $(this).val("");
                }
            });
        }
        
    });
    
    
    //J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率
    /** 限制條件/說明 */
    $("#rateLimitType2").change(function(e, data){
        var $rateLimitTypeTable2 = $("#rateLimitTypeTable2");
        $rateLimitTypeTable2.hide();
        if ($(this).val() == "13") {
            $rateLimitTypeTable2.show();
			$("#rateLimitCountRateSpan2_1,#rateLimitCountPrSpan2,#rateLimitTaxSpan2").hide();
			
        }
			
        if (!data) {
            //初始化欄位
            $rateLimitTypeTable2.find("select,input:text").each(function(){
                if (this.nodeName == "SELECT") {
                    $(this).val($(this).find("option").first().val());
                }
                else {
                    $(this).val("");
                }
            });
        }
        
    });
  
    /**
     * all-in 除扣稅負擔碼
     */
    $("#caculateTaxCode").click(function(){
        RateAction.calcRateAllIn();
    });
	 /**
     * 比較all-in最小試算利率
     */
    $("#compareAllIn").click(function(){
        RateAction.compareAllIn();
    });
	 /**
     * 比較all-in最高試算利率
     */
    $("#compareAllInMax").click(function(){
        RateAction.compareAllInMax();
    });    
    /**
     * 組合文字時顯示此項目
     */
    $("#reRateSelAll").click(function(){
        RateAction.calcRate();
    });
    
    /**
     * 重新引進利率
     */
    $("#reoladtRate").click(function(){
        RateAction.reoladtRate();
    });
    
    /**  新增利率  */
    $("#newRateBt").click(function(){
        RateAction.newRate(null, null, "");
    });
    $("#rateBtSpan").find("#twBT").click(function(){
        //利費率登錄新台幣
        RateAction.openRateBox("1");
    }).end().find("#usBT").click(function(){
        //利費率登錄美金
        RateAction.openRateBox("2");
    }).end().find("#jpBT").click(function(){
        //利費率登錄日幣
        RateAction.openRateBox("3");
    }).end().find("#euBT").click(function(){
        //利費率登錄歐元
        RateAction.openRateBox("4");
    }).end().find("#cnBT").click(function(){
        //人民幣
        RateAction.openRateBox("5");
    }).end().find("#auBT").click(function(){
        //澳幣
        RateAction.openRateBox("6");
        
    }).end().find("#hkBT").click(function(){
        //港幣
        RateAction.openRateBox("7");
        
    }).end().find("#otherBT").click(function(){
        //利費率登錄雜幣
        RateAction.openRateBox("Z");
    }).end().find("#rateBT").click(function(){
        //利費率登錄費率	
        openRateBox2();
    });
    
    /**
     * 修改最大利率
     */
    $("#tRateMaxEdit").click(function(){
        RateAction.enterRateBox("max");
    });
    
    /**
     * 修改最小利率
     */
    $("#tRateMinEdit").click(function(){
        RateAction.enterRateBox("min");
    });
    
    
    $("#rateBox2").find("#businessBT").click(function(){//利費率商業本票保證
        openRateChildrenBox('cpType');
    }).end().find("#promiseBT").click(function(){//利費率開發保證函
        openRateChildrenBox('cfType');
    }).end().find("#companyBT").click(function(){//利費率承兌費率
        openRateChildrenBox('cpyType');
    }).end().find("#thickRateBT").click(function(){//利費率登入新台幣
        openRateChildrenBox('paType');
    });
    
    /**
     * 選擇利率基礎
     */
    $("#loginRateBase").click(function(){
        RateAction.openRateBaseBox();
    });
    
    /**
     * 查詢利率基礎
     */
    $("#queryRateBase").click(function(){
        RateAction.openQueryRateBaseBox();
    });
	/**
     * 查詢All-in利率基礎
     */
    $("#selAllInRateBase").click(function(){
        RateAction.openAllInSelRateBaseBox();
    });
	
	/**
     * 引進目前利率
     */
    $("#selCurrentRate").click(function(){
        RateAction.applyCurrentRate();
    });
	/**
     * 引進下限利率
     */
    $("#selLimitRate").click(function(){
        RateAction.applyLimitRate();
    });
	
    $("#secNo").change(function(){
        if ($(this).val() == "0") {
            $("#secNoOp").val("1").prop("disabled", true).trigger("change");
        }
        else {
            $("#secNoOp").removeAttr("disabled");
        }
    });
    /**  更新gird  */
    $("#lms140Tab04").click(function(){
        dfd41.resolve();//打開利費率的grid
        dfd43.resolve();//打開利率的grid
        dfd44.resolve();//打開利率的grid
        $("#gridviewRate").jqGrid("setGridParam", {
            sortname: 'createTime',
            sortorder: 'asc',
            postData: {
                formAction: "queryL140m01f",
                tabFormMainId: $("#tabFormMainId").val(),
                noOpenDoc: true
            },
            search: true
        }).trigger("reloadGrid");
        
        //加入前面登錄的授信科目
        $.ajax({
            handler: inits.fhandle,
            action: "queryL140m01c",
            data: {
                tabFormMainId: $("#tabFormMainId").val(),
                noOpenDoc: true
            },
            success: function(obj){
                //刪除原本的項目
                $("#itemSpan_loanTPList").remove();
                $("#loanTPListLocal").html("<input type='checkbox' id='loanTPList' name='loanTPList' value='' />");
                //帶入現請額度目前的幣別
                $("#moneyNowSapn").html($("#currentApplyCurr").val());
                $("#itemShowTr").html("");
                if (obj.count != "0") {
                    $("input[name=loanTPList]").setItems({//利費率第一頁籤授信科目checkbok
                        item: obj.item
                    });
                    $("#itemShowTr").html(obj.str);
                }//close if
            }//close success function
        });//close ajax
    });
    //判斷清除費率組字按鈕是否出現 ，當利費率grid為空 且性質為不變
    $("#tab04_2a").click(function(){
        var countGrid = $("#gridviewRate").jqGrid('getGridParam', 'records');
        if (countGrid == 0 && $("#proPerty").val() == "7" && !inits.toreadOnly) {
            $("#cleanRateDrcLink").show();
        }
        else {
            $("#cleanRateDrcLink").hide();
        }
    });
    
    
    /** 組成利費率字串  */
    $("#tabRateDrcLink").click(function(){
        $.ajax({
            handler: inits.fhandle,
            action: "saveDscr2",//儲存所有利費率描述
            data: {//把資料轉成json
                tabFormMainId: $("#tabFormMainId").val(),
                pageNum: $("#pageNum2").val(),
                showMsg: true
            },
            success: function(obj){
                RateAction.setPage2RateDrc(obj.drc);
            }
        }); //close ajax
    });
    /** 清除利費率字串  */
    $("#cleanRateDrcLink").click(function(){
        RateAction.setPage2RateDrc("");
    });
    
    
    
    /**
     *  幣別組成文字
     */
    $("#toRateWordBT").click(function(){
        RateAction.saveL140M01N(false);
    });
    
    /**
     * 清除幣別組成文字
     */
    $("#cleanRateBT").click(function(){
        RateAction.cleanRateWord();
    });
    
    $("#wordBase").click(function(){
        //L140M01h.say=L/C有效期跟已承兌期間重疊部份免收，承兌期間超過有效期者，按實際超過天數以年率XXX%計收，每筆最低計費為
        //other.money=元
        $("#paDes").val(i18n.lms1401s02["L140M01h.say"] + "400" + i18n.lms1401s02["other.money"]);
    });
    
    /**  清除[費率]欄位內容  */
    $("#clearRate").click(function(){
        $("#" + RateAction.RateFormId).reset();
        $(".rateSpan1,.rateSpan2,.rateSpan3,.rateSpan4").hide();
    });
    
    /** 刪除利費率登錄  */
    $("#removeRate").click(function(){
        var gridID = $("#gridviewRate").getGridParam('selarrrow');
        if (gridID == "") {
            //TMMDeleteError=請先選擇需修改(刪除)之資料列
            return API.showMessage(i18n.def["TMMDeleteError"]);
            
        }
        //confirmDelete=是否確定刪除?
        API.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var gridIDList = [], gridSeqList = [];
                for (var i = 0; i < gridID.length; i++) {
                    gridIDList[i] = $("#gridviewRate").getRowData(gridID[i]).oid;
                    gridSeqList[i] = $("#gridviewRate").getRowData(gridID[i]).rateSeq;
                }
                $.ajax({
                    handler: inits.fhandle,
                    action: "deleteL140m01f",
                    data: {
                        tabFormMainId: $("#tabFormMainId").val(),
                        Idlist: gridIDList,
                        Seqlist: gridSeqList
                    },
                    success: function(obj){
                        if (obj && obj.drc) {
                            RateAction.setPage2RateDrc(obj.drc);
                        }
                        RateAction.reloadGird();
                    }
                });
            }
        });
    });
    /**
     * 刪除利率
     */
    $("#deleteL140M01N").click(function(){
        var $grid = $("#gridviewRate02");
        var rows = $grid.getGridParam('selarrrow');
        var data = [];
        if (rows.length == 0) {
            // TMMDeleteError=請先選擇需修改(刪除)之資料列
            return API.showMessage(i18n.def["TMMDeleteError"]);
        }
        //confirmDelete=是否確定刪除?
        API.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                for (var i in rows) {
                    data.push($grid.getRowData(rows[i]).oid);
                }
                $.ajax({
                    handler: inits.fhandle,
                    action: "deleteL140M01N",
                    data: {
                        tabFormMainId: $("#tabFormMainId").val(),
                        oids: data
                    },
                    success: function(obj){
                        if (obj && obj.drc) {
                            RateAction.setPage2RateDrc(obj.drc);
                        }
                        $grid.trigger("reloadGrid");
                    }
                });
            }
        });
    });
    
    /**
     * 複製利率
     */
    $("#copyL140M01N").click(function(){
        var $grid = $("#gridviewRate02");
        var rows = $grid.getGridParam('selarrrow');
        var data = [];
        if (rows.length == 0) {
            //grid_selector=請選擇資料
            return API.showMessage(i18n.def["grid_selector"]);
        }
        if (rows.length > 1) {
            //L140M01a.message110=此功能只能選擇單筆
            return API.showMessage(i18n.lms1401s02["L140M01a.message110"]);
        }
        for (var i in rows) {
            data.push($grid.getRowData(rows[i]).oid);
        }
        $.ajax({
            handler: inits.fhandle,
            action: "copyL140M01N",
            data: {
                tabFormMainId: $("#tabFormMainId").val(),
                oids: data
            },
            success: function(obj){
                RateAction.openRateBox(null, null, {
                    oid: data[0]
                });
                $grid.trigger("reloadGrid");
            }
        });
    });
    /**
     * 組成字串
     */
    $("#rateToWordBt").click(function(){
        rateToWord();
    });
    
    $("#wordBaseBt2").click(function(){
        //L140M01h.pa2Rate=費率、L140M01h.wordBase2=依本行規定計收
        $("#othDes").val("ＸＸ" + i18n.lms1401s02["L140M01h.pa2Rate"] + "：" + i18n.lms1401s02["L140M01h.wordBase2"]);
    });
    
    
    $("#theWordBt").click(function(){
        theWordBt();
    });

    /**
     * J-109-0179_09301_B1001 不得適用「本行各項優惠利率」
     */
    $("#inapplicability").click(function(){
        if ($("#inapplicability").is(":checked")) {
            $("#primeRatePlan").val("").prop("disabled", true);
        } else {
            $("#primeRatePlan").val("").prop("disabled", false);
            RateAction.showPrimeRatePlan(RateAction.openType, (_openerLockDoc == "1" || inits.toreadOnly));
        }
    });
    
    
    /**
     * 適用新台幣七百億優惠利率檢核
     * J-110-0399_05097_B1001 Web eloan額度明細表登錄新台幣利率條件增加「適用新台幣七百億優惠利率檢核」及「適用新台幣競爭性利率檢核」檢核功能
     */
    $("#chkForPrimeRate_700e").click(function(){
        RateAction.chkForPrimeRate_700e();
    });
    
    /**
     * 計算七百億優惠利率檢核
     * J-110-0399_05097_B1001 Web eloan額度明細表登錄新台幣利率條件增加「適用新台幣七百億優惠利率檢核」及「適用新台幣競爭性利率檢核」檢核功能
     */
    $("#chkForPrimeRate700eCompute").click(function(){
        RateAction.chkForPrimeRate700eCompute();
    });
    
    /**
     * 適用新台幣競爭性利率檢核
     * J-110-0399_05097_B1001 Web eloan額度明細表登錄新台幣利率條件增加「適用新台幣七百億優惠利率檢核」及「適用新台幣競爭性利率檢核」檢核功能
     */
    $("#chkForPrimeRate_competitive").click(function(){
        RateAction.chkForPrimeRate_competitive();
    });
    
    
    
    
    
    //====================button event END ========================
    
    //====================Grid Cod ================================
    /**  利費率grid  */
    function gridviewRate(){
        $("#gridviewRate").iGrid({//利費率grid
            handler: inits.ghandle,
            height: 200,
            rownumbers: true,
            multiselect: true,
            rowNum: 10,
            hideMultiselect: false,
            sortname: 'createTime|rateSeq',
            sortorder: 'asc|asc',
            action: "queryL140m01f",
            postData: {
                tabFormMainId: $("#tabFormMainId").val(),
                noOpenDoc: true
            },
            autowidth: true,
            colModel: [{
                colHeader: i18n.lms1401s02["L782M01A.loanTP"],//"科目",
                name: 'loanTPList',
                align: "left",
                width: 300,
                sortable: true,
                //在LMS1405s02panel02.js的格式化function
                formatter: "click",
                onclick: RateAction.newRate
            }, {
                colHeader: i18n.lms1401s02["L140M01g.rate"],//"利率",
                name: 'rateDscr',
                width: 500,
                sortable: false,
                formatter: 'click',
                onclick: RateAction.newRate
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }, {
                name: 'rateSeq',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridviewRate").getRowData(rowid);
                RateAction.newRate(null, null, data);
            }
        });
    }//close gridviewRate fn();
    /**  利費率grid  */
    function gridviewRate02(){
        $("#gridviewRate02").iGrid({//利費率grid
            handler: inits.ghandle,
            height: 230,
            rownumbers: true,
            multiselect: true,
            rowNum: 10,
            hideMultiselect: false,
            shrinkToFit: false,
            colModel: [{
                colHeader: i18n.lms1401s02["L782M01A.applyCurr"],//幣別,
                name: 'rateType',
                align: "left",
                width: 60,
                sortable: true,
                formatter: "click",
                onclick: RateAction.openRateBox
            }, {
                colHeader: i18n.lms1401s0204["l1401s02p04.004"],//分段,
                name: 'secNo',
                width: 60,
                sortable: true,
                formatter: 'click',
                onclick: RateAction.openRateBox
            }, {
                colHeader: i18n.lms1401s0204["l1401s02p04.007"],//適用期間,
                name: 'secNoOp',
                width: 150,
                sortable: true,
                formatter: 'click',
                onclick: RateAction.openRateBox
            }, {
                colHeader: i18n.lms1401s02["L140M01g.rate"],//利率,
                name: 'rateDscr',
                width: 300,
                sortable: true,
                formatter: 'click',
                onclick: RateAction.openRateBox
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }, {
                name: 'rateSeq',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridviewRate02").getRowData(rowid);
                RateAction.openRateBox(null, null, data);
            }
        });
    }//close gridviewRate fn();
	
	/**  查詢利率基礎Grid */
    function gridviewQueryRateBase(){
 
        $("#gridviewQueryRateBase").iGrid({
            handler: inits.ghandle,
            sortorder: 'asc',
            sortname: "LR_CODE",
			needPager: false,
            height: 230,
            multiselect: true,
            hideMultiselect: false,
			localFirst: true,
            postData: {
                formAction: "queryMisLnratByCurr",
                tabFormMainId: $("#tabFormMainId").val()
            },
            colModel: [{
                colHeader:i18n.lms1401s0204["l1401s02p04.053"],//利率代碼
                name: 'LR_CODE',
                align: "center",
                width: 20,
                sortable: false
            }, {
                colHeader: i18n.lms1401s0204["l1401s02p04.054"],//利率名稱
                name: 'LR_RATE_CNAME',
                align: "left",
                width: 60,
                sortable: false
            }, {
                colHeader: i18n.lms1401s0204["l1401s02p04.055"],//掛牌利率
                name: 'LR_RATE',
                align: "right",
                width: 20,
                sortable: false
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                 
            }
        });
		
		
		$("#gridviewQueryRateBaseAllIn").iGrid({
            handler: inits.ghandle,
            sortorder: 'asc',
            sortname: "LR_CODE",
			needPager: false,
            height: 230,
            multiselect: true,
            hideMultiselect: false,
			localFirst: true,
            postData: {
                formAction: "queryMisLnratByCurr",
                tabFormMainId: $("#tabFormMainId").val()
            },
            colModel: [{
                colHeader:i18n.lms1401s0204["l1401s02p04.053"],//利率代碼
                name: 'LR_CODE',
                align: "center",
                width: 20,
                sortable: false
            }, {
                colHeader: i18n.lms1401s0204["l1401s02p04.054"],//利率名稱
                name: 'LR_RATE_CNAME',
                align: "left",
                width: 60,
                sortable: false
            }, {
                colHeader: i18n.lms1401s0204["l1401s02p04.055"],//掛牌利率
                name: 'LR_RATE',
                align: "right",
                width: 20,
                sortable: false
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                 
            }
        });
		
		
    }
    //====================Grid Code END ===========================
    
    
    /** 字串轉換 */
    function toConverWord(spanName){
        var _str = "";
        var stop = false;
        $(spanName).find("input:text,select,span.word,textarea").filter(function(){
            return !($(this).hasClass("intputX"));//當class=intputX就不組入字串內
        }).each(function(){
            _str = $.trim(_str);
            if (!stop) {
                switch (this.nodeName.toLowerCase()) {
                    case 'span':
                        _str += $(this).text();
                        break;
                    case 'select':
                        _str += $(this).find(":selected").text();
                        break;
                    case 'input':
                    case 'textarea':
                        if ($(this).val() == "") {
                            stop = true;
                        }
                        
                        if ($(this).attr("id") == "cp1Fee") {
                            _str += " " + $(this).val();
                        }
                        else {
                            _str += $(this).val();
                        }
                        break;
                }
            }
        });
        if (_str.match(/\($/)) {
            return _str.replace(/\(/g, "");
        }
        return _str;
    }
    
    //針對 [費率]內有X的select做隱藏或顯示
    $(".selectX").change(function(){
        var $thisId = $(this).attr("id");
        if ($(this).val() == "5") {
            $("#" + $thisId + "Select").show();
        }
        else {
            $("#" + $thisId + "Select").hide();
        }
    });
    
    
    
    
    
    
    
    function openRateBox2(){
        //檢查是否已經選擇科目
        if (!RateAction.checkItem()) {
            return false;
        }
        $("#" + RateAction.RateFormId).reset();
        $(".rateSpan1,.rateSpan2,.rateSpan3,.rateSpan4").hide();
        $.ajax({
            handler: inits.fhandle,
            action: "queryL140m01h",
            data: {
                rateSeq: RateAction.rateSeq,
                tabFormMainId: $("#tabFormMainId").val(),
                showMsg: true
            },
            success: function(obj){
                $("#" + RateAction.RateFormId).injectData(obj);
                $("#radioSpan1").find(".rateSpan" + obj.cpType).show();
                $("#radioSpan2").find(".rateSpan" + obj.cfType).show();
                $("#radioSpan3").find(".rateSpan" + obj.cpyType).show();
                $("#radioSpan4").find(".rateSpan" + obj.paType).show();
            }
        });
        
        $("#rateBox2").thickbox({
            // title.17=登錄費率
            title: i18n.lms1401s02["title.17"],
            width: 920,
            height: 500,
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            modal: true,
            buttons: {
                "saveData": function(){
                    if (!$("#" + RateAction.RateFormId).valid()) {
                        //common.001=欄位檢核未完成，請填妥後再送出
                        API.showMessage(i18n.def['common.001']);
                        return false;
                    }
                    rateToWord();//在按確定的時候幫他組字串
                    FormAction.open = true;
                    $.ajax({
                        handler: inits.fhandle,
                        action: "saveL140m01h",
                        formId: [RateAction.RateMainFormId, RateAction.RateFormId],
                        data: {//把資料轉成json
                            rateId: RateAction.rateId,
                            rateSeq: RateAction.rateSeq,
                            tabFormMainId: $("#tabFormMainId").val(),
                            showMsg: true
                        },
                        success: function(obj){
                            FormAction.open = false;
                            RateAction.rateId = obj.oid;
                            RateAction.setRateSeq(obj.rateSeq);
                            $("#RateDrc").val(obj.RateDrc);
                            RateAction.reloadGird();
                            if (obj && obj.drc) {
                                RateAction.setPage2RateDrc(obj.drc);
                            }
                            $.thickbox.close();
                        }
                    });
                    
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    function openRateChildrenBox(type){//顯示出現哪一種thickbox
        var rateType = '';
        switch (type) {
            case "cpType":
                //L140M01h.cpType=商業本票
                rateTitleName = i18n.lms1401s02["L140M01h.cpType"];
                rateType = "1";
                break;
            case "cfType":
                //L140M01h.cfType=開發保證函
                rateTitleName = i18n.lms1401s02["L140M01h.cfType"];
                rateType = "2";
                break;
            case "cpyType":
                //L140M01h.cpyType=公司債保證
                rateTitleName = i18n.lms1401s02["L140M01h.cpyType"];
                rateType = "3";
                break;
            case "paType":
                //L140M01h.paType=承兌費率
                rateTitleName = i18n.lms1401s02["L140M01h.paType"];
                rateType = "4";
                break;
        }
        $("#rateChildrenBox" + rateType).show().siblings("table").hide();
        $("#rateChildrenBox").thickbox({
            title: rateTitleName,
            width: 720,
            height: 250,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    //cpType-商業本票保證,cfType-開發保證函,cpyType-公司債保證,paType-承兌費率
                    var radioVal = $("[name =" + type + "]:checked").val();
                    if (radioVal != 'c') {
                        $("#radioSpan" + rateType + " .rateSpan" + radioVal).show().siblings().hide().find(":input,select").val("");
                    }
                    else {
                        $("#radioSpan" + rateType).find("[class^=rateSpan]").hide().find(":input,select").val("");
                    }
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    
    /**
     * 帶入下拉選單值
     */
    function initSelect(){
        var item = API.loadCombos(["lms1405s0204_monType", "lms1405s0204_monType2", "lms1405s0204_rateKind", "lms1401s0204_secNoOp", "lms1401s0204_rateGetInt", "lms1401s0204_primeRatePlan"]);
        //[費率]預收方式 for 承兌
        $(".lms1405s0204_monType").setItems({
            space: true,
            item: item.lms1405s0204_monType,
            format: "{key}"
        });
        
        //[費率]預收方式2 for開發
        $(".lms1405s0204_monType2").setItems({
            space: true,
            item: item.lms1405s0204_monType2,
            format: "{key}"
        });
        //[利率] 利率方式
        $("#rateKind").setItems({
            space: true,
            item: item.lms1405s0204_rateKind,
            format: "{key}",
            fn: function(v, k){
                if ($("#rateKind").val() == "3") {
                    $("#rateChgKindTr").show();
                }
                else {
                    $("#rateChgKindTr").hide();
                }
            }
        });
        //[利率] 收息方式
        $("#rateGetInt").setItems({
            space: true,
            item: item.lms1401s0204_rateGetInt,
            format: "{value} {key}"
        });
        
        
        
        //[利率] 段數
        var tempHtml = "";
        tempHtml += "<option value=''>" + i18n.def.comboSpace + "</option>";
        //l1401s02p04.018=全
        tempHtml += "<option value='0'>" + i18n.lms1401s0204["l1401s02p04.018"] + "</option>";
        for (var i = 1; i <= 20; i++) {
            tempHtml += "<option value=" + i + ">" + i + "</option>";
        }
        $("#secNo").html(tempHtml);
        //[利率]適應期間
        $("#secNoOp").setItems({
            space: true,
            item: item.lms1401s0204_secNoOp,
            format: "{key}",
            fn: function(e, data){
                var $DateSpan = $("#secDateSpan");
                var $MonSpan = $("#secMonSpan");
                if (!data) {
                    $DateSpan.hide().find("input").val("");
                    $MonSpan.hide().find("input").val("");
                }
                var value = $(this).val();
                
                switch (value) {
                    case "2":
                    case "4":
                        $MonSpan.show();
                        break;
                    case "3":
                    	$DateSpan.show();
                    	//J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
                    	$("#secBegDateSpan").show();
                    	$("#secEndDateSpan").show();
                        break
                    case "5":
                    case "6":
                    	//J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
                        //自動用日起至迄日("5"), 自簽約日起至迄日("6")
                        $DateSpan.show();
                        $("#secBegDateSpan").hide();
                        $("#secEndDateSpan").show();
                        break
                    case "7":
                    	//J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
                        //自動用日起至迄日("5"), 自簽約日起至迄日("6"), YYYYMMDD至迄日("7")
                        $DateSpan.show();
                        $("#secBegDateSpan").show();
                        $("#secEndDateSpan").hide();
                        break    
                    default:
                        break;
                }
            }
        });

        // J-109-0179_09301_B1001 適用優惠利率
        $("#primeRatePlan").setItems({
            space: true,
            item: item.lms1401s0204_primeRatePlan,
            format: "{key}"
        });
        txtPrimeRatePlan3 = $("#primeRatePlan option[value=3]").text();
        var text4 = $("#primeRatePlan option[value=4]").text();
        $("#primeRatePlan option[value=4]").text(text4+"(不建議選用)");
        
    }
    
    
    
    
    function rateToWord(){//組費率字串  
        var AItemVal = $("[name=cpType]:checked").val();//商業本票保證的值
        var BItemVal = $("[name=cfType]:checked").val();//開發保證函的值
        var CItemVal = $("[name=cpyType]:checked").val();//公司債保證的值
        var DItemVal = $("[name=paType]:checked").val();//承兌費率 的值
        var other = $("#othDes").val();//其他
        var str = '';
        var sign = ",";
        var sign2 = "。";
        var sign3 = "－";
        if (AItemVal && AItemVal != "c") {//當不是空值並且非clear 才組字串
            var Aword = toConverWord("#radioSpan1" + " .rateSpan" + AItemVal);
            
            if (Aword != "") {
                //L140M01h.cpType=商業本票保證
                str = i18n.lms1401s02["L140M01h.cpType"] + sign3 + Aword + sign2;
            }
            
            $("#cpDes").val(Aword);
        }
        if (BItemVal && BItemVal != "c") {//當不是空值並且非clear 才組字串
            var Bword = toConverWord("#radioSpan2" + " .rateSpan" + BItemVal);
            if (Bword != "") {
                //L140M01h.cfType=開發保證函
                str = str + i18n.lms1401s02["L140M01h.cfType"] + sign3 + Bword + sign2;
            }
            
            switch (BItemVal) {
                case '1':
                    if ($("#cf1MD").val() == '5') {
                        str = str.replace("X", $("#cf1Mon2").val());
                        $("#cfDes").val(Bword.replace("X", $("#cf1Mon2").val()));
                    }
                    break;
                case '2':
                    if ($("#cf2MD").val() == '5') {
                        str = str.replace("X", $("#cf2Mon").val());
                        $("#cfDes").val(Bword.replace("X", $("#cf2Mon").val()));
                    }
                    break;
            }
            
        }
        if (CItemVal && CItemVal != "c") {//當不是空值並且非clear 才組字串
            var Cword = toConverWord("#radioSpan3" + " .rateSpan" + CItemVal);
            
            if (Cword != "") {
                //L140M01h.cpyType=公司債保證
                str = str + i18n.lms1401s02["L140M01h.cpyType"] + sign3 + Cword + sign2;
            }
            switch (CItemVal) {
                case '1':
                    if ($("#cpy1MD").val() == '5') {
                        str = str.replace("X", $("#cpy1Mon2").val());
                        $("#cpyDes").val(Cword.replace("X", $("#cpy1Mon2").val()));
                    }
                    break;
                case '2':
                    if ($("#cpy2MD").val() == '5') {
                        str = str.replace("X", $("#cpy2Mon").val());
                        $("#cpyDes").val(Cword.replace("X", $("#cpy2Mon").val()));
                    }
                    break;
            }
        }
        if (DItemVal && DItemVal != "c") {//當不是空值並且非clear 才組字串
            var Dword = toConverWord("#radioSpan4" + " .rateSpan" + DItemVal);
            if (Dword != "") {
                //L140M01h.paType=承兌費率
                str = str + i18n.lms1401s02["L140M01h.paType"] + sign3 + Dword + sign2;
            }
            switch (DItemVal) {
                case '1':
                    if ($("#pa1MD").val() == '5') {
                        str = str.replace("X", $("#pa1Mon").val());
                        $("#paDes").val(Dword.replace("X", $("#pa1Mon").val()));
                    }
                    break;
                case '2':
                    if ($("#pa2MD").val() == '5') {
                        str = str.replace("X", $("#pa2Mon").val());
                        $("#paDes").val(Dword.replace("X", $("#pa2Mon").val()));
                    }
                    break;
            }
            
        }
        if (other) {
            str = str + other + sign2;
        }
        
        $("#rateDscr2").val(str);
        return str;
    }
	
	
    
});
