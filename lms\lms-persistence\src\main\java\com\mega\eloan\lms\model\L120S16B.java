/* 
 * L120S16B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 主要申請敘作內容明細檔 **/
@NamedEntityGraph(name = "L120S16B-entity-graph", attributeNodes = { @NamedAttributeNode("l120s16a") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S16B", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "cntrNo", "type", "itemType" }))
public class L120S16B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件
	 * 
	 */
	@ManyToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "CUSTID", referencedColumnName = "CUSTID", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "DUPNO", referencedColumnName = "DUPNO", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "CNTRNO", referencedColumnName = "CNTRNO", nullable = false, insertable = false, updatable = false) })
	private L120S16A l120s16a;

	public L120S16A getL120s16a() {
		return l120s16a;
	}

	public void setL120s16a(L120S16A l120s16a) {
		this.l120s16a = l120s16a;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 本案授信戶統編
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 10)
	@Column(length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/**
	 * 本案授信戶重複序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 1)
	@Column(length = 1, columnDefinition = "CHAR(01)")
	private String dupNo;

	/**
	 * 本案授信戶額度序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	@Size(max = 12)
	@Column(length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/**
	 * 類別
	 * <p/>
	 * 1.本案授信戶<br/>
	 * 2.對照授信戶
	 */
	@Size(max = 1)
	@Column(name = "TYPE", length = 1, columnDefinition = "CHAR(01)")
	private String type;

	/**
	 * 項目類別
	 * <p/>
	 * 2利(費)率<br/>
	 * 3擔保品<br/>
	 * 4其他敘做條件
	 */
	@Size(max = 1)
	@Column(name = "ITEMTYPE", length = 1, columnDefinition = "CHAR(01)")
	private String itemType;

	/** 項目說明 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "ITEMDSCR", columnDefinition = "CLOB")
	private String itemDscr;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得本案授信戶統編
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCustId() {
		return this.custId;
	}

	/**
	 * 設定本案授信戶統編
	 * <p/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/**
	 * 取得本案授信戶重複序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getDupNo() {
		return this.dupNo;
	}

	/**
	 * 設定本案授信戶重複序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得本案授信戶額度序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCntrNo() {
		return this.cntrNo;
	}

	/**
	 * 設定本案授信戶額度序號
	 * <p/>
	 * 資料來源：額度明細表主檔
	 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/**
	 * 取得類別
	 * <p/>
	 * 1.本案授信戶<br/>
	 * 2.對照授信戶
	 */
	public String getType() {
		return this.type;
	}

	/**
	 * 設定類別
	 * <p/>
	 * 1.本案授信戶<br/>
	 * 2.對照授信戶
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/**
	 * 取得項目類別
	 * <p/>
	 * 2利(費)率<br/>
	 * 3擔保品<br/>
	 * 4其他敘做條件
	 */
	public String getItemType() {
		return this.itemType;
	}

	/**
	 * 設定項目類別
	 * <p/>
	 * 2利(費)率<br/>
	 * 3擔保品<br/>
	 * 4其他敘做條件
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/** 取得項目說明 **/
	public String getItemDscr() {
		return this.itemDscr;
	}

	/** 設定項目說明 **/
	public void setItemDscr(String value) {
		this.itemDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

}
