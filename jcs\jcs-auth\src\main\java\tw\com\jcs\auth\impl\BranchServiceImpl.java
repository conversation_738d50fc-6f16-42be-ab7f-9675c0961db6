package tw.com.jcs.auth.impl;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.springframework.jdbc.core.RowCallbackHandler;

import tw.com.jcs.auth.AuthQueryFactory;
import tw.com.jcs.auth.BranchService;
import tw.com.jcs.auth.model.Branch;
import tw.com.jcs.auth.model.impl.BranchImpl;
import tw.com.jcs.auth.util.StringUtil;

/**
 * <pre>
 * 單位相關資訊(已棄用)
 * </pre>
 * 
 * @since 2022年12月21日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月21日
 *          </ul>
 */
@SuppressWarnings("deprecation")
public class BranchServiceImpl implements BranchService {

    private AuthQueryFactory queryFactory;

    // branchId -> Branch
    Map<String, BranchImpl> branchs = new LinkedHashMap<String, BranchImpl>();

    @Resource
    public void setQueryFactory(AuthQueryFactory queryFactory) {
        this.queryFactory = queryFactory;
    }

    /**
     * constructor
     */
    public BranchServiceImpl() {
        super();
    }

    /**
     * 初始化參數
     */
    @PostConstruct
    void init() {
        branchs.clear();
        initBranch();
    }

    /**
     * 初始化參數
     */
    void initBranch() {
        queryFactory.execBranchQuery(new RowCallbackHandler() {
            public void processRow(ResultSet rs) throws SQLException {
                BranchImpl brn = new BranchImpl();

                brn.setBrNo(StringUtil.trim(rs.getString("BRNO")));
                brn.setBrName(StringUtil.trimFullSpace(rs.getString("BRNAME")));
                brn.setUpdater(StringUtil.trim(rs.getString("UPDATER")));
                brn.setChkNo(StringUtil.trim(rs.getString("CHKNO")));
                brn.setAddr(StringUtil.trim(rs.getString("ADDR")));
                brn.setTel(StringUtil.trim(rs.getString("TEL")));
                brn.setBrClass(StringUtil.trim(rs.getString("BRCLASS")));
                brn.setEngName(StringUtil.trim(rs.getString("ENGNAME")));
                brn.setUseSWFT(StringUtil.trim(rs.getString("USESWFT")));
                brn.setNameABBR(StringUtil.trim(rs.getString("NAMEABBR")));
                brn.setBrNoFlag(StringUtil.trim(rs.getString("BRNOFLAG")));
                brn.setBrNoArea(StringUtil.trim(rs.getString("BRNOAREA")));
                brn.setAccManager(StringUtil.trim(rs.getString("ACCMANAGER")));
                brn.setUnitType(StringUtil.trim(rs.getString("UNITTYPE")));
                brn.setBrnGroup(StringUtil.trim(rs.getString("BRNGROUP")));
                // brn.setBrnGrpName(StringUtil.trim(rs.getString("BRNGRPNAME")));
                brn.setCountryType(StringUtil.trim(rs.getString("COUNTRYTYPE")));
                brn.setTimeZone(StringUtil.trim(rs.getString("TIMEZONE")));
                brn.setHostCodePage(StringUtil.trim(rs.getString("HOSTCODEPAGE")));
                brn.setIsSatOpen(StringUtil.trim(rs.getString("ISSATOPEN")));
                brn.setParentBrNo(StringUtil.trim(rs.getString("PARENTBRNO")));

                branchs.put(brn.getBrNo(), brn);
            }
        });
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.BranchService#getBranch(java.lang.String)
     */
    @Override
    public Branch getBranch(String branchId) {
        return branchs.get(branchId);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.BranchService#getBranchName(java.lang.String)
     */
    @Override
    public String getBranchName(String branchId) {
        Branch brn = branchs.get(branchId);
        return (brn == null ? null : brn.getBrName());
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.BranchService#getAllBranch()
     */
    @Override
    public List<Branch> getAllBranch() {
        List<Branch> list = new LinkedList<Branch>();

        for (Branch brn : branchs.values()) {
            list.add(brn);
        }
        return list;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.BranchService#getBranchByUnitType(java.lang.String[])
     */
    @Override
    public List<Branch> getBranchByUnitType(String... unitType) {
        List<Branch> list = new LinkedList<Branch>();

        Set<String> unitTypeSet = new HashSet<String>(Arrays.asList(unitType));
        for (Branch brn : branchs.values()) {
            if (unitTypeSet.contains(brn.getUnitType())) {
                list.add(brn);
            }
        }
        return list;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.BranchService#getBranchByFlag(java.lang.String[])
     */
    @Override
    public List<Branch> getBranchByFlag(String... flag) {
        List<Branch> list = new LinkedList<Branch>();

        Set<String> flagSet = new HashSet<String>(Arrays.asList(flag));
        for (Branch brn : branchs.values()) {
            if (flagSet.contains(brn.getBrNoFlag())) {
                list.add(brn);
            }
        }
        return list;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.BranchService#getBranchByArea(java.lang.String[])
     */
    @Override
    public List<Branch> getBranchByArea(String... area) {
        List<Branch> list = new LinkedList<Branch>();

        Set<String> areaSet = new HashSet<String>(Arrays.asList(area));
        for (Branch brn : branchs.values()) {
            if (areaSet.contains(brn.getBrNoArea())) {
                list.add(brn);
            }
        }
        return list;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.BranchService#getBranchOfGroup(java.lang.String[])
     */
    @Override
    public List<Branch> getBranchOfGroup(String... groupId) {
        List<Branch> list = new LinkedList<Branch>();

        Set<String> groupSet = new HashSet<String>(Arrays.asList(groupId));
        for (Branch brn : branchs.values()) {
            if (groupSet.contains(brn.getBrnGroup())) {
                list.add(brn);
            }
        }
        return list;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.BranchService#getBranchOfParent(java.lang.String[])
     */
    @Override
    public List<Branch> getBranchOfParent(String... branchId) {
        List<Branch> list = new LinkedList<Branch>();

        Set<String> parentSet = new HashSet<String>(Arrays.asList(branchId));
        for (Branch brn : branchs.values()) {
            if (parentSet.contains(brn.getParentBrNo())) {
                list.add(brn);
            }
        }
        return list;
    }
}
