/* 
 * L161S01DDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L161S01D;

/** RPA自動發查明細檔 **/
public interface L161S01DDao extends IGenericDao<L161S01D> {

	L161S01D findByOid(String oid);
	
	List<L161S01D> findByMainId(String mainId);

	List<L161S01D> findByIndex01(String mainId, String type, String status);

	List<L161S01D> findByIndex03(String mainId, String docfileoid, String docfileoid2);

	List<L161S01D> findByIndex02(String mainId, String type, String status,
			String rpaQueryReason1);
}