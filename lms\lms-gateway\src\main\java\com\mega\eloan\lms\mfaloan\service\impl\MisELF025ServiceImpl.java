package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisELF025Service;

/**
 * <pre>
 * 核准額度資料檔  MIS.ELF447n
 * </pre>
 * 
 * @since 2012/6/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/6/8,REX,new
 *          </ul>
 */
@Service
public class MisELF025ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF025Service {

	@Override
	public void deleteByMainId(String mainId) {
		this.getJdbc().update("ELF025.delByMainId", new Object[] { mainId });

	}

	@Override
	public void insert(String ELF025_SDATE, String ELF025_MAINID,
			String ELF025_BRANCH, String ELF025_CASEDATE,
			String ELF025_DOCUMENT_NO, String ELF025_CONTRACT_CO,
			String ELF025_CONTRACT, String ELF025_SWFT,
			BigDecimal ELF025_FACT_AMT, String ELF025_UPDATER) {
		this.getJdbc().update(
				"ELF025.insert",
				new Object[] { ELF025_SDATE, ELF025_MAINID, ELF025_BRANCH,
						ELF025_CASEDATE, ELF025_DOCUMENT_NO,
						ELF025_CONTRACT_CO, ELF025_CONTRACT, ELF025_SWFT,
						ELF025_FACT_AMT, ELF025_UPDATER });

	}

}
