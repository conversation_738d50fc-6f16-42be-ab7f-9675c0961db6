/* 
 * DwdbBASEService.java
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2011 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.lms.dw.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * DwdbBASEService
 * </pre>
 * 
 * @since 2011/9/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/29,UFO,new
 *          </ul>
 */

public interface DwdbBASEService {

	/**
	 * 查詢國家別所對應區域別代碼（dw_cntry_area）
	 * 
	 * @param countryCode
	 *            國家代碼
	 * @return AREA_NO 區域別代碼
	 */
	public List<Map<String, Object>> findDW_CNTRYAREAByCountryCode(
			String countryCode);

	/**
	 * 刪除上傳授信報案考核表標頭檔
	 * 
	 * @param mainId
	 *            文件編號
	 */
	public void DW_ELINSPSC_delete(String mainId);

	/**
	 * 上傳授信報案考核表標頭檔
	 * 
	 * @param typCd
	 * @param branchId
	 * @param chkm
	 * @param mainId
	 * @param apprId
	 * @param chkId
	 * @param chkUnit
	 * @param custId
	 * @param dupNo
	 * @param projNo
	 * @param curr
	 * @param loanAmt
	 * @param caseAmt
	 * @param tmGrade
	 * @param sysType
	 */
	public void DW_ELINSPSC_insert(String typCd, String branchId, Date chkm,
			String mainId, String apprId, String chkId, String chkUnit,
			String custId, String dupNo, String projNo, String curr,
			double loanAmt, double caseAmt, double tmGrade, String sysType);

	/**
	 * 刪除上傳授信報案考核表子檔
	 * 
	 * @param mainId
	 *            文件編號
	 */
	public void DW_ELINSPDT_delete(String mainId);

	/**
	 * 上傳授信報案考核表子檔
	 * 
	 * @param dataList
	 *            資料群組
	 */
	public void DW_ELINSPDT_insert(List<Object[]> dataList);

	/**
	 * 額度明細表 -引進帳務資料(借方彙計數,貸方彙計數)
	 * 
	 * @param custId
	 *            客戶ID
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	public List<Map<String, Object>> findDW_LNCNTROVS_TOT(String custId,
			String cntrNo);

	/**
	 * 覆審管理報表(Type1)
	 * 
	 * @param brNo
	 * @param custId
	 * @return
	 */
	public List<Map<String, Object>> findDW_ASLNDAVGOVSJOIN_ByBrNoCustId(
			String brNo, String custId, String dupNo);

	// public List<?> findDW_ELF411OVS_ByCntr(String custId, String dupNo,
	// String branch);

	/**
	 * 找海外的利率
	 * 
	 * @param brNo
	 *            分行代號
	 * @return CURR 幣別 RATE 匯率
	 */
	public List<Map<String, Object>> findDW_DWFXRTHOVS_RATE(String brNo);

	/**
	 * @param brNo
	 *            分行代號
	 * @return CURR 該分行主要計價幣別
	 */
	public List<Map<String, Object>> findDW_DWFXRTHOVS_MainCurr(String brNo);

	public List<Map<String, Object>> findDW_MD_CUPFM_OTS_selCYC_MN(
			String custKey, String dupNo, String dateS, String dateE);

	/**
	 * 讀取國內貢獻度(存款,非存款)
	 * 
	 * @param custKey
	 *            custKey
	 * @param dateS
	 *            dateS
	 * @param dateE
	 *            dateE
	 * @return
	 */
	public List<Map<String, Object>> findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE(
			String custKey, String dupNo, String dateS, String dateE);

	/**
	 * 讀取海外貢獻度(非存款)
	 * 
	 * @param custKey
	 *            custKey
	 * @param dateS
	 *            dateS
	 * @param dateE
	 *            dateE
	 * @return
	 */
	public List<?> findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE(String custKey,
			String dupNo, String dateS, String dateE);

	/**
	 * 重新引進帳務資料(DW_ASLNDAVGOVS JOIN DW_LNQUOTOV)第一次下SQL
	 * 
	 * @param brNo
	 * @param custId
	 * @return
	 */
	public List<Map<String, Object>> findDW_ASLNDAVGOVSJOIN_ByCustIdData(
			String brNo, String custId, String dupNo);

	/**
	 * 重新引進帳務資料(DW_ASLNDAVGOVS JOIN DW_LNQUOTOV)第二次下SQL 利用額度序號找子母戶
	 * 
	 * @return
	 */
	public Map<String, Object> findDW_ASLNDAVGOVSJOIN_ByContractData(
			String custId, String contract);

	/**
	 * 額度明細表 -引進帳務資料(餘額)
	 * 
	 * @param custId
	 *            客戶統編+重複序號
	 * @param brNo
	 *            銀行代碼
	 * @param contract
	 *            額度序號
	 * @return 幣別 CURR ,餘額換算金額 COUNT
	 */
	public List<Map<String, Object>> findDW_LNQUOTOV_selBLAmt(String custId,
			String brNo, String contract);

	/**
	 * 覆審管理報表 type3 = 撥貸後半年內辦理覆審檢核表
	 * 
	 * @param custId411
	 * @param dupNo411
	 * @param brNo
	 * @return
	 */
	public Map<String, Object> findDwaslnquotByBranchForReportType3(
			String custId411, String dupNo411, String brNo);

	/**
	 * 查詢DW有效資料範圍日期
	 * 
	 * @return
	 */
	public List<Map<String, Object>> findDWADM_MD_CUPFM_OTS_selDate();

	/**
	 * 查詢DW最近半年之資料日期
	 * 
	 * @return
	 */
	public List<Map<String, Object>> findDWADM_MD_CUPFM_OTS_selDate2(
			Date queryDateS, Date queryDateE);

	/**
	 * 管理報表 type1=授信契約已逾期控制表
	 * 
	 * @param brNo
	 * @param baseDateStr
	 * @return
	 */

	public List<Map<String, Object>> findDW_LNQUOTOVforNewReportType1ByBrNo(
			String brNo, String dateStartDate, String dateEndDate);

	/**
	 * type9-營業單位授信報案考核彙總表 抓全年,抓指定月份
	 * 
	 * @param twYM
	 * @param type
	 * @return
	 */

	public List<Map<String, Object>> findDW_ELINSPDT_sel9ByBRANCHIDCHKYMTYPCDYM(
			String startTWYM, String endTWYM, String TWYM_START,
			String TWYM_END, String type);

	/**
	 * 查詢放款餘額檔
	 * 
	 * @param custId
	 * @return
	 */
	public List<Map<String, Object>> findDWADMSselDistinctByCust_Key(
			String custId);

	public int delete(Object[] msgFmtParam, Object[] data);

	public void insert(Object[] msgFmtParam, int[] type, List<Object[]> lst);

	public void update(Object[] msgFmtParam, int[] type, List<Object[]> lst);

	/**
	 * 取得MD_CUPFM_OTS_max最大資料日期，有走key，速度最快
	 */
	Date getMD_CUPFM_OTS_max_cyc_mn();

	/**
	 * 取得借款人海外授信額度、餘額、不列入授信額度與餘額
	 * 
	 * @param custId
	 * @param dupCode
	 * @return
	 */
	List<Map<String, Object>> findDW_LNCNTROVS_L120S05B1(String allCustId,
			String custId, String dupNo, String custName);

	/**
	 * 取得海外當地借款人海外授信額度、餘額、不列入授信額度與餘額
	 * 
	 * @param custId
	 * @param dupCode
	 * @param brNos
	 *            ex: Y01','Y02','Y03
	 * @return
	 */
	List<Map<String, Object>> findDW_LNCNTROVS_L120S05B1_ForLocal(
			String allCustId, String custId, String dupNo, String custName,
			String[] brNos);

	/**
	 * 取得借款人信用風險集中明細
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findDW_OTS_ROCLIST_ECUSD_ByCustId(String custId,
			String dupNo);

	/**
	 * 取得信用風險集中度，集團抓的規定限額比率 
	 * @param grade
	 * @return
	 */
	public Map<String, Object> findDW_GRP_LIMIT_ByGrade(String grade);
	
	/**
	 * 查詢負責人/連保人之存款資料
	 * 
	 * @param custId
	 *            客戶統編
	 * @return 所有存款別及台幣前日餘額 存款資料
	 */
	List<Map<String, Object>> findDW_IDDP_DPF_ByCustId(String custId);

	/**
	 * 取得本行存款帳戶序號BY CUSTID DUPNO BRNO
	 * 
	 * @param brno
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<Map<String, Object>> findDW_IDDP_DPF_Seqno_ByCustIdBrno(String brno,
			String custId, String dupNo);

	/**
	 * 取得本行存款帳戶序號BY CUSTID DUPNO
	 * 
	 * @param brno
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<Map<String, Object>> findDW_IDDP_DPF_Seqno_ByCustId(String custId,
			String dupNo);

	/**
	 * 檢查本行存款帳戶
	 * 
	 * @param brno
	 * @param apcd
	 * @param seqno
	 * @return
	 */
	List<Map<String, Object>> findDW_IDDP_DPF_CustData_ByAccount(String brno,
			String apcd, String seqno);

	/**
	 * 檢查本行存款帳戶
	 * 
	 * @param brno
	 * @param apcd
	 * @param seqno
	 * @return
	 */
	List<Map<String, Object>> findDW_IDDP_DPF_CustData_ByCustIdAccount(
			String[] custIds, String brno, String apcd, String seqno);

	/**
	 * 取得OTS_BSL2CSNET_AVG最大資料日期，有走key，速度最快
	 */
	Date getOTS_BSL2CSNET_AVG_max_cyc_mn();

	/**
	 * 取得風險性資產平均餘額
	 * 
	 * @param custKey
	 * @param dateS
	 * @param dateE
	 * @return
	 */
	List<Map<String, Object>> findOTS_BSL2CSNET_AVG_TOTAL_RA_AMT(
			String custKey, String dupNo, String dateS, String dateE);

	/**
	 * 取得海外平均動用率
	 * 
	 * @param cntrNo
	 * @param dateS
	 * @param dateE
	 * @return
	 */
	List<Map<String, Object>> findDW_LNMRATEOVS_USED_RATEY(String cntrNo,
			String dateS, String dateE);

	void updateDR__DR_1YR(String mowType, int mower1, int mower2,
			int finalRating, BigDecimal dr, BigDecimal dr_1yr);

	void delL140M01A(String BR_CD, String NOTEID, String CUSTID, String DUPNO,
			String MOWTYPE, String MOWVER1, String MOWVER2, String JCIC_DATE,
			String ACCT_KEY, String DELETE_REASON, String REJECT_OTHEREASON_TEXT);

	void delL140M01A_OVS(String BR_CD, String NOTEID, String RATING_DATE,
			String RATING_ID, String CUSTID, String DUPNO, String CUST_KEY,
			String LOAN_CODE, String MOWTYPE, String MOWVER1, String MOWVER2);

	/**
	 * 取得客戶項下DW未銷戶額務序號(企金用)
	 */
	public List<Map<String, Object>> findDWCntrnoByCustId(String custId);

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 取得客戶之海外額度序號資訊BY額度序號
	 */
	Map<String, Object> findDWCntrnoByCntrNo(String custId, String dupNo,
			String cntrNo);

	public List<Map<String, Object>> findDW_FXRTH_LatestRate();

	/**
	 * J-105-0017-001 Web e-Loan企金授信授權外簽報書第八章增加本行買入集團企業無擔保債券有效額度與餘額。
	 * 取得DW集團無擔保債券額度與餘額(國內)
	 * 
	 * @param grpid
	 * @param banDupNoAry
	 * @return
	 */
	public Map<String, Object> findDBUEstAmtAndBal(String grpid,
			String[] banDupNoAry);

	/**
	 * J-105-0017-001 Web e-Loan企金授信授權外簽報書第八章增加本行買入集團企業無擔保債券有效額度與餘額。
	 * 取得DW集團無擔保債券額度與餘額(海外)
	 * 
	 * @param grpid
	 * @param banDupNoAry
	 * @return
	 */
	public Map<String, Object> findOCEEstAmtAndBal(String grpid,
			String[] banDupNoAry);

	/**
	 * J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
	 * 
	 * @param brNo
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findDW_AS400_groupMember(String brNo,
			String custId, String dupNo);

	/**
	 * 修改LNUNID 統編 J-105-0202-001 Web e-Loan企金授信修改客戶統編。
	 * 
	 * @param orgCustId
	 * @param orgDupNo
	 * @param newCustId
	 * @param newDupNo
	 * @param cntrNo
	 * @param rptMainId
	 */
	public void updateJ1050202_by_custIdAndDupNo(String orgCustId,
			String orgDupNo, String newCustId, String newDupNo,
			String documentNo, String cntrNo, String rptMainId);

	/**
	 * 取得客戶海外額度 J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findDW_LNCNTROVS_By_CustId(String custId,
			String dupNo);

	/**
	 * 取得客戶海外額度 J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 * 
	 * @param cntrNo
	 * @return
	 */
	public List<Map<String, Object>> findDW_LNCNTROVS_By_CntrNo(String cntrNo);

	/**
	 * J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
	 * 
	 * @param cntrNo
	 * @return
	 */
	public List<Map<String, Object>> findDW_LNCNTROVS_By_CustId_And_CntrNo(
			String custId, String dupNo, String cntrNo);

	/**
	 * 與findDW_LNCNTROVS_By_CustId_And_CntrNo一樣 多抓原幣別的餘額金額
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	public List<Map<String, Object>> findDW_LNCNTROVS_By_CustId_And_CntrNo_WithOriginal(
			String custId, String dupNo, String cntrNo);

	/**
	 * J-106-0029-002/003/004 洗錢防制-新增洗錢防制頁籤 新增實質受益人 動審表新增洗錢防制頁籤
	 * 
	 * @param brNo
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findDW_OTS_EFFECTIVE_OVS_By_CustId_And_BrNo(
			String brNo, String custId, String dupNo);

	/**
	 * J-106-0029-002/003/004 洗錢防制-新增洗錢防制頁籤 新增實質受益人 動審表新增洗錢防制頁籤
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	public Map<String, Object> findDW_OTS_CSOVS_By_CustId_And_BrNo(String brNo,
			String custId, String dupNo);

	/**
	 * G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
	 */
	public void doLmsBatch0012();

	/**
	 * G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
	 */
	public void doLmsBatch0012_1();

	/**
	 * G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
	 */
	public void doLmsBatch0012_2();

	/**
	 * J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	public Map<String, Object> findDW_DW_ROCGRPRT_By_BadFg_And_GrpGrade(
			String badFg, String grpGrade);

	/**
	 * J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
	 * 
	 * @param badFg
	 * @return
	 */
	public List<Map<String, Object>> findDW_DW_ROCGRPRT_By_BadFg_And_GrpYy(
			String badFg, String grpYy, String grpCycMn);

	/**
	 * J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
	 * 
	 * @param badFg
	 * @return
	 */
	public List<Map<String, Object>> findDW_DW_ROCGRPRT_By_BadFg_With_MaxCycMn(
			String badFg);

	/**
	 * G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
	 */
	public void doLmsBatch0013();

	/**
	 * G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
	 */
	public void doLmsBatch0013_1();

	/**
	 * G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
	 */
	public void doLmsBatch0013_2();

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findDW_LNCNTROVS_Sum_By_CustId(String custId,
			String dupNo);

	/**
	 * J-110-0325_11557_B1001 增加合併關係企業與當地有往來資料(L120S11A_LOC)
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brIds
	 * @return
	 */
	public List<Map<String, Object>> findDW_LNCNTROVS_Sum_By_CustId_BrIds(
			String custId, String dupNo, List<String> brIds);

	/**
	 * J-107-0073 Web e-Loan 引入是否有定時定額扣款
	 */
	public List<Map<String, Object>> findDW_OTS_ASFDAC2_J_107_0073(
			String cust_key);

	/**
	 * J-107-0073 Web e-Loan 引入 INV_AMT_WMS 理財AUM(不含存款)
	 */
	public List<Map<String, Object>> findDW_OTS_WM_CUST_SUMMARY_J_107_0073(
			String cusCode, String cyc_mn_beg);

	/**
	 * J-107-0233_05097_B1001 Web e-Loan企金授信修訂「放款定價合理性分析表」。
	 * 
	 * @param custKey
	 * @param dupNo
	 * @param dateS
	 * @param dateE
	 * @return
	 */
	public List<Map<String, Object>> findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE_exclude_interest(
			String custKey, String dupNo, String dateS, String dateE);

	/**
	 * J-107-0233_05097_B1001 Web e-Loan企金授信修訂「放款定價合理性分析表」。
	 * 
	 * @param custKey
	 * @param dupNo
	 * @param dateS
	 * @param dateE
	 * @return
	 */
	public List<?> findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE_exclude_interest(
			String custKey, String dupNo, String dateS, String dateE);

	/**
	 * J-110-0155 修改e-loan授信管理系統簽報書之「利率定價合理性分析表」為「新臺幣、美元利率定價合理性及收益率分析表」
	 */
	public List<Map<String, Object>> findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE_ByBC3_CD(
			String custKey, String dupNo, String dateS, String dateE,
			String type);

	/**
	 * J-110-0155 修改e-loan授信管理系統簽報書之「利率定價合理性分析表」為「新臺幣、美元利率定價合理性及收益率分析表」
	 */
	public List<?> findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE_ByBC3_CD(
			String custKey, String dupNo, String dateS, String dateE,
			String type);

	/**
	 * J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
	 */
	public List<Map<String, Object>> findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE_GroupByBC3_CD(
			String custKey, String dupNo, String dateS, String dateE);

	public List<?> findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE_GroupByBC3_CD(
			String custKey, String dupNo, String dateS, String dateE);

	/**
	 * J-107-0224_05097_B1001 Web e-Loan企金處新增企金授信案件敘做情形及比較表
	 * 
	 * @param dateS
	 * @param dateE
	 * @return
	 */
	public List<Map<String, Object>> findOTS_RPC1A4ByRptDate(String dateS,
			String dateE);

	/**
	 * J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findDW_LNCNTROVSByCustIdForCreExcel2(
			String custId, String dupNo);

	public List<Map<String, Object>> findDW_RKAPPLICANT_byNoteIdCustIdDupNo(
			String noteId1, String noteId2, String custId, String dupNo);

	public List<Map<String, Object>> findDW_RKJCIC_byNoteIdCustIdDupNo(
			String noteId1, String noteId2, String custId, String dupNo);

	public List<Map<String, Object>> find_fixData(String br_cd, String noteId,
			String cntrNo);

	public void run_fixData(String BR_CD, String NOTEID, String CUSTID,
			String DUPNO, String MOWTYPE, String MOWVER1, String MOWVER2,
			String JCIC_DATE, String CNTRNO, String ACCT_KEY, Integer LNMONTH);

	public List<Map<String, Object>> find_clsScoreData_by_noteId(
			String tableName, String noteId);

	public void dw_fixData_DATA_SRC_DT__null(String tableName, String noteId,
			String data_src_dt);

	public List<Map<String, Object>> find_fixData_OTS_RKCREDITOVS_CHKDATE_CHKEMPNO(
			String br_cd, String noteId, String cntrNo);

	public void run_fixData_OTS_RKCREDITOVS_CHKDATE_CHKEMPNO(String BR_CD,
			String NOTEID, String RATING_DATE, String RATING_ID, String CUSTID,
			String DUPNO, String CUST_KEY, String LOAN_CODE, String MOWTYPE,
			String MOWVER1, String MOWVER2, String CHKDATE, String CHKEMPNO);

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 */
	public void doLmsBatch0015_5(String fBranch, String tBranch, String custId,
			String dupNo);

	/**
	 * 轉檔使用
	 * 
	 * @return
	 */
	public List<Map<String, Object>> findLnCntrovsExitem();

	public boolean isLnCustRel_done(String cyc_mn, String done_cust_key);

	public Map<String, Object> findLnCustRel_done_history(String done_cust_key);

	public List<Map<String, Object>> findLnCustRel_by_rel_flag(String cyc_mn,
			String rel_flag);

	public BigDecimal countLnCustRel_done_cust_key(String cyc_mn,
			String done_cust_key);

	public BigDecimal countLnCustRel_rel_type(String cyc_mn, String rel_type);

	public List<Map<String, Object>> findLnCustRel_top_dup_cnt(String cyc_mn,
			String rel_flag, Integer val);

	public List<Map<String, Object>> findLnCustBr(String cyc_mn);

	public BigDecimal countLnCustBr(String cyc_mn);

	public Map<String, Object> test_DwVam106Cc_getVAM106Data(String id);

	public Map<String, Object> test_DwVam107Cc_getVAM107Data(String id);

	public Map<String, Object> test_DwVam108Cc_getVAM108Data(String id);

	public Map<String, Object> test_DwBam095Cc_getBAM087DelayPayLoan(String id);

	public Map<String, Object> test_DwKrm040Cc_getKRM040CardPayCode2(
			String custId);

	public Map<String, Object> test_DwKrm040Cc_getKRM040CardPayCode3(
			String custId);

	public List<Map<String, Object>> test_DwKrm040Cc_getKRM040CardPayCode4(
			String custId);

	public Map<String, Object> test_DwKrm040Cc_getKRM040CashAdvance(
			String custId);

	public Map<String, Object> test_DwBam095Cc_getBAM087CashCard(String id);

	public Map<String, Object> test_DwBam095Cc_getD63LnNosBank(String id);

	public Map<String, Object> test_DwKrm040Cc_getAllCc6RcUseBank(String id);

	public Map<String, Object> test_DwBam095Cc_getD07_G_V_1_3(String id);

	public Map<String, Object> test_DwKrm040Cc_getP68P19_Q(String id);

	public List<Map<String, Object>> getElDeleteFileList(String time,
			String brs, int count);

	public List<Map<String, Object>> getElDeleteFileListSingle(String time,
			String brs);

	/**
	 * J-108-0143_05097_B1001 Web e-Loan國內外企金額度明細表簽報性質新作時加註(新客戶往來原有客戶往來)
	 * 
	 * @return
	 */
	public Map<String, Object> findOTS_CSFACT_LIST_maxCynMn();

	/**
	 * J-108-0143_05097_B1001 Web e-Loan國內外企金額度明細表簽報性質新作時加註(新客戶往來原有客戶往來)
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cycMn
	 * @return
	 */
	public Map<String, Object> findOTS_CSFACT_LIST_by_custKey(String custId,
			String dupNo, String cycMn);

	/**
	 * J-108-0268 覆審案件 客戶逾期情形
	 */
	public Map<String, Object> findOverDue(Date qryDate, String custId,
			String dupNo);

	/**
	 * M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param mainId
	 */
	public void delete_DW_COSTSHARE_LMS01(String mainId);

	/**
	 * M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param mainId
	 */
	public void delete_DW_COSTSHARE_LMS02(String mainId);

	/**
	 * M-108-0296_05097_B1003 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param mainId
	 */
	public void delete_DW_COSTSHARE_LMS03(String mainId);

	/**
	 * M-108-0296_05097_B1004 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param mainId
	 */
	public void delete_DW_COSTSHARE_LMS04(String mainId);

	/**
	 * M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param CNTRNO
	 * @param CASEBRID
	 * @param CASETYPE
	 * @param CASELVL
	 * @param COLLTYPE
	 * @param CASENO
	 * @param ENDDATE
	 * @param MAINID
	 * @param CNTRMAINID
	 * @param UPDATER
	 */
	public void DW_COSTSHARE_LMS01_INSERT(String CNTRNO, String CASEBRID,
			String CASETYPE, String CASELVL, String COLLTYPE, String CASENO,
			String ENDDATE, String MAINID, String CNTRMAINID, String AREABRID,
			String APPROVEDATE, String UPDATER, Timestamp DELETETIME);

	/**
	 * M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param CNTRNO
	 * @param OWNBRID
	 * @param CASETYPE
	 * @param CASELVL
	 * @param COLLTYPE
	 * @param RETRIALDATE
	 * @param PROJECTNO
	 * @param ENDDATE
	 * @param CASENO
	 * @param MAINID
	 * @param RPTMAINID
	 * @param UPDATER
	 */
	public void DW_COSTSHARE_LMS02_INSERT(String CNTRNO, String OWNBRID,
			String CASETYPE, String CASELVL, String COLLTYPE,
			String RETRIALDATE, String PROJECTNO, String ENDDATE,
			String CASENO, String MAINID, String RPTMAINID, String UPDATER,
			Timestamp DELETETIME);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * 衍生性商品FROM IDTR0024
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findOTS_SMTLMTByCustId(String custId,
			String dupNo);

	/**
	 * M-108-0296_05097_B1003 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param CASENO
	 * @param CASEBRID
	 * @param CASETYPE
	 * @param CASELVL
	 * @param DOCCODE
	 * @param ENDDATE
	 * @param MAINID
	 * @param AREABRID
	 * @param APPROVEDATE
	 * @param UPDATER
	 */
	public void DW_COSTSHARE_LMS03_INSERT(String CASENO, String CASEBRID,
			String CASETYPE, String CASELVL, String DOCCODE, String ENDDATE,
			String MAINID, String AREABRID, String APPROVEDATE, String UPDATER,
			Timestamp DELETETIME);

	/**
	 * M-108-0296_05097_B1004 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * @param CNTRMAINID
	 * @param CNTRNO
	 * @param SHAREBRID
	 * @param SHAREFLAG
	 * @param SHARERATE1
	 * @param SHARERATE2
	 * @param SHAREAMT
	 * @param TOTALAMT
	 * @param SHARENO
	 * @param SNOKIND
	 * @param CURRENTAPPLYCURR
	 * @param CURRENTAPPLYAMT
	 * @param UPDATER
	 * @param DELETETIME
	 */
	public void DW_COSTSHARE_LMS04_INSERT(String CNTRMAINID, String CNTRNO,
			String SHAREBRID, String SHAREFLAG, BigDecimal SHARERATE1,
			BigDecimal SHARERATE2, BigDecimal SHAREAMT, BigDecimal TOTALAMT,
			String SHARENO, String SNOKIND, String CURRENTAPPLYCURR,
			BigDecimal CURRENTAPPLYAMT, String UPDATER, Timestamp DELETETIME);

	/**
	 * J-108-0288_05097_B1003 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * 
	 * 其他具相同地址、電話等原因之關係企業
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findRelatedCompanyWithSameTELAndAddr(
			String custId, String dupNo);

	/**
	 * J-109-0115_10702_B1001 Web e-Loan管理報表新增海外分行「授信業務異常通報月報表」
	 * 
	 * @param custId
	 * @param brnNo
	 * @return
	 */
	List<Map<String, Object>> listLMS180R25_2(String brnNo, String custId);

	/**
	 * 取得線上對保存款帳號
	 * 
	 * @param custId
	 * @return
	 */
	List<Map<String, Object>> getGuarOnLineAccount(String custId);

	List<Map<String, Object>> getAccount_laborContract(String custId);

	List<Map<String, Object>> getAccount_laborContract(String custId,
			String dupNo);

	/**
	 * 
	 * 
	 * 抓是否屬凍結額度國家
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findDW_CTYRKANYByType2();

	/**
	 * J-109-0328_05097_B1001 Web e-loan企金授信額度明細表中小信保增加借款人非屬獨資或合夥之徵提連帶保證人檢核。
	 * 
	 * @param custId
	 * @return
	 */
	public Map<String, Object> findOTS_DW_BGMOPENByIdNo(String custId);

	/**
	 * J-109-0369_05097_B1001 e-Loan企金授權外簽報書引進國家風險限額資料
	 * 
	 * @param cntryCdStr
	 * @return
	 */
	public List<Map<String, Object>> findOTS_DW_CNTRY_C1ByCntryCd(
			String... cntryCdStr);

	/**
	 * M-110-0227 資料倉儲提供fptinsrt table至DWOTS供eloan授信簽報利率合理性分析引用
	 */
	public List<Map<String, Object>> findOTS_FTPINSRTByCurr(String currs);

	/**
	 * J-112-0281_05097_B1001 Web
	 * e-Loan簽報書BIS評估表調整海外分行存款貢獻度之年化處理及非授信貢獻度為負時應顯示之警語
	 */
	public List<Map<String, Object>> findOTS_FTPINSRTByCurr_lastMonth(
			String currs);

	/**
	 * 抓外部信評最新資料日期
	 * 
	 * @return
	 */
	public Map<String, Object> findDW_BSL2ECRSCRMaxCycMn();

	/**
	 * 抓外部信評資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cycMn
	 * @return
	 */
	public Map<String, Object> findDW_BSL2ECRSCRByCustKeyAndCycMn(
			String custId, String dupNo, String cycMn);

	/**
	 * J-109-0496 貸後管理 理財商品贖回追蹤
	 */
	public List<Map<String, Object>> queryDWLNWMCFMList();

	public List<Map<String, Object>> queryDWLNWMCFMUNID(String CUST_ID,
			String CUST_DUP_NO, String PRO_TYPE, String BANK_PRO_CODE,
			String ACC_NO);

	public void DW_LNWM_CFM_Update(String lstSellDt, String lstSellCurCd,
			BigDecimal lstSellAmt, String lstSellBrCd, String tranType,
			String dataDt, String bachDt, String unid, String custID,
			String custDupNo, String proType, String bankProCode, String accNo);

	/**
	 * 抓理財商品資料
	 */
	public List<Map<String, Object>> findOTS_DW_LNWM_MNT_ById(String custId,
			String dupNo, String bgnDate);

	public List<Map<String, Object>> findOTS_DW_LNWM_MNT_ByKey(String custId,
			String dupNo, String proType, String bankProCode, String accNo);

	/**
	 * 抓已確認的理財商品資料
	 */
	public List<Map<String, Object>> findOTS_DW_LNWM_CFM_ByUnid(String unid);

	public List<Map<String, Object>> findOTS_DW_LNWM_CFM_ByKey(String unid,
			String custId, String dupNo, String proType, String bankProCode,
			String accNo);

	public void OTS_DW_LNWM_CFM_delete(List<Object[]> dataList);

	public void OTS_DW_LNWM_CFM_insert(List<Object[]> dataList);

	public List<Map<String, Object>> findDW_RKCREDIT_by_acct_key(String acct_key);

	public List<Map<String, Object>> findDW_ASLNDNEWOVS_ByBrNoCustId(
			String brNo, String custId, String dupNo, String cntrNo,
			String loanNo);

	public int trans_DW_RKtbl_old_to_new_cntrNo(String old_cntrNo,
			String new_cntrNo);

	public void trans_DW_RKtbl_old_to_new_loanNo(String old_loanNo,
			String new_loanNo);

	List<Map<String, Object>> findDW_LNF273_by_cntrNo(String cntrNo);

	public void trans_OTS_CRDLN031_old_to_new_AREA_CD(String brNo,
			String old_areaNo, String new_areaNo);

	/**
	 * J-111-0052 修改借戶暨關係戶與本行往來實績彙總表 取得 OTS_HINS_ASCT 資料月數、最大資料日期、及半年前日期
	 */
	public Map<String, Object> getOTS_HINS_ASCT_maxCycMn();

	/**
	 * J-111-0052 修改借戶暨關係戶與本行往來實績彙總表 取得 OTS_HINS_ASCT 最新統計資料
	 */
	public Map<String, Object> getOTS_HINS_ASCT_data(String fullCustId,
			Date queryDate);

	/**
	 * J-111-0052 修改借戶暨關係戶與本行往來實績彙總表 取得 OTS_HINS_ASCT 近半年總數
	 */
	public Map<String, Object> getOTS_HINS_ASCT_sumData(String fullCustId,
			Date beginDate, Date endDate);

	public void DW_L120S21A_delete(String mainId);

	public void DW_L120S21A_insert(String UNID, String PROJECT_NO,
			String DATA_DATE, String STATUS, String STATUS_DT, String END_DT,
			String BR_NO, String CONTRACT_CO, String SWFT,
			BigDecimal FACT_AMT_CO, BigDecimal FACT_AMT_CO_NT, String CONTRACT,
			BigDecimal FACT_AMT_T, BigDecimal BAL_T, BigDecimal INT_AMT_T,
			BigDecimal FINAL_EAD, String REUSE);

	public void DW_L120S21B_delete(String mainId);

	// J-111-0083_05097_B1002 Web e-Loan企金授信額度明細表新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
	// J-113-0102_05097_B1001 修改e-Loan LGD之公司保證回收率估算規則
	public void DW_L120S21B_insert(String UNID, String PROJECT_NO,
			String CUSTID, String DATA_DATE, String STATUS, String STATUS_DT,
			String END_DT, BigDecimal LGD_MOWVER1, BigDecimal LGD_MOWVER2,
			BigDecimal EAD_MOWVER1, BigDecimal EAD_MOWVER2, String BR_NO,
			String CONTRACT, String CREDIT_FLAG, String GUR_FLAG,
			String UNION_FLAG, BigDecimal SYND_AMT_NT, BigDecimal UNION_AMT_NT,
			String FACT_CURR, BigDecimal FACT_AMT, BigDecimal FACT_AMT_T,
			BigDecimal BAL_T, BigDecimal INT_AMT_T, BigDecimal FINAL_EAD,
			BigDecimal PRO_R, BigDecimal COLL_R, BigDecimal CREDIT_R,
			BigDecimal UNSEC_R, BigDecimal EXP_R, BigDecimal UNDIR_COST,
			BigDecimal WORKOUT_LGD, BigDecimal WORKOUT,
			BigDecimal RESTRUCTURE_LGD, BigDecimal RESTRUCTURE,
			BigDecimal CURE_LGD, BigDecimal CURE, BigDecimal FACT_LGD,
			BigDecimal CUST_LGD, BigDecimal CREDIT_PT, String BUSS_TYPE,
			BigDecimal BUSS_LGD, String AUTH_TYPE,
			String ISGUARANTOREFFECT_S21B, String GUARANTORID_S21B,
			String GUARANTORDUPNO_S21B, String GUARANTORRNAME_S21B,
			String GUARANTORCRDTYPE_S21B, String GUARANTORCRDTYEAR_S21B,
			String GUARANTORGRADEORG_S21B, String GUARANTORGRADENEW_S21B,
			String GUARANTORCPTLCURR_S21B, BigDecimal GUARANTORCPTLAMT_S21B,
			BigDecimal GUARANTORCPTLUNIT_S21B, String GUARANTORNTCODE_S21B,
			String GUARANTORSTOCKSTATUS_S21B, String GUARANTORSTOCKNUM_S21B,
			String GUARANTORCRDTYPE2_S21B, String GUARANTORCRDTYEAR2_S21B,
			String GUARANTORGRADEORG2_S21B, String GUARANTORGRADENEW2_S21B,
			String GUARANTORSTKCATNM_S21B, String GUARANTORCRDTYPE3_S21B,
			String GUARANTORCRDTYEAR3_S21B, String GUARANTORGRADEORG3_S21B,
			String GUARANTORGRADENEW3_S21B, String GUARANTORSTKCATNM3_S21B);

	public void DW_L120S21C_delete(String mainId);

	public void DW_L120S21C_insert(String UNID, String PROJECT_NO,
			String DATA_DATE, String STATUS, String STATUS_DT, String END_DT,
			String BR_NO, String CONTRACT, String COLKIND, String COLCURR,
			BigDecimal COLTIMEVALUE, BigDecimal COLPRERGSTAMT,
			BigDecimal COLRGSTAMT, String COLCOUSEFLAG,
			BigDecimal COLSHARERATE, BigDecimal COLRATE,
			BigDecimal COLESTRECOVERY, BigDecimal COLRGSTRECOVERY,
			BigDecimal COLRECOVERY, BigDecimal COLRECOVERYTWD,
			String CMSBRANCH, String CMSTYPECD, String CMSCUSTID,
			String CMSDUPNO, String CMSCOLLNO, String CMSOID,
			String CMSCOLLKEY, String COLLTYPE, BigDecimal CMSGRTRT,
			String UNIONFLAG_S21C, String UNIONCURR_S21C,
			BigDecimal SYNDAMT_S21C, BigDecimal UNIONAMT_S21C);

	public List<Map<String, Object>> findlnf025OvsCoByCntrNoForLgd(
			List<String> cntrNos);

	public List<Map<String, Object>> findSumDW_ASLNDAVGOVSByCntrNo(String cntrNo);

	public List<Map<String, Object>> find_OTS_TRPAYLG(String custId,
			String dupNo, String begDate, String endDate);

	public List<Map<String, Object>> findlnf025OvsByCntrNoCo(String cntrNoCo);

	/**
	 * OTS_ACINSRT 存放款平訊利率表
	 * 
	 * @param CYC_DT
	 *            資料日
	 * @param BR_CD
	 *            分行代號
	 * @param DATA_DEF
	 *            利差類別(狹義:A/廣義:B)
	 * @param CUR_CD
	 *            幣別
	 * @param DW_DEP_CD
	 *            DW部門別 D = DBU、L = DBU+OBU
	 * @param DATA_TYP
	 *            是否含存行(Y/N)
	 * @return
	 */
	public List<Map<String, Object>> findOTS_ACINSRTByColumn(String CYC_DT,
			String BR_CD, String DATA_DEF, String CUR_CD, String DW_DEP_CD,
			String DATA_TYP);

	/**
	 * J-111-0551 取得客戶之海外額度序號(含已銷戶)
	 */
	public List<String> findDWLnquotovCntrnoByCustId(String custId);

	/**
	 * J-111-0551 確認額度序號不存在於海外額度檔
	 */
	public List<String> findDWLnquotovWithoutCntrno(List<String> cntrnoList);

	/**
	 * J-111-0443_05097_B1002 Web e-Loan企金授信開發授信BIS評估表
	 */
	public List<Map<String, Object>> findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE_None_Loan(
			String custKey, String dupNo, String dateS, String dateE);

	/**
	 * J-111-0443_05097_B1002 Web e-Loan企金授信開發授信BIS評估表
	 */
	public List<Map<String, Object>> findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE_None_Loan(
			String custKey, String dupNo, String dateS, String dateE);

	/**
	 * 取得表外科目CCF
	 * 
	 * @param acKey
	 * @return
	 */
	public List<Map<String, Object>> find_OTS_BSL2ACRULE_byAcKey(String acKey);

	public List<Map<String, Object>> findOtsCustInfoBy(
			List<String> contentList, String custId, String dupNo);

	/**
	 * 刪除DWADM.DW_ELOAN_APPLY上之資料by C122M01A.OID
	 * 
	 * @param oidList
	 *            C122M01A.OID
	 * @return
	 */
	public void DW_ELOAN_APPLY_DELETE(String[] oidArry);

	/**
	 * 寫入資料至DWADM.DW_ELOAN_APPLY by C122M01A.OID
	 * 
	 * @param dataList
	 *            資料值List
	 * @return
	 */
	public void DW_ELOAN_APPLY_INSERT(List<Object[]> dataList);

	/**
	 * 刪除DWADM.DW_ELOAN_REQUEST上之資料by L120M01A.OID
	 * 
	 * @param oidArry
	 *            L120M01A.OID
	 * @return
	 */
	public void DW_ELOAN_REQUEST_DELETE(String[] oidArry);

	/**
	 * 寫入資料至DWADM.DW_ELOAN_REQUEST by L120M01A.OID
	 * 
	 * @param dataList
	 *            資料值List
	 * @return
	 */
	public void DW_ELOAN_REQUEST_INSERT(List<Object[]> dataList);

	public Map<String, Object> findDwLncntrovsByCntrNo(String cntrNo);

    List<Map<String, Object>> findDW_ESGLOANLIST_SSByType(String type);

	/**
	 *  J-113-0402 依本行「企業授信ESG風險評級作業須知」第七條，為利追蹤ESG風險評級辦理頻率，新增貸後通知及報表(LLDLN350.LLWLN350)內容如下：
	 * 	1.請每月於授信管理系統「貸後管理追蹤檢核表」，針對ESG風險評級即將屆期之案件進行通知(中、低ESG風險至少每五年辦理一次、高ESG風險至少每年辦理一次，例如中風險最近一次評等日期112.12.10，屆期日為117.12.10，貸後通知117.11月)，且於本行尚有有效額度者，發送額度最大分行，通知分行重新辦理評級。
	 * 	2.追蹤事項通知內容提示「ESG風險評級即將屆期，請重新辦理ESG風險評級」。
	 * @param foDate
	 * @return
	 */
    List<Map<String, Object>> findDW_ESGLOANLIST_SSByRiskRating(String foDate);
}
