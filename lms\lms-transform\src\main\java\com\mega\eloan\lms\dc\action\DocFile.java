package com.mega.eloan.lms.dc.action;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Collections;

import javax.activation.MimetypesFileTypeMap;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.util.CodeUtils;
import com.mega.eloan.lms.dc.util.DXLUtil;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * DocFile
 * </pre>
 * 
 * @since 2013/2/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/2/4,Bang,new
 *          </ul>
 */
public class DocFile extends BaseAction {

	private Logger logger = LoggerFactory.getLogger(DocFile.class);
	private String loadDB2DataPath = "";
	private String dxlDirRootPath = "";
	private String loadDB2FilesPath = null;
	private PrintWriter logsCw = null;// 輸出log

	private final String SYS_CODE = "LMS";// 系統名稱企個金共用
	private String sysYear = "2013";// default 2013(原則上是案件號碼之年度)
	private String fieldId = "";
	private String currDTime = "";

	/**
	 * constructor
	 */
	public DocFile() {
	}

	/**
	 * init
	 * 
	 * @param schema
	 * @param dxlDirRootPath
	 * @param loadDB2DataPath
	 * @param loadDB2FilesPath
	 * @param logsCw
	 * @return
	 */
	public void init(String schema, String dxlDirRootPath,
			String loadDB2DataPath, String loadDB2FilesPath, PrintWriter logsCw) {
		if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(schema)) {
			this.fieldId = "lmsCtrDoc";
		} else {
			this.fieldId = "clsCtrDoc";
		}
		this.loadDB2DataPath = loadDB2DataPath;
		this.dxlDirRootPath = dxlDirRootPath;
		this.loadDB2FilesPath = loadDB2FilesPath;
		this.logsCw = logsCw;
	}

	public void create() {
		this.logsCw.println("正在處理附加檔案及圖檔...");
		this.currDTime = Util.getCurrentTimestamp();
		try {
			File mFile = new File(this.loadDB2DataPath + File.separator
					+ "BDOCFILE.txt");
			if (mFile.isFile()) {
				mFile.delete();
			}

			PrintWriter out = new PrintWriter(new BufferedWriter(
					new OutputStreamWriter(new FileOutputStream(new File(mFile
							.getAbsolutePath())), TextDefine.ENCODING_UTF8)), true);

			File file = new File(this.dxlDirRootPath);
			for (File fe : file.listFiles()) {
				// 第一層 分行代號
				if (fe.isDirectory()) {
					ArrayList<File> lst = new ArrayList<File>();// 檔案清單物件
					ArrayList<File> imgLst = new ArrayList<File>();

					for (File subDir : DXLUtil.list_subdir(fe)) {
//					for (File subDir : fe.listFiles()) {
						// 第二層 notes view name
						if (subDir.isDirectory()) {
							// 第三層 ==================檔案
							this.logsCw.println("\n---分行代號-:" + DXLUtil.proc_readline(fe.getName())
									+ ":---notes view name---:"
									+ DXLUtil.proc_readline(subDir.getName()) );
							long readViewTime = System.currentTimeMillis();

							File filePath = new File(subDir.getAbsolutePath()
									+ this.configData.getFilesPath());
							if (filePath.isDirectory()) {
								for (File o : filePath.listFiles()) {
									if (o.isFile()) {
										lst.add(o);
									}
								}
							}
							// 圖片
							File imgPath = new File(subDir.getAbsolutePath()
									+ this.configData.getImagesPath());
							if (imgPath.isDirectory()) {
								for (File o : imgPath.listFiles()) {
									if (o.isFile()) {
										imgLst.add(o);
									}
								}
							}
							long cost2 = System.currentTimeMillis()
									- readViewTime;
							this.logsCw.print(" , 結束時間 :" + Util.getNowTime()
									+ " ,TOTAL TIME :"
									+ Util.millis2minute(cost2));
						}
					}
					// 一般附件
					Collections.sort(lst);
					this.getData(out, lst);

					// ckeditor圖片
					Collections.sort(imgLst);
					this.getImgData(out, imgLst);
				}
			}
			out.close();
			out = null;
		} catch (Exception e) {
			String errmsg = "產生BDOCFILE.txt時出現錯誤 ";
			this.logger.error(errmsg, e);
			this.logsCw.println(errmsg);
			e.printStackTrace(this.logsCw);
			throw new DCException(errmsg, e);
		}
	}

	private static final boolean isEnEnv = StringUtils.trimToEmpty(
			System.getProperty("user.language")).indexOf("en") != -1;

	public void getData(PrintWriter out, ArrayList<File> lst) throws Exception {
		String wkMID = "";
		int seq = 0;
		for (File f : lst) {
			// Ex:FLMS130M01_1234567A87E7F9D5482572E500072FAB_2001_seq_信義路總清單90.05.19.xls
			String name = f.getName();
			if (isEnEnv && this.configData.isOnlineMode()) {
				name = CodeUtils.getUTF8String(name);
			}
			String[] fParm = name.split("_");
			this.sysYear = fParm[fParm.length - 3];
			if (fParm[1].length() < 32) {
				continue;
			}

			if (fParm[1].equals(wkMID)) {
				seq++;
			} else {
				seq = 1;
			}
			wkMID = fParm[1];
			String brn = new File(
					new File(new File(f.getParent()).getParent()).getParent())
					.getName();
			printOut(out, fParm[1], brn, seq, f);
		}
	}

	public void getImgData(PrintWriter out, ArrayList<File> lst)
			throws Exception {
		String wkMID = "";
		int seq = 0;
		for (File f : lst) {
			// Ex:FLMS130M01_1234567A87E7F9D5482572E500072FAB_2001_seq_信義路總清單90.05.19.xls
			String name = f.getName();
			if (isEnEnv && this.configData.isOnlineMode()) {
				name = CodeUtils.getUTF8String(name);
			}
			String[] fParm = name.split("_");
			this.sysYear = fParm[fParm.length - 3];
			String oid = fParm[fParm.length - 1].split("\\.")[0];
			if (fParm[1].length() < 32) {
				continue;
			}

			if (fParm[1].equals(wkMID)) {
				seq++;
			} else {
				seq = 1;
			}
			wkMID = fParm[1];
			String brn = new File(
					new File(new File(f.getParent()).getParent()).getParent())
					.getName();
			printOut(out, oid, "inserImgUploadFile", fParm[1], brn, seq, f);
		}
	}

	private StringBuffer sb = new StringBuffer();

	public void printOut(PrintWriter out, String mainId, String brn, int seq,
			File f) {
		this.printOut(out, null, null, mainId, brn, seq, f);
	}

	public void printOut(PrintWriter out, String oid, String fieldId,
			String mainId, String brn, int seq, File f) {
		sb.setLength(0);
		// oid如果為空的則自動產生一個
		if (StringUtils.isBlank(oid)) {
			oid = Util.getOID();
		}
		if (StringUtils.isBlank(fieldId)) {
			fieldId = this.fieldId;
		}
		// oid
		sb.append(oid).append(TextDefine.SYMBOL_SEMICOLON);
		// mainId
		sb.append(mainId).append(TextDefine.SYMBOL_SEMICOLON);
		// 子系統代號
		sb.append(this.SYS_CODE).append(TextDefine.SYMBOL_SEMICOLON);
		// 分行代號
		sb.append(brn).append(TextDefine.SYMBOL_SEMICOLON);
		// 建檔年份
		sb.append(this.sysYear).append(TextDefine.SYMBOL_SEMICOLON);
		// 欄位名稱
		sb.append(fieldId).append(TextDefine.SYMBOL_SEMICOLON);
		// 檔案內容類型
		sb.append(new MimetypesFileTypeMap().getContentType(f)).append(
				TextDefine.SYMBOL_SEMICOLON);
		// 原始檔案名稱
		String name = f.getName();
		try {
			if (isEnEnv && this.configData.isOnlineMode()) {
				name = CodeUtils.getUTF8String(name);
			}
		} catch (Exception e) {
			name = f.getName();
		}
		sb.append(name).append(TextDefine.SYMBOL_SEMICOLON);
		// 上傳時間
		sb.append(this.currDTime).append(TextDefine.SYMBOL_SEMICOLON);
		// 檔案說明
		sb.append(TextDefine.SYMBOL_SEMICOLON);
		// 邏輯刪除時間
		sb.append(TextDefine.SYMBOL_SEMICOLON);
		// 順序編號
		sb.append(seq).append(TextDefine.SYMBOL_SEMICOLON);
		// 總頁數
		sb.append("0").append(TextDefine.SYMBOL_SEMICOLON);
		// 檔案大小
		sb.append(f.length()).append(TextDefine.SYMBOL_SEMICOLON);
		// FLAG
		sb.append(TextDefine.SYMBOL_SEMICOLON);
		// PID
		sb.append(TextDefine.EMPTY_STRING);

		copy(f, brn, mainId, oid, fieldId);
		out.println(sb.toString());
	}

	public void copy(File srcFile, String brn, String mainId, String oid,
			String fieldId) {

		if (StringUtils.isEmpty(fieldId)) {
			fieldId = this.fieldId;
		}

		try {
			byte[] data = new byte[1];
			// ps:路徑不可變更
			File fileDir = new File(this.loadDB2FilesPath + File.separator
					+ this.SYS_CODE + File.separator + brn + File.separator
					+ this.sysYear + File.separator + mainId + File.separator
					+ fieldId);
			if (!fileDir.isDirectory()) {
				fileDir.mkdirs();
			}
			int startIndex = srcFile.getName().lastIndexOf(46) + 1;
			int endIndex = srcFile.getName().length();
			String subName = srcFile.getName().substring(startIndex, endIndex);
			// 2013-03-20 Modify By Bang:附檔名若含有中文則不加附檔名,否則解壓會出錯
			File desFile = null;
			if (Util.isChineseChr(subName)) {
				desFile = new File(fileDir.getAbsolutePath() + File.separator
						+ oid);
			} else {
				desFile = new File(fileDir.getAbsolutePath() + File.separator
						+ oid + "." + subName);
			}
			BufferedInputStream bufferedInputStream = new BufferedInputStream(
					new FileInputStream(srcFile));
			BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(
					new FileOutputStream(desFile));

			while (bufferedInputStream.read(data) != -1) {
				bufferedOutputStream.write(data);
			}

			// 將緩衝區中的資料全部寫出
			bufferedOutputStream.flush();

			// 關閉串流
			bufferedInputStream.close();
			bufferedOutputStream.close();
		} catch (Exception e) {
			String errmsg = "複製檔案至load_db2 : FILES目錄下時出現錯誤";
			this.logger.error(errmsg, e);
			this.logsCw.println(errmsg);
			e.printStackTrace(this.logsCw);
			throw new DCException(errmsg, e);
		}
	}

}
