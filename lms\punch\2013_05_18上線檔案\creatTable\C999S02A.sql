---------------------------------------------------------
-- LMS.C999S02A 個金約據書增補條款內容檔
---------------------------------------------------------
--DROP TABLE LMS.C999S02A;
CREATE TABLE LMS.C999S02A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	SEQ           DECIMAL(5,0)  not null,
	TYPE          CHAR(3)       not null,
	JSONDATA      VARCHAR(3072),
	CREATOR       CHAR(6)      ,
	CREATE<PERSON><PERSON>    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_C999S02A PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC999S02A01;
CREATE UNIQUE INDEX LMS.XC999S02A01 ON LMS.C999S02A   (MAINID, SEQ, TYPE);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C999S02A IS '個金約據書增補條款內容檔';
COMMENT ON LMS.C999S02A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	SEQ           IS '序號', 
	TYPE          IS '增補條款項目', 
	JSONDATA      IS '增補條款內容', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
