/* 
 * VL784S07A01.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.VL784S07A01;

/** 常董會明細 **/
public interface VL784S07A01Dao extends IGenericDao<VL784S07A01> {
	/** 取得常董會明細
	 * @param apprYY
	 * @return
	 */
	public List<VL784S07A01> findByApprYY(String apprYY);

}