/*
 *
 * Copyright (c) 2009-2012 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.i18n;

import java.io.InputStream;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Locale;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import com.iisigroup.cap.utils.CapAppContext;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapString;

/**
 * <p>
 * MessageBundleScriptCreator.<br>
 * prorerties轉換
 * </p>
 *
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/12,iristu,new
 *          </ul>
 */
public class MessageBundleScriptCreator {
    protected final static Logger LOGGER = LoggerFactory.getLogger(MessageBundleScriptCreator.class);

    /**
     * i18n key region<br>
     * {@value #I18NKEY_REG}
     */
    protected final static String I18NKEY_REG = "\\w+$";
    /**
     * 預設濾器
     */
    protected static Set<String> defaultFilter;

    static {
        defaultFilter = new HashSet<String>();
        defaultFilter.add("js.");
    }

    /**
     * 將properties轉成json格式
     *
     * @param i18nPath
     *            i18nPath
     * @return String
     */
    public static String generateJson(String i18nPath) {
        return generateJson(loadProperties(i18nPath), null);

    }

    /**
     * 將properties轉成json格式
     *
     * @param i18nPath
     *            i18nPath
     * @param filterList
     *            濾器
     * @return String
     */
    public static String generateJson(String i18nPath, Set<String> filterList) {
        return generateJson(loadProperties(i18nPath), filterList);

    }

    /**
     * 將properties轉成json格式
     *
     * @param props
     *            properties
     * @param filterList
     *            濾器
     * @return String
     */
    public static String generateJson(Properties props, Set<String> filterList) {
        CapAjaxFormResult result = new CapAjaxFormResult();
        String filterReg = filterList == null ? "" : generateFilterReg(filterList);
        if (props != null && !props.isEmpty()) {
            if (CapString.isEmpty(filterReg)) {
                for (Entry<Object, Object> entry : props.entrySet()) {
                    result.set((String) entry.getKey(), (String) entry.getValue());
                }
            } else {
                for (Entry<Object, Object> entry : props.entrySet()) {
                    if (CapString.checkRegularMatch((String) entry.getKey(), filterReg)) {
                        result.set(((String) entry.getKey()).replaceAll("js.", ""), (String) entry.getValue());
                    }
                }
            }
        }
        return result.getResult();
    }

    /**
     * 將properties轉成json格式
     *
     * @param i18nKeyName
     *            i18n key name
     * @param props
     *            properties
     * @return String
     */
    public static String createScript(String i18nKeyName, Properties props) {
        return createScript(i18nKeyName, props, null);
    }

    /**
     * 將properties轉成json格式
     *
     * @param i18nKeyName
     *            i18n key name
     * @param props
     *            properties
     * @param filterList
     *            濾器
     * @return String
     */
    public static String createScript(String i18nKeyName, Properties props, Set<String> filterList) {

        String message = generateJson(props, filterList);
        if (CapString.isEmpty(message)) {
            return null;
        }
        i18nKeyName = i18nKeyName.toLowerCase().replace("page", "").replace("panel", "");
        StringBuffer script = new StringBuffer();
        script.append("require(['mega.eloan.properties'], function(){i18n.set(\"").append(i18nKeyName.toLowerCase().replace("page", "")).append("\",").append(message).append(");});");
        return script.toString();
    }

    /**
     * 將properties轉成json格式
     *
     * @param i18nPath
     *            i18n Path
     * @return String
     */
    public static String createScript(String i18nPath) {
        return createScript(CapString.getRegularMatch(i18nPath, I18NKEY_REG), loadProperties(i18nPath), null);
    }

    /**
     * 將properties轉成json格式
     *
     * @param i18nPath
     *            i18n Path
     * @param filterList
     *            濾器
     * @return String
     */
    public static String createScript(String i18nPath, Set<String> filterList) {
        return createScript(CapString.getRegularMatch(i18nPath, I18NKEY_REG), loadProperties(i18nPath), filterList);
    }

    /**
     * 讀取 i18n 檔案
     *
     * @param i18nPath
     *            i18nPath
     * @return Properties
     */
    private static Properties loadProperties(String i18nPath) {
        Properties prop = new Properties();
        Locale locale = null;
        try {
            locale = LocaleContextHolder.getLocale();
            if (locale == null) {
                locale = Locale.getDefault();
            }
        } catch (Exception e) {
            locale = Locale.getDefault();
        }
        String i18nFile = null;
        InputStream is = null;
        try {
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            i18nFile = new StringBuffer("classpath*:i18n/**/").append(i18nPath).append("_").append(locale.toString()).append(".properties").toString();
            Resource[] resources = resolver.getResources(i18nFile);
            Resource rs = null;
            if (resources.length > 0 && resources[0] != null) {
                rs = resources[0];
                is = rs.getInputStream();
                prop.load(is);
            } else {
                // 抓取沒有地區後綴的properties
                i18nFile = new StringBuffer("classpath*:i18n/**/").append(i18nPath).append(".properties").toString();
                Resource[] resourcesNoLocal = resolver.getResources(i18nFile);
                rs = null;
                if (resourcesNoLocal.length > 0 && resourcesNoLocal[0] != null) {
                    rs = resourcesNoLocal[0];
                    is = rs.getInputStream();
                    prop.load(is);
                }
            }

        } catch (Exception e) {
            LOGGER.warn("can't load " + i18nPath);
        } finally {
            IOUtils.closeQuietly(is);
        }
        return prop;
    }

    /**
     * 產生 filter 比對表示式
     *
     * @param filterList
     *            濾器
     * @return String
     */
    private static String generateFilterReg(Set<String> filterList) {
        if (filterList == null) {
            filterList = defaultFilter;
        }
        if (!filterList.isEmpty()) {
            StringBuffer regSb = new StringBuffer("^(");
            for (Iterator<String> it = filterList.iterator(); it.hasNext();) {
                regSb.append(it.next()).append(CapConstants.VALUES_SEPARATOR);
            }
            regSb.deleteCharAt(regSb.length() - 1).append(")");
            return regSb.toString();
        } else {
            return null;
        }

    }

    /**
     * 取得組件來源<br>
     * 呼叫 {@linkplain #getComponentResource(String) getComponentResource}
     * 
     * @param component
     * @return {@code getComponentResource(component.getSimpleName())}
     */
    public static Properties getComponentResource(Class<?> component) {
        return getComponentResource(component.getSimpleName());
    }

    /**
     * 取得組件來源
     * 
     * @param prefix
     * @return
     */
    public static Properties getComponentResource(String prefix) {
        Properties properties = new Properties();
        properties.putAll(CapAppContext.getMessages(prefix, LocaleContextHolder.getLocale()));
        return properties;
    }

    public static Properties getComponentResource(Class<?> component, Locale locale) {
        Properties properties = new Properties();
        properties.putAll(CapAppContext.getMessages(component.getSimpleName(), locale));
        return properties;
    }

    /**
     * Add I18N Base name Key
     * 
     * @param key
     */
    @SuppressWarnings("unchecked")
    public static void addI18BasenameKey(String key) {
        LinkedHashSet<String> keySet = (LinkedHashSet<String>) RequestContextHolder.getRequestAttributes().getAttribute("basenameKey", RequestAttributes.SCOPE_REQUEST);
        if (keySet == null) {
            keySet = new LinkedHashSet<String>();
        }
        keySet.add(key);
        RequestContextHolder.getRequestAttributes().setAttribute("basenameKey", keySet, RequestAttributes.SCOPE_REQUEST);
    }
}
