var pageAction = {
    handler: 'lms9020formhandler',
    grid: null,
    build: function(){
        pageAction.grid = $("#gridview").iGrid({
            //localFirst: true,
            handler: 'lms9020gridhandler',
            action: "queryL902m01a",
            width: 785,
            height: 350,
            sortname: 'peNo',
            sortorder: 'asc',
            shrinkToFit: true,
            autowidth: false,
            postData: {
                mainDocStatus: viewstatus,
                rowNum: 15
            },
            rowNum: 15,
            //multiselect : true,
            colModel: [{
                colHeader: "oid",
                name: 'oid',
                hidden: true //是否隱藏
            }, {
                colHeader: "mainId",
                hidden: true, //是否隱藏
                name: 'mainId'
			},{
				colHeader : "docURL",
				hidden : true, //是否隱藏
				name : 'docURL'	
            }, {
                colHeader: i18n.lms9020v01["mainGrid.index1"], //私募基金代碼
                align: "center",
                width: 20, //設定寬度
                sortable: true, //是否允許排序
                //formatter : 'click',
                //onclick : function,
                name: 'peNo'
            }, {
                colHeader: i18n.lms9020v01["mainGrid.index2"], //私募基金名稱 
                align: "left",
                width: 150,
                sortable: true,
                formatter: 'click',
                onclick: pageAction.openBox,
                name: 'peName'
			 
			 }, {
                colHeader: i18n.lms9020v01["mainGrid.index3"], //異動人員
                align: "left",
                width: 40,
                sortable: true,
                name: 'updater'	
			}, {
                colHeader: i18n.lms9020v01["mainGrid.index4"], //異動日期
                align: "left",
                width: 50,
                sortable: true,
                name: 'updateTime'		
			}, {
                colHeader: i18n.lms9020v01["mainGrid.index5"], //停用註記
                align: "center",
                width: 20, //設定寬度
                sortable: true, //是否允許排序
                name:'isClosed'
			}, {
                colHeader: i18n.lms9020v01["mainGrid.index6"], //停用日期
                align: "left",
                width: 50,
                sortable: true,
                name: 'deletedTime'		
            }],
            ondblClickRow: function(rowid){//同修改
                var data = pageAction.grid.getRowData(rowid);
                pageAction.openBox(null, null, data);
            }
        });
        //build addThick selectes
         
		
        //build button 
        //查詢
        $("#buttonPanel").find("#btnAdd").click(function(){
            pageAction.newDoc();
        }).end().find("#btnView").click(function(){
            //調閱	    
            var row = pageAction.grid.getGridParam('selrow');
            if (!row) {
                // action_004=請先選擇需「調閱」之資料列
                return CommonAPI.showMessage(i18n.def["action_004"]);
            }
            else {
                var result = $("#gridview").getRowData(row);
                pageAction.openBox(null, null, result);
            }
        }).end().find("#btnDelete").click(function(){
            //刪除
            var row = pageAction.grid.getGridParam('selrow');
            if (!row) {
                // includeId.selData=請選擇一筆資料!!
                return CommonAPI.showMessage(i18n.def["includeId.selData"]);
            }
            else {
                // confirmDelete=是否確定刪除?
				//msg.004=若私募基金尚有轄下企業，則只會標記刪除，不會真正刪除
                CommonAPI.confirmMessage(i18n.def["confirmDelete"]+i18n.lms9020v01["msg.004"], function(b){
                    if (b) {
                        var result = $("#gridview").getRowData(row);
                        pageAction.startDel(null, null, result);
                    }
                });
            }
        });
    },
    /**
     * 開始刪除資料
     */
    startDel: function(cellvalue, options, rowObject){
        $.ajax({
            handler: pageAction.handler,
            action: 'startDel',
            data: {
                mainId: rowObject.mainId
            },
            success: function(response){
                // 更新Grid
                pageAction.reloadGrid();
            }
        });
    },
	/**
     * 新增資料(包含建立主檔與明細檔)
     */
    newDoc: function(){
	    $("#newDocInfo").thickbox({
            title: i18n.def["newData"],//'新增',
            width: 800,
            height: 200,
            modal: true,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                'sure': function(){
                    var $newDocForm = $("#newDocForm");
                    var peNo = $newDocForm.find("#peNo").val();
                    var peName = $newDocForm.find("#peName").val();
                   
                    if ($newDocForm.valid()) {
						$.thickbox.close();
						
                        $.ajax({
				            handler: pageAction.handler,
				            action: 'newDoc',
				            data: {
				                peNo: peNo,
				                peName: peName
				            },
				            success: function(response){
				                // 更新Grid
				                pageAction.reloadGrid();
				                // 查詢後開啟視窗
				                pageAction.openBox(null, null, response);
				            }
				        });
                    }
                },
                'cancel': function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });

       
    
        
    },
    /**
     * 開啟視窗
     */
    openBox: function(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        var url = '..' + rowObject.docURL;
        $.form.submit({
            url: url,
            data: {
                mainDocStatus: viewstatus,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                docURL: rowObject.docURL,
                oid: rowObject.oid
            },
            target: "_blank"
        });
    },
    /**
     * 取得資料表之選擇列
     */
    getRowData: function(){
        var row = pageAction.grid.getGridParam('selrow');
        var data;
        if (row) {
            data = pageAction.grid.getRowData(row);
        }
        else {
            MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["grid.selrow"]);
        }
        return data;
    },
    /**
     * 重整資料表
     */
    reloadGrid: function(data){
        if (data) {
            pageAction.grid.jqGrid("setGridParam", {
                posinputata: data,
                page: 1,
                search: true
            }).trigger("reloadGrid");
        }
        else {
            pageAction.grid.trigger('reloadGrid');
        }
    }
}

$(function(){
    pageAction.build();
    $("#getMaxNumber").click(function(){
        var $newDocForm = $("#newDocForm");
         
		$.ajax({
	        handler: pageAction.handler,
	        action: 'getMaxNumber',
	        data: {
			},
			success: function(response){
               $newDocForm.find("#peNo").val(response.maxNo);
            }
		
		});

    });
});
