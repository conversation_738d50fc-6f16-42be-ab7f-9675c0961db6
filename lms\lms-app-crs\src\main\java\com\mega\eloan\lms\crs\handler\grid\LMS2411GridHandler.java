package com.mega.eloan.lms.crs.handler.grid;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;


import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.eloan.lms.model.L140M01A;

@Scope("request")
@Controller("lms2411gridhandler")
public class LMS2411GridHandler extends AbstractGridHandler {

	@Resource
	DocFileService docFileService;

	@Resource
	RetrialService retrialService;

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryC241M01B(ISearch pageSetting,
			PageParameters params) throws CapException {

		C241M01A meta = retrialService.findC241M01A_oid(Util.trim(params
				.getString(EloanConstants.MAIN_OID)));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
				meta.getMainId());

		Map<String, String> m_c241m01bQuotaType = retrialService
				.get_crs_c241m01bQuotaType();
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<C241M01B> src_list = retrialService
				.sortC241M01B((List<C241M01B>) retrialService.findPage(
						C241M01B.class, pageSetting).getContent());
		for (C241M01B c241m01b : src_list) {
			Map<String, Object> row = new HashMap<String, Object>();
			LMSUtil.meta_to_map(row, c241m01b, new String[] { "oid", "mainId",
					"ynReview", "quotaNo", "loanNo", "quotaCurr", "balCurr",
					"reVolve", "guaranteeKind", "dateOfReview" });

			row.put("quotaType",
					m_c241m01bQuotaType.containsKey(c241m01b.getQuotaType()) ? m_c241m01bQuotaType
							.get(c241m01b.getQuotaType()) : c241m01b
							.getQuotaType());
			row.put("quotaAmt", NumConverter.addComma(c241m01b.getQuotaAmt()));
			row.put("balAmt", NumConverter.addComma(c241m01b.getBalAmt()));
			/*
			 * 在 notes 出現的是 2碼的科目及中文名稱 而 2碼的科目 是用 8碼的科目去join MIS.ELACNM
			 */
			row.put("subjectDesc", Util.trim(c241m01b.getSubjectName()));
			row.put("use_loan_dateF", Util.trim(TWNDate.toAD(CrsUtil
					.get_Use_Loan_FDate(c241m01b))));

			if (true) {
				String showBalCurr = "";
				String showBalAmt = "";
				if (CrsUtil.has_sBalCurrAmt(c241m01b)) {
					showBalCurr = c241m01b.getSBalCurr();
					showBalAmt = NumConverter.addComma(c241m01b.getSBalAmt());
				} else {
					showBalCurr = c241m01b.getBalCurr();
					showBalAmt = NumConverter.addComma(c241m01b.getBalAmt());
				}
				row.put("showBalCurr", showBalCurr);
				row.put("showBalAmt", showBalAmt);
			}
			// ---
			list.add(row);
		}

		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	public CapGridResult queryfile(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String fieldId = Util.nullToSpace(params.getString("fieldId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		Page<DocFile> page = docFileService.readToGrid(pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	// J-110-0505_05097_B1001 Web
	// e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
	public CapMapGridResult queryL140M01A(ISearch pageSetting,
			PageParameters params) throws CapException {

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		String parStrArr = Util.trim(params.getString("parStrArr"));
		if (Util.isNotEmpty(parStrArr)) {
			Map<String, String> rptDescMap = _rptNoMap();

			Map<String, C241M01A> oid_c241m01a_map = new HashMap<String, C241M01A>();
			Map<String, L140M01A> oid_l140m01a_map = new HashMap<String, L140M01A>();
			if (true) {
				List<String> oid_list_c241m01a = new ArrayList<String>();
				List<String> oid_list_l140m01a = new ArrayList<String>();
				for (String parStr : parStrArr.split("\\|")) {
					String[] parArr = Util.trim(parStr).split("\\^");
					String c241m01a_oid = parArr[0];
					String l140m01a_oid = parArr[2];
					// ~~~
					oid_list_c241m01a.add(c241m01a_oid);
					oid_list_l140m01a.add(l140m01a_oid);
				}
				if (oid_list_c241m01a.size() > 0) {
					for (C241M01A c241m01a : retrialService
							.findC241M01A_oid(oid_list_c241m01a)) {
						oid_c241m01a_map.put(c241m01a.getOid(), c241m01a);
					}
				}
				if (oid_list_l140m01a.size() > 0) {
					for (L140M01A l140m01a : retrialService
							.findL140M01A_oid(oid_list_l140m01a)) {
						oid_l140m01a_map.put(l140m01a.getOid(), l140m01a);
					}
				}
			}

			for (String parStr : parStrArr.split("\\|")) {
				String[] parArr = Util.trim(parStr).split("\\^");
				String c241m01a_oid = parArr[0];
				String rptNo = parArr[1];
				String l140m01a_oid = parArr[2];

				C241M01A c241m01a = oid_c241m01a_map.get(c241m01a_oid);
				L140M01A l140m01a = oid_l140m01a_map.get(l140m01a_oid);

				Map<String, Object> row = new HashMap<String, Object>();

				row.put("rptNo", rptNo);
				row.put("rptNoDesc", LMSUtil.getDesc(rptDescMap, rptNo));
				row.put("custId", c241m01a.getCustId());
				row.put("dupNo", c241m01a.getDupNo());
				row.put("cName", c241m01a.getCustName());

				row.put("oid", l140m01a.getOid());
				row.put("caseNo", l140m01a.getCaseNo());
				row.put("cntrNo", l140m01a.getCntrNo());
				row.put("cntrCustid", l140m01a.getCustId());
				row.put("cntrDupno", l140m01a.getDupNo());
				row.put("cntrCName", l140m01a.getCustName());
				// ---
				list.add(row);
			}
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	private Map<String, String> _rptNoMap() {
		Map<String, String> m = new HashMap<String, String>();
		m.put("R12", "額度明細表");
		m.put("R13", "額度批覆表");
		return m;
	}
}
