#==================================================
# \u8986\u5be9\u5831\u544a\u8868-\u5916Grid
#==================================================
L170M01a.retrialDate=\u8986\u5be9\u65e5\u671f
L170M01a.projectNo=\u8986\u5be9\u6848\u865f
L170M01a.custId=\u7d71\u4e00\u7de8\u865f
L170M01a.custName=\u4e3b\u8981\u501f\u6b3e\u4eba
L170M01a.mLoanPerson=\u7b26\u5408\u6388\u4fe1\u984d\u5ea6\u6a19\u6e96
L170M01a.mLoanPersonA=\u4e3b\u8981\u6388\u4fe1\u6236
L170M01a.lastRetrialDate=\u4e0a\u6b21\u8986\u5be9\u65e5\u671f
L170M01a.conFlag=\u514d\u8986\u5be9\u8a3b\u8a18
L170M01a.approver=\u8986\u5be9\u4eba\u54e1
L170M01a.complete=\u5b8c\u6210
#==================================================
# \u8986\u5be9\u5831\u544a\u8868-thickbox
#==================================================
L170M01a.insertData=\u65b0\u589e\u8986\u5be9\u5831\u544a\u8868
L170M01a.error1=\u5c1a\u672a\u9078\u53d6\u8cc7\u6599
L170M01a.error2=\u5c1a\u672a\u586b\u5beb\u8cc7\u6599
L170M01a.fileter=\u7be9\u9078\u689d\u4ef6