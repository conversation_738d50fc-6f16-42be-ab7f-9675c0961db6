package com.mega.eloan.lms.fms.handler.form;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.LMSLgdService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.fms.pages.LMS7500M01Page;
import com.mega.eloan.lms.fms.service.LMS7500Service;
import com.mega.eloan.lms.mfaloan.bean.ELF515;
import com.mega.eloan.lms.mfaloan.service.MisELF506Service;
import com.mega.eloan.lms.mfaloan.service.MisELF515Service;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140MM3A;
import com.mega.eloan.lms.model.L140MM3B;
import com.mega.eloan.lms.model.L140MM3C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 都更危老註記維護作業
 * </pre>
 * 
 * @since 2018
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms7500m01formhandler")
@DomainClass(L140MM3A.class)
public class LMS7500M01FormHandler extends AbstractFormHandler {

	@Resource
	LMS7500Service lms7500Service;

	@Resource
	MisELF506Service misELF506Service;

	@Resource
	MisELF515Service misELF515Service;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	BranchService branchService;

	@Resource
	LMSService lmsService;

	@Resource
	DocFileService docFileService;

	@Resource
	UserInfoService userInfoService;
	
	@Resource
	LMSLgdService lmsLgdService;

	/**
	 * 新增L140MM3A 都更危老註記維護主檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newl140mm3a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String l140mm3aMainid = "";
		L140MM3A l140mm3a = new L140MM3A();
		l140mm3a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		l140mm3a.setOwnBrId(user.getUnitNo());

		l140mm3aMainid = IDGenerator.getUUID();

		l140mm3a.setMainId(l140mm3aMainid);
		String txCode = Util.trim(params
				.getString(EloanConstants.TRANSACTION_CODE));
		l140mm3a.setTxCode(txCode);
		// UPGRADE: 待確認，URL是否正確
		l140mm3a.setDocURL(params.getString("docUrl"));
		l140mm3a.setDeletedTime(CapDate.getCurrentTimestamp());
		l140mm3a.setCntrNo(params.getString("cntrNo"));
		l140mm3a.setCustId(params.getString("custId", null));
		l140mm3a.setDupNo(params.getString("dupNo", null));
		l140mm3a.setCustName(params.getString("custName", null));

		lms7500Service.save(l140mm3a);

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(EloanConstants.OID, l140mm3a.getOid());
		result.set(EloanConstants.MAIN_ID, l140mm3a.getMainId());
		return result;
	}

	/**
	 * 查詢L140MM3A 都更危老註記維護主檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL140mm3a(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> dataMap = new HashMap<String, String>();

		if (!Util.isEmpty(oid)) {
			L140MM3A l140mm3a = lms7500Service.findModelByOid(L140MM3A.class,
					oid);
			if (l140mm3a == null) {
				// 開啟新案帶入起案的分行和目前文件狀態
				result.set(
						"docStatus",
						this.getMessage("docStatus."
								+ CreditDocStatusEnum.海外_編製中.getCode()));
				result.set("ownBrId", user.getUnitNo());
				result.set(
						"ownBrName",
						StrUtils.concat(" ",
								branchService.getBranchName(user.getUnitNo())));
				result.set("docStatusVal", CreditDocStatusEnum.海外_編製中.getCode());
			} else {
				// 有按過儲存會有 RandomCode
				if (Util.isEmpty(Util.nullToSpace(l140mm3a.getRandomCode()))) {
					dataMap = lms7500Service.getElf515Data(l140mm3a);
					// J-110-0382_05097_B1001 Web
					// e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
					dataMap.putAll(lms7500Service.getEllnseekData(l140mm3a,
							true));

					// J-111-0633_05097_B1001 Web
					// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
					dataMap.putAll(lms7500Service.getAdcInfo(l140mm3a, true));

					result.putAll(dataMap);
				} else {
					dataMap = lms7500Service.getData(l140mm3a);
					// J-110-0382_05097_B1001 Web
					// e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
					dataMap.putAll(lms7500Service.getEllnseekData(l140mm3a,
							false));
					// J-111-0633_05097_B1001 Web
					// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
					dataMap.putAll(lms7500Service.getAdcInfo(l140mm3a, false));

					result.putAll(dataMap);
				}
				result = formatResultShow(result, l140mm3a, page);
			}
		}
		/*
		 * List<L140MM3C> lastData =
		 * lms7500Service.findL140mm3csByMainId(mainId); if (lastData == null ||
		 * lastData.isEmpty()) { if (!Util.isEmpty(oid)) { L140MM3A l140mm3a =
		 * lms7500Service.findModelByOid( L140MM3A.class, oid); // 有按過儲存會有
		 * RandomCode if
		 * (Util.isEmpty(Util.nullToSpace(l140mm3a.getRandomCode()))) { dataMap
		 * = lms7500Service.getElf515Data(l140mm3a); // J-110-0382_05097_B1001
		 * Web // e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
		 * dataMap.putAll(lms7500Service.getEllnseekData(l140mm3a));
		 * result.putAll(dataMap); } result = formatResultShow(result, l140mm3a,
		 * page); } else { // 開啟新案帶入起案的分行和目前文件狀態 result.set( "docStatus",
		 * this.getMessage("docStatus." +
		 * CreditDocStatusEnum.海外_編製中.getCode())); result.set("ownBrId",
		 * user.getUnitNo()); result.set( "ownBrName", StrUtils.concat(" ",
		 * branchService.getBranchName(user.getUnitNo())));
		 * result.set("docStatusVal", CreditDocStatusEnum.海外_編製中.getCode()); } }
		 * else { if (!Util.isEmpty(oid)) { L140MM3A l140mm3a =
		 * lms7500Service.findModelByOid( L140MM3A.class, oid); dataMap =
		 * lms7500Service.getData(l140mm3a); // J-110-0382_05097_B1001 Web //
		 * e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
		 * dataMap.putAll(lms7500Service.getEllnseekData(l140mm3a));
		 * result.putAll(dataMap); result = formatResultShow(result, l140mm3a,
		 * page); } }
		 */
		// result.putAll(dataMap);

		return result;
	}

	/**
	 * 查詢不動產暨72-2相關資訊註記
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryL140MM3C(PageParameters params)
			throws CapException {

		String oid = Util.trim(params.getString("tOid"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		L140MM3C l140mm3c = lms7500Service.findModelByOid(L140MM3C.class, oid);
		String cntrNo = Util.trim(params.getString("cntrNo"));

		boolean isMegaInternalCntrNo = true;
		String cntrBranch = cntrNo.substring(0, 3);
		if (UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(cntrBranch).getBrNoFlag())
				|| UtilConstants.BrNoType.子銀行.equals(branchService.getBranch(
						cntrBranch).getBrNoFlag())) {
			isMegaInternalCntrNo = false;
		}

		// 海外額度序號都更危老案件都自行輸入，沒有母戶的資料
		result.set("isMegaInternalCntrNo", isMegaInternalCntrNo);
		if (l140mm3c != null) {

			result.putAll(DataParse
					.toResult(l140mm3c, DataParse.Delete, new String[] {
							EloanConstants.MAIN_ID, EloanConstants.OID }));

			String estateType = l140mm3c.getEstateType();
			if ("A0#".equals(estateType)
					|| "D".equals(estateType.substring(0, 1))) {
				// 這些是d類的共用欄位

				result.set("estateCityIdXX", l140mm3c.getEstateCityId());
				result.set("estateAreaIdXX", l140mm3c.getEstateAreaId());
				result.set("estateSit3NoXX", l140mm3c.getEstateSit3No());
				result.set("estateSit4NoXX", l140mm3c.getEstateSit4No());
				result.set("estateStatus_common", l140mm3c.getEstateStatus());
				result.set("overDate_common", l140mm3c.getOverDate());

				if (UtilConstants.L140M01T_estatType.公私立各級學校.equals(estateType)) {
					result.set("subject_D02", l140mm3c.getSubject());
				} else if (UtilConstants.L140M01T_estatType.醫療機構
						.equals(estateType)) {
					result.set("subject_D03", l140mm3c.getSubject());
					result.set("subjectCode_D03", l140mm3c.getSubjectCode());
				} else if (UtilConstants.L140M01T_estatType.政府廳舍
						.equals(estateType)) {
					result.set("subject_D04", l140mm3c.getSubject());
					result.set("subjectKind_D04", l140mm3c.getSubjectKind());
				} else if (UtilConstants.L140M01T_estatType.長照服務機構
						.equals(estateType)) {
					result.set("subject_D05", l140mm3c.getSubject());
					result.set("subjectKind_D05", l140mm3c.getSubjectKind());
				} else if (UtilConstants.L140M01T_estatType.社會住宅
						.equals(estateType)) {
					result.set("subject_D06", l140mm3c.getSubject());
				} else if (UtilConstants.L140M01T_estatType.A0井
						.equals(estateType)) {
					result.set("sectKind", l140mm3c.getSectKind());
					result.set("useSect", l140mm3c.getUseSect());
					result.set("useKind", l140mm3c.getUseKind());
					result.set("subjectCode_A0井", l140mm3c.getSubjectCode());
				}
				
				L140MM3C l140mm3cBeforeData = lms7500Service.findLastestL140mm3cByMainIdEstateType(l140mm3c.getMainId());
				if(l140mm3cBeforeData != null){
					result.set("before_overDate_common", l140mm3cBeforeData.getOverDate());
				}
				
			}

			result.set("_estateType", l140mm3c.getEstateType());
			result.set(
					"_estateTypeName",
					codeTypeService.findByCodeType("estateType").get(
							l140mm3c.getEstateType()));
			String estateSubType = l140mm3c.getEstateSubType();
			result.set("_estateSubType", estateSubType);

			if (UtilConstants.L140M01T_estatSubType.都更.equals(estateSubType)
					|| UtilConstants.L140M01T_estatSubType.危老
							.equals(estateSubType)
					|| UtilConstants.L140M01T_estatSubType.其它都更危老
							.equals(estateSubType)) {

				result.set("isCityRebuild", "Y");
			} else if (UtilConstants.L140M01T_estatSubType.一般
					.equals(estateSubType)) {

				result.set("isCityRebuild", "N");
			}
		}
		return result;
	}

	/**
	 * 儲存不動產暨72-2相關資訊註記
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL140MM3C(PageParameters params)
			throws CapException {

		String oid = Util.trim(params.getString("oid"));
		String mainId = Util.trim(params.getString("mainId"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		L140MM3C l140mm3c = lms7500Service.findModelByOid(L140MM3C.class, oid);
		String cntrNo = Util.trim(params.getString("cntrNo"));
		L140MM3A meta = lms7500Service.findL140mm3aByUniqueKey(mainId);

		if (l140mm3c != null) {
			String estateType = params.getString("_estateType");
			if (UtilConstants.L140M01T_estatType.都更危老.equals(estateType)) {

				String estateSubType = params.getString("estateSubType");
				if (UtilConstants.L140M01T_estatSubType.一般
						.equals(estateSubType)) {
					// 重建類別為一般，不需輸入其它名細資料
					l140mm3c.setEstateSubType(estateSubType);
					l140mm3c.setmCntrNo(null);
					l140mm3c.setEstateCityId(null);
					l140mm3c.setEstateAreaId(null);
					l140mm3c.setEstateSit3No(null);
					l140mm3c.setEstateSit4No(null);
					l140mm3c.setBuildWay(null);
					l140mm3c.setEstateStatus(null);
					l140mm3c.setLandlordNum(null);
					l140mm3c.setEstateNote(null);
					l140mm3c.setOverDate(null);
					l140mm3c.setSubTypeNote(null);
					l140mm3c.setCheckYN("Y");
				} else {
					String mCntrNo = params.getString("mCntrNo");
					String estateCityId = params.getString("estateCityId");
					String estateAreaId = params.getString("estateAreaId");
					String estateSit3No = params.getString("estateSit3No");
					String estateSit4No = params.getString("estateSit4No");
					String buildWay = params.getString("buildWay");
					String estateStatus = params.getString("estateStatus");
					String landNum = params.getString("landlordNum");
					BigDecimal landlordNum = NumberUtils.isNumber(landNum
							.replaceAll(",", "")) ? CapMath
							.getBigDecimal(landNum) : null;
					String estateNote = params.getString("estateNote");
					String subTypeNote = params.getString("subTypeNote");
					String overDate = params.getString("overDate");
					String otherDesc = params.getString("otherDesc"); //J-112-0460_12473_B1001 重建方式新增 05-其他 選項之自行輸入內容

					l140mm3c.setEstateType(estateType);
					l140mm3c.setmCntrNo(mCntrNo);
					l140mm3c.setEstateSubType(estateSubType);
					l140mm3c.setEstateCityId(estateCityId);
					l140mm3c.setEstateAreaId(estateAreaId);
					l140mm3c.setEstateSit3No(estateSit3No);
					l140mm3c.setEstateSit4No(estateSit4No);
					l140mm3c.setBuildWay(buildWay);
					l140mm3c.setEstateStatus(estateStatus);
					l140mm3c.setLandlordNum(landlordNum);
					l140mm3c.setEstateNote(estateNote);
					l140mm3c.setSubTypeNote(subTypeNote);
					l140mm3c.setOverDate(CapDate.parseDate(overDate));
					l140mm3c.setOtherDesc(otherDesc);

					String estateOwner = "";
					if (UtilConstants.L140M01T_estatSubType.危老
							.equals(estateSubType)) {
						estateOwner = params.getString("estateOwner");
					}
					l140mm3c.setEstateOwner(estateOwner);

					String isTurnOver = l140mm3c.getIsTurnOver();
					String[] checks = null;

					boolean isMegaInternalCntrNo = true;
					String cntrBranch = cntrNo.substring(0, 3);
					if (UtilConstants.BrNoType.國外.equals(branchService
							.getBranch(cntrBranch).getBrNoFlag())
							|| UtilConstants.BrNoType.子銀行.equals(branchService
									.getBranch(cntrBranch).getBrNoFlag())) {
						isMegaInternalCntrNo = false;
					}

					// 授審處管的，無母戶額度序號
					// 海外額度序號也無母戶額度序號
					if ("Y".equals(isTurnOver) || !isMegaInternalCntrNo) {
						checks = new String[] { "estateSubType",
								"estateCityId", "estateAreaId", "estateSit3No",
								"estateSit4No" };
					} else {
						checks = new String[] { "estateSubType", "mCntrNo",
								"estateCityId", "estateAreaId", "estateSit3No",
								"estateSit4No" };
					}

					l140mm3c.setCheckYN("Y");

					for (String check : checks) {
						// 先檢核這些欄位為必輸欄位
						if (Util.isEmpty(l140mm3c.get(check))) {
							l140mm3c.setCheckYN("N");
							break;
						}
					}

				}

			} else {
				l140mm3c.setEstateType(estateType);

				if ("A0#".equals(estateType)
						|| "D".equals(estateType.substring(0, 1))) {

					l140mm3c.setCheckYN("Y");

					// 這些是d類的共用欄位
					String position = params.getString("position");
					String estateCityId = params.getString("estateCityIdXX");
					String estateAreaId = params.getString("estateAreaIdXX");
					String estateSit3No = params.getString("estateSit3NoXX");
					String estateSit4No = params.getString("estateSit4NoXX");
					String siteNote = params.getString("siteNote");
					String overDate = params.getString("overDate_common");
					String estateStatus = params
							.getString("estateStatus_common");

					l140mm3c.setPosition(position);
					l140mm3c.setSiteNote(siteNote);
					l140mm3c.setOverDate(CapDate.parseDate(overDate));
					l140mm3c.setEstateStatus(estateStatus);

					if (CapString.isEmpty(position)
							|| CapString.isEmpty(overDate)
							|| CapString.isEmpty(estateStatus)) {
						l140mm3c.setCheckYN("N");
					}

					if ("0".equals(position)) {
						// 海外不需塞下列的值
						l140mm3c.setEstateCityId(estateCityId);
						l140mm3c.setEstateAreaId(estateAreaId);
						l140mm3c.setEstateSit3No(estateSit3No);
						l140mm3c.setEstateSit4No(estateSit4No);

						if (CapString.isEmpty(estateCityId)
								|| CapString.isEmpty(estateAreaId)
								|| CapString.isEmpty(estateSit3No)
								|| CapString.isEmpty(estateSit4No)) {
							l140mm3c.setCheckYN("N");
						}
					}

					if (UtilConstants.L140M01T_estatType.公私立各級學校
							.equals(estateType)) {
						String subject = params.getString("subject_D02");

						if (CapString.isEmpty(subject)) {
							l140mm3c.setCheckYN("N");
						}

						l140mm3c.setSubject(subject);
					} else if (UtilConstants.L140M01T_estatType.醫療機構
							.equals(estateType)) {
						String subject = params.getString("subject_D03");
						String subjectCode = params
								.getString("subjectCode_D03");

						if (CapString.isEmpty(subject)
								|| CapString.isEmpty(subjectCode)) {
							l140mm3c.setCheckYN("N");
						}

						l140mm3c.setSubject(subject);
						l140mm3c.setSubjectCode(subjectCode);
					} else if (UtilConstants.L140M01T_estatType.政府廳舍
							.equals(estateType)) {
						String subject = params.getString("subject_D04");
						String subjectKind = params
								.getString("subjectKind_D04");
						l140mm3c.setSubject(subject);
						l140mm3c.setSubjectKind(subjectKind);

						if (CapString.isEmpty(subject)
								|| CapString.isEmpty(subjectKind)) {
							l140mm3c.setCheckYN("N");
						}

					} else if (UtilConstants.L140M01T_estatType.長照服務機構
							.equals(estateType)) {
						String subject = params.getString("subject_D05");
						String subjectKind = params
								.getString("subjectKind_D05");
						l140mm3c.setSubject(subject);
						l140mm3c.setSubjectKind(subjectKind);

						if (CapString.isEmpty(subject)
								|| CapString.isEmpty(subjectKind)) {
							l140mm3c.setCheckYN("N");
						}

					} else if (UtilConstants.L140M01T_estatType.社會住宅
							.equals(estateType)) {
						String subject = params.getString("subject_D06");
						l140mm3c.setSubject(subject);

						if (CapString.isEmpty(subject)) {
							l140mm3c.setCheckYN("N");
						}
					} else if (UtilConstants.L140M01T_estatType.A0井
							.equals(estateType)) {
						String sectKind = params.getString("sectKind");
						String useSect = params.getString("useSect");
						String useKind = Util.trim(params.getString("useKind"));
						String subjectCode = params
								.getString("subjectCode_A0井");

						if ("0".equals(position)) {
							l140mm3c.setSectKind(sectKind);
							l140mm3c.setUseSect(useSect);
							l140mm3c.setUseKind(useKind);

							if (CapString.isEmpty(sectKind)
									|| CapString.isEmpty(useSect)
									|| CapString.isEmpty(useKind)) {
								// || CapString.isEmpty(subjectCode)) {
								l140mm3c.setCheckYN("N");
							}
						} else {
							l140mm3c.setSectKind(null);
							l140mm3c.setUseSect(null);
							l140mm3c.setUseKind(null);
						}
						l140mm3c.setSubjectCode(subjectCode);

					}
				}

			}

			lms7500Service.save(l140mm3c);

			// 一定是不動產，才會輸入排除條件
			meta.setIsBuy("Y");
			String is722 = lms7500Service.checkCaseIs72_2(meta, false);
			meta.setIs722Flag(is722);

			meta.setChkYN("");
			lms7500Service.save(meta);

			result.set("is722Flag", meta.getIs722Flag());

		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryOnLine772Flag(PageParameters params)
			throws CapException {
		String oid = params.getString("oid");
		String mainId = params.getString("mainId");
		String cntrNo = params.getString("cntrNo");

		CapAjaxFormResult result = new CapAjaxFormResult();

		L140MM3A meta = lms7500Service.findL140mm3aByUniqueKey(mainId);

		Map<String, String> dataMap = new HashMap<String, String>();

		dataMap = lms7500Service.getElf515Data(meta);

		result.putAll(dataMap);

		return result;
	}

	/**
	 * 刪除 不動產暨72-2相關資訊註記
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL140mm3c(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String deleteOid = params.getString("tOid");
		lms7500Service.deleteL140mm3cAndFile(deleteOid);

		String mainId = Util.trim(params.getString("mainId"));
		L140MM3A meta = lms7500Service.findL140mm3aByUniqueKey(mainId);

		// 一定是不動產，才會輸入排除條件
		meta.setIsBuy("Y");
		String is722 = lms7500Service.checkCaseIs72_2(meta, false);
		meta.setIs722Flag(is722);
		meta.setChkYN("");

		lms7500Service.save(meta);

		result.set("is722Flag", meta.getIs722Flag());

		return result;
	}

	/**
	 * 刪除本案全部不動產暨72-2相關資訊註記
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteCurrentL140mm3cs(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString("mainId"));
		L140MM3A meta = lms7500Service.findL140mm3aByUniqueKey(mainId);

		lms7500Service.deleteCurrentL140mm3cs(mainId);

		meta.setChkYN("");
		lms7500Service.save(meta);

		return result;
	}

	/**
	 * 快速新增不動產暨72-2相關資訊註記(只新增大項資料，如為重建類形案件，user還要再填名細資料)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult quickAddEstateDatas(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String oid = Util.trim(params.getString("oid"));
		String[] estateTypes = params.getStringArray("estateType");
		String estateSubType = params.getString("estateSubType");
		// 是否為純營運週轉金
		String isInstalment = params.getString("isInstalment");
		L140MM3A meta = lms7500Service.findL140mm3aByUniqueKey(mainId);

		Properties commPop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		String cntrNo = meta.getCntrNo();
		if (Util.isEmpty(cntrNo)) {
			// lmsL120M01A.error006=額度明細表額度序號不得為空白
			throw new CapMessageException(
					commPop.getProperty("lmsL120M01A.error006"),
					this.getClass());
		}

		String is722CntrNo = meta.getCntrNo();
		if (!cntrNo.equals(is722CntrNo)) {
			// lmsL120M01A.error046=額度序號資料與查詢銀行法72-2條控管對象額度序號資料額序號不一致
			throw new CapMessageException(
					commPop.getProperty("lmsL120M01A.error046"),
					this.getClass());
		}

		List<L140MM3C> existData = lms7500Service.findCurrentL140mm3cs(mainId);

		/**
		 * 這類的資料只能輸入一筆，前端已有檢核，但為了保險，後端也要再檢核一次
		 */
		String[] onlyOneType = new String[] {
				UtilConstants.L140M01T_estatType.A03,
				UtilConstants.L140M01T_estatType.A0N,
				UtilConstants.L140M01T_estatType.A0井,
				UtilConstants.L140M01T_estatType.A0鼠 };

		boolean onlyOneFlag = false;

		List<L140MM3C> l140mm3cs = new ArrayList<L140MM3C>();
		int tmp = 0;
		for (String estateType : estateTypes) {
			if (Util.isEmpty(estateType)) {
				continue;
			}

			// 營運週轉金 目前只能選這些項目
			if ("Y".equals(isInstalment)) {
				if (!estateType.equals("A0#")
						&& !"D".equals(estateType.substring(0, 1))) {
					// lmsL120M01A.error047=純營建工程業之營運週轉金只可選擇一般分類及A0#(廠房)
					result.set("warnMessage",
							commPop.getProperty("lmsL120M01A.error047"));
					continue;
				}
			}
			boolean match = false;
			for (L140MM3C data : existData) {
				L140MM3C l140mm3c = (L140MM3C) data;
				if (estateType.equals(l140mm3c.getEstateType())) {
					match = true;
					break;
				}

				if (!onlyOneFlag) {
					if (ArrayUtils.contains(onlyOneType,
							l140mm3c.getEstateType())) {
						onlyOneFlag = true;
					}
				}
			}
			if (match) {
				continue;
			}

			if (onlyOneFlag) {
				if (ArrayUtils.contains(onlyOneType, estateType)) {
					continue;
				}
			} else {
				if (ArrayUtils.contains(onlyOneType, estateType)) {
					onlyOneFlag = true;
				}
			}

			L140MM3C l140mm3c = new L140MM3C();
			l140mm3c.setMainId(mainId);
			l140mm3c.setEstateType(estateType);
			l140mm3c.setIsTurnOver(isInstalment);
			l140mm3c.setFlag("Y");
			if (UtilConstants.L140M01T_estatType.都更危老.equals(estateType)
					|| "D".equals(estateType.substring(0, 1))
					|| "A0#".equals(estateType)) {
				l140mm3c.setCheckYN("N");
				// 目前只有重建類型是需要補充其它欄位，所以預設為N
			} else {
				l140mm3c.setCheckYN("Y");
			}
			
			L140MM3C l140mm3cBeforeData = lms7500Service.findLastestL140mm3cByMainIdEstateType(mainId);
			if(l140mm3cBeforeData != null){
				l140mm3c.setEstateStatus(l140mm3cBeforeData.getEstateStatus());
				l140mm3c.setOverDate(l140mm3cBeforeData.getOverDate());
			}
			
			l140mm3c.setCreator(user.getUserId());
			l140mm3c.setCreateTime(new Timestamp(System.currentTimeMillis()
					+ tmp));
			l140mm3c.setUpdater(user.getUserId());
			l140mm3c.setUpdateTime(new Timestamp(System.currentTimeMillis()
					+ tmp));
			l140mm3cs.add(l140mm3c);

			tmp++;
		}
		if (l140mm3cs.size() > 0) {
			lms7500Service.saveL140mm3cList(l140mm3cs);
		}

		meta.setIsBuy("Y");
		meta.setIsInstalment(isInstalment);
		String is722 = lms7500Service.checkCaseIs72_2(meta, false);

		// 要將額度明細表的chkyn清除，讓user要定要在儲存動作，檢核輸入是否有完整
		meta.setIs722Flag(is722);
		meta.setChkYN("");
		// if(onlyOneFlag){
		// for (String estateType : estateTypes) {
		// if(ArrayUtils.contains(onlyOneType, estateType)){
		// meta.setExItem(estateType);
		// }
		// }
		// } else {
		// for (String estateType : estateTypes) {
		// meta.setExItem(estateType);
		// }
		// }
		lms7500Service.save(meta);

		result.set("is722Flag", meta.getIs722Flag());

		return result;
	}

	/**
	 * 取得已輸入的不動產暨72-2相關資訊註記類別
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getSelectedEstateType(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));

		List<L140MM3C> existData = lms7500Service.findCurrentL140mm3cs(mainId);
		JSONArray ja = new JSONArray();

		for (L140MM3C data : existData) {
			ja.add(data.getEstateType());
		}

		result.set("estateType", ja.toString());
		return result;
	}

	/**
	 * 刪除附加檔案
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteUploadFile(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String fid = params.getString("fileOid");
		if (docFileService.clean(fid)) {
			// EFD0019=INFO|刪除成功|
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		}
		return result;
	}

	/**
	 * 儲存L140MM3A 都更危老註記維護作業
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL140mm3a(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String oid = Util.trim(params.getString(EloanConstants.OID));

		String form = Util.trim(params.getString("mainPanel"));
		JSONObject jsonData = null;

		L140MM3A l140mm3a = null;
		Boolean showMsg = params.getAsBoolean("showMsg", false);
		String showMsg1 = "";
		if (Util.isNotEmpty(oid)) {
			l140mm3a = lms7500Service.findModelByOid(L140MM3A.class, oid);
			l140mm3a.setRandomCode(IDGenerator.getRandomCode());
		}
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS7500M01Page.class);

		l140mm3a.setDeletedTime(null);

		String validate = null;
		switch (page) {
		case 1:
			jsonData = JSONObject.fromObject(form);
			DataParse.toBean(jsonData, l140mm3a);
			validate = Util.validateColumnSize(l140mm3a, pop, "L140MM3A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}

			lms7500Service.save(l140mm3a);
			result.set("randomCode", l140mm3a.getRandomCode());
			break;
		}

		// J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位

		showMsg1 = this.checkSaveData1(l140mm3a, "sendBoss");
		showMsg1 = showMsg1 + (Util.notEquals(showMsg1, "") ? "<BR>" : "")
				+ this.checkSaveData2(l140mm3a, "sendBoss");
		showMsg1 = showMsg1 + (Util.notEquals(showMsg1, "") ? "<BR>" : "")
				+ this.checkSaveData3(l140mm3a, "sendBoss");

		// J-111-0633_05097_B1001 Web
		// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
		showMsg1 = showMsg1 + (Util.notEquals(showMsg1, "") ? "<BR>" : "")
				+ this.checkSaveData4(l140mm3a, "sendBoss");
		
		L140M01A l140m01a = this.lmsService.getL140m01aByCntrNoForNew(l140mm3a.getCntrNo());
		if(l140m01a != null){
			
			boolean isDerivatives = this.lms7500Service.processIsDerivatives(l140m01a.getMainId());
			if(isDerivatives){
				// J-112-0082 約定融資額度註記部分，改以問答方式
				// 此處配合衍生性商品不算約定融資額度，調整問答項目至對應選項
				l140mm3a.setExceptFlag("_");
				l140mm3a.setExceptFlagQAisY("1");
			}
			else{
				
				if (Util.equals(l140mm3a.getExceptFlag(), "_")) {
					l140mm3a.setExceptFlag("");
				}
			}
			
			showMsg1 = showMsg1 + (Util.notEquals(showMsg1, "") ? "<BR>" : "") + this.lms7500Service.checkFinancingNotesAgreed(l140mm3a);
		}

		if (Util.isEmpty(showMsg1)) {
			if (showMsg) {
				showMsg1 = RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功);
			}
			String is722 = lms7500Service.checkCaseIs72_2(l140mm3a, false);
			l140mm3a.setIs722Flag(is722);
			l140mm3a.setChkYN("Y");
			lms7500Service.save(l140mm3a);
		} else {
			if (showMsg) {

			} else {
				throw new CapMessageException(showMsg1, getClass());
			}
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, showMsg1);

		result.set(EloanConstants.OID, CapString.trimNull(l140mm3a.getOid()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(l140mm3a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(l140mm3a.getMainId()));
		result.set(
				"showCustId",
				CapString.trimNull(l140mm3a.getCustId()) + " "
						+ CapString.trimNull(l140mm3a.getDupNo()) + " "
						+ CapString.trimNull(l140mm3a.getCustName()));
		result.set("custId", CapString.trimNull(l140mm3a.getCustId()));
		result.set("dupNo", CapString.trimNull(l140mm3a.getDupNo()));
		return result;
	}

	/**
	 * 格式化顯示訊息
	 * 
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private CapAjaxFormResult formatResultShow(CapAjaxFormResult result,
			L140MM3A l140mm3a, Integer page) throws CapException {
		String mainId = l140mm3a.getMainId();
		result = DataParse.toResult(l140mm3a);
		switch (page) {
		case 1:
			// result = DataParse.toResult(l140mm3a);
			List<L140MM3B> l140mm3blist = (List<L140MM3B>) lms7500Service
					.findListByMainId(L140MM3B.class, mainId);
			if (!Util.isEmpty(l140mm3blist)) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (L140MM3B l140mm3b : l140mm3blist) {
					// 要加上人員代碼
					String type = Util.trim(l140mm3b.getStaffJob());
					String userId = Util.trim(l140mm3b.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {
						result.set("showApprId", userId + " " + value);
					} else if ("L3".equals(type)) {
						bossId.append(bossId.length() > 0 ? "<br/>" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) {
						result.set("reCheckId", userId + " " + value);
					} else if ("L5".equals(type)) {
						result.set("managerId", userId + " " + value);
					} else if ("L6".equals(type)) {
						result.set("mainApprId", userId + " " + value);
					} else if ("L7".equals(type)) {
						result.set("mainReCheckId", userId + " " + value);
					}
				}
				result.set("bossId", bossId.toString());
			}
			result.set("ownBrName",
					" " + branchService.getBranchName(l140mm3a.getOwnBrId()));

			StringBuilder cntrNo = new StringBuilder("");

			result.set("creator", lmsService.getUserName(l140mm3a.getCreator()));
			result.set("updater", lmsService.getUserName(l140mm3a.getUpdater()));
			result.set("docStatus",
					getMessage("docStatus." + l140mm3a.getDocStatus()));
			result.set("cntrNo", cntrNo.toString());
			break;
		}// close switch case
		result.set("exItemOn", "");
		result.set("exItem", CapString.trimNull(l140mm3a.getExItem()));
		// 將db存的值轉為jsonArray
		if (Util.isNotEmpty(l140mm3a.getExItemOn())) {
			String exItemOn = l140mm3a.getExItemOn();
			String[] exItemOnArray = exItemOn.split("\\|");
			JSONArray value = new JSONArray();
			for (String txt : exItemOnArray) {
				if (Util.isNotEmpty(txt)) {
					value.add(txt);
				}
			}
			result.set("exItemOn", value.toString());
		}
		result.set("showCustId", StrUtils.concat(
				CapString.trimNull(l140mm3a.getCustId()), " ",
				CapString.trimNull(l140mm3a.getDupNo()), " ",
				CapString.trimNull(l140mm3a.getCustName())));
		result.set("docStatusVal", l140mm3a.getDocStatus());
		result.set("docStatusVal", l140mm3a.getDocStatus());
		result.set("cntrNo", l140mm3a.getCntrNo());
		result.set(EloanConstants.OID, CapString.trimNull(l140mm3a.getOid()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(l140mm3a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(l140mm3a.getMainId()));
		// 可點選之項目
		result.set("etValue", "");
		String sysEstateType = Util.trim(lmsService
				.getSysParamDataValue("722_ESTATE_TYPE"));
		if (Util.notEquals(sysEstateType, "")) {
			Map<String, String> etValue = new HashMap<String, String>();
			for (String xx : sysEstateType.split(",")) {
				String txx = Util.equals(Util.trim(xx), "NULL") ? "" : xx;
				if (Util.isNotEmpty(txx)) {
					etValue.put(txx, txx);
				}
			}
			result.set("etValue", new CapAjaxFormResult(etValue));
		}

		result.set("custId", CapString.trimNull(l140mm3a.getCustId()));
		result.set("dupNo", CapString.trimNull(l140mm3a.getDupNo()));
		// 非土建融都顯示 N.A.
		// lnType 值 prodKind 顯示的文字

		// J-111-0633_05097_B1001 Web
		// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能

		// String prodKind = Util.trim(l140mm3a.getLnType());
		// if (Util.notEquals(prodKind, "33") && Util.notEquals(prodKind, "34"))
		// {
		// prodKind = "00";
		// } else {
		// Map<String, String> lnTypeMap = codeTypeService.findByCodeType(
		// "lms140_lnType", LMSUtil.getLocale().toString());
		// prodKind = Util.nullToSpace(lnTypeMap.get(prodKind));
		// }

		// result.set("lnType", Util.trim(l140mm3a.getLnType()));
		// result.set("prodKind", prodKind);
		// result.set("adcCaseNo", Util.trim(l140mm3a.getAdcCaseNo()));
		//
		// result.set("lnTypeOn", Util.trim(l140mm3a.getLnTypeOn()));
		// result.set("adcCaseNoOn", Util.trim(l140mm3a.getAdcCaseNoOn()));

		boolean showSpecialFinRisk = lms7500Service
				.needShowIsSpecialFinRisk(l140mm3a);

		result.set("showSpecialFinRisk", showSpecialFinRisk ? "Y" : "N");

		return result;
	}

	/**
	 * 刪除L140MM3A 都更危老註記維護作業
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL140mm3a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			if (lms7500Service.deleteL140mm3as(oids)) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
			}
		}
		return result;
	}

	/**
	 * 檢核資料是否已經有正確的登錄
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;

	}

	/*** 呈主管覆核(呈主管 主管覆核 拆2個method) */
	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {
		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L140MM3A l140mm3a = (L140MM3A) lms7500Service.findModelByOid(
				L140MM3A.class, oid);
		String[] formSelectBoss = params.getStringArray("selectBoss");

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		if (params.containsKey("flowAction")) {
			if (params.getBoolean("flowAction")) {
				// J-110-0382_05097_B1001 Web
				// e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
				String showMsg = this.checkSaveData2(l140mm3a, "check");
				showMsg = showMsg + (Util.notEquals(showMsg, "") ? "<BR>" : "")
						+ this.checkSaveData3(l140mm3a, "sendBoss");
				// J-111-0633_05097_B1001 Web
				// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
				showMsg = showMsg + (Util.notEquals(showMsg, "") ? "<BR>" : "")
						+ this.checkSaveData4(l140mm3a, "sendBoss");

				if (Util.isNotEmpty(showMsg)) {
					throw new CapMessageException(showMsg, getClass());
				}
			}
		}

		if (!Util.isEmpty(formSelectBoss)) {

			String manager = Util.trim(params.getString("manager"));
			List<L140MM3B> models = (List<L140MM3B>) lms7500Service
					.findListByMainId(L140MM3B.class, l140mm3a.getMainId());
			if (!models.isEmpty()) {
				lms7500Service.deleteL140mm3bs(models, false);
			}
			List<L140MM3B> l140mm3bs = new ArrayList<L140MM3B>();
			for (String people : formSelectBoss) {
				L140MM3B l140mm3b = new L140MM3B();
				l140mm3b.setCreator(user.getUserId());
				l140mm3b.setCreateTime(CapDate.getCurrentTimestamp());
				l140mm3b.setMainId(l140mm3a.getMainId());
				l140mm3b.setBranchType(user.getUnitType());
				l140mm3b.setBranchId(user.getUnitNo());
				// L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
				l140mm3b.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
				l140mm3b.setStaffNo(people);
				l140mm3bs.add(l140mm3b);
			}
			L140MM3B managerL140mm3b = new L140MM3B();
			managerL140mm3b.setCreator(user.getUserId());
			managerL140mm3b.setCreateTime(CapDate.getCurrentTimestamp());
			managerL140mm3b.setMainId(l140mm3a.getMainId());
			managerL140mm3b.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
			managerL140mm3b.setStaffNo(manager);
			managerL140mm3b.setBranchType(user.getUnitType());
			managerL140mm3b.setBranchId(user.getUnitNo());
			l140mm3bs.add(managerL140mm3b);
			L140MM3B apprL140mm3b = new L140MM3B();
			apprL140mm3b.setCreator(user.getUserId());
			apprL140mm3b.setCreateTime(CapDate.getCurrentTimestamp());
			apprL140mm3b.setMainId(l140mm3a.getMainId());
			apprL140mm3b.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
			apprL140mm3b.setStaffNo(user.getUserId());
			apprL140mm3b.setBranchType(user.getUnitType());
			apprL140mm3b.setBranchId(user.getUnitNo());
			l140mm3bs.add(apprL140mm3b);
			lms7500Service.saveL140mm3bList(l140mm3bs);
		}
		Boolean upMis = false;
		L140MM3B l140mm3bL4 = new L140MM3B();
		// 如果有這個key值表示是輸入chekDate核准日期
		if (params.containsKey("checkDate")) {
			l140mm3a.setApprover(user.getUserId());
			l140mm3a.setApproveTime(CapDate.getCurrentTimestamp());
			upMis = true;
			L140MM3B l140mm3b = lms7500Service.findL140mm3b(
					l140mm3a.getMainId(), user.getUnitType(), user.getUnitNo(),
					user.getUserId(), UtilConstants.STAFFJOB.執行覆核主管L4);
			if (l140mm3b == null) {
				l140mm3b = new L140MM3B();
				l140mm3b.setCreator(user.getUserId());
				l140mm3b.setCreateTime(CapDate.getCurrentTimestamp());
				l140mm3b.setMainId(l140mm3a.getMainId());
				l140mm3b.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
				l140mm3b.setStaffNo(user.getUserId());
				l140mm3b.setBranchType(user.getUnitType());
				l140mm3b.setBranchId(user.getUnitNo());
			}
			l140mm3bL4 = l140mm3b;
		}

		if (!Util.isEmpty(l140mm3a)) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
				if (params.containsKey("flowAction")) {
					// 退回部檢查
					if (params.getBoolean("flowAction")) {
						L140MM3B l140mm3b = lms7500Service.findL140mm3b(
								l140mm3a.getMainId(), user.getUnitType(),
								user.getUnitNo(), user.getUserId(),
								UtilConstants.STAFFJOB.經辦L1);

						if (l140mm3b != null) {
							// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						} else {
							lms7500Service.save(l140mm3bL4);
							upMis = true;
						}
					}
				}
				lms7500Service.flowAction(l140mm3a.getOid(), l140mm3a,
						params.containsKey("flowAction"),
						params.getAsBoolean("flowAction", false), upMis);
			} catch (FlowException t1) {
				logger.error(
						"[flowAction] lms7500Service.flowAction FlowException!!",
						t1);
				throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
			} catch (Throwable t1) {
				logger.error(
						"[flowAction]  lms7500Service.flowAction EXCEPTION!!",
						t1);
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}

		return new CapAjaxFormResult();
	}

	/**
	 * 查詢都更危老母戶預約額度資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryL140MM3CByMCntrNo(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mCntrNo = params.getString("mCntrNo");

		L140MM3C l140mm3c = lms7500Service.getBuildInfoByMcntrNo(mCntrNo);

		if (l140mm3c != null) {
			result.putAll(DataParse
					.toResult(l140mm3c, DataParse.Delete, new String[] {
							EloanConstants.MAIN_ID, EloanConstants.OID }));
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料));
		}

		return result;
	}

	public IResult echo_custId(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();

		result.set("custId", Util.trim(params.getString("custId")));
		result.set("dupNo", Util.trim(params.getString("dupNo")));
		result.set("custName", Util.trim(params.getString("custName")));
		result.set("cntrNo", Util.trim(params.getString("cntrNo")));
		return result;
	}

	// J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
	public String checkSaveData1(L140MM3A l140mm3a, String flowaAtion)
			throws CapException {
		String msg = "";

		// J-111-0633_05097_B1001 Web
		// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
		if (Util.notEquals(Util.trim(l140mm3a.getUpdateItem2()), "Y")
				&& Util.notEquals(Util.trim(l140mm3a.getUpdateItem3()), "Y")
				&& Util.notEquals(Util.trim(l140mm3a.getUpdateItem4()), "Y")
				&& Util.notEquals(Util.trim(l140mm3a.getUpdateItem5()), "Y")) {
			return "基本資訊頁籤之維護項目，請至少選擇一項。";
		}

		return msg;
	}

	// J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
	public String checkSaveData2(L140MM3A l140mm3a, String flowaAtion)
			throws CapException {
		String msg = "";

		if (Util.notEquals(Util.trim(l140mm3a.getUpdateItem2()), "Y")) {
			return msg;
		}

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS7500M01Page.class);
		List<L140MM3C> l140mm3cs = lms7500Service.findCurrentL140mm3cs(l140mm3a
				.getMainId());
		String custIdDupNo = (l140mm3a.getCustId() + "          ").substring(0,
				10) + l140mm3a.getDupNo();
		Map<String, String> codeMap = codeTypeService
				.findByCodeType("estateType");
		String thisCntrNo = Util.trim(l140mm3a.getCntrNo());
		Properties commPop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		if (Util.equals("sendBoss", flowaAtion)) {
			if (l140mm3a.getIs722QDate() != null) {
				Date currentDate = new Date();
				int diffDays = CapDate.calculateDays(currentDate,
						l140mm3a.getIs722QDate());
				if (diffDays > 31) {
					// L140MM3A.message07=本案是否屬銀行法72-2條控管對象帳務查詢日期已超過一個月，請重新查詢。
					msg = pop.getProperty("L140MM3A.message07");
				}

			}

			if (Util.nullToSpace(l140mm3a.getIsBuy()).isEmpty()) {
				msg = msg + ((Util.equals(msg, "")) ? "" : "<br/>");
				// 本案是否為購置不動產尚未完成
				msg = msg + pop.getProperty("L140M01a.isBuy")
						+ pop.getProperty("notCompleted");
			} else if (Util.equals("Y", Util.nullToSpace(l140mm3a.getIsBuy()))) {
				String busCode = lmsService.get_0024_busCd(
						Util.trim(StringUtils.substring(custIdDupNo, 0, 10)),
						Util.trim(StringUtils.substring(custIdDupNo, 10, 11)));

				if (l140mm3cs.isEmpty() || l140mm3cs == null) {
					// msg = msg+((msg == "")? "" : "<br/>");
					// 可以不要細項 => 拿掉此檢核 請輸入本次簽案資訊
					// msg =
					// msg+pop.getProperty("checkInput")+pop.getProperty("L140M01T.dial.005");
				} else {
					boolean l140mm3csYN = false;
					for (L140MM3C l140mm3c : l140mm3cs) {
						String estateType = l140mm3c.getEstateType();
						if (Util.notEquals("Y", l140mm3c.getCheckYN())) {
							msg = msg + ((Util.equals(msg, "")) ? "" : "<br/>");
							// 本次簽案資訊尚未完成
							msg = msg + pop.getProperty("L140M01T.dial.005")
									+ pop.getProperty("notCompleted");
							l140mm3csYN = true;
							break;
						}

						String mCntrNo = Util.trim(l140mm3c.getmCntrNo());
						if (thisCntrNo != ""
								&& Util.equals(thisCntrNo, mCntrNo)) {
							// lmsL120M01A.error061=本案額度序號不得為母戶額度序號
							throw new CapMessageException(
									commPop.getProperty("lmsL120M01A.error061"),
									this.getClass());
						}

						if (Util.equals(estateType,
								UtilConstants.L140M01T_estatType.都更危老)) {
							String estateSubType = l140mm3c.getEstateSubType();
							if (UtilConstants.L140M01T_estatSubType.都更
									.equals(estateSubType)
									|| UtilConstants.L140M01T_estatSubType.危老
											.equals(estateSubType)
									|| UtilConstants.L140M01T_estatSubType.其它都更危老
											.equals(estateSubType)) {
								if (Util.isNotEmpty(Util.trim(l140mm3c
										.getmCntrNo()))) {
									L140MM3C mCntrNoData = lms7500Service
											.getBuildInfoByMcntrNo(Util
													.trim(l140mm3c.getmCntrNo()));
									if (mCntrNoData == null) {
										msg = msg
												+ ((Util.equals(msg, "")) ? ""
														: "<br/>");
										msg = msg
												+ pop.getProperty("L140MM3A.message05");
									} else {
										if (Util.notEquals(
												mCntrNoData.getEstateSubType(),
												l140mm3c.getEstateSubType())
												|| Util.notEquals(
														mCntrNoData
																.getEstateStatus(),
														l140mm3c.getEstateStatus())) {
											msg = msg
													+ ((Util.equals(msg, "")) ? ""
															: "<br/>");
											msg = msg
													+ pop.getProperty("L140MM3A.message06");
										}
									}
								}
							}
						}

						if (Util.notEquals(Util.trim(busCode), "")) {
							String errExItem = "";
							if (LMSUtil.isBusCode_060000_130300(busCode)) {
								if (Util.equals(estateType, "A0@")) {
									// 個人戶不能選A0# A0@
									errExItem = estateType;
								}
							} else { // 企業戶
								if (Util.equals(estateType, "A0N")
										|| Util.equals(estateType, "C02")) {
									// 企業戶不能選A0N C02
									errExItem = estateType;
								}
								if (Util.equals(estateType.substring(0, 1), "B")) {
									// 企業戶不能選產品種類
									errExItem = estateType;
								}
							}

							if (Util.notEquals(errExItem, "")) {
								msg = msg
										+ ((Util.equals(msg, "")) ? ""
												: "<br/>");
								// L140MM3A.message08=本案是否屬銀行法72-2條控管對象之符合排除原因不得為
								msg = msg
										+ pop.getProperty("L140MM3A.message08")
										+ errExItem + "."
										+ codeMap.get(errExItem);
							}
						}

						if (Util.equals(estateType, "A0@")) {
							if (Util.notEquals(l140mm3a.getCntrNo(), "")) {
								String brnId = Util.getLeftStr(
										l140mm3a.getCntrNo(), 3);
								if (UtilConstants.BrNoType.國外
										.equals(branchService.getBranch(brnId)
												.getBrNoFlag())) {
									msg = msg
											+ ((Util.equals(msg, "")) ? ""
													: "<br/>");
									msg = msg
											+ pop.getProperty("L140MM3A.message08")
											+ estateType + "."
											+ codeMap.get(estateType);
								}
							}
						}

						// 開放維護之不動產暨72-2相關資訊註記選項
						if (true) {
							String sysEstateType = Util.trim(lmsService
									.getSysParamDataValue("722_ESTATE_TYPE"));
							if (Util.notEquals(sysEstateType, "")) {
								boolean chk = true;
								for (String xx : sysEstateType.split(",")) {
									String txx = Util.equals(Util.trim(xx),
											"NULL") ? "" : xx;
									if (Util.equals(txx, estateType)) {
										chk = false;
									}
								}
								if (chk) {
									msg = msg
											+ ((Util.equals(msg, "")) ? ""
													: "<br/>");
									msg = msg
											+ pop.getProperty("notAllowedToModify")
											+ estateType + "."
											+ codeMap.get(estateType);
								}
							}
						}
					}

					if (!l140mm3csYN
							&& (Util.nullToSpace(l140mm3a.getIs722Flag())
									.isEmpty() || Util.nullToSpace(
									l140mm3a.getIsInstalment()).isEmpty())) {
						msg = msg + ((Util.equals(msg, "")) ? "" : "<br/>");
						// 本案是否屬銀行法72-2條控管對象尚未完成
						msg = msg + pop.getProperty("L140M01a.is722Flag")
								+ pop.getProperty("notCompleted");
					}
				}
			}

			// J-111-0633_05097_B1001 Web
			// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
			// if (Util.equals(Util.nullToSpace(l140mm3a.getLnType()), "33")
			// || Util.equals(Util.nullToSpace(l140mm3a.getLnType()), "34")) {
			// if (Util.isEmpty(Util.nullToSpace(l140mm3a.getAdcCaseNo()))) {
			// msg = msg + ((Util.equals(msg, "")) ? "" : "<br/>");
			// msg = msg + pop.getProperty("L140MM3A.adcCaseNo")
			// + pop.getProperty("notCompleted");
			// }
			// }
		}

		// if(Util.equals("check", flowaAtion)){ //覆核 需檢查ELF515母戶是否存在
		/*
		 * for(L140MM3C l140mm3c : l140mm3cs){
		 * if(Util.isNotEmpty(Util.nullToSpace(l140mm3c.getmCntrNo()))){ ELF515
		 * elf515 = misELF515Service.findMcntrNo(l140mm3c.getmCntrNo());
		 * if(elf515 == null){ msg = msg+((msg == "")? "" : "<br/>"); msg =
		 * msg+pop.getProperty("L140MM3A.message05"); //母戶額度序號不存在，請至資料建檔系統維護 }
		 * else
		 * if(Util.notEquals(l140mm3c.getEstateStatus(),elf515.getElf515_sub_type2
		 * ())){ msg = msg+((msg == "")? "" : "<br/>"); msg =
		 * msg+pop.getProperty("L140MM3A.message06"); //與母戶計劃進度不一致，請確認 } } }
		 */
		// }

		return msg;
	}

	// J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
	public String checkSaveData3(L140MM3A l140mm3a, String flowaAtion)
			throws CapException {
		String msg = "";
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7500M01Page.class);
		if (Util.notEquals(Util.trim(l140mm3a.getUpdateItem3()), "Y")) {
			return msg;
		}
		// 統計未填欄位數
		int countItme = 1;
		StringBuffer temp = new StringBuffer();

		boolean needSpecialFinRisk = lms7500Service
				.needShowIsSpecialFinRisk(l140mm3a);
		String isSpecialFinRisk = Util.trim(l140mm3a.getIsSpecialFinRisk());
		if (needSpecialFinRisk) {

			if (Util.equals(isSpecialFinRisk, "")) {
				// L140M01a.isSpecialFinRisk=本案是否屬特殊融資暴險
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L140M01a.isSpecialFinRisk"));
			} else {
				if (Util.equals(Util.trim(l140mm3a.getSpecialFinRiskType()),
						"1")) {
					// 專案融資
					if (Util.equals(
							Util.trim(l140mm3a.getIsProjectFinOperateStag()),
							"")) {
						// L140M01a.isProjectFinOperateStag=專案融資是否屬營運階段
						countItme = this
								.setHtmlBr(
										temp,
										countItme,
										prop.getProperty("L140M01a.isProjectFinOperateStag"));
					}
				}
			}
		}

		String isCmsAdcRisk = Util.trim(l140mm3a.getIsCmsAdcRisk());
		if (Util.equals(isCmsAdcRisk, "")) {
			// L140M01a.isCmsAdcRisk=本案是否屬不動產ADC暴險
			countItme = this.setHtmlBr(temp, countItme,
					prop.getProperty("L140M01a.isCmsAdcRisk"));
		}

		if (needSpecialFinRisk) {
			if (Util.equals(isSpecialFinRisk, "Y")) {
				String specialFinRiskType = Util.trim(l140mm3a
						.getSpecialFinRiskType());
				if (Util.equals(specialFinRiskType, "")) {
					// L140M01a.specialFinRiskType=特殊融資暴險類別
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L140M01a.specialFinRiskType"));
				}
			}
		}

		if (Util.notEquals(temp.toString(), "")) {
			msg = temp.toString() + "尚未完成";
		} else {
			msg = temp.toString();
		}

		return msg;
	}

	// J-111-0633_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
	public String checkSaveData4(L140MM3A l140mm3a, String flowaAtion)
			throws CapException {
		String msg = "";
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7500M01Page.class);
		if (Util.notEquals(Util.trim(l140mm3a.getUpdateItem4()), "Y")) {
			return msg;
		}
		// 統計未填欄位數
		int countItme = 1;
		StringBuffer temp = new StringBuffer();

		if (Util.isEmpty(Util.nullToSpace(l140mm3a.getLnType()))) {
			// L140MM3A.lnType=產品種類
			countItme = this.setHtmlBr(temp, countItme,
					prop.getProperty("L140MM3A.lnType"));

		}

		if (Util.equals(Util.nullToSpace(l140mm3a.getLnType()), "33")
				|| Util.equals(Util.nullToSpace(l140mm3a.getLnType()), "34")) {
			if (Util.isEmpty(Util.nullToSpace(l140mm3a.getAdcCaseNo()))) {
				// L140MM3A.adcCaseNo=土建融案件編號
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L140MM3A.adcCaseNo"));

			}
		}

		if (Util.notEquals(temp.toString(), "")) {
			msg = temp.toString() + "尚未完成";
		} else {
			msg = temp.toString();
		}

		return msg;
	}

	/**
	 * 設定 訊息換列 目前設定五筆 就換列
	 * 
	 * @param temp
	 *            暫存文字
	 * @param countItme
	 *            計算欄位數量
	 * @param showMessage
	 * @return countItme 目前的 計算數量
	 */
	public int setHtmlBr(StringBuffer temp, int countItme, String showMessage) {
		int maxLenth = 5;
		temp.append(temp.length() > 0 ? "、" : "");
		if (countItme > maxLenth) {
			temp.append("<br/>");
			countItme = 1;
		} else {
			countItme++;
		}
		temp.append(showMessage);
		return countItme;
	}

	public IResult checkCntrno(PageParameters params)
			throws CapException {
		String cntrNo = Util.trim(params.getString("cntrNo"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<ELF515> allData = misELF515Service.find(cntrNo);

		if (allData == null) {
			result.set("count", 0);
		} else {
			result.set("count", allData.size());
			boolean isMom = false;
			for (ELF515 row : allData) {
				if (Util.equals(row.get("elf515_cntrno"),
						row.get("elf515_cntrno_m"))) {
					// 母戶
					isMom = true;
					break;
				} else {
					// 子戶
				}
			}
			result.set("isMom", isMom);
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryNewAdcCaseNo(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID, ""));
		L140MM3A l140mm3a = (L140MM3A) lms7500Service.findModelByOid(
				L140MM3A.class, oid);
		String newAdcNo = lmsService.getNewAdcCaseNo();
		if (l140mm3a != null) {
			l140mm3a.setAdcCaseNo(newAdcNo);
			l140mm3a.setRandomCode(IDGenerator.getRandomCode());
			lms7500Service.save(l140mm3a);
		}
		result.set("adcCaseNo", newAdcNo);
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult chkAdcCaseNo(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS7500M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String adcCaseNo = Util.trim(params.getString("adcCaseNo", ""));
		Boolean save = params.getAsBoolean("save", false);
		String oid = Util.trim(params.getString(EloanConstants.OID, ""));
		if (Util.isEmpty(adcCaseNo)) {
			result.set(
					"msg",
					pop.getProperty("checkInput")
							+ pop.getProperty("L140MM3A.adcCaseNo"));
		} else {
			HashSet<String> adcCaseNoSet = lmsService.getAdcCaseNoList("C", "",
					"", "", adcCaseNo);
			if (adcCaseNoSet == null || adcCaseNoSet.isEmpty()) {
				result.set("msg", RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料));
			} else {
				if (save) {
					L140MM3A l140mm3a = (L140MM3A) lms7500Service
							.findModelByOid(L140MM3A.class, oid);
					if (l140mm3a != null) {
						l140mm3a.setAdcCaseNo(adcCaseNo);
						l140mm3a.setRandomCode(IDGenerator.getRandomCode());
						lms7500Service.save(l140mm3a);
					}
				}
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getConfirmMsg(PageParameters params)
			throws CapException {
		StringBuffer confirmMsg = new StringBuffer("");
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = params.getString(EloanConstants.MAIN_OID);
		L140MM3A l140mm3a = (L140MM3A) lms7500Service.findModelByOid(
				L140MM3A.class, oid);

		String cntrNo = Util.nullToSpace(l140mm3a.getCntrNo());
		if (Util.isNotEmpty(cntrNo)) {
			// J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
			if (Util.equals(Util.trim(l140mm3a.getUpdateItem2()), "Y")) {

				Map<String, Object> elf506DataMap = misELF506Service
						.getByCntrNo(cntrNo);
				if (elf506DataMap != null && !elf506DataMap.isEmpty()) {
					String onFlag = Util.nullToSpace(elf506DataMap
							.get("ELF506_722_FLAG"));
					String is722Flag = Util
							.nullToSpace(l140mm3a.getIs722Flag());
					if (Util.isNotEmpty(onFlag)) {
						if (Util.notEquals(is722Flag, onFlag)) {
							confirmMsg.append("●").append(
									"「是否屬銀行法72-2條控管對象」本次與前次不一致，請確認");
						}
					}
				}
			}
		}

		if (Util.notEquals(confirmMsg.toString(), "")) {
			result.set("confirmMsg", confirmMsg.toString());
		}

		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult initExceptFlag(PageParameters params)
			throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		L140MM3A l140mm3a = lms7500Service.findL140mm3aByUniqueKey(mainId);
		
		result.set("isShowQ2Q7AttachedItem", this.lms7500Service.isShowQ2Q7AttachedItem(l140mm3a.getCntrNo()) ? "Y" : "N" );
		
		return result;// 傳回執行這個動作的AjAX
	}
}
