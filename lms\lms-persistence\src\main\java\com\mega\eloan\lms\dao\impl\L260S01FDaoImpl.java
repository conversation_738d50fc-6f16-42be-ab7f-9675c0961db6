package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L260S01FDao;
import com.mega.eloan.lms.model.L260S01F;

/** ESG項目檔 **/
@Repository
public class L260S01FDaoImpl extends LMSJpaDao<L260S01F, String> implements
		L260S01FDao {

	@Override
	public L260S01F findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L260S01F> findByMainId(String mainId, boolean notIncDel) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (notIncDel) {
			search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
					"");
		}
		search.addOrderBy("itemSeq");
		List<L260S01F> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L260S01F findByUniqueKey(String mainId, String itemNo) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (itemNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemNo", itemNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L260S01F> findByIndex01(String mainId, String itemNo) {
		ISearch search = createSearchTemplete();
		List<L260S01F> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (itemNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemNo", itemNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public L260S01F findByMainidAndItemNo(String mainId, String itemNo,
			boolean notIncDel) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (itemNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemNo", itemNo);
		if (notIncDel)
			search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
					"");
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}
}