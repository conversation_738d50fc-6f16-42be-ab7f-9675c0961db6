package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.LNF07A;


public interface LNLNF07AService {

	public void insert(List<LNF07A> list);
	public void delete_by_key1_key2(List<LNF07A> list);
	public void delete_by_key1_key2_key3(List<LNF07A> list);
	public List<LNF07A> sel_by_key1(String key1);
	public List<LNF07A> sel_by_key1_orderBykey3Desc(String key1);
	public List<LNF07A> sel_by_key1_key2(String key1, String key2);
	public List<LNF07A> sel_by_key1_key2_key3(String key1, String key2, String key3);
	List<Map<String, Object>> findByCntrNoControl();
	public int upd_A32(String lnf07a_key_1, String lnf07a_key_2
			, String lnf07a_key_4, String lnf07a_content_4);
}
