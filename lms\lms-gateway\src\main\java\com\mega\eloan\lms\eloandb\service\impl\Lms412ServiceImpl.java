package com.mega.eloan.lms.eloandb.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.eloandb.service.Lms412Service;

/**
 * <pre>
 * Lms412Service (R6) 覆審控制檔
 * </pre>
 * 
 * @since 2011/11/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/4,jessica,new
 *          </ul>
 */
@Service
public class Lms412ServiceImpl extends AbstractEloandbJdbc implements
		Lms412Service {

	public int insertLMS412(String custId, String dupNo, String brNo,
			String pkCust, String state, String newadd, Date newDt,
			String nckFg, String nckDt, String cancelDt, String commFg) {
		return this.getJdbc().update(
				"LMS412.insert",
				new Object[] { custId, dupNo, brNo, pkCust, state, newadd,
						newDt, nckFg, nckDt, cancelDt, commFg });
	}

	public int updateLMS412(String lms412DataDt, String lms412Maincust,
			String lms412Cstate, String lms412Newadd, Date lms412Newdate,
			String lms412Nckdflag, String lms412Nckddate,
			String lms412Canceldt, String lms412Dbuobu, String lms412Lrdate,
			String lms412Llrdate, String lms412Nckdmemo, String lms412Nextnwdt,
			String lms412Nextltdt, String lms412Branch, String lms412CustId,
			String lms412Dupno) {
		return this.getJdbc().update(
				"LMS412.update",
				new Object[] { lms412DataDt, lms412Maincust, lms412Cstate,
						lms412Newadd, lms412Newdate, lms412Nckdflag,
						lms412Nckddate, lms412Canceldt, lms412Dbuobu,
						lms412Lrdate, lms412Llrdate, lms412Nckdmemo,
						lms412Nextnwdt, lms412Nextltdt, lms412Branch,
						lms412CustId, lms412Dupno });
	}

	public int updateLMS412ByLNFE0851(String mdFlag, String mddt,
			String process, String newadd, Date newDt, String memo,
			String branchId, String custId, String duoNo) {
		return this.getJdbc().update(
				"LMS412.updateLNFE0851",
				new Object[] { mdFlag, mddt, process, newadd, newDt, memo,
						branchId, custId, duoNo });
	}

//	public List<?> findLMS412ByBranch(String branch, String dataDate) {
//		return this.getJdbc().queryForList("LMS412.selByBranch",
//				new Object[] { branch, dataDate });
//	}

	// public int updateLMS412(String uCase, String uCaseRole, String mainCust,
	// String crdType, String crdTTbl, String mowType, String fcrdType,
	// String fcrdArea, String fcrdPred, String fcrdGrad, Date elfLRDate,
	// String elfRCkdLine, String elfUCkdLINE, Date elfUCkdDt,
	// String elfMDFlag, Date elfMDDt, String elfProcess,
	// String elfNewAdd, String elfNewDate, String elfNCkdFlag,
	// Date elfNCkdDate, String elfNCkdMemo, Date elfNextNwDt,
	// String elfDBUOBU, String elfMemo) {
	// if ("NA".equals(mowType) || "NO".equals(mowType)) {
	// mowType = "Z";
	// }
	// if ("NA".equals(crdType) || "NO".equals(crdType)) {
	// crdType = "";
	// }
	// return this.getJdbc().update(
	// "LMS412.update",
	// new Object[] { uCase, uCaseRole, mainCust, crdType, crdTTbl,
	// mowType, fcrdType, fcrdArea, fcrdPred, fcrdGrad,
	// elfLRDate, elfRCkdLine, elfUCkdLINE, elfUCkdDt,
	// elfMDFlag, elfMDDt, elfProcess, elfNewAdd, elfNewDate,
	// elfNCkdFlag, elfNCkdDate, elfNCkdMemo, elfNextNwDt,
	// elfDBUOBU, elfMemo });
	// }

	public int updateLMS412ByGrade(String gradeType, String borrGrade,
			String mowType, String mowGrade, String fcrdType, String fcrdArea,
			String fcrdPred, String fcrdGrad, String custId, String dupNo,
			String branch) {
		if ("NA".equals(mowType) || "NO".equals(mowType)) {
			mowType = "Z";
		}
		if ("NA".equals(gradeType) || "NO".equals(gradeType)) {
			gradeType = "";
		}
		return this.getJdbc().update(
				"LMS412.updateByGrade",
				new Object[] { gradeType, borrGrade, mowType, mowGrade,
						fcrdType, fcrdArea, fcrdPred, fcrdGrad, custId, dupNo,
						branch });
	}

	public int updateELF412(String gradeType, String borrGrade, String mowType,
			String moodyGrade, String custId, String dupNo, String branch) {
		if ("NA".equals(mowType) || "NO".equals(mowType)) {
			mowType = "Z";
		}
		if ("NA".equals(gradeType) || "NO".equals(gradeType)) {
			gradeType = "";
		}
		return this.getJdbc().update(
				"ELF412.updateElf412",
				new Object[] { gradeType, borrGrade, mowType, moodyGrade,
						custId, dupNo, branch });
	}

	public List<Map<String, Object>> findELF412ByCustIdDupNoBranchs(
			String[] custIds, String[] dupNos, String[] branchs) {
		List<Object> params = new ArrayList<Object>();
		List<String> sqlParams = new ArrayList<String>();
		for (int i = 0; i < custIds.length; i++) {
			sqlParams.add("(?,?,?)");
			params.add(custIds[i]);
			params.add(dupNos[i]);
			params.add(branchs[i]);
		}

		return this.getJdbc().queryForListByCustParam(
				"LMS412.selectByCustIdDupNoBranchs",
				new Object[] { StringUtils.join(sqlParams, ",") }, params.toArray(new Object[0]));
	}

	public Map<String, Object> findELF412ByCustIdDupNoBranch(String custId,
			String dupNo, String branch) {
		return this.getJdbc().queryForMap("LMS412.selectByCustIdDupNoBranch",
				new Object[] { custId, dupNo, branch });
	}

	// Date UCKDDT, Object NEWDATE,
	@Override
	public int updateLMS412All(Date canceldt, Object nckdflag, Date nckddate,
			Date nextltdt, Date nextnwdt, Object mdflag, Object uckdline,
			Object rckdline, Object newadd, Object maincust, Object nckdmemo,
			Date uckddt, String newdate, Date lrdate, Date llrdate,
			String custId, String dupNo, String branch) {

		return this.getJdbc().update(
				"LMS412.updateElf412ByAll",
				new Object[] {
						canceldt,
						nckdflag,
						nckddate,
						nextltdt,
						nextnwdt,
						mdflag,
						uckdline,
						rckdline,
						newadd,
						maincust,
						nckdmemo,
						uckddt,
						Util.trim(newdate).length() >= 6 ? newdate.substring(0,
								6) : newdate, lrdate, llrdate, custId, dupNo,
						branch });
	}

	@Override
	public List<Map<String, Object>> findAllLMS412ByBranch(String branch) {
		return this.getJdbc().queryForList("LMS412.selAllByBranch",
				new Object[] { branch });
	}

	@Override
	public void saveLms412updateMdflag(String mdFlag, String mdDt,
			String process, String custId, String dupNo, String branch) {
		this.getJdbc().update("LMS412.updateByMdflag",
				new Object[] { mdFlag, mdDt, process, custId, dupNo, branch });
	}

	@Override
	public List<Map<String, Object>> findLms412JoinCust(String branch) {
		return this.getJdbc().queryForList("LMS412.selAllJoinCust",
				new Object[] { branch });
	}

	@Override
	public List<Map<String, Object>> findLms412ByCustId(String branch,
			String custId, String dupNo) {
		return this.getJdbc().queryForList("LMS412.selAllByCustId",
				new Object[] { branch, custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findLms412ByCustIdNoMis(String branch,
			String custId, String dupNo) {
		return this.getJdbc().queryForList("LMS412.selAllByCustIdNoMIS",
				new Object[] { branch, custId, dupNo });
	}

	@Override
	public void addLms412(String branch, String custId, String dupNo,
			String mainCust, String newAdd, String newDate, String rckdLine) {
		this.getJdbc().update(
				"LMS412.insert",
				new Object[] { branch, custId, dupNo, mainCust, newAdd,
						newDate, rckdLine });
	}

	@Override
	public void saveLms412(Date dataDate, String mainCust, String cState,
			String newAdd, String newDate, String nckdFlag, Date nckdDate,
			Date cancelDt, String dbuObu, Date lrDate, Date llrDate,
			String nckdMemo, Date nextNwdt, Date nexLtdt, String rckdLine,
			String branch, String custId, String dupNo) {
		this.getJdbc().update(
				"LMS412.update",
				new Object[] { dataDate, mainCust, cState, newAdd, newDate,
						nckdFlag, nckdDate, cancelDt, dbuObu, lrDate, llrDate,
						nckdMemo, nextNwdt, nexLtdt, rckdLine, branch, custId,
						dupNo });
	}

	@Override
	public void saveLms412Mdflag(String mdFlag, String mdDt, String process,
			String newAdd, String newDate, String memo, String branch,
			String custId, String dupNo) {
		this.getJdbc().update(
				"LMS412.updateLNFE0851",
				new Object[] { mdFlag, mdDt, process, newAdd, newDate, memo,
						branch, custId, dupNo });
	}

	@Override
	public Map<String, Object> findLms412Uni(String branch, String custId,
			String dupNo) {
		return this.getJdbc().queryForMap("LMS412.selAllByCustId",
				new Object[] { branch, custId, dupNo });
	}

	@Override
	public void saveBymaintain(String maincust, String crdtype, String crdttbl,
			String mowtype, String mowtbl1, Date lrdate, String rckdline,
			String mdflag, Date mddt, String process, String newadd,
			String newdate, String nckdflag, Date nckddate, String nckdmemo,
			String dbuobu, Date upddate, String updater, String memo,
			Date tmestamp, String uckdline, Date uckddt, Date nextnwdt,
			String ucase, String ucaserole, String isAllNew, String branch,
			String custid, String dupno) {
		if ("NA".equals(mowtype) || "NO".equals(mowtype)) {
			mowtype = "Z";
		}
		if ("NA".equals(crdtype) || "NO".equals(crdtype)) {
			crdtype = "";
		}
		this.getJdbc().update(
				"LMS412.updateByMaintain",
				new Object[] { maincust, crdtype, crdttbl, mowtype, mowtbl1,
						lrdate, rckdline, mdflag, mddt, process, newadd,
						newdate, nckdflag, nckddate, nckdmemo, dbuobu, upddate,
						updater, memo, tmestamp, uckdline, uckddt, nextnwdt,
						ucase, ucaserole, isAllNew, branch, custid, dupno });
	}

	@Override
	public void addLMS412ByMainTain(String maincust, String crdtype,
			String crdttbl, String mowtype, String mowtbl1, Date lrdate,
			String rckdline, String mdflag, Date mddt, String process,
			String newadd, String newdate, String nckdflag, Date nckddate,
			String nckdmemo, String dbuobu, Date upddate, String updater,
			String memo, Date tmestamp, String uckdline, Date uckddt,
			Date nextnwdt, String ucase, String ucaserole, String isAllNew,
			String branch, String custid, String dupno) {
		if ("NA".equals(mowtype) || "NO".equals(mowtype)) {
			mowtype = "Z";
		}
		if ("NA".equals(crdtype) || "NO".equals(crdtype)) {
			crdtype = "";
		}
		this.getJdbc().update(
				"LMS412.insertByMaintain",
				new Object[] { maincust, crdtype, crdttbl, mowtype, mowtbl1,
						lrdate, rckdline, mdflag, mddt, process, newadd,
						newdate, nckdflag, nckddate, nckdmemo, dbuobu, upddate,
						updater, memo, tmestamp, uckdline, uckddt, nextnwdt,
						ucase, ucaserole, isAllNew, branch, custid, dupno });
	}

	@Override
	public List<Map<String, Object>> findLMS412ByBranchForReportType1(
			String brNo) {
		return this.getJdbc().queryForList("LMS412.selByBrnoForReportType1",
				new Object[] { brNo });
	}

//	@Override
//	public List<Map<String, Object>> findLMS412ByBranchForReportType4(
//			String brNo) {
//		return this.getJdbc().queryForList("LMS412.selByBrnoForReportType4",
//				new Object[] { brNo });
//	}

	@Override
	public List<Map<String, Object>> findLms412JoinCustData(String custId412,
			String dupNo412, String brNo) {
		return this.getJdbc().queryForList("LMS412.selByBrnoForReportType2",
				new Object[] { custId412, dupNo412, brNo });
	}

	@Override
	public void applyUpdate412(String mainId, String branch, Date dataDate) {
		this.getJdbc().update(
				"apply.updatelms412",
				new Object[] { mainId, branch, dataDate, mainId, branch,
						dataDate });
	}

}
