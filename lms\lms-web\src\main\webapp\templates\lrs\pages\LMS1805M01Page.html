<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="innerPageBody">
		<div class="button-menu funcContainer" id="buttonPanel">
			<th:block th:if="${_btnDOC_EDITING_visible}">
				<button id="btnSave" type="button">
					<span class="ui-icon ui-icon-jcs-04" ></span>
					<th:block th:text="#{'button.save'}">儲存</th:block>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02" ></span>
					<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
				</button>
			</th:block>
			<th:block th:if="${_btnWAIT_APPROVE_visible}">
				<button type="button" id="btnAccept">
					<span class="ui-icon ui-icon-check" ></span>
					<th:block th:text="#{'button.accept'}">核可</th:block>
				</button>
				<!-- 退回  -->
				<button type="button" id="btnReturn">
					<span class="ui-icon ui-icon-closethick" ></span>
					<th:block th:text="#{'button.return'}">退回</th:block>
				</button>
			</th:block>
			<button type="button" id="btnExit" class="forview">
				<span class="ui-icon ui-icon-jcs-01"></span>
				<th:block th:text="#{'button.exit'}">離開</th:block>
			</button>
		</div>
		<div class="tit2 color-black">
			<th:block th:text="#{'lms160.title'}"></th:block>
			<span class="color-blue" id="titInfo"></span>
		</div>
		<div class="tabs doc-tabs">
			<ul>
				<li><a href="#tab-01" goto="01"><b><th:block th:text="#{'doc.tit01'}">文件資訊</th:block></b></a></li>
				<li><a href="#tab-02" goto="02"><b><th:block th:text="#{'doc.tit02'}">案件資訊</th:block></b></a></li>
				<li><a href="#tab-03" goto="03"><b><th:block th:text="#{'doc.tit03'}">附件檔案</th:block></b></a></li>
			</ul>
			<div class="tabCtx-warp">
				<div th:id="${tabID}" th:insert="~{${panelName} :: ${panelFragmentName}}"></div>
			</div>
		</div>
		<script type="text/javascript">
			loadScript('pagejs/lrs/LMS1805M01Page');
		</script>
	</th:block>
</body>
</html>
