var pageAction = {
	handler : 'cls1131formhandler',
	grid : null,
	build : function(obj){
		//ilog.debug("obj.show_s01r="+(obj.show_s01r||''));
		
		var gridview_colModel = [
		      {
				name : 'oid',
				hidden : true //是否隱藏
			},{
				name : 'mainId',
				hidden : true //是否隱藏
			},{
				colHeader : i18n.cls1131v01["C101S01G.custId"], //身分證統編重複碼
				width : 60, //設定寬度
				name : 'custId', //身分證統編
				formatter: 'click',
				onclick : function(cellvalue, options, rowObject){
					pageAction.openDoc(rowObject);
				}
				//hidden : true //是否隱藏
			},{
				colHeader : ' ',
				width : 10, //設定寬度
				name : 'dupNo',
				sortable : false
				//hidden : true //是否隱藏
			},/*{
				colHeader : i18n.cls1131v01["C101S01G.custId"], //身分證統編重複碼
				align : "left",
				width : 60, //設定寬度
				sortable : false, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'custNumber' //col.id
			},*/{
				colHeader : i18n.cls1131v01["C101S01G.custName"], //借款人姓名
				align : "left",
				width : 60, //設定寬度
				sortable : true, //是否允許排序
				name : 'custName' //col
			},{
				colHeader : i18n.cls1131v01["C101S01G.grade1"], //初始評等				
				name : 'c101s01g.grade1', //col.id
				hidden : true
			},{
				colHeader : i18n.cls1131v01["C101S01G.grade2"], //調整評等				
				name : 'c101s01g.grade2', //col.id
				hidden : true
			},{
				colHeader : i18n.cls1131v01["markModel.C101S01G.grade3"], //房貸最終評等
				align : "center",
				width : 60, //設定寬度
				sortable : false, //是否允許排序
				name : 'c101s01g.grade3' //col.id
			},{
				colHeader : i18n.cls1131v01["markModel.C101S01Q.grade3"], //非房貸最終評等
				align : "center",
				width : 60, //設定寬度
				sortable : false, //是否允許排序
				name : 'c101s01q.grade3' //col.id
			}
		];
		
		if(obj.show_s01r=="Y"){
			gridview_colModel.push({
				colHeader : i18n.cls1131v01["markModel.C101S01R.grade3"], //專案信貸(非團體)最終評等
				align : "center",
				width : 60, //設定寬度
				sortable : false, //是否允許排序
				name : 'c101s01r.grade3' //col.id		
	        });
		}
		
		if(true){
			gridview_colModel.push({
				colHeader : i18n.cls1131v01["C101S01G.alertMsg"], //票交所與聯徵特殊負面資訊
				align : "left",
				width : 100, //設定寬度
				sortable : false, //是否允許排序
				name : 'c101s01g.alertMsg' //col.id	
	        });
			gridview_colModel.push({
				colHeader : i18n.cls1131v01["C101S01G.jcicQDate"], //聯徵查詢日期
				align : "center",
				width : 60, //設定寬度
				sortable : true, //是否允許排序
				//name : 'c101s01g.jcicQDate' //col.id
				name : 'c101s01e.eJcicQDate' //col.id
	        });
			gridview_colModel.push({
				colHeader : i18n.cls1131v01["C101S01G.etchQDate"], //票信查詢日期
				align : "center",
				width : 60, //設定寬度
				sortable : true, //是否允許排序
				//name : 'c101s01g.etchQDate' //col.id
				name : 'c101s01e.eChkQDate' //col.id
	        });
		}
		
		pageAction.grid = $("#gridview").iGrid({			
			handler : 'cls1131gridhandler',
			height : 400,
			action :  'queryBaseData',
			sortname: 'custId',
			//sortorder: 'desc',
			rowNum:17,
			rownumbers:true,
			colModel : gridview_colModel,
			ondblClickRow: function(rowid){
				var data = pageAction.grid.getRowData(rowid);
				pageAction.openDoc(data);
			}				
		});
		//build button 
		//篩選
		$("#buttonPanel").find("#btnFilter").click(function() {
			$("#searchThickBox").thickbox({
				title : i18n.def['query'] || '篩選',
				width : 400,
				height : 150,
				modal : true,
				align : 'center',
				valign: 'bottom',
				i18n: i18n.def,
				buttons : {
					'sure' : function(){
						var $form = $('#searchForm');
						if ($form.valid()){	
							$.thickbox.close();
							pageAction.reloadGrid($form.serializeData());
						}
					},
					'close' : function(){	
						$.thickbox.close();
					}
				}
			});
		})
		//新增
		.end().find("#btnAdd").click(function() {
			/*
			  mega.eloan.sample.js 裡的 AddCustAction 寫了  class="checkID 
			  
			  common.validate.js 查找 checkID 可以看到，呼叫了 CommonAPI.checkTWID(value, element)
			   		, CommonAPI.checkCompanyNo(value, element)
			   		, CommonAPI.checkForeign(value, element)			  	
			
			AddCustAction.open({
				handler : pageAction.handler,
				action : 'addCust',
				callback : function(response){
					pageAction.reloadGrid();
					//MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["confirmDeleteSuccess"]);
					$.thickbox.close();
					pageAction.openDoc(response.C101M01A);
				}
			});  */
			
			//J-111-0125 因應obu可以承做外國自然人授信業務(法規未予限制), 開放eloan(包含個人授信/徵信/信評等系統)相關對外國自然人統編的限制
			var formId = "addborrowForm";
			var $addborrow = $("#"+formId);
			$addborrow.reset();
			
			$("#thickboxaddborrow").thickbox({		
				title : '', width : 500, height : 180, modal : false, i18n:i18n.def,
				buttons : {	}
			});
		})
		//編輯
		.end().find("#btnModify").click(function() {
			var data = pageAction.grid.getSingleData();
			if (data){
				pageAction.openDoc(data);
			}
		})
		//刪除
		.end().find("#btnDelete").click(function() {
			var data = pageAction.grid.getSingleData();
			if (data){
				MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
					if (action){
						$.ajax({
							action : 'importData',
							handler : pageAction.handler,
							action : 'deleteCust',
							data : data,
							success:function(responseData){
								pageAction.reloadGrid();
								MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["confirmDeleteSuccess"]);
							}
						});
					}
				});
			}
		})
		//傳送至其他分行
		.end().find("#btnDeliver").click(function() {
			var data = pageAction.grid.getSingleData();
			if (data) {
				CommonAPI.showAllBranch({
					btnAction: function(a, b) {
						$.thickbox.close();
						data['brno'] = b.brNo;
						$.ajax({
							handler: pageAction.handler,
							action: 'sendCust',
							data: data,
							success: function(responseData) {
								//pageAction.reloadGrid();
								MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["confirmDeliverSuccess"]);
							}
						});
					}
				});
			}
		})
		//調閱
		.end().find('#btnView').click(function(){
			var data = pageAction.grid.getSingleData();
			if (data){
				pageAction.openDoc(data);
			}
		})
		.end().find('#btnCallCenterSendCreditCase').click(function(){
			var data = pageAction.grid.getSingleData();
			if (data){
				pageAction.callCenterSendCreditCase(data);
			}
		}).end().find('#btnCopy').click(function(){ //測試上傳DW用，因為傳送有被用了，所以借用COPY
			var rows = $("#gridview").getGridParam('selrow');
	        var mainId = "";
	        if (rows != 'undefined' && rows != null && rows != 0) {
	            var data = $("#gridview").getRowData(rows);
	            mainId = data.mainId;
	        }
	        if (mainId == "") {
	            CommonAPI.showMessage(i18n.def["grid_selector"]);
	            return;
	        }
			CommonAPI.confirmMessage("是否test上傳DW?", function(b){
	            if (b) {
	                $.ajax({
	                    handler: "cls1131formhandler",
	                    type: "POST",
	                    dataType: "json",
	                    data: {
	                        'formAction': "testRatingDocDW",
	                        'mainId': mainId,
	                        'mainDocStatus': viewstatus
	                    },
	                    success: function(obj){
	                        API.showMessage("上傳成功");
	                    }
	                });
	            }
	        });	
		});
	},
	/**
	 * 開啟文件
	 */
	openDoc : function(data){
		//CLS1131S01.open(data);
		if ($('#CLS1131S01ThickBox').length == 0){
			$('#ClsCustInfo').load(webroot + '/app/cls/cls1131s01', function() {
				setTimeout(function() {
					if (!$("#buttonPanel").find("#btnAdd").is("button")) { // 在 CLS1131V01Page.java 去判斷,若操作者只有EL00
						CLS1131S01.readOnly = true;
					}
					CLS1131S01.open(data);
				}, 500);
			});
    	}else{
    		if(!$("#buttonPanel").find("#btnAdd").is("button")){ // 在 CLS1131V01Page.java 去判斷,若操作者只有EL00
    			CLS1131S01.readOnly = true;	
    		}
    		CLS1131S01.open(data);
    	}
	},
	/**
	 * 讀取資料
	 */
	loadData : function(data){
		$.ajax({
			handler : pageAction.handler,
			action : 'loadCust',
			data : data,
			success : function(response) {

			}
		});
	},
	/**
	 * 重整資料表
	 */
	reloadGrid : function(data){
		if (data){
			pageAction.grid.jqGrid("setGridParam", {
				postData : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}else{
			pageAction.grid.trigger('reloadGrid');
		}
	},
	/**
	 * 電銷中心傳送信貸案件
	 */
	callCenterSendCreditCase : function(data){
		
	}
}

$("#getCustData").click(function(){
	var formId = "addborrowForm";
	var $addborrow = $("#"+formId);
	
	var $custId = $addborrow.find("[name=addborrowForm_custId]").val();
	if( $custId != null && $custId != undefined && $custId != ''){
	    var defaultOption = {
				defaultValue: $custId //預設值 
		};			
		//綁入MegaID
		CommonAPI.openQueryBox(
			$.extend({
				defaultCustType : "1",
                divId: formId, //在哪個div 底下
                isInSide:false, 
                doNewUser:false, 
                autoResponse: { // 是否自動回填資訊 
                       id: "addborrowForm_custId", // 統一編號欄位ID 
                       dupno: "addborrowForm_dupNo", // 重覆編號欄位ID 
                       name: "addborrowForm_custName" // 客戶名稱欄位ID 
                },fn:function(obj){	
                	/*
					obj.custid "A123456789"
					obj.dupno "0"
					obj.name "TESTT"
					obj.buscd 	"060000"
                	*/
					if( $addborrow.valid()){
						$.ajax({							
							type : "POST", handler : pageAction.handler,
							data : { formAction : "addCust", 
								custId: obj.custid, dupNo: obj.dupno, custName: obj.name, busCode: obj.buscd
							},
							success:function(response){
								pageAction.reloadGrid();
								$.thickbox.close();	
								pageAction.openDoc(response.C101M01A);
							}
						}).fail(function(){ $addborrow.reset(); });						
					}
				}
			},defaultOption)
		);			
	}
});	


pageJsInit(function() {
	$(function() {
		$.ajax({
			handler: pageAction.handler,
			async: false,
			action: 'viewPage_init_param',
			data: {},
			}).done( function(json) {
				pageAction.build(json);
		});
	});
});