/* 
 * L120S21BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S21B;

/** LGD額度EAD檔 J-110-0986_05097_B1001 於簽報書新增LGD欄位 **/
public interface L120S21BDao extends IGenericDao<L120S21B> {

	L120S21B findByOid(String oid);

	List<L120S21B> findByMainId(String mainId);

	List<L120S21B> findByIndex01(String mainId);

	L120S21B findByIndex02(String mainId, String cntrNo_s21b);

	List<L120S21B> findByIndex03(String mainId, String custId_s21b,
			String dupNo_s21b);

	List<L120S21B> findByCustIdAndBussType(String mainId, String custId_s21b,
			String dupNo_s21b, String bussType_s21b);

	List<L120S21B> findByMainIdOrderByCustIdBussType(String mainId);

}