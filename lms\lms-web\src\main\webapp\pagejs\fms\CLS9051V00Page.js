
$(document).ready(function(){
    var L901M01aGrid = $("#gridview").iGrid({
        handler: "cls9051gridhandler",
        height: 350,
        rowNum: 15,
        
        postData: {
            formAction: "queryC9051S01",
            'fieldId': "fileName"
        },
        colModel: [{
            colHeader: "",
            name: 'oid',
            hidden: true
        }, {
            colHeader: i18n.cls9051v00["C905M01a.DataYYMM"],// 資料年月
            align: "center",
            width: 30,
            sortable: true,
            formatter: 'click',
            onclick: openDoc,
            name: 'fileDesc'
        }, {
            colHeader: i18n.cls9051v00["C905M01a.DataName"], // 檔案名稱
            align: "center",
            width: 70,
            sortable: true,
            name: 'srcFileName'
        }, {
            colHeader: i18n.cls9051v00["C905M01a.DataCreateDate"], // 資料建立日期
            align: "center",
            width: 40,
            sortable: true,
            name: 'uploadTime'
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L901M01aGrid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        $.capFileDownload({
            handler: "simplefiledwnhandler",
            data: {
                fileOid: rowObject.oid
            }
        });
    }
    
    $('#btnImport').click(function(){
        doUpload();
    })
    
    $('#btnDelete').click(function(){
        var select = L901M01aGrid.getGridParam('selrow');
        if (!select) {
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var oid = L901M01aGrid.getRowData(select).oid;
                $.ajax({
                    handler: "cls9051s01fileuploadhandler",
                    data: {
                        formAction: "deleteUploadFile",
                        'fileOid': oid
                    },
                    success: function(obj){
                        L901M01aGrid.trigger("reloadGrid");
                    }
                });
            }
        });
    });
    
    
    
    function doUpload(s){
        $.ajax({
            type: "POST",
            handler: 'cls9051s01fileuploadhandler',
            data: {
                formAction: "genMainId"
            },
            success: function(responseData){
                MegaApi.uploadDialog({
                    fieldId: "fileName",
                    width: 320,
                    height: 100,
                    fileCheck: ['.xls', '.xlsx'],
                    data: {
                        deleteDup: false,
                        uid: responseData.mainId, //避免此檔案被列為此文件之附加檔案
                        mainId: 'CLS9051V1'
                    },
                    success: function(response){
                        $.ajax({
                            type: "POST",
                            handler: 'cls9051s01fileuploadhandler',
                            data: {
                                formAction: "importExcelStep",
                                'excelId': response.fileKey
                            },
                            success: function(json){
                                $("#gridview").trigger("reloadGrid");
                                
                            }
                        });
                    }
                });
                
            }
        });
        
        /*
         $("#uploadFile1").val('')
         var fileSize = '1048576';
         s = $.extend({
         handler: 'cls9051s01fileuploadhandler',
         fieldId: "importExcelStep",
         title: i18n && i18n.def.insertfile || "請選擇需插入之檔案",
         fileCheck: ['xls'],
         
         data: {
         'excelId':
         }
         }, s);
         
         $("#uploadFileDialog1").thickbox({
         width: 280,
         height: 70,
         align: "right",
         valign: "bottom",
         buttons: (function(){
         var b = {};
         b[i18n && i18n.def.uploadbutton || "上傳"] = function(){
         $.capFileUpload({
         handler: s.handler,
         fileCheck: s.fileCheck,
         fileElementId: s.fieldId,
         successMsg: s.successMsg,
         data: s.data,
         success: function(json){
         //$('#_jgrid').trigger('reloadGrid');
         $.thickbox.close();
         grid.trigger("reloadGrid");
         }
         });
         };
         b[i18n && i18n.def.cancel || "取消"] = function(){
         $.thickbox.close();
         };
         return b;
         })()
         });
         */
    }
});

