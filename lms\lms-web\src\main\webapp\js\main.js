var baseUrl = baseUrl || '../../../';
require.config({
  // urlArgs : 'cache=' + new Date().getTime(),
  // cache by day
  // urlArgs : 'cache=' + new Date().toJSON().slice(0,10).split`-`.join``,
  // [refs#206] cache by server startup timestamp
  urlArgs : jsCache,
  waitSeconds : 200,
  baseUrl : baseUrl,
  paths : {
    'libjs' : 'js/libjs',
    'basejs' : 'js/basejs',
    'mega.eloan.properties' : 'js/common/mega.eloan.properties',
	'mega.eloan.lms' : 'js/common/mega.eloan.lms'
  },
  shim : {
    'mega.eloan.properties': [ 'libjs', 'basejs' ],
	'mega.eloan.lms': [ 'libjs', 'basejs' ]
  }
});

require([ 'libjs', 'basejs', 'mega.eloan.properties', 'mega.eloan.lms' ], function(mainjs) {
  console.log("mainjs init");
});

window.loadScript = function(url) {
  require([ 'mega.eloan.lms', 'mega.eloan.properties' ], function() {
    require.undef(url);
    require([ url ], function(pageJs) {
      console.log(url + ' loaded');
      pageJs && pageJs.init();
    });
  });
};

window.pageJsInit = function(settings) {
  if (settings) {
    define.call(window, [ 'mega.eloan.lms', 'mega.eloan.properties' ], function() {
      return settings instanceof Function ? {
        init : settings
      } : settings;
    });
  }
};
