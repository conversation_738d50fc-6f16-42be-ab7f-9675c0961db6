/* 
 * LMSRPT.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 授信批次報表歷史檔 **/
@Entity
@Table(name = "LMSRPT", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class LMSRPT extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	// /**
	// * JOIN條件 關聯黨
	// *
	// */
	// @ManyToOne
	// @OneToMany(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	// @JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable =
	// false)
	// private L180R02A l180r02a;
	//
	// public L180R02A getL180r02a() {
	// return l180r02a;
	// }
	//
	// public void setL180r02a(L180R02A l180r02a) {
	// this.l180r02a = l180r02a;
	// }

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** mainId **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 分行代碼 **/
	@Column(name = "BRANCH", length = 3, columnDefinition = "CHAR(3)")
	private String branch;

	/** 資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATADATE", columnDefinition = "DATE")
	private Date dataDate;

	/** 起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "BGNDATE", columnDefinition = "DATE")
	private Date bgnDate;

	/** 迄日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ENDDATE", columnDefinition = "DATE")
	private Date endDate;

	/** 報表代號 **/
	@Column(name = "RPTNO", length = 10, columnDefinition = "VARCHAR(10)")
	private String rptNo;

	/** 報表名稱 **/
	@Column(name = "RPTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String rptName;

	/**
	 * 是否為最新報表
	 * <p/>
	 * Y/N，分行
	 */
	@Column(name = "NOWRPT", length = 1, columnDefinition = "CHAR(01)")
	private String nowRpt;

	/** 批次時間 **/
	@Column(name = "BTHDATE", columnDefinition = "TIMESTAMP")
	private Date bthDate;

	/**
	 * 備註或其他需使用的值
	 * <p/>
	 * name=value;name=value
	 */
	@Column(name = "REMARKS", length = 600, columnDefinition = "varchar(600)")
	private String remarks;

	/**
	 * 分行傳送授管處人員
	 * <p/>
	 */
	@Column(name = "SENDER", length = 6, columnDefinition = "VARCHAR(6)")
	private String sender;

	/** 分行傳送授管處時間 **/
	@Column(name = "SENDTIME", columnDefinition = "TIMESTAMP")
	private Date sendTime;

	/**
	 * 授管處核備經辦
	 * <p/>
	 * 自動分案
	 */
	@Column(name = "JINGBAN", length = 6, columnDefinition = "VARCHAR(6)")
	private String jingBan;

	/** 授管處核備時間 **/
	@Column(name = "CFRMTIME", columnDefinition = "TIMESTAMP")
	private Date cfrmTime;

	/**
	 * 授管處核備註記
	 * <p/>
	 * Y/N
	 */
	@Column(name = "CFRMFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String cfrmFlag;

	/**
	 * 報表亂碼
	 * <p/>
	 * 產檔時一併產生
	 */
	@Column(name = "RANDOMCODE", length = 32, columnDefinition = "VARCHAR(32)")
	private String randomCode;

	/**
	 * 報表檔案OID
	 */
	@Column(name = "REPORTOIDFILE", length = 32, columnDefinition = "CHAR(32)")
	private String reportOidFile;

	/**
	 * 資料修改人(行編)
	 * <p/>
	 * 員編
	 */
	@Column(name = "UPDATER", length = 6, columnDefinition = "VARCHAR(6)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 刪除註記日期
	 */
//	@Temporal(TemporalType.TIMESTAMP)
	@Column(columnDefinition = "TIMESTAMP")
	private Timestamp deletedTime;

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}

	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得分行代碼 **/
	public String getBranch() {
		return this.branch;
	}

	/** 設定分行代碼 **/
	public void setBranch(String value) {
		this.branch = value;
	}

	/** 取得資料日期 **/
	public Date getDataDate() {
		return this.dataDate;
	}

	/** 設定資料日期 **/
	public void setDataDate(Date value) {
		this.dataDate = value;
	}

	/** 取得起日 **/
	public Date getBgnDate() {
		return this.bgnDate;
	}

	/** 設定起日 **/
	public void setBgnDate(Date value) {
		this.bgnDate = value;
	}

	/** 取得迄日 **/
	public Date getEndDate() {
		return this.endDate;
	}

	/** 設定迄日 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/** 取得報表代號 **/
	public String getRptNo() {
		return this.rptNo;
	}

	/** 設定報表代號 **/
	public void setRptNo(String value) {
		this.rptNo = value;
	}

	/** 取得報表名稱 **/
	public String getRptName() {
		return this.rptName;
	}

	/** 設定報表名稱 **/
	public void setRptName(String value) {
		this.rptName = value;
	}

	/**
	 * 取得是否為最新報表
	 * <p/>
	 * Y/N，分行
	 */
	public String getNowRpt() {
		return this.nowRpt;
	}

	/**
	 * 設定是否為最新報表
	 * <p/>
	 * Y/N，分行
	 **/
	public void setNowRpt(String value) {
		this.nowRpt = value;
	}

	/** 取得批次時間 **/
	public Date getBthDate() {
		return this.bthDate;
	}

	/** 設定批次時間 **/
	public void setBthDate(Date value) {
		this.bthDate = value;
	}

	/**
	 * 取得備註或其他需使用的值
	 * <p/>
	 * name=value;name=value
	 */
	public String getRemarks() {
		return this.remarks;
	}

	/**
	 * 設定備註或其他需使用的值
	 * <p/>
	 * name=value;name=value
	 **/
	public void setRemarks(String value) {
		this.remarks = value;
	}

	/** 取得分行傳送授管處時間 **/
	public Date getSendTime() {
		return this.sendTime;
	}

	/** 設定分行傳送授管處時間 **/
	public void setSendTime(Date value) {
		this.sendTime = value;
	}

	/**
	 * 取得授管處核備經辦
	 * <p/>
	 * 自動分案
	 */
	public String getJingBan() {
		return this.jingBan;
	}

	/**
	 * 設定授管處核備經辦
	 * <p/>
	 * 自動分案
	 **/
	public void setJingBan(String value) {
		this.jingBan = value;
	}

	/** 取得授管處核備時間 **/
	public Date getCfrmTime() {
		return this.cfrmTime;
	}

	/** 設定授管處核備時間 **/
	public void setCfrmTime(Date value) {
		this.cfrmTime = value;
	}

	/**
	 * 取得授管處核備註記
	 * <p/>
	 * Y/N
	 */
	public String getCfrmFlag() {
		return this.cfrmFlag;
	}

	/**
	 * 設定授管處核備註記
	 * <p/>
	 * Y/N
	 **/
	public void setCfrmFlag(String value) {
		this.cfrmFlag = value;
	}

	/**
	 * 取得報表亂碼
	 * <p/>
	 * 產檔時一併產生
	 */
	public String getRandomCode() {
		return this.randomCode;
	}

	/**
	 * 設定報表亂碼
	 * <p/>
	 * 產檔時一併產生
	 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/**
	 * 取得資料修改人(行編)
	 * <p/>
	 * 員編
	 */
	public String getUpdater() {
		return this.updater;
	}

	/**
	 * 設定資料修改人(行編)
	 * <p/>
	 * 員編
	 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定資料修改日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * @param sender
	 *            the sender to set
	 */
	public void setSender(String sender) {
		this.sender = sender;
	}

	/**
	 * @return the sender
	 */
	public String getSender() {
		return sender;
	}

	/**
	 * @param reportOidFile
	 *            the reportOidFile to set
	 */
	public void setReportOidFile(String reportOidFile) {
		this.reportOidFile = reportOidFile;
	}

	/**
	 * @return the reportOidFile
	 */
	public String getReportOidFile() {
		return reportOidFile;
	}

	/**
	 * get the deletedTime
	 * 
	 * @return the deletedTime
	 */
	public Timestamp getDeletedTime() {
		return deletedTime;
	}

	/**
	 * set the deletedTime
	 * 
	 * @param deletedTime
	 *            the deletedTime to set
	 */
	public void setDeletedTime(Timestamp deletedTime) {
		this.deletedTime = deletedTime;
	}

}
