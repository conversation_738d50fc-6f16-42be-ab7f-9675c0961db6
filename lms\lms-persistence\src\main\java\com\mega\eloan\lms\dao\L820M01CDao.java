/* 
 * L820M01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L820M01C;

/** 以房養老貸款撥款前查詢明細檔 **/
public interface L820M01CDao extends IGenericDao<L820M01C> {

	L820M01C findByOid(String oid);
	
	List<L820M01C> findByMainId(String mainId);
	
	L820M01C findByUniqueKey(String mainId, String custId, String dupNo, String cntrNo);

	List<L820M01C> findByIndex01(String mainId, String custId, String dupNo);
}