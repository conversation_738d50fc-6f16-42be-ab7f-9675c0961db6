/* 
 * MisEJF366Service.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service;

import java.util.List;
import java.util.Map;


/**
 * <pre>
 * MIS.ACM009>>MIS.EJV30702>>MIS.EJF307個人任職董監事資料
 * </pre>
 * 
 * @since 2012/03/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/03/23,TimChiang,new
 *          </ul>
 */
public interface MisEJF307Service {
	
	/**
	 * 查詢經營事業資料</br>
	 * 經營事業或職業_引進經營事業資料
	 * @param id 客戶統一編號
	 * @param prodId 查詢產品別
	 * @param statusCode 公司設立狀況代碼  (對照表)
	 * @param codeType 代號類別
	 * @return Map
	 */
	List<Map<String, Object>> findGuarantorBusiness(String id, String prodId, String statusCode, String codeType);
//	
//	/**
//	 * 取得客戶任職董監事的企業基本資訊</br>
//	 * 使用時機>>徵信-參：董監事名單及其信用狀況
//	 * @param id 查詢對象IDN
//	 * @return Map
//	 */
//	Map<String, Object> findCorpInfoOfDirectorSupervisor(String id);
//
//	/**
//	 * <nl>取得客戶任職董監事的企業基本資訊(多筆)</nl>
//	 * <dl>功能說明	取得董監事任職董監事的企業基本資訊</dl>
//	 * <dl>使用時機	徵信/參：董監事名單及其信用狀況</dl>
//	 * @param id String
//	 * @return list
//	 */
//	List<Map<String, Object>> findCorpsInfo(String id);

}
