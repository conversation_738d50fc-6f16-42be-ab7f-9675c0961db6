/* 
 * MisELF603Service.java
 */

package com.mega.eloan.lms.mfaloan.service;

/**
 * <pre>
 * 貸後追蹤分項紀錄檔 ELF603 (MIS.ELF603)
 * </pre>
 * 
 * @since 2024/05/07
 * @version <ul>
 *          2024/05/07,new
 *          </ul>
 */
public interface MisELF603Service {

	/**
	 * 
	 * @param uid
	 *            簽報書mainid
	 * @param apptime
	 *            簽報書核准時間
	 * @param cntrNo
	 *            額度序號
	 * @param seqno
	 *            序號

	 * @param esgtype
	 *            類別
	 * @param esgmodel
	 *            ESG模板
	 * @param tracond
	 *            起始追蹤日
	 * @param traprofik
	 *            追蹤週期
	 * @param tramonth
	 *            週期月
	 * @param content
	 *            內容
	 * @param updater
	 *            異動人員號碼
	 * @param updatetime
	 *            異動日期
	 */
	void insertForInside(String uid, String apptime, String cntrNo, int seqno, 
			String esgtype, String esgmodel, String tracond, String traprofik,
			int tramonth, String content, String updater, String updatetime);

	/**
	 * 刪除
	 * 
	 * @param uid
	 *            簽報書mainid
	 * @param apptime
	 *            簽案核准時間
	 */
	void delByUidIdAppDate(String uid, String apptime);
	
	/**
	 * 刪除
	 * 
	 * @param uid
	 *            簽報書mainid
	 * @param seqno
	 *            序號
	 */
	void delByUid(String uid);
	
	/**
	 * 刪除
	 * 
	 * @param uid
	 *            簽報書mainid
	 * @param apptime
	 *            簽案核准時間
	 * @param cntrNo
	 *            額度序號
	 *            
	 */
	void delByUidIdAppDateCntrNo(String uid, String apptime, String cntrNo);

}
