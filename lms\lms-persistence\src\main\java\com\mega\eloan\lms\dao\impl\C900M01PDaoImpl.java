/* 
 * C900M01PDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C900M01PDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900M01P;

/** PD等級分組對應表 **/
@Repository
public class C900M01PDaoImpl extends LMSJpaDao<C900M01P, String>
	implements C900M01PDao {

	@Override
	public C900M01P findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<C900M01P> findByDocTypeAndVersionAndCrdType(String docType, String pdVersion, String pdCrdType){
		ISearch search = createSearchTemplete();
		List<C900M01P> list = null;

		search.addSearchModeParameters(SearchMode.EQUALS, "docType", docType);
		search.addSearchModeParameters(SearchMode.EQUALS, "pdVersion", pdVersion);
		search.addSearchModeParameters(SearchMode.EQUALS, "pdCrdType", pdCrdType);
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}
}