/*!
 * 準備登入畫面執行loadScript所需的Javascript.
 */

var baseUrl = '../';
require.config({
	urlArgs : jsCache,
	waitSeconds : 200,
	baseUrl : baseUrl,
	paths : {
		'jquery': 'js/lib/jquery/3.6.0/jquery-3.6.0',
		'jqueryui': 'js/lib/jquery-ui/1.13.2/jquery-ui'
	},
	shim : {
		'jqueryui' : [ 'jquery' ]
	}
});

window.loadScript = function(url) {
	require([ 'jquery', 'jqueryui' ], function() {
		console.log('loginjs init');
		require([ url ], function(pageJs) {
			console.log(url + ' loaded');
		});
	});
};
