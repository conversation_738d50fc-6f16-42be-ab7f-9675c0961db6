/* 
 * L120S01D.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 企金銀行法／金控法利害關係人檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S01D", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class L120S01D extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 授信有無銀行法所稱與本行有利害關係（查詢日）
	 * <p/>
	 * 查詢日期為引進當天日期，引進不列印
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "MBDATE", columnDefinition = "DATE")
	private Date mbDate;

	/**
	 * 授信有無銀行法所稱與本行有利害關係人
	 * <p/>
	 * 1.有、2.無、3.不適用
	 */
	@Column(name = "MBRLT", length = 1, columnDefinition = "VARCHAR(1)")
	private String mbRlt;

	/**
	 * 銀行法關係內容
	 * <p/>
	 * 600個全型字<br/>
	 * 若有銀行法所稱與本行有利害關係者，一併列出關係內容及「授信影響等級」。<br/>
	 * 例：「本行轉投資事業，授信影響等級：所在分行」、「本行國內辦理授信人員，授信影響等級：全行」
	 */
	@Column(name = "MBRLTDSCR", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String mbRltDscr;

	/** 授信有無金控法44/45所稱金控利害關係人（查詢日） **/
	@Temporal(TemporalType.DATE)
	@Column(name = "MHDATE", columnDefinition = "DATE")
	private Date mhDate;

	/**
	 * 有無金控法44條所稱與金控有利害關係者(適用授信業務)
	 * <p/>
	 * 1.有、2.無、3.不適用
	 */
	@Column(name = "MHRLT44", length = 1, columnDefinition = "CHAR(1)")
	private String mhRlt44;

	/**
	 * 有無金控法45條所稱與金控有利害關係者(適用非授信業務)
	 * <p/>
	 * 1.有、2.無、3.不適用
	 */
	@Column(name = "MHRLT45", length = 1, columnDefinition = "CHAR(1)")
	private String mhRlt45;

	/**
	 * 金控法44條關係內容
	 * <p/>
	 * 600個全型字<br/>
	 * 授信「有」金控法44條所稱與金控有利害關係人時，引進金控法利害關係人檔之關係人原因，可修改。或自行填寫。
	 */
	@Column(name = "MHRLT44DSCR", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String mhRlt44Dscr;

	/**
	 * 金控法45條關係內容
	 * <p/>
	 * 600個全型字<br/>
	 * 授信「有」金控法45條所稱與金控有利害關係人時，引進金控法利害關係人檔之關係人原因，可修改。或自行填寫。
	 */
	@Column(name = "MHRLT45DSCR", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String mhRlt45Dscr;

	/**
	 * 銀行法/金控法利害關係人說明事項
	 * <p/>
	 * 100/11/24新增<br/>
	 * 600個全型字
	 */
	@Column(name = "MBMHRLTDSCR", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String mbMhRltDscr;

	/**
	 * 應收帳款承購無追索權－買方，有無銀行法所稱與本行有利害關係人
	 * <p/>
	 * 1.有、2.無、3.不適用
	 */
	@Column(name = "FCTMBRLT", length = 1, columnDefinition = "CHAR(1)")
	private String fctMbRlt;

	/**
	 * 應收帳款承購無追索權－買方，有無金控法所稱金控利害關係人
	 * <p/>
	 * 1.有、2.無、3.不適用
	 */
	@Column(name = "FCTMHRLT", length = 1, columnDefinition = "CHAR(1)")
	private String fctMhRlt;

	/**
	 * 應收帳款承購無追索權利害關係人說明事項
	 * <p/>
	 * 600個全型字
	 */
	@Column(name = "FCTMHRLTDSCR", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String fctMhRltDscr;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 授信有無銀行法所稱與本行有利害關係人
	 * <p/>
	 * 1.有、2.無、3.不適用
	 */
	@Column(name = "MBRLT33", length = 1, columnDefinition = "VARCHAR(1)")
	private String mbRlt33;

	/**
	 * 銀行法關係內容
	 * <p/>
	 * 600個全型字<br/>
	 * 本案「銀行法第33條之2、銀行法第33條之4」之情形
	 */
	@Column(name = "MBRLTDSCR33", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String mbRltDscr33;

	/**
	 * 是否為公司法第206條第三項與本行董事具有控制從屬關係之公司
	 * <p/>
	 * 1.有、2.無、3.不適用
	 */
	@Column(name = "CARLT206", length = 1, columnDefinition = "VARCHAR(1)")
	private String caRlt206;

	/**
	 * 銀行法關係內容
	 * <p/>
	 * 600個全型字<br/>
	 * 公司法第206條董監事名單內容
	 */
	@Column(name = "CARLT206DSCR", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String caRlt206Dscr;

	/** 公司法第206條第三項與本行董事具有控制從屬關係之公司（查詢日） **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CARLT206DATE", columnDefinition = "DATE")
	private Date caRlt206Date;

	/**
	 * 有無依當地法規所規範之關係人(含實質關係人)授信
	 * <p/>
	 * 1.有、2.無、3.不適用
	 */
	@Column(name = "LOCALRLT", length = 1, columnDefinition = "VARCHAR(1)")
	private String localRlt;

	/**
	 * 當地法規關係內容
	 * <p/>
	 * 600個全型字<br/>
	 * 
	 */
	@Column(name = "LOCALRLTDSCR", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String localRltDscr;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得授信有無銀行法所稱與本行有利害關係（查詢日）
	 * <p/>
	 * 查詢日期為引進當天日期，引進不列印
	 */
	public Date getMbDate() {
		return this.mbDate;
	}

	/**
	 * 設定授信有無銀行法所稱與本行有利害關係（查詢日）
	 * <p/>
	 * 查詢日期為引進當天日期，引進不列印
	 **/
	public void setMbDate(Date value) {
		this.mbDate = value;
	}

	/**
	 * 取得授信有無銀行法所稱與本行有利害關係人
	 * <p/>
	 * 1.有、2.無、3.不適用
	 */
	public String getMbRlt() {
		return this.mbRlt;
	}

	/**
	 * 設定授信有無銀行法所稱與本行有利害關係人
	 * <p/>
	 * 1.有、2.無、3.不適用
	 **/
	public void setMbRlt(String value) {
		this.mbRlt = value;
	}

	/**
	 * 取得銀行法關係內容
	 * <p/>
	 * 64個全型字<br/>
	 * 若有銀行法所稱與本行有利害關係者，一併列出關係內容及「授信影響等級」。<br/>
	 * 例：「本行轉投資事業，授信影響等級：所在分行」、「本行國內辦理授信人員，授信影響等級：全行」
	 */
	public String getMbRltDscr() {
		return this.mbRltDscr;
	}

	/**
	 * 設定銀行法關係內容
	 * <p/>
	 * 64個全型字<br/>
	 * 若有銀行法所稱與本行有利害關係者，一併列出關係內容及「授信影響等級」。<br/>
	 * 例：「本行轉投資事業，授信影響等級：所在分行」、「本行國內辦理授信人員，授信影響等級：全行」
	 **/
	public void setMbRltDscr(String value) {
		this.mbRltDscr = value;
	}

	/** 取得授信有無金控法44/45所稱金控利害關係人（查詢日） **/
	public Date getMhDate() {
		return this.mhDate;
	}

	/** 設定授信有無金控法44/45所稱金控利害關係人（查詢日） **/
	public void setMhDate(Date value) {
		this.mhDate = value;
	}

	/**
	 * 取得有無金控法44條所稱與金控有利害關係者(適用授信業務)
	 * <p/>
	 * 1.有、2.無、3.不適用
	 */
	public String getMhRlt44() {
		return this.mhRlt44;
	}

	/**
	 * 設定有無金控法44條所稱與金控有利害關係者(適用授信業務)
	 * <p/>
	 * 1.有、2.無、3.不適用
	 **/
	public void setMhRlt44(String value) {
		this.mhRlt44 = value;
	}

	/**
	 * 取得有無金控法45條所稱與金控有利害關係者(適用非授信業務)
	 * <p/>
	 * 1.有、2.無、3.不適用
	 */
	public String getMhRlt45() {
		return this.mhRlt45;
	}

	/**
	 * 設定有無金控法45條所稱與金控有利害關係者(適用非授信業務)
	 * <p/>
	 * 1.有、2.無、3.不適用
	 **/
	public void setMhRlt45(String value) {
		this.mhRlt45 = value;
	}

	/**
	 * 取得金控法44條關係內容
	 * <p/>
	 * 64個全型字<br/>
	 * 授信「有」金控法44條所稱與金控有利害關係人時，引進金控法利害關係人檔之關係人原因，可修改。或自行填寫。
	 */
	public String getMhRlt44Dscr() {
		return this.mhRlt44Dscr;
	}

	/**
	 * 設定金控法44條關係內容
	 * <p/>
	 * 64個全型字<br/>
	 * 授信「有」金控法44條所稱與金控有利害關係人時，引進金控法利害關係人檔之關係人原因，可修改。或自行填寫。
	 **/
	public void setMhRlt44Dscr(String value) {
		this.mhRlt44Dscr = value;
	}

	/**
	 * 取得金控法45條關係內容
	 * <p/>
	 * 64個全型字<br/>
	 * 授信「有」金控法45條所稱與金控有利害關係人時，引進金控法利害關係人檔之關係人原因，可修改。或自行填寫。
	 */
	public String getMhRlt45Dscr() {
		return this.mhRlt45Dscr;
	}

	/**
	 * 設定金控法45條關係內容
	 * <p/>
	 * 64個全型字<br/>
	 * 授信「有」金控法45條所稱與金控有利害關係人時，引進金控法利害關係人檔之關係人原因，可修改。或自行填寫。
	 **/
	public void setMhRlt45Dscr(String value) {
		this.mhRlt45Dscr = value;
	}

	/**
	 * 取得銀行法/金控法利害關係人說明事項
	 * <p/>
	 * 100/11/24新增<br/>
	 * 64個全型字
	 */
	public String getMbMhRltDscr() {
		return this.mbMhRltDscr;
	}

	/**
	 * 設定銀行法/金控法利害關係人說明事項
	 * <p/>
	 * 100/11/24新增<br/>
	 * 64個全型字
	 **/
	public void setMbMhRltDscr(String value) {
		this.mbMhRltDscr = value;
	}

	/**
	 * 取得應收帳款承購無追索權－買方，有無銀行法所稱與本行有利害關係人
	 * <p/>
	 * 1.有、2.無、3.不適用
	 */
	public String getFctMbRlt() {
		return this.fctMbRlt;
	}

	/**
	 * 設定應收帳款承購無追索權－買方，有無銀行法所稱與本行有利害關係人
	 * <p/>
	 * 1.有、2.無、3.不適用
	 **/
	public void setFctMbRlt(String value) {
		this.fctMbRlt = value;
	}

	/**
	 * 取得應收帳款承購無追索權－買方，有無金控法所稱金控利害關係人
	 * <p/>
	 * 1.有、2.無、3.不適用
	 */
	public String getFctMhRlt() {
		return this.fctMhRlt;
	}

	/**
	 * 設定應收帳款承購無追索權－買方，有無金控法所稱金控利害關係人
	 * <p/>
	 * 1.有、2.無、3.不適用
	 **/
	public void setFctMhRlt(String value) {
		this.fctMhRlt = value;
	}

	/**
	 * 取得應收帳款承購無追索權利害關係人說明事項
	 * <p/>
	 * 64個全型字
	 */
	public String getFctMhRltDscr() {
		return this.fctMhRltDscr;
	}

	/**
	 * 設定應收帳款承購無追索權利害關係人說明事項
	 * <p/>
	 * 64個全型字
	 **/
	public void setFctMhRltDscr(String value) {
		this.fctMhRltDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/**
	 * 設定本案「銀行法第33條之2、銀行法第33條之4」之情形
	 * <p/>
	 **/
	public void setMbRlt33(String mbRlt33) {
		this.mbRlt33 = mbRlt33;
	}

	/**
	 * 取得本案「銀行法第33條之2、銀行法第33條之4」之情形
	 * <p/>
	 */
	public String getMbRlt33() {
		return mbRlt33;
	}

	/**
	 * 取得本案「銀行法第33條之2、銀行法第33條之4」之情形說明
	 * <p/>
	 */
	public String getMbRltDscr33() {
		return this.mbRltDscr33;
	}

	/**
	 * 設定本案「銀行法第33條之2、銀行法第33條之4」之情形說明
	 * <p/>
	 **/
	public void setMbRltDscr33(String value) {
		this.mbRltDscr33 = value;
	}

	/**
	 * J-108-0178_05097_B1001 Web
	 * e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
	 * 
	 * 設定是否為公司法第206條第三項與本行董事具有控制從屬關係之公司
	 * 
	 * @param caRlt206
	 */
	public void setCaRlt206(String caRlt206) {
		this.caRlt206 = caRlt206;
	}

	/**
	 * J-108-0178_05097_B1001 Web
	 * e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
	 * 
	 * 取得是否為公司法第206條第三項與本行董事具有控制從屬關係之公司
	 * 
	 * @param caRlt206
	 */
	public String getCaRlt206() {
		return caRlt206;
	}

	/**
	 * J-108-0178_05097_B1001 Web
	 * e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
	 * 
	 * 設定公司法第206條董監事名單內容
	 * 
	 * @param caRlt206Dscr
	 */
	public void setCaRlt206Dscr(String caRlt206Dscr) {
		this.caRlt206Dscr = caRlt206Dscr;
	}

	/**
	 * J-108-0178_05097_B1001 Web
	 * e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
	 * 
	 * 取得公司法第206條董監事名單內容
	 * 
	 * @return
	 */
	public String getCaRlt206Dscr() {
		return caRlt206Dscr;
	}

	/**
	 * J-108-0178_05097_B1001 Web
	 * e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
	 * 
	 * 設定公司法第206條第三項與本行董事具有控制從屬關係之公司（查詢日）
	 * 
	 * @param caRlt206Date
	 */
	public void setCaRlt206Date(Date caRlt206Date) {
		this.caRlt206Date = caRlt206Date;
	}

	/**
	 * J-108-0178_05097_B1001 Web
	 * e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
	 * 
	 * 取得公司法第206條第三項與本行董事具有控制從屬關係之公司（查詢日）
	 * 
	 * @return
	 */
	public Date getCaRlt206Date() {
		return caRlt206Date;
	}

	/**
	 * J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
	 * 
	 * 設定有無依當地法規所規範之關係人(含實質關係人)授信 localRltDscr 當地法規關係內容
	 * 
	 * @return
	 */
	public void setLocalRlt(String localRlt) {
		this.localRlt = localRlt;
	}

	/**
	 * J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
	 * 
	 * 取得有無依當地法規所規範之關係人(含實質關係人)授信 localRltDscr 當地法規關係內容
	 * 
	 * @return
	 */
	public String getLocalRlt() {
		return localRlt;
	}

	/**
	 * J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
	 * 
	 * 設定當地法規關係內容
	 * 
	 * @return
	 */
	public void setLocalRltDscr(String localRltDscr) {
		this.localRltDscr = localRltDscr;
	}

	/**
	 * J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
	 * 
	 * 取得當地法規關係內容
	 * 
	 * @return
	 */
	public String getLocalRltDscr() {
		return localRltDscr;
	}
}
