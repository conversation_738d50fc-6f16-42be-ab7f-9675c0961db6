package com.mega.eloan.lms.lms.flow;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.provider.AnnotationHandler.Transition;

import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.dao.CommonMetaDao;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.gwclient.EmailClient;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FlowNameService.FlowReturnEnum;
import com.mega.eloan.lms.base.service.FlowNameService.FlowbackUnitEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.dao.L120A01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01FDao;
import com.mega.eloan.lms.dao.L120M01HDao;
import com.mega.eloan.lms.dao.L130M01ADao;
import com.mega.eloan.lms.dao.L130S01BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L700M01ADao;
import com.mega.eloan.lms.dao.L730M01ADao;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.model.L120A01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L120M01H;
import com.mega.eloan.lms.model.L130M01A;
import com.mega.eloan.lms.model.L130S01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L700M01A;
import com.mega.eloan.lms.model.L730M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 案件簽報書 - 營運中心流程
 * </pre>
 * 
 * @since 2011/11/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/2,REX,new
 *          <li>2013/1/8,Rex,若typcd =海外 核准文號 需增加外
 *          <li>2013/06/25,Rex,增加缺少的近期已收案件
 *          <li>2017/01/06,Johnny Lin J-105-0302 簽報發信件通知功能
 *          </ul>
 */
@Component
public class LMS1205AreaFlow extends AbstractFlowHandler {
	@Resource
	CommonMetaDao metaDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L700M01ADao l700m01aDao;

	@Resource
	L120A01ADao l120a01aDao;

	@Resource
	L730M01ADao l730m01aDao;

	@Resource
	L130S01BDao l130s01bDao;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	DocLogService docLogService;

	@Resource
	LMSService lmsService;

	@Resource
	CLSService clsService;

	@Resource
	NumberService numberService;

	@Resource
	L120M01HDao l120m01hDao;

	@Resource
	L130M01ADao l130m01aDao;

	@Resource
	L120M01FDao l120m01fDao;

	@Resource
	private EmailClient emailClient;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	private SysParameterService sysParameterService;

	public static final String FLOW_CODE = "LMS1205AreaFlow";

	@Transition(node = "決策_營運中心", value = "to待放行")
	public void decision3(FlowInstance instance) {
		// 如果是核定、泰國要走泰國提會流程
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		// 當為總處分行授權內才需檢查是否有額度批覆表(陳復述案沒有額度明細與額度批覆表，所以不用此檢核)
		if (UtilConstants.Casedoc.DocKind.授權內.equals(meta.getDocKind())
				&& LMSUtil.isSpecialBranch(Util.trim(meta.getCaseBrId()))
				&& !UtilConstants.Casedoc.DocCode.陳復陳述案.equals(meta
						.getDocCode())) {
			List<L140M01A> l140m01as2 = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度批覆表, null);
			if (l140m01as2.isEmpty()) {
				// J-111-0488_05097_B1001 Web
				// e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
				if (!lmsService.isOverAuthSendHead(meta)) {
					// EFD3015=WARN|尚未產生額度批覆表不得呈主管覆核！！|
					throw new FlowMessageException("EFD3015");
				}
			}
			// 檢查額度明細表 的額度序號批附表是否都產生
			HashMap<String, String> checkCntrNo = new HashMap<String, String>();
			for (L140M01A l140m01a : l140m01as2) {
				checkCntrNo.put(l140m01a.getCntrNo(), "");
				if (FlowDocStatusEnum.編製中.getCode().equals(
						l140m01a.getDocStatus())) {
					// EFD3012=WARN|所有額度批覆表需由經辦執行【批覆】後才能呈主管覆核|
					throw new FlowMessageException("EFD3012");
				}
				if (!UtilConstants.DEFAULT.是.equals(l140m01a.getChkYN())) {
					// EFD3024=WARN|額度批覆表有異動，請執行【計算授信額度合計】以確保額度批覆表之「授信額度合計」為正確數值。|
					throw new FlowMessageException("EFD3024");
				}
			}
		}
		// 當不等於異常通報且不為總處分行才檢查額度明細表
		if (!UtilConstants.Casedoc.DocCode.異常通報.equals(meta.getDocCode())
				&& !UtilConstants.Casedoc.DocCode.陳復陳述案.equals(meta
						.getDocCode())
				&& !LMSUtil.isSpecialBranch(Util.trim(meta.getCaseBrId()))
				&& UtilConstants.Casedoc.DocKind.授權內.equals(meta.getDocKind())) {
			List<L140M01A> l140m01as2 = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度批覆表, null);
			if (l140m01as2.isEmpty()) {
				// EFD3015=WARN|尚未產生額度批覆表不得呈主管覆核！！|
				// J-111-0488_05097_B1001 Web
				// e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
				if (!lmsService.isOverAuthSendHead(meta)) {
					throw new FlowMessageException("EFD3015");
				}

			}
			// 檢查額度明細表 的額度序號批附表是否都產生
			HashMap<String, String> checkCntrNo = new HashMap<String, String>();
			for (L140M01A l140m01a : l140m01as2) {
				checkCntrNo.put(l140m01a.getCntrNo(), "");
				if (FlowDocStatusEnum.編製中.getCode().equals(
						l140m01a.getDocStatus())) {
					// EFD3012=WARN|所有額度批覆表需由經辦執行【批覆】後才能呈主管覆核|
					throw new FlowMessageException("EFD3012");
				}
				if (!UtilConstants.DEFAULT.是.equals(l140m01a.getChkYN())) {
					// EFD3024=WARN|額度批覆表有異動，請執行【計算授信額度合計】以確保額度批覆表之「授信額度合計」為正確數值。|
					throw new FlowMessageException("EFD3024");
				}
			}

			List<L140M01A> l140m01as1 = l140m01aDao
					.findL140m01aListByL120m01cMainIdOrderByCust(
							meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度明細表);
			StringBuffer cntrnoMsg = new StringBuffer();
			for (L140M01A l140m01a : l140m01as1) {
				// 當不存在把這額度序號記錄下來
				if (!checkCntrNo.containsKey(l140m01a.getCntrNo())) {
					cntrnoMsg.append(cntrnoMsg.length() > 0 ? "、" : "");
					cntrnoMsg.append(l140m01a.getCntrNo());
				}
			}
			// 要出現的額度序號訊息
			if (cntrnoMsg.length() > 0) {
				// EFD3036=ERROR|尚未產生額度序號$\{cntrNo\}額度批覆表不得呈主管覆核！|

				// J-111-0488_05097_B1001 Web
				// e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
				if (!lmsService.isOverAuthSendHead(meta)) {
					HashMap<String, String> extraMessage = new HashMap<String, String>();
					extraMessage
							.put("cntrNo", " " + cntrnoMsg.toString() + " ");
					FlowMessageException msg = new FlowMessageException(
							"EFD3036");
					msg.setExtraMessage(extraMessage);
					throw msg;
				}

			}

		}

		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.營運中心,
				UtilConstants.STAFFJOB.經辦L1);
		meta.setHqMeetFlag(null);
	}

	// 免批覆案件
	@Transition(node = "決策_營運中心", value = "to核定")
	public void noCheckCase(FlowInstance instance) throws FlowException,
			CapException {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		meta.setOwnBrId(meta.getCaseBrId());
		meta.setAreaSendInfo(CapDate.getCurrentTimestamp());
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.營運中心,
				UtilConstants.STAFFJOB.經辦L1);
		meta.setHqMeetFlag(null);
		lmsService.upLnunid(meta);
		l120m01aDao.save(meta);

		// J-105-0302 簽報發信件通知功能
		notifyByMail(instance, "1", CreditDocStatusEnum.營運中心_已核准.name());
	}

	// 如果其中一份額度批覆表要進入已婉卻，海外流程沒有
	@Transition(node = "營運中心_待放行核定", value = "to判斷_營運中心")
	public void check(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		String result = (String) instance.getAttribute("result");

		if (!"to退回".equals(result)) {
			// 檢查主管不能與覆核人員相同
			lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.營運中心,
					UtilConstants.STAFFJOB.經辦L1);
			if (UtilConstants.Casedoc.DocKind.授權內.equals(meta.getDocKind())
					&& UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(meta
							.getAuthLvl())) {

				// 當為異常通報時走核定
				if (!UtilConstants.Casedoc.DocCode.異常通報.equals(meta
						.getDocCode())) {

					IBranch branch = branchService
							.getBranch(meta.getCaseBrId());
					String brNoType = branch.getCountryType();
					if (UtilConstants.Country.泰國.equals(brNoType)) {
						instance.setAttribute("result", "to泰國提會流程");
					} else {
						// 案件簽報書內批覆表有一份是核准即是核准
						List<L140M01A> l140m01as = l140m01aDao
								.findL140m01aListByL120m01cMainId(
										meta.getMainId(),
										UtilConstants.Cntrdoc.ItemType.額度批覆表,
										FlowDocStatusEnum.已核准.getCode());

						if (l140m01as.isEmpty()) {
							instance.setAttribute("result", "to已婉卻");
						} else {
							instance.setAttribute("result", "to核定");
						}
					}

				} else {
					instance.setAttribute("result", "to核定");
				}
			}

		}
	}

	/**
	 * 營運中心退回分行更正，將狀態改為待補件
	 * 
	 * @param instance
	 *            流程實體
	 */
	@Transition(node = "決策_營運中心", value = "to待補件")
	public void backUtil(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		List<L120M01H> listL120m01h = l120m01hDao
				.findByMainId(meta.getMainId());
		// 變更目前編製中分行
		String ownBrId = meta.getCaseBrId();
		meta.setOwnBrId(ownBrId);
		// 新增近期已收
		lmsService.saveL000M01A(meta, ownBrId);
		// 變更額度明細表為編製中
		lmsService.resetL140M01A(meta, FlowDocStatusEnum.編製中.getCode());
		meta.setBackUnit(FlowbackUnitEnum.營運中心_退回分行.getCode());
		meta.setHqMeetFlag(null);
		IBranch branch = branchService.getBranch(ownBrId);
		// 先要檢查目前要退的分行是不是總行，如果要退的分行有總行 要砍掉有總行的那筆資料
		if (Util.isNotEmpty(Util.trim(branch.getParentBrNo()))) {
			L120A01A l120a01a = l120a01aDao.findByAuthUnit(meta.getMainId(),
					branch.getParentBrNo());
			if (l120a01a != null) {
				// 紐約分行0A2 ACC 總行授權內要走分行授權內流程，故分行設定檔有勾0A2 母行為 0A2
				if (Util.notEquals(branch.getParentBrNo(), ownBrId)) {
					l120a01aDao.delete(l120a01a);
				}
			}
		}
		if (LMSUtil.isClsCase(meta)) {
			// 退回待補件要順便將授管處提會等相關內容刪除
			if (listL120m01h != null && !listL120m01h.isEmpty()) {
				l120m01hDao.delete(listL120m01h);
			}
			meta.setRptTitle1(null);
			meta.setRptTitle2(null);
			meta.setMeetingType(null);
			meta.setHqMeetFlag(null);
			meta.setRptTitleArea1(null);
			lmsService.save(meta);
		}

		lmsService.deleteL120M01F(meta.getMainId(), null, new String[] {
				UtilConstants.STAFFJOB.執行覆核主管L4,
				UtilConstants.STAFFJOB.提會放行主管L8,
				UtilConstants.STAFFJOB.提會登錄經辦L7 });
		instance.setAttribute("flowCode", LMSUtil.getFlowCode(branch));
		instance.setAttribute("result", "waitCase");

		// J-105-0302 簽報發信件通知功能
		notifyByMail(instance, "1", CreditDocStatusEnum.營運中心_待更正.name());
	}

	/**
	 * 傳送到授管處
	 * 
	 * @param instance
	 *            流程實體
	 */
	@Transition(node = "判斷_營運中心", value = "to呈授管處")
	public void sendHead(FlowInstance instance) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		// 變更目前編製中分行
		String nextOwnbrid = UtilConstants.BankNo.授管處;
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		L700M01A l700m01a = l700m01aDao.findByBranchId(nextOwnbrid,
				meta.getCaseBrId());
		if (l700m01a != null) {
			meta.setHqAppraiser(l700m01a.getUserNo());
		}
		// 報案考核表要加上覆核人員
		L730M01A l730m01a = (L730M01A) l730m01aDao.findByMainId(Util.trim(meta
				.getMainId()));
		if (l730m01a != null) {
			if (user.getUnitNo().equals(Util.trim(l730m01a.getChkUnit()))) {
				l730m01a.setChkId(user.getUserId());
				l730m01aDao.save(l730m01a);
			}
		}
		meta.setOwnBrId(nextOwnbrid);
		meta.setHqMeetFlag(null);
		meta.setAreaSendInfo(CapDate.getCurrentTimestamp());
		// 判斷退補件欄位
		lmsService.checkBackUnit(meta);
		// 新增授權檔
		lmsService.saveL12A01A(meta, nextOwnbrid,
				DocAuthTypeEnum.VIEW_TRANSFER.getCode());
		// 新增近期已收
		lmsService.saveL000M01A(meta, nextOwnbrid);

		// 在放行時把分行放行時間塞入
		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByL120m01cMainId(meta.getMainId(),
						UtilConstants.Cntrdoc.ItemType.額度明細表, null);
		for (L140M01A l140m01a : l140m01as) {
			// 當聯行轉入明細表不蓋掉放行時間
			if (UtilConstants.Cntrdoc.DataSrc.轉入額度明細表.equals(l140m01a
					.getDataSrc())) {
				continue;
			}
			l140m01a.setAreaSendInfo(CapDate.getCurrentTimestamp());
		}
		l140m01aDao.save(l140m01as);
		IBranch theBranch = branchService.getBranch(Util.trim(meta
				.getCaseBrId()));
		// 異常通報Mail風控處與所選參貸行
		if (theBranch != null) {
			String mainId = Util.trim(meta.getMainId());
			List<L130S01B> listL130s01a = l130s01bDao.findByMainId(mainId);
			String brnGroup = Util.trim(theBranch.getBrnGroup());
			if (UtilConstants.BankNo.中部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北一區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北二區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.中區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南區營運中心.equals(brnGroup)) {
				// 營運中心制分行於營運中心覆核時傳送異常通報MAIL給風控處與參貸行
				if (UtilConstants.BankNo.中部區域授信中心.equals(user.getUnitNo())
						|| UtilConstants.BankNo.北一區營運中心
								.equals(user.getUnitNo())
						|| UtilConstants.BankNo.南部區域授信中心.equals(user
								.getUnitNo())
						|| UtilConstants.BankNo.北二區營運中心
								.equals(user.getUnitNo())
						|| UtilConstants.BankNo.桃竹苗區營運中心.equals(user
								.getUnitNo())
						|| UtilConstants.BankNo.中區營運中心.equals(user.getUnitNo())
						|| UtilConstants.BankNo.南區營運中心.equals(user.getUnitNo())) {
					if (UtilConstants.Casedoc.DocCode.異常通報.equals(meta
							.getDocCode())) {
						// 開始進行Mail動作
						lmsService.sendEmail(mainId, meta, listL130s01a, "0");

						// J-103-0027 核准時設定為需要傳送卡務中心

						// 為了時效性，營運中心制分行核准送營運中心sendArea或總處分行核准送授管處sendHead時就設定為要傳給卡務
						// 但結案註記要等授管處核准時才寫為Y
						L130M01A l130m01a = l130m01aDao.findByUniqueKey(mainId);
						if (l130m01a != null) {
							l130m01a.setNeedSend("Y");

							// J-105-0065-001 Web e-Loan
							// 授信管理系統修改異常通報副知風控處說明文字及新增統計報表
							l130m01a.setSend912MailTime(CapDate
									.getCurrentTimestamp());

							// l130m01a.setCloseFg(""); 結案註記要等授管處核准時才寫為Y
							// LMSServiceImpl.java \insertLnfe0854
							l130m01aDao.save(l130m01a);
						}

					}
				}
			}
		}
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.海外_呈授管處.getCode(), nextOwnbrid);
		instance.setAttribute("flowCode", LMS1205HeadFlow.FLOW_CODE);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.營運中心,
				UtilConstants.STAFFJOB.執行覆核主管L4);
	}

	// 泰國提會流程
	@Transition(node = "判斷_營運中心", value = "to泰國提會流程")
	public void toTh(FlowInstance instance) throws FlowException, CapException {
		this.toEndAction(instance, CreditDocStatusEnum.海外_已核准.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4, true);

		// J-105-0302 簽報發信件通知功能
		notifyByMail(instance, "1", CreditDocStatusEnum.營運中心_已核准.name());
	}

	// 核定
	@Transition(node = "判斷_營運中心", value = "to核定")
	public void complete(FlowInstance instance) throws FlowException,
			CapException {
		this.toEndAction(instance, CreditDocStatusEnum.海外_已核准.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4, false);

		// J-105-0302 簽報發信件通知功能
		notifyByMail(instance, "1", CreditDocStatusEnum.營運中心_已核准.name());

		// J-106-0246-002 Web e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
		lmsService.notifyByMailForG1To940(instance,
				CreditDocStatusEnum.營運中心_已核准.name());

	}

	// 已婉卻
	@Transition(node = "判斷_營運中心", value = "to已婉卻")
	public void reject(FlowInstance instance) throws FlowException,
			CapException {
		this.toEndAction(instance, CreditDocStatusEnum.海外_婉卻.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4, false);

		// J-105-0302 簽報發信件通知功能
		notifyByMail(instance, "1", CreditDocStatusEnum.營運中心_已婉卻.name());

		// J-106-0246-002 Web e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
		lmsService.notifyByMailForG1To940(instance,
				CreditDocStatusEnum.營運中心_已婉卻.name());

	}

	/**
	 * 當案件結束時做的動作
	 * 
	 * @param instance
	 *            flow 流程檔
	 * @param docstatus
	 *            文件狀態
	 * @param staffjob
	 *            職稱欄
	 * @throws CapException
	 * @throws FlowException
	 */
	private void toEndAction(FlowInstance instance, String docstatus,
			String staffjob, Boolean sendToY01) throws FlowException,
			CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);

		// 用來判斷為營運中心或授管處覆核
		meta.setIsHeadCheck(UtilConstants.DEFAULT.是);
		String docRslt = "";
		if (CreditDocStatusEnum.海外_已核准.getCode().equals(docstatus)) {
			docRslt = UtilConstants.Casedoc.DocRslt.承做;
			//J-110-0330 確定承做，寫入AO帳號管理員
			lmsService.gfnLNF013(meta);
		} else {
			docRslt = UtilConstants.Casedoc.DocRslt.婉卻;
		}
		lmsService.uploadELLNSEEK(meta);
		meta.setSignNo(lmsService.getAreaSignNo(meta));
		meta.setDocRslt(docRslt);
		if (sendToY01) {
			instance.setAttribute("flowCode", LMS1205TH02Flow.FLOW_CODE);
			// 新增近期已收
			lmsService.saveL000M01A(meta, UtilConstants.BankNo.泰國曼谷總行);
			meta.setOwnBrId(UtilConstants.BankNo.泰國曼谷總行);
		} else {
			// start 2013/06/25,Rex,增加缺少的近期已收案件
			lmsService.saveL000M01A(meta, meta.getCaseBrId());
			// end 2013/06/25,Rex,增加缺少的近期已收案件
			// 流程結束塞入原本分行
			meta.setOwnBrId(meta.getCaseBrId());
		}

		// J-111-0488_05097_B1001 Web
		// e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
		if (!lmsService.isOverAuthSendHead(meta)) {
			// 為核定要把額度明細表變成結案
			List<L140M01A> l140m01as2 = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度明細表, null);
			for (L140M01A l140m01a : l140m01as2) {
				l140m01a.setDocStatus(FlowDocStatusEnum.結案.getCode());
			}
			l140m01aDao.save(l140m01as2);
		}

		// J-111-0488_05097_B1001 Web e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
		List<L140M01A> l140m01as = null;
		if (lmsService.isOverAuthSendHead(meta)) {
			l140m01as = l140m01aDao.findL140m01aListByL120m01cMainId(
					meta.getMainId(), UtilConstants.Cntrdoc.ItemType.額度明細表,
					null);
		} else {
			l140m01as = l140m01aDao.findL140m01aListByL120m01cMainId(
					meta.getMainId(), UtilConstants.Cntrdoc.ItemType.額度批覆表,
					null);
		}

		// 取得所有批附表mainId
		List<String> mainIds = new ArrayList<String>();
		for (L140M01A l140m01a : l140m01as) {
			mainIds.add(l140m01a.getMainId());
			l140m01a.setApprover(user.getUserId());
			l140m01a.setApproveTime(CapDate.getCurrentTimestamp());
		}
		// 當為核准、且為國內個金案件需更新團貸母戶編號 至L140M03A grpCntrNo 以利動審表取得團貸額度明細表
		if (UtilConstants.Casedoc.DocRslt.承做.equals(docRslt)
				&& LMSUtil.isClsCase(meta)
				&& UtilConstants.Casedoc.DocCode.一般.equals(meta.getDocCode())) {
			lmsService.setL140M03AGrpCntrNo(meta, mainIds);
		}

		// 國內個金才會有上傳優惠房貸
		if (LMSUtil.isClsCase(meta)) {
			List<L140M01A> l140m01asBy431 = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度批覆表,
							FlowDocStatusEnum.已核准.getCode());
			for (L140M01A l140m01a : l140m01asBy431) {
				lmsService.upELF431(meta, l140m01a);
			}
		}
		// 設定授信報案考核表
		lmsService.setL730M01A(meta, user);
		l140m01aDao.save(l140m01as);
		// 設定營運中心放行人員
		// meta.setAreaAppraiser(user.getUserId());
		// 設定營運中心放行時間
		meta.setAreaSendInfo(CapDate.getCurrentTimestamp());
		l120m01aDao.save(meta);
		// 要去除多餘的營運中心授權檔，不然在已核准或已婉卻會多出現
		lmsService.checkL120A01A(meta);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.營運中心, staffjob);
		// 更新預約額度檔
		lmsService.gfnDB2SetELF442_CNTRNO(meta,
				UtilConstants.Cntrdoc.ACTION.覆核, docstatus);
		// 新增 核准額度資料檔 MIS.ELF447n
		// J-111-0488_05097_B1001 Web
		// e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
		if (lmsService.isOverAuthSendHead(meta)) {
			lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
					docstatus, meta.getCaseBrId());
		}else{
			lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度批覆表,
					docstatus, meta.getCaseBrId());
		}
		
		
		// 海外各單位 授信戶異常通報表 [總處批覆] 自動傳送一份副本至海管處T1
		if (UtilConstants.Casedoc.DocCode.異常通報.equals(meta.getDocCode())) {
			try {
				lmsService.genlms1205r27(null, meta.getMainId());
			} catch (Exception e) {

			}
		}
		
		lmsService.upLoadMIS(meta);
		lmsService.upLnunid(meta);

		// J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
		if (CreditDocStatusEnum.海外_已核准.getCode().equals(docstatus)) {
			if (!LMSUtil.isClsCase(meta)) {
				lmsService.uploadL902Data(meta, docstatus);
			}
		}

		// J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程
		IBranch theBranch = branchService.getBranch(Util.trim(meta
				.getCaseBrId()));
		// 異常通報Mail風控處與所選參貸行
		if (theBranch != null) {
			String mainId = Util.trim(meta.getMainId());
			List<L130S01B> listL130s01a = l130s01bDao.findByMainId(mainId);
			String brnGroup = Util.trim(theBranch.getBrnGroup());
			if (UtilConstants.BankNo.中部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北一區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北二區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.中區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南區營運中心.equals(brnGroup)) {
				// 營運中心制分行於營運中心覆核時傳送異常通報MAIL給風控處與參貸行
				if (UtilConstants.unitType.營運中心.equals(user.getUnitType())) {
					if (UtilConstants.Casedoc.DocCode.異常通報.equals(meta
							.getDocCode())) {
						// 開始進行Mail動作
						// J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程
						// J-112-0548 調整授信戶異常通報表副知風控處規則，註解此段sendEmail，由上面lmsService.upLoadMIS(meta)裡寄送通知
						//lmsService.sendEmail(mainId, meta, listL130s01a, "2");

						// J-103-0027 核准時設定為需要傳送卡務中心

						// 為了時效性，營運中心制分行核准送營運中心sendArea或總處分行核准送授管處sendHead時就設定為要傳給卡務
						// 但結案註記要等授管處核准時才寫為Y
						L130M01A l130m01a = l130m01aDao.findByUniqueKey(mainId);
						if (l130m01a != null) {
							l130m01a.setNeedSend("Y");

							// J-105-0065-001 Web e-Loan
							// 授信管理系統修改異常通報副知風控處說明文字及新增統計報表
							l130m01a.setSend912MailTime(CapDate
									.getCurrentTimestamp());

							// l130m01a.setCloseFg(""); 結案註記要等授管處核准時才寫為Y
							// LMSServiceImpl.java \insertLnfe0854
							l130m01aDao.save(l130m01a);
						}

					}
				}
			}
		}

		if (LMSUtil.isClsCase(meta)) {
			// 國內個金簽報書上傳項目
			clsService.upMisByCls(meta);
		}
	}

	/**
	 * 簽報發信件通知功能
	 * 
	 * @param instance
	 * @param branchType
	 *            單位類型 1. 分行 2. 母行/海外總行 3. 營運中心 4. 授管處 5. 徵信承作分行 6. 國金部
	 * @param status
	 * @version <ul>
	 *          <li>2017/01/06,Johnny Lin J-105-0302 簽報發信件通知功能
	 *          </ul>
	 */
	private void notifyByMail(FlowInstance instance, String branchType,
			String status) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		Meta meta = metaDao.findByOid(getDomainClass(), instanceId);
		if (meta == null) {
			// EFD0025=ERROR|執行有誤|
			throw new FlowMessageException("EFD0025");
		}

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String hostAddr = sysParameterService
				.getParamValue(SysParamConstants.MAIL_ADDRESS_HOST);
		String[] toAddrs = null;
		String sendUsrId = "";
		StringBuilder custStr = new StringBuilder();

		// 取得案號
		L120M01A l120m01a = l120m01aDao.findByMainId(meta.getMainId());
		if (l120m01a != null) {
			custStr.append("案號：");
			custStr.append(l120m01a.getCaseNo());
			custStr.append("  ");
		}

		boolean isPerson = false;
		Map<String, Object> custData = (Map<String, Object>) misCustdataService
				.findBussTypeByIdDupNo(meta.getCustId(), meta.getDupNo());
		if (custData != null && !custData.isEmpty()) {
			if (Util.equals(MapUtils.getString(custData, "BUSCD", ""), "060000")
					|| Util.equals(MapUtils.getString(custData, "BUSCD", ""),
							"130300")) {
				isPerson = true;
			}
		}

		// 若為個人戶，需作資料隱碼 (身分證後三碼半形O，客戶名稱第二個字 全形Ｏ)
		custStr.append("客戶代號：");
		custStr.append(isPerson ? (meta.getCustId().substring(0, 7) + "OOO ")
				: meta.getCustId());
		custStr.append(meta.getDupNo() + " ");
		custStr.append(" 客戶名稱：");
		custStr.append(isPerson ? (meta.getCustName().substring(0, 1) + "Ｏ" + meta
				.getCustName().substring(2)) : meta.getCustName());
		custStr.append("\n");

		if (StringUtils.indexOf(hostAddr, "@notes.") >= 0) { // Production
			// 取得經辦ID
			L120M01F l120m01f = l120m01fDao.findByMainIdAndKey(
					meta.getMainId(), branchType, null, "L1");
			if (l120m01f != null) {
				sendUsrId = l120m01f.getStaffNo();
				toAddrs = new String[] { sendUsrId.substring(1) + hostAddr };
			}
		} else {
			// 測試用，暫時寄給建霖，柏翰
			String testReceiverAddr = "<EMAIL>,<EMAIL>,<EMAIL>";
			toAddrs = testReceiverAddr.split(",");
		}

		String subject = "簽報通知--下列清單內的「案件簽報書」的目前最新狀態為【" + status + "】！";
		emailClient.send(toAddrs, subject, custStr.toString());
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L120M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}

	// J-109-0092_10702_B1001 Web e-Loan新增營運中心簽報書(審查中)加撤件功能
	// 撤件
	@Transition(node = "決策_營運中心", value = "to撤件")
	public void cancelCase(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		meta.setReturnFromBH(FlowReturnEnum.撤件.getCode());
		meta.setHqMeetFlag(null);
		meta.setOwnBrId(meta.getCaseBrId());
		meta.setEndDate(CapDate.getCurrentTimestamp()); // 主檔最後批示日
		l120m01aDao.save(meta);
		// 新增 核准額度資料檔 MIS.ELF447n
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.海外_待撤件.getCode(), meta.getCaseBrId());

		// J-105-0302 簽報發信件通知功能
		notifyByMail(instance, "1", CreditDocStatusEnum.營運中心_待分行撤件.name());
	}
}