/* 
 * LMS9535Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONObject;

import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S04B;
import com.mega.eloan.lms.model.L120S04C;
import com.mega.eloan.lms.model.L120S04E;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;

public interface LMS9535Service extends AbstractService {

	// 關係戶於本行各項業務往來檔
	/**
	 * 利用Oid取得關係戶於本行各項業務往來檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S04A findL120s04aByOid(String oid);

	/**
	 * 利用獨特Key取得關係戶於本行各項業務往來檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @return
	 */
	L120S04A findL120s04aByUniqueKey(String mainId, String custId,
			String dupNo, String custName);

	/**
	 * 利用MainId 取得所有關係戶於本行各項業務往來檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S04A> findL120s04aByMainId(String mainId);

	/**
	 * 利用MainId,prtFlag 取得所有關係戶於本行各項業務往來檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S04A> findL120s04aByMainIdPrtFlag(String mainId, String prtFlag);

	/**
	 * 儲存群組關係戶於本行各項業務往來檔
	 * 
	 * @param list
	 */
	void saveL120s04aList(List<L120S04A> list);

	/**
	 * 刪除群組關係戶於本行各項業務往來檔
	 * 
	 * @param list
	 */
	void deleteListL120s04a(List<L120S04A> list);

	// 關係戶與本行往來實績彙總表主檔
	/**
	 * 利用Oid取得關係戶與本行往來實績彙總表主檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S04B findL120s04bByOid(String oid);

	/**
	 * 利用MainId 取得所有關係戶與本行往來實績彙總表主檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S04B> findL120s04bByMainId(String mainId);

	/**
	 * 儲存群組關係戶與本行往來實績彙總表主檔
	 * 
	 * @param list
	 */
	void saveL120s04BList(List<L120S04B> list);

	/**
	 * 刪除群組關係戶與本行往來實績彙總表主檔
	 * 
	 * @param list
	 */
	void deleteListL120s04b(List<L120S04B> list);

	// 關係戶於本行往來實績彙總表明細檔
	/**
	 * 利用Oid取得關係戶於本行往來實績彙總表明細檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S04C findL120s04cByOid(String oid);

	L120S04E findL120s04eByOid(String oid);

	/**
	 * 利用MainId 取得所有關係戶於本行往來實績彙總表明細檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S04C> findL120s04cByMainId(String mainId);

    List<L120S04E> findL120s04eByMainId(String mainId);

	/**
	 * 儲存群組關係戶於本行往來實績彙總表明細檔
	 * 
	 * @param list
	 */
	void saveL120s04cList(List<L120S04C> list);

    void saveL120s04eList(List<L120S04E> list);

	/**
	 * 儲存群組關係戶於本行往來實績彙總表主檔與明細檔
	 * 
	 * @param list
	 * @param model
	 */
	void saveL120s04bc(List<L120S04C> list, L120S04B model);

	/**
	 * 刪除群組關係戶於本行往來實績彙總表明細檔
	 * 
	 * @param list
	 */
	void deleteListL120s04c(List<L120S04C> list);

    void deleteListL120s04e(List<L120S04E> list);

	/**
	 * 刪除群組關係戶於本行往來實績彙總所有相關檔
	 * 
	 * @param list
	 */
	public void deleteListL120s04(List<L120S04B> listS04b,
			List<L120S04C> listS04c, List<L120S04E> listS04e);

	/**
	 * 產生往來實績彙總檔
	 * 
	 * @param parent
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param queryDateS
	 * @param queryDateE
	 * @throws CapMessageException
	 * @throws CapException
	 */
	public void importL120s04b(String mainId, String custId,
			String dupNo, String queryDateS, String queryDateE)
			throws CapMessageException, CapException;

	/**
	 * 查詢往來彙總DW有效資料範圍日期
	 * 
	 * @param queryDateS
	 * @param queryDateE
	 * @param json
	 * @return
	 */
	int checkDate(String queryDateS, String queryDateE, JSONObject json);

	/**
	 * 計算貢獻度(集團)、放款額度(集團)、放款餘額(集團)、活期存款(集團)、 貢獻度(關係)、放款額度(關係)、放款餘額(關係)、活期存款(關係)
	 * 之後儲存
	 * 
	 * @param mainId
	 *            文件編號
	 * @param map
	 *            Map
	 * @param json
	 *            前端欄位值
	 */
	void setTotal(String mainId, Map<String, Long> map, JSONObject json);

	/**
	 * 引進往來彙總
	 * 
	 * @param mainId
	 *            文件編號
	 * @param custId
	 *            主要借款人統編
	 * @param dupNo
	 *            主要借款人重覆序號
	 * @param custName
	 *            主要借款人名稱
	 * @param queryDateS
	 *            查詢日期起日
	 * @param queryDateE
	 *            查詢日期迄日
	 * @return List<L120S04A>
	 */
	public List<L120S04A> findL120s04a(String mainId, String custId,
			String dupNo, String custName, String queryDateS, String queryDateE);

	/**
	 * 利用借款人統編、重覆序號及文件編號取得所有信用評等資料檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<L120S01C> findL120s01cByCustId(String mainId, String custId,
			String dupNo);

	/**
	 * 刪除所有往來彙總相關資料
	 * 
	 * @param list1
	 * @param list2
	 * @param list3
	 * @param docFiles
	 */
	void deleteAllWanLai(List<L120S04A> list1, List<L120S04B> list2,
			List<L120S04C> list3, List<DocFile> docFiles);
	
	/**
	 * J-113-0183  新增RORWA計算
	 * @param parent
	 * @param kind
	 * @param mainId
	 * @param queryDateE
	 * @param jsonData
	 * @param branchRate
	 * @param qCustId
	 * @param qDupNo
	 * @param isKindA
	 * @param factAmtIncrease
	 * @throws CapException
	 */
	public void impBisEstimatedReturn(String kind,
			String mainId, String queryDateE, JSONObject jsonData,
			BranchRate branchRate, String qCustId, String qDupNo,
			Boolean isKindA, BigDecimal factAmtIncrease) throws CapException;

	void importL120s04b_Risk_weighted_Assets(String mainId,
			String custId, String dupNo, String queryDateS, String queryDateE,
			JSONObject jsonData, BranchRate branchRate) throws CapException;
}
