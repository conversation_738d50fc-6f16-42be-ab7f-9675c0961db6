---------------------------------------------------------
-- LMS.L800M01A 常用主管資料檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L800M01A;
CREATE TABLE LMS.L800M01A (
	OID           CHAR(32)      not null,
	BRNO          CHAR(03)      not null,
	ZHUGU<PERSON>       CHAR(6)       not null,
	<PERSON><PERSON><PERSON><PERSON><PERSON>      CHAR(1)      ,
	<PERSON><PERSON><PERSON><PERSON>        VARCHAR(48)  ,
	ISTYPE1       CHAR(1)      ,
	ISTYPE2       CHAR(1)      ,
	ISTYPE3       CHAR(1)      ,
	ISTYPE4       CHAR(1)      ,
	ISTYPE5       CHAR(1)      ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L800M01A PRIMARY KEY(OID)
) IN EL_DATA_4KTS
  INDEX IN EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL800M01A01;
CREATE UNIQUE INDEX LMS.XL800M01A01 ON LMS.L800M01A   (BRNO, ZHUGUAN);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L800M01A IS '常用主管資料檔';
COMMENT ON LMS.L800M01A (
	OID           IS 'oid', 
	BRNO          IS '單位代號', 
	ZHUGUAN       IS '主管員工號碼', 
	DATATYPE      IS '資料類型', 
	ZGNAME        IS '主管姓名', 
	ISTYPE1       IS '是否為帳戶管理員', 
	ISTYPE2       IS '是否為授信主管', 
	ISTYPE3       IS '是否為授權主管', 
	ISTYPE4       IS '是否為單位副主管', 
	ISTYPE5       IS '是否為單位主管', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
