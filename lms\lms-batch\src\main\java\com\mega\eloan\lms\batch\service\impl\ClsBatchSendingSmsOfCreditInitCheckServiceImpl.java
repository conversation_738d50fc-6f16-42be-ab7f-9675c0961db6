package com.mega.eloan.lms.batch.service.impl;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.model.SmsContent;
import com.mega.eloan.common.model.SmsProfile;
import com.mega.eloan.common.service.SmsService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C122M01A;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 *  J-111-0602 歡喜信貸徵審流程初審批次
 * </pre>
 * 
 * @since 2023/01/18
 * <AUTHOR>
 * @version <ul>
 *          <li>2023/01/18,new
 *          </ul>
 */
@Service("clsBatchSendingSmsOfCreditInitCheckServiceImpl")
public class ClsBatchSendingSmsOfCreditInitCheckServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory.getLogger(ClsBatchSendingSmsOfCreditInitCheckServiceImpl.class);
	
	private static final String HAPPY_CREDIT_LOAN_BUSINESS_TYPE = "HPCL";
	
	private static final String Virtual_Employee_id = "00ZCB2";
	
	protected static final int timeout = 60;
	
	@Resource
	C122M01ADao c122m01aDao;

	@Resource
	CLSService clsService;
	
	@Resource
	private SmsService smsService;
	
	@Resource
	CLS1220Service cls1220Service;

	@Resource
	MisdbBASEService misdbBASEService;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		
		JSONObject result = null;
		String mainId = null;
		String custId = null;
		String dupNo = null;
		String branchNo = null;
		try {
			
			// 判斷今天是不是營業日
			Map<String, Object> lnf320 = misdbBASEService.get_LNF320(CapDate.getCurrentDate("yyyy-MM-dd"));
			if (MapUtils.isEmpty(lnf320)) {
				result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
				result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl  執行成功！今日非營業日");
				return result;
			}
			
			List<C122M01A> c122m01aList = c122m01aDao.findSmsSendingCaseForOnlineInitialCheckCase();
	
			for(C122M01A c122m01a : c122m01aList){
				
				mainId = c122m01a.getMainId();
				custId = c122m01a.getCustId();
				dupNo = c122m01a.getDupNo();
				branchNo = c122m01a.getOwnBrId();
				
				Timestamp chkFinishTime = c122m01a.getInitChkFinishTime();
				Calendar cal = Calendar.getInstance();
				cal.setTime(chkFinishTime);
				cal.add(Calendar.HOUR_OF_DAY, 1);
				
				if(cal.getTime().compareTo(new Date()) <= 0){
					this.sendSMSs(c122m01a);// 發送婉拒簡訊，並將傳送結果寫入該人之簡訊Log；因傳送簡訊後一般不會馬上有確定結果，此時所收到之結果並非最終結果
					c122m01a.setSmsSendingStatus("U");// 故押簡訊傳送狀態為"U"(API在途)
					c122m01a.setUpdater(ClsBatchSendingSmsOfCreditInitCheckServiceImpl.Virtual_Employee_id);
					c122m01a.setUpdateTime(CapDate.getCurrentTimestamp());
					clsService.daoSave(c122m01a);
					LOGGER.debug("歡喜信貸初審簡訊發送成功 ===> mainId: {}, custId: {}, dupNo: {}, branchNo: {}", new String[]{mainId, custId, dupNo, branchNo});
				}
			}
			
			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE, "clsBatchSuspectedAgentAppCaseServiceImpl 執行成功！");

		} catch (Exception ex) {
			LOGGER.error(StrUtils.getStackTrace(ex));
			result = WebBatchCode.RC_ERROR;
			String msg = ex.getMessage() + "===> mainId:" + mainId + ", custId:" + custId + ", dupNo:" + dupNo + ", branchNo:" + branchNo;
			result.element(
					WebBatchCode.P_RESPONSE, "clsBatchSuspectedAgentAppCaseServiceImpl 執行失敗！==>" + msg + " - " + ex.getLocalizedMessage());
		}

		return result;
	}
	
	private void sendSMSs(C122M01A meta) throws CapMessageException {
		
		C120S01A s01a = this.cls1220Service.findC120S01A(meta.getMainId(), meta.getCustId(), meta.getDupNo());
		String cellPhone = Util.trim(s01a==null?"":s01a.getMTel());
		if (StringUtils.isBlank(cellPhone)) {
			return;
		}
		
		LOGGER.debug("Step1.新增手機設定檔");
		//是否有額外的介面處理此Step
		SmsProfile smsProfile = new SmsProfile();
		smsProfile.setTypCd(TypCdEnum.DBU);
		smsProfile.setOwnBrId(meta.getOwnBrId());
		smsProfile.setCustId(meta.getCustId());
		smsProfile.setDupNo(meta.getDupNo());
		smsProfile.setCustName(meta.getCustName());
		smsProfile.setContactId(meta.getCustId());
		smsProfile.setContactName(meta.getCustName());
		smsProfile.setTel(cellPhone);
		
		LOGGER.debug("Step2.覆核手機設定檔");
		//是否有額外的介面處理此Step
		smsProfile.setApproveTime(CapDate.getCurrentTimestamp());
		smsService.createPhoneProfile(smsProfile);
		
		LOGGER.debug("Step3.單筆新增簡訊");
		//若上述2Step均在其他介面處理，則第一件事則是依據條件取出前面已覆核的手機設定檔
		//SmsProfile smsProfile = smsService.queryApprovedSmsProfiles(meta.getOwnBrId(), meta.getCustId(), meta.getDupNo()).get(0);
		SmsContent smsObj = new SmsContent(meta.getMainId(), UtilConstants.CaseSchema.個金, ClsBatchSendingSmsOfCreditInitCheckServiceImpl.HAPPY_CREDIT_LOAN_BUSINESS_TYPE, smsProfile);
		//處理簡訊內文
		smsObj.setMsgContent("親愛的客戶您好，誠摯感謝您申辦兆豐銀行信用貸款，謹通知本次申請未達系統評分標準。希望將來能有機會為您服務，再次感謝申請。");
		smsService.createSingleMessage(smsObj);

		LOGGER.debug("Step4.傳送簡訊");
		smsService.sendMessages(meta.getMainId());
		
		LOGGER.debug("Step5.查詢簡訊");
		//是否有另外的查詢介面
		List<SmsContent> smsObjList = smsService.queryMsgsByMainId(meta.getMainId());
		LOGGER.debug("===================");
		
		for (SmsContent smsContent : smsObjList) {
			LOGGER.debug("{}", smsContent);
		}
	}
	
	public static void main(String[] args) {
		
		Date time = new Timestamp(System.currentTimeMillis());
		
		Calendar cal = Calendar.getInstance();
		cal.setTime(time);
		cal.add(Calendar.HOUR_OF_DAY, 1);
		
		System.out.println(time.compareTo(cal.getTime()));
	}
	
}
