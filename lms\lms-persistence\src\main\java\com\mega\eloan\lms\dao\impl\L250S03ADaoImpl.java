package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L250S03ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L250S03A;

@Repository
public class L250S03ADaoImpl extends LMSJpaDao<L250S03A, String> implements
		L250S03ADao {

	@Override
	public List<L250S03A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("groupOrder");
		List<L250S03A> list = createQuery(search).getResultList();
		return list;
	}
}