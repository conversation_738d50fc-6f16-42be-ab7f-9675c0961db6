require.config({
  paths : {
    'libjs' : 'js/libjs',
    'common.i18n' : 'js/common/common.i18n',
    'thickbox' : 'js/lib/thickbox/thickbox',
    'common.properties' : 'js/common/common.properties',
    'common.item' : 'js/common/common.item',
    'common' : 'js/common/common',
    'common.message' : 'js/common/common.message',
    'common.jqgrid' : 'js/common/common.jqgrid',
    'common.tabcrollable' : 'js/common/common.tabcrollable',
    'common.validate' : 'js/common/common.validate',
    'common.ckeditor.init' : 'js/common/common.ckeditor.init',
    'common.yuiswf.init' : 'js/common/common.yuiswf.init',
	//補齊lms部分js
	'mega.eloan.sample' : 'js/common/mega.eloan.sample',
	'yahoo-dom-event' : 'js/yui/yahoo-dom-event/yahoo-dom-event',
	'element' : 'js/yui/element/element',
	'event-delegate' : 'js/yui/event-delegate/event-delegate',
	'cookie' : 'js/yui/cookie/cookie',
	'swfstore' : 'js/yui/swfstore/swfstore'
  },
  shim : {
    'common.i18n' : ['libjs'],
    'thickbox' : [ 'common.i18n' ],
    'common.properties': [ 'common.i18n' ],
    'common.item': [ 'common.properties' ],
    'common': [ 'common.message', 'common.validate', 'common.ckeditor.init' ],
    'common.message': [ 'common.properties' ],
    'common.jqgrid': [ 'common' ],
    'common.tabcrollable': [ 'common.i18n' ],
    'common.validate': [ 'common.i18n' ],
    'common.ckeditor.init' : [ 'libjs' ],
	'common.yuiswf.init' : [ 'common' ],
    'mega.eloan.sample' : [ 'common' ],
	'yahoo-dom-event' : [ 'common' ],
	'element' : [ 'common' ],
	'event-delegate' : [ 'common' ],
	'cookie' : [ 'common' ],
	'swfstore' : [ 'common' ]
  }
});

define('basejs', [ 'libjs', 'common.i18n', 'thickbox', 'common.properties', 'common.item', 'common', 'common.message', 'common.jqgrid', 'common.tabcrollable', 'common.validate', 'common.ckeditor.init', 'common.yuiswf.init',	'mega.eloan.sample', 'yahoo-dom-event', 'element', 'event-delegate', 'cookie', 'swfstore' ], function(commonjs) {
  console.log("basejs init");
});
