<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
		<div id="C160M01BDiv" class="content">
			<fieldset>
				<legend><b><th:block th:text="#{'title.legend'}">額度明細</th:block></b></legend>
				<form id="totalForm" name="totalForm" >
					<table class="tb2" width="100%">
						<tr>
							<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M01B.count'}">筆數</th:block>&nbsp;&nbsp;</td>
							<td width="35%"><span id="loanCount" class="field" ></span>&nbsp;</td>
							<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M01B.total'}">核准總金額</th:block>&nbsp;&nbsp;</td>
							<td width="35%"><span id="loanTotal" class="field" ></span>&nbsp;</td>
						</tr>
					</table>
				</form>
				<button type="button" id="pullinAgain" >
					<span class="text-only"><th:block th:text="#{'button.pullinAgain'}">重新引進</th:block></span>
				</button>
				<button type="button" id="delete" >
					<span class="text-only"><th:block th:text="#{'button.delete'}">刪除</th:block></span>
				</button>
				<div id="C160M01BGird" ></div>
			</fieldset>
		</div>
		
		<div id="C160M01BDetail" style="display:none;" ></div>
		
		<fieldset>
					<legend>
                        <b><th:block th:text="#{'cls120s05.title4'}">各項費用</th:block></b>
                    </legend>
                          <tr id="feesTr">
								
								<td width="100%">
									<button type="button" id="addL140M01RBt">
                                         <span class="text-only"><th:block th:text="#{'button.add'}">新增</th:block></span>
                                    </button>
                                    <button type="button" id="delL140M01RBt">
                                         <span class="text-only"><th:block th:text="#{'button.delete'}">刪除</th:block></span>
                                    </button>
                                    <div>
                                    	<span class="color-red">若收取費用，請執行L039各項費用收取交易</span>
                                    </div>
									<div id="L140M01RGrid">             				        
									</div>								
									
								<div id="fee" style="display:none;">
				                <div id="charge">
					                <table class="tb2" width="100%">
					                 <tr>
									 	<td class="hd1"><th:block th:text="#{'L140M01R.016'}">案號</th:block></td>									 
										<td>	
										    <button type="button" id="includeL120M01A" class="" style="">										    	
                                                <span class="text-only"><th:block th:text="#{'button.pullin03'}">引進案號</th:block></span>
                                            </button>
										    <br>				             	
							           <input type="text" name="caseNo" id="caseNo"  size="62" maxlength="62"  class="required clearFee" readonly="readonly" />
									   <input type="text" name="caseYear" id="caseYear" class="clearFee" readonly="readonly"  style="display:none"   />
									   <input type="text" name="caseBrId" id="caseBrId" class="clearFee" readonly="readonly"  style="display:none" />
									   <input type="text" name="caseSeq" id="caseSeq"  class="clearFee" readonly="readonly"   style="display:none" />
									   <input type="text" name="feeSeq" id="feeSeq"  class="clearFee" style="display:none"/>
									   </td>
									 </tr>
									 	
						             <tr>						             	
							           <td class="hd1"><th:block th:text="#{'L140M01R.002'}">費用代碼</th:block></td>
							           <td><select id="feeNo" name="feeNo" combokey="cls1141_feeNo" comboType="4" ></select></td>
									 </tr>								 
									 <!-- 
									 <tr>	
									 <input type="text" name="caseSeq" id="caseSeq"  class="clearFee" readonly="readonly"  style ="display:none;"/>								 	
									 	<td class="hd1">
                                             <th:block th:text="#{'L140M01R.003'}">收取範圍</th:block>
										</td>
										<td>	                                         
									 	<label>
                                             <input type="radio" name="feeSphere" id="feeSphere" value="01" class="required" checked="checked" />
                                             <th:block th:text="#{'L140M01R.009'}">客戶編號</th:block>
                                        </label>
                                        <label>
                                             <input type="radio" name="feeSphere" value="02" class="required"/>
                                             <th:block th:text="#{'L140M01R.010'}">額度序號</th:block>
                                        </label>
										<label>
                                             <input type="radio" name="feeSphere" value="03" class="required"/>
                                             <th:block th:text="#{'L140M01R.011'}">帳號</th:block>
                                        </label>
										
										<br>
										<button type="button" id="includeC120M01A" class="group01" style="">
                                                <span class="text-only"><th:block th:text="#{'button.pullin01'}">引進客戶編號</th:block></span>
                                        </button>
										<button type="button" id="includeL140M01A" class="group02" style="">
                                                <span class="text-only"><th:block th:text="#{'button.pullin02'}">引進額度序號</th:block></span>
                                        </button>
										<br>											
										<input type="text" name="custId" id="custId" class="group01 clearFee" readonly="readonly" />
										<input type="text" name="dupNo" id="dupNo" class="group01 clearFee" readonly="readonly" /> 
										<input type="text" name="cntrno" id="cntrno" class="group02 clearFee" readonly="readonly"/>
										<input type="text" name="loanNo" id="loanNo" class="group03 clearFee" maxlength="14" size="14" style=""/>
										</td>
						             </tr>
									 -->
									 
									 <tr>										
							           <td class="hd1"><th:block th:text="#{'L140M01R.004'}">費用金額</th:block></td>
							           <td>									   
									        <select id="feeSwft" name="feeSwft" combokey="Common_Currcy" space="true" ></select>
                                            <input type="text" id="feeAmt" name="feeAmt" size="18" maxlength="19" integer="13" class="numeric required" />
                                            <th:block th:text="#{'other.money'}"><!-- 元--></th:block>
							           </td>
						             </tr>
									 <tr>										
							           <td class="hd1"><th:block th:text="#{'L140M01R.005'}">備註</th:block></td>
									   <td><textarea type="text" id="feeMemo" name="feeMemo" maxlength="100" maxlengthC="50" size="100" cols="70"></textarea></td>
							         </tr>
						            </table>
				                </div>
				                </div>	
								
								</td>	
								</tr>			
				 
				</fieldset>
				
				<div id="C120M01AThickBox" style="display:none;" >
			    <div id="gridC120M01A" ></div>
			    </div>
			
			    <div id="L140M01AThickBox" style="display:none;" >
			    <div id="gridL140M01A" ></div>
			    </div>	
				
				<div id="L120M01AThickBox" style="display:none;" >
			    <div id="gridL120M01A" ></div>
			    </div>		
		
		<script type="text/javascript">loadScript('pagejs/cls/CLS1161S02Panel');</script>
	</th:block>
</body>
</html>
