/* 
 *ObsdbELF384Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 科(子)目及其限額檔  ELF384
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
public interface ObsdbELF384Service {
	/**
	 * 批次新增
	 * 
	 * @param BRNID
	 *            上傳銀行代號
	 * @param dataList
	 *            sql 陣列
	 */
	void insert(String BRNID, List<Object[]> dataList);

	/**
	 * 單筆新增
	 * 
	 * @param BRNID
	 *            上傳銀行代號
	 * @param dataList
	 *            欄位 陣列
	 */
	void insert(String BRNID, Object[] dataList);

	/**
	 * 更新
	 * 
	 * @param BRNID
	 *            上傳銀行代號
	 * @param dataList
	 *            欄位 陣列
	 */
	void update(String BRNID, Object[] dataList);

	/**
	 * 查詢
	 * 
	 * @param BRNID 上傳銀行代號
//	 * @param custArgs
//	 *            科目代碼
	 * @param args
	 *            資料列
	 * @return
	 */
	List<Map<String, Object>> selByUniqueKey(String BRNID, Object[] args);

	/**
	 * 刪除 該筆額度明細表系統日期 資料
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            系統時間
	 * @return <pre>
	 * ONLNTIME: 
	 * BTHTIME :
	 * </pre>
	 * 
	 */
	public int delBySdate(String BRNID, String custId, String dupNo,
			String cntrNo, String sDate);

	/**
	 * 將系統當天同時間的 CHGFLAG更新為取消
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            系統時間
	 * 
	 */
	public int updateChgflagBySdate(String BRNID, String custId, String dupNo,
			String cntrNo, String sDate);

	/**
	 * 將舊資料複製到新資料 且將變更註記變為取消, a-Loan Batch Time 改成Null
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            系統時間
	 * @param OLDSDATE
	 *            舊案時間
	 * @param updater
	 *            更新者
	 * 
	 */
	public int insetSelectOldData(String BRNID, String custId, String dupNo,
			String cntrNo, String sDate, String OLDSDATE, String updater);
}
