package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 對大陸地區授信業務控管註記 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140M01Q", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class L140M01Q extends GenericBean implements IDataObject, IDocObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	@Column(name = "CNTRNOQ", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNoQ;

	/**
	 * 本額度是否屬「本行對大陸地區授信業務管理要點」定義之放款(修改前)
	 **/
	@Column(name = "BCNLOANFG", length = 1, columnDefinition = "CHAR(1)")
	private String bcnLoanFg;

	/** 本額度是否屬「本行對大陸地區授信業務管理要點」定義之放款 **/
	@Column(name = "CNLOANFG", length = 1, columnDefinition = "CHAR(1)")
	private String cnLoanFg;

	/** 是否為內保外貸 **/
	@Column(name = "BIGOLFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String biGolFlag;

	/** 是否為內保外貸 **/
	@Column(name = "IGOLFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String iGolFlag;

	/**
	 * 授信對象別(修改前)<br>
	 * 11.直接授信-大陸地區台商(經主管機關許可投資者) <br>
	 * 12.直接授信-大陸地區法人(陸資企業)、機關、團體 <br>
	 * 13.直接授信-大陸地區外商(第三地區法人在大陸地區之分支機構) <br>
	 * 21.間接授信-大陸地區法人、機關、團體在第三地區之分支機構者(不含在台灣之分支機構) <br>
	 * 22.間接授信-外國公司(不含註冊地在台灣或在大陸地區者) <br>
	 * 23.間接授信-境外公司(OBU) <br>
	 * 24.間接授信-國內公司(DBU) <br>
	 **/
	@Column(name = "BDIRECTFG", length = 2, columnDefinition = "CHAR(2)")
	private String bdirectFg;

	/**
	 * 授信對象別 <br>
	 * 11.直接授信-大陸地區台商(經主管機關許可投資者) <br>
	 * 12.直接授信-大陸地區法人(陸資企業)、機關、團體 <br>
	 * 13.直接授信-大陸地區外商(第三地區法人在大陸地區之分支機構) <br>
	 * 21.間接授信-大陸地區法人、機關、團體在第三地區之分支機構者(不含在台灣之分支機構) <br>
	 * 22.間接授信-外國公司(不含註冊地在台灣或在大陸地區者) <br>
	 * 23.間接授信-境外公司(OBU) <br>
	 * 24.間接授信-國內公司(DBU) <br>
	 **/
	@Column(name = "DIRECTFG", length = 2, columnDefinition = "CHAR(2)")
	private String directFg;

	/** 兩岸間短期貿易融資額度註記(修改前) **/
	@Column(name = "BSTRADEFG", length = 1, columnDefinition = "CHAR(1)")
	private String bsTradeFg;

	/** 兩岸間短期貿易融資額度註記 **/
	@Column(name = "STRADEFG", length = 1, columnDefinition = "CHAR(1)")
	private String sTradeFg;

	/** 保證1(修改前) **/
	@Column(name = "BGUAR1RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal bguar1Rate;
	/** 保證1 **/
	@Column(name = "GUAR1RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal guar1Rate;

	/** 保證2(修改前) **/
	@Column(name = "BGUAR2RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal bguar2Rate;

	/** 保證2 **/
	@Column(name = "GUAR2RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal guar2Rate;

	/** 保證3(修改前) **/
	@Column(name = "BGUAR3RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal bguar3Rate;

	/** 保證3 **/
	@Column(name = "GUAR3RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal guar3Rate;

	/** 擔保品1(修改前) **/
	@Column(name = "BCOLL1RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal bcoll1Rate;

	/** 擔保品1 **/
	@Column(name = "COLL1RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal coll1Rate;

	/** 擔保品2(修改前) **/
	@Column(name = "BCOLL2RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal bcoll2Rate;

	/** 擔保品2 **/
	@Column(name = "COLL2RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal coll2Rate;

	/** 擔保品3(修改前) **/
	@Column(name = "BCOLL3RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal bcoll3Rate;

	/** 擔保品3 **/
	@Column(name = "COLL3RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal coll3Rate;

	/** 擔保品4(修改前) **/
	@Column(name = "BCOLL4RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal bcoll4Rate;

	/** 擔保品4 **/
	@Column(name = "COLL4RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal coll4Rate;

	/** 擔保品5(修改前) **/
	@Column(name = "BCOLL5RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal bcoll5Rate;

	/** 擔保品 5 **/
	@Column(name = "COLL5RATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal coll5Rate;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 衍生性金融商品立約人身份別(修改前) **/
	@Column(name = "BCNTMUFG", length = 1, columnDefinition = "CHAR(1)")
	private String bcnTMUFg;

	/** 衍生性金融商品立約人身份別 **/
	@Column(name = "CNTMUFG", length = 1, columnDefinition = "CHAR(1)")
	private String cnTMUFg;

	/** 企業類別(修改前) **/
	@Column(name = "BCNBUSKIND", length = 1, columnDefinition = "CHAR(1)")
	private String bcnBusKind;

	/** 企業類別身份別 **/
	@Column(name = "CNBUSKIND", length = 1, columnDefinition = "CHAR(1)")
	private String cnBusKind;

	/** 新授信對象別(修改前) **/
	@Column(name = "BLOANTARGET", length = 5, columnDefinition = "CHAR(5)")
	private String bloanTarget;

	/** 新授信對象別 **/
	@Column(name = "LOANTARGET", length = 5, columnDefinition = "CHAR(5)")
	private String loanTarget;

	/** 開狀行為(修改前) **/
	@Column(name = "BISTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String bisType;

	/** 開狀行為 **/
	@Column(name = "ISTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String isType;

	/** 保證人種類(修改前) **/
	@Column(name = "BGRNTTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String bgrntType;

	/** 保證人種類 **/
	@Column(name = "GRNTTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String grntType;

	/** 擔保分類(修改前) **/
	@Column(name = "BGRNTCLASS", length = 1, columnDefinition = "CHAR(1)")
	private String bgrntClass;

	/** 擔保分類 **/
	@Column(name = "GRNTCLASS", length = 1, columnDefinition = "CHAR(1)")
	private String grntClass;

	/** 其他信用增強種類(修改前) **/
	@Column(name = "BOTHCRDTYPE", length = 40, columnDefinition = "VARCHAR(40)")
	private String bothCrdType;

	/** 其他信用增強種類 **/
	@Column(name = "OTHCRDTYPE", length = 40, columnDefinition = "VARCHAR(40)")
	private String othCrdType;

	/** 國際聯貸註記(修改前) **/
	@Column(name = "BUNIONAREA3", length = 1, columnDefinition = "CHAR(1)")
	private String bunionArea3;

	/** 國際聯貸註記 **/
	@Column(name = "UNIONAREA3", length = 1, columnDefinition = "CHAR(1)")
	private String unionArea3;

	/** 借款人性質別(舊授信對象別轉新授信對象別用 charcd由0024 MIS.CUSTDATA CHARCD來) **/
	@Column(name = "CHARCD", length = 2, columnDefinition = "CHAR(2)")
	private String charCd;

	/** 是否由非大陸地區本行聯行開具擔保信用狀十足保證(修改前) **/
	@Column(name = "BNCNSBLCFG", length = 1, columnDefinition = "CHAR(1)")
	private String bnCnSblcFg;

	/** 是否由非大陸地區本行聯行開具擔保信用狀十足保證 **/
	@Column(name = "NCNSBLCFG", length = 1, columnDefinition = "CHAR(1)")
	private String nCnSblcFg;
	
	/** J-112-0462 本案資金流向是否為中國?(但屬買賣關係者得予排除)(前) **/
	@Column(name = "BFUNDSTOCN", length = 1, columnDefinition = "CHAR(1)")
	private String bfundsToCn;
	
	/** J-112-0462 本案資金流向是否為中國?(但屬買賣關係者得予排除)**/
	@Column(name = "FUNDSTOCN", length = 1, columnDefinition = "CHAR(1)")
	private String fundsToCn;
	
	/** J-112-0462 借款人之股權結構往上追溯至中國籍股東止，其合計中國籍股東持股達50%者，請選是(前) **/
	@Column(name = "BCNSHAREHOLDER", length = 1, columnDefinition = "CHAR(1)")
	private String bcnShareholder;
	
	/** J-112-0462 借款人之股權結構往上追溯至中國籍股東止，其合計中國籍股東持股達50%者，請選是 **/
	@Column(name = "CNSHAREHOLDER", length = 1, columnDefinition = "CHAR(1)")
	private String cnShareholder;
	
	/** J-112-0462 本案任一保證人國籍/註冊地是否為中國?(前) **/
	@Column(name = "BCNGUARANTOR", length = 1, columnDefinition = "CHAR(1)")
	private String bcnGuarantor;
	
	/** J-112-0462 本案任一保證人國籍/註冊地是否為中國? **/
	@Column(name = "CNGUARANTOR", length = 1, columnDefinition = "CHAR(1)")
	private String cnGuarantor;
	
	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	/**
	 * 本額度是否屬「本行對大陸地區授信業務管理要點」定義之放款(修改前)
	 * 
	 * @return
	 */
	public String getBcnLoanFg() {
		return bcnLoanFg;
	}

	/**
	 * 本額度是否屬「本行對大陸地區授信業務管理要點」定義之放款(修改前)
	 * 
	 * @param bcnLoanFg
	 */
	public void setBcnLoanFg(String bcnLoanFg) {
		this.bcnLoanFg = bcnLoanFg;
	}

	/**
	 * 本額度是否屬「本行對大陸地區授信業務管理要點」定義之放款
	 * 
	 * @return
	 */
	public String getCnLoanFg() {
		return cnLoanFg;
	}

	/**
	 * 本額度是否屬「本行對大陸地區授信業務管理要點」定義之放款
	 * 
	 * @param cnLoanFg
	 */
	public void setCnLoanFg(String cnLoanFg) {
		this.cnLoanFg = cnLoanFg;
	}

	/**
	 * 是否為內保外貸(修改前)
	 * 
	 * @return
	 */
	public String getBiGolFlag() {
		return biGolFlag;
	}

	/**
	 * 是否為內保外貸(修改前)
	 * 
	 * @param biGolFlag
	 */
	public void setBiGolFlag(String biGolFlag) {
		this.biGolFlag = biGolFlag;
	}

	/**
	 * 是否為內保外貸
	 * 
	 * @return
	 */
	public String getiGolFlag() {
		return iGolFlag;
	}

	/**
	 * 是否為內保外貸
	 * 
	 * @param iGolFlag
	 */
	public void setiGolFlag(String iGolFlag) {
		this.iGolFlag = iGolFlag;
	}

	/**
	 * 授信對象別(修改前)
	 * 
	 * @return
	 */
	public String getBdirectFg() {
		return bdirectFg;
	}

	/**
	 * 授信對象別(修改前)
	 * 
	 * @param bdirectFg
	 */
	public void setBdirectFg(String bdirectFg) {
		this.bdirectFg = bdirectFg;
	}

	/**
	 * 授信對象別
	 * 
	 * @return
	 */
	public String getDirectFg() {
		return directFg;
	}

	/**
	 * 授信對象別
	 * 
	 * @param directFg
	 */
	public void setDirectFg(String directFg) {
		this.directFg = directFg;
	}

	/**
	 * 兩岸間短期貿易融資額度註記(修改前)
	 * 
	 * @return
	 */
	public String getBsTradeFg() {
		return bsTradeFg;
	}

	/**
	 * 兩岸間短期貿易融資額度註記(修改前)
	 * 
	 * @param bsTradeFg
	 */
	public void setBsTradeFg(String bsTradeFg) {
		this.bsTradeFg = bsTradeFg;
	}

	/**
	 * 兩岸間短期貿易融資額度註記
	 * 
	 * @return
	 */
	public String getsTradeFg() {
		return sTradeFg;
	}

	/**
	 * 兩岸間短期貿易融資額度註記
	 * 
	 * @param sTradeFg
	 */
	public void setsTradeFg(String sTradeFg) {
		this.sTradeFg = sTradeFg;
	}

	public BigDecimal getBguar1Rate() {
		return bguar1Rate;
	}

	public void setBguar1Rate(BigDecimal bguar1Rate) {
		bguar1Rate = bguar1Rate == null ? BigDecimal.ZERO : bguar1Rate;
		this.bguar1Rate = bguar1Rate;
	}

	public BigDecimal getGuar1Rate() {
		return guar1Rate;
	}

	public void setGuar1Rate(BigDecimal guar1Rate) {
		guar1Rate = guar1Rate == null ? BigDecimal.ZERO : guar1Rate;
		this.guar1Rate = guar1Rate;
	}

	public BigDecimal getBguar2Rate() {
		return bguar2Rate;
	}

	public void setBguar2Rate(BigDecimal bguar2Rate) {
		bguar2Rate = bguar2Rate == null ? BigDecimal.ZERO : bguar2Rate;
		this.bguar2Rate = bguar2Rate;
	}

	public BigDecimal getGuar2Rate() {
		return guar2Rate;
	}

	public void setGuar2Rate(BigDecimal guar2Rate) {
		guar2Rate = guar2Rate == null ? BigDecimal.ZERO : guar2Rate;
		this.guar2Rate = guar2Rate;
	}

	public BigDecimal getBguar3Rate() {
		return bguar3Rate;
	}

	public void setBguar3Rate(BigDecimal bguar3Rate) {
		bguar3Rate = bguar3Rate == null ? BigDecimal.ZERO : bguar3Rate;
		this.bguar3Rate = bguar3Rate;
	}

	public BigDecimal getGuar3Rate() {
		return guar3Rate;
	}

	public void setGuar3Rate(BigDecimal guar3Rate) {
		guar3Rate = guar3Rate == null ? BigDecimal.ZERO : guar3Rate;
		this.guar3Rate = guar3Rate;
	}

	public BigDecimal getBcoll1Rate() {
		return bcoll1Rate;
	}

	public void setBcoll1Rate(BigDecimal bcoll1Rate) {
		bcoll1Rate = bcoll1Rate == null ? BigDecimal.ZERO : bcoll1Rate;
		this.bcoll1Rate = bcoll1Rate;
	}

	public BigDecimal getColl1Rate() {
		return coll1Rate;
	}

	public void setColl1Rate(BigDecimal coll1Rate) {
		coll1Rate = coll1Rate == null ? BigDecimal.ZERO : coll1Rate;
		this.coll1Rate = coll1Rate;
	}

	public BigDecimal getBcoll2Rate() {
		return bcoll2Rate;
	}

	public void setBcoll2Rate(BigDecimal bcoll2Rate) {
		bcoll2Rate = bcoll2Rate == null ? BigDecimal.ZERO : bcoll2Rate;
		this.bcoll2Rate = bcoll2Rate;
	}

	public BigDecimal getColl2Rate() {
		return coll2Rate;
	}

	public void setColl2Rate(BigDecimal coll2Rate) {
		coll2Rate = coll2Rate == null ? BigDecimal.ZERO : coll2Rate;
		this.coll2Rate = coll2Rate;
	}

	public BigDecimal getBcoll3Rate() {
		return bcoll3Rate;
	}

	public void setBcoll3Rate(BigDecimal bcoll3Rate) {
		bcoll3Rate = bcoll3Rate == null ? BigDecimal.ZERO : bcoll3Rate;
		this.bcoll3Rate = bcoll3Rate;
	}

	public BigDecimal getColl3Rate() {
		return coll3Rate;
	}

	public void setColl3Rate(BigDecimal coll3Rate) {
		coll3Rate = coll3Rate == null ? BigDecimal.ZERO : coll3Rate;
		this.coll3Rate = coll3Rate;
	}

	public BigDecimal getBcoll4Rate() {
		return bcoll4Rate;
	}

	public void setBcoll4Rate(BigDecimal bcoll4Rate) {
		bcoll4Rate = bcoll4Rate == null ? BigDecimal.ZERO : bcoll4Rate;
		this.bcoll4Rate = bcoll4Rate;
	}

	public BigDecimal getColl4Rate() {
		return coll4Rate;
	}

	public void setColl4Rate(BigDecimal coll4Rate) {
		coll4Rate = coll4Rate == null ? BigDecimal.ZERO : coll4Rate;
		this.coll4Rate = coll4Rate;
	}

	public BigDecimal getBcoll5Rate() {
		return bcoll5Rate;
	}

	public void setBcoll5Rate(BigDecimal bcoll5Rate) {
		bcoll5Rate = bcoll5Rate == null ? BigDecimal.ZERO : bcoll5Rate;
		this.bcoll5Rate = bcoll5Rate;
	}

	public BigDecimal getColl5Rate() {
		return coll5Rate;
	}

	public void setColl5Rate(BigDecimal coll5Rate) {
		coll5Rate = coll5Rate == null ? BigDecimal.ZERO : coll5Rate;
		this.coll5Rate = coll5Rate;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public String getUpdater() {
		return updater;
	}

	public void setUpdater(String updater) {
		this.updater = updater;
	}

	public Timestamp getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Timestamp updateTime) {
		this.updateTime = updateTime;
	}

	public void setCntrNoQ(String cntrNoQ) {
		this.cntrNoQ = cntrNoQ;
	}

	public String getCntrNoQ() {
		return cntrNoQ;
	}

	public void setBcnTMUFg(String bcnTMUFg) {
		this.bcnTMUFg = bcnTMUFg;
	}

	public String getBcnTMUFg() {
		return bcnTMUFg;
	}

	public void setCnTMUFg(String cnTMUFg) {
		this.cnTMUFg = cnTMUFg;
	}

	public String getCnTMUFg() {
		return cnTMUFg;
	}

	public void setBcnBusKind(String bcnBusKind) {
		this.bcnBusKind = bcnBusKind;
	}

	public String getBcnBusKind() {
		return bcnBusKind;
	}

	public void setCnBusKind(String cnBusKind) {
		this.cnBusKind = cnBusKind;
	}

	public String getCnBusKind() {
		return cnBusKind;
	}

	/**
	 * 設定新授信對象別
	 * 
	 * @param loanTarget
	 */
	public void setLoanTarget(String loanTarget) {
		this.loanTarget = loanTarget;
	}

	/**
	 * 取得新授信對象別
	 * 
	 * @param loanTarget
	 */
	public String getLoanTarget() {
		return loanTarget;
	}

	/**
	 * 設定借款人類別
	 * 
	 * @param loanTarget
	 */
	public void setCharCd(String charCd) {
		this.charCd = charCd;
	}

	/**
	 * 取得借款人類別
	 * 
	 * @param loanTarget
	 */
	public String getCharCd() {
		return charCd;
	}

	/**
	 * 設定新授信對象別
	 * 
	 * @param loanTarget
	 */
	public void setBloanTarget(String bloanTarget) {
		this.bloanTarget = bloanTarget;
	}

	/**
	 * 取得新授信對象別
	 * 
	 * @param loanTarget
	 */
	public String getBloanTarget() {
		return bloanTarget;
	}

	public void setBisType(String bisType) {
		this.bisType = bisType;
	}

	public String getBisType() {
		return bisType;
	}

	public void setIsType(String isType) {
		this.isType = isType;
	}

	public String getIsType() {
		return isType;
	}

	public void setBgrntType(String bgrntType) {
		this.bgrntType = bgrntType;
	}

	public String getBgrntType() {
		return bgrntType;
	}

	public void setGrntType(String grntType) {
		this.grntType = grntType;
	}

	public String getGrntType() {
		return grntType;
	}

	public void setBgrntClass(String bgrntClass) {
		this.bgrntClass = bgrntClass;
	}

	public String getBgrntClass() {
		return bgrntClass;
	}

	public void setGrntClass(String grntClass) {
		this.grntClass = grntClass;
	}

	public String getGrntClass() {
		return grntClass;
	}

	public void setBothCrdType(String bothCrdType) {
		this.bothCrdType = bothCrdType;
	}

	public String getBothCrdType() {
		return bothCrdType;
	}

	public void setOthCrdType(String othCrdType) {
		this.othCrdType = othCrdType;
	}

	public String getOthCrdType() {
		return othCrdType;
	}

	public void setBunionArea3(String bunionArea3) {
		this.bunionArea3 = bunionArea3;
	}

	public String getBunionArea3() {
		return bunionArea3;
	}

	public void setUnionArea3(String unionArea3) {
		this.unionArea3 = unionArea3;
	}

	public String getUnionArea3() {
		return unionArea3;
	}

	/**
	 * 設定是否由非大陸地區本行聯行開具擔保信用狀十足保證(修改前)
	 * @param bnCnSblcFg
	 */
	public void setBnCnSblcFg(String bnCnSblcFg) {
		this.bnCnSblcFg = bnCnSblcFg;
	}

	/**
	 * 取得是否由非大陸地區本行聯行開具擔保信用狀十足保證(修改前)
	 * @param bnCnSblcFg
	 */
	public String getBnCnSblcFg() {
		return bnCnSblcFg;
	}

	/**
	 * 設定是否由非大陸地區本行聯行開具擔保信用狀十足保證
	 * @param bnCnSblcFg
	 */
	public void setNCnSblcFg(String nCnSblcFg) {
		this.nCnSblcFg = nCnSblcFg;
	}

	/**
	 * 取得是否由非大陸地區本行聯行開具擔保信用狀十足保證
	 * @param bnCnSblcFg
	 */
	public String getNCnSblcFg() {
		return nCnSblcFg;
	}

	/** J-112-0462 取得本案資金流向是否為中國?(但屬買賣關係者得予排除)(前) **/
	public String getBfundsToCn() {
		return bfundsToCn;
	}

	/** J-112-0462 設定本案資金流向是否為中國?(但屬買賣關係者得予排除)(前) **/
	public void setBfundsToCn(String bfundsToCn) {
		this.bfundsToCn = bfundsToCn;
	}

	/** J-112-0462 取得本案資金流向是否為中國?(但屬買賣關係者得予排除 **/
	public String getFundsToCn() {
		return fundsToCn;
	}

	/** J-112-0462 設定本案資金流向是否為中國?(但屬買賣關係者得予排除 **/
	public void setFundsToCn(String fundsToCn) {
		this.fundsToCn = fundsToCn;
	}

	/** J-112-0462 取得借款人之股權結構往上追溯至中國籍股東止，其合計中國籍股東持股達50%者，請選是(前) **/
	public String getBcnShareholder() {
		return bcnShareholder;
	}

	/** J-112-0462 設定借款人之股權結構往上追溯至中國籍股東止，其合計中國籍股東持股達50%者，請選是(前) **/
	public void setBcnShareholder(String bcnShareholder) {
		this.bcnShareholder = bcnShareholder;
	}

	/** J-112-0462 取得借款人之股權結構往上追溯至中國籍股東止，其合計中國籍股東持股達50%者，請選是 **/
	public String getCnShareholder() {
		return cnShareholder;
	}

	/** J-112-0462 設定借款人之股權結構往上追溯至中國籍股東止，其合計中國籍股東持股達50%者，請選是 **/
	public void setCnShareholder(String cnShareholder) {
		this.cnShareholder = cnShareholder;
	}

	/** J-112-0462 取得本案任一保證人國籍/註冊地是否為中國?(前) **/
	public String getBcnGuarantor() {
		return bcnGuarantor;
	}

	/** J-112-0462 設定本案任一保證人國籍/註冊地是否為中國?(前) **/
	public void setBcnGuarantor(String bcnGuarantor) {
		this.bcnGuarantor = bcnGuarantor;
	}

	/** J-112-0462 取得本案任一保證人國籍/註冊地是否為中國? **/
	public String getCnGuarantor() {
		return cnGuarantor;
	}

	/** J-112-0462 設定本案任一保證人國籍/註冊地是否為中國? **/
	public void setCnGuarantor(String cnGuarantor) {
		this.cnGuarantor = cnGuarantor;
	}

}
