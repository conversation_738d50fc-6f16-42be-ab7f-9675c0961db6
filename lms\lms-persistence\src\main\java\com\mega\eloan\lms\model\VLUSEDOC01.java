/* 
 *VLUSEDOC01.java 
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/**
 * <pre>
 * 選擇動用額度序號
 * </pre>
 * 
 * @since 2012/1/18
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/18,REX,new
 *          </ul>
 */
@Entity
@Table(name = "VLUSEDOC01")
public class VLUSEDOC01 extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	/** 額度明細表mainId */
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 統一編號 */
	@Column(name = "CUSTID", length = 10, columnDefinition = "CHAR(10)")
	private String custId;
	/** 重覆序號 */
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;
	/** 客戶名稱 */
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;
	/** 額度明細表額度序號 */
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;
	/** 額度明細表總類 */
	@Column(name = "ITEMTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String itemType;
	/** 攤貸行額度序號 */
	@Column(name = "SHARENO", length = 12, columnDefinition = "CHAR(12)")
	private String shareNo;
	/** 顯示的額度序號 */
	@Column(name = "USECNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String useCntrNo;
	/** 簽報書mainId */
	@Column(name = "CASEMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String caseMainId;
	/** 額度明細表現請額度幣別 */
	@Column(name = "CURRENTAPPLYCURR", length = 3, columnDefinition = "CHAR(3)")
	private String currentApplyCurr;
	/** 性質 */
	@Column(name = "PROPERTY", length = 30, columnDefinition = "VARCHAR(30)")
	private String proPerty;

	/** 共用額度序號 */
	@Column(name = "COMMSNO", length = 12, columnDefinition = "CHAR(12)")
	private String commSno;

	/** 取得性質 */
	public String getProPerty() {
		return proPerty;
	}

	/** 設定性質 */
	public void setProPerty(String proPerty) {
		this.proPerty = proPerty;
	}

	/** 取得共用額度序號 */
	public String getCommSno() {
		return commSno;
	}

	/** 設定共用額度序號 */
	public void setCommSno(String commSno) {
		this.commSno = commSno;
	}

	/** 取得額度明細表mainId */
	public String getMainId() {
		return mainId;
	}

	/** 設定額度明細表mainId */
	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public String getDupNo() {
		return dupNo;
	}

	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getCntrNo() {
		return cntrNo;
	}

	public void setCntrNo(String cntrNo) {
		this.cntrNo = cntrNo;
	}

	public String getItemType() {
		return itemType;
	}

	public void setItemType(String itemType) {
		this.itemType = itemType;
	}

	public String getShareNo() {
		return shareNo;
	}

	public void setShareNo(String shareNo) {
		this.shareNo = shareNo;
	}

	public String getUseCntrNo() {
		return useCntrNo;
	}

	public void setUseCntrNo(String useCntrNo) {
		this.useCntrNo = useCntrNo;
	}

	public String getCaseMainId() {
		return caseMainId;
	}

	public void setCaseMainId(String caseMainId) {
		this.caseMainId = caseMainId;
	}

	public String getCurrentApplyCurr() {
		return currentApplyCurr;
	}

	public void setCurrentApplyCurr(String currentApplyCurr) {
		this.currentApplyCurr = currentApplyCurr;
	}

	public String getCurrentApplyAmt() {
		return currentApplyAmt;
	}

	public void setCurrentApplyAmt(String currentApplyAmt) {
		this.currentApplyAmt = currentApplyAmt;
	}

	public String getShareAmt() {
		return shareAmt;
	}

	public void setShareAmt(String shareAmt) {
		this.shareAmt = shareAmt;
	}

	public String getUseAMT() {
		return useAMT;
	}

	public void setUseAMT(String useAMT) {
		this.useAMT = useAMT;
	}

	public String getUseBrId() {
		return useBrId;
	}

	public void setUseBrId(String useBrId) {
		this.useBrId = useBrId;
	}

	/** 額度明細表現請額度 */
	@Column(name = "CURRENTAPPLYAMT", columnDefinition = "DECIMAL(17,2)")
	private String currentApplyAmt;
	/** 攤貸行額度 */

	@Column(name = "SHAREAMT", columnDefinition = "DECIMAL(17,2)")
	private String shareAmt;
	/** 顯示的現請額度 */
	@Column(name = "USEAMT", columnDefinition = "DECIMAL(17,2)")
	private String useAMT;
	/** 可動用分行 */
	@Column(name = "USEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String useBrId;

}
