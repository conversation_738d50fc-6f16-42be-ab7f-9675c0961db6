package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 綜合評估 / 往來彙總(企金授權外) - 利害關係人授信條件對照表
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class CLSS07APanel04 extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public CLSS07APanel04(String id) {
		super(id);
	}
}
