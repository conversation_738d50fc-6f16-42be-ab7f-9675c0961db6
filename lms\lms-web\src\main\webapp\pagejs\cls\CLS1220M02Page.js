//做為畫面init完成後使用
var initDfd;
var _handler="cls1220m02formhandler";
pageJsInit(function() {
	$(function() {
		initDfd = initDfd || $.Deferred();
		
		var tabForm = $("#tabForm");
		var btnPanel = $("#buttonPanel");


		$.form.init({
			formId: "tabForm",
			formHandler: _handler,
			formAction: "query",
			loadSuccess: function(json) {
				if (!$("#buttonPanel").find("#btnSave").is("button") || json.lock) {
					tabForm.lockDoc();
				}

				if (true) {
					//當 html 中的 element 中有 codeType
					tabForm.buildItem();
				}

				if (json.page == "01") {
					build_selItem(json.selItem, json.selItemOrder, ".");
				}
				tabForm.injectData(json);
				initDfd.resolve(json);
			}
		});

		var saveAction = function(opts) {
			if (tabForm.valid()) {
				return $.ajax({
					type: "POST",
					handler: _handler,
					data: $.extend({
						formAction: "saveMain",
						page: responseJSON.page,
						mainOid: responseJSON.mainOid
					},
						tabForm.serializeData(),
						(opts || {})
					)
				}).done(function(json) {
					tabForm.injectData(json);
					//更新 opener 的 Grid
					API.triggerOpener();
				});

			} else {
				return $.Deferred();
			}
		}

		var flowAction = function(opts) {
			return $.ajax({
				type: "POST",
				handler: _handler, action: "flowAction",
				data: $.extend({
					mainOid: $("#mainOid").val(),
					mainDocStatus: $("#mainDocStatus").val()
				}
					, (opts || {})
				)
			}).done(function(json) {
				API.triggerOpener();//gridview.reloadGrid 
				window.close();
			});
		}

		btnPanel.find("#btnSave").click(function() {
			saveAction({ 'allowIncomplete': 'Y' }).done(function(json) {
				if (json.saveOkFlag) {
					var dyna = [];
					if (true) {
						dyna.push(i18n.def.saveSuccess);
					}

					if (json.IncompleteMsg) {
						dyna.push(json.IncompleteMsg);
					}

					API.showMessage(dyna.join("<br/>-------------------<br/>"));
				}
			});
		}).end().find("#btnSend").click(function() {
			saveAction().done(function(json) {
				if (json.saveOkFlag) {
					API.confirmMessage(i18n.def.confirmApply, function(result) {
						if (result) {
							flowAction();
						}
					});
				}
				else {
					/*
					改由 server 端拋出
					API.showMessage(i18n.cls1220m01['C122S01B.loanDataNotFound']);
					*/
					//	  API.showPopMessage(i18n.cls1220m01['C122S01B.loanDataNotFound'], window.close);
				}
			});

		}).end().find("#btnToEdit").click(function() {
			flowAction();
		}).end().find("#btnCancelEdit").click(function() {
			flowAction({ 'decisionExpr': '取消編製' });
		}).end().find("#btnAccept").click(function() {
			flowAction({ 'decisionExpr': '核定' });
		}).end().find("#btnReturn").click(function() {
			flowAction({ 'decisionExpr': '退回' });
		}).end().find("#btnPrint").click(function() {
			print();
		});

	});
});

function build_selItem(json_selItem, json_selItemOrder, sep, defaultVal){
	$.each(json_selItem, function(itemName, kvMap) {
		if( $("select#"+itemName).length>0 ){
			var chooseItem = $("#"+itemName);
			var _addSpace = false;
			if(chooseItem.attr("space")=="true"){
				_addSpace = true;
			}
			
			var _fmt = "{key}";
			if(chooseItem.attr("myShowKey")==="Y"){
				_fmt = "{value}"+sep+"{key}";
			}
			$.each(json_selItemOrder[itemName], function(idx, kVal) {
				var currobj = {};
	    		currobj[kVal] = kvMap[kVal];    			
	    		chooseItem.setItems({ item: currobj, format: _fmt, clear:false, space: (_addSpace?(idx==0):false) });	    		
			});
			
			if(defaultVal){
				chooseItem.val(defaultVal);
			}			
		}else if( $("input#"+itemName).length>0 ){
			/*
			 為了讓 c122s01a_batchNo 只有1筆時能隱藏
			模擬select 的狀態
			
			查出的資料順序是 
			[2]
			[1]
			
			所以在填入 input 時，抓 idx==0 的資料
			*/
			$.each(json_selItemOrder[itemName], function(idx, kVal) {
				if(idx==0){
					$("input#"+itemName).val(kVal);	
				}    		
			});
			if(defaultVal){
				$("input#"+itemName).val(defaultVal);
			}
		}		
	});
}


/**
 * 同時印多份: 
 * oid_arr.push(data.oid+"^"+data.mainId);
 * rptOid: oid_arr.join("|")
 */
function print(){
	$.form.submit({
        url: "../../simple/FileProcessingService",
        target: "_blank",
        data: {
            'rptOid': responseJSON.mainOid+"^"+responseJSON.mainId,
            'fileDownloadName': "CLS1220R01.pdf",
            serviceName: "cls1220r01rptservice"     
        }
    });    
}


$.extend(window.tempSave,{
	handler: _handler, // handler 名稱
	action: "tempSave", // action Method
	beforeCheck:function(){ // return false or true		
		return $("#tabForm").valid();
	},sendData:function(){ // 需上送之資料集合(Map<String,String>)
		return $("#tabForm").serializeData();
	}
});