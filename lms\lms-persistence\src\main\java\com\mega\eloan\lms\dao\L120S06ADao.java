/* 
 * L120S06ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;


import com.mega.eloan.lms.model.L120S06A;

/** 利害關係人授信條件對照表主檔 **/
public interface L120S06ADao extends IGenericDao<L120S06A> {

	L120S06A findByOid(String oid);

	List<L120S06A> findByMainId(String mainId);

	List<L120S06A> findByMainIdOrderPrintMode(String mainId);

	L120S06A findByUniqueKey(String mainId, String custId, String dupNo,
			String cntrNo, String refMainId);

	List<L120S06A> findByIndex01(String mainId, String custId, String dupNo,
			String cntrNo);

	List<L120S06A> findByCntrNo(String CntrNo);

	List<L120S06A> findByCustIdDupId(String custId,String DupNo);
	
	int delModel(String mainId);
}