<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
			<style type="text/css">        		 
			 table.alignTopTab tr{ vertical-align:top;}
			</style>
			<script type="text/javascript">
					loadScript('pagejs/cls/CLS3401M07Page');
			</script>  
			
			<div class="button-menu funcContainer" id="buttonPanel">
				
				<!--編製中  -->
				<th:block th:if="${_btnDOC_EDITING_visible}">
	        		<!--button id="btnSave"> 
	        			<span class="ui-icon ui-icon-jcs-04" />
	        			<th:block th:text="#{'button.save'}">儲存</th:block>
	        		</button-->
					<!--button id="btnSend" >
	        			<span class="ui-icon ui-icon-jcs-02" />
	        			<th:block th:text="#{'button.send'}" >呈主管覆核</th:block>
	        		</button-->
		        </th:block>		
				
				<!--待覆核  -->
				<th:block th:if="${_btnWAIT_APPROVE_visible}">
	        		<!--button id="btnAccept" >
	        			<span class="ui-icon ui-icon-jcs-106" />
	        			<th:block th:text="#{'button.check'}" >覆核</th:block>
	        		</button-->
		        </th:block>
				
				<th:block th:if="${_btnCANCEL_visible}">
					<!--button id="btnCancelFlow" >
						<span class="ui-icon ui-icon-jcs-106" />
						<th:block th:text="#{'button.cancelFlow'}" >對保作廢</th:block>
					</button-->
				</th:block>

				<!--button id="btnPrint" class="forview">
                	<span class="ui-icon ui-icon-jcs-03"></span>
					<th:block th:text="#{'button.print'}">列印</th:block>
				</button-->
                <button id="btnExit"  class="forview">
                	<span class="ui-icon ui-icon-jcs-01"></span>
					<th:block th:text="#{'button.exit'}">離開</th:block>
				</button>				
            </div>
			
			<div class="tit2 color-black">
				<span id="ctrTypeHeaderDesc" class="" />：<span id="custInfo" class="color-blue" />&nbsp;
			</div>

			<div class="tabs doc-tabs">
                <ul>
                    <li id="tab01"><a href="#tab-01" goto="01"><b><th:block th:text="#{'tab.01'}">文件資訊</th:block></b></a></li>
                </ul>
                <div class="tabCtx-warp">
                    <form id="tabForm">
                    	<div th:id="${tabIdx}" th:insert="~{${panelName} :: ${panelFragmentName}}"></div>
                    </form>
                </div>
            </div>	
			
		</th:block>
    </body>
</html>
