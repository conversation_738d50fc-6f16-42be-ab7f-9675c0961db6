var pageAction = {
	handler : 'lms9541v02formhandler',
	grid : null,
	cityGrid : null,
	brnoGrid : null,
	build : function(){
		pageAction.grid = $("#gridview").iGrid({
			handler : 'lms9541v02gridhandler',
			height : 400,
			action :  "query",
			rowNum:15,
			rownumbers:false,
			colModel : [{
				colHeader : "oid",
				name : 'oid',
				hidden : true //是否隱藏
			},{
				colHeader : "useType", //統計表類型
				hidden : true,
				name : 'useType' //col.id
			},{
				colHeader : "rptType", //報表類型
				hidden : true,
				name : 'rptType' //col.id
			},{
				colHeader : i18n.lms9541v02["useType"], //統計表類型
				align : "center",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				hidden : false,
				//formatter : 'click',
				//onclick : function,
				name : 'useTypeName' //col.id
			},{
				colHeader : i18n.lms9541v02["rptName"], //控制表類型
				align : "center",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				hidden : false,
				//formatter : 'click',
				//onclick : function,
				name : 'rptName' //col.id
			},{
				colHeader : i18n.lms9541v02["endDate"], //截止日期
				align : "center",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				hidden : false,
				//formatter : 'click',
				//onclick : function,
				name : 'endDate' //col.id
			},{
				colHeader : "creator", //建立人員號碼
				hidden : true,
				name : 'creator' //col.id
			},{
				colHeader : i18n.lms9541v02["rptTime"], //建立日期
				hidden:true,
				name : 'createTime' //col.id
			},{
				colHeader : "updater", //異動人員號碼
				hidden : true,
				name : 'updater' //col.id
			},{
				colHeader : "updateTime", //異動日期
				hidden : true,
				name : 'updateTime' //col.id
			}],
			ondblClickRow: function(rowid){//同列印
				var data = pageAction.grid.getRowData(rowid);
				if(data.useType=="3"){
					pageAction.openTotal(data);
				}
				else
					pageAction.openViewGrid(data);
			}				
		});
		
		pageAction.cityGrid = $("#cityGrid").iGrid({
			handler : 'lms9541v02gridhandler',
			height : 300,
			action : "queryCityGrid",
			rowNum : 13,
			rownumbers:false,
			//multiselect : true,
			colModel : [{
	            colHeader: i18n.lms9541v02['area'],//標的物所在地
	            name: 'area',
	            width: 50,
	            align: "center",
	            sortable: true
	        },{
	            colHeader: i18n.lms9541v02['houses'],//戶數
	            name: 'houses',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'integer'
	        },{
	            colHeader: i18n.lms9541v02['sumup'],//合計
	            name: 'sumup',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'integer'
	        },{
	            colHeader: i18n.lms9541v02['normal'],//一般利率
	            name: 'normal',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'integer'
	        },{
	            colHeader: i18n.lms9541v02['favor'],//優惠利率
	            name: 'favor',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'integer'
	        },{
	            colHeader: i18n.lms9541v02['houses'],//戶數
	            name: 'acHouses',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'integer'
	        },{
	            colHeader: i18n.lms9541v02['sumup'],//合計
	            name: 'acSumup',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'currency',
	            formatter: 'integer'
	        },{
	            colHeader: i18n.lms9541v02['normal'],//一般利率
	            name: 'acNormal',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'integer'
	        },{
	            colHeader: i18n.lms9541v02['favor'],//優惠利率
	            name: 'acFavor',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'integer'
	        },{
	            colHeader: i18n.lms9541v02['newHouse'],//新屋
	            name: 'newHouse',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'integer'
	        },{
	            colHeader: i18n.lms9541v02['oldHouse'],//中古屋
	            name: 'oldHouse',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'integer'
	        },{
	            colHeader: i18n.lms9541v02['overage'],//餘額
	            name: 'overage',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'integer'
	        }]
		});
		pageAction.cityGrid.jqGrid('setGroupHeaders', {  
	        groupHeaders:[  
	              {startColumnName: 'houses', numberOfColumns: 4, titleText: pageAction.addAlign(i18n.lms9541v02["accept"]+i18n.lms9541v02["amt"])} ,
	              {startColumnName: 'acHouses', numberOfColumns: 4, titleText: pageAction.addAlign(i18n.lms9541v02["allocate"]+i18n.lms9541v02["amt"])},
	              {startColumnName: 'newHouse', numberOfColumns: 2, titleText: pageAction.addAlign(i18n.lms9541v02["allocateHouses"])}
	        ]
	    });  
		pageAction.brnoGrid = $("#brnoGrid").iGrid({
			handler : 'lms9541v02gridhandler',
			height : 300,
			action : "queryBrnoGrid",
			rowNum : 13,
			rownumbers:false,
			//multiselect : true,
			colModel : [{
	            colHeader: i18n.lms9541v02['brno'],//分行名(代號+名)
	            name: 'brno',
	            width: 50,
	            align: "center",
	            sortable: true
	        },{
	            colHeader: i18n.lms9541v02['houses'],//戶數
	            name: 'houses',
	            width: 50,
	            align: "center",
	            sortable: true
	        },{
	            colHeader: i18n.lms9541v02['sumup'],//合計
	            name: 'sumUp',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'currency',
	            formatoptions: {
	            	thousandsSeparator: ",",
	            	decimalPlaces: 0
	            }
	        },{
	            colHeader: i18n.lms9541v02['normal'],//一般利率
	            name: 'normal',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'currency',
	            formatoptions: {
	            	thousandsSeparator: ",",
	            	decimalPlaces: 0
	            }
	        },{
	            colHeader: i18n.lms9541v02['favor'],//優惠利率
	            name: 'favor',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'currency',
	            formatoptions: {
	            	thousandsSeparator: ",",
	            	decimalPlaces: 0
	            }
	        },{
	            colHeader: i18n.lms9541v02['favorRate'],//優惠房貸比率
	            name: 'favorRate',
	            width: 50,
	            align: "center",
	            sortable: true
	        },{
	            colHeader: i18n.lms9541v02['houses'],//戶數
	            name: 'acHouses',
	            width: 50,
	            align: "center",
	            sortable: true
	        },{
	            colHeader: i18n.lms9541v02['sumup'],//合計
	            name: 'acSumUp',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'currency',
	            formatoptions: {
	            	thousandsSeparator: ",",
	            	decimalPlaces: 0
	            }
	        },{
	            colHeader: i18n.lms9541v02['normal'],//一般利率
	            name: 'acNormal',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'currency',
	            formatoptions: {
	            	thousandsSeparator: ",",
	            	decimalPlaces: 0
	            }
	        },{
	            colHeader: i18n.lms9541v02['favor'],//優惠利率
	            name: 'acFavor',
	            width: 50,
	            align: "center",
	            sortable: true,
	            formatter: 'currency',
	            formatoptions: {
	            	thousandsSeparator: ",",
	            	decimalPlaces: 0
	            }
	        },{
	            colHeader: i18n.lms9541v02['favorRate'],//優惠房貸比率
	            name: 'acFavorRate',
	            width: 50,
	            align: "center",
	            sortable: true
	        }]
		});
		pageAction.brnoGrid.jqGrid('setGroupHeaders', {  
	        useColSpanStyle: false, 
	        groupHeaders:[  
	              {startColumnName: 'houses', numberOfColumns: 5, titleText: pageAction.addAlign(i18n.lms9541v02["accept"])} ,
	              {startColumnName: 'acHouses', numberOfColumns: 5, titleText: pageAction.addAlign(i18n.lms9541v02["allocate"])}
	        ]
	    });  
		//build button 
		//新增
		$("#buttonPanel").find("#btnAdd").click(function() {
			//開窗~~
			$("div#addThickBox").thickbox({
				title : i18n.lms9541v02["addTitle"],
				width : 600,
				height : 340,
				modal : true,
				align : 'center',
				valign: 'bottom',
				i18n: i18n.def,
				buttons : {
					'sure' : function(){
						if($("#addForm").valid()){
							$.ajax({
								handler : pageAction.handler,
								action : 'add',
								data : {
									mainType : $("input[name=mainType]:checked").val(),
									type : $("input[name=type]:checked").val(),
									endDate : $("input#endDate").val()
								},
								success:function(response){
									$.thickbox.close();
									pageAction.reloadGrid();
									MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["addSuccess"]);
								}
							});
						}
					},
					'close' : function(){//關閉
						$.thickbox.close();
					}
				}
			});
		})
		//調閱
		.end().find("#btnView").click(function() {
			var data = pageAction.getRowData();
			if (data){
				if(data.useType=="3"){
					pageAction.openTotal(data);
				}
				else
					pageAction.openViewGrid(data);
			}
		})
		//列印(輸出excel)
		.end().find("#btnPrint").click(function() {
			var data = pageAction.getRowData();
			if (data){
				MegaApi.confirmMessage(i18n.lms9541v02["confirmOutputExcel"], function(action){
					if (action){
						$.capFileDownload({          
					        handler:"lmsdownloadformhandler",
					        data: {
					        	useType : data.useType,
								rptType : data.rptType,
								endDate : data.endDate ,
								fileDownloadName : data.rptName+".xls",
								serviceName : "lms9541v02xlsservice"
						   }
						}); 
					}
				});
			}
		});
	},
	openTotal : function(data){
		//塞值
		var frame = $("#totalBox");
		$.ajax({
			handler : pageAction.handler,
			action : 'queryTot',
			data : {
				kindNo :data.rptType
			},
			success:function(response){
				for(var key in response){
					//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
					var tKey = DOMPurify.sanitize(key);
					$("#"+tKey).val(response[tKey]);
				}
			}
		});
		//開啟畫面
		frame.thickbox({
			title : i18n.lms9541v02["viewTitle"],
			width : 600,
			height : 350,
			modal : true,
			align : 'left',
			valign: 'top',
			i18n: i18n.lms9541v02,
			buttons : {
				'toExcel' : function(){//輸出成Excel
					MegaApi.confirmMessage(i18n.lms9541v02["confirmOutputExcel"], function(action){
						if (action){
							$.capFileDownload({          
						        handler:"lmsdownloadformhandler",
						        data: {
						        	useType : data.useType,
									rptType : data.rptType,
									endDate : data.endDate ,
									fileDownloadName : data.rptName+".xls",
									serviceName : "lms9541v02xlsservice"
							   }
							}); 
						}
					});
				},
				'sendMail' : function(){
					MegaApi.confirmMessage(i18n.lms9541v02["confirmSendMail"], function(action){
						if (action){
							$.ajax({
								handler : pageAction.handler,
								action : 'sendMail',
								form : 'totalForm',
								data : {oid:data.oid},
								success:function(response){
									if(response["errorMsg"])
										MegaApi.showErrorMessage(i18n.def["confirmTitle"],i18n.lms9541v02[response["errorMsg"]]);
									else
										MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["confirmDeliverSuccess"]);
								}
							});
						}
					});
				},
				'close' : function(){//關閉
					$.thickbox.close();
				}
			}
		});
	},
	openViewGrid : function(data){
		var frame = $("#viewThickBox");
		frame.find("#city").hide();
		frame.find("#brno").hide();
		if(data.useType=="2"){
			frame.find("#city").show();
			pageAction.cityGrid.jqGrid("setGridParam", {
				postData: {
					type:data.rptType
				},
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}
		else{
			frame.find("#brno").show();
			pageAction.brnoGrid.jqGrid("setGridParam", {
				postData: {
					type:data.rptType,
					endDate : data.endDate
				},
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}
		//開啟畫面
		frame.thickbox({
			title : i18n.lms9541v02["viewTitle"],
			width : 1000,
			height : 520,
			modal : true,
			align : 'left',
			valign: 'top',
			i18n: i18n.lms9541v02,
			buttons : {
				'toExcel' : function(){//輸出成Excel
					MegaApi.confirmMessage(i18n.lms9541v02["confirmOutputExcel"], function(action){
						if (action){
							$.capFileDownload({          
						        handler:"lmsdownloadformhandler",
						        data: {
						        	useType : data.useType,
									rptType : data.rptType,
									endDate : data.endDate,
									fileDownloadName : data.rptName+".xls",
									serviceName : "lms9541v02xlsservice"
							    }
							}); 
							
						}
					});
				},
				'close' : function(){//關閉
					$.thickbox.close();
				}
			}
		});
	},
	/**
	 * 使字串置中
	 */
	addAlign : function(txt){
		return "<p align='center'>"+txt+"</p>";
	},	
	/**
	 * 取得資料表之選擇列
	 */
	getRowData : function(){
		var row = pageAction.grid.getGridParam('selrow');
		var data;
		if (row) {
			data = pageAction.grid.getRowData(row);
		}else{
			MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["grid.selrow"]);
		}
		return data;
	},
	/**
	 * 重整資料表
	 */
	reloadGrid : function(data){
		if (data){
			pageAction.grid.jqGrid("setGridParam", {
				postData : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}else{
			pageAction.grid.trigger('reloadGrid');
		}
	}
}

$(function() {
	pageAction.build();
});