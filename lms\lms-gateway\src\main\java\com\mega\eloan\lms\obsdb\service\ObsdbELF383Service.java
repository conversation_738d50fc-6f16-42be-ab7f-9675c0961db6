/* 
 *ObsdbELF383Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service;

import java.util.List;

/**
 * <pre>
 * 授信額度檔ELF383
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
public interface ObsdbELF383Service {

	/**
	 * 批次新增
	 * 
	 * @param BRNID
	 *            上傳銀行代碼
	 * @param dataList
	 *            sql list
	 * 
	 *            <pre>
	 * 
	 *   custid 借款人統編
	 *  dupno DUPNO
	 *  cntrno  CNTRNO
	 *  sdate 系統當天日期
	 *  casetype 案件性質
	 *  lnflag 性質A.增額、D.減額 N.新作 C.額度取消 U.僅聯貸參貸比例變更
	 * 
	 *  oldamt 原請額度
	 *  curamt 現請額度
	 * 
	 *  curcurr 現請額度幣別
	 *  lnqtflag 科子目限額變更
	 *  reclflag  循環額度變更
	 *  sguflag 送註記
	 *  lrptype  動用審核表－授信契約書型態，短期為１；中長期為2
	 *  llnno 動用審核表-授信期間代碼 1. YYY/MM/DD～YYY/MM/DD 2. 自首動日起YY年XX個月 3. 其他
	 * 
	 *  llnfdate 中長期授信期間代碼為1時為起始日期否則設為0001-01-01
	 *  llnedate 中長期授信期間代碼為1時為終止日期否則設為0001-01-01
	 *  llnmon 中長期授信期間代碼為2時會將年月轉換成月
	 * 
	 *  lnuseno 1.YYY/MM/DD～YYY/MM/DD 2.自首次動用日起XX月 3.其他
	 * 
	 *  usefmdt 動用期限代碼為1時為起始日期 動用期限代碼為2,3時 設為0001-01-01
	 * 
	 *  useendt 動用期限代碼為1時 為終止日期 動用期限代碼為2,3時 設為0001-01-01
	 * 
	 *  useftmn  動用期限代碼為2時 以月為單位,若為１年則為12
	 * 
	 *  memo  其他敘作條件提示用語
	 *  grantno 授權等級
	 *  commborw 是否有共同借款人
	 *  updater 資料修改人（行員代號）
	 *  TMESTAMP 資料修改日期DECIMAL(19)
	 *  reclchg 循環/不循環是否變更
	 *  sguchg 送保註記是否變更
	 *  gutflag 信保保證成數是否變更
	 *  gutper  信保保證成數DECIMAL(3)
	 *  llneflag 授信期間－終止日期是否變更(若非新作案件而此欄有變更者為Y)
	 *  useeflag 動用期限－終止日期是否變更(若非新作案件而此欄有變更者為Y)
	 *  lnnoflag 不計入授信項目代號
	 *  unichgflag 聯貸案參貸金額是否變更(Y.是、N.否(若為新案則皆為Y))
	 *  reflag  是否為銀行法或金控法利害關係人(Y.是、N.否(只要為其中之一即為Y))
	 *  unionamt   聯貸額度(包含同業) DECIMAL(18)
	 *  shareamt 本行參貸總額度 DECIMAL(18)
	 *  permittype 報核方式
	 *  hideunion  是否為隱名參貸
	 *  setdate  聯貸合約訂定日 DECIMAL(8)
	 *  unionarea 國內聯貸或國際聯貸 A=國內　B=國際
	 *  unionrole 聯行主辦(管理)行註記(Y=聯貸主辦行)
	 *  riskarea 本額度風險歸屬國別 Y=聯貸主辦行 L=聯貸案額度管理行 C=聯貸案擔保品管理行
	 * 
	 *  existdate   OBU公司存續證明到期日 DECIMAL(8)
	 *  feedate  OBU公司繳交年費證明到期日 DECIMAL(8)
	 *  countrydt OBU公司註冊國有效期 DECIMAL(8)
	 *  crdttbl   信用評等等級
	 *  mowtype  信用風險模型評等類別
	 *  mowtbl1  信用風險模型評等等級
	 *  syndipfd  聯貸信保註記
	 *  cokind  合作業務種類 1:價金履約保證 2:合作外匯 Z:其他
	 *   
	 *  cntrnom   合作業務母戶額度序號(12)
	 *  rclno   合作業務子戶代收帳號(14)
	 *  documentno 簽報書案號(20)
	 *  crdtymd   舊評等日期 DECIMAL(8)
	 *  crdtbr   舊評等分行
	 *  mowymd   MOW 評等日期 DECIMAL(8)
	 *  mowbr  MOW 評等分行
	 *  MODYDATE MOODY評等日期 DECIMAL(8)
	 *  MOODYGRD MOODY評等 CHAR(10)
	 *  SPDATE SP評等日期 DECIMAL(8)
	 *  SPGRD  SP評等 CHAR(10)
	 *  FITCHDATE FITCH評等日期 DECIMAL(8)
	 *  FITCHGRD FITCH評等 CHAR(10)
	 *  controlcd
	 *            央行購住/空地/建屋貸款註記 屬於自然人購入住宅者(不含購入謄本登記主要用途為廠辦、商辦、店舖者)|1
	 *            屬於自然人/法人空地抵押貸款者(含擔保或副擔保)|2 屬於自然人/法人興建房屋貸款者 |3 非屬以上性質者 |4
	 * 
	 *  duringflag 是否所有權取得日期在99/6/25以後或99/6/25後曾受央行管制客戶之增貸或轉貸案
	 *  ltvrate   貸款成數 DECIMAL(5,2)
	 *  locationcd 擔保品座落地區別 縣市別+區域別
	 *  jcicmark 本次聯徵查詢名下有其他房貸資料 是 |Y 否 |N
	 * </pre>
	 */

	void insertX_NOT_USE(String BRNID, List<Object[]> dataList); // NOT USE

	/**
	 * 單筆新增
	 * 
	 * @param BRNID
	 *            上傳銀行代碼
	 * @param dataList
	 *            sql list
	 * 
	 *            <pre>
	 * 
	 *   custid 借款人統編
	 *  dupno DUPNO
	 *  cntrno  CNTRNO
	 *  sdate 系統當天日期
	 *  casetype 案件性質
	 *  lnflag 性質A.增額、D.減額 N.新作 C.額度取消 U.僅聯貸參貸比例變更
	 * 
	 *  oldamt 原請額度
	 *  curamt 現請額度
	 * 
	 *  curcurr 現請額度幣別
	 *  lnqtflag 科子目限額變更
	 *  reclflag  循環額度變更
	 *  sguflag 送註記
	 *  lrptype  動用審核表－授信契約書型態，短期為１；中長期為2
	 *  llnno 動用審核表-授信期間代碼 1. YYY/MM/DD～YYY/MM/DD 2. 自首動日起YY年XX個月 3. 其他
	 * 
	 *  llnfdate 中長期授信期間代碼為1時為起始日期否則設為0001-01-01
	 *  llnedate 中長期授信期間代碼為1時為終止日期否則設為0001-01-01
	 *  llnmon 中長期授信期間代碼為2時會將年月轉換成月
	 * 
	 *  lnuseno 1.YYY/MM/DD～YYY/MM/DD 2.自首次動用日起XX月 3.其他
	 * 
	 *  usefmdt 動用期限代碼為1時為起始日期 動用期限代碼為2,3時 設為0001-01-01
	 * 
	 *  useendt 動用期限代碼為1時 為終止日期 動用期限代碼為2,3時 設為0001-01-01
	 * 
	 *  useftmn  動用期限代碼為2時 以月為單位,若為１年則為12
	 * 
	 *  memo  其他敘作條件提示用語
	 *  grantno 授權等級
	 *  commborw 是否有共同借款人
	 *  updater 資料修改人（行員代號）
	 *  TMESTAMP 資料修改日期DECIMAL(19)
	 *  reclchg 循環/不循環是否變更
	 *  sguchg 送保註記是否變更
	 *  gutflag 信保保證成數是否變更
	 *  gutper  信保保證成數DECIMAL(3)
	 *  llneflag 授信期間－終止日期是否變更(若非新作案件而此欄有變更者為Y)
	 *  useeflag 動用期限－終止日期是否變更(若非新作案件而此欄有變更者為Y)
	 *  lnnoflag 不計入授信項目代號
	 *  unichgflag 聯貸案參貸金額是否變更(Y.是、N.否(若為新案則皆為Y))
	 *  reflag  是否為銀行法或金控法利害關係人(Y.是、N.否(只要為其中之一即為Y))
	 *  unionamt   聯貸額度(包含同業) DECIMAL(18)
	 *  shareamt 本行參貸總額度 DECIMAL(18)
	 *  permittype 報核方式
	 *  hideunion  是否為隱名參貸
	 *  setdate  聯貸合約訂定日 DECIMAL(8)
	 *  unionarea 國內聯貸或國際聯貸 A=國內　B=國際
	 *  unionrole 聯行主辦(管理)行註記 Y=聯貸主辦行 L=聯貸案額度管理行 C=聯貸案擔保品管理行
	 *  riskarea 本額度風險歸屬國別 
	 * 
	 *  existdate   OBU公司存續證明到期日 DECIMAL(8)
	 *  feedate  OBU公司繳交年費證明到期日 DECIMAL(8)
	 *  countrydt OBU公司註冊國有效期 DECIMAL(8)
	 *  crdttbl   信用評等等級
	 *  mowtype  信用風險模型評等類別
	 *  mowtbl1  信用風險模型評等等級
	 *  syndipfd  聯貸信保註記
	 *  cokind  合作業務種類 1:價金履約保證 2:合作外匯 Z:其他
	 *   
	 *  cntrnom   合作業務母戶額度序號(12)
	 *  rclno   合作業務子戶代收帳號(14)
	 *  documentno 簽報書案號(20)
	 *  crdtymd   舊評等日期 DECIMAL(8)
	 *  crdtbr   舊評等分行
	 *  mowymd   MOW 評等日期 DECIMAL(8)
	 *  mowbr  MOW 評等分行
	 *  MODYDATE MOODY評等日期 DECIMAL(8)  as400新增欄位
	 *  MOODYGRD MOODY評等 CHAR(10)        as400新增欄位
	 *  SPDATE SP評等日期 DECIMAL(8)       as400新增欄位
	 *  SPGRD  SP評等 CHAR(10)             as400新增欄位
	 *  FITCHDATE FITCH評等日期 DECIMAL(8) as400新增欄位
	 *  FITCHGRD FITCH評等 CHAR(10)        as400新增欄位
	 *  controlcd
	 *            央行購住/空地/建屋貸款註記 屬於自然人購入住宅者(不含購入謄本登記主要用途為廠辦、商辦、店舖者)|1
	 *            屬於自然人/法人空地抵押貸款者(含擔保或副擔保)|2 屬於自然人/法人興建房屋貸款者 |3 非屬以上性質者 |4
	 * 
	 *  duringflag 是否所有權取得日期在99/6/25以後或99/6/25後曾受央行管制客戶之增貸或轉貸案
	 *  ltvrate   貸款成數 DECIMAL(5,2)
	 *  locationcd 擔保品座落地區別 縣市別+區域別
	 *  jcicmark 本次聯徵查詢名下有其他房貸資料 是 |Y 否 |N
	 * </pre>
	 */
	void insert(String BRNID, Object[] dataList);

	/**
	 * 刪除
	 * 
	 * @param BRNID
	 *            上傳銀行號碼
	 * @param custId
	 *            身分證/統編
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            系統當天日期
	 */
	void deleteByKey(String BRNID, String custId, String dupNo, String cntrNo,
			String sDate);

}
