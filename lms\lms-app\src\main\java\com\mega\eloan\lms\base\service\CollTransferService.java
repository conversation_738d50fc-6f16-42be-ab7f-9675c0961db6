/* 
 * CollTransferService.java
 * 
 * Copyright (c) 2011-2011 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service;

import tw.com.iisi.cap.exception.CapException;

import com.mega.eloan.lms.base.common.MISRows;

public interface CollTransferService {

	/**
	 * <pre>
	 * 擔保品資料上傳主機:
	 *   會先刪除主機上重複資料，在上傳本次新增資料
	 * </pre>
	 * 
	 * @param <T>
	 *            MIS Table物件
	 * @param misRows
	 *            MIS資料處理物件
	 */
	public <T> void upMisToServer(MISRows<T> misRows) throws CapException;

}
