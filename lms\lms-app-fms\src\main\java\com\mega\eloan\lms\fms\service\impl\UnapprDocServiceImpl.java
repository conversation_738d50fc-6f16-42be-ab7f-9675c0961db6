/* 
 * UnapprDocServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dao.C102M01ADao;
import com.mega.eloan.lms.dao.C124M01ADao;
import com.mega.eloan.lms.dao.C160M01ADao;
import com.mega.eloan.lms.dao.C240M01ADao;
import com.mega.eloan.lms.dao.C241M01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L141M01ADao;
import com.mega.eloan.lms.dao.L160M01ADao;
import com.mega.eloan.lms.dao.L170M01ADao;
import com.mega.eloan.lms.dao.L180M01ADao;
import com.mega.eloan.lms.dao.L181M01ADao;
import com.mega.eloan.lms.dao.L192M01ADao;
import com.mega.eloan.lms.dao.L210M01ADao;
import com.mega.eloan.lms.dao.L230M01ADao;
import com.mega.eloan.lms.dao.L712M01ADao;
import com.mega.eloan.lms.dao.L785M01ADao;
import com.mega.eloan.lms.dao.L918M01ADao;
import com.mega.eloan.lms.fms.service.UnapprDocService;
import com.mega.eloan.lms.model.C102M01A;
import com.mega.eloan.lms.model.C124M01A;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L141M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L210M01A;
import com.mega.eloan.lms.model.L230M01A;
import com.mega.eloan.lms.model.L712M01A;
import com.mega.eloan.lms.model.L785M01A;
import com.mega.eloan.lms.model.L918M01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;

/**
 * <pre>
 * 取消覆核
 * </pre>
 * 
 * @since 2012/6/26
 * <AUTHOR>
 * @version <ul>
 *          2012/6/26,REX,new
 *          </ul>
 */
@Service
public class UnapprDocServiceImpl implements UnapprDocService {
	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L141M01ADao l141m01aDao;

	@Resource
	L210M01ADao l210m01aDao;
	@Resource
	L160M01ADao l160m01aDao;

	@Resource
	L230M01ADao l230m01aDao;

	@Resource
	L170M01ADao l170m01aDao;

	@Resource
	L181M01ADao l181m01aDao;

	@Resource
	L180M01ADao l180m01aDao;

	@Resource
	C240M01ADao c240m01aDao;
	@Resource
	C241M01ADao c241m01aDao;
	@Resource
	L192M01ADao l192m01aDao;
	@Resource
	C160M01ADao c160m01aDao;
	@Resource
	C102M01ADao c102m01aDao;

	@Resource
	L918M01ADao l918m01aDao;
	@Resource
	L712M01ADao l712m01aDao;
	@Resource
	L785M01ADao l785m01aDao;
	@Resource
	C124M01ADao c124m01aDao;

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		// L120M01A 案件簽報書
		// L141M01A 聯行額度明細表
		// L160M01A 動用審核表
		// L210M01A 修改資料特殊流程
		// L230M01A 簽約未動用
		// L170M01A 企金覆審報告表
		// L180M01A 企金覆審名單
		// C241M01A 個金覆審報告表
		// C240M01A 個金覆審工作底稿
		// L192M01A 稽核工作底稿
		if (clazz == L120M01A.class) {
			return (T) l120m01aDao.findByOid(oid);
		} else if (clazz == L141M01A.class) {
			return (T) l141m01aDao.findByOid(oid);
		} else if (clazz == L160M01A.class) {
			return (T) l160m01aDao.findByOid(oid);
		} else if (clazz == L210M01A.class) {
			return (T) l210m01aDao.findByOid(oid);
		} else if (clazz == L230M01A.class) {
			return (T) l230m01aDao.findByOid(oid);
		} else if (clazz == L170M01A.class) {
			return (T) l170m01aDao.findByOid(oid);
		} else if (clazz == L180M01A.class) {
			return (T) l180m01aDao.findByOid(oid);
		} else if (clazz == C241M01A.class) {
			return (T) c241m01aDao.findByOid(oid);
		} else if (clazz == C240M01A.class) {
			return (T) c240m01aDao.findByOid(oid);
		} else if (clazz == L192M01A.class) {
			return (T) l192m01aDao.find(oid);
		} else if (clazz == C160M01A.class) {
			return (T) c160m01aDao.findByOid(oid);
		} else if (clazz == C102M01A.class) {
			return (T) c102m01aDao.findByOid(oid);
		} else if (clazz == L918M01A.class) {
			return (T) l918m01aDao.findByOid(oid);
		} else if (clazz == L712M01A.class) {
			return (T) l712m01aDao.findByOid(oid);
		} else if (clazz == L785M01A.class) {
			return (T) l785m01aDao.findByOid(oid);
		} else if (clazz == C124M01A.class) {
			return (T) c124m01aDao.findByOid(oid);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {

		// L120M01A 案件簽報書
		// L141M01A 聯行額度明細表
		// L160M01A 動用審核表
		// L210M01A 修改資料特殊流程
		// L230M01A 簽約未動用
		// L170M01A 企金覆審報告表
		// L180M01A 企金覆審名單
		// C241M01A 個金覆審報告表
		// C240M01A 個金覆審工作底稿
		// L192M01A 稽核工作底稿
		if (clazz == L120M01A.class) {
			return l120m01aDao.findPage(search);
		} else if (clazz == L141M01A.class) {
			return l141m01aDao.findPage(search);
		} else if (clazz == L160M01A.class) {
			return l160m01aDao.findPage(search);
		} else if (clazz == L210M01A.class) {
			return l210m01aDao.findPage(search);
		} else if (clazz == L230M01A.class) {
			return l230m01aDao.findPage(search);
		} else if (clazz == L170M01A.class) {
			return l170m01aDao.findPage(search);
		} else if (clazz == L180M01A.class) {
			return l180m01aDao.findPage(search);
		} else if (clazz == C240M01A.class) {
			return c240m01aDao.findPage(search);
		} else if (clazz == C241M01A.class) {
			return c241m01aDao.findPage(search);
		} else if (clazz == C241M01A.class) {
			return c241m01aDao.findPage(search);
		} else if (clazz == L192M01A.class) {
			return l192m01aDao.findPage(search);
		} else if (clazz == C160M01A.class) {
			return c160m01aDao.findPage(search);
		} else if (clazz == C102M01A.class) {
			return c102m01aDao.findPage(search);
		} else if (clazz == L918M01A.class) {
			return l918m01aDao.findPage(search);
		} else if (clazz == L712M01A.class) {
			return l712m01aDao.findPage(search);
		} else if (clazz == L785M01A.class) {
			return l785m01aDao.findPage(search);
		} else if (clazz == C124M01A.class) {
			return c124m01aDao.findPage(search);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		// L120M01A 案件簽報書
		// L141M01A 聯行額度明細表
		// L160M01A 動用審核表
		// L210M01A 修改資料特殊流程
		// L230M01A 簽約未動用
		// L170M01A 企金覆審報告表
		// L180M01A 企金覆審名單
		// C241M01A 個金覆審報告表
		// C240M01A 個金覆審工作底稿
		// L192M01A 稽核工作底稿
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L120M01A) {
					l120m01aDao.save((L120M01A) model);
				} else if (model instanceof L141M01A) {
					l141m01aDao.save((L141M01A) model);
				} else if (model instanceof L160M01A) {
					l160m01aDao.save((L160M01A) model);
				} else if (model instanceof L210M01A) {
					l210m01aDao.save((L210M01A) model);
				} else if (model instanceof L230M01A) {
					l230m01aDao.save((L230M01A) model);
				} else if (model instanceof L170M01A) {
					l170m01aDao.save((L170M01A) model);
				} else if (model instanceof L180M01A) {
					l180m01aDao.save((L180M01A) model);
				} else if (model instanceof C241M01A) {
					c241m01aDao.save((C241M01A) model);
				} else if (model instanceof C240M01A) {
					c240m01aDao.save((C240M01A) model);
				} else if (model instanceof L192M01A) {
					l192m01aDao.save((L192M01A) model);
				} else if (model instanceof C160M01A) {
					c160m01aDao.save((C160M01A) model);
				} else if (model instanceof C102M01A) {
					c102m01aDao.save((C102M01A) model);
				} else if (model instanceof L918M01A) {
					l918m01aDao.save((L918M01A) model);
				} else if (model instanceof L712M01A) {
					l712m01aDao.save((L712M01A) model);
				} else if (model instanceof L785M01A) {
					l785m01aDao.save((L785M01A) model);
				} else if (model instanceof C124M01A) {
					c124m01aDao.save((C124M01A) model);
				}
			}
		}
	}
}
