/* 
 * L120S08B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 利率定價核理性分析表明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S08B", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S08B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 幣別<p/>
	 * NOT NULL
	 */
	@Size(max=3)
	@Column(name="CURR", length=3, columnDefinition="CHAR(3)")
	private String curr;

	/** 序號 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SEQNO", columnDefinition="DECIMAL(3,0)")
	private BigDecimal seqNo;

	 

	/** 
	 * 項目欄位名稱<p/>
	 * baseRate基礎放款利率<br/>
	 *  reasonRate合理利潤率<br/>
	 *  underweight授權減碼<br/>
	 *  collateral擔保條件<br/>
	 *  dealing存、匯及其他業務實績<br/>
	 *  contribution具貢獻潛力<br/>
	 *  guarantor連保人(含評等、信譽)<br/>
	 *  group集團企業<br/>
	 *  competition同業競爭說明<br/>
	 *  otherDscr其他說明<br/>
	 *  lowestRate本案申請利率最低
	 */
	@Size(max=30)
	@Column(name="ITEMNAME", length=30, columnDefinition="VARCHAR(30)")
	private String itemName;

	/** 項目說明 **/
	@Size(max=4096)
	@Column(name="DSCR", length=4096, columnDefinition="VARCHAR(4096)")
	private String dscr;

	/** 加減碼/利率擔保 **/
	@Column(name="DISYEARRATES", columnDefinition="DECIMAL(7,5)")
	private BigDecimal disYearRateS;
	
	/** 加減碼/利率非擔保 **/
	@Column(name="DISYEARRATEN", columnDefinition="DECIMAL(7,5)")
	private BigDecimal disYearRateN;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String Creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 刪除註記 **/
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;
	
	/** 借款人統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "CHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得幣別<p/>
	 * NOT NULL
	 */
	public String getCurr() {
		return this.curr;
	}
	/**
	 *  設定幣別<p/>
	 *  NOT NULL
	 **/
	public void setCurr(String value) {
		this.curr = value;
	}

	/** 取得序號 **/
	public BigDecimal getSeqNo() {
		return this.seqNo;
	}
	/** 設定序號 **/
	public void setSeqNo(BigDecimal value) {
		this.seqNo = value;
	}

	 

	/** 
	 * 取得項目欄位名稱<p/>
	 * baseRate基礎放款利率<br/>
	 *  reasonRate合理利潤率<br/>
	 *  underweight授權減碼<br/>
	 *  collateral擔保條件<br/>
	 *  dealing存、匯及其他業務實績<br/>
	 *  contribution具貢獻潛力<br/>
	 *  guarantor連保人(含評等、信譽)<br/>
	 *  group集團企業<br/>
	 *  competition同業競爭說明<br/>
	 *  otherDscr其他說明<br/>
	 *  lowestRate本案申請利率最低
	 */
	public String getItemName() {
		return this.itemName;
	}
	/**
	 *  設定項目欄位名稱<p/>
	 *  baseRate基礎放款利率<br/>
	 *  reasonRate合理利潤率<br/>
	 *  underweight授權減碼<br/>
	 *  collateral擔保條件<br/>
	 *  dealing存、匯及其他業務實績<br/>
	 *  contribution具貢獻潛力<br/>
	 *  guarantor連保人(含評等、信譽)<br/>
	 *  group集團企業<br/>
	 *  competition同業競爭說明<br/>
	 *  otherDscr其他說明<br/>
	 *  lowestRate本案申請利率最低
	 **/
	public void setItemName(String value) {
		this.itemName = value;
	}

	/** 取得項目說明 **/
	public String getDscr() {
		return this.dscr;
	}
	/** 設定項目說明 **/
	public void setDscr(String value) {
		this.dscr = value;
	}

	/** 取得加減碼/利率擔保 **/
	public BigDecimal getDisYearRateS() {
		return this.disYearRateS;
	}
	/** 設定加減碼/利率擔保 **/
	public void setDisYearRateS(BigDecimal value) {
		this.disYearRateS = value;
	}
	
	/** 取得加減碼/利率非擔保 **/
	public BigDecimal getDisYearRateN() {
		return this.disYearRateN;
	}
	/** 設定加減碼/利率非擔保 **/
	public void setDisYearRateN(BigDecimal value) {
		this.disYearRateN = value;
	}
	

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.Creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.Creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得刪除註記 **/
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/** 設定刪除註記 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}
	
	/** 設定借款人統編 **/
	public void setCustId(String custId) {
		this.custId = custId;
	}

	/** 取得借款人統編 **/
	public String getCustId() {
		return custId;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return dupNo;
	}
	
}
