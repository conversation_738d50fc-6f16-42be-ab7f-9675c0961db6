
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 消金覆審名單抽樣資訊檔 **/
@Entity
@Table(name="C240M01C", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","ruleNo"}))
public class C240M01C extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;
		
	/** oid */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 */
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 抽樣規則(參考 ELF491C_RULE_NO)
	 * <ul>
	 * <li>95-1 :「疑似人頭戶」追蹤對象之10%範圍內逐年辦理「專案查核」 //J-109-0372
	 *     <ul>
	 *     <li>統計「起迄區間之內」MIS.ELF491C.ELF491C_RULE_NO=95-1 的已覆審件數有幾筆
	 *     </li>
	 *     </ul>
	 * </li>
	 * <li>LS_95:是為了輔助 95-1 而多留存這項資料，「R95-1 疑似人頭戶專案覆審_逐年抽樣」是以每年6月底為「年度基準日」去決定抽樣比數 => 為了呈現「基準年月」是 {去年6月底}或{今年6月底}
	 *     <ul>
	 *     <li>今年5月產出的覆審工作底稿，在「疑似人頭戶」抓的「基準年月」，就一定會是 {去年6月底}
	 *     </li>
	 *     <li>因為還沒有到 {今年6月底}
	 *     </li>
	 *     </ul>
	 * </li>
	 * <li>13 : 專案信貸
	 *     <ul>
	 *     <li>產品種類08,71（SELECT * FROM LN.LNF07A where LNF07A_KEY_1 = 'CLS REEXAMINATION' and LNF07A_KEY_2='RULE 13'）
	 *     </li>
	 *     </ul>
	 * </li>
	 * <li>R1R2S : 不動產十足擔保{雙北:3000萬元；其它:1500萬元(含)以下}  //J-109-0213, 申請單號 (109) 第 1603 號 , 自 2020-05-22 修訂覆審作業須知
	 *     <ul>
	 *     <li>統計「起迄區間之內」MIS.ELF491C.ELF491C_RULE_NO=R1R2S 的已覆審件數有幾筆
	 *     </li>
	 *     <li>在 LMS.C241M01A.RETRIALKIND 是放 1~2 ；上傳到 ELF491C 時，改成放 R1R2S
	 *     </li>
	 *     <li>今年度的 目標件數 的資料來源：是在 LNBD9455 裡，把去年底的該類總件數寫到 ELF491_REMOMO的前13碼（where elf491_custId='AAAAAAAAAA'）  
	 *     </li>
	 *     </ul>
	 * </li>
	 * </ul>
	 */
	@Column(name="RULENO", length=5, columnDefinition="VARCHAR(5)")
	private String ruleNo;

	/** 總件數起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ALLBEGDATE", columnDefinition="DATE")
	private Date allBegDate;
	
	/** 總件數迄日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ALLENDDATE", columnDefinition="DATE")
	private Date allEndDate;
	
	/** 總件數 **/
	@Column(name="ALLCNT", columnDefinition="DECIMAL(6,0)")
	private Integer allCnt;

	/** 有效件數 **/
	@Column(name="ACTIVECNT", columnDefinition="DECIMAL(6,0)")
	private Integer activeCnt;
	
	/** 已抽樣起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DONEBEGDATE", columnDefinition="DATE")
	private Date doneBegDate;
	
	/** 已抽樣迄日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DONEENDDATE", columnDefinition="DATE")
	private Date doneEndDate;

	/** 已抽樣件數 **/
	@Column(name="DONECNT", columnDefinition="DECIMAL(6,0)")
	private Integer doneCnt;
	
	/** 已抽樣比率 **/
	@Column(name="DONERATE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal doneRate;
	
	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getRuleNo() {
		return ruleNo;
	}

	public void setRuleNo(String ruleNo) {
		this.ruleNo = ruleNo;
	}

	public Date getAllBegDate() {
		return allBegDate;
	}

	public void setAllBegDate(Date allBegDate) {
		this.allBegDate = allBegDate;
	}

	public Date getAllEndDate() {
		return allEndDate;
	}

	public void setAllEndDate(Date allEndDate) {
		this.allEndDate = allEndDate;
	}

	public Integer getAllCnt() {
		return allCnt;
	}

	public void setAllCnt(Integer allCnt) {
		this.allCnt = allCnt;
	}

	public Integer getActiveCnt() {
		return activeCnt;
	}

	public void setActiveCnt(Integer activeCnt) {
		this.activeCnt = activeCnt;
	}

	public Date getDoneBegDate() {
		return doneBegDate;
	}

	public void setDoneBegDate(Date doneBegDate) {
		this.doneBegDate = doneBegDate;
	}

	public Date getDoneEndDate() {
		return doneEndDate;
	}

	public void setDoneEndDate(Date doneEndDate) {
		this.doneEndDate = doneEndDate;
	}

	public Integer getDoneCnt() {
		return doneCnt;
	}

	public void setDoneCnt(Integer doneCnt) {
		this.doneCnt = doneCnt;
	}

	public BigDecimal getDoneRate() {
		return doneRate;
	}

	public void setDoneRate(BigDecimal doneRate) {
		this.doneRate = doneRate;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdater() {
		return updater;
	}

	public void setUpdater(String updater) {
		this.updater = updater;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
}
