/* 
 * L140S09ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S09A;

/** 其他敘作條件資訊檔 **/
public interface L140S09ADao extends IGenericDao<L140S09A> {

	L140S09A findByOid(String oid);
	
	List<L140S09A> findByMainId(String mainId);

	L140S09A findMaxSeqNumByMainId(String mainId, String bizCat, Integer bizCatId);

	L140S09A findMaxBizCatSeqNumByMainId(String mainId);

	List<L140S09A> findExist(String mainId, String loanTPs, String bizCat, String bizItem);

	List<L140S09A> findByLoanTPs(String mainId, String loanTPs);

	List<L140S09A> findByBizCat(String mainId, String bizCat);
	
	L140S09A findMaxBizCatIdByMainIdAndBizCat(String mainId, String bizCat);

	List<L140S09A> findByBizCatAndId(String mainId, String bizCat, Integer bizCatId);
}