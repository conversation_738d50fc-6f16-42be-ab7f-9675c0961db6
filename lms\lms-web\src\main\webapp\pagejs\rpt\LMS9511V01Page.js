var button = $("#buttonPanel");

// 上個月的一號跟月底 2013-01-01 2013-01-31
var lastDateStart1;
var lastDateEnd1;
// 上個月 2013-01 2013-01
var lastDateStart2;
var lastDateEnd2;
// 上上個月的一號 2012-12-01
var lastDateStart3;
// 上周一
var lastDateStart4;
// 上周六
var lastDateEnd4;
// 前四個月YYYY-MM
var lastDateStart5;
// 今年
var todayYear;
// 今年這個月
var todayYearMonth;
// 上個月
// var lastyYearMonth;
// 昨天
var yesDay;
// 下個月的一號跟月底 2013-01-01 2013-01-31
var nextDateStart1;
var nextDateEnd1;
// 上上個月的今天
var beforeLastToday;
// 今天 "yyyy-MM-dd"
var today;
// 前12個月YYYY-MM
var lastDateStart6;
// 前6個月YYYY-MM
var lastDateStart7;

var L9511v01Grid01;
var LMS180R01Grid;
var LMS180R01DetailGrid;
var LMS180R02AGrid;
var LMS180R02ADetail1Grid;
var LMS180R16Grid;
var lastSel;
var LMS180R16DetailGrid;
var LMS180R15Grid;
var regYYYYMM = /^\d{4}\-(0?[1-9]|1[0-2])$/;
var crs_assignId = 'branchList';
var lrs_assignId = 'branchList_lrs';
var lms180R63_bgnDate;
var fileGrid;
var docType = '1';// Vector done:把企個金類型改全域
var proPerty_LMS180R74; //J-113-0125_05097_B1001 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
// ================================ START DOCUMENT READY
// ====================================

				$(function() {
					hideBtn();
					initData();
					$("#lms9511GridShow01").show();
					$("#buttonPanel")
							.before(
									'<div class=" tit2 color-black" id="searchActionName" name="searchActionName"></div>');
					// 初始化欄位
					function initData() {
						// brank("");
						$
								.ajax({
									type : "POST",
									handler : "lms9511m01formhandler",
									data : {
										formAction : "queryReportType"
									},
									success : function(obj) {
										// 企金
										$("#searchAction1").setItems({
											// i18n : i18n.samplehome,
											item : obj.item,
											format : "{key}" // ,
										// value :
										});
										// 個金
										$("#searchAction2").setItems({
											// i18n : i18n.samplehome,
											item : obj.clsItem,
											format : "{key}" // ,
										// value :
										});
										// 個金-產品種類
										$("#prod").setItems({
											// i18n : i18n.samplehome,
											item : obj.prodItem,
											format : "{key}" // ,
										// value :
										});
										
										lastDateStart1 = obj.DATASTARTDATE1;
										lastDateStart2 = obj.DATASTARTDATE2;
										lastDateEnd1 = obj.DATAENDDATE1;
										lastDateEnd2 = obj.DATAENDDATE2;
										todayYear = obj.YEAR;
										todayYearMonth = obj.TODAYYEARMONTH;
										yesDay = obj.YESDAY;
										lastDateStart3 = obj.DATASTARTDATE3;
										nextDateStart1 = obj.NEXTDATASTARTDATE1
										nextDateEnd1 = obj.NEXTDATAENDDATE1;
										lastDateStart4 = obj.DATASTARTDATE4;
										lastDateEnd4 = obj.DATAENDDATE4;
										lastDateStart5 = obj.DATASTARTDATE5;
										beforeLastToday = obj.BEFORELASTTODAY;
										// J-107-0342_05097_B1003 Web
										// e-Loan授信系統新增覆審考核相關報表
										lastDateStart6 = obj.DATASTARTDATE6;
										// J-109-0132_05097_B1001
										// e-Loan授信系統新增「法令遵循自評檢核表」之抽測筆數所需之各檢核項目授信案件明細報表。
										lastDateStart7 = obj.DATASTARTDATE7;
										// J-110-0049_05097_B1001 Web
										// e-Loan企金授信增加「境內法人於國際金融業務分行辦理外幣授信業務報表」
										lms180R63_bgnDate = obj.LMS180R63_BGNDATE;

										//J-113-0125_05097_B1001 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
										proPerty_LMS180R74= obj.proPerty_LMS180R74;
										
										
										today = obj.TODAY;
										$("#dataStartDate1")
												.val(lastDateStart1);
										$("#dataStartDate2")
												.val(lastDateStart2);
										$("#dataEndDate1").val(lastDateEnd1);
										$("#dataEndDate2").val(lastDateEnd2);
										$("#year").val(todayYear);
										$("#dataYM").val(todayYearMonth);
										hideSearchActionData(obj);

										// 消金覆審報表相關
										
										//J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
										// 覆審行員
										var brStaffNo = $("#brStaffNo");
										brStaffNo.setItems({
											// i18n : i18n.samplehome,
											item : obj.brStaffNo,
											format : "{key}" // ,
										// value :
										});
										
										if (true) {
											// 分行選單
											if (true) {
												$
														.each(
																obj.branchItemOrder,
																function(idx,
																		brNo) {
																	var currobj = {};
																	var brName = obj.branchItem[brNo];
																	currobj[brNo] = brName;
																	// 依
																	// itemOrder,
																	// 一個一個append,
																	// 把 clear
																	// 指為 false

																	// select
																	$(
																			"#brType_S")
																			.setItems(
																					{
																						item : currobj,
																						format : "{value} {key}",
																						clear : false,
																						space : false
																					});
																});
											}

											if (true) {
												var col_cnt = 2;
												$("#div_brType_M")
														.append(
																_branchList_div(
																		col_cnt,
																		crs_assignId,
																		obj.branchItemOrder,
																		obj.branchItem)
																		.join(
																				"\n"));
											}

											$('input[type=radio][name=brType]')
													.change(
															function() {
																$(
																		"#div_brType_S")
																		.hide();
																$(
																		"#div_brType_M")
																		.hide();
																$(
																		"#div_split_orgBrnGroup_931_932")
																		.hide();
																if (this.value == 'S') {
																	$(
																			"#div_brType_S")
																			.show();
																} else if (this.value == 'M') {
																	$(
																			"#div_brType_M")
																			.show();

																	if (userInfo.unitNo == "918"
																			|| userInfo.unitNo == "900"
																			|| userInfo.unitNo == "931") {
																		$(
																				"#div_split_orgBrnGroup_931_932")
																				.show();
																	}
																}
															});

											$('#btn_branchList_1')
													.click(
															function() {
																$(
																		"input[name="
																				+ crs_assignId
																				+ "]")
																		.prop(
																				'checked',
																				true);
															});
											$('#btn_branchList_0')
													.click(
															function() {
																$(
																		"input[name="
																				+ crs_assignId
																				+ "]")
																		.prop(
																				'checked',
																				false);
															});
											$('#btn_orgBrnGroup_931')
													.click(
															function() {
																$(
																		"input[name="
																				+ crs_assignId
																				+ "].orgBrnGroup931")
																		.prop(
																				"checked",
																				true)
																		.trigger(
																				'click')
																		.prop(
																				"checked",
																				true);
															});
											$('#btn_orgBrnGroup_932')
													.click(
															function() {
																$(
																		"input[name="
																				+ crs_assignId
																				+ "].orgBrnGroup932")
																		.prop(
																				"checked",
																				true)
																		.trigger(
																				'click')
																		.prop(
																				"checked",
																				true);
															});
											$('input[type=radio][name=brType]')
													.filter("[value='S']")
													.trigger('click').prop(
															"checked", true)
													.trigger('change');
											$('select[name=rdo_typeCLS180R14]')
													.trigger('change');
											$("#yyyyMM_typeCLS180R14").val(
													obj.TODAYYEARMONTH);
											$("#baseDate_CLS180R15D").val(
													obj.baseDate_CLS180R15D
															|| '');
										}
										// 企金覆審報表相關
										if (true) {
											if (true) {
												var col_cnt = 2;
												$("#div_brType_LRS")
														.append(
																_branchList_div(
																		col_cnt,
																		lrs_assignId,
																		obj.branchItemOrder,
																		obj.branchItem)
																		.join(
																				"\n"));
											}

											$('#btn_lrs_branchList_1')
													.click(
															function() {
																$(
																		"input[name="
																				+ lrs_assignId
																				+ "]")
																		.prop(
																				'checked',
																				true);
															});
											$('#btn_lrs_branchList_0')
													.click(
															function() {
																$(
																		"input[name="
																				+ lrs_assignId
																				+ "]")
																		.prop(
																				'checked',
																				false);
															});
											$('#btn_lrs_branchList_2')
													.click(
															function() {
																$(
																		"#div_LRS_BY_DEFAULTCTLDATE")
																		.thickbox(
																				{
																					title : '',
																					width : 360,
																					height : 180,
																					modal : false,
																					align : 'center',
																					valign : 'bottom',
																					i18n : i18n.def,
																					readOnly : false,
																					buttons : {
																						"sure" : function() {
																							if ($(
																									"#form_LRS_BY_DEFAULTCTLDATE")
																									.valid()) {
																								var lrsByDefaultCTLDate = $(
																										"#lrsByDefaultCTLDate")
																										.val();
																								if (!lrsByDefaultCTLDate
																										.match(regYYYYMM)) {
																									// val.date2=日期格式錯誤(YYYY-MM)
																									return CommonAPI
																											.showMessage(i18n.def["val.date2"]);
																								}

																								$
																										.ajax({
																											type : "POST",
																											handler : "lms9511m01formhandler",
																											data : $
																													.extend(
																															$(
																																	"#form_LRS_BY_DEFAULTCTLDATE")
																																	.serializeData(),
																															{
																																formAction : "queryLrsBrNo"
																															}),
																											success : function(
																													json) {
																												$
																														.each(
																																json.matchBr,
																																function(
																																		idx,
																																		val) {
																																	$(
																																			"input[name="
																																					+ lrs_assignId
																																					+ "][value="
																																					+ val
																																					+ "]")
																																			.prop(
																																					'checked',
																																					true);
																																});
																												$.thickbox
																														.close();
																											}
																										});

																							}
																						},
																						"cancel" : function() {
																							$.thickbox
																									.close();
																						}
																					}
																				});
															});
										}
									}
								});
					}

					/**
					 * 登錄分行代號
					 */
					$("#selectBrNoBt").click(function() {
						CommonAPI.showAllBranch({
							//J-111-0583_05097_B1001 Web e-Loan企金授信提供各營運中心可自行列印轄下分行於指定期間內所簽報異常通報之「授信異常通報案件報送統計表」
							//營運中心只顯示轄下分行
							action : userInfo.unitType == "2" ? "queryBranchC140" : "queryAllBranch", 
							btnAction : function(a, b) {
								$("#selectBrNo").val(b.brNo);
								$.thickbox.close();
							}
						});
					});

					function _branchList_div(col_cnt, assignId,
							branchItemOrder, branchMap) {
						var arr_org_931 = [ "008", "010", "012", "015", "017",
								"021", "027", "030", "034", "041", "043",
								"056", "057", "069", "070", "079", "086",
								"206", "219", "231", "234", "235", "238", "240" ];
						var arr_org_932 = [ "005", "019", "023", "031", "036",
								"042", "046", "048", "049", "051", "055",
								"062", "067", "074", "202", "210", "216",
								"226", "228", "229", "237" ];

						var str_org_931 = arr_org_931.join("|");
						var str_org_932 = arr_org_932.join("|");
						var elmArr = [];
						$
								.each(
										branchItemOrder,
										function(idx, brNo) {
											var brName = branchMap[brNo];
											// 依 itemOrder, 一個一個append, 把 clear
											// 指為 false

											var crs__branchList_class = "";
											if (str_org_931.indexOf(brNo) >= 0) {
												crs__branchList_class = "orgBrnGroup931 ";
											} else if (str_org_932
													.indexOf(brNo) >= 0) {
												crs__branchList_class = "orgBrnGroup932 ";
											}

											var tdcol = {};
											tdcol['_a'] = "<label style='letter-spacing:0px;cursor:pointer;font-weight:normal;'><input value='"
													+ brNo
													+ "' id='"
													+ assignId
													+ "' name='"
													+ assignId
													+ "' type='checkbox' class='"
													+ crs__branchList_class
													+ " '>"
													+ brNo
													+ " "
													+ brName + "</label>";
											elmArr.push(tdcol);
										});
						// ===
						// 補empty col
						var addcnt = (col_cnt - (elmArr.length % col_cnt));
						if (addcnt == col_cnt) {
							addcnt = 0;
						}
						for ( var i = 0; i < addcnt; i++) {
							var tdcol = {};
							tdcol['_a'] = "&nbsp;";
							elmArr.push(tdcol);
						}

						var dyna = [];
						dyna
								.push("<table width='100%' border='0' cellspacing='0' cellpadding='0'>");
						dyna.push("<tr>");
						for ( var i = 0; i < elmArr.length; i++) {
							dyna.push("<td>" + elmArr[i]['_a'] + "</td>");
							if ((i + 1) % col_cnt == 0) {
								dyna.push("</tr><tr>");
							}
						}
						dyna.push("</tr>");
						dyna.push("</table>");
						return dyna;
					}
					// 把所有按鈕隱藏起來
					function hideBtn() {
						$("#btnView").hide();
						$("#btnLongError").hide();
						// $("#btnCreateReport").hide();
						$("#btnPrint").hide();
						$("#btnFilter").hide();
						$("#btnSendDocTypeReport").hide();
						$("#btnLongViewMemo").hide();
						$("#btnReturnPage").hide();
						$("#btnPullinReport").hide();
						$("#btnUpload").hide();
					}
					// 隱藏此人不該顯示的報表
					function hideSearchActionData(obj) {

						var isOpenUnsoldHouseLoanInfoFunction;
						$
								.ajax({
									handler : 'lmscommonformhandler',
									action : "isOpenUnsoldHouseLoanInfoFunction",
									async : false,
									data : {},
									success : function(obj) {
										isOpenUnsoldHouseLoanInfoFunction = obj.isOpenUnsoldHouseLoanInfoFunction;
									}
								});

						var unitNo = userInfo ? userInfo.unitNo : "";
						var unitType = userInfo ? userInfo.unitType : "";
						var selecter = $("select#searchAction2");
						if (unitNo == '007') {
							// 007：國外部
							// 企金
							$("select#searchAction1 option[value=LMS180R02A]")
									.remove();
							$("select#searchAction1 option[value=LMS180R11]")
									.remove();
							$("select#searchAction1 option[value=LMS180R12]")
									.remove();
							$("select#searchAction1 option[value=LMS180R13]")
									.remove();
							$("select#searchAction1 option[value=LMS180R14]")
									.remove();
							$("select#searchAction1 option[value=LMS180R14Q]")
									.remove();
							$("select#searchAction1 option[value=LMS180R15]")
									.remove();
							$("select#searchAction1 option[value=LMS180R16]")
									.remove();
							$("select#searchAction1 option[value=LMS180R17]")
									.remove();
							$("select#searchAction1 option[value=LMS180R18]")
									.remove();
							// $("select#searchAction1
							// option[value=LMS180R25]").remove();
							$("select#searchAction1 option[value=LMS180R26]")
									.remove();
							$("select#searchAction1 option[value=LMS180R27]")
									.remove();
							$("select#searchAction1 option[value=LMS180R29]")
									.remove();
							$("select#searchAction1 option[value=LMS180R30]")
									.remove();
							$("select#searchAction1 option[value=LMS180R32]")
									.remove();
							$("select#searchAction1 option[value=LMS180R33]")
									.remove();
							$("select#searchAction1 option[value=LMS180R34]")
									.remove();
							$("select#searchAction1 option[value=LMS180R35]")
									.remove();
							$("select#searchAction1 option[value=LMS180R36]")
									.remove();
							$("select#searchAction1 option[value=LMS180R37]")
									.remove();
							$("select#searchAction1 option[value=LMS180R38]")
									.remove();
							$("select#searchAction1 option[value=LMS180R39]")
									.remove();
							$("select#searchAction1 option[value=LMS180R40]")
									.remove();
							$("select#searchAction1 option[value=LMS180R41]")
									.remove();
							$("select#searchAction1 option[value=LMS180R42]")
									.remove();
							$("select#searchAction1 option[value=LMS180R42T]")
									.remove();
							$("select#searchAction1 option[value=LMS180R43]")
									.remove();
							$("select#searchAction1 option[value=LMS180R44]")
									.remove();
							$("select#searchAction1 option[value=LMS180R45]")
									.remove();
							$("select#searchAction1 option[value=LMS180R46]")
									.remove();
							$("select#searchAction1 option[value=LMS180R47]")
									.remove();
							$("select#searchAction1 option[value=LMS180R48]")
									.remove();
							$("select#searchAction1 option[value=LMS180R49]")
									.remove();
							$("select#searchAction1 option[value=LMS180R51]")
									.remove();
							// $("select#searchAction1
							// option[value=LMS180R52]").remove();
							$("select#searchAction1 option[value=LMS180R53]")
									.remove();
							$("select#searchAction1 option[value=LMS180R54]")
									.remove();
							$("select#searchAction1 option[value=LMS180R54T]")
									.remove();
							$("select#searchAction1 option[value=LMS180R55]")
									.remove();
							$("select#searchAction1 option[value=LMS180R56]")
									.remove();
							$("select#searchAction1 option[value=LMS180R57]")
									.remove();
							$("select#searchAction1 option[value=LMS180R58]")
									.remove();
							$("select#searchAction1 option[value=LMS180R59]")
									.remove();
							// $("select#searchAction1
							// option[value=LMS180R60]").remove();
							// $("select#searchAction1
							// option[value=LMS180R61]").remove();
							// $("select#searchAction1
							// option[value=LMS180R62]").remove();
							$("select#searchAction1 option[value=LMS180R63]")
									.remove();
							$("select#searchAction1 option[value=LMS180R64]")
									.remove();
							$("select#searchAction1 option[value=LMS180R65]")
									.remove();
							$("select#searchAction1 option[value=LMS180R66]")
									.remove();
							$("select#searchAction1 option[value=LMS180R67]")
									.remove();
							$("select#searchAction1 option[value=LMS180R68]")
									.remove();
							$("select#searchAction1 option[value=LMS180R70]")
									.remove();		
				//							$("select#searchAction1 option[value=LMS180R72]")
				//							.remove();
							// $("select#searchAction1
							// option[value=LMS180R69]").remove();

							$("select#searchAction1 option[value=LMS180R74]")
							.remove();
							$("select#searchAction1 option[value=LMS180R75]").remove();
				//							$("select#searchAction1 option[value=LMS180R76]")
				//							.remove();
							$("select#searchAction1 option[value=LMS180R77]").remove();
							
							// 個金 => 國外部開始承做消金簽案
							/*
								* selecter.find("option[value=CLS180R01]").remove();
								* selecter.find("option[value=CLS180R02]").remove();
								* selecter.find("option[value=CLS180R03]").remove();
								* selecter.find("option[value=CLS180R04]").remove();
								* selecter.find("option[value=CLS180R05]").remove();
								* selecter.find("option[value=CLS180R06]").remove();
								*/
							selecter.find("option[value=CLS180R07]").remove();
							selecter.find("option[value=CLS180R08]").remove();
							/*
								* selecter.find("option[value=CLS180R09]").remove();
								* selecter.find("option[value=CLS180R10]").remove();
								*/
							selecter.find("option[value=CLS180R11]").remove();
							selecter.find("option[value=CLS180R52]").remove();
							selecter.find("option[value=CLS180R53]").remove(); // 整批貸款明細表
							selecter.find("option[value=CLS180R54]").remove();
							selecter.find("option[value=CLS180R27]").remove();
							selecter.find("option[value=CLS180R27B]").remove();
							selecter.find("option[value=CLS180R27C]").remove();
							selecter.find("option[value=CLS180R27D]").remove();
							selecter.find("option[value=CLS180R59]").remove();
							selecter.find("option[value=CLS180R62]").remove();
						} else if (unitNo == '940') {
							// 940：授信行銷處
							// 企金
							$("select#searchAction1 option[value=LMS180R02A]")
									.remove();
							$("select#searchAction1 option[value=LMS180R13]")
									.remove();
							$("select#searchAction1 option[value=LMS180R14]")
									.remove();
							$("select#searchAction1 option[value=LMS180R14Q]")
									.remove();
							$("select#searchAction1 option[value=LMS180R15]")
									.remove();
							$("select#searchAction1 option[value=LMS180R27]")
									.remove();
							$("select#searchAction1 option[value=LMS180R29]")
									.remove();
							$("select#searchAction1 option[value=LMS180R30]")
									.remove();
							$("select#searchAction1 option[value=LMS180R32]")
									.remove();
							$("select#searchAction1 option[value=LMS180R39]")
									.remove();
							$("select#searchAction1 option[value=LMS180R47]")
									.remove();
							$("select#searchAction1 option[value=LMS180R48]")
									.remove();
							$("select#searchAction1 option[value=LMS180R53]")
									.remove();
							$("select#searchAction1 option[value=LMS180R57]")
									.remove();
							$("select#searchAction1 option[value=LMS180R58]")
									.remove();
							$("select#searchAction1 option[value=LMS180R64]")
									.remove();
							//J-111-0352_05097_B1001 Web e-Loan修改企金授信簽報案件經區域營運中心流程進度控管表及企金授權外案件流程控管表新增部分欄位
				//							$("select#searchAction1 option[value=LMS180R65]")
				//									.remove();
							$("select#searchAction1 option[value=LMS180R66]")
									.remove();
							// $("select#searchAction1
							// option[value=LMS180R67]").remove();
							
							//J-111-0352_05097_B1001 Web e-Loan修改企金授信簽報案件經區域營運中心流程進度控管表及企金授權外案件流程控管表新增部分欄位
				//							$("select#searchAction1 option[value=LMS180R68]")
				//									.remove();
							// $("select#searchAction1
							// option[value=LMS180R69]").remove();
							$("select#searchAction1 option[value=LMS180R70]")
									.remove();
				//							$("select#searchAction1 option[value=LMS180R72]")
				//							.remove();
				//							$("select#searchAction1 option[value=LMS180R76]")
				//							.remove();

							// 個金
							selecter.find("option[value=CLS180R01]").remove();
							selecter.find("option[value=CLS180R02]").remove();
							selecter.find("option[value=CLS180R03]").remove();
							selecter.find("option[value=CLS180R03B]").remove();
							selecter.find("option[value=CLS180R04]").remove();
							selecter.find("option[value=CLS180R06]").remove();
							selecter.find("option[value=CLS180R07]").remove();
							selecter.find("option[value=CLS180R08]").remove();
							selecter.find("option[value=CLS180R09]").remove();
							selecter.find("option[value=CLS180R10]").remove();
							selecter.find("option[value=CLS180R27C]").remove();
							selecter.find("option[value=CLS180R27D]").remove();
							selecter.find("option[value=CLS180R40]").remove();
							selecter.find("option[value=CLS180R41]").remove();
							selecter.find("option[value=CLS180R50]").remove();
							selecter.find("option[value=CLS180R51]").remove();
							selecter.find("option[value=CLS180R52]").remove();
							selecter.find("option[value=CLS180R53]").remove(); // 整批貸款明細表
							selecter.find("option[value=CLS180R54]").remove();
							selecter.find("option[value=CLS180R59]").remove();
							selecter.find("option[value=CLS180R62]").remove();
						} else if (unitNo == '942') {
							// 942：海外業務處
							$("select#searchAction1 option[value=LMS180R02A]")
									.remove();
							$("select#searchAction1 option[value=LMS180R15]")
									.remove();
							$("select#searchAction1 option[value=LMS180R19B]")
									.remove();
							$("select#searchAction1 option[value=LMS180R23B]")
									.remove();
							$("select#searchAction1 option[value=LMS180R30]")
									.remove();
							$("select#searchAction1 option[value=LMS180R31]")
									.remove();
							$("select#searchAction1 option[value=LMS180R32]")
									.remove();
							$("select#searchAction1 option[value=LMS180R50]")
									.remove();
							$("select#searchAction1 option[value=LMS180R52]")
									.remove();

							$("select#searchAction1 option[value=LMS180R01]")
									.remove();
							$("select#searchAction1 option[value=LMS161T02]")
									.remove();
							$("select#searchAction1 option[value=LMS180R05]")
									.remove();
							$("select#searchAction1 option[value=LMS180R10]")
									.remove();
							$("select#searchAction1 option[value=LMS180R11]")
									.remove();
							$("select#searchAction1 option[value=LMS180R12]")
									.remove();
							$("select#searchAction1 option[value=LMS180R13]")
									.remove();
							$("select#searchAction1 option[value=LMS180R14]")
									.remove();
							$("select#searchAction1 option[value=LMS180R14Q]")
									.remove();
							$("select#searchAction1 option[value=LMS180R16]")
									.remove();
							$("select#searchAction1 option[value=LMS180R17]")
									.remove();
							$("select#searchAction1 option[value=LMS180R18]")
									.remove();
							$("select#searchAction1 option[value=LMS180R25]")
									.remove();
							$("select#searchAction1 option[value=LMS180R26]")
									.remove();
							$("select#searchAction1 option[value=LMS180R27]")
									.remove();
							$("select#searchAction1 option[value=LMS180R29]")
									.remove();
							$("select#searchAction1 option[value=LMS180R33]")
									.remove();
							$("select#searchAction1 option[value=LMS180R34]")
									.remove();
							$("select#searchAction1 option[value=LMS180R35]")
									.remove();
							$("select#searchAction1 option[value=LMS180R36]")
									.remove();
							$("select#searchAction1 option[value=LMS180R37]")
									.remove();
							$("select#searchAction1 option[value=LMS180R38]")
									.remove();
							$("select#searchAction1 option[value=LMS180R39]")
									.remove();
							$("select#searchAction1 option[value=LMS180R40]")
									.remove();
							$("select#searchAction1 option[value=LMS180R41]")
									.remove();
							$("select#searchAction1 option[value=LMS180R42]")
									.remove();
							$("select#searchAction1 option[value=LMS180R42T]")
									.remove();
							$("select#searchAction1 option[value=LMS180R43]")
									.remove();
							$("select#searchAction1 option[value=LMS180R44]")
									.remove();
							$("select#searchAction1 option[value=LMS180R45]")
									.remove();
							$("select#searchAction1 option[value=LMS180R46]")
									.remove();
							$("select#searchAction1 option[value=LMS180R47]")
									.remove();
							$("select#searchAction1 option[value=LMS180R48]")
									.remove();
							$("select#searchAction1 option[value=LMS180R49]")
									.remove();
							$("select#searchAction1 option[value=LMS180R51]")
									.remove();
							$("select#searchAction1 option[value=LMS180R53]")
									.remove();
							$("select#searchAction1 option[value=LMS180R54]")
									.remove();
							$("select#searchAction1 option[value=LMS180R54T]")
									.remove();
							$("select#searchAction1 option[value=LMS180R55]")
									.remove();
							$("select#searchAction1 option[value=LMS180R56]")
									.remove();
							$("select#searchAction1 option[value=LMS180R57]")
									.remove();
							$("select#searchAction1 option[value=LMS180R58]")
									.remove();
							$("select#searchAction1 option[value=LMS180R59]")
									.remove();
							$("select#searchAction1 option[value=LMS180R60]")
									.remove();
							$("select#searchAction1 option[value=LMS180R61]")
									.remove();
							$("select#searchAction1 option[value=LMS180R62]")
									.remove();
							$("select#searchAction1 option[value=LMS180R63]")
									.remove();
							$("select#searchAction1 option[value=LMS180R64]")
									.remove();
							$("select#searchAction1 option[value=LMS180R65]")
									.remove();
							$("select#searchAction1 option[value=LMS180R66]")
									.remove();
							// $("select#searchAction1
							// option[value=LMS180R67]").remove();
							$("select#searchAction1 option[value=LMS180R68]")
									.remove();
							$("select#searchAction1 option[value=LMS180R70]")
									.remove();
							//J-113-0347 海業處增加BIS評估表
							//$("select#searchAction1 option[value=LMS180R72]").remove();
							
							$("select#searchAction1 option[value=LMS180R73]")
							.remove();
							$("select#searchAction1 option[value=LMS180R74]")
							.remove();

							$("select#searchAction1 option[value=LMS180R75]").remove();
							// $("select#searchAction1
							// option[value=LMS180R69]").remove();
							
				//							$("select#searchAction1 option[value=LMS180R76]")
				//							.remove();
							$("select#searchAction1 option[value=LMS180R77]").remove();

							// 個金
							selecter.find("option[value=CLS180R01]").remove();
							selecter.find("option[value=CLS180R02]").remove();
							selecter.find("option[value=CLS180R03]").remove();
							selecter.find("option[value=CLS180R03B]").remove();
							selecter.find("option[value=CLS180R04]").remove();
							selecter.find("option[value=CLS180R05]").remove();
							selecter.find("option[value=CLS180R06]").remove();
							selecter.find("option[value=CLS180R07]").remove();
							selecter.find("option[value=CLS180R08]").remove();
							selecter.find("option[value=CLS180R09]").remove();
							selecter.find("option[value=CLS180R10]").remove();
							selecter.find("option[value=CLS180R11]").remove();
							selecter.find("option[value=CLS250R01]").remove();
							selecter.find("option[value=CLS180R27C]").remove();
							selecter.find("option[value=CLS180R27D]").remove();
							selecter.find("option[value=CLS180R40]").remove();
							selecter.find("option[value=CLS180R41]").remove();
							selecter.find("option[value=CLS180R42]").remove();
							selecter.find("option[value=CLS180R50]").remove();
							selecter.find("option[value=CLS180R51]").remove();
							selecter.find("option[value=CLS180R52]").remove();
							// selecter.find("option[value=CLS180R53]").remove();
							// //整批貸款明細表
							selecter.find("option[value=CLS180R54]").remove();
							selecter.find("option[value=CLS180R59]").remove();
							selecter.find("option[value=CLS180R62]").remove();
						} else if (unitNo == '943') {
							// 943：消金業務處
							// 企金
							// 將 LMS180R 開頭的選項, 給移掉
							$("select#searchAction1 option[value^=LMS180R]")
									.remove();
							$("select#searchAction1 option[value=LMS161T02]")
									.remove(); // 授信契約產生主辦聯貸案一覽表
							$("select#searchAction1 option[value=LMS180R39]")
									.remove();
							$("select#searchAction1 option[value=LMS180R40]")
									.remove();
							$("select#searchAction1 option[value=LMS180R42]")
									.remove();
							$("select#searchAction1 option[value=LMS180R42T]")
									.remove();
							$("select#searchAction1 option[value=LMS180R43]")
									.remove();
							$("select#searchAction1 option[value=LMS180R44]")
									.remove();
							$("select#searchAction1 option[value=LMS180R45]")
									.remove();
							$("select#searchAction1 option[value=LMS180R46]")
									.remove();
							$("select#searchAction1 option[value=LMS180R47]")
									.remove();
							$("select#searchAction1 option[value=LMS180R49]")
									.remove();
							$("select#searchAction1 option[value=LMS180R51]")
									.remove();
							$("select#searchAction1 option[value=LMS180R53]")
									.remove();
							$("select#searchAction1 option[value=LMS180R54]")
									.remove();
							$("select#searchAction1 option[value=LMS180R54T]")
									.remove();
							$("select#searchAction1 option[value=LMS180R55]")
									.remove();
							$("select#searchAction1 option[value=LMS180R56]")
									.remove();
							$("select#searchAction1 option[value=LMS180R57]")
									.remove();
							$("select#searchAction1 option[value=LMS180R58]")
									.remove();
							$("select#searchAction1 option[value=LMS180R64]")
									.remove();
							$("select#searchAction1 option[value=LMS180R65]")
									.remove();
							$("select#searchAction1 option[value=LMS180R66]")
									.remove();
							$("select#searchAction1 option[value=LMS180R67]")
									.remove();
							$("select#searchAction1 option[value=LMS180R68]")
									.remove();
							$("select#searchAction1 option[value=LMS180R70]")
									.remove();
							$("select#searchAction1 option[value=LMS180R72]")
							.remove();
							$("select#searchAction1 option[value=LMS180R73]")
							.remove();
							$("select#searchAction1 option[value=LMS180R74]")
							.remove();
							$("select#searchAction1 option[value=LMS180R75]").remove();
				//							$("select#searchAction1 option[value=LMS180R76]")
				//							.remove();
							// $("select#searchAction1
							// option[value=LMS180R69]").remove();
							$("select#searchAction1 option[value=LMS180R77]").remove();

							// 個金
							selecter.find("option[value=CLS180R01]").remove(); // 已敘作消金案件清單
							selecter.find("option[value=CLS180R02]").remove(); // 敘做無自用住宅購屋放款明細表
							selecter.find("option[value=CLS180R03]").remove(); // 授權內分行敘做房屋貸款月報
							selecter.find("option[value=CLS180R03B]").remove();
							selecter.find("option[value=CLS180R04]").remove(); // 授權內已敘做個人消費金融業務月報表
							// CLS180R05 報案考核表被扣分清單
							selecter.find("option[value=CLS180R06]").remove(); // 審件統計表
							selecter.find("option[value=CLS180R07]").remove(); // 分行對保費明細表
							selecter.find("option[value=CLS180R08]").remove(); // 已收件待辦中案件報表
							selecter.find("option[value=CLS180R09]").remove(); // 婉拒案件明細表
							selecter.find("option[value=CLS180R10]").remove(); // 消金授信已逾期控制表
							selecter.find("option[value=CLS180R53]").remove(); // 整批貸款明細表
							selecter.find("option[value=CLS180R54]").remove();
						} else if (unitNo == '918' || unitNo == '916'
								|| unitNo == '900') {
							// S：企金部 授管處
							// 企金
							// J-108-0098-001
							// 企金處移除LMS180R12、LMS180R26振興經濟非中小企相關報表
							$("select#searchAction1 option[value=LMS180R01]")
									.remove();
							$("select#searchAction1 option[value=LMS180R02A]")
									.remove();
							$("select#searchAction1 option[value=LMS161T02]")
									.remove();
							$("select#searchAction1 option[value=LMS180R10]")
									.remove();
							$("select#searchAction1 option[value=LMS180R12]")
									.remove();
							$("select#searchAction1 option[value=LMS180R05]")
									.remove();
							$("select#searchAction1 option[value=LMS180R15]")
									.remove();
							$("select#searchAction1 option[value=LMS180R26]")
									.remove();
							$("select#searchAction1 option[value=LMS180R30]")
									.remove();
							$("select#searchAction1 option[value=LMS180R32]")
									.remove();
							$("select#searchAction1 option[value=LMS180R36]")
									.remove();
							$("select#searchAction1 option[value=LMS180R37]")
									.remove();
							$("select#searchAction1 option[value=LMS180R38]")
									.remove();
							$("select#searchAction1 option[value=LMS180R39]")
									.remove();
							$("select#searchAction1 option[value=LMS180R40]")
									.remove();
							$("select#searchAction1 option[value=LMS180R42]")
									.remove();
							$("select#searchAction1 option[value=LMS180R42T]")
									.remove();
							$("select#searchAction1 option[value=LMS180R43]")
									.remove();
							$("select#searchAction1 option[value=LMS180R44]")
									.remove();
							$("select#searchAction1 option[value=LMS180R45]")
									.remove();
							$("select#searchAction1 option[value=LMS180R46]")
									.remove();
							$("select#searchAction1 option[value=LMS180R49]")
									.remove();
							$("select#searchAction1 option[value=LMS180R51]")
									.remove();
							$("select#searchAction1 option[value=LMS180R54]")
									.remove();
							$("select#searchAction1 option[value=LMS180R54T]")
									.remove();
							$("select#searchAction1 option[value=LMS180R55]")
									.remove();
							$("select#searchAction1 option[value=LMS180R56]")
									.remove();
							$("select#searchAction1 option[value=LMS180R67]")
									.remove();
							// $("select#searchAction1
							// option[value=LMS180R69]").remove();
							$("select#searchAction1 option[value=LMS180R77]").remove();

							if (unitNo == '918' || unitNo == '900') {
								// J-108-0166 LMS180R47 企業社會責任貸放情形統計表

								if (!isOpenUnsoldHouseLoanInfoFunction) {
									$(
											"select#searchAction1 option[value=LMS180R57]")
											.remove();
								}
							} else {
								$(
										"select#searchAction1 option[value=LMS180R47]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R57]")
										.remove();
								$("select#searchAction1 option[value=LMS180R75]").remove();
							}
							$("select#searchAction1 option[value=LMS180R59]")
									.remove();
							// $("select#searchAction1
							// option[value=LMS180R60]").remove();
							// $("select#searchAction1
							// option[value=LMS180R61]").remove();
							// $("select#searchAction1
							// option[value=LMS180R62]").remove();
							// $("select#searchAction1
							// option[value=LMS180R63]").remove();
							
				//							$("select#searchAction1 option[value=LMS180R72]")
				//							.remove();
							
				//							$("select#searchAction1 option[value=LMS180R73]")
				//							.remove();
							
							$("select#searchAction1 option[value=LMS180R74]")
							.remove();
							
				//							$("select#searchAction1 option[value=LMS180R76]")
				//							.remove();
							
							// 個金
							if (unitNo == '900') {

							} else {
								selecter.find("option[value=CLS180R01]")
										.remove();
								selecter.find("option[value=CLS180R02]")
										.remove();
								selecter.find("option[value=CLS180R03]")
										.remove();
								selecter.find("option[value=CLS180R03B]")
										.remove();
								selecter.find("option[value=CLS180R04]")
										.remove();
								selecter.find("option[value=CLS180R27C]").remove();
								selecter.find("option[value=CLS180R27D]").remove();
								selecter.find("option[value=CLS180R59]").remove();
							}
							selecter.find("option[value=CLS180R06]").remove();
							selecter.find("option[value=CLS180R07]").remove();
							selecter.find("option[value=CLS180R08]").remove();
							selecter.find("option[value=CLS180R09]").remove();
							selecter.find("option[value=CLS180R10]").remove();
							selecter.find("option[value=CLS180R53]").remove(); // 整批貸款明細表
							if (unitNo == '918' || unitNo == '900') {
								// display 已核准尚未撥款案件報表 CLS180R11
							} else {
								selecter.find("option[value=CLS180R11]")
										.remove();
								selecter.find("option[value=CLS180R40]")
										.remove();
								selecter.find("option[value=CLS180R41]")
										.remove();
								// selecter.find("option[value=CLS180R42]").remove();
								selecter.find("option[value=CLS180R50]")
										.remove();
								selecter.find("option[value=CLS180R51]")
										.remove();
								selecter.find("option[value=CLS180R52]")
										.remove();
							}
							
							if(unitNo == '916'){
								$("select#searchAction1 option[value=LMS180R70]")
									.remove();
							}
							
						} else if (unitNo == '912') {
							// B：一般分行 J簡易型分行
							// 企金
							$("select#searchAction1 option[value=LMS180R02A]")
									.remove();
							// $("select#searchAction1
							// option[value=LMS161T02]").remove();先MARK掉
							// 正式上線要拿掉這個MARK //TODO
							$("select#searchAction1 option[value=LMS180R11]")
									.remove();
							$("select#searchAction1 option[value=LMS180R12]")
									.remove();
							$("select#searchAction1 option[value=LMS180R13]")
									.remove();
							$("select#searchAction1 option[value=LMS180R14]")
									.remove();
							$("select#searchAction1 option[value=LMS180R14Q]")
									.remove();
							$("select#searchAction1 option[value=LMS180R15]")
									.remove();
							$("select#searchAction1 option[value=LMS180R16]")
									.remove();
							$("select#searchAction1 option[value=LMS180R17]")
									.remove();
							$("select#searchAction1 option[value=LMS180R18]")
									.remove();
							// $("select#searchAction1
							// option[value=LMS180R25]").remove();
							$("select#searchAction1 option[value=LMS180R26]")
									.remove();
							$("select#searchAction1 option[value=LMS180R30]")
									.remove();
							$("select#searchAction1 option[value=LMS180R32]")
									.remove();
							$("select#searchAction1 option[value=LMS180R33]")
									.remove();
							$("select#searchAction1 option[value=LMS180R34]")
									.remove();
							$("select#searchAction1 option[value=LMS180R35]")
									.remove();
							$("select#searchAction1 option[value=LMS180R36]")
									.remove();
							$("select#searchAction1 option[value=LMS180R37]")
									.remove();
							$("select#searchAction1 option[value=LMS180R38]")
									.remove();
							$("select#searchAction1 option[value=LMS180R39]")
									.remove();
							$("select#searchAction1 option[value=LMS180R40]")
									.remove();
							$("select#searchAction1 option[value=LMS180R41]")
									.remove();
							$("select#searchAction1 option[value=LMS180R42]")
									.remove();
							$("select#searchAction1 option[value=LMS180R42T]")
									.remove();
							$("select#searchAction1 option[value=LMS180R43]")
									.remove();
							$("select#searchAction1 option[value=LMS180R44]")
									.remove();
							$("select#searchAction1 option[value=LMS180R45]")
									.remove();
							$("select#searchAction1 option[value=LMS180R46]")
									.remove();
							$("select#searchAction1 option[value=LMS180R47]")
									.remove();
							$("select#searchAction1 option[value=LMS180R48]")
									.remove();
							$("select#searchAction1 option[value=LMS180R49]")
									.remove();
							$("select#searchAction1 option[value=LMS180R51]")
									.remove();
							$("select#searchAction1 option[value=LMS180R52]")
									.remove();
							$("select#searchAction1 option[value=LMS180R53]")
									.remove();
							$("select#searchAction1 option[value=LMS180R54]")
									.remove();
							$("select#searchAction1 option[value=LMS180R54T]")
									.remove();
							$("select#searchAction1 option[value=LMS180R55]")
									.remove();
							$("select#searchAction1 option[value=LMS180R56]")
									.remove();
							$("select#searchAction1 option[value=LMS180R57]")
									.remove();
							$("select#searchAction1 option[value=LMS180R58]")
									.remove();
							$("select#searchAction1 option[value=LMS180R59]")
									.remove();
							$("select#searchAction1 option[value=LMS180R60]")
									.remove();
							$("select#searchAction1 option[value=LMS180R61]")
									.remove();
							$("select#searchAction1 option[value=LMS180R62]")
									.remove();
							$("select#searchAction1 option[value=LMS180R63]")
									.remove();
							$("select#searchAction1 option[value=LMS180R65]")
									.remove();
							$("select#searchAction1 option[value=LMS180R66]")
									.remove();
							$("select#searchAction1 option[value=LMS180R67]")
									.remove();
							$("select#searchAction1 option[value=LMS180R68]")
									.remove();
							$("select#searchAction1 option[value=LMS180R70]")
									.remove();	
				//							$("select#searchAction1 option[value=LMS180R72]")
				//							.remove();
							
							$("select#searchAction1 option[value=LMS180R73]")
							.remove();
							$("select#searchAction1 option[value=LMS180R74]")
							.remove();
							$("select#searchAction1 option[value=LMS180R75]").remove();
							// $("select#searchAction1
							// option[value=LMS180R69]").remove();
				//							$("select#searchAction1 option[value=LMS180R76]")
				//							.remove();
							$("select#searchAction1 option[value=LMS180R77]").remove();
							
							// 個金
							selecter.find("option[value=CLS180R07]").remove();
							selecter.find("option[value=CLS180R08]").remove();
							selecter.find("option[value=CLS180R11]").remove();
							selecter.find("option[value=CLS180R27C]").remove();
							selecter.find("option[value=CLS180R27D]").remove();
							selecter.find("option[value=CLS180R52]").remove();
							selecter.find("option[value=CLS180R53]").remove(); // 整批貸款明細表
							selecter.find("option[value=CLS180R54]").remove();
							selecter.find("option[value=CLS180R59]").remove();
							selecter.find("option[value=CLS180R62]").remove();
						} else if (unitNo == '938') {
							// 938：業務管理處
							// 企金
							$("select#searchAction1 option[value=LMS180R02A]")
									.remove();
							$("select#searchAction1 option[value=LMS180R11]")
									.remove();
							$("select#searchAction1 option[value=LMS180R12]")
									.remove();
							$("select#searchAction1 option[value=LMS180R13]")
									.remove();
							$("select#searchAction1 option[value=LMS180R14]")
									.remove();
							$("select#searchAction1 option[value=LMS180R14Q]")
									.remove();
							$("select#searchAction1 option[value=LMS180R15]")
									.remove();
							$("select#searchAction1 option[value=LMS180R16]")
									.remove();
							$("select#searchAction1 option[value=LMS180R17]")
									.remove();
							$("select#searchAction1 option[value=LMS180R18]")
									.remove();
							// $("select#searchAction1
							// option[value=LMS180R25]").remove();
							$("select#searchAction1 option[value=LMS180R26]")
									.remove();
							$("select#searchAction1 option[value=LMS180R27]")
									.remove();
							$("select#searchAction1 option[value=LMS180R29]")
									.remove();
							$("select#searchAction1 option[value=LMS180R30]")
									.remove();
							$("select#searchAction1 option[value=LMS180R32]")
									.remove();
							$("select#searchAction1 option[value=LMS180R33]")
									.remove();
							$("select#searchAction1 option[value=LMS180R34]")
									.remove();
							$("select#searchAction1 option[value=LMS180R35]")
									.remove();
							$("select#searchAction1 option[value=LMS180R36]")
									.remove();
							$("select#searchAction1 option[value=LMS180R37]")
									.remove();
							$("select#searchAction1 option[value=LMS180R38]")
									.remove();
							$("select#searchAction1 option[value=LMS180R39]")
									.remove();
							$("select#searchAction1 option[value=LMS180R40]")
									.remove();
							$("select#searchAction1 option[value=LMS180R41]")
									.remove();
							$("select#searchAction1 option[value=LMS180R42]")
									.remove();
							$("select#searchAction1 option[value=LMS180R42T]")
									.remove();
							$("select#searchAction1 option[value=LMS180R43]")
									.remove();
							$("select#searchAction1 option[value=LMS180R44]")
									.remove();
							$("select#searchAction1 option[value=LMS180R45]")
									.remove();
							$("select#searchAction1 option[value=LMS180R46]")
									.remove();
							$("select#searchAction1 option[value=LMS180R47]")
									.remove();
							$("select#searchAction1 option[value=LMS180R48]")
									.remove();
							$("select#searchAction1 option[value=LMS180R49]")
									.remove();
							$("select#searchAction1 option[value=LMS180R51]")
									.remove();
							// $("select#searchAction1
							// option[value=LMS180R52]").remove();
							$("select#searchAction1 option[value=LMS180R53]")
									.remove();
							$("select#searchAction1 option[value=LMS180R54]")
									.remove();
							$("select#searchAction1 option[value=LMS180R54T]")
									.remove();
							$("select#searchAction1 option[value=LMS180R55]")
									.remove();
							$("select#searchAction1 option[value=LMS180R56]")
									.remove();
							$("select#searchAction1 option[value=LMS180R57]")
									.remove();
							$("select#searchAction1 option[value=LMS180R58]")
									.remove();
							$("select#searchAction1 option[value=LMS180R59]")
									.remove();
							// $("select#searchAction1
							// option[value=LMS180R60]").remove();
							// $("select#searchAction1
							// option[value=LMS180R61]").remove();
							// $("select#searchAction1
							// option[value=LMS180R62]").remove();
							$("select#searchAction1 option[value=LMS180R63]")
									.remove();
							$("select#searchAction1 option[value=LMS180R64]")
									.remove();
							$("select#searchAction1 option[value=LMS180R65]")
									.remove();
							// $("select#searchAction1
							// option[value=LMS180R66]").remove();
							$("select#searchAction1 option[value=LMS180R67]")
									.remove();
							$("select#searchAction1 option[value=LMS180R68]")
									.remove();
							$("select#searchAction1 option[value=LMS180R70]")
									.remove();
							$("select#searchAction1 option[value=LMS180R72]")
							.remove();
							
							$("select#searchAction1 option[value=LMS180R73]")
							.remove();
							$("select#searchAction1 option[value=LMS180R74]")
							.remove();
							$("select#searchAction1 option[value=LMS180R75]").remove();
				//							$("select#searchAction1 option[value=LMS180R76]")
				//							.remove();
							// $("select#searchAction1
							// option[value=LMS180R69]").remove();
							$("select#searchAction1 option[value=LMS180R77]").remove();
							// 個金 => 國外部開始承做消金簽案
							/*
								* selecter.find("option[value=CLS180R01]").remove();
								* selecter.find("option[value=CLS180R02]").remove();
								* selecter.find("option[value=CLS180R03]").remove();
								* selecter.find("option[value=CLS180R04]").remove();
								* selecter.find("option[value=CLS180R05]").remove();
								* selecter.find("option[value=CLS180R06]").remove();
								*/
							selecter.find("option[value=CLS180R07]").remove();
							selecter.find("option[value=CLS180R08]").remove();
							/*
								* selecter.find("option[value=CLS180R09]").remove();
								* selecter.find("option[value=CLS180R10]").remove();
								*/
							selecter.find("option[value=CLS180R11]").remove();
							selecter.find("option[value=CLS180R27C]").remove();
							selecter.find("option[value=CLS180R27D]").remove();
							selecter.find("option[value=CLS180R52]").remove();
							selecter.find("option[value=CLS180R53]").remove(); // 整批貸款明細表
							selecter.find("option[value=CLS180R54]").remove();
							selecter.find("option[value=CLS180R59]").remove();
							selecter.find("option[value=CLS180R62]").remove();
						} else if (unitNo == '109') {// 信用卡暨支付處(電銷人員亦屬旗下)
							// J-112-0222 因應卡處旗下電銷人員需求，讓該單位能查詢"CLS180R27C 歡喜信貸KYC分案報表"，其餘報表不需要
							$("select#searchAction1").find("option").filter(function() {// 企金報表
								if (this.value !== "") {
									$("select#searchAction1").find("option[value=" + this.value + "]").remove();
								}
							});
							selecter.find("option").filter(function() {// 個金報表
								if (this.value !== "CLS180R27C" && this.value !== "") {
									selecter.find("option[value=" + this.value + "]").remove();
								}
							});
						} else if (unitType == '2') {
							/*
								* 在 AbstractEloanPage.java 裡
								* userInfo.put("unitType",
								* UnitTypeEnum.convertToUnitType(user.getUnitType()).getCode());
								* 
								* [Server端]MegaSSOUserDetails.getUnitType() 的值 vs
								* [JS端] userInfo.unitType
								*/

							// A：區域授信中心 營運中心
							// 企金
							$("select#searchAction1 option[value=LMS180R01]")
									.remove();
							$("select#searchAction1 option[value=LMS161T02]")
									.remove();
							$("select#searchAction1 option[value=LMS180R05]")
									.remove();
							$("select#searchAction1 option[value=LMS180R10]")
									.remove();
							$("select#searchAction1 option[value=LMS180R11]")
									.remove();
							$("select#searchAction1 option[value=LMS180R12]")
									.remove();
							$("select#searchAction1 option[value=LMS180R13]")
									.remove();
							$("select#searchAction1 option[value=LMS180R14]")
									.remove();
							$("select#searchAction1 option[value=LMS180R14Q]")
									.remove();
							$("select#searchAction1 option[value=LMS180R16]")
									.remove();
							$("select#searchAction1 option[value=LMS180R17]")
									.remove();
							$("select#searchAction1 option[value=LMS180R18]")
									.remove();
							// $("select#searchAction1
							// option[value=LMS180R25]").remove();
							$("select#searchAction1 option[value=LMS180R26]")
									.remove();
							
							//J-111-0583_05097_B1001 Web e-Loan企金授信提供各營運中心可自行列印轄下分行於指定期間內所簽報異常通報之「授信異常通報案件報送統計表」
				//							$("select#searchAction1 option[value=LMS180R27]")
				//									.remove();
							$("select#searchAction1 option[value=LMS180R29]")
									.remove();
							$("select#searchAction1 option[value=LMS180R33]")
									.remove();
							$("select#searchAction1 option[value=LMS180R34]")
									.remove();
							$("select#searchAction1 option[value=LMS180R35]")
									.remove();
							$("select#searchAction1 option[value=LMS180R36]")
									.remove();
							$("select#searchAction1 option[value=LMS180R37]")
									.remove();
							$("select#searchAction1 option[value=LMS180R38]")
									.remove();
							$("select#searchAction1 option[value=LMS180R39]")
									.remove();
							$("select#searchAction1 option[value=LMS180R40]")
									.remove();
							$("select#searchAction1 option[value=LMS180R41]")
									.remove();
							$("select#searchAction1 option[value=LMS180R42]")
									.remove();
							$("select#searchAction1 option[value=LMS180R42T]")
									.remove();
							$("select#searchAction1 option[value=LMS180R43]")
									.remove();
							$("select#searchAction1 option[value=LMS180R44]")
									.remove();
							$("select#searchAction1 option[value=LMS180R45]")
									.remove();
							$("select#searchAction1 option[value=LMS180R46]")
									.remove();
							$("select#searchAction1 option[value=LMS180R47]")
									.remove();
							$("select#searchAction1 option[value=LMS180R48]")
									.remove();
							$("select#searchAction1 option[value=LMS180R49]")
									.remove();
							$("select#searchAction1 option[value=LMS180R51]")
									.remove();
							$("select#searchAction1 option[value=LMS180R53]")
									.remove();
							$("select#searchAction1 option[value=LMS180R54]")
									.remove();
							$("select#searchAction1 option[value=LMS180R54T]")
									.remove();
							$("select#searchAction1 option[value=LMS180R55]")
									.remove();
							$("select#searchAction1 option[value=LMS180R56]")
									.remove();
							$("select#searchAction1 option[value=LMS180R57]")
									.remove();
							$("select#searchAction1 option[value=LMS180R58]")
									.remove();
							$("select#searchAction1 option[value=LMS180R59]")
									.remove();
							// $("select#searchAction1
							// option[value=LMS180R60]").remove();
							// $("select#searchAction1
							// option[value=LMS180R61]").remove();
							// $("select#searchAction1
							// option[value=LMS180R62]").remove();
							$("select#searchAction1 option[value=LMS180R63]")
									.remove();
							$("select#searchAction1 option[value=LMS180R64]")
									.remove();
							$("select#searchAction1 option[value=LMS180R65]")
									.remove();
							$("select#searchAction1 option[value=LMS180R66]")
									.remove();
							$("select#searchAction1 option[value=LMS180R67]")
									.remove();
							$("select#searchAction1 option[value=LMS180R70]")
									.remove();
							$("select#searchAction1 option[value=LMS180R72]")
							.remove();
							$("select#searchAction1 option[value=LMS180R73]")
							.remove();
							$("select#searchAction1 option[value=LMS180R74]")
							.remove();
							$("select#searchAction1 option[value=LMS180R75]").remove();
				//							$("select#searchAction1 option[value=LMS180R76]")
				//							.remove();
							$("select#searchAction1 option[value=LMS180R77]").remove();
							
							// 個金
							selecter.find("option[value=CLS180R01]").remove();
							selecter.find("option[value=CLS180R02]").remove();
							selecter.find("option[value=CLS180R03]").remove();
							selecter.find("option[value=CLS180R03B]").remove();
							selecter.find("option[value=CLS180R04]").remove();
							selecter.find("option[value=CLS180R05]").remove();
							selecter.find("option[value=CLS180R06]").remove();
							selecter.find("option[value=CLS180R07]").remove();
							selecter.find("option[value=CLS180R08]").remove();
							selecter.find("option[value=CLS180R09]").remove();
							selecter.find("option[value=CLS180R10]").remove();
							selecter.find("option[value=CLS180R11]").remove();
							selecter.find("option[value=CLS250R01]").remove();
							selecter.find("option[value=CLS180R27C]").remove();
							selecter.find("option[value=CLS180R27D]").remove();
							selecter.find("option[value=CLS180R40]").remove();
							selecter.find("option[value=CLS180R41]").remove();
							selecter.find("option[value=CLS180R42]").remove();
							selecter.find("option[value=CLS180R50]").remove();
							selecter.find("option[value=CLS180R51]").remove();
							selecter.find("option[value=CLS180R52]").remove();
							selecter.find("option[value=CLS180R53]").remove(); // 整批貸款明細表
							selecter.find("option[value=CLS180R59]").remove();
							selecter.find("option[value=CLS180R62]").remove();
						} else if (unitType == '1' || unitType == '5') {

							if (userInfo.isOverSea) {
								// 海外分行
								$(
										"select#searchAction1 option[value=LMS180R01]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS161T02]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R05]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R10]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R02A]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R11]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R12]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R13]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R14]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R14Q]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R15]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R16]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R17]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R18]")
										.remove();
								// $("select#searchAction1
								// option[value=LMS180R25]").remove();
								$(
										"select#searchAction1 option[value=LMS180R26]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R27]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R29]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R30]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R32]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R33]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R34]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R35]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R36]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R37]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R38]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R39]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R40]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R41]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R42]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R42T]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R43]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R44]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R45]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R46]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R47]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R48]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R49]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R51]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R52]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R53]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R54]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R54T]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R55]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R56]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R57]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R58]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R59]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R60]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R61]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R62]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R63]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R64]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R65]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R66]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R67]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R68]")
										.remove();
										
									$("select#searchAction1 option[value=LMS180R70]")
									.remove();
								//$("select#searchAction1 option[value=LMS180R72]")
								//	.remove();
								$("select#searchAction1 option[value=LMS180R73]")
								.remove();
								$("select#searchAction1 option[value=LMS180R74]")
								.remove();
								$("select#searchAction1 option[value=LMS180R75]").remove();
								// $("select#searchAction1
								// option[value=LMS180R69]").remove();
				//								$("select#searchAction1 option[value=LMS180R76]")
				//								.remove();
								$("select#searchAction1 option[value=LMS180R77]").remove();
								

								selecter.find("option[value=CLS180R01]")
										.remove();
								selecter.find("option[value=CLS180R02]")
										.remove();
								selecter.find("option[value=CLS180R03]")
										.remove();
								selecter.find("option[value=CLS180R03B]")
										.remove();
								selecter.find("option[value=CLS180R04]")
										.remove();
								selecter.find("option[value=CLS180R05]")
										.remove();
								selecter.find("option[value=CLS180R06]")
										.remove();
								selecter.find("option[value=CLS180R07]")
										.remove();
								selecter.find("option[value=CLS180R08]")
										.remove();
								selecter.find("option[value=CLS180R09]")
										.remove();
								selecter.find("option[value=CLS180R10]")
										.remove();
								selecter.find("option[value=CLS180R11]")
										.remove();
								selecter.find("option[value=CLS250R01]")
										.remove();
								selecter.find("option[value=CLS180R17]")
										.remove();
								selecter.find("option[value=CLS180R27C]")
										.remove();
								selecter.find("option[value=CLS180R27D]")
										.remove();
								selecter.find("option[value=CLS180R52]")
										.remove();
								selecter.find("option[value=CLS180R54]")
										.remove();
								selecter.find("option[value=CLS180R59]").remove();
								selecter.find("option[value=CLS180R62]").remove();
								if (unitNo == '0A7' || unitNo == '0A8') {
									// J-110-0211_11557_B1002
									// 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
									// 0A7東京分行、0A8大阪分行要能看到此報表

								} else {
									selecter.find("option[value=CLS180R53]")
											.remove(); // 整批貸款明細表
								}
							} else {

								// 國內分行
								// B：一般分行 J簡易型分行
								// 企金
								$(
										"select#searchAction1 option[value=LMS180R02A]")
										.remove();
								// $("select#searchAction1
								// option[value=LMS161T02]").remove();先MARK掉
								// 正式上線要拿掉這個MARK //TODO
								$(
										"select#searchAction1 option[value=LMS180R11]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R12]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R13]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R14]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R14Q]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R15]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R16]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R17]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R18]")
										.remove();

								// J-107-0192_05097_B1001
								// 針對企金異常通報戶之覆審，增加覆審名單之控管措施與報表。
								if (unitType == '5') {
									// 國金部 金控總部要能開放
								} else {
									$(
											"select#searchAction1 option[value=LMS180R25]")
											.remove();
								}

								$(
										"select#searchAction1 option[value=LMS180R26]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R27]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R29]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R30]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R32]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R33]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R34]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R35]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R36]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R37]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R38]")
										.remove();

								// J-107-0249 共同行銷報表
								if (unitNo == '243') {
									// 243：投資處要能開放
								} else {
									$(
											"select#searchAction1 option[value=LMS180R39]")
											.remove();
								}

								$(
										"select#searchAction1 option[value=LMS180R40]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R41]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R42]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R42T]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R43]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R44]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R45]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R46]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R47]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R48]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R49]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R51]")
										.remove();
								// $("select#searchAction1
								// option[value=LMS180R52]").remove();
								$(
										"select#searchAction1 option[value=LMS180R53]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R54]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R54T]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R55]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R56]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R57]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R58]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R59]")
										.remove();
								// $("select#searchAction1
								// option[value=LMS180R60]").remove();
								// $("select#searchAction1
								// option[value=LMS180R61]").remove();
								// $("select#searchAction1
								// option[value=LMS180R62]").remove();
								$(
										"select#searchAction1 option[value=LMS180R63]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R64]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R65]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R66]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R67]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R68]")
										.remove();
								$("select#searchAction1 option[value=LMS180R70]")
									.remove();
				//								$("select#searchAction1 option[value=LMS180R72]")
				//								.remove();
								$("select#searchAction1 option[value=LMS180R73]")
								.remove();
								// $("select#searchAction1
								// option[value=LMS180R69]").remove();
								$("select#searchAction1 option[value=LMS180R74]")
								.remove();
								$("select#searchAction1 option[value=LMS180R75]").remove();
				//								$("select#searchAction1 option[value=LMS180R76]")
				//								.remove();
								$("select#searchAction1 option[value=LMS180R77]").remove();

								// 個金
								selecter.find("option[value=CLS180R07]")
										.remove();
								selecter.find("option[value=CLS180R08]")
										.remove();
								selecter.find("option[value=CLS180R11]")
										.remove();
								selecter.find("option[value=CLS180R52]")
										.remove();
								selecter.find("option[value=CLS180R54]")
										.remove();
								selecter.find("option[value=CLS180R27]")
										.remove();
								selecter.find("option[value=CLS180R27B]")
										.remove();
								selecter.find("option[value=CLS180R27C]")
										.remove();
								selecter.find("option[value=CLS180R27D]")
										.remove();
								selecter.find("option[value=CLS180R59]").remove();
								selecter.find("option[value=CLS180R62]").remove();
								if (unitNo == '243') {
									// 243：投資處要能開放
									selecter.find("option[value=CLS180R40]")
											.remove();
									selecter.find("option[value=CLS180R41]")
											.remove();
									selecter.find("option[value=CLS180R42]")
											.remove();
									selecter.find("option[value=CLS180R50]")
											.remove();
									selecter.find("option[value=CLS180R51]")
											.remove();
								}

								if (unitNo == '942') {
									// J-110-0211_11557_B1002
									// 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
									// 942海業管理處要能看到此報表

								} else {
									selecter.find("option[value=CLS180R53]")
											.remove(); // 整批貸款明細表
								}
							}

						} else {
							// J-107-0249 共同行銷報表
							if (unitNo == '243') {
								// 243：投資處要能開放 LMS180R39
								$(
										"select#searchAction1 option[value=LMS180R46]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R47]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R51]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R53]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R57]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R59]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R60]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R61]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R62]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R63]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R65]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R66]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R67]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R68]")
										.remove();
								// $("select#searchAction1
								// option[value=LMS180R69]").remove();
								$("select#searchAction1 option[value=LMS180R75]").remove();

							} else {
								$(
										"select#searchAction1 option[value=LMS180R39]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R40]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R42]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R42T]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R43]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R44]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R45]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R46]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R47]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R49]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R51]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R53]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R54]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R54T]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R55]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R56]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R57]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R58]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R59]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R60]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R61]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R62]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R63]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R65]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R66]")
										.remove();
								$(
										"select#searchAction1 option[value=LMS180R68]")
										.remove();
								// $("select#searchAction1
								// option[value=LMS180R69]").remove();
								$("select#searchAction1 option[value=LMS180R74]")
								.remove();
								$("select#searchAction1 option[value=LMS180R75]").remove();
								$("select#searchAction1 option[value=LMS180R77]").remove();
							}
							selecter.find("option[value=CLS180R27C]").remove();
							selecter.find("option[value=CLS180R27D]").remove();
							selecter.find("option[value=CLS180R53]").remove(); // 整批貸款明細表
							selecter.find("option[value=CLS180R54]").remove();
							selecter.find("option[value=CLS180R62]").remove();
						}
						// J-108-0098-002 移除LMS180R12、LMS180R26振興經濟非中小企相關報表
						$("select#searchAction1 option[value=LMS180R12]")
								.remove();
						$("select#searchAction1 option[value=LMS180R26]")
								.remove();
					}
				})
// ================================ END DOCUMENT READY
// ====================================

// ================================ START BUTTON SETTING
// ====================================
$("#btnRPTcompare").click(function() {
	showCompareBox();
})
button
		.find("#btnPullinReport")
		.click(function() {
			var rptNo = $("#reportNo").val();
			// 7. 常董會及申報案件統計表
			if (rptNo == 'LMS180R16') {
				execLMS180R16Data();
			}
		})
		.end()
		.find("#btnFilter")
		.click(function() {
			showL180R02AThickBox();
		})
		.end()
		.find("#btnCreateReport")
		.click(function() {
			showThickBox();
		})
		.end()
		.find("#btnLongError")
		.click(function() {
			saveLogError();
		})
		.end()
		.find("#btnLongViewMemo")
		.click(
				function() {
					var rptNo = $("#reportNo").val();
					if (rptNo == 'LMS180R01') {
						showL180R01Detail();
					} else if (rptNo == 'LMS180R02A') {
						if ($("#deep").val() == '1') {
							var datas = [];
							var gridview = $("#LMS180R02ADetail1Grid");
							var ids = gridview.getGridParam('selarrrow');
							for ( var i in ids) {
								datas.push(gridview.getRowData(ids[i]).oid);
							}
							if (datas.length == 0) {
								return CommonAPI
										.showMessage(i18n.lms9511v01["L784S01A.error1"]);
							}
							$.ajax({
								handler : "lms9511m01formhandler",
								action : 'remarkMemo',
								data : {
									oids : datas
								},
								success : function(responseData) {
									gridview.trigger("reloadGrid");
								}
							});
						} else if ($("#deep").val() == '2') {
							showL180R02ADetail();
						}
					} else if (rptNo == "CLS180R01" || rptNo == "CLS180R02"
							|| rptNo == "CLS180R03" || rptNo == "CLS180R03B"
							|| rptNo == "CLS180R04") {
						var data = fileGrid.getRowData(1);
						if (data.cfrmFlag == "N") {
							MegaApi
									.confirmMessage(
											i18n.lms9511v01["changeFlag"],
											function(action) {
												if (action) {
													$
															.ajax({
																handler : "lms9511m01formhandler",
																action : 'clsAccept',
																data : {
																	mainIdTmp : data.mainId
																},
																success : function(
																		responseData) {
																	fileGrid
																			.reload();
																	MegaApi
																			.showPopMessage(
																					i18n.def["confirmTitle"],
																					i18n.def["runSuccess"]);
																}
															});
												}
											});
						} else {
							$.ajax({
								handler : "lms9511m01formhandler",
								action : 'clsAccept',
								data : {
									mainIdTmp : data.mainId
								},
								success : function(responseData) {
									fileGrid.reload();
									MegaApi.showPopMessage(
											i18n.def["confirmTitle"],
											i18n.def["runSuccess"]);
								}
							});
						}

					}
				})
		.end()
		.find("#btnView")
		.click(function() {
			if (docType == '1')
				showLMS180R16Detail();
			else {
				var selRow = fileGrid.getGridParam('selrow');
				if (selRow)
					download(null, null, fileGrid.getRowData(selRow));
				else
					return CommonAPI.showMessage(i18n.def["grid_selector"]);
			}
		})
		.end()
		.find("#btnUpload")
		.click(
				function() {
					var selRow = fileGrid.getSingleData();
					if (selRow) {
						if (selRow.cfrmFlag == "Y")
							return CommonAPI
									.showErrorMessage(i18n.lms9511v01["uploadError"]);
						var rptNo = $("#reportNo").val();
						var limitFileSize = 3145728;
						MegaApi.uploadDialog({
							handler : "lms9511fileuploadhandler",
							fieldId : "rpt",
							fieldIdHtml : "size='30'",
							fileDescId : "fileDesc",
							fileDescHtml : "size='30' maxlength='30'",
							limitSize : limitFileSize,
							width : 320,
							height : 190,
							data : {
								sysId : "LMS",
								oid : selRow.oid,
								fileName : selRow.rptName
							},
							success : function(response) {
								// if(rptNo=="CLS180R01" || rptNo=="CLS180R02"
								// || rptNo=="CLS180R03" || rptNo=="CLS180R04"){
								// $.ajax({
								// handler : "lms9511m01formhandler",
								// action : 'dissend',
								// data : {
								// mainIdTmp:selRow.mainId
								// },
								// success:function(responseData){
								// fileGrid.reload();
								// MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["fileUploadSuccess"]);
								// }
								// });
								// }else{
								fileGrid.reload();
								MegaApi.showPopMessage(
										i18n.def["confirmTitle"],
										i18n.def["fileUploadSuccess"]);
								// }
							}
						});
					}
				})
		.end()
		.find("#btnReturnPage")
		.click(function() {
			if (docType == '1') {
				if (userInfo.unitType != '2' && $("#deep").val() == '2') {
					$("#btnPrint").hide();
					$("#" + DOMPurify.sanitize($("#reportNo").val()) + "GridShow").hide();
					$("#btnLongError").hide();
					$("#btnFilter").hide();
					$("#LMS180R02ADetail1GridShow").show();
					$("#deep").val('1');
					$("#mainId").val($("#mainId918").val());
					$("#areaBranchId").val('');
				} else if ($("#deep").val() == '1') {
					$("#LMS180R02ADetail1GridShow").hide();
					hideBtn(DOMPurify.sanitize($("#reportNo").val()) + "GridShow");
				} else {
					hideBtn(DOMPurify.sanitize($("#reportNo").val()) + "GridShow");
				}
			} else {
				hideBtn("fileGridView");
			}
		})
		.end()
		.find("#btnPrint")
		.click(function() {

			if ($("#reportNo").val() == 'LMS180R02A') {
				doPrintLMS180R02A();
			} else if ($("#reportNo").val() == 'LMS180R15') {
				doPrint($("#reportNo").val());
			} else {
				doPrint($("#reportNo").val());
			}
		})
		.end()
		.find("#btnSendDocTypeReport")
		.click(
				function() {
					// 傳送授管處
					var reportNo = $("#reportNo").val();
					var gridview;
					var datas = [];
					var data;
					var oidP = "";
					if (docType == '1') {
						if (reportNo == 'LMS180R02A') {
							gridview = $("#LMS180R02AGrid");
							// var ids = gridview.getGridParam('selarrrow');
							// for (var i in ids) {
							// datas.push(gridview.getRowData(ids[i]).oid);
							// }
							// if (datas.length == 0) {
							// return
							// CommonAPI.showMessage(i18n.lms9511v01["L784S01A.error1"]);
							// }
						} else if (reportNo == 'LMS180R01') {
							gridview = $("#LMS180R01Grid");
							var ids = gridview.getGridParam('selrow');
							if (!ids) { // grid_selector=請選擇資料
								return CommonAPI
										.showMessage(i18n.def["grid_selector"]);
							}
							data = gridview.getRowData(ids);
							oidP = data.oid;
						} else if (reportNo == 'LMS180R15') {
							gridview = $("#LMS180R15Grid");
							var ids = gridview.getGridParam('selrow');
							if (!ids) { // grid_selector=請選擇資料
								return CommonAPI
										.showMessage(i18n.def["grid_selector"]);
							}
							data = gridview.getRowData(ids);
							oidP = data.oid;
						}

					} else {
						gridview = fileGrid;
					}
					$
							.ajax({
								handler : "lms9511m01formhandler",
								action : "sendDocTypeReport",
								data : {
									// oids: datas,
									// oid: oidP,
									mainId : $("#mainId").val(),
									rptNo : reportNo,
									noOpenDoc : true
								},
								success : function(obj) {
									if (obj.sendTime) {
										// 此報表已傳送過
										return CommonAPI
												.showMessage(i18n.lms9511v01["L784S01A.haveSended"]);
									}
									// 更新Grid內容
									gridview.trigger("reloadGrid");
									if (reportNo == 'LMS180R02A') {
										if (obj.execSendTime) {
											$("#searchActionName")
													.val(
															$(
																	"#searchActionName2")
																	.val()
																	+ i18n.lms9511v01["L180R02A.sendTimeY"]);
										} else {
											$("#searchActionName")
													.val(
															$(
																	"#searchActionName2")
																	.val()
																	+ i18n.lms9511v01["L180R02A.sendTimeN"]);
										}
									} else if (reportNo == 'LMS180R01') {
										$("#L9511v01Grid01").trigger(
												"reloadGrid");
									} else if (reportNo == 'LMS180R15') {
										$("#L9511v15Grid01").trigger(
												"reloadGrid");
									}
								}
							});
				});
// ================================ END BUTTON SETTING
// ====================================
// ================================ START ELEMENT SETTING
// ====================================
// Vector done
$("input[id=docType]").change(function() {
	docType = DOMPurify.sanitize($("input[name=docType]:checked").val());
	$("tr#searchSelects").find("select").hide().val("");
	$("tr#type").hide();
	$("tr#bgnDateRow").hide();
	$("tr#endDateRow").hide();
	$("tr#dataYMRow").hide();
	$("tr#CLS180R27C_StartTime_Row").hide();// J-112-0099
	$("tr#CLS180R27C_EndTime_Row").hide();// J-112-0099

	$("tr#CLS250R01TYPE").hide();
	$("tr#CLS180R21TYPE").hide();
	$("tr#CLS180R53Row").hide();
	$("select#searchAction" + docType).show();

	$("#CLS_rptNo_srcDesc").empty();
	$("#LRS_rptNo_Desc").empty();

	// 消金報表切換企金報表
	$("tr.usedByCRS").hide();
	// 企金報表切換消金報表
	$("tr.usedByLRS").hide();
});

$("select#searchAction2").change(
		function() {
			// reset
			$("select#prod").val("");
			$("select#dateType").val(0);
			$("select#parentCntrNo").val("");
			$("#dataStartDate1").val(lastDateStart1);
			$("#dataEndDate1").val(lastDateEnd1);
			$("#dataStartDate2").val(lastDateStart2);
			$("#dataEndDate2").val(lastDateEnd2);
			$("#year").val(todayYear);
			$("#dataYM").val(lastDateStart2);
			// start change
			$("tr#yearDateRow").hide();
			$("tr#type").hide();
			$("tr#dataYMRow").show();
			$("tr#bgnDateRow").hide();
			$("tr#endDateRow").hide();
			$("tr#CLS250R01TYPE").hide();
			$("tr#CLS180R21TYPE").hide();
			$("tr#CLS180R15DRow").hide();
			$("tr#CLS180R27C_StartTime_Row").hide();// J-112-0099
			$("tr#CLS180R27C_EndTime_Row").hide();// J-112-0099
			$("tr#CLS180R53Row").hide();
			$("#cntrnoTr").hide();

			$("tr.usedByCRS").hide();
			
			//J-111-0600_05097_B1001 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
			$("[id^='lms180R68Filter']").hide(); 
			//J-111-0600_05097_B1002 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
			$("#show_dateType_lms180R68_3").hide();
			
			//J-111-0600_05097_B1003 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
			$("[class^='lms180R65Filter']").hide();
			
			//J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
			$("#div_brStaffNo").hide();
			
			//J-113-0125_05097_B1001 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
			$("[class^='proPertyFilter']").hide(); 
			
			switch ($("select#searchAction2").val()) {
			case 'CLS180R10':
				$("tr#dataYMRow").hide();
				break;
			case 'CLS180R02':
			case 'CLS180R03':
			case 'CLS180R03B':
			case 'CLS180R04':
				$("tr#type").show();
				break;
			case 'CLS180R06':
				$("tr#bgnDateRow").show();
				$("tr#endDateRow").show();
				$("tr#prodRow").show();
				$("tr#dataYMRow").hide();
				break;
			case 'CLS180R12':
				$("tr#dataYMRow").hide();
				$("tr#yearDateRow").show();
				break;
			case 'CLS180R13':
				$("tr#dataYMRow").hide();
				// 起的年月
				$("tr#bgnDateRow").find("#dataDate1").hide();
				$("tr#bgnDateRow").find("#dataDate3").show();
				$("tr#bgnDateRow").show();

				// 迄的年月
				$("tr#endDateRow").find("#dataDate2").hide();
				$("tr#endDateRow").find("#dataDate4").show();
				$("tr#endDateRow").show();
				if (true) {
					$("tr#typeCLS180R14Row")
							.find("#block_div_typeCLS180R14Row").hide();
					$("tr#typeCLS180R14Row").show();
				}
				
				//J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
				$("#div_brStaffNo").show();
				
				break;
			case 'CLS180R14':
				$("tr#dataYMRow").hide();
				$("tr#typeCLS180R14Row").find("#block_div_typeCLS180R14Row")
						.show();
				$("tr#typeCLS180R14Row").show();
				break;
			case 'CLS180R15':
			case 'CLS180R15B':
			case 'CLS180R15C':
			case 'CLS180R15E':
			case 'CLS180R15F':
				$("tr#dataYMRow").hide();
				$("tr#typeCLS180R14Row").find("#block_div_typeCLS180R14Row")
						.hide();
				$("tr#typeCLS180R14Row").show();
				break;
			case 'CLS180R15D':
				$("tr#dataYMRow").hide();
				$("tr#CLS180R15DRow").show();
				break;
			case 'CLS180R16':
				$("tr#dataYMRow").hide();
				// 起的年月
				$("tr#bgnDateRow").find("#dataDate1").hide();
				$("tr#bgnDateRow").find("#dataDate3").show();
				$("tr#bgnDateRow").show();

				// 迄的年月
				$("tr#endDateRow").find("#dataDate2").hide();
				$("tr#endDateRow").find("#dataDate4").show();
				$("tr#endDateRow").show();

				break;
			case 'CLS250R01':
				$("tr#CLS250R01TYPE").show();
				$("tr#dataYMRow").hide();
				break;
			case 'CLS180R21':
				$("tr#CLS180R21TYPE").show();
				$("tr#dataYMRow").hide();
				break;
			case 'CLS180R23':
				$("tr#dataYMRow").hide();
				break;
			case 'CLS180R25':
			case 'CLS180R54':
				$("tr#bgnDateRow").show();
				$("tr#endDateRow").show();
				$("#dataStartDate1").val(todayYear + "-01-01");
				$("#dataEndDate1").val(today);
				$("tr#dataYMRow").hide();
				//J-111-0600_05097_B1001 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
				$("[id^='lms180R68Filter']").show(); 
				//J-111-0600_05097_B1002 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
				$("#show_dateType_lms180R68_3").hide();
				break;
			case 'CLS180R30':
				$("tr#dataYMRow").hide();
				break;
			case 'CLS180R53':
				$("tr#bgnDateRow").show();
				$("tr#endDateRow").show();
				$("tr#dataYMRow").hide();
				$("tr#CLS180R53Row").show();
				break;
			case 'CLS180R26':
				$("tr#bgnDateRow").show();
				$("tr#endDateRow").show();
				$("#dataStartDate1").val(todayYear + "-01-01");
				$("#dataEndDate1").val(today);
				$("tr#dataYMRow").hide();
				break;
			case 'CLS180R27':
				$("tr#bgnDateRow").show();
				$("tr#endDateRow").show();
				$("#dataStartDate1").val(todayYear + "-01-01");
				$("#dataEndDate1").val(today);
				$("tr#dataYMRow").hide();
				break;
			case 'CLS180R27B':
				$("tr#bgnDateRow").show();
				$("tr#endDateRow").show();
				$("#dataStartDate1").val(todayYear + "-01-01");
				$("#dataEndDate1").val(today);
				$("tr#dataYMRow").hide();
				break;
			case 'CLS180R27C':
				$("tr#bgnDateRow").hide();
				$("tr#endDateRow").hide();
				$("tr#dataYMRow").hide();
				$("tr#CLS180R27C_StartTime_Row").show();
				$("tr#CLS180R27C_EndTime_Row").show();
				$("#CLS180R27C_DataStartDate").val(todayYear + "-01-01");
				$("#CLS180R27C_DataEndDate").val(today);
				break;
			case 'CLS180R27D':
				var currentDate = new Date().toISOString().slice(0, 10);
				var tomorrow = new Date();
				tomorrow.setDate(tomorrow.getDate()+1);
				var tomorrowDate = tomorrow.toISOString().slice(0, 10);
				$("tr#bgnDateRow").show();
				$("tr#endDateRow").show();
				$("#dataStartDate1").val(currentDate);
				$("#dataEndDate1").val(tomorrowDate);
				$("tr#dataYMRow").hide();
				break;
			default:
				$("tr#type").hide();
			}

			switch ($("select#searchAction2").val()) {
			case 'CLS180R01':
			case 'CLS180R02':
			case 'CLS180R03':
			case 'CLS180R03B':
			case 'CLS180R04':
			case 'CLS250R01':
			case 'CLS180R18':
			case 'CLS180R18B':
				$("#CLS_rptNo_srcDesc").html(
						"<span class='text-red'>"
								+ i18n.lms9511v01[$("select#searchAction2")
										.val()
										+ "_srcDesc"] + "</span>");
				break;
			case 'CLS180R15D':
				$("#CLS_rptNo_srcDesc").html(
						"<span class='text-red'>"
								+ i18n.lms9511v01[$("select#searchAction2")
										.val()
										+ "_srcDesc"] + "</span>");
				break;
			case 'CLS180R40':// 勞工紓困貸款彙總表
				$("tr#dataYMRow").hide();
				break;
			case 'CLS180R41':// 勞工紓困貸款明細表
				$("tr#dataYMRow").hide();
				break;
			case 'CLS180R42':// 特定金錢信託案件量統計報表
				$("tr#dataYMRow").hide();
				break;
			case 'CLS180R50':// 6個月內到期土建融案件月報表
				$("tr#dataYMRow").hide();
				break;
			case 'CLS180R52':// 地政士黑名單
				$("tr#dataYMRow").hide();
				break;
			case 'CLS180R57'://中期循環年度檢視表
				$("#cntrnoTr").show();
				$("#dataYMRow").hide();
				break;
			case 'CLS180R60'://分行承作購置住宅貸款年限逾30年統計表
				$("tr#dataYMRow").hide();
				$("#bgnDateRow").show();
				$("#endDateRow").show();
				break;
			case 'CLS180R61'://房貸案件明細表
				$("tr#dataYMRow").hide();
				$("#bgnDateRow").show();
				$("#endDateRow").show();
				break;
            case 'CLS180R62': //J-113-0237 青創追蹤報表
				$("tr#dataYMRow").hide();
                $("tr#bgnDateRow").show();
                $("tr#endDateRow").show();
			default:
				$("#CLS_rptNo_srcDesc").empty();
				break;
			}
		});

$("select#dataRange").change(function() {
	$("select#prod").val("");
	$("select#dateType").val(0);
	$("#dataStartDate1").val(lastDateStart1);
	$("#dataEndDate1").val(lastDateEnd1);
	$("#dataStartDate2").val(lastDateStart2);
	$("#dataEndDate2").val(lastDateEnd2);
	$("#year").val(todayYear);
	$("#dataYM").val(lastDateStart2);
	// start change
	$("tr#yearDateRow").hide();
	$("tr#type").hide();
	$("tr#dataYMRow").hide();
	$("tr#bgnDateRow").hide();
	$("tr#endDateRow").hide();
	$("tr.usedByCRS").hide();

	var dataRange = $("select#dataRange").val();
	if (dataRange == "0") {
		$("tr#dataYMRow").hide();
	}
	if (dataRange == "1" || dataRange == "2") {
		$("tr#dataYMRow").hide();
		// 起的年月
		$("tr#bgnDateRow").find("#dataDate1").hide();
		$("tr#bgnDateRow").find("#dataDate3").show();
		$("tr#bgnDateRow").show();

		// 迄的年月
		$("tr#endDateRow").find("#dataDate2").hide();
		$("tr#endDateRow").find("#dataDate4").show();
		$("tr#endDateRow").show();
	}
});

$("select#dataRangeCLS180R21").change(function() {
	$("select#prod").val("");
	$("select#dateType").val(0);
	$("#dataStartDate1").val(lastDateStart1);
	$("#dataEndDate1").val(lastDateEnd1);
	$("#dataStartDate2").val(lastDateStart2);
	$("#dataEndDate2").val(lastDateEnd2);
	$("#year").val(todayYear);
	$("#dataYM").val(lastDateStart2);
	// start change
	$("tr#yearDateRow").hide();
	$("tr#type").hide();
	$("tr#dataYMRow").hide();
	$("tr#bgnDateRow").hide();
	$("tr#endDateRow").hide();
	$("tr.usedByCRS").hide();

	var dataRange = $("select#dataRangeCLS180R21").val();
	if (dataRange == "0") {
		$("tr#dataYMRow").hide();
	}
	if (dataRange == "1" || dataRange == "2") {
		$("tr#dataYMRow").hide();
		// 起的年月
		$("tr#bgnDateRow").find("#dataDate1").hide();
		$("tr#bgnDateRow").find("#dataDate3").show();
		$("tr#bgnDateRow").show();

		// 迄的年月
		$("tr#endDateRow").find("#dataDate2").hide();
		$("tr#endDateRow").find("#dataDate4").show();
		$("tr#endDateRow").show();
	}
});

$("select#rdo_typeCLS180R14").change(function() {
	$("#block_rdo_typeCLS180R14_C").hide();
	$("#block_yyyyMM_typeCLS180R14").hide();
	if (this.value == 'A') {
		// all hide
	} else if (this.value == 'B') {
		$("#block_yyyyMM_typeCLS180R14").show();
	} else if (this.value == 'C') {
		$("#block_rdo_typeCLS180R14_C").show();
	} else if (this.value == 'D') {
	}
});

//J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表
$( 'input:checkbox[name="uselms180R72RptTitle1"]' ).change(function() {
	var val = $('input:checkbox:checked[name="uselms180R72RptTitle1"]').val();
	if(val == "Y"){
		$("#showlms180R72RptTitle1").show();
	}else{
		$("#showlms180R72RptTitle1").find(':input').not(':button, :submit, :reset, :hidden').prop('checked', false).prop('selected', false).not(':checkbox, :radio').val('');
		$("#showlms180R72RptTitle1").hide();
		
	}
});

//J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表
$( 'input:checkbox[name="uselms180R72RptTitle2"]' ).change(function() {
	var val = $('input:checkbox:checked[name="uselms180R72RptTitle2"]').val();
	if(val == "Y"){
		$("#showlms180R72RptTitle2").show();
	}else{
		$("#showlms180R72RptTitle2").find(':input').not(':button, :submit, :reset, :hidden').prop('checked', false).prop('selected', false).not(':checkbox, :radio').val('');
		$("#showlms180R72RptTitle2").hide();
		
	}
});


// ================================ END ELEMENT SETTING
// ====================================

function getBrNos(idName) {
	var choiceBrNos = [];
	$('input:checkbox:checked[id="' + idName + '"]').each(function(i) {
		choiceBrNos.push(this.value);
	});
	return choiceBrNos;
}
// 登錄資料有誤
function saveLogError() {
	var rptNo = $("#reportNo").val();
	if (rptNo == 'LMS180R01') {
		var gridview = $("#LMS180R01Grid");
		var rowid = gridview.getGridParam('selrow');
		if (!rowid) {
			return CommonAPI.showMessage(i18n.lms9511v01["L784S01A.error1"]);
		}
		var data = LMS180R01Grid.getRowData(rowid);
		$.ajax({
			handler : "lms9511m01formhandler",
			action : 'saveLogError2',
			data : {
				mainIdTemp : data.mainId
			},
			success : function(responseData) {
				gridview.trigger("reloadGrid");
			}
		});
	} else if (rptNo == 'LMS180R02A') {
		var datas = [];
		var gridview = $("#LMS180R02AGrid");
		var ids = gridview.getGridParam('selarrrow');
		for ( var i in ids) {
			datas.push(gridview.getRowData(ids[i]).oid);
		}
		if (datas.length == 0) {
			return CommonAPI.showMessage(i18n.lms9511v01["L784S01A.error1"]);
		}
		$.ajax({
			handler : "lms9511m01formhandler",
			action : 'saveLogError',
			data : {
				oids : datas
			},
			success : function(responseData) {
				gridview.trigger("reloadGrid");
			}
		});
	} else if (docType == "2") {
		var data = fileGrid.getRowData(1);
		if (data.cfrmFlag == "Y") {
			MegaApi.confirmMessage(i18n.lms9511v01["changeFlag"], function(
					action) {
				if (action) {
					$.ajax({
						handler : "lms9511m01formhandler",
						action : 'clsUnaccept',
						data : {
							mainIdTmp : data.mainId
						},
						success : function(responseData) {
							fileGrid.reload();
							MegaApi.showPopMessage(i18n.def["confirmTitle"],
									i18n.def["runSuccess"]);
						}
					});
				}
			});
		} else {
			$.ajax({
				handler : "lms9511m01formhandler",
				action : 'clsUnaccept',
				data : {
					mainIdTmp : data.mainId
				},
				success : function(responseData) {
					fileGrid.reload();
					MegaApi.showPopMessage(i18n.def["confirmTitle"],
							i18n.def["runSuccess"]);
				}
			});
		}
	}
}

// 彈出視窗顯示查詢條件
function showL180R02AThickBox() {
	listBranch("listBranch");
	listApprove();
	$("#lms9511thickbox3")
			.thickbox(
					{
						title : i18n.lms9511v01['L180R02A.filter'],
						width : 560,
						height : 340,
						modal : false,
						align : 'center',
						valign : 'bottom',
						i18n : i18n.def,
						readOnly : false,
						buttons : {
							"sure" : function() {
								if ($("#Lms9511v01From3").valid()) {
									var choiceBrNos = getBrNos("listBranch");
									if ($("select#approver option:selected")
											.val() == ''
											&& choiceBrNos.length == 0) {
										// val.inelbranch=請選分行
										return CommonAPI
												.showMessage(i18n.lms9511v01['L784M01a.error02']);
									}
									LMS180R02AGrid
											.reload({
												listBranch : choiceBrNos,
												approver : $(
														"select#approver option:selected")
														.val()
											});
									$.thickbox.close();
								}
							},
							"cancel" : function() {
								var choiceBrNos = [];
								choiceBrNos.push('');
								LMS180R02AGrid.reload({
									listBranch : choiceBrNos,
									approver : ''
								});
								$.thickbox.close();
							}
						}
					});// Ajax

}

function listApprove() {
	$.ajax({
		type : "POST",
		handler : "lms9511m01formhandler",
		data : {
			formAction : "queryApprove",
			mainId : $("#mainId").val(),
			noOpenDoc : true
		},
		success : function(responseData) {
			var data = {
				item : responseData.approvers
			};
			$("#approver").setItems(data);
		}
	});
}

function listBranch(choiceBranch) {
	$.ajax({
		type : "POST",
		handler : "lms9511m01formhandler",
		data : {
			formAction : "queryBranch",
			areaBranchId : $("#areaBranchId").val()
		},
		success : function(responseData) {
			// alert(JSON.stringify(obj));
			var data = {
				border : "none",
				size : 3,
				item : responseData.item
			};
			$("#" + choiceBranch).setItems(data);
		}
	});
}

// 彈出視窗顯示查詢條件
function showThickBox() {
	$("#lms9511thickbox1")
			.thickbox(
					{
						title : i18n.lms9511v01['L784M01a.newInfo01'],
						width : 560,
						height : 320,
						modal : false,
						align : 'center',
						valign : 'bottom',
						i18n : i18n.def,
						buttons : {
							"sure" : function() {
								if ($("#Lms9511v01From1").valid()) {
									var action = $("#searchAction" + docType)
											.val();
									if (action == "") {
										return CommonAPI
												.confirmMessage(i18n.lms9511v01['L784M01a.error01']);
									}
									if (docType == '2') {
										var rptNo = $("#searchAction2").val();
										if (rptNo == 'CLS180R06') {
											if ($("select#prod").val() == "")
												return CommonAPI
														.showMessage(i18n.lms9511v01['prodError']);
										} else if (!$("#dataYM").val().match(
												/^\d{4}\-(0[1-9]|1[0-2])$/)) {
											return CommonAPI
													.showMessage(i18n.def['val.date2']);
										}
									} else {
										if (!$("#dataStartDate2").val().match(
												/^\d{4}\-(0[1-9]|1[0-2])$/)) {
											return CommonAPI
													.showMessage(i18n.def['val.date2']);
										} else if (!$("#dataEndDate2")
												.val()
												.match(
														/^\d{4}\-(0[1-9]|1[0-2])$/)) {
											return CommonAPI
													.showMessage(i18n.def['val.date2']);
										}
									}
									
									if($('#searchAction2').val()==="CLS180R15F"){
										return CommonAPI
										      .showMessage(i18n.lms9511v01['L784M01a.error03']);
									}
									
									actionDos();
								}
							},
							"cancel" : function() {
								$.thickbox.close();
							}
						}
					});// Ajax
}

// 彈出視窗顯示查詢條件
function showCompareBox() {
	$("#XLS_1").val('');
	$("#XLS_2").val('');
	var fileSize = 10 * 1024 * 1024;
	var s = $.extend({
		handler : 'lms2105v01fileuploadhandler',
		fieldId : "XLS_1",
		title : i18n && i18n.def.insertfile || "請選擇附加檔案",
		fileCheck : [ 'xls' ],
		successMsg : false,
		success : function() {
		},
		data : {
			fileSize : fileSize,
			mainId : "",
			deleteDup : true,
			changeUploadName : "compareExl.xls"
		}
	}, s);

	var fileKey1 = "";
	var fileKey2 = "";
	$("#compareExl")
			.thickbox(
					{ // 使用選取的內容進行彈窗
						title : "匯入EXCEL名單",
						width : 500,
						height : 200,
						modal : true,
						i18n : i18n.def,
						buttons : (function() {
							var b = {};
							b['上傳'] = function() {
								if ($("#XLS_1").val() == ''
										|| $("#XLS_2").val() == '') {
									CommonAPI.showMessage("請選擇檔案");
									return;
								}

								$
										.capFileUpload({
											handler : 'Simplefileuploadhandler',// s.handler,
											fileCheck : s.fileCheck,
											fileElementId : 'XLS_1',// s.fieldId,
											successMsg : s.successMsg,
											data : $.extend({
												fieldId : "RPTcompare"
											}, s.data || {}),
											success : function(json) {
												fileKey1 = json.fileKey;
												ilog.debug("fileKey1=="
														+ fileKey1
														+ "      fileKey2=="
														+ fileKey2);
												// $.thickbox.close();
												// API.showPopMessage("上傳成功");
												$
														.capFileUpload({
															handler : 'Simplefileuploadhandler',// s.handler,
															fileCheck : s.fileCheck,
															fileElementId : 'XLS_2',// s.fieldId,
															successMsg : s.successMsg,
															data : $
																	.extend(
																			{
																				fieldId : "RPTcompare"
																			},
																			$(
																					"#compareExl")
																					.serializeData(),
																			s.data
																					|| {}),
															success : function(
																	json) {
																fileKey2 = json.fileKey;
																ilog
																		.debug("fileKey1=="
																				+ fileKey1
																				+ "      fileKey2=="
																				+ fileKey2);
																// $.thickbox.close();
																// API.showPopMessage("上傳成功");

																// $.ajax({
																// handler :
																// "lms9511m01formhandler",//s.handler,
																// type :
																// "POST",
																// dataType :
																// "json",
																// data : {
																// formAction :
																// "getUploadFile",
																// fileKey1 :
																// fileKey1,
																// fileKey2 :
																// fileKey2
																// },
																// success :
																// function(obj)
																// {
																//
																// }
																// });
																$.form
																		.submit({
																			url : __ajaxHandler,
																			target : "_blank",
																			data : {
																				_pa : 'lmsdownloadformhandler',
																				fileKey1 : fileKey1,
																				fileKey2 : fileKey2,
																				'mode' : 'compare',
																				'fileDownloadName' : 'compareExl.xls',
																				'serviceName' : 'lms7600r01rptservice'
																			}
																		});

															}
														});
											}
										});

							};
							b[i18n && i18n.def.cancel || "取消"] = function() {
								$.thickbox.close();
							};
							return b;
						})()
					});
}

// 此報表需要做的事情
function actionDos() {
	var rptNo;
	var startDate;
	var endDate;
	rptNo = DOMPurify.sanitize($("select#searchAction" + DOMPurify.sanitize(docType) + " option:selected").val());
	$("#reportNo").val(rptNo);
	// 需要取得哪種的日期格式
	if (docType == '2' && rptNo != 'CLS180R06' && rptNo != 'CLS180R10') {
		startDate = $("#dataYM").val();
		endDate = $("#dataYM").val();
		if (rptNo == "CLS250R01") {
			var dataRange = $("select#dataRange").val();
			if (dataRange != '0') {
				startDate = $("#dataStartDate2").val();
				endDate = $("#dataEndDate2").val();
			}
		}

		if (rptNo == "CLS180R21") {
			var dataRange = $("select#dataRangeCLS180R21").val();
			if (dataRange != '0') {
				startDate = $("#dataStartDate2").val();
				endDate = $("#dataEndDate2").val();
			}
		}
		if (rptNo == "CLS180R25") {
			startDate = $("#dataStartDate1").val();
			endDate = $("#dataEndDate1").val();
		}
		if (rptNo == "CLS180R53") {
			startDate = $("#dataStartDate1").val();
			endDate = $("#dataEndDate1").val();
		}
		if (rptNo == "CLS180R54") {
			startDate = $("#dataStartDate1").val();
			endDate = $("#dataEndDate1").val();
		}
		if (rptNo == "CLS180R26") {
			startDate = $("#dataStartDate1").val();
			endDate = $("#dataEndDate1").val();
		}
		if (rptNo == "CLS180R27") {
			startDate = $("#dataStartDate1").val();
			endDate = $("#dataEndDate1").val();
		}
		if (rptNo == "CLS180R27B") {
			startDate = $("#dataStartDate1").val();
			endDate = $("#dataEndDate1").val();
		}
		if (rptNo == "CLS180R27C") {
			startDate = $("#CLS180R27C_DataStartDate").val();
			endDate = $("#CLS180R27C_DataEndDate").val();
			startHour = $("#CLS180R27C_DataStartHour").val();
			endHour = $("#CLS180R27C_DataEndHour").val();
		}
		if (rptNo == "CLS180R27D") {
			startDate = $("#dataStartDate1").val();
			endDate = $("#dataEndDate1").val();
		}
		if (rptNo == "CLS180R60") {
			startDate = $("#dataStartDate1").val();
			endDate = $("#dataEndDate1").val();
		}
		if (rptNo == "CLS180R61") {
			startDate = $("#dataStartDate1").val();
			endDate = $("#dataEndDate1").val();
		}
		if (rptNo == "CLS180R62") {
			startDate = $("#dataStartDate1").val();
			endDate = $("#dataEndDate1").val();
		}
	} else if (rptNo == 'CLS180R10') {
		startDate = beforeLastToday;
		endDate = yesDay;
	} else if (rptNo == 'LMS180R11') {
		startDate = $("#dataStartDate2").val();
		endDate = $("#dataEndDate2").val();
	} else if (rptNo == 'LMS180R14' || rptNo == 'LMS180R17'
			|| rptNo == 'LMS180R02A' || rptNo == 'LMS180R37'
			|| rptNo == 'LMS180R42' || rptNo == 'LMS180R45'
			|| rptNo == 'LMS180R49') {
		startDate = $("#dataYM").val();
		endDate = $("#dataYM").val();
	} else if (rptNo == 'LMS180R14Q') {
		startDate = $("#dataStartDate2").val();
		endDate = $("#dataEndDate2").val();
	} else if (rptNo == 'LMS180R18') {
		startDate = $("#dataStartDate2").val();
		endDate = $("#dataEndDate2").val();
	} else if (rptNo == 'LMS180R25' || rptNo == 'LMS180R36') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R26') {
		startDate = $("#dataStartDate2").val();
		endDate = $("#dataEndDate2").val();
	} else if (rptNo == 'LMS180R27') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R29') {
		startDate = yesDay;
		endDate = yesDay;
	} else if (rptNo == 'LMS180R53') {
		startDate = $("#dataStartDate2").val();
		endDate = $("#dataEndDate2").val();
	}
	// else if(rptNo == 'LMS180R11'){
	// startDate = $("#dataStartDate2").val();
	// endDate = $("#dataEndDate2").val();
	// }
	else if (rptNo == 'LMS180R16' || rptNo == 'LMS180R47') {
		startDate = $("#year").val();
		endDate = $("#year").val();
	} else if (rptNo == 'LMS180R31') {
		startDate = $("#dataStartDate2").val();
		endDate = $("#dataEndDate2").val();
	} else if (rptNo == 'LMS180R33') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R34') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R35') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R38') {
		startDate = $("#dataStartDate2").val();
		endDate = $("#dataEndDate2").val();
	} else if (rptNo == 'LMS180R39') {
		startDate = today;
		endDate = today;
	} else if (rptNo == 'LMS180R42T') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R54T') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R43') {
		startDate = $("#dataStartDate2").val();
		endDate = $("#dataEndDate2").val();
	} else if (rptNo == 'LMS180R44') {
		startDate = $("#dataStartDate2").val();
		endDate = $("#dataEndDate2").val();
	} else if (rptNo == 'LMS180R54') {
		startDate = $("#dataStartDate2").val();
		endDate = $("#dataEndDate2").val();
	} else if (rptNo == 'LMS180R48') {
		startDate = $("#dataStartDate2").val();
		endDate = $("#dataEndDate2").val();
	} else if (rptNo == 'LMS180R52') {
		startDate = today;
		endDate = today;
	} else if (rptNo == 'LMS180R55') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R56') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();

	} else if (rptNo == 'LMS180R60') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R61') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R62') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R63') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R64') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R65') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R66') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R67') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R69') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R72') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();	
	} else if (rptNo == 'LMS180R73') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R74') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R75') {
        startDate = $("#dataStartDate1").val();
        endDate = $("#dataEndDate1").val();
        if ($.trim(startDate) != "" && $.trim(endDate) != "") {
            var mDateS = startDate.split("-");
            var mDateE = endDate.split("-");
            if(mDateS.length >= 2 && mDateE.length >= 2 ) {
                var diffDay = ((mDateE[0] - mDateS[0]) * 12) + (mDateE[1] - mDateS[1]);
                if (diffDay > 13) {
                    return CommonAPI.showErrorMessage(i18n.lms9511v01["LMS9511X75.error01"]);
                }
            }
        }
	} else if (rptNo == 'LMS180R76') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else if (rptNo == 'LMS180R77') {
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	} else { // LMS180R50 LMS180R51
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	}

	ilog.debug("【actionDos】{startDate=" + startDate + ", endDate=" + endDate
			+ "}");

	// 加入備註
	var remark = "";
	switch (rptNo) {
	case "CLS180R02":
	case "CLS180R03":
	case "CLS180R03B":
	case "CLS180R04":
	case "CLS180R10":
		remark = "dateType=" + $("select#dateType").val();
		break;
	case "CLS180R06":
		remark = "prodKind=" + $("select#prod").val() + ";prodName="
				+ $("select#prod option:selected").text();
	case "CLS250R01":
		remark = "dataRange=" + $("select#dataRange").val();
		break;
	case "CLS180R21":
		remark = "dataRange=" + $("select#dataRangeCLS180R21").val();
		break;
	case "CLS180R53":
		remark = "parentCntrNo=" + $("select#parentCntrNo").val();
		break;
	case "LMS180R27":
	case "LMS180R64":
		remark = "selectBrNo=" + $("#selectBrNo").val() + ";custId="
				+ $("#custId").val();
		break;
	case "LMS180R29":
		remark = "privateEquityData=" + $("#privateEquityData").val();
		break;
	case "LMS180R40":
		remark = "custId=" + $("#custId").val();
		break;
	case "LMS180R50":
		remark = $("input[name=exportType]:checked").val();// "exportType="+$("input[name=exportType]:checked").val();
		break;
	case "CLS180R57":
		remark = $("#cntrno").val();
		break;
	case "LMS180R61":
		// J-110-0018_05097_B1001 Web
		// e-Loan簽報書額度明細表中增列「兆豐百億挺你專案」及「協助農地工廠合法化融資貸款」兩項專案，並產生統計報表
		remark = "projClass=" + $("#projClass").val() + ";projName="
				+ $("select#projClass option:selected").text();
		break;
	case "LMS180R65":
		//J-111-0600_05097_B1003 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
		remark = "dateType_lms180R65="+$("input[name=dateType_lms180R65]:checked").val(); 
		var allCheackedVal_1 = [];
		$.each($("#Lms9511v01From1 :checkbox[name=docKind_180R65]:checked"), function(i, n){
            allCheackedVal_1[i] = $(n).val();
        });
		remark = remark + ";"+"docKind_180R65=" + allCheackedVal_1.join("|");
		break;
	case "LMS180R68":
	case "CLS180R54":
		// J-111-0600_05097_B1001 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
		// LMS180R68   企金授信簽報案件經區域營運中心流程進度控管表
		//J-111-0600_05097_B1002 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
		remark = "dateType_lms180R68="+$("input[name=dateType_lms180R68]:checked").val(); 
		break;		
	case "LMS180R72":
		// J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表
		// LMS180R72 企金授信核准案件BIS評估表
		var allCheackedVal_1 = [];
		$.each($("#Lms9511v01From1 :checkbox[name=docKind_180R72]:checked"), function(i, n){
            allCheackedVal_1[i] = $(n).val();
        });
        
        var allCheackedVal_2 = [];
		$.each($("#Lms9511v01From1 :checkbox[name=caseLvl_180R72]:checked"), function(i, n){
            allCheackedVal_2[i] = $(n).val();
        });
		
		remark = "docKind_180R72=" + allCheackedVal_1.join("|")
		    + ";caseLvl_180R72=" + allCheackedVal_2.join("|") 
		    +";rptTitle1=" + $('input:checkbox:checked[name="uselms180R72RptTitle1"]').val() 
		 	+";rptTitle1_a=" + $("#rptTitle1_a").val()
			+";rptTitle1_b=" + $("#rptTitle1_b").val()
			+";rptTitle1_c=" + $("#rptTitle1_c").val()
			+";rptTitle1_d=" + $("#rptTitle1_d").val()
			+";rptTitle1_e=" + $("#Lms9511v01From1").find("#rptTitle1_e option:selected").html()
			+";rptTitle2=" + $('input:checkbox:checked[name="uselms180R72RptTitle2"]').val() 
			+";rptTitle2_a=" + $("#rptTitle2_a").val()
			+";rptTitle2_b=" + $("#rptTitle2_b").val()
			+";rptTitle2_c=" + $("#rptTitle2_c").val()
			+";rptTitle2_d=" + $("#rptTitle2_d").val()
			+";rptTitle2_e=" + $("#rptTitle2_e").val()
			+";rptTitle2_f=" + $("#Lms9511v01From1").find("#rptTitle2_f option:selected").html()
			+";docStatus_180R72=" +$("#Lms9511v01From1").find("#docStatus_180R72 option:selected").val()
			+";caseOutputType_180R72=" + $('input:radio:checked[name="caseOutputType_180R72"]').val();
		 
		break;	
	case "LMS180R74":
		//J-113-0125_05097_B1001 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
		remark = "proPerty="+$("input[name=proPerty]:checked").val(); 
		var allCheackedVal_1 = [];
		$.each($("#Lms9511v01From1 :checkbox[name=proPerty]:checked"), function(i, n){
            allCheackedVal_1[i] = $(n).val();
        });
		remark = remark + ";"+"proPerty=" + allCheackedVal_1.join("|");
		break;	
	case "CLS180R27C":
		remark = $("#CLS180R27C_DataStartHour").val() + "," + $("#CLS180R27C_DataEndHour").val();
		break;
		
	}
	if (rptNo == 'LMS180R17') {
		execLMS180R17Data(startDate, endDate);
	} else if (rptNo == 'LMS180R13' || rptNo == 'LMS180R12'
			|| rptNo == 'LMS180R11' || rptNo == 'LMS180R18'
			|| rptNo == 'LMS180R25' || rptNo == 'LMS180R26'
			|| rptNo == 'LMS180R36' || rptNo == 'LMS180R37'
			|| rptNo == 'LMS180R31' || rptNo == 'LMS180R38'
			|| rptNo == 'LMS180R39' || rptNo == 'LMS180R53'
			|| rptNo == 'LMS180R55' || rptNo == 'LMS180R58'
			|| rptNo == 'LMS180R63' || rptNo == 'LMS180R75' ) {

		if (rptNo == 'LMS180R27' || rptNo == 'LMS180R64') {
			if (startDate < '2015-01-01') {
				// LMS9511X27.error01=查詢起日不得小於2015-01-01。
				return CommonAPI
						.showMessage(i18n.lms9511v01["LMS9511X27.error01"]);
			}
			if (endDate < startDate) {
				// LMS9511X27.error02=起始日期不能大於結束日期
				return CommonAPI
						.showMessage(i18n.lms9511v01["LMS9511X27.error02"]);
			}
		}

		// 先檢查是否這四張報表可以跑批次
		$
				.ajax({
					type : "POST",
					handler : "lms9511m01formhandler",
					data : {
						formAction : "checkBatchData",
						rptNo : $("#reportNo").val(),
						docType : $("input[name=docType]:checked").val(),// $("#docType").val(),
						dataStartDate : startDate,
						dataEndDate : endDate,
						remark : remark
					},
					success : function(responseData) {

						if (responseData.SUCCESS == 'N') {
							alert("ERRORAAAAAAAA");
						} else {
							$("#" + rptNo + "MSG")
									.thickbox(
											{
												title : i18n.lms9511v01['L784M01a.newInfo2'],
												width : 350,
												height : 200,
												modal : false,
												align : 'center',
												valign : 'bottom',
												i18n : i18n.def,
												buttons : {
													"sure" : function() {
														$.thickbox.close();
														// var scheduleUrl =
														// window.location.href.replace("home/repp","").replace("localhost","127.0.0.1")
														// + "scheduler";
														// if(window.location.href.indexOf("localhost:8080")
														// != -1){
														// $.ajax({
														// type: "POST",
														// handler:
														// "lms9511m01formhandler",
														// data: {
														// formAction:
														// "addBatchData",
														// rptNo:
														// $("#reportNo").val(),
														// docType:
														// $("input[name=docType]:checked").val(),//$("#docType").val(),
														// dataStartDate:
														// startDate,
														// dataEndDate: endDate,
														// remark:remark
														// },
														// success:
														// function(responseData){
														// $("#mainId").val(responseData.mainId);
														// L9511v01Grid01.trigger("reloadGrid");
														// if($("#reportNo").val()
														// == 'LMS180R16'){
														// $("#yearM1").val(responseData.dataDate);
														// $("#yearM").val(responseData.dataDate);
														// }
														// }
														// });
														// }else{
														// $.ajax({
														// type: "POST",
														// handler:
														// "lms9511m01formhandler",
														// data: {
														// formAction:
														// "callBatch",
														// rptNo:
														// $("#reportNo").val(),
														// docType:
														// $("input[name=docType]:checked").val(),//$("#docType").val(),
														// dataStartDate:
														// startDate,
														// dataEndDate: endDate,
														// dateType:
														// $("select#dateType").val(),
														// prodKind:
														// $("select#prod").val(),
														// prodName:
														// $("select#prod
														// option:selected").text(),
														// remark:remark
														// },
														// success:
														// function(responseData){
														//			    	        	        	
														// }
														// });
														// }
														$
																.ajax({
																	type : "POST",
																	handler : "lms9511m01formhandler",
																	data : {
																		formAction : "callBatch",
																		rptNo : $(
																				"#reportNo")
																				.val(),
																		docType : $(
																				"input[name=docType]:checked")
																				.val(),// $("#docType").val(),
																		dataStartDate : startDate,
																		dataEndDate : endDate,
																		dateType : $(
																				"select#dateType")
																				.val(),
																		prodKind : $(
																				"select#prod")
																				.val(),
																		prodName : $(
																				"select#prod option:selected")
																				.text(),
																		remark : remark
																	},
																	success : function(
																			responseData) {
																		L9511v01Grid01
																				.trigger("reloadGrid");
																	}
																});

														// var inputText =
														// "{'serviceId':'RptLMSBatch2ServiceImpl','rptNo':'"+$('#reportNo').val()+"','docType':'"+$('input[name=docType]:checked').val()+"','dataStartDate':'"+startDate+"','dataEndDate':'"+endDate+"','remark':'"+remark+"','unitNo':'"+userInfo.unitNo+"','userId':'"+userInfo.userId+"'}";
														// $.form.submit({
														// url: scheduleUrl,
														// target: "_blank",
														// data: {
														// input : inputText
														// },
														// });
														$.thickbox.close();
													},
													"cancel" : function() {
														$.thickbox.close();
													}
												}
											});// thickbox
						}
					}
				});

	} else if (rptNo == 'LMS180R01' || rptNo == 'LMS180R16'
			|| rptNo == 'LMS180R02A' || rptNo == 'CLS180R01'
			|| rptNo == 'CLS180R02' || rptNo == 'CLS180R03'
			|| rptNo == 'CLS180R03B' || rptNo == 'CLS180R04'
			|| rptNo == 'CLS180R06' || rptNo == 'CLS180R10'
			|| rptNo == 'CLS250R01' || rptNo == 'CLS180R21') {

		var rptNoNew = $("#reportNo").val();

		if (rptNo == 'CLS250R01') {
			var dataRange = $("#dataRange").val();
			if (dataRange == "0") {
				rptNoNew = 'CLS250R01A';
			} else if (dataRange == "1") {
				rptNoNew = 'CLS250R01B';
			} else if (dataRange == "2") {
				rptNoNew = 'CLS250R01C';
			}
		}

		if (rptNo == 'CLS180R21') {
			var dataRange = $("#dataRangeCLS180R21").val();
			if (dataRange == "0") {
				rptNoNew = 'CLS180R21A';
			} else if (dataRange == "1") {
				rptNoNew = 'CLS180R21B';
			} else if (dataRange == "2") {
				rptNoNew = 'CLS180R21C';
			}
		}
		ilog.debug("addBatchData[rptNo=" + rptNoNew + "][dataStartDate="
				+ startDate + "][dataEndDate=" + endDate + "][remark=" + remark
				+ "]");

		$.ajax({
			type : "POST",
			handler : "lms9511m01formhandler",
			data : {
				formAction : "addBatchData",
				// rptNo: $("#reportNo").val(),
				rptNo : rptNoNew,
				docType : $("input[name=docType]:checked").val(),// $("#docType").val(),
				dataStartDate : startDate,
				dataEndDate : endDate,
				dateType : $("select#dateType").val(),
				prodKind : $("select#prod").val(),
				prodName : $("select#prod option:selected").text(),
				remark : remark
			},
			success : function(responseData) {
				if (responseData.SUCCESS == 'N') {

				} else {
					$("#lms9511GridShow01").hide();
					if (docType == '1')
						$("#" + rptNo + "GridShow").show();
					else
						$("#fileGrid").show();
					$("#btnReturnPage").show();
					reloadGridAndBtnHandle(rptNo, responseData.mainId);
					$("#mainId").val(responseData.mainId);
					if (rptNo == 'LMS180R02A') {
						var name2 = $(
								'select#searchAction' + docType
										+ ' option:selected').text();
						$("#searchActionName2").val(name2);
						$("#searchActionName").val(
								name2 + i18n.lms9511v01["L180R02A.sendTimeN"]);
						$("#areaBranchId").val(responseData.brNo);
					} else if (rptNo == 'CLS180R01') {
						$("#searchActionName").val(
								$("#dataYM").val()
										+ " "
										+ $(
												'select#searchAction' + docType
														+ ' option:selected')
												.text());
					} else {
						$("#searchActionName").val(
								$(
										'select#searchAction' + docType
												+ ' option:selected').text());
					}
					L9511v01Grid01.trigger("reloadGrid");
				}

			}
		});
		$.thickbox.close();
	} else if (rptNo == 'CLS180R12' || rptNo == 'CLS180R13'
			|| rptNo == 'CLS180R14' || rptNo == 'CLS180R15'
			|| rptNo == 'CLS180R15B' || rptNo == 'CLS180R15C'
			|| rptNo == 'CLS180R15D' || rptNo == 'CLS180R15E'
			|| rptNo == 'CLS180R15F'
			|| rptNo == 'CLS180R16' || rptNo == 'LMS180R57') {
		// 消金覆審報表
		if (rptNo == 'CLS180R12') {
			// ---
			// 指定傳至 server 的起迄日
			startDate = $("#year").val();
			endDate = $("#year").val();
		}
		if (rptNo == 'CLS180R13') {
			var chkS = checkYM($("#dataStartDate2").val());
			if (chkS == true) {
			} else {
				return;
			}
			// ---
			var chkE = checkYM($("#dataEndDate2").val());
			if (chkE == true) {
			} else {
				return;
			}
			// ===
			var crs_brInfo = _get_crs_brInfo();
			if (crs_brInfo.brNoArr.length == 0) {
				return CommonAPI.showMessage(i18n.def.action_005);
			}
			if (crs_brInfo.chooseBrType == '') {
				return;
			}
			// ===
			// 設定 remark
			//J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
			remark = "brType=" + crs_brInfo.chooseBrType + ";br="
					+ crs_brInfo.brNoArr.join("|") + ";brStaffNo="+$("#brStaffNo").val();
			// ---
			// 指定傳至 server 的起迄日
			startDate = $("#dataStartDate2").val();
			endDate = $("#dataEndDate2").val();
		}
		if (rptNo == 'CLS180R14') {
			var rdo_typeCLS180R14_val = $("#rdo_typeCLS180R14").val();
			if (rdo_typeCLS180R14_val == "A") {
				rptNo = 'CLS180R14A';
			} else if (rdo_typeCLS180R14_val == "B") {
				rptNo = 'CLS180R14B';
			} else if (rdo_typeCLS180R14_val == "C") {
				rptNo = 'CLS180R14C';
			} else if (rdo_typeCLS180R14_val == "D") {
				rptNo = 'CLS180R14D';
			}
			// ===
			var crs_brInfo = _get_crs_brInfo();
			if (crs_brInfo.brNoArr.length == 0) {
				return CommonAPI.showMessage(i18n.def.action_005);
			}
			if (crs_brInfo.chooseBrType == '') {
				return;
			}
			// ===
			// 設定 remark
			remark = "brType=" + crs_brInfo.chooseBrType + ";br="
					+ crs_brInfo.brNoArr.join("|");

			// 加入 dt
			if ($("#yyyyMM_typeCLS180R14").is(":visible")) {
				var yyyyMM_typeCLS180R14_val = $("#yyyyMM_typeCLS180R14").val();

				var chkR = checkYM(yyyyMM_typeCLS180R14_val);
				if (chkR == true) {
				} else {
					return;
				}

				remark += (";dt=" + yyyyMM_typeCLS180R14_val);
			}
			// 加入 mCnt
			if ($("#rdo_typeCLS180R14_C").is(":visible")) {
				var rdo_typeCLS180R14_C_val = $("#rdo_typeCLS180R14_C").val();

				remark += (";mCnt=" + rdo_typeCLS180R14_C_val);
			}
			// ---
			// 指定傳至 server 的起迄日
			startDate = '1911';
			endDate = '1911';
		}
		if (rptNo == 'CLS180R15' || rptNo == 'CLS180R15B'
				|| rptNo == 'CLS180R15C' || rptNo == 'CLS180R15E'
				|| rptNo == 'CLS180R15F') {
			var crs_brInfo = _get_crs_brInfo();
			if (crs_brInfo.brNoArr.length == 0) {
				return CommonAPI.showMessage(i18n.def.action_005);
			}
			if (crs_brInfo.chooseBrType == '') {
				return;
			}
			// ===
			// 設定 remark
			remark = "brType=" + crs_brInfo.chooseBrType + ";br="
					+ crs_brInfo.brNoArr.join("|");
			// ---
			// 指定傳至 server 的起迄日
			startDate = '1911';
			endDate = '1911';
		}
		if (rptNo == 'CLS180R15D') {
			startDate = '1911';
			endDate = $("#baseDate_CLS180R15D").val();
		}
		if (rptNo == 'CLS180R16') {
			// ===
			// 設定 remark
			remark = "";
			// ---
			var chkS = checkYM($("#dataStartDate2").val());
			if (chkS == true) {
			} else {
				return;
			}
			// ---
			var chkE = checkYM($("#dataEndDate2").val());
			if (chkE == true) {
			} else {
				return;
			}
			// ---
			// 指定傳至 server 的起迄日
			startDate = $("#dataStartDate2").val();
			endDate = $("#dataEndDate2").val();
		}
		if (rptNo == 'LMS180R57') {
			// 設定 remark
			remark = "";
			// ---
			var chkS = checkYM($("#dataStartDate2").val());
			if (chkS == true) {
			} else {
				return;
			}
			// ---
			var chkE = checkYM($("#dataEndDate2").val());
			if (chkE == true) {
			} else {
				return;
			}
			// ---
			// 指定傳至 server 的起迄日
			startDate = $("#dataStartDate2").val();
			endDate = $("#dataEndDate2").val();
			if (endDate < startDate) {
				// LMS9511X27.error02=起始日期不能大於結束日期
				return CommonAPI
						.showMessage(i18n.lms9511v01["LMS9511X27.error02"]);
			}
		}

		ilog.debug("addBatchData[rptNo=" + rptNo + "][dataStartDate="
				+ startDate + "][dataEndDate=" + endDate + "][remark=" + remark
				+ "]");

		$.ajax({
			type : "POST",
			handler : "lms9511m01formhandler",
			data : {
				'formAction' : "addBatchData",
				'rptNo' : rptNo,
				'docType' : $("input[name=docType]:checked").val(),// $("#docType").val(),
				'dataStartDate' : startDate,
				'dataEndDate' : endDate,
				'remark' : remark
			},
			success : function(responseData) {
				$("#mainId").val(responseData.mainId);
				L9511v01Grid01.trigger("reloadGrid");
			}
		});
		$.thickbox.close();
	} else if (rptNo == 'LMS180R19' || rptNo == 'LMS180R19B'
			|| rptNo == 'LMS180R20' || rptNo == 'LMS180R21'
			|| rptNo == 'LMS180R22' || rptNo == 'LMS180R23'
			|| rptNo == 'LMS180R23B' || rptNo == 'LMS180R24'
			|| rptNo == 'LMS180R28') {
		if (rptNo == 'LMS180R19' || rptNo == 'LMS180R19B') {
			startDate = '1911';
			endDate = $("#baseDate").val();
		} else if (rptNo == 'LMS180R20') {
			startDate = '1911';
			endDate = '1911';
		} else if (rptNo == 'LMS180R21' || rptNo == 'LMS180R23'
				|| rptNo == 'LMS180R23B' || rptNo == 'LMS180R24'
				|| rptNo == 'LMS180R28') {
			var chkS = checkYM($("#dataStartDate2").val());
			if (chkS == true) {
			} else {
				return;
			}
			// ---
			var chkE = checkYM($("#dataEndDate2").val());
			if (chkE == true) {
			} else {
				return;
			}
			// ---
			// 指定傳至 server 的起迄日
			startDate = $("#dataStartDate2").val();
			endDate = $("#dataEndDate2").val();

		} else if (rptNo == 'LMS180R22') {
			startDate = '1911';
			endDate = '1911';
		}
		// ===
		// 設定 remark
		if (rptNo == 'LMS180R24') {
			remark = "";
		} else if (rptNo == 'LMS180R28' && userInfo.unitNo == '918') {
			remark = "br=007|201|149";
		} else {
			var brNoArr = $("[name=" + lrs_assignId + "]:checked").map(
					function() {
						return $(this).val();
					}).get();
			if (brNoArr.length == 0) {
				return CommonAPI.showMessage(i18n.def.action_005);
			}
			remark = "br=" + brNoArr.join("|");
		}
		$.ajax({
			type : "POST",
			handler : "lms9511m01formhandler",
			data : {
				'formAction' : "addBatchData",
				'rptNo' : rptNo,
				'docType' : $("input[name=docType]:checked").val(),
				'dataStartDate' : startDate,
				'dataEndDate' : endDate,
				'remark' : remark
			},
			success : function(responseData) {
				$("#mainId").val(responseData.mainId);
				L9511v01Grid01.trigger("reloadGrid");
			}
		});
		$.thickbox.close();
	} else if (rptNo == 'LMS180R30') {
		// J-105-0214-001 Web e-Loan 管理報表新增授信簽案已核准未能簽約撥貸原因表。
		// 指定傳至 server 的起迄日
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();

		// 設定 remark
		var brNoArr = $("[name=" + lrs_assignId + "]:checked").map(function() {
			return $(this).val();
		}).get();
		if (brNoArr.length == 0) {
			return CommonAPI.showMessage(i18n.def.action_005);
		}
		remark = "br=" + brNoArr.join("|");

		$.ajax({
			type : "POST",
			handler : "lms9511m01formhandler",
			data : {
				'formAction' : "addBatchData",
				'rptNo' : rptNo,
				'docType' : $("input[name=docType]:checked").val(),
				'dataStartDate' : startDate,
				'dataEndDate' : endDate,
				'remark' : remark
			},
			success : function(responseData) {
				$("#mainId").val(responseData.mainId);
				L9511v01Grid01.trigger("reloadGrid");
			}
		});
		$.thickbox.close();
	} else if (rptNo == 'LMS180R40' || rptNo == 'LMS180R47') {
		if (rptNo == 'LMS180R40') {
			if (endDate < startDate) {
				// LMS9511X27.error02=起始日期不能大於結束日期
				return CommonAPI
						.showMessage(i18n.lms9511v01["LMS9511X27.error02"]);
			}
		}
		$("#" + rptNo + "MSG").thickbox({
			title : i18n.lms9511v01['L784M01a.newInfo2'],
			width : 350,
			height : 200,
			modal : false,
			align : 'center',
			valign : 'bottom',
			i18n : i18n.def,
			buttons : {
				"sure" : function() {
					$.thickbox.close();
					$.ajax({
						type : "POST",
						handler : "lms9511m01formhandler",
						data : {
							formAction : "addBatchData",
							rptNo : $("#reportNo").val(),
							docType : $("input[name=docType]:checked").val(),// $("#docType").val(),
							dataStartDate : startDate,
							dataEndDate : endDate,
							remark : remark
						},
						success : function(responseData) {
							$("#mainId").val(responseData.mainId);
							L9511v01Grid01.trigger("reloadGrid");
						}
					});
					$.thickbox.close();
				},
				"cancel" : function() {
					$.thickbox.close();
				}
			}
		});// thickbox
	} else {
		ilog.debug("lms9511m01formhandler::addBatchData{rptNo:"
				+ $("#reportNo").val() + ", dataStartDate=" + startDate + ""
				+ ", dataEndDate=" + endDate + "" + ", remark=" + remark + ""
				+ "}")
		$.ajax({
			type : "POST",
			handler : "lms9511m01formhandler",
			data : {
				formAction : "addBatchData",
				rptNo : $("#reportNo").val(),
				docType : $("input[name=docType]:checked").val(),// $("#docType").val(),
				dataStartDate : startDate,
				dataEndDate : endDate,
				remark : remark
			},
			success : function(responseData) {
				$("#mainId").val(responseData.mainId);
				L9511v01Grid01.trigger("reloadGrid");
				if ($("#reportNo").val() == 'LMS180R16') {
					$("#yearM1").val(lastDateStart2);
					$("#yearM").val(lastDateStart2);
				}
			}
		});
		$.thickbox.close();
	}
}
/**
 * 回傳
 */
function _get_crs_brInfo() {
	var chooseBrType = $("input[name=brType]:checked").val();
	var brNoArr = [];
	if (chooseBrType == "S") {
		brNoArr.push($("#brType_S").val());
	} else if (chooseBrType == "M") {
		brNoArr = $("[name=" + crs_assignId + "]:checked").map(function() {
			return $(this).val();
		}).get();
	} else {

	}
	var r = {};
	r['chooseBrType'] = chooseBrType;
	r['brNoArr'] = brNoArr;
	return r;
}
// 返回(到第一個入口頁面)
function hideBtn(hideGridDiv) {
	$("#" + hideGridDiv).hide();
	$("#btnLongError").hide();
	$("#lms9511GridShow01").show();
	$("#btnCreateReport").show();
	// $("#btnRPTcompare").show();
	$("#btnReturnPage").hide();
	$("#btnFilter").hide();
	$("#btnSendDocTypeReport").hide();
	$("#btnLongViewMemo").hide();
	$("#btnView").hide();
	$("#btnPullinReport").hide();
	$("#btnPrint").hide();
	$("#btnUpload").hide();
	$("#deep").val('');
	$("#mainId").val('');
	$("#mainId918").val('');
	$("#reportNo").val('');
	$("#searchActionName").val('');
	$("#searchActionName2").val('');
}

// 進入報表GRID顯示
function goSpeGrid(cellvalue, options, rowObject) {
	ilog.debug(rowObject);
	var rptNo = rowObject.rptNo;
	if (rptNo.substr(0, 3) == "LMS") {
		docType = '1';
		$("#btnPrint").show();
		$("#" + rptNo + "GridShow").show();
		if (rptNo == 'LMS180R02A') {
			$("#deep").val('2');
			$("#mainId").val(rowObject.mainId);
			if (userInfo.unitType == '4') {
				$("#btnLongError").show();
				$("#areaBranchId").val(rowObject.jingBan);
			} else {
				$("#btnLongError").hide();
				$("#areaBranchId").val(rowObject.branch);
			}
			$("#btnFilter").show();
		} else if (rptNo == 'LMS180R01') {
			if (userInfo.unitType == '4') {
				$("#btnLongError").show();
			} else {
				$("#btnLongError").hide();
			}
		} else if (rptNo == 'LMS180R15') {

		}
	} else {
		docType = '2';
	}
	$("#LMS180R02ADetail1GridShow").hide();
	$("#reportNo").val(rptNo);
	$("#btnReturnPage").show();
	$("#lms9511GridShow01").hide();
	$("#btnCreateReport").hide();
	$("#btnRPTcompare").hide();
	$("#btnUpload").hide();
	if (rptNo == 'LMS180R02A') {
		$("#searchActionName2").val(rowObject.rptName);
		if (rowObject.sendTime != '') {
			$("#searchActionName").val(
					rowObject.rptName + i18n.lms9511v01["L180R02A.sendTimeY"]);
		} else {
			$("#searchActionName").val(
					rowObject.rptName + i18n.lms9511v01["L180R02A.sendTimeN"]);
		}
	} else if (rptNo == 'CLS180R01') {
		$("#searchActionName")
				.val(rowObject.dataDate + " " + rowObject.rptName);
	} else {
		$("#searchActionName").val(rowObject.rptName);
	}

	reloadGridAndBtnHandle(rowObject.rptNo, rowObject.mainId,
			rowObject.dataDate);
}

// 重新RELOAD GRID
function reloadGridAndBtnHandle(rptNos, rptMainId, dataDate) {
	var unitNo = userInfo ? userInfo.unitNo : "";
	var unitType = userInfo ? userInfo.unitType : "";
	if (docType == "1") {
		if (rptNos == 'LMS180R01' || rptNos == 'LMS180R02A') {
			if (rptNos == 'LMS180R01') {
				LMS180R01Grid.reload({
					mainId : rptMainId,
					rptNo : rptNos,
					nowRpt : "Y"
				});
			} else if (rptNos == 'LMS180R02A') {
				LMS180R02AGrid.reload({
					mainId : rptMainId,
					rptNo : rptNos,
					nowRpt : "Y"
				});
			}
			if (unitType == '4') {
				$("#btnLongViewMemo").show();
				$("#btnLongError").show();
			} else {
				$("#btnSendDocTypeReport").show();
			}
			$("#btnCreateReport").hide();
			if (rptNos == 'LMS180R02A') {
				$("#btnFilter").show();
				$("#btnPrint").show();
			}
		} else if (rptNos == 'LMS180R16') {
			$("#btnView").show();
			$("#btnPrint").show();
			$("#btnPullinReport").show();
			$("#btnCreateReport").hide();

			LMS180R16Grid.reload({
				mainId : rptMainId,
				rptNo : rptNos,
				nowRpt : "Y"
			});
		} else if (rptNos == 'LMS180R15') {
			if (unitType == '4') {
				$("#btnSendDocTypeReport").hide();
			} else {
				$("#btnSendDocTypeReport").show();
			}
			$("#btnView").hide();
			$("#btnPullinReport").hide();
			$("#btnCreateReport").hide();

			LMS180R15Grid.reload({
				mainId : rptMainId,
				rptNo : rptNos,
				startDate : dataDate,
				nowRpt : "Y"
			});
		}
	} else if (docType == "2") {// Vector done
		var needSend = (rptNos == "CLS180R01" || rptNos == "CLS180R02"
				|| rptNos == "CLS180R03" || rptNos == "CLS180R03B" || rptNos == "CLS180R04");
		if (needSend) {
			fileGrid.jqGrid("hideCol", "updateTime");
			fileGrid.jqGrid("hideCol", "dataDate");
			fileGrid.jqGrid("hideCol", "brName");
			fileGrid.jqGrid("showCol", "sendTime");
			fileGrid.jqGrid("showCol", "cfrmTime");
			fileGrid.jqGrid("showCol", "cfrmShow");
			fileGrid.setGridWidth(100);
		} else {
			fileGrid.jqGrid("hideCol", "sendTime");
			fileGrid.jqGrid("hideCol", "cfrmTime");
			fileGrid.jqGrid("hideCol", "cfrmShow");
			fileGrid.jqGrid("showCol", "brName");
			fileGrid.jqGrid("showCol", "updateTime");
			fileGrid.jqGrid("showCol", "dataDate");
			fileGrid.setGridWidth(100);
		}
		docType = '2';
		fileGrid.jqGrid("setGridParam", {// 重新設定grid需要查到的資料
			postData : {
				formAction : "queryFile",
				mainId : rptMainId,
				rptNo : rptNos,
				useMainId : (rptNos == "CLS180R06" || rptNos == "CLS180R11")
			},
			search : true,
			page : 1
		}).trigger("reloadGrid");
		$("#fileGridView").show();
		$("#btnView").show();
		$("#btnCreateReport").hide();
		$("#LMS180R02ADetail1GridShow").hide();
		$("#reportNo").val(rptNos);
		$("#btnReturnPage").show();
		$("#lms9511GridShow01").hide();
		$("#btnUpload").show();
		if (unitType == '4') {
			$("#btnLongError").show();
			if (rptNos == "CLS180R01" || rptNos == "CLS180R02"
					|| rptNos == "CLS180R03" || rptNos == "CLS180R03B"
					|| rptNos == "CLS180R04")
				$("#btnLongViewMemo").show();
		} else {
			if (needSend) {
				$("#btnSendDocTypeReport").show();
			} else {
				$("#btnSendDocTypeReport").hide();
			}
		}
	}
}

// 進入報表GRID顯示
function goSpeLMS180R02AGrid918(cellvalue, options, rowObject) {
	ilog.debug(rowObject);
	var rptNo = rowObject.rptNo;
	$("#LMS180R02ADetail1GridShow").show();
	$("#btnReturnPage").show();
	$("#lms9511GridShow01").hide();
	$("#btnCreateReport").hide();
	$("#btnUpload").hide();
	$("#searchActionName").val(rowObject.rptName);
	$("#btnFilter").hide();
	LMS180R02ADetail1Grid.reload({
		nowRpt : "Y",
		rptNo : rowObject.rptNo
	});
	$("#btnLongViewMemo").show();
}

// 下載檔案
function download(cellvalue, options, data) {
	// alert(data.reportOidFile);
	$.capFileDownload({
		handler : "simplefiledwnhandler",
		data : {
			fileOid : data.reportOidFile
		}
	});
}

function doPrintLMS180R02A() {
	$("#Lms9511v01From4").reset();
	$("#listBranch2Div").hide();
	listBranch("listBranch2");
	$("#lms9511thickbox4")
			.thickbox(
					{
						title : i18n.lms9511v01['L784M01a.print'],
						width : 560,
						height : 370,
						modal : false,
						align : 'left',
						valign : 'top',
						i18n : i18n.def,
						buttons : {
							"print" : function() {
								var printType2 = $(
										"input[name=printType2]:checked").val();
								var datas = [];
								var choiceBrNos
								if (printType2 == '2') {
									var gridview = $("#LMS180R02AGrid");
									var ids = gridview
											.getGridParam('selarrrow');
									for ( var i in ids) {
										datas
												.push(gridview
														.getRowData(ids[i]).oid);
									}
									if (datas.length == 0) {
										return CommonAPI
												.showMessage(i18n.def["grid_selector"]);
									}
								} else if (printType2 == '3') {
									var choiceBrNos = getBrNos("listBranch2");
									if (choiceBrNos.length == 0) {
										// val.inelbranch=請選分行
										return CommonAPI
												.showMessage(i18n.def['val.inelbranch']);
									}
								}
								$.form
										.submit({
											url : "../simple/FileProcessingService",
											target : "_blank",
											data : {
												choickBranchs : choiceBrNos,
												oids : datas,
												printType : printType2,
												printNote : $(
														"input[name=printNote]:checked")
														.val(),
												docKind : $(
														"input[name=docKind]:checked")
														.val(),
												docType : $(
														"input[name=docType2]:checked")
														.val(),
												mainId : $("#mainId").val(),
												fileDownloadName : "LMS180R02A.pdf",
												serviceName : "lms9511r05rptservice",
												areaBranchId : $(
														"#areaBranchId").val()
											}
										});
							},
							"close" : function() {
								$.thickbox.close();
							}
						}
					});// Ajax
}

function changePrintType2() {
	var printType2 = $("input[name=printType2]:checked").val();
	if (printType2 == '3') {
		$("#listBranch2Div").show();
	} else {
		$("#listBranch2Div").hide();
	}
}

function showYearMonth() {
	var printType = $('input:radio:checked[name="printType"]').val();
	if (printType == "allByAppr" || printType == "allByApprExcel") {
		$("#startYearMonth").show();
	} else {
		$("#startYearMonth").hide();
	}
}

// 列印
function doPrint(rptNo) {
	if (rptNo == 'LMS180R01' || rptNo == 'LMS180R17') {
		var rowid = LMS180R01Grid.getGridParam('selrow');
		if (!rowid) {
			return CommonAPI.showMessage(i18n.lms9511v01["L784S01A.error1"]);
		}
		var data = LMS180R01Grid.getRowData(rowid);
		download(null, null, data);
	} else if (rptNo == 'LMS180R15') {
		var rowid = LMS180R15Grid.getGridParam('selrow');
		if (!rowid) {
			return CommonAPI.showMessage(i18n.lms9511v01["L784S01A.error1"]);
		}
		var data = LMS180R15Grid.getRowData(rowid);
		download(null, null, data);
	} else if (rptNo == 'LMS180R16') {
		var LMS180R16Width = 250;
		// J-111-0329 常董會報告事項彙總及申報案件數統計表可下載成EXCEL檔
		if(userInfo.unitNo == '918' || userInfo.unitNo == "900"){
			$("#LMS180R16For918").show();// 多看得到選項-全行單月Excel
			LMS180R16Width = 350;// 把寬度拉寬一點
		}else{
			$("#LMS180R16For918").hide();// 隱藏選項-全行單月Excel
		}
		var test2 = $("#LMS180R16Print")
				.thickbox(
						{
							title : i18n.lms9511v01['L784M01a.ManageReport'],
							width : LMS180R16Width,
							height : 180,
							align : 'center',
							valign : 'bottom',
							modal : false,
							i18n : i18n.def,
							buttons : {
								"print" : function() {
									var printType = $(
											'input:radio:checked[name="printType"]')
											.val();
									var yearM = $("#yearM1").val();
									var caseDept = $(
											'input:radio:checked[name="caseDept"]')
											.val();
									if (printType == "allByAppr" || printType == "allByApprExcel") {
										var str = "";
										if (yearM == "") {
											// L784M01a.startDate=起始日期
											str = i18n.lms9511v01["L784M01a.startDate"]
											// val.ineldate=請輸入年月
											return CommonAPI.showMessage(str
													+ ""
													+ i18n.def["val.ineldate"]);
										}
										if (!yearM
												.match(/^\d{4}\-(0[1-9]|1[0-2])$/)) {
											// val.date2=日期格式錯誤(YYYY-MM)
											return CommonAPI
													.showMessage(i18n.def["val.date2"]);
										}

									}
									if (caseDept == "") {
										var str = "";
										str = i18n.lms9511v01["L180R02A.docType"] // L180R02A.docType=企/個金案件
																					// includeId.selData=請選擇一筆資料!!
										return CommonAPI
												.showMessage(str
														+ ""
														+ i18n.def["includeId.selData"]);
									}
									var brId = "";
									var tempMainId = "";
									if (printType == 'brNo') {
										var id = LMS180R16Grid
												.getGridParam('selrow');
										if (!id) {
											// action_006=請先選擇需「調閱」之資料列
											return CommonAPI
													.showMessage(i18n.def["action_006"]);
										}
										var data = LMS180R16Grid.getRowData(id);

										brId = data.brNo;
										tempMainId = data.mainId;
									}
									
									// J-111-0329 常董會報告事項彙總及申報案件數統計表可下載成EXCEL檔
									// 這個是下載成EXCEL，其餘保持為PDF
									if(printType == "allByApprExcel"){
										$.form.submit({
											url: __ajaxHandler,
											target : "_blank",
											data : {
												_pa : 'lmsdownloadformhandler',
												mainId : $("#mainId").val(),
												brNo : brId,
												printType : printType,
												startDate : yearM,
												caseDept : caseDept,
												fileDownloadName : 'LMS180R16.xls',
												serviceName : "lms9511r06rptservice"
											}
										});
									}else{
										$.form
										.submit({
											url : "../simple/FileProcessingService",
											target : "_blank",
											data : {
												mainId : $("#mainId").val(),
												brNo : brId,
												printType : printType,
												startDate : yearM,
												caseDept : caseDept,
												fileDownloadName : "LMS180R16.pdf",
												serviceName : "lms9511r02rptservice"
											}
										});
									}
								},
								"close" : function() {
									$.thickbox.close();
								}// 關閉
							}
						});// thickbox
	}
}

function padLeft(str, lenght) {
	str = str + "";
	if (str.length >= lenght)
		return str;
	else
		return padLeft("0" + str, lenght);
}

// 改變報表下拉式選單時 需要更換的報表時間顯示(企金)
function changeLMSData() {
	// 初始化所有的日期(若是有特殊需求請到RPTNO裡面自行修正
	$("#dataStartDate1").val(lastDateStart1);
	$("#dataEndDate1").val(lastDateEnd1);
	$("#dataStartDate2").val(lastDateStart2);
	$("#dataEndDate2").val(lastDateEnd2);
	$("#year").val(todayYear);
	$("#dataYM").val(todayYearMonth);
	$("#dataDate3").hide();
	$("#dataDate4").hide();
	$("#dataDate1").show();
	$("#dataDate2").show();
	$("#type").hide();
	$("tr.usedByLRS").hide();
	$("#LRS_rptNo_Desc").empty();

	$("#lms180R27FilterBrNo").hide();
	$("#lms180R27FilterCustId").hide();
	$("#lms180R29FilterPeNo").hide();
	
	//J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表
	$("#lms180R61FilterPeNo").hide();
	$("[id^='lms180R72Filter']").hide(); 
	//J-111-0600_05097_B1001 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
	$("[id^='lms180R68Filter']").hide();
	//J-111-0600_05097_B1002 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
	$("#show_dateType_lms180R68_3").hide();
	//J-111-0600_05097_B1003 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
	$("[class^='lms180R65Filter']").hide(); 
	//J-113-0125_05097_B1001 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
	$("[class^='proPertyFilter']").hide(); 
	 
	$("#exportTypeRow").hide();
	

	var rptNo = $("#searchAction" + docType).val();

	if (rptNo == 'LMS180R40') {
		$("#dataStartDate1").removeClass("required");
		$("#dataEndDate1").removeClass("required");
	} else {
		$("#dataStartDate1").addClass("required");
		$("#dataEndDate1").addClass("required");
	}

	if (rptNo == 'LMS180R01' || rptNo == 'LMS180R05' || rptNo == 'LMS161T02'
			|| rptNo == 'LMS180R10' || rptNo == 'LMS180R12'
			|| rptNo == 'LMS180R13' || rptNo == 'LMS180R58') {
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
		if (rptNo == 'LMS180R05') {
			$("#dataStartDate1").val(lastDateStart3);
		} else if (rptNo == 'LMS180R10') {
			$("#dataStartDate1").val(nextDateStart1);
			$("#dataEndDate1").val(nextDateEnd1);
		} else if (rptNo == 'LMS180R12' || rptNo == 'LMS180R13') {
			$("#dataStartDate1").val(lastDateStart4);
			$("#dataEndDate1").val(lastDateEnd4);
		}
	} else if (rptNo == 'LMS180R11') {
		$("#dataDate3").show();
		$("#dataDate4").show();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate2").val(lastDateStart5);
		$("#dataEndDate2").val(lastDateEnd2);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R18') {
		$("#dataDate3").show();
		$("#dataDate4").show();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate2").val(lastDateStart5);
		$("#dataEndDate2").val(lastDateEnd2);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R25') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();

	} else if (rptNo == 'LMS180R26') {
		$("#dataDate3").show();
		$("#dataDate4").show();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate2").val(lastDateStart5);
		$("#dataEndDate2").val(lastDateEnd2);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();

	} else if (rptNo == 'LMS180R27') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();

		$("#lms180R27FilterBrNo").show();
		$("#lms180R27FilterCustId").show();
	} else if (rptNo == 'LMS180R29') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").hide();
		$("#endDateRow").hide();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();

		// J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
		$.ajax({
			type : "POST",
			handler : "lms1205formhandler",
			data : {
				formAction : "getPrivateEquityData"
			},
			success : function(obj) {

				var $select = $("#privateEquityData");
				$("#privateEquityData").val('');
				$select.empty();
				var tempStr = "";
				var tmp = $("<option></option>");
				tmp.attr("value", "0000");
				tmp.text("0000." + i18n.lms9511v01["LMS9511X29.allPeNo"]);
				$select.append(tmp);

				if (obj.privateEquityData) {
					for ( var i in obj.privateEquityData) {

						var tmp = $("<option></option>");
						tmp.attr("value", obj.privateEquityData[i].key);
						tmp.text(obj.privateEquityData[i].key + "."
								+ obj.privateEquityData[i].val);
						$select.append(tmp);
					}

				}

			}

		});

		$("#lms180R29FilterPeNo").show();

	} else if (rptNo == 'LMS180R14' || rptNo == 'LMS180R02A'
			|| rptNo == 'LMS180R17' || rptNo == 'LMS180R37'
			|| rptNo == 'LMS180R42' || rptNo == 'LMS180R45'
			|| rptNo == 'LMS180R49') {
		$("#dataYM").val(lastDateStart2);
		$("#bgnDateRow").hide();
		$("#endDateRow").hide();
		$("#yearDateRow").hide();
		$("#dataYMRow").show();
	} else if (rptNo == 'LMS180R14Q') {
		// J-108-0192_05097_B1001 Web e-Loan企金授信新增每季海外營業單位授信報案考核彙總表
		$("#dataDate3").show();
		$("#dataDate4").show();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate2").val(lastDateStart5);
		$("#dataEndDate2").val(lastDateEnd2);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R15') {
		$("#dataStartDate1").val(yesDay);
		$("#dataEndDate1").val('');
		$("#bgnDateRow").show();
		$("#endDateRow").hide();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R16' || rptNo == 'LMS180R47') {
		$("#bgnDateRow").hide();
		$("#endDateRow").hide();
		$("#yearDateRow").show();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R19' || rptNo == 'LMS180R19B'
			|| rptNo == 'LMS180R20' || rptNo == 'LMS180R21'
			|| rptNo == 'LMS180R22' || rptNo == 'LMS180R23'
			|| rptNo == 'LMS180R23B' || rptNo == 'LMS180R24'
			|| rptNo == 'LMS180R28') {
		$("#bgnDateRow").hide();
		$("#endDateRow").hide();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();

		if (rptNo == 'LMS180R24') {
			$("tr#typeLRS_brRow").hide();
		} else if (rptNo == 'LMS180R28' && userInfo.unitNo == '918') {
			// 在此報表，授管處 傳到 ajax 的 remark 固定為 007|201，所以不顯示勾選 brNo 的 tr
			$("tr#typeLRS_brRow").hide();
		} else {
			$("tr#typeLRS_brRow").show();
		}

		if (rptNo == 'LMS180R19' || rptNo == 'LMS180R19B') {
			$("tr#typeLMS180R19Row").show();
		}

		if (rptNo == 'LMS180R21' || rptNo == 'LMS180R23'
				|| rptNo == 'LMS180R23B' || rptNo == 'LMS180R24'
				|| rptNo == 'LMS180R28') {
			if (rptNo == 'LMS180R21') {
				$("#LRS_rptNo_Desc").html(
						"<span class='text-red'>請輸入欲查詢新作或增額資料之起迄年月</span>");
			}
			// ------
			$("#dataDate1").hide();
			$("#dataDate3").show();
			$("#dataDate2").hide();
			$("#dataDate4").show();
			$("#bgnDateRow").show();
			$("#endDateRow").show();
		}
	} else if (rptNo == 'LMS180R30') {
		// J-105-0214-001 Web e-Loan 管理報表新增授信簽案已核准未能簽約撥貸原因表。
		$("#bgnDateRow").hide();
		$("#endDateRow").hide();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
		$("tr#typeLRS_brRow").show();
		$("#LRS_rptNo_Desc").html(
				"<span class='text-red'>請輸入欲查詢新作或增額資料之起迄年月</span>");
		$("#dataDate1").show();
		$("#dataDate3").hide();
		$("#dataDate2").show();
		$("#dataDate4").hide();
		$("#bgnDateRow").show();
		$("#endDateRow").show();

	} else if (rptNo == 'LMS180R31') {
		// J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
		$("#dataDate3").show();
		$("#dataDate4").show();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate2").val(lastDateStart5);
		$("#dataEndDate2").val(lastDateEnd2);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R32' || rptNo == 'LMS180R39') {
		// J-105-0321-001 Web e-Loan授信管理系統增加營運中心轄下分行往來客戶有全行通報異常情形彙總表

		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#bgnDateRow").hide();
		$("#endDateRow").hide();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R33') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R34') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").hide();
		$("#endDateRow").hide();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R35' || rptNo == 'LMS180R46'
			|| rptNo == 'LMS180R52' || rptNo == 'LMS180R59') { // All hide
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").hide();
		$("#endDateRow").hide();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R36' || rptNo == 'LMS180R50'
			|| rptNo == 'LMS180R51') { // 上個月1號~上個月最後一天
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
		if (rptNo == 'LMS180R50') {
			$("#exportTypeRow").show();
		}
	} else if (rptNo == 'LMS180R38') {
		// J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
		$("#dataDate3").show();
		$("#dataDate4").show();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate2").val(lastDateStart5);
		$("#dataEndDate2").val(lastDateEnd2);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R40') { // 簽報階段都更危老業務統計表
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate2").val(lastDateStart5);
		$("#dataEndDate2").val(lastDateEnd2);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
		$("#lms180R27FilterCustId").show();
	} else if (rptNo == 'LMS180R42T') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R54T') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R43') {
		$("#dataDate3").show();
		$("#dataDate4").show();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate2").val(lastDateStart5);
		$("#dataEndDate2").val(lastDateEnd2);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R44') {
		$("#dataDate3").show();
		$("#dataDate4").show();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate2").val(lastDateStart5);
		$("#dataEndDate2").val(lastDateEnd2);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R54') {
		$("#dataDate3").show();
		$("#dataDate4").show();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate2").val("2020-07");
		$("#dataEndDate2").val(lastDateEnd2);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R48') {
		// J-107-0342_05097_B1003 Web e-Loan授信系統新增覆審考核相關報表
		$("#dataDate3").show();
		$("#dataDate4").show();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate2").val(lastDateStart6);
		$("#dataEndDate2").val(lastDateEnd2);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R53') {
		// J-109-0132_05097_B1001 e-Loan授信系統新增「法令遵循自評檢核表」之抽測筆數所需之各檢核項目授信案件明細報表。
		$("#dataDate3").show();
		$("#dataDate4").show();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate2").val(lastDateStart7);
		$("#dataEndDate2").val(lastDateEnd2);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R55') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").hide();
		$("#endDateRow").hide();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R56') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val("2020-07-01");
		$("#dataEndDate1").val(yesDay);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R57') {
		// J-109-0226 建案餘屋及貸款資料控制管理
		$("#dataDate3").show();
		$("#dataDate4").show();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate2").val(lastDateStart5);
		$("#dataEndDate2").val(lastDateEnd2);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R60') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").hide();
		$("#endDateRow").hide();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();

	} else if (rptNo == 'LMS180R61') {
		// J-110-0018_05097_B1001 Web
		// e-Loan簽報書額度明細表中增列「兆豐百億挺你專案」及「協助農地工廠合法化融資貸款」兩項專案，並產生統計報表
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").hide();
		$("#endDateRow").hide();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();

		var obj = CommonAPI.loadOrderCombosAsList([ "lms140_projClass" ]);

		$("#projClass").setItems({
			item : obj.lms140_projClass,
			format : "{key}"
		});

		$("#lms180R61FilterPeNo").show();
	} else if (rptNo == 'LMS180R62') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").hide();
		$("#endDateRow").hide();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R63') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lms180R63_bgnDate);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R64') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();

		$("#lms180R27FilterBrNo").show();
		$("#lms180R27FilterCustId").show();
	} else if (rptNo == 'LMS180R65') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
		
		//J-111-0600_05097_B1003 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
		var obj = CommonAPI.loadOrderCombosAsList([ "L120M01A_docKind"  ]); 
		$("#docKind_180R65").setItems({
			format : "{key}",
			size: "2",
            item: obj.L120M01A_docKind,
			clear : true,
			itemType: 'checkbox' 
		});
		
		$("[class^='lms180R65Filter']").show(); 
		 
		
	} else if (rptNo == 'LMS180R66') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R67') { // 上個月1號~上個月最後一天
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R68') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
		//J-111-0600_05097_B1001 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
		$("[id^='lms180R68Filter']").show(); 
		//J-111-0600_05097_B1002 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
		$("#show_dateType_lms180R68_3").hide();
		
	} else if (rptNo == 'LMS180R69') { // 上個月1號~上個月最後一天
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R72') {
		//J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表
		//LMS180R72 企金授信核准案件BIS評估表
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();

		var obj = CommonAPI.loadOrderCombosAsList([ "L120M01A_docKind","lms1205m01_caseLvl","lms180R72FilterDocStatus","caseOutputType_180R72" ]); 

		$("#docKind_180R72").setItems({
			format : "{key}",
			size: "2",
            item: obj.L120M01A_docKind,
			clear : true,
			itemType: 'checkbox' 
		});
		$("#caseLvl_180R72").setItems({
			format : "{key}",
			size: "2",
	        item: obj.lms1205m01_caseLvl,
			clear : true,
			itemType: 'checkbox' 
		});
		$("#docStatus_180R72").setItems({
			format : "{key}",
			size: "2",
	        item: obj.lms180R72FilterDocStatus,
			clear : true,
			itemType: 'select' 
		});
		$("[id^='lms180R72Filter']").show(); 
		
		//J-113-0328 調整企金授信核准案件BIS評估表
		$("#caseOutputType_180R72").setItems({
			format : "{key}",
			size: "2",
	        item: obj.caseOutputType_180R72,
			clear : true,
			itemType: 'radio' 
		});
	} else if (rptNo == 'LMS180R73') {
		// J-111-0411_05097_B1002 Web e-Loan企金授信新增不動產授信例外管理相關功能
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R74') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val("2023-04-17");
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
		
		
		//J-113-0125_05097_B1001 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
		var obj = CommonAPI.loadOrderCombosAsList([ "lms1405s02_proPerty"  ]); 
		$("#proPerty").setItems({
			format : "{key}",
			size: "2",
            item: obj.lms1405s02_proPerty,
			clear : true,
			itemType: 'checkbox' 
		});
		 
		$("[class^='proPertyFilter']").show(); 
	 
		//J-113-0125_05097_B1001 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
		//預設勾選
		var default_proPertyArr = proPerty_LMS180R74.split(",");
        for (var i in default_proPertyArr) {
        	$( "[name=proPerty][value="+default_proPertyArr[i]+"]").attr("checked" , true );
        }	
	 
	} else if (rptNo == 'LMS180R75') {
        $("#dataDate3").hide();
        $("#dataDate4").hide();
        $("#dataDate1").show();
        $("#dataDate2").show();
        $("#dataStartDate1").val(lastDateStart1);
        $("#dataEndDate1").val(lastDateEnd1);
        $("#bgnDateRow").show();
        $("#endDateRow").show();
        $("#yearDateRow").hide();
        $("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R76') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(todayYear + "-01-01");
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	} else if (rptNo == 'LMS180R77') {
		$("#dataDate3").hide();
		$("#dataDate4").hide();
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataStartDate1").val(lastDateStart1);
		$("#dataEndDate1").val(lastDateEnd1);
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
	}
}

function showL180R01Detail() {
	var row = $("#LMS180R01Grid").getGridParam('selrow');
	if (!row) {
		return CommonAPI.showMessage(i18n.lms9511v01["L784S01A.error1"]);
	}
	var data = $("#LMS180R01Grid").getRowData(row);
	LMS180R01DetailGrid.reload({
		mainId : data.mainId
	});
	var test2 = $("#LMS180R01GridDetailShow")
			.thickbox(
					{
						title : i18n.lms9511v01['L784M01a.newInfo2a'],
						width : 850,
						height : 510,
						align : 'left',
						// valign : 'bottom',
						modal : false,
						i18n : i18n.def,
						buttons : {
							"lognView" : function() {
								var id = LMS180R01DetailGrid
										.getGridParam('selarrrow');
								var count = 0;
								var content = "";
								for ( var i = 0; i < id.length; i++) {
									if (id[i] != "") {
										var data = LMS180R01DetailGrid
												.getRowData(id[i]);
										if (content.length != 0) {
											content = content + "|" + data.oid;
										} else {
											content = content + data.oid;
										}
										count++;
									}
								}
								if (count == 0) {
									CommonAPI
											.showMessage(i18n.def['grid.selrow']);
								} else {
									$
											.ajax({
												handler : "lms9511m01formhandler",
												type : "POST",
												dataType : "json",
												data : {
													formAction : "queryL784S01a",
													mainOids : content
												},
												success : function(json) {
													$("#hqCheckDate").val(
															json.hqCheckDate);
													$("#hqCheckMemo").val(
															json.hqCheckMemo);
													var test3 = $(
															"#LMS180R01GridDetailShow2")
															.thickbox(
																	{
																		title : i18n.lms9511v01['L784M01a.newInfo2b'],
																		width : 560,
																		height : 380,
																		align : 'left',
																		modal : false,
																		i18n : i18n.def,
																		buttons : {
																			"saveData" : function() {
																				if ($(
																						"#lms9511v01From2")
																						.valid()) {
																					$
																							.ajax({
																								type : "POST",
																								handler : "lms9511m01formhandler",
																								data : $
																										.extend(
																												$(
																														"#lms9511v01From2")
																														.serializeData(),
																												{
																													formAction : "saveRemarkNotes",
																													oids : content,
																													mainIdTemp : data.mainId
																												}),
																								success : function(
																										responseData) {
																									LMS180R01DetailGrid
																											.trigger("reloadGrid");
																									$(
																											"#LMS180R01Grid")
																											.trigger(
																													"reloadGrid");
																									$(
																											"#lms9511v01From2")
																											.reset();
																								}
																							});// ajax
																					$.thickbox
																							.close();
																				}
																			},
																			"close" : function() {
																				$.thickbox
																						.close();
																			}// 關閉
																		}
																	});// thickbox
												}

											});// ajax
								}
							},//
							"print" : function() {
								$.form.submit({
									url : "../simple/FileProcessingService",
									target : "_blank",
									data : {
										mainId : data.mainId,
										fileDownloadName : "LMS180R0101.pdf",
										serviceName : "lms9511r03rptservice"
									}
								});
							},//
							"close" : function() {
								$.thickbox.close();
							}// 關閉
						}
					// bottons

					});// thickbox
}

function showL180R02ADetail() {
	var gridview = $("#LMS180R02AGrid");
	var datas = [];
	var ids = gridview.getGridParam('selarrrow');
	for ( var i in ids) {
		datas.push(gridview.getRowData(ids[i]).oid);
	}
	if (datas.length == 0) {
		return CommonAPI.showMessage(i18n.lms9511v01["L784S01A.error1"]);
	}
	$
			.ajax({
				handler : "lms9511m01formhandler",
				type : "POST",
				dataType : "json",
				data : {
					formAction : "queryLMSRPT",
					mainIdTemp : $("#mainId").val()
				},
				success : function(json) {
					$("#hqCheckDate").val(json.hqCheckDate);
					$("#hqCheckMemo").val(json.hqCheckMemo);
					$("#LMS180R01GridDetailShow2")
							.thickbox(
									{
										title : i18n.lms9511v01['L784M01a.newInfo2b'],
										width : 560,
										height : 380,
										align : 'left',
										modal : false,
										i18n : i18n.def,
										readOnly : false,
										buttons : {
											"saveData" : function() {
												if ($("#lms9511v01From2")
														.valid()) {
													$
															.ajax({
																type : "POST",
																handler : "lms9511m01formhandler",
																data : $
																		.extend(
																				$(
																						"#lms9511v01From2")
																						.serializeData(),
																				{
																					formAction : "saveL180R02ARemarkNotes",
																					oids : datas
																				}),
																success : function(
																		responseData) {
																	gridview
																			.trigger("reloadGrid");
																	$(
																			"#lms9511v01From2")
																			.reset();
																}
															});// ajax
													$.thickbox.close();
												}
											},
											"close" : function() {
												$.thickbox.close();
											}// 關閉
										}
									});// thickbox
				}

			});// ajax
}

function showL180R02ADetail() {
	var gridview = $("#LMS180R02AGrid");
	var datas = [];
	var ids = gridview.getGridParam('selarrrow');
	for ( var i in ids) {
		datas.push(gridview.getRowData(ids[i]).oid);
	}
	if (datas.length == 0) {
		return CommonAPI.showMessage(i18n.lms9511v01["L784S01A.error1"]);
	}
	$
			.ajax({
				handler : "lms9511m01formhandler",
				type : "POST",
				dataType : "json",
				data : {
					formAction : "queryLMSRPT",
					mainIdTemp : $("#mainId").val()
				},
				success : function(json) {
					$("#hqCheckDate").val(json.hqCheckDate);
					$("#hqCheckMemo").val(json.hqCheckMemo);
					$("#LMS180R01GridDetailShow2")
							.thickbox(
									{
										title : i18n.lms9511v01['L784M01a.newInfo2b'],
										width : 560,
										height : 380,
										align : 'left',
										modal : false,
										i18n : i18n.def,
										readOnly : false,
										buttons : {
											"saveData" : function() {
												if ($("#lms9511v01From2")
														.valid()) {
													$
															.ajax({
																type : "POST",
																handler : "lms9511m01formhandler",
																data : $
																		.extend(
																				$(
																						"#lms9511v01From2")
																						.serializeData(),
																				{
																					formAction : "saveL180R02ARemarkNotes",
																					oids : datas
																				}),
																success : function(
																		responseData) {
																	gridview
																			.trigger("reloadGrid");
																	$(
																			"#lms9511v01From2")
																			.reset();
																}
															});// ajax
													$.thickbox.close();
												}
											},
											"close" : function() {
												$.thickbox.close();
											}// 關閉
										}
									});// thickbox
				}

			});// ajax
}

function showLMS180R16Detail() {
	var gridView = $("#LMS180R16Grid");
	var ids = gridView.getGridParam('selrow');
	if (!ids) { // grid_selector=請選擇資料
		return CommonAPI.showMessage(i18n.lms9511v01["L784S01A.error1"]);
	}
	var data = gridView.getRowData(ids);
	LMS180R16DetailGrid.reload({
		mainId : data.mainId,
		brNo : data.brNo
	});
	var test2 = $("#LMS180R16GridDetailShow").thickbox(
			{
				title : i18n.lms9511v01['L784M01a.newInfo2a'] + "-"
						+ data.brName,
				width : 850,
				height : 510,
				align : 'left',
				// valign : 'bottom',
				modal : false,
				i18n : i18n.def,
				buttons : {
					"saveData" : function() {
						// 寫回額度明細表
						LMS180R16DetailGrid.jqGrid('saveRow', lastSel, false,
								'clientArray');
						var ids = LMS180R16DetailGrid.jqGrid('getDataIDs');
						// 用來放列印順序跟oid
						var json = {};
						var checkArray1 = LMS180R16DetailGrid
								.getCol("cItem12Rec");
						var checkArray2 = LMS180R16DetailGrid
								.getCol("cItem12Amt");

						for ( var id in ids) {
							var data = LMS180R16DetailGrid.jqGrid('getRowData',
									ids[id]);
							json[data.oid] = data.cItem12Rec + "|"
									+ data.cItem12Amt;
						}
						$.ajax({
							handler : "lms9511m01formhandler",
							action : "saveL784S07AData",
							data : {
								mainIdTemp : data.mainId,
								data : JSON.stringify(json)
							},
							success : function(obj) {
								LMS180R16Grid.trigger("reloadGrid");
							}
						});
					},//
					"print" : function() {
						$.form.submit({
							url : "../simple/FileProcessingService",
							target : "_blank",
							data : {
								mainId : data.mainId,
								brNo : data.brNo,
								fileDownloadName : "LMS180R16.pdf",
								serviceName : "lms9511r02rptservice"
							}
						});
					},//
					"close" : function() {
						$.thickbox.close();
					}// 關閉
				}
			// bottons

			});// thickbox
}

function execLMS180R16Data() {
	// 跳出[執行此功能會至中心主機將「所有分行」當月分資料引進，耗時較久，請問是否確定執行？]彈窗
	$("#LMS180R16MSG").thickbox({
		title : i18n.lms9511v01['L784M01a.newInfo2'],
		width : 350,
		height : 200,
		modal : false,
		align : 'center',
		valign : 'bottom',
		i18n : i18n.def,
		buttons : {
			"sure" : function() {
				$.thickbox.close();
				var test2 = $("#LMS180R16YM").thickbox({
					title : i18n.lms9511v01['L784M01a.newInfo1'],
					width : 250,
					height : 150,
					align : 'center',
					valign : 'bottom',
					modal : false,
					i18n : i18n.def,
					buttons : {
						"sure" : function() {
							// 起始日期
							var yearM = $("#yearM").val();
							if ($("#lms9511v01LMS180R16From").valid()) {
								if (checkYM(yearM)) {
									$.ajax({
										type : "POST",
										handler : "lms9511m01formhandler",
										data : {
											formAction : "importLMS180R16Data",
											startDate : yearM
										},
										success : function(responseData) {
											// 更新Grid內容
										}
									});// Ajax
									$.thickbox.close();
								}
							}
						},
						"close" : function() {
							$.thickbox.close();
						}// 關閉
					}
				});// thickbox
			},
			"cancel" : function() {
				$.thickbox.close();
			}
		}
	});// thickbox
}

function execLMS180R17Data(startDate, endDate) {
	// (8. 各級授權範圍內承做授信案件統計表)
	// 跳出[執行此功能會至中心主機將「所有分行」當月分資料引進，耗時較久，請問是否確定執行？]彈窗
	$("#LMS180R17MSG").thickbox({
		title : i18n.lms9511v01['L784M01a.newInfo2'],
		width : 350,
		height : 150,
		modal : false,
		align : 'center',
		valign : 'bottom',
		i18n : i18n.def,
		buttons : {
			"sure" : function() {
				$.thickbox.close();
				$.ajax({
					type : "POST",
					handler : "lms9511m01formhandler",
					data : {
						formAction : "addBatchData",
						rptNo : $("#reportNo").val(),
						docType : $("input[name=docType]:checked").val(),// $("#docType").val(),
						dataStartDate : startDate,
						dataEndDate : endDate,
						dateType : $("select#dateType").val()
					},
					success : function(responseData) {
						L9511v01Grid01.trigger("reloadGrid");
					}
				});
				$.thickbox.close();
			},
			"cancel" : function() {
				$.thickbox.close();
			}
		}
	});
}

// 檢核日期格式YYYY-MM
function checkYM(dateYM) {
	if (!dateYM.match(/^\d{4}\-(0[1-9]|1[0-2])$/)) {
		// val.date2=日期格式錯誤(YYYY-MM)
		return CommonAPI.showMessage(i18n.def["val.date2"]);
	}
	return true;
}
// ================================ START GRID
// ====================================
// 顯示現有報表清單(企金+個金)
L9511v01Grid01 = $("#L9511v01Grid01").iGrid({
	handler : 'lms9511gridhandler',
	height : 350, // 設定高度
	rownumbers : true,
	action : "queryLMSRPT",
	colModel : [ {
		colHeader : "oid",
		name : 'oid',
		hidden : true
	// 是否隱藏
	}, {
		colHeader : "mainId",
		name : 'mainId',
		hidden : true
	// 是否隱藏
	}, {
		colHeader : "rptNo",
		name : 'rptNo',
		hidden : true
	// 是否隱藏
	}, {
		colHeader : "reportOidFile",
		name : 'reportOidFile',
		hidden : true
	// 是否隱藏
	}, {
		colHeader : "branch",
		name : 'branch',
		hidden : true
	// 是否隱藏
	}, {
		colHeader : "sendTime",// sendTime
		name : 'sendTime',
		hidden : true
	// 是否隱藏
	}, {
		colHeader : i18n.lms9511v01["LMSRPT.dataDate"], // 資料日期
		align : "center",
		width : 50, // 設定寬度
		sortable : true, // 是否允許排序
		formatter : 'date',
		formatoptions : {
			srcformat : 'Y-m-d',
			newformat : 'Y-m'
		},
		name : 'dataDate' // col.id
	}, {
		colHeader : i18n.lms9511v01["LMSRPT.rptName"], // 報表名稱
		align : "left",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		// formatter : 'click',
		// onclick : function,
		formatter : 'click',
		onclick : openDoc001,
		name : 'rptName' // col.id
	}, {
		colHeader : i18n.lms9511v01["LMSRPT.updateTime"], // 建立時間
		align : "left",
		width : 50, // 設定寬度
		sortable : true, // 是否允許排序
		// formatter : 'click',
		// onclick : function,
		formatter : 'date',
		formatoptions : {
			srcformat : 'Y-m-d H:i:s',
			newformat : 'Y-m-d H:i:s'
		},
		name : 'updateTime' // col.id
	} ],
	ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		var data = L9511v01Grid01.getRowData(rowid);
		openDoc001(null, null, data);
	}
});

function openDoc001(cellvalue, options, data) {
	if (data.rptNo == 'LMS180R01' || data.rptNo == 'LMS180R02A'
			|| data.rptNo == 'LMS180R15' || data.rptNo == 'LMS180R16'
			|| data.rptNo == 'CLS180R01' || data.rptNo == 'CLS180R02'
			|| data.rptNo == 'CLS180R03' || data.rptNo == 'CLS180R03B'
			|| data.rptNo == 'CLS180R04' || data.rptNo == 'CLS180R06'
			|| data.rptNo == 'CLS180R11') {
		if (data.rptNo == 'LMS180R16') {
			$("#yearM1").val(lastDateStart2);
			$("#yearM").val(lastDateStart2);
			// $("#yearM1").val(data.dataDate);
			// $("#yearM").val(data.dataDate);
		}

		$("#mainId").val(data.mainId);
		$("#reportNo").val(data.rptNo);
		if (data.branch == '918' && data.rptNo == 'LMS180R02A') {
			$("#deep").val('1');
			$("#mainId918").val(data.mainId);
			goSpeLMS180R02AGrid918(null, null, data);
		} else {
			goSpeGrid(null, null, data);
		}
	} else {
		download(null, null, data);
	}
}

// 已敘做案件清單
LMS180R01Grid = $("#LMS180R01Grid").iGrid({
	localFirst : true,
	handler : 'lms9511gridhandler',
	height : 350, // 設定高度
	sortname : 'branch', // 預設排序
	rownumbers : true,
	action : "querySpeReport",
	colModel : [ {
		name : 'oid',
		hidden : true
	// 是否隱藏
	}, {
		name : 'mainId', // col.id
		hidden : true
	// 是否隱藏
	}, {
		colHeader : "reportOidFile",
		name : 'reportOidFile',
		hidden : true
	// 是否隱藏
	}, {
		colHeader : i18n.lms9511v01["L784M01a.ownBrId2"], // 分行名稱
		align : "center",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		formatter : 'click',
		onclick : download,
		name : 'branch' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784M01a.dataDate"], // 資料年月
		align : "center",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'dataDate', // col.id
		formatter : 'date',
		formatoptions : {
			srcformat : 'Y-m-d',
			newformat : 'Y-m'
		}
	}, {
		colHeader : i18n.lms9511v01["L784M01a.sendTime"], // 分行傳送時間
		align : "center",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'sendTime' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784M01a.cfrmTime"], // 核准日期
		align : "center",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'cfrmTime' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784M01a.cfrmFlag"], // 核備註記
		align : "center",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'cfrmFlag' // col.id
	} ],
	ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		var data = LMS180R01Grid.getRowData(rowid);
		download(null, null, data);
	}
});

LMS180R01DetailGrid = $("#LMS180R01GridDetail").iGrid({
	localFirst : true,
	handler : 'lms9511gridhandler',
	height : 350, // 設定高度
	rownumbers : true,
	multiselect : true,
	action : "queryL784s01a",
	// sortname : 'oid', //預設排序
	// multiselect : true, //是否開啟多選
	colModel : [ {
		colHeader : "oid",
		name : 'oid',
		hidden : true
	// 是否隱藏
	}, {
		colHeader : "mainId",
		name : 'mainId',
		hidden : true
	// 是否隱藏
	}, {
		colHeader : i18n.lms9511v01["L784S01A.endDate"], // 核定日
		align : "center",
		width : 50, // 設定寬度
		sortable : true, // 是否允許排序
		// formatter : 'click',
		// onclick : function,
		name : 'endDate' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784S01A.custId"], // 統一編號
		align : "left",
		width : 50, // 設定寬度
		sortable : true, // 是否允許排序
		// formatter : 'click',
		// onclick : function,
		name : 'custId' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784S01A.custName"], // 戶名
		align : "left",
		width : 50, // 設定寬度
		sortable : true, // 是否允許排序
		// formatter : 'click',
		// onclick : function,
		name : 'custName' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784S01A.cntrNo"], // 額度序號
		align : "left",
		width : 50, // 設定寬度
		sortable : true, // 是否允許排序
		// formatter : 'click',
		// onclick : function,
		name : 'cntrNo' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784S01A.currentApplyAmt"], // 額度(金額)
		align : "right",
		width : 50, // 設定寬度
		sortable : true, // 是否允許排序
		// formatter : GridFormatter.number['addComma'],
		name : 'currentApplyAmt', // col.id
		formatter : 'currency',
		formatoptions : {
			thousandsSeparator : ",",
			removeTrailingZero : true,
			decimalPlaces : 2
		// 小數點到第幾位
		}
	}, {
		colHeader : i18n.lms9511v01["L784S01A.hqCheckDate"], // 總處核備日
		name : 'hqCheckDate',
		align : "center",
		width : 50,
		sortable : true
	}, {
		colHeader : i18n.lms9511v01["L784S01A.hqCheckMemo"], // 備註
		align : "left",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'hqCheckMemo'
	} ]
});

// 營運中心已敘做案件清單(918使用的)
LMS180R02ADetail1Grid = $("#LMS180R02ADetail1Grid").iGrid({
	localFirst : true,
	handler : 'lms9511gridhandler',
	height : 350, // 設定高度
	rownumbers : true,
	rowNum : 40,
	multiselect : true,
	hideMultiselect : false,
	action : "querySpeLMS180R02AReport",
	colModel : [ {
		name : 'oid',
		hidden : true
	// 是否隱藏
	}, {
		name : 'mainId', // col.id
		hidden : true
	// 是否隱藏
	}, {
		colHeader : "rptNo",
		name : 'rptNo',
		hidden : true
	// 是否隱藏x
	}, {
		colHeader : "rptName",
		name : 'rptName',
		hidden : true
	// 是否隱藏
	}, {
		colHeader : "jingBan",// 暫存營運中心代碼
		name : 'jingBan',
		hidden : true
	// 是否隱藏
	}, {
		colHeader : i18n.lms9511v01["L180R02A.areaBranchId"], // 營運中心名稱
		align : "left",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		formatter : 'click',
		onclick : goSpeGrid,
		name : 'branch' // col.id
	}, {
		colHeader : i18n.lms9511v01["L180R02A.sendTime"], // 傳送授管處時間
		align : "center",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'sendTime' // col.id
	}, {
		colHeader : i18n.lms9511v01["L180R02A.cfrmFlag"], // 核備註記
		align : "center",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'cfrmFlag' // col.id
	} ],
	ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		var data = LMS180R02ADetail1Grid.getRowData(rowid);
		goSpeGrid(null, null, data);
	}
});

// 營運中心已敘做案件清單(918 and 營運中心使用的)
LMS180R02AGrid = $("#LMS180R02AGrid").iGrid({
	localFirst : true,
	handler : 'lms9511gridhandler',
	height : 350, // 設定高度
	rownumbers : true,
	rowNum : 40,
	multiselect : true,
	hideMultiselect : false,
	action : "querySpeLMS180R02AReport2",
	colModel : [ {
		name : 'oid',
		hidden : true
	// 是否隱藏
	}, {
		name : 'mainId', // col.id
		hidden : true
	// 是否隱藏
	}, {
		colHeader : i18n.lms9511v01["L180R02A.approver"], // 經辦
		align : "left",
		width : 25, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'approver' // col.id
	}, {
		colHeader : i18n.lms9511v01["L180R02A.docKind"], // 授權內外
		align : "center",
		width : 25, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'docKind' // col.id
	}, {
		colHeader : i18n.lms9511v01["L180R02A.docType"], // 企個金
		align : "center",
		width : 15, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'docType' // col.id
	}, {
		colHeader : i18n.lms9511v01["L180R02A.brno"], // 分行名稱
		align : "left",
		width : 30, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'brno' // col.id
	}, {
		colHeader : i18n.lms9511v01["L180R02A.caseNo"], // 案號
		align : "center",
		width : 50, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'caseNo' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784S01A.cntrNo"], // 額度序號
		align : "center",
		width : 30, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'cntrNo' // col.id
	}, {
		colHeader : i18n.lms9511v01["L180R02A.lnSubject"], // 授信科目
		align : "left",
		width : 40, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'lnSubject' // col.id
	}, {
		colHeader : i18n.lms9511v01["L180R02A.hqCheckDate"], // 備查日期
		align : "center",
		width : 20, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'hqCheckDate' // col.id
	}, {
		colHeader : i18n.lms9511v01["L180R02A.hqCheckMemo"], // 備註
		align : "left",
		width : 15, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'hqCheckMemo' // col.id
	}, {
		colHeader : i18n.lms9511v01["L180R02A.audit"], // 審核不通過
		align : "center",
		width : 10, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'audit' // col.id
	} ]
});

// 已敘做案件清單
LMS180R15Grid = $("#LMS180R15Grid").iGrid({
	localFirst : true,
	handler : 'lms9511gridhandler',
	height : 350, // 設定高度
	sortname : 'branch', // 預設排序
	rownumbers : true,
	action : "querySpeLMS180R15Report",
	colModel : [ {
		name : 'oid',
		hidden : true
	// 是否隱藏
	}, {
		name : 'mainId', // col.id
		hidden : true
	// 是否隱藏
	}, {
		colHeader : "reportOidFile",
		name : 'reportOidFile',
		hidden : true
	// 是否隱藏
	}, {
		colHeader : i18n.lms9511v01["L784M01a.ownBrId2"], // 分行名稱
		align : "center",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		formatter : 'click',
		onclick : download,
		name : 'branch' // col.id
	}, {
		colHeader : i18n.lms9511v01["LMSRPT.dataDate"], // 資料日期
		align : "center",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'dataDate', // col.id
		formatter : 'date',
		formatoptions : {
			srcformat : 'Y-m-d',
			newformat : 'Y-m-d'
		}
	}, {
		colHeader : i18n.lms9511v01["L784M01a.sendTime2"], // 營運中心傳送時間
		align : "center",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'sendTime' // col.id
	} ],
	ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		var data = LMS180R15Grid.getRowData(rowid);
		download(null, null, data);
	}
});

LMS180R16Grid = $("#LMS180R16Grid").iGrid({
	localFirst : true,
	rownumbers : true,
	handler : 'lms9511gridhandler',
	height : 350, // 設定高度
	sortname : 'brNo', // 預設排序
	action : "queryL784s07a",
	// multiselect : true,
	colModel : [ {
		name : 'brNo', // col.id
		hidden : true
	// 是否隱藏
	}, {
		name : 'mainId', // col.id
		hidden : true
	// 是否隱藏
	}, {
		colHeader : i18n.lms9511v01["L784M01a.yearDate"], // 資料年度
		align : "center",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'apprYY' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784M01a.ownBrId2"], // 分行別
		align : "left",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		// formatter : 'click',
		// onclick: showLMS180R16Detail,
		name : 'brName' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784M01a.rptType2"], // 資料性質
		align : "center",
		width : 100, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'caseDept' // col.id
	} ],
	ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		showLMS180R16Detail();
	}

});

LMS180R16DetailGrid = $("#LMS180R16GridDetail").iGrid({
	localFirst : true,
	handler : 'lms9511gridhandler',
	height : 350, // 設定高度
	cellsubmit : 'clientArray',
	sortname : 'apprMM', // 預設排序
	action : "queryL784s07aForTotalMonth",
	colModel : [ {
		colHeader : "oid",
		name : 'oid',
		hidden : true
	// 是否隱藏
	}, {
		colHeader : "mainId",
		name : 'mainId',
		hidden : true
	// 是否隱藏
	}, {
		colHeader : "brNo",
		name : 'brNo',
		hidden : true
	// 是否隱藏
	}, {
		colHeader : i18n.lms9511v01["L784S07A.apprYY"], // 案件核准年度(民國年)
		align : "center",
		width : 50, // 設定寬度
		// formatter : 'click',
		// onclick : function,
		name : 'apprYY' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784S07A.apprMM"], // 案件核准月份
		align : "center",
		width : 50, // 設定寬度
		// formatter : 'click',
		// onclick : function,
		name : 'apprMM' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784S07A.cItem12Rec"], // 逾放展期、轉正常(筆數)
		align : "right",
		width : 50, // 設定寬度
		editable : true,
		editrules : {
			number : true
		},
		edittype : 'text',
		editoptions : {
			size : 5,
			maxlength : 5
		},
		sortable : true, // 是否允許排序
		// formatter : GridFormatter.number['addComma'],
		name : 'cItem12Rec' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784S07A.cItem12Amt"], // 逾放展期、轉正常(金額)
		align : "right",
		width : 50, // 設定寬度
		editable : true,
		editrules : {
			number : true
		},
		edittype : 'text',
		editoptions : {
			size : 10,
			maxlength : 15
		},
		sortable : true, // 是否允許排序
		// formatter : GridFormatter.number['addComma'],
		name : 'cItem12Amt' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784S07A.updater"], // 異動人員
		align : "left",
		width : 50, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'updater' // col.id
	} ],
	onSelectRow : function(id) {
		if (id && id != lastSel) {
			$("#LMS180R16GridDetail").saveRow(lastSel, false, 'clientArray');
			$('#LMS180R16GridDetail').restoreRow(lastSel);
			lastSel = id;
		}
		$('#LMS180R16GridDetail').editRow(id, false);
	}
});
// Vector done
fileGrid = $("#fileGrid").iGrid({
	handler : 'lms9511gridhandler',
	height : 350, // 設定高度
	sortname : 'updateTime', // 預設排序
	rownumbers : true,
	action : "queryFile",
	rowNum : 15,
	colModel : [ {
		name : 'oid',
		hidden : true
	// 是否隱藏
	}, {
		name : 'mainId', // col.id
		hidden : true
	// 是否隱藏
	}, {
		name : 'reportOidFile', // col.id
		hidden : true
	// 是否隱藏
	}, {
		name : 'rptMainId', // col.id
		hidden : true
	// 是否隱藏
	}, {
		colHeader : 'branch', // 分行代碼
		hidden : true,
		name : 'branch' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784M01a.ownBrId2"], // 分行名稱
		align : "center",
		width : 50, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'brName' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784M01a.startDate"], // 起始日
		align : "center",
		width : 50, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'bgnDate' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784M01a.endDate"], // 迄日
		align : "center",
		width : 50, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'endDate' // col.id
	}, {
		colHeader : i18n.lms9511v01["LMSRPT.rptName"], // 報表名稱
		align : "center",
		width : 120, // 設定寬度
		sortable : true, // 是否允許排序
		formatter : 'click',
		onclick : downloadFile2,
		name : 'rptName' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784M01a.sendTime"], // 分行傳送時間
		align : "center",
		width : 80, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'sendTime' // col.id
	}, {
		colHeader : 'cfrmFlag', // 是否核准
		hidden : true,
		name : 'cfrmFlag' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784M01a.cfrmFlag"], // 是否核准
		align : "center",
		width : 40, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'cfrmShow' // col.id
	}, {
		colHeader : i18n.lms9511v01["L784M01a.cfrmTime"], // 核准日期
		align : "center",
		width : 80, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'cfrmTime' // col.id
	}, {
		colHeader : i18n.lms9511v01["LMSRPT.updateTime"], // 建立日期
		align : "center",
		width : 50, // 設定寬度
		sortable : true, // 是否允許排序
		name : 'updateTime', // col.id
		formatter : 'date',
		formatoptions : {
			srcformat : 'Y-m-d hh:MM:ss',
			newformat : 'Y-m-d'
		}

	}, {
		colHeader : i18n.def['uploadFile.srcFileDesc'],// 檔案說明
		name : 'fileDesc',
		width : 100,
		align : "center",
		sortable : true
	} ],
	ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		var data = fileGrid.getRowData(rowid);
		downloadFile2(null, null, data);
	}
});

function downloadFile2(cellvalue, options, data) {
	$.capFileDownload({
		handler : "simplefiledwnhandler",
		data : {
			fileOid : data.oid
		}
	});
}

// ================================ END GRID
// ====================================
