var initDfd = initDfd || new $.Deferred();
var _handler = "lms2411m01formhandler";
//J-110-0505_05097_B1001 Web e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
var grid_p = "grid_printL140M01A";
var grid_c = "grid_printCollSet";
var print_url = "../../simple/FileProcessingService";

$(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	var initControl_lockDoc = false;
	$.form.init({
		formHandler:_handler, 
		formAction:'query', 
		loadSuccess:function(json){			
			if(json.page=="02"){
				buildOverdue((json.docKind=="P")?true:false, json.overdueYN, json.overdueP);
			}
			
			// 控制頁面 Read/Write
			if(!$("#buttonPanel").find("#btnSave").is("button") || json.lock) {
				tabForm.lockDoc();
				initControl_lockDoc = true;
				json['initControl_lockDoc'] = initControl_lockDoc;
			}
			
			//覆審組不應 click 受檢單位的 btn【EX:輸入洽辦情形、呈主管、覆核】
			var _fromUnitNo = userInfo.ssoUnitNo; 
			if(_fromUnitNo.substring(0,1)=="9" && _fromUnitNo!="900"){
				var disableArr = ["btnBranchComm", "btnSend", "btnAccept"];
				$.each( disableArr, function(idx,btnId){					
					if(btnPanel.find("#"+btnId).is("button")){
						btnPanel.find("#"+btnId).addClass(" ui-state-disabled ").prop("disabled", true);
					}
            	});
			}
			if(json.processingBranchComm=="Y"){
				btnPanel.find("#btnOnlyUp491").addClass(" ui-state-disabled ").prop("disabled", true);
				btnPanel.find("#btnUpdate491").addClass(" ui-state-disabled ").prop("disabled", true);
			}
			
			if(json.isRetrialTeam=="N"){				 
				btnPanel.find("#btnSave").addClass(" ui-state-disabled ").prop("disabled", true);
				btnPanel.find("#btnMoveToBranch").addClass(" ui-state-disabled ").prop("disabled", true);
			}
			
			tabForm.injectData(json);
			initDfd.resolve(json);	
			if (json.page == "05") {
				var paForm = $("#paForm");
				paForm.injectData(json.paForm);
			}
			ilog.debug("mainId="+json.mainId+" , docKind=[" + json.docKind+"]");
	}});

	$(".phrase_branchComm").click(function(){
		$("#reg_branchComm").val( $(this).text() );
	});
	
	
	//J-110-0505_05097_B1001 Web e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
	$("#"+grid_p).iGrid({
        handler: 'lms2411gridhandler',        
        height: 350,
        postData: {
        	parStrArr: '',
            formAction: "queryL140M01A"
        },
        needPager: false,        
        shrinkToFit: false,
        multiselect: true,        
        colModel: [
          {
            // "借款人統編",
            colHeader: i18n.lms2411m01['grid.custId'],name:'custId',align: "left",width: 85,sortable: false
          }, {
            // "借款人姓名",
            colHeader: i18n.lms2411m01['L180M01B.elfCName'],name: 'cName',align: "left",width: 130,sortable: false
          },{//"類別"
            colHeader: i18n.lms2411m01['grid.cntrNoType'], name:'rptNoDesc',align: "left",width: 85,sortable: false
          }, {//"額度序號",
            colHeader: i18n.lms2411m01['grid.cntrNo'], name:'cntrNo',align: "left",width: 90,sortable: false
          },{//"案號"
              colHeader: i18n.lms2411m01['grid.caseNo'], name:'caseNo',align: "left",width: 300,sortable: false  
          }, { name: 'rptNo', hidden: true}
          , { name: 'oid', hidden: true}  
          , { name: 'cntrCustid', hidden: true }
          , { name: 'cntrDupno', hidden: true }
        ] 
    });
	
	//J-111-0554 配合授審處增進管理效益，修改相關功能程式 add 列印擔保品設定資料表
	$("#"+grid_c).iGrid({
        handler: 'lms1700gridhandler',        
        height: 350,
        postData: {
        	parStrArr: '',
            formAction: "queryC100m01ByMainid"
        },
        needPager: false,     
        multiselect: true,        
        colModel: [
		  { // "分行別",
            colHeader: '分行別',name:'branch',align: "left",width: 50,sortable: false
          }, {// "借款人統編",
            colHeader: i18n.lms2411m01['grid.custId'],name:'custId',align: "left",width: 85,sortable: false
          }, {// "借款人姓名",
            colHeader: i18n.lms2411m01['L180M01B.elfCName'],name: 'custName',align: "left",width: 130,sortable: false
          }, {//"擔保品編號"
            colHeader: '擔保品編號', name:'collNo',align: "left",width: 80,sortable: false
          }, {//"類別"
            colHeader: '類別', name:'collTyp1',align: "left",width: 60,sortable: false
          }, {//"額度序號",
            colHeader: i18n.lms2411m01['grid.cntrNo'], name:'cntrNo',align: "left",width: 230,sortable: false
          }, { name: 'mainId', hidden: true}
        ] 
    });
	
	//加入檔案按鈕
	$("#addFiles").click(function(){
			// FilesAction.open({ 'mainId': $("#mainId").val() });
			//J-110-0304_05097_B1003 Web e-Loan授信覆審配合RPA作業修改
			var limitFileSize = 3145728*100; //3M * 100 = 300M
	        MegaApi.uploadDialog({
	            fieldId: "crs",
	            fieldIdHtml: "size='30'",
	            fileDescId: "fileDesc",
	            fileDescHtml: "size='30' maxlength='30'",
	            subTitle: i18n.def('insertfileSize', {
	                'fileSize': (limitFileSize / 1048576).toFixed(2)
	            }),
	            limitSize: limitFileSize,
	            width: 320,
	            height: 190,
	            data: {
	                mainId: $("#mainId").val(),
	                sysId: "LMS"
	            },
	            success: function(){
	            	$("#gridfile").trigger("reloadGrid");
	            }
	        });
	        
		});
	
	//刪除檔案按鈕
    $("#deleteFiles").click(function(){
        var select = $("#gridfile").getGridParam('selarrrow');
        if (select == "") {
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var data = [];
                for (var i in select) {
                	var oid = $("#gridfile").getRowData(select[i]).oid ;

                	$.ajax({
	                    handler: _handler,
	                    data: {
	                        formAction: "deleteUploadFile",
	                        'fileOid': oid
	                    }
	                    }).done(function(obj){
	                    	$("#gridfile").trigger("reloadGrid");
	                });
                }
            } 
        });
    });	
	
	btnPanel.find("#btnSave").click(function(){		
		saveAction({'allowIncomplete':'Y'}).done(function(json){
			if(json.saveOkFlag){
				var dyna = [];
				if(true){
					dyna.push(i18n.def.saveSuccess);
				}	
				
				if(json.IncompleteMsg){
					dyna.push(json.IncompleteMsg);
				}
				if(json.promptMsg){
					dyna.push(json.promptMsg);
				}
				API.showMessage(dyna.join("<br/>-------------------<br/>"));
					
			}
        });
	}).end().find("#btnPrint").click(function(){
		
		if(initControl_lockDoc) {
			printC241M01A( $("#mainOid").val() +"^"+ $("#mainId").val() );
		}else{
			saveAction({'allowIncomplete':'Y'}).done(function(json){
				if(json.saveOkFlag){					
					printC241M01A( json.mainOid+"^"+json.mainId );
				}
	        });
		}
	}).end().find("#btnMoveToBranch").click(function(){//移受檢單位登錄		
		saveAction().done(function(json){
			if(json.saveOkFlag){
				//ui_lms2411.msg06=是否移「受檢單位」編製中
				API.confirmMessage(i18n.lms2411m01["ui_lms2411.msg06"], function(result){
		            if (result) {
		            	chose99_specifyCycle(false).done(function(){
			            	signContentC241M01E('B').done(function(){
			            		flowAction({'decisionExpr':'to_待覆核_覆審組'});
			            	});
		            	});
		        	}
		    	});
			}
        });
	}).end().find("#btnMoveToBranch_S").click(function(){//(編製中)送覆審主管
		saveAction().done(function(json){
			if(json.saveOkFlag){
				//ui_lms2411.msg30=是否送覆審主管？
				API.confirmMessage(i18n.lms2411m01["ui_lms2411.msg30"], function(result){
		            if (result) {		            	
			            signContentC241M01E('B').done(function(){
			            	flowAction({'decisionExpr':'to_待覆核_覆審組'});
			            });
		        	}
		    	});
			}
        });
	}).end().find("#btnDocKindS_toEnd").click(function(){//(覆審主管)核准
		API.confirmMessage(i18n.def["confirmApprove3"], function(result){
            if (result) {
            	flowAction({'decisionExpr':'to_已覆核已核定'});      
        	}
    	});		
	}).end().find("#btnBack_A_L1").click(function(){//退回「覆審單位」覆審人員
		API.confirmMessage(i18n.lms2411m01["ui_lms2411.msg07"], function(result){
            if (result) {
            	flowAction({'decisionExpr':'backto_編製中_覆審組'});    	
        	}
    	});		
	}).end().find("#btnBack_B_L1").click(function(){//退回「受檢單位」經辦
		API.confirmMessage(i18n.lms2411m01["ui_lms2411.msg06"], function(result){
            if (result) {
            	flowAction({'decisionExpr':'backto_編製中_分行端'});    	
        	}
    	});		
	}).end().find("#btnBranchComm").click(function(){
		//受檢單位洽辦情形
		var param = {'mainOid':$("#mainOid").val() };
		
		var $div = $("#divReg_branchComm");		
		//============
		//先清空值, 再填入 抓回的結果		
		$div.find("#reg_branchComm").val("");
		//============
		$.ajax({ type: "POST", handler: _handler
			, data: $.extend( {formAction: "getBranchComm"}, param)
			 }).done(function(json_branchComm){
				//textarea 設值
				$div.find("#reg_branchComm").val(json_branchComm.reg_branchComm);
				
				$div.thickbox({
        	        title: $("#btnBranchComm").text(),
        	        width: 850, height: 250, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
                    buttons: {
                        "sure": function(){
                        	if (!$("#reg_branchComm_form").valid()) {
                                return;
                            }	
                        	$.ajax({ type: "POST", handler: _handler
                    			, data:$.extend( {
                    					formAction: "saveBranchComm",
                    					'branchComm': $("#reg_branchComm").val()
                    			  }, param)
                    			}).done(function(json_BranchComm){
                    				                					
                    				var tabForm = $("#tabForm");
                        			tabForm.injectData(json_BranchComm);
                        	
                        			$.thickbox.close();
                    				
                        	});
                        	
                        },
                        "cancel": function(){
                        	$.thickbox.close();
                        }
                    }
        	    });    	
		});
		
	}).end().find("#btnSend").click(function(){
		//分行[受檢單位]經辦-呈主管

		//因分行經辦只能填入 受檢單位洽辦情形
		//沒有 save 的 button
		//所以這裡不 call saveAction
		API.confirmMessage(i18n.def.confirmApply, function(result){
            if (result) {
            	check_bef_btnSend().done(function(){            		
            		signContentC241M01E('A').done(function(){            		
            			flowAction({'decisionExpr':'呈主管'}); 
            		}); 
        		});    	
        	}
    	});    				
	}).end().find("#btnAccept").click(function(){
		//分行[受檢單位]主管-覆核
		var _id = "_div_btnAccept";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			//dyna.push("	<table><tr><td>");
			dyna.push("<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.lms2411m01['ui_lms2411.msg20']+"</label></p>");
			dyna.push("<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.lms2411m01['ui_lms2411.msg19']+"</label></p>");
			dyna.push("<p><label><input type='radio' name='decisionExpr' value='3' class='required' />"+i18n.lms2411m01['ui_lms2411.msg18']+"</label></p>");
			//dyna.push(" </td></tr></table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		    $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380,
            height: 180,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    if(val=="1"){
                    	flowAction({'decisionExpr':'to_已覆核未核定'});
                    }else if(val=="2"){
                    	flowAction({'decisionExpr':'backto_編製中_分行端'});
                    }else if(val=="3"){
                    	flowAction({'decisionExpr':'backto_編製中_覆審組'});
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	}).end().find("#btnOnlyUp491").click(function(){
		saveAction().done(function(json){
			if(json.saveOkFlag){
				//ui_lms2411.msg01=是否上傳至覆審控制檔
				API.confirmMessage(i18n.lms2411m01["ui_lms2411.msg01"], function(result){
		            if(result){
		            	chose99_specifyCycle(false).done(function(){
		            		$.ajax({
		            	        handler: _handler,
		            	        type: "POST",
		            	        dataType: "json",
		            	        data: {
		            	            formAction: "onlyUp491",
		            	            mainOid: $("#mainOid").val()
		            	        }
		            	        }).done(function(json){
		                        	tabForm.injectData(json);
		                        	//更新 opener 的 Grid
		                            CommonAPI.triggerOpener("gridview", "reloadGrid");

		            	        	API.showMessage(i18n.def.runSuccess);		            	        	
		            	    });
		            	});
		        	}
		    	});
			}
        });
	}).end().find("#btnUpdate491").click(function(){ //出現「覆審組簽章欄」 及 flowAction
		saveAction().done(function(json){
			if(json.saveOkFlag){
				if( $("#docKind").val()=="S"){
					signContentC241M01E('B').done(function(){
						flowAction({'decisionExpr':'to_已覆核已核定'});
					});
				}else{
				//ui_lms2411.msg01=是否上傳至覆審控制檔
				API.confirmMessage(i18n.lms2411m01["ui_lms2411.msg01"], function(result){
		            if(result){
		            	chose99_specifyCycle(true).done(function(){
			            	signContentC241M01E('B').done(function(){
			            		flowAction({'decisionExpr':'to_已覆核已核定'});            		
			         	   	});
		            	});
		        	}
		    	});
				}
			}
        });
	}).end().find("#btnEnd_to_01A").click(function(){
		choose_end_to_01A( $("#is_flowClass_throughBr").val()=="Y" ).done(function(json_choose_end_to_01A){
			//forceFlag
        	$.ajax({ handler: 'lms2411m01formhandler', data: $.extend({
        			formAction: "checkFlow"
       				, 'mainOid':$("#mainOid").val()
       				, 'act':'end_to_01A'
       				, 'forceFlag':'Y'
       				}, json_choose_end_to_01A
       		),
			}).done(function(json_checkFlow){
    			if(json_checkFlow.passedFlag=="Y"){
    				CommonAPI.triggerOpener("gridview", "reloadGrid");
    				window.close();        				
            }});    
		});	
	}).end().find("#btnCorrectP_SellerBuyerInfo").click(function(){
		//更正買方、賣方名稱
		var param = {'mainOid':$("#mainOid").val() };
		
		var $div = $("#divCorrectP_SellerBuyerInfo");		
		//============
		//先清空值, 再填入 抓回的結果
		
		var reg_colArr = ["reg_sellerId", "reg_sellerDup", "reg_sellerName"
		               , "reg_buyerId", "reg_buyerDup", "reg_buyerName"];
		$.each( reg_colArr, function(idx,reg_col){					
			$("#"+reg_col).val("");
    	});		
		//============
		$.ajax({ type: "POST", handler: _handler
			, data: $.extend( {formAction: "getP_SellerBuyerInfo"}, param)
			}).done(function(json_getP_SellerBuyerInfo){
				//設值
				$div.injectData(json_getP_SellerBuyerInfo);
				
				$div.thickbox({
        	        title: $("#btnCorrectP_SellerBuyerInfo").text(),
        	        width: 650, height: 240, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
                    buttons: {
                        "sure": function(){
                        	var collectData = {};
                    		$.each( reg_colArr, function(idx,reg_col){
                    			collectData[reg_col] = $("#"+reg_col).val();
                        	});	
                        	$.ajax({ type: "POST", handler: _handler
                    			, data:$.extend( {
                    					formAction: "saveP_SellerBuyerInfo"
                    			  }, collectData, param)
                    			 }).done(function(saveP_SellerBuyerInfo){
                    				                					
                    				var tabForm = $("#tabForm");
                        			tabForm.injectData(saveP_SellerBuyerInfo);
                        			//更新 opener 的 Grid
                                    CommonAPI.triggerOpener("gridview", "reloadGrid");
                                    
                        			$.thickbox.close();
                    				
                        	});
                        	
                        },
                        "cancel": function(){
                        	$.thickbox.close();
                        }
                    }
        	    });    	
		});
	}).end().find("#btnLoadLms8000v04").click(function(){
		$.ajax({ handler: 'lms2411m01formhandler', data: {formAction: "prepare_ELF601_ELF602_L260RelateData", 'mainOid':$("#mainOid").val()} }).done(function(json_rtn){
			$("#do_LoadLms8000v04_form").injectData( {'startDate':json_rtn.begDate 
					, 'endDate':json_rtn.endDate
					, 'status':'N' // N:未解除 , A=全部 , C=已解除
					, 'handlingStatus':['1', '2']  // 1=未辦理 , 2=辦理中, 3=已完成, 4=已刪除 { LMS2411M01Page_zh_TW.properties 查找  L260M01D.status }
					} 
				);
			//=======================================
			$("#elf601elf602_data").empty();
			if(json_rtn.elf601elf602_size > 0){
				var dyna = [];
				if(json_rtn.elf601elf602_hasDataCnt > 0){
					
				}else{
					dyna.push("<span class='text-red' >");
					dyna.push("客戶 "+encodeURI(json_rtn.custId)+"-"+encodeURI(json_rtn.dupNo)+" "+encodeURI(json_rtn.custName)+" 的「未銷戶額度」無「貸後管理資料」可供檢視");
					dyna.push("</span>");
				}
				dyna.push("<table border='1'>");
				$.each( json_rtn.elf601elf602_data.arr, function(idx, item){
					dyna.push("<tr>");
					dyna.push("<td style='padding:4px;'>");
					dyna.push(item.cntrNo);
					dyna.push("&nbsp;&nbsp;&nbsp;</td>");
					dyna.push("<td style='padding:4px;'>&nbsp;&nbsp;");
					if(item.hasData=="Y"){
						dyna.push("<span class='spanLoadLms8000v04' data-cntrNo='"+encodeURI(item.cntrNo)+"' style='color:#5291EF; text-decoration:underline;' >查詢</span>");
					}else if(item.hasIdData=="Y"){
						dyna.push("<span class='spanLoadLms8000v04' data-cntrNo='"+encodeURI(item.cntrNo)+"' style='color:#5291EF; text-decoration:underline;' >查詢（有ID層級資料）</span>");
					}else{
						dyna.push("N.A.");	
					}				
					dyna.push("&nbsp;&nbsp;</td>");
					dyna.push("</tr>");
				});
				dyna.push("</table>");
				$("#elf601elf602_data").append(dyna.join("\n"));	
			}			
			//=======================================
			$("span.spanLoadLms8000v04").click(function(){
				var $do_LoadLms8000v04_form = $('#do_LoadLms8000v04_form').serializeData();
				var $do_LoadLms8000v04_cntrNo = $(this).attr("data-cntrNo")||'';
				$.ajax({
                    type: "POST",
                    handler: "lms8000m01formhandler",
                    formId: "empty",
                    data: {
                    	'filterForm': JSON.stringify($.extend(  
                    					$do_LoadLms8000v04_form
                    				, {
                    						'cntrNo': $do_LoadLms8000v04_cntrNo
                    						, 'custId':json_rtn.custId
                    						, 'dupNo':json_rtn.dupNo                    					
                    				}))
                    	, 'openFrom': 'crs'		
                    	, 'handlingStatus': $do_LoadLms8000v04_form["handlingStatus"]			
                        , 'formAction': "queryAndAdd"
                    }
                    }).done(function(json){
                        if(json.hasData){
                            var urlStr = "../../fms/lms8000m01/01";                            
                            $.form.submit({
                                url: urlStr,
                                data: {
                                    oid: json.oid,
                                    custId: json.custId,
                                    dupNo: json.dupNo,
                                    custName: json.custName,
                                    cntrNo: json.cntrNo,
                                    loanNo: json.loanNo,
                                    mainId: json.mainId,
                                    mainOid: json.oid,
                                    mainDocStatus: '0CO', // viewstatus, 參考 LMS8000V04Page.java 裡面 setGridViewStatus(?) 的值
                                    txCode: ''  // txCode
                                },
                                target: "window_Lms8000v04_"+$do_LoadLms8000v04_cntrNo
                            });
                        } else {
                            return CommonAPI.showMessage(i18n.def["noData"]);
                        }
                });
				
				
			});
			//=======================================
			$("#div_LoadLms8000v04").thickbox({
    	        title: json_rtn.custId + "-" + json_rtn.dupNo + " "+json_rtn.custName ,
    	        width: 650, height: 360, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
                buttons: {
//                    "cancel": function(){
//                    	$.thickbox.close();
//                    }
                }
    	    });    	      
        });
	}).end().find("#btnPrintLatestL140M01A").click(function(){
		//J-110-0505_05097_B1001 Web e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
		printLatestL140M01A($("#mainOid").val() );
    }).end().find("#btnPrintCollSet").click(function(){
		//J-111-0554 配合授審處增進管理效益，修改相關功能程式
		printCollSet($("#mainOid").val() );
    });
	
	
	$("#btn_reQueryCustName").click(function(){
		$.ajax({ handler: 'lms2411m01formhandler', data: {formAction: "reQueryCustName", 'mainOid':$("#mainOid").val()} }).done(function(json_reQueryCustName){
			if(json_reQueryCustName.changed=="Y"){
				tabForm.find("#custName").text(json_reQueryCustName.custName);
        }});
	});
	
	$("#btn_reQueryCustName_reg_sellerId").click(function(){
		$.ajax({ handler: _handler, data: {
            formAction: "reQueryCustNameByIdDupNo",
            custId: $("#reg_sellerId").val(),
            dupNo: $("#reg_sellerDup").val()
        	}
		}).done(function(responseData_seller){
        	if(responseData_seller.hasResult=="Y"){
        		$("#reg_sellerName").val(responseData_seller.cName);	
        	}else{
        		CommonAPI.showErrorMessage(i18n.def['includeId.noData']);
        }});
	});
	
	$("#btn_reQueryCustName_reg_buyerId").click(function(){
		$.ajax({ handler: _handler, data: {
            formAction: "reQueryCustNameByIdDupNo",
            custId: $("#reg_buyerId").val(),
            dupNo: $("#reg_buyerDup").val()
        	} 
		}).done(function(responseData_buyer){ 
        	if(responseData_buyer.hasResult=="Y"){
        		$("#reg_buyerName").val(responseData_buyer.cName);
        	}else{
        		CommonAPI.showErrorMessage(i18n.def['includeId.noData']);
        				           
        }});
	});
	
	$("#sign_cntA").change(function(){
        var $target = $("#new_sign_A_L4Span");
        //清空原本的
        $target.empty();
        
        var newdiv = "";
        var val = parseInt($(this).val(), 10);
        if (val > 1) {
            for (var i = 2, count = val + 1; i < count; i++) {
            	var idName = "sign_A_L4_"+i;
            	
                newdiv+=  
                ("<div>" 
                + i18n.lms2411m01['label.signSeqDescPrefix'] + i +i18n.lms2411m01['label.signSeqDescPostfix']
                +"&nbsp;&nbsp;&nbsp;<select id='"+idName+"' name='"+idName+"' class='selectSign_A_L4'></select>&nbsp;"
                +"</div>");
            }
            $target.append(newdiv);
            
            var copyFrom = $("#sign_A_L4_1").html();//get srcElm html content
            $(".selectSign_A_L4").html(copyFrom);
        }
    });
	$("#sign_cntB").change(function(){
        var $target = $("#new_sign_B_L4Span");
        //清空原本的
        $target.empty();
        
        var newdiv = "";
        var val = parseInt($(this).val(), 10);
        if (val > 1) {
            for (var i = 2, count = val + 1; i < count; i++) {
            	var idName = "sign_B_L4_"+i;
            	
                newdiv+=  
                ("<div>" 
                + i18n.lms2411m01['label.signSeqDescPrefix'] + i +i18n.lms2411m01['label.signSeqDescPostfix']
                +"&nbsp;&nbsp;&nbsp;<select id='"+idName+"' name='"+idName+"' class='selectSign_B_L4'></select>&nbsp;"
                +"</div>");
            }
            $target.append(newdiv);
            
            var copyFrom = $("#sign_B_L4_1").html();//get srcElm html content
            $(".selectSign_B_L4").html(copyFrom);
        }
    });
	
	var choose_end_to_01A = function (isThrougrBr){
		var my_dfd = $.Deferred();
		//ui_lms2411.msg18=退回「覆審單位」覆審人員 
		//ui_lms2411.msg19=退回「受檢單位」經辦
		var _id = "_div_choose_end_to_01A";
		var _form = _id+"_form";
				
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.lms2411m01['ui_lms2411.msg18']+"</label></p>");
			if(isThrougrBr){
				if( $("#docKind").val()=="S"){
					// 不需把文件退回到「受檢單位」
				}else{
					dyna.push("<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.lms2411m01['ui_lms2411.msg19']+"</label></p>");
				}
				/*
                // 2022/04/18 授審處連喬凱來電  分處對覆審報告表有回頭打考評表之需求
                // 1. 「已覆核已核定」之覆審報告表可以退為「已覆核未核定」修改_限制為有考評表且有傳送至分行的覆審報告表
                // 2. 不限上傳者本人，任何人都可以修改
                */
                dyna.push("<p><label><input type='radio' name='decisionExpr' value='3' class='required' />"+i18n.lms2411m01['ui_lms2411.msg35']+"</label></p>");
			}
			dyna.push("</form>");				
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({
	       title: '', width: 280, height: 180, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
           buttons: {
               "sure": function(){
            	   
                   if (!$("#"+_form).valid()) {
                       return;
                   }
                   var decisionExpr = $("#"+_form).find("[name='decisionExpr']:checked").val();
                   if(decisionExpr=='2'){
                	   my_dfd.resolve({});
                   }else if(decisionExpr=='1'){
                	   my_dfd.resolve({'targetDocStatus':'010'});
                   }else if(decisionExpr=='3'){
                       my_dfd.resolve({'targetDocStatus':'050'});
                   }else{
                	   my_dfd.reject();   
                   }	
                   $.thickbox.close();
               },
               "cancel": function(){
            	   my_dfd.reject();
            	   $.thickbox.close();
               }
           }   
        });		
		return my_dfd.promise();
	}
	
	var check_bef_btnSend = function (){
		return $.ajax({
            type: "POST",
            handler: _handler,
            data:{
            	formAction: "check_bef_btnSend",
            	'mainOid':$("#mainOid").val()
            }                
            }).done(function(){
        });
	}
	
	var signContentC241M01E = function (signRole){
		var param = {'mainOid':$("#mainOid").val(), 'signRole': signRole};
		var my_dfd = $.Deferred();
		$.ajax({ type: "POST", handler: _handler
			, data: $.extend( {formAction: "getSignList"}, param)
			 }).done(function(json_signContent){
				if(json_signContent.canSkip && json_signContent.canSkip=="Y"){
					my_dfd.resolve();
				}else{
					var condL4 = null;
					var condL5 = null;
					var $div = null;
					var tb_title = "";
					if(signRole=="A"){
						condL4 = ".selectSign_A_L4"; 
						condL5 = ".selectSign_A_L5"; 
						$div = $("#divSignContent_A"); 
						tb_title = i18n.lms2411m01["reviewBrn.A"];//受檢單位
					}else if(signRole=="B"){
						condL4 = ".selectSign_B_L4"; 
						condL5 = ".selectSign_B_L5"; 
						$div = $("#divSignContent_B"); 
						tb_title = i18n.lms2411m01["reviewBrn.B"];//覆審單位
					}
					var jcondL4 = $(condL4);
					jcondL4.setItems({ item: json_signContent.l4_list, space: true });
					var jcondL5 = $(condL5);
					jcondL5.setItems({ item: json_signContent.l5_list, space: true });
	            	//=========
					$div.thickbox({
	        	        title: i18n.lms2411m01["label.signature"]+"("+tb_title+")",
	        	        width: 580, height: 250, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
	                    buttons: {
	                        "sure": function(){
	                        	var l4Arr = [];
	                        	var l5Arr = [];
	                        	                        	
	                        	$.each( $(condL4), function(idx,obj){
	                        		var val = $(obj).val();                        	
	                        		l4Arr.push( val );
	                        	});
	                        	$.each( $(condL5), function(idx,obj){
	                        		var val = $(obj).val();                        		
	                        		l5Arr.push( val );
	                        	});
	                        	                        		
	                        	$.ajax({ type: "POST", handler: _handler
	                    			, data:$.extend( {
	                    					formAction: "saveSignList"
	                    					,'l4Arr':l4Arr.join("|") 
	                    					,'l5Arr':l5Arr.join("|")
	                    			  }, param)
	                    			}).done(function(json_saveSignList){
	                    				if(json_saveSignList.saveSignFlag=="Y"){                    					
	                    					var tabForm = $("#tabForm");
	                        				tabForm.injectData(json_saveSignList);
	                        				
	                        				my_dfd.resolve();
	                        				$.thickbox.close();
	                    				}
	                        	});
	                        	
	                        },
	                        "cancel": function(){
	                        	my_dfd.reject();
	                        	$.thickbox.close();
	                        }
	                    }
	        	    });   
				}
		});		
		return my_dfd.promise();
	}
	
	var chose99_specifyCycle = function (canUseExistCycle){
		var my_dfd = $.Deferred();
		
		if($("#hasR99").val()=="Y"){
			if(canUseExistCycle && $("#is_flowClass_throughBr").val()=="Y" && $("#hasCycle").val()=="Y"){
				my_dfd.resolve();
			}else{				
				
				var _id = "_div_chose99_specifyCycle";
				var _form = _id+"_form";
						
				if ($("#"+_id).length == 0){
					var dyna = [];
					dyna.push("<div id='"+_id+"' style='display:none;' >");
					dyna.push("<form id='"+_form+"'>");
					dyna.push("<table>");
					dyna.push("<tr><td><select id='specifyCycle' name='specifyCycle' class='required' /></td></tr>");
					dyna.push("</table>");
					dyna.push("</form>");
					
					dyna.push("</div>");
					
				     $('body').append(dyna.join(""));
				     
				     $.ajax({
				    	 type: 'post', handler: _handler, data:{'formAction':'load_specifyCycle'}
				         }).done(function(json_combos){
						 	var jspecifyCycle = $("#"+_form).find("#specifyCycle");
				        	 jspecifyCycle.setItems({ item: json_combos.specifyCycle,format: "{key} ", space:true,size: 1});
				     });
				}
				//clear data
				$("#"+_form).reset();
				
				$("#"+_id).thickbox({
			       title: i18n.lms2411m01["C241M01A.specifyCycle"],
			       width: 250,
		           height: 140,
		           align: "center",
		           valign: "bottom",
		           modal: false,
		           i18n: i18n.def,
		           buttons: {
		               "sure": function(){
		            	   
		                   if (!$("#"+_form).valid()) {
		                       return;
		                   }
		                   var specifyCycle = $("#"+_form).find("[name='specifyCycle']").val();
		           
	   		            	$.ajax({
	 	                       type: "POST",
	 	                       handler: _handler,
	 	                       data: {
	 	                           formAction: 'saveSpecifyCycle' ,
	 	                           'specifyCycle': specifyCycle ,
	 	                           'mainOid':$("#mainOid").val()
	 	                       }
	 	                       }).done(function(json_saveSpecifyCycle){                    	   
	 	                    	  if(json_saveSpecifyCycle.saveSpecifyCycleFlag=="Y"){
	 		                    		   
	 		           					var tabForm = $("#tabForm");
	 		           					tabForm.injectData(json_saveSpecifyCycle);
	 	               				
	 		           					my_dfd.resolve();
	 		           					$.thickbox.close();
	 	                       }
	 	                   });    	
			   		        	
		               },
		               "cancel": function(){
		            	   my_dfd.reject();            	   
		            	   $.thickbox.close();
		               }
		           }   
		        });
			
			}
		}else{
			my_dfd.resolve();
		}		
		return my_dfd.promise();
	}
	
	var flowAction = function(opts){
		return $.ajax({
            type: "POST",
            handler: _handler, action: "flowAction",
            data:$.extend( {
            	mainOid: $("#mainOid").val(), 
            	mainDocStatus: $("#mainDocStatus").val() 
                }
                , ( opts||{} )
            )                
            }).done(function(json){            	
            	API.triggerOpener();//gridview.reloadGrid 
            	window.close();            	
        });
	}
});

$.extend(window.tempSave,{
	handler: _handler, // handler 名稱
	action: "tempSave", // action Method
	beforeCheck:function(){ // return false or true		
		return $("#tabForm").valid();
	},sendData:function(){ // 需上送之資料集合(Map<String,String>)
		return $("#tabForm").serializeData();
	}
});

/**
 * 同時印多份: 
 * oid_arr.push(data.oid+"^"+data.mainId);
 * rptOid: oid_arr.join("|")
 */
function printC241M01A(rptOid){
	var _id = "_div_printC241M01A";	
	var _form = _id+"_form";
	var rdoName = "rdo_printC241M01A";
	var option = {
		'X':'經副襄理',
		'Y':'正副營運長'
	};
	if ($("#"+_id).length == 0){
		var dyna = [];
		dyna.push("<div id='"+_id+"' style='display:none;' >");
		dyna.push("<form id='"+_form+"'>");
		$.each(option, function(k, v) { 
			dyna.push("   <p><label><input type='radio' name='"+rdoName+"' value='"+k+"' class='required cbox' />"+v+"</label></p>"); 
        });
		dyna.push("</form>");
		dyna.push("</div>");
	    $('body').append(dyna.join(""));
	}

	$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
       title: "",
       width: 250,
       height: 160,
       align: "center",
       valign: "bottom",
       modal: false,
       i18n: i18n.def,
       buttons: {
           "sure": function(){
        	   if (!$("#"+_form).valid()) {
                   return;
               }
               var L5DescFlag = $("#"+_form).find("[name='"+rdoName+"']:checked").val();
               
               $.form.submit({
                   url: "../../simple/FileProcessingService",
                   target: "_blank",
                   data: {
                       'rptOid': rptOid,
                       'fileDownloadName': "lms2411r01.pdf",
                       'L5DescFlag' : L5DescFlag,
                       serviceName: "lms2411r01rptservice"            
                   }
               });
               $.thickbox.close();
           }
       }
	});
}

/**
 * 重引帳務時【在LMS2411S02Panel.js 裡的 $("#btn_importLN").click(...)】,可能 overdueYN 會由 Y→'' 或 ''→Y
 * 若用 <wicket:enclosure>, 無法動態切換
 * 所以寫在 js 內, 於 tabForm.injectData(...) 之前執行
 */
function buildOverdue(isDocKindP, valYN, valP){
	var $o = $("#input_overdue");
	$o.empty();
	
	var $title = $("#titile_overdue");
	
	
	if(isDocKindP){
		/*
		 已逾到期日[無 , 4月 , 1年]
		 */
		var dyna = [];
		dyna.push("<label><input type='radio' id='overdueP' name='overdueP' value='0'  disabled >"+i18n.lms2411m01['label.overdueP.0']+"</label>");
		dyna.push("&nbsp;");
		dyna.push("<label><input type='radio' id='overdueP' name='overdueP' value='12' disabled >"+i18n.lms2411m01['label.overdueP.12']+"</label>");
		dyna.push("&nbsp;");
		dyna.push("<label><input type='radio' id='overdueP' name='overdueP' value='4'  disabled >"+i18n.lms2411m01['label.overdueP.4']+"</label>");
		$o.append(dyna.join(""));
	}else{		
		if(valYN=="Y" || valYN=="N"){
			$title.show();
			/*
			 無擔逾期戶是否需半年再審
			 */
			//$("input[name=overdueYN]").attr("disabled", true).removeAttr("checked");
			var dyna = [];
			dyna.push("<label><input type='radio' id='overdueYN' name='overdueYN' value='Y' class='required'>"+i18n.def.yes+"</label>");
			dyna.push("&nbsp;");
			dyna.push("<label><input type='radio' id='overdueYN' name='overdueYN' value='N' class='required'>"+i18n.def.no+"</label>");
			$o.append(dyna.join(""));
		}else{
			$title.hide();
			$o.append("<input type='hidden' name='overdueYN' value=''>");
		}
	}
}

function redo_updateMIS(){

	$.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "redo_updateMIS",
            mainOid: $("#mainOid").val()
        }
        }).done(function(json){
        	
    });
}

function saveAction(opts){
	var tabForm = $("#tabForm");
	if(tabForm.valid()){
		return $.ajax({
            type: "POST",
            handler: _handler,
            data:$.extend( {
            	formAction: "saveMain",
                page: responseJSON.page,
                mainOid: responseJSON.mainOid
                }, 
                tabForm.serializeData(),
                ( opts||{} )
            )                
            }).done(function(json){
            	tabForm.injectData(json);
				if (json.page == "05") {
					var paForm = $("#paForm");
					paForm.injectData(json.paForm);
				}
            	//更新 opener 的 Grid
                CommonAPI.triggerOpener("gridview", "reloadGrid");
        });
	}else{
		return $.Deferred();
	}
}


//J-110-0505_05097_B1001 Web e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
function printLatestL140M01A(oids){
	$.ajax({ type: "POST", handler: _handler, data:{ formAction: "getPrintL140M01AParam", 'oids': oids}
        }).done(function(json){
        	var my_dfd = $.Deferred();    	
        	if(json.notProc){
        		API.showPopMessage("", json.notProc,function(){
        			my_dfd.resolve();
  				});
        	}else{
        		my_dfd.resolve();
        	}

        	my_dfd.done(function(){
        		if(json.parStr){
            		$("#"+grid_p).jqGrid("setGridParam", {
                        postData: {
                        	parStrArr:json.parStr
                        },
                        search: true
                    }).trigger("reloadGrid");
            		
            		$("#div_printL140M01A").thickbox({ // 使用選取的內容進行彈窗
         		       title: "", width: 700, height: 500, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
         	           buttons: {
         	               "列印": function(){
         	            	  var rowId_arr = $("#"+grid_p).getGridParam('selarrrow');
         	            	  var oid_arr = [];
         	            	 	for (var i = 0; i < rowId_arr.length; i++) {
         	         			var data = $("#"+grid_p).getRowData(rowId_arr[i]);
         	         			oid_arr.push(data.rptNo+"^"+data.oid+"^"+data.cntrCustid+"^"+data.cntrDupno+"^"+data.cntrNo );    			
         	            	  }
         	            	  if(oid_arr.length==0){   	 		
    	                	 	API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
    	             	 		return;
    	             	 	  }	
         	            	 
         	            	  $.form.submit({
         	                     url: print_url,
         	                     target: "_blank",
         	                     data: {
         	                         'rptOid': oid_arr.join("|"),
         	                         'fileDownloadName': "cntrNo.pdf",
         	                         serviceName: "cls1141r01rptservice"            
         	                     }
         	            	  });  	                  	
         	               },
         	               "列印並加入附加檔案": function(){
         	            	  var rowId_arr = $("#"+grid_p).getGridParam('selarrrow');
         	            	  var oid_arr = [];
         	            	 	for (var i = 0; i < rowId_arr.length; i++) {
         	         			var data = $("#"+grid_p).getRowData(rowId_arr[i]);
         	         			var saveFile = "mainId:"+responseJSON.mainId+";"+"fileId:crs"+";"+"class:C241M01A"+";"+"brNo:" + userInfo.unitNo+";"+"rpa:N";
         	         			oid_arr.push(data.rptNo+"^"+data.oid+"^"+data.cntrCustid+"^"+data.cntrDupno+"^"+data.cntrNo+"^^^");    			
         	            	  }
         	            	  if(oid_arr.length==0){   	 		
    	                	 	API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
    	             	 		return;
    	             	 	  }	
         	            	 
         	            	  $.form.submit({
         	                     url: print_url,
         	                     target: "_blank",
         	                     data: {
         	                         'rptOid': oid_arr.join("|"),
         	                         'fileDownloadName': "cntrNo.pdf",
         	                         createFile:true,
         	                         saveFile: saveFile,
         	                         serviceName: "cls1141r01rptservice"            
         	                     }
         	            	  });  	           
         	               },
         	               "close": function(){
         	            	   $.thickbox.close();
         	               }
         	           }
            		});        		
            	}
        	});
    });
}

//J-111-0554 配合授審處增進管理效益，修改相關功能程式 add 列印擔保品設定資料表
function printCollSet(oids){
	$.ajax({ 
		type: "POST", 
		handler: _handler, 
		data:{ 
				formAction: "getPrintCollSet"
		        , 'oids': oids
		     }
        }).done(function(json){
        	var my_dfd = $.Deferred();    	
        	if(json.notProc){
        		API.showPopMessage("", json.notProc,function(){
        			my_dfd.resolve();
  				});
        	}else{
        		my_dfd.resolve();
        	}

        	my_dfd.done(function(){
        		if(json.parStr){
            		$("#"+grid_c).jqGrid("setGridParam", {
                        postData: {
                        	parStrArr:json.parStr
                        },
                        search: true
                    }).trigger("reloadGrid");
            		
            		$("#div_printCollSet").thickbox({ // 使用選取的內容進行彈窗
         		       title: "", width: 700, height: 500, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
         	           buttons: {
         	               "列印": function(){
         	            	  var rowId_arr = $("#"+grid_c).getGridParam('selarrrow');
         	            	  var oid_arr = [];
         	            	 	for (var i = 0; i < rowId_arr.length; i++) {
         	         			var data = $("#"+grid_c).getRowData(rowId_arr[i]);
         	         			oid_arr.push(data.mainId);    			
         	            	  }
         	            	  if(oid_arr.length==0){   	 		
    	                	 	API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
    	             	 		return;
    	             	 	  }	
         	            	 
         	            	  $.form.submit({
         	                     url: print_url,
         	                     target: "_blank",
         	                     data: {
         	                         'mainIds': oid_arr.join("|"),
         	                         'fileDownloadName': "collSet.pdf",
         	                         serviceName: "lmscallcmsr01rptservice"            
         	                     }
         	            	  });  	                  	
         	               },
         	               "列印並加入附加檔案": function(){
         	            	  var rowId_arr = $("#"+grid_c).getGridParam('selarrrow');
         	            	  var oid_arr = [];
         	            	 	for (var i = 0; i < rowId_arr.length; i++) {
         	         			var data = $("#"+grid_c).getRowData(rowId_arr[i]);
         	         			var saveFile = "mainId:"+responseJSON.mainId+";"+"fileId:crs"+";"+"class:C241M01A"+";"+"brNo:" + userInfo.unitNo+";"+"rpa:N";
         	         			oid_arr.push(data.mainId);    			
         	            	  }
         	            	  if(oid_arr.length==0){   	 		
    	                	 	API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
    	             	 		return;
    	             	 	  }	
         	            	 
         	            	  $.form.submit({
         	                     url: print_url,
         	                     target: "_blank",
         	                     data: {
         	                         'mainIds': oid_arr.join("|"),
         	                         'fileDownloadName': "collSet.pdf",
         	                         createFile:true,
         	                         saveFile: saveFile,
         	                         serviceName: "lmscallcmsr01rptservice"            
         	                     }
         	            	  });  	           
         	               },
         	               "close": function(){
         	            	   $.thickbox.close();
         	               }
         	           }
            		});        		
            	}
        	});
    });
}