package com.mega.eloan.lms.base.common;

import java.math.BigDecimal;

public class Pteamapp_TOT {
	private String brNo;
	private int cntById;
	private int cntByCntrNo;
	private int cntByLoanNo;
	private int cntLnapEq321;
	private int cntLnapDiff321;
	private BigDecimal sumFirstFactAmt;
	private BigDecimal sumFactAmt;
	private BigDecimal sumBal;

	public Pteamapp_TOT(){
		this.brNo = "";
		this.cntById = 0;
		this.cntByCntrNo = 0;
		this.cntByLoanNo = 0;
		this.cntLnapEq321 = 0;
		this.cntLnapDiff321 = 0;
		this.sumFirstFactAmt = BigDecimal.ZERO;
		this.sumFactAmt = BigDecimal.ZERO;
		this.sumBal = BigDecimal.ZERO;
	}

	public String getBrNo() {
		return brNo;
	}

	public void setBrNo(String brNo) {
		this.brNo = brNo;
	}

	public int getCntById() {
		return cntById;
	}

	public void setCntById(int cntById) {
		this.cntById = cntById;
	}

	public int getCntByCntrNo() {
		return cntByCntrNo;
	}

	public void setCntByCntrNo(int cntByCntrNo) {
		this.cntByCntrNo = cntByCntrNo;
	}

	public int getCntByLoanNo() {
		return cntByLoanNo;
	}

	public void setCntByLoanNo(int cntByLoanNo) {
		this.cntByLoanNo = cntByLoanNo;
	}

	public int getCntLnapEq321() {
		return cntLnapEq321;
	}

	public void setCntLnapEq321(int cntLnapEq321) {
		this.cntLnapEq321 = cntLnapEq321;
	}

	public int getCntLnapDiff321() {
		return cntLnapDiff321;
	}

	public void setCntLnapDiff321(int cntLnapDiff321) {
		this.cntLnapDiff321 = cntLnapDiff321;
	}

	public BigDecimal getSumFirstFactAmt() {
		return sumFirstFactAmt;
	}

	public void setSumFirstFactAmt(BigDecimal sumFirstFactAmt) {
		this.sumFirstFactAmt = sumFirstFactAmt;
	}

	public BigDecimal getSumFactAmt() {
		return sumFactAmt;
	}

	public void setSumFactAmt(BigDecimal sumFactAmt) {
		this.sumFactAmt = sumFactAmt;
	}

	public BigDecimal getSumBal() {
		return sumBal;
	}

	public void setSumBal(BigDecimal sumBal) {
		this.sumBal = sumBal;
	}
	


}
