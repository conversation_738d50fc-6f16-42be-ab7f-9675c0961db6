/* 
 * LMS1815V04Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.panels.GridViewFilterPanel01;
import com.mega.eloan.lms.lms.panels.LMS1205S05Panel;
import com.mega.eloan.lms.lms.panels.LMS1205S18Panel;


/**<pre>
 * 授信簽報書審核中(營運中心)
 * </pre>
 * @since  2011/10/19
 * <AUTHOR> Lin
 * @version <ul>
 *           <li>2011/10/19,Miller Lin,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1200v31")
public class LMS1200V31Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		//設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.營運中心_審查中);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Filter, LmsButtonEnum.ChangeCaseFormat1);
		//套用哪個i18N檔案
		renderJsI18N(LMS1205V01Page.class);
		renderJsI18N(LMS1205S05Panel.class);
		renderJsI18N(LMS1205S18Panel.class);
		renderJsI18N(AbstractEloanPage.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/lms/LMS1205V01Page');");

		setupIPanel(new GridViewFilterPanel01(PANEL_ID), model, params);
	}// ;

	// @Override
	// public String[] getJavascriptPath() {
	// return new String[] { "pagejs/lms/LMS1205V01Page.js" };
	// }
}
