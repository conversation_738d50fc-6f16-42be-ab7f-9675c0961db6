<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <fieldset>
                <legend>
                    <th:block th:text="#{'doc.baseInfo'}">基本資訊</th:block>
                </legend>
                <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tbody>
                        <tr>
                            <td width="30%" class="hd1">
                                <th:block th:text="#{'doc.branchName'}">分行名稱</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <span id="branchName" ></span>&nbsp;
                            </td>
							<td width="30%" class="hd1">
                                <th:block th:text="#{'C241M01A.staff'}">是否為行員</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <span id="staff" ></span>&nbsp;
                            </td>
                        </tr>
                        <tr>
                        	<td class="hd1">
                                <th:block th:text="#{'label.custInfo'}">借款人</th:block>&nbsp;&nbsp;
								<th:block th:if="${show_reQueryCustName}">
								<button type="button" id="btn_reQueryCustName" class='forview'>
									<th:block th:text="#{'btn.reQueryCustName'}">重引姓名</th:block>
								</button>
								</th:block>
                            </td>
                            <td>
                                <span id="custId"/>&nbsp; <th:block th:text="#{'C241M01A.dupNo'}">重覆序號</th:block>:<span id="dupNo"></span> 　<span id="custName"></span>&nbsp;
                            </td>
                            <td class="hd1">
                                <th:block th:text="#{'doc.docStatus'}">文件狀態</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <b><span class="color-red" id="status"></span></b>&nbsp;
                            </td>
                        </tr>
						<tr>
                        	<td class="hd1">
                                <th:block th:text="#{'C241M01A.projectNo'}">覆審序號</th:block>&nbsp;&nbsp;
                            </td>
                            <td colspan="3">
                                <span id="projectNo"></span>&nbsp;
                            </td>
                        </tr>
                    </tbody>
                </table>
            </fieldset>			
            <!-- ================================================== -->
            <fieldset>
                <legend>
                    <th:block th:text="#{'doc.docUpdateLog'}">
                        文件異動紀錄
                    </th:block>
                </legend>
			
                <div class="funcContainer">
                	<!-- 文件異動記錄 -->
                    <div th:include="common/panels/DocLogPanel :: DocLogPanel"></div>
                </div>
				
                <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tbody>
                        <tr>
                            <td class="hd1">
                                <th:block th:text="#{'doc.creator'}">
                                    文件建立者
                                </th:block>&nbsp;&nbsp;
                            </td>
                            <td width="30%">
                                <span id="creator"></span>(<span id="createTime"></span>)
                            </td>
                            <td class="hd1">
                                <th:block th:text="#{'doc.lastUpdater'}">
                                    最後異動者
                                </th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <span id="updater"></span>(<span id="updateTime"></span>)
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1">
                            </td>
                            <td>
                            </td>
                            <td class="hd1">
                                <th:block th:text="#{'doc.docCode'}">
                                    報表亂碼
                                </th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <span id="randomCode"></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </fieldset>
            
			<!-- ================================================== -->			
        </th:block>
    </body>
</html>
