/* 
 * C120S02B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 大數據風險報告查詢結果 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C120S02B", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class C120S02B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 借保人統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 借保人重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 借保人查詢電話 **/
	@Size(max = 150)
	@Column(name = "MPNUM", length = 150, columnDefinition = "VARCHAR(150)")
	private String mpnum;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "WFCREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String wfCreator;

	/** 建立日期 **/
	@Column(name = "WFCREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp wfCreateTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "WFUPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String wfUpdater;

	/** 異動日期 **/
	@Column(name = "WFUPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp wfUpdateTime;

	// ===================================================================

	/** 查詢號碼 **/
	@Size(max = 64)
	@Column(name = "num", length = 64, columnDefinition = "VARCHAR(64)")
	private String num;

	/** 騷擾類別 **/
	@Size(max = 200)
	@Column(name = "spam_type", length = 200, columnDefinition = "VARCHAR(200)")
	private String spam_type;

	/** 黃頁類別 **/
	@Size(max = 200)
	@Column(name = "yp_type", length = 200, columnDefinition = "VARCHAR(200)")
	private String yp_type;

	/** 特殊類別 **/
	@Size(max = 200)
	@Column(name = "special_type", length = 200, columnDefinition = "VARCHAR(200)")
	private String special_type;

	/** 回報標籤 **/
	@Size(max = 200)
	@Column(name = "report_tag", length = 200, columnDefinition = "VARCHAR(200)")
	private String report_tag;

	/** 特殊標籤 **/
	@Size(max = 200)
	@Column(name = "special_tag", length = 200, columnDefinition = "VARCHAR(200)")
	private String special_tag;

	/** 變造號碼 **/
	@Size(max = 200)
	@Column(name = "is_invalid_number", length = 200, columnDefinition = "VARCHAR(200)")
	private String is_invalid_number;

	/** 信用評等 **/
	@Size(max = 9)
	@Column(name = "rank", length = 9, columnDefinition = "VARCHAR(9)")
	private String rank;

	/** 資料更新日期 **/
	@Size(max = 10)
	@Column(name = "utime", length = 10, columnDefinition = "VARCHAR(10)")
	private String utime;

	/** 來源地 **/
	@Size(max = 10)
	@Column(name = "number_origin", length = 10, columnDefinition = "VARCHAR(10)")
	private String number_origin;

	/** 用戶驗證 **/
	@Column(name = "is_verify", columnDefinition = "BOOLEAN")
	private boolean is_verify;

	/** 警戒指數 **/
	@Column(name = "score", columnDefinition = "DECIMAL(4,2)")
	private BigDecimal score;

	/** 警戒層級 **/
	@Column(name = "alert", columnDefinition = "DECIMAL(4)")
	private BigDecimal alert;

	/** 詐欺資料庫 **/
	@Column(name = "in_fraud_db", columnDefinition = "DECIMAL(4)")
	private BigDecimal in_fraud_db;

	/** 不良紀錄 **/
	@Column(name = "bad_history", columnDefinition = "DECIMAL(4)")
	private BigDecimal bad_history;

	/** 騷擾號碼 **/
	@Column(name = "spam_num", columnDefinition = "DECIMAL(4)")
	private BigDecimal spam_num;

	/** 特殊號碼 **/
	@Column(name = "special_num", columnDefinition = "DECIMAL(4)")
	private BigDecimal special_num;

	/** 活躍頻率偏差 **/
	@Column(name = "using_frequency", columnDefinition = "DECIMAL(4)")
	private BigDecimal using_frequency;

	/** 可被聯繫 **/
	@Column(name = "reachable", columnDefinition = "DECIMAL(4)")
	private BigDecimal reachable;

	/** 最後活動區間 **/
	@Column(name = "last_activity_range", columnDefinition = "DECIMAL(4)")
	private BigDecimal last_activity_range;

	/** 社交狀態分析 **/
	@Column(name = "has_social_activity", columnDefinition = "DECIMAL(4)")
	private BigDecimal has_social_activity;

	/** 流量異常 **/
	@Column(name = "has_traffic_pattern", columnDefinition = "DECIMAL(4)")
	private BigDecimal has_traffic_pattern;

	/** 非常規行為 **/
	@Column(name = "has_abnormal_pattern", columnDefinition = "DECIMAL(4)")
	private BigDecimal has_abnormal_pattern;

	/** 跨國活動異常 **/
	@Column(name = "has_multiple_region", columnDefinition = "DECIMAL(4)")
	private BigDecimal has_multiple_region;

	/** 第一級金融群集 **/
	@Column(name = "whitelist_cluster", columnDefinition = "DECIMAL(4)")
	private BigDecimal whitelist_cluster;

	/** 疑似人頭戶群集 **/
	@Column(name = "phony_account_cluster", columnDefinition = "DECIMAL(4)")
	private BigDecimal phony_account_cluster;

	/** 代辦群集 **/
	@Column(name = "agency_cluster", columnDefinition = "DECIMAL(4)")
	private BigDecimal agency_cluster;

	/** 民間特殊借貸群集 **/
	@Column(name = "special_loan_cluster", columnDefinition = "DECIMAL(4)")
	private BigDecimal special_loan_cluster;

	/** 當鋪群集 **/
	@Column(name = "pawnshop_cluster", columnDefinition = "DECIMAL(4)")
	private BigDecimal pawnshop_cluster;

	/** 催繳群集 **/
	@Column(name = "debt_collect_cluster", columnDefinition = "DECIMAL(4)")
	private BigDecimal debt_collect_cluster;

	/** 八大行業群集 **/
	@Column(name = "spec_career_cluster", columnDefinition = "DECIMAL(4)")
	private BigDecimal spec_career_cluster;

	/** 第二級金融群集 **/
	@Column(name = "installment_cluster", columnDefinition = "DECIMAL(4)")
	private BigDecimal installment_cluster;

	/** 特定代書資料庫(客製化) **/
	@Column(name = "suspicious_laa_cluster", columnDefinition = "DECIMAL(4)")
	private BigDecimal suspicious_laa_cluster;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得借保人統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定借保人統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得借保人重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定借保人重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	public String getWfCreator() {
		return wfCreator;
	}

	public void setWfCreator(String wfCreator) {
		this.wfCreator = wfCreator;
	}

	public Timestamp getWfCreateTime() {
		return wfCreateTime;
	}

	public void setWfCreateTime(Timestamp wfCreateTime) {
		this.wfCreateTime = wfCreateTime;
	}

	public String getWfUpdater() {
		return wfUpdater;
	}

	public void setWfUpdater(String wfUpdater) {
		this.wfUpdater = wfUpdater;
	}

	public Timestamp getWfUpdateTime() {
		return wfUpdateTime;
	}

	public void setWfUpdateTime(Timestamp wfUpdateTime) {
		this.wfUpdateTime = wfUpdateTime;
	}

	public String getNum() {
		return num;
	}

	public void setNum(String num) {
		this.num = num;
	}

	public String getSpam_type() {
		return spam_type;
	}

	public void setSpam_type(String spam_type) {
		this.spam_type = spam_type;
	}

	public String getYp_type() {
		return yp_type;
	}

	public void setYp_type(String yp_type) {
		this.yp_type = yp_type;
	}

	public String getSpecial_type() {
		return special_type;
	}

	public void setSpecial_type(String special_type) {
		this.special_type = special_type;
	}

	public String getReport_tag() {
		return report_tag;
	}

	public void setReport_tag(String report_tag) {
		this.report_tag = report_tag;
	}

	public String getSpecial_tag() {
		return special_tag;
	}

	public void setSpecial_tag(String special_tag) {
		this.special_tag = special_tag;
	}

	public String getIs_invalid_number() {
		return is_invalid_number;
	}

	public void setIs_invalid_number(String is_invalid_number) {
		this.is_invalid_number = is_invalid_number;
	}

	public String getRank() {
		return rank;
	}

	public void setRank(String rank) {
		this.rank = rank;
	}

	public String getUtime() {
		return utime;
	}

	public void setUtime(String utime) {
		this.utime = utime;
	}

	public String getNumber_origin() {
		return number_origin;
	}

	public void setNumber_origin(String number_origin) {
		this.number_origin = number_origin;
	}

	public boolean isIs_verify() {
		return is_verify;
	}

	public void setIs_verify(boolean is_verify) {
		this.is_verify = is_verify;
	}

	public BigDecimal getScore() {
		return score;
	}

	public void setScore(BigDecimal score) {
		this.score = score;
	}

	public BigDecimal getAlert() {
		return alert;
	}

	public void setAlert(BigDecimal alert) {
		this.alert = alert;
	}

	public BigDecimal getIn_fraud_db() {
		return in_fraud_db;
	}

	public void setIn_fraud_db(BigDecimal in_fraud_db) {
		this.in_fraud_db = in_fraud_db;
	}

	public BigDecimal getBad_history() {
		return bad_history;
	}

	public void setBad_history(BigDecimal bad_history) {
		this.bad_history = bad_history;
	}

	public BigDecimal getSpam_num() {
		return spam_num;
	}

	public void setSpam_num(BigDecimal spam_num) {
		this.spam_num = spam_num;
	}

	public BigDecimal getSpecial_num() {
		return special_num;
	}

	public void setSpecial_num(BigDecimal special_num) {
		this.special_num = special_num;
	}

	public BigDecimal getUsing_frequency() {
		return using_frequency;
	}

	public void setUsing_frequency(BigDecimal using_frequency) {
		this.using_frequency = using_frequency;
	}

	public BigDecimal getReachable() {
		return reachable;
	}

	public void setReachable(BigDecimal reachable) {
		this.reachable = reachable;
	}

	public BigDecimal getLast_activity_range() {
		return last_activity_range;
	}

	public void setLast_activity_range(BigDecimal last_activity_range) {
		this.last_activity_range = last_activity_range;
	}

	public BigDecimal getHas_social_activity() {
		return has_social_activity;
	}

	public void setHas_social_activity(BigDecimal has_social_activity) {
		this.has_social_activity = has_social_activity;
	}

	public BigDecimal getHas_traffic_pattern() {
		return has_traffic_pattern;
	}

	public void setHas_traffic_pattern(BigDecimal has_traffic_pattern) {
		this.has_traffic_pattern = has_traffic_pattern;
	}

	public BigDecimal getHas_abnormal_pattern() {
		return has_abnormal_pattern;
	}

	public void setHas_abnormal_pattern(BigDecimal has_abnormal_pattern) {
		this.has_abnormal_pattern = has_abnormal_pattern;
	}

	public BigDecimal getHas_multiple_region() {
		return has_multiple_region;
	}

	public void setHas_multiple_region(BigDecimal has_multiple_region) {
		this.has_multiple_region = has_multiple_region;
	}

	public BigDecimal getWhitelist_cluster() {
		return whitelist_cluster;
	}

	public void setWhitelist_cluster(BigDecimal whitelist_cluster) {
		this.whitelist_cluster = whitelist_cluster;
	}

	public BigDecimal getPhony_account_cluster() {
		return phony_account_cluster;
	}

	public void setPhony_account_cluster(BigDecimal phony_account_cluster) {
		this.phony_account_cluster = phony_account_cluster;
	}

	public BigDecimal getAgency_cluster() {
		return agency_cluster;
	}

	public void setAgency_cluster(BigDecimal agency_cluster) {
		this.agency_cluster = agency_cluster;
	}

	public BigDecimal getSpecial_loan_cluster() {
		return special_loan_cluster;
	}

	public void setSpecial_loan_cluster(BigDecimal special_loan_cluster) {
		this.special_loan_cluster = special_loan_cluster;
	}

	public BigDecimal getPawnshop_cluster() {
		return pawnshop_cluster;
	}

	public void setPawnshop_cluster(BigDecimal pawnshop_cluster) {
		this.pawnshop_cluster = pawnshop_cluster;
	}

	public BigDecimal getDebt_collect_cluster() {
		return debt_collect_cluster;
	}

	public void setDebt_collect_cluster(BigDecimal debt_collect_cluster) {
		this.debt_collect_cluster = debt_collect_cluster;
	}

	public BigDecimal getSpec_career_cluster() {
		return spec_career_cluster;
	}

	public void setSpec_career_cluster(BigDecimal spec_career_cluster) {
		this.spec_career_cluster = spec_career_cluster;
	}

	public BigDecimal getInstallment_cluster() {
		return installment_cluster;
	}

	public void setInstallment_cluster(BigDecimal installment_cluster) {
		this.installment_cluster = installment_cluster;
	}

	public BigDecimal getSuspicious_laa_cluster() {
		return suspicious_laa_cluster;
	}

	public void setSuspicious_laa_cluster(BigDecimal suspicious_laa_cluster) {
		this.suspicious_laa_cluster = suspicious_laa_cluster;
	}

	public String getMpnum() {
		return mpnum;
	}

	public void setMpnum(String mpnum) {
		this.mpnum = mpnum;
	}

}
