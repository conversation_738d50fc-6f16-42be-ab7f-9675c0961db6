---------------------------------------------------------
-- LMS.L140S02A 個金產品種類檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140S02A;
CREATE TABLE LMS.L140S02A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	SEQ           DECIMAL(5,0)  not null,
	SECNO         DECIMAL(5,0) ,
	C<PERSON><PERSON><PERSON>O        CHAR(12)     ,
	PROPERTY      VARCHAR(2)   ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>        CHAR(14)     ,
	<PERSON><PERSON><PERSON>CODE     CHAR(1)      ,
	OR<PERSON><PERSON>OANNO     CHAR(14)     ,
	LNSELECT      CHAR(1)      ,
	LNFROMDATE    DATE         ,
	LNENDDATE     DATE         ,
	LNYEAR        DECIMAL(2,0) ,
	LNMONTH       DECIMAL(2,0) ,
	LNOTHER       VARCHAR(350) ,
	PRODKIND      CHAR(02)     ,
	FOR921CASE    CHAR(01)     ,
	SUBJCODE      VARCHAR(8)   ,
	CASENAME      VARCHAR(180) ,
	BUILDNAME     VARCHAR(180) ,
	LANDCITY      VARCHAR(10)  ,
	LANDAREA      VARCHAR(3)   ,
	LANDPART1     VARCHAR(15)  ,
	LANDPART2     VARCHAR(15)  ,
	CUSTID        VARCHAR(10)  ,
	DUPNO         CHAR(1)      ,
	GRADE1        CHAR(2)      ,
	CHGCONTRACT   CHAR(1)      ,
	CHGCONTIMES   DECIMAL(3,0) ,
	USETYPE       CHAR(1)      ,
	DBR22CURR     CHAR(3)      ,
	DBR22AMT      DECIMAL(13,0),
	FFUND         CHAR(1)      ,
	DFUND         CHAR(4)      ,
	LOANCURR      CHAR(3)      ,
	LOANAMT       DECIMAL(13,0),
	AGENCYAMT     DECIMAL(15,2),
	CHKUSED       CHAR(1)      ,
	LNPURPOSE     CHAR(1)      ,
	LNPURS        CHAR(1)      ,
	RESIDENTIAL   CHAR(1)      ,
	IMPORTID      VARCHAR(6)   ,
	SELLBANK      CHAR(3)      ,
	GUTPERCENT    DECIMAL(5,2) ,
	RATEDESC      VARCHAR(3072),
	FREERATEDESC  VARCHAR(768) ,
	PAYOFFWAY     VARCHAR(1200) ,
	PERIODCHK     CHAR(1)      ,
	PERIODCURR    CHAR(3)      ,
	PERIODAMT     DECIMAL(13,0),
	PERIODSDATE   DATE         ,
	PERIODEDATE   DATE         ,
	CHGCONSDATE   DATE         ,
	CHGCONEDATE   DATE         ,
	CHARGEBACKWAY CHAR(1)      ,
	UNDERTAKING   VARCHAR(5)   ,
	CHKYN         CHAR(1)      ,
	CREATSRC      VARCHAR(1)   ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,
	SUBPROPERTY   VARCHAR(30)  ,
	MODELKIND	  CHAR(1) ,
	SRCLOANNO	  CHAR(14) ,
    SRCPRODKIND	  CHAR(02) ,
    SRCSUBJCODE	  VARCHAR(8) ,
	constraint P_L140S02A PRIMARY KEY(OID)
) IN EL_DATA_8KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140S02A01;
CREATE UNIQUE INDEX LMS.XL140S02A01 ON LMS.L140S02A   (MAINID, SEQ);

CREATE INDEX LMS.XL140S02A02 ON LMS.L140S02A   (MAINID, PROPERTY, PRODKIND, RESIDENTIAL) ALLOW REVERSE SCANS;
REORG INDEXES ALL FOR TABLE LMS.L140S02A  ALLOW WRITE ACCESS;
RUNSTATS ON TABLE LMS.L140S02A  WITH DISTRIBUTION AND DETAILED INDEXES ALL ALLOW WRITE ACCESS;

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140S02A IS '個金產品種類檔';
COMMENT ON LMS.L140S02A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	SEQ           IS '序號', 
	SECNO         IS '流水號', 
	CNTRNO        IS '額度序號', 
	PROPERTY      IS '承做性質', 
	LOANNO        IS '放款帳號', 
	TRANSCODE     IS '開戶原因', 
	ORGLOANNO     IS '承接之前放款帳號', 
	LNSELECT      IS '授信期間選項', 
	LNFROMDATE    IS '授信期間-起始日期', 
	LNENDDATE     IS '授信期間-截止日期', 
	LNYEAR        IS '授信期間-年數', 
	LNMONTH       IS '授信期間-月數', 
	LNOTHER       IS '授信期間-其他', 
	PRODKIND      IS '產品種類', 
	FOR921CASE    IS '購置', 
	SUBJCODE      IS '科目', 
	CASENAME      IS '團貸案名稱', 
	BUILDNAME     IS '建案名稱', 
	LANDCITY      IS '土地坐落區-縣市', 
	LANDAREA      IS '土地坐落區-區域', 
	LANDPART1     IS '土地坐落區-大段', 
	LANDPART2     IS '土地坐落區-小段', 
	CUSTID        IS '最終評等－採用借保人ID', 
	DUPNO         IS '最終評等－採用借保人重複序號', 
	GRADE1        IS '最終評等－評等', 
	CHGCONTRACT   IS '是否重簽契約', 
	CHGCONTIMES   IS '現已續約次數', 
	USETYPE       IS '動用方式', 
	DBR22CURR     IS '應計入DBR22倍規範額度－幣別', 
	DBR22AMT      IS '應計入DBR22倍規範額度－金額', 
	FFUND         IS '資金來源', 
	DFUND         IS '資金來源小類', 
	LOANCURR      IS '動撥幣別', 
	LOANAMT       IS '動撥金額', 
	AGENCYAMT     IS '開辦費',
	CHKUSED       IS '是否搭配長擔貸款', 
	LNPURPOSE     IS '融資業務分類', 
	LNPURS        IS '用途別', 
	RESIDENTIAL   IS '是否屬興建住宅', 
	IMPORTID      IS '引介行員代號', 
	SELLBANK      IS '行銷分行', 
	GUTPERCENT    IS '信保成數%', 
	RATEDESC      IS '利率', 
	FREERATEDESC  IS '費率', 
	PAYOFFWAY     IS '償還方式', 
	PERIODCHK     IS '期付金是否超過薪水1/3', 
	PERIODCURR    IS '期付金幣別', 
	PERIODAMT     IS '期付金金額', 
	PERIODSDATE   IS '期付金對照表起日', 
	PERIODEDATE   IS '期付金對照表迄日', 
	CHGCONSDATE   IS '契約起日', 
	CHGCONEDATE   IS '契約迄日',
	CHARGEBACKWAY IS '償還扣款方式',
	UNDERTAKING   IS '承諾書',
	CHKYN         IS '輸入資料檢誤完成', 
	CREATSRC      IS '新增來源',
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期',
	SUBPROPERTY   IS '次要性質',
	MODELKIND	  IS '選用模型',
	SRCLOANNO	  IS '原始放款帳號',
	SRCPRODKIND	  IS '原始產品種類',
	SRCSUBJCODE	  IS '原始科目'
);
