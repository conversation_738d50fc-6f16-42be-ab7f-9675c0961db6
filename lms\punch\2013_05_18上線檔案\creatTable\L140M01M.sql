---------------------------------------------------------
-- LMS.L140M01M 維護央行購屋/空地/建屋貸款註記資訊
---------------------------------------------------------
---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140M01M;
CREATE TABLE LMS.L140M01M (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	CBCCASE       CHAR(1)       not null,
	PAYPERCENT    DECIMAL(5,2) ,
	CITYID        VARCHAR(1)   ,
	AREAID        VARCHAR(3)   ,
	CUSTYN        CHAR(1)      ,
	APPAMT        DECIMAL(13,0),
	BUILDYN       CHAR(1)      ,
	COMMONYN      CHAR(1)      ,
	SHARECOLLYN   CHAR(1)      ,
	SHARECOLLAMT  DECIMAL(13,0),
	PLUSREASON    CHAR(1)      ,
	PLUSREASONMEMO VARCHAR(150) ,
	NOWAMT        DECIMAL(13,0),
	VALUEAMT      DECIMAL(13,0),
	HOUSEYN       CHAR(1)      ,
	HOUSETYPE     CHAR(1)      ,
	PURPOSETYPE   CHAR(1)      ,
	CMSTYPE       CHAR(1)      ,
	KEEPYN        CHAR(1)      ,
	HOUSEOTHER	  VARCHAR(120) ,
	CMSOTHER	  VARCHAR(120) ,
	ISLIMITCUST	  CHAR(1)      ,
	ISHIGHHOUSE	  CHAR(1)      ,
	SIT3NO        DECIMAL(4,0) ,
	SIT4NO        VARCHAR(11)  ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,
	CHECKYN		  CHAR(1)	,
	SLNDTAX		  VARCHAR(180)	,
	LANDBUILDYN	  CHAR(1)	,
	PRODCLASS	  CHAR(2)	,	
	BUILDDATE	  DATE	,
	WAITMONTH	  DECIMAL(3,0)	,
	LOCATIONCD	  VARCHAR(3)	,
	SITE3NO		  DECIMAL(4,0)	,
	SITE4NO		  CHAR(11)	,
	LANDTYPE	  CHAR(1)	,
	AREALAND	  DECIMAL(13,2)	,
	LOCATIONCITY  CHAR(3)	,
	LANDBUILDCNTRNO CHAR(12)	,
	constraint P_L140M01M PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140M01M01;
CREATE UNIQUE INDEX LMS.XL140M01M01 ON LMS.L140M01M   (MAINID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140M01M IS '維護央行購屋/空地/建屋貸款註記資訊';
COMMENT ON LMS.L140M01M (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	CBCCASE       IS '央行購住狀態', 
	PAYPERCENT    IS '貸款成數', 
	CITYID        IS '縣市代碼', 
	AREAID        IS '鄉鎮市區代碼', 
	CUSTYN        IS '借款人聯徵名下有無 - 用途別為[1]之其他房貸資料', 
	APPAMT        IS '擔保品購價(台幣元)', 
	BUILDYN       IS '建物謄本登記用途是否屬「住」類，或「住商」類等無營業登記', 
	COMMONYN      IS '有無與其他帳號共用同一擔保品', 
	SHARECOLLYN   IS '有無授權外核准得分批撥款「擔保品總貸款額度」', 
	SHARECOLLAMT  IS '授權外核准得分批撥款「擔保品總貸款額度」', 
	PLUSREASON    IS '非央行控管種類', 
	PLUSREASONMEMO IS '非央行控管種類原因', 
	NOWAMT        IS '擔保品鑑價(台幣元)', 
	VALUEAMT      IS '擔保品估值(台幣元)', 
	HOUSEYN       IS '是否屬都市計畫劃定之區域', 
	HOUSETYPE     IS '都市計畫劃定之使用分區', 
	PURPOSETYPE   IS '是否借款用途', 
	CMSTYPE       IS '是否擔保品性質別', 
	KEEPYN        IS '是否保留一成估值', 
	HOUSEOTHER	  IS '都市計畫劃定之使用分區-其他',
	CMSOTHER	  IS '是否擔保品性質別-其他',
	ISLIMITCUST	  IS '是否為央行受限戶',
	ISHIGHHOUSE	  IS '是否為高價住宅',
	SIT3NO        IS '擔保品座落-段',
	SIT4NO        IS '擔保品座落-村',
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期',
	CHECKYN		  IS '更新註記,
	SLNDTAX		  IS '引進之擔保備註,
	LANDBUILDYN	  IS '是否屬土建融案,
    PRODCLASS	  IS '產品種類,    
    BUILDDATE	  IS '預計取得建照日期,
    WAITMONTH	  IS '預計撥款至動工期間月數,
    LOCATIONCD	  IS '擔保品座落,
    SITE3NO	      IS '座落區段,
    SITE4NO	      IS '座落區村里,
    LANDTYPE	  IS '土地使用分區,
    AREALAND	  IS '土地面積,
    LOCATIONCITY  IS '擔保品座落縣市,
    LANDBUILDCNTRNO IS '預約額度序號,
);
