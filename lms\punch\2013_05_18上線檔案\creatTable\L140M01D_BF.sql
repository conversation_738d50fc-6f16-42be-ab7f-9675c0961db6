---------------------------------------------------------
-- LMS.L140M01D_BF 額度授信科目限額檔(變更前)
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140M01D_BF;
CREATE TABLE LMS.L140M01D_BF (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	<PERSON><PERSON><PERSON><PERSON><PERSON>       CHAR(1)       not null,
	LMTSEQ        DECIMAL(5,0)  not null,
	SUBJECT       VARCHAR(300) ,
	LMTCURR       CHAR(3)      ,
	LMTAMT        DECIMAL(13,0),
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140M01D_BF PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140M01D_BF01;
CREATE UNIQUE INDEX LMS.XL140M01D_BF01 ON LMS.L140M01D_BF (MAINID, LMTTYPE, LMTSEQ);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140M01D_BF IS '額度授信科目限額檔(變更前)';
COMMENT ON LMS.L140M01D_BF (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	LMTTYPE       IS '限額類型', 
	LMTSEQ        IS '序號', 
	SUBJECT       IS '授信科目組合', 
	LMTCURR       IS '限額－幣別', 
	LMTAMT        IS '限額－金額', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
