/* 
 * LMSBATCHDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.LMSBATCH;

/** 授信批次報表檔 **/
public interface LMSBATCHDao extends IGenericDao<LMSBATCH> {

	LMSBATCH findByOid(String oid);

	List<LMSBATCH> findByMainId(String mainId);

	List<LMSBATCH> findByIndex01(String branch, Date endDate, String rptNo,
			String nowRpt);

	List<LMSBATCH> findByIndex02(String branch, Date bgnDate, Date endDate,
			String rptNo, String nowRpt);

	LMSBATCH findByIndex03(String mainId);

	List<LMSBATCH> findByIndex04(String mainId, String rptNo, Date dataDate);
}