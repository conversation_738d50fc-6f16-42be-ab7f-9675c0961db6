package com.mega.eloan.lms.fms.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.dao.L140MM2ADao;
import com.mega.eloan.lms.dao.L140S02LDao;
import com.mega.eloan.lms.fms.service.CLS2801Service;
import com.mega.eloan.lms.model.L140MM2A;
import com.mega.eloan.lms.model.L140S02L;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

@Service
public class CLS2801ServiceImpl extends AbstractCapService implements
		CLS2801Service {
	private static final Logger logger = LoggerFactory
			.getLogger(CLS2801ServiceImpl.class);
	
	@Resource
	L140S02LDao l140s02lDao;

	@Resource
	L140MM2ADao l140mm2aDao;
	
	@Resource
	TempDataService tempDataService;
	
	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L140MM2A) {
					L140MM2A l140mm2a = (L140MM2A) model;
					if (Util.isNotEmpty(l140mm2a.getOid()) ) {
						l140mm2a.setUpdater(user.getUserId());
						l140mm2a.setUpdateTime(CapDate.getCurrentTimestamp());
					}
					l140mm2aDao.save(l140mm2a);
					
					if (Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(l140mm2a.getMainId());
					}
				}else if (model instanceof L140S02L) {
					l140s02lDao.save((L140S02L)model);
				} 
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L140S02L) {
					L140S02L l140s02l = (L140S02L) model;
					if (Util.isNotEmpty(l140s02l.getOid()) ) {
						//刪除資料
						l140s02lDao.delete(l140s02l);
					}
				}
			}
		}
	}

	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L140MM2A.class) {
			return l140mm2aDao.findPage(search);
		}else if (clazz == L140S02L.class) {
			return l140s02lDao.findPage(search);
		}
		return null;
	}

	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L140S02L.class) {
			return (T) l140s02lDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public L140S02L findL140S02L_oid(String oid){
		return l140s02lDao.findByOid(oid);
	}
	
	@Override
	public L140S02L findByUniqueKey(String mainId, Integer seq){
		return l140s02lDao.findByUniqueKey(mainId, 0);
	}
	
	@Override
	public L140MM2A findL140MM2A_oid(String oid){
		return l140mm2aDao.findByOid(oid);
	}
	
	@Override
	public L140MM2A findL140MM2A_MainIdSeq(String mainId, Integer seq){
		return l140mm2aDao.findByMainIdSeq(mainId, 0);
	}
}
