/* 
 * L120S08BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S08BDao;
import com.mega.eloan.lms.model.L120S08B;

/** 利率定價核理性分析表明細檔 **/
@Repository
public class L120S08BDaoImpl extends LMSJpaDao<L120S08B, String>
	implements L120S08BDao {

	@Override
	public L120S08B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S08B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S08B> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S08B> findByIndex01(String mainId){
		ISearch search = createSearchTemplete();
		List<L120S08B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S08B> findByIndex02(String mainId, String curr){
		ISearch search = createSearchTemplete();
		List<L120S08B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (curr != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "curr", curr);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S08B> findByMainIdCurrSeqNo(String mainId, String curr, BigDecimal seqNo){
		ISearch search = createSearchTemplete();
		List<L120S08B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (curr != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "curr", curr);
		if (seqNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seqNo", seqNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public L120S08B findByMainIdCurrSeqNoItemName(String mainId, String curr, BigDecimal seqNo, String itemName){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "curr", curr);
		search.addSearchModeParameters(SearchMode.EQUALS, "seqNo", seqNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemName", itemName);
		return findUniqueOrNone(search);
	}
}