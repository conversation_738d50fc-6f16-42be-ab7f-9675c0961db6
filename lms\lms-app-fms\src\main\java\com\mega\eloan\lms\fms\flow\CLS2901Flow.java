package com.mega.eloan.lms.fms.flow;

import java.sql.Timestamp;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.mfaloan.service.MisLnunIdService;
import com.mega.eloan.lms.model.C900M01J;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;


@Component
public class CLS2901Flow extends AbstractFlowHandler {
	public static final String FLOW_CODE = "CLS2901Flow";

	@Resource
	CLSService clsService;
	
	@Resource
	MisLnunIdService misLnunIdService;
	
	@Transition(node = "開始", value = "起案")
	public void init_flow(FlowInstance instance) {		
		String oid = Util.trim(instance.getId());
		
		C900M01J meta = clsService.findC900M01J_oid(oid);
		if(true){
			meta.setApprover(null);
			meta.setApproveTime(null);	
		}		
		clsService.daoSave(meta);
	}
	
	
	@Transition(node = "確認", value = "核定")
	public void apply(FlowInstance instance) {
		String oid = Util.trim(instance.getId());
		
		C900M01J meta = clsService.findC900M01J_oid(oid);
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		if(true){			
			// 婉卻原因代碼
			// select * from com.bcodetype where codetype='RejtCode' and locale='zh_TW'
			int REFUSECD = 23;
			
			//婉卻說明
			String memo = clsService.get_C900M01J_output_memo(meta);
			
			/*if(Util.isEmpty(memo)){
				memo = "人頭戶或代辦案件黑名單建檔作業";
			}*/
			String REFUSEDS = Util.trimSizeInOS390("本行人頭戶或代辦案件黑名單。"+memo, 200);
			
			//比照  LMSService :: upLnunid(L120M01A meta) 
			String RFSAUTH = "";
			String CLSCASE = "C";
			String CARDREJ = "";
			String STATUSCD = "1";
			String OID = ""; //LMSUtil.getUploadCaseNo(meta);
			
			/*
			 【LMSM01FormHandler】
			 	Map<String, Object> lnun = lnunSrv.findByIdDup(custId, dupNo);
				if (lnun != null && !lnun.isEmpty()) {
				}
			 【CLS1131ServiceImpl】
			 	Map<String, Object> map = misLnunIdService.queryReject(custId,
					dupNo);
				if (map != null) {
				}
			 */
			misLnunIdService.insertLnunId(meta.getCustId(), meta.getDupNo(), nowTS, meta.getOwnBrId(), meta.getUpdater(), 
					REFUSECD, REFUSEDS, RFSAUTH, meta.getUpdater(), nowTS, CLSCASE, CARDREJ, 
					Util.trimSizeInOS390(Util.trim(meta.getCustName()), 44), STATUSCD, OID);
			//=========================
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			meta.setApprover(user.getUserId());
			meta.setApproveTime(nowTS);	
		}
		clsService.daoSave(meta);
	}
	//=================================================
	@Transition(node = "已核准", value = "呈主管")
	public void send(FlowInstance instance) {
		String oid = Util.trim(instance.getId());
		
		C900M01J meta = clsService.findC900M01J_oid(oid);
		if(true){
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			meta.setDcUpdater(user.getUserId());
			meta.setDcUpdateTime(CapDate.getCurrentTimestamp());	
		}
		clsService.daoSave(meta);		
	}	
	
	@Transition(node = "解除確認", value = "解除退回")
	public void removeBack(FlowInstance instance) {
		String oid = Util.trim(instance.getId());
		
		C900M01J meta = clsService.findC900M01J_oid(oid);
		if(true){
			meta.setDcUpdater(null);
			meta.setDcUpdateTime(null);	
		}
		clsService.daoSave(meta);
	}
	
	@Transition(node = "解除確認", value = "解除核定")
	public void removeOK(FlowInstance instance) {
		String oid = Util.trim(instance.getId());
		
		C900M01J meta = clsService.findC900M01J_oid(oid);
		if(true){
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			meta.setDcApprover(user.getUserId());
			meta.setDcApproveTime(CapDate.getCurrentTimestamp());	
		}
		clsService.daoSave(meta);
	}
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return C900M01J.class;
	}


	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return FlowDocStatusEnum.class;
	}
}