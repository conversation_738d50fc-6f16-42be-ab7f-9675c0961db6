package com.mega.eloan.lms.fms.pages;

import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.fms.service.CLS2601Service;
import com.mega.eloan.lms.model.C900M01H;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.auth.AuthType;


@Controller
@RequestMapping(path = "/fms/cls2601m01/{page}")
public class CLS2601M01Page extends AbstractEloanForm {

	@Autowired
	CLSService clsService;
	
	@Resource
	CLS2601Service service;
	
	public CLS2601M01Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		new DocLogPanel("_docLog").processPanelData(model, params);
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = params.getString(EloanConstants.MAIN_DOC_STATUS);
		
		
		addAclLabel(model, new AclLabel("_btnSave", params, getDomainClass(),
				AuthType.Modify	, FlowDocStatusEnum.編製中));
		
		if (docStatus.equals("030|0C0") ){
			//黑名單查詢一律不顯示
			model.addAttribute("_btnWAIT_APPROVE", false );
			model.addAttribute("_btnWAIT_REMOVE", false );
			model.addAttribute("_btnRemove", false );
		}else{
			addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
					AuthType.Accept, false, FlowDocStatusEnum.待覆核));
			addAclLabel(model, new AclLabel("_btnWAIT_REMOVE", params, getDomainClass(),
					AuthType.Accept, false,FlowDocStatusEnum.待解除));
			addAclLabel(model, new AclLabel("_btnRemove", params, getDomainClass(),
					AuthType.Modify, false, FlowDocStatusEnum.已核准));
		}
		//======
        if(true){
            String link_laaWord_url = "http://pip.moi.gov.tw/V2/G/SCRG0404.aspx";
            Map<String, String> url_map = clsService.get_codeTypeWithOrder("C101S01E_laaWord_url");
            if(url_map.containsKey("url")){
            	link_laaWord_url = url_map.get("url");
            }
            StringBuilder sbLaa = new StringBuilder();
            sbLaa.append("<a ");
            sbLaa.append(" href=\""+link_laaWord_url+"\" ");
            sbLaa.append(" target=\"_blank\" ");
            sbLaa.append(">");
            sbLaa.append("內政部‧開業地政士查詢");
            sbLaa.append("</a>");
            // UPGRADE: 前端須配合改為 <span th:utext......>（類似於 setEscapeModelStrings(false)，不轉義 HTML，直接解析）
            // Label laaLabel = new Label("C101S01E_laaWord_url", sbLaa.toString());
            // laaLabel.setEscapeModelStrings(false);
            // add(laaLabel);
            model.addAttribute("C101S01E_laaWord_url", sbLaa.toString());
        }
        
		renderJsI18N(CLS2601M01Page.class);
	}

	@Override
	public Class<? extends Meta> getDomainClass() {		
		return C900M01H.class;
	}
}
