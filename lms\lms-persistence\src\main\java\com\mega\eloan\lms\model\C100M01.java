/* 
 * C100M01.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.validation.group.Check;

/** 擔保品主檔 **/
@Entity
@Table(name = "C100M01", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C100M01 extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 鑑價單位代號 **/
	@Column(name = "ESTBRN", length = 3, columnDefinition = "CHAR(3)")
	private String estBrn;

	/**
	 * 分行代碼
	 * <p/>
	 * 擔保品所屬分行
	 */
	@Column(name = "BRANCH", length = 3, columnDefinition = "CHAR(3)")
	private String branch;

	/** 鑑估日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ESTDATE", columnDefinition = "DATE")
	private Date estDate;

	/**
	 * OBS文件狀態
	 * <p/>
	 * 1待補登2補登待覆核3補登已覆核<br/>
	 * 4待塗銷5塗銷待覆核6塗銷已覆核
	 */
	@Column(name = "OBSSTATUS", length = 1, columnDefinition = "CHAR(01)")
	private String obsStatus;

	/**
	 * 借款類別
	 * <p/>
	 * 1.授信、2.進口、3.遠匯、4.出口
	 */
	@Column(name = "LNKIND", length = 1, columnDefinition = "CHAR(1)")
	private String lnKind;

	/**
	 * 擔保品大類
	 * <p/>
	 * 詳擔保品編號
	 */
	@Column(name = "COLLTYP1", length = 2, columnDefinition = "CHAR(2)")
	private String collTyp1;

	/**
	 * 擔保品小類
	 * <p/>
	 * 詳擔保品編號
	 */
	@Column(name = "COLLTYP2", length = 2, columnDefinition = "CHAR(2)")
	private String collTyp2;

	/** 擔保品編號【註2】 **/
	@Column(name = "COLLNO", length = 9, columnDefinition = "VARCHAR(09)")
	private String collNo;

	/**
	 * 擔保品種類
	 * <p/>
	 * 1國內擔保品、2海外擔保品
	 */
	@Column(name = "COLLKIND", length = 1, columnDefinition = "CHAR(1)")
	private String collKind;

	/**
	 * 擔保品設定類別
	 * <p/>
	 * 1自行保管2聯行委託代保管
	 */
	@Column(name = "SETTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String setType;

	/** 委託分行代碼 **/
	@Column(name = "SETBRANCH", length = 3, columnDefinition = "CHAR(3)")
	private String setBranch;

	/**
	 * 是否為買賣契約
	 * <p/>
	 * Y/N，買賣件
	 */
	@Column(name = "CTRFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String ctrFlag;

	/** 表單種類【註3】 **/
	@Column(name = "RPTTYPE", length = 2, columnDefinition = "CHAR(2)")
	private String rptType;

	/** 電子謄本申請編號 **/
	@Column(name = "BIOID", length = 15, columnDefinition = "VARCHAR(15)")
	private String bioid;

	/** 電子謄本申請日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELDATE", columnDefinition = "DATE")
	private Date elDate;

	/**
	 * 中長期案件
	 * <p/>
	 * 1是2否
	 */
	@Column(name = "LONGCASE", length = 1, columnDefinition = "CHAR(01)")
	private String longCase;

	/**
	 * 寬限期/預提折舊-年
	 * <p/>
	 * 不動產中長期：寬限期<br/>
	 * 不動產非中長期：預提折舊
	 */
	@Column(name = "EXTYEAR", columnDefinition = "DECIMAL(02)")
	private Integer extYear;

	/**
	 * 寬限期/預提折舊-月
	 * <p/>
	 * 同上
	 */
	@Column(name = "EXTMONTH", columnDefinition = "DECIMAL(02)")
	private Integer extMonth;

	/**
	 * 鑑價方式【註4】
	 * <p/>
	 * 不動產
	 */
	@Column(name = "TAXTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String taxType;

	/**
	 * 面積單位
	 * <p/>
	 * 計量單位。<br/>
	 * 1平方公尺、2平方公里、3坪、4公頃、5公畝、6英畝<br/>
	 * 國內擔保品預設平方公尺
	 */
	@Column(name = "ESTUNIT", length = 2, columnDefinition = "CHAR(02)")
	private String estUnit;

	/**
	 * 國別
	 * <p/>
	 * 國內擔保品預設TW
	 */
	@Column(name = "NATCODE", length = 2, columnDefinition = "CHAR(2)")
	private String natCode;

	/**
	 * 幣別
	 * <p/>
	 * 國內擔保品預設TWD
	 */
	@Column(name = "CURRCD", length = 3, columnDefinition = "CHAR(3)")
	private String currCd;

	/** 折合台幣匯率 **/
	@Column(name = "TWDRATE", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal twdRate;

	/** 折合美元匯率 **/
	@Column(name = "USDRATE", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal usdRate;

	/**
	 * 鑑估金額（原幣）
	 * <p/>
	 * 元
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "APPAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal appAmt;

	/**
	 * 鑑估金額(TWD)
	 * <p/>
	 * 鑑估金額折合美元
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "TWDAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal twdAmt;

	/**
	 * 鑑估金額(USD)
	 * <p/>
	 * 鑑估金額折合美元
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "USDAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal usdAmt;

	/** 貸放成數/平均貸放成數 **/
	@Column(name = "LOANRT", columnDefinition = "DECIMAL(3,0)")
	private Integer loanRt;

	/**
	 * 放款值(原幣)
	 * <p/>
	 * 元
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LOANAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal loanAmt;

	/**
	 * 放款值（TWD）
	 * <p/>
	 * 放款值折合台幣
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LOANTWD", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal loanTwd;

	/**
	 * 放款值（USD）
	 * <p/>
	 * 放款值折合美元
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LOANUSD", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal loanUsd;

	/** 聯貸幣別 **/
	@Column(name = "JOINCD", length = 3, columnDefinition = "CHAR(3)")
	private String joinCd;

	/** 聯貸幣別折合美元匯率 **/
	@Column(name = "JOINRATE", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal joinRate;

	/** 聯貸總金額(原幣) **/
	@Column(name = "MONEYTO", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal moneyTo;

	/** 聯貸總金額(台幣) **/
	@Column(name = "MONEYTOTWD", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal moneyToTWD;

	/** 聯貸總金額(美元) **/
	@Column(name = "MONEYTOUSD", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal moneyToUSD;

	/** 參貸金額(原幣) **/
	@Column(name = "JOINAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal joinAmt;

	/** 參貸金額(台幣) **/
	@Column(name = "JOINAMTTWD", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal joinAmtTWD;

	/** 參貸金額(美元) **/
	@Column(name = "JOINAMTUSD", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal joinAmtUSD;

	/** 非本行他項權利金額合計 **/
	@Column(name = "OTHAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal othAmt;

	/** 放款值（扣除非本行他項權利金額合計） **/
	@Column(name = "MEGAAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal megaAmt;

	/**
	 * 主辦行代碼
	 * <p/>
	 * 參貸他行使用
	 */
	@Column(name = "HSTBKCD", length = 3, columnDefinition = "CHAR(03)")
	private String hstBkCd;

	/**
	 * 主辦行名稱
	 * <p/>
	 * 參貸他行使用
	 */
	@Column(name = "HSTBKNM", length = 48, columnDefinition = "VARCHAR(48)")
	private String hstBkNm;

	/** 前順位金額合計 **/
	@Column(name = "PRIAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal priAmt;

	/**
	 * 是否為聯貸案件
	 * <p/>
	 * Y/N
	 */
	@Column(name = "ISSLOAN", length = 1, columnDefinition = "CHAR(1)")
	private String isSLoan;

	/** 買賣契約日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CTRDATE", columnDefinition = "DATE")
	private Date ctrDate;

	/**
	 * 土地明細筆數
	 * <p/>
	 * 不動產以外為明細資料筆數
	 */
	@Column(name = "LNDNUM", columnDefinition = "INTEGER")
	private Integer lndNum;

	/**
	 * 建物明細筆數
	 * <p/>
	 * 不動產以外為0（暫保留未用）
	 */
	@Column(name = "BLDNUM", columnDefinition = "INTEGER")
	private Integer bldNum;

	/** 租賃用益減值 **/
	@Column(name = "RENTAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal rentAmt;

	/**
	 * 異動原因
	 * <p/>
	 * （股票）S新增、R補提
	 */
	@Column(name = "TXCD", length = 1, columnDefinition = "CHAR(01)")
	private String txCd;

	/**
	 * 異動日期
	 * <p/>
	 * （股票）
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "TXDATE", columnDefinition = "DATE")
	private Date txDate;

	/**
	 * 輸入資料檢誤完成
	 * <p/>
	 * Y/N，鑑價輸入資料檢誤
	 */
	@Column(name = "CHKYN", length = 1, columnDefinition = "CHAR(1)")
	private String chkYn;

	/**
	 * 擔保品有效期限
	 * <p/>
	 * Collmstr現有欄位，未使用，先保留給Null值。（上傳misdb給空白）
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "VALIDATE", columnDefinition = "DATE")
	private Date valiDate;

	/**
	 * 折舊止日
	 * <p/>
	 * 動產用
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "FNSHDATE", columnDefinition = "DATE")
	private Date fnshDate;

	/** 依本行估價辦法之鑑估值 **/
	@Column(name = "LAWVAL", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal lawVal;

	/**
	 * 擔保品時價購價
	 * <p/>
	 * 新台幣元。不動產房地購價或時價（買賣契約價格）
	 */
	@Column(name = "TIMEVAL", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal timeVal;

	/**
	 * 緩辦案件
	 * <p/>
	 * Y/N
	 */
	@Column(name = "PNDFLAG", length = 1, columnDefinition = "CHAR(01)")
	private String pndFlag;

	/**
	 * 塗銷日期
	 * <p/>
	 * ???
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ERADATE", columnDefinition = "DATE")
	private Date eraDate;

	/**
	 * 目前文件說明
	 * <p/>
	 * 1複製修改中、2複製修改、3重鑑估中、4重鑑估、5重設定中、6重設定、7部份塗銷中、8部份塗銷<br/>
	 * 0部份塗銷重鑑估、1重鑑估、2重設定、3複製修改中、4退回聯行、5回營運中心、6主管退回
	 */
	@Column(name = "DOCDSCR", length = 1, columnDefinition = "CHAR(1)")
	private String docDscr;

	/**
	 * 委託代鑑價日期
	 * <p/>
	 * 代鑑價資料用，若傳送多次僅保留最新一次紀錄
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ENTRDATE", columnDefinition = "DATE")
	private Date entrDate;

	/**
	 * 案件傳回日期
	 * <p/>
	 * 代鑑價資料用，若傳送多次僅保留最新一次紀錄
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ESNDDATE", columnDefinition = "DATE")
	private Date esndDate;

	/**
	 * 代鑑價分行覆核
	 * <p/>
	 * 代鑑價資料用，若傳送多次僅保留最新一次紀錄
	 */
	@Column(name = "ENTRCHECK", length = 6, columnDefinition = "CHAR(6)")
	private String entrCheck;

	/**
	 * 代鑑價分行鑑估人員
	 * <p/>
	 * 代鑑價資料用，若傳送多次僅保留最新一次紀錄
	 */
	@Column(name = "ENTRAPPR", length = 6, columnDefinition = "CHAR(6)")
	private String entrAppr;

	/**
	 * 保證估價報告書種類
	 * <p/>
	 * 100/10/19增加:提供保證時使用. <br/>
	 * 記錄保證種類１.銀行保證、２.政府公庫主管機關保證、３.信用保證機構保證、４.公司保證
	 */
	@Column(name = "DOCKIND", length = 1, columnDefinition = "CHAR(1)")
	private String Dockind;

	/** 鑑估人員編號(行編) **/
	@Column(name = "APPRAISER", length = 6, columnDefinition = "CHAR(6)")
	private String appraiser;

	/** 顯示用欄位 **/
	@Transient
	private String appraiserName;

	public void setAppraiserName(String value) {
		this.appraiserName = value;
	}

	/** 書面審核人員(行編) **/
	@Column(name = "BOSSID", length = 6, columnDefinition = "CHAR(6)")
	private String bossid;

	/**
	 * 單位主管號碼1
	 * <p/>
	 * 記錄單位最高主管. <br/>
	 * 上傳MIS.ELF447用(系統依"鑑估覆核分行"讀取人事檔單位主管)
	 */
	@Column(name = "UNITMGER1", length = 6, columnDefinition = "CHAR(6)")
	private String unitMger1;

	/**
	 * 單位主管號碼2
	 * <p/>
	 * 記錄單位最高主管<br/>
	 * 上傳MIS.ELF447用(系統依"鑑估覆核分行"讀取人事檔單位主管)
	 */
	@Column(name = "UNITMGER2", length = 6, columnDefinition = "CHAR(6)")
	private String unitMger2;

	/**
	 * 建立人員姓名
	 * <p/>
	 * 2011-12-19新增
	 */
	@Column(name = "CREATENAME", length = 48, columnDefinition = "VARCHAR(48)")
	private String createName;

	/**
	 * 異動人員姓名
	 * <p/>
	 * 2011-12-19新增
	 */
	@Column(name = "UPDATENAME", length = 48, columnDefinition = "VARCHAR(48)")
	private String updateName;

	/**
	 * 核准人員姓名
	 * <p/>
	 * 2011-12-19新增
	 */
	@Column(name = "APPROVENAME", length = 48, columnDefinition = "VARCHAR(48)")
	private String approveName;

	/**
	 * 設定經辦ID
	 * <p/>
	 * 2012-02-24新增
	 */
	@Column(name = "SETEMP", length = 6, columnDefinition = "CHAR(6)")
	private String setEmp;

	/**
	 * 設定覆核主管ID
	 * <p/>
	 * 2012-02-24新增
	 */
	@Column(name = "SETBOSS", length = 6, columnDefinition = "CHAR(6)")
	private String setBoss;

	/**
	 * 塗銷經辦ID
	 * <p/>
	 * 2012-02-24新增
	 */
	@Column(name = "ERAEMP", length = 6, columnDefinition = "CHAR(6)")
	private String eraEmp;

	/**
	 * 塗銷覆核主管ID
	 * <p/>
	 * 2012-02-24新增
	 */
	@Column(name = "ERABOSS", length = 6, columnDefinition = "CHAR(6)")
	private String eraBoss;

	/**
	 * 最新已設定鑑估表
	 * <p/>
	 * Y/N
	 */
	@Column(name = "ISJCIC", length = 1, columnDefinition = "CHAR(1)")
	private String isJcic;

	/** 擔保品唯一鍵值 */
	@Column(name = "COLLKEY", length = 32, columnDefinition = "CHAR(32)")
	private String collKey;

	/** 取得鑑價單位代號 **/
	public String getEstBrn() {
		return this.estBrn;
	}

	/** 設定鑑價單位代號 **/
	public void setEstBrn(String value) {
		this.estBrn = value;
	}

	/**
	 * 取得分行代碼
	 * <p/>
	 * 擔保品所屬分行
	 */
	public String getBranch() {
		return this.branch;
	}

	/**
	 * 設定分行代碼
	 * <p/>
	 * 擔保品所屬分行
	 **/
	public void setBranch(String value) {
		this.branch = value;
	}

	/** 取得鑑估日期 **/
	public Date getEstDate() {
		return this.estDate;
	}

	/** 設定鑑估日期 **/
	public void setEstDate(Date value) {
		this.estDate = value;
	}

	/**
	 * 取得OBS文件狀態
	 * <p/>
	 * 1待補登2補登待覆核3補登已覆核<br/>
	 * 4待塗銷5塗銷待覆核6塗銷已覆核
	 */
	public String getObsStatus() {
		return this.obsStatus;
	}

	/**
	 * 設定OBS文件狀態
	 * <p/>
	 * 1待補登2補登待覆核3補登已覆核<br/>
	 * 4待塗銷5塗銷待覆核6塗銷已覆核
	 **/
	public void setObsStatus(String value) {
		this.obsStatus = value;
	}

	/**
	 * 取得借款類別
	 * <p/>
	 * 1.授信、2.進口、3.遠匯、4.出口
	 */
	public String getLnKind() {
		return this.lnKind;
	}

	/**
	 * 設定借款類別
	 * <p/>
	 * 1.授信、2.進口、3.遠匯、4.出口
	 **/
	public void setLnKind(String value) {
		this.lnKind = value;
	}

	/**
	 * 取得擔保品大類
	 * <p/>
	 * 詳擔保品編號
	 */
	public String getCollTyp1() {
		return this.collTyp1;
	}

	/**
	 * 設定擔保品大類
	 * <p/>
	 * 詳擔保品編號
	 **/
	public void setCollTyp1(String value) {
		this.collTyp1 = value;
	}

	/**
	 * 取得擔保品小類
	 * <p/>
	 * 詳擔保品編號
	 */
	public String getCollTyp2() {
		return this.collTyp2;
	}

	/**
	 * 設定擔保品小類
	 * <p/>
	 * 詳擔保品編號
	 **/
	public void setCollTyp2(String value) {
		this.collTyp2 = value;
	}

	/** 取得擔保品編號【註2】 **/
	public String getCollNo() {
		return this.collNo;
	}

	/** 設定擔保品編號【註2】 **/
	public void setCollNo(String value) {
		this.collNo = value;
	}

	/**
	 * 取得擔保品種類
	 * <p/>
	 * 1國內擔保品、2海外擔保品
	 */
	public String getCollKind() {
		return this.collKind;
	}

	/**
	 * 設定擔保品種類
	 * <p/>
	 * 1國內擔保品、2海外擔保品
	 **/
	public void setCollKind(String value) {
		this.collKind = value;
	}

	/**
	 * 取得擔保品設定類別
	 * <p/>
	 * 1自行保管2聯行委託代保管
	 */
	public String getSetType() {
		return this.setType;
	}

	/**
	 * 設定擔保品設定類別
	 * <p/>
	 * 1自行保管2聯行委託代保管
	 **/
	public void setSetType(String value) {
		this.setType = value;
	}

	/** 取得委託分行代碼 **/
	public String getSetBranch() {
		return this.setBranch;
	}

	/** 設定委託分行代碼 **/
	public void setSetBranch(String value) {
		this.setBranch = value;
	}

	/**
	 * 取得是否為買賣契約
	 * <p/>
	 * Y/N，買賣件
	 */
	public String getCtrFlag() {
		return this.ctrFlag;
	}

	/**
	 * 設定是否為買賣契約
	 * <p/>
	 * Y/N，買賣件
	 **/
	public void setCtrFlag(String value) {
		this.ctrFlag = value;
	}

	/** 取得表單種類【註3】 **/
	public String getRptType() {
		return this.rptType;
	}

	/** 設定表單種類【註3】 **/
	public void setRptType(String value) {
		this.rptType = value;
	}

	/** 取得電子謄本申請編號 **/
	public String getBioid() {
		return this.bioid;
	}

	/** 設定電子謄本申請編號 **/
	public void setBioid(String value) {
		this.bioid = value;
	}

	/** 取得電子謄本申請日期 **/
	public Date getElDate() {
		return this.elDate;
	}

	/** 設定電子謄本申請日期 **/
	public void setElDate(Date value) {
		this.elDate = value;
	}

	/**
	 * 取得中長期案件
	 * <p/>
	 * 1是2否
	 */
	public String getLongCase() {
		return this.longCase;
	}

	/**
	 * 設定中長期案件
	 * <p/>
	 * 1是2否
	 **/
	public void setLongCase(String value) {
		this.longCase = value;
	}

	/**
	 * 取得寬限期/預提折舊-年
	 * <p/>
	 * 不動產中長期：寬限期<br/>
	 * 不動產非中長期：預提折舊
	 */
	public Integer getExtYear() {
		return this.extYear;
	}

	/**
	 * 設定寬限期/預提折舊-年
	 * <p/>
	 * 不動產中長期：寬限期<br/>
	 * 不動產非中長期：預提折舊
	 **/
	public void setExtYear(Integer value) {
		this.extYear = value;
	}

	/**
	 * 取得寬限期/預提折舊-月
	 * <p/>
	 * 同上
	 */
	public Integer getExtMonth() {
		return this.extMonth;
	}

	/**
	 * 設定寬限期/預提折舊-月
	 * <p/>
	 * 同上
	 **/
	public void setExtMonth(Integer value) {
		this.extMonth = value;
	}

	/**
	 * 取得鑑價方式【註4】
	 * <p/>
	 * 不動產
	 */
	public String getTaxType() {
		return this.taxType;
	}

	/**
	 * 設定鑑價方式【註4】
	 * <p/>
	 * 不動產
	 **/
	public void setTaxType(String value) {
		this.taxType = value;
	}

	/**
	 * 取得面積單位
	 * <p/>
	 * 計量單位。<br/>
	 * 1平方公尺、2平方公里、3坪、4公頃、5公畝、6英畝<br/>
	 * 國內擔保品預設平方公尺
	 */
	public String getEstUnit() {
		return this.estUnit;
	}

	/**
	 * 設定面積單位
	 * <p/>
	 * 計量單位。<br/>
	 * 1平方公尺、2平方公里、3坪、4公頃、5公畝、6英畝<br/>
	 * 國內擔保品預設平方公尺
	 **/
	public void setEstUnit(String value) {
		this.estUnit = value;
	}

	/**
	 * 取得國別
	 * <p/>
	 * 國內擔保品預設TW
	 */
	public String getNatCode() {
		return this.natCode;
	}

	/**
	 * 設定國別
	 * <p/>
	 * 國內擔保品預設TW
	 **/
	public void setNatCode(String value) {
		this.natCode = value;
	}

	/**
	 * 取得幣別
	 * <p/>
	 * 國內擔保品預設TWD
	 */
	public String getCurrCd() {
		return this.currCd;
	}

	/**
	 * 設定幣別
	 * <p/>
	 * 國內擔保品預設TWD
	 **/
	public void setCurrCd(String value) {
		this.currCd = value;
	}

	/** 取得折合台幣匯率 **/
	public BigDecimal getTwdRate() {
		return this.twdRate;
	}

	/** 設定折合台幣匯率 **/
	public void setTwdRate(BigDecimal value) {
		this.twdRate = value;
	}

	/** 取得折合美元匯率 **/
	public BigDecimal getUsdRate() {
		return this.usdRate;
	}

	/** 設定折合美元匯率 **/
	public void setUsdRate(BigDecimal value) {
		this.usdRate = value;
	}

	/**
	 * 取得鑑估金額（原幣）
	 * <p/>
	 * 元
	 */
	public BigDecimal getAppAmt() {
		return this.appAmt;
	}

	/**
	 * 設定鑑估金額（原幣）
	 * <p/>
	 * 元
	 **/
	public void setAppAmt(BigDecimal value) {
		this.appAmt = value;
	}

	/**
	 * 取得鑑估金額(TWD)
	 * <p/>
	 * 鑑估金額折合美元
	 */
	public BigDecimal getTwdAmt() {
		return this.twdAmt;
	}

	/**
	 * 設定鑑估金額(TWD)
	 * <p/>
	 * 鑑估金額折合美元
	 **/
	public void setTwdAmt(BigDecimal value) {
		this.twdAmt = value;
	}

	/**
	 * 取得鑑估金額(USD)
	 * <p/>
	 * 鑑估金額折合美元
	 */
	public BigDecimal getUsdAmt() {
		return this.usdAmt;
	}

	/**
	 * 設定鑑估金額(USD)
	 * <p/>
	 * 鑑估金額折合美元
	 **/
	public void setUsdAmt(BigDecimal value) {
		this.usdAmt = value;
	}

	/** 取得貸放成數/平均貸放成數 **/
	public Integer getLoanRt() {
		return this.loanRt;
	}

	/** 設定貸放成數/平均貸放成數 **/
	public void setLoanRt(Integer value) {
		this.loanRt = value;
	}

	/**
	 * 取得放款值(原幣)
	 * <p/>
	 * 元
	 */
	public BigDecimal getLoanAmt() {
		return this.loanAmt;
	}

	/**
	 * 設定放款值(原幣)
	 * <p/>
	 * 元
	 **/
	public void setLoanAmt(BigDecimal value) {
		this.loanAmt = value;
	}

	/**
	 * 取得放款值（TWD）
	 * <p/>
	 * 放款值折合台幣
	 */
	public BigDecimal getLoanTwd() {
		return this.loanTwd;
	}

	/**
	 * 設定放款值（TWD）
	 * <p/>
	 * 放款值折合台幣
	 **/
	public void setLoanTwd(BigDecimal value) {
		this.loanTwd = value;
	}

	/**
	 * 取得放款值（USD）
	 * <p/>
	 * 放款值折合美元
	 */
	public BigDecimal getLoanUsd() {
		return this.loanUsd;
	}

	/**
	 * 設定放款值（USD）
	 * <p/>
	 * 放款值折合美元
	 **/
	public void setLoanUsd(BigDecimal value) {
		this.loanUsd = value;
	}

	/** 取得聯貸幣別 **/
	public String getJoinCd() {
		return this.joinCd;
	}

	/** 設定聯貸幣別 **/
	public void setJoinCd(String value) {
		this.joinCd = value;
	}

	/** 取得聯貸幣別折合美元匯率 **/
	public BigDecimal getJoinRate() {
		return this.joinRate;
	}

	/** 設定聯貸幣別折合美元匯率 **/
	public void setJoinRate(BigDecimal value) {
		this.joinRate = value;
	}

	/** 取得聯貸總金額(原幣) **/
	public BigDecimal getMoneyTo() {
		return this.moneyTo;
	}

	/** 設定聯貸總金額(原幣) **/
	public void setMoneyTo(BigDecimal value) {
		this.moneyTo = value;
	}

	/** 取得聯貸總金額(台幣) **/
	public BigDecimal getMoneyToTWD() {
		return this.moneyToTWD;
	}

	/** 設定聯貸總金額(台幣) **/
	public void setMoneyToTWD(BigDecimal value) {
		this.moneyToTWD = value;
	}

	/** 取得聯貸總金額(美元) **/
	public BigDecimal getMoneyToUSD() {
		return this.moneyToUSD;
	}

	/** 設定聯貸總金額(美元) **/
	public void setMoneyToUSD(BigDecimal value) {
		this.moneyToUSD = value;
	}

	/** 取得參貸金額(原幣) **/
	public BigDecimal getJoinAmt() {
		return this.joinAmt;
	}

	/** 設定參貸金額(原幣) **/
	public void setJoinAmt(BigDecimal value) {
		this.joinAmt = value;
	}

	/** 取得參貸金額(台幣) **/
	public BigDecimal getJoinAmtTWD() {
		return this.joinAmtTWD;
	}

	/** 設定參貸金額(台幣) **/
	public void setJoinAmtTWD(BigDecimal value) {
		this.joinAmtTWD = value;
	}

	/** 取得參貸金額(美元) **/
	public BigDecimal getJoinAmtUSD() {
		return this.joinAmtUSD;
	}

	/** 設定參貸金額(美元) **/
	public void setJoinAmtUSD(BigDecimal value) {
		this.joinAmtUSD = value;
	}

	/** 取得非本行他項權利金額合計 **/
	public BigDecimal getOthAmt() {
		return this.othAmt;
	}

	/** 設定非本行他項權利金額合計 **/
	public void setOthAmt(BigDecimal value) {
		this.othAmt = value;
	}

	/** 取得放款值（扣除非本行他項權利金額合計） **/
	public BigDecimal getMegaAmt() {
		return this.megaAmt;
	}

	/** 設定放款值（扣除非本行他項權利金額合計） **/
	public void setMegaAmt(BigDecimal value) {
		this.megaAmt = value;
	}

	/**
	 * 取得主辦行代碼
	 * <p/>
	 * 參貸他行使用
	 */
	public String getHstBkCd() {
		return this.hstBkCd;
	}

	/**
	 * 設定主辦行代碼
	 * <p/>
	 * 參貸他行使用
	 **/
	public void setHstBkCd(String value) {
		this.hstBkCd = value;
	}

	/**
	 * 取得主辦行名稱
	 * <p/>
	 * 參貸他行使用
	 */
	public String getHstBkNm() {
		return this.hstBkNm;
	}

	/**
	 * 設定主辦行名稱
	 * <p/>
	 * 參貸他行使用
	 **/
	public void setHstBkNm(String value) {
		this.hstBkNm = value;
	}

	/** 取得前順位金額合計 **/
	public BigDecimal getPriAmt() {
		return this.priAmt;
	}

	/** 設定前順位金額合計 **/
	public void setPriAmt(BigDecimal value) {
		this.priAmt = value;
	}

	/**
	 * 取得是否為聯貸案件
	 * <p/>
	 * Y/N
	 */
	public String getIsSLoan() {
		return this.isSLoan;
	}

	/**
	 * 設定是否為聯貸案件
	 * <p/>
	 * Y/N
	 **/
	public void setIsSLoan(String value) {
		this.isSLoan = value;
	}

	/** 取得買賣契約日期 **/
	public Date getCtrDate() {
		return this.ctrDate;
	}

	/** 設定買賣契約日期 **/
	public void setCtrDate(Date value) {
		this.ctrDate = value;
	}

	/**
	 * 取得土地明細筆數
	 * <p/>
	 * 不動產以外為明細資料筆數
	 */
	public Integer getLndNum() {
		return this.lndNum;
	}

	/**
	 * 設定土地明細筆數
	 * <p/>
	 * 不動產以外為明細資料筆數
	 **/
	public void setLndNum(Integer value) {
		this.lndNum = value;
	}

	/**
	 * 取得建物明細筆數
	 * <p/>
	 * 不動產以外為0（暫保留未用）
	 */
	public Integer getBldNum() {
		return this.bldNum;
	}

	/**
	 * 設定建物明細筆數
	 * <p/>
	 * 不動產以外為0（暫保留未用）
	 **/
	public void setBldNum(Integer value) {
		this.bldNum = value;
	}

	/** 取得租賃用益減值 **/
	public BigDecimal getRentAmt() {
		return this.rentAmt;
	}

	/** 設定租賃用益減值 **/
	public void setRentAmt(BigDecimal value) {
		this.rentAmt = value;
	}

	/**
	 * 取得異動原因
	 * <p/>
	 * （股票）S新增、R補提
	 */
	public String getTxCd() {
		return this.txCd;
	}

	/**
	 * 設定異動原因
	 * <p/>
	 * （股票）S新增、R補提
	 **/
	public void setTxCd(String value) {
		this.txCd = value;
	}

	/**
	 * 取得異動日期
	 * <p/>
	 * （股票）
	 */
	public Date getTxDate() {
		return this.txDate;
	}

	/**
	 * 設定異動日期
	 * <p/>
	 * （股票）
	 **/
	public void setTxDate(Date value) {
		this.txDate = value;
	}

	/**
	 * 取得輸入資料檢誤完成
	 * <p/>
	 * Y/N，鑑價輸入資料檢誤
	 */
	public String getChkYn() {
		return this.chkYn;
	}

	/**
	 * 設定輸入資料檢誤完成
	 * <p/>
	 * Y/N，鑑價輸入資料檢誤
	 **/
	public void setChkYn(String value) {
		this.chkYn = value;
	}

	/**
	 * 取得擔保品有效期限
	 * <p/>
	 * Collmstr現有欄位，未使用，先保留給Null值。（上傳misdb給空白）
	 */
	public Date getValiDate() {
		return this.valiDate;
	}

	/**
	 * 設定擔保品有效期限
	 * <p/>
	 * Collmstr現有欄位，未使用，先保留給Null值。（上傳misdb給空白）
	 **/
	public void setValiDate(Date value) {
		this.valiDate = value;
	}

	/**
	 * 取得折舊止日
	 * <p/>
	 * 動產用
	 */
	public Date getFnshDate() {
		return this.fnshDate;
	}

	/**
	 * 設定折舊止日
	 * <p/>
	 * 動產用
	 **/
	public void setFnshDate(Date value) {
		this.fnshDate = value;
	}

	/** 取得依本行估價辦法之鑑估值 **/
	public BigDecimal getLawVal() {
		return this.lawVal;
	}

	/** 設定依本行估價辦法之鑑估值 **/
	public void setLawVal(BigDecimal value) {
		this.lawVal = value;
	}

	/**
	 * 取得擔保品時價購價
	 * <p/>
	 * 新台幣元。不動產房地購價或時價（買賣契約價格）
	 */
	public BigDecimal getTimeVal() {
		return this.timeVal;
	}

	/**
	 * 設定擔保品時價購價
	 * <p/>
	 * 新台幣元。不動產房地購價或時價（買賣契約價格）
	 **/
	public void setTimeVal(BigDecimal value) {
		this.timeVal = value;
	}

	/**
	 * 取得緩辦案件
	 * <p/>
	 * Y/N
	 */
	public String getPndFlag() {
		return this.pndFlag;
	}

	/**
	 * 設定緩辦案件
	 * <p/>
	 * Y/N
	 **/
	public void setPndFlag(String value) {
		this.pndFlag = value;
	}

	/**
	 * 取得塗銷日期
	 * <p/>
	 * ???
	 */
	public Date getEraDate() {
		return this.eraDate;
	}

	/**
	 * 設定塗銷日期
	 * <p/>
	 * ???
	 **/
	public void setEraDate(Date value) {
		this.eraDate = value;
	}

	/**
	 * 取得目前文件說明
	 * <p/>
	 * 1複製修改中、2複製修改、3重鑑估中、4重鑑估、5重設定中、6重設定、7部份塗銷中、8部份塗銷<br/>
	 * 0部份塗銷重鑑估、1重鑑估、2重設定、3複製修改中、4退回聯行、5回營運中心、6主管退回
	 */
	public String getDocDscr() {
		return this.docDscr;
	}

	/**
	 * 設定目前文件說明
	 * <p/>
	 * 1複製修改中、2複製修改、3重鑑估中、4重鑑估、5重設定中、6重設定、7部份塗銷中、8部份塗銷<br/>
	 * 0部份塗銷重鑑估、1重鑑估、2重設定、3複製修改中、4退回聯行、5回營運中心、6主管退回
	 **/
	public void setDocDscr(String value) {
		this.docDscr = value;
	}

	/**
	 * 取得委託代鑑價日期
	 * <p/>
	 * 代鑑價資料用，若傳送多次僅保留最新一次紀錄
	 */
	public Date getEntrDate() {
		return this.entrDate;
	}

	/**
	 * 設定委託代鑑價日期
	 * <p/>
	 * 代鑑價資料用，若傳送多次僅保留最新一次紀錄
	 **/
	public void setEntrDate(Date value) {
		this.entrDate = value;
	}

	/**
	 * 取得案件傳回日期
	 * <p/>
	 * 代鑑價資料用，若傳送多次僅保留最新一次紀錄
	 */
	public Date getEsndDate() {
		return this.esndDate;
	}

	/**
	 * 設定案件傳回日期
	 * <p/>
	 * 代鑑價資料用，若傳送多次僅保留最新一次紀錄
	 **/
	public void setEsndDate(Date value) {
		this.esndDate = value;
	}

	/**
	 * 取得代鑑價分行覆核
	 * <p/>
	 * 代鑑價資料用，若傳送多次僅保留最新一次紀錄
	 */
	public String getEntrCheck() {
		return this.entrCheck;
	}

	/**
	 * 設定代鑑價分行覆核
	 * <p/>
	 * 代鑑價資料用，若傳送多次僅保留最新一次紀錄
	 **/
	public void setEntrCheck(String value) {
		this.entrCheck = value;
	}

	/**
	 * 取得代鑑價分行鑑估人員
	 * <p/>
	 * 代鑑價資料用，若傳送多次僅保留最新一次紀錄
	 */
	public String getEntrAppr() {
		return this.entrAppr;
	}

	/**
	 * 設定代鑑價分行鑑估人員
	 * <p/>
	 * 代鑑價資料用，若傳送多次僅保留最新一次紀錄
	 **/
	public void setEntrAppr(String value) {
		this.entrAppr = value;
	}

	/**
	 * 取得保證估價報告書種類
	 * <p/>
	 * 100/10/19增加:提供保證時使用. <br/>
	 * 記錄保證種類１.銀行保證、２.政府公庫主管機關保證、３.信用保證機構保證、４.公司保證
	 */
	public String getDockind() {
		return this.Dockind;
	}

	/**
	 * 設定保證估價報告書種類
	 * <p/>
	 * 100/10/19增加:提供保證時使用. <br/>
	 * 記錄保證種類１.銀行保證、２.政府公庫主管機關保證、３.信用保證機構保證、４.公司保證
	 **/
	public void setDockind(String value) {
		this.Dockind = value;
	}

	/** 取得鑑估人員編號(行編) **/
	public String getAppraiser() {
		return this.appraiser;
	}

	/** 設定鑑估人員編號(行編) **/
	public void setAppraiser(String value) {
		this.appraiser = value;
	}

	/** 取得書面審核人員(行編) **/
	public String getBossid() {
		return this.bossid;
	}

	/** 設定書面審核人員(行編) **/
	public void setBossid(String value) {
		this.bossid = value;
	}

	/**
	 * 取得單位主管號碼1
	 * <p/>
	 * 記錄單位最高主管. <br/>
	 * 上傳MIS.ELF447用(系統依"鑑估覆核分行"讀取人事檔單位主管)
	 */
	public String getUnitMger1() {
		return this.unitMger1;
	}

	/**
	 * 設定單位主管號碼1
	 * <p/>
	 * 記錄單位最高主管. <br/>
	 * 上傳MIS.ELF447用(系統依"鑑估覆核分行"讀取人事檔單位主管)
	 **/
	public void setUnitMger1(String value) {
		this.unitMger1 = value;
	}

	/**
	 * 取得單位主管號碼2
	 * <p/>
	 * 記錄單位最高主管<br/>
	 * 上傳MIS.ELF447用(系統依"鑑估覆核分行"讀取人事檔單位主管)
	 */
	public String getUnitMger2() {
		return this.unitMger2;
	}

	/**
	 * 設定單位主管號碼2
	 * <p/>
	 * 記錄單位最高主管<br/>
	 * 上傳MIS.ELF447用(系統依"鑑估覆核分行"讀取人事檔單位主管)
	 **/
	public void setUnitMger2(String value) {
		this.unitMger2 = value;
	}

	/**
	 * 取得建立人員姓名
	 * <p/>
	 * 2011-12-19新增
	 */
	public String getCreateName() {
		return this.createName;
	}

	/**
	 * 設定建立人員姓名
	 * <p/>
	 * 2011-12-19新增
	 **/
	public void setCreateName(String value) {
		this.createName = value;
	}

	/**
	 * 取得異動人員姓名
	 * <p/>
	 * 2011-12-19新增
	 */
	public String getUpdateName() {
		return this.updateName;
	}

	/**
	 * 設定異動人員姓名
	 * <p/>
	 * 2011-12-19新增
	 **/
	public void setUpdateName(String value) {
		this.updateName = value;
	}

	/**
	 * 取得核准人員姓名
	 * <p/>
	 * 2011-12-19新增
	 */
	public String getApproveName() {
		return this.approveName;
	}

	/**
	 * 設定核准人員姓名
	 * <p/>
	 * 2011-12-19新增
	 **/
	public void setApproveName(String value) {
		this.approveName = value;
	}

	/**
	 * 取得設定經辦ID
	 * <p/>
	 * 2012-02-24新增
	 */
	public String getSetEmp() {
		return this.setEmp;
	}

	/**
	 * 設定設定經辦ID
	 * <p/>
	 * 2012-02-24新增
	 **/
	public void setSetEmp(String value) {
		this.setEmp = value;
	}

	/**
	 * 取得設定覆核主管ID
	 * <p/>
	 * 2012-02-24新增
	 */
	public String getSetBoss() {
		return this.setBoss;
	}

	/**
	 * 設定設定覆核主管ID
	 * <p/>
	 * 2012-02-24新增
	 **/
	public void setSetBoss(String value) {
		this.setBoss = value;
	}

	/**
	 * 取得塗銷經辦ID
	 * <p/>
	 * 2012-02-24新增
	 */
	public String getEraEmp() {
		return this.eraEmp;
	}

	/**
	 * 設定塗銷經辦ID
	 * <p/>
	 * 2012-02-24新增
	 **/
	public void setEraEmp(String value) {
		this.eraEmp = value;
	}

	/**
	 * 取得塗銷覆核主管ID
	 * <p/>
	 * 2012-02-24新增
	 */
	public String getEraBoss() {
		return this.eraBoss;
	}

	/**
	 * 設定塗銷覆核主管ID
	 * <p/>
	 * 2012-02-24新增
	 **/
	public void setEraBoss(String value) {
		this.eraBoss = value;
	}

	/**
	 * 取得最新已設定鑑估表
	 * <p/>
	 * Y/N
	 */
	public String getIsJcic() {
		return this.isJcic;
	}

	/**
	 * 設定最新已設定鑑估表
	 * <p/>
	 * Y/N
	 **/
	public void setIsJcic(String value) {
		this.isJcic = value;
	}

	/**
	 * 是否為歷史檔
	 */
	@Transient
	private String isHis = "N";

	public String getIsHis() {
		return isHis;
	}

	public void setIsHis(String isHis) {
		this.isHis = isHis;
	}

	/**
	 * 擔保品種類
	 */
	@Transient
	private String collKindHide = "";

	public String getCollKindHide() {
		return collKindHide;
	}

	public void setCollKindHide(String collKindHide) {
		this.collKindHide = collKindHide;
	}

	/**
	 * 擔保品小類
	 */
	@Transient
	private String collTTyp2Hide = "";

	public String getCollTyp2Hide() {
		return collTTyp2Hide;
	}

	public void setCollTyp2Hide(String collTTyp2Hide) {
		this.collTTyp2Hide = collTTyp2Hide;
	}

	public String getCollKey() {
		return collKey;
	}

	public void setCollKey(String collKey) {
		this.collKey = collKey;
	}

}
