/* 
 * C126M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C126M01A;

/** 房仲引介資料檔 **/
public interface C126M01ADao extends IGenericDao<C126M01A> {

	C126M01A findByOid(String oid);
	List<C126M01A> findByMainId(String mainId);
	C126M01A findUniqueBy(String custId, String dupNo, String ownBrid, String agntNo, String agntChain, String contractNo);
	List<C126M01A> findByCustIdAndDupNo(String custId, String dupNo);
}