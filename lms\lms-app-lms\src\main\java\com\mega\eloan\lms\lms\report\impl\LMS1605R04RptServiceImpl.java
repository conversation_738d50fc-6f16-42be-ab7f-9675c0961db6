/* 
 *LMS1605R04RptServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.lms.service.LMS1605Service;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L163S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * 先行動用待辦事項控制表
 * </pre>
 * 
 * @since 2012/2/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/9,REX,new
 *          </ul>
 */
@Service("lms1605r04rptservice")
public class LMS1605R04RptServiceImpl extends AbstractReportService {

	@Resource
	LMS1605Service lms1605Service;

	@Resource
	BranchService branch;

	@Resource
	CodeTypeService codeTypeService;

	private static ThreadLocal<DecimalFormat> dfMoney = new ThreadLocal<DecimalFormat>();
	private static ThreadLocal<DecimalFormat> dfRate = new ThreadLocal<DecimalFormat>();

	@Override
	public String getReportTemplateFileName() {
		Locale locale = null;
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		return "report/lms/LMS1605R04_" + locale.toString() + ".rpt";
		// return "D:/LMS1605R04_zh_TW.rpt";
	}

	/*
	 * (non-Javadoc) 設定需要傳入RPT參數
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.AbstractReportService#setReportData(com
	 * .mega.eloan.lms.base.report.ReportGenerator,
	 * org.apache.wicket.PageParameters)
	 */
	@Override
	public void setReportData(ReportGenerator reportTools, PageParameters params) {

		Locale locale = null;
		try {

			locale = LocaleContextHolder.getLocale();
			if (locale == null) {
				locale = Locale.getDefault();
			}
			dfMoney.set(new DecimalFormat("#,###,###,###,##0"));
			dfRate.set(new DecimalFormat("#,###,###,###,##0.00"));

			Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
			List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			rptVariableMap.put("queryDate",
					CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM));
			String[] docStatusArray = new String[] {
					CreditDocStatusEnum.海外_已核准.getCode(),
					CreditDocStatusEnum.先行動用_已覆核.getCode() };
			List<L160M01A> l160m01as = lms1605Service.findFirstUse(
					docStatusArray, user.getUnitNo());

			titleRows = this.setL160M01ADataList(titleRows, locale, l160m01as);
			reportTools.setLang(locale);
			reportTools.setVariableData(rptVariableMap);
			reportTools.setRowsData(titleRows);
			// reportTools.setTestMethod(true);
		}finally{
			
		}
	}

	/**
	 * 設定L160M01A資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L160M01A List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setL160M01ADataList(
			List<Map<String, String>> titleRows, Locale locale,
			List<L160M01A> list) {
		// F代表第一次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		Map<String, String> mapInTitleRows = null;
		int count = 1;
		L163S01A l163s01a = null;
		StringBuffer temp = new StringBuffer();
		for (L160M01A l160m01a : list) {
			l163s01a = l160m01a.getL163S01A();
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column01", String.valueOf(count));

			mapInTitleRows.put("ReportBean.column02",
					Util.getDate(l160m01a.getApproveTime()));

			mapInTitleRows.put("ReportBean.column03",
					Util.getDate(l163s01a.getWillFinishDate()));
			mapInTitleRows.put("ReportBean.column04", LMSUtil.concat(temp,
					Util.nullToSpace(l160m01a.getCustId()), " ",
					Util.nullToSpace(l160m01a.getDupNo())));
			mapInTitleRows.put("ReportBean.column05",
					Util.nullToSpace(l160m01a.getCustName()));
			mapInTitleRows.put("ReportBean.column06",
					Util.nullToSpace(l163s01a.getWaitingItem()));

			count++;
			titleRows.add(mapInTitleRows);
		}
		return titleRows;
	}

	@Override
	public OutputStream generateReport(PageParameters params)
			throws CapException {

		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		ReportGenerator rptGenerator = new ReportGenerator(
				this.getReportTemplateFileName());
		this.setReportData(rptGenerator, params);
		OutputStream outputStream = null;
		int subLine = 8;
		Locale locale = null;
		Properties propEloanPage = null;
		try {
			propEloanPage = MessageBundleScriptCreator
					.getComponentResource(AbstractEloanPage.class);
			locale = LMSUtil.getLocale();
			outputStream = rptGenerator.generateReport();
			// 有詳細資料
			pdfNameMap.put(new ByteArrayInputStream(
					((ByteArrayOutputStream) outputStream).toByteArray()),
					subLine);
			if (pdfNameMap != null && pdfNameMap.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						locale, subLine);
			}
		} catch (Exception ex) {
			throw new CapException(ex.getCause(), ex.getClass());
		} finally {
			if (outputStream != null) {
				try {
					outputStream.close();
				} catch (IOException ex) {
					LOGGER.error("[generateReport]close() Exception!!", ex);
				}
			}
		}
		return outputStream;
	}

}
