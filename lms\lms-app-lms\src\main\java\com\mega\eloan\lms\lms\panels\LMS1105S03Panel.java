/* 
 * LMS1205S03Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**<pre>
 * 額度明細表(企金授權內)
 * </pre>
 * @since  2012/1/19
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class LMS1105S03Panel extends Panel {

	public LMS1105S03Panel(String id) {
		super(id);		
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		new LMS1405S01Panel("lms1405s01_panel").processPanelData(model, params);
		new LMS1405S02Panel("lms1405s02_panel").processPanelData(model, params);
	}

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

}
