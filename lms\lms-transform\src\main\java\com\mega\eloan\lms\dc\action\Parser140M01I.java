package com.mega.eloan.lms.dc.action;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.bean.L140M01IBean;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * Parser140M01I
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          </ul>
 */
public class Parser140M01I extends AbstractLMSCustParser {
	private static final boolean BYPASS_PRECHECK = false;

	// UFO@2013-01-21
	public static final int MAX_LOOP = 17;// 最大序號值(每一筆DXL應最多產生17筆資料)

	private static List<String[]> KEY_LIST = new ArrayList<String[]>();
	private static final String[] KEYS_1 = { "np_guarantor", "np_gt_Name",
			"tLngere_", "ntcode_", };// 2013-01-23:規則變更去掉tLngere_type_
	private static final String[] KEYS_2 = { "lp_guarantor", "lp_gt_Name",
			"tLngere_", "ntcode_", };// 2013-01-23:np_gt_Name修正為lp_gt_Name,規則變更同上

	static {
		initKeyString();
	}

	/**
	 * @param pid
	 * @param doViewName
	 * @param formGroup
	 */
	public Parser140M01I(String pid, String doViewName, String formGroup) {
		super(pid, doViewName, formGroup);
	}

	/**
	 * 讀取,處理及轉換
	 * 
	 * @param dxlPath
	 *            String : .dxl檔存放路徑
	 * @param dxlName
	 *            :.dxl列表中的.dxl檔名
	 * @param strBrn
	 *            String:分行名稱
	 * @param domDoc
	 *            DOM Document:已轉為DOM Document的.dxl檔
	 * @param dxlXml
	 *            String : 己轉為String型態的.dxl檔 2013-01-24變更
	 *            np_guarantor(lp_guarantor
	 *            )若為空值則setrId("00000000");setrDupNo("");
	 */
	@SuppressWarnings("unused")
	protected void transferDXL(String dxlPath, String dxlName, String strBrn,
			Document domDoc, String dxlXml) {
		long t1 = System.currentTimeMillis();
		try {
			String[] k1 = dxlName.split(TextDefine.ATTACH_DXL);// EX:{FLMS120M01_2E3761E1BB971A2B48257A7D00143D9C,.dxl}
			String[] k2 = k1[0].split("_");// EX:{FLMS120M01,2E3761E1BB971A2B48257A7D00143D9C}
			String tmpMainId = "";
			if (k2.length == 2) {
				tmpMainId = k2[1];// 主檔
			} else {
				tmpMainId = k2[2];// 明細檔之UNID
			}

			// 20130417
			// Sandra若CNTRDOCID有值，以CNTRDOCID為140開頭所有的table的mainid；若無值，則取unid為mainid
			// 20130419 Sandra因CNTRDOCID仍會有重覆，建霖mail通知調整使用WEBELOANMAINID
			String cntrDocId = getItemValue(domDoc, "WEBELOANMAINID");
			tmpMainId = cntrDocId.isEmpty() ? tmpMainId : cntrDocId;

			for (int i = 1; i <= MAX_LOOP; i++) {
				if (!BYPASS_PRECHECK && isKeyEmpty(dxlXml, i)) {
					continue;
				}

				if (DEBUG && logger.isDebugEnabled()) {
					logger.debug("@@@@@@@@ DO=>" + dxlName + " :: " + i);
				}

				L140M01IBean L140i = new L140M01IBean();
				L140i.setOid(Util.getOID());
				L140i.setMainId(tmpMainId);
				if (i <= 12) {
					L140i.setType("1");
					// 註：db2.rId均取notes值的第1碼至倒數第2碼；db2.rDupNo均取notes值的最後1碼
					String ridDup = getItemValue(domDoc, "np_guarantor" + i);
					// 連保人統編
					String rid = this.getRid(ridDup);
					L140i.setrId(rid);
					// 連保人統編重覆碼
					String dup = this.getDup(ridDup, i);
					L140i.setrDupNo(dup);
					// 連保人名稱
					String rName = getItemValue(domDoc, "np_gt_Name" + i);
					L140i.setrName(rName);
					// 關係類別及關係類別細項 2013-01-23:規則變更
					this.setRKindMD(domDoc, L140i, i);
					// 國別
					String rCountry = getItemValue(domDoc, "ntcode_" + i);
					L140i.setrCountry(rCountry);
				} else {
					L140i.setType("2");
					// 註：db2.rId均取notes值的第1碼至倒數第2碼；db2.rDupNo均取notes值的最後1碼
					String lpRidDup = getItemValue(domDoc, "lp_guarantor"
							+ (i - 12));// 再從 1開始
					// 連保人統編
					String rid = this.getRid(lpRidDup);
					L140i.setrId(rid);
					// 連保人統編重覆碼
					String dup = this.getDup(lpRidDup, i);
					L140i.setrDupNo(dup);
					// 連保人名稱
					String rName = getItemValue(domDoc, "lp_gt_Name" + (i - 12));// 再從1開始
					L140i.setrName(rName);
					// 關係類別及關係類別細項 //直接接續13.14.15.16.17 , 2013-01-23:規則變更
					this.setRKindMD(domDoc, L140i, i);
					// 國別
					String rCountry = getItemValue(domDoc, "ntcode_" + i);// 直接接續13.14.15.16.17
					L140i.setrCountry(rCountry);
				}
				// 當rId、rName、rKindM、rKindD、rCountry欄位均為空白時不需產生資料
				String rid = L140i.getrId();
				// String rDupNo = L140i.getrDupNo(); 2013/03/18
				String rName = L140i.getrName();
				String rKindM = L140i.getrKindM();
				String rKindD = L140i.getrKindD();
				String rCountry = L140i.getrCountry();
				// 2013.04.08 modify by Vance 統編與戶名有一欄非空白，則該筆資料轉入
				if (!Util.isEmptyWithTrim(rid) || !Util.isEmptyWithTrim(rName)) {
					this.txtWrite.println(L140i.toString());
					this.parserTotal++;
				}
			}
		} catch (Exception e) {
			String errmsg = "【" + strBrn
					+ "】分行執行Parser140M01I 之transferDXL時產生錯誤,dxl檔名:" + dxlName
					+ ",dxlPath=" + dxlPath;
			throw new DCException(errmsg, e);
		} finally {
			if (DEBUG && logger.isDebugEnabled()) {
				logger.debug("@@@@@@@@ TOTAL_COST="
						+ (System.currentTimeMillis() - t1));
			}
		}
	}

	/**
	 * 取得連保人統編
	 * 
	 * @param ridDup
	 *            String :(np_guarantor+序號)欄位之值
	 * @return 連保人統編 2013-02-20 Rid為空，則一律為寫入"        "八個空白
	 */
	private String getRid(String ridDup) {
		if (StringUtils.isNotBlank(ridDup)) {
			String tmp = ridDup.substring(0, ridDup.length() - 1);
			// 2013-01-25 若長度大於10，僅取前10碼的值寫入
			if (tmp.length() > 10) {
				return tmp.substring(0, 10);
			} else {
				return tmp;
			}
		} else {
			return "        ";
		}
	}

	/**
	 * 取得連保人統編重覆碼
	 * 
	 * @param ridDup
	 *            String :(np_guarantor+序號)欄位之值
	 * @param idx
	 *            :當前序號值
	 * @return 連保人統編重覆碼 取notes值的最後1碼 2013-01-25 若字元長度大於1，則一律寫入0 2013-02-20
	 *         ridDup為空則塞入當前序號值對應的英文字母 Ex:若該案共有8個連保人，第5筆的身分證字號為空，
	 *         則第5筆的身分證字號rId寫入"        "rDupNo寫入"E"
	 */
	@SuppressWarnings("unused")
	private String getDup(String ridDup, int idx) {
		if (StringUtils.isNotBlank(ridDup)) {
			String tmp = ridDup.substring(ridDup.length() - 1);
			// 該欄位為char(1)，避掉中文字
			char[] chars = tmp.toCharArray();
			for (int i = 0; i < chars.length; i++) {
				if ((int) chars[i] < '\200') {
					return tmp;
				} else {
					return "0";
				}
			}
		} else {
			String[] eng = new String[] { "A", "B", "C", "D", "E", "F", "G",
					"H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S",
					"T", "U", "V", "W", "X", "Y", "Z" };
			return eng[idx - 1];
		}
		return ridDup;
	}

	/**
	 * 設定 rKindM及rKindD之值
	 * 
	 * @param domDoc
	 *            DOM Document:已轉為DOM Document的.dxl檔
	 * @param L140i
	 *            L140M01IBean
	 * @param idx
	 *            目前執行的序號
	 * @throws Exception
	 * @Rule //若db2.rKindD第二碼為"X"，則db2.rKindM="1"<br>
	 *       //若db2.rKindD第一碼為"X"，則db2.rKindM="2"<br>
	 *       //若db2.rKindD兩碼不為空白，且皆不為X，則db2.rKindM="3"<br>
	 *       //若以上皆非，db2.rKindD = ""；db2.rKindM=""
	 */
	private void setRKindMD(Document domDoc, L140M01IBean L140i, int idx)
			throws Exception {
		String rKindD = getItemValue(domDoc, "tLngere_" + idx);
		if (StringUtils.isBlank(rKindD)) {
			L140i.setrKindD(TextDefine.EMPTY_STRING);
			L140i.setrKindM(TextDefine.EMPTY_STRING);// 這裡不能給null否則文字檔會寫出'null'
		} else {
			L140i.setrKindD(rKindD);
			if (rKindD.length() <= 1) {
				//20130503 modified by Sandra 與建霖確認，若不符合則轉為空白
				L140i.setrKindM(TextDefine.EMPTY_STRING);
				if (logger.isDebugEnabled()) {
					logger.debug(L140i.getMainId()+"的tLngere_" + idx + "=[" + rKindD
							+ "]有值但長度不足，無法判斷KindM值，已清空該資料！");
				}
			}else{
				String tmpD1 = rKindD.substring(0, 1);
				String tmpD2 = rKindD.substring(1, 2);
				if (tmpD2.equalsIgnoreCase("X")) {
					String rKindM = "1";
					L140i.setrKindM(rKindM);
				}
				if (tmpD1.equalsIgnoreCase("X")) {
					String rKindM = "2";
					L140i.setrKindM(rKindM);
				}
				if (!tmpD1.equalsIgnoreCase("X") && !tmpD2.equalsIgnoreCase("X")) {
					String rKindM = "3";
					L140i.setrKindM(rKindM);
				}
			}
		}
	}

	private boolean isKeyEmpty(String xml, int idx) {
		String[] keystr = KEY_LIST.get(idx - 1);
		for (int i = 0, size = keystr.length; i < size; i++) {
			// 2013-01-23 modify by
			// Bang:getKeyAry(i)[i]-->getKeyAry(idx)[i]:(i)[i]會抓錯欄位名稱,抓不到lp開頭的
			if (xml.indexOf(keystr[i]) == -1
					&& xml.indexOf(getKeyAry(idx)[i]) != -1) {
				return false;
			}
		}
		return true;
	}

	private static void initKeyString() {
		Logger logger = LoggerFactory.getLogger(Parser140M01I.class);

		String[] keys = null;
		for (int i = 1; i <= MAX_LOOP; i++) {
			keys = getKeyAry(i);
			String[] keystr = new String[keys.length];
			for (int j = 0, jsize = keys.length; j < jsize; j++) {
				// 2013-01-23 modify By Bang:第13為lp開頭的欄位,序號要從1開始
				// keystr[j] = "<item name='" + keys[j] + i +
				// "'><text/></item>";
				if (keys[j].indexOf("lp") > -1) {
					keystr[j] = "<item name='" + keys[j] + (i - 12)
							+ "'><text/></item>";
				} else {
					keystr[j] = "<item name='" + keys[j] + i
							+ "'><text/></item>";
				}
			}
			KEY_LIST.add(keystr);

			if (logger.isDebugEnabled()) {
				logger.debug(ArrayUtils.toString(keystr));
			}
		}
	}

	private static String[] getKeyAry(int idx) {
		return (idx <= 12) ? KEYS_1 : KEYS_2;
	}

}
