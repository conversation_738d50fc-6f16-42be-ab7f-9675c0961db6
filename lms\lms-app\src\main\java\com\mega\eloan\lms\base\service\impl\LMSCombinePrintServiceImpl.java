/* 
 * MicroEntServiceImpl.java 
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.ArrayUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.gwclient.RPAFTPClient;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.RPAService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSCombinePrintService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01I;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * BY 專案共用Service
 * </pre>
 * 
 * @since 2019/09
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/09,009301,new
 *          </ul>
 */
@Service("LMSCombinePrintService")
public class LMSCombinePrintServiceImpl extends AbstractCapService implements
		LMSCombinePrintService {

	protected final Logger logger = LoggerFactory.getLogger(getClass());
	@Resource
	DocFileService docFileService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	RPAFTPClient rpaFtpClient;

	@Resource
	RPAService rpaservice;

	@Resource
	LMSService lmsService;

	@Resource
	SysParameterService sysparamService;

	@Override
	public void saveFileInfo(String mainId, String fieldId, JSONObject jsonData) {

		List<DocFile> listFile = docFileService.findByIDAndName(mainId,
				fieldId, null);
		if (!listFile.isEmpty()) {
			for (DocFile file : listFile) {
				String fileOid = Util.trim(file.getOid());
				if (jsonData.containsKey(fileOid)) {
					JSONObject jsonFileInfo = jsonData.fromObject(jsonData
							.opt(fileOid));
					file.setSrcFileName(jsonFileInfo.optString("srcFileName"));
					file.setFileDesc(jsonFileInfo.optString("fileDesc"));
					docFileService.save(file, false);
				}
			}
		}

	}

	@Override
	public void sendCreatDoc(PageParameters params) throws CapMessageException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String fieldId = Util.trim(params.getString("fieldId"));
		String[] oids = params.getStringArray("oids");
		String rpaReturnFileName = Util.trim(params
				.getString("rpaReturnFileName"));

		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);

		boolean isTestEmail = "true"
				.equals(PropUtil.getProperty("isTestEmail")) ? true : false; // 是否為測試信件

		if (l120m01a != null) {
			ArrayList<String> filePath = new ArrayList<String>();
			ArrayList<String> fileName = new ArrayList<String>();
			List<DocFile> listFile = docFileService.findByIDAndName(mainId,
					fieldId, null);
			if (!listFile.isEmpty()) {
				for (DocFile file : listFile) {
					if (ArrayUtils.contains(oids, Util.trim(file.getOid()))) {
						filePath.add(docFileService.getFilePath(file));
						fileName.add(file.getSrcFileName());
					}
				}
			}

			// Map<String, Object> onlineDateMap = eloandbBASEService
			// .getSysParamData("RPAFTP_DEF_DIR"); ///HiNetRealEstate/
			// RPAFTP的預設目錄

			String combinePrint_unid = IDGenerator.getRandomCode();
			String mergeFileName = Util.equals(rpaReturnFileName, "") ? combinePrint_unid
					: rpaReturnFileName;

			JSONObject json = new JSONObject();

			// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
			json.put("responseURL", sysparamService
					.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS)); // 回傳位址
																				// SIT
																				// :
																				// "http://192.168.53.85:9081/lms-web/app/schedulerRPA"
			json.put("system", "eloan");
			json.put("funType", "1");
			json.put("branchNo", unitNo);
			json.put("empNo", user.getUserId());
			json.put("uniqueID", combinePrint_unid);
			json.put("totCount", filePath.size());
			json.put("mergeFileName", mergeFileName);
			json.put("Reference", l120m01a.getMainId());

			// MergePdfFiles/Input/
			String LMS_RPA_COMBINE_FTP_SERVER_DIR = Util.trim(lmsService
					.getSysParamDataValue("LMS_RPA_COMBINE_FTP_DIR_INPUT"));

			String serverDir = LMS_RPA_COMBINE_FTP_SERVER_DIR
					+ combinePrint_unid;

			String msgId = IDGenerator.getUUID();
			String[] ftpFiles = rpaFtpClient.list(msgId, serverDir);
			for (String ftpFile : ftpFiles) {
				rpaFtpClient.delete(msgId, ftpFile, serverDir);
			}
			rpaFtpClient.deleteFolder(msgId, mainId, serverDir);

			rpaFtpClient.send(mainId, filePath.toArray(new String[] {}),
					serverDir, fileName.toArray(new String[] {}), true, true,
					false);

			// 呼叫RPA
			String errorMsg = "";

			try {
				if (isTestEmail) {
					if (true) {
						errorMsg = this.queryRpaQuery(l120m01a, json);
					}
				} else {
					errorMsg = this.queryRpaQuery(l120m01a, json);
				}

			} catch (Exception ex) {
				ex.printStackTrace();
			}

			if (Util.equals(errorMsg, "")) {

				L120M01I l120m01i = l120m01a.getL120m01i();
				if (l120m01i == null) {
					l120m01i = new L120M01I();
					l120m01i.setMainId(l120m01a.getMainId());
				}

				l120m01i.setCombinePrint_unid(combinePrint_unid);
				l120m01i.setCombinePrint_time(CapDate.getCurrentTimestamp());
				// A01:查詢中 A02:查詢完成 A03:查詢失敗
				l120m01i.setCombinePrint_status(UtilConstants.RPA.STATUS.查詢中);
				l120m01i.setCombinePrint_errMsg("");
				lmsService.save(l120m01i);
			} else {
				throw new CapMessageException(errorMsg, this.getClass());
			}

		}

	}

	public String queryRpaQuery(L120M01A l120m01a, JSONObject json)
			throws CapMessageException {
		String token = "";
		String errorMsg = "";

		Map<String, Object> objResult = new LinkedHashMap<String, Object>();

		// Step 1 取得 token
		JSONObject resultJson = null;
		try {

			resultJson = rpaservice.getRPAAccessToken(objResult);
			token = resultJson != null ? resultJson.getString("result") : "";

			// token =
			// "eyJhbGciOiJSUzI1NiIsImtpZCI6IkI1M0FCMTY4RTIwMTBCQjIwRkU1Qzk1Q0I4OTY0RjhFOTFFMDc0N0EiLCJ0eXAiOiJKV1QiLCJjdHkiOiJKV1QiLCJ4NXQiOiJ0VHF4YU9JQkM3SVA1Y2xjdUpaUGpwSGdkSG8ifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RfMXnd4cx0CUP95oKwWJB0WRMMymzgYEja6KiPnLEoA0zKqlZVhDfRp8ul0ERsuunu4SnWPPr4HPing2ivXx5UOVwe5mGUtQ--n0o1FCckYXcb6N9h7Q5OZWw1Zt6syXp0h7Jilld-6HZWVJzT5vyXIJqXIHSTn6j-9dmm8o3vXKndQM5ibAPx7bfrzvrx1v2uW1BYTzZDgsDdqh-meFB-CjZm4irUkc6qZXUgoXqYdXgJsEPWUdjFYA0BL6bd7O_wInnDdbVaxur-U4PuECTrLPnkRy3xYJKoCNnrLzKN2y1bjRaDkEv-VmvS5dhBqjtr5fcNQd5tMNiwDZAVxEt3IIoz4sn2hlTSzkadegjTbmWpjWXKNcSukmjNIWZDeNlqjlexp8e99CJ6D9gHpt2ouVN97tfsV2CSs3D6VOkhV9PVFu_x4C7rUWhGXXzy943bYZ6CgvXyXbGFTlUuNrswle-676_6r4U_lsNUDHp304RtlIm3VxOnP3K4_nmCp4mccfLndg2u30QnqMh54vlRKqhD2nIy2aCi6nU2DaaPpLMUro6NR8ttt5xRxfueIYWxvtAuuepwZMTziLBNn85EZUCrNwIqcagp9zrfLFzZtehuacA-F31mqxh5nL5ePIQ8O748yZuv5wZoEI2NfC1Ff0cmFe481QTKJJo_TQzeg.HUhm6qA6VV3V9f6nZ4LPiVvjP-jGEOcf1MxgLeXlLdx-fa_j5kIAAEiJ4672iFdfhU3SyEp9l8u-xIvD6zkr44G2S5syFjlBaHbdazZOdBbejySV79FRv8F6bCOvTaz3N3DhjMf_guWl0hVRdp1hMTGg3KhUBCWXPNKNxJUO6VHuUdA9A8DsqmMn8_ulMAoCUk1CyZseGjBjIPWGGfqhG4lceoCXPcKNhtGFpiHfmmnQUjuXUgjr1LhW7GpjNsgnuaKrcFm46N7wOfjE4WqxmBMHfeSq1K839wm-vNgPmCleDbAxnLhBwZ8j0ZAfcPwvyG7pmhvfSda49m1h4vHmzqSnnCj2qAVwDys1PpAPrt95Qbiz10-sdNcEHvtVO7CrKdIHsFATLp_v81eUKGCanst2_g1FogYPpaeUx_wDqKqQyuyehj0u7VxRIJd9ZMtzlgpwqJwWXWdtPWZTO_Zj_-sK-2G32Ak3qNLZ0MTIIi1dOLJe_X9ODqQ6O_FezMYR3TGvy-BfwWX6yGVKZzZKffiytS2SLHc-Ohptb_OACG4uFKDdZ5UNO5LWGFtZHgLsbFhQm0rdmIqGY3OhcEmDVbAHigcgMbLDXH_7mjDV8zTe_enWmcYqIfGq5kTqov_dMG2Kag9dvH6tuZqDv5IauTLeTVw1fcPatEdgkN5EYns";

			token = "Bearer " + token;
			if (CapString.isEmpty(token)) {
				errorMsg = "queryRpaQuery\\getRPAAccessToken失敗，請稍後再試。";
				logger.info(errorMsg);
				return errorMsg;
			}
		} catch (Exception e) {
			errorMsg = "queryRpaQuery\\取得RPA Token失敗，請稍後再試。";
			logger.info(errorMsg);
			return errorMsg;
		}

		try {
			errorMsg = this.rpaPutQ(token, l120m01a, json);
		} catch (CapException e) {
			// throw new CapMessageException(e.getMessage(), this.getClass());
			errorMsg = "queryRpaQuery失敗\\" + e.getMessage();
			logger.info(errorMsg);
			return errorMsg;
		}

		return errorMsg;
	}

	public String rpaPutQ(String token, L120M01A l120m01a, JSONObject json)
			throws CapException {
		String errorMsg = "";
		String processKey = "";
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		JSONObject resultJson = null;
		boolean isTestEmail = "true"
				.equals(PropUtil.getProperty("isTestEmail")) ? true : false; // 是否為測試信件

		try {
			// STEP2 啟動JOB
			objResult = new LinkedHashMap<String, Object>();
			try {
				logger.info("啟動RPA開始=>" + SysParamConstants.RPA_授審會常董會合併列印審
						+ ":" + json.toString());
				// 查詢條件
				// objResult.put("system", json.get("system"));
				// objResult.put("uniqueID", json.get("uniqueID"));
				// objResult.put("SpecificContent", json.toString());

				String jsonStr = json.toString();

				objResult.putAll(new ObjectMapper().readValue(jsonStr,
						HashMap.class));

				logger.info("傳入參數==>[{}]", objResult.toString());

				resultJson = rpaservice.StartRPAJobForLMS(objResult, token,
						processKey, SysParamConstants.RPA_授審會常董會合併列印審);

				logger.info("執行啟動RPA結果=>[{}]", resultJson.toString());
				// {"message":"Queue_API_Start_Eloan_Retrial_934 does not exist.","errorCode":1002,"resourceIds":null}
				if (resultJson.containsKey("errorCode")
						&& Util.notEquals(
								resultJson.optString("errorCode", ""), "")) {

					errorMsg = "啟動RPA失敗=>" + (jsonStr) + "->"
							+ resultJson.toString();
					logger.info(errorMsg);

				}

				logger.info("啟動RPA結束=>" + SysParamConstants.RPA_授審會常董會合併列印審
						+ ":" + jsonStr);
			} catch (Exception e) {
				errorMsg = "RPA Job建立失敗，請稍後再試。" + e.toString();
				logger.info(errorMsg);
			}
		} catch (Exception e) {
			errorMsg = "RPA Job建立失敗，請稍後再試。" + e.toString();
			logger.info(errorMsg);
		}
		return errorMsg;
	}

	@Override
	public void clearRpaCombine(String mainId) {

		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);

		if (l120m01a != null) {
			L120M01I l120m01i = l120m01a.getL120m01i();
			if (l120m01i == null) {
				l120m01i = new L120M01I();
				l120m01i.setMainId(l120m01a.getMainId());
			}

			l120m01i.setCombinePrint_unid("");
			l120m01i.setCombinePrint_time(null);
			l120m01i.setCombinePrint_status("");
			l120m01i.setCombinePrint_errMsg("");

			lmsService.save(l120m01i);
		}

	}

	/**
	 * J-112-0508_05097_B1001 Web e-Loan為提升授信簽報效率,
	 * 三大部及授審處可由eloan授信系統依據授審會及常董會提案稿所需文件之順序產生相關提案稿pdf
	 * 
	 * @param l120m01a
	 * @param l120m01i
	 * @return
	 * @throws CapException
	 */
	public boolean checkIsOutNewVer(L120M01A l120m01a, L120M01I l120m01i)
			throws CapException {

		boolean isOut = false;

		if (l120m01i != null) {
			String printVerStr = LMSUtil.getPrintVerStr(l120m01a, l120m01i,
					false);
			if ((Util.equals(printVerStr, "v202011") || Util.equals(
					printVerStr, "v202212"))) {
				isOut = true;
			}
		}

		return isOut;
	}

}
