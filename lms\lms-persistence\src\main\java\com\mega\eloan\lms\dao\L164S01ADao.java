/* 
 * L164S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L162S01A;
import com.mega.eloan.lms.model.L164S01A;

/** 動審表借款人基本資料檔 **/
public interface L164S01ADao extends IGenericDao<L164S01A> {

	L164S01A findByOid(String oid);

	List<L164S01A> findByOid(String[] oids);

	List<L164S01A> findByMainId(String mainId);

	L164S01A findByUniqueKey(String mainId, String custId, String dupNo);

	List<L164S01A> findByIndex01(String mainId, String custId, String dupNo);

	List<L164S01A> findByIndex02(String mainId);
}