package com.mega.eloan.lms.fms.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 常用主管資料
 * </pre>
 * 
 * @since 2011/9/29
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/10/29,Vector Lo,new
 *          </ul>
 */
@Controller
@RequestMapping(path = "/fms/lms7010v01")
public class LMS7010V01Page extends AbstractEloanInnerView {

	@Autowired
	BranchService branchService;
	
	public LMS7010V01Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.Add, LmsButtonEnum.Modify,
				LmsButtonEnum.Delete);
		renderJsI18N(LMS7010V01Page.class);
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch ibranch = branchService.getBranch(user.getUnitNo());
		
		String value = "N";
		if(ibranch.getBrnMgr().equals(user.getUserId())){
			value = "Y";
		}
		
		// 前端須配合改Thymeleaf的樣式
		// TextField<?> textField = (TextField<?>) new TextField<Object>("isBranchManager").add(new SimpleAttributeModifier("value", value));
		// add(textField);
		model.addAttribute("isBranchManager", value);

		
	}// ;

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS7010V01Page.js" };
	}
}
