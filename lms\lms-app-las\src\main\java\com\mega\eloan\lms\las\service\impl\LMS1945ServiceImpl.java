package com.mega.eloan.lms.las.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;

import com.mega.eloan.lms.dao.L192M01ADao;
import com.mega.eloan.lms.las.service.LMS1945Service;
import com.mega.eloan.lms.model.L192M01A;

/**
 * 分行 稽核工作底稿 介面實作
 * 
 * <AUTHOR>
 * 
 */
@Service
public class LMS1945ServiceImpl extends AbstractCapService implements
		LMS1945Service {

	@Resource
	L192M01ADao l192m01aDao;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1945Service#get1945V01(tw.com.iisi.
	 * cap.dao.utils.ISearch)
	 */
	@Override
	public Page<L192M01A> get1945V01(ISearch search) {
		return l192m01aDao.findPage(search);
	}

}
