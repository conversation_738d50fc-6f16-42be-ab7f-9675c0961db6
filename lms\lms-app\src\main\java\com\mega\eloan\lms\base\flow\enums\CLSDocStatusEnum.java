/* 
 * CMSDocStatusEnum.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.flow.enums;

import java.util.Properties;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.pages.AbstractEloanPage;

/**<pre>
 * 個金文件狀態
 * </pre>
 * @since  2012/12/26
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/12/26,Fantasy,new
 *          </ul>
 */
public enum CLSDocStatusEnum {

	編製中("01O"),
	待覆核("02O"),
	已核准("03O"),
	//動用審核表 
	先行動用_已覆核("04O"), 
	先行動用_待覆核("05O");

	private String code;
	private static Properties pop = MessageBundleScriptCreator
			.getComponentResource(AbstractEloanPage.class);

	CLSDocStatusEnum(String code) {
		this.code = code;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return pop.getProperty("status." + this.code,
				EloanConstants.EMPTY_STRING);
	}

	public boolean isEquals(Object other) {
		if (other instanceof String) {
			return code.equals(other);
		} else {
			return super.equals(other);
		}
	}

	public static String getMessage(CLSDocStatusEnum status) {
		return getMessage(status.toString());
	}

	public static String getMessage(String status) {
		return pop.getProperty("CLSDocStatus." + status, EloanConstants.EMPTY_STRING);
	}

	public static CLSDocStatusEnum getEnum(String code) {
		for (CLSDocStatusEnum enums : CLSDocStatusEnum.values()) {
			if (enums.isEquals(code)) {
				return enums;
			}
		}
		return null;
	}

	@Override
	public String toString() {
		return code;
	}

}
