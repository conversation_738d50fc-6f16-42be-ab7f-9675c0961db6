package com.mega.eloan.lms.dao;

import com.mega.eloan.lms.model.C101S01W;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

/** 收入明細表 **/
public interface C101S01WDao extends IGenericDao<C101S01W> {

	C101S01W findByOid(String oid);

	List<C101S01W> findByMainId(String mainId);

	List<C101S01W> findByList(String mainId, String custId, String dupNo);

	List<C101S01W> findByList(String mainId, String custId, String dupNo, String[] incomeItemArray);
}