/* 
 * QUOTUNIO .java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import tw.com.iisi.cap.model.GenericBean;


/** 聯貸案參貸比率檔 **/
public class QUOTUNIO extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 
	 * 聯貸性質<p/>
	 * 同業聯貸<br/>
	 *  自行聯貸
	 */
	@Column(name="UNLNTYPE", length=1, columnDefinition="CHAR(01)",unique = true)
	private String unlntype;

	/** 
	 * 額度序號<p/>
	 * 額度序號即可區分DBU or OBU
	 */
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)",unique = true)
	private String cntrno;

	/** 
	 * 參貸行庫<p/>
	 * 國內銀行為行庫(3碼)及單位(3碼，檢查碼不傳)共6碼，若為國外銀行則另編6碼流水號
	 */
	@Column(name="UNITNO", length=6, columnDefinition="CHAR(06)",unique = true)
	private String unitno;

	/** 
	 * 主辦行<p/>
	 * Y 或 空白
	 */
	@Column(name="MAINBH", length=1, columnDefinition="CHAR(01)")
	private String mainbh;

	/** 
	 * 參貸金額<p/>
	 * 幣別及金額單位與總額度相同
	 */
	@Column(name="LNAMT", columnDefinition="DECIMAL(15)")
	private BigDecimal lnamt;

	/** 資料修改人（行員代號） **/
	@Column(name="UPDATER", length=5, columnDefinition="CHAR(05)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name="TMESTAMP", columnDefinition="TIMESTAMP")
	private Date tmestamp;

	/** 
	 * a-Loan執行時間（on Line）<p/>
	 * 暫不使用
	 */
	@Column(name="ONLNTIME", columnDefinition="TIMESTAMP")
	private Date onlntime;

	/** 
	 * a-Loan執行時間（Batch）<p/>
	 * 暫不使用
	 */
	@Column(name="BTHTIME", columnDefinition="TIMESTAMP")
	private Date bthtime;

	/** 
	 * 取得聯貸性質<p/>
	 * 同業聯貸<br/>
	 *  自行聯貸
	 */
	public String getUnlntype() {
		return this.unlntype;
	}
	/**
	 *  設定聯貸性質<p/>
	 *  同業聯貸<br/>
	 *  自行聯貸
	 **/
	public void setUnlntype(String value) {
		this.unlntype = value;
	}

	/** 
	 * 取得額度序號<p/>
	 * 額度序號即可區分DBU or OBU
	 */
	public String getCntrno() {
		return this.cntrno;
	}
	/**
	 *  設定額度序號<p/>
	 *  額度序號即可區分DBU or OBU
	 **/
	public void setCntrno(String value) {
		this.cntrno = value;
	}

	/** 
	 * 取得參貸行庫<p/>
	 * 國內銀行為行庫(3碼)及單位(3碼，檢查碼不傳)共6碼，若為國外銀行則另編6碼流水號
	 */
	public String getUnitno() {
		return this.unitno;
	}
	/**
	 *  設定參貸行庫<p/>
	 *  國內銀行為行庫(3碼)及單位(3碼，檢查碼不傳)共6碼，若為國外銀行則另編6碼流水號
	 **/
	public void setUnitno(String value) {
		this.unitno = value;
	}

	/** 
	 * 取得主辦行<p/>
	 * Y 或 空白
	 */
	public String getMainbh() {
		return this.mainbh;
	}
	/**
	 *  設定主辦行<p/>
	 *  Y 或 空白
	 **/
	public void setMainbh(String value) {
		this.mainbh = value;
	}

	/** 
	 * 取得參貸金額<p/>
	 * 幣別及金額單位與總額度相同
	 */
	public BigDecimal getLnamt() {
		return this.lnamt;
	}
	/**
	 *  設定參貸金額<p/>
	 *  幣別及金額單位與總額度相同
	 **/
	public void setLnamt(BigDecimal value) {
		this.lnamt = value;
	}

	/** 取得資料修改人（行員代號） **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定資料修改人（行員代號） **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Date getTmestamp() {
		return this.tmestamp;
	}
	/** 設定資料修改日期 **/
	public void setTmestamp(Date value) {
		this.tmestamp = value;
	}

	/** 
	 * 取得a-Loan執行時間（on Line）<p/>
	 * 暫不使用
	 */
	public Date getOnlntime() {
		return this.onlntime;
	}
	/**
	 *  設定a-Loan執行時間（on Line）<p/>
	 *  暫不使用
	 **/
	public void setOnlntime(Date value) {
		this.onlntime = value;
	}

	/** 
	 * 取得a-Loan執行時間（Batch）<p/>
	 * 暫不使用
	 */
	public Date getBthtime() {
		return this.bthtime;
	}
	/**
	 *  設定a-Loan執行時間（Batch）<p/>
	 *  暫不使用
	 **/
	public void setBthtime(Date value) {
		this.bthtime = value;
	}
}
