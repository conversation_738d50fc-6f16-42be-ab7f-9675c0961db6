/* 
 * C103M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.C103M01ADao;
import com.mega.eloan.lms.model.C103M01A;

/** 訪談紀錄表資料檔 **/
@Repository
public class C103M01ADaoImpl extends LMSJpaDao<C103M01A, String> implements
		C103M01ADao {

	@Override
	public C103M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C103M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C103M01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public C103M01A findByUniqueKey(String mainId) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C103M01A> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<C103M01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C103M01A> findRptCls180r58(String ownBrId, Date rptStartDate,
			Date rptEndDate) {
		ISearch search = createSearchTemplete();
		if (Util.isNotEmpty(ownBrId)) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					ownBrId);
		}

		Date fromDate = rptStartDate;
		Date endDate = null;

		if (Util.isNotEmpty(rptEndDate)) {
			endDate = Util.parseDate(CapDate.formatDate(rptEndDate,
					"yyyy-MM-dd") + " 23:59:59");
		}

		if (fromDate != null && endDate != null) {
			Object[] reason = { fromDate, endDate };
			search.addSearchModeParameters(SearchMode.BETWEEN, "createTime",
					reason);
		}

		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);

		if (true) {
			search.addOrderBy("createTime", false);
		}

		List<C103M01A> list = createQuery(C103M01A.class, search)
				.getResultList();

		return list;
	}
}