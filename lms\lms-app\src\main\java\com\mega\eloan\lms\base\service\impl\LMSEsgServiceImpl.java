/* 
 * MicroEntServiceImpl.java 
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.LMSEsgService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;

/**
 * <pre>
 * BY 專案共用Service
 * </pre>
 *
 * @since 2024/02
 * <AUTHOR>
 * @version <ul>
 *          <li>2024/02,009301,new
 *          </ul>
 */
@Service("LMSEsgService")
public class LMSEsgServiceImpl extends AbstractCapService implements
		LMSEsgService {

	protected final Logger logger = LoggerFactory.getLogger(getClass());

	@Resource
	EloandbBASEService eloandbBASEService;

	public CapAjaxFormResult applyEsgFa(String mainId, String custId, String dupNo) {
		CapAjaxFormResult result = new CapAjaxFormResult();

		Map<String, Object> map = eloandbBASEService
				.findLastC290m01aByCustId(custId);
		String finalAssessment = "";
		if (map == null) {
			finalAssessment = "查無資料";
		} else {
			finalAssessment = Util.nullToSpace(MapUtils.getString(map,
					"FINALASSESSMENT"));
		}

		result.set("finalAssessment", finalAssessment);
		return result;
	}

	public CapAjaxFormResult applyCesEsg(String mainId, String custId, String dupNo) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		Map<String, Object> c290Map = eloandbBASEService.findLastC290m01aByCustId(custId);
		Map<String, Object> c280Map = eloandbBASEService.findLastC280m01aByCustId(custId);
		String bizName = "";		// 行業對象別(*)
		String bizName4 = "";		// 行業對象別(後4碼)
		String subBizName = "";		// 行業對象別次註記
		String expLabor = "";		// 涉及有害或剝削勞工(超過新臺幣300萬元以上者)
		String envPollution = "";	// 涉及環境汙染事件(超過新臺幣300萬元以上者)
		String vioPrinciple = "";	// 涉及違反誠信經營原則(超過新臺幣300萬元以上者)
		String isCoal = "";			// 是否為煤炭企業
		String isUnconventionalOilgas = "";	// 是否為非傳統油氣企業
		String isEsgPlaned = "";	// 是否已提出永續轉型之明確佐證與計畫，或資金用途為永續發展之用，且具有可衡量且可驗證效益之指標者
		String isHighEnv = "";		// 高環境 High Environment
		String isHighCarbonEms = "";// 高碳排 High Carbon Emissions
		String isDeCarbonEms = "";	// 去碳化 decarbonization
		String isSustain = "";		// 永續 Sustain
		/*
		String[] highEnvBiz = { "0500", "0600", "1301", "4699", "1140", "7210",
				"1910", "1810", "2820", "1920", "1931", "1932", "1990" };
		String[] highCEmsBiz = { "1700", "1810", "1841", "2331", "2411", "3510" };
		*/
		String[] highEnvBiz = { "0500", "0600", "1140",	"1910" };
		String[] highEnvBizSubY = { "1301", "4699", "7210", "1810", "2820",
				"1920", "1931", "1932", "1990" };
		String[] highCEmsBiz = { "0500", "1511", "1512", "1513", "1520",
				"1591", "1599", "2411", "2412", "2413", "2414", "2421", "2422",
				"2423", "1700", "1810", "1841", "2331" };
		String[] highCEmsBizSubY = { "3510" };
		String[] esgPlanedY = { "1", "2", "3", "4", "5" };
		String[] isCoalY = { "1", "2", "3" };
		String[] isUnconventionalOilgasY = { "1", "2", "3", "4", "5" };
		if (c290Map == null) {
			isHighEnv = "K";
			isHighCarbonEms = "K";
		} else {
			bizName = Util.nullToSpace(MapUtils.getString(c290Map, "BIZNAME"));
			// [上午11:03] 黃俊達(資訊處,專員) 不用管前兩碼,你只比對後4碼
			int len = bizName.length();
			if(len > 3) {
				bizName4 = bizName.substring(len - 4, len);
			}
			subBizName = Util.nullToSpace(MapUtils.getString(c290Map, "SUBBIZNAME"));
			expLabor = Util.nullToSpace(MapUtils.getString(c290Map, "EXPLABOR"));
			envPollution = Util.nullToSpace(MapUtils.getString(c290Map, "ENVPOLLUTION"));
			vioPrinciple = Util.nullToSpace(MapUtils.getString(c290Map, "VIOPRINCIPLE"));

			if (Util.equals("Y", expLabor) || Util.equals("Y", envPollution) || Util.equals("Y", vioPrinciple)) {
				isHighEnv = "Y";
			} else if (Arrays.asList(highEnvBiz).contains(bizName4)) {
				isHighEnv = "Y";
			} else if (Util.equals("Y", subBizName) && Arrays.asList(highEnvBizSubY).contains(bizName4)) {
				isHighEnv = "Y";
			} else {
				isHighEnv = "N";
			}

			if (Arrays.asList(highCEmsBiz).contains(bizName4)) {
				isHighCarbonEms = "Y";
			} else if (Util.equals("Y", subBizName) && Arrays.asList(highCEmsBizSubY).contains(bizName4)) {
				isHighCarbonEms = "Y";
			} else {
				isHighCarbonEms = "N";
			}
		}

		if (c280Map == null) {
			isDeCarbonEms = "K";
			isSustain = "K";
		} else {
			String isNew = Util.nullToSpace(MapUtils.getString(c280Map, "ISNEW"));
			isCoal = Util.nullToSpace(MapUtils.getString(c280Map, "ISCOAL"));
			isUnconventionalOilgas = Util.nullToSpace(MapUtils.getString(c280Map, "ISUNCONVENTIONALOILGAS"));
			isEsgPlaned = Util.nullToSpace(MapUtils.getString(c280Map, "ISESGPLANED"));

			if (Util.isEmpty(isNew) || Util.equals("0", isNew)) {
				isDeCarbonEms = "K";
			} else if (Arrays.asList(isCoalY).contains(isCoal) || Arrays.asList(isUnconventionalOilgasY).contains(isUnconventionalOilgas)) {
				isDeCarbonEms = "Y";
			} else {
				isDeCarbonEms = "N";
			}

			if (Util.isEmpty(isNew) || Util.equals("0", isNew)) {
				isSustain = "K";
			} else if (Arrays.asList(esgPlanedY).contains(isEsgPlaned)) {
				isSustain = "Y";
			} else if (Util.equals("N", isEsgPlaned)) {
				isSustain = "N";
			} else {
				isSustain = "";
			}
		}
		result.set("isHighEnv", isHighEnv);
		result.set("isHighCarbonEms", isHighCarbonEms);
		result.set("isDeCarbonEms", isDeCarbonEms);
		result.set("isSustain", isSustain);
		result.set("qCesEsgDate", CapDate.getCurrentDate("yyyy-MM-dd"));

		StringBuffer esgMsgSb = new StringBuffer();
		if((Util.equals(isHighEnv, "Y") || Util.equals(isHighCarbonEms, "Y"))
				&& Util.equals(isSustain, "N")) {
			esgMsgSb.append(esgMsgSb.length() > 0 ? "<br/>" : "");
			esgMsgSb.append(pop.getProperty("L120S01Q.msg01"));
		}
		if(Util.equals(isDeCarbonEms, "Y") && Util.equals(isSustain, "N")) {
			esgMsgSb.append(esgMsgSb.length() > 0 ? "<br/>" : "");
			esgMsgSb.append(pop.getProperty("L120S01Q.msg02"));
		}
		if (esgMsgSb.length() > 0) {
			result.set("msg", esgMsgSb.toString());
		}
		return result;
	}

	public CapAjaxFormResult applyEsgSbti(String mainId, String custId, String dupNo) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		Map<String, Object> c280Map = eloandbBASEService.findLastC280m01aHadSbtiByCustId(custId);
		String isCommited = "";
		String sbtiApproveDate = "";

		if (c280Map == null) {
			isCommited = pop.getProperty("L120S01Q.noData");
		} else {
			String isNew = Util.nullToSpace(MapUtils.getString(c280Map, "ISNEW"));

			if (Util.isEmpty(isNew) || Util.equals("0", isNew)) {
				isCommited = pop.getProperty("L120S01Q.noData");
			} else {
				isCommited = Util.nullToSpace(MapUtils.getString(c280Map, "ISCOMMITED"));
				sbtiApproveDate = Util.nullToSpace(MapUtils.getString(c280Map, "APPROVETIME"));
			}
		}
		result.set("sbtiIsCommited", isCommited);
		result.set("qEsgSbtiDate", CapDate.getCurrentDate("yyyy-MM-dd"));
		if (Util.isNotEmpty(sbtiApproveDate)) {
			result.set("sbtiApproveDate", CapDate.formatDate(
					CapDate.parseDate(sbtiApproveDate), "yyyy-MM-dd"));
		} else {
			result.set("sbtiApproveDate", "");
		}

		return result;
	}

	public CapAjaxFormResult applySustainEval(String mainId, String custId, String dupNo) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		String hasSustainEval = "";
		String sustainEval = "";
		String qSustainEvalDate = CapDate.getCurrentDate("yyyy-MM-dd");
		BigDecimal rcvRatioSumY = BigDecimal.ZERO;
		BigDecimal rcvRatioSum = BigDecimal.ZERO;
		BigDecimal rcvRatioPercent = BigDecimal.ZERO;
		int countY = 0;
		int count = 0;
		List<Map<String, Object>> list = eloandbBASEService.findLastC300s01a(custId);
		if (list != null && list.size() > 0) {
			count = list.size();
			for (Map<String, Object> map : list) {
				BigDecimal rcvRatio = new BigDecimal(MapUtils.getDouble(map, "PJRCVRATIO",0.0));
				String pjResult = Util.nullToSpace(MapUtils.getString(map, "PJRESULT"));
				if (Util.equals(pjResult, "Y")) {
					countY++;
					rcvRatioSumY = rcvRatioSumY.add(rcvRatio);
				}
				rcvRatioSum = rcvRatioSum.add(rcvRatio);
			}
			if (rcvRatioSum.compareTo(BigDecimal.ZERO) == 0) {

			} else {
				rcvRatioPercent = rcvRatioSumY.divide(rcvRatioSum, 5, BigDecimal.ROUND_HALF_UP)
						.multiply(BigDecimal.valueOf(100))
						.setScale(0, BigDecimal.ROUND_HALF_UP);
			}
			hasSustainEval = "Y";
			result.set("seRec", count);
			result.set("seRateSum", rcvRatioSum);
			result.set("seRec_Y", countY);
			result.set("seRatePct", rcvRatioPercent);
		} else {
			hasSustainEval = "N";
			sustainEval = pop.getProperty("L120S01Q.noData");
			result.set("seRec", "");
			result.set("seRateSum", "");
			result.set("seRec_Y", "");
			result.set("seRatePct", "");
		}

		result.set("hasSustainEval", hasSustainEval);
		result.set("sustainEval", sustainEval);
		result.set("qSustainEvalDate", qSustainEvalDate);
		return result;
	}
}
