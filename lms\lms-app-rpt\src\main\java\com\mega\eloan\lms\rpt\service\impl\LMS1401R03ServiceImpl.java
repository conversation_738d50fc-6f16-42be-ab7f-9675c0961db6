package com.mega.eloan.lms.rpt.service.impl;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;

import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.rpt.service.LMS1401R03Service;
import com.mega.sso.service.BranchService;

/**
 * <pre>
 * 共同行銷維護作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class LMS1401R03ServiceImpl extends AbstractCapService implements LMS1401R03Service {

	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	EloandbcmsBASEService eloandbcmsBASEService;
	
	public Map<String, Integer> getTitleMap(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("LMS1401R03.UnsoldHouseInFinishedConstructionFinancingReport", 20);//建案完成未出售房屋融資統計表
		return titleMap;
	}
	
	@Override
	public void setTitleContent(WritableSheet sheet, Map<String, Integer> titleMap, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 15);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}

		for(String title : titleMap.keySet()){
			this.setCellsMergeFormat(sheet, fromColIndex, toColIndex, fromRowIndex, toRowIndex, prop.getProperty(title), cellFormat);
		}
	}
	
	@Override
	public Map<String, Integer> getHeaderMap(){
		
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("LMS1401R03.contract", 15);//額度序號
		titleMap.put("LMS1401R03.buildItem", 20);//融資案件分類
		titleMap.put("LMS1401R03.site1No", 18);//擔保品座落縣市別  
		titleMap.put("LMS1401R03.site2No", 20);//擔保品座落鄉鎮市區
		titleMap.put("LMS1401R03.site3No", 20);//擔保品座落段小段  
		titleMap.put("LMS1401R03.site4No", 20);//擔保品座落村里    
		titleMap.put("LMS1401R03.beginForSell", 15);//初貸餘屋戶數
		titleMap.put("LMS1401R03.newForSell", 15);//目前餘屋戶數
		titleMap.put("LMS1401R03.duringMonth", 20);//本案授信期間年限(月)
		titleMap.put("LMS1401R03.createTime", 15);//建檔時間
		titleMap.put("LMS1401R03.modifyTime", 15);//最近異動時間
		titleMap.put("LMS1401R03.cityOrArea", 18);//城市或區域別
		titleMap.put("LMS1401R03.riseTax", 15);//應計增值稅
		titleMap.put("LMS1401R03.branchNo", 15);//分行代碼
		titleMap.put("LMS1401R03.appraisalAmount", 15);//鑑估金額
		titleMap.put("LMS1401R03.loanAmount", 18);//放款值/參貸金額
		titleMap.put("LMS1401R03.timeValue", 18);//擔保品時價購價
		titleMap.put("LMS1401R03.custIdAndDupNo", 18);//客戶ID+重複
		titleMap.put("LMS1401R03.ChineseName", 15);//中文戶名
		titleMap.put("LMS1401R03.totalLoanAmount", 18);//台幣放款總
		titleMap.put("LMS1401R03.quotaAmount", 20);//額度金額等值台
		titleMap.put("LMS1401R03.interest", 15);//放款利率
		titleMap.put("LMS1401R03.recurringQuotaMark", 18);//循環額度註記
		titleMap.put("LMS1401R03.creditContractBeginDate", 18);//授信訂約起日
		titleMap.put("LMS1401R03.creditContractEndDate", 18);//授信訂約止日
		titleMap.put("LMS1401R03.firstUseDate", 15);//首次動用日
		return titleMap;
	}
	
	@Override
	public int setHeaderContent(WritableSheet sheet, Map<String, Integer> headerMap, Properties prop, int colIndex, int rowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}

		for(String header : headerMap.keySet()){
			this.setCellsFormat(sheet, headerMap.get(header), prop.getProperty(header), colIndex++, rowIndex, cellFormat);
		}
		
		return ++rowIndex;
	}
	
	@Override
	public List<Map<String, Object>> getStatisticsData(String startDate, String endDate){
		
		List<Map<String, Object>> elf517List = misdbBASEService.getElf517hBetweenStartDateAndEndDate(startDate, endDate);
		Map<String, String> caseTypeMap = codeTypeService.findByCodeType("lms140_remainLoanClass");
		Map<String, Map<String, Object>> locationMap = new HashMap<String, Map<String, Object>>();
		
		for(Map<String, Object> map : elf517List){
			
			String caseType = CapString.trimNull(map.get("ELF517H_BUILD_ITEM"));//融資案件分類
			map.put("CASE_TYPE_NAME", caseTypeMap.get(caseType));
			
			String cityCode = CapString.trimNull(map.get("ELF517H_SITE1NO"));//擔保品座落縣市別
			String districtCode = CapString.trimNull(map.get("ELF517H_SITE2NO"));//擔保品座落鄉鎮市區別
			String sectionCode = CapString.trimNull(map.get("ELF517H_SITE3NO"));//擔保品座落段小段
			String villageCode = CapString.trimNull(map.get("ELF517H_SITE4NO"));//擔保品座落村里
			districtCode = StringUtils.leftPad(districtCode, 2, '0');
			sectionCode = StringUtils.leftPad(districtCode, 4, '0');
				
		    String key = cityCode + districtCode + sectionCode + villageCode;
		    Map<String, Object> areaMap = locationMap.get(key);
		    
		    if(null == areaMap){
		    	locationMap.putAll(eloandbcmsBASEService.getCollateralLocationByDistrict(cityCode + districtCode));
		    	areaMap = locationMap.get(key);
		    }
		    
		    if(areaMap != null){
		    	map.put("CITY_COUNTY_NAME", areaMap.get("CITY_COUNTY_NAME"));
			    map.put("DISTRICT_NAME", areaMap.get("DISTRICT_NAME"));
			    map.put("SECTION_NAME", areaMap.get("SECTION_NAME"));
			    map.put("VILLAGE_NAME", areaMap.get("VILLAGE_NAME"));
		    }
		    
		    String createTime = CapString.trimNull(map.get("ELF517H_CREATETIME"));
		    createTime = !"".equals(createTime) ? CapDate.formatDateFormatToyyyyMMdd(createTime, "yyyy-MM-dd") : "";
		    map.put("ELF517H_CREATETIME", createTime);//建檔時間
		}
		
		return elf517List;
	}

	
	
	@Override
	public void setBodyContent(WritableSheet sheet, List<Map<String, Object>> elf517List, int colIndex, int rowIndex) throws RowsExceededException, WriteException{
		
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		for(Map<String, Object> map : elf517List){
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517H_CONTRACT")), cellFormat));//額度序號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CASE_TYPE_NAME")), cellFormat));//融資案件分類
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CITY_COUNTY_NAME")), cellFormat));//擔保品座落縣市別
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("DISTRICT_NAME")), cellFormat));//擔保品座落鄉鎮市區別
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("SECTION_NAME")), cellFormat));//擔保品座落段小段
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("VILLAGE_NAME")), cellFormat));//擔保品座落村里
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "ELF517H_BEGFORSELL"), cellFormat));//初貸餘屋戶數
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "ELF517H_NEWFORSELL"), cellFormat));//目前餘屋戶數
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517H_DURING_MON")), cellFormat));//本案授信期間年限(月)
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517H_CREATETIME")), cellFormat));//建檔時間
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517H_MODIFYTIME")), cellFormat));//最近異動時間
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517H_CITYNMH")), cellFormat));//城市或區域別
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517H_RISETAX")), cellFormat));//應計增值稅
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517H_BRN")), cellFormat));//分行代碼
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "ELF517H_APPAMT"), cellFormat));//鑑估金額
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "ELF517H_LOANAMT"), cellFormat));//放款值/參貸金額
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "ELF517H_TIMEVAL"), cellFormat));//擔保品時價購價
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517H_CUST_ID")), cellFormat));//客戶ID+重複碼
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517H_CNAME")), cellFormat));//中文戶名
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "ELF517H_LOAN_BAL"), cellFormat));//台幣放款總額
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "ELF517H_FACT_AMT"), cellFormat));//額度金額等值台幣
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "ELF517H_INT_RATE"), cellFormat));//放款利率
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517H_REVOLVE")), cellFormat));//循環額度註記
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517H_BEG_DATE")), cellFormat));//授信訂約起日
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517H_END_DATE")), cellFormat));//授信訂約止日
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF517H_USE_DATE")), cellFormat));//首次動用日
			rowIndex++;
			colIndex = 0;
		}
	}
	
	private void setCellsFormat(WritableSheet sheet, int width, String content, int colIndex, int rowIndex, WritableCellFormat cellFormat) throws RowsExceededException, WriteException{
		sheet.setColumnView(colIndex, width);//設定欄寬
		sheet.addCell(new Label(colIndex, rowIndex, content, cellFormat));
	}
	
	private void setCellsMergeFormat(WritableSheet sheet, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, 
										String content, WritableCellFormat cellFormat) throws RowsExceededException, WriteException{
		
		sheet.mergeCells(fromColIndex, fromRowIndex, toColIndex, toRowIndex);
		sheet.addCell(new Label(fromColIndex, fromRowIndex, content, cellFormat));
	}
}
