/* 
 * L140M01NEnum.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.enums;

/**
 * <pre>
 * L140M01N 利率結構化欄位相關ENUM
 * </pre>
 * 
 * @since 2012/10/24
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/24,REX,new
 *          </ul>
 */
public interface L140S02AEnum {

	/**
	 * <pre>
	 * 融資業務分類
	 * </pre>
	 * 
	 */
	public enum LnPurposeEnum {
		個人投資理財貸款("1"), 購置住宅貸款_其他("2"), 房屋修繕貸款_其他("3"), 購置住宅貸款_非自用("L"), 購置住宅貸款_自用(
				"M"), 房屋修繕貸款("N"), 汽車貸款("O"), 職工福利貸款("P"), 創業貸款("S"), 建築融資貸款(
				"U"), 代墊投標保證金貸款("V"), 參與都市更新計畫貸款("X"), 企業員工認購股票_或可轉換公司債貸款("Y"), 其他個人金融貸款(
				"Z"),由信用卡衍生之小額信用貸款("Q"),所營事業營運週轉金貸款("T"),農業用途貸款("W");

		private String code;

		private LnPurposeEnum(String code) {
			this.code = code;
		}

		public String getCode() {
			return this.code;
		}

		public boolean isEquals(Object other) {
			if ((other instanceof String)) {
				return this.code.equals(other);
			}
			return super.equals(other);
		}

		public static LnPurposeEnum getEnum(String code) {
			for (LnPurposeEnum enums : values()) {
				if (enums.isEquals(code)) {
					return enums;
				}
			}
			return null;
		}

	}

	/**
	 * <pre>
	 * 用途別
	 * </pre>
	 * 
	 */
	public enum LnPursEnum {
		購置不動產("1"), 購置動產("2"), 企業投資("3"), 週轉金("4");

		private String code;

		private LnPursEnum(String code) {
			this.code = code;
		}

		public String getCode() {
			return this.code;
		}

		public boolean isEquals(Object other) {
			if ((other instanceof String)) {
				return this.code.equals(other);
			}
			return super.equals(other);
		}

		public static LnPursEnum getEnum(String code) {
			for (LnPursEnum enums : values()) {
				if (enums.isEquals(code)) {
					return enums;
				}
			}
			return null;
		}

	}

	/**
	 * <pre>
	 * 用途別
	 * </pre>
	 * 
	 */
	public enum CreatSrcEnum {
		舊案轉入("0"), 分行新增("1"), 批覆書新增("2");

		private String code;

		private CreatSrcEnum(String code) {
			this.code = code;
		}

		public String getCode() {
			return this.code;
		}

		public boolean isEquals(Object other) {
			if ((other instanceof String)) {
				return this.code.equals(other);
			}
			return super.equals(other);
		}

		public static CreatSrcEnum getEnum(String code) {
			for (CreatSrcEnum enums : values()) {
				if (enums.isEquals(code)) {
					return enums;
				}
			}
			return null;
		}

	}

}
