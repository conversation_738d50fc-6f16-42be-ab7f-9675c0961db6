/* 
 * C101S01LDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C101S01LDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C101S01L;


/** 個金相關查詢利害關係人檔 **/
@Repository
public class C101S01LDaoImpl extends LMSJpaDao<C101S01L, String>
	implements C101S01LDao {

	@Override
	public C101S01L findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C101S01L> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C101S01L> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C101S01L findByUniqueKey(String mainId, String custId, String dupNo, String xType, String xCustId, String xDupNo){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (xType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "xType", xType);
		if (xCustId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "xCustId", xCustId);
		if (xDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "xDupNo", xDupNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C101S01L> findByIndex01(String mainId, String custId, String dupNo, String xType, String xCustId, String xDupNo){
		ISearch search = createSearchTemplete();
		List<C101S01L> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (xType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "xType", xType);
		if (xCustId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "xCustId", xCustId);
		if (xDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "xDupNo", xDupNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	@Override
	public List<C101S01L> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<C101S01L> list = createQuery(C101S01L.class,search).getResultList();
		return list;
	}
	
	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C101S01L.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}
}