/* 
 * L820M01EDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L820M01E;

/** RPA發查明家事公告細檔 **/
public interface L820M01EDao extends IGenericDao<L820M01E> {

	L820M01E findByOid(String oid);
	
	List<L820M01E> findByMainId(String mainId);
	
	L820M01E findByUniqueKey(String mainId, String custId, String dupNo);

	List<L820M01E> findByIndex01(String mainId, String custId, String dupNo);
}