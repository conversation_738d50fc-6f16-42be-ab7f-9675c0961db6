/* 
 *  LMS1405GridHandler.java 
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.grid;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.common.enums.CodeTypeEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.I18NFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.gwclient.EloanBatchClient;
import com.mega.eloan.common.gwclient.EloanServerBatReqMessage;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CMSDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.pages.LMS1205M01Page;
import com.mega.eloan.lms.lms.pages.LMS7840M01Page;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lms.service.LMS1415Service;
import com.mega.eloan.lms.model.C100M01;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01ATMP1;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01D;
import com.mega.eloan.lms.model.L140M01E;
import com.mega.eloan.lms.model.L140M01E_AF;
import com.mega.eloan.lms.model.L140M01F;
import com.mega.eloan.lms.model.L140M01I;
import com.mega.eloan.lms.model.L140M01J;
import com.mega.eloan.lms.model.L140M01K;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140M01O_0307;
import com.mega.eloan.lms.model.L140M01T;
import com.mega.eloan.lms.model.L140S04A;
import com.mega.eloan.lms.model.L140S06A;
import com.mega.eloan.lms.model.L140S11A;
import com.mega.eloan.lms.model.L140S12A;
import com.mega.eloan.lms.model.L141M01A;
import com.mega.eloan.lms.model.L999LOG01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 額度明細表grid
 * </pre>
 * 
 * @since 2011/9/6
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/6,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1405gridhandler")
public class LMS1405GridHandler extends AbstractGridHandler {

	@Resource
	LMS1405Service lms1405Service;

	@Resource
	LMS1205Service lms1205Service;

	@Resource
	LMS1415Service lms1415Service;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;
	@Resource
	LMSService lmsService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	EloanBatchClient eloanBatClient;

	@Resource
	DocFileService docFileService;

	private static String 額度明細表mainId = "tabFormMainId";

	/**
	 * 查詢額度明細表Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		String itemType = Util.nullToSpace(params.getString("itemType"));
		boolean includeNoChange = params.getAsBoolean("includeNoChange", true);
		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.mainId", caseMainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.itemType", itemType);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		if (!includeNoChange) {
			// pageSetting.addSearchModeParameters(SearchMode.AND,
			// new SearchModeParameter(SearchMode.NOT_LIKE, "proPerty", "%7%"),
			// new SearchModeParameter(SearchMode.NOT_LIKE, "proPerty", "%8%"));

			// pageSetting.addSearchModeParameters(SearchMode.NOT_LIKE,
			// "proPerty", "7");
			// pageSetting.addSearchModeParameters(SearchMode.NOT_LIKE,
			// "proPerty", "8");

			// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
			// 一、利用現行額度明細表「六、主要申請敘做條件」之(一)主要敘做條件維護功能, 增加額度檢視表所需「說明」欄位
			String LMS_0327_IMPORT_PROPERTY_7 = Util.trim(lmsService
					.getSysParamDataValue("LMS_0327_IMPORT_PROPERTY_7"));
			if (Util.notEquals(LMS_0327_IMPORT_PROPERTY_7, "Y")) {
				// 不能引進不變
				pageSetting.addSearchModeParameters(SearchMode.NOT_LIKE,
						"proPerty", "7");
			}
            String LMS_IMPORT_L120S16A_PROPERTY_8 = Util.trim(lmsService
                    .getSysParamDataValue("LMS_IMPORT_L120S16A_PROPERTY_8"));
            if (Util.notEquals(LMS_IMPORT_L120S16A_PROPERTY_8, "Y")) {
                pageSetting.addSearchModeParameters(SearchMode.NOT_LIKE,
                        "proPerty", "8"); // 只排除取消
            }
		}
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01A.class, pageSetting);
		// 檢核欄V為通過檢核且經過計算，O為 通過檢核 但尚未計算，X為尚未通過檢核
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name)); //
//		dataReformatter.put("docStatus", new I18NFormatter("docStatus."));
		List<L140M01A> l140m01alist = (List<L140M01A>) page.getContent();
		for (L140M01A l140m01a : l140m01alist) {

			// J-112-0426_05097_B1001
			// 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			sb.append("docStatus.");

			if (UtilConstants.DEFAULT.是.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("V");
			} else if (UtilConstants.DEFAULT.否.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("O");
			} else {
				l140m01a.setChkYN("X");
			}

			// J-112-0426_05097_B1001
			// 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。
			String addCheckBtEsg = "";
			if (Util.equals(l140m01a.getDocStatus(),
					FlowDocStatusEnum.已核准.getCode())
					|| Util.equals(l140m01a.getDocStatus(),
							FlowDocStatusEnum.婉卻.getCode())) {
				String checkBtEsg = Util.trim(l140m01a.getCheckBtEsg());
				addCheckBtEsg = Util.isEmpty(checkBtEsg) ? "" : "("
						+ checkBtEsg + ")";
			}

			// 用來暫放文件狀態
			l140m01a.setApprover(l140m01a.getDocStatus());
			if (UtilConstants.Cntrdoc.DataSrc.轉入額度明細表.equals(l140m01a
					.getDataSrc())) {
				// L140M01a.together=聯行
				l140m01a.setDataSrc(pop2.getProperty("L140M01a.together"));
			} else {
				l140m01a.setDataSrc("");
			}

			// J-112-0426_05097_B1001
			// 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。
			sb.append(l140m01a.getDocStatus());
			l140m01a.setDocStatus(getMessage(sb.toString()) + addCheckBtEsg);

		}
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢額度明細表-額度序號Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL140m01aCntrNo(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		String itemType = Util.nullToSpace(params.getString("itemType"));
		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.mainId", caseMainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.itemType", itemType);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		pageSetting
				.addSearchModeParameters(SearchMode.NOT_EQUALS, "cntrNo", "");
		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name)); //
		dataReformatter.put("docStatus", new I18NFormatter("docStatus."));

		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢額度明細表-額度序號Grid 資料 加上依據借款人統編來篩選
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL140m01aCntrNoForL120S08(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		String itemType = Util.nullToSpace(params.getString("itemType"));
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));
		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.mainId", caseMainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.itemType", itemType);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		pageSetting
				.addSearchModeParameters(SearchMode.NOT_EQUALS, "cntrNo", "");
		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name)); //
		dataReformatter.put("docStatus", new I18NFormatter("docStatus."));

		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢額度明細表Grid 資料 在聯行額度明細表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01aByL141m01b(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		L141M01A l141m01a = lms1415Service.findL141M01AByMainId(caseMainId);
		String itemType = lmsService.checkL140M01AItemType(
				l141m01a.getCaseBrId(), l141m01a.getDocKind(),
				l141m01a.getAuthLvl());
		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l141m01b.mainId", caseMainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l141m01b.itemType", itemType);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		// pageSetting.addOrderBy("custId");
		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name)); //
		dataReformatter.put("docStatus", new I18NFormatter("docStatus."));
		result.setDataReformatter(dataReformatter);
		List<L140M01A> l140m01as = (List<L140M01A>) page.getContent();
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		for (L140M01A l140m01a : l140m01as) {
			if (UtilConstants.DEFAULT.是.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("V");
			} else if (UtilConstants.DEFAULT.否.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("O");
			} else {
				l140m01a.setChkYN("X");
			}

			l140m01a.setApprover(l140m01a.getDocStatus());
			if (UtilConstants.Cntrdoc.DataSrc.轉入額度明細表.equals(l140m01a
					.getDataSrc())) {
				// L140M01a.together=聯行
				l140m01a.setDataSrc(pop2.getProperty("L140M01a.together"));
			} else {
				l140m01a.setDataSrc("");
			}
		}

		return result;
	}

	/**
	 * 查詢授信科目Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL140m01c(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String MainId = Util.nullToSpace(params.getString(額度明細表mainId));
		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01C.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		// lms1405m01_SubItem授信科目
		dataReformatter.put("loanTP", new CodeTypeFormatter(codeTypeService,
				UtilConstants.CodeTypeItem.授信科目)); // codeType格式化
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 篩選要複製的額度明細表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            <pre>
	 *            {
	 *            	type:1.列出本案借戶項下所有額度明細表 2.列出特定借戶項下所有額度明細表 3.列出所有額度明細表
	 *            	brNo:分行代號
	 *            borrowId:客戶統編+重覆序號
	 *                     
	 *            }
	 * </pre>
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryCopyFitlePeole(ISearch pageSetting,
			PageParameters params) throws CapException {

		String searchType = params.getString("type");
		String brNo = params.getString("brNo", "");

		if (!Util.isEmpty(brNo)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					brNo);
		}

		if ("1".equals(searchType) || "2".equals(searchType)) {
			String borrowId = Util
					.nullToSpace(params.getString("borrowId", ""));
			String custId = borrowId.substring(0, borrowId.length() - 1);
			String dupNo = borrowId.substring(borrowId.length() - 1,
					borrowId.length());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId.toUpperCase());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
					dupNo.toUpperCase());
		}
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		String copyType = Util.trim(params.getString("copyType", "1"));
		if ("1".equals(copyType)) {
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"docStatus", FlowDocStatusEnum.結案.getCode());
		} else {
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					new String[] { FlowDocStatusEnum.已核准.getCode(),
							FlowDocStatusEnum.婉卻.getCode() });
		}

		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01A.class, pageSetting);

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name)); //
		dataReformatter.put("docStatus", new I18NFormatter("docStatus."));
		result.setDataReformatter(dataReformatter);
		List<L140M01A> l140m01as = (List<L140M01A>) page.getContent();
		for (L140M01A l140m01a : l140m01as) {
			l140m01a.setCaseNo(Util.toSemiCharString(l140m01a.getCaseNo()));
		}
		return result;
	}

	/**
	 * 查詢科子目限額Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01d(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));
		String lmtType = Util.nullToSpace(params.getString("lmtType"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "lmtType",
				lmtType);
		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01D.class, pageSetting);

		if ("1".equals(lmtType)) {
			CapGridResult result = new CapGridResult(page.getContent(),
					page.getTotalRow());
			Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
			// lms1405m01_SubItem授信科目
			dataReformatter.put("subject", new CodeTypeFormatter(
					codeTypeService, UtilConstants.CodeTypeItem.授信科目)); // codeType格式化
			result.setDataReformatter(dataReformatter);
			return result;
		} else {

			StringBuilder buffer = new StringBuilder("");
			String[] codeType = { UtilConstants.CodeTypeItem.授信科目 };
			Map<String, CapAjaxFormResult> codeMap = codeTypeService
					.findByCodeType(codeType);
			List<L140M01D> l140m01ds = (List<L140M01D>) page.getContent();
			for (L140M01D l140m01d : l140m01ds) {
				String[] subject = l140m01d.getSubject().split(
						UtilConstants.Mark.SPILT_MARK);
				for (String item : subject) {
					buffer.append(buffer.length() > 0 ? "、" : "");
					buffer.append(codeMap.get(UtilConstants.CodeTypeItem.授信科目)
							.get(item));
				}
				l140m01d.setSubject(buffer.toString());
				buffer.setLength(0);
			}

			return new CapGridResult(l140m01ds, page.getTotalRow());
		}

	}

	/**
	 * 查詢攤貸比率Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01e(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01E.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("shareBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name)); //
		result.setDataReformatter(dataReformatter);
		List<L140M01E> l140m01es = (List<L140M01E>) page.getContent();
		for (L140M01E l140m01e : l140m01es) {
			if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(l140m01e
					.getShareFlag())) {
				l140m01e.setShowRate(l140m01e.getShareRate1() + "/"
						+ l140m01e.getShareRate2());
			} else {
				l140m01e.setShowRate("");
			}
		}
		return result;
	}
	
	/**
	 * 查詢動審後攤貸比率Grid 資料
	 * 
	 * @param pageSetting ISearch
	 * @param paramsPageParameters
	 * @param parent Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01e_af(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.MAIN_ID, mainId);
		Page<? extends GenericBean> page = lms1405Service.findPage(L140M01E_AF.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(), page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("shareBrId", new BranchNameFormatter(branchService, ShowTypeEnum.ID_Name)); //
		result.setDataReformatter(dataReformatter);
		List<L140M01E_AF> l140m01e_afs = (List<L140M01E_AF>) page.getContent();
		for (L140M01E_AF l140m01e_af : l140m01e_afs) {
			if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(l140m01e_af.getShareFlag())) {
				l140m01e_af.setShowRate(l140m01e_af.getShareRate1() + "/"
						+ l140m01e_af.getShareRate2());
			} else {
				l140m01e_af.setShowRate("");
			}
		}
		return result;
	}

	/**
	 * 查詢額度明細表利費率Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01f(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01F.class, pageSetting);
		StringBuilder builder = new StringBuilder("");
		String[] codeType = { UtilConstants.CodeTypeItem.授信科目 };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);
		List<L140M01F> l140m01fs = (List<L140M01F>) page.getContent();
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		for (L140M01F l140m01f : l140m01fs) {
			String[] subject = Util.trim(l140m01f.getLoanTPList()).split(
					UtilConstants.Mark.SPILT_MARK);
			for (String item : subject) {
				builder.append(builder.length() > 0 ? "、" : "");

				// 當項目值為空時不去找對應項目
				if (!Util.isEmpty(subject[0])) {
					builder.append(codeMap.get(UtilConstants.CodeTypeItem.授信科目)
							.get(item));
				}

			}

			if (Util.isEmpty(builder.toString())) {

				// L140M01c.lmtOther=沒有項目不組入字串
				builder.append(pop2.getProperty("L140M01c.lmtOther"));
			}

			l140m01f.setLoanTPList(builder.toString());
			builder.setLength(0);
		}
		return new CapGridResult(l140m01fs, page.getTotalRow());
	}

	/**
	 * 查詢連保人Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01i(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String type = Util.nullToSpace(params.getString("type"));

		// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
		String rType = Util.nullToSpace(params.getString("rType"));
		if (Util.equals(rType, "")) {
			rType = UtilConstants.lngeFlag.連帶保證人;
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rType", rType);
		DecimalFormat df = new DecimalFormat(
				"###,###,###,###,###,###,###,###.##");
		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01I.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		String[] codeType = { UtilConstants.CodeTypeItem.企業關係,
				UtilConstants.CodeTypeItem.親屬關係,
				UtilConstants.CodeTypeItem.綜合關係_企業,
				UtilConstants.CodeTypeItem.綜合關係_親屬, "lms1605s03_rType",
				CodeTypeEnum.國家代碼.getCode() };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);

		List<L140M01I> l140m01is = (List<L140M01I>) page.getContent();
		StringBuilder show = new StringBuilder("");
		for (L140M01I l140m01i : l140m01is) {
			show.setLength(0);
			l140m01i.setRId(l140m01i.getRId() + " " + l140m01i.getRDupNo());

			int rkindm = Util.parseInt(l140m01i.getRKindM());
			switch (rkindm) {
			case 1:
				l140m01i.setRKindD((String) codeMap.get(
						UtilConstants.CodeTypeItem.企業關係).get(
						l140m01i.getRKindD()));
				break;
			case 2:
				l140m01i.setRKindD((String) codeMap.get(
						UtilConstants.CodeTypeItem.親屬關係).get(
						l140m01i.getRKindD()));
				break;
			case 3:
				char[] kind = l140m01i.getRKindD().toCharArray();
				String kind1 = (String) codeMap.get(
						UtilConstants.CodeTypeItem.綜合關係_企業).get(
						String.valueOf(kind[0]));
				String kind2 = (String) codeMap.get(
						UtilConstants.CodeTypeItem.綜合關係_親屬).get(
						String.valueOf(kind[1]));
				show.append(kind1).append(" - ").append(kind2);
				l140m01i.setRKindD(show.toString());
				break;

			}

			l140m01i.setGuaPercentStr(l140m01i.getGuaPercent() == null ? ""
					: df.format(l140m01i.getGuaPercent()));

			// 國別
			l140m01i.setRCountry((String) codeMap.get(
					CodeTypeEnum.國家代碼.getCode()).get(l140m01i.getRCountry()));
		}
		return result;
	}

	/**
	 * 查詢聯行額度明細表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL141m01a(ISearch pageSetting,
			PageParameters params) throws CapException {

		String searchType = params.getString("type");
		String brNo = params.getString("brNo", "");
		// 建立主要Search 條件

		if (!Util.isEmpty(brNo) && brNo.length() == 3) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					brNo);
		}

		if ("1".equals(searchType) || "2".equals(searchType)) {
			String borrowId = Util
					.nullToSpace(params.getString("borrowId", ""));
			String custId = borrowId.substring(0, borrowId.length() - 1);
			String dupNo = borrowId.substring(borrowId.length() - 1,
					borrowId.length());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId.toUpperCase());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
					dupNo.toUpperCase());
		}

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = lms1415Service.findPage(
				L141M01A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("caseBrId", new BranchNameFormatter(branchService,
				BranchNameFormatter.ShowTypeEnum.ID_Name)); // 分行名稱格式化
		dataReformatter.put("coAppraiser", new UserNameFormatter(
				userInfoService, UserNameFormatter.ShowTypeEnum.Name)); // 使用者名稱格式化
		result.setDataReformatter(dataReformatter);
		List<L141M01A> l141m01as = (List<L141M01A>) page.getContent();
		for (L141M01A l141m01a : l141m01as) {
			l141m01a.setCaseNo(Util.toSemiCharString(l141m01a.getCaseNo()));
		}
		return result;
	}

	/**
	 * 查詢L120M01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		// 取得文件狀態代碼(交易代碼)
		// String docStatus = params.getString("mainDocStatus");

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		// 第三個參數為formatting
		Page<? extends GenericBean> page = lms1205Service.findPage(
				L120M01A.class, pageSetting);
		StringBuilder allCust = new StringBuilder();
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			allCust.setLength(0);
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo()).append(" ")
					.append(model.getCustName());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());

			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode()));
			} else {
				model.setDocStatus("");
			}
			model.setUpdater(this.getUserName(model.getUpdater()));
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(this.getUserName(model
						.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(Util.trim(model.getAreaAppraiser()));
			}
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(this.getUserName(model.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setApproveTime(model.getAreaSendInfo());
			}

		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢收付彙計數 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryCollect(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = params.getString(額度明細表mainId, "");
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01K.class, pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢額度明細表擔保品table
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL140m01o(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.trim(params.getString(額度明細表mainId));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01O.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("branch", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name));
		dataReformatter.put("collTyp1", new CodeTypeFormatter(codeTypeService,
				"lmsUseCms_collTyp1"));

		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢擔保品
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            <pre>
	 *            colltyp1 擔保品大類 
	 *            custId 客戶統編 
	 *            dupNo 重覆序號 
	 *            branch 分行代號
	 * </pre>
	 * @param parent
	 *            Component
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryCMS(ISearch pageSetting, PageParameters params) throws CapException {
		String colltyp1 = Util.trim(params.getString("cmsType", ""));
		String custId = Util.trim(params.getString("cmsCustId", ""));
		String dupNo = Util.trim(params.getString("cmsDupNo", ""));
		String branch = Util.trim(params
				.getString("selectFilterCMDBrno", "005"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "collTyp1",
				colltyp1);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);
		// '11B','12B','13B','14B','15B','16B','18B','1DC','17S','19S','1AS','1BS'

		pageSetting.addSearchModeParameters(
				SearchMode.IN,
				EloanConstants.DOC_STATUS,
				new String[] { CMSDocStatusEnum.分行_編製中.getCode(),
						CMSDocStatusEnum.分行_待覆核.getCode(),
						CMSDocStatusEnum.分行_已覆核.getCode(),
						CMSDocStatusEnum.分行_待設質.getCode(),
						CMSDocStatusEnum.分行_已設質.getCode(),
						CMSDocStatusEnum.分行_待塗銷.getCode(),
						CMSDocStatusEnum.聯行傳回.getCode(),
						CMSDocStatusEnum.代鑑價編製中.getCode(),
						CMSDocStatusEnum.代鑑價待覆核.getCode(),
						CMSDocStatusEnum.代鑑價已完成.getCode(),
						CMSDocStatusEnum.營運中心_編製中.getCode(),
						CMSDocStatusEnum.營運中心_待覆核.getCode(),
						CMSDocStatusEnum.營運中心_已覆核.getCode(),
						CMSDocStatusEnum.營運中心_已傳回.getCode(),
						CMSDocStatusEnum.營運中心_待收件.getCode(),
						CMSDocStatusEnum.營運中心_覆核待收件.getCode(),
						CMSDocStatusEnum.營運中心_覆核編制中.getCode(),
						CMSDocStatusEnum.營運中心_覆核待覆核.getCode(),
						CMSDocStatusEnum.營運中心_覆核已覆核.getCode(),
						CMSDocStatusEnum.營運中心_覆核已傳回.getCode(),
						CMSDocStatusEnum.待斷頭.getCode(),
						CMSDocStatusEnum.已斷頭.getCode(),
						CMSDocStatusEnum.補提.getCode(),
						CMSDocStatusEnum.擔保率不足.getCode(),
						CMSDocStatusEnum.授管處_編製中.getCode(),
						CMSDocStatusEnum.授管處_待覆核.getCode(),
						CMSDocStatusEnum.授管處_已覆核.getCode() });
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		Page<? extends GenericBean> page = lmsService.findPage(C100M01.class,
				pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		List<C100M01> c100m01s = (List<C100M01>) page.getContent();
		for (C100M01 model : c100m01s) {
			model.setAppraiserName(userInfoService.getUserName(model
					.getAppraiser()));
		}
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("branch", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name));
		dataReformatter.put("collTyp1", new CodeTypeFormatter(codeTypeService,
				"lmsUseCms_collTyp1"));
		if (UtilConstants.CollTyp1.保證.equals(colltyp1)) {
			dataReformatter.put("collTyp2", new CodeTypeFormatter(
					codeTypeService, "cms1090_collTyp2_5"));
		} else if (UtilConstants.CollTyp1.動產.equals(colltyp1)
				|| UtilConstants.CollTyp1.信託占有.equals(colltyp1)) {
			dataReformatter.put("collTyp2", new CodeTypeFormatter(
					codeTypeService, "cms1090_collTyp2_2"));
		} else if (UtilConstants.CollTyp1.權利質權.equals(colltyp1)) {
			dataReformatter.put("collTyp2", new CodeTypeFormatter(
					codeTypeService, "cms1090_collTyp2_3"));
		} else {
			dataReformatter.put("collTyp2", new CodeTypeFormatter(
					codeTypeService, ""));
		}

		dataReformatter.put("docStatus", new I18NFormatter("status."));
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (Util.isEmpty(userId)) {
			return "";
		}
		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

	/**
	 * 查詢資信檢表 平均動用率
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesInfo(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(額度明細表mainId));
		String caseMainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = lms1205Service.findL120m01aByMainId(caseMainId);
		L140M01A l140m01a = lms1405Service.findL140m01aByMainId(mainId);
		String ownBrId = l140m01a.getOwnBrId();
		if (l120m01a != null) {
			ownBrId = l120m01a.getCaseBrId();
		}
		Page<Map<String, Object>> page = eloandbBASEService.findCESL120S01A(
				pageSetting, ownBrId, l140m01a.getCustId(),
				l140m01a.getDupNo(), l140m01a.getCntrNo());
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			String docstatus = Util.trim(map.get("docStatus"));
			map.put("docStatus", Util.isEmpty(docstatus) ? ""
					: getMessage("docStatus." + docstatus));

			String strJson = Util.trim(map.get("JSONOB"));
			if (Util.notEquals(strJson, "")) {
				JSONObject jsObj = JSONObject.fromObject(strJson);
				map.put("UPDATETIME", jsObj.optString("lnDataDate", ""));
			}
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢共同借款人grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140M01J(ISearch pageSetting,
			PageParameters params) throws CapException {
		String tabMainId = Util.trim(params.getString(額度明細表mainId));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, tabMainId);
		String[] codeType = { "Relation_type1", "Relation_type2",
				"Relation_type31", "Relation_type32", "lms1605s03_rType",
				CodeTypeEnum.國家代碼.getCode() };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);
		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01J.class, pageSetting);
		List<L140M01J> l140m01js = (List<L140M01J>) page.getContent();
		for (L140M01J data : l140m01js) {
			data.setCustId(data.getCustId() + " " + data.getDupNo());

			data.setCustRlt(LMSUtil.changeCustRlt(Util.trim(data.getCustRlt()),
					codeMap));
			data.setNtCode(Util.trim(codeMap.get(CodeTypeEnum.國家代碼.getCode())
					.get(Util.trim(data.getNtCode()))));
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢簽報書借款人grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120S01A(ISearch pageSetting,
			PageParameters params) throws CapException {

		String mainId = Util.nullToSpace(params.getString("mainId"));
		HashMap<String, String> code = lms1405Service.getCustCounty(mainId);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);

		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		String[] codeType = { "Relation_type1", "Relation_type2",
				"Relation_type31", "Relation_type32", "lms1605s03_rType",
				CodeTypeEnum.國家代碼.getCode() };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);

		L120M01A l120m01a = lms1205Service.findL120m01aByMainId(mainId);

		if (LMSUtil.isOverSea_CLS(l120m01a)) {
			List<C120M01A> newlist = new ArrayList<C120M01A>();
			Page<? extends GenericBean> page = lms1205Service.findPage(
					C120M01A.class, pageSetting);

			List<C120M01A> list = (List<C120M01A>) page.getContent();

			for (C120M01A c120m01a : list) {
				if ((custId + dupNo).equals(c120m01a.getCustId()
						+ c120m01a.getDupNo())) {
					continue;
				}
				// 排除這份額度明細表的主要借款人
				c120m01a.setCustPos(Util.trim(code.get(c120m01a.getCustId()
						+ c120m01a.getDupNo())));
				c120m01a.setCustId(c120m01a.getCustId() + " "
						+ c120m01a.getDupNo());

				newlist.add(c120m01a);
			}
			return new CapGridResult(newlist, newlist.size());
		} else {
			List<L120S01A> newlist = new ArrayList<L120S01A>();
			Page<? extends GenericBean> page = lms1205Service.findPage(
					L120S01A.class, pageSetting);

			List<L120S01A> list = (List<L120S01A>) page.getContent();

			for (L120S01A l120s01a : list) {
				if ((custId + dupNo).equals(l120s01a.getCustId()
						+ l120s01a.getDupNo())) {
					continue;
				}
				// 排除這份額度明細表的主要借款人
				l120s01a.setCustPos(Util.trim(code.get(l120s01a.getCustId()
						+ l120s01a.getDupNo())));
				l120s01a.setCustId(l120s01a.getCustId() + " "
						+ l120s01a.getDupNo());
				l120s01a.setCustRlt(LMSUtil.changeCustRlt(
						l120s01a.getCustRlt(), codeMap));

				newlist.add(l120s01a);
			}
			return new CapGridResult(newlist, newlist.size());
		}

	}

	/**
	 * 查詢不符合授信政策事項Grid 資料 J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */

	@SuppressWarnings("unchecked")
	public CapGridResult queryL140s04a(ISearch pageSetting,
			PageParameters params) throws CapException {

		// 建立主要Search 條件
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String itemType = params.getString("itemType");
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "itemType",
				itemType);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140S04A.class, pageSetting);

		// List<L140S04A> list = (List<L140S04A>) page.getContent();
		// 格式化Grid內容
		// if (!list.isEmpty()) {
		// for (int i = 0; i < list.size(); i++) {
		// L140S04A model = list.get(i);
		// }
		// }
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 篩選L140M01AGrid 外部的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL140m01a2(ISearch pageSetting,
			PageParameters params) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7840M01Page.class);

		String unitType = user.getUnitType();
		String typCd = Util.trim(params.getString("typCd"));
		String docType = Util.trim(params.getString("docType"));
		String docKind = Util.trim(params.getString("docKind"));
		String docCode = Util.trim(params.getString("docCode"));
		String custName = Util.trim(params.getString("custName"));
		String updater = Util.trim(params.getString("updater"));
		String approveDateS = Util.nullToSpace(Util.trim(params
				.getString("approveDateS")));
		String approveDateE = Util.nullToSpace(Util.trim(params
				.getString("approveDateE")));
		String caseBrId = Util.nullToSpace(params.getString("caseBrId"));

		// 2012-09-06 黃建霖 begin
		String custId = Util.trim(params.getString("custId"));
		String cntrNo = Util.trim(params.getString("cntrNo"));

		String callFrom = Util.trim(params.getString("callFrom"));
		String mainId = Util.trim(params.getString("mainId"));

		// 全文檢索專用欄位**************************************************************
		String fxUserId = user.getUserId();
		String fxGroupId = user.getUnitNo();

		// ************************************************************************

		String reQuery = params.getString("newQuery") == null ? "N" : params
				.getString("newQuery");

		if (Util.notEquals(reQuery, "Y")) {
			// 單純REFRESH 網頁 GRID
		} else {
			// 授管處會走這邊，營運中心走LMS7850Flow.java送出查詢
			int delCount = lms1405Service.delPreSearchResult(fxUserId);

			L999LOG01A logDoc = lms1405Service.findLatestL999log01a(fxUserId,
					"2");
			String filterForm = params.getString("filterForm");

			// 刪除中文欄位SHOW，因為太長SEND 會ERROR
			JSONObject newFilter = JSONObject.fromObject(filterForm);
			String[] deleteShow = { "fxCurrShow", "fxLnSubjectShow",
					"fxRateText1Show", "fxCollateral1Show", "fxProdKindShow",
					"fxLnSubjectClsShow", "fxRateTextClsShow" };
			for (String delKey : deleteShow) {
				if (newFilter.containsKey(delKey)) {
					newFilter.remove(delKey);
				}
			}

			if (logDoc != null
					&& (Util.notEquals(Util.trim(logDoc.getResult()), "Y") && Util
							.notEquals(Util.trim(logDoc.getResult()), "N"))) {

				// 一直沒有結果且
				// 小於120分鐘
				Date currDate = CapDate.getCurrentTimestamp();
				Date createDate = logDoc.getCreateTime();
				long diff = currDate.getTime() - createDate.getTime();
				if (((diff) / (1000 * 60)) <= 120) {
					// L784M01A.error1=前次查詢尚未完成，請稍後再試 。最近一次查詢時間:
					throw new CapMessageException(
							prop.getProperty("L784M01A.error1")
									+ CapDate.getDateTimeFormat(logDoc
											.getCreateTime()) + "] ",
							getClass());
				}
			}

			logDoc = new L999LOG01A();
			logDoc.setItemType("2");
			logDoc.setMainId(IDGenerator.getUUID());

			// 因為資料很多，使用呼叫批次的方式執行
			try {
				EloanServerBatReqMessage req = new EloanServerBatReqMessage();
				req.setUserId(user.getUserId());
				req.setRunType(EloanServerBatReqMessage.RUN_TYPE_QUEUE);
				req.setSchId("SLMS-00047");
				JSONObject paraJson = new JSONObject();
				paraJson.put(EloanConstants.MAIN_OID, logDoc.getOid());

				paraJson.put("updater", user.getUserId());
				paraJson.put("filterForm", newFilter.toString());
				paraJson.put("unitType", user.getUnitType());
				paraJson.put("docStatus", docStatus);

				paraJson.put("userId", user.getUserId());
				paraJson.put("unitNo", user.getUnitNo());

				StringBuffer batchParams = new StringBuffer();
				batchParams.append("REQUEST=").append(paraJson.toString());

				req.setParams(batchParams.toString());

				req.setDupeId(StringUtils.left(logDoc.getOid(), 30));
				eloanBatClient.send(req);

				// 新增一筆L999LOG01A

				logDoc.setResult("S"); // 排程中
				logDoc.setItemDscr(filterForm);
				logDoc.setCreateTime(CapDate.getCurrentTimestamp());
				logDoc.setCreator(fxUserId);
				logDoc.setUpdater("");
				logDoc.setUpdateTime(null);
				lms1405Service.save(logDoc);

			} catch (Exception e) {
				// 新增一筆L999LOG01A

				// logDoc = new L999LOG01A();
				logDoc.setItemType("2");
				logDoc.setResult("N"); // 排程中
				logDoc.setItemDscr(filterForm);
				logDoc.setCreateTime(CapDate.getCurrentTimestamp());
				logDoc.setCreator(fxUserId);
				logDoc.setUpdater("");
				logDoc.setUpdateTime(null);
				// L784M01A.error2=排程失敗，請洽資訊處:
				logDoc.setExecMsg(prop.getProperty("L784M01A.error2")
						+ StrUtils.getStackTrace(e));
				lms1405Service.save(logDoc);
				e.printStackTrace();
				throw new CapMessageException(
						prop.getProperty("L784M01A.error2") + e.toString(),
						getClass());

			}

		}

		// ********************************************************************

		// 建立主要Search 條件
		if (Util.equals(callFrom, "lms7850m01page")) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "uid",
					mainId);
		} else {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "notesUp",
					fxUserId);
		}

		// 第三個參數為formatting
		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01ATMP1.class, pageSetting);

		// List<L140M01ATMP1> list = (List<L140M01ATMP1>) page.getContent();
		// 格式化Grid內容
		// if (!list.isEmpty()) {
		// for (int i = 0; i < list.size(); i++) {
		// L140M01ATMP1 model = list.get(i);
		// }
		// }
		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	private void setBy050Grid(ISearch pageSetting, String unitType) {
		if (BranchTypeEnum.營運中心.getCode().equals(unitType)) {
			// 已核准的營運中心只能看到授權外的或營運中心授權內
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "authLvl",
							UtilConstants.Casedoc.AuthLvl.營運中心授權內),
					new SearchModeParameter(SearchMode.EQUALS, "docKind",
							UtilConstants.Casedoc.DocKind.授權外));
		} else if (BranchTypeEnum.授管處.getCode().equals(unitType)) {
			// 已核准的授管處只能看到授權外的

			// J-104-0066-001 Web e-Loan授信系統授管處可以看到全行授權內、外簽報書
			// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docKind",
			// UtilConstants.Casedoc.DocKind.授權外);
		}
	}

	/**
	 * 依照案件別代碼取得相對應案件別名稱
	 * 
	 * @param doccode
	 *            String
	 * @return Properties
	 */
	public String docCodeName(String doccode) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205M01Page.class);
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		if ("1".equals(doccode)) {
			return pop.getProperty("L1205G.grid9");
		} else if ("2".equals(doccode)) {
			return pop.getProperty("L1205G.grid10");
		} else if ("4".equals(doccode)) {
			return pop2.getProperty("other.msg59");
		} else {
			return pop.getProperty("L1205G.grid11");
		}
	}

	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * 
	 * @param id
	 *            使用者id
	 * @return 空值: 使用者id 非空值: 使用者名稱
	 */
	private String getPerName(String id) {
		return (!Util.isEmpty(userInfoService.getUserName(id)) ? userInfoService
				.getUserName(id) : id);
	}

	/**
	 * 取得 案件類別名稱
	 * 
	 * @param model
	 *            簽報書主檔
	 * @param pop
	 *            語系檔
	 * @param temp
	 *            暫存的stringBuffer
	 * 
	 * @return
	 */
	private String getCaseType(L120M01A model, Properties pop, StringBuffer temp) {
		temp.setLength(0);
		String areaTitle = null;
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		if (UtilConstants.Casedoc.DocType.企金.equals(model.getDocType())) {
			if (UtilConstants.Casedoc.DocKind.授權內.equals(model.getDocKind())) {
				// L1205G.grid1=企金營運中心授權內
				// L1205G.grid1a=企金分行授權內
				if (UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(Util
						.trim(model.getAuthLvl()))) {
					temp.append(pop.getProperty("L1205G.grid1"));
				} else {
					if (UtilConstants.Casedoc.AuthLvl.總行授權內.equals(Util
							.trim(model.getAuthLvl()))) {
						// 企金總行授權內
						temp.append(pop.getProperty("L1205G.grid14"));

					} else {

						temp.append(pop.getProperty("L1205G.grid1a"));
					}

				}
			} else {
				areaTitle = queryAreaTitle(model);
				if (Util.isNotEmpty(areaTitle)) {
					// l120m01a.title0a=企金
					temp.append(pop.getProperty("l120m01a.title0a")).append(
							areaTitle);
				} else {
					temp.append(pop.getProperty("L1205G.grid2"));
				}
			}
		} else {
			if (UtilConstants.Casedoc.DocKind.授權內.equals(model.getDocKind())) {
				// L1205G.grid12=個金營運中心授權內
				// L1205G.grid12a=個金分行授權內
				if (UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(Util
						.trim(model.getAuthLvl()))) {
					temp.append(pop.getProperty("L1205G.grid12"));
				} else {

					if (UtilConstants.Casedoc.AuthLvl.總行授權內.equals(Util
							.trim(model.getAuthLvl()))) {
						// 個金總行授權內
						temp.append(pop.getProperty("L1205G.grid15"));
					} else {
						temp.append(pop.getProperty("L1205G.grid12a"));
					}

				}
			} else {
				areaTitle = queryAreaTitle(model);
				if (Util.isNotEmpty(areaTitle)) {
					// l120m01a.title0b=個金
					temp.append(pop.getProperty("l120m01a.title0b")).append(
							areaTitle);
				} else {
					temp.append(pop.getProperty("L1205G.grid13"));
				}
			}
		}
		// L1205G.grid9=一般
		// L1205G.grid10=其他
		// L1205G.grid11=陳復/陳述案
		temp.append("(");
		if (UtilConstants.Casedoc.DocCode.一般.equals(model.getDocCode())) {
			temp.append(pop.getProperty("L1205G.grid9"));
		} else if (UtilConstants.Casedoc.DocCode.其他.equals(model.getDocCode())) {
			temp.append(pop.getProperty("L1205G.grid10"));
		} else if (UtilConstants.Casedoc.DocCode.異常通報.equals(Util.trim(model
				.getDocCode()))) {
			// other.msg59=異常通報案件
			temp.append(pop2.getProperty("other.msg59"));
		} else if (UtilConstants.Casedoc.DocCode.團貸案件.equals(Util.trim(model
				.getDocCode()))) {
			// other.msg134=團貸
			temp.append(pop2.getProperty("other.msg134"));
		} else {
			temp.append(pop.getProperty("L1205G.grid11"));
		}
		temp.append(")");
		return temp.toString();
	}

	/**
	 * 取得國內屬營運中心制分行的標題名稱
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @return
	 * @throws CapException
	 */
	private String queryAreaTitle(L120M01A l120m01a) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch tBranch = branchService.getBranch((l120m01a != null) ? Util
				.trim(l120m01a.getCaseBrId()) : user.getUnitNo());
		String docKind = Util.trim(l120m01a.getDocKind());
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		if (tBranch != null) {
			String brnGroup = Util.trim(tBranch.getBrnGroup());
			if (UtilConstants.BankNo.中部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北一區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北二區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.中區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南區營運中心.equals(brnGroup)) {
				if (UtilConstants.Casedoc.DocKind.授權外.equals(docKind)) {
					/*
					 * 因為海外分行不屬於營運中心制，所以提醒第四階段，國內屬營運中心制分行時TITLE顯示會有差異
					 * 國內營運中心制分行，分行授權外案件會顯示營運中心授權外案件簽報書
					 */
					// other.msg131=營運中心授權外
					return pop.getProperty("other.msg131");
				}
			}
		}
		return null;
	}

	public CapGridResult queryL140m01t(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "flag",
				params.getString("flag"));

		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01T.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

		formatter
				.put("estateType",
						new CodeTypeFormatter(
								codeTypeService,
								"estateType",
								com.mega.eloan.common.formatter.CodeTypeFormatter.ShowTypeEnum.ValSpaceDesc));

		Map<String, String> estateType = codeTypeService
				.findByCodeType("estateType");
		Map<String, String> estateSubType = codeTypeService
				.findByCodeType("estateSubType");

		class EstateTypeFormatter implements IBeanFormatter {
			private static final long serialVersionUID = 2501150363189246663L;
			Map<String, String> estateType;
			Map<String, String> estateSubType;

			public EstateTypeFormatter(Map<String, String> estateType,
					Map<String, String> estateSubType) {
				this.estateType = estateType;
				this.estateSubType = estateSubType;
			}

			@Override
			public String reformat(Object in) throws CapFormatException {
				L140M01T l140m01t = (L140M01T) in;
				String estateType2 = l140m01t.getEstateType();
				if (UtilConstants.L140M01T_estatType.都更危老.equals(estateType2)) {
					return estateType2
							+ " "
							+ estateType.get(estateType2)
							+ "-"
							+ Util.trim(estateSubType.get(l140m01t
									.getEstateSubType()));
				} else {
					return estateType2 + " " + estateType.get(estateType2);
				}

			}

		}

		formatter.put("estateType", new EstateTypeFormatter(estateType,
				estateSubType));

		formatter.put("checkYN", new IFormatter() {

			@Override
			public String reformat(Object in) throws CapFormatException {
				String txt = (String) in;
				if ("Y".equals(txt)) {
					return "O";
				}
				return "X";
			}
		});

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}

	public CapGridResult queryFile(ISearch pageSetting, PageParameters params) throws CapException {

		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));
		String fieldId = params.getString("fieldId");
		boolean needCngName = params.getBoolean("needCngName");
		boolean needBranch = params.getBoolean("needBranch");
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		Page<DocFile> page = docFileService.readToGrid(pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL140s06aList(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.setMaxResults(Integer.MAX_VALUE);

		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140S06A.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

		formatter
				.put("intReg",
						new CodeTypeFormatter(
								codeTypeService,
								"lms140_intReg",
								com.mega.eloan.common.formatter.CodeTypeFormatter.ShowTypeEnum.Desc));

		formatter.put("checkYN", new IFormatter() {

			@Override
			public String reformat(Object in) throws CapFormatException {
				String txt = (String) in;
				if ("Y".equals(txt)) {
					return "O";
				}
				return "X";
			}
		});

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}

	/**
	 * 查詢額度明細表擔保品股票table
	 * 
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL140m01o_0307(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.trim(params.getString(額度明細表mainId));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Page<? extends GenericBean> page = lms1405Service.findPage(
				L140M01O_0307.class, pageSetting);

		List<L140M01O_0307> l140m01o_0307s = (List<L140M01O_0307>) page
				.getContent();
		for (L140M01O_0307 l140m01o_0307 : l140m01o_0307s) {
			if (Util.equals(Util.trim(l140m01o_0307.getCtrlKind()), "2")) {
				l140m01o_0307.setStkNum(null);
				l140m01o_0307.setSetRatio(null);
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * J-109-0470_05097_B1001 Web e-Loan授信簽案配合本行110年施行LTV法，土建融案件新增案件編號
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryAdcList(ISearch pageSetting,
			PageParameters params) throws CapException {
		String queryType = Util.trim(params.getString("queryType", ""));
		String queryCustId = Util.trim(params.getString("queryCustId", ""));
		String queryDupNo = Util.trim(params.getString("queryDupNo", ""));
		String queryCntrNo = Util.trim(params.getString("queryCntrNo", ""));
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		if (Util.isNotEmpty(queryType)) {
			HashSet<String> adcCaseNoSet = lmsService.getAdcCaseNoList(
					queryType, queryCustId, queryDupNo, queryCntrNo, "");
			for (String no : adcCaseNoSet) {
				data = new HashMap<String, Object>();
				data.put("adcCaseNo", no);
				newList.add(data);
			}
		}
		Page<Map<String, Object>> pages = LMSUtil.setPageMap(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
	}

	/**
	 * J-111-0397 RWA
	 **/
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryRwaL140m01a(ISearch pageSetting,
			PageParameters params) throws CapException {

		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		Boolean init = params.getAsBoolean("init", false);
		if (init) { // 第一次load畫面先不查
			return new CapMapGridResult(newList, newList.size());
		}
		// 查這份文件的MinId
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		String itemType = Util.nullToSpace(params.getString("itemType"));
		Map<String, String> proPertyMap = codeTypeService
				.findByCodeType("lms1405s02_proPerty");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.mainId", caseMainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.itemType", itemType);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");

		Page<? extends GenericBean> page = lmsService.findPage(L140M01A.class,
				pageSetting);
		List<L140M01A> l140m01aList = (List<L140M01A>) page.getContent();
		if (l140m01aList != null && l140m01aList.size() > 0) {
			for (L140M01A l140m01a : l140m01aList) {
				if (lmsService.chkCntrNoNeedRwa(l140m01a)) {
					data = new HashMap<String, Object>();
					data.put("oid", Util.nullToSpace(l140m01a.getOid()));
					data.put("custId", Util.nullToSpace(l140m01a.getCustId())
							+ "　" + Util.nullToSpace(l140m01a.getDupNo()));
					data.put("cntrNo", Util.nullToSpace(l140m01a.getCntrNo()));
					String[] ppArr = Util.nullToSpace(l140m01a.getProPerty())
							.split(UtilConstants.Mark.SPILT_MARK);
					StringBuffer temp = new StringBuffer();
					for (String value : ppArr) {
						temp.append(temp.length() > 0 ? "、" : "");
						temp.append(Util.trim(proPertyMap.get(value)));
					}
					data.put("proPerty", temp.toString());
					String[] strArr = lmsService.getRwaApplyAmt(l140m01a);
					if (strArr.length >= 2) {
						data.put(
								"applyAmt",
								Util.nullToSpace(strArr[0])
										+ "　"
										+ NumConverter.addComma(Util
												.nullToSpace(strArr[1])));
					} else {
						data.put("applyAmt", "");
					}
					data.put("rItemD", Util.nullToSpace(l140m01a.getRItemD()));
					newList.add(data);
				}
			}
		}

		// 排序
		Collections.sort(newList, new Comparator<Map<String, Object>>() {
			public int compare(Map<String, Object> o1, Map<String, Object> o2) {
				String custId1 = o1.get("custId").toString();
				String custId2 = o2.get("custId").toString();
				int a = custId1.compareTo(custId2);
				if (a != 0) {
					return a;
				}

				String cntrNo1 = o1.get("cntrNo").toString();
				String cntrNo2 = o2.get("cntrNo").toString();
				return cntrNo1.compareTo(cntrNo2);
			}
		});

		Page<Map<String, Object>> pages = LMSUtil.getMapGirdDataRow(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
	}

	/**
	 * // BIS
	 **/
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryBisL140m01a(ISearch pageSetting,
			PageParameters params) throws CapException {

		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		Boolean init = params.getAsBoolean("init", false);
		if (init) { // 第一次load畫面先不查
			return new CapMapGridResult(newList, newList.size());
		}
		// 查這份文件的MinId
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		Map<String, String> proPertyMap = codeTypeService
				.findByCodeType("lms1405s02_proPerty");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.mainId", caseMainId);
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.itemType", "1");
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");

		Page<? extends GenericBean> page = lmsService.findPage(L140M01A.class,
				pageSetting);
		List<L140M01A> l140m01aList = (List<L140M01A>) page.getContent();
		if (l140m01aList != null && l140m01aList.size() > 0) {
			for (L140M01A l140m01a : l140m01aList) {
				if (lmsService.chkCntrNoNeedBis(l140m01a)) {
					data = new HashMap<String, Object>();
					data.put("oid", Util.nullToSpace(l140m01a.getOid()));
					data.put("custId", Util.nullToSpace(l140m01a.getCustId())
							+ "　" + Util.nullToSpace(l140m01a.getCustName()));
					data.put("cntrNo", Util.nullToSpace(l140m01a.getCntrNo()));
					String[] ppArr = Util.nullToSpace(l140m01a.getProPerty())
							.split(UtilConstants.Mark.SPILT_MARK);
					StringBuffer temp = new StringBuffer();
					for (String value : ppArr) {
						temp.append(temp.length() > 0 ? "、" : "");
						temp.append(Util.trim(proPertyMap.get(value)));
					}
					data.put("proPerty", temp.toString());
					data.put("bisApplyCurr",
							Util.nullToSpace(l140m01a.getCurrentApplyCurr()));
					data.put("bisApplyAmt", NumConverter.addComma(Util
							.nullToSpace(l140m01a.getCurrentApplyAmt())));
					data.put("bisRItemD",
							Util.nullToSpace(l140m01a.getRItemD()));
					newList.add(data);
				}
			}
		}

		// 排序
		Collections.sort(newList, new Comparator<Map<String, Object>>() {
			public int compare(Map<String, Object> o1, Map<String, Object> o2) {
				String custId1 = o1.get("custId").toString();
				String custId2 = o2.get("custId").toString();
				int a = custId1.compareTo(custId2);
				if (a != 0) {
					return a;
				}

				String cntrNo1 = o1.get("cntrNo").toString();
				String cntrNo2 = o2.get("cntrNo").toString();
				return cntrNo1.compareTo(cntrNo2);
			}
		});

		Page<Map<String, Object>> pages = LMSUtil.getMapGirdDataRow(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
	}
	
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140s11aList(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Map<String, Boolean> orderBy = new LinkedHashMap<String, Boolean>();
		orderBy.put("seqNum", false);
		orderBy.put("createTime", true);
		pageSetting.setOrderBy(orderBy);
		pageSetting.setMaxResults(Integer.MAX_VALUE);

		Page<? extends GenericBean> page = lmsService.findPage(L140S11A.class,
				pageSetting);

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		return result;
	}
	
	public CapMapGridResult queryImportL140s11aList(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));
		L140M01A l140m01a = lms1405Service.findL140m01aByMainId(mainId);
		String cntrNo = null;
		if (l140m01a != null) {
			cntrNo = Util.nullToSpace(l140m01a.getCntrNo());
		}
		if (cntrNo == null) {
			List<Map<String, Object>> nullList = new ArrayList<Map<String, Object>>();
			return new CapMapGridResult(nullList, nullList.size());
		}
		String caseMainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		
		Page<Map<String, Object>> page = eloandbBASEService.queryOtherL140S11AByL140M01A(
				pageSetting, caseMainId, "1", cntrNo);
		
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
	/**
	 * J-113-0035 ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意承諾待追蹤ESG連結條款」的登錄機制
	 */
	@SuppressWarnings({ "unchecked", "serial" })
	public CapGridResult queryL140s12aList(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Map<String, Boolean> orderBy = new LinkedHashMap<String, Boolean>();
		orderBy.put("seqNum", false);
		orderBy.put("createTime", true);
		pageSetting.setOrderBy(orderBy);
		pageSetting.setMaxResults(Integer.MAX_VALUE);

		Page<? extends GenericBean> page = lmsService.findPage(L140S12A.class,
				pageSetting);
		
		L140M01A l140m01a = lms1405Service.findL140m01aByMainId(mainId);
		String esgSustainLoanType = "";
		if(l140m01a != null){
			esgSustainLoanType = l140m01a.getEsgSustainLoanType();
		}
		List<L140S12A> l140s12as = (List<L140S12A>) page.getContent();
		for (L140S12A data : l140s12as) {
			data.setEsgModel(esgSustainLoanType);//暫放 文件資訊 >> 永續績效連結授信類別	
		}
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("esgType", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L140S12A meta = (L140S12A) in;
				StringBuilder builder = new StringBuilder("");
				String l140s12a_esgtype = meta.getEsgType();
				String[] formatEsgType = Util.trim(l140s12a_esgtype).split(UtilConstants.Mark.SPILT_MARK);
				Map<String, String> esgTypeMap = codeTypeService.findByCodeType("l140s12a_esgType");
				
				for (String type : formatEsgType) {
					builder.append(builder.length() > 0 ? "<br/>" : "");
					// 當項目值為空時不去找對應項目
					if (Util.isNotEmpty(type)) {
						builder.append(esgTypeMap.get(type));
					}
				}		
				return builder.toString();
			}
		});
		
		
		CapGridResult result = new CapGridResult(page.getContent(), page.getTotalRow());
		result.setDataReformatter(dataReformatter);
		return result;
	}

}