package com.mega.eloan.lms.batch.service.impl;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * batch 刪除天數
 * </pre>
 * 
 * @since 2012/7/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/26,REX,new
 *          </ul>
 */
@Service("deleteBatchServiceImpl")
public class DeleteBatchServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(DeleteBatchServiceImpl.class);

	private static final long serialVersionUID = 1L;

	@Resource
	EloandbBASEService r6dbService;

	@Resource
	DocFileService docFileService;

	@Resource
	FlowService flowService;
	@Resource
	DocLogService docLogService;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		// 可以從json內取得參數
		long t1 = System.currentTimeMillis();
		LOGGER.info("傳入參數==>[{}]", json.toString());

		int totalCount = 0;
		JSONObject request = json.getJSONObject("request");
		int deletDay = request.getInt("day");

		StringBuilder failItem = new StringBuilder();
		HashMap<String, String[]> map = new HashMap<String, String[]>();

		// deleteTable.properties 記錄需要刪除的table
		Properties properties = new Properties();
		JSONObject result = new JSONObject();

		try {
			InputStream is = Thread.currentThread().getContextClassLoader()
					.getResourceAsStream("deleteTable.properties");
			properties.load(is);

			for (Object key : properties.keySet()) {
				String mainTable = Util.trim(key);
				String[] tabname = Util.trim(properties.get(key)).split(",");
				map.put(mainTable, tabname);
				LOGGER.info("mainTable==>[{}], refTable===>[{}]", mainTable,
						properties.get(key));
			}

			for (String tableName : map.keySet()) {

				List<Map<String, Object>> listData = r6dbService
						.findDeleteTime(tableName, deletDay);
				List<String> mainIdsObj = new ArrayList<String>();
				for (Map<String, Object> data : listData) {
					String mainid = Util.trim(data.get("MAINID"));
					if (mainid.isEmpty()) {
						continue;
					}
					mainIdsObj.add(mainid);

					if (UtilConstants.CaseDefName.案件簽報書.equals(tableName)
							|| UtilConstants.CaseDefName.額度明細表
									.equals(tableName)
							|| UtilConstants.CaseDefName.動用審核表
									.equals(tableName)
							|| UtilConstants.CaseDefName.企金戶新增增額逾放轉正名單檔
									.equals(tableName)
							|| UtilConstants.CaseDefName.企金覆審管理報表
									.equals(tableName)
							|| UtilConstants.CaseDefName.企金授信管理報表檔
									.equals(tableName)
							|| UtilConstants.CaseDefName.企金覆審名單
									.equals(tableName)
							|| UtilConstants.CaseDefName.個金覆審工作底稿主檔
									.equals(tableName)
                            || Util.equals(tableName, "L260M01A")) {
						// 刪除附加檔案
						docFileService.deleteByMainId(mainid);
						// 刪除流程
						flowService.cancel(Util.trim(data.get("OID")));
						// 刪除文件異動記錄
						docLogService.deleteLog(Util.trim(data.get("OID")));
					}

					if (UtilConstants.CaseDefName.管理報表.equals(tableName)) {
						// 刪除附加檔案
						docFileService.deleteByMainId(mainid);
					}

				}

				if (mainIdsObj.size() > 0) {
					String[] tableList = map.get(tableName);
					int count = 0;
					for (String table : tableList) {
						table = StringEscapeUtils.escapeSql(table);
						//Object[] msgFmtParam = new Object[] { table };

						try {
							if ("BRelated".equals(table)) {
								count = r6dbService
										.deleteBRelatedByMainId1(table, mainIdsObj.toArray(new String[0]));
							} else {
								count = r6dbService.deleteByMainId(table, mainIdsObj.toArray(new String[0]));
							}
							totalCount += count;
							LOGGER.info("mainId=>[{}] tableName=[{}]",
									StringUtils.join(mainIdsObj,","), tableName);
							LOGGER.info("{} edit count row {}", table, count);
						} catch (Exception e) {
							failItem.append(table
									+ " deleteBRelatedByMainId ===>"
									+ StringUtils.join(mainIdsObj,",") + " Fail;");
							LOGGER.error(
									"[execute] deleteByMainId Exception!!", e);
						}
					}
				}
			}

			if (Util.isEmpty(failItem.toString())) {
				result = WebBatchCode.RC_SUCCESS;
				result.element(WebBatchCode.P_RESPONSE, "執行成功！文件刪除總筆數 ==> "
						+ totalCount);
			} else {
				LOGGER.warn("FAIL_MESSAGE={}", failItem.toString());
				result = WebBatchCode.RC_ERROR;
				result.element(WebBatchCode.P_RC_MSG, failItem.toString());
			}

		} catch (IOException ex) {
			LOGGER.error("[execute] properties.load Exception!!", ex);
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, "properties.load Exception!!"
					+ StrUtils.getStackTrace(ex));
			return result;
		} catch (Exception ex) {
			LOGGER.error("[execute] Exception!!", ex);
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, ex.getLocalizedMessage()
					+ "==>" + failItem.toString());
		} finally {
			LOGGER.info("RESULT={}", result.toString());
			LOGGER.info(StrUtils.concat("TOTAL_COST= ",
					(System.currentTimeMillis() - t1), " ms"));
		}

		return result;
	}

}
