/* 
 * L140M01D_BFDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01D_BF;

/** 額度授信科目限額檔(變更前) **/
public interface L140M01D_BFDao extends IGenericDao<L140M01D_BF> {

	L140M01D_BF findByOid(String oid);

	List<L140M01D_BF> findByMainId(String mainId);

	L140M01D_BF findByUniqueKey(String mainId, String lmtType, Integer lmtSeq);

	List<L140M01D_BF> findByIndex01(String mainId, String lmtType,
			Integer lmtSeq);
}