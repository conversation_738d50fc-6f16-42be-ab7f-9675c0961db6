---------------------------------------------------------
-- LMS.L140S02J 外勞貸款檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140S02J;
CREATE TABLE LMS.L140S02J (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	SEQ           DECIMAL(5,0)  not null,
	INTROID       VARCHAR(10)  ,
	INTRODUPNO    CHAR(1)      ,
	INTRONAME     VARCHAR(120) ,
	INTRORATE     DECIMAL(5,2) ,
	HABITDATE     DATE         ,
	HABITID       VARCHAR(20)  ,
	SUBPAYWAY     VARCHAR(5)   ,
	ACCNO         VARCHAR(14)  ,
	AGENCY        CHAR(1)      ,
	GUATYPE       VARCHAR(5)   ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140S02J PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140S02J01;
CREATE UNIQUE INDEX LMS.XL140S02J01 ON LMS.L140S02J   (MAINID, SEQ);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140S02J IS '外勞貸款檔';
COMMENT ON LMS.L140S02J (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	SEQ           IS '序號', 
	INTROID       IS '仲介公司統一編號', 
	INTRODUPNO    IS '仲介公司重覆序號', 
	INTRONAME     IS '隸屬之仲介公司', 
	INTRORATE     IS '仲介獎金比率', 
	HABITDATE     IS '居留證核發日期', 
	HABITID       IS '居留證字號', 
	SUBPAYWAY     IS '償還辦理方式', 
	ACCNO         IS 'A/C存款帳戶', 
	AGENCY        IS '承諾存款', 
	GUATYPE       IS '保證金', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
