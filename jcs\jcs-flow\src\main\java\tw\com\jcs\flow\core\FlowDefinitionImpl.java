package tw.com.jcs.flow.core;

import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.Map;

import tw.com.jcs.flow.FlowDefinition;
import tw.com.jcs.flow.node.FlowNode;
import tw.com.jcs.flow.provider.FlowHandler;

/**
 * <pre>
 * <h1>流程定義資訊</h1> 儲存流程定義的相關資訊，做為流程實體執行的依據
 * </pre>
 * 
 * @since 2022年12月22日
 * <AUTHOR> Software Inc.
 * @version
 *          <ul>
 *          <li>2022年12月22日
 *          </ul>
 */
public class FlowDefinitionImpl implements FlowDefinition {

    /**
     * 流程定義名稱
     */
    String name;

    /**
     * 流程定義的處理器 {@link tw.com.jcs.flow.provider.FlowHandler}
     */
    FlowHandler handler;
    String handlerClass;

    /**
     * 流程啟始節點
     */
    FlowNode startNode;

    /**
     * 流程終止節點
     */
    Collection<FlowNode> endNodes = new LinkedList<FlowNode>();

    /**
     * 流程定義的所有節點
     */
    Map<String, FlowNode> nodes = new HashMap<String, FlowNode>();

    public String getName() {
        return name;
    }

    public FlowHandler getHandler() {
        return handler;
    }

    public void setHandler(FlowHandler handler) {
        this.handler = handler;
    }

    public Map<String, FlowNode> getNodes() {
        return nodes;
    }

    public void setNodes(Map<String, FlowNode> nodes) {
        this.nodes = nodes;
    }

    public void setName(String name) {
        this.name = name;
    }

    public FlowNode getStartNode() {
        return startNode;
    }

    public void setStartNode(FlowNode startNode) {
        this.startNode = startNode;
    }

    public Collection<FlowNode> getEndNodes() {
        return endNodes;
    }

    public void setEndNodes(Collection<FlowNode> endNodes) {
        this.endNodes = endNodes;
    }

    /**
     * @return the handlerClass
     */
    public String getHandlerClass() {
        return handlerClass;
    }

    /**
     * @param handlerClass
     *            the handlerClass to set
     */
    public void setHandlerClass(String handlerClass) {
        this.handlerClass = handlerClass;
    }

}
