/*
 * CapLoggerListener.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.security.authentication.event;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.event.AbstractAuthenticationEvent;
import org.springframework.security.authentication.event.AbstractAuthenticationFailureEvent;
import org.springframework.security.authentication.event.InteractiveAuthenticationSuccessEvent;
import org.springframework.security.authentication.event.LoggerListener;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.WebAuthenticationDetails;

/**
 * <pre>
 * 事件紀錄
 * </pre>
 * 
 * @since 2011/3/24
 * <AUTHOR>
 * @version $Id$
 * @version
 *          <ul>
 *          <li>2011/3/24,iristu,new
 *          </ul>
 */
public class CapLoggerListener extends LoggerListener {

    private static final Logger logger = LoggerFactory.getLogger(CapLoggerListener.class);

    /*
     * 身分驗證事件紀錄
     * 
     * @see org.springframework.security.authentication.event.LoggerListener#onApplicationEvent(org.springframework.security.authentication.event.AbstractAuthenticationEvent)
     */
    @Override
    public void onApplicationEvent(AbstractAuthenticationEvent event) {
        if (event instanceof AbstractAuthenticationEvent && !(event instanceof InteractiveAuthenticationSuccessEvent)) {

            AbstractAuthenticationEvent authEvent = (AbstractAuthenticationEvent) event;

            Authentication auth = authEvent.getAuthentication();

            String errorMsg = null;

            if (event instanceof AbstractAuthenticationFailureEvent) {
                errorMsg = ((AbstractAuthenticationFailureEvent) event).getException().getMessage();
            }

            if (logger.isDebugEnabled()) {

                StringBuffer message = new StringBuffer("Session Login - LoginId: ").append(authEvent.getAuthentication().getName()).append("; RemoteAddress: ")
                        .append(((WebAuthenticationDetails) auth.getDetails()).getRemoteAddress());

                if (errorMsg != null) {
                    message.append("; login fail: ").append(errorMsg);
                }
                logger.debug(message.toString());
            }
        }
    }

}
