package com.mega.eloan.lms.crs.handler.form;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;

import com.iisigroup.cap.component.PageParameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocOpener;
import com.mega.eloan.common.model.DocOpener.OpenTypeCode;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.pages.LMS2420M01Page;
import com.mega.eloan.lms.crs.service.LMS2400Service;
import com.mega.eloan.lms.model.C242M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;



@Scope("request")
@Controller("lms2420m01formhandler")
@DomainClass(C242M01A.class)
public class LMS2420M01Formhandler extends AbstractFormHandler {
	private static final Logger logger = LoggerFactory.getLogger(LMS2420M01Formhandler.class);
	private static final DateFormat S_FORMAT = new SimpleDateFormat(UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
	private static final int MAXLEN_C242M01A_EXEPARAM = StrUtils.getEntityFileldLegth(C242M01A.class, "exeParam", 768);
		
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	BranchService branchService;
		
	@Resource
	DocCheckService docCheckService;
	
	@Resource
	LMS2400Service lms2400Service;
	
	@Resource
	RetrialService retrialService;
	
	Properties prop_lms2420m01 = MessageBundleScriptCreator.getComponentResource(LMS2420M01Page.class);
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C242M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findC242M01A_oid(mainOid);	
			
			String status = lms2400Service.c242m01a_docStatusDesc(meta);
			if(meta.getExeTime()!=null && Util.equals(meta.getDocStatus(), RetrialDocStatusEnum.預約單_未處理.getCode())){
				status = S_FORMAT.format(meta.getExeTime());
			}
			result.set("status", status);			
			result.set("creator", _id_name(meta.getCreator()));				
			result.set("updater", _id_name(meta.getUpdater()));
			result.set("exeParam", new CapAjaxFormResult(CrsUtil.parse_exeParam(meta.getExeParam())));
			String sch_str = "";
			if(meta.getSchTime()!=null){
				sch_str = S_FORMAT.format(meta.getSchTime());
			}else{
				if(Util.isNotEmpty(meta.getExeParam())){
					
				}else{
					int delayMinutes = 10;
					Timestamp defTS = CapDate.getCurrentTimestamp();			
					defTS.setTime(defTS.getTime() + delayMinutes*60*1000);
					sch_str = S_FORMAT.format(defTS);
				}
			}			
			result.set("sch_date", StringUtils.substring(sch_str, 0, 10));
			result.set("sch_hh", StringUtils.substring(sch_str, 11, 13));
			result.set("sch_mm", StringUtils.substring(sch_str, 14, 16));
			
		}

		return defaultResult(params, meta, result);
	}
	
	private CapAjaxFormResult defaultResult(PageParameters params, C242M01A meta,
			CapAjaxFormResult result) throws CapException {
		
		// required information
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));		
		return result;
	}
	
	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}
	
	private CapAjaxFormResult _saveAction(PageParameters params,String tempSave)
	throws CapException{
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		//===
		String KEY = "saveOkFlag";
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C242M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try{
				meta = retrialService.findC242M01A_oid(mainOid);
				if(meta.getExeTime()!=null){
					throw new CapMessageException(prop_lms2420m01.getProperty("lms2420m01.msg.inProcessing"), getClass());
				}
				//---
				String exeParam = Util.trim(params.getString("exeParam"));
				if(exeParam.length()>MAXLEN_C242M01A_EXEPARAM){
					Map<String, String> param = new HashMap<String, String>();
					param.put("colName", prop_lms2420m01.getProperty("lms2420m01.exeParam"));
					throw new CapMessageException(RespMsgHelper.getMessage(
							UtilConstants.AJAX_RSP_MSG.輸入位數超過, param), getClass());
				}
				meta.setExeParam(exeParam);
				meta.setSchTime(Timestamp.valueOf(Util.trim(params.getString("schTime"))));
				//---
				retrialService.save(meta);				
				result.set(KEY, true);	
			}catch(Exception e){
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}		
		}
		
		result.add(query(params));
		
		return result;
	}
	
	private String _id_name(String raw_id){
		String id = Util.trim(raw_id);
		return Util.trim(id+" "+Util.trim(userInfoService.getUserName(id)));
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult newC242M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		C242M01A c242m01a = new C242M01A();
		c242m01a.setMainId(IDGenerator.getUUID());
		c242m01a.setOwnBrId(user.getUnitNo());
		c242m01a.setUnitType(user.getUnitType());
		c242m01a.setDocStatus(RetrialDocStatusEnum.預約單_未處理.getCode());
		c242m01a.setCreator(user.getUserId());
		c242m01a.setCreateTime(nowTS);
		c242m01a.setUpdater(user.getUserId());
		c242m01a.setUpdateTime(nowTS);
		//---
		retrialService.save(c242m01a);
		
		return defaultResult(params, c242m01a, result);
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult deleteMark(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String list = params.getString("list");
		
		boolean edit = false;
		C242M01A c242m01a = retrialService.findC242M01A_oid(list);
		List<DocOpener> docOpeners = docCheckService.findByMainId(c242m01a.getMainId());
		for(DocOpener docOpener : docOpeners){
			if(OpenTypeCode.Writing.getCode().equals(docOpener.getOpenType())){
				HashMap<String, String> hm = new HashMap<String, String>();
				hm.put("userId", docOpener.getOpener());
				hm.put("userName",
						userInfoService.getUserName(docOpener.getOpener()));
				edit = true;
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMessage(
						"EFD0009", hm));
				break;
			}
		}
		if(!edit){
			if(c242m01a!=null){
				if(c242m01a.getExeTime()!=null){
					throw new CapMessageException(prop_lms2420m01.getProperty("lms2420m01.msg.inProcessing"), getClass());
				}	
				c242m01a.setDeletedTime(CapDate.getCurrentTimestamp());
				retrialService.save(c242m01a);
			}
			// EFD0019=刪除成功
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
					RespMsgHelper.getMainMessage("EFD0019"));
		}
		return result;
	}
	
	public IResult queryBranch(PageParameters params)
	throws CapException {
	
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		TreeMap<String, String> tm = retrialService.getBranch(user.getUnitNo());
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("item", new CapAjaxFormResult(tm));
		result.set("itemOrder", new ArrayList<String>(tm.keySet()));
		return result;
	}
}