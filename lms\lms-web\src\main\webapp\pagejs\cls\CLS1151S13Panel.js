var GroupLoanBuildCaseInfo = {
    isInit: false,
    /**
     *頁面初始化的動作
     * */
    init: function(){
		//帶入下拉選單
        var dropDownObject = $("#CLS1151Form13").find("[itemType]");
		
        var dropDownKeyNames = [];
        dropDownObject.each(function(){
            dropDownKeyNames.push($(this).attr("itemType"));
        });
		
		var dropDownKeyValue = API.loadCombos(dropDownKeyNames);
		
		dropDownObject.each(function(){
            var $obj = $(this);
            var itemType = $obj.attr("itemType");
            if (itemType == 'L140M01L_toDoReason') {
                var format = $obj.attr("itemFormat") || "{value} - {key}";
                $obj.setItems({
                    space: $obj.attr("space") || true,
                    item: dropDownKeyValue[itemType],
                    format: format
                });
            }
			
			if (itemType == 'L140M01L_areaGrade') {
                var format = $obj.attr("itemFormat") || "{key}";
                $obj.setItems({
                    space: $obj.attr("space") || true,
                    item: dropDownKeyValue[itemType],
                    format: format
                });
            }
        });
		
		//整批房貸額度不再動用註記
		$("[name=notUsingMark]").attr('disabled', 'disabled');
    }
    
};

GroupLoanBuildCaseInfo.init();

