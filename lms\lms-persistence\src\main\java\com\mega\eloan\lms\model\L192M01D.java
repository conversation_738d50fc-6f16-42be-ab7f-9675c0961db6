package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/**
 * 房屋貸款查核事項檔 
 */
@Entity
@Table(uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
@EntityListeners({ DocumentModifyListener.class })
public class L192M01D extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 3928503981288489967L;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	@Column(length = 32, nullable = false, columnDefinition = "CHAR(32)")
	private String mainId;
	
	/**契約簽妥*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String ck1;

	/**對保*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String ck2;

	/**關係人查詢*/
	@Temporal(TemporalType.DATE)
	private Date ck3Date;
	
	/**房貸同意書*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String ck4;

	/**動用審核表*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String ck5;

	/**查遺失身份證*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String ck6;

	/**自訂欄位1*/
	@Column(length = 60, columnDefinition = "VARCHAR(60)")
	private String userItem1;

	/**自訂欄位查核1*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String userCk1;

	/**自訂欄位2*/
	@Column(length = 60, columnDefinition = "VARCHAR(60)")
	private String userItem2;

	/**自訂欄位查核2*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String userCk2;

	/**自訂欄位3*/
	@Column(length = 60, columnDefinition = "VARCHAR(60)")
	private String userItem3;

	/**自訂欄位查核3*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String userCk3;

	/**建立人員號碼*/
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/**建立日期*/
	@Temporal(TemporalType.TIMESTAMP)
	private Date createTime;

	/**異動人員號碼*/
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/**異動日期*/
	@Temporal(TemporalType.TIMESTAMP)
	private Date updateTime;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	/** 契約簽妥 */
	public String getCk1() {
		return ck1;
	}

	/** 契約簽妥 */
	public void setCk1(String ck1) {
		this.ck1 = ck1;
	}

	/** 對保 */
	public String getCk2() {
		return ck2;
	}

	/** 對保 */
	public void setCk2(String ck2) {
		this.ck2 = ck2;
	}

	/** 關係人查詢 */
	public Date getCk3Date() {
		return ck3Date;
	}

	/** 關係人查詢 */
	public void setCk3Date(Date ck3Date) {
		this.ck3Date = ck3Date;
	}

	/** 房貸同意書 */
	public String getCk4() {
		return ck4;
	}

	/** 房貸同意書 */
	public void setCk4(String ck4) {
		this.ck4 = ck4;
	}

	/** 動用審核表 */
	public String getCk5() {
		return ck5;
	}

	/** 動用審核表 */
	public void setCk5(String ck5) {
		this.ck5 = ck5;
	}

	/** 查遺失身份證 */
	public String getCk6() {
		return ck6;
	}

	/** 查遺失身份證 */
	public void setCk6(String ck6) {
		this.ck6 = ck6;
	}

	/** 自訂欄位1 */
	public String getUserItem1() {
		return userItem1;
	}

	/** 自訂欄位1 */
	public void setUserItem1(String userItem1) {
		this.userItem1 = userItem1;
	}

	/** 自訂欄位查核1 */
	public String getUserCk1() {
		return userCk1;
	}

	/** 自訂欄位查核1 */
	public void setUserCk1(String userCk1) {
		this.userCk1 = userCk1;
	}

	/** 自訂欄位2 */
	public String getUserItem2() {
		return userItem2;
	}

	/** 自訂欄位2 */
	public void setUserItem2(String userItem2) {
		this.userItem2 = userItem2;
	}

	/** 自訂欄位查核2 */
	public String getUserCk2() {
		return userCk2;
	}

	/** 自訂欄位查核2 */
	public void setUserCk2(String userCk2) {
		this.userCk2 = userCk2;
	}

	/** 自訂欄位3 */
	public String getUserItem3() {
		return userItem3;
	}

	/** 自訂欄位3 */
	public void setUserItem3(String userItem3) {
		this.userItem3 = userItem3;
	}

	/** 自訂欄位查核3 */
	public String getUserCk3() {
		return userCk3;
	}

	/** 自訂欄位查核3 */
	public void setUserCk3(String userCk3) {
		this.userCk3 = userCk3;
	}

	/** 建立人員號碼 */
	public String getCreator() {
		return creator;
	}

	/** 建立人員號碼 */
	public void setCreator(String creator) {
		this.creator = creator;
	}

	/** 建立日期 */
	public Date getCreateTime() {
		return createTime;
	}

	/** 建立日期 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	/** 異動人員號碼 */
	public String getUpdater() {
		return updater;
	}

	/** 異動人員號碼 */
	public void setUpdater(String updater) {
		this.updater = updater;
	}

	/** 異動日期 */
	public Date getUpdateTime() {
		return updateTime;
	}

	/** 異動日期 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

}
