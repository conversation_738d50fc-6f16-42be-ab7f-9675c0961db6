#/*******************************************************************
#Source Id :          $Id: config.properties,v *******, 2012-12-21 14:48:05Z, Bang Lee$
#Source Date :        $Date: 2012/12/21 14:51:05$
#Source Version :     $Revision: 1$
#Last Modify Member : $Author: Bang Lee$
#*******************************************************************/

HOME_NAME=/DXL_Home

TODAY=********

dominoServerIp=***************

#USER_ID\ufffd\ufffd
usr_id=\u5f35\u742c\u96c5 07153/MEGABANK/MEGA

#USER_PASSWORD
usr_psw=********

#PORT
port=63148

#RichText Column Name
# column not used  richTxtColName=Aterms_Attch;
richTxtColName=

#ViewList Total \ufffd\ufffd
viewListSize=1

#LMS db2XML path:userPath\xml\LMS
XML_LMS=/xml/LMS

#CLS db2XML path:userPath\xml\CLS
XML_CLS=/xml/CLS

#LMSMain Form
LMSMainForm=FLMS110M01;FLMS120M01;FLMS130M01;FLMS110S01;FLMS120S01;FLMS140M01;FLMS740M01;FLMS140S01;FLMS140S02;FLMS140S03;FLMS140S04;FLMS140S05;FLMS140S06;FLMS140S07;

#CLSMain Form
CLSMainForm=FCLS102M05;FCLS102M06;FCLS104S12;FCLS104S03;FCLS104S04;FCLS104S11;FCLS106M01;FCLS106M02;FCLS107M01;FCLS107M02;FCLS108M01;FCLS108M02;FCLS109M01;FCLS109M02;FCLS109M03;FCLS110M01;FCLS110M02;FCLS111M01;FCLS111M02;FCLS113M01;FCLS113M02;FCLS115M01;FCLS115M03;FCLS118M01;FCLS118M02;FCLS119M01;FCLS119M02;FCLS140T12;FCLS140T21;FCLS715M01;FCLS715M02;FCLS114M01;FCLS114M02

#LMSView Name
LMSViewName=VLMSDB201B;VLMS14020;VLMS11020;VLMS14011

#LMSView Name
CLSViewName=VCLS00101;VCLS00113;VCLS09105;VCLS10105Z;VCLS10111;VCLS10112;VCLS10123;VCLS10130

# ******************************************************************************

#Vance local setting
#DC_ROOT=D:\\workspace\\mega\\lms\\lms\\lms-config\\src\\main\\resources\\lmsdc
#DC_ONLINE_FILE_ROOT=D:\\VANCE-T

#DEV
#DC_ROOT=/eloan/DEV/conf/lms/lmsdc
#DC_ONLINE_FILE_ROOT=/elnfs/LMS/lmsdc

#SIT
DC_ROOT=/eloan/conf/lms/lmsdc
DC_ONLINE_FILE_ROOT=/elnfs/LMS/lmsdc

CONF_PATH=/conf
htmlPath=/HTML
imagesPath=/IMAGES
filesPath=/FILES
textPath=/TEXT
rejectPath=/REJECT
dataPath=/data
clobPath=/CLOB

LMSViewListName=LMSViewList
CLSViewListName=CLSViewList
viewListExt=lst


