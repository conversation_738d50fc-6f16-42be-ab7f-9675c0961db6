/* 
 * L300M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L300M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L300M01A;

/** 覆審考核表主檔 **/
@Repository
public class L300M01ADaoImpl extends LMSJpaDao<L300M01A, String>
	implements L300M01ADao {

	@Override
	public L300M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L300M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}
			
	@Override
	public List<L300M01A> findByDocStatus(String docStatus){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L300M01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L300M01A> findByIndex01(String ownBrId, String branchId, Date bgnDate, Date endDate){
		ISearch search = createSearchTemplete();
		List<L300M01A> list = null;
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		if (branchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		if (bgnDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "bgnDate", bgnDate);
		if (endDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "endDate", endDate);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L300M01A> findByIndex02(String mainId){
		ISearch search = createSearchTemplete();
		List<L300M01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<L300M01A> findByIndex03(String ownBrId, String docStatus, Date bgnDate, Date endDate, String produceFlag){
		ISearch search = createSearchTemplete();
		List<L300M01A> list = null;
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		if (docStatus != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);
		if (bgnDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "bgnDate", bgnDate);
		if (endDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "endDate", endDate);
		if (produceFlag != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "produceFlag", produceFlag);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	/**
	 * J-112-0461 授信覆審考核表-編製中-下拉選項統計由每半年改為每季統計
	 */
	@Override
	public List<L300M01A> findL300m01a(String ownBrId, String branchId, Date bgnDate, Date endDate){
		ISearch search = createSearchTemplete();
		List<L300M01A> list = null;
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		if (branchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);		
		if (bgnDate != null && endDate != null) 
			search.addSearchModeParameters(SearchMode.LESS_EQUALS, "bgnDate", bgnDate );			
		if (endDate != null) 	
			search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "endDate", endDate );
		
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}