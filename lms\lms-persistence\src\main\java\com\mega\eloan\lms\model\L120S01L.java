/* 
 * L120S01L.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金相關查詢資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name="L120S01L", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo"}))
public class L120S01L extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 資料來源 **/
	@Column(name="DATASRC", length=60, columnDefinition="VARCHAR(60)")
	private String dataSrc;

	/** 
	 * 有無票信退補記錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="ECHKFLAG", length=1, columnDefinition="VARCHAR(1)")
	private String eChkFlag;

	/** 票信資料截止日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ECHKDDATE", columnDefinition="DATE")
	private Date eChkDDate;

	/** 票信查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ECHKQDATE", columnDefinition="DATE")
	private Date eChkQDate;

	/** 
	 * 有無聯徵逾催呆記錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="EJCICFLAG", length=1, columnDefinition="VARCHAR(1)")
	private String eJcicFlag;

	/** 聯徵資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="EJCICDDATE", columnDefinition="DATE")
	private Date eJcicDDate;

	/** 聯徵查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="EJCICQDATE", columnDefinition="DATE")
	private Date eJcicQDate;

	/** 
	 * 引進原舊案資料<p/>
	 * 100/12/08新增<br/>
	 *  Y/N<br/>
	 *  ※國內DBU/OBU才需填寫
	 */
	@Column(name="ISFROMOLD", length=1, columnDefinition="VARCHAR(1)")
	private String isFromOld;

	/** 
	 * 婉卻紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="ISQDATA1", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata1;

	/** 
	 * 本行利害關係人<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="ISQDATA2", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata2;

	/** 
	 * 金控利害關係人(44條)<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="ISQDATA3", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata3;

	/** 
	 * 金控利害關係人(45條)<p/>
	 * 100/12/08新增<br/>
	 *  1.有、2.無、3.N.A
	 */
	@Column(name="ISQDATA16", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata16;

	/** 
	 * 主從債務人(不含本次資料)<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="ISQDATA6", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata6;

	/** 
	 * 對同一自然人授信總餘額比率<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="ISQDATA5", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata5;

	/** 
	 * 歸戶(本行餘額為a-Loan資料、他行餘額為聯徵資料)<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="ISQDATA4", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata4;

	/** 
	 * 近一年內不含查詢當日非Z類被聯行查詢紀錄明細<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="ISQDATA14", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata14;

	/** 
	 * 黑名單<p/>
	 * 100/12/08新增<br/>
	 *  1.有、2.無、3.N.A<br/>
	 *  ※國內DBU/OBU才需填寫
	 */
	@Column(name="ISQDATA7", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata7;

	/** 
	 * 黑名單全型字英文名<p/>
	 * 100/12/08新增<br/>
	 *  ※國內DBU/OBU才需填寫
	 */
	@Column(name="ENAME", length=60, columnDefinition="VARCHAR(60)")
	private String eName;

	/** 
	 * 證券暨期貨違約交割紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="ISQDATA8", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata8;

	/** 
	 * 退票紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="ISQDATA9", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata9;

	/** 
	 * 拒絕往來紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="ISQDATA10", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata10;

	/** 
	 * 主債務逾期、催收、呆帳紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="ISQDATA11", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata11;

	/** 
	 * 信用卡強停紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="ISQDATA13", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata13;

	/** 
	 * 身分證補、換發紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="ISQDATA12", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata12;

	/** 
	 * 成年監護制度查詢紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	@Column(name="ISQDATA15", length=1, columnDefinition="VARCHAR(1)")
	private String isQdata15;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/**
	 * 本案「銀行法第33條之2、銀行法第33條之4」之情形
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@Size(max = 1)
	@Column(name = "MBRLT33", length = 1, columnDefinition = "CHAR(1)")
	private String mbRlt33;
	
	/** 說明 **/
	@Size(max = 1800)
	@Column(name = "MBRLTDSCR33", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String mbRltDscr33;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得資料來源 **/
	public String getDataSrc() {
		return this.dataSrc;
	}
	/** 設定資料來源 **/
	public void setDataSrc(String value) {
		this.dataSrc = value;
	}

	/** 
	 * 取得有無票信退補記錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getEChkFlag() {
		return this.eChkFlag;
	}
	/**
	 *  設定有無票信退補記錄<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setEChkFlag(String value) {
		this.eChkFlag = value;
	}

	/** 取得票信資料截止日 **/
	public Date getEChkDDate() {
		return this.eChkDDate;
	}
	/** 設定票信資料截止日 **/
	public void setEChkDDate(Date value) {
		this.eChkDDate = value;
	}

	/** 取得票信查詢日期 **/
	public Date getEChkQDate() {
		return this.eChkQDate;
	}
	/** 設定票信查詢日期 **/
	public void setEChkQDate(Date value) {
		this.eChkQDate = value;
	}

	/** 
	 * 取得有無聯徵逾催呆記錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getEJcicFlag() {
		return this.eJcicFlag;
	}
	/**
	 *  設定有無聯徵逾催呆記錄<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setEJcicFlag(String value) {
		this.eJcicFlag = value;
	}

	/** 取得聯徵資料日期 **/
	public Date getEJcicDDate() {
		return this.eJcicDDate;
	}
	/** 設定聯徵資料日期 **/
	public void setEJcicDDate(Date value) {
		this.eJcicDDate = value;
	}

	/** 取得聯徵查詢日期 **/
	public Date getEJcicQDate() {
		return this.eJcicQDate;
	}
	/** 設定聯徵查詢日期 **/
	public void setEJcicQDate(Date value) {
		this.eJcicQDate = value;
	}

	/** 
	 * 取得引進原舊案資料<p/>
	 * 100/12/08新增<br/>
	 *  Y/N<br/>
	 *  ※國內DBU/OBU才需填寫
	 */
	public String getIsFromOld() {
		return this.isFromOld;
	}
	/**
	 *  設定引進原舊案資料<p/>
	 *  100/12/08新增<br/>
	 *  Y/N<br/>
	 *  ※國內DBU/OBU才需填寫
	 **/
	public void setIsFromOld(String value) {
		this.isFromOld = value;
	}

	/** 
	 * 取得婉卻紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata1() {
		return this.isQdata1;
	}
	/**
	 *  設定婉卻紀錄<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata1(String value) {
		this.isQdata1 = value;
	}

	/** 
	 * 取得本行利害關係人<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata2() {
		return this.isQdata2;
	}
	/**
	 *  設定本行利害關係人<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata2(String value) {
		this.isQdata2 = value;
	}

	/** 
	 * 取得金控利害關係人(44條)<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata3() {
		return this.isQdata3;
	}
	/**
	 *  設定金控利害關係人(44條)<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata3(String value) {
		this.isQdata3 = value;
	}

	/** 
	 * 取得金控利害關係人(45條)<p/>
	 * 100/12/08新增<br/>
	 *  1.有、2.無、3.N.A
	 */
	public String getIsQdata16() {
		return this.isQdata16;
	}
	/**
	 *  設定金控利害關係人(45條)<p/>
	 *  100/12/08新增<br/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata16(String value) {
		this.isQdata16 = value;
	}

	/** 
	 * 取得主從債務人(不含本次資料)<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata6() {
		return this.isQdata6;
	}
	/**
	 *  設定主從債務人(不含本次資料)<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata6(String value) {
		this.isQdata6 = value;
	}

	/** 
	 * 取得對同一自然人授信總餘額比率<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata5() {
		return this.isQdata5;
	}
	/**
	 *  設定對同一自然人授信總餘額比率<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata5(String value) {
		this.isQdata5 = value;
	}

	/** 
	 * 取得歸戶(本行餘額為a-Loan資料、他行餘額為聯徵資料)<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata4() {
		return this.isQdata4;
	}
	/**
	 *  設定歸戶(本行餘額為a-Loan資料、他行餘額為聯徵資料)<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata4(String value) {
		this.isQdata4 = value;
	}

	/** 
	 * 取得近一年內不含查詢當日非Z類被聯行查詢紀錄明細<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata14() {
		return this.isQdata14;
	}
	/**
	 *  設定近一年內不含查詢當日非Z類被聯行查詢紀錄明細<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata14(String value) {
		this.isQdata14 = value;
	}

	/** 
	 * 取得黑名單<p/>
	 * 100/12/08新增<br/>
	 *  1.有、2.無、3.N.A<br/>
	 *  ※國內DBU/OBU才需填寫
	 */
	public String getIsQdata7() {
		return this.isQdata7;
	}
	/**
	 *  設定黑名單<p/>
	 *  100/12/08新增<br/>
	 *  1.有、2.無、3.N.A<br/>
	 *  ※國內DBU/OBU才需填寫
	 **/
	public void setIsQdata7(String value) {
		this.isQdata7 = value;
	}

	/** 
	 * 取得黑名單全型字英文名<p/>
	 * 100/12/08新增<br/>
	 *  ※國內DBU/OBU才需填寫
	 */
	public String getEName() {
		return this.eName;
	}
	/**
	 *  設定黑名單全型字英文名<p/>
	 *  100/12/08新增<br/>
	 *  ※國內DBU/OBU才需填寫
	 **/
	public void setEName(String value) {
		this.eName = value;
	}

	/** 
	 * 取得證券暨期貨違約交割紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata8() {
		return this.isQdata8;
	}
	/**
	 *  設定證券暨期貨違約交割紀錄<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata8(String value) {
		this.isQdata8 = value;
	}

	/** 
	 * 取得退票紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata9() {
		return this.isQdata9;
	}
	/**
	 *  設定退票紀錄<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata9(String value) {
		this.isQdata9 = value;
	}

	/** 
	 * 取得拒絕往來紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata10() {
		return this.isQdata10;
	}
	/**
	 *  設定拒絕往來紀錄<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata10(String value) {
		this.isQdata10 = value;
	}

	/** 
	 * 取得主債務逾期、催收、呆帳紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata11() {
		return this.isQdata11;
	}
	/**
	 *  設定主債務逾期、催收、呆帳紀錄<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata11(String value) {
		this.isQdata11 = value;
	}

	/** 
	 * 取得信用卡強停紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata13() {
		return this.isQdata13;
	}
	/**
	 *  設定信用卡強停紀錄<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata13(String value) {
		this.isQdata13 = value;
	}

	/** 
	 * 取得身分證補、換發紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata12() {
		return this.isQdata12;
	}
	/**
	 *  設定身分證補、換發紀錄<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata12(String value) {
		this.isQdata12 = value;
	}

	/** 
	 * 取得成年監護制度查詢紀錄<p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata15() {
		return this.isQdata15;
	}
	/**
	 *  設定成年監護制度查詢紀錄<p/>
	 *  1.有、2.無、3.N.A
	 **/
	public void setIsQdata15(String value) {
		this.isQdata15 = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
	
	/**
	 * 取得本案「銀行法第33條之2、銀行法第33條之4」之情形
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getMbRlt33() {
		return mbRlt33;
	}

	/**
	 * 設定本案「銀行法第33條之2、銀行法第33條之4」之情形
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public void setMbRlt33(String mbRlt33) {
		this.mbRlt33 = mbRlt33;
	}
	
	/** 取得說明 **/
	public String getMbRltDscr33() {
		return mbRltDscr33;
	}
	
	/** 設定說明 **/
	public void setMbRltDscr33(String mbRltDscr33) {
		this.mbRltDscr33 = mbRltDscr33;
	}
}
