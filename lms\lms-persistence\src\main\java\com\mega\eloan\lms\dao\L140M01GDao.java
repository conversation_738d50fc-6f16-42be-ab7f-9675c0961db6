/* 
 * L140M01GDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01G;

/** 額度利費率主檔 **/
public interface L140M01GDao extends IGenericDao<L140M01G> {

	L140M01G findByOid(String oid);
	
	List<L140M01G> findByMainId(String mainId);
	
	L140M01G findByUniqueKey(String mainId,Integer rateSeq,String rateType);

	List <L140M01G> findByMainIdAndRateSeq(String mainId, int rateSeq);
	
	L140M01G findByMainIdAndRatetype(String mainId,String rateType);
}