/* 
 * LMS7010Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service;

import java.util.List;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L800M01A;

/**
 * 常用主管資料 Service
 * 
 * @since 2011/9/29
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/10/29,Vector Lo,new
 *          </ul>
 */
public interface LMS7010Service extends AbstractService {

	// 常用主管資料
	L800M01A findL800m01aByOid(String oid);

	void deleteListL800m01a(String[] oidArray);

	void deleteL800m01a(String oid);

	List<L800M01A> findL800m01aByZhuGuan(String zhuGuan, String brno);
}
