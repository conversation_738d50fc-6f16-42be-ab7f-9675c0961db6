var dfd61 = new $.Deferred();
initAll.done(function(){

    /**  檔案上傳grid  */
    function gridfile(){
        $("#gridfile").iGrid({
            handler: "lms1605gridhandler",
            height: 120,
            autowidth: true,
            postData: {
                formAction: "queryfile",
                fieldId: "ctrDoc",
                mainId: $("#tabFormMainId").val()
            },
            rowNum: 15,
            multiselect: true,
            colModel: [{
                colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
                name: 'srcFileName',
                width: 120,
                align: "left",
                sortable: true,
                formatter: 'click',
                onclick: download
            }, {
                colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
                name: 'fileDesc',
                width: 140,
                align: "center",
                sortable: true
            }, {
                colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
                name: 'uploadTime',
                width: 140,
                align: "center",
                sortable: true
            }, {
                name: 'oid',
                hidden: true
            }]
        });
    }
    
    dfd61.done(gridfile);
    
    $("#lms140Tab06").click(function(){
        dfd61.resolve();
        $("#gridfile").jqGrid("setGridParam", {
            postData: {
                formAction: "queryfile",
                mainId: $("#tabFormMainId").val()
            },
            search: true
        }).trigger("reloadGrid");
        
        
        var headItem1 = $("input[name=headItem1]:checked").val();
        if ("Y" == headItem1) {
        	$("#page08isGutCut").show();
        } else {
        	$("#page08isGutCut").hide();
        }
    });
    
    /**  上傳檔案按鈕*/
    $("#uploadFile").click(function(){
        var limitFileSize = 3145728;
        MegaApi.uploadDialog({
            fieldId: "ctrDoc",
            fieldIdHtml: "size='30'",
            fileDescId: "fileDesc",
            fileDescHtml: "size='30' maxlength='30'",
            subTitle: i18n.def('insertfileSize', {
                'fileSize': (limitFileSize / 1048576).toFixed(2)
            }),
            limitSize: limitFileSize,
            width: 320,
            height: 190,
            data: {
                mainId: $("#tabFormMainId").val(),
                sysId: "LMS"
            },
            success: function(){
                $("#gridfile").trigger("reloadGrid");
            }
        });
    });
    
    /**  刪除檔案按鈕 */
    $("#deleteFile").click(function(){
        var select = $("#gridfile").getGridParam('selarrrow');
        if (select == "") {
        
            // TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var data = [];
                for (var i in select) {
                    data.push($("#gridfile").getRowData(select[i]).oid);
                }
                
                $.ajax({
                    handler: "lms1605m01formhandler",
                    data: {
                        formAction: "deleteUploadFile",
                        oids: data
                    },
                    success: function(obj){
                        $("#gridfile").trigger("reloadGrid");
                    }
                });
            }
        });
    });
    
    
    /**  檔案下載  */
    function download(cellvalue, options, data){
        $.capFileDownload({
            handler: "simplefiledwnhandler",
            data: {
                fileOid: data.oid
            }
        });
    }
    
    //J-113-0035  ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意承諾待追蹤ESG連結條款」的登錄機制
    var l140s12aGrid = $("#l140s12aGrid").iGrid({
		needPager : false,
		localFirst : true,
		handler : inits.ghandle,//queryAdcList
		height : "340",
		width : "100%",
		multiselect : true,
		sortname : 'seqNum',
		sortorder : 'asc|asc',
		postData: {
        	formAction: "queryL140s11aList",
            tabFormMainId: $("#tabFormMainId").val()
        },
		colModel : [ {
			colHeader : i18n.lms1405s02["L140S12A.seqNo"],//序號
			name : 'seqNum',
			width : '50px',
			sortable : true,
			align : "center"
			//onclick : branchBox
		},{
			colHeader : i18n.lms1405s02["L140S12A.type"],//類別
			name : 'esgType',
			width : '220px',
			sortable : true,
			align : "left",
		}, {
			colHeader : i18n.lms1405s02["L140S12A.contentText"],//內容
			name : 'contentText',
			width : '400px',
			sortable : true,
			align : "left"
		}, {
			colHeader : i18n.lms1405s02["L140S12A.esgType"],//ESG性質
			name : 'esgModel',
			width : '80px',
			sortable : true,
			align : "center"
		},{
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }],
		ondblClickRow : function(rowid) {
			var rowdata = $("#l140s12aGrid").getRowData(rowid);
			esgTraceAciton.openBox(null, null, rowdata);

		}
	});
    esgTraceAciton.initUI();
    
});
var esgTraceAciton = {
		initUI: function(){
			//console.log("esgTraceAciton.initui");
			if (inits.toreadOnly) {
                $("#btnESGNew").hide();
                $("#btnESGMod").hide();
                $("#btnESGDel").hide();
            }
		    // 區分新舊動撥提醒事項 / ESG追蹤分項是否啟用
		    $.ajax({
		    	handler: inits.fhandle,
		        action: "checkOldtoALoan",
		        data: {
		        	oid: responseJSON.oid, //L120M01A.oid
		        	mainId: $("#tabFormMainId").val() //L140M01A.mainId
		        },
		        success: function(obj){//有資料
		        	var isnew = false;
		        	var as400_on = false;
		        	if(!$.isEmptyObject(obj)){
		        		isnew = obj.isnew;
		        		as400_on = obj.as400_on;
		        	}
		        	if(isnew){
		        		$("tr.J-113-0035_check").hide();
		        	}
		        	if(!as400_on){//啟用ESG貸後管理分項
		        		$("button.AS400_ON").hide();
		        		$("button.AS400_ON").parents("span").hide()
		        	}
		        }
		    })
		    //esgType
		    var esgType = API.loadOrderCombosAsList("l140s12a_esgType")["l140s12a_esgType"];
		    $("#esgType").setItems({
				 size: "1",
			     item: convertItems(esgType),
				 clear : true,
				 itemType: 'checkbox',
				 disabled: inits.toreadOnly
			});
		    //ESG模板
		    var esgMsgEitem = API.loadCombos("l140s12a_esgModelE")["l140s12a_esgModelE"];
			$("#esgMsgE").setItems({
				 size: "1",
			     item: esgMsgEitem,
				 clear : true,
				 itemType: 'checkbox',
				 disabled: inits.toreadOnly
			});
			var esgMsgSitem = API.loadCombos("l140s12a_esgModelS")["l140s12a_esgModelS"];
			$("#esgMsgS").setItems({
				 size: "1",
			     item: esgMsgSitem,
				 clear : true,
				 itemType: 'checkbox',
				 disabled: inits.toreadOnly
			});
			var esgMsgGitem = API.loadCombos("l140s12a_esgModelG")["l140s12a_esgModelG"];
			$("#esgMsgG").setItems({
				 size: "1",
			     item: esgMsgGitem,
				 clear : true,
				 itemType: 'checkbox',
				 disabled: inits.toreadOnly
			});
			//載入完成後，區分esgType type2, type3 選項
			var model12 = API.loadOrderCombosAsList("l140s12a_esgModelType12")["l140s12a_esgModelType12"];//永續績效
			var model13 = API.loadOrderCombosAsList("l140s12a_esgModelType13")["l140s12a_esgModelType13"];//其他
			if(!$.isEmptyObject(model12)){
				var listvalue = DOMPurify.sanitize(model12[0].value).split('|');
				$("input[type='checkbox'].checkbox_esgMsgAll").filter(function(){
				    return listvalue.indexOf($(this).val()) > -1;
				}).parent().addClass("label_checkbox_esgMsg_type2");
			}
			if(!$.isEmptyObject(model13)){
				var listvalue = DOMPurify.sanitize(model13[0].value).split('|');
				$("input[type='checkbox'].checkbox_esgMsgAll").filter(function(){
				    return listvalue.indexOf($(this).val()) > -1;
				}).parent().addClass("label_checkbox_esgMsg_type3");
			}
			
			$("input[name='esgType']").change(function(){
				var esgType2 = $("input[name='esgType'][value='12']:checked").length;
				var esgType3 = $("input[name='esgType'][value='13']:checked").length;
				if(esgType2 > 0 || esgType3 > 0){
					//永續績效連結授信條件(跟利率計價或手續費減免等優惠措施有關)、其他ESG條件(非以上相關)
					var esgGreenSpendType = $("#esgSustainLoanType").val();
					if (esgGreenSpendType != "") {//"E|S|G"
						//永續績效連結授信條件(跟利率計價或手續費減免等優惠措施有關)
						if(esgType2 == 0){
							$(".label_checkbox_esgMsg_type2").hide();
							$(".label_checkbox_esgMsg_type2").find("input[type='checkbox']").prop("checked", false);
						}else{
							$(".label_checkbox_esgMsg_type2").show();
						}
						//其他ESG條件(非以上相關)
						if(esgType3 == 0){
							$(".label_checkbox_esgMsg_type3").hide();
							$(".label_checkbox_esgMsg_type3").find("input[type='checkbox']").prop("checked", false);
						}else{
							$(".label_checkbox_esgMsg_type3").show();
						}
						if(esgGreenSpendType.indexOf("E") >=0){
							$("#td_esgMsgE").show();
						}else{
							$("#td_esgMsgE").hide();
						}
						if(esgGreenSpendType.indexOf("S") >=0){
							$("#td_esgMsgS").show();
						}else{
							$("#td_esgMsgS").hide();
						}
						if(esgGreenSpendType.indexOf("G") >=0){
							$("#td_esgMsgG").show();
						}else{
							$("#td_esgMsgG").hide();
						}
						$(".tr_esgMsg_type").show();
						$(".tr_esgMsgImporBtn").show();
					}
					//起始追蹤日 只能選其他
					$("input[type='radio'][name='traceCondition'][value='3']").prop("checked", true);
					$("input[type='radio'][name='traceCondition'][value!='3']").prop("disabled", true);
					$("input[type='radio'][name='traceCondition']").trigger("change");
				}else{//隱藏ESG模板,取消勾選
					$(".tr_esgMsg_type").hide();
					$(".tr_esgMsgImporBtn").hide();
					$(".checkbox_esgMsgAll").prop("checked", false);
					$("input[type='radio'][name='traceCondition'][value!='3']").prop("disabled", false);
					$("input[type='radio'][name='traceCondition']").trigger("change");
				}
			});
			
			$("input[type='radio'][name='traceCondition']").change(function(){
				if($("input[type='radio'][name='traceCondition'][value='3']:checked").length > 0){
					$(".td_traceProfiling").show();
				}else{
					//隱藏追蹤週期,取消勾選
					$(".td_traceProfiling").hide();
					$("input[type='radio'][name='traceProfiling']").prop("checked", false);
					$("#traceProfilingMonth").val("");
				}
			});
			
			$("#btn_ESG").click(function() {
				//明細初始化
				$("#divESG").thickbox({
		            title: i18n.lms1405s02["L140S12A.memoEsgTerms"],//登錄應注意/承諾/待追蹤/ESG連結條款
		            align: "center",
		            valign: "bottom",
		            width: 800,
		            height: 510,
		            buttons: {
		                "close": function(){
		                    $.thickbox.close();
		                }
		            }
		        });
				//gridreload
				$("#l140s12aGrid").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
		            postData : {
		                formAction: "queryL140s12aList",
		                tabFormMainId: $("#tabFormMainId").val()
		            },
		            search : true
		        }).trigger("reloadGrid");
			});
			
			$("#btmImportEsgMsg").click(function() {
				//esg模板勾選項目引入內文
				if($(".checkbox_esgMsgAll:checked").length > 0){
					$(".checkbox_esgMsgAll:checked").each(function(){
					    $("#contentText").val(DOMPurify.sanitize($("#contentText").val()) + "\n" + DOMPurify.sanitize($(this).parent().text().trim()));
					});
					 $("#contentText").val(DOMPurify.sanitize($("#contentText").val()) + "\n" + DOMPurify.sanitize(i18n.lms1405s02["L140S12A.esgModelMemo_1"]));
					 $("#contentText").val(DOMPurify.sanitize($("#contentText").val()) + "\n" + DOMPurify.sanitize(i18n.lms1405s02["L140S12A.esgModelMemo_2"]));
				}

			});
			// 
			$("#btnESGNew").click(function() {
				esgTraceAciton.openBox(null, "add", null);
			});
			$("#btnESGMod").click(function() {
				var rowid = $("#l140s12aGrid").getGridParam('selrow');
				var rowdata = $("#l140s12aGrid").getRowData(rowid);
				esgTraceAciton.openBox(null, null, rowdata);
			});
			$("#btnESGDel").click(function() {
				esgTraceAciton.deleteL140S12A();
			});
		},
		/**
		 *  明細視窗
		 */
		openBox: function(cellvalue, options, l140s12a_rowdata){
			//console.log(l140s12a_rowdata);
			// 當新增時 data 會是 null
	        var hasData = false;
	        if (typeof l140s12a_rowdata != 'undefined' && l140s12a_rowdata != null && l140s12a_rowdata != 0) {
	            hasData = true;
	        }
		    var l140s12aOid = hasData ? l140s12a_rowdata.oid : "";
		    //console.log(l140s12aOid);
	        $.ajax({
		    	handler: inits.fhandle,
		        action: "queryL140s12aDetail",
		        data: {
		        	l140s12aOid: l140s12aOid
		        },
		        success: function(obj){//有資料帶入
		        	if(!$.isEmptyObject(obj)){//checkbox  1|2|3|9
		        		$("#l140s12aFormDetail").injectData(obj);
		        		if(!$.isEmptyObject(obj.esgType)){
		        			var esgTypeArr = obj.esgType.split("|");
		        			$.each(esgTypeArr, function(i,v){
		        			    $("#l140s12aFormDetail").find("input[name='esgType'][value='" + DOMPurify.sanitize(v) + "']").prop("checked", true);
		        			});
		        		}
		        		if(!$.isEmptyObject(obj.esgType)){//checkbox  S1|S2|E1|G1
		        			var esgModelArr = obj.esgModel.split("|");
		        			$.each(esgModelArr, function(i,v){
		        			    $("#l140s12aFormDetail").find("input.checkbox_esgMsgAll[value='" + DOMPurify.sanitize(v) + "']").prop("checked", true);
		        			});
		        		}
		        		if(!$.isEmptyObject(obj.traceCondition)){//radio
		        			$("#l140s12aFormDetail").find("input[name='traceCondition'][value='" + DOMPurify.sanitize(obj.traceCondition) + "']").prop("checked", true);
		        		}
		        		if(!$.isEmptyObject(obj.traceProfiling)){//radio
		        			$("#l140s12aFormDetail").find("input[name='traceProfiling'][value='" + DOMPurify.sanitize(obj.traceProfiling) + "']").prop("checked", true);
		        		}
		        	}else{
		        		$("input[type='checkbox'][name='esgType']").prop("checked", false);
		        		$("input[type='radio'][name='traceCondition']").prop("checked", false);
		        		$("input[type='radio'][name='traceProfiling']").prop("checked", false);
		        		$("input.checkbox_esgMsgAll").prop("checked", false);
		        		$("#traceProfilingMonth").val("");
		        		$("#contentText").val("");
		        	}
		         	
		        }
		    }).done(function(){
				$("input[type='checkbox'][name='esgType']").trigger("change");
				$("input[type='radio'][name='traceCondition']").trigger("change");
				
				var buttonsF = {
	            	"sure": function(){
	            		esgTraceAciton.saveL140S12A(l140s12aOid);
	                 },
	                "cancel": function(){
	                    $.thickbox.close();
	                }
	            };
	            if (inits.toreadOnly) {
	                delete buttonsF.sure;
	                $("#l140s12aFormDetail").find("textarea").prop("readonly", true);
	            }
	            
				$("#divESGDetail").thickbox({
					title: i18n.lms1405s02["L140S12A.memoEsgTermsDetail"],//應注意/承諾/待追蹤/ESG連結條款明細
		            align: "center",
		            valign: "bottom",
		            width: 800,
		            height: 800,
		            buttons: buttonsF
				});
				
				
		    });
		},
		checkL140S12Adata: function(){
			var errmsg = "";
			if($("input[type='radio'][name='traceCondition']:checked").length == 0){
				errmsg += i18n.lms1405s02["L140S12A.errorMsg01"];
				//return CommonAPI.showMessage(i18n.lms1405s02["L140S12A.errorMsg01"]);
			}
			
			if($("input[type='radio'][name='traceCondition'][value='3']:checked").length > 0){
				if($("input[type='radio'][name='traceProfiling']:checked").length == 0){
					errmsg += i18n.lms1405s02["L140S12A.errorMsg02"];
				}
				if($("input[type='radio'][name='traceProfiling'][value='2']:checked").length > 0 && $("#traceProfilingMonth").val() == ""){
					errmsg += i18n.lms1405s02["L140S12A.errorMsg03"];
				}
			}
			return errmsg;
		},
		/**儲存L140S12A
	     *
	     */
		saveL140S12A: function(l140s12aOid){
			//儲存L140S12A
			var errorMsg = esgTraceAciton.checkL140S12Adata();
			if(errorMsg != ""){
				return CommonAPI.showMessage(errorMsg);
			}else{
				 // confirm=是否寫入主文
                CommonAPI.confirmMessage(i18n.lms1405s02["L140S12A.isImportContext"], function(b){//是否將追蹤內容寫入主文
                    if (b) {
                    	//將內容寫入最外層的"其他敘做條件textarea"
                    	var content = $("#contentText").val().replace(/#|\n/g, "<BR>");
                    	$('#itemDscr4').val(DOMPurify.sanitize($('#itemDscr4').val() + "<p>" + content + "</p>"));
                    }else{
                    	
                    }
                  //儲存L140S12A
                    $.ajax({
    	                handler: inits.fhandle,
    	                data: {
    	                    formAction: "saveL140S12A",
    	                    oid: l140s12aOid,
    	                    caseMainId: responseJSON.mainId,
    	                    mainId: $("#tabFormMainId").val(),
    	                    cntrNo: $("#cntrNo").val(),
    	                    esgType: esgTraceAciton.formatEsgCheckBoxData("esgType"),
    	                    esgMsgE: esgTraceAciton.formatEsgCheckBoxData("esgMsgE"),
    	                    esgMsgS: esgTraceAciton.formatEsgCheckBoxData("esgMsgS"),
    	                    esgMsgG: esgTraceAciton.formatEsgCheckBoxData("esgMsgG"),
    	                    traceCondition: esgTraceAciton.formatEsgCheckBoxData("traceCondition"),
    	                    traceProfiling: esgTraceAciton.formatEsgCheckBoxData("traceProfiling"),
    	                    traceProfilingMonth: $("#traceProfilingMonth").val(),
    	                    contentText: $("#contentText").val()
    	                    //l140s12aForm: JSON.stringify($("#l140s12aFormDetail").serializeData()),//備用
    	                },
    	                success: function(obj){
    	                    //gridreload
    	                    $("#l140s12aGrid").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
    	                        postData : {
    	                            formAction: "queryL140s12aList",
    	                            tabFormMainId: $("#tabFormMainId").val()
    	                        },
    	                        search : true
    	                    }).trigger("reloadGrid");
    	                    
    	                    $.thickbox.close();//關閉
    	                }
    	            });
                });
				
			}
       	 	
		},
		/**
		 * 修改L140S12A
		 */
		modifyL140S12A: function(){
			var row = $("#l140s12aGrid").getGridParam('selrow');
			if (!row) {
		    	return CommonAPI.showMessage(i18n.def["action_002"]);
		    }
			var data = $("#l140s12aGrid").getRowData(row);
			esgTraceAciton.openBox(null, null, rowdata);
		},
		/**
		 * 刪除L140S12A
		 */
		deleteL140S12A: function(){
	        //var row = $("#l140s12aGrid").getGridParam('selrow');
	        var rows = $("#l140s12aGrid").getGridParam('selarrrow');
	    	var data = [];
	        if (!rows) {
	            return CommonAPI.showMessage(i18n.def["action_002"]);
	        }
	        else {
	            // confirmDelete=是否確定刪除?
	            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
	                if (b) {
	                    //var data = $("#l140s12aGrid").getRowData(row);
	                    for (var i in rows) {
	                        data.push($("#l140s12aGrid").getRowData(rows[i]).oid);
	                    }
	                    //var oid = data.oid;
	                    $.ajax({
	                        type: "POST",
	                        handler: inits.fhandle,
	                        data: {
	                            formAction: "deleteL140s12a",
	                            oids: data,
	                            tabFormMainId: $("#tabFormMainId").val()
	                        },
	                        success: function(responseData){
	                        	$("#l140s12aGrid").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
	                                postData : {
	                                    formAction: "queryL140s12aList",
	                                    tabFormMainId: $("#tabFormMainId").val()
	                                },
	                                search : true
	                            }).trigger("reloadGrid");
	                        }
	                    });
	                }
	            });
	        }
		},
		/**
	     * 取得esgcheckbox值
	     * return XXX|XXX
	     */
	    formatEsgCheckBoxData: function(objName){
	        var data = [];
	        $("#l140s12aFormDetail").find("input[name='"+ DOMPurify.sanitize(objName) +"']:checked").each(function(v, k){
	            data.push($(k).val());
	        });
	        return data.join("|");
	    }
}