/* 
 *LMS9091V00Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;

/**
 * <pre>
 * 一段式總費用年百分率
 * </pre>
 * 
 * @since 2012/11/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/01,<PERSON>,new
 *          </ul>
 */
@Controller@RequestMapping(path = "/fms/lms9091v00")
public class LMS9091V00Page extends AbstractEloanInnerView {

	public LMS9091V00Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {

		// UPGRADE: 後續沒有用到就可以刪掉
		// add(new Label("_buttonPanel"));

	}// ;

	public String[] getJavascriptPath() {

		return new String[] { "pagejs/fms/LMS9091V00Page.js" };
	}
}
