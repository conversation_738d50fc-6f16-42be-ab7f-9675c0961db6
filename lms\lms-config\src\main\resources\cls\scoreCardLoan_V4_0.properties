#=====================================================
# \u8a55\u7b49\u53c3\u6578\u8a2d\u5b9a \u7248\u672c[varVer] A[varA] B[varB] \u5e38\u6578\u9805[varC]
#=====================================================
#varVer=4.0
varVer=4.0
slope=0.11
interCept=-5.06

#===============================================================================
#\u8aaa\u660e\uff1a\u4ee5","\u70ba\u5206\u9694\u7b26\u865f[0]\u5206\u6578\u3001[1]\u8a55\u7b49\u516c\u5f0f(javascript)\u3001[3]\u70ba\u8a55\u7b49\u53c3\u6578,\u5206\u9694\u7b26\u865f\u70ba";"
#===============================================================================
#--------------------------------------------------------------
# \u5c08\u6848\u4fe1\u8cb8(\u975e\u5718\u9ad4)\u8a55\u7b49\u9805\u76ee(\u57fa\u672c)
#--------------------------------------------------------------

#\u5b78\u6b77[education]
cardLoanBase_V4_0.screducation.01= 25.44, /{0}/.test("Null"),education
cardLoanBase_V4_0.screducation.02= 13.52, /{0}/.test("***********.08"),education
cardLoanBase_V4_0.screducation.03= 20.88, /{0}/.test("05.06.07"),education


#\u8fd112\u500b\u6708\u6388\u4fe1\u5e33\u6236\u7e73\u6b3e\u72c0\u6cc1\u51fa\u73fe 0 \u7684\u7e3d\u6b21\u6578\uff0c\u4e0d\u542b\u672c\u884c[P1]
cardLoanBase_V4_0.scrP01.01= 18.66, /{0}/.test("Null"), itemP01
cardLoanBase_V4_0.scrP01.02= 23.49, {0} < 1, itemP01
cardLoanBase_V4_0.scrP01.03= 17.42, {0} >= 1 && {0} < 37, itemP01
cardLoanBase_V4_0.scrP01.04= 8.38,  {0} >= 37 , itemP01

#\u500b\u4eba\u8ca0\u50b5\u6bd4%[nochkItemDrate]
cardLoanBase_V4_0.noscrItemDrate.01=24.74, {0} < 25, nochkItemDrate
cardLoanBase_V4_0.noscrItemDrate.02=19.52, {0} >= 25 && {0} < 55, nochkItemDrate
cardLoanBase_V4_0.noscrItemDrate.03=14.20, {0} >= 55 && {0} < 105, nochkItemDrate
cardLoanBase_V4_0.noscrItemDrate.04=12.17, {0} >= 105, nochkItemDrate
cardLoanBase_V4_0.noscrItemDrate.05=12.17, /{0}/.test("Null"), nochkItemDrate

#\u76ee\u524d\u6709\u64d4\u4fdd\u9918\u984d ID \u6b78\u6236[loanBalSByid]
cardLoanBase_V4_0.scrLoanBalSByid.01=13.76, /{0}/.test("Null"), loanBalSByid
cardLoanBase_V4_0.scrLoanBalSByid.02=24.77, {0} < 500000, loanBalSByid
cardLoanBase_V4_0.scrLoanBalSByid.03=21.57, {0} >= 500000 && {0} < 9500000, loanBalSByid
cardLoanBase_V4_0.scrLoanBalSByid.04=11.79, {0} >= 9500000, loanBalSByid


#\u8fd112\u500b\u6708\u4fe1\u7528\u5361(\u6bcf\u7b46)\u5faa\u74b0\u4fe1\u7528\u5e73\u5747\u4f7f\u7528\u7387 [MaxR01]
cardLoanBase_V4_0.scrMaxR01.01= 13.11,/{0}/.test("Null"),itemMaxR01
cardLoanBase_V4_0.scrMaxR01.02= 20.66, {0} == 0, itemMaxR01
cardLoanBase_V4_0.scrMaxR01.03= 12.65, {0} < 0.5 && {0} != 0, itemMaxR01
cardLoanBase_V4_0.scrMaxR01.04= 8.06, {0} >= 0.5, itemMaxR01


#--------------------------------------------------------------
# \u5c08\u6848\u4fe1\u8cb8(\u975e\u5718\u9ad4)\u8a55\u7b49\u9805\u76ee(\u7b49\u7d1a)
#--------------------------------------------------------------
#2.6\u7248
#cardLoanGrade.level.01=1, {0} > 125.5, scrNum13
#cardLoanGrade.level.02=2, {0} > 121.8 && {0} <= 125.5, scrNum13
#cardLoanGrade.level.03=3, {0} > 118.2 && {0} <= 121.8, scrNum13
#cardLoanGrade.level.04=4, {0} > 114.6 && {0} <= 118.2, scrNum13
#cardLoanGrade.level.05=5, {0} > 111.0 && {0} <= 114.6, scrNum13
#cardLoanGrade.level.06=6, {0} > 107.3 && {0} <= 111.0, scrNum13
#cardLoanGrade.level.07=7, {0} > 103.7 && {0} <= 107.3, scrNum13
#cardLoanGrade.level.08=8, {0} > 100.0 && {0} <= 103.7, scrNum13
#cardLoanGrade.level.09=9, {0} > 96.4 && {0} <= 100.0, scrNum13
#cardLoanGrade.level.10=10, {0} > 92.7 && {0} <= 96.4, scrNum13
#cardLoanGrade.level.11=11, {0} > 88.9 && {0} <= 92.7, scrNum13
#cardLoanGrade.level.12=12, {0} > 85.2 && {0} <= 88.9, scrNum13
#cardLoanGrade.level.13=13, {0} > 81.3 && {0} <= 85.2, scrNum13
#cardLoanGrade.level.14=14, {0} > 77.3 && {0} <= 81.3, scrNum13
#cardLoanGrade.level.15=15, {0} <= 77.3, scrNum13

#5.1\u7248
#cardLoanGrade.level.01=1, {0} > 126.5, scrNum13
#cardLoanGrade.level.02=2, {0} > 123.6 && {0} <= 126.5, scrNum13
#cardLoanGrade.level.03=3, {0} > 120.7 && {0} <= 123.6, scrNum13
#cardLoanGrade.level.04=4, {0} > 117.8 && {0} <= 120.7, scrNum13
#cardLoanGrade.level.05=5, {0} > 114.9 && {0} <= 117.8, scrNum13
#cardLoanGrade.level.06=6, {0} > 112.0 && {0} <= 114.9, scrNum13
#cardLoanGrade.level.07=7, {0} > 109.1 && {0} <= 112.0, scrNum13
#cardLoanGrade.level.08=8, {0} > 106.1 && {0} <= 109.1, scrNum13
#cardLoanGrade.level.09=9, {0} > 103.2 && {0} <= 106.1, scrNum13
#cardLoanGrade.level.10=10, {0} > 100.3 && {0} <= 103.2, scrNum13
#cardLoanGrade.level.11=11, {0} > 97.3 && {0} <= 100.3, scrNum13
#cardLoanGrade.level.12=12, {0} > 94.2 && {0} <= 97.3, scrNum13
#cardLoanGrade.level.13=13, {0} > 91.2 && {0} <= 94.2, scrNum13
#cardLoanGrade.level.14=14, {0} > 87.9 && {0} <= 91.2, scrNum13
#cardLoanGrade.level.15=15, {0} <= 87.9, scrNum13


#0.75\u7248
cardLoanGrade.level.01=1, {0} >= 113.3, scrNum13
cardLoanGrade.level.02=2, {0} >= 109.6 && {0} <= 113.2, scrNum13
cardLoanGrade.level.03=3, {0} >= 105.9 && {0} <= 109.5, scrNum13
cardLoanGrade.level.04=4, {0} >= 102.2 && {0} <= 105.8, scrNum13
cardLoanGrade.level.05=5, {0} >= 98.5 && {0} <= 102.1, scrNum13
cardLoanGrade.level.06=6, {0} >= 94.8 && {0} <= 98.4, scrNum13
cardLoanGrade.level.07=7, {0} >= 91.1 && {0} <= 94.7, scrNum13
cardLoanGrade.level.08=8, {0} >= 87.4 && {0} <= 91.0, scrNum13
cardLoanGrade.level.09=9, {0} >= 83.7 && {0} <= 87.3, scrNum13
cardLoanGrade.level.10=10, {0} >= 79.9 && {0} <= 83.6, scrNum13
cardLoanGrade.level.11=11, {0} >= 76.1 && {0} <= 79.8, scrNum13
cardLoanGrade.level.12=12, {0} >= 72.3 && {0} <= 76.0, scrNum13
cardLoanGrade.level.13=13, {0} >= 68.3 && {0} <= 72.2, scrNum13
cardLoanGrade.level.14=14, {0} >= 64.2 && {0} <= 68.2, scrNum13
cardLoanGrade.level.15=15, {0} <= 64.1, scrNum13

#--------------------------------------------------------------
# \u5c08\u6848\u4fe1\u8cb8(\u975e\u5718\u9ad4)\u8a55\u7b49\u9805\u76ee(\u9055\u7d04\u6a5f\u7387)
#--------------------------------------------------------------
#dr_1YR_S=\u9055\u7d04\u6a5f\u7387(\u9810\u4f301\u5e74\u671f)-\u77ed
cardLoanDR.dr_1YR_S.01=0.05, /{0}/.test("1"), grade3
cardLoanDR.dr_1YR_S.02=0.08, /{0}/.test("2"), grade3
cardLoanDR.dr_1YR_S.03=0.11, /{0}/.test("3"), grade3
cardLoanDR.dr_1YR_S.04=0.17, /{0}/.test("4"), grade3
cardLoanDR.dr_1YR_S.05=0.25, /{0}/.test("5"), grade3
cardLoanDR.dr_1YR_S.06=0.38, /{0}/.test("6"), grade3
cardLoanDR.dr_1YR_S.07=0.57, /{0}/.test("7"), grade3
cardLoanDR.dr_1YR_S.08=0.85, /{0}/.test("8"), grade3
cardLoanDR.dr_1YR_S.09=1.28, /{0}/.test("9"), grade3
cardLoanDR.dr_1YR_S.10=1.92, /{0}/.test("10"), grade3
cardLoanDR.dr_1YR_S.11=2.88, /{0}/.test("11"), grade3
cardLoanDR.dr_1YR_S.12=4.32, /{0}/.test("12"), grade3
cardLoanDR.dr_1YR_S.13=6.49, /{0}/.test("13"), grade3
cardLoanDR.dr_1YR_S.14=9.73, /{0}/.test("14"), grade3
cardLoanDR.dr_1YR_S.15=14.6, /{0}/.test("15"), grade3
#dr_1YR_L=\u9055\u7d04\u6a5f\u7387(\u9810\u4f301\u5e74\u671f)-\u4e2d\u9577
cardLoanDR.dr_1YR_L.01=0.05, /{0}/.test("1"), grade3
cardLoanDR.dr_1YR_L.02=0.08, /{0}/.test("2"), grade3
cardLoanDR.dr_1YR_L.03=0.11, /{0}/.test("3"), grade3
cardLoanDR.dr_1YR_L.04=0.17, /{0}/.test("4"), grade3
cardLoanDR.dr_1YR_L.05=0.25, /{0}/.test("5"), grade3
cardLoanDR.dr_1YR_L.06=0.38, /{0}/.test("6"), grade3
cardLoanDR.dr_1YR_L.07=0.57, /{0}/.test("7"), grade3
cardLoanDR.dr_1YR_L.08=0.85, /{0}/.test("8"), grade3
cardLoanDR.dr_1YR_L.09=1.28, /{0}/.test("9"), grade3
cardLoanDR.dr_1YR_L.10=1.92, /{0}/.test("10"), grade3
cardLoanDR.dr_1YR_L.11=2.88, /{0}/.test("11"), grade3
cardLoanDR.dr_1YR_L.12=4.32, /{0}/.test("12"), grade3
cardLoanDR.dr_1YR_L.13=6.49, /{0}/.test("13"), grade3
cardLoanDR.dr_1YR_L.14=9.73, /{0}/.test("14"), grade3
cardLoanDR.dr_1YR_L.15=14.6, /{0}/.test("15"), grade3
