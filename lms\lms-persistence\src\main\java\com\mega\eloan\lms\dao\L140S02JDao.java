/* 
 * L140S02JDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S02J;

/** 外勞貸款檔 **/
public interface L140S02JDao extends IGenericDao<L140S02J> {

	L140S02J findByOid(String oid);

	List<L140S02J> findByMainId(String mainId);

	L140S02J findByUniqueKey(String mainId, Integer seq);

	List<L140S02J> findByIndex01(String mainId, Integer seq);
}