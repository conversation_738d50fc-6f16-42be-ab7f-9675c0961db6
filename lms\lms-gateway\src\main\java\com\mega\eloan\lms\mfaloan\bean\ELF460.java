package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** ACH手續費紀錄檔 **/
public class ELF460 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 記帳日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF460_DATE", length = 10, columnDefinition = "DATE", nullable=false,unique = true)
	private Date elf460_date;

	/** 發動分行 **/
	@Column(name="ELF460_BRN", columnDefinition="CHAR(3)", nullable=false)
	private String elf460_brn;

	/** ACH交易代號 **/
	@Column(name="ELF460_TXID", columnDefinition="CHAR(3)", nullable=false)
	private String elf460_txId;

	/** 類別 **/
	@Column(name="ELF460_TYPE", columnDefinition="CHAR(2)", nullable=false)
	private String elf460_type;

	/** 契約編號/放款帳號 **/
	@Column(name="ELF460_CNTRNO_SEQ", length=16, columnDefinition="CHAR(16)", nullable=false,unique = true)
	private String elf460_cntrNo_Seq;

	/** 收受行代號 **/
	@Column(name="ELF460_RBANK", columnDefinition="CHAR(7)")
	private String elf460_rBank;

	/** 收受者帳號  **/
	@Column(name="ELF460_RCLNO", columnDefinition="CHAR(16)")
	private String elf460_rclNo;

	/** 資料來源  **/
	@Column(name="ELF460_SOURCE", columnDefinition="CHAR(10)")
	private String elf460_source;

	/** 是否已出帳  **/
	@Column(name="ELF460_FLAG", columnDefinition="CHAR(1)")
	private String elf460_flag;

	/** 資料日期  **/
	@Column(name="ELF460_TMESTAMP", columnDefinition="TIMESTAMP")
	private Timestamp elf460_tmeStamp;

	public Date getElf460_date() {
		return elf460_date;
	}

	public void setElf460_date(Date elf460_date) {
		this.elf460_date = elf460_date;
	}

	public String getElf460_brn() {
		return elf460_brn;
	}

	public void setElf460_brn(String elf460_brn) {
		this.elf460_brn = elf460_brn;
	}

	public String getElf460_txId() {
		return elf460_txId;
	}

	public void setElf460_txId(String elf460_txId) {
		this.elf460_txId = elf460_txId;
	}

	public String getElf460_type() {
		return elf460_type;
	}

	public void setElf460_type(String elf460_type) {
		this.elf460_type = elf460_type;
	}

	public String getElf460_cntrNo_Seq() {
		return elf460_cntrNo_Seq;
	}

	public void setElf460_cntrNo_Seq(String elf460_cntrNo_Seq) {
		this.elf460_cntrNo_Seq = elf460_cntrNo_Seq;
	}

	public String getElf460_rBank() {
		return elf460_rBank;
	}

	public void setElf460_rBank(String elf460_rBank) {
		this.elf460_rBank = elf460_rBank;
	}

	public String getElf460_rclNo() {
		return elf460_rclNo;
	}

	public void setElf460_rclNo(String elf460_rclNo) {
		this.elf460_rclNo = elf460_rclNo;
	}

	public String getElf460_source() {
		return elf460_source;
	}

	public void setElf460_source(String elf460_source) {
		this.elf460_source = elf460_source;
	}

	public String getElf460_flag() {
		return elf460_flag;
	}

	public void setElf460_flag(String elf460_flag) {
		this.elf460_flag = elf460_flag;
	}

	public Timestamp getElf460_tmeStamp() {
		return elf460_tmeStamp;
	}

	public void setElf460_tmeStamp(Timestamp elf460_tmeStamp) {
		this.elf460_tmeStamp = elf460_tmeStamp;
	}
}
