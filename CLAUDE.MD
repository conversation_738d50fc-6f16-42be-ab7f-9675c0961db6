
# Wicket 升級至 Thymeleaf+Java 8 指南
本文件為 Mega eLoan LMS 系統從 Wicket 框架升級至 Spring Boot、Thymeleaf 及 Java 8 的現代化技術棧所涉及的關鍵變更、挑戰與解決方案的綜合摘要。

## 1. 系統架構演進

### 1.1. 原始系統架構 (升級前)
* **後端**: Java 7 + Spring Framework (舊版)
* **前端**: Apache Wicket + jQuery 1.5.2
* **資料存取**: OpenJPA
* **前端資源管理**: JAWR (JavaScript 與 CSS 壓縮管理)
* **模組相依性**: `cap` (共用核心) → `jcs` (嘉誠共用) → `mega` (兆豐共用) → 業務系統

### 1.2. 目標系統架構 (升級後)
* **後端**: Java 8 + Spring Framework 5.3
* **前端**: Spring MVC + Thymeleaf + jQuery 3.6.0
* **資料存取**: Hibernate JPA 2.1
* **前端資源管理**: RequireJS + Sass

## 2. 開發環境建置要求
* **JDK**: IBM Java SDK Version 8
* **Maven**: 3.5.4 (為配合 TFS 環境)
* **Eclipse**: 2019-12 (含 Team Explorer Everywhere 插件)
* **本地開發伺服器**: Jetty 9.4.20
* **Node.js**: 用於 CKEditor5 客製化建置與前端資源優化

## 3. 升級SOP與程式碼修改規範
* [cite_start]**`UPGRADETODO`**: 對於不確定的修改，一律增加 `UPGRADETODO` 註解，修改正確後再移除 [cite: 4]。
* [cite_start]**既有註解**: 不要刪除既有的註解 [cite: 5]。
* [cite_start]**共用模組或組件**: 共同組件有很多JS盡量不要改，盡量不要修改共用模組 (CAP、JCS、MEGA)，如需修改請用 `override` 的作法 [cite: 7]。
* [cite_start]**歷程比對**: 對於有疑慮的程式碼片段，請善用歷程修改紀錄進行比對 [cite: 8]。
* [cite_start]**共用資源**: 修改到共用的 `js` 或 `css` 檔案時，必須執行 Maven 指令重新打包後才能 commit 到 Git [cite: 9]。
* [cite_start]**排版 Template**: 目前沒有統一規定 [cite: 6]。

## 4. 後端升級 (Java & Spring)

### 4.1. 框架遷移：Wicket 至 Spring MVC
* **Page 轉為 Controller**: Wicket 的 `Page` 類別被轉換為 Spring MVC 的 `Controller` 類別。
    * `@MountPath` 被 `@Controller` 與 `@RequestMapping` 取代。
    * `execute` 方法的簽名從 `public void execute(PageParameters params)` 變更為 `public void execute(ModelMap model, PageParameters params)`。
* **URL 路徑處理**:
    * [cite_start]為防止解析 `@RequestMapping` 時出現 `IndexOutOfBoundsException`，應使用 `value=` 或直接寫入字串，而不是 `path=` [cite: 413, 414][cite_start]。範例：`@RequestMapping(value="/lrs/lms1800m01/{page}")` [cite: 421]。
    * [cite_start]對於沒有分頁功能的頁面，需從 `@RequestMapping` 路徑中移除可選的 `/{page}`，以避免 404 錯誤，因為 Thymeleaf 不接受路徑變數為空值 [cite: 448, 452]。

### 4.2. 資料庫層：OpenJPA 至 Hibernate

#### Entity 與映射調整
* **`@OneToMany` 關聯**:
    * [cite_start]為解決 Hibernate 的 `Repeated column` (重複欄位) 映射異常，主表 (One) 的 `@OneToMany` 註解應使用 `mappedBy`，並移除該端的 `@JoinColumn` [cite: 527, 528, 460]。
    * [cite_start]子表 (Many) 保留其 `@JoinColumn`，但若該欄位已在 Entity 中被 `@Column` 定義，則可能需要加上 `insertable = false, updatable = false` [cite: 429, 460][cite_start]。這可以防止 Hibernate 嘗試插入同一個欄位兩次，從而避免 `SQLCODE=-121` 錯誤 [cite: 425, 433]。
* **`@ToOne` 關聯**:
    * [cite_start]所有 `@OneToOne` 和 `@ManyToOne` 映射都應明確加上 `fetch = FetchType.LAZY` 以優化效能 [cite: 455, 456][cite_start]。同時，建議在類別上配置 `@NamedEntityGraph` 來定義特定查詢中需要預先載入的懶加載屬性 [cite: 456]。
* [cite_start]**Hibernate 嚴格性**: 遷移至 Hibernate 後，需要更嚴格地遵守 JPA 規範，因其對重複欄位映射的檢查比 OpenJPA 更嚴格 [cite: 435]。

#### 查詢與資料處理
* [cite_start]**`params.getStringArray()`**: 在新版本中，若前端未傳遞某個參數，`params.getStringArray()` 會回傳一個空陣列 (`new String[0]`) 而不是 `null` [cite: 578, 579][cite_start]。所有檢查其回傳值的 `if` 判斷式，都必須從 `Util.isEmpty()` 改為 `ArrayUtils.isEmpty()` 以正確處理此邏輯變更 [cite: 695]。
* [cite_start]**日期型別轉換**: 從前端傳來的字串型別日期參數，不會再被自動轉換為 `Date` 物件 [cite: 438][cite_start]。必須在傳遞給查詢前，使用 `SimpleDateFormat` 進行手動解析，以避免 `Parameter value did not match expected type` 的錯誤 [cite: 437, 439, 440, 441]。
* [cite_start]**`IS NULL` 查詢**: 當查詢條件為某欄位 `is null` 時 (例如 `deletedTime`)，應使用 `SearchMode.IS_NULL` [cite: 445][cite_start]。若使用 `SearchMode.EQUALS` 搭配 `null` 值，SQL 會被錯誤地轉換為 `deletedTime="null"` 而非 `deletedTime IS NULL` [cite: 443, 444]。
* [cite_start]**`SQLCODE=-302` (輸入值過長)**: 此錯誤可能因 IPv6 位址 (約 25 bytes) 被傳入設計給 IPv4 (15 bytes) 的資料庫欄位而發生 [cite: 769, 779][cite_start]。解決方法是在執行環境的 VM 參數中加入 `-Djava.net.preferIPv4Stack=true`，強制系統使用 IPv4 [cite: 775, 780]。

### 4.3. 國際化 (i18n)
* [cite_start]若頁面上的 i18n 碼未被正確轉換，需在對應的 Java Page 類別中，手動加入載入 i18n properties 檔案的程式碼 [cite: 401, 402]。
* [cite_start]請確保所有 i18n properties 檔案都已根據語系完整命名 (例如 `_zh_TW.properties`, `_en.properties`) [cite: 406, 410][cite_start]。系統依賴此命名慣例來載入正確的語系檔案 [cite: 408]。

## 5. 前端升級 (HTML, JS, CSS)

### 5.1. HTML & Thymeleaf 轉換
* **標籤轉換**: Wicket 專用標籤被替換為 Thymeleaf 的對應標籤：
    * [cite_start]`<wicket:message>` → `<th:block th:text="...">` 或 `<span th:utext="...">` [cite: 182, 91]
    * [cite_start]`<wicket:enclosure>` → `<th:block th:if="...">` [cite: 178]
    * [cite_start]`wicket:message="title:..."` → `th:title="#{'...'}"` [cite: 145]
    * [cite_start]用於動態屬性的 `wicket:message` → `th:attr="displayMessage=..."` [cite: 121]
* **條件渲染**:
    * [cite_start]元件的可見性邏輯 (`setVisible`, `isVisible`) 被 Java 中的 `model.addAttribute("key", booleanValue)` 和 HTML 中的 `th:if="${key}"` 所取代 [cite: 177]。
    * [cite_start]對於使用 `addAclLabel` 的元件，屬性名稱會被自動加上 `_visible` 後綴 [cite: 196, 198][cite_start]。因此 `th:if` 條件也必須包含此後綴 (例如 `th:if="${_btnDOC_EDITING_visible}"`) [cite: 192, 193, 195]。
* [cite_start]**HTML 字元渲染**: 為正確渲染 `&nbsp;` 等 HTML 實體，應使用 `th:utext` 而非 `th:text` [cite: 86, 91]。
* [cite_start]**超連結**: 為確保 URL 能被應用程式上下文正確解析，應使用 `th:href="@{/path/to/file}"` 而非靜態的 `href` 屬性 [cite: 94, 98]。
* [cite_start]**iGrid 顯示問題**: 如果 iGrid 只顯示為一條直線，在其外層的 `<table>` 標籤加上 `width="100%"` 屬性 [cite: 100, 104]。
* [cite_start]**HTML ID 重複**: 若因 ID 重複導致事件觸發失敗，可將 ID 重新命名以確保唯一性 [cite: 162][cite_start]，或使用屬性選擇器如 `$Form.find("[id=XXX]").hide()` 來選取所有具備該 ID 的元素 [cite: 163]。

### 5.2. JavaScript & jQuery 升級
* **jQuery 3.x 相容性**:
    * `.live()` 被 `.on()` 取代。
    * `.size()` 被 `.length` 取代。
    * [cite_start]對於布林屬性，建議使用 `.prop()` 而非 `.attr()`。例如，`item.attr('tagName')` 應改為 `item.prop('tagName')` 來修復驗證樣式問題 [cite: 398]。
    * AJAX 的 `success`, `error`, `complete` 回呼被基於 Promise 的 `.done()`, `.fail()`, `.always()` 取代。
* **腳本載入**:
    * [cite_start]舊的 Java `getJavascriptPath()` 方法已被棄用 [cite: 12]。
    * [cite_start]在 Java 中使用 `model.addAttribute("loadScript", "loadScript('path/to/script.js');")` 來載入腳本 [cite: 17]。
    * [cite_start]若腳本載入失敗，可直接在 HTML 中使用 Thymeleaf 的 URL 表示式語法以獲得更可靠的路徑：`<script th:src="@{/pagejs/script.js}"></script>` [cite: 373] [cite_start]或 `<script>loadScript('[[@{/pagejs/script.js}]]');</script>` [cite: 372]。
* **AJAX 呼叫**:
    * [cite_start]在某些情況下，`$.ajax` 呼叫中必須明確提供 `url` [cite: 233][cite_start]，因為系統無法再僅根據 `handler` 名稱自動解析路徑，尤其是在 `V00Page` (列表頁) 中 [cite: 238, 239]。
    * [cite_start]AJAX 呼叫中的 `formId` 不能是 "empty"，必須指向頁面上一個實際存在的表單 ID [cite: 234, 235]。
* **JSON 處理**:
    * [cite_start]jQuery 對 JSON 格式的要求變得更嚴格。若後端回傳字串格式的 JSON 物件，前端必須先使用 `JSON.parse()` 手動解析後才能使用 [cite: 362, 363]。
    * [cite_start]透過 AJAX 提交表單時，需將表單資料序列化為 JSON 物件再傳送至後端，因為 Wicket 的自動資料綁定機制已不存在 [cite: 228]。`JSON.stringify($('#form').serializeData())` 是新的標準作法。
* **錯誤處理與防呆**:
    * [cite_start]為防止 `Uncaught TypeError: Cannot read properties of undefined (reading 'length')` 錯誤，在存取物件屬性前，務必先檢查該陣列或物件是否存在 (例如 `if (Array.isArray(json?.listData))`) [cite: 351, 354]。
    * [cite_start]JS 的執行順序至關重要。需確保資料注入與元素建立 (`tabForm.buildItem()`, `tabForm.injectData(json)`) 的動作，發生在鎖定表單 (`tabForm.lockDoc()`) 等功能之前 [cite: 357, 358]。

### 5.3. 共用/通用 JS 問題
* **qTip 提示視窗**: qTip 套件升級後，需要修改語法。
    * [cite_start]為完整移除 tooltip，`item.qtip("destroy")` 必須改為 `item.qtip("destroy", true)` [cite: 387][cite_start]。`true` 參數可確保 qTip 的資料也從 jQuery 的記憶體中清除 [cite: 382, 384]。
    * [cite_start]Tooltip 容器的選擇器從 `$("#ui-tooltip-" + tipid)` 改為 `$('#qtip-' + tipid)` [cite: 392]。
* [cite_start]**驗證樣式**: 為修復輸入框在驗證錯誤時未顯示紅色警告底線的問題，`common.validation.js` 中獲取標籤名稱的方法從 `item.attr('tagName')` 改為 `item.prop('tagName')` [cite: 395, 396, 398]。

## 6. 報表功能 (PDF & Excel)

### 6.1. PDF 產生 (PdfTools)
* [cite_start]**Unbalanced operators 錯誤**: 此錯誤由未正確關閉的 `PdfContentByte` 引起 [cite: 731][cite_start]。確保每一個 `pcb.beginText()` 呼叫都有一個對應的 `pcb.endText()` 即可解決 [cite: 733]。
* [cite_start]**行內顯示 vs. 附件下載**: 使用 `CapByteArrayDownloadResult` 建構子中的 `isInline` 布林值來控制 PDF 的行為 [cite: 735, 743]。
    * [cite_start]`true`: PDF 將直接在瀏覽器中顯示 [cite: 744]。
    * [cite_start]`false` (或省略): 瀏覽器將提示使用者以下載附件的方式儲存 PDF [cite: 744]。

### 6.2. Excel 產生：JXL 轉換為 Apache POI
* [cite_start]從 JXL 遷移至 Apache POI 時，合併儲存格的框線處理方式不同。POI 預設只會在合併區域的左上角儲存格應用框線 [cite: 748, 749, 752]。
* [cite_start]**解決方案**: 在使用 `sheet.addMergedRegion(region)` 合併儲存格後，必須使用 `RegionUtil` 的方法 (如 `setBorderTop`, `setBorderBottom` 等) 手動為整個合併區域設定框線 [cite: 758, 764, 765, 766, 767]。

```