package com.mega.eloan.lms.cls.report;

import java.io.FileNotFoundException;
import java.io.IOException;

import com.iisigroup.cap.component.PageParameters;

/**
 * <pre>
 * rpt報表service程式
 * </pre>
 * 
 * @since 2023/01/13
 * <AUTHOR>
 * @version <ul>
 *          <li>
 *          </ul>
 */
public interface CLS1141BarcodeR01RptService {
	public byte[] getContent(PageParameters params) throws FileNotFoundException, IOException, Exception;
}
