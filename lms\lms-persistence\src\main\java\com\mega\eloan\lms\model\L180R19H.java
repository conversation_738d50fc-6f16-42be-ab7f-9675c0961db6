/* 
 * L180R19H.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 逾期未覆審歷史檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L180R19H", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L180R19H extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * mainId<p/>
	 * LELF412A/B mainId
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 資料日期<p/>
	 * LELF412A/B DATADATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="DATADATE", columnDefinition="DATE")
	private Date dataDate;

	/** 
	 * 企/個金案件<p/>
	 * 1企金<br/>
	 *  2個金
	 */
	@Size(max=1)
	@Column(name="DOCTYPE", length=1, columnDefinition="CHAR(1)")
	private String docType;

	/** 
	 * 覆審種類<p/>
	 * FOR 企金：<br/>
	 *  A或空白:主辦覆審<br/>
	 *  B:自辦覆審
	 */
	@Size(max=1)
	@Column(name="CTLTYPE", length=1, columnDefinition="CHAR(1)")
	private String ctlType;

	/** 
	 * 分行<p/>
	 * LELF412A/B BRANCH
	 */
	@Size(max=3)
	@Column(name="BRANCH", length=3, columnDefinition="CHAR(3)")
	private String branch;

	/** 
	 * 統一編號<p/>
	 * LELF412A/B CUSTID
	 */
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 
	 * 重覆序號<p/>
	 * LELF412A/B DUPNO
	 */
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 額度－幣別 **/
	@Size(max=3)
	@Column(name="FACTCURR", length=3, columnDefinition="CHAR(3)")
	private String factCurr;

	/** 額度－金額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factAmt;

	/** 逾額－幣別 **/
	@Size(max=3)
	@Column(name="BALCURR", length=3, columnDefinition="CHAR(3)")
	private String balCurr;

	/** 逾額－金額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="BALAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal balAmt;

	/** 
	 * 科目代碼<p/>
	 * 企金:2碼科目代碼<br/>
	 *  個金:3碼科目代碼<br/>
	 *  空白為有額度但未動用
	 */
	@Size(max=3)
	@Column(name="SUBJECT", length=3, columnDefinition="VARCHAR(3)")
	private String subject;

	/** 授信期間-起 **/
	@Temporal(TemporalType.DATE)
	@Column(name="BGNDATE", columnDefinition="DATE")
	private Date bgnDate;

	/** 授信期間-迄 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ENDDATE", columnDefinition="DATE")
	private Date endDate;

	/** 前次覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="LRDATE", columnDefinition="DATE")
	private Date lrDate;

	/** 最遲應覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DUEDATE", columnDefinition="DATE")
	private Date dueDate;

	/** 
	 * 覆審類別(消金)/覆審週期(企金)<p/>
	 * 企金:A~H (覆審週期)<br/>
	 *  消金:1;2;4 (覆審類別)
	 */
	@Size(max=40)
	@Column(name="RCKDLINE", length=40, columnDefinition="VARCHAR(40)")
	private String rckdLine;

	/** 異常通報日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="MDDATE", columnDefinition="DATE")
	private Date mdDate;

	/** 
	 * 異常通報覆審逾期註記<p/>
	 * Y/N<br/>
	 *  Y:mdDate – lrDate 超過一個月
	 */
	@Size(max=1)
	@Column(name="ISMDDUE", length=1, columnDefinition="CHAR(1)")
	private String isMdDue;

	/** 
	 * 備註<p/>
	 * 200個中文字
	 */
	@Size(max=600)
	@Column(name="MEMO", length=600, columnDefinition="VARCHAR(600)")
	private String memo;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得mainId<p/>
	 * LELF412A/B mainId
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定mainId<p/>
	 *  LELF412A/B mainId
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得資料日期<p/>
	 * LELF412A/B DATADATE
	 */
	public Date getDataDate() {
		return this.dataDate;
	}
	/**
	 *  設定資料日期<p/>
	 *  LELF412A/B DATADATE
	 **/
	public void setDataDate(Date value) {
		this.dataDate = value;
	}

	/** 
	 * 取得企/個金案件<p/>
	 * 1企金<br/>
	 *  2個金
	 */
	public String getDocType() {
		return this.docType;
	}
	/**
	 *  設定企/個金案件<p/>
	 *  1企金<br/>
	 *  2個金
	 **/
	public void setDocType(String value) {
		this.docType = value;
	}

	/** 
	 * 取得覆審種類<p/>
	 * FOR 企金：<br/>
	 *  A或空白:主辦覆審<br/>
	 *  B:自辦覆審
	 */
	public String getCtlType() {
		return this.ctlType;
	}
	/**
	 *  設定覆審種類<p/>
	 *  FOR 企金：<br/>
	 *  A或空白:主辦覆審<br/>
	 *  B:自辦覆審
	 **/
	public void setCtlType(String value) {
		this.ctlType = value;
	}

	/** 
	 * 取得分行<p/>
	 * LELF412A/B BRANCH
	 */
	public String getBranch() {
		return this.branch;
	}
	/**
	 *  設定分行<p/>
	 *  LELF412A/B BRANCH
	 **/
	public void setBranch(String value) {
		this.branch = value;
	}

	/** 
	 * 取得統一編號<p/>
	 * LELF412A/B CUSTID
	 */
	public String getCustId() {
		return this.custId;
	}
	/**
	 *  設定統一編號<p/>
	 *  LELF412A/B CUSTID
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 
	 * 取得重覆序號<p/>
	 * LELF412A/B DUPNO
	 */
	public String getDupNo() {
		return this.dupNo;
	}
	/**
	 *  設定重覆序號<p/>
	 *  LELF412A/B DUPNO
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得額度－幣別 **/
	public String getFactCurr() {
		return this.factCurr;
	}
	/** 設定額度－幣別 **/
	public void setFactCurr(String value) {
		this.factCurr = value;
	}

	/** 取得額度－金額 **/
	public BigDecimal getFactAmt() {
		return this.factAmt;
	}
	/** 設定額度－金額 **/
	public void setFactAmt(BigDecimal value) {
		this.factAmt = value;
	}

	/** 取得逾額－幣別 **/
	public String getBalCurr() {
		return this.balCurr;
	}
	/** 設定逾額－幣別 **/
	public void setBalCurr(String value) {
		this.balCurr = value;
	}

	/** 取得逾額－金額 **/
	public BigDecimal getBalAmt() {
		return this.balAmt;
	}
	/** 設定逾額－金額 **/
	public void setBalAmt(BigDecimal value) {
		this.balAmt = value;
	}

	/** 
	 * 取得科目代碼<p/>
	 * 企金:2碼科目代碼<br/>
	 *  個金:3碼科目代碼<br/>
	 *  空白為有額度但未動用
	 */
	public String getSubject() {
		return this.subject;
	}
	/**
	 *  設定科目代碼<p/>
	 *  企金:2碼科目代碼<br/>
	 *  個金:3碼科目代碼<br/>
	 *  空白為有額度但未動用
	 **/
	public void setSubject(String value) {
		this.subject = value;
	}

	/** 取得授信期間-起 **/
	public Date getBgnDate() {
		return this.bgnDate;
	}
	/** 設定授信期間-起 **/
	public void setBgnDate(Date value) {
		this.bgnDate = value;
	}

	/** 取得授信期間-迄 **/
	public Date getEndDate() {
		return this.endDate;
	}
	/** 設定授信期間-迄 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/** 取得前次覆審日 **/
	public Date getLrDate() {
		return this.lrDate;
	}
	/** 設定前次覆審日 **/
	public void setLrDate(Date value) {
		this.lrDate = value;
	}

	/** 取得最遲應覆審日 **/
	public Date getDueDate() {
		return this.dueDate;
	}
	/** 設定最遲應覆審日 **/
	public void setDueDate(Date value) {
		this.dueDate = value;
	}

	/** 
	 * 取得覆審類別(消金)/覆審週期(企金)<p/>
	 * 企金:A~H (覆審週期)<br/>
	 *  消金:1;2;4 (覆審類別)
	 */
	public String getRckdLine() {
		return this.rckdLine;
	}
	/**
	 *  設定覆審類別(消金)/覆審週期(企金)<p/>
	 *  企金:A~H (覆審週期)<br/>
	 *  消金:1;2;4 (覆審類別)
	 **/
	public void setRckdLine(String value) {
		this.rckdLine = value;
	}

	/** 取得異常通報日期 **/
	public Date getMdDate() {
		return this.mdDate;
	}
	/** 設定異常通報日期 **/
	public void setMdDate(Date value) {
		this.mdDate = value;
	}

	/** 
	 * 取得異常通報覆審逾期註記<p/>
	 * Y/N<br/>
	 *  Y:mdDate – lrDate 超過一個月
	 */
	public String getIsMdDue() {
		return this.isMdDue;
	}
	/**
	 *  設定異常通報覆審逾期註記<p/>
	 *  Y/N<br/>
	 *  Y:mdDate – lrDate 超過一個月
	 **/
	public void setIsMdDue(String value) {
		this.isMdDue = value;
	}

	/** 
	 * 取得備註<p/>
	 * 200個中文字
	 */
	public String getMemo() {
		return this.memo;
	}
	/**
	 *  設定備註<p/>
	 *  200個中文字
	 **/
	public void setMemo(String value) {
		this.memo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
