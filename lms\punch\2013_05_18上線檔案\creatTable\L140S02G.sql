---------------------------------------------------------
-- LMS.L140S02G 代償轉貸借新還舊主檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140S02G;
CREATE TABLE LMS.L140S02G (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	SEQ           DECIMAL(5,0)  not null,
	CH<PERSON><PERSON><PERSON>       CHAR(1)      ,
	CHGOTHER      CHAR(1)      ,
	CHGBOR<PERSON><PERSON>ER   CHAR(1)      ,
	CHGALOAN      CHAR(1)      ,
	ONLENTDATE    DATE         ,
	CHAR<PERSON><PERSON>AG    CHAR(1)      ,
	CHARGEAMT     DECIMAL(13,0),
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140S02G PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140S02G01;
CREATE UNIQUE INDEX LMS.XL140S02G01 ON LMS.L140S02G   (MAINID, SEQ);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140S02G IS '代償轉貸借新還舊主檔';
COMMENT ON LMS.L140S02G (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	SEQ           IS '序號', 
	CHGCASE       IS '是否辦理「代償/轉貸/借新還舊」', 
	CHGOTHER      IS '本筆借款是否用來償還其他筆貸款', 
	CHGBORROWER   IS '該其他筆貸款之主借款人是否和本案主借款人相同', 
	CHGALOAN      IS '是否為本行貸款', 
	ONLENTDATE    IS '轉貸日期', 
	CHARGEFLAG    IS '是否收取手續費', 
	CHARGEAMT     IS '手續費金額', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
