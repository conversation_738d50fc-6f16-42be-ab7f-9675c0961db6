package com.mega.eloan.lms.fms.handler.form;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.fms.pages.CLS2701V01Page;
import com.mega.eloan.lms.fms.service.CLS2701Service;
import com.mega.eloan.lms.model.C900M01I;
import com.mega.eloan.lms.model.C900S01I;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;


@Scope("request")
@Controller("cls2701formhandler")
public class CLS2701FormHandler extends AbstractFormHandler {

	@Resource
	CLS2701Service cls2701Service;

	@Resource
	LMSService lmsService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	ICustomerService iCustomerService;
	
	@Resource
	UserInfoService userInfoService;
	@Resource
	UserInfoService userService;
	
	@Resource
	BranchService branchService;
	
	Properties prop_cls2701v01 = MessageBundleScriptCreator
		.getComponentResource(CLS2701V01Page.class);

	
	Properties prop_AbstractEloanPage = MessageBundleScriptCreator
		.getComponentResource(AbstractEloanPage.class);
	
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult insertC900M01I(PageParameters params)
	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		//========
		C900M01I model = new C900M01I();
		CapBeanUtil.map2Bean(params, model, new String[] {
				"custId", "dupNo",
				"orgCustId", "orgDupNo", 
				"docDate", "docNo"
		});
		
		Map<String, Object> newDataMap = iCustomerService.findByIdDupNo(model.getCustId(), model.getDupNo());
		if(newDataMap==null){
			throw new CapMessageException(model.getCustId()+"-"+model.getDupNo()+"查無資料", getClass());
		}
		String newCustName = Util.trim(MapUtils.getString(newDataMap, "CNAME"));
		
		Map<String, Object> orgDataMap = iCustomerService.findByIdDupNo(model.getOrgCustId(), model.getOrgDupNo());
		//可能在0024已把 oldData 刪掉
//		if(orgDataMap==null){
//			throw new CapMessageException(model.getOrgCustId()+"-"+model.getOrgDupNo()+"查無資料", getClass());
//		}
		String orgCustName = Util.trim(MapUtils.getString(orgDataMap, "CNAME"));
		
		if(true){
			if(Util.equals("金徵(業)字第號", model.getDocNo())){
				throw new CapMessageException("請輸入"+prop_cls2701v01.getProperty("C900M01I.docNo"), getClass());	
			}
		}
		model.setMainId(IDGenerator.getUUID());
		model.setCustName(newCustName);
		model.setOrgCustName(orgCustName);
		model.setOwnBrId(user.getUnitNo());
		model.setDocStatus(FlowDocStatusEnum.已確認.getCode());
		model.setCreator(user.getUserId());
		model.setCreateTime(nowTS);
		model.setUpdater(user.getUserId());
		model.setUpdateTime(nowTS);
		
		cls2701Service.save(model);
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteC900M01I(PageParameters params)
	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("oid"));
		C900M01I model = cls2701Service.findC900M01I_oid(oid);
		if(model!=null){			
			List<C900S01I> c900s01i_list = cls2701Service.findC900S01I_mainId(model.getMainId());
			if(c900s01i_list.size()>0){
				throw new CapMessageException("已有"+c900s01i_list.size()+"筆資料", getClass());
			}else{
				model.setDeletedTime(CapDate.getCurrentTimestamp());
				cls2701Service.save(model);	
			}			
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult insertC900S01I(PageParameters params)
	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		String c900m01i_mainId = params.getString("c900m01i_mainId");
		String[] oid_arr = params.getStringArray("oid_arr");
		C900M01I c900m01i = cls2701Service.findC900M01I_mainId(c900m01i_mainId);
		
		List<C900S01I> c900s01i_list = new ArrayList<C900S01I>();
		for(L120M01A l120m01a: cls2701Service.findL120M01A_oid(oid_arr)){
			String mainId = c900m01i.getMainId();
			String caseMainId = l120m01a.getMainId();
			String caseNo = Util.toSemiCharString(l120m01a.getCaseNo());
			
			C900S01I exist_c900s01i = cls2701Service.findC900S01I_mainId_caseMainId(mainId, caseMainId);
			if(exist_c900s01i!=null){
				throw new CapMessageException(caseNo+"已存在", getClass());
			}
			C900S01I c900s01i = new C900S01I();
			c900s01i.setOwnBrId(user.getUnitNo());
			c900s01i.setDocStatus(FlowDocStatusEnum.編製中.getCode());
			c900s01i.setCreator(user.getUserId());
			c900s01i.setCreateTime(nowTS);
			c900s01i.setUpdater(user.getUserId());
			c900s01i.setUpdateTime(nowTS);
			//~~~~~~
			c900s01i.setMainId(mainId);
			c900s01i.setCustId(c900m01i.getCustId());
			c900s01i.setDupNo(c900m01i.getDupNo());
			c900s01i.setCustName(c900m01i.getCustName());
			c900s01i.setOrgCustId(c900m01i.getOrgCustId());
			c900s01i.setOrgDupNo(c900m01i.getOrgDupNo());
			c900s01i.setOrgCustName(c900m01i.getOrgCustName());
			//copy from L120M01A
			c900s01i.setCaseMainId(caseMainId);
			c900s01i.setCaseNo(caseNo);
			c900s01i.setCaseDate(l120m01a.getCaseDate());
			//~~~~~~
			c900s01i_list.add(c900s01i);
		}
		cls2701Service.saveC900S01I(c900s01i_list);
		
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteC900S01I(PageParameters params)
	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String[] oid_arr = params.getStringArray("oid_arr");
		for(String oid: oid_arr){
			C900S01I c900s01i = cls2701Service.findC900S01I_oid(oid);
			if(c900s01i!=null){
				cls2701Service.delete(c900s01i);
			}
		}
		
		return result;
	}
	
	private void proc_c900s01i_status_chg(String[] oid_arr
			, String oldStatus, String newStatus)
	throws CapMessageException{
		
		List<C900S01I> c900s01i_list = new ArrayList<C900S01I>();
		for(String oid: oid_arr){
			C900S01I c900s01i = cls2701Service.findC900S01I_oid(oid);
			if(c900s01i==null){
				throw new CapMessageException(oid+"查無資料", getClass());
			}else{
				if(Util.equals(c900s01i.getDocStatus(), oldStatus)){
					c900s01i.setDocStatus(newStatus);					
					c900s01i_list.add(c900s01i);
				}else{
					throw new CapMessageException(Util.trim(c900s01i.getCaseNo())
							+"狀態為"+c900s01i.getDocStatus(), getClass());
				}	
			}
			
		}
		cls2701Service.saveC900S01I(c900s01i_list);
	}
	
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult to_020(PageParameters params)
	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String[] oid_arr = params.getStringArray("oid_arr");		
		proc_c900s01i_status_chg(oid_arr, FlowDocStatusEnum.編製中.getCode(), FlowDocStatusEnum.待覆核.getCode());
		
		return result;
	}
	
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = false)
	public IResult to_030(PageParameters params)
	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		String approver = user.getUserId();
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		
		String[] oid_arr = params.getStringArray("oid_arr");		
		String oldStatus = FlowDocStatusEnum.待覆核.getCode();
		String newStatus = FlowDocStatusEnum.已核准.getCode();

		List<C900S01I> c900s01i_list = new ArrayList<C900S01I>();
		for(String oid: oid_arr){
			C900S01I c900s01i = cls2701Service.findC900S01I_oid(oid);
			if(c900s01i==null){
				throw new CapMessageException(oid+"查無資料", getClass());
			}else{
				if(Util.equals(c900s01i.getDocStatus(), oldStatus)){
					c900s01i.setDocStatus(newStatus);
					if(true){
						if(Util.equals(c900s01i.getUpdater(), approver)){
							throw new CapMessageException(Util.trim(c900s01i.getCaseNo())
									+"異動經辦、覆核不可同一人", getClass());
						}
						L120M01A l120m01a = clsService.findL120M01A_mainId(c900s01i.getCaseMainId());
						lmsService.updCustId(l120m01a, c900s01i.getOrgCustId(), c900s01i.getOrgDupNo()
								, c900s01i.getCustId(), c900s01i.getDupNo(), false, "");
						
						c900s01i.setApprover(approver);
						c900s01i.setApproveTime(nowTS);
					}
					c900s01i_list.add(c900s01i);
				}else{
					throw new CapMessageException(Util.trim(c900s01i.getCaseNo())
							+"狀態為"+c900s01i.getDocStatus(), getClass());
				}	
			}
			
		}
		cls2701Service.saveC900S01I(c900s01i_list);
	
		
		return result;
	}
	
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = false)
	public IResult backto_010(PageParameters params)
	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String[] oid_arr = params.getStringArray("oid_arr");		
		proc_c900s01i_status_chg(oid_arr, FlowDocStatusEnum.待覆核.getCode(), FlowDocStatusEnum.編製中.getCode());
		
		return result;
	}
	
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Query)
	public IResult loadData(PageParameters params)
			throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
				
		ISearch pageSetting = clsService.getMetaSearch();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("docDate", true);
		Page<? extends GenericBean> page = cls2701Service.findPage(C900M01I.class, pageSetting);
		
		Map<String, String> map = new HashMap<String, String>();
		ArrayList<String> itemOrder = new ArrayList<String>();
		List<C900M01I> list = (List<C900M01I>) page.getContent();
		for (C900M01I model : list) {
			String key = model.getMainId();
			itemOrder.add(key);
			map.put(key, Util.trim(model.getOrgCustId())+"-"+Util.trim(model.getOrgDupNo())
					+" 改為 "
					+Util.trim(model.getCustId())+"-"+Util.trim(model.getDupNo())
					+" "
					+Util.trim(model.getCustName())+"，"+Util.trim(model.getDocNo()));
		}
		result.set("item", new CapAjaxFormResult(map));
		result.set("itemOrder", itemOrder);
		return result;
	}

}
