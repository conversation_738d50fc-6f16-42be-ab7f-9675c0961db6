package tw.com.jcs.flow.core;

/**
 * <pre>
 * Flow Acction 有錯誤時拋出的 Exception
 * </pre>
 * 
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月6日
 *          </ul>
 */
public class FlowException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * constructor
     */
    public FlowException() {
        super();
    }

    /**
     * constructor
     * 
     * @param message
     * @param cause
     */
    public FlowException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * constructor
     * 
     * @param message
     */
    public FlowException(String message) {
        super(message);
    }

    /**
     * constructor
     * 
     * @param cause
     */
    public FlowException(Throwable cause) {
        super(cause);
    }

}
