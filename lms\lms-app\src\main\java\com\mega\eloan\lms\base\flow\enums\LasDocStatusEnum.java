/* 
 * LasDocStatusEnum.java
 */
package com.mega.eloan.lms.base.flow.enums;

/**<pre>
 * 文件的狀態表(共用)
 * 若要取得各系統使用的文件狀態代碼，請使用其系統所定義之enum
 * 
 * EX: 編製中 010
 * 第一碼：系統碼
 *   0.共用 ,1.擔保品管理系統,2.徵信管理系統,3.企金授信管理系統,4.消金授信管理系統,5.逾放催收管理系統,6.資料建檔
 * 第二碼：類別碼(依據notes文件狀態)
 *   0.待收案件,1.編製中,2.待覆核,3.已核准,4.婉卻	,7.待補件或撤件
 * 第三碼：單位碼&自訂義
 *   0.預設狀態(保留關鍵字),B.分行,H.授管處,C.總行,A.區中心,O.海外
 */
public enum LasDocStatusEnum {
	
	//L授信稽核工作底稿
	
	分行_編製中("31B"),
	分行_待覆核("32B"),
	分行_已核准("33B"),
	//分行_已傳送("29B"),
	
	@Deprecated
	稽核室_分行_編製中("30G"),
	
	稽核室_編製中("31G"),
	稽核室_待覆核("32G"),
	稽核室_已核准("33G"),
	
	
		
	//改使用中文...(以下英文暫時保留..)
	/** 編製中 **/
	DOC_EDITING("010"),
	/** 待覆核 **/
	WAIT_APPROVE("020");

	private String code;
	LasDocStatusEnum(String code){
		this.code = code;
	}
	
	public String getCode() {
        return code;
    }
	
	public boolean isEquals(Object other){
		if (other instanceof String){
			return code.equals(other);
		}else{
			return super.equals(other);
		}
	}
	
	public static LasDocStatusEnum getEnum(String code){
		for (LasDocStatusEnum enums : LasDocStatusEnum.values()){
			if (enums.isEquals(code)){
				return enums;
			}
		}
		return null;
	}
	
	@Override
	public String toString() {
		return code;
	}
	
}
