package com.mega.eloan.lms.lrs.pages;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.mega.eloan.lms.base.pages.AbstractFileDownloadPage;

@Controller
@RequestMapping("/lrs/lms1805r01")
public class LMS1805M01Report extends AbstractFileDownloadPage {
	
	public LMS1805M01Report() {
		super();
	}

	@Override
	public String getDownloadFileName() {
		return "LMS1805PreList.xls";
	}

	@Override
	public String getFileDownloadServiceName() {
		return "lms1805xlsservice";
	}

	@Override
	protected String getViewName() {
		// TODO Auto-generated method stub
		return null;
	}
}
