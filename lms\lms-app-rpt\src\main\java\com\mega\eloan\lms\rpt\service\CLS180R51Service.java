package com.mega.eloan.lms.rpt.service;

import java.util.Map;
import java.util.Properties;

import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;




/**
 * <pre>
 * 整批房貸統計表
 * </pre>
 * 
 * @since 2020
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface CLS180R51Service {
	
	public Map<String, Integer> getTitleMap();
	
	public void setTitleContent(WritableSheet sheet, Map<String, Integer> titleMap, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex) throws WriteException;

	public Map<String, Integer> getHeaderMap();
	
	public void setHeaderContent(WritableSheet sheet, Map<String, Integer> headerMap1, Properties prop, int colIndex, int rowIndex) throws WriteException;
	
	public void setBodyContent(WritableSheet sheet, Map<String, Map<String, Object>> data, int colIndex, int rowIndex) throws RowsExceededException, WriteException;
	
	public void processIsCloseCase(Map<String, Map<String, Object>> masterData, String endDate);
}
