/* 
 * C160S01EDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C160S01E;

/** 代償轉貸借新還舊主檔 **/
public interface C160S01EDao extends IGenericDao<C160S01E> {

	C160S01E findByOid(String oid);
	
	List<C160S01E> findByMainId(String mainId);
	
	C160S01E findByUniqueKey(String mainId, Integer seq);

	List<C160S01E> findByIndex01(String mainId, Integer seq);

	public C160S01E findByMainIdRefMainIdSeq (String mainId,String refmainId,Integer seqNo);
	
	public C160S01E findByMainIdRefMainIdSeq (String mainId,Integer seqNo,String refmainId);
}