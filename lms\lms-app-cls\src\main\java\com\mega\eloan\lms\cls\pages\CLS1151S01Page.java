/* 
 *CLS1151S01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import java.util.HashSet;
import java.util.Properties;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.panels.LMSL140M01MPanel;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.panels.CLS1151S01Panel;
import com.mega.eloan.lms.cls.panels.CLS1151S02Panel;
import com.mega.eloan.lms.cls.panels.CLS1151S03Panel;
import com.mega.eloan.lms.cls.panels.CLS1151S04Panel;
import com.mega.eloan.lms.cls.panels.CLS1151S05Panel;
import com.mega.eloan.lms.cls.panels.CLS1151S06Panel;
import com.mega.eloan.lms.cls.panels.CLS1151S07Panel;
import com.mega.eloan.lms.cls.panels.CLS1151S08Panel;
import com.mega.eloan.lms.cls.panels.CLS1151S09Panel;
import com.mega.eloan.lms.cls.panels.CLS1151S10Panel;
import com.mega.eloan.lms.cls.panels.CLS1151S11Panel;
import com.mega.eloan.lms.cls.panels.CLS1151S12Panel;
import com.mega.eloan.lms.cls.panels.CLS1151S13Panel;
import com.mega.eloan.lms.model.C102M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02F;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金額度明細表
 * </pre>
 * 
 * @since 2012/12/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/7,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1151s01")
public class CLS1151S01Page extends AbstractEloanForm {

	@Autowired
	LMSService lmsService;
	
	@Autowired
	CLSService clsService;
	
	@Override
	public void execute(ModelMap model, PageParameters params)
			throws Exception {
		/*
		 * 透過  CLS1141S03Panel.js :: openCntrNoDoc
		 */
		String caseMainId = Util.trim(params.getString("CaseMainId"));
		String mainId = Util.trim(params.getString("mainId"));
		L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);
		L140M01A l140m01a = clsService.findL140M01A_mainId(mainId);
		Set<String> c102m01a_rptId = new HashSet<String>();
		boolean show_change_L140M01A_ownBrId = false;
		boolean showITWCODE = false;
		boolean showCoreBussITWCODE = false;
		boolean isProdKind07 = false;
		boolean isProdKind08 = false;
		boolean isProdKind71 = false;
		Properties prop_CLS1151S01 = MessageBundleScriptCreator.getComponentResource(CLS1151S01Page.class);
		if(l120m01a!=null && l140m01a!=null){			
			if(LMSUtil.isParentCase(l120m01a)){
				//團貸母戶, 不應選專案種類細項
			}else{
				String busCode = clsService.get0024_busCode(l140m01a.getCustId(), l140m01a.getDupNo());
				if(Util.isEmpty(l140m01a.getIsStartUp())
						&& OverSeaUtil.isBranchEditing(l120m01a.getDocStatus())){
					//［在途、編製中］的案件,無 IsStartUp, 才補資料
					// String busCode = clsService.get0024_busCode(l140m01a.getCustId(), l140m01a.getDupNo());
					String isStartUp = "N";
					if( !LMSUtil.isBusCode_060000_130300(busCode)){
						try{
							isStartUp = lmsService.applyIsStartUpInner(l140m01a.getCaseDate(), busCode);
						}catch(CapException e){
							
						}
					}
					l140m01a.setIsStartUp(isStartUp);
					clsService.daoSave(l140m01a);
				}
				if (Util.isEmpty(l140m01a.getIsCoreBuss())
						&& OverSeaUtil.isBranchEditing(l120m01a.getDocStatus())) {
					String isCoreBuss = "N";
					if (!LMSUtil.isBusCode_060000_130300(busCode)) {
						try {
							isCoreBuss = lmsService.applyIsCoreBuss(l140m01a.getCaseDate(), busCode);
						} catch (CapException e) {

						}
					}
					l140m01a.setIsCoreBuss(isCoreBuss);
					clsService.daoSave(l140m01a);
				}
				if(Util.equals(l140m01a.getIsStartUp(), "N")){
					showITWCODE = true;
				}				
				if (Util.equals(l140m01a.getIsCoreBuss(), "N")) {
					showCoreBussITWCODE = true;
				}
				if(true){
					int cnt_prodKind07 = 0;
					int cnt_prodKindNot07 = 0;
					
					int cnt_prodKind08 = 0;
					int cnt_prodKindNot08 = 0;

					int cnt_prodKind71 = 0;
					int cnt_prodKindNot71 = 0;
					for(L140S02A l140s02a : clsService.findL140S02A(l140m01a)){
						if(CrsUtil.is_07(l140s02a.getProdKind())){
							++cnt_prodKind07;
						}else{
							++cnt_prodKindNot07;
						}
						
						if(CrsUtil.is_08(l140s02a.getProdKind())){
							++cnt_prodKind08;
						}else{
							++cnt_prodKindNot08;
						}
						
						if(CrsUtil.is_71(l140s02a.getProdKind())){
							++cnt_prodKind71;
						}else{
							++cnt_prodKindNot71;
						}
					}
					isProdKind07 = ClsUtil.decide_prodKind07(cnt_prodKind07, cnt_prodKindNot07);
					isProdKind08 = ClsUtil.decide_prodKind08(cnt_prodKind08, cnt_prodKindNot08);					
					isProdKind71 = ClsUtil.decide_prodKind71(cnt_prodKind71, cnt_prodKindNot71);
				}				
			}
			
			if(!LMSUtil.isParentCase(l120m01a) && OverSeaUtil.isBranchEditing(l120m01a.getDocStatus())
					&& !Util.equals(Util.getLeftStr(Util.trim(l140m01a.getCntrNo()), 3), l120m01a.getCaseBrId())
					&& !Util.equals(l140m01a.getOwnBrId(), l120m01a.getCaseBrId())){
				show_change_L140M01A_ownBrId = true;
			}

			for(L140S02A l140s02a : clsService.findL140S02A(l140m01a)){
				L140S02F l140s02f = clsService.findL140S02F(l140s02a.getMainId(), l140s02a.getSeq());
				if(l140s02f!=null && Util.isNotEmpty(Util.trim(l140s02f.getRefMainId()))){
					for(C102M01A c102m01a : clsService.findC102M01A_mainId(l140s02f.getRefMainId())){
						String rptId = Util.trim(c102m01a.getRptId());
						if(Util.isEmpty(rptId)){
							continue;
						}
						c102m01a_rptId.add(rptId);
					}
				}
			}
		}
		
		new CLS1151S01Panel("CLS1151S01", show_change_L140M01A_ownBrId,
				showITWCODE, showCoreBussITWCODE)
				.processPanelData(model, params);
		new CLS1151S02Panel("CLS1151S02").processPanelData(model, params);
		new CLS1151S03Panel("CLS1151S03").processPanelData(model, params);
		new CLS1151S04Panel("CLS1151S04").processPanelData(model, params);
		//page5.051=房貸相關
		//page5.240=卡友信貸相關
		//page5.241=信貸相關
		String tabTitle = prop_CLS1151S01.getProperty("page5.051");
		if(isProdKind07){
			tabTitle = prop_CLS1151S01.getProperty("page5.241");
		}else if(isProdKind08){
			tabTitle = prop_CLS1151S01.getProperty("page5.240");
		}else if(isProdKind71){
			tabTitle = prop_CLS1151S01.getProperty("page5.241");
		}
		new CLS1151S05Panel("CLS1151S05",
				(l120m01a == null ? null : l120m01a.getEndDate()), tabTitle,
				c102m01a_rptId).processPanelData(model, params);
		new CLS1151S06Panel("CLS1151S06").processPanelData(model, params);
		new CLS1151S07Panel("CLS1151S07").processPanelData(model, params);
		new CLS1151S08Panel("CLS1151S08").processPanelData(model, params);
		new CLS1151S09Panel("CLS1151S09").processPanelData(model, params);
		new CLS1151S10Panel("CLS1151S10").processPanelData(model, params);
		new CLS1151S11Panel("CLS1151S11").processPanelData(model, params);
		// 央行購住共用
		new LMSL140M01MPanel("LMSL140M01MPanel").processPanelData(model,
				params);
		new CLS1151S12Panel("CLS1151S12").processPanelData(model, params);
		new CLS1151S13Panel("CLS1151S13",
				l120m01a.getDocCode() != null
						&& "5".equals(l120m01a.getDocCode()) ? true : false)
				.processPanelData(model, params);
		model.addAttribute("isShowCls1151s13", l120m01a.getDocCode() != null
				&& "5".equals(l120m01a.getDocCode()) ? true : false);

		renderJsI18N(CLS1151S01Page.class);
		renderJsI18N(LMSL140M01MPanel.class);
		renderJsI18N(CLS1220M04Page.class); //線上信貸進件
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}

}
