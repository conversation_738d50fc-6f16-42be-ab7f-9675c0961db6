package com.mega.eloan.lms.fms.handler.form;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.fms.pages.LMS7800M01Page;
import com.mega.eloan.lms.fms.service.LMS7800Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L140MM6A;
import com.mega.eloan.lms.model.L140MM6B;
import com.mega.eloan.lms.model.L140MM6C;
import com.mega.eloan.lms.model.L180R46A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 共同行銷維護作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms7800m01formhandler")
@DomainClass(L140MM6A.class)
public class LMS7800M01FormHandler extends AbstractFormHandler {
	
	@Resource
	BranchService branchService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	LMS7800Service lms7800Service;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	Properties pop = MessageBundleScriptCreator.getComponentResource(LMS7800M01Page.class);
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL140mm6a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			if (lms7800Service.deleteL140mm6as(oids)) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
			}
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newL140mm6a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String l140mm6aMainid = "";
		L140MM6A l140mm6a = new L140MM6A();
		l140mm6a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		l140mm6a.setOwnBrId(user.getUnitNo());

		l140mm6aMainid = IDGenerator.getUUID();

		l140mm6a.setMainId(l140mm6aMainid);
		String txCode = Util.trim(params
				.getString(EloanConstants.TRANSACTION_CODE));
		l140mm6a.setTxCode(txCode);
		// UPGRADE: 待確認，URL是否正確
		l140mm6a.setDocURL(params.getString("docUrl"));
		l140mm6a.setDeletedTime(CapDate.getCurrentTimestamp());
		String cntrNo = params.getString("cntrNo","");
		if (Util.equals(cntrNo, "")) {
			// 額度序號不得為空白
			throw new CapMessageException(pop.getProperty("cntrNoEmpty"), getClass());
		}
		l140mm6a.setCntrNo(cntrNo);
		
		L180R46A l180r46a = lms7800Service.findL180r46aByCntrNoType(cntrNo,"A");
		if (l180r46a != null && Util.isNotEmpty(l180r46a)) {
			l140mm6a.setAppraiser(l180r46a.getAppraiser());
			l140mm6a.setInfoAppraiser(l180r46a.getInfoAppraiser());
			l140mm6a.setCustId(l180r46a.getCustId());
			l140mm6a.setDupNo(l180r46a.getDupNo());
			l140mm6a.setCustName(l180r46a.getCustName());
		}
		
		lms7800Service.save(l140mm6a);

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(EloanConstants.OID, l140mm6a.getOid());
		result.set(EloanConstants.MAIN_ID, l140mm6a.getMainId());
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL140mm6a(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> dataMap = new HashMap<String, String>();

		List<L140MM6C> lastData = lms7800Service.findL140mm6csByMainId(mainId);
		if(lastData == null || lastData.isEmpty()){
			if (!Util.isEmpty(oid)) {
				L140MM6A l140mm6a = lms7800Service.findModelByOid(L140MM6A.class, oid);
				//第一次啟案自動塞入前案資訊
				dataMap = lms7800Service.getData(l140mm6a, true);
				result.putAll(dataMap);
				result = formatResultShow(result, l140mm6a, page);
			} else {
				// 開啟新案帶入起案的分行和目前文件狀態
				result.set(
						"docStatus",
						this.getMessage("docStatus."
								+ CreditDocStatusEnum.海外_編製中.getCode()));
				result.set("ownBrId", user.getUnitNo());
				result.set(
						"ownBrName",
						StrUtils.concat(" ",
								branchService.getBranchName(user.getUnitNo())));
				result.set("docStatusVal", CreditDocStatusEnum.海外_編製中.getCode());
			}
		} else {
			if (!Util.isEmpty(oid)) {
				L140MM6A l140mm6a = lms7800Service.findModelByOid(L140MM6A.class, oid);
				dataMap = lms7800Service.getData(l140mm6a, false);
				result.putAll(dataMap);
				result = formatResultShow(result, l140mm6a, page);
			}
		}
		
		return result;
	}
	
	public IResult checkCntrNo(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String cntrNo = Util.trim(params.getString("cntrNo"));
		
		L180R46A l180r46a = lms7800Service.findL180r46aByCntrNoType(cntrNo, "A");
		if(l180r46a != null && Util.isNotEmpty(l180r46a)){
			result = DataParse.toResult(l180r46a);
			result.set("count", 1);
		} else {
			result.set("count", 0);
		}
		
		return result;
	}
	
	/**
	 * 格式化顯示訊息
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private CapAjaxFormResult formatResultShow(CapAjaxFormResult result,
			L140MM6A l140mm6a, Integer page) throws CapException {
		String mainId = l140mm6a.getMainId();

		switch (page) {
		case 1:
			result = DataParse.toResult(l140mm6a);
			List<L140MM6B> l140mm6blist = (List<L140MM6B>) lms7800Service
					.findListByMainId(L140MM6B.class, mainId);
			if (!Util.isEmpty(l140mm6blist)) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (L140MM6B l140mm6b : l140mm6blist) {
					// 要加上人員代碼
					String type = Util.trim(l140mm6b.getStaffJob());
					String userId = Util.trim(l140mm6b.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {
						result.set("showApprId", userId + " " + value);
					} else if ("L3".equals(type)) {
						bossId.append(bossId.length() > 0 ? "<br/>" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) {
						result.set("reCheckId", userId + " " + value);
					} else if ("L5".equals(type)) {
						result.set("managerId", userId + " " + value);
					} else if ("L6".equals(type)) {
						result.set("mainApprId", userId + " " + value);
					} else if ("L7".equals(type)) {
						result.set("mainReCheckId", userId + " " + value);
					}
				}
				result.set("bossId", bossId.toString());
			}
			result.set("ownBrName",	" " + branchService.getBranchName(l140mm6a.getOwnBrId()));

			result.set("creator", lmsService.getUserName(l140mm6a.getCreator()));
			result.set("updater", lmsService.getUserName(l140mm6a.getUpdater()));
			result.set("createTime", Util.nullToSpace(TWNDate.valueOf(l140mm6a.getCreateTime())));
			result.set("updateTime", Util.nullToSpace(TWNDate.valueOf(l140mm6a.getUpdateTime())));
			result.set("docStatus",	getMessage("docStatus." + l140mm6a.getDocStatus()));
			break;
		}// close switch case

		result.set("showCustId", StrUtils.concat(CapString.trimNull(l140mm6a.getCustId()), " ",
				CapString.trimNull(l140mm6a.getDupNo()), " ", CapString.trimNull(l140mm6a.getCustName())));
		result.set("docStatusVal", l140mm6a.getDocStatus());
		result.set("cntrNo", l140mm6a.getCntrNo());
		result.set(EloanConstants.OID, CapString.trimNull(l140mm6a.getOid()));
		result.set(EloanConstants.MAIN_OID,	CapString.trimNull(l140mm6a.getOid()));
		result.set(EloanConstants.MAIN_ID, CapString.trimNull(l140mm6a.getMainId()));
		
		L140MM6C l140mm6cA = lms7800Service.findL140mm6c(mainId, "A");
		result = this.formatL140MM6C(result, l140mm6cA, "A");
		L140MM6C l140mm6cB = lms7800Service.findL140mm6c(mainId, "B");
		result = this.formatL140MM6C(result, l140mm6cB, "B");
		L140MM6C l140mm6cC = lms7800Service.findL140mm6c(mainId, "C");
		result = this.formatL140MM6C(result, l140mm6cC, "C");

		return result;
	}
	
	private CapAjaxFormResult formatL140MM6C(CapAjaxFormResult result,
			L140MM6C l140mm6c, String type) throws CapException {
		if(l140mm6c == null && Util.isEmpty(l140mm6c)){
			result.set(type, "");
		} else {
			result.set(type, type);
			result.set("checkYN_"+type, l140mm6c.getCheckYN());

			String typeResult = l140mm6c.getResult();
			result.set("result_"+type, typeResult);
			
			String contact = Util.trim(l140mm6c.getContact());
			String memo = l140mm6c.getMemo();
			result.set("select_"+type, contact);
			result.set("memo_"+type, memo);
		}
		return result;
	}
	
	/**
	 * 		查詢共同行銷窗口
	 */
	public IResult querySynBankOfCsTypes(PageParameters params) 
			throws CapException{
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("select_AList", new CapAjaxFormResult(lmsService
												.querySynBankOfCsTypes("1B", "800")));
		result.set("select_BList", new CapAjaxFormResult(lmsService
												.querySynBankOfCsTypes("06", "060")));
		result.set("select_CList", new CapAjaxFormResult(lmsService
												.querySynBankOfCsTypes("1A", "700")));
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL140mm6a(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, params.getString("tempSave", "N"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String oid = Util.trim(params.getString(EloanConstants.OID));

		String form = Util.trim(params.getString("mainPanel"));
		JSONObject jsonData = null;

		L140MM6A l140mm6a = null;
		Boolean showMsg = params.getAsBoolean("showMsg", false);
		String showMsg1 = "";
		if (Util.isNotEmpty(oid)) {
			l140mm6a = lms7800Service.findModelByOid(L140MM6A.class, oid);
			l140mm6a.setRandomCode(IDGenerator.getRandomCode());
		}
		l140mm6a.setDeletedTime(null);

		String validate = null;
		switch (page) {
		case 1:
			jsonData = JSONObject.fromObject(form);
			DataParse.toBean(jsonData, l140mm6a);
			validate = Util.validateColumnSize(l140mm6a, pop, "L140MM6A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}

			lms7800Service.save(l140mm6a);
			result.set("randomCode", l140mm6a.getRandomCode());
			break;
		}
		this.saveL140mm6c(l140mm6a.getMainId(), jsonData);
		result = formatResultShow(result, l140mm6a, page);
		if (Util.isEmpty(showMsg1)) {
			if (showMsg) {
				showMsg1 = RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功);
			}
		} else {
			if (showMsg) {

			}else{
				throw new CapMessageException(showMsg1,	getClass());
			}
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, showMsg1);

		result.set(EloanConstants.OID, CapString.trimNull(l140mm6a.getOid()));
		result.set(EloanConstants.MAIN_OID,	CapString.trimNull(l140mm6a.getOid()));
		result.set(EloanConstants.MAIN_ID, CapString.trimNull(l140mm6a.getMainId()));

		return result;
	}
	
	public void saveL140mm6c(String mainId, JSONObject jsonData) {
		String[] typeArr = {"A","B","C"};
		for(String type : typeArr){
			String checkYN = jsonData.optString("checkYN_"+type, "");
			String result = jsonData.optString("result_"+type, "");
			String select = jsonData.optString("select_"+type, "");
			String memo = jsonData.optString("memo_"+type, "");
			L140MM6C l140mm6c = lms7800Service.findL140mm6c(mainId, type);
			if(l140mm6c == null && Util.isEmpty(l140mm6c)){
				//不需執行
			} else {
				l140mm6c.setCheckYN(checkYN);
				l140mm6c.setResult(result);
				l140mm6c.setContact(select);
				l140mm6c.setMemo(memo);
				lms7800Service.save(l140mm6c);
			}
		}
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;

	}
	
	 /*** 呈主管覆核(呈主管 主管覆核 拆2個method) */
	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {
		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L140MM6A l140mm6a = (L140MM6A) lms7800Service.findModelByOid(L140MM6A.class, oid);
		String[] formSelectBoss = params.getStringArray("selectBoss");

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		if (!Util.isEmpty(formSelectBoss)) {

			String manager = Util.trim(params.getString("manager"));
			List<L140MM6B> models = (List<L140MM6B>) lms7800Service
					.findListByMainId(L140MM6B.class, l140mm6a.getMainId());
			if (!models.isEmpty()) {
				lms7800Service.deleteL140mm6bs(models, false);
			}
			List<L140MM6B> l140mm6bs = new ArrayList<L140MM6B>();
			for (String people : formSelectBoss) {
				L140MM6B l140mm6b = new L140MM6B();
				l140mm6b.setCreator(user.getUserId());
				l140mm6b.setCreateTime(CapDate.getCurrentTimestamp());
				l140mm6b.setMainId(l140mm6a.getMainId());
				l140mm6b.setBranchType(user.getUnitType());
				l140mm6b.setBranchId(user.getUnitNo());
				// L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
				l140mm6b.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
				l140mm6b.setStaffNo(people);
				l140mm6bs.add(l140mm6b);
			}
			L140MM6B managerL140mm6b = new L140MM6B();
			managerL140mm6b.setCreator(user.getUserId());
			managerL140mm6b.setCreateTime(CapDate.getCurrentTimestamp());
			managerL140mm6b.setMainId(l140mm6a.getMainId());
			managerL140mm6b.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
			managerL140mm6b.setStaffNo(manager);
			managerL140mm6b.setBranchType(user.getUnitType());
			managerL140mm6b.setBranchId(user.getUnitNo());
			l140mm6bs.add(managerL140mm6b);
			L140MM6B apprL140mm6b = new L140MM6B();
			apprL140mm6b.setCreator(user.getUserId());
			apprL140mm6b.setCreateTime(CapDate.getCurrentTimestamp());
			apprL140mm6b.setMainId(l140mm6a.getMainId());
			apprL140mm6b.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
			apprL140mm6b.setStaffNo(user.getUserId());
			apprL140mm6b.setBranchType(user.getUnitType());
			apprL140mm6b.setBranchId(user.getUnitNo());
			l140mm6bs.add(apprL140mm6b);
			lms7800Service.saveL140mm6bList(l140mm6bs);
		}
		Boolean upMis = false;
		L140MM6B l140mm6bL4 = new L140MM6B();
		// 如果有這個key值表示是輸入chekDate核准日期
		if (params.containsKey("checkDate")) {
			l140mm6a.setApprover(user.getUserId());
			l140mm6a.setApproveTime(CapDate.getCurrentTimestamp());
			upMis = true;
			L140MM6B l140mm6b = lms7800Service.findL140mm6b(
					l140mm6a.getMainId(), user.getUnitType(), user.getUnitNo(),
					user.getUserId(), UtilConstants.STAFFJOB.執行覆核主管L4);
			if (l140mm6b == null) {
				l140mm6b = new L140MM6B();
				l140mm6b.setCreator(user.getUserId());
				l140mm6b.setCreateTime(CapDate.getCurrentTimestamp());
				l140mm6b.setMainId(l140mm6a.getMainId());
				l140mm6b.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
				l140mm6b.setStaffNo(user.getUserId());
				l140mm6b.setBranchType(user.getUnitType());
				l140mm6b.setBranchId(user.getUnitNo());
			}
			l140mm6bL4 = l140mm6b;
		}

		if (!Util.isEmpty(l140mm6a)) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
				if (params.containsKey("flowAction")) {
					// 退回部檢查
					if (params.getBoolean("flowAction")) {
						L140MM6B l140mm6b = lms7800Service.findL140mm6b(
								l140mm6a.getMainId(), user.getUnitType(),
								user.getUnitNo(), user.getUserId(),
								UtilConstants.STAFFJOB.經辦L1);

						if (l140mm6b != null) {
							// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						} else {
							lms7800Service.save(l140mm6bL4);
							upMis = true;
						}
					}
				}
				lms7800Service.flowAction(l140mm6a.getOid(), l140mm6a,
						params.containsKey("flowAction"),
						params.getAsBoolean("flowAction", false), upMis);
			} catch (FlowException t1) {
				logger.error(
						"[flowAction] lms7800Service.flowAction FlowException!!",
						t1);
				throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
			} catch (Throwable t1) {
				logger.error(
						"[flowAction]  lms7800Service.flowAction EXCEPTION!!",
						t1);
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}

		return new CapAjaxFormResult();
	}
}
