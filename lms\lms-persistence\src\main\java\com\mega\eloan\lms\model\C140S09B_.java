package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * The persistent class for the C140S09B database table.
 * </pre>
 * @since  2011/10/27
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/10/27,<PERSON>,new
 *          </ul>
 */
@StaticMetamodel(C140S09B.class)
public class C140S09B_ extends RelativeMeta_{
	public static volatile SingularAttribute<C140S09B, Date> grtD;
	public static volatile SingularAttribute<C140S09B, String> grtNa1;
	public static volatile SingularAttribute<C140S09B, BigDecimal> grtNm1;
	public static volatile SingularAttribute<C140S09B, BigDecimal> grtNt1;
	public static volatile SingularAttribute<C140S09B, BigDecimal> grtYm1;
	public static volatile SingularAttribute<C140S09B, BigDecimal> grtYt1;
	public static volatile SingularAttribute<C140S09B, C140M01A> c140m01a;
}
