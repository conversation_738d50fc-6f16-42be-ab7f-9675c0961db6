/* 
 * L140MM1ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import com.mega.eloan.lms.model.L140MM1A;

import tw.com.iisi.cap.dao.IGenericDao;


/** 央行註記異動作業主檔 **/
public interface L140MM1ADao extends IGenericDao<L140MM1A> {

	L140MM1A findByOid(String oid);
//	
//	L140MM1A findByMainId(String mainId);
//	
	List<L140MM1A> findByMainId(String mainId);
	
	List<L140MM1A> findByDocStatus(String docStatus);
	
	L140MM1A findByUniqueKey(String mainId, String custId, String dupNo, String cntrNo);

	List<L140MM1A> findByIndex01(String mainId, String custId, String dupNo, String cntrNo);
	
	List<L140MM1A> findByCntrNo(String cntrNo);
}
