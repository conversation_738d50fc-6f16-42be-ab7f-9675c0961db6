/* 
 * BSG1120GService.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.lms.service;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.service.ICapService;

/**
 * <pre>
 * 集團112提供的Global Service API.
 * </pre>
 * 
 * @since 2012/2/2
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2012/2/2,Sunkist Wang,new
 *          </ul>
 */
public interface LMS9101GService extends ICapService {

	public OutputStream generateReport(PageParameters params)
			throws FileNotFoundException, IOException, Exception;

}
