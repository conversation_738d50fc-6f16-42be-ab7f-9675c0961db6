$(function() {
	
});

var L120s01rGrid = $("#l120s01rGrid").iGrid({
    handler: 'lms1201gridhandler',
    height: 350,
    postData: {
        formAction: "queryL120s01rList"
    },
    rownumbers: true,
    multiselect : true,
    needPager: false,
    colModel: [{
        colHeader: i18n.lmss07a06["L120S01R.grid.custId"],
        align: "left",
        width: 60, // 設定寬度
        sortable: true, // 是否允許排序
        formatter: 'click',
        onclick: loadS01rDoc,
        name: 'custId'
    }, {
        colHeader: i18n.lmss07a06["L120S01R.grid.dupNo"],
        align: "left",
        width: 10, // 設定寬度
        name: 'dupNo'
    }, {
        colHeader: i18n.lmss07a06["L120S01R.grid.custName"],
        align: "left",
        width: 60, // 設定寬度
        name: 'custName'
    }, {
        colHeader: "oid",
        name: 'oid',
        hidden: true
    }],
    ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
        var data = $("#l120s01rGrid").getRowData(rowid);
        loadS01rDoc(null, null, data);
    }
}).trigger("reloadGrid");

var custChooseGrid = $("#l120s01rCustChooseGrid").iGrid({ // 選借款人GridView
    handler: 'lms1201gridhandler',
    height: 200,
	multiselect: true,
    postData: {
        formAction: "queryL120s01rCustChoose"
    },
    colModel: [{
        colHeader: i18n.lmss07a06["L120S01R.grid.custId"], // 身分證字號/統一編號
        align: "left",
        width: 100, // 設定寬度
        sortable: false, // 是否允許排序
        name: 'custId' // col.id
    }, {
        colHeader: i18n.lmss07a06["L120S01R.grid.dupNo"], // 重複序號
        align: "left",
        width: 40, // 設定寬度
        sortable: false, // 是否允許排序
        name: 'dupNo' // col.id
    }, {
        colHeader: i18n.lmss07a06["L120S01R.grid.custName"], // 客戶名稱
        align: "left",
        width: 100, // 設定寬度
        sortable: false, // 是否允許排序
        name: 'custName' // col.id
    }]
});

function loadS01rDoc(cellvalue, options, data){
    $.ajax({
        handler: responseJSON["handler"],
        action: "queryL120s01r",
        data: {
            mainId: responseJSON.mainId,
            oid: data.oid
        },
        success: function(data){
        	openS01rDoc(data);
        }
    });
}


/**
 * J-111-0220 選擇相關保證人->確定後產生檢核表主檔
 */
function genL120S01R() {
	// reload借款人grid
	$("#l120s01rCustChooseGrid").trigger("reloadGrid");
	$("#l120s01rCustChoose").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss07a06["L120S01R.chooseTitle"],//"選擇借款人"
		width : 600,
		height : 400,
		modal : true,
		i18n:i18n.def,
		buttons : {
			"sure" : function(b) {
				if(b){
					var chooseCust= $("#l120s01rCustChooseGrid").getGridParam('selarrrow');
					if(chooseCust.length <=0){
						// 請選擇資料
						return CommonAPI.showErrorMessage(i18n.def["grid_selector"]);
					}else {
						$.thickbox.close();
						
						// 組裝客戶custId^dupNo 傳給後端做比對
						var chooseCustArr = [];
                        for (var i in chooseCust) {
			                var row = $("#l120s01rCustChooseGrid").getRowData(chooseCust[i]);
			                chooseCustArr.push( row.custId + "^" + row.dupNo);// 放進一個array中
                        }
                        
                        // 傳給後端建立主檔
						$.ajax({
							handler : responseJSON["handler"],
							type : "POST",
							dataType : "json",
							action : "genL120s01r",
							data : {
								mainId : responseJSON.mainid,
								chooseCustArr : chooseCustArr
							},
							success : function(obj) {
								L120s01rGrid.trigger("reloadGrid");
							}
						});	
					}
				}
			},
			"close" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
					if(res){
						$.thickbox.close();
					}
		         });
			}
		}
	});
}

function addL120S01R(){
	openS01rDoc();
}

function openS01rDoc(data){
	$("#form06detailDiv").empty();
	// data是空的代表是新增add來的
	var versionDate = "20220706";
	if(data){
		versionDate = DOMPurify.sanitize(data.versionDate);
	}
	$("#form06detailDiv").load("../../lms/lmsS07APage06V"+escape(versionDate),function(){
		if(data){
			$("#LMS1205S07Form06").setData(data);
		}
		thickboxOptions.customButton = [i18n.lmss07a06["L1205S07Page06.saveData"], i18n.lmss07a06["L1205S07Page06.close"]];
		$("#form06detailDiv").thickbox({
			title: "",
			width: 1000,
			height: 700,
			modal : true,
			buttons:  API.createJSON([{
				// 儲存
				key: i18n.lmss07a06["L1205S07Page06.saveData"],
				value: function(){
					if(!$("#LMS1205S07Form06").valid()){
						return false;
					}
					$.ajax({
						handler: responseJSON["handler"],//"lms1201formhandler",
						type: "POST",
						action : "saveL120s01r",
						dataType: "json",
						data:{
							mainId: responseJSON.mainId,
							oid: data ? data.oid : '',
							versionDate:versionDate,
							LMS1205S07Form06: JSON.stringify($("#LMS1205S07Form06").serializeData())
						},
						success : function(json) {
							$.thickbox.close();
							API.showMessage(i18n.def['saveSuccess']);
							L120s01rGrid.trigger("reloadGrid");
						}
					});
				}
			}, {
				// 離開
				key: i18n.lmss07a06["L1205S07Page06.close"],
				value: function(){
					$.thickbox.close();
				}
			}])
		});
	});
}

function deleteL120S01R(){
	var rows = $("#l120s01rGrid").getGridParam('selarrrow');
	var data = [];
   
	if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
		return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
	}
	
	// confirmDelete=是否確定刪除?
	CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
		if (b) {
			for (var i in rows) {
				data.push($("#l120s01rGrid").getRowData(rows[i]).oid);
			}
			
			$.ajax({
				handler: responseJSON["handler"],
				type: "POST",
				action : "deleteL120s01rs",
				data: {
					oids: data
				},
				success: function(obj){
					$("#l120s01rGrid").trigger("reloadGrid");
				}
			});
		}
	});
}
