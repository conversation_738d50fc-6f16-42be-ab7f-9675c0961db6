package com.mega.eloan.lms.base.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;



import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.common.CrsVO;
import com.mega.eloan.lms.base.common.RO412;
import com.mega.eloan.lms.mfaloan.bean.ELF412;
import com.mega.eloan.lms.mfaloan.bean.ELF412B;
import com.mega.eloan.lms.mfaloan.bean.ELF412C;
import com.mega.eloan.lms.mfaloan.bean.ELF486;
import com.mega.eloan.lms.mfaloan.bean.ELF491;
import com.mega.eloan.lms.mfaloan.bean.ELF491B;
import com.mega.eloan.lms.mfaloan.bean.ELF491C;
import com.mega.eloan.lms.mfaloan.bean.ELF492;
import com.mega.eloan.lms.mfaloan.bean.ELF493;
import com.mega.eloan.lms.mfaloan.bean.ELF494;
import com.mega.eloan.lms.mfaloan.bean.ELF495;
import com.mega.eloan.lms.mfaloan.bean.ELF498;
import com.mega.eloan.lms.mfaloan.bean.ELF498B;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C240M01B;
import com.mega.eloan.lms.model.C240M01C;
import com.mega.eloan.lms.model.C241A01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.eloan.lms.model.C241M01C;
import com.mega.eloan.lms.model.C241M01E;
import com.mega.eloan.lms.model.C241M01F;
import com.mega.eloan.lms.model.C241M01Z;
import com.mega.eloan.lms.model.C242M01A;
import com.mega.eloan.lms.model.C243M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.eloan.lms.model.L170M01D;
import com.mega.eloan.lms.model.L170M01E;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.eloan.lms.model.L170M01G;
import com.mega.eloan.lms.model.L170M01H;
import com.mega.eloan.lms.model.L170M01J;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.eloan.lms.model.L180M01C;
import com.mega.eloan.lms.model.L180M01Z;
import com.mega.eloan.lms.model.L181M01A;
import com.mega.eloan.lms.model.L181M01B;
import com.mega.eloan.lms.model.L182M01A;
import com.mega.eloan.lms.model.L186M01A;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * 覆審
 */
/**
 * <AUTHOR>
 *
 */
/**
 * <AUTHOR>
 *
 */
/**
 * <AUTHOR>
 * 
 */
public interface RetrialService extends ICapService {

	/**
	 * 單筆儲存
	 * 
	 * @param entity
	 */
	void save(List<C241M01A> list);

	void saveL170M01A(List<L170M01A> list);

	void saveL170M01D(List<L170M01D> list);

	/**
	 * J-110-0308 覆審考核表
	 */
	void saveL170M01J(List<L170M01J> list);

	void deleteL170M01J(List<L170M01J> list);

	/**
	 * 單筆儲存
	 * 
	 * @param entity
	 */
	void save(GenericBean... entity);

	void del(GenericBean... entity);

	void saveC242M01A_procStart(C242M01A meta);

	void saveC242M01A_procEnd(C242M01A meta, boolean r);

	void saveL182M01A_procStart(L182M01A meta);

	void saveC241M01C(List<C241M01C> c241m01c_list);

	void saveC241M01Z(List<C241M01Z> c241m01z_list);

	public L170M01A findL170M01A_oid(String oid);

	public L170M01A findL170M01A(L180M01B l180m01b);

	public List<L170M01A> findL170M01A(L180M01A l180m01a);

	public List<L170M01A> findL170M01A(L180M01A l180m01a, String ctlType);

	/**
	 * 用本次的 l170m01a 找前次的 l170m01a
	 * 
	 * @param meta
	 * @return
	 */
	public L170M01A findL170M01A_bef(L170M01A meta);

	public boolean isL170M01A_upELF412(L180M01A l180m01a);

	public List<L170M01B> findL170M01B_orderBy(L170M01A l170m01a);

	public L170M01B findL170M01B_oid(String oid);

	public List<L170M01D> findL170M01D_orderBySeq(L170M01A l170m01a);

	public L170M01D findL170M01D_oid(String oid);

	public List<L170M01E> findL170M01E(L170M01A l170m01a);

	/**
	 * flag 為 C、M、F
	 */
	public Map<String, List<L170M01E>> findL170M01E_type(List<L170M01E> list);

	public L170M01F findL170M01F(L170M01A l170m01a);

	/**
	 * @param meta
	 * @param branchType
	 *            1.受檢單位, 2.覆審單位
	 * @return
	 */
	public L170M01G findL170M01G_first_L1(L170M01A meta, String branchType);

	/**
	 * 取得覆審組的L1
	 * 
	 * @param meta
	 * @return
	 */
	public L170M01G findL170M01G_first_L1_retrialTeam(L170M01A meta);

	/**
	 * J-110-0308 覆審考核表
	 */
	public List<L170M01J> findL170M01JByMainId(L170M01A l170m01a);

	public L170M01J findL170M01JByMainIdItemName(L170M01A l170m01a,
			String itemName);

	/**
	 * 用 oid 找 C240M01A
	 * 
	 * @param oid
	 * @return
	 */
	public C240M01A findC240M01A_oid(String oid);

	/**
	 * 用 c241m01a 找 C240M01A
	 * 
	 * @param oid
	 * @return
	 */
	public C240M01A findC240M01A_C241M01A(C241M01A c241m01a);

	public int count_retrialKind_in_c240m01a(String c240m01a_mainId,
			String param_ruleNo);

	/**
	 * 用 c240m01a 找 C240M01B
	 * 
	 * @param oid
	 * @return
	 */
	public List<C240M01B> findC240M01B(C240M01A c240m01a);

	public C240M01C findC240M01C(String c240m01a_mainId, String ruleNo);

	public List<C240M01C> findC240M01C(String c240m01a_mainId);

	/**
	 * 用 oid 找 C241M01A
	 * 
	 * @param oid
	 * @return
	 */
	public C241M01A findC241M01A_oid(String oid);

	/**
	 * 用 oid 找 C242M01A
	 * 
	 * @param oid
	 * @return
	 */
	public C242M01A findC242M01A_oid(String oid);

	public C242M01A findC242M01A_inProcess();

	public C242M01A findC242M01A_notProcess();

	public C243M01A findC243M01A_oid(String oidd);

	public C243M01A findC243M01A_mainId(String mainId);

	public L182M01A findL182M01A_typCd1_inProcess();

	public L182M01A findL182M01A_typCd1_notProcess();

	/**
	 * 用 oid_list 找 C241M01A
	 * 
	 * @param oid
	 * @return
	 */
	public List<C241M01A> findC241M01A_oid(List<String> oid_list);

	/**
	 * 用 C240M01A 找 List<C241M01A>
	 * 
	 * @param c240m01a
	 * @return
	 */
	public List<C241M01A> findC241M01A_C240M01A(String c240m01a_mainId);

	public List<C241M01A> findC241M01A_C240M01A_idDup(String c240m01a_mainId,
			String custId, String dupNo);

	/**
	 * 用本次的 c241m01a 找前次的 c241m01a
	 * 
	 * @param meta
	 * @return
	 */
	public C241M01A findC241M01A_bef(C241M01A meta);

	/**
	 * 按 notes VCLS24021 的欄位排序，欄位由左至右 在 headerColumn 右鍵>直欄屬性>第2個頁籤>排序:有區分[升冪、降冪]
	 * 
	 * If Instr(Trim(Ndoc.SubjectName(0)) ,"擔") > 0 Then Ndoc.Assure="Y"
	 * 可能同一個額度序號有 2 筆(1筆有餘額, 另1筆餘額0, 此狀況要 order by balance, 0在下面) order by
	 * fromDate asc, (Assure +tSno+@Text(balance)) desc
	 */
	public List<C241M01B> sortC241M01B(List<C241M01B> src);

	/**
	 * 用 c241m01a 找 C241M01B
	 * 
	 * @param oid
	 * @return
	 */
	public List<C241M01B> findC241M01B_c241m01a(C241M01A c241m01a);

	/**
	 * 用 c241m01a 找 C241M01B,且YnReviewY==Y
	 * 
	 * @param oid
	 * @return
	 */
	public List<C241M01B> findC241M01B_YnReviewY_c241m01a(C241M01A c241m01a);

	public C241M01B findC241M01B_oid(String oid);

	/**
	 * 用 c241m01a 找 C241M01B
	 * 
	 * @param oid
	 * @return
	 */
	public List<C241M01C> findC241M01C_c241m01a(C241M01A c241m01a);

	public C241M01C findC241M01C_oid(String oid);

	/**
	 * branchType:【1.受檢單位】【 2.覆審單位】
	 */
	public List<L170M01G> findL170M01G_byBranchTypeStaffJob(
			List<L170M01G> l170m01g_list, String branchType, String staffJob);

	/**
	 * branchType:【1.受檢單位】【 2.覆審單位】 <br>
	 * staffJob:【L1 經辦】【L4覆核】【L5主管】 <br>
	 * L4覆核主管, 可能同時有 2 個人 <br>
	 * ref:L120M01F
	 */
	public List<C241M01E> findC241M01E_byBranchTypeStaffJob(
			List<C241M01E> c241m01e_list, String branchType, String staffJob);

	public List<L170M01G> findL170M01G_l170m01a(L170M01A meta);

	public List<L170M01H> findL170M01H_fmtSYS_l170m01a(L170M01A meta);

	public List<L170M01H> findL170M01H_fmtSYS_debTypeC(L170M01A meta);

	public List<L170M01H> findL170M01H_fmtSYS_debTypeGN(L170M01A meta);

	public L170M01H findL170M01H_fmtFreeG_l170m01a(L170M01A meta);

	public L170M01H findL170M01H_fmtFreeC_l170m01a(L170M01A meta);

	public List<C241M01E> findC241M01E_c241m01a(C241M01A c241m01a);

	public C241M01E findC241M01E_first_L1(C241M01A c241m01a, String branchType);

	public C241M01E setC241M01E_L1(C241M01A c241m01a, String branchType,
			String userId, String branchId);

	public List<C241M01F> findC241M01F_c241m01a(C241M01A c241m01a);

	public LinkedHashMap<String, Map<String, List<String>>> groupC241M01F_by_caseMainId_rel_type(
			C241M01A c241m01a);

	public L170M01G setL170M01G_L1(L170M01A meta, String branchType,
			String userId, String branchId);

	public void saveC241M01E_L1L4L5(C241M01A c241m01a, String branchType,
			MegaSSOUserDetails user, List<String> l4List, List<String> l5List);

	// J-111-0033_05097_B1001 Web e-Loan
	// 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
	public void saveL170M01G_L1L4L5(L170M01A meta, String branchType,
			MegaSSOUserDetails user, List<String> l4List, List<String> l5List,
			List<String> l2List);

	/**
	 * 判斷登入分行 以 資訊處帳號 登入 201Br ● userInfo.unitNo=201 ● userInfo.ssoUnitNo=900
	 * 
	 * 採用海外、國內的程式
	 * 
	 * @return
	 */
	public boolean overSeaProgram();

	/**
	 * 判斷採用海外、國內的程式
	 * 
	 * @return
	 */
	public boolean overSeaProgram(String branch);

	/**
	 * 一般戶+團貸(母)+價金履約保證
	 * 
	 * @param c240m01a_mainId
	 * @return
	 */
	public List<C241M01A> grid_C241M01A_default(String c240m01a_mainId);

	/**
	 * 團貸(子)
	 * 
	 * @param c240m01a_mainId
	 * @param grpCntrNo
	 * @return
	 */
	public List<C241M01A> grid_C241M01A_byGrpCntrNo(String c240m01a_mainId,
			String grpCntrNo);

	/**
	 * 判斷是否已更新 ELF490
	 * 
	 * @param d
	 * @param brNo
	 * @return
	 */
	public boolean existC240M01Z(Date d, String brNo);

	/**
	 * 搜尋是否為行員
	 * 
	 * @param custId
	 * @return
	 */
	public boolean findstaff(CrsVO crsVO, String custId, String dupNo);

	/**
	 * 設定覆審批號
	 * 
	 * @param meta
	 */
	public void setBatchNo(L180M01A meta) throws CapMessageException;

	/**
	 * 設定覆審批號
	 * 
	 * @param meta
	 */
	public void setBatchNo(C240M01A meta) throws CapMessageException;

	public void importLNtoL170M01B(L170M01A meta);

	public void delLNtoL170M01B(L170M01A meta, List<L170M01B> dcList);

	public void l170m01b_ReCalculation(L170M01A meta);

	/**
	 * 重引帳務資料 <br>
	 * 一次重引 N 筆 C241M01A
	 */
	public void importLNtoC241M01B(C240M01A c240m01a, List<String> oid_list)
			throws CapException;

	/**
	 * 重引帳務資料 <br>
	 * 一次重引 1 筆 C241M01A (一般||團貸子戶)
	 */
	public void importLNtoC241M01B_single(C240M01A c240m01a, C241M01A c241m01a)
			throws CapException;

	/**
	 * 重引帳務資料 <br>
	 * 一次重引 1 筆 C241M01A, 當無 LN 資料,回傳 false
	 */
	public boolean importLNtoC241M01B(CrsVO crsVO, String c240m01a_branchId,
			C241M01A c241m01a, List<C241M01B> addList,
			List<C241M01B> delC241M01BList, String userId,
			String latest_aprdcdate) throws CapException;

	public void fetchELF491_LNF020_030_040(CrsVO crsVO, String brNo,
			Date dataEndDate, String elf491_newflag);

	public void fetch_LNF020_LNF030040(CrsVO crsVO, String custId, String dupNo);

	public List<Map<String, Object>> filter_crsVO_getLNF020(
			List<Map<String, Object>> src_list, String c240m01a_branchId);

	/**
	 * 將團貸子戶的 C241M01A, 加總合計到 團貸母戶的 C241M01A
	 */
	public void importLNtoC241M01B_sumGrpParent(List<C241M01A> c241m01as)
			throws CapException;

	public String getLatestRetrialItemVer(L170M01A l170m01a);

	public String getLatestRetrialItemVer(C241M01A c241m01a);

	public void importRetrialItemToC241M01C(C241M01A c241m01a,
			List<C241M01C> saveList, List<C241M01C> delC241M01CList);

	public void importRetrialItemToL170M01D(L170M01A meta,
			List<L170M01D> saveList, List<L170M01D> delL170M01DList);

	public Map<String, C241M01C> toMap_keyAs_itemNo(List<C241M01C> c241m01c_list);

	public Map<String, L170M01D> lrs_toMap_keyAs_itemNo(
			List<L170M01D> l170m01d_list);

	/**
	 * [0]CoBorrower [1]Guarantor
	 */
	public String[] getC241M01B_misEllngtee(String c240m01a_branchId,
			C241M01B c241m01b);

	public void genProjectNo(L180M01A meta)
			throws CapMessageException;

	/**
	 * 重新編排覆審序號
	 */
	public void genProjectNo(C240M01A c240m01a)
			throws CapMessageException;

	public void genProjectNo_append(L180M01A meta);

	public void genProjectNo_append(C240M01A c240m01a);

	public void genProjectNo_append_grpDetail(C240M01A c240m01a, C241M01A c241m01a_grpMain);

	/**
	 * 傳送覆審名單至BTT gfnDB2LNF09G
	 * 
	 * @param mainId
	 */
	public void sendBtt(L180M01A meta) throws CapException;

	public void sendBtt(C240M01A c240m01a) throws CapException;

	public Map<String, String> get_codeTypeWithOrder(String codeType);

	public Map<String, String> get_codeTypeWithOrder(String codeType,
			String locale);

	/**
	 * 1: S&P <br/>
	 * 2: Moody's <br/>
	 * 3: Fitch <br/>
	 * 4: 中華
	 */
	public Map<String, String> get_lrs_FcrdType();

	public Map<String, String> get_lrs_FcrdArea();

	public Map<String, String> get_lrs_FcrdPred();

	public String get_lrs_FcrdGrad_key(String elf412_fcrdType,
			String elf412_fcrdArea, String elf412_fcrdPred);

	public String toStr(L170M01E l170m01e);

	public Map<String, String> get_lrs_NewAdd();

	public Map<String, String> get_lrs_RckdLine();

	public Map<String, String> get_lrs_NckdFlagMap();

	public Map<String, String> get_lrs_CState();

	public Map<String, String> get_lrs_MdFlagMap();

	public Map<String, String> get_lrs_FinRatio();

	/**
	 * 傳回 DB, DL, OB, OU, NA-未評等
	 */
	public Map<String, String> get_lrs_CrdtType_2_withXX();

	public Map<String, String> get_lrs_CrdtType_2to1();

	/**
	 * 傳回 M1,..., NO-免辦
	 */
	public Map<String, String> get_lrs_MowType_2_withXX();

	/**
	 * NS: S&P <br/>
	 * NM: Moody's <br/>
	 * NF: Fitch <br/>
	 * NC: 中華
	 */
	public Map<String, String> get_lrs_FcrdType_2();

	/**
	 * J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
	 * 
	 * @param fcrdType
	 * @return
	 */
	public boolean isFcrdTypeEffect(String fcrdType);

	/**
	 * 若 MowType 為 M1:大型企業 回傳 {1:大型企業}
	 */
	public Map<String, String> get_lrs_MowType_1();

	public Map<String, String> get_lrs_MowType_1to2();

	public Map<String, String> get_lrs_MowType_2to1();

	/**
	 * 若 MowType 為 M1:大型企業 回傳 {M1:大型企業}
	 */
	public Map<String, String> get_lrs_MowType_2();

	public Map<String, String> get_lrs_MowTbl();

	public Map<String, String> get_lrs_CrdtTbl();

	/**
	 * J-107-0245 Web e-Loan企金授信系統覆審報告中增列上次覆審日當時之「信用評等及信用風險內部評等」資訊。
	 */
	Map<String, String> get_lrs_exType(String kind);

	public Map<String, String> get_crs_NckdFlagMap();

	public Map<String, String> get_crs_NckdFlagMapActive();

	public Map<String, String> get_crs_NewCaseDescMap();

	public Map<String, String> get_crs_lrs_LNFE0854_MDFLAG();

	/**
	 * 比照國內消金簽報書, 由 lms.c900m01a 取得
	 */
	public Map<String, String> get_crs_prodKindMap();

	public Map<String, String> get_crs_specifyCycle();

	/**
	 * <ul>
	 * <li>L140M01A.proPerty 是 VARCHAR(30)
	 * <ul>
	 * <li>在 CLS1151GridHandler::queryL140m01a
	 * <li>性質由 代碼→中文敘述, 用 select * from com.bcodetype where
	 * codetype='lms1405s02_proPerty'
	 * </ul>
	 * <li>但 C241M01B.quotaType 只有 VARCHAR(1)
	 * <ul>
	 * <li>海外的程式, 用 where codetype='c241m01b_quotaType'
	 * <li>Notes 的程式,搜尋SnoCase, 參考gfnCreatNPrintData
	 * </ul>
	 * </ul>
	 */
	public Map<String, String> get_crs_c241m01bQuotaType();

	/**
	 * 比照國內消金簽報書, 由 com.bcodetype 取得
	 */
	public Map<String, String> get_crs_lnPurposeMap();

	public Map<String, String> get_common_codeTypeMap(String codeType);

	/**
	 * 覆審控制檔維護，上傳「調整後」的欄位
	 */
	public void upELF412(L181M01A meta);

	public void upELF412_DelThenInsert(ELF412 elf412);

	public void upELF412_DelThenInsert(List<ELF412> elf412_list);

	public void upELF486_DelThenInsert(List<ELF486> elf486_list);

	public void upELF491_DelThenInsert(ELF491 elf491);

	public void upELF491_DelThenInsert(List<ELF491> elf491_list);

	public void upELF491B_DelThenInsert(List<ELF491B> elf491b_list);

	public void upELF491C_Del_by_unid(String elf491c_unid);

	public void upELF491C_DelThenInsert(List<ELF491C> elf491c_list);

	public void upELF492_Del_by_unid(String elf492_unid);

	public void upELF492_DelThenInsert(List<ELF492> elf492_list);

	public void upELF493_Del_by_rptDocId(String elf493_rptDocId);

	public void upELF493_DelThenInsert(List<ELF493> elf493_list);

	public void upELF494_DelThenInsert(ELF494 elf494);

	public void upELF495_DelThenInsert(String elf495_rptDocId,
			List<ELF495> elf495_list);

	public void upELF498_single_DelThenInsert(ELF498 elf498);

	public void upELF498_DelThenInsert(List<ELF498> elf498_list);

	public void upELF498B_Del_by_unid(String elf498b_unid);

	public void upELF498B_DelThenInsert(List<ELF498B> elf498b_list);

	/**
	 * 490 -> 491 [nChang, oChang]
	 * 
	 * 【ACLS0120_fnTransNewData_新案轉491】 ELF491_NEWFLAG= 'Y'
	 * ,ELF491_REPORTKIND='N', ELF491_REMOMO=? elf491_crdate=? elf491_TMESTAMP =
	 * ? ,ELF491_UPDATER ='nChang' , ELF491_NCKDFLAG= ''
	 * ,ELF491_NCKDDATE='0001-01-01' ,ELF491_NCKDMEMO= ''
	 * 
	 * 
	 * 【ACLS0120_fnTransOldData_舊案轉491】 ELF491_NEWFLAG= '' ,
	 * ELF491_REPORTKIND='O',ELF491_REMOMO=? , elf491_crdate=? ,
	 * elf491_TMESTAMP=? ,ELF491_UPDATER ='oChang'
	 */
	public void up491_at_490to491_match(String flag, List<ELF491> elf491_list,
			List<C241M01Z> c241m01z_list, ELF491 elf491,
			String elf491_reportkind, Date decideCrDate, String elf490_rule_no,
			String elf490_rule_no_new, String c241m01z_reasonDesc);

	public void up491_at_490to491_notMatch(List<ELF491> elf491_list,
			List<C241M01Z> c241m01z_list, ELF491 elf491, String elf490_rule_no,
			String elf490_rule_no_new, String c241m01z_reasonDesc);

	/**
	 * gfnRecheckReViewData【491->C24M01A之前,重整491】 [nRView, UnioLn]
	 * 
	 * elf491_crdate=? elf491_TMESTAMP= ? ELF491_UPDATER=? ELF491_NCKDFLAG= ''
	 * ELF491_NCKDDATE='0001-01-01' ELF491_NCKDMEMO= ''
	 */
	public void up491_at_bf_491toc241m01a(List<ELF491> elf491_list,
			List<C241M01Z> c241m01z_list, ELF491 elf491, String elf491_updater,
			String c241m01z_reasonDesc);

	public void up491_at_unmatchR1R2R4(List<ELF491> elf491_list,
			List<C241M01Z> c241m01z_list, C241M01Z c241m01z, ELF491 elf491,
			String elf491_updater, String c241m01z_reasonDesc);

	/**
	 * 【491->C24M01A之中, 覆審資料日期外之新案資料-A】 gfnDB2AutoCancelNewData [gICBCNo]
	 * ELF491_CRDATE=ShouldReviewDate ELF491_TMESTAMP=?, ELF491_UPDATER =?
	 * ELF491_NCKDFLAG=? ELF491_NCKDDATE=? ELF491_NCKDMEMO=?
	 */
	public void up491_at_491toc241m01a_nckdFlagA(List<ELF491> elf491_list,
			List<C241M01Z> c241m01z_list, ELF491 elf491, Date elf491_crdate,
			String nckdflag, Date nckdDate, String nckdmemo,
			String elf491_updater);

	/**
	 * 【saveNoCtl / saveReCtl】 fnDB2UpdateMISELF491
	 * 
	 * ELF491_CRDATE=? , ELF491_TMESTAMP=? ELF491_UPDATER =? ELF491_NCKDFLAG=?
	 * ELF491_NCKDDATE=? ELF491_NCKDMEMO=?
	 * 
	 * when NCKDFLAG=='' CRDATE=ShouldReviewDate ELF491_NCKDFLAG=''
	 * ELF491_NCKDDATE="0001-01-01" ELF491_NCKDMEMO="" when NCKDFLAG=='A'
	 * CRDATE=ShouldReviewDate ELF491_NCKDFLAG='A' ELF491_NCKDDATE="2014-01-01"
	 * ELF491_NCKDMEMO="改期覆審" when NCKDFLAG=='B, C.....Z' CRDATE=0001-01-01
	 * ELF491_NCKDFLAG='Z' ELF491_NCKDDATE="2014-01-01"
	 * ELF491_NCKDMEMO="其他依規免覆審案件"
	 */
	public void up491_at_save_NoCtl_ReCtl(List<ELF491> elf491_list,
			List<C241M01Z> c241m01z_list, String brNo,
			List<C241M01A> c241m01a_list);

	public void up491_at_init99(List<ELF491> elf491_list, ELF491 elf491,
			Date c241m01a_crdate, String elf491_remomo);

	/**
	 * gfnForKind99Trans
	 * 
	 * 由消金的產品種類, 會計科目, 反推 ELF491_MAINCUST='' ELF491_CRDATE=? , ELF491_TMESTAMP=?
	 * ELF491_UPDATER =? ELF491_NCKDFLAG= '' ELF491_NCKDDATE='0001-01-01'
	 * ELF491_NCKDMEMO= '' ELF491_REMOMO=?
	 */
	public void up491_at_deriveFrom99(List<ELF491> elf491_list,
			List<C241M01Z> c241m01z_list, ELF491 elf491,
			String elf491_reportkind, Date decideCrDate, String elf490_rule_no,
			String elf490_rule_no_new, String c241m01z_reasonDesc);

	/**
	 * 編製完成上傳時[只符合 6-1] or [只符合 8-1]: 仍上傳ELF492,但ELF491.CRDATE為0001-01-01.
	 * 
	 * 團貸母戶: ID為公司戶,不上傳491. 上傳母戶時,即使團貸子戶有輸入,不連帶上傳 (直到 user 在子戶, 人工執行上傳)
	 * 
	 * 參考: notes
	 * 
	 * 算出下次應覆審日期 If Trim(doc.DocKind(0))<>"G" Then Call
	 * gfnUpdateELF491BefCheck(doc,tNowDate, tFinalData)
	 * 
	 * If Trim(doc.DocKind(0))<>"G" Then If Not gfnDB2UpdateMISELF491(doc,
	 * tFinalData) Then Msgbox "更新覆審控制檔失敗，請洽資訊處":Exit Sub If Not
	 * gfnDB2UpdataMISELF498(doc) Then Msgbox "更新覆審稽核控制檔失敗，請洽資訊處":Exit Sub
	 * Elseif Trim(doc.DocKind(0))="G" Then If Not fnGrpUpdaateELF492Data Then
	 * Msgbox "更新覆審控制檔失敗，請洽資訊處":Exit Sub End If
	 */
	public void up491_492_498_at_c241m01aFinish(List<ELF491> elf491_list,
			List<ELF492> elf492_list, List<ELF498> elf498_list,
			C241M01A c241m01a);

	public void up491_at_c243m01aFinish(List<ELF491> elf491_list,
			List<C241M01Z> c241m01z_list, ELF491 elf491, C243M01A c243m01a,
			String approver);

	public void up491_at_proc_crossMonth_loanData(List<ELF491> elf491_list,
			List<C241M01Z> c241m01z_list, ELF491 elf491,
			String elf491_reportkind, Date decideCrDate, String elf490_rule_no,
			String elf490_rule_no_new, String c241m01z_reasonDesc);

	public void up491_at_proc_elf591_custid_not_in_ELF491(
			List<ELF491> elf491_list, List<C241M01Z> c241m01z_list,
			ELF491 elf491, Date decideCrDate, String added_rule,
			String c241m01z_reasonDesc);

	public List<DocFile> findDocFileByMainIdFieldId(String mainId,
			String fieldId);

	public boolean delfile(String fileOid);

	/**
	 * 搜尋
	 * 
	 * @param clazz
	 * @param search
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	public ISearch getMetaSearch();

	public Map<String, String> get_c241m01c_defaultVal(C241M01A c241m01a);

	public Map<String, String> get_l170m01d_defaultVal(L170M01A meta,
			boolean hasCMS, List<String> msgList);

	public void decideC241M01A_overdueYN(C241M01A c241m01a,
			List<C241M01B> ln_list);

	public List<C241A01A> auth_C241A01A_ToBr(String ownUnit, String userId,
			List<C241M01A> c241m01as);

	public TreeMap<String, String> getBranch(String logonBr);

	public C241M01Z copyBf491(ELF491 elf491);

	public void copyAf491(C241M01Z o, ELF491 elf491, String reason,
			String reasonDesc);

	public void copyAf491(C241M01Z o, String reason, String reasonDesc);

	public void classifyGuaranteeSubj(String brNo, CrsVO crvVO, String cntrNo,
			List<String> lnap2_loanNoList, List<String> lnap46_loanNoList,
			List<String> lnap135_loanNoList);

	public L140M01A findL140M01A_oid(String oid);

	public List<L140M01A> findL140M01A_oid(List<String> oid_list);

	public L180M01A findL180M01A_oid(String oid);

	public L180M01A findL180M01A(L180M01B model);

	public L180M01A findL180M01A(L170M01A l170m01a);

	public L180M01B findL180M01B_oid(String oid);

	public List<L180M01B> findL180M01B_oid(List<String> oid_list);

	public L180M01B findL180M01B(L170M01A l170m01a);

	// J-110-0272 抽樣覆審
	public L186M01A findL186M01AByUniqueKey(Date dataDate, String branchId,
			String custId, String dupNo, String randomType);

	public List<L180M01B> findL180M01BDefaultOrder(String mainId);

	public List<L180M01C> findL180M01C(L180M01B l180m01b);

	public String findL180M01C_cntrNo(L180M01B l180m01b);

	public L180M01Z findL180M01Z(Date dataDate, String branchId);

	public L181M01A findL181M01A_oid(String oid);

	public L181M01B findL181M01B_mainid_Bf(String mainId);

	public L181M01B findL181M01B_mainid_Af(String mainId);

	public boolean existL180M01Z_sysMonth(String branch);

	public boolean is_flowClass_throughBr(L170M01A meta);

	public void gfnCTL_Import_LNF025(String branch, String custId,
			String dupNo, Map<String, String> elf412_DBUCOID,
			Map<String, String> elf412_OBUCOID);

	public RO412 gfnGenCTLList_PROCESS_SHARE_GRADE(String brNo,
			String lnf022_cust_id, RO412 src_ro412,
			Map<String, String> elf412_DBUCOID,
			Map<String, String> elf412_OBUCOID);

	/**
	 * @param mode
	 * <br/>
	 *            mode = 1 產生本月覆審名單 gfnStartNorthBranch_GenList <br/>
	 *            mode = 2(已取消)產生未覆審名單 gfnGenerateCTL_FLMS180R01 <br/>
	 *            mode = 3 維護覆審名單 <br/>
	 *            mode = 4 產生未覆審名單 gfnGenerateCTL_FLMS180R01
	 * 
	 * <br/>
	 *            在 mode = 3, 算出真正的覆審日 <br/>
	 *            而在 mode = 1,2,4, 可能 baseDate 是2014/09.
	 *            若算出的日期<=baseDate,才要出現在覆審名單內
	 * 
	 * @param baseDate
	 * @param elfOCkdLine
	 * @param l181m01b
	 * @return
	 */
	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	public Date gfnCTL_Caculate_DueDate(String mode, Date baseDate,
			RO412 ro412, RO412 ro412b, RO412 ro412c);

	public void gfnCTL_Caculate_ELF412(ELF412 elf412, ELF412 nextMdElf412);

	public boolean gfnCTLCaculateFCRDTYPESixMon(String fcrdType,
			String fcrdArea, String fcrdPred, String fcrdGrad);

	public String gfnCTLCaculateFCRDTYPENM(String fcrdType, String fcrdArea,
			String fcrdPred, String fcrdGrad);

	public String[] gfnDB2_CTL_Get_LNF_MSTR(String custId, String dupNo,
			String brNo, List<String> cntrNo_list, List<String> loanTP_list,
			List<String> quotaAmt_list, List<String> balAmt_list,
			List<String> useDate_list);

	public String[] gfnCTL_Import_LNF022(String branch, String custId,
			String dupNo, Date cancelDt);

	public String[] gfnCTL_Get_Cust_Worst_Status(String branch, String custId,
			String dupNo, Date cancelDt);

	public Map<String, String> get_lrs_rpt_brMap(String remarks);

	public List<C241M01A> getCrsRetrialSummaryList(C240M01A meta);

	public List<L170M01A> getLrsRetrialSummaryList(L180M01A meta, String ctlType);

	public TreeMap<String, List<String>> getCrsRetrialSummary(
			List<C241M01A> c241m01a_list);

	public TreeMap<String, List<String>> getLrsRetrialSummary(
			List<L170M01A> l170m01a_list, Boolean section);

	public TreeMap<String, List<String>> getLrsRetrialSummary_ctlTypeB(
			List<L170M01A> l170m01a_list);

	public void saveEndFlow(Meta meta);

	public void saveReInitFlow(Meta meta);

	public String lrsInExePeriod();

	public List<C241M01C> gfnSetData_crs(C241M01A c241m01a);

	public List<L170M01D> gfnSetData_lrs(L170M01A l170m01a);

	public void gfnSetData_ptCrs(String mgrId, List<C241M01C> itemList);

	public void gfnSetData_ptLrs(String mgrId, List<L170M01D> l170m01d_list);

	public void gfnCTL_Import_CRDTTBL(L170M01E l170m01e_C, String custId,
			String dupNo);

	public void gfnCTL_Import_MONTHBAL(L170M01E l170m01e_M, String custId,
			String dupNo);

	/**
	 * 
	 * 取得最小覆審基準日
	 * 
	 * @param l170m01e_M
	 * @param custId
	 */
	public Map<String, String> gfnGetAloanRealDt(String custId, String dupNo,
			String brNo);

	/**
	 * 取得企金覆審查核事項 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 * 
	 * @return
	 */
	public Map<String, String> getLRS_ITEM_DESC_MAP();

	/**
	 * 取得企金覆審查核事項順序 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 * 
	 * @param version
	 * @param itemNo
	 * @return
	 */
	public Integer getItemSeqByItemNo(String version, String itemNo);

	/**
	 * 依MAINID 取得覆審報告表
	 * 
	 * @param mainId
	 * @return
	 */
	public L170M01A findL170M01A_mainId(String mainId);

	/**
	 * J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	 * 
	 * @param elf412b
	 */
	public void upELF412B_DelThenInsert(ELF412B elf412b);

	/**
	 * J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	 * 
	 * @param elf412b_list
	 */
	public void upELF412B_DelThenInsert(List<ELF412B> elf412b_list);

	/**
	 * 
	 * 取得最小覆審基準日 J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	 * 
	 * @param l170m01e_M
	 * @param custId
	 */
	public Map<String, String> gfnGetAloanRealDt2(String custId, String dupNo,
			String brNo);

	/**
	 * J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	 * 
	 * @param custId
	 * @param dupNo
	 * @param caseLvl
	 * @return
	 */
	public boolean chkNeedRealReview(String custId, String dupNo, String caseLvl);

	/**
	 * J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	 * 
	 * @param nckdFlag
	 * @return
	 */
	public boolean isElf412B_NckdFlagHasNoReviewFlag(String nckdFlag);

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能 取得覆審分行類別
	 * 
	 * @param unitNo
	 * @return
	 */
	public boolean chkCtlTypeByBrNo(String unitNo, String ctlType);

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 */
	public void gfnCTL_Caculate_ELF412B(ELF412B elf412b);

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 */
	public L120M01A findL120M01A_mainId(String mainId);

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 */
	public Map<String, String> get_lrs_CtlTypeMap();

	/**
	 * J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	 * 
	 * @param elf412b
	 */
	public void upELF412B(L181M01A meta);

	/**
	 * J-106-0278-002 Web e-Loan國內企金授信配合實地覆審作業，額度簽報明細表增加聯貸案件管理行之建檔及修改實地覆審相關檢核
	 */
	public String chkNckdFlag_1(String elfBranch, String elfCustId,
			String elfDupNo, String elfCtlType);

	/**
	 * J-106-0278-002 Web e-Loan國內企金授信配合實地覆審作業，額度簽報明細表增加聯貸案件管理行之建檔及修改實地覆審相關檢核
	 */
	public String chkNckdFlag_E(String elfBranch, String elfCustId,
			String elfDupNo, String elfCtlType);

	public boolean inBaseDate(Date baseDate, Date calcDate);

	/**
	 * J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	public boolean isPricePperformanceGuarantee(String custId, String dupNo,
			String cntrNo);

	/**
	 * J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	 * 
	 * @param elf412b
	 */
	public void upELF412C(L181M01A meta);

	/**
	 * J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	 * 
	 * @param elf412c
	 */
	public void upELF412C_DelThenInsert(ELF412C elf412c);

	/**
	 * J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	 * 
	 * @param elf412c_list
	 */
	public void upELF412C_DelThenInsert(List<ELF412C> elf412c_list);

	/**
	 * J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	 */
	public void gfnCTL_Caculate_ELF412C(ELF412C elf412c);

	/**
	 * J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	 */
	public String[] gfnCTL_Import_LNF022_with_ctlType(String branch,
			String custId, String dupNo, Date cancelDt, String ctlType);

	/**
	 * J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	 * 除了判斷LNF022狀態，還有判斷cltType與LNF020
	 * 當=>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
	 * 主辦覆審/自辦覆審，但只有價金履約額度，視為銷戶。 價金履約覆審，但只有一般授信額度，視為銷戶。
	 */
	public String[] gfnReChkCancelAfter_CTL_Import_LNF022(String branch,
			String custId, String dupNo, String[] arr, String ctlType);

	/**
	 * J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	 */
	public TreeMap<String, List<String>> getLrsRetrialSummary_ctlTypeC(
			List<L170M01A> l170m01a_list);

	/**
	 * J-108-0036_05097_B1001 修改web e-loan授信覆審系統，修改覆審控制檔,得變更授信覆審期日。
	 * 
	 * @param brNo
	 * @param ndDate
	 * @param baseDate
	 * @return
	 */
	public String isLrDateNeedAdjustForLunarNewYear(String strNdDate,
			String brNo, Date baseDate);

	/**
	 * J-110-0309 為因應疫情影響之覆審補辦措施，請協助調整每月逾期報表
	 * 
	 * @param strNdDate
	 * @param brNo
	 * @param baseDate
	 * @return
	 */
	public String isLrDateNeedAdjust(String strNdDate, String brNo,
			Date baseDate);

	/**
	 * J-108-0078_05097_B1001 Web e-Loan企金授信覆審系統修改首次往來之新授信戶應辦理覆審之期限
	 * 
	 * @param baseDate
	 * @return
	 */
	public boolean isRckdLine_I_Effective(Date baseDate);

	/**
	 * J-108-0128_05097_B1001 Web e-Loan企金授信覆審系統修改覆審報告表內容。
	 */
	public String getLatestRetrialItemVer_OverSea(L170M01A l170m01a);

	/**
	 * J-108-0128_05097_B1001 Web e-Loan企金授信覆審系統修改覆審報告表內容。
	 * 
	 * @param ELF412_MOWTBL1
	 * @param ELF412_MOWTYPE
	 * @param ELF412_CRDTTBL
	 * @param ELF412_FCRDTYPE
	 * @param ELF412_FCRDAREA
	 * @param ELF412_FCRDPRED
	 * @param ELF412_FCRDGRAD
	 * @return
	 */
	public boolean isBadMow__CrdtTbl_GE_D__FCRDTYPESixMon(
			String ELF412_MOWTBL1, String ELF412_MOWTYPE,
			String ELF412_CRDTTBL, String ELF412_FCRDTYPE,
			String ELF412_FCRDAREA, String ELF412_FCRDPRED,
			String ELF412_FCRDGRAD);

	/**
	 * 
	 * @param version
	 * @param itemNo
	 * @return
	 */
	public String getItemSeqShowByItemNo(String version, String itemNo);

	/**
	 * J-108-0268 覆審案件 客戶逾期情形
	 */
	public Map<String, Object> getOverDueData(Date qryDate, String custId,
			String dupNo);

	/**
	 * J-108-0260 海外覆審檢視表 取得檢核表各項title
	 */
	public String[] getChkListSpan(String reviewType,
			HashMap<String, String> itemMap, Properties prop);

	// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
	// 判斷是否為 純紓困客戶
	public String[] getOnlyRescueCase(String branch, String custId,
			String dupNo, String cntrNo);

	public boolean getOnlyRescueCase_ByCntrNo(String cntrNo);

	// J-109-0313 小規模覆審 - 判斷是否為純小規模
	public L180M01B getOnlySmallBussCaseC(L180M01B l180m01b);

	// J-109-0313 小規模覆審 - 計算抽樣率
	public BigDecimal cauculateSamplingRate(String l180m01aMainId, String type);

	/**
	 * J-109-0456
	 * 授信有效額度新臺幣五百萬元(含)以下且經信保七成(含)以上之不循環動用案件，除新做、增貸案件應於撥貸後之半年內辦理覆審外，免再辦理覆審。
	 * J-110-0272 抽樣覆審
	 */
	public boolean isNckd_12(String custId, String dupNo);

	/**
	 * J-110-0272 抽樣覆審
	 */
	public boolean isFullGuarantee(String custId, String dupNo, String date);

	public boolean isSamplingType(String custId, String dupNo, String date);

	public int[] getRandomInt(int start, int end, int count);

	public int randomInteger(int min, int max);

	public List<Map<String, Object>> getNeedSamplingList(String mainId,
			boolean byCnt, int samplingCnt);

	public BigDecimal cauculateRandomSamplingRate(String l180m01aMainId,
			String type);

	/**
	 * J-110-0308 覆審考核表
	 */
	public boolean hidePaFormPanel();

	public String getPaFormVer(L170M01A l170m01a);

	public String getPaFormVerByDate(Date verDate);

	public String[] getPaCol(String verStr, String type);

	public String buildPaFormHtml(L170M01A l170m01a, Properties prop);

	/**
	 * 產生覆審考核表HTML企消金通用
	 * 
	 * @param Meta
	 *            (L170M01A C241M01A)
	 * @param prop
	 * @return
	 * @throws CapException
	 */
	public String buildPaFormHtml(Meta l170m01a, Properties prop)
			throws CapException;

	/**
	 * J-110-0505_05097_B1001 Web
	 * e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
	 * 
	 * @param oid_list
	 * @return
	 */
	List<L170M01A> findL170M01A_oid(List<String> oid_list);

	/**
	 * J-110-0505_05097_B1001 Web
	 * e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
	 * 
	 * @param cntrNo_Set
	 * @return
	 */
	public List<Map<String, Object>> findPrint_L140M01A_By_CntrNos(
			Set<String> cntrNo_Set);

	/**
	 * J-110-0505_05097_B1001 Web
	 * e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
	 * 
	 * @param cntrNo_Set
	 * @return
	 */
	List<Map<String, Object>> findPrint_L140M01A_By_CustId(String custId,
			String dupNo, Set<String> cntrNo_list);

	/**
	 * J-111-0560 配合授信審查處，Web-eloan授信管理系統，覆審作業聯徵資料PPA已查詢部份,增加一鍵查詢功能，自動比對債票信及卡信資料
	 * 傳入L170M01A=企金覆審報告表主檔 傳入C241M01A=消金覆審報告表主檔
	 */
	public Map<String, String> getEjcicReusltRecord(GenericBean bean,
			boolean doSave);

	/**
	 * J-111-0622_05097_B1001 Web
	 * e-Loan配合本行授信覆審作業須知111.12.1修訂，修改E-Loan系統企金授信覆審作業系統
	 * 
	 * @param brNo
	 * @return
	 */
	public String getRetrailNewBranch(String brNo);

	/**
	 * J-111-0622_05097_B1001 Web
	 * e-Loan配合本行授信覆審作業須知111.12.1修訂，修改E-Loan系統企金授信覆審作業系統
	 * 
	 * @param brNo
	 * @return
	 */
	public String[] getRetrailSpecialBranch();

	/**
	 * J-111-0622_05097_B1002 Web
	 * e-Loan配合本行授信覆審作業須知111.12.1修訂，修改E-Loan系統企金授信覆審作業系統
	 * 
	 * 企金覆審用
	 * 
	 * 007、201的覆審報告表，覆審種類為 CTLTYPE
	 * A、C案件，覆審日期大於2023/4，改為931覆審，一切要比照目前931的做法(檢核、列印)
	 * 
	 * @param l170m01a
	 * @return
	 */
	public boolean isSpecialBranchChgTo931_lrs(L170M01A l170m01a);

	/**
	 * J-111-0622_05097_B1002 Web
	 * e-Loan配合本行授信覆審作業須知111.12.1修訂，修改E-Loan系統企金授信覆審作業系統
	 * 
	 * 消金覆審用
	 * 
	 * 007、201的覆審報告表，覆審種類為 CTLTYPE
	 * A、C案件，覆審日期大於2023/4，改為931覆審，一切要比照目前931的做法(檢核、列印)
	 * 
	 * @param c240m01a
	 * @return
	 */
	public boolean isSpecialBranchChgTo931_crs(C241M01A c241m01a);

	/**
	 * J-111-0554 配合授審處增進管理效益，修改相關功能程式 add 列印擔保品設定資料表
	 * 
	 * @param cntrNo_Set
	 * @return
	 */
	List<Map<String, Object>> findCMS_C100m01byCntrno(Set<String> cntrNo_Set);

	List<Map<String, Object>> findCMS_C100m01byMainId(Set<String> mainId_Set);

    /**
     * J-113-0519 e-Loan授信覆審作業系統之覆審名單篩選條件修正
     *
     */
    int updateRetrialDataJ1130519();
}
