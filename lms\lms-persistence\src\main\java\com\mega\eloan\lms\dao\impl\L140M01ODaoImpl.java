/* 
 * L140M01ODaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L140M01ODao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140M01O;

/** 擔保品資料明細檔 **/
@Repository
public class L140M01ODaoImpl extends LMSJpaDao<L140M01O, String> implements
		L140M01ODao {

	@Override
	public L140M01O findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140M01O> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		LinkedHashMap<String, Boolean> orderByMap = new LinkedHashMap<String, Boolean>();
		orderByMap.put("collTyp1", false);
		orderByMap.put("seqNo", false);
		orderByMap.put("createTime", false);
		search.setOrderBy(orderByMap);
		List<L140M01O> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L140M01O findByUniqueKey(String mainId, Integer seqNo) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seqNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seqNo", seqNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L140M01O> findByIndex01(String mainId, Integer seqNo) {
		ISearch search = createSearchTemplete();
		List<L140M01O> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seqNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seqNo", seqNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140M01O> findByIndex02(String mainId, String collNo,
			String custId) {
		ISearch search = createSearchTemplete();
		List<L140M01O> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (collNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "collNo", collNo);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140M01O> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		List<L140M01O> list = null;
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140M01O> findByCustIdDupId(String custId, String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L140M01O> list = createQuery(L140M01O.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L140M01O> findByMainIdOrderByCcollTyp1(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		LinkedHashMap<String, Boolean> orderByMap = new LinkedHashMap<String, Boolean>();
		orderByMap.put("collTyp1", false);
		orderByMap.put("collTyp2", false);
		search.setOrderBy(orderByMap);
		List<L140M01O> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L140M01O> findByMainIdOrderForLgd(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		LinkedHashMap<String, Boolean> orderByMap = new LinkedHashMap<String, Boolean>();
		orderByMap.put("custId", false);
		orderByMap.put("collNo", false);
		orderByMap.put("estDate", true);
		orderByMap.put("loanTwd", true);
		search.setOrderBy(orderByMap);
		List<L140M01O> list = createQuery(search).getResultList();
		return list;
	}

}