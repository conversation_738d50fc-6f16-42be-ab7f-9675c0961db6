/* 
 * MisEJF334Service.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * AAS103->EJV33401->EJF334法人戶戶名,身分證補發,通報,補充註記
 * </pre>
 * 
 * @since 2012/3/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/3/22,<PERSON>,new
 *          </ul>
 */
public interface MisEJF334Service {

	/**
	 * 查詢 中文戶名
	 * 
	 * @param custId
	 *            客戶統編
	 * @return Map
	 */
	Map<String, Object> findCNameById(String custId);

	/**
	 * 以其集團公司名單, 引進該集團轄下公司的負責人資料
	 * @param readId 集團公司名單
	 * @return Map
	 */
	Map<String, Object> findPNameByBan(String readId);
	
	List<Map<String, Object>> findPNameByBans(List<String> readIds);
}
