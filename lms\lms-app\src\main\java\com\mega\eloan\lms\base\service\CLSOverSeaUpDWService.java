package com.mega.eloan.lms.base.service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.OTS_RKADJUSTOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKAPPLICANTOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKCNTRNOOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKCOLLOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKCREDITOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKJCICOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKPROJECTOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKSCOREOVS;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121S01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01C;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.service.ICapService;

public interface CLSOverSeaUpDWService extends ICapService {
	public List<OTS_RKSCOREOVS> upDW_OVS_RKSCORE(List<OTS_RKSCOREOVS> data,
			L120M01A l120m01a, L140M01A l140m01a, L140M01C l140m01c,
			C121M01A c121m01a, C120M01A c120m01a, Date ratingDate,
			Timestamp nowTS, Map<String, String> loanTp_actCodeMap,
			String dDOCSTATUS, GenericBean c121m01_grade);
	
	
	public List<OTS_RKCREDITOVS> upDW_OVS_RKCREDIT(List<OTS_RKCREDITOVS> data,
			L120M01A l120m01a, L140M01A l140m01a, L140M01C l140m01c,
			C121M01A c121m01a, C121M01A c121m01a_docStatus05O,
			C120M01A c120m01a, C120S01E c120s01e, Date ratingDate,
			Timestamp nowTS, Map<String, String> loanTp_actCodeMap,
			String dDOCSTATUS, GenericBean c121m01_grade);
	
	
	public List<OTS_RKJCICOVS> upDW_OVS_RKJCIC(List<OTS_RKJCICOVS> data,
			L120M01A l120m01a, L140M01A l140m01a, L140M01C l140m01c,
			C121M01A c121m01a, C120M01A c120m01a, C120S01E c120s01e,
			Date ratingDate, Timestamp nowTS,
			Map<String, String> loanTp_actCodeMap, String dDOCSTATUS,
			GenericBean c121m01_grade);
			
	public List<OTS_RKADJUSTOVS> upDW_OVS_RKADJUST(List<OTS_RKADJUSTOVS> data,
			L120M01A l120m01a, L140M01A l140m01a, L140M01C l140m01c,
			C121M01A c121m01a, C120M01A c120m01a, Date ratingDate,
			Timestamp nowTS, Map<String, String> loanTp_actCodeMap,
			String dDOCSTATUS, GenericBean c121m01_grade);	
	
	public List<OTS_RKAPPLICANTOVS> upDW_OVS_RKAPPLICANT(
			List<OTS_RKAPPLICANTOVS> data, L120M01A l120m01a,
			L140M01A l140m01a, L140M01C l140m01c, C121M01A c121m01a,
			C120M01A c120m01a, C120S01A c120s01a, C120S01B c120s01b,
			C120S01C c120s01c, Date ratingDate, Timestamp nowTS,
			Map<String, String> loanTp_actCodeMap, String dDOCSTATUS,
			GenericBean c121m01_grade);
	
	public List<OTS_RKCOLLOVS> upDW_OVS_RKCOLL(List<OTS_RKCOLLOVS> data,
			L120M01A l120m01a, L140M01A l140m01a, L140M01C l140m01c,
			C121M01A c121m01a, C120M01A c120m01a, Date ratingDate,
			Timestamp nowTS, Map<String, String> loanTp_actCodeMap,
			String dDOCSTATUS, C121S01A c121s01a);
	
	public List<OTS_RKPROJECTOVS> upDW_OVS_RKPROJECT(
			List<OTS_RKPROJECTOVS> data, L120M01A l120m01a, L140M01A l140m01a,
			L140M01C l140m01c, C121M01A c121m01a, C120M01A c120m01a,
			Date ratingDate, Timestamp nowTS,
			Map<String, String> loanTp_actCodeMap, String dDOCSTATUS);
	
	public List<OTS_RKCNTRNOOVS> upDW_OVS_RKCNTRNO(List<OTS_RKCNTRNOOVS> data,
			L120M01A l120m01a, L140M01A l140m01a, L140M01C l140m01c,
			C121M01A c121m01a, C120M01A c120m01a, Date ratingDate,
			Timestamp nowTS, Map<String, String> loanTp_actCodeMap,
			String dDOCSTATUS);


	public void updateELF026(L120M01A l120m01a, L140M01A l140m01a,
			L140M01C l140m01c, 
			C121M01A c121m01a, C121M01A c121m01a_docStatus05O,
			C120M01A c120m01a,
			Date ratingDate, Timestamp nowTS,
			Map<String, String> loanTp_actCodeMap, GenericBean c121m01_grade);
	
	public boolean updateELF026Flag();

}
