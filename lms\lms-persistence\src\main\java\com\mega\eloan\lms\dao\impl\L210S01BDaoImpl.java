/* 
 * L210S01BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L210S01BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L210S01B;

/** 同業聯貸攤貸比率檔 **/
@Repository
public class L210S01BDaoImpl extends LMSJpaDao<L210S01B, String> implements
		L210S01BDao {

	@Override
	public L210S01B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L210S01B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L210S01B> list = createQuery(L210S01B.class, search).getResultList();
		return list;
	}

	@Override
	public L210S01B findByUniqueKey(String mainId, Integer seq, String chgFlag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		search.addSearchModeParameters(SearchMode.EQUALS, "chgFlag", chgFlag);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L210S01B> findByIndex01(String mainId, Integer seq,
			String chgFlag) {
		ISearch search = createSearchTemplete();
		List<L210S01B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		if (chgFlag != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "chgFlag",
					chgFlag);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L210S01B.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L210S01B> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<L210S01B> list = createQuery(L210S01B.class, search).getResultList();
		return list;
	}

	@Override
	public List<L210S01B> findByMainIdAndChgFlag(String mainId, String chgFlag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "chgFlag", chgFlag);
		List<L210S01B> list = createQuery(L210S01B.class, search).getResultList();
		return list;
	}
}