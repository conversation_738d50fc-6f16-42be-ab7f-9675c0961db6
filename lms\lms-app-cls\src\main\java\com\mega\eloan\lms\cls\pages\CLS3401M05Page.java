package com.mega.eloan.lms.cls.pages;

import java.util.List;

import org.kordamp.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.panels.CLS3401S051Panel;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.C340M01C;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 擔保物提供人購屋房貸增貸線上同意書(C340M01A_CtrType.Type_S)
 * </pre>
 * 
 * @since 2020/11/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/11/26,EL08034,J-109-0232
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls3401m05/{page}")
public class CLS3401M05Page extends AbstractEloanForm { 

	@Autowired
	CLSService clsService;
	
	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		String mainId = params.getString(EloanConstants.MAIN_ID);
		C340M01A meta = null;
		JSONObject jsonObject = null;
		if(Util.isNotEmpty(mainId)){
			meta = clsService.findC340M01A_mainId(mainId);
			
			if(meta!=null){
				List<C340M01C> c340m01c_list = clsService.findC340M01C(meta.getMainId());
				if(c340m01c_list.size()>0){
					C340M01C c340m01c = c340m01c_list.get(0);
					String jsonData = Util.trim(c340m01c.getJsonData());
					try {
						jsonObject = DataParse.toJSON(jsonData);
					} catch (CapException e) {
						
					}
				}
			}
		}
		
		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params,
				getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));
		
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params,
				getDomainClass(), AuthType.Accept, CreditDocStatusEnum.海外_待覆核));

		addAclLabel(model, new AclLabel("_btnCANCEL", params, getDomainClass(),
				AuthType.Accept, CreditDocStatusEnum.海外_已核准));
		
		renderJsI18N(CLS3401M02Page.class);
		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2);
		Panel panel = getPanel(page, params, meta);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
	}
	
	public Panel getPanel(int index, PageParameters params, C340M01A meta) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new CLS3401S051Panel(TAB_CTX, true, meta);
			break;
		default:
			panel = new CLS3401S051Panel(TAB_CTX, true, meta);
			break;
		}
		return panel;
	}
	
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return C340M01A.class;
	}
}
