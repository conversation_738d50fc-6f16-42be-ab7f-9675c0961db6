---------------------------------------------------------
-- LMS.L999M01A 企金約據書主檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L999M01A;
CREATE TABLE LMS.L999M01A (
	OID           CHAR(32)      not null,
	UID           CHAR(32)     ,
	<PERSON><PERSON><PERSON>        CHAR(32)     ,
	TYPCD         CHAR(1)      ,
	<PERSON><PERSON><PERSON><PERSON>        VARCHAR(10)  ,
	<PERSON><PERSON><PERSON><PERSON>         CHAR(1)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>      VARCHAR(120) ,
	UNITTYP<PERSON>      CHAR(1)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>       CHAR(3)      ,
	DOC<PERSON>ATUS     VARCHAR(3)   ,
	RANDOMCODE    CHAR(32)     ,
	DOCURL        VARCHAR(40)  ,
	TXCODE        CHAR(6)      ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,
	APPROVER      CHAR(6)      ,
	APPROVETIME   TIMESTAMP    ,
	ISCLOSED      CHAR(1)      ,
	DELETEDTIME   TIMESTAMP    ,
	SRCMAINID     CHAR(32)     ,
	CASEYEAR      DECIMAL(4,0) ,
	CASEBRID      CHAR(3)      ,
	CASESEQ       DECIMAL(5,0) ,
	CASENO        VARCHAR(62)  ,
	CASEDATE      DATE         ,
	CONTRACTTYPE  CHAR(2)      ,
	CONTRACTWORD  VARCHAR(30)  ,
	CONTRACTNO    VARCHAR(20)  ,
	CONTRACTRATE  DECIMAL(7,4) ,
	DATAUSEFLAG   CHAR(1)      ,
	DATAUSEITEM   DECIMAL(3,0) ,
	COURTCODE     CHAR(3)      ,
	ADDRZIP       DECIMAL(5)   ,
	ADDRCITY      VARCHAR(12)  ,
	ADDRTOWN      VARCHAR(12)  ,
	ADDR          VARCHAR(120) ,
	RPTID         VARCHAR(32)  ,

	constraint P_L999M01A PRIMARY KEY(OID)
) IN  EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L999M01A IS '企金約據書主檔';
COMMENT ON LMS.L999M01A (
	OID           IS 'oid', 
	UID           IS 'uid', 
	MAINID        IS '文件編號', 
	TYPCD         IS '區部別', 
	CUSTID        IS '統一編號', 
	DUPNO         IS '重覆序號', 
	CUSTNAME      IS '客戶名稱', 
	UNITTYPE      IS '辦理單位類別', 
	OWNBRID       IS '編製單位代號', 
	DOCSTATUS     IS '目前文件狀態', 
	RANDOMCODE    IS '文件亂碼', 
	DOCURL        IS '文件URL', 
	TXCODE        IS '交易代碼', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期', 
	APPROVER      IS '核准人員號碼', 
	APPROVETIME   IS '核准日期', 
	ISCLOSED      IS '是否結案', 
	DELETEDTIME   IS '刪除註記', 
	SRCMAINID     IS '來源文件編號', 
	CASEYEAR      IS '案件號碼-年度', 
	CASEBRID      IS '案件號碼-分行', 
	CASESEQ       IS '案件號碼-流水號', 
	CASENO        IS '案件號碼', 
	CASEDATE      IS '簽案日期', 
	CONTRACTTYPE  IS '約據書種類', 
	CONTRACTWORD  IS '連保書字號(字)', 
	CONTRACTNO    IS '約據書編號/連保書字號(號)', 
	CONTRACTRATE  IS '基準利率及調整', 
	DATAUSEFLAG   IS '資料保密_是否同意', 
	DATAUSEITEM   IS '資料保密_同意項目', 
	COURTCODE     IS '管轄法院', 
	ADDRZIP       IS '戶籍地址郵遞區號', 
	ADDRCITY      IS '戶籍地址(縣市)', 
	ADDRTOWN      IS '戶籍地址(區鄉鎮市)', 
	ADDR          IS '戶籍地址', 
	RPTID         IS 'RPTID'
);
