package tw.com.jcs.flow.node;

import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map.Entry;
import java.util.Stack;

import tw.com.jcs.flow.core.FlowDefinitionImpl;
import tw.com.jcs.flow.core.FlowInstanceImpl;
import tw.com.jcs.flow.service.impl.FlowServiceImpl;

/**
 * <pre>
 * ForkNode
 * TODO
 * </pre>
 * 
 * @since 2023年1月9日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月9日
 *          </ul>
 */
public class ForkNode extends FlowNode {

    public static final String FORK_STACK = "_forkStack_";

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.node.FlowNode#next(tw.com.jcs.flow.core.FlowInstanceImpl)
     */
    @Override
    public void next(FlowInstanceImpl instance) {
        instance.handle();
        FlowDefinitionImpl definition = instance.getDefinition();
        finishCurrentNode(instance);
        changeToThisNode(instance);

        // 如果有設定預設路徑，則父流程走預設路徑，否則父流程走第一條路徑
        Entry<String, String> pEntry = null;
        List<Entry<String, String>> subEntry = new LinkedList<Entry<String, String>>();

        Iterator<Entry<String, String>> iter = transitions.entrySet().iterator();
        pEntry = iter.next();
        if (defaultTransition != null && !pEntry.getKey().equals(defaultTransition)) {
            subEntry.add(pEntry);
            pEntry = null;
        }
        while (iter.hasNext()) {
            Entry<String, String> entry = iter.next();
            if (entry.getKey().equals(defaultTransition)) {
                pEntry = entry;
            } else {
                subEntry.add(entry);
            }
        }

        // 執行所有子流程
        for (Entry<String, String> entry : subEntry) {
            FlowInstanceImpl subInst = ((FlowServiceImpl) instance.getEngine().getService()).forkSubFlow(definition, instance, name, entry.getKey());

            pushToForkStack(instance, name);
            FlowNode subCurrNode = definition.getNodes().get(entry.getValue());
            if (!(subCurrNode instanceof StateNode)) {
                subInst.next();
            }
        }
        // 執行父流程
        FlowNode nextNode = definition.getNodes().get(pEntry.getValue());
        nextNode.next(instance);
    }

    /**
     * 將節點名稱(狀態)放進stack中
     * 
     * @param instance
     *            流程實體
     * @param name
     *            節點名稱(狀態)
     */
    public static void pushToForkStack(FlowInstanceImpl instance, String name) {
        Stack<String> stack = getForkStack(instance, true);
        stack.push(name);
    }

    /**
     * 拋出stack
     * 
     * @param instance
     *            流程實體
     * @return
     */
    public static String popFromForkStack(FlowInstanceImpl instance) {
        Stack<String> stack = getForkStack(instance, false);
        if (stack != null && stack.size() > 0) {
            return stack.pop();
        }
        return null;
    }

    /**
     * 窺視stack
     * 
     * @param instance
     *            流程實體
     * @return
     */
    public static String peekFromForkStack(FlowInstanceImpl instance) {
        Stack<String> stack = getForkStack(instance, false);
        if (stack != null && stack.size() > 0) {
            return stack.peek();
        }
        return null;
    }

    /**
     * 取得流程實體的stack 若無stack則新建一個
     * 
     * @param instance
     *            流程實體
     * @param create
     *            是否創建一個stack
     * @return
     */
    @SuppressWarnings("unchecked")
    static Stack<String> getForkStack(FlowInstanceImpl instance, boolean create) {
        Object _stack = instance.getAttribute(FORK_STACK);
        if (_stack == null) {
            if (create) {
                _stack = new Stack<String>();
                instance.setAttribute(FORK_STACK, _stack);
            } else {
                return null;
            }
        }
        return (Stack<String>) _stack;
    }

}
