package com.mega.eloan.lms.rpt.report;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import tw.com.iisi.cap.exception.CapException;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.LMSBATCH;

public interface LMS9511R01RptService extends AbstractService {

	/**
	 * <pre>
	 * 產生PDF檔
	 * 已敘做授信案件清單
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R01Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * <pre>
	 * 產生PDF檔
	 * 授信契約已逾期控制表
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R05Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * <pre>
	 * 產生PDF檔
	 * 信保案件未動用屆期清單
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R10Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * <pre>
	 * 產生PDF檔
	 * 區域中心每日授權外授信案件清單
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R15Report(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * <pre>
	 * 產生PDF檔
	 * 本行各營業單位各級授權範圍內承做授信案件統計表
	 * @param mainId
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws Exception
	 */
	String generateLMS180R17Report(String mainId) throws CapException,
			IOException, Exception;

	String generateLMS180R23Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateLMS180R23BReport(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String generateCLS180R17Report(LMSBATCH batch) throws CapException,
			IOException, Exception;

	String tranSportLMS180R50Pdf(String mainId) throws CapException,
			IOException, Exception;

	/**
	 * J-110-0403_05097_B1001 新增按月提供近一年(上年度1月至今年度最近月份)分行授權內企金業務「已敘做授信案件清單」月報予稽核處
	 * @param l120s01a
	 * @param l120s01cList
	 * @param crdTypeMap
	 * @param prop
	 * @param l140m01a
	 * @return
	 */
	String setL120S01CData(L120S01A l120s01a, List<L120S01C> l120s01cList,
			Map<String, String> crdTypeMap, Properties prop, L140M01A l140m01a);

}
