/* 
 * C140M01ADaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C140M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C140M01A;
import com.mega.eloan.lms.model.C140M01A_;

/**
 * <pre>
 * 徵信調查報告書主檔
 * </pre>
 * 
 * @since 2011/9/20
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
@Repository
public class C140M01ADaoImpl extends LMSJpaDao<C140M01A, String> implements
		C140M01ADao {
	
	/* (non-Javadoc)
	 * @see com.mega.eloan.ces.ces.dao.C140M01ADao#deleteC140(java.lang.String)
	 */
	@Override
	public void deleteC140(String mainId) {
		// call ces.deleteC140(?1)
		Query query = getEntityManager().createNamedQuery("c140m01a.deleteC140");
		query.setParameter(1, mainId);
		query.executeUpdate();
	}
	
	@Override
	public C140M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();	
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140M01A_.mainId.getName(), mainId);
		return findUniqueOrNone(search);
	}
}// ;
