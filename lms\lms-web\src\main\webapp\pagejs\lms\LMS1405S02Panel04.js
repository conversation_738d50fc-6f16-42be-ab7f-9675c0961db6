var dfd41 = new $.Deferred();
var dfd42 = new $.Deferred();

initAll.done(function(inits){
    dfd41.done(gridviewRate);//利費率
    dfd42.done(initSelect);//下拉選單
    //====================button event ================================
    $("#rateBtSpan").find("#twBT").click(function(){//利費率登錄新台幣
        openRateBox('1');
    }).end().find("#usBT").click(function(){//利費率登錄美金
        openRateBox('2');
    }).end().find("#jpBT").click(function(){//利費率登錄日幣
        openRateBox('3');
    }).end().find("#euBT").click(function(){//利費率登錄歐元
        openRateBox('4');
	}).end().find("#thBT").click(function(){//利費率登錄歐元
        openRateBox('7');	
    }).end().find("#otherBT").click(function(){//利費率登錄雜幣
        openRateBox('5');
    }).end().find("#rateBT").click(function(){//利費率登錄費率
        openRateBox2();
    });
    
    $("#rateBox2").find("#businessBT").click(function(){//利費率商業本票保證
        openRateChildrenBox('cpType');
    }).end().find("#promiseBT").click(function(){//利費率開發保證函
        openRateChildrenBox('cfType');
    }).end().find("#companyBT").click(function(){//利費率承兌費率
        openRateChildrenBox('cpyType');
    }).end().find("#thickRateBT").click(function(){//利費率登入新台幣
        openRateChildrenBox('paType');
    });
    
    /**  新增利率  */
    $("#newRateBt").click(function(){
        newRate(null, null, "");
    });
    
    /**  更新gird  */
    $("#lms140Tab04").click(function(){
        dfd41.resolve();//打開利費率的grid
        $("#gridviewRate").jqGrid("setGridParam", {
            sortname: 'createTime',
            sortorder: 'asc',
            postData: {
                formAction: "queryL140m01f",
                tabFormMainId: $("#tabFormMainId").val()
            },
            search: true
        }).trigger("reloadGrid");
        
        //加入前面登錄的授信科目
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "queryL140m01c",
                tabFormMainId: $("#tabFormMainId").val()
            },
            success: function(obj){
                //刪除原本的項目
                $("#itemSpan_loanTPList").remove();
                $("#loanTPListLocal").html("<input type='checkbox' id='loanTPList' name='loanTPList' value='' />");
                //帶入現請額度目前的幣別
                $("#moneyNowSapn").html($("#currentApplyCurr").val());
                $("#itemShowTr").html("");
                if (obj.count != "0") {
                    $("input[name=loanTPList]").setItems({//利費率第一頁籤授信科目checkbok
                        item: obj.item
                    });
                    $("#itemShowTr").html(obj.str);
                }//close if
            }//close success function
        });//close ajax
    });
    
    
    /** 組成利費率字串  */
    $("#tabRateDrcLink").click(function(){
        $.ajax({
            handler: inits.fhandle,
            data: {//把資料轉成json
                formAction: "saveDscr2",//儲存所有利費率描述
                tabFormMainId: $("#tabFormMainId").val(),
                pageNum: $("#pageNum2").val(),
                showMsg: true
            },
            success: function(responseData){
                $("#itemDscr2").val(responseData.str);
                
            }
        }); //close ajax
    });
    
    
    $(".item1BBbt,.item1CBbt,.item1CCbt,.item1CDbt").click(function(){
        var $thisId = $(this).attr("class").slice(0, 7);
        $("#openSelectBox2").thickbox({//加碼基準 一二三 六個月
            //L140M01a.select=選取關鍵字
            title: i18n.lms1405s02["L140M01a.select"],
            width: 300,
            height: 250,
            align: "center",
            i18n: i18n.def,
            valign: "bottom",
            buttons: {
                "sure": function(){
                    $.thickbox.close();
                    var newSelectName = $("#newSelectItem2").val();
                    if (newSelectName != "") {
                        if ($("#openSelectList2 option[value=" + newSelectName + "]").text() == "") {//判斷是否已經有這個值
                            $("#openSelectList2").append($("<option/>").attr("value", newSelectName).text(newSelectName));//新增select選項
                            $("#openSelectList2").val(newSelectName);
                        }
                    }
                    $("#" + $thisId).val($("#openSelectList2").val());
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
    
    $("#wordBase").click(function(){
        //L140M01h.say=L/C有效期跟已承兌期間重疊部份免收，承兌期間超過有效期者，按實際超過天數以年率XXX%計收，每筆最低計費為
        //other.money=元
        $("#paDes").val(i18n.lms1405s02["L140M01h.say"] + "400" + i18n.lms1405s02["other.money"]);
    });
    
    /**  清除幣別欄位內容  */
    $("#clearRate").click(function(){
        $("#L140M01HForm").reset();
        $(".rateSpan1,.rateSpan2,.rateSpan3,.rateSpan4").hide();
    });
    
    /** 刪除利費率登錄  */
    $("#removeRate").click(function(){
        var gridID = $("#gridviewRate").getGridParam('selarrrow');
        if (gridID == "") {
            //TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            
        }
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var gridIDList = [], gridSeqList = [];
                for (var i = 0; i < gridID.length; i++) {
                    gridIDList[i] = $("#gridviewRate").getRowData(gridID[i]).oid;
                    gridSeqList[i] = $("#gridviewRate").getRowData(gridID[i]).rateSeq;
                }
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteL140m01f",
                        tabFormMainId: $("#tabFormMainId").val(),
                        Idlist: gridIDList,
                        Seqlist: gridSeqList
                    },
                    success: function(obj){
                        if (obj && obj.drc) {
                            $("#itemDscr2").val(obj.drc);
                        }
                        $("#gridviewRate").trigger("reloadGrid");
                    }
                });
            }
        });
    });
    
    $("#rateToWordBt").click(function(){
        rateToWord();
    });
    
    $("#wordBaseBt2").click(function(){
        //L140M01h.pa2Rate=費率、L140M01h.wordBase2=依本行規定計收
        $("#othDes").val("ＸＸ" + i18n.lms1405s02["L140M01h.pa2Rate"] + "：" + i18n.lms1405s02["L140M01h.wordBase2"]);
    });
    
    /**  引進利率  */
    $(".rateNum").click(function(){
        var $thisId = $(this).attr("id").replace("BT", ""), type = '', curr = '';
        switch ($thisId) {
            case "item1A":
                type = "6D";//本行基準利率
                curr = "TWD";
                break;
            case "item1Q":
                type = "6S";//6S基準利率月指標利率
                curr = "TWD";
                break;
            case "item1S":
                type = "QX";//6165初級市場ＣＰ九十天期(KX)平均利率
                curr = "TWD";
                break;
        }//close switch
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "queryBaseRate",
                type: type,
                curr: curr
            },
            success: function(obj){
                $("#" + $thisId).val(obj.rate);
            }//close success
        });//close ajax
    });
    
    
    $("#openSelectBt").click(function(){
        $("#openSelectBox").thickbox({//加碼基準 第18項新增select
            //L140M01a.select=選取關鍵字
            title: i18n.lms1405s02["L140M01a.select"],
            width: 300,
            height: 250,
            align: "center",
            valign: "bottom",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    $.thickbox.close();
                    var newSelectName = $("#newSelectItem").val();
                    if (newSelectName != "") {
                        if ($("#openSelectList option[value=" + newSelectName + "]").text() == "") {//判斷是否已經有這個值
                            $("#openSelectList").append($("<option/>").attr("value", newSelectName).text(newSelectName));//新增select選項
                            $("#openSelectList").val(newSelectName);
                        }
                    }
                    $("#item1R").val($("#openSelectList :selected").text());
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
    
    
    
    
    $("#theWordBt").click(function(){
        theWordBt();
    });
    
    $("#cleartwBt").click(function(){
        cleartw();
    });
    //====================button event END ========================
    
    //====================Grid Cod ================================
    /**  利費率grid  */
    function gridviewRate(){
        $("#gridviewRate").iGrid({//利費率grid
            handler: inits.ghandle,
            height: 200,
            rownumbers: true,
            multiselect: true,
            rowNum: 10,
            hideMultiselect: false,
            sortname: 'createTime',
            sortorder: 'asc',
            postData: {
                formAction: "queryL140m01f",
                tabFormMainId: $("#tabFormMainId").val()
            },
            autowidth: true,
            colModel: [{
                colHeader: i18n.lms1405s02["L782M01A.loanTP"],//"科目",
                name: 'loanTPList',
                align: "left",
                width: 300,
                sortable: true,
                //在LMS1405s02panel02.js的格式化function
                formatter: "click",
                onclick: newRate
            }, {
                colHeader: i18n.lms1405s02["L140M01g.rate"],//"利率",
                name: 'rateDscr',
                width: 500,
                sortable: false,
                formatter: 'click',
                onclick: newRate
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }, {
                name: 'rateSeq',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridviewRate").getRowData(rowid);
                newRate(null, null, data);
            }
        });
    }//close gridviewRate fn();
    //====================Grid Code END ===========================
    var titleName = '';
    var openType = '';
    
    /** 字串轉換 */
    function toConverWord(spanName){
        var _str = "";
        var stop = false;
        $(spanName).find("input:text,select,span.word,textarea").filter(function(){
            return !($(this).hasClass("intputX"));//當class=intputX就不組入字串內
        }).each(function(){
            _str = $.trim(_str);
            if (!stop) {
                switch (this.nodeName.toLowerCase()) {
                    case 'span':
                        _str += $(this).text();
                        break;
                    case 'select':
                        _str += $(this).find(":selected").text();
                        break;
                    case 'input':  case 'textarea':
                        if ($(this).val() == "") {
                            stop = true;
                        }
                        if($(this).attr("id") =="cp1Fee"){
							_str += " "+$(this).val();
						} else {
							_str += $(this).val();	
						}
                        break;
                }
            }
        });
        if (_str.match(/\($/)) {
            return _str.replace(/\(/g, "");
        }
        return _str;
    }
    
    
    
    //組字串
    function toConverWordforRate(select){
        var _str = "";
        var input1 = $.trim($("#item1" + select).val());
        var input2 = $.trim($("#item2" + select).val());
        var input3 = $.trim($("#item3" + select).val());
        var input4 = $.trim($("#item4" + select).val());
        var input5 = $.trim($("#item5" + select).val());
        //針對新台幣 .....等幣別去 組
        switch (select) {
            case "A":
                _str = i18n.lms1405s0204['l1405s02p04.001'];
                if (input1 != "") {
                    _str += "(" + input1 + "％)";
                }
                if (input2 != "") {
                    //l1405s02p04.002  加年率
                    _str += i18n.lms1405s0204['l1405s02p04.002'] + " " + input2 + " " + "％";
                }
                
                if (input3 != "") {
                    //l1405s02p04.003 -- 目前為
                    _str += "，" + i18n.lms1405s0204['l1405s02p04.003'] + "：" + input3 + "％";
                }
                
                
                break;
            case "B":
                //l1405s02p04.005  按本行
                _str = i18n.lms1405s0204['l1405s02p04.005'];
                //l1405s02p04.006-年期定存
                _str += " " + input1 + " " + i18n.lms1405s0204['l1405s02p04.006']
                //l1405s02p04.063--利率
                _str += " " + $("#item2B :selected").text() + i18n.lms1405s0204['l1405s02p04.063'];
                if (input3 != "") {
                    //l1405s02p04.002  加年率
                    _str += i18n.lms1405s0204['l1405s02p04.002'] + " " + input3 + " " + "％";
                }
                break;
            case "C":
                //l1405s02p04.005  按本行
                _str += i18n.lms1405s0204['l1405s02p04.005'];
                //l1405s02p04.007-年期定儲 
                _str += " " + input1 + " " + i18n.lms1405s0204['l1405s02p04.007']
                //l1405s02p04.063--利率
                _str += " " + $("#item2C :selected").text() + i18n.lms1405s0204['l1405s02p04.063'];
                if (input3 != "") {
                    //l1405s02p04.002  加年率
                    _str += i18n.lms1405s0204['l1405s02p04.002'] + " " + input3 + " " + "％";
                }
                break;
            case "D":
                
                //l1405s02p04.008  按中華郵政股份有限公司
                _str = i18n.lms1405s0204['l1405s02p04.008'];
                //l1405s02p04.009 年期定儲機動利率
                _str += " " + input1 + " " + i18n.lms1405s0204['l1405s02p04.009'];
                if (input2 != "") {
                    //l1405s02p04.002加年率
                    _str += i18n.lms1405s0204['l1405s02p04.002'] + " " + input2 + " " + "％";
                }
                break;
            case "E":
                //l1405s02p04.010  按經建會中長期資金運用計畫之資金轉融通成本加年率
                _str = i18n.lms1405s0204['l1405s02p04.010'];
                if (input1 != "") {
                    //l1405s02p04.002  加年率
                    _str += " " + i18n.lms1405s0204['l1405s02p04.002'] + " " + input1 + " " + "％";
                }
                break;
            case "F":
                //l1405s02p04.011  按本行每日拆款利率
                _str = i18n.lms1405s0204['l1405s02p04.011'];
                if (input1 != "") {
                    //l1405s02p04.002加年率
                    _str += " " + i18n.lms1405s0204['l1405s02p04.002'] + " " + input1 + " " + "％";
                }
                break;
            case "G":
                //l1405s02p04.013-按
                _str = i18n.lms1405s0204['l1405s02p04.013'];
                _str += " " + $("#item1G :selected").text();
                //l1405s02p04.012-銀行之基準利率平均值
                _str += " " + i18n.lms1405s0204['l1405s02p04.012'];
                if (input2 != "" && input3 != "") {
                    _str += " " + $("#item2G :selected").text() + " " + i18n.lms1405s0204['l1405s02p04.015'] + " " + input3 + " " + "％";
                }
                
                break;
            case "H":
                //l1405s02p04.013-按
                _str = i18n.lms1405s0204['l1405s02p04.013'];
                _str += " " +  $("#item1H :selected").text();
                //l1405s02p04.014---基準利率
                _str += " " + i18n.lms1405s0204['l1405s02p04.014'];
                if (input2 != "" && input3 != "") {
                    //l1405s02p04.015  年率
                    _str += " " + $("#item2H :selected").text() + i18n.lms1405s0204['l1405s02p04.015'] + " " + input3 + " " + "％";
                }
                
                break;
            case "I":
                //l1405s02p04.013-按
                _str = i18n.lms1405s0204['l1405s02p04.013'];
                _str += " " + $("#item1I :selected").text();
                //l1405s02p04.016-之二年期定存機動利率平均值
                _str += " " + i18n.lms1405s0204['l1405s02p04.016'];
                _str += $("#item2I :selected").text();
                
                if (input2 == "") {
                    return _str;
                } else {
                    //l1405s02p04.002  加年率
                    _str += i18n.lms1405s0204['l1405s02p04.002'] + " " + input2 + " " + "％";
                }
                break;
            case "J":
                //l1405s02p04.017-按工商時報刊載金融行情表之同天期商業本票發行(平均)利率
                _str = i18n.lms1405s0204['l1405s02p04.017'];
                if (input1 != "") {
                    //l1405s02p04.002  加年率
                    _str += i18n.lms1405s0204['l1405s02p04.002'] + " " + input1 + " " + "％";
                }
                break;
            case "K":
                //l1405s02p04.018-按橋訊社+ 51328+l1405s02p04.019 頁短期票券Fixing Rate
                _str = i18n.lms1405s0204['l1405s02p04.018'] + 51328 + " " + i18n.lms1405s0204['l1405s02p04.019'];
                if (input1 != "") {
                    //l1405s02p04.002  加年率
                    _str += i18n.lms1405s0204['l1405s02p04.002'] + " " + input1 + " " + "％";
                }
                
                break;
            case "L":
                //l1405s02p04.018-按橋訊社+ 51328+l1405s02p04.019 頁短期票券Fixing Rate
                _str = i18n.lms1405s0204['l1405s02p04.018'] + 6165 + " " + i18n.lms1405s0204['l1405s02p04.019'];
                if (input1 != "") {
                    //l1405s02p04.002  加年率
                    _str += i18n.lms1405s0204['l1405s02p04.002'] + " " + input1 + " " + "％";
                }
                
                break;
            case "M":
                //l1405s02p04.020-按央行重貼現率與擔保放款融通利率之平均值加年率
                _str = i18n.lms1405s0204['l1405s02p04.020'];
                if (input1 != "") {
                    //l1405s02p04.002  加年率
                    _str += " " + i18n.lms1405s0204['l1405s02p04.002'] + " " + input1 + " " + "％";
                }
                break;
            case "N":
                //l1405s02p04.021-視資金情況適用本行競爭性利率
                _str = i18n.lms1405s0204['l1405s02p04.021'];
                break;
            case "O":
                //l1405s02p04.022-按本行資金成本加年率
                _str = i18n.lms1405s0204['l1405s02p04.022'];
                if (input1 != "") {
                    //l1405s02p04.002  加年率
                    _str += i18n.lms1405s0204['l1405s02p04.002'] + " " + input1 + " " + "％";
                }
                break;
            case "P":
                _str = input1;
                break;
            case "Q":
                //l1405s02p04.013-按+ l1405s02p04.024-6S基準利率月指標利率
                _str = i18n.lms1405s0204['l1405s02p04.013'] + " " + i18n.lms1405s0204['l1405s02p04.024'];
                if (input1 != "") {
                    _str += "(" + input1 + "％)";
                }
                
                if (input2 != "") {
                    _str += i18n.lms1405s0204['l1405s02p04.002'] + " " + input2 + " " + "％";
                }
                
                if (input3 != "") {
                    //l1405s02p04.003 -- 目前為
                    _str += "，" + i18n.lms1405s0204['l1405s02p04.003'] + " " + input3 + " " + "％";
                }
                break;
            case "R":
                //l1405s02p04.013-按
                _str = i18n.lms1405s0204['l1405s02p04.013'];
                if (input1 != "") {
                    //l1405s02p04.026 --期
                    _str += input1 + " " + i18n.lms1405s0204['l1405s02p04.026'] + " ";
                }
                _str += "TAIBOR";
                if (input2 != "") {
                    //l1405s02p04.015--年率i18n.lms1405s0204['l1405s02p04.015']
                    _str += " " + $("#item2R :selected").text() + " " + i18n.lms1405s0204['l1405s02p04.015'];
                }
                if (input3 != "") {
                    _str += " " + input3 + "％ ";
                }
                
                if (input4 != "") {
                    if (input4 == 1) {
                        _str += i18n.def['calculate'];
                    } else {
                        //l1405s02p04.027 --後除以 calculate 計算
                        _str += i18n.lms1405s0204['l1405s02p04.027'] + " " + input4 + " " + i18n.def['calculate'];
                    }
                    
                }
                
                
                break;
            case "S":
                //l1405s02p04.028--按6165初級市場ＣＰ九十天期(KX)平均利率
                _str = i18n.lms1405s0204['l1405s02p04.028'];
                if (input1 != "") {
                    _str += "(" + input1 + "％)";
                }
                
                if (input2 != "") {
                    //l1405s02p04.002 加年率
                    _str += i18n.lms1405s0204['l1405s02p04.002'] + " " + input2 + " " + "％";
                }
                
                if (input3 != "") {
                    //l1405s02p04.003--目前為
                    _str += "，" + i18n.lms1405s0204['l1405s02p04.003'] + " " + input3 + " " + "％";
                }
                
                break;
            case "BA":
                //l1405s02p04.042--按本行短期貸款利率
                _str = i18n.lms1405s0204['l1405s02p04.042'];
                if (input1 != "") {
                    //l1405s02p04.015--年率
                    _str += " " + $("#item1BA :selected").text() + " " + i18n.lms1405s0204['l1405s02p04.015'];
                }
                
                if (input2 != "") {
                    _str += " " + input2 + " " + "％";
                }
                
                
                break;
            case "BB":
                //l1405s02p04.013-按
                _str = i18n.lms1405s0204['l1405s02p04.013'];
                if (input1 != "") {
                    //l1405s02p04.030--個月期
                    _str += " " + input1 + " " + i18n.lms1405s0204['l1405s02p04.030'];
                }
                _str += " " + $("#item2BB :selected").text();
                if (input3 != "") {
                    //l1405s02p04.015--年率
                    _str += " " + $("#item3BB :selected").text() + " " + i18n.lms1405s0204['l1405s02p04.015'];
                }
                
                if (input4 != "") {
                    _str += " " + input4 + " " + "％";
                }
                if (input5 != "") {
                    if (input5 == 1) {
                        _str += " " + i18n.def['calculate'];
                    } else {
                        //l1405s02p04.027--後除以 ,button.calc =計算
                        _str += " " + i18n.lms1405s0204['l1405s02p04.027'] + " " + input5 + " " + i18n.def['calculate'];
                    }
                    
                }
                break;
            case "BC":
                _str = input1;
                break;
            case "CA":
                //l1405s02p04.031--按本行借入資金成本
                _str = i18n.lms1405s0204['l1405s02p04.031'];
                if (input1 != "") {
                    //l1405s02p04.002--加年率
                    _str += " " + i18n.lms1405s0204['l1405s02p04.002'] + " " + input1 + " " + "％";
                }
                
                if (input2 != "") {
                    if (input2 == 1) {
                        _str += " " + i18n.def['calculate'];
                    } else {
                        //l1405s02p04.027--後除以 ,button.calc =計算
                        _str += " " + i18n.lms1405s0204['l1405s02p04.027'] + " " + input2 + " " + i18n.def['calculate'];
                    }
                }
                
                break;
            case "CB":
                //l1405s02p04.013-按
                _str = i18n.lms1405s0204['l1405s02p04.013'];
                if (input1 != "") {
                    _str += " " + input1 + " " + i18n.lms1405s0204['l1405s02p04.030'];
                }
                _str += " " + "LIBOR";
                if (input2 != "") {
                    //l1405s02p04.002--加年率
                    _str += " " + i18n.lms1405s0204['l1405s02p04.002'] + " " + input2 + " " + "％";
                }
                
                if (input3 != "") {
                    if (input3 == 1) {
                        //button.calc =計算
                        _str += " " + i18n.def['calculate'];
                    } else {
                        //l1405s02p04.027--後除以,button.calc =計算
                        _str += " " + i18n.lms1405s0204['l1405s02p04.027'] + " " + input3 + " " + i18n.def['calculate'];
                    }
                    
                }
                break;
            case "CC":
                //l1405s02p04.032-按路透社
                _str = i18n.lms1405s0204['l1405s02p04.032'];
                if (input1 != "") {
                    //個月期
                    _str += " " + input1 + " " + i18n.lms1405s0204['l1405s02p04.030'];
                }
                
                _str += " " + "JNBOR";
                if (input2 != "") {
                    //l1405s02p04.002--加年率
                    _str += " " + i18n.lms1405s0204['l1405s02p04.002'] + " " + input2 + " " + "％";
                }
                
                
                if (input3 != "") {
                    if (input3 == 1) {
                        _str += " " + i18n.def['calculate'];
                    } else {
                        //l1405s02p04.027--後除以,button.calc =計算
                        _str += " " + i18n.lms1405s0204['l1405s02p04.027'] + " " + input3 + " " + i18n.def['calculate'];
                        
                    }
                    
                }
                
                break;
            case "CD":
                //l1405s02p04.013-按
                _str = i18n.lms1405s0204['l1405s02p04.013'];
                if (input1 != "") {
                    //l1405s02p04.030-個月期
                    _str += " " + input1 + i18n.lms1405s0204['l1405s02p04.030'];
                }
                _str += " " + "TIBR";
                
                if (input2 != "") {
                    //l1405s02p04.002--加年率
                    _str += " " + i18n.lms1405s0204['l1405s02p04.002'] + " " + input2 + " " + "％";
                }
                
                if (input3 != "") {
                    if (input3 == 1) {
                        _str += " " + i18n.def['calculate'];
                    } else {
                        //l1405s02p04.027--後除以,button.calc =計算
                        _str += " " + i18n.lms1405s0204['l1405s02p04.027'] + " " + input3 + " " + i18n.def['calculate'];
                    }
                    
                }
                
                break;
            case "CE":
                _str = input1;
                break;
            case "DE":
                _str = input1;
                break;
        }
        return _str;
    }
    
    
    
    
    
    //當從欄位離開判斷select 裡面是否有這個值 加碼基礎 第18項
    $("#item1R").blur(function(){
        $this = $(this);
        //當這個欄位不等於空再判斷
        if ($this.val() != "") {
            //如果select內有這個欄位就把他選起來
            $("#openSelectList option[value=" + $this.val() + "]").prop("selected", true);
            //判斷是否有這個option
            if ($("#openSelectList option[value=" + $this.val() + "]").text() == "") {
                $("#openSelectList").append($("<option/>").attr("value", $this.val()).text($this.val()));//新增select選項
                $("#openSelectList,#newSelectItem").val($this.val());
            }
        }
    });
    
    //當從欄位離開判斷select 裡面是否有這個值 加碼基礎 
    $("#item1BB,#item1CB,#item1CC,#item1CD").blur(function(){
        $this = $(this);
        if ($this.val() != "") {//當這個欄位不等於空再判斷
            $("#openSelectList2 option[value=" + $this.val() + "]").prop("selected", true);//如果select內有這個欄位就把他選起來
            if ($("#openSelectList2 option[value=" + $this.val() + "]").text() == "") {//判斷是否有這個option
                $("#openSelectList2").append($("<option/>").attr("value", $this.val()).text($this.val()));//新增select選項
                $("#openSelectList2,#newSelectItem2").val($this.val());
            }
        }
    });
    
    $(".selectX").change(function(){//針對登錄費率內有X的select做隱藏或顯示
        var $thisId = $(this).attr("id");
        if ($(this).val() == "5") {
            $("#" + $thisId + "Select").show();
        } else {
            $("#" + $thisId + "Select").hide();
        }
    });
    
    
    
    $("#rateBase,#rateKind,#rateChgKind").change(function(){
        var select = $(this).attr("id"), selectVal = $(this).val();
        switch (select) {
            case "rateBase"://加碼基礎select
                $("#rateTable #option" + selectVal).show().siblings("span").hide().find("input").val("");
                //清空加總利率
                $("#rateValue").val("");
                break;
            case "rateKind"://收息方式select
                if (selectVal == "3") {//定期浮動
                    $("#hideRateChgKind").show();
                } else {
                    $("#hideRateChgKind").hide();
                }
                break;
            case "rateChgKind"://利率變動方式select
                switch (selectVal) {
                    case "1"://中長期
                        $("#longtw").show().siblings("div").hide();
                        break;
                    case "2"://短期
                        $("#shorttw").show().siblings("div").hide();
                        break;
                    default:
                        $("#shorttw,#longtw").hide()
                        break;
                }
                break;
                
        }
    });
    
    
    $("#item1A,#item1Q,#item1S,#item2A,#item2Q,#item2S").blur(function(){//當這幾個欄位離開焦點算出數值
        var select = $("#rateBase").val();
        var Value1 = Math.round($("#item1" + select).val() * 1000) / 1000;
        var Value2 = Math.round($("#item2" + select).val() * 1000) / 1000;
        var count = Math.round((Value1 + Value2) * 1000) / 1000;
        if (count != 0) {
            $("#option" + select).find(".nowRate").val(count);
        }
        
        $("#rateValue").val(count);//當加碼基礎選項為1,17,19 就將算出的目前利率 放入對應的欄位
    });
    
    
    
    function newRate(bb, oid, data){
        //下拉選單
        dfd42.resolve();
        $("#L140M01FForm").reset();
        $("#L140M01GForm").reset();
        $("#RateDrc").html("");
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "queryL140m01f",
                tabFormMainId: $("#tabFormMainId").val(),
                oid: data.oid || "",
                showMsg: true
            },
            success: function(responseData){
                if (data.oid) {
                    $("#L140M01FForm").injectData(responseData);
                    $("#RateDrc").val(responseData.RateDrc);
                    var list = responseData.loanTPListAll.split("|");
                    for (var i = 0; i < list.length; i++) {//轉換科目代碼將值帶入
                        var loanTP = list[i];
                        $("[name=loanTPList][value=" + loanTP + "]").prop("checked", true);
                    }//close for
                } else {
                    //清空tabFormid 和 oid
                    $("#L140M01GForm ,#L140M01FForm").find(":hidden:text").val('');
                }
                openNewRateBox();
                
            }//close success function
        });//close ajax 
        //根據第二頁籤的現請額度幣別判斷出現非美元、新台幣、日幣、歐元只出現雜幣
        //判斷
        if ($("#otherCurr").val() == "") {
            $("#twBT,#thBT,#euBT,#usBT,#jpBT,#otherBT").parent().parent().hide();
            switch ($("#currentApplyCurr").val()) {
                case 'TWD':
                    $("#twBT").parent().parent().show();
                    break;
                case 'USD':
                    $("#usBT").parent().parent().show();
                    break;
                case 'JPY':
                    $("#jpBT").parent().parent().show();
                    break;
                case 'EUR':
                    $("#euBT").parent().parent().show();
                    break;
				case 'THB':
                    $("#thBT").parent().parent().show();
                    break;	
                default:
                    $("#otherBT").parent().parent().show();
                    break;
            }
        } else if ($("#otherCurr").val() == "4") {
            $("#twBT,#thBT,#euBT,#usBT,#jpBT,#otherBT").parent().parent().hide();
            $("#twBT").parent().parent().show();
        } else {
            $("#twBT,#rateBT,#otherBT,#euBT,#usBT,#jpBT,#thBT").parent().parent().show();
        }
        
        $("#rateBT").parent().parent().show();
        if (inits.toreadOnly) {
            $("#twBT,#euBT,#thBT,#usBT,#jpBT,#otherBT,#rateBT").parent().parent().hide();
        }
        
    }
    
    function openNewRateBox(){
        $("input[name=loanTPList]").removeAttr("disabled");
        $("#newRateBox").thickbox({
            //title.16=登錄利(費)率
            title: i18n.lms1405s02["title.16"],
            width: 880,
            height: 420,
            modal: true,
            readOnly: _openerLockDoc == "1" || inits.toreadOnly,
            i18n: i18n.def,
            open: function(){
                $("[name=loanTPList]").readOnly(_openerLockDoc == "1" || inits.toreadOnly);
            },
            buttons: {
                "saveData": function(){
                    //檢查是否已經選擇科目
                    if ($("#itemSpan_loanTPList").find("[name=loanTPList]:checked").length == 0) {
                        //L140M01a.error07=請選擇 L782M01A.loanTP=科目
                        return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error07"] + i18n.lms1405s02["L782M01A.loanTP"]);
                    }
                    var htmlSpan = $("#RateDrc").val();
                    //當敘述是空的不儲存
                    if (htmlSpan == "") {
                        $.thickbox.close();
                    } else {
                    
                        FormAction.open = true;
                        $.ajax({
                            handler: inits.fhandle,
                            data: {//把資料轉成json
                                formAction: "saveL140m01f",
                                tabFormMainId: $("#tabFormMainId").val(),
                                rateSeq: $("#L140M01FForm #rateSeq").val(),
                                showMsg: true
                            },
                            success: function(obj){
                                FormAction.open = false;
                                if (obj && obj.drc) {
                                    $("#itemDscr2").val(obj.drc);
                                }
                                $("#gridviewRate").trigger('reloadGrid');
                                $.thickbox.close();
                            }
                        }); //close ajax
                    }
                },
                "close": function(){
                    $.thickbox.close();
                    $("#gridviewRate").trigger('reloadGrid');
                }
            }
        });
    }
    
    /**  打開幣別利率的box  */
    function openRateBox(type){
        //檢查是否已經選擇科目
        if ($("#itemSpan_loanTPList").find("[name=loanTPList]:checked").length == 0) {
            //L140M01a.error07=請選擇 L782M01A.loanTP=科目
            return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error07"] + i18n.lms1405s02["L782M01A.loanTP"]);
        }
        var objs = CommonAPI.loadCombos(["lms1405m01_RateGetInt", "lms1405m01_TWDRateBase", "lms1405m01_USDRateBase", "lms1405m01_JPYRateBase", "lms1405m01_OtherRateBase"]);
        
        //清空欄位值
        
        var twOption = objs.lms1405m01_TWDRateBase;
        var usOption = objs.lms1405m01_USDRateBase;
        var jpOption = objs.lms1405m01_JPYRateBase;
        var otherOption = objs.lms1405m01_OtherRateBase;
        $("#rateValue").val("");
        $("#hideRateTax").hide();//只有新台幣需要的欄位
        $("#rateType").val(type);
        $("#rateGetInt").setItems({
            space: false,
            item: objs.lms1405m01_RateGetInt,
            format: "{value}.{key}"
        });
        
		
        var moneyItemSelect = '';
        openType = type;
        switch (type) {
            case "1":
                
                titleName = i18n.lms1405s02["L140M01f.TWD"];
                moneyItemSelect = twOption;
                
                $("#rateBaseNum").html("19");
                $("#hideRateTax").show();
                break;
            case "2":
                titleName = i18n.lms1405s02["L140M01f.USD"];
                moneyItemSelect = usOption;
                $("#rateGetInt option[value=6]").remove();
                $("#rateBaseNum").html("3");
                $("#hideRateTax").show();
                break;
            case "3":
                titleName = i18n.lms1405s02["L140M01f.JPY"];
                moneyItemSelect = jpOption;
                $("#rateGetInt option[value=6]").remove();
                $("#rateBaseNum").html("5");
                $("#hideRateTax").show();
                break;
            case "4":
                titleName = i18n.lms1405s02["L140M01f.EUR"];
                moneyItemSelect = usOption;
                $("#rateGetInt option[value=6]").remove();
                $("#rateBaseNum").html("3");
                $("#hideRateTax").show();
                break;
			case "7":
                titleName = i18n.lms1405s02["L140M01f.THB"];
                moneyItemSelect = usOption;
                $("#rateGetInt option[value=6]").remove();
                $("#rateBaseNum").html("3");
                $("#hideRateTax").show();
                break;	
            case "5":
                titleName = i18n.lms1405s02["L140M01f.Other"];
                moneyItemSelect = otherOption;
                $("#rateGetInt option[value=6]").remove();
                $("#rateBaseNum").html("2");
                $("#hideRateTax").show();
                break;
                
        }
		
		//下列利率加碼基礎只有泰國才有
		if(type == "7"){	
			$("#item2BB").find("option[value=MOR]").prop("disabled", false);
			$("#item2BB").find("option[value=MLR]").prop("disabled", false);
			$("#item2BB").find("option[value=MRR]").prop("disabled", false);
			$("#item2BB").find("option[value=MHR]").prop("disabled", false);
			$("#item2BB").find("option[value=BIBOR]").prop("disabled", false);
			$("#item2BB").find("option[value=THBFIX]").prop("disabled", false);
		}else{
			$("#item2BB").find("option[value=MOR]").prop("disabled", true);
			$("#item2BB").find("option[value=MLR]").prop("disabled", true);
			$("#item2BB").find("option[value=MRR]").prop("disabled", true);
			$("#item2BB").find("option[value=MHR]").prop("disabled", true);
			$("#item2BB").find("option[value=BIBOR]").prop("disabled", true);
			$("#item2BB").find("option[value=THBFIX]").prop("disabled", true);
		}
		
		
        cleartw();//;進來先清除欄位
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "queryL140m01g",
                whereBox: type,//告訴方法是哪一個box
                tabFormMainId: $("#tabFormMainId").val(),
                rateSeq: $("#L140M01FForm #rateSeq").val(),
                showMsg: true
            },
            success: function(responseData){
                $("#L140M01GForm").injectData(responseData);
                $("#rateBase").setItems({
                    value: $.trim(responseData.rateBase),
                    item: moneyItemSelect,
                    space: false
                });
                var baseSelectVal = "";
                if (responseData.rateBase) {
                    baseSelectVal = DOMPurify.sanitize(responseData.rateBase);
                } else {
                    switch (type) {
                        case "1":
                            baseSelectVal = "A";
                            break;
                        case "2":
                            baseSelectVal = "BA";
                            break;
                        case "3":
                            baseSelectVal = "CA";
                            break;
                        case "4":
                            baseSelectVal = "BA";
                            break;
						case "7":
                            baseSelectVal = "BA";
                            break;	
                        case "5":
                            baseSelectVal = "CA";
                            break;
                    }
                    
                }
                
                $("#rateBase" + baseSelectVal).show();//加碼基礎選項
                $("#option" + baseSelectVal).show();//加碼基礎選項
                if (responseData.rateKind == "3") {//定期浮動
                    $("#hideRateChgKind").show();
                    if (responseData.rateChgKind == "1") {
                        $("#longtw").show();
                        var list = responseData.rateChg2.split("|");
                        for (var i = 0; i < list.length; i++) {//將中長期 處理月份輸出
                            var loanTP = list[i];
                            $("[name=rateChg2][value=" + loanTP + "]").prop("checked", true);
                        }
                    } else if (responseData.rateChgKind == "2") {
                        $("#shorttw").show();
                    } else {
                        $("#shorttw,#longtw").hide();
                    }
                } else {
                    $("#hideRateChgKind,#shorttw,#longtw").hide();
                }
                
                var select = $("#rateBase").val();
                //當選項為自行輸入時不把值塞到共同欄位
                if (select != "BC" && select != "CE" && select != "DE" && select != "P") {
                    for (var i = 1; i < 6; i++) {
                        $("#item" + i + select).val($("#L140M01GForm #rateInput" + i).val());
                    }
                } else {
                    //當選項為其他時從加碼基礎欄位取得內容
                    $("#item1" + select).val($("#rateInput").val());
                }
            }
        });
        $("#rateBox").thickbox({
            //other.login=登錄、L140M01g.rate=利率
            title: i18n.lms1405s02["other.login"] + titleName + i18n.lms1405s02["L140M01g.rate"],
            width: 960,
            height: 500,
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            modal: true,
            buttons: {
                "saveData": function(){
                    if (!$("#L140M01GForm").valid()) {
                        return false;
                    }
                    var select = $("#rateBase").val();//加碼基礎
                    var rateKind = $("#rateKind").val();//利率方式
                    var rateGetInt = $("#rateGetInt").val();//收息方式
                    var rateChgKind = $("#rateChgKind").val();//利率變動方式
                    var rateTax = $("#rateTax").val();//稅負洽收
                    var rateChg1 = $("#rateChg1").val();//中長期第一選項
                    theWordBt(type);
                    var $select = $("#rateBase").val();
                    if ($select != "BC" && $select != "CE" && $select != "DE" && $select != "P") {//當選項為自行輸入時不把值塞到共同欄位
                        $("#option" + $select).find("[id^=item]").each(function(k){
                            $("[name^=rateInput]").eq(k + 1).val($(this).val());//將選到的select內的欄位值塞到隱藏的欄位裡
                        });
                    }
                    FormAction.open = true;
                    $.ajax({//按下確定後將值傳到資料庫
                        handler: inits.fhandle,
                        data: {//把資料轉成json
                            formAction: "saveL140m01g",
                            rateType: $("#rateType").val(),
                            rateId: $("#rateId").val(),
                            rateSeq: $("#rateSeq").val(),
                            tabFormMainId: $("#tabFormMainId").val(),
                            showMsg: true
                        },
                        success: function(obj){
                            FormAction.open = false;
                            $("#RateDrc").val(obj.RateDrc);
                            $("#rateId").val(obj.oid);
                            $("#rateSeq").val(obj.rateSeq);
                            $("#gridviewRate").trigger('reloadGrid');
                            if (obj && obj.drc) {
                                $("#itemDscr2").val(obj.drc);
                            }
                            $.thickbox.close();
                            
                        }
                    });
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    function openRateBox2(){
        //檢查是否已經選擇科目
        if ($("#itemSpan_loanTPList").find("[name=loanTPList]:checked").length == 0) {
            //L140M01a.error07=請選擇 L782M01A.loanTP=科目
            return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error07"] + i18n.lms1405s02["L782M01A.loanTP"]);
        }
        $("#L140M01HForm").reset();
        $(".rateSpan1,.rateSpan2,.rateSpan3,.rateSpan4").hide();
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "queryL140m01h",
                rateSeq: $("#rateSeq").val(),
                tabFormMainId: $("#tabFormMainId").val(),
                showMsg: true
            },
            success: function(obj){
                $("#L140M01HForm").injectData(obj);
                $("#radioSpan1").find(".rateSpan" + obj.cpType).show();
                $("#radioSpan2").find(".rateSpan" + obj.cfType).show();
                $("#radioSpan3").find(".rateSpan" + obj.cpyType).show();
                $("#radioSpan4").find(".rateSpan" + obj.paType).show();
            }
        });
        
        $("#rateBox2").thickbox({
            // title.17=登錄費率
            title: i18n.lms1405s02["title.17"],
            width: 920,
            height: 500,
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "saveData": function(){
                    if (!$("#L140M01HForm").valid()) {
                        return false;
                    }
                    rateToWord();//在按確定的時候幫他組字串
                    FormAction.open = true;
                    $.ajax({
                        handler: inits.fhandle,
                        data: {//把資料轉成json
                            formAction: "saveL140m01h",
                            rateId: $("#rateId").val(),
                            rateSeq: $("#rateSeq").val(),
                            tabFormMainId: $("#tabFormMainId").val(),
                            //							   L140M01HForm:JSON.stringify($("#L140M01HForm").serializeData()),
                            //							   L140M01FForm:JSON.stringify($("#L140M01FForm").serializeData()),
                            showMsg: true
                        },
                        success: function(obj){
                            FormAction.open = false;
                            $("#rateId").val(obj.oid);
                            $("#rateSeq").val(obj.rateSeq);
                            $("#RateDrc").val(obj.RateDrc);
                            $("#gridviewRate").trigger('reloadGrid');
                            if (obj && obj.drc) {
                                $("#itemDscr2").val(obj.drc);
                            }
                            $.thickbox.close();
                        }
                    });
                    
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    function openRateChildrenBox(type){//顯示出現哪一種thickbox
        var rateType = '';
        switch (type) {
            case "cpType":
                //L140M01h.cpType=商業本票
                rateTitleName = i18n.lms1405s02["L140M01h.cpType"];
                rateType = "1";
                break;
            case "cfType":
                //L140M01h.cfType=開發保證函
                rateTitleName = i18n.lms1405s02["L140M01h.cfType"];
                rateType = "2";
                break;
            case "cpyType":
                //L140M01h.cpyType=公司債保證
                rateTitleName = i18n.lms1405s02["L140M01h.cpyType"];
                rateType = "3";
                break;
            case "paType":
                //L140M01h.paType=承兌費率
                rateTitleName = i18n.lms1405s02["L140M01h.paType"];
                rateType = "4";
                break;
        }
        $("#rateChildrenBox" + rateType).show().siblings("table").hide();
        $("#rateChildrenBox").thickbox({
            title: rateTitleName,
            width: 720,
            height: 250,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    //cpType-商業本票保證,cfType-開發保證函,cpyType-公司債保證,paType-承兌費率
                    var radioVal = $("[name =" + type + "]:checked").val();
                    if (radioVal != 'c') {
                        $("#radioSpan" + rateType + " .rateSpan" + radioVal).show().siblings().hide().find(":input,select").val("");
                    } else {
                        $("#radioSpan" + rateType).find("[class^=rateSpan]").hide().find(":input,select").val("");
                    }
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    
    
    function theWordBt(type){//組幣別字串
        var select = $("#rateBase").val();//加碼基礎
        var rateKind = $("#rateKind").val();//利率方式
        var rateGetInt = $("#rateGetInt").val();//收息方式
        var rateChgKind = $("#rateChgKind").val();//利率變動方式
        var rateTax = $("#rateTax").val();//稅負洽收
        var rateChg1 = $("#rateChg1").val();//中長期第一選項
        var rateChg2 = $("input[name=rateChg2]:checked");
        var sign = "，";
        var sign2 = "。";
        var sign3 = ":";
        var str = titleName + sign3;
        var baseValue = toConverWordforRate(select);
        str = str + baseValue;
        $("#rateInput").val(baseValue);//放入加碼基礎組成字串
        if (rateKind == "1") {//1.固定利率
            str = str + sign + $("#rateKind :selected").text().slice(2);
            if (rateGetInt != "1" && rateGetInt != 0) {//1.按月收息不組入字串
                str = str + sign + $("#rateGetInt :selected").text().slice(2);
            }
        } else if (rateKind == "2") {//2.機動利率不組入字串
            if (rateGetInt != "1" && rateGetInt != 0) {//按月收息不組入字串
                str = str + sign + $("#rateGetInt :selected").text().slice(2);
            }
        } else if (rateKind == "3") {//3.定期浮動
            str = str + sign + $("#rateKind :selected").text().slice(2);
            if (rateGetInt != "1" && rateGetInt != 0) {//按月收息不組入字串
                str = str + sign + $("#rateGetInt :selected").text().slice(2) + sign2;
            } else {
                str = str + sign2;
            }
            if (rateChgKind == "1") {
            
                str += $("#rateChgKind :selected").text().slice(2) + sign3;
                if ($("#rateChg1").val() != "0") {
                    //other.every=每
                    //other.edit=調整乙次
                    str += i18n.lms1405s02["other.every"] + $("#rateChg1 :selected").text();
                    str += i18n.lms1405s02["other.edit"];
                }
                
                if (rateChg2.length > 0) {//當有勾選幾個月在執行
                    if ($("#rateChg1").val() != "0") {
                        str += sign;
                    }
                    
                    rateChg2.each(function(k){
                        if (k > 0) {
                            //other.or=或
                            str = str + i18n.lms1405s02["other.or"] + $(this).parent("label").text();
                        } else {
                            str += i18n.lms1405s02["other.every"] + $(this).parent("label").text();
                        }
                    });
                    str += i18n.lms1405s02["other.edit"];
                }
            } else if (rateChgKind == "2") {
                str = str + $("#rateChgKind :selected").text().slice(2) + sign3 + $("#rateChg3 :selected").text();
            }
        }
        if ($("#rateTax").val() != "" && $("#rateTax").val() != "0") {
            //other.rateto=稅負由、other.take=負擔
            str = str + sign + i18n.lms1405s02["other.rateto"] + " " + $("#rateTax :selected").text() + " " +i18n.lms1405s02["other.take"] + sign2;
        } else {
            str = str;
        }
        
        $("#rateDscr").val(formatString(str).replace(/。，/g, "。"));
        
    }
    
    /**
     * format 中英文夾雜文字 i love  your -> i love your
     * 我 愛 你 -> 我愛你
     */
    function formatString(text){
    	text = $.trim(text);
    	var textArray = text.split("");
    	var isFull = false;
    	
    	var value = "";
    	for(var i = 0; i < textArray.length; i++ ){
    		//var halfWord = /^[\x00-\xff]+$/;
    		var temp = textArray[i];
    		
    		if (i == 0){
    			value = value + temp;
    			if(/^[\x00-\xff]+$/.test(temp) && temp != " "){
        			// 半型字
        			isFull = false;
        		} else {
        			// 全型字
        			isFull = true;
        		}
    			continue;
    		} else {
    			var isLastFull =  isFull;
    			if(isLastFull && temp == " "){
    				
    			} else {
    				value = value + temp;
    			}    			
    		}    		
    		if(/^[\x00-\xff]+$/.test(temp) && temp != " "){
    			// 半型字
    			isFull = false;
    		} else {
    			// 全型字
    			isFull = true;
    		}
    		
    	}
    	return value;
    }
    
    
    
    function cleartw(){//清除幣別欄位內容
        $("#L140M01GForm").find("[id^=rateInput]").val("");
        $("#rateTable").find("input[id^=item]").val("");
        $("#rateChg1Oth").hide().val("");
        $("#rateChgKind,#rateKind,#rateBase,#rateGetInt,#rateTax,#rateChg1").find("option[value=0]").remove();//清空所有空白選項
        //加入空白選項
        $("#rateChgKind,#rateKind,#rateBase,#rateGetInt,#rateTax,#rateChg1").prepend($("<option/>").attr("value", "0").text("")).val("0");
        $("[name=rateChg2]:checked").removeAttr("checked");
        $("#hideRateChgKind,#shorttw,#longtw,span[id^=option]").hide();//隱藏加碼基礎和利率變動方式
        $("#rateDscr").val("");
		$("#item2BB").val("");
    }
    
    
    
    function rateToWord(){//組費率字串  
        var AItemVal = $("[name=cpType]:checked").val();//商業本票保證的值
        var BItemVal = $("[name=cfType]:checked").val();//開發保證函的值
        var CItemVal = $("[name=cpyType]:checked").val();//公司債保證的值
        var DItemVal = $("[name=paType]:checked").val();//承兌費率 的值
        var other = $("#othDes").val();//其他
        var str = '';
        var sign = ",";
        var sign2 = "。";
        var sign3 = "-";
        if (AItemVal && AItemVal != "c") {//當不是空值並且非clear 才組字串
            var Aword = toConverWord("#radioSpan1" + " .rateSpan" + AItemVal);
            
            if (Aword != "") {
                //L140M01h.cpType=商業本票保證
                str = i18n.lms1405s02["L140M01h.cpType"] + sign3 + Aword + sign2;
            }
            
            $("#cpDes").val(Aword);
        }
        if (BItemVal && BItemVal != "c") {//當不是空值並且非clear 才組字串
            var Bword = toConverWord("#radioSpan2" + " .rateSpan" + BItemVal);
            if (Bword != "") {
                //L140M01h.cfType=開發保證函
                str = str + i18n.lms1405s02["L140M01h.cfType"] + sign3 + Bword + sign2;
            }
            
            switch (BItemVal) {
                case '1':
                    if ($("#cf1MD").val() == '5') {
                        str = str.replace("X", $("#cf1Mon2").val());
                        $("#cfDes").val(Bword.replace("X", $("#cf1Mon2").val()));
                    }
                    break;
                case '2':
                    if ($("#cf2MD").val() == '5') {
                        str = str.replace("X", $("#cf2Mon").val());
                        $("#cfDes").val(Bword.replace("X", $("#cf2Mon").val()));
                    }
                    break;
            }
            
        }
        if (CItemVal && CItemVal != "c") {//當不是空值並且非clear 才組字串
            var Cword = toConverWord("#radioSpan3" + " .rateSpan" + CItemVal);
            
            if (Cword != "") {
                //L140M01h.cpyType=公司債保證
                str = str + i18n.lms1405s02["L140M01h.cpyType"] + sign3 + Cword + sign2;
            }
            switch (CItemVal) {
                case '1':
                    if ($("#cpy1MD").val() == '5') {
                        str = str.replace("X", $("#cpy1Mon2").val());
                        $("#cpyDes").val(Cword.replace("X", $("#cpy1Mon2").val()));
                    }
                    break;
                case '2':
                    if ($("#cpy2MD").val() == '5') {
                        str = str.replace("X", $("#cpy2Mon").val());
                        $("#cpyDes").val(Cword.replace("X", $("#cpy2Mon").val()));
                    }
                    break;
            }
        }
        if (DItemVal && DItemVal != "c") {//當不是空值並且非clear 才組字串
            var Dword = toConverWord("#radioSpan4" + " .rateSpan" + DItemVal);
            if (Dword != "") {
                //L140M01h.paType=承兌費率
                str = str + i18n.lms1405s02["L140M01h.paType"] + sign3 + Dword + sign2;
            }
            switch (DItemVal) {
                case '1':
                    if ($("#pa1MD").val() == '5') {
                        str = str.replace("X", $("#pa1Mon").val());
                        $("#paDes").val(Dword.replace("X", $("#pa1Mon").val()));
                    }
                    break;
                case '2':
                    if ($("#pa2MD").val() == '5') {
                        str = str.replace("X", $("#pa2Mon").val());
                        $("#paDes").val(Dword.replace("X", $("#pa2Mon").val()));
                    }
                    break;
            }
            
        }
        if (other) {
            str = str + other + sign2;
        }
        
        $("#rateDscr2").val(str.replace(/。/g, "。\r"));
    }
    
    //帶入下拉選單值
    function initSelect(){
        var item = CommonAPI.loadCombos(["lms1405s0204_rateKind", "lms1405s0204_monType", "lms1405s0204_monType2", "lms1405s0204_rateType", "lms1405s0204_dateKind", "lms1405s0204_rateChg1", "lms1405s0204_rateChgKind", "lms1405s0204_count", "lms1405s0204_rateTax", "lms1405s0204_rateChg2", "lms1405s0204_rateChg3"]);
        //利率方式
        $(".lms1405s0204_rateKind").setItems({
            space: false,
            item: item.lms1405s0204_rateKind,
            format: "{value}.{key}"
        });
        //預收方式 for 承兌
        $(".lms1405s0204_monType").setItems({
            space: true,
            item: item.lms1405s0204_monType,
            format: "{key}"
        });
        
        //預收方式2 for開發
        $(".lms1405s0204_monType2").setItems({
            space: true,
            item: item.lms1405s0204_monType2,
            format: "{key}"
        });
        //定儲方式
        $(".lms1405s0204_rateType").setItems({
            space: false,
            item: item.lms1405s0204_rateType,
            format: "{key}"
        });
        //利率週期
        $(".lms1405s0204_dateKind").setItems({
            space: false,
            item: item.lms1405s0204_dateKind,
            format: "{key}"
        });
        //中長期利率變動方式
        $(".lms1405s0204_rateChg1").setItems({
            space: false,
            item: item.lms1405s0204_rateChg1,
            format: "{key}"
        });
        
        //中長期利率變動方式月份
        $(".lms1405s0204_rateChg2").setItems({
            item: item.lms1405s0204_rateChg2,
            format: "{key}",
            border: "none",
            size: "6"
        });
        //利率變動方式
        $(".lms1405s0204_rateChgKind").setItems({
            space: false,
            item: item.lms1405s0204_rateChgKind,
            format: "{value}.{key}"
        });
        //利費率-加減碼
        $(".lms1405s0204_count").setItems({
            space: true,
            item: item.lms1405s0204_count,
            format: "{key}"
        });
        //利費率-稅負洽收
        $(".lms1405s0204_rateTax").setItems({
            space: false,
            item: item.lms1405s0204_rateTax,
            format: "{key}"
        });
        //利費率-短期方式
        $(".lms1405s0204_rateChg3").setItems({
            space: false,
            item: item.lms1405s0204_rateChg3,
            format: "{key}"
        });
    }
    
});
