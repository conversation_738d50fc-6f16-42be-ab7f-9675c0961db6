package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.fms.panels.CLS2901FilterPanel;

@Controller
@RequestMapping(path = "/fms/cls2901v02")
public class CLS2901V02Page extends AbstractEloanInnerView {

	public CLS2901V02Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(FlowDocStatusEnum.待覆核);
		setJavaScriptVar("noOpenDoc", "N");
		//---
		addToButtonPanel(model, LmsButtonEnum.Filter);
		
		renderJsI18N(CLS2901V01Page.class);
		
		setupIPanel(new CLS2901FilterPanel(PANEL_ID), model, params);
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/CLS2901V01Page.js" };
	}
}
