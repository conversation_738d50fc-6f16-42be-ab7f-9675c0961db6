/* 
 * L161S01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 額度動用資訊敘述說明檔 **/
@NamedEntityGraph(name = "L161S01C-entity-graph", attributeNodes = { @NamedAttributeNode("l161s01a") })
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L161S01C", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","pid","itemType"}))
public class L161S01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	
	/**
	 * JOIN條件 L161S01A．動審表額度序號資料
	 * 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "pid", referencedColumnName = "uid", nullable = false, insertable = false, updatable = false) })
	private L161S01A l161s01a;

	public L161S01A getL161s01a() {
		return l161s01a;
	}

	public void setL161S01a(L161S01A l161s01a) {
		this.l161s01a = l161s01a;
	}
	
	/** 
	 * 文件編號PID<p/>
	 * 102/01/17新增
	 */
	@Size(max=32)
	@Column(name="PID", length=32, columnDefinition="CHAR(32)")
	private String pid;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 
	 * 項目類別<p/>
	 * 1限額條件<br/>
	 *  2利(費)率<br/>
	 *  3擔保品<br/>
	 *  4其他敘做條件<br/>
	 *  5敘做條件異動情形<br/>
	 *  6附表第一頁<br/>
	 *  7附表第二頁<br/>
	 *  8附表第三頁<br/>
	 *  <br/>
	 *  100/11/29調整<br/>
	 *  9動撥提示用語<br/>
	 *  A總處核定意見<br/>
	 *  B董事會意見<br/>
	 *  <br/>
	 *  101/02/21調整<br/>
	 *  C聯貸說明  (※國內個金)<br/>
	 *  D 國內個金團貸借保人說明(※國內個金)<br/>
	 *  X 央行購置註記
	 */
	@Size(max=1)
	@Column(name="ITEMTYPE", length=1, columnDefinition="CHAR(1)")
	private String itemType;

	/** 
	 * 列印於主表<p/>
	 * 0.印於主表(預設)<br/>
	 *  1.印於附表第一頁<br/>
	 *  2.印於附表第二頁<br/>
	 *  3.印於附表第三頁
	 */
	@Size(max=1)
	@Column(name="PAGENUM", length=1, columnDefinition="CHAR(1)")
	private String pageNum;

	/** 
	 * 項目說明<p/>
	 * 100/11/29調整<br/>
	 *  for「9動撥提示用語」承諾事項<br/>
	 *  最多只能輸入３９０個全型字！
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name="ITEMDSCR", columnDefinition="CLOB")
	private String itemDscr;

	/** 
	 * 項目補充<p/>
	 * 100/11/29調整<br/>
	 *  for「4其他敘做條件」<br/>
	 *  存放內容：<br/>
	 *  1.動撥提示用語<br/>
	 *  for「9動撥提示用語」注意事項<br/>
	 *  最多只能輸入３０個全型字！<br/>
	 *  <br/>
	 *  101/03/29調整<br/>
	 *  2.總處核定意見<br/>
	 *  3.董事會意見<br/>
	 *  <br/>
	 *  102/5/14 長度由1080 -> 3000
	 */
	@Size(max=3000)
	@Column(name="TOALOAN", length=3000, columnDefinition="VARCHAR(3000)")
	private String toALoan;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得文件編號PID<p/>
	 * 102/01/17新增
	 */
	public String getPid() {
		return this.pid;
	}
	/**
	 *  設定文件編號PID<p/>
	 *  102/01/17新增
	 **/
	public void setPid(String value) {
		this.pid = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 
	 * 取得項目類別<p/>
	 * 1限額條件<br/>
	 *  2利(費)率<br/>
	 *  3擔保品<br/>
	 *  4其他敘做條件<br/>
	 *  5敘做條件異動情形<br/>
	 *  6附表第一頁<br/>
	 *  7附表第二頁<br/>
	 *  8附表第三頁<br/>
	 *  <br/>
	 *  100/11/29調整<br/>
	 *  9動撥提示用語<br/>
	 *  A總處核定意見<br/>
	 *  B董事會意見<br/>
	 *  <br/>
	 *  101/02/21調整<br/>
	 *  C聯貸說明  (※國內個金)<br/>
	 *  D 國內個金團貸借保人說明(※國內個金)<br/>
	 *  X 央行購置註記
	 */
	public String getItemType() {
		return this.itemType;
	}
	/**
	 *  設定項目類別<p/>
	 *  1限額條件<br/>
	 *  2利(費)率<br/>
	 *  3擔保品<br/>
	 *  4其他敘做條件<br/>
	 *  5敘做條件異動情形<br/>
	 *  6附表第一頁<br/>
	 *  7附表第二頁<br/>
	 *  8附表第三頁<br/>
	 *  <br/>
	 *  100/11/29調整<br/>
	 *  9動撥提示用語<br/>
	 *  A總處核定意見<br/>
	 *  B董事會意見<br/>
	 *  <br/>
	 *  101/02/21調整<br/>
	 *  C聯貸說明  (※國內個金)<br/>
	 *  D 國內個金團貸借保人說明(※國內個金)<br/>
	 *  X 央行購置註記
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/** 
	 * 取得列印於主表<p/>
	 * 0.印於主表(預設)<br/>
	 *  1.印於附表第一頁<br/>
	 *  2.印於附表第二頁<br/>
	 *  3.印於附表第三頁
	 */
	public String getPageNum() {
		return this.pageNum;
	}
	/**
	 *  設定列印於主表<p/>
	 *  0.印於主表(預設)<br/>
	 *  1.印於附表第一頁<br/>
	 *  2.印於附表第二頁<br/>
	 *  3.印於附表第三頁
	 **/
	public void setPageNum(String value) {
		this.pageNum = value;
	}

	/** 
	 * 取得項目說明<p/>
	 * 100/11/29調整<br/>
	 *  for「9動撥提示用語」承諾事項<br/>
	 *  最多只能輸入３９０個全型字！
	 */
	public String getItemDscr() {
		return this.itemDscr;
	}
	/**
	 *  設定項目說明<p/>
	 *  100/11/29調整<br/>
	 *  for「9動撥提示用語」承諾事項<br/>
	 *  最多只能輸入３９０個全型字！
	 **/
	public void setItemDscr(String value) {
		this.itemDscr = value;
	}

	/** 
	 * 取得項目補充<p/>
	 * 100/11/29調整<br/>
	 *  for「4其他敘做條件」<br/>
	 *  存放內容：<br/>
	 *  1.動撥提示用語<br/>
	 *  for「9動撥提示用語」注意事項<br/>
	 *  最多只能輸入３０個全型字！<br/>
	 *  <br/>
	 *  101/03/29調整<br/>
	 *  2.總處核定意見<br/>
	 *  3.董事會意見<br/>
	 *  <br/>
	 *  102/5/14 長度由1080 -> 3000
	 */
	public String getToALoan() {
		return this.toALoan;
	}
	/**
	 *  設定項目補充<p/>
	 *  100/11/29調整<br/>
	 *  for「4其他敘做條件」<br/>
	 *  存放內容：<br/>
	 *  1.動撥提示用語<br/>
	 *  for「9動撥提示用語」注意事項<br/>
	 *  最多只能輸入３０個全型字！<br/>
	 *  <br/>
	 *  101/03/29調整<br/>
	 *  2.總處核定意見<br/>
	 *  3.董事會意見<br/>
	 *  <br/>
	 *  102/5/14 長度由1080 -> 3000
	 **/
	public void setToALoan(String value) {
		this.toALoan = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
