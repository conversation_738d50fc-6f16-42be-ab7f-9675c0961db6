/* 
 *ObsdbELF422 Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service;

import java.util.List;

/**
 * <pre>
 * 額度資訊檔  ELF422
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
public interface ObsdbELF422Service {
	/**
	 * 新增
	 * 
	 * @param BRNID
	 *            上傳銀行代碼
	 * @param dataList
	 *            sql list
	 * 
	 *            <pre>
	 *           Object[]
	 *  custId   客戶編號
	 *  dupNo 重複序號
	 *  branch  分行別
	 *  cntrNo  額度序號
	 *  appDate  申請日
	 *  aprDate  核准日
	 *  custName  主要借人姓名
	 *  bossId 核准主管代號
	 *  bossName  核准主管姓名
	 *  unId  簽報書mainId
	 *  newCase   若為新貸案件則為Y
	 *  updater 上傳者
	 * </pre>
	 */
	void insert(String BRNID, List<Object[]> dataList);

	/**
	 * 刪除
	 * 
	 * @param BRNID
	 *            上傳銀行代碼
	 * @param custId
	 *            客戶編號
	 * @param dupNo
	 *            重複序號
	 * @param branch
	 *            分行別
	 * @param cntrNo
	 *            額度序號
	 */
	void delByKey(String BRNID, String custId, String dupNo, String branch,
			String cntrNo);
}
