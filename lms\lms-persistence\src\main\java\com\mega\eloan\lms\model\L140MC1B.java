/* 
 * L140MC1B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 房貸利率成數條件檢核明細紀錄檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140MC1B", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L140MC1B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 來源簽報書文件編號<p/>
	 * =L120M01A.mainId
	 */
	@Size(max=32)
	@Column(name="CASEMAINID", length=32, columnDefinition="CHAR(32)")
	private String caseMainId;

	/** 統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 產品種類 **/
	@Size(max=2)
	@Column(name="PRODKIND", length=2, columnDefinition="CHAR(02)")
	private String prodKind;

	/** 擔保品數量 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="COLLATERALCOUNT", columnDefinition="DECIMAL(3,0)")
	private Integer collateralCount;

	/** 分項金額 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="LOANAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal loanAmt;

	/** 現請額度－幣別 **/
	@Size(max=3)
	@Column(name="CURRENTAPPLYCURR", length=3, columnDefinition="CHAR(3)")
	private String currentApplyCurr;

	/** 現請額度－金額 **/
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="CURRENTAPPLYAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal currentApplyAmt;

	/** 餘額－幣別 **/
	@Size(max=3)
	@Column(name="BLCURR", length=3, columnDefinition="CHAR(3)")
	private String BLCurr;

	/** 餘額－金額 **/
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="BLAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal BLAmt;

	/** 擔保品時價 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="INAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal inAmt;

	/** 擔保品土地應計增值稅 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="LNDTAX", columnDefinition="DECIMAL(13,0)")
	private BigDecimal lndTax;

	/** 擔保品扣除寬限期預提折舊金額 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="DISAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal disAmt;

	/** 擔保品鑑估日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ESTDATE", columnDefinition="DATE")
	private Date estDate;

	/** 年薪(金額) **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="PAYAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal payAmt;

	/** 其他收入(金額) **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="OTHAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal othAmt;

	/** 擔保品土地(非/都市計畫區) **/
	@Size(max=1)
	@Column(name="SECTKIND", length=1, columnDefinition="CHAR(1)")
	private String sectKind;

	/** 性質 **/
	@Size(max=30)
	@Column(name="PROPERTY", length=30, columnDefinition="VARCHAR(30)")
	private String proPerty;

	/** 房貸利率方案 **/
	@Size(max=2)
	@Column(name="RATEPLAN", length=2, columnDefinition="CHAR(2)")
	private String ratePlan;

	/** 
	 * 房貸屬性<p/>
	 * 1-投資型/2-自住型
	 */
	@Size(max=1)
	@Column(name="REALESTATEHOUSELOANPLAN", length=1, columnDefinition="CHAR(1)")
	private String realEstateHouseLoanPlan;

	/** 有無搭配自住型成長方案條件 **/
	@Size(max=1)
	@Column(name="ISMATCHSELFUSEPLAN", length=1, columnDefinition="CHAR(1)")
	private String isMatchSelfUsePlan;

	/** 現請額度－是否循環使用 **/
	@Size(max=1)
	@Column(name="REUSE", length=1, columnDefinition="CHAR(1)")
	private String reUse;

	/** 擔保品縣市別代碼 **/
	@Size(max=10)
	@Column(name="COUNTYCITYCODE", length=10, columnDefinition="VARCHAR(10)")
	private String countyCityCode;

	/** 擔保品鄉鎮市區代碼 **/
	@Size(max=3)
	@Column(name="AREACODE", length=3, columnDefinition="VARCHAR(3)")
	private String areaCode;

	/** 
	 * 建物座落分區<p/>
	 * 1.A區、2.B區、3.其他地區、4.A+區
	 */
	@Size(max=1)
	@Column(name="DSTRTYPE", length=1, columnDefinition="CHAR(1)")
	private String dstrType;

	/** 初始核貸成數 **/
	@Digits(integer=3, fraction=2, groups = Check.class)
	@Column(name="INITIALRATIO", columnDefinition="DECIMAL(5,2)")
	private BigDecimal initialRatio;

	/** 房貸信用評等 **/
	@Size(max=2)
	@Column(name="GRADE3", length=2, columnDefinition="VARCHAR(2)")
	private String grade3;

	/** 
	 * 減成條件1<p/>
	 * 信用評等4-5等得減成客戶
	 */
	@Size(max=1)
	@Column(name="RATIOREDUCEDITEM1", length=1, columnDefinition="CHAR(1)")
	private String ratioReducedItem1;

	/** 
	 * 減成條件2<p/>
	 * 工業區住宅減成
	 */
	@Size(max=1)
	@Column(name="RATIOREDUCEDITEM2", length=1, columnDefinition="CHAR(1)")
	private String ratioReducedItem2;

	/** 
	 * 減成條件3<p/>
	 * 房屋貸款負債比控管減成對象
	 */
	@Size(max=1)
	@Column(name="RATIOREDUCEDITEM3", length=1, columnDefinition="CHAR(1)")
	private String ratioReducedItem3;

	/** 
	 * 搭配青安得免扣成數<p/>
	 * 需為產品種類59且為新做且評等為4~5等
	 */
	@Size(max=1)
	@Column(name="IS59NOTDEDUCT", length=1, columnDefinition="CHAR(1)")
	private String is59NotDeduct;

	/** 主要用途(登記用途) **/
	@Size(max=2)
	@Column(name="BLDUSE", length=2, columnDefinition="CHAR(2)")
	private String bldUse;

	/** 實際用途 **/
	@Size(max=2)
	@Column(name="BLDACTUALUSE", length=2, columnDefinition="CHAR(2)")
	private String bldActualUse;

	/** 
	 * 聯徵B50閒置工業用地<p/>
	 * Y:是 or 否, N:不適用
	 */
	@Size(max=1)
	@Column(name="ISIDLELAND", length=1, columnDefinition="CHAR(1)")
	private String isIdleLand;

	/** 個人負債比率 **/
	@Digits(integer=3, fraction=2, groups = Check.class)
	@Column(name="DRATE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal dRate;

	/** 家庭負債比率 **/
	@Digits(integer=3, fraction=2, groups = Check.class)
	@Column(name="FRATE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal fRate;

	/** 家庭年收入(幣別) **/
	@Size(max=3)
	@Column(name="FINCOMECURR", length=3, columnDefinition="CHAR(3)")
	private String fincomeCurr;

	/** 家庭年收入(金額) **/
	@Digits(integer=10, fraction=0, groups = Check.class)
	@Column(name="FINCOME", columnDefinition="DECIMAL(10,0)")
	private BigDecimal fincome;

	/** 家庭之負債佔資產比率(家庭資產總額/家庭負債餘額) **/
	@Digits(integer=3, fraction=2, groups = Check.class)
	@Column(name="FDEBTASSETRATIO", columnDefinition="DECIMAL(5,2)")
	private BigDecimal fDebtAssetRatio;

	/** 個人之負債佔資產比率(個人資產總額/個人負債餘額) **/
	@Digits(integer=3, fraction=2, groups = Check.class)
	@Column(name="IDEBTASSETRATIO", columnDefinition="DECIMAL(5,2)")
	private BigDecimal iDebtAssetRatio;

	/** 職業別大類 **/
	@Size(max=2)
	@Column(name="JOBTYPE1", length=2, columnDefinition="CHAR(2)")
	private String jobType1;

	/** 職業別細項 **/
	@Size(max=1)
	@Column(name="JOBTYPE2", length=1, columnDefinition="CHAR(1)")
	private String jobType2;

	/** 職稱 **/
	@Size(max=1)
	@Column(name="JOBTITLE", length=1, columnDefinition="CHAR(1)")
	private String jobTitle;

	/** 首購 **/
	@Size(max=1)
	@Column(name="ISFIRSTBUY", length=1, columnDefinition="CHAR(1)")
	private String isFirstBuy;

	/** 借款人_聯徵有無主債務(係指房貸) => Y:有主債務, N:無主債務 **/
	@Size(max=1)
	@Column(name="ISNOMAINDEBT", length=1, columnDefinition="CHAR(1)")
	private String isNoMainDebt;

	/** 借款人_聯徵有無從債務(係指房貸) => Y:有從債務, N:無從債務 **/
	@Size(max=1)
	@Column(name="ISNOSECONDDEBT", length=1, columnDefinition="CHAR(1)")
	private String isNoSecondDebt;

	/** 
	 * 加成條件(1)<p/>
	 * 是否為醫師、會計師、律師、建築師、上市櫃公司財務長或負責人
	 */
	@Size(max=1)
	@Column(name="RATIOADDEDITEM1", length=1, columnDefinition="CHAR(1)")
	private String ratioAddedItem1;

	/** 
	 * 加成條件(2)<p/>
	 * 屬首購之公教人員(公立機關含學校、公營事業編制內正式現職員工，不含軍職人員、試用人員及約聘僱人員)
	 */
	@Size(max=1)
	@Column(name="RATIOADDEDITEM2", length=1, columnDefinition="CHAR(1)")
	private String ratioAddedItem2;

	/** 
	 * 加成條件(3)<p/>
	 * 歡喜房貸專案不動產分區屬六都(台北市、新北市、桃園市、台中市、台南市、高雄市) A+區及新竹市(含新竹縣竹北市都市計畫區)A+區之首購且聯徵無主、從債務(係指房貸)，且個人(或家庭)負債比率60%以下者
	 */
	@Size(max=1)
	@Column(name="RATIOADDEDITEM3", length=1, columnDefinition="CHAR(1)")
	private String ratioAddedItem3;

	/** 
	 * 加成條件(4)<p/>
	 * 歡喜房貸專案不動產分區屬其他縣市A+區之首購且聯徵無主、從債務(係指房貸)，且個人(或家庭)負債比率30%以下者
	 */
	@Size(max=1)
	@Column(name="RATIOADDEDITEM4", length=1, columnDefinition="CHAR(1)")
	private String ratioAddedItem4;

	/** 是否房貸壽險躉繳保費融資 **/
	@Size(max=1)
	@Column(name="INSFLAG", length=1, columnDefinition="CHAR(1)")
	private String insFlag;

	/** 保費融資-金額 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="INSLOANBAL", columnDefinition="DECIMAL(13,0)")
	private BigDecimal insLoanbal;

	/** 
	 * 成數限制(1)<p/>
	 * 台北市全區或新北市捷運(含機場捷運) 直線距離1000 公尺及淡海輕軌捷運等各車站(含已興建中直線距離500 公尺)內，擔保品提供人限借款人本人或配偶
	 */
	@Size(max=1)
	@Column(name="RATIOLIMIT1", length=1, columnDefinition="CHAR(1)")
	private String ratioLimit1;

	/** 
	 * 成數限制(2)<p/>
	 * 公教人員(政府機關(不含軍、警、消防)、公營事業、公立中小學以上學校之編制內正式職員)
	 */
	@Size(max=1)
	@Column(name="RATIOLIMIT2", length=1, columnDefinition="CHAR(1)")
	private String ratioLimit2;

	/** 
	 * 成數限制(3)<p/>
	 * 年收入60 萬元以上且【聯徵中心總負債(含本次)/年收入】8 倍以下
	 */
	@Size(max=1)
	@Column(name="RATIOLIMIT3", length=1, columnDefinition="CHAR(1)")
	private String ratioLimit3;

	/** 
	 * 成數限制(4)<p/>
	 * 借款人及配偶合併年收入120 萬元以上且【聯徵中心總負債(含本次)/年收入】12 倍以下
	 */
	@Size(max=1)
	@Column(name="RATIOLIMIT4", length=1, columnDefinition="CHAR(1)")
	private String ratioLimit4;

	/** 
	 * 成數限制(5)<p/>
	 * 最近半年在本行活(儲)存平均餘額達50 萬元以上
	 */
	@Size(max=1)
	@Column(name="RATIOLIMIT5", length=1, columnDefinition="CHAR(1)")
	private String ratioLimit5;

	/** 
	 * 成數限制(6)<p/>
	 * 房貸信用評等為3 等以上
	 */
	@Size(max=1)
	@Column(name="RATIOLIMIT6", length=1, columnDefinition="CHAR(1)")
	private String ratioLimit6;

	/** 
	 * 成數限制(7)<p/>
	 * 成數限制_國別非TW-中華民國
	 */
	@Size(max=1)
	@Column(name="RATIOLIMIT7", length=1, columnDefinition="CHAR(1)")
	private String ratioLimit7;

	/** 
	 * 成數限制(8)<p/>
	 * 工業住宅核貸成數規範
	 */
	@Size(max=1)
	@Column(name="RATIOLIMIT8", length=1, columnDefinition="CHAR(1)")
	private String ratioLimit8;

	/** 
	 * 成數限制(9)<p/>
	 * 青安成數
	 */
	@Size(max=1)
	@Column(name="RATIOLIMIT9", length=1, columnDefinition="CHAR(1)")
	private String ratioLimit9;

	/** 
	 * 調整後成數限制1<p/>
	 * 台北市、新北市、桃園市、新竹市、台中市、台南市、高雄市、新竹縣竹北市(都市計畫區)、新竹縣竹東鎮(都市計畫區)、苗栗縣竹南鎮(都市計畫區)、苗栗縣頭份市(都市計畫區)、苗栗縣苗栗市(都市計畫區) 等不動產分區屬A+、A區調整後核貸成數最高不逾8.5成
	 */
	@Size(max=1)
	@Column(name="RATIOADJUSTEDLIMIT1", length=1, columnDefinition="CHAR(1)")
	private String ratioAdjustedLimit1;

	/** 
	 * 調整後成數限制2<p/>
	 * 其他縣市A+區調整後最終核貸成數最高不逾8成，新北市、桃園市、台中市、台南市、高雄市、新竹縣竹東鎮、苗栗縣竹南鎮、苗栗縣頭份市、苗栗縣苗栗市等B區及其他縣市A區及B區，調整後最終核貸成數最高不逾7.5成其他縣市：係指台北市、新北市、桃園市、新竹市、台中市、台南市、高雄市以外之縣市
	 */
	@Size(max=1)
	@Column(name="RATIOADJUSTEDLIMIT2", length=1, columnDefinition="CHAR(1)")
	private String ratioAdjustedLimit2;

	/** 
	 * 調整後成數限制3<p/>
	 * 檢核2時有發現Y1<br/>
	 *  最高額度上限不得逾8.5成
	 */
	@Size(max=1)
	@Column(name="RATIOADJUSTEDLIMIT3", length=1, columnDefinition="CHAR(1)")
	private String ratioAdjustedLimit3;

	/** 
	 * 調整後成數限制4<p/>
	 * 前述條件若有符合成數限<br/>
	 *  制(1)~(6)項且搭配房貸壽險，最高額度上限不得逾8.5成
	 */
	@Size(max=1)
	@Column(name="RATIOADJUSTEDLIMIT4", length=1, columnDefinition="CHAR(1)")
	private String ratioAdjustedLimit4;

	/** 
	 * 調整後成數限制5<p/>
	 * 如符合自住型成長直達8成之條件，<br/>
	 *  成數限制最高8成
	 */
	@Size(max=1)
	@Column(name="RATIOADJUSTEDLIMIT5", length=1, columnDefinition="CHAR(1)")
	private String ratioAdjustedLimit5;

	/** 專案種類 **/
	@Size(max=2)
	@Column(name="PROJCLASS", length=2, columnDefinition="CHAR(2)")
	private String projClass;

	/** (不動產)核貸成數 **/
	@Digits(integer=3, fraction=2, groups = Check.class)
	@Column(name="APPROVEDPERCENT", columnDefinition="DECIMAL(5,2)")
	private BigDecimal approvedPercent;

	/** 房貸壽險融資再加成成數 **/
	@Digits(integer=3, fraction=2, groups = Check.class)
	@Column(name="INSPLUSRATIO", columnDefinition="DECIMAL(5,2)")
	private BigDecimal InsPlusRatio;

	/** 檢核一產品成數 **/
	@Digits(integer=3, fraction=2, groups = Check.class)
	@Column(name="CHECK1PRODKINDRATIO", columnDefinition="DECIMAL(5,2)")
	private BigDecimal check1ProdkindRatio;

	/** 檢核一檢核結果 **/
	@Size(max=1)
	@Column(name="CHECK1RESULTS", length=1, columnDefinition="CHAR(1)")
	private String check1Results;

	/** 檢核二核貸成數 **/
	@Digits(integer=3, fraction=4, groups = Check.class)
	@Column(name="CHECK2APPROVEDRATIO", columnDefinition="DECIMAL(7,4)")
	private BigDecimal check2ApprovedRatio;

	/** 檢核二檢核結果 **/
	@Size(max=1)
	@Column(name="CHECK2RESULTS", length=1, columnDefinition="CHAR(1)")
	private String check2Results;

	/** 
	 * 菁英方案分項金額<p/>
	 * 檢核三金額
	 */
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="ELITEPLANLOANAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal elitePlanLoanAmt;

	/** 
	 * 菁英方案_成數限制<p/>
	 * 檢核三成數
	 */
	@Digits(integer=3, fraction=2, groups = Check.class)
	@Column(name="ELITEPLANRATIOLIMIT", columnDefinition="DECIMAL(5,2)")
	private BigDecimal elitePlanRatioLimit;

	/** 菁英方案檢核結果 **/
	@Size(max=1)
	@Column(name="ELITECHECKRESULTS", length=1, columnDefinition="CHAR(1)")
	private String eliteCheckResults;

	/** 總檢核結果 **/
	@Size(max=1)
	@Column(name="FINALRESULTS", length=1, columnDefinition="CHAR(1)")
	private String finalResults;

	/** 
	 * 是否為都市計畫區<p/>
	 * 座落分區其它註記有勾選32-都市計畫地區的選項
	 */
	@Size(max=1)
	@Column(name="ISURBANPLANAREA", length=1, columnDefinition="CHAR(1)")
	private String isUrbanPlanArea;

	/** 是否為其他縣市 **/
	@Size(max=1)
	@Column(name="ISOTHERCOUNTYCITY", length=1, columnDefinition="CHAR(1)")
	private String isOtherCountyCity;

	/** 
	 * 成數檢核類型<p/>
	 * A:青安, C:自住型成長, D:投資型, B:自住型, 房貸壽險(E), 菁英方案(F)
	 * 
	 * (判斷適用檢核類型為優先房貸壽(E) 依序為1青安 2 自住型成長 3自住型or投資型 4菁英方案)			
	 */
	@Size(max=1)
	@Column(name="PRODKINDTYPE", length=1, columnDefinition="CHAR(1)")
	private String prodKindType;

	/** 不須檢核事項代碼 **/
	@Size(max=15)
	@Column(name="NOTCHECKCODE", length=15, columnDefinition="VARCHAR(15)")
	private String notCheckCode;

	/** 擔保品Oid **/
	@Size(max=32)
	@Column(name="CMSOID", length=32, columnDefinition="CHAR(32)")
	private String cmsOid;

	/** 夫妻年收入 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="YFAMAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal yFamAmt;

	/** 國別 **/
	@Size(max=2)
	@Column(name="NTCODE", length=2, columnDefinition="CHAR(2)")
	private String ntCode;

	/** 縣市代碼_鄉鎮市區_是否為都市計畫區 key值 **/
	@Size(max=20)
	@Column(name="CITYAREAURBANPLANKEY", length=20, columnDefinition="VARCHAR(20)")
	private String cityAreaUrbanPlanKey;

	/** 版本 **/
	@Size(max=10)
	@Column(name="VERSION", length=10, columnDefinition="VARCHAR(10)")
	private String version;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;
	
	/** 
	 * 聯徵B42共同債務查詢－擔保品類別<p/>
	 * C120S01E.isQdata32<br/>
	 *  1.有、2.無、3.N.A
	 */
	@Size(max=1)
	@Column(name="QRYMUTUALDEBTB42", length=1, columnDefinition="CHAR(1)")
	private String qryMutualDebtB42;
	
	/** 
	 * 借款人三年內購置不動產結案資訊，是否有近一年有二戶以上授信借貸結案紀錄<p/>
	 * C120S01E.isQdata33<br/>
	 *  1.有、2.無、3.N.A
	 */
	@Size(max=1)
	@Column(name="OVER2DATAPASTY", length=1, columnDefinition="CHAR(1)")
	private String over2DataPastY;
	
	/** 
	 * 借款人三年內購置不動產結案資訊，是否有近三年有二戶以上授信借貸結案紀錄<p/>
	 * C120S01E.isQdata34<br/>
	 *  1.有、2.無、3.N.A
	 */
	@Size(max=1)
	@Column(name="OVER2DATAPAST3Y", length=1, columnDefinition="CHAR(1)")
	private String over2DataPast3y;
	
	/** 
	 * 符合減成條件三「個人負債比率貸放控管原則」減成對象及「負債比不介於61~80之間」
	 * 不檢核房貸壽險加成
	 */
	@Size(max=1)
	@Column(name="ISNOTCHKHOUSEINS", length=1, columnDefinition="CHAR(1)")
	private String isNotChkHouseIns;
	
	/** 
	 * 聯徵B42從債務查詢－擔保品類別<p/>
	 * C101S01E.isQdata31<br/>
	 *  1.有、2.無、3.N.A
	 */
	@Size(max=1)
	@Column(name="QRYCOLLATERALB42", length=1, columnDefinition="CHAR(1)")
	private String qryCollateralB42;
	
	/** 檢核二最終核貸成數 **/
	@Digits(integer=3, fraction=4, groups = Check.class)
	@Column(name="CHECK2FINALRATIO", columnDefinition="DECIMAL(7,4)")
	private BigDecimal check2FinalRatio;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得來源簽報書文件編號<p/>
	 * =L120M01A.mainId
	 */
	public String getCaseMainId() {
		return this.caseMainId;
	}
	/**
	 *  設定來源簽報書文件編號<p/>
	 *  =L120M01A.mainId
	 **/
	public void setCaseMainId(String value) {
		this.caseMainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得產品種類 **/
	public String getProdKind() {
		return this.prodKind;
	}
	/** 設定產品種類 **/
	public void setProdKind(String value) {
		this.prodKind = value;
	}

	/** 取得擔保品數量 **/
	public Integer getCollateralCount() {
		return this.collateralCount;
	}
	/** 設定擔保品數量 **/
	public void setCollateralCount(Integer value) {
		this.collateralCount = value;
	}

	/** 取得分項金額 **/
	public BigDecimal getLoanAmt() {
		return this.loanAmt;
	}
	/** 設定分項金額 **/
	public void setLoanAmt(BigDecimal value) {
		this.loanAmt = value;
	}

	/** 取得現請額度－幣別 **/
	public String getCurrentApplyCurr() {
		return this.currentApplyCurr;
	}
	/** 設定現請額度－幣別 **/
	public void setCurrentApplyCurr(String value) {
		this.currentApplyCurr = value;
	}

	/** 取得現請額度－金額 **/
	public BigDecimal getCurrentApplyAmt() {
		return this.currentApplyAmt;
	}
	/** 設定現請額度－金額 **/
	public void setCurrentApplyAmt(BigDecimal value) {
		this.currentApplyAmt = value;
	}

	/** 取得餘額－幣別 **/
	public String getBLCurr() {
		return this.BLCurr;
	}
	/** 設定餘額－幣別 **/
	public void setBLCurr(String value) {
		this.BLCurr = value;
	}

	/** 取得餘額－金額 **/
	public BigDecimal getBLAmt() {
		return this.BLAmt;
	}
	/** 設定餘額－金額 **/
	public void setBLAmt(BigDecimal value) {
		this.BLAmt = value;
	}

	/** 取得擔保品時價 **/
	public BigDecimal getInAmt() {
		return this.inAmt;
	}
	/** 設定擔保品時價 **/
	public void setInAmt(BigDecimal value) {
		this.inAmt = value;
	}

	/** 取得擔保品土地應計增值稅 **/
	public BigDecimal getLndTax() {
		return this.lndTax;
	}
	/** 設定擔保品土地應計增值稅 **/
	public void setLndTax(BigDecimal value) {
		this.lndTax = value;
	}

	/** 取得擔保品扣除寬限期預提折舊金額 **/
	public BigDecimal getDisAmt() {
		return this.disAmt;
	}
	/** 設定擔保品扣除寬限期預提折舊金額 **/
	public void setDisAmt(BigDecimal value) {
		this.disAmt = value;
	}

	/** 取得擔保品鑑估日期 **/
	public Date getEstDate() {
		return this.estDate;
	}
	/** 設定擔保品鑑估日期 **/
	public void setEstDate(Date value) {
		this.estDate = value;
	}

	/** 取得年薪(金額) **/
	public BigDecimal getPayAmt() {
		return this.payAmt;
	}
	/** 設定年薪(金額) **/
	public void setPayAmt(BigDecimal value) {
		this.payAmt = value;
	}

	/** 取得其他收入(金額) **/
	public BigDecimal getOthAmt() {
		return this.othAmt;
	}
	/** 設定其他收入(金額) **/
	public void setOthAmt(BigDecimal value) {
		this.othAmt = value;
	}

	/** 取得擔保品土地(非/都市計畫區) **/
	public String getSectKind() {
		return this.sectKind;
	}
	/** 設定擔保品土地(非/都市計畫區) **/
	public void setSectKind(String value) {
		this.sectKind = value;
	}

	/** 取得性質 **/
	public String getProPerty() {
		return this.proPerty;
	}
	/** 設定性質 **/
	public void setProPerty(String value) {
		this.proPerty = value;
	}

	/** 取得房貸利率方案 **/
	public String getRatePlan() {
		return this.ratePlan;
	}
	/** 設定房貸利率方案 **/
	public void setRatePlan(String value) {
		this.ratePlan = value;
	}

	/** 
	 * 取得房貸屬性<p/>
	 * 1-投資型/2-自住型
	 */
	public String getRealEstateHouseLoanPlan() {
		return this.realEstateHouseLoanPlan;
	}
	/**
	 *  設定房貸屬性<p/>
	 *  1-投資型/2-自住型
	 **/
	public void setRealEstateHouseLoanPlan(String value) {
		this.realEstateHouseLoanPlan = value;
	}

	/** 取得有無搭配自住型成長方案條件 **/
	public String getIsMatchSelfUsePlan() {
		return this.isMatchSelfUsePlan;
	}
	/** 設定有無搭配自住型成長方案條件 **/
	public void setIsMatchSelfUsePlan(String value) {
		this.isMatchSelfUsePlan = value;
	}

	/** 取得現請額度－是否循環使用 **/
	public String getReUse() {
		return this.reUse;
	}
	/** 設定現請額度－是否循環使用 **/
	public void setReUse(String value) {
		this.reUse = value;
	}

	/** 取得擔保品縣市別代碼 **/
	public String getCountyCityCode() {
		return this.countyCityCode;
	}
	/** 設定擔保品縣市別代碼 **/
	public void setCountyCityCode(String value) {
		this.countyCityCode = value;
	}

	/** 取得擔保品鄉鎮市區代碼 **/
	public String getAreaCode() {
		return this.areaCode;
	}
	/** 設定擔保品鄉鎮市區代碼 **/
	public void setAreaCode(String value) {
		this.areaCode = value;
	}

	/** 
	 * 取得建物座落分區<p/>
	 * 1.A區、2.B區、3.其他地區、4.A+區
	 */
	public String getDstrType() {
		return this.dstrType;
	}
	/**
	 *  設定建物座落分區<p/>
	 *  1.A區、2.B區、3.其他地區、4.A+區
	 **/
	public void setDstrType(String value) {
		this.dstrType = value;
	}

	/** 取得初始核貸成數 **/
	public BigDecimal getInitialRatio() {
		return this.initialRatio;
	}
	/** 設定初始核貸成數 **/
	public void setInitialRatio(BigDecimal value) {
		this.initialRatio = value;
	}

	/** 取得房貸信用評等 **/
	public String getGrade3() {
		return this.grade3;
	}
	/** 設定房貸信用評等 **/
	public void setGrade3(String value) {
		this.grade3 = value;
	}

	/** 
	 * 取得減成條件1<p/>
	 * 信用評等4-5等得減成客戶
	 */
	public String getRatioReducedItem1() {
		return this.ratioReducedItem1;
	}
	/**
	 *  設定減成條件1<p/>
	 *  信用評等4-5等得減成客戶
	 **/
	public void setRatioReducedItem1(String value) {
		this.ratioReducedItem1 = value;
	}

	/** 
	 * 取得減成條件2<p/>
	 * 工業區住宅減成
	 */
	public String getRatioReducedItem2() {
		return this.ratioReducedItem2;
	}
	/**
	 *  設定減成條件2<p/>
	 *  工業區住宅減成
	 **/
	public void setRatioReducedItem2(String value) {
		this.ratioReducedItem2 = value;
	}

	/** 
	 * 取得減成條件3<p/>
	 * 房屋貸款負債比控管減成對象
	 */
	public String getRatioReducedItem3() {
		return this.ratioReducedItem3;
	}
	/**
	 *  設定減成條件3<p/>
	 *  房屋貸款負債比控管減成對象
	 **/
	public void setRatioReducedItem3(String value) {
		this.ratioReducedItem3 = value;
	}

	/** 
	 * 取得搭配青安得免扣成數<p/>
	 * 需為產品種類59且為新做且評等為4~5等
	 */
	public String getIs59NotDeduct() {
		return this.is59NotDeduct;
	}
	/**
	 *  設定搭配青安得免扣成數<p/>
	 *  需為產品種類59且為新做且評等為4~5等
	 **/
	public void setIs59NotDeduct(String value) {
		this.is59NotDeduct = value;
	}

	/** 取得主要用途(登記用途) **/
	public String getBldUse() {
		return this.bldUse;
	}
	/** 設定主要用途(登記用途) **/
	public void setBldUse(String value) {
		this.bldUse = value;
	}

	/** 取得實際用途 **/
	public String getBldActualUse() {
		return this.bldActualUse;
	}
	/** 設定實際用途 **/
	public void setBldActualUse(String value) {
		this.bldActualUse = value;
	}

	/** 
	 * 取得聯徵B50閒置工業用地<p/>
	 * Y:是, N:否, -:不適用
	 */
	public String getIsIdleLand() {
		return this.isIdleLand;
	}
	/**
	 *  設定聯徵B50閒置工業用地<p/>
	 *  Y:是, N:否, -:不適用
	 **/
	public void setIsIdleLand(String value) {
		this.isIdleLand = value;
	}

	/** 取得個人負債比率 **/
	public BigDecimal getDRate() {
		return this.dRate;
	}
	/** 設定個人負債比率 **/
	public void setDRate(BigDecimal value) {
		this.dRate = value;
	}

	/** 取得家庭負債比率 **/
	public BigDecimal getFRate() {
		return this.fRate;
	}
	/** 設定家庭負債比率 **/
	public void setFRate(BigDecimal value) {
		this.fRate = value;
	}

	/** 取得家庭年收入(幣別) **/
	public String getFincomeCurr() {
		return this.fincomeCurr;
	}
	/** 設定家庭年收入(幣別) **/
	public void setFincomeCurr(String value) {
		this.fincomeCurr = value;
	}

	/** 取得家庭年收入(金額) **/
	public BigDecimal getFincome() {
		return this.fincome;
	}
	/** 設定家庭年收入(金額) **/
	public void setFincome(BigDecimal value) {
		this.fincome = value;
	}

	/** 取得家庭之負債佔資產比率(家庭資產總額/家庭負債餘額) **/
	public BigDecimal getFDebtAssetRatio() {
		return this.fDebtAssetRatio;
	}
	/** 設定家庭之負債佔資產比率(家庭資產總額/家庭負債餘額) **/
	public void setFDebtAssetRatio(BigDecimal value) {
		this.fDebtAssetRatio = value;
	}

	/** 取得個人之負債佔資產比率(個人資產總額/個人負債餘額) **/
	public BigDecimal getIDebtAssetRatio() {
		return this.iDebtAssetRatio;
	}
	/** 設定個人之負債佔資產比率(個人資產總額/個人負債餘額) **/
	public void setIDebtAssetRatio(BigDecimal value) {
		this.iDebtAssetRatio = value;
	}

	/** 取得職業別大類 **/
	public String getJobType1() {
		return this.jobType1;
	}
	/** 設定職業別大類 **/
	public void setJobType1(String value) {
		this.jobType1 = value;
	}

	/** 取得職業別細項 **/
	public String getJobType2() {
		return this.jobType2;
	}
	/** 設定職業別細項 **/
	public void setJobType2(String value) {
		this.jobType2 = value;
	}

	/** 取得職稱 **/
	public String getJobTitle() {
		return this.jobTitle;
	}
	/** 設定職稱 **/
	public void setJobTitle(String value) {
		this.jobTitle = value;
	}

	/** 取得首購 **/
	public String getIsFirstBuy() {
		return this.isFirstBuy;
	}
	/** 設定首購 **/
	public void setIsFirstBuy(String value) {
		this.isFirstBuy = value;
	}

	/** 取得借款人_聯徵無主債務(係指房貸) **/
	public String getIsNoMainDebt() {
		return this.isNoMainDebt;
	}
	/** 設定借款人_聯徵無主債務(係指房貸) **/
	public void setIsNoMainDebt(String value) {
		this.isNoMainDebt = value;
	}

	/** 取得借款人_聯徵無從債務 **/
	public String getIsNoSecondDebt() {
		return this.isNoSecondDebt;
	}
	/** 設定借款人_聯徵無從債務 **/
	public void setIsNoSecondDebt(String value) {
		this.isNoSecondDebt = value;
	}

	/** 
	 * 取得加成條件(1)<p/>
	 * 是否為醫師、會計師、律師、建築師、上市櫃公司財務長或負責人
	 */
	public String getRatioAddedItem1() {
		return this.ratioAddedItem1;
	}
	/**
	 *  設定加成條件(1)<p/>
	 *  是否為醫師、會計師、律師、建築師、上市櫃公司財務長或負責人
	 **/
	public void setRatioAddedItem1(String value) {
		this.ratioAddedItem1 = value;
	}

	/** 
	 * 取得加成條件(2)<p/>
	 * 屬首購之公教人員(公立機關含學校、公營事業編制內正式現職員工，不含軍職人員、試用人員及約聘僱人員)
	 */
	public String getRatioAddedItem2() {
		return this.ratioAddedItem2;
	}
	/**
	 *  設定加成條件(2)<p/>
	 *  屬首購之公教人員(公立機關含學校、公營事業編制內正式現職員工，不含軍職人員、試用人員及約聘僱人員)
	 **/
	public void setRatioAddedItem2(String value) {
		this.ratioAddedItem2 = value;
	}

	/** 
	 * 取得加成條件(3)<p/>
	 * 歡喜房貸專案不動產分區屬六都(台北市、新北市、桃園市、台中市、台南市、高雄市) A+區及新竹市(含新竹縣竹北市都市計畫區)A+區之首購且聯徵無主、從債務(係指房貸)，且個人(或家庭)負債比率60%以下者
	 */
	public String getRatioAddedItem3() {
		return this.ratioAddedItem3;
	}
	/**
	 *  設定加成條件(3)<p/>
	 *  歡喜房貸專案不動產分區屬六都(台北市、新北市、桃園市、台中市、台南市、高雄市) A+區及新竹市(含新竹縣竹北市都市計畫區)A+區之首購且聯徵無主、從債務(係指房貸)，且個人(或家庭)負債比率60%以下者
	 **/
	public void setRatioAddedItem3(String value) {
		this.ratioAddedItem3 = value;
	}

	/** 
	 * 取得加成條件(4)<p/>
	 * 歡喜房貸專案不動產分區屬其他縣市A+區之首購且聯徵無主、從債務(係指房貸)，且個人(或家庭)負債比率30%以下者
	 */
	public String getRatioAddedItem4() {
		return this.ratioAddedItem4;
	}
	/**
	 *  設定加成條件(4)<p/>
	 *  歡喜房貸專案不動產分區屬其他縣市A+區之首購且聯徵無主、從債務(係指房貸)，且個人(或家庭)負債比率30%以下者
	 **/
	public void setRatioAddedItem4(String value) {
		this.ratioAddedItem4 = value;
	}

	/** 取得是否房貸壽險躉繳保費融資 **/
	public String getInsFlag() {
		return this.insFlag;
	}
	/** 設定是否房貸壽險躉繳保費融資 **/
	public void setInsFlag(String value) {
		this.insFlag = value;
	}

	/** 取得保費融資-金額 **/
	public BigDecimal getInsLoanbal() {
		return this.insLoanbal;
	}
	/** 設定保費融資-金額 **/
	public void setInsLoanbal(BigDecimal value) {
		this.insLoanbal = value;
	}

	/** 
	 * 取得成數限制(1)<p/>
	 * 台北市全區或新北市捷運(含機場捷運) 直線距離1000 公尺及淡海輕軌捷運等各車站(含已興建中直線距離500 公尺)內，擔保品提供人限借款人本人或配偶
	 */
	public String getRatioLimit1() {
		return this.ratioLimit1;
	}
	/**
	 *  設定成數限制(1)<p/>
	 *  台北市全區或新北市捷運(含機場捷運) 直線距離1000 公尺及淡海輕軌捷運等各車站(含已興建中直線距離500 公尺)內，擔保品提供人限借款人本人或配偶
	 **/
	public void setRatioLimit1(String value) {
		this.ratioLimit1 = value;
	}

	/** 
	 * 取得成數限制(2)<p/>
	 * 公教人員(政府機關(不含軍、警、消防)、公營事業、公立中小學以上學校之編制內正式職員)
	 */
	public String getRatioLimit2() {
		return this.ratioLimit2;
	}
	/**
	 *  設定成數限制(2)<p/>
	 *  公教人員(政府機關(不含軍、警、消防)、公營事業、公立中小學以上學校之編制內正式職員)
	 **/
	public void setRatioLimit2(String value) {
		this.ratioLimit2 = value;
	}

	/** 
	 * 取得成數限制(3)<p/>
	 * 年收入60 萬元以上且【聯徵中心總負債(含本次)/年收入】8 倍以下
	 */
	public String getRatioLimit3() {
		return this.ratioLimit3;
	}
	/**
	 *  設定成數限制(3)<p/>
	 *  年收入60 萬元以上且【聯徵中心總負債(含本次)/年收入】8 倍以下
	 **/
	public void setRatioLimit3(String value) {
		this.ratioLimit3 = value;
	}

	/** 
	 * 取得成數限制(4)<p/>
	 * 借款人及配偶合併年收入120 萬元以上且【聯徵中心總負債(含本次)/年收入】12 倍以下
	 */
	public String getRatioLimit4() {
		return this.ratioLimit4;
	}
	/**
	 *  設定成數限制(4)<p/>
	 *  借款人及配偶合併年收入120 萬元以上且【聯徵中心總負債(含本次)/年收入】12 倍以下
	 **/
	public void setRatioLimit4(String value) {
		this.ratioLimit4 = value;
	}

	/** 
	 * 取得成數限制(5)<p/>
	 * 最近半年在本行活(儲)存平均餘額達50 萬元以上
	 */
	public String getRatioLimit5() {
		return this.ratioLimit5;
	}
	/**
	 *  設定成數限制(5)<p/>
	 *  最近半年在本行活(儲)存平均餘額達50 萬元以上
	 **/
	public void setRatioLimit5(String value) {
		this.ratioLimit5 = value;
	}

	/** 
	 * 取得成數限制(6)<p/>
	 * 房貸信用評等為3 等以上
	 */
	public String getRatioLimit6() {
		return this.ratioLimit6;
	}
	/**
	 *  設定成數限制(6)<p/>
	 *  房貸信用評等為3 等以上
	 **/
	public void setRatioLimit6(String value) {
		this.ratioLimit6 = value;
	}

	/** 
	 * 取得成數限制(7)<p/>
	 * 成數限制_國別非TW-中華民國
	 */
	public String getRatioLimit7() {
		return this.ratioLimit7;
	}
	/**
	 *  設定成數限制(7)<p/>
	 *  成數限制_國別非TW-中華民國
	 **/
	public void setRatioLimit7(String value) {
		this.ratioLimit7 = value;
	}

	/** 
	 * 取得成數限制(8)<p/>
	 * 工業住宅核貸成數規範
	 */
	public String getRatioLimit8() {
		return this.ratioLimit8;
	}
	/**
	 *  設定成數限制(8)<p/>
	 *  工業住宅核貸成數規範
	 **/
	public void setRatioLimit8(String value) {
		this.ratioLimit8 = value;
	}

	/** 
	 * 取得成數限制(9)<p/>
	 * 青安成數
	 */
	public String getRatioLimit9() {
		return this.ratioLimit9;
	}
	/**
	 *  設定成數限制(9)<p/>
	 *  青安成數
	 **/
	public void setRatioLimit9(String value) {
		this.ratioLimit9 = value;
	}

	/** 
	 * 取得調整後成數限制1<p/>
	 * 台北市、新北市、桃園市、新竹市、台中市、台南市、高雄市、新竹縣竹北市(都市計畫區)、新竹縣竹東鎮(都市計畫區)、苗栗縣竹南鎮(都市計畫區)、苗栗縣頭份市(都市計畫區)、苗栗縣苗栗市(都市計畫區) 等不動產分區屬A+、A區調整後核貸成數最高不逾8.5成
	 */
	public String getRatioAdjustedLimit1() {
		return this.ratioAdjustedLimit1;
	}
	/**
	 *  設定調整後成數限制1<p/>
	 *  台北市、新北市、桃園市、新竹市、台中市、台南市、高雄市、新竹縣竹北市(都市計畫區)、新竹縣竹東鎮(都市計畫區)、苗栗縣竹南鎮(都市計畫區)、苗栗縣頭份市(都市計畫區)、苗栗縣苗栗市(都市計畫區) 等不動產分區屬A+、A區調整後核貸成數最高不逾8.5成
	 **/
	public void setRatioAdjustedLimit1(String value) {
		this.ratioAdjustedLimit1 = value;
	}

	/** 
	 * 取得調整後成數限制2<p/>
	 * 其他縣市A+區調整後最終核貸成數最高不逾8成，新北市、桃園市、台中市、台南市、高雄市、新竹縣竹東鎮、苗栗縣竹南鎮、苗栗縣頭份市、苗栗縣苗栗市等B區及其他縣市A區及B區，調整後最終核貸成數最高不逾7.5成其他縣市：係指台北市、新北市、桃園市、新竹市、台中市、台南市、高雄市以外之縣市
	 */
	public String getRatioAdjustedLimit2() {
		return this.ratioAdjustedLimit2;
	}
	/**
	 *  設定調整後成數限制2<p/>
	 *  其他縣市A+區調整後最終核貸成數最高不逾8成，新北市、桃園市、台中市、台南市、高雄市、新竹縣竹東鎮、苗栗縣竹南鎮、苗栗縣頭份市、苗栗縣苗栗市等B區及其他縣市A區及B區，調整後最終核貸成數最高不逾7.5成其他縣市：係指台北市、新北市、桃園市、新竹市、台中市、台南市、高雄市以外之縣市
	 **/
	public void setRatioAdjustedLimit2(String value) {
		this.ratioAdjustedLimit2 = value;
	}

	/** 
	 * 取得調整後成數限制3<p/>
	 * 檢核2時有發現Y1<br/>
	 *  最高額度上限不得逾8.5成
	 */
	public String getRatioAdjustedLimit3() {
		return this.ratioAdjustedLimit3;
	}
	/**
	 *  設定調整後成數限制3<p/>
	 *  檢核2時有發現Y1<br/>
	 *  最高額度上限不得逾8.5成
	 **/
	public void setRatioAdjustedLimit3(String value) {
		this.ratioAdjustedLimit3 = value;
	}

	/** 
	 * 取得調整後成數限制4<p/>
	 * 前述條件若有符合成數限<br/>
	 *  制(1)~(6)項且搭配房貸壽險，最高額度上限不得逾8.5成
	 */
	public String getRatioAdjustedLimit4() {
		return this.ratioAdjustedLimit4;
	}
	/**
	 *  設定調整後成數限制4<p/>
	 *  前述條件若有符合成數限<br/>
	 *  制(1)~(6)項且搭配房貸壽險，最高額度上限不得逾8.5成
	 **/
	public void setRatioAdjustedLimit4(String value) {
		this.ratioAdjustedLimit4 = value;
	}

	/** 
	 * 取得調整後成數限制5<p/>
	 * 如符合自住型成長直達8成之條件，<br/>
	 *  成數限制最高8成
	 */
	public String getRatioAdjustedLimit5() {
		return this.ratioAdjustedLimit5;
	}
	/**
	 *  設定調整後成數限制5<p/>
	 *  如符合自住型成長直達8成之條件，<br/>
	 *  成數限制最高8成
	 **/
	public void setRatioAdjustedLimit5(String value) {
		this.ratioAdjustedLimit5 = value;
	}

	/** 取得專案種類 **/
	public String getProjClass() {
		return this.projClass;
	}
	/** 設定專案種類 **/
	public void setProjClass(String value) {
		this.projClass = value;
	}

	/** 取得(不動產)核貸成數 **/
	public BigDecimal getApprovedPercent() {
		return this.approvedPercent;
	}
	/** 設定(不動產)核貸成數 **/
	public void setApprovedPercent(BigDecimal value) {
		this.approvedPercent = value;
	}

	/** 取得房貸壽險融資再加成成數 **/
	public BigDecimal getInsPlusRatio() {
		return this.InsPlusRatio;
	}
	/** 設定房貸壽險融資再加成成數 **/
	public void setInsPlusRatio(BigDecimal value) {
		this.InsPlusRatio = value;
	}

	/** 取得檢核一產品成數 **/
	public BigDecimal getCheck1ProdkindRatio() {
		return this.check1ProdkindRatio;
	}
	/** 設定檢核一產品成數 **/
	public void setCheck1ProdkindRatio(BigDecimal value) {
		this.check1ProdkindRatio = value;
	}

	/** 取得檢核一檢核結果 **/
	public String getCheck1Results() {
		return this.check1Results;
	}
	/** 設定檢核一檢核結果 **/
	public void setCheck1Results(String value) {
		this.check1Results = value;
	}

	/** 取得檢核二核貸成數 **/
	public BigDecimal getCheck2ApprovedRatio() {
		return this.check2ApprovedRatio;
	}
	/** 設定檢核二核貸成數 **/
	public void setCheck2ApprovedRatio(BigDecimal value) {
		this.check2ApprovedRatio = value;
	}

	/** 取得檢核二檢核結果 **/
	public String getCheck2Results() {
		return this.check2Results;
	}
	/** 設定檢核二檢核結果 **/
	public void setCheck2Results(String value) {
		this.check2Results = value;
	}

	/** 
	 * 取得菁英方案分項金額<p/>
	 * 檢核三金額
	 */
	public BigDecimal getElitePlanLoanAmt() {
		return this.elitePlanLoanAmt;
	}
	/**
	 *  設定菁英方案分項金額<p/>
	 *  檢核三金額
	 **/
	public void setElitePlanLoanAmt(BigDecimal value) {
		this.elitePlanLoanAmt = value;
	}

	/** 
	 * 取得菁英方案_成數限制<p/>
	 * 檢核三成數
	 */
	public BigDecimal getElitePlanRatioLimit() {
		return this.elitePlanRatioLimit;
	}
	/**
	 *  設定菁英方案_成數限制<p/>
	 *  檢核三成數
	 **/
	public void setElitePlanRatioLimit(BigDecimal value) {
		this.elitePlanRatioLimit = value;
	}

	/** 取得菁英方案檢核結果 **/
	public String getEliteCheckResults() {
		return this.eliteCheckResults;
	}
	/** 設定菁英方案檢核結果 **/
	public void setEliteCheckResults(String value) {
		this.eliteCheckResults = value;
	}

	/** 取得總檢核結果 **/
	public String getFinalResults() {
		return this.finalResults;
	}
	/** 設定總檢核結果 **/
	public void setFinalResults(String value) {
		this.finalResults = value;
	}

	/** 
	 * 取得是否為都市計畫區<p/>
	 * 土地使用分區為1:都市土地, 則為都市計畫區
	 */
	public String getIsUrbanPlanArea() {
		return this.isUrbanPlanArea;
	}
	/**
	 *  設定是否為都市計畫區<p/>
	 *  土地使用分區為1:都市土地, 則為都市計畫區
	 **/
	public void setIsUrbanPlanArea(String value) {
		this.isUrbanPlanArea = value;
	}

	/** 取得是否為其他縣市 **/
	public String getIsOtherCountyCity() {
		return this.isOtherCountyCity;
	}
	/** 設定是否為其他縣市 **/
	public void setIsOtherCountyCity(String value) {
		this.isOtherCountyCity = value;
	}

	/** 
	 * 取得產品種類類型<p/>
	 * A:青安, C:自住型成長, D:投資型, B:自住型
	 */
	public String getProdKindType() {
		return this.prodKindType;
	}
	/**
	 *  設定產品種類類型<p/>
	 *  A:青安, C:自住型成長, D:投資型, B:自住型
	 **/
	public void setProdKindType(String value) {
		this.prodKindType = value;
	}

	/** 取得不須檢核事項代碼 **/
	public String getNotCheckCode() {
		return this.notCheckCode;
	}
	/** 設定不須檢核事項代碼 **/
	public void setNotCheckCode(String value) {
		this.notCheckCode = value;
	}

	/** 取得擔保品Oid **/
	public String getCmsOid() {
		return this.cmsOid;
	}
	/** 設定擔保品Oid **/
	public void setCmsOid(String value) {
		this.cmsOid = value;
	}

	/** 取得夫妻年收入 **/
	public BigDecimal getYFamAmt() {
		return this.yFamAmt;
	}
	/** 設定夫妻年收入 **/
	public void setYFamAmt(BigDecimal value) {
		this.yFamAmt = value;
	}

	/** 取得國別 **/
	public String getNtCode() {
		return this.ntCode;
	}
	/** 設定國別 **/
	public void setNtCode(String value) {
		this.ntCode = value;
	}

	/** 取得縣市代碼_鄉鎮市區_是否為都市計畫區 key值 **/
	public String getCityAreaUrbanPlanKey() {
		return this.cityAreaUrbanPlanKey;
	}
	/** 設定縣市代碼_鄉鎮市區_是否為都市計畫區 key值 **/
	public void setCityAreaUrbanPlanKey(String value) {
		this.cityAreaUrbanPlanKey = value;
	}

	/** 取得版本 **/
	public String getVersion() {
		return this.version;
	}
	/** 設定版本 **/
	public void setVersion(String value) {
		this.version = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	
	public BigDecimal getdRate() {
		return dRate;
	}
	public void setdRate(BigDecimal dRate) {
		this.dRate = dRate;
	}
	
	public BigDecimal getfRate() {
		return fRate;
	}
	public void setfRate(BigDecimal fRate) {
		this.fRate = fRate;
	}
	
	public BigDecimal getfDebtAssetRatio() {
		return fDebtAssetRatio;
	}
	public void setfDebtAssetRatio(BigDecimal fDebtAssetRatio) {
		this.fDebtAssetRatio = fDebtAssetRatio;
	}
	
	public BigDecimal getiDebtAssetRatio() {
		return iDebtAssetRatio;
	}
	public void setiDebtAssetRatio(BigDecimal iDebtAssetRatio) {
		this.iDebtAssetRatio = iDebtAssetRatio;
	}
	public BigDecimal getyFamAmt() {
		return yFamAmt;
	}
	public void setyFamAmt(BigDecimal yFamAmt) {
		this.yFamAmt = yFamAmt;
	}
	
	public String getQryMutualDebtB42() {
		return qryMutualDebtB42;
	}
	public void setQryMutualDebtB42(String qryMutualDebtB42) {
		this.qryMutualDebtB42 = qryMutualDebtB42;
	}
	
	public String getOver2DataPastY() {
		return over2DataPastY;
	}
	public void setOver2DataPastY(String over2DataPastY) {
		this.over2DataPastY = over2DataPastY;
	}
	
	public String getOver2DataPast3y() {
		return over2DataPast3y;
	}
	public void setOver2DataPast3y(String over2DataPast3y) {
		this.over2DataPast3y = over2DataPast3y;
	}
	
	public String getIsNotChkHouseIns() {
		return isNotChkHouseIns;
	}
	public void setIsNotChkHouseIns(String isNotChkHouseIns) {
		this.isNotChkHouseIns = isNotChkHouseIns;
	}
	
	public String getQryCollateralB42() {
		return qryCollateralB42;
	}
	public void setQryCollateralB42(String qryCollateralB42) {
		this.qryCollateralB42 = qryCollateralB42;
	}
	
	public BigDecimal getCheck2FinalRatio() {
		return check2FinalRatio;
	}
	public void setCheck2FinalRatio(BigDecimal check2FinalRatio) {
		this.check2FinalRatio = check2FinalRatio;
	}
}
