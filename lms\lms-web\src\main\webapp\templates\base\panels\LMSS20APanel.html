<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
   	<script type="text/javascript">
      	loadScript('pagejs/base/LMSS20APage');
    </script>
	
		<form id="LMS1205S20Form01">
				<div class="content">
					<fieldset>
						<legend>
							<b><th:block th:text="#{'L120S09a.blacklistQuery'}">洗錢防制查詢</th:block></b>
						</legend>
						<div class="funcContainer">
							
							<!--J-106-0238-001 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」-->
							<div class="oldAmlButtons">
								<button type="button" onClick="importBlackList()">
									<span class="text-only"><th:block th:text="#{'L120S09a.reApplyBlacklist'}">重新引進查詢名單</th:block></span>
								</button>
								<button type="button" onClick="queryBlackList()">
									<span class="text-only"><th:block th:text="#{'L120S09a.queryBlacklist'}">黑名單查詢</th:block></span>
								</button>	
							</div>
							<!--FOR SAS-->
							<div class="newAmlButtons">
								<button class="canSendSas" type="button" onClick="importBlackListNew() ">
									<span class="text-only"><th:block th:text="#{'L120S09a.reApplyBlacklist'}">重新引進查詢名單</th:block></span>
								</button>
								<button class="canSendSas" type="button" onClick="sendAmlList()">
									<span class="text-only"><th:block th:text="#{'L120S09a.sendAmlList'}">傳送名單掃描</th:block></span>
								</button>	

								<button  id="btnCheckAmlResult"  type="button" onClick="checkAmlResult()"> <!--class="notSendSas"-->
									<span class="text-only"><th:block th:text="#{'L120S09a.checkAmlResult'}">取得黑名單查詢結果</th:block></span>
								</button>
	
								<button class="show0024AmlStatus" type="button" onClick="applyCm1AmlStatus()">
									<span class="text-only"><th:block th:text="#{'L120S09a.apply'}">引進</th:block><th:block th:text="#{'L120S09a.cm1AmlStatus'}">0024註記</th:block></span>
							    </button>	
								
								<!--I-107-0260_05097_B1001 Web e-Loan企金授信系統增加提示訊息【該客戶屬於巴拿馬文件名單】-->
								<button class="show0024AmlStatus" type="button" onClick="applyCustPanaInfo()">
									<span class="text-only"><th:block th:text="#{'L120S09a.applyCustPanaInfo'}">查詢主借款人0024-23巴拿馬文件名單註記</th:block></span>
							    </button>	
								
	
								<!--總處管理專用-->
								<button class="showFor900" type="button" onClick="askAmlList()">
									<span class="text-only color-red" ><th:block th:text="#{'L120S09a.askAmlList'}">確認掃描狀態(管理用)</th:block></span>
								</button>	
								
								<!--總處管理專用-->
								<button class="showFor900" type="button" onClick="clearNcResult()">
									<span class="text-only color-red"><th:block th:text="#{'L120S09a.clearNcResult'}">清除案件調查結果(管理用)</th:block></span>
								</button>		
							</div>	
							<div>
								<!--J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級-->
								<button type="button" onClick="applyLuvRiskLevel()">
									<span class="text-only"><th:block th:text="#{'L120S09a.applyLuvRiskLevel'}">引進申貸戶風險等級</th:block></span>
							    </button>	
								<!--J-112-0534 配合金控證券暨期貨違約交割查詢下架，新增引入聯徵T70-->
								<button type="button" id="importT70Button" onClick="importT70()">
									<span class="text-only"><th:block th:text="#{'L120S09a.importT70Result'}">引進聯徵T70查詢結果</th:block></span>
							    </button>
								<!--J-113-0082 配合法務部新規，新增引入「受告誡處分」資訊-->
								<button type="button" id="importCmfwarnpButton" onClick="importCmfwarnpResult()">
									<span class="text-only"><th:block th:text="#{'L120S09a.importCmfwarnpResult'}">告誡戶掃描</th:block></span>
							    </button>
								
								
							</div>		
						</div>
						
						
						<table width="100%">
							<tr>
								<td width="30%"><span class="color-blue"><th:block th:text="#{'L120S09a.queryDateS'}">資料查詢日期</th:block>：</span>
									<span class="color-blue field" id="blackListQDate" name="blackListQDate"></span> <br>
									<div class="newAmlField">
										<span class="color-blue"><th:block th:text="#{'L120S09a.ncResult'}">案件調查結果</th:block>：</span>
										    <select id="ncResult" name="ncResult"  disabled="disabled"></select><br>  
										<span class="color-blue"><th:block th:text="#{'L120S09a.refNo'}">掃描編號</th:block>：</span><span class="color-blue field" id="refNo" name="refNo"></span>  <br>
										<span class="color-blue"><th:block th:text="#{'L120S09a.uniqueKey'}">掃描批號</th:block>：</span><span class="color-blue field" id="uniqueKey" name="uniqueKey"></span> <br>	
										<span class="color-blue"><th:block th:text="#{'L120S09a.ncCaseId'}">案例ID</th:block>：</span><span class="color-blue field" id="ncCaseId" name="ncCaseId"></span> <br>
										<div id="QueryInfo">
											<span class="color-blue"><th:block th:text="#{'L120S09a.caseBrId'}">案件調查分行</th:block>：</span><span class="color-blue field" id="sCaseBrId" name="sCaseBrId"></span><br>
											<span class="color-blue"><th:block th:text="#{'L120S09a.queryUser'}">案件調查人員</th:block>：</span><span class="color-blue field" id="sQueryUser" name="sQueryUser"></span><br>
										</div>
									</div>	
								</td>
								<td width="50%">
                                	<div id="ncResultRemarkDiv" class="hide">
										<span class="color-blue" style="vertical-align:top">
                                            <th:block th:text="#{'L120S09a.ncResultRemark'}">制裁/管制名單掃描調查結果說明</th:block>：
											<br/>
                                            <th:block th:text="#{'L120S09a.ncResultRemarkTip'}">(請簡要說明調查結果，可參照A2表或C表內容)</th:block>
                                        </span>
										<br/>
										<textarea name="ncResultRemark" id="ncResultRemark" style="width:300px;height:60px;" maxlength="300" maxlengthC="100"></textarea>
                                	</div>
									<div id="highRiskRemarkDiv" class="hide">
										<span class="color-blue" style="vertical-align:top">
                                            <th:block th:text="#{'L120S09a.highRiskRemark'}">高風險調查結果說明</th:block>：
											<br/>
                                            <th:block th:text="#{'L120S09a.highRiskRemarkTip'}">(請簡要說明調查結果，可參照F1表或B3表內容)</th:block>
                                        </span>
										<br/>
										<textarea name="highRiskRemark" id="highRiskRemark" style="width:300px;height:60px;" maxlength="300" maxlengthC="100"></textarea>
                                	</div>
								</td>
							</tr>
							<tr>
							   <td width="50%">
								   <div class="funcContainer">
										&nbsp;&nbsp;
										<button type="button" class="canSendSas" onClick="addData()">
											<span class="text-only"><th:block th:text="#{'L120S09a.addNew'}">新增查詢名單</th:block></span>
										</button>
										<button type="button" class="canSendSas" onClick="deleteList()">
											<span class="text-only"><th:block th:text="#{'L120S09a.delete'}">刪除名單</th:block></span>
										</button>	
										<!--J-106-0057-001 Web e-Loan授信管理系統新增「應收帳款承購無追索權-買方黑名單查詢」功能  -->
										<button type="button" class="canSendSas" onClick="importByExl()">
											<span class="text-only"><th:block th:text="#{'L120S09a.importByExl'}">匯入EXCEL</th:block></span>
										</button>		
										
										<span class="newAmlField">                       
											<a th:href="@{/img/lms/AML_Route_Rule_EL.htm}" target="_blank"><th:block th:text="#{'L120S09a.openRouteRule'}">開啟命中代碼說明</th:block></a>	
										</span>	
									</div>
								</td>
								<td width="50%" align="right"> 
									
								</td>
							</tr>
						</table>
	                   <div id="gridview_blackList" style="margin-left: 10px; margin-right: 10px"></div>
					   <span >
						　<th:block th:utext="#{'L120S09a.showMemo'}">查詢結果顯示說明：◎:未列於黑名單&nbsp;&nbsp; ★:是黑名單&nbsp;&nbsp;△:可能是黑名單&nbsp;&nbsp;╳:拒絕交易</th:block>&nbsp;&nbsp;
					　　</span>
					    <br>
					    <span >
						　<th:block th:utext="#{'L120S09a.showMemo1'}">風險等級說明：L:低&nbsp;&nbsp; M:中&nbsp;&nbsp;H:高&nbsp;&nbsp;</th:block>&nbsp;&nbsp;
					　　</span>
						<br>
						<span>
							&nbsp;&nbsp;<th:block th:text="#{'L120S09a.showMemo2'}">*掃描黑名單時點係包含1.徵信 2.簽案 3.動審等階段。</th:block>
						</span>
					</fieldset>
									
				</div>
			</form>

			<!--J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信-->	
			<form id="LMS1205S20Form02">
				<div class="content" id="amlStateCheckDiv">
					<fieldset>
						<legend>
							<b><th:block th:text="#{'L120S09a.amlStateCheckTable'}">疑似洗錢或資恐交易態樣檢核表</th:block></b>
						</legend>
						<div class="funcContainer">
							<button type="button" onClick="addStateCheckList()" id="l120s09cAddButton">
								<span class="text-only"><th:block th:text="#{'button.add'}">新增</th:block></span>
							</button>
							<button type="button" onClick="deleteStateCheckList()">
								<span class="text-only"><th:block th:text="#{'button.delete'}">刪除</th:block></span>
							</button>	
							<button type="button" onClick="printStateCheckList()">
								<span class="text-only"><th:block th:text="#{'button.print'}">列印</th:block></span>
							</button>	
							<br/>
							<span id="l120s09c_11311_text" class="color-red">配合洗防處2024.12.06簽核郵件通知，依113.11.25兆銀洗防字第1130050016號函已修訂「兆豐商銀疑似洗錢、資恐及異常交易態樣檢核表-授信」113.11版，e-loan系統須更新格式版本，更新期間請暫時使用紙本態樣檢核表簽核。</span>
						</div>
	                   <div id="gridview_amlStateCheckList" style="margin-left: 10px; margin-right: 10px"></div>
					</fieldset>
									
				</div>
			</form>
			
	        <!-- pop up screen -->
			<div id="blackListDetail" style="display: none;">
			<form id="tLMS1205S20Form01">
				<!--<fieldset style="width:900px;">-->
				<fieldset>
					<legend>
						<b><th:block th:text="#{'L120S09a.inputBlacklist'}">登錄洗錢防制名單</th:block></b>
					</legend>
					<p />
					<b><th:block th:text="#{'L120S09a.createBY'}">文件產生方式</th:block>：</b><span class="field" id="createBY2" name="createBY2" ></span>
					<span class="field color-blue max" id="createBY" name="createBY" maxlength="3" style="display:none;"></span>
					<p />
					<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
						<tbody>								
							<tr>
								<!--J-106-0057-001 Web e-Loan授信管理系統新增「應收帳款承購無追索權-買方黑名單查詢」功能  -->
								<td width="20%" class="hd1"><th:block th:text="#{'L120S09a.custRelation'}">與本案關係</th:block>&nbsp;&nbsp;</td>
								<td width="30%" >
									<input type="checkbox" name="custRelation" id="custRelation" />
								</td>
								<!--
								<td width="15%">
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="1" />
									<th:block th:text="#{'L120S09a.checkbox1'}">借戶</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="3" />
									<th:block th:text="#{'L120S09a.checkbox3'}">借戶負責人</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="5" />
									<th:block th:text="#{'L120S09a.checkbox5'}">擔保品提供人</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="7" />
									<th:block th:text="#{'L120S09a.checkbox7'}">實質受益人</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="9" />
									<th:block th:text="#{'L120S09a.checkbox8'}">一般保證人</th:block></label><br />
								</td>
								<td width="15%">
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="2" />
									<th:block th:text="#{'L120S09a.checkbox2'}">共同借款人</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="4" />
									<th:block th:text="#{'L120S09a.checkbox4'}">連保人</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="6" />
									<th:block th:text="#{'L120S09a.checkbox6'}">關係企業</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="8" />
									<th:block th:text="#{'L120S09a.checkbox8'}">一般保證人</th:block></label><br />
									<br />
								</td>
								-->
							</tr>
						</tbody>
					</table>
					<br />
					<div class="funcContainer"></div>
					<table class="tb2" width="100%" border="0" cellspacing="0"
						cellpadding="0">
						<tbody>
							<tr>
								<td class="hd2" colspan="4" ><th:block th:text="#{'L120S09a.blacklistContent'}">名單內容</th:block></td>	
							</tr>
							<tr>
								<td class="hd1" width="20%" ><th:block th:text="#{'L120S09a.custId'}">本案關係人統編</th:block>&nbsp;&nbsp;</td>
								<td width="30%">
									<input type="text" id="custId" name="custId" class="upText"  maxlength="10"/>
								</td>
								<td class="hd1" width="20%"    ><th:block th:text="#{'L120S09a.dupNo'}">重覆序號</th:block>&nbsp;&nbsp;</td>
								<td width="30%">
									<input type="text" id="dupNo" name="dupNo" maxlength="1" size="5"/>
								</td> 
							</tr>
							<tr>
								<td class="hd1" width="20%"  >
									<th:block th:text="#{'L120S09a.custName'}">戶名</th:block>&nbsp;&nbsp;
								    <br>
									<button type="button" onClick="applyCustName()">
										<span class="text-only"><th:block th:text="#{'L120S09a.apply'}">引進</th:block></span>
									</button>
								</td>
								<td width="30%" colspan="3">
									<!--halfword-->
									<input type="text" id="custName" name="custName"  class="required halfText" size="100" maxlength="120" maxlengthC="38" />
								</td>
								
							</tr>	
							<tr>
								
								<td class="hd1" width="20%"  ><th:block th:text="#{'L120S09a.custEName'}">英文戶名</th:block>&nbsp;&nbsp;</td>
								<td width="30%" colspan="3">
									<input type="text" id="custEName" name="custEName" size="100" maxlength="76" maxlengthC="38" class="halfText halfword"/>
								</td> 
							</tr>		
							<!--J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能-->
							<tr>
								
								<td class="hd1" width="20%"  ><th:block th:text="#{'L120S09a.country'}">國別</th:block>&nbsp;&nbsp;</td>
								<td width="30%" colspan="3">
									<select id="country" name="country" combokey="CountryCode" combotype="4" space="true"></select>
								</td> 
							</tr>		
							<tr>
								<td class="hd1" width="20%"  ><th:block th:text="#{'L120S09a.queryDateS'}">查詢日</th:block>&nbsp;&nbsp;</td>
								<td width="30%">
									<span id="queryDateS" class="field"></span>
								</td>
								<td class="hd1" width="20%"  ><th:block th:text="#{'L120S09a.blackListCode'}">查詢結果</th:block>&nbsp;&nbsp;</td>
								<td width="30%">
									<select id="blackListCode" name="blackListCode"  disabled="disabled"></select>
								</td> 
							</tr>		
							<tr>
								<td class="hd1" width="20%"  ><th:block th:text="#{'L120S09a.memo'}">命中代碼</th:block>&nbsp;&nbsp;</td>
								<td width="30%">
									<span id="memo" class="field"></span>
								</td>
								<td class="hd1" width="20%"  ><th:block th:text="#{'L120S09a.cm1AmlStatus'}">0024註記</th:block>&nbsp;&nbsp;</td>
								<td width="30%">
									<span id="cm1AmlStatus" class="field"></span>
								</td> 
							</tr>		
							<tr>
								<td class="hd1" width="20%"  ><th:block th:text="#{'L120S09a.luvRiskLevel'}">風險等級</th:block>&nbsp;&nbsp;</td>
								<td width="30%">
									<span id="luvRiskLevel" class="field"></span>
								</td>
								<td class="hd1" width="20%"  ><th:block th:text="#{'L120S09a.cmfwarnpResult'}">受告誡處分</th:block>&nbsp;&nbsp;</td>
								<td width="30%">
									<span id="cmfwarnpResultDesc" class="field"></span>
								</td> 
							</tr>						
						</tbody>
					</table>
				</fieldset>
				<div id="t70Detail">
				<fieldset>
					<legend>
						<b><th:block th:text="#{'L120S09a.T70Title'}">聯徵T70證券商授信業務負面信用資料</th:block></b>
					</legend>
					<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td class="hd1" width="20%"><th:block th:text="#{'L120S09a.T70Date'}">資料回覆日期</th:block></td>
							<td width="30%"><!--<input type="text" name="T70Date" id="T70Date" readonly="true"/>-->
							<span id="T70Date" name="T70Date" class="field"></span>
							</td>
							<td class="hd1" width="20%"><th:block th:text="#{'L120S09a.T70HtmlDoc'}">報表檔案</th:block></td>
							<td width="30%">
								<input type="hidden" id="T70Docfileoid"  name="T70Docfileoid" />
								<button type="button" id="printT70Button" onClick="printT70()">
									<span class="text-only">列印T70</span>
							    </button>
							</td>
							
						</tr>
						<tr>
							<td class="hd1"><th:block th:text="#{'L120S09a.T70Status'}">查詢狀態</th:block></td>
							<td colspan="3"><select id="T70Status" name="T70Status" comboKey="T70_STATUS" comboType="2" space="true"></select></td>
						</tr>
						<tr>
							<td class="hd1"><th:block th:text="#{'L120S09a.T70NegFlag'}">負面紀錄</th:block></td>
							<td><!--<input type="text" id="T70NegFlag" name="T70NegFlag" readonly="true"/> -->
								<span id="T70NegFlag" name="T70NegFlag" class="field"></span>
							</td>
							<td class="hd1"><th:block th:text="#{'L120S09a.T70Amt'}">未清償總餘額</th:block></td>
							<td><!--<input type="text" name="T70Amt" id="T70Amt" class="numeric" integer="10" fraction="0" readonly="true"/> -->
								<span id="T70Amt" name="T70Amt" class="numeric" integer="10" fraction="0"></span>
								</td>
						</tr>
					</table>
					<fieldset>
						<legend><th:block th:text="#{'L120S09a.T70NegFlag.desc'}">負面紀錄說明:</th:block></legend>
						<th:block th:text="#{'L120S09a.T70NegFlag.N0'}">
							N0：受查戶於揭露期限內無授信負面紀錄，且查詢日期於證券商聯合徵信系統與授信機構無授信往來資訊，即無授信額度資料；
						</th:block><br/>
						<th:block th:text="#{'L120S09a.T70NegFlag.N1'}">
							N1：受查戶於揭露期限內無授信負面紀錄，且查詢日期於證券商聯合徵信系統與授信機構有授信往來資訊，即有授信額度資料；
						</th:block><br/>
						<th:block th:text="#{'L120S09a.T70NegFlag.Y0'}">
							Y0：受查戶於揭露期限內有授信負面紀錄(違約)，惟經證券商通報結案；
						</th:block><br/>
						<th:block th:text="#{'L120S09a.T70NegFlag.Y1'}">
							Y1：受查戶於揭露期限內有授信負面紀錄(違約)，尚未經證券商通報結案。
						</th:block>
					</fieldset>
					<fieldset>
						<legend><th:block th:text="#{'L120S09a.T70Amt.desc'}">未清償總餘額說明:</th:block></legend>
						<th:block th:text="#{'L120S09a.T70Amt.desc1'}">
							證券授信業務原始未清償總餘額，揭露金額為「信用交易(不含融券)」、「證券業務借貸款項」及「不限用途款項借貸」
							三種業務之負面紀錄原始未清償餘額合計數。(若授信負面信用資料註記為N0、N1、Y0，該未清償總餘額皆為0)
						</th:block>
					</fieldset>
				</fieldset>
				</div>
				<input type="hidden" id="seqNum" name="seqNum" />
			</form>
			
		</div>
		<!--J-106-0057-001 Web e-Loan授信管理系統新增「應收帳款承購無追索權-買方黑名單查詢」功能 -->
		<div id="loginImportByExl" style="display:none; margin-top:5px;">
			<table border="1" cellpadding="0" cellspacing="0">
				<tr>
					<td width="20%" class="hd1"><th:block th:text="#{'L120S09a.custRelation'}">與本案關係</th:block>&nbsp;&nbsp;</td>
					<td width="30%" >
						<input type="checkbox" name="custRelationExl" id="custRelationExl" class="required"/>
					</td>
				</tr>
				<tr>
					<td>
						<input type="file" id="uploadFileAml" name="uploadFileAml" class="required"/>
					</td>	
					<td>
						※點選右方連結下載EXCEL範本檔:<a th:href="@{/img/lms/AML.xls}" target="_blank"><th:block th:text="#{'L120S09a.excelDownload'}">下載</th:block></a>		
						<br>
						EXCEL格式說明:<br>
						1.欄位依序如下:<br>
						&nbsp;&nbsp;&nbsp;&nbsp;A.本案關係人統編 (最長10碼)<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;B.重覆序號(最長1碼)<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;C.本案關係人戶名<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;D.本案關係人英文戶名<br>
						&nbsp;&nbsp;&nbsp;&nbsp;E.國別(2碼註冊地國別，例如:US、JP...等)<br>
						2.若本案關係僅勾選為負責人或實際受益人時，則EXCEL內「本案關係人統編」欄位可不輸入。<br>
						3.若本案關係有勾選非負責人或實際受益人時，則EXCEL內「本案關係人統編」 與「重覆序號」為必要輸入欄位，匯入時，系統會依統編查詢0024最新戶名。<br>
						4.若本案關係有勾選申貸戶、共同借款人、連帶保證人、一般保證人或擔保品提供人時，則EXCEL內「國別」為必要輸入欄位。<br>
						5.EXCEL檔案副檔名必須為.xls。<br>
						6.匯入EXCEL檔案時自第二列開始匯入，第一列預設為標題。<br>
						7.匯入之EXCEL系統不留存，請自行保留。<br>
					</td>
				</tr>	
			</table>	
	    </div>
		<!--J-107-0176 配合企金處針對AML/CFT查詢作業，修改授信管理系統之AML/CFT查詢作業功能 -->
		<div id="amlQueryRole" style="display:none">
			<form id="roleForm">
				<table border="0" cellpadding="0" cellspacing="0" class="tb4" width="100%">
					<tr>
						<td class="hd1" style="width:40%"><th:block th:text="#{'L120S09a.caseBrId'}">案件調查分行</th:block></td>
						<td style="width:60%"><select id="caseBrId" name="caseBrId" onChange="changeItem(this);" space="true"></select></td>
					</tr>
					<tr>
						<td class="hd1" style="width:40%"><th:block th:text="#{'L120S09a.queryUser'}">案件調查人員</th:block></td>
						<td style="width:60%"><select id="queryUser" name="queryUser" space="true"></select></td>
					</tr>
				</table>
			</form>
		</div>
		<!--J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信  這個是選人的grid-->
		<div id="amlCheckStateChoose" style="display:none">
			<!-- 舊版的按鈕全部從HTML裡拔掉，因為不會有回去新增舊版的需求 -->
			<!-- 這功能舊純粹讓他們填表格印出用而已，也不需要被檢核，所以刪掉舊版讓程式簡潔點 -->
			<label id="l120s09c_11311_label"><input id="state_versionDate" name="state_versionDate" type="radio" value="11311" checked="checked"/>
				<th:block th:text="#{'L120S09a.stateVersionDate11311'}">11311版<s></label>
			<br/>
			<br/>
			<div id="amlCheckL120s01agrid" width="100%"
				style="margin-left: 10px; margin-right: 10px">
			</div>
		</div>
		<!--疑似洗錢或資恐交易態樣檢核表-"檢核表本人"有版本控制的機制-->
		<div id="amlCheckStateDetail" style="display: none;">
		</div>
	</th:block>
</body>
</html>
