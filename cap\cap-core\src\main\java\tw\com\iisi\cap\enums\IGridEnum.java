/*
 * IGridEnum.java
 *
 * Copyright (c) 2009-2012 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
*/
package tw.com.iisi.cap.enums;

/**
 * <p>
 * 資料顯示
 * </p>
 *
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/16,iristu,new
 *          <li>2012/2/23,iristu,修改PAGE("gridPage")參數以避免與url中的page衝突
 *          </ul>
 */
public enum IGridEnum {

    /**
     * gridPage
     */
    PAGE("gridPage"),

    /**
     * rows
     */
    PAGEROWS("rows"),

    /**
     * total
     */
    TOTAL("total"),

    /**
     * records
     */
    RECORDS("records"),

    /**
     * sord
     */
    SORTTYPE("sord"),

    /**
     * asc
     */
    SORTASC("asc"),

    /**
     * desc
     */
    SORTDESC("desc"),

    /**
     * sidx
     */
    SORTCOLUMN("sidx"),

    /**
     * cell
     */
    CELL("cell"),

    /**
     * name
     */
    COL_NAME("name"),

    /**
     * index
     */
    COL_INDEX("index"),

    /**
     * _columnParam
     */
    COL_PARAM("_columnParam");

    /**
     * 列舉內容
     */
    private String code;

    /**
     * 設置列舉內容
     */
    IGridEnum(String code) {
        this.code = code;
    }

    /**
     * 取得列舉內容
     */
    public String getCode() {
        return code;
    }

}
