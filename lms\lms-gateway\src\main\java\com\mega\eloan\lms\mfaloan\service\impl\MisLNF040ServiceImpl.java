package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF040Service;

@Service
public class MisLNF040ServiceImpl extends AbstractMFAloanJdbc implements
		MisLNF040Service {

	@Override
	public Map<String, String> getDpCode() {
		Map<String, String> dpCode = new HashMap<String, String>();
		List<Map<String, Object>> Lnf040List = getJdbc().queryForList(
				"LNF040.GetAll", new String[] {});

		// 放款科目的對照
		for (Map<String, Object> lnf040Map : Lnf040List) {
			dpCode.put((String) lnf040Map.get("LNF040_LNAP_CODE"),
					(String) lnf040Map.get("LNF040_ACT_NAME_S"));
		}

		return dpCode;
	}
	

	@Override
	public List<Map<String, Object>> selAll(){
		return getJdbc().queryForListWithMax(
				"LNLNF040.selAll", new String[] {});
	}

}
