package com.mega.eloan.lms.dao.impl;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C900S02EDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900S02E;

/** 同一通訊指標控制檔  **/
@Repository
public class C900S02EDaoImpl extends LMSJpaDao<C900S02E, String>
	implements C900S02EDao {

	@Override
	public C900S02E findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public C900S02E findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}
	
	@Override
	public int countByCYC_MN(String CYC_MN){
		Query query = getEntityManager().createNamedQuery("C900S02E.countByCYC_MN");
		// 設置參數
		query.setParameter(1, CYC_MN);
		Integer count = (Integer) query.getSingleResult();
		return count.intValue();
	}
	
	@Override
	public int countByCYC_MN_no_chk_result(String CYC_MN){
		Query query = getEntityManager().createNamedQuery("C900S02E.countByCYC_MN_no_chk_result");
		// 設置參數
		query.setParameter(1, CYC_MN);
		Integer count = (Integer) query.getSingleResult();
		return count.intValue();
	}
	
	@Override
	public int setDeletedtimeByCYC_MN_no_chk_result(String CYC_MN){
		Query query = entityManager.createNamedQuery("C900S02E.setDeletedtimeByCYC_MN_no_chk_result");
		query.setParameter(1, CYC_MN);
		return query.executeUpdate();
	}
}