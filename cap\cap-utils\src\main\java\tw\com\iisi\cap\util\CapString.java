/*
 * CapString.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,
		Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.util;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.CharacterCodingException;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.CharsetEncoder;
import java.nio.charset.CodingErrorAction;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tw.com.iisi.cap.constant.CapConstants;

/**
 * 
 * <p>
 * 字串處理.
 * </p>
 * 
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/6/1,iristu,新增trimLineSeparator(),處理字串中斷行符號.
 *          <li>2011/9/7,SunkistWang,update checkRegularMatch(), 避開NPE.
 *          <li>2011/11/25,SunkistWang,add toHalfString(String), 全形英數字轉換成半形英數字.
 *          <li>2012/7/10,iristu,#49修改trimFullSpace方式
 *          <li>2013/6/20,EL07623,增加isDecimal判斷字串是否為整數或小數
 *          </ul>
 */
public class CapString {
    private static Logger logger = LoggerFactory.getLogger(CapString.class);

    /**
     * 十六進位字串格式
     */
    public final static char[] hexChar = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };

    /**
     * 將byte陣列轉為十六進位字串 如{0x01, 0x02} => 0102 (Move from tw.com.iisi.ettk.api.API.java)
     * 
     * @param ba
     *            傳入byte陣列
     * @return 轉換後的字串陣列
     */
    public static String byteArrayToHexString(byte[] ba) {
        if (ba == null) {
            return "";
        }
        char[] ca = new char[2 * ba.length];
        for (int i = 0; i < ca.length; i += 2) {
            byte b = ba[i / 2];
            ca[i] = hexChar[(b >> 4) & 0xf];
            ca[i + 1] = hexChar[b & 0xf];
        }
        return new String(ca);
    }

    /**
     * hex string to byte array ex. 6364 -> [c][d]
     * 
     * @param s
     *            input string
     * @return output byte array
     */
    public static byte[] hexStrToByteArray(String s) {
        byte[] ba = new byte[s.length() / 2];
        for (int i = 0; i < s.length(); i += 2) {
            String ch = s.substring(i, i + 2);
            ba[i / 2] = (byte) Integer.parseInt(ch, 16);
        }
        return ba;
    }

    /**
     * 在字串前面補空白.
     * 
     * @param in
     *            the input string
     * @param length
     *            the output string length
     * @return the output string
     */
    public static String fillBlankHead(String in, int length) {
        return fillString(in, length, true, ' ');
    }

    /**
     * 在字串前面補0.
     * 
     * @param in
     *            the input
     * @param length
     *            補滿的字串長度
     * @return String
     */
    public static String fillZeroHead(String in, int length) {
        return fillString(in, length, true, '0');
    }

    /**
     * fill space character to tail of string to length.
     * 
     * @param in
     *            the input string
     * @param length
     *            the output string length
     * @return the output string
     */
    public static String fillBlankTail(String in, int length) {
        return fillString(in, length, false, ' ');
    }

    /**
     * 在字串後面補0
     * 
     * @param in
     *            the input
     * @param length
     *            補滿的字串長度
     * @return String
     */
    public static String fillZeroTail(String in, int length) {
        return fillString(in, length, false, '0');
    }

    /**
     * fill specify character to string. 對字串補特殊字元.
     * 
     * @param in
     *            the input string. 輸入字串.
     * @param length
     *            the output string length. 長度.
     * @param rightAlign
     *            true, the source string is right alignment, otherwise the output string is left alignment. 是否右靠.
     * @param ch
     *            the append character. 填滿的字元.
     * @return the output string. 輸出加長後字串.
     */
    public static String fillString(String in, int length, boolean rightAlign, char ch) {

        StringBuffer sb = new StringBuffer();
        if (in == null) {
            in = ""; //$NON-NLS-1$
        }
        int inLength = in.getBytes().length;
        if (inLength >= length) {
            return in;
        } else {
            int loopLength = ch < 256 ? length - inLength : (length - inLength) / 2;
            for (int i = 0; i < loopLength; i++) {
                sb.append(ch);
            }
            if (rightAlign) {
                sb.append(in);
            } else {
                sb.insert(0, in);
            }
        }
        return sb.toString();
    }// ;

    /**
     * fill specify character to string. 對字串補特殊字元.
     * 
     * @param rStr
     *            the input string. 輸入字串.
     * @param length
     *            the output string length. 長度.
     * @param rightAlign
     *            true, the source string is right alignment, otherwise the output string is left alignment. 是否右靠.
     * @param ch
     *            the append character. 填滿的字元.
     * @param charset
     *            the input string charset.
     * @return the output string. 輸出加長後字串.
     */
    public static String fillString(String rStr, int length, boolean rightAlign, String ch, String charset) {
        if (rStr == null) {
            rStr = CapConstants.EMPTY_STRING;
        }
        try {
            StringBuffer st = new StringBuffer(rStr);
            while (st.toString().getBytes(charset).length < length) {
                if (rightAlign) {
                    st.insert(0, ch);
                } else {
                    st.append(ch);
                }
            }
            rStr = st.toString();
            if (rStr.getBytes(charset).length > length) {
                if (rightAlign) {
                    int index = rStr.getBytes(charset).length - length;
                    rStr = new String(rStr.getBytes(charset), index, length, charset);
                } else {
                    rStr = new String(rStr.getBytes(charset), 0, length, charset);
                }

            }
        } catch (UnsupportedEncodingException e) {
            logger.error(e.getMessage(), e);
        }
        return rStr;
    }

    /**
     * trimNull 當傳入值為Null時，則回傳空字串。不為Null時，則回傳trim過的String
     * 
     * @param o
     *            the input
     * @return string
     */
    public static String trimNull(Object o) {
        if (o != null && !"".equals(o)) {
            return (o.toString()).trim();
        } else {
            return CapConstants.EMPTY_STRING;
        }
    }// ;

    /**
     * 全形空白轉char Array
     */
    static final char[] fullSpace = "　".toCharArray();

    /**
     * 半形空白轉char Array
     */
    static char[] space = " ".toCharArray();

    /**
     * <pre>
     * trim全形空白
     * </pre>
     * 
     * @param in
     *            the input
     * @return String
     */
    public static String trimFullSpace(String in) {
        if (isEmpty(in)) {
            return "";
        }
        return in.replaceAll("^[　 ]+", "").replaceAll("[　 ]+$", "");
    }// ;

    /**
     * 判斷字串是否為空白.
     * 
     * @param s
     *            字串
     * @return boolean
     */
    public static boolean isEmpty(String s) {
        return s == null || s.trim().length() == 0;
    }

    /**
     * 
     * 判斷字串是否與正規表示式相同
     * 
     * @param str
     *            字串
     * @param regEx
     *            正規表示式
     * @return boolean
     */
    public static boolean checkRegularMatch(String str, String regEx) {
        if (CapString.isEmpty(str)) {
            return false;
        }
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.find();
    }

    /**
     * <pre>
     * 最得第一個符合條件之字串
     * </pre>
     * 
     * @param str
     *            the input
     * @param regEx
     *            正規式表示式
     * @return String
     * 
     */
    public static String getRegularMatch(String str, String regEx) {
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return (m.find()) ? m.group() : CapConstants.EMPTY_STRING;
    }// ;

    /**
     * 
     * 判斷傳入之字串是否為數字
     * 
     * @param str
     *            input string
     * @return boolean
     */
    public static boolean isNumeric(String str) {
        return str.matches("^[0-9]+$");
    }

    /**
     * 
     * 判斷傳入之字串是否為整數或小數
     * 
     * @param str
     *            input string
     * @return boolean
     */
    public static boolean isDecimal(String str) {
        return str.matches("^-?[0-9]+(\\.[0-9]+)?$");
    }

    /**
     * 字符轉換
     */
    private static Map<Character, Character> helfConvertMap;
    static {
        helfConvertMap = new HashMap<Character, Character>();
        helfConvertMap.put(' ', '\u3000');
        helfConvertMap.put('"', '”');
        helfConvertMap.put('\'', '’');
        helfConvertMap.put('.', '。');
        helfConvertMap.put('[', '〔');
        helfConvertMap.put(']', '〕');
        helfConvertMap.put('^', '︿');
        helfConvertMap.put('_', '﹍');
        helfConvertMap.put('`', '‵');
    }

    /**
     * 轉全型
     * 
     * @param str
     *            the input string
     * @return 全型String
     */
    public static String halfWidthToFullWidth(String str) {
        char[] chs = str.toCharArray();
        char[] rtn = new char[chs.length];
        for (int i = 0; i < chs.length; i++) {
            char ch = chs[i];
            if (!(ch >= '\u2000') && (ch <= '\ufffd')) {
                if (helfConvertMap.containsKey(ch)) {
                    rtn[i] = helfConvertMap.get(ch);
                }
                if ((ch > '\u0020') && (ch <= '\u007f')) {
                    rtn[i] = (char) (ch - '\u0020' + 0xff00);
                }
            } else {
                rtn[i] = ch;
            }
        }
        return new String(rtn);
    }// ;

    /**
     * trim Left
     * 
     * @param source
     * @return String
     */
    public static String trimLeft(String source) {
        if (isEmpty(source)) {
            return source;
        }
        if (source.trim().length() == 0)
            return "";
        int index = 0;
        for (int i = 0; i < source.length(); i++) {
            if (Character.isWhitespace(source.charAt(i))) {
                index = i + 1;
            } else {
                break;
            }
        }
        return index != 0 ? source.substring(index) : source;
    }// ;

    /**
     * 將字串中有斷行的符號去除
     * 
     * @param source
     *            String
     * @return String
     */
    public static String trimLineSeparator(String source) {
        if (isEmpty(source)) {
            return source;
        }
        source = source.replaceAll("\r\n", "");
        source = source.replaceAll("\n", "");
        return source;
    }

    /**
     * 將陣列展開為字串, 若傳入值為null則返回空字串
     * 
     * @param objects
     */
    public static String flattenArrayAsString(Object[] objects) {
        if (objects != null) {
            StringBuilder builder = new StringBuilder("");
            for (Object o : objects) {
                if (o != null) {
                    builder.append(o.toString());
                } else {
                    builder.append("null");
                }
                builder.append(",");
            }
            return builder.toString();
        }
        return "";
    }

    /**
     * 取得 UUID 字串
     * 
     * @return {@code UUID.randomUUID().toString().replaceAll("-", "")}
     */
    public static String getUUIDString() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * 截取所需長度字串
     * 
     * @param in
     *            字串
     * @param encoding
     *            編碥
     * @param len
     *            所需長度
     * @return String
     */
    public static String cutString(String in, String encoding, int len) {
	    if (in == null) {
		    return null;
	    }
	    Charset charset = Charset.forName(encoding);
	    CharsetDecoder decoder = charset.newDecoder();
	    byte[] sba = in.getBytes(charset);
	    if (sba.length <= len) {
		    return in;
	    }
	    // Ensure truncation by having byte buffer = maxBytes
	    ByteBuffer bb = ByteBuffer.wrap(sba, 0, len);
	    CharBuffer cb = CharBuffer.allocate(len);
	    // Ignore an incomplete character
	    decoder.onMalformedInput(CodingErrorAction.IGNORE);
	    decoder.decode(bb, cb, true);
	    decoder.flush(cb);

        String res = new String(cb.array(), 0, cb.position());

        // 為避免 cp937 這類, 在 new String 時自動補上 0x0f, 這裡做個檢查, 必要時切掉最後一個字
        if (res.length() > 0 && res.getBytes(charset).length > len)
            return res.substring(0, res.length() - 1);
        return res;
    }

    /**
     * trimNull 當傳入值為Null時，則回傳為指定name字串。不為Null時，則回傳trim 過的String
     * 
     * @param o
     *            the input
     * @param name
     *            the string
     * @return string
     */
    public static String trimNull(Object o, String name) {
        if (o != null && !"".equals(o)) {
            return (o.toString()).trim();
        } else {
            return name;
        }
    }// ;

    /**
     * Array transform String
     * 
     * @param ary
     *            String[]
     * @return String
     */
    public static String array2String(String[] ary) {
        StringBuffer sb = new StringBuffer();
        for (String s : ary) {
            sb.append(s).append(",");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }// ;

    /**
     * 全型轉半型，可包含中文
     * 
     * @param str
     *            全型英數字
     * @return 半型英數字
     */
    public static String toHalfString(String str) {
        if (null == str) {
            return null;
        }
        char[] chars = str.toCharArray();
        StringBuffer sb = new StringBuffer();

        for (int i = 0; i < chars.length; i++) {
            sb.append(charFulltoSemi(chars[i]));
        }
        return sb.toString();
    }

    /**
     * 對單一字元進行全形轉半形.
     * 
     * @param ch
     *            char
     * @return char
     */
    private static char charFulltoSemi(char ch) {
        char chSemi = '0';

        int iChar = (int) ch;

        if ((iChar >= 65296 && iChar <= 65305)) {
            // 數字(Number)
            iChar = iChar - 65296 + 48;
            String after = String.valueOf(Integer.toHexString(iChar));
            chSemi = (char) Integer.parseInt(after, 16);

        } else if ((iChar >= 65345 && iChar <= 65370)) {
            // 小寫英文字母(Alphabet)
            iChar = iChar - 65345 + 97;
            String after = String.valueOf(Integer.toHexString(iChar));
            chSemi = (char) Integer.parseInt(after, 16);
        } else if ((iChar >= 65313 && iChar <= 65338)) {
            // 大寫英文字母(Alphabet)
            iChar = iChar - 65313 + 65;
            String after = String.valueOf(Integer.toHexString(iChar));
            chSemi = (char) Integer.parseInt(after, 16);
        } else {
            // 特殊符號
            switch (iChar) {
            case 65281:
                chSemi = (char) 33;
                break;
            case 8221: // 22 " A1A8 ” 201D
                chSemi = (char) 34;
                break;
            case 65283: // 23 # A1AD ＃ FF03
                chSemi = (char) 35;
                break;
            case 65284: // 24 $ A243 ＄ FF04
                chSemi = (char) 36;
                break;
            case 65285: // 25 % A248 ％ FF05
                chSemi = (char) 37;
                break;
            case 65286: // 26 & A1AE ＆ FF06
                chSemi = (char) 38;
                break;
            case 8217: // 27 ' A1A6 ’ 2019
                chSemi = (char) 39;
                break;
            case 65288: // 28 ( A15D （ FF08
                chSemi = (char) 40;
                break;
            case 65289: // 29 ) A15E ） FF09
                chSemi = (char) 41;
                break;
            case 65290: // 2A * A1AF ＊ FF0A
                chSemi = (char) 42;
                break;
            case 65291: // 2B + A1CF ＋ FF0B
                chSemi = (char) 43;
                break;
            case 65292: // 2C , A141 ， FF0C
                chSemi = (char) 44;
                break;
            case 65293: // 2D - A1D0 － FF0D
            case 8212:
                chSemi = (char) 45;
                break;
            case 65294: // 2E . A144 ．FF0E
                chSemi = (char) 46;
                break;
            case 65295: // 2F / A1FE ∕ FF0F
                chSemi = (char) 47;
                break;
            case 65306: // 3A : A147 ： FF1A
                chSemi = (char) 58;
                break;
            case 65307: // 3B ; A146 ； FF1B
                chSemi = (char) 59;
                break;
            case 65308: // 3C < A1D5 ＜ FF1C
                chSemi = (char) 60;
                break;
            case 65309: // 3D = A1D7 ＝ FF1D
                chSemi = (char) 61;
                break;
            case 65310: // 3E > A1D6 ＞ FF1E
                chSemi = (char) 62;
                break;
            case 65311: // 3F ? A148 ？ FF1F
                chSemi = (char) 63;
                break;
            case 65312: // 40 @ A249 ＠ FF20
                chSemi = (char) 64;
                break;
            case 65340: // 5C \ A240 ﹨ FF3C
                chSemi = (char) 92;
                break;
            case 65343: // 5F _ A1C4 ＿ FF3F
                chSemi = (char) 95;
                break;
            case 65371: // 7B { A1A1 ﹛ FF5B
                chSemi = (char) 123;
                break;
            case 65372: // 7C | A155 ｜ FF5C
                chSemi = (char) 124;
                break;
            case 65373: // 7D } A1A2 ﹜ FF5D
                chSemi = (char) 125;
                break;
            case 65374: // 7E ~ A1E3 ? FF5E
                chSemi = (char) 126;
                break;
            case 12288: // 全型空白轉半型
                chSemi = (char) 32;
                break;
            default:
                chSemi = ch;
            }
        }

        return chSemi;
    }
    /**
   	 * 
   	 * 判斷傳入之字串是否為全型數字
   	 * 
   	 * @param str
   	 *            input string
   	 * @return boolean
   	 */
   	public static boolean isNumericForFullWidth(String str) {
   		char[] chs = str.toCharArray();
   		for (int i = 0; i < chs.length; i++) {
   			char ch = chs[i];
   			if (ch < 65296 || ch > 65305) {
   				return false;
   			}
   		}
   		return true;
   	}
   	
   	private static Map<Character, Character> fullConvertMap;
   	static {
   		fullConvertMap = new HashMap<Character, Character>();
   		fullConvertMap.put('\u3000', ' ');
   		fullConvertMap.put('”', '"');
   		fullConvertMap.put('’', '\'');
   		fullConvertMap.put('。', '.');
   		fullConvertMap.put('〔', '[');
   		fullConvertMap.put('〕', ']');
   		fullConvertMap.put('︿', '^');
   		fullConvertMap.put('﹍', '_');
   		fullConvertMap.put('‵', '`');
   	}

   /**
   	 * 轉半型中文用 '；'不轉 '：'不轉 '，'為65292 ,前一位及後一位為數字才轉
   	 * '．'為65294,前一位為數字才轉,否則轉為'。'12290
   	 * 
   	 * @param str
   	 *            the input string
   	 * @return 半型String
   	 */
   	public static String fullWidthToHalfWidthForChinese(String str) {
   		char[] chs = str.toCharArray();
   		char[] rtn = new char[chs.length];
   		int j = 0;
   		for (int i = 0; i < chs.length; i++) {
   			char ch = chs[i];

   			if ((ch != 12290 && fullConvertMap.containsKey(ch))
   					|| (ch == 12290 && (i > 0 && isNumericForFullWidth(String
   							.valueOf(chs[i - 1]))))) {
   				// '。'為12290,前一位為數字才轉
   				rtn[j++] = fullConvertMap.get(ch);

   			} else if ((ch == 65292 && (i > 0
   					&& isNumericForFullWidth(String.valueOf(chs[i - 1])) && (i != chs.length - 1 && isNumericForFullWidth(String
   					.valueOf(chs[i + 1])))))
   					|| (ch == 65294 && i > 0 && isNumericForFullWidth(String
   							.valueOf(chs[i - 1])))
   					|| (ch >= 65281 && ch <= 65374 && ch != 65307
   							&& ch != 65306 && ch != 65292 && ch != 65294)) {
   				// 全型字符範圍：65281-65374
   				// '；'為65307
   				// '：'為65306
   				// '，'為65292 ,前一位及後一位為數字才轉
   				// '．'為65294,前一位為數字才轉,否則轉為'。'12290

   				rtn[j++] = (char) (ch - 0xfee0);
   			} else {
   				rtn[j++] = ch;
   			}
   		}
   		char[] rtnn = new char[j];
   		System.arraycopy(rtn, 0, rtnn, 0, j);
   		return new String(rtnn);
   	}// ;
   	
    /**
     * 在指定長度的字串後面附加指定的字串
     *
     * @param str
     *            原始字串
     * @param len
     *            指定長度
     * @param addstr
     *            欲附加的字串
     * @return 組合後字串
     */
    public static String addStrByFixedLength(String str, int len, String addstr) {
        String[] tempStr = splitFixedLength(str, len);
        StringBuilder strgrp = new StringBuilder();
        if (tempStr.length == 1) {
            strgrp.append(tempStr[0]);
        } else {
            for (int i = 0; i < tempStr.length; i++) {
                strgrp.append(tempStr[i]).append(addstr);
            }
        }
        return strgrp.toString();
    }
    
    /**
     * UTF-8轉換為BIG5 無法轉換的字用?取代
     * @param s
     * @return
     * @throws CharacterCodingException
     */
    public static String transUTF8ToBig5(String s) throws CharacterCodingException {
        Charset big5Charset = Charset.forName("Big5");

        CharsetEncoder encoder = big5Charset.newEncoder();
        encoder.onMalformedInput(CodingErrorAction.REPLACE);
        encoder.onUnmappableCharacter(CodingErrorAction.REPLACE);
        encoder.replaceWith(new byte[] {(byte)'?'});

        ByteBuffer big5Bytes = encoder.encode(CharBuffer.wrap(s));
        String big5String = new String(big5Bytes.array(), big5Charset);
		return big5String;
    }

    /**
	 * 截字串 for e-Loan DB
	 *
	 * @param s
	 * @param maxBytes
	 * @return
	 */
	public static String truncateToFitUtf8ByteLength(String s, int maxBytes) {

		return truncateToFitByteLength(s, maxBytes, "UTF-8");
	}
	
	public static String truncateToFitByteLength(String s, int maxBytes, String charset) {
		if (s == null) {
			return null;
		}
		Charset cs = Charset.forName(charset);
		CharsetDecoder decoder = cs.newDecoder();
		byte[] sba = s.getBytes(cs);
		if (sba.length <= maxBytes) {
			return s;
		}
		// Ensure truncation by having byte buffer = maxBytes
		ByteBuffer bb = ByteBuffer.wrap(sba, 0, maxBytes);
		CharBuffer cb = CharBuffer.allocate(maxBytes);
		// Ignore an incomplete character
		decoder.onMalformedInput(CodingErrorAction.IGNORE);
		decoder.decode(bb, cb, true);
		decoder.flush(cb);
		String res = new String(cb.array(), 0, cb.position());

		// 為避免 cp937 這類, 在 new String 時自動補上 0x0f, 這裡做個檢查, 必要時切掉最後一個字
		if (res.length() > 0 && res.getBytes(cs).length > maxBytes) return res.substring(0, res.length() - 1);
		return res;
	}
	
    /**
     * 依指定長度分割字串
     *
     * @param str
     *            原始字串
     * @param len
     *            指定長
     * @return 分割後字串陣列
     */
    public static String[] splitFixedLength(String str, final int len) {
        // return str.split("(?<=\\G.{" + len + "})");
        List<String> tempList = new ArrayList<String>();
        int idx = 0;
        byte[] bmsg = str.getBytes();
        int blen = bmsg.length;
        while (idx < blen) {
            String tmp = new String(bmsg, idx, idx + len < blen ? len : blen - idx);
            tempList.add(tmp);
            idx += len;

        }
        return tempList.toArray(new String[tempList.size()]);
    }

    /**
     * 取得組合字串
     *
     * @param params
     *            字串片斷
     * @return 組合字串
     */
    public final static String concat(Object... params) {
        StringBuffer strBuf = new StringBuffer();
        for (Object o : params) {
            if (o instanceof byte[]) {
                strBuf.append(new String((byte[]) o));
            } else {
                strBuf.append(String.valueOf(o));
            }
        }
        return strBuf.toString();
    }

    /**
     * 解析每行數據獲取數據庫每個欄位對應的數據集合
     *
     * @param dataString
     *            一行數據
     * @param delimit
     *            分隔符號
     * @return List<String> 數據集合
     */
    public static List<String> split(String dataString, String delimit) {
        int delSize = delimit.trim().length();
        List<String> list = new ArrayList<String>();
        dataString = dataString + delimit;
        int j = 0;
        while ((j = dataString.indexOf(delimit)) != -1) {
            String str = dataString.substring(0, j);
            dataString = dataString.substring(j + delSize, dataString.length());
            list.add(str);
        }
        return list;
    }

    /**
     * convert InputStream to String
     *
     * @param is
     *            InputStream object
     * @return String
     */
    public static String stream2String(InputStream is) {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {
            String line = null;
            while ((line = reader.readLine()) != null) {
                if (sb.length() > 0) {
                    sb.append("\n");
                }
                sb.append(line);
            }
        } catch (Exception ex) {
            sb.append("<<Exception>>:" + ex.getLocalizedMessage());
        }
        return sb.toString();
    }
    
    /**
     * 當有個人戶的名稱
     * "備償XX'
     * "管委會"
     * "履保戶"
     * 會清掉
     * @param val
     * @return
     */
    public static String trim0024Str(String val){
        if(isEmpty(val)){
            return "";
        }
        return val.replaceFirst("履保戶","").replace("管委會","").replace("備償戶","").replace("備償專戶","");
    }

}
