package com.mega.eloan.lms.dw.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dw.service.DWELF490ovsService;

@Service
public class DWELF490ovsServiceImpl extends AbstractDWJdbc implements
		DWELF490ovsService{
	public List<?> findELF490ovsSelNewRule(String dataYm,String branch){
		return this.getJdbc().queryForList("DW_ELF490OVS.selNewRule", new Object[] { dataYm,branch });
	}
	
	public List<?> findELF490ovsSelOldRule(String dataYm,String branch){
		return this.getJdbc().queryForList("DW_ELF490OVS.selOldRule", new Object[] { dataYm,branch });
	}
}
