/* 
 * LMS9020ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.service.FlowNameService;
import com.mega.eloan.lms.dao.L140M01QDao;
import com.mega.eloan.lms.dao.L902M01ADao;
import com.mega.eloan.lms.dao.L902S01ADao;
import com.mega.eloan.lms.fms.service.LMS9020Service;
import com.mega.eloan.lms.model.L140M01Q;
import com.mega.eloan.lms.model.L902M01A;
import com.mega.eloan.lms.model.L902S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 停權解除維護ServiceImpl
 * </pre>
 * 
 * @since 2013/1/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/21,Miller,new
 *          </ul>
 */
@Service
public class LMS9020ServiceImpl extends AbstractCapService implements
		LMS9020Service {
	@Resource
	L902M01ADao l902m01aDao;
	@Resource
	L902S01ADao l902s01aDao;
	@Resource
	FlowService flowService;
	@Resource
	FlowNameService flowNameService;
	@Resource
	L140M01QDao l140m01qDao;

	private static final Logger logger = LoggerFactory
			.getLogger(LMS9020ServiceImpl.class);

	// 以下程式碼不會用到****************************
	@SuppressWarnings("rawtypes")
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		return null;
	}

	// ****************************************************

	@Override
	public void save(GenericBean... entity) {
		// 進行無限多筆儲存
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				try {// 設定更新與建立人員
					if (Util.isEmpty(model.get(EloanConstants.OID))) {// 有OID=>已存在=>不更改建立人員
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}
					model.set("updater", user.getUserId());
					model.set("updateTime", CapDate.getCurrentTimestamp());
				} catch (CapException e) {
					logger.error("CapException!!", e);
				}

				if (model instanceof L902M01A) {
					l902m01aDao.save((L902M01A) model);
					// 記錄文件異動記錄
					// docLogService.record(model.getOid(),DocLogEnum.SAVE);
				} else if (model instanceof L902S01A) {
					l902s01aDao.save((L902S01A) model);
				}
			}
		}

	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L902M01A) {
					((L902M01A) model).setDeletedTime(CapDate
							.getCurrentTimestamp());
					this.save(model);
				} else if (model instanceof L140M01Q) {
					// deleteTable.properties 有設L902M01A deletedTime
					// 刪除時一併刪除L140M01Q
				}
			}
		}

	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L902M01A.class) {
			return l902m01aDao.findPage(search);
		} else if (clazz == L902S01A.class) {
			return l902s01aDao.findPage(search);
		}
		return null;
	}

	@Override
	public L902M01A findL902m01aByOid(String oid) {
		return l902m01aDao.findByOid(oid);
	}

	@Override
	public L902M01A findL902m01aByMainId(String mainId) {
		return l902m01aDao.findByMainId(mainId);
	}

	@Override
	public L902M01A findL902m01aByPeNo(String peNo) {
		return l902m01aDao.findByPeNo(peNo);
	}

	@Override
	public L902M01A findL902m01aMaxPeNo() {
		return l902m01aDao.findMaxPeNo();
	}

	@Override
	public void deleteL902m01a(String oid) {
		L902M01A model = l902m01aDao.findByOid(oid);
		if (model != null) {
			l902m01aDao.delete(model);
		}
	}

	@Override
	public List<L902S01A> findL902s01aByMainId(String mainId) {
		return l902s01aDao.findByMainId(mainId);
	}

	@Override
	public L902S01A findL902s01aByOid(String oid) {
		return l902s01aDao.findByOid(oid);
	}

	@Override
	public void deleteL902s01a(String oid) {
		L902S01A model = l902s01aDao.findByOid(oid);
		if (model != null) {
			l902s01aDao.delete(model);
		}

	}

	@Override
	public void saveList902s01a(List<L902S01A> list) {
		if (list.size() > 0) {
			l902s01aDao.save(list);
		}
	}

	@Override
	public List<L902S01A> findL902s01aByMainIdWithoutDelete(String mainId) {
		return l902s01aDao.findByIndex01(mainId);
	}

	@Override
	public List<L902S01A> findL902s01aByMainIdContainDelete(String mainId) {
		return l902s01aDao.findByMainIdIgnoreDelete(mainId);
	}

	@Override
	public List<L902S01A> findL902s01aByCustIdAndPeNo(String custId,
			String dupNo, String peNo) {
		return l902s01aDao.findByCustIdAndPeNo(custId, dupNo, peNo);
	}

	@Override
	public List<L902S01A> findL902s01aByCustIdWithoutDelete(String custId,
			String dupNo) {
		return l902s01aDao.findByCustId(custId, dupNo);
	}

}
