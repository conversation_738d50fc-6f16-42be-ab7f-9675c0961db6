/*
 * LMS1601FileUploadHandler.java
 *
 * Mega International Commercial Bank.
 */
package com.mega.eloan.lms.lns.handler.form;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.response.MegaErrorResult;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.lns.pages.LMS1601M01Page;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.lns.service.LMS1601Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L162S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.handler.FileUploadHandler;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 上傳主從債務人檔
 * </pre>
 *
 * <AUTHOR>
 * @version <ul>
 * <li>2024年03月18日,011850,new
 * </ul>
 * @since 2024年03月18日
 */
@Scope("request")
@Controller("lms1601fileuploadhandler")
public class LMS1601FileUploadHandler extends FileUploadHandler {

	@Autowired
	DocFileService fileService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	MisCustdataService custDataService;
	
	@Resource
	LMS1601Service lms1601Service;
	
	@Resource
	LMS1401Service lms1401Service;
	
	@SuppressWarnings("unused")
	@Override
	public IResult afterUploaded(PageParameters params)
			throws CapException {
		Properties prop = MessageBundleScriptCreator
		.getComponentResource(LMS1601M01Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		MultipartFile file = params.getFile(params.getString("fieldId"));
		InputStream io = null;
		Workbook wb = null;
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		boolean isGetImgDimension = params.getBoolean("getImgDimension");
		
		String sysId = params.getString("sysId", fileService.getSysId());

		// 設定上傳檔案資訊
		String fileName = file.getName();
		String fieldId = Util.trim(params.getString("fieldId"));

		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);
		Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
		Set<L162S01A> oldl162s01as = l160m01a.getL162S01A();
		List<String> selectMainId = new ArrayList<String>();
		HashMap<String, String> mainIdmapingCntrno = new HashMap<String, String>();
		List<String> l160m01aCntrno = new ArrayList<String>();
		for (L160M01B l160m01b : l160m01bs) {
			selectMainId.add(l160m01b.getReMainId());
			mainIdmapingCntrno
					.put(l160m01b.getReMainId(), l160m01b.getCntrNo());
			l160m01aCntrno.add(l160m01b.getCntrNo());
		}
		List<String> oldl162s02aCntrno = new ArrayList<String>();
		for (L162S01A l162s01a : oldl162s01as) {
			oldl162s02aCntrno.add(l162s01a.getCntrNo()+l162s01a.getCustId());
		}
		
		HashMap<String, L140M01A> l140m01aCntrno = new HashMap<String, L140M01A>();
		List<L140M01A> l140m01as = lms1401Service.findL140m01aListByMainIdList(selectMainId
				.toArray(new String[selectMainId.size()]));
		for (L140M01A l140m01a : l140m01as) {
			l140m01aCntrno.put(l140m01a.getCntrNo(), l140m01a);
		}
		
		if (params.containsKey("fileSize")) {
			if (file.getSize() > params.getLong("fileSize", 1048576)) {
				// EFD0063=ERROR|上送的檔案已超過$\{fileSize\}M的限制大小，無法執行上傳動作。|
				Map<String, String> msg = new HashMap<String, String>();
				msg.put("fileSize",
						CapMath.divide(params.getString("fileSize"), "1048576")); // 1M*1024*1024
				MegaErrorResult result = new MegaErrorResult();
				result.putError(params, new CapMessageException(RespMsgHelper.getMessage("EFD0063", msg), getClass()));
				return result;
			}
		}	
		
		// 開始匯入EXCEL****************************************************************************
		if(file.getSize() > 0){
			int currentCol = 0, currentRow = 0, dataStartRow = 1, dataLastRow = 0;
			int rowCount = 0;
			
			Map<String, CapAjaxFormResult> codeMaps = codeTypeService.findByCodeType(
					new String[]{ "CountryCode", "lms1605s03_rType", "G100M01A_SECONDARY_DEBTOR_TYPE",
							"G100M01A_SOLICITOR_REASON", "Relation_type1", "Relation_type2" });

			CapAjaxFormResult cuntryCodeMap = codeMaps.get("CountryCode");
			CapAjaxFormResult lms1605s03_rTypeMap = codeMaps.get("lms1605s03_rType");
			CapAjaxFormResult secondary_debtor_typeMap = codeMaps.get("G100M01A_SECONDARY_DEBTOR_TYPE");
			CapAjaxFormResult solicitor_reasonMap = codeMaps.get("G100M01A_SOLICITOR_REASON");
			
			CapAjaxFormResult masterDebtorRelationshipMap = codeMaps.get("Relation_type1");
			masterDebtorRelationshipMap = masterDebtorRelationshipMap.putAll(codeMaps.get("Relation_type2"));
			//最後要寫入的連保人
			List<L162S01A> dataList = new ArrayList<L162S01A>();
			//處理過的額度
			HashSet<String> cntrnoList =  new HashSet<String>();
			try {
				io = file.getInputStream();
				wb = Workbook.getWorkbook(io);
				Sheet sheet = wb.getSheet(0);
				
				Cell[] branchCell = sheet.getColumn(0);
				dataLastRow = branchCell.length;
				if (dataLastRow == 0) {
					// message.001=檔案內sheet1裏無資料！
					throw new CapMessageException(prop.getProperty("L162S02A.message.1"), getClass());
				}
				//額度序號
				List<String> custIdCntrNo = new ArrayList<String>();
				
				try {
					// 額度序號	Mega ID	國家別	相關身份	從債務人類別	與主債務人關係	保證人保證迄日/董監事任期止日	徵提保證人原因代碼	保證成數
					// 本行國家暴險是否以保證人國別為計算基準
					// (取代最終風險國別)
					// 擔保限額   當地客戶識別ID
					Map<String, Map<String, Object>> custMaps = new HashMap<String, Map<String, Object>>();
					for (currentRow = dataStartRow; currentRow < dataLastRow; currentRow++) {
						currentCol = 0;
						String cntrNo = CapString.trimNull(
								sheet.getCell(currentCol++, currentRow).getContents()); //額度序號
						String custId = CapString.trimNull(
								sheet.getCell(currentCol++, currentRow).getContents()); // Mega ID
						
						if(CapString.isEmpty(cntrNo) || CapString.isEmpty(cntrNo)){
							break;//break;
						}
						
						if(!l160m01aCntrno.contains(cntrNo)){
							//message17=Excel中額度序號:{0},從債務人ID:{1},已存在請勿重覆建檔!
							throw new CapMessageException(MessageFormat.format(prop.getProperty("L162S02A.message21"), cntrNo), getClass());
						}
						
						

						if (custIdCntrNo.contains(custId + cntrNo)) {
							//message17=Excel中額度序號:{0},從債務人ID:{1},已存在請勿重覆建檔!
							throw new CapMessageException(MessageFormat.format(prop.getProperty("L162S02A.message17"), cntrNo,
									custId), getClass());
						}
						//
						if (oldl162s02aCntrno.contains(custId + cntrNo)) {
							//message17=Excel中額度序號:{0},從債務人ID:{1},已存在請勿重覆建檔!
							throw new CapMessageException(MessageFormat.format(prop.getProperty("L162S02A.message17"), cntrNo,
									custId), getClass());
						}
						
						Map<String, Object> custMap;
						if (!custMaps.containsKey(custId)) {
							custMap = custDataService.findByIdDupNo(custId, "0");
							custMaps.put(custId, custMap);
						} else {
							custMap = custMaps.get(custId);
						}
						String custName = CapString.trimNull(custMap.get("LCNAME"));
						if (CapString.isEmpty(custName)) {
							custName = CapString.trimNull(custMap.get("CNAME"));
						}
						if (CapString.isEmpty(cntrNo)) {
							if (CapString.isEmpty(custId)) {
								break;
							} else {
								//message5=不可為空白!!
								throw new CapMessageException(prop.getProperty("L162S02A.message5"), getClass());
							}
						} else if (custMap == null) {
							//gte1000.002=若無客戶建檔者，請先至0024完成建檔
							throw new CapMessageException(custId + prop.getProperty("L162S02A.message20"), getClass());
						}
						custIdCntrNo.add(custId + cntrNo);
						String nation = CapString.trimNull(
								sheet.getCell(currentCol++, currentRow).getContents()); // 國家別
						if (!cuntryCodeMap.containsKey(nation)) {
							//message6={0}查無此國家代碼!
							throw new CapMessageException(MessageFormat.format(prop.getProperty("L162S02A.message6"), nation),
									getClass());
						}
						String relatedIdentity = CapString.trimNull(
								sheet.getCell(currentCol++, currentRow).getContents()); //
						// 相關身份
						if (!CapString.isEmpty(relatedIdentity)) {
							relatedIdentity = relatedIdentity.substring(0, 1);
						}
						if (!lms1605s03_rTypeMap.containsKey(relatedIdentity)) {
							//message7={0}查無此相關身份!
							throw new CapMessageException(MessageFormat.format(prop.getProperty("L162S02A.message7"),
									relatedIdentity),
									getClass());
						}
						String secondaryDebtorType = CapString.trimNull(
								sheet.getCell(currentCol++, currentRow).getContents()); // 從債務人類別
						if (!CapString.isEmpty(secondaryDebtorType)) {
							secondaryDebtorType = secondaryDebtorType.substring(0, 1);
						}
						if (!secondary_debtor_typeMap.containsKey(secondaryDebtorType)) {
							//message8={0}查無此從債務人類別!
							throw new CapMessageException(
									MessageFormat.format(prop.getProperty("L162S02A.message8"), secondaryDebtorType), getClass());
						}

						String masterDebtorRelationship = CapString.trimNull(
								sheet.getCell(currentCol++, currentRow).getContents()); // 與主債務人關係
						if (!CapString.isEmpty(masterDebtorRelationship)) {
							if(masterDebtorRelationship.indexOf("-")>0){
								masterDebtorRelationship = masterDebtorRelationship.substring(0,
										masterDebtorRelationship.indexOf("-"));
							}
							
						}
						if (!masterDebtorRelationshipMap.containsKey(masterDebtorRelationship)) {
							//message9={0}查無此與主債務人關係!
							throw new CapMessageException(
									MessageFormat.format(prop.getProperty("L162S02A.message9"), masterDebtorRelationship),
									getClass());
						}
						String dueDate = CapString.trimNull(
								sheet.getCell(currentCol++, currentRow).getContents()); // 保證人保證迄日/董監事任期止日
						if (!CapString.isEmpty(dueDate) && !CapDate.validDate(dueDate, "yyyy-MM-dd")) {
							// message10=保證人保證迄日/董監事任期止日不符合yyyy-MM-dd格式，如無此日期請留空白
							throw new CapMessageException(MessageFormat.format(prop.getProperty("L162S02A.message10"), dueDate),
									getClass());
						}
						String solicitorReasonCode = CapString.trimNull(
								sheet.getCell(currentCol++, currentRow).getContents());//徵提保證人原因代碼
						if (!CapString.isEmpty(solicitorReasonCode)) {
							solicitorReasonCode = solicitorReasonCode.substring(0, 2);
							if (!solicitor_reasonMap.containsKey(solicitorReasonCode)) {
								//message11={0}查無此徵提保證人原因代碼!
								throw new CapMessageException(
										MessageFormat.format(prop.getProperty("L162S02A.message11"), solicitorReasonCode),
										getClass());
							}
						}

						String guaranteeRatio = CapString.trimNull(
								sheet.getCell(currentCol++, currentRow).getContents());//保證成數
						if (!CapString.isEmpty(guaranteeRatio) && (CapMath.compare(guaranteeRatio,
								"100") == 1 || CapMath.compare(guaranteeRatio, "0") == -1)) {
							//message12={0}保證成數需大於等於0且小於等於100
							throw new CapMessageException(MessageFormat.format(prop.getProperty("L162S02A.message12"),
									guaranteeRatio),
									getClass());
						}
						String guanaexpo = CapString.trimNull(
								sheet.getCell(currentCol++, currentRow).getContents());//本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)
						if (!CapString.isEmpty(guanaexpo)) {
							if (CapString.checkRegularMatch(relatedIdentity, "[CS]")) {
								//message15=相關身份為C共同借款人或S擔保品提供人時，本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)，僅能空白
								throw new CapMessageException(
										MessageFormat.format(prop.getProperty("L162S02A.message15"), guaranteeRatio), getClass());
							}
							if (!CapString.checkRegularMatch(guanaexpo, "[YN]")) {
								//message13={0} 本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)，僅能空白或YN
								throw new CapMessageException(
										MessageFormat.format(prop.getProperty("L162S02A.message13"), guaranteeRatio), getClass());
							}
						}
						
						String grtAmt = CapString.trimNull(sheet.getCell(currentCol++, currentRow).getContents()).replaceAll(",", "");//擔保限額
						String localId = CapString.trimNull(sheet.getCell(currentCol++, currentRow).getContents());//當地客戶識別ID	
						
						//L162S01A oldS01a = lms1601Service.findS01AByCustIdCntrNo(custId, "0", cntrNo);
						L162S01A s01a  = new L162S01A();
						List<L162S01A> oldS01a_list = lms1601Service.findL162s01aByMainIdCntrno(mainId, cntrNo);
						
						String rKindM = "";
						if (Util.isNotEmpty(masterDebtorRelationship)) {
							int index = masterDebtorRelationship.indexOf("X");
							switch (index) {
							case 0:
								rKindM = "2";
								break;
							case 1:
								rKindM = "1";
								break;
							case -1:
								rKindM = "3";
								break;
							}
						}
						//
						// 設定主債務人資料
						s01a.setMainId(mainId);
						s01a.setCntrNo(cntrNo);
						s01a.setCreator(user.getUserId());
						s01a.setCreateTime(CapDate.getCurrentTimestamp());
						s01a.setCustId(l160m01a.getCustId());
						s01a.setDupNo(l160m01a.getDupNo());

						// 設定從債務人資料
						s01a.setRCountry(nation);//國別
						s01a.setRId(custId);//ID
						s01a.setRDupNo("0");//重複碼
						s01a.setRKindD(Util.trim(masterDebtorRelationship));//關係
						s01a.setRKindM(rKindM);//關係類別
						s01a.setRName(custName);//從債務人姓名
						s01a.setDataSrc("2");// 2 是自行新增的選項
						s01a.setRType(relatedIdentity);
						s01a.setGrtAmt(Util.parseBigDecimal(grtAmt));
						s01a.setLocalId(localId);
						
						// J-103-0299 Web e-Loan企金額度明細表保證人新增保證比例
						if (Util.equals(relatedIdentity, UtilConstants.lngeFlag.擔保品提供人)) {
							s01a.setGuaPercent(null);
							// J-110-0040_05097_B1001 Web
							// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
							s01a.setGuaNaExposure(null);
						} else {
							if (Util.isEmpty(guaranteeRatio)) {
								s01a.setGuaPercent(BigDecimal.valueOf(100));
							} else {
								s01a.setGuaPercent(Util.parseBigDecimal(guaranteeRatio));
							}
							
							L140M01A l140m01a = l140m01aCntrno.get(cntrNo);
							// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
							//s01a.setPriority(l140m01i.getPriority());
							// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
							//if(l140m01a != null){
							s01a.setGuaNaExposure(Util.trim(guanaexpo));
							//}
						}
						// 檢查是否已經有此債務人
						for (L162S01A l162m01aold : oldS01a_list) {
							if (Util.trim(l162m01aold.getCustId()).toUpperCase().equals(Util.trim(s01a.getCustId()).toUpperCase())
									&& Util.trim(l162m01aold.getDupNo()).toUpperCase().equals(Util.trim(s01a.getDupNo()).toUpperCase())) {
								if (Util.trim(l162m01aold.getRId()).toUpperCase().equals(Util.trim(s01a.getRId()).toUpperCase())
										&& Util.trim(l162m01aold.getRDupNo()).toUpperCase().equals(Util.trim(s01a.getRDupNo()).toUpperCase())
										&& Util.trim(l162m01aold.getRType()).toUpperCase().equals(Util.trim(s01a.getRType()).toUpperCase())) {
									if (Util.trim(l162m01aold.getCntrNo()).toUpperCase().equals(Util.trim(s01a.getCntrNo()).toUpperCase())) {
										Map<String, String> param = new HashMap<String, String>();
										param.put("custId",Util.trim(s01a.getRId()).toUpperCase()
														+ " "
														+ Util.trim(s01a.getRDupNo()).toUpperCase());
										// EFD3010=ERROR|$\{custId\}此債務人已經存在 ！！|
										throw new CapMessageException(RespMsgHelper.getMessage("EFD3010", param),
												getClass());
									}
	
								}
							}
						}
						if(s01a != null){
							//處理過的額度序號
							if(!cntrnoList.contains(cntrNo)){
								cntrnoList.add(cntrNo);
							}
							
							dataList.add(s01a);
						}
					}
					
					if(dataList != null && dataList.size() > 0){
						//G-113-0036 主從債務人
						//取得 MIS.ELLNGTEE(ELF401)，如額度明細引入之主從債務人 擔保限額Guarante Amount(GRTAMT)、當地客戶識別ID Local Id(LOCALID)未填，則帶MIS.ELLNGTEE為預設值
						for(String checkCntrno : cntrnoList){
							lms1601Service.checkL162s01aLocalIdAndGrtAmt(l160m01a.getCustId(), l160m01a.getDupNo(), checkCntrno, dataList);
						}

						lms1601Service.saveL162m01aList(dataList);
					}
					
				} catch (Exception fe) {
					// FileUpload.checkfileMsg=檔案中，第{0}列中{1}-{2},請查明後再執行！
					throw new CapMessageException(MessageFormat.format(prop
							.getProperty("L162S02A.FileUpload.checkfileMsg"),
							String.valueOf(currentRow + 1),
							((currentCol) + " 欄"), fe.getMessage()), getClass());
				}
			} catch (CapMessageException cm) {
				throw cm;
			} catch (Exception e) {
				throw new CapException(e, getClass());
			} finally {
				if (io != null) {
					try {
						io.close();
					} catch (IOException e) {
						throw new CapException(e, getClass());
					}
				}
				if (wb != null) wb.close();
			}
		}
		
		return null;
	}
	
}
