/* 
 * L120S21ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S21A;

/** LGD額度共用檔 J-110-0986_05097_B1001 於簽報書新增LGD欄位s **/
public interface L120S21ADao extends IGenericDao<L120S21A> {

	L120S21A findByOid(String oid);

	List<L120S21A> findByMainId(String mainId);

	List<L120S21A> findByIndex01(String mainId);

	List<L120S21A> findByIndex02(String mainId, String cntrNoCo_s21a);

	List<L120S21A> findByIndex03(String mainId, String cntrNoCo_s21a,
			String cntrNo_s21a);

	List<Object[]> findMinAllocate(String mainId);

	List<L120S21A> findByMainIdAndCntrNo(String mainId, String cntrNo);
}