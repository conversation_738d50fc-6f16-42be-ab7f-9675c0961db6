package com.mega.eloan.lms.crs.handler.form;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.gwclient.EloanBatchClient;
import com.mega.eloan.common.gwclient.EloanSubsysBatReqMessage;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FlowSimplifyService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.flow.LMS2411Flow;
import com.mega.eloan.lms.crs.pages.LMS2411M01Page;
import com.mega.eloan.lms.crs.service.LMS2400Service;
import com.mega.eloan.lms.crs.service.LMS2401Service;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.bean.ELF491;
import com.mega.eloan.lms.mfaloan.bean.ELF601;
import com.mega.eloan.lms.mfaloan.bean.ELF602;
import com.mega.eloan.lms.mfaloan.bean.MISLN20;
import com.mega.eloan.lms.mfaloan.service.MisELF491Service;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.eloan.lms.model.C241M01C;
import com.mega.eloan.lms.model.C241M01E;
import com.mega.eloan.lms.model.C241M01G;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * [個金]覆審報告表
 * </pre>
 * 
 */

@Scope("request")
@Controller("lms2411m01formhandler")
@DomainClass(C241M01A.class)
public class LMS2411M01Formhandler extends AbstractFormHandler {
	// J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
	private static final Logger logger = LoggerFactory
			.getLogger(LMS2411M01Formhandler.class);
	private static final DateFormat S_FORMAT = new SimpleDateFormat(
			UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
	private static final int MAXLEN_C241M01A_CONDITION = StrUtils
			.getEntityFileldLegth(C241M01A.class, "condition", 9000);
	private static final int MAXLEN_C241M01A_BRANCHCOMM = StrUtils
			.getEntityFileldLegth(C241M01A.class, "branchComm", 384);
	private static final int MAXLEN_C241M01C_CHKTEXT = StrUtils
			.getEntityFileldLegth(C241M01C.class, "chkText", 1500);
	private static final int MAXLEN_C241M01B_GUARANTEENAME = StrUtils
			.getEntityFileldLegth(C241M01B.class, "guaranteeName", 9000);
	private static final int MAXLEN_C241M01B_MAJORMEMO = StrUtils
			.getEntityFileldLegth(C241M01B.class, "majorMemo", 3000);
	private static final int MAXLEN_C241M01B_COBORROWER = StrUtils
			.getEntityFileldLegth(C241M01B.class, "coBorrower", 192);
	private static final int MAXLEN_C241M01B_GUARANTOR = StrUtils
			.getEntityFileldLegth(C241M01B.class, "guarantor", 192);

	@Resource
	EloanBatchClient eloanBatchClient;

	@Resource
	RetrialService retrialService;

	@Resource
	ICustomerService iCustomerService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branchService;

	@Resource
	FlowSimplifyService flowSimplifyService;

	@Resource
	TempDataService tempDataService;

	@Resource
	DocCheckService docCheckService;

	@Resource
	DocLogService docLogService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	LMS2400Service lms2400Service;

	@Resource
	LMS2401Service lms2401Service;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	MisELF491Service misELF491Service;

	@Resource
	MisMISLN20Service misMISLN20Service;

	@Resource
	MisPTEAMAPPService misPTEAMAPPService;

	@Resource
	CLSService clsService;

	@Resource
	EjcicService ejcicService;

	@Resource
	DocFileService docFileService;

	@Resource
	LMSService lmsService;

    @Resource
    SysParameterService sysParameterService;

	Properties prop_lms2411m01 = MessageBundleScriptCreator
			.getComponentResource(LMS2411M01Page.class);

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C241M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findC241M01A_oid(mainOid);

			String page = params.getString(EloanConstants.PAGE);
			if ("01".equals(page)) {
				{
					LMSUtil.addMetaToResult(result, meta,
							new String[] { "randomCode" });
				}

				result.set("status",
						lms2400Service.c241m01a_docStatusDesc(meta));

				result.set("creator", _id_name(meta.getCreator()));
				result.set("createTime",
						Util.nullToSpace(TWNDate.valueOf(meta.getCreateTime())));
				result.set("updater", _id_name(meta.getUpdater()));
				result.set("updateTime",
						Util.nullToSpace(TWNDate.valueOf(meta.getUpdateTime())));

				_signInfo(result, meta);
			} else if ("02".equals(page)) {
				// 覆審訊息
				{
					LMSUtil.addMetaToResult(result, meta, new String[] {
							"retrialDate", "lastRetrialDate",
							"shouldReviewDate", "uckdDt", "mdFlag",
							"retrialKind", "nCkdFlag", "nCkdMemo",
							"nckdDetail"
							// ==========
							, "lnf660_loan_class", "lnf660_m_contract",
							"comId", "comDup", "comName"
							// ==========
							, "lnDataDate", "totQuotaCurr", "totBalCurr"
							// ==========
							, "docFmt", "lastRealDt", "realCkFg" });
				}
				// overdueYN 可以為 empty, 避免傳回 ' ', 增加trim
				result.set("overdueYN", Util.trim(meta.getOverdueYN()));
				result.set("overdueP", Util.trim(meta.getOverdueP()));
				result.set("mdFlagDesc", "");
				if (Util.isNotEmpty(meta.getMdFlag())) {
					Map<String, String> mdFlagMap = retrialService
							.get_crs_lrs_LNFE0854_MDFLAG();
					{
						String key = Util.trim(meta.getMdFlag());
						result.set("mdFlagDesc",
								mdFlagMap.containsKey(key) ? mdFlagMap.get(key)
										: key);
					}
				}

				_specifyCycle(result, meta);

				// 授信帳務資料
				{
					result.set("totQuota",
							NumConverter.addComma(meta.getTotQuota()));
					result.set("totBal",
							NumConverter.addComma(meta.getTotBal()));
				}

				{// 附件檔
					_grpDetail_attch(result, meta);
				}

				if (CrsUtil.isCaseP(meta)) {
					TreeSet<String> lcNoSet = new TreeSet<String>();
					for (C241M01B c241m01b : retrialService
							.findC241M01B_c241m01a(meta)) {
						lcNoSet.add(Util.trim(c241m01b.getLcNo()));
					}
					lcNoSet.remove("");

					result.set("lcNo", StringUtils.join(lcNoSet, "、"));
					// ---------------------
					// 價金履保可以改 custId, dupNo, custName
					_setResult_P_SellerBuyerInfo(result, meta, false);
					result.set("pbAcct", Util.trim(meta.getPbAcct()));

				} else if (CrsUtil.isCaseS(meta)) {
					LinkedHashMap<String, Map<String, List<String>>> data_map = retrialService
							.groupC241M01F_by_caseMainId_rel_type(meta);
					JSONArray jsonAraay = new JSONArray();
					if (data_map.size() == 0) {
						JSONObject o = new JSONObject();
						o.put("m01f_caseInfo", "");
						o.put("m01f_rel_type_1", "");
						o.put("m01f_rel_type_2", "");
						o.put("m01f_rel_type_3", "");
						// ---
						jsonAraay.add(o);
					} else {
						for (String caseMainId : data_map.keySet()) {
							Map<String, List<String>> rel_type_staffStr = data_map
									.get(caseMainId);
							L120M01A l120m01a = clsService
									.findL120M01A_mainId(caseMainId);
							String caseInfo = caseMainId;
							if (l120m01a != null) {
								caseInfo = Util.toSemiCharString(l120m01a
										.getCaseNo());
							}
							// ====
							JSONObject o = new JSONObject();
							o.put("m01f_caseInfo", caseInfo);
							o.put("m01f_rel_type_1", StringUtils.join(
									rel_type_staffStr.get("1"), "<br/>"));
							o.put("m01f_rel_type_2", StringUtils.join(
									rel_type_staffStr.get("2"), "<br/>"));
							o.put("m01f_rel_type_3", StringUtils.join(
									rel_type_staffStr.get("3"), "<br/>"));
							// ---
							jsonAraay.add(o);
						}
					}

					HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
					map.put("key", jsonAraay);
					result.set("c241m01f_list", new CapAjaxFormResult(map));

					result.set(
							"docKindS_pa_period",
							StringUtils.join(
									CrsUtil.get_CLS180R18_period_byEndYM(
											meta.getPa_ym(), 7), "~"));
				}
			} else if ("03".equals(page)) {
				{
					LMSUtil.addMetaToResult(result, meta,
							new String[] { "rptId" });
				}

				// 組 item
				if (CrsUtil.isCaseS(meta)) {
					if (true) {
						HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();

						List<C241M01C> list = retrialService
								.findC241M01C_c241m01a(meta);
						if (true) {
							String itemType_js = "S_ITEM";
							JSONArray jsonAraay = new JSONArray();

							// ---
							for (C241M01C c241m01c : list) {

								JSONObject o = new JSONObject();
								o.putAll(DataParse.toJSON(c241m01c, true));
								o.put("_chkResult_fmt",
										CrsUtil.chkResult_fmt(c241m01c));

								if (true) {
									String _ptMgrName = "";
									String ptMgrId = Util.trim(c241m01c
											.getPtMgrId());
									if (Util.isNotEmpty(ptMgrId)) {
										_ptMgrName = Util.trim(userInfoService
												.getUserName(ptMgrId));
									}
									o.put("_ptMgrName", _ptMgrName);
								}
								jsonAraay.add(o);
							}
							map.put(itemType_js, jsonAraay);
						}
						result.set("c241m01c_list", new CapAjaxFormResult(map));
						// 指定 chkText 長度
						int chkText_maxlengthC = MAXLEN_C241M01C_CHKTEXT / 3;
						result.set("c241m01c_chkText_maxlength",
								MAXLEN_C241M01C_CHKTEXT);
						result.set("c241m01c_chkText_maxlengthC",
								chkText_maxlengthC);
					}
				} else {
					HashMap<String, String> map_title = new HashMap<String, String>();
					{
						map_title.put(CrsUtil.A_徵信事項, "徵信事項");
						map_title.put(CrsUtil.B_債權確保, "債權確保");
						map_title.put(CrsUtil.C_其他, "其他");
						map_title.put(CrsUtil.D_開辦徵信事項, "開辦徵信事項");
						map_title.put(CrsUtil.E_專戶款項撥付作業, "專戶款項撥付作業");
						map_title.put(CrsUtil.F_糾紛案件處理, "糾紛案件處理");
						map_title.put(CrsUtil.Y_項目附屬選項, "");
						map_title.put(CrsUtil.Z_電腦建檔資料, "電腦建檔資料");
						result.set("c241m01c_title", new CapAjaxFormResult(
								map_title));
					}
					// ---
					{
						HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();

						List<C241M01C> list = retrialService
								.findC241M01C_c241m01a(meta);
						for (String itemType : map_title.keySet()) {
							setC241M01C(map, itemType, list, meta);
						}
						result.set("c241m01c_list", new CapAjaxFormResult(map));
						// 指定 chkText 長度
						int chkText_maxlengthC = MAXLEN_C241M01C_CHKTEXT / 3;
						result.set("c241m01c_chkText_maxlength",
								MAXLEN_C241M01C_CHKTEXT);
						result.set("c241m01c_chkText_maxlengthC",
								chkText_maxlengthC);
					}
				}
			} else if ("04".equals(page)) {
				{
					LMSUtil.addMetaToResult(result, meta, new String[] {
							"conFlag", "condition", "upDate", "branchComm",
							"conFlag2A", "conFlag2B", "conFlag2C" });
					result.set("approver", _id_name(meta.getApprover()));
				}

				_signInfo(result, meta);
			} else if ("05".equals(page)) {
				{
					LMSUtil.addMetaToResult(result, meta,
							new String[] { "needPa" });
					if (Util.isEmpty(Util.trim(meta.getNeedPa()))
							&& Util.equals(meta.getDocStatus(),
									RetrialDocStatusEnum.區中心_編製中.getCode())
							&& meta.getRetrialDate() != null
							&& LMSUtil.cmpDate(meta.getRetrialDate(), ">",
									CapDate.parseDate("2022-02-07"))) {
						result.set("needPa", "N");
					}
					result.set("paForm", retrialService.buildPaFormHtml(meta, prop_lms2411m01));
				}
			}
		}

		return defaultResult(params, meta, result);
	}

	private void _grpDetail_attch(CapAjaxFormResult result, C241M01A meta)
			throws CapException {
		HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
		setAttchFile(map, "grpDetailFile", meta.getMainId(),
				CrsUtil.ATTCH_C241M01A_GRPDTL);
		result.set("attch", new CapAjaxFormResult(map));
	}

	private void _specifyCycle(CapAjaxFormResult result, C241M01A meta) {
		Map<String, String> specifyCycleMap = retrialService
				.get_crs_specifyCycle();
		{
			String key = Util.trim(meta.getSpecifyCycle());
			result.set("specifyCycle",
					specifyCycleMap.containsKey(key) ? specifyCycleMap.get(key)
							: key);
		}
	}

	private void _signInfo(CapAjaxFormResult result, C241M01A meta) {

		List<C241M01E> list = retrialService.findC241M01E_c241m01a(meta);
		String branch_L1 = _filter_branchType_staffJob(list, "1",
				UtilConstants.STAFFJOB.經辦L1);
		String branch_L4 = _filter_branchType_staffJob(list, "1",
				UtilConstants.STAFFJOB.執行覆核主管L4);
		String branch_L5 = _filter_branchType_staffJob(list, "1",
				UtilConstants.STAFFJOB.單位授權主管L5);

		String area_L1 = _filter_branchType_staffJob(list, "2",
				UtilConstants.STAFFJOB.經辦L1);
		String area_L4 = _filter_branchType_staffJob(list, "2",
				UtilConstants.STAFFJOB.執行覆核主管L4);
		String area_L5 = _filter_branchType_staffJob(list, "2",
				UtilConstants.STAFFJOB.單位授權主管L5);

		// 受檢
		result.set("appraiser1", branch_L1);
		result.set("reCheck1", branch_L4);
		result.set("manager1", branch_L5);
		// 覆審組
		result.set("appraiser2", area_L1);
		result.set("reCheck2", area_L4);
		result.set("manager2", area_L5);

	}

	private void setAttchFile(HashMap<String, JSONArray> map, String ui_id,
			String mainId, String fieldId) throws CapException {
		JSONArray jsonAraay = null;
		if (map.containsKey(ui_id)) {
			jsonAraay = map.get(ui_id);
		} else {
			jsonAraay = new JSONArray();
		}
		// ---
		List<DocFile> docFileList = retrialService.findDocFileByMainIdFieldId(
				mainId, fieldId);
		for (DocFile docFile : docFileList) {
			if (Util.equals(fieldId, docFile.getFieldId())) {
				JSONObject o = new JSONObject();
				o.putAll(DataParse.toJSON(docFile, true));
				o.put("uploadTime", TWNDate.toFullTW(docFile.getUploadTime()));
				jsonAraay.add(o);
			}
		}
		// ---
		map.put(ui_id, jsonAraay);
	}

	private void setC241M01C(HashMap<String, JSONArray> map, String itemType,
			List<C241M01C> raw_list, C241M01A meta) throws CapException {
		JSONArray jsonAraay = new JSONArray();

		// ---
		for (C241M01C c241m01c : raw_list) {
			if (Util.notEquals(itemType, c241m01c.getItemType())) {
				continue;
			}

			JSONObject o = new JSONObject();
			o.putAll(DataParse.toJSON(c241m01c, true));
			o.put("_chkResult_fmt", CrsUtil.chkResult_fmt(c241m01c));

			// 授信用途是否與申貸用途相符？
			if (Util.equals(CrsUtil.N007, c241m01c.getItemNo())) {
				o.put("_prefix", CrsUtil.Z_DESC_N007);
				o.put("_td_border", "Y");
			}

			if (Util.equals(CrsUtil.ZB1A, c241m01c.getItemNo())) {
				/*
				 * 初期： 在 LMS2411S03Panel.js 有用到 _itemPost 去動態調整，但因覆審條款的文字持續在變動
				 * 
				 * 之後的改版： 在 LMS2411S03PanelA.js ，就不在 js 去處理
				 * _itemPost，直接把【擔保品，若勾「有」則應檢視下列資料建檔是否正確】寫在 html => 這樣子，維護上會較單純
				 * 
				 * 適用的 html 清單 + LMS2411S03Panel201907NA.html +
				 * LMS2411S03Panel202008NA.html + LMS2411S03Panel202105NA.html +
				 * LMS2411S03Panel202204NA.html
				 */
				o.put("_itemPost", CrsUtil.Z_DESC_ZB1A_Y);
			}

			if (Util.equals(CrsUtil.ZA20, c241m01c.getItemNo())) {
				o.put("_u_note", ("<u>") + CrsUtil.Z_NOTE_ZA20 + ("</u>"));
			}

			// "前次覆審有無應行改善事項？
			if (Util.equals(CrsUtil.N013, c241m01c.getItemNo())) {
				o.put("_chkPreReview_fmt", "Y3|N3|K");// 【已改善|未改善|一】
			}

			if (true) {
				String _ptMgrName = "";
				String ptMgrId = Util.trim(c241m01c.getPtMgrId());
				if (Util.isNotEmpty(ptMgrId)) {
					_ptMgrName = Util
							.trim(userInfoService.getUserName(ptMgrId));
				}
				o.put("_ptMgrName", _ptMgrName);
			}

			// J-108-0268 逾期情形
			String overDueText = "";
			if (meta.getOvQryDt() != null) {
				// ui_lms2411.msg32=本金最長逾期天數：{0}天，利息最長逾期天數：{1}天。引進資料日期:{2}
				overDueText = MessageFormat.format(
						prop_lms2411m01.getProperty("ui_lms2411.msg32"),
						Util.nullToSpace(meta.getCapDays()),
						Util.nullToSpace(meta.getIntDays()),
						TWNDate.toAD(meta.getOvQryDt()));
			}
			if (CrsUtil.isCaseN(meta)
					&& Util.equals(CrsUtil.N009, c241m01c.getItemNo())) {
				o.put("_overDue", "Y");
				o.put("overDueText", overDueText);
			} else if (CrsUtil.isCaseG___N_Detail(meta)
					&& Util.equals(CrsUtil.N009, c241m01c.getItemNo())) {
				o.put("_overDue", "Y");
				o.put("overDueText", overDueText);
			} else {
				o.put("_overDue", "");
				o.put("overDueText", "");
			}

			jsonAraay.add(o);
		}
		map.put(itemType, jsonAraay);
	}

	private String _filter_branchType_staffJob(List<C241M01E> list,
			String branchType, String staffJob) {
		List<C241M01E> filter = retrialService
				.findC241M01E_byBranchTypeStaffJob(list, branchType, staffJob);
		List<String> staffNoStr = new ArrayList<String>();
		if (CollectionUtils.isNotEmpty(filter)) {
			for (C241M01E c241m01e : filter) {
				staffNoStr.add(Util.trim(c241m01e.getStaffNo()) + " "
						+ Util.trim(lmsService.getUserName(c241m01e
								.getStaffNo())));
			}
		}

		return StringUtils.join(staffNoStr, "&nbsp;&nbsp;、<br/>");
	}

	private String _id_name(String raw_id) {
		String id = Util.trim(raw_id);
		if (StringUtils.equalsIgnoreCase(id, "BATCH")) {
			return id;
		} else {
			return Util.trim(id + " "
					+ Util.trim(userInfoService.getUserName(id)));
		}
	}

	private CapAjaxFormResult defaultResult(PageParameters params,
			C241M01A meta, CapAjaxFormResult result) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		C240M01A c240m01a = retrialService.findC240M01A_C241M01A(meta);
		String branchName = c240m01a == null ? "" : branchService
				.getBranchName(c240m01a.getBranchId());
		C241M01A befMeta = retrialService.findC241M01A_bef(meta);

		result.set("branchName", c240m01a.getBranchId() + " " + branchName);
		{
			result.set("bef_oid", befMeta == null ? "" : befMeta.getOid());
			result.set("bef_mainId", befMeta == null ? "" : befMeta.getMainId());
			result.set(
					"bef_diffVer",
					befMeta == null ? "N" : (Util.equals(befMeta.getRptId(),
							meta.getRptId()) ? "N" : "Y"));
			result.set("custId", meta.getCustId());
			result.set("dupNo", meta.getDupNo());
			result.set("staff", meta.getStaff());
			result.set("custName", meta.getCustName());
			result.set("projectNo", meta.getProjectNo());
			result.set("grpCntrNo", meta.getGrpCntrNo());
			result.set("grpCount", meta.getGrpCount());
			result.set("grpEnd", meta.getGrpEnd());
			result.set("docKind", meta.getDocKind()); // {N,G,S,P}
			result.set("c240m01a_mainId",
					c240m01a == null ? "" : c240m01a.getMainId());
			result.set("retrialYN", Util.trim(meta.getRetrialYN()));
			result.set("hasR99", CrsUtil.hasR99(meta) ? "Y" : "N");
			result.set("hasCycle", CrsUtil.hasR99_Cycle(meta) ? "Y" : "N");
			result.set("isRetrialTeam", CrsUtil.isRetrialTeam(user) ? "Y" : "N");
			result.set("is_flowClass_throughBr",
					CrsUtil.is_flowClass_throughBr(sysParameterService,c240m01a) ? "Y" : "N");
			// 若在 受檢端 處理中.EX: 編製中_分行端, 待覆核_分行端
			// 不應直接上傳 elf491
			boolean processingBranchComm = false;
			if (Util.equals(RetrialDocStatusEnum.編製中.getCode(),
					meta.getDocStatus())
					|| Util.equals(RetrialDocStatusEnum.待覆核.getCode(),
							meta.getDocStatus())) {
				processingBranchComm = true;
			}
			result.set("processingBranchComm", processingBranchComm ? "Y" : "N");
			result.set("pa_ym", Util.trim(meta.getPa_ym()));
			result.set("pa_trg", Util.trim(meta.getPa_trg()));
		}
		// required information
		result.set(EloanConstants.PAGE,
				Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));

		result.set("titleBr", c240m01a.getBranchId() + " " + branchName);
		result.set("titleInfo", meta.getCustId() + " " + meta.getDupNo() + "  "
				+ meta.getCustName());
		if (true) {
			String docFmtInfo = "";
			if (CrsUtil.docKindN_since_R11(meta)) {

				if (Util.equals(CrsUtil.DOCFMT_土建融實地覆審, meta.getDocFmt())) {
					docFmtInfo = prop_lms2411m01
							.getProperty("label.docFmt.B.short");
				} else {
					docFmtInfo = prop_lms2411m01
							.getProperty("label.docFmt.A.short");
				}
			}
			result.set("docFmtInfo", docFmtInfo);
		}

		if (true) {
			boolean isLock = false;
			Set<String> docstatus_lockSet = new HashSet<String>();
			{
				docstatus_lockSet.add(RetrialDocStatusEnum.待覆核.getCode());
			}

			if (docstatus_lockSet.contains(Util.trim(meta.getDocStatus()))) {
				isLock = true;
			}
			if (Util.notEquals("Y", meta.getRetrialYN())) {
				isLock = true;
			}
			result.set("lock", isLock);
		}
		return result;
	}

	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params)
			throws CapException {
		return _saveAction(params, "Y");
	}

	private CapAjaxFormResult _saveAction(PageParameters params,
			String tempSave) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y",
				params.getString("allowIncomplete"));

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// ===
		String KEY = "saveOkFlag";

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C241M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try {
				meta = retrialService.findC241M01A_oid(mainOid);
				boolean write2L1 = (retrialService.findC241M01E_first_L1(meta,
						"2") == null);
				String page = params.getString(EloanConstants.PAGE);
				if ("01".equals(page)) {

				} else if ("02".equals(page)) {
					// 實際覆審日,totQuota,totBal 可讓 user 改
					// 因「保證」可能會有美金,所以 totQuota,totBal 開放讓 user 改
					if (CrsUtil.isCaseP(meta)) {
						CapBeanUtil.map2Bean(params, meta,
								new String[] { "retrialDate" });

						meta.setPbAcct(Util.trim(params.getString("pbAcct")));
					} else if (CrsUtil.isCaseS(meta)) {
						CapBeanUtil.map2Bean(params, meta,
								new String[] { "retrialDate" });
					} else {
						CapBeanUtil.map2Bean(params, meta, new String[] {
								"retrialDate", "overdueYN", "totQuota",
								"totBal" });
						retrialService.decideC241M01A_overdueYN(meta,
								retrialService.findC241M01B_c241m01a(meta));
					}
					if (CrsUtil.docKindN_since_R11(meta)) {
						String docFmt_old = meta.getDocFmt();
						String docFmt_new = Util.trim(params
								.getString("docFmt"));
						if (Util.notEquals(docFmt_old, docFmt_new)) {
							meta.setDocFmt(docFmt_new);
							if (true) {
								// sync _c241m01c_latestVer
								lms2400Service.upd_c241m01c_latestVer(meta);
							}
						}
					}
					// ==========================
					if (Util.isEmpty(meta.getTotQuota())) {
						meta.setTotQuota(BigDecimal.ZERO);
					}
					if (Util.isEmpty(meta.getTotBal())) {
						meta.setTotBal(BigDecimal.ZERO);
					}
					// 
					String paVer = retrialService.getPaFormVerByDate(meta.getRetrialDate());
					if (Util.notEquals(meta.getPaVer(), paVer)) {
						// 清除C241M01G
						lms2400Service.deleteC241M01GSByMeta(meta);
					}
					meta.setPaVer(paVer);
				} else if ("03".equals(page)) {
					List<C241M01C> c241m01c_list = retrialService
							.findC241M01C_c241m01a(meta);
					/*
					 * _chkPreReview_N013 _chkResult_N001, _chkResult_N002
					 * _chkText_N001, _chkText_N002
					 */

					int cnt_NY3A_NY3B = 0;
					for (C241M01C c241m01c : c241m01c_list) {
						if (Util.equals(CrsUtil.Y_NY3A, c241m01c.getItemNo())
								|| Util.equals(CrsUtil.Y_NY3B,
										c241m01c.getItemNo())) {
							++cnt_NY3A_NY3B;
						}
					}
					for (C241M01C c241m01c : c241m01c_list) {
						if (true) { // part 1
							String key = "_chkResult_" + c241m01c.getItemNo();
							if (params.containsKey(key)) {
								c241m01c.setChkResult(Util.trim(params
										.getString(key)));
							} else {
								// 可能 server 端有值，但UI不呈現
							}

							// 另判斷N012, 該項目在前端不顯示 []Y []N []K
							if (CrsUtil.docKindN_since_R11(meta)
									&& Util.equals(CrsUtil.N012,
											c241m01c.getItemNo())) {
								String val_NY1A = Util.trim(params
										.getString("_chkResult_"
												+ CrsUtil.Y_NY1A));
								String val_NY1B = Util.trim(params
										.getString("_chkResult_"
												+ CrsUtil.Y_NY1B));
								c241m01c.setChkResult(_chkResult_N012_sub(
										val_NY1A, val_NY1B));
							}

							// 判斷 N026, 自 J-110-0070 上線後，該項目在前端不顯示 []Y []N []K
							if (cnt_NY3A_NY3B > 0
									&& Util.equals(CrsUtil.N026,
											c241m01c.getItemNo())) {
								String val_NY3A = Util.trim(params
										.getString("_chkResult_"
												+ CrsUtil.Y_NY3A));
								String val_NY3B = Util.trim(params
										.getString("_chkResult_"
												+ CrsUtil.Y_NY3B));
								c241m01c.setChkResult(_chkResult_N026_sub(
										val_NY3A, val_NY3B));
							}
						}
						if (true) { // part 2
							String key = "_chkText_" + c241m01c.getItemNo();
							if (params.containsKey(key)) {
								c241m01c.setChkText(Util.truncateString(
										Util.trim(params.getString(key)),
										MAXLEN_C241M01C_CHKTEXT));
							} else {
								// 可能 server 端有值，但UI不呈現
							}
						}
						if (true) { // part 3
							String key = "_chkPreReview_"
									+ c241m01c.getItemNo();
							if (params.containsKey(key)) {
								c241m01c.setChkPreReview(Util.trim(params
										.getString(key)));
							} else {
								// 可能 server 端有值，但UI不呈現
							}
						}
						// ---
						retrialService.save(c241m01c);
					}
				} else if ("04".equals(page)) {
					if (CrsUtil.isCaseS(meta)) {
						CapBeanUtil.map2Bean(params, meta, new String[] {
								"conFlag", "condition", "conFlag2A",
								"conFlag2B", "conFlag2C" });

						meta.setCondition(Util.truncateString(
								meta.getCondition(), MAXLEN_C241M01A_CONDITION));

					} else {
						CapBeanUtil.map2Bean(params, meta, new String[] {
								"conFlag", "condition", "branchComm" });

						meta.setCondition(Util.truncateString(
								meta.getCondition(), MAXLEN_C241M01A_CONDITION));
						meta.setBranchComm(Util.truncateString(
								meta.getBranchComm(),
								MAXLEN_C241M01A_BRANCHCOMM));
					}
				} else if ("05".equals(page)) {
					CapBeanUtil.map2Bean(params, meta,
							new String[] { "needPa" });
					Set<C241M01G> c241m01gs = meta.getC241m01gs();
					String paVer = meta.getPaVer();
					String[] fieldNames = retrialService.getPaCol(paVer, "1");
					String[] ItemTypeYNs = retrialService.getPaCol(paVer, "2");
					String[] ItemTypeCnts = retrialService.getPaCol(paVer, "3");
					for (String fieldName : fieldNames) {
						String itemName = fieldName;
						String itemType = params.getString(fieldName
								+ "_itemType", "");
						String dscr = params.getString(fieldName + "_dscr", "");
						String itemYn = params.getString(fieldName + "_itemYn",
								"");
						String itemCnt = params.getString(fieldName
								+ "_itemCnt", "0");

						C241M01G c241m01g = null;
						for (C241M01G tmp : c241m01gs) {
							if (fieldName.equals(tmp.getItemName())) {
								c241m01g = tmp;
								break;
							}
						}
						if (c241m01g == null) {
							c241m01g = new C241M01G();
							c241m01g.setMainId(meta.getMainId());
							c241m01g.setItemName(itemName);
							c241m01g.setCreator(user.getUserId());
							c241m01g.setCreateTime(CapDate
									.getCurrentTimestamp());
							c241m01gs.add(c241m01g);
						}

						if (ArrayUtils
								.contains(ItemTypeYNs, itemName)) {
							c241m01g.setItemType("YN");
							itemType = "YN";
						} else if (ArrayUtils.contains(ItemTypeCnts,
								itemName)) {
							c241m01g.setItemType("CNT");
							itemType = "CNT";
						} else {
							c241m01g.setItemType(itemType);
						}
						c241m01g.setDscr(dscr);
						c241m01g.setItemYn(Util.equals(itemType, "YN") ? itemYn
								: null);
						c241m01g.setItemCnt(Util.equals(itemType, "CNT") ? NumberUtils
								.toInt(itemCnt) : null);
						c241m01g.setUpdater(user.getUserId());
						c241m01g.setUpdateTime(CapDate.getCurrentTimestamp());

					}
					if (!c241m01gs.isEmpty()) {
						meta.setC241m01gs(c241m01gs);
						meta.setPaVer(paVer);
					}
				}

				retrialService.save(meta);

				List<String> promptMsg_list = new ArrayList<String>();
				// ===
				/*
				 * prompt : result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
				 * errMsg.toString()); error: throw new
				 * CapMessageException(errMsg, getClass());
				 */
				if (Util.notEquals("Y",
						SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
					// 在tempSave<>Y,若有未填欄位,丟 CapMessageException, 讓
					// saveOkFlag==false

					String msg = lms2400Service.checkIncompleteMsg(meta);
					if (Util.isNotEmpty(msg)) {
						if (allowIncomplete) {
							result.set("IncompleteMsg", msg);
						} else {
							throw new CapMessageException(msg, getClass());
						}
					}

					if (write2L1
							&& (RetrialDocStatusEnum.區中心_編製中 == RetrialDocStatusEnum
									.getEnum(meta.getDocStatus()))) {
						C241M01E c241m01e = retrialService.setC241M01E_L1(meta,
								"2", user.getUserId(), user.getUnitNo());
						retrialService.save(c241m01e);
					}

					if (Util.equals(meta.getDocKind(), CrsUtil.DOCKIND_N)
							&& Util.equals(meta.getDocFmt(), CrsUtil.DOCFMT_一般)
							&& Util.equals(meta.getRealCkFg(), "Y")
							&& (meta.getRetrialDate() != null)) {
						Date baseDate = meta.getLastRealDt();
						if (baseDate == null) {
							baseDate = CapDate.parseDate(CrsUtil.R11_ON_DATE);
						}

						int addedMonth = 7;

						Date calcDate = CapDate.addMonth(baseDate, addedMonth);

						if (LMSUtil.cmpDate(meta.getRetrialDate(), ">",
								calcDate)) {
							// ui_lms2411.msg29=實地覆審基準日加七個月已超過本次覆審日期，請確認本次是否需要實地覆審
							promptMsg_list.add(prop_lms2411m01
									.getProperty("ui_lms2411.msg29"));
						}
					}
				}
				result.set(KEY, true);

				if (promptMsg_list.size() > 0) {
					String promptMsg = StringUtils.join(promptMsg_list,
							UtilConstants.Mark.HTMLBR);
					if (clsService
							.is_function_on_codetype("chkC241M01A_DOCFMT")) {
						result.set(CapConstants.AJAX_NOTIFY_MESSAGE, promptMsg);
					} else {
						result.set("promptMsg", promptMsg);
					}

				}
			} catch (Exception e) {
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}
		}

		result.add(query(params));

		return result;
	}

	private String _chkResult_N012_sub(String val_NY1A, String val_NY1B) {
		String valA = "K";
		String valB = "K";
		if (Util.isNotEmpty(Util.trim(val_NY1A))) {
			valA = Util.trim(val_NY1A);
		}
		if (Util.isNotEmpty(Util.trim(val_NY1B))) {
			valB = Util.trim(val_NY1B);
		}

		String r = "";
		if (Util.equals("K", valA) && Util.equals("K", valB)) {
			r = "K";
		} else {
			if (Util.equals("N", valA) || Util.equals("N", valB)) {
				r = "N";
			} else {
				r = "Y";
			}
		}

		return r;
	}

	private String _chkResult_N026_sub(String val_NY3A, String val_NY3B) {
		return _chkResult_N012_sub(val_NY3A, val_NY3B);
	}

	private boolean _errorBranchComm(C241M01A meta) {
		return Util.isEmpty(Util.trim(meta.getBranchComm()));
	}

	public IResult check_bef_btnSend(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C241M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findC241M01A_oid(mainOid);

			if (_errorBranchComm(meta)) {
				// 請輸入受檢單位洽辦情形
				throw new CapMessageException(
						prop_lms2411m01.getProperty("ui_lms2411.msg09")
								+ prop_lms2411m01
										.getProperty("C241M01A.branchComm"),
						getClass());
			}
		}
		return result;
	}

	/**
	 * 呈主管覆核
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		boolean addMetaDesc = Util.equals("Y",
				Util.trim(params.getString("addMetaDesc")));
		C241M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findC241M01A_oid(mainOid);
			C240M01A c240m01a = retrialService.findC240M01A_C241M01A(meta);

			List<C241M01E> c241m01e_list = retrialService
					.findC241M01E_c241m01a(meta);
			RetrialDocStatusEnum docStatusEnum = RetrialDocStatusEnum
					.getEnum(meta.getDocStatus());

			boolean no_A_L1 = CollectionUtils
					.isEmpty(retrialService.findC241M01E_byBranchTypeStaffJob(
							c241m01e_list, "1", "L1"));
			boolean no_A_L4 = CollectionUtils
					.isEmpty(retrialService.findC241M01E_byBranchTypeStaffJob(
							c241m01e_list, "1", "L4"));
			boolean no_A_L5 = CollectionUtils
					.isEmpty(retrialService.findC241M01E_byBranchTypeStaffJob(
							c241m01e_list, "1", "L5"));

			boolean no_B_L1 = CollectionUtils
					.isEmpty(retrialService.findC241M01E_byBranchTypeStaffJob(
							c241m01e_list, "2", "L1"));
			boolean no_B_L4 = CollectionUtils
					.isEmpty(retrialService.findC241M01E_byBranchTypeStaffJob(
							c241m01e_list, "2", "L4"));
			boolean no_B_L5 = CollectionUtils
					.isEmpty(retrialService.findC241M01E_byBranchTypeStaffJob(
							c241m01e_list, "2", "L5"));

			String errMsg = "";
			if (Util.equals("to_待覆核_覆審組", decisionExpr)) {
				errMsg = lms2400Service.checkIncompleteMsg(meta);

				if (Util.isEmpty(errMsg)) {
					Set<String> msgSet = new LinkedHashSet<String>();
					// 不論何種 status,都檢查 覆審組 的簽章欄
					// J-111-0622_05097_B1002 Web
					// e-Loan配合本行授信覆審作業須知111.12.1修訂，修改E-Loan系統企金授信覆審作業系統
					_lackSign(msgSet, c240m01a, meta.getOwnBrId(), "B",
							no_B_L1, no_B_L4, no_B_L5, meta);

					errMsg = StringUtils.join(msgSet, "<br/>");
				}

			} else if (Util.equals("to_編製中_分行端", decisionExpr)) {
				errMsg = lms2400Service.checkIncompleteMsg(meta);
			} else if (Util.equals("呈主管", decisionExpr)) {
				// 受檢單位 經辦 → 呈主管
				Set<String> msgSet = new LinkedHashSet<String>();

				if (_errorBranchComm(meta)) {
					// 請輸入受檢單位洽辦情形
					msgSet.add(prop_lms2411m01.getProperty("ui_lms2411.msg09")
							+ prop_lms2411m01
									.getProperty("C241M01A.branchComm"));
				}
				// J-111-0622_05097_B1002 Web
				// e-Loan配合本行授信覆審作業須知111.12.1修訂，修改E-Loan系統企金授信覆審作業系統
				_lackSign(msgSet, c240m01a, meta.getOwnBrId(), "A", no_A_L1,
						no_A_L4, no_A_L5, meta);

				errMsg = StringUtils.join(msgSet, "<br/>");
			} else if (Util.equals("to_已覆核未核定", decisionExpr)) {
				// 受檢單位 主管覆核

				// 檢查經辦和主管是否為同一人
				if (userId_in_L1(user.getUserId(),
						retrialService.findC241M01E_byBranchTypeStaffJob(
								c241m01e_list, "1", "L1"))) {
					errMsg = RespMsgHelper.getMessage("EFD0053");
				}

			} else if (Util.equals("to_已覆核已核定", decisionExpr)) {

				Set<String> msgSet = new LinkedHashSet<String>();

				// 不論何種 status,都檢查 覆審組 的簽章欄
				// J-111-0622_05097_B1002 Web
				// e-Loan配合本行授信覆審作業須知111.12.1修訂，修改E-Loan系統企金授信覆審作業系統
				_lackSign(msgSet, c240m01a, meta.getOwnBrId(), "B", no_B_L1,
						no_B_L4, no_B_L5, meta);

				if (docStatusEnum == RetrialDocStatusEnum.區中心_編製中) {
					// 在覆審組_編製中 → 已覆核已核定
				} else if (docStatusEnum == RetrialDocStatusEnum.已覆核未核定) {
					// 已覆核未核定 → 已覆核已核定. 會分[UI,BATCH]. 這裡只有 UI
					// J-111-0622_05097_B1002 Web
					// e-Loan配合本行授信覆審作業須知111.12.1修訂，修改E-Loan系統企金授信覆審作業系統
					_lackSign(msgSet, c240m01a, meta.getOwnBrId(), "A",
							no_A_L1, no_A_L4, no_A_L5, meta);
				} else {
					// 其它狀況(受檢單位待覆核, 覆審組就按上傳)
				}

				if (CrsUtil.hasR99(meta) && CrsUtil.hasR99_Cycle(meta) == false) {
					/*
					 * ui_lms2411.msg09=請輸入 C241M01A.specifyCycle=99類覆審週期
					 */
					msgSet.add(prop_lms2411m01.getProperty("ui_lms2411.msg09")
							+ prop_lms2411m01
									.getProperty("C241M01A.specifyCycle"));
				}

				errMsg = StringUtils.join(msgSet, "<br/>");
			}

			if (Util.isNotEmpty(errMsg)) {
				if (addMetaDesc) {
					errMsg = (meta.getCustId() + " " + meta.getCustName()
							+ "<br/>" + errMsg);
				}
				throw new CapMessageException(errMsg, getClass());
			}

			flowSimplifyService.flowNext(meta.getOid(), decisionExpr);

			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(),
					user.getUserId());
		}
		return defaultResult(params, meta, result);
	}

	private boolean userId_in_L1(String userId, List<C241M01E> c241m01e_list) {
		boolean r = false;
		for (C241M01E c241m01e : c241m01e_list) {
			if (Util.equals(userId, c241m01e.getStaffNo())) {
				r = true;
			}
		}
		return r;
	}

	private void _lackSign(Set<String> msgSet, C240M01A c240m01a,
			String c241m01a_ownBrId, String signRole, boolean lackL1,
			boolean lackL4, boolean lackL5, C241M01A c241m01a) {
		if (Util.equals("A", signRole)) {
			_lackSignHelper(msgSet, lackL1, signRole, "reviewBrn.A.appraiser");
			_lackSignHelper(msgSet, lackL4, signRole, "reviewBrn.A.reChecker");
			_lackSignHelper(msgSet, lackL5, signRole, "reviewBrn.A.manager");
		} else if (Util.equals("B", signRole)) {
			_lackSignHelper(msgSet, lackL1, signRole, "reviewBrn.B.appraiser");

			if (LMSUtil.isSpecialBranch(c241m01a_ownBrId)
					|| CrsUtil.is_flowClass_throughBr(sysParameterService,c240m01a)) {
				_lackSignHelper(msgSet, lackL4, signRole,
						"reviewBrn.B.reChecker");
			}
			// 目前007,025,201 分行, 要檢查 覆審組的L5 為必填
			if (LMSUtil.isSpecialBranch(c241m01a_ownBrId)) {
				// J-111-0622_05097_B1002 Web
				// e-Loan配合本行授信覆審作業須知111.12.1修訂，修改E-Loan系統企金授信覆審作業系統
				if (!retrialService.isSpecialBranchChgTo931_crs(c241m01a)) {
					_lackSignHelper(msgSet, lackL5, signRole,
							"reviewBrn.B.manager");
				}

			}
		}

	}

	private void _lackSignHelper(Set<String> msgSet, boolean bLack,
			String signRole, String propKey) {
		if (bLack) {
			String roleDesc = "";
			if (Util.equals("A", signRole)) {
				roleDesc = prop_lms2411m01.getProperty("reviewBrn.A");
			} else if (Util.equals("B", signRole)) {
				roleDesc = prop_lms2411m01.getProperty("reviewBrn.B");
			}
			/*
			 * ui_lms2411.msg09=請輸入 label.signature=簽章欄 reviewBrn.A=受檢單位
			 */
			String s = prop_lms2411m01.getProperty("ui_lms2411.msg09")
					+ prop_lms2411m01.getProperty("label.signature") + "("
					+ roleDesc + ")" + prop_lms2411m01.getProperty(propKey);
			msgSet.add(s);
		}
	}

	/**
	 * 刪除上傳檔案
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteUploadFile(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
		// L224與聯徵查詢結果
		String fid = params.getString("fileOid");
		// 後台管理->系統設定維護->LMS_RETRIAL_CAN_DEL_RPA_FILE 覆審報告表可以刪除RPA產生的資料
		String LMS_RETRIAL_CAN_DEL_RPA_FILE = Util.trim(lmsService
				.getSysParamDataValue("LMS_RETRIAL_CAN_DEL_RPA_FILE"));

		DocFile doc = docFileService.findByOidAndSysId(fid, "LMS");
		if (doc != null
				&& Util.equals(Util.trim(doc.getFlag()), "R")
				&& ("crs".equals(Util.trim(doc.getFieldId())) || "lrs"
						.equals(Util.trim(doc.getFieldId())))) {
			// RPA產生的檔案
			boolean canDel = false;
			if (Util.notEquals(LMS_RETRIAL_CAN_DEL_RPA_FILE, "")
					&& Util.notEquals(LMS_RETRIAL_CAN_DEL_RPA_FILE, "XXX")) {
				String[] item = LMS_RETRIAL_CAN_DEL_RPA_FILE.split(",");
				for (String xx : item) {
					if (Util.equals(Util.trim(user.getUnitNo()), xx)) {
						canDel = true;
						break;
					}
				}
			}

			if (!canDel) {
				throw new CapMessageException("覆審人員不可刪除RPA產生之檔案", getClass());
			}

		}

		if (retrialService.delfile(fid)) {
			// ...
		}
		return query(params);
	}

	/**
	 * 起flow
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult checkFlow(PageParameters params)
			throws CapException {

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String act = Util.trim(params.getString("act"));
		String forceFlag = Util.trim(params.getString("forceFlag"));
		String targetDocStatus = Util.trim(params.getString("targetDocStatus"));
		boolean passedFlag = false;

		if (Util.isNotEmpty(mainOid)) {
			C241M01A meta = retrialService.findC241M01A_oid(mainOid);

			if (Util.equals("initFlow", act)) {
				passedFlag = true;

				if (Util.isEmpty(meta.getDocStatus())
						&& Util.equals("Y", meta.getRetrialYN())
						&& CrsUtil.isRetrialTeam(user)) {
					C240M01A c240m01a = retrialService
							.findC240M01A_C241M01A(meta);
					if (c240m01a != null
							&& Util.equals(
									RetrialDocStatusEnum.已產生覆審名單報告檔.getCode(),
									c240m01a.getDocStatus())) {
						if (Util.equals(user.getSsoUnitNo(),
								c240m01a.getOwnBrId())) {
							flowSimplifyService.flowStart(
									LMS2411Flow.LMS2411FLOW, meta.getOid(),
									user.getUserId(), user.getUnitNo());
						} else {
							// 覆審組開啟非自己單位的工作底稿，不啟動 workflow,不進到編製中
						}

					}
				}
			} else if (Util.equals("end_to_01A", act)) {
				C241M01E c241m01e = retrialService.findC241M01E_first_L1(meta,
						"2");
				if (c241m01e != null
						&& Util.notEquals(c241m01e.getStaffNo(),
								user.getUserId())) {
					if (Util.equals("Y", forceFlag)) {
						// 不卡 userId
					} else {
						// ui_lms2411.msg08=本案由{0}覆審，不可再由您異動
						throw new CapMessageException(
								MessageFormat.format(
										prop_lms2411m01
												.getProperty("ui_lms2411.msg08"),
										Util.isNotEmpty(c241m01e.getStaffName()) ? c241m01e
												.getStaffName() : c241m01e
												.getStaffNo()), getClass());
					}
				}

				if (CrsUtil.isCaseN(meta)) {
					ELF491 elf491 = misELF491Service.findByPk(
							meta.getOwnBrId(), meta.getCustId(),
							meta.getDupNo());
					if (elf491 != null
							&& CrsUtil.isNOT_null_and_NOTZeroDate(elf491
									.getElf491_lrdate())) {
						if (!LMSUtil.cmpDate(elf491.getElf491_lrdate(), "==",
								meta.getRetrialDate())) {
							// ui_lms2411.msg11=中心的已覆審日期為{0}，本次覆審日期為{1}，不可異動
							throw new CapMessageException(MessageFormat.format(
									prop_lms2411m01
											.getProperty("ui_lms2411.msg11"),
									Util.getDate(elf491.getElf491_lrdate()),
									Util.getDate(meta.getRetrialDate())),
									getClass());
						}
					}
				}

				if (CrsUtil.isCaseS(meta)
						&& Util.equals(targetDocStatus,
								RetrialDocStatusEnum.編製中.getCode())) {
					// ui_lms2411.msg31=防杜代辦覆審報告表，流程不需至「受檢單位」
					throw new CapMessageException(
							prop_lms2411m01.getProperty("ui_lms2411.msg31"),
							getClass());
				}

				passedFlag = true;
				// 要把 TEMPSAVE_RUN 指定為 N
				C240M01A c240m01a = retrialService.findC240M01A_C241M01A(meta);
				if (CrsUtil.is_flowClass_throughBr(sysParameterService,c240m01a)
						&& Util.equals(targetDocStatus,
								RetrialDocStatusEnum.編製中.getCode())) {
					Map<String, Object> map = new HashMap<String, Object>();
					map.put(LMS2411Flow.ZINITPARAM, "to010");
					// 退到[受檢單位]編製中
					flowSimplifyService.flowStart(LMS2411Flow.LMS2411FLOW,
							meta.getOid(), user.getUserId(), user.getUnitNo(),
							map);
				} else if (CrsUtil.is_flowClass_throughBr(sysParameterService,c240m01a)
						&& Util.equals(targetDocStatus,
								RetrialDocStatusEnum.已覆核未核定.getCode())) {
					// 2022/04/18 授審處連喬凱來電 分處對覆審報告表有回頭打考評表之需求
					// 1. 「已覆核已核定」之覆審報告表可以退為「已覆核未核定」修改_限制為有考評表且有傳送至分行的覆審報告表
					// 2. 不限上傳者本人，任何人都可以修改
					Map<String, Object> map = new HashMap<String, Object>();
					map.put(LMS2411Flow.ZINITPARAM, "to050");
					// 退到[已覆核未核定]
					flowSimplifyService.flowStart(LMS2411Flow.LMS2411FLOW,
							meta.getOid(), user.getUserId(), user.getUnitNo(),
							map);
				} else {
					if (true) {
						List<String> l4List = new ArrayList<String>();
						List<String> l5List = new ArrayList<String>();
						// 007提出，在退回時，要把簽章欄清掉
						// 列印時才不會印出 簽章欄
						String branchType = "2";
						retrialService.saveC241M01E_L1L4L5(meta, branchType,
								user, l4List, l5List);
					}
					// 退到[覆審單位]編製中
					flowSimplifyService.flowStart(LMS2411Flow.LMS2411FLOW,
							meta.getOid(), user.getUserId(), user.getUnitNo());
				}

				retrialService.saveReInitFlow(meta);// 由編製完成 → 編製中，要再存一次。目的： 把
													// bTempData 的資料刪掉
			}
		}
		result.set("passedFlag", passedFlag ? "Y" : "N");

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult openPageParam(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String mainId = "";
		String mainDocStatus = "";
		C241M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findC241M01A_oid(mainOid);

			mainId = Util.trim(meta.getMainId());
			mainDocStatus = Util.trim(meta.getDocStatus());
		}

		result.set(EloanConstants.MAIN_OID, mainOid);
		result.set(EloanConstants.MAIN_ID, mainId);
		result.set(EloanConstants.MAIN_DOC_STATUS, mainDocStatus);
		return result;
	}

	/**
	 * 重引帳務
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult importLNSingle(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C241M01A c241m01a = retrialService.findC241M01A_oid(mainOid);
		C240M01A c240m01a = retrialService.findC240M01A_C241M01A(c241m01a);

		String realCkFg_old = c241m01a.getRealCkFg();
		retrialService.importLNtoC241M01B_single(c240m01a, c241m01a);

		result.set("show_msg10", "N");// 上傳後,仍可重引帳務
		result.set("lnDataDate", c241m01a.getLnDataDate());
		result.set("overdueYN", c241m01a.getOverdueYN());

		if (CrsUtil.docKindN_since_R11(c241m01a)
				&& Util.notEquals(c241m01a.getRealCkFg(), realCkFg_old)) {
			result.set("realCkFg", c241m01a.getRealCkFg());
			if (Util.equals(c241m01a.getRealCkFg(), "Y")) {
				result.set("docFmt", CrsUtil.DOCFMT_土建融實地覆審);
			} else {
				result.set("docFmt", CrsUtil.DOCFMT_一般);
			}
		}
		result.set("totQuota", NumConverter.addComma(c241m01a.getTotQuota()));
		result.set("totBal", NumConverter.addComma(c241m01a.getTotBal()));
		return defaultResult(params, c241m01a, result);
	}

	/**
	 * 覆審項目預設值
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult c241m01c_defaultVal(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C241M01A c241m01a = retrialService.findC241M01A_oid(mainOid);
		Map<String, String> map = retrialService
				.get_c241m01c_defaultVal(c241m01a);
		result.set("defVal", new CapAjaxFormResult(map));
		return defaultResult(params, c241m01a, result);
	}

	@DomainAuth(AuthType.Modify)
	public IResult c241m01c_latestVer(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C241M01A c241m01a = retrialService.findC241M01A_oid(mainOid);

		lms2400Service.upd_c241m01c_latestVer(c241m01a);

		return defaultResult(params, c241m01a, result);
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getSignList(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String signRole = Util.trim(params.getString("signRole"));
		C241M01A c241m01a = retrialService.findC241M01A_oid(mainOid);
		C240M01A c240m01a = retrialService.findC240M01A_C241M01A(c241m01a);
		String chooseUnitNo = "";
		// =====================
		if (c240m01a != null) {
			if (Util.equals("A", signRole)) {
				// 受檢單位
				chooseUnitNo = c240m01a.getBranchId();
			} else if (Util.equals("B", signRole)) {
				// 覆審單位
				chooseUnitNo = c240m01a.getOwnBrId();
			}
		}

		SignEnum[] signs_l4 = { SignEnum.甲級主管, SignEnum.乙級主管, SignEnum.首長,
				SignEnum.單位主管 };
		SignEnum[] signs_l5 = { SignEnum.甲級主管, SignEnum.首長, SignEnum.單位主管 };
		Map<String, String> l4_list = userInfoService.findByBrnoAndSignId(
				chooseUnitNo, signs_l4);
		Map<String, String> l5_list = userInfoService.findByBrnoAndSignId(
				chooseUnitNo, signs_l5);

		IBranch ibranch = branchService.getBranch(chooseUnitNo);
		if (ibranch != null) {
			String l5Id = Util.trim(ibranch.getBrnMgr());
			l5_list.put(l5Id, Util.trim(userInfoService.getUserName(l5Id)));
		}

		result.set("chooseUnitNo", chooseUnitNo);
		result.set("l4_list", new CapAjaxFormResult(l4_list));
		result.set("l5_list", new CapAjaxFormResult(l5_list));
		if (Util.equals("B", signRole)
				&& CrsUtil.is_flowClass_throughBr(sysParameterService,c240m01a)
				&& Util.equals(RetrialDocStatusEnum.已覆核未核定.getCode(),
						c241m01a.getDocStatus())) {
			// 若已有 L4, L5, 不跳出
			List<C241M01E> c241m01e_list = retrialService
					.findC241M01E_c241m01a(c241m01a);

			if (c241m01e_list != null && c241m01e_list.size() > 0) {
				boolean has_B_L4 = CollectionUtils.isNotEmpty(retrialService
						.findC241M01E_byBranchTypeStaffJob(c241m01e_list, "2",
								"L4"));
				boolean has_B_L5 = CollectionUtils.isNotEmpty(retrialService
						.findC241M01E_byBranchTypeStaffJob(c241m01e_list, "2",
								"L5"));
				if (has_B_L4 || has_B_L5) {
					result.set("canSkip", "Y");
				}
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSignList(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		String key = "saveSignFlag";
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(key, "N");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String signRole = Util.trim(params.getString("signRole"));

		List<String> l4List = _staffStr(params.getString("l4Arr"));
		List<String> l5List = _staffStr(params.getString("l5Arr"));
		C241M01A meta = retrialService.findC241M01A_oid(mainOid);
		// =====================
		String branchType = "";
		if (Util.equals("A", signRole)) {
			// 受檢單位
			branchType = "1";
		} else if (Util.equals("B", signRole)) {
			// 覆審單位
			branchType = "2";
		}
		retrialService.saveC241M01E_L1L4L5(meta, branchType, user, l4List,
				l5List);
		// ==========
		_signInfo(result, meta);
		result.set(key, "Y");
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveSpecifyCycle(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		String key = "saveSpecifyCycleFlag";
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(key, "N");

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String specifyCycle = Util.trim(params.getString("specifyCycle"));

		if (Util.isEmpty(specifyCycle)) {
			/*
			 * ui_lms2411.msg09=請輸入 C241M01A.specifyCycle=99類覆審週期
			 */
			throw new CapMessageException(
					prop_lms2411m01.getProperty("ui_lms2411.msg09")
							+ prop_lms2411m01
									.getProperty("C241M01A.specifyCycle"),
					getClass());
		}
		C241M01A meta = retrialService.findC241M01A_oid(mainOid);
		meta.setSpecifyCycle(specifyCycle);
		retrialService.save(meta);
		// ==========
		// 若在 panel02,更改 UI 上的 specifyCycle
		_specifyCycle(result, meta);

		result.set(key, "Y");
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getBranchComm(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		C241M01A meta = retrialService.findC241M01A_oid(mainOid);
		result.set("reg_branchComm", meta.getBranchComm());
		return defaultResult(params, meta, result);
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveBranchComm(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);

		C241M01A meta = retrialService.findC241M01A_oid(mainOid);

		CapBeanUtil.map2Bean(params, meta, new String[] { "branchComm" });
		if (Util.isNotEmpty(Util.trim(meta.getBranchComm()))) {
			// 若有輸入受檢單位洽辦情形，不得＜
			if (Util.trim(meta.getBranchComm()).getBytes().length < 4) {
				throw new CapMessageException(
						prop_lms2411m01.getProperty("ui_lms2411.msg24"),
						getClass());
			}
		}
		retrialService.save(meta);
		// ==========
		result.set("branchComm", meta.getBranchComm());
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getP_SellerBuyerInfo(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		C241M01A meta = retrialService.findC241M01A_oid(mainOid);

		_setResult_P_SellerBuyerInfo(result, meta, true);

		return defaultResult(params, meta, result);
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveP_SellerBuyerInfo(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);

		C241M01A meta = retrialService.findC241M01A_oid(mainOid);

		_setModel_P_SellerBuyerInfo(params, meta, true);

		// ==========
		List<String> errMsgList = new ArrayList<String>();
		if (Util.isEmpty(Util.trim(meta.getCustId()))) {
			errMsgList.add(prop_lms2411m01.getProperty("ui_lms2411.msg09")
					+ prop_lms2411m01.getProperty("label.sellerInfo"));
		}
		if (Util.isEmpty(Util.trim(meta.getDupNo()))) {
			errMsgList.add(prop_lms2411m01.getProperty("ui_lms2411.msg09")
					+ prop_lms2411m01.getProperty("label.sellerInfo") + "-"
					+ prop_lms2411m01.getProperty("C241M01A.dupNo"));
		}

		String errMsg = StringUtils.join(errMsgList, "、");
		if (Util.isNotEmpty(errMsg)) {
			throw new CapMessageException(errMsg, getClass());
		}

		retrialService.save(meta);

		// ==========
		// panel
		_setResult_P_SellerBuyerInfo(result, meta, false);

		return defaultResult(params, meta, result);
	}

	private List<String> _staffStr(String src) {
		List<String> list = new ArrayList<String>();
		for (String raw_staffNo : Util.trim(src).split("\\|")) {
			String s = Util.trim(raw_staffNo);
			if (Util.isEmpty(s)) {
				continue;
			}
			if (list.contains(s)) {
				continue;
			}
			list.add(s);
		}
		return list;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryLNDetail(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String c241m01b_oid = Util.trim(params.getString("c241m01b_oid"));
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		C241M01A meta = retrialService.findC241M01A_oid(mainOid);
		C241M01B c241m01b = retrialService.findC241M01B_oid(c241m01b_oid);
		if (c241m01b == null) {
			throw new CapMessageException("以 [" + c241m01b_oid + "] 查無 授信帳務資料",
					getClass());
		}

		Map<String, String> lnTypeMap = retrialService.get_crs_prodKindMap();
		Map<String, String> lnBusinessMap = retrialService
				.get_crs_lnPurposeMap();
		LMSUtil.addMetaToResult(result, c241m01b, new String[] { "lnDataDate",
				"ynReview", "dateOfReview", "quotaType", "subjectNo",
				"subjectName", "actcd", "privateNo", "quotaNo", "reVolve",
				"loanNo", "lcNo", "quotaCurr", "useFDate", "useEDate",
				"balCurr", "loanFDate", "loanEDate", "sQuotaCurr", "sBalCurr",
				"LNF020CrtDate", "LNF030CrtDate", "overDueDate",
				"guaranteeKind", "guaranteeName", "majorMemo", "estCurr",
				"loanCurr", "coBorrower", "guarantor" });
		// 呈現到 時:分:秒
		result.set(
				"lnDataDate",
				c241m01b.getLnDataDate() == null ? "" : (S_FORMAT
						.format(new Date(c241m01b.getLnDataDate().getTime()))));
		result.set(
				"lnType",
				c241m01b.getLnType()
						+ (lnTypeMap.containsKey(c241m01b.getLnType()) ? " - "
								+ lnTypeMap.get(c241m01b.getLnType()) : ""));
		result.set(
				"lnBusiness",
				c241m01b.getLnBusiness()
						+ (lnBusinessMap.containsKey(c241m01b.getLnBusiness()) ? " - "
								+ lnBusinessMap.get(c241m01b.getLnBusiness())
								: ""));
		result.set("quotaAmt", NumConverter.addComma(c241m01b.getQuotaAmt()));
		result.set("balAmt", NumConverter.addComma(c241m01b.getBalAmt()));
		result.set("sQuotaAmt", NumConverter.addComma(c241m01b.getSQuotaAmt()));
		result.set("sBalAmt", NumConverter.addComma(c241m01b.getSBalAmt()));
		result.set("estAmt", NumConverter.addComma(c241m01b.getEstAmt()));
		result.set("loanAmt", NumConverter.addComma(c241m01b.getLoanAmt()));
		result.set("haveBeenUpload", CrsUtil.haveBeenUpload(meta) ? "Y" : "N");
		if (Util.equals(meta.getCustId(), c241m01b.getCustId())
				&& Util.equals(meta.getDupNo(), c241m01b.getDupNo())) {
			result.set("custInfo", meta.getCustId() + " " + meta.getDupNo()
					+ " " + meta.getCustName());
		} else {
			result.set("custInfo",
					c241m01b.getCustId() + " " + c241m01b.getDupNo() + " ");
		}

		CapAjaxFormResult addt = new CapAjaxFormResult();
		addt.set(
				"quotaType",
				new CapAjaxFormResult(retrialService
						.get_crs_c241m01bQuotaType()));
		addt.set("lnType", new CapAjaxFormResult(lnTypeMap));
		addt.set("lnBusiness", new CapAjaxFormResult(lnBusinessMap));
		result.set("addt", addt);
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult checkC241M01A(PageParameters params)
			throws CapException, IOException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);

		C241M01A c241m01a = retrialService.findC241M01A_oid(Util.trim(oid));

		// check doc status
		result.set("isCheckOk", "N");
		if (Util.equals(c241m01a.getDocStatus(),
				RetrialDocStatusEnum.區中心_編製中.getCode())) {

			// check C241M01B 資料完整性
			String errMsg = this.lms2400Service.checkIncompleteMsg(c241m01a);
			boolean pass_R99_check = false;
			if (CrsUtil.hasR99(c241m01a)) {
				pass_R99_check = CrsUtil.hasR99_Cycle(c241m01a);
			} else {
				pass_R99_check = true;
			}

			if (CapString.isEmpty(errMsg) && pass_R99_check) {
				result.set("c241m01aOid", oid);
				result.set("isCheckOk", "Y");
			}
		}

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult checkC241M01AFromAreaWaitApproveToEnd(PageParameters params) throws CapException, IOException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);

		C241M01A c241m01a = retrialService.findC241M01A_oid(Util.trim(oid));
		result.set("isCheckOk", "N");
		if (Util.equals(c241m01a.getDocStatus(),
				RetrialDocStatusEnum.區中心_待覆核.getCode())) {
			if (CrsUtil.isCaseS(c241m01a)) {
				result.set("c241m01aOid", oid);
				result.set("isCheckOk", "Y");
			} else {
				// 其它的 DocKind 未開放
			}
		}

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveLNDetail(PageParameters params)
			throws CapException {

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		String key = "saveLNFlag";
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(key, "N");

		String c241m01b_oid = Util.trim(params.getString("c241m01b_oid"));
		C241M01B c241m01b = retrialService.findC241M01B_oid(c241m01b_oid);
		CapBeanUtil.map2Bean(params, c241m01b, new String[] { "ynReview",
				"quotaType", "guaranteeName", "majorMemo", "estAmt", "loanAmt",
				"coBorrower", "guarantor" });
		c241m01b.setGuaranteeName(Util.truncateString(
				c241m01b.getGuaranteeName(), MAXLEN_C241M01B_GUARANTEENAME));
		c241m01b.setMajorMemo(Util.truncateString(c241m01b.getMajorMemo(),
				MAXLEN_C241M01B_MAJORMEMO));

		if (Util.isEmpty(c241m01b.getEstAmt())) {
			c241m01b.setEstAmt(BigDecimal.ZERO);
		}
		if (Util.isEmpty(c241m01b.getLoanAmt())) {
			c241m01b.setLoanAmt(BigDecimal.ZERO);
		}
		c241m01b.setCoBorrower(Util.truncateString(c241m01b.getCoBorrower(),
				MAXLEN_C241M01B_COBORROWER));
		c241m01b.setGuarantor(Util.truncateString(c241m01b.getGuarantor(),
				MAXLEN_C241M01B_GUARANTOR));
		retrialService.save(c241m01b);

		result.set(key, "Y");
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult impEllngtee(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		String c241m01b_oid = Util.trim(params.getString("c241m01b_oid"));
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		C241M01A meta = retrialService.findC241M01A_oid(mainOid);
		C241M01B c241m01b = retrialService.findC241M01B_oid(c241m01b_oid);
		String[] ellngteeArr = retrialService.getC241M01B_misEllngtee(
				meta.getOwnBrId(), c241m01b);

		result.set("coBorrower", ellngteeArr[0]);
		result.set("guarantor", ellngteeArr[1]);

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult produce_grpDetail_attch(PageParameters params) throws Exception {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C241M01A meta = retrialService.findC241M01A_oid(mainOid);

		boolean success = false;
		if (meta != null) {
			success = lms2400Service.produce_grpDetail_attch(meta,
					CrsUtil.ATTCH_C241M01A_GRPDTL);
		}

		// 附件檔
		_grpDetail_attch(result, meta);

		if (success) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0025"));
		}

		return result;
	}

	public IResult load_specifyCycle(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("specifyCycle",
				new CapAjaxFormResult(retrialService.get_crs_specifyCycle()));
		return result;
	}

	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult redo_updateMIS(PageParameters params)
			throws CapException {
		String msg = "msg";

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);

		C241M01A c241m01a = retrialService.findC241M01A_oid(mainOid);
		if (c241m01a != null
				&& Util.equals(RetrialDocStatusEnum.已覆核已核定.getCode(),
						c241m01a.getDocStatus())) {
			lms2401Service.up_to_mis(c241m01a);
		} else {
			result.set(msg, "status error");
		}
		return result;
	}

	public IResult reQueryCustName(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C241M01A c241m01a = retrialService.findC241M01A_oid(mainOid);
		String changed = "N";

		if (c241m01a != null) {
			String custId = c241m01a.getCustId();
			String dupNo = c241m01a.getDupNo();

			Map<String, Object> latestData = iCustomerService.findByIdDupNo(
					custId, dupNo);
			if (latestData != null) {
				String orgCustName = Util.trim(c241m01a.getCustName());
				String newCustName = Util.trim(latestData.get("CNAME"));

				if (Util.isNotEmpty(newCustName)
						&& Util.notEquals(orgCustName, newCustName)) {
					changed = "Y";
					// ---
					SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
					c241m01a.setCustName(newCustName);
					// ---
					lms2400Service.daoSave(c241m01a);
				}
			}
		}

		result.set("changed", changed);
		result.set("custName", c241m01a.getCustName());
		return result;
	}

	public IResult reQueryCustNameByIdDupNo(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String hasResult = "N";
		String cName = "";
		if (Util.isNotEmpty(custId)) {

			Map<String, Object> latestData = iCustomerService.findByIdDupNo(
					custId, dupNo);
			if (latestData != null) {

				cName = Util.trim(latestData.get("CNAME"));

				if (Util.isNotEmpty(cName)) {
					hasResult = "Y";
				}
			}
		}

		result.set("hasResult", hasResult);
		result.set("cName", cName);
		return result;
	}

	private void _setResult_P_SellerBuyerInfo(CapAjaxFormResult result,
			C241M01A meta, boolean addReg) {
		HashMap<String, String> m = new HashMap<String, String>();
		m.put((addReg ? "reg_" : "") + "sellerId", Util.trim(meta.getCustId()));
		m.put((addReg ? "reg_" : "") + "sellerDup", Util.trim(meta.getDupNo()));
		m.put((addReg ? "reg_" : "") + "sellerName",
				Util.trim(meta.getCustName()));
		m.put((addReg ? "reg_" : "") + "buyerId", Util.trim(meta.getBuyerId()));
		m.put((addReg ? "reg_" : "") + "buyerDup", Util.isEmpty(Util.trim(meta
				.getBuyerDup())) ? "0" : Util.trim(meta.getBuyerDup()));
		m.put((addReg ? "reg_" : "") + "buyerName",
				Util.trim(meta.getBuyerName()));

		for (String k : m.keySet()) {
			result.set(k, m.get(k));
		}
	}

	private void _setModel_P_SellerBuyerInfo(PageParameters params,
			C241M01A meta, boolean addReg) {

		meta.setCustId(Util.trim(params.getString((addReg ? "reg_" : "")
				+ "sellerId")));
		meta.setDupNo(Util.trim(params.getString((addReg ? "reg_" : "")
				+ "sellerDup")));
		meta.setCustName(Util.trim(params.getString((addReg ? "reg_" : "")
				+ "sellerName")));

		meta.setBuyerId(Util.trim(params.getString((addReg ? "reg_" : "")
				+ "buyerId")));
		meta.setBuyerDup(Util.trim(params.getString((addReg ? "reg_" : "")
				+ "buyerDup")));
		meta.setBuyerName(Util.trim(params.getString((addReg ? "reg_" : "")
				+ "buyerName")));
	}

	/**
	 * 呼叫批次作業
	 * 
	 * @param params
	 * @param parent
	 * @throws CapException
	 * @throws IOException
	 */
	public IResult callBatch(PageParameters params)
			throws CapException, IOException {

		int jq_timeout = Util.parseInt(params.getString("jq_timeout"));
		if (jq_timeout == 0) {
			jq_timeout = 100;// default
		}

		EloanSubsysBatReqMessage esbrm = new EloanSubsysBatReqMessage();
		esbrm.setUrl(SysParamConstants.SYS_URL_LMS);
		esbrm.setReqFormat(EloanSubsysBatReqMessage.REQ_FMT_JSON);
		esbrm.setServiceId("crsBatchServiceImpl");
		esbrm.setTimeout(jq_timeout);
		esbrm.setLocalUrl(true);

		JSONObject requestJSON = new JSONObject();

		String act = Util.trim(params.getString("act"));
		List<String> paramList = new ArrayList<String>();
		if (Util.equals("c241m01a_dbu_approveToEnd", act)) {
			paramList.add("userId");
			paramList.add("unitNo");
		} else if (Util.equals("nonOversea_produceC240M01A", act)) {
			/*
			 * TODO 在「本機」開發時
			 * 
			 * http://127.0.0.1:9081/lms-web/app/scheduler?input={
			 * 'serviceId':'crsBatchServiceImpl','request':{act:'nonOversea_produceC240M01A',
			 * 'userId':'933001', 'unitNo':'933', 'unitType':'2',
			 * 'par_arr':'207^2022-01'}}
			 */
			paramList.add("par_arr");
			paramList.add("userId");
			paramList.add("unitNo");
			paramList.add("unitType");
		} else if (Util.equals("update491_abnormal", act)) {
			// no param
		} else if (Util.equals("elf490_to_elf491", act)) {
			paramList.add("limit_branch");
		} else if (Util.equals("elf490_to_elf491_force", act)) {
			paramList.add("limit_branch");
		} else if (Util.equals("procC242M01A", act)) {
			paramList.add("normalMin");
		} else if (Util.equals(CrsUtil.BATCH_FUNC_NAME_CHKR1R2R4_GEN, act)) { // key="chkR1R2R4_gen"
			paramList.add("brNo");
			paramList.add("custId");
			paramList.add("cmpLrDate");
		} else if (Util.equals(CrsUtil.BATCH_FUNC_NAME_CHKR1R2R4_PROCESS, act)) { // key="chkR1R2R4_process"
			paramList.add("cnt1");
			paramList.add("cnt2");
		} else if (Util.equals("updateDW_RKCREDIT", act)) {
		} else if (Util.equals("updateDW_delL140M01A", act)) {
			paramList.add("BR_CD");
			paramList.add("NOTEID");
			paramList.add("CUSTID");
			paramList.add("DUPNO");
			paramList.add("MOWTYPE");
			paramList.add("MOWVER1");
			paramList.add("MOWVER2");
			paramList.add("JCIC_DATE");
			paramList.add("ACCT_KEY");
			paramList.add("DELETE_REASON");
			paramList.add("REJECT_OTHEREASON_TEXT");
		} else if (Util.equals("updateDW_delL140M01A_OVS", act)) {
			paramList.add("BR_CD");
			paramList.add("NOTEID");
			paramList.add("RATING_DATE");
			paramList.add("RATING_ID");
			paramList.add("CUSTID");
			paramList.add("DUPNO");
			paramList.add("CUST_KEY");
			paramList.add("LOAN_CODE");
			paramList.add("MOWTYPE");
			paramList.add("MOWVER1");
			paramList.add("MOWVER2");
		}
		// ---
		requestJSON.element("act", act);
		for (String k : paramList) {
			requestJSON.element(k, params.getString(k));
		}
		// ---
		esbrm.setRequestJSON(requestJSON);

		String respStr = eloanBatchClient.send(esbrm);
		logger.debug("send to batch data={}", respStr);
		// =============
//		CapAjaxFormResult result = new CapAjaxFormResult();
//		result.set("r", respStr);
		JSONObject respObj = JSONObject.fromObject(respStr);
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("r", new CapAjaxFormResult(respObj));
		return result;
	}

	public IResult importBefText(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String bef_oid = Util.trim(params.getString("bef_oid"));
		C241M01A meta = retrialService.findC241M01A_oid(params
				.getString(EloanConstants.MAIN_OID));
		C241M01A befMeta = retrialService.findC241M01A_oid(bef_oid);

		if (befMeta == null) {
			throw new CapMessageException(MessageFormat.format(
					prop_lms2411m01.getProperty("ui_lms2411.msg15"), bef_oid),
					getClass());
		}
		if (CrsUtil.isCaseN(meta) || CrsUtil.isCaseG___N_Detail(meta)) {
			// 比對 覆審日、前次覆審日
			if (Util.equals(TWNDate.toAD(befMeta.getRetrialDate()),
					TWNDate.toAD(meta.getLastRetrialDate()))) {
				lms2400Service.replaceWithBef(meta, befMeta);
			}
		}
		return result;
	}

	public IResult onlyUp491(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// ---
		CapAjaxFormResult result = new CapAjaxFormResult();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp now = CapDate.getCurrentTimestamp();

		C241M01A meta = retrialService.findC241M01A_oid(params
				.getString(EloanConstants.MAIN_OID));

		String errMsg = lms2400Service.checkIncompleteMsg(meta);
		if (Util.isNotEmpty(errMsg)) {
			throw new CapMessageException(errMsg, getClass());
		}

		if (true) {
			meta.setApprover(user.getUserId());
			meta.setApproveTime(now);
			meta.setUpDate(now);
		}
		retrialService.save(meta);

		lms2401Service.up_to_mis(meta);

		result.add(query(params));
		return result;
	}

	@DomainAuth(AuthType.Modify)
	public IResult produceNewGrpDetail(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// ---
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C241M01A c241m01a_grpMain = retrialService.findC241M01A_oid(mainOid);
		C240M01A meta = retrialService.findC240M01A_C241M01A(c241m01a_grpMain);

		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String cName = Util.trim(params.getString("custName"));
		// 是否存在團貸母戶的子戶中
		if (true) {
			boolean inList = false;
			List<Map<String, Object>> list = misPTEAMAPPService
					.selCrsGroupDetail_orderByLoanBalDesc(
							c241m01a_grpMain.getOwnBrId(),
							c241m01a_grpMain.getGrpCntrNo());
			for (int i_list = 0; i_list < list.size(); i_list++) {
				Map<String, Object> map = list.get(i_list);

				String db_custId = Util.trim(Util.getLeftStr(
						Util.trim(map.get("LNF020_CUST_ID")), 10));
				String db_dupNo = Util.getRightStr(
						Util.trim(map.get("LNF020_CUST_ID")), 1);
				if (Util.equals(db_custId, custId)
						&& Util.equals(db_dupNo, dupNo)) {
					inList = true;
					break;
				}
			}
			if (inList == false) {
				// ui_lms2411.msg21={0}不存在於團貸明細
				throw new CapMessageException(
						MessageFormat.format(
								prop_lms2411m01.getProperty("ui_lms2411.msg21"),
								custId), getClass());
			}
		}

		// 檢查 custId 是否已存在工作底稿
		for (C241M01A c241m01a : retrialService.findC241M01A_C240M01A(meta
				.getMainId())) {
			if (CrsUtil.isCaseP(c241m01a)) {
				// 在同一份覆審工作底稿, 若同 1 個人有 N 個存款(代收)帳號, 則價金履約保證
				continue;
			}

			if (CrsUtil.isCaseS(c241m01a)) {
				continue;
			}

			if (Util.equals(c241m01a.getCustId(), custId)
					&& Util.equals(c241m01a.getDupNo(), dupNo)) {
				// ui_lms2411.msg22={0}已存在於工作底稿，無法重複新增
				throw new CapMessageException(
						MessageFormat.format(
								prop_lms2411m01.getProperty("ui_lms2411.msg22"),
								custId), getClass());
			}
		}

		boolean r = lms2400Service.produceNewGrpDetail(meta, c241m01a_grpMain,
				custId, dupNo, cName);
		if (r == false) {
			String errMsg = prop_lms2411m01.getProperty("ui_lms2411.msg23");
			throw new CapMessageException(errMsg, getClass());
		} else {
			retrialService.genProjectNo_append_grpDetail(meta,
					c241m01a_grpMain);
		}

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult update_itemNo_ptMgrId(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();

		String ptMgrId = Util.trim(params.getString("ptMgrId"));
		String model_oid = params.getString("model_oid");
		String prefix = "c241m01c_";
		if (Util.isNotEmpty(model_oid)
				&& StringUtils.startsWith(model_oid, prefix)) {
			String oid = StringUtils.substring(model_oid, prefix.length());
			if (Util.isNotEmpty(oid)) {
				C241M01C c241m01c = retrialService.findC241M01C_oid(oid);
				if (c241m01c != null) {
					c241m01c.setPtMgrId(ptMgrId);
					retrialService.save(c241m01c);
					// ---
					result.set("id", c241m01c.getPtMgrId());
					result.set("name",
							userInfoService.getUserName(c241m01c.getPtMgrId()));
				}
			}
		}
		return result;
	}

	/** J-108-0268 覆審案件 客戶逾期情形 **/
	@DomainAuth(AuthType.Modify)
	public IResult getOverDueData(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C241M01A c241m01a = null;
		c241m01a = retrialService.findC241M01A_oid(mainOid);

		Map<String, Object> map = retrialService
				.getOverDueData(new Date(), Util.trim(c241m01a.getCustId()),
						Util.trim(c241m01a.getDupNo()));
		String CapDays = (map.get("CapDays") == null ? "0" : map.get("CapDays")
				.toString());
		String CapOvDate = (map.get("CapOvDate") == null ? null : map.get(
				"CapOvDate").toString());
		String CapDataDate = (map.get("CapDataDate") == null ? null : map.get(
				"CapDataDate").toString());
		String IntDays = (map.get("IntDays") == null ? "0" : map.get("IntDays")
				.toString());
		String IntOvDate = (map.get("IntOvDate") == null ? null : map.get(
				"IntOvDate").toString());
		String IntDataDate = (map.get("IntDataDate") == null ? null : map.get(
				"IntDataDate").toString());
		String qryDate = (map.get("qryDate") == null ? null : map
				.get("qryDate").toString());
		DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		Date qryD = new Date();
		Date CapD = new Date();
		Date CapDataD = new Date();
		Date IntD = new Date();
		Date IntDataD = new Date();
		try {
			if (map.get("CapDays") == null) { // 沒有本金逾期資料
				c241m01a.setCapDays(0);
				c241m01a.setCapDt(null);
				c241m01a.setCapDataDt(null);
			} else {
				c241m01a.setCapDays((Integer) map.get("CapDays"));
				CapD = ((CapOvDate != null) ? format.parse(CapOvDate) : null);
				CapDataD = ((CapDataDate != null) ? format.parse(CapDataDate)
						: null);
				c241m01a.setCapDt(CapD);
				c241m01a.setCapDataDt(CapDataD);
			}
			if (map.get("IntDays") == null) { // 沒有利息逾期資料
				c241m01a.setIntDays(0);
				c241m01a.setIntDt(null);
				c241m01a.setIntDataDt(null);
			} else {
				c241m01a.setIntDays((Integer) map.get("IntDays"));
				IntD = ((IntOvDate != null) ? format.parse(IntOvDate) : null);
				IntDataD = ((IntDataDate != null) ? format.parse(IntDataDate)
						: null);
				c241m01a.setIntDt(IntD);
				c241m01a.setIntDataDt(IntDataD);
			}

			qryD = format.parse(qryDate);
			c241m01a.setOvQryDt(qryD); // 查詢日
		} catch (ParseException e) {
			throw new RuntimeException("無法初始化日期！" + e);
		}

		retrialService.save(c241m01a);
		// ui_lms2411.msg32=本金最長逾期天數：{0}天，利息最長逾期天數：{1}天。引進資料日期:{2}
		result.set("overDueText", MessageFormat.format(
				prop_lms2411m01.getProperty("ui_lms2411.msg32"), CapDays,
				IntDays, Util.nullToSpace(qryDate)));
		return defaultResult(params, c241m01a, result);
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult prepare_ELF601_ELF602_L260RelateData(PageParameters params) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C241M01A meta = null;

		String custId = "";
		String dupNo = "";
		String custName = "";
		Map<String, Boolean> cntrNo_L260_map = new TreeMap<String, Boolean>();
		Map<String, Boolean> id_L260_map = new TreeMap<String, Boolean>();
		Set<String> cntrNo_sameBr = new TreeSet<String>();
		Set<String> cntrNo_diffBr = new TreeSet<String>();
		int has_data_cnt = 0;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findC241M01A_oid(mainOid);
			if (meta != null) {
				custId = meta.getCustId();
				dupNo = meta.getDupNo();
				String brNo = meta.getOwnBrId();

				List<MISLN20> lnf020_list = misMISLN20Service.findByCustId(
						custId, dupNo);
				Map<String, String> elf602_status_map = clsService
						.get_codeTypeWithOrder("postLoan_handlingStatus");
				String[] elf602_status_arr = elf602_status_map.keySet()
						.toArray((new String[elf602_status_map.size()]));
				for (MISLN20 lnf020 : lnf020_list) {
					if (CrsUtil.isNull_or_ZeroDate(lnf020
							.getLnf020_cancel_date())) {
						String cntrNo = lnf020.getLnf020_contract();
						if (cntrNo_L260_map.containsKey(cntrNo)) {
							continue;
						} else {
							if (Util.equals(brNo,
									CrsUtil.getBrNoFromLNF020_CONTRACT(cntrNo))) {
								cntrNo_sameBr.add(cntrNo);
							} else {
								cntrNo_diffBr.add(cntrNo);
							}
							cntrNo_L260_map
									.put(cntrNo,
											has_ELF601_ELF602_cntrNo(custId,
													dupNo, cntrNo, "",
													elf602_status_arr, brNo));
							id_L260_map.put(
									cntrNo,
									has_ELF601_ELF602_id(custId, dupNo, cntrNo,
											"", elf602_status_arr, brNo));
						}
					} else {
						continue; // 已銷戶額度，不顯示
					}
				}
			}
		}
		if (true) {
			// 參考 lms8000m01formhandler :: queryAndAdd(...)
			has_data_cnt = 0;
			for (String cntrNo : cntrNo_L260_map.keySet()) {
				if (cntrNo_L260_map.get(cntrNo)) {
					++has_data_cnt;
				}
			}
			if (has_data_cnt == 0) {
				has_data_cnt = id_L260_map.size();
			}
			if (has_data_cnt == 0) {
				// throw new
				// CapMessageException("客戶 "+custId+"-"+dupNo+" 的「未銷戶額度」無「貸後管理資料」可供檢視",
				// getClass());
			}
		}

		HashMap<String, JSONArray> map601602 = new HashMap<String, JSONArray>();
		if (true) {
			JSONArray jsonArray = new JSONArray();
			LinkedHashSet<String> ord_set = new LinkedHashSet<String>();
			ord_set.addAll(cntrNo_sameBr);
			ord_set.addAll(cntrNo_diffBr);
			for (String cntrNo : ord_set) {
				JSONObject o = new JSONObject();
				o.put("cntrNo", cntrNo);
				o.put("hasData", cntrNo_L260_map.get(cntrNo) ? "Y" : "N");
				o.put("hasIdData", id_L260_map.get(cntrNo) ? "Y" : "N");
				jsonArray.add(o);
			}
			map601602.put("arr", jsonArray);

		}
		Date nowTS = CapDate.getCurrentTimestamp();
		result.set("custId", meta.getCustId());
		result.set("dupNo", meta.getDupNo());
		result.set("custName", meta.getCustName());
		result.set(
				"begDate",
				(meta != null && CrsUtil.isNOT_null_and_NOTZeroDate(meta
						.getLastRetrialDate())) ? TWNDate.toAD(meta
						.getLastRetrialDate()) : TWNDate.toAD(CapDate.addMonth(
						nowTS, -12)));
		result.set("endDate", TWNDate.toAD(nowTS));
		result.set("elf601elf602_data", new CapAjaxFormResult(map601602));
		result.set("elf601elf602_size", cntrNo_L260_map.size());
		result.set("elf601elf602_hasDataCnt", has_data_cnt);
		return result;
	}

	private boolean has_ELF601_ELF602_cntrNo(String custId, String dupNo,
			String cntrNo, String loanNo, String[] elf602_status_arr,
			String brNo) {
		List<ELF601> elf601s = misdbBASEService.getElf601ByFilter(custId,
				dupNo, cntrNo, loanNo, "A", brNo); // A-全部
		List<ELF602> elf602s = misdbBASEService.getElf602ByFilter(custId,
				dupNo, cntrNo, loanNo, "", "", elf602_status_arr, brNo);
		if (elf601s.size() <= 0 && elf602s.size() <= 0) {
			return false;
		}
		return true;
	}

	private boolean has_ELF601_ELF602_id(String custId, String dupNo,
			String cntrNo, String loanNo, String[] elf602_status_arr,
			String brNo) {
		List<ELF601> elf601sId = misdbBASEService.getElf601OnlyById(
				Util.trim(custId), Util.trim(dupNo), Util.trim(brNo));
		List<ELF602> elf602sId = misdbBASEService.getElf602OnlyById(
				Util.trim(custId), Util.trim(dupNo), false, Util.trim(brNo));
		// J-110-0363 增加以ID層級的追蹤事項架構
		if (elf601sId.size() <= 0 && elf602sId.size() <= 0) {
			return false;
		}
		return true;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult addAuthUnitByLnf078T(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String exDate = params.getString("exDate");
		String branch_o = params.getString("branch_o");
		String branch_allow = params.getString("branch_allow");
		if (Util.isEmpty(exDate) || Util.isEmpty(branch_o)) {
			throw new CapMessageException("lost param", getClass());
		}
		String authTable = "C241A01A";
		String mainTable = "C241M01A";
		String docStatus = "050,060";
		String fromBranch = branch_o;
		String toBranch = branch_allow;
		int exCount = eloandbBASEService.addAuthUnitByLnf078t(authTable,
				mainTable, docStatus.split(","), fromBranch, toBranch, exDate);

		result.set("msg", "TABLE：【" + authTable + "】，更新筆數：【" + exCount + "】筆\r");
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult addRetrialAuthUnitByLnf078T(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String exDate = params.getString("exDate");
		String branch_o = params.getString("branch_o");
		String branch_n = params.getString("branch_n");
		if (Util.isEmpty(exDate) || Util.isEmpty(branch_o)) {
			throw new CapMessageException("lost param", getClass());
		}
		String authTable = "C241A01A";
		String mainTable = "C241M01A";
		String docStatus = "050,060";
		String fromBranch = branch_o;
		String toBranch = branch_n;
		String toBranchRetrialUnit = "";
		IBranch ibranch = branchService.getBranch(branch_n);
		if (ibranch != null) {
			toBranchRetrialUnit = Util.trim(ibranch.getBrnGroup());
		}
		if (Util.isEmpty(toBranchRetrialUnit)) {
			throw new CapMessageException("toBranchRetrialUnit param",
					getClass());
		}
		int exCount = eloandbBASEService.addRetrialAuthUnitByLnf078T(authTable,
				mainTable, docStatus.split(","), fromBranch, toBranch,
				toBranchRetrialUnit, exDate);

		result.set("msg", "TABLE：【" + authTable + "】，更新筆數：【" + exCount + "】筆\r");
		return result;
	}

	// J-110-0505_05097_B1001 Web
	// e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPrintL140M01AParam(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = Util.trim(params.getString("oids")).split("\\|");
		List<String> noL120M01A = new ArrayList<String>();
		List<String> noL140M01A = new ArrayList<String>();
		List<String> paramList = new ArrayList<String>();
		String ctlType = LrsUtil.CTLTYPE_主辦覆審;
		List<String> notProc = new ArrayList<String>();

		if (oids != null && oids.length > 0) {
			List<C241M01A> c241m01a_list = new ArrayList<C241M01A>();

			if (true) {
				List<String> oid_list = new ArrayList<String>();
				for (String oid : oids) {
					oid_list.add(oid);
				}
				c241m01a_list = retrialService.findC241M01A_oid(oid_list);
				for (C241M01A c241m01a : c241m01a_list) {

					C240M01A c240m01a = retrialService
							.findC240M01A_C241M01A(c241m01a);

					Map<String, String> cntrNo_list = new HashMap<String, String>();
					Set<String> cntrNo_Set = new HashSet<String>();
					List<C241M01B> c241m01b_list = retrialService
							.findC241M01B_c241m01a(c241m01a);

					if (c241m01b_list == null || c241m01b_list.isEmpty()) {
						// 若覆審報告表沒有授信額度資料，就先自動引進
						retrialService.importLNtoC241M01B_single(c240m01a,
								c241m01a);
						c241m01b_list = retrialService
								.findC241M01B_c241m01a(c241m01a);
					}

					if (CollectionUtils.isNotEmpty(c241m01b_list)) {
						for (C241M01B c241m01b : c241m01b_list) {
							String cntrNo = Util.trim(c241m01b.getQuotaNo());
							cntrNo_list.put(cntrNo, "");
							cntrNo_Set.add(cntrNo); // 要查詢的額度Set
						}
					}

					// [下午 01:28] 金至忠(授信審查處,襄理)
					// 我問覆審人員他們是用id查覆審分行已核准簽報書, 拿該分行的額度明細表出來看!(沒有在對額度序號)
					// List<Map<String, Object>> dataList = lms1700Service
					// .findPrint_L140M01A_By_CntrNos(cntrNo_Set);

					if (cntrNo_Set != null && !cntrNo_Set.isEmpty()) {

						List<Map<String, Object>> dataList = retrialService
								.findPrint_L140M01A_By_CustId(
										c241m01a.getCustId(),
										c241m01a.getDupNo(), cntrNo_Set);

						if (dataList != null && !dataList.isEmpty()) {

							for (Map<String, Object> dataMap : dataList) {

								// OID, CUSTID, CNTRNO, MAINID, ENDDATE,
								// ISHEADCHECK
								String OID = Util.trim(MapUtils.getString(
										dataMap, "OID"));
								String CNTRNO = Util.trim(MapUtils.getString(
										dataMap, "CNTRNO"));

								String ISHEADCHECK = Util.trim(MapUtils
										.getString(dataMap, "ISHEADCHECK"));

								String rptNo = Util.equals("Y", ISHEADCHECK) ? "R13"
										: "R12";

								cntrNo_list.put(CNTRNO, "Y");

								paramList.add(c241m01a.getOid() + "^" + rptNo
										+ "^" + OID);
							}
						}
					} else {
						notProc.add("借款人無額度明細表/批覆書：" + c241m01a.getCustId()
								+ " " + c241m01a.getDupNo());
					}

					// 額度找不到額度明細表/批覆書
					for (String cntrNoStr : cntrNo_list.keySet()) {
						if (Util.notEquals(cntrNo_list.get(cntrNoStr), "Y")) {
							noL140M01A.add(cntrNoStr);
						}
					}

				}
			}

		}

		if (true) {

			if (noL140M01A.size() > 0) {
				notProc.add("無額度明細表/批覆書：" + StringUtils.join(noL140M01A, "、"));
			}

		}
		if (notProc.size() > 0) {
			result.set("notProc", StringUtils.join(notProc, "<br/>"));
		}
		if (paramList.size() > 0) {
			result.set("parStr", StringUtils.join(paramList, "|"));
		}
		return result;
	}

	/**
	 * J-111-0560 配合授信審查處，Web-eloan授信管理系統，覆審作業聯徵資料PPA已查詢部份,增加一鍵查詢功能，自動比對債票信及卡信資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getEjcicReusltRecord(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C241M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findC241M01A_oid(mainOid);
		}
		// 自動比對債票信及卡信資料
		Map<String, String> resultMap = retrialService.getEjcicReusltRecord(
				meta, false);
		// 回傳頁面結果
		result.putAll(resultMap);
		return result;
	}

	// J-111-0554 配合授審處增進管理效益，修改相關功能程式 add 列印擔保品設定資料表
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPrintCollSet(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = Util.trim(params.getString("oids")).split("\\|");
		List<String> noL140M01A = new ArrayList<String>();
		List<String> paramList = new ArrayList<String>();
		List<String> notProc = new ArrayList<String>();

		if (oids != null && oids.length > 0) {
			List<C241M01A> c241m01a_list = new ArrayList<C241M01A>();

			if (true) {
				List<String> oid_list = new ArrayList<String>();
				for (String oid : oids) {
					oid_list.add(oid);
				}
				c241m01a_list = retrialService.findC241M01A_oid(oid_list);
				for (C241M01A c241m01a : c241m01a_list) {
					C240M01A c240m01a = retrialService
							.findC240M01A_C241M01A(c241m01a);
		
					Map<String, String> cntrNo_list = new HashMap<String, String>();
					Set<String> cntrNo_Set = new HashSet<String>();
					List<C241M01B> c241m01b_list = retrialService
							.findC241M01B_c241m01a(c241m01a);
		
					if (c241m01b_list == null || c241m01b_list.isEmpty()) {
						// 若覆審報告表沒有授信額度資料，就先自動引進
						retrialService.importLNtoC241M01B_single(c240m01a,
								c241m01a);
						c241m01b_list = retrialService
								.findC241M01B_c241m01a(c241m01a);
					}
		
					if (CollectionUtils.isNotEmpty(c241m01b_list)) {
						for (C241M01B c241m01b : c241m01b_list) {
							String cntrNo = Util.trim(c241m01b.getQuotaNo());
							cntrNo_list.put(cntrNo, "");
							cntrNo_Set.add(cntrNo); // 要查詢的額度Set
						}
					}
					if (cntrNo_Set != null && !cntrNo_Set.isEmpty()) {

						//依額度撈取擔保品設定資料表(限不動產/動產)
						List<Map<String, Object>> dataList = retrialService
								.findCMS_C100m01byCntrno(cntrNo_Set);

						if (dataList != null && !dataList.isEmpty()) {

							for (Map<String, Object> dataMap : dataList) {
								String MAINID = Util.trim(MapUtils.getString(
										dataMap, "MAINID"));
//								String BRANCH = Util.trim(MapUtils.getString(
//										dataMap, "BRANCH"));
//								String CUSTID = Util.trim(MapUtils.getString(
//										dataMap, "CUSTID"));
//								String COLLNO = Util.trim(MapUtils.getString(
//										dataMap, "COLLNO"));
//								String COLLTYP1 = Util.trim(MapUtils.getString(
//										dataMap, "COLLTYP1"));
//								String CNTRNO = Util.trim(MapUtils.getString(
//										dataMap, "CNTRNO"));
								paramList.add(MAINID);  //估價報告書mainid
							}
						} else {
							notProc.add("查無擔保品設定資料表：" + c241m01a.getCustId()
									+ " " + c241m01a.getDupNo());
						}
					} else {
						notProc.add("無授信額度資料：" + c241m01a.getCustId()
								+ " " + c241m01a.getDupNo());
					}
				}
			}
		}
		if (notProc.size() > 0) {
			result.set("notProc", StringUtils.join(notProc, "<br/>"));
		}
		if (paramList.size() > 0) {
			result.set("parStr", StringUtils.join(paramList, "|"));
		}
		return result;
	}
}
