/* 
 * PageParameters.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.iisigroup.cap.component;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletRequest;

/**
 * <pre>
 * 取代原系統wicket框架的同名物件
 * request傳入的參數
 * </pre>
 * 
 * @since 2011/11/22
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011/11/22,rodeschen,new
 *          </ul>
 */
public interface PageParameters extends Map<String, Object>, Serializable, Cloneable {

    /**
     * set Request Object
     * 
     * @param obj
     *            request obj
     */
    void setRequestObject(Object obj);

    /**
     * 取得 Servlet Request
     * 
     * @param <T>
     * @return
     */
    <T extends ServletRequest> T getServletRequest();

    /**
     * get request parameter
     * 
     * @param key
     *            欄位名
     * @return 回傳
     */
    String getString(String key);

    /**
     * get request parameter
     * 
     * @param key
     *            欄位名
     * @return 回傳
     */
    Object getObject(String key);

    /**
     * get request parameter
     * 
     * @param key
     *            欄位名
     * @param defaultValue
     *            預設值
     * @return 回傳
     */
    String getString(String key, String defaultValue);

    /**
     * 取出字串, 但限制內容不得含有可能有危害的內容 (目前僅先檢查 Cross Site Scripting 的幾個字)
     * 
     * @param key
     * @param defaultValue
     * @return
     */
    String getStrickString(String key, String defaultValue);

    /**
     * 取出字串, 但限制內容不得含有可能有危害的內容 (目前僅先檢查 Cross Site Scripting 的幾個字)
     * 
     * @param key
     * @param defaultValue
     * @param maxLength
     * @return
     */
    String getStrickString(String key, String defaultValue, int maxLength);

    /**
     * get request parameter use xssEncode
     * 
     * @param key
     *            欄位名
     * @return 回傳
     */
    String getEscapString(String key);

    /**
     * get request parameter use xssEncode
     * 
     * @param key
     *            欄位名
     * @param defaultValue
     *            預設值
     * @return 回傳
     */
    String getEscapString(String key, String defaultValue);

    /**
     * get escape string with xssEncode
     * 
     * @param value
     *            String
     * @return escape string
     */
    String getEscapeStringFromValue(String value);

    /**
     * set customize parameter
     * 
     * @param key
     *            欄位名
     * @param value
     *            值
     */
    void setParameter(String key, Object value);

    /**
     * check parameter exist
     * 
     * @param key
     *            欄位名
     * @return 回傳
     */
    boolean containsParamsKey(String key);

    /**
     * get Integer parameter
     * 
     * @param key
     *            欄位名
     * @return 回傳
     */
    int getAsInteger(String key);

    /**
     * get Integer parameter
     * 
     * @param key
     *            欄位名
     * @param defaultValue
     *            預設值
     * @return 回傳
     */
    int getAsInteger(String key, int defaultValue);

    /**
     * 取得字串陣列
     * 
     * @param key
     * @return
     */
    String[] getStringArray(String key);

    /**
     * 取得字串
     * 
     * @param key
     *            欄位名
     * @param defaultValue
     *            找不到對應key值後回傳的預設值
     * @param escape
     *            是否需要跳脫字元
     * @return
     */
    String getString(String key, String defaultValue, boolean escape);

    /**
     * 取得檔案
     * 
     * @param <T>
     * @param key
     *            欄位名
     * @return
     */
    <T> T getFile(String key);

    /**
     * 取得多個檔案
     * 
     * @param <T>
     * @param key
     *            欄位名
     * @return
     */
    <T> List<T> getFiles(String key);

    /**
     * 判斷傳入字串並回傳布林值
     * 
     * @param key
     *            欄位名
     * @return
     */
    Boolean getAsBoolean(String key);

    /**
     * 判斷傳入字串並回傳布林值, 可設定預設值
     * 
     * @param key
     *            欄位名
     * @param defaultValue
     *            預設值
     * @return
     */
    Boolean getAsBoolean(String key, boolean defaultValue);

    /**
     * 取得布林值
     * 
     * @param key
     *            欄位名
     * @return
     */
    boolean getBoolean(String key);

    /**
     * 取得long值
     * 
     * @param key
     *            欄位名
     * @param defaultValue
     *            預設值
     * @return
     */
    long getLong(String key, long defaultValue);

    /**
     * 取得int值, 可設置預設值
     * 
     * @param key
     *            欄位名
     * @param defaultValue
     *            預設值
     * @return
     */
    int getInt(String key, int defaultValue);

    /**
     * 將陣列轉換為字串
     * 
     * @param key
     *            欄位名
     * @return
     */
    String getArrayAsString(String key);

    /**
     * 取得Int
     * 
     * @param key
     *            欄位名
     * @return
     */
    int getInt(String key);

    /**
     * 取值後轉成Double
     * 
     * @param key
     *            欄位名
     * @param defaultValue
     *            預設值
     * @return
     */
    double getAsDouble(String key, double defaultValue);

    Double getAsDouble(String key);

    /**
     * 取得Double
     * 
     * @param key
     *            欄位名
     * @return
     */
    Double getDouble(String key);

    /**
     * 舊的 PageParameter 有此 method, 抄一份過來用.<br>
     * 
     * @param key
     *            欄位名
     * @return 如果 key 存在 (不分大小寫), 回傳已存在的 key (可能大小寫不同), 否則 null
     */
    public String getKey(final String key);

    /**
     * 模擬 wicket PageParameter 功能
     * 
     * @param key
     * @param value
     */
    void add(String key, String value);

    /*
     * (non-Javadoc)
     * 
     * @see java.lang.Object#clone()
     */
    Object clone();

}
