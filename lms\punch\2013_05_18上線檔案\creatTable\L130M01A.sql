---------------------------------------------------------
-- LMS.L130M01A 異常通報表主檔
---------------------------------------------------------
--DROP TABLE LMS.L130M01A;
CREATE TABLE LMS.L130M01A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	DOCTYPE       CHAR(1)      ,
	CHAIRMANID    VARCHAR(10)  ,
	CHA<PERSON>MA<PERSON><PERSON>N<PERSON> CHAR(1)      ,
	CHAIRMAN      VARCHAR(120) ,
	PROCESS       VARCHAR(3072),
	FIRSTDATE     DATE         ,
	LASTDATE      DATE         ,
	COLLSTAT      VARCHAR(3072),
	PROMISECAS<PERSON>   CHAR(1)      ,
	PROMISERAT<PERSON>  VARCHAR(3)   ,
	AMT           VARCHAR(3072),
	<PERSON><PERSON><PERSON><PERSON><PERSON>(3072),
	TOTRISKAMT    DECIMAL(13,0),
	<PERSON><PERSON><PERSON><PERSON>       DECIMAL(13,0),
	BANKAMT       VARCHAR(3072),
	<PERSON><PERSON><PERSON>REMAIND   VARCHAR(3072),
	GROUP         VARCHAR(60)  ,
	SAMETOTAMT    VARCHAR(3072),
	GRPID         VARCHAR(4)   ,
	GRPNAME       VARCHAR(60)  ,
	MDCLASS       CHAR(3)      ,
	SAMEIDEA      VARCHAR(3072),
	REPORTDSCR    VARCHAR(3072),
	HASEBRID      CHAR(1)      ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L130M01A PRIMARY KEY(OID)
) in EL_DATA_32KTS index in EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL130M01A01;
CREATE UNIQUE INDEX LMS.XL130M01A01 ON LMS.L130M01A   (MAINID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L130M01A IS '異常通報表主檔';
COMMENT ON LMS.L130M01A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	DOCTYPE       IS '企/個金案件', 
	CHAIRMANID    IS '負責人統編', 
	CHAIRMANDUPNO IS '負責人統編重複碼', 
	CHAIRMAN      IS '負責人姓名', 
	PROCESS       IS '異常狀況', 
	FIRSTDATE     IS '初次授信往來日期', 
	LASTDATE      IS '最後一次續約日期', 
	COLLSTAT      IS '擔保品內容及押值', 
	PROMISECASE   IS '信保基金保證案件', 
	PROMISERATIO  IS '保證成數', 
	AMT           IS '額度', 
	REMAIND       IS '餘額', 
	TOTRISKAMT    IS '本行曝險金額總計', 
	LOSTAMT       IS '預估損失金額', 
	BANKAMT       IS '該戶在聯行之額度', 
	BANKREMAIND   IS '該戶在聯行之餘額', 
	GROUP         IS '所屬企業集團', 
	SAMETOTAMT    IS '該戶在同業之總餘額', 
	GRPID         IS '集團代碼', 
	GRPNAME       IS '集團名稱', 
	MDCLASS       IS '異常類別代碼', 
	SAMEIDEA      IS '同業擬(已)採取之措施', 
	REPORTDSCR    IS '陳報及說明事項', 
	HASEBRID      IS '是否有參貸行', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
