/* 
 * L140M01DDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01D;

/** 額度授信科目限額檔 **/
public interface L140M01DDao extends IGenericDao<L140M01D> {

	L140M01D findByOid(String oid);

	List<L140M01D> findByOids(String[] oids);

	List<L140M01D> findByMainId(String mainId);

	L140M01D findByUniqueKey(String mainId, String lmtType, Integer lmtSeq);

	List<L140M01D> findByMainIdAndlmtType(String mainId, String lmtType);
	
	List<L140M01D> findByMainIdAndLmtTypeAndSubject(String mainId, String lmtType, String subject);
}