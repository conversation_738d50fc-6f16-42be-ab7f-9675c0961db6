/*_
 * ConditionsUnanimousBased.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */

package tw.com.iisi.cap.security.vote;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.access.AccessDecisionVoter;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.vote.RoleVoter;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.SpringSecurityMessageSource;
import org.springframework.util.Assert;

/**
 * <pre>
 * 依據Config中所設定的access條件當作Voter
 * </pre>
 * 
 * @since 2011/1/20
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011/1/20,iristu,new
 *          </ul>
 */
public class ConditionsUnanimousBased implements AccessDecisionManager, InitializingBean, MessageSourceAware {

    /**
     * 條件選民
     */
    private Map<String, AccessDecisionVoter> conditionVoters;

    /**
     * Spring security 訊息
     */
    protected MessageSourceAccessor messages = SpringSecurityMessageSource.getAccessor();

    /**
     * <pre>
     * 依據Config中所設定的access條件當作Voter進行檢核
     * </pre>
     * 
     * @param authentication
     *            使用者登入資訊
     * @param object
     *            FilterInvocation
     * @param config
     *            ConfigAttributeDefinition
     */
    @SuppressWarnings("rawtypes")
    @Override
    public void decide(Authentication authentication, Object object, Collection<ConfigAttribute> configAttributes) {
        int grant = 0;
        int abstain = 0;

        List<AccessDecisionVoter> decisionVoters = new ArrayList<AccessDecisionVoter>();

        Iterator configIter = configAttributes.iterator();

        while (configIter.hasNext()) {

            String s = ((ConfigAttribute) configIter.next()).getAttribute();

            if (conditionVoters.containsKey(s)) {
                AccessDecisionVoter voter = conditionVoters.get(s);
                if (voter instanceof RoleVoter) {
                    ((RoleVoter) voter).setRolePrefix(s);
                }
                decisionVoters.add(voter);
            }

        }

        Iterator<AccessDecisionVoter> voters = decisionVoters.iterator();

        while (voters.hasNext()) {
            AccessDecisionVoter voter = (AccessDecisionVoter) voters.next();
            int result = voter.vote(authentication, object, configAttributes);

            switch (result) {
            case AccessDecisionVoter.ACCESS_GRANTED:
                grant++;

                break;

            case AccessDecisionVoter.ACCESS_DENIED:
                throw new AccessDeniedException(messages.getMessage("AbstractAccessDecisionManager.accessDenied", "Access is denied"));

            default:
                abstain++;

                break;
            }
        }

        // To get this far, there were no deny votes
        if (grant > 0) {
            return;
        } else if (abstain > 0) {
            throw new AccessDeniedException(messages.getMessage("AbstractAccessDecisionManager.accessDenied", "Access is denied"));
        }

    }// ;

    /*
     * 判斷設定中是否存在對應條件選民
     * 
     * @see org.springframework.security.access.AccessDecisionManager#supports(org.springframework.security.access.ConfigAttribute)
     */
    @Override
    public boolean supports(ConfigAttribute attribute) {
        return (getConditionVoters().containsKey(attribute.toString()));
    }

    /*
     * (non-Javadoc)
     * 
     * @see org.springframework.security.access.AccessDecisionManager#supports(java.lang.Class)
     */
    @SuppressWarnings("rawtypes")
    @Override
    public boolean supports(Class clazz) {
        return true;
    }

    /**
     * 取得條件選民
     * 
     * @return conditionVoters
     */
    public Map<String, AccessDecisionVoter> getConditionVoters() {
        return conditionVoters;
    }

    /**
     * 設置條件選民
     * 
     * @param conditionVoters
     */
    public void setConditionVoters(Map<String, AccessDecisionVoter> conditionVoters) {
        this.conditionVoters = conditionVoters;
    }

    /*
     * 設置訊息來源
     * 
     * @see org.springframework.context.MessageSourceAware#setMessageSource(org.springframework.context.MessageSource)
     */
    @Override
    public void setMessageSource(MessageSource messageSource) {
        this.messages = new MessageSourceAccessor(messageSource);
    }

    /*
     * 屬性設置後提醒
     * 
     * @see org.springframework.beans.factory.InitializingBean#afterPropertiesSet()
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        Assert.notEmpty(this.conditionVoters, "AccessDecisionVoters is required");
    }

}// ~
