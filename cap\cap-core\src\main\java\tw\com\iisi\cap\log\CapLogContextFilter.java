/*
 * CapLogInfoFilter.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.log;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.util.StringChecker;

/**
 * <p>
 * set log4j MDC for log user information.
 * </p>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/6,iristu,new
 *          </ul>
 */
public class CapLogContextFilter implements Filter {

    private static Logger logger = LoggerFactory.getLogger(CapLogContextFilter.class);

    /**
     * {@value #LOGIN_USERNAME}
     */
    public final static String LOGIN_USERNAME = "LOGIN_USERNAME";
    /**
     * {@value #LOGIN_UNITNO}
     */
    public final static String LOGIN_UNITNO = "LOGIN_UNITNO";

    /**
     * {@value #DEFAULT_LOGIN}
     */
    private final static String DEFAULT_LOGIN = "------";
    /**
     * {@value #DEFAULT_UNITNO}
     */
    private final static String DEFAULT_UNITNO = "---";

    /**
     * {@value #BAT_LOGIN}
     */
    private final static String BAT_LOGIN = "UNKNOWN";
    /**
     * q{@value #BAT_UNITNO}
     */
    private final static String BAT_UNITNO = "BAT";
    /**
     * {@value #BAT_URI}
     */
    private final static String BAT_URI = "/app/scheduler";

    /*
     * 清除線程
     * 
     * @see javax.servlet.Filter#destroy()
     */
    @Override
    public void destroy() {
        CapLogContext.resetLogContext();
    }

    /*
     * 執行濾器
     * 
     * @see javax.servlet.Filter#doFilter(javax.servlet.ServletRequest, javax.servlet.ServletResponse, javax.servlet.FilterChain)
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpSession session = req.getSession(false);

        // 用戶端IP
        CapLogContext.put("clientAddr", req.getRemoteAddr());

        if (session == null) {
            if (StringUtils.contains(req.getRequestURI(), BAT_URI)) {
                // 預設路徑=BAT
                CapLogContext.setUnitNo(BAT_UNITNO);

                // 批次排程
                try {
                    String input = (String) req.getParameter("input");
                    String userId = StringUtils.isNotBlank(input) ? JSONObject.fromObject(input).optString("serviceId") : BAT_LOGIN;
                    // 20220503 [ref #45] Trust Boundary Violation
                    String checkedUserId = StringChecker.checkUserNoString(userId);
                    CapLogContext.setLogin(checkedUserId);
                } catch (Exception ex) {
                    logger.error(ex.getMessage(), ex);
                    CapLogContext.setLogin(BAT_LOGIN);
                }
            } else {
                CapLogContext.setLogin(DEFAULT_LOGIN);
            }
        } else {
            // 20220503 [ref #45] Trust Boundary Violation
            // Session ID
            String checkedSessionId = StringChecker.checkXssString(session.getId());
            CapLogContext.setSessionId(checkedSessionId);

            // 20220503 [ref #45] Trust Boundary Violation
            String requestUrl = getRequestURL(req);
            String checkedRequestUrl = StringChecker.checkXssString(requestUrl);
            CapLogContext.setRequestURL(checkedRequestUrl);

            // User相關資訊
            String userId = (String) session.getAttribute(LOGIN_USERNAME);
            userId = CapString.isEmpty(userId) ? (String) request.getParameter("j_username") : userId;

            String unitNo = (String) session.getAttribute(LOGIN_UNITNO);

            // 20220503 [ref #45] Trust Boundary Violation
            String checkedUserId = StringChecker.checkUserNoString(userId);
            String checkedUnitNo = StringChecker.checkUserNoString(unitNo);

            if (CapString.isEmpty(checkedUserId)) {
                CapLogContext.setLogin(DEFAULT_LOGIN);
                CapLogContext.setUnitNo(DEFAULT_UNITNO);

            } else {
                CapLogContext.setLogin(checkedUserId);
                CapLogContext.setUnitNo(checkedUnitNo);
            }
        }

        try {
            chain.doFilter(request, response);
        } finally {
            CapLogContext.resetLogContext();
            SimpleContextHolder.resetInfoStore();
        }
    }// ;

    /**
     * Gets the request url.
     * 
     * @param filter
     *            the filter
     * 
     * @return the request url
     */
    private String getRequestURL(HttpServletRequest req) {
        String url = req.getRequestURI().replaceFirst(req.getContextPath(), "");
        int f = url.indexOf("/app");
        if (f > -1) {
            url = url.substring(f + 4);
        }

        if (!CapString.isEmpty(req.getParameter("_pa"))) {
            url = "/" + req.getParameter("_pa");
        }

        return url;
    }

    /*
     * 實例化，但此方法沒有做任何事情
     * 
     * @see javax.servlet.Filter#init(javax.servlet.FilterConfig)
     */
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // do nothing
    }

}
