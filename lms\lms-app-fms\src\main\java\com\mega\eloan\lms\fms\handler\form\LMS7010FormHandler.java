/* 
 * LMS7010FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.form;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.lms.fms.service.LMS7010Service;
import com.mega.eloan.lms.model.L800M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * 常用主管資料 Service
 * 
 * @since 2011/9/29
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/10/29,Vector Lo,new
 *          <li>2013/07/05,Vector Lo,修正unitType=K的分行顯示異常問題(顯示應同一般分行)
 *          </ul>
 */
@Scope("request")
@Controller("lms7010formhandler")
public class LMS7010FormHandler extends AbstractFormHandler {

	@Resource
	LMS7010Service service;

	@Resource
	BranchService branch;

	@Resource
	UserInfoService userinfoservice;

	/**
	 * 下拉式選單控制(只顯示同分行行員)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBrnoData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String brno = params.getString("brno");
		// 登入者所屬分行
		result.set("itemBranch", brno + "-" + branch.getBranchName(brno));
		// 經辦人員

		Map<String, String> allPeople = userinfoservice.getBRUserName(brno);
		CapAjaxFormResult allPeopleMap = new CapAjaxFormResult(allPeople);
		result.set("userNo", allPeopleMap);
		// 主管
		SignEnum[] sign = { SignEnum.甲級主管, SignEnum.單位主管, SignEnum.乙級主管 };
		Map<String, String> mainPeople = userinfoservice.findByBrnoAndSignId(
				brno, sign);
		CapAjaxFormResult mainPeopleMap = new CapAjaxFormResult(mainPeople);
		result.set("zgNo", mainPeopleMap);

		// Vector @ 20130705 修正unitType=K的分行顯示異常問題(顯示應同一般分行)
		result.set("unitType", branch.getBranch(brno).getUnitType());

		return result;
	}

	/**
	 * 刪除L800m01a grid資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult deleteL800m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String oid = params.getString("oid");
		service.deleteL800m01a(oid);
		return result;
	}// ;

	/**
	 * 儲存L800m01a(含修改)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveL800m01a(PageParameters params)
			throws CapException {
		// 取得當前使用者資料
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();
		// 接收AJAX
		String oid = Util.trim(params.getString("oid"));
		String zhuGuan = Util.trim(params.getString("zhuGuan")).split(" ")[0];
		String zgName = Util.trim(params.getString("zgName"));

		String brno = user.getUnitNo();

		if (Util.isEmpty(oid) && isDuplicate(zhuGuan, brno)) {
			result.set("error", "dup");
		} else {
			L800M01A l800m01a = service.findL800m01aByOid(oid);
			if (l800m01a == null) {// 無舊資料=>新增
				l800m01a = new L800M01A();
				l800m01a.setBrno(brno);
			}
			l800m01a.setZGName(zgName);
			l800m01a.setZhuGuan(zhuGuan);
			l800m01a.setDataType(Util.trim(params.getString("dataType")));
			l800m01a.setIsType1(params.getString("isType1"));
			l800m01a.setIsType2(params.getString("isType2"));
			l800m01a.setIsType3(params.getString("isType3"));
			l800m01a.setIsType4(params.getString("isType4"));
			l800m01a.setIsType5(params.getString("isType5"));
			// 存檔
			service.save(l800m01a);
		}
		return result;
	}

	private boolean isDuplicate(String zhuGuan, String brno) {
		List<L800M01A> existZhuGuan = service.findL800m01aByZhuGuan(zhuGuan,
				brno);
		if (existZhuGuan != null && existZhuGuan.size() > 0)
			return true;
		else
			return false;
	}
}
