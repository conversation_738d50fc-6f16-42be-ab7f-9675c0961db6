/* 
 * LMS9015M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.form;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.fms.service.LMS9015Service;
import com.mega.eloan.lms.model.L901M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 動審表稽核項目
 * </pre>
 * 
 * @since 2011/11/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/22,jessica,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9015m01formhandler")
public class LMS9015M01FormHandler extends AbstractFormHandler {

	@Resource
	LMS9015Service lms9015Service;

	/**
	 * 查詢
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		if (!CapString.isEmpty(oid)) {
			L901M01A l901m01a = lms9015Service.findByOid(oid);
			if (l901m01a != null) {
				result = DataParse.toResult(l901m01a);
			}
		}
		result.set(EloanConstants.MAIN_OID, CapString.trimNull(oid));
		return result;

	}// ;

	/**
	 * 儲存
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult save(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String oid = params.getString(EloanConstants.MAIN_OID);
		String form = params.getString("lms9015tabForm");
		L901M01A l901m01a = lms9015Service.findByOid(oid);
		String itemType = "";
		// 1.全行共同項目(總行維護) 、 2.當地特殊規定項目(海外各分行自行維護)
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
			itemType = UtilConstants.Usedoc.itemType.全行共同項目;
		} else {
			itemType = UtilConstants.Usedoc.itemType.當地特殊規定項目;
		}
		if (l901m01a == null) {
			l901m01a = new L901M01A();
			l901m01a.setCreateTime(CapDate.getCurrentTimestamp());
			l901m01a.setCreator(user.getUserId());
			l901m01a.setBranchId(user.getUnitNo());
			l901m01a.setItemSeq(lms9015Service.findL9010m01fByItemSeqMax(
					itemType, user.getUnitNo()));
			// 1.全行共同項目(總行維護) 、 2.當地特殊規定項目(海外各分行自行維護)
			l901m01a.setItemType(itemType);
		}
		DataParse.toBean(form, l901m01a);
		lms9015Service.save(l901m01a);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	/**
	 * 刪除
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteList(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));
		L901M01A l901m01a = lms9015Service.findByOid(oid);
		lms9015Service.delete(l901m01a);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;
	}// ;

}
