/* 
 * ELF500.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 * 在部份BigDecimal後面加了.setScale(0)是因為帳務主機有二位小數但LMS沒有小數
 */
 
package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;


/** 消金額度介面檔 <br/>
	● 在 notes e-Loan 時，是上傳到 MIS.PQUOTAPP ( MIS.ELF386 ) <br/>
	－－－－－－－－－－－－－－－　<br/>
  	註：企金動審 MIS.QUOTAPPR ( ELF383 )
*/

public class ELF500 extends GenericBean{

	private static final long serialVersionUID = 1L;

	
	/** 
	 * 借款人身分證統一編號<p/>
	 * LNF020_CUST_ID<br/>
	 *  左邊10位
	 */
	//刪除 nullable=false,unique = true
	@Column(name="ELF500_CUSTID", length=10, columnDefinition="CHAR(10)")
	private String elf500_custid;

	/** 
	 * 重複序號<p/>
	 * LNF020_CUST_ID<br/>
	 *  右邊1位
	 */
	@Column(name="ELF500_DUPNO", length=1, columnDefinition="CHAR(1)")
	private String elf500_dupno;

	/** 
	 * 本件額度序號<p/>
	 * LNF020_CONTRACT
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="ELF500_CNTRNO", length=12, columnDefinition="CHAR(12)", nullable=false,unique = true)
	private String elf500_cntrno;

	/** 
	 * 額度幣別<p/>
	 * LNF020_SWFT
	 */
	@Column(name="ELF500_SWFT", length=3, columnDefinition="CHAR(3)")
	private String elf500_swft;

	/** 
	 * 核准額度<p/>
	 * LNF020_FACT_AMT
	 */
	@Column(name="ELF500_FACTAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf500_factamt;

	/** 
	 * 額度控管種類<p/>
	 * LNF020_FACT_TYPE
	 */
	@Column(name="ELF500_FACTTYPE", length=2, columnDefinition="CHAR(2)")
	private String elf500_facttype;

	/** 
	 * 信保成數<p/>
	 * LNF020_IPFD_RATE
	 */
	@Column(name="ELF500_CINSPENT", columnDefinition="DECIMAL(5,2)")
	private BigDecimal elf500_cinspent;

	/** 
	 * 動用期限－代碼<p/>
	 * LNF020_DUE_CODE
	 */
	@Column(name="ELF500_LNUSENO", length=1, columnDefinition="CHAR(01)")
	private String elf500_lnuseno;

	/** 
	 * 動用期限－起始日期<p/>
	 * LNF020_BEG_DATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="ELF500_USEFMDT", columnDefinition="DATE")
	private Date elf500_usefmdt;

	/** 
	 * 動用期限－終止日期<p/>
	 * LNF020_END_DATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="ELF500_USEENDT", columnDefinition="DATE")
	private Date elf500_useendt;

	/** 
	 * 動用期限－自首次動用日起月<p/>
	 * LNF020_DUE_MM
	 */
	@Column(name="ELF500_USEFTMN", columnDefinition="DECIMAL(03)")
	private Integer elf500_useftmn;

	/** 
	 * 授信期間－代碼<p/>
	 * LNF020_DURATION_CD
	 */
	@Column(name="ELF500_LLNNO", length=1, columnDefinition="CHAR(01)")
	private String elf500_llnno;

	/** 
	 * 授信期間(起)<p/>
	 * LNF020_DURATION_BG
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="ELF500_CNTFROM", columnDefinition="DATE")
	private Date elf500_cntfrom;

	/** 
	 * 授信期間(迄)<p/>
	 * LNF020_DURATION_ED
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="ELF500_CNTEND", columnDefinition="DATE")
	private Date elf500_cntend;

	/** 
	 * 授信期間－月<p/>
	 * LNF020_DURATION_MM
	 */
	@Column(name="ELF500_LLNMON", columnDefinition="DECIMAL(03)")
	private Integer elf500_llnmon;

	/** 
	 * 授權等級<p/>
	 * LNF020_AUTHO_LEVEL
	 */
	@Column(name="ELF500_GRANTNO", length=1, columnDefinition="CHAR(1)")
	private String elf500_grantno;

	/** 
	 * 循環<p/>
	 * LNF020_REVOLVE
	 */
	@Column(name="ELF500_LNCICL", length=1, columnDefinition="CHAR(1)")
	private String elf500_lncicl;

	/** 
	 * 共同借款人註記<p/>
	 * LNF020_COLN_FLAG
	 */
	@Column(name="ELF500_COBORFLG", length=1, columnDefinition="CHAR(1)")
	private String elf500_coborflg;

	/** 
	 * 簽案時利害關係人註記{Y, N, A, B} LNF020_RELATE{Y, N}
	 * 在 J-105-0250  增加  ELF500_UNSECUREFLA 及 LNF020_UNSECUREFLA
	 * 上線之後的案件, elf500_relate上傳{A:利害關係人}, {B:非利害關係人}, 和之前的Y/N區別
	 * 上線之前的舊案【利害關係人 且 無擔科目(排除321) 且UnsecureFlag=''】, elf500_relate 仍上傳Y/N
	 */
	@Column(name="ELF500_RELATE", length=1, columnDefinition="CHAR(1)")
	private String elf500_relate;

	/** 
	 * 不計入同一關係戶代號<p/>
	 * LNF020_IDENTITY_NO不計入同一關係戶代號<br/>
	 *  1:配合政府政策經財政部或央行核准之專案授信<br/>
	 *  2:對政府機關之授信<br/>
	 *  3:以公債國庫券央行儲券可轉讓存單本行存單或債券<br/>
	 *  4:推動小額放款業務要點之台幣一百萬元以下之授信<br/>
	 *  5:配合政府不列同一人限額但需列入同一關係人企業
	 */
	@Column(name="ELF500_IDENTITY_NO", length=1, columnDefinition="CHAR(01)")
	private String elf500_identity_no;

	/** 
	 * 風險國別<p/>
	 * LNF020_RISK_AREA
	 */
	@Column(name="ELF500_RISK_AREA", length=2, columnDefinition="CHAR(02)")
	private String elf500_risk_area;

	/** 
	 * ＤＢＲ２２倍無擔保額度<p/>
	 * LNF020_DBR22_FACT
	 */
	@Column(name="ELF500_DBR22_FACT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf500_dbr22_fact;

	/** 
	 * 單據寄發方式(對帳單)<p/>
	 * LNF020_NOTICE_TYPE<br/>
	 *  1:郵寄通訊地址<br/>
	 *  9:不印授信對帳單
	 */
	@Column(name="ELF500_NOTICE_TYPE", length=1, columnDefinition="CHAR(01)")
	private String elf500_notice_type;

	/** 
	 * 契約核准編號(簽報書或審核書的案號)<p/>
	 * LNF020_DOCUMENT_NO
	 */
	@Column(name="ELF500_DOCUMENT_NO", length=20, columnDefinition="CHAR(20)")
	private String elf500_document_no;

	/** 
	 * 團貸總戶之額度序號<p/>
	 * LNF020-GRP-CNTRNO
	 */
	@Column(name="ELF500_GRP_CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String elf500_grp_cntrno;

	/** 
	 * 服務公司/建設公司統編<p/>
	 * LNF033_GRP_NO
	 */
	@Column(name="ELF500_CMPID", length=11, columnDefinition="CHAR(11)")
	private String elf500_cmpid;

	/** 
	 * 批號<p/>
	 * LNF033_GRP_SEQ
	 */
	@Column(name="ELF500_LOTNO", length=4, columnDefinition="CHAR(4)")
	private String elf500_lotno;

	/** 青創同一事業體統編 **/
	@Column(name="ELF500_COMPANY_ID", length=10, columnDefinition="CHAR(10)")
	private String elf500_company_id;

	/** 青創同一事業體名稱(長度由30放大為60) **/
	@Column(name="ELF500_COMPANY_NM", length=60, columnDefinition="CHAR(60)")
	private String elf500_company_nm;

	/** 青創事業體縣/市欄位 **/
	@Column(name="ELF500_COMPANY_AREA", length=3, columnDefinition="CHAR(03)")
	private String elf500_company_area;

	/** 設立登記日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF500_REGISTER_DT", columnDefinition="DATE")
	private Date elf500_register_dt;

	/** 登記出資額 **/
	@Column(name="ELF500_CAPITAL_AMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf500_capital_amt;

	/** 育成輔導註記 **/
	@Column(name="ELF500_ASSIST_FLAG", length=1, columnDefinition="CHAR(1)")
	private String elf500_assist_flag;

	/** 職業別 **/
	@Column(name="ELF500_POS", length=4, columnDefinition="CHAR(04)")
	private String elf500_pos;

	/** 
	 * 年薪<p/>
	 * 萬元
	 */
	@Column(name="ELF500_YPAY", columnDefinition="DECIMAL(5,0)")
	private Integer elf500_ypay;

	/** 
	 * 家庭所得<p/>
	 * 萬元
	 */
	@Column(name="ELF500_HINCOME", columnDefinition="DECIMAL(5,0)")
	private Integer elf500_hincome;

	/** 
	 * 其他所得<p/>
	 * 萬元
	 */
	@Column(name="ELF500_OMONEY", columnDefinition="DECIMAL(5,0)")
	private Integer elf500_omoney;

	/** 
	 * 資料維護狀態{0:引進中 , 1:已覆核 , 2:解控 , 3:刪除 , 4:已開戶 , 9:行家理財變更}                               
	 */
	@Column(name="ELF500_STATUS", length=1, columnDefinition="CHAR(1)")
	private String elf500_status;

	/** 
	 * 央行控管類別1/2/3/4<p/>
	 * LNF087_CONTROL_CD
	 */
	@Column(name="ELF500_CONTROL_CD", length=1, columnDefinition="CHAR(1)")
	private String elf500_control_cd;

	/** 
	 * JCIC已有購屋融資註記Y一戶/N無/B二戶(含)以上<p/>
	 * LNF087_JCIC_MARK
	 */
	@Column(name="ELF500_JCIC_MARK", length=1, columnDefinition="CHAR(1)")
	private String elf500_jcic_mark;

	/** 
	 * 個人:「住」或「住商」無營業註記
法人:建物謄本登記用途是否屬「住」類(Y/N)<p/>
	 * LNF087_REG_PURPOSE
	 */
	@Column(name="ELF500_REG_PURPOSE", length=1, columnDefinition="CHAR(1)")
	private String elf500_reg_purpose;

	/** 
	 * 擔保品坐落區<p/>
	 * LNF087_LOCATION_CD
	 */
	@Column(name="ELF500_LOCATION_CD", length=3, columnDefinition="CHAR(3)")
	private String elf500_location_cd;

	/** 
	 * 擔保品購價或時價<p/>
	 * LNF087_APP_AMT
	 */
	@Column(name="ELF500_APP_AMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf500_app_amt;

	/** 擔保品鑑價 **/
	@Column(name="ELF500_EST_AMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf500_est_amt;

	/** 擔保品估價 **/
	@Column(name="ELF500_LAWVAL", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf500_lawval;

	/** 
	 * 貸放成數<p/>
	 * LNF087_LTV_RATE
	 */
	@Column(name="ELF500_LTV_RATE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal elf500_ltv_rate;

	/** 
	 * 有無與其他額度共用擔保品<p/>
	 * LNF087_COCOLL_FG
	 */
	@Column(name="ELF500_COCOLL_FG", length=1, columnDefinition="CHAR(1)")
	private String elf500_cocoll_fg;

	/** 
	 * 有無授權外核准得分批動用<p/>
	 * LNF087_PART_FUND
	 */
	@Column(name="ELF500_PART_FUND", length=1, columnDefinition="CHAR(1)")
	private String elf500_part_fund;

	/** 
	 * 共用同一擔保品總額度金額<p/>
	 * LNF087_SUM_FACTAMT
	 */
	@Column(name="ELF500_SUM_FACTAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf500_sum_factamt;

	/** 是否為受限戶(Y/N) **/
	@Column(name="ELF500_RESTRICT", length=1, columnDefinition="CHAR(1)")
	private String elf500_restrict;

	/** 是否為高價住宅(Y/N) **/
	@Column(name="ELF500_HP_HOUSE", length=1, columnDefinition="CHAR(1)")
	private String elf500_hp_house;

	/** 是否屬都市計畫劃定之區域(Y/N) **/
	@Column(name="ELF500_PLAN_AREA", length=1, columnDefinition="CHAR(1)")
	private String elf500_plan_area;

	/** 
	 * 都市計畫劃定之使用分區<p/>
	 * 1住宅區<br/>
	 *  2商業區<br/>
	 *  3其他
	 */
	@Column(name="ELF500_P_USETYPE", length=1, columnDefinition="CHAR(1)")
	private String elf500_p_usetype;

	/** 
	 * 借款用途<p/>
	 * 1興建房屋<br/>
	 *  2自然人理財週轉<br/>
	 *  3營運週轉
	 */
	@Column(name="ELF500_P_LOANUSE", length=1, columnDefinition="CHAR(1)")
	private String elf500_p_loanuse;

	/** 
	 * 擔保品性質別<p/>
	 * 1房地建地<br/>
	 *  2空地<br/>
	 *  3其他
	 */
	@Column(name="ELF500_COLL_CHAR", length=1, columnDefinition="CHAR(1)")
	private String elf500_coll_char;

	/** 保留一成估值(Y/N) **/
	@Column(name="ELF500_KEEP_LAWVAL", length=1, columnDefinition="CHAR(1)")
	private String elf500_keep_lawval;

	/** 
	 * 央行控管種類4的理由<p/>
	 * LNF087_PLUS_REASON<br/>
	 *  ○建物所有權取得日期在99/6/25以前(自然人使用)<br/>
	 *  ○建物所有權取得日期在99/12/31以前(公司法人使用)<br/>
	 *  ○建物登記用途無「住」字樣、或「住商」類等有營業登記<br/>
	 *  ○擔保品非屬建物<br/>
	 *  ○非屬購屋貸款<br/>
	 *  ○轉換產品種類<br/>
	 *  ○其他（　　選其他者必key理由，約20字左右）
	 */
	@Column(name="ELF500_PLUS_REASON", length=1, columnDefinition="CHAR(1)")
	private String elf500_plus_reason;

	/** 非屬央行填報對象理由 **/
	@Column(name="ELF500_PLUS_MEMO", length=62, columnDefinition="CHAR(62)")
	private String elf500_plus_memo;

	/** 
	 * 編輯序號<p/>
	 * N
	 */
	@Column(name="ELF500_EDITNO", length=2, columnDefinition="CHAR(2)")
	private String elf500_editno;

	/** ELOAN寫入時間 **/
	@Column(name="ELF500_ELOANTIMES", columnDefinition="TIMESTAMP")
	private Date elf500_eloantimes;

	/** 寫入時間 **/
	@Column(name="ELF500_TMESTAMP", columnDefinition="TIMESTAMP")
	private Date elf500_tmestamp;
	
	/** 
	 * ALOAN寫入時間<p/>
	 * N
	 */
	@Column(name="ELF500_ALOANTIMES", columnDefinition="TIMESTAMP")
	private Date elf500_aloantimes;

	/** 櫃員代號 **/
	@Column(name="ELF500_TELLER", length=6, columnDefinition="CHAR(06)")
	private String elf500_teller;

	/** 主管代號 **/
	@Column(name="ELF500_SUPVNO", length=6, columnDefinition="CHAR(06)")
	private String elf500_supvno;

	/** 
	 * 擔保品座落-段<p/>
	 * Ex:0205鳳林
	 */
	@Column(name="ELF500_SITE3NO", columnDefinition="DECIMAL(4,0)")
	private Integer elf500_site3no;

	/** 
	 * 擔保品座落-村<p/>
	 * Ex:1000305-006蘆竹村
	 */
	@Column(name="ELF500_SITE4NO", length=11, columnDefinition="CHAR(11)")
	private String elf500_site4no;

	/** 擔保品性質別3其他之說明 **/
	@Column(name="ELF500_COLL_CHAR_M", length=20, columnDefinition="CHAR(20)")
	private String elf500_coll_char_m;
	
	/** 團貸職工編號 **/
	@Column(name="ELF500_STAFFNO", length=20, columnDefinition="CHAR(20)")
	private String elf500_staffno;	
	
	/** 青創申請日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF500_APPLY_DATE", columnDefinition="DATE")
	private Date elf500_apply_date;
	
	/** 
	 * 時價<p/>
	 */
	@Column(name="ELF500_TIMEVAL", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf500_timeval;
	
	/**
	 * 學歷
	 * @return
	 */
	@Column(name="ELF500_EDU", length=2, columnDefinition="CHAR(02)")
	private String elf500_edu;
	
	/**
	 * 婚姻狀況
	 * @return
	 */
	@Column(name="ELF500_MARRY", length=1, columnDefinition="CHAR(01)")
	private String elf500_marry;
	
	/**
	 * 子女數
	 * @return
	 */
	@Column(name="ELF500_CHILD", columnDefinition="DECIMAL(2,0)")
	private Integer elf500_child;
	
	/**
	 * 年資
	 * @return
	 */
	@Column(name="ELF500_SENIORITY", columnDefinition="DECIMAL(4,2)")
	private BigDecimal elf500_seniority;
	
	/**
	 * 個人負債比率
	 */
	@Column(name="ELF500_DRATE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal elf500_drate;
	
	
	/**
	 * 家庭負債比率
	 */
	@Column(name="ELF500_YRATE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal elf500_yrate;
	
	/**
	 * 使用信用卡循環信用或現金卡情形
	 */	
	@Column(name="ELF500_CREDIT", length=3, columnDefinition="CHAR(03)")
	private String elf500_credit;
	
	/**
	 * 是否於本行財富管理有定時定額扣款
	 */
	@Column(name="ELF500_ISPERIODFUND", length=1, columnDefinition="CHAR(01)")
	private String elf500_isperiodfund;
	
	/**
	 * 與本行其他業務往來(財富管理業務如基金保險信用卡等)
	 */
	@Column(name="ELF500_BUSI", length=1, columnDefinition="CHAR(01)")
	private String elf500_busi;
	
	/**
	 * 與本行財富管理三個月平均總資產(幣別)
	 */
	@Column(name="ELF500_INVMBALCURR", length=3, columnDefinition="CHAR(03)")
	private String elf500_invmbalcurr;
	
	/**
	 * 與本行財富管理三個月平均總資產(金額)
	 */
	@Column(name="ELF500_INVMBALAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal elf500_invmbalamt;
	
	/**
	 * 與他行財富管理三個月平均總資產(幣別)
	 */
	@Column(name="ELF500_INVOBALCURR", length=3, columnDefinition="CHAR(03)")
	private String elf500_invobalcurr;
	
	/**
	 * 與他行財富管理三個月平均總資產(金額)
	 */
	@Column(name="ELF500_INVOBALAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal elf500_invobalamt;
	
	/**
	 * 與金融機構存款往來情形(近六個月平均餘額)(幣別)
	 */
	@Column(name="ELF500_BRANCURR", length=3, columnDefinition="CHAR(03)")
	private String elf500_brancurr;
	
	/**
	 * 與金融機構存款往來情形(近六個月平均餘額)(金額)
	 */
	@Column(name="ELF500_BRANAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal elf500_branamt;
	
	/**
	 * 不動產狀況
	 */
	@Column(name="ELF500_CMSSTATUS", length=1, columnDefinition="CHAR(01)")
	private String elf500_cmsstatus;		
	
	/** 聯徵查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF500_EJCICQDATE", columnDefinition="DATE")
	private Date elf500_ejcicqdate;
	
	/**
	 * 以基金贖回款為還款來源之授信
	 */
	@Column(name="ELF500_REPAYFUND", length=1, columnDefinition="CHAR(01)")
	private String elf500_repayfund;

	/**
	 * L56A引進選項
	 */
	@Column(name="ELF500_L56A_FUNC", length=1, columnDefinition="CHAR(01)")
	private String elf500_l56a_func;
	
	/**
	 * 約定融資額度註記
	 */
	@Column(name="ELF500_EXCEPT", length=1, columnDefinition="CHAR(01)")
	private String elf500_except;
	
	/**
	 * 利害關係人可做無擔保註記（當LNF020_RELATE=Y時，允許承做無擔的原因）
	 */
	@Column(name="ELF500_UNSECUREFLA", length=1, columnDefinition="CHAR(01)")
	private String elf500_unsecurefla;
	
	/**
	 * ELF500 無效
	 */
	@Column(name="ELF500_NOTVALID", length=1, columnDefinition="CHAR(01)")
	private String elf500_notvalid;
	
	/**
	 * 專案種類{05:新創產業}  
	 */
	@Column(name="ELF500_PROJ_CLASS", length=2, columnDefinition="CHAR(02)")
	private String elf500_proj_class;
	
	/**
	 * 專案種類細項{AA:綠能科技 , BB:亞洲矽谷 , CC:生技醫療 , DD:國防產業 , EE:智慧機械 , FF:新農業 , GG:循環經濟}
	 */
	@Column(name="ELF500_ITW_CODE", length=2, columnDefinition="CHAR(02)")
	private String elf500_itw_code;

	/** 擔保品屋齡 **/
	@Column(name="ELF500_HOUSE_AGE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal elf500_house_age; 

	/** 擔保品放款值 **/
	@Column(name="ELF500_LOANAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf500_loanAmt;
	
	/**
	 * 引介行員代號
	 */
	@Column(name="ELF500_MEGA_EMPNO", length=6, columnDefinition="CHAR(06)")
	private String elf500_mega_empno;
	
	/**
	 * 引介房仲代號{11111:永慶, 22222:信義, 55555:住商} select * from com.bcodetype where codetype ='L140M01A_agntNo'
	 */
	@Column(name="ELF500_AGNT_NO", length=5, columnDefinition="CHAR(05)")
	private String elf500_agnt_no;
	
	/**
	 * 引介房仲連鎖店類型{A:直營店, B:加盟店}
	 */
	@Column(name="ELF500_AGNT_CHAIN", length=1, columnDefinition="CHAR(01)")
	private String elf500_agnt_chain;
	
	/**
	 * 引介子公司代號{90001, 90002...} select * from com.bcodetype where codetype='L140S02A_megaCode'
	 */
	@Column(name="ELF500_MEGA_CODE", length=5, columnDefinition="CHAR(05)")
	private String elf500_mega_code;
	
	/**
	 * 引介子公司分支代號
	 */
	@Column(name="ELF500_SUB_UNITNO", length=5, columnDefinition="CHAR(05)")
	private String elf500_sub_unitno;
	
	/**
	 * 引介子公司員工編號
	 */
	@Column(name="ELF500_SUB_EMPNO", length=6, columnDefinition="CHAR(6)")
	private String elf500_sub_empno;
	
	/**
	 * 引介子公司員工姓名
	 */
	@Column(name="ELF500_SUB_EMPNM", length=22, columnDefinition="CHAR(22)")
	private String elf500_sub_empnm;

	/**
	 * 扣薪福委會代號          select * from com.bcodetype where codeType in ('grpCntrNo_TPC_321', 'TPCWelfareCommitteeNo')
	 */
	@Column(name="ELF500_WELFARE_CMTE", length=3, columnDefinition="CHAR(3)")
	private String elf500_welfare_cmte;
	
	/**
	 * 為紀錄央行於2020-12-08對辦理不動產抵押貸款業務版本
	 */
	@Column(name = "ELF500_VERSION", columnDefinition = "VARCHAR(20)")
	private String elf500_version;
	
	
	/**
	 * 央行於2020-12-08對辦理不動產抵押貸款業務規定項目
	 */
	@Column(name = "ELF500_HLOANLIMIT", columnDefinition = "CHAR(1)")
	private String elf500_hLoanLimit;
	
	/**
	 * 引介來源
	 */
	@Column(name = "ELF500_INTRO_SRC", columnDefinition = "CHAR(1)")
	private String elf500_intro_src;
	
	/**
	 * 引介房仲是否收取回饋金
	 */
	@Column(name = "ELF500_AGNTFBFG", columnDefinition = "CHAR(1)")
	private String elf500_agntfbfg;
	
	/**
	 * 青創同一事業體行業代碼   misSQL.xml="BSTBL.findByECOCD" , SELECT ECOCD, ECONM FROM MIS.BSTBL WHERE ECOCD =? <br/> 
	 * 	或是 參考0024的查詢方式：  前2碼{01:民營企業, 02:公營企業} + 後4碼 http://web02.mof.gov.tw/std/search10.htm
	 */
	@Column(name = "ELF500_CMBULL_KEY", columnDefinition = "CHAR(6)")
	private String elf500_cmbull_key;
	
	/**
	 * 行銷註記
	 */
	@Column(name = "ELF500_MARKET_NOTE", columnDefinition = "VARCHAR(50)")
	private String elf500_market_note;
	
	/**
	 * 疑似整批房貸案件原因
	 */
	@Column(name = "ELF500_SUSPLN_RSON", columnDefinition = "VARCHAR(2)")
	private String elf500_suspln_rson;
	
	/**
	 * 疑似整批房貸案件註記
	 */
	@Column(name = "ELF500_SUSPLN_FLAG", columnDefinition = "CHAR(1)")
	private String elf500_suspln_flag;
	
	/**
	 * 疑似整批房貸案件額度序號
	 */
	@Column(name = "ELF500_SUSPLN_CNTR", columnDefinition = "VARCHAR(100)")
	private String elf500_suspln_cntr;
	
	/**
	 * 是否為購置第3戶(含)以上高價住宅
	 */
	@Column(name = "ELF500_HLOANLIMIT_2", columnDefinition = "CHAR(01)")
	private String elf500_hLoanLimit_2;
	
	/**
	 * elf447n核准日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="ELF500_ENDDATE", columnDefinition="DATE")
	private Date elf500_endDate;
	
	/**
	 * 是否續約/提前續約
	 */
	@Column(name = "ELF500_ISRENEW", columnDefinition = "CHAR(1)")
	private String elf500_isRenew;
	
	/**
	 * 是否以新額度償還舊額度(含轉貸)	
	 */
	@Column(name = "ELF500_ISPAY_OLD", columnDefinition = "CHAR(1)")
	private String elf500_isPay_old;
	
	/** 原(舊)額度 **/
	@Column(name="ELF500_OLD_QUOTA", columnDefinition="DECIMAL(13,0)")
	private BigDecimal elf500_old_quota;
	
	/** 本額度償還原(舊)額度之金額	 **/
	@Column(name="ELF500_PAY_OLD_AMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal elf500_pay_old_amt;
	
	/**
	 * 償還之舊額度類型	
	 */
	@Column(name = "ELF500_PAY_OLD_ITEM", columnDefinition = "CHAR(1)")
	private String elf500_pay_old_item;
	
	/**
	 * 是否符合餘屋貸款排除控管事項	
	 */
	@Column(name = "ELF500_ISMATCH_ITEM", columnDefinition = "CHAR(1)")
	private String elf500_isMatch_item;
	
	/**
	 * 是否符合餘屋貸款排除控管事項	
	 */
	@Column(name = "ELF500_ISSALE_CASE", columnDefinition = "CHAR(1)")
	private String elf500_isSale_case;
	
	/**
	 * 央行購住最新預計動工日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF500_LSTDATE", columnDefinition = "DATE")
	private Date elf500_lstDate;
	
	/** 
	 * 信保基金核准之保證手續費率<p/>
	 */
	@Column(name="ELF500_CGF_RATE", columnDefinition="DECIMAL(6,4)")
	private BigDecimal elf500_cgf_rate;


	@Column(name = "ELF500_SIXCOR_CODE", columnDefinition = "CHAR(2)")
	private String elf500_sixcor_code;
	
	/** 
	 * 資料來源 IL:ILOAN (NOT NULL WITH DEFAULT EL)<p/>
	 */
	@Column(name="ELF500_DT_SOURCE", columnDefinition="CHAR(2)")
	private String elf500_dt_source;
	
	/** 
	 * 資料狀態 NOT NULL WITH DEFAULT 2
	 * 1:簽報核准
	 * 2:動審核准
	 * 非1時，ALOAN視為動審核准，可以引進<p/>
	 */
	@Column(name="ELF500_DT_STATUS", columnDefinition="CHAR(1)")
	private String elf500_dt_status;
	
	/**
	 * 家庭所得(萬元)
	 */
	@Column(name="ELF500_FINCOME", columnDefinition="DECIMAL(5,0)")
	private Integer elf500_fincome;
		
	/** 
	 * J-113-0323配合0403花蓮震災新增房貸信保註記
	 * 房貸信保註記
	 * H:1.產品種類63、64+災害種類05且要申請房貸信保時使用
	 *   2.要申請房貸信保，科目為403/603(產品種類不限)，並搭配申請63、64+災害種類05時使用
	 * R:1.產品種類65+災害種類05且要申請房貸信保時使用
	 *   2.要申請房貸信保，科目為403/603(產品種類不限)，並搭配申請65+災害種類05時使用
	 */
	@Column(name="ELF500_SYND_IPFD", columnDefinition="CHAR(1)")
	private String elf500_synd_ipfd;
	
	/** 
	 * 取得借款人身分證統一編號<p/>
	 * LNF020_CUST_ID<br/>
	 *  左邊10位
	 */
	public String getElf500_custid() {
		return this.elf500_custid;
	}
	/**
	 *  設定借款人身分證統一編號<p/>
	 *  LNF020_CUST_ID<br/>
	 *  左邊10位
	 **/
	public void setElf500_custid(String value) {
		this.elf500_custid = value;
	}

	/** 
	 * 取得重複序號<p/>
	 * LNF020_CUST_ID<br/>
	 *  右邊1位
	 */
	public String getElf500_dupno() {
		return this.elf500_dupno;
	}
	/**
	 *  設定重複序號<p/>
	 *  LNF020_CUST_ID<br/>
	 *  右邊1位
	 **/
	public void setElf500_dupno(String value) {
		this.elf500_dupno = value;
	}

	/** 
	 * 取得本件額度序號<p/>
	 * LNF020_CONTRACT
	 */
	public String getElf500_cntrno() {
		return this.elf500_cntrno;
	}
	/**
	 *  設定本件額度序號<p/>
	 *  LNF020_CONTRACT
	 **/
	public void setElf500_cntrno(String value) {
		this.elf500_cntrno = value;
	}

	/** 
	 * 取得額度幣別<p/>
	 * LNF020_SWFT
	 */
	public String getElf500_swft() {
		return this.elf500_swft;
	}
	/**
	 *  設定額度幣別<p/>
	 *  LNF020_SWFT
	 **/
	public void setElf500_swft(String value) {
		this.elf500_swft = value;
	}

	/** 
	 * 取得核准額度<p/>
	 * LNF020_FACT_AMT
	 */
	public BigDecimal getElf500_factamt() {
		return this.elf500_factamt;
	}
	/**
	 *  設定核准額度<p/>
	 *  LNF020_FACT_AMT
	 **/
	public void setElf500_factamt(BigDecimal value) {
		this.elf500_factamt = value;
	}

	/** 
	 * 取得額度控管種類<p/>
	 * LNF020_FACT_TYPE
	 */
	public String getElf500_facttype() {
		return this.elf500_facttype;
	}
	/**
	 *  設定額度控管種類<p/>
	 *  LNF020_FACT_TYPE
	 **/
	public void setElf500_facttype(String value) {
		this.elf500_facttype = value;
	}

	/** 
	 * 取得信保成數<p/>
	 * LNF020_IPFD_RATE
	 */
	public BigDecimal getElf500_cinspent() {
		return this.elf500_cinspent;
	}
	/**
	 *  設定信保成數<p/>
	 *  LNF020_IPFD_RATE
	 **/
	public void setElf500_cinspent(BigDecimal value) {
		this.elf500_cinspent = value;
	}

	/** 
	 * 取得動用期限－代碼<p/>
	 * LNF020_DUE_CODE
	 */
	public String getElf500_lnuseno() {
		return this.elf500_lnuseno;
	}
	/**
	 *  設定動用期限－代碼<p/>
	 *  LNF020_DUE_CODE
	 **/
	public void setElf500_lnuseno(String value) {
		this.elf500_lnuseno = value;
	}

	/** 
	 * 取得動用期限－起始日期<p/>
	 * LNF020_BEG_DATE
	 */
	public Date getElf500_usefmdt() {
		return this.elf500_usefmdt;
	}
	/**
	 *  設定動用期限－起始日期<p/>
	 *  LNF020_BEG_DATE
	 **/
	public void setElf500_usefmdt(Date value) {
		this.elf500_usefmdt = value;
	}

	/** 
	 * 取得動用期限－終止日期<p/>
	 * LNF020_END_DATE
	 */
	public Date getElf500_useendt() {
		return this.elf500_useendt;
	}
	/**
	 *  設定動用期限－終止日期<p/>
	 *  LNF020_END_DATE
	 **/
	public void setElf500_useendt(Date value) {
		this.elf500_useendt = value;
	}

	/** 
	 * 取得動用期限－自首次動用日起月<p/>
	 * LNF020_DUE_MM
	 */
	public Integer getElf500_useftmn() {
		return this.elf500_useftmn;
	}
	/**
	 *  設定動用期限－自首次動用日起月<p/>
	 *  LNF020_DUE_MM
	 **/
	public void setElf500_useftmn(Integer value) {
		this.elf500_useftmn = value;
	}

	/** 
	 * 取得授信期間－代碼<p/>
	 * LNF020_DURATION_CD
	 */
	public String getElf500_llnno() {
		return this.elf500_llnno;
	}
	/**
	 *  設定授信期間－代碼<p/>
	 *  LNF020_DURATION_CD
	 **/
	public void setElf500_llnno(String value) {
		this.elf500_llnno = value;
	}

	/** 
	 * 取得授信期間(起)<p/>
	 * LNF020_DURATION_BG
	 */
	public Date getElf500_cntfrom() {
		return this.elf500_cntfrom;
	}
	/**
	 *  設定授信期間(起)<p/>
	 *  LNF020_DURATION_BG
	 **/
	public void setElf500_cntfrom(Date value) {
		this.elf500_cntfrom = value;
	}

	/** 
	 * 取得授信期間(迄)<p/>
	 * LNF020_DURATION_ED
	 */
	public Date getElf500_cntend() {
		return this.elf500_cntend;
	}
	/**
	 *  設定授信期間(迄)<p/>
	 *  LNF020_DURATION_ED
	 **/
	public void setElf500_cntend(Date value) {
		this.elf500_cntend = value;
	}

	/** 
	 * 取得授信期間－月<p/>
	 * LNF020_DURATION_MM
	 */
	public Integer getElf500_llnmon() {
		return this.elf500_llnmon;
	}
	/**
	 *  設定授信期間－月<p/>
	 *  LNF020_DURATION_MM
	 **/
	public void setElf500_llnmon(Integer value) {
		this.elf500_llnmon = value;
	}

	/** 
	 * 取得授權等級<p/>
	 * LNF020_AUTHO_LEVEL
	 */
	public String getElf500_grantno() {
		return this.elf500_grantno;
	}
	/**
	 *  設定授權等級<p/>
	 *  LNF020_AUTHO_LEVEL
	 **/
	public void setElf500_grantno(String value) {
		this.elf500_grantno = value;
	}

	/** 
	 * 取得循環<p/>
	 * LNF020_REVOLVE
	 */
	public String getElf500_lncicl() {
		return this.elf500_lncicl;
	}
	/**
	 *  設定循環<p/>
	 *  LNF020_REVOLVE
	 **/
	public void setElf500_lncicl(String value) {
		this.elf500_lncicl = value;
	}

	/** 
	 * 取得共同借款人註記<p/>
	 * LNF020_COLN_FLAG
	 */
	public String getElf500_coborflg() {
		return this.elf500_coborflg;
	}
	/**
	 *  設定共同借款人註記<p/>
	 *  LNF020_COLN_FLAG
	 **/
	public void setElf500_coborflg(String value) {
		this.elf500_coborflg = value;
	}

	/** 
	 * 取得簽案時利害關係人註記{Y, N, A, B} LNF020_RELATE{Y, N}
	 */
	public String getElf500_relate() {
		return this.elf500_relate;
	}
	/**
	 *  設定簽案時利害關係人註記{Y, N, A, B} LNF020_RELATE{Y, N}
	 **/
	public void setElf500_relate(String value) {
		this.elf500_relate = value;
	}

	/** 
	 * 取得不計入同一關係戶代號<p/>
	 * LNF020_IDENTITY_NO不計入同一關係戶代號<br/>
	 *  1:配合政府政策經財政部或央行核准之專案授信<br/>
	 *  2:對政府機關之授信<br/>
	 *  3:以公債國庫券央行儲券可轉讓存單本行存單或債券<br/>
	 *  4:推動小額放款業務要點之台幣一百萬元以下之授信<br/>
	 *  5:配合政府不列同一人限額但需列入同一關係人企業
	 */
	public String getElf500_identity_no() {
		return this.elf500_identity_no;
	}
	/**
	 *  設定不計入同一關係戶代號<p/>
	 *  LNF020_IDENTITY_NO不計入同一關係戶代號<br/>
	 *  1:配合政府政策經財政部或央行核准之專案授信<br/>
	 *  2:對政府機關之授信<br/>
	 *  3:以公債國庫券央行儲券可轉讓存單本行存單或債券<br/>
	 *  4:推動小額放款業務要點之台幣一百萬元以下之授信<br/>
	 *  5:配合政府不列同一人限額但需列入同一關係人企業
	 **/
	public void setElf500_identity_no(String value) {
		this.elf500_identity_no = value;
	}

	/** 
	 * 取得風險國別<p/>
	 * LNF020_RISK_AREA
	 */
	public String getElf500_risk_area() {
		return this.elf500_risk_area;
	}
	/**
	 *  設定風險國別<p/>
	 *  LNF020_RISK_AREA
	 **/
	public void setElf500_risk_area(String value) {
		this.elf500_risk_area = value;
	}

	/** 
	 * 取得ＤＢＲ２２倍無擔保額度<p/>
	 * LNF020_DBR22_FACT
	 */
	public BigDecimal getElf500_dbr22_fact() {
		return this.elf500_dbr22_fact;
	}
	/**
	 *  設定ＤＢＲ２２倍無擔保額度<p/>
	 *  LNF020_DBR22_FACT
	 **/
	public void setElf500_dbr22_fact(BigDecimal value) {
		this.elf500_dbr22_fact = value;
	}

	/** 
	 * 取得單據寄發方式(對帳單)<p/>
	 * LNF020_NOTICE_TYPE<br/>
	 *  1:郵寄通訊地址<br/>
	 *  9:不印授信對帳單
	 */
	public String getElf500_notice_type() {
		return this.elf500_notice_type;
	}
	/**
	 *  設定單據寄發方式(對帳單)<p/>
	 *  LNF020_NOTICE_TYPE<br/>
	 *  1:郵寄通訊地址<br/>
	 *  9:不印授信對帳單
	 **/
	public void setElf500_notice_type(String value) {
		this.elf500_notice_type = value;
	}

	/** 
	 * 取得契約核准編號(簽報書或審核書的案號)<p/>
	 * LNF020_DOCUMENT_NO
	 */
	public String getElf500_document_no() {
		return this.elf500_document_no;
	}
	/**
	 *  設定契約核准編號(簽報書或審核書的案號)<p/>
	 *  LNF020_DOCUMENT_NO
	 **/
	public void setElf500_document_no(String value) {
		this.elf500_document_no = value;
	}

	/** 
	 * 取得團貸總戶之額度序號<p/>
	 * LNF020-GRP-CNTRNO
	 */
	public String getElf500_grp_cntrno() {
		return this.elf500_grp_cntrno;
	}
	/**
	 *  設定團貸總戶之額度序號<p/>
	 *  LNF020-GRP-CNTRNO
	 **/
	public void setElf500_grp_cntrno(String value) {
		this.elf500_grp_cntrno = value;
	}

	/** 
	 * 取得服務公司/建設公司統編<p/>
	 * LNF033_GRP_NO
	 */
	public String getElf500_cmpid() {
		return this.elf500_cmpid;
	}
	/**
	 *  設定服務公司/建設公司統編<p/>
	 *  LNF033_GRP_NO
	 **/
	public void setElf500_cmpid(String value) {
		this.elf500_cmpid = value;
	}

	/** 
	 * 取得批號<p/>
	 * LNF033_GRP_SEQ
	 */
	public String getElf500_lotno() {
		return this.elf500_lotno;
	}
	/**
	 *  設定批號<p/>
	 *  LNF033_GRP_SEQ
	 **/
	public void setElf500_lotno(String value) {
		this.elf500_lotno = value;
	}

	/** 取得青創同一事業體統編 **/
	public String getElf500_company_id() {
		return this.elf500_company_id;
	}
	/** 設定青創同一事業體統編 **/
	public void setElf500_company_id(String value) {
		this.elf500_company_id = value;
	}

	/** 取得青創同一事業體名稱 **/
	public String getElf500_company_nm() {
		return this.elf500_company_nm;
	}
	/** 設定青創同一事業體名稱 **/
	public void setElf500_company_nm(String value) {
		this.elf500_company_nm = value;
	}

	/** 取得青創事業體縣/市欄位 **/
	public String getElf500_company_area() {
		return this.elf500_company_area;
	}
	/** 設定青創事業體縣/市欄位 **/
	public void setElf500_company_area(String value) {
		this.elf500_company_area = value;
	}

	/** 取得設立登記日期 **/
	public Date getElf500_register_dt() {
		return this.elf500_register_dt;
	}
	/** 設定設立登記日期 **/
	public void setElf500_register_dt(Date value) {
		this.elf500_register_dt = value;
	}

	/** 取得登記出資額 **/
	public BigDecimal getElf500_capital_amt() {
		return this.elf500_capital_amt == null ? elf500_capital_amt : elf500_capital_amt.setScale(0);
	}
	/** 設定登記出資額 **/
	public void setElf500_capital_amt(BigDecimal value) {
		this.elf500_capital_amt = value;
	}

	/** 取得育成輔導註記 **/
	public String getElf500_assist_flag() {
		return this.elf500_assist_flag;
	}
	/** 設定育成輔導註記 **/
	public void setElf500_assist_flag(String value) {
		this.elf500_assist_flag = value;
	}

	/** 取得職業別 **/
	public String getElf500_pos() {
		return this.elf500_pos;
	}
	/** 設定職業別 **/
	public void setElf500_pos(String value) {
		this.elf500_pos = value;
	}

	/** 
	 * 取得年薪<p/>
	 * 萬元
	 */
	public Integer getElf500_ypay() {
		return this.elf500_ypay;
	}
	/**
	 *  設定年薪<p/>
	 *  萬元
	 **/
	public void setElf500_ypay(Integer value) {
		this.elf500_ypay = value;
	}

	/** 
	 * 取得家庭所得<p/>
	 * 萬元
	 */
	public Integer getElf500_hincome() {
		return this.elf500_hincome;
	}
	/**
	 *  設定家庭所得<p/>
	 *  萬元
	 **/
	public void setElf500_hincome(Integer value) {
		this.elf500_hincome = value;
	}

	/** 
	 * 取得其他所得<p/>
	 * 萬元
	 */
	public Integer getElf500_omoney() {
		return this.elf500_omoney;
	}
	/**
	 *  設定其他所得<p/>
	 *  萬元
	 **/
	public void setElf500_omoney(Integer value) {
		this.elf500_omoney = value;
	}

	/** 
	 * 取得資料維護狀態{0:引進中 , 1:已覆核 , 2:解控 , 3:刪除 , 4:已開戶 , 9:行家理財變更}
	 */
	public String getElf500_status() {
		return this.elf500_status;
	}
	/**
	 *  設定資料維護狀態{0:引進中 , 1:已覆核 , 2:解控 , 3:刪除 , 4:已開戶 , 9:行家理財變更}
	 **/
	public void setElf500_status(String value) {
		this.elf500_status = value;
	}

	/** 
	 * 取得央行控管類別1/2/3/4<p/>
	 * LNF087_CONTROL_CD
	 */
	public String getElf500_control_cd() {
		return this.elf500_control_cd;
	}
	/**
	 *  設定央行控管類別1/2/3/4<p/>
	 *  LNF087_CONTROL_CD
	 **/
	public void setElf500_control_cd(String value) {
		this.elf500_control_cd = value;
	}

	/** 
	 * 取得JCIC已有購屋融資註記Y一戶/N無/B二戶(含)以上<p/>
	 * LNF087_JCIC_MARK
	 */
	public String getElf500_jcic_mark() {
		return this.elf500_jcic_mark;
	}
	/**
	 *  設定JCIC已有購屋融資註記Y一戶/N無/B二戶(含)以上<p/>
	 *  LNF087_JCIC_MARK
	 **/
	public void setElf500_jcic_mark(String value) {
		this.elf500_jcic_mark = value;
	}

	/** 
	 * 取得個人:「住」或「住商」無營業註記
法人:建物謄本登記用途是否屬「住」類(Y/N)<p/>
	 * LNF087_REG_PURPOSE
	 */
	public String getElf500_reg_purpose() {
		return this.elf500_reg_purpose;
	}
	/**
	 *  設定個人:「住」或「住商」無營業註記
法人:建物謄本登記用途是否屬「住」類(Y/N)<p/>
	 *  LNF087_REG_PURPOSE
	 **/
	public void setElf500_reg_purpose(String value) {
		this.elf500_reg_purpose = value;
	}

	/** 
	 * 取得擔保品坐落區<p/>
	 * LNF087_LOCATION_CD
	 */
	public String getElf500_location_cd() {
		return this.elf500_location_cd;
	}
	/**
	 *  設定擔保品坐落區<p/>
	 *  LNF087_LOCATION_CD
	 **/
	public void setElf500_location_cd(String value) {
		this.elf500_location_cd = value;
	}

	/** 
	 * 取得擔保品購價或時價<p/>
	 * LNF087_APP_AMT
	 */
	public BigDecimal getElf500_app_amt() {
		return this.elf500_app_amt == null ? elf500_app_amt : elf500_app_amt.setScale(0);
	}
	/**
	 *  設定擔保品購價或時價<p/>
	 *  LNF087_APP_AMT
	 **/
	public void setElf500_app_amt(BigDecimal value) {
		this.elf500_app_amt = value;
	}

	/** 取得擔保品鑑價 **/
	public BigDecimal getElf500_est_amt() {
		return this.elf500_est_amt == null ? elf500_est_amt : elf500_est_amt.setScale(0);
	}
	/** 設定擔保品鑑價 **/
	public void setElf500_est_amt(BigDecimal value) {
		this.elf500_est_amt = value;
	}

	/** 取得擔保品估價 **/
	public BigDecimal getElf500_lawval() {
		return this.elf500_lawval == null ? elf500_lawval : elf500_lawval.setScale(0);
	}
	/** 設定擔保品估價 **/
	public void setElf500_lawval(BigDecimal value) {
		this.elf500_lawval = value;
	}

	/** 
	 * 取得貸放成數<p/>
	 * LNF087_LTV_RATE
	 */
	public BigDecimal getElf500_ltv_rate() {
		return this.elf500_ltv_rate;
	}
	/**
	 *  設定貸放成數<p/>
	 *  LNF087_LTV_RATE
	 **/
	public void setElf500_ltv_rate(BigDecimal value) {
		this.elf500_ltv_rate = value;
	}

	/** 
	 * 取得有無與其他額度共用擔保品<p/>
	 * LNF087_COCOLL_FG
	 */
	public String getElf500_cocoll_fg() {
		return this.elf500_cocoll_fg;
	}
	/**
	 *  設定有無與其他額度共用擔保品<p/>
	 *  LNF087_COCOLL_FG
	 **/
	public void setElf500_cocoll_fg(String value) {
		this.elf500_cocoll_fg = value;
	}

	/** 
	 * 取得有無授權外核准得分批動用<p/>
	 * LNF087_PART_FUND
	 */
	public String getElf500_part_fund() {
		return this.elf500_part_fund;
	}
	/**
	 *  設定有無授權外核准得分批動用<p/>
	 *  LNF087_PART_FUND
	 **/
	public void setElf500_part_fund(String value) {
		this.elf500_part_fund = value;
	}

	/** 
	 * 取得共用同一擔保品總額度金額<p/>
	 * LNF087_SUM_FACTAMT
	 */
	public BigDecimal getElf500_sum_factamt() {
		return this.elf500_sum_factamt == null ? elf500_sum_factamt : elf500_sum_factamt.setScale(0);
	}
	/**
	 *  設定共用同一擔保品總額度金額<p/>
	 *  LNF087_SUM_FACTAMT
	 **/
	public void setElf500_sum_factamt(BigDecimal value) {
		this.elf500_sum_factamt = value;
	}

	/** 取得是否為受限戶(Y/N) **/
	public String getElf500_restrict() {
		return this.elf500_restrict;
	}
	/** 設定是否為受限戶(Y/N) **/
	public void setElf500_restrict(String value) {
		this.elf500_restrict = value;
	}

	/** 取得是否為高價住宅(Y/N) **/
	public String getElf500_hp_house() {
		return this.elf500_hp_house;
	}
	/** 設定是否為高價住宅(Y/N) **/
	public void setElf500_hp_house(String value) {
		this.elf500_hp_house = value;
	}

	/** 取得是否屬都市計畫劃定之區域(Y/N) **/
	public String getElf500_plan_area() {
		return this.elf500_plan_area;
	}
	/** 設定是否屬都市計畫劃定之區域(Y/N) **/
	public void setElf500_plan_area(String value) {
		this.elf500_plan_area = value;
	}

	/** 
	 * 取得都市計畫劃定之使用分區<p/>
	 * 1住宅區<br/>
	 *  2商業區<br/>
	 *  3其他
	 */
	public String getElf500_p_usetype() {
		return this.elf500_p_usetype;
	}
	/**
	 *  設定都市計畫劃定之使用分區<p/>
	 *  1住宅區<br/>
	 *  2商業區<br/>
	 *  3其他
	 **/
	public void setElf500_p_usetype(String value) {
		this.elf500_p_usetype = value;
	}

	/** 
	 * 取得借款用途<p/>
	 * 1興建房屋<br/>
	 *  2自然人理財週轉<br/>
	 *  3營運週轉
	 */
	public String getElf500_p_loanuse() {
		return this.elf500_p_loanuse;
	}
	/**
	 *  設定借款用途<p/>
	 *  1興建房屋<br/>
	 *  2自然人理財週轉<br/>
	 *  3營運週轉
	 **/
	public void setElf500_p_loanuse(String value) {
		this.elf500_p_loanuse = value;
	}

	/** 
	 * 取得擔保品性質別<p/>
	 * 1房地建地<br/>
	 *  2空地<br/>
	 *  3其他
	 */
	public String getElf500_coll_char() {
		return this.elf500_coll_char;
	}
	/**
	 *  設定擔保品性質別<p/>
	 *  1房地建地<br/>
	 *  2空地<br/>
	 *  3其他
	 **/
	public void setElf500_coll_char(String value) {
		this.elf500_coll_char = value;
	}

	/** 取得保留一成估值(Y/N) **/
	public String getElf500_keep_lawval() {
		return this.elf500_keep_lawval;
	}
	/** 設定保留一成估值(Y/N) **/
	public void setElf500_keep_lawval(String value) {
		this.elf500_keep_lawval = value;
	}

	/** 
	 * 取得央行控管種類4的理由<p/>
	 * LNF087_PLUS_REASON<br/>
	 *  ○建物所有權取得日期在99/6/25以前(自然人使用)<br/>
	 *  ○建物所有權取得日期在99/12/31以前(公司法人使用)<br/>
	 *  ○建物登記用途無「住」字樣、或「住商」類等有營業登記<br/>
	 *  ○擔保品非屬建物<br/>
	 *  ○非屬購屋貸款<br/>
	 *  ○轉換產品種類<br/>
	 *  ○其他（　　選其他者必key理由，約20字左右）
	 */
	public String getElf500_plus_reason() {
		return this.elf500_plus_reason;
	}
	/**
	 *  設定央行控管種類4的理由<p/>
	 *  LNF087_PLUS_REASON<br/>
	 *  ○建物所有權取得日期在99/6/25以前(自然人使用)<br/>
	 *  ○建物所有權取得日期在99/12/31以前(公司法人使用)<br/>
	 *  ○建物登記用途無「住」字樣、或「住商」類等有營業登記<br/>
	 *  ○擔保品非屬建物<br/>
	 *  ○非屬購屋貸款<br/>
	 *  ○轉換產品種類<br/>
	 *  ○其他（　　選其他者必key理由，約20字左右）
	 **/
	public void setElf500_plus_reason(String value) {
		this.elf500_plus_reason = value;
	}

	/** 取得非屬央行填報對象理由 **/
	public String getElf500_plus_memo() {
		return this.elf500_plus_memo;
	}
	/** 設定非屬央行填報對象理由 **/
	public void setElf500_plus_memo(String value) {
		this.elf500_plus_memo = value;
	}

	/** 
	 * 取得編輯序號<p/>
	 * N
	 */
	public String getElf500_editno() {
		return this.elf500_editno;
	}
	/**
	 *  設定編輯序號<p/>
	 *  N
	 **/
	public void setElf500_editno(String value) {
		this.elf500_editno = value;
	}

	/** 取得ELOAN寫入時間 **/
	public Date getElf500_eloantimes() {
		return this.elf500_eloantimes;
	}
	/** 設定ELOAN寫入時間 **/
	public void setElf500_eloantimes(Date value) {
		this.elf500_eloantimes = value;
	}

	/** 取得ELOAN寫入時間 **/
	public Date getElf500_tmestamp() {
		return this.elf500_tmestamp;
	}
	/** 設定ELOAN寫入時間 **/
	public void setElf500_tmestamp(Date value) {
		this.elf500_tmestamp = value;
	}
	
	/** 
	 * 取得ALOAN寫入時間<p/>
	 * N
	 */
	public Date getElf500_aloantimes() {
		return this.elf500_aloantimes;
	}
	/**
	 *  設定ALOAN寫入時間<p/>
	 *  N
	 **/
	public void setElf500_aloantimes(Date value) {
		this.elf500_aloantimes = value;
	}

	/** 取得櫃員代號 **/
	public String getElf500_teller() {
		return this.elf500_teller;
	}
	/** 設定櫃員代號 **/
	public void setElf500_teller(String value) {
		this.elf500_teller = value;
	}

	/** 取得主管代號 **/
	public String getElf500_supvno() {
		return this.elf500_supvno;
	}
	/** 設定主管代號 **/
	public void setElf500_supvno(String value) {
		this.elf500_supvno = value;
	}

	/** 
	 * 取得擔保品座落-段<p/>
	 * Ex:0205鳳林
	 */
	public Integer getElf500_site3no() {
		return this.elf500_site3no;
	}
	/**
	 *  設定擔保品座落-段<p/>
	 *  Ex:0205鳳林
	 **/
	public void setElf500_site3no(Integer value) {
		this.elf500_site3no = value;
	}

	/** 
	 * 取得擔保品座落-村<p/>
	 * Ex:1000305-006蘆竹村
	 */
	public String getElf500_site4no() {
		return this.elf500_site4no;
	}
	/**
	 *  設定擔保品座落-村<p/>
	 *  Ex:1000305-006蘆竹村
	 **/
	public void setElf500_site4no(String value) {
		this.elf500_site4no = value;
	}

	/** 取得擔保品性質別3其他之說明 **/
	public String getElf500_coll_char_m() {
		return this.elf500_coll_char_m;
	}
	/** 設定擔保品性質別3其他之說明 **/
	public void setElf500_coll_char_m(String value) {
		this.elf500_coll_char_m = value;
	}
	
	/** 設定團貸職工編號 **/
	public void setElf500_staffno(String value) {
		this.elf500_staffno = value;
	}
	
	/** 取得團貸職工編號 **/
	public String getElf500_staffno() {
		return this.elf500_staffno;
	}
	
	/** 設定青創申請日期 **/
	public void setElf500_apply_date(Date value) {
		this.elf500_apply_date = value;
	}
	
	/** 取得青創申請日期 **/
	public Date getElf500_apply_date() {
		return this.elf500_apply_date;
	}
	
	/**
	 * 設定學歷
	 * @return
	 */
	public void setElf500_edu(String value) {
		this.elf500_edu = value;
	}
	
	/**
	 * 取得學歷
	 * @return
	 */
	public String getElf500_edu() {
		return elf500_edu;
	}
	
	/**
	 * 設定婚姻狀況
	 * @return
	 */
	public void setElf500_marry(String value) {
		this.elf500_marry = value;
	}
	
	/**
	 * 取得婚姻狀況
	 * @return
	 */
	public String getElf500_marry() {
		return elf500_marry;
	}
	
	/**
	 * 設定子女數
	 * @return
	 */
	public void setElf500_child(Integer value) {
		this.elf500_child = value;
	}
	
	/**
	 * 取得子女數
	 * @return
	 */
	public Integer getElf500_child() {
		return elf500_child;
	}
	
	/**
	 * 設定年資
	 * @return
	 */
	public void setElf500_seniority(BigDecimal value) {
		this.elf500_seniority = value;
	}
	
	/**
	 * 取得年資
	 * @return
	 */
	public BigDecimal getElf500_seniority() {
		return elf500_seniority;
	}
	
	/**
	 * 設定個人負債比率
	 */
	public void setElf500_drate(BigDecimal value) {
		this.elf500_drate = value;
	}
	
	/**
	 * 取得個人負債比率
	 */
	public BigDecimal getElf500_drate() {
		return elf500_drate;
	}
	
	/**
	 * 設定家庭負債比率
	 */
	public void setElf500_yrate(BigDecimal value) {
		this.elf500_yrate = value;
	}
	
	/**
	 * 取得家庭負債比率
	 */
	public BigDecimal getElf500_yrate() {
		return elf500_yrate;
	}
	
	/**
	 * 設定使用信用卡循環信用或現金卡情形
	 */	
	public void setElf500_credit(String value) {
		this.elf500_credit = value;
	}
	
	/**
	 * 取得使用信用卡循環信用或現金卡情形
	 */	
	public String getElf500_credit() {
		return elf500_credit;
	}
	
	/**
	 * 設定是否於本行財富管理有定時定額扣款
	 */
	public void setElf500_isperiodfund(String value) {
		this.elf500_isperiodfund = value;
	}
	
	/**
	 * 取得是否於本行財富管理有定時定額扣款
	 */
	public String getElf500_isperiodfund() {
		return elf500_isperiodfund;
	}
	
	/**
	 * 設定與本行其他業務往來(財富管理業務如基金保險信用卡等)
	 */
	public void setElf500_busi(String value) {
		this.elf500_busi = value;
	}
	
	/**
	 * 取得與本行其他業務往來(財富管理業務如基金保險信用卡等)
	 */
	public String getElf500_busi() {
		return elf500_busi;
	}
	
	/**
	 * 設定與本行財富管理三個月平均總資產(幣別)
	 */
	public void setElf500_invmbalcurr(String value) {
		this.elf500_invmbalcurr = value;
	}
	
	/**
	 * 取得與本行財富管理三個月平均總資產(幣別)
	 */
	public String getElf500_invmbalcurr() {
		return elf500_invmbalcurr;
	}
	
	/**
	 * 設定與本行財富管理三個月平均總資產(金額)
	 */
	public void setElf500_invmbalamt(BigDecimal value) {
		this.elf500_invmbalamt = value;
	}
	
	/**
	 * 取得與本行財富管理三個月平均總資產(金額)
	 */
	public BigDecimal getElf500_invmbalamt() {
		return elf500_invmbalamt;
	}
	
	/**
	 * 設定與他行財富管理三個月平均總資產(幣別)
	 */
	public void setElf500_invobalcurr(String value) {
		this.elf500_invobalcurr = value;
	}
	
	/**
	 * 取得與他行財富管理三個月平均總資產(幣別)
	 */
	public String getElf500_invobalcurr() {
		return elf500_invobalcurr;
	}
	
	/**
	 * 設定與他行財富管理三個月平均總資產(金額)
	 */
	public void setElf500_invobalamt(BigDecimal value) {
		this.elf500_invobalamt = value;
	}
	
	/**
	 * 取得與他行財富管理三個月平均總資產(金額)
	 */
	public BigDecimal getElf500_invobalamt() {
		return elf500_invobalamt;
	}
	
	/**
	 * 設定與金融機構存款往來情形(近六個月平均餘額)(幣別)
	 */
	public void setElf500_brancurr(String value) {
		this.elf500_brancurr = value;
	}
	
	/**
	 * 取得與金融機構存款往來情形(近六個月平均餘額)(幣別)
	 */
	public String getElf500_brancurr() {
		return elf500_brancurr;
	}
	
	/**
	 * 設定與金融機構存款往來情形(近六個月平均餘額)(金額)
	 */
	public void setElf500_branamt(BigDecimal value) {
		this.elf500_branamt = value;
	}
	
	/**
	 * 取得與金融機構存款往來情形(近六個月平均餘額)(金額)
	 */
	public BigDecimal getElf500_branamt() {
		return elf500_branamt;
	}
	
	/**
	 * 設定不動產狀況
	 */
	public void setElf500_cmsstatus(String value) {
		this.elf500_cmsstatus = value;
	}
	
	/**
	 * 取得不動產狀況
	 */
	public String getElf500_cmsstatus() {
		return elf500_cmsstatus;
	}
	
	/** 設定聯徵查詢日期 **/
	public void setElf500_ejcicqdate(Date value) {
		this.elf500_ejcicqdate = value;
	}
	
	/** 取得聯徵查詢日期 **/
	public Date getElf500_ejcicqdate() {
		return elf500_ejcicqdate;
	}
	
	/** 設定以基金贖回款為還款來源之授信 **/
	public void setElf500_repayfund(String value) {
		this.elf500_repayfund = value;
	}
	
	/** 取得以基金贖回款為還款來源之授信 **/
	public String getElf500_repayfund() {
		return elf500_repayfund;
	}
	
	/** 設定L56A引進選項 **/
	public void setElf500_l56a_func(String elf500_l56a_func) {
		this.elf500_l56a_func = elf500_l56a_func;
	}
	/** 取得L56A引進選項 **/
	public String getElf500_l56a_func() {
		return elf500_l56a_func;
	}
	
	/** 設定約定融資額度註記 **/
	public void setElf500_except(String elf500_except) {
		this.elf500_except = elf500_except;
	}
	/** 取得約定融資額度註記 **/
	public String getElf500_except() {
		return elf500_except;
	}

	/** 設定利害關係人可做無擔保註記 **/
	public void setElf500_unsecurefla(String s){
		this.elf500_unsecurefla = s; 
	}
	
	/** 取得利害關係人可做無擔保註記 **/
	public String getElf500_unsecurefla(){
		return elf500_unsecurefla;
	}
	
	/** 設定ELF500 無效 **/
	public void setElf500_notvalid(String s){
		this.elf500_notvalid = s;
	}
	
	/** 取得ELF500 無效 **/
	public String getElf500_notvalid(){
		return elf500_notvalid;
	}
	
	/**
	 * 取得專案種類{05:新創產業}  
	 */
	public String getElf500_proj_class() {
		return elf500_proj_class;
	}
	/**
	 * 設定專案種類{05:新創產業}  
	 */
	public void setElf500_proj_class(String elf500_proj_class) {
		this.elf500_proj_class = elf500_proj_class;
	}
	
	/**
	 * 取得專案種類細項{AA:綠能科技 , BB:亞洲矽谷 , CC:生技醫療 , DD:國防產業 , EE:智慧機械 , FF:新農業 , GG:循環經濟}
	 */
	public String getElf500_itw_code() {
		return elf500_itw_code;
	}
	/**
	 * 設定專案種類細項{AA:綠能科技 , BB:亞洲矽谷 , CC:生技醫療 , DD:國防產業 , EE:智慧機械 , FF:新農業 , GG:循環經濟}
	 */
	public void setElf500_itw_code(String elf500_itw_code) {
		this.elf500_itw_code = elf500_itw_code;
	}
	
	/** 取得擔保品屋齡 **/
	public BigDecimal getElf500_house_age() {
		return elf500_house_age;
	}
	/** 設定擔保品屋齡 **/
	public void setElf500_house_age(BigDecimal elf500_house_age) {
		this.elf500_house_age = elf500_house_age;
	}
	
	/** 取得擔保品放款值 **/
	public BigDecimal getElf500_loanAmt() {
		return elf500_loanAmt;
	}
	/** 設定擔保品放款值 **/
	public void setElf500_loanAmt(BigDecimal elf500_loanAmt) {
		this.elf500_loanAmt = elf500_loanAmt;
	}
	
	/** 取得引介行員代號 */
	public String getElf500_mega_empno() {
		return elf500_mega_empno;
	}
	/** 設定引介行員代號 */
	public void setElf500_mega_empno(String elf500_mega_empno) {
		this.elf500_mega_empno = elf500_mega_empno;
	}
	
	/** 取得引介房仲代號{11111:永慶, 22222:信義, 55555:住商} */
	public String getElf500_agnt_no() {
		return elf500_agnt_no;
	}
	/** 設定引介房仲代號{11111:永慶, 22222:信義, 55555:住商} */
	public void setElf500_agnt_no(String elf500_agnt_no) {
		this.elf500_agnt_no = elf500_agnt_no;
	}
	
	/** 取得引介房仲連鎖店類型{A:直營店, B:加盟店} */
	public String getElf500_agnt_chain() {
		return elf500_agnt_chain;
	}
	/** 設定引介房仲連鎖店類型{A:直營店, B:加盟店} */
	public void setElf500_agnt_chain(String elf500_agnt_chain) {
		this.elf500_agnt_chain = elf500_agnt_chain;
	}
	
	/** 取得引介子公司代號{90001, 90002...} */
	public String getElf500_mega_code() {
		return elf500_mega_code;
	}
	/** 設定引介子公司代號{90001, 90002...} */
	public void setElf500_mega_code(String elf500_mega_code) {
		this.elf500_mega_code = elf500_mega_code;
	}
	
	/** 取得引介子公司分支代號 */
	public String getElf500_sub_unitno() {
		return elf500_sub_unitno;
	}
	/** 設定引介子公司分支代號 */
	public void setElf500_sub_unitno(String elf500_sub_unitno) {
		this.elf500_sub_unitno = elf500_sub_unitno;
	}
	
	/** 取得引介子公司員工編號 */
	public String getElf500_sub_empno() {
		return elf500_sub_empno;
	}
	/** 設定引介子公司員工編號 */
	public void setElf500_sub_empno(String elf500_sub_empno) {
		this.elf500_sub_empno = elf500_sub_empno;
	}
	
	/** 取得引介子公司員工姓名 */
	public String getElf500_sub_empnm() {
		return elf500_sub_empnm;
	}
	/** 設定引介子公司員工姓名 */
	public void setElf500_sub_empnm(String elf500_sub_empnm) {
		this.elf500_sub_empnm = elf500_sub_empnm;
	}
	
	public String getElf500_welfare_cmte() {
		return elf500_welfare_cmte;
	}
	public void setElf500_welfare_cmte(String elf500_welfare_cmte) {
		this.elf500_welfare_cmte = elf500_welfare_cmte;
	}
	
	public String getElf500_version() {
		return elf500_version;
	}
	public void setElf500_version(String elf500_version) {
		this.elf500_version = elf500_version;
	}
	
	public String getElf500_hLoanLimit() {
		return elf500_hLoanLimit;
	}
	public void setElf500_hLoanLimit(String elf500_hLoanLimit) {
		this.elf500_hLoanLimit = elf500_hLoanLimit;
	}
	public String getElf500_intro_src() {
		return elf500_intro_src;
	}
	public void setElf500_intro_src(String elf500_intro_src) {
		this.elf500_intro_src = elf500_intro_src;
	}
	public String getElf500_agntfbfg() {
		return elf500_agntfbfg;
	}
	public void setElf500_agntfbfg(String elf500_agntfbfg) {
		this.elf500_agntfbfg = elf500_agntfbfg;
	}
	public String getElf500_cmbull_key() {
		return elf500_cmbull_key;
	}
	public void setElf500_cmbull_key(String elf500_cmbull_key) {
		this.elf500_cmbull_key = elf500_cmbull_key;
	}
	public String getElf500_market_note() {
		return elf500_market_note;
	}
	public void setElf500_market_note(String elf500_market_note) {
		this.elf500_market_note = elf500_market_note;
	}
	public String getElf500_suspln_rson() {
		return elf500_suspln_rson;
	}
	public void setElf500_suspln_rson(String elf500_suspln_rson) {
		this.elf500_suspln_rson = elf500_suspln_rson;
	}
	public String getElf500_suspln_flag() {
		return elf500_suspln_flag;
	}
	public void setElf500_suspln_flag(String elf500_suspln_flag) {
		this.elf500_suspln_flag = elf500_suspln_flag;
	}
	public String getElf500_suspln_cntr() {
		return elf500_suspln_cntr;
	}
	public void setElf500_suspln_cntr(String elf500_suspln_cntr) {
		this.elf500_suspln_cntr = elf500_suspln_cntr;
	}
	public String getElf500_hLoanLimit_2() {
		return elf500_hLoanLimit_2;
	}
	public void setElf500_hLoanLimit_2(String elf500_hLoanLimit_2) {
		this.elf500_hLoanLimit_2 = elf500_hLoanLimit_2;
	}
	public Date getElf500_endDate() {
		return elf500_endDate;
	}
	public void setElf500_endDate(Date elf500_endDate) {
		this.elf500_endDate = elf500_endDate;
	}
	public String getElf500_isRenew() {
		return elf500_isRenew;
	}
	public void setElf500_isRenew(String elf500_isRenew) {
		this.elf500_isRenew = elf500_isRenew;
	}
	public String getElf500_isPay_old() {
		return elf500_isPay_old;
	}
	public void setElf500_isPay_old(String elf500_isPay_old) {
		this.elf500_isPay_old = elf500_isPay_old;
	}
	public BigDecimal getElf500_old_quota() {
		return elf500_old_quota;
	}
	public void setElf500_old_quota(BigDecimal elf500_old_quota) {
		this.elf500_old_quota = elf500_old_quota;
	}
	public BigDecimal getElf500_pay_old_amt() {
		return elf500_pay_old_amt;
	}
	public void setElf500_pay_old_amt(BigDecimal elf500_pay_old_amt) {
		this.elf500_pay_old_amt = elf500_pay_old_amt;
	}
	public String getElf500_pay_old_item() {
		return elf500_pay_old_item;
	}
	public void setElf500_pay_old_item(String elf500_pay_old_item) {
		this.elf500_pay_old_item = elf500_pay_old_item;
	}
	public String getElf500_isMatch_item() {
		return elf500_isMatch_item;
	}
	public void setElf500_isMatch_item(String elf500_isMatch_item) {
		this.elf500_isMatch_item = elf500_isMatch_item;
	}
	public String getElf500_isSale_case() {
		return elf500_isSale_case;
	}
	public void setElf500_isSale_case(String elf500_isSale_case) {
		this.elf500_isSale_case = elf500_isSale_case;
	}
	public Date getElf500_lstDate() {
		return elf500_lstDate;
	}
	public void setElf500_lstDate(Date elf500_lstDate) {
		this.elf500_lstDate = elf500_lstDate;
	}
	public BigDecimal getElf500_timeval() {
		return elf500_timeval;
	}
	public void setElf500_timeval(BigDecimal elf500_timeval) {
		this.elf500_timeval = elf500_timeval;
	}
	public BigDecimal getElf500_cgf_rate() {
		return elf500_cgf_rate;
	}
	public void setElf500_cgf_rate(BigDecimal elf500_cgf_rate) {
		this.elf500_cgf_rate = elf500_cgf_rate;
	}
	public void setElf500_sixcor_code(String elf500_sixcor_code) {
		this.elf500_sixcor_code = elf500_sixcor_code;
	}
	public String getElf500_sixcor_code() {
		return elf500_sixcor_code;
	}
	public String getElf500_dt_source() {
		return elf500_dt_source;
	}
	public void setElf500_dt_source(String elf500_dt_source) {
		this.elf500_dt_source = elf500_dt_source;
	}
	public String getElf500_dt_status() {
		return elf500_dt_status;
	}
	public void setElf500_dt_status(String elf500_dt_status) {
		this.elf500_dt_status = elf500_dt_status;
	}
	/** 取得家庭所得 */
	public Integer getElf500_fincome() {
		return elf500_fincome;
	}
	/** 設定家庭所得 */
	public void setElf500_fincome(Integer elf500_fincome) {
		this.elf500_fincome = elf500_fincome;
	}
	/** 取得房貸信保註記 */
	public String getElf500_synd_ipfd() {
		return elf500_synd_ipfd;
	}
	
	/** 設定房貸信保註記 */
	public void setElf500_synd_ipfd(String elf500_synd_ipfd) {
		this.elf500_synd_ipfd = elf500_synd_ipfd;
	}
}
