package com.mega.eloan.lms.lrs.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * [企金]-覆審報告表  考核表
 * </pre>
 * 
 * @since 2021/10/
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/10/,009301,new
 *          </ul>
 */
public class LMS1700S06Panel extends Panel {

	public LMS1700S06Panel(String id) {
		super(id);

	}
	
	public LMS1700S06Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);

	}

	private static final long serialVersionUID = 1L;
}
