package com.mega.eloan.lms.batch.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.batch.service.LmsBatchCommonService;
import com.mega.eloan.lms.mfaloan.bean.ELF602;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;

/**
 * <pre>
 *  SLMS-00014 企金個人基本資料補行業別(含海外個人戶)
 * </pre>
 * 
 * @since 2013/8/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/8/13,007625,new
 *          </ul>
 */
@Service("lmsBatchUnsoldHouseManageServiceImpl")
public class LmsBatchUnsoldHouseManageServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory.getLogger(LmsBatchUnsoldHouseManageServiceImpl.class);

	@Resource
	LmsBatchCommonService lmsBatchCommonService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		//JSONObject request = json.getJSONObject("request");

		String toDate = CapDate.getCurrentDate("yyyy-MM-dd");
		
		DateFormat dateformat = new SimpleDateFormat("yyyyMMddHHmmss");
		
		JSONObject result = new JSONObject();
		String cntrno = null;
		String custIdDupNo = null;
		String branchNo = null;
		try {
			
			List<Map<String, Object>> list = this.misdbBASEService.getUnsoldHouseDataByUncancelledAndRemainingHouses(toDate);
			for(Map<String, Object> m : list){
				//insert into elf602
				cntrno = String.valueOf(m.get("ELF517_CONTRACT"));
				custIdDupNo = String.valueOf(m.get("LNF030_CUST_ID"));
				branchNo = String.valueOf(m.get("LNF020_LN_BR_NO"));
				String accountNo = String.valueOf(m.get("LNF030_LOAN_NO"));
				String[] idArray = custIdDupNo.split(" ");
				String custId = null;
				String dupNo = null;
				if(idArray.length > 1){
					custId = idArray[0];
					dupNo = idArray[idArray.length-1];
				}
				else{
					custId = custIdDupNo.substring(0, 10);
					dupNo = custIdDupNo.substring(custIdDupNo.length()-1, custIdDupNo.length());
				}
				
				ELF602 elf602 = new ELF602();
				elf602.setElf602_unid(cntrno + "-" + dateformat.format(new Date()));
				elf602.setElf602_cntrno(cntrno);
				elf602.setElf602_case_mark("03");
				elf602.setElf602_fo_content("餘屋貸後管理");//追蹤事項通知內容
				elf602.setElf602_fo_date(new java.sql.Date(new Date().getTime()));//追蹤事項通知日期
				elf602.setElf602_status("1"); // 狀態：1-未辦理
				elf602.setElf602_custid(custId);
				elf602.setElf602_dupno(dupNo);
				elf602.setElf602_br_no(branchNo);
				elf602.setElf602_loan_no(accountNo);
				elf602.setElf602_loan_kind("LN");
				elf602.setElf602_conform_fg("");
				elf602.setElf602_staff("01");
				elf602.setElf602_unusual_fg("N");
				elf602.setElf602_upd_teller("");
				elf602.setElf602_upd_supvno("");
				misdbBASEService.insertELF602(elf602);
			}
			
			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE, "lmsBatchUnsoldHouseManageServiceImpl 執行成功！");

		} catch (Exception ex) {
			LOGGER.error(StrUtils.getStackTrace(ex));
			result = WebBatchCode.RC_ERROR;
			result.element(
					WebBatchCode.P_RESPONSE, "lmsBatchUnsoldHouseManageServiceImpl 執行失敗！==>" + "cntrno:" + cntrno + "-custId:" + custIdDupNo + "-branchNo:" + branchNo + "-" + ex.getLocalizedMessage());

		}

		return result;
	}

}
