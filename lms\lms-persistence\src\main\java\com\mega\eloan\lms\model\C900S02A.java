/* 
 * C900S02A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.lms.validation.group.Check;

/** 房貸利率永慶信義100億統計 **/
@Entity
@Table(name="C900S02A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C900S02A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;
	
	/** 分行 **/
	@Size(max = 3)
	@Column(name = "BRNO", length = 3, columnDefinition = "CHAR(3)")
	private String brNo;
	
	/** 額度 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 產品種類 **/
	@Size(max=2)
	@Column(name="PRODKIND", length=2, columnDefinition="CHAR(2)")
	private String prodKind;
	
	/** 帳號 **/
	@Size(max=14)
	@Column(name="LOANNO", length=14, columnDefinition="CHAR(14)")
	private String loanNo;

	/** 利率方案 **/
	@Size(max=2)
	@Column(name="RATEPLAN", length=2, columnDefinition="CHAR(2)")
	private String ratePlan;

	/** 產品方案 **/
	@Size(max=2)
	@Column(name="PRODPLAN", length=2, columnDefinition="CHAR(2)")
	private String prodPlan;

	/** 同LNF033_COMPANY_ID **/
	@Size(max=5)
	@Column(name="COMPANYID5", length=5, columnDefinition="CHAR(5)")
	private String companyId5;
	
	/** 分組鍵值 **/
	@Size(max=5)
	@Column(name="CATEKEY", length=5, columnDefinition="CHAR(5)")
	private String cateKey;
	
	/** 交易日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="TXNDATE", columnDefinition="Date")
	private Date txnDate;

	/** 交易時間 **/
	@Size(max=12)
	@Column(name="TXNTIME", length=12, columnDefinition="CHAR(12)")
	private String txnTime;
	
	/** 同LNF090_SOLAR_DATE **/
	@Temporal(TemporalType.DATE)
	@Column(name="SOLARDATE", columnDefinition="Date")
	private Date solarDate;
	
	/** 幣別 **/
	@Size(max=3)
	@Column(name="SWFT", length=3, columnDefinition="CHAR(3)")
	private String swft;

	/** 交易金額 **/
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="TXNAMT", columnDefinition="DEC(15,2)")
	private BigDecimal txnAmt;

	/** 擔保品坐落區 **/
	@Size(max=3)
	@Column(name="LOCATIONCD", length=3, columnDefinition="CHAR(3)")
	private String locationCd;

	/** 簽報書核准案號 **/
	@Size(max=20)
	@Column(name="DOCNO", length=20, columnDefinition="CHAR(20)")
	private String docNo;
	
	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}
	
	/** 取得分行 **/
	public String getBrNo() {
		return brNo;
	}
	/** 設定分行 **/
	public void setBrNo(String brNo) {
		this.brNo = brNo;
	}
	
	/** 取得額度 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得產品種類 **/
	public String getProdKind() {
		return this.prodKind;
	}
	/** 設定產品種類 **/
	public void setProdKind(String value) {
		this.prodKind = value;
	}

	/** 取得帳號 **/
	public String getLoanNo() {
		return this.loanNo;
	}
	/** 設定帳號 **/
	public void setLoanNo(String value) {
		this.loanNo = value;
	}

	/** 取得利率方案 **/
	public String getRatePlan() {
		return this.ratePlan;
	}
	/** 設定利率方案 **/
	public void setRatePlan(String value) {
		this.ratePlan = value;
	}

	/** 取得產品方案 **/
	public String getProdPlan() {
		return this.prodPlan;
	}
	/** 設定產品方案 **/
	public void setProdPlan(String value) {
		this.prodPlan = value;
	}

	
	public String getCompanyId5() {
		return companyId5;
	}
	public void setCompanyId5(String companyId5) {
		this.companyId5 = companyId5;
	}
	
	public String getCateKey() {
		return cateKey;
	}
	public void setCateKey(String cateKey) {
		this.cateKey = cateKey;
	}
	/** 取得交易日 **/
	public Date getTxnDate() {
		return this.txnDate;
	}
	/** 設定交易日 **/
	public void setTxnDate(Date value) {
		this.txnDate = value;
	}

	/** 取得幣別 **/
	public String getSwft() {
		return this.swft;
	}
	/** 設定幣別 **/
	public void setSwft(String value) {
		this.swft = value;
	}
	public String getTxnTime() {
		return txnTime;
	}
	public void setTxnTime(String txnTime) {
		this.txnTime = txnTime;
	}		
	public Date getSolarDate() {
		return solarDate;
	}
	public void setSolarDate(Date solarDate) {
		this.solarDate = solarDate;
	}
	/** 取得交易金額 **/
	public BigDecimal getTxnAmt() {
		return this.txnAmt;
	}
	/** 設定交易金額 **/
	public void setTxnAmt(BigDecimal value) {
		this.txnAmt = value;
	}

	/** 取得擔保品坐落區 **/
	public String getLocationCd() {
		return this.locationCd;
	}
	/** 設定擔保品坐落區 **/
	public void setLocationCd(String value) {
		this.locationCd = value;
	}
	
	/** 取得簽報書核准案號 **/
	public String getDocNo() {
		return this.docNo;
	}
	/** 設定簽報書核准案號 **/
	public void setDocNo(String value) {
		this.docNo = value;
	}
	
	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}
}
