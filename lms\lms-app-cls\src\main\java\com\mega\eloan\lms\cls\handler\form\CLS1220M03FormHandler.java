package com.mega.eloan.lms.cls.handler.form;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.pages.CLS1220M03Page;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 線上勞工紓困貸款
 * </pre>
 * 
 * @since 2020/5/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/5/1,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("cls1220m03formhandler")
@DomainClass(C122M01A.class)
public class CLS1220M03FormHandler extends AbstractFormHandler {

	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	CLS1220Service service;

	@Resource
	RetrialService retrialService;
	
	@Resource
	UserInfoService userInfoService;	
	
	@Resource
	BranchService branchService;

	@Resource
	TempDataService tempDataService;
	
	@Resource
	DocCheckService docCheckService;

	@Resource
	DocLogService docLogService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	MisdbBASEService misdbBASEService;
			
	
	Properties prop_cls1220m03 = MessageBundleScriptCreator.getComponentResource(CLS1220M03Page.class);
	Properties prop_abstractEloan = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
	/**
	 * 查詢文件
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		int page = Util.parseInt(params.getString("page"));

		C122M01A meta = null;
		if (mainOid != null) {
			meta = service.getC122M01A_byOid(mainOid); 
		}
		switch (page) {
		case 1:
			LMSUtil.addMetaToResult(result, meta, new String[]{  "ownBrId", "custId", "dupNo"
					, "custName", "docStatus"
					, "notifyWay", "notifyCust", "notifyMemo"
					, "applyCurr", "maturity", "resource", "custENameLast", "custENameFirst"
					, "createTime", "updateTime", "randomCode", "orgBrId", "agreeQueryEJ", "statFlag"
					, "idCardIssueArea", "idCardIssueDate", "idCardChgFlag", "idCardPhoto"
					, "agreeQueryEJIp", "agreeQueryEJTs", "agreeQueryEJVer", "agreeQueryEJMtel", "voider", "voidTime"
					});
			C120S01A c120s01a = clsService.findC120S01A(meta.getMainId(), meta.getCustId(), meta.getDupNo());
			if(c120s01a!=null){
				result.set("coTel", c120s01a.getCoTel());
				result.set("mTel", c120s01a.getMTel());
				result.set("edu", c120s01a.getEdu());
				result.set("birthday", TWNDate.toAD(c120s01a.getBirthday()));
				result.set("marry", c120s01a.getMarry());
				result.set("child", c120s01a.getChild());
				result.set("email", c120s01a.getEmail());
				result.set("fTarget", c120s01a.getFTarget());
				result.set("coTarget", c120s01a.getCoTarget());
				result.set("houseStatus", c120s01a.getHouseStatus());
				result.set("residenceTargetDesc", ClsUtil.build_residenceTargetDesc(c120s01a));
			}
			C120S01B c120s01b = clsService.findC120S01B(meta.getMainId(), meta.getCustId(), meta.getDupNo());
			if(c120s01b!=null){
				result.set("comName", c120s01b.getComName());
				result.set("comTel", c120s01b.getComTel());
				result.set("jobTitle", c120s01b.getJobTitle());
				result.set("seniority", LMSUtil.pretty_numStr(ClsUtility.floor_seniorityYM_to_yearVal_because_outer_system(c120s01b.getSeniority())));
				result.set("payAmt", LMSUtil.pretty_numStr(c120s01b.getPayAmt()));
				result.set("othType", Util.trim(c120s01b.getOthType()));
				result.set("othAmt", LMSUtil.pretty_numStr(c120s01b.getOthAmt()));
			
			}
			result.set("ownBrName", branchService.getBranchName(meta.getOwnBrId()));
			
			result.set("applyAmt", meta.getApplyAmt()==null?"":NumConverter.addComma(meta.getApplyAmt()));			
			result.set("applyTS", CapDate.formatDate(meta.getApplyTS(), "yyyy-MM-dd"));
			result.set("applyDocId", meta.getMainId());
			result.set("applyDocStatusCN", LMSUtil.getDesc(service.get_ApplyDocStatusDescMap(), Util.trim(meta.getApplyStatus()) ));			
			result.set("approveAmt", meta.getApproveAmt()==null?"":NumConverter.addComma(meta.getApproveAmt()));
			result.set("creator", Util.trim(meta.getCreator())+" "+Util.trim(new UserNameFormatter(userInfoService).reformat(meta.getCreator())));
			result.set("updater", Util.trim(meta.getUpdater())+" "+Util.trim(new UserNameFormatter(userInfoService).reformat(meta.getUpdater())));
			result.set("contactEmpNm", userInfoService.getUserName(meta.getContactEmpNo()));
			String noLabInsrMonth = "";
			if(true){
				String itemType = "0";
				C122M01B c122m01b = service.getC122M01B_byMainIdItemType(meta.getMainId(), itemType);
				if(c122m01b!= null && Util.isNotEmpty(c122m01b.getJsonData())){
					JSONObject jsonObj = JSONObject.fromObject(c122m01b.getJsonData());
					noLabInsrMonth = Util.trim(jsonObj.optString("noLabInsrMonth"));
				}
			}
			result.set("noLabInsrMonth", noLabInsrMonth);
			result.set("receDate", "");
			break;		
		default:
			break;			
		}		
		
		return defaultResult( params, meta, result);
	}
		
	private CapAjaxFormResult defaultResult(PageParameters params, C122M01A meta,
			CapAjaxFormResult result) {		
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));
		result.set("applyDocStatus", Util.trim(meta.getApplyStatus()));
		result.set("titInfo", getTitInfo(meta));
		return result;
	}
	
	private String getTitInfo(C122M01A meta) {
		StringBuffer title = new StringBuffer();
		title.append(CapString.trimNull(meta.getCustId()));
		title.append(' ');
		title.append(CapString.trimNull(meta.getDupNo()));
		title.append(' ');
		title.append(CapString.trimNull(meta.getCustName()));
		return title.toString();
	}
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params) throws CapException {
		return _saveAction(params, "N");
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params) throws CapException {
		return _saveAction(params, "Y");
	}
	
	private IResult _saveAction(PageParameters params, String tempSave)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y", params.getString("allowIncomplete"));
		//-------------------
		String KEY = "saveOkFlag";
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);		
		String page = params.getString(EloanConstants.PAGE);
		C122M01A meta = null;
		try{
			meta = service.getC122M01A_byOid(mainOid);
			
			if ("01".equals(page)) {
				CapBeanUtil.map2Bean(params, meta, new String[] {"notifyMemo"
						, "statFlag"
				});
			}
			
			service.save(meta);			
			result.set(KEY, true);				
			
			
		}catch(Exception e){
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}	
		
		result.add(query(params));
		
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult changeOwnBrId(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String newBrNo = Util.trim(params.getString("newBrNo"));
		C122M01A meta = service.getC122M01A_byOid(mainOid);
		if(meta==null){
			throw new CapMessageException("["+mainOid+"]not found", getClass());
		}
		if(Util.equals(meta.getOwnBrId(), newBrNo)){
			throw new CapMessageException("未更新分行別", getClass());
		}

		meta.setOwnBrId(newBrNo);
		clsService.daoSave(meta);
		C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(meta.getMainId(), meta.getCustId(), meta.getDupNo());
		if(c120m01a!=null){
			c120m01a.setOwnBrId(newBrNo);
			clsService.daoSave(c120m01a);
		}

		return result;
	}

	/**
	 * 申請作廢
	 *
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult voidTheApply(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C122M01A meta = service.getC122M01A_byOid(mainOid);
		if(meta==null){
			throw new CapMessageException("["+mainOid+"]not found", getClass());
		} else {
			String statFlag = meta.getStatFlag();
			if ("X".equals(statFlag)) {
				throw new CapMessageException("本案已執行作廢，不可重覆執行", getClass());
			}
		}
		meta.setStatFlag("X");
		meta.setVoider(user.getUserId());
		meta.setVoidTime(CapDate.getCurrentTimestamp());
		clsService.daoSave(meta);

		result.set("voider", meta.getVoider());
		result.set("voidTime", meta.getVoidTime());
		result.set("statFlag", meta.getStatFlag());

		return result;
	}
	
	/**
	 * 狀態調回審核中
	 *
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult changeCaseStatus(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C122M01A meta = service.getC122M01A_byOid(mainOid);
		if(meta==null){
			throw new CapMessageException("["+mainOid+"]not found", getClass());
		} else {
			
			String statFlag = meta.getStatFlag();
			List<CodeType> codeTypeList = this.codeTypeService.findByCodeTypeAndCodeDescs("labor_c122m01a_statFlag_toCust", "不承作", null, null, "zh_TW");
			List<String> statusList = new ArrayList<String>();
			for(CodeType entity : codeTypeList){
				statusList.add(entity.getCodeValue());
			}
			
			if(!statusList.contains(statFlag)){
				throw new CapMessageException("不承作案件，才可變更", getClass());
			}
		}
		
		
		
		meta.setStatFlag(params.getString("statFlagChanged"));
		meta.setUpdater(user.getUserId());
		meta.setUpdateTime(CapDate.getCurrentTimestamp());
		clsService.daoSave(meta);
		
		result.set("statFlag", meta.getStatFlag());
		return result;
	}
}	
