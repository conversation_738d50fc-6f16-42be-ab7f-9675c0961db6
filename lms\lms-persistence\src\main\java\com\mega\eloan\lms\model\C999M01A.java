/* 
 * C999M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金約據書主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C999M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C999M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * 刪除註記<p/>
	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 
	 * 來源文件編號<p/>
	 * 資料來源：L120M01A.mainId
	 */
	@Column(name="SRCMAINID", length=32, columnDefinition="CHAR(32)")
	private String srcMainId;

	/** 
	 * 案件號碼-年度<p/>
	 * 資料來源：案件簽報書
	 */
	@Column(name="CASEYEAR", columnDefinition="DECIMAL(4,0)")
	private Integer caseYear;

	/** 
	 * 案件號碼-分行<p/>
	 * 資料來源：案件簽報書
	 */
	@Column(name="CASEBRID", length=3, columnDefinition="CHAR(3)")
	private String caseBrId;

	/** 
	 * 案件號碼-流水號<p/>
	 * 資料來源：案件簽報書
	 */
	@Column(name="CASESEQ", columnDefinition="DECIMAL(5,0)")
	private Integer caseSeq;

	/** 
	 * 案件號碼<p/>
	 * 資料來源：案件簽報書
	 */
	@Column(name="CASENO", length=62, columnDefinition="VARCHAR(62)")
	private String caseNo;

	/** 
	 * 簽案日期<p/>
	 * 資料來源：案件簽報書
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="CASEDATE", columnDefinition="DATE")
	private Date caseDate;

	/** 
	 * 約據書種類<p/>
	 * 一般<br/>
	 *  政策性留學貸款
	 */
	@Column(name="CONTRACTTYPE", length=1, columnDefinition="CHAR(1)")
	private String contractType;

	/** 
	 * 約據書類型<p/>
	 * A.契約書(含個別商議)<br/>
	 *  B.切結書<br/>
	 *  C.同意書<br/>
	 *  D.宣告書<br/>
	 *  E.增補條款(含個別商議)<br/>
	 *  F.其他
	 */
	@Column(name="CONTRACTKIND", length=1, columnDefinition="CHAR(1)")
	private String contractKind;

	/** 
	 * 約據書表單<p/>
	 * “”,AB01,BB01,BB02
	 */
	@Column(name="CONTRACTTYPE2", length=3, columnDefinition="CHAR(3)")
	private String contractType2;

	/** 連保書字號(字) **/
	@Column(name="CONTRACTWORD", length=30, columnDefinition="VARCHAR(30)")
	private String contractWord;

	/** 約據書編號/
連保書字號(號) **/
	@Column(name="CONTRACTNO", length=20, columnDefinition="VARCHAR(20)")
	private String contractNo;

	/** 
	 * 基準利率及調整<p/>
	 * 999.9999
	 */
	@Column(name="CONTRACTRATE", columnDefinition="DECIMAL(7,4)")
	private BigDecimal contractRate;

	/** 
	 * 資料保密_是否同意<p/>
	 * ※Y.同意, N.不同意<br/>
	 *  甲方及連保人 同意 不同意 乙方得將甲方及連保人之帳務、信用、投資及保險等資料，在合於營業登記項目或基於業務需要並遵守銀行所屬之金融控股公司及其各子公司
	 */
	@Column(name="DATAUSEFLAG", length=1, columnDefinition="CHAR(1)")
	private String dataUseFlag;

	/** 
	 * 資料保密_同意項目<p/>
	 * 以2進位儲存<br/>
	 *  2^0=1, 2^1=2, 2^2=4<br/>
	 *  同意者勾選：<br/>
	 *  1兆豐證券股份有限公司<br/>
	 *  2兆豐產物保險股份有限公司<br/>
	 *  4兆豐票券金融股份有限公司<br/>
	 *  8兆豐人身保險代理人股份有限公司<br/>
	 *  16兆豐國際證券投資信託股份有限公司<br/>
	 *  32兆豐資產管理股份有限公司<br/>
	 *  64兆豐創業投資股份有限公司<br/>
	 *  0.上述所有公司
	 */
	@Column(name="DATAUSEITEM", columnDefinition="DECIMAL(3,0)")
	private Integer dataUseItem;

	/** 管轄法院 **/
	@Column(name="COURTCODE", length=3, columnDefinition="CHAR(3)")
	private String courtCode;

	/** 
	 * MIS郵遞區號<p/>
	 * 戶籍地址/公司設立地址<br/>
	 *  資料來源：MIS.CUSTDATA.ADDRZIP
	 */
	@Column(name="ADDRZIP", columnDefinition="DECIMAL(5,0)")
	private Integer addrZip;

	/** 
	 * MIS地址(縣市)<p/>
	 * 戶籍地址/公司設立地址<br/>
	 *  資料來源：MIS.CUSTDATA.CITYR
	 */
	@Column(name="ADDRCITY", length=12, columnDefinition="VARCHAR(12)")
	private String addrCity;

	/** 
	 * MIS地址(區鄉鎮市)<p/>
	 * 戶籍地址/公司設立地址<br/>
	 *  資料來源：MIS.CUSTDATA.TOWNR
	 */
	@Column(name="ADDRTOWN", length=12, columnDefinition="VARCHAR(12)")
	private String addrTown;

	/** 
	 * MIS地址<p/>
	 * (含縣市、鄉鎮市區)<br/>
	 *  資料來源：MIS.CUSTDATA<br/>
	 *  CITYR+TOWNR+LEER+LINR+ADDRR
	 */
	@Column(name="ADDR", length=300, columnDefinition="VARCHAR(300)")
	private String addr;

	/** 
	 * 逾期在x個月以內<p/>
	 * 2012/09/25新增<br/>
	 *  違約金及遲延利息(預設：6)
	 */
	@Column(name="AMONTH", columnDefinition="DECIMAL(2,0)")
	private Integer aMonth;

	/** 
	 * 百分之x計付違約金<p/>
	 * 2012/09/25新增<br/>
	 *  違約金及遲延利息(預設：10)
	 */
	@Column(name="ARATE", columnDefinition="DECIMAL(2,0)")
	private Integer aRate;

	/** 
	 * 逾期超過x個月部份<p/>
	 * 2012/09/25新增<br/>
	 *  違約金及遲延利息(預設：6)
	 */
	@Column(name="BMONTH", columnDefinition="DECIMAL(2,0)")
	private Integer bMonth;

	/** 
	 * 百分之x計付違約金<p/>
	 * 2012/09/25新增<br/>
	 *  違約金及遲延利息(預設：20)
	 */
	@Column(name="BRATE", columnDefinition="DECIMAL(2,0)")
	private Integer bRate;

	/** 
	 * 計付遲延利息<p/>
	 * 2012/09/25新增<br/>
	 *  違約金及遲延利息
	 */
	@Column(name="CRATE", columnDefinition="DECIMAL(7,4)")
	private BigDecimal cRate;

	/** 
	 * RPTID<p/>
	 * 電子表單列印套版版本ID
	 */
	@Column(name="RPTID", length=32, columnDefinition="VARCHAR(32)")
	private String rptId;

	/** 
	 * 取得刪除註記<p/>
	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 
	 * 取得來源文件編號<p/>
	 * 資料來源：L120M01A.mainId
	 */
	public String getSrcMainId() {
		return this.srcMainId;
	}
	/**
	 *  設定來源文件編號<p/>
	 *  資料來源：L120M01A.mainId
	 **/
	public void setSrcMainId(String value) {
		this.srcMainId = value;
	}

	/** 
	 * 取得案件號碼-年度<p/>
	 * 資料來源：案件簽報書
	 */
	public Integer getCaseYear() {
		return this.caseYear;
	}
	/**
	 *  設定案件號碼-年度<p/>
	 *  資料來源：案件簽報書
	 **/
	public void setCaseYear(Integer value) {
		this.caseYear = value;
	}

	/** 
	 * 取得案件號碼-分行<p/>
	 * 資料來源：案件簽報書
	 */
	public String getCaseBrId() {
		return this.caseBrId;
	}
	/**
	 *  設定案件號碼-分行<p/>
	 *  資料來源：案件簽報書
	 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/** 
	 * 取得案件號碼-流水號<p/>
	 * 資料來源：案件簽報書
	 */
	public Integer getCaseSeq() {
		return this.caseSeq;
	}
	/**
	 *  設定案件號碼-流水號<p/>
	 *  資料來源：案件簽報書
	 **/
	public void setCaseSeq(Integer value) {
		this.caseSeq = value;
	}

	/** 
	 * 取得案件號碼<p/>
	 * 資料來源：案件簽報書
	 */
	public String getCaseNo() {
		return this.caseNo;
	}
	/**
	 *  設定案件號碼<p/>
	 *  資料來源：案件簽報書
	 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/** 
	 * 取得簽案日期<p/>
	 * 資料來源：案件簽報書
	 */
	public Date getCaseDate() {
		return this.caseDate;
	}
	/**
	 *  設定簽案日期<p/>
	 *  資料來源：案件簽報書
	 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/** 
	 * 取得約據書種類<p/>
	 * 一般<br/>
	 *  政策性留學貸款
	 */
	public String getContractType() {
		return this.contractType;
	}
	/**
	 *  設定約據書種類<p/>
	 *  一般<br/>
	 *  政策性留學貸款
	 **/
	public void setContractType(String value) {
		this.contractType = value;
	}

	/** 
	 * 取得約據書類型<p/>
	 * A.契約書(含個別商議)<br/>
	 *  B.切結書<br/>
	 *  C.同意書<br/>
	 *  D.宣告書<br/>
	 *  E.增補條款(含個別商議)<br/>
	 *  F.其他
	 */
	public String getContractKind() {
		return this.contractKind;
	}
	/**
	 *  設定約據書類型<p/>
	 *  A.契約書(含個別商議)<br/>
	 *  B.切結書<br/>
	 *  C.同意書<br/>
	 *  D.宣告書<br/>
	 *  E.增補條款(含個別商議)<br/>
	 *  F.其他
	 **/
	public void setContractKind(String value) {
		this.contractKind = value;
	}

	/** 
	 * 取得約據書表單<p/>
	 * “”,AB01,BB01,BB02
	 */
	public String getContractType2() {
		return this.contractType2;
	}
	/**
	 *  設定約據書表單<p/>
	 *  “”,AB01,BB01,BB02
	 **/
	public void setContractType2(String value) {
		this.contractType2 = value;
	}

	/** 取得連保書字號(字) **/
	public String getContractWord() {
		return this.contractWord;
	}
	/** 設定連保書字號(字) **/
	public void setContractWord(String value) {
		this.contractWord = value;
	}

	/** 取得約據書編號/
連保書字號(號) **/
	public String getContractNo() {
		return this.contractNo;
	}
	/** 設定約據書編號/
連保書字號(號) **/
	public void setContractNo(String value) {
		this.contractNo = value;
	}

	/** 
	 * 取得基準利率及調整<p/>
	 * 999.9999
	 */
	public BigDecimal getContractRate() {
		return this.contractRate;
	}
	/**
	 *  設定基準利率及調整<p/>
	 *  999.9999
	 **/
	public void setContractRate(BigDecimal value) {
		this.contractRate = value;
	}

	/** 
	 * 取得資料保密_是否同意<p/>
	 * ※Y.同意, N.不同意<br/>
	 *  甲方及連保人 同意 不同意 乙方得將甲方及連保人之帳務、信用、投資及保險等資料，在合於營業登記項目或基於業務需要並遵守銀行所屬之金融控股公司及其各子公司
	 */
	public String getDataUseFlag() {
		return this.dataUseFlag;
	}
	/**
	 *  設定資料保密_是否同意<p/>
	 *  ※Y.同意, N.不同意<br/>
	 *  甲方及連保人 同意 不同意 乙方得將甲方及連保人之帳務、信用、投資及保險等資料，在合於營業登記項目或基於業務需要並遵守銀行所屬之金融控股公司及其各子公司
	 **/
	public void setDataUseFlag(String value) {
		this.dataUseFlag = value;
	}

	/** 
	 * 取得資料保密_同意項目<p/>
	 * 以2進位儲存<br/>
	 *  2^0=1, 2^1=2, 2^2=4<br/>
	 *  同意者勾選：<br/>
	 *  1兆豐證券股份有限公司<br/>
	 *  2兆豐產物保險股份有限公司<br/>
	 *  4兆豐票券金融股份有限公司<br/>
	 *  8兆豐人身保險代理人股份有限公司<br/>
	 *  16兆豐國際證券投資信託股份有限公司<br/>
	 *  32兆豐資產管理股份有限公司<br/>
	 *  64兆豐創業投資股份有限公司<br/>
	 *  0.上述所有公司
	 */
	public Integer getDataUseItem() {
		return this.dataUseItem;
	}
	/**
	 *  設定資料保密_同意項目<p/>
	 *  以2進位儲存<br/>
	 *  2^0=1, 2^1=2, 2^2=4<br/>
	 *  同意者勾選：<br/>
	 *  1兆豐證券股份有限公司<br/>
	 *  2兆豐產物保險股份有限公司<br/>
	 *  4兆豐票券金融股份有限公司<br/>
	 *  8兆豐人身保險代理人股份有限公司<br/>
	 *  16兆豐國際證券投資信託股份有限公司<br/>
	 *  32兆豐資產管理股份有限公司<br/>
	 *  64兆豐創業投資股份有限公司<br/>
	 *  0.上述所有公司
	 **/
	public void setDataUseItem(Integer value) {
		this.dataUseItem = value;
	}

	/** 取得管轄法院 **/
	public String getCourtCode() {
		return this.courtCode;
	}
	/** 設定管轄法院 **/
	public void setCourtCode(String value) {
		this.courtCode = value;
	}

	/** 
	 * 取得MIS郵遞區號<p/>
	 * 戶籍地址/公司設立地址<br/>
	 *  資料來源：MIS.CUSTDATA.ADDRZIP
	 */
	public Integer getAddrZip() {
		return this.addrZip;
	}
	/**
	 *  設定MIS郵遞區號<p/>
	 *  戶籍地址/公司設立地址<br/>
	 *  資料來源：MIS.CUSTDATA.ADDRZIP
	 **/
	public void setAddrZip(Integer value) {
		this.addrZip = value;
	}

	/** 
	 * 取得MIS地址(縣市)<p/>
	 * 戶籍地址/公司設立地址<br/>
	 *  資料來源：MIS.CUSTDATA.CITYR
	 */
	public String getAddrCity() {
		return this.addrCity;
	}
	/**
	 *  設定MIS地址(縣市)<p/>
	 *  戶籍地址/公司設立地址<br/>
	 *  資料來源：MIS.CUSTDATA.CITYR
	 **/
	public void setAddrCity(String value) {
		this.addrCity = value;
	}

	/** 
	 * 取得MIS地址(區鄉鎮市)<p/>
	 * 戶籍地址/公司設立地址<br/>
	 *  資料來源：MIS.CUSTDATA.TOWNR
	 */
	public String getAddrTown() {
		return this.addrTown;
	}
	/**
	 *  設定MIS地址(區鄉鎮市)<p/>
	 *  戶籍地址/公司設立地址<br/>
	 *  資料來源：MIS.CUSTDATA.TOWNR
	 **/
	public void setAddrTown(String value) {
		this.addrTown = value;
	}

	/** 
	 * 取得MIS地址<p/>
	 * (含縣市、鄉鎮市區)<br/>
	 *  資料來源：MIS.CUSTDATA<br/>
	 *  CITYR+TOWNR+LEER+LINR+ADDRR
	 */
	public String getAddr() {
		return this.addr;
	}
	/**
	 *  設定MIS地址<p/>
	 *  (含縣市、鄉鎮市區)<br/>
	 *  資料來源：MIS.CUSTDATA<br/>
	 *  CITYR+TOWNR+LEER+LINR+ADDRR
	 **/
	public void setAddr(String value) {
		this.addr = value;
	}

	/** 
	 * 取得逾期在x個月以內<p/>
	 * 2012/09/25新增<br/>
	 *  違約金及遲延利息(預設：6)
	 */
	public Integer getAMonth() {
		return this.aMonth;
	}
	/**
	 *  設定逾期在x個月以內<p/>
	 *  2012/09/25新增<br/>
	 *  違約金及遲延利息(預設：6)
	 **/
	public void setAMonth(Integer value) {
		this.aMonth = value;
	}

	/** 
	 * 取得百分之x計付違約金<p/>
	 * 2012/09/25新增<br/>
	 *  違約金及遲延利息(預設：10)
	 */
	public Integer getARate() {
		return this.aRate;
	}
	/**
	 *  設定百分之x計付違約金<p/>
	 *  2012/09/25新增<br/>
	 *  違約金及遲延利息(預設：10)
	 **/
	public void setARate(Integer value) {
		this.aRate = value;
	}

	/** 
	 * 取得逾期超過x個月部份<p/>
	 * 2012/09/25新增<br/>
	 *  違約金及遲延利息(預設：6)
	 */
	public Integer getBMonth() {
		return this.bMonth;
	}
	/**
	 *  設定逾期超過x個月部份<p/>
	 *  2012/09/25新增<br/>
	 *  違約金及遲延利息(預設：6)
	 **/
	public void setBMonth(Integer value) {
		this.bMonth = value;
	}

	/** 
	 * 取得百分之x計付違約金<p/>
	 * 2012/09/25新增<br/>
	 *  違約金及遲延利息(預設：20)
	 */
	public Integer getBRate() {
		return this.bRate;
	}
	/**
	 *  設定百分之x計付違約金<p/>
	 *  2012/09/25新增<br/>
	 *  違約金及遲延利息(預設：20)
	 **/
	public void setBRate(Integer value) {
		this.bRate = value;
	}

	/** 
	 * 取得計付遲延利息<p/>
	 * 2012/09/25新增<br/>
	 *  違約金及遲延利息
	 */
	public BigDecimal getCRate() {
		return this.cRate;
	}
	/**
	 *  設定計付遲延利息<p/>
	 *  2012/09/25新增<br/>
	 *  違約金及遲延利息
	 **/
	public void setCRate(BigDecimal value) {
		this.cRate = value;
	}

	/** 
	 * 取得RPTID<p/>
	 * 電子表單列印套版版本ID
	 */
	public String getRptId() {
		return this.rptId;
	}
	/**
	 *  設定RPTID<p/>
	 *  電子表單列印套版版本ID
	 **/
	public void setRptId(String value) {
		this.rptId = value;
	}
}
