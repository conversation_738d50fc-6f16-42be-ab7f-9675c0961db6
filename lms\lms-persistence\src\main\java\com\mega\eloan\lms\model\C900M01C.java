/* 
 * C900M01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 產品種類關聯檔 **/
@NamedEntityGraph(name = "C900M01C-entity-graph", attributeNodes = { @NamedAttributeNode("c900m01b") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C900M01C", uniqueConstraints = @UniqueConstraint(columnNames = {
		"prodKind", "subjCode", "type" ,"souseCode"}))
public class C900M01C extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
		@JoinColumn(name = "PRODKIND", referencedColumnName = "PRODKIND", insertable = false, updatable = false),
		@JoinColumn(name = "SUBJCODE", referencedColumnName = "SUBJCODE", insertable = false, updatable = false)
		})
	private C900M01B c900m01b;
	
	public C900M01B getC900m01b() {
		return c900m01b;
	}

	public void setC900m01b(C900M01B c900m01b) {
		this.c900m01b = c900m01b;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	/** 產品代號 **/
	@Size(max = 2)
	@Column(name = "PRODKIND", length = 2, columnDefinition = "VARCHAR(2)", nullable = false)
	private String prodKind;

	/** 會計科子細目 **/
	@Size(max = 8)
	@Column(name = "SUBJCODE", length = 8, columnDefinition = "VARCHAR(8)", nullable = false)
	private String subjCode;

	/**
	 * 對應種類
	 * <p/>
	 * 1|查詢事項(額度明細表) join lms.c900s01a，寫入至 lms.l140m04a  <br/>
	 * 2|檢附文件(動審表) join lms.c900s01b，寫入至 lms.c160m01c
	 */
	@Size(max = 1)
	@Column(name = "TYPE", length = 1, columnDefinition = "CHAR(1)", nullable = false)
	private String type;

	/** 對應項目代碼 **/
	@Size(max = 10)
	@Column(name = "SOUSECODE", length = 10, columnDefinition = "VARCHAR(10)", nullable = false)
	private String souseCode;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得產品代號 **/
	public String getProdKind() {
		return this.prodKind;
	}

	/** 設定產品代號 **/
	public void setProdKind(String value) {
		this.prodKind = value;
	}

	/** 取得會計科子細目 **/
	public String getSubjCode() {
		return this.subjCode;
	}

	/** 設定會計科子細目 **/
	public void setSubjCode(String value) {
		this.subjCode = value;
	}

	/**
	 * 取得對應種類
	 * <p/>
	 * 1|查詢事項(額度明細表) join lms.c900s01a，寫入至 lms.l140m04a  <br/>
	 * 2|檢附文件(動審表) join lms.c900s01b，寫入至 lms.c160m01c
	 */
	public String getType() {
		return this.type;
	}

	/**
	 * 設定對應種類
	 * <p/>
	 * 1|查詢事項(額度明細表) join lms.c900s01a，寫入至 lms.l140m04a  <br/>
	 * 2|檢附文件(動審表) join lms.c900s01b，寫入至 lms.c160m01c
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/** 取得對應項目代碼 **/
	public String getSouseCode() {
		return this.souseCode;
	}

	/** 設定對應項目代碼 **/
	public void setSouseCode(String value) {
		this.souseCode = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
