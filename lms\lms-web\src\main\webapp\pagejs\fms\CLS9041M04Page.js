var pageAction = {
			grid : null,
			build : function(){
				pageAction.grid = $("#gridview").iGrid({
					//localFirst: true,
					handler : 'cls9041m04gridhandler',
					height : 400,
					action :  "query",
					rowNum:15,
					rownumbers:true,
					//multiselect : true,
					colModel : [{
						colHeader : i18n.cls9041m04["brno"], //承作分行
						name : 'brnoName', //col.id
						align : "left",
						width : 50, //設定寬度
						sortable : true //是否允許排序
						//formatter : 'click',
						//onclick : function,
						
					},{
						colHeader : i18n.cls9041m04["stuid"], //留學生id
						align : "left",
						align:"center",
						width : 50, //設定寬度
						sortable : true, //是否允許排序
						//formatter : 'click',
						//onclick : function,
						name : 'stuid' //col.id
					},{
						colHeader : i18n.cls9041m04["stuname"], //學生姓名
						align : "left",
						align:"center",
						width : 50, //設定寬度
						sortable : true, //是否允許排序
						//formatter : 'click',
						//onclick : function,
						name : 'rsfld1' //col.id
					},{
						colHeader : i18n.cls9041m04["educls"], //攻讀學位
						align : "left",
						align:"center",
						width : 50, //設定寬度
						sortable : true, //是否允許排序
						//formatter : 'click',
						//onclick : function,
						name : 'educls' //col.id
					},{
						colHeader : i18n.cls9041m04["fact_amt"], //貸款金額(十成)
						align : "center",
						align:"center",
						width : 50, //設定寬度
						sortable : true, //是否允許排序
						//formatter : 'click',
						//onclick : function,
						name : 'fact_amt' //col.id
					},{
						colHeader : i18n.cls9041m04["loan_val"], //餘額
						align : "left",
						align:"center",
						width : 50, //設定寬度
						sortable : true, //是否允許排序
						//formatter : 'click',
						//onclick : function,
						name : 'loan_val' //col.id
					}],
					ondblClickRow: function(rowid){
						
					}				
				});
			}
		}
$(document).ready(function(){
	pageAction.build();

});
