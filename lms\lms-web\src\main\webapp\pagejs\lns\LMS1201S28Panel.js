initDfd.done(function(json){

    var item = API.loadOrderCombosAsList("BlackListRelation")["BlackListRelation"];
	
	var a = {"value": "99" , "desc" : "董監事"};
	item.push(a);
	
    $("#custRelation").setItems({
        size: "2",
        item: item,
        clear: true,
        itemType: 'checkbox'
    });
    
    var item_type = API.loadOrderCombosAsList("lms1201s28_type")["lms1201s28_type"];
    $("#type").setItems({
        size: "2",
        item: item_type,
        clear: true,
        itemType: 'checkbox'
    });
    
    var gridviewQuery = $("#gridviewQuery").iGrid({
        handler: 'lms1201gridhandler',
        width: "100%",
        multiselect: false,
        sortname: 'custRelation|custId|dupNo',
        sortorder: 'asc|asc|asc',
        postData: {
            formAction: "queryL161S01E"
        },
        colModel: [{
            colHeader: i18n.lms1601m01['L160S01E.custId'],// "統編",
            name: 'custId',
            width: '60px',
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms1601m01['L160S01E.custName'],// "戶名",
            name: 'custName',
            width: '80px',
            sortable: true,
            align: "left",
            formatter: 'click',
            onclick: queryBox
        }, {
            colHeader: i18n.lms1601m01['L160S01E.custRelation'],// "與本案關係",
            name: 'custRelationIndex',
            width: '110px',
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms1601m01['L160S01E.type'],// "查詢項目",
            name: 'type',
            width: '50px',
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms1601m01['L160S01E.queryDateS'],// "資料查詢日",
            name: 'queryDateS',
            width: '40px',
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1601m01['L160S01E.chkYN'],// "資料檢誤",
            name: 'chkYN',
            width: '25px',
            sortable: true,
            align: "center"
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainid',
            hidden: true
        }],
        loadComplete: function(){
            //
        },
        ondblClickRow: function(rowid){
            var data = gridviewQuery.getRowData(rowid);
            queryBox(null, null, data);
        }
    });
    
    function queryBox(cellvalue, options, rowObject){
        //ilog.debug(rowObject);
        $("#L160S01EForm01").reset();
        
        $.ajax({
            handler: "lms1201formhandler",
            type: "POST",
            dataType: "json",
            action: "queryL161s01e",
            data: {
                oid: rowObject.oid
            },
            success: function(obj){
                var $L160S01EForm01 = $("#L160S01EForm01");
                for (o in obj.L160S01EForm01.custRelation) {
                    $L160S01EForm01.find("[name=custRelation]").each(function(i){
                        var $this = $(this);
                        if ($this.val() == obj.L160S01EForm01.custRelation[o]) {
                            $this.attr("checked", true);
                        }
                    });
                }
                
                for (o in obj.L160S01EForm01.type) {
                    $L160S01EForm01.find("[name=type]").each(function(i){
                        var $this = $(this);
                        if ($this.val() == obj.L160S01EForm01.type[o]) {
                            $this.attr("checked", true);
                        }
                    });
                }
                
                $L160S01EForm01.setData(obj.L160S01EForm01, false);
                
                thickboxOptions.readOnly = (responseJSON.mainDocStatus == "01O" ? false : true);
                $("#L160S01EDetail").thickbox({ // 使用選取的內容進行彈窗
                    title: "查詢名單",//"查詢名單",
                    width: 960,
                    height: 500,
                    modal: true,
                    i18n: i18n.def,
                    buttons: {
                        "saveData": function(){
                            var $L160S01EForm01 = $("#L160S01EForm01");
                            
                            var list = "", listType = "";
                            var sign = ",";
                            if ($L160S01EForm01.valid()) {
                                var checkCou = 0;
                                $L160S01EForm01.find("[name=custRelation]:checkbox:checked").each(function(i){
                                    list += ((list == "") ? "" : sign) + $(this).val();
                                    checkCou++;
                                });
                                if (checkCou == 0) {
                                    CommonAPI.showMessage("尚未勾選欄位【與本案關係】內容，請勾選");
                                    return;
                                }
                                
                                checkCou = 0;
                                $L160S01EForm01.find("[name=type]:checkbox:checked").each(function(i){
                                    listType += ((listType == "") ? "" : sign) + $(this).val();
                                    checkCou++;
                                });
                                if (checkCou == 0) {
                                    CommonAPI.showMessage("尚未勾選欄位【查詢項目】內容，請勾選");
                                    return;
                                }
                                
                                
                                $.ajax({
                                    handler: "lms1201formhandler",
                                    type: "POST",
                                    dataType: "json",
                                    action: "saveL161s01e",
                                    data: {
                                        L160S01EForm01: JSON.stringify($L160S01EForm01.serializeData()),
                                        mainId: responseJSON.mainid,
                                        custId: $("#L160S01EForm01").find("#custId").html(),
                                        dupNo: $("#L160S01EForm01").find("#dupNo").html(),
                                        oid: obj.L160S01EForm01.oid,
                                        list: list,
                                        listType: listType
                                    },
                                    success: function(json){
                                        oid = json.newOid;
                                        $("#gridviewQuery").trigger("reloadGrid");
                                    }
                                });
                            }
                        },
                        "close": function(){
                            API.confirmMessage(i18n.def['flow.exit'], function(res){
                                if (res) {
                                    $.thickbox.close();
                                }
                            });
                        }
                    }
                });
            }
        });
    }
    
    var gridviewRpaInfo = $("#gridviewRpaInfo").iGrid({
        handler: 'lms1201gridhandler',
        height: "250px",
        width: "100%",
        multiselect: false,
        sortname: 'type|queryTime',
        sortorder: 'asc|desc',
        postData: {
            formAction: "queryRpaInfo"
        },
        colModel: [{
            colHeader: i18n.lms1601m01['L160S01D.type'],// "資料類別",
            name: 'type',
            width: '85px',
            sortable: true,
            align: "left",
            formatter: 'click',
            onclick: rpaInfoBox
        }, {
            colHeader: i18n.lms1601m01['L160S01D.queryTime'],// "查詢時間",
            name: 'queryTime',
            width: '80px',
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1601m01['L160S01D.status'],// "查詢狀態",
            name: 'status',
            width: '40px',
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1601m01['L160S01D.memo'],// "備註",
            name: 'memo',
            width: '220px',
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms1601m01['L160S01D.reason'],// "回傳結果",
            name: 'reason',
            width: 100,
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms1601m01['L160S01D.rpaQueryReason1'],// "查詢條件1",
            name: 'rpaQueryReason1',
            width: 50,
            sortable: true,
            align: "left"
        }, {
            name: 'docfileoid',
            hidden: true
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainid',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = gridviewRpaInfo.getRowData(rowid);
            rpaInfoBox(null, null, data);
        }
    });
    
    function rpaInfoBox(cellvalue, options, rowData){
        if (rowData.docfileoid == null || rowData.docfileoid == "" || rowData.docfileoid == "undefined") {
            return false;
        }
        $.capFileDownload({
            handler: "simplefiledwnhandler",
            data: {
                fileOid: rowData.docfileoid
            }
        });
    }
    
    //重新引進查詢名單
    $("#importQueryList").click(function(){
        CommonAPI.confirmMessage("執行「重新引進查詢名單」後，請先確認名單查詢內容，再執行「一鍵查詢」。<br>是否確定執行本作業?", function(b){
            if (b) {
                $.ajax({
                    type: "POST",
                    handler: "lms1201formhandler",
                    data: {
                        formAction: "importQueryList",
                        oid: responseJSON.oid
                    },
                    success: function(responseData){
                        $("#gridviewQuery").trigger("reloadGrid");
                    }
                });
            }
        });
        
    });
    
    //一鍵查詢
    $("#rpaQuery").click(function(){
    
        $.ajax({
            handler: "lms1201formhandler",
            data: {
                formAction: "checkRpaData",
                page: responseJSON.page,
                showMsg: false, //是否顯示儲存成功
                txCode: responseJSON.txCode
            },
            success: function(result){
                if (result.confirmMsg != "") {
                    CommonAPI.confirmMessage(result.confirmMsg, function(b){
                        if (b) {
                            $.ajax({
                                type: "POST",
                                handler: "lms1201formhandler",
                                data: {
                                    formAction: "queryRpaQuery",
                                    oid: responseJSON.oid
                                },
                                success: function(responseData){
									$("#gridviewQuery").trigger("reloadGrid");
                                    $("#gridviewRpaInfo").trigger("reloadGrid");
                                }
                            });
                        }
                    });
                }
                else {
                    $.ajax({
                        type: "POST",
                        handler: "lms1201formhandler",
                        data: {
                            formAction: "queryRpaQuery",
                            oid: responseJSON.oid
                        },
                        success: function(responseData){
							$("#gridviewQuery").trigger("reloadGrid");
                            $("#gridviewRpaInfo").trigger("reloadGrid");
                        }
                    });
                }
            }
        });
    });
    
    $("#addQuery").click(function(){
        $("#L160S01EForm01").reset();
        
        $("#L160S01EDetail").thickbox({ // 使用選取的內容進行彈窗
            title: "查詢名單",//"查詢名單",
            width: 960,
            height: 500,
            modal: true,
            i18n: i18n.def,
            buttons: {
                "saveData": function(){
                    var $L160S01EForm01 = $("#L160S01EForm01");
                    
                    var list = "", listType = "";
                    var sign = ",";
                    if ($L160S01EForm01.valid()) {
                        var checkCou = 0;
                        $L160S01EForm01.find("[name=custRelation]:checkbox:checked").each(function(i){
                            list += ((list == "") ? "" : sign) + $(this).val();
                            checkCou++;
                        });
                        if (checkCou == 0) {
                            CommonAPI.showMessage("尚未勾選欄位【與本案關係】內容，請勾選");
                            return;
                        }
                        
                        checkCou = 0;
                        $L160S01EForm01.find("[name=type]:checkbox:checked").each(function(i){
                            listType += ((listType == "") ? "" : sign) + $(this).val();
                            checkCou++;
                        });
                        if (checkCou == 0) {
                            CommonAPI.showMessage("尚未勾選欄位【查詢項目】內容，請勾選");
                            return;
                        }
                        
                        
                        $.ajax({
                            handler: "lms1201formhandler",
                            type: "POST",
                            dataType: "json",
                            action: "saveL161s01e",
                            data: {
                                L160S01EForm01: JSON.stringify($L160S01EForm01.serializeData()),
                                mainId: responseJSON.mainid,
                                custId: $("#L160S01EForm01").find("#custId").html(),
                                dupNo: $("#L160S01EForm01").find("#dupNo").html(),
                                oid: $("#L160S01EForm01").find("#oid").val(),
                                list: list,
                                listType: listType
                            },
                            success: function(json){
								$("#L160S01EForm01").find("#oid").val(json.newOid);
                                $("#gridviewQuery").trigger("reloadGrid");
                            }
                        });
                    }
                },
                "close": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
    });
    
    $("#deleteQuery").click(function(){
        var row = $("#gridviewQuery").getGridParam('selrow');
        var data = $("#gridviewQuery").getRowData(row);
        
        if (data == undefined || data.oid == undefined) {
            CommonAPI.showMessage(i18n.def["grid.selrow"]);
            return;
        }
        
        CommonAPI.confirmMessage("是否確定要刪除名單?", function(b){
            if (b) {
                $.ajax({
                    type: "POST",
                    handler: "lms1201formhandler",
                    data: {
                        formAction: "deleteL161S01E",
                        oid: data.oid
                    },
                    success: function(responseData){
                        $("#gridviewQuery").trigger("reloadGrid");
                    }
                });
            }
        });
    });
    
    $("#refresh").click(function(){
        $("#gridviewRpaInfo").trigger("reloadGrid");
    });
    
    $("#reTry").click(function(){
        var row = $("#gridviewRpaInfo").getGridParam('selrow');
        var data = $("#gridviewRpaInfo").getRowData(row);
        
        if (data == undefined || data.oid == undefined) {
            CommonAPI.showMessage(i18n.def["grid.selrow"]);
            return;
        }
        
        var msg = "";
        if (data.status == "查詢中") {
            msg = "案件仍再查詢中，"
        }
        
        CommonAPI.confirmMessage(msg + "請確認是否重新查詢?", function(b){
            if (b) {
                $.ajax({
                    type: "POST",
                    handler: "lms1201formhandler",
                    data: {
                        formAction: "queryRpaRetry",
                        oid: data.oid
                    },
                    success: function(responseData){
                        $("#gridviewRpaInfo").trigger("reloadGrid");
                    }
                });
            }
        });
    });
    
    $("#printAll").click(function(){
        CommonAPI.confirmMessage(i18n.def["actoin_001"], function(b){
            if (b) {
                $.form.submit({
                    url: "../../simple/FileProcessingService",
                    target: "_blank",
                    data: {
                        mainId: responseJSON.mainId,
                        serviceName: "lms1016rptservice",
                        fileDownloadName: "lms1016rpt.pdf"
                    }
                });
            }
        });
    });
});



function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    ilog.debug("=============readOnly=============" + auth.readOnly);
    // 海外_編製中("01O"),       海外_待補件("07O"),       會簽後修改編製中("01K")
    if (auth.readOnly || (responseJSON.mainDocStatus != "01O" && responseJSON.mainDocStatus != "07O" && responseJSON.mainDocStatus != "01K")) {
        return true;
    }
    return false;
}

$(document).ready(function(){
	 
	if (checkReadonly()) {
        $(".readOnlyhide").hide();
        $("form").lockDoc();
		_openerLockDoc="1";
    }
	 
});