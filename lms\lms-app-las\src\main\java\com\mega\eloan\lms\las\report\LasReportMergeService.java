package com.mega.eloan.lms.las.report;

import java.io.InputStream;
import java.util.List;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.exception.CapException;

/**
 * 稽核工作底稿合併列印
 * <AUTHOR>
 *
 */

public interface LasReportMergeService {
	/**
	 * 產生報表
	 * 
	 * @param params
	 *            PageParameters
	 * @return 報表內容
	 * @throws CapException
	 *             Exception
	 */
	public List<InputStream> generateLasMergeReport(PageParameters params)
			throws CapException;
}
