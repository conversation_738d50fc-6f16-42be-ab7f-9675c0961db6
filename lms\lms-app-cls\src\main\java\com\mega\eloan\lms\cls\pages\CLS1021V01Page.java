/* 
 * CLS1021V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 購置房屋擔保放款風險權數檢核表- 編製中
 * </pre>
 * 
 * @since 2013/01/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/07,GaryChang,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1021v01")
public class CLS1021V01Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);

		// 加上Button
		List<EloanPageFragment> list = new ArrayList<EloanPageFragment>();
		// 主管跟經辦都會出現的按鈕

		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
			list.add(LmsButtonEnum.View);
		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			list.add(LmsButtonEnum.Add);
			list.add(LmsButtonEnum.Modify);
			list.add(LmsButtonEnum.Delete);
		}
		addToButtonPanel(model, list);

		renderJsI18N(CLS1021V01Page.class);
		
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/cls/CLS1021V01Page')");
	}

}
