/* 
 * LNF010.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import tw.com.iisi.cap.model.GenericBean;


/** 消金所得職業檔 **/
public class LNF010 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 客戶統編 **/
	@Column(name="LNF010_CUST_ID", length=11, columnDefinition="CHAR(11)",unique = true)
	private String lnf010_cust_id;

	/** 額度序號 **/
	@Column(name="LNF010_CONTRACT", length=12, columnDefinition="CHAR(12)")
	private String lnf010_contract;

	/** 建立時間 **/
	@Temporal(TemporalType.DATE)
	@Column(name="LNF010_CREATE_DT", columnDefinition="DATE")
	private Date lnf010_create_dt;

	/** 更新時間 **/
	@Column(name="LNF010_UPDATE_TS", columnDefinition="TIMESTAMP")
	private Date lnf010_update_ts;

	/** OBU戶註記 **/
	@Column(name="LNF010_OBU_FLAG", length=1, columnDefinition="CHAR(1)")
	private String lnf010_obu_flag;

	/** 收入註記 **/
	@Column(name="LNF010_REVENUE_FG", length=1, columnDefinition="CHAR(1)")
	private String lnf010_revenue_fg;

	/** 年薪(萬元/年) **/
	@Column(name="LNF010_SALARY", columnDefinition="DECIMAL(9,0)")
	private BigDecimal lnf010_salary;

	/** 其他所得(萬元/年) **/
	@Column(name="LNF010_OTH_REVENUE", columnDefinition="DECIMAL(9,0)")
	private BigDecimal lnf010_oth_revenue;

	/** 職業別 
	 * => LN.PROD.SOURCE(LNPBBA31) 把 e-loan 的職業大小類，轉換成報送JCIC的 [061500 , 061400 , ...]  
	 */
	@Column(name="LNF010_JOB_CLASS", length=4, columnDefinition="CHAR(4)")
	private String lnf010_job_class;

	/** 報送註記 **/
	@Column(name="LNF010_UPDATE_YN", length=1, columnDefinition="CHAR(1)")
	private String lnf010_update_yn;

	/** 更新人員 **/
	@Column(name="LNF010_UPDATER", length=6, columnDefinition="CHAR(6)")
	private String lnf010_updater;

	/** 取得客戶統編 **/
	public String getLnf010_cust_id() {
		return this.lnf010_cust_id;
	}
	/** 設定客戶統編 **/
	public void setLnf010_cust_id(String value) {
		this.lnf010_cust_id = value;
	}

	/** 取得額度序號 **/
	public String getLnf010_contract() {
		return this.lnf010_contract;
	}
	/** 設定額度序號 **/
	public void setLnf010_contract(String value) {
		this.lnf010_contract = value;
	}

	/** 取得建立時間 **/
	public Date getLnf010_create_dt() {
		return this.lnf010_create_dt;
	}
	/** 設定建立時間 **/
	public void setLnf010_create_dt(Date value) {
		this.lnf010_create_dt = value;
	}

	/** 取得更新時間 **/
	public Date getLnf010_update_ts() {
		return this.lnf010_update_ts;
	}
	/** 設定更新時間 **/
	public void setLnf010_update_ts(Date value) {
		this.lnf010_update_ts = value;
	}

	/** 取得OBU戶註記 **/
	public String getLnf010_obu_flag() {
		return this.lnf010_obu_flag;
	}
	/** 設定OBU戶註記 **/
	public void setLnf010_obu_flag(String value) {
		this.lnf010_obu_flag = value;
	}

	/** 取得收入註記 **/
	public String getLnf010_revenue_fg() {
		return this.lnf010_revenue_fg;
	}
	/** 設定收入註記 **/
	public void setLnf010_revenue_fg(String value) {
		this.lnf010_revenue_fg = value;
	}

	/** 取得年薪(萬元/年) **/
	public BigDecimal getLnf010_salary() {
		return this.lnf010_salary;
	}
	/** 設定年薪(萬元/年) **/
	public void setLnf010_salary(BigDecimal value) {
		this.lnf010_salary = value;
	}

	/** 取得其他所得(萬元/年) **/
	public BigDecimal getLnf010_oth_revenue() {
		return this.lnf010_oth_revenue;
	}
	/** 設定其他所得(萬元/年) **/
	public void setLnf010_oth_revenue(BigDecimal value) {
		this.lnf010_oth_revenue = value;
	}

	/** 取得職業別 **/
	public String getLnf010_job_class() {
		return this.lnf010_job_class;
	}
	/** 設定職業別 **/
	public void setLnf010_job_class(String value) {
		this.lnf010_job_class = value;
	}

	/** 取得報送註記 **/
	public String getLnf010_update_yn() {
		return this.lnf010_update_yn;
	}
	/** 設定報送註記 **/
	public void setLnf010_update_yn(String value) {
		this.lnf010_update_yn = value;
	}

	/** 取得更新人員 **/
	public String getLnf010_updater() {
		return this.lnf010_updater;
	}
	/** 設定更新人員 **/
	public void setLnf010_updater(String value) {
		this.lnf010_updater = value;
	}
}
