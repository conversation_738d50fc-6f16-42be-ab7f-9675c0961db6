/**
 * 常用主管名單共用js
 */
var lmsM03Json = {
	handlerName : null,
	// 設定handler名稱
	setHandler : function(){
		if(responseJSON.docURL == "/lms/lms1201m01"){
			// 授權外企金
			lmsM03Json.handlerName = "lms1201formhandler";
		}else if(responseJSON.docURL == "/lms/lms1101m01"){
			// 授權內企金
			lmsM03Json.handlerName = "lms1101formhandler";
		}else if(responseJSON.docURL == "/lms/lms1211m01"){
			// 授權外個金
			lmsM03Json.handlerName = "lms1211formhandler";
		}else if(responseJSON.docURL == "/lms/lms1111m01"){
			lmsM03Json.handlerName = "lms1111formhandler";
		}else{
			lmsM03Json.handlerName = "lms1301formhandler";
		}		
	},
	// 開啟常用主管名單ThickBox
	tZhuGuan : function(id){
		$("#tZhuGuan").thickbox({ // 使用選取的內容進行彈窗
			title : i18n.lmscommom["other.msg123"],	//other.msg123=常用主管名單
			width : 500,
			height : 250,
			modal : true,
			align : "center",
			valign : "bottom",
			i18n : i18n.def,
			buttons : {
				"sure" : function() {
					var zhuGuan = $("#formZhuGuan").find("#zhuGuan option:selected").val();
					if(zhuGuan != undefined && zhuGuan != null && zhuGuan != ''){
						var hasData = false;
						$("#"+id+" option").each(function(i){
							var $this = $(this);
							if($this.val() == zhuGuan){
								$this.attr("selected", true);
								hasData = true;
							}
						});
						if(!hasData){
							// 所選取常用名單人員未存在於該單位內，請確認...
							// other.msg124=所選取常用名單人員未存在於該單位內，請確認
							CommonAPI.showMessage(i18n.lmscommom["other.msg124"]);
							return;
						}
						$.thickbox.close();
					}else{
						// 請選擇...
						// other.msg125=請選擇一筆常用名單人員
						CommonAPI.showMessage(i18n.lmscommom["other.msg125"]);
						return;
					}					
				},						
				"cancel" : function() {
					 API.confirmMessage(i18n.def['flow.exit'], function(res){
							if(res){
								$.thickbox.close();
							}
				        });
				}
			}
		});		
	},
	// 查詢常用主管名單
	beforeOpen : function(id){
		$.ajax({ 
			handler : lmsM03Json.handlerName,
			type : "POST",
			dataType : "json",
			action : "queryL800M01a",
			data : {
				id : id,
				mainId : responseJSON.mainId
			},
			success : function(json) {
				$("#formZhuGuan").find("#zhuGuan").setItems({
	                item: json.formZhuGuan.zhuGuan,
	                format: "{value} - {key}"
				});
				lmsM03Json.tZhuGuan(id);
			}
		});		
	}			
};

$(function() {
	lmsM03Json.setHandler();
});