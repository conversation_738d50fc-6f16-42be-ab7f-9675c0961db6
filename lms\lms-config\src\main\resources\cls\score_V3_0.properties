#=====================================================
# \u8a55\u7b49\u53c3\u6578\u8a2d\u5b9a \u7248\u672c[varVer] \u5e38\u6578[BETA0]~[BETA9]
#=====================================================
varVer=3.0
#--------------------0.685\u7248--------------------
slope=0.115
interCept=-4.54

#===============================================================================
#\u8aaa\u660e\uff1a\u4ee5","\u70ba\u5206\u9694\u7b26\u865f[0]\u5206\u6578\u3001[1]\u8a55\u7b49\u516c\u5f0f(javascript)\u3001[3]\u70ba\u8a55\u7b49\u53c3\u6578,\u5206\u9694\u7b26\u865f\u70ba";"
#===============================================================================

#--------------------------------------------------------------
# \u8a55\u7b49\u9805\u76ee(\u57fa\u672c)
#--------------------------------------------------------------

#\u5bb6\u5ead\u8ca0\u50b5\u6bd4\u7387[scrYRate]
baseG_V3_0.scrYRate.01= 13.26, {0} < 20, yRate
baseG_V3_0.scrYRate.02= 10.34, {0} >= 20 && {0} < 30, yRate
baseG_V3_0.scrYRate.03= 9.13, {0} >= 30 && {0} < 55, yRate
baseG_V3_0.scrYRate.04= 7.31, {0} >= 55, yRate

#\u8077\u7a31\u5206\u6578[scrJobTitle]
baseG_V3_0.scrJobTitle.01= 12.34, /{0}/.test("a.g.h.k"),jobTitle
baseG_V3_0.scrJobTitle.02= 10.44, /{0}/.test("d.e.f"),jobTitle
baseG_V3_0.scrJobTitle.03= 6.99, /{0}/.test("b.c.i.i.j"),jobTitle
baseG_V3_0.scrJobTitle.04= 6.99, /{0}/.test("Null"),jobTitle

#\u7576\u6708\u6709\u6548\u4fe1\u7528\u5361\u4e3b\u5361\u5e73\u5747\u4fe1\u7528\u984d\u5ea6(\u4edf\u5143) [scrD42]
baseG_V3_0.scrD42.01= 3.13,/{0}/.test("Null"),itemD42
baseG_V3_0.scrD42.02= 6.07, {0} < 90, itemD42
baseG_V3_0.scrD42.03= 11.23, {0} >= 90 && {0} < 130, itemD42
baseG_V3_0.scrD42.04= 13.61, {0} >= 130, itemD42

#\u8fd112\u500b\u6708\u65b0\u696d\u52d9\u7533\u8acb\u67e5\u8a62\u7e3d\u6b21\u6578\uff0c\u8a08\u7b97\u65b9\u5f0f-\u5929\u6578\u5225(30\u5929\u5167\u7b971\u6b21)[scrN18Only]
#baseG_V3_0.scrN18Only.01= 15.25,/{0}/.test("Null"),itemN18
#baseG_V3_0.scrN18Only.02= 12.65, {0} < 1, itemN18
#baseG_V3_0.scrN18Only.03= 10.08, {0} >= 1 && {0} < 2, itemN18
#baseG_V3_0.scrN18Only.04= 7.36, {0} >= 2 && {0} < 3, itemN18
#baseG_V3_0.scrN18Only.05= 0.91,{0} >= 3, itemN18

#N22_INQ12_By30d[scrN18] \u8fd112\u500b\u6708\u65b0\u696d\u52d9\u7533\u8acb\u67e5\u8a62\u7e3d\u6b21\u6578\uff0c\u8a08\u7b97\u65b9\u5f0f-\u5929\u6578\u5225(30\u5929\u5167\u7b971\u6b21)(\u8fd1\u4e09\u500b\u6708\u672c\u884c\u67e5\u8a62\u4e0d\u5217\u5165\u8a08\u7b97)
#select * from com.bcodetype where codetype='lms1205s01_jobTitle' and locale='zh_TW'
baseG_V3_0.scrN22.01=15.41, /{0}/.test("Null") , itemN22
baseG_V3_0.scrN22.02=9.38, {0} < 2, itemN22
baseG_V3_0.scrN22.03=7.72, {0} >= 2 && {0} < 3 , itemN22
baseG_V3_0.scrN22.04=7.72, {0} >= 3 && {0} < 4  && /{1}/.test("g.h"), itemN22;jobTitle
baseG_V3_0.scrN22.05=7.72, {0} >= 4  && /{1}/.test("g.h"), itemN22;jobTitle
baseG_V3_0.scrN22.06=6.81, {0} >= 3 && {0} < 4  && /{1}/.test("a.b.c.d.e.f.i.j.k"), itemN22;jobTitle
baseG_V3_0.scrN22.07=-4.61, {0} >= 4  && /{1}/.test("a.b.c.d.e.f.i.j.k"), itemN22;jobTitle


#\u8fd13\u500b\u6708\u975eZ\u985e\u7533\u8acb\u67e5\u8a62\u7e3d\u6b21\u6578(\u8fd1\u4e09\u500b\u6708\u672c\u884c\u67e5\u8a62\u4e0d\u5217\u5165\u8a08\u7b97)[scrN01]
baseG_V3_0.scrN01.01= 12.48,/{0}/.test("Null"),itemN01
baseG_V3_0.scrN01.02= 8.81, {0} < 3, itemN01
baseG_V3_0.scrN01.03= 4.21, {0} >= 3, itemN01

#\u5b78\u6b77\u5206\u6578[scrEdu]
baseG_V3_0.scrEdu.01= 4.77, /{0}/.test("Null"),edu
baseG_V3_0.scrEdu.02= 4.77, /{0}/.test("01"),edu
baseG_V3_0.scrEdu.03= 6.60, /{0}/.test("02.03.04"),edu
baseG_V3_0.scrEdu.04= 10.09, /{0}/.test("05"),edu
baseG_V3_0.scrEdu.05= 15.38, /{0}/.test("06.07"),edu

#\u8fd16\u500b\u6708\u4fe1\u7528\u5361\u7e73\u6b3e\u72c0\u6cc1\u51fa\u73fe\u4e0d\u826f\u7e73\u6b3e\u7d00\u9304\u6216\u4f7f\u7528\u5faa\u74b0\u4fe1\u7528\u7684\u6b21\u6578 [scrP68]
baseG_V3_0.scrP68.01= 9.71, {0} < 1, itemP68
baseG_V3_0.scrP68.02= 7.95, {0} >= 1 && {0} < 2, itemP68
baseG_V3_0.scrP68.03= 4.41, {0} >= 2, itemP68

#\u8fd112\u500b\u6708\u4fe1\u7528\u5361\u7e73\u6b3e\u72c0\u6cc1\u51fa\u73fe\u5168\u984d\u7e73\u6e05\u7121\u5ef6\u9072\u6b21\u6578(\u4e0d\u542b\u7121\u9808\u7e73\u6b3e)[scrP19]
baseG_V3_0.scrP19.01= 4.22,/{0}/.test("Null"),chkNum3
baseG_V3_0.scrP19.02= 5.92, {0} < 7, chkNum3
baseG_V3_0.scrP19.03= 11.33, {0} >= 7, chkNum3

#\u8fd112\u500b\u6708\u4fe1\u7528\u5361(\u6bcf\u7b46)\u5faa\u74b0\u4fe1\u7528\u5e73\u5747\u4f7f\u7528\u7387_(mean(\u55ae\u7b46\u5faa\u74b0\u4fe1\u7528\u4f7f\u7528\u7387))[scrNum07]
baseG_V3_0.scrNum07.01= 5.91,/{0}/.test("Null"),avgRate01
baseG_V3_0.scrNum07.02= 10.70, {0} == 0, avgRate01
baseG_V3_0.scrNum07.03= 9.94, {0} < 0.008 && {0} != 0, avgRate01
baseG_V3_0.scrNum07.04= 5.84, {0} >= 0.008, avgRate01

#--------------------------------------------------------------
# \u8a55\u7b49\u9805\u76ee(\u7b49\u7d1a)
#--------------------------------------------------------------
#--------------------0.9\u7248--------------------
#grade.level.01= 1, {0} > 102.5, scrNum13
#grade.level.02= 2, {0} > 99.5 && {0} <= 102.5, scrNum13
#grade.level.03= 3, {0} > 96.4 && {0} <= 99.5, scrNum13
#grade.level.04= 4, {0} > 93.3 && {0} <= 96.4, scrNum13
#grade.level.05= 5, {0} > 90.2 && {0} <= 93.3, scrNum13
#grade.level.06= 6, {0} > 87.1 && {0} <= 90.2, scrNum13
#grade.level.07= 7, {0} > 84.1 && {0} <= 87.1, scrNum13
#grade.level.08= 8, {0} > 81.0 && {0} <= 84.1, scrNum13
#grade.level.09= 9, {0} > 77.8 && {0} <= 81.0, scrNum13
#grade.level.10= 10, {0} > 74.7 && {0} <= 77.8, scrNum13
#grade.level.11= 11, {0} > 71.5 && {0} <= 74.7, scrNum13
#grade.level.12= 12, {0} > 68.3 && {0} <= 71.5, scrNum13
#grade.level.13= 13, {0} > 65.0 && {0} <= 68.3, scrNum13
#grade.level.14= 14, {0} > 61.6 && {0} <= 65.0, scrNum13
#grade.level.15= 15, {0} <= 61.6, scrNum13

#--------------------0.47\u7248--------------------
#grade.level.01= 1, {0} > 97.2, scrNum13
#grade.level.02= 2, {0} > 94.1 && {0} <= 97.2, scrNum13
#grade.level.03= 3, {0} > 91.1 && {0} <= 94.1, scrNum13
#grade.level.04= 4, {0} > 88.0 && {0} <= 91.1, scrNum13
#grade.level.05= 5, {0} > 84.9 && {0} <= 88.0, scrNum13
#grade.level.06= 6, {0} > 81.8 && {0} <= 84.9, scrNum13
#grade.level.07= 7, {0} > 78.7 && {0} <= 81.8, scrNum13
#grade.level.08= 8, {0} > 75.6 && {0} <= 78.7, scrNum13
#grade.level.09= 9, {0} > 72.5 && {0} <= 75.6, scrNum13
#grade.level.10= 10, {0} > 69.4 && {0} <= 72.5, scrNum13
#grade.level.11= 11, {0} > 66.2 && {0} <= 69.4, scrNum13
#grade.level.12= 12, {0} > 63.0 && {0} <= 66.2, scrNum13
#grade.level.13= 13, {0} > 59.7 && {0} <= 63.0, scrNum13
#grade.level.14= 14, {0} > 56.3 && {0} <= 59.7, scrNum13
#grade.level.15= 15, {0} <= 56.3, scrNum13

#--------------------0.685\u7248--------------------
grade.level.01=1, {0} > 103.8, scrNum13
grade.level.02=2, {0} >= 100.3 && {0} <= 103.8, scrNum13
grade.level.03=3, {0} >= 96.8 && {0} <= 100.2, scrNum13
grade.level.04=4, {0} >= 93.3 && {0} <= 96.7, scrNum13
grade.level.05=5, {0} >= 89.7 && {0} <= 93.2, scrNum13
grade.level.06=6, {0} >= 86.2 && {0} <= 89.6, scrNum13
grade.level.07=7, {0} >= 82.6 && {0} <= 86.1, scrNum13
grade.level.08=8, {0} >= 79.1 && {0} <= 82.5, scrNum13
grade.level.09=9, {0} >= 75.5 && {0} <= 79.0, scrNum13
grade.level.10=10, {0} >= 71.9 && {0} <= 75.4, scrNum13
grade.level.11=11, {0} >= 68.3 && {0} <= 71.8, scrNum13
grade.level.12=12, {0} >= 64.6 && {0} <= 68.2, scrNum13
grade.level.13=13, {0} >= 60.8 && {0} <= 64.5, scrNum13
grade.level.14=14, {0} >= 56.9 && {0} <= 60.7, scrNum13
grade.level.15=15, {0} <= 56.8, scrNum13

#--------------------------------------------------------------
# \u623f\u8cb8\u8a55\u7b49\u9805\u76ee(\u9055\u7d04\u6a5f\u7387)
#--------------------------------------------------------------
houseLoanDR.dr_1YR.01=0.0005, /{0}/.test("1"), grade3
houseLoanDR.dr_1YR.02=0.0008, /{0}/.test("2"), grade3
houseLoanDR.dr_1YR.03=0.0011, /{0}/.test("3"), grade3
houseLoanDR.dr_1YR.04=0.0017, /{0}/.test("4"), grade3
houseLoanDR.dr_1YR.05=0.0025, /{0}/.test("5"), grade3
houseLoanDR.dr_1YR.06=0.0038, /{0}/.test("6"), grade3
houseLoanDR.dr_1YR.07=0.0057, /{0}/.test("7"), grade3
houseLoanDR.dr_1YR.08=0.0085, /{0}/.test("8"), grade3
houseLoanDR.dr_1YR.09=0.0128, /{0}/.test("9"), grade3
houseLoanDR.dr_1YR.10=0.0192, /{0}/.test("10"), grade3
houseLoanDR.dr_1YR.11=0.0288, /{0}/.test("11"), grade3
houseLoanDR.dr_1YR.12=0.0432, /{0}/.test("12"), grade3
houseLoanDR.dr_1YR.13=0.0649, /{0}/.test("13"), grade3
houseLoanDR.dr_1YR.14=0.0973, /{0}/.test("14"), grade3
houseLoanDR.dr_1YR.15=0.1460, /{0}/.test("15"), grade3
houseLoanDR.dr_1YR.16=0.0000, /{0}/.test("Null"), grade3



