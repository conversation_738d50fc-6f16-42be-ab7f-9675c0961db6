package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisEllnseekservice;

/**
 * <pre>
 * 表格名稱(中文)：	授信案件統計檔	表格名稱(英文)：	ELF404(ELCSECNT)
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,jessica,new
 *          </ul>
 */
@Service
public class MisEllnseekserviceImpl extends AbstractMFAloanJdbc implements
		MisEllnseekservice {

	@Override
	public List<Map<String, Object>> findMisEllnseekforNewReportType3ByBrNo(
			String benDate, String endDate, String ovUnitNo) {

		return this.getJdbc()
				.queryForList(
						"Ellnseek.selReport3ByUidBrNo",

						new Object[] { benDate, endDate, ovUnitNo, benDate,
								endDate, ovUnitNo });
	}

	@Override
	public List<Map<String, Object>> findByKey(String custId, String dupNo,
			String cntrNo) {
		return this.getJdbc().queryForList("MISELLNSEEK.selbyKey",
				new Object[] { custId, dupNo, cntrNo });
	}

	@Override
	public void insert(String custId, String dupNo, String cntrNo, String brNo,
			String status, String apprYY, String apprMM, String cType,
			String updater, String gutcDate, String proJno, String property,
			String byNewOld, String lnuseNo, String useFmDt, String useEnDt,
			int useFtMn, String hasAmFee, String experf_fg, String flaw_fg,
			BigDecimal flaw_amt, String ELF461_ISREVIVE_Y,
			String ELF461_MAINID, String ELF461_ISOFCLCGA,
			String ELF461_CGA_COUNTRY, String ELF461_CGA_CRDTYPE,
			String ELF461_CGA_CRDAREA, String ELF461_CGA_CRDPRED,
			String ELF461_CGA_CRDGRAD, BigDecimal ELF461_CGA_RSKRTO,
			BigDecimal ELF461_CGA_GRADSCR, String isSpecialFinRisk,
			String specialFinRiskType, String isCmsAdcRisk, String lnType,
			String yoPurpose, String subSidyut, String isProjectFinOperateStag,
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, String isHighQualityProjOpt_3,
			String isHighQualityProjOpt_4, String isHighQualityProjOpt_5, String isHighQualityProjResult,
			String curr, BigDecimal curAmt, String currL, BigDecimal curAmtL) {
		this.getJdbc()
				.update("MISELLNSEEK.insert",
						new Object[] { custId, dupNo, cntrNo, brNo, status,
								apprYY, apprMM, cType, updater, gutcDate,
								proJno, property, byNewOld, lnuseNo, useFmDt,
								useEnDt, useFtMn, hasAmFee, experf_fg, flaw_fg,
								flaw_amt, ELF461_ISREVIVE_Y, ELF461_MAINID,
								ELF461_ISOFCLCGA, ELF461_CGA_COUNTRY,
								ELF461_CGA_CRDTYPE, ELF461_CGA_CRDAREA,
								ELF461_CGA_CRDPRED, ELF461_CGA_CRDGRAD,
								ELF461_CGA_RSKRTO, ELF461_CGA_GRADSCR,
								isSpecialFinRisk, specialFinRiskType,
								isCmsAdcRisk, lnType, yoPurpose, subSidyut,
								isProjectFinOperateStag, isHighQualityProjOpt_1, 
								isHighQualityProjOpt_2, isHighQualityProjOpt_3, 
								isHighQualityProjOpt_4, isHighQualityProjOpt_5, 
								isHighQualityProjResult, curr, curAmt, currL, curAmtL });

	}

	@Override
	public void update(String custId, String dupNo, String cntrNo, String brNo,
			String status, String apprYY, String apprMM, String cType,
			String updater, String gutcDate, String proJno, String property,
			String byNewOld, String lnuseNo, String useFmDt, String useEnDt,
			int useFtMn, String hasAmFee, String experf_fg, String flaw_fg,
			BigDecimal flaw_amt, String ELF461_ISREVIVE_Y,
			String ELF461_MAINID, String ELF461_ISOFCLCGA,
			String ELF461_CGA_COUNTRY, String ELF461_CGA_CRDTYPE,
			String ELF461_CGA_CRDAREA, String ELF461_CGA_CRDPRED,
			String ELF461_CGA_CRDGRAD, BigDecimal ELF461_CGA_RSKRTO,
			BigDecimal ELF461_CGA_GRADSCR, String isSpecialFinRisk,
			String specialFinRiskType, String isCmsAdcRisk, String lnType,
			String yoPurpose, String subSidyut, String isProjectFinOperateStag,
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, String isHighQualityProjOpt_3,
			String isHighQualityProjOpt_4, String isHighQualityProjOpt_5, String isHighQualityProjResult,
			String curr, BigDecimal curAmt, String currL, BigDecimal curAmtL) {
		this.getJdbc().update(
				"MISELLNSEEK.update",
				new Object[] { brNo, status, apprYY, apprMM, cType, updater,
						gutcDate, proJno, property, byNewOld, lnuseNo, useFmDt,
						useEnDt, useFtMn, hasAmFee, experf_fg, flaw_fg,
						flaw_amt, ELF461_ISREVIVE_Y, ELF461_MAINID,
						ELF461_ISOFCLCGA, ELF461_CGA_COUNTRY,
						ELF461_CGA_CRDTYPE, ELF461_CGA_CRDAREA,
						ELF461_CGA_CRDPRED, ELF461_CGA_CRDGRAD,
						ELF461_CGA_RSKRTO, ELF461_CGA_GRADSCR,
						isSpecialFinRisk, specialFinRiskType, isCmsAdcRisk,
						lnType, yoPurpose, subSidyut, isProjectFinOperateStag,
						isHighQualityProjOpt_1, isHighQualityProjOpt_2, 
						isHighQualityProjOpt_3, isHighQualityProjOpt_4, 
						isHighQualityProjOpt_5, isHighQualityProjResult,
						curr, curAmt, currL, curAmtL,
						custId, dupNo, cntrNo });

	}

	@Override
	public void updateGutData(String custId, String dupNo, String cntrNo,
			String gutCutDate, String byNewOld, String property, String projNO) {
		this.getJdbc().update(
				"MISELLNSEEK.updateGutData",
				new Object[] { gutCutDate, byNewOld, property, projNO, custId,
						dupNo, cntrNo });

	}

	@Override
	public void updateOnlyRevive(String custId, String dupNo, String cntrNo,
			String ELF461_ISREVIVE_Y, String ELF461_MAINID) {
		this.getJdbc().update(
				"MISELLNSEEK.updateOnlyRevive",
				new Object[] { ELF461_ISREVIVE_Y, ELF461_MAINID, custId, dupNo,
						cntrNo });

	}

	@Override
	public void updateOnlySpecialFinRisk(String custId, String dupNo,
			String cntrNo, String timeStamp, String isSpecialFinRisk,
			String specialFinRiskType, String isCmsAdcRisk,
			String isProjectFinOperateStag,
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, String isHighQualityProjOpt_3, 
			String isHighQualityProjOpt_4, String isHighQualityProjOpt_5, String isHighQualityProjResult) {
		this.getJdbc().update(
				"MISELLNSEEK.updateOnlySpecialFinRisk",
				new Object[] { isSpecialFinRisk, specialFinRiskType,
						isCmsAdcRisk, isProjectFinOperateStag, 
						isHighQualityProjOpt_1, isHighQualityProjOpt_2, isHighQualityProjOpt_3,
						isHighQualityProjOpt_4, isHighQualityProjOpt_5, isHighQualityProjResult,
						custId, dupNo,
						cntrNo, timeStamp });

	}

	@Override
	public void insertOnlySpecialFinRisk(String custId, String dupNo,
			String cntrNo, String brNo, String status, String apprYY,
			String apprMM, String updater, String isSpecialFinRisk,
			String specialFinRiskType, String isCmsAdcRisk,
			String isProjectFinOperateStag,
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, String isHighQualityProjOpt_3, 
			String isHighQualityProjOpt_4, String isHighQualityProjOpt_5, String isHighQualityProjResult) {
		this.getJdbc().update(
				"MISELLNSEEK.insertOnlySpecialFinRisk",
				new Object[] { custId, dupNo, cntrNo, brNo, status, apprYY,
						apprMM, updater, isSpecialFinRisk, specialFinRiskType,
						isCmsAdcRisk, isProjectFinOperateStag,
						isHighQualityProjOpt_1, isHighQualityProjOpt_2, isHighQualityProjOpt_3,
						isHighQualityProjOpt_4, isHighQualityProjOpt_5, isHighQualityProjResult
				});

	}

	/**
	 * J-111-0506_05097_B1001 Web e-Loan企金授信動審表增加授信作業手續費之欄位
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param isOperationFee
	 * @param operationFeeCurr
	 * @param operationFeeAmt
	 * @param operationFeeDueDate
	 */
	@Override
	public void updateOperationFee(String custId, String dupNo, String cntrNo,
			String isOperationFee, String operationFeeCurr,
			BigDecimal operationFeeAmt, String operationFeeDueDate) {
		this.getJdbc().update(
				"MISELLNSEEK.updateOperationFee",
				new Object[] { isOperationFee, operationFeeCurr,
						operationFeeAmt, operationFeeDueDate, custId, dupNo,
						cntrNo });

	}
}
