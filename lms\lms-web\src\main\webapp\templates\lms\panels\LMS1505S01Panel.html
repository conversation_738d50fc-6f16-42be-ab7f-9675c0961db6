<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
	<div id="tab-01">
		<form id="L150M01AForm"> 
			<fieldset>
				<legend><th:block th:text="#{'doc.baseInfo'}"><!-- 基本資訊--></th:block></legend>
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
					<tbody>
						<tr>
							<td class="hd1" width="20%"><th:block th:text="#{'doc.branchName'}"><!--  分行名稱--></th:block></td>
							<td width="30%"><span id="ownBrId"></td>
							<td class="hd1" width="20%"></td>
							<td width="30%"></td>
						</tr>
					</tbody>
				</table>
			</fieldset>
			<br/>
			<fieldset>
				<legend><th:block th:text="#{'doc.docUpdateLog'}"><!--  文件異動紀錄--></th:block></legend>
				<div class="funcContainer">
					<!-- 文件異動紀錄 -->
					<div th:include="common/panels/DocLogPanel :: DocLogPanel"></div> 
				</div>
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
					<tbody>
						<tr>
							<td class="hd1" width="20%"><th:block th:text="#{'doc.creator'}"><!--  文件建立者--></th:block></td>
							<td width="30%"><span id='creator'/> (<span id='createTime'/>)</td>
							<td class="hd1" width="20%"><th:block th:text="#{'doc.lastUpdater'}"><!--  最後異動者--></th:block></td>
							<td width="30%"><span id='updater'/> (<span id='updateTime'/>)</td>
						</tr>
						<tr>
							<td class="hd1"></td>
							<td></td>
							<td class="hd1"><th:block th:text="#{'doc.docCode'}"><!--文件亂碼--></th:block>&nbsp;&nbsp;</td>
							<td><span id="randomCode"></span></td>
						</tr>
					</tbody>
				</table>
			</fieldset>
		</form>
		</div>
					
			<script type="text/javascript">
				require(['pagejs/lms/LMS1505M01Page'], function() {
			  	loadScript('pagejs/lms/LMS1505S01Panel');
				});
			</script>
			
	</th:block>
	

</body>
</html>
