var initDfd;
$(function() {
	initDfd = initDfd || $.Deferred();
    initGridView();
    initItem();

    $("#impCntrDoc").click(function(){
        $("#gridviewCntr").jqGrid("setGridParam", {
            postData: {
                init: false,
                mainId: responseJSON.mainid,
                itemType: "1"
            },
            search: true
        }).trigger("reloadGrid");

        $("#openCntrBox").thickbox({
            title: "",
            width: 800,
            height: 400,
            modal: true,
            i18n: i18n.def,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var rows = $("#gridviewCntr").getGridParam('selarrrow');
                    var data = [];

                    if (rows == 'undefined' || rows == null || rows == "") {   // action_005=請先選取一筆以上之資料列
                        return CommonAPI.showMessage(i18n.def["action_005"]);
                    }

                    /*
                    for (var i = 0; i < rows.length; i++) {
                        if (id[i] != "") {
                            var datas = $("#gridviewCntr").getRowData(id[i]);
                            data.push(datas.oid + "^" + datas.custId);
                        }
                    }
                    var oids = data.join("|");
                    */
                    for (var i in rows) {
                        data.push($("#gridviewCntr").getRowData(rows[i]).oid);
                    }

                    $.ajax({
                        handler: "lms1401s09formhandler",
                        data: {
                            formAction: "importL120S23A",
                            mainId: responseJSON.mainid,
                            oids: data
                        }
					}).done(function(obj) {
						$("#gridviewRWA").trigger("reloadGrid");
						$("#LMS1401S09Form02").hide();
						if (obj.isNeedRorwa && obj.isNeedRorwa != undefined && obj.isNeedRorwa != null && obj.isNeedRorwa == "Y") {
						    $("#gridviewRORWA").trigger("reloadGrid");
						    $("#LMS1401S09Form02").show();
						}
						$.thickbox.close();
						showTot();
                    });
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    });

    $("#delL120s23a").click(function(){
        var row = $("#gridviewRWA").getGridParam('selrow');

        if (!row) {
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        } else {
            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    var data = $("#gridviewRWA").getRowData(row);
                    var oid = data.oid;
                    $.ajax({
                        handler: "lms1401s09formhandler",
                        data: {
                            formAction: "deleteL120S23A",
                            mainId: responseJSON.mainid,
                            oids: oid
                        }
					}).done(function(obj) {
						$("#gridviewRWA").trigger("reloadGrid");
						$("#LMS1401S09Form02").hide();
						if (obj.isNeedRorwa && obj.isNeedRorwa != undefined && obj.isNeedRorwa != null && obj.isNeedRorwa == "Y") {
						    $("#gridviewRORWA").trigger("reloadGrid");
						    $("#LMS1401S09Form02").show();
						}
						showTot();
                    });
                }
            });
        }
    });

    $("#calcRWA").click(function(){
        $.ajax({
            handler: "lms1401s09formhandler",
            action: "calcAllRWA",
            data: {
                mainId: responseJSON.mainid
            }
		}).done(function(obj) {
			showTot();
			$("#gridviewRWA").trigger("reloadGrid");
        });
    });

    $("#calcRORWA").click(function(){
        $.ajax({
            handler: "lms1401s09formhandler",
            action: "calcAllRORWA",
            data: {
                mainId: responseJSON.mainid
            }
		}).done(function(obj) {
			showTot();
			$("#gridviewRORWA").trigger("reloadGrid");
        });
    });

	initDfd.done(function(auth) {
		if (checkReadonly() || thickboxOptions.readOnly) {
	        $("#LMS1401S09Form01").find("button").hide();
	        $("#LMS1401S09Form02").find("button").hide();
	        $("#formRwaDetail").find("button").hide();
	        $("#formRwaDetail").readOnlyChilds(true);
	    }

	    showRorwaForm();
	    showTot();
	});
});

function initGridView(){
    $("#gridviewRWA").iGrid({
        height: "230px",
        width: "100%",
        needPager: false,
        multiselect: false,
        handler: "lms1201gridhandler",
        postData: {
            formAction: "queryL120s23aList",
            mainId: responseJSON.mainid,
            cellvalue: "RWA"
        },
        loadComplete: function(){
            $('#gridviewRWA a').click(function(e){
                // 避免<a href="#"> go to top
                e.preventDefault();
            });
        },
        colModel: [{
            colHeader: i18n.lms1401s09["L120S23A.rwaCustId"],//借款人名稱
            name: 'rwaCustId',
            align: 'left',
            width: 30,
            sortable: false
        }, {
            colHeader: i18n.lms1401s09["L120S23A.rwaCntrNo"],
            name: 'rwaCntrNo',
            align: 'center',
            width: 30,
            sortable: false,
            formatter: 'click',
            onclick: function(cellvalue, options, rowObject){
                openRWA("RWA", null, rowObject);
            }
        }, {
            colHeader: i18n.lms1401s09["L120S23A.rwaProPerty"],
            name: 'rwaProPerty',
            align: 'left',
            width: 20,
            sortable: false
        }, {
            colHeader: i18n.lms1401s09["L120S23A.rwaApplyCurr"],
            name: 'rwaApplyCurr',
            align: 'left',
            width: 10,
            sortable: false
        }, {
            colHeader: i18n.lms1401s09["L120S23A.rwaApplyAmt"],
            name: 'rwaApplyAmt',
            align: 'right',
            width: 60,
            sortable: false,
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
                removeTrailingZero: true,
                decimalPlaces: 2,    //小數點到第幾位
                defaultValue: ""
            }
        }, {
            colHeader: i18n.lms1401s09["L120S23A.rwaRItemD"]+"(%)",
            name: 'rwaRItemD',
            align: 'right',
            width: 35,
            sortable: false,
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
                removeTrailingZero: true,
                decimalPlaces: 2,    //小數點到第幾位
                defaultValue: ""
            }
        }, {
            colHeader: i18n.lms1401s09["L120S23A.rwa"]+"<BR/>（新台幣元）",
            name: 'rwaTwd',
            align: 'right',
            width: 60,
            sortable: false,
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
                removeTrailingZero: true,
                decimalPlaces: 0,    //小數點到第幾位
                defaultValue: ""
            }
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridviewRWA").getRowData(rowid);
            openRWA("RWA", null, data);
        }
    });

    $("#gridviewRORWA").iGrid({
        height: "230px",
        width: "100%",
        needPager: false,
        multiselect: false,
        handler: "lms1201gridhandler",
        postData: {
            formAction: "queryL120s23aList",
            mainId: responseJSON.mainid,
            cellvalue: "RORWA"
        },
        loadComplete: function(){
            $('#gridviewRORWA a').click(function(e){
                // 避免<a href="#"> go to top
                e.preventDefault();
            });
        },
        colModel: [{
            colHeader: i18n.lms1401s09["L120S23A.rwaCustId"],//借款人名稱
            name: 'rwaCustId',
            align: 'left',
            width: 30,
            sortable: false
        }, {
            colHeader: i18n.lms1401s09["L120S23A.rwaCntrNo"],
            name: 'rwaCntrNo',
            align: 'center',
            width: 30,
            sortable: false,
            formatter: 'click',
            onclick: function(cellvalue, options, rowObject){
                openRWA("RORWA", null, rowObject);
            }
        }, {
            colHeader: i18n.lms1401s09["L120S23A.incomeRate"]+"(%)",
            name: 'incomeRate',
            align: 'right',
            width: 40,
            sortable: false,
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
                removeTrailingZero: true,
                decimalPlaces: 5,    //小數點到第幾位
                defaultValue: ""
            }
        }, {
            colHeader: i18n.lms1401s09["L120S23A.ftpRate"]+"(%)",
            name: 'ftpRate',
            align: 'right',
            width: 35,
            sortable: false,
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
                removeTrailingZero: true,
                decimalPlaces: 5,    //小數點到第幾位
                defaultValue: ""
            }
        }, {
            colHeader: i18n.lms1401s09["L120S23A.rateSpread"]+"(%)",
            name: 'rateSpread',
            align: 'right',
            width: 35,
            sortable: false,
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
                removeTrailingZero: true,
                decimalPlaces: 5,    //小數點到第幾位
                defaultValue: ""
            }
        }, {
            colHeader: i18n.lms1401s09["L120S23A.rorwa"]+"(%)",
            name: 'rorwa',
            align: 'right',
            width: 60,
            sortable: false,
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
                removeTrailingZero: true,
                decimalPlaces: 5,    //小數點到第幾位
                defaultValue: ""
            }
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridviewRORWA").getRowData(rowid);
            openRWA("RORWA", null, data);
        }
    });

    $("#gridviewCntr").iGrid({
        height: "230px",
        width: "100%",
        needPager: false,
        multiselect: true,
        handler: "lms1401gridhandler",
        action: "queryRwaL140m01a",
        postData: {
            init: true
        },
        loadComplete: function(){
            $('#gridviewCntr a').click(function(e){
                // 避免<a href="#"> go to top
                e.preventDefault();
            });
        },
        colModel: [{
            colHeader: i18n.lms1401s09["L120S23A.rwaCustId"],//借款人名稱
            name: 'custId',
            align: 'left',
            width: 35,
            sortable: false
        }, {
            colHeader: i18n.lms1401s09["L120S23A.rwaCntrNo"],
            name: 'cntrNo',
            align: 'center',
            width: 40,
            sortable: false
        }, {
            colHeader: i18n.lms1401s09["L120S23A.rwaProPerty"],
            name: 'proPerty',
            align: 'left',
            width: 20,
            sortable: false
        }, {
            colHeader: i18n.lms1401s09["L120S23A.rwaApplyAmt"],
            name: 'applyAmt',
            align: 'right',
            width: 80,
            sortable: false
        }, {
            colHeader: i18n.lms1401s09["L120S23A.rwaRItemD"]+"(%)",
            name: 'rItemD',
            align: 'right',
            width: 35,
            sortable: false
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }]
    });

    $("#RwaFtpRateGrid").iGrid({
        handler: "lms1201gridhandler",//inits.ghandle,
        needPager: false,
        multiselect: false,
        sortname: 'insCdCnm',
        sortorder: 'asc',
        postData : {
            formAction: "queryFtpRate"
        },
        colModel: [{
            colHeader: i18n.lms1401s09["L120S23A.item"],//"項目",
            name: 'insCdCnm',
            align: "center",
            width: 80,
            sortable: false
        }, {
            colHeader: " ",
            name: 'insRt',
            align: "center",
            width: 20,
            sortable: false
        }]
    });
}

function openRWA(cellvalue, options, rowObject) {
    var $form = $("#formRwaDetail");
    $form.reset();
    $form.find("input").each(function(){
        var $item = $(this);
        var itemType = $item.attr("type");
        if (itemType == "hidden") {
            // 代表是 injectData 自動生成的隱藏欄位 也就是畫面上沒有的欄位
            // .reset() 不會清到這塊  要特別清除  避免欄位值沒更新
            $item.val('');
        }
    });

    var buttons = {
        "saveData": function(){
            if ($form.valid()) {
                $.ajax({
                    handler: "lms1401s09formhandler",
                    data: $.extend($("#formRwaDetail").serializeData(), {
                        formAction: "saveL120S23A",
                        oid: $("#oidL120S23A").val(),
                        cellvalue: cellvalue
                    })
				}).done(function(obj) {
					$.thickbox.close();
					if (cellvalue == "RWA") {
					    $("#gridviewRWA").trigger("reloadGrid");
					    $("#LMS1401S09Form02").hide();
					    if (obj.isNeedRorwa && obj.isNeedRorwa != undefined && obj.isNeedRorwa != null && obj.isNeedRorwa == "Y") {
					        $("#gridviewRORWA").trigger("reloadGrid");
					        $("#LMS1401S09Form02").show();
					    }
					} else {
					    $("#gridviewRORWA").trigger("reloadGrid");
					}
					showTot();
                });
            }
        },
        "close": function(){
            $.thickbox.close();
        }
    }

    $.ajax({
        handler: "lms1401s09formhandler",
        action: "queryL120S23A",
        data: {
            oid: rowObject.oid
        }
	}).done(function(obj) {
		$form.injectData(obj);

		var boxHeight = 250;
		if (cellvalue == "RWA") {
		    $(".rorwaShow").hide();
		    $(".rorwaReadOnly").find("input, select").each(function(){
		        $(this).attr("readOnly", false).attr("disabled", false);;
		    });
		} else {
		    $(".rorwaShow").show();
		    $(".rorwaReadOnly").find("input, select").each(function(){
		        $(this).attr("readOnly", true).attr("disabled", true);;
		    });
		    boxHeight = 350;
		}

		$("#RwaDetailThickbox").thickbox({
		    title: "",
		    width: 700,
		    height: boxHeight,
		    modal: true,
		    readOnly: _openerLockDoc == "1" || thickboxOptions.readOnly,
		    i18n: i18n.def,
		    buttons: buttons
		});
    });
}

function initItem(){
    //select source
    var result_s = CommonAPI.loadCombos(["Common_Currcy"]);

    //幣別
    $(".money").setItems({
        item: result_s.Common_Currcy,
        format: "{value} - {key}"
    });
}

function showRorwaForm(){
    $.ajax({
        handler: "lms1401s09formhandler",
        action: "showRorwa",
        data: {
            mainId: responseJSON.mainid
        }
	}).done(function(obj) {
		if (obj.isNeedRorwa && obj.isNeedRorwa != undefined && obj.isNeedRorwa != null && obj.isNeedRorwa == "Y") {
		    $("#LMS1401S09Form02").show();
		} else {
		    $("#LMS1401S09Form02").hide();
		}
    });
}

function showTot(){
    $.ajax({
        handler: "lms1401s09formhandler",
        action: "queryL120s23aTot",
        data: {
            mainId: responseJSON.mainid
        }
	}).done(function(obj) {
		$("#rwaTwdTot").val(obj.rwaTwdTot);
		$("#wRorwaTwd").val(obj.wRorwaTwd);
    });
}

function checkReadonly(){
	var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    if (auth.readOnly || _openerLockDoc == "1") {
        return true;
    }
    return false;
}

function impFtpRate() {
//    var curr = $L120S08Form.find('#curr').val();
    $("#ftpRateCurrBox").thickbox({
        title: i18n.lms1401s09['other.curr'],
        width: 100,
        height: 20,
        align: "center",
        valign: "bottom",
        i18n: i18n.def,
        open: function(){
            $("input[name='ftpRateCurr'][value='TWD']").prop("checked", true);
        },
        buttons: {
            "sure": function(){
                var ftpRateCurr = $("[name=ftpRateCurr]:checked").val();
                if (ftpRateCurr == "") {
                    return CommonAPI.showErrorMessage(i18n.abstracteloan['plsSel']);
                }

                $.thickbox.close();

                $("#RwaFtpRateGrid").jqGrid("setGridParam", {
                    postData: {
                        curr: ftpRateCurr
                    },
                    search: true
                }).trigger("reloadGrid");

                $("#impRwaFtpRateThickBox").thickbox({ // 使用選取的內容進行彈窗
                    title: i18n.def['grid_selector'],
                    width: 500,
                    height: 250,
                    modal: true,
                    valign: "bottom",
                    align: "center",
                    i18n: i18n.def,
                    buttons: {
                        "sure": function(){
                            var rowId = $("#RwaFtpRateGrid").getGridParam('selrow');
                            if (rowId) {
                                var data = $("#RwaFtpRateGrid").getRowData(rowId);
                                $("#formRwaDetail").find("#ftpRate").val(data.insRt);
                                $.thickbox.close();
                            } else {
                                CommonAPI.showMessage(i18n.def['grid.selrow']);
                            }
                        },
                        "cancel": function(){
                            API.confirmMessage(i18n.def['flow.exit'], function(res){
                                if (res) {
                                    $.thickbox.close();
                                }
                            });
                        }
                    }
                });
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}
