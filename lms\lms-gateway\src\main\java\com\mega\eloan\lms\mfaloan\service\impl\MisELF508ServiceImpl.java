
package com.mega.eloan.lms.mfaloan.service.impl;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF508;
import com.mega.eloan.lms.mfaloan.service.MisELF508Service;

/**
 * <pre>
 * 天然及重大災害受災戶住宅補貼控制檔
 * </pre>
 * 
 * @since 2017/8/24
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/8/24,EL09301,new
 *          </ul>
 */
@Service
public class MisELF508ServiceImpl extends AbstractMFAloanJdbc implements MisELF508Service {

	@Override
	public ELF508 findByPk(String ELF508_CUST_ID, String ELF508_DISAS_TYPE,String ELF508_HOLD_NO,String ELF508_OWNER_ID) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList("ELF508.selByPk", new String[]{ELF508_CUST_ID, ELF508_DISAS_TYPE, ELF508_HOLD_NO, ELF508_OWNER_ID});
		List<ELF508> list = toELF508(rowData);
		if(list.size()==1){
			return list.get(0);
		}else{
			return null;
		}		
	}
	
	private List<ELF508> toELF508(List<Map<String, Object>> rowData){
		List<ELF508> list = new ArrayList<ELF508>();
		for (Map<String, Object> row : rowData) {
			ELF508 model = new ELF508();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF508> findByCustidDupno(String ELF508_CUST_ID, String dupNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"ELF508.findByCustidDupno", new Object[] { ELF508_CUST_ID, dupNo });

		List<ELF508> list = new ArrayList<ELF508>();
		for (Map<String, Object> row : rowData) {
			ELF508 model = new ELF508();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
	
	@Override
	public List<ELF508> findByCntrNo(String ELF508_CntrNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"ELF508.findByCntrNo", new Object[] { ELF508_CntrNo});

		List<ELF508> list = new ArrayList<ELF508>();
		for (Map<String, Object> row : rowData) {
			ELF508 model = new ELF508();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
	
	@Override
	public void deleteELF508ByPk(String ELF508_CUST_ID, String ELF508_DISAS_TYPE,String ELF508_HOLD_NO,String ELF508_OWNER_ID) {
		this.getJdbc().update("ELF508.delByPk", new Object[] { ELF508_CUST_ID, ELF508_DISAS_TYPE, ELF508_HOLD_NO, ELF508_OWNER_ID });
	}
	
	@Override
	public void insertELF508(String ELF508_BRNO, String ELF508_CUST_ID, String ELF508_DISAS_TYPE, String ELF508_HOLD_NO, String ELF508_OWNER_ID, 
			String ELF508_CNTRNO, String ELF508_LOAN_TYPE, Date ELF508_APP_DATE, BigDecimal ELF508_APP_AMT, String ELF508_HOUSE_ADR, String ELF508_OWNER_NM,
			String ELF508_OWNSP_ID, String ELF508_OWNSP_NM, String ELF508_SPOUSE_ID, String ELF508_SPOUSE_NM, String ELF508_COLL_LN, String ELF508_COLL_BN, 
			String ELF508_COLL_ADDR, String ELF508_SET_HOLD, Date ELF508_CANCEL_DATE, Timestamp ELF508_ELOAN_DATE, Timestamp ELF508_ALOAN_DATE) {
		this.getJdbc()
				.update("ELF508.insertELF508",
						new Object[] { ELF508_BRNO, ELF508_CUST_ID, ELF508_DISAS_TYPE, ELF508_HOLD_NO, ELF508_OWNER_ID, 
						ELF508_CNTRNO, ELF508_LOAN_TYPE, ELF508_APP_DATE, ELF508_APP_AMT, ELF508_HOUSE_ADR, ELF508_OWNER_NM,
						ELF508_OWNSP_ID, ELF508_OWNSP_NM, ELF508_SPOUSE_ID, ELF508_SPOUSE_NM, ELF508_COLL_LN, ELF508_COLL_BN, 
						ELF508_COLL_ADDR, ELF508_SET_HOLD, ELF508_CANCEL_DATE, ELF508_ELOAN_DATE, ELF508_ALOAN_DATE });
	}
}