<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <script type="text/javascript">
            	loadScript('pagejs/lrs/LMS1705S02Panel');
            </script>
            <fieldset>
                <legend>
                    <b>
                        <th:block th:text="#{'L170M01b.creditData'}">
                            一般授信資料
                        </th:block>
                    </b>
                </legend>
                &emsp; <span>&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;<b>
                        <th:block th:text="#{'L170M01b.unit2'}">
                            <!-- 金額單位-->
                        </th:block>
						<span id="totBalCurr"></span>
						<th:block th:text="#{'L170M01b.unit3'}">
                            <!-- 仟元-->
                        </th:block>
                    </b></span>
                <br/>
                <FORM id="lms1705S02Button1">
                    <table width="860" class="tb2" border="1" bordercolor="orange" cellspacing="0" style="margin: 3px, 3px, 3px, 3px;" align="center">
                        <tr>
                            <td width="20%" class="hd1">
                                <th:block th:text="#{'L170M01b.quotaAmtSum'}">
                                    額度合計
                                </th:block>
                            </td>
                            <td width="30%" style="text-align: right">
                            	<span id="totQuota" name="totQuota"></span>
                            </td>
                            <td width="20%" class="hd1">
                                <th:block th:text="#{'L170M01b.balCurrSum'}">
                                    前日結欠餘額合計
                                </th:block>
                            </td>
                            <td width="30%" style="text-align: right">
                            	<span id="totBal" name="totBal"></span>
                            </td>
                        </tr>
                    </table>
                </FORM>
                <div class="funcContainer">
                    <FORM id="lms1705S02Button2">
                        <table width="860">
                            <tr>
                                <td>
                                    <!--新增  -->
                                    <button id="_lms1705s02ADD" type="button">
                                        <span class="text-only">
                                            <th:block th:text="#{'button.add'}">
                                                新增
                                            </th:block>
                                        </span>
                                    </button>
                                    <!--刪除  -->
                                    <button id="_lms1705s02Delete" type="button">
                                        <span class="text-only">
                                            <th:block th:text="#{'button.delete'}">
                                                刪除
                                            </th:block>
                                        </span>
                                    </button>
                                    <!--產生所有授信資料  -->
                                    <button id="_lms1705s02ButtonADD" type="button">
                                        <span class="text-only">
                                            <th:block th:text="#{'L170M01b.bunCreatAll'}">
                                                產生所有授信資料
                                            </th:block>
                                        </span>
                                    </button>
                                    <button id="_lms1705s02ButtonDEL" type="button">
                                        <span class="text-only">
                                            <th:block th:text="#{'L170M01b.bunDeleteAll'}">
                                                刪除所有授信資料
                                            </th:block>
                                        </span>
                                    </button>
                                </td>
                                <td style="text-align: right">
                                	<th:block th:text="#{'L170M01b.lnDataDate'}">
                                              	 授信資料日期
                                            </th:block>：
                                    <span class="sscolor-blue" id="lnDataDate" name="lnDataDate"></span>
                                </td>
                            </tr>
                        </table>
                    </FORM>
                </div>
                <!-- 一般授信資料Grid顯示 -->
                <div id="L170M01bGrid"></div> 
                <!-- 一般授信資料彈跳視窗容 -->
                <div id="lms1705s02" style="display: none">
                    一般授信資料彈跳視窗的內容
                    <th:block id="_lms1705s02" th:include="lrs/panels/LMS1705S02Panel01 :: _lms1705s02"></th:block>
                </div>
            </fieldset>
        </th:block>
    </body>
</html>