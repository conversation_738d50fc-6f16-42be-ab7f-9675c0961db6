/* 
 * L830M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L830M01ADao;
import com.mega.eloan.lms.model.L830M01A;

/** 帳戶管理員維護 **/
@Repository
public class L830M01ADaoImpl extends LMSJpaDao<L830M01A, String>
	implements L830M01ADao {

	@Override
	public L830M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	
	@Override
	public List<L830M01A> findByBrno(String brNo, String creator) {
		ISearch search = createSearchTemplete();
		List<L830M01A> list = null;
		if(brNo != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", brNo);
		}
		if(creator != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "creator", creator);
		}
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<L830M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L830M01A> list = createQuery(search).getResultList();
		return list;
	}
}