package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 整批自動開戶
 * </pre>
 * 
 * @since 2017/03/05
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/03/05,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1161v05")
public class CLS1161V05Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);
		// 加上Button
		List<EloanPageFragment> list = new ArrayList<EloanPageFragment>();
		
		Set<String> allowBrSet = new HashSet<String>();
		allowBrSet.add("900");
		if(true){
			list.add(LmsButtonEnum.Filter);
			list.add(LmsButtonEnum.Add);
			list.add(LmsButtonEnum.Modify);
			list.add(LmsButtonEnum.Delete);
			if(allowBrSet.contains(user.getSsoUnitNo())){
				list.add(LmsButtonEnum.UPCls);
			}
			list.add(LmsButtonEnum.CreateExcel);
		}
		addToButtonPanel(model, list);

		// build i18n
		renderJsI18N(CLS1161M03Page.class);
		renderJsI18N(CLS1161V05Page.class);
		
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1161V05Page');");
	}

}
