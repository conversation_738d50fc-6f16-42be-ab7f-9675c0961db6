/* 
 * C900S02ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900S02A;

/** 房貸利率永慶信義100億統計 **/
public interface C900S02ADao extends IGenericDao<C900S02A> {

	public C900S02A findByOid(String oid);
	
	public Date findMaxTxnDate(String companyId5);
	
	public C900S02A findLNF090_UK(String loanNo, Date txnDate, Date solarDate, String txnTime);
}