/* 
 * LMS1601S02Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 動用審核表 - 動用審核表
 * </pre>
 * 
 * @since 2012/11/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/20,REX,new
 *          </ul>
 */
public class LMS1601S02Panel extends Panel {
	
	private boolean isShowAllocateFundListMsg;

	public LMS1601S02Panel(String id, boolean isShowAllocateFundListMsg) {
		super(id);
		this.isShowAllocateFundListMsg = isShowAllocateFundListMsg;
	}
	
	public LMS1601S02Panel(String id, boolean updatePanelName, boolean isShowAllocateFundListMsg) {
		super(id, updatePanelName);
		this.isShowAllocateFundListMsg = isShowAllocateFundListMsg;
	}
	
	

	/**/
	private static final long serialVersionUID = 1L;
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		model.addAttribute("show_allocateFundList_Msg", isShowAllocateFundListMsg);
	}

}
