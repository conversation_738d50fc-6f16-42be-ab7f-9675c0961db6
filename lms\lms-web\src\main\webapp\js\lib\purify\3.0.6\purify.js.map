{"version": 3, "file": "purify.js", "sources": ["../src/utils.js", "../src/tags.js", "../src/attrs.js", "../src/regexp.js", "../src/purify.js"], "sourcesContent": ["const {\n  entries,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n} = Object;\n\nlet { freeze, seal, create } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst regExpTest = unapply(RegExp.prototype.test);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\n/**\n * Creates a new function that calls the given function with a specified thisArg and arguments.\n *\n * @param {Function} func - The function to be wrapped and called.\n * @returns {Function} A new function that calls the given function with a specified thisArg and arguments.\n */\nfunction unapply(func) {\n  return (thisArg, ...args) => apply(func, thisArg, args);\n}\n\n/**\n * Creates a new function that constructs an instance of the given constructor function with the provided arguments.\n *\n * @param {Function} func - The constructor function to be wrapped and called.\n * @returns {Function} A new function that constructs an instance of the given constructor function with the provided arguments.\n */\nfunction unconstruct(func) {\n  return (...args) => construct(func, args);\n}\n\n/**\n * Add properties to a lookup table\n *\n * @param {Object} set - The set to which elements will be added.\n * @param {Array} array - The array containing elements to be added to the set.\n * @param {Function} transformCaseFunc - An optional function to transform the case of each element before adding to the set.\n * @returns {Object} The modified set with added elements.\n */\nfunction addToSet(set, array, transformCaseFunc = stringToLowerCase) {\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/**\n * Shallow clone an object\n *\n * @param {Object} object - The object to be cloned.\n * @returns {Object} A new object that copies the original.\n */\nexport function clone(object) {\n  const newObject = create(null);\n\n  for (const [property, value] of entries(object)) {\n    if (getOwnPropertyDescriptor(object, property) !== undefined) {\n      newObject[property] = value;\n    }\n  }\n\n  return newObject;\n}\n\n/**\n * This method automatically checks if the prop is function or getter and behaves accordingly.\n *\n * @param {Object} object - The object to look up the getter function in its prototype chain.\n * @param {String} prop - The property name for which to find the getter function.\n * @returns {Function} The getter function found in the prototype chain or a fallback function.\n */\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(element) {\n    console.warn('fallback value for', element);\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  // Object\n  entries,\n  freeze,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n  isFrozen,\n  setPrototypeOf,\n  seal,\n  create,\n  // RegExp\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n  // Other\n  lookupGetter,\n  addToSet,\n  // Reflect\n  unapply,\n  unconstruct,\n};\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n]);\n\n// SVG\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n]);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n]);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nexport const svgDisallowed = freeze([\n  'animate',\n  'color-profile',\n  'cursor',\n  'discard',\n  'font-face',\n  'font-face-format',\n  'font-face-name',\n  'font-face-src',\n  'font-face-uri',\n  'foreignobject',\n  'hatch',\n  'hatchpath',\n  'mesh',\n  'meshgradient',\n  'meshpatch',\n  'meshrow',\n  'missing-glyph',\n  'script',\n  'set',\n  'solidcolor',\n  'unknown',\n  'use',\n]);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n  'mprescripts',\n]);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nexport const mathMlDisallowed = freeze([\n  'maction',\n  'maligngroup',\n  'malignmark',\n  'mlongdiv',\n  'mscarries',\n  'mscarry',\n  'msgroup',\n  'mstack',\n  'msline',\n  'msrow',\n  'semantics',\n  'annotation',\n  'annotation-xml',\n  'mprescripts',\n  'none',\n]);\n\nexport const text = freeze(['#text']);\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'nonce',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'xmlns',\n  'slot',\n]);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'targetx',\n  'targety',\n  'transform',\n  'transform-origin',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n]);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n]);\n", "import { seal } from './utils.js';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nexport const TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nexport const DOCTYPE_NAME = seal(/^html$/i);\n", "import * as TAGS from './tags.js';\nimport * as ATTRS from './attrs.js';\nimport * as EXPRESSIONS from './regexp.js';\nimport {\n  addToSet,\n  clone,\n  entries,\n  freeze,\n  arrayForEach,\n  arrayPop,\n  arrayPush,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  typeErrorCreate,\n  lookupGetter,\n  create,\n} from './utils.js';\n\nconst getGlobal = function () {\n  return typeof window === 'undefined' ? null : window;\n};\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {HTMLScriptElement} purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported or creating the policy failed).\n */\nconst _createTrustedTypesPolicy = function (trustedTypes, purifyHostElement) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n    suffix = purifyHostElement.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nfunction createDOMPurify(window = getGlobal()) {\n  const DOMPurify = (root) => createDOMPurify(root);\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  DOMPurify.version = VERSION;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  DOMPurify.removed = [];\n\n  if (!window || !window.document || window.document.nodeType !== 9) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  let { document } = window;\n\n  const originalDocument = document;\n  const currentScript = originalDocument.currentScript;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  const ElementPrototype = Element.prototype;\n\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  let trustedTypesPolicy;\n  let emptyHTML = '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let hooks = {};\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    typeof entries === 'function' &&\n    typeof getParentNode === 'function' &&\n    implementation &&\n    implementation.createHTMLDocument !== undefined;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /*\n   * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(\n    create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false,\n      },\n    })\n  );\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'noscript',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'role',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet(\n    {},\n    [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE],\n    stringToString\n  );\n\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE = null;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc = null;\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function (testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg = {}) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n\n    PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1\n        ? (PARSER_MEDIA_TYPE = DEFAULT_PARSER_MEDIA_TYPE)\n        : (PARSER_MEDIA_TYPE = cfg.PARSER_MEDIA_TYPE);\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc =\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml'\n        ? stringToString\n        : stringToLowerCase;\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS =\n      'ALLOWED_TAGS' in cfg\n        ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc)\n        : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR =\n      'ALLOWED_ATTR' in cfg\n        ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc)\n        : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES =\n      'ALLOWED_NAMESPACES' in cfg\n        ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString)\n        : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES =\n      'ADD_URI_SAFE_ATTR' in cfg\n        ? addToSet(\n            clone(DEFAULT_URI_SAFE_ATTRIBUTES), // eslint-disable-line indent\n            cfg.ADD_URI_SAFE_ATTR, // eslint-disable-line indent\n            transformCaseFunc // eslint-disable-line indent\n          ) // eslint-disable-line indent\n        : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS =\n      'ADD_DATA_URI_TAGS' in cfg\n        ? addToSet(\n            clone(DEFAULT_DATA_URI_TAGS), // eslint-disable-line indent\n            cfg.ADD_DATA_URI_TAGS, // eslint-disable-line indent\n            transformCaseFunc // eslint-disable-line indent\n          ) // eslint-disable-line indent\n        : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS =\n      'FORBID_CONTENTS' in cfg\n        ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc)\n        : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS =\n      'FORBID_TAGS' in cfg\n        ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc)\n        : {};\n    FORBID_ATTR =\n      'FORBID_ATTR' in cfg\n        ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc)\n        : {};\n    USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || EXPRESSIONS.IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements ===\n        'boolean'\n    ) {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements =\n        cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, [...TAGS.text]);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    if (cfg.TRUSTED_TYPES_POLICY) {\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.'\n        );\n      }\n\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.'\n        );\n      }\n\n      // Overwrite existing TrustedTypes policy.\n      trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY;\n\n      // Sign local variables required by `sanitize`.\n      emptyHTML = trustedTypesPolicy.createHTML('');\n    } else {\n      // Uninitialized policy, attempt to initialize the internal dompurify policy.\n      if (trustedTypesPolicy === undefined) {\n        trustedTypesPolicy = _createTrustedTypesPolicy(\n          trustedTypes,\n          currentScript\n        );\n      }\n\n      // If creating the internal policy succeeded sign internal variables.\n      if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      }\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  const MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n    'mi',\n    'mo',\n    'mn',\n    'ms',\n    'mtext',\n  ]);\n\n  const HTML_INTEGRATION_POINTS = addToSet({}, [\n    'foreignobject',\n    'desc',\n    'title',\n    'annotation-xml',\n  ]);\n\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n    'title',\n    'style',\n    'font',\n    'a',\n    'script',\n  ]);\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, TAGS.svg);\n  addToSet(ALL_SVG_TAGS, TAGS.svgFilters);\n  addToSet(ALL_SVG_TAGS, TAGS.svgDisallowed);\n\n  const ALL_MATHML_TAGS = addToSet({}, TAGS.mathMl);\n  addToSet(ALL_MATHML_TAGS, TAGS.mathMlDisallowed);\n\n  /**\n   * @param  {Element} element a DOM element whose namespace is being checked\n   * @returns {boolean} Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function (element) {\n    let parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template',\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return (\n          tagName === 'svg' &&\n          (parentTagName === 'annotation-xml' ||\n            MATHML_TEXT_INTEGRATION_POINTS[parentTagName])\n        );\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (\n        parent.namespaceURI === SVG_NAMESPACE &&\n        !HTML_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      if (\n        parent.namespaceURI === MATHML_NAMESPACE &&\n        !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return (\n        !ALL_MATHML_TAGS[tagName] &&\n        (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName])\n      );\n    }\n\n    // For XHTML and XML documents that support custom namespaces\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      ALLOWED_NAMESPACES[element.namespaceURI]\n    ) {\n      return true;\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n  const _forceRemove = function (node) {\n    arrayPush(DOMPurify.removed, { element: node });\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      node.parentNode.removeChild(node);\n    } catch (_) {\n      node.remove();\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n  const _removeAttribute = function (name, node) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: node.getAttributeNode(name),\n        from: node,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: node,\n      });\n    }\n\n    node.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\"\" attributes\n    if (name === 'is' && !ALLOWED_ATTR[name]) {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(node);\n        } catch (_) {}\n      } else {\n        try {\n          node.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty) {\n    /* Create a HTML document */\n    let doc = null;\n    let leadingWhitespace = null;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      NAMESPACE === HTML_NAMESPACE\n    ) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty =\n        '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' +\n        dirty +\n        '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT\n          ? emptyHTML\n          : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(\n        doc,\n        WHOLE_DOCUMENT ? 'html' : 'body'\n      )[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * Creates a NodeIterator object that you can use to traverse filtered lists of nodes or elements in a document.\n   *\n   * @param  {Node} root The root element or node to start traversing on.\n   * @return {NodeIterator} The created NodeIterator\n   */\n  const _createNodeIterator = function (root) {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT,\n      null\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n  const _isClobbered = function (elm) {\n    return (\n      elm instanceof HTMLFormElement &&\n      (typeof elm.nodeName !== 'string' ||\n        typeof elm.textContent !== 'string' ||\n        typeof elm.removeChild !== 'function' ||\n        !(elm.attributes instanceof NamedNodeMap) ||\n        typeof elm.removeAttribute !== 'function' ||\n        typeof elm.setAttribute !== 'function' ||\n        typeof elm.namespaceURI !== 'string' ||\n        typeof elm.insertBefore !== 'function' ||\n        typeof elm.hasChildNodes !== 'function')\n    );\n  };\n\n  /**\n   * Checks whether the given object is a DOM node.\n   *\n   * @param  {Node} object object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n  const _isNode = function (object) {\n    return typeof Node === 'function' && object instanceof Node;\n  };\n\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n  const _executeHook = function (entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n\n    arrayForEach(hooks[entryPoint], (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function (currentNode) {\n    let content = null;\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeElements', currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (\n      currentNode.hasChildNodes() &&\n      !_isNode(currentNode.firstElementChild) &&\n      regExpTest(/<[/\\w]/g, currentNode.innerHTML) &&\n      regExpTest(/<[/\\w]/g, currentNode.textContent)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _isBasicCustomElement(tagName)) {\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n          regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)\n        ) {\n          return false;\n        }\n\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)\n        ) {\n          return false;\n        }\n      }\n\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            parentNode.insertBefore(\n              cloneNode(childNodes[i], true),\n              getNextSibling(currentNode)\n            );\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if (\n      (tagName === 'noscript' ||\n        tagName === 'noembed' ||\n        tagName === 'noframes') &&\n      regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        content = stringReplace(content, expr, ' ');\n      });\n\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeElements', currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (\n      ALLOW_DATA_ATTR &&\n      !FORBID_ATTR[lcName] &&\n      regExpTest(DATA_ATTR, lcName)\n    ) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        (_isBasicCustomElement(lcTag) &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag))) &&\n          ((CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName)) ||\n            (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)))) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        (lcName === 'is' &&\n          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))))\n      ) {\n        // If user has supplied a regexp or function in CUSTOM_ELEMENT_HANDLING.tagNameCheck, we need to also allow derived custom elements using the same tagName test.\n        // Additionally, we need to allow attributes passing the CUSTOM_ELEMENT_HANDLING.attributeNameCheck user has configured, as custom elements can define these at their own discretion.\n      } else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n    } else if (value) {\n      return false;\n    } else {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    }\n\n    return true;\n  };\n\n  /**\n   * _isBasicCustomElement\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   *\n   * @param {string} tagName name of the tag of the node to sanitize\n   * @returns {boolean} Returns true if the tag name meets the basic criteria for a custom element, otherwise false.\n   */\n  const _isBasicCustomElement = function (tagName) {\n    return tagName.indexOf('-') > 0;\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n  const _sanitizeAttributes = function (currentNode) {\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n    const { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n    };\n    let l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      const attr = attributes[l];\n      const { name, namespaceURI, value: attrValue } = attr;\n      const lcName = transformCaseFunc(name);\n\n      let value = name === 'value' ? attrValue : stringTrim(attrValue);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n      value = hookEvent.attrValue;\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Remove attribute */\n      _removeAttribute(name, currentNode);\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n          value = stringReplace(value, expr, ' ');\n        });\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n\n      /* Handle attributes that require Trusted Types */\n      if (\n        trustedTypesPolicy &&\n        typeof trustedTypes === 'object' &&\n        typeof trustedTypes.getAttributeType === 'function'\n      ) {\n        if (namespaceURI) {\n          /* Namespaces are not yet supported, see https://bugs.chromium.org/p/chromium/issues/detail?id=1305293 */\n        } else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML': {\n              value = trustedTypesPolicy.createHTML(value);\n              break;\n            }\n\n            case 'TrustedScriptURL': {\n              value = trustedTypesPolicy.createScriptURL(value);\n              break;\n            }\n\n            default: {\n              break;\n            }\n          }\n        }\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        arrayPop(DOMPurify.removed);\n      } catch (_) {}\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment) {\n    let shadowNode = null;\n    const shadowIterator = _createNodeIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(shadowNode)) {\n        continue;\n      }\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(shadowNode);\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} cfg object\n   */\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg = {}) {\n    let body = null;\n    let importedNode = null;\n    let currentNode = null;\n    let returnNode = null;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n\n    /* Return dirty HTML if DOMPurify cannot run */\n    if (!DOMPurify.isSupported) {\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if (dirty.nodeName) {\n        const tagName = transformCaseFunc(dirty.nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate(\n            'root node is forbidden and cannot be sanitized in-place'\n          );\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createNodeIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(currentNode)) {\n        continue;\n      }\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(currentNode);\n    }\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmode) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Serialize doctype if allowed */\n    if (\n      WHOLE_DOCUMENT &&\n      ALLOWED_TAGS['!doctype'] &&\n      body.ownerDocument &&\n      body.ownerDocument.doctype &&\n      body.ownerDocument.doctype.name &&\n      regExpTest(EXPRESSIONS.DOCTYPE_NAME, body.ownerDocument.doctype.name)\n    ) {\n      serializedHTML =\n        '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        serializedHTML = stringReplace(serializedHTML, expr, ' ');\n      });\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n  DOMPurify.setConfig = function (cfg = {}) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {String} tag Tag name of containing element.\n   * @param  {String} attr Attribute name.\n   * @param  {String} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   * @return {Function} removed(popped) hook\n   */\n  DOMPurify.removeHook = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      return arrayPop(hooks[entryPoint]);\n    }\n  };\n\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n  DOMPurify.removeHooks = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   */\n  DOMPurify.removeAllHooks = function () {\n    hooks = {};\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n"], "names": ["entries", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "Object", "freeze", "seal", "create", "apply", "construct", "Reflect", "x", "fun", "thisValue", "args", "Func", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayPop", "pop", "arrayPush", "push", "stringToLowerCase", "String", "toLowerCase", "stringToString", "toString", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "regExpTest", "RegExp", "test", "typeErrorCreate", "unconstruct", "TypeError", "func", "thisArg", "addToSet", "set", "array", "transformCaseFunc", "l", "length", "element", "lcElement", "clone", "object", "newObject", "property", "value", "undefined", "lookupGetter", "prop", "desc", "get", "fallback<PERSON><PERSON><PERSON>", "console", "warn", "html", "svg", "svgFilters", "svgDisallowed", "mathMl", "mathMlDisallowed", "text", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "purifyHostElement", "createPolicy", "suffix", "ATTR_NAME", "hasAttribute", "getAttribute", "policyName", "createHTML", "createScriptURL", "scriptUrl", "_", "createDOMPurify", "DOMPurify", "root", "version", "VERSION", "removed", "document", "nodeType", "isSupported", "originalDocument", "currentScript", "DocumentFragment", "HTMLTemplateElement", "Node", "Element", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "hooks", "createHTMLDocument", "EXPRESSIONS", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "Function", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "TRUSTED_TYPES_POLICY", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "Boolean", "_forceRemove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "remove", "_removeAttribute", "name", "attribute", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createNodeIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "_isClobbered", "elm", "nodeName", "textContent", "attributes", "hasChildNodes", "_isNode", "_executeHook", "entryPoint", "currentNode", "data", "hook", "_sanitizeElements", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_isBasicCustomElement", "childCount", "i", "expr", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "attr", "forceKeepAttr", "getAttributeType", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "returnNode", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmode", "serializedHTML", "outerHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"], "mappings": ";;;;;;;;EAAA,MAAM;EACJA,EAAAA,OADI;EAEJC,EAAAA,cAFI;EAGJC,EAAAA,QAHI;EAIJC,EAAAA,cAJI;EAKJC,EAAAA,wBAAAA;EALI,CAAA,GAMFC,MANJ,CAAA;EAQA,IAAI;EAAEC,EAAAA,MAAF;EAAUC,EAAAA,IAAV;EAAgBC,EAAAA,MAAAA;EAAhB,CAA2BH,GAAAA,MAA/B;;EACA,IAAI;EAAEI,EAAAA,KAAF;EAASC,EAAAA,SAAAA;EAAT,CAAA,GAAuB,OAAOC,OAAP,KAAmB,WAAnB,IAAkCA,OAA7D,CAAA;;EAEA,IAAI,CAACL,MAAL,EAAa;EACXA,EAAAA,MAAM,GAAG,SAAUM,MAAAA,CAAAA,CAAV,EAAa;EACpB,IAAA,OAAOA,CAAP,CAAA;EACD,GAFD,CAAA;EAGD,CAAA;;EAED,IAAI,CAACL,IAAL,EAAW;EACTA,EAAAA,IAAI,GAAG,SAAUK,IAAAA,CAAAA,CAAV,EAAa;EAClB,IAAA,OAAOA,CAAP,CAAA;EACD,GAFD,CAAA;EAGD,CAAA;;EAED,IAAI,CAACH,KAAL,EAAY;EACVA,EAAAA,KAAK,GAAG,SAAUI,KAAAA,CAAAA,GAAV,EAAeC,SAAf,EAA0BC,IAA1B,EAAgC;EACtC,IAAA,OAAOF,GAAG,CAACJ,KAAJ,CAAUK,SAAV,EAAqBC,IAArB,CAAP,CAAA;EACD,GAFD,CAAA;EAGD,CAAA;;EAED,IAAI,CAACL,SAAL,EAAgB;EACdA,EAAAA,SAAS,GAAG,SAAA,SAAA,CAAUM,IAAV,EAAgBD,IAAhB,EAAsB;EAChC,IAAA,OAAO,IAAIC,IAAJ,CAAS,GAAGD,IAAZ,CAAP,CAAA;EACD,GAFD,CAAA;EAGD,CAAA;;EAED,MAAME,YAAY,GAAGC,OAAO,CAACC,KAAK,CAACC,SAAN,CAAgBC,OAAjB,CAA5B,CAAA;EAEA,MAAMC,QAAQ,GAAGJ,OAAO,CAACC,KAAK,CAACC,SAAN,CAAgBG,GAAjB,CAAxB,CAAA;EACA,MAAMC,SAAS,GAAGN,OAAO,CAACC,KAAK,CAACC,SAAN,CAAgBK,IAAjB,CAAzB,CAAA;EAGA,MAAMC,iBAAiB,GAAGR,OAAO,CAACS,MAAM,CAACP,SAAP,CAAiBQ,WAAlB,CAAjC,CAAA;EACA,MAAMC,cAAc,GAAGX,OAAO,CAACS,MAAM,CAACP,SAAP,CAAiBU,QAAlB,CAA9B,CAAA;EACA,MAAMC,WAAW,GAAGb,OAAO,CAACS,MAAM,CAACP,SAAP,CAAiBY,KAAlB,CAA3B,CAAA;EACA,MAAMC,aAAa,GAAGf,OAAO,CAACS,MAAM,CAACP,SAAP,CAAiBc,OAAlB,CAA7B,CAAA;EACA,MAAMC,aAAa,GAAGjB,OAAO,CAACS,MAAM,CAACP,SAAP,CAAiBgB,OAAlB,CAA7B,CAAA;EACA,MAAMC,UAAU,GAAGnB,OAAO,CAACS,MAAM,CAACP,SAAP,CAAiBkB,IAAlB,CAA1B,CAAA;EAEA,MAAMC,UAAU,GAAGrB,OAAO,CAACsB,MAAM,CAACpB,SAAP,CAAiBqB,IAAlB,CAA1B,CAAA;EAEA,MAAMC,eAAe,GAAGC,WAAW,CAACC,SAAD,CAAnC,CAAA;EAEA;EACA;EACA;EACA;EACA;EACA;;EACA,SAAS1B,OAAT,CAAiB2B,IAAjB,EAAuB;EACrB,EAAA,OAAO,UAACC,OAAD,EAAA;EAAA,IAAA,KAAA,IAAA,IAAA,GAAA,SAAA,CAAA,MAAA,EAAa/B,IAAb,GAAA,IAAA,KAAA,CAAA,IAAA,GAAA,CAAA,GAAA,IAAA,GAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,GAAA,IAAA,EAAA,IAAA,EAAA,EAAA;EAAaA,MAAAA,IAAb,CAAA,IAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;EAAA,KAAA;;EAAA,IAAA,OAAsBN,KAAK,CAACoC,IAAD,EAAOC,OAAP,EAAgB/B,IAAhB,CAA3B,CAAA;EAAA,GAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;;;EACA,SAAS4B,WAAT,CAAqBE,IAArB,EAA2B;EACzB,EAAO,OAAA,YAAA;EAAA,IAAA,KAAA,IAAA,KAAA,GAAA,SAAA,CAAA,MAAA,EAAI9B,IAAJ,GAAA,IAAA,KAAA,CAAA,KAAA,CAAA,EAAA,KAAA,GAAA,CAAA,EAAA,KAAA,GAAA,KAAA,EAAA,KAAA,EAAA,EAAA;EAAIA,MAAAA,IAAJ,CAAA,KAAA,CAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA;EAAA,KAAA;;EAAA,IAAA,OAAaL,SAAS,CAACmC,IAAD,EAAO9B,IAAP,CAAtB,CAAA;EAAA,GAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,SAASgC,QAAT,CAAkBC,GAAlB,EAAuBC,KAAvB,EAAqE;EAAA,EAAvCC,IAAAA,iBAAuC,uEAAnBxB,iBAAmB,CAAA;;EACnE,EAAA,IAAIzB,cAAJ,EAAoB;EAClB;EACA;EACA;EACAA,IAAAA,cAAc,CAAC+C,GAAD,EAAM,IAAN,CAAd,CAAA;EACD,GAAA;;EAED,EAAA,IAAIG,CAAC,GAAGF,KAAK,CAACG,MAAd,CAAA;;EACA,EAAOD,OAAAA,CAAC,EAAR,EAAY;EACV,IAAA,IAAIE,OAAO,GAAGJ,KAAK,CAACE,CAAD,CAAnB,CAAA;;EACA,IAAA,IAAI,OAAOE,OAAP,KAAmB,QAAvB,EAAiC;EAC/B,MAAA,MAAMC,SAAS,GAAGJ,iBAAiB,CAACG,OAAD,CAAnC,CAAA;;EACA,MAAIC,IAAAA,SAAS,KAAKD,OAAlB,EAA2B;EACzB;EACA,QAAA,IAAI,CAACnD,QAAQ,CAAC+C,KAAD,CAAb,EAAsB;EACpBA,UAAAA,KAAK,CAACE,CAAD,CAAL,GAAWG,SAAX,CAAA;EACD,SAAA;;EAEDD,QAAAA,OAAO,GAAGC,SAAV,CAAA;EACD,OAAA;EACF,KAAA;;EAEDN,IAAAA,GAAG,CAACK,OAAD,CAAH,GAAe,IAAf,CAAA;EACD,GAAA;;EAED,EAAA,OAAOL,GAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;;;EACO,SAASO,KAAT,CAAeC,MAAf,EAAuB;EAC5B,EAAA,MAAMC,SAAS,GAAGjD,MAAM,CAAC,IAAD,CAAxB,CAAA;;EAEA,EAAK,KAAA,MAAM,CAACkD,QAAD,EAAWC,KAAX,CAAX,IAAgC3D,OAAO,CAACwD,MAAD,CAAvC,EAAiD;EAC/C,IAAIpD,IAAAA,wBAAwB,CAACoD,MAAD,EAASE,QAAT,CAAxB,KAA+CE,SAAnD,EAA8D;EAC5DH,MAAAA,SAAS,CAACC,QAAD,CAAT,GAAsBC,KAAtB,CAAA;EACD,KAAA;EACF,GAAA;;EAED,EAAA,OAAOF,SAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,SAASI,YAAT,CAAsBL,MAAtB,EAA8BM,IAA9B,EAAoC;EAClC,EAAON,OAAAA,MAAM,KAAK,IAAlB,EAAwB;EACtB,IAAA,MAAMO,IAAI,GAAG3D,wBAAwB,CAACoD,MAAD,EAASM,IAAT,CAArC,CAAA;;EAEA,IAAA,IAAIC,IAAJ,EAAU;EACR,MAAIA,IAAAA,IAAI,CAACC,GAAT,EAAc;EACZ,QAAA,OAAO9C,OAAO,CAAC6C,IAAI,CAACC,GAAN,CAAd,CAAA;EACD,OAAA;;EAED,MAAA,IAAI,OAAOD,IAAI,CAACJ,KAAZ,KAAsB,UAA1B,EAAsC;EACpC,QAAA,OAAOzC,OAAO,CAAC6C,IAAI,CAACJ,KAAN,CAAd,CAAA;EACD,OAAA;EACF,KAAA;;EAEDH,IAAAA,MAAM,GAAGrD,cAAc,CAACqD,MAAD,CAAvB,CAAA;EACD,GAAA;;EAED,EAASS,SAAAA,aAAT,CAAuBZ,OAAvB,EAAgC;EAC9Ba,IAAAA,OAAO,CAACC,IAAR,CAAa,oBAAb,EAAmCd,OAAnC,CAAA,CAAA;EACA,IAAA,OAAO,IAAP,CAAA;EACD,GAAA;;EAED,EAAA,OAAOY,aAAP,CAAA;EACD;;EC3JM,MAAMG,MAAI,GAAG9D,MAAM,CAAC,CACzB,GADyB,EAEzB,MAFyB,EAGzB,SAHyB,EAIzB,SAJyB,EAKzB,MALyB,EAMzB,SANyB,EAOzB,OAPyB,EAQzB,OARyB,EASzB,GATyB,EAUzB,KAVyB,EAWzB,KAXyB,EAYzB,KAZyB,EAazB,OAbyB,EAczB,YAdyB,EAezB,MAfyB,EAgBzB,IAhByB,EAiBzB,QAjByB,EAkBzB,QAlByB,EAmBzB,SAnByB,EAoBzB,QApByB,EAqBzB,MArByB,EAsBzB,MAtByB,EAuBzB,KAvByB,EAwBzB,UAxByB,EAyBzB,SAzByB,EA0BzB,MA1ByB,EA2BzB,UA3ByB,EA4BzB,IA5ByB,EA6BzB,WA7ByB,EA8BzB,KA9ByB,EA+BzB,SA/ByB,EAgCzB,KAhCyB,EAiCzB,QAjCyB,EAkCzB,KAlCyB,EAmCzB,KAnCyB,EAoCzB,IApCyB,EAqCzB,IArCyB,EAsCzB,SAtCyB,EAuCzB,IAvCyB,EAwCzB,UAxCyB,EAyCzB,YAzCyB,EA0CzB,QA1CyB,EA2CzB,MA3CyB,EA4CzB,QA5CyB,EA6CzB,MA7CyB,EA8CzB,IA9CyB,EA+CzB,IA/CyB,EAgDzB,IAhDyB,EAiDzB,IAjDyB,EAkDzB,IAlDyB,EAmDzB,IAnDyB,EAoDzB,MApDyB,EAqDzB,QArDyB,EAsDzB,QAtDyB,EAuDzB,IAvDyB,EAwDzB,MAxDyB,EAyDzB,GAzDyB,EA0DzB,KA1DyB,EA2DzB,OA3DyB,EA4DzB,KA5DyB,EA6DzB,KA7DyB,EA8DzB,OA9DyB,EA+DzB,QA/DyB,EAgEzB,IAhEyB,EAiEzB,MAjEyB,EAkEzB,KAlEyB,EAmEzB,MAnEyB,EAoEzB,SApEyB,EAqEzB,MArEyB,EAsEzB,UAtEyB,EAuEzB,OAvEyB,EAwEzB,KAxEyB,EAyEzB,MAzEyB,EA0EzB,IA1EyB,EA2EzB,UA3EyB,EA4EzB,QA5EyB,EA6EzB,QA7EyB,EA8EzB,GA9EyB,EA+EzB,SA/EyB,EAgFzB,KAhFyB,EAiFzB,UAjFyB,EAkFzB,GAlFyB,EAmFzB,IAnFyB,EAoFzB,IApFyB,EAqFzB,MArFyB,EAsFzB,GAtFyB,EAuFzB,MAvFyB,EAwFzB,SAxFyB,EAyFzB,QAzFyB,EA0FzB,QA1FyB,EA2FzB,OA3FyB,EA4FzB,QA5FyB,EA6FzB,QA7FyB,EA8FzB,MA9FyB,EA+FzB,QA/FyB,EAgGzB,QAhGyB,EAiGzB,OAjGyB,EAkGzB,KAlGyB,EAmGzB,SAnGyB,EAoGzB,KApGyB,EAqGzB,OArGyB,EAsGzB,OAtGyB,EAuGzB,IAvGyB,EAwGzB,UAxGyB,EAyGzB,UAzGyB,EA0GzB,OA1GyB,EA2GzB,IA3GyB,EA4GzB,OA5GyB,EA6GzB,MA7GyB,EA8GzB,IA9GyB,EA+GzB,OA/GyB,EAgHzB,IAhHyB,EAiHzB,GAjHyB,EAkHzB,IAlHyB,EAmHzB,KAnHyB,EAoHzB,OApHyB,EAqHzB,KArHyB,CAAD,CAAnB;;EAyHA,MAAM+D,KAAG,GAAG/D,MAAM,CAAC,CACxB,KADwB,EAExB,GAFwB,EAGxB,UAHwB,EAIxB,aAJwB,EAKxB,cALwB,EAMxB,cANwB,EAOxB,eAPwB,EAQxB,kBARwB,EASxB,QATwB,EAUxB,UAVwB,EAWxB,MAXwB,EAYxB,MAZwB,EAaxB,SAbwB,EAcxB,QAdwB,EAexB,MAfwB,EAgBxB,GAhBwB,EAiBxB,OAjBwB,EAkBxB,UAlBwB,EAmBxB,OAnBwB,EAoBxB,OApBwB,EAqBxB,MArBwB,EAsBxB,gBAtBwB,EAuBxB,QAvBwB,EAwBxB,MAxBwB,EAyBxB,UAzBwB,EA0BxB,OA1BwB,EA2BxB,MA3BwB,EA4BxB,SA5BwB,EA6BxB,SA7BwB,EA8BxB,UA9BwB,EA+BxB,gBA/BwB,EAgCxB,MAhCwB,EAiCxB,MAjCwB,EAkCxB,OAlCwB,EAmCxB,QAnCwB,EAoCxB,QApCwB,EAqCxB,MArCwB,EAsCxB,UAtCwB,EAuCxB,OAvCwB,EAwCxB,MAxCwB,EAyCxB,OAzCwB,EA0CxB,MA1CwB,EA2CxB,OA3CwB,CAAD,CAAlB,CAAA;EA8CA,MAAMgE,UAAU,GAAGhE,MAAM,CAAC,CAC/B,SAD+B,EAE/B,eAF+B,EAG/B,qBAH+B,EAI/B,aAJ+B,EAK/B,kBAL+B,EAM/B,mBAN+B,EAO/B,mBAP+B,EAQ/B,gBAR+B,EAS/B,cAT+B,EAU/B,SAV+B,EAW/B,SAX+B,EAY/B,SAZ+B,EAa/B,SAb+B,EAc/B,SAd+B,EAe/B,gBAf+B,EAgB/B,SAhB+B,EAiB/B,SAjB+B,EAkB/B,aAlB+B,EAmB/B,cAnB+B,EAoB/B,UApB+B,EAqB/B,cArB+B,EAsB/B,oBAtB+B,EAuB/B,aAvB+B,EAwB/B,QAxB+B,EAyB/B,cAzB+B,CAAD,CAAzB;EA6BP;EACA;EACA;;EACO,MAAMiE,aAAa,GAAGjE,MAAM,CAAC,CAClC,SADkC,EAElC,eAFkC,EAGlC,QAHkC,EAIlC,SAJkC,EAKlC,WALkC,EAMlC,kBANkC,EAOlC,gBAPkC,EAQlC,eARkC,EASlC,eATkC,EAUlC,eAVkC,EAWlC,OAXkC,EAYlC,WAZkC,EAalC,MAbkC,EAclC,cAdkC,EAelC,WAfkC,EAgBlC,SAhBkC,EAiBlC,eAjBkC,EAkBlC,QAlBkC,EAmBlC,KAnBkC,EAoBlC,YApBkC,EAqBlC,SArBkC,EAsBlC,KAtBkC,CAAD,CAA5B,CAAA;EAyBA,MAAMkE,QAAM,GAAGlE,MAAM,CAAC,CAC3B,MAD2B,EAE3B,UAF2B,EAG3B,QAH2B,EAI3B,SAJ2B,EAK3B,OAL2B,EAM3B,QAN2B,EAO3B,IAP2B,EAQ3B,YAR2B,EAS3B,eAT2B,EAU3B,IAV2B,EAW3B,IAX2B,EAY3B,OAZ2B,EAa3B,SAb2B,EAc3B,UAd2B,EAe3B,OAf2B,EAgB3B,MAhB2B,EAiB3B,IAjB2B,EAkB3B,QAlB2B,EAmB3B,OAnB2B,EAoB3B,QApB2B,EAqB3B,MArB2B,EAsB3B,MAtB2B,EAuB3B,SAvB2B,EAwB3B,QAxB2B,EAyB3B,KAzB2B,EA0B3B,OA1B2B,EA2B3B,KA3B2B,EA4B3B,QA5B2B,EA6B3B,YA7B2B,EA8B3B,aA9B2B,CAAD,CAArB;EAkCP;;EACO,MAAMmE,gBAAgB,GAAGnE,MAAM,CAAC,CACrC,SADqC,EAErC,aAFqC,EAGrC,YAHqC,EAIrC,UAJqC,EAKrC,WALqC,EAMrC,SANqC,EAOrC,SAPqC,EAQrC,QARqC,EASrC,QATqC,EAUrC,OAVqC,EAWrC,WAXqC,EAYrC,YAZqC,EAarC,gBAbqC,EAcrC,aAdqC,EAerC,MAfqC,CAAD,CAA/B,CAAA;EAkBA,MAAMoE,IAAI,GAAGpE,MAAM,CAAC,CAAC,OAAD,CAAD,CAAnB;;ECrRA,MAAM8D,IAAI,GAAG9D,MAAM,CAAC,CACzB,QADyB,EAEzB,QAFyB,EAGzB,OAHyB,EAIzB,KAJyB,EAKzB,gBALyB,EAMzB,cANyB,EAOzB,sBAPyB,EAQzB,UARyB,EASzB,YATyB,EAUzB,SAVyB,EAWzB,QAXyB,EAYzB,SAZyB,EAazB,aAbyB,EAczB,aAdyB,EAezB,SAfyB,EAgBzB,MAhByB,EAiBzB,OAjByB,EAkBzB,OAlByB,EAmBzB,OAnByB,EAoBzB,MApByB,EAqBzB,SArByB,EAsBzB,UAtByB,EAuBzB,cAvByB,EAwBzB,QAxByB,EAyBzB,aAzByB,EA0BzB,UA1ByB,EA2BzB,UA3ByB,EA4BzB,SA5ByB,EA6BzB,KA7ByB,EA8BzB,UA9ByB,EA+BzB,yBA/ByB,EAgCzB,uBAhCyB,EAiCzB,UAjCyB,EAkCzB,WAlCyB,EAmCzB,SAnCyB,EAoCzB,cApCyB,EAqCzB,MArCyB,EAsCzB,KAtCyB,EAuCzB,SAvCyB,EAwCzB,QAxCyB,EAyCzB,QAzCyB,EA0CzB,MA1CyB,EA2CzB,MA3CyB,EA4CzB,UA5CyB,EA6CzB,IA7CyB,EA8CzB,WA9CyB,EA+CzB,WA/CyB,EAgDzB,OAhDyB,EAiDzB,MAjDyB,EAkDzB,OAlDyB,EAmDzB,MAnDyB,EAoDzB,MApDyB,EAqDzB,SArDyB,EAsDzB,MAtDyB,EAuDzB,KAvDyB,EAwDzB,KAxDyB,EAyDzB,WAzDyB,EA0DzB,OA1DyB,EA2DzB,QA3DyB,EA4DzB,KA5DyB,EA6DzB,WA7DyB,EA8DzB,UA9DyB,EA+DzB,OA/DyB,EAgEzB,MAhEyB,EAiEzB,OAjEyB,EAkEzB,SAlEyB,EAmEzB,YAnEyB,EAoEzB,QApEyB,EAqEzB,MArEyB,EAsEzB,SAtEyB,EAuEzB,SAvEyB,EAwEzB,aAxEyB,EAyEzB,aAzEyB,EA0EzB,QA1EyB,EA2EzB,SA3EyB,EA4EzB,SA5EyB,EA6EzB,YA7EyB,EA8EzB,UA9EyB,EA+EzB,KA/EyB,EAgFzB,UAhFyB,EAiFzB,KAjFyB,EAkFzB,UAlFyB,EAmFzB,MAnFyB,EAoFzB,MApFyB,EAqFzB,SArFyB,EAsFzB,YAtFyB,EAuFzB,OAvFyB,EAwFzB,UAxFyB,EAyFzB,OAzFyB,EA0FzB,MA1FyB,EA2FzB,OA3FyB,EA4FzB,MA5FyB,EA6FzB,SA7FyB,EA8FzB,OA9FyB,EA+FzB,KA/FyB,EAgGzB,QAhGyB,EAiGzB,MAjGyB,EAkGzB,OAlGyB,EAmGzB,SAnGyB,EAoGzB,UApGyB,EAqGzB,OArGyB,EAsGzB,WAtGyB,EAuGzB,MAvGyB,EAwGzB,QAxGyB,EAyGzB,QAzGyB,EA0GzB,OA1GyB,EA2GzB,OA3GyB,EA4GzB,OA5GyB,EA6GzB,MA7GyB,CAAD,CAAnB,CAAA;EAgHA,MAAM+D,GAAG,GAAG/D,MAAM,CAAC,CACxB,eADwB,EAExB,YAFwB,EAGxB,UAHwB,EAIxB,oBAJwB,EAKxB,QALwB,EAMxB,eANwB,EAOxB,eAPwB,EAQxB,SARwB,EASxB,eATwB,EAUxB,gBAVwB,EAWxB,OAXwB,EAYxB,MAZwB,EAaxB,IAbwB,EAcxB,OAdwB,EAexB,MAfwB,EAgBxB,eAhBwB,EAiBxB,WAjBwB,EAkBxB,WAlBwB,EAmBxB,OAnBwB,EAoBxB,qBApBwB,EAqBxB,6BArBwB,EAsBxB,eAtBwB,EAuBxB,iBAvBwB,EAwBxB,IAxBwB,EAyBxB,IAzBwB,EA0BxB,GA1BwB,EA2BxB,IA3BwB,EA4BxB,IA5BwB,EA6BxB,iBA7BwB,EA8BxB,WA9BwB,EA+BxB,SA/BwB,EAgCxB,SAhCwB,EAiCxB,KAjCwB,EAkCxB,UAlCwB,EAmCxB,WAnCwB,EAoCxB,KApCwB,EAqCxB,MArCwB,EAsCxB,cAtCwB,EAuCxB,WAvCwB,EAwCxB,QAxCwB,EAyCxB,aAzCwB,EA0CxB,aA1CwB,EA2CxB,eA3CwB,EA4CxB,aA5CwB,EA6CxB,WA7CwB,EA8CxB,kBA9CwB,EA+CxB,cA/CwB,EAgDxB,YAhDwB,EAiDxB,cAjDwB,EAkDxB,aAlDwB,EAmDxB,IAnDwB,EAoDxB,IApDwB,EAqDxB,IArDwB,EAsDxB,IAtDwB,EAuDxB,YAvDwB,EAwDxB,UAxDwB,EAyDxB,eAzDwB,EA0DxB,mBA1DwB,EA2DxB,QA3DwB,EA4DxB,MA5DwB,EA6DxB,IA7DwB,EA8DxB,iBA9DwB,EA+DxB,IA/DwB,EAgExB,KAhEwB,EAiExB,GAjEwB,EAkExB,IAlEwB,EAmExB,IAnEwB,EAoExB,IApEwB,EAqExB,IArEwB,EAsExB,SAtEwB,EAuExB,WAvEwB,EAwExB,YAxEwB,EAyExB,UAzEwB,EA0ExB,MA1EwB,EA2ExB,cA3EwB,EA4ExB,gBA5EwB,EA6ExB,cA7EwB,EA8ExB,kBA9EwB,EA+ExB,gBA/EwB,EAgFxB,OAhFwB,EAiFxB,YAjFwB,EAkFxB,YAlFwB,EAmFxB,cAnFwB,EAoFxB,cApFwB,EAqFxB,aArFwB,EAsFxB,aAtFwB,EAuFxB,kBAvFwB,EAwFxB,WAxFwB,EAyFxB,KAzFwB,EA0FxB,MA1FwB,EA2FxB,OA3FwB,EA4FxB,QA5FwB,EA6FxB,MA7FwB,EA8FxB,KA9FwB,EA+FxB,MA/FwB,EAgGxB,YAhGwB,EAiGxB,QAjGwB,EAkGxB,UAlGwB,EAmGxB,SAnGwB,EAoGxB,OApGwB,EAqGxB,QArGwB,EAsGxB,aAtGwB,EAuGxB,QAvGwB,EAwGxB,UAxGwB,EAyGxB,aAzGwB,EA0GxB,MA1GwB,EA2GxB,YA3GwB,EA4GxB,qBA5GwB,EA6GxB,kBA7GwB,EA8GxB,cA9GwB,EA+GxB,QA/GwB,EAgHxB,eAhHwB,EAiHxB,qBAjHwB,EAkHxB,gBAlHwB,EAmHxB,GAnHwB,EAoHxB,IApHwB,EAqHxB,IArHwB,EAsHxB,QAtHwB,EAuHxB,MAvHwB,EAwHxB,MAxHwB,EAyHxB,aAzHwB,EA0HxB,WA1HwB,EA2HxB,SA3HwB,EA4HxB,QA5HwB,EA6HxB,QA7HwB,EA8HxB,OA9HwB,EA+HxB,MA/HwB,EAgIxB,iBAhIwB,EAiIxB,kBAjIwB,EAkIxB,kBAlIwB,EAmIxB,cAnIwB,EAoIxB,aApIwB,EAqIxB,cArIwB,EAsIxB,aAtIwB,EAuIxB,YAvIwB,EAwIxB,cAxIwB,EAyIxB,kBAzIwB,EA0IxB,mBA1IwB,EA2IxB,gBA3IwB,EA4IxB,iBA5IwB,EA6IxB,mBA7IwB,EA8IxB,gBA9IwB,EA+IxB,QA/IwB,EAgJxB,cAhJwB,EAiJxB,OAjJwB,EAkJxB,cAlJwB,EAmJxB,gBAnJwB,EAoJxB,UApJwB,EAqJxB,SArJwB,EAsJxB,SAtJwB,EAuJxB,WAvJwB,EAwJxB,kBAxJwB,EAyJxB,aAzJwB,EA0JxB,iBA1JwB,EA2JxB,gBA3JwB,EA4JxB,YA5JwB,EA6JxB,MA7JwB,EA8JxB,IA9JwB,EA+JxB,IA/JwB,EAgKxB,SAhKwB,EAiKxB,QAjKwB,EAkKxB,SAlKwB,EAmKxB,YAnKwB,EAoKxB,SApKwB,EAqKxB,YArKwB,EAsKxB,eAtKwB,EAuKxB,eAvKwB,EAwKxB,OAxKwB,EAyKxB,cAzKwB,EA0KxB,MA1KwB,EA2KxB,cA3KwB,EA4KxB,kBA5KwB,EA6KxB,kBA7KwB,EA8KxB,GA9KwB,EA+KxB,IA/KwB,EAgLxB,IAhLwB,EAiLxB,OAjLwB,EAkLxB,GAlLwB,EAmLxB,IAnLwB,EAoLxB,IApLwB,EAqLxB,GArLwB,EAsLxB,YAtLwB,CAAD,CAAlB,CAAA;EAyLA,MAAMkE,MAAM,GAAGlE,MAAM,CAAC,CAC3B,QAD2B,EAE3B,aAF2B,EAG3B,OAH2B,EAI3B,UAJ2B,EAK3B,OAL2B,EAM3B,cAN2B,EAO3B,aAP2B,EAQ3B,YAR2B,EAS3B,YAT2B,EAU3B,OAV2B,EAW3B,KAX2B,EAY3B,SAZ2B,EAa3B,cAb2B,EAc3B,UAd2B,EAe3B,OAf2B,EAgB3B,OAhB2B,EAiB3B,QAjB2B,EAkB3B,MAlB2B,EAmB3B,IAnB2B,EAoB3B,SApB2B,EAqB3B,QArB2B,EAsB3B,eAtB2B,EAuB3B,QAvB2B,EAwB3B,QAxB2B,EAyB3B,gBAzB2B,EA0B3B,WA1B2B,EA2B3B,UA3B2B,EA4B3B,aA5B2B,EA6B3B,SA7B2B,EA8B3B,SA9B2B,EA+B3B,eA/B2B,EAgC3B,UAhC2B,EAiC3B,UAjC2B,EAkC3B,MAlC2B,EAmC3B,UAnC2B,EAoC3B,UApC2B,EAqC3B,YArC2B,EAsC3B,SAtC2B,EAuC3B,QAvC2B,EAwC3B,QAxC2B,EAyC3B,aAzC2B,EA0C3B,eA1C2B,EA2C3B,sBA3C2B,EA4C3B,WA5C2B,EA6C3B,WA7C2B,EA8C3B,YA9C2B,EA+C3B,UA/C2B,EAgD3B,gBAhD2B,EAiD3B,gBAjD2B,EAkD3B,WAlD2B,EAmD3B,SAnD2B,EAoD3B,OApD2B,EAqD3B,OArD2B,CAAD,CAArB,CAAA;EAwDA,MAAMqE,GAAG,GAAGrE,MAAM,CAAC,CACxB,YADwB,EAExB,QAFwB,EAGxB,aAHwB,EAIxB,WAJwB,EAKxB,aALwB,CAAD,CAAlB;;EChWA,MAAMsE,aAAa,GAAGrE,IAAI,CAAC,2BAAD,CAA1B;;EACA,MAAMsE,QAAQ,GAAGtE,IAAI,CAAC,uBAAD,CAArB,CAAA;EACA,MAAMuE,WAAW,GAAGvE,IAAI,CAAC,eAAD,CAAxB,CAAA;EACA,MAAMwE,SAAS,GAAGxE,IAAI,CAAC,4BAAD,CAAtB;;EACA,MAAMyE,SAAS,GAAGzE,IAAI,CAAC,gBAAD,CAAtB;;EACA,MAAM0E,cAAc,GAAG1E,IAAI,CAChC,2FADgC;EAAA,CAA3B,CAAA;EAGA,MAAM2E,iBAAiB,GAAG3E,IAAI,CAAC,uBAAD,CAA9B,CAAA;EACA,MAAM4E,eAAe,GAAG5E,IAAI,CACjC,6DADiC;EAAA,CAA5B,CAAA;EAGA,MAAM6E,YAAY,GAAG7E,IAAI,CAAC,SAAD,CAAzB;;;;;;;;;;;;;;;ECQP,MAAM8E,SAAS,GAAG,SAAZA,SAAY,GAAY;EAC5B,EAAA,OAAO,OAAOC,MAAP,KAAkB,WAAlB,GAAgC,IAAhC,GAAuCA,MAA9C,CAAA;EACD,CAFD,CAAA;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,MAAMC,yBAAyB,GAAG,SAA5BA,yBAA4B,CAAUC,YAAV,EAAwBC,iBAAxB,EAA2C;EAC3E,EACE,IAAA,OAAOD,YAAP,KAAwB,QAAxB,IACA,OAAOA,YAAY,CAACE,YAApB,KAAqC,UAFvC,EAGE;EACA,IAAA,OAAO,IAAP,CAAA;EACD,GAN0E;EAS3E;EACA;;;EACA,EAAIC,IAAAA,MAAM,GAAG,IAAb,CAAA;EACA,EAAMC,MAAAA,SAAS,GAAG,uBAAlB,CAAA;;EACA,EAAIH,IAAAA,iBAAiB,IAAIA,iBAAiB,CAACI,YAAlB,CAA+BD,SAA/B,CAAzB,EAAoE;EAClED,IAAAA,MAAM,GAAGF,iBAAiB,CAACK,YAAlB,CAA+BF,SAA/B,CAAT,CAAA;EACD,GAAA;;EAED,EAAMG,MAAAA,UAAU,GAAG,WAAeJ,IAAAA,MAAM,GAAG,GAAMA,GAAAA,MAAT,GAAkB,EAAvC,CAAnB,CAAA;;EAEA,EAAI,IAAA;EACF,IAAA,OAAOH,YAAY,CAACE,YAAb,CAA0BK,UAA1B,EAAsC;EAC3CC,MAAAA,UAAU,CAAC5B,IAAD,EAAO;EACf,QAAA,OAAOA,IAAP,CAAA;EACD,OAH0C;;EAI3C6B,MAAAA,eAAe,CAACC,SAAD,EAAY;EACzB,QAAA,OAAOA,SAAP,CAAA;EACD,OAAA;;EAN0C,KAAtC,CAAP,CAAA;EAQD,GATD,CASE,OAAOC,CAAP,EAAU;EACV;EACA;EACA;EACAjC,IAAAA,OAAO,CAACC,IAAR,CACE,sBAAyB4B,GAAAA,UAAzB,GAAsC,wBADxC,CAAA,CAAA;EAGA,IAAA,OAAO,IAAP,CAAA;EACD,GAAA;EACF,CArCD,CAAA;;EAuCA,SAASK,eAAT,GAA+C;EAAA,EAAtBd,IAAAA,MAAsB,GAAbD,SAAAA,CAAAA,MAAAA,GAAAA,CAAAA,IAAAA,SAAAA,CAAAA,CAAAA,CAAAA,KAAAA,SAAAA,GAAAA,SAAAA,CAAAA,CAAAA,CAAAA,GAAAA,SAAS,EAAI,CAAA;;EAC7C,EAAA,MAAMgB,SAAS,GAAIC,IAAD,IAAUF,eAAe,CAACE,IAAD,CAA3C,CAAA;EAEA;EACF;EACA;EACA;;;EACED,EAAAA,SAAS,CAACE,OAAV,GAAoBC,OAApB,CAAA;EAEA;EACF;EACA;EACA;;EACEH,EAAAA,SAAS,CAACI,OAAV,GAAoB,EAApB,CAAA;;EAEA,EAAA,IAAI,CAACnB,MAAD,IAAW,CAACA,MAAM,CAACoB,QAAnB,IAA+BpB,MAAM,CAACoB,QAAP,CAAgBC,QAAhB,KAA6B,CAAhE,EAAmE;EACjE;EACA;EACAN,IAAAA,SAAS,CAACO,WAAV,GAAwB,KAAxB,CAAA;EAEA,IAAA,OAAOP,SAAP,CAAA;EACD,GAAA;;EAED,EAAI,IAAA;EAAEK,IAAAA,QAAAA;EAAF,GAAA,GAAepB,MAAnB,CAAA;EAEA,EAAMuB,MAAAA,gBAAgB,GAAGH,QAAzB,CAAA;EACA,EAAA,MAAMI,aAAa,GAAGD,gBAAgB,CAACC,aAAvC,CAAA;EACA,EAAM,MAAA;EACJC,IAAAA,gBADI;EAEJC,IAAAA,mBAFI;EAGJC,IAAAA,IAHI;EAIJC,IAAAA,OAJI;EAKJC,IAAAA,UALI;EAMJC,IAAAA,YAAY,GAAG9B,MAAM,CAAC8B,YAAP,IAAuB9B,MAAM,CAAC+B,eANzC;EAOJC,IAAAA,eAPI;EAQJC,IAAAA,SARI;EASJ/B,IAAAA,YAAAA;EATI,GAAA,GAUFF,MAVJ,CAAA;EAYA,EAAA,MAAMkC,gBAAgB,GAAGN,OAAO,CAAC9F,SAAjC,CAAA;EAEA,EAAA,MAAMqG,SAAS,GAAG5D,YAAY,CAAC2D,gBAAD,EAAmB,WAAnB,CAA9B,CAAA;EACA,EAAA,MAAME,cAAc,GAAG7D,YAAY,CAAC2D,gBAAD,EAAmB,aAAnB,CAAnC,CAAA;EACA,EAAA,MAAMG,aAAa,GAAG9D,YAAY,CAAC2D,gBAAD,EAAmB,YAAnB,CAAlC,CAAA;EACA,EAAMI,MAAAA,aAAa,GAAG/D,YAAY,CAAC2D,gBAAD,EAAmB,YAAnB,CAAlC,CA5C6C;EA+C7C;EACA;EACA;EACA;EACA;;EACA,EAAA,IAAI,OAAOR,mBAAP,KAA+B,UAAnC,EAA+C;EAC7C,IAAA,MAAMa,QAAQ,GAAGnB,QAAQ,CAACoB,aAAT,CAAuB,UAAvB,CAAjB,CAAA;;EACA,IAAID,IAAAA,QAAQ,CAACE,OAAT,IAAoBF,QAAQ,CAACE,OAAT,CAAiBC,aAAzC,EAAwD;EACtDtB,MAAAA,QAAQ,GAAGmB,QAAQ,CAACE,OAAT,CAAiBC,aAA5B,CAAA;EACD,KAAA;EACF,GAAA;;EAED,EAAA,IAAIC,kBAAJ,CAAA;EACA,EAAIC,IAAAA,SAAS,GAAG,EAAhB,CAAA;EAEA,EAAM,MAAA;EACJC,IAAAA,cADI;EAEJC,IAAAA,kBAFI;EAGJC,IAAAA,sBAHI;EAIJC,IAAAA,oBAAAA;EAJI,GAAA,GAKF5B,QALJ,CAAA;EAMA,EAAM,MAAA;EAAE6B,IAAAA,UAAAA;EAAF,GAAA,GAAiB1B,gBAAvB,CAAA;EAEA,EAAI2B,IAAAA,KAAK,GAAG,EAAZ,CAAA;EAEA;EACF;EACA;;EACEnC,EAAAA,SAAS,CAACO,WAAV,GACE,OAAO5G,OAAP,KAAmB,UAAnB,IACA,OAAO4H,aAAP,KAAyB,UADzB,IAEAO,cAFA,IAGAA,cAAc,CAACM,kBAAf,KAAsC7E,SAJxC,CAAA;EAMA,EAAM,MAAA;EACJgB,IAAAA,aADI;EAEJC,IAAAA,QAFI;EAGJC,IAAAA,WAHI;EAIJC,IAAAA,SAJI;EAKJC,IAAAA,SALI;EAMJE,IAAAA,iBANI;EAOJC,IAAAA,eAAAA;EAPI,GAAA,GAQFuD,WARJ,CAAA;EAUA,EAAI,IAAA;EAAEzD,oBAAAA,gBAAAA;EAAF,GAAA,GAAqByD,WAAzB,CAAA;EAEA;EACF;EACA;EACA;;EAEE;;EACA,EAAIC,IAAAA,YAAY,GAAG,IAAnB,CAAA;EACA,EAAA,MAAMC,oBAAoB,GAAG7F,QAAQ,CAAC,EAAD,EAAK,CACxC,GAAG8F,MADqC,EAExC,GAAGA,KAFqC,EAGxC,GAAGA,UAHqC,EAIxC,GAAGA,QAJqC,EAKxC,GAAGA,IALqC,CAAL,CAArC,CAAA;EAQA;;EACA,EAAIC,IAAAA,YAAY,GAAG,IAAnB,CAAA;EACA,EAAMC,MAAAA,oBAAoB,GAAGhG,QAAQ,CAAC,EAAD,EAAK,CACxC,GAAGiG,IADqC,EAExC,GAAGA,GAFqC,EAGxC,GAAGA,MAHqC,EAIxC,GAAGA,GAJqC,CAAL,CAArC,CAAA;EAOA;EACF;EACA;EACA;EACA;EACA;;EACE,EAAIC,IAAAA,uBAAuB,GAAG5I,MAAM,CAACE,IAAP,CAC5BC,MAAM,CAAC,IAAD,EAAO;EACX0I,IAAAA,YAAY,EAAE;EACZC,MAAAA,QAAQ,EAAE,IADE;EAEZC,MAAAA,YAAY,EAAE,KAFF;EAGZC,MAAAA,UAAU,EAAE,IAHA;EAIZ1F,MAAAA,KAAK,EAAE,IAAA;EAJK,KADH;EAOX2F,IAAAA,kBAAkB,EAAE;EAClBH,MAAAA,QAAQ,EAAE,IADQ;EAElBC,MAAAA,YAAY,EAAE,KAFI;EAGlBC,MAAAA,UAAU,EAAE,IAHM;EAIlB1F,MAAAA,KAAK,EAAE,IAAA;EAJW,KAPT;EAaX4F,IAAAA,8BAA8B,EAAE;EAC9BJ,MAAAA,QAAQ,EAAE,IADoB;EAE9BC,MAAAA,YAAY,EAAE,KAFgB;EAG9BC,MAAAA,UAAU,EAAE,IAHkB;EAI9B1F,MAAAA,KAAK,EAAE,KAAA;EAJuB,KAAA;EAbrB,GAAP,CADsB,CAA9B,CAAA;EAuBA;;EACA,EAAI6F,IAAAA,WAAW,GAAG,IAAlB,CAAA;EAEA;;EACA,EAAIC,IAAAA,WAAW,GAAG,IAAlB,CAAA;EAEA;;EACA,EAAIC,IAAAA,eAAe,GAAG,IAAtB,CAAA;EAEA;;EACA,EAAIC,IAAAA,eAAe,GAAG,IAAtB,CAAA;EAEA;;EACA,EAAIC,IAAAA,uBAAuB,GAAG,KAA9B,CAAA;EAEA;EACF;;EACE,EAAIC,IAAAA,wBAAwB,GAAG,IAA/B,CAAA;EAEA;EACF;EACA;;EACE,EAAIC,IAAAA,kBAAkB,GAAG,KAAzB,CAAA;EAEA;;EACA,EAAIC,IAAAA,cAAc,GAAG,KAArB,CAAA;EAEA;;EACA,EAAIC,IAAAA,UAAU,GAAG,KAAjB,CAAA;EAEA;EACF;;EACE,EAAIC,IAAAA,UAAU,GAAG,KAAjB,CAAA;EAEA;EACF;EACA;EACA;;EACE,EAAIC,IAAAA,UAAU,GAAG,KAAjB,CAAA;EAEA;EACF;;EACE,EAAIC,IAAAA,mBAAmB,GAAG,KAA1B,CAAA;EAEA;EACF;;EACE,EAAIC,IAAAA,mBAAmB,GAAG,KAA1B,CAAA;EAEA;EACF;EACA;;EACE,EAAIC,IAAAA,YAAY,GAAG,IAAnB,CAAA;EAEA;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACE,EAAIC,IAAAA,oBAAoB,GAAG,KAA3B,CAAA;EACA,EAAMC,MAAAA,2BAA2B,GAAG,eAApC,CAAA;EAEA;;EACA,EAAIC,IAAAA,YAAY,GAAG,IAAnB,CAAA;EAEA;EACF;;EACE,EAAIC,IAAAA,QAAQ,GAAG,KAAf,CAAA;EAEA;;EACA,EAAIC,IAAAA,YAAY,GAAG,EAAnB,CAAA;EAEA;;EACA,EAAIC,IAAAA,eAAe,GAAG,IAAtB,CAAA;EACA,EAAMC,MAAAA,uBAAuB,GAAG7H,QAAQ,CAAC,EAAD,EAAK,CAC3C,gBAD2C,EAE3C,OAF2C,EAG3C,UAH2C,EAI3C,MAJ2C,EAK3C,eAL2C,EAM3C,MAN2C,EAO3C,QAP2C,EAQ3C,MAR2C,EAS3C,IAT2C,EAU3C,IAV2C,EAW3C,IAX2C,EAY3C,IAZ2C,EAa3C,OAb2C,EAc3C,SAd2C,EAe3C,UAf2C,EAgB3C,UAhB2C,EAiB3C,WAjB2C,EAkB3C,QAlB2C,EAmB3C,OAnB2C,EAoB3C,KApB2C,EAqB3C,UArB2C,EAsB3C,OAtB2C,EAuB3C,OAvB2C,EAwB3C,OAxB2C,EAyB3C,KAzB2C,CAAL,CAAxC,CAAA;EA4BA;;EACA,EAAI8H,IAAAA,aAAa,GAAG,IAApB,CAAA;EACA,EAAA,MAAMC,qBAAqB,GAAG/H,QAAQ,CAAC,EAAD,EAAK,CACzC,OADyC,EAEzC,OAFyC,EAGzC,KAHyC,EAIzC,QAJyC,EAKzC,OALyC,EAMzC,OANyC,CAAL,CAAtC,CAAA;EASA;;EACA,EAAIgI,IAAAA,mBAAmB,GAAG,IAA1B,CAAA;EACA,EAAA,MAAMC,2BAA2B,GAAGjI,QAAQ,CAAC,EAAD,EAAK,CAC/C,KAD+C,EAE/C,OAF+C,EAG/C,KAH+C,EAI/C,IAJ+C,EAK/C,OAL+C,EAM/C,MAN+C,EAO/C,SAP+C,EAQ/C,aAR+C,EAS/C,MAT+C,EAU/C,SAV+C,EAW/C,OAX+C,EAY/C,OAZ+C,EAa/C,OAb+C,EAc/C,OAd+C,CAAL,CAA5C,CAAA;EAiBA,EAAMkI,MAAAA,gBAAgB,GAAG,oCAAzB,CAAA;EACA,EAAMC,MAAAA,aAAa,GAAG,4BAAtB,CAAA;EACA,EAAMC,MAAAA,cAAc,GAAG,8BAAvB,CAAA;EACA;;EACA,EAAIC,IAAAA,SAAS,GAAGD,cAAhB,CAAA;EACA,EAAIE,IAAAA,cAAc,GAAG,KAArB,CAAA;EAEA;;EACA,EAAIC,IAAAA,kBAAkB,GAAG,IAAzB,CAAA;EACA,EAAA,MAAMC,0BAA0B,GAAGxI,QAAQ,CACzC,EADyC,EAEzC,CAACkI,gBAAD,EAAmBC,aAAnB,EAAkCC,cAAlC,CAFyC,EAGzCtJ,cAHyC,CAA3C,CAAA;EAMA;;EACA,EAAI2J,IAAAA,iBAAiB,GAAG,IAAxB,CAAA;EACA,EAAA,MAAMC,4BAA4B,GAAG,CAAC,uBAAD,EAA0B,WAA1B,CAArC,CAAA;EACA,EAAMC,MAAAA,yBAAyB,GAAG,WAAlC,CAAA;EACA,EAAIxI,IAAAA,iBAAiB,GAAG,IAAxB,CAAA;EAEA;;EACA,EAAIyI,IAAAA,MAAM,GAAG,IAAb,CAAA;EAEA;;EACA;;EAEA,EAAA,MAAMC,WAAW,GAAGlF,QAAQ,CAACoB,aAAT,CAAuB,MAAvB,CAApB,CAAA;;EAEA,EAAA,MAAM+D,iBAAiB,GAAG,SAApBA,iBAAoB,CAAUC,SAAV,EAAqB;EAC7C,IAAA,OAAOA,SAAS,YAAYtJ,MAArB,IAA+BsJ,SAAS,YAAYC,QAA3D,CAAA;EACD,GAFD,CAAA;EAIA;EACF;EACA;EACA;EACA;EACE;;;EACA,EAAA,MAAMC,YAAY,GAAG,SAAfA,YAAe,GAAoB;EAAA,IAAVC,IAAAA,GAAU,uEAAJ,EAAI,CAAA;;EACvC,IAAA,IAAIN,MAAM,IAAIA,MAAM,KAAKM,GAAzB,EAA8B;EAC5B,MAAA,OAAA;EACD,KAAA;EAED;;;EACA,IAAA,IAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;EACnCA,MAAAA,GAAG,GAAG,EAAN,CAAA;EACD,KAAA;EAED;;;EACAA,IAAAA,GAAG,GAAG1I,KAAK,CAAC0I,GAAD,CAAX,CAAA;EAEAT,IAAAA,iBAAiB;EAEfC,IAAAA,4BAA4B,CAACrJ,OAA7B,CAAqC6J,GAAG,CAACT,iBAAzC,MAAgE,CAAC,CAAjE,GACKA,iBAAiB,GAAGE,yBADzB,GAEKF,iBAAiB,GAAGS,GAAG,CAACT,iBAJ/B,CAbuC;;EAoBvCtI,IAAAA,iBAAiB,GACfsI,iBAAiB,KAAK,uBAAtB,GACI3J,cADJ,GAEIH,iBAHN,CAAA;EAKA;;EACAiH,IAAAA,YAAY,GACV,cAAA,IAAkBsD,GAAlB,GACIlJ,QAAQ,CAAC,EAAD,EAAKkJ,GAAG,CAACtD,YAAT,EAAuBzF,iBAAvB,CADZ,GAEI0F,oBAHN,CAAA;EAIAE,IAAAA,YAAY,GACV,cAAA,IAAkBmD,GAAlB,GACIlJ,QAAQ,CAAC,EAAD,EAAKkJ,GAAG,CAACnD,YAAT,EAAuB5F,iBAAvB,CADZ,GAEI6F,oBAHN,CAAA;EAIAuC,IAAAA,kBAAkB,GAChB,oBAAA,IAAwBW,GAAxB,GACIlJ,QAAQ,CAAC,EAAD,EAAKkJ,GAAG,CAACX,kBAAT,EAA6BzJ,cAA7B,CADZ,GAEI0J,0BAHN,CAAA;EAIAR,IAAAA,mBAAmB,GACjB,mBAAA,IAAuBkB,GAAvB,GACIlJ,QAAQ,CACNQ,KAAK,CAACyH,2BAAD,CADC;EAENiB,IAAAA,GAAG,CAACC,iBAFE;EAGNhJ,IAAAA,iBAHM;EAAA,KADZ;EAAA,MAMI8H,2BAPN,CAAA;EAQAH,IAAAA,aAAa,GACX,mBAAA,IAAuBoB,GAAvB,GACIlJ,QAAQ,CACNQ,KAAK,CAACuH,qBAAD,CADC;EAENmB,IAAAA,GAAG,CAACE,iBAFE;EAGNjJ,IAAAA,iBAHM;EAAA,KADZ;EAAA,MAMI4H,qBAPN,CAAA;EAQAH,IAAAA,eAAe,GACb,iBAAA,IAAqBsB,GAArB,GACIlJ,QAAQ,CAAC,EAAD,EAAKkJ,GAAG,CAACtB,eAAT,EAA0BzH,iBAA1B,CADZ,GAEI0H,uBAHN,CAAA;EAIApB,IAAAA,WAAW,GACT,aAAA,IAAiByC,GAAjB,GACIlJ,QAAQ,CAAC,EAAD,EAAKkJ,GAAG,CAACzC,WAAT,EAAsBtG,iBAAtB,CADZ,GAEI,EAHN,CAAA;EAIAuG,IAAAA,WAAW,GACT,aAAA,IAAiBwC,GAAjB,GACIlJ,QAAQ,CAAC,EAAD,EAAKkJ,GAAG,CAACxC,WAAT,EAAsBvG,iBAAtB,CADZ,GAEI,EAHN,CAAA;EAIAwH,IAAAA,YAAY,GAAG,cAAkBuB,IAAAA,GAAlB,GAAwBA,GAAG,CAACvB,YAA5B,GAA2C,KAA1D,CAAA;EACAhB,IAAAA,eAAe,GAAGuC,GAAG,CAACvC,eAAJ,KAAwB,KAA1C,CAnEuC;;EAoEvCC,IAAAA,eAAe,GAAGsC,GAAG,CAACtC,eAAJ,KAAwB,KAA1C,CApEuC;;EAqEvCC,IAAAA,uBAAuB,GAAGqC,GAAG,CAACrC,uBAAJ,IAA+B,KAAzD,CArEuC;;EAsEvCC,IAAAA,wBAAwB,GAAGoC,GAAG,CAACpC,wBAAJ,KAAiC,KAA5D,CAtEuC;;EAuEvCC,IAAAA,kBAAkB,GAAGmC,GAAG,CAACnC,kBAAJ,IAA0B,KAA/C,CAvEuC;;EAwEvCC,IAAAA,cAAc,GAAGkC,GAAG,CAAClC,cAAJ,IAAsB,KAAvC,CAxEuC;;EAyEvCG,IAAAA,UAAU,GAAG+B,GAAG,CAAC/B,UAAJ,IAAkB,KAA/B,CAzEuC;;EA0EvCC,IAAAA,mBAAmB,GAAG8B,GAAG,CAAC9B,mBAAJ,IAA2B,KAAjD,CA1EuC;;EA2EvCC,IAAAA,mBAAmB,GAAG6B,GAAG,CAAC7B,mBAAJ,IAA2B,KAAjD,CA3EuC;;EA4EvCH,IAAAA,UAAU,GAAGgC,GAAG,CAAChC,UAAJ,IAAkB,KAA/B,CA5EuC;;EA6EvCI,IAAAA,YAAY,GAAG4B,GAAG,CAAC5B,YAAJ,KAAqB,KAApC,CA7EuC;;EA8EvCC,IAAAA,oBAAoB,GAAG2B,GAAG,CAAC3B,oBAAJ,IAA4B,KAAnD,CA9EuC;;EA+EvCE,IAAAA,YAAY,GAAGyB,GAAG,CAACzB,YAAJ,KAAqB,KAApC,CA/EuC;;EAgFvCC,IAAAA,QAAQ,GAAGwB,GAAG,CAACxB,QAAJ,IAAgB,KAA3B,CAhFuC;;EAiFvCxF,IAAAA,gBAAc,GAAGgH,GAAG,CAACG,kBAAJ,IAA0B1D,cAA3C,CAAA;EACA0C,IAAAA,SAAS,GAAGa,GAAG,CAACb,SAAJ,IAAiBD,cAA7B,CAAA;EACAlC,IAAAA,uBAAuB,GAAGgD,GAAG,CAAChD,uBAAJ,IAA+B,EAAzD,CAAA;;EACA,IAAA,IACEgD,GAAG,CAAChD,uBAAJ,IACA4C,iBAAiB,CAACI,GAAG,CAAChD,uBAAJ,CAA4BC,YAA7B,CAFnB,EAGE;EACAD,MAAAA,uBAAuB,CAACC,YAAxB,GACE+C,GAAG,CAAChD,uBAAJ,CAA4BC,YAD9B,CAAA;EAED,KAAA;;EAED,IAAA,IACE+C,GAAG,CAAChD,uBAAJ,IACA4C,iBAAiB,CAACI,GAAG,CAAChD,uBAAJ,CAA4BK,kBAA7B,CAFnB,EAGE;EACAL,MAAAA,uBAAuB,CAACK,kBAAxB,GACE2C,GAAG,CAAChD,uBAAJ,CAA4BK,kBAD9B,CAAA;EAED,KAAA;;EAED,IAAA,IACE2C,GAAG,CAAChD,uBAAJ,IACA,OAAOgD,GAAG,CAAChD,uBAAJ,CAA4BM,8BAAnC,KACE,SAHJ,EAIE;EACAN,MAAAA,uBAAuB,CAACM,8BAAxB,GACE0C,GAAG,CAAChD,uBAAJ,CAA4BM,8BAD9B,CAAA;EAED,KAAA;;EAED,IAAA,IAAIO,kBAAJ,EAAwB;EACtBH,MAAAA,eAAe,GAAG,KAAlB,CAAA;EACD,KAAA;;EAED,IAAA,IAAIQ,mBAAJ,EAAyB;EACvBD,MAAAA,UAAU,GAAG,IAAb,CAAA;EACD,KAAA;EAED;;;EACA,IAAA,IAAIQ,YAAJ,EAAkB;EAChB/B,MAAAA,YAAY,GAAG5F,QAAQ,CAAC,EAAD,EAAK,CAAC,GAAG8F,IAAJ,CAAL,CAAvB,CAAA;EACAC,MAAAA,YAAY,GAAG,EAAf,CAAA;;EACA,MAAA,IAAI4B,YAAY,CAACtG,IAAb,KAAsB,IAA1B,EAAgC;EAC9BrB,QAAAA,QAAQ,CAAC4F,YAAD,EAAeE,MAAf,CAAR,CAAA;EACA9F,QAAAA,QAAQ,CAAC+F,YAAD,EAAeE,IAAf,CAAR,CAAA;EACD,OAAA;;EAED,MAAA,IAAI0B,YAAY,CAACrG,GAAb,KAAqB,IAAzB,EAA+B;EAC7BtB,QAAAA,QAAQ,CAAC4F,YAAD,EAAeE,KAAf,CAAR,CAAA;EACA9F,QAAAA,QAAQ,CAAC+F,YAAD,EAAeE,GAAf,CAAR,CAAA;EACAjG,QAAAA,QAAQ,CAAC+F,YAAD,EAAeE,GAAf,CAAR,CAAA;EACD,OAAA;;EAED,MAAA,IAAI0B,YAAY,CAACpG,UAAb,KAA4B,IAAhC,EAAsC;EACpCvB,QAAAA,QAAQ,CAAC4F,YAAD,EAAeE,UAAf,CAAR,CAAA;EACA9F,QAAAA,QAAQ,CAAC+F,YAAD,EAAeE,GAAf,CAAR,CAAA;EACAjG,QAAAA,QAAQ,CAAC+F,YAAD,EAAeE,GAAf,CAAR,CAAA;EACD,OAAA;;EAED,MAAA,IAAI0B,YAAY,CAAClG,MAAb,KAAwB,IAA5B,EAAkC;EAChCzB,QAAAA,QAAQ,CAAC4F,YAAD,EAAeE,QAAf,CAAR,CAAA;EACA9F,QAAAA,QAAQ,CAAC+F,YAAD,EAAeE,MAAf,CAAR,CAAA;EACAjG,QAAAA,QAAQ,CAAC+F,YAAD,EAAeE,GAAf,CAAR,CAAA;EACD,OAAA;EACF,KAAA;EAED;;;EACA,IAAIiD,IAAAA,GAAG,CAACI,QAAR,EAAkB;EAChB,MAAI1D,IAAAA,YAAY,KAAKC,oBAArB,EAA2C;EACzCD,QAAAA,YAAY,GAAGpF,KAAK,CAACoF,YAAD,CAApB,CAAA;EACD,OAAA;;EAED5F,MAAAA,QAAQ,CAAC4F,YAAD,EAAesD,GAAG,CAACI,QAAnB,EAA6BnJ,iBAA7B,CAAR,CAAA;EACD,KAAA;;EAED,IAAI+I,IAAAA,GAAG,CAACK,QAAR,EAAkB;EAChB,MAAIxD,IAAAA,YAAY,KAAKC,oBAArB,EAA2C;EACzCD,QAAAA,YAAY,GAAGvF,KAAK,CAACuF,YAAD,CAApB,CAAA;EACD,OAAA;;EAED/F,MAAAA,QAAQ,CAAC+F,YAAD,EAAemD,GAAG,CAACK,QAAnB,EAA6BpJ,iBAA7B,CAAR,CAAA;EACD,KAAA;;EAED,IAAI+I,IAAAA,GAAG,CAACC,iBAAR,EAA2B;EACzBnJ,MAAAA,QAAQ,CAACgI,mBAAD,EAAsBkB,GAAG,CAACC,iBAA1B,EAA6ChJ,iBAA7C,CAAR,CAAA;EACD,KAAA;;EAED,IAAI+I,IAAAA,GAAG,CAACtB,eAAR,EAAyB;EACvB,MAAIA,IAAAA,eAAe,KAAKC,uBAAxB,EAAiD;EAC/CD,QAAAA,eAAe,GAAGpH,KAAK,CAACoH,eAAD,CAAvB,CAAA;EACD,OAAA;;EAED5H,MAAAA,QAAQ,CAAC4H,eAAD,EAAkBsB,GAAG,CAACtB,eAAtB,EAAuCzH,iBAAvC,CAAR,CAAA;EACD,KAAA;EAED;;;EACA,IAAA,IAAIsH,YAAJ,EAAkB;EAChB7B,MAAAA,YAAY,CAAC,OAAD,CAAZ,GAAwB,IAAxB,CAAA;EACD,KAAA;EAED;;;EACA,IAAA,IAAIoB,cAAJ,EAAoB;EAClBhH,MAAAA,QAAQ,CAAC4F,YAAD,EAAe,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,CAAf,CAAR,CAAA;EACD,KAAA;EAED;;;EACA,IAAIA,IAAAA,YAAY,CAAC4D,KAAjB,EAAwB;EACtBxJ,MAAAA,QAAQ,CAAC4F,YAAD,EAAe,CAAC,OAAD,CAAf,CAAR,CAAA;EACA,MAAOa,OAAAA,WAAW,CAACgD,KAAnB,CAAA;EACD,KAAA;;EAED,IAAIP,IAAAA,GAAG,CAACQ,oBAAR,EAA8B;EAC5B,MAAI,IAAA,OAAOR,GAAG,CAACQ,oBAAJ,CAAyBzG,UAAhC,KAA+C,UAAnD,EAA+D;EAC7D,QAAMtD,MAAAA,eAAe,CACnB,6EADmB,CAArB,CAAA;EAGD,OAAA;;EAED,MAAI,IAAA,OAAOuJ,GAAG,CAACQ,oBAAJ,CAAyBxG,eAAhC,KAAoD,UAAxD,EAAoE;EAClE,QAAMvD,MAAAA,eAAe,CACnB,kFADmB,CAArB,CAAA;EAGD,OAX2B;;;EAc5BuF,MAAAA,kBAAkB,GAAGgE,GAAG,CAACQ,oBAAzB,CAd4B;;EAiB5BvE,MAAAA,SAAS,GAAGD,kBAAkB,CAACjC,UAAnB,CAA8B,EAA9B,CAAZ,CAAA;EACD,KAlBD,MAkBO;EACL;EACA,MAAIiC,IAAAA,kBAAkB,KAAKrE,SAA3B,EAAsC;EACpCqE,QAAAA,kBAAkB,GAAG1C,yBAAyB,CAC5CC,YAD4C,EAE5CsB,aAF4C,CAA9C,CAAA;EAID,OAPI;;;EAUL,MAAImB,IAAAA,kBAAkB,KAAK,IAAvB,IAA+B,OAAOC,SAAP,KAAqB,QAAxD,EAAkE;EAChEA,QAAAA,SAAS,GAAGD,kBAAkB,CAACjC,UAAnB,CAA8B,EAA9B,CAAZ,CAAA;EACD,OAAA;EACF,KA7NsC;EAgOvC;;;EACA,IAAA,IAAI1F,MAAJ,EAAY;EACVA,MAAAA,MAAM,CAAC2L,GAAD,CAAN,CAAA;EACD,KAAA;;EAEDN,IAAAA,MAAM,GAAGM,GAAT,CAAA;EACD,GAtOD,CAAA;;EAwOA,EAAA,MAAMS,8BAA8B,GAAG3J,QAAQ,CAAC,EAAD,EAAK,CAClD,IADkD,EAElD,IAFkD,EAGlD,IAHkD,EAIlD,IAJkD,EAKlD,OALkD,CAAL,CAA/C,CAAA;EAQA,EAAA,MAAM4J,uBAAuB,GAAG5J,QAAQ,CAAC,EAAD,EAAK,CAC3C,eAD2C,EAE3C,MAF2C,EAG3C,OAH2C,EAI3C,gBAJ2C,CAAL,CAAxC,CApjB6C;EA4jB7C;EACA;EACA;;EACA,EAAA,MAAM6J,4BAA4B,GAAG7J,QAAQ,CAAC,EAAD,EAAK,CAChD,OADgD,EAEhD,OAFgD,EAGhD,MAHgD,EAIhD,GAJgD,EAKhD,QALgD,CAAL,CAA7C,CAAA;EAQA;EACF;EACA;;EACE,EAAM8J,MAAAA,YAAY,GAAG9J,QAAQ,CAAC,EAAD,EAAK8F,KAAL,CAA7B,CAAA;EACA9F,EAAAA,QAAQ,CAAC8J,YAAD,EAAehE,UAAf,CAAR,CAAA;EACA9F,EAAAA,QAAQ,CAAC8J,YAAD,EAAehE,aAAf,CAAR,CAAA;EAEA,EAAMiE,MAAAA,eAAe,GAAG/J,QAAQ,CAAC,EAAD,EAAK8F,QAAL,CAAhC,CAAA;EACA9F,EAAAA,QAAQ,CAAC+J,eAAD,EAAkBjE,gBAAlB,CAAR,CAAA;EAEA;EACF;EACA;EACA;EACA;EACA;;EACE,EAAA,MAAMkE,oBAAoB,GAAG,SAAvBA,oBAAuB,CAAU1J,OAAV,EAAmB;EAC9C,IAAA,IAAI2J,MAAM,GAAGpF,aAAa,CAACvE,OAAD,CAA1B,CAD8C;EAI9C;;EACA,IAAA,IAAI,CAAC2J,MAAD,IAAW,CAACA,MAAM,CAACC,OAAvB,EAAgC;EAC9BD,MAAAA,MAAM,GAAG;EACPE,QAAAA,YAAY,EAAE9B,SADP;EAEP6B,QAAAA,OAAO,EAAE,UAAA;EAFF,OAAT,CAAA;EAID,KAAA;;EAED,IAAA,MAAMA,OAAO,GAAGvL,iBAAiB,CAAC2B,OAAO,CAAC4J,OAAT,CAAjC,CAAA;EACA,IAAA,MAAME,aAAa,GAAGzL,iBAAiB,CAACsL,MAAM,CAACC,OAAR,CAAvC,CAAA;;EAEA,IAAA,IAAI,CAAC3B,kBAAkB,CAACjI,OAAO,CAAC6J,YAAT,CAAvB,EAA+C;EAC7C,MAAA,OAAO,KAAP,CAAA;EACD,KAAA;;EAED,IAAA,IAAI7J,OAAO,CAAC6J,YAAR,KAAyBhC,aAA7B,EAA4C;EAC1C;EACA;EACA;EACA,MAAA,IAAI8B,MAAM,CAACE,YAAP,KAAwB/B,cAA5B,EAA4C;EAC1C,QAAO8B,OAAAA,OAAO,KAAK,KAAnB,CAAA;EACD,OANyC;EAS1C;EACA;;;EACA,MAAA,IAAID,MAAM,CAACE,YAAP,KAAwBjC,gBAA5B,EAA8C;EAC5C,QAAA,OACEgC,OAAO,KAAK,KAAZ,KACCE,aAAa,KAAK,gBAAlB,IACCT,8BAA8B,CAACS,aAAD,CAFhC,CADF,CAAA;EAKD,OAjByC;EAoB1C;;;EACA,MAAA,OAAOC,OAAO,CAACP,YAAY,CAACI,OAAD,CAAb,CAAd,CAAA;EACD,KAAA;;EAED,IAAA,IAAI5J,OAAO,CAAC6J,YAAR,KAAyBjC,gBAA7B,EAA+C;EAC7C;EACA;EACA;EACA,MAAA,IAAI+B,MAAM,CAACE,YAAP,KAAwB/B,cAA5B,EAA4C;EAC1C,QAAO8B,OAAAA,OAAO,KAAK,MAAnB,CAAA;EACD,OAN4C;EAS7C;;;EACA,MAAA,IAAID,MAAM,CAACE,YAAP,KAAwBhC,aAA5B,EAA2C;EACzC,QAAA,OAAO+B,OAAO,KAAK,MAAZ,IAAsBN,uBAAuB,CAACQ,aAAD,CAApD,CAAA;EACD,OAZ4C;EAe7C;;;EACA,MAAA,OAAOC,OAAO,CAACN,eAAe,CAACG,OAAD,CAAhB,CAAd,CAAA;EACD,KAAA;;EAED,IAAA,IAAI5J,OAAO,CAAC6J,YAAR,KAAyB/B,cAA7B,EAA6C;EAC3C;EACA;EACA;EACA,MACE6B,IAAAA,MAAM,CAACE,YAAP,KAAwBhC,aAAxB,IACA,CAACyB,uBAAuB,CAACQ,aAAD,CAF1B,EAGE;EACA,QAAA,OAAO,KAAP,CAAA;EACD,OAAA;;EAED,MACEH,IAAAA,MAAM,CAACE,YAAP,KAAwBjC,gBAAxB,IACA,CAACyB,8BAA8B,CAACS,aAAD,CAFjC,EAGE;EACA,QAAA,OAAO,KAAP,CAAA;EACD,OAhB0C;EAmB3C;;;EACA,MAAA,OACE,CAACL,eAAe,CAACG,OAAD,CAAhB,KACCL,4BAA4B,CAACK,OAAD,CAA5B,IAAyC,CAACJ,YAAY,CAACI,OAAD,CADvD,CADF,CAAA;EAID,KAtF6C;;;EAyF9C,IACEzB,IAAAA,iBAAiB,KAAK,uBAAtB,IACAF,kBAAkB,CAACjI,OAAO,CAAC6J,YAAT,CAFpB,EAGE;EACA,MAAA,OAAO,IAAP,CAAA;EACD,KA9F6C;EAiG9C;EACA;EACA;;;EACA,IAAA,OAAO,KAAP,CAAA;EACD,GArGD,CAAA;EAuGA;EACF;EACA;EACA;EACA;;;EACE,EAAA,MAAMG,YAAY,GAAG,SAAfA,YAAe,CAAUC,IAAV,EAAgB;EACnC9L,IAAAA,SAAS,CAAC6E,SAAS,CAACI,OAAX,EAAoB;EAAEpD,MAAAA,OAAO,EAAEiK,IAAAA;EAAX,KAApB,CAAT,CAAA;;EACA,IAAI,IAAA;EACF;EACAA,MAAAA,IAAI,CAACC,UAAL,CAAgBC,WAAhB,CAA4BF,IAA5B,CAAA,CAAA;EACD,KAHD,CAGE,OAAOnH,CAAP,EAAU;EACVmH,MAAAA,IAAI,CAACG,MAAL,EAAA,CAAA;EACD,KAAA;EACF,GARD,CAAA;EAUA;EACF;EACA;EACA;EACA;EACA;;;EACE,EAAMC,MAAAA,gBAAgB,GAAG,SAAnBA,gBAAmB,CAAUC,IAAV,EAAgBL,IAAhB,EAAsB;EAC7C,IAAI,IAAA;EACF9L,MAAAA,SAAS,CAAC6E,SAAS,CAACI,OAAX,EAAoB;EAC3BmH,QAAAA,SAAS,EAAEN,IAAI,CAACO,gBAAL,CAAsBF,IAAtB,CADgB;EAE3BG,QAAAA,IAAI,EAAER,IAAAA;EAFqB,OAApB,CAAT,CAAA;EAID,KALD,CAKE,OAAOnH,CAAP,EAAU;EACV3E,MAAAA,SAAS,CAAC6E,SAAS,CAACI,OAAX,EAAoB;EAC3BmH,QAAAA,SAAS,EAAE,IADgB;EAE3BE,QAAAA,IAAI,EAAER,IAAAA;EAFqB,OAApB,CAAT,CAAA;EAID,KAAA;;EAEDA,IAAAA,IAAI,CAACS,eAAL,CAAqBJ,IAArB,EAb6C;;EAgB7C,IAAIA,IAAAA,IAAI,KAAK,IAAT,IAAiB,CAAC7E,YAAY,CAAC6E,IAAD,CAAlC,EAA0C;EACxC,MAAIzD,IAAAA,UAAU,IAAIC,mBAAlB,EAAuC;EACrC,QAAI,IAAA;EACFkD,UAAAA,YAAY,CAACC,IAAD,CAAZ,CAAA;EACD,SAFD,CAEE,OAAOnH,CAAP,EAAU,EAAE;EACf,OAJD,MAIO;EACL,QAAI,IAAA;EACFmH,UAAAA,IAAI,CAACU,YAAL,CAAkBL,IAAlB,EAAwB,EAAxB,CAAA,CAAA;EACD,SAFD,CAEE,OAAOxH,CAAP,EAAU,EAAE;EACf,OAAA;EACF,KAAA;EACF,GA3BD,CAAA;EA6BA;EACF;EACA;EACA;EACA;EACA;;;EACE,EAAA,MAAM8H,aAAa,GAAG,SAAhBA,aAAgB,CAAUC,KAAV,EAAiB;EACrC;EACA,IAAIC,IAAAA,GAAG,GAAG,IAAV,CAAA;EACA,IAAIC,IAAAA,iBAAiB,GAAG,IAAxB,CAAA;;EAEA,IAAA,IAAInE,UAAJ,EAAgB;EACdiE,MAAAA,KAAK,GAAG,mBAAA,GAAsBA,KAA9B,CAAA;EACD,KAFD,MAEO;EACL;EACA,MAAA,MAAMG,OAAO,GAAGtM,WAAW,CAACmM,KAAD,EAAQ,aAAR,CAA3B,CAAA;EACAE,MAAAA,iBAAiB,GAAGC,OAAO,IAAIA,OAAO,CAAC,CAAD,CAAtC,CAAA;EACD,KAAA;;EAED,IAAA,IACE7C,iBAAiB,KAAK,uBAAtB,IACAJ,SAAS,KAAKD,cAFhB,EAGE;EACA;EACA+C,MAAAA,KAAK,GACH,gEACAA,GAAAA,KADA,GAEA,gBAHF,CAAA;EAID,KAAA;;EAED,IAAMI,MAAAA,YAAY,GAAGrG,kBAAkB,GACnCA,kBAAkB,CAACjC,UAAnB,CAA8BkI,KAA9B,CADmC,GAEnCA,KAFJ,CAAA;EAGA;EACJ;EACA;EACA;;EACI,IAAI9C,IAAAA,SAAS,KAAKD,cAAlB,EAAkC;EAChC,MAAI,IAAA;EACFgD,QAAAA,GAAG,GAAG,IAAI5G,SAAJ,EAAA,CAAgBgH,eAAhB,CAAgCD,YAAhC,EAA8C9C,iBAA9C,CAAN,CAAA;EACD,OAFD,CAEE,OAAOrF,CAAP,EAAU,EAAE;EACf,KAAA;EAED;;;EACA,IAAA,IAAI,CAACgI,GAAD,IAAQ,CAACA,GAAG,CAACK,eAAjB,EAAkC;EAChCL,MAAAA,GAAG,GAAGhG,cAAc,CAACsG,cAAf,CAA8BrD,SAA9B,EAAyC,UAAzC,EAAqD,IAArD,CAAN,CAAA;;EACA,MAAI,IAAA;EACF+C,QAAAA,GAAG,CAACK,eAAJ,CAAoBE,SAApB,GAAgCrD,cAAc,GAC1CnD,SAD0C,GAE1CoG,YAFJ,CAAA;EAGD,OAJD,CAIE,OAAOnI,CAAP,EAAU;EAEX,OAAA;EACF,KAAA;;EAED,IAAMwI,MAAAA,IAAI,GAAGR,GAAG,CAACQ,IAAJ,IAAYR,GAAG,CAACK,eAA7B,CAAA;;EAEA,IAAIN,IAAAA,KAAK,IAAIE,iBAAb,EAAgC;EAC9BO,MAAAA,IAAI,CAACC,YAAL,CACElI,QAAQ,CAACmI,cAAT,CAAwBT,iBAAxB,CADF,EAEEO,IAAI,CAACG,UAAL,CAAgB,CAAhB,KAAsB,IAFxB,CAAA,CAAA;EAID,KAAA;EAED;;;EACA,IAAI1D,IAAAA,SAAS,KAAKD,cAAlB,EAAkC;EAChC,MAAA,OAAO7C,oBAAoB,CAACyG,IAArB,CACLZ,GADK,EAELpE,cAAc,GAAG,MAAH,GAAY,MAFrB,CAAA,CAGL,CAHK,CAAP,CAAA;EAID,KAAA;;EAED,IAAA,OAAOA,cAAc,GAAGoE,GAAG,CAACK,eAAP,GAAyBG,IAA9C,CAAA;EACD,GAnED,CAAA;EAqEA;EACF;EACA;EACA;EACA;EACA;;;EACE,EAAA,MAAMK,mBAAmB,GAAG,SAAtBA,mBAAsB,CAAU1I,IAAV,EAAgB;EAC1C,IAAO8B,OAAAA,kBAAkB,CAAC2G,IAAnB,CACLzI,IAAI,CAAC0B,aAAL,IAAsB1B,IADjB,EAELA,IAFK;EAILa,IAAAA,UAAU,CAAC8H,YAAX,GAA0B9H,UAAU,CAAC+H,YAArC,GAAoD/H,UAAU,CAACgI,SAJ1D,EAKL,IALK,CAAP,CAAA;EAOD,GARD,CAAA;EAUA;EACF;EACA;EACA;EACA;EACA;;;EACE,EAAA,MAAMC,YAAY,GAAG,SAAfA,YAAe,CAAUC,GAAV,EAAe;EAClC,IAAA,OACEA,GAAG,YAAY/H,eAAf,KACC,OAAO+H,GAAG,CAACC,QAAX,KAAwB,QAAxB,IACC,OAAOD,GAAG,CAACE,WAAX,KAA2B,QAD5B,IAEC,OAAOF,GAAG,CAAC7B,WAAX,KAA2B,UAF5B,IAGC,EAAE6B,GAAG,CAACG,UAAJ,YAA0BpI,YAA5B,CAHD,IAIC,OAAOiI,GAAG,CAACtB,eAAX,KAA+B,UAJhC,IAKC,OAAOsB,GAAG,CAACrB,YAAX,KAA4B,UAL7B,IAMC,OAAOqB,GAAG,CAACnC,YAAX,KAA4B,QAN7B,IAOC,OAAOmC,GAAG,CAACT,YAAX,KAA4B,UAP7B,IAQC,OAAOS,GAAG,CAACI,aAAX,KAA6B,UAT/B,CADF,CAAA;EAYD,GAbD,CAAA;EAeA;EACF;EACA;EACA;EACA;EACA;;;EACE,EAAA,MAAMC,OAAO,GAAG,SAAVA,OAAU,CAAUlM,MAAV,EAAkB;EAChC,IAAA,OAAO,OAAOyD,IAAP,KAAgB,UAAhB,IAA8BzD,MAAM,YAAYyD,IAAvD,CAAA;EACD,GAFD,CAAA;EAIA;EACF;EACA;EACA;EACA;EACA;EACA;EACA;;;EACE,EAAM0I,MAAAA,YAAY,GAAG,SAAfA,YAAe,CAAUC,UAAV,EAAsBC,WAAtB,EAAmCC,IAAnC,EAAyC;EAC5D,IAAA,IAAI,CAACtH,KAAK,CAACoH,UAAD,CAAV,EAAwB;EACtB,MAAA,OAAA;EACD,KAAA;;EAED3O,IAAAA,YAAY,CAACuH,KAAK,CAACoH,UAAD,CAAN,EAAqBG,IAAD,IAAU;EACxCA,MAAAA,IAAI,CAAChB,IAAL,CAAU1I,SAAV,EAAqBwJ,WAArB,EAAkCC,IAAlC,EAAwCnE,MAAxC,CAAA,CAAA;EACD,KAFW,CAAZ,CAAA;EAGD,GARD,CAAA;EAUA;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACE,EAAA,MAAMqE,iBAAiB,GAAG,SAApBA,iBAAoB,CAAUH,WAAV,EAAuB;EAC/C,IAAI9H,IAAAA,OAAO,GAAG,IAAd,CAAA;EAEA;;EACA4H,IAAAA,YAAY,CAAC,wBAAD,EAA2BE,WAA3B,EAAwC,IAAxC,CAAZ,CAAA;EAEA;;;EACA,IAAA,IAAIT,YAAY,CAACS,WAAD,CAAhB,EAA+B;EAC7BxC,MAAAA,YAAY,CAACwC,WAAD,CAAZ,CAAA;;EACA,MAAA,OAAO,IAAP,CAAA;EACD,KAAA;EAED;;;EACA,IAAA,MAAM5C,OAAO,GAAG/J,iBAAiB,CAAC2M,WAAW,CAACP,QAAb,CAAjC,CAAA;EAEA;;EACAK,IAAAA,YAAY,CAAC,qBAAD,EAAwBE,WAAxB,EAAqC;EAC/C5C,MAAAA,OAD+C;EAE/CgD,MAAAA,WAAW,EAAEtH,YAAAA;EAFkC,KAArC,CAAZ,CAAA;EAKA;;;EACA,IAAA,IACEkH,WAAW,CAACJ,aAAZ,EAAA,IACA,CAACC,OAAO,CAACG,WAAW,CAACK,iBAAb,CADR,IAEA3N,UAAU,CAAC,SAAD,EAAYsN,WAAW,CAACnB,SAAxB,CAFV,IAGAnM,UAAU,CAAC,SAAD,EAAYsN,WAAW,CAACN,WAAxB,CAJZ,EAKE;EACAlC,MAAAA,YAAY,CAACwC,WAAD,CAAZ,CAAA;;EACA,MAAA,OAAO,IAAP,CAAA;EACD,KAAA;EAED;;;EACA,IAAI,IAAA,CAAClH,YAAY,CAACsE,OAAD,CAAb,IAA0BzD,WAAW,CAACyD,OAAD,CAAzC,EAAoD;EAClD;EACA,MAAI,IAAA,CAACzD,WAAW,CAACyD,OAAD,CAAZ,IAAyBkD,qBAAqB,CAAClD,OAAD,CAAlD,EAA6D;EAC3D,QAAA,IACEhE,uBAAuB,CAACC,YAAxB,YAAgD1G,MAAhD,IACAD,UAAU,CAAC0G,uBAAuB,CAACC,YAAzB,EAAuC+D,OAAvC,CAFZ,EAGE;EACA,UAAA,OAAO,KAAP,CAAA;EACD,SAAA;;EAED,QAAA,IACEhE,uBAAuB,CAACC,YAAxB,YAAgD6C,QAAhD,IACA9C,uBAAuB,CAACC,YAAxB,CAAqC+D,OAArC,CAFF,EAGE;EACA,UAAA,OAAO,KAAP,CAAA;EACD,SAAA;EACF,OAAA;EAED;;;EACA,MAAA,IAAIzC,YAAY,IAAI,CAACG,eAAe,CAACsC,OAAD,CAApC,EAA+C;EAC7C,QAAMM,MAAAA,UAAU,GAAG3F,aAAa,CAACiI,WAAD,CAAb,IAA8BA,WAAW,CAACtC,UAA7D,CAAA;EACA,QAAMuB,MAAAA,UAAU,GAAGnH,aAAa,CAACkI,WAAD,CAAb,IAA8BA,WAAW,CAACf,UAA7D,CAAA;;EAEA,QAAIA,IAAAA,UAAU,IAAIvB,UAAlB,EAA8B;EAC5B,UAAA,MAAM6C,UAAU,GAAGtB,UAAU,CAAC1L,MAA9B,CAAA;;EAEA,UAAA,KAAK,IAAIiN,CAAC,GAAGD,UAAU,GAAG,CAA1B,EAA6BC,CAAC,IAAI,CAAlC,EAAqC,EAAEA,CAAvC,EAA0C;EACxC9C,YAAAA,UAAU,CAACqB,YAAX,CACEnH,SAAS,CAACqH,UAAU,CAACuB,CAAD,CAAX,EAAgB,IAAhB,CADX,EAEE3I,cAAc,CAACmI,WAAD,CAFhB,CAAA,CAAA;EAID,WAAA;EACF,SAAA;EACF,OAAA;;EAEDxC,MAAAA,YAAY,CAACwC,WAAD,CAAZ,CAAA;;EACA,MAAA,OAAO,IAAP,CAAA;EACD,KAAA;EAED;;;EACA,IAAIA,IAAAA,WAAW,YAAY3I,OAAvB,IAAkC,CAAC6F,oBAAoB,CAAC8C,WAAD,CAA3D,EAA0E;EACxExC,MAAAA,YAAY,CAACwC,WAAD,CAAZ,CAAA;;EACA,MAAA,OAAO,IAAP,CAAA;EACD,KAAA;EAED;;;EACA,IACE,IAAA,CAAC5C,OAAO,KAAK,UAAZ,IACCA,OAAO,KAAK,SADb,IAECA,OAAO,KAAK,UAFd,KAGA1K,UAAU,CAAC,6BAAD,EAAgCsN,WAAW,CAACnB,SAA5C,CAJZ,EAKE;EACArB,MAAAA,YAAY,CAACwC,WAAD,CAAZ,CAAA;;EACA,MAAA,OAAO,IAAP,CAAA;EACD,KAAA;EAED;;;EACA,IAAA,IAAI/F,kBAAkB,IAAI+F,WAAW,CAAClJ,QAAZ,KAAyB,CAAnD,EAAsD;EACpD;EACAoB,MAAAA,OAAO,GAAG8H,WAAW,CAACN,WAAtB,CAAA;EAEAtO,MAAAA,YAAY,CAAC,CAAC2D,aAAD,EAAgBC,QAAhB,EAA0BC,WAA1B,CAAD,EAA0CwL,IAAD,IAAU;EAC7DvI,QAAAA,OAAO,GAAG9F,aAAa,CAAC8F,OAAD,EAAUuI,IAAV,EAAgB,GAAhB,CAAvB,CAAA;EACD,OAFW,CAAZ,CAAA;;EAIA,MAAA,IAAIT,WAAW,CAACN,WAAZ,KAA4BxH,OAAhC,EAAyC;EACvCvG,QAAAA,SAAS,CAAC6E,SAAS,CAACI,OAAX,EAAoB;EAAEpD,UAAAA,OAAO,EAAEwM,WAAW,CAACpI,SAAZ,EAAA;EAAX,SAApB,CAAT,CAAA;EACAoI,QAAAA,WAAW,CAACN,WAAZ,GAA0BxH,OAA1B,CAAA;EACD,OAAA;EACF,KAAA;EAED;;;EACA4H,IAAAA,YAAY,CAAC,uBAAD,EAA0BE,WAA1B,EAAuC,IAAvC,CAAZ,CAAA;;EAEA,IAAA,OAAO,KAAP,CAAA;EACD,GA5GD,CAAA;EA8GA;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACE;;;EACA,EAAMU,MAAAA,iBAAiB,GAAG,SAApBA,iBAAoB,CAAUC,KAAV,EAAiBC,MAAjB,EAAyB9M,KAAzB,EAAgC;EACxD;EACA,IAAA,IACE0G,YAAY,KACXoG,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,MADnB,CAAZ,KAEC9M,KAAK,IAAI+C,QAAT,IAAqB/C,KAAK,IAAIiI,WAF/B,CADF,EAIE;EACA,MAAA,OAAO,KAAP,CAAA;EACD,KAAA;EAED;EACJ;EACA;EACA;;;EACI,IAAA,IACEjC,eAAe,IACf,CAACF,WAAW,CAACgH,MAAD,CADZ,IAEAlO,UAAU,CAACwC,SAAD,EAAY0L,MAAZ,CAHZ,EAIE,CAJF,MAMO,IAAI/G,eAAe,IAAInH,UAAU,CAACyC,SAAD,EAAYyL,MAAZ,CAAjC,EAAsD,CAAtD,MAGA,IAAI,CAAC3H,YAAY,CAAC2H,MAAD,CAAb,IAAyBhH,WAAW,CAACgH,MAAD,CAAxC,EAAkD;EACvD,MACE;EACA;EACA;EACCN,MAAAA,qBAAqB,CAACK,KAAD,CAArB,KACGvH,uBAAuB,CAACC,YAAxB,YAAgD1G,MAAhD,IACAD,UAAU,CAAC0G,uBAAuB,CAACC,YAAzB,EAAuCsH,KAAvC,CADX,IAEEvH,uBAAuB,CAACC,YAAxB,YAAgD6C,QAAhD,IACC9C,uBAAuB,CAACC,YAAxB,CAAqCsH,KAArC,CAJL,CAKGvH,KAAAA,uBAAuB,CAACK,kBAAxB,YAAsD9G,MAAtD,IACAD,UAAU,CAAC0G,uBAAuB,CAACK,kBAAzB,EAA6CmH,MAA7C,CADX,IAEExH,uBAAuB,CAACK,kBAAxB,YAAsDyC,QAAtD,IACC9C,uBAAuB,CAACK,kBAAxB,CAA2CmH,MAA3C,CARL,CAAD;EAUA;EACCA,MAAAA,MAAM,KAAK,IAAX,IACCxH,uBAAuB,CAACM,8BADzB,KAEGN,uBAAuB,CAACC,YAAxB,YAAgD1G,MAAhD,IACAD,UAAU,CAAC0G,uBAAuB,CAACC,YAAzB,EAAuCvF,KAAvC,CADX,IAEEsF,uBAAuB,CAACC,YAAxB,YAAgD6C,QAAhD,IACC9C,uBAAuB,CAACC,YAAxB,CAAqCvF,KAArC,CALL,CAfH,EAqBE,CArBF,MAwBO;EACL,QAAA,OAAO,KAAP,CAAA;EACD,OAAA;EACD;;EACD,KA7BM,MA6BA,IAAIoH,mBAAmB,CAAC0F,MAAD,CAAvB,EAAiC,CAAjC,MAIA,IACLlO,UAAU,CAAC0C,gBAAD,EAAiBhD,aAAa,CAAC0B,KAAD,EAAQwB,eAAR,EAAyB,EAAzB,CAA9B,CADL,EAEL,CAFK,MAMA,IACL,CAACsL,MAAM,KAAK,KAAX,IAAoBA,MAAM,KAAK,YAA/B,IAA+CA,MAAM,KAAK,MAA3D,KACAD,KAAK,KAAK,QADV,IAEArO,aAAa,CAACwB,KAAD,EAAQ,OAAR,CAAb,KAAkC,CAFlC,IAGAkH,aAAa,CAAC2F,KAAD,CAJR,EAKL,CALK,MAUA,IACL5G,uBAAuB,IACvB,CAACrH,UAAU,CAAC2C,iBAAD,EAAoBjD,aAAa,CAAC0B,KAAD,EAAQwB,eAAR,EAAyB,EAAzB,CAAjC,CAFN,EAGL,CAHK,MAMA,IAAIxB,KAAJ,EAAW;EAChB,MAAA,OAAO,KAAP,CAAA;EACD,KAFM,MAEA,CAGN;;EAED,IAAA,OAAO,IAAP,CAAA;EACD,GAtFD,CAAA;EAwFA;EACF;EACA;EACA;EACA;EACA;EACA;EACA;;;EACE,EAAA,MAAMwM,qBAAqB,GAAG,SAAxBA,qBAAwB,CAAUlD,OAAV,EAAmB;EAC/C,IAAA,OAAOA,OAAO,CAAC7K,OAAR,CAAgB,GAAhB,IAAuB,CAA9B,CAAA;EACD,GAFD,CAAA;EAIA;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACE,EAAA,MAAMsO,mBAAmB,GAAG,SAAtBA,mBAAsB,CAAUb,WAAV,EAAuB;EACjD;EACAF,IAAAA,YAAY,CAAC,0BAAD,EAA6BE,WAA7B,EAA0C,IAA1C,CAAZ,CAAA;;EAEA,IAAM,MAAA;EAAEL,MAAAA,UAAAA;EAAF,KAAA,GAAiBK,WAAvB,CAAA;EAEA;;EACA,IAAI,IAAA,CAACL,UAAL,EAAiB;EACf,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAMmB,SAAS,GAAG;EAChBC,MAAAA,QAAQ,EAAE,EADM;EAEhBC,MAAAA,SAAS,EAAE,EAFK;EAGhBC,MAAAA,QAAQ,EAAE,IAHM;EAIhBC,MAAAA,iBAAiB,EAAEjI,YAAAA;EAJH,KAAlB,CAAA;EAMA,IAAA,IAAI3F,CAAC,GAAGqM,UAAU,CAACpM,MAAnB,CAAA;EAEA;;EACA,IAAOD,OAAAA,CAAC,EAAR,EAAY;EACV,MAAA,MAAM6N,IAAI,GAAGxB,UAAU,CAACrM,CAAD,CAAvB,CAAA;EACA,MAAM,MAAA;EAAEwK,QAAAA,IAAF;EAAQT,QAAAA,YAAR;EAAsBvJ,QAAAA,KAAK,EAAEkN,SAAAA;EAA7B,OAAA,GAA2CG,IAAjD,CAAA;EACA,MAAA,MAAMP,MAAM,GAAGvN,iBAAiB,CAACyK,IAAD,CAAhC,CAAA;EAEA,MAAIhK,IAAAA,KAAK,GAAGgK,IAAI,KAAK,OAAT,GAAmBkD,SAAnB,GAA+BxO,UAAU,CAACwO,SAAD,CAArD,CAAA;EAEA;;EACAF,MAAAA,SAAS,CAACC,QAAV,GAAqBH,MAArB,CAAA;EACAE,MAAAA,SAAS,CAACE,SAAV,GAAsBlN,KAAtB,CAAA;EACAgN,MAAAA,SAAS,CAACG,QAAV,GAAqB,IAArB,CAAA;EACAH,MAAAA,SAAS,CAACM,aAAV,GAA0BrN,SAA1B,CAXU;;EAYV+L,MAAAA,YAAY,CAAC,uBAAD,EAA0BE,WAA1B,EAAuCc,SAAvC,CAAZ,CAAA;;EACAhN,MAAAA,KAAK,GAAGgN,SAAS,CAACE,SAAlB,CAAA;EACA;;EACA,MAAIF,IAAAA,SAAS,CAACM,aAAd,EAA6B;EAC3B,QAAA,SAAA;EACD,OAAA;EAED;;;EACAvD,MAAAA,gBAAgB,CAACC,IAAD,EAAOkC,WAAP,CAAhB,CAAA;EAEA;;;EACA,MAAA,IAAI,CAACc,SAAS,CAACG,QAAf,EAAyB;EACvB,QAAA,SAAA;EACD,OAAA;EAED;;;EACA,MAAI,IAAA,CAACjH,wBAAD,IAA6BtH,UAAU,CAAC,MAAD,EAASoB,KAAT,CAA3C,EAA4D;EAC1D+J,QAAAA,gBAAgB,CAACC,IAAD,EAAOkC,WAAP,CAAhB,CAAA;;EACA,QAAA,SAAA;EACD,OAAA;EAED;;;EACA,MAAA,IAAI/F,kBAAJ,EAAwB;EACtB7I,QAAAA,YAAY,CAAC,CAAC2D,aAAD,EAAgBC,QAAhB,EAA0BC,WAA1B,CAAD,EAA0CwL,IAAD,IAAU;EAC7D3M,UAAAA,KAAK,GAAG1B,aAAa,CAAC0B,KAAD,EAAQ2M,IAAR,EAAc,GAAd,CAArB,CAAA;EACD,SAFW,CAAZ,CAAA;EAGD,OAAA;EAED;;;EACA,MAAA,MAAME,KAAK,GAAGtN,iBAAiB,CAAC2M,WAAW,CAACP,QAAb,CAA/B,CAAA;;EACA,MAAI,IAAA,CAACiB,iBAAiB,CAACC,KAAD,EAAQC,MAAR,EAAgB9M,KAAhB,CAAtB,EAA8C;EAC5C,QAAA,SAAA;EACD,OAAA;EAED;EACN;EACA;;;EACM,MAAI2G,IAAAA,oBAAoB,KAAKmG,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,MAAnC,CAAxB,EAAoE;EAClE;EACA/C,QAAAA,gBAAgB,CAACC,IAAD,EAAOkC,WAAP,CAAhB,CAFkE;;;EAKlElM,QAAAA,KAAK,GAAG4G,2BAA2B,GAAG5G,KAAtC,CAAA;EACD,OAAA;EAED;;;EACA,MAAA,IACEsE,kBAAkB,IAClB,OAAOzC,YAAP,KAAwB,QADxB,IAEA,OAAOA,YAAY,CAAC0L,gBAApB,KAAyC,UAH3C,EAIE;EACA,QAAA,IAAIhE,YAAJ,EAAkB,CAAlB,MAEO;EACL,UAAA,QAAQ1H,YAAY,CAAC0L,gBAAb,CAA8BV,KAA9B,EAAqCC,MAArC,CAAR;EACE,YAAA,KAAK,aAAL;EAAoB,cAAA;EAClB9M,gBAAAA,KAAK,GAAGsE,kBAAkB,CAACjC,UAAnB,CAA8BrC,KAA9B,CAAR,CAAA;EACA,gBAAA,MAAA;EACD,eAAA;;EAED,YAAA,KAAK,kBAAL;EAAyB,cAAA;EACvBA,gBAAAA,KAAK,GAAGsE,kBAAkB,CAAChC,eAAnB,CAAmCtC,KAAnC,CAAR,CAAA;EACA,gBAAA,MAAA;EACD,eAAA;EATH,WAAA;EAeD,SAAA;EACF,OAAA;EAED;;;EACA,MAAI,IAAA;EACF,QAAA,IAAIuJ,YAAJ,EAAkB;EAChB2C,UAAAA,WAAW,CAACsB,cAAZ,CAA2BjE,YAA3B,EAAyCS,IAAzC,EAA+ChK,KAA/C,CAAA,CAAA;EACD,SAFD,MAEO;EACL;EACAkM,UAAAA,WAAW,CAAC7B,YAAZ,CAAyBL,IAAzB,EAA+BhK,KAA/B,CAAA,CAAA;EACD,SAAA;;EAEDrC,QAAAA,QAAQ,CAAC+E,SAAS,CAACI,OAAX,CAAR,CAAA;EACD,OATD,CASE,OAAON,CAAP,EAAU,EAAE;EACf,KAAA;EAED;;;EACAwJ,IAAAA,YAAY,CAAC,yBAAD,EAA4BE,WAA5B,EAAyC,IAAzC,CAAZ,CAAA;EACD,GAvHD,CAAA;EAyHA;EACF;EACA;EACA;EACA;;;EACE,EAAA,MAAMuB,kBAAkB,GAAG,SAArBA,kBAAqB,CAAUC,QAAV,EAAoB;EAC7C,IAAIC,IAAAA,UAAU,GAAG,IAAjB,CAAA;;EACA,IAAA,MAAMC,cAAc,GAAGvC,mBAAmB,CAACqC,QAAD,CAA1C,CAAA;EAEA;;;EACA1B,IAAAA,YAAY,CAAC,yBAAD,EAA4B0B,QAA5B,EAAsC,IAAtC,CAAZ,CAAA;;EAEA,IAAA,OAAQC,UAAU,GAAGC,cAAc,CAACC,QAAf,EAArB,EAAiD;EAC/C;EACA7B,MAAAA,YAAY,CAAC,wBAAD,EAA2B2B,UAA3B,EAAuC,IAAvC,CAAZ,CAAA;EAEA;;;EACA,MAAA,IAAItB,iBAAiB,CAACsB,UAAD,CAArB,EAAmC;EACjC,QAAA,SAAA;EACD,OAAA;EAED;;;EACA,MAAA,IAAIA,UAAU,CAACvJ,OAAX,YAA8BhB,gBAAlC,EAAoD;EAClDqK,QAAAA,kBAAkB,CAACE,UAAU,CAACvJ,OAAZ,CAAlB,CAAA;EACD,OAAA;EAED;;;EACA2I,MAAAA,mBAAmB,CAACY,UAAD,CAAnB,CAAA;EACD,KAAA;EAED;;;EACA3B,IAAAA,YAAY,CAAC,wBAAD,EAA2B0B,QAA3B,EAAqC,IAArC,CAAZ,CAAA;EACD,GA3BD,CAAA;EA6BA;EACF;EACA;EACA;EACA;EACA;EACA;EACE;;;EACAhL,EAAAA,SAAS,CAACoL,QAAV,GAAqB,UAAUvD,KAAV,EAA2B;EAAA,IAAVjC,IAAAA,GAAU,uEAAJ,EAAI,CAAA;EAC9C,IAAI0C,IAAAA,IAAI,GAAG,IAAX,CAAA;EACA,IAAI+C,IAAAA,YAAY,GAAG,IAAnB,CAAA;EACA,IAAI7B,IAAAA,WAAW,GAAG,IAAlB,CAAA;EACA,IAAI8B,IAAAA,UAAU,GAAG,IAAjB,CAAA;EACA;EACJ;EACA;;EACItG,IAAAA,cAAc,GAAG,CAAC6C,KAAlB,CAAA;;EACA,IAAA,IAAI7C,cAAJ,EAAoB;EAClB6C,MAAAA,KAAK,GAAG,OAAR,CAAA;EACD,KAAA;EAED;;;EACA,IAAI,IAAA,OAAOA,KAAP,KAAiB,QAAjB,IAA6B,CAACwB,OAAO,CAACxB,KAAD,CAAzC,EAAkD;EAChD,MAAA,IAAI,OAAOA,KAAK,CAACpM,QAAb,KAA0B,UAA9B,EAA0C;EACxCoM,QAAAA,KAAK,GAAGA,KAAK,CAACpM,QAAN,EAAR,CAAA;;EACA,QAAA,IAAI,OAAOoM,KAAP,KAAiB,QAArB,EAA+B;EAC7B,UAAMxL,MAAAA,eAAe,CAAC,iCAAD,CAArB,CAAA;EACD,SAAA;EACF,OALD,MAKO;EACL,QAAMA,MAAAA,eAAe,CAAC,4BAAD,CAArB,CAAA;EACD,OAAA;EACF,KAAA;EAED;;;EACA,IAAA,IAAI,CAAC2D,SAAS,CAACO,WAAf,EAA4B;EAC1B,MAAA,OAAOsH,KAAP,CAAA;EACD,KAAA;EAED;;;EACA,IAAI,IAAA,CAAClE,UAAL,EAAiB;EACfgC,MAAAA,YAAY,CAACC,GAAD,CAAZ,CAAA;EACD,KAAA;EAED;;;EACA5F,IAAAA,SAAS,CAACI,OAAV,GAAoB,EAApB,CAAA;EAEA;;EACA,IAAA,IAAI,OAAOyH,KAAP,KAAiB,QAArB,EAA+B;EAC7BzD,MAAAA,QAAQ,GAAG,KAAX,CAAA;EACD,KAAA;;EAED,IAAA,IAAIA,QAAJ,EAAc;EACZ;EACA,MAAIyD,IAAAA,KAAK,CAACoB,QAAV,EAAoB;EAClB,QAAA,MAAMrC,OAAO,GAAG/J,iBAAiB,CAACgL,KAAK,CAACoB,QAAP,CAAjC,CAAA;;EACA,QAAI,IAAA,CAAC3G,YAAY,CAACsE,OAAD,CAAb,IAA0BzD,WAAW,CAACyD,OAAD,CAAzC,EAAoD;EAClD,UAAMvK,MAAAA,eAAe,CACnB,yDADmB,CAArB,CAAA;EAGD,SAAA;EACF,OAAA;EACF,KAVD,MAUO,IAAIwL,KAAK,YAAYjH,IAArB,EAA2B;EAChC;EACN;EACM0H,MAAAA,IAAI,GAAGV,aAAa,CAAC,SAAD,CAApB,CAAA;EACAyD,MAAAA,YAAY,GAAG/C,IAAI,CAAC3G,aAAL,CAAmBO,UAAnB,CAA8B2F,KAA9B,EAAqC,IAArC,CAAf,CAAA;;EACA,MAAIwD,IAAAA,YAAY,CAAC/K,QAAb,KAA0B,CAA1B,IAA+B+K,YAAY,CAACpC,QAAb,KAA0B,MAA7D,EAAqE;EACnE;EACAX,QAAAA,IAAI,GAAG+C,YAAP,CAAA;EACD,OAHD,MAGO,IAAIA,YAAY,CAACpC,QAAb,KAA0B,MAA9B,EAAsC;EAC3CX,QAAAA,IAAI,GAAG+C,YAAP,CAAA;EACD,OAFM,MAEA;EACL;EACA/C,QAAAA,IAAI,CAACiD,WAAL,CAAiBF,YAAjB,CAAA,CAAA;EACD,OAAA;EACF,KAdM,MAcA;EACL;EACA,MACE,IAAA,CAACxH,UAAD,IACA,CAACJ,kBADD,IAEA,CAACC,cAFD;EAIAmE,MAAAA,KAAK,CAAC9L,OAAN,CAAc,GAAd,CAAuB,KAAA,CAAC,CAL1B,EAME;EACA,QAAO6F,OAAAA,kBAAkB,IAAImC,mBAAtB,GACHnC,kBAAkB,CAACjC,UAAnB,CAA8BkI,KAA9B,CADG,GAEHA,KAFJ,CAAA;EAGD,OAAA;EAED;;;EACAS,MAAAA,IAAI,GAAGV,aAAa,CAACC,KAAD,CAApB,CAAA;EAEA;;EACA,MAAI,IAAA,CAACS,IAAL,EAAW;EACT,QAAOzE,OAAAA,UAAU,GAAG,IAAH,GAAUE,mBAAmB,GAAGlC,SAAH,GAAe,EAA7D,CAAA;EACD,OAAA;EACF,KAAA;EAED;;;EACA,IAAIyG,IAAAA,IAAI,IAAI1E,UAAZ,EAAwB;EACtBoD,MAAAA,YAAY,CAACsB,IAAI,CAACkD,UAAN,CAAZ,CAAA;EACD,KAAA;EAED;;;EACA,IAAMC,MAAAA,YAAY,GAAG9C,mBAAmB,CAACvE,QAAQ,GAAGyD,KAAH,GAAWS,IAApB,CAAxC,CAAA;EAEA;;;EACA,IAAA,OAAQkB,WAAW,GAAGiC,YAAY,CAACN,QAAb,EAAtB,EAAgD;EAC9C;EACA,MAAA,IAAIxB,iBAAiB,CAACH,WAAD,CAArB,EAAoC;EAClC,QAAA,SAAA;EACD,OAAA;EAED;;;EACA,MAAA,IAAIA,WAAW,CAAC9H,OAAZ,YAA+BhB,gBAAnC,EAAqD;EACnDqK,QAAAA,kBAAkB,CAACvB,WAAW,CAAC9H,OAAb,CAAlB,CAAA;EACD,OAAA;EAED;;;EACA2I,MAAAA,mBAAmB,CAACb,WAAD,CAAnB,CAAA;EACD,KAAA;EAED;;;EACA,IAAA,IAAIpF,QAAJ,EAAc;EACZ,MAAA,OAAOyD,KAAP,CAAA;EACD,KAAA;EAED;;;EACA,IAAA,IAAIhE,UAAJ,EAAgB;EACd,MAAA,IAAIC,mBAAJ,EAAyB;EACvBwH,QAAAA,UAAU,GAAGtJ,sBAAsB,CAAC0G,IAAvB,CAA4BJ,IAAI,CAAC3G,aAAjC,CAAb,CAAA;;EAEA,QAAO2G,OAAAA,IAAI,CAACkD,UAAZ,EAAwB;EACtB;EACAF,UAAAA,UAAU,CAACC,WAAX,CAAuBjD,IAAI,CAACkD,UAA5B,CAAA,CAAA;EACD,SAAA;EACF,OAPD,MAOO;EACLF,QAAAA,UAAU,GAAGhD,IAAb,CAAA;EACD,OAAA;;EAED,MAAA,IAAI7F,YAAY,CAACiJ,UAAb,IAA2BjJ,YAAY,CAACkJ,cAA5C,EAA4D;EAC1D;EACR;EACA;EACA;EACA;EACA;EACA;EACQL,QAAAA,UAAU,GAAGpJ,UAAU,CAACwG,IAAX,CAAgBlI,gBAAhB,EAAkC8K,UAAlC,EAA8C,IAA9C,CAAb,CAAA;EACD,OAAA;;EAED,MAAA,OAAOA,UAAP,CAAA;EACD,KAAA;;EAED,IAAIM,IAAAA,cAAc,GAAGlI,cAAc,GAAG4E,IAAI,CAACuD,SAAR,GAAoBvD,IAAI,CAACD,SAA5D,CAAA;EAEA;;EACA,IAAA,IACE3E,cAAc,IACdpB,YAAY,CAAC,UAAD,CADZ,IAEAgG,IAAI,CAAC3G,aAFL,IAGA2G,IAAI,CAAC3G,aAAL,CAAmBmK,OAHnB,IAIAxD,IAAI,CAAC3G,aAAL,CAAmBmK,OAAnB,CAA2BxE,IAJ3B,IAKApL,UAAU,CAACmG,YAAD,EAA2BiG,IAAI,CAAC3G,aAAL,CAAmBmK,OAAnB,CAA2BxE,IAAtD,CANZ,EAOE;EACAsE,MAAAA,cAAc,GACZ,YAAetD,GAAAA,IAAI,CAAC3G,aAAL,CAAmBmK,OAAnB,CAA2BxE,IAA1C,GAAiD,KAAjD,GAAyDsE,cAD3D,CAAA;EAED,KAAA;EAED;;;EACA,IAAA,IAAInI,kBAAJ,EAAwB;EACtB7I,MAAAA,YAAY,CAAC,CAAC2D,aAAD,EAAgBC,QAAhB,EAA0BC,WAA1B,CAAD,EAA0CwL,IAAD,IAAU;EAC7D2B,QAAAA,cAAc,GAAGhQ,aAAa,CAACgQ,cAAD,EAAiB3B,IAAjB,EAAuB,GAAvB,CAA9B,CAAA;EACD,OAFW,CAAZ,CAAA;EAGD,KAAA;;EAED,IAAOrI,OAAAA,kBAAkB,IAAImC,mBAAtB,GACHnC,kBAAkB,CAACjC,UAAnB,CAA8BiM,cAA9B,CADG,GAEHA,cAFJ,CAAA;EAGD,GA3KD,CAAA;EA6KA;EACF;EACA;EACA;EACA;EACA;;;EACE5L,EAAAA,SAAS,CAAC+L,SAAV,GAAsB,YAAoB;EAAA,IAAVnG,IAAAA,GAAU,uEAAJ,EAAI,CAAA;;EACxCD,IAAAA,YAAY,CAACC,GAAD,CAAZ,CAAA;;EACAjC,IAAAA,UAAU,GAAG,IAAb,CAAA;EACD,GAHD,CAAA;EAKA;EACF;EACA;EACA;EACA;;;EACE3D,EAAAA,SAAS,CAACgM,WAAV,GAAwB,YAAY;EAClC1G,IAAAA,MAAM,GAAG,IAAT,CAAA;EACA3B,IAAAA,UAAU,GAAG,KAAb,CAAA;EACD,GAHD,CAAA;EAKA;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACE3D,EAAAA,SAAS,CAACiM,gBAAV,GAA6B,UAAUC,GAAV,EAAevB,IAAf,EAAqBrN,KAArB,EAA4B;EACvD;EACA,IAAI,IAAA,CAACgI,MAAL,EAAa;EACXK,MAAAA,YAAY,CAAC,EAAD,CAAZ,CAAA;EACD,KAAA;;EAED,IAAA,MAAMwE,KAAK,GAAGtN,iBAAiB,CAACqP,GAAD,CAA/B,CAAA;EACA,IAAA,MAAM9B,MAAM,GAAGvN,iBAAiB,CAAC8N,IAAD,CAAhC,CAAA;EACA,IAAA,OAAOT,iBAAiB,CAACC,KAAD,EAAQC,MAAR,EAAgB9M,KAAhB,CAAxB,CAAA;EACD,GATD,CAAA;EAWA;EACF;EACA;EACA;EACA;EACA;EACA;;;EACE0C,EAAAA,SAAS,CAACmM,OAAV,GAAoB,UAAU5C,UAAV,EAAsB6C,YAAtB,EAAoC;EACtD,IAAA,IAAI,OAAOA,YAAP,KAAwB,UAA5B,EAAwC;EACtC,MAAA,OAAA;EACD,KAAA;;EAEDjK,IAAAA,KAAK,CAACoH,UAAD,CAAL,GAAoBpH,KAAK,CAACoH,UAAD,CAAL,IAAqB,EAAzC,CAAA;EACApO,IAAAA,SAAS,CAACgH,KAAK,CAACoH,UAAD,CAAN,EAAoB6C,YAApB,CAAT,CAAA;EACD,GAPD,CAAA;EASA;EACF;EACA;EACA;EACA;EACA;EACA;EACA;;;EACEpM,EAAAA,SAAS,CAACqM,UAAV,GAAuB,UAAU9C,UAAV,EAAsB;EAC3C,IAAA,IAAIpH,KAAK,CAACoH,UAAD,CAAT,EAAuB;EACrB,MAAA,OAAOtO,QAAQ,CAACkH,KAAK,CAACoH,UAAD,CAAN,CAAf,CAAA;EACD,KAAA;EACF,GAJD,CAAA;EAMA;EACF;EACA;EACA;EACA;EACA;;;EACEvJ,EAAAA,SAAS,CAACsM,WAAV,GAAwB,UAAU/C,UAAV,EAAsB;EAC5C,IAAA,IAAIpH,KAAK,CAACoH,UAAD,CAAT,EAAuB;EACrBpH,MAAAA,KAAK,CAACoH,UAAD,CAAL,GAAoB,EAApB,CAAA;EACD,KAAA;EACF,GAJD,CAAA;EAMA;EACF;EACA;EACA;;;EACEvJ,EAAAA,SAAS,CAACuM,cAAV,GAA2B,YAAY;EACrCpK,IAAAA,KAAK,GAAG,EAAR,CAAA;EACD,GAFD,CAAA;;EAIA,EAAA,OAAOnC,SAAP,CAAA;EACD,CAAA;;AAED,eAAeD,eAAe,EAA9B;;;;;;;;"}