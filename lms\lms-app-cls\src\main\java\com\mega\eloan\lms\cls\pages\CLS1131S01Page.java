/* 
 *CLS1131S01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.lms.cls.panels.CLS1131S01Panel;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 放款信用評分表
 * </pre>
 * 
 * @since 2012/11/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/2,Fantasy,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1131s01")
public class CLS1131S01Page extends AbstractOutputPage {
	
	private static final Logger logger = LoggerFactory.getLogger(CLS1131S01Page.class);

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {

		setNeedHtml(true); // need html

		new CLS1131S01Panel("CLS1131S01", params.getString("mainId")).processPanelData(model, params); // add panel

		renderJsI18N(CLS1131S01Panel.class); // render i18n

		setJavascript(new String[] { "pagejs/cls/CLS1131S01Page.js" });

		return "&nbsp;";
	}

	@Override
	protected String getViewName() {
		return getEloanPagePathByClass(getClass());
	}
	
	
    @Override
    public void execute(ModelMap model, PageParameters params) throws Exception {

        HttpServletResponse response = getResponse();
        response.setCharacterEncoding(getCharCode());
        response.setContentType("text/html;charset=" + getCharCode());

        String output = getOutputString(model, params);
        if (Util.isEmpty(output)) {
            StringBuilder sb = new StringBuilder();
            sb.append("<center><b><font color=red>");
            sb.append(getI18nMsg("noData"));
            sb.append("</font></b></center>");

            output = sb.toString();
        }

        // 2023/09/12 fix checkmarx Stored Open Redirect.
        // 目前只有 ces 和 lms 有程式繼承此 class
        // ces 的程式並不會回傳 http:// 開頭的 output
        // // redirect
        // if (output.startsWith("http://")) {
        // response.sendRedirect(output);
        // } else {
        response.getWriter().write(output);
        // }

        // render javascript
        if (getJavascript() != null) {
            StringBuilder sb = new StringBuilder();
            
            //renderJsI18N For js
            String i18njs = MessageBundleScriptCreator.createScript(CLS1131S01Panel.class.getSimpleName());
            sb.append("<script type=\"text/javascript\" id=\"i18n-" + CLS1131S01Panel.class.getSimpleName() + "\"> ");
            sb.append(i18njs).append("</script>");
            
            for (String javascript : getJavascript()) {
                sb.append(EloanConstants.HTML_NEWLINE);
                sb.append("<script type=\"text/javascript\" src=\"");
                sb.append(getServerBaseURL()).append(javascript).append("\"></script>");
            }
            
            logger.info(sb.toString());
            response.getWriter().write(sb.toString());
        }

    }

}
