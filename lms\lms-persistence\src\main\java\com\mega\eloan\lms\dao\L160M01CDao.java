/* 
 * L160M01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L160M01C;

/** 動審表查核項目資料 **/
public interface L160M01CDao extends IGenericDao<L160M01C> {

	L160M01C findByOid(String oid);
	
	List<L160M01C> findByMainId(String mainId);
	
	L160M01C findByUniqueKey(String mainId, String itemType, Integer itemSeq);

	List<L160M01C> findByIndex01(String mainId, String itemType, Integer itemSeq);
}