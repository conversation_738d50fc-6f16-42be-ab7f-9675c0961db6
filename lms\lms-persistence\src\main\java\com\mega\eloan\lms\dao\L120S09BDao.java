/* 
 * L120S09BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S09B;

/** 洗錢防制主檔 **/
public interface L120S09BDao extends IGenericDao<L120S09B> {

	L120S09B findByOid(String oid);
	
	L120S09B findByMainId(String mainId);
	
	L120S09B findByMainId_latestOne(String mainId);
	L120S09B findByRefNo_latestOne(String refNo);
}