package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;

/** 央行購屋／空地／興宅貸款資料檔
===== 當 update elf500 的某些欄位, 會 call trigger, 再 call LOAN.TEST.SOURCE(LNSP0400) 
*/
public class LNF087 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="LNF087_CONTRACT", length=12, columnDefinition="CHAR(12)", nullable=false,unique = true)
	private String lnf087_contract; //ELF500_CNTRNO

	/** 控管類別{1:自然人購屋, 5:法人購屋貸款, 2:[自然人+法人]土地抵押貸款, 4:非屬填報對象 } **/
	//L140M01M.CbcCase
	@Size(max=1)
	@Column(name="LNF087_CONTROL_CD", length=1, columnDefinition="CHAR(1)")
	private String lnf087_control_cd; //ELF500_CONTROL_CD

	/** 本案是否於控管期間內Y/N **/
	@Size(max=1)
	@Column(name="LNF087_DURING_FLAG", length=1, columnDefinition="CHAR(1)")
	private String lnf087_during_flag;

	/** 貸放成數 **/
	//L140M01M.PayPercent 
	@Column(name="LNF087_LTV_RATE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal lnf087_ltv_rate; //ELF500_LTV_RATE

	/** 擔保品坐落區 **/
	//LMSUtil::getUploadLocationCd(L140M01M.AreaId) 
	@Size(max=3)
	@Column(name="LNF087_LOCATION_CD", length=3, columnDefinition="CHAR(3)")
	private String lnf087_location_cd; //ELF500_LOCATION_CD

	/** JCIC已有購屋融資註記=L140M01M.custYN{Y:一戶, B:二戶(含)以上, N:無}  **/
	//L140M01M.CustYN 
	@Size(max=1)
	@Column(name="LNF087_JCIC_MARK", length=1, columnDefinition="CHAR(1)")
	private String lnf087_jcic_mark;  //ELF500_JCIC_MARK

	/** 此次報送撥款金額 **/
	@Column(name="LNF087_TOT_DR_AMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf087_tot_dr_amt;

	/** 最近一次報送日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="LNF087_REPORT_DATE", columnDefinition="DATE")
	private Date lnf087_report_date;

	/** 最近一次報送金額 **/
	@Column(name="LNF087_REPORT_AMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf087_report_amt;

	/** 擔保品購價或時價 **/
	//隨CbcCase 決定，抓 appAmt 
	@Column(name="LNF087_APP_AMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf087_app_amt;  //ELF500_APP_AMT

	/** 控管類別4之理由 **/
	//L140M01M.PlusReason
	@Size(max=1)
	@Column(name="LNF087_PLUS_REASON", length=1, columnDefinition="CHAR(1)")
	private String lnf087_plus_reason;  //ELF500_PLUS_REASON

	/** 有無與其他額度共用擔保品 **/
	//L140M01M.CommonYN 
	@Size(max=1)
	@Column(name="LNF087_COCOLL_FG", length=1, columnDefinition="CHAR(1)")
	private String lnf087_cocoll_fg;  //ELF500_COCOLL_FG

	/** 共用同一擔保品總額度金額 **/
	//CommonYN,ShareCollYN 決定ShareCollAmt 
	@Column(name="LNF087_SUM_FACTAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf087_sum_factamt;  //ELF500_SUM_FACTAMT

	/** 有無授權外核准得分批動用 **/
	//L140M01M.ShareCollYN 
	@Size(max=1)
	@Column(name="LNF087_PART_FUND", length=1, columnDefinition="CHAR(1)")
	private String lnf087_part_fund;  //ELF500_PART_FUND

	/** 非屬央行填報對象理由 **/
	//L140M01M.PlusReasonMeMo
	@Size(max=62)
	@Column(name="LNF087_PLUS_MEMO", length=62, columnDefinition="CHAR(62)")
	private String lnf087_plus_memo;  //ELF500_PLUS_MEMO 

	/** 「住」或「住商」無營業註記 **/
	//L140M01M.BuildYN 
	@Size(max=1)
	@Column(name="LNF087_REG_PURPOSE", length=1, columnDefinition="CHAR(1)")
	private String lnf087_reg_purpose;  //ELF500_REG_PURPOSE

	/** 擔保品鑑價 **/
	//隨CbcCase 決定，抓 NowAMT
	@Column(name="LNF087_EST_AMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf087_est_amt;  //ELF500_EST_AMT

	/** 擔保品估價 **/
	//隨CbcCase 決定，抓 ValueAMT
	@Column(name="LNF087_LAWVAL", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf087_lawval;  //ELF500_LAWVAL

	/** 是否為受限戶Y/N **/
	//L140M01M.isLimitCust(由系統判斷，非人工輸入)
	@Size(max=1)
	@Column(name="LNF087_RESTRICT", length=1, columnDefinition="CHAR(1)")
	private String lnf087_restrict;  //ELF500_RESTRICT

	/** 是否為高價住宅Y/N **/
	//L140M01M.IsHighHouse
	@Size(max=1)
	@Column(name="LNF087_HP_HOUSE", length=1, columnDefinition="CHAR(1)")
	private String lnf087_hp_house;  //ELF500_HP_HOUSE

	/** 屬都市計畫劃定區域Y/N **/
	//L140M01M.HouseYN
	@Size(max=1)
	@Column(name="LNF087_PLAN_AREA", length=1, columnDefinition="CHAR(1)")
	private String lnf087_plan_area;  //ELF500_PLAN_AREA

	/** 都市計畫使用分區 **/
	//L140M01M.HouseType
	@Size(max=1)
	@Column(name="LNF087_P_USETYPE", length=1, columnDefinition="CHAR(1)")
	private String lnf087_p_usetype;  //ELF500_P_USETYPE

	/** 借款用途 **/
	//L140M01M.PurposeType
	@Size(max=1)
	@Column(name="LNF087_P_LOANUSE", length=1, columnDefinition="CHAR(1)")
	private String lnf087_p_loanuse;  //ELF500_P_LOANUSE

	/** 擔保品性質別 **/
	//L140M01M.CmsType
	@Size(max=1)
	@Column(name="LNF087_COLL_CHAR", length=1, columnDefinition="CHAR(1)")
	private String lnf087_coll_char;  //ELF500_COLL_CHAR

	/** 是否保留一成估值Y/N **/
	//L140M01M.KeepYN
	@Size(max=1)
	@Column(name="LNF087_KEEP_LAWVAL", length=1, columnDefinition="CHAR(1)")
	private String lnf087_keep_lawval;  //ELF500_KEEP_LAWVAL

	/** 座落區段 **/
	//L140M01M 有 sit3No【dec(4,0)】, site3No【dec(4,0)---(預約額度)】
	@Column(name="LNF087_SITE3NO", columnDefinition="DECIMAL(4,0)")
	private Integer lnf087_site3no;  //ELF500_SITE3NO 

	/** 座落區村里 **/
	@Size(max=11)
	@Column(name="LNF087_SITE4NO", length=11, columnDefinition="CHAR(11)")
	private String lnf087_site4no;  //ELF500_SITE4NO 

	/** 擔保品性質別其他之說明 **/
	//L140M01M.CmsOther
	@Size(max=22)
	@Column(name="LNF087_COLL_CHAR_M", length=22, columnDefinition="CHAR(22)")
	private String lnf087_coll_char_m;  //ELF500_COLL_CHAR_M

	/** 擔保品屋齡 **/
	//L140M01M.
	@Column(name="LNF087_HOUSE_AGE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal lnf087_house_age;  //ELF500_HOUSE_AGE 

	/** 擔保品放款值 **/
	//L140M01M.
	@Column(name="LNF087_LOANAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf087_loanAmt;  //ELF500_LOANAMT
	
	
	/** 取得額度序號 **/
	public String getLnf087_contract() {
		return this.lnf087_contract;
	}
	/** 設定額度序號 **/
	public void setLnf087_contract(String value) {
		this.lnf087_contract = value;
	}

	/** 取得控管類別{1:自然人購屋, 5:法人購屋貸款, 2:[自然人+法人]土地抵押貸款, 4:非屬填報對象 }  **/
	public String getLnf087_control_cd() {
		return this.lnf087_control_cd;
	}
	/** 設定控管類別{1:自然人購屋, 5:法人購屋貸款, 2:[自然人+法人]土地抵押貸款, 4:非屬填報對象 }  **/
	public void setLnf087_control_cd(String value) {
		this.lnf087_control_cd = value;
	}

	/** 取得本案是否於控管期間內Y/N **/
	public String getLnf087_during_flag() {
		return this.lnf087_during_flag;
	}
	/** 設定本案是否於控管期間內Y/N **/
	public void setLnf087_during_flag(String value) {
		this.lnf087_during_flag = value;
	}

	/** 取得貸放成數 **/
	public BigDecimal getLnf087_ltv_rate() {
		return this.lnf087_ltv_rate;
	}
	/** 設定貸放成數 **/
	public void setLnf087_ltv_rate(BigDecimal value) {
		this.lnf087_ltv_rate = value;
	}

	/** 取得擔保品坐落區 **/
	public String getLnf087_location_cd() {
		return this.lnf087_location_cd;
	}
	/** 設定擔保品坐落區 **/
	public void setLnf087_location_cd(String value) {
		this.lnf087_location_cd = value;
	}

	/** 取得JCIC已有購屋融資註記=L140M01M.custYN{Y:一戶, B:二戶(含)以上, N:無}  **/
	public String getLnf087_jcic_mark() {
		return this.lnf087_jcic_mark;
	}
	/** 設定JCIC已有購屋融資註記=L140M01M.custYN{Y:一戶, B:二戶(含)以上, N:無}  **/
	public void setLnf087_jcic_mark(String value) {
		this.lnf087_jcic_mark = value;
	}

	/** 取得此次報送撥款金額 **/
	public BigDecimal getLnf087_tot_dr_amt() {
		return this.lnf087_tot_dr_amt;
	}
	/** 設定此次報送撥款金額 **/
	public void setLnf087_tot_dr_amt(BigDecimal value) {
		this.lnf087_tot_dr_amt = value;
	}

	/** 取得最近一次報送日期 **/
	public Date getLnf087_report_date() {
		return this.lnf087_report_date;
	}
	/** 設定最近一次報送日期 **/
	public void setLnf087_report_date(Date value) {
		this.lnf087_report_date = value;
	}

	/** 取得最近一次報送金額 **/
	public BigDecimal getLnf087_report_amt() {
		return this.lnf087_report_amt;
	}
	/** 設定最近一次報送金額 **/
	public void setLnf087_report_amt(BigDecimal value) {
		this.lnf087_report_amt = value;
	}

	/** 取得擔保品購價或時價 **/
	public BigDecimal getLnf087_app_amt() {
		return this.lnf087_app_amt;
	}
	/** 設定擔保品購價或時價 **/
	public void setLnf087_app_amt(BigDecimal value) {
		this.lnf087_app_amt = value;
	}

	/** 取得控管類別4之理由 **/
	public String getLnf087_plus_reason() {
		return this.lnf087_plus_reason;
	}
	/** 設定控管類別4之理由 **/
	public void setLnf087_plus_reason(String value) {
		this.lnf087_plus_reason = value;
	}

	/** 取得有無與其他額度共用擔保品 **/
	public String getLnf087_cocoll_fg() {
		return this.lnf087_cocoll_fg;
	}
	/** 設定有無與其他額度共用擔保品 **/
	public void setLnf087_cocoll_fg(String value) {
		this.lnf087_cocoll_fg = value;
	}

	/** 取得共用同一擔保品總額度金額 **/
	public BigDecimal getLnf087_sum_factamt() {
		return this.lnf087_sum_factamt;
	}
	/** 設定共用同一擔保品總額度金額 **/
	public void setLnf087_sum_factamt(BigDecimal value) {
		this.lnf087_sum_factamt = value;
	}

	/** 取得有無授權外核准得分批動用 **/
	public String getLnf087_part_fund() {
		return this.lnf087_part_fund;
	}
	/** 設定有無授權外核准得分批動用 **/
	public void setLnf087_part_fund(String value) {
		this.lnf087_part_fund = value;
	}

	/** 取得非屬央行填報對象理由 **/
	public String getLnf087_plus_memo() {
		return this.lnf087_plus_memo;
	}
	/** 設定非屬央行填報對象理由 **/
	public void setLnf087_plus_memo(String value) {
		this.lnf087_plus_memo = value;
	}

	/** 取得「住」或「住商」無營業註記 **/
	public String getLnf087_reg_purpose() {
		return this.lnf087_reg_purpose;
	}
	/** 設定「住」或「住商」無營業註記 **/
	public void setLnf087_reg_purpose(String value) {
		this.lnf087_reg_purpose = value;
	}

	/** 取得擔保品鑑價 **/
	public BigDecimal getLnf087_est_amt() {
		return this.lnf087_est_amt;
	}
	/** 設定擔保品鑑價 **/
	public void setLnf087_est_amt(BigDecimal value) {
		this.lnf087_est_amt = value;
	}

	/** 取得擔保品估價 **/
	public BigDecimal getLnf087_lawval() {
		return this.lnf087_lawval;
	}
	/** 設定擔保品估價 **/
	public void setLnf087_lawval(BigDecimal value) {
		this.lnf087_lawval = value;
	}

	/** 取得是否為受限戶Y/N **/
	public String getLnf087_restrict() {
		return this.lnf087_restrict;
	}
	/** 設定是否為受限戶Y/N **/
	public void setLnf087_restrict(String value) {
		this.lnf087_restrict = value;
	}

	/** 取得是否為高價住宅Y/N **/
	public String getLnf087_hp_house() {
		return this.lnf087_hp_house;
	}
	/** 設定是否為高價住宅Y/N **/
	public void setLnf087_hp_house(String value) {
		this.lnf087_hp_house = value;
	}

	/** 取得屬都市計畫劃定區域Y/N **/
	public String getLnf087_plan_area() {
		return this.lnf087_plan_area;
	}
	/** 設定屬都市計畫劃定區域Y/N **/
	public void setLnf087_plan_area(String value) {
		this.lnf087_plan_area = value;
	}

	/** 取得都市計畫使用分區 **/
	public String getLnf087_p_usetype() {
		return this.lnf087_p_usetype;
	}
	/** 設定都市計畫使用分區 **/
	public void setLnf087_p_usetype(String value) {
		this.lnf087_p_usetype = value;
	}

	/** 取得借款用途 **/
	public String getLnf087_p_loanuse() {
		return this.lnf087_p_loanuse;
	}
	/** 設定借款用途 **/
	public void setLnf087_p_loanuse(String value) {
		this.lnf087_p_loanuse = value;
	}

	/** 取得擔保品性質別 **/
	public String getLnf087_coll_char() {
		return this.lnf087_coll_char;
	}
	/** 設定擔保品性質別 **/
	public void setLnf087_coll_char(String value) {
		this.lnf087_coll_char = value;
	}

	/** 取得是否保留一成估值Y/N **/
	public String getLnf087_keep_lawval() {
		return this.lnf087_keep_lawval;
	}
	/** 設定是否保留一成估值Y/N **/
	public void setLnf087_keep_lawval(String value) {
		this.lnf087_keep_lawval = value;
	}

	/** 取得座落區段 **/
	public Integer getLnf087_site3no() {
		return this.lnf087_site3no;
	}
	/** 設定座落區段 **/
	public void setLnf087_site3no(Integer value) {
		this.lnf087_site3no = value;
	}

	/** 取得座落區村里 **/
	public String getLnf087_site4no() {
		return this.lnf087_site4no;
	}
	/** 設定座落區村里 **/
	public void setLnf087_site4no(String value) {
		this.lnf087_site4no = value;
	}

	/** 取得擔保品性質別其他之說明 **/
	public String getLnf087_coll_char_m() {
		return this.lnf087_coll_char_m;
	}
	/** 設定擔保品性質別其他之說明 **/
	public void setLnf087_coll_char_m(String value) {
		this.lnf087_coll_char_m = value;
	}
	
	/** 取得擔保品屋齡 **/
	public BigDecimal getLnf087_house_age() {
		return lnf087_house_age;
	}
	/** 設定擔保品屋齡 **/
	public void setLnf087_house_age(BigDecimal lnf087_house_age) {
		this.lnf087_house_age = lnf087_house_age;
	}
	
	/** 取得擔保品放款值 **/
	public BigDecimal getLnf087_loanAmt() {
		return lnf087_loanAmt;
	}
	/** 設定擔保品放款值 **/
	public void setLnf087_loanAmt(BigDecimal lnf087_loanAmt) {
		this.lnf087_loanAmt = lnf087_loanAmt;
	}
}
