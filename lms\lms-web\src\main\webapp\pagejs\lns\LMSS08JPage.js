var dfd1 = new $.Deferred();
var initS08jJson = {
	handlerName : null,
	// 設定handler名稱
	setHandler : function(){
		if(responseJSON.docURL == "/lms/lms1201m01"){
			// 授權外企金
			this.handlerName = "lms1201formhandler";
		}else if(responseJSON.docURL == "/lms/lms1101m01"){
			// 授權內企金
			this.handlerName = "lms1101formhandler";
		}else if(responseJSON.docURL == "/lms/lms1211m01"){
			// 授權外個金
			this.handlerName = "lms1211formhandler";
		}else if(responseJSON.docURL == "/lms/lms1111m01"){
			this.handlerName = "lms1111formhandler";
		}else{
			this.handlerName = "lms1301formhandler";
		}		
	},
	// 設定附加檔案內容
	fileSet : function(delFileId, fileGridId){
		// 刪除檔案按鈕
		$("#" + delFileId).click(function(){
			var select  = $("#" + fileGridId).getGridParam('selrow');		
			// confirmDelete=是否確定刪除?
			CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
				if(b){				
					var data = $("#" + fileGridId).getRowData(select);
					if(data.oid == "" || data.oid == undefined || data.oid == null){		
						// TMMDeleteError=請先選擇需修改(刪除)之資料列
						CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
						return;
					}				
					$.ajax({
						handler : (this.handlerName == null) ? "lms1201formhandler" : this.handlerName,
						type : "POST",
						dataType : "json",
						data : {
							formAction : "deleteUploadFile",
							fileOid:data.oid
						},
						success : function(obj) {
							$("#" + fileGridId).trigger("reloadGrid");
						}
					});
				}else{
					return ;
				}
			});
		});		
	},
	// 設定附加檔案Grid
	fileGrid : function(fileGridId, fieldId){
		// 檔案上傳grid
		$("#" + fileGridId).iGrid({
			handler : 'lms1201gridhandler',
			height : 100,
			sortname : 'srcFileName',
			postData : {
				formAction : "queryfileFromCES",
				fieldId:fieldId,
				mainId:responseJSON.mainId
			},
			rowNum : 15,
			caption: "&nbsp;",
			hiddengrid : false,
			// expandOnLoad : true, //只對subgrid有用
			// multiselect : true,
			colModel : [ {
				colHeader : i18n.lmss08a['L120S08J.srcFileName'],// 原始檔案名稱,
				name : 'srcFileName',
				width : 120,
				align: "left",
				sortable : false,
				formatter : 'click',
				onclick : openDoc
			}, {
				colHeader :  i18n.lmss08a['L120S08J.fileDesc'],// 檔案說明
				name : 'fileDesc',
				width : 140,
				sortable : false
			}, {
				colHeader : i18n.lmss08a['L120S08J.uploadTime'],// 上傳時間
				name : 'uploadTime',
				width : 140,
				sortable : false
			}, {
				name : 'oid',
				hidden : true
			}]
		});		
	}
};


 

$(document).ready(function() {
	
	setCloseConfirm(true);
	// 設定handler名稱
	initS08jJson.setHandler();
	
    var fileGridId = "lmss08j_gridfile";
    
	initS08jJson.fileSet("lmss08j_deleteFile", fileGridId);
	 
	// 設定附加檔案Grid
	initS08jJson.fileGrid(fileGridId, "CESForLMS_1,CESForLMS_2");
	
	//產生報表
	$("#lmss08j_generate").click(function(){
		lmss08j_generateReport(fileGridId);
	});
	  
});



function lmss08j_generateReport(fileGridId){

	var count = $("#"+fileGridId).jqGrid('getGridParam', 'records');
	if (count > 0) {
		//L120S08I.confirm1=執行前會刪除已存在之資料，是否確定執行？
		CommonAPI.confirmMessage(i18n.lmss08a["L120S08J.confirm1"], function(b){
            if (b) {
                //是的function
            	var select = $("#"+fileGridId).jqGrid('getRowData');
            	var data = [];
                for (var i in select) {
                    data.push( select[i].oid);
                }
                
                $.ajax({
                	handler : "lmscommonformhandler",
                    data: {
                        formAction: "deleteUploadFile",
                        realTime : "Y",
                        oids: data
                    },
                    success: function(obj){
                    	$("#"+fileGridId).trigger("reloadGrid");
                        $.ajax({
                            handler: "lms1201docservice",
                            action: "saveCreatDocFromCES",
                            data: {
                            	mainId: responseJSON.mainId,
                                docTempType: "1and2"
                            },
                            success: function(obj){
                            	$("#"+fileGridId).trigger("reloadGrid");
                            	if (obj.errorMessage) {
                                    CommonAPI.showErrorMessage(obj.errorMessage);
                                }
                            }
                        });		
                    }
                });
                
                
            }
        });
	} else{
		$("#"+fileGridId).trigger("reloadGrid");
        $.ajax({
            handler: "lms1201docservice",
            action: "saveCreatDocFromCES",
            data: {
            	mainId: responseJSON.mainId,
                docTempType: "1and2"
            },
            success: function(obj){
            	$("#"+fileGridId).trigger("reloadGrid");
            	if (obj.errorMessage) {
                    CommonAPI.showErrorMessage(obj.errorMessage);
                }
            }
        });		
	}

}

function openDoc(cellvalue, options, rowObject){
    $.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:rowObject.oid
        }
    });
}