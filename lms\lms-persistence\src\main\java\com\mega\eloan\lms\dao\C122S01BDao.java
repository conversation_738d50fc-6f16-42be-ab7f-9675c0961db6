/* 
 * C122S01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C122S01B;

/** 線上增貸核貸批號明細 **/
public interface C122S01BDao extends IGenericDao<C122S01B> {

	C122S01B findByOid(String oid);
	
	List<C122S01B> findByMainIdBatchNo(String mainId, Integer batchNo);
	
	C122S01B findByUniqueKey(String mainId, Integer batchNo, String cntrNoMainId, Integer seq);

	C122S01B findByMainId(String mainId);
}