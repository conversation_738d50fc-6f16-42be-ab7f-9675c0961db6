package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

public interface MisELF338Service {

	public Map<String, Object> findELF338N_CRDT(String custId, String dupNo);
	public List<?> findELF338L120s01c1(String custId, String dupNo,String countryType);

	public List<?> findELF338L120s01c2(String custId, String dupNo);
	
	public List<?> findELF338L120s01c3(String custId, String dupNo, String brno);
}
