package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.GenericBean;

/**
 * <pre>
 * L201S99A model.
 * </pre>
 * 
 * @since 2012/7/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/16,Tammy<PERSON><PERSON>,new
 *          </ul>
 */
@Entity
@Table(name = "L201S99A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L201S99A extends GenericBean implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	/** 主文件編號 */
	@Column(length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** UID */
	@Column(length = 32, columnDefinition = "CHAR(32)")
	private String uid;

	@Temporal(TemporalType.DATE)
	private Date qryDate;

	private Timestamp creDate;
	
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String sender;

	private Timestamp sendTime;

	@Column(length = 1)
	private String exeResult;

	@Column(length = 120)
	private String exeMsg;

	// bi-directional many-to-one association to D201S99B
	@ToStringExclude
	@OneToMany(mappedBy = "l201s99a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<L201S99B> l201s99bs;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

	public Date getQryDate() {
		return qryDate;
	}

	public void setQryDate(Date qryDate) {
		this.qryDate = qryDate;
	}

	public Timestamp getCreDate() {
		return creDate;
	}

	public void setCreDate(Timestamp creDate) {
		this.creDate = creDate;
	}

	public String getSender() {
		return sender;
	}

	public void setSender(String sender) {
		this.sender = sender;
	}

	public Timestamp getSendTime() {
		return sendTime;
	}

	public void setSendTime(Timestamp sendTime) {
		this.sendTime = sendTime;
	}

	public String getExeResult() {
		return exeResult;
	}

	public void setExeResult(String exeResult) {
		this.exeResult = exeResult;
	}

	public String getExeMsg() {
		return exeMsg;
	}

	public void setExeMsg(String exeMsg) {
		this.exeMsg = exeMsg;
	}

	public List<L201S99B> getL201s99bs() {
		return l201s99bs;
	}

	public void setL201s99bs(List<L201S99B> l201s99bs) {
		this.l201s99bs = l201s99bs;
	}

}