---------------------------------------------------------
-- LMS.C900M01B 產品種類對應表
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.C900M01B;
CREATE TABLE LMS.C900M01B (
	OID           CHAR(32)     ,
	PRODKIND      VARCHAR(2)    not null,
	SUBJCODE      VARCHAR(8)    not null,
	RINTWAY       VARCHAR(36)  ,
	ISCANCEL      CHAR(1)      ,
	CREATO<PERSON>       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDA<PERSON><PERSON><PERSON>    TIMESTAMP    ,

	constraint P_C900M01B PRIMARY KEY(PRODKIND, SUBJCODE)
) in EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC900M01B01;
--CREATE UNIQUE INDEX LMS.XC900M01B01 ON LMS.C900M01B   (PRODKIND, SUBJCODE);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C900M01B IS '產品種類對應表';
COMMENT ON LMS.C900M01B (
	OID           IS 'oid', 
	PRODKIND      IS '產品代號', 
	SUBJCODE      IS '會計科子細目', 
	RINTWAY       IS '計息方式', 
	ISCANCEL      IS '是否取消', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
