/* 
 * LMS9541GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.grid;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.model.C820M01A;
import com.mega.eloan.lms.rpt.service.CLS9501V01Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 收集批覆書額度資訊
 * </pre>
 * 
 * @since 2013/01/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/23,Vector,new
 *          </ul>
 */
@Scope("request")
@Controller("cls9501v01gridhandler")
public class CLS9501V01GridHandler extends AbstractGridHandler {

	@Resource
	BranchService branch;
	
	@Resource
	CLS9501V01Service service;
	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryBranch(ISearch pageSetting,
			PageParameters params){
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String area = params.getString("area");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "groupId", area);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "creator", user.getUserId());
		Page page = service.findPage(C820M01A.class, pageSetting);
		if(page.getTotalRow()==0){//無資料=>新增後顯示
			if (Util.isNotEmpty(area)){
				List<IBranch> branchs = branch.getBranchOfGroup(area);
				String mainId = IDGenerator.getUUID();
				for(int i=0 ; i<branchs.size() ; i++){
					IBranch pivot = branchs.get(i);
					C820M01A record = new C820M01A();
					record.setBrName(pivot.getBrName());
					record.setBrno(pivot.getBrNo());
					record.setGroupId(area);
					record.setMainId(mainId);
					record.setIsSelected("N");
					service.save(record);
				}
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
				page = service.findPage(C820M01A.class, pageSetting);
			}
		}
		//有資料=>顯示舊資料
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}
}
