/* 
 * L120S11ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S11A_LOC;

/** 本案借款人同時為其他授信戶應收帳款債務人之當地往來額度資料 **/
public interface L120S11A_LOCDao extends IGenericDao<L120S11A_LOC> {

	L120S11A_LOC findByOid(String oid);

	List<L120S11A_LOC> findByMainId(String mainId);

	L120S11A_LOC findByUniqueKey(String mainId, String custId, String dupNo,
			String custId2, String dupNo2);

	L120S11A_LOC findByMainIdCustIdAndCustId2(String mainId, String custId,
			String dupNo, String custId2, String dupNo2);

	List<L120S11A_LOC> findByMainIdAndCustId(String mainId, String custId,
			String dupNo);

	List<L120S11A_LOC> findByMainIdCustIdAndRelType(String mainId, String custId,
			String dupNo, Integer relType);

	L120S11A_LOC findByMainIdCustIdItemSeq(String mainId, String custId,
			String dupNo, Integer itemSeq);

	List<L120S11A_LOC> findByMainIdCustId2(String mainId, String custId2,
			String dupNo2);

}