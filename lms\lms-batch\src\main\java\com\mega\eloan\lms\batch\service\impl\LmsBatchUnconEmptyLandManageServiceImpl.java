package com.mega.eloan.lms.batch.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.batch.service.LmsBatchCommonService;
import com.mega.eloan.lms.mfaloan.bean.ELF602;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;

/**
 * <pre>
 *  SLMS-00014 企金個人基本資料補行業別(含海外個人戶)
 * </pre>
 * 
 * @since 2022/10/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2022/10/12,010173,new
 *          </ul>
 */
@Service("lmsBatchUnconEmptyLandManageServiceImpl")
public class LmsBatchUnconEmptyLandManageServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory.getLogger(LmsBatchUnconEmptyLandManageServiceImpl.class);
	
	private static final String NOTIFICATION_CONTENT = "本案為央行購地或本行空地貸款案, 原「預計動工日」將於本月到期, 請查明是否已興建動工, 並填寫「實際動工日」資訊, 若有變更興建計畫情事者, 請另以變更條件重新簽報方式辦理。";

	@Resource
	LmsBatchCommonService lmsBatchCommonService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {

		DateFormat dateformat = new SimpleDateFormat("yyyyMMddHHmmss");
		
		JSONObject result = new JSONObject();
		String cntrno = null;
		String custId = null;
		String dupNo = null;
		String branchNo = null;
		try {
			
			List<Map<String, Object>> list = this.misdbBASEService.getUnconEmptyLandDataByActStDateAndUncancelled();
			for(Map<String, Object> m : list){

				//insert into elf602
				cntrno = String.valueOf(m.get("ELF600_CONTRACT"));
				custId = String.valueOf(m.get("ELF600_CUST_ID"));
				dupNo = String.valueOf(m.get("ELF600_CUST_DUP"));
				branchNo = String.valueOf(m.get("LNF020_LN_BR_NO"));
				
				ELF602 elf602 = new ELF602();
				elf602.setElf602_unid(cntrno + "-" + dateformat.format(new Date()));
				elf602.setElf602_cntrno(cntrno);
				elf602.setElf602_case_mark("04");
				elf602.setElf602_fo_content(NOTIFICATION_CONTENT);//追蹤事項通知內容
				elf602.setElf602_fo_date(new java.sql.Date(new Date().getTime()));//追蹤事項通知日期
				elf602.setElf602_status("1"); // 狀態：1-未辦理
				elf602.setElf602_fo_kind("8");// 類別 : 其他
				elf602.setElf602_custid(custId);
				elf602.setElf602_dupno(dupNo);
				elf602.setElf602_br_no(branchNo);
				elf602.setElf602_loan_no("");
				elf602.setElf602_loan_kind("LN");
				elf602.setElf602_conform_fg("");
				elf602.setElf602_staff("01");
				elf602.setElf602_unusual_fg("N");
				elf602.setElf602_upd_teller("");
				elf602.setElf602_upd_supvno("");
				misdbBASEService.insertELF602(elf602);
			}
			
			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE, "lmsBatchUnconEmptyLandManageServiceImpl 執行成功！");

		} catch (Exception ex) {
			LOGGER.error(StrUtils.getStackTrace(ex));
			result = WebBatchCode.RC_ERROR;
			result.element(
					WebBatchCode.P_RESPONSE, "lmsBatchUnconEmptyLandManageServiceImpl 執行失敗！==>" + "cntrno:" + cntrno + "-custId:" + custId + "-dupNo:" + dupNo + "-branchNo:" + branchNo + "-" + ex.getLocalizedMessage());

		}

		return result;
	}

}
