var initDfd = initDfd || new $.Deferred();
var initAll = initAll || new $.Deferred();

initDfd.done(function(json){
	var initControl_lockDoc = json['initControl_lockDoc'];
	
	var gridL170M01B = $("#gridL170M01B").iGrid({
		handler : 'lms1700gridhandler',
		rownumbers: true,
		height : 180, // 設定高度
		postData : {
			mainOid: json.mainOid,
            formAction: "queryL170M01B",
            'flag':'ALL'	
		},
		needPager: false,
		colModel : [ 
           {name : 'oid', hidden : true}
         , {//額度序號
			colHeader :i18n.lms1700m01["L170M01B.cntrNo"], name : 'cntrNo', align : "left", width : 120
			, formatter: 'click', onclick: openDoc
		}, {//科目代碼
			colHeader :i18n.lms1700m01["label.L170M01B_loanTP"], name : 'loanTP', align : "left", width : 60
		}, {//科目
			colHeader :i18n.lms1700m01["label.L170M01B_subject"], name : 'subject', align : "left", width : 120
		}, {//
			colHeader : ' ', name : 'quotaCurr', align : "left", width : 30
		}, {//額度(仟元)
			colHeader :i18n.lms1700m01["label.L170M01B_quotaAmt"], name : 'quotaAmt', align : "right", width : 100
		}, {//
			colHeader :' ', name : 'balCurr', align : "left", width : 30
		}, {//前日餘額(仟元)
			colHeader :i18n.lms1700m01["label.L170M01B_balAmt"], name : 'balAmt', align : "right", width : 100
		}, {//循環
			colHeader :i18n.lms1700m01["label.L170M01B_revolve"], name : 'revolve', align : "center", width : 40         
		}, {//新貸/舊案
			colHeader : ' ', name : 'newCase', align : "left", width : 40
		}, {//系統產生/人工新增
			colHeader :' ',name : 'createBy', align : "left", width : 40, sortable:false
        }
      ]
	});
	
	var gridL170M01B_PEO = $("#gridL170M01B_PEO").iGrid({
		handler : 'lms1700gridhandler',
		rownumbers: true,
		height : 120, // 設定高度
		postData : {
			mainOid: json.mainOid,
            formAction: "queryL170M01B",
            'flag':'PEO'	
		},
		needPager: false,
		multiselect: true,
		colModel : [ 
           {name : 'oid', hidden : true}
         , {//額度序號
			colHeader :i18n.lms1700m01["L170M01B.cntrNo"], name : 'cntrNo', align : "left", width : 120
			, formatter: 'click', onclick: openDoc
		}, {//科目代碼
			colHeader :i18n.lms1700m01["label.L170M01B_loanTP"], name : 'loanTP', align : "left", width : 60
		}, {//科目
			colHeader :i18n.lms1700m01["label.L170M01B_subject"], name : 'subject', align : "left", width : 120
		}, {//
			colHeader : ' ', name : 'quotaCurr', align : "left", width : 30
		}, {//額度(仟元)
			colHeader :i18n.lms1700m01["label.L170M01B_quotaAmt"], name : 'quotaAmt', align : "right", width : 100
		}, {//
			colHeader :' ', name : 'balCurr', align : "left", width : 30
		}, {//前日餘額(仟元)
			colHeader :i18n.lms1700m01["label.L170M01B_balAmt"], name : 'balAmt', align : "right", width : 100
		}, {//循環
			colHeader :i18n.lms1700m01["label.L170M01B_revolve"], name : 'revolve', align : "center", width : 40         
		}, {//新貸/舊案
			colHeader : ' ', name : 'newCase', align : "left", width : 40
		}, {//系統產生/人工新增
			colHeader :' ',name : 'createBy', align : "left", width : 40, sortable:false
        }
      ]
	});
	
	//匯入前次覆審意見資料
	$("#btn_importBefText").click(function(){
		if(json.bef_oid.length==0){
			API.showMessage(i18n.lms1700m01["ui_lms1700.msg24"]);//於 Web e-Loan 未查到前次覆審報告表
		}else{
			CommonAPI.confirmMessage(i18n.lms1700m01["ui_lms1700.msg25"], function(b){
	            if (b) {
	            	 if(true){
	            		 $.ajax({
					           handler: _handler, type: "POST", dataType: "json",
					           data: {
					                formAction: "importBefText",
					                mainOid: $("#mainOid").val()
					           },
					     }).done(function(){
							API.showMessage(i18n.def.runSuccess);
						 }) 
	            	 }	
	            }
	        });
		}
	});
	
	//調閱覆審報告
	$("#btn_printBefReviewDoc").click(function(){
		if(json.bef_oid.length==0){
			API.showMessage(i18n.lms1700m01["ui_lms1700.msg24"]);//於 Web e-Loan 未查到前次覆審報告表
		}else{
			printL170M01A( json.bef_oid );	
		}
		
	});
	
	function openDoc(cellvalue, options, rowObject){
		openBy_l170m01b_oid(rowObject.oid);
	}
	function openBy_l170m01b_oid(l170m01b_oid){
		var _id = "_divId_LNDetail";
		if ($("#"+_id).length == 0){
			$('body').append("<div id='"+_id+"' style='display:none;'></div>");		
		}
		$("#"+_id).empty();//clear
		
		var param = {'l170m01b_oid':l170m01b_oid};
		$("#"+_id).load("../lms1700m02", param,  function() {
			//隱碼headerarea
			$("#"+_id).find("#headerarea").hide();
			$("#"+_id).find(".body").css("width", "850px");
			
			if(initControl_lockDoc){
				$("#"+_id).find("#lnForm").lockDoc();
				$("#buttonPanelLNDetail").find("#btnSaveLNDetail").addClass(" ui-state-disabled ").attr("disabled", "true");
			}
			
			$.ajax({
		        handler: _handler,
		        data: $.extend({}, param, {
                    formAction: "queryLNDetail"
                    , mainOid: $("#mainOid").val()
                }),
			}).done(function(json_queryLNDetail){
				$("#"+_id).find("#lnForm").injectData(json_queryLNDetail);
				var pagesize = tb_getPageSize();
				var x = pagesize[0] - 60;
				if(x>900){
					x = 900;
				}
				var y = pagesize[1] - 40;
				$("#"+_id).thickbox({
				    title: '', width: x, height: y, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
				    buttons: {}
				});
			});
		});      
    }
	//================	
	$("#btn_insertPEO").click(function(){
		openBy_l170m01b_oid('');
	});
	
	$("#btn_deletePEO").click(function(){
		gridL170M01B_PEO.trigger("reloadGrid");
		
		$("#div_gridL170M01B_PEO").thickbox({ // 使用選取的內容進行彈窗
	        title: '',
	        width: 800, height: 260, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
            buttons: {
                "sure": function(){
                	var rowId_arr = gridL170M01B_PEO.getGridParam('selarrrow');	
            		var oid_arr = [];
               	 	for (var i = 0; i < rowId_arr.length; i++) {
            			var data = gridL170M01B_PEO.getRowData(rowId_arr[i]);
            			oid_arr.push(data.oid);    			
                    }
	               	if(oid_arr.length==0){
	               		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
	               		return;
	               	}
	               	$.ajax({
	        	        handler: _handler,
	        	        data: {
	        	        	formAction: "deleteLN_PEO"
	        	            , mainOid: $("#mainOid").val()
	        	            , 'oids': oid_arr.join("|")
	        	        },
					}).done(function(json_deleteLN_PEO){
						$("#tabForm").injectData(json_deleteLN_PEO);
						
						gridL170M01B.trigger("reloadGrid");//更新Grid內容	

						$.thickbox.close();

						API.showMessage(i18n.def.runSuccess);
					});
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	});

	$("#btn_importLnData").click(function(){
		$.ajax({
	        handler: _handler,
	        data: {
	        	formAction: "importLNSingle",
	            mainOid: $("#mainOid").val()
	        },
		}).done(function(json_importLNSingle){
			$("#tabForm").injectData(json_importLNSingle);

			gridL170M01B.trigger("reloadGrid");//更新Grid內容	

			API.showMessage(i18n.def.runSuccess);
		})
	});

	$("#btn_delteAllLnData").click(function(){
		$.ajax({
			handler: _handler,
	        data: {
	        	formAction: "deleteLN",
	            mainOid: $("#mainOid").val()
	        },
		}).done(function(json_deleteLN){
			$("#tabForm").injectData(json_deleteLN);
			
			gridL170M01B.trigger("reloadGrid");//更新Grid內容	

			API.showMessage(i18n.def.runSuccess);
		});
	});	
	//================
});
