/* 
 *CLS1141Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.kordamp.json.JSONObject;

import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.BRelated;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01A;
import com.mega.eloan.lms.model.C101S01B;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C120S01T;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C127M01A;
import com.mega.eloan.lms.model.C140JSON;
import com.mega.eloan.lms.model.C140M01A;
import com.mega.eloan.lms.model.C140M07A;
import com.mega.eloan.lms.model.C140S04A;
import com.mega.eloan.lms.model.C140S04B;
import com.mega.eloan.lms.model.C140S04C;
import com.mega.eloan.lms.model.C140S07A;
import com.mega.eloan.lms.model.C140S09A;
import com.mega.eloan.lms.model.C140S09B;
import com.mega.eloan.lms.model.C140S09C;
import com.mega.eloan.lms.model.C140S09D;
import com.mega.eloan.lms.model.C140S09E;
import com.mega.eloan.lms.model.C140S09F;
import com.mega.eloan.lms.model.C140SDSC;
import com.mega.eloan.lms.model.C140SFFF;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01D;
import com.mega.eloan.lms.model.L120M01E;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L120M01H;
import com.mega.eloan.lms.model.L120S03A;
import com.mega.eloan.lms.model.L120S05A;
import com.mega.eloan.lms.model.L120S05C;
import com.mega.eloan.lms.model.L120S06A;
import com.mega.eloan.lms.model.L120S06B;
import com.mega.eloan.lms.model.L120S07A;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L121M01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140M01R;
import com.mega.eloan.lms.model.L140M04A;
import com.mega.eloan.lms.model.L140MC1A;
import com.mega.eloan.lms.model.L140MC2A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L730A01A;
import com.mega.eloan.lms.model.L730M01A;
import com.mega.eloan.lms.model.L730S01A;
import com.mega.eloan.lms.model.L800M01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;

/**
 * <pre>
 * 授信簽報書 Service
 * </pre>
 * 
 * @since 2012/12/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/5,REX,new
 *          </ul>
 */
public interface CLS1141Service extends AbstractService {

	/**
	 * 儲存所有model
	 * 
	 * @param list
	 *            modelList
	 */
	void saveByGenericBeanList(List<GenericBean> list);

	/**
	 * 查詢model by mainId
	 * 
	 * @param <T>
	 * @param clazz
	 * @param mainId
	 *            文件編號
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public <T extends GenericBean> T findModelByManId(Class clazz, String mainId);

	/**
	 * 查詢借款人
	 * 
	 * @param mainId
	 *            文件編號
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	public C120M01A findc120m01aByUniqueKey(String mainId, String custId,
			String dupNo);

	/**
	 * 儲存借款人陣列
	 * 
	 * @param c120m01as
	 *            借款人陣列
	 * @param bean
	 *            model
	 */
	void saveC120M01As(List<C120M01A> c120m01as, GenericBean... bean);

	/**
	 * 徵信報告引用文件儲存
	 * 
	 * @param brelateds
	 *            BRelated
	 */
	void saveBRelated(List<BRelated> brelateds);

	/**
	 * 利用oid 找到授信簽報書主檔
	 * 
	 * @param oid
	 * @return
	 */
	L120M01A findL120m01aByOid(String oid);

	/**
	 * 利用mainId 找到所有授信簽報書主檔
	 * 
	 * @param oid
	 * @return
	 */
	L120M01A findL120m01aByMainId(String mainId);

	/**
	 * 利用獨特Key 找到簽報書敘述說明檔
	 * 
	 * @param mainId
	 * @param itemType
	 * @return
	 */
	L120M01D findL120m01dByUniqueKey(String mainId, String itemType);

	/**
	 * 取得徵信報告書 綜合評估及敘做理由
	 * 
	 * @param cesMainId
	 * @return
	 */
	String findFfbody(String cesMainId);

	/**
	 * 取得徵信報告書 其他內容
	 * 
	 * @param cesMainId
	 * @param custName
	 * @return
	 */
	Map<String, String> findOther(String cesMainId, String custName);

	/**
	 * 儲存相關文件資料群組
	 * 
	 * @param list
	 */
	void saveListL120m01e(List<L120M01E> list);

	/**
	 * 刪除相關文件資料群組
	 * 
	 * @param list
	 */
	void delListL120m01e(List<L120M01E> list);

	/**
	 * 依照特定條件找出案件改分派已存在的簽章欄檔
	 * @param mainId
	 * @param branchType
	 * @param branchId
	 * @param staffJob
	 * @return
	 */
	List<L120M01F> findToSaveHq(String mainId, String branchType,
			String branchId, String staffJob);	
	
	/**
	 * 利用獨特Key取得案件簽章欄檔
	 * 
	 * @param mainId
	 * @param branchType
	 * @param branchId
	 * @param staffNo
	 * @param staffJob
	 * @return
	 */
	L120M01F findL120m01fByUniqueKey(String mainId, String branchType,
			String branchId, String staffNo, String staffJob);

	/**
	 * 刪除案件簽章欄檔群組
	 * 
	 * @param list
	 */
	void delListL120m01f(List<L120M01F> list);
	
	/**
	 * 儲存案件簽章欄檔群組
	 * 
	 * @param list
	 */
	void saveListL120m01f(List<L120M01F> list);

	/**
	 * 利用獨特Key取得授審會／催收會會議決議檔
	 * 
	 * @param mainId
	 * @param meetingType
	 * @return
	 */
	L120M01H findL120m01hByUniqueKey(String mainId, String meetingType);

	/**
	 * 查詢利害關係人授信條件對照表
	 * 
	 * @param allCustId
	 * @param search
	 * @return
	 */
	Page<Map<String, Object>> getLihai(String allCustId, String caseBrid,
			ISearch search);
	/**
	 * 查找最近一年簽准的(非利害關係人)授信案件
	 * 
	 * @param prodKind
	 * @param subj
	 * @param lnPurs
	 * @param search
	 * @return Page<Map<String, Object>>
	 */
	Page<Map<String, Object>> queryList_for_cls_l120s06b_type2_orderByRate(String prodKind, String subj, String lnPurs, ISearch search);
	
	/**
	 * 透過JDBC從徵信報告取得MainId(資信簡表用)
	 * 
	 * @param caseBrId
	 *            案件號碼-分行
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            客戶重覆序號
	 * @param search
	 *            ISearch
	 * @return Page<Map<String, Object>>
	 */
	Page<Map<String, Object>> getCesMainId1(String caseBrId, String custId,
			String dupNo, ISearch search);

	/**
	 * 透過JDBC從徵信報告依照使用者輸入統編取得MainId(徵信報告用)
	 * 
	 * @param caseBrId
	 *            案件號碼-分行
	 * @param custId
	 *            使用者輸入統編
	 * @param search
	 *            ISearch
	 * @return Page<Map<String, Object>>
	 */
	Page<Map<String, Object>> getCesMainId(String caseBrId, String custId,
			ISearch search);

	/**
	 * 透過JDBC從徵信報告取得MainId(徵信報告用)
	 * 
	 * @param caseBrId
	 *            案件號碼-分行
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            客戶重覆序號
	 * @param search
	 *            ISearch
	 * @return Page<Map<String, Object>>
	 */
	Page<Map<String, Object>> getCesMainId2(String caseBrId, String custId,
			String dupNo, ISearch search);

	/**
	 * 透過JDBC從徵信報告取得MainId(範圍)(徵信報告用)
	 * 
	 * @param caseBrId
	 *            案件號碼-分行
	 * @param mainId1
	 *            授信簽報書文件編號
	 * @param mainId2
	 *            授信簽報書文件編號
	 * @param search
	 *            ISearch
	 * @return Page<Map<String, Object>>
	 */
	Page<Map<String, Object>> getCesMainId2s(String caseBrId, String mainId1,
			String mainId2, ISearch search);


	/**
	 * 透過JDBC從徵信報告取得MainId(範圍)(徵信報告用，不限制文件狀態)
	 * 
	 * @param caseBrId
	 *            案件號碼-分行
	 * @param search
	 *            ISearch
	 * @return Page<Map<String, Object>>
	 */
	Page<Map<String, Object>> getCesMainId2ss(String caseBrId, ISearch search);

	/**
	 * 取得徵信報告書 使用者輸入之Id 下的 文件編號(資信簡表用)
	 * 
	 * @param caseBrId
	 * @param custId
	 * @param search
	 * @return
	 */
	Page<Map<String, Object>> getCesMainIda(String caseBrId, String mainId1,
			String mainId2, ISearch search);

	/**
	 * 取得徵信報告書 使用者輸入之Id 下的 文件編號(資信簡表用)
	 * 
	 * @param caseBrId
	 * @param custId
	 * @param search
	 * @return
	 */
	public Page<Map<String, Object>> getCesMainIdb(String caseBrId,
			String custId, ISearch search);

	/**
	 * 取得徵信 主要借款人Id群組 下的 文件編號(範圍)(資信簡表用，不限制文件狀態)
	 * 
	 * @param caseBrId
	 * @param search
	 * @return
	 */
	Page<Map<String, Object>> getCesMainIdc(String caseBrId, ISearch search);

	/**
	 * 從徵信報告取得主要營業項目
	 * 
	 * @param cesMainId
	 *            徵信文件編號
	 * @return 主要營業項目內容
	 */
	String findBusi(String cesMainId);


	/**
	 * 利用獨特Key取得資本適足率影響數資料檔
	 * 
	 * @param mainId
	 * @param cntrMainId
	 * @param cntrNo
	 * @return
	 */
	public L120S03A findL120s03aByUniqueKey(String mainId, String cntrMainId,
			String cntrNo);

	/**
	 * 儲存群組資本適足率影響數資料檔
	 * 
	 * @param list
	 */
	void saveL120s03aList(List<L120S03A> list);

	/**
	 * 儲存資本適足率影響數資料檔
	 * 
	 * @param model
	 */
	void saveL120s03a(L120S03A model);

	/**
	 * 刪除群組資本適足率影響數資料檔
	 * 
	 * @param list
	 */
	void deleteListL120s03a(List<L120S03A> list);


	/**
	 * 利用MainId取得所有借款人集團相關資料檔
	 * 
	 * @param mainId
	 * @return
	 */
	L120S05A findL120s05aByMainId(String mainId);


	/**
	 * 利用MainId取得所有借款人關係企業相關資料檔
	 * 
	 * @param mainId
	 * @return
	 */
	L120S05C findL120s05cByMainId(String mainId);

	/**
	 * 利用MainId取得所有利害關係人授信條件對照表主檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S06A> findL120s06aByMainId(String mainId);
	
	/**
	 * 利用MainId取得所有利害關係人授信條件對照表主檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S06A> findL120s06aByMainIdOrderPrintMode(String mainId);

	/**
	 * 刪除群組利害關係人授信條件對照表主檔
	 * 
	 * @param model
	 */
	public void deleteListL120s06a(List<L120S06A> model);

	/**
	 * 儲存利害關係人授信條件對照表主檔及利害關係人授信條件對照表明細檔
	 * 
	 * @param listL120s06a
	 * @param listL120s06b
	 */
	public void saveListL120s06a(List<L120S06A> listL120s06a,
			List<L120S06B> listL120s06b);

	/**
	 * 利用Oid群組刪除利害關係人授信條件對照表主檔
	 * 
	 * @param oidArray
	 */
	public void deleteListL120s06a(String[] oidArray);

	/**
	 * 利用MainId取得所有利害關係人授信條件對照表明細檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S06B> findL120s06bByMainId(String mainId);
	
	/**
	 * 刪除群組利害關係人授信條件對照表明細檔
	 * 
	 * @param model
	 */
	public void deleteListL120s06b(List<L120S06B> model);

	/**
	 * 利用MainId取得土建融案檢視清單主檔
	 * 
	 * @param mainId
	 * @return
	 */
	public L120S07A findL120s07aByUniqueKey(String mainId);

	/**
	 * 利用獨特Key取得授信報案考核表授權檔
	 * 
	 * @param mainId
	 * @param ownUnit
	 * @param authType
	 * @param authUnit
	 * @return
	 */
	L730A01A findL730a01aByUniqueKey(String mainId, String ownUnit,
			String authType, String authUnit);

	/**
	 * 利用MainId取得授信報案考核表主檔
	 * 
	 * @param mainId
	 * @return
	 */
	L730M01A findL730m01aByMainId(String mainId);

	/**
	 * 刪除授信報案考核表明細檔群組
	 * 
	 * @param list
	 */
	void deleteListL730s01a(List<L730S01A> list);

	/**
	 * 儲存授信報案考核表明細檔群組
	 * 
	 * @param list
	 */
	void saveListL730s01a(List<L730S01A> list);

	/**
	 * 依照登入分行取得主管常用名單
	 * 
	 * @return
	 */
	List<L800M01A> findL800m01aByBrno();

	/**
	 * 取得徵信報告書 相關文件之徵信報告書
	 * 
	 * @param cesMainId
	 *            徵信文件編號
	 * @return String
	 */
	public Map<String, String> findCesCustName(String cesMainId);

	/**
	 * 取得徵信報告書 相關文件之資信簡表
	 * 
	 * @param cesMainId
	 *            徵信文件編號
	 * @return
	 */
	public Map<String, String> findCesCustName2(String cesMainId);

	/**
	 * 取得grid頁面所需資料
	 * 
	 * @param mainId
	 *            mainId
	 * @param printCondition
	 *            printCondition
	 * @param search
	 *            search
	 * @return Page<Map<String, Object>>
	 * @throws CapException
	 *             CapException
	 */
	Page<Map<String, Object>> getBorrows(String mainId, String printCondition,
			ISearch search) throws CapException;

	/**
	 * 根據oid陣列尋找L120M01A List
	 * 
	 * @param oids
	 * @return List<L120M01A>
	 */
	List<L120M01A> findL120m01asByOids(String[] oids);

	/**
	 * 儲存 List<L120M01A>
	 * 
	 * @param l120m01as
	 */
	public void saveL120m01as(List<L120M01A> l120m01as);

	/**
	 * 儲存l120m01a和l120m01d List
	 * 
	 * @param list1
	 * @param list2
	 */
	public void saveL120m01aAndL120m01d(List<L120M01A> list1,
			List<L120M01D> list2);

	/**
	 * 取得第柒章主檔(財務分析)
	 * 
	 * @param mainId
	 *            String
	 * @param pid
	 *            m01a.uid
	 * @param tab
	 *            tab
	 * @param subtab
	 *            subTab
	 * @return C140M07A
	 */
	C140M07A getC140M07A(String mainId, String pid, String tab, String subtab);


	/**
	 * 取得徵信報告主檔
	 * 
	 * @param oid
	 *            String
	 * @return C140M01A
	 */
	C140M01A getC140M01A(String oid);

	/**
	 * 使用MainId查詢C140M01A
	 * 
	 * @param MainId
	 *            String
	 * @return C140M01A
	 */
	C140M01A getC140M01AByMainId(String MainId);

	/**
	 * 取得JSON內容
	 * 
	 * @param uid
	 *            String
	 * @param mainId
	 *            String
	 * @param tab
	 *            String
	 * @param subTab
	 *            String
	 * @return C140JSON
	 */
	C140JSON getC140JSONByMainPidTab(String uid, String mainId, String tab,
			String subTab);

	/**
	 * 取得財報資料
	 * 
	 * @param m01a
	 *            C140M01A
	 * @param tab
	 *            頁籤
	 * @param subtab
	 *            子頁籤
	 * @return List<C120S01C>
	 */
	List<C140S07A> getS07aByMetaAndTab(C140M01A m01a, String tab, String subtab);

	List<C140S07A> getS07aBySubDocAndTab(String mainId, String uid, String tab,
			String subtab);

	List<C140S07A> getS07aByM07aAndTab(C140M07A m07a);

	/**
	 * 取得徵信報告自由格式
	 * 
	 * @param mainId
	 *            String
	 * @param pid
	 *            String
	 * @param fieldId
	 *            String
	 * @param tab
	 *            tab
	 * @return C140SFFF
	 */
	C140SFFF getC140SFFF(String mainId, String pid, String fieldId, String tab);

	/**
	 * 取得徵信報告自由格式
	 * 
	 * @param mainId
	 *            String
	 * @param pid
	 *            String
	 * @param tab
	 *            tab
	 * @return List<C140SFFF>
	 */
	List<C140SFFF> getC140SFFF(String mainId, String pid, String tab);

	/**
	 * 取得徵信報告附加檔案
	 * 
	 * @param oid
	 *            String
	 * @return DocFile
	 */
	DocFile getDocFile(String oid);

	/**
	 * 取得JSON內容
	 * 
	 * @param uid
	 *            String
	 * @param mainId
	 *            String
	 * @param tab
	 *            String
	 * @return List<C140JSON>
	 */
	List<C140JSON> getC140JSONByMainPidTab(String uid, String mainId, String tab);

	/**
	 * 取得註記說明內容
	 * 
	 * @param uid
	 *            String
	 * @param mainId
	 *            String
	 * @param tab
	 *            String
	 * @param fieldId
	 *            String
	 * @return C140SDSC
	 */
	C140SDSC getC140SDSCByMainPidTab(String uid, String mainId, String tab,
			String fieldId);

	/**
	 * 取得註記說明內容
	 * 
	 * @param uid
	 *            String
	 * @param mainId
	 *            String
	 * @param tab
	 *            String
	 * @return List<C140SDSC>
	 */
	List<C140SDSC> getC140SDSCByMainPidTab(String uid, String mainId, String tab);

	/**
	 * 儲存徵信報告主檔及附加文件
	 * 
	 * @param c140m01a
	 *            C140M01A
	 * @param docFiles
	 *            List<DocFile>
	 */
	void saveC140m01aAndDocFiles(C140M01A c140m01a, List<DocFile> docFiles);

	/**
	 * 儲存徵信報告主檔
	 * 
	 * @param c140m01a
	 *            C140M01A
	 */
	void saveC140M01A(C140M01A c140m01a);

	/**
	 * 儲存徵信報告主檔
	 * 
	 * @param c140m01a
	 *            C140M01A
	 */
	void saveDocument(C140M01A c140m01a, List<DocFile> docFiles);

	/**
	 * 多筆徵信報告附加檔案儲存
	 * 
	 * @param docFiles
	 *            DocFile
	 */
	void saveDocFile(List<DocFile> docFiles);


	/**
	 * 刪除 C140M04A
	 * 
	 * @param mainOid
	 *            String
	 */
	void deleteC140M04A(String mainOid);

	/**
	 * 刪除 C140M07A
	 * 
	 * @param c140m07as
	 *            List<C140M07A>
	 */
	void deleteC140M07A(List<C140M07A> c140m07as);

	/**
	 * 取得徵信報告第四章 經營事業(子) Grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S04A>
	 */
	Page<C140S04A> getC140S04APage(ISearch search);

	/**
	 * 取得徵信報告第四章 土地(子) Grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S04A>
	 */
	Page<C140S04B> getC140S04BPage(ISearch search);

	/**
	 * 取得徵信報告第四章 建物(子) Grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S04A>
	 */
	Page<C140S04C> getC140S04CPage(ISearch search);

	/**
	 * 取得第四章 經營事業(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S04A
	 */
	C140S04A getC140S04A(String oid);

	/**
	 * 取得第四章 經營事業(子)(多筆)
	 * 
	 * @param mainId
	 *            String
	 * @param uid
	 *            String
	 * @return List<C140S04A>
	 */
	List<C140S04A> getC140S04A(String mainId, String uid);

	/**
	 * 取得第四章 土地(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S04B
	 */
	C140S04B getC140S04B(String oid);

	/**
	 * 取得第四章 土地(子)(多筆)
	 * 
	 * @param mainId
	 *            String
	 * @param uid
	 *            String
	 * @return List<C140S04B>
	 */
	List<C140S04B> getC140S04B(String mainId, String uid);

	/**
	 * 取得第四章 建物(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S04C
	 */
	C140S04C getC140S04C(String oid);

	/**
	 * 取得第四章 建物(子)(多筆)
	 * 
	 * @param mainId
	 *            String
	 * @param uid
	 *            String4
	 * @return List<C140S04C>
	 */
	List<C140S04C> getC140S04C(String mainId, String uid);

	/**
	 * 刪除 C140S04A
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S04A(String subOid);

	/**
	 * 刪除 C140S04B
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S04B(String subOid);

	/**
	 * 刪除 C140S04C
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S04C(String subOid);

	/**
	 * 取得徵信報告第九章 集團往來(子)(多筆)
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S09A>
	 */
	Page<C140S09A> getC140S09APage(ISearch search);

	/**
	 * 取得徵信報告第九章 集團退票(子)
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S09B>
	 */
	Page<C140S09B> getC140S09BPage(ISearch search);

	/**
	 * 取得徵信報告第九章 集團拒絕(子)
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S09C>
	 */
	Page<C140S09C> getC140S09CPage(ISearch search);

	/**
	 * 取得徵信報告第九章 集團逾催呆(子)
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S09D>
	 */
	Page<C140S09D> getC140S09DPage(ISearch search);

	/**
	 * 取得徵信報告第九章 集團財務(子)
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S09E>
	 */
	Page<C140S09E> getC140S09EPage(ISearch search);

	/**
	 * 取得徵信報告第九章 集團大陸(子)
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S09F>
	 */
	Page<C140S09F> getC140S09FPage(ISearch search);

	/**
	 * 取得第九章 董監事(子)名單(多筆)
	 * 
	 * @param oid
	 *            String
	 * @return C140S09A
	 */
	C140S09A getC140S09A(String oid);

	/**
	 * 取得第九章 董監事(子)名單(多筆)
	 * 
	 * @param mainId
	 *            String
	 * @param uid
	 *            String
	 * @return List<C140S09A>
	 */
	List<C140S09A> getC140S09A(String mainId, String uid);

	/**
	 * 取得第九章 集團退票(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S09B
	 */
	C140S09B getC140S09B(String oid);

	/**
	 * 取得第九章 集團拒絕(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S09C
	 */
	C140S09C getC140S09C(String oid);

	/**
	 * 取得第九章 集團逾催呆(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S09D
	 */
	C140S09D getC140S09D(String oid);

	/**
	 * 取得第九章 集團財務(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S09E
	 */
	C140S09E getC140S09E(String oid);

	/**
	 * 取得第九章 集團大陸(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S09F
	 */
	C140S09F getC140S09F(String oid);

	/**
	 * 刪除 C140S09A
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S09A(String subOid);

	/**
	 * 刪除 C140S09B
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S09B(String subOid);

	/**
	 * 刪除 C140S09C
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S09C(String subOid);

	/**
	 * 刪除 C140S09D
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S09D(String subOid);

	/**
	 * 刪除 C140S09B
	 * 
	 * @param mainId
	 *            String
	 * @param pid
	 *            String
	 */
	void deleteC140S09B(String mainId, String pid);

	/**
	 * 刪除 C140S09C
	 * 
	 * @param mainId
	 *            String
	 * @param pid
	 *            String
	 */
	void deleteC140S09C(String mainId, String pid);

	/**
	 * 複製案件簽報書
	 * 
	 * @param mainId
	 *            複製的mainId來源
	 * @return newMainId 新文件編號
	 * @throws CapMessageException 
	 * @throws NumberFormatException 
	 */
	String copyL120m01All(String mainId) throws NumberFormatException, CapMessageException;

	/**
	 * 取得徵信財務預估表一覽(徵信報告用)
	 * 
	 * @param caseBrId
	 * @param custId
	 * @param dupNo
	 * @param search
	 * @return
	 */
	public Page<Map<String, Object>> getFss(String caseBrId, String custId,
			String dupNo, ISearch search);

	/**
	 * 刪除授信簽報書
	 * 
	 * @param mainId
	 */
	void delLms(String mainId);

	/**
	 * 刪除利害關係人
	 * 
	 * @param listL120s06a
	 * @param listL120s06b
	 */
	void deleteListL120s06ab(List<L120S06A> listL120s06a,
			List<L120S06B> listL120s06b);

	/**
	 * 中長期結構化表格徵信報告複製
	 * 
	 * @param oid
	 * @param mainId
	 * @param cesMainId1
	 * @return
	 */
	C140M01A getC140m01a(String oid, String mainId, String cesMainId1);

	/**
	 * 中長期結構化表格徵信報告更新
	 * 
	 * @param oid
	 * @param mainId
	 * @param cesMainId1
	 * @return
	 */
	C140M01A updateC140m01a(String oid, String mainId, String cesMainId1);

	/**
	 * 產銷方式徵信報告複製
	 * 
	 * @param oid
	 * @param mainId
	 * @param cesMainId1
	 * @return
	 */
	C140M01A getC140m01a2(String oid, String mainId, String cesMainId1);

	/**
	 * 產銷方式徵信報告更新
	 * 
	 * @param oid
	 * @param mainId
	 * @param cesMainId1
	 * @return
	 */
	C140M01A updateC140m01a2(String oid, String mainId, String cesMainId1);

	/**
	 * 產業概況徵信報告複製
	 * 
	 * @param oid
	 * @param mainId
	 * @param cesMainId1
	 * @return
	 */
	C140M01A getC140m01a3(String oid, String mainId, String cesMainId1);

	/**
	 * 產業概況徵信報告更新
	 * 
	 * @param oid
	 * @param mainId
	 * @param cesMainId1
	 * @return
	 */
	C140M01A updateC140m01a3(String oid, String mainId, String cesMainId1);

	/**
	 * 刪除中長期結構化表格徵信報告
	 * 
	 * @param mainId
	 */
	void deleteC140m01a(String mainId);

	/**
	 * 儲存授信報案考核表
	 * 
	 * @param l120m01a
	 * @param l730a01a
	 * @param l730m01a
	 * @param listl730s01a
	 */
	void saveL730M01A(L120M01A l120m01a, L730A01A l730a01a, L730M01A l730m01a,
			List<L730S01A> listl730s01a);

	/**
	 * 儲存簽章欄及分行首次、最後送件時間
	 * 
	 * @param l120m01a
	 * @param models
	 */
	void saveL120m01fAndModel(L120M01A l120m01a, List<L120M01F> models);

	/**
	 * 儲存相關文件群組
	 * 
	 * @param l120m01a
	 *            案件簽報書
	 * @param list
	 *            相關文件群組
	 */
	void saveRel(L120M01A l120m01a, List<L120M01E> list);

	/**
	 * 引進集團名單(相關文件)
	 * 
	 * @param s09aList
	 */
	@SuppressWarnings("rawtypes")
	void saveC140S09A(List s09aList);

	/**
	 * 刪除集團名單(相關文件)
	 * 
	 * @param c140s09as
	 */
	void deleteC140S09A(List<C140S09A> c140s09as);

	/**
	 * 利用獨特Key 找到簽報書敘述說明檔
	 * 
	 * @param mainId
	 * @param itemType
	 * @return
	 */
	L121M01B findL121m01bByUniqueKey(String mainId, String itemType);

	/**
	 * 取得Mow內部信評
	 * 
	 * @param custId
	 * @param dupNo
	 * @param search
	 * @return
	 */
	Page<Map<String, Object>> getMowTrust(String custId, String dupNo,
			ISearch search);

	/**
	 * 儲存相關文件所有Model
	 * 
	 * @param list
	 * @param entity
	 */
	void saveRelAll(L120M01A l120m01a, L120M01F l120m01f);

	/**
	 * 集團企業拒往記錄
	 * 
	 * @param c140s09bs
	 */
	@SuppressWarnings("rawtypes")
	void saveC140S09B(List c140s09bs);

	/**
	 * 集團企業拒往記錄
	 * 
	 * @param c140s09bs
	 */
	@SuppressWarnings("rawtypes")
	void saveC140S09C(List c140s09cs);

	/**
	 * 刪除額度明細表與資本適足率(案件變更格式用)
	 * 
	 * @param list1
	 *            額度明細表群組
	 * @param list2
	 *            資本適足率
	 */
	void deleteL140M01aBis(List<L140M01A> list1, List<L120S03A> list2);

	/**
	 * 儲存營運中心簽章欄及意見
	 * 
	 * @param l120m01a
	 * @param l120m01d1
	 * @param l120m01d2
	 * @param list
	 */
	void saveArea(L120M01A l120m01a, List<L120M01F> list);

	/**
	 * 儲存國金部簽章欄及意見
	 * 
	 * @param l120m01a
	 * @param list
	 */
	void saveSea(L121M01B l121m01b, List<L120M01F> list);

	/**
	 * 儲存國金部簽章欄及意見--呈主管放行(海外聯貸)用
	 * 
	 * @param l120m01a
	 *            主檔
	 * @param l120m01b
	 *            意見檔
	 * @param addL120m01f
	 *            新增存在放行人員(新增用)
	 */
	void saveSeaAndDel(L120M01A l120m01a, L121M01B l120m01b,
			L120M01F addL120m01f);

	/**
	 * 取得主檔ISearch
	 * 
	 * @return
	 */
	public ISearch getMetaSearch();

	/**
	 * 
	 * 案件簽報書flow
	 * 
	 * @param mainOid
	 *            文件編號
	 * @param model
	 *            資料表
	 * @param setResult
	 *            boolean
	 * @param next
	 *            執行的下個動作
	 * 
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, String next) throws Throwable;


	/**
	 * 取得擔保品 相關文件之擔保品
	 * 
	 * @param cmsMainId
	 * @return
	 */
	public Map<String, String> findCmsCustName(String cmsMainId);

	/**
	 * 取得擔保品文件編號 (by 分行)
	 * 
	 * @param branchId
	 * @param search
	 * @return
	 */
	public Page<Map<String, Object>> getCmsMainId(String branchId,
			ISearch search);

	/**
	 * 取得擔保品 簽報書主要借款人Id群組 下的 文件編號
	 * 
	 * @param branchId
	 * @param search
	 * @return
	 */
	public Page<Map<String, Object>> getCmsMainId2(String mainId, ISearch search);

	/**
	 * 取得擔保品 大類 下的 文件編號
	 * 
	 * @param branchId
	 * @param search
	 * @return
	 */
	public Page<Map<String, Object>> getCmsMainId3(String collTyp1,
			ISearch search);

	/**
	 * 
	 * 個金相關資料查詢
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public C120S01E findC120S01EByUniqueKey(String mainId, String custId,
			String dupNo);

	/**
	 * 查詢相關文件
	 * 
	 * @param mainId
	 * @param docType
	 * @return
	 */
	public List<L120M01E> findL120M01EByByMainIdAndDocType(String mainId,
			String docType);

	/**
	 * 查詢主要借款款
	 * 
	 * @param mainId
	 * @return
	 */
	C120M01A findC120M01AByMainIdAndKeyMan(String mainId);

	/**
	 * 查詢借款人資料
	 * 
	 * @param mainIds
	 * @return
	 */
	public List<C101M01A> findC101M01AByMaindIds(String[] mainIds);

	public List<L140M01A> findl140m01aByl120m01cMainid(String l120Mainid);

	public List<L140S02A> findl140s02aByl140m01aMainid(String l140Mainid);

	/**
	 * 查詢簽章欄
	 * 
	 * @param mainId
	 *            文件編號
	 * @param branchType
	 *            行別
	 * @param branchId
	 *            分行代號
	 * @param staffJob
	 *            職稱
	 * @return
	 */
	public L120M01F findL120m01fByMainIdAndKey(String mainId,
			String branchType, String branchId, String staffJob);
	
	@SuppressWarnings("rawtypes")
	public List<? extends GenericBean> findModelListByMainIdCustIdDupNo(Class clazz,
			String mainId, String custId, String dupNo);
	
	/**
	 * 刪除各項費用資料
	 * 
	 * @param l140m01r
	 *            L140M01R
	 */
	void deleteL140M01R(L140M01R l140m01r);
	
	/**
	 * 儲存各項費用資料
	 * 
	 * @param l140m01r
	 *            L140M01R
	 */
	void saveL140M01R(L140M01R l140m01r);
	
	
	/**
	 * 取得各項費用資料BY OID
	 * 
	 * @param oid
	 *            key
	 * @return L140M01R
	 */
	L140M01R getL140M01R(String oid);
	
	/**
	 * 取得各項費用資料BY mainId
	 */
	List<L140M01R> findl140m01rByMainid(String mainId);
	
	/**
	 * 呈案前設定各項費用資料之SEQ
	 * @param mainId
	 */
	public void setL140M01RSeq(String mainId);
	
	public void saveL140MC1A_list(List<L140MC1A> l140mc1a_list);
	
	/**
	 * 取得Grid呈現所需的資料
	 * 
	 * @param clazz
	 *            要搜索model的class
	 * @param search
	 *            搜索的條件
	 * @return Page
	 */
	@SuppressWarnings("rawtypes")
	Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	
	/**
	 * 消金簽報書的科目
	 * 是抓產品(一份額度明細表可能有多個產品)
	 * 目前分隔符號 \r
	 */
	public String get_CLS_l140m01a_subject(L140M01A l140m01a);
	public String get_CLS_l140m01a_subject(L140M01A l140m01a, String sep);
	/**
	 * 取得 借保人 的字串
	 */
	public String get_CLS_l140m01a_guarantor(L140M01A l140m01a);
	/**
	 * 把 purpose 的代碼轉成說明
	 * EX:【3|B】轉成 【 其它、修繕房屋】
	 */
	public String get_CLS_l120m01a_purpose(L120M01A l120m01a);
	
	/**
	 * 取得利率
	 */
	public String get_CLS_l140s02a_rateWithLatestBaseRate(String l140m01a_mainId, String mark);
	
	public String get_CLS_l140s02a_rate(String l140m01a_mainId, String mark);
	
	/**
	 * 取得授信期間-其他
	 */
	public String get_CLS_l140s02a_lnother(String l140m01a_mainId, boolean isParentCase);
	
	/**
	 * 利用Oid取得利害關係人授信條件對照表主檔
	 * 
	 * @param oid
	 * @return
	 */
	public L120S06A findL120s06aByOid(String oid);

	/**
	 * 因應非分行授權內案件總處要調整各項費用值故於分行呈案時先將原分行的登錄內容儲存起來
	 * @param mainId
	 * @throws CapException
	 */
	public void AuthLvlreSetL140M01R(String mainId) throws CapException;
	
	/**
	 * 刪除查核事項檔
	 * 
	 * @param oidArray
	 */
	void deleteListL140M04A(List<L140M04A> list);

	public List<C120M01A> findC120M01AByMainIdForOrder(String mainId);
	
	public String l140m01r_desc(L120M01A l120m01a);
	public Map<String, String> get_l120s12a_formData(L120M01A l120m01a, L140M01A l140m01a, L140S02A l140s02a);
	public Map<String, String> get_l120s13a_formData(L120M01A l120m01a, L140M01A l140m01a, L140S02A l140s02a);
	public Map<String, String> get_l120s15a_formData(L120M01A l120m01a, L140M01A l140m01a, L140S02A l140s02a);
	public String build_html_SimplifyFlag_B(L120M01A l120m01a);
	public String build_html_SimplifyFlag_C(L120M01A l120m01a);
	public String build_html_SimplifyFlag_D(L120M01A l120m01a);
	/**
	 * 引入簡化簽報書借款人明細
	 * @param l120m01a
	 */
	String importC120S01T(L120M01A l120m01a);

	/**
	 * 簡化簽報書借款人明細變更順序
	 * @param model
	 * @param boolean1
	 */
	public boolean changeC120S01TSeqNo(C120S01T model, boolean upOrDown);

	TreeMap<String, String> getJ10DefaultRateByType(String type);

	List<L120S09A> findL120S09AByMainIdAndCustIdDupNo(C120S01T model,
			String custId, String dupNo);

	C120M01A findC120M01AByfindByUniqueKey(String trim, 
			String custId, String dupNo);

	CapAjaxFormResult queryC120S01T(C120S01T model);

	public String checkRiskEvaluateQ1(L120M01A l120m01a);

	CapAjaxFormResult getCLS1201S22Panel(L120M01A l120m01a);

	public Map<String, Boolean> checkIsSuspectedHeadAccountForL140MC1A(L140M01A l140m01a, String l120m01a_mainId) throws CapMessageException;

	void deleteAllL140mc1a(String l140m01a_mainId);
	
	public Map<String, Object> getDocFileForDeleteBorrower(String c120m01a_custId, String c120m01a_dupNo, String c120m01a_ownBrId);

	public JSONObject prepare_gen_caseDoc_tabDoc(String brNo, String custId, String dupNo, String prodKind);
	
	public List<C122M01A> findC122M01A_by_brNo_custId_applyKind_orderByApplyTSDesc(String ownBrId, String custId, String applyKind, String createTimeSince);

	public String ha_factor_L(String mainLenderId, List<String> guarantorList);
	public String ha_factor_M(List<C101S01A> mainLender_and_guarantor_List);
	public String ha_factor_B(C101M01A c101m01a,C101S01A c101s01a, C101S01B mainLender, String branchNo);
	public String ha_factor_3(List<C101S01B> mainLender_and_guarantor_List);
	public void check_l140m01a_loanTotAmt_3000WAN(String mainId, String caseType, CapAjaxFormResult result);
	
	public Map<String, Object> modifCaseType(L120M01A l120m01a, String docCode, String authLvl, String ngFlag, String areaBrid);
	public List<C127M01A> saveClsAreaPriceExcel(HSSFSheet sheet, String mainId)
			throws CapMessageException;
	public String checkClsAreaPrice(L120M01A l120m01a);
	public void createCreditConditionLog(String mainId, String content, String type, String remark) throws Exception;
	
	/**
	 * 儲存L120S19A的利率、備註欄位，寫一筆L120S19B修改歷程
	 * @param mainId
	 * @param rateDesc
	 * @param rateRemark
	 */
	public String saveL120S19AForRate(String mainId, String rateDesc, String rateRemark);
	public String formatContentString(String type, String curr, String content);
	public Map<String, String> formatDisplayDisplayStringForL120S19C(String mainId, String role) throws CapException;
	
	/**
	 * 無紙化作業-更新額度明細表L140M01A狀態-擬核定
	 * @param mainId
	 * @throws CapException
	 */
	public void chgL140m01aDocStatus(String mainId) throws CapException;

	public String checkIsNotifiedMessageForPaperlessSigning(String l120m01a_mainId, String simpleFlag) throws CapException;

	public void importLendCollateral(String mainId, String[] rows, String unitNo) throws CapMessageException,CapException, IOException;
	public String getL120s19b_content(String l120m01a_mainId, String type);
    C122M01A findByPloanCaseNo(String ploanCaseNo);

	public boolean processIsHittingSameBorrowerInfo(L140M01A l140m01a, String l120m01a_mainId);

	public void deleteAllL140mc2a(String l140m01a_mainId);

	public List<L140MC2A> findL140MC2ABy(String l140m01a_mainId);
	
	public String import_collRealEstateFrom_L140M01O(List<L140M01O> l140m01os);

	public BigDecimal sumCompensationAmt(JSONObject json);
	public String promptUsePlan(String l120m01a_mainId);

	public void startMortgageRatioValidation(L120M01A l120m01a) throws CapMessageException;

	public String getMortgageRatioCheckTipsMessage(L120M01A l120m01a);

	public String getMortgageRatioCheckErrorMessage(String l120m01a_mainId);
}
