package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.lms.panels.LMSS02APanel01;
import com.mega.eloan.lms.lms.panels.LMSS02APanel02;
import com.mega.eloan.lms.lms.panels.LMSS02APanel03;
import com.mega.eloan.lms.lms.panels.LMSS02APanel04;
import com.mega.eloan.lms.lms.panels.LMSS02APanel05;

/**
 * <pre>
 * 借款人個金-分頁
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lmss02a/{page}")
public class LMSS02APage extends AbstractEloanForm {

	@Override
	public void afterExecute(PageParameters parameters) {
		super.afterExecute(parameters);
		// remove("_headerPanel");

	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		model.addAttribute("_lmss02apanel01_visible", true);
		new LMSS02APanel01("lmss02apanel01").processPanelData(model, params);

		model.addAttribute("_lmss02apanel02_visible", true);
		new LMSS02APanel02("lmss02apanel02").processPanelData(model, params);

		model.addAttribute("_lmss02apanel03_visible", true);
		new LMSS02APanel03("lmss02apanel03").processPanelData(model, params);

		model.addAttribute("_lmss02apanel04_visible", true);
		new LMSS02APanel04("lmss02apanel04").processPanelData(model, params);

		model.addAttribute("_lmss02apanel05_visible", true);
		new LMSS02APanel05("lmss02apanel05").processPanelData(model, params);
	}

	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
}
