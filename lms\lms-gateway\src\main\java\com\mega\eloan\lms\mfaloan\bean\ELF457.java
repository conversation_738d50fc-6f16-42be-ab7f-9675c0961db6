package com.mega.eloan.lms.mfaloan.bean;


import javax.persistence.*;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;


/** 地政士進件額度資料 **/

public class ELF457 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 額度序號 **/
	@Size(max = 12)
	@Column(name="ELF457_CNTRNO", length=12, columnDefinition="CHAR(12)", nullable=false,unique = true)
	private String elf457_cntrno;

	/** 地政士證書字號Part1 **/
	@Column(name="ELF457_LAYEAR", length=3, columnDefinition="CHAR(3), nullable=false,unique = true")
	private String elf457_layear;

	/** 地政士證書字號Part2 **/
	@Column(name="ELF457_LAWORD", length=15, columnDefinition="CHAR(15), nullable=false,unique = true")
	private String elf457_laword;

	/** 地政士證書字號Part3 **/
	@Column(name="ELF457_LANO", length=6, columnDefinition="CHAR(6), nullable=false,unique = true")
	private String elf457_lano;

	/** 地政士姓名 **/
	@Column(name="ELF457_LANAME", length=120, columnDefinition="CHAR(120)")
	private String elf457_laname;
	
	/** 事務所統編 **/
	@Column(name="ELF457_LOID", length=10, columnDefinition="CHAR(10)")
	private String elf457_loid;
	
	/** 事務所名稱 **/
	@Column(name="ELF457_LONAME", length=150, columnDefinition="CHAR(150)")
	private String elf457_loname;
	
	/** 進件來源  **/
	@Column(name="ELF457_CASESRCFLAG", length=150, columnDefinition="CHAR(1)")
	private String elf457_casesrcflag;
		
	/** 取得額度序號 **/
	public String getElf457_cntrno() {
		return this.elf457_cntrno;
	}
	/** 設定額度序號 **/
	public void setElf457_cntrno(String value) {
		this.elf457_cntrno = value;
	}

	/** 取得地政士證書字號Part1 **/
	public String getElf457_layear() {
		return this.elf457_layear;
	}
	/** 設定地政士證書字號Part1 **/
	public void setElf457_layear(String value) {
		this.elf457_layear = value;
	}

	/** 取得地政士證書字號Part2 **/
	public String getElf457_laword() {
		return this.elf457_laword;
	}
	/** 設定地政士證書字號Part2 **/
	public void setElf457_laword(String value) {
		this.elf457_laword = value;
	}

	/** 取得地政士證書字號Part3 **/
	public String getElf457_lano() {
		return this.elf457_lano;
	}
	/** 設定地政士證書字號Part3 **/
	public void setElf457_lano(String value) {
		this.elf457_lano = value;
	}

	/** 取得地政士姓名 **/
	public String getElf457_laname() {
		return this.elf457_laname;
	}
	/** 設定地政士姓名 **/
	public void setElf457_laname(String value) {
		this.elf457_laname = value;
	}
	
	/** 取得事務所統編 **/
	public String getElf457_loid() {
		return elf457_loid;
	}
	/** 設定事務所統編 **/
	public void setElf457_loid(String elf457_loid) {
		this.elf457_loid = elf457_loid;
	}
	/** 取得事務所名稱 **/
	public String getElf457_loname() {
		return elf457_loname;
	}
	/** 設定事務所名稱 **/
	public void setElf457_loname(String elf457_loname) {
		this.elf457_loname = elf457_loname;
	}
	/** 取得進件來源  **/
	public String getElf457_casesrcflag() {
		return elf457_casesrcflag;
	}
	/** 設定進件來源  **/
	public void setElf457_casesrcflag(String elf457_casesrcflag) {
		this.elf457_casesrcflag = elf457_casesrcflag;
	}
}
