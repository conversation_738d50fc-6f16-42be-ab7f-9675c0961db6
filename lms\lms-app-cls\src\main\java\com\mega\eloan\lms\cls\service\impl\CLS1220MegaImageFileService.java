package com.mega.eloan.lms.cls.service.impl;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.service.MEGAImageService;
import com.mega.eloan.lms.base.service.FileDownloadService;

import tw.com.jcs.common.Util;

@Service("cls1220MegaImageFileService")
public class CLS1220MegaImageFileService implements FileDownloadService {
	
	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS1220MegaImageFileService.class);

	@Resource
	MEGAImageService mEGAImageService;
	
	@Override
	public byte[] getContent(PageParameters params) throws Exception {
		
		String id = Util.trim(params.getString("custId"));
		return mEGAImageService.getRPAQueryCLImage(id);
	}
}
