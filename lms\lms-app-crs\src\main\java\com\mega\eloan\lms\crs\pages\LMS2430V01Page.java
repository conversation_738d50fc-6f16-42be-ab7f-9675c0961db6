package com.mega.eloan.lms.crs.pages;

import java.util.ArrayList;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;



@Controller
@RequestMapping("/crs/lms2430v01")
public class LMS2430V01Page extends AbstractEloanInnerView {

	public LMS2430V01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(FlowDocStatusEnum.編製中);
		//---
		ArrayList<EloanPageFragment> list = new ArrayList<>();
		//list.add(SimpleButtonEnum.Filter);
		list.add(LmsButtonEnum.Add);
		list.add(LmsButtonEnum.Delete);
		addToButtonPanel(model, list);
		
		renderJsI18N(LMS2430V01Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/crs/LMS2430V01Page');");
	}

}
