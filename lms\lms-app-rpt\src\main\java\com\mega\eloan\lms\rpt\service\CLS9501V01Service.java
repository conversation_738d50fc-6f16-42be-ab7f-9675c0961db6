/* 
 * LMS9541Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.C820M01A;

public interface CLS9501V01Service extends AbstractService {

	List<Map<String,Object>> queryData(String brno, String dateYM);

	List<C820M01A> getAllSelects(String mainId);
}
