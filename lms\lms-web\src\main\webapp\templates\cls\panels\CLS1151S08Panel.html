<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
        <wicket:panel>
        	<span id="page08isGutCut" style="display:none">
				<font style='font-size:16px; font-weight: bold;' color="red">
					<wicket:message key="L140M01b.page08isGutCut">※信保機構保證書所囑事項，應詳實列入額度明細表中</wicket:message>
				</font>
			</span>				
            <form action="" id="CLS1151Form08" name="CLS1151Form08">
				<table class="tb2" width="100%" border="0" cellpadding="0" cellspacing="0">
                    <tr class="hd1" style="text-align:left">
                        <td colspan="2">
                            <select id="pageNum8" name="pageNum8" class="nodisabled" onchange="changedistanceWord('8','44','53');">
                                <option value="0" selected="selected"><wicket:message key="L140M01b.printMain"><!-- 印於主表--></wicket:message></option>
                                <option value="1"><wicket:message key="L140M01b.print01"><!-- 印於附表(一)--></wicket:message></option>
                            </select>
							&nbsp;
							<select id="formatType" name="formatType" class="nodisabled">
								<option value="1" selected="selected"><wicket:message key="L140M01B.editByFreeFormat"><!-- 以自由格式編輯--></wicket:message></option>
								<option value="2"><wicket:message key="L140M01B.editByTemplateFormat"><!-- 以樣版格式編輯--></wicket:message></option>
							</select>
							
							<input type="hidden" id="selectedBizCat" name="selectedBizCat" />
							<input type="hidden" id="selectedBizItem" name="selectedBizItem" />
							
							<div id="bizCatTypeThickbox" style="display:none;">
								<button type="button" id="selectTemplateButton">
									<span class="text-only"><wicket:message key="btn.addL140S09A">選取簽報樣版</wicket:message></span>
								</button>
								<button type="button" id="bizCatTypeDelButton">
									<span class="text-only"><wicket:message key="button.delete">刪除</wicket:message></span>
								</button>
								<button type="button" id="bizCatTypeMoveUpButton">
									<span class="text-only"><wicket:message key="btn.upSeqno">向上移動</wicket:message></span>
								</button>
								<button type="button" id="bizCatTypeMoveDownButton">
									<span class="text-only"><wicket:message key="btn.downSeqno">向下移動</wicket:message></span>
								</button>
								<button type="button" class="previewTemplateFormat">
									<span class="text-only"><wicket:message key="button.preview">預覽</wicket:message></span>
								</button>
								<div id="bizCatTypeGrid"></div>
							</div>
							<div id="bizCatItemContentThickbox" style="display:none;">
								<!--<form id="formL140S09ADetail">-->
								<button type="button" id="bizCatItemAddButton">
									<span class="text-only"><wicket:message key="btn.addL140S09A_ItemXX">新增自訂項目</wicket:message></span>
								</button>
								<button type="button" id="bizCatItemChgButton">
									<span class="text-only"><wicket:message key="btn.chgBizItemName">更改項目名稱</wicket:message></span>
								</button>
								<button type="button" id="bizCatItemDelButton">
									<span class="text-only"><wicket:message key="button.delete">刪除</wicket:message></span>
								</button>
								<button type="button" id="bizCatItemMoveUpButton">
									<span class="text-only"><wicket:message key="btn.upSeqno">向上移動</wicket:message></span>
								</button>
								<button type="button" id="bizCatItemMoveDownButton">
									<span class="text-only"><wicket:message key="btn.downSeqno">向下移動</wicket:message></span>
								</button>
								<button type="button" class="previewTemplateFormat">
									<span class="text-only"><wicket:message key="button.preview">預覽</wicket:message></span>
								</button>
								<br/>
								<span class="text-red"><wicket:message key="L140S09A.msg"></wicket:message></span>
								<br/>
								<div id="bizCatItemContentGrid"></div>
								<!--</form>-->
							</div>
			
							<div id="detailContentThickBox" style="display:none;">
								<button type="button" id="bizCatContentAddButton">
									<span class="text-only"><wicket:message key="button.add"></wicket:message></span>
								</button>
								<button type="button" id="bizCatContentDelButton">
									<span class="text-only"><wicket:message key="button.delete">刪除</wicket:message></span>
								</button>
								<button type="button" id="bizCatContentMoveUpButton">
									<span class="text-only"><wicket:message key="btn.upSeqno">向上移動</wicket:message></span>
								</button>
								<button type="button" id="bizCatContentMoveDownButton">
									<span class="text-only"><wicket:message key="btn.downSeqno">向下移動</wicket:message></span>
								</button>
								<button type="button" class="previewTemplateFormat">
									<span class="text-only"><wicket:message key="button.preview">預覽</wicket:message></span>
								</button>
								<div id="bizCatContentGrid"></div>
							</div>
							<div id="newL140S09ABox" style="display:none;">
								<!--<form id="L140S09AForm">-->
								<table width="100%" class="tb2" border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td class="hd1"><wicket:message key="L140S09A.bizCat"></wicket:message></td>
										<td><select id ="bizCat" name="bizCat" combokey="template_type"/></td>
									</tr>
									<tr>
										<td class="hd1"><wicket:message key="L140S09A.loanTPsName"></wicket:message></td>
										<td><input type="text" id="loanTPsName" name="loanTPsName" maxlengthC="20"/>(如：A、A~B，不輸入則不呈現)</td>
									</tr>
								</table>
								<!--</form>-->
							</div>
							<div id="l140s10bContentBox" style="display:none;">
								<table id="l140s10bContentForm">
									<tr>
										<td>
											<b class="star"><wicket:message key="lms.ckeditRemark3">註1:|←建議換行</wicket:message></b><br/>
										　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　|←
											<textarea id="content" name="content" cols="75" rows="3" maxlength="150" maxlengthC="450"></textarea>
										</td>
									</tr>
								</table>
							</div>
							
							<div id="modifyCustomItemAndContentBox" style="display:none;">
								<table>
									<tr id="itemDescTr">
										<td class="hd1"><wicket:message key="L140S10A.signingTemplate.item">項目</wicket:message>：</td>
										<td><input type="text" id="itemDesc" name="itemDesc" maxlengthC="50"/></td>
									</tr>
									<tr id="contentTextTr">
										<td colspan="2">
											<textarea id="contentText" name="contentText" cols="75" rows="3" maxlength="150" maxlengthC="450"></textarea>
										</td>
									</tr>
								</table>
							</div>
							
							<div id="previewBox" style="display:none;">
								<span id="previewSpan"/>
							</div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                        	<button type="button" id="selectTmpFormatButton" class="forview">
								<wicket:message key="btn.L140S09A">選擇樣版格式</wicket:message>
							</button>
                            <textarea cols="100" rows="10%" id="itemDscr8" name="itemDscr8" class="tckeditor" showType="b" showNewLineMessage="Y" distanceWord="44" t_width="800" t_height="500" wicket:message="displayMessage:cls1151s01.title08" preview="width:800;height:900"></textarea>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <span class="text-red"><wicket:message key="page8.001"><!-- 動撥提醒事項--></wicket:message>&nbsp;&nbsp;</span>
                        </td>
                        <td>
                            <wicket:message key="page8.004"><!-- 注意事項--></wicket:message>：
                            <input type="text" id="toALoan1" name="toALoan1" maxlength="30" maxlengthC="30" size="80" class="nodisabled fullText" />
                            <br/>
                            <wicket:message key="page8.005"><!-- 承諾事項--></wicket:message>：
                            <br/>
                            <textarea id="toALoan2" name="toALoan2" maxlengthC="390" rows="5" cols="70" class="nodisabled fullText"></textarea>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <wicket:message key="page8.002"><!--附加檔案--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td>
                            <button type="button" id="cls_uploadFile">
                                <span class="text-only"><wicket:message key="page8.003"><!--選擇附加檔案--></wicket:message></span>
                            </button> 
                            <button type="button" id="cls_deleteFile">
                                <span class="text-only"><wicket:message key="button.delete"><!-- 刪除--></wicket:message></span>
                            </button>
                            <br/>
                            <div id="cls_gridfile" />
                        </td>
                    </tr>
                </table>
            </form><script type="text/javascript" src="pagejs/cls/CLS1151S08Panel.js?ver=20220516"></script>
        </wicket:panel>
    </body>
</html>
