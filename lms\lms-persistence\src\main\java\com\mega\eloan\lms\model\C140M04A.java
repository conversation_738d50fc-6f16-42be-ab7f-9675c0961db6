package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.RelativeMeta;

/**
 * <pre>
 * C140M04A model. 肆、負責人保證人(主)
 * </pre>
 * 
 * @since 2011/9/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/20,<PERSON>,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "C140M04A-entity-graph", attributeNodes = { 
		@NamedAttributeNode("c140m01a"),
		@NamedAttributeNode("l120m01e")
		})
@Entity
@Table(name = "C140M04A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C140M04A extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件 L120M01E．關聯檔
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumn(name = "OID", referencedColumnName = "DOCOID", nullable = false, insertable = false, updatable = false)
	private L120M01E l120m01e;

	public L120M01E getL120m01e() {
		return l120m01e;
	}

	public void setL120m01e(L120M01E l120m01e) {
		this.l120m01e = l120m01e;
	}

	@Transient
	private String custId;
 
	public void setCustId(L120M01E l120m01e) {
		if(l120m01e == null){
			this.custId = "";
		}else{
			this.custId= l120m01e.getDocCustId() + " " + l120m01e.getDocDupNo();	
		}		
	}
	public String getCustId() {
		return custId;
	}
	
	@Column(length = 32)
	private String uid;

	@Column(length = 10)
	private String pcId;
	
	@Column(length = 1)
	private String pcDupNo;
	
	@Column(length = 228)
	private String pcName;

	@Column(length = 1)
	private String pcType;

	@Column(length = 2)
	private String pcTitle;

	@Column(length = 1)
	private String pcSex;

	// bi-directional many-to-one association to C140JSON
	@ToStringExclude
	@OneToMany(mappedBy = "c140m04a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140JSON> c140jsons;

	// bi-directional many-to-one association to C140M01A
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({ @JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
        @JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false) })
	private C140M01A c140m01a;

	// bi-directional many-to-one association to C140S04A
	@ToStringExclude
	@OneToMany(mappedBy = "c140m04a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140S04A> c140s04as;

	// bi-directional many-to-one association to C140S04B
	@ToStringExclude
	@OneToMany(mappedBy = "c140m04a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140S04B> c140s04bs;

	// bi-directional many-to-one association to C140S04C
	@ToStringExclude
	@OneToMany(mappedBy = "c140m04a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140S04C> c140s04cs;

	public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

	public String getPcId() {
		return pcId;
	}

	public void setPcId(String pcId) {
		this.pcId = pcId;
	}

	public String getPcDupNo() {
		return pcDupNo;
	}

	public void setPcDupNo(String pcDupNo) {
		this.pcDupNo = pcDupNo;
	}
	
	
	public String getPcName() {
		return pcName;
	}

	public void setPcName(String pcName) {
		this.pcName = pcName;
	}

	public String getPcType() {
		return pcType;
	}

	public void setPcType(String pcType) {
		this.pcType = pcType;
	}

	public String getPcTitle() {
		return pcTitle;
	}

	public void setPcTitle(String pcTitle) {
		this.pcTitle = pcTitle;
	}

	public String getPcSex() {
		return pcSex;
	}

	public void setPcSex(String pcSex) {
		this.pcSex = pcSex;
	}

	public List<C140JSON> getC140jsons() {
		return this.c140jsons;
	}

	public void setC140jsons(List<C140JSON> c140jsons) {
		this.c140jsons = c140jsons;
	}

	public C140M01A getC140m01a() {
		return this.c140m01a;
	}

	public void setC140m01a(C140M01A c140m01a) {
		this.c140m01a = c140m01a;
	}

	public List<C140S04A> getC140s04as() {
		return this.c140s04as;
	}

	public void setC140s04as(List<C140S04A> c140s04as) {
		this.c140s04as = c140s04as;
	}

	public List<C140S04B> getC140s04bs() {
		return this.c140s04bs;
	}

	public void setC140s04bs(List<C140S04B> c140s04bs) {
		this.c140s04bs = c140s04bs;
	}

	public List<C140S04C> getC140s04cs() {
		return this.c140s04cs;
	}

	public void setC140s04cs(List<C140S04C> c140s04cs) {
		this.c140s04cs = c140s04cs;
	}

}