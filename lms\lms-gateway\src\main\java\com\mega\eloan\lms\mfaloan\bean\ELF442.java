/* 
 * ELF442.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import tw.com.iisi.cap.model.GenericBean;

/** 優惠房貸額度控管檔 **/

public class ELF442 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Column(name="ELF442_BRANCH", length=3, columnDefinition="CHAR(03)",unique = true)
	private String elf442_branch;

	/** 客戶統編 **/
	@Column(name="ELF442_CUSTID", length=10, columnDefinition="CHAR(10)",unique = true)
	private String elf442_custid;

	/** 重複序號 **/
	@Column(name="ELF442_DUPNO", length=1, columnDefinition="CHAR(01)",unique = true)
	private String elf442_dupno;
	
	/** 額度序號 **/
	@Column(name="ELF442_CNTRNO", length=12, columnDefinition="CHAR(12)",unique = true)
	private String elf442_cntrno;

	/** 
	 * 客戶名稱
	 **/ 
	@Column(name="ELF442_CUSNAME", length=102, columnDefinition="CHAR(102)")
	private String elf442_cusname;
	
	/** 
	 * 預約額度幣別
	 **/ 
	@Column(name="ELF442_CURR", length=3, columnDefinition="CHAR(03)")
	private String elf442_curr;

	/** 
	 * 預約額度原幣金額
	 */
	@Column(name="ELF442_QUOTA", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf442_quota;

	/** 
	 * 額度擔保類別
	 **/ 
	@Column(name="ELF442_KIND", length=1, columnDefinition="CHAR(01)")
	private String elf442_kind;
	
	/** 預約額度申請到期日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF442_ENDDT", columnDefinition="DATE")
	private Date elf442_enddt;
	
	/** 預約設定刪除日 **/
	@Column(name="ELF442_DELETEDT", columnDefinition="TIMESTAMP")
	private Date elf442_deletedt;
	
	/** 
	 * 集團代號
	 **/ 
	@Column(name="ELF442_GRPID", length=4, columnDefinition="CHAR(04)")
	private String elf442_grpid;
	
	/** 
	 * 預約人員員工代號
	 **/ 
	@Column(name="ELF442_EMPID", length=7, columnDefinition="CHAR(07)")
	private String elf442_empid;
	
	/** 
	 * 預約人員員工姓名
	 **/ 
	@Column(name="ELF442_EMPNAME", length=40, columnDefinition="CHAR(40)")
	private String elf442_empname;
	
	/** 
	 * 預約人員分機
	 **/ 
	@Column(name="ELF442_EMPTELNO", length=20, columnDefinition="CHAR(20)")
	private String elf442_emptelno;
	
	/** 
	 * 備註
	 **/ 
	@Column(name="ELF442_MEMO", length=80, columnDefinition="CHAR(80)")
	private String elf442_memo;
	
	/** 
	 * 資料修改人（行員代號）
	 **/ 
	@Column(name="ELF442_UPDATER", length=7, columnDefinition="CHAR(07)")
	private String elf442_updater;
	
	/** 資料修改日期 **/
	@Column(name="ELF442_TMESTAMP", columnDefinition="TIMESTAMP")
	private Date elf442_tmestamp;

	/** 
	 * 案件狀態
	 **/ 
	@Column(name="ELF442_STATUS", length=1, columnDefinition="CHAR(01)")
	private String elf442_status;
	
	/** 
	 * 預約類別
	 **/ 
	@Column(name="ELF442_CLASS_CODE", length=2, columnDefinition="CHAR(02)")
	private String elf442_class_code;
	
	/** 
	 * 核准註記
	 **/ 
	@Column(name="ELF442_AGREE_FLAG", length=1, columnDefinition="CHAR(01)")
	private String elf442_agree_flag;
	
	/** 
	 * 風險國別
	 **/ 
	@Column(name="ELF442_RISK_CNTRY", length=2, columnDefinition="CHAR(02)")
	private String elf442_risk_cntry;
	
	/** 
	 * 風險區域別
	 **/ 
	@Column(name="ELF442_RISK_AREA", length=2, columnDefinition="CHAR(02)")
	private String elf442_risk_area;
	
	/** 
	 * 行業對象別
	 **/ 
	@Column(name="ELF442_BUS_CD", length=6, columnDefinition="CHAR(06)")
	private String elf442_bus_cd;
	
	/** 
	 * 行業對象別細分類
	 **/ 
	@Column(name="ELF442_BUS_SUB_CD", length=2, columnDefinition="CHAR(02)")
	private String elf442_bus_sub_cd;
	
	/** 
	 * 預約核准註記
	 **/ 
	@Column(name="ELF442_PROVED_FLAG", length=1, columnDefinition="CHAR(01)")
	private String elf442_proved_flag;
	
	/** 
	 * 
	 **/ 
	@Column(name="ELF442_CN_LOAN_FG", length=1, columnDefinition="CHAR(01)")
	private String elf442_cn_loan_fg;
	
	/** 
	 * 
	 **/ 
	@Column(name="ELF442_ALOAN_EMPID", length=1, columnDefinition="CHAR(01)")
	private String elf442_aloan_empid;
	
	/** 
	 * 產品種類
	 **/ 
	@Column(name="ELF442_PROD_CLASS", length=2, columnDefinition="CHAR(02)")
	private String elf442_prod_class;
	
	/** 
	 * 土地面積
	 */
	@Column(name="ELF442_LAND_AREA", columnDefinition="DECIMAL(13,2)")
	private BigDecimal elf442_land_area;

	/** 預計取得建照日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF442_BUILD_DATE", columnDefinition="DATE")
	private Date elf442_build_date;

	/** 
	 * 預計撥款至動工期間月數
	 */
	@Column(name="ELF442_WAIT_MONTH", columnDefinition="DECIMAL(3,0)")
	private BigDecimal elf442_wait_month;
	
	/** 
	 * 擔保品座落區
	 **/ 
	@Column(name="ELF442_LOCATION_CD", length=3, columnDefinition="CHAR(03)")
	private String elf442_location_cd;
	
	/** 
	 * 座落區段
	 */
	@Column(name="ELF442_SITE3NO", columnDefinition="DECIMAL(4,0)")
	private BigDecimal elf442_site3no;
	
	/** 
	 * 座落區村里
	 **/ 
	@Column(name="ELF442_SITE4NO", length=11, columnDefinition="CHAR(11)")
	private String elf442_site4no;
	
	/** 
	 * 土地使用分區
	 **/ 
	@Column(name="ELF442_LAND_TYPE", length=1, columnDefinition="CHAR(01)")
	private String elf442_land_type;
	
	/** 
	 * 
	 **/ 
	@Column(name="ELF442_RESTRICT_FG", length=1, columnDefinition="CHAR(01)")
	private String elf442_restrict_fg;
	
	/** 取得分行別 **/
	public void setElf442_branch(String value) {
		this.elf442_branch = value;
	}
	
	/** 設定分行別 **/
	public String getElf442_branch() {
		return elf442_branch;
	}

	/** 取得客戶統編 **/
	public void setElf442_custid(String value) {
		this.elf442_custid = value;
	}

	/** 設定客戶統編 **/
	public String getElf442_custid() {
		return elf442_custid;
	}

	/** 設定重複序號 **/
	public void setElf442_dupno(String value) {
		this.elf442_dupno = value;
	}

	/** 取得重複序號 **/
	public String getElf442_dupno() {
		return elf442_dupno;
	}

	/** 設定額度序號 **/
	public void setElf442_cntrno(String value) {
		this.elf442_cntrno = value;
	}

	/** 取得額度序號 **/
	public String getElf442_cntrno() {
		return elf442_cntrno;
	}

	/** 
	 * 設定客戶名稱
	 **/ 
	public void setElf442_cusname(String value) {
		this.elf442_cusname = value;
	}

	/** 
	 * 取得客戶名稱
	 **/ 
	public String getElf442_cusname() {
		return elf442_cusname;
	}

	/** 
	 * 設定預約額度幣別
	 **/ 
	public void setElf442_curr(String value) {
		this.elf442_curr = value;
	}

	/** 
	 * 取得預約額度幣別
	 **/ 
	public String getElf442_curr() {
		return elf442_curr;
	}

	/** 
	 * 設定預約額度原幣金額
	 */
	public void setElf442_quota(BigDecimal value) {
		this.elf442_quota = value;
	}

	/** 
	 * 取得預約額度原幣金額
	 */
	public BigDecimal getElf442_quota() {
		return elf442_quota;
	}

	/** 
	 * 設定額度擔保類別
	 **/
	public void setElf442_kind(String value) {
		this.elf442_kind = value;
	}

	/** 
	 * 取得額度擔保類別
	 **/
	public String getElf442_kind() {
		return elf442_kind;
	}

	/** 設定預約額度申請到期日 **/
	public void setElf442_enddt(Date value) {
		this.elf442_enddt = value;
	}

	/** 取得預約額度申請到期日 **/
	public Date getElf442_enddt() {
		return elf442_enddt;
	}

	/** 設定預約設定刪除日 **/
	public void setElf442_deletedt(Date value) {
		this.elf442_deletedt = value;
	}

	/** 取得預約設定刪除日 **/
	public Date getElf442_deletedt() {
		return elf442_deletedt;
	}

	/** 
	 * 設定集團代號
	 **/ 
	public void setElf442_grpid(String value) {
		this.elf442_grpid = value;
	}

	/** 
	 * 取得集團代號
	 **/ 
	public String getElf442_grpid() {
		return elf442_grpid;
	}

	/** 
	 * 設定預約人員員工代號
	 **/ 
	public void setElf442_empid(String value) {
		this.elf442_empid = value;
	}

	/** 
	 * 取得預約人員員工代號
	 **/ 
	public String getElf442_empid() {
		return elf442_empid;
	}

	/** 
	 * 設定預約人員員工姓名
	 **/ 
	public void setElf442_empname(String value) {
		this.elf442_empname = value;
	}

	/** 
	 * 取得預約人員員工姓名
	 **/ 
	public String getElf442_empname() {
		return elf442_empname;
	}

	/** 
	 * 設定預約人員分機
	 **/ 
	public void setElf442_emptelno(String value) {
		this.elf442_emptelno = value;
	}

	/** 
	 * 取得預約人員分機
	 **/ 
	public String getElf442_emptelno() {
		return elf442_emptelno;
	}

	/** 
	 * 設定備註
	 **/
	public void setElf442_memo(String value) {
		this.elf442_memo = value;
	}

	/** 
	 * 取得備註
	 **/
	public String getElf442_memo() {
		return elf442_memo;
	}

	/** 
	 * 設定資料修改人（行員代號）
	 **/ 
	public void setElf442_updater(String value) {
		this.elf442_updater = value;
	}

	/** 
	 * 取得資料修改人（行員代號）
	 **/ 
	public String getElf442_updater() {
		return elf442_updater;
	}

	/** 設定資料修改日期 **/
	public void setElf442_tmestamp(Date value) {
		this.elf442_tmestamp = value;
	}

	/** 取得資料修改日期 **/
	public Date getElf442_tmestamp() {
		return elf442_tmestamp;
	}

	/** 
	 * 設定案件狀態
	 **/ 
	public void setElf442_status(String value) {
		this.elf442_status = value;
	}

	/** 
	 * 取得案件狀態
	 **/ 
	public String getElf442_status() {
		return elf442_status;
	}

	/** 
	 * 設定預約類別
	 **/ 
	public void setElf442_class_code(String value) {
		this.elf442_class_code = value;
	}

	/** 
	 * 取得預約類別
	 **/ 
	public String getElf442_class_code() {
		return elf442_class_code;
	}

	/** 
	 * 設定核准註記
	 **/ 
	public void setElf442_agree_flag(String value) {
		this.elf442_agree_flag = value;
	}

	/** 
	 * 取得核准註記
	 **/ 
	public String getElf442_agree_flag() {
		return elf442_agree_flag;
	}

	/** 
	 * 設定風險國別
	 **/
	public void setElf442_risk_cntry(String value) {
		this.elf442_risk_cntry = value;
	}

	/** 
	 * 取得風險國別
	 **/
	public String getElf442_risk_cntry() {
		return elf442_risk_cntry;
	}

	/** 
	 * 設定風險區域別
	 **/ 
	public void setElf442_risk_area(String value) {
		this.elf442_risk_area = value;
	}

	/** 
	 * 取得風險區域別
	 **/ 
	public String getElf442_risk_area() {
		return elf442_risk_area;
	}

	/** 
	 * 設定行業對象別
	 **/
	public void setElf442_bus_cd(String value) {
		this.elf442_bus_cd = value;
	}

	/** 
	 * 取得行業對象別
	 **/
	public String getElf442_bus_cd() {
		return elf442_bus_cd;
	}

	/** 
	 * 設定行業對象別細分類
	 **/
	public void setElf442_bus_sub_cd(String value) {
		this.elf442_bus_sub_cd = value;
	}

	/** 
	 * 取得行業對象別細分類
	 **/
	public String getElf442_bus_sub_cd() {
		return elf442_bus_sub_cd;
	}

	/** 
	 * 設定預約核准註記
	 **/
	public void setElf442_proved_flag(String value) {
		this.elf442_proved_flag = value;
	}

	/** 
	 * 取得預約核准註記
	 **/
	public String getElf442_proved_flag() {
		return elf442_proved_flag;
	}

	public void setElf442_cn_loan_fg(String value) {
		this.elf442_cn_loan_fg = value;
	}

	public String getElf442_cn_loan_fg() {
		return elf442_cn_loan_fg;
	}

	public void setElf442_aloan_empid(String value) {
		this.elf442_aloan_empid = value;
	}

	public String getElf442_aloan_empid() {
		return elf442_aloan_empid;
	}

	/** 
	 * 設定產品種類
	 **/
	public void setElf442_prod_class(String value) {
		this.elf442_prod_class = value;
	}

	/** 
	 * 取得產品種類
	 **/
	public String getElf442_prod_class() {
		return elf442_prod_class;
	}

	/** 
	 * 設定土地面積
	 */
	public void setElf442_land_area(BigDecimal value) {
		this.elf442_land_area = value;
	}

	/** 
	 * 取得土地面積
	 */
	public BigDecimal getElf442_land_area() {
		return elf442_land_area;
	}

	/** 設定預計取得建照日期 **/
	public void setElf442_build_date(Date value) {
		this.elf442_build_date = value;
	}

	/** 取得預計取得建照日期 **/
	public Date getElf442_build_date() {
		return elf442_build_date;
	}

	/** 
	 * 設定預計撥款至動工期間月數
	 */
	public void setElf442_wait_month(BigDecimal value) {
		this.elf442_wait_month = value;
	}

	/** 
	 * 取得預計撥款至動工期間月數
	 */
	public BigDecimal getElf442_wait_month() {
		return elf442_wait_month;
	}

	/** 
	 * 設定擔保品座落區
	 **/ 
	public void setElf442_location_cd(String value) {
		this.elf442_location_cd = value;
	}

	/** 
	 * 取得擔保品座落區
	 **/ 
	public String getElf442_location_cd() {
		return elf442_location_cd;
	}

	/** 
	 * 設定座落區段
	 */
	public void setElf442_site3no(BigDecimal value) {
		this.elf442_site3no = value;
	}

	/** 
	 * 取得座落區段
	 */
	public BigDecimal getElf442_site3no() {
		return elf442_site3no;
	}

	/** 
	 * 設定座落區村里
	 **/ 
	public void setElf442_site4no(String value) {
		this.elf442_site4no = value;
	}

	/** 
	 * 取得座落區村里
	 **/ 
	public String getElf442_site4no() {
		return elf442_site4no;
	}

	/** 
	 * 設定土地使用分區
	 **/
	public void setElf442_land_type(String value) {
		this.elf442_land_type = value;
	}

	/** 
	 * 取得土地使用分區
	 **/
	public String getElf442_land_type() {
		return elf442_land_type;
	}
	
	public void setElf442_restrict_fg(String value) {
		this.elf442_restrict_fg = value;
	}

	public String getElf442_restrict_fg() {
		return elf442_restrict_fg;
	}
}
