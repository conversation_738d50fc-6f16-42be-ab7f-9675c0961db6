
package com.mega.eloan.lms.mfaloan.bean;

import javax.persistence.Column;

import tw.com.iisi.cap.model.GenericBean;

/** 萬用定義檔 **/
public class LNF07A extends GenericBean {

	private static final long serialVersionUID = 1L;

	@Column(name = "LNF07A_KEY_1", length = 50, columnDefinition = "CHAR(50)")
	private String lnf07a_key_1;
	
	@Column(name = "LNF07A_KEY_2", length = 50, columnDefinition = "CHAR(50)")
	private String lnf07a_key_2;
	
	@Column(name = "LNF07A_KEY_3", length = 50, columnDefinition = "CHAR(50)")
	private String lnf07a_key_3;
	
	@Column(name = "LNF07A_KEY_4", length = 50, columnDefinition = "CHAR(50)")
	private String lnf07a_key_4;
	
	@Column(name = "LNF07A_KEY_5", length = 50, columnDefinition = "CHAR(50)")
	private String lnf07a_key_5;
	
	@Column(name = "LNF07A_EXPLAIN", length = 100, columnDefinition = "CHAR(100)")
	private String lnf07a_explain;
	
	@Column(name = "LNF07A_CONTENT_1", length = 250, columnDefinition = "VARCHAR(250)")
	private String lnf07a_content_1;
	
	@Column(name = "LNF07A_CONTENT_2", length = 250, columnDefinition = "VARCHAR(250)")
	private String lnf07a_content_2;
	
	@Column(name = "LNF07A_CONTENT_3", length = 250, columnDefinition = "VARCHAR(250)")
	private String lnf07a_content_3;
	
	@Column(name = "LNF07A_CONTENT_4", length = 250, columnDefinition = "VARCHAR(250)")
	private String lnf07a_content_4;
	
	@Column(name = "LNF07A_CONTENT_5", length = 250, columnDefinition = "VARCHAR(250)")
	private String lnf07a_content_5;

	public String getLnf07a_key_1() {
		return lnf07a_key_1;
	}

	public void setLnf07a_key_1(String lnf07a_key_1) {
		this.lnf07a_key_1 = lnf07a_key_1;
	}

	public String getLnf07a_key_2() {
		return lnf07a_key_2;
	}

	public void setLnf07a_key_2(String lnf07a_key_2) {
		this.lnf07a_key_2 = lnf07a_key_2;
	}

	public String getLnf07a_key_3() {
		return lnf07a_key_3;
	}

	public void setLnf07a_key_3(String lnf07a_key_3) {
		this.lnf07a_key_3 = lnf07a_key_3;
	}

	public String getLnf07a_key_4() {
		return lnf07a_key_4;
	}

	public void setLnf07a_key_4(String lnf07a_key_4) {
		this.lnf07a_key_4 = lnf07a_key_4;
	}

	public String getLnf07a_key_5() {
		return lnf07a_key_5;
	}

	public void setLnf07a_key_5(String lnf07a_key_5) {
		this.lnf07a_key_5 = lnf07a_key_5;
	}

	public String getLnf07a_explain() {
		return lnf07a_explain;
	}

	public void setLnf07a_explain(String lnf07a_explain) {
		this.lnf07a_explain = lnf07a_explain;
	}

	public String getLnf07a_content_1() {
		return lnf07a_content_1;
	}

	public void setLnf07a_content_1(String lnf07a_content_1) {
		this.lnf07a_content_1 = lnf07a_content_1;
	}

	public String getLnf07a_content_2() {
		return lnf07a_content_2;
	}

	public void setLnf07a_content_2(String lnf07a_content_2) {
		this.lnf07a_content_2 = lnf07a_content_2;
	}

	public String getLnf07a_content_3() {
		return lnf07a_content_3;
	}

	public void setLnf07a_content_3(String lnf07a_content_3) {
		this.lnf07a_content_3 = lnf07a_content_3;
	}

	public String getLnf07a_content_4() {
		return lnf07a_content_4;
	}

	public void setLnf07a_content_4(String lnf07a_content_4) {
		this.lnf07a_content_4 = lnf07a_content_4;
	}

	public String getLnf07a_content_5() {
		return lnf07a_content_5;
	}

	public void setLnf07a_content_5(String lnf07a_content_5) {
		this.lnf07a_content_5 = lnf07a_content_5;
	}
	
}
