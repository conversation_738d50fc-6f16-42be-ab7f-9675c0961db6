/* 
 * UnapprDocService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;

/**
 * <pre>
 * 取消覆核流程
 * </pre>
 * 
 * @since 2012/6/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/6/26,REX,new
 *          </ul>
 */
public interface UnapprDocService {
	/**
	 * 取得grid 資料
	 * 
	 * @param clazz
	 * @param search
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	/**
	 * 儲存
	 * 
	 * @param entity
	 *            model
	 */
	public void save(GenericBean... entity);

	/**
	 * 取得該oid model
	 * 
	 * @param <T>
	 * @param clazz
	 * @param oid
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid);
}
