package com.mega.eloan.lms.lrs.flow;

import java.util.LinkedList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.dao.L170M01ADao;
import com.mega.eloan.lms.dao.L170M01GDao;
import com.mega.eloan.lms.lrs.constants.lrsConstants;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01G;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 企金覆審 - 流程執行
 * </pre>
 * 
 * @since 2011/04/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/04/17 new
 *          </ul>
 */

@Component
public class LMS1705Flow extends AbstractFlowHandler {

	@Resource
	UserInfoService userInfoService;
	@Resource
	L170M01ADao l170m01aDao;
	@Resource
	L170M01GDao l170m01gDao;
	
	/** 尋找L170M01G
	 * @param mainId
	 * @param branchType
	 * @param staffJob
	 * @return
	 */
	private L170M01G setL170M01GData(String mainId,String branchType,String staffJob){
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L170M01G l170m01g = l170m01gDao.findByBranchTypeStaffJob(mainId, branchType, staffJob);
//		List<L170M01G> l170m01gList = l170m01gDao.findByMainId(mainId);
//		for(L170M01G bean : l170m01gList){
//			if(branchType.equals(bean.getBranchType()) && staffJob.equals(bean.getStaffJob())){
//				l170m01g = bean;
//			}
//		}
		if(l170m01g == null) {
			l170m01g = new L170M01G();
			l170m01g.setMainId(mainId);
			l170m01g.setBranchType(branchType);
			l170m01g.setStaffJob(staffJob);
		}
		l170m01g.setBranchId(user.getUnitNo());
		l170m01g.setStaffNo(user.getUserId());
		l170m01g.setStaffName(user.getUserCName());
		l170m01g.setCreator(user.getUserId());
		l170m01g.setUpdater(user.getUserId());
		l170m01g.setCreateTime(CapDate.getCurrentTimestamp());
		l170m01g.setUpdateTime(CapDate.getCurrentTimestamp());
		return l170m01g;
	}

	@Transition(node="編製中", value="送待受檢單位")
	public void flow1(FlowInstance instance) {
		String mainId = (String) instance.getAttribute("mainId");
		//2. 覆審單位 L1. 分行經辦(授管處/營運中心)
		L170M01G l170m01g = this.setL170M01GData(mainId, lrsConstants.BRANCHTYPE.覆審單位, UtilConstants.STAFFJOB.經辦L1);
		l170m01gDao.save(l170m01g);
	}
	
	@Transition(node="待受檢單位回覆", value="確定")
	public void flow2(FlowInstance instance) {
	}
	
	@Transition(node="受檢確認", value="通過")
	public void flow3(FlowInstance instance) {
		String mainId = (String) instance.getAttribute("mainId");
		//1. 受檢單位 L1. 分行經辦(授管處/營運中心)
		L170M01G l170m01g = this.setL170M01GData(mainId, lrsConstants.BRANCHTYPE.受檢單位, UtilConstants.STAFFJOB.經辦L1);
		l170m01gDao.save(l170m01g);
	}

	@Transition(node="受檢確認", value="退回")
	public void flow4(FlowInstance instance) {
		String mainId = (String) instance.getAttribute("mainId");
		L170M01G l170m01g = null;
		l170m01g = l170m01gDao.findByBranchTypeStaffJob(mainId, lrsConstants.BRANCHTYPE.覆審單位, UtilConstants.STAFFJOB.經辦L1);
		if(l170m01g != null)
			l170m01gDao.delete(l170m01g);
	}

	@Transition(node="編製完成", value="呈主管")
	public void flow5(FlowInstance instance) {
		String mainId = (String) instance.getAttribute("mainId");
		//1. 受檢單位 L1. 分行經辦(授管處/營運中心)
		L170M01G l170m01g = this.setL170M01GData(mainId, lrsConstants.BRANCHTYPE.受檢單位, UtilConstants.STAFFJOB.單位授權主管L5);
		//由於此處是呈主管 所以需要紀錄的是主管的資料 資料為managerId
		String managerId = (String) instance.getAttribute("managerId");
		l170m01g.setBranchId(userInfoService.getUser(Util.nullToSpace(managerId)).getBrno());
		l170m01g.setStaffNo(managerId);
		l170m01g.setStaffName(userInfoService.getUserName(managerId));
		l170m01gDao.save(l170m01g);
		
	}

	@Transition(node="待覆核", value="確定")
	public void flow6(FlowInstance instance) {
		//String mainId = (String) instance.getAttribute("mainId");
	}

	@Transition(node="覆核確認", value="退回")
	public void flow7(FlowInstance instance) {
		String mainId = (String) instance.getAttribute("mainId");
		L170M01G l170m01g = null;
		List<L170M01G> l170m01gList = new LinkedList<L170M01G>();
		l170m01g = l170m01gDao.findByBranchTypeStaffJob(mainId, lrsConstants.BRANCHTYPE.覆審單位, UtilConstants.STAFFJOB.經辦L1);
		if(l170m01g != null) l170m01gList.add(l170m01g);
		l170m01g = l170m01gDao.findByBranchTypeStaffJob(mainId, lrsConstants.BRANCHTYPE.覆審單位, UtilConstants.STAFFJOB.執行覆核主管L4);
		if(l170m01g != null) l170m01gList.add(l170m01g);
		l170m01g = l170m01gDao.findByBranchTypeStaffJob(mainId, lrsConstants.BRANCHTYPE.覆審單位, UtilConstants.STAFFJOB.單位授權主管L5);
		if(l170m01g != null) l170m01gList.add(l170m01g);
		l170m01g = l170m01gDao.findByBranchTypeStaffJob(mainId, lrsConstants.BRANCHTYPE.受檢單位, UtilConstants.STAFFJOB.經辦L1);
		if(l170m01g != null) l170m01gList.add(l170m01g);
		l170m01g = l170m01gDao.findByBranchTypeStaffJob(mainId, lrsConstants.BRANCHTYPE.受檢單位, UtilConstants.STAFFJOB.執行覆核主管L4);
		if(l170m01g != null) l170m01gList.add(l170m01g);
		l170m01g = l170m01gDao.findByBranchTypeStaffJob(mainId, lrsConstants.BRANCHTYPE.受檢單位, UtilConstants.STAFFJOB.單位授權主管L5);
		if(l170m01g != null) l170m01gList.add(l170m01g);
		if(!l170m01gList.isEmpty())
			l170m01gDao.delete(l170m01gList);
	}

	@Transition(node="覆核確認", value="核定")
	public void flow8(FlowInstance instance) {
		String mainId = (String) instance.getAttribute("mainId");
		//1. 受檢單位 L1. 分行經辦(授管處/營運中心)
		L170M01G l170m01g = this.setL170M01GData(mainId, lrsConstants.BRANCHTYPE.受檢單位, UtilConstants.STAFFJOB.執行覆核主管L4);
		l170m01gDao.save(l170m01g);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String instanceId = instance.getId().toString();
		L170M01A l170m01a = l170m01aDao.findByOid(instanceId);
		//寫入文件核定者
		l170m01a.setApprover(user.getUserId());
		l170m01a.setApproveTime(CapDate.getCurrentTimestamp());
		l170m01aDao.save(l170m01a);
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L170M01A.class;
	}
	
	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return RetrialDocStatusEnum.class;
	}
}