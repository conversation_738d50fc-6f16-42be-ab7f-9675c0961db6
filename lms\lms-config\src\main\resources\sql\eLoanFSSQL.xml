<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
 xsi:schemaLocation="
 http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
 http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-2.0.xsd ">

 
<util:map id="eLoanFsSql" map-class="java.util.HashMap" key-type="java.lang.String">
    
    <!-- 取得簽報書敘述說明檔 -->
	<entry key="selL120m01d">
        <value>
         	SELECT
			(case t1.typcd when '5' then 'LMS' else 'LNS' end ) as SYSTYP<PERSON>,
			t1.oid,t1.mainid,t1.caseno,t1.custid,t1.dupno,t1.custName,t1.enddate,t1.typCd ,t1.docType,t1.docKind,t1.docCode,t1.caseBrId,t1.caseDate,t1.caseLvl,t1.docStatus,
			t2.itemtype,  t2.itemdscr  ,t2.itemTitle
			FROM 
			(SELECT * FROM lms.l120m01a WHERE DOCSTATUS IN ('05O') AND deletedTime IS NULL AND docType = '1' AND MAX(VALUE(CREATETIME,'0001-01-01-01.01.01.000000'),VALUE(UPDATETIME,'0001-01-01-01.01.01.000000'),VALUE(APPROVETIME,'0001-01-01-01.01.01.000000')) BETWEEN ? AND ? ) AS t1   
			LEFT OUTER JOIN 
			lms.l120m01d AS t2 
			ON 
			t1.mainid = t2.mainid 
			WHERE 
			t2.itemDscr is not NULL AND length(t2.itemDscr) != 0 
        </value>
    </entry>
	
	
	
	<!-- 取得額度明細表敘述說明檔 -->
	<entry key="selL140m01b">
        <value>
         	SELECT
			(case t1.typcd when '5' then 'LMS' else 'LNS' end ) as SYSTYPE,
			t1.oid,t1.mainid,t1.caseno,t1.custid,t1.dupno,t1.custName,t1.enddate,t1.typCd ,t1.docType,t1.docKind,t1.docCode,t1.caseBrId,t1.caseDate,t1.caseLvl,t1.docStatus,
			t4.mainId as cntrMainId,t4.itemtype,  
			t4.itemdscr  ,
			t4.toALoan  
			FROM 
			(SELECT * FROM lms.l120m01a WHERE DOCSTATUS IN ('05O') AND deletedTime IS NULL AND docType = '1' AND MAX(VALUE(CREATETIME,'0001-01-01-01.01.01.000000'),VALUE(UPDATETIME,'0001-01-01-01.01.01.000000'),VALUE(APPROVETIME,'0001-01-01-01.01.01.000000')) BETWEEN ? AND ? ) AS t1   
			LEFT OUTER JOIN
			lms.l120m01c AS t2
			ON 
			t1.mainid = t2.mainid 
			LEFT OUTER JOIN 
			(SELECT * FROM  lms.l140m01a WHERE DELETEDTIME IS NULL ) AS t3 
			ON
			t2.refmainid = t3.mainid 
			LEFT OUTER JOIN 
			lms.l140m01b AS t4 
			ON 
			t3.mainid = t4.mainid AND 
			t4.ITEMTYPE IS NOT NULL
			WHERE 
			t3.mainid IS NOT NULL  AND 
			t1.mainid IS NOT NULL  AND    
			( 
			  (t4.itemDscr is not NULL AND length(t4.itemDscr) != 0  )
			  or
			  (t4.toALoan is not NULL AND length(t4.toALoan) != 0   ) 
			)  
        </value>
    </entry>

		
    <!--取得借款人基本資料-->
	<entry key="selL120s01ab">
        <value>
         	SELECT
			(case t1.typcd when '5' then 'LMS' else 'LNS' end ) as SYSTYPE,
			t1.oid,t1.mainid,t1.caseno,t1.custid,t1.dupno,t1.custName,t1.enddate,t1.typCd ,t1.docType,t1.docKind,t1.docCode,t1.caseBrId,t1.caseDate,t1.caseLvl,t1.docStatus,
			t2.CUSTID AS BCUSTID,
			t2.DUPNO AS BDUPNO,
			t2.CUSTNAME AS BCUSTNAME,
			t2.CUSTNO as CUSTNO,
			t2.CMPNM as CMPNM,
			t2.RMK as RMK,
			t2.REJTREASON as REJTREASON,
			t2.REJTCASEADJMEMO as REJTCASEADJMEMO,
			t2.BLACKNAME as BLACKNAME,
			t2.REJTREASON1 as REJTREASON1,
			t2.REJTCASEADJMEMO1 as REJTCASEADJMEMO1,
			t3.POSDSCR as POSDSCR,
			t3.CHAIRMAN as CHAIRMAN,
			t3.GMANAGERDSCR as GMANAGERDSCR,
			t3.GMANAGER as GMANAGER,
			t3.STOCKHOLDER as STOCKHOLDER,
			t3.CMPADDR as CMPADDR,
			t3.FACTORYADDR as FACTORYADDR,
			t3.GROUPNAME as GROUPNAME,
			t3.BUSSITEM as BUSSITEM,
			t3.INVMDSCR as INVMDSCR,
			t3.ECONM as ECONM,
			t3.ECONM07A as ECONM07A,
			t3.BUSMEMO as BUSMEMO,
			t3.CCSMEMO as CCSMEMO,
			t3.PRIVATEEQUITYNAME as PRIVATEEQUITYNAME,
			t3.BFPRIVATEEQUITYNAME as BFPRIVATEEQUITYNAME,
			t3.BENEFICIARY as BENEFICIARY,
			t3.SENIORMGR as SENIORMGR,
			t3.CTRLPEO as CTRLPEO,
			t3.PRODMKT as PRODMKT,
			t3.NONEGRADE as NONEGRADE  ,
			t4.dataDscr as dataDscr1,
			t5.dataDscr as dataDscr2,
			t6.HASD1, 
			t6.HASD2, 
			t6.HASD3,
			t6.BADFAITHMEMO as BADFAITHMEMO,          
			t6.ITEM1_D1 as ITEM1_D1,  
			t6.ITEMID_D1 as ITEMID_D1,  
			t6.ITEMDUPNO_D1 as ITEMDUPNO_D1,  
			t6.ITEMNAME_D1 as ITEMNAME_D1,  
			t6.ITEM2_D1 as ITEM2_D1,  
			t6.ITEMMEMO_D1 as ITEMMEMO_D1,  
			t6.ITEM3_D1 as ITEM3_D1,  
			t6.ITEM1_D2 as ITEM1_D2,  
			t6.ITEMID_D2 as ITEMID_D2,  
			t6.ITEMDUPNO_D2 as ITEMDUPNO_D2,  
			t6.ITEMNAME_D2 as ITEMNAME_D2,  
			t6.ITEM2_D2 as ITEM2_D2,  
			t6.ITEMMEMO_D2 as ITEMMEMO_D2,  
			t6.ITEM1_D3 as ITEM1_D3,  
			t6.ITEMID_D3 as ITEMID_D3,  
			t6.ITEMDUPNO_D3 as ITEMDUPNO_D3,  
			t6.ITEMNAME_D3 as ITEMNAME_D3,  
			t6.ITEM2_D3 as ITEM2_D3,  
			t6.ITEMMEMO_D3 as ITEMMEMO_D3
			FROM 
			(SELECT * FROM lms.l120m01a WHERE DOCSTATUS IN ('05O') AND deletedTime IS NULL AND docType = '1' AND MAX(VALUE(CREATETIME,'0001-01-01-01.01.01.000000'),VALUE(UPDATETIME,'0001-01-01-01.01.01.000000'),VALUE(APPROVETIME,'0001-01-01-01.01.01.000000')) BETWEEN ? AND ? ) AS t1  
			LEFT OUTER JOIN 
			(select * from lms.l120s01a where custid != '' and custid IS NOT NULL  and dupno != '' and dupno  IS NOT NULL) AS t2
			ON 
			t1.mainid = t2.mainid 
			LEFT OUTER JOIN 
			(select * from lms.l120s01b where custid != '' and custid IS NOT NULL  and dupno != '' and dupno  IS NOT NULL) AS t3 
			ON 
			t3.mainid = t2.mainid and 
			t3.custid = t2.custid and 
			t3.dupno = t2.dupno
			LEFT OUTER JOIN 
			lms.l120s01g AS t4
			ON 
			t4.mainid = t1.mainid and 
			t4.custid = t2.custid and 
			t4.dupno = t2.dupno and 
			t4.dataType = '1'
			LEFT OUTER JOIN 
			lms.l120s01g AS t5
			ON 
			t5.mainid = t1.mainid and 
			t5.custid = t2.custid and 
			t5.dupno = t2.dupno   and 
			t5.dataType = '2'
			LEFT OUTER JOIN 
			lms.l120s01q AS t6
			ON 
			t6.mainid = t1.mainid and 
			t6.custid = t2.custid and 
			t6.dupno = t2.dupno   
			where 
			t1.mainid is not null  
        </value>
    </entry>	
	
	
	
	<!--取得額度明細表資料-->
	<!--
			SELECT
			t1.mainid,t1.caseno,t1.custid,t1.dupno,t1.custName,t1.enddate,t1.typCd ,t1.docType,t1.docKind,t1.docCode,t1.caseBrId,t1.caseDate,t1.caseLvl,t1.docStatus,
			t2.CUSTID AS BCUSTID,
			t2.DUPNO AS BDUPNO,
			t2.CUSTNAME AS BCUSTNAME,
			t2.headItem1,
			t2.gutPercent,
			t2.gutCutDate,
			t2.headItem2,
			t2.syndIPFD,
			t2.headItem3,
			t2.headItem3Ttl,
			t2.headItem5,
			t2.mRateType,
			t2.mRate,
			t2.property,
			t2.snoKind	,		
			t2.commSno,
			t2.cntrNo,
			t2.sbjProperty,
			t2.unitCase2,
			t2.headItem4,
			t2.isDerivatives,
			t2.currentApplyCurr,
			t2.currentApplyAmt,
			t2.reUse,
			t2.otherCurr,
			t2.useDeadline,
			t2.guarantorType,
			t2.noLoan,
			t2.residential,			
			t2.LNTYPE,			
			t2.IS722FLAG,
			t2.isBuy,
			t2.UNSECUREFLAG,
			t2.CASENO AS BCASENO,
			t2.CESRJTREASON as CESRJTREASON ,
			t2.PROPERTY,
			t2.GIST as GIST ,
			t2.LNSUBJECT as LNSUBJECT,
			t2.PAYDEADLINE as PAYDEADLINE,
			t2.DERIVATIVES,
			t2.BLMEMO as BLMEMO,
			t2.COUNTSAY as COUNTSAY,
			t2.DESP1 as DESP1,
			t2.CHECKNOTE as CHECKNOTE,
			t2.GUARANTOR as GUARANTOR,
			t2.GUARANTORMEMO as GUARANTORMEMO,
			t2.GUARANTOR1 as GUARANTOR1,
			t2.NOINSUREASONOTHER as NOINSUREASONOTHER,
			t2.RMK as RMK,
			t2.multiAmtFlag,
			t2.MULTIAMT as MULTIAMT,
			t2.MULTIASSUREAMT as MULTIASSUREAMT,
			t2.L140M01JSTR as L140M01JSTR,
			t2.DERVMULTIAMT as DERVMULTIAMT,
			t2.DERIVATIVESNUMDSCR as DERIVATIVESNUMDSCR,
			t2.FACILITYRATING as FACILITYRATING,
			t2.projClass,
			t4.custNo as custNo,
			t5.ntCode,
			t5.posType,
			t5.posDscr as posDscr,
			t5.chairmanId,
			t5.chairmanDupNo,
			t5.chairman as chairman,
			t5.gManagerDscr as gManagerDscr,
			t5.gManager as gManager,
			t5.rgtCurr,
			t5.rgtAmt,
			t5.rgtUnit,
			t5.cptlCurr,
			t5.cptlAmt,
			t5.cptlUnit,
			t5.stockHolder as stockHolder,
			t5.cmpAddr as cmpAddr,
			t5.factoryAddr as factoryAddr,
			t5.groupNo,
			t5.groupName as groupName,
			t5.bussItem as bussItem,
			t5.invMFlag,
			(CASE WHEN LENGTH(t5.invMDscr) != 0 then  t5.invMDscr ELSE '' END ) as invMDscr,
			t5.busCode,
			t5.ecoNm,
			t5.bussKind,
			t5.ecoNm07A,
			t5.custClass,
			t5.busMemo as busMemo,
			t5.ccsMemo as ccsMemo,
			t5.groupBadFlag,
			t5.privateEquityFg,
			t5.privateEquityNo,
			t5.privateEquityName as privateEquityName,
			t5.beneficiary as beneficiary,
			t5.seniorMgr as seniorMgr,
			t5.ctrlPeo as ctrlPeo,
			(CASE WHEN LENGTH(t5.PRODMKT) != 0 then  t5.PRODMKT ELSE '' END ) as PRODMKT ,  
			t6.mbRlt,
			t6.mbRltDscr as mbRltDscr , 
			t6.mhRlt44,
			t6.mhRlt45,
			t6.mhRlt44Dscr as mhRlt44Dscr , 
			t6.mhRlt45Dscr as mhRlt45Dscr , 
			t6.mbMhRltDscr as mbMhRltDscr , 
			t6.fctMbRlt,
			t6.fctMhRlt,
			t6.fctMhRltDscr as fctMhRltDscr , 
			t6.mbRlt33,
			t6.mbRltDscr33 as mbRltDscr33 , 
			t6.caRlt206,
			t6.caRlt206Dscr as caRlt206Dscr , 
			t6.localRlt,
			t6.localRltDscr as localRltDscr ,
			(CASE WHEN LENGTH(t7_2.itemDscr) != 0 then  t7_2.itemDscr ELSE '' END ) AS ITEMDSCR_1402,
			(CASE WHEN LENGTH(t7_3.itemDscr) != 0 then  t7_3.itemDscr ELSE '' END ) AS ITEMDSCR_1403,
			(CASE WHEN LENGTH(t7_4.itemDscr) != 0 then  t7_4.itemDscr ELSE '' END ) AS ITEMDSCR_1404,
			(CASE WHEN LENGTH(t7_5.itemDscr) != 0 then  t7_5.itemDscr ELSE '' END ) AS ITEMDSCR_1405,
			(CASE WHEN LENGTH(t8_2.itemDscr) != 0 then  t8_2.itemDscr ELSE '' END ) AS ITEMDSCR_1202,
			(CASE WHEN LENGTH(t8_3.itemDscr) != 0 then  t8_3.itemDscr ELSE '' END ) AS ITEMDSCR_1203,
			(CASE WHEN LENGTH(t8_4.itemDscr) != 0 then  t8_4.itemDscr ELSE '' END ) AS ITEMDSCR_1204,
			(CASE WHEN LENGTH(t8_E.itemDscr) != 0 then  t8_E.itemDscr ELSE '' END ) AS ITEMDSCR_120E
			FROM 
			(SELECT * FROM lms.l120m01a WHERE enddate is not null and  DOCSTATUS IN ('05O','06O') AND deletedTime IS NULL AND docType = '1' AND (enddate between ? AND ? OR approveTime between ? AND ? ) ) AS t1  
			LEFT OUTER JOIN 
			lms.l120m01c AS t3 
			ON 
			t1.mainid = t3.mainid 
			LEFT OUTER JOIN 
			(SELECT * FROM  lms.l140m01a WHERE DELETEDTIME IS NULL AND DOCSTATUS = '030' ) AS t2 
			ON 
			t2.mainid = t3.refmainid 
			LEFT OUTER JOIN 
			lms.l120s01a  AS t4
			ON 
			t4.mainid = t1.mainid AND 
			t4.custid = t2.custid AND 
			t4.dupno = t2.DUPNO
			LEFT OUTER JOIN 
			lms.l120s01b  AS t5
			ON 
			t5.mainid = t4.mainid AND 
			t5.custid = t4.custid AND 
			t5.dupno = t4.DUPNO
			LEFT OUTER JOIN 
			lms.l120s01d  AS t6
			ON 
			t6.mainid = t4.mainid AND 
			t6.custid = t4.custid AND 
			t6.dupno = t4.DUPNO
			LEFT OUTER JOIN 
			lms.l140m01b  AS t7_2
			ON 
			t7_2.mainid = t2.mainid AND 
			t7_2.itemType = '2'
			
			LEFT OUTER JOIN 
			lms.l140m01b  AS t7_3
			ON 
			t7_3.mainid = t2.mainid AND 
			t7_3.itemType = '3'
			
			LEFT OUTER JOIN 
			lms.l140m01b  AS t7_4
			ON 
			t7_4.mainid = t2.mainid AND 
			t7_4.itemType = '4'
			
			LEFT OUTER JOIN 
			lms.l140m01b  AS t7_5
			ON 
			t7_5.mainid = t2.mainid AND 
			t7_5.itemType = '5'
			
			LEFT OUTER JOIN 
			lms.l120m01d  AS t8_2
			ON 
			t8_2.mainid = t1.mainid AND 
			t8_2.itemType = '2'
			
			LEFT OUTER JOIN 
			lms.l120m01d  AS t8_3
			ON 
			t8_3.mainid = t1.mainid AND 
			t8_3.itemType = '3'
			
			LEFT OUTER JOIN 
			lms.l120m01d  AS t8_4
			ON 
			t8_4.mainid = t1.mainid AND 
			t8_4.itemType = '4'
			
			LEFT OUTER JOIN 
			lms.l120m01d  AS t8_E
			ON 
			t8_E.mainid = t1.mainid AND 
			t8_E.itemType = 'E'
			WHERE 
			t1.mainid IS NOT NULL  and 
			t3.mainid is not null and 
			t4.mainid is not null 
			-->
	<entry key="selL140m01a">
        <value>
			SELECT
			(case t1.typcd when '5' then 'LMS' else 'LNS' end ) as SYSTYPE,
			t1.oid,t1.mainid,t1.caseno,t1.custid,t1.dupno,t1.custName,t1.enddate,t1.typCd ,t1.docType,t1.docKind,t1.docCode,t1.caseBrId,t1.caseDate,t1.caseLvl,t1.docStatus,
			t2.CUSTID AS BCUSTID,
			t2.DUPNO AS BDUPNO,
			t2.CUSTNAME AS BCUSTNAME,
			t2.mainId as cntrMainId,
			t2.headItem1,
			t2.gutPercent,
			t2.gutCutDate,
			t2.headItem2,
			t2.syndIPFD,
			t2.headItem3,
			t2.headItem3Ttl,
			t2.headItem5,
			t2.mRateType,
			t2.mRate,
			t2.property,
			t2.snoKind	,		
			t2.commSno,
			t2.cntrNo,
			t2.sbjProperty,
			t2.unitCase2,
			t2.headItem4,
			t2.isDerivatives,
			t2.currentApplyCurr,
			t2.currentApplyAmt,
			t2.reUse,
			t2.otherCurr,
			t2.useDeadline,
			t2.guarantorType,
			t2.noLoan,
			t2.residential,			
			t2.LNTYPE,			
			t2.IS722FLAG,
			t2.isBuy,
			t2.UNSECUREFLAG,
			t2.CASENO AS BCASENO,
			t2.CESRJTREASON as CESRJTREASON ,
			t2.GIST as GIST ,
			t2.LNSUBJECT as LNSUBJECT,
			t2.PAYDEADLINE as PAYDEADLINE,
			t2.DERIVATIVES,
			t2.BLMEMO as BLMEMO,
			t2.COUNTSAY as COUNTSAY,
			t2.DESP1 as DESP1,
			t2.CHECKNOTE as CHECKNOTE,
			t2.GUARANTOR as GUARANTOR,
			t2.GUARANTORMEMO as GUARANTORMEMO,
			t2.GUARANTOR1 as GUARANTOR1,
			t2.NOINSUREASONOTHER as NOINSUREASONOTHER,
			t2.RMK as RMK,
			t2.multiAmtFlag,
			t2.MULTIAMT as MULTIAMT,
			t2.MULTIASSUREAMT as MULTIASSUREAMT,
			t2.L140M01JSTR as L140M01JSTR,
			t2.DERVMULTIAMT as DERVMULTIAMT,
			t2.DERIVATIVESNUMDSCR as DERIVATIVESNUMDSCR,
			t2.FACILITYRATING as FACILITYRATING,
			t2.projClass,
			t4.custNo as custNo,
			t5.ntCode,
			t5.posType,
			t5.posDscr as posDscr,
			t5.chairmanId,
			t5.chairmanDupNo,
			t5.chairman as chairman,
			t5.gManagerDscr as gManagerDscr,
			t5.gManager as gManager,
			t5.rgtCurr,
			t5.rgtAmt,
			t5.rgtUnit,
			t5.cptlCurr,
			t5.cptlAmt,
			t5.cptlUnit,
			t5.stockHolder as stockHolder,
			t5.cmpAddr as cmpAddr,
			t5.factoryAddr as factoryAddr,
			t5.groupNo,
			t5.groupName as groupName,
			t5.bussItem as bussItem,
			t5.invMFlag,
			(CASE WHEN LENGTH(t5.invMDscr) != 0 then  t5.invMDscr ELSE '' END ) as invMDscr,
			t5.busCode,
			t5.ecoNm,
			t5.bussKind,
			t5.ecoNm07A,
			t5.custClass,
			t5.busMemo as busMemo,
			t5.ccsMemo as ccsMemo,
			t5.groupBadFlag,
			t5.privateEquityFg,
			t5.privateEquityNo,
			t5.privateEquityName as privateEquityName,
			t5.beneficiary as beneficiary,
			t5.seniorMgr as seniorMgr,
			t5.ctrlPeo as ctrlPeo,
			(CASE WHEN LENGTH(t5.PRODMKT) != 0 then  t5.PRODMKT ELSE '' END ) as PRODMKT ,  
			t6.mbRlt,
			t6.mbRltDscr as mbRltDscr , 
			t6.mhRlt44,
			t6.mhRlt45,
			t6.mhRlt44Dscr as mhRlt44Dscr , 
			t6.mhRlt45Dscr as mhRlt45Dscr , 
			t6.mbMhRltDscr as mbMhRltDscr , 
			t6.fctMbRlt,
			t6.fctMhRlt,
			t6.fctMhRltDscr as fctMhRltDscr , 
			t6.mbRlt33,
			t6.mbRltDscr33 as mbRltDscr33 , 
			t6.caRlt206,
			t6.caRlt206Dscr as caRlt206Dscr , 
			t6.localRlt,
			t6.localRltDscr as localRltDscr ,
			(CASE WHEN LENGTH(t7_2.itemDscr) != 0 then  t7_2.itemDscr ELSE '' END ) AS ITEMDSCR_1402,
			(CASE WHEN LENGTH(t7_3.itemDscr) != 0 then  t7_3.itemDscr ELSE '' END ) AS ITEMDSCR_1403,
			(CASE WHEN LENGTH(t7_4.itemDscr) != 0 then  t7_4.itemDscr ELSE '' END ) AS ITEMDSCR_1404,
			(CASE WHEN LENGTH(t7_5.itemDscr) != 0 then  t7_5.itemDscr ELSE '' END ) AS ITEMDSCR_1405,
			t8.MAINBIZID,
			t8.MAINBIZNAME,
			t8.MAINBIZCNTRNO
			FROM 
			(SELECT * FROM lms.l120m01a WHERE DOCSTATUS IN ('05O') AND deletedTime IS NULL AND docType = '1' AND MAX(VALUE(CREATETIME,'0001-01-01-01.01.01.000000'),VALUE(UPDATETIME,'0001-01-01-01.01.01.000000'),VALUE(APPROVETIME,'0001-01-01-01.01.01.000000')) BETWEEN ? AND ? ) AS t1  
			LEFT OUTER JOIN 
			lms.l120m01c AS t3 
			ON 
			t1.mainid = t3.mainid 
			LEFT OUTER JOIN 
			(SELECT * FROM  lms.l140m01a WHERE DELETEDTIME IS NULL  ) AS t2 
			ON 
			t2.mainid = t3.refmainid 
			LEFT OUTER JOIN 
			lms.l120s01a  AS t4
			ON 
			t4.mainid = t1.mainid AND 
			t4.custid = t2.custid AND 
			t4.dupno = t2.DUPNO
			LEFT OUTER JOIN 
			lms.l120s01b  AS t5
			ON 
			t5.mainid = t4.mainid AND 
			t5.custid = t4.custid AND 
			t5.dupno = t4.DUPNO
			LEFT OUTER JOIN 
			lms.l120s01d  AS t6
			ON 
			t6.mainid = t4.mainid AND 
			t6.custid = t4.custid AND 
			t6.dupno = t4.DUPNO
			LEFT OUTER JOIN 
			lms.l140m01b  AS t7_2
			ON 
			t7_2.mainid = t2.mainid AND 
			t7_2.itemType = '2'
			
			LEFT OUTER JOIN 
			lms.l140m01b  AS t7_3
			ON 
			t7_3.mainid = t2.mainid AND 
			t7_3.itemType = '3'
			
			LEFT OUTER JOIN 
			lms.l140m01b  AS t7_4
			ON 
			t7_4.mainid = t2.mainid AND 
			t7_4.itemType = '4'
			
			LEFT OUTER JOIN 
			lms.l140m01b  AS t7_5
			ON 
			t7_5.mainid = t2.mainid AND 
			t7_5.itemType = '5'
			
			LEFT OUTER JOIN 
			lms.l140m01w  AS t8
			ON 
			t8.mainid = t2.mainid 
			
			WHERE 
			t1.mainid IS NOT NULL  and 
			t3.mainid is not null and 
			t4.mainid is not null  
			
	 </value>
    </entry>		
			
	<!--取得異常通報資料-->		
    <entry key="selL130m01a">
        <value>
         	SELECT 
			(case t1.typcd when '5' then 'LMS' else 'LNS' end ) as SYSTYPE,
			t1.oid,t1.mainid,t1.caseno,t1.custid,t1.dupno,t1.custName,t1.enddate,t1.typCd ,t1.docType,t1.docKind,t1.docCode,t1.caseBrId,t1.caseDate,t1.caseLvl,t1.docStatus,
			t2.CHAIRMAN as CHAIRMAN ,
			t2.PROCESS as PROCESS ,
			t2.COLLSTAT as COLLSTAT ,
			t2.AMT as AMT ,
			t2.REMAIND as REMAIND ,
			t2.BANKAMT as BANKAMT ,
			t2.BANKREMAIND as BANKREMAIND ,
			t2.GROUP as GROUP ,
			t2.SAMETOTAMT as SAMETOTAMT ,
			t2.GRPNAME as GRPNAME ,
			t2.SAMEIDEA as SAMEIDEA ,
			t2.REPORTDSCR as REPORTDSCR ,
			T3.SEQDSCR AS SEQDSCR_1 ,
			T4.SEQDSCR AS SEQDSCR_2 ,
			T5.SEQDSCR AS SEQDSCR_3 ,
			T6.SEQDSCR AS SEQDSCR_4 
			from
			(SELECT * FROM lms.l120m01a WHERE DOCSTATUS IN ('05O') AND deletedTime IS NULL AND docType = '1' AND docCode = '4' AND MAX(VALUE(CREATETIME,'0001-01-01-01.01.01.000000'),VALUE(UPDATETIME,'0001-01-01-01.01.01.000000'),VALUE(APPROVETIME,'0001-01-01-01.01.01.000000')) BETWEEN ? AND ? ) AS t1
			LEFT OUTER JOIN
			lms.l130m01a AS t2 
			ON 
			t1.mainid = t2.mainid 
			LEFT OUTER JOIN
			lms.l130m01b AS t3
			ON 
			t1.mainid = t3.mainid AND 
			t3.branchKind = '1'
			LEFT OUTER JOIN
			lms.l130m01b AS t4
			ON 
			t1.mainid = t4.mainid AND 
			t4.branchKind = '2'
			LEFT OUTER JOIN
			lms.l130m01b AS t5
			ON 
			t1.mainid = t5.mainid AND 
			t5.branchKind = '3'
			LEFT OUTER JOIN
			lms.l130m01b AS t6
			ON 
			t1.mainid = t6.mainid AND 
			t6.branchKind = '4' 
			WHERE t2.mainid IS NOT null
        </value>
    </entry>
	
	
	<!--取得簽報書主檔資料-->		
    <entry key="selL120m01a">
        <value>
         	SELECT 
			(case t1.typcd when '5' then 'LMS' else 'LNS' end ) as SYSTYPE,
			t1.oid,t1.mainid,t1.caseno,t1.custid,t1.dupno,t1.custName,t1.enddate,t1.typCd ,t1.docType,t1.docKind,t1.docCode,t1.caseBrId,t1.caseDate,t1.caseLvl,t1.docStatus,
			t1.REASONDESC as REASONDESC ,
		    t1.GIST as GIST ,
			t1.ITEMOFBUSI as ITEMOFBUSI ,
			t1.RPTTITLE1 as RPTTITLE1 ,
			t1.RPTTITLE2 as RPTTITLE2 ,
			t1.RPTTITLEAREA1 as RPTTITLEAREA1 ,
			t1.SIGNNO as SIGNNO ,
			t1.BACKREASON as BACKREASON ,
			t2.ITEMDSCR as ITEMDSCR ,
            t3.OPINION as OPINION 
			from
			(SELECT * FROM lms.l120m01a WHERE DOCSTATUS IN ('05O') AND deletedTime IS NULL AND docType = '1' AND MAX(VALUE(CREATETIME,'0001-01-01-01.01.01.000000'),VALUE(UPDATETIME,'0001-01-01-01.01.01.000000'),VALUE(APPROVETIME,'0001-01-01-01.01.01.000000')) BETWEEN ? AND ? ) AS t1
			LEFT OUTER JOIN
			lms.L121M01B AS t2 
			ON 
			t1.mainid = t2.mainid 
			LEFT OUTER JOIN
			lms.L120S10B AS t3
			ON 
			t1.mainid = t3.mainid 
        </value>
    </entry>			
	
	
	<!--取得會議決議檔資料-->		
    <entry key="selL120m01h">
        <value>
         	SELECT 
			(case t1.typcd when '5' then 'LMS' else 'LNS' end ) as SYSTYPE,
			t1.oid,t1.mainid,t1.caseno,t1.custid,t1.dupno,t1.custName,t1.enddate,t1.typCd ,t1.docType,t1.docKind,t1.docCode,t1.caseBrId,t1.caseDate,t1.caseLvl,t1.docStatus,
			t2.meetingType ,
			t2.gist as gist ,
			t2.dispWord as dispWord ,
			t2.quotaDesrc as quotaDesrc ,
			t2.meetingNote as meetingNote 
			from
			(SELECT * FROM lms.l120m01a WHERE DOCSTATUS IN ('05O') AND deletedTime IS NULL AND docType = '1' AND MAX(VALUE(CREATETIME,'0001-01-01-01.01.01.000000'),VALUE(UPDATETIME,'0001-01-01-01.01.01.000000'),VALUE(APPROVETIME,'0001-01-01-01.01.01.000000')) BETWEEN ? AND ? ) AS t1
			LEFT OUTER JOIN
			lms.L120M01H AS t2 
			ON 
			t1.mainid = t2.mainid 
			where t2.mainid is not null
        </value>
    </entry>			
			
	 
	
</util:map>
</beans>
