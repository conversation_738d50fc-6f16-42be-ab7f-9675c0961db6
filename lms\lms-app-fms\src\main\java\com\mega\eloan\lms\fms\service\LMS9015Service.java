/* 
 * LMS9015Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service;

import com.mega.eloan.lms.model.L901M01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

public interface LMS9015Service extends ICapService {

	/**
	 * findByOid
	 * 
	 * @param oid
	 * @return
	 */
	L901M01A findByOid(String oid);

	/**
	 * @param model
	 */
	void save(L901M01A model);

	/**
	 * @param model
	 */
	void delete(L901M01A model);

	/**
	 * 取得這銀行代號最大的ItemSeq
	 * 
	 * @param itemType
	 * @param branchId
	 * @return
	 */
	int findL9010m01fByItemSeqMax(String itemType, String branchId);

	/**
	 * 搜尋類別裡有幾筆資料撈出
	 * 
	 * @param clazz
	 * @param search
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	Page<? extends GenericBean> findPage(Class clazz, ISearch search);

}
