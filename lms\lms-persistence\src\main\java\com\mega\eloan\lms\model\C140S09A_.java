package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * The persistent class for the C140S09A database table.
 * </pre>
 * @since  2011/10/27
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/10/27,<PERSON>,new
 *          </ul>
 */
@StaticMetamodel(C140S09A.class)
public class C140S09A_ extends RelativeMeta_{
	public static volatile SingularAttribute<C140S09A, BigDecimal> gAbkAmt;
	public static volatile SingularAttribute<C140S09A, BigDecimal> gAbkBal;
	public static volatile SingularAttribute<C140S09A, String> gHolder;
	public static volatile SingularAttribute<C140S09A, String> gId;
	public static volatile SingularAttribute<C140S09A, String> grpId;
	public static volatile SingularAttribute<C140S09A, String> dupNo;
	public static volatile SingularAttribute<C140S09A, BigDecimal> gMbkAmt;
	public static volatile SingularAttribute<C140S09A, BigDecimal> gMbkBal;
	public static volatile SingularAttribute<C140S09A, String> gNa;
	public static volatile SingularAttribute<C140S09A, BigDecimal> gObuAmt;
	public static volatile SingularAttribute<C140S09A, BigDecimal> gObuBal;
	public static volatile SingularAttribute<C140S09A, BigDecimal> gOvsAmt;
	public static volatile SingularAttribute<C140S09A, BigDecimal> gOvsBal;
	public static volatile SingularAttribute<C140S09A, C140M01A> c140m01a;
}
