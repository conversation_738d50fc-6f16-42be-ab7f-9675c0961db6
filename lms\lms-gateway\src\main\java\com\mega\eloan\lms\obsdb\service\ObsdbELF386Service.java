package com.mega.eloan.lms.obsdb.service;

import java.math.BigDecimal;


/**
 * <pre>
 * 消金核准明細檔  ELF386
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
public interface ObsdbELF386Service {

	/**
	 * 新增個金資料檔
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param staffNo
	 * @param cusBan
	 * @param cusNm
	 * @param sDate
	 * @param isMates
	 * @param degreeCd
	 * @param pos
	 * @param seniority
	 * @param hIncome
	 * @param dRate
	 * @param oDep
	 * @param oBusiness
	 * @param cmsStatus
	 * @param credit
	 * @param yRate
	 * @param isPfund
	 * @param invMbal
	 * @param invObal
	 * @param loanPct
	 * @param isBins
	 * @param yPay
	 * @param oMoney
	 * @param updater
	 */
	void insertElf386(String brno, String custId, String dupNo,
			String cntrNo, String staffNo, String cusBan, String cusNm,
			String sDate, String isMates, String degreeCd, String pos,
			BigDecimal seniority, long hIncome, int dRate, long oDep,
			String oBusiness, String cmsStatus, String credit, int yRate,
			String isPfund, long invMbal, long invObal, long loanPct,
			String isBins, long yPay, long oMoney, String updater,
			BigDecimal timeNow);

	/**
	 * 刪除個金資料檔
	 * @param brno
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 */
	void delElf386(String brno, String custId, String dupNo, String cntrNo);

}
