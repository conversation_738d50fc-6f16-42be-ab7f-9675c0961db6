package com.mega.eloan.lms.batch.service.impl;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import javax.annotation.Resource;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.CapMvcParameters;
import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.gwclient.RPQSFTPClient;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.batch.report.LMSSEL001RptService;
import com.mega.eloan.lms.batch.report.LMSSEL002RptService;
import com.mega.eloan.lms.batch.report.LMSWEL003RptService;
import com.mega.eloan.lms.batch.report.LMSWEL004RptService;
import com.mega.eloan.lms.batch.report.LMSWEL005RptService;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

@Service("rpqsbatchpdfftpserviceimpl")
public class RPQSBatchPdfFtpServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger logger = LoggerFactory
			.getLogger(RPQSBatchPdfFtpServiceImpl.class);

	private final static String RPQS_TEMP_DIR = "/LMS/RPQS/";

	@Resource
	BranchService branchService;

	@Resource
	RPQSFTPClient rpqsFtpClient;

	@Resource
	SysParameterService sysParamService;

	@Resource
	C122M01ADao c122m01aDao;

	@Resource
	LMSWEL003RptService lmswel003RptService;

	@Resource
	LMSWEL004RptService lmswel004RptService;
	
	@Resource
	LMSWEL005RptService lmswel005RptService;

	@Resource
	LMSSEL001RptService lmssel001RptService;
	
	@Resource
	LMSSEL002RptService lmssel002RptService;


	@Override
	public JSONObject execute(JSONObject json) {
		boolean isSuccess = false;
		JSONObject result = null;
		JSONObject rq = json.getJSONObject("request");

		String act = rq.optString("act");
		String reportId = rq.optString("reportId");
		String msg = "";
		try {
			byte[] datas = null;
			if (Util.equals("procRpqs", act)) {
				boolean skipNoData = rq.optBoolean("skipNoData");
				PageParameters params = new CapMvcParameters();
				params.put("skipNoData", skipNoData);
				//授管處要求由月報改為週報
				if (Util.equals(reportId, "LLMEL019")) { 
					datas = lmswel003RptService.getContent(params);
				}
				else if (Util.equals(reportId, "LLMEL020")) { 
					datas = lmswel004RptService.getContent(params);
				} 
				else if (Util.equals(reportId, "LLMEL021")) {
					datas = lmswel005RptService.getContent(params);
				}
				// J-108-0092 消金無擔保放款比率季報表
				else if (Util.equals(reportId, "LLSEL001")) {
					params.put("yyyyMM", rq.optString("yyyyMM"));
					datas = lmssel001RptService.getContent(params);
				}
				// J-110-0395eloan線上貸款申貸資料查詢紀錄報表
				else if (Util.equals(reportId, "LLSEL002")) {
					params.put("reportDate", rq.optString("reportDate"));
					datas = lmssel002RptService.getContent(params);
				}

				if (datas != null) {
					// 產生報表檔
					int year = Calendar.getInstance().get(Calendar.YEAR) - 1911;
					String fullFileName = this.getRootPath()
							+ StrUtils.concat(reportId, ".", year,
									new SimpleDateFormat("MMdd")
											.format(new Date())) + "_918.PDF";
					if (Util.equals(reportId, "LLSEL001")
							|| Util.equals(reportId, "LLSEL002")) {
						fullFileName = fullFileName.replace("_918", "_943");
					}
					FileUtils.deleteQuietly(new File(fullFileName));
					FileUtils.writeByteArrayToFile(new File(fullFileName),
							datas);

					String fileName = FilenameUtils.getName(fullFileName);
					String msgId = IDGenerator.getUUID();

					// 送FTP
					logger.info("[execute] FTP Client : {}",
							rpqsFtpClient.toString());
					rpqsFtpClient.send(msgId, fullFileName,
							rpqsFtpClient.getServerDir(), fileName, true, true,
							false);
					logger.info("[execute] FTP上傳結果： {} ", ToStringBuilder
							.reflectionToString(rpqsFtpClient.list(msgId,
									rpqsFtpClient.getServerDir())));
				}
			} else {
				throw new CapException("未知的method【" + act + "】", getClass());
			}
			isSuccess = true;
		} catch (Throwable e) {
			msg = e.getMessage();
			logger.error(msg, e);
			isSuccess = false;
		}

		if (isSuccess) {
			result = WebBatchCode.RC_SUCCESS;// 此json object 內已包含 SUCCESS
			result.element(WebBatchCode.P_RESPONSE,
					result.get(WebBatchCode.P_RC_MSG));
		} else {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, msg);
			result.element(WebBatchCode.P_RESPONSE, msg);
		}
		return result;
	}

	/**
	 * 取得報表檔目錄，若不存在則建立之
	 * 
	 * @return 報表檔目錄
	 * @throws IOException
	 */
	private String getRootPath() throws IOException {
		String docFileRoot = this.sysParamService
				.getParamValue(SysParamConstants.SYS_DOCFILE_ROOT_DIR);

		docFileRoot += (RPQS_TEMP_DIR + new SimpleDateFormat("yyyyMM")
				.format(new Date()));
		File rootPath = new File(docFileRoot);
		if (!rootPath.exists()) {
			FileUtils.forceMkdir(new File(docFileRoot));
		}
		return docFileRoot + "/";
	}
//其次在於能夠下次來說明這
}
