/* 
 *  LMS9515ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.dao.C820M01ADao;
import com.mega.eloan.lms.eloandb.service.impl.AbstractEloandbJdbc;
import com.mega.eloan.lms.model.C820M01A;
import com.mega.eloan.lms.rpt.service.CLS9501V01Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class CLS9501V01ServiceImpl extends AbstractEloandbJdbc implements
		CLS9501V01Service {

	@Resource
	C820M01ADao c820m01aDao;

	private static final Logger logger = LoggerFactory
			.getLogger(CLS9501V01ServiceImpl.class);

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				// set updater and updateTime
				try {
					if (Util.isEmpty(model.get(EloanConstants.OID))) {
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}
					model.set("updater", user.getUserId());
					model.set("updateTime", CapDate.getCurrentTimestamp());
				} catch (CapException e) {
					logger.error("CapException!!", e);
				}

				if (model instanceof C820M01A) {
					c820m01aDao.save(((C820M01A) model));
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model instanceof C820M01A) {
				c820m01aDao.delete(((C820M01A) model));
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == C820M01A.class) {
			return c820m01aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == C820M01A.class) {
			C820M01A model = Util.isEmpty(oid) ? null : c820m01aDao
					.findByOid(oid);
			return (T) (model == null ? null : model);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == C820M01A.class) {
			List<C820M01A> list = Util.isEmpty(mainId) ? null : c820m01aDao
					.findByMainId(mainId);
			return (list == null ? null : list);
		}
		return null;
	}

	@Override
	public List<C820M01A> getAllSelects(String mainId) {
		ISearch search = c820m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "isSelected", "Y");
		return c820m01aDao.find(search);
	}

	@Override
	public List<Map<String, Object>> queryData(String brno, String dateYM) {
		return this.getJdbc().queryForList("queryData.cls9501v01",
				new String[] { brno, dateYM + "-%" });
	}
}
