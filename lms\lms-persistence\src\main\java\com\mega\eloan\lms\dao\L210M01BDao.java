/* 
 * L210M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L210M01B;

/** 異動聯貸案參貸比率簽章欄檔 **/
public interface L210M01BDao extends IGenericDao<L210M01B> {

	L210M01B findByOid(String oid);

	List<L210M01B> findByMainId(String mainId);

	L210M01B findByUniqueKey(String mainId, String branchType, String branchId,
			String staffNo, String staffJob);

	List<L210M01B> findByIndex01(String mainId, String branchType,
			String branchId, String staffNo, String staffJob);
}