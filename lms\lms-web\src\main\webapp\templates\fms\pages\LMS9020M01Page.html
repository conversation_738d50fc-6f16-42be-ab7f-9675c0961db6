<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
        <wicket:extend>
        	<script type="text/javascript" src="pagejs/fms/LMS9020M01Page.js"></script>
        	<div class="button-menu funcContainer" id="buttonPanel">
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04" />
					<wicket:message key="button.save">儲存</wicket:message>
				</button>
				<button id="btnExit" class="forview">
	                    <span class="ui-icon ui-icon-jcs-01"></span>
	                    <wicket:message key="button.exit">離開</wicket:message>
	           	</button>
			</div>
			<div class="tit2" id="stopDetail1" >
				<form id="formStopDetail1">
					<input type="hidden" id="oid" name="oid"/>
					<input type="hidden" id="mainId" name="mainId"/>					
					<table class="tb2" width="95%">
						<tr>
							<td width="25%" class="hd1"><wicket:message key="html.index1">私募基金代碼</wicket:message>&nbsp;&nbsp;</td>
							<td width="25%" colspan="3"><span class="color-blue" id="peNo" name="peNo"></span></td>
						</tr>
						<tr>
							<td width="25%" class="hd1"><wicket:message key="html.index2">私募基金名稱</wicket:message>&nbsp;&nbsp;</td>
							<td width="25%" colspan="3">
							   <input id="peName" name="peName"  size="100" maxlength="120" maxlengthC="40" class="required"/>
                            </td>
						</tr>
						<tr>
							<td class="hd1"><wicket:message key="html.index3">建立時間</wicket:message>&nbsp;&nbsp;</td>
							<td><span class="color-blue" id="createTime" name="createTime"></span></td>
							<td class="hd1"><wicket:message key="html.index4">建立人員</wicket:message>&nbsp;&nbsp;</td>
							<td><span class="color-blue" id="creator" name="creator"></span></td>
						</tr>
						<tr>
							<td class="hd1"><wicket:message key="html.index5">修改時間</wicket:message>&nbsp;&nbsp;</td>
							<td><span class="color-blue" id="updateTime" name="updateTime"></span></td>
							<td class="hd1"><wicket:message key="html.index6">修改人員</wicket:message>&nbsp;&nbsp;</td>
							<td><span class="color-blue" id="updater" name="updater"></span></td>
						</tr>
						<tr>
							<td class="hd1"><wicket:message key="html.index7">刪除時間</wicket:message>&nbsp;&nbsp;</td>
							<td>
								<span class="color-blue field" id="deletedTime" name="deletedTime"></span>
								<div id="showClearDeletedTime" style="display:none">
									<button type="button" onClick="pageAction.clearDeletedTime()">
										<span class="text-only"><wicket:message key="btn.index3">取消刪除</wicket:message></span>
									</button>
								</div>
							</td>
							<td class="hd1"> </td>
							<td> </td>
						</tr>
					</table>
				</form>
			</div>			
			<!-- 停權Grid明細 -->
			<div id="stopDetail2" style="display:none">
				<form id="formStopDetail2">
					<table width="100%" class="tb2">
						<tr>
							<td class="hd1">
								<wicket:message key="subGrid.index1">客戶統編</wicket:message>
							</td>
							<td>
								<input class="addNeed max upText required" type="text" id="custId" name="custId" size="10" maxlength="10"/>&nbsp;&nbsp;
								<br>
								<wicket:message key="subGrid.index2">重覆序號</wicket:message>
								<input class="addNeed max required" type="text" id="dupNo" name="dupNo" size="1" maxlength="1"/>
							</td>
							<td class="hd1">
								<wicket:message key="subGrid.index3">客戶名稱</wicket:message>
								<button type="button" onClick="pageAction.applyCustName()">
									<span class="text-only"><wicket:message key="btn.index4">引進戶名</wicket:message></span>
								</button>
							</td>
							<td>
								<input type="text" class="required" id="custName" name="custName" maxlength="120" maxlengthC="40" size="60" />
							</td>							
						</tr>						
																						
					</table>					
				</form>
			</div>
			<div class="funcContainer tit2">
				<form id="formStopBtn">
					<button type="button" onClick="pageAction.addDetail()">
						<span class="text-only"><wicket:message key="btn.index1">新增</wicket:message></span>
					</button>
					<button type="button" onClick="pageAction.delGridDetail()">
						<span class="text-only"><wicket:message key="btn.index2">刪除</wicket:message></span>
					</button>
					<button type="button" onClick="pageAction.undoGridDetail()">
						<span class="text-only"><wicket:message key="btn.index3">取消刪除</wicket:message></span>
					</button>
			        <div id="gridStopView">
			            <div id="gridStopDetail" name="gridStopDetail"></div>
			        </div>
				</form>
			</div>
        </wicket:extend>
    </body>
</html>
