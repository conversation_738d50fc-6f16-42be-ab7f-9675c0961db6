/* 
 * MisEJF315ServiceImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service.impl;

import java.text.MessageFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.constant.CapConstants;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.jdbc.EloanNamedJdbcTemplate;
import com.mega.eloan.common.service.EjcicLogService;
import com.mega.eloan.lms.ejcic.service.MisEJF315Service;

/**
 * <pre>
 * MIS.EJF315 -> MIS.DATADATE
 * </pre>
 * 
 * @since 2011/11/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/11,CP,new
 *          <li>2013/01/29,EL07623,修改查詢ITEMNAME
 *          </ul>
 */
@Service
public class MisEJF315ServiceImpl extends AbstractEjcicJdbc implements
		MisEJF315Service {

	EloanNamedJdbcTemplate njdbc;

	@Resource
	EjcicLogService ejcicLogService;

	@Resource(name = "ejcic-db")
	public void setNJdbc(DataSource dataSource) {
		njdbc = new EloanNamedJdbcTemplate(dataSource, GWException.GWTYPE_EJCIC, ejcicLogService);
		njdbc.setSqlProvider(super.sqlp);
		njdbc.setCauseClass(this.getClass());
	}

	public EloanNamedJdbcTemplate getNamedJdbc() {
		return njdbc;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.ejcic.service.MisEJF315Service#getQueryEJDataDate(
	 * java.lang.String)
	 */
	@Override
	public Map<String, Object> getQueryEJDataDate(String custId) {
		// SELECT ID, PRODID, ITEMNAME, CHAR(DATADATE) AS DATADATE, CHAR(QDATE)
		// AS QDATE
		// FROM MIS.DATADATE WHERE ID=? AND PRODID IN
		// ('P1','P2','P3','P4','P5','P7','P8') ORDER BY QDATE DESC FETCH FIRST
		// 1 ROW ONLY
		return getJdbc().queryForMap("DATADATE.findById",
				new String[] { custId });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.ejcic.service.MisEJF315Service#findByIdP1Bai001(java
	 * .lang.String)
	 */
	@Override
	public List<Map<String, Object>> findByIdP1Bai001(String custId) {
		// SELECT ID, PRODID, ITEMNAME, DATADATE, CHAR(QDATE) AS QDATE FROM
		// MIS.DATADATE WHERE ID=? AND PRODID = 'P1' AND
		// ITEMNAME = 'BAI001' ORDER BY int(replace(DATADATE,'/','')) DESC
		return getJdbc().queryForList("DATADATE.findByIdP1Bai001",
				new String[] { custId });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.ejcic.service.MisEJF315Service#findByIds(java.lang
	 * .String[])
	 */
	@Override
	public List<Map<String, Object>> findByIds(String[] custIds) {
		StringBuffer s1 = new StringBuffer();
		for (int i = 0; i < custIds.length; i++) {
			s1.append("?,");
		}
		if (custIds.length > 0)
			s1.deleteCharAt(s1.length() - 1);
		String sql = MessageFormat.format(getSqlBySqlId("DATADATE.findByIds"),
				new Object[] { s1.toString() });
		return getJdbc().queryForList(sql, custIds);
	}

	@Override
	public List<Map<String, Object>> findByIdAndNotInPIdAndItNm(String custId,
			String[] prdIds, String[] itemNms) {
		StringBuffer appendSQL = new StringBuffer();
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("ID", custId);
		if (prdIds != null && prdIds.length > 0 && !prdIds[0].isEmpty()) {
			// 當prdIds有值時
			map.put("ProdIds", Arrays.asList(prdIds));
			appendSQL.append(" AND PRODID NOT IN (:ProdIds) ");
		}
		if (itemNms != null && itemNms.length > 0 && !itemNms[0].isEmpty()) {
			// 當itemNms有值時
			map.put("ItemNames", Arrays.asList(itemNms));
			appendSQL.append(" AND ITEMNAME IN (:ItemNames) ");
		}
		appendSQL
				.append(" ORDER BY int(replace(QDATE,'/','')) DESC, int(replace(DATADATE,'/','')) DESC ");
		return getNamedJdbc().queryForList(
				"DATADATE.findMaxDataDateByIdAndPIdAndItNm",
				appendSQL.toString(), map);
	}// ;

	@Override
	public List<Map<String, Object>> findByIdAndPIdAndItNm(String custId,
			String[] prdIds, String[] itemNms) {
		StringBuffer appendSQL = new StringBuffer();
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("ID", custId);
		if (prdIds != null && prdIds.length > 0 && !prdIds[0].isEmpty()) {
			// 當prdIds有值時
			map.put("ProdIds", Arrays.asList(prdIds));
			appendSQL.append(" AND PRODID IN (:ProdIds) ");
		}
		if (itemNms != null && itemNms.length > 0 && !itemNms[0].isEmpty()) {
			// 當itemNms有值時
			map.put("ItemNames", Arrays.asList(itemNms));
			appendSQL.append(" AND ITEMNAME IN (:ItemNames) ");
		}
		appendSQL
				.append(" ORDER BY int(replace(QDATE,'/','')) DESC, int(replace(DATADATE,'/','')) DESC ");
		return getNamedJdbc().queryForList(
				"DATADATE.findMaxDataDateByIdAndPIdAndItNm",
				appendSQL.toString(), map);
	}// ;

	public String getMaxDateByIdAndPIdAndItNm(String custId, String[] prdIds,
			String[] itemNms) {
		StringBuffer appendSQL = new StringBuffer();
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("ID", custId);
		if (prdIds != null && prdIds.length > 0 && !prdIds[0].isEmpty()) {
			// 當prdIds有值時
			map.put("ProdIds", Arrays.asList(prdIds));
			appendSQL.append(" AND PRODID IN (:ProdIds) ");
		}
		if (itemNms != null && itemNms.length > 0 && !itemNms[0].isEmpty()) {
			// 當itemNms有值時
			map.put("ItemNames", Arrays.asList(itemNms));
			appendSQL.append(" AND ITEMNAME IN (:ItemNames) ");
		}
		appendSQL
				.append(" ORDER BY int(replace(QDATE,'/','')) DESC, int(replace(DATADATE,'/','')) DESC ");
		Map<String, Object> rtn = getNamedJdbc().queryForMap(
				"DATADATE.findMaxDataDateByIdAndPIdAndItNm",
				appendSQL.toString(), map);
		if (rtn != null && !rtn.isEmpty()) {
			return MapUtils.getString(rtn, "DATADATE",
					CapConstants.EMPTY_STRING);
		} else {
			return CapConstants.EMPTY_STRING;
		}
	}// ;

	@Override
	public String getMaxQDateByIdAndPIdAndItNm(String custId, String[] prdIds,
			String[] itemNms) {
		StringBuffer appendSQL = new StringBuffer();
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("ID", custId);
		if (prdIds != null && prdIds.length > 0 && !prdIds[0].isEmpty()) {
			// 當prdIds有值時
			map.put("ProdIds", Arrays.asList(prdIds));
			appendSQL.append(" AND PRODID IN (:ProdIds) ");
		}
		if (itemNms != null && itemNms.length > 0 && !itemNms[0].isEmpty()) {
			// 當itemNms有值時
			map.put("ItemNames", Arrays.asList(itemNms));
			appendSQL.append(" AND ITEMNAME IN (:ItemNames) ");
		}
		appendSQL.append(" ORDER BY int(replace(QDATE,'/','')) DESC ");

		Map<String, Object> rtn = getNamedJdbc().queryForMap(
				"DATADATE.findMaxQDateByIdAndPIdAndItNm", appendSQL.toString(),
				map);
		if (rtn != null && !rtn.isEmpty()) {
			return MapUtils.getString(rtn, "QDATE");
		} else {
			return CapConstants.EMPTY_STRING;
		}
	}// ;

	@Override
	public Map<String, Object> getQueryEJDataDateP2(String custId) {
		return getJdbc().queryForMap("DATADATE.findP2ById",
				new String[] { custId });
	}

	@Override
	public Map<String, Object> getQueryEJDataDateP3P4(String custId) {
		return getJdbc().queryForMap("DATADATE.findP3P4ById",
				new String[] { custId });
	}
}
