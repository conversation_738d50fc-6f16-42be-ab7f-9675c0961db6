package com.mega.eloan.lms.lrs.constants;

public interface lrsConstants {
	/** 簽章欄單位類型 */
	interface BRANCHTYPE {
		static final String 受檢單位 = "1";
		static final String 覆審單位 = "2";
	}

	interface CREATEBY {
		static final String 系統產生 = "SYS";
		static final String 人工產生 = "PEO";
	}
	
	interface mode {
		static final String _1 = "1";
		static final String _2 = "2";
		static final String _3 = "3";
		static final String _4 = "4";
	}
	
	interface docStatus1 {
		static final String 要覆審 = "1";
		static final String 不覆審 = "2";
	}
}
