package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 授信案件統計檔
 * </pre>
 * 
 * @since 2012/1/6
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/6,jessica,new
 *          </ul>
 */
public interface MisElcsecntService {

	/**
	 * (管理報表) Elcsecnt By 全部銀行代碼
	 * 
	 * @param yy
	 * @param mm
	 * @return
	 */
	List<Map<String, Object>> findElcsecntforType7ByAllBrNoAndDate(String yy,
			String mm);

	/**
	 * (管理報表) Find Elcsecnt By 全部銀行代碼 ,ctype=1(授權內)
	 * 
	 * @param yy
	 * @param mm
	 * @param ctype
	 * @param ctype2
	 * @return
	 */
	List<Map<String, Object>> findElcsecntforType8ByAllBrNoAndDate(String yy,
			String mm, String ctype);

	/**
	 * 授信簽報書上傳用 - 額度明細表 MIS.ELCSECNT 新增
	 * 
	 * @param brno
	 * @param appryy
	 * @param apprmm
	 * @param ctype
	 * @param caseDept
	 * @param citem1
	 * @param citem2
	 * @param citem3
	 * @param citem4
	 * @param citem5
	 * @param appramt
	 * @param updater
	 * @param caseNo
	 * @param cntrNo
	 * @param ISCOLLRT
	 * @param COLLRT
	 */
	public void insertElcsecnt(String brno, String appryy, String apprmm,
			String ctype, String caseDept, Integer citem1, Integer citem2,
			Integer citem3, Integer citem4, Integer citem5, BigDecimal appramt,
			String updater, String caseNo, String cntrNo, String ISCOLLRT,
			Double COLLRT, String comID, String comDupNo, String lnType,
			String caseLevel, String newCust);

	/**
	 * 授信簽報書上傳用- 額度明細表 MIS.ELCSECNT 更新
	 * 
	 * @param brno
	 * @param appryy
	 * @param apprmm
	 * @param ctype
	 * @param caseNo
	 * @param cntrNo
	 * @param citem1
	 * @param citem2
	 * @param citem3
	 * @param citem4
	 * @param citem5
	 * @param appramt
	 * @param updater
	 * @param caseDept
	 * @param ISCOLLRT
	 * @param COLLRT
	 */
	public void updateElcsecnt(String brno, String appryy, String apprmm,
			String ctype, String caseNo, String cntrNo, Integer citem1,
			Integer citem2, Integer citem3, Integer citem4, Integer citem5,
			BigDecimal appramt, String updater, String caseDept,
			String ISCOLLRT, Double COLLRT, String comID, String comDupNo,
			String lnType, String caseLevel, String newCust);

	boolean selectElcsecnt(String brno, String appryy, String apprmm,
			String ctype, String caseDept, String caseNo, String cntrNo);

	/**
	 * 授信簽報書上傳用 - 上傳授信簽報書 MIS.ELCSECNT 新增
	 * 
	 * @param brno
	 * @param appryy
	 * @param apprmm
	 * @param ctype
	 * @param caseNo
	 * @param cntrNo
	 * @param citem6
	 * @param citem7
	 * @param citem8
	 * @param citem9
	 * @param citem10
	 * @param appramt
	 * @param updater
	 * @param casedept
	 */
	void insertElcsecnt2(String brno, String appryy, String apprmm,
			String ctype, String caseNo, String cntrNo, int citem6, int citem7,
			int citem8, int citem9, int citem10, double appramt,
			String updater, String casedept, String comID, String comDupNo,
			String lnType, String caseLevel);

	/**
	 * 授信簽報書上傳用- 上傳授信簽報書 MIS.ELCSECNT 更新
	 * 
	 * @param brno
	 * @param appryy
	 * @param apprmm
	 * @param ctype
	 * @param caseNo
	 * @param cntrNo
	 * @param citem6
	 * @param citem7
	 * @param citem8
	 * @param citem9
	 * @param citem10
	 * @param appramt
	 * @param updater
	 * @param caseDept
	 */
	void updateElcsecnt2(String brno, String appryy, String apprmm,
			String ctype, String caseNo, String cntrNo, int citem6, int citem7,
			int citem8, int citem9, int citem10, double appramt,
			String updater, String caseDept, String comID, String comDupNo,
			String lnType, String caseLevel);

	boolean selectElcsecnt2(String brno, String appryy, String apprmm,
			String ctype, String caseNo, String cntrNo);

	/**
	 * 檢核青年創業及啟動金貸款有無在個金簽過
	 * 
	 * @param comId
	 *            公司統編
	 * @param comDupNo
	 *            重覆序號
	 * @param lnType
	 *            產品種類
	 * @param caseDept
	 *            1.企金 2.個金
	 * @return
	 */
	public Map<String, Object> findElcsecntforLnType(String comId,
			String comDupNo, String lnType, String caseDept);

	/**
	 * J-108-0242_05097_B1001 Web e-Loan每月常董會報告事項彙總及申報案件數統計表新做案件之筆數統計再區分為新戶及原授信戶
	 * 
	 * @param appryy
	 * @param apprmm
	 * @return
	 */
	public List<Map<String, Object>> selectElcsecnt_doLmsBatch0024(
			String appryy, String apprmm);

	/**
	 * J-108-0242_05097_B1001 Web e-Loan每月常董會報告事項彙總及申報案件數統計表新做案件之筆數統計再區分為新戶及原授信戶
	 * 
	 * @param brno
	 * @param appryy
	 * @param apprmm
	 * @param ctype
	 * @param caseDept
	 * @param caseNo
	 * @param cntrNo
	 * @param newCust
	 */
	public void updateElcsecnt_doLmsBatch0024(String brno, String appryy,
			String apprmm, String ctype, String caseDept, String caseNo,
			String cntrNo, String newCust);
}
