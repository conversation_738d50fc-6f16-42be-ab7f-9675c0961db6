package com.mega.eloan.lms.rpt.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 綜合評估 / 往來彙總(企金授權外)
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class LMSS07A_Panel extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public LMSS07A_Panel(String id) {
		super(id);
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		// add(new LMSS07Panel01("lmss07panel01"));
		// add(new LMSS07Panel02("lmss07panel02"));
		// add(new LMSS07Panel03("lmss07panel03"));
		// add(new LMSS07Panel04("lmss07panel04"));
		
		new LMS1301S05Panel01("lms1305s05panel01").processPanelData(model, params);

	}
}
