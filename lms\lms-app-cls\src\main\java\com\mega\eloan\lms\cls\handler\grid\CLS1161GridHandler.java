/* 
 * CLS1161GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.handler.grid;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.CustIdFormatter;
import com.mega.eloan.common.formatter.I18NFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMS2501Service;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.pages.CLS1161M01Page;
import com.mega.eloan.lms.cls.panels.CLS1161S22Panel;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.cls.service.CLS1151Service;
import com.mega.eloan.lms.cls.service.CLS1161Service;
import com.mega.eloan.lms.cls.service.CLS3301Service;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01B;
import com.mega.eloan.lms.model.C160M02A;
import com.mega.eloan.lms.model.C160M03A;
import com.mega.eloan.lms.model.C160S01A;
import com.mega.eloan.lms.model.C160S01B;
import com.mega.eloan.lms.model.C160S01C;
import com.mega.eloan.lms.model.C160S01F;
import com.mega.eloan.lms.model.C160S03A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01R;
import com.mega.eloan.lms.model.L140M03A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L141M01A;
import com.mega.eloan.lms.model.L250M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 動用審核表 GridHandler
 * </pre>
 * 
 * @since 2012/12/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/21,Fantasy,new
 *          <li>2013/06/17,Fantasy,修改取得簽報書之額度度明細表文件狀態為已核准
 *          <li>2013/08/06,Rex,#649修改未動用查詢方式 若為null 或空白 也算在未動用
 *          </ul>
 */
@Scope("request")
@Controller("cls1161gridhandler")
public class CLS1161GridHandler extends AbstractGridHandler {

	@Resource
	BranchService branchService;
	@Resource
	CodeTypeService cts;

	@Resource
	UserInfoService uis;

	@Resource
	LMSService lmsService;

	@Resource
	CLSService clsService;

	@Resource
	CLS1161Service service;

	@Resource
	MisdbBASEService mis;

	@Resource
	EloandbBASEService eloandbbaseservice;

	@Resource
	MisPTEAMAPPService mps;
	@Resource
	ProdService prodService;

	@Resource
	CLS1141Service cls1141Service;

	@Resource
	CLS1151Service cls1151Service;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	DwdbBASEService dwdbService;

	@Resource
	LMS2501Service lms2501Service;

	Properties prop;

	@Resource
	UserInfoService userInfoService;

	@Resource
	CLS3301Service cls3301Service;

	@Resource
	SysParameterService sysParameterService;

	Properties prop_AbstractEloanPage = MessageBundleScriptCreator
			.getComponentResource(AbstractEloanPage.class);

	/**
	 * 查詢動用審核表主檔
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryViewData(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		Map<String, String> map = cts
				.findByCodeType(ClsConstants.codeType.動審表種類);

		String docStatus = Util.trim(params.getString("docStatus"));
		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);
		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, null);

		// 客戶ID查詢
		String custId = Util.trim(params.getString("custId"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
			// String dupNo = Util.trim(params.getString("dupNo"));
			// if (Util.isNotEmpty(dupNo)) {
			// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
			// dupNo);
			// }
		}
		// 依核定期間查詢
		String fromDate = Util.trim(params.getString("fromDate"));
		String endDate = Util.trim(params.getString("endDate"));
		if (Util.isNotEmpty(fromDate) && Util.isNotEmpty(endDate)) {
			Object[] reason = { Util.parseDate(fromDate),
					Util.parseDate(endDate + " 23:59:59") };
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
					"approveTime", reason);
		}

		Page<? extends GenericBean> page = service.findPage(C160M01A.class,
				pageSetting);
		List<C160M01A> list = (List<C160M01A>) page.getContent();
		for (C160M01A model : list) {
			model.setCustId(Util.trim(model.getCustId()));
			model.setApprId(uis.getUserName(model.getApprId()));
			if (Util.equals(model.getCaseType(), "1")
					|| Util.equals(model.getCaseType(), "3")) {
				model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			} else if (Util.equals(model.getCaseType(), "2")) {
				String seq = Util.trim(model.getPackNo());
				if (!Util.isEmpty(seq)) {
					seq = "(" + seq + ")";
				}
				model.setCaseNo(Util.toSemiCharString(model.getLoanMasterNo()
						+ seq));
			}
			model.setCaseType(Util.trim(map.get(model.getCaseType())));
			model.setUseType(UtilConstants.DEFAULT.是.equals(Util.trim(model
					.getUseType())) ? "Ｖ" : "");
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 額度明細查詢
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult C160M01BQuery(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addOrderBy("createTime");
		Page<? extends GenericBean> page = service.findPage(C160M01B.class,
				pageSetting);
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		List<C160M01B> list = (List<C160M01B>) page.getContent();
		for (C160M01B model : list) {
			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setDataCheck(UtilConstants.DEFAULT.是.equals(Util.trim(model
					.getDataCheck())) ? "Ｖ" : "");
		}
		formatter.put("isChange", new isChangeFormatter());

		// return new CapGridResult(page.getContent(), page.getTotalRow(),
		// formatter);
		CapGridResult capGridResult = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return capGridResult;
	}

	public class isChangeFormatter implements IBeanFormatter {

		/**
		 * 
		 */
		private static final long serialVersionUID = -8263270098032527293L;

		@SuppressWarnings("unchecked")
		@Override
		public String reformat(Object in) throws CapFormatException {
			C160M01B model = (C160M01B) in;
			if (!"".equals(Util.trim(model.getBfLoanTotAmt()))) {
				BigDecimal bfAmt = ""
						.equals(Util.trim(model.getBfLoanTotAmt())) ? BigDecimal.ZERO
						: model.getBfLoanTotAmt();
				BigDecimal afAmt = "".equals(Util.trim(model.getLoanTotAmt())) ? BigDecimal.ZERO
						: model.getLoanTotAmt();
				return afAmt.compareTo(bfAmt) != 0 ? "Ｖ" : "";
			}
			return "";
		}

	}

	/**
	 * 擔保品查詢
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult C160S01AQuery(ISearch pageSetting,
			PageParameters params) throws CapException {
		Map<String, String> map = cts.findByCodeType("cms1090_collTyp1");

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		String refmainId = Util.trim(params.getString("refmainId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "refmainId",
				refmainId);
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "collTyp1",
		// UtilConstants.CollTyp1.不動產);
		Page<? extends GenericBean> page = service.findPage(C160S01A.class,
				pageSetting);
		List<C160S01A> list = (List<C160S01A>) page.getContent();
		for (C160S01A model : list) {
			model.setDocStatus(Util.trim(prop_AbstractEloanPage
					.getProperty("status." + Util.trim(model.getDocStatus()))));
			String CollTyp1 = Util.trim(model.getCollTyp1());
			model.setCollTyp1(CollTyp1 + " " + Util.trim(map.get(CollTyp1)));
		}
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		// Map<String, IFormatter> dataReformatter = new HashMap<String,
		// IFormatter>();
		// dataReformatter.put("branch", new BranchNameFormatter(branchService,
		// ShowTypeEnum.Name));
		// dataReformatter.put("collTyp1", new CodeTypeFormatter(cts,
		// "lmsUseCms_collTyp1"));

		// result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 主從債務人資料表查詢
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult C160S01BQuery(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		String refmainId = Util.trim(params.getString("refmainId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "refmainId",
				refmainId);
		Page<? extends GenericBean> page = service.findPage(C160S01B.class,
				pageSetting);
		List<C160S01B> c160s01bs = (List<C160S01B>) page.getContent();

		String[] codeType = { UtilConstants.CodeTypeItem.企業關係,
				UtilConstants.CodeTypeItem.親屬關係,
				UtilConstants.CodeTypeItem.綜合關係_企業,
				UtilConstants.CodeTypeItem.綜合關係_親屬 };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);

		for (C160S01B c160s01b : c160s01bs) {
			if (Util.isNotEmpty(Util.trim(c160s01b.getRKindD()))) {
				c160s01b.setRKindDStr(LMSUtil.changeCustRlt(
						(c160s01b.getRKindD()), codeMap));
			} else {
				c160s01b.setRKindDStr("");
			}
			c160s01b.setGuaPercentStr(c160s01b.getGuaPercent() == null ? ""
					: cls1151Service.fmt_l140s01a_guaPercent(c160s01b
							.getGuaPercent()) + ("%"));
		}

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		// dataReformatter.put("rType", new CodeTypeFormatter(codeTypeService,
		// "L140S01A_custPos"));
		dataReformatter.put("rType", new CodeTypeFormatter(codeTypeService,
				"lms1605s03_rType"));
		dataReformatter.put("rCountry", new CodeTypeFormatter(codeTypeService,
				"CountryCode"));
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 個金產品種類查詢
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult C160S01CQuery(ISearch pageSetting,
			PageParameters params) throws CapException {
		Map<String, String> ProdKindNameMap = prodService.getProdKindName();
		Map<String, String> SubCodeMap = prodService.getSubCode();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		String refmainId = Util.trim(params.getString("refmainId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "refmainId",
				refmainId);
		pageSetting.setOrderBy(ClsUtil.getOrderBy_uiSeq_seq());

		Page<? extends GenericBean> page = service.findPage(C160S01C.class,
				pageSetting);
		List<C160S01C> c160s01cs = (List<C160S01C>) page.getContent();
		for (C160S01C c160s01c : c160s01cs) {
			String prodKindNm = Util.trim(ProdKindNameMap.get(c160s01c
					.getProdKind()));

			if (CrsUtil.is_prodKind_in_63_64_65(c160s01c.getProdKind())) {
				// TODO 【can't use】
				// if(prodService.is_63_64_65(c160s01c.getProdKind())){
				// 程式緊急PUNCH申請單 PU-A2016-0180
				L140S02A l140s02a = clsService
						.findL140S02A_by_C160S01C(c160s01c);
				if (l140s02a != null) {
					String disasType = Util.trim(l140s02a.getDisasType());
					if (Util.isNotEmpty(disasType)) {
						Map<String, String> codeMap = clsService
								.get_disasType_desc();
						prodKindNm = prodKindNm + "-"
								+ LMSUtil.getDesc(codeMap, disasType);
					}
				}
			}
			c160s01c.setProdKind(prodKindNm);
			c160s01c.setSubjCode(Util.trim(SubCodeMap.get(c160s01c
					.getSubjCode())));
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 個金產品種類查詢
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult C160S01FQuery(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		String refmainId = Util.trim(params.getString("refmainId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "refmainId",
				refmainId);
		int seq = Util.parseInt(params.getString("seq"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);

		Page<? extends GenericBean> page = service.findPage(C160S01F.class,
				pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 簽報書查詢
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult L120M01AQuery(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 分行
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// 客戶統編
		String custId = Util.trim(params.getString("queryId")); // X101010107
																// A100063900
		String dupNo = Util.trim(params.getString("queryDupNo"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);

		if (Util.isNotEmpty(dupNo)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
					dupNo);
		}
		// 文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				CreditDocStatusEnum.海外_已核准.getCode());
		// 案件類別
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.個金);

		String dataDrc = Util.trim(params.getString("dataSrc"));
		// 聯行額度明細表
		if (UtilConstants.Usedoc.dataDrc.聯行額度明細表.equals(dataDrc)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					user.getUnitNo());
			Page<? extends GenericBean> page = lmsService.findPage(
					L141M01A.class, pageSetting);
			List<L141M01A> list = (List<L141M01A>) page.getContent();
			for (L141M01A model : list) {
				model.setMainId(model.getSrcMainId()); // set mainId
				model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			}

			return new CapGridResult(list, page.getTotalRow());
		}
		// 簽報書
		else {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
					user.getUnitNo());
			// 種類
			pageSetting.addSearchModeParameters(SearchMode.IN, "docCode",
					new String[] { UtilConstants.Casedoc.DocCode.一般,
							UtilConstants.Casedoc.DocCode.其他 });
			// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
			// "packLoan",
			// "N");
			pageSetting.addOrderBy("endDate", true);
			pageSetting.addOrderBy("caseYear", true); // 為讓 RPA 抓到最新一筆, 多加判斷
			pageSetting.addOrderBy("caseSeq", true);
			Page<? extends GenericBean> page = lmsService.findPage(
					L120M01A.class, pageSetting);

			List<L120M01A> list = (List<L120M01A>) page.getContent();
			for (L120M01A model : list) {
				model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));

			}
			if (list.size() == 0) {
				prop = MessageBundleScriptCreator
						.getComponentResource(CLS1161M01Page.class);
				throw new CapMessageException(
						prop.getProperty("cls1161.error1"), getClass());

			}
			return new CapGridResult(list, page.getTotalRow());
		}
	}

	/**
	 * 搜尋團貸年度總額度檔
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult PTEAMAPPQuery(ISearch pageSetting,
			PageParameters params) throws CapException {
		String caseType = Util.trim(params.getString("caseType"));

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		if (UtilConstants.Usedoc.caseType2.整批匯入.equals(caseType)) {
			String custId = Util.trim(params.getString("custId"));
			String dupNo = Util.trim(params.getString("dupNo"));
			list.addAll(mps.findListByIdNo(custId, Util.isEmpty(dupNo) ? "0"
					: dupNo));
		} else {
			String custId = Util.trim(params.getString("groupId"));
			String dupNo = Util.trim(params.getString("groupDupNo"));
			// custId = "12251662"; // test
			list.addAll(mps.getPTEAMAPPData(custId, Util.isEmpty(dupNo) ? "0"
					: dupNo));
		}
		if (list.size() == 0) {
			prop = MessageBundleScriptCreator
					.getComponentResource(CLS1161M01Page.class);
			throw new CapMessageException(prop.getProperty("cls1161.error1"),
					getClass());
		}
		return new CapMapGridResult(list, list.size());
	}

	/**
	 * 額度明細表查詢
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult L140M01AQuery(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 團貸
		String caseType = Util.trim(params.getString("caseType"));
		if (UtilConstants.Usedoc.caseType2.團貸.equals(caseType)) {
			String grpCntrNo = Util.trim(params.getString("GRPCNTRNO"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"l140m03a.grpCntrNo", grpCntrNo);
			String isUse = Util.trim(params.getString("isUse"));

			// 2013/08/06,Rex,#649修改未動用查詢方式 若為null 或空白 也算在未動用
			if (UtilConstants.DEFAULT.否.equals(isUse)) {
				pageSetting.addSearchModeParameters(SearchMode.OR,
						new SearchModeParameter(SearchMode.IS_NULL,
								"l140m03a.isUse", ""), new SearchModeParameter(
								SearchMode.OR, new SearchModeParameter(
										SearchMode.EQUALS, "l140m03a.isUse",
										"N"),
								new SearchModeParameter(SearchMode.EQUALS,
										"l140m03a.isUse", "")));
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"l140m03a.isUse", isUse);
			}
		}
		// 一般
		else {
			// mainId
			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			L120M01A l120m01a = lmsService.findModelByMainId(L120M01A.class,
					mainId);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"l120m01c.mainId", mainId);
			// docKind
			// String docKind = params.getString("docKind");
			String itemType = lmsService.checkL140M01AItemType(l120m01a);

			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"l120m01c.itemType", itemType);
		}
		// 額度序號
		pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL, "cntrNo",
				null);
		pageSetting
				.addSearchModeParameters(SearchMode.NOT_EQUALS, "cntrNo", "");
		// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
		// "docStatus",FlowDocStatusEnum.婉卻.getCode());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				FlowDocStatusEnum.已核准.getCode());
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "proPerty",
				UtilConstants.Cntrdoc.Property.取消);
		// 2013/06/27 GaryChan 明澤說不變 也不取
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "proPerty",
				UtilConstants.Cntrdoc.Property.不變);
		// 2013-11-05 遇到額度為0時也不取
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
				"currentApplyAmt", 0);

		// 刪除時間
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, null);
		Page<? extends GenericBean> page = lmsService.findPage(L140M01A.class,
				pageSetting);

		// 排除額度序號前三碼不是當前分行代碼 add by fantasy 2013/05/02

		int total = 0;
		List<L140M01A> output = new ArrayList<L140M01A>();
		List<L140M01A> list = (List<L140M01A>) page.getContent();
		String uintNo = Util.trim(MegaSSOSecurityContext.getUnitNo());
		for (L140M01A l140m01a : list) {
			String cntrNo = Util.trim(l140m01a.getCntrNo());
			if (cntrNo.startsWith(uintNo)) {
				L140M03A l140m03a = l140m01a.getL140M03A();
				L120M01C l120m01c = l140m01a.getL120m01c();
				// 為一般案時排除團貸編號不為空值的額度明細表
				if (UtilConstants.Usedoc.caseType2.一般.equals(caseType)) {
					if (!Util.isEmpty(Util.trim(l140m03a.getGrpCntrNo()))) {
						continue;
					}
					// 為團貸案時排除團貸編號為空值的額度明細表
				} else if (UtilConstants.Usedoc.caseType2.團貸.equals(caseType)) {
					if (Util.isEmpty(Util.trim(l140m03a.getGrpCntrNo()))) {
						continue;
					}
					// 當案件為核准時才需顯示
					if (l120m01c != null) {
						L120M01A l120m01a = lmsService.findModelByMainId(
								L120M01A.class, l120m01c.getMainId());
						if (l120m01a != null
								&& !CreditDocStatusEnum.海外_已核准.getCode()
										.equals(l120m01a.getDocStatus())) {
							continue;
						}
					}
				}
				// 2013/06/24 GaryChang 案號顯示全形轉半形
				l140m01a.setCaseNo(Util.toSemiCharString(Util.trim(l140m01a
						.getCaseNo())));
				output.add(l140m01a);
				total++;
			}
		}

		if (total == 0 && list.size() > 0) {
			logger.error("L140M01AQuery[caseType=" + caseType + "]"
					+ "[mainId="
					+ Util.trim(params.getString(EloanConstants.MAIN_ID)) + "]"
					+ "[grpCntrNo=" + Util.trim(params.getString("GRPCNTRNO"))
					+ "], get {" + list.size() + "} from DB, but result is {"
					+ total + "}");
		}
		return new CapGridResult(output, total);
	}

	/**
	 * 額度明細表查詢
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult BorrowersQuery(ISearch pageSetting,
			PageParameters params) throws CapException {
		// C160S01A model do Entity
		List<C160S01A> result = new ArrayList<C160S01A>();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));

		// 設主債務人
		C160S01A model = new C160S01A();
		model.setCustId(custId);
		model.setDupNo(dupNo);
		model.setCustName(custName);
		result.add(model);

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		String refmainId = Util.trim(params.getString("refmainId"));
		// refmainId = "4828a3853dad491191ec342c1d992f7e"; // test
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "refmainId",
				refmainId);
		// 相關身份
		String rType = Util.trim(params.getString("rType"));
		if (Util.isNotEmpty(rType)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rType",
					rType);
		}
		pageSetting.addOrderBy("custId");
		pageSetting.addOrderBy("rId");
		Page<? extends GenericBean> page = service.findPage(C160S01B.class,
				pageSetting);
		List<C160S01B> list = (List<C160S01B>) page.getContent();
		for (C160S01B c160s01b : list) {
			if (!custId.equals(c160s01b.getRId())
					|| !dupNo.equals(c160s01b.getRDupNo())) {
				C160S01A c160s01a = new C160S01A();
				c160s01a.setCustId(c160s01b.getRId());
				c160s01a.setDupNo(c160s01b.getRDupNo());
				c160s01a.setCustName(c160s01b.getRName());
				result.add(c160s01a);
			}
		}

		return new CapGridResult(result, result.size());
	}

	/**
	 * 查詢帳號
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult AccountQuery(ISearch pageSetting,
			PageParameters params) throws CapException {
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		List<Map<String, Object>> list = dwdbService
				.findDW_IDDP_DPF_Seqno_ByCustId(custId, dupNo);
		for (Map<String, Object> map : list) {
			StringBuilder sb = new StringBuilder();
			sb.append(Util.trim(map.get("BRNO")));
			sb.append(Util.trim(map.get("APCD")));
			sb.append(Util.trim(map.get("SEQNO")));
			map.put("Account", sb.toString());
		}

		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());

		// 以下寫法, 當客戶的帳號多筆, 某幾筆位在第2頁之後的資料, 在 grid 會出不來
		// return new CapMapGridResult(list, list.size());
	}

	/**
	 * 取得各項費用資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("serial")
	public CapGridResult queryL140M01R(ISearch pageSetting,
			PageParameters params) throws CapException {

		logger.debug("mainId : " + params.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID,
				params.getString(EloanConstants.MAIN_ID));

		Page<? extends GenericBean> page = cls1141Service.findPage(
				L140M01R.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		CodeTypeFormatter ctf = new CodeTypeFormatter(codeTypeService,
				"cls1141_feeNo");
		formatter.put("feeNo", ctf);
		formatter.put("feeSphere", new I18NFormatter("L140M01R."));

		formatter.put("caseNo", new IFormatter() {
			@Override
			@SuppressWarnings("unchecked")
			public String reformat(Object in) throws CapFormatException {
				if (in != null) {
					return Util.toSemiCharString((String) in);
				}
				return "";
			}
		});

		CapGridResult capGridResult = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return capGridResult;
	}

	/**
	 * 查詢個金借保人檔
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryC120M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		// String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		String findId = Util.trim(params.getString("findId"));
		if (Util.isNotEmpty(findId)) {
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId",
					findId + "%");
		}
		// 查這份文件的MinId
		String caseMainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
				caseMainId);
		Page<? extends GenericBean> page = cls1141Service.findPage(
				C120M01A.class, pageSetting);
		// 排除額度明細表本身借款人
		List<C120M01A> c120m01as = (List<C120M01A>) page.getContent();
		List<C120M01A> newC120M01as = new ArrayList<C120M01A>();
		for (C120M01A c120m01a : c120m01as) {
			newC120M01as.add(c120m01a);
		}

		CapGridResult result = new CapGridResult(newC120M01as,
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("custId", new CustIdFormatter());
		// dataReformatter.put("custPos", new CodeTypeFormatter(codeTypeService,
		// "L140S01A_custPos")); //
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢個金額度明細表檔
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		// String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		String findId = Util.trim(params.getString("findId"));
		if (Util.isNotEmpty(findId)) {
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId",
					findId + "%");
		}

		Page<? extends GenericBean> page = cls1141Service.findPage(
				L140M01A.class, pageSetting);
		List<L140M01A> l140m01as = (List<L140M01A>) page.getContent();
		List<L140M01A> newL140M01as = new ArrayList<L140M01A>();
		for (L140M01A l140m01a : l140m01as) {
			newL140M01as.add(l140m01a);
		}

		CapGridResult result = new CapGridResult(newL140M01as,
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("cntrno", new CustIdFormatter());
		// dataReformatter.put("custPos", new CodeTypeFormatter(codeTypeService,
		// "L140S01A_custPos")); //
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢動審表的案件編號
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryL120M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		List<Map<String, Object>> list = eloandbbaseservice
				.findL120M01AByC160M01A(mainId);
		for (Map<String, Object> map : list) {
			StringBuilder sb = new StringBuilder();
			sb.append(Util.toSemiCharString(Util.trim(map.get("CASENO"))));
			map.put("CASENO", sb.toString());
		}

		return new CapMapGridResult(list, list.size());
	}

	/**
	 * 查詢中鋼整批評等檔
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult queryViewC160M02A(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String search_custId = Util.trim(params.getString("search_custId"));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				user.getUnitNo());

		String docStatus = Util.trim(params.getString("docStatus"));
		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);
		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, null);

		if (Util.isNotEmpty(search_custId)) {
			List<String> joinMainId = get_c160mainIdSet_by_custId(search_custId);
			if (joinMainId.size() > 0) {
				pageSetting.addSearchModeParameters(SearchMode.IN, "mainId",
						joinMainId);
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"mainId", "none");
			}

		}
		Page<? extends GenericBean> page = service.findPage(C160M02A.class,
				pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	private List<String> get_c160mainIdSet_by_custId(String search_custId) {
		ISearch precond = clsService.getMetaSearch();
		precond.addSearchModeParameters(SearchMode.LIKE, "custId",
				search_custId + "%");
		Page<? extends GenericBean> precond_page = clsService.findPage(
				C120S01Q.class, precond);
		Set<String> c160mainIdSet = new HashSet<String>();
		for (GenericBean bean : precond_page.getContent()) {
			C120S01Q model = (C120S01Q) bean;
			String srcMainId = Util.trim(model.getSrcMainId());
			String mainId = Util.trim(model.getMainId());
			if (Util.isNotEmpty(srcMainId)) {
				c160mainIdSet.add(srcMainId);
			} else {
				c160mainIdSet.add(mainId);
			}
		}
		return new ArrayList<String>(c160mainIdSet);
	}

	/*
	 * private List<String> getC160MainIdArr(List<String> c160mainIdSet, String
	 * search_custId) { List<String> r = new ArrayList<String>(); if
	 * (c160mainIdSet.size() > 0) { ISearch mySearch =
	 * clsService.getMetaSearch();
	 * mySearch.addSearchModeParameters(SearchMode.LIKE, "custId", search_custId
	 * + "%"); mySearch.addSearchModeParameters(SearchMode.IN, "mainId",
	 * c160mainIdSet); Page<? extends GenericBean> c120page =
	 * clsService.findPage( C120M01A.class, mySearch); for (GenericBean bean :
	 * c120page.getContent()) { C120M01A c120m01a = (C120M01A) bean;
	 * r.add(c120m01a.getMainId()); } } return r; }
	 */

	/**
	 * 中鋼整批滙入查詢相關評分模型
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult queryScoreModel(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString("mainId"));
		String search_custId = Util.trim(params.getString("search_custId"));
		if (StringUtils.isNotEmpty(mainId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
					mainId);
			if (Util.isNotEmpty(search_custId)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId",
						search_custId + "%");
			}
			Page<? extends GenericBean> page = cls1141Service.findPage(
					C120M01A.class, pageSetting);
			prop = MessageBundleScriptCreator
					.getComponentResource(CLS1161S22Panel.class);
			Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

			formatter.put("varVer", new varVerFormatter());
			formatter.put("grade3", new grade3Formatter());
			formatter.put("grade2", new grade2Formatter());
			formatter.put("rawMarkModel", new rawMarkModelFormatter());

			CapGridResult result = new CapGridResult(page.getContent(),
					page.getTotalRow());
			result.setDataReformatter(formatter);
			return result;
		}
		return new CapGridResult();
	}

	public CapGridResult queryViewC160M03A(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String search_custId = Util.trim(params.getString("search_custId"));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				user.getUnitNo());

		String docStatus = Util.trim(params.getString("docStatus"));
		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);
		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, null);

		if (Util.isNotEmpty(search_custId)) {
			Page<? extends GenericBean> precond_page = service.findPage(
					C160M03A.class, pageSetting);
			List<String> c160mainIdSet = new ArrayList<String>();
			for (GenericBean bean : precond_page.getContent()) {
				C160M03A c160m03a = (C160M03A) bean;
				c160mainIdSet.add(c160m03a.getMainId());
			}

			List<String> joinMainId = getC160S03AMainIdArr(c160mainIdSet,
					search_custId);
			if (joinMainId.size() > 0) {
				pageSetting.addSearchModeParameters(SearchMode.IN, "mainId",
						joinMainId);
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"mainId", "none");
			}

		}
		Page<? extends GenericBean> page = service.findPage(C160M03A.class,
				pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	@SuppressWarnings("serial")
	public CapGridResult queryC160S03A(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString("mainId"));
		String search_custId = Util.trim(params.getString("search_custId"));
		if (StringUtils.isNotEmpty(mainId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
					mainId);
			if (Util.isNotEmpty(search_custId)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE,
						"custId_s", search_custId + "%");
			}
			Page<? extends GenericBean> page = service.findPage(C160S03A.class,
					pageSetting);

			Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
			formatter.put("chkYN", new IFormatter() {
				@SuppressWarnings("unchecked")
				@Override
				public String reformat(Object in) throws CapFormatException {
					return Util.equals("Y", in) ? "V" : "X";
				}
			});

			CapGridResult result = new CapGridResult(page.getContent(),
					page.getTotalRow());
			result.setDataReformatter(formatter);
			return result;
		}
		return new CapGridResult();
	}

	private List<String> getC160S03AMainIdArr(List<String> c160mainIdSet,
			String search_custId) {
		List<String> r = new ArrayList<String>();
		if (c160mainIdSet.size() > 0) {
			ISearch mySearch = clsService.getMetaSearch();
			mySearch.addSearchModeParameters(SearchMode.LIKE, "custId_s",
					search_custId + "%");
			mySearch.addSearchModeParameters(SearchMode.IN, "mainId",
					c160mainIdSet);
			Page<? extends GenericBean> custId_page = clsService.findPage(
					C160S03A.class, mySearch);
			for (GenericBean bean : custId_page.getContent()) {
				C160S03A obj = (C160S03A) bean;
				r.add(obj.getMainId());
			}
		}
		return r;
	}

	private class rawMarkModelFormatter implements IBeanFormatter {

		private static final long serialVersionUID = 1L;

		@SuppressWarnings("unchecked")
		@Override
		public String reformat(Object in) throws CapFormatException {
			C120M01A model = (C120M01A) in;
			return Util.trim(model.getMarkModel());
		}

	}

	private class grade2Formatter implements IBeanFormatter {

		private static final long serialVersionUID = 1L;

		@SuppressWarnings("unchecked")
		@Override
		public String reformat(Object in) throws CapFormatException {
			C120M01A m = (C120M01A) in;
			C120S01Q q = m.getC120s01q();
			return ClsUtil.c120s01q_adjGrade(q);
		}

	}

	private class grade3Formatter implements IBeanFormatter {

		private static final long serialVersionUID = 1L;

		@SuppressWarnings("unchecked")
		@Override
		public String reformat(Object in) throws CapFormatException {
			C120M01A m = (C120M01A) in;
			C120S01Q q = m.getC120s01q();
			return q != null ? Util.trim(q.getGrade3()) : "";
		}

	}

	private class varVerFormatter implements IBeanFormatter {

		private static final long serialVersionUID = 1L;

		@SuppressWarnings("unchecked")
		@Override
		public String reformat(Object in) throws CapFormatException {
			C120M01A m = (C120M01A) in;
			C120S01Q q = m.getC120s01q();
			return q != null ? Util.trim(q.getVarVer()) : "";
		}

	}

	public CapGridResult queryL250M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l250a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rptId", "CLS");
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		// 建立主要Search 條件

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);

		if (params.containsKey("custId")) {
			String custId = Util.nullToSpace(params.getString("custId"));

			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);

		}

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("apprId", new UserNameFormatter(userInfoService));

		// 第三個參數為formatting
		Page<? extends GenericBean> page = lms2501Service.findPage(
				L250M01A.class, pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow(),
				formatter);
	}

	public CapGridResult L140M01AQueryForSimu(ISearch pageSetting,
			PageParameters params) throws CapException {
		// mainId
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = lmsService
				.findModelByMainId(L120M01A.class, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.mainId", mainId);
		String itemType = lmsService.checkL140M01AItemType(l120m01a);

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.itemType", itemType);

		// 額度序號
		pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL, "cntrNo",
				null);
		pageSetting
				.addSearchModeParameters(SearchMode.NOT_EQUALS, "cntrNo", "");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				FlowDocStatusEnum.已核准.getCode());
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "proPerty",
				UtilConstants.Cntrdoc.Property.取消);
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "proPerty",
				UtilConstants.Cntrdoc.Property.不變);
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
				"currentApplyAmt", 0);

		// 刪除時間
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, null);
		Page<? extends GenericBean> page = lmsService.findPage(L140M01A.class,
				pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	// J-108-0217_10702_B1001 模擬動審新增IVR語音系統選取視窗
	public CapMapGridResult queryIVRFiltergird(ISearch pageSetting,
			PageParameters params) throws CapException {
		String oid = params.getString(EloanConstants.MAIN_OID);
		List<Map<String, Object>> list = service.getIVRFiltergrid(oid);

		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				list, list.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());
	}

	// J-108-0217_10702_B1001 模擬動審IVR語音系統查詢
	public CapMapGridResult queryIVRgrid(ISearch pageSetting,
			PageParameters params) throws CapException {
		String oid = params.getString(EloanConstants.MAIN_OID);
		List<Map<String, Object>> list = service.getIVRgrid(oid);

		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				list, list.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());
	}

	// J-109-0150_10702_B1001 Web e-Loan IVR頁籤由模擬動審移至動審表
	public CapMapGridResult queryIVRFiltergird2(ISearch pageSetting,
			PageParameters params) throws CapException {
		String oid = params.getString(EloanConstants.MAIN_OID);
		List<Map<String, Object>> list = service.getIVRFiltergrid2(oid);

		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				list, list.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());
	}

	public CapMapGridResult queryIVRgrid2(ISearch pageSetting,
			PageParameters params) throws CapException {
		String oid = params.getString(EloanConstants.MAIN_OID);
		List<Map<String, Object>> list = service.getIVRgrid2(oid);

		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				list, list.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());
	}

	/**
	 * 取得簽報與動審聯徵查詢結果比較表
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryJcicResultCompare(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = params.getString(EloanConstants.MAIN_ID);
		List<Map<String, Object>> list = service.getJcicResultCompareMap(mainId);
		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				list, list.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());
	}
}