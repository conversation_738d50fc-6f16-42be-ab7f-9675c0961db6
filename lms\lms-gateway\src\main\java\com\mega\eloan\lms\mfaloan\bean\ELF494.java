package com.mega.eloan.lms.mfaloan.bean;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 企金覆審報告檔 **/
public class ELF494 extends GenericBean {

	/** 分行代號 **/
	@Column(name = "ELF494_BRANCH", length = 3, columnDefinition = "CHAR(3)")
	private String elf494_branch;

	/** 借款人統一編號(PV-IDNO) **/
	@Column(name = "ELF494_CUSTID", length = 10, columnDefinition = "CHAR(10)")
	private String elf494_custId;

	/** 重複序號 **/
	@Column(name = "ELF494_DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String elf494_dupNo;

	/** DBUOBU **/
	@Column(name = "ELF494_DBUOBU", length = 3, columnDefinition = "CHAR(3)")
	private String elf494_dbuObu;

	/** 覆審報告表UNID **/
	@Column(name = "ELF494_RPTDOCID", length = 32, columnDefinition = "CHAR(32)", nullable = false, unique = true)
	private String elf494_rptDocId;

	/** 上次覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF494_LLRDATE", columnDefinition = "DATE")
	private Date elf494_llrDate;

	/** 本次覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF494_LRDATE", columnDefinition = "DATE")
	private Date elf494_lrDate;

	/** 覆審報告表案號 **/
	@Column(name = "ELF494_PROJNO", length = 100, columnDefinition = "CHAR(100)")
	private String elf494_projNo;

	/** 資料年 **/
	@Column(name = "ELF494_DATADTY", length = 3, columnDefinition = "CHAR(3)")
	private String elf494_dataDtY;

	/** 覆審批號 **/
	@Column(name = "ELF494_BATCHNO", length = 3, columnDefinition = "CHAR(3)")
	private String elf494_batchNo;

	/** 覆審序號 **/
	@Column(name = "ELF494_SNO", length = 3, columnDefinition = "CHAR(3)")
	private String elf494_sno;

	/** 主要授信戶 **/
	@Column(name = "ELF494_MAINCUST", length = 1, columnDefinition = "CHAR(1)")
	private String elf494_mainCust;

	/** 資信評等類別 **/
	@Column(name = "ELF494_CRDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf494_crdType;

	/** 資信評等 **/
	@Column(name = "ELF494_CRDTTBL", length = 2, columnDefinition = "CHAR(2)")
	private String elf494_crdtTbl;

	/** 信用模型評等類別 **/
	@Column(name = "ELF494_MOWTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf494_mowType;

	/** 信用模型評等 **/
	@Column(name = "ELF494_MOWTBL1", length = 2, columnDefinition = "CHAR(2)")
	private String elf494_mowTbl1;

	/** 不覆審代碼 **/
	@Column(name = "ELF494_NCKDFLAG", length = 2, columnDefinition = "CHAR(2)")
	private String elf494_nckdFlag;

	/** 有無必要辦理保全措施 **/
	@Column(name = "ELF494_RETIAL", length = 1, columnDefinition = "CHAR(1)")
	private String elf494_retial;

	/** 覆審結果 **/
	@Column(name = "ELF494_CONFLAG", length = 2, columnDefinition = "CHAR(2)")
	private String elf494_conFlag;

	/** 編製完成日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF494_UPDATE", columnDefinition = "DATE")
	private Date elf494_upDate;

	/** X覆審經副襄理 **/
	@Column(name = "ELF494_MANAGERID", length = 8, columnDefinition = "CHAR(8)")
	private String elf494_managerId;

	/** X覆審主管 **/
	@Column(name = "ELF494_BOSSID", length = 8, columnDefinition = "CHAR(8)")
	private String elf494_bossId;

	/** X覆審人員 **/
	@Column(name = "ELF494_APPRID", length = 8, columnDefinition = "CHAR(8)")
	private String elf494_apprId;

	/** 資料修改人 **/
	@Column(name = "ELF494_UPDATER", length = 8, columnDefinition = "CHAR(8)")
	private String elf494_updater;

	/** 資料更新日 **/
	@Column(name = "ELF494_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf494_tmestamp;

	/** 覆審項目內容 **/
	@Column(name = "ELF494_CHKITEM", length = 2400, columnDefinition = "VARCHAR(2400)")
	private String elf494_chkItem;

	/**
	 * 外部評等類別 <br/>
	 * 標準普爾 | 1 <br/>
	 * 穆迪信評 | 2 <br/>
	 * 惠譽信評 | 3 <br/>
	 * 中華信評 | 4
	 */
	@Column(name = "ELF494_FCRDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf494_fcrdType;

	/**
	 * 外部評等地區別 <br/>
	 * 國際 | 1 <br/>
	 * 本國 | 2
	 */
	@Column(name = "ELF494_FCRDAREA", length = 1, columnDefinition = "CHAR(1)")
	private String elf494_fcrdArea;

	/**
	 * 外部評等期間別 <br/>
	 * 長期 | 1 <br/>
	 * 短期 | 2
	 */
	@Column(name = "ELF494_FCRDPRED", length = 1, columnDefinition = "CHAR(1)")
	private String elf494_fcrdPred;

	/** 外部評等等級 **/
	@Column(name = "ELF494_FCRDGRAD", length = 30, columnDefinition = "CHAR(30)")
	private String elf494_fcrdGrad;

	/**
	 * 是否為實地覆審報告表 <br/>
	 * 長期 | 1 <br/>
	 * 短期 | 2
	 */
	@Column(name = "ELF494_REALRPFG", length = 1, columnDefinition = "CHAR(1)")
	private String elf494_realRpFg;

	/** 實地覆審基準日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF494_REALRPDT", columnDefinition = "DATE")
	private Date elf494_realRpDt;

	/**
	 * 覆審名單類別 J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 */
	@Column(name = "ELF494_CTLTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf494_ctlType;
	
	/**
	 * J-107-0245_09301_B1001 Web e-Loan企金授信系統覆審報告中增列上次覆審日當時之「信用評等及信用風險內部評等」資訊
	 * 上次覆審_資信評等類別
	 * 若無上次覆審為”XC”
	 */
	@Column(name = "ELF494_EXCRDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf494_excrdType;

	/**
	 * J-107-0245_09301_B1001 Web e-Loan企金授信系統覆審報告中增列上次覆審日當時之「信用評等及信用風險內部評等」資訊
	 * 上次覆審_資信評等
	 */
	@Column(name = "ELF494_EXCRDTTBL", length = 2, columnDefinition = "CHAR(2)")
	private String elf494_excrdtTbl;
	
	/**
	 * J-107-0245_09301_B1001 Web e-Loan企金授信系統覆審報告中增列上次覆審日當時之「信用評等及信用風險內部評等」資訊
	 * 上次覆審_信用模型評等類別
	 * 若無上次覆審為”XM”
	 */
	@Column(name = "ELF494_EXMOWTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf494_exmowType;

	/**
	 * J-107-0245_09301_B1001 Web e-Loan企金授信系統覆審報告中增列上次覆審日當時之「信用評等及信用風險內部評等」資訊
	 * 上次覆審_信用模型評等
	 */
	@Column(name = "ELF494_EXMOWTBL1", length = 2, columnDefinition = "CHAR(2)")
	private String elf494_exmowTbl1;
	
	/**
	 * J-107-0245_09301_B1001 Web e-Loan企金授信系統覆審報告中增列上次覆審日當時之「信用評等及信用風險內部評等」資訊
	 * 上次覆審_外部評等類別
	 */
	@Column(name = "ELF494_EXFCRDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf494_exfcrdType;

	/**
	 * J-107-0245_09301_B1001 Web e-Loan企金授信系統覆審報告中增列上次覆審日當時之「信用評等及信用風險內部評等」資訊
	 * 上次覆審_外部評等地區別
	 */
	@Column(name = "ELF494_EXFCRDAREA", length = 1, columnDefinition = "CHAR(1)")
	private String elf494_exfcrdArea;

	/**
	 * J-107-0245_09301_B1001 Web e-Loan企金授信系統覆審報告中增列上次覆審日當時之「信用評等及信用風險內部評等」資訊
	 * 上次覆審_外部評等期間別
	 */
	@Column(name = "ELF494_EXFCRDPRED", length = 1, columnDefinition = "CHAR(1)")
	private String elf494_exfcrdPred;

	/**
	 * J-107-0245_09301_B1001 Web e-Loan企金授信系統覆審報告中增列上次覆審日當時之「信用評等及信用風險內部評等」資訊
	 * 上次覆審_外部評等等級
	 */
	@Column(name = "ELF494_EXFCRDGRAD", length = 30, columnDefinition = "CHAR(30)")
	private String elf494_exfcrdGrad;

	public String getElf494_branch() {
		return elf494_branch;
	}

	public void setElf494_branch(String elf494_branch) {
		this.elf494_branch = elf494_branch;
	}

	public String getElf494_custId() {
		return elf494_custId;
	}

	public void setElf494_custId(String elf494_custId) {
		this.elf494_custId = elf494_custId;
	}

	public String getElf494_dupNo() {
		return elf494_dupNo;
	}

	public void setElf494_dupNo(String elf494_dupNo) {
		this.elf494_dupNo = elf494_dupNo;
	}

	public String getElf494_dbuObu() {
		return elf494_dbuObu;
	}

	public void setElf494_dbuObu(String elf494_dbuObu) {
		this.elf494_dbuObu = elf494_dbuObu;
	}

	public String getElf494_rptDocId() {
		return elf494_rptDocId;
	}

	public void setElf494_rptDocId(String elf494_rptDocId) {
		this.elf494_rptDocId = elf494_rptDocId;
	}

	public Date getElf494_llrDate() {
		return elf494_llrDate;
	}

	public void setElf494_llrDate(Date elf494_llrDate) {
		this.elf494_llrDate = elf494_llrDate;
	}

	public Date getElf494_lrDate() {
		return elf494_lrDate;
	}

	public void setElf494_lrDate(Date elf494_lrDate) {
		this.elf494_lrDate = elf494_lrDate;
	}

	public String getElf494_projNo() {
		return elf494_projNo;
	}

	public void setElf494_projNo(String elf494_projNo) {
		this.elf494_projNo = elf494_projNo;
	}

	public String getElf494_dataDtY() {
		return elf494_dataDtY;
	}

	public void setElf494_dataDtY(String elf494_dataDtY) {
		this.elf494_dataDtY = elf494_dataDtY;
	}

	public String getElf494_batchNo() {
		return elf494_batchNo;
	}

	public void setElf494_batchNo(String elf494_batchNo) {
		this.elf494_batchNo = elf494_batchNo;
	}

	public String getElf494_sno() {
		return elf494_sno;
	}

	public void setElf494_sno(String elf494_sno) {
		this.elf494_sno = elf494_sno;
	}

	public String getElf494_mainCust() {
		return elf494_mainCust;
	}

	public void setElf494_mainCust(String elf494_mainCust) {
		this.elf494_mainCust = elf494_mainCust;
	}

	public String getElf494_crdType() {
		return elf494_crdType;
	}

	public void setElf494_crdType(String elf494_crdType) {
		this.elf494_crdType = elf494_crdType;
	}

	public String getElf494_crdtTbl() {
		return elf494_crdtTbl;
	}

	public void setElf494_crdtTbl(String elf494_crdtTbl) {
		this.elf494_crdtTbl = elf494_crdtTbl;
	}

	public String getElf494_mowType() {
		return elf494_mowType;
	}

	public void setElf494_mowType(String elf494_mowType) {
		this.elf494_mowType = elf494_mowType;
	}

	public String getElf494_mowTbl1() {
		return elf494_mowTbl1;
	}

	public void setElf494_mowTbl1(String elf494_mowTbl1) {
		this.elf494_mowTbl1 = elf494_mowTbl1;
	}

	public String getElf494_nckdFlag() {
		return elf494_nckdFlag;
	}

	public void setElf494_nckdFlag(String elf494_nckdFlag) {
		this.elf494_nckdFlag = elf494_nckdFlag;
	}

	public String getElf494_retial() {
		return elf494_retial;
	}

	public void setElf494_retial(String elf494_retial) {
		this.elf494_retial = elf494_retial;
	}

	public String getElf494_conFlag() {
		return elf494_conFlag;
	}

	public void setElf494_conFlag(String elf494_conFlag) {
		this.elf494_conFlag = elf494_conFlag;
	}

	public Date getElf494_upDate() {
		return elf494_upDate;
	}

	public void setElf494_upDate(Date elf494_upDate) {
		this.elf494_upDate = elf494_upDate;
	}

	public String getElf494_managerId() {
		return elf494_managerId;
	}

	public void setElf494_managerId(String elf494_managerId) {
		this.elf494_managerId = elf494_managerId;
	}

	public String getElf494_bossId() {
		return elf494_bossId;
	}

	public void setElf494_bossId(String elf494_bossId) {
		this.elf494_bossId = elf494_bossId;
	}

	public String getElf494_apprId() {
		return elf494_apprId;
	}

	public void setElf494_apprId(String elf494_apprId) {
		this.elf494_apprId = elf494_apprId;
	}

	public String getElf494_updater() {
		return elf494_updater;
	}

	public void setElf494_updater(String elf494_updater) {
		this.elf494_updater = elf494_updater;
	}

	public Timestamp getElf494_tmestamp() {
		return elf494_tmestamp;
	}

	public void setElf494_tmestamp(Timestamp elf494_tmestamp) {
		this.elf494_tmestamp = elf494_tmestamp;
	}

	public String getElf494_chkItem() {
		return elf494_chkItem;
	}

	public void setElf494_chkItem(String elf494_chkItem) {
		this.elf494_chkItem = elf494_chkItem;
	}

	public String getElf494_fcrdType() {
		return elf494_fcrdType;
	}

	public void setElf494_fcrdType(String elf494_fcrdType) {
		this.elf494_fcrdType = elf494_fcrdType;
	}

	public String getElf494_fcrdArea() {
		return elf494_fcrdArea;
	}

	public void setElf494_fcrdArea(String elf494_fcrdArea) {
		this.elf494_fcrdArea = elf494_fcrdArea;
	}

	public String getElf494_fcrdPred() {
		return elf494_fcrdPred;
	}

	public void setElf494_fcrdPred(String elf494_fcrdPred) {
		this.elf494_fcrdPred = elf494_fcrdPred;
	}

	public String getElf494_fcrdGrad() {
		return elf494_fcrdGrad;
	}

	public void setElf494_fcrdGrad(String elf494_fcrdGrad) {
		this.elf494_fcrdGrad = elf494_fcrdGrad;
	}

	public void setElf494_realRpFg(String elf494_realRpFg) {
		this.elf494_realRpFg = elf494_realRpFg;
	}

	public String getElf494_realRpFg() {
		return elf494_realRpFg;
	}

	public void setElf494_realRpDt(Date elf494_realRpDt) {
		this.elf494_realRpDt = elf494_realRpDt;
	}

	public Date getElf494_realRpDt() {
		return elf494_realRpDt;
	}

	public void setElf494_ctlType(String elf494_ctlType) {
		this.elf494_ctlType = elf494_ctlType;
	}

	public String getElf494_ctlType() {
		return elf494_ctlType;
	}

	public String getElf494_excrdType() {
		return elf494_excrdType;
	}

	public void setElf494_excrdType(String elf494_excrdType) {
		this.elf494_excrdType = elf494_excrdType;
	}

	public String getElf494_excrdtTbl() {
		return elf494_excrdtTbl;
	}

	public void setElf494_excrdtTbl(String elf494_excrdtTbl) {
		this.elf494_excrdtTbl = elf494_excrdtTbl;
	}

	public String getElf494_exmowType() {
		return elf494_exmowType;
	}

	public void setElf494_exmowType(String elf494_exmowType) {
		this.elf494_exmowType = elf494_exmowType;
	}

	public String getElf494_exmowTbl1() {
		return elf494_exmowTbl1;
	}

	public void setElf494_exmowTbl1(String elf494_exmowTbl1) {
		this.elf494_exmowTbl1 = elf494_exmowTbl1;
	}
	
	public String getElf494_exfcrdType() {
		return elf494_exfcrdType;
	}

	public void setElf494_exfcrdType(String elf494_exfcrdType) {
		this.elf494_exfcrdType = elf494_exfcrdType;
	}

	public String getElf494_exfcrdArea() {
		return elf494_exfcrdArea;
	}

	public void setElf494_exfcrdArea(String elf494_exfcrdArea) {
		this.elf494_exfcrdArea = elf494_exfcrdArea;
	}

	public String getElf494_exfcrdPred() {
		return elf494_exfcrdPred;
	}

	public void setElf494_exfcrdPred(String elf494_exfcrdPred) {
		this.elf494_exfcrdPred = elf494_exfcrdPred;
	}

	public String getElf494_exfcrdGrad() {
		return elf494_exfcrdGrad;
	}

	public void setElf494_exfcrdGrad(String elf494_exfcrdGrad) {
		this.elf494_exfcrdGrad = elf494_exfcrdGrad;
	}
}
