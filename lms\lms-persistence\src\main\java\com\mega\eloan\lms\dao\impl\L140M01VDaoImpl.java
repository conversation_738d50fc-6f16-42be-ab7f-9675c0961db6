/*
 * L140M01VDaoImpl.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L140M01VDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140M01V;

/** 連鎖加盟貸款資訊檔 **/
@Repository
public class L140M01VDaoImpl extends LMSJpaDao<L140M01V, String>
		implements L140M01VDao {

	@Override
	public L140M01V findByUniqueKey(String cntrNo){
		ISearch search = createSearchTemplete();
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L140M01V> findByIndex01(String cntrNo){
		ISearch search = createSearchTemplete();
		List<L140M01V> list = null;
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", cntrNo);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140M01V> findByIndex02(String mainBizId, String mainBizDupNo){
		ISearch search = createSearchTemplete();
		List<L140M01V> list = null;
		if (mainBizId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainBizId", mainBizId);
		if (mainBizDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainBizDupNo", mainBizDupNo);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}