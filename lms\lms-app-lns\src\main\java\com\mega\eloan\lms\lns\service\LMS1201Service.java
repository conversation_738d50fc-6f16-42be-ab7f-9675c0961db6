/* 
 * LMS1201Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONObject;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.model.BRelated;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C140JSON;
import com.mega.eloan.lms.model.C140M01A;
import com.mega.eloan.lms.model.C140M04A;
import com.mega.eloan.lms.model.C140M04B;
import com.mega.eloan.lms.model.C140M07A;
import com.mega.eloan.lms.model.C140S04A;
import com.mega.eloan.lms.model.C140S04B;
import com.mega.eloan.lms.model.C140S04C;
import com.mega.eloan.lms.model.C140S07A;
import com.mega.eloan.lms.model.C140S09A;
import com.mega.eloan.lms.model.C140S09B;
import com.mega.eloan.lms.model.C140S09C;
import com.mega.eloan.lms.model.C140S09D;
import com.mega.eloan.lms.model.C140S09E;
import com.mega.eloan.lms.model.C140S09F;
import com.mega.eloan.lms.model.C140SDSC;
import com.mega.eloan.lms.model.C140SFFF;
import com.mega.eloan.lms.model.L120A01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01B;
import com.mega.eloan.lms.model.L120M01D;
import com.mega.eloan.lms.model.L120M01E;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L120M01H;
import com.mega.eloan.lms.model.L120M01I;
import com.mega.eloan.lms.model.L120M01J;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.eloan.lms.model.L120S01E;
import com.mega.eloan.lms.model.L120S01F;
import com.mega.eloan.lms.model.L120S01G;
import com.mega.eloan.lms.model.L120S01M;
import com.mega.eloan.lms.model.L120S01N;
import com.mega.eloan.lms.model.L120S01O;
import com.mega.eloan.lms.model.L120S01P;
import com.mega.eloan.lms.model.L120S01T;
import com.mega.eloan.lms.model.L120S03A;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S04B;
import com.mega.eloan.lms.model.L120S04C;
import com.mega.eloan.lms.model.L120S04E;
import com.mega.eloan.lms.model.L120S05A;
import com.mega.eloan.lms.model.L120S05B;
import com.mega.eloan.lms.model.L120S05C;
import com.mega.eloan.lms.model.L120S05D;
import com.mega.eloan.lms.model.L120S05E;
import com.mega.eloan.lms.model.L120S05F;
import com.mega.eloan.lms.model.L120S06A;
import com.mega.eloan.lms.model.L120S06B;
import com.mega.eloan.lms.model.L120S07A;
import com.mega.eloan.lms.model.L120S08A;
import com.mega.eloan.lms.model.L120S08B;
import com.mega.eloan.lms.model.L120S10A;
import com.mega.eloan.lms.model.L120S10B;
import com.mega.eloan.lms.model.L120S11A;
import com.mega.eloan.lms.model.L120S14A;
import com.mega.eloan.lms.model.L120S14E;
import com.mega.eloan.lms.model.L120S16A;
import com.mega.eloan.lms.model.L120S16B;
import com.mega.eloan.lms.model.L120S16C;
import com.mega.eloan.lms.model.L120S17A;
import com.mega.eloan.lms.model.L120S23A;
import com.mega.eloan.lms.model.L120S24A;
import com.mega.eloan.lms.model.L120S25A;
import com.mega.eloan.lms.model.L120S25B;
import com.mega.eloan.lms.model.L120S25C;
import com.mega.eloan.lms.model.L121M01B;
import com.mega.eloan.lms.model.L130M01A;
import com.mega.eloan.lms.model.L130M01B;
import com.mega.eloan.lms.model.L130S01A;
import com.mega.eloan.lms.model.L130S01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L720M01A;
import com.mega.eloan.lms.model.L730A01A;
import com.mega.eloan.lms.model.L730M01A;
import com.mega.eloan.lms.model.L730S01A;
import com.mega.eloan.lms.model.L800M01A;
import com.mega.eloan.lms.model.L902M01A;
import com.mega.eloan.lms.model.L902S01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;

/**
 * 授信簽報書 Service
 * 
 * <AUTHOR> Lin
 * 
 */
public interface LMS1201Service extends AbstractService {

	// // 初始化 JDBC
	// void init();

	// 案件簽報書授權檔
	// 使用者自定表格範本
	/**
	 * 透過Oid取得資料
	 * 
	 * @param oid
	 * @return
	 */
	L720M01A findL720m01aByOid(String oid);

	/**
	 * 刪除多筆資料
	 * 
	 * @param oidArray
	 */
	void deleteListL720m01a(String[] oidArray);

	/**
	 * 透過獨特Key取得資料
	 * 
	 * @param patternNm
	 * @return
	 */
	L720M01A findL720m01aByUniqueKey(String patternNm);

	/**
	 * 取得所有資料
	 * 
	 * @param search
	 * @return
	 */
	List<L720M01A> findL720m01aList(ISearch search);

	List<L720M01A> findL720m01aList();

	/**
	 * 利用OID 找到案件簽報書授權檔
	 * 
	 * @param oid
	 * @return
	 */
	L120A01A findL120a01aByOid(String oid);

	/**
	 * 利用mainId 找到所有案件簽報書授權檔
	 * 
	 * @param oid
	 * @return
	 */
	List<L120A01A> findL120a01aByMainId(String mainId);

	/**
	 * 利用獨特Key 找到案件簽報書授權檔
	 * 
	 * @param oid
	 * @return
	 */
	L120A01A findL120a01aByUniqueKey(String mainId, String ownUnit,
			String authType, String authUnit);

	// 授信簽報書主檔

	/**
	 * 利用OID 找到授信簽報書主檔
	 * 
	 * @param oid
	 * @return
	 */
	L120M01A findL120m01aByOid(String oid);

	/**
	 * 利用mainId 找到所有授信簽報書主檔
	 * 
	 * @param oid
	 * @return
	 */
	L120M01A findL120m01aByMainId(String mainId);

	/**
	 * 利用Oid群組刪除所有授信簽報書主檔
	 * 
	 * @param oid
	 * @return
	 */
	void deleteListL120m01a(String[] oidArray);

	/**
	 * 利用mainId刪除所有授信簽報書主檔及其旗下相關聯Table
	 * 
	 * @param oid
	 * @return
	 */
	void deleteAllRelate(String mainId);

	// 簽報書敘述說明檔
	/**
	 * 利用Oid 找到簽報書敘述說明檔
	 * 
	 * @param oid
	 * @return
	 */
	L120M01D findL120m01dByOid(String oid);

	/**
	 * 利用mainId 找到所有簽報書敘述說明檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120M01D> findL120m01dByMainId(String mainId);

	/**
	 * 利用獨特Key 找到簽報書敘述說明檔
	 * 
	 * @param mainId
	 * @param itemType
	 * @return
	 */
	L120M01D findL120m01dByUniqueKey(String mainId, String itemType);

	/**
	 * 取得徵信報告書 綜合評估及敘做理由
	 * 
	 * @param cesMainId
	 * @return
	 */
	String findFfbody(String cesMainId);

	/**
	 * 取得徵信報告書 其他內容
	 * 
	 * @param cesMainId
	 * @param custName
	 * @return
	 */
	Map<String, String> findOther(String cesMainId, String custName);

	/**
	 * 利用Oid群組刪除簽報書敘述說明檔
	 * 
	 * @param oidArray
	 */
	void deleteListL120m01d(String[] oidArray);

	/**
	 * 刪除簽報書敘述說明檔群組
	 * 
	 * @param oidArray
	 */
	void deleteListL120m01d(List<L120M01D> list);

	// 相關文件資料檔
	/**
	 * 利用Oid 取得相關文件資料
	 * 
	 * @param oid
	 * @return
	 */
	L120M01E findL120m01eByOid(String oid);

	/**
	 * 利用MainId 取得所有相關文件資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120M01E> findL120m01eByMainId(String mainId);

	/**
	 * 利用獨特Key 取得相關文件資料
	 * 
	 * @param mainId
	 * @param docType
	 * @param docURL
	 * @param docOid
	 * @return
	 */
	L120M01E findL120m01eByUniqueKey(String mainId, String docType,
			String docURL, String docOid);

	/**
	 * 利用MainId、DocType 取得所有相關文件資料
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120M01E> findL120M01EByByMainIdAndDocType(String mainId,
			String docType);

	/**
	 * 儲存相關文件資料群組
	 * 
	 * @param list
	 */
	void saveListL120m01e(List<L120M01E> list);

	/**
	 * 刪除相關文件資料群組
	 * 
	 * @param list
	 */
	void delListL120m01e(List<L120M01E> list);

	/**
	 * 利用Oid 取得案件簽章欄檔
	 * 
	 * @param oid
	 * @return
	 */
	L120M01F findL120m01fByOid(String oid);

	/**
	 * 利用MainId取得所有案件簽章欄檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120M01F> findL120m01fByMainId(String mainId);

	/**
	 * 依照特定條件找出案件改分派已存在的簽章欄檔
	 * 
	 * @param mainId
	 * @param branchType
	 * @param branchId
	 * @param staffJob
	 * @return
	 */
	List<L120M01F> findToSaveHq(String mainId, String branchType,
			String branchId, String staffJob);

	/**
	 * 利用獨特Key取得案件簽章欄檔
	 * 
	 * @param mainId
	 * @param branchType
	 * @param branchId
	 * @param staffNo
	 * @param staffJob
	 * @return
	 */
	L120M01F findL120m01fByUniqueKey(String mainId, String branchType,
			String branchId, String staffNo, String staffJob);

	/**
	 * 刪除案件簽章欄檔群組
	 * 
	 * @param list
	 */
	void delListL120m01f(List<L120M01F> list);

	/**
	 * 刪除並儲存案件改分派相關Table
	 * 
	 * @param list
	 * @param l120m01f
	 * @param model
	 */
	void delAndSaveL120m01f(List<L120M01F> list, L120M01F l120m01f,
			L120M01A model);

	/**
	 * 儲存案件簽章欄檔群組
	 * 
	 * @param list
	 */
	void saveListL120m01f(List<L120M01F> list);

	// 授審會／催收會會議決議檔
	/**
	 * 利用Oid 取得授審會／催收會會議決議檔
	 * 
	 * @param oid
	 * @return
	 */
	L120M01H findL120m01hByOid(String oid);

	/**
	 * 利用MainId取得所有授審會／催收會會議決議檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120M01H> findL120m01hByMainId(String mainId);

	/**
	 * 利用獨特Key取得授審會／催收會會議決議檔
	 * 
	 * @param mainId
	 * @param meetingType
	 * @return
	 */
	L120M01H findL120m01hByUniqueKey(String mainId, String meetingType);

	// 借款人主檔
	/**
	 * 利用Oid取得借款人主檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S01A findL120s01aByOid(String oid);

	/**
	 * 利用MainId 取得所有借款人主檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S01A> findL120s01aByMainId(String mainId);

	/**
	 * 利用MainId 取得所有借款人主檔+排序
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S01A> findL120s01aByMainIdForOrder(String mainId);

	/**
	 * 利用獨特Key取得借款人主檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	L120S01A findL120s01aByUniqueKey(String mainId, String custId, String dupNo);

	/**
	 * 利用Oid群組刪除所有借款人主檔
	 * 
	 * @param oidArray
	 */
	void deleteListL120s01a(String[] oidArray);

	// 企金基本資料檔
	/**
	 * 利用Oid取得企金基本資料檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S01B findL120s01bByOid(String oid);

	/**
	 * 利用MainId取得所有企金基本資料檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S01B> findL120s01bByMainId(String mainId);

	/**
	 * 利用獨特Key取得企金基本資料檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	L120S01B findL120s01bByUniqueKey(String mainId, String custId, String dupNo);

	/**
	 * 利用Oid群組刪除企金基本資料檔
	 * 
	 * @param oidArray
	 */
	void deleteListL120s01b(String[] oidArray);

	/**
	 * 儲存群組企金基本資料檔
	 * 
	 * @param list
	 */
	void saveListL120s01b(List<L120S01B> list);

	/**
	 * 刪除群組企金基本資料檔
	 * 
	 * @param list
	 */
	void deleteListL120s01b(List<L120S01B> list);

	// 企金信用評等資料檔
	/**
	 * 利用Oid 取得信用評等資料檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S01C findL120s01cByOid(String oid);

	/**
	 * 利用MainId 取得所有信用評等資料檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S01C> findL120s01cByMainId(String mainId);

	/**
	 * 利用借款人統編、重覆序號及文件編號取得所有信用評等資料檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<L120S01C> findL120s01cByCustId(String mainId, String custId,
			String dupNo);

	/**
	 * 利用借款人統編、重覆序號及文件編號取得所有信用評等資料檔 使用評等日期做排序，由新到舊
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<L120S01C> findL120s01cByCustIdOrderByCrdTYear(String mainId,
			String custId, String dupNo);

	/**
	 * 利用獨特Key 取得信用評等資料檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param crdTYear
	 * @param crdTBR
	 * @param crdType
	 * @param finYear
	 * @param cntrNo
	 * @return
	 */
	L120S01C findL120s01cByUniqueKey(String mainId, String custId,
			String dupNo, Date crdTYear, String crdTBR, String crdType,
			String finYear, String cntrNo);

	/**
	 * 儲存群組信用評等資料檔
	 * 
	 * @param list
	 */
	void saveListL120s01c(List<L120S01C> list);

	/**
	 * 利用Oid群組刪除信用評等資料檔
	 * 
	 * @param oidArray
	 */
	void deleteListL120s01c(String[] oidArray);

	/**
	 * 刪除群組信用評等資料檔
	 * 
	 * @param list
	 */
	void deleteListL120s01c(List<L120S01C> list);

	/**
	 * 透過JDBC從MIS取得信用評等資料
	 * 
	 * @param mainId
	 *            文件編號
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            客戶重覆序號
	 * @param delList
	 *            要刪除的已存在信評資料
	 * @param frag
	 *            要查詢的信用評等種類 1: 信用評等 2: 內部信評 3: 外部信評
	 * @return List<L120S01C>
	 */
	public List<L120S01C> findL120s01c(String mainId, String custId,
			String dupNo, String brno, List<L120S01C> delList, int frag)
			throws CapException;

	// 企金銀行法/金控法利害關係人檔
	/**
	 * 利用Oid 取得 企金銀行法/金控法利害關係人檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S01D findL120s01dByOid(String oid);

	/**
	 * 利用Mainid 取得所有企金銀行法/金控法利害關係人檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S01D> findL120s01dByMainId(String mainId);

	/**
	 * 利用獨特Key 取得企金銀行法/金控法利害關係人檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	L120S01D findL120s01dByUniqueKey(String mainId, String custId, String dupNo);

	/**
	 * 利用Oid群組刪除企金銀行法/金控法利害關係人檔
	 * 
	 * @param oidArray
	 */
	void deleteListL120s01d(String[] oidArray);

	/**
	 * 刪除群組企金銀行法/金控法利害關係人檔
	 * 
	 * @param list
	 */
	void deleteListL120s01d(List<L120S01D> list);

	// 企金營收獲利財務狀況檔
	/**
	 * 利用Oid 取得企金營收獲利財務狀況檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S01E findL120s01eByOid(String oid);

	/**
	 * 利用MainId 取得所有企金營收獲利財務狀況檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S01E> findL120s01eByMainId(String mainId);

	/**
	 * 利用文件編號及類別取得所有企金營收獲利財務狀況檔
	 * 
	 * @param mainId
	 * @param finKind
	 * @return
	 */
	List<L120S01E> findL120s01eByKindMainId(String mainId, String finKind);

	/**
	 * 利用獨特Key取得所有企金營收獲利財務狀況檔
	 * 
	 * @param finKind
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<L120S01E> findListL120s01eByUniqueKey(String finKind, String mainId,
			String custId, String dupNo);

	/**
	 * 利用獨特Key取得所有企金營收獲利財務狀況檔(以年度、項目升冪排序)
	 * 
	 * @param finKind
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<L120S01E> findListL120s01eByUniqueKey2(String finKind, String mainId,
			String custId, String dupNo);

	/**
	 * 利用獨特Key取得企金營收獲利財務狀況檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param finKind
	 * @param finYear
	 * @param finItem
	 * @return
	 */
	L120S01E findL120s01eByUniqueKey(String mainId, String custId,
			String dupNo, String finKind, Date finYear, String finItem);

	/**
	 * 利用Oid群組刪除企金營收獲利財務狀況檔
	 * 
	 * @param oidArray
	 */
	void deleteListL120s01e(String[] oidArray);

	/**
	 * 刪除群組企金營收獲利財務狀況檔
	 * 
	 * @param list
	 */
	void deleteListL120s01e(List<L120S01E> list);

	/**
	 * 儲存群組企金營收獲利財務狀況檔
	 * 
	 * @param list
	 */
	void saveListL120s01e(List<L120S01E> list);

	/**
	 * 儲存群組企金營收獲利財務狀況檔及企金借款人資料檔
	 * 
	 * @param list
	 * @param l120s01b
	 */
	void saveListL120s01e(List<L120S01E> list, L120S01B l120s01b);

	/**
	 * 從徵信取得營運概況及財務狀況
	 * 
	 * @param finKind
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param finItem
	 * @param map
	 * @param cesMainId
	 * @return
	 */
	List<L120S01E> findListL120s01e(String finKind, String mainId,
			String custId, String dupNo, String[] finItem,
			Map<String, List<String>> map, String cesMainId);

	/**
	 * 利用類別、借款人統編、重覆序號及文件編號取得所有企金營收獲利財務狀況檔
	 * 
	 * @param finKind
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<L120S01E> findListL120s01e2(String finKind, String mainId,
			String custId, String dupNo);

	/**
	 * 利用類別、借款人統編、重覆序號及文件編號取得所有年度
	 * 
	 * @param finKind
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<Date> findListFinYear(String finKind, String mainId, String custId,
			String dupNo);

	/**
	 * 查詢利害關係人授信條件對照表
	 * 
	 * @param allCustId
	 * @param search
	 * @return
	 */
	Page<Map<String, Object>> getLihai(String allCustId, String caseBrid,
			ISearch search);

	/**
	 * 透過JDBC從徵信報告取得MainId(資信簡表用)
	 * 
	 * @param caseBrId
	 *            案件號碼-分行
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            客戶重覆序號
	 * @param search
	 *            ISearch
	 * @return Page<Map<String, Object>>
	 */
	Page<Map<String, Object>> getCesMainId1(String caseBrId, String custId,
			String dupNo, ISearch search);

	// 企金存放款外匯往來檔
	/**
	 * 利用Oid取得企金存放款外匯往來檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S01F findL120s01fByOid(String oid);

	/**
	 * 利用MainId取得所有企金存放款外匯往來檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S01F> findL120s01fByMainId(String mainId);

	/**
	 * 利用獨特Key取得企金存放款外匯往來檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	L120S01F findL120s01fByUniqueKey(String mainId, String custId, String dupNo);

	/**
	 * 刪除群組企金存放款外匯往來檔
	 * 
	 * @param oidArray
	 */
	void deleteListL120s01f(String[] oidArray);

	/**
	 * 刪除群組企金存放款外匯往來檔
	 * 
	 * @param list
	 */
	void deleteListL120s01f(List<L120S01F> list);

	/**
	 * 從徵信 資信簡表取得 企金存放款外匯往來檔
	 * 
	 * @param model
	 * @param mainId
	 * @return
	 */
	L120S01F findl120s01f(L120S01F model, String mainId);

	// 企金分析與評估檔
	/**
	 * 利用Oid 取得企金分析與評估檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S01G findL120s01gByOid(String oid);

	/**
	 * 利用MainId 取得所有企金分析與評估檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S01G> findL120s01gByMainId(String mainId);

	/**
	 * 利用MainId 及類別取得所有企金分析與評估檔
	 * 
	 * @param mainId
	 * @param dataType
	 * @return
	 */
	List<L120S01G> findL120s01gByTypeMainId(String mainId, String dataType);

	/**
	 * 利用獨特Key 取得企金分析與評估檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param dataType
	 * @return
	 */
	L120S01G findL120s01gByUniqueKey(String mainId, String custId,
			String dupNo, String dataType);

	/**
	 * 刪除群組企金分析與評估檔
	 * 
	 * @param oidArray
	 */
	void deleteListL120s01g(String[] oidArray);

	/**
	 * 刪除群組企金分析與評估檔
	 * 
	 * @param list
	 */
	void deleteListL120s01g(List<L120S01G> list);

	/**
	 * 透過JDBC從徵信報告依照使用者輸入統編取得MainId(徵信報告用)
	 * 
	 * @param caseBrId
	 *            案件號碼-分行
	 * @param custId
	 *            使用者輸入統編
	 * @param search
	 *            ISearch
	 * @return Page<Map<String, Object>>
	 */
	Page<Map<String, Object>> getCesMainId(String caseBrId, String custId,
			ISearch search);

	/**
	 * 透過JDBC從徵信報告取得MainId(徵信報告用)
	 * 
	 * @param caseBrId
	 *            案件號碼-分行
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            客戶重覆序號
	 * @param search
	 *            ISearch
	 * @return Page<Map<String, Object>>
	 */
	Page<Map<String, Object>> getCesMainId2(String caseBrId, String custId,
			String dupNo, ISearch search);

	/**
	 * 透過JDBC從徵信報告取得MainId(範圍)(徵信報告用)
	 * 
	 * @param caseBrId
	 *            案件號碼-分行
	 * @param mainId1
	 *            授信簽報書文件編號
	 * @param mainId2
	 *            授信簽報書文件編號
	 * @param search
	 *            ISearch
	 * @return Page<Map<String, Object>>
	 */
	Page<Map<String, Object>> getCesMainId2s(String caseBrId, String mainId1,
			String mainId2, ISearch search);

	/**
	 * 透過JDBC從徵信報告取得借款人資料
	 * 
	 * @param caseBrId
	 *            案件號碼-分行
	 * @param mainId1
	 *            授信簽報書文件編號
	 * @param mainId2
	 *            授信簽報書文件編號
	 * @return Page<Map<String, Object>>
	 */
	Map<String, String> getCustData(String caseBrId, String mainId1,
			String mainId2);

	/**
	 * 透過JDBC從MIS DB取得所有集團代號與集團名稱
	 * 
	 * @return
	 */
	Map<String, String> getAllGrpid();

	/**
	 * 透過JDBC從徵信報告取得MainId(範圍)(徵信報告用，不限制文件狀態)
	 * 
	 * @param caseBrId
	 *            案件號碼-分行
	 * @param search
	 *            ISearch
	 * @return Page<Map<String, Object>>
	 */
	Page<Map<String, Object>> getCesMainId2ss(String caseBrId, ISearch search);

	/**
	 * 取得徵信報告書 使用者輸入之Id 下的 文件編號(資信簡表用)
	 * 
	 * @param caseBrId
	 * @param custId
	 * @param search
	 * @return
	 */
	Page<Map<String, Object>> getCesMainIda(String caseBrId, String mainId1,
			String mainId2, ISearch search);

	/**
	 * 取得徵信報告書 使用者輸入之Id 下的 文件編號(資信簡表用)
	 * 
	 * @param caseBrId
	 * @param custId
	 * @param search
	 * @return
	 */
	public Page<Map<String, Object>> getCesMainIdb(String caseBrId,
			String custId, ISearch search);

	/**
	 * 取得徵信 主要借款人Id群組 下的 文件編號(範圍)(資信簡表用，不限制文件狀態)
	 * 
	 * @param caseBrId
	 * @param search
	 * @return
	 */
	Page<Map<String, Object>> getCesMainIdc(String caseBrId, ISearch search);

	/**
	 * 從徵信報告取得主要營業項目
	 * 
	 * @param cesMainId
	 *            徵信文件編號
	 * @return 主要營業項目內容
	 */
	String findBusi(String cesMainId);

	/**
	 * 透過JDBC從徵信報告取得分析與評估
	 * 
	 * @param model
	 *            使用者選擇的徵信報告MainId
	 * @param mainId
	 *            文件編號
	 * @return 分析與評估Model
	 */
	L120S01G find120s01g(L120S01G model, String mainId);

	// 個金基本資料檔
	/**
	 * 利用Oid 取得個金基本資料檔
	 * 
	 * @param oid
	 * @return
	 */
	C120S01A findC120S01AByOid(String oid);

	/**
	 * 利用獨特Key 取得個金基本資料檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	C120S01A findC120S01AByUniqueKey(String mainId, String custId, String dupNo);

	/**
	 * 利用群組Oid刪除個金基本資料檔
	 * 
	 * @param oidArray
	 */
	void deleteListC120S01A(String[] oidArray);

	/**
	 * 刪除群組個金基本資料檔
	 * 
	 * @param list
	 */
	void deleteListC120S01A(List<C120S01A> list);

	/**
	 * 利用MainId取得所有個金基本資料檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<C120S01A> findC120S01AByMainId(String mainId);

	/**
	 * 利用客戶統編及重覆序號取得個金客戶資料
	 * 
	 * @param json
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	JSONObject findCustDataByCustId(JSONObject json, String custId, String dupNo);

	/**
	 * 利用客戶統編及重覆序號取得個金客戶配偶資料
	 * 
	 * @param json
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	JSONObject findCustData2ByCustId(JSONObject json, String custId,
			String dupNo);

	/**
	 * 利用客戶統編及重覆序號取得個金申貸戶通訊地址 及申貸戶服務單位電話及電子郵件地址
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	Map<String, Map<String, String>> findCustData2ByCustId(String custId,
			String dupNo);

	// 個金服務單位檔
	/**
	 * 利用Oid 取得個金服務單位檔
	 * 
	 * @param oid
	 * @return
	 */
	C120S01B findC120S01BByOid(String oid);

	/**
	 * 利用獨特Key 取得個金服務單位檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	C120S01B findC120S01BByUniqueKey(String mainId, String custId, String dupNo);

	/**
	 * 利用Oid群組刪除個金服務單位檔
	 * 
	 * @param oidArray
	 */
	void deleteListC120S01B(String[] oidArray);

	/**
	 * 刪除群組個金服務單位檔
	 * 
	 * @param list
	 */
	void deleteListC120S01B(List<C120S01B> list);

	/**
	 * 利用MainId 取得所有個金服務單位檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<C120S01B> findC120S01BByMainId(String mainId);

	// 個金償債能力檔
	/**
	 * 利用Oid 取得個金償債能力檔
	 * 
	 * @param oid
	 * @return
	 */
	C120S01C findC120S01CByOid(String oid);

	/**
	 * 利用MainId 取得所有個金償債能力檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<C120S01C> findC120S01CByMainId(String mainId);

	/**
	 * 利用獨特Key取得個金償債能力檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	C120S01C findC120S01CByUniqueKey(String mainId, String custId, String dupNo);

	/**
	 * 利用Oid群組刪除個金償債能力檔
	 * 
	 * @param oidArray
	 */
	void deleteListC120S01C(String[] oidArray);

	/**
	 * 刪除群組個金償債能力檔
	 * 
	 * @param list
	 */
	void deleteListC120S01C(List<C120S01C> list);

	// 個金配偶資料檔
	/**
	 * 利用Oid取得個金配偶資料檔
	 * 
	 * @param oid
	 * @return
	 */
	C120S01D findC120S01DByOid(String oid);

	/**
	 * 利用MainId取得所有個金配偶資料檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<C120S01D> findC120S01DByMainId(String mainId);

	/**
	 * 利用獨特Key取得個金配偶資料檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	C120S01D findC120S01DByUniqueKey(String mainId, String custId, String dupNo);

	/**
	 * 利用Oid群組刪除個金配偶資料檔
	 * 
	 * @param oidArray
	 */
	void deleteListC120S01D(String[] oidArray);

	/**
	 * 刪除群組個金配偶資料檔
	 * 
	 * @param list
	 */
	void deleteListC120S01D(List<C120S01D> list);

	// 個金相關查詢資料檔
	/**
	 * 利用Oid取得個金相關查詢資料檔
	 * 
	 * @param oid
	 * @return
	 */
	C120S01E findC120S01EByOid(String oid);

	/**
	 * 利用MainId取得所有個金相關查詢資料檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<C120S01E> findC120S01EByMainId(String mainId);

	/**
	 * 利用獨特Key取得個金相關查詢資料檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	C120S01E findC120S01EByUniqueKey(String mainId, String custId, String dupNo);

	/**
	 * 利用Oid群組刪除個金相關查詢資料檔
	 * 
	 * @param oidArray
	 */
	void deleteListC120S01E(String[] oidArray);

	/**
	 * 刪除群組個金相關查詢資料檔
	 * 
	 * @param list
	 */
	void deleteListC120S01E(List<C120S01E> list);

	// 資本適足率影響數資料檔
	/**
	 * 利用Oid取得資本適足率影響數資料檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S03A findL120s03aByOid(String oid);

	/**
	 * 利用MainId取得所有資本適足率影響數資料檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S03A> findL120s03aByMainId(String mainId);

	/**
	 * 利用獨特Key取得資本適足率影響數資料檔
	 * 
	 * @param mainId
	 * @param cntrMainId
	 * @param cntrNo
	 * @return
	 */
	public L120S03A findL120s03aByUniqueKey(String mainId, String cntrMainId,
			String cntrNo);

	/**
	 * 儲存群組資本適足率影響數資料檔
	 * 
	 * @param list
	 */
	void saveL120s03aList(List<L120S03A> list);

	/**
	 * 儲存資本適足率影響數資料檔
	 * 
	 * @param model
	 */
	void saveL120s03a(L120S03A model);

	/**
	 * 刪除群組資本適足率影響數資料檔
	 * 
	 * @param list
	 */
	void deleteListL120s03a(List<L120S03A> list);

	// 關係戶於本行各項業務往來檔
	/**
	 * 利用Oid取得關係戶於本行各項業務往來檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S04A findL120s04aByOid(String oid);

	/**
	 * 利用獨特Key取得關係戶於本行各項業務往來檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @return
	 */
	L120S04A findL120s04aByUniqueKey(String mainId, String custId,
			String dupNo, String custName);

	/**
	 * 利用MainId 取得所有關係戶於本行各項業務往來檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S04A> findL120s04aByMainId(String mainId);

	/**
	 * 利用MainId,prtFlag 取得所有關係戶於本行各項業務往來檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S04A> findL120s04aByMainIdPrtFlag(String mainId, String prtFlag);

	/**
	 * 儲存群組關係戶於本行各項業務往來檔
	 * 
	 * @param list
	 */
	void saveL120s04aList(List<L120S04A> list);

	/**
	 * 刪除群組關係戶於本行各項業務往來檔
	 * 
	 * @param list
	 */
	void deleteListL120s04a(List<L120S04A> list);

	// 關係戶與本行往來實績彙總表主檔
	/**
	 * 利用Oid取得關係戶與本行往來實績彙總表主檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S04B findL120s04bByOid(String oid);

	/**
	 * 利用MainId 取得所有關係戶與本行往來實績彙總表主檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S04B> findL120s04bByMainId(String mainId);

	/**
	 * 儲存群組關係戶與本行往來實績彙總表主檔
	 * 
	 * @param list
	 */
	void saveL120s04BList(List<L120S04B> list);

	/**
	 * 刪除群組關係戶與本行往來實績彙總表主檔
	 * 
	 * @param list
	 */
	void deleteListL120s04b(List<L120S04B> list);

	// 關係戶於本行往來實績彙總表明細檔
	/**
	 * 利用Oid取得關係戶於本行往來實績彙總表明細檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S04C findL120s04cByOid(String oid);

	/**
	 * J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
	 */
	L120S04E findL120s04eByOid(String oid);

	/**
	 * 利用MainId 取得所有關係戶於本行往來實績彙總表明細檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S04C> findL120s04cByMainId(String mainId);

	/**
	 * J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
	 */
	List<L120S04E> findL120s04eByMainId(String mainId);

	/**
	 * 儲存群組關係戶於本行往來實績彙總表明細檔
	 * 
	 * @param list
	 */
	void saveL120s04cList(List<L120S04C> list);

	/**
	 * J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
	 */
	void saveL120s04eList(List<L120S04E> list);

	/**
	 * 儲存群組關係戶於本行往來實績彙總表主檔與明細檔
	 * 
	 * @param list
	 * @param model
	 */
	void saveL120s04bc(List<L120S04C> list, L120S04B model);

	/**
	 * 刪除群組關係戶於本行往來實績彙總表明細檔
	 * 
	 * @param list
	 */
	void deleteListL120s04c(List<L120S04C> list);

	/**
	 * J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
	 */
	void deleteListL120s04e(List<L120S04E> list);

	/**
	 * 刪除群組關係戶於本行往來實績彙總所有相關檔
	 * 
	 * @param list
	 */
	public void deleteListL120s04(List<L120S04B> listS04b,
			List<L120S04C> listS04c, List<L120S04E> listS04e);

	/**
	 * 產生往來實績彙總檔
	 * 
	 * @param mainId
	 * @param queryDateS
	 * @param queryDateE
	 * @return
	 * @throws CapException
	 */
	public void importL120s04b(String mainId,
			String queryDateS, String queryDateE, PageParameters params)
			throws CapMessageException, CapException;

	/**
	 * 查詢往來彙總DW有效資料範圍日期
	 * 
	 * @param queryDateS
	 * @param queryDateE
	 * @param json
	 * @return
	 */
	int checkDate(String queryDateS, String queryDateE, JSONObject json);

	/**
	 * 計算貢獻度(集團)、放款額度(集團)、放款餘額(集團)、活期存款(集團)、 貢獻度(關係)、放款額度(關係)、放款餘額(關係)、活期存款(關係)
	 * 之後儲存
	 * 
	 * @param mainId
	 *            文件編號
	 * @param map
	 *            Map
	 * @param json
	 *            前端欄位值
	 */
	void setTotal(String mainId, Map<String, Long> map, JSONObject json);

	/**
	 * 引進往來彙總
	 * 
	 * @param mainId
	 *            文件編號
	 * @param custId
	 *            主要借款人統編
	 * @param dupNo
	 *            主要借款人重覆序號
	 * @param custName
	 *            主要借款人名稱
	 * @param queryDateS
	 *            查詢日期起日
	 * @param queryDateE
	 *            查詢日期迄日
	 * @return List<L120S04A>
	 */
	public List<L120S04A> findL120s04a(String mainId, String custId,
			String dupNo, String custName, String queryDateS,
			String queryDateE, String keyCustId, String keyDupNo);

	// 借款人集團相關資料檔
	/**
	 * 利用Oid取得 借款人集團相關資料檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S05A findL120s05aByOid(String oid);

	/**
	 * 利用MainId取得所有借款人集團相關資料檔
	 * 
	 * @param mainId
	 * @return
	 */
	L120S05A findL120s05aByMainId(String mainId);

	/**
	 * 透過JDBC取得借款人集團相關資料檔
	 * 
	 * @param json
	 * @return
	 */
	public L120S05A findl120s05a1(JSONObject json);

	// 借款人集團授信明細檔
	/**
	 * 利用Oid取得借款人集團授信明細檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S05B findL120s05bByOid(String oid);

	/**
	 * 利用獨特Key取得借款人集團授信明細檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupCode
	 * @return
	 */
	L120S05B findL120s05bByUniqueKey(String mainId, String custId,
			String dupCode);

	/**
	 * 儲存並查詢借款人集團授信明細檔
	 * 
	 * @param list
	 * @param l120s05a
	 * @param json
	 * @return
	 */
	L120S05A saveAndQueryListL120s05b(List<L120S05B> list, L120S05A l120s05a,
			JSONObject json);

	/**
	 * 利用MainId取得借款人集團授信明細檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S05B> findL120s05bByMainId(String mainId);

	/**
	 * 刪除群組借款人集團授信明細檔
	 * 
	 * @param model
	 */
	public void deleteListL120s05b(List<L120S05B> model);

	/**
	 * 透過JDBC取得借款人集團授信明細檔
	 * 
	 * @param json
	 * @return
	 */
	public List<L120S05B> findL120s05b1(JSONObject json);

	/**
	 * 透過徵信取得集團代號
	 * 
	 * @param json
	 * @param cesMainId
	 */
	public void findGrpId(JSONObject json, String cesMainId);

	// 借款人關係企業相關資料檔
	/**
	 * 利用Oid取得借款人關係企業相關資料檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S05C findL120s05cByOid(String oid);

	/**
	 * 利用MainId取得所有借款人關係企業相關資料檔
	 * 
	 * @param mainId
	 * @return
	 */
	L120S05C findL120s05cByMainId(String mainId);

	/**
	 * 利用JDBC取得借款人關係企業相關資料檔
	 * 
	 * @param json
	 * @return
	 */
	public L120S05C findl120s05c1(JSONObject json);

	// 借款人關係企業授信明細檔
	/**
	 * 利用Oid取得借款人關係企業授信明細檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S05D findL120s05dByOid(String oid);

	/**
	 * 利用獨特Key取得借款人關係企業授信明細檔
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupCode
	 * @return
	 */
	L120S05D findL120s05dByUniqueKey(String mainId, String custId,
			String dupCode);

	/**
	 * 儲存並查詢借款人關係企業授信明細檔
	 * 
	 * @param list
	 * @param l120s05c
	 * @param json
	 * @return
	 */
	L120S05C saveAndQueryListL120s05d(List<L120S05D> list, L120S05C l120s05c,
			JSONObject json);

	/**
	 * 利用MainId取得借款人關係企業授信明細檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S05D> findL120s05dByMainId(String mainId);

	/**
	 * 刪除群組借款人關係企業授信明細檔
	 * 
	 * @param model
	 */
	public void deleteListL120s05d(List<L120S05D> model);

	/**
	 * 透過JDBC取得借款人關係企業授信明細檔
	 * 
	 * @param json
	 * @return
	 */
	public List<L120S05D> findL120s05d1(JSONObject json);

	// 利害關係人授信條件對照表主檔
	/**
	 * 利用Oid取得利害關係人授信條件對照表主檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S06A findL120s06aByOid(String oid);

	/**
	 * 利用獨特Key取得利害關係人授信條件對照表主檔s
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	L120S06A findL120s06aByUniqueKey(String mainId, String custId,
			String dupNo, String cntrNo, String refMainId);

	/**
	 * 利用MainId取得所有利害關係人授信條件對照表主檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S06A> findL120s06aByMainId(String mainId);

	/**
	 * 利用MainId取得所有利害關係人授信條件對照表主檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S06A> findL120s06aByMainIdOrderPrintMode(String mainId);

	/**
	 * 刪除群組利害關係人授信條件對照表主檔
	 * 
	 * @param model
	 */
	public void deleteListL120s06a(List<L120S06A> model);

	/**
	 * 儲存利害關係人授信條件對照表主檔及利害關係人授信條件對照表明細檔
	 * 
	 * @param listL120s06a
	 * @param listL120s06b
	 */
	public void saveListL120s06a(List<L120S06A> listL120s06a,
			List<L120S06B> listL120s06b);

	/**
	 * 利用Oid群組刪除利害關係人授信條件對照表主檔
	 * 
	 * @param oidArray
	 */
	public void deleteListL120s06a(String[] oidArray);

	// 利害關係人授信條件對照表明細檔
	/**
	 * 利用Oid取得利害關係人授信條件對照表明細檔
	 * 
	 * @param oid
	 * @return
	 */
	L120S06B findL120s06bByOid(String oid);

	/**
	 * 利用獨特Key取得利害關係人授信條件對照表明細檔
	 * 
	 * @param mainId
	 * @param type
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param itemType
	 * @return
	 */
	L120S06B findL120s06bByUniqueKey(String mainId, String type, String custId,
			String dupNo, String cntrNo, String itemType, String refMainId);

	/**
	 * 利用MainId取得所有利害關係人授信條件對照表明細檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L120S06B> findL120s06bByMainId(String mainId);

	/**
	 * 刪除群組利害關係人授信條件對照表明細檔
	 * 
	 * @param model
	 */
	public void deleteListL120s06b(List<L120S06B> model);

	/**
	 * 利用Oid取得土建融案檢視清單主檔
	 * 
	 * @param oid
	 * @return
	 */
	public L120S07A findL120s07aByOid(String oid);

	/**
	 * 利用MainId取得土建融案檢視清單主檔
	 * 
	 * @param mainId
	 * @return
	 */
	public L120S07A findL120s07aByUniqueKey(String mainId);

	// L730A01A 授信報案考核表授權檔
	/**
	 * 利用Oid取得授信報案考核表授權檔
	 * 
	 * @param oid
	 * @return
	 */
	L730A01A findL730a01aByOid(String oid);

	/**
	 * 利用MainId取得授信報案考核表授權檔群組
	 * 
	 * @param mainId
	 * @return
	 */
	List<L730A01A> findL730a01aByMainId(String mainId);

	/**
	 * 利用獨特Key取得授信報案考核表授權檔
	 * 
	 * @param mainId
	 * @param ownUnit
	 * @param authType
	 * @param authUnit
	 * @return
	 */
	L730A01A findL730a01aByUniqueKey(String mainId, String ownUnit,
			String authType, String authUnit);

	// L730M01A 授信報案考核表主檔
	/**
	 * 利用Oid取得授信報案考核表主檔
	 * 
	 * @param oid
	 * @return
	 */
	L730M01A findL730m01aByOid(String oid);

	/**
	 * 利用MainId取得授信報案考核表主檔
	 * 
	 * @param mainId
	 * @return
	 */
	L730M01A findL730m01aByMainId(String mainId);

	/**
	 * 依照Oid群組刪除授信報案考核表主檔群組
	 * 
	 * @param oidArray
	 */
	void deleteListL730m01a(String[] oidArray);

	// L730S01A 授信報案考核表明細檔
	/**
	 * 利用Oid取得授信報案考核表明細檔
	 * 
	 * @param oid
	 * @return
	 */
	L730S01A findL730s01aByOid(String oid);

	/**
	 * 利用MainId取得所有授信報案考核表明細檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L730S01A> findL730s01aByMainId(String mainId);

	/**
	 * 利用獨特Key取得授信報案考核表明細檔
	 * 
	 * @param mainId
	 * @param chkYM
	 * @param sysType
	 * @param itemType
	 * @param itemNo
	 * @return
	 */
	L730S01A findL730s01aByUniqueKey(String mainId, Date chkYM, String sysType,
			String itemType, String itemNo);

	/**
	 * 利用Oid群組刪除授信報案考核表明細檔群組
	 * 
	 * @param oidArray
	 */
	void deleteListL730s01a(String[] oidArray);

	/**
	 * 刪除授信報案考核表明細檔群組
	 * 
	 * @param list
	 */
	void deleteListL730s01a(List<L730S01A> list);

	/**
	 * 儲存授信報案考核表明細檔群組
	 * 
	 * @param list
	 */
	void saveListL730s01a(List<L730S01A> list);

	/**
	 * 依照登入分行取得主管常用名單
	 * 
	 * @return
	 */
	List<L800M01A> findL800m01aByBrno();

	/**
	 * 取得徵信報告書 相關文件之徵信報告書
	 * 
	 * @param cesMainId
	 *            徵信文件編號
	 * @return String
	 */
	public Map<String, String> findCesCustName(String cesMainId);

	/**
	 * 取得徵信報告書 相關文件之資信簡表
	 * 
	 * @param cesMainId
	 *            徵信文件編號
	 * @return
	 */
	public Map<String, String> findCesCustName2(String cesMainId);

	/**
	 * <pre>
	 * 處理八.1該集團之淨值,營收及八.3:集團評等及八.4 之相關欄位
	 * @param json
	 * </pre>
	 */
	// J-107-0087-001 Web
	// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
	public void gfnDb2GetRptGroupData(JSONObject json) throws CapException;

	/**
	 * <pre>
	 * 透過JDBC取得 處理八.1該集團之淨值,營收及八.3:集團評等及八.4 之相關欄位
	 * @param json JSONObject
	 * @param netAMT BigDecimal
	 * @param income BigDecimal
	 * @param reStr Sring
	 * </pre>
	 */
	// J-107-0087-001 Web
	// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
	public boolean getRptGroupData1(JSONObject json, BigDecimal netAMT,
			BigDecimal income, String reStr) throws CapException;

	/**
	 * <pre>
	 * 透過JDBC取得 最新結帳匯率
	 * @param sCurr String
	 * @return
	 * </pre>
	 */
	public double getRate(String sCurr);

	/**
	 * <pre>
	 * 開始flow流程
	 * @param mainOid String
	 * </pre>
	 */
	void startFlow(String mainOid);

	/**
	 * <pre>
	 * flow流程內容
	 * @param mainOid String
	 * @param model GenericBean
	 * @param setResult boolean
	 * @param resultType boolean
	 * @throws Throwable
	 * </pre>
	 */
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, boolean resultType) throws Throwable;

	/**
	 * 取得grid頁面所需資料
	 * 
	 * @param mainId
	 *            mainId
	 * @param printCondition
	 *            printCondition
	 * @param search
	 *            search
	 * @return Page<Map<String, Object>>
	 * @throws CapException
	 *             CapException
	 */
	Page<Map<String, Object>> getBorrows(String mainId, String printCondition,
			ISearch search) throws CapException;

	/**
	 * 根據oid陣列尋找L120M01A List
	 * 
	 * @param oids
	 * @return List<L120M01A>
	 */
	List<L120M01A> findL120m01asByOids(String[] oids);

	/**
	 * 儲存 List<L120M01A>
	 * 
	 * @param l120m01as
	 */
	public void saveL120m01as(List<L120M01A> l120m01as);

	/**
	 * 儲存l120m01a和l120m01d List
	 * 
	 * @param list1
	 * @param list2
	 */
	public void saveL120m01aAndL120m01d(List<L120M01A> list1,
			List<L120M01D> list2);

	/**
	 * 取得輸入資料檢誤完成判斷
	 * 
	 * @param mainId
	 *            文件編號
	 * @param json
	 *            檢核不過的Table
	 * @return 是否通過儲存檢核
	 */
	public boolean checkSend(String mainId, JSONObject json);

	/* Query */

	/**
	 * 取得徵信報告Grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140M01A>
	 */
	Page<C140M01A> getC1400Page(ISearch search);

	/**
	 * 取得徵信報告第四章 登錄主要負責人連保人資信狀況資料 Grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140M04A>
	 */
	Page<C140M04A> getC140M04APage(ISearch search);

	/**
	 * 取得徵信報告第四章 登錄保證公司/母公司營運及財務概況 Grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140M04B>
	 */
	Page<C140M04B> getC140M04BPage(ISearch search);

	/**
	 * 取得徵信報告第柒章Grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140M04A>
	 */
	Page<C140M07A> getC140M07APage(ISearch search);

	/**
	 * 取得徵信報告第柒章 財務分析(子) Grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S07A>
	 */
	Page<C140S07A> getC140S07APage(ISearch search);

	/**
	 * 取得第四章登錄主要負責人連保人資信狀況資料主檔
	 * 
	 * @param oid
	 *            String
	 * @return C140M04A
	 */
	C140M04A getC140M04A(String oid);

	/**
	 * 取得第四章登錄保證公司/母公司營運及財務概況主檔
	 * 
	 * @param oid
	 *            String
	 * @return C140M04B
	 */
	C140M04B getC140M04B(String oid);

	/**
	 * 取得第柒章主檔(財務分析)
	 * 
	 * @param mainId
	 *            String
	 * @param pid
	 *            m01a.uid
	 * @param tab
	 *            tab
	 * @param subtab
	 *            subTab
	 * @return C140M07A
	 */
	C140M07A getC140M07A(String mainId, String pid, String tab, String subtab);

	List<C140M07A> getC140M07A(String mainId, String pid, String tab);

	/**
	 * 取得第柒章 財務分析(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S07A
	 */
	C140S07A getC140S07A(String oid);

	/**
	 * 取得徵信報告主檔
	 * 
	 * @param oid
	 *            String
	 * @return C140M01A
	 */
	C140M01A getC140M01A(String oid);

	/**
	 * 使用MainId查詢C140M01A
	 * 
	 * @param MainId
	 *            String
	 * @return C140M01A
	 */
	C140M01A getC140M01AByMainId(String MainId);

	/**
	 * 使用MainId查詢C140M04A 肆、負責人保證人(主)
	 * 
	 * @param mainId
	 *            String
	 * @return List<C140M04A>
	 */
	List<C140M04A> getC140M04AByMainId(String mainId);

	/**
	 * 使用MainId查詢C140M04B 肆、保證公司(主)
	 * 
	 * @param mainId
	 *            String
	 * @return List<C140M04B>
	 */
	List<C140M04B> getC140M04BByMainId(String mainId);

	/**
	 * 使用MainId查詢C140M04B 肆、保證公司(主)，並自訂排序
	 * 
	 * @param mainId
	 *            String
	 * @param orderBy
	 *            String[]
	 * @return List<C140M04B>
	 */
	List<C140M04B> getC140M04BByMainIdOrderByProbussId(String mainId,
			String[] orderBy);

	/**
	 * 取得JSON內容
	 * 
	 * @param uid
	 *            String
	 * @param mainId
	 *            String
	 * @param tab
	 *            String
	 * @param subTab
	 *            String
	 * @return C140JSON
	 */
	C140JSON getC140JSONByMainPidTab(String uid, String mainId, String tab,
			String subTab);

	/**
	 * 取得財報資料
	 * 
	 * @param m01a
	 *            C140M01A
	 * @param tab
	 *            頁籤
	 * @param subtab
	 *            子頁籤
	 * @return List<C120S01C>
	 */
	List<C140S07A> getS07aByMetaAndTab(C140M01A m01a, String tab, String subtab);

	List<C140S07A> getS07aBySubDocAndTab(String mainId, String uid, String tab,
			String subtab);

	List<C140S07A> getS07aByM07aAndTab(C140M07A m07a);

	/**
	 * 取得徵信報告自由格式
	 * 
	 * @param mainId
	 *            String
	 * @param pid
	 *            String
	 * @param fieldId
	 *            String
	 * @param tab
	 *            tab
	 * @return C140SFFF
	 */
	C140SFFF getC140SFFF(String mainId, String pid, String fieldId, String tab);

	/**
	 * 取得徵信報告自由格式
	 * 
	 * @param mainId
	 *            String
	 * @param pid
	 *            String
	 * @param tab
	 *            tab
	 * @return List<C140SFFF>
	 */
	List<C140SFFF> getC140SFFF(String mainId, String pid, String tab);

	/**
	 * 取得徵信報告附加檔案
	 * 
	 * @param oid
	 *            String
	 * @return DocFile
	 */
	DocFile getDocFile(String oid);

	void deleteDocFile(String dfOid);

	/**
	 * 取得JSON內容
	 * 
	 * @param uid
	 *            String
	 * @param mainId
	 *            String
	 * @param tab
	 *            String
	 * @return List<C140JSON>
	 */
	List<C140JSON> getC140JSONByMainPidTab(String uid, String mainId, String tab);

	/**
	 * 取得註記說明內容
	 * 
	 * @param uid
	 *            String
	 * @param mainId
	 *            String
	 * @param tab
	 *            String
	 * @param fieldId
	 *            String
	 * @return C140SDSC
	 */
	C140SDSC getC140SDSCByMainPidTab(String uid, String mainId, String tab,
			String fieldId);

	/**
	 * 取得註記說明內容
	 * 
	 * @param uid
	 *            String
	 * @param mainId
	 *            String
	 * @param tab
	 *            String
	 * @return List<C140SDSC>
	 */
	List<C140SDSC> getC140SDSCByMainPidTab(String uid, String mainId, String tab);

	BRelated getBRelatedBymainId1AnddocType2(String mainId1, String docType2);

	List<BRelated> getBRelatedByAll(String mainId1, String docType1,
			String docType2, String tab, String subtab, String relatedflag,
			boolean includeFlag);

	/**
	 * 查詢連保人資料
	 * 
	 * @param mainId
	 *            String
	 * @return Map<String, Object>
	 */
	Map<String, Object> findC140M04ById(String mainId);

	/**
	 * 取得檔案連結
	 * 
	 * @param mainId
	 *            String
	 * @param fieldId
	 *            欄位名稱
	 * @return DocFile
	 */
	DocFile getDocFileByField(String mainId, String fieldId);

	/**
	 * 取得檔案，使用mainId & fieldId
	 * 
	 * @param mainId
	 *            String
	 * @param fieldId
	 *            String
	 * @return DocFile
	 */
	List<DocFile> getDocFileByMainIdAndFieldId(String mainId, String fieldId);

	/**
	 * 取得關聯信評
	 * 
	 * @param meta
	 *            C140M01A
	 * @return ArrayList<String>
	 */
	ArrayList<String> getRelatedMow(C140M01A meta);

	/**
	 * 取得關聯資簡
	 * 
	 * @param meta
	 *            C140M01A
	 * @return String
	 */
	String getRelatedCES120(C140M01A meta);

	/**
	 * 取得關聯應索取洽談
	 * 
	 * @param mainid
	 *            String
	 * @return String
	 */
	String getRelatedCES112(String mainid);

	/* Save ----------------------------------------- */
	/**
	 * 儲存徵信報告主檔及附加文件
	 * 
	 * @param c140m01a
	 *            C140M01A
	 * @param docFiles
	 *            List<DocFile>
	 */
	void saveC140m01aAndDocFiles(C140M01A c140m01a, List<DocFile> docFiles);

	/**
	 * 多筆徵信報告附加檔案儲存刪除註記
	 * 
	 * @param oids
	 *            String[]
	 * @param delFlag
	 *            String
	 */
	void saveDocFile(String[] oids, String delFlag);

	/**
	 * 儲存徵信報告主檔
	 * 
	 * @param c140m01a
	 *            C140M01A
	 */
	void saveC140M01A(C140M01A c140m01a);

	/**
	 * 儲存徵信報告之財報部分
	 * 
	 * @param c140m07a
	 *            C140M07A
	 */
	void saveC140M07A(C140M07A c140m07a);

	void saveC140M07A(List<C140M07A> c140m07as);

	/**
	 * 儲存徵信報告主檔
	 * 
	 * @param c140m01a
	 *            C140M01A
	 */
	void saveDocument(C140M01A c140m01a, List<DocFile> docFiles);

	/**
	 * 呈送主管
	 * 
	 * @param c140m01a
	 *            C140M01A
	 */
	void saveAndSendDocument(C140M01A c140m01a);

	/**
	 * 流程控管
	 * 
	 * @param mainOid
	 *            C140M01A.oid
	 * @param action
	 *            主管動作(核准|退回經辦修改)
	 */
	void flowControl(String mainOid, String action);

	/**
	 * 徵信報告附加檔案儲存
	 * 
	 * @param docFile
	 *            DocFile
	 */
	void saveDocFile(DocFile docFile);

	/**
	 * 徵信報告引用文件儲存
	 * 
	 * @param brelateds
	 *            BRelated
	 */
	void saveBRelated(List<BRelated> brelateds);

	void saveBRelated(List<BRelated> brelateds, BRelated toDelRel);

	/**
	 * 多筆徵信報告附加檔案儲存
	 * 
	 * @param docFiles
	 *            DocFile
	 */
	void saveDocFile(List<DocFile> docFiles);

	/* Delete ---------------------------------------------- */
	/**
	 * 刪除此文件
	 * 
	 * @param mainOid
	 *            文件編號
	 * @param mainId
	 *            文件編號
	 */
	void deleteDocument(String mainOid, String mainId);

	/**
	 * 刪除 C140M04A
	 * 
	 * @param mainOid
	 *            String
	 */
	void deleteC140M04A(String mainOid);

	/**
	 * 刪除 C140M04B
	 * 
	 * @param mainOid
	 *            String
	 */
	void deleteC140M04B(String mainOid);

	/**
	 * 刪除 C140M07A
	 * 
	 * @param c140m07as
	 *            List<C140M07A>
	 */
	void deleteC140M07A(List<C140M07A> c140m07as);

	/**
	 * 刪除 BRELATED
	 * 
	 * @param mainId1
	 *            String
	 * @param docType2
	 *            String
	 */
	void deleteBRelated(String mainId1, String docType2);

	/**
	 * 取得徵信報告第四章 經營事業(子) Grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S04A>
	 */
	Page<C140S04A> getC140S04APage(ISearch search);

	/**
	 * 取得徵信報告第四章 土地(子) Grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S04A>
	 */
	Page<C140S04B> getC140S04BPage(ISearch search);

	/**
	 * 取得徵信報告第四章 建物(子) Grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S04A>
	 */
	Page<C140S04C> getC140S04CPage(ISearch search);

	/**
	 * 取得第四章 經營事業(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S04A
	 */
	C140S04A getC140S04A(String oid);

	/**
	 * 取得第四章 經營事業(子)(多筆)
	 * 
	 * @param mainId
	 *            String
	 * @param uid
	 *            String
	 * @return List<C140S04A>
	 */
	List<C140S04A> getC140S04A(String mainId, String uid);

	/**
	 * 取得第四章 土地(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S04B
	 */
	C140S04B getC140S04B(String oid);

	/**
	 * 取得第四章 土地(子)(多筆)
	 * 
	 * @param mainId
	 *            String
	 * @param uid
	 *            String
	 * @return List<C140S04B>
	 */
	List<C140S04B> getC140S04B(String mainId, String uid);

	/**
	 * 取得第四章 建物(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S04C
	 */
	C140S04C getC140S04C(String oid);

	/**
	 * 取得第四章 建物(子)(多筆)
	 * 
	 * @param mainId
	 *            String
	 * @param uid
	 *            String4
	 * @return List<C140S04C>
	 */
	List<C140S04C> getC140S04C(String mainId, String uid);

	/**
	 * 刪除 C140S04A
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S04A(String subOid);

	/**
	 * 刪除 C140S04B
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S04B(String subOid);

	/**
	 * 刪除 C140S04C
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S04C(String subOid);

	/**
	 * 取得徵信報告第九章 集團往來(子)(多筆)
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S09A>
	 */
	Page<C140S09A> getC140S09APage(ISearch search);

	/**
	 * 取得徵信報告第九章 集團退票(子)
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S09B>
	 */
	Page<C140S09B> getC140S09BPage(ISearch search);

	/**
	 * 取得徵信報告第九章 集團拒絕(子)
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S09C>
	 */
	Page<C140S09C> getC140S09CPage(ISearch search);

	/**
	 * 取得徵信報告第九章 集團逾催呆(子)
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S09D>
	 */
	Page<C140S09D> getC140S09DPage(ISearch search);

	/**
	 * 取得徵信報告第九章 集團財務(子)
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S09E>
	 */
	Page<C140S09E> getC140S09EPage(ISearch search);

	/**
	 * 取得徵信報告第九章 集團大陸(子)
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<C140S09F>
	 */
	Page<C140S09F> getC140S09FPage(ISearch search);

	/**
	 * 取得第九章 董監事(子)名單(多筆)
	 * 
	 * @param oid
	 *            String
	 * @return C140S09A
	 */
	C140S09A getC140S09A(String oid);

	/**
	 * 取得第九章 董監事(子)名單(多筆)
	 * 
	 * @param mainId
	 *            String
	 * @param uid
	 *            String
	 * @return List<C140S09A>
	 */
	List<C140S09A> getC140S09A(String mainId, String uid);

	/**
	 * 取得第九章 集團退票(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S09B
	 */
	C140S09B getC140S09B(String oid);

	/**
	 * 取得第九章 集團拒絕(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S09C
	 */
	C140S09C getC140S09C(String oid);

	/**
	 * 取得第九章 集團逾催呆(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S09D
	 */
	C140S09D getC140S09D(String oid);

	/**
	 * 取得第九章 集團財務(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S09E
	 */
	C140S09E getC140S09E(String oid);

	/**
	 * 取得第九章 集團大陸(子)
	 * 
	 * @param oid
	 *            String
	 * @return C140S09F
	 */
	C140S09F getC140S09F(String oid);

	/**
	 * 刪除 C140S09A
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S09A(String subOid);

	/**
	 * 刪除 C140S09B
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S09B(String subOid);

	/**
	 * 刪除 C140S09C
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S09C(String subOid);

	/**
	 * 刪除 C140S09D
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S09D(String subOid);

	/**
	 * 刪除 C140S09E
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S09E(String subOid);

	/**
	 * 刪除 C140S09F
	 * 
	 * @param subOid
	 *            String
	 */
	void deleteC140S09F(String subOid);

	/**
	 * 刪除 C140S09B
	 * 
	 * @param mainId
	 *            String
	 * @param pid
	 *            String
	 */
	void deleteC140S09B(String mainId, String pid);

	/**
	 * 刪除 C140S09C
	 * 
	 * @param mainId
	 *            String
	 * @param pid
	 *            String
	 */
	void deleteC140S09C(String mainId, String pid);

	/**
	 * 刪除 C140S09E
	 * 
	 * @param mainId
	 *            String
	 * @param pid
	 *            String
	 */
	void deleteC140S09E(String mainId, String pid);

	/**
	 * 刪除案件簽報書(物理刪除)
	 * 
	 * @param mainId
	 *            文件編號
	 */
	void delL120m01All(String mainId);

	/**
	 * 複製案件簽報書
	 * 
	 * @param mainId
	 *            複製的mainId來源
	 * @return newMainId 新文件編號
	 * @throws CapMessageException
	 * @throws NumberFormatException
	 */
	String copyL120m01All(String mainId) throws NumberFormatException,
			CapMessageException;

	// /**
	// * 上傳授信簽報書
	// * @param l120m01a
	// */
	// void upLoadMIS(L120M01A l120m01a);

	/**
	 * 取得徵信財務預估表一覽(徵信報告用)
	 * 
	 * @param caseBrId
	 * @param custId
	 * @param dupNo
	 * @param search
	 * @return
	 */
	public Page<Map<String, Object>> getFss(String caseBrId, String custId,
			String dupNo, ISearch search);

	/**
	 * 新增授信簽報書
	 * 
	 * @param l120a01a
	 * @param l120m01a
	 * @param l120m01d01
	 * @param l120m01d02
	 * @param l120m01d03
	 * @param l120m01d04
	 * @param l120m01d05
	 * @param l120m01d06
	 * @param l120m01d07
	 * @param l120m01d08
	 * @param l120m01d09
	 * @param l120m01d0a
	 * @param l120m01d0b
	 * @param l120m01b
	 * @param l120m01f
	 * @param mainId
	 */
	void addLms(L120A01A l120a01a, L120M01A l120m01a, L120M01D l120m01d01,
			L120M01D l120m01d02, L120M01D l120m01d03, L120M01D l120m01d04,
			L120M01D l120m01d05, L120M01D l120m01d06, L120M01D l120m01d07,
			L120M01D l120m01d08, L120M01D l120m01d09, L120M01D l120m01d0a,
			L120M01D l120m01d0b, L120M01B l120m01b, L120M01F l120m01f,
			String mainId, L120S05A l120s05a, L120S05C l120s05c,
			L120M01I l120m01i);

	/**
	 * 刪除授信簽報書
	 * 
	 * @param mainId
	 */
	void delLms(String mainId);

	/**
	 * 刪除借款人企金
	 * 
	 * @param l120m01a
	 * @param l120s01a
	 * @param l120s01b
	 * @param l120s01d
	 * @param l120s01f
	 * @param l120s01g_1
	 * @param l120s01g_2
	 * @param listDel
	 */
	void delBorrow(L120M01A l120m01a, L120S01A l120s01a, L120S01B l120s01b,
			L120S01D l120s01d, L120S01F l120s01f, L120S01G l120s01g_1,
			L120S01G l120s01g_2, List<L120S01C> listDel,
			List<L120S01M> listL120s01m, List<L120S01N> listL120s01n,
			List<L120S01O> listL120s01o, List<L120S01P> listL120s01p);

	/**
	 * 刪除集團與關係企業所有相關Table
	 * 
	 * @param l120s05a
	 *            集團企業主檔
	 * @param listL120s05b
	 *            集團企業明細檔
	 * @param l120s05c
	 *            關係企業主檔
	 * @param listL120s05d
	 *            關係企業明細檔
	 */
	void deleteGrpRel(L120S05A l120s05a, List<L120S05B> listL120s05b,
			L120S05C l120s05c, List<L120S05D> listL120s05d);

	/**
	 * 刪除群組企金借款人主檔旗下所有Table
	 * 
	 * @param listL120s01b
	 * @param listL120s01c
	 * @param listL120s01d
	 * @param listL120s01e
	 * @param listL120s01f
	 * @param listL120s01g
	 * @param listL120s01a
	 */
	void deleteList1(List<L120S01B> listL120s01b, List<L120S01C> listL120s01c,
			List<L120S01D> listL120s01d, List<L120S01E> listL120s01e,
			List<L120S01F> listL120s01f, List<L120S01G> listL120s01g,
			List<L120S01A> listL120s01a, List<L120S01M> listL120s01m,
			List<L120S01N> listL120s01n, List<L120S01O> listL120s01o,
			List<L120S01P> listL120s01p);

	/**
	 * 刪除群組個金借款人主檔旗下所有Table
	 * 
	 * @param listL120s01h
	 * @param listL120s01i
	 * @param listL120s01j
	 * @param listL120s01k
	 * @param listL120s01l
	 * @param listL120s01a
	 */
	void deleteList2(List<C120S01A> listC120S01A, List<C120S01B> listC120S01B,
			List<C120S01C> listC120S01C, List<C120S01D> listC120S01D,
			List<C120S01E> listC120S01E, List<L120S01A> listL120s01a);

	/**
	 * 刪除信用評等且將主檔檢核設成N(條件變更續約專用)
	 * 
	 * @param listL120s01c
	 *            listL120s01c
	 * @param listL120s01a
	 *            listL120s01a
	 */
	void deleteList3(List<L120S01C> listL120s01c, List<L120S01A> listL120s01a,
			List<L120S01M> listL120s01m, List<L120S01N> listL120s01n,
			List<L120S01O> listL120s01o);

	/**
	 * 刪除信用評等且將主檔檢核設成N(條件變更續約專用) 清除集團、利害關係、婉卻、黑名單等相關資料 J-106-0029-003
	 * 洗錢防制-新增實質受益人
	 * 
	 * @param listL120s01c
	 *            listL120s01c
	 * @param listL120s01a
	 *            listL120s01a
	 * @param listL120s01b
	 *            listL120s01b
	 * @param listL120s01d
	 *            listL120s01d
	 */
	void deleteList3(List<L120S01C> listL120s01c, List<L120S01A> listL120s01a,
			List<L120S01B> listL120s01b, List<L120S01D> listL120s01d,
			List<L120S01M> listL120s01m, List<L120S01N> listL120s01n,
			List<L120S01O> listL120s01o, List<L120S01P> listL120s01p,
			List<L120S11A> listL120s11a);

	/**
	 * 引進徵信資信簡表取得營運概況與財務狀況及存放款外匯往來情形
	 * 
	 * @param list1
	 * @param list2
	 * @param l120m01a
	 * @param model1
	 * @param model2
	 * @param model3
	 * @param l120s01b
	 */
	void saveL120s01e(List<L120S01E> list1, List<L120S01E> list2,
			L120M01A l120m01a, L120S01G model1, L120S01G model2,
			L120S01F model3, L120S01B l120s01b);

	/**
	 * 儲存額度明細表
	 * 
	 * @param l120m01a
	 * @param l120m01b
	 * @param l120m01f
	 */
	void saveL1405(L120M01A l120m01a, L120M01B l120m01b, L120M01F l120m01f);

	/**
	 * 刪除利害關係人
	 * 
	 * @param listL120s06a
	 * @param listL120s06b
	 */
	void deleteListL120s06ab(List<L120S06A> listL120s06a,
			List<L120S06B> listL120s06b);

	/**
	 * 中長期結構化表格徵信報告複製
	 * 
	 * @param oid
	 * @param mainId
	 * @param cesMainId1
	 * @return
	 */
	C140M01A getC140m01a(String oid, String mainId, String cesMainId1);

	/**
	 * 中長期結構化表格徵信報告更新
	 * 
	 * @param oid
	 * @param mainId
	 * @param cesMainId1
	 * @return
	 */
	C140M01A updateC140m01a(String oid, String mainId, String cesMainId1);

	/**
	 * 產銷方式徵信報告複製
	 * 
	 * @param oid
	 * @param mainId
	 * @param cesMainId1
	 * @return
	 */
	C140M01A getC140m01a2(String oid, String mainId, String cesMainId1);

	/**
	 * 產銷方式徵信報告更新
	 * 
	 * @param oid
	 * @param mainId
	 * @param cesMainId1
	 * @return
	 */
	C140M01A updateC140m01a2(String oid, String mainId, String cesMainId1);

	/**
	 * 產業概況徵信報告複製
	 * 
	 * @param oid
	 * @param mainId
	 * @param cesMainId1
	 * @return
	 */
	C140M01A getC140m01a3(String oid, String mainId, String cesMainId1);

	/**
	 * 產業概況徵信報告更新
	 * 
	 * @param oid
	 * @param mainId
	 * @param cesMainId1
	 * @return
	 */
	C140M01A updateC140m01a3(String oid, String mainId, String cesMainId1);

	/**
	 * 刪除中長期結構化表格徵信報告
	 * 
	 * @param mainId
	 */
	void deleteC140m01a(String mainId);

	/**
	 * 儲存授信報案考核表
	 * 
	 * @param l120m01a
	 * @param l730a01a
	 * @param l730m01a
	 * @param listl730s01a
	 */
	void saveL730M01A(L120M01A l120m01a, L730A01A l730a01a, L730M01A l730m01a,
			List<L730S01A> listl730s01a);

	/**
	 * 儲存簽章欄及分行首次、最後送件時間
	 * 
	 * @param l120m01a
	 * @param models
	 */
	void saveL120m01fAndModel(L120M01A l120m01a, List<L120M01F> models);

	/**
	 * 取得隸屬集團(借款人企金用)
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            借款人重覆序號
	 * @return
	 */
	Map<String, String> getGrpdtl(String custId, String dupNo);

	/**
	 * 儲存取消列印利害關係人
	 * 
	 * @param model
	 *            案件簽報書
	 * @param list
	 *            利害關係群組
	 */
	void saveCancelPrint(L120M01A model, List<L120S04A> list);

	/**
	 * 儲存相關文件群組
	 * 
	 * @param l120m01a
	 *            案件簽報書
	 * @param list
	 *            相關文件群組
	 */
	void saveRel(L120M01A l120m01a, List<L120M01E> list);

	/**
	 * 儲存相關文件群組
	 * 
	 * @param l120m01a
	 *            案件簽報書
	 * @param list
	 *            相關文件群組
	 * @param listToSave
	 *            個金簽章欄檔
	 */
	void saveRel(L120M01A l120m01a, List<L120M01E> list,
			List<L120M01F> listToSave);

	/**
	 * 引進集團名單(相關文件)
	 * 
	 * @param c140s09a
	 */
	void saveC140S09A(C140S09A c140s09a);

	/**
	 * 引進集團名單(相關文件)
	 * 
	 * @param s09aList
	 */
	@SuppressWarnings("rawtypes")
	void saveC140S09A(List s09aList);

	void saveC140S09D(C140S09D c140s09d);

	void saveC140S09E(List<C140S09E> c140s09es);

	void saveC140S09F(C140S09F c140s09f);

	/**
	 * 刪除集團名單(相關文件)
	 * 
	 * @param c140s09as
	 */
	void deleteC140S09A(List<C140S09A> c140s09as);

	/**
	 * 調整案件簽報書到海外聯貸案主檔
	 * 
	 * @param mainId
	 *            案件簽報書文件編號
	 */
	void copyToL120M01A(String mainId);

	// 簽報書敘述說明檔
	/**
	 * 利用Oid 找到簽報書敘述說明檔
	 * 
	 * @param oid
	 * @return
	 */
	L121M01B findL121m01bByOid(String oid);

	/**
	 * 利用mainId 找到所有簽報書敘述說明檔
	 * 
	 * @param mainId
	 * @return
	 */
	List<L121M01B> findL121m01bByMainId(String mainId);

	/**
	 * 利用獨特Key 找到簽報書敘述說明檔
	 * 
	 * @param mainId
	 * @param itemType
	 * @return
	 */
	L121M01B findL121m01bByUniqueKey(String mainId, String itemType);

	/**
	 * 利用Oid群組刪除簽報書敘述說明檔
	 * 
	 * @param oidArray
	 */
	void deleteListL121m01b(String[] oidArray);

	/**
	 * 刪除簽報書敘述說明檔群組
	 * 
	 * @param oidArray
	 */
	void deleteListL121m01b(List<L121M01B> list);

	/**
	 * 取得Mow內部信評
	 * 
	 * @param custId
	 * @param dupNo
	 * @param search
	 * @return
	 */
	Page<Map<String, Object>> getMowTrust(String custId, String dupNo,
			ISearch search);

	/**
	 * 依照信用評等種類產生相對應信評檔
	 * 
	 * @param list
	 *            儲存所有信評類型檔
	 * @param mainId
	 *            文件編號
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            客戶重覆序號
	 * @param crdType
	 *            信評類型(未評等/免辦)
	 * @return list 更新過的信評群組
	 */
	List<L120S01C> getNewTrust(List<L120S01C> list, String mainId,
			String custId, String dupNo, String crdType);

	/**
	 * 儲存相關文件所有Model
	 * 
	 * @param list
	 * @param entity
	 */
	void saveRelAll(L120M01A l120m01a, L120M01F l120m01f);

	/**
	 * 集團企業拒往記錄
	 * 
	 * @param c140s09bs
	 */
	@SuppressWarnings("rawtypes")
	void saveC140S09B(List c140s09bs);

	/**
	 * 集團企業拒往記錄
	 * 
	 * @param c140s09bs
	 */
	@SuppressWarnings("rawtypes")
	void saveC140S09C(List c140s09cs);

	/**
	 * 刪除額度明細表與資本適足率(案件變更格式用)
	 * 
	 * @param list1
	 *            額度明細表群組
	 * @param list2
	 *            資本適足率
	 */
	void deleteL140M01aBis(List<L140M01A> list1, List<L120S03A> list2);

	/**
	 * 儲存營運中心簽章欄及意見
	 * 
	 * @param l120m01a
	 * @param l120m01d1
	 * @param l120m01d2
	 * @param list
	 */
	void saveArea(L120M01A l120m01a, List<L120M01F> list);

	void saveArea(L120M01A l120m01a, L120M01D l120m01d1, L120M01D l120m01d2,
			List<L120M01F> list);

	/**
	 * 儲存國金部簽章欄及意見
	 * 
	 * @param l120m01a
	 * @param list
	 */
	void saveSea(L121M01B l121m01b, List<L120M01F> list);

	/**
	 * 儲存國金部簽章欄及意見--呈主管放行(海外聯貸)用
	 * 
	 * @param l120m01a
	 *            主檔
	 * @param l120m01b
	 *            意見檔
	 * @param addL120m01f
	 *            新增存在放行人員(新增用)
	 */
	void saveSeaAndDel(L120M01A l120m01a, L121M01B l120m01b,
			L120M01F addL120m01f);

	/**
	 * 取得主檔ISearch
	 * 
	 * @return
	 */
	public ISearch getMetaSearch();

	/**
	 * 取得餘額日期(國內)
	 * 
	 * @param l140m01a
	 * @param var001B
	 * @return
	 */
	public String getLoanDate(L140M01A l140m01a, String var001B);

	/**
	 * 取得餘額日期(海外)
	 * 
	 * @param l140m01a
	 * @param var001B
	 * @return
	 */
	public String getLoanDate2(L140M01A l140m01a, String var001B);

	/**
	 * 
	 * 案件簽報書flow
	 * 
	 * @param mainOid
	 *            文件編號
	 * @param model
	 *            資料表
	 * @param setResult
	 *            boolean
	 * @param next
	 *            執行的下個動作
	 * 
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, String next, PageParameters params)
			throws Throwable;

	/**
	 * 儲存內部信評資料
	 * 
	 * @param mainId
	 * @param _crdType
	 * @param _crdTBR
	 * @param crdTYear
	 * @param grade
	 * @param finYear
	 * @param prospect
	 * @param prCustId
	 * @param prDupNo
	 * @param prCNAME
	 * @param prFR
	 * @param prFinDate
	 * @param prMOWBr
	 * @param custId
	 * @param dupNo
	 */
	public void saveMow(String mainId, String _crdType, String _crdTBR,
			String crdTYear, String grade, String finYear, String prospect,
			String prCustId, String prDupNo, String prCNAME, String prFR,
			String prFinDate, String prMOWBr, String custId, String dupNo);

	/**
	 * 取得股票資訊(企金借款人專用)
	 * 
	 * @param custId
	 *            借款人統編
	 * @param hasStockNum
	 *            是否有輸入股票代號
	 * @return
	 */
	public Map<String, Object> getStock(String custId, boolean hasStockNum);

	/**
	 * 黑名單查詢
	 * 
	 * @param ownBrId
	 *            分行代碼
	 * @param eName
	 *            英文名稱
	 * @return
	 */
	public Map<String, Object> getBlack(String ownBrId, String eName);

	/**
	 * 婉卻紀錄查詢
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重覆序號
	 * @return 婉卻紀錄資料
	 */
	public List<Map<String, Object>> findReject(String custId, String dupNo);

	/**
	 * 取得擔保品 相關文件之擔保品
	 * 
	 * @param cmsMainId
	 * @return
	 */
	public Map<String, String> findCmsCustName(String cmsMainId);

	/**
	 * 取得擔保品文件編號 (by 分行)
	 * 
	 * @param branchId
	 * @param search
	 * @return
	 */
	public Page<Map<String, Object>> getCmsMainId(String branchId,
			ISearch search);

	/**
	 * 取得擔保品 簽報書主要借款人Id群組 下的 文件編號
	 * 
	 * @param branchId
	 * @param search
	 * @return
	 */
	public Page<Map<String, Object>> getCmsMainId2(String mainId, ISearch search);

	/**
	 * 取得擔保品 大類 下的 文件編號
	 * 
	 * @param branchId
	 * @param search
	 * @return
	 */
	public Page<Map<String, Object>> getCmsMainId3(String collTyp1,
			ISearch search);

	/**
	 * 利用oid取得異常通報表主檔
	 * 
	 * @param oid
	 * @return
	 */
	public L130M01A findL130m01aByOid(String oid);

	/**
	 * 利MainId取得異常通報表主檔
	 * 
	 * @param mainId
	 * @return
	 */
	public L130M01A findL130m01aByMainId(String mainId);

	/**
	 * 利用oid取得異常通報表事項檔
	 * 
	 * @param oid
	 * @return
	 */
	public L130M01B findL130m01bByOid(String oid);

	/**
	 * 利用獨特Key取得異常通報事項檔
	 * 
	 * @param mainId
	 * @param branchKind
	 * @return
	 */
	public L130M01B findL130m01bByUniqueKey(String mainId, String branchKind);

	/**
	 * 利用mainId取得異常通報表事項檔群組
	 * 
	 * @param mainId
	 * @return
	 */
	public List<L130M01B> findL130m01bByMainId(String mainId);

	/**
	 * 依照oid陣列刪除相對應的異常通報表事項檔
	 * 
	 * @param oidArray
	 */
	public void deleteListL130m01b(String[] oidArray);

	/**
	 * 依照異常通報表事項檔List刪除相對應的異常通報表事項檔
	 * 
	 * @param list
	 */
	public void deleteListL130m01b(List<L130M01B> list);

	/**
	 * 儲存異常通報表事項檔與明細檔群組
	 * 
	 * @param l130m01b
	 *            異常通報表事項檔
	 * @param list
	 *            異常通報表明細檔群組
	 */
	public void saveL130m01bListL130s01a(L130M01B l130m01b, List<L130S01A> list);

	/**
	 * 依照oid取得異常通報表明細檔
	 * 
	 * @param oid
	 * @return
	 */
	public L130S01A findL130s01aByOid(String oid);

	/**
	 * 依照mainId取得異常通報表明細檔群組
	 * 
	 * @param mainId
	 * @return
	 */
	public List<L130S01A> findL130s01aByMainId(String mainId);

	/**
	 * 依照mainId與單位種類取得異常通報表明細檔群組
	 * 
	 * @param mainId
	 * @param branchKind
	 * @return
	 */
	public List<L130S01A> findByMainIdAndBranchKind(String mainId,
			String branchKind);

	/**
	 * 依照oid陣列刪除相對應的異常通報表明細檔群組
	 * 
	 * @param oidArray
	 */
	public void deleteListL130s01a(String[] oidArray);

	/**
	 * 將異常通報表明細檔List儲存
	 * 
	 * @param list
	 */
	public void saveListL130s01a(List<L130S01A> list);

	/**
	 * 依照異常通報表明細檔List刪除相對應的異常通報表明細檔群組
	 * 
	 * @param list
	 */
	public void deleteListL130s01a(List<L130S01A> list);

	/**
	 * 依照oid取得異常通報表參貸行檔
	 * 
	 * @param oid
	 * @return
	 */
	public L130S01B findL130s01bByOid(String oid);

	/**
	 * 依照mainId取得異常通報表參貸行檔群組
	 * 
	 * @param mainId
	 * @return
	 */
	public List<L130S01B> findL130s01bByMainId(String mainId);

	/**
	 * 依照oid陣列刪除相對應的異常通報表參貸行檔群組
	 * 
	 * @param oidArray
	 */
	public void deleteListL130s01b(String[] oidArray);

	/**
	 * 依照異常通報表參貸行List刪除相對應的異常通報表參貸行檔群組
	 * 
	 * @param list
	 */
	public void deleteListL130s01b(List<L130S01B> list);

	/**
	 * 儲存使用者指定異常通報表參貸行List
	 * 
	 * @param list
	 */
	public void saveListL130s01b(List<L130S01B> list);

	/**
	 * 儲存異常通報表主檔與異常通報表參貸行List
	 * 
	 * @param model
	 *            異常通報表主檔
	 * @param list
	 *            異常通報表參貸行List
	 */
	public void saveL130m01aL130s01bs(L130M01A model, List<L130S01B> list);

	/**
	 * 更新ELF412 - 匯入LNFE0851
	 * 
	 * @param branch
	 * @param custid
	 * @param dupno
	 * @param mdFlag
	 * @param mddt
	 * @param process
	 * @return
	 */
	public Map<String, String> getLnfe0851(String branch, String custid,
			String dupno);

	/**
	 * 引進異常通報帳務資料
	 * 
	 * @param brno
	 *            分行代碼
	 * @param custId
	 *            統編
	 * @param dupNo
	 *            重覆序號
	 * @param allCustId
	 *            統編+重覆序號
	 * @return 帳務資料Map
	 */
	public Map<String, Object> getUnNormalData(String brno, String custId,
			String dupNo, String allCustId);

	/**
	 * 取得異常類別
	 * 
	 * @return
	 */
	public Map<String, String> getUnNormalClass();

	/**
	 * 查詢異常通報事項(解除異常通報、停權)
	 * 
	 * @return
	 */
	public Map<String, String> selUnNormalBC(String type);

	/**
	 * 依照事項代碼查詢異常通報事項(解除異常通報、停權)
	 * 
	 * @param seqNo
	 * @return
	 */
	public Map<String, String> selUnNormalBCa(String type, String seqNo);

	/**
	 * 查詢異常通報事項-分行
	 * 
	 * @return
	 */
	public Map<String, String> getUnNormal1();

	/**
	 * 開始查詢異常通報事項-分行
	 * 
	 * @param seqNo
	 * @return
	 */
	public Map<String, String> getUnNormal1a(String seqNo);

	/**
	 * 查詢異常通報事項-營運中心
	 * 
	 * @return
	 */
	public Map<String, String> getUnNormal2();

	/**
	 * 開始查詢異常通報事項-營運中心
	 * 
	 * @param seqNo
	 * @return
	 */
	public Map<String, String> getUnNormal2a(String seqNo);

	/**
	 * 查詢異常通報事項-授管處
	 * 
	 * @return
	 */
	public Map<String, String> getUnNormal3();

	/**
	 * 開始查詢異常通報事項-授管處
	 * 
	 * @param seqNo
	 * @return
	 */
	public Map<String, String> getUnNormal3a(String seqNo);

	void saveC140JSON(List<C140JSON> c140jsons);

	void saveC140S04A(C140S04A c140s04a);

	void deleteC140S04A(List<C140S04A> s04as);

	void saveC140S04A(List<C140S04A> c140s04as);

	/**
	 * 查詢簽章欄
	 * 
	 * @param mainId
	 *            文件編號
	 * @param branchType
	 *            行別
	 * @param branchId
	 *            分行代號
	 * @param staffJob
	 *            職稱
	 * @return
	 */
	public L120M01F findL120m01fByMainIdAndKey(String mainId,
			String branchType, String branchId, String staffJob);

	/**
	 * 刪除異常通報旗下所有Table
	 * 
	 * @param l130m01a
	 * @param list1
	 * @param list2
	 * @param list3
	 */
	void deleteList4(L130M01A l130m01a, List<L130M01B> list1,
			List<L130S01A> list2, List<L130S01B> list3);

	/**
	 * 取得營運概況，財務概況引入時的預設科目
	 * 
	 * @param isGaap
	 *            0:gaap , 1: IFRS
	 * @param tradeType
	 *            行業別
	 * @return [0] 營運概況所而的四個科目和比率，[1]財務概況的比率
	 */
	String[] getFssItemCode(char isGaap, char tradeType);

	/**
	 * 取得營運概況內容
	 * 
	 * @param mainId
	 *            mainid
	 * @param custId
	 *            custid
	 * @param dupNo
	 *            dupNo
	 * @param gaapFlag
	 *            0 gaap, 1 ifrs
	 * @param fssType
	 * @return
	 */
	JSONObject getL120s01eKind1Data(String mainId, String custId, String dupNo,
			char gaapFlag, char fssType);

	/**
	 * 取得財務概況內容
	 * 
	 * @param mainId
	 *            mainid
	 * @param custId
	 *            custid
	 * @param dupNo
	 *            dupNo
	 * @param gaapFlag
	 *            0 gaap, 1 ifrs
	 * @param fssType
	 * @return
	 */
	JSONObject getL120s01eKind2Data(String mainId, String custId, String dupNo,
			char gaapFlag, char fssType);

	/**
	 * 刪除相關文件by oid 陣列
	 * 
	 * @param oids
	 */
	public void deleteL120M01EByoid(String[] oids);

	/**
	 * 集團年度淨值與營收改抓徵信報告第九章集團財務資訊
	 * 
	 * @param json
	 */
	public String getRptGroupData3(JSONObject json);

	// /**
	// * 取得授管處列印該簽報書資信簡表清單
	// * @param ces140MainIds
	// * @param ces120MainIds
	// * @param search
	// * @return
	// */
	// Page<Map<String, Object>> getCesPrint(List<String> ces140MainIds,
	// List<String> ces120MainIds,
	// ISearch search);

	/**
	 * 信用風險管理遵循
	 */
	public L120S01M findL120s01mByOid(String oid);

	public List<L120S01M> findL120s01mByMainId(String mainId);

	public List<L120S01M> findL120s01mByCustId(String mainId, String custId,
			String dupNo);

	public void deleteListL120s01m(String[] oidArray);

	public void deleteListL120s01m(List<L120S01M> list);

	public void saveListL120s01m(List<L120S01M> list);

	public L120S01N findL120s01nByOid(String oid);

	public List<L120S01N> findL120s01nByMainId(String mainId);

	public List<L120S01N> findL120s01nByCustId(String mainId, String custId,
			String dupNo);

	public L120S01M findL120s01mByIndex01(String mainId, String custId,
			String dupNo);

	public void deleteListL120s01n(String[] oidArray);

	public void deleteListL120s01n(List<L120S01N> list);

	public void saveListL120s01n(List<L120S01N> list);

	void deleteListL120s01mno(List<L120S01M> listS01m, List<L120S01N> listS01n,
			List<L120S01O> listS01o);

	public L120S01O findL120s01oByOid(String oid);

	public List<L120S01O> findL120s01oByMainId(String mainId);

	public List<L120S01O> findL120s01oByCustId(String mainId, String custId,
			String dupNo);

	public void deleteListL120s01o(String[] oidArray);

	public void deleteListL120s01o(List<L120S01O> list);

	public void saveListL120s01o(List<L120S01O> list);

	List<L120S01O> findL120s01oByCustIdRelType(String mainId, String custId,
			String dupNo, String relType);

	public L120S01T findL120s01tByOid(String oid);

    public List<L120S01T> findL120s01tByCustId(String mainId, String custId, String dupNo);

    public List<L120S01T> findL120s01tByCustIdFlag(String mainId, String custId, String dupNo, String flag);

	public List<L120S01T> findL120s01tByCustIdFlagPeNo(String mainId, String custId,
		   	String dupNo, String flag, String peNo);

	public void deleteListL120s01t(List<L120S01T> list);

	public void saveListL120s01t(List<L120S01T> list);

	List<L120S04C> findL120s04cByMainIdDocKind(String mainId, String[] docKind);

	List<L120S04B> findL120s04bByMainIdDocKind(String mainId, String[] docKind);

	void importL120s04b_Risk_weighted_Assets(String mainId,
			String queryDateS, String queryDateE, JSONObject jsonData,
			BranchRate branchRate, String keyCustId, String keyDupNo)
			throws CapException;

	void importL120s04b_Risk_weighted_Assets_exclude_interest(
			String mainId, String queryDateS, String queryDateE,
			JSONObject jsonData, BranchRate branchRate, String keyCustId,
			String keyDupNo) throws CapException;

	public L120M01I findL120m01iByMainId(String mainId);

	boolean changeCustShowSeqNum(L120S01A l120s01a, boolean upOrDown);

	boolean resetL120S01AAllCustShowSeqNum(String mainId);

	Integer addL120S01ACustShowSeqNumToMax(L120S01A l120s01a);

	/**
	 * 取得所有私募基金資料 J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 * 
	 * @return
	 */
	public List<L902M01A> findL902m01aList();

	/**
	 * 取得隸屬私募基金旗下事業BY CUSTID J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 * 
	 * @return
	 */
	public List<L902S01A> findL902s01aByCustId(String custId, String dupNo);

	/**
	 * 取得隸屬私募基金旗下事業BY CUSTID J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 * 
	 * @return
	 */
	public List<L902S01A> findL902s01aByRptMainIdAndCustId(String rptMainId,
			String custId, String dupNo);

	/**
	 * J-105-0264-001 WEB-ELOAN授信案件簽報書增訂「新臺幣、美元利率定價合理性分析表」。
	 * 
	 * @param mainId
	 * @return
	 */
	public List<L120S08A> findL120s08aByMainId(String mainId);

	/**
	 * J-105-0264-001 WEB-ELOAN授信案件簽報書增訂「新臺幣、美元利率定價合理性分析表」。
	 * 
	 * @param mainId
	 * @param curr
	 * @return
	 */
	public List<L120S08A> findL120s08aByMainIdCurr(String mainId, String curr);

	/**
	 * J-105-0264-001 WEB-ELOAN授信案件簽報書增訂「新臺幣、美元利率定價合理性分析表」。
	 * 
	 * @param oid
	 * @return
	 */
	public L120S08A findL120s08aByOid(String oid);

	/**
	 * J-105-0264-001 WEB-ELOAN授信案件簽報書增訂「新臺幣、美元利率定價合理性分析表」。
	 * 
	 * @param mainId
	 * @param curr
	 * @param seqNo
	 * @return
	 */
	public List<L120S08B> findL120s08bByMainIdCurrSeqNo(String mainId,
			String curr, BigDecimal seqNo);

	/**
	 * J-112-0217 配合修訂「本行授信利率暨保證及承兌手續費計收標準實施要點」，修改e-loan簽報書_利率定價合理性及收益率分析表格頁籤。
	 * 
	 * @param mainId
	 * @param curr
	 * @param seqNo
	 * @param itemName
	 * @return
	 */
	public L120S08B findL120s08bByMainIdCurrSeqNoItemName(String mainId,
			String curr, BigDecimal seqNo, String itemName);

	/**
	 * J-105-0264-001 WEB-ELOAN授信案件簽報書增訂「新臺幣、美元利率定價合理性分析表」。
	 * 
	 * @param list
	 */
	public void deleteListL120s08b(List<L120S08B> list);

	/**
	 * J-105-0264-001 WEB-ELOAN授信案件簽報書增訂「新臺幣、美元利率定價合理性分析表」。
	 * 
	 * @param list
	 */
	public void saveListL120s08b(List<L120S08B> list);

	/**
	 * J-105-0264-001 WEB-ELOAN授信案件簽報書增訂「新臺幣、美元利率定價合理性分析表」。
	 * 
	 * @param oids
	 * @return
	 */
	public List<L120S08A> findL120s08aByOids(String[] oids);

	/**
	 * 引進集團貢獻度 J-105-0264-001 WEB-ELOAN授信案件簽報書增訂「新臺幣、美元利率定價合理性分析表」。
	 */
	public void importGroupDataFromL120s04aList(String mainId, String queryDateS, String queryDateE,
			JSONObject jsonData, BranchRate branchRate) throws CapException;

	/**
	 * J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
	 * 透過JDBC取得借款人同一關係企業/集團企業授信明細檔
	 * 
	 * @param json
	 * @return
	 */
	public List<L120S05B> findL120s05b1_A(JSONObject json);

	/**
	 * J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
	 * 同一關係企業/集團企業
	 * 
	 * @param list
	 * @param l120s05a
	 * @param json
	 * @return
	 */
	public L120S05A saveAndQueryListL120s05a_A(List<L120S05B> list,
			L120S05A l120s05a, JSONObject json, boolean hasRelGrp);

	/**
	 * J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
	 * 同一關係企業/集團企業
	 */
	public L120S05A findl120s05a1_A(JSONObject json);

	/**
	 * J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
	 * 
	 * @param mainId
	 * @param l120s05a
	 * @return
	 */
	public void setGrpRate(L120S05A l120s05a);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public List<L120M01J> findL120m01jByMainId(String mainId);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public List<L120M01J> findL120m01jByMainIdType(String mainId, String type);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public void deleteL120m01jByMainId(String mainId);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public void deleteL120m01jByMainIdAndType(String mainId, String type);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public List<L120M01J> findL120m01jByMainIdTypeCustId(String mainId,
			String type, String custId, String dupNo);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public List<L120M01J> findL120m01jByMainIdTypeCustId2(String mainId,
			String type, String custId, String dupNo);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public L120M01J findL120m01jByMainIdTypeCustIdCntrNo(String mainId,
			String type, String custId, String dupNo, String custId2,
			String dupNo2, String cntrNo);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public int findL120m01jByMainIdTypeAndCustIdMax(String mainId, String type,
			String custId, String dupNo);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public L120M01J findL120m01jByMainIdTypeCsutIdItemSeq(String mainId,
			String type, String custId, String dupNo, Integer itemSeq);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public String convertRateJsonStringToDescr(String mainCurr,
			String refRateStr);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public boolean chkIsNeedARBuyer(String snoKind, String isEfin,
			L140M01A l140m01a);

	/**
	 * 取得a-Loan未銷戶額務序號未包含於e-Loan報案內之額度序號 J-107-0184_05097_B1001 Web
	 * e-loan企金授信簽報時提供以借款人查詢應簽報的額度明細表及該客戶所有的往來分行
	 * (包含前次簽報書所簽報額度明細表所屬分行及現有有效額度的往來分行)等資訊,並於送呈前進行差異比對, 就存在差異時提供警示訊息,
	 * 以避免錯選授信案件授權層級情事。
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public HashMap<String, String> findAloanCntrnoLost(String mainId)
			throws CapException;

	/**
	 * 取得e-Loan未動用額務序號未包含於e-Loan報案內之額度序號 J-107-0184_05097_B1001 Web
	 * e-loan企金授信簽報時提供以借款人查詢應簽報的額度明細表及該客戶所有的往來分行
	 * (包含前次簽報書所簽報額度明細表所屬分行及現有有效額度的往來分行)等資訊,並於送呈前進行差異比對, 就存在差異時提供警示訊息,
	 * 以避免錯選授信案件授權層級情事。
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public HashMap<String, String> findEloanCntrnoLost(String mainId)
			throws CapException;

	/**
	 * J-107-0233_05097_B1001 Web e-Loan企金授信修訂「放款定價合理性分析表」。
	 * 
	 * @param mainId
	 * @param curr
	 * @return
	 */
	public List<L120S08A> findL120s08aByMainIdCurrCustIdPrintGroup(
			String mainId, String curr, String custId, String dupNo,
			BigDecimal printGroup);

	/**
	 * J-107-0233_05097_B1001 Web e-Loan企金授信修訂「放款定價合理性分析表」。
	 */
	public L120S08A findL120s08aMaxSeqNoByMainIdAndCurr(String mainId,
			String curr);

	/**
	 * J-107-0233_05097_B1001 Web e-Loan企金授信修訂「放款定價合理性分析表」。
	 */
	public List<L120S08A> findL120s08aByMainIdAndVersionDate(String mainId,
			String versionDate);

	/**
	 * J-107-0233_05097_B1001 Web e-Loan企金授信修訂「放款定價合理性分析表」。
	 */
	public void importL120s08bDataFromL120s04aList_20181001(
			String kind, String mainId, String queryDateE, JSONObject jsonData,
			BranchRate branchRate, String qCustId, String qDupNo,
			boolean excludeInterest) throws CapException;

	/**
	 * 實績貢獻 J-110-0155 修改e-loan授信管理系統簽報書之「利率定價合理性分析表」為「新臺幣、美元利率定價合理性及收益率分析表」
	 */
	public void importL120s08bDataFromL120s04aList_20210501(
			String kind, String mainId, String queryDateE, JSONObject jsonData,
			BranchRate branchRate, String qCustId, String qDupNo)
			throws CapException;

	/**
	 * 實績貢獻 J-112-0217 與2021的有些微差異
	 */
	public void importL120s08bDataFromL120s04aList_20230301(
			String kind, String mainId, String queryDateE, JSONObject jsonData,
			BranchRate branchRate, String qCustId, String qDupNo)
			throws CapException;

	/**
	 * 實績貢獻 J-112-0217 與2021的有些微差異 針對組合出來的資料要再做處理
	 */
	public void importL120s08bDataFromL120s04aList_20230301_process(
			String mainId, String queryDateE,
			JSONObject jsonData, BranchRate branchRate, String qCustId,
			String qDupNo) throws CapException;

	C120M01A findC120M01AByOid(String oid);

	/**
	 * J-108-0243 微型企業
	 */
	public List<L120S10A> findL120s10aByMainId(String mainId);

	public L120S10B findL120s10bByMainId(String mainId);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 */
	public List<L120S11A> findL120s11aByMainId(String mainId);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 */
	public void deleteL120s11aByMainId(String mainId);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 */
	public List<L120S11A> findL120s11aByMainIdCustId(String mainId,
			String custId, String dupNo);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 */
	public L120S11A findL120s11aByMainIdCustIdAndCustId2(String mainId,
			String custId, String dupNo, String custId2, String dupNo2);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 */
	public int findL120s11aByMainIdTypeAndCustIdMax(String mainId,
			String custId, String dupNo);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 */
	public L120S11A findL120s11aByMainIdCsutIdItemSeq(String mainId,
			String custId, String dupNo, Integer itemSeq);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 */
	public List<L120S11A> findL120s11aByMainIdCustId2(String mainId,
			String custId2, String dupNo2);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 */
	public void saveListL120s11a(List<L120S11A> list);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊 本案無追索買方額度資訊
	 */
	public void deleteListL120s11a(List<L120S11A> list);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param mainId
	 */
	public void applyRelatedCompany(String mainId) throws CapMessageException;

	public L120S14A findL120s14aByMainId(String mainId);

	public <T extends GenericBean> T findL120s14ByMainId(Class clazz,
			String mainId);

	public List<? extends GenericBean> findL120s14ListByMainId(Class clazz,
			String mainId);

	public <T extends GenericBean> T findL120s14ByMainIdCntrNo(Class clazz,
			String mainId, String cntrNo);

	public L120S05E findL120s05eByOid(String oid);

	public L120S05E findL120s05eByUniqueKey(String mainId, String custId,
			String dupNo);

	public L120S05F findL120s05fByOid(String oid);

	public L120S05F findL120s05fByUniqueKey(String mainId, String custId,
			String dupNo, String custId1, String dupNo1);

	public L120S05E saveAndQueryListL120s05f(List<L120S05F> list,
			L120S05E l120s05e, JSONObject json, String custId, String dupNo);

	public List<L120S05F> findL120s05fByMainIdCustIdDupNo(String mainId,
			String custId, String dupNo);

	public void deleteListL120s05f(List<L120S05F> list);

	public L120S05E findl120s05e1(JSONObject json, String custId, String dupNo);

	public List<L120S05F> findL120s05f1(JSONObject json, String custId,
			String dupNo);

	public void setGrpRate(L120S05E l120s05e);

	public List<L120S05F> findL120s05f1_A(JSONObject json, String custId,
			String dupNo, String custName);

	public L120S05E saveAndQueryListL120s05e_A(List<L120S05F> list,
			L120S05E l120s05e, JSONObject json, boolean hasRelGrp,
			String custId, String dupNo);

	public L120S05E findl120s05e1_A(JSONObject json, String custId, String dupNo);

	/**
	 * J-109-0371_05097_B1002 簡化青年創業及啟動金貸款簽報書簽案流程
	 * 
	 * @param mainId
	 * @return
	 */
	public L120S14E findL120s14eByMainId(String mainId);

	public String getStockNoByCustId(String custId) throws CapMessageException;

	/**
	 * J-109-0370_05097_B1001 配合企金一般簽報書格式修訂，新增、調整簽報作業相關內容
	 * 
	 * 引進TCRI信用風險
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 * @throws CapException
	 */
	public Map<String, String> queryTCRI(String custId, String dupNo)
			throws CapException;

	/**
	 * J-109-0370_05097_B1001 配合企金一般簽報書格式修訂，新增、調整簽報作業相關內容
	 * 
	 * @param t_score1
	 * @return
	 */
	public Map<String, String> formatTCRIGrade(String t_score1);

	public L120S16A findL120s16aByOid(String oid);

	public L120S16A findL120s16aByUniqueKey(String mainId, String custId,
			String dupNo, String cntrNo);

	public List<L120S16A> findL120s16aByMainId(String mainId);

	public void deleteListL120s16a(List<L120S16A> list);

	public void saveListL120s16a(List<L120S16A> listL120s16a,
			List<L120S16B> listL120s16b);

	public void deleteListL120s16a(String[] oidArray);

	public L120S16B findL120s16bByOid(String oid);

	public L120S16B findL120s16bByUniqueKey(String mainId, String type,
			String custId, String dupNo, String cntrNo, String itemType);

	public List<L120S16B> findL120s16bByMainId(String mainId);

	public void deleteListL120s16b(List<L120S16B> list);

	public void deleteListL120s16ab(List<L120S16A> listL120s16a,
			List<L120S16B> listL120s16b);

	public L120S16C findL120s16cByOid(String oid);

	public L120S16C findL120s16cByUniqueKey(String mainId, String custId,
			String dupNo);

	public List<L120S16C> findL120s16cByMainId(String mainId);

	public void deleteListL120s16c(List<L120S16C> list);

	public void saveListL120s16c(List<L120S16C> listL120s16c);

	public void deleteListL120s16c(String[] oidArray);

	public L120S16A findL120s16aByMainIdCustIdCntrNo(String mainId,
			String custId, String dupNo, String cntrNo);

	public List<L120S16B> findL120s16bByMainIdCustIdCntrNo(String mainId,
			String custId, String dupNo, String cntrNo);

	public List<Map<String, Object>> getBorrowsInner(String mainId,
			String printCondition);

	/**
	 * J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
	 * 
	 * @param oid
	 * @return
	 */

	public L120S17A findL120s17aByOid(String oid);

	/**
	 * J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
	 * 
	 * @param mainId
	 * @return
	 */
	public L120S17A findL120s17aByMainId(String mainId);

	/**
	 * 重新引進查詢名單
	 * 
	 * @param mainId
	 */
	public void importQueryList(String oid) throws CapException;

	public void reSetL161S01E(String mainId, String custId, String dupNo,
			String custName, String newRelation);

	/**
	 * RPA一鍵發查
	 * 
	 * @param mainId
	 */
	public void queryRpaQuery(String oid) throws CapException;

	/**
	 * RPA重新查詢
	 * 
	 * @param mainId
	 */
	public void queryRpaRetry(String oid) throws CapException;

	/**
	 * 取得小規模簽報書
	 * 
	 * @param brNo
	 * @param custId
	 * @return
	 */
	public List<L120M01A> findByBranchCustIdIsSmallBuss(String brNo,
			String custId);

	/**
	 * 取得徵信報告書 使用者輸入之Id 下的 文件編號(資信簡表用)
	 * 
	 * @param caseBrId
	 * @param custId
	 * @param search
	 * @return
	 */
	Page<Map<String, Object>> getCesMainIdaByCustId(String caseBrId,
			String custId, String dupNo, ISearch search);

	public void saveWithoutUpdater(GenericBean... entity);

	public Page<Map<String, Object>> getCes140MainIdaByCustId(String caseBrId,
			String custId, String dupNo, ISearch search);

	/**
	 * 取得國發基金協助新創事業紓困融資加碼方案
	 * 
	 * @param brNo
	 * @param custId
	 * @return
	 */
	public List<L120M01A> findByBranchCustIdIsStartUpRelief(String brNo,
			String custId, String caseType);

	/**
	 * J-110-0493_11557_B1001 檢查利害關係人授信額度合計是否達新台幣1億元以上
	 * 
	 * @param mainId
	 * @param l120m01a
	 * @return
	 */
	public boolean checkRltOver100Million(String mainId, L120M01A l120m01a);

	/**
	 * J-111-0283 E-LOAN簽報，關係企業額度已逾分行權限，在案件簽報畫面明顯位置處，加註「疑似逾越授權」之註記
	 * 
	 * 清除授信額度、出口押匯額度、單獨劃分授信額度逾權的註記
	 * 
	 * @param mainId
	 * @param amtIsOverAuth
	 * @param experfAmtIsOverAuth
	 * @param aloneLoanIsOverAuth
	 */
	public void clearCheckAmtIsOverAuth(String mainId, boolean amtIsOverAuth,
			boolean experfAmtIsOverAuth, boolean aloneLoanIsOverAuth);

	/**
	 * J-111-0536 顯示逾權計算公式
	 * 
	 * 清除授信額度、出口押匯額度、單獨劃分授信額度逾權的計算說明
	 * 
	 * @param mainId
 	 * @param amtIsOverAuth
	 * @param experfAmtIsOverAuth
	 * @param aloneLoanIsOverAuth
	 */
	public void clearL120m01kAmtIsOverAuth(String mainId,
			boolean amtIsOverAuth, boolean experfAmtIsOverAuth,
			boolean aloneLoanIsOverAuth);

	/**
	 * J-111-0283 E-LOAN簽報，關係企業額度已逾分行權限，在案件簽報畫面明顯位置處，加註「疑似逾越授權」之註記
	 * 
	 * 壓上授信額度、出口押匯額度、單獨劃分授信額度逾權的註記
	 * 
	 * @param mainId
	 * @param amtIsOverAuth
	 * @param experfAmtIsOverAuth
	 * @param aloneLoanIsOverAuth
	 */
	public void remarkAmtIsOverAuth(String mainId, boolean amtIsOverAuth,
			boolean experfAmtIsOverAuth, boolean aloneLoanIsOverAuth);

	/**
	 * J-111-0461
	 * 
	 * 逾越授權新版，匯判斷要走原版的有擔/無擔，還是要走LGD的方式
	 * 
	 * @param mainId
	 * @param l120m01a
	 * @param listL140m01a
	 * @return
	 */
	public String checkAmtIsOverChooseWay(String mainId, L120M01A l120m01a,
			List<L140M01A> listL140m01a) throws CapMessageException;

	/**
	 * J-110-0325-001 為改善營業單位適用合併關係企業簽報逾越授權，eloan增加相關控管機制
	 * 
	 * 檢查 合併關係企業額度彙總表之授信合併額度(不含借戶本身) + 借戶本身之授信案件額度明細表額度合計之現請額度 > 簽報分行之授信業務授權限額
	 * 是否有超過簽報分行之授信業務授權限額，回傳超過限額的項目
	 * 
	 * @param mainId
	 * @param l120m01a
	 * @param listL140m01a
	 * @return
	 */
	public String checkAmtIsOver(String mainId, L120M01A l120m01a,
			List<L140M01A> listL140m01a);

	/**
	 * J-111-0461
	 * 
	 * 逾越授權新版，走LGD的檢核
	 * 
	 * @param mainId
	 * @param l120m01a
	 * @param listL140m01a
	 * @return
	 */
	public String checkAmtIsOverByLGD(String mainId, L120M01A l120m01a,
			List<L140M01A> listL140m01a);

	/**
	 * J-110-0343-001 為改善營業單位適用合併關係企業簽報逾越授權，eloan增加出口押匯相關控管機制
	 * 
	 * 檢查 若不含借戶本身之其他關係企業額度彙總表之出口押匯各項合併額度+出口押匯案件額度明細表之現請額度>簽報分行之出口押匯業務相關授權限額
	 * 是否有超過簽報分行之授信業務授權限額，回傳超過限額的項目
	 * 
	 * @param mainId
	 * @param l120m01a
	 * @param listL140m01a
	 * @return
	 */
	public String checkExperfAmtIsOver(String mainId, L120M01A l120m01a,
			List<L140M01A> listL140m01a);

	/**
	 * J-11X-XXXX-001 為改善營業單位適用合併關係企業簽報逾越授權，eloan增加相關控管機制
	 * 
	 * 增加「納入授權額度限額計算註記」4者之限額檢核，針對同一客戶之授信業務額度， 計算「納入授權額度限額計算註記」為4者之現請額度合計
	 * 
	 * @param mainId
	 * @param l120m01a
	 * @param listL140m01a
	 * @return
	 */
	public String checkAloneAmtIsOver(String mainId, L120M01A l120m01a,
			List<L140M01A> listL140m01a);


	/**
	 * J-111-0107_05097_B1001 Web e-Loan企金增加借戶ESG外部綜合評分資料相關資料。
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public CapAjaxFormResult applyEsg(String mainId, String custId, String dupNo);

	public Map<String, Object> getESG_RateByCustId(String custId);

	public Map<String, Object> getESG_MaxDate();

	/**
	 * J-111-0208_05097_B1004 Web
	 * e-Loan國內外企金授信簽報，各額度LGD隨核定內容上送ALOAN、AS400(ELF383)
	 */
	public String chkCntrNoWithoutLgd(L120M01A model);

	/**
	 * J-110-0485_05097_B1006 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
	 */
	public String chkCntrNoLgdWithoutColl(L120M01A model);

	/**
	 * J-110-0485_05097_B1006 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
	 */
	public boolean chkCntrNoNeedLgd(L140M01A l140m01a);

	/**
	 * J-110-0485_05097_B1006 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
	 */
	public String chkLgdInfo(L120M01A model);

	/**
	 * 
	 * J-111-0112_05097_B1002 Web e-Loan企金授信管理系統新增111年經濟部紓困方案
	 * 
	 * 檢核額度不可以有同時動用過兩代以上之紓困代碼，如紓困代碼為111年度時，前年度紓困代碼不可以是109年
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param rescueItem
	 * @param rescueItemOn
	 * @return
	 */
	public String chkRescueItemHasTwoCross(String custId, String dupNo,
			String cntrNo, String rescueItem, String rescueItemOn);

	/**
	 * chkType J-111-0303_05097_B1001 Web e-Loan企金授信管理系統新增111年經濟部紓困方案
	 * 
	 * 檢核紓困代碼
	 * 
	 * @param rescueItem
	 * @param chkType
	 *            1:簽報書，2:動審表
	 * @return
	 */
	public String chkRescueItemNotEffect(String rescueItem, String chkType);

	/**
	 * J-111-0397 RWA
	 **/
	public void saveL120s23aList(List<L120S23A> l120s23aList);

	public List<L120S23A> findL120s23aByMainId(String mainId);

	public List<L120S23A> findL120s23aByMainIdCntrNo(String mainId,
			String cntrNo);

	public void deleteL120s23aList(String[] oidArray);

	public void deleteL120s23as(List<L120S23A> l120s23as);

	public L120S23A findL120s23aByOid(String oid);

	public List<L120S24A> findL120s24aByMainIdCntrNo(String mainId,
			String cntrNo);

	public L120S25A findL120s25aByOid(String oid);

	public void saveL120s25aList(List<L120S25A> l120s25aList);

	public void deleteL120s25aList(String[] oidArray);

	public List<L120S25A> findL120s25aByMainId(String mainId);

	public List<L120S25A> findL120s25aByMainIdAndCntrNo(String mainId,
			String cntrNo);

	public List<L120S25A> findL120s25aByMainIdAndCustId(String mainId,
			String bisCustId_s25a, String bisDupNo_s25a);

	public List<L120S25B> findL120s25bByAcKey(String acKey);

	public List<L120S25C> findL120s25bByParamTypeDate(String paramType,
			String paramDate);

	public List<L120S25C> findL120s25bParamTypeKeyDate(String paramType,
			String paramKey, String paramDate);

	/**
	 * J-113-0044_12473_B1001 儲存試行新模型資料
	 */
	public void saveTmpNewMow(Map<String, Object> CesM100m01aMap, String mainId);

	/**
	 * J-110-0485_05097_B1009 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
	 * 
	 * @param l120m01a
	 * @return
	 */
	public String getLmsLgdReportTitleR42(L120M01A l120m01a);

	/**
	 * J-111-0535_05097_B1001 Web e-Loan企金授信配合「ESG綜效調查表 」建置，於簽報書增設相對應欄位
	 */
	public List<Map<String, Object>> findEsgReject(String custId, String dupNo);

}