var L901VAction = {
    fhandler: "lms9101m01formhandler",
    $form: $("#lms9101tabForm"),
    $formSingle: $("#lms9101tabFormSingle"),
    gridId: "#gridfile"
};
/**
 * 開啟上傳(單筆)視窗
 */
function openUpdateSingle(){
	
    var $form = $('#lms9101tabFormSingle');
	$form.setValue({
			queryData : '1'
		}, true);
    
    // 選取的縣市名稱
    var JobClassCode = $form.find('#fJobClass').val();
    var Code = String.fromCharCode(97 + JobClassCode);
	// 取得編號
    var combos = CommonAPI.loadCombos(['lms1205s01_jobType1', 'lms1205s01_jobType2' + Code, 'lms1205s01_jobTitle']);
    // 下拉式選單(職業別)
    $form.find("#JobClass").setItems({
        item: combos['lms1205s01_jobType1'],
        format: "{key}",
        value: JobClassCode,
        fn: function(){
            var $form = $('#lms9101tabFormSingle');
            var JobClassCode = parseInt($form.find('#JobClass').val(), 10);
            var Code = String.fromCharCode(96 + JobClassCode);
            var combos = CommonAPI.loadCombos(['lms1205s01_jobType1', 'lms1205s01_jobType2' + Code, 'lms1205s01_jobTitle']);
            $form.find('#Job').setItems({
                item: combos['lms1205s01_jobType2' + Code],
                format: '{key}',
                value: $form.find('#Job').val()
            });
        }
    });
    // 下拉式選單(職業)
    $form.find("#Job").setItems({
        item: combos['lms1205s01_jobType2'],
        format: "{key}",
        value: $form.find('#Job').val()
    });
    // 下拉式選單(職稱)
    $form.find("#jTitle").setItems({
        item: combos['lms1205s01_jobTitle'],
        format: "{key}",
        value: $form.find('#jTitle').val()
    });
    $("#lms9101newSingle").thickbox({
        width: 680,
        height: 250,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                if ($("#lms9101tabFormSingle").valid()) {
                    var $form = $('#lms9101tabFormSingle');
                    $.ajax({
                        handler: L901VAction.fhandler,
                        action: "querysingleUpdate",
                        type: 'post',
                        async: false,
                        data: {
                            custId: $form.find("#custId").val(),
                            tDup: $form.find("#tDup").val(),
                            yPay: $form.find("#yPay").val(),
                            oMoney: $form.find("#oMoney").val(),
                            JobClass: $form.find("#JobClass").val() +
                            $form.find("#Job").val() +
                            $form.find("#jTitle").val()
                        },
                        success: function(responseData){
                        }
                    });
                }
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}

/**
 * 開啟上傳(整批)視窗
 */
function openUpdate(){
    // 開啟上傳(整批)視窗
    var limitFileSize = 3145728;
    var mainId = null;
    var oId = null
    $.ajax({
        handler: L901VAction.fhandler,
        action: "getmianId",
        type: 'post',
        async: false,
        success: function(responseData){
            mainId = responseData['mainId'];
        }
    });
    MegaApi.uploadDialog({
        fieldId: "lms9101update",
        fieldIdHtml: "size='30'",
        fileDescId: "fileDesc",
        fileDescHtml: "size='30' maxlength='30'",
        width: 320,
        height: 190,
        data: {
            mainId: mainId,
            sysId: "LMS",
            pId: ""
        },
        success: function(obj){
            oId = obj['fileKey'];
            $.ajax({
                handler: L901VAction.fhandler,
                action: "queryupdate2",
                type: 'post',
                async: false,
                data: {
                    mainId: mainId,
                    sysId: "LMS",
                    oId: obj['fileKey'],
                    fieldId: "lms9101update",
                    name: $("#lms9101update").val()
                },
                success: function(responseData){
                
                    $.capFileDownload({
                        handler: "simplefiledwnhandler",
                        data: {
                            fileOid: oId
                        }
                    });
                    $('#gridfile').trigger("reloadGrid");
                    
                }
            });
        }
    });
    
}

/**
 * 取得借款人所得空白資料存成EXCEL
 */
function datadl(){
    $.form.submit({
        url: "../fms/lms9101r01.xls",
        target: "_blank",
        data: {
            fileDownloadName: "LMS9101PreList.xls",
            serviceName: "lms9101xlsservice"
        }
    });
}

//檔案下載
function download(cellvalue, options, data){
    $.capFileDownload({
        handler: "simplefiledwnhandler",
        data: {
            fileOid: data.oid
        }
    });
}

$(function(){
    //檔案上傳grid
    var L910M01aGrid = $('#gridfile').iGrid({
        handler: "lms9101gridhandler",
        height: 350,
        rowNum: 15,
        postData: {
            formAction: "queryfile",
            fieldId: "lms9101update"
        },
        colModel: [{
            colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
            name: 'srcFileName',
            width: 120,
            align: "left",
            sortable: true,
            formatter: 'click',
            onclick: download
        }, {
            colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
            name: 'fileDesc',
            width: 140,
            align: "center",
            sortable: true
        }, {
            colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
            name: 'uploadTime',
            width: 140,
            align: "center",
            sortable: true
        }, {
            name: 'oid',
            hidden: true
        }]
    
    });
    // 開啟下載(EXECL)視窗
    $("#getdata").click(function(){
        datadl();
    });
    // 開啟上傳(EXECL)視窗
    $("#updata").click(function(){
        openUpdate();
    });
    // 開啟上傳(單筆)視窗
    $("#updatasingle").click(function(){
        openUpdateSingle();
    });
    
});
