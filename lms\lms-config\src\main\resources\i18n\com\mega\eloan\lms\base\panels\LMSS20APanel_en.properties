#J-109-0067_05097_B1001 Web e-Loan \u6388\u4fe1\u5c0d\u65bc\u7121\u9700\u8fa8\u8b58\u5be6\u8cea\u53d7\u76ca\u4eba\u4e4b\u6cd5\u4eba\u4e3b\u9ad4\uff0c\u7c3d\u5831\u66f8\u53ca\u984d\u5ea6\u660e\u7d30\u8868\u9700\u51fa\u73fe\u7121\u5be6\u8cea\u53d7\u76ca\u4eba
nohave=No
notApplicable=Not Applicable

#J-106-0029-002  \u6d17\u9322\u9632\u5236-\u65b0\u589e\u6d17\u9322\u9632\u5236\u9801\u7c64
L120S09a.blacklistQuery=Anti-Money Laundering/Combating the Financing of Terrorism
L120S09a.reApplyBlacklist=Import the query list
L120S09a.queryBlacklist=Blacklist check all 
L120S09a.queryDateS=Blacklist query date
L120S09a.addNew=Add
L120S09a.delete=Delete
L120S09a.showMemo=The query results show instructions\uff1a\u25ce:Not listed on the blacklist&nbsp;&nbsp; \u2605:Listed on the blacklist&nbsp;&nbsp;\u25b3:May be listed on the blacklist&nbsp;&nbsp;\u2573:Reject transaction
L120S09a.showMemo2=*\u6383\u63cf\u9ed1\u540d\u55ae\u6642\u9ede\u4fc2\u5305\u542b1.\u5fb5\u4fe1 2.\u7c3d\u6848 3.\u52d5\u5be9\u7b49\u968e\u6bb5\u3002
L120S09a.inputBlacklist=Edit AML/CFT list details
L120S09a.createBY=Data generation mode
L120S09a.createBY1=System
L120S09a.createBY2=Artificial
L120S09a.custRelation=Relationship with the case
L120S09a.checkbox1=Borrower
L120S09a.checkbox2=Co-borrower
L120S09a.checkbox3=Principal
L120S09a.checkbox4=Association guarantor
L120S09a.checkbox5=Collateral provider
L120S09a.checkbox6=Affiliated company
L120S09a.checkbox7=Effective Bene
L120S09a.checkbox8=General guarantor
L120S09a.checkbox9=Factoring buyer without recourse
#J-107-0070-001  Web e-Loan \u570b\u5167\u5fb5\u4fe1\u3001\u7c3d\u5831\u3001\u52d5\u5be9AML\u9801\u7c64\u8acb\u5c07\u300c\u9ad8\u968e\u7ba1\u7406\u4eba\u54e1\u300d\u7d0d\u5165\u61c9\u67e5\u8a62\u6bd4\u5c0d\u9ed1\u540d\u55ae\u4e4b\u5c0d\u8c61\u3002
L120S09a.checkbox10=Senior management
#J-108-0039_05097_B1001 Web e-Loan \u570b\u5167\u4f01\u91d1\u6388\u4fe1\u7cfb\u7d71\u7c3d\u5831\u3001\u52d5\u5be9AML\u9801\u7c64\u5c07\u501f\u6236\u4e4b\u300c\u5177\u63a7\u5236\u6b0a\u4eba\u300d\u7d0d\u5165\u61c9\u67e5\u8a62\u6bd4\u5c0d\u9ed1\u540d\u55ae\u4e4b\u5c0d\u8c61\u3002
L120S09a.checkbox11=Controlling Person
L120S09a.blacklistContent=List content
L120S09a.custId=ID
L120S09a.dupNo=Repeat Serial No.
L120S09a.custName=Name
L120S09a.apply=Query
L120S09a.custEName=English name
L120S09a.blackListCode=Result
L120S09a.memo=Hit list
L120S09a.importByExl=Import EXCEL
L120S09a.importBlacklistByExcel=Import the EXCEL list
L120S09a.upLoad=Upload
L120S09a.success=Success
L120S09a.excelDownload=Download EXCEL
#J-106-0238-001 \u56e0\u61c9\u65bce-Loan\u6388\u4fe1\u7ba1\u7406\u7cfb\u7d71\u4f01\u3001\u500b\u91d1\u5fb5\u3001\u6388\u4fe1\u696d\u52d9\u9632\u5236\u6d17\u9322\u4f5c\u696d\u9801\u7c64\uff0c\u5c0d\u61c90015\u9ed1\u540d\u55ae\u6aa2\u6838\u547d\u4e2d\u5be9\u67e5\u4e4b\u5f8c\u7e8c\u4f5c\u696d\uff0c\u589e\u52a0\u300c\u9ed1\u540d\u55ae/\u9ed1\u570b\u5bb6/\u653f\u6cbb\u654f\u611f\u4eba\u7269\u4ea4\u6613\u5177\u9ad4\u6aa2\u6838\u6a5f\u5236\u300d
L120S09a.cm1AmlStatus=0024 reject transaction note
L120S09a.checkSeq=Chk.No
L120S09a.sendAmlList=Send list scan
L120S09a.checkAmlResult=Obtain blacklist results
L120S09a.ncResult=Black list case investigation result
L120S09a.refNo=Reference number
L120S09a.uniqueKey=Batch number
L120S09a.openRouteRule=Turn on the hit code description
#J-106-0238-003 \u56e0\u61c9\u65bce-Loan\u6388\u4fe1\u7ba1\u7406\u7cfb\u7d71\u4f01\u3001\u500b\u91d1\u5fb5\u3001\u6388\u4fe1\u696d\u52d9\u9632\u5236\u6d17\u9322\u4f5c\u696d\u9801\u7c64\uff0c\u5c0d\u61c90015\u9ed1\u540d\u55ae\u6aa2\u6838\u547d\u4e2d\u5be9\u67e5\u4e4b\u5f8c\u7e8c\u4f5c\u696d\uff0c\u589e\u52a0\u300c\u9ed1\u540d\u55ae/\u9ed1\u570b\u5bb6/\u653f\u6cbb\u654f\u611f\u4eba\u7269\u4ea4\u6613\u5177\u9ad4\u6aa2\u6838\u6a5f\u5236\u300d
L120S09a.askAmlList=\u78ba\u8a8d\u6383\u63cf\u72c0\u614b(\u7ba1\u7406\u7528)
L120S09a.clearNcResult=\u6e05\u9664\u6848\u4ef6\u8abf\u67e5\u7d50\u679c(\u7ba1\u7406\u7528)
L120S09a.ncCaseId=Case Id
#J-107-0059-001 Web e-Loan \u6388\u4fe1\u7c3d\u5831\u66f8\u8207\u52d5\u5be9\u8868\u4e4bAML\u9801\u7c64\u53ca\u5217\u5370\u6aa2\u6838\u8868\u6642\uff0c\u589e\u52a0\u5f15\u9032\u98a8\u96aa\u7b49\u7d1a
L120S09a.luvRiskLevel=Risk level
L120S09a.showMemo1=Risk Level Description\uff1aL:Low&nbsp;&nbsp; M:Medium&nbsp;&nbsp;H:High&nbsp;&nbsp;</wicket:message>&nbsp;&nbsp;
L120S09a.riskLvl_L=Low
L120S09a.riskLvl_M=Medium
L120S09a.riskLvl_H=High
L120S09a.applyLuvRiskLevel=Obtain borrower risk level
#J-107-0176 \u914d\u5408\u4f01\u91d1\u8655\u91dd\u5c0dAML/CFT\u67e5\u8a62\u4f5c\u696d\uff0c\u4fee\u6539\u6388\u4fe1\u7ba1\u7406\u7cfb\u7d71\u4e4bAML/CFT\u67e5\u8a62\u4f5c\u696d\u529f\u80fd
L120S09a.caseBrId=Case Query Branch
L120S09a.queryUser=Case Query User
L120S09a.inputQueryrole=Type Query Role
#J-111-0141 \u91dd\u5c0d\u570b\u5167\u4f01\u91d1\u3001\u6d88\u91d1\u53ca\u6d77\u5916\u6388\u4fe1\u7c3d\u6848\u7cfb\u7d71\u4e4bAML\u9801\u7c64\uff0c\u589e\u52a0\u300c\u8abf\u67e5\u7d50\u679c\u8aaa\u660e\u300d\u6b04\u4f4d
L120S09a.ncResultRemark=\u5236\u88c1/\u7ba1\u5236\u540d\u55ae\u6383\u63cf\u8abf\u67e5\u7d50\u679c\u8aaa\u660e
L120S09a.highRiskRemark=\u9ad8\u98a8\u96aa\u8abf\u67e5\u7d50\u679c\u8aaa\u660e
L120S09a.ncResultRemarkTip=(\u8acb\u7c21\u8981\u8aaa\u660e\u8abf\u67e5\u7d50\u679c\uff0c\u53ef\u53c3\u7167A2\u8868\u6216C\u8868\u5167\u5bb9\uff1b\u82e5\u65bc\u5fb5\u4fe1\u6642\u9ede\u5df2\u5224\u5b9a\u53ef\u6558\u4f5c\u4e4bPEPs\u4eba\u7269\u4ea6\u61c9\u65bc\u6b64\u6b04\u8aaa\u660e)
L120S09a.highRiskRemarkTip=(\u8acb\u7c21\u8981\u8aaa\u660e\u8abf\u67e5\u7d50\u679c\uff0c\u53ef\u53c3\u7167F1\u8868\u6216B3\u8868\u5167\u5bb9)
#J-107-0248_05097_B1001 Web e-Loan\u4f01\u91d1\u6388\u4fe1\u7ba1\u7406\u7cfb\u7d71AML/CFT\u589e\u52a0\u570b\u5225\u7ba1\u5236\u540d\u55ae\u6383\u63cf\u529f\u80fd
L120S09a.country=Country
#I-107-0260_05097_B1001 Web e-Loan\u4f01\u91d1\u6388\u4fe1\u7cfb\u7d71\u589e\u52a0\u63d0\u793a\u8a0a\u606f\u3010\u8a72\u5ba2\u6236\u5c6c\u65bc\u5df4\u62ff\u99ac\u6587\u4ef6\u540d\u55ae\u3011
L120S09a.applyCustPanaInfo=Query primary borrower 0024-23 Panama Papers List Information
L120S09a.lnsp0130_1=Error code
L120S09a.lnsp0130_2=LNSP0130 CMPEPUPD does not return SP_RETURN result
L120S09a.lnsp0130_3=The following main borrowers are on the list of Panama Papers at 0024-23
L120S09a.lnsp0130_4=The main borrowers of this case is not listed in the Panama Papers list at 0024-23.
L120S09a.lnsp0130_5=No primary borrower information can be found in Panama Papers list
#J-107-0226_11557_B1001 \u7591\u4f3c\u6d17\u9322\u6216\u8cc7\u6050\u4ea4\u6613\u614b\u6a23\u6aa2\u6838\u8868-\u6388\u4fe1
L120S09a.stateChooseTitle=\u9078\u64c7\u501f\u6b3e\u4eba
L120S09a.stateChooseCustId=\u8eab\u5206\u8b49\u5b57\u865f/\u7d71\u4e00\u7de8\u865f
L120S09a.stateChooseDupNo=\u91cd\u8907\u5e8f\u865f
L120S09a.stateChooseCustName=\u5ba2\u6236\u540d\u7a31
L120S09a.amlStateCheckTable=\u7591\u4f3c\u6d17\u9322\u6216\u8cc7\u6050\u4ea4\u6613\u614b\u6a23\u6aa2\u6838\u8868
L120S09a.changeMessage1=\u4f9d\u5146\u9280\u7e3d\u6d17\u9632\u7b2c1110038750\u865f\u51fd\uff0c\u4fee\u8a02\u5f8c\u4e4b\u7591\u4f3c\u6d17\u9322\u6216\u8cc7\u6050\u4ea4\u6613\u614b\u6a23\u6aa2\u6838\u8868\uff0c\u6aa2\u8996\u6642\u9ede\u6539\u65bce-Loan\u7ba1\u7406\u7cfb\u7d71\u7de8\u88fd\u52d5\u7528\u5be9\u6838\u8868
L120S09a.stateVersionDate11311=11311\u7248
L120S09a.stateAutoSettingReason11311=\u67e5\u672a\u6709\u7591\u4f3c\u6d17\u9322\u3001\u8cc7\u6050\u53ca\u7570\u5e38\u4ea4\u6613\u60c5\u5f62\u3002
L120S09a.stateVersionDate11307=11307\u7248
L120S09a.stateVersionDate11107=11107\u7248
L120S09a.stateVersionDate10807=10807\u7248
L120S09a.stateAutoSettingReason=\u67e5\u672a\u6709\u7591\u4f3c\u6d17\u9322\u6216\u8cc7\u6050\u4ea4\u6613\u60c5\u5f62\u3002
#J-107-0226_11557_B1001 \u7591\u4f3c\u6d17\u9322\u6216\u8cc7\u6050\u4ea4\u6613\u614b\u6a23\u6aa2\u6838\u8868 for grid
L120S09a.stateCustName=\u5ba2\u6236\u540d\u7a31
L120S09a.stateCustId=\u8eab\u5206\u8b49\u5b57\u865f/\u7d71\u4e00\u7de8\u865f
L120S09a.stateCaseNo=\u4ea4\u6613\u7de8\u865f
L120S09a.stateCaseDate=\u4ea4\u6613\u65e5\u671f
L120S09a.stateCaseType=\u6aa2\u8996\u6642\u9ede
L120S09a.stateVersionDate=\u7248\u672c\u65e5\u671f
#// I-111-0089 \u8abf\u6574\u6388\u4fe1\u5be9\u67e5\u8655\u5b50\u7cfb\u7d71e-loan\uff0c\u9664\u5fb5\u4fe1\u7cfb\u7d71\u5916\uff0c\u5176\u9918\u6392\u9664\u6383\u7784PEPs\u540d\u55ae\u3002
L120S09a.passPeps=\u524d\u968e\u6bb5\u5df2\u6383PEPS
#J-112-0534 \u56e0\u61c9\u5146\u8c50\u91d1\u63a7\u81ea113.1.1\u4e0b\u67b6\u8b49\u5238\u9055\u7d04\u4ea4\u5272\u4e0a\u5e02\u6ac3\u89c0\u5bdf\u540d\u55ae\uff0c\u6545\u8abf\u6574E-Loan\u6388\u4fe1\u7ba1\u7406\u7cfb\u7d71\u76f8\u95dc\u6b04\u4f4d\u8cc7\u8a0a
L120S09a.importT70Result=\u5f15\u9032\u806f\u5fb5T70\u67e5\u8a62\u7d50\u679c
L120S09a.T70Title=\u806f\u5fb5T70\u8b49\u5238\u5546\u6388\u4fe1\u696d\u52d9\u8ca0\u9762\u4fe1\u7528\u8cc7\u6599
L120S09a.T70HtmlDoc=\u5831\u8868\u6a94\u6848
L120S09a.T70Date=\u8cc7\u6599\u56de\u8986\u65e5\u671f
L120S09a.T70Status=\u67e5\u8a62\u72c0\u614b
L120S09a.T70NegFlag=\u8ca0\u9762\u7d00\u9304
L120S09a.T70Amt=\u672a\u6e05\u511f\u7e3d\u9918\u984d
L120S09a.T70NegFlag.desc=\u8ca0\u9762\u7d00\u9304\u8aaa\u660e
L120S09a.T70NegFlag.N0=N0\uff1a\u53d7\u67e5\u6236\u65bc\u63ed\u9732\u671f\u9650\u5167\u7121\u6388\u4fe1\u8ca0\u9762\u7d00\u9304\uff0c\u4e14\u67e5\u8a62\u65e5\u671f\u65bc\u8b49\u5238\u5546\u806f\u5408\u5fb5\u4fe1\u7cfb\u7d71\u8207\u6388\u4fe1\u6a5f\u69cb\u7121\u6388\u4fe1\u5f80\u4f86\u8cc7\u8a0a\uff0c\u5373\u7121\u6388\u4fe1\u984d\u5ea6\u8cc7\u6599\uff1b
L120S09a.T70NegFlag.N1=N1\uff1a\u53d7\u67e5\u6236\u65bc\u63ed\u9732\u671f\u9650\u5167\u7121\u6388\u4fe1\u8ca0\u9762\u7d00\u9304\uff0c\u4e14\u67e5\u8a62\u65e5\u671f\u65bc\u8b49\u5238\u5546\u806f\u5408\u5fb5\u4fe1\u7cfb\u7d71\u8207\u6388\u4fe1\u6a5f\u69cb\u6709\u6388\u4fe1\u5f80\u4f86\u8cc7\u8a0a\uff0c\u5373\u6709\u6388\u4fe1\u984d\u5ea6\u8cc7\u6599\uff1b
L120S09a.T70NegFlag.Y0=Y0\uff1a\u53d7\u67e5\u6236\u65bc\u63ed\u9732\u671f\u9650\u5167\u6709\u6388\u4fe1\u8ca0\u9762\u7d00\u9304(\u9055\u7d04)\uff0c\u60df\u7d93\u8b49\u5238\u5546\u901a\u5831\u7d50\u6848\uff1b
L120S09a.T70NegFlag.Y1=Y1\uff1a\u53d7\u67e5\u6236\u65bc\u63ed\u9732\u671f\u9650\u5167\u6709\u6388\u4fe1\u8ca0\u9762\u7d00\u9304(\u9055\u7d04)\uff0c\u5c1a\u672a\u7d93\u8b49\u5238\u5546\u901a\u5831\u7d50\u6848\u3002
L120S09a.T70Amt.desc=\u672a\u6e05\u511f\u7e3d\u9918\u984d\u8aaa\u660e
L120S09a.T70Amt.desc1=\u8b49\u5238\u6388\u4fe1\u696d\u52d9\u539f\u59cb\u672a\u6e05\u511f\u7e3d\u9918\u984d\uff0c\u63ed\u9732\u91d1\u984d\u70ba\u300c\u4fe1\u7528\u4ea4\u6613(\u4e0d\u542b\u878d\u5238)\u300d\u3001\u300c\u8b49\u5238\u696d\u52d9\u501f\u8cb8\u6b3e\u9805\u300d\u53ca\u300c\u4e0d\u9650\u7528\u9014\u6b3e\u9805\u501f\u8cb8\u300d\u4e09\u7a2e\u696d\u52d9\u4e4b\u8ca0\u9762\u7d00\u9304\u539f\u59cb\u672a\u6e05\u511f\u9918\u984d\u5408\u8a08\u6578\u3002(\u82e5\u6388\u4fe1\u8ca0\u9762\u4fe1\u7528\u8cc7\u6599\u8a3b\u8a18\u70baN0\u3001N1\u3001Y0\uff0c\u8a72\u672a\u6e05\u511f\u7e3d\u9918\u984d\u7686\u70ba0)
#J-113-0082 \u914d\u5408\u6cd5\u52d9\u90e8\u65b0\u898f\uff0c\u65b0\u589e\u5f15\u5165\u300c\u53d7\u544a\u8aa1\u8655\u5206\u300d\u8cc7\u8a0a
L120S09a.importCmfwarnpResult=\u544a\u8aa1\u6236\u6383\u63cf
L120S09a.cmfwarnpResult=\u53d7\u544a\u8aa1\u8655\u5206
L120S09a.cmfwarnpResult.1=\u6709
L120S09a.cmfwarnpResult.2=\u7121
L120S09a.cmfwarnpResult.3=\u4e0d\u9069\u7528
L120S09a.cmfwarnpResult1.Desc=\u501f\u6b3e\u4ebaID&nbsp;{0}&nbsp;{1}\u65bc{2}\u88ab\u5217\u70ba\u300c\u6d17\u9322\u9632\u5236\u6cd5\u7b2c\u5341\u4e94\u689d\u4e4b\u4e8c\u7b2c\u516d\u9805\u5e33\u6236\u5e33\u865f\u66ab\u505c\u9650\u5236\u529f\u80fd\u6216\u9015\u4e88\u95dc\u9589\u7ba1\u7406\u8fa6\u6cd5\u300d\u6240\u7a31\u300c\u7d93\u88c1\u8655\u544a\u8aa1\u8005\u300d\uff0c\u8acb\u65bc\u9ad8\u98a8\u96aa\u6b04\u4f4d\u6216\u7d9c\u5408\u8a55\u4f30\u9801\u7c64\u88dc\u5145\u8aaa\u660e\u6558\u505a\u7406\u7531\u3002
L120S09a.cmfwarnpResult1.DescOverSea=\u501f\u6b3e\u4ebaID&nbsp;{0}&nbsp;{1}\u65bc{2}\u65bc\u53f0\u7063\u88ab\u5217\u70ba\u300c\u6d17\u9322\u9632\u5236\u6cd5\u7b2c\u5341\u4e94\u689d\u4e4b\u4e8c\u7b2c\u516d\u9805\u5e33\u6236\u5e33\u865f\u66ab\u505c\u9650\u5236\u529f\u80fd\u6216\u9015\u4e88\u95dc\u9589\u7ba1\u7406\u8fa6\u6cd5\u300d\u6240\u7a31\u300c\u7d93\u88c1\u8655\u544a\u8aa1\u8005\u300d\uff0c\u8acb\u6d77\u5916\u5206\u884c\u8a55\u4f30\u6558\u505a\u98a8\u96aa\uff0c\u8996\u500b\u6848\u81ea\u884c\u88dc\u5145\u8aaa\u660e\u3002
L120S09a.cmfwarnpResult3.Desc=\u53d7\u544a\u8aa1\u8655\u5206\u8cc7\u6599\u5eab\u66ab\u505c\u670d\u52d9\uff0c\u8acb\u6d3d\u8cc7\u8a0a\u8655\u3002
L120S09a.cmfwarnpResult3.GridDesc=\u7dad\u8b77\u4e2d
L120S09a.cmfwarnpResult.Msg1=\u4e0b\u5217\u501f\u6b3e\u4eba\u88ab\u5217\u70ba\u300c\u6d17\u9322\u9632\u5236\u6cd5\u7b2c\u5341\u4e94\u689d\u4e4b\u4e8c\u7b2c\u516d\u9805\u5e33\u6236\u5e33\u865f\u66ab\u505c\u9650\u5236\u529f\u80fd\u6216\u9015\u4e88\u95dc\u9589\u7ba1\u7406\u8fa6\u6cd5\u300d\u6240\u7a31\u300c\u7d93\u88c1\u8655\u544a\u8aa1\u8005\u300d\uff0c\u8acb\u65bc\u9ad8\u98a8\u96aa\u6b04\u4f4d\u6216\u7d9c\u5408\u8a55\u4f30\u9801\u7c64\u88dc\u5145\u8aaa\u660e\u6558\u505a\u7406\u7531\u3002
L120S09a.cmfwarnpResult.Msg1OverSea=\u4e0b\u5217\u501f\u6b3e\u4eba\u65bc\u53f0\u7063\u88ab\u5217\u70ba\u300c\u6d17\u9322\u9632\u5236\u6cd5\u7b2c\u5341\u4e94\u689d\u4e4b\u4e8c\u7b2c\u516d\u9805\u5e33\u6236\u5e33\u865f\u66ab\u505c\u9650\u5236\u529f\u80fd\u6216\u9015\u4e88\u95dc\u9589\u7ba1\u7406\u8fa6\u6cd5\u300d\u6240\u7a31\u300c\u7d93\u88c1\u8655\u544a\u8aa1\u8005\u300d\uff0c\u8acb\u6d77\u5916\u5206\u884c\u8a55\u4f30\u6558\u505a\u98a8\u96aa\uff0c\u8996\u500b\u6848\u81ea\u884c\u88dc\u5145\u8aaa\u660e\u3002
L120S09a.cmfwarnpResult.Msg2={0}&nbsp;{1}&nbsp;(\u544a\u8aa1\u8d77\u65e5\uff1a{2})
L120S09a.cmfwarnpResult.Msg3=\u4e0b\u5217\u501f\u6b3e\u4eba\u53d7\u544a\u8aa1\u8655\u5206\u8cc7\u6599\u5eab\u66ab\u505c\u670d\u52d9\uff0c\u8acb\u6d3d\u8cc7\u8a0a\u8655\u3002
L120S09a.cmfwarnpResult.Msg4={0}&nbsp;{1}&nbsp;(\u67e5\u8a62\u65e5\u671f\uff1a{2})
L120S09a.cmfwarnpResult.error=AML\u9801\u7c64\u5c1a\u672a\u57f7\u884c\u544a\u8aa1\u6236\u6383\u63cf


L120S09a.message01=Field\u3010Relationship with the case\u3011can not be empty
L120S09a.message02=Must execute\u3010Import the query list\u3011First\uff01
L120S09a.message03=No data has been selected yet
L120S09a.message04=Are you sure you want to delete the list
L120S09a.message05=Execution will delete the existing data, whether to determine the implementation\uff1f
L120S09a.message06=
L120S09a.message07=\u300c{0}\u300dField can not be empty
L120S09a.message08=\u300c{0}\u300dID or Name already exists
L120S09a.message09=No name information found
L120S09a.message10=\u501f\u6b3e\u4eba\u65bc0024-23\u6d17\u9322\u9632\u5236\u7dad\u8b77\u4f5c\u696d\u5c1a\u672a\u5b8c\u6210\u6cd5\u4eba\u6236\u5be6\u969b\u53d7\u76ca\u4eba\u8eab\u4efd\u78ba\u8a8d\u3002
L120S09a.message11={0} No \u300cEffective Bene\u300d information in customer file
L120S09a.message12=\u501f\u6b3e\u4eba\u65bc0024-23\u6d17\u9322\u9632\u5236\u7dad\u8b77\u4f5c\u696d\u6cd5\u4eba\u6236\u5be6\u969b\u53d7\u76ca\u4eba\u8eab\u4efd\u70ba\u7121\u9808\u78ba\u8a8d\u3002
L120S09a.message13=\u500b\u4eba\u6236\u7121\u9808\u78ba\u8a8d\u5be6\u969b\u53d7\u76ca\u4eba\u8eab\u4efd\u3002
L120S09a.message14=EXCEL format error\u3002
L120S09a.message15=\u300c{0}\u300dfield is incorrect\u3002
L120S09a.message16=No files selected\u3002
L120S09a.message17=File format error(not EXCEL)\u3002
#J-107-0070-001  Web e-Loan \u570b\u5167\u5fb5\u4fe1\u3001\u7c3d\u5831\u3001\u52d5\u5be9AML\u9801\u7c64\u8acb\u5c07\u300c\u9ad8\u968e\u7ba1\u7406\u4eba\u54e1\u300d\u7d0d\u5165\u61c9\u67e5\u8a62\u6bd4\u5c0d\u9ed1\u540d\u55ae\u4e4b\u5c0d\u8c61\u3002
L120S09a.message18={0}\u7121\u501f\u6b3e\u4eba\u57fa\u672c\u8cc7\u6599\u53ef\u4ee5\u5f15\u9032
L120S09a.message19={0}\u7121\u9ad8\u968e\u7ba1\u7406\u4eba\u54e1\u8cc7\u6599\u53ef\u4ee5\u5f15\u9032
L120S09a.message20=\u50b3\u9001\u540d\u55ae\u6383\u63cf\u5f8c\uff0c\u8acb\u57f7\u884c\u300c\u53d6\u5f97\u9ed1\u540d\u55ae\u67e5\u8a62\u7d50\u679c\u300d\uff0c\u82e5\u56de\u50b3\u4e4b\u6848\u4ef6\u8abf\u67e5\u7d50\u679c\u70ba\u6709\u547d\u4e2d\u7591\u4f3c\u540d\u55ae\uff0c\u9808\u5148\u5b8c\u6210\u540d\u55ae\u8abf\u67e5(\u570b\u5167\u5206\u884c\u70baBTT 0015-10\uff0c\u6d77\u5916\u5206\u884c\u70ba\u9632\u5236\u6d17\u9322\u53ca\u6253\u64ca\u8cc7\u6050\u7cfb\u7d71)\u5f8c\uff0c\u518d\u91cd\u65b0\u57f7\u884c\u300c\u53d6\u5f97\u9ed1\u540d\u55ae\u67e5\u8a62\u7d50\u679c\u300d\u5f15\u9032\u8abf\u67e5\u7d50\u679c\u3002<br>\u662f\u5426\u78ba\u5b9a\u57f7\u884c\u672c\u4f5c\u696d?
L120S09a.message21=\u57f7\u884c\u300c\u91cd\u65b0\u5f15\u9032\u67e5\u8a62\u540d\u55ae\u300d\u5f8c\uff0c\u8acb\u5148\u78ba\u8a8d\u540d\u55ae\u5167\u5bb9\u5f8c\uff0c\u518d\u57f7\u884c\u300c\u50b3\u9001\u540d\u55ae\u6383\u63cf\u300d\u3002<br>\u662f\u5426\u78ba\u5b9a\u57f7\u884c\u672c\u4f5c\u696d?
L120S09a.message22=\u5132\u5b58\u5f8c\uff0c\u6240\u6709\u540d\u55ae\u9700\u91cd\u65b0\u57f7\u884c\u300c\u50b3\u9001\u540d\u55ae\u6383\u63cf\u300d\u3002<br>\u662f\u5426\u78ba\u5b9a\u57f7\u884c\u672c\u4f5c\u696d?
#J-107-0176 \u914d\u5408\u4f01\u91d1\u8655\u91dd\u5c0dAML/CFT\u67e5\u8a62\u4f5c\u696d\uff0c\u4fee\u6539\u6388\u4fe1\u7ba1\u7406\u7cfb\u7d71\u4e4bAML/CFT\u67e5\u8a62\u4f5c\u696d\u529f\u80fd
L120S09a.message23=Please select query branch
L120S09a.message24=Please select query user
L120S09a.message25=Overseas branches are not open function yet
#J-108-0039_05097_B1001 Web e-Loan \u570b\u5167\u4f01\u91d1\u6388\u4fe1\u7cfb\u7d71\u7c3d\u5831\u3001\u52d5\u5be9AML\u9801\u7c64\u5c07\u501f\u6236\u4e4b\u300c\u5177\u63a7\u5236\u6b0a\u4eba\u300d\u7d0d\u5165\u61c9\u67e5\u8a62\u6bd4\u5c0d\u9ed1\u540d\u55ae\u4e4b\u5c0d\u8c61\u3002
L120S09a.message26={0}\u7121\u5177\u63a7\u5236\u6b0a\u4eba\u8cc7\u6599\u53ef\u4ee5\u5f15\u9032
#J-108-0145_05097_B1001 Web e-Loan \u570b\u5167\u5916\u4f01\u91d1\u6388\u4fe1\u79c1\u52df\u57fa\u91d1\u6848\u4ef6\u8abf\u6574\u5be6\u8cea\u53d7\u76ca\u4eba\u63a7\u7ba1
L120S09a.message27=This case is to complete the identification of the beneficiary and others later,please consider adding relevant control conditions.<br>\u672c\u6848\u70ba\u5be6\u8cea\u53d7\u76ca\u4eba\u7b49\u5ef6\u5f8c\u8fa6\u7406\u8fa8\u8b58\u6848\u4ef6\uff0c\u8acb\u8861\u914c\u52a0\u8a3b\u76f8\u95dc\u63a7\u7ba1\u689d\u4ef6(\u5982\uff1a\u300c\u9996\u6b21\u52d5\u64a5\u524d\u52d9\u5fc5\u78ba\u5be6\u5b8c\u6210\u5be6\u8cea\u53d7\u76ca\u4eba\u7b49\u8fa8\u8b58\u76f8\u95dc\u4f5c\u696d\u300d)
L120S09a.message28=This case is to complete the identification of the beneficiary and others later,be sure to confirm that it has been completed.<br>\u672c\u6848\u70ba\u5be6\u8cea\u53d7\u76ca\u4eba\u7b49\u5ef6\u5f8c\u8fa6\u7406\u8fa8\u8b58\u6848\u4ef6\uff0c\u52d9\u5fc5\u78ba\u8a8d\u5df2\u78ba\u5be6\u5b8c\u6210\u8fa8\u8b58\u7b49\u76f8\u95dc\u4f5c\u696d
#J-107-0226_11557_B1001 \u7591\u4f3c\u6d17\u9322\u6216\u8cc7\u6050\u4ea4\u6613\u614b\u6a23\u6aa2\u6838\u8868-\u6388\u4fe1
L120S09a.message29=\u662f\u5426\u78ba\u5b9a\u8981\u522a\u9664\u6aa2\u6838\u8868
L120S09a.message30=\u7121\u614b\u6a23\u6aa2\u6838\u8868\u8cc7\u6599\uff0c\u8acb\u65b0\u589e\u5f8c\u518d\u9032\u884c\u5217\u5370

AML.error001=The following list not list on the AML tab <BR>{0}
AML.error002=Blacklist scan result has empty on AML tab.
AML.error003=list on the tab \u300cRegister the related account to the bank's bankcing banking transactions\u300d is empty.
AML.error004=\u300c{0}\u300dBorrower's Profile Tab ->Borrower's Details\uff0cField \u300cEffective Bene\u300d can not be empty.
AML.error005=Field\u300c{0}\u300dcan not be empty.
AML.error006=\u501f\u6b3e\u4eba\u300c{0}\u300d\u65bc{1}\u5c1a\u672a\u5b8c\u6210\u6cd5\u4eba\u6236\u5be6\u969b\u53d7\u76ca\u4eba\u8eab\u4efd\u78ba\u8a8d.
AML.error007=\u501f\u6b3e\u4eba\u300c{0}\u300d\u65bc{1}\u6cd5\u4eba\u6236\u5be6\u969b\u53d7\u76ca\u4eba\u8eab\u4efd\u70ba\u300c\u9700\u78ba\u8a8d\u300d\uff0ce-Loan\u501f\u6b3e\u4eba\u57fa\u672c\u8cc7\u6599\u4e4b\u300c\u5be6\u8cea\u53d7\u76ca\u4eba\u300d\u6b04\u4f4d\u4e0d\u5f97\u70ba\u300c\u7121\u300d.
AML.error008=Borrower/Co-borrower {0}\uff0chas empty field in Related Reports->Borrower's & Guarantor's Profile Sheet.
AML.error009=Contract No. or Borrower {0} not list in Related Reports->Borrower's & Guarantor's Profile Sheet.
AML.error010=Borrower's & Guarantor's Profile\u300c{0}\u300d0024 No\u300cIndustry type(\u884c\u696d\u5c0d\u8c61\u5225)\u300dinformation.
AML.error011=0024 No Borrower's\u300cIndustry type(\u884c\u696d\u5c0d\u8c61\u5225)\u300dinformation.
AML.error012=The Borrower\u300c{0}\u300dhas Effective Bene information in {1}\uff0ce-Loan\u300cEffective Bene\u300dField can not be\u300cNone\u300d.
#J-106-0238-001 \u56e0\u61c9\u65bce-Loan\u6388\u4fe1\u7ba1\u7406\u7cfb\u7d71\u4f01\u3001\u500b\u91d1\u5fb5\u3001\u6388\u4fe1\u696d\u52d9\u9632\u5236\u6d17\u9322\u4f5c\u696d\u9801\u7c64\uff0c\u5c0d\u61c90015\u9ed1\u540d\u55ae\u6aa2\u6838\u547d\u4e2d\u5be9\u67e5\u4e4b\u5f8c\u7e8c\u4f5c\u696d\uff0c\u589e\u52a0\u300c\u9ed1\u540d\u55ae/\u9ed1\u570b\u5bb6/\u653f\u6cbb\u654f\u611f\u4eba\u7269\u4ea4\u6613\u5177\u9ad4\u6aa2\u6838\u6a5f\u5236\u300d
AML.error013=The blacklist has not been scanned yet and will be executed later(\u9ed1\u540d\u55ae\u5c1a\u672a\u958b\u59cb\u6383\u63cf\uff0c\u7a0d\u5f8c\u518d\u57f7\u884c\u672c\u4f5c\u696d)\u3002
AML.error014=Blacklist scanning error, please inform the information office\uff0cError Code:SAS Error LEVEL_1(\u9ed1\u540d\u55ae\u6383\u63cf\u767c\u751f\u932f\u8aa4\u8acb\u901a\u77e5\u8cc7\u8a0a\u8655\uff0c\u932f\u8aa4\u4ee3\u78bc:SAS\u932f\u8aa4LEVEL_1)\u3002
AML.error015=Blacklist scanning error, please inform the information office\uff0cError Code:SAS Error LEVEL_2(\u9ed1\u540d\u55ae\u6383\u63cf\u767c\u751f\u932f\u8aa4\u8acb\u901a\u77e5\u8cc7\u8a0a\u8655\uff0c\u932f\u8aa4\u4ee3\u78bc:SAS\u932f\u8aa4LEVEL_2)\u3002
AML.error016=Blacklist scanning system did not return details, please inform the information office(\u9ed1\u540d\u55ae\u6383\u63cf\u7cfb\u7d71\u672a\u56de\u50b3\u660e\u7d30\uff0c\u8acb\u901a\u77e5\u8cc7\u8a0a\u8655)\u3002
AML.error017=Blacklist scanning system\u300c{0}\u300d No corresponding scan results, please inform the information office(\u9ed1\u540d\u55ae\u6383\u63cf\u7cfb\u7d71\u7121\u5c0d\u61c9\u4e4b\u6383\u63cf\u7d50\u679c\uff0c\u8acb\u901a\u77e5\u8cc7\u8a0a\u8655)\u3002
AML.error018=Prepare to scan the list\u300c{0}\u300dmust have English username(\u6b32\u6383\u63cf\u4e4b\u540d\u55ae\u5fc5\u9808\u8981\u6709\u82f1\u6587\u6236\u540d)\u3002
AML.error019=Please execute the \u300cSend list scan\u300dbutton and try again(\u672c\u6848\u76f8\u95dc\u6383\u63cf\u5c0d\u8c61\u7de8\u865f\u8207\u6383\u63cf\u6279\u865f\u5c1a\u672a\u9001\u9632\u5236\u6d17\u9322\u53ca\u6253\u64ca\u8cc7\u6050\u7cfb\u7d71\u7cfb\u7d71\u6383\u63cf\uff0c\u8acb\u5148\u57f7\u884c\u3010\u50b3\u9001\u540d\u55ae\u6383\u63cf\u3011\u6309\u9215\u5f8c\u518d\u8a66).
#J-107-0059-001 Web e-Loan \u6388\u4fe1\u7c3d\u5831\u66f8\u8207\u52d5\u5be9\u8868\u4e4bAML\u9801\u7c64\u53ca\u5217\u5370\u6aa2\u6838\u8868\u6642\uff0c\u589e\u52a0\u5f15\u9032\u98a8\u96aa\u7b49\u7d1a
AML.error020=The borrower\u300c{0}\u300dIn the AML/CFT tab\uff0cfield \u300cRisk level\u300d on the list must not be blank.
AML.error021=\u672c\u6848\u501f\u6b3e\u4eba\u6aa2\u68380024-23\u5f80\u4f86\u9805\u76ee\u8cc7\u6599\u932f\u8aa4\u5982\u4e0b\uff1a<BR>{0}
AML.error021_002423_NO_0024_23=\u71210024-23\u8cc7\u6599;
AML.error021_002423_03=\u672a\u52fe\u907803-\u9032\u51fa\u53e3\u696d\u52d9;
AML.error021_002423_07=\u672a\u52fe\u907807-\u884d\u751f\u6027\u91d1\u878d\u5546\u54c1\u696d\u52d9;
AML.error021_002423_01OR03=\u672a\u52fe\u907801-\u5b58\u532f\u696d\u52d9\u621603-\u9032\u51fa\u53e3\u696d\u52d9;
AML.error021_002423_02=\u672a\u52fe\u907802-\u6388\u4fe1\u696d\u52d9;
#J-107-0070-001  Web e-Loan \u570b\u5167\u5fb5\u4fe1\u3001\u7c3d\u5831\u3001\u52d5\u5be9AML\u9801\u7c64\u8acb\u5c07\u300c\u9ad8\u968e\u7ba1\u7406\u4eba\u54e1\u300d\u7d0d\u5165\u61c9\u67e5\u8a62\u6bd4\u5c0d\u9ed1\u540d\u55ae\u4e4b\u5c0d\u8c61\u3002
AML.error022=\u300c{0}\u300d\u501f\u6b3e\u4eba\u57fa\u672c\u8cc7\u6599\u300c\u9ad8\u968e\u7ba1\u7406\u4eba\u54e1\u300d\u6b04\u4f4d\u4e0d\u5f97\u7a7a\u767d
AML.error023=\u300c{0}\u300d\u501f\u6b3e\u4eba0024-23\u7121\u300c\u9ad8\u968e\u7ba1\u7406\u4eba\u54e1\u300d\u8cc7\u6599
AML.error024=\u300c{0}\u300d\u501f\u6b3e\u4eba0024-23\u6709\u300c\u9ad8\u968e\u7ba1\u7406\u4eba\u54e1\u300d\u8cc7\u6599\uff0ce-Loan\u501f\u6b3e\u4eba\u57fa\u672c\u8cc7\u6599\u300c\u9ad8\u968e\u7ba1\u7406\u4eba\u54e1\u300d\u4e0d\u5f97\u70ba\u300c\u7121\u300d
#J-107-0248_05097_B1001 Web e-Loan\u4f01\u91d1\u6388\u4fe1\u7ba1\u7406\u7cfb\u7d71AML/CFT\u589e\u52a0\u570b\u5225\u7ba1\u5236\u540d\u55ae\u6383\u63cf\u529f\u80fd
AML.error025=e-Loan system does not have this country\u300c{0}\u300d
AML.error026=List to scan\u300c{0}\u300dmust have\u300c{1}\u300ddata\u3002
#J-108-0039_05097_B1001 Web e-Loan \u570b\u5167\u4f01\u91d1\u6388\u4fe1\u7cfb\u7d71\u7c3d\u5831\u3001\u52d5\u5be9AML\u9801\u7c64\u5c07\u501f\u6236\u4e4b\u300c\u5177\u63a7\u5236\u6b0a\u4eba\u300d\u7d0d\u5165\u61c9\u67e5\u8a62\u6bd4\u5c0d\u9ed1\u540d\u55ae\u4e4b\u5c0d\u8c61\u3002
AML.error027=\u300c{0}\u300d\u501f\u6b3e\u4eba\u57fa\u672c\u8cc7\u6599\u300c\u5177\u63a7\u5236\u6b0a\u4eba\u300d\u6b04\u4f4d\u4e0d\u5f97\u7a7a\u767d
AML.error028=\u300c{0}\u300d\u501f\u6b3e\u4eba0024-23\u7121\u300c\u5177\u63a7\u5236\u6b0a\u4eba\u300d\u8cc7\u6599
AML.error029=\u300c{0}\u300d\u501f\u6b3e\u4eba0024-23\u6709\u300c\u5177\u63a7\u5236\u6b0a\u4eba\u300d\u8cc7\u6599\uff0ce-Loan\u501f\u6b3e\u4eba\u57fa\u672c\u8cc7\u6599\u300c\u5177\u63a7\u5236\u6b0a\u4eba\u300d\u4e0d\u5f97\u70ba\u300c\u7121\u300d
AML.error030=Prepare to scan the list\u300c{0}\u300dName must be in English, numbers or punctuation(\u6b32\u6383\u63cf\u4e4b\u540d\u55ae\u672c\u6848\u6236\u540d\u5fc5\u9808\u70ba\u82f1\u6587\u3001\u6578\u5b57\u6216\u6a19\u9ede\u7b26)\u3002


