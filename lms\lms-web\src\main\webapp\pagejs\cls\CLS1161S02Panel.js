var panelAction = {
    handler: 'cls1161formhandler',
    grid: null,
    build: function(){
        var $div = $('#C160M01BDiv');
        
        panelAction.grid = $div.find("#C160M01BGird").iGrid({
            //localFirst: true,
            handler: 'cls1161gridhandler',
            height: 130,
            action: 'C160M01BQuery',
            rowNum: 15,
            rownumbers: true,
            colModel: [{
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }, {
                name: 'refmainId',
                hidden: true
            }, {
                name: 'dupNo',
                hidden: true
            }, {
                colHeader: i18n.cls1161s02["C160M01B.custId"], // 身分證統編
                align: "left",
                width: 30, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'custId',
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                    panelAction.openDoc(rowObject);
                }
            }, {
                colHeader: i18n.cls1161s02["C160M01B.custName"], // 借款人姓名
                align: "left",
                width: 60, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'custName'
            }, {
                colHeader: i18n.cls1161s02["C160M01B.cntrNo"], // 額度序號
                align: "center",
                width: 40, // 設定寬度
                sortable: true, // 是否允許排序,
                name: 'cntrNo'
            }, {
                colHeader: i18n.cls1161s02["C160M01B.LoanTotAmt"], // 核准額度
                align: "right",
                width: 35, // 設定寬度
                sortable: true, // 是否允許排序
                formatter: GridFormatter.number.addComma,
                name: 'LoanTotAmt'
            }, {
                colHeader: i18n.cls1161s02["C160M01B.caseNo"], // 案號
                align: "left",
                width: 80, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'caseNo'
            }, {
                colHeader: i18n.cls1161s02["C160M01B.dataCheck"], // 是否完成
                align: "center",
                width: 20, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'dataCheck'
            }, {
				colHeader: i18n.cls1161s02["C160M01B.isChange"], // 異動
                align: "center",
                width: 5, // 設定寬度
                sortable: false, //在model中無此欄位，不能排序
                name: 'isChange'
            }],
            ondblClickRow: function(rowid){
                var data = panelAction.grid.getRowData(rowid);
                panelAction.openDoc(data);
            }
        });
        // build button
        // 重新引進
        $div.find("#pullinAgain").click(function(){
            MegaApi.confirmMessage(i18n.def["actoin_001"], function(action){
                if (action) {
                    $.ajax({
                        handler: panelAction.handler,
                        action: 'importC160M01B',
                        data: {},
                        success: function(response){
                            MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["runSuccess"]);
                            panelAction.grid.reload();
                            pageAction.init();
                        }
                    });
                }
            });
        }) // 刪除
.end().find("#delete").click(function(){
            var data = panelAction.grid.getSingleData();
            if (data) {
                MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
                    if (action) {
                        $.ajax({
                            handler: panelAction.handler,
                            action: 'deleteDetial',
                            data: $.extend({
                                formName: 'C160M01BForm'
                            }, data),
                            success: function(response){
                                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["confirmDeleteSuccess"]);
                                panelAction.grid.reload();
                                pageAction.init();
                            }
                        });
                    }
                });
            }
        });
    },
    /**
     * 開啟文件
     */
    openDoc: function(data){
        console.log('panelAction.openDoc 被調用，data:', data);
        console.log('檢查 C160M01BThickBox 是否存在:', $('#C160M01BThickBox').length);
        
        if ($('#C160M01BThickBox').length == 0) {
            console.log('C160M01BThickBox 不存在，開始載入完整的額度明細頁面...');
            //link to CLS1161S02APage.java - 載入包含頁籤的完整介面
        	var param = {'mainId':responseJSON.mainId };
            console.log('載入參數:', param);
            
            $('#C160M01BDetail').load(webroot + '/app/cls/cls1161s02', param, function(response, status, xhr){
                console.log('載入狀態:', status);
                if (status === "error") {
                    console.error('載入失敗:', xhr.status, xhr.statusText);
                    API.showMessage('載入額度明細頁面失敗: ' + xhr.status + ' ' + xhr.statusText);
                    return;
                }
                
                console.log('頁面載入成功，開始初始化...');
                try {
                    // 修復 defaultValue 錯誤：為 pageInit 定義必要的變數
                    if (typeof window.defaultValue === 'undefined') {
                        window.defaultValue = "";
                    }
                    
                    console.log('開始執行 buildItem...');
                    //在 mega.eloan.sample.js 定義，會去抓 codeType 的值
                    $('#C160M01BDetail').buildItem();
                    console.log('buildItem 完成');
                    
                    var auth = (responseJSON ? responseJSON.Auth : {});
                    if (!auth.readOnly) {
                        console.log('執行保險公司查詢...');
                        $.ajax({
                            handler: 'cls1151m01formhandler',
                            action: "queryNoneLifeInsurance",
                            async: false,
                            data: {},
                            success: function(resp){
                                console.log('保險公司查詢成功');
                                $.each($("#insId option"), function(idx, obj){
                                    var key = obj.value;
                                    var value = obj.text;
                                    // 為了處理可以出現[請選擇]
                                    if(key != ""){
                                        if ($.inArray(key, resp.flag_1_list) > -1) {
                                            $("#insId option[value=" + key + "]").attr("disabled", "disabled");
                                        }
                                        else if ($.inArray(key, resp.flag_2_list) > -1) {
                                            // 保留
                                        }
                                        else {
                                            $("#insId option[value=" + key + "]").remove();
                                        }
                                    }
                                });
                            },
                            error: function(xhr, status, error) {
                                console.error('保險公司查詢失敗:', error);
                            }
                        });
                    }
                    
                    console.log('檢查 CLS1161S02Action 是否已定義:', typeof CLS1161S02Action);
                    
                    // 載入 CLS1161S02APage.js 來初始化頁籤和 Grid
                    if (typeof CLS1161S02Action === 'undefined') {
                        console.log('CLS1161S02Action 未定義，開始載入 JavaScript...');
                        console.log('載入腳本: CLS1161S02APage.js');
                        
                        loadScript('pagejs/cls/CLS1161S02APage');
                        
                        // 給腳本足夠時間載入，然後執行
                        setTimeout(function() {
                            console.log('腳本載入完成，檢查 CLS1161S02Action:', typeof CLS1161S02Action);
                            if (typeof CLS1161S02Action !== 'undefined' && typeof CLS1161S02Action.open === 'function') {
                                console.log('調用 CLS1161S02Action.open 開啟完整的額度明細編輯視窗...');
                                CLS1161S02Action.open(data);
                            } else {
                                console.error('CLS1161S02Action 載入失敗');
                                API.showMessage('額度明細功能載入失敗，請重新整理頁面後再試');
                            }
                        }, 500); // 增加延遲時間確保 Grid 初始化完成
                    } else {
                        console.log('CLS1161S02Action 已存在，直接調用 open 方法...');
                        CLS1161S02Action.open(data);
                    }
                } catch (e) {
                    console.error("openDoc error:", e);
                    API.showMessage('初始化過程發生錯誤: ' + e.message);
                }
            });
        }
        else {
            console.log('C160M01BThickBox 已存在，直接開啟...');
            if (typeof CLS1161S02Action !== 'undefined' && typeof CLS1161S02Action.open === 'function') {
                console.log('調用 CLS1161S02Action.open...');
                CLS1161S02Action.open(data);
            } else {
                console.log('CLS1161S02Action 不可用，需要重新載入');
                API.showMessage('額度明細功能未正確載入，請重新整理頁面後再試');
            }
        }
    },
    
    /**
     * 最簡單的開啟編輯視窗方法 - 避開複雜的 JavaScript 載入問題
     */
    openDocSimple: function(data) {
        console.log('openDocSimple 被調用，資料:', data);
        // 直接使用現有的 HTML 結構開啟編輯視窗
        if (data && data.oid) {
            $.ajax({
                handler: panelAction.handler,
                data: {
                    formAction: 'loadDetial',
                    formName: 'C160M01BForm',
                    oid: data.oid
                }
            }).done(function(response) {
                // 載入資料到表單
                if (response.C160M01BForm) {
                    $('#C160M01BForm').setValue(response.C160M01BForm);
                }
                
                // 開啟編輯視窗
                $('#C160M01BThickBox').thickbox({
                    title: '編輯額度明細',
                    width: 850,
                    height: 600,
                    modal: true,
                    buttons: {
                        'sure': function() {
                            if ($('#C160M01BForm').valid()) {
                                // 儲存資料
                                $.ajax({
                                    handler: panelAction.handler,
                                    data: $.extend($('#C160M01BForm').serializeData(), {
                                        formAction: 'saveDetial',
                                        oid: data.oid
                                    })
                                }).done(function() {
                                    $.thickbox.close();
                                    panelAction.grid.reload();
                                });
                            }
                        },
                        'cancel': function() {
                            $.thickbox.close();
                        }
                    }
                });
            }).fail(function() {
                console.error('載入額度明細資料失敗');
            });
        }
    },
    
    /**
     * 備用開啟方法 - 不依賴外部 JavaScript
     */
    directOpenThickbox: function(data) {
        console.log('directOpenThickbox 被調用, data:', data);
        console.log('檢查 C160M01BThickBox 是否存在:', $('#C160M01BThickBox').length);
        
        // 如果 thickbox 容器不存在，顯示簡化的彈出窗口
        if ($('#C160M01BThickBox').length === 0) {
            console.log('C160M01BThickBox 不存在，創建簡化的編輯窗口');
            
            // 創建一個簡單的編輯對話框
            var dialogHtml = '<div id="simpleEditDialog" title="編輯額度明細">' +
                '<p>額度明細資訊：</p>' +
                '<table class="tb2">' +
                '<tr><td>客戶統編：</td><td>' + (data.custId || '') + '</td></tr>' +
                '<tr><td>客戶名稱：</td><td>' + (data.custName || '') + '</td></tr>' +
                '<tr><td>額度序號：</td><td>' + (data.cntrNo || '') + '</td></tr>' +
                '<tr><td>核准額度：</td><td>' + (data.LoanTotAmt || '') + '</td></tr>' +
                '</table>' +
                '<p style="color: red;">注意：額度明細編輯功能正在載入中，請稍後再試或重新整理頁面。</p>' +
                '</div>';
                
            $('body').append(dialogHtml);
            
            $('#simpleEditDialog').dialog({
                width: 500,
                height: 300,
                modal: true,
                buttons: {
                    '關閉': function() {
                        $(this).dialog('close').remove();
                    },
                    '重新載入': function() {
                        $(this).dialog('close').remove();
                        // 強制重新載入額度明細頁面
                        panelAction.forceReloadEditDialog(data);
                    }
                }
            });
            return;
        }
        
        // 直接開啟 thickbox，不依賴外部 JS
        try {
            console.log('開啟 thickbox...');
            $('#C160M01BThickBox').thickbox({
                title: '編輯額度明細 (簡化模式)',
                width: 850,
                height: 600,
                modal: true,
                readOnly: false,
                valign: 'bottom',
                align: 'center',
                open: function() {
                    console.log('thickbox 開啟成功');
                    // 載入資料
                    if (data) {
                        console.log('填入資料到表單...');
                        for (var key in data) {
                            var $field = $('#' + key);
                            if ($field.length > 0) {
                                if ($field.is(':input')) {
                                    $field.val(data[key]);
                                } else {
                                    $field.text(data[key]);
                                }
                            }
                        }
                        console.log('資料填入完成');
                    }
                },
                buttons: {
                    'saveData': function() {
                        console.log('保存按鈕被點擊');
                        // 基本儲存邏輯
                        var formData = $('#C160M01BForm').serializeData();
                        console.log('表單資料:', formData);
                        $.ajax({
                            handler: panelAction.handler,
                            action: 'saveDetial',
                            data: $.extend(formData, {
                                formAction: 'saveDetial',
                                formName: 'C160M01BForm'
                            }),
                            success: function(response) {
                                console.log('保存成功:', response);
                                if (response.success) {
                                    $.thickbox.close();
                                    panelAction.grid.reload();
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('保存失敗:', error);
                                API.showMessage('保存失敗: ' + error);
                            }
                        });
                    },
                    'close': function() {
                        console.log('關閉按鈕被點擊');
                        $.thickbox.close();
                    }
                }
            });
        } catch (e) {
            console.error('開啟 thickbox 失敗:', e);
            API.showMessage('開啟編輯視窗失敗: ' + e.message);
        }
    },
    
    /**
     * 強制重新載入編輯對話框
     */
    forceReloadEditDialog: function(data) {
        console.log('強制重新載入編輯對話框...');
        // 清除現有的內容
        $('#C160M01BDetail').empty();
        
        // 重置載入狀態
        setTimeout(function() {
            panelAction.openDoc(data);
        }, 500);
    },
    promptL039: function(){
    	var _fee_amt = $("#charge #feeAmt").val();
    	if(_fee_amt && _fee_amt>0){
    		API.showMessage("若收取費用，請執行L039各項費用收取交易");    	
    	}
    }
}

$(function(){
    panelAction.build();
});


var wrapFormId = "#formL1140m01r";
var wrapForm = "<form id='formL1140m01r'></form>";

$("#delL140M01RBt").click(function(){
    var selrow = grid.getGridParam('selrow');
    if (selrow) {
        var ret = grid.getRowData(selrow);
        API.flowConfirmAction({
            message: i18n.def["action_003"],
            handler: "cls1161formhandler",
            action: "deleteL140M01R",
            data: {
                deleteMainOid: ret.oid,
                deleteMainId: ret.mainId
            },
            success: function(){
                CommonAPI.showErrorMessage(i18n.def["confirmDeleteSuccess"]);
                grid.trigger("reloadGrid");
            }
        });
    }
    else {
        CommonAPI.showErrorMessage(i18n.def["action_002"]);
    }
});

$("#addL140M01RBt").click(function(){
    $("#fee").thickbox({
        title: i18n.cls1161s02['L140M01R.006'],//'新增費用',
        width: 600,
        height: 400,
        align: 'left',
        valign: 'top',
        modal: false,
        modal: false,
        open: function(){
			$("#formL1140m01r").reset();
            $("#charge").wrap(wrapForm);
            $(wrapFormId).reset();
            $("#feeSwft").val("TWD");
        },
        buttons: API.createJSON([{
            key: i18n.def['sure'],
            value: function(){
                if ($(wrapFormId).valid()) {
                    $.ajax({
                        handler: "cls1161formhandler",
                        data: $.extend($("#charge").serializeData(), {
                            formAction: "addL140M01R"
                        }),
                        success: function(responseData){
                            $("#charge").unwrap();
                            $.thickbox.close();
                            if(true){
                            	panelAction.promptL039();
                            }
                            grid.trigger("reloadGrid");
                            
                        }
                    });
                }
            }
        }, {
            key: i18n.def['cancel'],
            value: function(){
                $("#charge").unwrap();
                $.thickbox.close();
            }
        }])
    });
});

var grid = $("#L140M01RGrid").iGrid({
    height: 90,
    handler: "cls1161gridhandler",
    action: "queryL140M01R",
    rownumbers: true,
    colModel: [{
        width: 40, // 設定寬度
        colHeader: i18n.cls1161s02['L140M01R.016'],//"案號",
        name: "caseNo",
        align: "center",
        formatter: 'click',
        onclick: openDoc
    }, {
        width: 30, // 設定寬度
        colHeader: i18n.cls1161s02['L140M01R.002'],//"費用代碼",
        name: "feeNo",
        align: "center",
        formatter: 'click',
        onclick: openDoc
        //    }, {
        //		width: 30, // 設定寬度
        //        colHeader: i18n.cls1161s02['L140M01R.003'],//"收取範圍",
        //        name: "feeSphere",
        //        align: "center"
    }, {
        width: 20, // 設定寬度
        colHeader: i18n.cls1161s02['L140M01R.015'],//"幣別",
        name: "feeSwft",
        align: "center"
    }, {
        width: 30, // 設定寬度
        colHeader: i18n.cls1161s02['L140M01R.004'],//"費用金額",
        name: "feeAmt",
        align: "right",
        formatter: 'currency',
        formatoptions: {
            decimalSeparator: ",",
            thousandsSeparator: ",",
            decimalPlaces: 0,
            defaultValue: ""
        }
        //    }, {
        //		width: 30, // 設定寬度
        //        colHeader: i18n.cls1161s02['L140M01R.009'],//"客戶編號",
        //        name: "custId",
        //        align: "center"
        //    }, {
        //		width: 30, // 設定寬度
        //        colHeader: i18n.cls1161s02['L140M01R.010'],//"額度序號",
        //        name: "cntrno",
        //        align: "center"
        //    }, {
        //		width: 35, // 設定寬度
        //        colHeader: i18n.cls1161s02['L140M01R.011'],//"帳號",
        //        name: "loanNo",
        //        align: "center"
    }, {
        width: 40, // 設定寬度
        colHeader: i18n.cls1161s02['L140M01R.005'],//"備註",
        name: "feeMemo",
        align: "center"
    }, {
        name: "oid",
        hidden: "true"
    }, {
        name: "mainId",
        hidden: "true"
    }],
    ondblClickRow: function(rowid){
        var data = grid.getRowData(rowid);
        json.editable == "N" ? openDocNoEdit(null, null, data) : openDoc(null, null, data);
    }
});

var gridC120M01A = $("#gridC120M01A").iGrid({
    height: 70,
    width: 80,
    autowidth: true,
    handler: "cls1161gridhandler",
    action: "queryC120M01A",
    rownumbers: true,
    colModel: [{
        colHeader: i18n.cls1161s02['L140M01R.009'],//'客戶編號',
        name: "custId",
        align: "center"
    }, {
        colHeader: i18n.cls1161s02['L140M01R.014'],//'客戶姓名',
        name: "custName",
        align: "center"
    }]

});

var gridL140M01A = $("#gridL140M01A").iGrid({
    height: 150,
    width: 80,
    autowidth: true,
    handler: "cls1161gridhandler",
    action: "queryL140M01A",
    rownumbers: true,
    colModel: [{
        colHeader: i18n.cls1161s02['L140M01R.009'],//'客戶編號',
        name: "custId",
        align: "center"
    }, {
        colHeader: i18n.cls1161s02['L140M01R.014'],//'客戶姓名',
        name: "custName",
        align: "center"
    }, {
        colHeader: i18n.cls1161s02['L140M01R.010'],//'額度序號',
        name: "cntrNo",
        align: "center"
    }]

});

var gridL120M01A = $("#gridL120M01A").iGrid({
    height: 180,
    width: 80,
    autowidth: true,
    handler: "cls1161gridhandler",
    action: "queryL120M01A",
    rownumbers: true,
    colModel: [{
        colHeader: i18n.cls1161s02['L140M01R.016'],//'案號',
        name: "CASENO",
        align: "center"
    }, {
        name: "CASEYEAR",
        hidden: true
    }, {
        name: "CASEBRID",
        hidden: true
    }, {
        name: "CASESEQ",
        hidden: true
    }]

});

function openDoc(cellvalue, options, rowObject){
    $("#fee").thickbox({
        title: i18n.cls1161s02['L140M01R.007'],//'修改費用',
        width: 600,
        height: 400,
        align: 'left',
        valign: 'top',
        open: function(){
            $("#charge").wrap(wrapForm);
            $(wrapFormId).reset();
            $.ajax({
                handler: "cls1161formhandler",
                data: {
                    l140m01roid: rowObject.oid,
                    formAction: "getL140M01R"
                },
                success: function(responseData){
                    $('#charge').injectData(responseData);
                }
            });
        },
        buttons: API.createJSON([{
            key: i18n.def['sure'],
            value: function(){
                if ($(wrapFormId).valid()) {
                    $.ajax({
                        handler: "cls1161formhandler",
                        data: $.extend($("#charge").serializeData(), {
                            formAction: "updateL140M01R"
                        }),
                        success: function(responseData){
                            $("#charge").unwrap();
                            $.thickbox.close();
                            if(true){
                            	panelAction.promptL039();
                            }
                            grid.trigger("reloadGrid");
                        }
                    });
                }
            }
        }, {
            key: i18n.def['cancel'],
            value: function(){
                $("#charge").unwrap();
                $.thickbox.close();
            }
        }])
    });
}


$("input[type='radio'][name='feeSphere']").change(function(){
    var value = $(this).filter(":checked").val();
    $(".clearFee").val("");
    
    $("input[type='text'][class*='group'],button[class*='group'],span[class*='group']").hide();
    $("input[type='text'][class*='group" + DOMPurify.sanitize(value) + "'],button[class*='group" + DOMPurify.sanitize(value) + "'],span[class*='group" + DOMPurify.sanitize(value) + "']").show();
});

$("input[type='radio'][name='feeSphere']:checked").trigger("change");

$("#includeC120M01A").click(function(){

    $('#C120M01AThickBox').thickbox({
        title: i18n.cls1161s02['L140M01R.012'],//'借款人基本資料中借保人',
        width: 400,
        height: 300,
        align: 'center',
        valign: 'bottom',
        i18n: i18n.def,
        buttons: {
            'sure': function(){
                var data = gridC120M01A.getSingleData();
                var data1 = data.custId.substring(0, 11);
                var data2 = data.custId.substring(11, 12);
                $("#custId").val(data1);
                $("#dupNo").val(data2);
                $.thickbox.close();
            },
            'close': function(){
                $.thickbox.close();
            }
        }
    });
});

$("#includeL140M01A").click(function(){

    $('#L140M01AThickBox').thickbox({
        title: i18n.cls1161s02['L140M01R.013'],//'額度明細表中額度序號',
        width: 800,
        height: 400,
        align: 'center',
        valign: 'bottom',
        i18n: i18n.def,
        buttons: {
            'sure': function(){
                var data = gridL140M01A.getSingleData();
                $("#cntrno").val(data.cntrNo);
                $.thickbox.close();
            },
            'close': function(){
                $.thickbox.close();
            }
        }
    });
});

$("#includeL120M01A").click(function(){

    $('#L120M01AThickBox').thickbox({
        title: i18n.cls1161s02['button.pullin03'],//'引進案號',
        width: 450,
        height: 300,
        align: 'center',
        valign: 'bottom',
        i18n: i18n.def,
        buttons: {
            'sure': function(){
                var data = gridL120M01A.getSingleData();
                //				alert(data.CASENO);
                $("#caseNo").val(data.CASENO);
                $("#caseYear").val(data.CASEYEAR);
                $("#caseBrId").val(data.CASEBRID);
                $("#caseSeq").val(data.CASESEQ);
                $.thickbox.close();
            },
            'close': function(){
                $.thickbox.close();
            }
        }
    });
});
