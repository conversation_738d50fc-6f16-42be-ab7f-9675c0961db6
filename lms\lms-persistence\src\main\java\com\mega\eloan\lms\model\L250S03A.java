package com.mega.eloan.lms.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/**
 * 企金模擬動審檢核表第一大類記錄檔
 * 
 * <AUTHOR>
 * 
 */
@Entity
@Table(name = "L250S03A")
@EntityListeners({ DocumentModifyListener.class })
public class L250S03A extends GenericBean implements IDataObject, IDocObject {
	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	private String mainId;

	/**
	 * 大類項目
	 */
	private Integer group;

	/**
	 * 大類順序
	 */
	private Integer groupOrder;

	private String showHead;

	private String yesTitle;

	private String noTitle;

	private String naTitle;

	private String groupTitle;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	/**
	 * 大類項目
	 * 
	 * @return
	 */
	public Integer getGroup() {
		return group;
	}

	/**
	 * 大類項目
	 * 
	 * @param group
	 */
	public void setGroup(Integer group) {
		this.group = group;
	}

	/**
	 * 大類順序
	 * 
	 * @return
	 */
	public Integer getGroupOrder() {
		return groupOrder;
	}

	/**
	 * 大類順序
	 * 
	 * @param groupOrder
	 */
	public void setGroupOrder(Integer groupOrder) {
		this.groupOrder = groupOrder;
	}

	/**
	 * 大類名稱
	 * 
	 * @return
	 */
	public String getGroupTitle() {
		return groupTitle;
	}

	/**
	 * 大類名稱
	 * 
	 * @param groupTitle
	 */
	public void setGroupTitle(String groupTitle) {
		this.groupTitle = groupTitle;
	}

	/**
	 * 是否顯示標題y/n
	 * 
	 * @param showHead
	 */
	public void setShowHead(String showHead) {
		this.showHead = showHead;
	}

	/**
	 * 是否顯示標題y/n
	 * 
	 * @return
	 */
	public String getShowHead() {
		return showHead;
	}

	/**
	 * 顯示名稱
	 * 
	 * @param yesTitle
	 */
	public void setYesTitle(String yesTitle) {
		this.yesTitle = yesTitle;
	}

	/**
	 * 顯示名稱
	 * 
	 * @return
	 */
	public String getYesTitle() {
		return yesTitle;
	}

	/**
	 * 顯示名稱
	 * 
	 * @param noTitle
	 */
	public void setNoTitle(String noTitle) {
		this.noTitle = noTitle;
	}

	/**
	 * 顯示名稱
	 * 
	 * @return
	 */
	public String getNoTitle() {
		return noTitle;
	}

	/**
	 * 顯示名稱
	 * 
	 * @param naTitle
	 */
	public void setNaTitle(String naTitle) {
		this.naTitle = naTitle;
	}

	/**
	 * 顯示名稱
	 * 
	 * @return
	 */
	public String getNaTitle() {
		return naTitle;
	}

}
