/* 
 * Table.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.constants;


/**<pre>
 * table Constants
 * </pre>
 * @since  2011/8/12
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/8/12,fantasy,new
 *          </ul>
 */
public interface Table {
	
	/** Schema **/
	interface Schema {
		String 共用 = "COM";
		String 授信 = "LMS";
		String MIS = "MIS";
		String DW = "DW";
	}

	/** 共用資料庫 **/
    interface COM {
    	
    }
    
    /** Schema共用資料庫 **/
    interface SchemaCOM {
    	
    }
	
	/** 授信資料庫 **/
    interface LMS {
    	String 授信簽報書主檔 = "L120M01A";
    	String 小放會會議紀錄檔 = "L150M01A";
    }
	
    /** Schema授信資料庫 **/
    interface SchemaLMS {
    	String 授信簽報書主檔 = Schema.授信 + "." + LMS.授信簽報書主檔;
    	String 小放會會議紀錄檔 = Schema.授信 + "." + LMS.小放會會議紀錄檔 ;
    }
}
