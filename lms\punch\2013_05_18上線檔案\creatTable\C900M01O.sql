---------------------------------------------------------
-- LMS.C900M01O 歡喜信貸自動派案維護-被指派分行清單檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.C900M01O;
CREATE TABLE LMS.C900M01O (
	OID            CHAR(32) not null,
	ASSIGNEEBRCHID CHAR(3),
	ASSIGNOR<PERSON><PERSON>    CHAR(3),
	CREATOR        CHAR(6),
	CREA<PERSON>TI<PERSON>     TIMESTAMP,
	UPDATER        CHAR(6),
	UPDATETIME     TIMESTAMP,

	constraint P_C900M01O PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS;
---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC900M01O01;
--CREATE UNIQUE INDEX LMS.XC900M01O01 ON LMS.C900M01O   (OID);
---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C900M01O IS '歡喜信貸自動派案維護-被指派分行清單檔';
COMMENT ON LMS.C900M01O (
	OID            IS 'oid',
	ASSIGNEEBRCHID IS '被分派分行代碼',
	ASSIGNORDER    IS '分派順序',
	CREATOR        IS '建立人員代碼',
	CREATETIME     IS '建立日期',
	UPDATER        IS '異動人員代碼',
	UPDATETIME     IS '異動日期'
);