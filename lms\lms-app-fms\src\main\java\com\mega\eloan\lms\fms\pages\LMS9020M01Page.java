/* 
 *LMS9020M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.model.L918M01A;

/**<pre>
 * 停權解除維護明細
 * </pre>
 * @since  2013/1/22
 * <AUTHOR>
 * @version <ul>
 *           <li>2013/1/22,<PERSON>,new
 *          </ul>
 */
@Controller@RequestMapping(path = "/fms/lms9020m01/{page}")
public class LMS9020M01Page extends AbstractEloanForm {

	public LMS9020M01Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {		
		renderJsI18N(LMS9020M01Page.class);
		renderRespMsgJsI18N(UtilConstants.AJAX_RSP_MSG.ERR_MSG); // 多render一個msgi18n
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L918M01A.class;
	}
}
