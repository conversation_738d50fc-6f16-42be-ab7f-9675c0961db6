/* 
 * FSS1010GServiceImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.lms.base.constants.CESConstant;
import com.mega.eloan.lms.dao.F101M01ADao;
import com.mega.eloan.lms.dao.F101S01ADao;
import com.mega.eloan.lms.dao.F101S01BDao;
import com.mega.eloan.lms.enums.FssQuickFlagEnum;
import com.mega.eloan.lms.enums.FssTabEnum;
import com.mega.eloan.lms.lms.service.FSS1010GService;
import com.mega.eloan.lms.model.F101M01A;
import com.mega.eloan.lms.model.F101S01A;
import com.mega.eloan.lms.model.F101S01B;
import com.mega.sso.context.MegaSSOSecurityContext;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.iisi.cap.utils.CapEntityUtil;

/**
 * <pre>
 * 財報提供的介面實作。
 * </pre>
 * 
 * @since 2011/8/24
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/8/24,Sunkist Wang,new
 *          <li>2011/8/25,Sunkist Wang,update for findFssTab()
 *          <li>2011/10/3, CP, 把 FSS1010Service 與 F101S01D 有關的兩個 method
 *          [findFssPreDoc, findF101S01DByMainIdAndFieldId]搬到 GService 使用。</li>
 *          <li>2011/12/12,Sunkist Wang,update for insert fintbl columns.</li>
 *          </ul>
 */
@Service
public class FSS1010GServiceImpl extends AbstractCapService implements
		FSS1010GService {

	protected final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

	@Resource
	private F101M01ADao f101m01aDao;
	@Resource
	private F101S01BDao f101s01bDao;
	@Resource
	private F101S01ADao f101s01aDao;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.fss.service.FSS1010GService#findFssRatio(java.lang
	 * .String, java.lang.String[])
	 */
	public Map<String, BigDecimal> findFssRatio(String mainId, String[] ratioNo) {
		List<F101S01B> list = f101s01bDao.findByMainIdAndRatioNo(mainId,
				ratioNo);
		Map<String, BigDecimal> map = new HashMap<String, BigDecimal>();
		for (F101S01B fssRatio : list) {
			BigDecimal ratio = null;
			if (fssRatio.getRatio() != null) {
				ratio = fssRatio.getRatio();
			}
			map.put(CapString.trimNull(fssRatio.getRatioNo()), ratio);
		}
		return map;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.fss.service.FSS1010GService#findFssRatio(java.lang
	 * .String)
	 */
	public Map<String, BigDecimal> findFssRatio(String mainId) {
		return findFssRatio(mainId, null);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.fss.service.FSS1010GService#findFssTab(java.lang.String
	 * )
	 */
	public Map<String, F101S01A> findFssTab(String mainId) {
		return findFssTab(mainId, null, null);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.fss.service.FSS1010GService#findFssTab(java.lang.String
	 * , java.lang.String)
	 */
	public Map<String, F101S01A> findFssTab(String mainId, String tab) {
		return findFssTab(mainId, tab, null);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.fss.service.FSS1010GService#findFssTab(java.lang.String
	 * , java.lang.String[])
	 */
	public Map<String, F101S01A> findFssTab(String mainId, String[] subNos) {
		return findFssTab(mainId, null, subNos);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.fss.service.FSS1010GService#findFssTab(java.lang.String
	 * , java.lang.String, java.lang.String[])
	 */
	public Map<String, F101S01A> findFssTab(String mainId, String tab,
			String[] subNos) {
		List<F101S01A> list = f101s01aDao.findF101S01AByMetaAndSubNo(mainId,
				tab, subNos);

		Map<String, F101S01A> map = new HashMap<String, F101S01A>();
		for (F101S01A fssTab : list) {
			F101S01A newFssTab = new F101S01A();
			try {
				newFssTab = (F101S01A) CapBeanUtil.copyBean(fssTab, newFssTab,
						CapEntityUtil.getColumnName(fssTab));
			} catch (CapException e) {
				LOGGER.error(e.getMessage());
			}
			map.put(CapString.trimNull(newFssTab.getSubNo()), newFssTab);
		}
		return map;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.fss.service.FSS1010GService#findFssMeta(java.lang.
	 * String)
	 */
	public F101M01A findFssMeta(String mainId) {
		return f101m01aDao.getF101M01AbyMainId(mainId);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.fss.service.FSS1010GService#findFssPages(tw.com.iisi
	 * .cap.dao.utils.ISearch)
	 */
	public Page<F101M01A> findFssPages(ISearch search) {
		boolean hasAuthUnit = false, hasDocStatus = false;
		ISearch tSearch = f101m01aDao.createSearchTemplete();
		for (SearchModeParameter p : search.getSearchModeParameters()) {
			if ("f101a01as.authUnit".equals(p.getKey())) {
				hasAuthUnit = true;
			}
			if (CESConstant.DOC_STATUS.equals(p.getKey())) {
				hasDocStatus = true;
			}
		}
		// 由於iSearch為依照前端傳入的條件，若前面交易沒有代入下兩項規則，將預設限制交易可取得的財報。
		if (!hasAuthUnit) {
			tSearch.addSearchModeParameters(SearchMode.EQUALS,
					"f101a01as.authUnit", MegaSSOSecurityContext.getUnitNo());
		}
		if (!hasDocStatus) {
//			tSearch.addSearchModeParameters(SearchMode.EQUALS,
//					CESConstant.DOC_STATUS, CESDocStatusEnum.已確認.getCode());
		}
		tSearch.addSearchModeParameters(search);
		return f101m01aDao.findPage(tSearch);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.fss.service.FSS1010Service#findSpeedAssets(java.lang
	 * .String)
	 */
	public BigDecimal findSpeedAssets(String mainId) {
		ISearch search = f101s01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "tab",
				FssTabEnum.BS.getCode());
		search.addSearchModeParameters(SearchMode.EQUALS, "quickflag",
				FssQuickFlagEnum.YES.getCode());
		List<F101S01A> speedAssets = f101s01aDao.find(search);
		BigDecimal speedAssetAmt = CESConstant.B0;
		for (F101S01A f101s01a : speedAssets) {
			if (f101s01a.getAmt() != null) {
				speedAssetAmt = speedAssetAmt.add(f101s01a.getAmt());
			}
		}
		return speedAssetAmt;
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
    public Map findFss(String mainId, String fssCols[][])
    	throws CapException
	{
	    Map map = new HashMap();
	    map.put("mainId", mainId);
	    if(fssCols.length > 0 && fssCols[0] != null)
	    {
	        F101M01A meta = findFssMeta(mainId);
	        map.putAll(CapBeanUtil.bean2Map(meta, fssCols[0]));
	    }
	    if(fssCols.length > 1 && fssCols[1] != null)
	    {
	        Map fss = findFssTab(mainId);
	        String arr$[] = fssCols[1];
	        int len$ = arr$.length;
	        for(int i$ = 0; i$ < len$; i$++)
	        {
	            String fa = arr$[i$];
	            F101S01A s01a = (F101S01A)fss.get(fa);
	            if(s01a != null)
	            {
	                map.put((new StringBuilder()).append("a").append(fa).toString(), s01a.getAmt() == null ? "0" : ((Object) (s01a.getAmt().toString())));
	                map.put((new StringBuilder()).append("aName").append(fa).toString(), s01a.getSubName());
	                map.put((new StringBuilder()).append("aRatio").append(fa).toString(), s01a.getRatio() == null ? "0" : ((Object) (s01a.getRatio().toString())));
	            } else
	            {
	                map.put((new StringBuilder()).append("a").append(fa).toString(), "N.A.");
	            }
	        }
	
	    }
	    if(fssCols.length > 2 && fssCols[2] != null)
	    {
	        Map ratios = findFssRatio(mainId);
	        String arr$[] = fssCols[2];
	        int len$ = arr$.length;
	        for(int i$ = 0; i$ < len$; i$++)
	        {
	            String fr = arr$[i$];
	            BigDecimal ra = (BigDecimal)ratios.get(fr);
	            map.put((new StringBuilder()).append("r").append(fr).toString(), ra == null ? "N.A." : ((Object) (ra.toString())));
	        }
	
	    }
	    return map;
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
    public List findFss(String mainId[], String fssCols[][])
    	throws CapException
	{
	    List fssDatas = new ArrayList();
	    String arr$[] = mainId;
	    int len$ = arr$.length;
	    for(int i$ = 0; i$ < len$; i$++)
	    {
	        String fssMID = arr$[i$];
	        Map map = findFss(fssMID, fssCols);
	        fssDatas.add(map);
	    }
	
	    return fssDatas;
	}
	
	/**
	 * this for trim null.
	 * 
	 * @param map
	 *            Map<String, F101S01A> fssTabMap
	 * @param key
	 *            String
	 * @return BigDecimal
	 */
	private BigDecimal getVal(Map<String, F101S01A> map, String key) {
		F101S01A fssTab = map.get(key);
		if (fssTab == null) {
			return null;
		}
		return fssTab.getAmt();
	}

	/**
	 * if null get defaultValue.
	 * 
	 * @param map
	 *            Map<String, F101S01A> fssTabMap
	 * @param key
	 *            String
	 * @param defaultValue
	 *            String
	 * @return BigDecimal
	 */
	@SuppressWarnings("unused")
	private BigDecimal getDefaultVal(Map<String, F101S01A> map, String key,
			BigDecimal defaultValue) {
		BigDecimal val = getVal(map, key);
		if (val == null) {
			return defaultValue;
		}
		return val;
	}

	/**
	 * Get preDoc value, if null get the value zero.
	 * 
	 * @param map
	 *            Map<String, Object> fssPreDocMap
	 * @param key
	 *            String
	 * @return BigDecimal
	 */
	@SuppressWarnings("unused")
	private BigDecimal getPreDocVal(Map<String, Object> map, String key) {
		BigDecimal val = CapMath.getBigDecimal((String) CapString.trimNull(map
				.get(key)));
		if (val == null) {
			return CESConstant.B0;
		}
		return val;
	}

	@Override
	public void save(GenericBean... entity) {	
	}

	@Override
	public void delete(GenericBean... entity) {		
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		return null;
	}

	@Override
	public void save(F101M01A entity, boolean tempSave) {
		
		
	}

	@Override
	public void saveDocument(F101M01A entity, boolean isNewDoc) {
		
		
	}

	@Override
	public void saveAndSend(F101M01A entity, boolean isNewDoc) {
		
		
	}

	@Override
	public void deleteF101M01A(String oid) {
		
		
	}

	@Override
	public F101M01A getF101M01(String oid) {
		
		return null;
	}

	@Override
	public boolean hasTimeFssBas(String custId) {
		
		return false;
	}

	@Override
	public boolean isDuplicate(String custId, String dupNo, String conso,
			String publicFlag, String year, String periodType, String mainId,
			String docstatus, String type) {
		
		return false;
	}

	@Override
	public List<F101S01A> findTabByMetaAndSubNo(String mainId, String tab,
			String[] subNos) {
		
		return null;
	}

	@Override
	public List<F101S01A> findTabByMetaAndSubNo(String mainId, String tab) {
		
		return null;
	}

	@Override
	public List<F101S01A> findTabByMetaAndSubNo(String mainId, String[] subNos) {
		
		return null;
	}

	@Override
	public List<F101S01A> findTabByMetaAndSubNo(String mainId) {
		
		return null;
	}

	@Override
	public List<F101M01A> findPreDocs(String branch, String custId,
			String dupNo, String docStatusCode, String periodType,
			String fssTypeCode, String conso, String year,
			boolean mustOnlyPeriodType) {
		
		return null;
	}

	@Override
	public Page<F101M01A> findPreDocsPages(String branch, String custId,
			String dupNo, String docStatusCode, String periodType,
			String fssTypeCode, String conso, String year,
			boolean mustOnlyPeriodType) {
		
		return null;
	}

	@Override
	public void createPreFss(F101M01A meta, F101M01A preDoc)
			throws CapException {
		
		
	}

	@Override
	public F101M01A getF101M01byMainId(String cMainId) {
		
		return null;
	}

	@Override
	public int getMonthsByTypCd(String periodType, Date s, Date e)
			throws CapException {
		
		return 0;
	}

	@Override
	public void flowControl(String flowName, String mainOid, String action) {
		
		
	}

	@Override
	public void saveHistory(F101M01A meta) {
		
		
	}

	@Override
	public Map<String, String> copyDocuments(String[] mainIds) {
		
		return null;
	}

	@Override
	public boolean isAccTitleDuplicate(String mainOid, String accCode) {
		
		return false;
	}

	@Override
	public void deleteTimeInfo(String timeInfoOid) {
		
		
	}

	@Override
	public List<F101M01A> findFssMetaList(String[] mainIds) {
		ISearch search = this.f101m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "mainId", mainIds);
		search.addOrderBy("eDate", false);
		return this.f101m01aDao.find(search);
	}

	@Override
	public void deleteDocFiles(String[] oids) {
		
		
	}

	@Override
	public void saveTimeFssBatchLog(String fileKey) {
		
		
	}
	
	@Override
	public List<String> getSortMainId(String[] mainIds) {
		return f101m01aDao.getSortMainId(mainIds);
	}	
}
