/* 
 * L784M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L784M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L784M01A;

/** 企金授信管理報表檔 **/
@Repository
public class L784M01ADaoImpl extends LMSJpaDao<L784M01A, String> implements
		L784M01ADao {

	@Override
	public L784M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L784M01A findByMainIdUn(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L784M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L784M01A> list = createQuery(L784M01A.class, search)
				.getResultList();
		return list;
	}
	@Override
	public List<L784M01A> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L784M01A> list = createQuery(L784M01A.class,search).getResultList();
		return list;
	}
	@Override
	public L784M01A findByBrNoAndRptTypeAndBaseDate(String brNo, int i,
			String baseDate) {

		ISearch search = createSearchTemplete();

		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", brNo);
		// 報表類型
		search.addSearchModeParameters(SearchMode.EQUALS, "rptType",
				String.valueOf(i));
		// 資料產生日期
		search.addSearchModeParameters(SearchMode.EQUALS, "dataDate", baseDate);

		return findUniqueOrNone(search);
	}

	@Override
	public List<L784M01A> findByIndex01(Date dataDate, String branchId) {
		return null;
	}

}