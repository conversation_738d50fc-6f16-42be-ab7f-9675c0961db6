package com.mega.eloan.lms.cls.service.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.lowagie.text.Document;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfImportedPage;
import com.lowagie.text.pdf.PdfReader;
import com.lowagie.text.pdf.PdfWriter;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.DocLog;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.ContractDocUtil;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ContractDocService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.cls.pages.CLS3401V01Page;
import com.mega.eloan.lms.cls.service.CLS3401Service;
import com.mega.eloan.lms.dao.C340M01ADao;
import com.mega.eloan.lms.dao.C340M01BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140S02HDao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisMislnratService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01E;
import com.mega.eloan.lms.model.C160S01D;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.C340M01B;
import com.mega.eloan.lms.model.C340M01C;
import com.mega.eloan.lms.model.C900M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140M01R;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02C;
import com.mega.eloan.lms.model.L140S02D;
import com.mega.eloan.lms.model.L140S02E;
import com.mega.eloan.lms.model.L140S02F;
import com.mega.eloan.lms.model.L140S02H;
import com.mega.eloan.lms.ods.service.OdsdbBASEService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.dao.utils.SearchParameterUtil;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapCommonUtil;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

@Service
public class CLS3401ServiceImpl extends AbstractCapService implements CLS3401Service {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS3401ServiceImpl.class);
	
	@Resource
	C340M01ADao c340m01aDao;

    @Resource
    L140S02HDao l140s02hDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	FlowService flowService;

	@Resource
	DocFileService docFileService;
	
	@Resource
	DocLogService docLogService;

	@Resource
	TempDataService tempDataService;

	@Resource
	CLSService clsService;

	@Resource
	MisMislnratService misMislnratService;

	@Resource
	DwdbBASEService dwdbBASEService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	ContractDocService contractDocService;

	@Resource
	NumberService numberService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;
	
	@Resource
	C340M01BDao c340m01bDao;
	
	@Resource
	LMSService lmsService;

	@Resource
	OdsdbBASEService odsdbBASEService;

	private static final String CB_ON_STR = "■";
	private static final String CB_Y_STR = "Y";
	private static final String CB_N_STR = "N";
	private static final String VAL_OTHER = "Other";
	private static final String VAL_MONTH_INTEREST = "MonthInterest";
	private static final String VAL_OVERDRAW = "OverDraw";

	@Override
	public void inValidOnlineCtr(C340M01A c340m01a) throws CapMessageException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String ploanCtrStatus = Util.trim(c340m01a.getPloanCtrStatus());
		Boolean haveLnf020 = misdbBASEService.chkFilterData1(c340m01a.getCustId(), c340m01a.getDupNo(), c340m01a.getContrNumber());
		if ("2".equals(ploanCtrStatus)) {
			throw new CapMessageException("本案件對保程序已作廢，不可重複執行", this.getClass());
		} else if ("9".equals(ploanCtrStatus)) {
			if(clsService.is_function_on_codetype("c340_dc_replyCase")){
				if(haveLnf020){
					if (clsService.is_function_on_codetype("c340_chkTypeA_haveLNF020")) {
						throw new CapMessageException("本案件已建立額度檔，無法作廢，請先註銷主機額度檔才允許作廢。", this.getClass());
					}
				}
			}else{
				throw new CapMessageException("本案件對保程序已完成作業，無法作廢", this.getClass());
			}
		} else if(haveLnf020){
			if (clsService.is_function_on_codetype("c340_chkTypeA_haveLNF020")) {
				throw new CapMessageException("本案件已建立額度檔，無法作廢，請先註銷主機額度檔才允許作廢。", this.getClass());
			}
		}
		
		String ctrType = c340m01a.getCtrType();
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_A)){
			JSONObject rtnJSON = contractDocService.ploan_discardContract(c340m01a);
			if(!Util.equals(rtnJSON.optString("stat"), "ok")){
				throw new FlowMessageException("送「線上貸款平台」失敗，請洽資訊處!");
			}	
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_B)){
			JSONObject rtnJSON = contractDocService.ploan_discardContract(c340m01a);
			if(!Util.equals(rtnJSON.optString("stat"), "ok")){
				throw new FlowMessageException("送「線上貸款平台」失敗，請洽資訊處!");
			}
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_S)){
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_L)){
		}		
		//~~~~~~~~~~~
		c340m01a.setPloanCtrStatus("2");
		c340m01a.setPloanCtrDcTime(CapDate.getCurrentTimestamp());
		c340m01a.setPloanCtrDcUser(user.getUserId());

		//信貸才需要報送聯徵
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_A)){
			//J-111-0524 作廢需一併作廢聯徵報送
			if (clsService.is_function_on_codetype("J-111-0524_sendJCIC2ELF632")) {
				//lnf020沒資料才可作廢聯徵
				if (!haveLnf020) {
					Map<String, Object>  lastestELF632 = misdbBASEService
							.getELF632LastestByCntrNo(c340m01a.getContrNumber());
					if (Util.isNotEmpty(lastestELF632)) {
						//判斷聯徵報送最新一筆ELF632沒作廢過，則需要申請作廢
						//指定ELF632_FACT_AMT_A=0，聯徵報送那邊會自動作廢
						if (Util.parseBigDecimal(lastestELF632.get("ELF632_FACT_AMT_A")).compareTo(new BigDecimal(0))!=0) {
							Timestamp ELF632_TXN_DATE = new Timestamp(System.currentTimeMillis()); // 異動日期
							String ELF632_CONTRACT = c340m01a.getContrNumber();// 額度序號
							String ELF632_BANK_CODE = "017";// 銀行代碼
							String ELF632_LN_BR_NO = c340m01a.getOwnBrId();// 分行別
							String ELF632_FACT_TYPE = Util.trim(lastestELF632.get("ELF632_FACT_TYPE"));;// 額度控管種類
							String ELF632_CUST_ID_B = Util.trim(lastestELF632.get("ELF632_CUST_ID_A")); // 原ID
							String ELF632_SWFT_B = Util.trim(lastestELF632.get("ELF632_SWFT_A")); // 原幣別
							String ELF632_FACT_AMT_B = Util.trim(lastestELF632.get("ELF632_FACT_AMT_A")); // 原核准額度
							String ELF632_DBR22_AMT_B = Util.trim(lastestELF632.get("ELF632_DBR22_AMT_A")); // 原DBR22無擔保額度
							String ELF632_CUST_ID_A = c340m01a.getCustId() + c340m01a.getDupNo();
							String ELF632_SWFT_A = "TWD";
							String ELF632_FACT_AMT_A = "0";
							String ELF632_DBR22_AMT_A = "0";
							String ELF632_WRITE_SYS = "E"; // A:ALOAN系統、E:ELOAN系統

							misdbBASEService.addElf632(ELF632_TXN_DATE,ELF632_CONTRACT,ELF632_BANK_CODE,ELF632_LN_BR_NO,ELF632_FACT_TYPE
									,ELF632_CUST_ID_B,ELF632_SWFT_B,ELF632_FACT_AMT_B,ELF632_DBR22_AMT_B
									,ELF632_CUST_ID_A,ELF632_SWFT_A,ELF632_FACT_AMT_A,ELF632_DBR22_AMT_A,ELF632_WRITE_SYS,"H","X");
						}
					}
					c340m01a.setHaveLnf020(UtilConstants.DEFAULT.否);
				}
				else{
					c340m01a.setHaveLnf020(UtilConstants.DEFAULT.是);
				}
			}
		}

		c340m01aDao.save(c340m01a);
	}


	@Override
	public void saveMeta(C340M01A meta) throws CapMessageException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		boolean newCase = meta.getOid() == null;

		c340m01aDao.save(meta);

		// 起 flag 或記 log
		if (newCase) {
			flowService.start("CLS3401Flow", meta.getOid(), user.getUserId(), user.getUnitNo());
		} else {
			docLogService.record(meta.getOid(), DocLogEnum.SAVE);
		}

		if ("01O".equals(meta.getDocStatus())) {
			meta.setUpdater(user.getUserId());
			meta.setUpdateTime(CapDate.getCurrentTimestamp());
		}

		// 刪除暫存機制備份
		tempDataService.deleteByMainId(meta.getMainId());
	}

	@Override
	public void saveTemporaryMeta(C340M01A meta) {
		c340m01aDao.save(meta);
	}


	@Override
	public List<C340M01A> findFlowingC340M01A(String cntrNo, String ctrType) {
		ISearch search = c340m01aDao.createSearchTemplete();

		search.addSearchModeParameters(SearchMode.EQUALS, "ctrType", ctrType);

		SearchModeParameter and1 = SearchParameterUtil.getMultiAndParameters(
				new SearchModeParameter(SearchMode.EQUALS, "c340m01bs.cntrNo", cntrNo),
				new SearchModeParameter(SearchMode.EQUALS, "docStatus", CreditDocStatusEnum.海外_已核准.getCode()),
				//PLOAN契約約態{1:已送至PLOAN啟動對保, 2:已作廢, 9:已對保完成}
				new SearchModeParameter(SearchMode.EQUALS, "ploanCtrStatus", "1"),
				new SearchModeParameter(SearchMode.GREATER_THAN, "ploanCtrExprDate",
						CapDate.getCurrentDate("yyyy-MM-dd")),
				new SearchModeParameter(SearchMode.IS_NULL, "deletedTime", null));

		SearchModeParameter and2 = SearchParameterUtil.getMultiAndParameters(
				new SearchModeParameter(SearchMode.EQUALS, "c340m01bs.cntrNo", cntrNo),
				new SearchModeParameter(SearchMode.EQUALS, "docStatus", CreditDocStatusEnum.海外_待覆核.getCode()),
				new SearchModeParameter(SearchMode.IS_NULL, "deletedTime", null));


		search.addSearchModeParameters(SearchMode.OR, and1, and2);
		return c340m01aDao.find(search);

	}

	@Override
	public List<C340M01A> findFlowingC340M01AComplete(String cntrNo, String ctrType) {
		ISearch search = c340m01aDao.createSearchTemplete();

		search.addSearchModeParameters(SearchMode.EQUALS, "ctrType", ctrType);

		SearchModeParameter and1 = SearchParameterUtil.getMultiAndParameters(
				new SearchModeParameter(SearchMode.EQUALS, "c340m01bs.cntrNo", cntrNo),
				new SearchModeParameter(SearchMode.EQUALS, "docStatus", CreditDocStatusEnum.海外_已核准.getCode()),
				//PLOAN契約約態{1:已送至PLOAN啟動對保, 2:已作廢, 9:已對保完成}
				new SearchModeParameter(SearchMode.EQUALS, "ploanCtrStatus", "9"),
				new SearchModeParameter(SearchMode.GREATER_THAN, "ploanCtrExprDate",
						CapDate.getCurrentDate("yyyy-MM-dd")),
				new SearchModeParameter(SearchMode.IS_NULL, "deletedTime", null));

		SearchModeParameter and2 = SearchParameterUtil.getMultiAndParameters(
				new SearchModeParameter(SearchMode.EQUALS, "c340m01bs.cntrNo", cntrNo),
				new SearchModeParameter(SearchMode.EQUALS, "docStatus", CreditDocStatusEnum.海外_待覆核.getCode()),
				new SearchModeParameter(SearchMode.IS_NULL, "deletedTime", null));


		search.addSearchModeParameters(SearchMode.OR, and1, and2);
		return c340m01aDao.find(search);

	}

	/* private String get_loanPurpose(L120M01A l120m01a){
		String[] purposes = Util.trim(l120m01a.getPurpose()).split(
				UtilConstants.Mark.SPILT_MARK);
		// 資金用途
		ArrayList<String> r = new ArrayList<String>();

		Map<String, String> cls1141_purpose = clsService.get_codeTypeWithOrder("cls1141_purpose");

		for (String purpose : purposes) {
			if (Util.isEmpty(purpose)) {
				continue;
			}
			// ---
			if (Util.equals(UtilConstants.Casedoc.purpose.其他, purpose)){
				r.add(Util.trim(l120m01a.getPurposeOth()));
			}else{
				r.add(Util.trim(cls1141_purpose.get(purpose)));
			}
		}

		return StringUtils.join(r, UtilConstants.Mark.MARKDAN);
	}*/

	private Map<String, String> build_loanPurpose_map(L120M01A l120m01a){
		String[] purposes = Util.trim(l120m01a.getPurpose()).split(
				UtilConstants.Mark.SPILT_MARK);
		String is_C = "N";
		String is_G = "N";
		String is_H = "N";
		String is_J = "N";
		String is_K = "N";
		String is_F = "N";
		String is_N = "N";
		String is_O = "N";
		String is_P = "N";
		List<String> loanPurpose_otherDesc = new ArrayList<String>();

		Map<String, String> cls1141_purpose = clsService.get_codeTypeWithOrder("cls1141_purpose");

		for (String purpose : purposes) {
			if (Util.isEmpty(purpose)) {
				continue;
			}
			// ---
			if (Util.equals(UtilConstants.Casedoc.purpose.其他, purpose)){
				loanPurpose_otherDesc.add(Util.trim(l120m01a.getPurposeOth()));
			}else{
				/**
				 * J-111-0524 新增新舊契約書purpose對應
				 * 週轉(含行家理財中長期)>>>>個人週轉金
				 * 出國留學>>>>>>>>>>>>>>>>個人週轉金
				 * 繳稅>>>>>>>>>>>>>>>>>>>個人週轉金
				 * 購置自用耐久性消費品>>>>>>個人消費性用途
				 * 子女教育>>>>>>>>>>>>>>>>個人週轉金
				**/
				if (Util.equals(UtilConstants.Casedoc.purpose.週轉_含行家理財中長期, purpose)){
					is_C = "Y";
					is_N = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.投資理財, purpose)){
					is_G = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.購置自用耐久性消費品, purpose)){
					is_H = "Y";
					is_O = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.子女教育, purpose)){
					is_J = "Y";
					is_N = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.繳稅, purpose)){
					is_K = "Y";
					is_N = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.出國留學, purpose)){
					is_F = "Y";
					is_N = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.個人週轉金, purpose)){
					is_N = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.個人消費性用途, purpose)){
					is_O = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.企業員工認購股票, purpose)){
					is_P = "Y";
				}else{
					loanPurpose_otherDesc.add(Util.trim(cls1141_purpose.get(purpose)));	
				}
			}
		}
		Map<String, String> resultMap = new HashMap<String, String>();
		resultMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_C, is_C);
		resultMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_G, is_G);
		resultMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_H, is_H);
		resultMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_J, is_J);
		resultMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_K, is_K);
		resultMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_F, is_F);
		resultMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_N, is_N);
		resultMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_O, is_O);
		resultMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_P, is_P);
		resultMap.put(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_OTHERDESC, StringUtils.join(loanPurpose_otherDesc, UtilConstants.Mark.MARKDAN));
		return resultMap;
	}

	private Map<String, String> build_loanPurpose_map_TypeB(L120M01A l120m01a){
		String[] purposes = Util.trim(l120m01a.getPurpose()).split(
				UtilConstants.Mark.SPILT_MARK);
		String is_A = "N";
		String is_B = "N";
		String is_C = "N";
		String is_D = "N";
		String is_G = "N";
		String is_H = "N";
		String is_J = "N";
		String is_K = "N";
		String is_F = "N";
		String is_N = "N";
		String is_O = "N";
		String is_P = "N";
		List<String> loanPurpose_otherDesc = new ArrayList<String>();

		Map<String, String> cls1141_purpose = clsService.get_codeTypeWithOrder("cls1141_purpose");

		for (String purpose : purposes) {
			if (Util.isEmpty(purpose)) {
				continue;
			}
			// ---
			if (Util.equals(UtilConstants.Casedoc.purpose.其他, purpose)){
				loanPurpose_otherDesc.add(Util.trim(l120m01a.getPurposeOth()));
			}else{
				/**
				 * J-111-0524 新增新舊契約書purpose對應
				 * 週轉(含行家理財中長期)>>>>個人週轉金
				 * 出國留學>>>>>>>>>>>>>>>>個人週轉金
				 * 繳稅>>>>>>>>>>>>>>>>>>>個人週轉金
				 * 購置自用耐久性消費品>>>>>>個人消費性用途
				 * 子女教育>>>>>>>>>>>>>>>>個人週轉金
				 **/
				if (Util.equals(UtilConstants.Casedoc.purpose.購置住宅, purpose)){
					is_A = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.修繕房屋, purpose)){
					is_B = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.週轉_含行家理財中長期, purpose)){
					is_C = "Y";
					is_N = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.購置汽車, purpose)){
					is_D = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.投資理財, purpose)){
					is_G = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.購置自用耐久性消費品, purpose)){
					is_H = "Y";
					is_O = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.子女教育, purpose)){
					is_J = "Y";
					is_N = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.繳稅, purpose)){
					is_K = "Y";
					is_N = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.出國留學, purpose)){
					is_F = "Y";
					is_N = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.個人週轉金, purpose)){
					is_N = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.個人消費性用途, purpose)){
					is_O = "Y";
				}else if (Util.equals(UtilConstants.Casedoc.purpose.企業員工認購股票, purpose)){
					is_P = "Y";
				}else{
					loanPurpose_otherDesc.add(Util.trim(cls1141_purpose.get(purpose)));
				}
			}
		}
		Map<String, String> resultMap = new HashMap<String, String>();
		resultMap.put(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_C, is_C);
		resultMap.put(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_A, is_A);
		resultMap.put(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_B, is_B);
		resultMap.put(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_D, is_D);
		resultMap.put(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_J, is_J);
		resultMap.put(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_H, is_H);
		resultMap.put(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_OTHERDESC, StringUtils.join(loanPurpose_otherDesc, UtilConstants.Mark.MARKDAN));
		return resultMap;
	}
	
	/**
	 * 這裡產出的  ploanCtrNo 重複也無所謂，在契約書呈主管前，再檢核一次就好
	*/
	private String build_ploanCtrNo(String ctrType, String l140m01a_cntrNo){
		List<C340M01A> list = c340m01aDao.findByCtrTypeContrNumber_OrderByCreateTimeDesc(ctrType, l140m01a_cntrNo);
		
		int seq = 1;
		int digitCnt = 3;
		String splitStr = "-";
		
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_S)){
			digitCnt = 2;
			splitStr = "-S";
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_L)){
			digitCnt = 2;
			splitStr = "-L";
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_C)){
			digitCnt = 2;
			splitStr = "-C";
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_A)){
			//
		}
		
		if(list.size()==0){
			//seq still 1
		}else{
			String existMax = list.get(0).getPloanCtrNo();
			if(existMax.length()==l140m01a_cntrNo.length()+splitStr.length()+digitCnt){
				seq = Util.parseInt(Util.getRightStr(existMax, digitCnt))+1;
			}else{
				//seq still 1
			}
		}
		return l140m01a_cntrNo + splitStr+ Util.addZeroWithValue(seq, digitCnt);		
	}
	private String build_ploanCtrNo_ctrTypeC(String l140m01a_cntrNo, String ploanPlan){
		List<C340M01A> list = c340m01aDao.findByCtrTypeContrNumber_OrderByCreateTimeDesc(ContractDocConstants.C340M01A_CtrType.Type_C, l140m01a_cntrNo);
		
		int seq = 1;
		int digitCnt = 2;
		String splitStr = "-"+ploanPlan+"-";
		
		
		if(list.size()==0){
			//seq still 1
		}else{
			String existMax = list.get(0).getPloanCtrNo();
			if(existMax.length()==l140m01a_cntrNo.length()+splitStr.length()+digitCnt){
				seq = Util.parseInt(Util.getRightStr(existMax, digitCnt))+1;
			}else{
				//seq still 1
			}
		}
		return l140m01a_cntrNo + splitStr+ Util.addZeroWithValue(seq, digitCnt);		
	}
	
	private BigDecimal _get_latest_mis_MISLNRAT_currTWD(String lrCode){
		List<Map<String, Object>> rowDatas = misMislnratService.findByCurr("TWD");
		for (Map<String, Object> data : rowDatas) {
			String key = Util.trim(data.get("LR_CODE"));
			BigDecimal rate = new BigDecimal(Util.trim(data.get("LR_RATE")));
			//=====
			if(Util.equals(key, lrCode)){
				return rate;
			}
		}
		return null;
	}
	
	@Override
	public void init_C340RelateCtrTypeA(C340M01A c340m01a, String caseMainId,
			String tabMainIds) throws CapMessageException {

		JSONObject jsParam = new JSONObject();
		L140M01A l140m01a = clsService.findL140M01A_mainId(tabMainIds);
		if (l140m01a != null) {
			L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);
			
			c340m01a.setCustName(l140m01a.getCustName());
			c340m01a.setContrNumber(l140m01a.getCntrNo());
			c340m01a.setContrPartyNm(l140m01a.getCustName());

//			String seq = numberService.getNumberWithMax(C340M01A.class,
//					c340m01a.getOwnBrId(), "", 99999);
//			c340m01a.setPloanCtrNo(l140m01a.getCntrNo() + "-" + Util.addZeroWithValue(seq, 5));
			c340m01a.setPloanCtrNo(build_ploanCtrNo(c340m01a.getCtrType(), l140m01a.getCntrNo()));
			
			if(true){
				jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_COURT_NAME, "");//地方法院預設是空白
			}
			C120S01A mainCust = clsService.findC120S01A(caseMainId, l140m01a.getCustId(), l140m01a.getDupNo());

			if (mainCust != null) {
				String mTel = mainCust.getMTel();
				String email = mainCust.getEmail();
				Date birthday = mainCust.getBirthday();
				jsParam.put("borrowerMobileNumber", mTel);
				jsParam.put("borrowerBirthDate", CapDate.formatDate(birthday, "yyyy-MM-dd"));
				jsParam.put("borrowerEmail", email);
			}


			C340M01B c340m01b = new C340M01B();
			c340m01b.setMainId(c340m01a.getMainId());
			c340m01b.setTabMainId(l140m01a.getMainId());
			c340m01b.setCntrNo(l140m01a.getCntrNo());
			clsService.save(c340m01b);


			C340M01C c340m01c = new C340M01C();
			c340m01c.setMainId(c340m01a.getMainId());
			c340m01c.setItemType(ContractDocConstants.C340M01C_ItemType.TYPE_0);
			c340m01c.setCreator(c340m01a.getCreator());
			c340m01c.setCreateTime(c340m01a.getCreateTime());


			// 借保人檔
			List<L140S01A> l140s01as = clsService.findL140S01A(l140m01a);
			//產品種類
			List<L140S02A> l140s02as = clsService.findL140S02A(l140m01a);
			if(true){ //預設值
				jsParam.put("relatedPersonType", "");
				jsParam.put("relatedPersonTypeDesc", "");
				jsParam.put("relatedPersonName", "");
				jsParam.put("relatedPersonId", "");
			}
			if (l140s01as == null || l140s01as.size() == 0) {
			} else if (l140s01as.size() == 1) {
				// 關係人
				L140S01A l140s01a = l140s01as.get(0);
				String custId = l140s01a.getCustId();
				String dupNo = l140s01a.getDupNo();
				String custPos = l140s01a.getCustPos();
				String custName = l140s01a.getCustName();
				
				if(Util.equals(custPos, UtilConstants.lngeFlag.連帶保證人)){
					//ok
				}else if(Util.equals(custPos, UtilConstants.lngeFlag.ㄧ般保證人)){
					//ok
				}else{
					throw new CapMessageException("借保人身分非「連帶保證人」或「一般保證人」，不適用線上對保", this.getClass());
				}	
				
				C120S01A c120s01a = clsService.findC120S01A(caseMainId, custId, dupNo);
				if (c120s01a != null) {
					String mTel = c120s01a.getMTel();
					String email = c120s01a.getEmail();
					Date birthday = c120s01a.getBirthday();
					jsParam.put("relatedPersonMobileNumber", mTel);
					jsParam.put("relatedPersonBirthDate", CapDate.formatDate(birthday, "yyyy-MM-dd"));
					jsParam.put("relatedPersonEmail", email);
				}
				//L140S01A_custPos
				Map<String, String> custPosDescMap = codeTypeService.findByCodeType("L140S01A_custPos");
				jsParam.put("relatedPersonType", custPos);
				jsParam.put("relatedPersonTypeDesc", custPosDescMap.get(custPos));
				jsParam.put("relatedPersonName", custName);
				jsParam.put("relatedPersonId", custId);
			} else {
				throw new CapMessageException("借保人超過1位，不適用線上對保", this.getClass());
			}

			
			if (l140s02as != null && l140s02as.size() == 1) {
				L140S02A l140s02a = l140s02as.get(0);
				int lnYear = l140s02a.getLnYear() * 12;
				int lnMonth = l140s02a.getLnMonth();

				BigDecimal loanAmt = l140s02a.getLoanAmt() == null ? BigDecimal.ZERO : l140s02a.getLoanAmt();

				jsParam.put("loanAmt", loanAmt.divide(BigDecimal.valueOf(10000), 0, BigDecimal.ROUND_HALF_UP));
				if(Util.equals(c340m01a.getCtrType(), ContractDocConstants.C340M01A_CtrType.Type_L)){
					jsParam.put("loanAmt", LMSUtil.pretty_numStr(loanAmt));
				}
				
				jsParam.put("loanPeriod", String.valueOf(lnYear + lnMonth));				
				//jsParam.put("loanPurpose", get_loanPurpose(l120m01a));
				jsParam.putAll(build_loanPurpose_map(l120m01a));
				jsParam.put("drawDownType", "一次撥付");


				L140S02E l140s02e = clsService.findL140S02E(l140m01a.getMainId(), l140s02a.getSeq());
				if (l140s02e != null) {
					// 還款方式
					String l140s02e_payWay = Util.trim(l140s02e.getPayWay());
					String desc_l140s02e_payWay = "";
					if(Util.equals("1", l140s02e_payWay)){
						desc_l140s02e_payWay = "本息平均攤還";
						if(Util.equals(l120m01a.getSimplifyFlag(), "C")){
							desc_l140s02e_payWay += "(寬限期001期-006期)";
						}
					}else if(Util.equals("2", l140s02e_payWay)){
						throw new CapMessageException("還款方式為「本息平均攤還」，但非月繳，無法執行線上對保", this.getClass());
					}else{
						throw new CapMessageException("還款方式非「本息平均攤還」，無法執行線上對保", this.getClass());
						// Map<String, String> payWayMap = codeTypeService.findByCodeType("L140S02E_payWay");	
						// desc_l140s02e_payWay = payWayMap.get(l140s02e_payWay);
					}
					jsParam.put("repaymentMethod", desc_l140s02e_payWay);
				}

				L140S02F l140s02f = clsService.findL140S02F(l140m01a.getMainId(), l140s02a.getSeq());
				List<L140S02D> l140s02ds = clsService.findL140S02D_orderByPhase(l140m01a.getMainId(), l140s02a.getSeq(), "Y");
				boolean is_only_M3_and_pmRate_0 = ClsUtility.is_only_M3_and_pmRate_0(l140s02ds);
				boolean is_only_MR_and_pmRate_0 = ClsUtility.is_only_MR_and_pmRate_0(l140s02ds);
				
				//利率資訊
				if(is_only_M3_and_pmRate_0 || is_only_MR_and_pmRate_0){
					List<Map<String, Object>> lrRate_list = misMislnratService.findMislnratByLRRate(CrsUtil.RATE_TYPE_M3, "TWD" );
					//判斷MR，要用新利率+PmRate
					if (is_only_MR_and_pmRate_0) {
						lrRate_list = misMislnratService.findMislnratByLRRate(CrsUtil.RATE_TYPE_MR, "TWD" );
					}

					String rateValue = "";
					if(lrRate_list.size() > 0){
						rateValue = LMSUtil.pretty_numStr(CrsUtil.parseBigDecimal(MapUtils.getObject(lrRate_list.get(0), "LR_RATE")).add(l140s02ds.get(0).getPmRate()));
					}

					String bgnNum = "";
					String endNum = "";
					if(l140s02ds.size()>0){
						bgnNum = String.valueOf(l140s02ds.get(0).getBgnNum());
						
						endNum = String.valueOf(l140s02ds.get(l140s02ds.size()-1).getEndNum());
					}
					String rateDesc_M3 = MessageFormat.format("第{0}期至第{1}期。甲方申辦乙方之員工貸款者，按乙方撥款當日公告之消費金融放款指標利率加碼年息百分之{2}計算(目前為{3}%)；嗣後隨乙方消費金融放款指標利率調整而調整，並於每屆滿一個月之日按當日調整後之年利率計算。除甲方因公死亡或退休者外，本貸款自乙方認定之甲方離職日起(甲方離職事由包括但不限於留職停薪、辭職、退職、資遣、死亡等)，改按乙方公告之消費金融放款指標利率加年利率3%浮動計息。乙方消費金融放款指標利率每月調整乙次，調整時本貸款利率隨同調整，並於每屆滿一個月之日按調整後之年利率計息。"
							, bgnNum, endNum,l140s02ds.get(0).getPmRate(), rateValue);
					jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1, "Y");
					jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_RATEDESC_M3, rateDesc_M3);
				}else{
					HashMap<String, LinkedHashMap<String, String>> _map = misMislnratService.findBaseRateByCurrs(
							new String[] { "TWD" });
					Map<String, String> rateDesc_map = _map.get("TWD");
					rateDesc_map.put(CrsUtil.RATE_TYPE_01, "自訂利率");

					if (l140s02ds != null && l140s02ds.size() > 2) {
						throw new CapMessageException("利率以2段為限，超過無法執行線上對保", this.getClass());
					} else {
						for (L140S02D l140s02d : l140s02ds) {
							
							Integer phase = l140s02d.getPhase();
							Integer bgnNum = l140s02d.getBgnNum();
							Integer endNum = l140s02d.getEndNum();
							String rateType = l140s02d.getRateType();
							BigDecimal l140s02d_baseRate = l140s02d.getBaseRate();
							BigDecimal baseRate = l140s02d_baseRate;
							if (CrsUtil.RATE_TYPE_01.equals(rateType) && Util.equals("C01", l140s02d.getRateUserType())) {
								
							}else{
								if(!Util.equals("1", l140s02d.getRateFlag())){ //非固定利率，去中心抓最新的代率值
									baseRate = _get_latest_mis_MISLNRAT_currTWD(rateType);
								}	
							}
							
							String pmFlag = l140s02d.getPmFlag();
							BigDecimal pmRate = l140s02d.getPmRate();
							BigDecimal nowRate = ClsUtility.calc_nowRate(l140s02d.getRateType(), l140s02d.getRateUserType(), l140s02d.getRateFlag(), l140s02d.getNowRate(), baseRate, pmFlag, pmRate);
							BigDecimal rateUser = l140s02d.getRateUser();
							String l140s02dOid = l140s02d.getOid();

							if ("M".equals(pmFlag)) {
								// 和消金處確認過，不會有減碼的狀況產生，因契約文字就是這樣寫
								throw new CapMessageException("利率為減碼，不適用線上對保", this.getClass());
							}

							boolean isRateType01_C01 = false;

							if (CrsUtil.RATE_TYPE_01.equals(rateType) && Util.equals("C01", l140s02d.getRateUserType())) {
								isRateType01_C01 = true;
							} else {

							}

							if (1 == phase) { //送至 線上貸款平台 時，在 ContractDocService :: geCtrTypeArateDesc(...) 組字
								jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1, "Y");
								jsParam.put("rateRange1Oid", l140s02dOid);
								if (isRateType01_C01) {
									jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_TYPE01, "Y");
									if(clsService.is_function_on_codetype("c340_RateType01_C01_periodHide")){
										
									}else{
										jsParam.put("rateRange1Param01", String.valueOf(bgnNum));
										jsParam.put("rateRange1Param02", String.valueOf(endNum));
									}
									jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_FIXED_RATE, LMSUtil.pretty_numStr(rateUser));
								} else {
									jsParam.put("rateRange1Param01", String.valueOf(bgnNum));
									jsParam.put("rateRange1Param02", String.valueOf(endNum));
									jsParam.put("rateRange1Param04", rateDesc_map.get(rateType));
									jsParam.put("rateRange1Param05", LMSUtil.pretty_numStr(baseRate));//將 baseRate 轉成字串，避免在 Map<String, String> rptVariableMap 去 putAll(jsContent); 因 Double, String 不符而出錯
									jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_PLUSRATE, LMSUtil.pretty_numStr(pmRate));
									jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_RESULTRATE , LMSUtil.pretty_numStr(nowRate));
								}


							} else if (2 == phase) {
								jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2, "Y");
								jsParam.put("rateRange2Oid", l140s02dOid);
								if (isRateType01_C01) {
									jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2_TYPE01, "Y");
									if(clsService.is_function_on_codetype("c340_RateType01_C01_periodHide")){
										
									}else{
										jsParam.put("rateRange2Param01", String.valueOf(bgnNum));
										jsParam.put("rateRange2Param02", String.valueOf(endNum));
									}
									jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2_FIXED_RATE, LMSUtil.pretty_numStr(rateUser));
								} else {
									jsParam.put("rateRange2Param01", String.valueOf(bgnNum));
									jsParam.put("rateRange2Param02", String.valueOf(endNum));
									jsParam.put("rateRange2Param04", rateDesc_map.get(rateType));
									jsParam.put("rateRange2Param05", LMSUtil.pretty_numStr(baseRate));//將 baseRate 轉成字串，避免在 Map<String, String> rptVariableMap 去 putAll(jsContent); 因 Double, String 不符而出錯
									jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2_PLUSRATE, LMSUtil.pretty_numStr(pmRate));
									jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2_RESULTRATE , LMSUtil.pretty_numStr(nowRate));
								}
							}
						}
					}
				}

				if(true){
					String lendingPlanInfo_showOption = ""; //{1:無限制清償, 2:限制清償}
					//~~~~
					String advancedRedemptionTitle = "無限制清償期";
					String advancedRedemptionDesc = "甲方得隨時償還借款或結清帳戶，無須支付違約金。";
					String advancedRateTitle = "利率"; //應只有出現在PLOAN系統的UI上
					//~~~~
					String limitedDesc_p1 = "{0}" //"甲方自撥款日起{0}期(含)內，若提前償還部分或全部本金，甲方應支付乙方提前清償違約金，計算方式如下：\n"
											+"甲方自本借款撥款日起算至第{1}期(含)內，提前清償部分或全部本金時，按提前清償本金金額之{2}%計付違約金"; 
					//2021-02-25 之前提供的契約 word 檔，多出了「至」
					//String limitedDesc_p2 = "；自本借款撥款日起算至第{3}期(含)內至第{4}期(含)內，提前清償部分或全部本金時，按提前清償本金金額之{5}%計付違約金。";
					String limitedDesc_p2 = "；自本借款撥款日起算第{3}期(含)內至第{4}期(含)內，提前清償部分或全部本金時，按提前清償本金金額之{5}%計付違約金。";	
					
					String limitedDesc_p3 = "\n(「甲方死亡或重大傷殘並取得證明」或「乙方主動要求還款」，不在此限。)";
					String limitedRedemptionTitle = "限制清償期";
					String limitedRedemptionDesc = ""; //組字
					String limitedRateTitle = "利率"; //應只有出現在PLOAN系統的UI上


					String html_space = "&nbsp;";
					String html_space_3 = StringUtils.repeat(html_space, 3);
					String html_space_6 = StringUtils.repeat(html_space, 3); // 6);
					limitedRedemptionDesc = MessageFormat.format(limitedDesc_p1+limitedDesc_p2+limitedDesc_p3
							, "", html_space_3, html_space_6
							, html_space_3, html_space_3, html_space_6);
					if (l140s02f != null) {
						if (l140s02f.getPConBeg1() != null && l140s02f.getPConEnd1() != null && l140s02f.getPCalCon1() != null) {
							lendingPlanInfo_showOption = "2";
							if(true){
								boolean has2 = false;
								StringBuffer txtPattern = new StringBuffer(limitedDesc_p1);
								if (l140s02f.getPConBeg2() != null && l140s02f.getPConEnd2() != null && l140s02f.getPCalCon2() != null) {
									has2 = true;
								}
								if (has2) {
									txtPattern.append(limitedDesc_p2);
								} else {
									txtPattern.append("。");
								}
								txtPattern.append(limitedDesc_p3);
								
								if (has2) {
									limitedRedemptionDesc = MessageFormat.format(txtPattern.toString(), "", //l140s02f.getPConEnd2(),
											l140s02f.getPConEnd1(), l140s02f.getPCalCon1(), l140s02f.getPConBeg2(),
											l140s02f.getPConEnd2(), l140s02f.getPCalCon2());
								} else {
									limitedRedemptionDesc = MessageFormat.format(txtPattern.toString(), "", //l140s02f.getPConEnd1(),
											l140s02f.getPConEnd1(), l140s02f.getPCalCon1());
								}
							}							
						} else {
							lendingPlanInfo_showOption = "1";
						}
					}else{
						lendingPlanInfo_showOption = "1";
					}
					//~~~~~~~~~~~~~~~~~~~~~
					jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_ADVANCED_REDEMPT_TITLE, advancedRedemptionTitle);
					jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_ADVANCED_REDEMPT_DESC, advancedRedemptionDesc);
					jsParam.put("advancedRateTitle", advancedRateTitle);
					jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_LIMITED_REDEMPT_TITLE, limitedRedemptionTitle);
					jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_RATE_LIMITED_REDEMPT_DESC, limitedRedemptionDesc);
					jsParam.put("limitedRateTitle", limitedRateTitle);
					jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_LENDING_PLAN_OPTION, lendingPlanInfo_showOption);
					String redemptionTitle = "";
					String redemptionDesc = "";
					String rateTitle = "";
					if(Util.equals("1", lendingPlanInfo_showOption)){
						redemptionTitle = advancedRedemptionTitle;
						redemptionDesc = advancedRedemptionDesc;
						rateTitle = advancedRateTitle;
					}else if(Util.equals("2", lendingPlanInfo_showOption)){
						redemptionTitle = limitedRedemptionTitle;
						redemptionDesc = limitedRedemptionDesc;		
						rateTitle = limitedRateTitle;
					}
					jsParam.put("redemptionTitle", redemptionTitle);
					jsParam.put("redemptionDesc", redemptionDesc);
					jsParam.put("rateTitle", rateTitle);
				}
				//=================================
				if(true){
					jsParam.put("otherInfoTitle", "");
					if(is_only_M3_and_pmRate_0|| is_only_MR_and_pmRate_0){
						//已在組字時，將自離職日起加3%的字串同時寫入
					}else{
						boolean has_M3_MD = false; 
						for (L140S02D l140s02d : l140s02ds) {
							String rateType = Util.trim(l140s02d.getRateType());
							if(Util.equals(CrsUtil.RATE_TYPE_M3, rateType) 
									|| Util.equals(CrsUtil.RATE_TYPE_MD, rateType)
									|| Util.equals(CrsUtil.RATE_TYPE_MR, rateType)){
								has_M3_MD = true;
							}
						}
						
						if(has_M3_MD){
							jsParam.put("otherInfoTitle", "行員信貸方案備註");
							
							JSONArray otherInfoDesc_array = new JSONArray();
							otherInfoDesc_array.add("本借款利息，按乙方之員工消費型貸款利率，機動調整。甲方自乙方離職（含留職停薪、辭職、退職、資遣、非因公死亡等）時，自離職日起利率改按乙方消費金融放款指標利率加年利率百分之3計算，嗣後隨乙方消費金融放款指標利率調整而調整，並於每屆滿一個月之日按當日調整後之年利率計算。");
							jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_OTHERINFODESC, otherInfoDesc_array);
						}
					}
				}
			} else {
				throw new CapMessageException("產品種類超過1樣，不適用線上對保", this.getClass());
			}

			//手續費先有預設值
			jsParam.put("preliminaryFee", "0");
			jsParam.put("creditCheckFee", "0");
			List<L140M01R> l140m01r_list = clsService.findL140M01R_exclude_feeSrc3(caseMainId);
			for (L140M01R l140m01r : l140m01r_list) {
				String feeNo = l140m01r.getFeeNo();
				if ("01".equals(feeNo)) {
					// 開辦費
					jsParam.put("preliminaryFee",
							l140m01r.getFeeAmt() == null ? BigDecimal.ZERO : LMSUtil.pretty_numStr(l140m01r.getFeeAmt()));
				} else if ("02".equals(feeNo)) {
					// 信用查詢費
					jsParam.put("creditCheckFee",
							l140m01r.getFeeAmt() == null ? BigDecimal.ZERO : LMSUtil.pretty_numStr(l140m01r.getFeeAmt()));
				}
			}

			Map<String, String> cntrNo_belong_co70647919_c101_map = contractDocService.is_cntrNo_belong_co70647919_c101(l140m01a.getMainId());
			boolean is_cntrNo_belong_co70647919_c101_map = MapUtils.isNotEmpty(cntrNo_belong_co70647919_c101_map);
			if(true){ //存款帳號
				List<Map<String, Object>> accounts = dwdbBASEService.getGuarOnLineAccount(l140m01a.getCustId());
				if (accounts != null && accounts.size() > 0) {
					JSONArray loanAcct = new JSONArray();
					/*
					  例如：66-黃金存摺
					*/
					Map<String, String> exclude_dpCate = clsService.get_codeTypeWithOrder("c340_ctrTypeA_exclude_dpCate");
					for (Map<String, Object> account : accounts) {
						String accoutNo = MapUtils.getString(account, "MR_PB_ACT_NO", "");	
						String taxNo = MapUtils.getString(account, "MR_PB_TAX_NO", "");
						String dpCate = ClsUtility.get_acctNo_APCODE(accoutNo); //存款帳號第4~5碼是存款種類
						if(exclude_dpCate.containsKey(dpCate)){
							continue;
						}
						if(Util.equals("09", dpCate)){
							if(ClsUtility.ctr_acctNo_APCODE_09_forPersoanlLoanContract(accoutNo, taxNo)){
								//可用於線上對保契約的撥款
							}else{
								continue;
							}
						}
						if(is_cntrNo_belong_co70647919_c101_map){
							if(Util.equals(accoutNo, MapUtils.getString(cntrNo_belong_co70647919_c101_map, "appnDpAcct"))){
								//精銳案，撥款的帳號已在申貸進件時指定 => 只讓該帳號出現
							}else{
								continue;
							}
						}else{
							//照原邏輯
						}
						
						loanAcct.add(accoutNo);
					}
					if (loanAcct.size()>0) {
						jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_ACCTNO_LIST, loanAcct);
					}
				}
			}

			if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_L, c340m01a.getCtrType())){
				List<Map<String, Object>> accounts = dwdbBASEService.getAccount_laborContract(l140m01a.getCustId(),l140m01a.getDupNo());
				if (accounts != null && accounts.size() > 0) {
					JSONArray loanAcct = new JSONArray();
					/*
					  例如：66-黃金存摺
					*/
					Map<String, String> exclude_dpCate = clsService.get_codeTypeWithOrder("c340_ctrTypeA_exclude_dpCate");
					for (Map<String, Object> dataRow : accounts) {
						String accoutNo = MapUtils.getString(dataRow, "MR_PB_ACT_NO", "");
						String flag = MapUtils.getString(dataRow, "MR_PB_MSAC_FLAG", "");
						String dpCate = StringUtils.substring(accoutNo, 3, 5); //存款帳號第4~5碼是存款種類
						if(exclude_dpCate.containsKey(dpCate)){
							continue;
						}
						loanAcct.add(accoutNo+"^"+flag);
					}
					jsParam.put(ContractDocConstants.CtrTypeL.PLOAN_ACCTNO_FLAG_LIST, loanAcct);
				}
			}
			
			// 保證
			if(true){
				String generalGuaranteePlan = "一般保證責任";
				String jointGuaranteePlan = "連帶保證責任";
				jsParam.put("generalGuaranteePlan", generalGuaranteePlan);
				jsParam.put("generalGuaranteePlanInfo", "甲方到期（含喪失期限利益之視為到期）不履行債務時，經乙方對甲方之財產強制執行後而無效果時，一般保證人應代負履行之責任。乙方並得依第七條其他契約條款第(三)項約定方式，就一般保證人寄存乙方之存款及對乙方之一切債權行使抵銷權。");
				jsParam.put("jointGuaranteePlan", jointGuaranteePlan);
				jsParam.put("jointGuaranteePlanInfo", "甲方到期（含喪失期限利益之視為到期）不履行債務時，除銀行法第十二條之一或其他法律另有規定外，乙方得逕向連帶保證人求償，並得依第七條其他契約條款第(三)項約定方式，就連帶保證人寄存乙方之存款及對乙方之一切債權行使抵銷權。若抵銷金額不足抵償連帶保證人所應負擔之債務時，連帶保證人仍應繼續負責償還。");
				//~~~~~~~~~~~~~~~~~~~~~~~~
				String custPos = Util.trim(jsParam.optString("relatedPersonType"));
				String guaranteePlan = "";
				String guaranteeAmt = "";
			
				if(Util.equals(custPos, "")){
					
				}else{
					if(Util.equals(custPos, UtilConstants.lngeFlag.連帶保證人)){
						guaranteePlan = jointGuaranteePlan;
					}else if(Util.equals(custPos, UtilConstants.lngeFlag.ㄧ般保證人)){
						guaranteePlan = generalGuaranteePlan;
					}else{
						guaranteePlan = custPos;						
					}	
					guaranteeAmt = jsParam.optString("loanAmt");
				}
				jsParam.put("guaranteePlan", guaranteePlan);
				jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_GUARANTEEAMT, guaranteeAmt);
			}

			//J-110-0335_10702_B1002 Web e-Loan對保契約計算總費用年百分率
			if (true) {
				String eloan_pc_spTerm_yRate="";
				Map<String, Object> map = new HashMap<String, Object>();
				List<String> list = new ArrayList<String>();
				Integer loan = 0;
				Integer bufferMonth = 0;
				Integer cal_Type = 1 ;
				Integer limitMonth = 0;
				String rate = "";
				List<Double> multipleRate= new ArrayList<Double>();
				List<Integer> multipleRate_TimeStart= new ArrayList<Integer>();
				List<Integer> multipleRate_TimeEnd= new ArrayList<Integer>();
				List<Integer> costList = new ArrayList<Integer>();
				LMSUtil.addJsonToMap(map, jsParam);

				loan = Util.parseInt(jsParam.get("loanAmt"));
				L140S02A l140s02a = l140s02as.get(0);
				List<L140S02D> l140s02ds = clsService.findL140S02D_orderByPhase(l140m01a.getMainId(), l140s02a.getSeq(),
						"Y");
				boolean is_only_M3_and_pmRate_0 = ClsUtility.is_only_M3_and_pmRate_0(l140s02ds);
				boolean is_only_MR_and_pmRate_0 = ClsUtility.is_only_MR_and_pmRate_0(l140s02ds);
				BigDecimal l140s02dNowRate = BigDecimal.ZERO;

				//利率資訊
				if(is_only_M3_and_pmRate_0 || is_only_MR_and_pmRate_0){
					List<Map<String, Object>> lrRate_list = misMislnratService.findMislnratByLRRate(CrsUtil.RATE_TYPE_M3, "TWD" );
					String rateValue = "";
					if (is_only_MR_and_pmRate_0) {
						lrRate_list = misMislnratService.findMislnratByLRRate(CrsUtil.RATE_TYPE_MR, "TWD" );
					}
					if(lrRate_list.size() > 0){
						rateValue = LMSUtil.pretty_numStr(CrsUtil.parseBigDecimal(MapUtils.getObject(lrRate_list.get(0), "LR_RATE")));
					}
					String bgnNum = "";
					String endNum = "";
					if(l140s02ds.size()>0){
						multipleRate_TimeStart.add((l140s02ds.get(0).getBgnNum()));
						multipleRate_TimeEnd.add((l140s02ds.get(l140s02ds.size()-1).getEndNum()));
						limitMonth=(l140s02ds.get(l140s02ds.size()-1).getEndNum());
						l140s02dNowRate = l140s02ds.get(0).getNowRate() == null ? BigDecimal.ZERO : l140s02ds.get(0).getNowRate().setScale(2, BigDecimal.ROUND_DOWN);
					}
					multipleRate.add(Util.parseDouble(rateValue));
				}else{
					if(l140s02ds.size()>0){
						for (L140S02D l140s02d : l140s02ds) {
							Integer phase = l140s02d.getPhase();
							String l140s02dOid = l140s02d.getOid();
							l140s02dNowRate = l140s02d.getNowRate() == null ? BigDecimal.ZERO : l140s02d.getNowRate().setScale(2, BigDecimal.ROUND_DOWN);
							BigDecimal l140s02d_rateUser = l140s02d.getRateUser() == null ? BigDecimal.ZERO : l140s02d.getRateUser();
							if (phase == 1) {
								BigDecimal rateRange1Param03 = CapMath.getBigDecimal(
										MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_FIXED_RATE , ""));
								BigDecimal rateRange1Param07 = CapMath.getBigDecimal(
										MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_RESULTRATE , ""));
								if(CrsUtil.RATE_TYPE_01.equals(l140s02d.getRateType()) && Util.equals("C01", l140s02d.getRateUserType())){
//									multipleRate.add(l140s02d_rateUser.doubleValue());
									multipleRate.add(rateRange1Param03.doubleValue());
								}else{
//									multipleRate.add(l140s02dNowRate.doubleValue());
									multipleRate.add(rateRange1Param07.doubleValue());
								}
								multipleRate_TimeStart.add(l140s02d.getBgnNum());
								multipleRate_TimeEnd.add(l140s02d.getEndNum());
							} else if (phase == 2) {
								BigDecimal rateRange2Param03 = CapMath.getBigDecimal(
										MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2_FIXED_RATE , ""));
								BigDecimal rateRange2Param07 = CapMath.getBigDecimal(
										MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2_RESULTRATE , ""));
								if(CrsUtil.RATE_TYPE_01.equals(l140s02d.getRateType()) && Util.equals("C01", l140s02d.getRateUserType())){
//									multipleRate.add(l140s02d_rateUser.doubleValue());
									multipleRate.add(rateRange2Param03.doubleValue());
								}else{
//									multipleRate.add(l140s02dNowRate.doubleValue());
									multipleRate.add(rateRange2Param07.doubleValue());
								}
								multipleRate_TimeStart.add(l140s02d.getBgnNum());
								multipleRate_TimeEnd.add(l140s02d.getEndNum());
							}
						}
						limitMonth=(l140s02ds.get(l140s02ds.size()-1).getEndNum());
					}
				}
				for (L140M01R l140m01r : l140m01r_list) {
					if(l140m01r.getFeeAmt().compareTo(BigDecimal.ZERO)>0){
						costList.add(l140m01r.getFeeAmt().intValue());
					}
				}
				try {
					rate = clsService.calculateRate(loan*10000,limitMonth,bufferMonth,cal_Type,multipleRate,multipleRate_TimeStart,multipleRate_TimeEnd,costList);
					if(Util.isNotEmpty(rate) && list.size()==0){
						eloan_pc_spTerm_yRate =rate;
					}
					//J-112-0502  調整短期循環(續約)/中期循環(續約)總費用百分率
					String prodKind = l140s02a.getProdKind();
					if(Util.isEmpty(rate) && ("02".equals(prodKind) || "68".equals(prodKind))){
						eloan_pc_spTerm_yRate = l140s02dNowRate.toString();
					}
				}
				catch(Exception ex){
					LOGGER.error("calculateRate@"+Thread.currentThread().getStackTrace()[1].getMethodName()+" input{"
							+"loan:"+loan
							+", limitMonth:"+limitMonth
							+", bufferMonth:"+bufferMonth
							+", cal_Type:"+cal_Type
							+", multipleRate:"+multipleRate
							+", multipleRate_TimeStart:"+multipleRate_TimeStart
							+", multipleRate_TimeEnd:"+multipleRate_TimeEnd
							+", costList:"+costList
							+"}");
					LOGGER.error(StrUtils.getStackTrace(ex));
					eloan_pc_spTerm_yRate ="";
				}
				jsParam.put("annualPercentageRate", eloan_pc_spTerm_yRate);
			}

			if(is_cntrNo_belong_co70647919_c101_map){
				jsParam.put(ContractDocConstants.CtrTypeA.PLOAN_IS_CNTRNO_BELONG_CO70647919_C101, "Y");
				String loanPlan = MapUtils.getString(cntrNo_belong_co70647919_c101_map, "loanPlan");
				jsParam.put("grpCntrNo", MapUtils.getString(cntrNo_belong_co70647919_c101_map, "grpCntrNo"));				
				jsParam.put("loanPlan", loanPlan);
				jsParam.put("subscribeAmt", MapUtils.getString(cntrNo_belong_co70647919_c101_map, "subscribeAmt"));
				// 依「行銷方案」指定「撥款起迄區間」select * from com.bcodetype where codetype in ('C340M01A_loanPlan_ApprDtBeg','C340M01A_loanPlan_ApprDtEnd')
				// 已在前面的程式, 判斷出 loanPlan=="C101"
				jsParam.put("givenApprBegDate", Util.trim(MapUtils.getString(clsService.get_codeTypeWithOrder("C340M01A_loanPlan_ApprDtBeg"), loanPlan))); 
				jsParam.put("givenApprEndDate", Util.trim(MapUtils.getString(clsService.get_codeTypeWithOrder("C340M01A_loanPlan_ApprDtEnd"), loanPlan)));
			}

			//fix loanAmt如果json格式是integer產報表會跳錯
			jsParam.put("loanAmt", Util.trim(jsParam.getString("loanAmt")));

			//J-112-0205_10702_B1001 Web e-Loan新增代償相關欄位
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			String ssoUnitNo = user.getSsoUnitNo();
			String unitNo = user.getUnitNo();
			Boolean active_repayment = false;
			Map<String, String> map= clsService.get_codeType_codeDesc2WithOrder("LMS_FUNC_ON_FLAG","zh_TW");
			if(clsService.is_function_on_codetype("cls_repayment_pilot")){
				if (map.containsKey("cls_repayment_pilot")) {
					String val = Util.trim(map.get("cls_repayment_pilot"));
					if(val.contains(unitNo)){
						active_repayment = true;
					}
				}
			}
			if(clsService.is_function_on_codetype("cls_repayment_all")){
				active_repayment = true;
			}
			if (active_repayment) {
				JSONArray repaymentList = new JSONArray();
                String isRepayment = UtilConstants.DEFAULT.否;//預設代償塞否
//				C120S01C c120s01c = clsService.findC120S01C(l120m01a.getMainId(),l120m01a.getCustId(),l120m01a.getDupNo());
                if (l140s02as.size() > 0) {
                    List<L140S02H> l140s02hs = this.findByMainIdSeq(l140s02as.get(0).getMainId(), l140s02as.get(0).getSeq());
                    if (l140s02hs.size() > 0) {
                        for (L140S02H l140s02h:l140s02hs) {
                        	if(Util.isEmpty(l140s02h.getRepaymentProductType())){
                        		throw new CapMessageException("請至個金徵信 -> 償債能力 -> 個人負債比率，執行「計算」，確認代償欄位，並簽報書退回再重新引進借款人資訊。"
                        				, this.getClass());
                        	}
                            JSONObject compensation = new JSONObject();
                            String repaymentProductName = "";
							List<CodeType> jcicCodeTypes = codeTypeService.findByCodeTypeList("repaymentProductId");
							boolean isMatch = false;
							for (CodeType jcicitem : jcicCodeTypes) {
								if (l140s02h.getRepaymentProductType().equals(jcicitem.getCodeValue())) {
									isMatch = true;
									repaymentProductName = jcicitem.getCodeDesc();
								}
							}
							if (Util.equals(l140s02h.getRepaymentProductType(),"信用卡")) {
								isMatch = true;
							}
							//貸款符合 H，才允許新增代償列表
							if(isMatch){
								compensation.put("bankCode",l140s02h.getBankNo());
								compensation.put("bankName",l140s02h.getBankName());
								compensation.put("repaymentProductType",l140s02h.getRepaymentProductType() + repaymentProductName);
								compensation.put("originalAmt",l140s02h.getOriginalAmt());
								repaymentList.add(compensation);
							}
                        }
                        if (repaymentList.size()>0) {
                            isRepayment = UtilConstants.DEFAULT.是;
                            jsParam.put("repaymentList", repaymentList);
                        }
                    }
                    jsParam.put("isRepayment", isRepayment);
                }
			}
			c340m01c.setJsonData(jsParam.toString());
			clsService.save(c340m01c);
		}


		this.saveMeta(c340m01a);
	}

	@Override
	public void init_C340RelateCtrTypeB(C340M01A c340m01a, String caseMainId,
										String tabMainIds) throws CapException {

		JSONObject jsParam = new JSONObject();
		Integer cal_Type = 1 ;
		TreeMap<Integer, L140S02C> map_l140s02cData = new TreeMap<Integer, L140S02C>();
		TreeMap<Integer, List<L140S02D>> map_l140s02dData = new TreeMap<Integer, List<L140S02D>>();
		TreeMap<Integer, L140S02E> map_l140s02eData = new TreeMap<Integer, L140S02E>();
		TreeMap<Integer, L140S02F> map_l140s02fData = new TreeMap<Integer, L140S02F>();
		BigDecimal loanAmt = BigDecimal.ZERO;
		String html_space = "&nbsp;";
		String html_space_3 = StringUtils.repeat(html_space, 3);
		String html_space_6 = StringUtils.repeat(html_space, 3); // 6);

		String[] page1Params = getHouseLoanParams();
		for (String param : page1Params) {
			jsParam.put(param,"");
		}

		L140M01A l140m01a = clsService.findL140M01A_mainId(tabMainIds);
		List<C340M01B> c340m01bs = clsService.findC340M01B(c340m01a.getMainId());
		if (l140m01a != null) {
			L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);

			c340m01a.setCustName(l140m01a.getCustName());
			c340m01a.setContrNumber(l140m01a.getCntrNo());
			c340m01a.setContrPartyNm(l140m01a.getCustName());

//			String seq = numberService.getNumberWithMax(C340M01A.class,
//					c340m01a.getOwnBrId(), "", 99999);
//			c340m01a.setPloanCtrNo(l140m01a.getCntrNo() + "-" + Util.addZeroWithValue(seq, 5));
			c340m01a.setPloanCtrNo(build_ploanCtrNo(c340m01a.getCtrType(), l140m01a.getCntrNo()));

			if(true){
				jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_COURT_NAME, "");//地方法院預設是空白
			}
			C120S01A mainCust = clsService.findC120S01A(caseMainId, l140m01a.getCustId(), l140m01a.getDupNo());

			if (mainCust != null) {
				String mTel = mainCust.getMTel();
				String email = mainCust.getEmail();
				Date birthday = mainCust.getBirthday();
				jsParam.put("borrowerMobileNumber", mTel);
				jsParam.put("borrowerBirthDate", CapDate.formatDate(birthday, "yyyy-MM-dd"));
				jsParam.put("borrowerEmail", email);
			}


			C340M01B c340m01b = new C340M01B();
			c340m01b.setMainId(c340m01a.getMainId());
			c340m01b.setTabMainId(l140m01a.getMainId());
			c340m01b.setCntrNo(l140m01a.getCntrNo());
			clsService.save(c340m01b);

			C340M01C c340m01c = new C340M01C();
			c340m01c.setMainId(c340m01a.getMainId());
			c340m01c.setItemType(ContractDocConstants.C340M01C_ItemType.TYPE_0);
			c340m01c.setCreator(c340m01a.getCreator());
			c340m01c.setCreateTime(c340m01a.getCreateTime());


			// 借保人檔
			List<L140S01A> l140s01as = clsService.findL140S01A(l140m01a);
			//產品種類
			List<L140S02A> l140s02as = clsService.findL140S02A(l140m01a);
			if(true){ //預設值
				jsParam.put("relatedPersonType", "");
				jsParam.put("relatedPersonTypeDesc", "");
				jsParam.put("relatedPersonName", "");
				jsParam.put("relatedPersonId", "");
			}
			if (l140s01as == null || l140s01as.size() == 0) {
			} else if (l140s01as.size() == 1) {
//				// 關係人
//				L140S01A l140s01a = l140s01as.get(0);
//				String custId = l140s01a.getCustId();
//				String dupNo = l140s01a.getDupNo();
//				String custPos = l140s01a.getCustPos();
//				String custName = l140s01a.getCustName();
//
//				if(Util.equals(custPos, UtilConstants.lngeFlag.連帶保證人)){
//					//ok
//				}else if(Util.equals(custPos, UtilConstants.lngeFlag.ㄧ般保證人)){
//					//ok
//				}else{
//					throw new CapMessageException("借保人身分非「連帶保證人」或「一般保證人」，不適用線上對保", this.getClass());
//				}
//
//				C120S01A c120s01a = clsService.findC120S01A(caseMainId, custId, dupNo);
//				if (c120s01a != null) {
//					String mTel = c120s01a.getMTel();
//					String email = c120s01a.getEmail();
//					Date birthday = c120s01a.getBirthday();
//					jsParam.put("relatedPersonMobileNumber", mTel);
//					jsParam.put("relatedPersonBirthDate", CapDate.formatDate(birthday, "yyyy-MM-dd"));
//					jsParam.put("relatedPersonEmail", email);
//				}
//				//L140S01A_custPos
//				Map<String, String> custPosDescMap = codeTypeService.findByCodeType("L140S01A_custPos");
//				jsParam.put("relatedPersonType", custPos);
//				jsParam.put("relatedPersonTypeDesc", custPosDescMap.get(custPos));
//				jsParam.put("relatedPersonName", custName);
//				jsParam.put("relatedPersonId", custId);
				throw new CapMessageException("增提借保人不適用線上對保", this.getClass());
			} else {
				throw new CapMessageException("借保人超過1位，不適用線上對保", this.getClass());
			}


			if (l140s02as != null && l140s02as.size() == 1) {
				L140S02A l140s02a = l140s02as.get(0);
				
				//J-112-0502 產品種類02、68，動用期限只能為選項1、2、3、8
				String prodKindCheck = Util.trim(lmsService.getSysParamDataValue("J_112_0502_prodKindCheck_on"));
				if("Y".equals(prodKindCheck)){
					String prodKind = l140s02a.getProdKind();
					String useDeadline = l140m01a.getUseDeadline();
					if(("02".equals(prodKind) || "68".equals(prodKind)) && 
							!("1".equals(useDeadline) || "2".equals(useDeadline) || "3".equals(useDeadline) || "8".equals(useDeadline))){			
						throw new CapMessageException("產品種類02、68，動用期限只能為選項1、2、3、8", this.getClass());
					}
				}
				
				int lnYear = l140s02a.getLnYear() * 12;
				int lnMonth = l140s02a.getLnMonth();

				loanAmt = l140s02a.getLoanAmt() == null ? BigDecimal.ZERO : l140s02a.getLoanAmt();

				String loanAmt_chinese = CapCommonUtil.toChineseUpperAmount(LMSUtil.pretty_numStr(loanAmt), true);
//				jsParam.put("loanAmt", loanAmt.divide(BigDecimal.valueOf(10000), 0, BigDecimal.ROUND_HALF_UP));
                jsParam.put("loanAmt", LMSUtil.pretty_numStr(loanAmt));


				jsParam.put("loanPeriod", String.valueOf(lnYear + lnMonth));
				//jsParam.put("loanPurpose", get_loanPurpose(l120m01a));
				//借款用途
				//jsParam.putAll(build_loanPurpose_map_TypeB(l120m01a));
				jsParam.put("loanPurpose_otherDesc", "週轉");
				jsParam.put("drawDownType", "一次撥付");


				L140S02E l140s02e = clsService.findL140S02E(l140m01a.getMainId(), l140s02a.getSeq());
				if (l140s02e != null) {
					// 還款方式
					String l140s02e_payWay = Util.trim(l140s02e.getPayWay());
					String desc_l140s02e_payWay = "";
					if(Util.equals("1", l140s02e_payWay)){
						desc_l140s02e_payWay = "本息平均攤還";
						if(Util.equals(l120m01a.getSimplifyFlag(), "C")){
							desc_l140s02e_payWay += "(寬限期001期-006期)";
						}
					}else if(Util.equals("2", l140s02e_payWay)){
						desc_l140s02e_payWay = "本息平均攤還";
					}else if(Util.equals("3", l140s02e_payWay)){
						desc_l140s02e_payWay = "本金平均攤還";
						cal_Type = 2;
					}else if(Util.equals("7", l140s02e_payWay)){
						desc_l140s02e_payWay = "到期一次還本";
						cal_Type = 3;
					}else{
						throw new CapMessageException("還款方式非「1、2、3、7」，無法執行線上對保", this.getClass());
						// Map<String, String> payWayMap = codeTypeService.findByCodeType("L140S02E_payWay");
						// desc_l140s02e_payWay = payWayMap.get(l140s02e_payWay);
					}
					jsParam.put("repaymentMethod", desc_l140s02e_payWay);
				}

				L140S02F l140s02f = clsService.findL140S02F(l140m01a.getMainId(), l140s02a.getSeq());
				List<L140S02D> l140s02ds = clsService.findL140S02D_orderByPhase(l140m01a.getMainId(), l140s02a.getSeq(), "Y");
				boolean is_only_staff_and_pmRate_0 = this.get_CtrTypeB_Staff_RateType(l140s02ds.get(0).getRateType());

				//利率資訊
				if(is_only_staff_and_pmRate_0){
					List<Map<String, Object>> lrRate_list = misMislnratService.findMislnratByLRRate(l140s02ds.get(0).getRateType(), "TWD" );
					String rateValue = "";
					if(lrRate_list.size() > 0){
						rateValue = LMSUtil.pretty_numStr(CrsUtil.parseBigDecimal(MapUtils.getObject(lrRate_list.get(0), "LR_RATE")));
					}
					String bgnNum = "";
					String endNum = "";
					String rateDesc = proc_l140s02a_rateDesc(l140s02a);
					if(l140s02ds.size()>0){
						bgnNum = String.valueOf(l140s02ds.get(0).getBgnNum());

						endNum = String.valueOf(l140s02ds.get(l140s02ds.size()-1).getEndNum());
					}

					jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1, "Y");
					//jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RATEDESC, rateDesc);//員工利率說明
				}else{
					HashMap<String, LinkedHashMap<String, String>> _map = misMislnratService.findBaseRateByCurrs(
							new String[] { "TWD" });
					Map<String, String> rateDesc_map = _map.get("TWD");
					rateDesc_map.put(CrsUtil.RATE_TYPE_01, "自訂利率");

					if (l140s02ds != null && l140s02ds.size() > 2) {
						throw new CapMessageException("利率以2段為限，超過無法執行線上對保", this.getClass());
					} else {
						for (L140S02D l140s02d : l140s02ds) {

							Integer phase = l140s02d.getPhase();
							Integer bgnNum = l140s02d.getBgnNum();
							Integer endNum = l140s02d.getEndNum();
							String rateType = l140s02d.getRateType();
							BigDecimal l140s02d_baseRate = l140s02d.getBaseRate();
							BigDecimal baseRate = l140s02d_baseRate;
							if (CrsUtil.RATE_TYPE_01.equals(rateType) && Util.equals("C01", l140s02d.getRateUserType())) {

							}else{
								if(!Util.equals("1", l140s02d.getRateFlag())){ //非固定利率，去中心抓最新的代率值
									baseRate = _get_latest_mis_MISLNRAT_currTWD(rateType);
								}
							}

							String pmFlag = l140s02d.getPmFlag();
							BigDecimal pmRate = l140s02d.getPmRate();
							BigDecimal nowRate = ClsUtility.calc_nowRate(l140s02d.getRateType(), l140s02d.getRateUserType(), l140s02d.getRateFlag(), l140s02d.getNowRate(), baseRate, pmFlag, pmRate);
							BigDecimal rateUser = l140s02d.getRateUser();
							String l140s02dOid = l140s02d.getOid();

							if ("M".equals(pmFlag)) {
								// 和消金處確認過，不會有減碼的狀況產生，因契約文字就是這樣寫
								throw new CapMessageException("利率為減碼，不適用線上對保", this.getClass());
							}

							boolean isRateType01_C01 = false;

							if (CrsUtil.RATE_TYPE_01.equals(rateType) && Util.equals("C01", l140s02d.getRateUserType())) {
								isRateType01_C01 = true;
							} else {

							}

							if (1 == phase) { //送至 線上貸款平台 時，在 ContractDocService :: geCtrTypeBrateDesc(...) 組字
								jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1, "Y");
								jsParam.put("rateRange1Oid", l140s02dOid);
								if (isRateType01_C01) {
									jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_TYPE01, "Y");
									if(clsService.is_function_on_codetype("c340_RateType01_C01_periodHide")){

									}else{
										jsParam.put("rateRange1Param01", String.valueOf(bgnNum));
										jsParam.put("rateRange1Param02", String.valueOf(endNum));
									}
									jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_FIXED_RATE, LMSUtil.pretty_numStr(rateUser));
								} else {
									jsParam.put("rateRange1Param01", String.valueOf(bgnNum));
									jsParam.put("rateRange1Param02", String.valueOf(endNum));
									jsParam.put("rateRange1Param04", rateDesc_map.get(rateType));
									jsParam.put("rateRange1Param05", LMSUtil.pretty_numStr(baseRate));//將 baseRate 轉成字串，避免在 Map<String, String> rptVariableMap 去 putAll(jsContent); 因 Double, String 不符而出錯
									jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_PLUSRATE, LMSUtil.pretty_numStr(pmRate));
									jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RESULTRATE , LMSUtil.pretty_numStr(nowRate));
								}


							} else if (2 == phase) {
								jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2, "Y");
								jsParam.put("rateRange2Oid", l140s02dOid);
								if (isRateType01_C01) {
									jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2_TYPE01, "Y");
									if(clsService.is_function_on_codetype("c340_RateType01_C01_periodHide")){

									}else{
										jsParam.put("rateRange2Param01", String.valueOf(bgnNum));
										jsParam.put("rateRange2Param02", String.valueOf(endNum));
									}
									jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2_FIXED_RATE, LMSUtil.pretty_numStr(rateUser));
								} else {
									jsParam.put("rateRange2Param01", String.valueOf(bgnNum));
									jsParam.put("rateRange2Param02", String.valueOf(endNum));
									jsParam.put("rateRange2Param04", rateDesc_map.get(rateType));
									jsParam.put("rateRange2Param05", LMSUtil.pretty_numStr(baseRate));//將 baseRate 轉成字串，避免在 Map<String, String> rptVariableMap 去 putAll(jsContent); 因 Double, String 不符而出錯
									jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2_PLUSRATE, LMSUtil.pretty_numStr(pmRate));
									jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2_RESULTRATE , LMSUtil.pretty_numStr(nowRate));
								}
							}
						}
					}
				}

				if(true){
					String lendingPlanInfo_showOption = ""; //{1:無限制清償, 2:限制清償}
					//~~~~
					String advancedRedemptionTitle = "無限制清償期";
					String advancedRedemptionDesc = "甲方同意按本章第五條計付借款利息，甲方並得隨時償還借款或結清帳戶，無須支付違約金。";
					String advancedRateTitle = "利率"; //應只有出現在PLOAN系統的UI上
					//~~~~
					String limitedDesc_p1 = "{0}" //"甲方自撥款日起{0}期(含)內，若提前償還部分或全部本金，甲方應支付乙方提前清償違約金，計算方式如下：\n"
							+"甲方同意按本章第五條計付借款利息，並同意如於本借款撥款日起二年內提前清償本金時，給付提前清償違約金，但甲方因「提供抵押之不動產遭政府徵收或天災毀損並取得證明」、「借款人死亡或重大傷殘並取得證明」或「乙方主動要求還款」致須提前清償借款者，不在此限。上開提前清償違約金之計算方式如下：";
					String limitedDesc_p2 = "\n一、自撥款日起一年內提前還款者(第一年還款)，得就提前償還之本金按{1}%計收。";

					String limitedDesc_p3 = "\n二、自撥款日起超過一年未滿二年內提前還款者(第二年還款)，得就提前償還之本金按{2}%計收。";
					String limitedRedemptionTitle = "限制清償期";
					String limitedRedemptionDesc = ""; //組字
					String limitedRateTitle = "利率"; //應只有出現在PLOAN系統的UI上
					String advancedFirstRate = "1";
					String advancedSecondRate = "0.5";

					limitedRedemptionDesc = MessageFormat.format(limitedDesc_p1+limitedDesc_p2+limitedDesc_p3
							, "", html_space_3, html_space_3);
					if (l140s02f != null) {
						if (l140s02f.getPConBeg1() != null && l140s02f.getPConEnd1() != null && l140s02f.getPCalCon1() != null) {
							lendingPlanInfo_showOption = "2";
							limitedRedemptionDesc = MessageFormat.format(limitedDesc_p1+limitedDesc_p2+limitedDesc_p3, "", //l140s02f.getPConEnd2(),
									l140s02f.getPCalCon1(), l140s02f.getPCalCon2());
							advancedFirstRate = Util.trim(l140s02f.getPCalCon1());
							advancedSecondRate = Util.trim(l140s02f.getPCalCon2());
						} else {
							lendingPlanInfo_showOption = "1";
						}
					}else{
						lendingPlanInfo_showOption = "1";
					}
					//~~~~~~~~~~~~~~~~~~~~~
					jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_ADVANCED_REDEMPT_TITLE, advancedRedemptionTitle);
					jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_ADVANCED_REDEMPT_DESC, advancedRedemptionDesc);
					jsParam.put("advancedRateTitle", advancedRateTitle);
					jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_LIMITED_REDEMPT_TITLE, limitedRedemptionTitle);
					jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_LIMITED_REDEMPT_DESC, limitedRedemptionDesc);
					jsParam.put("limitedRateTitle", limitedRateTitle);
					jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_LENDING_PLAN_OPTION, lendingPlanInfo_showOption);
					String redemptionTitle = "";
					String redemptionDesc = "";
					String rateTitle = "";
					if(Util.equals("1", lendingPlanInfo_showOption)){
						redemptionTitle = advancedRedemptionTitle;
						redemptionDesc = advancedRedemptionDesc;
						rateTitle = advancedRateTitle;
					}else if(Util.equals("2", lendingPlanInfo_showOption)){
						redemptionTitle = limitedRedemptionTitle;
						redemptionDesc = limitedRedemptionDesc;
						rateTitle = limitedRateTitle;
					}
					jsParam.put("redemptionTitle", redemptionTitle);
					jsParam.put("redemptionDesc", redemptionDesc);
					jsParam.put("rateTitle", rateTitle);
					jsParam.put("advancedFirstRate", advancedFirstRate);
					jsParam.put("advancedSecondRate", advancedSecondRate);
				}
				//=================================
				if(true){
					jsParam.put("otherInfoTitle", "");
					if(is_only_staff_and_pmRate_0){
						//已在組字時，將自離職日起加3%的字串同時寫入
					}else{
						boolean has_M2_MD = false;
						boolean has_N2_MD = false;
						for (L140S02D l140s02d : l140s02ds) {
							String rateType = Util.trim(l140s02d.getRateType());
							if(Util.equals(CrsUtil.RATE_TYPE_M2, rateType)){
								has_M2_MD = true;
							} else if (Util.equals(CrsUtil.RATE_TYPE_N2, rateType)) {
								has_N2_MD = true;
							}
							else if (Util.equals(CrsUtil.RATE_TYPE_MR, rateType)) {
								has_N2_MD = true;
							}
						}

						if(has_M2_MD){
							jsParam.put("otherInfoTitle", "行員房貸方案備註");

							JSONArray otherInfoDesc_array = new JSONArray();
							otherInfoDesc_array.add("本借款利息，按乙方之行員房貸利率，機動調整。");
							jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_OTHERINFODESC, otherInfoDesc_array);
						}
						if(has_N2_MD){
							jsParam.put("otherInfoTitle", "行員房貸方案備註");

							JSONArray otherInfoDesc_array = new JSONArray();
							otherInfoDesc_array.add("本借款利息，按乙方之行員理財型貸款利率，機動調整。如借款人離職，原按本行員工房貸、理財型房貸利率，自離職日起改按本行消費金融放款指標利率加計0.51％計算，但因公死亡或退休者不在此限。");
							jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_OTHERINFODESC, otherInfoDesc_array);
						}
					}
				}

				List<L140M01O> l140m01os = clsService.findL140M01O(l140m01a.getMainId());
				if (l140m01os.size() == 0) {
					throw new CapMessageException("無擔保品，不適用線上對保", this.getClass());
				}
				if (l140m01os.size() > 1) {
					int CMS_01_count = 0;
					for (L140M01O l140m01o : l140m01os) {
						if (Util.equals(l140m01o.getCollTyp1(), UtilConstants.CollTyp1.不動產)) {
							CMS_01_count++;
						}
					}
					if (CMS_01_count == 0) {
						throw new CapMessageException("無不動產擔保品，不適用線上對保", this.getClass());
					}
					if (CMS_01_count > 1) {
						throw new CapMessageException("不動產擔保品超過1樣，不適用線上對保", this.getClass());
					}
				}

				//比對歡喜房貸31舊案
				//1.擔保品為不動產
				//2.(設定金額/1.2)-舊案餘額不得超過本次借款
				if (Util.equals(l140s02a.getProdKind(), ProdService.ProdKindEnum.房貸專案_B案_100億_31.getCode())) {
					BigDecimal setAmt = BigDecimal.ZERO;
					BigDecimal oldLoanAmt = BigDecimal.ZERO;
					for (L140M01O l140m01o : l140m01os) {
						if (Util.equals(l140m01o.getCollTyp1(), UtilConstants.CollTyp1.不動產)) {
							BigDecimal l140m01oSetAmt = BigDecimal.ZERO;
							if (l140m01o.getUnitChg().equals(UtilConstants.DEFAULT.是)) {
								l140m01oSetAmt = l140m01o.getSetAmt().multiply(new BigDecimal(1000));
							} else {
								l140m01oSetAmt = l140m01o.getSetAmt().multiply(new BigDecimal(10000));
							}
							setAmt = setAmt.add(l140m01oSetAmt);
						}
					}

					//舊案只計算不變、擔保品需與新案相同、擔保品只能有一筆
					List<L140M01A> l140m01as = l140m01aDao
							.findL140m01aListByL120m01cMainId(l120m01a.getMainId(),
									UtilConstants.Cntrdoc.ItemType.額度明細表, null);
					if (l140m01as.size() > 0) {
						for (L140M01A l140m01a_7 : l140m01as) {
							if (UtilConstants.Cntrdoc.Property.不變.equals(l140m01a_7.getProPerty())) {
								//產品種類
								List<L140S02A> oldl140s02as = clsService.findL140S02A(l140m01a_7);
								if (oldl140s02as != null && oldl140s02as.size() == 1) {
									if (Util.equals(oldl140s02as.get(0).getProdKind(), ProdService.ProdKindEnum.房貸專案_B案_100億_31.getCode())) {
										List<L140M01O> oldL140m01os = clsService.findL140M01O(l140m01a_7.getMainId());
										if (oldL140m01os.size() == 0) {
											throw new CapMessageException("舊案(" + l140m01a_7.getCntrChgOid() + ")無擔保品，不適用線上對保", this.getClass());
										}
										if (oldL140m01os.size() > 1) {
											int CMS_01_count = 0;
											for (L140M01O l140m01o : l140m01os) {
												if (Util.equals(l140m01o.getCollTyp1(), UtilConstants.CollTyp1.不動產)) {
													CMS_01_count++;
												}
											}
											if (CMS_01_count == 0) {
												throw new CapMessageException("舊案(" + l140m01a_7.getCntrChgOid() + ")無不動產擔保品，不適用線上對保", this.getClass());
											}
											if (CMS_01_count > 1) {
												throw new CapMessageException("舊案(" + l140m01a_7.getCntrChgOid() + ")不動產擔保品超過1樣，不適用線上對保", this.getClass());
											}
										}
										if (Util.equals(l140m01os.get(0).getCmsOid(), oldL140m01os.get(0).getCmsOid())) {
											oldLoanAmt = oldLoanAmt.add(l140m01a_7.getCurrentApplyAmt());
										}
									}
								} else {
									throw new CapMessageException("舊案產品種類超過1樣，不適用線上對保", this.getClass());
								}
							}
						}
					}

					//計算新案不可超過(設定金額/1.2)-舊案餘額
					BigDecimal maxAmt = (setAmt.divide(new BigDecimal("1.2"), 4, BigDecimal.ROUND_HALF_UP)).subtract(oldLoanAmt);
					if (l140m01a.getCurrentApplyAmt().compareTo(maxAmt) > 0) {
						throw new CapMessageException("本次增貸金額超過可申請額度，不適用線上對保", this.getClass());
					}
				}

				//交付方式
				jsParam.put("eloan_pa_deliv_A_cb", CB_Y_STR);
				jsParam.put("eloan_pa_deliv_A_t1", "活期儲蓄");
				if (Util.equals(l140s02a.getProdKind(),ProdService.ProdKindEnum.行家理財貸款_短期_02.getCode()) || Util.equals(l140s02a.getProdKind(),ProdService.ProdKindEnum.行家理財中期循環_68.getCode())) {
					// 透支科目102,104,202,204,404
					String[] overdraw_acct = new String[] { "12400000",
							"12100200", "12800000", "12600200", "13500200" };
					if (CrsUtil.inCollection(l140s02a.getSubjCode(),
							overdraw_acct)){
						jsParam.put("eloan_pa_deliv_A_cb", "");
						jsParam.put("eloan_pa_deliv_A_t1", "");
						jsParam.put("eloan_pa_deliv_B_cb", CB_Y_STR);
					}
				}

				//動用方式
				if(true){
					String distinct_lnTotMonth = get_distinct_lnTotMonth_cross_prod(l140s02as);
					//02、68且科目選透支 =>選二，02、68非透支科目選一
					if (Util.equals(l140s02a.getProdKind(),ProdService.ProdKindEnum.行家理財貸款_短期_02.getCode())
							|| Util.equals(l140s02a.getProdKind(),ProdService.ProdKindEnum.行家理財中期循環_68.getCode())) {
						// 透支科目102,104,202,204,404
						String[] overdraw_acct = new String[] { "12400000",
								"12100200", "12800000", "12600200", "13500200" };
						if (CrsUtil.inCollection(l140s02a.getSubjCode(),
								overdraw_acct)){
							jsParam.put("eloan_pa_use_B", "Y");
							jsParam.put("eloan_pa_use_B_t8", jsParam.optString("loanAmt"));
							if(Util.isNotEmpty(distinct_lnTotMonth)){
								jsParam.put("eloan_pa_use_B_t7", distinct_lnTotMonth);
							}
						}
						else{
							jsParam.put("eloan_pa_use_A", "Y");
							jsParam.put("eloan_pa_use_A_t8", jsParam.optString("loanAmt"));
							if(Util.isNotEmpty(distinct_lnTotMonth)){
								jsParam.put("eloan_pa_use_A_t7", distinct_lnTotMonth);
							}
						}
					}

					//03、31(中、長期不循環)且科目14500100 =>選四
					if (Util.equals(l140s02a.getProdKind(),ProdService.ProdKindEnum.行家理財貸款_中長期_03.getCode())
							|| Util.equals(l140s02a.getProdKind(),ProdService.ProdKindEnum.房貸專案_B案_100億_31.getCode())) {
						boolean match_eloan_pa_use_D = false; //適用於中、長期不循環借款
						if(l140m01a!=null && Util.equals(UtilConstants.Cntrdoc.ReUse.不循環使用, l140m01a.getReUse())){
							if(Util.isNotEmpty(distinct_lnTotMonth)){
								int i_lnTotMonth = Integer.parseInt(distinct_lnTotMonth);
								//~~~~
								if(i_lnTotMonth > 12){ //月份>12 ，表示中、長期
									match_eloan_pa_use_D = true;
								}
							}
						}
						if(!Util.equals("3", l140m01a.getUseDeadline())){ // 自簽約日起
							throw new CapMessageException("若需採線上對保，產品種類03、31其動用期限應選擇「3.自簽約日起MM個月」", this.getClass());
						}
						if(true){
							BigDecimal l140m01a_currentApplyAmt = l140m01a.getCurrentApplyAmt();
							BigDecimal sum_l140s02a_amt = sum_l140s02a_amt = l140s02a.getLoanAmt();
							BigDecimal choseAmt = (sum_l140s02a_amt.compareTo(BigDecimal.ZERO)>0 && sum_l140s02a_amt.compareTo(l140m01a_currentApplyAmt)<0)?sum_l140s02a_amt:l140m01a_currentApplyAmt;

							if(match_eloan_pa_use_D && Util.equals("3", l140m01a.getUseDeadline())){ // 自簽約日起
								jsParam.put("eloan_pa_use_D", "Y");
								jsParam.put("eloan_pa_use_D_t1", l140m01a.getDesp1());
								jsParam.put("eloan_pa_use_D_t2", jsParam.optString("loanAmt"));
							}
						}
						if(true){
							if(Util.isNotEmpty(distinct_lnTotMonth)){
								int i_lnTotMonth = Integer.parseInt(distinct_lnTotMonth);
								//~~~~
								if(match_eloan_pa_use_D && Util.equals("3", l140m01a.getUseDeadline()) && Util.isNotEmpty(l140m01a.getDesp1())){ // 自簽約日起
									jsParam.put("eloan_pa_use_D", "Y");
									jsParam.put("eloan_pa_use_D_t3", LMSUtil.pretty_numStr(Arithmetic.div(BigDecimal.valueOf(i_lnTotMonth), BigDecimal.valueOf(12), 2)));
								}
								// eloan_pa_use_y = String.valueOf(i_lnTotMonth/12);
								// eloan_pa_use_m = String.valueOf(i_lnTotMonth%12);
							}
						}
					}
				}

				//貸款本息攤還方式之約定 L140S02E.payWay
				Set<Integer> noPPP_seq_set = new LinkedHashSet<Integer>();
				Set<Integer> withPPP_seq_set = new LinkedHashSet<Integer>();
				String distinct_cycl_cross_prod = "";
				String distinct_rtType_cross_prod = "";
				String distinct_extendCntSinceFirst_cross_prod = "";
				if(true){
					String eloan_pa_repay_way = "";

					L140S02C l140s02c = clsService.findL140S02C(l140m01a.getMainId(), l140s02a.getSeq());
					List<L140S02D> l140s02d_list = clsService.findL140S02D_orderByPhase(l140m01a.getMainId(), l140s02a.getSeq(), "Y");
					if(l140s02c!=null){
						map_l140s02cData.put(l140s02a.getSeq(), l140s02c);
					}
					if(l140s02d_list!=null){
						map_l140s02dData.put(l140s02a.getSeq(), l140s02d_list);
					}
					if (l140s02e != null) {
						map_l140s02eData.put(l140s02a.getSeq(), l140s02e);
					}
					if (l140s02f != null) {
						map_l140s02fData.put(l140s02a.getSeq(), l140s02f);

						//==================================
						// 用 L140S02F.pConBeg1 來區分{無限制清償, 限制清償}
						// 例如：產品種類10, 但有輸入「提前還本管制設定」
						if(ContractDocUtil.belong_no_PPP(l140s02f)){
							noPPP_seq_set.add(l140s02a.getSeq());
						}else{
							withPPP_seq_set.add(l140s02a.getSeq());
						}
					}


					distinct_cycl_cross_prod = get_distinct_cycl_cross_prod(l140s02as, map_l140s02cData, map_l140s02eData);
					distinct_rtType_cross_prod = get_distinct_rtType_cross_prod(l140s02as, map_l140s02cData, map_l140s02eData);
					distinct_extendCntSinceFirst_cross_prod = get_distinct_extendCntSinceFirst_cross_prod(l140s02as, map_l140s02cData, map_l140s02eData);

					if(Util.isNotEmpty(distinct_cycl_cross_prod)
							&& Util.isNotEmpty(distinct_rtType_cross_prod)
							&& Util.isNotEmpty(distinct_extendCntSinceFirst_cross_prod)){
						boolean match_CtrTypeB_repay1 = false;
						boolean match_CtrTypeB_repay2 = false;
						boolean match_CtrTypeB_repay3 = false;
						boolean match_CtrTypeB_repay4 = false;
						boolean match_CtrTypeB_repay5 = false;
						boolean match_CtrTypeB_repay6 = false;
						if(Util.equals("1", distinct_cycl_cross_prod)){
							//************************************
							//月繳
							if(Util.equals("2", distinct_rtType_cross_prod)){ //本息平均
								if(Util.equals(VAL_OTHER, distinct_extendCntSinceFirst_cross_prod)){

								}else if(Util.equals("0", distinct_extendCntSinceFirst_cross_prod)){ //無寬限期
									match_CtrTypeB_repay2 = true;
								}else{ //有寬限期
									int extendCntSinceFirst_cross_prod = Util.parseInt(distinct_extendCntSinceFirst_cross_prod);
									String[] exend_info_arr = fmt_extendYear(extendCntSinceFirst_cross_prod);
									jsParam.put("eloan_pa_repay_D_t1", exend_info_arr[0]);
									jsParam.put("eloan_pa_repay_D_t2", exend_info_arr[1]);
									jsParam.put("eloan_pa_repay_D_t3", exend_info_arr[2]);
									jsParam.put("eloan_pa_repay_D_t4", exend_info_arr[3]);
									match_CtrTypeB_repay4 = true;
								}
							}else if(Util.equals("3", distinct_rtType_cross_prod)){ //本金平均
								if(Util.equals(VAL_OTHER, distinct_extendCntSinceFirst_cross_prod)){

								}else if(Util.equals("0", distinct_extendCntSinceFirst_cross_prod)){ //無寬限期
									match_CtrTypeB_repay3 = true;
								}else{ //有寬限期
									int extendCntSinceFirst_cross_prod = Util.parseInt(distinct_extendCntSinceFirst_cross_prod);
									String[] exend_info_arr = fmt_extendYear(extendCntSinceFirst_cross_prod);
									jsParam.put("eloan_pa_repay_E_t1", exend_info_arr[0]);
									jsParam.put("eloan_pa_repay_E_t2", exend_info_arr[1]);
									jsParam.put("eloan_pa_repay_E_t3", exend_info_arr[2]);
									jsParam.put("eloan_pa_repay_E_t4", exend_info_arr[3]);
									match_CtrTypeB_repay5 = true;
								}
							}else{
								//期付金{月繳}的攤還方式，但(非本息平均、非本金平均)
							}
						}else if(Util.equals("2", distinct_cycl_cross_prod)){
							//************************************
							//雙週繳
							String distinct_intEndTermSinceFirst_cross_prod = get_distinct_intEndTermSinceFirst_cross_prod(l140s02as, map_l140s02cData, map_l140s02dData);
							if(Util.equals(VAL_OTHER, distinct_intEndTermSinceFirst_cross_prod)){

							}else{
								jsParam.put("eloan_pa_repay_F_t1", distinct_intEndTermSinceFirst_cross_prod);
								match_CtrTypeB_repay6 = true;
							}
						}else if(Util.equals(VAL_MONTH_INTEREST, distinct_cycl_cross_prod)){//按月計息
							match_CtrTypeB_repay1 = true;
						}else{
							//************************************
							//其它繳款週期(L140S02E_payWayOth)
						}

						if(true){
							LinkedHashMap<String, Boolean> CtrTypeB_ctrRepayMap = new LinkedHashMap<String, Boolean>();
							jsParam.put("eloan_pa_repay_A", match_CtrTypeB_repay1? CB_Y_STR : "");
							jsParam.put("eloan_pa_repay_B", match_CtrTypeB_repay2? CB_Y_STR : "");
							jsParam.put("eloan_pa_repay_C", match_CtrTypeB_repay3? CB_Y_STR : "");
							jsParam.put("eloan_pa_repay_D", match_CtrTypeB_repay4? CB_Y_STR : "");
							jsParam.put("eloan_pa_repay_E", match_CtrTypeB_repay5? CB_Y_STR : "");
							jsParam.put("eloan_pa_repay_F", match_CtrTypeB_repay6? CB_Y_STR : "");
//							CtrTypeB_ctrRepayMap.put("一", match_CtrTypeB_repay1);
//							CtrTypeB_ctrRepayMap.put("二", match_CtrTypeB_repay2);
//							CtrTypeB_ctrRepayMap.put("三", match_CtrTypeB_repay3);
//							CtrTypeB_ctrRepayMap.put("四", match_CtrTypeB_repay4);
//							CtrTypeB_ctrRepayMap.put("五", match_CtrTypeB_repay5);
//							CtrTypeB_ctrRepayMap.put("六", match_CtrTypeB_repay6);
							eloan_pa_repay_way = build_choseKey_by_booleanVal(CtrTypeB_ctrRepayMap, "、");
						}
					}else{
						//可能A) 2個產品, 都是 {本息均攤、月繳}, 但從 中間期數 開始申請「寬限期」
						//可能B) 2個產品, {1個是 本息均攤}{另1個是 本金均攤}}
					}

					if(Util.isEmpty(eloan_pa_repay_way) && Util.equals(VAL_OVERDRAW, distinct_cycl_cross_prod)){
						jsParam.put("eloan_pa_repay_A", CB_Y_STR);
					} else if (Util.isEmpty(eloan_pa_repay_way) && Util.equals(VAL_MONTH_INTEREST, distinct_cycl_cross_prod)) {
						jsParam.put("eloan_pa_repay_A", CB_Y_STR);
					} else {

					}
				}

				//判斷是否有「限制清償期間」
				if(true){
					String rateDesc = "";
					if(noPPP_seq_set.size()>0){
						if(true){
							jsParam.put("eloan_pa_intr_noPPP_cb", CB_Y_STR);
							jsParam.put("eloan_pa_repay_noPPP_cb", CB_Y_STR);
						}
						LinkedHashMap<String, LinkedHashSet<String>> l140s02d_phase_map = get_distinct_l140s02d_intr_info(l140s02as, noPPP_seq_set, map_l140s02cData, map_l140s02dData);
						boolean build_intr_text = true;
						String traceStr = "noPPP"
								+", is_mapValue_cnt_eq_1=" + is_mapValue_cnt_eq_1(l140s02d_phase_map)
								+", phase_size=" + l140s02d_phase_map.size()
								+")";
						//~~~~~~
						LOGGER.debug(traceStr + "distinct_cycl_cross_prod="+distinct_cycl_cross_prod);
						if(is_mapValue_cnt_eq_1(l140s02d_phase_map)){
							if(l140s02d_phase_map.size()==1){ //一段式利率
								String intr_info = "";
								for(String phase: l140s02d_phase_map.keySet()){ //在此段程式之前，已先限制 is_mapValue_cnt_eq_1==true
									intr_info = StringUtils.join(l140s02d_phase_map.get(phase), "");
								}
								LOGGER.debug(traceStr + "intr_info="+intr_info);
								if(Util.isNotEmpty(intr_info)){
									String[] intr_info_arr = intr_info.split("\\|");
									String rateType = intr_info_arr[2];
									String baseRate = intr_info_arr[3];
									String pmFlag = intr_info_arr[4];
									String pmRate = intr_info_arr[5];
//								String nowRate = intr_info_arr[6];
									String rateFlag = intr_info_arr[7];
									if(Util.equals(CrsUtil.RATE_TYPE_6R, rateType) && (Util.equals("", pmFlag)||Util.equals("P", pmFlag)) ){
										if (Util.equals(l140s02a.getProdKind(),ProdService.ProdKindEnum.行家理財貸款_短期_02.getCode())
												|| Util.equals(l140s02a.getProdKind(),ProdService.ProdKindEnum.行家理財中期循環_68.getCode())) {
											build_intr_text = false;
											jsParam.put("eloan_pa_intr_noPPP_1t1", Util.equals("P", pmFlag)?pmRate:"0");
											jsParam.put("eloan_pa_intr_noPPP_baseRate", baseRate);
										}else if (Util.equals(l140s02a.getProdKind(),ProdService.ProdKindEnum.房貸專案_B案_100億_31.getCode())
												|| Util.equals(l140s02a.getProdKind(),ProdService.ProdKindEnum.行家理財貸款_中長期_03.getCode())) {
											build_intr_text = false;
											jsParam.put("eloan_pa_intr_noPPP_2t1", Util.equals("P", pmFlag)?pmRate:"0");
											jsParam.put("eloan_pa_intr_noPPP_baseRate", baseRate);
										}
//										if(Util.equals("2", rateFlag)){ //按日計息 => 機動
//											build_intr_text = false;
//											if(!build_intr_text){
//												jsParam.put("eloan_pa_intr_noPPP_1t1", Util.equals("P", pmFlag)?pmRate:"0");
//												jsParam.put("eloan_pa_intr_noPPP_baseRate", baseRate);
//											}
//										}else if(Util.equals("3", rateFlag)
//												&& Util.equals("1", intr_info_arr[8])
//												&& Util.equals("1", intr_info_arr[9]) ){ //每月浮動(一段式)
//											build_intr_text = false;
//											if(!build_intr_text){
//												jsParam.put("eloan_pa_intr_noPPP_2t1", Util.equals("P", pmFlag)?pmRate:"0");
//												jsParam.put("eloan_pa_intr_noPPP_baseRate", baseRate);
//											}
//										}else if(Util.equals("1", rateFlag) ){ //固定(一段式)
//											build_intr_text = false;
//											if(!build_intr_text){
//												jsParam.put("eloan_pa_intr_noPPP_3t1", Util.equals("P", pmFlag)?pmRate:"0");
//												jsParam.put("eloan_pa_intr_noPPP_baseRate", baseRate);
//											}
//										}
									}
								}
							}else{
								//多段式
								build_intr_text = true;
							}
						}else{
							build_intr_text = true;
						}

						if(!build_intr_text){
							//已填入組字的欄位
						}else{
							rateDesc = build_intr_textStr(l140s02as, noPPP_seq_set, map_l140s02dData, distinct_cycl_cross_prod);
							if (Util.equals(jsParam.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1),"Y")) {
								if (Util.isNotEmpty(rateDesc)) {
									BigDecimal baseRate = _get_latest_mis_MISLNRAT_currTWD("6R");

									String quit_Desc = MessageFormat.format("除甲方因公死亡或退休者外，本貸款自乙方認定之甲方離職日起(甲方之離職事由包括但不限於留職停薪、辭職、退職、資遣、死亡等)，改按乙方公告之消費金融放款指標利率加年利率0.51％（簽約日之本貸款利率合計為年利率{0}％）浮動計息。乙方消費金融放款指標利率每月調整乙次，調整時本貸款利率隨同調整，並於每屆滿一個月之日按調整後之年利率計息。"
											, LMSUtil.pretty_numStr(baseRate.add(new BigDecimal("0.51"))));
									rateDesc = rateDesc + quit_Desc;
									jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RATEDESC, rateDesc);
								}
							}
							jsParam.put("eloan_pa_intr_noPPP_4t1", rateDesc);
						}
					}
					if(withPPP_seq_set.size()>0){
						if(true){
							jsParam.put("eloan_pa_intr_withPPP_cb", CB_Y_STR);
							jsParam.put("eloan_pa_repay_withPPP_cb", CB_Y_STR);
						}
						LinkedHashMap<String, LinkedHashSet<String>> l140s02d_phase_map = get_distinct_l140s02d_intr_info(l140s02as, withPPP_seq_set, map_l140s02cData, map_l140s02dData);
						boolean build_intr_text = true;
						String traceStr = "withPPP"
								+", is_mapValue_cnt_eq_1=" + is_mapValue_cnt_eq_1(l140s02d_phase_map)
								+", phase_size=" + l140s02d_phase_map.size()
								+")";
						//~~~~~~
						LOGGER.debug(traceStr+"distinct_cycl_cross_prod="+distinct_cycl_cross_prod);
						if(is_mapValue_cnt_eq_1(l140s02d_phase_map)
								&& Util.equals("1", distinct_cycl_cross_prod)  ){ //月繳
							if(l140s02d_phase_map.size()==1){ //一段式利率
								String intr_info = "";
								for(String phase: l140s02d_phase_map.keySet()){ //在此段程式之前，已先限制 is_mapValue_cnt_eq_1==true
									intr_info = StringUtils.join(l140s02d_phase_map.get(phase), "");
								}
								LOGGER.debug(traceStr + "intr_info="+intr_info);
								if(Util.isNotEmpty(intr_info)){
									String[] intr_info_arr = intr_info.split("\\|");
									String bgnNum = intr_info_arr[0];
									String endNum = intr_info_arr[1];
									String rateType = intr_info_arr[2];
									String baseRate = intr_info_arr[3];
									String pmFlag = intr_info_arr[4];
									String pmRate = intr_info_arr[5];
									String nowRate = intr_info_arr[6];
									String rateFlag = intr_info_arr[7];
									if(Util.equals("1", rateFlag)){ //固定(一段式)
										build_intr_text = false;
										if(!build_intr_text){
											jsParam.put("eloan_pa_intr_withPPP_1x1", bgnNum);
											jsParam.put("eloan_pa_intr_withPPP_1x2", endNum);
											jsParam.put("eloan_pa_intr_withPPP_1x3", nowRate);
										}
									}else if(Util.equals(CrsUtil.RATE_TYPE_6R, rateType) && (Util.equals("", pmFlag)||Util.equals("P", pmFlag)) ){
										if(Util.equals("3", rateFlag)
												&& Util.equals("1", intr_info_arr[8])
												&& Util.equals("1", intr_info_arr[9]) ){ //每月浮動(一段式)
											build_intr_text = false;
											if(!build_intr_text){
												jsParam.put("eloan_pa_intr_withPPP_1y1", bgnNum);
												jsParam.put("eloan_pa_intr_withPPP_1y2", endNum);
												jsParam.put("eloan_pa_intr_withPPP_1y3", Util.equals("P", pmFlag)?pmRate:"0");
//											eloan_pa_intr_withPPP_1y4 = nowRate;

												if(true){
													jsParam.put("eloan_pa_intr_withPPP_baseRate", baseRate);
												}
											}
										}
									}
								}
							}
							if(l140s02d_phase_map.size()==2){//二段式利率
								String[] intr_info_list = new String[]{"", ""};
								int currentIdx = 0;
								for(String phase: l140s02d_phase_map.keySet()){ //在此段程式之前，已先限制 is_mapValue_cnt_eq_1==true
									intr_info_list[currentIdx] = StringUtils.join(l140s02d_phase_map.get(phase), "");
									++currentIdx;
									if(currentIdx>=2){
										break;
									}
								}
								LOGGER.debug(traceStr+ "intr_info_1st="+intr_info_list[0]);
								LOGGER.debug(traceStr+ "intr_info_2nd="+intr_info_list[1]);
								if(Util.isNotEmpty(intr_info_list[0]) && Util.isNotEmpty(intr_info_list[1])){
									String[] intr_info_1st_arr = intr_info_list[0].split("\\|");
									String[] intr_info_2nd_arr = intr_info_list[1].split("\\|");
									String bgnNum_1st = intr_info_1st_arr[0];
									String endNum_1st = intr_info_1st_arr[1];
									String rateType_1st = intr_info_1st_arr[2];
									String baseRate_1st = intr_info_1st_arr[3];
									String pmFlag_1st = intr_info_1st_arr[4];
									String pmRate_1st = intr_info_1st_arr[5];
//								String nowRate_1st = intr_info_1st_arr[6];
									String rateFlag_1st = intr_info_1st_arr[7];
									//~~~~~~
									String bgnNum_2nd = intr_info_2nd_arr[0];
									String endNum_2nd = intr_info_2nd_arr[1];
									String rateType_2nd = intr_info_2nd_arr[2];
									String baseRate_2nd = intr_info_2nd_arr[3];
									String pmFlag_2nd = intr_info_2nd_arr[4];
									String pmRate_2nd = intr_info_2nd_arr[5];
//								String nowRate_2nd = intr_info_2nd_arr[6];
									String rateFlag_2nd = intr_info_2nd_arr[7];
									//~~~~~~
									if(Util.equals(CrsUtil.RATE_TYPE_6R, rateType_1st) && (Util.equals("", pmFlag_1st)||Util.equals("P", pmFlag_1st))
											&& Util.equals(CrsUtil.RATE_TYPE_6R, rateType_2nd) && (Util.equals("", pmFlag_2nd)||Util.equals("P", pmFlag_2nd)) ){
										if(Util.equals("3", rateFlag_1st)
												&& Util.equals("1", intr_info_1st_arr[8])
												&& Util.equals("1", intr_info_1st_arr[9])
												&& Util.equals("3", rateFlag_2nd)
												&& Util.equals("1", intr_info_2nd_arr[8])
												&& Util.equals("1", intr_info_2nd_arr[9]) ){ //每月浮動(二段式)
											build_intr_text = false;
											if(!build_intr_text){
												jsParam.put("eloan_pa_intr_withPPP_1y1", bgnNum_1st);
												jsParam.put("eloan_pa_intr_withPPP_1y2", endNum_1st);
												jsParam.put("eloan_pa_intr_withPPP_1y3", Util.equals("P", pmFlag_1st)?pmRate_1st:"0");
//											eloan_pa_intr_withPPP_1y4 = nowRate_1st;

												jsParam.put("eloan_pa_intr_withPPP_1y5", bgnNum_2nd);
												jsParam.put("eloan_pa_intr_withPPP_1y6", endNum_2nd);
												jsParam.put("eloan_pa_intr_withPPP_1y7", Util.equals("P", pmFlag_2nd)?pmRate_2nd:"0");
//											eloan_pa_intr_withPPP_1y8 = nowRate_2nd;

												if(Util.equals(baseRate_1st, baseRate_2nd)){
													jsParam.put("eloan_pa_intr_withPPP_baseRate", baseRate_1st);
												}
											}
										}else{
											//phase1及 phase2 有任一為［6R定期浮動］以外
										}
									}
								}
							}
						}else{
							build_intr_text = true;
						}

						if(!build_intr_text){
							//已填入組字的欄位
						}else{
							rateDesc = build_intr_textStr(l140s02as, withPPP_seq_set, map_l140s02dData, distinct_cycl_cross_prod);
							if (Util.equals(jsParam.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1),"Y")) {
								if (Util.isNotEmpty(rateDesc)) {
									BigDecimal baseRate = _get_latest_mis_MISLNRAT_currTWD("6R");

									String quit_Desc = MessageFormat.format("除甲方因公死亡或退休者外，本貸款自乙方認定之甲方離職日起(甲方之離職事由包括但不限於留職停薪、辭職、退職、資遣、死亡等)，改按乙方公告之消費金融放款指標利率加年利率0.51％（簽約日之本貸款利率合計為年利率{0}％）浮動計息。乙方消費金融放款指標利率每月調整乙次，調整時本貸款利率隨同調整，並於每屆滿一個月之日按調整後之年利率計息。"
											, LMSUtil.pretty_numStr(baseRate.add(new BigDecimal("0.51"))));
									rateDesc = rateDesc + quit_Desc;
									jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RATEDESC, rateDesc);
								}
							}
							jsParam.put("eloan_pa_intr_noPPP_4t1", rateDesc);
						}
					}
					
					//J-113-0050 房貸增貸對保新增約據,相關欄位塞入預設值
					String collateralContractTerms = "1"; // 1=無租賃切結  2=租賃切結
					for(L140M01O l140m01o : l140m01os){
						if(!"1".equals(l140m01o.getTerm1())){
							collateralContractTerms = "2";
						}
						BigDecimal inMinus = l140m01o.getInMinus() != null ? l140m01o.getInMinus() : BigDecimal.ZERO; //租賃用益減值
						if(inMinus.compareTo(BigDecimal.ZERO) > 0){
							collateralContractTerms = "2";
						}
					}
					jsParam.put("collateralContractTerms", collateralContractTerms);
					
				}
			} else {
				throw new CapMessageException("產品種類超過1樣，不適用線上對保", this.getClass());
			}

			//手續費先有預設值
			jsParam.put("preliminaryFee", "0");
			jsParam.put("creditCheckFee", "0");
			jsParam.put("renewFee", "2000");
			jsParam.put("changeFee", "2000");
			jsParam.put("certFee", "100");
			jsParam.put("reissueFee", "500");
			BigDecimal sum_preliminaryFee = BigDecimal.ZERO;
			List<L140M01R> l140m01r_list = clsService.findL140M01R_exclude_feeSrc3(caseMainId);
			for (L140M01R l140m01r : l140m01r_list) {
				String feeNo = l140m01r.getFeeNo();
				if ("01".equals(feeNo)) {
					// 開辦費=開辦費+續約作業費+變更條件手續費
					if (l140m01r.getFeeAmt() != null) {
						sum_preliminaryFee = sum_preliminaryFee.add(l140m01r.getFeeAmt());
					}
				} else if ("02".equals(feeNo)) {
					// 信用查詢費
					jsParam.put("creditCheckFee",
							l140m01r.getFeeAmt() == null ? BigDecimal.ZERO : LMSUtil.pretty_numStr(l140m01r.getFeeAmt()));
				} else if ("03".equals(feeNo) || "04".equals(feeNo)) {
					// 續約作業費
					// 變更條件手續費
					sum_preliminaryFee = l140m01r.getFeeAmt() == null ? sum_preliminaryFee : sum_preliminaryFee.add(l140m01r.getFeeAmt());
				}
			}
			if (sum_preliminaryFee.compareTo(BigDecimal.ZERO) > 0) {
				jsParam.put("preliminaryFee",LMSUtil.pretty_numStr(sum_preliminaryFee));
			}

			//存款帳號
			if(true){
				List<Map<String, Object>> accounts = dwdbBASEService.getGuarOnLineAccount(l140m01a.getCustId());
				if (accounts != null && accounts.size() > 0) {
					JSONArray loanAcct = new JSONArray();
					/*
					  例如：66-黃金存摺
					*/
					Map<String, String> exclude_dpCate = clsService.get_codeTypeWithOrder("c340_ctrTypeA_exclude_dpCate");
					for (Map<String, Object> account : accounts) {
						String accoutNo = MapUtils.getString(account, "MR_PB_ACT_NO", "");
						String taxNo = MapUtils.getString(account, "MR_PB_TAX_NO", "");
						String dpCate = ClsUtility.get_acctNo_APCODE(accoutNo); //存款帳號第4~5碼是存款種類
						if(exclude_dpCate.containsKey(dpCate)){
							continue;
						}
						if(Util.equals("09", dpCate)){
							if(ClsUtility.ctr_acctNo_APCODE_09_forPersoanlLoanContract(accoutNo, taxNo)){
								//可用於線上對保契約的撥款
							}else{
								continue;
							}
						}
						loanAcct.add(accoutNo);
					}
					if (loanAcct.size()>0) {
						jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_ACCTNO_LIST, loanAcct);
					}
				}
			}

			//借款資訊
			Map<String, L140M01A> c340m01b_map = new HashMap<String, L140M01A>();
			c340m01b_map.put(c340m01b.getTabMainId(), l140m01a);
			//LinkedHashMap<String, String> param_map = contractDocService.init_houseContract(c340m01a, c340m01b_map);
			//jsParam.putAll( param_map );

			// 保證
			if(true){
				String generalGuaranteePlan = "一般保證責任";
				String jointGuaranteePlan = "連帶保證責任";
				jsParam.put("generalGuaranteePlan", generalGuaranteePlan);
				jsParam.put("generalGuaranteePlanInfo", "甲方到期（含喪失期限利益之視為到期）不履行債務時，經乙方對甲方之財產強制執行後而無效果時，一般保證人應代負履行之責任。乙方並得依第七條其他契約條款第(三)項約定方式，就一般保證人寄存乙方之存款及對乙方之一切債權行使抵銷權。");
				jsParam.put("jointGuaranteePlan", jointGuaranteePlan);
				jsParam.put("jointGuaranteePlanInfo", "甲方到期（含喪失期限利益之視為到期）不履行債務時，除銀行法第十二條之一或其他法律另有規定外，乙方得逕向連帶保證人求償，並得依第七條其他契約條款第(三)項約定方式，就連帶保證人寄存乙方之存款及對乙方之一切債權行使抵銷權。若抵銷金額不足抵償連帶保證人所應負擔之債務時，連帶保證人仍應繼續負責償還。");
				//~~~~~~~~~~~~~~~~~~~~~~~~
				String custPos = Util.trim(jsParam.optString("relatedPersonType"));
				String guaranteePlan = "";
				String guaranteeAmt = "";

				if(Util.equals(custPos, "")){

				}else{
					if(Util.equals(custPos, UtilConstants.lngeFlag.連帶保證人)){
						guaranteePlan = jointGuaranteePlan;
					}else if(Util.equals(custPos, UtilConstants.lngeFlag.ㄧ般保證人)){
						guaranteePlan = generalGuaranteePlan;
					}else{
						guaranteePlan = custPos;
					}
					guaranteeAmt = jsParam.optString("loanAmt");
				}
				jsParam.put("guaranteePlan", guaranteePlan);
				jsParam.put(ContractDocConstants.CtrTypeB.PLOAN_GUARANTEEAMT, guaranteeAmt);
			}

			//J-110-0335_10702_B1002 Web e-Loan對保契約計算總費用年百分率
			if (true) {
				String eloan_pc_spTerm_yRate="";
				Map<String, Object> map = new HashMap<String, Object>();
				List<String> list = new ArrayList<String>();
				Integer loan = 0;
				Integer bufferMonth = 0;
				cal_Type = 1 ;
				Integer limitMonth = 0;
				String rate = "";
				List<Double> multipleRate= new ArrayList<Double>();
				List<Integer> multipleRate_TimeStart= new ArrayList<Integer>();
				List<Integer> multipleRate_TimeEnd= new ArrayList<Integer>();
				List<Integer> costList = new ArrayList<Integer>();
				LMSUtil.addJsonToMap(map, jsParam);

				loan = Util.parseInt(jsParam.get("loanAmt"));
				L140S02A l140s02a = l140s02as.get(0);
				List<L140S02D> l140s02ds = clsService.findL140S02D_orderByPhase(l140m01a.getMainId(), l140s02a.getSeq(),
						"Y");
				boolean is_only_staff_and_pmRate_0 = this.get_CtrTypeB_Staff_RateType(l140s02ds.get(0).getRateType());
				BigDecimal l140s02dNowRate = BigDecimal.ZERO;
				
				//利率資訊
				if(is_only_staff_and_pmRate_0){
					List<Map<String, Object>> lrRate_list = misMislnratService.findMislnratByLRRate(l140s02ds.get(0).getRateType(), "TWD" );
					String rateValue = "";
					if(lrRate_list.size() > 0){
						rateValue = LMSUtil.pretty_numStr(CrsUtil.parseBigDecimal(MapUtils.getObject(lrRate_list.get(0), "LR_RATE")));
					}
					String bgnNum = "";
					String endNum = "";
					if(l140s02ds.size()>0){
						multipleRate_TimeStart.add((l140s02ds.get(0).getBgnNum()));
						multipleRate_TimeEnd.add((l140s02ds.get(l140s02ds.size()-1).getEndNum()));
						limitMonth=(l140s02ds.get(l140s02ds.size()-1).getEndNum());
//						if (Util.isEmpty(limitMonth)) {
//							limitMonth = Util.parseInt(jsParam.get("loanPeriod"));
//							multipleRate_TimeStart.add(0,1);
//							multipleRate_TimeEnd.add(0, limitMonth);
//						}
						l140s02dNowRate = l140s02ds.get(0).getNowRate() == null ? BigDecimal.ZERO : l140s02ds.get(0).getNowRate().setScale(2, BigDecimal.ROUND_DOWN);
					}
					multipleRate.add(Util.parseDouble(rateValue));
				}else{
					if(l140s02ds.size()>0){
						for (L140S02D l140s02d : l140s02ds) {
							Integer phase = l140s02d.getPhase();
							String l140s02dOid = l140s02d.getOid();
							l140s02dNowRate = l140s02d.getNowRate() == null ? BigDecimal.ZERO : l140s02d.getNowRate().setScale(2, BigDecimal.ROUND_DOWN);
							BigDecimal l140s02d_rateUser = l140s02d.getRateUser() == null ? BigDecimal.ZERO : l140s02d.getRateUser();
							if (phase == 1) {
								BigDecimal rateRange1Param03 = CapMath.getBigDecimal(
										MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_FIXED_RATE , ""));
								BigDecimal rateRange1Param07 = CapMath.getBigDecimal(
										MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_RESULTRATE , ""));
								if(CrsUtil.RATE_TYPE_01.equals(l140s02d.getRateType()) && Util.equals("C01", l140s02d.getRateUserType())){
//									multipleRate.add(l140s02d_rateUser.doubleValue());
									multipleRate.add(rateRange1Param03.doubleValue());
								}else{
//									multipleRate.add(l140s02dNowRate.doubleValue());
									multipleRate.add(rateRange1Param07.doubleValue());
								}
								multipleRate_TimeStart.add(l140s02d.getBgnNum());
								multipleRate_TimeEnd.add(l140s02d.getEndNum());
							} else if (phase == 2) {
								BigDecimal rateRange2Param03 = CapMath.getBigDecimal(
										MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2_FIXED_RATE , ""));
								BigDecimal rateRange2Param07 = CapMath.getBigDecimal(
										MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2_RESULTRATE , ""));
								if(CrsUtil.RATE_TYPE_01.equals(l140s02d.getRateType()) && Util.equals("C01", l140s02d.getRateUserType())){
//									multipleRate.add(l140s02d_rateUser.doubleValue());
									multipleRate.add(rateRange2Param03.doubleValue());
								}else{
//									multipleRate.add(l140s02dNowRate.doubleValue());
									multipleRate.add(rateRange2Param07.doubleValue());
								}
								multipleRate_TimeStart.add(l140s02d.getBgnNum());
								multipleRate_TimeEnd.add(l140s02d.getEndNum());
							}
						}
						limitMonth=(l140s02ds.get(l140s02ds.size()-1).getEndNum());
					}
				}
				for (L140M01R l140m01r : l140m01r_list) {
					if(l140m01r.getFeeAmt().compareTo(BigDecimal.ZERO)>0){
						costList.add(l140m01r.getFeeAmt().intValue());
					}
				}
				try {
					if (Util.isNotEmpty(limitMonth)) {
						rate = clsService.calculateRate(loan,limitMonth,bufferMonth,cal_Type,multipleRate,multipleRate_TimeStart,multipleRate_TimeEnd,costList);
					}

					if(Util.isNotEmpty(rate) && list.size()==0){
						eloan_pc_spTerm_yRate =rate;
					}
					//J-112-0502  調整短期循環(續約)/中期循環(續約)總費用百分率
					String prodKind = l140s02a.getProdKind();
					if(Util.isEmpty(rate) && ("02".equals(prodKind) || "68".equals(prodKind))){
						eloan_pc_spTerm_yRate = l140s02dNowRate.toString();
					}
				}
				catch(Exception ex){
					LOGGER.error("calculateRate@"+Thread.currentThread().getStackTrace()[1].getMethodName()+" input{"
							+"loan:"+loan
							+", limitMonth:"+limitMonth
							+", bufferMonth:"+bufferMonth
							+", cal_Type:"+cal_Type
							+", multipleRate:"+multipleRate
							+", multipleRate_TimeStart:"+multipleRate_TimeStart
							+", multipleRate_TimeEnd:"+multipleRate_TimeEnd
							+", costList:"+costList
							+"}");
					LOGGER.error(StrUtils.getStackTrace(ex));
					eloan_pc_spTerm_yRate ="";
				}
				jsParam.put("annualPercentageRate", eloan_pc_spTerm_yRate);
			}

			//fix loanAmt如果json格式是integer產報表會跳錯
			jsParam.put("loanAmt", Util.trim(jsParam.getString("loanAmt")));

			//目前房貸對保沒有傳pLoan代償科目，所以暫不開放
			if (true) {
//				MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
//				String ssoUnitNo = user.getSsoUnitNo();
//				String unitNo = user.getUnitNo();
//				Boolean active_repayment = false;
//				Map<String, String> map= clsService.get_codeType_codeDesc2WithOrder("LMS_FUNC_ON_FLAG","zh_TW");
//				if(clsService.is_function_on_codetype("cls_repayment_pilot")){
//					if (map.containsKey("cls_repayment_pilot")) {
//						String val = Util.trim(map.get("cls_repayment_pilot"));
//						if(val.contains(unitNo)){
//							active_repayment = true;
//						}
//					}
//				}
//				if(clsService.is_function_on_codetype("cls_repayment_all")){
//					active_repayment = true;
//				}
//				if (active_repayment) {
//					JSONArray repaymentList = new JSONArray();
//					String isRepayment = UtilConstants.DEFAULT.否;//預設代償塞否
//					if (l140s02as.size() > 0) {
//						List<L140S02H> l140s02hs = this.findByMainIdSeq(l140s02as.get(0).getMainId(), l140s02as.get(0).getSeq());
//						if (l140s02hs.size() > 0) {
//							for (L140S02H l140s02h:l140s02hs) {
//								JSONObject compensation = new JSONObject();
//								String repaymentProductName = "";
//								List<CodeType> jcicCodeTypes = codeTypeService.findByCodeTypeList("repaymentProductId");
//								boolean isMatch = false;
//								for (CodeType jcicitem : jcicCodeTypes) {
//									if (Util.isNotEmpty(l140s02h.getRepaymentProductType())) {
//										if (l140s02h.getRepaymentProductType().equals(jcicitem.getCodeValue())) {
//											isMatch = true;
//											repaymentProductName = jcicitem.getCodeDesc();
//										}
//									}
//								}
//								if (Util.equals(l140s02h.getRepaymentProductType(),"信用卡")) {
//									isMatch = true;
//								}
//								//貸款符合 H，才允許新增代償列表
//								if(isMatch){
//									compensation.put("bankCode",l140s02h.getBankNo());
//									compensation.put("bankName",l140s02h.getBankName());
//									compensation.put("repaymentProductType",l140s02h.getRepaymentProductType() + repaymentProductName);
//									compensation.put("originalAmt",l140s02h.getOriginalAmt());
//									repaymentList.add(compensation);
//								}
//							}
//							if (repaymentList.size()>0) {
//								isRepayment = UtilConstants.DEFAULT.是;
//								jsParam.put("repaymentList", repaymentList);
//							}
//						}
//						jsParam.put("isRepayment", isRepayment);
//					}
//				}
			}
			
			c340m01c.setJsonData(jsParam.toString());
			clsService.save(c340m01c);
		}
		//檢查房貸線上對保僅開放6R、M2、N2、MR
		//J-113-0283 新增利率代碼 7D、7C
		if (c340m01bs.size() > 0) {
			List<L140S02A> l140s02as = clsService.findL140S02A(l140m01a);
			if (l140s02as.size() > 0) {
				List<L140S02D> l140s02ds = clsService.findL140S02D_orderByPhase(l140m01a.getMainId(), l140s02as.get(0).getSeq(),
						"Y");
				if (l140s02ds.size() > 0) {
					if (!this.get_CtrTypeB_pass_RateType(l140s02ds.get(0).getRateType())) {
						throw new CapMessageException("若需採線上對保，利率應選擇6R、M2、N2、MR、7C、7D", this.getClass());
					}
				}
			}
		}

		this.saveMeta(c340m01a);
	}

	@Override
	public void init_C340RelateCtrTypeS(C340M01A c340m01a, String caseMainId,
			String tabMainIds) throws CapMessageException {

		L140M01A l140m01a = clsService.findL140M01A_mainId(tabMainIds);
		if (l140m01a != null) {
			// L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);
			
			c340m01a.setCustName(l140m01a.getCustName());
			c340m01a.setContrNumber(l140m01a.getCntrNo());
			c340m01a.setContrPartyNm(l140m01a.getCustName());

			c340m01a.setPloanCtrNo(build_ploanCtrNo(c340m01a.getCtrType(), l140m01a.getCntrNo()));
			
			C340M01B c340m01b = new C340M01B();
			c340m01b.setMainId(c340m01a.getMainId());
			c340m01b.setTabMainId(l140m01a.getMainId());
			c340m01b.setCntrNo(l140m01a.getCntrNo());
			clsService.save(c340m01b);

			// 借保人檔
			List<L140S01A> l140s01as = clsService.findL140S01A(l140m01a);
			if (l140s01as == null || l140s01as.size() == 0) {
			} else if (l140s01as.size() == 1) {
				// 關係人
				L140S01A l140s01a = l140s01as.get(0);
				c340m01a.setPloanPosSId(l140s01a.getCustId());
			} else {
				throw new CapMessageException("借保人超過1位，不適用線上對保", this.getClass());
			}

			//c340m01c.setJsonData(jsParam.toString());
			//clsService.save(c340m01c);
		}

		this.saveMeta(c340m01a);
	}
	
	@Override
	public void init_C340RelateCtrTypeL(C340M01A c340m01a, String caseMainId, String tabMainIds) throws CapMessageException{
		init_C340RelateCtrTypeA(c340m01a, caseMainId, tabMainIds);
	}
	
	@Override
	public C340M01A sys_create_C340RelateCtrTypeL(String tabMainId, String c340m01a_docStatus, String creator, String txCode) throws CapMessageException{
		L140M01A l140m01a = clsService.findL140M01A_mainId(tabMainId);
		L120M01A l120m01a = clsService.findL120M01A_by_L140M01A_mainId(tabMainId);
		
		String ctrType = ContractDocConstants.C340M01A_CtrType.Type_L;
		String custId = l140m01a.getCustId();
		String dupNo = l140m01a.getDupNo();
		
		C340M01A c340m01a = new C340M01A();
		c340m01a.setMainId(IDGenerator.getUUID());
		c340m01a.setTypCd(UtilConstants.Casedoc.typCd.DBU);
		c340m01a.setOwnBrId(l140m01a.getOwnBrId());
		c340m01a.setDocStatus(c340m01a_docStatus);

		c340m01a.setTxCode(txCode);
		// UPGRADE: 待確認，URL是否正確
		// c340m01a.setDocURL(CLS3401M06Page.class.getAnnotation(MountPath.class).path());
		c340m01a.setDocURL("");
		c340m01a.setDeletedTime(CapDate.getCurrentTimestamp());

		c340m01a.setCustId(custId);
		c340m01a.setDupNo(dupNo);
		c340m01a.setCreator(creator);
		c340m01a.setCreateTime(CapDate.getCurrentTimestamp());
		c340m01a.setCtrType(ctrType);
		c340m01a.setRptId(ClsUtility.get_latest_rptId_C340M01A_CtrType(ctrType));
		c340m01a.setContrNumber("");
		c340m01a.setContrPartyNm("");


		if (l120m01a != null) {
			c340m01a.setCaseMainId(l120m01a.getMainId());
			c340m01a.setCaseNo(Util.toSemiCharString(l120m01a.getCaseNo()));
		}

		init_C340RelateCtrTypeL(c340m01a, l120m01a.getMainId(), tabMainId);
		return c340m01a;
	}
	
	@Override
	public C340M01A init_deactive_C340_ctrType_C(String c340m01a_mainId, C160S01D c160s01d, C122M01A c122m01a, C122M01E c122m01e, String docUrl){
		String idDup = Util.trim(c160s01d.getCustId());
		String cntrNo = Util.trim(c160s01d.getCntrNo());
		String ploanPlan = Util.trim(c122m01a.getPloanPlan());
		
		if(Util.isEmpty(idDup)|| Util.isEmpty(cntrNo)){
			return null;
			
		}
		if(Util.isEmpty(ploanPlan)){
			return null;
		}
		if(Util.equals(ClsUtility.get_ploanPlan_ChinaSteelCorp_HQ(), c122m01a.getPloanPlan()) && c122m01e==null){
			return null;
		}
		if(Util.isEmpty(Util.trim(c122m01a.getCrossMarket())) ){
			return null;
		}
		
		Map<String, String> ctrParam_from_C122M01A = ClsUtility.get_ctrParam_from_C122M01A(c122m01a);
		String grpCntrNo = Util.trim(MapUtils.getString(ctrParam_from_C122M01A, "grpCntrNo"));
		Date ctrCheckDate = CapDate.parseDate(MapUtils.getString(ctrParam_from_C122M01A, "ctrCheckDate"));

		if(ctrCheckDate==null){
			return null;
		}
		if( c122m01a.getStkhQueryEJTs()==null ){
			if(Util.equals("C020", c122m01a.getPloanPlan()) && Util.equals(ClsUtility.CSC_GRPCNTRNO_PERIOD_046, grpCntrNo)){
				/*一開始只有編到 C019
				  C020 是後來才加的, 導致  (1)React前端 , (2)SLMS-00142 中鋼消貸進件資料發查利害關係人 都沒有把 C020 當成中鋼子公司
				*/
			}else{
				return null;
			}
		}
		if(ClsUtility.get_ploanPlan_ChinaSteelCorp_subsidinary().contains(ploanPlan)){
			//中鋼子公司進件的資料, 有發現 缺職工編號, 放款帳號 => 這2個欄位，之後可在 c160s01d 再去補強
			/*
			C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(c122m01a.getMainId(), c122m01a.getCustId(), c122m01a.getDupNo());
			if(c120m01a==null){
				return null;
			}else{
				String c120s01a_dpAcct = "";
				C120S01A c120s01a = clsService.findC120S01A(c120m01a);
				if(c120s01a!=null){
					c120s01a_dpAcct = Util.trim(c120s01a.getDpAcct());
				}				
				if(Util.isEmpty(c120s01a_dpAcct)){
					return null;
				}
			}
			*/
		}
		
		
		String brNo = StringUtils.substring(cntrNo, 0, 3);
		String custId = CrsUtil.get_custId_from_custKey(idDup);
		String dupNo = CrsUtil.get_dupNo_from_custKey(idDup);
		String custName = c160s01d.getCustName();
		String txCode = "338100"; //比照 消金契約書 的  txCode 【1】select * from com.BELSPGM where pgmcode=338100 【2】EloanHome_zh_TW.properties 查找 menu.338100
		//~~~~~~~~~~~~~~~
		C340M01A c340m01a = new C340M01A();
		c340m01a.setMainId(c340m01a_mainId);		
		c340m01a.setTypCd(UtilConstants.Casedoc.typCd.DBU);
		c340m01a.setOwnBrId(brNo);
		c340m01a.setDocStatus(CreditDocStatusEnum.海外_已核准.getCode());
		c340m01a.setTxCode(txCode);
		c340m01a.setDocURL(docUrl);		
		c340m01a.setCustId(custId);
		c340m01a.setDupNo(dupNo);
		c340m01a.setCustName(custName);
		c340m01a.setCreator("system");
		c340m01a.setCreateTime(CapDate.getCurrentTimestamp());
		c340m01a.setCtrType(ContractDocConstants.C340M01A_CtrType.Type_C);
		c340m01a.setRptId("");
		c340m01a.setContrNumber(cntrNo);
		c340m01a.setContrPartyNm(custName);
		
		if(true){
			c340m01a.setCaseMainId("");
			c340m01a.setCaseNo(grpCntrNo+"-"+ploanPlan);
		}
		if(true){
			c340m01a.setDeletedTime(CapDate.getCurrentTimestamp());
		}
		c340m01a.setPloanCtrNo(build_ploanCtrNo_ctrTypeC( cntrNo, ploanPlan));
		c340m01a.setPloanCtrStatus("");
		c340m01a.setPloanCtrBegDate(c160s01d.getLnFromDate());
		c340m01a.setPloanCtrEndDate(c160s01d.getLnEndDate());
		c340m01a.setPloanNotifyT1TS(c340m01a.getCreateTime());
		c340m01a.setPloanCtrSignTimeM(c122m01a.getAgreeQueryEJTs());
		c340m01a.setPloanBorrowerIPAddr(c122m01a.getAgreeQueryEJIp());
		c340m01a.setCtrCheckDate(ctrCheckDate);
		c340m01a.setC160s01d_oid(c160s01d.getOid());
		//~~~~~~~~~~~~~~~~~~~~~~~~~
		clsService.save(c340m01a);
		
		return c340m01a;		
	}
	
	@Override
	public C340M01A save_C340_ctrType_C(C340M01A c340m01a, byte[] pdf_contract, byte[] pdf_deductWageAgrmt){		
		if(true){
			String contentType = "application/pdf";								
			String attchFileExt = "pdf";
			if(true){
				String fieldId = "ploan_ctr"; //和 mega-gw 裡的 ploan_ctr 相同
				saveDocFile(c340m01a, pdf_contract, fieldId, contentType, "線上對保契約書", attchFileExt);
			}	
			if(true){
				String fieldId = "deductWageAgrmt";
				saveDocFile(c340m01a, pdf_deductWageAgrmt, fieldId, contentType, "扣薪扣款同意書", attchFileExt);
			}
		}
		
		if(true){
			c340m01a.setApprover("system");
			c340m01a.setApproveTime(c340m01a.getCreateTime());
			c340m01a.setDeletedTime(null);
			clsService.daoSave(c340m01a);
		}
		return c340m01a;
	}
	
	private void saveDocFile(C340M01A meta, byte[] bytes, String fieldId,
			String contentType, String attchFileName, String attchFileExt) {
		DocFile docFile = new DocFile();
		docFile.setBranchId(meta.getOwnBrId());
		docFile.setContentType(contentType);
		docFile.setMainId(meta.getMainId());
		docFile.setPid(null);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId(fieldId);
		docFile.setDeletedTime(null);
		docFile.setSrcFileName(attchFileName + "." + attchFileExt);
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId("LMS");
		docFile.setFileSize(bytes.length);
		docFile.setData(bytes);
		docFile.setFileDesc("");		
		docFile.setFlag("");
		docFileService.save(docFile);
	}
	
	@Override
	public byte[] get_C340_ctrType_C_merge_Nto1_pdf(C340M01A c340m01a){
		byte[] result = null;
		List<InputStream> inputPdfList = new ArrayList<InputStream>();
		if(true){
			String[] fieldId_arr = new String[]{"ploan_ctr", "deductWageAgrmt"};
			for(String fieldId :fieldId_arr){
				List<DocFile> docFile_list = docFileService.findByIDAndName(c340m01a.getMainId(), fieldId, "");
				if(docFile_list.size()==0){
					return null;
				}
				
				for(DocFile tmp_docFile : docFile_list){
					DocFile docFile = tmp_docFile;
					if(docFile.getData()==null){
						docFile = docFileService.read(docFile.getOid());
					}
					inputPdfList.add(new ByteArrayInputStream(docFile.getData()));
				}		
			}
		}
		if(inputPdfList.size()>0){
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			try{
				mergePdfFiles(inputPdfList, outputStream);
			}catch(Exception e){
				LOGGER.error(StrUtils.getStackTrace(e));
			}
			if(outputStream != null){
				result =  outputStream.toByteArray();
			}
		}		
		return result;
	}
	
	/**
	 * ref  https://stackoverflow.com/questions/3585329/
	 */
	private void mergePdfFiles(List<InputStream> inputPdfList,
	        OutputStream outputStream) throws Exception{
	    //Create document and pdfReader objects.
	    Document document = new Document();
	    List<PdfReader> readers = 
	            new ArrayList<PdfReader>();
	    int totalPages = 0;

	    //Create pdf Iterator object using inputPdfList.
	    Iterator<InputStream> pdfIterator = 
	            inputPdfList.iterator();

	    // Create reader list for the input pdf files.
	    while (pdfIterator.hasNext()) {
	            InputStream pdf = pdfIterator.next();
	            PdfReader pdfReader = new PdfReader(pdf);
	            readers.add(pdfReader);
	            totalPages = totalPages + pdfReader.getNumberOfPages();
	    }

	    // Create writer for the outputStream
	    PdfWriter writer = PdfWriter.getInstance(document, outputStream);

	    //Open document.
	    document.open();

	    //Contain the pdf data.
	    PdfContentByte pageContentByte = writer.getDirectContent();

	    PdfImportedPage pdfImportedPage;
	    int currentPdfReaderPage = 1;
	    Iterator<PdfReader> iteratorPDFReader = readers.iterator();

	    // Iterate and process the reader list.
	    while (iteratorPDFReader.hasNext()) {
	            PdfReader pdfReader = iteratorPDFReader.next();
	            //Create page and add content.
	            while (currentPdfReaderPage <= pdfReader.getNumberOfPages()) {
	                  document.newPage();
	                  pdfImportedPage = writer.getImportedPage(
	                          pdfReader,currentPdfReaderPage);
	                  pageContentByte.addTemplate(pdfImportedPage, 0, 0);
	                  currentPdfReaderPage++;
	            }
	            currentPdfReaderPage = 1;
	    }

	    //Close document and outputStream.
	    outputStream.flush();
	    document.close();
	    outputStream.close();
	}
	
	@Override
	public String get_ploanPosSId_name(C340M01A meta){
		String ploanPosSId = Util.trim(meta.getPloanPosSId());
		
		List<C340M01B> c340m01b_list = clsService.findC340M01B(meta.getMainId());

		for(C340M01B c340m01b : c340m01b_list){			 
			L140M01A l140m01a = clsService.findL140M01A_mainId(c340m01b.getTabMainId());
			if(l140m01a!=null){
				for(L140S01A l140s01a : clsService.findL140S01A(l140m01a)){
					if(Util.equals(l140s01a.getCustId(), ploanPosSId)){
						return Util.trim(l140s01a.getCustName());
					}
				}
			}
		}
		return "NameFor_"+ploanPosSId;
	}
	
	@Override
	public void flowAction(C340M01A meta, boolean setResult, String resultType) throws Throwable {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		try {
			FlowInstance inst = flowService.createQuery().id(meta.getOid()).query();
			if (inst == null) {
				if(Util.equals(resultType,"autoCreate")){
					inst = flowService.start("CLS3401Flow", meta.getOid(), "system", user.getUnitNo());
				}
				else{
					inst = flowService.start("CLS3401Flow", meta.getOid(), user.getUserId(), user.getUnitNo());
				}
			}

			if (setResult) {
				inst.setAttribute("result", resultType);
			}

			inst.next();
		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}
	
	@Override
	public String getTipsOfComparingLoanAmountAndApprovedQuota(String c340m01a_mainId, String chineseLoanAmount){
		
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS3401V01Page.class);
		
		String str = "";
		List<C340M01B> c340m01bList = this.c340m01bDao.findByMainId(c340m01a_mainId);
		
		if(c340m01bList.size() == 1){
			String l140m01a_mainId = c340m01bList.get(0).getTabMainId();
			L140M01A l140m01a = clsService.findL140M01A_mainId(l140m01a_mainId);
			
			if(l140m01a == null){//可能是 user 把簽報書刪除了
				return prop.getProperty("C340M01B.tips.message.quotaDetailListInvalid");//此份額度明細表無效，請確認！
			}
			
			String chineseCurrentApplyAmount = CapCommonUtil.toChineseUpperAmount(LMSUtil.pretty_numStr(l140m01a.getCurrentApplyAmt()), true);
			//核貸額度及契約書借款額度不一致，請確認是否契約書借款額度有誤！
			str = !chineseLoanAmount.equals(chineseCurrentApplyAmount) ? prop.getProperty("C340M01B.tips.message.loanAmountNotSameAsCurrentApplyAmount") : "";
		}
		
		return str;
	}

	@Override
	public void init_C340M01ARelateCtrTypeA(C340M01A c340m01a,String custId,String dupNo,String ctrType,String caseMainId,String tabMainId,String UnitNo,String UserId,String txCode,String docUrl) throws CapMessageException {
		L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);

		c340m01a.setMainId(IDGenerator.getUUID());
		c340m01a.setTypCd(UtilConstants.Casedoc.typCd.DBU);
		c340m01a.setOwnBrId(UnitNo);
		c340m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());

		c340m01a.setTxCode(txCode);
		c340m01a.setDocURL(docUrl);
		c340m01a.setDeletedTime(CapDate.getCurrentTimestamp());

		c340m01a.setCustId(custId);
		c340m01a.setDupNo(dupNo);
		c340m01a.setCreator(UserId);
		c340m01a.setCreateTime(CapDate.getCurrentTimestamp());
		c340m01a.setCtrType(ctrType);
		c340m01a.setRptId(get_latest_rptId(ctrType));
		c340m01a.setContrNumber("");
		c340m01a.setContrPartyNm("");


		if (l120m01a != null) {
			c340m01a.setCaseMainId(caseMainId);
			c340m01a.setCaseNo(Util.toSemiCharString(l120m01a.getCaseNo()));
		}
	}

	@Override
	public void init_C340M01ARelateCtrTypeB(C340M01A c340m01a,String custId,String dupNo,String ctrType,String caseMainId,String tabMainId,String UnitNo,String UserId,String txCode,String docUrl) throws CapMessageException {
		L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);

		c340m01a.setMainId(IDGenerator.getUUID());
		c340m01a.setTypCd(UtilConstants.Casedoc.typCd.DBU);
		c340m01a.setOwnBrId(UnitNo);
		c340m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());

		c340m01a.setTxCode(txCode);
		c340m01a.setDocURL(docUrl);
		c340m01a.setDeletedTime(CapDate.getCurrentTimestamp());

		c340m01a.setCustId(custId);
		c340m01a.setDupNo(dupNo);
		c340m01a.setCreator(UserId);
		c340m01a.setCreateTime(CapDate.getCurrentTimestamp());
		c340m01a.setCtrType(ctrType);
		c340m01a.setRptId(get_latest_rptId(ctrType));
		c340m01a.setContrNumber("");
		c340m01a.setContrPartyNm("");


		if (l120m01a != null) {
			c340m01a.setCaseMainId(caseMainId);
			c340m01a.setCaseNo(Util.toSemiCharString(l120m01a.getCaseNo()));
		}
	}

	@Override
	public String get_latest_rptId(String ctrType){
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_A)){
			if(clsService.is_function_on_codetype("CtrTypeA_V202401")){
				return ContractDocConstants.C340M01A_RptId.CtrTypeA_V202401;
			}else if(clsService.is_function_on_codetype("CtrTypeA_V202307")){
				return ContractDocConstants.C340M01A_RptId.CtrTypeA_V202307;
			}else if(clsService.is_function_on_codetype("CtrTypeA_V202304")){
				return ContractDocConstants.C340M01A_RptId.CtrTypeA_V202304;
			}else if(clsService.is_function_on_codetype("CtrTypeA_V202212")){
				return ContractDocConstants.C340M01A_RptId.CtrTypeA_V202212;
			}else if(clsService.is_function_on_codetype("CtrTypeA_V202209")){
				return ContractDocConstants.C340M01A_RptId.CtrTypeA_V202209;
			}
			else if(clsService.is_function_on_codetype("CtrTypeA_V202206")){
				return ContractDocConstants.C340M01A_RptId.CtrTypeA_V202206;
			}else if(clsService.is_function_on_codetype("CtrTypeA_V202011")){
				return ContractDocConstants.C340M01A_RptId.CtrTypeA_V202011;
			}
			return ContractDocConstants.C340M01A_RptId.CtrTypeA_V202008;
		}
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_B)){
			if(clsService.is_function_on_codetype("CtrTypeB_V202401")){
				return ContractDocConstants.C340M01A_RptId.CtrTypeB_V202401;
			}else if(clsService.is_function_on_codetype("CtrTypeB_V202309")){
				return ContractDocConstants.C340M01A_RptId.CtrTypeB_V202309;
			}
			return ContractDocConstants.C340M01A_RptId.CtrTypeB_V202309;
		}
		return "";
	}

	@Override
	public void check_CtrTypeA(C340M01A c340m01a, String userId,String decisionExpr, Properties prop_cls3401m02, Properties prop_abstractEloanPage) throws Throwable {
		if (CreditDocStatusEnum.海外_編製中.getCode().equals(c340m01a.getDocStatus())) {
			int cnt_actNo = 0;
			List<Object> loanAcct_list = new ArrayList<Object>();
			List<C340M01C> c340m01c_list = clsService.findC340M01C(c340m01a.getMainId());
			if(c340m01c_list.size()>0){
				C340M01C c340m01c = c340m01c_list.get(0);
				String jsonData = Util.trim(c340m01c.getJsonData());
				JSONObject jsonObject = DataParse.toJSON(jsonData);

				loanAcct_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsonObject, ContractDocConstants.CtrTypeA.PLOAN_ACCTNO_LIST);
				cnt_actNo = loanAcct_list.size();
			}
			//
			if((cnt_actNo==0||(cnt_actNo==1 && Util.equals("[]", StringUtils.join(loanAcct_list, "")))) && clsService.is_function_on_codetype("c340_chkTypeA_actNo")){
				boolean active_ACH = false;

				MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
				String ssoUnitNo = user.getSsoUnitNo();
				String unitNo = user.getUnitNo();
				if(Util.equals("900", ssoUnitNo) || Util.equals("943", ssoUnitNo)){
					active_ACH = true;
				}else{
					Map<String, String> map= clsService.get_codeType_codeDesc2WithOrder("LMS_FUNC_ON_FLAG","zh_TW");
					if(clsService.is_function_on_codetype("cls_ACH_pilot")){
						if (map.containsKey("cls_ACH_pilot")) {
							String val = Util.trim(map.get("cls_ACH_pilot"));
							if(val.contains(unitNo)){
								active_ACH = true;
							}
						}
					}
					if(clsService.is_function_on_codetype("cls_ACH_all")){
						active_ACH = true;
					}
				}

				if( !active_ACH) {
					throw new CapMessageException("此客戶「無本行存款帳號」或「僅有數位存款帳號但未提升權限」，無法進行線上對保", this.getClass());
				}
                //J-111-0227_10702_B1001 pLoan開放他行扣放款帳號
                LOGGER.debug("此客戶「無本行存款帳號」或「僅有數位存款帳號但未提升權限」，開放走他行放款帳號");
			}

			if(true){
				String ploanCtrNo = Util.trim(c340m01a.getPloanCtrNo());
				if(this.findC340M01A_ploanCtrNo_ctrType(ploanCtrNo, c340m01a.getCtrType()).size()>1){
					throw new CapMessageException(prop_cls3401m02.getProperty("C340M01A.ploanCtrNo")+"(" + ploanCtrNo + ")重複，請刪除此份契約，重新產出。", this.getClass());
				}
			}
			//~~~~~~~~~~
			String cntrNo = "";
			if(Util.isNotEmpty(c340m01a.getC340m01bs()) && c340m01a.getC340m01bs().size()>0){
				cntrNo = c340m01a.getC340m01bs().get(0).getCntrNo();
			}
			else{
				L140M01A l140m01a = clsService.findL140M01A_l120m01aMainId(c340m01a.getCaseMainId()).get(0);
				cntrNo = l140m01a.getCntrNo();
			}
			List<C340M01A> flowingC340M01A = this.findFlowingC340M01A(cntrNo, c340m01a.getCtrType());
			if (flowingC340M01A.size() > 0) {
				C340M01A pendingCase = flowingC340M01A.get(0);
				throw new CapMessageException("額度序號" + cntrNo
						+ "（"+prop_cls3401m02.getProperty("C340M01A.ploanCtrNo")+"：" +pendingCase.getPloanCtrNo()
						+ (Util.equals(CreditDocStatusEnum.海外_待覆核.getCode(), pendingCase.getDocStatus())?"，e-Loan文件狀態："+prop_abstractEloanPage.getProperty("docStatus." + pendingCase.getDocStatus()):"")
						+"）"
						+"尚未完成線上對保程序。"+"<br/>"
						+"因此本案契約（" +c340m01a.getPloanCtrNo()+"）請先作廢前一份對保契約書後再產生對保契約書。", this.getClass());
			}
			//J-111-0524 因報送聯徵，不允許同額度序號多次對保，需將其他對保契約作廢才准新增對保契約書
			List<C340M01A> flowingC340M01AComplete = this.findFlowingC340M01AComplete(cntrNo, c340m01a.getCtrType());
			if (flowingC340M01AComplete.size() > 0) {
				C340M01A pendingCase = flowingC340M01AComplete.get(0);
				throw new CapMessageException("額度序號" + cntrNo
						+ "（"+prop_cls3401m02.getProperty("C340M01A.ploanCtrNo")+"：" +pendingCase.getPloanCtrNo()
						+ (Util.equals(CreditDocStatusEnum.海外_待覆核.getCode(), pendingCase.getDocStatus())?"，e-Loan文件狀態："+prop_abstractEloanPage.getProperty("docStatus." + pendingCase.getDocStatus()):"")
						+"）"
						+"已完成線上對保程序。"+"<br/>"
						+"因此本案契約（" +c340m01a.getPloanCtrNo()+"）請先作廢前一份對保契約書後再產生對保契約書。", this.getClass());
			}
		}

		if (Util.isNotEmpty(decisionExpr)) {
			if ("ok".equals(decisionExpr)) {
				if (Util.equals(userId, c340m01a.getUpdater())) {
					// EFD0053' 覆核人員不可與「經辦人員或其它覆核人員」為同一人
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
				}
			}
		}
	}

	@Override
	public void check_CtrTypeB(C340M01A c340m01a, String userId,String decisionExpr, Properties prop_cls3401m02, Properties prop_abstractEloanPage) throws Throwable {
		if (CreditDocStatusEnum.海外_編製中.getCode().equals(c340m01a.getDocStatus())) {
			int cnt_actNo = 0;
			List<Object> loanAcct_list = new ArrayList<Object>();
			C340M01C c340m01c = clsService.findC340M01C(c340m01a.getMainId(), ContractDocConstants.C340M01C_ItemType.TYPE_0);
			List<Object> collateralContractTerms_list = new ArrayList<Object>();
			if(c340m01c != null){
				String jsonData = Util.trim(c340m01c.getJsonData());
				JSONObject jsonObject = DataParse.toJSON(jsonData);

				loanAcct_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsonObject, ContractDocConstants.CtrTypeB.PLOAN_ACCTNO_LIST);
				cnt_actNo = loanAcct_list.size();
				
				//J-113-0050
				collateralContractTerms_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsonObject, ContractDocConstants.CtrTypeB.COLLATERALCONTRACTTERMS);
			
			}
			
			//J-113-0050  約據-擔保品使用狀況承諾書若有選到三~六選項之一，不得做線上對保
			if(collateralContractTerms_list.contains("3") || collateralContractTerms_list.contains("4")
					||collateralContractTerms_list.contains("5") || collateralContractTerms_list.contains("6")){
				throw new CapMessageException("本案有未辦所有權總登記或保存登記之情事，不開放線上對保，請採行原線下對保方式作業。", this.getClass());
			}
			
			//
			if((cnt_actNo==0||(cnt_actNo==1 && Util.equals("[]", StringUtils.join(loanAcct_list, "")))) && clsService.is_function_on_codetype("c340_chkTypeA_actNo")){
				boolean active_ACH = false;

				MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
				String ssoUnitNo = user.getSsoUnitNo();
				String unitNo = user.getUnitNo();
				//房貸待確認是否需要他行對保
//				if(Util.equals("900", ssoUnitNo) || Util.equals("943", ssoUnitNo)){
//					active_ACH = true;
//				}else{
//					Map<String, String> map= clsService.get_codeType_codeDesc2WithOrder("LMS_FUNC_ON_FLAG","zh_TW");
//					if(clsService.is_function_on_codetype("cls_ACH_pilot")){
//						if (map.containsKey("cls_ACH_pilot")) {
//							String val = Util.trim(map.get("cls_ACH_pilot"));
//							if(val.contains(unitNo)){
//								active_ACH = true;
//							}
//						}
//					}
//					if(clsService.is_function_on_codetype("cls_ACH_all")){
//						active_ACH = true;
//					}
//				}

				if( !active_ACH) {
					throw new CapMessageException("此客戶「無本行存款帳號」或「僅有數位存款帳號但未提升權限」，無法進行線上對保", this.getClass());
				}
				//J-111-0227_10702_B1001 pLoan開放他行扣放款帳號
				LOGGER.debug("此客戶「無本行存款帳號」或「僅有數位存款帳號但未提升權限」，開放走他行放款帳號");
			}

			if(true){
				String ploanCtrNo = Util.trim(c340m01a.getPloanCtrNo());
				if(this.findC340M01A_ploanCtrNo_ctrType(ploanCtrNo, c340m01a.getCtrType()).size()>1){
					throw new CapMessageException(prop_cls3401m02.getProperty("C340M01A.ploanCtrNo")+"(" + ploanCtrNo + ")重複，請刪除此份契約，重新產出。", this.getClass());
				}
			}
			//~~~~~~~~~~
			String cntrNo = "";
			if(Util.isNotEmpty(c340m01a.getC340m01bs()) && c340m01a.getC340m01bs().size()>0){
				cntrNo = c340m01a.getC340m01bs().get(0).getCntrNo();
			}
			else{
				L140M01A l140m01a = clsService.findL140M01A_l120m01aMainId(c340m01a.getCaseMainId()).get(0);
				cntrNo = l140m01a.getCntrNo();
			}
			List<C340M01A> flowingC340M01A = this.findFlowingC340M01A(cntrNo, c340m01a.getCtrType());
			if (flowingC340M01A.size() > 0) {
				C340M01A pendingCase = flowingC340M01A.get(0);
				throw new CapMessageException("額度序號" + cntrNo
						+ "（"+prop_cls3401m02.getProperty("C340M01A.ploanCtrNo")+"：" +pendingCase.getPloanCtrNo()
						+ (Util.equals(CreditDocStatusEnum.海外_待覆核.getCode(), pendingCase.getDocStatus())?"，e-Loan文件狀態："+prop_abstractEloanPage.getProperty("docStatus." + pendingCase.getDocStatus()):"")
						+"）"
						+"尚未完成線上對保程序。"+"<br/>"
						+"因此本案契約（" +c340m01a.getPloanCtrNo()+"）請先作廢前一份對保契約書後再產生對保契約書。", this.getClass());
			}
			//J-111-0524 因報送聯徵，不允許同額度序號多次對保，需將其他對保契約作廢才准新增對保契約書
			List<C340M01A> flowingC340M01AComplete = this.findFlowingC340M01AComplete(cntrNo, c340m01a.getCtrType());
			if (flowingC340M01AComplete.size() > 0) {
				C340M01A pendingCase = flowingC340M01AComplete.get(0);
				throw new CapMessageException("額度序號" + cntrNo
						+ "（"+prop_cls3401m02.getProperty("C340M01A.ploanCtrNo")+"：" +pendingCase.getPloanCtrNo()
						+ (Util.equals(CreditDocStatusEnum.海外_待覆核.getCode(), pendingCase.getDocStatus())?"，e-Loan文件狀態："+prop_abstractEloanPage.getProperty("docStatus." + pendingCase.getDocStatus()):"")
						+"）"
						+"已完成線上對保程序。"+"<br/>"
						+"因此本案契約（" +c340m01a.getPloanCtrNo()+"）請先作廢前一份對保契約書後再產生對保契約書。", this.getClass());
			}
		}

		if (Util.isNotEmpty(decisionExpr)) {
			if ("ok".equals(decisionExpr)) {
				if (Util.equals(userId, c340m01a.getUpdater())) {
					// EFD0053' 覆核人員不可與「經辦人員或其它覆核人員」為同一人
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
				}
			}
		}

		//檢查房貸線上對保僅開放6R、M2、N2、MR
		//J-113-0283 新增利率代碼 7D、7C
		List<C340M01B> c340m01bs = clsService.findC340M01B(c340m01a.getMainId());
		if (c340m01bs.size() > 0) {
			L140M01A l140m01a = clsService.findL140M01A_mainId(c340m01bs.get(0).getTabMainId());
			List<L140S02A> l140s02as = clsService.findL140S02A(l140m01a);
			if (l140s02as.size() > 0) {
				List<L140S02D> l140s02ds = clsService.findL140S02D_orderByPhase(l140m01a.getMainId(), l140s02as.get(0).getSeq(),
						"Y");
				if (l140s02ds.size() > 0) {
					if (!this.get_CtrTypeB_pass_RateType(l140s02ds.get(0).getRateType())) {
						throw new CapMessageException("若需採線上對保，利率應選擇6R、M2、N2、MR、7C、7D", this.getClass());
					}
				}
			}
			//J-112-0502 產品種類02、68，動用期限只能為選項1、2、3、8
			String prodKindCheck = Util.trim(lmsService.getSysParamDataValue("J_112_0502_prodKindCheck_on"));
			if("Y".equals(prodKindCheck)){
				L140S02A l140s02a = l140s02as.get(0);
				String prodKind = l140s02a.getProdKind();
				String useDeadline = l140m01a.getUseDeadline();
				if(("02".equals(prodKind) || "68".equals(prodKind)) && 
						!("1".equals(useDeadline) || "2".equals(useDeadline) || "3".equals(useDeadline) || "8".equals(useDeadline))){			
					throw new CapMessageException("產品種類02、68，動用期限只能為選項1、2、3、8", this.getClass());
				}
			}
		}

	}

	@Override
	public void updateDocLogUser(String oid, String userId){
		List<DocLog> docLogs = docLogService.getDocLogList(oid);
		for(DocLog docLog : docLogs){
			docLogService.updateUserId(docLog,userId);
		}
	}

    private List<L140S02H> findByMainIdSeq(String mainId, Integer seq) {
        return l140s02hDao.findByMainIdSeq(mainId, seq);
    }
    
    @Override
    public List<C340M01A> findByPloanCtrBegDate_ctrTypeC(String ploanCtrBegDate) {
		return c340m01aDao.findByPloanCtrBegDate_ctrTypeC(ploanCtrBegDate);
	}

	@Override
	public List<C340M01A> findC340M01A_ploanCtrNo_ctrType(String ploanCtrNo, String ctrType) {
		return c340m01aDao.findByPloanCtrNo_ctrType(ploanCtrNo, ctrType);
	}

	private String get_distinct_lnTotMonth_cross_prod(List<L140S02A> l140s02a_list){
		TreeSet<Integer> set = _lnTotMonth_cross_prod(l140s02a_list);
		if(set.size()==1){
			Integer distinct_lnTotMonth = set.last();
			return String.valueOf(distinct_lnTotMonth);
		}
		return "";
	}
	private TreeSet<Integer> _lnTotMonth_cross_prod(List<L140S02A> l140s02a_list){
		TreeSet<Integer> set = new TreeSet<Integer>();
		for(L140S02A l140s02a : l140s02a_list){
			if (l140s02a.getLnYear() != null
					&& l140s02a.getLnMonth() != null) {
				set.add(l140s02a.getLnYear() * 12 + l140s02a.getLnMonth());
			}
		}
		return set;
	}
	private String[] fmt_extendYear(int extendCntSinceFirst_cross_prod){
		String[] arr = new String[]{"", "", "", ""};
		if(true){
			int extendY = extendCntSinceFirst_cross_prod/12;
			int extendM = extendCntSinceFirst_cross_prod%12;

			arr[0] = String.valueOf(extendY);
			arr[1] = extendM==0?"":String.valueOf(extendM);
		}
		if(true){
			int nextVal = extendCntSinceFirst_cross_prod;
			int div = nextVal/12;
			int remainder = nextVal%12;
			if(remainder==0){
				arr[2] = String.valueOf(div+1);
				arr[3] = "";
			}else if(remainder==11){
				arr[2] = String.valueOf(div);
				arr[3] = String.valueOf(remainder+1);
			}else{
				int i_nextY = div+((remainder+1)/12>0?1:0);
				arr[2] = String.valueOf(i_nextY);
				arr[3] = String.valueOf(remainder+1);
			}
		}
		return arr;
	}
	private String get_distinct_cycl_cross_prod(List<L140S02A> l140s02a_list, Map<Integer, L140S02C> map_l140s02cData, Map<Integer, L140S02E> map_l140s02eData){
		TreeSet<String> _set_MONTH_INTEREST = new TreeSet<String>();
		TreeSet<String> _set_OVERDRAW = new TreeSet<String>();
		TreeSet<String> _set_LNF033_INTRT_CYCL = new TreeSet<String>();
		for(L140S02A l140s02a: l140s02a_list){
			Integer seq = l140s02a.getSeq();
			L140S02C l140s02c = map_l140s02cData.get(seq);
			L140S02E l140s02e = map_l140s02eData.get(seq);
			//IntWay{2:期付金}
			if(l140s02c!=null && Util.equals(UtilConstants.L140S02CIntway.期付金, l140s02c.getIntWay()) && l140s02e!=null){
				String l140s02e_payWay = Util.trim(l140s02e.getPayWay());
				// =========
				// 繳款週期( ELF501_PAYCLE )( LNF033_INTRT_CYCL )
				if ("1".equals(l140s02e_payWay) || "3".equals(l140s02e_payWay)){
					_set_LNF033_INTRT_CYCL.add("1"); //月繳
				}else if ("2".equals(l140s02e_payWay) || "4".equals(l140s02e_payWay)){
					_set_LNF033_INTRT_CYCL.add("2"); //雙週繳
				} else if ("6".equals(l140s02e_payWay)) {
					// 其它
					_set_LNF033_INTRT_CYCL.add(VAL_OTHER);
				} else if ("7".equals(l140s02e_payWay)) {
					// 按月付息
					_set_LNF033_INTRT_CYCL.add(VAL_MONTH_INTEREST);
				}else{
					_set_LNF033_INTRT_CYCL.add(VAL_OTHER);
				}
			}
			else if(l140s02c!=null && Util.equals(UtilConstants.L140S02CIntway.按月計息, l140s02c.getIntWay()) ){
				_set_MONTH_INTEREST.add(VAL_MONTH_INTEREST);
			}
			else if(l140s02c!=null
					&& ( Util.equals(UtilConstants.L140S02CIntway.透支end, l140s02c.getIntWay())
					|| Util.equals(UtilConstants.L140S02CIntway.透支top, l140s02c.getIntWay()) ) ){
				//透支不會有 LNF033
				_set_OVERDRAW.add(VAL_OVERDRAW);
			}
		}
		if(_set_MONTH_INTEREST.size()==0 && _set_OVERDRAW.size()==0 && _set_LNF033_INTRT_CYCL.size()==1){
			return _set_LNF033_INTRT_CYCL.last();
		}
		if(_set_MONTH_INTEREST.size()>0 && _set_OVERDRAW.size()==0 && _set_LNF033_INTRT_CYCL.size()==0){
			return _set_MONTH_INTEREST.last();
		}
		if(_set_MONTH_INTEREST.size()==0 && _set_OVERDRAW.size()>0 && _set_LNF033_INTRT_CYCL.size()==0){
			return _set_OVERDRAW.last();
		}
		return "";
	}
	/** 判斷 本息平均 or 本金平均( ELF501_AVGPAY )( LNF033_RT_TYPE )
	 * @return LNF033_RT_TYPE {2:本息平均攤還, 3:本金平均攤還} 或 Other
	 */
	private String get_distinct_rtType_cross_prod(List<L140S02A> l140s02a_list, Map<Integer, L140S02C> map_l140s02cData, Map<Integer, L140S02E> map_l140s02eData){
		TreeSet<String> _set_LNF033_RT_TYPE = new TreeSet<String>();
		for(L140S02A l140s02a: l140s02a_list){
			Integer seq = l140s02a.getSeq();
			L140S02C l140s02c = map_l140s02cData.get(seq);
			L140S02E l140s02e = map_l140s02eData.get(seq);
			//IntWay{2:期付金}
			if(l140s02c!=null && Util.equals(UtilConstants.L140S02CIntway.期付金, l140s02c.getIntWay()) && l140s02e!=null){
				String l140s02e_payWay = Util.trim(l140s02e.getPayWay());
				// =========
				// 判斷 本息平均 or 本金平均( ELF501_AVGPAY )( LNF033_RT_TYPE )
				if ("1".equals(l140s02e_payWay) || "2".equals(l140s02e_payWay)) {
					_set_LNF033_RT_TYPE.add("2"); // 本息平均攤還
				} else if ("3".equals(l140s02e_payWay) || "4".equals(l140s02e_payWay)) {
					_set_LNF033_RT_TYPE.add("3"); // 本金平均攤還
				} else if ("6".equals(l140s02e_payWay)) {
					// 其它
					_set_LNF033_RT_TYPE.add(VAL_OTHER);
				} else if ("7".equals(l140s02e_payWay)) {
					// 按月付息
					_set_LNF033_RT_TYPE.add(VAL_MONTH_INTEREST);
				}else{
					_set_LNF033_RT_TYPE.add(VAL_OTHER);
				}
			}
		}
		if(_set_LNF033_RT_TYPE.size()==1){
			return _set_LNF033_RT_TYPE.last();
		}
		return "";
	}
	/** @return 自第1期起的期數 or Other(非第1期開始)
	 */
	private String get_distinct_extendCntSinceFirst_cross_prod(List<L140S02A> l140s02a_list, Map<Integer, L140S02C> map_l140s02cData, Map<Integer, L140S02E> map_l140s02eData){
		TreeSet<String> _set = new TreeSet<String>();
		for(L140S02A l140s02a: l140s02a_list){
			Integer seq = l140s02a.getSeq();
			L140S02C l140s02c = map_l140s02cData.get(seq);
			L140S02E l140s02e = map_l140s02eData.get(seq);
			//IntWay{2:期付金}
			if(l140s02c!=null && Util.equals(UtilConstants.L140S02CIntway.期付金, l140s02c.getIntWay()) && l140s02e!=null){
				// =========
				// 判斷 寬限期，正常且新做的期付金, 是從第1期起算。
				if (Util.equals("Y", l140s02e.getNowExtend())){
					if ( Util.parseInt(l140s02e.getNowFrom()) == 1
							&& Util.parseInt(l140s02e.getNowEnd()) >= 1) {
						int period_cnt = Util.parseInt(l140s02e.getNowEnd()) - Util.parseInt(l140s02e.getNowFrom()) + 1;
						_set.add(String.valueOf(period_cnt));
					}else{
						//一開始無寬限期，中間才申請
						_set.add(VAL_OTHER);
					}
				}else{
					_set.add("0");
				}
			}
		}
		if(_set.size()==1){
			return _set.last();
		}
		return "";
	}
	private String get_distinct_intEndTermSinceFirst_cross_prod(List<L140S02A> l140s02a_list, TreeMap<Integer, L140S02C> map_l140s02cData, TreeMap<Integer, List<L140S02D>> map_l140s02dData){
		TreeSet<String> _set = new TreeSet<String>();
		for(L140S02A l140s02a: l140s02a_list){
			Integer seq = l140s02a.getSeq();
			L140S02C l140s02c = map_l140s02cData.get(seq);
			List<L140S02D> l140s02d_list = map_l140s02dData.get(seq);
			int l140s02d_size = l140s02d_list.size();
			//IntWay{2:期付金}
			if(l140s02c!=null && Util.equals(UtilConstants.L140S02CIntway.期付金, l140s02c.getIntWay()) && l140s02d_size>0){
				// =========
				// 判斷 迄期，正常且新做的期付金, 是從第1期起算。
				int begTerm = l140s02d_list.get(0).getBgnNum();
				int endTerm = l140s02d_list.get(l140s02d_size-1).getEndNum();

				if (begTerm == 1 && endTerm >= 1) {
					_set.add(String.valueOf(endTerm));
				}else{
					//第1段，非由第1期開始
					_set.add(VAL_OTHER);
				}
			}
		}
		if(_set.size()==1){
			return _set.last();
		}
		return "";
	}
	public String build_choseKey_by_booleanVal(LinkedHashMap<String, Boolean> dataMap, String splitStr){
		List<String> r_list = new ArrayList<String>();
		for(String itemNameDesc: dataMap.keySet()){
			if(dataMap.get(itemNameDesc)==true){
				r_list.add(itemNameDesc);
			}
		}
		return StringUtils.join(r_list, splitStr);
	}
	/*
	[Key]			[0]		[1]		[2]						[3]					[4]					[5]			[6]							[7]								[8]			  [9]
	phase(1或2) | bgnNum | endNum | rateType(6R或 M2 或N2) | baseRate DEC(6,4) | pmFlag(P:加, M:減)| pmRate | nowRate(等同baseRate+pmRate) | rateFlag(1:固定,2:機動,3:浮動) | rateChgWay | rateChgWay2

	 其它欄位
		seq(1或2)
	 	rateUser(自訂利率DEC(6,4))
	*/
	private LinkedHashMap<String, LinkedHashSet<String>> get_distinct_l140s02d_intr_info(List<L140S02A> l140s02a_list, Set<Integer> chose_seq_set
			, TreeMap<Integer, L140S02C> map_l140s02cData, TreeMap<Integer, List<L140S02D>> map_l140s02dData){
		LinkedHashMap<String, LinkedHashSet<String>> result = new LinkedHashMap<String, LinkedHashSet<String>>();
		for(Integer seq: chose_seq_set){
			L140S02C l140s02c = map_l140s02cData.get(seq);
			List<L140S02D> l140s02d_list = map_l140s02dData.get(seq);
			int l140s02d_size = l140s02d_list.size();
			//IntWay{2:期付金}
			//if(l140s02c!=null && Util.equals(UtilConstants.L140S02CIntway.期付金, l140s02c.getIntWay()) && l140s02d_size>0){
			if(l140s02c!=null &&  l140s02d_size>0){ // 其它貸款契約書，可能包含{期付金、透支、按月計息} => 不要只限制 期付金
				for(L140S02D l140s02d : l140s02d_list){
					String phase = Util.trim(l140s02d.getPhase());
					if(!result.containsKey(phase)){
						result.put(phase, new LinkedHashSet<String>());
					}
					result.get(phase).add(build_l140s02d_intr_info(l140s02d));
				}
			}
		}
		return result;
	}
	private String build_l140s02d_intr_info(L140S02D l140s02d){
		String bgnNum = Util.trim(l140s02d.getBgnNum());
		String endNum = Util.trim(l140s02d.getEndNum());
		String rateType = Util.trim(l140s02d.getRateType());
		String baseRate = Util.trim(LMSUtil.pretty_numStr(l140s02d.getBaseRate()));
		String pmFlag = Util.trim(l140s02d.getPmFlag());
		String pmRate = Util.trim(LMSUtil.pretty_numStr(l140s02d.getPmRate()));
		String nowRate = Util.trim(LMSUtil.pretty_numStr(l140s02d.getNowRate()));
		String rateFlag = Util.trim(l140s02d.getRateFlag());
		String rateChgWay = Util.trim(l140s02d.getRateChgWay());
		String rateChgWay2 = Util.trim(l140s02d.getRateChgWay2());
		//~~~~~~
		if (CrsUtil.RATE_TYPE_01.equals(rateType) && Util.equals("C01", l140s02d.getRateUserType())) {

		}else{
			if(!Util.equals("1", l140s02d.getRateFlag())){ //非固定利率，去中心抓最新的代率值
				baseRate = LMSUtil.pretty_numStr(_get_latest_mis_MISLNRAT_currTWD(rateType));
			}
		}
		nowRate = LMSUtil.pretty_numStr(ClsUtility.calc_nowRate(l140s02d.getRateType(), l140s02d.getRateUserType(), l140s02d.getRateFlag(), l140s02d.getNowRate(), Util.parseBigDecimal(baseRate), pmFlag, l140s02d.getPmRate()));
		List<String> list = new ArrayList<String>();
		list.add(bgnNum);
		list.add(endNum);
		list.add(rateType);
		list.add(baseRate);
		list.add(pmFlag);
		list.add(pmRate);
		list.add(nowRate);
		list.add(rateFlag);
		list.add(rateChgWay);
		list.add(rateChgWay2);
		return StringUtils.join(list, "|");
	}
	private boolean is_mapValue_cnt_eq_1(LinkedHashMap<String, LinkedHashSet<String>> map){
		for(String k: map.keySet()){
			if(map.get(k).size()==1){

			}else{
				return false;
			}
		}
		return true;
	}
	private String build_intr_textStr(List<L140S02A> l140s02a_list, Set<Integer> chose_seq_set
			, TreeMap<Integer, List<L140S02D>> map_l140s02dData, String distinct_cycl_cross_prod){
		List<String> result = new ArrayList<String>();
		if(chose_seq_set.size()==1){
			for(L140S02A l140s02a: l140s02a_list){
				if(!chose_seq_set.contains(l140s02a.getSeq())){
					continue;
				}
				String rateDesc = proc_l140s02a_rateDesc(l140s02a);
				//----
				result.add(rateDesc);
			}
		}else{
			for(L140S02A l140s02a: l140s02a_list){
				if(!chose_seq_set.contains(l140s02a.getSeq())){
					continue;
				}
				C900M01A c900m01a = clsService.findC900M01A_prodKind(l140s02a.getProdKind());
				String prodDesc = "("+CrsUtil.getProdKindName(c900m01a)+")";
				String rateDesc = proc_l140s02a_rateDesc(l140s02a);
				//----
				result.add(prodDesc+rateDesc);
			}
		}

		// 在組字時，可能是 6R但三段式、四段式
		// 或是 P7 中華郵政二年期定期儲金額度未達新台幣５００萬元機動利率
		// 或是 M2
		if(true){
			LinkedHashMap<String, String> show_baseRate_rateTypeMap = new LinkedHashMap<String, String>();
			for(Integer seq : chose_seq_set){
				for(L140S02D l140s02d: map_l140s02dData.get(seq)){
					show_baseRate_rateTypeMap.put(l140s02d.getRateType(), LMSUtil.pretty_numStr(l140s02d.getBaseRate()));
				}
			}
			if(show_baseRate_rateTypeMap.size()>0){
				String[] currs = new String[] { "TWD" };
				HashMap<String, LinkedHashMap<String, String>> _map = misMislnratService
						.findBaseRateByCurrs(currs);
				Map<String, String> rateDesc_map = _map.get("TWD");
				for(String _LR_CODE : show_baseRate_rateTypeMap.keySet()){
					result.add("訂約時"+LMSUtil.getDesc(rateDesc_map, _LR_CODE)+"為"+Util.trim(show_baseRate_rateTypeMap.get(_LR_CODE))+"%。");
				}
			}
		}
		return StringUtils.join(result, "");
	}
	private String proc_l140s02a_rateDesc(L140S02A l140s02a){
		String rateDesc = l140s02a.getRateDesc();
		rateDesc = rateDesc.replaceAll("計息方式：期付金。收息方式：期付金。", "");
		rateDesc = rateDesc.replaceAll("<br>", "");
		rateDesc = rateDesc.replaceAll("<br/>", "");
		rateDesc = rateDesc.replaceAll("<BR>", "");
		rateDesc = rateDesc.replaceAll("<BR/>", "");
		return rateDesc;
	}

	public String[] getHouseLoanParams(){
		String[] page1Params = new String[] { "loanAmt", "preliminaryFee", "creditCheckFee"
				, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_C, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_A, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_B
				, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_D, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_J, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_H
				, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_OTHERDESC
				, "annualPercentageRate"
				, ContractDocConstants.CtrTypeB.PLOAN_COURT_NAME
				, ContractDocConstants.CtrTypeB.PLOAN_GUARANTEEAMT
				, ContractDocConstants.CtrTypeB.PLOAN_BORROWERMOBILENUMBER
				, ContractDocConstants.CtrTypeB.PLOAN_BORROWEREMAIL

				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_A_CB
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_A_T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_A_T2
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_B_CB
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_C_CB
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_C_T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_D_CB
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_D_T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_D_T2
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_D1_T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_D1_T2
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_D1_T3
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_D1_T4
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_D1_T5
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_D1_T6
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_D1_T7
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_D2_1CB
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_D2_2CB
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_D3_T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_E_CB
				,ContractDocConstants.CtrTypeB.ELOAN_PA_DELIV_E_T1

				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_A
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_A_T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_A_T2
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_A_T3
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_A_T4
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_A_T5
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_A_T6
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_A_T7
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_A_T8
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_B
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_B_T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_B_T2
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_B_T3
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_B_T4
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_B_T5
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_B_T6
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_B_T7
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_B_T8
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_B_T9
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_C
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_C_T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_C_T2
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_C_T3
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_C_T4
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_C_T5
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_C_T6
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_C_T7
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_C_T8
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_C_T9
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_D
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_D_T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_D_T2
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_D_T3
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_D_T4
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_D_T5
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_D_T6
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_E
				,ContractDocConstants.CtrTypeB.ELOAN_PA_USE_E_T1

				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_A
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_B
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_C
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_D
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_D_T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_D_T2
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_D_T3
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_D_T4
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_E
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_E_T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_E_T2
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_E_T3
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_E_T4
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_F
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_F_T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_F_T2
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_G
				,ContractDocConstants.CtrTypeB.ELOAN_PA_REPAY_G_T1

				,ContractDocConstants.CtrTypeB.preliminaryFee
				,ContractDocConstants.CtrTypeB.creditCheckFee
				,ContractDocConstants.CtrTypeB.renewFee
				,ContractDocConstants.CtrTypeB.changeFee
				,ContractDocConstants.CtrTypeB.certFee
				,ContractDocConstants.CtrTypeB.reissueFee

				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_NOPPP_CB
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_NOPPP_BASERATE
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_NOPPP_1T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_NOPPP_2T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_NOPPP_3T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_NOPPP_4T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_CB
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_BASERATE
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1X1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1X2
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1X3
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1Y1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1Y2
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1Y3
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1Y5
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1Y6
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1Y7
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_2T1
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_OTHER_CB
				,ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_OTHER_T1
				
				//J-113-0050 線上房貸增貸對保契約書新增約據及央行切結書資料
				,ContractDocConstants.CtrTypeB.CONSENTVER
				,ContractDocConstants.CtrTypeB.COLLATERALBUILDINGADDR_1
				,ContractDocConstants.CtrTypeB.MORTGAGEMAXAMT_1
				,ContractDocConstants.CtrTypeB.FIRSTLOANDATE_YEAR
				,ContractDocConstants.CtrTypeB.FIRSTLOANDATE_MTH
				,ContractDocConstants.CtrTypeB.FIRSTLOANDATE_DAY
				,ContractDocConstants.CtrTypeB.FIRSTLOANAMT_1
				,ContractDocConstants.CtrTypeB.COLLATERALBUILDINGADDR_2
				,ContractDocConstants.CtrTypeB.MORTGAGEMAXAMT_2
				,ContractDocConstants.CtrTypeB.FIRSTLOANAMT_2
				,ContractDocConstants.CtrTypeB.HOUSELOANCONTRACTNO
				,ContractDocConstants.CtrTypeB.COLLATERALCONTRACTTERMS
				,ContractDocConstants.CtrTypeB.UNREGISTEREDBUILDINGDESC
				,ContractDocConstants.CtrTypeB.COLLATERALCONTRACTTERMS_5_DESC
				,ContractDocConstants.CtrTypeB.CBAFFTTERMS
				,ContractDocConstants.CtrTypeB.CBAFFT1_1
				,ContractDocConstants.CtrTypeB.CBAFFT1_2
				,ContractDocConstants.CtrTypeB.CBAFFT1_3_YEAR
				,ContractDocConstants.CtrTypeB.CBAFFT1_3_MTH
				,ContractDocConstants.CtrTypeB.CBAFFT1_3_DAY
				,ContractDocConstants.CtrTypeB.CBAFFT1_4
				,ContractDocConstants.CtrTypeB.CBAFFT1_5
				,ContractDocConstants.CtrTypeB.CBAFFT1_6
				,ContractDocConstants.CtrTypeB.CBAFFT1_7
				,ContractDocConstants.CtrTypeB.CBAFFT1_8
				,ContractDocConstants.CtrTypeB.CBAFFT1_9_YEAR
				,ContractDocConstants.CtrTypeB.CBAFFT1_9_MTH
				,ContractDocConstants.CtrTypeB.CBAFFT1_9_DAY
				,ContractDocConstants.CtrTypeB.CBAFFT1_10_YEAR
				,ContractDocConstants.CtrTypeB.CBAFFT1_10_MTH
				,ContractDocConstants.CtrTypeB.CBAFFT1_10_DAY
				,ContractDocConstants.CtrTypeB.CBAFFT1_10_NO
				,ContractDocConstants.CtrTypeB.CBAFFT1_11
				,ContractDocConstants.CtrTypeB.CBAFFT1_12
				,ContractDocConstants.CtrTypeB.CBAFFT1_13
				,ContractDocConstants.CtrTypeB.CBAFFT1_14
				,ContractDocConstants.CtrTypeB.CBAFFT1_15
				,ContractDocConstants.CtrTypeB.CBAFFT1_16
				,ContractDocConstants.CtrTypeB.CBAFFT2_1
				,ContractDocConstants.CtrTypeB.CBAFFT2_2
				,ContractDocConstants.CtrTypeB.CBAFFT2_3_YEAR
				,ContractDocConstants.CtrTypeB.CBAFFT2_3_MTH
				,ContractDocConstants.CtrTypeB.CBAFFT2_3_DAY
				,ContractDocConstants.CtrTypeB.CBAFFT2_4
				,ContractDocConstants.CtrTypeB.CBAFFT2_5
				,ContractDocConstants.CtrTypeB.CBAFFT2_6
				,ContractDocConstants.CtrTypeB.CBAFFT2_7
				,ContractDocConstants.CtrTypeB.CBAFFT2_8
				,ContractDocConstants.CtrTypeB.CBAFFT2_9
				,ContractDocConstants.CtrTypeB.CBAFFT2_10
				,ContractDocConstants.CtrTypeB.CBAFFT3_1
				,ContractDocConstants.CtrTypeB.CBAFFT3_2
				,ContractDocConstants.CtrTypeB.CBAFFT3_3_YEAR
				,ContractDocConstants.CtrTypeB.CBAFFT3_3_MTH
				,ContractDocConstants.CtrTypeB.CBAFFT3_3_DAY
				,ContractDocConstants.CtrTypeB.CBAFFT3_4
				,ContractDocConstants.CtrTypeB.CBAFFT3_5
				,ContractDocConstants.CtrTypeB.CBAFFT4_1
				,ContractDocConstants.CtrTypeB.CBAFFT4_2_YEAR
				,ContractDocConstants.CtrTypeB.CBAFFT4_2_MTH
				,ContractDocConstants.CtrTypeB.CBAFFT4_2_DAY
				,ContractDocConstants.CtrTypeB.CBAFFT4_3
				,ContractDocConstants.CtrTypeB.CBAFFT4_4
				,ContractDocConstants.CtrTypeB.CBAFFT4_5
				,ContractDocConstants.CtrTypeB.CBAFFT4_6
				,ContractDocConstants.CtrTypeB.CBAFFT4_7
				,ContractDocConstants.CtrTypeB.CBAFFT5_1
				,ContractDocConstants.CtrTypeB.CBAFFT5_2
				,ContractDocConstants.CtrTypeB.CBAFFT5_3_YEAR
				,ContractDocConstants.CtrTypeB.CBAFFT5_3_MTH
				,ContractDocConstants.CtrTypeB.CBAFFT5_3_DAY
				,ContractDocConstants.CtrTypeB.CBAFFT5_4
				,ContractDocConstants.CtrTypeB.CBAFFT5_5
				,ContractDocConstants.CtrTypeB.CBAFFT5_6
				,ContractDocConstants.CtrTypeB.CBAFFT5_7
				,ContractDocConstants.CtrTypeB.CBAFFT5_8
				,ContractDocConstants.CtrTypeB.CBAFFT5_9
				,ContractDocConstants.CtrTypeB.CBAFFT5_10
				,ContractDocConstants.CtrTypeB.CBAFFT5_11
				,ContractDocConstants.CtrTypeB.CBAFFT5_12
				,ContractDocConstants.CtrTypeB.CBAFFT5_13
				,ContractDocConstants.CtrTypeB.CBAFFT5_14
				,ContractDocConstants.CtrTypeB.CBAFFT5_15
		};

		return page1Params;
	}

	private boolean get_CtrTypeB_Staff_RateType(String baseRateCode){
		if(CrsUtil.inCollection(baseRateCode, new String[]{CrsUtil.RATE_TYPE_M3, CrsUtil.RATE_TYPE_N2, CrsUtil.RATE_TYPE_MR})){ //{N2-行員理財型貸款利率, M3-行員消貸利率}
			return true;
		}
		return false;
	}

	private boolean get_CtrTypeB_pass_RateType(String baseRateCode){
		//J-113-0283 新增利率代碼 7D、7C
		if(CrsUtil.inCollection(baseRateCode, new String[]{CrsUtil.RATE_TYPE_6R, CrsUtil.RATE_TYPE_7C, CrsUtil.RATE_TYPE_7D})
			|| this.get_CtrTypeB_Staff_RateType(baseRateCode)){ //{N2-行員理財型貸款利率, M3-行員消貸利率}
			return true;
		}
		return false;
	}

	@Override
	public List<Map<String, Object>> find_ploanAcct(String custId) {
		return dwdbBASEService.getGuarOnLineAccount(custId);
	}

	@Override
	public Map<String, Object> findODS_CMFAUDAC_ByAcc(String brNo,String acct) {

		return odsdbBASEService.findODS_CMFAUDAC_ByAcc(brNo,acct);
	}
}
