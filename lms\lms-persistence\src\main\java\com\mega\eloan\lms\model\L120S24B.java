/* 
 * L120S24B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 風控風險權數擔保品明細 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S24B", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S24B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * refOid<p/>
	 * 對應到L120S24A.OID
	 */
	@Size(max=32)
	@Column(name="REFOID_S24B", length=32, columnDefinition="CHAR(32)")
	private String refOid_s24b;

	/** 
	 * 合格金融擔保品<p/>
	 * CODETYPE= quaColl_s24b
	 */
	@Size(max=2)
	@Column(name="QUACOLL_S24B", length=2, columnDefinition="VARCHAR(2)")
	private String quaColl_s24b;

	/** 擔保品幣別 **/
	@Size(max=3)
	@Column(name="COLLCURR_S24B", length=3, columnDefinition="CHAR(3)")
	private String collCurr_s24b;

	/** 擔保品價值 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="COLLAMT_S24B", columnDefinition="DECIMAL(17,2)")
	private BigDecimal collAmt_s24b;

	/** 幣別對稱 **/
	@Size(max=2)
	@Column(name="CURRSYM_S24B", length=2, columnDefinition="VARCHAR(2)")
	private String currSym_s24b;

	/** 折扣值 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="DISCOUNTPER_S24B", columnDefinition="DECIMAL(5,2)")
	private BigDecimal discountPer_s24b;

	/** 折扣後擔保品價值 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="DISCOLLAMT_S24B", columnDefinition="DECIMAL(17,2)")
	private BigDecimal disCollAmt_s24b;

	/** 折扣後擔保品價值_TWD **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="DISCOLLAMT_TWD_S24B", columnDefinition="DECIMAL(17,2)")
	private BigDecimal disCollAmt_TWD_s24b;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 擔保品評等 **/
	@Size(max=2)
	@Column(name="COLLCRDGRADE_S24B", length=2, columnDefinition="VARCHAR(2)")
	private String collCrdGrade_s24b;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得refOid<p/>
	 * 對應到L120S24A.OID
	 */
	public String getRefOid_s24b() {
		return this.refOid_s24b;
	}
	/**
	 *  設定refOid<p/>
	 *  對應到L120S24A.OID
	 **/
	public void setRefOid_s24b(String value) {
		this.refOid_s24b = value;
	}

	/** 
	 * 取得合格金融擔保品<p/>
	 * CODETYPE= quaColl_s24b
	 */
	public String getQuaColl_s24b() {
		return this.quaColl_s24b;
	}
	/**
	 *  設定合格金融擔保品<p/>
	 *  CODETYPE= quaColl_s24b
	 **/
	public void setQuaColl_s24b(String value) {
		this.quaColl_s24b = value;
	}

	/** 取得擔保品幣別 **/
	public String getCollCurr_s24b() {
		return this.collCurr_s24b;
	}
	/** 設定擔保品幣別 **/
	public void setCollCurr_s24b(String value) {
		this.collCurr_s24b = value;
	}

	/** 取得擔保品價值 **/
	public BigDecimal getCollAmt_s24b() {
		return this.collAmt_s24b;
	}
	/** 設定擔保品價值 **/
	public void setCollAmt_s24b(BigDecimal value) {
		this.collAmt_s24b = value;
	}

	/** 取得幣別對稱 **/
	public String getCurrSym_s24b() {
		return this.currSym_s24b;
	}
	/** 設定幣別對稱 **/
	public void setCurrSym_s24b(String value) {
		this.currSym_s24b = value;
	}

	/** 取得折扣值 **/
	public BigDecimal getDiscountPer_s24b() {
		return this.discountPer_s24b;
	}
	/** 設定折扣值 **/
	public void setDiscountPer_s24b(BigDecimal value) {
		this.discountPer_s24b = value;
	}

	/** 取得折扣後擔保品價值 **/
	public BigDecimal getDisCollAmt_s24b() {
		return this.disCollAmt_s24b;
	}
	/** 設定折扣後擔保品價值 **/
	public void setDisCollAmt_s24b(BigDecimal value) {
		this.disCollAmt_s24b = value;
	}

	/** 取得折扣後擔保品價值_TWD **/
	public BigDecimal getDisCollAmt_TWD_s24b() {
		return this.disCollAmt_TWD_s24b;
	}
	/** 設定折扣後擔保品價值_TWD **/
	public void setDisCollAmt_TWD_s24b(BigDecimal value) {
		this.disCollAmt_TWD_s24b = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	
	/** 取得擔保品評等 **/
	public String getCollCrdGrade_s24b() {
		return this.collCrdGrade_s24b;
	}
	/** 設定擔保品評等 **/
	public void setCollCrdGrade_s24b(String collCrdGrade_s24b) {
		this.collCrdGrade_s24b = collCrdGrade_s24b;
	}
}
