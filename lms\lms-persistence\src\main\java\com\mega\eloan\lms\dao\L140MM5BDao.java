/* 
 * L140MM5BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140MM5B;

/** 電子文件維護作業簽章欄檔 **/
public interface L140MM5BDao extends IGenericDao<L140MM5B> {

	L140MM5B findByOid(String oid);
	
	List<L140MM5B> findByMainId(String mainId);
	
	L140MM5B findByUniqueKey(String mainId, String branchType, String branchId, String staffNo, String staffJob);

	List<L140MM5B> findByIndex01(String mainId, String branchType, String branchId, String staffNo, String staffJob);
}