/* 
 * L120M01KDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L120M01KDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120M01K;

/** 簽報書逾權記錄檔 **/
@Repository
public class L120M01KDaoImpl extends LMSJpaDao<L120M01K, String> implements
		L120M01KDao {

	@Override
	public L120M01K findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120M01K> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120M01K> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120M01K> findByIndex01(String mainId, String overType) {
		ISearch search = createSearchTemplete();
		List<L120M01K> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "overType",
					overType);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120M01K> findByMainIdAndOverType(String mainId,
			String[] overTypeArr) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (overTypeArr != null && overTypeArr.length > 0) {
			search.addSearchModeParameters(SearchMode.IN, "overType",
					overTypeArr);
		}

		search.addOrderBy("overType", false);// 先依照逾權類別
		search.addOrderBy("isFullCaseFlag", false);// 全案那筆排序往前放(先顯示)
		search.addOrderBy("custId", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120M01K> list = createQuery(search).getResultList();
		return list;
	}
}