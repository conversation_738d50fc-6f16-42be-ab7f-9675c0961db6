/* 
 * DWASLNOVEROVSService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dw.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 授信逾放比例月檔(13月)
 * </pre>
 * 
 * @since 2012/2/6
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/6,REX,new
 *          </ul>
 */
public interface DWASLNOVEROVSService {

	/**
	 * 額度明細表-分行逾放比率
	 * 
	 * @param ownBrId
	 *            分行代號
	 * @return 逾放比率資料
	 */
	public List<Map<String, Object>> findDW_ASLNOVEROVSForRate(String[] ownBrId);
}
