package com.mega.eloan.lms.cls.handler.form;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ContractDocService;
import com.mega.eloan.lms.cls.pages.CLS3401M08Page;
import com.mega.eloan.lms.cls.service.CLS3401Service;
import com.mega.eloan.lms.mfaloan.service.MisMislnratService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.C340M01B;
import com.mega.eloan.lms.model.C340M01C;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01R;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02D;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 線上對保契約書 => 對應 ContractDocConstants.C340M01A_CtrType.Type_B 線上對保契約書
 * </pre>
 *
 * <AUTHOR>
 * @version <ul>
 * <li>2020/06/20,EL07625,new
 * </ul>
 * @since 2020/06/20
 */
@Scope("request")
@Controller("cls3401m08formhandler")
@DomainClass(C340M01A.class)
public class CLS3401M08FormHandler extends AbstractFormHandler {
	private static final DateFormat S_FORMAT = new SimpleDateFormat(UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);

	@Resource
	BranchService branchService;

	@Resource
	CLSService clsService;

	@Resource
	ICustomerService iCustomerService;

	@Resource
	TempDataService tempDataService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	CLS3401Service cls3401Service;

	@Resource
	ContractDocService contractDocService;

	@Resource
	MisMislnratService misMislnratService;
	
	@Resource
	SysParameterService sysParameterService;

	Properties prop_cls3401m08 = MessageBundleScriptCreator.getComponentResource(CLS3401M08Page.class);
	Properties prop_abstractEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newC340M01A(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();

		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String ctrType = Util.trim(
				params.getString("ctrType", ContractDocConstants.C340M01A_CtrType.Type_B));
		String caseMainId = Util.trim(params.getString("caseMainId"));
		String tabMainId = Util.trim(params.getString("tabMainId"));

		L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);

		C340M01A c340m01a = new C340M01A();
		String txCode = Util.trim(params.getString(EloanConstants.TRANSACTION_CODE));
		// UPGRADE: 待確認，URL是否正確
		String docUrl = params.getString("docUrl");

		cls3401Service.init_C340M01ARelateCtrTypeB(c340m01a,custId,dupNo,ctrType,caseMainId,tabMainId,user.getUnitNo(),user.getUserId(),txCode,docUrl);

		cls3401Service.init_C340RelateCtrTypeB(c340m01a, caseMainId, tabMainId);

		return defaultResult(params, c340m01a, result);
	}

	@DomainAuth(value = AuthType.Query)
	public IResult queryC340M01A(PageParameters params) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC340M01A_oid(mainOid);

			String page = params.getString(EloanConstants.PAGE);

			if ("01".equals(page)) {
				LMSUtil.addMetaToResult(result, meta,
						new String[] { "custId", "dupNo", "custName", "caseNo", "contrNumber", "contrPartyNm", "ploanCtrNo"
								, "ploanCtrExprDate", "ploanCtrSignTimeM", "ploanCtrSignTime1"
								, "ploanCtrBegDate", "ploanCtrEndDate", "ploanBorrowerIPAddr", "ploanStakeholderIPAddr"});
				String ownBrId = meta.getOwnBrId();
				result.set("ownBrId", StrUtils.concat(ownBrId, " ", branchService.getBranchName(ownBrId)));
				result.set("docStatus", getMessage("docStatus." + meta.getDocStatus()));
				result.set("ploanCtrStatus", getMessage("ploanCtrStatus." + Util.trim(meta.getPloanCtrStatus())));
				result.set("typCd", getMessage("typCd." + meta.getTypCd()));
				result.set("ctrTypeMapDesc", get_ctrType_desc(meta.getCtrType()));
				result.set("creator", _id_name(meta.getCreator()));
				result.set("createTime", Util.trim(TWNDate.valueOf(meta.getCreateTime())));
				result.set("updater", _id_name(meta.getUpdater()));
				result.set("updateTime", Util.trim(TWNDate.valueOf(meta.getUpdateTime())));
				result.set("rptId_desc", rptId_desc(meta));
				result.set("ploanCtrDcTime", meta.getPloanCtrDcTime()==null?"":S_FORMAT.format(meta.getPloanCtrDcTime()));
				result.set("ploanCtrDcUser", _id_name(meta.getPloanCtrDcUser()));
				result.set("ploanCtrBegDateToEndDate", Util.trim(TWNDate.toAD(meta.getPloanCtrBegDate()))
						+(meta.getPloanCtrBegDate()!=null&&meta.getPloanCtrEndDate()!=null?" ~ ":"")
						+Util.trim(TWNDate.toAD(meta.getPloanCtrEndDate())));
				List<C340M01B> c340m01b_list = clsService.findC340M01B(meta.getMainId());
				result.set("cntrNo", get_c340m01b_cntrNo(c340m01b_list));

				//J-111-0227 eDDA訊息
				if(Util.isNotEmpty(meta.getIsNeedACH())){
					if (Util.equals(meta.getIsNeedACH(),UtilConstants.DEFAULT.是)) {
						C340M01C c340m01c = clsService.findC340M01C(meta.getMainId(), ContractDocConstants.C340M01C_ItemType.TYPE_9);

						if (c340m01c != null) {
							JSONObject jsonData = c340m01c.getJsonData() == null ? new JSONObject() : JSONObject.fromObject(
									c340m01c.getJsonData());
							result.set("eddaResult", Util.isNotEmpty(jsonData.optString("rcDesc"))?"訂約新增成功":"");
							result.set("eddaResultTime", jsonData.optString("rcADate"));
						}
					}
				}

				//J-112-0205 Web e-Loan新增代償相關欄位
				C340M01C c340m01c = clsService.findC340M01C(meta.getMainId(), ContractDocConstants.C340M01C_ItemType.TYPE_0);
				if (c340m01c != null) {
					JSONObject jsonData = c340m01c.getJsonData() == null ? new JSONObject() : JSONObject.fromObject(
							c340m01c.getJsonData());
					String isRepayment = Util.trim(jsonData.optString("isRepayment"));
					if ( Util.equals(isRepayment,UtilConstants.DEFAULT.是) ) {
						String repayList = Util.trim(MapUtils.getString(jsonData, ContractDocConstants.CtrTypeB.PLOAN_REPAYMENT_LIST));
						if (Util.isNotEmpty(repayList)) {
							List<Object> repayment_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsonData, ContractDocConstants.CtrTypeB.PLOAN_REPAYMENT_LIST);
							int i = 1;
							result.set("repaymentCount",repayment_list.size());
							for (Object ob : repayment_list) {
								JSONObject repaymentJson = JSONObject.fromObject(ob);
								result.set("bankCode_"+i,Util.trim(MapUtils.getString(repaymentJson,"bankCode")));
								result.set("bankName_"+i,Util.trim(MapUtils.getString(repaymentJson,"bankName")));
								result.set("repaymentProductType_"+i,Util.trim(MapUtils.getString(repaymentJson,"repaymentProductType")));
								result.set("originalAmt_"+i,Util.trim(MapUtils.getString(repaymentJson,"originalAmt")));
								i++;
							}
						}
					}
				}
				C340M01C c340m01c_9 = clsService.findC340M01C(meta.getMainId(), ContractDocConstants.C340M01C_ItemType.TYPE_9);
				if (c340m01c_9 != null) {
					JSONObject jsonData = c340m01c_9.getJsonData() == null ? new JSONObject() : JSONObject.fromObject(
							c340m01c_9.getJsonData());

					//新增每月定期還款日欄位
					Date firstPayDate = Util.parseDate(Util.trim(jsonData.optString("firstPaymentDate")));
					String firstPaymentDate=Util.trim(firstPayDate)!=null? CapDate.formatDate(firstPayDate,"yyyy-MM-dd"):"";
					result.set("ploanFirstPaymentDate", firstPaymentDate);
				}
			}
		}

		//在 defaultResult(...) 去讀  c340m01c
		return defaultResult(params, meta, result);
	}

	private String get_c340m01b_cntrNo(List<C340M01B> c340m01b_list) {
		List<String> cntrNo_list = new ArrayList<String>();
		for (C340M01B c340m01b : c340m01b_list) {
			cntrNo_list.add(c340m01b.getCntrNo());
		}
		return StringUtils.join(cntrNo_list, "、");
	}


	private String _id_name(String raw_id) {
		String id = Util.trim(raw_id);
		return Util.trim(id + " " + Util.trim(userInfoService.getUserName(id)));
	}

	/**
	 * 儲存
	 *
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params) throws CapException {
		return _saveAction(params, "N");
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params) throws CapException {
		return _saveAction(params, "Y");
	}

	private CapAjaxFormResult _saveAction(PageParameters params,
			String tempSave) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y", params.getString("allowIncomplete"));
		//===
		String KEY = "saveOkFlag";


		Map<String, Object> paramMap = new HashMap<String, Object>();
		// 整理進來的參數值
		for (String key : params.keySet()) {
			if (params.get(key) instanceof String[] && params.getStringArray(key).length > 1) {
				paramMap.put(key, params.getStringArray(key));
			} else {
				paramMap.put(key, params.getString(key));
			}
		}


		String[] page1Params = cls3401Service.getHouseLoanParams();

		//貼上的文字有 tab => 要用 StringUtils.trimToEmpty(?) 把 tab 移掉
		String[] trimToEmptyParams = new String[]{ContractDocConstants.CtrTypeB.PLOAN_COURT_NAME
				, ContractDocConstants.CtrTypeB.PLOAN_BORROWERMOBILENUMBER
				, ContractDocConstants.CtrTypeB.PLOAN_BORROWEREMAIL};
		JSONObject tabJson = new JSONObject();
		for (String param : page1Params) {
			if (paramMap.containsKey(param)) {
				Object obj_value = paramMap.get(param);
				Object target_value = obj_value;
				if(CrsUtil.inCollection(param, trimToEmptyParams)
						&& (obj_value instanceof String)) {
					target_value = StringUtils.trimToEmpty((String)obj_value);
				}
				tabJson.put(param, target_value);
			}
			else{
//				throw new CapMessageException("lost["+param+"]", getClass());
			}
		}
//		if(true){ //若前端不輸入 pmRate，把空白轉成0
//			String[] numberColumnArr = new String[]{ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_PLUSRATE, ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2_PLUSRATE};
//			for(String column : numberColumnArr){
//				if(Util.isEmpty(paramMap.get(column))){
//					tabJson.put(column, "0");
//				}
//			}
//		}

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try {
				meta = clsService.findC340M01A_oid(mainOid);
				List<C340M01C> c340m01c_list = clsService.findC340M01C(meta.getMainId());
				//~~~~~~
				String page = params.getString(EloanConstants.PAGE);

				if ("01".equals(page)) {
					//這2欄在 前端 先不顯示
					//CapBeanUtil.map2Bean(params, meta, new String[] { "contrNumber", "contrPartyNm" });
				}

				C340M01C c340m01c =  c340m01c_list.get(0);
				JSONObject targetJsonObject = DataParse.toJSON(c340m01c.getJsonData());

				//特別處理-計息方式如果是員工利率，其他文字寫回PLOAN_RATE_RANGE1_RATEDESC，因Ploan顯示的是PLOAN_RATE_RANGE1_RATEDESC欄位
				if (Util.equals(targetJsonObject.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1),"Y")) {
					if (Util.isNotEmpty(tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_2T1))
							|| Util.isNotEmpty(tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_NOPPP_4T1))) {
						String rateDesc = tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_2T1) ;
						if (Util.isEmpty(rateDesc)) {
							rateDesc = tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_NOPPP_4T1) ;
						}
						tabJson.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RATEDESC , rateDesc);
					}
				}

				//因為利率欄位都可以調整，須寫回rateRangeXParamXX
				if (Util.equals("Y", tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_CB))) {
					if (Util.isNotEmpty(tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1X1))
					&& Util.isNotEmpty(tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1X2))) {
						tabJson.put("rateRange1Param01", tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1X1));
						tabJson.put("rateRange1Param02", tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1X2));
						tabJson.put("rateRange1Param03", tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1X3));
						tabJson.put("rateRange2Param01", tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1Y1));
						tabJson.put("rateRange2Param02", tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1Y2));
						tabJson.put("rateRange2Param03", tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1Y3));
					}
					else{
						tabJson.put("rateRange1Param01", tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1Y1));
						tabJson.put("rateRange1Param02", tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1Y2));
						tabJson.put("rateRange1Param03", tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1Y3));
						tabJson.put("rateRange2Param01", tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1Y5));
						tabJson.put("rateRange2Param02", tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1Y6));
						tabJson.put("rateRange2Param03", tabJson.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_1Y7));
					}
				}


//				if ("Y".equals(targetJsonObject.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1))) {
//					// 計算加減碼總合
//					BigDecimal base = CapMath.getBigDecimal(targetJsonObject.optString("rateRange1Param05"));
//					BigDecimal plus = CapMath.getBigDecimal(tabJson.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_PLUSRATE));
//					tabJson.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RESULTRATE, LMSUtil.pretty_numStr(base.add(plus)));
//				}
//
//				if ("Y".equals(targetJsonObject.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2))) {
//					// 計算加減碼總合
//					BigDecimal base = CapMath.getBigDecimal(targetJsonObject.optString("rateRange2Param05"));
//					BigDecimal plus = CapMath.getBigDecimal(tabJson.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2_PLUSRATE));
//					tabJson.put(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2_RESULTRATE, LMSUtil.pretty_numStr(base.add(plus)));
//				}

				//若 該契約 沒有從債務人, 不必硬塞入 保證金額
				//tabJson.put("guaranteeAmt", tabJson.optString("loanAmt"));

				//==================================================
				//此寫法會把 JsonData 全部蓋掉
//				c340m01c.setJsonData(tabJson.toString());

				//==================================================
				//只針對｛指定欄位｝去 update 值
				if(tabJson.containsKey(ContractDocConstants.CtrTypeB.PLOAN_ACCTNO_LIST)){
					throw new CapMessageException("["+ContractDocConstants.CtrTypeB.PLOAN_ACCTNO_LIST+"] should readonly", getClass());
				}
				targetJsonObject.putAll(tabJson);

				C340M01C c340m01c_1 = clsService.findC340M01C(meta.getMainId(),ContractDocConstants.C340M01C_ItemType.TYPE_1);
				if (Util.isEmpty(c340m01c_1)) {
					C340M01C c340m01c_reg = new C340M01C();
					c340m01c_reg.setMainId(meta.getMainId());
					c340m01c_reg.setItemType(ContractDocConstants.C340M01C_ItemType.TYPE_1);
					c340m01c_reg.setCreator(user.getUserId());
					c340m01c_reg.setCreateTime(CapDate.getCurrentTimestamp());
					c340m01c_reg.setJsonData(c340m01c.getJsonData());
					clsService.save(c340m01c_reg);
				}
				c340m01c.setJsonData(targetJsonObject.toString());
				c340m01c.setUpdater(user.getUserId());
				c340m01c.setUpdateTime(CapDate.getCurrentTimestamp());
				clsService.save(c340m01c);


				meta.setDeletedTime(null);
				meta.setUpdater(user.getUserId());
				meta.setUpdateTime(CapDate.getCurrentTimestamp());
				meta.setRandomCode(IDGenerator.getRandomCode());

				if ("Y".equals(tempSave)) {
					cls3401Service.saveTemporaryMeta(meta);
				} else {
					cls3401Service.saveMeta(meta);
				}
				// ===
				if (Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
					// 在tempSave<>Y,若有未填欄位,丟 CapMessageException, 讓
					// saveOkFlag==false

					String err_msg = check_data_relation(meta);
					String unKeyIn_column = unKeyIn_column(meta);
					if (allowIncomplete == false && Util.isNotEmpty(err_msg)) {
						throw new CapMessageException(err_msg, getClass());
					}

					if (allowIncomplete && (Util.isNotEmpty(err_msg) || Util.isNotEmpty(unKeyIn_column))) {
						result.set("IncompleteMsg", err_msg + ((Util.isNotEmpty(err_msg) && Util.isNotEmpty(
								unKeyIn_column)) ? "<br/>" : "") + unKeyIn_column);
					}
				}
				result.set(KEY, true);
			} catch (Exception e) {
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}
		}

		return defaultResult(params, meta, result);
	}

	/**
	 * 呈主管覆核
	 *
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params) throws Throwable {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));

		C340M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC340M01A_oid(mainOid);

			cls3401Service.check_CtrTypeB(meta, user.getUserId(),decisionExpr, prop_cls3401m08,prop_abstractEloanPage);

			cls3401Service.flowAction(meta, params.containsKey("decisionExpr"), decisionExpr);

		}
		return defaultResult(params, meta, result);
	}

	@DomainAuth(AuthType.Modify)
	public IResult delC340M01A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A c340m01a = clsService.findC340M01A_oid(mainOid);
		if (c340m01a != null) {
			c340m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			clsService.save(c340m01a);
		}
		return result;
	}

	private String check_data_relation(C340M01A c340m01a) throws CapException {
		//===========================
		// 若簽報書核准後，有被退回修改
		if (c340m01a == null) {
			return "查無 契約書資料";
		}
		L120M01A l120m01a = clsService.findL120M01A_mainId(c340m01a.getCaseMainId());
		if (l120m01a == null) {
			return "查無 簽報書資料" + "(" + c340m01a.getCaseMainId() + ")。" + "若退回且修改已核准簽報書，請重新產製契約書。";
		}
		for (C340M01B c340m01b : clsService.findC340M01B(c340m01a.getMainId())) {
			L140M01A l140m01a = clsService.findL140M01A_mainId(c340m01b.getTabMainId());
			if (l140m01a == null) {
				return "查無 額度明細表資料(" + c340m01b.getTabMainId() + ")。" + "若退回且修改已核准簽報書，請重新產製契約書。";
			}
		}

		String ploanCtrNo = Util.trim(c340m01a.getPloanCtrNo());
		if(cls3401Service.findC340M01A_ploanCtrNo_ctrType(ploanCtrNo, c340m01a.getCtrType()).size()>1){
			return prop_cls3401m08.getProperty("C340M01A.ploanCtrNo")+"(" + ploanCtrNo + ")重複，請刪除此份契約，重新產出。";
		}

		//J-111-0227 pLoan調整使用用途
		Map<String, Object> map = new HashMap<String, Object>();
		List<C340M01C> c340m01c_list = clsService.findC340M01C(c340m01a.getMainId());
		for (C340M01C c340m01c : c340m01c_list) {
			String jsonData = Util.trim(c340m01c.getJsonData());
			JSONObject jsonObject = DataParse.toJSON(jsonData);

			LMSUtil.addJsonToMap(map, jsonObject);
		}
		boolean chose_N = Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_N, ""));
		boolean chose_G = Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_G, ""));
		boolean chose_P = Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_P, ""));
		if(chose_N || chose_G || chose_P){
			C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(c340m01a.getCaseMainId(),c340m01a.getCustId(),c340m01a.getDupNo());
			if (Util.isNotEmpty(c120m01a)) {
				C120S01E c120s01e = clsService.findC120S01E(c120m01a);
				if(Util.isNotEmpty(c120s01e)){
					//J-111-0227 eDDA判斷利害關係人且個人週轉金、投資理財、員工認股不能選，只能選個人消費性用途
					if(Util.equals("1", c120s01e.getIsQdata2()) || Util.equals("1", c120s01e.getIsQdata3()) ){
						return prop_cls3401m08.getProperty("C340M01A.ploanCtrNo") +"(" + ploanCtrNo + ")" +"，此為利害關係人(使用用途)不得選擇個人週轉金、投資理財、員工認股";
					}
				}
			}
		}

		return "";
	}

	@DomainAuth(AuthType.Modify)
	public IResult check_C340(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A meta = clsService.findC340M01A_oid(mainOid);
		if (true) {
			String err_msg = check_data_relation(meta);
			if (Util.isNotEmpty(err_msg)) {
				throw new CapMessageException(err_msg, getClass());
			}
		}
		if (true) {
			String msg = unKeyIn_column(meta);
			if (Util.isNotEmpty(msg)) {
				result.set("msg", msg);
			}
		}

		if (clsService.is_function_on_codetype("chk_rate_gt_16percent_C340_M02")
				&& chk_rate_gt_16percent(meta)){
			//J-111-0340 e-Loan 簽報書及對保契約書檢核利率大於16%時無法送出覆核
			throw new CapMessageException("依程修(111)第(2063)號檢核規則：利率大於16%時無法送出覆核", getClass());

		}
		result.set("model_custId", Util.trim(meta.getCustId()));
		result.set("model_custName", Util.trim(meta.getCustName()));
		result.set("model_caseNo", Util.trim(meta.getCaseNo()));
		result.set("model_ctrTypeDesc", get_ctrType_desc(meta.getCtrType()));
		return result;
	}

	private boolean chk_rate_gt_16percent(C340M01A c340m01a)
			throws CapException {
		Map<String, Object> map = new HashMap<String, Object>();
		List<C340M01C> c340m01c_list = clsService.findC340M01C(c340m01a.getMainId());
		for (C340M01C c340m01c : c340m01c_list) {
			String jsonData = Util.trim(c340m01c.getJsonData());
			JSONObject jsonObject = DataParse.toJSON(jsonData);

			LMSUtil.addJsonToMap(map, jsonObject);
		}
		String rateRange1Oid = MapUtils.getString(map, "rateRange1Oid", "");
		String rateRange2Oid = MapUtils.getString(map, "rateRange2Oid", "");

		BigDecimal rateRange1Param07 = CapMath.getBigDecimal(
				MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RESULTRATE , ""));

		BigDecimal rateRange2Param07 = CapMath.getBigDecimal(
				MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2_RESULTRATE , ""));

		if(_rate_gt_16percent(rateRange1Oid, rateRange1Param07)
				|| _rate_gt_16percent(rateRange2Oid, rateRange2Param07)){
			return true;
		}
		return false;
	}

	private boolean _rate_gt_16percent(String rateRangeNOid, BigDecimal rateRangeNParam07){
		if(Util.isNotEmpty(rateRangeNOid)){
			L140S02D l140s02d = clsService.findL140S02D_oid(rateRangeNOid);
			if(l140s02d!=null){
				if(CrsUtil.is_fix_rate(l140s02d)){

				}else{
					if(rateRangeNParam07!=null && ClsUtility.is_rate_gt_16percent(rateRangeNParam07) ){
						return true;
					}
				}
			}
		}
		return false;
	}

	private String unKeyIn_column(C340M01A c340m01a) throws CapException {
		Map<String, Object> map = new HashMap<String, Object>();
		C340M01C c340m01c = clsService.findC340M01C(c340m01a.getMainId(), ContractDocConstants.C340M01C_ItemType.TYPE_0);
		String jsonData = Util.trim(c340m01c.getJsonData());
		JSONObject jsonObject = DataParse.toJSON(jsonData);

		LMSUtil.addJsonToMap(map, jsonObject);

		List<String> list = new ArrayList<String>();

		String[] cols_deliv = new String[] { "loanAmt", "loanPeriod", "preliminaryFee", "creditCheckFee"
				, "drawDownType", "repaymentMethod", "annualPercentageRate"
				, ContractDocConstants.CtrTypeB.PLOAN_BORROWERMOBILENUMBER
				, ContractDocConstants.CtrTypeB.PLOAN_BORROWEREMAIL
		};
		if(true){//檢核借款用途
			boolean chose_C = Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_C, ""));
			boolean chose_G = Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_G, ""));
			boolean chose_H = Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_H, ""));
			boolean chose_J = Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_J, ""));
			boolean chose_K = Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_K, ""));
			boolean chose_F = Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_F, ""));
			boolean chose_N = Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_N, ""));
			boolean chose_O = Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_O, ""));
			boolean chose_P = Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_P, ""));
			boolean chose_otherDesc = Util.isNotEmpty(Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_OTHERDESC, "")));
			if(chose_C || chose_G || chose_H || chose_J || chose_K || chose_F  || chose_N || chose_O || chose_P || chose_otherDesc){
				//ok
			}else{
				list.add(MessageFormat.format(prop_cls3401m08.getProperty("msg.unkeyin"), prop_cls3401m08.getProperty("loanPurpose")));
			}
		}
		for (String columnChk : cols_deliv) {
			if (CapString.isEmpty(MapUtils.getString(map, columnChk, ""))) {
				list.add(MessageFormat.format(prop_cls3401m08.getProperty("msg.unkeyin"),
						prop_cls3401m08.getProperty(columnChk)));
			}


			//if (!have_keyin_data(map, cols_deliv)) {
			//	list.add(MessageFormat.format(prop_cls3401m08.getProperty("msg.unkeyin"),
			//			prop_cls3401m08.getProperty("column.deliv")));
			//}
		}

		if(clsService.is_function_on_codetype("chk_ploanCtr_input")){
			String borrowerMobileNumber = Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_BORROWERMOBILENUMBER, ""));
			String borrowerEmail = Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_BORROWEREMAIL, ""));
			if(Util.isNotEmpty(borrowerMobileNumber)){
				if(borrowerMobileNumber.length()!=10){
					list.add(prop_cls3401m08.getProperty("borrowerMobileNumber")+":"+borrowerMobileNumber+" 資料格式錯誤");
				}
			}
			if(Util.isNotEmpty(borrowerEmail)){
				if(borrowerEmail.indexOf("@")<=0){
					list.add(prop_cls3401m08.getProperty("borrowerEmail")+":"+borrowerEmail+" 資料格式錯誤");
				}
			}
		}
		List<C340M01B> c340m01b_list = clsService.findC340M01B(c340m01a.getMainId());
		if (c340m01b_list != null) {
			//產品種類
			String tabMainId = c340m01b_list.get(0).getTabMainId();
			L140M01A l140m01a = clsService.findL140M01A_mainId(tabMainId);
			List<L140S02A> l140s02as = clsService.findL140S02A(l140m01a);
			L140S02A l140s02a = l140s02as.get(0);

			BigDecimal origloanAmt = l140s02a.getLoanAmt() == null ? BigDecimal.ZERO : l140s02a.getLoanAmt();
			BigDecimal loanAmt = CapMath.getBigDecimal(MapUtils.getString(map, "loanAmt", ""));

			if (loanAmt.compareTo(origloanAmt) > 0) {
				list.add("借款金額(" + loanAmt + "元)" + " > 原核准額度金額(" + origloanAmt + "元)");
			}


			List<L140S02D> l140s02ds = clsService.findL140S02D_orderByPhase(l140m01a.getMainId(), l140s02a.getSeq(),
					"Y");
			for (L140S02D l140s02d : l140s02ds) {
				Integer phase = l140s02d.getPhase();
				String l140s02dOid = l140s02d.getOid();
				BigDecimal l140s02dNowRate = l140s02d.getNowRate() == null ? BigDecimal.ZERO : l140s02d.getNowRate();
				BigDecimal l140s02d_rateUser = l140s02d.getRateUser() == null ? BigDecimal.ZERO : l140s02d.getRateUser();
				if (phase == 1) {

					String rateRange1Oid = MapUtils.getString(map, "rateRange1Oid", "");
					if(Util.isEmpty(rateRange1Oid) && Util.isNotEmpty(Util.trim(map.get(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RATEDESC)))){
						continue;
					}
					BigDecimal rateRange1Param03 = CapMath.getBigDecimal(
							MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_FIXED_RATE , ""));
					BigDecimal rateRange1Param07 = CapMath.getBigDecimal(
							MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RESULTRATE , ""));
					if (l140s02dOid.equals(rateRange1Oid)) {
						if(CrsUtil.RATE_TYPE_01.equals(l140s02d.getRateType()) && Util.equals("C01", l140s02d.getRateUserType())){
							addList_when___inputRate_lt_approveRate(list, rateRange1Param03, l140s02d_rateUser);
						}else{
							addList_when___inputRate_lt_approveRate(list, rateRange1Param07, l140s02dNowRate);
						}
					} else {
						list.add("利率資訊有誤，請刪除此筆資料，重新產生契約書");
					}

				} else if (phase == 2) {

					String rateRange2Oid = MapUtils.getString(map, "rateRange2Oid", "");
					if(Util.isEmpty(rateRange2Oid) && Util.isNotEmpty(Util.trim(map.get(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RATEDESC)))){
						continue;
					}
					BigDecimal rateRange2Param03 = CapMath.getBigDecimal(
							MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2_FIXED_RATE , ""));
					BigDecimal rateRange2Param07 = CapMath.getBigDecimal(
							MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2_RESULTRATE , ""));
					if (l140s02dOid.equals(rateRange2Oid)) {
						if(CrsUtil.RATE_TYPE_01.equals(l140s02d.getRateType()) && Util.equals("C01", l140s02d.getRateUserType())){
							addList_when___inputRate_lt_approveRate(list, rateRange2Param03, l140s02d_rateUser);
						}else{
							addList_when___inputRate_lt_approveRate(list, rateRange2Param07, l140s02dNowRate);
						}
					} else {
						list.add("利率資訊有誤，請刪除此筆資料，重新產生契約書");
					}
				}
			}

		}


		BigDecimal origPreliminaryFee = BigDecimal.ZERO;
		BigDecimal origCreditCheckFee = BigDecimal.ZERO;
		List<L140M01R> l140m01r_list = clsService.findL140M01R_exclude_feeSrc3(c340m01a.getCaseMainId());
		for (L140M01R l140m01r : l140m01r_list) {
			// select codevalue, codeDesc from com.bcodetype where codetype='cls1141_feeNo'
			String feeNo = l140m01r.getFeeNo();
			if ("01".equals(feeNo)) {
				// 開辦費=開辦費+續約作業費+變更條件手續費
				origPreliminaryFee = l140m01r.getFeeAmt() == null ? BigDecimal.ZERO : l140m01r.getFeeAmt();

			} else if ("02".equals(feeNo)) {
				// 信用查詢費
				origCreditCheckFee = l140m01r.getFeeAmt() == null ? BigDecimal.ZERO : l140m01r.getFeeAmt();
			} else if ("03".equals(feeNo) || "04".equals(feeNo)) {
				// 續約作業費
				// 變更條件手續費
				origPreliminaryFee = l140m01r.getFeeAmt() == null ? origPreliminaryFee : origPreliminaryFee.add(l140m01r.getFeeAmt());
			}
		}

		BigDecimal preliminaryFee = CapMath.getBigDecimal(MapUtils.getString(map, "preliminaryFee", ""));
		BigDecimal creditCheckFee = CapMath.getBigDecimal(MapUtils.getString(map, "creditCheckFee", ""));

		/*［開辦費、信用查詢費］是在「簽報書」層輸入，而非每１個額度明細表，都有１個自己的［開辦費、信用查詢費］欄位
		 * 原則上，信貸應該每１個簽報書，只會有１個額度明細表
		 * 為免有預期之外的狀況發生，允許契約書上的費用＜簽報書上的費用，這裡的檢核用［參數］控制 => 原則上應該啟用控管，若有例外發生，再暫時關掉
		 */
		if (clsService.is_function_on_codetype("cls3401m02_chk_fee01") && preliminaryFee.compareTo(origPreliminaryFee) < 0) {
			list.add("開辦手續費(" + LMSUtil.pretty_numStr(preliminaryFee) + "元)" + " < 原核准額度開辦手續費(" + LMSUtil.pretty_numStr(origPreliminaryFee) + "元)");
		}

		if (clsService.is_function_on_codetype("cls3401m02_chk_fee02") && creditCheckFee.compareTo(origCreditCheckFee) < 0) {
			list.add("信用查詢費(" + LMSUtil.pretty_numStr(creditCheckFee) + "元)" + " < 原核准額度信用查詢費(" + LMSUtil.pretty_numStr(origCreditCheckFee) + "元)");
		}

		return StringUtils.join(list, "<br/>");
	}


	private void addList_when___inputRate_lt_approveRate(List<String> list, BigDecimal inputRate, BigDecimal approveRate){
		if(inputRate.compareTo(approveRate) < 0){
			list.add("利率:" + inputRate.stripTrailingZeros() + "%不得低於原核准額度利率:" + approveRate.stripTrailingZeros() + "%");
		}
	}

	private String get_ctrType_desc(String ctrType) {
		if (Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_B)) {
			return prop_cls3401m08.getProperty("C340M01A.ctrType.B");
		}
		return "";
	}
	private String rptId_desc(C340M01A meta) {
		String ctrType = Util.trim(meta.getCtrType());
		String rptId = Util.trim(meta.getRptId());
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_B)){

			if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrTypeB_V202401, rptId)){
				return "2024.01版本";
			}else if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrTypeB_V202309, rptId)){
				return "2023.09版本";
			}else{
				return meta.getRptId().replace("CtrTypeB_","");
			}
		}
		return rptId;
	}

	private CapAjaxFormResult defaultResult(PageParameters params, C340M01A meta,
											CapAjaxFormResult result) throws CapException {
		// 在 UI 可調整加碼利率，連帶會影響｛計算結果利率｝，所以之前才會在 defaultResult(...) 裡去抓 c340m01c
		C340M01C c340m01c = clsService.findC340M01C(meta.getMainId(), ContractDocConstants.C340M01C_ItemType.TYPE_0);

		if (c340m01c != null) {
			JSONObject jsonData = c340m01c.getJsonData() == null ? new JSONObject() : JSONObject.fromObject(
					c340m01c.getJsonData());
			if(true){
				jsonData.put("ui_loanAcct", jsonData.opt(ContractDocConstants.CtrTypeB.PLOAN_ACCTNO_LIST));
				jsonData.remove(ContractDocConstants.CtrTypeB.PLOAN_ACCTNO_LIST);
			}
			result.putAll(jsonData);

			result.set(ContractDocConstants.CtrTypeB.PLOAN_LENDING_PLAN_OPTION, LMSUtil.getDesc(contractDocService.get_ploan_lendingPlanInfo_showOption(), Util.trim(result.get(ContractDocConstants.CtrTypeB.PLOAN_LENDING_PLAN_OPTION))));


			result.set("jsShow_rateRange1View_N2", Util.isEmpty(Util.trim(jsonData.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RATEDESC)))?"N":"Y");			
		}
		if(true){
			C340M01C c340m01c_9 = clsService.findC340M01C(meta.getMainId(), "9");
			if (c340m01c_9 != null) {
				JSONObject jsonData = c340m01c_9.getJsonData() == null ? new JSONObject() : JSONObject.fromObject(c340m01c_9.getJsonData());
				if(jsonData!=null){
					//J-111-0227_10702_B1001 pLoan開放他行扣放款帳號
					if (Util.equals(jsonData.getString("bankAcctNo"),"017")) {
						result.set("ui_loanAcct_chose", "客戶對保選擇:" + jsonData.getString("bankAcctCode")+"");
					}
					else{
						result.set("ui_loanAcct_chose", "客戶撥款選擇:"+ jsonData.getString("bankAcctNo")+"-"+ jsonData.getString("bankAcctCode")+"(分行代碼:"+ jsonData.getString("bankAcctBranchCode") +")");
						if(jsonData.containsKey("isNeedACH")){
							if(Boolean.parseBoolean(jsonData.getString("isNeedACH"))){
								result.set("ui_loanAcct_chose2", "客戶還款選擇:"+ jsonData.getString("bankACHAcctCode")+"-"+ jsonData.getString("bankACHAcctNo")+"(分行代碼:"+ jsonData.getString("bankACHAcctBranchCode") +")");
							}else if(!Boolean.parseBoolean(jsonData.getString("isNeedACH"))){
								result.set("ui_loanAcct_chose2", "客戶自行繳款");
							}
						}
					}

					String ploanStakeholder=jsonData.optString("stakeholderMap");
					if(Util.isNotEmpty(ploanStakeholder)){
						JSONObject ploanStakeholder_jsonData=JSONObject.fromObject(ploanStakeholder);
						if(!Util.isEmpty(ploanStakeholder_jsonData) && !Util.isEmpty(ploanStakeholder_jsonData.optString("remain")) && !Util.isEmpty(ploanStakeholder_jsonData.optString("law44")) && !Util.isEmpty(ploanStakeholder_jsonData.optString("law45"))){
							String signTime=meta.getPloanCtrSignTimeM()!=null?S_FORMAT.format(meta.getPloanCtrSignTimeM()):"";
							result.set("ploanStakeholder",signTime +" 銀行法:"+ploanStakeholder_jsonData.optString("remain")+"|44條:"+ploanStakeholder_jsonData.optString("law44")+"|45條:"+ploanStakeholder_jsonData.optString("law45"));
						}
					}
				}
			}
		}
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));
		result.set("custId", meta.getCustId());
		result.set("ctrType", meta.getCtrType());
		result.set("custInfo",
				Util.trim(meta.getCustId()) + " " + Util.trim(meta.getDupNo()) + " " + Util.trim(meta.getCustName()));
		result.set("ctrTypeHeaderDesc", get_ctrType_desc(meta.getCtrType()));

		//控制是否允許分行修改選項
		result.set( "houseContractReadOnly", clsService.is_function_on_codetype("houseContractReadOnly"));
		return result;
	}


	/**
	 * 線上對保作廢
	 *
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Accept)
	public IResult inValidC340M01A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A c340m01a = clsService.findC340M01A_oid(mainOid);
		if (c340m01a != null) {
			cls3401Service.inValidOnlineCtr(c340m01a);
			result.set("ploanCtrStatus", getMessage("ploanCtrStatus." + Util.trim(c340m01a.getPloanCtrStatus())));
			result.set("ploanCtrDcTime", c340m01a.getPloanCtrDcTime()==null?"":S_FORMAT.format(c340m01a.getPloanCtrDcTime()));
			result.set("ploanCtrDcUser", _id_name(c340m01a.getPloanCtrDcUser()));
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult showJSON(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();


		String mainId = params.getString(EloanConstants.MAIN_ID);
		C340M01A meta = clsService.findC340M01A_mainId(mainId);
		if(meta==null){
			throw new CapMessageException("mainId="+mainId+" not found", getClass());
		}
		if(true){
			List<C340M01C> c340m01c_list = clsService.findC340M01C(meta.getMainId());
			for(C340M01C c340m01c : c340m01c_list){
				result.set("c340m01c_"+c340m01c.getItemType(), c340m01c.getJsonData());
			}
			result.set("mainId", meta.getMainId());
		}
		return result;
	}
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult queryCalculateRate(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		Map<String, Object> map = new HashMap<String, Object>();
		List<C340M01C> c340m01c_list = clsService.findC340M01C(mainId);
		for (C340M01C c340m01c : c340m01c_list) {
			String jsonData = Util.trim(c340m01c.getJsonData());
			JSONObject jsonObject = DataParse.toJSON(jsonData);

			LMSUtil.addJsonToMap(map, jsonObject);
		}

		List<String> list = new ArrayList<String>();


		Integer loan = Util.parseInt(params.getString("loanAmt"));
		Integer bufferMonth = 0;
		Integer cal_Type = 1 ;
		Integer limitMonth = Util.parseInt(params.getString("loanPeriod"));
		Integer preliminaryFee = Util.parseInt(params.getString("preliminaryFee"));
		Integer creditCheckFee = Util.parseInt(params.getString("creditCheckFee"));
		List<C340M01B> c340m01b_list = clsService.findC340M01B(mainId);
		String rate = "";

		if(loan>0 && limitMonth>0){
			List<Double> multipleRate= new ArrayList<Double>();
			List<Integer> multipleRate_TimeStart= new ArrayList<Integer>();
			List<Integer> multipleRate_TimeEnd= new ArrayList<Integer>();
			List<Integer> costList = new ArrayList<Integer>();
			String prodKind = "";
			BigDecimal nowRate = BigDecimal.ZERO;
			if(preliminaryFee>0){
				costList.add(preliminaryFee);
			}
			if(creditCheckFee>0){
				costList.add(creditCheckFee);
			}

			if (c340m01b_list != null) {
				//產品種類
				String tabMainId = c340m01b_list.get(0).getTabMainId();
				L140M01A l140m01a = clsService.findL140M01A_mainId(tabMainId);
				List<L140S02A> l140s02as = clsService.findL140S02A(l140m01a);
				L140S02A l140s02a = l140s02as.get(0);
				List<L140S02D> l140s02ds = clsService.findL140S02D_orderByPhase(tabMainId, l140s02a.getSeq(),
						"Y");
				boolean is_only_M3_and_pmRate_0 = ClsUtility.is_only_M3_and_pmRate_0(l140s02ds);
				boolean is_only_MR_and_pmRate_0 = ClsUtility.is_only_MR_and_pmRate_0(l140s02ds);
				prodKind = l140s02a.getProdKind();
				//利率資訊
				if(is_only_M3_and_pmRate_0 || is_only_MR_and_pmRate_0){
					List<Map<String, Object>> lrRate_list = misMislnratService.findMislnratByLRRate(CrsUtil.RATE_TYPE_M3, "TWD" );
					if (is_only_MR_and_pmRate_0) {
						lrRate_list = misMislnratService.findMislnratByLRRate(CrsUtil.RATE_TYPE_MR, "TWD" );
					}
					String rateValue = "";
					if(lrRate_list.size() > 0){
						rateValue = LMSUtil.pretty_numStr(CrsUtil.parseBigDecimal(MapUtils.getObject(lrRate_list.get(0), "LR_RATE")));
					}
					String bgnNum = "";
					String endNum = "";
					if(l140s02ds.size()>0){
						multipleRate_TimeStart.add((l140s02ds.get(0).getBgnNum()));

						multipleRate_TimeEnd.add((l140s02ds.get(l140s02ds.size()-1).getEndNum()));
					}
					multipleRate.add(Util.parseDouble(rateValue));
					limitMonth=(l140s02ds.get(l140s02ds.size()-1).getEndNum());
				}else{
					for (L140S02D l140s02d : l140s02ds) {
						Integer phase = l140s02d.getPhase();
						String l140s02dOid = l140s02d.getOid();
						BigDecimal l140s02dNowRate = l140s02d.getNowRate() == null ? BigDecimal.ZERO : l140s02d.getNowRate();
						BigDecimal l140s02d_rateUser = l140s02d.getRateUser() == null ? BigDecimal.ZERO : l140s02d.getRateUser();
						nowRate = l140s02d.getNowRate() != null ? l140s02d.getNowRate().setScale(2, BigDecimal.ROUND_DOWN) : nowRate;
						if (phase == 1) {

							String rateRange1Oid = MapUtils.getString(map, "rateRange1Oid", "");
							if(Util.isEmpty(rateRange1Oid) && Util.isNotEmpty(Util.trim(map.get(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RATEDESC)))){
								continue;
							}
							BigDecimal rateRange1Param03 = CapMath.getBigDecimal(
									MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_FIXED_RATE , ""));
							BigDecimal rateRange1Param07 = CapMath.getBigDecimal(
									MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RESULTRATE , ""));
							if (l140s02dOid.equals(rateRange1Oid)) {
								if(CrsUtil.RATE_TYPE_01.equals(l140s02d.getRateType()) && Util.equals("C01", l140s02d.getRateUserType())){
//									multipleRate.add(l140s02d_rateUser.doubleValue());
									multipleRate.add(rateRange1Param03.doubleValue());
								}else{
//									multipleRate.add(l140s02dNowRate.doubleValue());
									multipleRate.add(rateRange1Param07.doubleValue());
								}
								multipleRate_TimeStart.add(l140s02d.getBgnNum());
								multipleRate_TimeEnd.add(l140s02d.getEndNum());
							} else {
								list.add("利率資訊有誤，請刪除此筆資料，重新產生契約書");
							}

						} else if (phase == 2) {

							String rateRange2Oid = MapUtils.getString(map, "rateRange2Oid", "");
							if(Util.isEmpty(rateRange2Oid) && Util.isNotEmpty(Util.trim(map.get(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RATEDESC)))){
								continue;
							}
							BigDecimal rateRange2Param03 = CapMath.getBigDecimal(
									MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2_FIXED_RATE , ""));
							BigDecimal rateRange2Param07 = CapMath.getBigDecimal(
									MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2_RESULTRATE , ""));
							if (l140s02dOid.equals(rateRange2Oid)) {
								if(CrsUtil.RATE_TYPE_01.equals(l140s02d.getRateType()) && Util.equals("C01", l140s02d.getRateUserType())){
//									multipleRate.add(l140s02d_rateUser.doubleValue());
									multipleRate.add(rateRange2Param03.doubleValue());
								}else{
//									multipleRate.add(l140s02dNowRate.doubleValue());
									multipleRate.add(rateRange2Param07.doubleValue());
								}
								multipleRate_TimeStart.add(l140s02d.getBgnNum());
								multipleRate_TimeEnd.add(l140s02d.getEndNum());
							} else {
								list.add("利率資訊有誤，請刪除此筆資料，重新產生契約書");
							}
						}
					}
					limitMonth=(l140s02ds.get(l140s02ds.size()-1).getEndNum());
				}

			}
			rate = clsService.calculateRate(loan,limitMonth,bufferMonth,cal_Type,multipleRate,multipleRate_TimeStart,multipleRate_TimeEnd,costList);
			//J-112-0502  調整短期循環(續約)/中期循環(續約)總費用百分率
			if(Util.isEmpty(rate) && ("02".equals(prodKind) || "68".equals(prodKind))){
				rate = nowRate.toString();
			}
		}

		if(Util.isNotEmpty(rate) && list.size()==0){
			result.set("msg",true);
			result.set("rate",rate);
		}
		else if(list.size()>0){
			throw new CapMessageException(StringUtils.join(list, "<br/>"), getClass());
		}

		return result;
	}

	//無紙化簽報書自動產生對保契約書
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newC340M01A_byL120M01A(PageParameters params)
			throws Throwable {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();

		String caseMainId = Util.trim(params.getString("caseMainId"));
		String decisionExpr = "ok";
		String ctrType = Util.trim(
				params.getString("ctrType", ContractDocConstants.C340M01A_CtrType.Type_B));

		String msg ="";

		L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);
		String custId = l120m01a.getCustId();
		String dupNo = l120m01a.getDupNo();
		L140M01A l140m01a = clsService.findL140m01aListByL120m01cMainIdAndProperty(caseMainId,null,UtilConstants.Cntrdoc.Property.新做).get(0);
		String tabMainId = l140m01a.getMainId();
		String txCode = Util.trim(params.getString(EloanConstants.TRANSACTION_CODE));
		// UPGRADE: 待確認，URL是否正確
		String docUrl = params.getString("docUrl");
		C340M01A c340m01a = new C340M01A();

		//產生對保契約書
		cls3401Service.init_C340M01ARelateCtrTypeB(c340m01a,custId,dupNo,ctrType,caseMainId,tabMainId,user.getUnitNo(),"system",txCode,docUrl);
		//對保契約書細部設定
		cls3401Service.init_C340RelateCtrTypeB(c340m01a, caseMainId, tabMainId);

		//預設deleteTime有壓日期，且Updater會是簽報書覆核人，故需要壓掉避免同一人檢核失敗
		c340m01a.setUpdater("system");
		c340m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		c340m01a.setDeletedTime(null);
		cls3401Service.saveTemporaryMeta(c340m01a);

		//檢查對保契約是否有問題
		cls3401Service.check_CtrTypeB(c340m01a, user.getUserId(),decisionExpr, prop_cls3401m08,prop_abstractEloanPage);

		List<C340M01C> c340m01c_list = clsService.findC340M01C(c340m01a.getMainId());
		Map<String, Object> map = new HashMap<String, Object>();
		for (C340M01C c340m01c : c340m01c_list) {
			String jsonData = Util.trim(c340m01c.getJsonData());
			JSONObject jsonObject = DataParse.toJSON(jsonData);

			LMSUtil.addJsonToMap(map, jsonObject);
		}

		String annualPercentageRate = MapUtils.getString(map, "annualPercentageRate", "");
		msg = msg + unKeyIn_column(c340m01a);
		if (Util.isEmpty(annualPercentageRate)) {
			//更新docLog，將建檔人員改為system
			cls3401Service.updateDocLogUser(c340m01a.getOid(),"system");
			result.set("msg", "年費用總百分率無法計算，已產製對保契約書至編製中，請經辦編輯後自行送出。");
		}else if(Util.isNotEmpty(msg)){
			cls3401Service.updateDocLogUser(c340m01a.getOid(),"system");
			result.set("msg", msg + "，已產製對保契約書至編製中，請經辦編輯後自行送出。");
		}
		else{
			if (Util.isNotEmpty(c340m01a)) {
				try {
					//產至待覆核
					cls3401Service.flowAction(c340m01a, false, "autoCreate");

					//更新docLog，將建檔人員改為system
					cls3401Service.updateDocLogUser(c340m01a.getOid(),"system");

					//自動覆核完成對保契約書，並發電文至PLOAN
					cls3401Service.flowAction(c340m01a, true, "ok");

					//覆核完成再把Updater壓回簽報書覆核人
					c340m01a.setUpdater(user.getUserId());
					c340m01a.setUpdateTime(CapDate.getCurrentTimestamp());
					cls3401Service.saveTemporaryMeta(c340m01a);

					result.set("msg", "線上對保契約書已覆核，已傳送簡訊及email通知客戶對保。");
				}
				catch (Throwable ex){
					logger.error(StrUtils.getStackTrace(ex));
					//退回至編制中
					cls3401Service.flowAction(c340m01a, false, "autoCreate");
					result.set("msg", "對保契約書產製異常，已產製對保契約書至編製中，請經辦編輯後自行送出。");
				}
			}
			else{
				result.set("msg", "對保契約書產製異常，請經辦自行產製對保契約書。");
			}
		}

		return result;
	}
	/**
	 * 判斷登入者是否僅有EL電銷權限
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult check_only_expermission(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		result.set("only_ex_permission", user.isEXAuth());//是否僅有電銷權限
		return result;
	}
	
	/**
	 * J_113_0050  線上房貸增貸對保契約書-頁簽控制
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult checkBookmark(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<String> hideBook = new ArrayList<String>();
		
		//J_113_0050  線上房貸增貸對保契約書-約據頁簽
		String J_113_0050_ON = Util.trim(sysParameterService.getParamValue("J_113_0050_ON"));
		if(!"Y".equals(J_113_0050_ON)){
			hideBook.add("#tab02");
		}
		
		result.set("hideBook", hideBook);
		return result;
	}
}
