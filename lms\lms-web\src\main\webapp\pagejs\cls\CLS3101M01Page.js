var initDfd = $.Deferred();
var _handler = "cls3101m01formhandler";

$(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	var initControl_lockDoc = false;
	if(true){
		$.form.init({
			formHandler:_handler, 
			formAction:'queryC310M01A', 
			loadSuccess:function(json){			
				
				// 控制頁面 Read/Write
				if(!$("#buttonPanel").find("#btnSave").is("button") || json.lock) {
					tabForm.lockDoc();
					initControl_lockDoc = true;
				}
				
				tabForm.injectData(json);
				initDfd.resolve(json);
				
				if(json.bossId) {
					$("#bossId").html(json.bossId);
				}	
				
				ilog.debug("grid:: queryC900S02F["+json.refMainId+"]");
				var $gridview = $("#grid_C900S02F").iGrid({
			        handler: "cls3101gridhandler",
			        height: 140,
			        needPager: false, 
			        shrinkToFit: false,
			        multiselect: false,  
			        sortname: 'rel_custId|rel_dupNo' ,
			       	sortorder: 'asc|asc' ,
			        postData: {
			              'formAction' : "queryC900S02F"
			            , 'mainId' : json.refMainId
			        },
			        colModel: [
			            {
				            colHeader: " ", 
				            align: "left", width: 30, sortable: false, name: 'rel_flag'
				        }, {
				            colHeader: i18n.cls3101m01["C900S02F.rel_custId"], 
				            align: "left", width: 120, sortable: true, name: 'rel_custId'
				        }, {
				            colHeader: i18n.cls3101m01["C900S02F.rel_dupNo"], 
				            align: "left", width: 150, sortable: true, name: 'rel_dupNo'
				        }, {
				            colHeader: i18n.cls3101m01["C900S02F.rel_cname"], 
				            align: "left", width: 200, sortable: false, name: 'rel_cname'
				        }
				     ],
					ondblClickRow : function(rowid){
						
					}
			    });
		}});
	}
	//================================
	var item;
	
	//================================
	//事件
	$("#numPerson").change(function(){
	    $('#bossItem').empty();
	    var value = $(this).val();
	    if (value) {
	        var html = '';
	        for (var i = 1; i <= value; i++) {
	            var name = 'boss' + i;
	            html += i + '. ';
	            html += '<select id="' + name + '" name="boss" class="required" CommonManager="kind:2;type:2"></select>';
	            html += '<br/>';
	        }
	        $('#bossItem').append(html).find('select').each(function(){
	            $(this).setItems({
	                item: item,
	                format: "{value} {key}"
	            });
	        });
	    }
	});


	
	btnPanel.find("#btnSave").click(function(){		
		saveAction({'allowIncomplete':'Y'}).done(function(json){
			if(json.saveOkFlag){
				if(json.IncompleteMsg){
					API.showMessage(i18n.def.saveSuccess+"<br/>-------------------<br/>"+json.IncompleteMsg);
				}else{
					API.showMessage(i18n.def.saveSuccess);	
				}	
			}
        });
	}).end().find("#btnPrint").click(function(){
		if(initControl_lockDoc) {
			printC310M01A( $("#mainOid").val() ,  $("#mainId").val() );
		}else{
			saveAction({'allowIncomplete':'Y'}).done(function(json){
				if(json.saveOkFlag){					
					printC310M01A( json.mainOid , json.mainId );
				}
	        });
		}
	}).end().find("#btnSend").click(function(){	
		saveAction().done(function(json_saveAction){
    		if(json_saveAction.saveOkFlag){
    			API.confirmMessage(i18n.def.confirmApply, function(result){
    	            if (result) {
    	            	$.ajax({
    	                    handler: _handler,
    	                    action: "checkData",
    	                    data: {}
    	                    }).done(function(json){
//    	                        $('#managerItem').empty();
    	                        $('#bossItem').empty();
    	                        item = json.bossList;
    	                        var bhtml = '1. <select id="boss1" name="boss" class="required" CommonManager="kind:2;type:2"/>';
    	                        $('#bossItem').append(bhtml).find('select').each(function(){
    	                            $(this).setItems({
    	                                item: item,
    	                                format: "{value} {key}"
    	                            });
    	                        });
//    	                        var html = '<select id="manager" name="manager" class="required" CommonManager="kind:2;type:2" />';
//    	                        $('#managerItem').append(html).find('select').each(function(){
//    	                            $(this).setItems({
//    	                                item: item,
//    	                                format: "{value} {key}"
//    	                            });
//    	                        });
    	                        
    	                        $("#selectBossBox").thickbox({                                    
                                    title: "",
                                    width: 500,
                                    height: 300,
                                    modal: true,
                                    readOnly: false,
                                    valign: "bottom",
                                    align: "center",
                                    i18n: i18n.def,
                                    buttons: {
                                        "sure": function(){
                                        
                                            var selectBoss = $("select[name^=boss]").map(function(){
                                                return $(this).val();
                                            }).toArray();
                                            
                                            for (var i in selectBoss) {
                                                if (selectBoss[i] == "") {
                                                    // C250M01E.error2=請選擇
                                                    // C250M01E.bossId=授信主管
                                                    return CommonAPI.showErrorMessage(i18n.cls3101m01['msg.01'] + i18n.cls3101m01['C310M01E.bossId']);
                                                }
                                            }
                                            // C310M01E.managerId=經副襄理
//                                            if ($("#manager").val() =="") {                                                 
//                                                return CommonAPI.showErrorMessage(i18n.cls3101m01['msg.01'] +i18n.cls3101m01['C310M01E.managerId']);
//                                            }
                                            // 驗證是否有重複的主管
                                            if (checkArrayRepeat(selectBoss)) {
                                                // 主管人員名單重複請重新選擇
                                                return CommonAPI.showErrorMessage(i18n.cls3101m01['msg.02']);
                                            }
                                            
                                            flowAction( $.extend(
                                            		{'decisionExpr':'呈主管'}
                                            		, {'saveData':'Y', 'selectBoss': selectBoss} //, 'manager':$("#manager").val()
                                            	) 
                                            	);           
                                        },
                                        
                                        "cancel": function(){
                                            $.thickbox.close();
                                        }
                                    }
                                });
    	                });
    	            	
    	        	}
    	    	});
    		}
    	});
	}).end().find("#btnAccept").click(function(){
		var _id = "_div_btnAccept";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");

			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.def['accept']+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.def['return']+"</label></p>");

			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380,
            height: 180,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    if(val=="1"){
                    	flowAction({'decisionExpr':'核定'});
                    }else if(val=="2"){
                    	flowAction({'decisionExpr':'退回'});
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	}).end().find("#btnPrint").click(function(){
	});	
	
	// 檢查陣列內容是否重複
    function checkArrayRepeat(arrVal){
        var newArray = [];
        for (var i = arrVal.length; i--;) {
            var val = arrVal[i];
            if ($.inArray(val, newArray) == -1) {
                newArray.push(val);
            }
            else {
                return true;
            }
        }
        return false;
    }
    
	var flowAction = function(opts){
		return $.ajax({
            type: "POST",
            handler: _handler, action: "flowAction",
            data:$.extend( {
            	mainOid: $("#mainOid").val(), 
            	mainDocStatus: $("#mainDocStatus").val() 
                }
                , ( opts||{} )
            )                
            }).done(function(json){            	
            	API.triggerOpener();//gridview.reloadGrid 
            	window.close();            	
        });
	}
   
});

function saveAction(opts){
	var tabForm = $("#tabForm");
	if(tabForm.valid()){
		var optsPage = {};
		
		return $.ajax({
            type: "POST",
            handler: _handler,
            data:$.extend( {
            	formAction: "saveMain",
                page: responseJSON.page,
                mainOid: responseJSON.mainOid
                }, 
                tabForm.serializeData(),
                optsPage,
                ( opts||{} )
            )                
            }).done(function(json){
            	tabForm.injectData(json);
            	
            	//更新 opener 的 Grid
                CommonAPI.triggerOpener("gridview", "reloadGrid");
        });
	}else{
		return $.Deferred();
	}
}


function printC310M01A(param_oid, param_mainId){
	$.form.submit({
        url: "../../simple/FileProcessingService",
        target: "_blank",
        data: {
        	mainOid: param_oid,
        	mainId: param_mainId,            
            fileDownloadName: "cls3101r01.pdf",
            serviceName: "cls3101r01rptservice"                        
        }
    });
}