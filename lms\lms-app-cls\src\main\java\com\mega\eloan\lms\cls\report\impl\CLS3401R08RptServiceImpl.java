package com.mega.eloan.lms.cls.report.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.JSONUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ContractDocService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.pages.CLS3401M08Page;
import com.mega.eloan.lms.cls.report.CLS3401R08RptService;
import com.mega.eloan.lms.cls.service.CLS3401Service;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.C340M01C;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;


@Service("cls3401r08rptservice")
public class CLS3401R08RptServiceImpl extends AbstractReportService implements CLS3401R08RptService  {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS3401R08RptServiceImpl.class);
	
	@Resource
	LMSService lmsService;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	CLSService clsService;

	@Resource
	CLS3401Service cls3401service;

	@Resource
	ContractDocService contractDocService;
	
	@Override
	public String getReportTemplateFileName() {
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		// 測試用
		return "report/cls/CLS3401R08_" + locale.toString() + ".rpt";
	}

	@Override
	public void setReportData(ReportGenerator rptGenerator,
			PageParameters params) throws CapException, ParseException {
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS3401M08Page.class);
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		try {
			C340M01A meta = clsService.findC340M01A_oid(mainOid);
			String cntrNo = meta.getC340m01bs().get(0).getCntrNo();
			String ownBrId = meta.getOwnBrId();
			C340M01C c340m01c = meta.getC340m01cs().get(0);
			String content = "";
			if (c340m01c == null || CapString.isEmpty(c340m01c.getJsonData())) {
				content = "{}";
			} else {
				content = c340m01c.getJsonData();
			}
			JSONObject jsContent = JSONObject.fromObject(content);
			if(true){
				List<String> target_acctNo_list = new ArrayList<String>();
				List<Object> acctNo_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsContent, ContractDocConstants.CtrTypeB.PLOAN_ACCTNO_LIST);
				if(acctNo_list.size()==1 && Util.equals("[]", StringUtils.join(acctNo_list, ","))){
					//放款帳號, 若只有[]
				}else{
					for(Object o_acctNo : acctNo_list){
						String acctNo = Util.trim(o_acctNo);
						target_acctNo_list.add(acctNo);
					}	
				}
				jsContent.put(ContractDocConstants.CtrTypeB.PLOAN_ACCTNO_LIST, StringUtils.join(target_acctNo_list, ","));	
			}
			
			//J-113-0050 多選checkbox的陣列值會造成列印有exception,約據頁簽目前無列印功能所以將其都先拿掉
			jsContent.remove("collateralContractTerms");
			jsContent.remove("cbAfft1_5");
			jsContent.remove("cbAfft5_5");
			
			rptVariableMap.putAll(JSONUtil.parseJsonToStringMap(jsContent));
			//fix loanAmt如果json格式是integer產報表會跳錯
			rptVariableMap.put("loanAmt", NumConverter.addComma(Util.trim(jsContent.getString("loanAmt"))));
			if(true){
				List<String> loanPurpose_list = new ArrayList<String>();
				if(Util.equals("Y", jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_C))){
					loanPurpose_list.add(prop.getProperty("loanPurpose.C"));
				}
				if(Util.equals("Y", jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_G))){
					loanPurpose_list.add(prop.getProperty("loanPurpose.G"));
				}
				if(Util.equals("Y", jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_H))){
					loanPurpose_list.add(prop.getProperty("loanPurpose.H"));
				}
				if(Util.equals("Y", jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_J))){
					loanPurpose_list.add(prop.getProperty("loanPurpose.J"));
				}
				if(Util.equals("Y", jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_K))){
					loanPurpose_list.add(prop.getProperty("loanPurpose.K"));
				}
				if(Util.equals("Y", jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_F))){
					loanPurpose_list.add(prop.getProperty("loanPurpose.F"));
				}
				if(Util.equals("Y", jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_N))){
					loanPurpose_list.add(prop.getProperty("loanPurpose.N"));
				}
				if(Util.equals("Y", jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_O))){
					loanPurpose_list.add(prop.getProperty("loanPurpose.O"));
				}
				if(Util.equals("Y", jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_P))){
					loanPurpose_list.add(prop.getProperty("loanPurpose.P"));
				}
				String loanPurpose_otherDesc = Util.trim(jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_OTHERDESC));
				if(Util.isEmpty(loanPurpose_otherDesc)){
					//	
				}else{
					loanPurpose_list.add(prop.getProperty("loanPurpose.otherDesc")+"："+loanPurpose_otherDesc);
				}
				rptVariableMap.put("loanPurpose", StringUtils.join(loanPurpose_list, "、"));
			}
			
			if(true){
				List<Object> otherInfoDesc_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsContent, ContractDocConstants.CtrTypeB.PLOAN_OTHERINFODESC);
				rptVariableMap.put(ContractDocConstants.CtrTypeB.PLOAN_OTHERINFODESC, StringUtils.join(otherInfoDesc_list, "\r\n"));
			}
			
			if(true){
				String courtName = rptVariableMap.get(ContractDocConstants.CtrTypeB.PLOAN_COURT_NAME);
				if(Util.isEmpty(courtName)){
					courtName = "　　"; //2個全型空白
				}
				rptVariableMap.put(ContractDocConstants.CtrTypeB.PLOAN_COURT_NAME
						, prop.getProperty("label.countName.prefix")+" "+courtName+" "+prop.getProperty("label.countName.postfix"));	
			}			
			
			if(true){
				rptVariableMap.put(ContractDocConstants.CtrTypeB.PLOAN_LENDING_PLAN_OPTION
						, LMSUtil.getDesc(contractDocService.get_ploan_lendingPlanInfo_showOption(), rptVariableMap.get(ContractDocConstants.CtrTypeB.PLOAN_LENDING_PLAN_OPTION)));
			}
			rptVariableMap.put("rptId_desc", rptId_desc(meta));
			rptVariableMap.put("rateDesc", StringUtils.join(contractDocService.geCtrTypeB_rateDesc(jsContent), "\n"));
			rptVariableMap.put("cntrNo", cntrNo);
			rptVariableMap.put("caseNo", meta.getCaseNo());
			rptVariableMap.put("custName", meta.getCustId() + " " + meta.getCustName());
			rptVariableMap.put("branchName", StrUtils.concat(ownBrId, " ", branchService.getBranchName(ownBrId)));
			rptVariableMap.put("ploanCtrNo", Util.trim(meta.getPloanCtrNo()));

//			if(Util.equals("Y", MapUtils.getString(rptVariableMap, ContractDocConstants.CtrTypeB.PLOAN_IS_CNTRNO_BELONG_CO70647919_C101))){
//				String[] num_arr = new String[]{ "payeeTotalAmt", "payeeSelfProvide", "payeeRemittance"};
//				for(String num_col:num_arr){
//					if(!rptVariableMap.containsKey(num_col)){
//						continue;
//					}
//					rptVariableMap.put(num_col, NumConverter.addComma(rptVariableMap.get(num_col)));
//				}
//			}

			//J-112-0205 修正代償欄位導致列印錯誤
			List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
			rptVariableMap.put("repaymentList","");
			rptVariableMap.put("isRepayment",Util.isEmpty(jsContent.optString("isRepayment"))? "N" : jsContent.optString("isRepayment"));

			// -----------------------------------第一區段資料開始，列印客戶基本資料------------------------------
			Map<String, String> values = reNewHashMapParams();

			values.put("ReportBean.column40", "section1");

			titleRows.add(values);
			// -----------------------------------第一區段資料結束--------------------------------------------------------

			// -----------------------------------第二區段資料開始，列印代償資料----------------------------------
			if (Util.equals(jsContent.optString("isRepayment"),"Y")) {
				JSONArray repaymentList = jsContent.getJSONArray("repaymentList");
				int t_arr_size = repaymentList.size();
				values = reNewHashMapParams();
				for(int t_idx=0 ; t_idx < t_arr_size ; t_idx++) {
					JSONObject t_obj = repaymentList.getJSONObject(t_idx);
					values = values == null ? reNewHashMapParams() : values;
					values.put("ReportBean.column40", "section2");
					values.put("ReportBean.column01",Util.trim(t_obj.optString("bankName")));
					values.put("ReportBean.column02",Util.trim(t_obj.optString("repaymentProductType")));
					values.put("ReportBean.column03",Util.trim(t_obj.optString("originalAmt")));
					titleRows.add(values);
					values = null;
				}
			}
			else{
				values = reNewHashMapParams();
				values.put("ReportBean.column40", "section2");
			}
			// -----------------------------------第二區段資料結束--------------------------------------------------------

			// -----------------------------------第三區段資料開始，列印保證人、認股基本資料--------------------------------------------------------
			values = reNewHashMapParams();
			values.put("ReportBean.column40", "section3");

			titleRows.add(values);
			// -----------------------------------第三區段資料結束--------------------------------------------------------

			rptGenerator.setRowsData(titleRows);

			rptGenerator.setVariableData(rptVariableMap);
		} catch(Exception e) {
			LOGGER.error(StrUtils.getStackTrace(e));
		}
		
	}

	private String rptId_desc(C340M01A meta) {
		String ctrType = Util.trim(meta.getCtrType());
		String rptId = Util.trim(meta.getRptId());
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_B)){

			if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrTypeB_V202309, rptId)){
				return "2023.09版本";
			}
		}
		return rptId;
	}

	private Map<String, String> reNewHashMapParams() {
		Map<String, String> values = new HashMap<String, String>();
		for (int i = 1; i <= 60; i++) {
			values.put("ReportBean.column" + String.format("%02d", i), "");
		}
		return values;
	}
}
