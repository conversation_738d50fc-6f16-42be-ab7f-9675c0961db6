/* 
 * L800M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 常用主管資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L800M01A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"brno", "zhuGuan" }))
public class L800M01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 單位代號 **/
	@Size(max = 3)
	@Column(name = "BRNO", length = 3, columnDefinition = "CHAR(03)")
	private String brno;

	/** 主管員工號碼 **/
	@Size(max = 6)
	@Column(name = "ZHUGUAN", length = 6, columnDefinition = "CHAR(6)")
	private String zhuGuan;

	/**
	 * 資料類型
	 * <p/>
	 * 1企金<br/>
	 * 2個金<br/>
	 * 3.通用
	 */
	@Size(max = 1)
	@Column(name = "DATATYPE", length = 1, columnDefinition = "CHAR(1)")
	private String dataType;

	/** 主管姓名 **/
	@Size(max = 48)
	@Column(name = "ZGNAME", length = 48, columnDefinition = "VARCHAR(48)")
	private String ZGName;

	/**
	 * 是否為帳戶管理員
	 * <p/>
	 * V:是
	 */
	@Size(max = 1)
	@Column(name = "ISTYPE1", length = 1, columnDefinition = "CHAR(1)")
	private String isType1;

	/**
	 * 是否為授信主管
	 * <p/>
	 * V:是
	 */
	@Size(max = 1)
	@Column(name = "ISTYPE2", length = 1, columnDefinition = "CHAR(1)")
	private String isType2;

	/**
	 * 是否為授權主管
	 * <p/>
	 * V:是
	 */
	@Size(max = 1)
	@Column(name = "ISTYPE3", length = 1, columnDefinition = "CHAR(1)")
	private String isType3;

	/**
	 * 是否為單位副主管
	 * <p/>
	 * V:是
	 */
	@Size(max = 1)
	@Column(name = "ISTYPE4", length = 1, columnDefinition = "CHAR(1)")
	private String isType4;

	/**
	 * 是否為單位主管
	 * <p/>
	 * V:是
	 */
	@Size(max = 1)
	@Column(name = "ISTYPE5", length = 1, columnDefinition = "CHAR(1)")
	private String isType5;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得單位代號 **/
	public String getBrno() {
		return this.brno;
	}

	/** 設定單位代號 **/
	public void setBrno(String value) {
		this.brno = value;
	}

	/** 取得主管員工號碼 **/
	public String getZhuGuan() {
		return this.zhuGuan;
	}

	/** 設定主管員工號碼 **/
	public void setZhuGuan(String value) {
		this.zhuGuan = value;
	}

	/**
	 * 取得資料類型
	 * <p/>
	 * 1企金<br/>
	 * 2個金<br/>
	 * 3.通用
	 */
	public String getDataType() {
		return this.dataType;
	}

	/**
	 * 設定資料類型
	 * <p/>
	 * 1企金<br/>
	 * 2個金<br/>
	 * 3.通用
	 **/
	public void setDataType(String value) {
		this.dataType = value;
	}

	/** 取得主管姓名 **/
	public String getZGName() {
		return this.ZGName;
	}

	/** 設定主管姓名 **/
	public void setZGName(String value) {
		this.ZGName = value;
	}

	/**
	 * 取得是否為帳戶管理員
	 * <p/>
	 * V:是
	 */
	public String getIsType1() {
		return this.isType1;
	}

	/**
	 * 設定是否為帳戶管理員
	 * <p/>
	 * V:是
	 **/
	public void setIsType1(String value) {
		this.isType1 = value;
	}

	/**
	 * 取得是否為授信主管
	 * <p/>
	 * V:是
	 */
	public String getIsType2() {
		return this.isType2;
	}

	/**
	 * 設定是否為授信主管
	 * <p/>
	 * V:是
	 **/
	public void setIsType2(String value) {
		this.isType2 = value;
	}

	/**
	 * 取得是否為授權主管
	 * <p/>
	 * V:是
	 */
	public String getIsType3() {
		return this.isType3;
	}

	/**
	 * 設定是否為授權主管
	 * <p/>
	 * V:是
	 **/
	public void setIsType3(String value) {
		this.isType3 = value;
	}

	/**
	 * 取得是否為單位副主管
	 * <p/>
	 * V:是
	 */
	public String getIsType4() {
		return this.isType4;
	}

	/**
	 * 設定是否為單位副主管
	 * <p/>
	 * V:是
	 **/
	public void setIsType4(String value) {
		this.isType4 = value;
	}

	/**
	 * 取得是否為單位主管
	 * <p/>
	 * V:是
	 */
	public String getIsType5() {
		return this.isType5;
	}

	/**
	 * 設定是否為單位主管
	 * <p/>
	 * V:是
	 **/
	public void setIsType5(String value) {
		this.isType5 = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	@Override
	public String getMainId() {
		return null;
	}

	public String getZGtype() {
		String result = "";
		if (Util.isNotEmpty(this.isType1)) {
			result += "1,";
		}
		if (Util.isNotEmpty(this.isType2)) {
			result += "2,";
		}
		if (Util.isNotEmpty(this.isType3)) {
			result += "3,";
		}
		if (Util.isNotEmpty(this.isType4)) {
			result += "4,";
		}
		if (Util.isNotEmpty(this.isType5)) {
			result += "5,";
		}
		result = result.length() > 0 ? result.substring(0, result.length() - 1)
				: null;
		return result;

	}
}
