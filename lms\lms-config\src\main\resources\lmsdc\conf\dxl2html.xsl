<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0" xmlns:d="http://www.lotus.com/dxl">
	<xsl:variable name="uid">
		<xsl:value-of select="//d:note/d:noteinfo/@unid"/>
	</xsl:variable>
	<xsl:variable name="pname">
		<xsl:value-of select="//d:note/d:item[@name='DocTitle']"/>
	</xsl:variable>
	<xsl:template match="/">
		<xsl:apply-templates select="//d:note"/>
	</xsl:template>
	<xsl:template match="d:note">
		<xsl:apply-templates select="d:item"/>
	</xsl:template>
	<xsl:template match="d:item">
		<xsl:apply-templates select="d:richtext"/>
	</xsl:template>
	<xsl:template match="d:richtext">
		<xsl:apply-templates/>
	</xsl:template>
	<xsl:template match="d:table">
		<table width="100%" cellspacing="0" cellpadding="0">
			<xsl:attribute name="width"><xsl:if test="@widthtype='fitmargins'">100%</xsl:if></xsl:attribute>
			<xsl:attribute name="style">border-color: black ;border-collapse: collapse;<xsl:if test="string(d:border/@style)"> border-style: <xsl:choose><xsl:when test="d:border/@style='dot'">dotted</xsl:when><xsl:when test="d:border/@style='dash'">dashed</xsl:when><xsl:otherwise><xsl:value-of select="d:border/@style"/></xsl:otherwise></xsl:choose>;</xsl:if><xsl:if test="string(d:border/@width)"> border-width: <xsl:value-of select="d:border/@width"/>;</xsl:if><xsl:if test="string(d:border/@color)"> border-color: <xsl:value-of select="d:border/@color"/>;</xsl:if></xsl:attribute>
			<tr>
				<xsl:for-each select="d:tablecolumn">
					<!--This sets the column widths-->
					<th style="border: 0px 0px 0px 0px;">
						<xsl:attribute name="width"><xsl:choose><xsl:when test="contains(@width, 'in')"><xsl:variable name="wd"><xsl:value-of select="substring-before(@width, 'in')"/></xsl:variable><xsl:value-of select="70*$wd"/></xsl:when><xsl:otherwise><xsl:value-of select="@width"/></xsl:otherwise></xsl:choose></xsl:attribute>
					</th>
				</xsl:for-each>
			</tr>
			<xsl:apply-templates/>
		</table>
	</xsl:template>
	<xsl:template match="d:tablerow">
		<tr>
			<xsl:apply-templates/>
		</tr>
	</xsl:template>
	<xsl:template match="d:tablecell">
		<td valign="top">
			<xsl:attribute name="style">border-width: <xsl:choose><xsl:when test="string(@borderwidth)"><xsl:value-of select="@borderwidth"/></xsl:when><xsl:otherwise>1px</xsl:otherwise></xsl:choose>; border-color: <xsl:choose><xsl:when test="string(../../@cellbordercolor)"><xsl:value-of select="../../@cellbordercolor"/></xsl:when><xsl:otherwise>black</xsl:otherwise></xsl:choose>; border-style: solid;<xsl:choose><xsl:when test="string(@bgcolor)"> background-color: <xsl:value-of select="@bgcolor"/>;</xsl:when></xsl:choose></xsl:attribute>
			<xsl:apply-templates/>
		</td>
	</xsl:template>
	<xsl:template match="d:pardef">
		<style type="text/css">
				.par_<xsl:value-of select="$uid"/>_<xsl:value-of select="@id"/> {<xsl:if test="@align!=''">text-align: <xsl:value-of select="@align"/>;</xsl:if> font-family: sans-serif; color: black; font-size: 10pt; font-weight: normal; text-decoration: none;}
			</style>
	</xsl:template>
	<xsl:template match="d:par">
		<xsl:variable name="defnum"><xsl:value-of select="@def"/></xsl:variable>
		<xsl:choose>
      <xsl:when test="contains(//d:note/d:item/d:richtext/d:pardef[@id=$defnum]/@list, 'bullet') or contains(//d:note/d:item/d:richtext/d:table/d:tablerow/d:tablecell/d:pardef[@id=$defnum]/@list, 'bullet')">
				<xsl:if test="preceding-sibling::d:par[1]/@def!=$defnum">
					<xsl:text disable-output-escaping='yes'><![CDATA[<ul>]]></xsl:text>
				</xsl:if>
					<li>
						<xsl:attribute name="class">par_<xsl:value-of select="$uid"/>_<xsl:value-of select="@def"/></xsl:attribute>
						<xsl:apply-templates/>
					</li>
				<xsl:if test="following-sibling::d:par[1]/@def!=$defnum">
					<xsl:text disable-output-escaping='yes'><![CDATA[</ul>]]></xsl:text>
				</xsl:if>
			</xsl:when>
			<xsl:when test="contains(//d:note/d:item/d:richtext/d:pardef[@id=$defnum]/@list, 'number') or contains(//d:note/d:item/d:richtext/d:table/d:tablerow/d:tablecell/d:pardef[@id=$defnum]/@list, 'number')">
				<xsl:if test="preceding-sibling::d:par[1]/@def!=$defnum">
					<xsl:text disable-output-escaping='yes'><![CDATA[<ol>]]></xsl:text>
					<xsl:attribute name="class">par_<xsl:value-of select="$uid"/>_<xsl:value-of select="@def"/></xsl:attribute>
				</xsl:if>
					<li>
						<xsl:attribute name="style">font-family: <xsl:value-of select="d:run/d:font/@name"/>; font-size: <xsl:value-of select="d:run/d:font/@size"/>;<xsl:choose><xsl:when test="contains(d:run/d:font/@style, 'bold')"> font-weight: bold;</xsl:when><xsl:otherwise> font-weight: normal;</xsl:otherwise></xsl:choose><xsl:choose><xsl:when test="contains(d:run/d:font/@style, 'italic')"> font-style: italic;</xsl:when><xsl:otherwise> font-style: normal;</xsl:otherwise></xsl:choose><!--Can't have both selected at same time--><xsl:choose><xsl:when test="contains(d:run/d:font/@style, 'superscript')"> vertical-align: super;</xsl:when><xsl:when test="contains(d:run/d:font/@style, 'subscript')"> vertical-align: sub;</xsl:when><xsl:otherwise> vertical-align: bottom;</xsl:otherwise></xsl:choose><!--Could potentially have both selected here--> text-decoration: <xsl:choose><xsl:when test="contains(d:run/d:font/@style, 'underline')">underline </xsl:when><xsl:when test="contains(d:run/d:font/@style, 'strikethrough')">line-through</xsl:when><xsl:otherwise>none</xsl:otherwise></xsl:choose>; color: <xsl:choose><xsl:when test="string(d:run/d:font/@color)"><xsl:value-of select="d:run/d:font/@color"/></xsl:when><xsl:otherwise>black</xsl:otherwise></xsl:choose>;</xsl:attribute>
						<xsl:attribute name="class">par_<xsl:value-of select="$uid"/>_<xsl:value-of select="@def"/></xsl:attribute>
						<xsl:apply-templates/>
					</li>
				<xsl:if test="following-sibling::d:par[1]/@def!=$defnum">
					<xsl:text disable-output-escaping='yes'><![CDATA[</ol>]]></xsl:text>
				</xsl:if>
			</xsl:when>
			<xsl:otherwise>
				<xsl:if test="following-sibling::d:par[1]=''">
				  <br/>
				</xsl:if>
				<div>
					<xsl:attribute name="class">par_<xsl:value-of select="$uid"/>_<xsl:value-of select="@def"/></xsl:attribute>
						<xsl:apply-templates/>
				</div>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>
	<xsl:template match="d:run">
		<span>
			<xsl:attribute name="style">font-family: <xsl:value-of select="d:font/@name"/>; font-size: <xsl:value-of select="d:font/@size"/>;<xsl:choose><xsl:when test="contains(d:font/@style, 'bold')"> font-weight: bold;</xsl:when><xsl:otherwise> font-weight: normal;</xsl:otherwise></xsl:choose><xsl:choose><xsl:when test="contains(d:font/@style, 'italic')"> font-style: italic;</xsl:when><xsl:otherwise> font-style: normal;</xsl:otherwise></xsl:choose><!--Can't have both selected at same time--><xsl:choose><xsl:when test="contains(d:font/@style, 'superscript')"> vertical-align: super;</xsl:when><xsl:when test="contains(d:font/@style, 'subscript')"> vertical-align: sub;</xsl:when><xsl:otherwise> vertical-align: bottom;</xsl:otherwise></xsl:choose><!--Could potentially have both selected here--> text-decoration: <xsl:choose><xsl:when test="contains(d:font/@style, 'underline')">underline </xsl:when><xsl:when test="contains(d:font/@style, 'strikethrough')">line-through</xsl:when><xsl:otherwise>none</xsl:otherwise></xsl:choose>; color: <xsl:choose><xsl:when test="string(d:font/@color)"><xsl:value-of select="d:font/@color"/></xsl:when><xsl:otherwise>black</xsl:otherwise></xsl:choose>;</xsl:attribute>
			<xsl:apply-templates/>&#160;
    </span>
	</xsl:template>
	<xsl:template match="d:attachmentref">
		<a target="8">
			<xsl:attribute name="href"><xsl:value-of select="$pname"/>exp_files/<xsl:value-of select="$uid"/>_<xsl:value-of select="@name"/></xsl:attribute>
			<img>
				<xsl:attribute name="height"><xsl:value-of select="d:picture/@height"/></xsl:attribute>
				<xsl:attribute name="width"><xsl:value-of select="d:picture/@width"/></xsl:attribute>
				<xsl:attribute name="src">file?id=<xsl:value-of select="d:picture/d:gif"/></xsl:attribute>
				<xsl:attribute name="border">0</xsl:attribute>
			</img>
		</a>
	</xsl:template>
	<xsl:template match="d:picture">
		<img>
			<xsl:attribute name="height"><xsl:value-of select="@height"/></xsl:attribute>
			<xsl:attribute name="width"><xsl:value-of select="@width"/></xsl:attribute>
			<xsl:attribute name="src">file?id=<xsl:value-of select="d:gif"/></xsl:attribute>
			<xsl:attribute name="border">0</xsl:attribute>
		</img>
	</xsl:template>	
	<!-- 
	<xsl:template match="d:attachmentref">
		<a target="8">
			<xsl:attribute name="href"><xsl:value-of select="$pname"/>/$FILE/<xsl:value-of select="@name"/></xsl:attribute>
			<img>
				<xsl:attribute name="height"><xsl:value-of select="d:picture/@height"/></xsl:attribute>
				<xsl:attribute name="width"><xsl:value-of select="d:picture/@width"/></xsl:attribute>
				<xsl:attribute name="src">data:image/gif;base64,<xsl:value-of select="d:picture/d:gif"/></xsl:attribute>
				<xsl:attribute name="border">0</xsl:attribute>
			</img>
		</a>
	</xsl:template>	
	<xsl:template match="d:picture">
		<img>
			<xsl:attribute name="height"><xsl:value-of select="@height"/></xsl:attribute>
			<xsl:attribute name="width"><xsl:value-of select="@width"/></xsl:attribute>
			<xsl:attribute name="src">data:image/gif;base64,<xsl:value-of select="d:gif"/></xsl:attribute>
			<xsl:attribute name="border">0</xsl:attribute>
		</img>
	</xsl:template>
	 -->
</xsl:stylesheet>