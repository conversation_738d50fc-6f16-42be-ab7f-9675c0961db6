/**
 * 用於在載入初期就做最基本檢查. 例如檢查 browser 類型.
 */

//check browser version
var checkBrowserType = 0;
var validBrowser = testBrowser();

if( checkBrowserType == 0 && ! validBrowser ) {
	// redirect right now
	processInvalidBrowser();
} else if( checkBrowserType == 1 ) {
	// redirect onload
	window.addEventListener( 'load', function(evt) {
		if ( ! validBrowser) 
			processInvalidBrowser();
	});
}

function testBrowser(){
	// Edge only
	var ua = navigator.userAgent;
	var match = /Edg\/([\d\.]+)/.exec(ua) || /Edge\/([\d\.]+)/.exec(ua) || [];
	return match.length > 0;
}

function processInvalidBrowser() {
	alert('本系統只限 Edge ,請至相關網頁下載正確版本\nThe system only use Edge, please to related website downloading correct version');
	window.location = "../error/403";
}