package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L250S02BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L250S02B;

@Repository
public class L250S02BDaoImpl extends LMSJpaDao<L250S02B, String> implements
		L250S02BDao {

	@Override
	public List<L250S02B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("type");
		search.addOrderBy("groupOrder");
		search.addOrderBy("subOrder");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L250S02B> list = createQuery(search).getResultList();
		return list;
	}
}