package com.mega.eloan.lms.lms.handler.form;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.formatter.BranchDateTimeFormatter;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.Bstbl;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.BstblService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.ScoreJP;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.base.service.ScoreServiceJP;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.pages.LMS1015M01Page;
import com.mega.eloan.lms.lms.pages.LMS1205M01Page;
import com.mega.eloan.lms.lms.service.LMS1015Service;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01B;
import com.mega.eloan.lms.model.C121M01E;
import com.mega.eloan.lms.model.C121M01F;
import com.mega.eloan.lms.model.C121S01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01M;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 消金信用評等模型
 * </pre>
 * 
 * @since 2015/3/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/3/1,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1015m01formhandler")
@DomainClass(C121M01A.class)
public class LMS1015M01FormHandler extends AbstractFormHandler {
	
	@Resource
	BranchService branchService;

	@Resource
	DocLogService docLogService;
	
	@Resource
	DocCheckService docCheckService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	NumberService numberService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	ScoreServiceJP scoreServiceJP;
	
	@Resource
	LMS1015Service lms1015Service;
	
	@Resource
	LMS1205Service lms1205Service;

	@Resource
	RetrialService retrialService;
	
	@Resource
	BstblService bstblService;
	
	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	MisGrpcmpService misGrpcmpService;
	
	
	Properties prop_abstractOverSeaCLSPage = MessageBundleScriptCreator.getComponentResource(AbstractOverSeaCLSPage.class);
	Properties prop_lms1205m01Page = MessageBundleScriptCreator.getComponentResource(LMS1205M01Page.class);
	Properties prop_lms1015m01Page = MessageBundleScriptCreator.getComponentResource(LMS1015M01Page.class);
	
	private static final String UI_PRE_C120_OID = "bef_c120m01a_oid";
	private static final String UI_C120_OID = "c120m01a_oid";
	
	private static Logger logger = LoggerFactory.getLogger(LMS1015M01FormHandler.class);
	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}

	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params)
	throws CapException {
		return _saveAction(params, "Y");
	}
	
	private CapAjaxFormResult _saveAction(PageParameters params,String tempSave)
	throws CapException{
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y", params.getString("allowIncomplete"));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		//===
		String KEY = "saveOkFlag";
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = null;
		String page = params.getString(EloanConstants.PAGE);
		if (Util.isNotEmpty(mainOid)) {
			try{
				meta = clsService.findC121M01A_oid(mainOid);	
				List<GenericBean> saveList = new ArrayList<GenericBean>();
				
				Properties prop_lms1015m01 = MessageBundleScriptCreator.getComponentResource(LMS1015M01Page.class);
				if(true){
					//在 addRatingDoc 時，預先塞入 DeletedTime
					//meta.setDeletedTime(new Timestamp(DateUtils.addDays(CapDate.getCurrentTimestamp(), 1).getTime()));
					
					boolean chkAtLeastOne = true;
					if(meta.getDeletedTime()!=null){
						chkAtLeastOne = false;
					}
					if(chkAtLeastOne){
						List<C120M01A> list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
						if(CollectionUtils.isEmpty(list)){
							Map<String, String> param = new HashMap<String, String>();
							param.put("colName", "");
							throw new CapMessageException(MessageFormat.format(prop_lms1015m01.getProperty("l120m01a.error24"),
								    param.get("colName")), getClass());

						}
					}
				}
						
				if ("01".equals(page)) {
					CapBeanUtil.map2Bean(params, meta, new String[] {	
							"lnYear", "lnMonth"
							, "repaymentSchFmt", "repaymentSchDays"
					});			
					if( ! Util.equals("2", meta.getRepaymentSchFmt())){
						meta.setRepaymentSchDays(null);
					}
				} else if ("02".equals(page)) {
					
				} else if ("03".equals(page)) {					
					C121S01A c121s01a = clsService.findC121S01A(meta);
					if(c121s01a==null){
						c121s01a = new C121S01A();
						OverSeaUtil.copyC121S01A(c121s01a, meta);
					}
					CapBeanUtil.map2Bean(params, c121s01a, new String[] {	
							"cmsType"							
							, "location"
							, "region"  , "houseAge", "houseArea"
							, "factor1", "factor2", "collUsage", "locationType"
					});
					//~~~
					saveList.add(c121s01a);
				} else if ("04".equals(page) || "05".equals(page) || "06".equals(page)) {
					String bef_c120m01a_oid = Util.trim(params.getString(UI_PRE_C120_OID));
					C120M01A c120m01a = clsService.findC120M01A_oid(bef_c120m01a_oid);
					if(Util.isEmpty(bef_c120m01a_oid)){
						throw new CapMessageException("未選取關係戶ID", getClass());	
					}
					if(c120m01a!=null){
						if ("04".equals(page)) {
							
						} else if ("05".equals(page)) {
							
						} else if ("06".equals(page)) { //調整評等，調整評等只有在1.0版本會有，所以也只有C121M01B
							String noAdj = Util.trim(params.getString("noAdj"));
							String adjustStatus = "";
							String adjustFlag = "";
							String adjustReason = "";
							String adjRating = "";
							
							
							C121M01B c121m01_grade = clsService.findC121M01B_byC120M01A(c120m01a);
							String fRating = Util.trim(c121m01_grade.getOrgFr());	
							
							if(Util.equals("2", noAdj)){
								//不符直覺：調升 or 調降
								adjustStatus = Util.trim(params.getString("adjustStatus"));							
								adjRating = Util.trim(params.getString("adjRating"));
								adjustReason = Util.trim(params.getString("adjustReason"));
								
								String sprtRating = c121m01_grade.getSprtRating();
								if(Util.equals(OverSeaUtil.DF, sprtRating)){
									adjustStatus = "3";//回復
									adjustFlag = "";
									adjustReason = "";
									adjRating = "";
									//fRating		
									noAdj = "1";//DF強迫轉為符合直覺
								}else{
									//不符直覺，且不為DF
									int i_sprtRating = Util.parseInt(sprtRating);								
									// adjustStatus 1.調升 2.調降 3.回復
									if(i_sprtRating!=0){
										int i_adjRating = Util.parseInt(adjRating);
										int raw_fRating = -1;
										String fmtMsg = "";
										if(Util.equals(adjustStatus, "2")){
											//降-不限
											raw_fRating = i_sprtRating + i_adjRating;		
											fmtMsg = MessageFormat.format(prop_lms1015m01.getProperty("fmt.downgrade") , Util.trim(i_adjRating));
										}else if(Util.equals(adjustStatus, "1")){
											//只有升等，才填理由是[1.淨資產 2.職業 3.其它]
											adjustFlag = Util.trim(params.getString("adjustFlag"));
											
											//升-以2等為限
											int maxUpRating = 2;
											if(i_adjRating>maxUpRating){											
												throw new CapMessageException(prop_lms1015m01.getProperty("msg.002"), getClass());
											}else{			
												int ajd_j10 = c121m01_grade.getAdj_j10(); 
												if(ajd_j10==99){
													//DF不能升降等												
												}else if(ajd_j10<0){
													//支援評等 被降等
												}else{
													if(i_adjRating + ajd_j10>maxUpRating){
														throw new CapMessageException(MessageFormat.format(prop_lms1015m01.getProperty("msg.003")
																, Util.trim(ajd_j10), Util.trim(maxUpRating - ajd_j10)), getClass());	
													}	
												}
											}
											raw_fRating = i_sprtRating - i_adjRating;
											fmtMsg = MessageFormat.format(prop_lms1015m01.getProperty("fmt.upgrade") , Util.trim(i_adjRating));
										}else{
											/*
											下拉選單有2個人，第1個人只 click 不符合直覺
											但未選 調升/調降 
											*/
											throw new CapMessageException(MessageFormat.format(prop_lms1015m01Page.getProperty("msg.004")
													, prop_abstractOverSeaCLSPage.getProperty("message.adjustStatus")), getClass());
										}
										if(raw_fRating!=OverSeaUtil.rating_min1_max10(raw_fRating)){
											String baseMsg = prop_lms1015m01.getProperty("C121M01B.sprtRating")+":"+Util.trim(i_sprtRating);
											throw new CapMessageException(MessageFormat.format(prop_lms1015m01.getProperty("msg.007")
													, baseMsg, fmtMsg), getClass());
										}
										fRating = Util.trim(OverSeaUtil.rating_min1_max10(raw_fRating));
									}	
								}							
							}else{
								//符合直覺
								adjustStatus = "3";//回復
								adjustFlag = "";
								adjustReason = "";
								adjRating = "";
								//fRating					
							}
							if(true){
								Timestamp nowTS = CapDate.getCurrentTimestamp();
								if(Util.isEmpty(noAdj)){
									// 若從「有值」變成「被清空」 => ratingDate也要被清空
									c121m01_grade.setRatingDate(null);
								}else{
									if(Util.equals(c121m01_grade.getNoAdj(), noAdj) 
											&& Util.equals(c121m01_grade.getAdjustStatus(), adjustStatus)
											&& Util.equals(c121m01_grade.getAdjRating(), adjRating)
											&& Util.equals(c121m01_grade.getAdjustFlag(), adjustFlag)
											){
												
									}else{
										c121m01_grade.setRatingDate(nowTS);										
									}	
								}
								if(Util.equals(c120m01a.getKeyMan(), "Y")){
									meta.setRatingDate(c121m01_grade.getRatingDate());
								}
																
								c121m01_grade.setGrdTDate(Util.equals(adjustStatus, "3")?null:nowTS);
								c121m01_grade.setAdjRating(adjRating);
								c121m01_grade.setFRating(fRating);
								if(true){ //原本的C
									JSONObject inputJson = new JSONObject();
									inputJson.put("fRating", fRating);
									JSONObject outputJson = new JSONObject();									
									//---依 fRating重算 違約機率
									//舊版的日本模型，沒有分房貸跟非房貸。
									scoreServiceJP.setJPDR(scoreServiceJP.scoreJP(ScoreJP.type.日本消金模型違約機率, inputJson, c121m01_grade.getVarVer(), OverSeaUtil.海外評等_房貸)
											, outputJson);
									DataParse.toBean(outputJson, c121m01_grade);									
								}
								c121m01_grade.setNoAdj(noAdj);
								c121m01_grade.setAdjustStatus(adjustStatus);
								c121m01_grade.setAdjustFlag(adjustFlag);
								c121m01_grade.setAdjustReason(adjustReason);
								if(true){
									c121m01_grade.setUpdater(user.getUserId());
									c121m01_grade.setUpdateTime(nowTS);
								}
								clsService.daoSave(c121m01_grade);
							}
						}		
					}
									
				} else if ("07".equals(page)) {
				}
				//~~~
				saveList.add(meta);
				clsService.save(saveList);
				if(true){
					//為免輸入理由後，忘了 click 儲存
					//即使是 tempSave, 也刪 tempData
					if(Util.equals(params.getString("page"), "06") 
							&& Util.equals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))){
						tempDataService.deleteByMainId(meta.getMainId());
					}					
				}
				//===先重算評等再去做資料檢核??
				if(true){ 
					if(Util.equals("Y", tempSave)){
						
					}else{
						/*刪除評等日期，1.0版本的評等，在重算時會把評等日期清空，再主管評等等資訊都寫完後，再填入評等日期
						 * 2.0沿用刪除部分，但會評等計算後就寫入評等日期
						*/
						lms1015Service.del_noneRating_score(meta);	
					}
					
					if(lms1015Service.should_calc_C121_score(Util.parseInt(page), meta)){ //房貸C121S01B
						String varVerNow = scoreServiceJP.get_Version_JP(); //目前適用之評等版本
						if(varVerNow.equals(OverSeaUtil.V2_0_LOAN_JP)){
							lms1015Service.calc_C121_score_v2_0(meta);
						}else{
							//檢查是否有非房貸評等資料(2.0轉回1.0會有，測試會遇到) >> 刪除C121M01F資料
							lms1015Service.del_c101m01f(meta);
							lms1015Service.calc_C121_score(meta);
						}
						
					}
				}	
				
				if(Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
					//在tempSave<>Y,若有未填欄位,丟 CapMessageException, 讓 saveOkFlag==false
					
					List<String> adjReasonCnt = new ArrayList<String>(); 
					LinkedHashMap<String, String> adjReasonCfmMap = new LinkedHashMap<String, String>();
					
					String msg = lms1015Service.checkIncompleteMsg(meta, adjReasonCnt, adjReasonCfmMap);
					if(Util.isNotEmpty(msg)){
						if(allowIncomplete){
							result.set("IncompleteMsg", msg);			
						}else{
							throw new CapMessageException(msg, getClass());	
						}
					}else{
						if(allowIncomplete){
										
						}else{
							//評等調升警示訊息(2.0模型無法做評等調整，故無需此部分)
							String varVer = Util.trim(meta.getVarVer());
							if(Util.equals(OverSeaUtil.V1_0_LOAN_JP, varVer)){
								String cfmStr = OverSeaUtil.adjustReason_html(adjReasonCfmMap);
								OverSeaUtil.set_adjustReason_fmt_result(result, adjReasonCnt.get(0)
										, cfmStr, prop_abstractOverSeaCLSPage);
							}
						}
					}					
				}
												
				
				result.set(KEY, true);	
			}catch(Exception e){
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}		
		}		
		result.add(query(params));
		
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC121M01A_oid(mainOid);	
			String varVer = Util.trim(meta.getVarVer());
			String page = params.getString(EloanConstants.PAGE);		
			if ("01".equals(page)) {
				if(true){
					LMSUtil.addMetaToResult(result, meta, new String[]{ 
							"ratingDate"	
							, "lnYear", "lnMonth"
							, "repaymentSchDays"
							, "varVer"
							, "caseNo" 
							, "randomCode" });
				}				
				result.set("repaymentSchFmt", Util.equals("2", meta.getRepaymentSchFmt())?"2":"1"); //default=1
				result.set("status", _docStatus(meta));
				result.set("creator", _id_name(meta.getCreator()));
				result.set("createTime", Util.trim(TWNDate.valueOf(meta.getCreateTime())));
				result.set("updater", _id_name(meta.getUpdater()));
				result.set("updateTime",Util.trim(TWNDate.valueOf(meta.getUpdateTime())));
				
				//審核結果與批示 -簽章欄
				String appr = "";	//經辦
			    String boss = "";	//主管
			    String manager = "";	//經副襄理			    
			    if(meta.getDocStatus().equals(CreditDocStatusEnum.海外_編製中.getCode())){ 
			    	appr = Util.nullToSpace(userInfoService.getUserName(meta.getUpdater()));
			    	result.set("appr", appr);
			    }else{
			    	List<C121M01E> c121m01eList = clsService.findC121M01E_mainId(meta.getMainId());
				    if(c121m01eList!=null){
					    for (C121M01E c121m01e : c121m01eList) {
					    	if ("L5".equals(c121m01e.getStaffJob())){	//經副襄理
					    		manager = _id_name(c121m01e.getStaffNo());
					    		result.set("manager", manager);
					    	} else if ("L3".equals(Util.trim(c121m01e.getStaffJob()))) {	//主管
					    		if("".equals(boss)){boss = _id_name(c121m01e.getStaffNo());}
					    		else{boss = boss+ "、" +_id_name(c121m01e.getStaffNo());}
					    		result.set("boss", boss);
							} else if ("L1".equals(Util.trim(c121m01e.getStaffJob()))) {	//經辦
								appr = _id_name(c121m01e.getStaffNo());
								result.set("appr", appr);
							}
					    }
				    }
				    result.set("approver", _id_name(meta.getApprover()));
			    }
			} else if ("02".equals(page)) {
				
			} else if ("03".equals(page)) {
				C121S01A c121s01a = clsService.findC121S01A(meta);
				if(c121s01a==null){
					c121s01a = new C121S01A();
				}
				LMSUtil.addMetaToResult(result, c121s01a, new String[]{ 
						"cmsType"							
						, "location"
						, "region"  , "houseAge", "houseArea"
						, "factor1", "factor2", "collUsage", "locationType" });
				
			} else if ("04".equals(page)) {
				List<C120M01A> list = clsService.filter_shouldRating(clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta));
				_add_borrower_list(result, list);
			} else if ("05".equals(page)) {
				List<C120M01A> list = clsService.filter_shouldRating(clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta));
				_add_borrower_list(result, list);
			} else if ("06".equals(page)) {
				List<C120M01A> list = clsService.filter_shouldRating(clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta));
				_add_borrower_list(result, list);
				
			} else if ("07".equals(page)) {
				List<C120M01A> list = clsService.filter_shouldRating(clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta));
				
				Map<String, String> custPosMap = retrialService.get_codeTypeWithOrder("lms1015_custPos");
				JSONArray jsonAraay = new JSONArray();
				
				for(C120M01A c120m01a: list){
					JSONObject o = new JSONObject();
					o.put("c120_oid", c120m01a.getOid());
					o.put("c120_custId", Util.trim(c120m01a.getCustId()));
					o.put("c120_dupNo", Util.trim(c120m01a.getDupNo()));
					o.put("c120_custName", Util.trim(c120m01a.getCustName()));
					o.put("c120_custPos", _custPosDesc(custPosMap, Util.equals("Y", c120m01a.getKeyMan())?OverSeaUtil.M:Util.trim(c120m01a.getCustPos())));
					
					//房貸部份
					C121M01B c121m01b = clsService.findC121M01B_byC120M01A(c120m01a);
					//=====================================
					String c121m01b_oid = "";
					String c121m01b_pRating = "";
					String c121m01b_sRating = "";
					String c121m01b_sprtRating = "";
					String c121m01b_fRating = "";
					String c121m01b_negaInfoStr = ""; //因1.0與2.0所適用的負面訊息一樣，因此顯示一組就好
					String c121m01b_j10Desc = ""; //因1.0與2.0所適用的負面訊息一樣，因此顯示一組就好
					String c121m01b_adjustReason = "";
					if(c121m01b!=null){
						c121m01b_oid = Util.trim(c121m01b.getOid());
						c121m01b_pRating = Util.trim(c121m01b.getPRating());
						c121m01b_sRating = Util.trim(c121m01b.getSRating());
						c121m01b_sprtRating = Util.trim(c121m01b.getSprtRating());
						c121m01b_fRating = Util.trim(c121m01b.getFRating());
						c121m01b_negaInfoStr = OverSeaUtil.get_jp_negaInfoStr(prop_lms1015m01Page, OverSeaUtil.TYPE_UI, c121m01b);
						c121m01b_negaInfoStr = OverSeaUtil.jp_negaInfoEmpty(c121m01b_negaInfoStr);
						c121m01b_j10Desc = OverSeaUtil.get_jp_j10Desc(prop_abstractOverSeaCLSPage, prop_lms1015m01Page, c121m01b);
						c121m01b_adjustReason = Util.trim(c121m01b.getAdjustReason());
					}
					o.put("c121m01b_oid", c121m01b_oid);
					o.put("c121m01b_pRating", c121m01b_pRating);
					o.put("c121m01b_sRating", c121m01b_sRating);
					o.put("c121m01b_sprtRating", clsService.convertRatingDF(c121m01b_sprtRating));
					o.put("c121m01b_fRating", clsService.convertRatingDF(c121m01b_fRating));
					o.put("c121m01b_negaInfoStr", c121m01b_negaInfoStr);
					o.put("c121m01b_j10Desc", c121m01b_j10Desc);
					o.put("c121m01b_adjustReason", c121m01b_adjustReason);
					
					if(varVer.equals(OverSeaUtil.V2_0_LOAN_JP)){ //日本房貸2.0，非房貸模型部分(C121M01F)
						C121M01F c121m01f = clsService.findC121M01F_byC120M01A(c120m01a);
						//=====================================
						String c121m01f_oid = "";
						String c121m01f_pRating = "";
						String c121m01f_sRating = "";
						String c121m01f_sprtRating = "";
						String c121m01f_fRating = "";

						String c121m01f_adjustReason = "";
						if(c121m01f!=null){
							c121m01f_oid = Util.trim(c121m01f.getOid());
							c121m01f_pRating = Util.trim(c121m01f.getPRating());
							c121m01f_sRating = Util.trim(c121m01f.getSRating());
							c121m01f_sprtRating = Util.trim(c121m01f.getSprtRating());
							c121m01f_fRating = Util.trim(c121m01f.getFRating());
							c121m01f_adjustReason = Util.trim(c121m01f.getAdjustReason());
						}
						o.put("c121m01f_oid", c121m01f_oid);
						o.put("c121m01f_pRating", c121m01f_pRating);
						o.put("c121m01f_sRating", c121m01f_sRating);
						o.put("c121m01f_sprtRating", clsService.convertRatingDF(c121m01f_sprtRating));
						o.put("c121m01f_fRating", clsService.convertRatingDF(c121m01f_fRating));
						o.put("c121m01f_adjustReason", c121m01f_adjustReason);
					}
					//---
					jsonAraay.add(o);
				}
				HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
				map.put("key", jsonAraay);
				
				result.set("c120m01a_list", new CapAjaxFormResult(map));
			}
			result.set("varVer", varVer);
		}
		return defaultResult(params, meta, result);
	}
	private void _add_borrower_list(CapAjaxFormResult result, List<C120M01A> list){
		JSONArray jsonAraay = new JSONArray();
		for(C120M01A c120m01a: list){
			JSONObject o = new JSONObject();
			o.put("oid", c120m01a.getOid());
			o.put("custId", Util.trim(c120m01a.getCustId()));
			o.put("dupNo", Util.trim(c120m01a.getDupNo()));
			o.put("custName", Util.trim(c120m01a.getCustName()));
			//---
			jsonAraay.add(o);
		}
		HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
		map.put("key", jsonAraay);
		result.set("c120m01a_list", new CapAjaxFormResult(map));
		if(true){
			String c120m01a_oid_m = "";
			for(C120M01A c120m01a: list){
				if(Util.equals("Y", c120m01a.getKeyMan())){
					c120m01a_oid_m = c120m01a.getOid();
				}
			}
			if(Util.isNotEmpty(c120m01a_oid_m)){
				result.set("c120m01a_oid_m", c120m01a_oid_m);	
			}					
		}
	}
	private String _docStatus(C121M01A meta){
		String docStatus = Util.trim(meta.getDocStatus());
		if(Util.isNotEmpty(docStatus)){
			Properties prop = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
			String _code = docStatus;
			if(Util.equals(OverSeaUtil.C121M01A_IMPDOCSTATUS, docStatus)){
				_code = CreditDocStatusEnum.海外_已核准.getCode();
			}
			String desc = Util.trim(prop.get("docStatus."+_code));
			if(Util.isNotEmpty(desc)){
				return desc;
			}
		}
		
		return docStatus;
	}
	private CapAjaxFormResult defaultResult(PageParameters params, C121M01A meta,
			CapAjaxFormResult result) throws CapException {
		// required information
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));
		result.set("ratingId", meta.getRatingId());
		
		String branchName = meta==null?"":branchService.getBranchName(meta.getCaseBrId());
		result.set("branchName", meta.getCaseBrId() + " "+ branchName);
		result.set("custId", Util.trim(meta.getCustId()));
		result.set("dupNo", Util.trim(meta.getDupNo()));
		result.set("custName", Util.trim(meta.getCustName()));
		
		set_titleInfo(result, meta);
		return result;
	}
	
	private void set_titleInfo(CapAjaxFormResult result, C121M01A meta){
		result.set("titleInfo", Util.trim(Util.trim(meta.getCaseNo())+" "+Util.trim(meta.getCustId())
				+"-"+Util.trim(meta.getDupNo())+" "+Util.trim(meta.getCustName())));
	}
	private String _id_name(String raw_id){
		String id = Util.trim(raw_id);		
		return Util.trim(id+" "+Util.trim(userInfoService.getUserName(id)));
	}
	
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		
		C121M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC121M01A_oid(mainOid);
			
			String errMsg = "";
			
			String docStatus = Util.trim(meta.getDocStatus());
			String nextStatus = "";
			DocLogEnum _DocLogEnum = null;
			if(Util.equals(CreditDocStatusEnum.海外_編製中.getCode(), docStatus)){
				nextStatus = CreditDocStatusEnum.海外_待覆核.getCode();
				_DocLogEnum = DocLogEnum.FORWARD;
			}else if(Util.equals(CreditDocStatusEnum.海外_待覆核.getCode(), docStatus)){
				//核定、退回
				if(Util.equals("核定", decisionExpr)){					
					if(Util.equals(user.getUserId(), meta.getUpdater())){
						errMsg = RespMsgHelper.getMessage("EFD0053");
					}else{
						nextStatus = CreditDocStatusEnum.海外_已核准.getCode();
						_DocLogEnum = DocLogEnum.ACCEPT;	
					}
				}else if(Util.equals("退回", decisionExpr)){
					nextStatus = CreditDocStatusEnum.海外_編製中.getCode();
					_DocLogEnum = DocLogEnum.BACK;
				}
			}else if(Util.equals(CreditDocStatusEnum.海外_已核准.getCode(), docStatus)){	
				if(Util.equals("退回", decisionExpr)){
					//被簽報書引用，則不能退回
					errMsg = clsService.returnRatingDoc_Approved_to_Editing(meta);
					
					nextStatus = CreditDocStatusEnum.海外_編製中.getCode();
					_DocLogEnum = DocLogEnum.CREATE;
				}
			}
						
			if(Util.isNotEmpty(errMsg)){				
				throw new CapMessageException(errMsg, getClass());
			}else{
				if(Util.isEmpty(nextStatus)){
					throw new CapMessageException("流程異常["+docStatus+"]", getClass());
				}	
			}
			if(true){
				if(Util.equals(nextStatus, CreditDocStatusEnum.海外_已核准.getCode())){
					meta.setApprover(user.getUserId());
					meta.setApproveTime(CapDate.getCurrentTimestamp());
				}else if(Util.equals(nextStatus, CreditDocStatusEnum.海外_編製中.getCode())){
					meta.setApprover(null);
					meta.setApproveTime(null);
				}else if(Util.equals(nextStatus, CreditDocStatusEnum.海外_待覆核.getCode())){
					Date ratingDate_b = null;
					Date ratingDate_f = null;
					C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(meta.getMainId(), meta.getCustId(), meta.getDupNo());
					if(c120m01a!=null){
						String varVer = Util.trim(meta.getVarVer());
						C121M01B c121m01b = clsService.findC121M01B_byC120M01A(c120m01a);
						if(c121m01b!=null){
							//抓最終評等日期
							ratingDate_b = c121m01b.getRatingDate();
						}
						//2.0多一個非房貸，也要檢核
						if(varVer.equals(OverSeaUtil.V2_0_LOAN_JP)){
							C121M01F c121m01f = clsService.findC121M01F_byC120M01A(c120m01a);
							if(c121m01b!=null){
								ratingDate_f = c121m01f.getRatingDate();
							}
						}else{
							//1.0版不會有F的資料，所以當做他跟B是一樣的，後面檢核就不用寫太複雜
							ratingDate_f = ratingDate_b;
						}
					}	
					
					if(CrsUtil.isNull_or_ZeroDate(ratingDate_b) && CrsUtil.isNull_or_ZeroDate(ratingDate_f)){
						//主借人完成最終評等的日期，不可空白
						throw new CapMessageException(prop_abstractOverSeaCLSPage.getProperty("message.c121m01a_ratingDate_is_empty"), getClass());
					}
					//避免更換主借人時，漏掉同步(因房貸非房貸會同時重算，所以理當日期會一樣)
					meta.setRatingDate(ratingDate_b);
				}
				meta.setDocStatus(nextStatus);
				clsService.daoSave(meta);
				//===
				if(_DocLogEnum!=null){
					docLogService.record(meta.getOid(), _DocLogEnum);	
				}				
			}
			
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(), user.getUserId());
		}
		return defaultResult( params, meta, result);
	}
	
	/**
	 * 比照建立簽報書 LMSM02FormHandler::addL120m01a
	 * 在insert L120M01A 相關 table 時，預先塞入 deletedTime
	 * 在 save 時，再把 deletedTime 清空
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addRatingDoc(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String uuid = IDGenerator.getUUID();
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		C121M01A meta = new C121M01A();
		if(true){
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			meta.setOwnBrId(user.getUnitNo());		
			OverSeaUtil.setC121M01A(meta, uuid);			
			meta.setCustId("");
			meta.setDupNo("");
			meta.setCustName("");
			meta.setTypCd(TypCdEnum.海外.getCode());
			meta.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			meta.setDeletedTime(new Timestamp(DateUtils.addDays(CapDate.getCurrentTimestamp(), 1).getTime()));
			meta.setRatingDate(null);
			meta.setCaseYear(Integer.parseInt(StringUtils.substring(TWNDate.toAD(nowTS), 0, 4)));
			meta.setCaseBrId(user.getUnitNo());
			meta.setCaseNo("");
			meta.setRandomCode("");
			
			meta.setCreator(user.getUserId());
			meta.setCreateTime(CapDate.getCurrentTimestamp());
			if(true){
				//把 creator, createTime 填入 updater, updateTime
				meta.setUpdater(meta.getCreator());
				meta.setUpdateTime(meta.getCreateTime());	
			}
			if(true){
				String mowType = "";
				mowType = OverSeaUtil.getMowType(meta.getCaseBrId());
				meta.setMowType(mowType);
			}	
			//國別碼
			if(true){
				String mowTypeCountry = "";
				mowTypeCountry = OverSeaUtil.getMowTypeCountry(meta.getCaseBrId());
				meta.setMowTypeCountry(mowTypeCountry);
			}
			//======
			clsService.save(meta);	
		}
		return defaultResult(params, meta, result);
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult delRatingDoc(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---		
		CapAjaxFormResult result = new CapAjaxFormResult();
		String failmsg = "";
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = clsService.findC121M01A_oid(mainOid);
		if(true){
			eloandbBASEService.c121m01aclearUnUsedDoc();	
		}		
		
		Map<String, String> lockedUser = docCheckService.listLockedDocUser(meta.getMainId());
		if (lockedUser != null) {
			failmsg = meta.getCustId()+" "+ getPopMessage("EFD0055", lockedUser);
			throw new CapMessageException(failmsg, getClass());
		}
		meta.setDeletedTime(CapDate.getCurrentTimestamp());
		clsService.save(meta);
		
		return result;
	}
	
	/**
	XXX 參考 原海外消金 FormHandler 的程式
	● checkAddBorrow{checkdata:false,  haseCust:true}
	
	● addBorrowMain2 @ LMSS02APage01.js
		openDocAddBorrow @ LMSS02APage02.js
		
	● codetypehandler
	
	● getCustData3 @ LMSS02APage01.js
	
	● customerformhandler::custQueryBy0024ByIdDupNo
 
 	● setCustData3 @ LMSS02APage02.js
 	 	在開啟 thickbox 後{'通訊地址': coAddr, '行動電話': mTel, 'E-mail': email} 來自 getCustData3	
 	
	*/
	@DomainAuth(AuthType.Modify)
	public IResult addRatingDocCust(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---	
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = clsService.findC121M01A_oid(mainOid);
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo", "0"));
		String custName = Util.trim(params.getString("custName"));
		String custPos = Util.trim(params.getString("custPos"));
		String o_custRlt = Util.trim(params.getString("o_custRlt"));
		String ownBrId = meta.getOwnBrId();
		
		if(true){
			List<C120M01A> list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
			if(CollectionUtils.isEmpty(list)){
				custPos = "M";
			}
			
			if(CollectionUtils.isEmpty(list)){
				if(meta.getDeletedTime()!=null 
						&& meta.getDeletedTime().getTime()>CapDate.getCurrentTimestamp().getTime()){
					//先設成主要借款人
					meta.setDeletedTime(null);
					
					//XXX 在引入 C120M01A 時 insert into LMS.NUMBER 且 UPDATE LMS.L120M01A  SET deletedTime=null, CASENO = ?, CASESEQ = ?
					if (Util.isEmpty(meta.getCaseSeq()) && Util.isEmpty(meta.getCaseNo())) {
						meta.setCaseSeq(Integer.parseInt(numberService.getNumberWithMax(C121M01A.class, ownBrId, null, 99999)));
						StringBuilder caseNum = new StringBuilder();
						IBranch ibranch = branchService.getBranch(ownBrId);

						caseNum.append(
								Util.toSemiCharString(meta.getCaseYear().toString()))
								.append(Util.trim(ibranch.getNameABBR()))
								.append("消金評等第")
								.append(Util.toSemiCharString(Util.addZeroWithValue(Util.trim(meta.getCaseSeq()), 5)))
								.append("號");
						meta.setCaseNo(caseNum.toString());
					}
				}				
				
			}else{
				C120M01A c120m01a = lms1205Service.findC120M01AByUniqueKey(meta.getMainId(), custId, dupNo);
				if(c120m01a!=null){
					Map<String, String> map = new HashMap<String, String>();
					map.put("msg", custId+"-"+dupNo);

					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.資料已存在, map),
							getClass());
				}
			}
		}
		
		Map<String, Object> m_busCd_ecoNm = misdbBASEService.findCustBussDataByIdAndDup(custId, dupNo);
		String busCode = Util.trim(MapUtils.getString(m_busCd_ecoNm, "BUSCD"));
		String ecoNm = Util.trim(MapUtils.getString(m_busCd_ecoNm, "ECONM"));
		if(Util.isEmpty(busCode)){
			/*
			 * 當不存在於 mis.custdata 時，允許 user 從 e-loan 新增
			 */
			busCode = Util.trim(params.getString("busCode"));
			Bstbl bstbl = bstblService.findByEcocd(busCode);
			if(bstbl != null){
				ecoNm = Util.trim(bstbl.getEconm());
			}
		}
		
		if(true){
			C121M01B c121m01b = new C121M01B();
			if(true){
				OverSeaUtil.copyC121M01B(c121m01b, meta, custId, dupNo);
			}
			
			C120M01A c120m01a = new C120M01A();
			C120S01A c120s01a = new C120S01A();
			C120S01B c120s01b = new C120S01B();
			C120S01C c120s01c = new C120S01C();
			C120S01D c120s01d = new C120S01D();
			C120S01E c120s01e = new C120S01E();

			if(true){
				_set_common_filed(meta.getMainId(), custId, dupNo
						, c120m01a, c120s01a, c120s01b, c120s01c, c120s01d, c120s01e);
				if(true){
					c120m01a.setOwnBrId(ownBrId);
					c120m01a.setTypCd(UtilConstants.Casedoc.typCd.海外);
					c120m01a.setCustName(custName);
					
					if(Util.equals(custPos, "M") && !LMSUtil.isBusCode_060000_130300(busCode)){
						custPos = "";//企業戶不應為主借人
					}
					
					c120m01a.setRatingDesc(meta.getCaseNo());
					
					if(Util.equals(custPos, "M")){
						c120m01a.setKeyMan("Y");
						c120m01a.setCustPos("M");
						c120m01a.setO_custRlt("");
					}else{
						c120m01a.setKeyMan("N");
						c120m01a.setCustPos(custPos);
						c120m01a.setO_custRlt(o_custRlt);
					}
					c120m01a.setNaturalFlag(LMSUtil.isBusCode_060000_130300(busCode)?"Y":"N");
				}
				if(true){
					c120s01a.setBusCode(busCode);
					c120s01a.setEcoNm(ecoNm);					
				}
			}
			if(true){
				List<GenericBean> savedList = new ArrayList<GenericBean>();
				savedList.add(c121m01b);
				if(true){
					savedList.add(c120m01a);
					savedList.add(c120s01a);
					savedList.add(c120s01b);
					savedList.add(c120s01c);
					savedList.add(c120s01d);
					savedList.add(c120s01e);
				}
				savedList.add(meta);
				//~~~
				clsService.save(savedList);
			}
			
			if(Util.equals(custPos, "M")){
				clsService.syncCustPosM(meta, c120m01a.getOid());	
				set_titleInfo(result, meta);
			}
			result.set("c120m01a_oid", c120m01a.getOid());
		}
		
		if(true){
			Map<String, Map<String, String>> map = lms1205Service.findCustData2ByCustId(custId, dupNo);
			CapAjaxFormResult map1 = new CapAjaxFormResult(map.get("coAddr"));
			CapAjaxFormResult map2 = new CapAjaxFormResult(map.get("mComTel"));
			CapAjaxFormResult map3 = new CapAjaxFormResult(map.get("mTel"));
			CapAjaxFormResult map4 = new CapAjaxFormResult(map.get("email"));
			
			OverSeaUtil.getCustData3(result, map1, map2, map3, map4);
		}
		return result;
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult impRatingDocCust(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---	
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = clsService.findC121M01A_oid(mainOid);
		String custPos = Util.trim(params.getString("custPos"));
		String o_custRlt = Util.trim(params.getString("o_custRlt"));
		String ownBrId = meta.getOwnBrId();
		C120M01A src_c120m01a = clsService.findC120M01A_oid(Util.trim(params.getString(UI_C120_OID)));
		String custId = src_c120m01a.getCustId();
		String dupNo = src_c120m01a.getDupNo();
		if(true){
			List<C120M01A> list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
			if(CollectionUtils.isEmpty(list)){
				custPos = "M";
			}
			
			if(CollectionUtils.isEmpty(list)){
				if(meta.getDeletedTime()!=null 
						&& meta.getDeletedTime().getTime()>CapDate.getCurrentTimestamp().getTime()){
					//先設成主要借款人
					meta.setDeletedTime(null);
					
					//XXX 在引入 C120M01A 時 insert into LMS.NUMBER 且 UPDATE LMS.L120M01A  SET deletedTime=null, CASENO = ?, CASESEQ = ?
					if (Util.isEmpty(meta.getCaseSeq()) && Util.isEmpty(meta.getCaseNo())) {
						meta.setCaseSeq(Integer.parseInt(numberService.getNumberWithMax(C121M01A.class, ownBrId, null, 99999)));
						StringBuilder caseNum = new StringBuilder();
						IBranch ibranch = branchService.getBranch(ownBrId);

						caseNum.append(
								Util.toSemiCharString(meta.getCaseYear().toString()))
								.append(Util.trim(ibranch.getNameABBR()))
								.append("消金評等")
								.append(Util.toSemiCharString(Util.addZeroWithValue(Util.trim(meta.getCaseSeq()), 5)))
								.append("號");
						meta.setCaseNo(caseNum.toString());
					}
				}	
			}
		}
		
		
		if(true){
			//已存在的 data 要被 replace 掉
			if(true){
				C120M01A exist_c120m01a = lms1205Service.findC120M01AByUniqueKey(meta.getMainId(), custId, dupNo);
				if(exist_c120m01a!=null){
					//已存在的評等表，也應一併刪掉
					lms1015Service.delRatingDocCust(meta, exist_c120m01a);
					//===
					clsService.removeC120M01A_attachFile(exist_c120m01a);
				}
			}
			
			Map<String, Object> colMap = new HashMap<String, Object>();
			colMap.put("o_single", "");
			C120M01A c120m01a = clsService.copyC120Relate(src_c120m01a, meta.getMainId(), colMap);			
			clsService.copyC120AttchDocFile("", c120m01a);
			C121M01B c121m01b = new C121M01B();
			if(true){
				OverSeaUtil.copyC121M01B(c121m01b, meta, custId, dupNo);
			}

			if(true){				
				if(true){
					C120S01A c120s01a = clsService.findC120S01A(c120m01a);
					if(c120s01a!=null){
						List<Map<String, Object>> listGrpMap = misGrpcmpService.findGrpcmpSelGrpdtl(
								c120m01a.getCustId(), c120m01a.getDupNo());
						Map<String, Object> grpMap = null;				
						if(CollectionUtils.isNotEmpty(listGrpMap)){
							grpMap = listGrpMap.get(0);
						}
						//參考 L120S01B 的定義
						String groupNo = Util.trim(MapUtils.getString(grpMap, "GRPID"));
						String groupName = Util.trim(MapUtils.getString(grpMap, "GRPNM"));
						String groupBadFlag = Util.trim(MapUtils.getString(grpMap, "BADFLAG"));
						
						c120s01a.setGroupNo(groupNo);
						c120s01a.setGroupName(groupName);
						c120s01a.setGroupBadFlag(groupBadFlag);
						clsService.save(c120s01a);
					}					
					
					String busCode = c120s01a.getBusCode();
					if(Util.equals(custPos, "M") && !LMSUtil.isBusCode_060000_130300(busCode)){
						custPos = "";//企業戶不應為主借人
					}
					
					c120m01a.setRatingDesc(meta.getCaseNo());
					
					if(Util.equals(custPos, "M")){
						c120m01a.setKeyMan("Y");
						c120m01a.setCustPos("M");
						c120m01a.setO_custRlt("");
					}else{
						c120m01a.setKeyMan("N");
						c120m01a.setCustPos(custPos);
						c120m01a.setO_custRlt(o_custRlt);
					}
					c120m01a.setNaturalFlag(LMSUtil.isBusCode_060000_130300(busCode)?"Y":"N");
				}
			}
			if(true){
				List<GenericBean> savedList = new ArrayList<GenericBean>();
				savedList.add(c121m01b);
				if(true){
					savedList.add(c120m01a);					
				}
				savedList.add(meta);
				//~~~
				clsService.save(savedList);
			}
			
			if(Util.equals(custPos, "M")){
				clsService.syncCustPosM(meta, c120m01a.getOid());
				set_titleInfo(result, meta);
			}
			result.set("c120m01a_oid", c120m01a.getOid());
		}
		return result;
	}	
	
	@DomainAuth(AuthType.Modify)
	public IResult delRatingDocCust(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---	
		CapAjaxFormResult result = new CapAjaxFormResult();
		C120M01A c120m01a = clsService.findC120M01A_oid(params.getString(UI_C120_OID));
			
		if(c120m01a==null){
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		
		C121M01A c121m01a = clsService.findC121M01A_oid(Util.trim(params.getString("c121m01a_oid")));
		if(c121m01a!=null){			
			lms1015Service.delRatingDocCust(c121m01a, c120m01a);
		}
		return result;
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult addBaseData(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---	
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = OverSeaUtil.trimRightPaddingFullEmpty(params.getString("custName"));
		
		C120M01A c120m01a = clsService.findC120M01A_o_single_Y(user.getUnitNo(), custId, dupNo);
		if(c120m01a!=null){
			Map<String, String> map = new HashMap<String, String>();
			map.put("msg", custId+"-"+dupNo);

			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.資料已存在, map), getClass());
		}else{
			c120m01a = new C120M01A();	
		}
		
		Map<String, Object> m_busCd_ecoNm = misdbBASEService.findCustBussDataByIdAndDup(custId, dupNo);
		String busCode = Util.trim(MapUtils.getString(m_busCd_ecoNm, "BUSCD"));
		String ecoNm = Util.trim(MapUtils.getString(m_busCd_ecoNm, "ECONM"));
		if(Util.isEmpty(busCode)){
			/*
			 * 當不存在於 mis.custdata 時，允許 user 從 e-loan 新增
			 */
			busCode = Util.trim(params.getString("busCode"));
			Bstbl bstbl = bstblService.findByEcocd(busCode);
			if(bstbl != null){
				ecoNm = Util.trim(bstbl.getEconm());
			}
		}
		
		if(!LMSUtil.isBusCode_060000_130300(busCode)){
			String pattern = prop_abstractOverSeaCLSPage.getProperty("error.04");
			throw new CapMessageException(MessageFormat.format(pattern, custId, busCode), getClass());
		}
		
		if(true){			
			C120S01A c120s01a = new C120S01A();
			C120S01B c120s01b = new C120S01B();
			C120S01C c120s01c = new C120S01C();
			C120S01D c120s01d = new C120S01D();
			C120S01E c120s01e = new C120S01E();

			if(true){
				String uuid = IDGenerator.getUUID();
				String ownBrId = user.getUnitNo();
				_set_common_filed(uuid, custId, dupNo
						, c120m01a, c120s01a, c120s01b, c120s01c, c120s01d, c120s01e);
				if(true){
					c120m01a.setOwnBrId(ownBrId);
					c120m01a.setTypCd(UtilConstants.Casedoc.typCd.海外);
					c120m01a.setCustName(custName);
					c120m01a.setO_single("Y");
				}
				if(true){
					c120s01a.setBusCode(busCode);
					c120s01a.setEcoNm(ecoNm);					
				}
			}
			if(true){
				List<GenericBean> savedList = new ArrayList<GenericBean>();				
				if(true){
					savedList.add(c120m01a);
					savedList.add(c120s01a);
					savedList.add(c120s01b);
					savedList.add(c120s01c);
					savedList.add(c120s01d);
					savedList.add(c120s01e);
				}
				//~~~
				clsService.save(savedList);
			}
		}		
		result.set("c120m01a_oid", c120m01a.getOid());
		return result;
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult delBaseData(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---	
		CapAjaxFormResult result = new CapAjaxFormResult();
		C120M01A c120m01a = clsService.findC120M01A_oid(params.getString(UI_C120_OID));
		
		//String mainId = Util.trim(params.getString("mainId"));
		if(c120m01a==null){
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		clsService.delC120Relate(c120m01a);
		
		return result;
	}
	
	
	private void _set_common_filed(
			String mainId, String custId, String dupNo
			, C120M01A c120m01a, C120S01A c120s01a
			, C120S01B c120s01b, C120S01C c120s01c
			, C120S01D c120s01d, C120S01E c120s01e) 
	throws CapFormatException{
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch iBranch = branchService.getBranch(MegaSSOSecurityContext.getUnitNo());
		String creator = user.getUserId();
		String createTime = new BranchDateTimeFormatter(iBranch)
						.reformat(CapDate.parseToString(CapDate.getCurrentTimestamp()));
		
		OverSeaUtil.copyC120_ref(mainId, custId, dupNo, creator, createTime, c120m01a, c120s01a, c120s01b, c120s01c, c120s01d, c120s01e);				
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult loadC121M_items(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		C120M01A c120m01a = clsService.findC120M01A_oid(params.getString(UI_C120_OID));
		if(c120m01a==null){
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = clsService.findC121M01A_oid(mainOid);	
		
		if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_日本, meta.getMowType())){
			String varVer = Util.trim(meta.getVarVer());
			
			C121M01B c121m01b = clsService.findC121M01B_byC120M01A(c120m01a);
			C121M01F c121m01f = null;
			if(varVer.equals(OverSeaUtil.V2_0_LOAN_JP)){
				c121m01f = clsService.findC121M01F_byC120M01A(c120m01a);
			}
			
			String page = params.getString(EloanConstants.PAGE);
			if ("04".equals(page)) {
				LMSUtil.addMetaToResult(result, c121m01b, new String[]{ 
						"chkItem1","chkItem2","chkItem4","chkItem5", "chkItem7"
						, "chkItem9", "chkItem10", "chkItem11", "chkItem12", "chkItem13"
						, "sumRiskPt"
						, "pRating", "sRating"});
				
			} else if ("05".equals(page)) { //支援評等
				//1.0跟2.0共用部分
				
				if(varVer.equals(OverSeaUtil.V2_0_LOAN_JP)){ //日本模型2.0
					//房貸(C121M01B)
					result.set("c121m01b_pRating", c121m01b.getPRating());		
					result.set("c121m01b_sRating", c121m01b.getSRating());
					result.set("c121m01b_sprtRating", c121m01b.getSprtRating());
					result.set("c121m01b_sprtRatingDesc", clsService.convertRatingDF(Util.trim(c121m01b.getSprtRating())));
					result.set("c121m01b_j10Desc", this.getJ10Desc(c121m01b, c121m01f, OverSeaUtil.海外評等_房貸));
					
					//非房貸(C121M01F)
					if(c121m01f != null){
						result.set("c121m01f_pRating", c121m01f.getPRating());		
						result.set("c121m01f_sRating", c121m01f.getSRating());
						result.set("c121m01f_sprtRating", c121m01f.getSprtRating());
						result.set("c121m01f_sprtRatingDesc", clsService.convertRatingDF(Util.trim(c121m01f.getSprtRating())));
						result.set("c121m01f_j10Desc", this.getJ10Desc(c121m01b, c121m01f, OverSeaUtil.海外評等_非房貸));
					}
				}else{ //日本模型1.0
					LMSUtil.addMetaToResult(result, c121m01b, new String[]{ 
							"pRating", "sRating", "sprtRating"});
					result.set("j10Desc", this.getJ10Desc(c121m01b, c121m01f, OverSeaUtil.海外評等_房貸)); //1.0固定抓C121M01B
					result.set("sprtRatingDesc", clsService.convertRatingDF(Util.trim(c121m01b.getSprtRating())));		
				}
			} else if ("06".equals(page)) { //評等調整(2.0不會有評等調整，這邊不用特別改)
				LMSUtil.addMetaToResult(result, c121m01b, new String[]{ 
						"pRating","sRating","sprtRating", "adjRating",
						"noAdj", "adjustStatus"
						, "adjustFlag", "adjustReason" });
				result.set("sprtRatingDesc", clsService.convertRatingDF(Util.trim(c121m01b.getSprtRating())));
				result.set("attchFileOid2", Util.trim(c120m01a.getAttchFileOid2()));
			} 
			if(true){
				//page 4,5,6都會有的欄位
				Map<String, String> custPosMap = retrialService.get_codeTypeWithOrder("lms1015_custPos");
				result.set("c120_custId", c120m01a.getCustId());
				result.set("c120_dupNo", c120m01a.getDupNo());
				result.set("c120_custName", c120m01a.getCustName());		
				result.set("c120_custPos", _custPosDesc(custPosMap, Util.equals("Y", c120m01a.getKeyMan())?OverSeaUtil.M:Util.trim(c120m01a.getCustPos())));
			}
		}
		
		return result;				
	}	
	
	private String getJ10Desc(C121M01B c121m01b, C121M01F c121m01f, String MNflag){
		String j10Desc = "";
		String j10_score_flag = "";
		
		if(MNflag.equals(OverSeaUtil.海外評等_非房貸)){
			j10_score_flag = Util.trim(c121m01f.getJ10_score_flag());
		}else{
			j10_score_flag = Util.trim(c121m01b.getJ10_score_flag());
		}
		if(Util.equals("A", j10_score_flag)){
			if(MNflag.equals(OverSeaUtil.海外評等_非房貸)){
				j10Desc = Util.trim(c121m01f.getJ10_score());
			}else{
				j10Desc = Util.trim(c121m01b.getJ10_score());
			}
		}else if(Util.equals("B", j10_score_flag)){
			j10Desc = prop_lms1015m01Page.getProperty("C121M01B.j10_score_flag.B");
		}else if(Util.equals("C", j10_score_flag)){
			j10Desc = prop_lms1015m01Page.getProperty("C121M01B.j10_score_flag.C");
		}else if(Util.equals("D", j10_score_flag)){
			j10Desc = prop_lms1015m01Page.getProperty("C121M01B.j10_score_flag.D");
		}else if(Util.equals("N", j10_score_flag)){
			j10Desc = prop_lms1015m01Page.getProperty("C121M01B.j10_score_flag.N");
		}else if(Util.equals("NA", j10_score_flag)){
			j10Desc = prop_lms1015m01Page.getProperty("C121M01B.j10_score_flag.NA");
		}
		
		return j10Desc;
	}
	
	private String _custPosDesc(Map<String, String> custPosMap, String custPos){
		
		String custPosDesc = ""; 
		if(Util.isNotEmpty(custPos)){
			custPosDesc = LMSUtil.getDesc(custPosMap, custPos);
			if(Util.notEquals(custPos, custPosDesc)){
				custPosDesc = (custPos+"."+custPosDesc);	
			}	
		}
		return custPosDesc;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryC120BusCd(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		C120M01A c120m01a = clsService.findC120M01A_oid(params.getString(UI_C120_OID));
		if(c120m01a==null){
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		
		C120S01A c120s01a = clsService.findC120S01A(c120m01a);
		if (c120s01a == null) {
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
//	        throw new CapMessageException("C120S01A 資料不存在，請先完成借款人基本資料／營業類別建檔後再執行此動作", getClass());
	    }
		
		String busCode = Util.trim(c120s01a.getBusCode());
		result.set("busCode", busCode);
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult overSeaCLS_queryC120BusCd(PageParameters params)
			throws CapException {		
		return queryC120BusCd(params);
	}
	
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult overSeaCLS_del(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		C120M01A c120m01a = clsService.findC120M01A_oid(params.getString(UI_C120_OID));
		
		String mainId = Util.trim(params.getString("mainId"));
		if(c120m01a==null){
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		
		if(true){
			/*
			於「基本資料」刪除：delBaseData
			於「評等文件編製中」刪除：delRatingDocCust
			‧這裡應該是「簽報書」刪除「個別借款人」；「簽報書」刪除「評等文件」另寫 caseDocDelRatingDoc
			*/
			L120M01A l120m01a = clsService.findL120M01A_mainId(mainId);
			if(OverSeaUtil.isCaseDoc_CLS_rawBorrowerPanel(l120m01a)){
				
			}else{
				//陳復(述)、異常通報，沒有引入C121M01A
			}					
			//-------------
			clsService.delC120Relate(c120m01a);
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult overSeaCLS_query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		C120M01A c120m01a = clsService.findC120M01A_oid(params.getString(UI_C120_OID));
		if(c120m01a==null){
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		
		C120S01A c120s01a = clsService.findC120S01A(c120m01a);
		C120S01B c120s01b = clsService.findC120S01B(c120m01a);
		C120S01C c120s01c = clsService.findC120S01C(c120m01a);
		C120S01D c120s01d = clsService.findC120S01D(c120m01a);
		C120S01E c120s01e = clsService.findC120S01E(c120m01a);

		L120S01M l120s01m = clsService.findL120S01M(c120s01e);
		
		
		Map<String, Object> m = new HashMap<String, Object>();
		m.put("formAttrTxFlag", OverSeaUtil.getFormAttrTxFlag(params));
		if(true){
			clsService.clspage_srvPage_html(m, OverSeaUtil.getFormAttrTxFlag(params)
					, c120m01a, c120s01a, c120s01b
					, c120s01c, c120s01d, c120s01e);
			
		}
		result.set("c120m01a_oid", c120m01a.getOid());
		result.set("c120m01a_mainId", c120m01a.getMainId());
		result.set("c120m01a_ratingKind", Util.trim(c120m01a.getRatingKind()));
		result.set("c120m01a_noReqFieldForRating", noReqFieldForRating(c120m01a)?"Y":"N");
		if(OverSeaUtil.getRating_FORMATT_AU_TXFLAG().contains(OverSeaUtil.getFormAttrTxFlag(params)) ){
			result.set("lms1025m00page_titleInfo", Util.trim(c120m01a.getCustId())+"-"+Util.trim(c120m01a.getDupNo())+" "+Util.trim(c120m01a.getCustName()));
		}
		result.putAll(m);
		LMSUtil.setL120M01M(result, l120s01m);		
		
		//J-108-0143_10702_B1001 新增新往來客戶註記並於額度明細表新做加註
		result.set("newCustFlag", c120s01a.getNewCustFlag());
		
		return result;
	}
	
	private boolean noReqFieldForRating(C120M01A c120m01a){
		boolean noReqFieldForRating = false; 
		if(true){
			if(Util.isEmpty(Util.trim(c120m01a.getO_single()))){
				if(Util.equals(OverSeaUtil.C120M01A_RATINGKIND_2, c120m01a.getRatingKind())){
					//RATINGKIND_2==簽報書新增
					noReqFieldForRating = true;
				}else{
					L120M01A l120m10a = clsService.findL120M01A_mainId(c120m01a.getMainId());
					if(l120m10a==null){
						//C120M01A 的 mainId 可能對到 C121M01A 或 L120M01A						
					}else{						
						noReqFieldForRating = OverSeaUtil.isCaseDoc_CLS_rawBorrowerPanel(l120m10a);
					}	
				}					
			}else{
				//基本資料					
			}
		}	
		return noReqFieldForRating;
	}
	
	/**
	 * 參考  _LMSM01FormHandler_saveBorrow2
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)	
	public IResult overSeaCLS_save(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String c121m01a_oid = Util.trim(params.getString("c121m01a_oid"));
		String formAttrTxFlag = OverSeaUtil.getFormAttrTxFlag(params);
		C120M01A c120m01a = clsService.findC120M01A_oid(params.getString(UI_C120_OID));
		if(c120m01a==null){
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		List<String> errMsg = new ArrayList<String>();
		String mainId = Util.trim(params.getString("mainId"));
		C120M01A existKeyMan = clsService.findExistKeyMan(mainId);
		
		C120S01A c120s01a = clsService.findC120S01A(c120m01a);
		C120S01B c120s01b = clsService.findC120S01B(c120m01a);
		C120S01C c120s01c = clsService.findC120S01C(c120m01a);
		C120S01D c120s01d = clsService.findC120S01D(c120m01a);
		C120S01E c120s01e = clsService.findC120S01E(c120m01a);
		
		clsService.clspage_html_srvPage(params, c120m01a, c120s01a, c120s01b
				, c120s01c, c120s01d, c120s01e);		
		//==============
		if(true){
			//檢核資料
			List<String> panel1 = new ArrayList<String>();
			List<String> panel2 = new ArrayList<String>();
			List<String> panel3 = new ArrayList<String>();
			List<String> panel4 = new ArrayList<String>();
			List<String> panel5 = new ArrayList<String>();
			
			L120S01M l120s01m = clsService.findL120S01M(c120s01e);
			
			boolean has_snrY_snrM = (params.containsKey("snrY") && params.containsKey("snrM")); 
			String snrY = Util.trim(params.getString("snrY"));
			String snrM = Util.trim(params.getString("snrM"));
			
			OverSeaUtil.verifyEntity(panel1, panel2, panel3, panel4,panel5, 
					prop_abstractOverSeaCLSPage, formAttrTxFlag, noReqFieldForRating(c120m01a), 
					c120m01a, c120s01a, c120s01b, c120s01c, c120s01d, c120s01e, l120s01m,
					has_snrY_snrM, snrY, snrM);
			
			if(panel1.size()==0 && panel2.size()==0 && panel3.size()==0 
					&& panel4.size()==0 && panel5.size()==0){
				boolean isCls = LMSUtil.isBusCode_060000_130300(c120s01a.getBusCode());
				if(isCls && !clsService.verifyCurrAmt(c120m01a.getOwnBrId(), c120s01b, c120s01c, c120s01d)){
					//異常!!夫妻年收入 < (年薪 + 其他收入 + 配偶年薪)，請再次確認!!
					panel3.add(prop_lms1205m01Page.getProperty("l120m01a.error38"));
				}	
			}			
			//~~~~~
			add_errMsg(errMsg, prop_abstractOverSeaCLSPage.getProperty("l120s02.tag1"), panel1);
			add_errMsg(errMsg, prop_abstractOverSeaCLSPage.getProperty("l120s02.index26"), panel2);
			add_errMsg(errMsg, prop_abstractOverSeaCLSPage.getProperty("l120s02.index37"), panel3);
			add_errMsg(errMsg, prop_abstractOverSeaCLSPage.getProperty("l120s02.tag7"), panel4);
			add_errMsg(errMsg, prop_abstractOverSeaCLSPage.getProperty("l120s02.index56"), panel5);			
		}
		//==============	
		List<GenericBean> saveList = new ArrayList<GenericBean>();
		//簽報書在 呈主管時，有檢查 chkYN 都要為Y
		c120m01a.setO_chkYN(errMsg.size()==0?"Y":"N");
		
		if(c120s01a!=null){
			List<Map<String, Object>> listGrpMap = misGrpcmpService.findGrpcmpSelGrpdtl(
					c120m01a.getCustId(), c120m01a.getDupNo());
			Map<String, Object> grpMap = null;				
			if(CollectionUtils.isNotEmpty(listGrpMap)){
				grpMap = listGrpMap.get(0);
			}
			//參考 L120S01B 的定義
			String groupNo = Util.trim(MapUtils.getString(grpMap, "GRPID"));
			String groupName = Util.trim(MapUtils.getString(grpMap, "GRPNM"));
			String groupBadFlag = Util.trim(MapUtils.getString(grpMap, "BADFLAG"));
			
			c120s01a.setGroupNo(groupNo);
			c120s01a.setGroupName(groupName);
			c120s01a.setGroupBadFlag(groupBadFlag);
		}
		saveList.add(c120m01a);
		saveList.add(c120s01a);
		saveList.add(c120s01b);		
		saveList.add(c120s01c);
		saveList.add(c120s01d);
		saveList.add(c120s01e);
		
		Meta l120m01a_or_c121m01a = null;
		if(l120m01a_or_c121m01a==null){
			L120M01A l120m01a = clsService.findL120M01A_mainId(mainId);
			if(l120m01a!=null){
				l120m01a_or_c121m01a = l120m01a;
			}
		}
		if(l120m01a_or_c121m01a==null){			
			C121M01A c121m01a = clsService.findC121M01A_oid(c121m01a_oid);
			if(c121m01a!=null){
				l120m01a_or_c121m01a = c121m01a;
			}
		}
		
		if(existKeyMan!=null 
				&& Util.equals("Y", c120m01a.getKeyMan()) 
				&& Util.notEquals(existKeyMan.getOid(), c120m01a.getOid())){
			//當keyMan==Y, 要處理其它的 c120m01a
			existKeyMan.setKeyMan("N");
			// 取消掉的話就要輸入相關身份，故檢核設為N
			existKeyMan.setO_chkYN("N");
			//~~~
			saveList.add(existKeyMan);

			//替代簽報書中的主借人
			if(l120m01a_or_c121m01a!=null){
				l120m01a_or_c121m01a.setCustId(c120m01a.getCustId());
				l120m01a_or_c121m01a.setDupNo(c120m01a.getDupNo());
				l120m01a_or_c121m01a.setCustName(c120m01a.getCustName());	
			}			
		}
		if(l120m01a_or_c121m01a!=null){
			saveList.add(l120m01a_or_c121m01a);//M01A放最後,deleteByMainId
		}
		clsService.save(saveList);
		
		if(errMsg.size()>0){
			result.set("errMsg", StringUtils.join(errMsg, "<br>"));	
		}				
		result.set("l120m01a_custId", l120m01a_or_c121m01a==null?"":l120m01a_or_c121m01a.getCustId());
		result.set("l120m01a_dupNo", l120m01a_or_c121m01a==null?"":l120m01a_or_c121m01a.getDupNo());
		result.set("l120m01a_custName", l120m01a_or_c121m01a==null?"":l120m01a_or_c121m01a.getCustName());
		return result;
	}
	
	/**
	 * 傳入 {'docOid': ''}
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult overSeaCLS_logicalDeleteBDocFile(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String docOid = params.getString("docOid");
		clsService.logicalDeleteBDocFile(docOid);
		return result;
	}
	
	/**
	 * 傳入 {'c120m01a_oid': '', 'docOid': ''}
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult overSeaCLS_set_c120m01a_attchFileOid(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---
		CapAjaxFormResult result = new CapAjaxFormResult();
		String docOid = params.getString("docOid");
		C120M01A c120m01a = clsService.findC120M01A_oid(params.getString(UI_C120_OID));
		if(c120m01a==null){
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		c120m01a.setAttchFileOid(docOid);
		
		clsService.save(c120m01a);
		if(true){
			//C120M01A 非 meta
			tempDataService.deleteByMainId(c120m01a.getMainId());
		}
		return result;
	}
	
	/**
	 * 傳入 {'c120m01a_oid': '', 'docOid': ''}
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult set_c120m01a_attchFileOid2(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---
		CapAjaxFormResult result = new CapAjaxFormResult();
		String docOid = params.getString("docOid");
		C120M01A c120m01a = clsService.findC120M01A_oid(params.getString(UI_C120_OID));
		if(c120m01a==null){
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		c120m01a.setAttchFileOid2(docOid);
		
		clsService.save(c120m01a);
		if(true){
			//C120M01A 非 meta
			tempDataService.deleteByMainId(c120m01a.getMainId());
		}
		return result;
	}
	
	private void add_errMsg(List<String> errMsg, String panelTitle, List<String> panelErrMsg){
		if(panelErrMsg.size()>0){
			errMsg.add("【"+panelTitle+"】");
			for(String m : panelErrMsg){
				errMsg.add("&nbsp;&nbsp;&nbsp;&nbsp;"+m);	
			}
		}
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult imp_mateData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mCustId = Util.trim(params.getString("custId"));
		String mDupNo = Util.trim(params.getString("dupNo"));
		String mName = Util.trim(params.getString("custName"));
		
		JSONObject json = new JSONObject(); 
		json = lms1205Service.findCustData2ByCustId(json, mCustId, mDupNo);
		if (json.isEmpty()) {
			
		}else{
			String[] cols = {"mBirthday", "mSex","mComName","mJobTitle","mComTel"};
			for(String col: cols){
				result.set(col, Util.trim(json.optString(col)));	
			}
		}		
		result.set("mCustId", mCustId);
		result.set("mDupNo", mDupNo);
		result.set("mName", mName);
		return result;
	}
		
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult calc_C121_score(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = clsService.findC121M01A_oid(mainOid);			
		
		lms1015Service.del_noneRating_score(meta);
		
		//強迫重算
		lms1015Service.calc_C121_score(meta);	
				
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult setC120CustPosCustRlt(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = clsService.findC121M01A_oid(mainOid);
		String custPos = Util.trim(params.getString("custPos"));
		String o_custRlt = Util.trim(params.getString("o_custRlt"));
		
		C120M01A c120m01a = clsService.findC120M01A_oid(Util.trim(params.getString(UI_C120_OID)));
		if(meta!=null && c120m01a!=null){
			if(Util.equals(c120m01a.getKeyMan(), "Y")){
				//原本是主借人
			}else{
				c120m01a.setCustPos(custPos);
				c120m01a.setO_custRlt(o_custRlt);
				if(true){
					List<GenericBean> saveList = new ArrayList<GenericBean>();
					saveList.add(c120m01a);
					clsService.save(saveList);						
				}
				
				if(Util.equals(custPos, "M")){
					clsService.syncCustPosM(meta, c120m01a.getOid());
					set_titleInfo(result, meta);
				}	
			}
				
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult convertToC121Param(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		C120M01A c120m01a = clsService.findC120M01A_oid(Util.trim(params.getString(UI_C120_OID)));
		if(c120m01a!=null){
			C121M01A c121m01a = clsService.findC121M01A(c120m01a);
			if(c121m01a!=null){
				result.set("mainOid", c121m01a.getOid());
				result.set("mainId", c121m01a.getMainId());
			}
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryLMS1015M02(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String c121m01b_oid = Util.trim(params.getString("c121m01b_oid"));
		if (Util.isNotEmpty(c121m01b_oid)) {
			C121M01B c121m01b = clsService.findC121M01B_oid(c121m01b_oid);
			if(c121m01b!=null){
				C121M01A c121m01a = clsService.findC121M01AByMainId(c121m01b.getMainId());
				C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(c121m01b.getMainId()
						, c121m01b.getCustId(), c121m01b.getDupNo());
				if(true){
					LMSUtil.addMetaToResult(result, c121m01b, new String[]{
							//~~~~~~~~~~~~~~
							//part1
							"ownBrId"	
							, "custId", "dupNo"
							//~~~~~~~~~~~~~~
							//part2
							, "grdCDate", "jcicQDate"
							, "grdTDate"
							, "item_m1", "scr_m1", "std_m1", "weight_m1"
							, "raw_m1"
							, "item_m5", "scr_m5", "std_m5", "weight_m5"
							, "item_m7", "scr_m7", "std_m7", "weight_m7"
							, "item_p2", "scr_p2", "std_p2", "weight_p2"
							, "raw_payCurr", "raw_payAmt", "exRate_pay", "raw_otherCurr", "raw_otherAmt", "exRate_oth" 
							, "item_a5", "scr_a5", "std_a5", "weight_a5"
							, "item_z1", "scr_z1", "std_z1", "weight_z1"
							, "item_z2", "scr_z2", "std_z2", "weight_z2"
							, "scr_core", "std_core"
							
							//~~~~~~~~~~~~~~
							//part3
							, "chkItem1","chkItem2","chkItem4","chkItem5", "chkItem7"
							, "chkItem9", "chkItem10", "chkItem11", "chkItem12", "chkItem13"
							, "sumRiskPt"
							//~~~~~~~~~~~~~~
							//part4
							, "noAdj", "adjustStatus", "adjRating", "adjustFlag", "adjustReason" 
							//~~~~~~~~~~~~~~
							//part5
							, "pRating", "sRating", "sprtRating", "fRating" 
							, "varVer"
							});
				}			
				String caseNo = c121m01a.getCaseNo();
				//part1
				result.set("caseNo", caseNo);
				result.set("custName", c120m01a.getCustName());				
				result.set("creator", _id_name(c121m01b.getCreator()));
				result.set("createTime", Util.trim(TWNDate.valueOf(c121m01b.getCreateTime())));
				result.set("updater", _id_name(c121m01b.getUpdater()));
				result.set("updateTime",Util.trim(TWNDate.valueOf(c121m01b.getUpdateTime())));
				//part2
				result.set("approveTime",Util.trim(TWNDate.toAD(c121m01a.getApproveTime())));
				result.set("initRating", Util.trim(c121m01b.getPRating()));
				//part3
				result.set("adj_pts", c121m01b.getAdj_pts()*-1);
				//part4
				String j10_desc = "";
				if(true){
					if(Util.equals("A", c121m01b.getJ10_score_flag())){
						j10_desc = Util.trim(c121m01b.getJ10_score());	
					}else if(Util.equals("B", c121m01b.getJ10_score_flag())){
						j10_desc = prop_lms1015m01Page.getProperty("C121M01B.j10_score_flag.B");
					}else if(Util.equals("C", c121m01b.getJ10_score_flag())){
						j10_desc = prop_lms1015m01Page.getProperty("C121M01B.j10_score_flag.C");
					}else if(Util.equals("D", c121m01b.getJ10_score_flag())){
						j10_desc = prop_lms1015m01Page.getProperty("C121M01B.j10_score_flag.D");
					}else if(Util.equals("N", c121m01b.getJ10_score_flag())){
						j10_desc = prop_lms1015m01Page.getProperty("C121M01B.j10_score_flag.N");
					}else if(Util.equals("NA", c121m01b.getJ10_score_flag())){
						j10_desc = prop_lms1015m01Page.getProperty("C121M01B.j10_score_flag.NA");
					}else {
						j10_desc = Util.trim(c121m01b.getJ10_score_flag());
					}					
				}
				result.set("j10_desc", j10_desc); 
				result.set("adj_j10", (c121m01b.getAdj_j10()==99)?OverSeaUtil.DF:Util.trim(c121m01b.getAdj_j10()));
				//=====================
				result.set("titleInfo", caseNo
						+" "+c120m01a.getCustId()+" "+"-"+c120m01a.getDupNo()
						+" "+c120m01a.getCustName());			
			}			
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult testRatingDocDW(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A c121m01a = clsService.findC121M01A_oid(mainOid);
		C121S01A c121s01a = clsService.findC121S01A(c121m01a);
		
		L140M01C l140m01c = new L140M01C();
		if (true) {
			l140m01c.setLoanTP("Y01");
			l140m01c.setC121CustId(c121m01a.getCustId());
			l140m01c.setC121DupNo(c121m01a.getDupNo());
			l140m01c.setModelType("M"); //房貸
//			l140m01c.setModelType("N"); //非房貸
		}
		
		
		if(true){
			for(C120M01A c120m01a: clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(c121m01a)){
				GenericBean c121m01_grade = null;
				if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_日本, c121m01a.getMowType())){
					/*上傳DW用的測試用程式，因為沒有簽報書無對應的會計科目，無法確定該案件屬於房貸還是非房貸。
					 * 因DW約束條鍵所致，一次只能驗一個模型。
					 * */
					
					//房貸
					c121m01_grade = clsService.findC121M01B_byC120M01A(c120m01a);
					//非房貸
//					c121m01_grade = clsService.findC121M01F_byC120M01A(c120m01a);
					
				}else if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_澳洲, c121m01a.getMowType())){
					//房貸
					c121m01_grade = clsService.findC121M01C_byC120M01A(c120m01a);
					//非房貸
//					c121m01_grade = clsService.findC121M01G_byC120M01A(c120m01a);
				}else if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_泰國, c121m01a.getMowType())){
					c121m01_grade = clsService.findC121M01D_byC120M01A(c120m01a);
					
//					c121m01_grade = clsService.findC121M01H_byC120M01A(c120m01a);
					
				}
				if(c121m01_grade==null){
					continue;
				}
				C120S01A c120s01a = clsService.findC120S01A(c120m01a);
				C120S01B c120s01b = clsService.findC120S01B(c120m01a);
				C120S01C c120s01c = clsService.findC120S01C(c120m01a);
				C120S01E c120s01e = clsService.findC120S01E(c120m01a);
				clsService.verify_C121M01A(c121m01a, c121s01a, c120m01a, 
						c120s01a, c120s01b, c120s01c, c120s01e, c121m01_grade, l140m01c);		
			}
		}
		
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult testExistC121M01ACopyDocC120Data(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		/*
$.ajax({
    handler: "lms1015m01formhandler", action: "testExistC121M01ACopyDocC120Data",
    data: {'oid':'', 'brNo':''},success: function(json){ alert('ok'); }
});
		==============
		A,B 的 attachFile 原本都是 O
		(1)處理到 A
			A | A' [A']
			B | A'
		(2)處理到 B
			A | B'[A']
			B | B'[B']
		(3)若再跑一次該 method , 處理到A
			A | A''[A', A'']
			B | A''[B']
		(4)再跑一次該 method , 處理到B
			A | B'' [A', A'']
			B | B'' [B', B'']
	
		 */
		String brNo = Util.trim(params.getString("brNo"));
		String oid = Util.trim(params.getString("oid"));
		if(Util.isNotEmpty(oid) || Util.isNotEmpty(brNo)){
			logger.debug("======1aunch[oid="+oid+"][brNo="+brNo+"]");
			
			ISearch iSearch = clsService.getMetaSearch();
			if(Util.isNotEmpty(oid)){
				iSearch.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);	
			}
			if(Util.isNotEmpty(brNo)){
				iSearch.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", brNo);	
			}
			iSearch.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
			iSearch.addSearchModeParameters(SearchMode.IN, "docStatus"
					, new String[]{CreditDocStatusEnum.海外_編製中.getCode()
						, CreditDocStatusEnum.海外_待覆核.getCode()
						, CreditDocStatusEnum.海外_已核准.getCode()});
			iSearch.setMaxResults(Integer.MAX_VALUE);
			
			Page<? extends GenericBean> c121page = clsService.findPage(C121M01A.class, iSearch);
			int idx = 0;
			for(GenericBean bean : c121page.getContent()){
				C121M01A c121m01a = (C121M01A)bean;
				int idx_sameMainId = 0;				
				for(C120M01A c120m01a: clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(c121m01a)){
					String idxStr = idx+"-"+idx_sameMainId;
					Map<String, String> chg = clsService.copyC120AttchDocFile(idxStr, c120m01a);
					if(chg.size()>0){
						logger.debug("======1aunch,c121m01a.mainId=["+c121m01a.getMainId()+"]idx=["+idxStr+"] chg");
						clsService.daoSave(c120m01a);
						//===========
						clsService.fixFildOid(idxStr, chg);						
					}else{
						logger.debug("======1aunch,c121m01a.mainId=["+c121m01a.getMainId()+"]idx=["+idxStr+"] not chg");
					}
					idx_sameMainId++;
				}	
				idx++;
			}
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult testCopyDocC120Data(PageParameters params)
			throws CapException {
		/*
$.ajax({
    handler: "lms1015m01formhandler", action: "testCopyDocC120Data",
    data: {genCnt:5},success: function(json){ alert('ok'); }
});
		==============
		暫時只能用在 testing, 不要用在 production【custName, busCode】會變
		(1)舊案的 busCode=130300 
		(2)已被海外分行, 修改為 060000 , 經0024引入, 也是 060000  
		(3)若只單純 copy 舊案, 會 copy 到舊案的 busCode=130300 , user應該不會主動重新引入
		*/
		CapAjaxFormResult result = new CapAjaxFormResult();
		int genCnt = 300;
		if(true){
			Integer _GEN_CNT = params.getAsInteger("genCnt");
			if(_GEN_CNT!=null && _GEN_CNT>0){
				genCnt = _GEN_CNT;
			}
		}		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<String[]> exist_list = new ArrayList<String[]>();
		Map<String, C120M01A> oid_model_map = new HashMap<String, C120M01A>();
		String ownBrId = user.getUnitNo();
		if(true){
			ISearch search = clsService.getMetaSearch();
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
			search.addSearchModeParameters(SearchMode.OR, 
					new SearchModeParameter(SearchMode.EQUALS, "o_single", ""), 
					new SearchModeParameter(SearchMode.IS_NULL, "o_single", ""));		
			search.setMaxResults(Integer.MAX_VALUE);
			//~~~
			Page<? extends GenericBean> page = clsService.findPage(C120M01A.class, search);			
			for(GenericBean t: page.getContent()){
				C120M01A c120m01a = (C120M01A)t;
				String[] param = new String[3];
				param[0] = LMSUtil.getCustKey_len10custId(c120m01a.getCustId(), c120m01a.getDupNo());
				param[1] = c120m01a.getOid();
				param[2] = CapDate.convertTimestampToString(c120m01a.getUpdateTime()!=null?c120m01a.getUpdateTime():c120m01a.getCreateTime()
						,  UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS );
				//~~~
				exist_list.add(param);
				oid_model_map.put(c120m01a.getOid(), c120m01a);
			}			
		}
		
		Map<String, Object> colMap = new HashMap<String, Object>();
		if(true){
			colMap.put("o_single", "Y");
			colMap.put("o_chkYN", "N");
			colMap.put("creator", "sys");
			colMap.put("updater", "sys");
		}
		Map<String, String> map = OverSeaUtil.get_max_colData(exist_list);
		int procCnt = 0;
		for(String idDup : map.keySet() ){
			String custId = Util.trim(StringUtils.substring(idDup, 0, 10));
			String dupNo = Util.trim(StringUtils.substring(idDup, 10));
			String oid = map.get(idDup);
			C120M01A singleY = clsService.findC120M01A_o_single_Y(ownBrId, custId, dupNo);
			if(singleY!=null){
				logger.info("[testCopyDocC120Data]"+custId+"-"+dupNo+" exist");
				continue;
			}
			C120M01A c120m01a = oid_model_map.get(oid);
			String newMainId = IDGenerator.getUUID();
			C120M01A new_c120m01a = clsService.copyC120Relate(c120m01a, newMainId, colMap);
			logger.info("[testCopyDocC120Data]"+custId+"-"+dupNo+" copy, new_c120m01a.oid="+new_c120m01a.getOid());
			procCnt++;
			//======
			if(procCnt >= genCnt){
				break;
			}
		}
	
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult fixImpRatingDoc(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] dataSplit = Util.trim(params.getString("idarr")).split("\\|");
		int procCnt = 0;
		for(String ratingId: dataSplit){
			clsService.fixImpRatingDoc(ratingId);
			procCnt++;
		}
		result.set("procCnt", procCnt);
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult validateAdjustReason(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String keyStr = Util.trim(params.getString("keyStr"));
		String adjustReason = Util.trim(params.getString("adjustReason"));
		String mowType = Util.trim(params.getString("mowType"));
		String func = Util.trim(params.getString("func"));
		
		String reasonCnt = "0";
		String cfmStr = "";
		if(Util.isNotEmpty(adjustReason)){
			Map<String, String> inputMap = new HashMap<String, String>();
			Map<String, String> errorMap = new HashMap<String, String>();
			Map<String, String> confirmMap = new HashMap<String, String>();
			
			inputMap.put(keyStr, adjustReason);
			clsService.validate_adjustReason(inputMap, errorMap, confirmMap, mowType, func);
			
			if(errorMap.size()>0){
				throw new CapMessageException(StringUtils.join(errorMap.values(), ""), getClass());
			}else{
				if(confirmMap.size()>0){
					cfmStr = OverSeaUtil.adjustReason_html(confirmMap);			
				}else{
					//not match
				}				
			}
			reasonCnt = "1";			
		} 
		
		OverSeaUtil.set_adjustReason_fmt_result(result, reasonCnt, cfmStr, prop_abstractOverSeaCLSPage);
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult codeTypeWithOrder(PageParameters params)
	throws CapException {
		String key = Util.trim(params.getString("key"));
		Map<String, String> tm = clsService.get_codeTypeWithOrder(key);
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("item", new CapAjaxFormResult(tm));
		result.set("itemOrder", new ArrayList<String>(tm.keySet()));
		return result;
	}
	
	/**
	 * 查詢主管人員清單
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(AuthType.Modify)
	public IResult queryBoss(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = {SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管, SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;
	}
	
	/**
	 * 建立並儲存簽章欄檔
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	//@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveC121m01e(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String[] formSelectBoss = params.getStringArray("selectBoss");	// L3. 分行授信主管
		String manager = Util.trim(params.getString("sManager"));	//L5. 經副襄理
		C121M01A c121m01a = clsService.findC121M01AByMainId(mainId);
		
		List<C121M01E> list = (List<C121M01E>)  lms1015Service.findC121m01eByMainId(mainId);
		if (!list.isEmpty()) {
			// 總處分行只刪除分行的簽章欄，其他單位保留
			if (LMSUtil.isSpecialBranch(Util.trim(c121m01a.getCaseBrId()))) {
				List<C121M01E> listToDel = new ArrayList<C121M01E>();
				for (C121M01E model : list) {
					if (UtilConstants.BRANCHTYPE.分行.equals(Util.trim(model
							.getBranchType()))) {
						listToDel.add(model);
					}
				}
				if (listToDel != null && !listToDel.isEmpty()) {
					lms1015Service.delListC121m01e(listToDel);
				}
			} else {
				lms1015Service.delListC121m01e(list);
			}
		}
		if (Util.isNotEmpty(formSelectBoss)) {
			List<C121M01E> models = new ArrayList<C121M01E>();
			int seq = 0;
			for (String people : formSelectBoss) {
				C121M01E model = new C121M01E();
				model.setCreator(user.getUserId());
				model.setCreateTime(CapDate.getCurrentTimestamp());
				model.setUpdater(user.getUserId());
				model.setUpdateTime(CapDate.getCurrentTimestamp());
				model.setMainId(mainId);
				model.setBranchType(UtilConstants.BRANCHTYPE.分行);
				model.setBranchId(user.getUnitNo());
				// L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
				model.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
				model.setStaffNo(people);
				model.setSeq(BigDecimal.valueOf(++seq));
				models.add(model);
			}
			C121M01E managerC121m01e = new C121M01E();
			managerC121m01e.setCreator(user.getUserId());
			managerC121m01e.setCreateTime(CapDate.getCurrentTimestamp());
			managerC121m01e.setUpdater(user.getUserId());
			managerC121m01e.setUpdateTime(CapDate.getCurrentTimestamp());
			managerC121m01e.setMainId(mainId);
			managerC121m01e.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
			managerC121m01e.setStaffNo(manager);
			managerC121m01e.setBranchType(UtilConstants.BRANCHTYPE.分行);
			managerC121m01e.setBranchId(user.getUnitNo());
			models.add(managerC121m01e);
			C121M01E apprC121m01e = new C121M01E();
			apprC121m01e.setCreator(user.getUserId());
			apprC121m01e.setCreateTime(CapDate.getCurrentTimestamp());
			apprC121m01e.setUpdater(user.getUserId());
			apprC121m01e.setUpdateTime(CapDate.getCurrentTimestamp());
			apprC121m01e.setMainId(mainId);
			apprC121m01e.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
			apprC121m01e.setStaffNo(user.getUserId());
			apprC121m01e.setBranchType(UtilConstants.BRANCHTYPE.分行);
			apprC121m01e.setBranchId(user.getUnitNo());
			models.add(apprC121m01e);

			lms1015Service.saveListC121m01e(models);
		}
		// 文件狀態
		result.set("docStatus", Util.trim(c121m01a.getDocStatus()));
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryBranchInSameCountry(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();
		
		TreeMap<String, String> tm = new TreeMap<String, String>();
		//因不同國家的基本資料不同，只傳送同一國家
		Set<String> brInSameCountry = LMSUtil.get_TH_BRNO_SET();
		if(brInSameCountry.contains(unitNo)){
			for(String brNo: brInSameCountry){
				if(Util.equals(unitNo, brNo)){
					continue;
				}
				tm.put(brNo, Util.trim(branchService.getBranchName(brNo)));
			}						
		}
		
		result.set("item", new CapAjaxFormResult(tm));
		result.set("itemOrder", new ArrayList<String>(tm.keySet()));
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult copyCustDataToTargetBrNo(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		C120M01A src_c120m01a = clsService.findC120M01A_oid(params.getString(UI_C120_OID));
		String toBrNo = Util.trim(params.getString("toBrNo"));
		//String mainId = Util.trim(params.getString("mainId"));
		if(src_c120m01a==null){
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		if(Util.isEmpty("toBrNo")){
			throw new CapMessageException("toBrNo="+toBrNo, getClass());
		}
		C120M01A targetC120 = clsService.findC120M01A_o_single_Y(toBrNo, src_c120m01a.getCustId(),src_c120m01a.getDupNo());
		if(targetC120!=null){
			throw new CapMessageException(toBrNo+"已有"+src_c120m01a.getCustId()+"-"+src_c120m01a.getDupNo(), getClass());
		}else{
			Map<String, Object> colMap = new HashMap<String, Object>();
			colMap.put("ownBrId", toBrNo);
			targetC120 = clsService.copyC120Relate(src_c120m01a, IDGenerator.getUUID(), colMap);			
			clsService.copyC120AttchDocFile("", targetC120);			
		}
		
		return result;
	}
}