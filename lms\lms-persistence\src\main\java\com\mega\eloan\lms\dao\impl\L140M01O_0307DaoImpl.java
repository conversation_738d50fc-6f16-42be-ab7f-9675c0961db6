/* 
 * L140M01O_0307DaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L140M01O_0307Dao;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140M01O_0307;

/** 擔保品資料股票明細檔 **/
@Repository
public class L140M01O_0307DaoImpl extends LMSJpaDao<L140M01O_0307, String>
		implements L140M01O_0307Dao {

	@Override
	public L140M01O_0307 findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140M01O_0307> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("ctrlKind", false);
		search.addOrderBy("setRatio", true);
		search.addOrderBy("stkNo", false);
		search.setMaxResults(Integer.MAX_VALUE);

		List<L140M01O_0307> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L140M01O_0307> findByIndex01(String mainId, String stkNo) {
		ISearch search = createSearchTemplete();
		List<L140M01O_0307> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (stkNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "stkNo", stkNo);
		// 檢查是否有查詢參數

		search.addOrderBy("ctrlKind", false);
		search.addOrderBy("setRatio", true);
		search.addOrderBy("stkNo", false);
		search.setMaxResults(Integer.MAX_VALUE);

		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140M01O_0307> findByIndex02(String mainId) {
		ISearch search = createSearchTemplete();
		List<L140M01O_0307> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數

		search.addOrderBy("ctrlKind", false);
		search.addOrderBy("setRatio", true);
		search.addOrderBy("stkNo", false);
		search.setMaxResults(Integer.MAX_VALUE);

		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140M01O_0307> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		List<L140M01O_0307> list = null;
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		// 檢查是否有查詢參數

		search.addOrderBy("ctrlKind", false);
		search.addOrderBy("setRatio", true);
		search.addOrderBy("stkNo", false);
		search.setMaxResults(Integer.MAX_VALUE);

		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140M01O_0307> findByStkNoAndStkNm(String mainId, String stkNo,
			String stkNm) {
		ISearch search = createSearchTemplete();
		List<L140M01O_0307> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (stkNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "stkNo", stkNo);
		if (stkNm != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "stkNm", stkNm);

		search.addOrderBy("ctrlKind", false);
		search.addOrderBy("setRatio", true);
		search.addOrderBy("stkNo", false);
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;

	}

}