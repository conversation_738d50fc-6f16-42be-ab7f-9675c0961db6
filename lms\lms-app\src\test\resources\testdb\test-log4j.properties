# Log4J configuring file for application logging
#
# Define the default log level, and the default appenders
# LEVEL : OFF\u3001FATAL\u3001ERROR\u3001WARN\u3001INFO\u3001DEBUG\u3001ALL
#log4j.rootLogger=INFO,Stdout

log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.conversionPattern=%d [%t] [%X{sessionId}] | %X{login} | %X{reqURI} | %-28.28c{1} [%-5p] %m%n

log4j.appender.FILE.encoding=UTF-8
log4j.appender.FILE=org.apache.log4j.RollingFileAppender
log4j.appender.FILE.File=logs/ForCodeType.log
log4j.appender.FILE.MaxFileSize=10240KB
log4j.appender.FILE.MaxBackupIndex=100
log4j.appender.FILE.layout=org.apache.log4j.PatternLayout
log4j.appender.FILE.layout.ConversionPattern=%d [%t] [%X{sessionId}] | %X{login} | %X{reqURI} | %-28.28c{1} [%-5p] %m%n

# ----- APPLICATION
log4j.rootLogger=INFO,FILE,console

log4j.logger.org=ERROR
log4j.logger.net=ERROR
log4j.logger.tw.com.iisi=DEBUG
log4j.logger.tw.com.iisi.cap.ajax.CapAjaxBehavior=TRACE
log4j.logger.tw.com.iisi.cap.jawr.CapJawrRequestHandler=ERROR

log4j.logger.com.mega.eloan=DEBUG

# ----- TRANSACTION
log4j.logger.org.springframework.transaction=WARN
log4j.logger.org.springframework.orm.hibernate3=WARN

# ----- OPENJPA
log4j.logger.openjpa.DefaultLevel=WARN
#log4j.logger.openjpa.Tool=TRACE
#log4j.logger.openjpa.Runtime=TRACE
#log4j.logger.openjpa.Remote=WARN
#log4j.logger.openjpa.DataCache=WARN
#log4j.logger.openjpa.MetaData=WARN
#log4j.logger.openjpa.Enhance=WARN
#log4j.logger.openjpa.Query=TRACE
log4j.logger.openjpa.jdbc.SQL=TRACE
#log4j.logger.openjpa.jdbc.JDBC=WARN
#log4j.logger.openjpa.jdbc.Schema=WARN


# ----- OTHER OPEN SOURCE PACKAGES
# avoid misleading log "No service named XXX is available"
# More on this topic: http://wiki.apache.org/ws/FrontPage/Axis/DealingWithCommonExceptions
log4j.logger.org.springframework.security=WARN
log4j.logger.org.apache.commons=WARN
log4j.logger.org.apache.velocity=WARN
log4j.logger.org.springframework=WARN
#log4j.logger.org.springframework.beans.factory.support=WARN

# ----- JAWR
log4j.logger.net.jawr=ERROR

# ----- WICKET
log4j.logger.org.apache.wicket=WARN
org.apache.wicket.util.watch.ModificationWatcher=ERROR
#log4j.logger.org.apache.wicket.protocol.http.HttpSessionStore=INFO
#log4j.logger.org.apache.wicket.version=WARN
#log4j.logger.org.apache.wicket.RequestCycle=WARN


# ----- SUBSTITUTE SYMBOL
# %c Logger, %c{2 } last 2 partial names
# %C Class name (full agony), %C{2 } last 2 partial names
# %d{dd MMM yyyy HH:MM:ss } Date, format see java.text.SimpleDateFormat, If no date format specifier is given then ISO8601 format is assumed.
# %F File name
# %l Location (caution: compiler-option-dependently)
# %L Line number
# %m user-defined message
# %M Method name
# %p Level
# %r Milliseconds since program start
# %t Threadname
# %x, %X see Doku
# %% individual percentage sign
# Caution: %C, %F, %l, %L, %M slow down program run!

