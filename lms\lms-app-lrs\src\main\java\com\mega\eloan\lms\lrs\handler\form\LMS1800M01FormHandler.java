package com.mega.eloan.lms.lrs.handler.form;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.common.gwclient.EloanBatchClient;
import com.mega.eloan.common.gwclient.EloanSubsysBatReqMessage;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.common.RO412;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.FlowSimplifyService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.lrs.constants.lrsConstants;
import com.mega.eloan.lms.lrs.pages.LMS1800M01Page;
import com.mega.eloan.lms.lrs.service.LMS1800Service;
import com.mega.eloan.lms.lrs.service.LMS1801Service;
import com.mega.eloan.lms.lrs.service.LMS1810Service;
import com.mega.eloan.lms.mfaloan.bean.ELF412;
import com.mega.eloan.lms.mfaloan.bean.ELF412B;
import com.mega.eloan.lms.mfaloan.bean.ELF412C;
import com.mega.eloan.lms.mfaloan.bean.ELF493;
import com.mega.eloan.lms.mfaloan.service.MisELF412BService;
import com.mega.eloan.lms.mfaloan.service.MisELF412CService;
import com.mega.eloan.lms.mfaloan.service.MisELF412Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.eloan.lms.model.L180M01D;
import com.mega.eloan.lms.model.L180M01Z;
import com.mega.eloan.lms.model.L181M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("lms1800formhandler")
@DomainClass(L180M01A.class)
public class LMS1800M01FormHandler extends AbstractFormHandler {

	private static Logger logger = LoggerFactory
			.getLogger(LMS1800M01FormHandler.class);
	private static final DateFormat T_FORMAT = new SimpleDateFormat(
			UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
	@Resource
	LMSService lmsService;

	@Resource
	DocLogService docLogService;

	@Resource
	DocCheckService docCheckService;

	@Resource
	EloanBatchClient eloanBatchClient;

	@Resource
	FlowSimplifyService flowSimplifyService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branchService;

	@Resource
	TempDataService tempDataService;

	@Resource
	LMS1800Service lms1800Service;

	@Resource
	LMS1810Service lms1810Service;

	@Resource
	LMS1801Service lms1801Service;

	@Resource
	MisELF412Service misELF412Service;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	RetrialService retrialService;

	@Resource
	MisELF412BService misELF412BService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	MisELF412CService misELF412CService;

	Properties prop_abstractEloan = MessageBundleScriptCreator
			.getComponentResource(AbstractEloanPage.class);

	Properties prop_lms1800m01 = MessageBundleScriptCreator
			.getComponentResource(LMS1800M01Page.class);

	private CapAjaxFormResult defaultResult(PageParameters params,
			L180M01A meta, CapAjaxFormResult result) throws CapException {
		String branchName = meta == null ? "" : branchService
				.getBranchName(meta.getBranchId());
		// required information
		result.set(EloanConstants.PAGE,
				Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));

		result.set("titleInfo", meta.getBranchId() + " " + branchName);

		return result;
	}

	public IResult queryBranch(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		TreeMap<String, String> tm = retrialService.getBranch(user.getUnitNo());

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("item", new CapAjaxFormResult(tm));
		result.set("itemOrder", new ArrayList<String>(tm.keySet()));
		return result;
	}

	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params)
			throws CapException {
		return _saveAction(params, "Y");
	}

	private CapAjaxFormResult _saveAction(PageParameters params, String tempSave) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y",
				params.getString("allowIncomplete"));

		// ===
		String KEY = "saveOkFlag";

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L180M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try {
				meta = retrialService.findL180M01A_oid(mainOid);

				String page = params.getString(EloanConstants.PAGE);
				if ("01".equals(page)) {
					Date defaultCTLDate = CapDate.parseDate(Util.trim(params
							.getString("defaultCTLDate")));
					if (defaultCTLDate != null) {
						meta.setDefaultCTLDate(defaultCTLDate);
					}
				} else if ("02".equals(page)) {

				}

				retrialService.save(meta);
				// ===
				if (Util.notEquals("Y",
						SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
					// 在tempSave<>Y,若有未填欄位,丟 CapMessageException, 讓
					// saveOkFlag==false

					if (CrsUtil.isNull_or_ZeroDate(meta.getDefaultCTLDate())) {
						String msg = prop_lms1800m01.getProperty("err.noDate");
						if (allowIncomplete) {
							result.set("IncompleteMsg", msg);
						} else {
							throw new CapMessageException(msg, getClass());
						}
					}

                    // J-109-0313 小規模覆審 檢查抽樣率>=10
                    BigDecimal samplingRate = retrialService.cauculateSamplingRate(meta.getMainId(), "");
					if(samplingRate != null) {
						if (samplingRate.compareTo(Util.parseBigDecimal("10")) < 0) {
							// err.rateNotEnough=小規模抽樣率不足，目前抽樣率為：{0}％
							throw new CapMessageException(MessageFormat.format(
									prop_lms1800m01.getProperty("err.rateNotEnough"), samplingRate), getClass());
						}
					}

					// J-110-0272 抽樣覆審 檢查抽樣率>=10
					BigDecimal randomSamplingRate = retrialService.cauculateRandomSamplingRate(meta.getMainId(), "");
					if(randomSamplingRate != null) {
						if (randomSamplingRate.compareTo(Util.parseBigDecimal("10")) < 0) {
							// err.rateNotEnoughByType={0}抽樣率不足，目前抽樣率為：{1}％
							throw new CapMessageException(MessageFormat.format(
									prop_lms1800m01.getProperty("err.rateNotEnoughByType"), "" , randomSamplingRate), getClass());
						}
					}
				}
				result.set(KEY, true);
			} catch (Exception e) {
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}
		}

		result.add(query(params));

		return result;
	}

	/**
	 * 因 L180M01B 沒有 docStatus 傳入的參數只有 mainOid 及 可借修改的欄位 若不指定 CheckDocStatus =
	 * false, 會出現「此文件狀態已改變，請重新讀取最新資訊。」
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveM02(PageParameters params)
			throws CapException {
		String[] rawParam = { "newNextNwDt", "newNCkdMemo" };
		List<String> paramList = new ArrayList<String>();
		for (String k : rawParam) {
			if (params.containsKey(k)) {
				paramList.add(k);
			}
		}

		CapAjaxFormResult result = new CapAjaxFormResult();
		if (paramList.size() > 0) {
			SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

			String mainOid = params.getString(EloanConstants.MAIN_OID);
			L180M01A meta = null;
			if (Util.isNotEmpty(mainOid)) {
				L180M01B model = retrialService.findL180M01B_oid(mainOid);
				meta = retrialService.findL180M01A(model);
				// ---
				if (paramList.contains("newNextNwDt")) {
					Date newNextNwDt = CapDate.parseDate(params
							.getString("newNextNwDt"));

					if (newNextNwDt != null) {
						if (LMSUtil.cmp_yyyyMM(newNextNwDt, "<", new Date())) {
							throw new CapMessageException("下次恢復覆審年月【"
									+ LrsUtil.toStrYM(newNextNwDt)
									+ "】必須等於或晚於本月", getClass());
						}
					}

					if (Util.equals(LrsUtil.NCKD_8_本次暫不覆審,
							model.getNewNCkdFlag())) {
						L180M01B mockItem = new L180M01B();
						DataParse.copy(model, mockItem);
						// ---
						List<String> etraMsg = new ArrayList<String>();

						// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
						Date ndDate = calcNdDate(etraMsg, meta.getBranchId(),
								model.getCustId(), model.getDupNo(), mockItem);
						if (CrsUtil.isNull_or_ZeroDate(ndDate)) {
							throw new CapMessageException("計算下次覆審日異常",
									getClass());
						} else {
							if (LMSUtil.cmp_yyyyMM(newNextNwDt, ">", ndDate)) {
								throw new CapMessageException(StringUtils.join(
										etraMsg, "")
										+ Util.trim(model.getCustId())
										+ " "
										+ Util.trim(model.getElfCName())
										+ "下次恢復覆審日期【"
										+ LrsUtil.toStrYM(newNextNwDt)
										+ "】已逾下次最遲應覆審日【"
										+ LrsUtil.toStrYM(ndDate) + "】。",
										getClass());
							}
						}

					}
				}
				CapBeanUtil.map2Bean(params, model,
						paramList.toArray(new String[paramList.size()]));

				retrialService.save(model);
				retrialService.save(meta);
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L180M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL180M01A_oid(mainOid);

			String page = params.getString(EloanConstants.PAGE);
			if ("01".equals(page)) {
				String branchName = branchService.getBranchName(meta
						.getBranchId());
				{
					LMSUtil.addMetaToResult(result, meta, new String[] {
							"generateDate", "defaultCTLDate", "randomCode" });
				}
				result.set("dataDate", LrsUtil.toStrYM(meta.getDataDate()));
				result.set("branchName", meta.getBranchId() + " " + branchName);
				_result_add_batchNO(result, meta);
				result.set(
						"status",
						prop_abstractEloan.getProperty("docStatus."
								+ meta.getDocStatus()));
				result.set("creator", _id_name(meta.getCreator()));
				result.set("createTime",
						Util.nullToSpace(TWNDate.valueOf(meta.getCreateTime())));
				result.set("updater", _id_name(meta.getUpdater()));
				result.set("updateTime",
						Util.nullToSpace(TWNDate.valueOf(meta.getUpdateTime())));
				result.set("approvercn", _id_name(meta.getApprover()));
				result.set("apprId", _id_name(meta.getApprId()));

			} else if ("02".equals(page)) {

				HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
				setAttchFile(map, "divExcelFile", meta.getMainId(),
						LrsUtil.ATTCH_L180M01A_0);
				setAttchFile(map, "divChkExcelFile", meta.getMainId(),
						LrsUtil.ATTCH_L180M01A_1);
				setAttchFile(map, "divZipFile", meta.getMainId(),
						LrsUtil.ATTCH_L180M01A_ZIP);
				result.set("attch", new CapAjaxFormResult(map));

			} else if ("03".equals(page)) {
			}
		}

		return defaultResult(params, meta, result);
	}

	public IResult load_nckdFlag(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("nckdFlag",
				new CapAjaxFormResult(retrialService.get_lrs_NckdFlagMap()));
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryM02(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		L180M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			L180M01B model = retrialService.findL180M01B_oid(mainOid);
			meta = retrialService.findL180M01A(model);

			// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			// J-108-0078_05097_B1001
			// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
			LMSUtil.addMetaToResult(result, model,
					new String[] { "custId", "dupNo", "elfCName", "projectNo",
							"eCoNm", "eCoNm07A", "busCd", "bussKind",
							"sup3CNm", "updateTime", "elfCrdTTbl", "elfLRDate",
							"elfMowTbl1", "elfFcrdGrad", "rltGuarantor",
							"rltBorrower", "newNCkdMemo", "newNextNwDt",
							"newLRDate", "elfUCkdDt", "elfCancelDt", "elfMDDt",
							"elfProcess", "elfNCkdDate", "elfNCkdMemo",
							"elfNextNwDt", "elfUpdDate", "elfUpdater",
							"elfMemo", "elfRealDt", "ctlType", "isAllNew" });

			result.set(
					"createBY",
					LMSUtil.getDesc(l180m01b_createByMap(),
							Util.trim(model.getCreateBY())));
			result.set(
					"mainDocStatus",
					prop_abstractEloan.getProperty("docStatus."
							+ meta.getDocStatus()));
			result.set("typCd",
					LMSUtil.getDesc(typCdMap(), Util.trim(model.getTypCd())));
			result.set(
					"docStatus1",
					LMSUtil.getDesc(docStatus1Map(),
							Util.trim(model.getDocStatus1())));
			result.set("updater",
					Util.trim(userInfoService.getUserName(model.getUpdater())));
			result.set(
					"elfMowType",
					LMSUtil.getDesc(retrialService.get_lrs_MowType_1(),
							Util.trim(model.getElfMowType())));
			result.set(
					"elfFcrdType",
					LMSUtil.getDesc(retrialService.get_lrs_FcrdType(),
							Util.trim(model.getElfFcrdType())));
			result.set(
					"elfFcrdArea",
					LMSUtil.getDesc(retrialService.get_lrs_FcrdArea(),
							Util.trim(model.getElfFcrdArea())));
			result.set(
					"elfFcrdPred",
					LMSUtil.getDesc(retrialService.get_lrs_FcrdPred(),
							Util.trim(model.getElfFcrdPred())));
			result.set(
					"elfMainCust",
					LMSUtil.getDesc(yes_noMap(),
							Util.trim(model.getElfMainCust())));
			result.set(
					"newNCkdFlag",
					LMSUtil.getDesc(retrialService.get_lrs_NckdFlagMap(),
							Util.trim(model.getNewNCkdFlag())));
			result.set(
					"elfRCkdLine",
					LMSUtil.getDesc(retrialService.get_lrs_RckdLine(),
							Util.trim(model.getElfRCkdLine())));
			result.set(
					"elfUCkdLINE",
					LMSUtil.getDesc(yes_noMap(),
							Util.trim(model.getElfUCkdLINE())));
			result.set(
					"elfCState",
					LMSUtil.getDesc(retrialService.get_lrs_CState(),
							Util.trim(model.getElfCState())));

			// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			result.set(
					"elfRealCkFg",
					LMSUtil.getDesc(yes_noMap(),
							Util.trim(model.getElfRealCkFg())));

			//
			String elfMDFlag = "";
			if (true) {
				String code_mdFlag = Util.trim(model.getElfMDFlag());
				if (Util.isNotEmpty(code_mdFlag)) {
					Map<String, String> mdFlag_map = retrialService
							.get_lrs_MdFlagMap();

					if (mdFlag_map.containsKey(code_mdFlag)) {
						elfMDFlag = (code_mdFlag.length() == 2
								&& code_mdFlag.startsWith("0") ? StringUtils
								.substring(code_mdFlag, 1) : code_mdFlag)
								+ "."
								+ mdFlag_map.get(code_mdFlag);
					} else {
						elfMDFlag = code_mdFlag;
					}
				}
			}

			result.set("elfMDFlag", elfMDFlag);
			result.set(
					"elfNewAdd",
					LMSUtil.getDesc(retrialService.get_lrs_NewAdd(),
							Util.trim(model.getElfNewAdd())));
			result.set("elfNewDate", LrsUtil.toStrYM(LrsUtil
					.model_elfNewDate_to_Date(model.getElfNewDate())));
			result.set("l180m01c_cntrNo",
					retrialService.findL180M01C_cntrNo(model));
			result.set(
					"elfNCkdFlag",
					LMSUtil.getDesc(retrialService.get_lrs_NckdFlagMap(),
							Util.trim(model.getElfNCkdFlag())));
			result.set(
					"elfDBUOBU",
					LMSUtil.getDesc(yes_noMap(),
							Util.trim(model.getElfDBUOBU())));

			List<String> elf412_DBUCOID_list = new ArrayList<String>();
			List<String> elf412_OBUCOID_list = new ArrayList<String>();
			Set<L180M01D> l180m01ds = model.getL180m01ds();
			if (l180m01ds != null && !l180m01ds.isEmpty()) {
				for (L180M01D l180m01d : l180m01ds) {
					String str = l180m01d.getDbuObuId() + " "
							+ l180m01d.getDbuObuName();
					if (Util.equals("0", l180m01d.getDbuObuType())) {
						elf412_DBUCOID_list.add(str);
					} else {
						elf412_OBUCOID_list.add(str);
					}
				}
			}

			result.set("elf412_DBUCOID",
					StringUtils.join(elf412_DBUCOID_list, "、"));
			result.set("elf412_OBUCOID",
					StringUtils.join(elf412_OBUCOID_list, "、"));
			result.set("elfTmeStamp", T_FORMAT.format(model.getElfTmeStamp()));

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			result.set(
					"cltTypeForShow",
					LMSUtil.getDesc(l180m01b_ctlTypeMap(),
							Util.trim(model.getCtlType())));

			result.set("elf412_NEWRPTDOCNO", "");
			if (Util.notEquals(Util.trim(model.getNewRptId()), "")) {
				L120M01A l120m01a = retrialService.findL120M01A_mainId(Util
						.trim(model.getNewRptId()));
				if (l120m01a != null) {
					result.set("elf412_NEWRPTDOCNO", l120m01a.getCaseNo());
				}
			}

			if (model.getNewRptDt() != null) {
				result.set("elf412_NEWRPTSDATE",
						CapDate.formatDate(model.getNewRptDt(), "yyyy-MM-dd"));
			} else {
				result.set("elf412_NEWRPTSDATE", "");
			}

			result.set("elf412_OLDRPTDOCNO", "");
			if (Util.notEquals(Util.trim(model.getOldRptId()), "")) {
				L120M01A l120m01a = retrialService.findL120M01A_mainId(Util
						.trim(model.getOldRptId()));
				if (l120m01a != null) {
					result.set("elf412_OLDRPTDOCNO", l120m01a.getCaseNo());
				}
			}

			if (model.getOldRptDt() != null) {
				result.set("elf412_OLDRPTSDATE",
						CapDate.formatDate(model.getOldRptDt(), "yyyy-MM-dd"));
			} else {
				result.set("elf412_OLDRPTSDATE", "");
			}

			// J-108-0078_05097_B1001
			// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
			result.set(
					"elfIsAllNew",
					LMSUtil.getDesc(yes_noMap(),
							Util.trim(model.getElfIsAllNew())));

			// ===

            // 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
            result.set("elfIsRescue", LMSUtil.getDesc(yes_noMap(),
                            Util.trim(model.getElfIsRescue())));
			result.set("elfGuarFlag", LMSUtil.getDesc(yes_noMap(),
					Util.trim(model.getElfGuarFlag())));
			result.set("elfNewRescue", LMSUtil.getDesc(yes_noMap(),
							Util.trim(model.getElfNewRescue())));
            result.set("elfNewRescueYM", LrsUtil.toStrYM(LrsUtil
                    .model_elfNewDate_to_Date(model.getElfNewRescueYM())));
			// J-110-0272 抽樣覆審
			result.set("elfRandomTypeForShow", LMSUtil.getDesc(
					retrialService.get_codeTypeWithOrder("lms1815m01_elfRandomType"),
					Util.trim(model.getElfRandomType())));
			result.set("elfRandomType", Util.trim(model.getElfRandomType()));
			// J-110-0396 配合授審處，E-Loan企金授信覆審系統修改每月需覆審名單報表，優化由系統排除免覆審名單等事項。
			result.set("elfNReviewShow", Util.isEmpty(Util.trim(model.getElfNReview())) ? "" :
					("（" + LrsUtil.toStrYM(LrsUtil.model_elfNewDate_to_Date(model.getElfNReviewYM())) + "）"
							+ Util.trim(model.getElfNReview())));
		}
		result.set(EloanConstants.MAIN_OID, mainOid);

		String branchId = meta == null ? "" : meta.getBranchId();
		result.set(
				"titleInfo",
				branchId + " "
						+ Util.trim(branchService.getBranchName(branchId)));
		return result;
	}

	private Map<String, String> l180m01b_createByMap() {
		Map<String, String> m = new HashMap<String, String>();
		m.put(lrsConstants.CREATEBY.系統產生,
				prop_lms1800m01.getProperty("L180M01B.createBY.SYS"));
		m.put(lrsConstants.CREATEBY.人工產生,
				prop_lms1800m01.getProperty("L180M01B.createBY.PEO"));
		return m;
	}

	private Map<String, String> typCdMap() {
		Map<String, String> m = new HashMap<String, String>();
		m.put("1", prop_abstractEloan.getProperty("typCd.1"));
		m.put("4", prop_abstractEloan.getProperty("typCd.4"));
		m.put("5", prop_abstractEloan.getProperty("typCd.5"));
		return m;
	}

	private Map<String, String> docStatus1Map() {
		Map<String, String> m = new HashMap<String, String>();
		m.put("1", prop_lms1800m01.getProperty("L180M01B.docStatus1.1"));
		m.put("2", prop_lms1800m01.getProperty("L180M01B.docStatus1.2"));
		return m;
	}

	private Map<String, String> yes_noMap() {
		Map<String, String> m = new HashMap<String, String>();
		m.put("Y", prop_abstractEloan.getProperty("yes"));
		m.put("N", prop_abstractEloan.getProperty("no"));
		return m;
	}

	private void setAttchFile(HashMap<String, JSONArray> map, String ui_id,
			String mainId, String fieldId) throws CapException {
		JSONArray jsonAraay = null;
		if (map.containsKey(ui_id)) {
			jsonAraay = map.get(ui_id);
		} else {
			jsonAraay = new JSONArray();
		}
		// ---
		List<DocFile> docFileList = retrialService.findDocFileByMainIdFieldId(
				mainId, fieldId);
		for (DocFile docFile : docFileList) {
			if (Util.equals(fieldId, docFile.getFieldId())) {
				JSONObject o = new JSONObject();
				o.putAll(DataParse.toJSON(docFile, true));
				o.put("uploadTime", TWNDate.toFullTW(docFile.getUploadTime()));
				jsonAraay.add(o);
			}
		}
		// ---
		map.put(ui_id, jsonAraay);
	}

	private void _result_add_batchNO(CapAjaxFormResult result, L180M01A meta) {
		result.set(
				"batchNO",
				LrsUtil.isExistBatchNo(meta) ? Util.addZeroWithValue(
						meta.getBatchNO(), 3) : "");
	}

	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		L180M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL180M01A_oid(mainOid);

			if (Util.isEmpty(meta.getDefaultCTLDate())) {
				throw new CapMessageException(
						prop_lms1800m01.getProperty("err.noDate"), getClass());
			}

			if (Util.equals(RetrialDocStatusEnum.已核准.getCode(),
					meta.getDocStatus())) {
				if (Util.equals(decisionExpr, "退回")) {
					flowSimplifyService.flowNext(meta.getOid(), decisionExpr);
				} else {
					// 因時間可能＞１分鐘，改在 callBatch 裡執行
				}
			} else {
				flowSimplifyService.flowNext(meta.getOid(), decisionExpr);
			}
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(),
					user.getUserId());
		}
		return defaultResult(params, meta, result);
	}

	private String _id_name(String raw_id) {
		String id = Util.trim(raw_id);
		String name = "";
		if (Util.isNotEmpty(id)) {
			name = Util.trim(userInfoService.getUserName(id));
		}
		return Util.trim(id + " " + name);
	}

	@DomainAuth(AuthType.Modify)
	public IResult genLrsProjectNo(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L180M01A meta = retrialService.findL180M01A_oid(mainOid);

		retrialService.genProjectNo(meta);

		_result_add_batchNO(result, meta);
		return defaultResult(params, meta, result);
	}

	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult genExcel(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		HashSet<String> failSet = new HashSet<String>();
		// String mainDocStatus =
		// Util.trim(params.getString(EloanConstants.MAIN_DOC_STATUS));
		String mode = Util.trim(params.getString("mode"));
		String[] oids = Util.trim(params.getString("oids")).split("\\|");
		if (oids != null && oids.length > 0) {
			for (String oid : oids) {
				L180M01A meta = retrialService.findL180M01A_oid(oid);
				if (meta != null) {
					boolean success = false;
					if (Util.equals("1", mode)) {
						success = lms1801Service.gfnGenCTLListExcel(meta);
					} else if (Util.equals("2", mode)) {
						// '郭慧珠說要先檢查覆審控制檔維護是否有未覆核之文件
						// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
						L181M01A existObj = lms1810Service.findInProcessData(
								meta.getBranchId(), new String[] {
										RetrialDocStatusEnum.編製中.getCode(),
										RetrialDocStatusEnum.待覆核.getCode() },
								"", meta.getOwnBrId());
						if (existObj != null) {
							throw new CapMessageException("覆審控制檔維護 "
									+ meta.getBranchId()
									+ " 尚有未覆核之文件，請覆核後再執行本作業", getClass());
						}
						success = lms1801Service.gfnGenCTLListChkExcel(meta);
					}
					if (success == false) {
						failSet.add(meta.getBranchId());
					}
				}
			}
		}

		if (CollectionUtils.isEmpty(failSet)) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		} else {
			Map<String, String> errmap = new HashMap<String, String>();
			errmap.put("msg", StringUtils.join(failSet, "、"));
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
					RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, errmap));
		}

		return result;
	}

	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult sendBtt(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L180M01A meta = retrialService.findL180M01A_oid(mainOid);
		if (meta == null) {
			meta = new L180M01A();
		}
		retrialService.sendBtt(meta);

		return defaultResult(params, meta, result);
	}

	@DomainAuth(value = AuthType.Modify)
	public IResult deleteMeta(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String failmsg = "";
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L180M01A meta = retrialService.findL180M01A_oid(mainOid);
		Map<String, String> lockedUser = docCheckService.listLockedDocUser(meta
				.getMainId());
		if (lockedUser != null) {
			failmsg = meta.getBranchId() + "分行"
					+ LrsUtil.toStrYM(meta.getDataDate()) + "<br/>"
					+ getPopMessage("EFD0055", lockedUser);
		}
		if (Util.isEmpty(failmsg)) {
			if (Util.equals(RetrialDocStatusEnum.已產生覆審名單報告檔.getCode(),
					meta.getDocStatus())
					&& retrialService.isL170M01A_upELF412(meta)) {
				// 若已有任一筆 L170M01A 上傳，不可刪
				failmsg = meta.getBranchId() + "分行"
						+ LrsUtil.toStrYM(meta.getDataDate())
						+ prop_lms1800m01.getProperty("err.alreadyUpToMis");
			}
		}
		if (Util.isEmpty(failmsg)) {
			lms1800Service.deleteMeta(meta);
		}
		result.set("failmsg", failmsg);
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult checkL180M01Z(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		TreeMap<String, String> banks_dbu = retrialService.getBranch(user
				.getUnitNo());
		LinkedHashMap<String, String> banks_oversea = new LinkedHashMap<String, String>();
		if ("900".equals(user.getUnitNo())) {
			for (IBranch b : branchService.getBranchByUnitType(
					BranchTypeEnum.海外分行.getCode(),
					BranchTypeEnum.海外分行當地有總行.getCode(),
					BranchTypeEnum.海外總行泰國.getCode(),
					BranchTypeEnum.海外總行澳洲加拿大.getCode())) {
				banks_oversea.put(Util.trim(b.getBrNo()),
						Util.trim(b.getBrName()));
			}
		}
		String err_dbu = StringUtils.join(_check_L180M01Z(banks_dbu), ",");
		String err_oversea = StringUtils.join(_check_L180M01Z(banks_oversea),
				",");

		List<String> err = new ArrayList<String>();
		if (Util.isNotEmpty(err_dbu)) {
			err.add("國內分行：" + err_dbu);
		}
		if (Util.isNotEmpty(err_oversea)) {
			err.add("海外分行：" + err_oversea);
		}

		String msg = "已更新完畢";
		if (err.size() > 0) {
			err.add("→ 仍未更新");
			msg = StringUtils.join(err, "<BR/>");
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, msg);

		return result;
	}

	private List<String> _check_L180M01Z(Map<String, String> m) {
		List<String> r = new ArrayList<String>();
		for (String brNo : m.keySet()) {
			if (retrialService.existL180M01Z_sysMonth(brNo) == false) {
				r.add(brNo);
			}
		}
		return r;
	}

	public IResult callBatch(PageParameters params)
			throws CapException, IOException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		try {
			int jq_timeout = Util.parseInt(params.getString("jq_timeout"));
			if (jq_timeout == 0) {
				jq_timeout = 60 * 60;// default
			}

			EloanSubsysBatReqMessage esbrm = new EloanSubsysBatReqMessage();
			esbrm.setUrl(SysParamConstants.SYS_URL_LMS);
			esbrm.setReqFormat(EloanSubsysBatReqMessage.REQ_FMT_JSON);
			esbrm.setServiceId("lrs2BatchServiceImpl");
			esbrm.setTimeout(jq_timeout);
			esbrm.setLocalUrl(true);

			JSONObject requestJSON = new JSONObject();
			Map<String, String> addInfoMap = new HashMap<String, String>();
			String act = Util.trim(params.getString("act"));
			List<String> paramList = new ArrayList<String>();
			if (Util.equals("StartGenerateCTLList", act)) {
				paramList.add("limit_branch");
			} else if (Util.equals("procL182M01A", act)) {
				paramList.add("normalMin");
			} else if (Util.equals("genL180M01A", act)) {
				paramList.add("par_arr");
				paramList.add("userId");
				paramList.add("unitNo");
				paramList.add("unitType");
			} else if (Util.equals("sendBr_genL170M01A", act)) {
				paramList.add(EloanConstants.MAIN_OID);
				paramList.add(LrsUtil.K_GET_LOAN_DATA);
				paramList.add("decisionExpr");
				paramList.add("userId");
				paramList.add("unitNo");
			} else if (Util.equals("l170m01a_approveToEnd", act)) {
				paramList.add("userId");
				paramList.add("unitNo");
			} else if (Util.equals("gen_file_for_download", act)) {
				if (true) {
					if (true) {
						String dir = PropUtil.getProperty("docFile.dir");// docFile.dir=/elnfs
						String systemId = PropUtil.getProperty("systemId");// systemId=LMS
						String file_separator = "/";
						String zfile_oid = IDGenerator.getRandomCode();
						String zfile_loc = dir + (file_separator) + systemId
								+ (file_separator) + zfile_oid
								+ LrsUtil.TEMP_PDF_EXT;

						addInfoMap.put("zfile_oid", zfile_oid);
						addInfoMap.put("zfile_loc", zfile_loc);
					}
					for (String k : addInfoMap.keySet()) {
						params.put(k, addInfoMap.get(k));
					}
				}
				paramList.addAll(params.keySet());
			} else if (Util.equals("downloadZip_cntrNoPdf", act)) {
				paramList.add("mainOid");
				paramList.add("rptOid");
				paramList.add("serviceName");
			}
			// ---
			requestJSON.element("act", act);
			for (String k : paramList) {
				requestJSON.element(k, params.getString(k));
			}
			requestJSON.element("Z_ALL_KEY", StringUtils.join(paramList, "|"));
			// ---
			esbrm.setRequestJSON(requestJSON);
			String respStr = eloanBatchClient.send(esbrm);
			logger.debug("send to batch data={}", respStr);
			// =============
			
			try {
			    ObjectMapper objectMapper = new ObjectMapper();
			    Map<String, String> responseMap = objectMapper.readValue(respStr, Map.class);
			    result.set("r", new CapAjaxFormResult(responseMap)); // 將 Map 物件放入 result

			} catch (Exception e) {
			    logger.error("Error parsing JSON string: {}", respStr, e);
			    result.set("r", "JSON_PARSE_ERROR: " + respStr);
			}

			for (String k : addInfoMap.keySet()) {
				result.set(k, addInfoMap.get(k));
			}

			if (Util.equals("downloadZip_cntrNoPdf", act)) {
				String mainOid = Util.trim(params.getString("mainOid"));
				L180M01A meta = retrialService.findL180M01A_oid(mainOid);
				if (meta != null) {
					HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
					setAttchFile(map, "divZipFile", meta.getMainId(),
							LrsUtil.ATTCH_L180M01A_ZIP);
					result.set("attch", new CapAjaxFormResult(map));
				}

			}
		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}

		return result;
	}

	@DomainAuth(AuthType.Modify)
	public IResult delL180M01Z(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		TreeMap<String, String> banks_dbu = retrialService.getBranch(user
				.getUnitNo());

		Date dataDate = CapDate.parseDate(CapDate
				.getCurrentDate(UtilConstants.DateFormat.YYYY_MM) + "-01");

		for (String branchId : banks_dbu.keySet()) {
			L180M01Z l180m01z = retrialService.findL180M01Z(dataDate, branchId);
			if (l180m01z != null) {
				retrialService.del(l180m01z);
			}
		}
		return result;
	}

	public IResult checkExistL180M01Z_010(PageParameters params) throws CapException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String par_arr = Util.trim(params.getString("par_arr"));
		String unitNo = Util.trim(params.getString("unitNo"));
		List<String> noL180M01ZList = new ArrayList<String>();
		List<String> existBrSkipList = new ArrayList<String>();
		int genCnt = 0;
		try {
			String[] dataSplit = Util.trim(par_arr).split("\\|");
			if (dataSplit == null || dataSplit.length == 0) {
				// 未輸入分行、資料年月
			} else {
				for (String item_branch_basedata : dataSplit) {
					String[] item = item_branch_basedata.split("\\^");
					if (item == null || item.length != 2) {
						continue;
					}
					String branch = item[0];
					String str_basedate = item[1];
					Date baseDate = CapDate.parseDate(str_basedate + "-01");
					if (baseDate == null) {
						continue;
					}

					// 當該分行 無 ELF411 轉 ELF412 的記錄(L180M01Z),略過
					if (retrialService.existL180M01Z_sysMonth(branch) == false) {
						noL180M01ZList.add(branch);
						continue;
					}

					if (lms1800Service.isL180M01AInCompiling(unitNo, branch,
							baseDate)) {
						existBrSkipList.add(branch);
						continue;
					}
					genCnt++;
				}
			}

		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e));
		}

		List<String> msg = new ArrayList<String>();
		if (noL180M01ZList.size() > 0) {
			msg.add("分行：" + StringUtils.join(noL180M01ZList, "、")
					+ "MIS控制檔資料尚未匯入完成");
		}
		if (existBrSkipList.size() > 0) {
			msg.add("分行：" + StringUtils.join(existBrSkipList, "、")
					+ "已有編製中資料，不重複產生");
		}
		// ---
		result.set("isMsg", (msg.size() > 0) ? "Y" : "N");
		result.set("msg", StringUtils.join(msg, "<br/>"));
		result.set("allSkip", genCnt == 0 ? "Y" : "N");
		return result;
	}

	public IResult saveCtlDate(PageParameters params)
			throws CapException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String[] oids = Util.trim(params.getString("oids")).split("\\|");
		Date defaultCTLDate = CapDate.parseDate(Util.trim(params
				.getString("defaultCTLDate")));
		for (String oid : oids) {
			L180M01A meta = retrialService.findL180M01A_oid(oid);
			if (meta == null) {
				continue;
			}
			if (defaultCTLDate == null) {
				continue;
			}
			meta.setDefaultCTLDate(defaultCTLDate);
			retrialService.save(meta);
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPrintL140M01AParam(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = Util.trim(params.getString("oids")).split("\\|");
		List<String> noL120M01A = new ArrayList<String>();
		List<String> noL140M01A = new ArrayList<String>();
		List<String> paramList = new ArrayList<String>();
		String ctlType = LrsUtil.CTLTYPE_主辦覆審;

		if (oids != null && oids.length > 0) {
			List<L180M01B> l180m01b_list = new ArrayList<L180M01B>();
			String brNo = "";
			List<String> custId_list = new ArrayList<String>();
			List<String> dupNo_list = new ArrayList<String>();

			if (true) {
				List<String> oid_list = new ArrayList<String>();
				for (String oid : oids) {
					oid_list.add(oid);
				}
				l180m01b_list = retrialService.findL180M01B_oid(oid_list);
				for (L180M01B l180m01b : l180m01b_list) {
					if (Util.isEmpty(brNo)) {
						brNo = retrialService.findL180M01A(l180m01b)
								.getBranchId();
					}

					if (Util.equals(l180m01b.getCtlType(), LrsUtil.CTLTYPE_自辦覆審)) {
						// ●●純常董會(董事會)權限實地覆審用(僅適用一般分行(純實地覆審)，不含 007、201、025)
						// 分行的常董會實地覆審報告表，通常都是國外部、金控總部簽的，所以原來用CASEBRID=分行的條件，分行反映不適合
						ctlType = LrsUtil.CTLTYPE_自辦覆審;
					}

					custId_list.add(l180m01b.getCustId());
					dupNo_list.add(l180m01b.getDupNo());
				}
			}

			Map<String, String> process_l180m01b = new HashMap<String, String>();

			Map<String, L120M01A> map = lms1800Service.findPrint_L120M01A(brNo,
					custId_list, dupNo_list, ctlType);
			for (L180M01B l180m01b : l180m01b_list) {

				// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				// 註記這筆已經處理過，後續補沒有ELF412 但有 ELF412B時就不再處理
				String buildKey = LMSUtil.getCustKey_len10custId(
						l180m01b.getCustId(), l180m01b.getDupNo());

				// 已經處理了
				if (process_l180m01b.containsKey(buildKey)) {
					continue;
				} else {
					process_l180m01b.put(buildKey, buildKey);
				}

				L120M01A l120m01a = map.get(LMSUtil.getCustKey_len10custId(
						l180m01b.getCustId(), l180m01b.getDupNo()));
				if (l120m01a == null) {
					noL120M01A.add(Util.trim(l180m01b.getCustId()) + " "
							+ Util.trim(l180m01b.getElfCName()));
					continue;
				}

				List<L140M01A> l140m01a_list = lms1800Service
						.findPrint_L140M01A(l120m01a.getMainId());
				if (l140m01a_list == null || l140m01a_list.size() == 0) {
					noL140M01A.add(Util.trim(l180m01b.getCustId()) + " "
							+ Util.trim(l180m01b.getElfCName()));
					continue;
				}

				// ref : lms1201r01rptservice,
				String rptNo = Util.equals("Y", l120m01a.getIsHeadCheck()) ? "R13"
						: "R12";
				for (L140M01A l140m01a : l140m01a_list) {
					paramList.add(l180m01b.getOid() + "^" + rptNo + "^"
							+ l140m01a.getOid());
				}
			}
		}
		List<String> notProc = new ArrayList<String>();
		if (true) {
			if (noL120M01A.size() > 0) {
				notProc.add("無簽報書：" + StringUtils.join(noL120M01A, "、"));
			}
			if (noL140M01A.size() > 0) {
				notProc.add("簽報書無額度：" + StringUtils.join(noL140M01A, "、"));
			}
		}
		if (notProc.size() > 0) {
			result.set("notProc", StringUtils.join(notProc, "<br/>"));
		}
		if (paramList.size() > 0) {
			result.set("parStr", StringUtils.join(paramList, "|"));
		}
		return result;
	}

	public IResult produceNew(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L180M01A meta = retrialService.findL180M01A_oid(mainOid);

		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String cName = Util.trim(params.getString("custName"));

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		String ctlType = Util.trim(params.getString("ctlType"));

		// 檢查 custId 是否已存在工作底稿
		for (L180M01B model : retrialService.findL180M01BDefaultOrder(meta
				.getMainId())) {

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			if (Util.equals(model.getCustId(), custId)
					&& Util.equals(model.getDupNo(), dupNo)
					&& Util.equals(model.getCtlType(), ctlType)) {
				throw new CapMessageException(
						prop_lms1800m01.getProperty("err.alreadyHave"),
						getClass());
			}
		}

		if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {
			ELF412B elf412b = misELF412BService.findByPk(meta.getBranchId(),
					custId, dupNo);
			if (elf412b == null) {
				throw new CapMessageException(
						prop_lms1800m01.getProperty("err.notInELF412"),
						getClass());
			}
		} else if (Util.equals(ctlType, LrsUtil.CTLTYPE_價金履約)) {
			ELF412C elf412c = misELF412CService.findByPk(meta.getBranchId(),
					custId, dupNo);
			if (elf412c == null) {
				throw new CapMessageException(
						prop_lms1800m01.getProperty("err.notInELF412"),
						getClass());
			}
		} else {
			ELF412 elf412 = misELF412Service.findByPk(meta.getBranchId(),
					custId, dupNo);
			if (elf412 == null) {
				throw new CapMessageException(
						prop_lms1800m01.getProperty("err.notInELF412"),
						getClass());
			}
		}

		boolean r = lms1800Service.produceNew(meta, custId, dupNo, cName,
				ctlType);
		if (r == false) {
			throw new CapMessageException(
					prop_lms1800m01.getProperty("err.insertFail"), getClass());
		}

		return defaultResult(params, meta, result);
	}

	public IResult saveNoCTL(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L180M01A meta = retrialService.findL180M01A_oid(mainOid);
		String newNCkdFlag = Util.trim(params.getString("newNCkdFlag"));
		Date newNextNwDt = CapDate.parseDate(params.getString("newNextNwDt"));
		String[] oids = Util.trim(params.getString("oids")).split("\\|");

		if (newNextNwDt != null) {
			if (LMSUtil.cmp_yyyyMM(newNextNwDt, "<", new Date())) {
				throw new CapMessageException("下次恢復覆審年月【"
						+ LrsUtil.toStrYM(newNextNwDt) + "】必須等於或晚於本月",
						getClass());
			}
		}

		List<L180M01B> l180m01b_list = new ArrayList<L180M01B>();
		if (oids != null && oids.length > 0) {

			for (String oid : oids) {
				L180M01B model = retrialService.findL180M01B_oid(oid);
				// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				if (Util.notEquals(model.getCtlType(), LrsUtil.CTLTYPE_自辦覆審)
						&& (Util.equals(newNCkdFlag,
								LrsUtil.NCKD_A_非董事會或常董會權限案件)
								|| Util.equals(newNCkdFlag,
										LrsUtil.NCKD_B_參貸同業主辦之聯合授信案件)
								|| Util.equals(
										newNCkdFlag,
										LrsUtil.NCKD_C_國內營業單位辦理之境外公司授信案件含對大陸地區授信)
								|| Util.equals(newNCkdFlag,
										LrsUtil.NCKD_D_國外營業單位單獨承做之跨國非當地國授信案件) || Util
								.equals(newNCkdFlag, LrsUtil.NCKD_E_非實地覆審主辦分行))) {
					// '不覆審註記
					// A.非董事會(或常董會)權限案件
					// B.參貸同業主辦之聯合授信案件
					// C.國內營業單位辦理之境外公司授信案件（含對大陸地區授信）
					// D.國外營業單位單獨承做之跨國(非當地國)授信案件

					throw new CapMessageException("不覆審註記【" + newNCkdFlag
							+ "】僅適用於董事會(或常董會)權限實地覆審案件", getClass());

				}

				// J-106-0278-002 Web
				// e-Loan國內企金授信配合實地覆審作業，額度簽報明細表增加聯貸案件管理行之建檔及修改實地覆審相關檢核
				// elfNCkdFlag
				if (Util.equals(newNCkdFlag, "1")) {
					String elfBranch = Util.trim(meta.getBranchId());
					String elfCustId = Util.trim(model.getCustId());
					String elfDupNo = Util.trim(model.getDupNo());
					String elfCtlType = Util.trim(model.getCtlType());

					String errMsg = retrialService.chkNckdFlag_1(elfBranch,
							elfCustId, elfDupNo, elfCtlType);
					if (Util.notEquals(errMsg, "")) {
						// 不覆審代碼為「1.本行或同業主辦之聯貸案件，非擔任管理行。」
						// ，<BR>借款人項下所有屬該分行之聯貸額度序號(額度序號前三碼為該分行代號且a-Loan額度控管種類為30/60)
						// <BR>必須至少有一筆攤貸行之覆審報告表且該分行有覆審控制檔且不覆審代碼目前非「1.本行或同業主辦之聯貸案件，非擔任管理行。」
						throw new CapMessageException(errMsg, getClass());
					}

				}

                // 非小規模覆審不能選11
                if(Util.equals(newNCkdFlag, LrsUtil.NCKD_11_小規模營業人_央行C方案_已抽樣覆審於次年起免辦覆審或未列於抽樣需覆審名單內)){
                    // J-109-0313 小規模覆審 - 以最新判斷為主
                    String[] arr = lmsService.getOnlySmallBussCaseC(Util.trim(model.getCustId()), Util.trim(model.getDupNo()));
                    if(Util.notEquals(arr[0], "Y")){
                        throw new CapMessageException("非純小規模營業人，不覆審代碼不得為【 11 】。",
                                getClass());
                    }
                }

                // J-109-0456 五百萬元(含)以下、信保七成(含)以上、不循環動用
                if(Util.equals(newNCkdFlag, LrsUtil.NCKD_12_有效額度NTD1000w以下信保七成以上或十足擔保之不循環案件_已於新作增貸後辦理一次覆審)){
                    // J-110-0272 不覆審代碼12條件放寬
                    //  新臺幣一千萬元以下且為十足擔保授信或經信用保證基金保證成數七成以上，均為不循環動用額度者，免再辦理覆審。倘含循環動用者，抽樣覆審。
                    if(!retrialService.isNckd_12(Util.trim(model.getCustId()), Util.trim(model.getDupNo()))){
                        throw new CapMessageException("條件不符合，不覆審代碼不得為【 12 】。", getClass());
                    }
                }

				// J-110-0272 抽樣覆審
				// 不覆審代碼13 不是抽樣案件不可使用
				if(Util.equals(newNCkdFlag, LrsUtil.NCKD_13_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件_未列於抽樣需覆審名單內)){
					if(!Util.equals(Util.trim(model.getElfRandomType()), LrsUtil.RANDOMTYPE_A_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件)){
						throw new CapMessageException("不覆審代碼不得為【 13 】。", getClass());
					}
				}

				// J-106-0278-002 Web
				// e-Loan國內企金授信配合實地覆審作業，額度簽報明細表增加聯貸案件管理行之建檔及修改實地覆審相關檢核
				if (Util.equals(newNCkdFlag, "E")) {
					String elfBranch = Util.trim(meta.getBranchId());
					String elfCustId = Util.trim(model.getCustId());
					String elfDupNo = Util.trim(model.getDupNo());
					String elfCtlType = Util.trim(model.getCtlType());

					String errMsg = retrialService.chkNckdFlag_E(elfBranch,
							elfCustId, elfDupNo, elfCtlType);
					if (Util.notEquals(errMsg, "")) {
						// 不覆審代碼為「1.本行或同業主辦之聯貸案件，非擔任管理行。」
						// ，<BR>借款人項下所有屬該分行之聯貸額度序號(額度序號前三碼為該分行代號且a-Loan額度控管種類為30/60)
						// <BR>必須至少有一筆攤貸行之覆審報告表且該分行有覆審控制檔且不覆審代碼目前非「1.本行或同業主辦之聯貸案件，非擔任管理行。」
						throw new CapMessageException(errMsg, getClass());
					}
				}

				// J-107-0192_05097_B1001 針對企金異常通報戶之覆審，增加覆審名單之控管措施與報表。
				if (Util.notEquals(lrsConstants.CREATEBY.人工產生,
						model.getCreateBY())) {
					if (Util.notEquals(Util.trim(model.getCtlType()),
							LrsUtil.CTLTYPE_自辦覆審)
							&& Util.notEquals(Util.trim(model.getCtlType()),
									LrsUtil.CTLTYPE_價金履約)) {
						// 郭慧珠說，只要這間分行有通報且未解除之異常通報，就一定要覆審
						// J-108-0043_05097_B1001 Web
						// e-Loan企金授信覆審，開放不覆審代碼5拆放同業或對同業之融通時，異常通報案件仍可維護為不覆審
						// && Util.notEquals(afElfNCkdFlag, "5")
						// 2021/02 純小規案件 如異常通報 可不覆審
						String afElfNCkdFlag = Util.trim(newNCkdFlag);
						if (Util.notEquals(afElfNCkdFlag, "")
								&& Util.notEquals(afElfNCkdFlag, "3")
								&& Util.notEquals(afElfNCkdFlag, "5")
								&& Util.notEquals(afElfNCkdFlag, "6")
								&& Util.notEquals(afElfNCkdFlag, "7")
								&& Util.notEquals(afElfNCkdFlag, "8")
								&& Util.notEquals(afElfNCkdFlag, "9")
                                && Util.notEquals(afElfNCkdFlag,
                                    LrsUtil.NCKD_11_小規模營業人_央行C方案_已抽樣覆審於次年起免辦覆審或未列於抽樣需覆審名單內)) {

							// J-107-0213_05097_B1001 Web
							// e-Loan國內企金覆審當同一分行對同一公司同時有自貸案額度及非「任管理行與主辦行」之聯行參貸額度時,該自貸額度,覆審主辦單位不得維護為免辦理覆審之案件
							boolean canPass = false;

							String elfBranch = Util.trim(meta.getBranchId());
							String elfCustId = Util.trim(model.getCustId());
							String elfDupNo = Util.trim(model.getDupNo());
							String elfName = Util.trim(model.getElfCName());
							String elfCtlType = Util.trim(model.getCtlType());

							// ALOAN額度皆為聯貸案非該分行主辦或非該分行額度序號
							if (Util.equals(afElfNCkdFlag, "1")) {

								// 該分行有沒有需覆審之自貸案額度
								List<Map<String, Object>> lnf020Map = misdbBASEService
										.gfnCTL_Import_LNF020_Without_SYND(
												Util.trim(elfBranch),
												Util.trim(elfCustId),
												Util.trim(elfDupNo));

								if (lnf020Map == null || lnf020Map.isEmpty()) {
									// 沒有需覆審之自貸案額度，可以PASS檢核

									// SQL 已排除存單質借、應收帳款買方、進出口、聯貸案
									canPass = true;
								}
							}

							if (!canPass) {
								List<Map<String, Object>> lnf0854Map = misdbBASEService
										.findLnfe0854UnClosedByCustIdAndBrNo(
												elfCustId, elfDupNo, elfBranch);
								if (lnf0854Map != null && !lnf0854Map.isEmpty()) {
									throw new CapMessageException("此分行"
											+ elfCustId + " " + elfName
											+ "有未解除之已核准異常通報表，不得為不覆審案件。",
											getClass());
								}
							}

						}
					}

				}

				// ---
				if (Util.equals(lrsConstants.CREATEBY.人工產生, model.getCreateBY())) {
				} else {
					if (Util.equals(LrsUtil.NCKD_8_本次暫不覆審, newNCkdFlag)) {
						L180M01B mockItem = new L180M01B();
						DataParse.copy(model, mockItem);
						// ---
						List<String> etraMsg = new ArrayList<String>();

						// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
						Date ndDate = calcNdDate(etraMsg, meta.getBranchId(),
								model.getCustId(), model.getDupNo(), mockItem);
						if (CrsUtil.isNull_or_ZeroDate(ndDate)) {
							continue;
						}

						if (LMSUtil.cmp_yyyyMM(newNextNwDt, ">", ndDate)) {
							throw new CapMessageException(StringUtils.join(
									etraMsg, "")
									+ Util.trim(model.getCustId())
									+ " "
									+ Util.trim(model.getElfCName())
									+ "下次恢復覆審日期【"
									+ LrsUtil.toStrYM(newNextNwDt)
									+ "】已逾下次最遲應覆審日【"
									+ LrsUtil.toStrYM(ndDate)
									+ "】。", getClass());
						}
					}
				}
				// ---
				l180m01b_list.add(model);
			}
		}
		lms1800Service.saveNoCTL(meta, l180m01b_list, newNCkdFlag, newNextNwDt);
		return defaultResult(params, meta, result);
	}

	public IResult saveNoCTL_O(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L180M01A meta = retrialService.findL180M01A_oid(mainOid);
		String cmpOid = Util.trim(params.getString("cmpOid"));
		String dataSrc = Util.trim(params.getString("dataSrc"));
		Set<String> existIdDupSet = new HashSet<String>();
		Date exist_defaultCTLDate = CapDate.parseDate(CapDate.ZERO_DATE);
		if (Util.equals(dataSrc, "LMS")) {
			L180M01A cmpMeta = retrialService.findL180M01A_oid(cmpOid);
			exist_defaultCTLDate = cmpMeta.getDefaultCTLDate();
			if (true) {
				for (L180M01B cmp_model : retrialService
						.findL180M01BDefaultOrder(cmpMeta.getMainId())) {
					if (Util.equals(lrsConstants.docStatus1.要覆審,
							cmp_model.getDocStatus1())) {
						existIdDupSet.add(LMSUtil.getCustKey_len10custId(
								cmp_model.getCustId(), cmp_model.getDupNo())
								+ Util.trim(cmp_model.getCtlType()));
					}
				}
			}
		} else if (Util.equals(dataSrc, "MIS")) {
			String elf493_rptDocId = cmpOid;
			List<ELF493> elf493_list = misdbBASEService.findELF493(
					elf493_rptDocId, lrsConstants.docStatus1.要覆審);
			if (CollectionUtils.isNotEmpty(elf493_list)) {
				exist_defaultCTLDate = elf493_list.get(0).getElf493_dfctlDt();
				for (ELF493 elf493 : elf493_list) {
					existIdDupSet
							.add(LMSUtil.getCustKey_len10custId(
									elf493.getElf493_custId(),
									elf493.getElf493_dupNo())
									+ elf493.getElf493_ctlType());
				}
			}
		}
		List<L180M01B> l180m01b_list = new ArrayList<L180M01B>();
		for (L180M01B model : retrialService.findL180M01BDefaultOrder(meta
				.getMainId())) {
			if (Util.equals(lrsConstants.docStatus1.要覆審, model.getDocStatus1())
					&& existIdDupSet.contains(LMSUtil.getCustKey_len10custId(
							model.getCustId(), model.getDupNo())
							+ Util.trim(model.getCtlType()))) {
				l180m01b_list.add(model);
			}
		}
		lms1800Service.saveNoCTL_O(meta, l180m01b_list, exist_defaultCTLDate);

		return defaultResult(params, meta, result);
	}

	public IResult saveReCTL1(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L180M01A meta = retrialService.findL180M01A_oid(mainOid);

		List<String> oid_arr = new ArrayList<String>();
		String[] oids = Util.trim(params.getString("oids")).split("\\|");
		if (oids != null && oids.length > 0) {
			for (String oid : oids) {
				oid_arr.add(oid);
			}
		}
		List<String> needLrDateOidList = new ArrayList<String>();
		List<String> needLrDateDescList = new ArrayList<String>();
		List<String> newLRDateList = new ArrayList<String>();
		List<String> elfRckdLineList = new ArrayList<String>();
		lms1800Service.saveReCTL_1(meta, oid_arr, needLrDateOidList,
				needLrDateDescList, newLRDateList, elfRckdLineList);
		if (needLrDateOidList.size() > 0) {
			result.set("needLrDateOidList", needLrDateOidList);
			result.set("needLrDateDescList", needLrDateDescList);
			result.set("newLRDateList", newLRDateList);
			result.set("elfRckdLineList", elfRckdLineList);
		}

		return defaultResult(params, meta, result);
	}

	public IResult saveReCTL2(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L180M01A meta = retrialService.findL180M01A_oid(mainOid);

		String oid = Util.trim(params.getString(EloanConstants.OID));
		Date newLRDate = CapDate.parseDate(params.getString("newLRDate"));
		lms1800Service.saveReCTL_2(meta, oid, newLRDate);

		return defaultResult(params, meta, result);
	}

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	private Date calcNdDate(List<String> etraMsg, String branchId,
			String custId, String dupNo, L180M01B mockItem) {
		Date baseDate = new Date();
		String mode = "3";
		String ctlType = Util.trim(mockItem.getCtlType());
		if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {

			RO412 src_ro412 = new RO412(branchId, mockItem);
			RO412 ro412 = new RO412(branchId, mockItem);

			Date ndDate = retrialService.gfnCTL_Caculate_DueDate(mode,
					baseDate, null, ro412, null);
			if (ndDate == null) {
				return null;
			} else {
				return ndDate;
			}
		} else if (Util.equals(ctlType, LrsUtil.CTLTYPE_價金履約)) {

			RO412 src_ro412 = new RO412(branchId, mockItem);
			RO412 ro412 = new RO412(branchId, mockItem);

			Date ndDate = retrialService.gfnCTL_Caculate_DueDate(mode,
					baseDate, null, null, ro412);
			if (ndDate == null) {
				return null;
			} else {
				return ndDate;
			}
		} else {
			// LrsUtil.CTLTYPE_主辦覆審
			RO412 src_ro412 = new RO412(branchId, mockItem);
			RO412 ro412 = new RO412(branchId, mockItem);
			// ---
			LinkedHashMap<String, String> elf412_DBUCOID_map = new LinkedHashMap<String, String>();
			LinkedHashMap<String, String> elf412_OBUCOID_map = new LinkedHashMap<String, String>();

			retrialService.gfnCTL_Import_LNF025(branchId, custId, dupNo,
					elf412_DBUCOID_map, elf412_OBUCOID_map);
			String elf412_DBUCOID = LrsUtil.toDBUOBU_COID(elf412_DBUCOID_map);
			String elf412_OBUCOID = LrsUtil.toDBUOBU_COID(elf412_OBUCOID_map);
			boolean hasCoFlag = (elf412_DBUCOID_map.size() > 0 || elf412_OBUCOID_map
					.size() > 0);
			if (hasCoFlag) {
				ro412 = retrialService.gfnGenCTLList_PROCESS_SHARE_GRADE(
						branchId, LMSUtil.getCustKey_len10custId(
								mockItem.getCustId(), mockItem.getDupNo()),
						src_ro412, elf412_DBUCOID_map, elf412_OBUCOID_map);
				// ---
				boolean adjFlag = LrsUtil.get_adjFlag(src_ro412, ro412);
				if (adjFlag) {
					// '因額度共用
					etraMsg.add("依覆審辦法第五條之一(共用戶以符合授信額度標準與特定評等)，覆審週期計算調整為以各共用戶上次覆審日加計半年【B】計算最遲覆審日，惟若有境外公司或新設公司無評等者不予採計。");
					if (elf412_DBUCOID_map.size() > 0) {
						etraMsg.add("DBU共用戶：" + elf412_DBUCOID);
					}
					if (elf412_OBUCOID_map.size() > 0) {
						etraMsg.add("OBU共用戶：" + elf412_OBUCOID);
					}
				}
			}

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			Date ndDate = retrialService.gfnCTL_Caculate_DueDate(mode,
					baseDate, ro412, null, null);
			if (ndDate == null) {
				return null;
			}
			Date minChkDate = null;
			String mainChkID = "";
			if (hasCoFlag) {
				String[] rs = lms1810Service
						.gfnGetNEW_DBUOBU_MINCHKDATE(mode, branchId, ro412,
								elf412_DBUCOID_map, elf412_OBUCOID_map);
				if (Util.isNotEmpty(rs[0])) {
					minChkDate = CapDate.parseDate(rs[1]);
					mainChkID = rs[2];
				}
			}
			if (CrsUtil.isNOT_null_and_NOTZeroDate(minChkDate)
					&& LMSUtil.cmp_yyyyMM(minChkDate, "<", ndDate)) {
				etraMsg.add("以 " + mainChkID + " 覆審日期【"
						+ Util.trim(TWNDate.toAD(minChkDate)) + "】為主");
				return minChkDate;
			} else {
				return ndDate;
			}
		}
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult end_to_01A(PageParameters params)
			throws CapException {

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		boolean passedFlag = false;

		if (Util.isNotEmpty(mainOid)) {
			L180M01A meta = retrialService.findL180M01A_oid(mainOid);
			if (Util.equals(RetrialDocStatusEnum.已產生覆審名單報告檔.getCode(),
					meta.getDocStatus())) {

				passedFlag = true;
				// 要把 TEMPSAVE_RUN 指定為 N
				flowSimplifyService.flowStart("LMS1800Flow", meta.getOid(),
						user.getUserId(), user.getUnitNo());
				retrialService.saveReInitFlow(meta);// 由已產生覆審名單報告檔 →
													// 編製中，要再存一次。目的： 把 bTempData
													// 的資料刪掉
			}

		}
		result.set("passedFlag", passedFlag ? "Y" : "N");

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult inExePriod(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		String errMsg = retrialService.lrsInExePeriod();
		if (Util.isNotEmpty(errMsg)) {
			throw new CapMessageException(errMsg, getClass());
		}

		return result;
	}

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getCtlTypeByBrNo(PageParameters params)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();

		// boolean ctlTypeA = retrialService.chkCtlTypeByBrNo(user.getUnitNo(),
		// LrsUtil.CTLTYPE_主辦覆審);
		//
		// boolean ctlTypeB = retrialService.chkCtlTypeByBrNo(user.getUnitNo(),
		// LrsUtil.CTLTYPE_自辦覆審);
		//
		// boolean ctlTypeC = retrialService.chkCtlTypeByBrNo(user.getUnitNo(),
		// LrsUtil.CTLTYPE_價金履約);

		// if (ctlTypeA && ctlTypeB) {
		// result.set("ctlType", "Z");
		// } else if (ctlTypeA || ctlTypeB) {
		//
		// result.set("ctlType", ctlTypeA ? LrsUtil.CTLTYPE_主辦覆審
		// : LrsUtil.CTLTYPE_自辦覆審);
		// } else {
		// result.set("ctlType", "Z");
		// }

		// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
		Map<String, String> ctlTypeMap = new LinkedHashMap<String, String>();

		List<CodeType> list = codeTypeService.findByCodeTypeList(
				"lms1815m01_elfCtlType", LocaleContextHolder.getLocale().toString());
		if (CollectionUtils.isNotEmpty(list)) {
			for (CodeType ct : list) {
				if (retrialService.chkCtlTypeByBrNo(user.getUnitNo(),
						ct.getCodeValue())) {
					ctlTypeMap.put(ct.getCodeValue(), ct.getCodeDesc());
				}

			}
		}

		CapAjaxFormResult selItem = new CapAjaxFormResult();
		CapAjaxFormResult selItemOrder = new CapAjaxFormResult();
		if (true) {
			_setMapWithOrder(selItem, selItemOrder, "_ctlType", ctlTypeMap);
		}
		result.set("selItem", selItem);
		result.set("selItemOrder", selItemOrder);

		if (ctlTypeMap.size() == 1) {
			String cltType = Util
					.trim((String) ctlTypeMap.keySet().toArray()[0]);
			result.set("ctlType", cltType);
		} else {
			result.set("ctlType", "Z");
		}

		return result;
	}

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	private Map<String, String> l180m01b_ctlTypeMap() {
		Map<String, String> m = new HashMap<String, String>();
		// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
		List<CodeType> list = codeTypeService.findByCodeTypeList(
				"lms1815m01_elfCtlType", LocaleContextHolder.getLocale().toString());
		if (CollectionUtils.isNotEmpty(list)) {
			for (CodeType ct : list) {

				m.put(ct.getCodeValue(),
						StringUtils.replace(Util.trim(ct.getCodeDesc()),
								ct.getCodeValue() + ".", ""));

			}
		}

		// m.put(LrsUtil.CTLTYPE_主辦覆審,
		// prop_lms1800m01.getProperty("L180M01B.ctlType_A"));
		// m.put(LrsUtil.CTLTYPE_自辦覆審,
		// prop_lms1800m01.getProperty("L180M01B.ctlType_B"));
		//
		// m.put(LrsUtil.CTLTYPE_價金履約,
		// prop_lms1800m01.getProperty("L180M01B.ctlType_C"));
		return m;
	}

	private void _setMapWithOrder(CapAjaxFormResult result,
			CapAjaxFormResult rItemOrder, String elm, Map<String, String> m) {
		List<String> ord = new ArrayList<String>();
		if (m instanceof LinkedHashMap) {
			for (String k : m.keySet()) {
				ord.add(k);
			}
		} else {
			TreeSet<String> ts = new TreeSet<String>();
			ts.addAll(m.keySet());
			ord.addAll(ts);
		}
		result.set(elm, new CapAjaxFormResult(m));
		rItemOrder.set(elm, ord);
	}

    public IResult cauculateSamplingRate(PageParameters params)
            throws CapException {
        CapAjaxFormResult result = new CapAjaxFormResult();
        String mainOid = params.getString(EloanConstants.MAIN_OID);
        L180M01A meta = retrialService.findL180M01A_oid(mainOid);
		BigDecimal samplingRate = retrialService.cauculateSamplingRate(meta.getMainId(), "");
		if(samplingRate != null) {
			result.set("samplingRate", samplingRate);
		} else {
			result.set("samplingRate", "");
		}
        return result;
    }

	public IResult cauculateRandomSamplingRate(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L180M01A meta = retrialService.findL180M01A_oid(mainOid);
		BigDecimal randomSamplingRate = retrialService.cauculateRandomSamplingRate(meta.getMainId(), "");
		if(randomSamplingRate != null) {
			result.set("randomSamplingRate", randomSamplingRate);
		} else {
			result.set("randomSamplingRate", "");
		}
		return result;
	}

	// J-110-0272 抽樣覆審
	public IResult samplingList(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		boolean byCnt = params.getBoolean("byCnt");
		int samplingCnt = params.getInt("samplingCnt", 0);

		// 如果從一般的不覆審 改成要覆審  又沒抽中  還是需要輸入上次覆審日  saveReCTL_1()
		List<String> oid_arr = new ArrayList<String>();	// L180M01B 的 oid

		L180M01A meta = retrialService.findL180M01A_oid(mainOid);
		List<Map<String, Object>> src_list = retrialService.getNeedSamplingList(meta.getMainId(), byCnt, samplingCnt);
		int sCnt = src_list.size();
		if(sCnt > 0){
			if(!byCnt){	// 重新抽樣   直接抓10%筆數
				samplingCnt = BigDecimal.valueOf(sCnt).divide(new BigDecimal(10), 0,
						BigDecimal.ROUND_UP).intValue();
			}
			if(sCnt == samplingCnt){ // 百分百抽中
				for (Map<String, Object> src_row : src_list) {
					String L180M01B_oid = Util.trim(MapUtils.getString(src_row, "oid"));
					L180M01B l180m01b = retrialService.findL180M01B_oid(L180M01B_oid);
					if(l180m01b != null){
						l180m01b.setDocStatus1(lrsConstants.docStatus1.要覆審);
						l180m01b.setNewNCkdFlag("");
						l180m01b.setNewNCkdMemo("");
						l180m01b.setNewNextNwDt(null);
						retrialService.save(l180m01b);
					}
				}
			} else if(sCnt > samplingCnt){
				int[] rInt = retrialService.getRandomInt(1, sCnt, samplingCnt);
				int seq = 0;
				for (Map<String, Object> src_row : src_list) {
                    seq++;
					String L180M01B_oid = Util.trim(MapUtils.getString(src_row, "oid"));
					L180M01B l180m01b = retrialService.findL180M01B_oid(L180M01B_oid);
					if(l180m01b != null){
						if (ArrayUtils.contains(rInt, seq)) {	// 壓成要覆審
							l180m01b.setDocStatus1(lrsConstants.docStatus1.要覆審);
							l180m01b.setNewNCkdFlag("");
							l180m01b.setNewNCkdMemo("");
							l180m01b.setNewNextNwDt(null);
							oid_arr.add(l180m01b.getOid());
						} else {	// 壓成不覆審
							l180m01b.setDocStatus1(lrsConstants.docStatus1.不覆審);
							l180m01b.setNewNextNwDt(null);
							l180m01b.setNewNCkdFlag(LrsUtil.NCKD_13_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件_未列於抽樣需覆審名單內);
							l180m01b.setNewLRDate(null);
						}
						retrialService.save(l180m01b);
					}
				}
			} else {
				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤,
						"抽樣筆數：" + samplingCnt + "筆，不可大於資料筆數：" + sCnt + "筆！"), getClass());
			}
		} else {
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, "無可抽樣案件！"),
					getClass());
		}

		List<String> needLrDateOidList = new ArrayList<String>();
		List<String> needLrDateDescList = new ArrayList<String>();
		List<String> newLRDateList = new ArrayList<String>();
		List<String> elfRckdLineList = new ArrayList<String>();
		lms1800Service.saveReCTL_1(meta, oid_arr, needLrDateOidList,
				needLrDateDescList, newLRDateList, elfRckdLineList);
		if (needLrDateOidList.size() > 0) {
			result.set("needLrDateOidList", needLrDateOidList);
			result.set("needLrDateDescList", needLrDateDescList);
			result.set("newLRDateList", newLRDateList);
			result.set("elfRckdLineList", elfRckdLineList);
		}

		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}
}