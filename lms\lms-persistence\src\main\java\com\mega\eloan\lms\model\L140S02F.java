/* 
 * L140S02F.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 房屋貸款檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140S02F", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "seq" }))
public class L140S02F extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 序號 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "SEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer seq;

	/**
	 * 轉貸前後為相同性質之政策性貸款
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 是|Y<br/>
	 * 否|N
	 */
	@Size(max = 1)
	@Column(name = "FAVCHGCASE", length = 1, columnDefinition = "CHAR(1)")
	private String favChgCase;

	/**
	 * 優惠房貸額度申請日
	 * <p/>
	 * ※優惠房貸
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "APPDATE", columnDefinition = "DATE")
	private Date appDate;

	/**
	 * 新中古屋
	 * <p/>
	 * ※優惠房貸<br/>
	 * N.新屋<br/>
	 * O.中古屋
	 */
	@Size(max = 1)
	@Column(name = "NOHOUSE", length = 1, columnDefinition = "CHAR(1)")
	private String noHouse;

	/**
	 * 售屋者統編
	 * <p/>
	 * ※優惠房貸(二千億優惠房貸)<br/>
	 * ※若售屋者身份為法院(即法拍物)<br/>
	 * sellerId=99999999<br/>
	 * sellerName=法院
	 */
	@Size(max = 11)
	@Column(name = "SELLERID", length = 11, columnDefinition = "VARCHAR(11)")
	private String sellerId;

	/**
	 * 售屋者姓名
	 * <p/>
	 * ※優惠房貸(二千億優惠房貸)<br/>
	 * ※非自行輸入(gfnDb2GetCustName1)
	 */
	@Size(max = 120)
	@Column(name = "SELLERNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String sellerName;

	/**
	 * 核准編號/核發證明編號
	 * <p/>
	 * ※優惠房貸(青年安心成家方案)<br/>
	 * ※優惠房貸(內政部整合住宅方案)
	 */
	@Size(max = 20)
	@Column(name = "CHKNUMBER", length = 20, columnDefinition = "VARCHAR(20)")
	private String chkNumber;

	/**
	 * 產權登記日期
	 * <p/>
	 * ※優惠房貸(青年安心成家方案)
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "HOMEREGISTERDATE", columnDefinition = "DATE")
	private Date homeRegisterDate;

	/**
	 * 客戶類別
	 * <p/>
	 * ※優惠房貸(青年安心成家方案)<br/>
	 * 1.第1類客戶:第三年起實際支付利率為P7-0.533% <br/>
	 * 2.第2類客戶:第三年起實際支付利率為P7+0.042% <br/>
	 * 3.第3類客戶:第三年起實際支付3.利率為與本行議定之一般房貸利率
	 */
	@Size(max = 1)
	@Column(name = "CUSTTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String custType;

	/**
	 * 是否為縣(市)政府首購貸款
	 * <p/>
	 * Ａ台北市、Ｂ台中市、Ｃ基隆市、Ｄ台南市、Ｅ高雄市、Ｆ新北市、Ｇ宜蘭縣、Ｈ桃園縣、Ｉ嘉義市、Ｊ新竹縣<br/>
     * Ｋ苗栗縣、Ｌ台中縣、Ｍ南投縣、Ｎ彰化縣、Ｏ新竹市、Ｐ雲林縣、Ｑ嘉義縣、Ｒ台南縣、Ｓ高雄縣、Ｔ屏東縣<br/>
     * Ｕ花蓮縣、Ｖ台東縣、Ｗ金門縣、Ｘ澎湖縣、Ｙ陽明山、Ｚ連江縣、0無<br/>
	 */
	@Size(max = 1)
	@Column(name = "KGAGREEYN", length = 1, columnDefinition = "CHAR(1)")
	private String kgAgreeYN;

	/**
	 * 縣(市)政府首購貸款核准編號
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否為縣(市)政府首購貸款為「是」才顯示
	 */
	@Size(max = 9)
	@Column(name = "KGAGREENO", length = 9, columnDefinition = "VARCHAR(9)")
	private String kgAgreeNo;

	/**
	 * 縣(市)政府首購貸款核准日
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否為縣(市)政府首購貸款為「是」才顯示
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "KGAGREEDT", columnDefinition = "DATE")
	private Date kgAgreeDt;

	/**
	 * 縣(市)政府首購貸款終止補貼日
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否為縣(市)政府首購貸款為「是」才顯示
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "KGENDDATE", columnDefinition = "DATE")
	private Date kgEndDate;

	/**
	 * 縣(市)政府首購貸款終止補貼原因
	 * <p/>
	 * 100個中文字<br/>
	 * ※2012--10-25 新增<br/>
	 * 當是否為縣(市)政府首購貸款為「是」才顯示
	 */
	@Size(max = 300)
	@Column(name = "KGENDCODE", length = 300, columnDefinition = "VARCHAR(300)")
	private String kgEndCode;

	/**
	 * 縣(市)政府首購貸款補貼起日
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否為縣(市)政府首購貸款為「是」才顯示
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "KGINTDATE", columnDefinition = "DATE")
	private Date kgIntDate;

	/**
	 * 中籤編號
	 * <p/>
	 * ※勞工建購住宅方案
	 */
	@Size(max = 7)
	@Column(name = "TAGNO", length = 7, columnDefinition = "VARCHAR(7)")
	private String tagNo;

	/**
	 * 中籤年份
	 * <p/>
	 * ※勞工建購住宅方案<br/>
	 * YYYY
	 */
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "TAGYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer tagYear;

	/**
	 * 收件編號
	 * <p/>
	 * ※勞工建購住宅方案<br/>
	 * ※收件編號前二碼應為【01,02,11–15,31–46,50–55,80–86,91–98】<br/>
	 * ※依收件編號前2碼，可預設中籤單位<br/>
	 * 01：台北市政府勞工局<br/>
	 * 02：高雄市政府勞工局<br/>
	 * 97：行政院農委會漁業署<br/>
	 * 其他：勞委會
	 */
	@Size(max = 8)
	@Column(name = "TAGRENO", length = 8, columnDefinition = "VARCHAR(8)")
	private String tagReNo;

	/**
	 * 中籤單簽發單位
	 * <p/>
	 * ※勞工建購住宅方案<br/>
	 * 單選：<br/>
	 * 1.台北市政府勞工局<br/>
	 * 2.高雄市政府勞工局<br/>
	 * 3.勞委會<br/>
	 * 4.行政院農委會漁業署
	 */
	@Size(max = 1)
	@Column(name = "TAGUNIT", length = 1, columnDefinition = "CHAR(1)")
	private String tagUnit;

	/**
	 * 輔購市府核准年度
	 * <p/>
	 * ※輔助人民自購住宅方案
	 */
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "ASSAPPYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer assAppYear;

	/**
	 * 名冊編號
	 * <p/>
	 * ※輔助人民自購住宅方案
	 */
	@Size(max = 14)
	@Column(name = "ASSBOOKNO", length = 14, columnDefinition = "VARCHAR(14)")
	private String assBookNo;

	/**
	 * 原貸行庫利率
	 * <p/>
	 * ※輔助人民自購住宅方案<br/>
	 * ％
	 */
	@Digits(integer = 3, fraction = 4, groups = Check.class)
	@Column(name = "ASSORIRATE", columnDefinition = "DECIMAL(7,4)")
	private BigDecimal assOriRate;

	/**
	 * 是否搭配房貸壽險
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 是|Y<br/>
	 * 否|N
	 */
	@Size(max = 1)
	@Column(name = "RMBINSFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String rmbinsFlag;

	/**
	 * 是否搭配房貸壽險利率優惠方案
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否搭配房貸壽險為「是」才出現<br/>
	 * 是|Y<br/>
	 * 否|N
	 */
	@Size(max = 1)
	@Column(name = "RMBINTFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String rmbintFlag;

	/**
	 * 搭配房貸壽險利率優惠方案-期數
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否搭配房貸壽險利率優惠方案為「是」才出現
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "RMBINTTERM", columnDefinition = "DECIMAL(5,0)")
	private Integer rmbintTerm;

	/**
	 * 是否保費融資
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否搭配房貸壽險利率優惠方案為「是」才出現<br/>
	 * 是|Y<br/>
	 * 否|N
	 */
	@Size(max = 1)
	@Column(name = "INSFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String insFlag;

	/**
	 * 是否保費融資-金額
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否保費融資「是」才出現
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "INSLOANBAL", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal insLoanbal;

	/**
	 * 房貸選擇權設定－種類
	 * <p/>
	 * ※歡喜優惠房貸違約條款<br/>
	 * 單選：<br/>
	 * 0.無搭配選擇權<br/>
	 * 1.UP選擇權
	 */
	@Size(max = 1)
	@Column(name = "UPTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String upType;

	/**
	 * 房貸選擇權設定－起期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "UPBEG", columnDefinition = "DECIMAL(5,0)")
	private Integer upBeg;

	/**
	 * 房貸選擇權設定－迄期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "UPEND", columnDefinition = "DECIMAL(5,0)")
	private Integer upEnd;

	/**
	 * 房貸選擇權設定－利率
	 * <p/>
	 * ※歡喜優惠房貸違約條款<br/>
	 * ％
	 */
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "UPRATE", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal upRate;

	/**
	 * 提前還本管制設定第一段－起期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "PCONBEG1", columnDefinition = "DECIMAL(3,0)")
	private Integer pConBeg1;

	/**
	 * 提前還本管制設定第一段－迄期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "PCONEND1", columnDefinition = "DECIMAL(3,0)")
	private Integer pConEnd1;

	/**
	 * 提前還本管制設定第一段－違約金計算條件
	 * <p/>
	 * ※歡喜優惠房貸違約條款<br/>
	 * ％
	 */
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "PCALCON1", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal pCalCon1;

	/**
	 * 提前還本管制設定第二段－起期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "PCONBEG2", columnDefinition = "DECIMAL(3,0)")
	private Integer pConBeg2;

	/**
	 * 提前還本管制設定第二段－迄期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "PCONEND2", columnDefinition = "DECIMAL(3,0)")
	private Integer pConEnd2;

	/**
	 * 提前還本管制設定第二段－違約金計算條件
	 * <p/>
	 * ※歡喜優惠房貸違約條款<br/>
	 * ％
	 */
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "PCALCON2", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal pCalCon2;

	/**
	 * 提前還本違約金免收條件
	 * <p/>
	 * ※歡喜優惠房貸違約條款<br/>
	 * 複選：(eg.1|2|3)<br/>
	 * 1.借款人出售擔保品<br/>
	 * 2.借款人同意本行於其結清貸款後三個月內不給抵押權塗銷同意書者,惟還款時須向借戶徵提切結書,或於簽訂借款契約書內明定之<br/>
	 * 3.其他
	 */
	@Size(max = 5)
	@Column(name = "TNF", length = 5, columnDefinition = "VARCHAR(5)")
	private String tnf;

	/**
	 * 其他說明
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 */
	@Size(max = 60)
	@Column(name = "TNFOTHER", length = 60, columnDefinition = "VARCHAR(60)")
	private String tnfOther;

	/**
	 * 房貸資訊(一)－抵利/連動式
	 * <p/>
	 * ※房貸資訊<br/>
	 * 【註1：抵利型房貸說明：因稅務疑義以暫停辦理。】<br/>
	 * 【註2：連動式房貸說明：方案一以房貸餘額 40% 為上限者，方案二以房貸餘額 70% 為上限者。】<br/>
	 * 單選：<br/>
	 * N.非以下三者<br/>
	 * Y.抵利房貸<br/>
	 * 1.連動式方案一<br/>
	 * 2.連動式方案二
	 */
	@Size(max = 1)
	@Column(name = "ISCREDIT", length = 1, columnDefinition = "CHAR(1)")
	private String isCredit;

	/**
	 * 房貸資訊(一)－引介行員代號(l140s02a, l140s02f皆有此欄位, select * from com.bcodetype where codetype='L140S02F_importId_housebroker' )
	 * <p/>
	 * ※房貸資訊
	 */
	@Size(max = 6)
	@Column(name = "IMPORTID", length = 6, columnDefinition = "VARCHAR(6)")
	private String importId;

	/**
	 * 房貸資訊(一)－是否代付費用
	 * <p/>
	 * ※房貸資訊<br/>
	 * 是|Y<br/>
	 * 否|N
	 */
	@Size(max = 1)
	@Column(name = "ISTAKFEE", length = 1, columnDefinition = "CHAR(1)")
	private String isTakFee;

	/**
	 * 房貸資訊(一)－代付費用管制迄期
	 * <p/>
	 * ※房貸資訊<br/>
	 * isTakFee = Y 才顯示
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "TCONEND", columnDefinition = "DECIMAL(3,0)")
	private Integer tconend;

	/**
	 * 房貸資訊(二)－房貸利率方案
	 * <ul>
	 * <li> <EMAIL>(LNPS028)  </li>
	 * <li> select * from com.bcodetype where codetype='L140S02F_ratePlan'  </li>
	 * </ul>
	 * ※房貸資訊<br/>
	 * ※本欄位供授管處統計分行房貸方案敘做量(LLMLN16A)用，項目請依此順序顯示，不要重新排序<br/>
	 * 單選：<br/>
	 * 12：優質方案<br/>
	 * 14：公教方案<br/>
	 * 13：卓越方案Ａ(階梯式), 在 106/08/22 兆銀總銷字第1060041755號被停用, 承辦人06436. <br/>
	 * 01：卓越方案Ｂ(一段式利率)<br/>
	 * 07：永慶信義特案A<br/>
	 * 08：永慶信義特案B<br/>
	 * 09：一價到底<br/>
	 * 15：壽險專案<br/>
	 * 06：青年安家購屋優惠貸款<br/>
	 * 10：其他(含政策性貸款)<br/>
	 * 11：例外不計入週報(如催收轉正常之新開立帳戶)<br/>
	 * 16：永慶信義100億專案  <br/>
	 * 17：住商公司專案  <br/>
	 * 18：A+方案  <br/>
	 */
	@Size(max = 2)
	@Column(name = "RATEPLAN", length = 2, columnDefinition = "CHAR(2)")
	private String ratePlan;

	/**
	 * 房貸資訊(二)－房貸產品方案
	 * <ul>
	 * <li> <EMAIL>(LNPS028) </li>
	 * <li> select * from com.bcodetype where codetype='L140S02F_prodPlan'  </li>
	 * </ul>
	 * ※房貸資訊<br/>
	 * 01：歡喜雙享砲<br/>
	 * 03：歡喜理財家
	 */
	@Size(max = 2)
	@Column(name = "PRODPLAN", length = 2, columnDefinition = "CHAR(2)")
	private String prodPlan;

	/**
	 * 房貸資訊(二)－借款繳保費之金額
	 * <p/>
	 * ※房貸資訊<br/>
	 * 單位：TWD元
	 */
	@Digits(integer = 15, fraction = 0, groups = Check.class)
	@Column(name = "INSCAMT", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal inscAmt;

	/**
	 * 擔保品資料檔oid
	 * <p/>
	 * L140M01O.oid<br/>
	 * 擔保品稅籍地址<br/>
	 * 當科目為673或473才有
	 */
	@Size(max = 32)
	@Column(name = "CMSSRCOID", length = 32, columnDefinition = "CHAR(32)")
	private String cmsSrcOid;

	/**
	 * 購置房屋擔保放款風險權數檢核表
	 * <p/>
	 * 檢核表之mainId<br/>
	 * C102M01A.mainId<br/>
	 * ※若由額度明細表產生，無授權檔
	 */
	@Size(max = 32)
	@Column(name = "REFMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String refMainId;

	/**
	 * 是否重新辦理
	 * <p>
	 * repeat CHAR(1)<br/>
	 * 2013-3-11新增欄位<br/>
	 * F：第一次申請<br/>
	 * Y：是<br/>
	 * N:否
	 */
	@Size(max = 1)
	@Column(name = "REPEAT", length = 1, columnDefinition = "CHAR(1)")
	private String repeat;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 引介行員名稱
	 */
	@Size(max = 30)
	@Column(name = "IMPORTNAME", length = 30, columnDefinition = "VARCHAR(30)")
	private String importName;
	
	/** 名下房貸(含本次申請)共N戶(1,2,3,4,5,6,7,8,9,A) */
	@Column(name = "HOUSE_ITEM1", length = 1, columnDefinition = "CHAR(1)")
	private String house_item1;
	
	/** 台北市全區或新北市捷運及淡海輕軌捷運 */
	@Column(name = "HOUSE_ITEM2", length = 1, columnDefinition = "CHAR(1)")
	private String house_item2;
	
	/** 公教人員 */
	@Column(name = "HOUSE_ITEM3", length = 1, columnDefinition = "CHAR(1)")
	private String house_item3;
	
	/** 個人年所得60萬元以上且【聯徵中心總負債(含本次)/年所得】8倍以下 */
	@Column(name = "HOUSE_ITEM4", length = 1, columnDefinition = "CHAR(1)")
	private String house_item4;
	
	/** 借款人及配偶合併年所得120萬元以上且【聯徵中心總負債(含本次)/年所得】12倍以下 */
	@Column(name = "HOUSE_ITEM5", length = 1, columnDefinition = "CHAR(1)")
	private String house_item5;
	
	/** 最近半年在本行活(儲)存平均餘額達50萬元以上 */
	@Column(name = "HOUSE_ITEM6", length = 1, columnDefinition = "CHAR(1)")
	private String house_item6;
	
	/** 房貸評等為特A級 */
	@Column(name = "HOUSE_ITEM7", length = 1, columnDefinition = "CHAR(1)")
	private String house_item7;
	
	/** 
	 * 是否符合自住型房貸成長方案之8成條件
	 * Y/N
	 */
	@Column(name = "ISHOUSEPLANEIGHTYPER", length = 1, columnDefinition = "CHAR(1)")
	private String isHousePlanEightyPer;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得序號 **/
	public Integer getSeq() {
		return this.seq;
	}

	/** 設定序號 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/**
	 * 取得轉貸前後為相同性質之政策性貸款
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 是|Y<br/>
	 * 否|N
	 */
	public String getFavChgCase() {
		return this.favChgCase;
	}

	/**
	 * 設定轉貸前後為相同性質之政策性貸款
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 是|Y<br/>
	 * 否|N
	 **/
	public void setFavChgCase(String value) {
		this.favChgCase = value;
	}

	/**
	 * 取得優惠房貸額度申請日
	 * <p/>
	 * ※優惠房貸
	 */
	public Date getAppDate() {
		return this.appDate;
	}

	/**
	 * 設定優惠房貸額度申請日
	 * <p/>
	 * ※優惠房貸
	 **/
	public void setAppDate(Date value) {
		this.appDate = value;
	}

	/**
	 * 取得新中古屋
	 * <p/>
	 * ※優惠房貸<br/>
	 * N.新屋<br/>
	 * O.中古屋
	 */
	public String getNoHouse() {
		return this.noHouse;
	}

	/**
	 * 設定新中古屋
	 * <p/>
	 * ※優惠房貸<br/>
	 * N.新屋<br/>
	 * O.中古屋
	 **/
	public void setNoHouse(String value) {
		this.noHouse = value;
	}

	/**
	 * 取得售屋者統編
	 * <p/>
	 * ※優惠房貸(二千億優惠房貸)<br/>
	 * ※若售屋者身份為法院(即法拍物)<br/>
	 * sellerId=99999999<br/>
	 * sellerName=法院
	 */
	public String getSellerId() {
		return this.sellerId;
	}

	/**
	 * 設定售屋者統編
	 * <p/>
	 * ※優惠房貸(二千億優惠房貸)<br/>
	 * ※若售屋者身份為法院(即法拍物)<br/>
	 * sellerId=99999999<br/>
	 * sellerName=法院
	 **/
	public void setSellerId(String value) {
		this.sellerId = value;
	}

	/**
	 * 取得售屋者姓名
	 * <p/>
	 * ※優惠房貸(二千億優惠房貸)<br/>
	 * ※非自行輸入(gfnDb2GetCustName1)
	 */
	public String getSellerName() {
		return this.sellerName;
	}

	/**
	 * 設定售屋者姓名
	 * <p/>
	 * ※優惠房貸(二千億優惠房貸)<br/>
	 * ※非自行輸入(gfnDb2GetCustName1)
	 **/
	public void setSellerName(String value) {
		this.sellerName = value;
	}

	/**
	 * 取得核准編號/核發證明編號
	 * <p/>
	 * ※優惠房貸(青年安心成家方案)<br/>
	 * ※優惠房貸(內政部整合住宅方案)
	 */
	public String getChkNumber() {
		return this.chkNumber;
	}

	/**
	 * 設定核准編號/核發證明編號
	 * <p/>
	 * ※優惠房貸(青年安心成家方案)<br/>
	 * ※優惠房貸(內政部整合住宅方案)
	 **/
	public void setChkNumber(String value) {
		this.chkNumber = value;
	}

	/**
	 * 取得產權登記日期
	 * <p/>
	 * ※優惠房貸(青年安心成家方案)
	 */
	public Date getHomeRegisterDate() {
		return this.homeRegisterDate;
	}

	/**
	 * 設定產權登記日期
	 * <p/>
	 * ※優惠房貸(青年安心成家方案)
	 **/
	public void setHomeRegisterDate(Date value) {
		this.homeRegisterDate = value;
	}

	/**
	 * 取得客戶類別
	 * <p/>
	 * ※優惠房貸(青年安心成家方案)<br/>
	 * 1.第1類客戶:第三年起實際支付利率為P7-0.533% <br/>
	 * 2.第2類客戶:第三年起實際支付利率為P7+0.042% <br/>
	 * 3.第3類客戶:第三年起實際支付3.利率為與本行議定之一般房貸利率
	 */
	public String getCustType() {
		return this.custType;
	}

	/**
	 * 設定客戶類別
	 * <p/>
	 * ※優惠房貸(青年安心成家方案)<br/>
	 * 1.第1類客戶:第三年起實際支付利率為P7-0.533% <br/>
	 * 2.第2類客戶:第三年起實際支付利率為P7+0.042% <br/>
	 * 3.第3類客戶:第三年起實際支付3.利率為與本行議定之一般房貸利率
	 **/
	public void setCustType(String value) {
		this.custType = value;
	}

	/**
	 * 取得是否為高雄市首購貸款
	 * <p/>
	 * Ａ台北市、Ｂ台中市、Ｃ基隆市、Ｄ台南市、Ｅ高雄市、Ｆ新北市、Ｇ宜蘭縣、Ｈ桃園縣、Ｉ嘉義市、Ｊ新竹縣<br/>
     * Ｋ苗栗縣、Ｌ台中縣、Ｍ南投縣、Ｎ彰化縣、Ｏ新竹市、Ｐ雲林縣、Ｑ嘉義縣、Ｒ台南縣、Ｓ高雄縣、Ｔ屏東縣<br/>
     * Ｕ花蓮縣、Ｖ台東縣、Ｗ金門縣、Ｘ澎湖縣、Ｙ陽明山、Ｚ連江縣、0無<br/>
	 */
	public String getKgAgreeYN() {
		return this.kgAgreeYN;
	}

	/**
	 * 設定是否為高雄市首購貸款
	 * <p/>
	 * Ａ台北市、Ｂ台中市、Ｃ基隆市、Ｄ台南市、Ｅ高雄市、Ｆ新北市、Ｇ宜蘭縣、Ｈ桃園縣、Ｉ嘉義市、Ｊ新竹縣<br/>
     * Ｋ苗栗縣、Ｌ台中縣、Ｍ南投縣、Ｎ彰化縣、Ｏ新竹市、Ｐ雲林縣、Ｑ嘉義縣、Ｒ台南縣、Ｓ高雄縣、Ｔ屏東縣<br/>
     * Ｕ花蓮縣、Ｖ台東縣、Ｗ金門縣、Ｘ澎湖縣、Ｙ陽明山、Ｚ連江縣、0無<br/>
	 **/
	public void setKgAgreeYN(String value) {
		this.kgAgreeYN = value;
	}

	/**
	 * 取得高市首購核准編號
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否為高雄市首購貸款為「是」才顯示
	 */
	public String getKgAgreeNo() {
		return this.kgAgreeNo;
	}

	/**
	 * 設定高市首購核准編號
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否為高雄市首購貸款為「是」才顯示
	 **/
	public void setKgAgreeNo(String value) {
		this.kgAgreeNo = value;
	}

	/**
	 * 取得高市首購核准日
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否為高雄市首購貸款為「是」才顯示
	 */
	public Date getKgAgreeDt() {
		return this.kgAgreeDt;
	}

	/**
	 * 設定高市首購核准日
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否為高雄市首購貸款為「是」才顯示
	 **/
	public void setKgAgreeDt(Date value) {
		this.kgAgreeDt = value;
	}

	/**
	 * 取得高市終止補貼日
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否為高雄市首購貸款為「是」才顯示
	 */
	public Date getKgEndDate() {
		return this.kgEndDate;
	}

	/**
	 * 設定高市終止補貼日
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否為高雄市首購貸款為「是」才顯示
	 **/
	public void setKgEndDate(Date value) {
		this.kgEndDate = value;
	}

	/**
	 * 取得高市終止補貼原因
	 * <p/>
	 * 100個中文字<br/>
	 * ※2012--10-25 新增<br/>
	 * 當是否為高雄市首購貸款為「是」才顯示
	 */
	public String getKgEndCode() {
		return this.kgEndCode;
	}

	/**
	 * 設定高市終止補貼原因
	 * <p/>
	 * 100個中文字<br/>
	 * ※2012--10-25 新增<br/>
	 * 當是否為高雄市首購貸款為「是」才顯示
	 **/
	public void setKgEndCode(String value) {
		this.kgEndCode = value;
	}

	/**
	 * 取得高市首購補貼起日
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否為高雄市首購貸款為「是」才顯示
	 */
	public Date getKgIntDate() {
		return this.kgIntDate;
	}

	/**
	 * 設定高市首購補貼起日
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否為高雄市首購貸款為「是」才顯示
	 **/
	public void setKgIntDate(Date value) {
		this.kgIntDate = value;
	}

	/**
	 * 取得中籤編號
	 * <p/>
	 * ※勞工建購住宅方案
	 */
	public String getTagNo() {
		return this.tagNo;
	}

	/**
	 * 設定中籤編號
	 * <p/>
	 * ※勞工建購住宅方案
	 **/
	public void setTagNo(String value) {
		this.tagNo = value;
	}

	/**
	 * 取得中籤年份
	 * <p/>
	 * ※勞工建購住宅方案<br/>
	 * YYYY
	 */
	public Integer getTagYear() {
		return this.tagYear;
	}

	/**
	 * 設定中籤年份
	 * <p/>
	 * ※勞工建購住宅方案<br/>
	 * YYYY
	 **/
	public void setTagYear(Integer value) {
		this.tagYear = value;
	}

	/**
	 * 取得收件編號
	 * <p/>
	 * ※勞工建購住宅方案<br/>
	 * ※收件編號前二碼應為【01,02,11–15,31–46,50–55,80–86,91–98】<br/>
	 * ※依收件編號前2碼，可預設中籤單位<br/>
	 * 01：台北市政府勞工局<br/>
	 * 02：高雄市政府勞工局<br/>
	 * 97：行政院農委會漁業署<br/>
	 * 其他：勞委會
	 */
	public String getTagReNo() {
		return this.tagReNo;
	}

	/**
	 * 設定收件編號
	 * <p/>
	 * ※勞工建購住宅方案<br/>
	 * ※收件編號前二碼應為【01,02,11–15,31–46,50–55,80–86,91–98】<br/>
	 * ※依收件編號前2碼，可預設中籤單位<br/>
	 * 01：台北市政府勞工局<br/>
	 * 02：高雄市政府勞工局<br/>
	 * 97：行政院農委會漁業署<br/>
	 * 其他：勞委會
	 **/
	public void setTagReNo(String value) {
		this.tagReNo = value;
	}

	/**
	 * 取得中籤單簽發單位
	 * <p/>
	 * ※勞工建購住宅方案<br/>
	 * 單選：<br/>
	 * 1.台北市政府勞工局<br/>
	 * 2.高雄市政府勞工局<br/>
	 * 3.勞委會<br/>
	 * 4.行政院農委會漁業署
	 */
	public String getTagUnit() {
		return this.tagUnit;
	}

	/**
	 * 設定中籤單簽發單位
	 * <p/>
	 * ※勞工建購住宅方案<br/>
	 * 單選：<br/>
	 * 1.台北市政府勞工局<br/>
	 * 2.高雄市政府勞工局<br/>
	 * 3.勞委會<br/>
	 * 4.行政院農委會漁業署
	 **/
	public void setTagUnit(String value) {
		this.tagUnit = value;
	}

	/**
	 * 取得輔購市府核准年度
	 * <p/>
	 * ※輔助人民自購住宅方案
	 */
	public Integer getAssAppYear() {
		return this.assAppYear;
	}

	/**
	 * 設定輔購市府核准年度
	 * <p/>
	 * ※輔助人民自購住宅方案
	 **/
	public void setAssAppYear(Integer value) {
		this.assAppYear = value;
	}

	/**
	 * 取得名冊編號
	 * <p/>
	 * ※輔助人民自購住宅方案
	 */
	public String getAssBookNo() {
		return this.assBookNo;
	}

	/**
	 * 設定名冊編號
	 * <p/>
	 * ※輔助人民自購住宅方案
	 **/
	public void setAssBookNo(String value) {
		this.assBookNo = value;
	}

	/**
	 * 取得原貸行庫利率
	 * <p/>
	 * ※輔助人民自購住宅方案<br/>
	 * ％
	 */
	public BigDecimal getAssOriRate() {
		return this.assOriRate;
	}

	/**
	 * 設定原貸行庫利率
	 * <p/>
	 * ※輔助人民自購住宅方案<br/>
	 * ％
	 **/
	public void setAssOriRate(BigDecimal value) {
		this.assOriRate = value;
	}

	/**
	 * 取得是否搭配房貸壽險
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 是|Y<br/>
	 * 否|N
	 */
	public String getRmbinsFlag() {
		return this.rmbinsFlag;
	}

	/**
	 * 設定是否搭配房貸壽險
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 是|Y<br/>
	 * 否|N
	 **/
	public void setRmbinsFlag(String value) {
		this.rmbinsFlag = value;
	}

	/**
	 * 取得是否搭配房貸壽險利率優惠方案
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否搭配房貸壽險為「是」才出現<br/>
	 * 是|Y<br/>
	 * 否|N
	 */
	public String getRmbintFlag() {
		return this.rmbintFlag;
	}

	/**
	 * 設定是否搭配房貸壽險利率優惠方案
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否搭配房貸壽險為「是」才出現<br/>
	 * 是|Y<br/>
	 * 否|N
	 **/
	public void setRmbintFlag(String value) {
		this.rmbintFlag = value;
	}

	/**
	 * 取得搭配房貸壽險利率優惠方案-期數
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否搭配房貸壽險利率優惠方案為「是」才出現
	 */
	public Integer getRmbintTerm() {
		return this.rmbintTerm;
	}

	/**
	 * 設定搭配房貸壽險利率優惠方案-期數
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否搭配房貸壽險利率優惠方案為「是」才出現
	 **/
	public void setRmbintTerm(Integer value) {
		this.rmbintTerm = value;
	}

	/**
	 * 取得是否保費融資
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否搭配房貸壽險利率優惠方案為「是」才出現<br/>
	 * 是|Y<br/>
	 * 否|N
	 */
	public String getInsFlag() {
		return this.insFlag;
	}

	/**
	 * 設定是否保費融資
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否搭配房貸壽險利率優惠方案為「是」才出現<br/>
	 * 是|Y<br/>
	 * 否|N
	 **/
	public void setInsFlag(String value) {
		this.insFlag = value;
	}

	/**
	 * 取得是否保費融資-金額
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否保費融資「是」才出現
	 */
	public BigDecimal getInsLoanbal() {
		return this.insLoanbal;
	}

	/**
	 * 設定是否保費融資-金額
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 當是否保費融資「是」才出現
	 **/
	public void setInsLoanbal(BigDecimal value) {
		this.insLoanbal = value;
	}

	/**
	 * 取得房貸選擇權設定－種類
	 * <p/>
	 * ※歡喜優惠房貸違約條款<br/>
	 * 單選：<br/>
	 * 0.無搭配選擇權<br/>
	 * 1.UP選擇權
	 */
	public String getUpType() {
		return this.upType;
	}

	/**
	 * 設定房貸選擇權設定－種類
	 * <p/>
	 * ※歡喜優惠房貸違約條款<br/>
	 * 單選：<br/>
	 * 0.無搭配選擇權<br/>
	 * 1.UP選擇權
	 **/
	public void setUpType(String value) {
		this.upType = value;
	}

	/**
	 * 取得房貸選擇權設定－起期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 */
	public Integer getUpBeg() {
		return this.upBeg;
	}

	/**
	 * 設定房貸選擇權設定－起期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 **/
	public void setUpBeg(Integer value) {
		this.upBeg = value;
	}

	/**
	 * 取得房貸選擇權設定－迄期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 */
	public Integer getUpEnd() {
		return this.upEnd;
	}

	/**
	 * 設定房貸選擇權設定－迄期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 **/
	public void setUpEnd(Integer value) {
		this.upEnd = value;
	}

	/**
	 * 取得房貸選擇權設定－利率
	 * <p/>
	 * ※歡喜優惠房貸違約條款<br/>
	 * ％
	 */
	public BigDecimal getUpRate() {
		return this.upRate;
	}

	/**
	 * 設定房貸選擇權設定－利率
	 * <p/>
	 * ※歡喜優惠房貸違約條款<br/>
	 * ％
	 **/
	public void setUpRate(BigDecimal value) {
		this.upRate = value;
	}

	/**
	 * 取得提前還本管制設定第一段－起期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 */
	public Integer getPConBeg1() {
		return this.pConBeg1;
	}

	/**
	 * 設定提前還本管制設定第一段－起期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 **/
	public void setPConBeg1(Integer value) {
		this.pConBeg1 = value;
	}

	/**
	 * 取得提前還本管制設定第一段－迄期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 */
	public Integer getPConEnd1() {
		return this.pConEnd1;
	}

	/**
	 * 設定提前還本管制設定第一段－迄期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 **/
	public void setPConEnd1(Integer value) {
		this.pConEnd1 = value;
	}

	/**
	 * 取得提前還本管制設定第一段－違約金計算條件
	 * <p/>
	 * ※歡喜優惠房貸違約條款<br/>
	 * ％
	 */
	public BigDecimal getPCalCon1() {
		return this.pCalCon1;
	}

	/**
	 * 設定提前還本管制設定第一段－違約金計算條件
	 * <p/>
	 * ※歡喜優惠房貸違約條款<br/>
	 * ％
	 **/
	public void setPCalCon1(BigDecimal value) {
		this.pCalCon1 = value;
	}

	/**
	 * 取得提前還本管制設定第二段－起期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 */
	public Integer getPConBeg2() {
		return this.pConBeg2;
	}

	/**
	 * 設定提前還本管制設定第二段－起期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 **/
	public void setPConBeg2(Integer value) {
		this.pConBeg2 = value;
	}

	/**
	 * 取得提前還本管制設定第二段－迄期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 */
	public Integer getPConEnd2() {
		return this.pConEnd2;
	}

	/**
	 * 設定提前還本管制設定第二段－迄期
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 **/
	public void setPConEnd2(Integer value) {
		this.pConEnd2 = value;
	}

	/**
	 * 取得提前還本管制設定第二段－違約金計算條件
	 * <p/>
	 * ※歡喜優惠房貸違約條款<br/>
	 * ％
	 */
	public BigDecimal getPCalCon2() {
		return this.pCalCon2;
	}

	/**
	 * 設定提前還本管制設定第二段－違約金計算條件
	 * <p/>
	 * ※歡喜優惠房貸違約條款<br/>
	 * ％
	 **/
	public void setPCalCon2(BigDecimal value) {
		this.pCalCon2 = value;
	}

	/**
	 * 取得提前還本違約金免收條件
	 * <p/>
	 * ※歡喜優惠房貸違約條款<br/>
	 * 複選：(eg.1|2|3)<br/>
	 * 1.借款人出售擔保品<br/>
	 * 2.借款人同意本行於其結清貸款後三個月內不給抵押權塗銷同意書者,惟還款時須向借戶徵提切結書,或於簽訂借款契約書內明定之<br/>
	 * 3.其他
	 */
	public String getTnf() {
		return this.tnf;
	}

	/**
	 * 設定提前還本違約金免收條件
	 * <p/>
	 * ※歡喜優惠房貸違約條款<br/>
	 * 複選：(eg.1|2|3)<br/>
	 * 1.借款人出售擔保品<br/>
	 * 2.借款人同意本行於其結清貸款後三個月內不給抵押權塗銷同意書者,惟還款時須向借戶徵提切結書,或於簽訂借款契約書內明定之<br/>
	 * 3.其他
	 **/
	public void setTnf(String value) {
		this.tnf = value;
	}

	/**
	 * 取得其他說明
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 */
	public String getTnfOther() {
		return this.tnfOther;
	}

	/**
	 * 設定其他說明
	 * <p/>
	 * ※歡喜優惠房貸違約條款
	 **/
	public void setTnfOther(String value) {
		this.tnfOther = value;
	}

	/**
	 * 取得房貸資訊(一)－抵利/連動式
	 * <p/>
	 * ※房貸資訊<br/>
	 * 【註1：抵利型房貸說明：因稅務疑義以暫停辦理。】<br/>
	 * 【註2：連動式房貸說明：方案一以房貸餘額 40% 為上限者，方案二以房貸餘額 70% 為上限者。】<br/>
	 * 單選：<br/>
	 * N.非以下三者<br/>
	 * Y.抵利房貸<br/>
	 * 1.連動式方案一<br/>
	 * 2.連動式方案二
	 */
	public String getIsCredit() {
		return this.isCredit;
	}

	/**
	 * 設定房貸資訊(一)－抵利/連動式
	 * <p/>
	 * ※房貸資訊<br/>
	 * 【註1：抵利型房貸說明：因稅務疑義以暫停辦理。】<br/>
	 * 【註2：連動式房貸說明：方案一以房貸餘額 40% 為上限者，方案二以房貸餘額 70% 為上限者。】<br/>
	 * 單選：<br/>
	 * N.非以下三者<br/>
	 * Y.抵利房貸<br/>
	 * 1.連動式方案一<br/>
	 * 2.連動式方案二
	 **/
	public void setIsCredit(String value) {
		this.isCredit = value;
	}

	/**
	 * 取得房貸資訊(一)－引介行員代號(l140s02a, l140s02f皆有此欄位, select * from com.bcodetype where codetype='L140S02F_importId_housebroker' )
	 * <p/>
	 * ※房貸資訊
	 */
	public String getImportId() {
		return this.importId;
	}

	/**
	 * 設定房貸資訊(一)－引介行員代號(l140s02a, l140s02f皆有此欄位, select * from com.bcodetype where codetype='L140S02F_importId_housebroker' )
	 * <p/>
	 * ※房貸資訊
	 **/
	public void setImportId(String value) {
		this.importId = value;
	}

	/**
	 * 取得房貸資訊(一)－是否代付費用
	 * <p/>
	 * ※房貸資訊<br/>
	 * 是|Y<br/>
	 * 否|N
	 */
	public String getIsTakFee() {
		return this.isTakFee;
	}

	/**
	 * 設定房貸資訊(一)－是否代付費用
	 * <p/>
	 * ※房貸資訊<br/>
	 * 是|Y<br/>
	 * 否|N
	 **/
	public void setIsTakFee(String value) {
		this.isTakFee = value;
	}

	/**
	 * 取得房貸資訊(一)－代付費用管制迄期
	 * <p/>
	 * ※房貸資訊<br/>
	 * isTakFee = Y 才顯示
	 */
	public Integer getTconend() {
		return this.tconend;
	}

	/**
	 * 設定房貸資訊(一)－代付費用管制迄期
	 * <p/>
	 * ※房貸資訊<br/>
	 * isTakFee = Y 才顯示
	 **/
	public void setTconend(Integer value) {
		this.tconend = value;
	}

	public String getRatePlan() {
		return this.ratePlan;
	}

	public void setRatePlan(String value) {
		this.ratePlan = value;
	}

	public String getProdPlan() {
		return this.prodPlan;
	}

	public void setProdPlan(String value) {
		this.prodPlan = value;
	}

	/**
	 * 取得房貸資訊(二)－借款繳保費之金額
	 * <p/>
	 * ※房貸資訊<br/>
	 * 單位：TWD元
	 */
	public BigDecimal getInscAmt() {
		return this.inscAmt;
	}

	/**
	 * 設定房貸資訊(二)－借款繳保費之金額
	 * <p/>
	 * ※房貸資訊<br/>
	 * 單位：TWD元
	 **/
	public void setInscAmt(BigDecimal value) {
		this.inscAmt = value;
	}

	/**
	 * 取得擔保品資料檔oid
	 * <p/>
	 * L140M01O.oid<br/>
	 * 擔保品稅籍地址<br/>
	 * 當科目為673或473才有
	 */
	public String getCmsSrcOid() {
		return this.cmsSrcOid;
	}

	/**
	 * 設定擔保品資料檔oid
	 * <p/>
	 * L140M01O.oid<br/>
	 * 擔保品稅籍地址<br/>
	 * 當科目為673或473才有
	 **/
	public void setCmsSrcOid(String value) {
		this.cmsSrcOid = value;
	}

	/**
	 * 取得購置房屋擔保放款風險權數檢核表
	 * <p/>
	 * 檢核表之mainId<br/>
	 * C102M01A.mainId<br/>
	 * ※若由額度明細表產生，無授權檔
	 */
	public String getRefMainId() {
		return this.refMainId;
	}

	/**
	 * 設定購置房屋擔保放款風險權數檢核表
	 * <p/>
	 * 檢核表之mainId<br/>
	 * C102M01A.mainId<br/>
	 * ※若由額度明細表產生，無授權檔
	 **/
	public void setRefMainId(String value) {
		this.refMainId = value;
	}


	/**
	 * 是否重新辦理
	 * <p>
	 * repeat CHAR(1)<br/>
	 * 2013-3-11新增欄位<br/>
	 * F：第一次申請<br/>
	 * Y：是<br/>
	 * N:否
	 */
	public String getRepeat() {
		return repeat;
	}

	/**
	 * 是否重新辦理
	 * <p>
	 * repeat CHAR(1)<br/>
	 * 2013-3-11新增欄位<br/>
	 * F：第一次申請<br/>
	 * Y：是<br/>
	 * N:否
	 */
	public void setRepeat(String repeat) {
		this.repeat = repeat;
	}

	
	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得引介行員名稱 **/
	public String getImportName() {
		return this.importName;
	}

	/** 設定引介行員名稱 **/
	public void setImportName(String value) {
		this.importName = value;
	}

	public String getHouse_item1() {
		return house_item1;
	}

	public void setHouse_item1(String house_item1) {
		this.house_item1 = house_item1;
	}

	public String getHouse_item2() {
		return house_item2;
	}

	public void setHouse_item2(String house_item2) {
		this.house_item2 = house_item2;
	}

	public String getHouse_item3() {
		return house_item3;
	}

	public void setHouse_item3(String house_item3) {
		this.house_item3 = house_item3;
	}

	public String getHouse_item4() {
		return house_item4;
	}

	public void setHouse_item4(String house_item4) {
		this.house_item4 = house_item4;
	}

	public String getHouse_item5() {
		return house_item5;
	}

	public void setHouse_item5(String house_item5) {
		this.house_item5 = house_item5;
	}

	public String getHouse_item6() {
		return house_item6;
	}

	public void setHouse_item6(String house_item6) {
		this.house_item6 = house_item6;
	}

	public String getHouse_item7() {
		return house_item7;
	}

	public void setHouse_item7(String house_item7) {
		this.house_item7 = house_item7;
	}

	public String getIsHousePlanEightyPer() {
		return isHousePlanEightyPer;
	}

	public void setIsHousePlanEightyPer(String isHousePlanEightyPer) {
		this.isHousePlanEightyPer = isHousePlanEightyPer;
	}
}
