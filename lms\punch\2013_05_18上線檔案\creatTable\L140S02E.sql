---------------------------------------------------------
-- LMS.L140S02E 償還方式檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140S02E;
CREATE TABLE LMS.L140S02E (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	SEQ           DECIMAL(5,0)  not null,
	PAYWAY        CHAR(1)      ,
	PAYWAYAMT     DECIMAL(13,0),
	PAYWAYOTH     VARCHAR(1200)  ,
	LASTYM        VARCHAR(6)   ,
	EXTFROM       DECIMAL(3,0) ,
	EXTEND        DECIMAL(3,0) ,
	OVERTERM      DECIMAL(3,0) ,
	NOWEXTEND     CHAR(1)      ,
	NOWFROM       DECIMAL(3,0) ,
	NOWEND        DECIMAL(3,0) ,
	NOWTERM       DECIMAL(3,0) ,
	NOWTERMRATE   DECIMAL(3,0) ,
	ADJCHECK      CHAR(1)      ,
	ADJKIND       CHAR(1)      ,
	ADJITEM       CHAR(1)      ,
	ADJCAPSNUM    DECIMAL(3,0) ,
	ADJCAPENUM    DECIMAL(3,0) ,
	ADJINTSNUM    DECIMAL(3,0) ,
	ADJINTENUM    DECIMAL(3,0) ,
	AMORENUM      DECIMAL(3,0) ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140S02E PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140S02E01;
CREATE UNIQUE INDEX LMS.XL140S02E01 ON LMS.L140S02E   (MAINID, SEQ);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140S02E IS '償還方式檔';
COMMENT ON LMS.L140S02E (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	SEQ           IS '序號', 
	PAYWAY        IS '償還方式', 
	PAYWAYAMT     IS '償還方式(每期攤還本金)', 
	PAYWAYOTH     IS '償還方式(其他)', 
	LASTYM        IS '前次寬限期起始年月', 
	EXTFROM       IS '前次寬限期(起)', 
	EXTEND        IS '前次寬限期(迄)', 
	OVERTERM      IS '截至前次剩餘之寬限期', 
	NOWEXTEND     IS '本次是否有寬限期', 
	NOWFROM       IS '本次寬限期(起)', 
	NOWEND        IS '本次寬限期(迄)', 
	NOWTERM       IS '本次寬限期(餘)', 
	NOWTERMRATE   IS '本次寬限期(％)', 
	ADJCHECK      IS '是否展延', 
	ADJKIND       IS '展延種類', 
	ADJITEM       IS '展延項目', 
	ADJCAPSNUM    IS '本金展延起始期', 
	ADJCAPENUM    IS '本金展延截止期', 
	ADJINTSNUM    IS '利息展延起始期', 
	ADJINTENUM    IS '利息展延截止期', 
	AMORENUM      IS '應收利息攤還截止期', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
