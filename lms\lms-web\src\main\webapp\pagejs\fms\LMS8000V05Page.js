$(document).ready(function(){
    init_grid();
    initUser();
    
    $("#buttonPanel").find("#btnFilter").click(function(){    	
        openFilterBox();
    });
    
    function init_grid(){
        var grid = $("#gridview").iGrid({
            handler: 'lms8000gridhandler', //設定handler
            height: 350, //設定高度
            action: 'queryMisList', //執行的Method
            postData: {
            },
            sortname: "seq|custId|cntrNo|loanNo|followDate",
            sortorder: "asc|asc|asc|asc|asc",
            needPager: true,
            rownumbers: true,
            shrinkToFit: false,
            colModel: [{
                colHeader: i18n.lms8000v01["L260M01A.custId"], //借款戶統一編號
                align: "left", width: 100, sortable: false, name: 'custId',
                formatter: 'click', onclick: sendToEdit
            }, {
                colHeader: i18n.lms8000v01["L260M01A.dupNo"], //重複序號
                align: "left", width: 20, sortable: false, name: 'dupNo'
            }, {
                colHeader: i18n.lms8000v01["L260M01A.custName"], //借款戶名稱
                align: "left", width: 150, sortable: false, name: 'custName'
                //,hidden: userInfo.isOverSea
            }, {
                colHeader: i18n.lms8000v01["L260M01A.cntrNo"], //額度序號
                align: "left", width: 100, sortable: false, name: 'cntrNo'
            }, {
                colHeader: (userInfo.isOverSea ? i18n.lms8000v01["L260M01A.loanNoOvs"] : i18n.lms8000v01["L260M01A.loanNo"]), //放款帳號
                align: "left", width: 110, sortable: false, name: 'loanNo'
            }, {
                 colHeader: i18n.lms8000v01["L260M01A.followKind"],
                 align: "left", width: 150, sortable: true, name: 'followKind'
            }, {
                 colHeader: i18n.lms8000v01["L260M01A.followDate"], // 追蹤日期
                 align: "left", width: 100, sortable: true, name: 'followDate' 
            }, {   
                name: 'seq',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridview").getRowData(rowid);
                sendToEdit(null, null, data);
            }
        });
    }

    
    function initUser(){
        $.ajax({
            handler: "codetypehandler",
            action: "userByBrnoTellerId1",//"userByBranch",
            async: false,
            success: function(json){
                $("#fo_staffNo").setItems({
                    item: json,
                    format: "{value} - {key}"
                });
                $("#ao_staffNo").setItems({
                    item: json,
                    format: "{value} - {key}"
                });
            }
        });
    }
    
    function sendToEdit(cellvalue, options, rowObject){
        //msg.sendToEdit=將傳送至編制中起案，是否執行此動作?
        CommonAPI.confirmMessage(i18n.lms8000v01["msg.sendToEdit"], function(b){
            if (b) {
                var custId = rowObject.custId;
                var dupNo = rowObject.dupNo;
                var custName = rowObject.custName;
                var cntrNo = rowObject.cntrNo;
                var loanNo = rowObject.loanNo;
                $.ajax({
                    handler: "lms8000m01formhandler",
                    action: "chkL260M01ANotEnd",
                    data: {
                        single: true,
                        custId: custId,
                        dupNo: dupNo,
                        cntrNo: cntrNo,
                        loanNo: loanNo
                    },
                    success: function(obj){
                        if(obj.msg){
                            return CommonAPI.showErrorMessage(i18n.lms8000v01["msg.alreadyHave2"]);
                        } else {
                            var newType = "1";
                            if(cntrNo == "" || cntrNo == null || cntrNo == undefined){
                                newType = "0";
                            }
                            if(loanNo != undefined && loanNo != ""){
                                newType = "2";
                            }
                            $.ajax({
                                handler: "lms8000m01formhandler",
                                action: 'newl260m01a',
                                data: {
                                    custId: custId,
                                    dupNo: dupNo,
                                    custName: custName,
                                    cntrNo: cntrNo,
                                    loanNo: loanNo,
                                    newType: newType
                                },
                                success: function(obj){
                                    if (obj.raspMsg && obj.raspMsg != "") {
                                        CommonAPI.showMessage(i18n.def["runSuccess"] + "，" + obj.raspMsg);
                                    } else {
                                        CommonAPI.showMessage(i18n.def["runSuccess"]);
                                    }
                                }
                            });
                        }
                    }
                });
            }
        });
    }
    
    
    // 篩選
    function openFilterBox(){
        var $filterForm = $("#filterForm");
        $filterForm.reset();            // 初始化
       
        $("#filterBox").thickbox({
            // LMS8000V01.title=請輸入欲查詢項目：
            title: i18n.lms8000v01["LMS8000V01.title"],
            width: 650,
            height: 210,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#filterForm").valid()) {
                        return;
                    }
                    grid();

                    $.thickbox.close();
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
    }
    
    function grid(){
        $("#gridview").jqGrid("setGridParam", {        	        		
            postData: $.extend($("#filterForm").serializeData(), {
                handler: "lms8000gridhandler",
                formAction: "queryMisList",
                searchType: "filter"
            }),
            rowNum: 15,
            page: 1
        }).trigger("reloadGrid");
    }
        
});