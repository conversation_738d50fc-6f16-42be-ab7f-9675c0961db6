/* 
 * L140S02HDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S02H;

/** 代償轉貸借新還舊明細檔 **/
public interface L140S02HDao extends IGenericDao<L140S02H> {

	L140S02H findByOid(String oid);

	List<L140S02H> findByOids(String[] oids);

	List<L140S02H> findByMainId(String mainId);

	List<L140S02H> findByMainIdSeq(String mainId, int seq);

	L140S02H findByUniqueKey(String mainId, Integer seq, String bankNo,
			String branchNo, String subACNo);

	List<L140S02H> findByIndex01(String mainId, Integer seq, String bankNo,
			String branchNo, String subACNo);
}