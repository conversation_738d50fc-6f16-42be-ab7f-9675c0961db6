/* Head
 #top {
 padding: 0px;
 border-bottom: 1px solid #C7C7C7;
 margin: 2px;
 }
 #top a{
 color: black;
 }
 */ /* End Head */ /* Colors */
.color-yellow {
    background: #f2bc00;
}

.color-red {
    background: #dd0000;
}

.color-blue {
    background: #148ea4;
}

.color-white {
    background: #4f4f4f;
}

.color-orange {
    background: #f66e00;
}

.color-green {
    background: #8dc100;
}

.color-red2 {
	background: #EF575A;
}

.color-yellow h3, .color-white h3, .color-green h3 {
    color: #FFF;
}

.color-red h3, .color-blue h3, .color-orange h3 {
    color: #FFF;
}

/* End Colors */ /* Head section */
#head {
    background: #0A3579;
    height: 178px;
}

#head h1 {
    line-height: 180px;
    color: #0A3579;
    text-align: center;
    background: url(/webroot/img/head.jpg) no-repeat center;
    text-indent: -9999em
}

/* End Head Section */ /* Columns section */
.datalist tr {
    border-bottom: 1px dashed;
    height: 40px;
}

.datalist td {
    padding: 0 3px 0 3px;
}

.datalist tr.odd {
    background: #FFF8EA;
}

.datalist tr:hover {
    background: #FFE8EA;
    border-bottom: 1px solid;
}

#columns .column {
    float: left;
    /* Min-height: */
    min-height: 400px;
    height: auto !important;
    font-size: 92%;
}

#columns .column_l {
    width: 30%;
    /* add buttom padding size */
    padding-bottom: 80px;
}

#columns .column_c {
    width: 70%;
    padding-bottom: 80px;
}

#columns .column_r {
    width: 24%;
    padding-bottom: 80px;
}

/* Column dividers (background-images) : */
#columns #column1 {
    background: no-repeat right top;
}

#columns #column3 {
    background: no-repeat left top;
}

#columns #column1 .widget {
    margin: 10px 5px 0 5px;
}

#columns #column3 .widget {
    margin: 10px 5px 0 5px;
}

#columns .widget {
    margin: 10px 5px 0 5px;
    padding: 1px;
    "-moz-border-radius": 4px;
    "-webkit-border-radius": 4px;
}

#columns .widget .widget-head {
    color: #FFF;
    overflow: hidden;
    width: 100%;
    height: 30px;
    line-height: 30px;
}

#columns .widget .widget-head h3 {
    padding: 0 5px;
    float: left;
}

#columns .widget .widget-content {
    background: #fff; /*url(/webroot/img/widget-content-bg.png) repeat-x;*/
    padding: 0 5px;
    color: #333;
    "-moz-border-radius-bottomleft": 2px;
    "-moz-border-radius-bottomright": 2px;
    "-webkit-border-bottom-left-radius": 2px;
    "-webkit-border-bottom-right-radius": 2px;

line-height:
    1.2em;

overflow:hidden;
}

#columns .widget .widget-content p {
    padding: 0.8em 0;
    border-bottom: 0px solid #666;
}

#columns .widget .widget-content img {
    float: right;
    margin: 10px;
    border: 1px solid #FFF;
}

#columns .widget .widget-content pre {
    padding: 0.5em 5px;
    color: #EEE;
    font-size: 13px;
}

#columns .widget .widget-content ul {
    padding: 5px 0 5px 20px;
    list-style: disc;
}

#columns .widget .widget-content ul li {
    padding: 3px 0;
}

#columns .widget .widget-content ul.images {
    padding: 7px 0 0 0;
    list-style: none;
    height: 1%;
}

#columns .widget .widget-content ul.images li {
    display: inline;
    float: left;
}

#columns .widget .widget-content ul.images img {
    display: inline;
    float: left;
    margin: 0 0 7px 7px;
}

/* End Columns section */