/* 
 * BRelated.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import tw.com.iisi.cap.model.GenericBean;

/**
 * <pre>
 * 引用資料關聯記錄
 * </pre>
 * 
 * @since 2011/7/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/7/21,iristu,new
 *          </ul>
 */
@Entity
// @Table(name = "BRELATED", uniqueConstraints = @UniqueConstraint(columnNames =
// "oid"))
@Table(name = "BRelated")
public class BRelated extends GenericBean implements Serializable {
	private static final long serialVersionUID = 1L;

	public BRelated() {}

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	@Column(length = 300)
	private String docNote;

	@Column(length = 6)
	private String docType1;

	@Column(length = 6)
	private String docType2;

	@Column(length = 32)
	private String mainId1;

	@Column(length = 32)
	private String mainId2;
	
	@Column(length = 3)
	private String tab;
	
	@Column(length = 3)
	private String subtab;

	@Column(length = 1)
	private String relatedflag;
	
	@Column(length = 1)
	private String flag;

	@Column(length = 6)
	private String updater;

	private Timestamp updateTime;
	
	@Column(length=32)
	private String pid1;

	public String getOid() {
		return oid;
	}

	public BRelated setOid(String oid) {
		this.oid = oid;
		return this;
	}

	public String getDocNote() {
		return docNote;
	}

	public BRelated setDocNote(String docNote) {
		this.docNote = docNote;
		return this;
	}

	public String getDocType1() {
		return docType1;
	}

	public BRelated setDocType1(String docType1) {
		this.docType1 = docType1;
		return this;
	}
	
	public BRelated setDocType1(DocTypeEnum docType1) {
		this.docType1 = docType1.toString();
		return this;
	}

	public String getDocType2() {
		return docType2;
	}

	public BRelated setDocType2(String docType2) {
		this.docType2 = docType2;
		return this;
	}
	
	public BRelated setDocType2(DocTypeEnum docType2) {
		this.docType2 = docType2.toString();
		return this;
	}

	public String getMainId1() {
		return mainId1;
	}

	public BRelated setMainId1(String mainId1) {
		this.mainId1 = mainId1;
		return this;
	}

	public String getMainId2() {
		return mainId2;
	}

	public BRelated setMainId2(String mainId2) {
		this.mainId2 = mainId2;
		return this;
	}

	public String getRelatedflag() {
		return this.relatedflag;
	}

	public BRelated setRelatedflag(String relatedflag) {
		this.relatedflag = relatedflag;
		return this;
	}

	public String getUpdater() {
		return this.updater;
	}

	public BRelated setUpdater(String updater) {
		this.updater = updater;
		return this;
	}

	public Timestamp getUpdateTime() {
		return updateTime;
	}

	public BRelated setUpdateTime(Timestamp updateTime) {
		this.updateTime = updateTime;
		return this;
	}
	
	public String getPid1() {
		return pid1;
	}

	public BRelated setPid1(String pid1) {
		this.pid1 = pid1;
		return this;
	}

	public String getTab() {
		return tab;
	}

	public BRelated setTab(String tab) {
		this.tab = tab;
		return this;
	}

	public String getSubtab() {
		return subtab;
	}

	public BRelated setSubtab(String subtab) {
		this.subtab = subtab;
		return this;
	}
	
	public String getFlag() {
		return flag;
	}

	public BRelated setFlag(String flag) {
		this.flag = flag;
		return this;
	}





	/**
	 * <pre>
	 * 引用文件 - 類型
	 * </pre>
	 */
	public enum DocTypeEnum {
		洽談記錄表("CES110"),
		企業應索取資料("CES112"),
		資信簡表("CES120"),
		徵信報告("CES140"),
		一般財報("FSS101"),
		預估財報("FSS102"),
		信用評等("MOW131");
		
		private String code;

		DocTypeEnum(String code) {
			this.code = code;
		}

		public String toString() {
			return code;
		}

		public boolean isEquals(Object other) {
			if (other instanceof String) {
				return code.equals(other);
			} else {
				return super.equals(other);
			}
		}

		public static DocTypeEnum getEnum(String code) {
			for (DocTypeEnum enums : DocTypeEnum.values()) {
				if (enums.toString().equals(code)) {
					return enums;
				}
			}
			return null;
		}
	}

}
