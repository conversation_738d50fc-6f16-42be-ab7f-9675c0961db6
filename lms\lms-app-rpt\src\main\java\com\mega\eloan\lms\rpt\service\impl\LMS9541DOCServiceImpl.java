/* 
 * LMS9025DOServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.model.C004S01A;
import com.mega.eloan.lms.rpt.pages.LMS9541V01Page;
import com.mega.eloan.lms.rpt.pages.LMS9541V02Page;
import com.mega.eloan.lms.rpt.service.LMS9541V02Service;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 產Word Service
 * </pre>
 * 
 * @since 2012/11/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/15,Vector
 *          </ul>
 */
@Service("lms9541docservice")
public class LMS9541DOCServiceImpl extends AbstractFormHandler implements
		FileDownloadService {
	
	@Resource
	LMS9541V02Service lms9541v02Service;
	
	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;

	final String WORD_TR_BEGIN = "<w:tr wsp:rsidR=\"00440EF2\">";

	final String WORD_TR_END = "</w:tr>";

	final String WORD_BEGIN = "<w:tc><w:tcPr><w:tcW w:w=\"1980\" w:type=\"dxa\"/></w:tcPr><w:p wsp:rsidR=\"00440EF2\" wsp:rsidRDefault=\"00440EF2\" wsp:rsidP=\"004B7D68\"><w:pPr><w:jc w:val=\"center\"/>	</w:pPr><w:r><w:rPr><w:rFonts w:ascii=\"細明體\" w:fareast=\"細明體\" w:hint=\"fareast\"/><wx:font wx:val=\"細明體\"/><w:color w:val=\"000000\"/><w:kern w:val=\"0\"/></w:rPr><w:t>";

	final String WORD_END = "</w:t></w:r></w:p></w:tc>";

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS9541DOCServiceImpl.class);
	// SHIFT+ENTER效果
	// private final static String 換行符號 = "<w:br/>";
	private static final String CODE_UTF_8 = "UTF-8";
	
	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(
	 */
	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		OutputStream outputStream = null;
		ByteArrayOutputStream baos = null;
		try {
			outputStream = this.createDoc(params);
			if (outputStream != null) {
				baos = (ByteArrayOutputStream) outputStream;
			}
			if (baos == null) {
				baos = new ByteArrayOutputStream();
			}
			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}

		}
		return null;
	}
	
	/**
	 * 產生word
	 * 
	 * @param params
	 *            PageParameters
	 * @return word
	 */
	public OutputStream createDoc(PageParameters params) {
		OutputStream outputStream = null;
		try {
			int type = params.getInt("type");
			switch (type) {
			case 1:
				outputStream = this.getByCityDoc(params);
				break;
			case 2:
				outputStream = this.getByBrnoDoc(params);
				break;
			case 3:
				outputStream = this.getAllNumDoc(params);
			}

		} catch (Exception e) {
			LOGGER.error("Exception!!", e);
		}
		return outputStream;
	}

	/**
	 * 取得
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	public OutputStream getByCityDoc(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
//		String mainId = params.getString(EloanConstants.MAIN_ID);
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		String rptType = params.getString("rptType");
		List<C004S01A> qData = null;
		Properties prop = null;
		try {
			prop = MessageBundleScriptCreator
					.getComponentResource(LMS9541V01Page.class);
			// 讀檔

			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/fms/LMS9541W0"
					+ rptType.charAt(1) + ".xml");
			// 撈資料
			//qData = lms9025Service.getQ(mainId);

			if (qData == null)
				qData = new ArrayList<C004S01A>();
			if (rptType.equals("Q1") || rptType.equals("Q2"))
				this.setQ1Q2Data(map, qData, prop, params);
			else if (rptType.equals("Q3"))
				this.setQ3Data(map, qData, prop, params);
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			LOGGER.error("FileNotFoundException!!", e);
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			LOGGER.error("IOException!!", e);
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			LOGGER.error("Exception!!", e);
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得 lms9025m06收據
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	public OutputStream getByBrnoDoc(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// String mainId = params.getString(EloanConstants.MAIN_ID);
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		List<C004S01A> qData = null;

		Properties prop = null;
		try {
			prop = MessageBundleScriptCreator
					.getComponentResource(LMS9541V02Page.class);
			// 取得XML範本檔案名稱
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir")) + "word/fms/LMS9025W06.xml");
			// 撈資料
			// qData = lms9025m06Service.getMisData(beginDate, endDate, rptType)

			if (qData == null)
				qData = new ArrayList<C004S01A>();

			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			LOGGER.error("FileNotFoundException!!", e);
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			LOGGER.error("IOException!!", e);
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			LOGGER.error("Exception!!", e);
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}
	/**
	 * 取得 lms9025m06收據
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	public OutputStream getAllNumDoc(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// String mainId = params.getString(EloanConstants.MAIN_ID);
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		List<C004S01A> qData = null;

		Properties prop = null;
		try {
			prop = MessageBundleScriptCreator
					.getComponentResource(LMS9541V02Page.class);
			// 取得XML範本檔案名稱
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir")) + "word/fms/LMS9025W06.xml");
			// 撈資料
			// qData = lms9025m06Service.getMisData(beginDate, endDate, rptType)

			if (qData == null)
				qData = new ArrayList<C004S01A>();

			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			LOGGER.error("FileNotFoundException!!", e);
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			LOGGER.error("IOException!!", e);
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			LOGGER.error("Exception!!", e);
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}
	

	/**
	 * 寫入word資料
	 * 
	 * @param content
	 *            xml 內容
	 * @param map
	 *            對應欄位值
	 * @return ByteArrayOutputStream
	 * @throws CapMessageException
	 */
	private ByteArrayOutputStream writeWordContent(String content,
			Map<String, String> map) throws CapMessageException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		content = Util.replaceWordContent(content, map);
		OutputStreamWriter outWriter = null;
		try {
			outWriter = new OutputStreamWriter(baos, CODE_UTF_8);
			outWriter.write(content);
			outWriter.close();

			return baos;
		} catch (UnsupportedEncodingException e) {
			LOGGER.error("[getContent] Exception!!", e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException i) {
			LOGGER.error("[getContent] Exception!!", i.getMessage());
			throw new CapMessageException(getMessage(i.getMessage()),
					getClass());
		}

	}

	/**
	 * 設定 企金約據書連保人(保證人)檔
	 * 
	 * @param map
	 *            共用map
	 * @param c004s01aList
	 *            企金約據書連保人(保證人)檔
	 * @param prop
	 *            語系檔
	 * @return 要置換的key Map
	 */
	private Map<String, String> setQ1Q2Data(Map<String, String> map,
			List<C004S01A> c004s01aList, Properties prop, PageParameters params) {
		map.put("C004S01A.rptDate",
				Util.nullToSpace(params.getString("rptDate")));
		map.put("C004S01A.today", Util.getDate(new Date()));

		StringBuffer str = new StringBuffer();
		// Q1Q2唯一不同:文件上Q1單位是千元
		BigDecimal div = null;
		if ("Q1".equals(params.get("rptType")))
			div = new BigDecimal(1000);
		else
			div = new BigDecimal(1);

		for (int i = 0; i < c004s01aList.size(); i++) {
			C004S01A pivot = c004s01aList.get(i);
			str.append(WORD_TR_BEGIN + WORD_BEGIN).append(pivot.getBrno())
					.append(WORD_END + WORD_BEGIN).append(pivot.getNum())
					.append(WORD_END + WORD_BEGIN)
					.append(pivot.getAmt().divide(div))
					.append(WORD_END + WORD_TR_END);
		}
		map.put("C004S01A.table", str.toString());
		return map;
	}

	/**
	 * 設定 企金約據書連保人(保證人)檔
	 * 
	 * @param map
	 *            共用map
	 * @param c004s01aList
	 *            企金約據書連保人(保證人)檔
	 * @param prop
	 *            語系檔
	 * @return 要置換的key Map
	 */
	private Map<String, String> setQ3Data(Map<String, String> map,
			List<C004S01A> c004s01aList, Properties prop, PageParameters params) {
		map.put("C004S01A.rptDate",
				Util.nullToSpace(params.getString("rptDate")));
		map.put("C004S01A.today", Util.getDate(new Date()));

		StringBuffer str = new StringBuffer();

		for (int i = 0; i < c004s01aList.size(); i++) {
			C004S01A pivot = c004s01aList.get(i);
			str.append(WORD_TR_BEGIN + WORD_BEGIN).append(pivot.getBrno())
					.append(WORD_END + WORD_BEGIN).append(pivot.getNum())
					.append(WORD_END + WORD_BEGIN).append(pivot.getAmt())
					.append(WORD_END + WORD_TR_END);
		}
		map.put("C004S01A.table", str.toString());
		return map;
	}
}