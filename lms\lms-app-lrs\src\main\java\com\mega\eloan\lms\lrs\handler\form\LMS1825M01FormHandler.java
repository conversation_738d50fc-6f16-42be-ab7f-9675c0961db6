/* 
 * LMS1825M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.handler.form;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.ELRoleEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocOpener;
import com.mega.eloan.common.model.DocOpener.OpenTypeCode;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.lrs.pages.LMS1825M01Page;
import com.mega.eloan.lms.lrs.service.LMS1825Service;
import com.mega.eloan.lms.model.L182A01A;
import com.mega.eloan.lms.model.L182M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 預約產生覆審名單
 * </pre>
 * 
 * @since 2011/9/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/19,irene,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1825formhandler")
@DomainClass(L182M01A.class)
public class LMS1825M01FormHandler extends AbstractFormHandler {
	private static final DateFormat S_FORMAT = new SimpleDateFormat(UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
	
	@Resource
	LMS1825Service service;

	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branch;
	
	@Resource
	RetrialService retrialService;
	
	@Resource
	DocCheckService docCheckService;

	Properties lms1825m01 = MessageBundleScriptCreator
			.getComponentResource(LMS1825M01Page.class);

	/**
	 * 查詢
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		
		CapAjaxFormResult L182M01AForm = new CapAjaxFormResult();
		String str_ts = "";
		if (!CapString.isEmpty(oid)) {
			L182M01A l182m01a = service.findModelByOid(L182M01A.class, oid);
			if (l182m01a != null) {
				if (!Util.isEmpty(l182m01a.getDocStatus())) {
					l182m01a.setDocStatus(lms1825m01.getProperty("doc.status"
							+ l182m01a.getDocStatus()));
				}
				
				L182M01AForm = DataParse.toResult(l182m01a);
				result.set("branchList", l182m01a.getBranchList());
				if (l182m01a.getBaseDate() != null) {
					result.set("baseDate2", TWNDate
							.toAD(l182m01a.getBaseDate()).substring(0, 7));
				}
				if (!Util.isEmpty(l182m01a.getUpdater())) {
					String updaterC = userInfoService.getUserName(l182m01a.getUpdater());
					L182M01AForm.set("updater", Util.isEmpty(updaterC) ? l182m01a.getUpdater() : updaterC);
				}
				if (!Util.isEmpty(l182m01a.getCreator())) {
					L182M01AForm.set("creator", userInfoService.getUserName(l182m01a
							.getCreator()));
				}
				
				if(!Util.isEmpty(l182m01a.getGenDate())){
					str_ts = S_FORMAT.format(l182m01a.getGenDate());	
				}
			}
		}else{
			int delayMinutes = 10;
			Timestamp defTS = CapDate.getCurrentTimestamp();			
			defTS.setTime(defTS.getTime() + delayMinutes*60*1000);
			str_ts = S_FORMAT.format(defTS);
		}
		L182M01AForm.set("genDate1", StringUtils.substring(str_ts, 0, 10));
		L182M01AForm.set("meetingTime", StringUtils.substring(str_ts, 11, 16));
		result.set("L182M01AForm", L182M01AForm);
		result.set(EloanConstants.OID, CapString.trimNull(oid));
		return result;
	}

	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));
		L182M01A l182m01a = service.findModelByOid(L182M01A.class, oid);
		if (l182m01a == null) {
			l182m01a = new L182M01A();
			l182m01a.setMainId(IDGenerator.getUUID());
			l182m01a.setOwnBrId(user.getUnitNo());
			l182m01a.setTypCd(retrialService.overSeaProgram()?TypCdEnum.海外.getCode():TypCdEnum.DBU.getCode());
			l182m01a.setUnitType(user.getUnitType());

			L182A01A l182a01a = new L182A01A();
			l182a01a.setAuthType("1");
			l182a01a.setAuthTime(CapDate.getCurrentTimestamp());
			l182a01a.setAuthUnit(user.getUnitNo());
			l182a01a.setOwnUnit(user.getUnitNo());
			l182a01a.setMainId(l182m01a.getMainId());
			l182a01a.setOwner(user.getUserId());
			l182a01a.setOwnUnit(user.getUnitNo());

			service.save(l182a01a);
		}
		String formL181m01a = params.getString("L182M01AForm");
		JSONObject jsonL181m01a = JSONObject.fromObject(formL181m01a);
		DataParse.toBean(jsonL181m01a, l182m01a);
		String genDate = jsonL181m01a.getString("genDate1");
		if(Util.isNotEmpty(jsonL181m01a.getString("meetingTime"))){
			String meetingTime = jsonL181m01a.getString("meetingTime")+":00";
			jsonL181m01a.put("genDate", genDate+" "+meetingTime);
			l182m01a.setGenDate(Timestamp.valueOf(genDate+" "+meetingTime));
		}

		String[] list = params.getStringArray("branchList");
		StringBuilder branchlist = new StringBuilder();
		for (String branch : list) {
			branchlist.append(branch + ",");
		}
		l182m01a.setBranchList(branchlist.toString());

		if (!Util.isEmpty(l182m01a.getGenDate())
				&& l182m01a.getGenDate()
						.compareTo(
								CapDate.parseDate(CapDate
										.getCurrentDate("yyyy-MM-dd"))) < 0) {
			throw new CapMessageException(lms1825m01.getProperty("afterToday"),
					getClass());
		}

		String baseDate = params.getString("baseDate") + "-01";
		if (params.getString("baseDate") == null
				|| params.getString("baseDate").isEmpty()) {
			l182m01a.setBaseDate(null);
		} else {
			try {
				l182m01a.setBaseDate(Util.parseDate(baseDate));
			} catch (RuntimeException e) {
				logger.error("LMS1825M01FormHandler saveMain EXCEPTION!!", e);
				throw new CapMessageException(
						lms1825m01.getProperty("dataDateFormat"), getClass());
			}
		}

		service.save(l182m01a);

		if (!Util.isEmpty(l182m01a.getDocStatus())) {
			l182m01a.setDocStatus(lms1825m01.getProperty("doc.status"
					+ l182m01a.getDocStatus()));
		}
		result.set("L182M01AForm", DataParse.toResult(l182m01a));
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));

		result.set("branchList", branchlist.toString());
		return result;
	}

	/**
	 * 搜尋分行
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult queryBranch(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		TreeMap<String, String> tm = retrialService.getBranch(user.getUnitNo());
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("item", new CapAjaxFormResult(tm));
		result.set("itemOrder", new ArrayList<String>(tm.keySet()));
		return result;
	}

	/**
	 * 上刪除註記
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteMark(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));
		if (isAuthForDel(oid)) {
			//檢查文件是否被編輯
			boolean edit = false;
			L182M01A l182m01a = service.findModelByOid(L182M01A.class, oid);
			List<DocOpener> docOpeners = docCheckService.findByMainId(l182m01a.getMainId());
			for(DocOpener docOpener : docOpeners){
				if(OpenTypeCode.Writing.getCode().equals(docOpener.getOpenType())){
					HashMap<String, String> hm = new HashMap<String, String>();
					hm.put("userId", docOpener.getOpener());
					hm.put("userName",
							userInfoService.getUserName(docOpener.getOpener()));
					edit = true;
					result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMessage("EFD0009", hm));
					break;
				}
			}
			if(!edit){
				l182m01a.setDeletedTime(CapDate.getCurrentTimestamp());
				service.save(l182m01a);
				// EFD0019=刪除成功
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0019"));
			}
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0058"));
		}
		
		return result;
	}
	
	/**
	 * <pre>
	 * 是否有刪除權限:
	 * 	主管可刪除任何文件,經辦僅能刪除自己的文件(updater)
	 * </pre>
	 * 
	 * @param oid
	 *            文件OID
	 * @return
	 */
	private boolean isAuthForDel(String oid) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 主管
		if (user.getRoles().containsKey(ELRoleEnum.主管.getCode())) {
			return true;
		} else {
			// 非主管
			L182M01A l182m01a = service.findModelByOid(L182M01A.class, oid);
			if (user.getUserId().equals(Util.trimSpace(l182m01a.getUpdater()))) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 刪除
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult deleteList(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String list = params.getString("list");

		service.deleteMain(list);

		// EFD0019=刪除成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0019"));
		return result;
	}
}
