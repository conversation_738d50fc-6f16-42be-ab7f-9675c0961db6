var Action = {
    baseUrl: "../lms/lms1505m01/02",
    //1.海外 2.企金 3.個金
    createType: "3",
    i18nkey: i18n.cls1401v00
};
$(function(){

    var grid = $("#gridview").iGrid({
        handler: 'lms1505gridhandler',
        height: 347,
        sortname: 'meetingDate|oid',
        sortorder: 'desc|desc',
        postData: {
            formAction: "query",
            createType: Action.createType
        },
        rowNum: 15,
        colModel: [{
            colHeader: i18n.cls1401v00['L150M01a.meetingDate'],//"會議日期",
            name: 'meetingDate',
            align: "center",
            width: 70,
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.cls1401v00['L150M01a.gist'],//"案由",
            name: 'gist',
            width: 100,
            sortable: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        $.form.submit({
            url: '..' + rowObject.docURL + '/02',//../lms/lms1505m01/02
            data: {
                formAction: "query",
                mainOid: rowObject.oid,
                mainId: rowObject.mainId,
                mainDocStatus: viewstatus,
                txCode: txCode
            
            },
            target: rowObject.oid
        });
    };
    
    
    $("#buttonPanel").find("#btnDelete").click(function(){
        var $gridview = $("#gridview");
        var ids = $gridview.getGridParam('selrow');
        
        if (!ids) {//TMMDeleteError=請先選擇需修改(刪除)之資料列
            return API.showMessage(i18n.def["TMMDeleteError"]);
        }
        var oids = [];
		if (ids) {
			var rowData = $gridview.getRowData(ids);
				oids.push(rowData.oid);
		}
        //confirmDelete=是否確定刪除?
        API.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "lms1505m01formhandler",
                    data: {
                        formAction: "deleteList",
                        oids: oids
                    }
                }).done(function(obj){
					   $("#gridview").trigger("reloadGrid");		
				});
            }
        });
        
    }).end().find("#btnAdd").click(function(){
        $.form.submit({
            url: Action.baseUrl,//'../lms/lms1505m01/02',
            data: {
                mainDocStatus: viewstatus,
                createType: Action.createType
            },
            target: "_blank"
        });
    }).end().find("#btnModify").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {//TMMDeleteError=請先選擇需修改(刪除)之資料列
            return API.showMessage(i18n.def["TMMDeleteError"]);
        }
        var result = $("#gridview").getRowData(id);
        openDoc(null, null, result);
    });
});
