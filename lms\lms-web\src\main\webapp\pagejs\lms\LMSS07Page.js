//var responseJSON["handler"] = "";
initDfd.done(function() {
	setCloseConfirm(true);
	var pageUrl = "";
	
	//登錄事件
	$("#lmss07a").click(function(){
		pageUrl = "../../lms/lmss07A";
		loadHtml(pageUrl, "7a");
		
		//J-104-0138-001 隱藏引進社會與環境風險(赤道原則)評估
		if(responseJSON.docType == "2"){
		    $("#btnApplyEquatorPrinciples").hide();
		}else{
			$("#btnApplyEquatorPrinciples").show();
		}
	});	
	$("#lmss07b").click(function(){
		pageUrl = "../../lms/lmss07B";
		loadHtml(pageUrl, "7b");
	});
	$("#lmss07c").click(function(){
		pageUrl = "../../lms/lmss07C";
		loadHtml(pageUrl, "7c");
	});
	$("#lmss07d").click(function(){
		pageUrl = "../../lms/lmss07D";
		loadHtml(pageUrl, "7d");
	});		
	//J-104-0183	
	$("#lmss07f").click(function(){
		pageUrl = "../../lms/lmss07F";
		loadHtml(pageUrl, "7f");
	});			
	
	
	//預設LOAD HTML
	if (responseJSON.docType == '1') {
	    // 企金
		
		pageUrl = "../../lms/lmss07A";
		loadHtml(pageUrl, "7a");
		
		$("#lmss07f").show();
		pageUrl = "../../lms/lmss07F";	
	    loadHtml(pageUrl, "7f");
	}else{
		// 個金
		$("#lmss07f").hide();
		pageUrl = "../../lms/lmss07A";	
	    loadHtml(pageUrl, "7a");
		$("#lmss07a").trigger('click');
	}	
	
	// J-112-0013 (112) 隱藏企金簽報書之綜合評估/往來彙總下之風險權數之頁籤 
	hidePanelByCallAjax();
});

var haveloadPage = {};

function hidePanelByCallAjax() {
	$.ajax({
		handler : "lms1205formhandler",
		type : "POST",
		dataType : "json",
		action : "hidePanelLmsS07",
		data : {
			mainId : responseJSON.mainId
		},
		success : function(json) {
			// 舊的風險權數頁籤隱藏
			if(json.hide_lmss07b){
				$("#lmss07b").hide();// 隱藏這個id
			}
		}
	});
}

function loadHtml(pageUrl, page){
		if(responseJSON.docURL == "/lms/lms1205m01"){
			// 授權外企金
			responseJSON["handler"] = "lms1205formhandler";
		}else if(responseJSON.docURL == "/lms/lms1105m01"){
			//授權內企金
			responseJSON["handler"] = "lms1105formhandler";
		}else if(responseJSON.docURL == "/lms/lms1215m01"){
			responseJSON["handler"] = "lms1215formhandler";
		}else if(responseJSON.docURL == "/lms/lms1115m01"){
			responseJSON["handler"] = "lms1115formhandler";
		}else{
			responseJSON["handler"] = "lms1305formhandler";
		}
		responseJSON["s07page"]=page;
		
		if(!haveloadPage[page]){
			haveloadPage[page] = true;
			$("#tab-"+page).load(pageUrl, function(){
				//查詢分頁資料
				$.ajax({		
					handler : "lms1205formhandler",
					type : "POST",
					dataType : "json",
					action : "queryL120S07",
					data : {
						mainId : responseJSON.mainId,
						page: page
					},
					success : function(jsonInit) {
						if(page == "7a"){
							setCkeditor2("ffbody",jsonInit.L120M01dForm04.ffbody);
							setCkeditor2("itemDscrT", jsonInit.L120M01dForm04.itemDscrT, '500', '900');
                            setCkeditor2("itemDscrU", jsonInit.L120M01dForm04.itemDscrU, '500', '900');
                            setCkeditor2("itemDscrR", jsonInit.L120M01dForm04.itemDscrR, '500', '100');
                            if(jsonInit.L120M01dForm04.isOut){
                                $(".isOutShow").show();
                            } else {
                                $(".isOutShow").hide();
                            }
							$F = $("#L120M01D_hkmaForm");
							$F.setData(jsonInit.L120M01D_hkmaForm);
							// J
//							$("#L120M01dForm04").find("#cesId_ItemTitle").val(jsonInit.L120M01dForm04.itemTitle);

							//$("#L120M01dForm04").find("#ffbody").val(jsonInit.L120M01dForm04.ffbody);							
						}else if(page == "7c"){
//							$("#LMS1205S07Form03").setData(jsonInit.LMS1205S07Form03,false);
						}else if(page == "7f"){
						    /*
							$("#LMS1205S07Form05").setData(jsonInit.LMS1205S07Form05,false);
							$("#LMS1205S07Form05").find(".hasDeeds").trigger("change");
							//J-106-0213-001 Web e-Loan 授信管理系統新增企業誠信經營評估
							$("#LMS1205S07Form05").find(".hasBadFaith").trigger("change");
							// J-108-0166 社會與環境風險評估改版
							if(jsonInit.LMS1205S07Form05.ver == "01"){
								$("#LMS1205S07Form05").find("#ver1").show();
								$("#LMS1205S07Form05").find("#ver2").hide();
							} else {
								var item1_D1 = jsonInit.LMS1205S07Form05.item1_D1.split("|");
								for (var i = 0; i < item1_D1.length; i++) {
									var val = item1_D1[i];
									$("[name='item1_D1'][value=" + val + "]").attr("checked", true);
								}
								var item1_D2 = jsonInit.LMS1205S07Form05.item1_D2.split("|");
								for (var i = 0; i < item1_D2.length; i++) {
									var val = item1_D2[i];
									$("[name='item1_D2'][value=" + val + "]").attr("checked", true);
								}
								var item1_D3 = jsonInit.LMS1205S07Form05.item1_D3.split("|");
								for (var i = 0; i < item1_D3.length; i++) {
									var val = item1_D3[i];
									$("[name='item1_D3'][value=" + val + "]").attr("checked", true);
								}
								var item2_D1 = jsonInit.LMS1205S07Form05.item2_D1.split("|");
								for (var i = 0; i < item2_D1.length; i++) {
									var val = item2_D1[i];
									$("[name='item2_D1'][value=" + val + "]").attr("checked", true);
								}
								var item2_D2 = jsonInit.LMS1205S07Form05.item2_D2.split("|");
								for (var i = 0; i < item2_D2.length; i++) {
									var val = item2_D2[i];
									$("[name='item2_D2'][value=" + val + "]").attr("checked", true);
								}
								var item2_D3 = jsonInit.LMS1205S07Form05.item2_D3.split("|");
								for (var i = 0; i < item2_D3.length; i++) {
									var val = item2_D3[i];
									$("[name='item2_D3'][value=" + val + "]").attr("checked", true);
								}

								$("#LMS1205S07Form05").find("#itemSpan_item1_D1,#itemSpan_item1_D2,#itemSpan_item1_D3").trigger("change");
								$("#LMS1205S07Form05").find("#itemSpan_item2_D1,#itemSpan_item2_D2,#itemSpan_item2_D3").trigger("change");

								if(jsonInit.LMS1205S07Form05.hasD1 == "Y"){
									$("#LMS1205S07Form05").find("#jsonD1").show();
								} else {
									$("#LMS1205S07Form05").find("#jsonD1").hide();
								}
								if(jsonInit.LMS1205S07Form05.hasD2 == "Y"){
									$("#LMS1205S07Form05").find("#jsonD2").show();
								} else {
									$("#LMS1205S07Form05").find("#jsonD2").hide();
								}
								if(jsonInit.LMS1205S07Form05.hasD3 == "Y"){
									$("#LMS1205S07Form05").find("#jsonD3").show();
								} else {
									$("#LMS1205S07Form05").find("#jsonD3").hide();
								}

//							if(jsonInit.LMS1205S07Form05.ver == "01"){
//								$("#LMS1205S07Form05").find("#ver1").show();
//								$("#LMS1205S07Form05").find("#ver2").hide();
//							} else {
								$("#LMS1205S07Form05").find("#ver2").show();
								$("#LMS1205S07Form05").find("#ver1").hide();
							}
							*/
						}
						
						var $showBorrowData = $("#showBorrowData");
						$showBorrowData.reset();
						$showBorrowData.setData(jsonInit.showBorrowData,false);
					}
				});

				if(responseJSON.readOnly == "true"){
					if(page == "7a"){
						var $L120M01dForm04 = $("#L120M01dForm04");				
						$L120M01dForm04.readOnlyChilds(true);
						$L120M01dForm04.find("button").hide();
					}else if(page == "7b"){
						var $LMS1205S07Form02 = $("#LMS1205S07Form02");
						var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
						$LMS1205S07Form02.readOnlyChilds(true);
						$LMS1205S07Form02.find("button").hide();
						$tLMS1205S07Form02.readOnlyChilds(true);
						$tLMS1205S07Form02.find("button").hide();
					}else if(page == "7c"){
						var $LMS1205S07Form03 = $("#LMS1205S07Form03");
						var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");
						var $tLMS1205S07Form03_btn = $("#tLMS1205S07Form03_btn");
						$LMS1205S07Form03.readOnlyChilds(true);
						$LMS1205S07Form03.find("button").hide();
						$tLMS1205S07Form03.readOnlyChilds(true);
						$tLMS1205S07Form03.find("button").hide();
						$tLMS1205S07Form03_btn.readOnlyChilds(true);
                        $tLMS1205S07Form03_btn.find("button").hide();
					}else if(page == "7d"){
						var $LMS1205S07Form04 = $("#LMS1205S07Form04");
						var $tLMS1205S07Form04 = $("#tLMS1205S07Form04");
						$LMS1205S07Form04.readOnlyChilds(true);
						$LMS1205S07Form04.find("button").hide();
						$tLMS1205S07Form04.readOnlyChilds(true);
						$tLMS1205S07Form04.find("button").hide();
					}else if (page == "7f") {
						var $LMS1205S07Form05 = $("#LMS1205S07Form05");
						var $tLMS1205S07Form05 = $("#tLMS1205S07Form05");
						var $tLMS1205S07Form05_btn = $("#tLMS1205S07Form05_btn");
						$LMS1205S07Form05.readOnlyChilds(true);
						$LMS1205S07Form05.find("button").hide();
						$tLMS1205S07Form05.readOnlyChilds(true);
						$tLMS1205S07Form05.find("button").hide();
						$tLMS1205S07Form05_btn.readOnlyChilds(true);
                        $tLMS1205S07Form05_btn.find("button").hide();
					}								
				}
				// 控制ThickBox唯讀狀態
				thickReadOnly();			
			});
		}	
}

function gridSelectThick(list) {
	$.ajax({
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		data : 
		{
			formAction : "findFfbody",
		mainId : responseJSON.mainId,
		cesMainId : list
		},
		success : function(json) {
		setCkeditor2("ffbody",json.ffbody);
		}
	});	
/*
	$("#gridSelectThick").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss07["L1205S07.thickbox16"],
		width : 640,
		height : 350,
		modal : true,
		align : 'center',
		valign: 'bottom',
		i18n:i18n.lmss07,
		buttons : {
			"L1205S07.thickbox1" : function() {
					var row = $("#gridSelectMainId").getGridParam('selrow'); 
					var list = "";
					var data = $("#gridSelectMainId").getRowData(row);
					list = data.mainId;
					list = (list == undefined ? "" : list);
					if(list != ""){
				  		$.ajax({
				  			handler : responseJSON["handler"],
				  			type : "POST",
				  			dataType : "json",
				  			data : 
				  			{
				  				formAction : "findFfbody",
								mainId : responseJSON.mainId,
								cesMainId : list
				  			},
				  			success : function(json) {
								setCkeditor2("ffbody",json.ffbody);
				  			}
				  		});				
						$.thickbox.close();	
					}
			},
			"L1205S07.thickbox2" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
*/
}

function getLocalData(){
	var $tLMS1205S07Form04 = $("#tLMS1205S07Form04");
	var oid = $tLMS1205S07Form04.find("#oldOid").val();
	$.ajax({
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "queryL120s06bl",
		data : {
			oid : oid,
			typCd2 : $tLMS1205S07Form04.find("#typCd2").val(),
			custId2 : $tLMS1205S07Form04.find("#custId2").val(),
			dupNo2 : $tLMS1205S07Form04.find("#dupNo2").val(),
			custName2 : $tLMS1205S07Form04.find("#custName2").val(),
			requery: true,
			payDeadline2: $tLMS1205S07Form04.find("#payDeadline2").val(),
			guarantor2: $tLMS1205S07Form04.find("#guarantor2").val(),
			purpose2: $tLMS1205S07Form04.find("#purpose2").val(),
			lnSubject2: $tLMS1205S07Form04.find("#lnSubject2").val(),
			cntrNo2: $tLMS1205S07Form04.find("#cntrNo2").val(),
			property2: $tLMS1205S07Form04.find("#property2").val(),
			currentApplyCurr2: $tLMS1205S07Form04.find("#currentApplyCurr2").val(),
			currentApplyAmt2: $tLMS1205S07Form04.find("#currentApplyAmt2").val(),
			gutPercent2: $tLMS1205S07Form04.find("#gutPercent2").val(),
			guarantorMemo2: $tLMS1205S07Form04.find("#guarantorMemo2").val()
		},
		success : function(obj) {
			var $tLMS1205S07Form04 = $("#tLMS1205S07Form04");
			$tLMS1205S07Form04.setData(obj.tLMS1205S07Form04,false);
		}
	});	
}

function inputSearch() {
	var nowDate = new Date();
	var MM = nowDate.getMonth();
	var YY = nowDate.getFullYear();
	var SMM;
	var SYY;
	if(MM == 0){
		MM = 12;
	}
	
	if(MM ==12 ){
		SMM = MM - 5;
		YY = YY -1 ;
		SYY = YY; 
	}else if(MM > 5  && MM < 12  ){
		SMM = MM - 5;
		SYY = YY;
	}else{
		SMM = MM + 12 - 5;
		SYY = YY-1; 
	}
	
	var $tLMS1205S07Form03b = $("#tLMS1205S07Form03b");
	$tLMS1205S07Form03b.find("#queryDateS0").val(SYY);
	$tLMS1205S07Form03b.find("#queryDateS1").val(SMM);
	$tLMS1205S07Form03b.find("#queryDateE0").val(YY);
	$tLMS1205S07Form03b.find("#queryDateE1").val(MM);	
	$("#inputSearch").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss07["L1205S07.thickbox8"],
		width : 450,
		height : 210,
		modal : true,
		align : 'center',
		valign: 'bottom',
		i18n:i18n.lmss07,
		buttons : {
			"L1205S07.thickbox1" : function() {
				var $tLMS1205S07Form03b = $("#tLMS1205S07Form03b");
				var $LMS1205S07Form03 = $("#LMS1205S07Form03");				
				if($tLMS1205S07Form03b.valid()){
					if($tLMS1205S07Form03b.find("#queryDateS1").val()< 1
					|| $tLMS1205S07Form03b.find("#queryDateS1").val()> 12
					|| $tLMS1205S07Form03b.find("#queryDateE1").val()< 1
					|| $tLMS1205S07Form03b.find("#queryDateE1").val()> 12){
						CommonAPI.showMessage(i18n.lmss07["l120v01.error3"]);
						return;
					}else if($tLMS1205S07Form03b.find("#queryDateS0").val()<=0
						   ||$tLMS1205S07Form03b.find("#queryDateE0").val()<=0){
						CommonAPI.showMessage(i18n.lmss07["l120v01.error8"]);
						return;
					}else if($tLMS1205S07Form03b.find("#queryDateE0").val()-
						     $tLMS1205S07Form03b.find("#queryDateS0").val()<0){
						CommonAPI.showMessage(i18n.lmss07["l120v01.error9"]);
						return;
					}else if(($tLMS1205S07Form03b.find("#queryDateE0").val()-
						      $tLMS1205S07Form03b.find("#queryDateS0").val()==0) &&
							 ($tLMS1205S07Form03b.find("#queryDateE1").val()-
						      $tLMS1205S07Form03b.find("#queryDateS1").val()<0)
							 ){
						CommonAPI.showMessage(i18n.lmss07["l120v01.error9"]);
						return;		
					}else{
						$.thickbox.close();
				  		$.ajax({
				  			handler : responseJSON["handler"],
				  			type : "POST",
				  			dataType : "json",
				  			data : 
				  			{
				  				formAction : "saveL120s04a2",
								LMS1205S07Form03 : JSON.stringify($LMS1205S07Form03.serializeData()),
				  				mainId : responseJSON.mainid,
				  				queryDateS0 : $tLMS1205S07Form03b.find("#queryDateS0").val(),
				  				queryDateS1 : $tLMS1205S07Form03b.find("#queryDateS1").val(),
				  				queryDateE0 : $tLMS1205S07Form03b.find("#queryDateE0").val(),
				  				queryDateE1 : $tLMS1205S07Form03b.find("#queryDateE1").val(),
                                oidL120s04d : $("#oidL120s04d").val()
				  			},
				  			success : function(json) {
								var $LMS1205S07Form03 = $("#LMS1205S07Form03");
				  				$LMS1205S07Form03.setData(json.LMS1205S07Form03);
				  				$LMS1205S07Form03.find("#gridview_A-1-8-1").trigger("reloadGrid");
				  			}
				  		});										
					}
				}			
			},
			// "刪除本頁": function() {alert("刪除本頁");},
			"L1205S07.thickbox2" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}
function setTotal(){
	var count=$("#gridview_A-1-8-1").jqGrid('getGridParam','records');
	if(count == 0){
		CommonAPI.showMessage(i18n.lmss07["L1205S07.error14"]);
		return;
	}	
	var $LMS1205S07Form03 = $("#LMS1205S07Form03");
	$.ajax({
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "saveTotal",
		data : {
			mainId : responseJSON.mainid,
			LMS1205S07Form03 : JSON.stringify($LMS1205S07Form03.serializeData()),
			queryDateS : $LMS1205S07Form03.find("#queryDateS").html(),
			queryDateE : $LMS1205S07Form03.find("#queryDateE").html(),
            oidL120s04d: $("#oidL120s04d").val()
		},
		success : function(json) {
			$LMS1205S07Form03.setData(json.LMS1205S07Form03);
			$LMS1205S07Form03.find("#gridview_A-1-8-1").trigger("reloadGrid");
		}
	});	
}

/**
 * 查詢並開啟往來實績彙總表ThickBox
 */
/**
 * 查詢並開啟往來實績彙總表ThickBox
 */
function tL120s04b(cellvalue, options, rowObject){
    var docKind = rowObject.docKind;
	if(docKind=="A"){
		tL120s04b_A(cellvalue, options, rowObject);
		
	}else{
		tL120s04b_0(cellvalue, options, rowObject);
	}
}	
function tL120s04b_0(cellvalue, options, rowObject){
	var oid = rowObject.oid;
	// 進行查詢 
	$.ajax({		//查詢主要借款人資料
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "queryL120s04b",
		data : {
			oid : oid,
			mainId : responseJSON.mainId,
			oidL120s04d: $("#oidL120s04d").val()
		},
		success : function(json) {
			var $formL120s04b = $("#formL120s04b");
			
		
			/*
			var grpGrrd = $formL120s04b.find("#grpGrrd option:selected").val();
			// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat 2012/11/27
			if(grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3" || grpGrrd == "4" || grpGrrd == "5"){
				// 顯示屬主要集團企業...
				$formL120s04b.find(".spectialHide").show();
			}else{
				// 隱藏屬主要集團企業...
				$formL120s04b.find(".spectialHide").hide();
			}
			*/
			
			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			var grpYear = json.formL120s04b.grpYear;
			var grpGrrd = json.formL120s04b.grpGrrd;
			if (grpYear) {
				// 判斷2017以後為新版，之前為舊版
				if (parseInt(grpYear, 10) >= 2017) {

					var obj = CommonAPI
							.loadCombos([ "GroupGrade2017" ]);

					// 評等等級
					$("#grpGrrd").setItems({
						item : obj.GroupGrade2017,
						format : "{key}"
					});

					// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller
					// eddedat 2012/11/27
					if (grpGrrd == "1" || grpGrrd == "2"
							|| grpGrrd == "3" || grpGrrd == "4"
							|| grpGrrd == "5" || grpGrrd == "6"
							|| grpGrrd == "7") {
						// 顯示屬主要集團企業...
						$formL120s04b.find(".spectialHide").show();
					} else {
						// 隱藏屬主要集團企業...
						$formL120s04b.find(".spectialHide").hide();
					}
				} else {

					var obj = CommonAPI.loadCombos([ "GroupGrade" ]);

					// 評等等級
					$("#grpGrrd").setItems({
						item : obj.GroupGrade,
						format : "{key}"
					});

					// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller
					// eddedat 2012/11/27
					if (grpGrrd == "1" || grpGrrd == "2"
							|| grpGrrd == "3" || grpGrrd == "4"
							|| grpGrrd == "5") {
						// 顯示屬主要集團企業...
						$formL120s04b.find(".spectialHide").show();
					} else {
						// 隱藏屬主要集團企業...
						$formL120s04b.find(".spectialHide").hide();
					}
				}

			} else {

				var obj = CommonAPI.loadCombos([ "GroupGrade" ]);

				// 評等等級
				$("#grpGrrd").setItems({
					item : obj.GroupGrade,
					format : "{key}"
				});

				// 如果沒有評等年度，以舊版執行
				// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat
				// 2012/11/27
				if (grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3"
						|| grpGrrd == "4" || grpGrrd == "5") {
					// 顯示屬主要集團企業...
					$formL120s04b.find(".spectialHide").show();
				} else {
					// 隱藏屬主要集團企業...
					$formL120s04b.find(".spectialHide").hide();
				}
			}
			
            $formL120s04b.setData(json.formL120s04b);
            // J-111-0052 修改借戶暨關係戶與本行往來實績彙總表
            var showHins = json.formL120s04b.showHins;
            if(showHins == "Y"){
                $formL120s04b.find("#hinsDiv").show();
            } else {
                $formL120s04b.find("#hinsDiv").hide();
            }
			var grpNo = DOMPurify.sanitize($formL120s04b.find("#grpNo").html());
			
				$("#tL120s04b").thickbox({ // 使用選取的內容進行彈窗
					title : i18n.lmss07["L1205S07.grid43"],
					width : 965,
					height : 480,
					modal : true,
					i18n:i18n.def,
					buttons : {
						"saveData" : function() {
							$("#tL120s04bDate").thickbox({
								title : "",
								width : 800,
								height : 200,
								modal : false,
								i18n:i18n.def,
								buttons : {
									"sure" : function(){
										if(!$("#tL120s04bDateTmpForm").find("#_docDateYear,#_docDateMonth").valid()){
											return false;
										}
										var beginMonth = 1;
										var endMonth = parseInt($("#_docDateMonth").val(), 10);
										var year = parseInt($("#_docDateYear").val(), 10);
										$("#docDate3,#docDate6").val(year+"/"+beginMonth+"~"+endMonth+"月");										
										$.thickbox.close();
										if($formL120s04b.valid()){
											$formL120s04b.find(".numeric").each(function(i){
												$(this).val(RemoveStringComma($(this).val()));
											});
											// 進行報酬率計算
											for(var i=1; i<=6; i++){
												// D利潤貢獻(TWD)
												
												//var profitAmt = ($formL120s04b.find("#profitAmt" + i).val() == undefined || 
												//$formL120s04b.find("#profitAmt" + i).val() == null || 
												//$formL120s04b.find("#profitAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#profitAmt" + i).val(),10);
												var profitAmt = 0;									
												if($formL120s04b.find("#profitAmt" + i).val() == undefined || 
												$formL120s04b.find("#profitAmt" + i).val() == null || 
												$formL120s04b.find("#profitAmt" + i).val() == ""){
													profitAmt = 0;
													$formL120s04b.find("#profitAmt" + i).val(profitAmt);
												}else {
													profitAmt = parseInt($formL120s04b.find("#profitAmt" + i).val(),10);
												}								
																								
												// A平均授信(TWD)
												//var avgLoanAmt = ($formL120s04b.find("#avgLoanAmt" + i).val() == undefined || 
												//$formL120s04b.find("#avgLoanAmt" + i).val() == null || 
												//$formL120s04b.find("#avgLoanAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#avgLoanAmt" + i).val(),10);
												
												var avgLoanAmt = 0;
												if($formL120s04b.find("#avgLoanAmt" + i).val() == undefined || 
												$formL120s04b.find("#avgLoanAmt" + i).val() == null || 
												$formL120s04b.find("#avgLoanAmt" + i).val() == ""){
													avgLoanAmt = 0;
													$formL120s04b.find("#avgLoanAmt" + i).val(avgLoanAmt);
												} else {
													avgLoanAmt = parseInt($formL120s04b.find("#avgLoanAmt" + i).val(),10);
												}
												
												
												// B應收帳款無追索買方承購平均餘額(TWD)
												//var rcvBuyAvgAmt = ($formL120s04b.find("#rcvBuyAvgAmt" + i).val() == undefined || 
												//$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == null || 
												//$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#rcvBuyAvgAmt" + i).val(),10);
												
												var rcvBuyAvgAmt = 0;
												if($formL120s04b.find("#rcvBuyAvgAmt" + i).val() == undefined || 
												$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == null || 
												$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == ""){
													rcvBuyAvgAmt = 0;
													$formL120s04b.find("#rcvBuyAvgAmt" + i).val(rcvBuyAvgAmt);
												}else {
													rcvBuyAvgAmt = parseInt($formL120s04b.find("#rcvBuyAvgAmt" + i).val(),10);
												}						
												
												// C應收帳款無追索權賣方融資平均餘額(TWD)
												//var rcvSellAvgAmt = ($formL120s04b.find("#rcvSellAvgAmt" + i).val() == undefined || 
												//$formL120s04b.find("#rcvSellAvgAmt" + i).val() == null || 
												//$formL120s04b.find("#rcvSellAvgAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#rcvSellAvgAmt" + i).val(),10);
												
												var rcvSellAvgAmt = 0;
												if($formL120s04b.find("#rcvSellAvgAmt" + i).val() == undefined || 
												$formL120s04b.find("#rcvSellAvgAmt" + i).val() == null || 
												$formL120s04b.find("#rcvSellAvgAmt" + i).val() == ""){
													rcvSellAvgAmt = 0;
													$formL120s04b.find("#rcvSellAvgAmt" + i).val(rcvSellAvgAmt);
												} else {
													rcvSellAvgAmt = parseInt($formL120s04b.find("#rcvSellAvgAmt" + i).val(),10);
												}
												
												// 報酬率% = D/(A-B+C)
												if((avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt) != 0){
													// 四捨五入取到小數點兩位																										
													if(i==3 || i == 6){
														//資料需要年化
														var num = new Number(profitAmt / (avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt)/(endMonth - beginMonth + 1)* 12 * 100);
														$formL120s04b.find("#profitRate" + i).html(parseFloat(num.toFixed(2)));
													}else{
														var num = new Number(profitAmt / (avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt) * 100);
														$formL120s04b.find("#profitRate" + i).html(parseFloat(num.toFixed(2)));
													}
																																	
												}
											}							
											// 進行儲存
											$.ajax({
												handler : responseJSON["handler"],
												type : "POST",
												dataType : "json",
												action : "saveL120s04b",
												data : {
													oid : oid,
													formL120s04b : JSON.stringify($formL120s04b.serializeData())
												},
												success : function(json) {
													//$.thickbox.close();
													$.thickbox.close();
													CommonAPI.showMessage(json.NOTIFY_MESSAGE);
												}
											});
										}
									}
								}
							});
						},
						"print" : function(){
							if($("#gridview_A-1-8-2").jqGrid('getGridParam','records') <= 0){
								// 報表無資料
								CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
							}else{
								var count = 0;
								$.form.submit({
							        url: "../../simple/FileProcessingService",
							        target: "_blank",
							        data: {
							        	mainId : responseJSON.mainId,
							        	rptOid : "R24" + "^" + "^" + "^" + "^" + "^" + $("#oidL120s04d").val(),//rptOid : "R24" + "^" + "",
										fileDownloadName : "l120r01.pdf",
										serviceName : "lms1205r01rptservice"
							        }
							    });		
							}							
						},
						"close" : function() {
							 API.confirmMessage(i18n.def['flow.exit'], function(res){
									if(res){
										$.thickbox.close();
									}
						        });
						}
					}
				});			
		}
	});		
}

//A.共借戶與本行往來實績彙總表
function tL120s04b_A(cellvalue, options, rowObject){
	var oid = rowObject.oid;
	
	// 進行查詢 
	$.ajax({		//查詢主要借款人資料
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "queryL120s04b_a",
		data : {
			oid : oid,
			mainId : responseJSON.mainId
		},
		success : function(json) {
			var $formL120s04b_a = $("#formL120s04b_a");
			$formL120s04b_a.find("#showDetailHtml").html(DOMPurify.sanitize(json.showDetailResult));
			$formL120s04b_a.find(".numeric").each(function(i){
				$(this).val(util.addComma($(this).val()));
			});
				$("#tL120s04b_a").thickbox({ // 使用選取的內容進行彈窗
					title : i18n.lmss07["L1205S07.grid43"],
					width : 1000,
					height : 500,
					modal : true,
					i18n:i18n.def,
					buttons : {
						"saveData" : function() {
							var docDate6 = $("#docDate_3_1").val();
							$("#_docDateYear").val(docDate6.substring(0, 4));
							$("#_docDateMonth").val(docDate6.substring(docDate6.indexOf("~")+1, docDate6.length-1));
							
							$("#tL120s04bDate").thickbox({
								title : "",
								width : 800,
								height : 200,
								modal : false,
								i18n:i18n.def,
								buttons: {
									"sure": function(){
										if(!$("#tL120s04bDateTmpForm").find("#_docDateYear,#_docDateMonth").valid()){
											return false;
										}
										var beginMonth = 1;
										var endMonth = parseInt($("#_docDateMonth").val(), 10);
										var year = parseInt($("#_docDateYear").val(), 10);
										//$("#docDate3,#docDate6").val(year+"/"+beginMonth+"~"+endMonth+"月");		
										
										$("input[name^='docDate_3_']").val(year+"/"+beginMonth+"~"+endMonth+"月");		
																		
										$.thickbox.close();
										
										
										if($formL120s04b_a.valid()){
											$formL120s04b_a.find(".numeric").each(function(i){
												$(this).val(RemoveStringComma($(this).val()));
											});
											
											
											var custCount = $formL120s04b_a.find("#custCount").val() ;
											
											//進行合計計算***************
											//初始化合計欄位
											for (var i = 1; i <= 3; i++) {
												$formL120s04b_a.find("#avgDepositAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#avgLoanAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#rcvBuyAvgAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#rcvSellAvgAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#exportAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#importAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#profitAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#profitSalaryAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#profitTrustFdtaAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#profitRate" + "_" + i + "_"+ custCount).val(0);
											}
											//合計欄位
											for (var k = 1; k <= custCount-1; k++) {
											    for (var i = 1; i <= 3; i++) {
//													alert("#profitAmt" + "_" + i + "_"+ custCount+"="+
//													   $formL120s04b_a.find("#profitAmt" + "_" + i + "_"+ custCount).val()+"+"+$formL120s04b_a.find("#profitAmt" + "_" + i + "_"+ k).val()													
//													);

												   	$formL120s04b_a.find("#avgDepositAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#avgDepositAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#avgDepositAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													$formL120s04b_a.find("#avgLoanAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#avgLoanAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#avgLoanAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													$formL120s04b_a.find("#rcvBuyAvgAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#rcvBuyAvgAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#rcvBuyAvgAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													$formL120s04b_a.find("#rcvSellAvgAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#rcvSellAvgAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#rcvSellAvgAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													$formL120s04b_a.find("#exportAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#exportAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#exportAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													$formL120s04b_a.find("#importAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#importAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#importAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													 
													$formL120s04b_a.find("#profitAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#profitAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#profitAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													$formL120s04b_a.find("#profitSalaryAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#profitSalaryAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#profitSalaryAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													$formL120s04b_a.find("#profitTrustFdtaAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#profitTrustFdtaAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#profitTrustFdtaAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
												}
											}
												
												
											// 進行報酬率計算
											for (var k = 1; k <= custCount; k++) {
												for(var i=1; i<=3; i++){
													// D利潤貢獻(TWD)
													var profitAmt = 0;									
													if($formL120s04b_a.find("#profitAmt" + "_"+i + "_"+ k).val() == undefined || 
													$formL120s04b_a.find("#profitAmt" + "_"+i + "_"+ k).val() == null || 
													$formL120s04b_a.find("#profitAmt" + "_"+i + "_"+ k).val() == ""){
														profitAmt = 0;
														$formL120s04b_a.find("#profitAmt" + "_"+i + "_"+ k).val(profitAmt);
													}else {
														profitAmt = parseInt($formL120s04b_a.find("#profitAmt" + "_"+i + "_"+ k).val(),10);
													}								
																									
													// A平均授信(TWD)
													var avgLoanAmt = 0;
													if($formL120s04b_a.find("#avgLoanAmt" + "_"+i + "_"+ k).val() == undefined || 
													$formL120s04b_a.find("#avgLoanAmt" + "_"+i + "_"+ k).val() == null || 
													$formL120s04b_a.find("#avgLoanAmt" + "_"+i + "_"+ k).val() == ""){
														avgLoanAmt = 0;
														$formL120s04b_a.find("#avgLoanAmt" + "_"+i + "_"+ k).val(avgLoanAmt);
													} else {
														avgLoanAmt = parseInt($formL120s04b_a.find("#avgLoanAmt" + "_"+i + "_"+ k).val(),10);
													}
													
													
													// B應收帳款無追索買方承購平均餘額(TWD)
													var rcvBuyAvgAmt = 0;
													if($formL120s04b_a.find("#rcvBuyAvgAmt" + "_"+i + "_"+ k).val() == undefined || 
													$formL120s04b_a.find("#rcvBuyAvgAmt" + "_"+i + "_"+ k).val() == null || 
													$formL120s04b_a.find("#rcvBuyAvgAmt" + "_"+i + "_"+ k).val() == ""){
														rcvBuyAvgAmt = 0;
														$formL120s04b_a.find("#rcvBuyAvgAmt" + "_"+i + "_"+ k).val(rcvBuyAvgAmt);
													}else {
														rcvBuyAvgAmt = parseInt($formL120s04b_a.find("#rcvBuyAvgAmt" + "_"+i + "_"+ k).val(),10);
													}						
													
													// C應收帳款無追索權賣方融資平均餘額(TWD)
													var rcvSellAvgAmt = 0;
													if($formL120s04b_a.find("#rcvSellAvgAmt" + "_"+i + "_"+ k).val() == undefined || 
													$formL120s04b_a.find("#rcvSellAvgAmt" + "_"+i + "_"+ k).val() == null || 
													$formL120s04b_a.find("#rcvSellAvgAmt" + "_"+i + "_"+ k).val() == ""){
														rcvSellAvgAmt = 0;
														$formL120s04b_a.find("#rcvSellAvgAmt" + "_"+i + "_"+ k).val(rcvSellAvgAmt);
													} else {
														rcvSellAvgAmt = parseInt($formL120s04b_a.find("#rcvSellAvgAmt" + "_"+i + "_"+ k).val(),10);
													}
													
													// 報酬率% = D/(A-B+C)
													if((avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt) != 0){
														// 四捨五入取到小數點兩位																										
														if(i==3 || i == 6){
															//資料需要年化
															var num = new Number(profitAmt / (avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt)/(endMonth - beginMonth + 1)* 12 * 100);
															$formL120s04b_a.find("#profitRate" + "_"+i + "_"+ k).html(parseFloat(num.toFixed(2)));
														}else{
															var num = new Number(profitAmt / (avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt) * 100);
															$formL120s04b_a.find("#profitRate" + "_"+i + "_"+ k).html(parseFloat(num.toFixed(2)));
														}
																																		
													}
												}
											}
											
											// 進行儲存
											$.ajax({
												handler : responseJSON["handler"],
												type : "POST",
												dataType : "json",
												action : "saveL120s04b_a",
												data : {
													oid : oid,
													formL120s04b : JSON.stringify($formL120s04b_a.serializeData())
												},
												success : function(json) {
													//$.thickbox.close();
													$formL120s04b_a.find(".numeric").each(function(i){
														$(this).val(util.addComma($(this).val()));
													});
													$.thickbox.close();
													CommonAPI.showMessage(json.NOTIFY_MESSAGE);
												}
											});
											
										}
									}
								}
							});
							
							
						},
						"close" : function() {
							 API.confirmMessage(i18n.def['flow.exit'], function(res){
									if(res){
										$.thickbox.close();
									}
						        });
						}
					}
				});			
		}
	});		
}

/**
 * 產生往來實績彙總表
 */
function createReport(){
	var count=$("#gridview_A-1-8-1").jqGrid('getGridParam','records');
	if(count > 0){
		// L1205S07.confirm4=執行引進後會刪除已存在之借戶暨關係戶與本行往來實績彙總表，是否確定執行？
		CommonAPI.confirmMessage(i18n.lmss07["L1205S07.confirm4"], function(b){
			if (b) {					
				//是的function
				$.ajax({
					handler : responseJSON["handler"],
					type : "POST",
					dataType : "json",
					data : {
						formAction : "deleteL120s04b",
						mainId : responseJSON.mainid,
                        oidL120s04d: $("#oidL120s04d").val()
					},
					success : function(json) {
						$.thickbox.close();
						// 開始產生實績彙總表
						importReport();
					}
				});				
			}				
		});		
	}
}

/**
 * 刪除往來實績彙總表
 */
function deleteL120s04a(){
	var id = $("#gridview_A-1-8-2").getGridParam('selrow'); 
	var data = $("#gridview_A-1-8-2").getRowData(id);
//	if (data.oid == "" || data.oid == undefined || data.oid == null) {
//			CommonAPI.showMessage(i18n.lmss07["L1205S07.alert1"]);
//			return;
//	}
	CommonAPI.confirmMessage(i18n.def["action_003"], function(b){
		if (b) {					
			//是的function
			$.ajax({
				handler : responseJSON["handler"],
				type : "POST",
				dataType : "json",
				data : {
					formAction : "deleteL120s04b",
					mainId : responseJSON.mainid,
                    oidL120s04d: $("#oidL120s04d").val()
				},
				success : function(json) {
					// 更新實績彙總表Grid
					$("#LMS1205S07Form03").find("#gridview_A-1-8-2").trigger("reloadGrid");					
				}
			});				
		}				
	});
}

/**
 * 執行產生往來實績彙總表
 */
function importReport(){
	var $LMS1205S07Form03 = $("#LMS1205S07Form03");
	$.ajax({
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		data : {
			formAction : "importL120s04b",
			mainId : responseJSON.mainid,
		  	queryDateS : $LMS1205S07Form03.find("#queryDateS").html(),
		  	queryDateE : $LMS1205S07Form03.find("#queryDateE").html(),
            oidL120s04d: $("#oidL120s04d").val()
			
		},
		success : function(json) {
			// 更新實績彙總表Grid
			$("#LMS1205S07Form03").find("#gridview_A-1-8-2").trigger("reloadGrid");
		}
	});	
}

function cancelPrint(){
	var rows = $("#gridview_A-1-8-1").getGridParam('selarrrow');
	var list = "";
	var sign = ",";
	for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
			var data = $("#gridview_A-1-8-1").getRowData(rows[i]);
			list += ((list == "") ? "" : sign ) + data.oid;
		}
	}
	if (list == "") {
		CommonAPI.showMessage(i18n.lmss07["L1205S07.alert1"]);
		return;
	}	

	API.confirmMessage(i18n.lmss07["L1205S07.confirm1"],function(b){
		if(b){
			//是的function
			$.ajax({
				handler : responseJSON["handler"],
				type : "POST",
				dataType : "json",
				action : "cancelPrint",
				data : {
					listOid : list,
					mainId : responseJSON.mainid
				},
				success : function(json) {
					$("#LMS1205S07Form03").find("#gridview_A-1-8-1").trigger("reloadGrid");
				}
			});
		}else{
			//否的function
			//CommonAPI.showMessage(i18n.lmss07["L1205S07.alert3"]);
		}
	})	
}
function undoPrint(){
	var rows = $("#gridview_A-1-8-1").getGridParam('selarrrow');
	var list = "";
	var sign = ",";
	for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
			var data = $("#gridview_A-1-8-1").getRowData(rows[i]);
			list += ((list == "") ? "" : sign ) + data.oid;
		}
	}
	if (list == "") {
		CommonAPI.showMessage(i18n.lmss07["L1205S07.alert1"]);
		return;
	}
	API.confirmMessage(i18n.lmss07["L1205S07.confirm2"],function(b){
		if(b){
			//是的function
			$.ajax({
				handler : responseJSON["handler"],
				type : "POST",
				dataType : "json",
				action : "undoPrint",
				data : {
					listOid : list,
					mainId : responseJSON.mainid
				},
				success : function(json) {
					$("#LMS1205S07Form03").find("#gridview_A-1-8-1").trigger("reloadGrid");
				}
			});
		}else{
			//否的function
			//CommonAPI.showMessage(i18n.lmss07["L1205S07.alert4"]);
		}
	})	
}
function deleteAllCust(){
	API.confirmMessage(i18n.lmss07["L1205S07.confirm3"],function(b){
		if(b){
			//是的function
			$.ajax({
				handler : responseJSON["handler"],
				type : "POST",
				dataType : "json",
				action : "deleteL120s04a",
				data : {
					formAction : "deleteL120s04a",
					mainId : responseJSON.mainid,
                    oidL120s04d: $("#oidL120s04d").val()
				},
				success : function(json) {
					var $LMS1205S07Form03 = $("#LMS1205S07Form03");
					$LMS1205S07Form03.setData(json.LMS1205S07Form03,false);
					$LMS1205S07Form03.find("#gridview_A-1-8-1").trigger("reloadGrid");
				}
			});
		}else{
			//否的function
			//CommonAPI.showMessage(i18n.lmss07["L1205S07.alert5"]);
		}
	})
}

function proPertyFormatter(cellvalue,otions,rowObject){
	 //登錄授信科目的格式化
	var itemName='';	
	if(cellvalue == null || 	
	   cellvalue == undefined || 
	   cellvalue == ""){
		//授信科目為空!!
	}else{
		var list = cellvalue.split("|");
		if(cellvalue){
		itemName = i18n.lms1405s02["L140M01a.type"+list[0]];
			if(cellvalue.length > 1){
	  			for(var i =1; i< list.length ;i++ ){
	  				var itemone = i18n.lms1405s02["L140M01a.type"+list[i]];
	  				itemName = itemName +"、"+itemone; 
	  			}
			}
		}
	} 		
 	return itemName;
}

function test999(oid) {
	$("#borrower-data999").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss07["L1205S07.thickbox9"],
		width : 960,
		height : 500,
		modal : true,
		i18n:i18n.def,
		buttons : {
			"reQuery" : function() {
				var $LMS1205S07Form03 = $("#LMS1205S07Form03");
				$.ajax({
					handler : responseJSON["handler"],
					type : "POST",
					dataType : "json",
					action : "rQueryL120s04a",
					data : {
						oid : oid,
						mainId : responseJSON.mainid,
		  				custId : $("#tLMS1205S07Form03").find("#custId").html(),
		  				dupNo : $("#tLMS1205S07Form03").find("#dupNo").html(),
		  				custName : $("#tLMS1205S07Form03").find("#custName").html(),
		  				queryDateS : $LMS1205S07Form03.find("#queryDateS").html(),
		  				queryDateE : $LMS1205S07Form03.find("#queryDateE").html(),
                        oidL120s04d: $("#oidL120s04d").val()
					},
					success : function(obj) {
						var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");	
						$tLMS1205S07Form03.setData(obj);
						for(o in obj.tLMS1205S07Form03.custRelation){
							$tLMS1205S07Form03.find("[name=custRelation]").each(function(i){
								if($(this).val() == obj.tLMS1205S07Form03.custRelation[o]){
									$(this).attr("checked",true);
								}
							});
						}
						$("LMS1205S07Form03").setData(obj.LMS1205S07Form03,false);						
						$("#gridview_A-1-8-1").trigger("reloadGrid");
					}
				});
			},
			"saveData" : function() {
				var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");
				var $LMS1205S07Form03 = $("#LMS1205S07Form03");
				var count=$("#gridview_A-1-8-1").jqGrid('getGridParam','records');
				if(count == 0){
					CommonAPI.showMessage(i18n.lmss07["L1205S07.error14"]);
					return;
				}
				var list = "";
				var sign = ",";
				if($tLMS1205S07Form03.valid()){
					var checkSub = false;
					var kind = 0;
					var checkCou = 0;
					$tLMS1205S07Form03.find("[name=custRelation]:checkbox:checked").each(function(i){
						if($(this).val() == 3){
							checkSub = true;
							kind = 1;
						}else if($(this).val() == 4){
							checkSub = true;
							kind = 2;
						}
						list += ((list == "") ? "" : sign ) + $(this).val();
						checkCou++;
					});
					if(checkCou == 0){
						CommonAPI.showMessage(i18n.lmss07["L1205S07.error15"]);
						return;
					}
					if(checkSub && list.length > 1){
						if(kind == 1){
							// 集團企業合計
							CommonAPI.showMessage(i18n.lmss07["L1205S07.error13"]);
						}else if(kind == 2){
							// 關係企業合計
							CommonAPI.showMessage(i18n.lmss07["L1205S07.error12"]);
						}
						return;
					}
					$.ajax({		//查詢主要借款人資料
						handler : responseJSON["handler"],
						type : "POST",
						dataType : "json",
						action : "saveL120s04a",
						data : {
							tLMS1205S07Form03 : JSON.stringify($tLMS1205S07Form03.serializeData()),
							mainId : responseJSON.mainid,
			  				custId : $("#tLMS1205S07Form03").find("#custId").html(),
			  				dupNo : $("#tLMS1205S07Form03").find("#dupNo").html(),
			  				queryDateS : $LMS1205S07Form03.find("#queryDateS").html(),
			  				queryDateE : $LMS1205S07Form03.find("#queryDateE").html(),
							oid : oid,
							list : list,
                            oidL120s04d: $("#oidL120s04d").val()
						},
						success : function(json) {
							$LMS1205S07Form03.find("#gridview_A-1-8-1").trigger("reloadGrid");
							oid = json.newOid;
						}
					});
					//$.thickbox.close();					
				}
			},
			// "刪除本頁": function() {alert("刪除本頁");},
			"close" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
					if(res){
						$.thickbox.close();
					}
		         });
			}
		}
	});
}

function addData() {
	$("#formAdd").reset();
	var count=$("#gridview_A-1-8-1").jqGrid('getGridParam','records');
	if(count == 0){
		CommonAPI.showMessage(i18n.lmss07["L1205S07.error14"]);
		return;
	}	
	$("#addData").find("#showSel03").hide();
		$("#addData").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss07["L1205S07.thickbox11"],
		width : 800,
		height : 200,
		modal : true,
		align : 'center',
		valign: 'bottom',
		i18n:i18n.lmss07,
		buttons : {
			"L1205S07.thickbox1" : function() {				
				var gridCust = $("#gridview_A-1-8-1").getCol("custId");
				var gridName = $("#gridview_A-1-8-1").getCol("custName");
				if(!$("#selCus03").children().is("option")){
		  			CommonAPI.showMessage(i18n.lmss07["L1205S07.alert7"]);
		  			return;					
				}else if($("#selCus03 option:eq(0)").attr("selected")){
		  			CommonAPI.showMessage(i18n.lmss07["L1205S07.alert6"]);
		  			return;
				}else{
			  		var	custIdAll = $("#selCus03 option:selected").val();
					var custIdOnly = custIdAll.substring(0,custIdAll.length-1) + " " + custIdAll.substring(custIdAll.length-1);
					var	custName = $("#selCus03 option:selected").html();
					var custNameOnly = custName.substring(custIdAll.length+3);
/*
					var countCustId = 0;
					var countCustName = 0;					
					for(o in gridCust){
						if(custIdOnly == gridCust[o]){
							countCustId++;
						}
					}
*/
/*
					for(p in gridName){									
						if(custNameOnly == FtoH(gridName[p])){
							countCustName++;
						}
					}
*/
/*
					if(countCustId >= 1){	//&& countCustName >= 1
			  			CommonAPI.showMessage(i18n.lmss07["L1205S07.alert8"]);
			  			return;
					}
*/					
					var $LMS1205S07Form03 = $("#LMS1205S07Form03");
			  		$.ajax({
			  			handler : responseJSON["handler"],
			  			type : "POST",
			  			dataType : "json",
			  			data : 
			  			{
			  				formAction : "addL120s04a",
			  				custIdAll : custIdAll,
							custName : custName,
							mainId : responseJSON.mainId,
			  				queryDateS : $LMS1205S07Form03.find("#queryDateS").html(),
			  				queryDateE : $LMS1205S07Form03.find("#queryDateE").html(),
                            oidL120s04d: $("#oidL120s04d").val()
			  			},
			  			success : function(json) {
							var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");		  				
			  				$tLMS1205S07Form03.setData(json);
			  				$tLMS1205S07Form03.find("#createBY2").html(DOMPurify.sanitize(i18n.lmss07["L1205S07.createBY2"]));
			  				$("#LMS1205S07Form03").find("#gridview_A-1-8-1").trigger("reloadGrid");
			  				test999("");
			  			}
			  		});	  		
					$.thickbox.close();			  			
		  		}				
			},
			// "刪除本頁": function() {alert("刪除本頁");},
			"L1205S07.thickbox2" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

/**
 * 讀取往來彙總以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc3(cellvalue, options, rowObject) {
	ilog.debug(rowObject);
	$("#tLMS1205S07Form03").reset();
	$.ajax({
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "queryL120s04a",
		data : {
			oid : rowObject.oid
		},
		success : function(obj) {
			var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");
			$tLMS1205S07Form03.setData(obj);
			for(o in obj.tLMS1205S07Form03.custRelation){
				$tLMS1205S07Form03.find("[name=custRelation]").each(function(i){
					var $this = $(this);
					if($this.val() == obj.tLMS1205S07Form03.custRelation[o]){
						$this.attr("checked",true);
					}
				});
			}
			$tLMS1205S07Form03.setData(obj.LMS1205S07Form03,false);
			test999(obj.tLMS1205S07Form03.oid);
		}
	});	
}

/**
 * 引進額度明細表以讀取資本適足率
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function getBisFromCn() {
	$.ajax({		//查詢主要借款人資料
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "getBis",
		data : {
			mainId : responseJSON.mainid
		},
		success : function(obj) {		
			$("#LMS1205S07Form02").find("#gridview_A-1-9-1").trigger("reloadGrid");	//更新Grid內容
		}
	});	
}

/**
 * 寫回額度明細表
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function saveToL140m01a() {
	$.ajax({		//查詢主要借款人資料
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "saveToL140m01a",
		data : {
			mainId : responseJSON.mainid
		},
		success : function(obj) {
		}
	});	
}

/**
 * 讀取資本適足率以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc(cellvalue, options, rowObject) {
	ilog.debug(rowObject);
	$("#tLMS1205S07Form02").reset();
	$(function(){
		var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
		if(rowObject.crdFlag == i18n.lmss07["L1205S07.index2"]){
			$tLMS1205S07Form02.find("#crdRatio").attr({id: "collAmt", name: "collAmt"});
			$tLMS1205S07Form02.find("#rskMega").attr({id: "rskRatio", name: "rskRatio"});
			$tLMS1205S07Form02.find("#rskCrd").val("");
			$tLMS1205S07Form02.find("#rskAmt2").attr({id: "rskAmt1", name: "rskAmt1"});
			$tLMS1205S07Form02.find("#rskr2").attr({id: "rskr1", name: "rskr1"});
			$tLMS1205S07Form02.find("#camt2").attr({id: "camt1", name: "camt1"});
			$tLMS1205S07Form02.find("#bisr2").attr({id: "bisr1", name: "bisr1"});
			$tLMS1205S07Form02.find("#costr2").attr({id: "costr1", name: "costr1"});
		}else{
			$tLMS1205S07Form02.find("#collAmt").attr({id: "crdRatio", name: "crdRatio"});
			$tLMS1205S07Form02.find("#rskRatio").attr({id: "rskMega", name: "rskMega"});
			$tLMS1205S07Form02.find("#rskCrd").val("");
			$tLMS1205S07Form02.find("#rskAmt1").attr({id: "rskAmt2", name: "rskAmt2"});
			$tLMS1205S07Form02.find("#rskr1").attr({id: "rskr2", name: "rskr2"});
			$tLMS1205S07Form02.find("#camt1").attr({id: "camt2", name: "camt2"});
			$tLMS1205S07Form02.find("#bisr1").attr({id: "bisr2", name: "bisr2"});
			$tLMS1205S07Form02.find("#costr1").attr({id: "costr2", name: "costr2"});
		}
	});
	$.ajax({		//查詢主要借款人資料
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "queryBis",
		data : {
			oid : rowObject.oid
		},
		success : function(obj) {	
			$("#tLMS1205S07Form02").setData(obj);
			addBis(obj.tLMS1205S07Form02.oid);
		}
	});	
}

/**
 * 修改資本適足率
 */
function addBis(oid) {
	$("#thickboxAdd").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss07["L1205S07.thickbox12"],
		width : 965,
		height : 330,
		modal : true,
		i18n:i18n.def,
		buttons : {
			"reQuery" : function() {
				var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
				$.ajax({		//查詢主要借款人資料
					handler : responseJSON["handler"],
					type : "POST",
					dataType : "json",
					action : "queryBis2",
					data : {
						oid : oid,
						crdFlag : $tLMS1205S07Form02.find("#crdFlag").html()
					},
					success : function(obj) {	
						$tLMS1205S07Form02.setData(obj.tLMS1205S07Form02, false);
					}
				});
			},
			"calculate" : function() {
				var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
				$.ajax({		//查詢主要借款人資料
					handler : responseJSON["handler"],
					type : "POST",
					dataType : "json",
					action : "calculateBis",
					formId : 'tLMS1205S07Form02',
					data : {
						oid : oid,
						tLMS1205S07Form02 : JSON.stringify($tLMS1205S07Form02.serializeData())
					},
					success : function(json) {
						var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
						$tLMS1205S07Form02.setData(json.tLMS1205S07Form02);					
					}
				});	
			},
			"saveData" : function() {
				var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
				$.ajax({		//查詢主要借款人資料
					handler : responseJSON["handler"],
					type : "POST",
					dataType : "json",
					action : "calculateBis",
					data : {
						oid : oid,
						tLMS1205S07Form02 : JSON.stringify($tLMS1205S07Form02.serializeData())
					},
					success : function(json) {
						var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
						$tLMS1205S07Form02.setData(json.tLMS1205S07Form02);
						$.ajax({		//查詢主要借款人資料
							handler : responseJSON["handler"],
							type : "POST",
							dataType : "json",
							action : "saveBis",
							data : {
								oid : oid,
								mainId : responseJSON.mainId,
								tLMS1205S07Form02 : JSON.stringify($tLMS1205S07Form02.serializeData())
							},
							success : function(json) {
								$("#LMS1205S07Form02").find("#gridview_A-1-9-1").trigger("reloadGrid");
							}
						});											
					}
				});				
				//$.thickbox.close();
			},
			"close" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

/**
 * 讀取利害關係人以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc2(cellvalue, options, rowObject) {
	ilog.debug(rowObject);
	$.ajax({
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "queryL120s06b",
		data : {
			oid : rowObject.oid,
			requery : false
		},
		success : function(obj) {
			var $tLMS1205S07Form04 = $("#tLMS1205S07Form04");
			bbbb999(obj.tLMS1205S07Form04.oid);
			$tLMS1205S07Form04.setData(obj.tLMS1205S07Form04);
			$tLMS1205S07Form04.find("#oldOid").val(obj.tLMS1205S07Form04.oid);
			if(obj.tLMS1205S07Form04.printMode == '2'){
				$tLMS1205S07Form04.find("[name='printMode']:eq(1)").attr("checked",true);
			}else{
				$tLMS1205S07Form04.find("[name='printMode']:eq(0)").attr("checked",true);
			}
		}
	});	
}

//利害關係人授信條件對照表-thickBox
function bbbb999(oid){
	  $("#borrower-data921").thickbox({	// 使用選取的內容進行彈窗
	    title : i18n.lmss07["L1205S07.thickbox14"],//'利害關係人授信條件對照表'
	    width : 960,
	    height : 500,
	    modal : true,
	    i18n:i18n.def,
	    buttons: {
			  	"saveData": function() {
					var $tLMS1205S07Form04 = $("#tLMS1205S07Form04");
					if($tLMS1205S07Form04.valid()){
						$.ajax({		//查詢主要借款人資料
							handler : responseJSON["handler"],
							type : "POST",
							dataType : "json",
							action : "saveL120s06b",
							data : {
								oid : oid,
								tLMS1205S07Form04 : JSON.stringify($tLMS1205S07Form04.serializeData())
							},
							success : function(json) {
								$("#LMS1205S07Form04").find("#gridviewShow").trigger("reloadGrid");
							}
						});
						$.thickbox.close();						
					}
			  	},	   
				"close": function() {
					 API.confirmMessage(i18n.def['flow.exit'], function(res){
							if(res){
								$.thickbox.close();
							}
				        });
				}
		     }
		});
}	
function openbox(){
	$("#formSearch").find("#showSel04").hide();
	$("#formSearch").find("#showBrid").hide();
	$("#formSearch").reset();
	  $("#showGrid").hide();
	  $("#gridviewAA").setGridParam({'selrow' : null}).trigger("reloadGrid");
	  $("#LMS1205S07Form04").find("#gridviewAA").trigger("reloadGrid");
	  $("#openbox").thickbox({	// 使用選取的內容進行彈窗
	    title : i18n.lmss07["L1205S07.thickbox14"],//'利害關係人授信條件對照表',
	    width : 700,
	    height : 500,
	    modal : true,
		align:"center",
		valign:"bottom",
		i18n:i18n.lmss07,
	    buttons: {
		  	"L1205S07.thickbox1": function() {
		  		var id = $("#gridviewAA").getGridParam('selrow'); 
		  		var data = $("#gridviewAA").getRowData(id);
				if(!$("#selCus04").children().is("option")){
		  			CommonAPI.showMessage(i18n.lmss07["L1205S07.alert7"]);
		  			return;					
				}else if($("#selCus04 option:eq(0)").attr("selected")){
		  			CommonAPI.showMessage(i18n.lmss07["L1205S07.alert6"]);
		  			return;
				}else if (data.mainId == "" || data.mainId == undefined || data.mainId == null) {
		  			CommonAPI.showMessage(i18n.lmss07["L1205S07.alert1"]);
		  			return;
		  			}	
		  		$.ajax({
		  			handler : responseJSON["handler"],
		  			type : "POST",
		  			dataType : "json",
		  			action : "queryL120s06c",
		  			data : {
		  				mainId : data.mainId,
		  				oldOid : $("#tLMS1205S07Form04").find("#oldOid").val()
		  			},
		  			success : function(json) {
		  				$("#showGrid").hide();
		  				$("#tLMS1205S07Form04").setData(json.tLMS1205S07Form04,false);
		  				$("#LMS1205S07Form04").find("#gridviewShow").trigger("reloadGrid");
		  			}
		  		});
		  		//gridviewAA
				$.thickbox.close();
				},	   
			"L1205S07.thickbox2": function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$("#showGrid").hide();
							$.thickbox.close();
						}
			        });
				}
		     }
		});
}	

function deleteL120s06a(){
	var rows = $("#gridviewShow").getGridParam('selarrrow');
	var list = "";
	var sign = ",";
	for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
			var data = $("#gridviewShow").getRowData(rows[i]);
			list += ((list == "") ? "" : sign ) + data.oid;
		}
	}
	if (list == "") {
		CommonAPI.showMessage(i18n.lmss07["L1205S07.alert1"]);
		return;
	}
	$.ajax({
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "deleteL120s06b",
		data : {
			listOid : list,
			sign : sign
		},
		success : function(json) {
			$("#LMS1205S07Form04").find("#gridviewShow").trigger("reloadGrid");
		}
	});
	$.thickbox.close();
}
	
function openbox222(){
	  $("#openbox222").thickbox({	// 使用選取的內容進行彈窗
	    title : i18n.lmss07["L1205S07.thickbox15"],//'額度明細表選擇'
	    width : 800,
	    height :400,
	    modal : true,
		align:"center",
		valign:"bottom",
		i18n:i18n.lmss07,
	    buttons: {
		  	"L1205S07.thickbox1": function() {
					var rows = $("#gridviewCC").getGridParam('selarrrow');
					var list = "";
					var sign = ",";
					for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
						if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
							var data = $("#gridviewCC").getRowData(rows[i]);
							list += ((list == "") ? "" : sign ) + data.oid;
						}
					}
					if (list == "") {
						CommonAPI.showMessage(i18n.lmss07["L1205S07.alert1"]);
						return;
					}
					$.ajax({
						handler : responseJSON["handler"],
						type : "POST",
						dataType : "json",
						action : "addL120s06a",
						data : {
							listOid : list,
							mainId : responseJSON.mainid,
							sign : sign
						},
						success : function(json) {		
							$("#LMS1205S07Form04").find("#gridviewShow").trigger("reloadGrid");
						}
					});
					$.thickbox.close();
				},	   
			"L1205S07.thickbox2": function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
				}
		     }
		});
}

//資本適足率列印(產報表)
function printBIS(){
	if($("#gridview_A-1-9-1").jqGrid('getGridParam','records') <= 0){
		// 報表無資料
		CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
	}else{
		var count = 0;
		$.form.submit({
	        url: "../../simple/FileProcessingService",
	        target: "_blank",
	        data: {
	        	mainId : responseJSON.mainId,
	        	rptOid : "R04" + "^" + "",
				fileDownloadName : "l120r01.pdf",
				serviceName : "lms1205r01rptservice"
	        }
	    });		
	}
}

function keyinHKMAText(){	
	$("#L120M01D_hkmadiv").thickbox({
		title : '',
		width : 800,
		height : 450,
		modal : true,
		align : 'center',
		valign: 'bottom',
		i18n:i18n.def,
		buttons : {
			 "sure": function() {				                    		
				 $.thickbox.close();
             },				                     
             "cancel": function() {
            	
            	$.ajax({		
            		handler : responseJSON["handler"],
            		type : "POST",
            		dataType : "json",
            		action : "getHKMAText",
            		data : { 
            			mainId : responseJSON.mainid
            		},
            		success : function(json) {
            			$F = $("#L120M01D_hkmaForm");
						$F.setData(json.L120M01D_hkmaForm);
						$.thickbox.close();
            		}
            	});
            	
             }
		}
	});
}

function importHKMAText(){
	$F = $("#L120M01D_hkmaForm");
	
	$.ajax({		
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "importHKMAText",
		data : { 
			"L120M01D_hkmaForm": JSON.stringify($F.serializeData()) 
		},
		success : function(json) {
			var newstr = [];
			newstr.push("<p><table border='1' cellpadding='0' cellspacing='0'>");
			for(var i=1;i<=7;i++){
				newstr.push("<tr>");
				
					newstr.push("<td style='vertical-align:top;width: 165px'>");
					newstr.push("<p style='margin:0px;padding:0px;font-family: 新細明體;font-size: 16px;'>");
					newstr.push($F.find("#label_hkmaText"+i).html());
					newstr.push("</p>");
					newstr.push("</td>");
				
					newstr.push("<td style='width: 450px'>");
					newstr.push("<p style='margin:0px;padding:0px;font-family: 新細明體;font-size: 16px;'>");
					newstr.push(json["hkmaText"+i]);
					newstr.push("</p>");
					newstr.push("</td>");
					
				newstr.push("</tr>");
			}
			newstr.push("</table></p>");
			setCkeditor2("ffbody",$("#ffbody").val()+newstr.join(""));
		}
	});
	

}

//綜合評估及敘做理由列印(產報表)
function printDscr04(){
    //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
    CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
        if (b) {
			// 儲存
			$("#buttonPanel").find("#btnSave").click();
			setTimeout(function(){
				var gffbody = getCkeditor("ffbody");
				if(gffbody == "" || 
				 gffbody == null || 
				 gffbody == undefined){
					// 報表無資料
					CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
				}else{
					var count = 0;
					$.form.submit({
				        url: "../../simple/FileProcessingService",
				        target: "_blank",
				        data: {
				        	mainId : responseJSON.mainId,
				        	rptOid : "R16" + "^" + "",
							fileDownloadName : "l120r01.pdf",
							serviceName : "lms1205r01rptservice"
				        }
				    });	
				}				
			},2000);						
		}
	});
}

//往來彙總(產報表)
function printR14(){
	if($("#gridview_A-1-8-1").jqGrid('getGridParam','records') <= 0){
		// 報表無資料
		CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
	}else{
		var count = 0;
		$.form.submit({
	        url: "../../simple/FileProcessingService",
	        target: "_blank",
	        data: {
	        	mainId : responseJSON.mainId,
//	        	rptOid : "R14" + "^" + "",
                rptOid : "R14" + "^" + "^" + "^" + "^" + "^" + $("#oidL120s04d").val(),
				fileDownloadName : "l120r01.pdf",
				serviceName : "lms1205r01rptservice"
	        }
	    });	
	}
}

/**
 * 產生主要關係戶與本行授信往來比較表(Excel)
 */
function creExcel(){
	API.confirmMessage(i18n.def["confirmRun"],function(b){
		if(b){
			//是的function
			$.ajax({
				handler : responseJSON["handler"],
				type : "POST",
				dataType : "json",
				action : "creExcel",
				data : {
					mainId : $("#oidL120s04d").val(),//responseJSON.mainId,
					type : "1"
				},
				success : function(json) {
					$("#gridviewPare").trigger("reloadGrid");
				}
			});	
/*
	        $.form.submit({
	            url: "../../simple/FileProcessingService",
	            target: "_blank",
	            data: {
	                mainId: responseJSON.mainId,
	                fileDownloadName: "LMS1201M01A.xls",
	                serviceName: "lms1201xlsservice"
	            }
	        });
*/
		}else{
			//否的function
			//CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert5"]);
		}
	})
}

function applyEquatorPrinciples(){

    var $LMS1205S07Form05 = $("#LMS1205S07Form05");
	
    $.ajax({ // 查詢主要借款人資料
		handler : "lms1205formhandler",
		type : "POST",
		dataType : "json",
		data : {
			formAction : "applyEquatorPrinciples",
			mainId : responseJSON.mainId,
			LMS1205S07Form05: JSON.stringify($LMS1205S07Form05.serializeData())
		},
		success : function(json) {
			var newstr = json.equatorPrinciples;
			setCkeditor2("itemDscr03",newstr+$("#itemDscr03").val());
		}
	});
}

/**
 * J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
 * 產生集團／關係企業與本行授信往來條件比較表(Excel)
 */
function creExcel2() {
	API.confirmMessage(i18n.def["confirmRun"], function(b) {
		if (b) {
			// 是的function
			$.ajax({
				handler : responseJSON["handler"],
				type : "POST",
				dataType : "json",
				action : "creExcel2",
				data : {
					mainId : $("#oidL120s04d").val(),//responseJSON.mainId,
					type : "2"
				},
				success : function(json) {
					$("#gridviewPare").trigger("reloadGrid");
				}
			});
			/*
			 * $.form.submit({ url: "../../simple/FileProcessingService",
			 * target: "_blank", data: { mainId: responseJSON.mainId,
			 * fileDownloadName: "LMS1201M01A.xls", serviceName:
			 * "lms1201xlsservice" } });
			 */
		} else {
			// 否的function
			// CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert5"]);
		}
	})
}

