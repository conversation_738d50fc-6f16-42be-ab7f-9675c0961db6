
$.extend($cap._fn, {
    /**
     * The way of use localSave in common rule. ex: var o=$('#divId').localSave({});
     * @param {Object} value
     */
    localSave: {
        rule: "[role=localSaveStore]",
        fn: {
            /**
             * add current value
             */
            save: function(){
                var value = this.val();
                return value && this.add(value) || false;
            },
            
            /**
             * add a value to a field of the document.
             * @param {Object} value
             */
            add: function(value){
                var key = $(this).data("swfkey");
                if (key) {
                    // 與最後一筆檢查判斷是否有修改，有修改再存。
                    var old = this.get();
                    if (old && value.length == old.length && value === old) {
                        return false;
                    }
                    $.localSave.swfFunction.apply(this, ["add", value]);
                    return true;
                }
                return false;
            },
            
            /**
             * get item by key (document oid + field id).
             */
            get: function(){
                var $this = $(this);
                $this.removeData("itemget");
                var mainOid = $this.data("swfkey");
                $.localSave.swfFunction.apply(this, ["getByKey", mainOid]);
                var ret = $this.data("itemget");
                if (ret && ret.text) {
                    return decodeURIComponent(ret.text);
                }
            },
            
            /**
             * get all items in specific field of the document.
             */
            getItems: function(){
                var $this = $(this);
                var items = $.localSave.getItems();
                var mainOid = $this.data("swfkey");
                var fieldid = $this.attr("id");
                if (items == undefined || !mainOid) {
                    return;
                }
                return items[mainOid] && items[mainOid][fieldid];
            },
            
            /**
             * get swfkey of init.
             */
            getSwfKey: function(){
                return $(this).data("swfkey");
            },
            
            /**
             * remove item by key (document oid + field id).
             */
            removeAll: function(){
                var mainOid = $(this).data("swfkey");
                if (mainOid) {
                    $.localSave.swfFunction.apply(this, ["removeByKey", mainOid]);
                }
            },
            
            openHistory: function(){
                var $this = this;
                var list = $this.getItems() || {};
                // if (!list) {
                //     API.showMessage("no History")
                //     return;
                // }
                var o = {}, s = {}, dialog = $("#storeDialog"), select = dialog.find("#storeSelect");
                for (var i in list) {
                    var d = new Date(list[i].timestamp);
                    o[list[i].timestamp] = d.getFullYear() + "-" + (d.getMonth() + 1) + "-" + d.getDate() + " " + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds();
                    s[list[i].timestamp] = decodeURIComponent(list[i].text);
                }
                select.setOptions(o);
                var buttons = {}
                buttons[i18n.def.sure] = function(){
                    if (select.val()) {
                        $this.val(s[select.val()]);
                    }
                    $.thickbox.close();
                };
                buttons[i18n.def.saveData] = function(){
                    $this.save();
                    $.thickbox.close();
                }
                buttons[i18n.def.cancel] = function(){
                    $.thickbox.close();
                };
                
                
                dialog.thickbox({
                    title: i18n && i18n.def && i18n.def.confirmTitle || "提示",
                    // modal: true,
                    width: 150,
                    height: 50,
                    align: 'right',
                    valign: 'bottom',
                    buttons: buttons
                });
            }
        }
    }
});
var localdfd = $.Deferred();
jQuery.fn.extend({
    /**
     * This is just create YUI localSaveStore tool for local save.
     *
     * @param {JSON}
     *            settings
     */
    localSave: function(){
        var $this = $(this), oid = $("#mainOid").val() || responseJSON && responseJSON.mainOid || undefined;
        if (oid == undefined) {
			localdfd.resolve(false);
            return;
        }
		// 如果 locaStorage 裡保存的資料不能轉成 json object 的話就 init
		var existing = localStorage.getItem(oid);
		try {
		  JSON.parse(existing);
		} catch (e) {
		  existing = undefined;
		}
		typeof existing == "undefined" && $.localSave.init(oid);
        $this.attr("role", "localSaveStore");
        $this.data("swfkey", oid);
        
        // 設定存放本地端儲存大多筆數
        $.localSave.setRowCount(Properties && Properties.localSaveRowCount || 3);
        
        // 增加 yuiSWF 功能
        $.extend($this, $cap._fn.localSave.fn);
		localdfd.resolve(true);
        return $this;
    }
});


$.localSave = {

    /**
     * 設定存放本地端儲存大多筆數
     * @param {Object} value
     */
    setRowCount: function(value){
        return value && $(this).data("rowCount", value) || false;
    },
    
    /**
     * 取得存放本地端儲存大多筆數
     */
    getRowCount: function(){
        return $(this).data("rowCount") || false;
    },
    
	/* upgradetest 參考ces做法
    init: function(mainOid){
        window.swfstoreObj = null;
        mainOid = mainOid || $("#mainOid").val();
        var dfd = $.Deferred();
        var innerHtml = "<div id=\"__swfstoreContainer\" style=\"width:0px;height:0px;\">Unable to load Flash content. The YUI SWFStore Utility requires Flash Player 9.0.115 or higher.<a href=\"http://www.adobe.com/go/getflash/\">Get Flash</a></div>";
        var fixedURL = webroot+"/js/yui/swfstore.swf?" + Math.random(), oldSWFURL = YAHOO.util.SWFStore.SWFURL;
        YAHOO.util.SWFStore.SWFURL = fixedURL || YAHOO.util.SWFStore.SWFURL;
        if ($("body").find("#__swfstoreContainer").length == 0) {
            $("body").append(innerHtml);
            swfstoreObj = new YAHOO.util.SWFStore("__swfstoreContainer", false, false, mainOid);
        }
        else {
            $("body").find("#__swfstoreContainer").replaceWith(innerHtml);
            swfstoreObj = new YAHOO.util.SWFStore("__swfstoreContainer", false, false, mainOid);
        }
        YAHOO.util.SWFStore.SWFURL = oldSWFURL;
        swfstoreObj.addListener("swfReady", onContentReady);
        swfstoreObj.addListener("quotaExceededError", onQuotaExceededError);
        dfd.promise(swfstoreObj);
        function onContentReady(event){
            swfstoreObj.setShareData(false);
            dfd.resolve(swfstoreObj, mainOid);
			localdfd.resolve(true);
        }
        function onQuotaExceededError(event){
            // 關閉所有的thickbox。
            $.thickbox.close();
            var buttons = {};
            buttons[i18n.def.sure] = function(){
                $.thickbox.close();
                $.localSave.init(mainOid);
                $.localSave.displaySettings();
            };
            $("#accessControlDialg").thickbox({
                title: i18n && i18n.def && i18n.def.confirmTitle || "提示",
                modal: true,
                width: 150,
                height: 50,
                align: 'right',
                valign: 'bottom',
                buttons: buttons
            });
        }
        return swfstoreObj;
    },
	*/
	init : function(mainOid) {
	  mainOid = mainOid || $("#mainOid").val();
	  localStorage.setItem(mainOid, "{}");
	},
    
    /**
     * 顯示sharedObject存取設定dialog。
     */
	/* upgradetest ces已沒有這個方法
    displaySettings: function(mainOid){
        if (typeof swfstoreObj == "undefined") {
            return;
        }
        swfstoreObj.done(function(swf, mainOid){
            var retString = swf.setSize(10241);
            if (retString == "pending") {
                var buttons = {};
                buttons[i18n.def.close] = function(){
                    $.thickbox.close();
                    $.localSave.init(mainOid);
                };
                // 關閉所有的thickbox。
                $.thickbox.close();
                $("#__swfstoreContainer").thickbox({
                    title: i18n && i18n.def && i18n.def['localSave.setedClose'] || i18n.def.confirmTitle || "提示",
                    modal: true,
                    width: 400,
                    height: 350,
                    buttons: buttons
                });
                if ($.browser.mozilla && "fox") {
                    setTimeout(function(){
                        swf.setSize(10241);
                    }, 500);
                }
            }
            //"flushed" 空間充足，所以就不顯示設定視窗了。
        });
    },
	*/
    
    /**
     * remove all item in all field of the document(by mainOid) (not clear() of localSaveStore.).
     * @param {Object} mainOid
     */
    removeAll: function(mainOid, fieldid){
        $.localSave.swfFunction.apply(this, ["removeAll", mainOid, fieldid]);
    },
    /**
     * get all items in the document.
     */
    getItems: function(){
        var $this = $(this);
        $.localSave.swfFunction.apply(this, ["getItems"]);
        var ret = $this.data("items");
        return ret;
    },
    /**
     * localSaveStore API for used.
     */
    swfFunction: function(){
        var $this = $(this), action = arguments[0], value = arguments[1], fieldname = arguments[2];
        
        /**
         * do something by action and argument.
         * @param {Object} swf
         */
		switch (action) {
			 case "add":
			   value = value || $this.val() || $this.html();
			   var _key = $this.data("swfkey");
			   // var _oldval = swf.getValueOf(_key);
			   var _oldval = localStorage.getItem(_key);
			   var _newval = _getNewValue($this.attr("id"), value, JSON.parse(_oldval));
			   localStorage.setItem(_key, JSON.stringify(_newval));
			   break;
			 case "getItems":
			   var len = localStorage.length;
			   var items = {};
			   for (var i = 0; i < len; i++) {
			     try {
			       items[localStorage.key(i)] = JSON.parse(localStorage.getItem(localStorage.key(i)));
			     } catch (e) {
			       items[localStorage.key(i)] = {};
			     }
			   }
			   $this.removeData("items");
			   $this.data("items", len < 1 ? undefined : items);
			   break;
			 case "getByKey":
			   var item = localStorage.getItem(value);
			   var freshMeat = _getFreshMeat($this.attr("id"), JSON.parse(item));
			   $this.data("itemget", freshMeat);
			   break;
			 case "removeByKey":
			   var fieldid = $this.attr("id");
			   var items;
			   try {
			     items = JSON.parse(localStorage.getItem(value));
			   } catch (e) {
			     items = {};
			   }
			   _removeBadMeat(fieldid, items);
			   if (items != undefined) {
			     if (_isEmpty(items)) {
			       localStorage.removeItem(value);
			     } else {
			       localStorage.setItem(value, items);
			     }
			   }
			   break;
			 case "removeAll":
			   var items;
			   try {
			     items = JSON.parse(localStorage.getItem(value));
			   } catch (e) {
			     items = {};
			   }
			   if (fieldname != undefined) {
			     _removeBadMeat(fieldname, items);
			     if (items != undefined) {
			       if (_isEmpty(items)) {
			         localStorage.removeItem(value);
			       } else {
			         localStorage.setItem(value, items);
			       }
			     }
			   } else {
			     if (items != undefined) {
			       localStorage.removeItem(value);
			     }
			   }
			   break;
			 default:
			   break;
		 }
            
        
        /**
         * 取得的新增值後的json物件。
         * @param {Object} arg
         * @param {Object} oldvalue
         */
        function _getNewValue(fieldid, arg, oldvalue){
            var d = new Date(), data = (oldvalue) ? oldvalue : JSON.parse("{}"), _meat = (oldvalue && oldvalue[fieldid]) ? oldvalue[fieldid] : [], json;
            try {
                json = JSON.parse("{\"timestamp\":" + d.getTime() + ",\"text\":\"" + encodeURIComponent(arg) + "\"}");
            } 
            catch (e) {
                logDebug("parsing to json error", e);
                return data;
            }
            var rowCount = $.localSave.getRowCount();
            if (rowCount && _meat.length < rowCount) {
                _meat.push(json);
            }
            else {
                // 只留rowCount筆在localsave版本裡面。
                while (_meat.length >= rowCount) {
                    _meat.shift();
                }
                _meat.push(json);
            }
            data[fieldid] = _meat;
            return data;
        }
        
        /**
         * 取得的刪除某field的值後的json物件。
         * @param {Object} fieldid
         * @param {Object} item
         */
        function _removeBadMeat(fieldid, item){
            if (item && item[fieldid]) {
                delete item[fieldid];
            }
            return item;
        }
        
        /**
         * 檢查json物件是否為空的了。
         * @param {Object} item
         */
        function _isEmpty(item){
            var isEmpty = true;
            if (item == undefined) {
                return isEmpty;
            }
            for (var i in item) {
                if (i != undefined) {
                    isEmpty = false;
                }
            }
            return isEmpty;
        }
        
        /**
         * 取得最新的一筆值。
         * @param {Object} item
         */
        function _getFreshMeat(fieldid, item){
            if (item && item[fieldid]) {
                var len = item[fieldid].length;
                var freshMeat = item[fieldid][len - 1];
                return freshMeat;
            }
            return;
        }
    }
}

//var swfstoreObj;// = $.localSave.init();
$(function() {
    var storeDialog = $("#storeDialog");
    if (!storeDialog.length == 0) {
        storeDialog = $("<div id='storeDialog' style='display:none'><select id='storeSelect' name='storeSelect' space='true' comboType='2'></select></div>").appendTo("body");
    }
    var accessControlDialg = $("#accessControlDialg");
    if (!accessControlDialg.length == 0) {
        accessControlDialg = $("<div id='accessControlDialg' style='display:none'><div>" + i18n.def['localSave.quotaExceededError'] + "</div></div>").appendTo("body");
    }
});


