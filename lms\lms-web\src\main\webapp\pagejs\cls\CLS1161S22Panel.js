var panelAction = {
    handler: 'cls1161m02formhandler',
    gridhandler: 'cls1161gridhandler',
    grid: null,
    init: function(){
    },
    build: function(){
        panelAction.grid = $("#gridview").iGrid({
            handler: panelAction.gridhandler,
            height: 400,
            action: 'queryScoreModel',
            sortname: 'custNo|custId|createTime',
            sortorder: "asc|asc|desc",
            rowNum: 20,
            rownumbers: true,
            colModel: [{
                name: 'oid', hidden: true //是否隱藏
            }, {
            	name: 'mainId', hidden: true //是否隱藏
            }, {
                name: 'rawMarkModel',
                hidden: true //是否隱藏
            }, {
                name: 'varVer',
                hidden: true
            }, {
                colHeader: i18n.cls1161s22["C161M02A.createTime"], //建立日期
                align: "center",
                width: 60, //設定寬度
                sortable: true, //是否允許排序
                formatter: GridFormatter.date['yyyy-mm-dd'],
                name: 'createTime' //col.id
            }, {
                colHeader: i18n.cls1161s22["C161M02A.ownBrId"], //編製單位代號
                align: "left",
                width: 60, //設定寬度
                sortable: true, //是否允許排序
                name: 'ownBrId' //col.id
            }, {
                colHeader: i18n.cls1161s22["C161M02A.custId"], //身分證統編
                align: "left",
                width: 80,
                name: 'custId', //身分證統編
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                    pageAction.doAdjRating(rowObject);
                },
                sortable: true //是否允許排序
            }, {
                colHeader: ' ', //身分證統編
                align: "left",
                width: 10,
                name: 'dupNo', //身分證統編重複碼
                sortable: false
            }, {
                colHeader: i18n.cls1161s22["C161M02A.custName"], //借款人姓名
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'custName' //col.id
            }, {
                colHeader: '非房貸初始評等', align: "center", width: 100, sortable: true, name: 'c120s01q.grade1'		
            }, {
                colHeader: '升降等數', align: "center", width: 100, sortable: false, name: 'grade2'
            }, {
                colHeader: i18n.cls1161s22["C161M02A.grade3"], align: "center", width: 100,  sortable: true, 
                name: 'c120s01q.grade3' //把 name 用 c120s01q.grade3, 才能 sorting。若用 grade3，會有 error
            }, {
                colHeader: i18n.cls1161s22["C161M02A.varVer"], //非房貸模型版本
                align: "center",
                width: 80, //設定寬度
                sortable: false, //是否允許排序n,
                name: 'varVer' //col.id
            }, {
                colHeader: i18n.cls1161s22["C161M02A.updater"], //異動人員號碼
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'updater' //col.id
            }],
            ondblClickRow: function(rowid){
                
            }
        });
       
        $("#findBaseDataIdBt").click(function(){
        	$("#findBaseDataId").removeAttr("disabled").removeAttr("readonly");
        	
    		var search_custId = $("#findBaseDataId").val();
    		
    		var my_post_data = {
    			formAction : "queryScoreModel"
    			, search_custId: search_custId
    		};
    		
    		panelAction.grid .jqGrid("setGridParam", {
    			postData : my_post_data,
    			search : true
    		}).trigger("reloadGrid");			
    	});
        
        // EJCIC聯徵查詢列印
        $('#btEjcicQueryPrint').click(function(){
        	panelAction.print('ejcic');
        });
        // ETCH票信查詢列印
        $('#btEtchQueryPrint').click(function(){
        	panelAction.print('etch');
        });
        
        $('#btExpAdj').click(function(){
        	 //不能直接用 $.capFileDownload(...)，會強制 encode 把  | 轉成 %7C
            $.form.submit({
           	url: __ajaxHandler,
        		target : "_blank",
        		data : {
        			_pa : 'lmsdownloadformhandler',
        			'mode': 'A',
                    'mainId': responseJSON.mainId,
        			'fileDownloadName' : 'export.xls',
        			'serviceName' : "cls1161r03rptservcie"
        		}
        	 });
        });
        
        $('#btOpenQ').click(function(){
        	var data = panelAction.grid .getSingleData();
            if (data) {
            	pageAction.openQDoc(data);
            }
        });
    },
    print : function (queryType){
    	var data = panelAction.grid .getSingleData();
        if (data) {
        	var my_data = {'mainId': responseJSON.mainId
        			, 'custId': data.custId
        			, 'dupNo': data.dupNo
        			, 'queryType': queryType
        			, 'isC120M01A': 'true'
        		};
            $.form.submit({
                url: webroot + '/app/cls/cls1131p01',
                target: data.oid+"_"+queryType,
                data: my_data
            });
        }
    }

};


$(function(){
    panelAction.build();
    panelAction.init();
});
