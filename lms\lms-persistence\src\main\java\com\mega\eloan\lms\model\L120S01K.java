/* 
 * L120S01K.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotNull;

import org.apache.bval.constraints.NotEmpty;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check2;

/** 個金配偶資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name="L120S01K", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo"}))
public class L120S01K extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 配偶統一編號 **/
	@Column(name="MCUSTID", length=10, columnDefinition="VARCHAR(10)")
	@NotNull(message="{required.message}", groups=Check2.class)
	@NotEmpty(message="{required.message}", groups=Check2.class)
	private String mCustId;

	/** 配偶統一編號重複碼 **/
	@Column(name="MDUPNO", length=1, columnDefinition="CHAR(1)")
	@NotNull(message="{required.message}", groups=Check2.class)
	@NotEmpty(message="{required.message}", groups=Check2.class)
	private String mDupNo;

	/** 配偶姓名 **/
	@Column(name="MNAME", length=120, columnDefinition="VARCHAR(120)")
	@NotNull(message="{required.message}", groups=Check2.class)
	@NotEmpty(message="{required.message}", groups=Check2.class)
	private String mName;

	/** 
	 * 外國人無特定住所<p/>
	 * 100/12/08新增<br/>
	 *  Y/N<br/>
	 *  ※國內DBU/OBU才需填寫
	 */
	@Column(name="MFOREGINAL", length=1, columnDefinition="CHAR(1)")
	private String mForeginal;

	/** 
	 * 性別<p/>
	 * F男、M女
	 */
	@Column(name="MSEX", length=1, columnDefinition="CHAR(1)")
	private String mSex;

	/** 出生日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="MBIRTHDAY", columnDefinition="DATE")
	private Date mBirthday;

	/** 
	 * 行業別<p/>
	 * 100/12/08調整<br/>
	 *  (單選)<br/>
	 *  01.公<br/>
	 *  02.軍<br/>
	 *  03.製造<br/>
	 *  04.營造<br/>
	 *  05.金融<br/>
	 *  06.資訊<br/>
	 *  07.保險<br/>
	 *  08.個人服務<br/>
	 *  99.其他(自行輸入)
	 */
	@Column(name="MJOBKIND", length=2, columnDefinition="VARCHAR(2)")
	private String mJobKind;

	/** 
	 * 行業別(其他)<p/>
	 * 100/12/08新增<br/>
	 *  其他(自行輸入)
	 */
	@Column(name="MJOBOTHER", length=60, columnDefinition="VARCHAR(60)")
	private String mJobOther;

	/** 服務單位名稱 **/
	@Column(name="MCOMNAME", length=60, columnDefinition="VARCHAR(60)")
	private String mComName;

	/** 
	 * 服務單位地址<p/>
	 * 64個全型字
	 */
	@Column(name="MCOMADDR", length=192, columnDefinition="VARCHAR(192)")
	private String mComAddr;

	/** 服務單位電話 **/
	@Column(name="MCOMTEL", length=20, columnDefinition="VARCHAR(20)")
	private String mComTel;

	/** 職稱 **/
	@Column(name="MJOBTITLE", length=60, columnDefinition="VARCHAR(60)")
	private String mJobTitle;

	/** 年資 **/
	@Column(name="MSENIORITY", columnDefinition="DECIMAL(2,0)")
	private Integer mSeniority;

	/** 年薪(幣別) **/
	@Column(name="MPAYCURR", length=3, columnDefinition="VARCHAR(3)")
	private String mPayCurr;

	/** 年薪(金額) **/
	@Column(name="MPAYAMT", columnDefinition="DECIMAL(13,0)")
	private Long mPayAmt;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得配偶統一編號 **/
	public String getMCustId() {
		return this.mCustId;
	}
	/** 設定配偶統一編號 **/
	public void setMCustId(String value) {
		this.mCustId = value;
	}

	/** 取得配偶統一編號重複碼 **/
	public String getMDupNo() {
		return this.mDupNo;
	}
	/** 設定配偶統一編號重複碼 **/
	public void setMDupNo(String value) {
		this.mDupNo = value;
	}

	/** 取得配偶姓名 **/
	public String getMName() {
		return this.mName;
	}
	/** 設定配偶姓名 **/
	public void setMName(String value) {
		this.mName = value;
	}

	/** 
	 * 取得外國人無特定住所<p/>
	 * 100/12/08新增<br/>
	 *  Y/N<br/>
	 *  ※國內DBU/OBU才需填寫
	 */
	public String getMForeginal() {
		return this.mForeginal;
	}
	/**
	 *  設定外國人無特定住所<p/>
	 *  100/12/08新增<br/>
	 *  Y/N<br/>
	 *  ※國內DBU/OBU才需填寫
	 **/
	public void setMForeginal(String value) {
		this.mForeginal = value;
	}

	/** 
	 * 取得性別<p/>
	 * F男、M女
	 */
	public String getMSex() {
		return this.mSex;
	}
	/**
	 *  設定性別<p/>
	 *  F男、M女
	 **/
	public void setMSex(String value) {
		this.mSex = value;
	}

	/** 取得出生日期 **/
	public Date getMBirthday() {
		return this.mBirthday;
	}
	/** 設定出生日期 **/
	public void setMBirthday(Date value) {
		this.mBirthday = value;
	}

	/** 
	 * 取得行業別<p/>
	 * 100/12/08調整<br/>
	 *  (單選)<br/>
	 *  01.公<br/>
	 *  02.軍<br/>
	 *  03.製造<br/>
	 *  04.營造<br/>
	 *  05.金融<br/>
	 *  06.資訊<br/>
	 *  07.保險<br/>
	 *  08.個人服務<br/>
	 *  99.其他(自行輸入)
	 */
	public String getMJobKind() {
		return this.mJobKind;
	}
	/**
	 *  設定行業別<p/>
	 *  100/12/08調整<br/>
	 *  (單選)<br/>
	 *  01.公<br/>
	 *  02.軍<br/>
	 *  03.製造<br/>
	 *  04.營造<br/>
	 *  05.金融<br/>
	 *  06.資訊<br/>
	 *  07.保險<br/>
	 *  08.個人服務<br/>
	 *  99.其他(自行輸入)
	 **/
	public void setMJobKind(String value) {
		this.mJobKind = value;
	}

	/** 
	 * 取得行業別(其他)<p/>
	 * 100/12/08新增<br/>
	 *  其他(自行輸入)
	 */
	public String getMJobOther() {
		return this.mJobOther;
	}
	/**
	 *  設定行業別(其他)<p/>
	 *  100/12/08新增<br/>
	 *  其他(自行輸入)
	 **/
	public void setMJobOther(String value) {
		this.mJobOther = value;
	}

	/** 取得服務單位名稱 **/
	public String getMComName() {
		return this.mComName;
	}
	/** 設定服務單位名稱 **/
	public void setMComName(String value) {
		this.mComName = value;
	}

	/** 
	 * 取得服務單位地址<p/>
	 * 64個全型字
	 */
	public String getMComAddr() {
		return this.mComAddr;
	}
	/**
	 *  設定服務單位地址<p/>
	 *  64個全型字
	 **/
	public void setMComAddr(String value) {
		this.mComAddr = value;
	}

	/** 取得服務單位電話 **/
	public String getMComTel() {
		return this.mComTel;
	}
	/** 設定服務單位電話 **/
	public void setMComTel(String value) {
		this.mComTel = value;
	}

	/** 取得職稱 **/
	public String getMJobTitle() {
		return this.mJobTitle;
	}
	/** 設定職稱 **/
	public void setMJobTitle(String value) {
		this.mJobTitle = value;
	}

	/** 取得年資 **/
	public Integer getMSeniority() {
		return this.mSeniority;
	}
	/** 設定年資 **/
	public void setMSeniority(Integer value) {
		this.mSeniority = value;
	}

	/** 取得年薪(幣別) **/
	public String getMPayCurr() {
		return this.mPayCurr;
	}
	/** 設定年薪(幣別) **/
	public void setMPayCurr(String value) {
		this.mPayCurr = value;
	}

	/** 取得年薪(金額) **/
	public Long getMPayAmt() {
		return this.mPayAmt;
	}
	/** 設定年薪(金額) **/
	public void setMPayAmt(Long value) {
		this.mPayAmt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
