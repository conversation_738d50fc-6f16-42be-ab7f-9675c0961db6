thickbox.addMsg= whether new information?
detailTitle= MOE students student loan breakdown of the amount of each foreign students subsidize interest rates (total non-interest)
canotInsert= can not add, please delete the file
uploadTxt= select additional files
deleteTxt= delete
close= Close
#==================================================
#\u300c\u6559\u80b2\u90e8\u88dc\u52a9\u7559\u5b78\u751f\u5c31\u5b78\u8cb8\u6b3e\u300d\u6bcf\u4f4d\u7559\u5b78\u751f\u88dc\u8cbc\u606f\uff08\u975e\u5229\u606f\u7e3d\u984d\uff09\u91d1\u984d\u660e\u7d30
#==================================================
C004M01A.detailTilte= submit information
C004M01A.date= Data Date
C004M01A.rptType= statements Category
C004M01A.bgnDate=Data from the date 
C004M01A.endDate= date are to
C004M01A.rptDate= determine the submitted date
C004M01A.rptName= Report Name
C004M01A.filePath= Additional file path
C004M01A.fileName= attached file name
C004M01A.rmk= Remarks
C004M01A.creator= establish personnel numbers
C004M01A.createTime= creation date
C004M01A.updater=The transaction staff numbers for
C004M01A.updateTime= transaction date
C004M01A.confirmDelete= whether to delete such data?
C004M01A.S1Name= Student loans S1 \u200b\u200bpolicy statements
C004M01A.uploadData= upload data
C004M01A.delData= delete data