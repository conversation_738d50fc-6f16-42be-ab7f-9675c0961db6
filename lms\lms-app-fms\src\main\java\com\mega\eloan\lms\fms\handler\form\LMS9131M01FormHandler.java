/* 
 *  LMS9131M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.form;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.NGFlagHelper;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.fms.service.LMS9131Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 授信簽報書審核層級整批修改
 * </pre>
 * 
 * @since 2013/01/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/16,GaryChang,new
 *          <li>2013/06/14,UFO,修正建擋維護審核層級整批修改功能應該也要讓債管處可以選到協議案執行修改
 *          </ul>
 */
@Scope("request")
@Controller("lms9131m01formhandler")
@DomainClass(L120M01A.class)
public class LMS9131M01FormHandler extends AbstractFormHandler {

	@Resource
	MisStoredProcService misStoredProcService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	LMSService lmsService;

	@Resource
	BranchService branchService;

	@Resource
	LMS9131Service lms9131service;

	@Resource
	UserInfoService userInfoService;

	/**
	 * 查詢L120M01A簽報書
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120m01a(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		if (!Util.isEmpty(oid)) {
			L120M01A l120m01a = lms9131service.findModelByOid(L120M01A.class,
					oid);
			result = formatResultShow(result, l120m01a, page);
		}
		return result;
	}

	/**
	 * 儲存
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveL120m01a(PageParameters params)
			throws CapException {
		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L120M01A l120m01a = (L120M01A) lms9131service.findModelByOid(
				L120M01A.class, oid);
		String CaseLvl = params.getString("CaseLvl");
		l120m01a.setCaseLvl(CaseLvl);
		lms9131service.saveL120M01A(l120m01a);
		String[] formSelectBoss = params.getStringArray("selectBoss");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String branchTypebr = UtilConstants.BRANCHTYPE.分行;
		String branchTypeArea = UtilConstants.BRANCHTYPE.營運中心;
		String branchTypeHead = UtilConstants.BRANCHTYPE.授管處;
		if (!Util.isEmpty(formSelectBoss)) {
			String apprid = Util.trim(params.getString("showApprId"));
			String AOId = Util.trim(params.getString("AOId"));
			String manager = Util.trim(params.getString("manager"));
			String recheck = Util.trim(params.getString("recheck"));
			String UnitManagerId = Util.trim(params.getString("UnitManagerId"));
			// 營運中心簽章欄
			String CenterNo = Util.trim(params.getString("CenterNo"));
			String AreaReCheck = Util.trim(params.getString("AreaReCheckId"));
			String AreaAppraiser = Util.trim(params
					.getString("AreaAppraiserId"));
			String UnitManagerA = Util.trim(params.getString("UnitManagerAId"));
			String HeadReCheck = Util.trim(params.getString("HeadReCheckId"));
			String HeadAppraiser = Util.trim(params
					.getString("HeadAppraiserId"));
			String UnitManagerH = Util.trim(params.getString("UnitManagerHId"));
			String HeadManager = Util.trim(params.getString("HeadManager"));
			String HeadSubLeader = Util.trim(params.getString("HeadSubLeader"));
			String AreaManager = Util.trim(params.getString("AreaManager"));
			String AreaSubLeader = Util.trim(params.getString("AreaSubLeader"));
			List<L120M01F> models = (List<L120M01F>) lms9131service
					.findListByMainId(L120M01F.class, l120m01a.getMainId());
			if (!models.isEmpty()) {
				lms9131service.deleteL120m01fs(models, false);

			}
			List<L120M01F> l120m01fs = new ArrayList<L120M01F>();
			if (!Util.equals(apprid, "")) {
				L120M01F apprl120m01f = new L120M01F();
				apprl120m01f.setCreator(user.getUserId());
				apprl120m01f.setCreateTime(CapDate.getCurrentTimestamp());
				apprl120m01f.setMainId(l120m01a.getMainId());
				apprl120m01f.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
				apprl120m01f.setStaffNo(apprid);
				apprl120m01f.setBranchType(branchTypebr);
				apprl120m01f.setBranchId(l120m01a.getCaseBrId());
				l120m01fs.add(apprl120m01f);
			}
			if (!Util.equals(AOId, "")) {
				L120M01F apprl120m01f = new L120M01F();
				apprl120m01f.setCreator(Util.addZeroWithValue(user.getUserId(),
						6));
				apprl120m01f.setCreateTime(CapDate.getCurrentTimestamp());
				apprl120m01f.setMainId(l120m01a.getMainId());
				apprl120m01f.setStaffJob(UtilConstants.STAFFJOB.帳戶管理員L2);
				apprl120m01f.setStaffNo(AOId);
				apprl120m01f.setBranchType(branchTypebr);
				apprl120m01f.setBranchId(l120m01a.getCaseBrId());
				l120m01fs.add(apprl120m01f);
			}
			for (String people : formSelectBoss) {
				if (!Util.equals(people, "")) {
					L120M01F l120m01f = new L120M01F();
					l120m01f.setCreator(user.getUserId());
					l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
					l120m01f.setMainId(l120m01a.getMainId());
					l120m01f.setBranchType(branchTypebr);
					l120m01f.setBranchId(l120m01a.getCaseBrId());
					l120m01f.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
					l120m01f.setStaffNo(people);
					l120m01fs.add(l120m01f);
				}
			}

			if (!Util.equals(recheck, "")) {
				L120M01F recheckl120m01f = new L120M01F();
				recheckl120m01f.setCreator(user.getUserId());
				recheckl120m01f.setCreateTime(CapDate.getCurrentTimestamp());
				recheckl120m01f.setMainId(l120m01a.getMainId());
				recheckl120m01f.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
				recheckl120m01f.setStaffNo(recheck);
				recheckl120m01f.setBranchType(branchTypebr);
				recheckl120m01f.setBranchId(l120m01a.getCaseBrId());
				l120m01fs.add(recheckl120m01f);
			}
			if (!Util.equals(manager, "")) {
				L120M01F managerl120m01f = new L120M01F();
				managerl120m01f.setCreator(user.getUserId());
				managerl120m01f.setCreateTime(CapDate.getCurrentTimestamp());
				managerl120m01f.setMainId(l120m01a.getMainId());
				managerl120m01f.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
				managerl120m01f.setStaffNo(manager);
				managerl120m01f.setBranchType(branchTypebr);
				managerl120m01f.setBranchId(l120m01a.getCaseBrId());
				l120m01fs.add(managerl120m01f);
			}
			if (!Util.equals(UnitManagerId, "")) {
				L120M01F UManagerl120m01f = new L120M01F();
				UManagerl120m01f.setCreator(user.getUserId());
				UManagerl120m01f.setCreateTime(CapDate.getCurrentTimestamp());
				UManagerl120m01f.setMainId(l120m01a.getMainId());
				UManagerl120m01f.setStaffJob(UtilConstants.STAFFJOB.單位主管L9);
				UManagerl120m01f.setStaffNo(UnitManagerId);
				UManagerl120m01f.setBranchType(branchTypebr);
				UManagerl120m01f.setBranchId(l120m01a.getCaseBrId());
				l120m01fs.add(UManagerl120m01f);
			}
			// 營運中心簽章欄
			if (!Util.equals(AreaReCheck, "")) {
				L120M01F l120m01f = new L120M01F();
				l120m01f.setCreator(user.getUserId());
				l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
				l120m01f.setMainId(l120m01a.getMainId());
				l120m01f.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
				l120m01f.setStaffNo(AreaReCheck);
				l120m01f.setBranchType(branchTypeArea);
				l120m01f.setBranchId(CenterNo);
				l120m01fs.add(l120m01f);
			}
			if (!Util.equals(AreaAppraiser, "")) {
				L120M01F l120m01f = new L120M01F();
				l120m01f.setCreator(user.getUserId());
				l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
				l120m01f.setMainId(l120m01a.getMainId());
				l120m01f.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
				l120m01f.setStaffNo(AreaAppraiser);
				l120m01f.setBranchType(branchTypeArea);
				l120m01f.setBranchId(CenterNo);
				l120m01fs.add(l120m01f);
			}
			if (!Util.equals(AreaManager, "")) {
				L120M01F l120m01f = new L120M01F();
				l120m01f.setCreator(user.getUserId());
				l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
				l120m01f.setMainId(l120m01a.getMainId());
				l120m01f.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
				l120m01f.setStaffNo(AreaManager);
				l120m01f.setBranchType(branchTypeArea);
				l120m01f.setBranchId(CenterNo);
				l120m01fs.add(l120m01f);
			}
			if (!Util.equals(AreaSubLeader, "")) {
				L120M01F l120m01f = new L120M01F();
				l120m01f.setCreator(user.getUserId());
				l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
				l120m01f.setMainId(l120m01a.getMainId());
				l120m01f.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
				l120m01f.setStaffNo(AreaSubLeader);
				l120m01f.setBranchType(branchTypeArea);
				l120m01f.setBranchId(CenterNo);
				l120m01fs.add(l120m01f);
			}
			if (!Util.equals(UnitManagerA, "")) {
				L120M01F l120m01f = new L120M01F();
				l120m01f.setCreator(user.getUserId());
				l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
				l120m01f.setMainId(l120m01a.getMainId());
				l120m01f.setStaffJob(UtilConstants.STAFFJOB.單位主管L9);
				l120m01f.setStaffNo(UnitManagerA);
				l120m01f.setBranchType(branchTypeArea);
				l120m01f.setBranchId(CenterNo);
				l120m01fs.add(l120m01f);
			}
			// 授管處簽章欄
			if (!Util.equals(HeadReCheck, "")) {
				L120M01F l120m01f = new L120M01F();
				l120m01f.setCreator(user.getUserId());
				l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
				l120m01f.setMainId(l120m01a.getMainId());
				l120m01f.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
				l120m01f.setStaffNo(HeadReCheck);
				l120m01f.setBranchType(branchTypeHead);
				l120m01f.setBranchId(UtilConstants.BankNo.授管處);
				l120m01fs.add(l120m01f);
			}
			if (!Util.equals(HeadAppraiser, "")) {
				L120M01F l120m01f = new L120M01F();
				l120m01f.setCreator(user.getUserId());
				l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
				l120m01f.setMainId(l120m01a.getMainId());
				l120m01f.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
				l120m01f.setStaffNo(HeadAppraiser);
				l120m01f.setBranchType(branchTypeHead);
				l120m01f.setBranchId(UtilConstants.BankNo.授管處);
				l120m01fs.add(l120m01f);
			}
			if (!Util.equals(UnitManagerH, "")) {
				L120M01F l120m01f = new L120M01F();
				l120m01f.setCreator(user.getUserId());
				l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
				l120m01f.setMainId(l120m01a.getMainId());
				l120m01f.setStaffJob(UtilConstants.STAFFJOB.單位主管L9);
				l120m01f.setStaffNo(UnitManagerH);
				l120m01f.setBranchType(branchTypeHead);
				l120m01f.setBranchId(UtilConstants.BankNo.授管處);
				l120m01fs.add(l120m01f);
			}
			if (!Util.equals(HeadManager, "")) {
				L120M01F l120m01f = new L120M01F();
				l120m01f.setCreator(user.getUserId());
				l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
				l120m01f.setMainId(l120m01a.getMainId());
				l120m01f.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
				l120m01f.setStaffNo(HeadManager);
				l120m01f.setBranchType(branchTypeHead);
				l120m01f.setBranchId(UtilConstants.BankNo.授管處);
				l120m01fs.add(l120m01f);
			}
			if (!Util.equals(HeadSubLeader, "")) {
				L120M01F l120m01f = new L120M01F();
				l120m01f.setCreator(user.getUserId());
				l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
				l120m01f.setMainId(l120m01a.getMainId());
				l120m01f.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
				l120m01f.setStaffNo(HeadSubLeader);
				l120m01f.setBranchType(branchTypeHead);
				l120m01f.setBranchId(UtilConstants.BankNo.授管處);
				l120m01fs.add(l120m01f);
			}
			lms9131service.saveL120m01fList(l120m01fs);

		}
		return new CapAjaxFormResult();
	}

	/**
	 * 取得單位人員
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult branchData(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		SignEnum[] signs;
		SignEnum[] appridsigns;
		Map<String, String> bossList = null;
		Map<String, String> appridList = null;
		// 查詢所選銀行的甲級主管、乙級主管清單
		String UnitNo = new String();
		if (!Util.equals(user.getUnitNo(), UtilConstants.BankNo.授管處)) {
			UnitNo = user.getUnitNo();
			signs = new SignEnum[] { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
					SignEnum.乙級主管, SignEnum.經辦人員 };
			appridsigns = new SignEnum[] { SignEnum.首長, SignEnum.單位主管,
					SignEnum.甲級主管, SignEnum.乙級主管, SignEnum.經辦人員 };
			bossList = userInfoService.findByBrnoAndSignId(UnitNo, signs);
			appridList = userInfoService.findByBrnoAndSignId(UnitNo,
					appridsigns);
		} else {
			// ufo@********:增加判斷是否為債管處
			UnitNo = NGFlagHelper.getBossDept(user,
					params.getString("branchId"));
			if (!Util.equals(UnitNo, "")) {
				bossList = userInfoService.getBRUserName(UnitNo);
				appridList = userInfoService.getBRUserName(UnitNo);
			}

		}
		result.set("bossList", new CapAjaxFormResult(bossList));
		result.set("appridList", new CapAjaxFormResult(appridList));
		return result;
	}

	/**
	 * 取得分行名單
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getbanchId(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		result.set("userbanchId", user.getUnitNo());
		result.set("userUnitType", user.getUnitType());
		return result;
	}

	/**
	 * 取得人員名單
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryuserId(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		String branchId = params.getString("branch");
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> userlist = userInfoService.findByBrnoAndSignId(
				branchId, signs);
		result.set("userlist", new CapAjaxFormResult(userlist));
		return result;

	}

	/**
	 * 查詢
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult querybranch(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		List<IBranch> listAll = branchService.getAllBranch();
		Map<String, String> mapBid = new TreeMap<String, String>();
		for (IBranch brn : listAll) {
			mapBid.put(brn.getBrNo(), brn.getBrName());
		}
		CapAjaxFormResult resultBid = new CapAjaxFormResult(mapBid);
		result.set("itemBranch", resultBid);

		return result;
	}// ;

	/**
	 * 格式化顯示訊息
	 * 
	 * @param mainId
	 *            簽報書mainId
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private CapAjaxFormResult formatResultShow(CapAjaxFormResult result,
			L120M01A l120m01a, Integer page) throws CapException {
		String mainId = l120m01a.getMainId();

		switch (page) {
		case 1:
			result = DataParse.toResult(l120m01a);
			// ---------------
			List<L120M01F> c120m01flist = (List<L120M01F>) lms9131service
					.findListByMainId(L120M01F.class, mainId);
			if (!Util.isEmpty(c120m01flist)) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				StringBuilder bossIdVal = new StringBuilder("");
				for (L120M01F c120m01f : c120m01flist) {
					// 要加上人員代碼
					String type = Util.trim(c120m01f.getStaffJob());
					String userId = Util.trim(c120m01f.getStaffNo());
					String branchType = Util.trim(c120m01f.getBranchType());
					String value = Util.trim(lmsService.getUserName(userId));

					switch (Util.parseInt(branchType)) {
					case 1:
						if ("L1".equals(type)) {// 分行經辦(Appraiser)
							result.set("showApprId", userId);
							result.set("showApprIdVal", value);
						} else if ("L2".equals(type)) {// 帳戶管理員(AO)
							result.set("AO", Util.addZeroWithValue(userId, 6));
							result.set("AOVal", value);
						} else if ("L3".equals(type)) {// 分行授信/覆核主管(Boss)
							bossId.append(bossId.length() > 0 ? "," : "");
							bossId.append(userId);
							bossIdVal.append(bossIdVal.length() > 0 ? "<br/>"
									: "");
							bossIdVal.append(userId);
							bossIdVal.append(" ");
							bossIdVal.append(value);
						} else if ("L4".equals(type)) {// 分行覆核主管(ReCheck)
							result.set("reCheckId", userId);
							result.set("reCheckIdVal", value);
						} else if ("L5".equals(type)) {// 單位/授權主管(Manager)
							result.set("managerId", userId);
							result.set("managerIdVal", value);
						} else if ("L9".equals(type)) {// 分行單位主管(UNIT_MANAGERID)
							result.set("UnitManagerId", userId);
							result.set("UnitManagerIdVal", value);
						}
						break;
					case 3:
						if ("L1".equals(type)) {// 營運中心經辦(AreaAppraiser)
							result.set("AreaAppraiserId", userId);
							result.set("AreaAppraiserIdVal", value);
						} else if ("L3".equals(type)) {// 營運中心襄理(AreaAppraiser)
							result.set("AreaManagerId", userId);
							result.set("AreaManagerIdVal", value);
						} else if ("L4".equals(type)) {// 營運中心覆核主管(AreaReCheck)
							result.set("AreaReCheckId", userId);
							result.set("AreaReCheckIdVal", value);
						} else if ("L5".equals(type)) {// 營運中心副營運長(AreaReCheck)
							result.set("AreaSubLeaderId", userId);
							result.set("AreaSubLeaderIdVal", value);
						} else if ("L9".equals(type)) {// 營運中心主管(UNIT_MANAGERID_A)
							result.set("UnitManagerAId", userId);
							result.set("UnitManagerAIdVal", value);
						}
						break;
					case 4:
						if ("L1".equals(type)) {// 授管處經辦(AreaAppraiser)
							result.set("HeadAppraiserId", userId);
							result.set("HeadAppraiserIdVal", value);
						} else if ("L3".equals(type)) {// 授管處覆核(AreaManager)
							result.set("HeadManagerId", userId);
							result.set("HeadManagerIdVal", value);
						} else if ("L4".equals(type)) {// 授管處覆核主管(AreaReCheck)
							result.set("HeadReCheckId", userId);
							result.set("HeadReCheckIdVal", value);
						} else if ("L5".equals(type)) {// 授管處副處長(HeadSubLeader)
							result.set("HeadSubLeaderId", userId);
							result.set("HeadSubLeaderIdVal", value);
						} else if ("L9".equals(type)) {// 授管處主管(UNIT_MANAGERID_A)
							result.set("UnitManagerHId", userId);
							result.set("UnitManagerHIdVal", value);
						}

						break;
					}
				}
				result.set("bossId", bossId.toString());
				result.set("bossIdVal", bossIdVal.toString());
			}
			// ----------------
			result.set("ownBrName",
					" " + branchService.getBranchName(l120m01a.getOwnBrId()));

			StringBuilder cntrNo = new StringBuilder("");

			result.set("creator", lmsService.getUserName(l120m01a.getCreator()));
			result.set("updater", lmsService.getUserName(l120m01a.getUpdater()));
			result.set("docStatus",
					getMessage("docStatus." + l120m01a.getDocStatus()));
			result.set("cntrNo", cntrNo.toString());
			break;
		}// close switch case
		result.set("docStatusVal", l120m01a.getDocStatus());
		if (!Util.isEmpty(l120m01a.getCustId())) {
			result.set("typCd", this.getMessage("typCd." + l120m01a.getTypCd()));
			result.set("showTypCd",
					this.getMessage("typCd." + l120m01a.getTypCd()));
			result.set("showCustId", StrUtils.concat(l120m01a.getCustId()
					.toUpperCase(), " ", l120m01a.getDupNo().toUpperCase(),
					" ", l120m01a.getCustName()));
		}
		String branchChk = Util.trim(l120m01a.getCaseBrId());
		if (Util.equals(l120m01a.getAreaChk(), 3)) {
			branchChk = l120m01a.getAreaBrId();
		} else {
			branchChk = branchService.getBranch(l120m01a.getCaseBrId())
					.getBrnGroup();
		}
		String AreaBrn = !Util.equals(l120m01a.getAuthLvl(), "1") ? branchChk
				: "";
		String AreaBrnVal = !Util.equals(l120m01a.getAuthLvl(), "1") ? branchService
				.getBranchName(branchChk) : "";

		result.set("CenterNo", AreaBrn);
		result.set("CenterNoVal", AreaBrnVal);
		result.set("randomCode", l120m01a.getRandomCode());
		result.set(EloanConstants.OID, CapString.trimNull(l120m01a.getOid()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(l120m01a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(l120m01a.getMainId()));
		return result;
	}

	/**
	 * 上傳ELF447
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult UpdateELF447(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String sign = Util.nullToSpace(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(sign);
		lms9131service.upLoadELF447(oidArray);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	/**
	 * 清除/新增
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult Clear(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String sign = Util.nullToSpace(params.getString("sign"));

		String flag = Util.trim(params.getString("flag"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidlist = listOid.split(sign);
		lms9131service.ClearL120M01A(oidlist, flag);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}
	// /**
	// * 壓力測試用
	// *
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapAjaxFormResult
	// * @throws CapException
	// */
	// @DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	// public IResult TESTMIS(PageParameters params)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// String oid = params.getString(EloanConstants.OID);
	// L120M01A l120m01a = lms9131service.findModelByOid(L120M01A.class, oid);
	// // lmsService.upLoadMIS(l120m01a);
	//
	// lms9131service.upLoadELF447(new String[] {oid});
	// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
	// .getMainMessage(this.getComponent(),
	// UtilConstants.AJAX_RSP_MSG.執行成功));
	// return result;
	// }

}
