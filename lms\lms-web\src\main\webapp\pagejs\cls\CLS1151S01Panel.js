//var panelAction01 = {
//    isInit: false,
//    initAction: function(){
//        alert(1);
//    }
//};
//
//_M.pageInitAcion["01"] = panelAction01;
var _PanelAction01 = {
    isInit: false,
    /**
     *頁面初始化的動作
     * */
    initAction: function(){
    	ilog.debug("_PanelAction01 :: initAction()");
        _M.initItem("01");
    },
    /**
     *初始化
     * @param {Object} $form 表單物件
     *  @param {Object} obj前端資訊
     */
    /**
     *修改案件視窗
     * @param {Object} obj前端資訊
     */
    openBox: function(obj){
        var $form = $(this.formId);
        this.init($form, obj);
        $("#CLS1151Form01").thickbox({
            title: i18n.def.confirmTitle,
            width: 500,
            height: 300,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if ($form.valid()) {
                        var $packLoanTr = $("#packLoanTr");
                        var $docCode = $form.find("[name=changeDocCode]:checked");
                        var docCode = $docCode.val();
                        var authLvl = $form.find("#changeAuthLvl").val();
                        var areaBrid = $form.find("#changeArea").val();
                        var changeNgFlag = $form.find("[name=changeNgFlag]:checked").val();
                        $packLoanTr.hide();
                        //當案件性質為一般才顯示整批團貸的選項
                        
                        if (_M.AllFormData.docCode == "1") {
                            $packLoanTr.show();
                        }
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
};

/**
 * 團貸案相關問題
 */
var ParentAction = {
    ParentGrid: null,
    openbox: function(){
        if (this.ParentGrid) {
            this.ParentGrid.setGridParam({
                postData: {
                    custId: $("#parentId").val(),
                    dupNo: $("#parentDupNo").val()
                },
                search: true
            }).trigger("reloadGrid");
        }
        else {
            this.ParentGrid = $("#ParentGrid").iGrid({
                handler: "cls1151gridhandler",
                //                handler: _M.fhandle,
                rowNum: 10,
                postData: {
                    formAction: "PTEAMAPPQuery",
                    custId: $("#parentId").val(),
                    dupNo: $("#parentDupNo").val()
                },
                rowNum: 10,
                autowidth: true,
                colModel: [{
                    colHeader: i18n.cls1151s01["L120M01G.A05"],//"簽案行",
                    name: 'ISSUEBRNO',
                    width: 20,
                    sortable: false,
                    align: "left"
                }, {
                    colHeader: i18n.cls1151s01["L120M01G.A04"],//"總戶名稱",
                    width: 200,
                    name: 'PROJECTNM',
                    align: "left",
                    sortable: true
                }, {
                    colHeader: i18n.cls1151s01["L120M01G.A08"],//L120M01G.A08=總戶案名
                    name: 'BUILDNAME',
                    align: "left",
                    width: 110,
                    sortable: false
                }, {
                    colHeader: i18n.cls1151s01["L120M01G.A06"],//"總戶序號",
                    width: 120,
                    name: 'GRPCNTRNO',
                    sortable: true
                }, {
                    name: 'YEAR',//"總額度申請年度"
                    hidden: true
                }, {
                    name: 'AMTAPPNO',//"批　　號"
                    hidden: true
                }]
            });
        }
        $("#ParentBox").thickbox({
            title: i18n.cls1151s01["L120M01G.A03"],//L120M01G.A03=引入總戶額度資訊
            width: 800,
            height: 300,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $grid = ParentAction.ParentGrid;
                    var data = $grid.getSingleData();
                    if (data) {
                        $.ajax({
                            handler: "cls1151m01formhandler",
                            formId: "empty",
                            action: "saveL120M01G",
                            data: {
                                data: JSON.stringify(data),
                                custId: $("#parentId").val(),
                                dupNo: $("#parentDupNo").val()
                            },
                            success: function(obj){
                                $.thickbox.close();
                                var $form = $("#CLS1151Form01");
                                $form.injectData(obj);
								$("#parentCntrNo").trigger('blur');//for J-108-0238 台電消貸明細
                            }
                        });
                    }
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
};
/**
 *是否整批貸款
 */
var $packLoanTb = $("#packLoanTb");
$("input[name=packLoan]").click(function(){
    if ($(this).val() == "Y") {
        $packLoanTb.show();
    }
    else {
        $packLoanTb.hide();
        $packLoanTb.find("span.field").html("");
        //$packLoanTb.find("input").val("");
        $packLoanTb.find("#parentCntrNo,#parentId,#parentDupNo,#deductSalaryWelfareCommitteeNo").val("");
        //[整批分戶貸款]改為[否]時，[歡喜信貸員工認股貸款適用條款]欄位改回預設值[否]
        $("input[name='staffRuleYN'][value='N']").attr('checked',true);
    }
});


/**
 * J-108-0238 台電消貸明細
 */
$("#parentCntrNo").blur(function(){
	var parentCntrNo = $(this).val();
	//if(parentCntrNo == '918110800027' || parentCntrNo == '918110900035' ){
	
	var grpCntrNo_TPC_321_list = $("#grpCntrNo_TPC_321_list").val()||'';
	//ilog.debug("parentCntrNo="+parentCntrNo+", grpCntrNo_TPC_321_list="+grpCntrNo_TPC_321_list+"[in-list=]"+(grpCntrNo_TPC_321_list.indexOf(parentCntrNo)>-1));
	if(grpCntrNo_TPC_321_list.indexOf(parentCntrNo) > -1){ 
		$("#deductSalaryWelfareCommitteeNoTr").show();
	}
	else{
		$("#deductSalaryWelfareCommitteeNoTr").hide();
	}
});


/**
 *取得總戶資訊
 */
$("#getParentInfoBt").click(function(){
    var $form = $("#CLS1151Form01");
    $.ajax({
        handler: "cls1151m01formhandler",
        action: "getParentInfo",
		async:false,
        data: {
            custId: $("#parentId").val(),
            dupNo: $("#parentDupNo").val(),
			parentCntrNo: $("#parentCntrNo").val()
        },
        success: function(obj){
            if (obj.msg) {
                API.showMessage(obj.msg);
            }
            else {
                if (obj.size == "1") {
                    $form.injectData(obj);
                }
                else {
                    ParentAction.openbox();
                }
            }
        }
    });
});

$("#btn_change_L140M01A_ownBrId").click(function(){
	API.confirmMessage(i18n.def["confirmRun"], function(b){
        if (b) {
        	$.ajax({
                handler: "cls1151m01formhandler",
                data: {
                    formAction: "change_L140M01A_ownBrId",
                    tabFormMainId: _M.tabMainId
                },
                success: function(rtn_obj){
                	$("#CLS1151Form01").injectData(rtn_obj);
                	API.showMessage(i18n.def.runSuccess);
                	$("#btn_change_L140M01A_ownBrId").hide();
                	API.triggerOpener("gridveiwCntrNoDoc");
                }
            });
        }
    });
	
});

_M.pageInitAcion["01"] = _PanelAction01;


