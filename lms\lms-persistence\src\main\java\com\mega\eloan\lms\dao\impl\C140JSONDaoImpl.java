/* 
 * C140JSONDaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.model.GenericBean;

import com.mega.eloan.lms.dao.C140JSONDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C140JSON;
import com.mega.eloan.lms.model.C140JSON_;
import com.mega.eloan.lms.model.C140M04A;
import com.mega.eloan.lms.model.C140M04B;
import com.mega.eloan.lms.model.C140M07A;

/**
 * <pre>
 * 徵信調查報告書副檔 JSON
 * </pre>
 * 
 * @since 2011/9/20
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
@Repository
public class C140JSONDaoImpl extends LMSJpaDao<C140JSON, String> implements
		C140JSONDao {

	@Override
	public int deleteByMeta(GenericBean meta) {
		Query query = entityManager
				.createNamedQuery("ces140json.deleteByMainIdAndPid");
		if(meta instanceof C140M04A){
			query.setParameter("mainId", ((C140M04A)meta).getMainId());
			query.setParameter("pid", ((C140M04A)meta).getUid());
		}else if(meta instanceof C140M04B){
			query.setParameter("mainId", ((C140M04B)meta).getMainId());
			query.setParameter("pid", ((C140M04B)meta).getUid());
		}else if(meta instanceof C140M07A){
			query.setParameter("mainId", ((C140M07A)meta).getMainId());
			query.setParameter("pid", ((C140M07A)meta).getUid());
		}
		
		return query.executeUpdate();
	}
	
	@Override
	public List<C140JSON> findByMainPid(String uid, String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140JSON_.mainId.getName(), mainId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140JSON_.pid.getName(), uid);

		return find(search);
	}

	@Override
	public List<C140JSON> findByMainPidTab(String uid, String mainId, String tab) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140JSON_.mainId.getName(), mainId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140JSON_.pid.getName(), uid);
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140JSON_.tab.getName(), tab);

		return find(search);
	}

	@Override
	public C140JSON findByMainPidTab(String uid, String mainId, String tab,
			String subTab) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140JSON_.mainId.getName(), mainId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140JSON_.pid.getName(), uid);
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140JSON_.tab.getName(), tab);
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140JSON_.subtab.getName(), subTab);

		return findUniqueOrNone(search);
	}
}// ;
