/* 
 * L830M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 帳戶管理員維護 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L830M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L830M01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * not null
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;
	
	/** mainId **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重複序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 客戶名稱 **/
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;
	
	/** a-Loan客戶id+檢核碼 **/
	@Size(max=11)
	@Column(name="ALCUSTID", length=11, columnDefinition="CHAR(11)")
	private String ALcustId;

	/** 
	 * 編製單位代號<p/>
	 * 評等單位/編製單位
	 */
	@Size(max=3)
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;

	/** 
	 * 目前文件狀態<p/>
	 * 編製中=01O<br/>
	 *  待覆核=02O<br/>
	 *  已核准=05O
	 */
	@Size(max=3)
	@Column(name="DOCSTATUS", length=3, columnDefinition="CHAR(3)")
	private String docStatus;

	/** 
	 * 文件URL<p/>
	 * CLS:國內個金<br/>
	 *  LMS:國內企金<br/>
	 *  NULL:海外案件
	 */
	@Size(max=40)
	@Column(name="DOCURL", length=40, columnDefinition="VARCHAR(40)")
	private String docURL;

	/** 
	 * (原) 建立人員號碼<p/>
	 * 原LNF013紀錄呈主管覆核人LNF013_CRE_SUPVNO
	 */
	@Size(max=6)
	@Column(name="ORIG_CREATOR", length=6, columnDefinition="CHAR(6)")
	private String orig_creator;

	/** 
	 * 建立人員號碼<p/>
	 * LNF013_CRE_SUPVNO
	 */
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 
	 * (原) 核准人員號碼<p/>
	 * 原LNF013紀錄之覆核主管LNF013_CRE_SUPVNO
	 */
	@Size(max=6)
	@Column(name="ORIG_APPROVER", length=6, columnDefinition="CHAR(6)")
	private String orig_approver;

	/** 
	 * 核准人員號碼<p/>
	 * LNF013_CRE_SUPVNO
	 */
	@Size(max=6)
	@Column(name="APPROVER", length=6, columnDefinition="CHAR(6)")
	private String approver;

	/** 
	 * 核准日期<p/>
	 * sendDateTime
	 */
	@Column(name="APPROVETIME", columnDefinition="TIMESTAMP")
	private Timestamp approveTime;

	/** 邏輯刪除日期 **/
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 
	 * 修改前_消金帳務管理員行編<p/>
	 * 修改前LNF013_STAFF_NO
	 */
	@Size(max=6)
	@Column(name="ORIG_AOID", length=6, columnDefinition="CHAR(6)")
	private String orig_AOId;

	/** 
	 * 修改後_消金帳務管理員行編<p/>
	 * LNF013_STAFF_NO
	 */
	@Size(max=6)
	@Column(name="AOID", length=6, columnDefinition="CHAR(6)")
	private String AOId;

	/** 備註 **/
	@Size(max=200)
	@Column(name="MEMO", length=200, columnDefinition="VARCHAR(200)")
	private String memo;

	/** 
	 * 取得oid<p/>
	 * not null
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  not null
	 **/
	public void setOid(String value) {
		this.oid = value;
	}
	
	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重複序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重複序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}
	
	/** 取得a-Loan客戶id+檢核碼 **/
	public String getALcustId() {
		return this.ALcustId;
	}
	/** 設定a-Loan客戶id+檢核碼 **/
	public void setALcustId(String value) {
		this.ALcustId = value;
	}

	/** 
	 * 取得編製單位代號<p/>
	 * 評等單位/編製單位
	 */
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/**
	 *  設定編製單位代號<p/>
	 *  評等單位/編製單位
	 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 
	 * 取得目前文件狀態<p/>
	 * 編製中=01O<br/>
	 *  待覆核=02O<br/>
	 *  已核准=05O
	 */
	public String getDocStatus() {
		return this.docStatus;
	}
	/**
	 *  設定目前文件狀態<p/>
	 *  編製中=01O<br/>
	 *  待覆核=02O<br/>
	 *  已核准=05O
	 **/
	public void setDocStatus(String value) {
		this.docStatus = value;
	}
	
	@SuppressWarnings("rawtypes")
	public void setDocStatus(Enum docStatusEnum) {
		this.docStatus = docStatusEnum.toString();
	}


	/** 
	 * 取得文件URL<p/>
	 * CLS:國內個金<br/>
	 *  LMS:國內企金<br/>
	 *  NULL:海外案件
	 */
	public String getDocURL() {
		return this.docURL;
	}
	/**
	 *  設定文件URL<p/>
	 *  CLS:國內個金<br/>
	 *  LMS:國內企金<br/>
	 *  NULL:海外案件
	 **/
	public void setDocURL(String value) {
		this.docURL = value;
	}

	/** 
	 * 取得(原) 建立人員號碼<p/>
	 * 原LNF013紀錄呈主管覆核人LNF013_CRE_SUPVNO
	 */
	public String getOrig_creator() {
		return this.orig_creator;
	}
	/**
	 *  設定(原) 建立人員號碼<p/>
	 *  原LNF013紀錄呈主管覆核人LNF013_CRE_SUPVNO
	 **/
	public void setOrig_creator(String value) {
		this.orig_creator = value;
	}

	/** 
	 * 取得建立人員號碼<p/>
	 * LNF013_CRE_SUPVNO
	 */
	public String getCreator() {
		return this.creator;
	}
	/**
	 *  設定建立人員號碼<p/>
	 *  LNF013_CRE_SUPVNO
	 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 
	 * 取得(原) 核准人員號碼<p/>
	 * 原LNF013紀錄之覆核主管LNF013_CRE_SUPVNO
	 */
	public String getOrig_approver() {
		return this.orig_approver;
	}
	/**
	 *  設定(原) 核准人員號碼<p/>
	 *  原LNF013紀錄之覆核主管LNF013_CRE_SUPVNO
	 **/
	public void setOrig_approver(String value) {
		this.orig_approver = value;
	}

	/** 
	 * 取得核准人員號碼<p/>
	 * LNF013_CRE_SUPVNO
	 */
	public String getApprover() {
		return this.approver;
	}
	/**
	 *  設定核准人員號碼<p/>
	 *  LNF013_CRE_SUPVNO
	 **/
	public void setApprover(String value) {
		this.approver = value;
	}

	/** 
	 * 取得核准日期<p/>
	 * sendDateTime
	 */
	public Timestamp getApproveTime() {
		return this.approveTime;
	}
	/**
	 *  設定核准日期<p/>
	 *  sendDateTime
	 **/
	public void setApproveTime(Timestamp value) {
		this.approveTime = value;
	}

	/** 取得邏輯刪除日期 **/
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/** 設定邏輯刪除日期 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 
	 * 取得修改前_消金帳務管理員行編<p/>
	 * 修改前LNF013_STAFF_NO
	 */
	public String getOrig_AOId() {
		return this.orig_AOId;
	}
	/**
	 *  設定修改前_消金帳務管理員行編<p/>
	 *  修改前LNF013_STAFF_NO
	 **/
	public void setOrig_AOId(String value) {
		this.orig_AOId = value;
	}

	/** 
	 * 取得修改後_消金帳務管理員行編<p/>
	 * LNF013_STAFF_NO
	 */
	public String getAOId() {
		return this.AOId;
	}
	/**
	 *  設定修改後_消金帳務管理員行編<p/>
	 *  LNF013_STAFF_NO
	 **/
	public void setAOId(String value) {
		this.AOId = value;
	}

	/** 取得備註 **/
	public String getMemo() {
		return this.memo;
	}
	/** 設定備註 **/
	public void setMemo(String value) {
		this.memo = value;
	}
}
