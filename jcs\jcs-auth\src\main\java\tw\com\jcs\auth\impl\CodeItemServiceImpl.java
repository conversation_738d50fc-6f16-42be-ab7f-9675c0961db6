package tw.com.jcs.auth.impl;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.springframework.jdbc.core.RowCallbackHandler;

import tw.com.jcs.auth.AuthQueryFactory;
import tw.com.jcs.auth.CodeItemService;
import tw.com.jcs.auth.MemberService;
import tw.com.jcs.auth.model.CodeItem;
import tw.com.jcs.auth.model.impl.CodeItemImpl;
import tw.com.jcs.auth.util.StringUtil;

/**
 * <pre>
 * 使用者角色權限
 * </pre>
 * 
 * @since 2022年12月21日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月21日
 *          </ul>
 */
public class CodeItemServiceImpl implements CodeItemService {

    private static final int NO_PARENT = -1;
    private AuthQueryFactory queryFactory;
    private MemberService memberService;

    private Map<Integer, CodeItem> codes = new ConcurrentHashMap<Integer, CodeItem>();
    private Map<String, Set<Integer>> roleSteps = new ConcurrentHashMap<String, Set<Integer>>();
    private Map<String, List<CodeItem>> roleStepCodes = new ConcurrentHashMap<String, List<CodeItem>>();
    private Map<String, List<CodeItem>> urlCodes = new ConcurrentHashMap<String, List<CodeItem>>();

    private String systemType;

    /**
     * constructor
     */
    public CodeItemServiceImpl() {
        super();
    }

    @Resource
    public void setQueryFactory(AuthQueryFactory queryFactory) {
        this.queryFactory = queryFactory;
    }

    @Resource
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    /**
     * 初始化參數
     */
    @PostConstruct
    public void init() {
        codes.clear();
        roleSteps.clear();
        roleStepCodes.clear();
        urlCodes.clear();

        initCode();
        initCodeRelation();
        initSystemType();
    }

    /**
     * 初始化參數
     */
    void initCode() {
        queryFactory.execCodeQuery(new RowCallbackHandler() {
            public void processRow(ResultSet rs) throws SQLException {
                CodeItemImpl code = new CodeItemImpl();
                code.setCode(rs.getInt("CODE"));
                code.setStep(rs.getInt("STEP"));
                code.setSeq(rs.getInt("SEQ"));
                code.setParent(rs.getInt("PARENT"));
                code.setName(rs.getString("NAME"));
                code.setPath(rs.getString("PATH")); // add
                code.setDesc(rs.getString("DESC"));
                code.setDocid(rs.getString("DOCID")); // add by fantasy
                                                      // 2012/04/13

                codes.put(code.getCode(), code);

                String key = code.getPath();
                if (key != null) {
                    List<CodeItem> cis = urlCodes.get(key);
                    if (cis == null) {
                        cis = new LinkedList<CodeItem>();
                    }
                    cis.add(code);
                    urlCodes.put(key, cis);
                }
            }
        });
    }

    /**
     * 初始化參數
     */
    void initCodeRelation() {
        Set<Entry<String, Map<Integer, Integer>>> roleAuthes = null;

        // this step depends on the specfic implementation of MemberService
        if (memberService instanceof MemberServiceImpl) {
            MemberServiceImpl service = (MemberServiceImpl) memberService;
            roleAuthes = service.roleAuthes.entrySet();
        } else {
            MemberServiceImpl _service = new MemberServiceImpl();
            _service.setQueryFactory(queryFactory);
            _service.initRoleAuthes();
            roleAuthes = _service.roleAuthes.entrySet();
        }

        for (Entry<String, Map<Integer, Integer>> entry : roleAuthes) {
            String role = entry.getKey();
            Set<Integer> steps = roleSteps.get(role);
            if (steps == null) {
                steps = new HashSet<Integer>();
            }
            for (Integer auth : entry.getValue().keySet()) {
                CodeItem code = codes.get(auth);
                if (code == null)
                    continue;
                String key = getRoleStepKey(role, code.getStep());
                List<CodeItem> stepCodes = roleStepCodes.get(key);
                if (stepCodes == null) {
                    stepCodes = new LinkedList<CodeItem>();
                    roleStepCodes.put(key, stepCodes);
                }
                stepCodes.add(code);
                steps.add(code.getStep());
            }
            roleSteps.put(role, steps);
        }
    }

    /**
     * 
     * 
     * @param role
     *            角色
     * @param step
     *            階段
     * @return 角色_階段
     */
    private String getRoleStepKey(String role, int step) {
        return role + "_" + step;
    }

    /**
     * 已棄用
     * 
     * @param pgmDept
     *            指定部門
     * @param userId
     *            使用者代號
     * @param parent
     * @param steps
     *            階段
     * @return
     */
    @SuppressWarnings("deprecation")
    public List<CodeItem> findByParentAndSteps(String pgmDept, String userId, int parent, int... steps) {
        // copy from initCodeRelation
        Map<String, String> roleAuthDepts = null;
        // this step depends on the specfic implementation of MemberService
        if (memberService instanceof MemberServiceImpl) {
            roleAuthDepts = ((MemberServiceImpl) memberService).getPGMDepts();
        } else {
            MemberServiceImpl _service = new MemberServiceImpl();
            _service.setQueryFactory(queryFactory);
            _service.initRoleAuthes();
            roleAuthDepts = ((MemberServiceImpl) _service).getPGMDepts();
        }

        Set<Integer> pSet = new HashSet<Integer>();
        pSet.add(parent);

        Set<CodeItem> set = new HashSet<CodeItem>();
        Set<String> roles = memberService.getRolesByUser(userId);
        Arrays.sort(steps);

        for (String role : roles) {
            for (int step : steps) {
                String key = getRoleStepKey(role, step);
                List<CodeItem> stepCodes = roleStepCodes.get(key);
                if (stepCodes == null) {
                    continue;
                } else if (parent == NO_PARENT) {
                    set.addAll(stepCodes);
                    for (CodeItem code : stepCodes) {
                        // ************************
                        if (!isPGMDeptCheckOK(roleAuthDepts, role, code.getCode(), pgmDept)) {
                            continue;
                        }
                        // ************************
                        pSet.add(code.getCode());
                    }
                } else {
                    for (CodeItem code : stepCodes) {
                        if (pSet.contains(code.getParent())) {
                            // ************************
                            if (!isPGMDeptCheckOK(roleAuthDepts, role, code.getCode(), pgmDept)) {
                                continue;
                            }
                            // ************************
                            set.add(code);
                            pSet.add(code.getCode());
                        }
                    }
                }
            }
        }
        return Arrays.asList(set.toArray(new CodeItem[set.size()]));
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.CodeItemService#findByStep(java.lang.String, java.util.Set, int[])
     */
    @Override
    public List<CodeItem> findByStep(String pgmDept, Set<String> roles, int... step) {
        return findByParentAndSteps(pgmDept, roles, NO_PARENT, step);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.CodeItemService#findByParent(java.lang.String, java.util.Set, int)
     */
    @Override
    public List<CodeItem> findByParent(String pgmDept, Set<String> roles, int parent) {
        int step = codes.get(parent).getStep() + 1;
        return findByParentAndSteps(pgmDept, roles, parent, new int[] { step });
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.CodeItemService#findByParentAndSteps(java.lang.String, java.util.Set, int, int[])
     */
    @Override
    public List<CodeItem> findByParentAndSteps(String pgmDept, Set<String> roles, int parent, int... steps) {

        // copy from initCodeRelation
        Map<String, String> roleAuthDepts = null;
        // this step depends on the specfic implementation of MemberService
        if (memberService instanceof MemberServiceImpl) {
            roleAuthDepts = ((MemberServiceImpl) memberService).getPGMDepts();
        } else {
            MemberServiceImpl _service = new MemberServiceImpl();
            _service.setQueryFactory(queryFactory);
            _service.initRoleAuthes();
            roleAuthDepts = ((MemberServiceImpl) _service).getPGMDepts();
        }

        Set<Integer> pSet = new HashSet<Integer>();
        pSet.add(parent);

        Set<CodeItem> set = new HashSet<CodeItem>();
        if (roles == null) {
            roles = Collections.emptySet();
        }
        Arrays.sort(steps);

        for (String role : roles) {
            for (int step : steps) {
                String key = getRoleStepKey(role, step);
                List<CodeItem> stepCodes = roleStepCodes.get(key);

                if (stepCodes == null) {
                    continue;
                } else if (parent == NO_PARENT) {
                    set.addAll(stepCodes);
                    for (CodeItem code : stepCodes) {
                        // ************************
                        if (!isPGMDeptCheckOK(roleAuthDepts, role, code.getCode(), pgmDept)) {
                            continue;
                        }
                        // ************************
                        pSet.add(code.getCode());

                    }
                } else {
                    for (CodeItem code : stepCodes) {
                        // ************************
                        if (!isPGMDeptCheckOK(roleAuthDepts, role, code.getCode(), pgmDept)) {
                            continue;
                        }
                        // ************************
                        if (pSet.contains(code.getParent())) {
                            set.add(code);
                            pSet.add(code.getCode());
                        }
                    }
                }
            }
        }
        return Arrays.asList(set.toArray(new CodeItem[set.size()]));
    }

    /**
     * 角色代號_功能項目代號是否有指定部門,若有指定需要判斷是否登錄分行有在部門清單用
     * 
     * @param roleAuthDepts
     * @param rolecode
     *            角色代號
     * @param pgmcode
     *            功能項目代號
     * @param pgmDept
     *            指定部門
     * @return
     */
    private boolean isPGMDeptCheckOK(Map<String, String> roleAuthDepts, String rolecode, int pgmcode, String pgmDept) {
        String key = rolecode + "_" + pgmcode;
        String deptList = StringUtil.trim(roleAuthDepts.get(key));

        // add by fantasy 2012/04/19 排除null
        if (deptList == null)
            return true;
        if ("null".equals(StringUtil.trim(deptList).toLowerCase()))
            return true;
        if (pgmDept == null)
            return true;

        boolean isok = ("".equals(deptList) || deptList.indexOf(pgmDept) != -1);

        return isok;
    }

    /**
     * 初始化參數
     */
    void initSystemType() {
        this.systemType = queryFactory.getSystemType();
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.CodeItemService#getSystemType()
     */
    @Override
    public String getSystemType() {
        return systemType;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.CodeItemService#getUrlPathByCode(int)
     */
    @Override
    public String getUrlPathByCode(int code) {
        CodeItem ci = codes.get(code);
        if (ci == null)
            return "";
        return ci.getPath();
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.CodeItemService#getCodeItemByCode(int)
     */
    @Override
    public CodeItem getCodeItemByCode(int code) {
        return codes.get(code);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.CodeItemService#getCodeItemByCodes(int[])
     */
    @Override
    public List<CodeItem> getCodeItemByCodes(int... _codes) {
        Set<CodeItem> set = new HashSet<CodeItem>();
        Arrays.sort(_codes);

        for (int code : _codes) {
            CodeItem ci = codes.get(code);
            set.add(ci);
        }
        return Arrays.asList(set.toArray(new CodeItem[set.size()]));
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.CodeItemService#getCodeItemByURL(java.lang.String[])
     */
    @Override
    public List<CodeItem> getCodeItemByURL(String... urls) {
        Set<CodeItem> set = new HashSet<CodeItem>();

        for (String url : urls) {
            if (url != null) {
                List<CodeItem> cis = urlCodes.get(url);
                if (cis != null) {
                    for (int i = 0; i < cis.size(); i++) {
                        CodeItem ci = cis.get(i);
                        if (!set.contains(ci)) {
                            set.add(cis.get(i));
                        }
                    }
                }
            }
        }
        return Arrays.asList(set.toArray(new CodeItem[set.size()]));
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.CodeItemService#getParentNameByCode(int)
     */
    @Override
    public String getParentNameByCode(int code) {
        String result = "";
        CodeItem ci = getCodeItemByCode(code);
        if (ci != null) {
            int currStep = ci.getStep();
            if (currStep > 2) {
                int[] steps = new int[currStep - 2];
                for (int i = 0; i < currStep - 2; i++) {
                    steps[i] = i + 2;
                }
                result = getParentNameByCode(code, steps);
            } else {
                result = ci.getName();
            }
        }
        return result;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.CodeItemService#getParentNameByCode(int, int[])
     */
    @Override
    public String getParentNameByCode(int code, int... steps) {
        CodeItem ci = getCodeItemByCode(code);
        String resultName = "";
        if (ci != null) {
            int currStep = ci.getStep();
            int parent = ci.getParent();

            boolean result = false;
            for (int step : steps) {
                if (currStep == step)
                    result = true;
            }

            if (currStep != 0) {
                String parentName = getParentNameByCode(parent, steps);
                resultName = parentName + (result ? (!"".equals(parentName) ? "-" : "") + ci.getName() : "");
            }
        }

        return resultName;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.CodeItemService#getPgmNameByCode(int, java.lang.String)
     */
    @Override
    public String getPgmNameByCode(int code, String url) {
        String[] urls = url.split("/");
        String pgmName = "";
        int len = urls.length;
        if (len >= 3) {
            pgmName = StringUtil.trim(urls[len - 1]);
            if (pgmName.length() < 3) { // 2碼時為tab名稱,非程式名稱
                pgmName = StringUtil.trim(urls[len - 2]);
            }
        } else if (len > 0 && len < 3) {
            pgmName = StringUtil.trim(urls[len - 1]);
        }

        return pgmName.toUpperCase() + (!"".equals(pgmName) ? " " : "") + getParentNameByCode(code);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.CodeItemService#reload()
     */
    @Override
    public synchronized void reload() {
        this.init();
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.CodeItemService#getParentByCode(int)
     */
    @Override
    public String getParentByCode(int code) {
        CodeItem ci = getCodeItemByCode(code);
        return String.valueOf(ci.getParent() == 0 ? code : ci.getParent());
    }
}
