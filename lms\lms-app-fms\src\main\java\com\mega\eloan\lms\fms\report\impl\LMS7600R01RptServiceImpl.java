package com.mega.eloan.lms.fms.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.StringReader;
import java.math.BigDecimal;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.Html2Text;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.pages.LMS7600M01Page;
import com.mega.eloan.lms.fms.report.LMS7600R01RptService;
import com.mega.eloan.lms.fms.service.LMS7600Service;
import com.mega.eloan.lms.mfaloan.service.MisELF600Service;
import com.mega.eloan.lms.mfaloan.service.MisQuotapprService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L140MM4A;
import com.mega.eloan.lms.model.L140MM4B;
import com.mega.eloan.lms.model.L140MM4C;
import com.mega.sso.service.BranchService;

import jxl.Cell;
import jxl.CellType;
import jxl.DateCell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.WorkbookSettings;
import jxl.format.Alignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;
import tw.com.jcs.common.report.SubReportParam;

@Service("lms7600r01rptservice")
public class LMS7600R01RptServiceImpl implements LMS7600R01RptService,
		FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS7600R01RptServiceImpl.class);

	@Resource
	BranchService branch;

	@Resource
	LMSService lmsService;

	@Resource
	LMS7600Service lms7600Service;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	MisELF600Service misELF600Service;

	@Resource
	DocFileService fileService;

	@Resource
	MisQuotapprService misQuotapprService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			String mode = Util.trim(params.getString("mode"));
			if (Util.equals("xls", mode)) {
				baos = (ByteArrayOutputStream) this.genXls(params);
			} else if (Util.equals("compare", mode)) {
				baos = (ByteArrayOutputStream) this.genCompare(params);
			} else if (Util.equals("uploadSole", mode)) {
				baos = (ByteArrayOutputStream) this.uploadSole(params);
			} else if (Util.equals("uploadSoleX", mode)) {
				baos = (ByteArrayOutputStream) this.uploadSoleX(params);
			} else if (Util.equals("getLatestCol", mode)) {
				baos = (ByteArrayOutputStream) this.getLatestCol(params);
			} else {
				baos = (ByteArrayOutputStream) this.generateReport(params);
			}
			if (baos == null) {
				return null;
			} else {
				return baos.toByteArray();
			}
			// baos = (ByteArrayOutputStream) this.generateReport(params);
			// return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}

	@SuppressWarnings("unchecked")
	public OutputStream generateReport(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		LOGGER.info("into setReportData");
		Locale locale = null;
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		ReportGenerator generator = new ReportGenerator(
				"report/fms/LMS7600R01_" + locale.toString() + ".rpt");
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		OutputStream outputStream = null;

		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L140MM4A l140mm4a = null;
		List<L140MM4B> l140mm4blist = null;

		String branchName = null;
		try {
			l140mm4a = lms7600Service.findModelByOid(L140MM4A.class, mainOid);
			if (l140mm4a == null) {
				l140mm4a = new L140MM4A();
			}

			String apprid = "";
			String recheckid = "";
			String bossid = "";
			String managerid = "";
			l140mm4blist = (List<L140MM4B>) lms7600Service.findListByMainId(
					L140MM4B.class, mainId);
			if (!Util.isEmpty(l140mm4blist)
					&& Util.notEquals(l140mm4a.getDocStatus(),
							CreditDocStatusEnum.海外_編製中)) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (L140MM4B l140mm4b : l140mm4blist) {
					// 要加上人員代碼
					String type = Util.trim(l140mm4b.getStaffJob());
					String userId = Util.trim(l140mm4b.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) { // L1. 分行經辦
						apprid = userId + " " + value;
					} else if ("L3".equals(type)) { // L3. 分行授信主管
						bossId.append(bossId.length() > 0 ? "\r\n" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) { // L4. 分行覆核主管
						recheckid = userId + " " + value;
					} else if ("L5".equals(type)) { // L5. 經副襄理
						managerid = userId + " " + value;
					}
				}
				bossid = bossId.toString();
			}
			branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(l140mm4a.getOwnBrId())));
			String custId = Util.nullToSpace(l140mm4a.getCustId()) + " "
					+ Util.nullToSpace(l140mm4a.getDupNo());
			String custname = Util.nullToSpace(l140mm4a.getCustName());
			String cntrno = Util.nullToSpace(l140mm4a.getCntrNo());
			String updater = Util.trim(l140mm4a.getUpdater()) + " "
					+ Util.trim(lmsService.getUserName(l140mm4a.getUpdater()));
			String tDate = "";
			if (!"".equals(Util.trim(l140mm4a.getApproveTime()))) {
				tDate = Util.trim(l140mm4a.getUpdateTime()) + "("
						+ Util.trim(l140mm4a.getApproveTime()) + ")";
			} else {
				tDate = Util.trim(l140mm4a.getUpdateTime());
			}
			String qDate = CapDate.formatDate(l140mm4a.getQueryDate(),
					"yyyy-MM-dd");

			Map<String, String> current = new HashMap<String, String>();
			Map<String, String> last = new HashMap<String, String>();
			List<Map<String, String>> thisRows = new LinkedList<Map<String, String>>();
			List<Map<String, String>> lastRows = new LinkedList<Map<String, String>>();

			last.put("L140MM4A.QDATE", qDate);

			L140MM4C currentL140mm4c = lms7600Service
					.findCurrentL140mm4c(l140mm4a.getMainId());
			L140MM4C lastL140mm4c = lms7600Service.findLastL140mm4c(l140mm4a
					.getMainId());

			List<String> diffData = this.compareData(lastL140mm4c,
					currentL140mm4c);

			Map<String, CapAjaxFormResult> codeTypes = codetypeservice
					.findByCodeType(new String[] { "LandUse1", "LandUse21",
							"LandUse22", "cms1010_useKind1",
							"cms1010_useKind2", "cms1010_landkind",
							"lms7600_ctlType", "lms7600_elFlag",
							"lms7600_adoptFg", "lms7600_cstReason" });

			if (lastL140mm4c != null && Util.isNotEmpty(lastL140mm4c)) {
				Map<String, String> rowData = new LinkedHashMap<String, String>();
				String txt = this.convertL140MM4cString(lastL140mm4c,
						codeTypes, false, diffData);
				rowData.put("ReportBean.column01", txt);
				lastRows.add(rowData);
			} else {
				Map<String, String> rowData = new LinkedHashMap<String, String>();
				rowData.put("ReportBean.column01", "");
				lastRows.add(rowData);
			}

			if (currentL140mm4c != null && Util.isNotEmpty(currentL140mm4c)) {
				Map<String, String> rowData = new LinkedHashMap<String, String>();
				String txt = this.convertL140MM4cString(currentL140mm4c,
						codeTypes, true, diffData);
				rowData.put("ReportBean.column01", txt);
				thisRows.add(rowData);
			} else {
				Map<String, String> rowData = new LinkedHashMap<String, String>();
				rowData.put("ReportBean.column01", "");
				thisRows.add(rowData);
			}

			// 子報表設定
			SubReportParam subReportParam = new SubReportParam();
			subReportParam.setData(0, last, lastRows);
			subReportParam.setData(1, current, thisRows);
			generator.setSubReportParam(subReportParam);

			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap.put("L140MM4A.Mainid",
					Util.trim(l140mm4a.getMainId()));
			rptVariableMap.put("L140MM4A.CUSTID", custId);
			rptVariableMap.put("L140MM4A.CUSTNAME", custname);
			rptVariableMap.put("L140MM4A.CNTRNO", cntrno);
			rptVariableMap.put("L140MM4A.UPDATER", updater);
			rptVariableMap.put("L140MM4B.APPRID", apprid);
			rptVariableMap.put("L140MM4B.RECHECKID", recheckid);
			rptVariableMap.put("L140MM4B.BOSSID", bossid);
			rptVariableMap.put("L140MM4B.MANAGERID", managerid);
			rptVariableMap.put("L140MM4A.DATE", tDate);
			rptVariableMap.put("L140MM4A.RANDOMCODE",
					Util.trim(l140mm4a.getRandomCode()));

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setLang(locale);

			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	private List<String> compareData(L140MM4C lastL140mm4c,
			L140MM4C currentL140mm4c) {
		List<String> cList = new ArrayList<String>();

		if (Util.notEquals(Util.trim(lastL140mm4c.getContract()),
				Util.trim(currentL140mm4c.getContract()))) {
			cList.add("cntrNo");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getUse_Date()),
				Util.trim(currentL140mm4c.getUse_Date()))) {
			cList.add("useDate");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getUseCd()),
				Util.trim(currentL140mm4c.getUseCd()))) {
			cList.add("useCd");
			cList.add("useType");
		}
		if (!cList.contains("useCd")
				&& Util.notEquals(Util.trim(lastL140mm4c.getContract()),
						Util.trim(currentL140mm4c.getContract()))) {
			cList.add("useType");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getLandType()),
				Util.trim(currentL140mm4c.getLandType()))) {
			cList.add("landType");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getLandKind()),
				Util.trim(currentL140mm4c.getLandKind()))) {
			cList.add("landKind");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getIdleLand()),
				Util.trim(currentL140mm4c.getIdleLand()))) {
			cList.add("idleLand");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getCtlType()),
				Util.trim(currentL140mm4c.getCtlType()))) {
			cList.add("ctlType");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getFstDate()),
				Util.trim(currentL140mm4c.getFstDate()))) {
			cList.add("fstDate");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getLstDate()),
				Util.trim(currentL140mm4c.getLstDate()))) {
			cList.add("lstDate");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getRrmoveDate()),
				Util.trim(currentL140mm4c.getRrmoveDate()))) {
			cList.add("removeDt");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getElFlag()),
				Util.trim(currentL140mm4c.getElFlag()))) {
			cList.add("elFlag");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getEndDate()),
				Util.trim(currentL140mm4c.getEndDate()))) {
			cList.add("endDate");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getDocumentNo()),
				Util.trim(currentL140mm4c.getDocumentNo()))) {
			cList.add("documentNo");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getCstDate()),
				Util.trim(currentL140mm4c.getCstDate()))) {
			cList.add("cstDate");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getAdoptFg()),
				Util.trim(currentL140mm4c.getAdoptFg()))) {
			cList.add("adoptFg");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getRateAdd()),
				Util.trim(currentL140mm4c.getRateAdd()))) {
			cList.add("rateAdd");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getCustRoa()),
				Util.trim(currentL140mm4c.getCustRoa()))) {
			cList.add("custRoa");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getRelRoa()),
				Util.trim(currentL140mm4c.getRelRoa()))) {
			cList.add("relRoa");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getCstReason()),
				Util.trim(currentL140mm4c.getCstReason()))) {
			cList.add("cstReason");
		}
		if (Util.notEquals(Util.trim(lastL140mm4c.getIsLegal()),
				Util.trim(currentL140mm4c.getIsLegal()))) {
			cList.add("isLegal");
		}

		return cList;
	}

	private String convertL140MM4cString(L140MM4C data,
			Map<String, CapAjaxFormResult> codeTypes, boolean needCheck,
			List<String> compareData) {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7600M01Page.class);

		StringBuffer sb = new StringBuffer();

		if (needCheck && compareData.contains("cntrNo")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.cntrNo")).append("：")
				.append(Util.trim(data.getContract()));
		sb.append("<br/>");
		if (needCheck && compareData.contains("useDate")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.useDate")).append("：")
				.append(CapDate.formatDate(data.getUse_Date(), "yyyy-MM-dd"));
		sb.append("<br/>");
		if (needCheck && compareData.contains("useCd")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.useCd"))
				.append("：")
				.append(Util.trim(codeTypes.get("LandUse1").get(
						Util.trim(data.getUseCd()))));
		sb.append("<br/>");
		if (needCheck && compareData.contains("useType")) {
			sb.append("<font color='red'>＊</font>");
		}
		String useType = (Util.equals("1", data.getUseCd()) ? "LandUse21"
				: "LandUse22");
		sb.append(prop.getProperty("L140MM4A.useType"))
				.append("：")
				.append(Util.trim(codeTypes.get(useType).get(
						Util.trim(data.getUseType()))));
		sb.append("<br/>");
		if (needCheck && compareData.contains("landType")) {
			sb.append("<font color='red'>＊</font>");
		}
		String landType = (Util.equals("1", data.getUseCd()) ? "cms1010_useKind1"
				: "cms1010_useKind2");
		sb.append(prop.getProperty("L140MM4A.landType"))
				.append("：")
				.append(Util.trim(codeTypes.get(landType).get(
						Util.trim(data.getLandType()))));
		sb.append("<br/>");
		if (needCheck && compareData.contains("landKind")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.landKind"))
				.append("：")
				.append(Util.trim(codeTypes.get("cms1010_landkind").get(
						Util.trim(data.getLandKind()))));
		sb.append("<br/>");
		if (needCheck && compareData.contains("idleLand")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.idleLand"))
				.append("：")
				.append((Util.isNotEmpty(Util.trim(data.getIdleLand())) ? prop
						.getProperty(Util.trim(data.getIdleLand())) : ""));
		sb.append("<br/>");
		if (needCheck && compareData.contains("ctlType")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.ctlType"))
				.append("：")
				.append(Util.trim(codeTypes.get("lms7600_ctlType").get(
						Util.trim(data.getCtlType()))));
		sb.append("<br/>");
		if (needCheck && compareData.contains("fstDate")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.fstDate")).append("：")
				.append(CapDate.formatDate(data.getFstDate(), "yyyy-MM-dd"));
		sb.append("<br/>");
		if (needCheck && compareData.contains("lstDate")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.lstDate")).append("：")
				.append(CapDate.formatDate(data.getLstDate(), "yyyy-MM-dd"));
		sb.append("<br/>");
		if (needCheck && compareData.contains("removeDt")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.removeDt")).append("：")
				.append(CapDate.formatDate(data.getRrmoveDate(), "yyyy-MM-dd"));
		sb.append("<br/>");
		if (needCheck && compareData.contains("elFlag")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.elFlag"))
				.append("：")
				.append(Util.trim(codeTypes.get("lms7600_elFlag").get(
						Util.trim(data.getElFlag()))));
		sb.append("<br/>");
		if (needCheck && compareData.contains("endDate")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.endDate")).append("：")
				.append(CapDate.formatDate(data.getEndDate(), "yyyy-MM-dd"));
		sb.append("<br/>");
		if (needCheck && compareData.contains("documentNo")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.documentNo")).append("：")
				.append(Util.trim(data.getDocumentNo()));
		sb.append("<br/>");
		if (needCheck && compareData.contains("cstDate")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.cstDate")).append("：")
				.append(CapDate.formatDate(data.getCstDate(), "yyyy-MM-dd"));
		sb.append("<br/>");
		if (needCheck && compareData.contains("adoptFg")) {
			sb.append("<font color='red'>＊</font>");
		}
		String[] adoptFg = StringUtils.split(data.getAdoptFg(), "|");
		String sb_adoptFg = "";
		if (adoptFg.length > 0) {
			for (String i : adoptFg) {
				sb_adoptFg += (Util.isEmpty(sb_adoptFg) ? "" : "、");
				sb_adoptFg = sb_adoptFg
						+ Util.trim(codeTypes.get("lms7600_adoptFg").get(i));
			}
		}
		sb.append(prop.getProperty("L140MM4A.adoptFg")).append("：")
				.append(sb_adoptFg);
		sb.append("<br/>");
		if (needCheck && compareData.contains("rateAdd")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.rateAdd")).append("：")
				.append(Util.trim(data.getRateAdd())).append("%");
		sb.append("<br/>");
		if (needCheck && compareData.contains("custRoa")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.custRoa")).append("：")
				.append(Util.trim(data.getCustRoa())).append("%");
		sb.append("<br/>");
		if (needCheck && compareData.contains("relRoa")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.relRoa")).append("：")
				.append(Util.trim(data.getRelRoa())).append("%");
		sb.append("<br/>");
		if (needCheck && compareData.contains("cstReason")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.cstReason"))
				.append("：")
				.append(Util.trim(codeTypes.get("lms7600_cstReason").get(
						Util.trim(data.getCstReason()))));
		sb.append("<br/>");
		if (needCheck && compareData.contains("isLegal")) {
			sb.append("<font color='red'>＊</font>");
		}
		sb.append(prop.getProperty("L140MM4A.isLegal")).append("：")
				.append(Util.trim(this.showYNPic(data.getIsLegal(), prop, 2)));

		return sb.toString();
	}

	/**
	 * @param type
	 *            Y=是 N=否 X=空白 A=兩種都有
	 * @param prop
	 *            prop
	 * @return string
	 */
	private String showYNPic(String type, Properties prop, int typeSize) {
		StringBuffer str = new StringBuffer();
		if ("Y".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("yes")); // 是
		if ("N".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("no")); // 否

		if (typeSize > 2) {
			if ("A".equals(type)) {
				str.append("■");
			} else {
				str.append("□");
			}
			str.append(prop.getProperty("both")); // 兩種都有
		}

		return str.toString();
	}

	public ByteArrayOutputStream genXls(PageParameters params)
			throws IOException, Exception, WriteException {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7600M01Page.class);

		LOGGER.info("genXls START=========");
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		Locale locale = null;
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();

		Map<String, CapAjaxFormResult> codeTypes = codetypeservice
				.findByCodeType(new String[] { "LandUse1", "LandUse21",
						"LandUse22", "cms1010_useKind1", "cms1010_useKind2",
						"cms1010_landkind", "lms7600_ctlType",
						"lms7600_elFlag", "lms7600_adoptFg",
						"lms7600_cstReason" });

		// -------
		// 資料
		List<String[]> rows = new ArrayList<String[]>();
		int intput_col_size = 30;
		List<Map<String, Object>> list = misELF600Service.getAll();
		if (list.size() > 0) {
			for (Map<String, Object> map : list) {
				String[] arr = new String[intput_col_size];
				for (int i = 0; i < intput_col_size; i++) {
					arr[i] = "";
				}

				boolean isControlByCentralBank = this
						.isControlByCentralBank(map);
				arr[0] = Util.trim(MapUtils.getString(map, "ELF600_CUST_ID"));
				arr[1] = Util.trim(MapUtils.getString(map, "ELF600_CUST_DUP"));
				arr[2] = Util.trim(MapUtils.getString(map, "ELF600_CONTRACT"));
				arr[3] = Util.trim(TWNDate.toAD((Date) MapUtils.getObject(map,
						"ELF600_USE_DATE")));
				String useCd = Util.trim(MapUtils
						.getString(map, "ELF600_USECD"));
				arr[4] = Util.trim(codeTypes.get("LandUse1").get(useCd))
						.toString();
				String useType = (Util.equals("1", useCd) ? "LandUse21"
						: "LandUse22");
				Object elf600UseType = codeTypes.get(useType).get(
						Util.trim(MapUtils.getString(map, "ELF600_USETYPE")));
				arr[5] = elf600UseType == null ? "" : elf600UseType.toString();
				String landType = (Util.equals("1", useCd) ? "cms1010_useKind1"
						: "cms1010_useKind2");
				Object elf600LandType = codeTypes.get(landType).get(
						Util.trim(MapUtils.getString(map, "ELF600_LANDTYPE")));
				arr[6] = elf600LandType == null ? "" : elf600LandType
						.toString();
				Object elf600LandKind = codeTypes.get("cms1010_landkind").get(
						Util.trim(MapUtils.getString(map, "ELF600_LANDKIND")));
				arr[7] = elf600LandKind == null ? "" : elf600LandKind
						.toString();
				String idleLand = Util.trim(MapUtils.getString(map,
						"ELF600_IDLELAND"));
				arr[8] = (Util.isNotEmpty(Util.trim(idleLand)) ? prop
						.getProperty(Util.trim(idleLand)) : "");
				arr[9] = codeTypes
						.get("lms7600_ctlType")
						.get(Util.trim(MapUtils
								.getString(map, "ELF600_CTLTYPE"))).toString();
				arr[10] = Util.trim(TWNDate.toAD((Date) MapUtils.getObject(map,
						"ELF600_FSTDATE")));
				arr[11] = Util.trim(TWNDate.toAD((Date) MapUtils.getObject(map,
						"ELF600_LSTDATE")));
				arr[12] = Util.trim(TWNDate.toAD((Date) MapUtils.getObject(map,
						"ELF600_RRMOVEDT")));
				String elFlag = Util.trim(MapUtils.getString(map,
						"ELF600_ELFLAG"));
				arr[13] = Util.isNotEmpty(elFlag) ? codeTypes
						.get("lms7600_elFlag").get(elFlag).toString() : "";
				arr[14] = Util.trim(TWNDate.toAD((Date) MapUtils.getObject(map,
						"ELF600_ENDDATE")));
				arr[15] = Util.trim(MapUtils
						.getString(map, "ELF600_DOCUMENTNO"));
				arr[16] = Util.trim(TWNDate.toAD((Date) MapUtils.getObject(map,
						"ELF600_CSTDATE")));
				String[] adoptFg = StringUtils.split(
						Util.trim(MapUtils.getString(map, "ELF600_ADOPTFG")),
						"|");
				String sb_adoptFg = "";
				if (adoptFg.length > 0) {
					for (String i : adoptFg) {
						sb_adoptFg += (Util.isEmpty(sb_adoptFg) ? "" : "、");
						sb_adoptFg = sb_adoptFg
								+ Util.trim(codeTypes.get("lms7600_adoptFg")
										.get(i));
					}
				}
				arr[17] = sb_adoptFg;
				arr[18] = getBigDecimal(map, "ELF600_RATEADD");
				arr[19] = getBigDecimal(map, "ELF600_CUSTROA");
				arr[20] = getBigDecimal(map, "ELF600_RELROA");
				String cstReason = Util.trim(MapUtils.getString(map,
						"ELF600_CSTREASON"));
				arr[21] = Util.isNotEmpty(cstReason) ? codeTypes
						.get("lms7600_cstReason").get(cstReason).toString()
						: "";
				arr[22] = Util.trim(MapUtils.getString(map, "ELF600_ISLEGAL"));
				arr[23] = Util.trim(MapUtils.getString(map, "ELF600_UPDATER"));
				arr[24] = Util.trim(MapUtils.getString(map, "ELF600_TMESTAMP"));
				arr[25] = Util.trim(MapUtils.getString(map,
						"ELF600_UNIVERSAL_ID"));
				arr[26] = Util.trim(MapUtils.getString(map, "ELF600_UPDFROM"));
				arr[27] = getBigDecimal(map, "ELF600_LOAN_BAL");
				arr[28] = isControlByCentralBank ? "V" : "";
				arr[29] = map.get("ELF600_ACT_ST_DATE") == null 
						? "" 
						: CapDate.formatDate((Date)map.get("ELF600_ACT_ST_DATE"), UtilConstants.DateFormat.YYYY_MM_DD);
				rows.add(arr);
			}
		}
		// -------

		WritableWorkbook wBook = null;
		Workbook src_workbook = null;
		String path = PropUtil.getProperty("loadFile.dir")
				+ "excel/LMS7600.xls";
		URL urlRpt = null;
		urlRpt = Thread.currentThread().getContextClassLoader()
				.getResource(path);
		if (urlRpt == null)
			throw new IOException("get File fail");

		try {
			src_workbook = Workbook.getWorkbook(new File(urlRpt.toURI()));
		} catch (Exception e) {
		}
		WorkbookSettings settings = new WorkbookSettings();
		settings.setWriteAccess(null);
		wBook = Workbook.createWorkbook(outputStream, src_workbook, settings);
		if (src_workbook != null) {
			src_workbook.close();
		}

		if (true) {
			WritableFont def_font = new WritableFont(WritableFont.ARIAL, 10);
			WritableCellFormat cellFormatL = new WritableCellFormat(def_font);
			{
				cellFormatL.setAlignment(Alignment.LEFT);
				cellFormatL.setWrap(false);
			}
			WritableCellFormat cellFormatR = new WritableCellFormat(def_font);
			{
				cellFormatR.setAlignment(Alignment.RIGHT);
				cellFormatR.setWrap(false);
			}
			if (true) {
				WritableSheet sheet1 = wBook.getSheet(0);
				int i = 0;
				int headerCnt = 1;

				for (String[] arr : rows) {
					int colLen = arr.length;
					int rowIdx = (i + headerCnt);

					if (true) {

					}
					for (int i_col = 0; i_col < colLen; i_col++) {
						// 數值欄位(向右對齊)
						boolean alignRight = (i_col == 19 || i_col == 20
								|| i_col == 21 || i_col == 27);
						sheet1.addCell(new Label(i_col, rowIdx, arr[i_col],
								alignRight ? cellFormatR : cellFormatL));
					}
					i++;
				}
			}
		}
		wBook.write();
		wBook.close();

		if (outputStream != null) {
			outputStream.flush();
		}
		return outputStream;
	}

	private String getBigDecimal(Map<String, Object> map, String key) {
		Object d = MapUtils.getObject(map, key);
		if (d == null) {
			return "";
		} else if (d instanceof Double) {

			return Util.trim(LMSUtil.pretty_numStr(new BigDecimal((Double) d)));
		} else if (d instanceof Long) {

			return Util.trim(LMSUtil.pretty_numStr(new BigDecimal((Long) d)));
		} else {

			return Util.trim(MapUtils.getString(map, key));
		}
	}

	public ByteArrayOutputStream genCompare(PageParameters params)
			throws IOException, Exception, WriteException {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7600M01Page.class);

		LOGGER.info("genCompare START=========");
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		Locale locale = null;
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();

		// 產出比對結果XLS
		Workbook src_workbook = null;
		WritableWorkbook compareWB = null;
		WritableSheet compareSheet = null;
		File file = null;
		WritableFont font12 = null;
		WritableCellFormat formatFont = null;
		WritableCellFormat format12Right = null;
		WritableCellFormat format12Left = null;
		// String localFilePath = PropUtil.getProperty("docFile.dir")
		// + File.separator + PropUtil.getProperty("systemId")
		// + File.separator + "900" + File.separator;
		// File filePath = new File(localFilePath);
		// filePath.mkdirs();
		String path = PropUtil.getProperty("loadFile.dir")
				+ "excel/LMS7600.xls";// "excel/compareExl.xls";
		// file = new File(localFilePath + "compareExl.xls");
		// logger.info("filePath:{}", new Object[] { file.getAbsolutePath() });
		// String path = file.getAbsolutePath();
		URL urlRpt = null;
		urlRpt = Thread.currentThread().getContextClassLoader()
				.getResource(path);
		if (urlRpt == null)
			throw new IOException("get File fail");
		try {
			src_workbook = Workbook.getWorkbook(new File(urlRpt.toURI()));
		} catch (Exception e) {
		}
		WorkbookSettings settings = new WorkbookSettings();
		settings.setWriteAccess(null);
		compareWB = Workbook.createWorkbook(outputStream, src_workbook,
				settings);
		if (src_workbook != null) {
			src_workbook.close();
		}
		compareSheet = compareWB.createSheet("Sheet1", 0);
		font12 = new WritableFont(WritableFont.createFont("標楷體"), 12,
				WritableFont.BOLD);
		formatFont = LMSUtil
				.setCellFormat(formatFont, font12, Alignment.CENTRE);
		format12Right = LMSUtil.setCellFormat(format12Right, font12,
				Alignment.RIGHT);
		format12Left = LMSUtil.setCellFormat(format12Left, font12,
				Alignment.LEFT);

		this.setColumnTitle(compareSheet, formatFont);
		compareWB.removeSheet(1);
		List<Map<String, Object>> compareList = new ArrayList<Map<String, Object>>();

		String fileKey1 = params.getString("fileKey1"); // 基準
		String fileKey2 = params.getString("fileKey2"); // 比對
		DocFile df_1 = fileService.findByOidAndSysId(fileKey1, "LMS");
		DocFile df_2 = fileService.findByOidAndSysId(fileKey2, "LMS");
		File file_1 = null;
		File file_2 = null;
		InputStream is_1 = null;
		InputStream is_2 = null;
		Workbook workbook_1 = null;
		Workbook workbook_2 = null;
		file_1 = fileService.getRealFile(df_1);
		file_2 = fileService.getRealFile(df_2);
		is_1 = FileUtils.openInputStream(file_1);
		is_2 = FileUtils.openInputStream(file_2);
		workbook_1 = Workbook.getWorkbook(is_1);
		workbook_2 = Workbook.getWorkbook(is_2);
		Sheet sheet_1 = workbook_1.getSheet(1);
		Sheet sheet_2 = workbook_2.getSheet(1);
		int totalCol_1 = sheet_1.getColumns();
		if (totalCol_1 == 0) {
			throw new CapMessageException("匯入之【基準】名單EXCEL格式錯誤。", getClass());
		}
		int totalCol_2 = sheet_2.getColumns();
		if (totalCol_2 == 0) {
			throw new CapMessageException("匯入之【比對】名單EXCEL格式錯誤。", getClass());
		}

		// 讀取資料
		List<Map<String, Object>> list_1 = new ArrayList<Map<String, Object>>();
		Map<String, Integer> cntrnoMap_1 = new HashMap<String, Integer>();
		this.getXlsData(sheet_1, list_1, totalCol_1, cntrnoMap_1);
		List<Map<String, Object>> list_2 = new ArrayList<Map<String, Object>>();
		Map<String, Integer> cntrnoMap_2 = new HashMap<String, Integer>();
		this.getXlsData(sheet_2, list_2, totalCol_2, cntrnoMap_2);

		// 開始compare
		if (list_1 != null && !list_1.isEmpty()) {
			for (Map<String, Object> data_1 : list_1) {
				Map<String, Object> compareMap = new HashMap<String, Object>(
						data_1);
				String key_cntrno = Util.nullToSpace(data_1.get("cntrNo"));
				// 找到同額度序號 => 可能有異動
				boolean change = false;
				if (cntrnoMap_2.containsKey(key_cntrno)) {
					int list_2_no = cntrnoMap_2.get(key_cntrno);
					change = this.compareData(compareMap, data_1,
							list_2.get(list_2_no));
					if (change) {
						compareMap.put("change", "異動");
					} else {
						compareMap.put("change", "");
					}
				} else { // 沒找到 => 新增
					compareMap.put("change", "新增");
				}
				compareList.add(compareMap);
			}
		}
		if (list_2 != null && !list_2.isEmpty()) {
			for (Map<String, Object> data_2 : list_2) {
				Map<String, Object> compareMap = new HashMap<String, Object>(
						data_2);
				String key_cntrno = Util.nullToSpace(data_2.get("cntrNo"));
				if (!cntrnoMap_1.containsKey(key_cntrno)) {
					compareMap.put("change", "刪除");
					compareList.add(compareMap);
				}
			}
		}

		// 開始輸出 比對XLS
		int y = 0;
		for (Map<String, Object> map : compareList) {
			y++;
			compareSheet.addCell(new Label(0, y, Util.trim(map.get("id")),
					format12Left));
			compareSheet.addCell(new Label(1, y, Util.trim(map.get("dupNo")),
					formatFont));
			compareSheet.addCell(new Label(2, y, Util.trim(map.get("name")),
					format12Left));
			compareSheet.addCell(new Label(3, y, Util.trim(map.get("br")),
					formatFont));
			compareSheet.addCell(new Label(4, y, Util.trim(map.get("caseNo")),
					format12Left));
			compareSheet.addCell(new Label(5, y,
					Util.trim(map.get("caseDate")), formatFont));
			compareSheet.addCell(new Label(6, y, Util.trim(map.get("endDate")),
					formatFont));
			compareSheet.addCell(new Label(7, y, Util.trim(map.get("cntrNo")),
					formatFont));
			compareSheet.addCell(new Label(8, y, Util.trim(map.get("change")),
					formatFont));
			compareSheet.addCell(new Label(9, y, Util.trim(map.get("flag")),
					formatFont));
			compareSheet.addCell(new Label(10, y,
					Util.trim(map.get("loanType")), formatFont));
			compareSheet.addCell(new Label(11, y, Util.trim(map.get("rate")),
					format12Right));
			compareSheet.addCell(new Label(12, y, Util.trim(map.get("curr")),
					formatFont));
			compareSheet.addCell(new Label(13, y, Util.trim(map.get("amt")),
					format12Right));
			compareSheet.addCell(new Label(14, y,
					Util.trim(map.get("project")), formatFont));
			compareSheet.addCell(new Label(15, y,
					Util.trim(map.get("balCurr")), formatFont));
			compareSheet.addCell(new Label(16, y, Util.trim(map.get("balAmt")),
					format12Right));
			compareSheet.addCell(new Label(17, y, Util.trim(map.get("other")),
					formatFont));
			compareSheet.addCell(new Label(18, y, Util.trim(map.get("date")),
					formatFont));
			compareSheet.addCell(new Label(19, y, Util.trim(map.get("sb")),
					formatFont));
			compareSheet.addCell(new Label(20, y, Util.trim(map.get("sbRP")),
					formatFont));
			compareSheet.addCell(new Label(21, y, Util.trim(map.get("sbPP")),
					formatFont));
			compareSheet.addCell(new Label(22, y, Util.trim(map.get("sbCR")),
					formatFont));
			compareSheet.addCell(new Label(23, y, Util.trim(map.get("sbCS")),
					formatFont));
			compareSheet.addCell(new Label(24, y, Util.trim(map.get("sbBS")),
					formatFont));
			compareSheet.addCell(new Label(25, y, Util.trim(map.get("sbS")),
					formatFont));
		}

		compareWB.write();
		compareWB.close();
		// 刪除 DocFile df_1,df_2 setDeletedTime

		if (outputStream != null) {
			outputStream.flush();
		}
		return outputStream;
	}

	private String getContents(Cell cell) {
		DateCell dCell = null;
		if (cell.getType() == CellType.DATE) {
			dCell = (DateCell) cell;
			// System.out.println("Value of Date Cell is: " + dCell.getDate());
			// ==> Value of Date Cell is: Thu Apr 22 02:00:00 CEST 2088
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			// System.out.println(sdf.format(dCell.getDate()));
			// ==> 2088-04-22
			return sdf.format(dCell.getDate());
		}
		// possibly manage other types of cell in here if needed for your goals
		// read more:
		// http://www.quicklyjava.com/reading-excel-file-in-java-datatypes/#ixzz2fYIkHdZP
		return cell.getContents();
	}

	private void setColumnTitle(WritableSheet compareSheet,
			WritableCellFormat formatFont) {
		compareSheet.setColumnView(0, 16); // 統編
		compareSheet.setColumnView(1, 6); // 重複序號
		compareSheet.setColumnView(2, 20); // 戶名
		compareSheet.setColumnView(3, 10); // 簽案分行
		compareSheet.setColumnView(4, 20); // 案號
		compareSheet.setColumnView(5, 16); // 簽案日期
		compareSheet.setColumnView(6, 16); // 核准日期
		compareSheet.setColumnView(7, 20); // 額度序號
		compareSheet.setColumnView(8, 10); // 結果：新增/刪除/異動
		compareSheet.setColumnView(9, 10); // 紓困註記
		compareSheet.setColumnView(10, 10); // 紓困貸款類別
		compareSheet.setColumnView(11, 16); // 減收利率
		compareSheet.setColumnView(12, 10); // 現請額度幣別
		compareSheet.setColumnView(13, 20); // 現請額度金額(原幣)
		compareSheet.setColumnView(14, 14); // 央行轉融通資金專案
		compareSheet.setColumnView(15, 10); // 至109.03.12餘額幣別
		compareSheet.setColumnView(16, 20); // 至109.03.12餘額(原幣)
		compareSheet.setColumnView(17, 14); // 符合其他紓困貸款利息補貼
		compareSheet.setColumnView(18, 14); // 受理日期
		compareSheet.setColumnView(19, 14); // 央行轉融通辦理類別
		compareSheet.setColumnView(20, 14); // 登記期間
		compareSheet.setColumnView(21, 14); // 負責人從事本業期間
		compareSheet.setColumnView(22, 14); // 負責人個人信用評分資訊
		compareSheet.setColumnView(23, 14); // 擔保情形
		compareSheet.setColumnView(24, 14); // 營業狀況
		compareSheet.setColumnView(25, 14); // 銀行簡易評分表總分

		try {
			compareSheet.addCell(new Label(0, 0, "統編", formatFont));
			compareSheet.addCell(new Label(1, 0, "重複序號", formatFont));
			compareSheet.addCell(new Label(2, 0, "戶名", formatFont));
			compareSheet.addCell(new Label(3, 0, "簽案分行", formatFont));
			compareSheet.addCell(new Label(4, 0, "案號", formatFont));
			compareSheet.addCell(new Label(5, 0, "簽案日期", formatFont));
			compareSheet.addCell(new Label(6, 0, "核准日期", formatFont));
			compareSheet.addCell(new Label(7, 0, "額度序號", formatFont));
			compareSheet.addCell(new Label(8, 0, "結果", formatFont));
			compareSheet.addCell(new Label(9, 0, "紓困註記", formatFont));
			compareSheet.addCell(new Label(10, 0, "紓困貸款類別", formatFont));
			compareSheet.addCell(new Label(11, 0, "減收利率", formatFont));
			compareSheet.addCell(new Label(12, 0, "現請額度幣別", formatFont));
			compareSheet.addCell(new Label(13, 0, "現請額度金額(原幣)", formatFont));
			compareSheet.addCell(new Label(14, 0, "央行轉融通資金專案", formatFont));
			compareSheet
					.addCell(new Label(15, 0, "至109.03.12餘額幣別", formatFont));
			compareSheet.addCell(new Label(16, 0, "至109.03.12餘額(原幣)",
					formatFont));
			compareSheet.addCell(new Label(17, 0, "符合其他紓困貸款利息補貼", formatFont));
			compareSheet.addCell(new Label(18, 0, "受理日期", formatFont));
			compareSheet.addCell(new Label(19, 0, "央行轉融通辦理類別", formatFont));
			compareSheet.addCell(new Label(20, 0, "登記期間", formatFont));
			compareSheet.addCell(new Label(21, 0, "負責人從事本業期間", formatFont));
			compareSheet.addCell(new Label(22, 0, "負責人個人信用評分資訊", formatFont));
			compareSheet.addCell(new Label(23, 0, "擔保情形", formatFont));
			compareSheet.addCell(new Label(24, 0, "營業狀況", formatFont));
			compareSheet.addCell(new Label(25, 0, "銀行簡易評分表總分", formatFont));
		} catch (WriteException e) {
			e.printStackTrace();
		}
	}

	private void getXlsData(Sheet sheet, List<Map<String, Object>> list,
			int totalCol, Map<String, Integer> cntrnoMap) {
		int listNo = 0;
		for (int row = 3; row < sheet.getRows(); row++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int column = 7;
			String cntrNo = ""; // key - 額度序號 H(7)
			String flag = ""; // 紓困註記 I(8)
			String loanType = ""; // 紓困貸款類別 J(9)
			String rate = ""; // 減收利率 K(10)
			String curr = ""; // 現請額度幣別 L(11)
			String amt = ""; // 現請額度金額(原幣) M(12)
			String project = ""; // 央行轉融通資金專案 P(15)
			String balCurr = ""; // 至109.03.12餘額幣別 Q(16)
			String balAmt = ""; // 至109.03.12餘額(原幣) R(17)
			String other = ""; // 符合其他紓困貸款利息補貼 T(19)
			String date = ""; // 受理日期 V(21)
			String sb = ""; // 央行轉融通辦理類別 isSmallBuss AA(26)
			String sbRP = ""; // 登記期間 sbRegistPeriod AB(27)
			String sbPP = ""; // 負責人從事本業期間 sbPrincipalPeriod AC(28)
			String sbCR = ""; // 負責人個人信用評分資訊 sbCreditRating AD(29)
			String sbCS = ""; // 擔保情形 sbColStatus AE(30)
			String sbBS = ""; // 營業狀況 sbBussStatus AF(31)
			String sbS = ""; // 銀行簡易評分表總分 sbScore AG(32)

			if (8 <= totalCol) { // 此時column=8 故 減1
				cntrNo = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(7, row))));
			}
			if (9 <= totalCol) {
				flag = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(8, row))));
			}
			if (10 <= totalCol) {
				loanType = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(9, row))));
			}
			if (11 <= totalCol) {
				rate = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(10, row))));
			}
			if (12 <= totalCol) {
				curr = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(11, row))));
			}
			if (13 <= totalCol) {
				amt = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(12, row))));
			}
			if (16 <= totalCol) {
				project = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(15, row))));
			}
			if (17 <= totalCol) {
				balCurr = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(16, row))));
			}
			if (18 <= totalCol) {
				balAmt = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(17, row))));
			}
			if (20 <= totalCol) {
				other = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(19, row))));
			}
			if (22 <= totalCol) {
				date = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(21, row))));
				if (Util.notEquals(date, "")) {
					if (StringUtils.length(date) < 7) {
						date = Util.getRightStr("00000000" + date, 7);
					}
					date = TWNDate.valueOf(date).toAD('-');
				}
			}
			if (27 <= totalCol) {
				sb = StringUtils.upperCase(Util.trim(getContents(sheet.getCell(
						26, row))));
			}
			if (28 <= totalCol) {
				sbRP = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(27, row))));
			}
			if (29 <= totalCol) {
				sbPP = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(28, row))));
			}
			if (30 <= totalCol) {
				sbCR = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(29, row))));
			}
			if (31 <= totalCol) {
				sbCS = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(30, row))));
			}
			if (32 <= totalCol) {
				sbBS = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(31, row))));
			}
			if (33 <= totalCol) {
				sbS = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(32, row))));
			}

			map.put("cntrNo", cntrNo);
			map.put("flag", flag);
			map.put("loanType", loanType);
			map.put("rate", rate);
			map.put("curr", curr);
			map.put("amt", amt);
			map.put("project", project);
			map.put("balCurr", balCurr);
			map.put("balAmt", balAmt);
			map.put("other", other);
			map.put("date", date);
			map.put("sb", sb);
			map.put("sbRP", sbRP);
			map.put("sbPP", sbPP);
			map.put("sbCR", sbCR);
			map.put("sbCS", sbCS);
			map.put("sbBS", sbBS);
			map.put("sbS", sbS);

			// 基準檔多前面欄位
			String id = ""; // 統編
			String dupNo = ""; // 重複序號
			String name = ""; // 戶名
			String br = ""; // 簽案分行
			String caseNo = ""; // 案號
			String caseDate = ""; // 簽案日期
			String endDate = ""; // 核准日期
			if (1 <= totalCol) {
				id = StringUtils.upperCase(Util.trim(getContents(sheet.getCell(
						0, row))));
			}
			if (2 <= totalCol) {
				dupNo = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(1, row))));
			}
			if (3 <= totalCol) {
				name = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(2, row))));
			}
			if (4 <= totalCol) {
				br = StringUtils.upperCase(Util.trim(getContents(sheet.getCell(
						3, row))));
			}
			if (5 <= totalCol) {
				caseNo = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(4, row))));
			}
			if (6 <= totalCol) {
				caseDate = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(5, row))));
				if (Util.notEquals(caseDate, "")) {
					if (StringUtils.length(caseDate) < 7) {
						caseDate = Util.getRightStr("00000000" + caseDate, 7);
					}
					caseDate = TWNDate.valueOf(caseDate).toAD('-');
				}
			}
			if (7 <= totalCol) {
				endDate = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(6, row))));
				if (Util.notEquals(endDate, "")) {
					if (StringUtils.length(endDate) < 7) {
						endDate = Util.getRightStr("00000000" + endDate, 7);
					}
					endDate = TWNDate.valueOf(endDate).toAD('-');
				}
			}
			map.put("id", id);
			map.put("dupNo", dupNo);
			map.put("name", name);
			map.put("br", br);
			map.put("caseNo", caseNo);
			map.put("caseDate", caseDate);
			map.put("endDate", endDate);

			list.add(map);

			cntrnoMap.put(cntrNo, listNo);
			listNo++;
		}
	}

	private boolean compareData(Map<String, Object> compareMap,
			Map<String, Object> data_1, Map<String, Object> data_2) {
		boolean change = false;
		String[] compareArr = new String[] { "flag", "loanType", "rate",
				"curr", "amt", "project", "balCurr", "balAmt", "other", "date",
				"sb", "sbRP", "sbPP", "sbCR", "sbCS", "sbBS", "sbS" };
		for (String key : compareArr) {
			if (!Util.equals(Util.nullToSpace(data_1.get(key)),
					Util.nullToSpace(data_2.get(key)))) {
				change = true;
				compareMap.put(key, Util.nullToSpace(data_1.get(key)) + " / "
						+ Util.nullToSpace(data_2.get(key)));
			}
		}

		return change;
	}

	@NonTransactional
	public ByteArrayOutputStream uploadSoleX(PageParameters params)
			throws IOException, Exception, WriteException {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7600M01Page.class);

		LOGGER.info("uploadSole START=========");
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		Locale locale = null;
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();

		// 產出結果XLS
		Workbook result_workbook = null;
		WritableWorkbook resultWB = null;
		WritableSheet resultSheet = null;
		// File result_file = null;
		WritableFont font12 = null;
		WritableFont font12No = null;
		WritableCellFormat formatFont = null;
		WritableCellFormat format12Right = null;
		WritableCellFormat format12Left = null;
		String path = PropUtil.getProperty("loadFile.dir")
				+ "excel/LMS7600.xls";
		URL urlRpt = null;
		urlRpt = Thread.currentThread().getContextClassLoader()
				.getResource(path);
		if (urlRpt == null)
			throw new IOException("get File fail");
		try {
			result_workbook = Workbook.getWorkbook(new File(urlRpt.toURI()));
		} catch (Exception e) {
		}
		WorkbookSettings settings = new WorkbookSettings();
		settings.setWriteAccess(null);
		resultWB = Workbook.createWorkbook(outputStream, result_workbook,
				settings);
		if (result_workbook != null) {
			result_workbook.close();
		}
		resultSheet = resultWB.createSheet("Sheet1", 0);
		resultWB.removeSheet(1); // 拿掉LMS7600.xls的頁籤
		font12 = new WritableFont(WritableFont.createFont("標楷體"), 12,
				WritableFont.BOLD);
		font12No = new WritableFont(WritableFont.createFont("標楷體"), 12,
				WritableFont.NO_BOLD);
		formatFont = LMSUtil
				.setCellFormat(formatFont, font12, Alignment.CENTRE);
		format12Right = LMSUtil.setCellFormat(format12Right, font12No,
				Alignment.RIGHT);
		format12Left = LMSUtil.setCellFormat(format12Left, font12No,
				Alignment.LEFT);

		// title
		resultSheet.setColumnView(0, 14); // 分行別
		resultSheet.setColumnView(1, 10); // 額度序號
		resultSheet.setColumnView(2, 16); // 客戶編號
		resultSheet.setColumnView(3, 20); // 客戶名稱
		resultSheet.setColumnView(4, 20); // 獨資合夥註記
		resultSheet.setColumnView(5, 20); // 檢核結果(Y-獨資合夥無商業登記N-非獨資合夥或有商業登記)
		resultSheet.setColumnView(6, 10); // 執行結果
		resultSheet.setColumnView(7, 20); // 執行結果
		resultSheet.setColumnView(8, 20); // 執行結果
		resultSheet.setColumnView(9, 30); // 執行結果
		resultSheet.setColumnView(10, 30); // 執行結果

		resultSheet.addCell(new Label(0, 0, "核准日期", formatFont));
		resultSheet.addCell(new Label(1, 0, "簽案分行", formatFont));
		resultSheet.addCell(new Label(2, 0, "統編", formatFont));
		resultSheet.addCell(new Label(3, 0, "戶名", formatFont));
		resultSheet.addCell(new Label(4, 0, "簽報書案號", formatFont));
		resultSheet.addCell(new Label(5, 0, "額度序號", formatFont));
		resultSheet.addCell(new Label(6, 0, "現請額度幣別", formatFont));
		resultSheet.addCell(new Label(7, 0, "現請額度金額", formatFont));
		resultSheet.addCell(new Label(8, 0, "客戶別", formatFont));
		resultSheet.addCell(new Label(9, 0, "科目", formatFont));
		resultSheet.addCell(new Label(10, 0, "擔保品", formatFont));
		List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();

		String fileKey = params.getString("fileKey");
		DocFile df = fileService.findByOidAndSysId(fileKey, "LMS");
		File file = null;
		InputStream is = null;
		Workbook workbook = null;
		file = fileService.getRealFile(df);
		is = FileUtils.openInputStream(file);
		workbook = Workbook.getWorkbook(is);
		Sheet sheet = workbook.getSheet(0);

		int totalCol = sheet.getColumns();
		if (totalCol == 0) {
			throw new CapMessageException("匯入之名單EXCEL格式錯誤。", getClass());
		}

		// 讀取資料
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		LOGGER.info("讀取Sole資料 START=========");
		this.getSoleDataX(sheet, list, totalCol);
		LOGGER.info("讀取Sole資料 END=========");

		// 開始
		if (list != null && !list.isEmpty()) {
			LOGGER.info("讀取資料 START=========");
			for (Map<String, Object> data : list) {
				Map<String, Object> dataMap = new HashMap<String, Object>(data);

				String CMSDSCRStr = MapUtils.getString(data, "CMSDSCR")
						.toLowerCase();
				String CMSDSCRStr1 = StringEscapeUtils.unescapeHtml(CMSDSCRStr);
				String newCMSDSCRStr = this.trimHtml2Txt(CMSDSCRStr1);
				LOGGER.info("轉換後=" + newCMSDSCRStr);
				dataMap.put("CMSDSCR", newCMSDSCRStr);
				resultList.add(dataMap);
			}
			LOGGER.info("讀取資料 END=========");
		}

		// 開始輸出 XLS
		int y = 0;
		for (Map<String, Object> map : resultList) {
			y++;
			resultSheet.addCell(new Label(0, y, Util.trim(map.get("ENDDATE")),
					format12Left));
			resultSheet.addCell(new Label(1, y, Util.trim(map.get("CASEBRID")),
					format12Left));
			resultSheet.addCell(new Label(2, y, Util.trim(map.get("CUSTID")),
					format12Left));
			resultSheet.addCell(new Label(3, y, Util.trim(map.get("CUSTNAME")),
					format12Left));
			resultSheet.addCell(new Label(4, y, Util.trim(map.get("CASENO")),
					format12Left));
			resultSheet.addCell(new Label(5, y, Util.trim(map.get("CNTRNO")),
					format12Left));
			resultSheet.addCell(new Label(6, y, Util.trim(map
					.get("CURRENTAPPLYCURR")), format12Left));
			resultSheet.addCell(new Label(7, y, NumConverter.addComma(Util
					.trim(map.get("CURRENTAPPLYAMT"))), format12Right));

			resultSheet.addCell(new Label(8, y, Util.trim(map.get("CCSMEMO")),
					format12Left));
			resultSheet.addCell(new Label(9, y,
					Util.trim(map.get("LNSUBJECT")), format12Left));
			resultSheet.addCell(new Label(10, y, Util.trim(map.get("CMSDSCR")),
					format12Left));

		}

		resultWB.write();
		resultWB.close();

		if (outputStream != null) {
			outputStream.flush();
		}
		return outputStream;
	}

	public String trimHtml2Txt(String html) {

		String noHTMLString = "";
		// TEST 1
		// String htmlTagPattern = "<{1}[^>]{1,}>{1}"; // HTML Tag Pattern
		// String htmlSplit = html.replaceAll(htmlTagPattern, "");

		// TEST 2
		// noHTMLString = html.replaceAll("\\<.*?\\>", "");
		// noHTMLString = noHTMLString.replaceAll("&#92;&#92;s{2,}", " ");

		// TEST 3
		try {
			StringReader in = new StringReader(html);
			Html2Text parser = new Html2Text();
			parser.parse(in);
			noHTMLString = parser.getText();
		} catch (Exception e) {
			e.printStackTrace();
		}

		return noHTMLString.trim();
	}

	@NonTransactional
	public ByteArrayOutputStream uploadSole(PageParameters params)
			throws IOException, Exception, WriteException {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7600M01Page.class);

		LOGGER.info("uploadSole START=========");
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		Locale locale = null;
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();

		// 產出結果XLS
		Workbook result_workbook = null;
		WritableWorkbook resultWB = null;
		WritableSheet resultSheet = null;
		// File result_file = null;
		WritableFont font12 = null;
		WritableFont font12No = null;
		WritableCellFormat formatFont = null;
		WritableCellFormat format12Right = null;
		WritableCellFormat format12Left = null;
		String path = PropUtil.getProperty("loadFile.dir")
				+ "excel/LMS7600.xls";
		URL urlRpt = null;
		urlRpt = Thread.currentThread().getContextClassLoader()
				.getResource(path);
		if (urlRpt == null)
			throw new IOException("get File fail");
		try {
			result_workbook = Workbook.getWorkbook(new File(urlRpt.toURI()));
		} catch (Exception e) {
		}
		WorkbookSettings settings = new WorkbookSettings();
		settings.setWriteAccess(null);
		resultWB = Workbook.createWorkbook(outputStream, result_workbook,
				settings);
		if (result_workbook != null) {
			result_workbook.close();
		}
		resultSheet = resultWB.createSheet("Sheet1", 0);
		resultWB.removeSheet(1); // 拿掉LMS7600.xls的頁籤
		font12 = new WritableFont(WritableFont.createFont("標楷體"), 12,
				WritableFont.BOLD);
		font12No = new WritableFont(WritableFont.createFont("標楷體"), 12,
				WritableFont.NO_BOLD);
		formatFont = LMSUtil
				.setCellFormat(formatFont, font12, Alignment.CENTRE);
		format12Right = LMSUtil.setCellFormat(format12Right, font12No,
				Alignment.RIGHT);
		format12Left = LMSUtil.setCellFormat(format12Left, font12No,
				Alignment.LEFT);

		// title
		resultSheet.setColumnView(0, 10); // 分行別
		resultSheet.setColumnView(1, 20); // 額度序號
		resultSheet.setColumnView(2, 16); // 客戶編號
		resultSheet.setColumnView(3, 20); // 客戶名稱
		resultSheet.setColumnView(4, 5); // 獨資合夥註記
		resultSheet.setColumnView(5, 5); // 檢核結果(Y-獨資合夥無商業登記N-非獨資合夥或有商業登記)
		resultSheet.setColumnView(6, 30); // 執行結果
		resultSheet.addCell(new Label(0, 0, "分行別", formatFont));
		resultSheet.addCell(new Label(1, 0, "額度序號", formatFont));
		resultSheet.addCell(new Label(2, 0, "客戶編號", formatFont));
		resultSheet.addCell(new Label(3, 0, "客戶名稱", formatFont));
		resultSheet.addCell(new Label(4, 0, "獨資合夥註記", formatFont));
		resultSheet.addCell(new Label(5, 0, "檢核結果(Y-獨資合夥無商業登記N-非獨資合夥或有商業登記)",
				formatFont));
		resultSheet.addCell(new Label(6, 0, "執行結果", formatFont));
		List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();

		String fileKey = params.getString("fileKey");
		DocFile df = fileService.findByOidAndSysId(fileKey, "LMS");
		File file = null;
		InputStream is = null;
		Workbook workbook = null;
		file = fileService.getRealFile(df);
		is = FileUtils.openInputStream(file);
		workbook = Workbook.getWorkbook(is);
		Sheet sheet = workbook.getSheet(0);

		int totalCol = sheet.getColumns();
		if (totalCol == 0) {
			throw new CapMessageException("匯入之名單EXCEL格式錯誤。", getClass());
		}

		// 讀取資料
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		LOGGER.info("讀取Sole資料 START=========");
		this.getSoleData(sheet, list, totalCol);
		LOGGER.info("讀取Sole資料 END=========");

		// 開始
		if (list != null && !list.isEmpty()) {
			LOGGER.info("讀取MIS.QUOTAPPR資料 START=========");
			for (Map<String, Object> data : list) {
				Map<String, Object> dataMap = new HashMap<String, Object>(data);

				String brId = Util.nullToSpace(data.get("brId"));
				String key_cntrno = Util.trim(Util.nullToSpace(data
						.get("cntrNo")));
				String key_custId = Util.trim(Util.nullToSpace(data
						.get("custId")));
				String key_dupNo = "0";
				String custName = Util.nullToSpace(data.get("custName"));
				String isSole = Util.nullToSpace(data.get("isSole"));
				String hasRegis = Util.trim(Util.nullToSpace(data
						.get("hasRegis")));

				String str = "";
				if (Util.notEquals(hasRegis, "Y")
						&& Util.notEquals(hasRegis, "N")) {
					str = "檢核結果不為Y/N，故不更新資料";
				} else {
					if (Util.notEquals(isSole, "Y")) {
						str = "獨資合夥註記不為Y，故不更新資料";
					} else {
						if (Util.isEmpty(key_cntrno)) {
							str = "額度序號為空值，故不更新資料";
						} else if (Util.isEmpty(key_custId)) {
							str = "客戶編號為空值，故不更新資料";
						} else {
							List<Map<String, Object>> elf383List = misQuotapprService
									.findByCustIdCntrNo(key_custId, key_dupNo,
											key_cntrno);
							if (elf383List != null && !elf383List.isEmpty()) {
								int cnt = 0;
								// cnt = elf383List.size();
								cnt = misQuotapprService.updateSole(hasRegis,
										key_custId, key_dupNo, key_cntrno);
								str = "更新 " + cnt + " 筆資料";
							} else {
								str = "無相對應資料";
							}
						}
					}
				}

				dataMap.put("result", str);
				resultList.add(dataMap);
			}
			LOGGER.info("讀取MIS.QUOTAPPR資料 END=========");
		}

		// 開始輸出 XLS
		int y = 0;
		for (Map<String, Object> map : resultList) {
			y++;
			resultSheet.addCell(new Label(0, y, Util.trim(map.get("brId")),
					format12Left));
			resultSheet.addCell(new Label(1, y, Util.trim(map.get("cntrNo")),
					format12Right));
			resultSheet.addCell(new Label(2, y, Util.trim(map.get("custId")),
					format12Right));
			resultSheet.addCell(new Label(3, y, Util.trim(map.get("custName")),
					format12Left));
			resultSheet.addCell(new Label(4, y, Util.trim(map.get("isSole")),
					format12Left));
			resultSheet.addCell(new Label(5, y, Util.trim(map.get("hasRegis")),
					format12Left));
			resultSheet.addCell(new Label(6, y, Util.trim(map.get("result")),
					format12Left));
		}

		resultWB.write();
		resultWB.close();

		if (outputStream != null) {
			outputStream.flush();
		}
		return outputStream;
	}

	private void getSoleDataX(Sheet sheet, List<Map<String, Object>> list,
			int totalCol) {
		LOGGER.info("sheet.getRows()=========" + sheet.getRows());
		for (int row = 1; row < sheet.getRows(); row++) {
			Map<String, Object> dataMap = new HashMap<String, Object>();
			// int column = 1;
			String ENDDATE = ""; // 分行別 (0)
			String CASEBRID = ""; // key - 額度序號 (1)
			String CUSTID = ""; // key - 客戶編號 (2)
			String CUSTNAME = ""; // 客戶名稱 (3)
			String CASENO = ""; // 獨資合夥註記 (4)

			String CNTRNO = ""; // 獨資合夥註記 (4)
			String CURRENTAPPLYCURR = ""; // 獨資合夥註記 (4)
			String CURRENTAPPLYAMT = ""; // 獨資合夥註記 (4)
			String CCSMEMO = ""; // 獨資合夥註記 (4)
			String LNSUBJECT = ""; // 獨資合夥註記 (4)
			String CMSDSCR = ""; // 獨資合夥註記 (4)

			// 讀到 Y ==> hasRegis 改 N N ==> hasRegis 改 Y 其他值則不update
			String hasRegis = ""; // Y-獨資合夥無商業登記N-非獨資合夥或有商業登記 (5)

			if (1 <= totalCol) {
				ENDDATE = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(0, row))));
			}
			if (2 <= totalCol) {
				CASEBRID = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(1, row))));
			}
			if (3 <= totalCol) {
				CUSTID = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(2, row))));
			}
			if (4 <= totalCol) {
				CUSTNAME = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(3, row))));
			}
			if (5 <= totalCol) {
				CASENO = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(4, row))));
			}
			if (6 <= totalCol) {
				CNTRNO = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(5, row))));
			}

			if (7 <= totalCol) {
				CURRENTAPPLYCURR = StringUtils.upperCase(Util
						.trim(getContents(sheet.getCell(6, row))));
			}

			if (8 <= totalCol) {
				CURRENTAPPLYAMT = StringUtils.upperCase(Util
						.trim(getContents(sheet.getCell(7, row))));
			}

			if (9 <= totalCol) {
				CCSMEMO = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(8, row))));
			}

			if (10 <= totalCol) {
				LNSUBJECT = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(9, row))));
			}

			if (11 <= totalCol) {
				CMSDSCR = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(10, row))));
			}

			dataMap.put("ENDDATE", ENDDATE);
			dataMap.put("CASEBRID", CASEBRID);
			dataMap.put("CUSTID", CUSTID);
			dataMap.put("CUSTNAME", CUSTNAME);
			dataMap.put("CASENO", CASENO);
			dataMap.put("CNTRNO", CNTRNO);
			dataMap.put("CURRENTAPPLYCURR", CURRENTAPPLYCURR);
			dataMap.put("CURRENTAPPLYAMT", CURRENTAPPLYAMT);
			dataMap.put("CCSMEMO", CCSMEMO);
			dataMap.put("LNSUBJECT", LNSUBJECT);
			dataMap.put("CMSDSCR", CMSDSCR);

			list.add(dataMap);
		}
	}

	private void getSoleData(Sheet sheet, List<Map<String, Object>> list,
			int totalCol) {
		LOGGER.info("sheet.getRows()=========" + sheet.getRows());
		for (int row = 1; row < sheet.getRows(); row++) {
			Map<String, Object> dataMap = new HashMap<String, Object>();
			// int column = 1;
			String brId = ""; // 分行別 (0)
			String cntrNo = ""; // key - 額度序號 (1)
			String custId = ""; // key - 客戶編號 (2)
			String custName = ""; // 客戶名稱 (3)
			String isSole = ""; // 獨資合夥註記 (4)
			// 讀到 Y ==> hasRegis 改 N N ==> hasRegis 改 Y 其他值則不update
			String hasRegis = ""; // Y-獨資合夥無商業登記N-非獨資合夥或有商業登記 (5)

			if (1 <= totalCol) {
				brId = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(0, row))));
			}
			if (2 <= totalCol) {
				cntrNo = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(1, row))));
			}
			if (3 <= totalCol) {
				custId = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(2, row))));
			}
			if (4 <= totalCol) {
				custName = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(3, row))));
			}
			if (5 <= totalCol) {
				isSole = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(4, row))));
			}
			if (6 <= totalCol) {
				hasRegis = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(5, row))));
			}

			dataMap.put("brId", brId);
			dataMap.put("cntrNo", cntrNo);
			dataMap.put("custId", custId);
			dataMap.put("custName", custName);
			dataMap.put("isSole", isSole);
			dataMap.put("hasRegis", hasRegis);
			list.add(dataMap);
		}
	}

	@NonTransactional
	public ByteArrayOutputStream getLatestCol(PageParameters params)
			throws IOException, Exception, WriteException {
		LOGGER.info("getLatestCol START=========");
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

		// 產出結果XLS
		Workbook result_workbook = null;
		WritableWorkbook resultWB = null;
		WritableSheet resultSheet = null;
		WritableFont font12 = null;
		WritableFont font12No = null;
		WritableCellFormat formatFont = null;
		WritableCellFormat format12Right = null;
		WritableCellFormat format12Left = null;
		String path = PropUtil.getProperty("loadFile.dir")
				+ "excel/LMS7600.xls";
		URL urlRpt = null;
		urlRpt = Thread.currentThread().getContextClassLoader()
				.getResource(path);
		if (urlRpt == null)
			throw new IOException("get File fail");
		try {
			result_workbook = Workbook.getWorkbook(new File(urlRpt.toURI()));
		} catch (Exception e) {
		}
		WorkbookSettings settings = new WorkbookSettings();
		settings.setWriteAccess(null);
		resultWB = Workbook.createWorkbook(outputStream, result_workbook,
				settings);
		if (result_workbook != null) {
			result_workbook.close();
		}
		resultSheet = resultWB.createSheet("Sheet1", 0);
		resultWB.removeSheet(1); // 拿掉LMS7600.xls的頁籤
		font12 = new WritableFont(WritableFont.createFont("標楷體"), 12,
				WritableFont.BOLD);
		font12No = new WritableFont(WritableFont.createFont("標楷體"), 12,
				WritableFont.NO_BOLD);
		formatFont = LMSUtil
				.setCellFormat(formatFont, font12, Alignment.CENTRE);
		format12Right = LMSUtil.setCellFormat(format12Right, font12No,
				Alignment.RIGHT);
		format12Left = LMSUtil.setCellFormat(format12Left, font12No,
				Alignment.LEFT);

		// title
		resultSheet.setColumnView(0, 16); // 客戶編號
		resultSheet.setColumnView(1, 5); // 客戶編號-重複序號
		resultSheet.setColumnView(2, 20); // 客戶名稱
		resultSheet.setColumnView(3, 20); // 額度序號
		resultSheet.setColumnView(4, 10); // 分行別
		resultSheet.setColumnView(5, 10); // 簽案日期
		resultSheet.setColumnView(6, 10); // 核准日期
		resultSheet.setColumnView(7, 30); // 案號
		resultSheet.setColumnView(8, 50); // 擔保品
		resultSheet.setColumnView(9, 50); // 授信科子目
		resultSheet.addCell(new Label(0, 0, "客戶統編", formatFont));
		resultSheet.addCell(new Label(1, 0, "重複序號", formatFont));
		resultSheet.addCell(new Label(2, 0, "客戶名稱", formatFont));
		resultSheet.addCell(new Label(3, 0, "額度序號", formatFont));
		resultSheet.addCell(new Label(4, 0, "分行別", formatFont));
		resultSheet.addCell(new Label(5, 0, "簽案日期", formatFont));
		resultSheet.addCell(new Label(6, 0, "核准日期", formatFont));
		resultSheet.addCell(new Label(7, 0, "案號", formatFont));
		resultSheet.addCell(new Label(8, 0, "擔保品", formatFont));
		resultSheet.addCell(new Label(9, 0, "授信科子目", formatFont));
		List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();

		String fileKey = params.getString("fileKey");
		DocFile df = fileService.findByOidAndSysId(fileKey, "LMS");
		File file = null;
		InputStream is = null;
		Workbook workbook = null;
		file = fileService.getRealFile(df);
		is = FileUtils.openInputStream(file);
		workbook = Workbook.getWorkbook(is);
		Sheet sheet = workbook.getSheet(0);

		int totalCol = sheet.getColumns();
		if (totalCol == 0) {
			throw new CapMessageException("匯入之名單EXCEL格式錯誤。", getClass());
		}

		// 讀取資料
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		LOGGER.info("讀取LatestCol資料 START=========");
		LOGGER.info("sheet.getRows()=========" + sheet.getRows());
		for (int row = 0; row < sheet.getRows(); row++) {
			Map<String, Object> dataMap = new HashMap<String, Object>();

			String custId = ""; // 客戶編號 (0)

			if (1 <= totalCol) {
				custId = StringUtils.upperCase(Util.trim(getContents(sheet
						.getCell(0, row))));
			}

			dataMap.put("custId", custId);
			list.add(dataMap);
		}
		LOGGER.info("讀取LatestCol資料 END=========");

		// 開始
		if (list != null && !list.isEmpty()) {
			LOGGER.info("SELECT資料 START=========");
			String bgnDT = params.getString("bgnDT");
			String endDT = params.getString("endDT");
			for (Map<String, Object> data : list) {
				Map<String, Object> dataMap = new HashMap<String, Object>(data);

				String custId = Util.trim(Util.nullToSpace(data.get("custId")));

				if (Util.isEmpty(custId)) {
					continue;
				} else {
					List<Map<String, Object>> outList = eloandbBASEService
							.getLatestCol(custId, bgnDT, endDT);

					if (outList != null && !outList.isEmpty()) {
						for (Map<String, Object> outMap : outList) {
							if (outMap != null && !outMap.isEmpty()) {
								resultList.add(outMap);
							}
						}
					}
				}
			}
			LOGGER.info("SELECT資料 END=========");
		}

		// 開始輸出 XLS
		int y = 0;
		for (Map<String, Object> map : resultList) {
			y++;
			resultSheet.addCell(new Label(0, y, Util.trim(map.get("CUSTID")),
					format12Left));
			resultSheet.addCell(new Label(1, y, Util.trim(map.get("DUPNO")),
					format12Left));
			resultSheet.addCell(new Label(2, y, Util.trim(map.get("CUSTNAME")),
					format12Left));
			resultSheet.addCell(new Label(3, y, Util.trim(map.get("CNTRNO")),
					format12Left));
			resultSheet.addCell(new Label(4, y, Util.trim(map.get("CASEBRID")),
					format12Left));
			resultSheet.addCell(new Label(5, y, Util.trim(map.get("CASEDATE")),
					format12Left));
			resultSheet.addCell(new Label(6, y, Util.trim(map.get("ENDDATE")),
					format12Left));
			resultSheet.addCell(new Label(7, y, Util.trim(map.get("CASENO")),
					format12Left));
			resultSheet.addCell(new Label(8, y, Util.trim(map.get("ITEMDSCR")),
					format12Left));
			resultSheet.addCell(new Label(9, y,
					Util.trim(map.get("LNSUBJECT")), format12Left));
		}

		resultWB.write();
		resultWB.close();

		if (outputStream != null) {
			outputStream.flush();
		}
		return outputStream;
	}

	private boolean isControlByCentralBank(Map<String, Object> map) {

		Date date = CapDate.parseDate(MapUtils
				.getString(map, "Elf600_RRMOVEDT"));
		if (CrsUtil.isNOT_null_and_NOTZeroDate(date)
				|| "X".equals(map.get("ELF600_CTLTYPE"))) {
			return false;
		}

		Map<String, Object> lnf087Map = this.misdbBASEService
				.findLNF087(MapUtils.getString(map, "ELF600_CONTRACT"));
		if (lnf087Map != null
				&& "4".equals(MapUtils
						.getString(lnf087Map, "LNF087_CONTROL_CD"))) {
			return false;
		}

		return true;
	}
}
