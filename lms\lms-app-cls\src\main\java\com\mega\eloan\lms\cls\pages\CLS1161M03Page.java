package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.cls.panels.CLS1161S31Panel;
import com.mega.eloan.lms.cls.panels.CLS1161S32Panel;
import com.mega.eloan.lms.model.C160M03A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 整批自動開戶 
 * </pre>
 * 
 * @since 2017/03/05
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/03/05,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1161m03/{page}")
public class CLS1161M03Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		MegaSSOUserDetails users = MegaSSOSecurityContext.getUserDetails();
		
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params,
				getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params,
				getDomainClass(), AuthType.Accept, CreditDocStatusEnum.海外_待覆核));
		addAclLabel(model, new AclLabel("_btn_APPROVED", params,
				getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_已核准));
		
		// hide tab, [資訊處]可查看詳細資料
		boolean dtl_view = users.getSsoUnitNo().matches("900");
		model.addAttribute("_for900View", dtl_view);
		
		// i18n
		renderJsI18N(CLS1161M03Page.class);
		// tabs
		int page = params.getAsInteger(EloanConstants.PAGE, 1);
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
	}

	// 頁籤
	@SuppressWarnings("unused")
	public Panel getPanel(int index) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new CLS1161S31Panel(TAB_CTX, true);
			break;
		case 2:
			panel = new CLS1161S32Panel(TAB_CTX, true);
			break;
		default:
			panel = new CLS1161S31Panel(TAB_CTX, true);
			break;
		}
		if (panel == null) {
			panel = new Panel(TAB_CTX);
		}

		return panel;
	}

	// 驗證文件狀態Class
	public Class<? extends Meta> getDomainClass() {
		return C160M03A.class;
	}
}
