/* 
 * FlowNameServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.util.HashMap;
import java.util.LinkedHashMap;

import javax.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.base.service.FlowNameService;

/**
 * <pre>
 * flow name 英文名和中文轉換
 * </pre>
 * 
 * @since 2011/11/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/15,REX,new
 *          </ul>
 */
@Service("FlowNameService")
public class FlowNameServiceImpl implements FlowNameService {

	private HashMap<String, String> container = new LinkedHashMap<String, String>();

	@PostConstruct
	@Override
	public void init() {
		container.put("sendHead", "to呈授管處");
		container.put("sendArea", "to呈區域中心");
		container.put("back", "to退回");
		container.put("backFirst", "to退回編製中");
		container.put("backArea", "to退回區域中心");
		container.put("waitCheck", "to待放行");
		container.put("waitCase", "to待補件");
		container.put("cancelCase", "to撤件");
		container.put("sendMain", "to總行待覆核");
		container.put("sendDrc", "to陳復述");
		container.put("check", "to核定");
		container.put("noCheck", "to已婉卻");
		container.put("afterSign", "to會簽後修改編製中");
		container.put("sendAWait", "to會簽後修改待覆核");
		container.put("hasSign", "to已會簽");
		container.put("sendHWait", "to會簽待覆核");
		container.put("sendStop", "to停權待覆核");
		container.put("backStop", "to退回停權編製中");
		container.put("sendParent", "to母行待覆核");

	}

	@Override
	public String getKey(String key) {
		if (container.containsKey(key)) {
			return container.get(key);
		}
		return key;
	}
}
