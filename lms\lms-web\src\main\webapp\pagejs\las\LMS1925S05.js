$(document).ready(function() {

	$("#cancel").click(function() {
		clearAll();
	});
	$("#finish").click(function() {
		clearAll();
		$("select[name^='ck']").val('V');
		$("select[name^='userCk']").each(function(index) {
			if ($("#userItem" + (index + 1)).val().length > 0) {
				$("#userCk" + (index + 1)).val('V');
			}
		});
	});

	var clearAll = function() {
		$("select[name^='ck'],select[name^='userCk']").val('');
	}
});

initDfd.done(function(json) {
	// ilog.debug(json);
	var enterpriseCustomers = $('#enterpriseCustomers').val();
	//alert(enterpriseCustomers);
	if (enterpriseCustomers == "Y") {		
		$('#ck5Date').show();
		$('#ck5Amt').remove();
	} else {	
		$('#ck5Date').datepicker("destroy").remove();
		$('#ck5Amt').show();
	}

});