/* 
 * L140M01RDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import com.mega.eloan.lms.dao.L140M01RDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140M01R;

/** 個金產品種類檔 **/
@Repository
public class L140M01RDaoImpl extends LMSJpaDao<L140M01R, String> implements
		L140M01RDao {
	
	@Override
	public List<L140M01R> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L140M01R> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L140M01R> findByMainIdFeeSrc(String mainId, String feeSrc) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "feeSrc", feeSrc);
		List<L140M01R> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L140M01R> findByMainIdFeeSrcFeeNo(String mainId, String feeSrc, String feeNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "feeSrc", feeSrc);
		search.addSearchModeParameters(SearchMode.EQUALS, "feeNo", feeNo);
		List<L140M01R> list = createQuery(search).getResultList();
		return list;
	}

}