/* 
 * L141M01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 共同借款人檔 **/
@Entity
// @EntityListeners({DocumentModifyListener.class})
@Table(name = "L141M01C", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "cntrNo" }))
public class L141M01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 額度序號
	 * <p/>
	 * 101/01/02配合國內個金案件新增<br/>
	 * ※如本欄為空白或null則表示全部。
	 */
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/** 借款人姓名 **/
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/**
	 * 企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	@Column(name = "DOCTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String docType;

	/**
	 * 客戶型態 (區部別)
	 * <p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 */
	@Column(name = "TYPCD", length = 1, columnDefinition = "CHAR(1)")
	private String typCd;

	/**
	 * 客戶編號
	 * <p/>
	 * 非必要輸入
	 */
	@Column(name = "CUSTNO", length = 7, columnDefinition = "VARCHAR(7)")
	private String custNo;

	/**
	 * 主要借款人註記
	 * <p/>
	 * Y/N
	 */
	@Column(name = "KEYMAN", length = 1, columnDefinition = "CHAR(1)")
	private String keyMan;

	/**
	 * 國別註冊地
	 * <p/>
	 * 1.企金：L120S01B.ntCode<br/>
	 * 2個金：L120S01H.ntCode
	 */
	@Column(name = "NTCODE", length = 2, columnDefinition = "VARCHAR(2)")
	private String ntCode;

	/**
	 * 與主要借款人關係
	 * <p/>
	 * 本人、配偶…<br/>
	 * 企業關係人：1X, 2X, …<br/>
	 * 親屬關係：X0, XA, …<br/>
	 * 其他綜合關係：10, 1A, …<br/>
	 * (詳註一)
	 */
	@Column(name = "CUSTRLT", length = 2, columnDefinition = "CHAR(2)")
	private String custRlt;

	/**
	 * 相關身份
	 * <p/>
	 * C.共同借款人<br/>
	 * D.共同發票人(企金)<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)<br/>
	 * ※非主要借款人時才需填列
	 */
	@Column(name = "CUSTPOS", length = 1, columnDefinition = "CHAR(1)")
	private String custPos;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得額度序號
	 * <p/>
	 * 101/01/02配合國內個金案件新增<br/>
	 * ※如本欄為空白或null則表示全部。
	 */
	public String getCntrNo() {
		return this.cntrNo;
	}

	/**
	 * 設定額度序號
	 * <p/>
	 * 101/01/02配合國內個金案件新增<br/>
	 * ※如本欄為空白或null則表示全部。
	 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得借款人姓名 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定借款人姓名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	public String getDocType() {
		return this.docType;
	}

	/**
	 * 設定企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 **/
	public void setDocType(String value) {
		this.docType = value;
	}

	/**
	 * 取得客戶型態 (區部別)
	 * <p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 */
	public String getTypCd() {
		return this.typCd;
	}

	/**
	 * 設定客戶型態 (區部別)
	 * <p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/**
	 * 取得客戶編號
	 * <p/>
	 * 非必要輸入
	 */
	public String getCustNo() {
		return this.custNo;
	}

	/**
	 * 設定客戶編號
	 * <p/>
	 * 非必要輸入
	 **/
	public void setCustNo(String value) {
		this.custNo = value;
	}

	/**
	 * 取得主要借款人註記
	 * <p/>
	 * Y/N
	 */
	public String getKeyMan() {
		return this.keyMan;
	}

	/**
	 * 設定主要借款人註記
	 * <p/>
	 * Y/N
	 **/
	public void setKeyMan(String value) {
		this.keyMan = value;
	}

	/**
	 * 取得國別註冊地
	 * <p/>
	 * 1.企金：L120S01B.ntCode<br/>
	 * 2個金：L120S01H.ntCode
	 */
	public String getNtCode() {
		return this.ntCode;
	}

	/**
	 * 設定國別註冊地
	 * <p/>
	 * 1.企金：L120S01B.ntCode<br/>
	 * 2個金：L120S01H.ntCode
	 **/
	public void setNtCode(String value) {
		this.ntCode = value;
	}

	/**
	 * 取得與主要借款人關係
	 * <p/>
	 * 本人、配偶…<br/>
	 * 企業關係人：1X, 2X, …<br/>
	 * 親屬關係：X0, XA, …<br/>
	 * 其他綜合關係：10, 1A, …<br/>
	 * (詳註一)
	 */
	public String getCustRlt() {
		return this.custRlt;
	}

	/**
	 * 設定與主要借款人關係
	 * <p/>
	 * 本人、配偶…<br/>
	 * 企業關係人：1X, 2X, …<br/>
	 * 親屬關係：X0, XA, …<br/>
	 * 其他綜合關係：10, 1A, …<br/>
	 * (詳註一)
	 **/
	public void setCustRlt(String value) {
		this.custRlt = value;
	}

	/**
	 * 取得相關身份
	 * <p/>
	 * C.共同借款人<br/>
	 * D.共同發票人(企金)<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)<br/>
	 * ※非主要借款人時才需填列
	 */
	public String getCustPos() {
		return this.custPos;
	}

	/**
	 * 設定相關身份
	 * <p/>
	 * C.共同借款人<br/>
	 * D.共同發票人(企金)<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)<br/>
	 * ※非主要借款人時才需填列
	 **/
	public void setCustPos(String value) {
		this.custPos = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
