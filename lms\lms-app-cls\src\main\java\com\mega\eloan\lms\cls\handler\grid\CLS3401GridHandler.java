package com.mega.eloan.lms.cls.handler.grid;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.RelatedAccountService;
import com.mega.eloan.lms.cls.pages.CLS3401V01Page;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 消金契約書
 * </pre>
 * 
 * @since 2020/02/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/02/14,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("cls3401gridhandler")
public class CLS3401GridHandler extends AbstractGridHandler {

	@Resource
	CLSService clsService;
	
	@Resource
	UserInfoService userInfoService;

	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	DwdbBASEService dwdbBASEService;
	
	@Resource
	ICustomerService iCustomerService;
		
	@Resource
	RelatedAccountService relatedAccountService;
	
	public CapGridResult queryView(ISearch pageSetting, PageParameters params)
			throws CapException {
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());	
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);		
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		if(true){ //篩選
			String custId = Util.trim(params.getString("search_custId"));
			if (Util.isNotEmpty(custId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
			}	
			//~~~~~~
			String ctrType = Util.trim(params.getString("search_ctrType"));
			if (Util.isNotEmpty(ctrType)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ctrType", ctrType);
			}
			//~~~~~~
			String ploanCtrNo = Util.trim(params.getString("search_ploanCtrNo"));
			if (Util.isNotEmpty(ploanCtrNo)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ploanCtrNo", ploanCtrNo);
			}	
			//~~~~~~
			String ploanCtrStatus = Util.trim(params.getString("search_ploanCtrStatus"));
			if (Util.isNotEmpty(ploanCtrStatus)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ploanCtrStatus", ploanCtrStatus);
			}
			//~~~~~~
			String ploanBorrowerIPAddr = Util.trim(params.getString("search_ploanBorrowerIPAddr"));
			if (Util.isNotEmpty(ploanBorrowerIPAddr)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "ploanBorrowerIPAddr", ploanBorrowerIPAddr+"%");
			}
			//~~~~~~
			String ploanCtrBegDate_beg = Util.trim(params.getString("ploanCtrBegDate_beg"));
			String ploanCtrBegDate_end = Util.trim(params.getString("ploanCtrBegDate_end"));
			if(Util.isNotEmpty(ploanCtrBegDate_beg) && Util.isNotEmpty(ploanCtrBegDate_end) ){
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "ploanCtrBegDate", new Object[] { ploanCtrBegDate_beg, ploanCtrBegDate_end });
			}
		}
		
		Page<? extends GenericBean> page =  clsService.findPage(C340M01A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		if(true){
			final Properties prop_cls3401v01 = MessageBundleScriptCreator.getComponentResource(CLS3401V01Page.class);
			UserNameFormatter userNameFormatter = new UserNameFormatter(userInfoService, UserNameFormatter.ShowTypeEnum.Name);
			dataReformatter.put("updater", userNameFormatter); // 使用者名稱格式化	
			dataReformatter.put("approver", userNameFormatter); // 使用者名稱格式化
			dataReformatter.put("ctrType", new IFormatter() {
				@Override
				public String reformat(Object in) throws CapFormatException {
					String str = (String) in;
					return prop_cls3401v01.getProperty("C340M01A.ctrType." + str);
				}
			});
			
			dataReformatter.put("category", new CodeTypeFormatter(codeTypeService, "C900S02D_category"));
		}
		result.setDataReformatter(dataReformatter);
		
		return result;
	}
	
	public CapMapGridResult queryCntrNo_ctrType_1(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		String brNo = user.getUnitNo();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		if (true) {
			Map<String, String> codeMap = codeTypeService.findByCodeType("lms1405s02_proPerty");
			String l120m01a_docstatus = "";
			if(clsService.is_function_on_codetype("c340m01a_approved_case")){
				l120m01a_docstatus = CreditDocStatusEnum.海外_已核准.getCode();
			}
			for(Map<String, Object> data_row : eloandbBASEService.listCntr_c340m01a_ctrType_1_match(l120m01a_docstatus, brNo, custId, dupNo)){
				Map<String, Object> row = new HashMap<String, Object>();
				row.put("caseMainId", MapUtils.getString(data_row, "CASEMAINID"));
				row.put("caseNo", Util.toSemiCharString(MapUtils.getString(data_row, "CASENO")));
				Date d_endDate = (Date)MapUtils.getObject(data_row, "ENDDATE");
				row.put("endDate", Util.trim(TWNDate.toAD(d_endDate)));
				
				row.put("tabMainId", MapUtils.getString(data_row, "TABMAINID"));
				row.put("custId", MapUtils.getString(data_row, "CUSTID"));
				row.put("dupNo", MapUtils.getString(data_row, "DUPNO"));
				row.put("custName", MapUtils.getString(data_row, "CUSTNAME"));
				row.put("cntrNo", MapUtils.getString(data_row, "CNTRNO"));
				String raw_property = MapUtils.getString(data_row, "PROPERTY"); // 由 代碼 轉 說明
				String[] proretys = Util.trim(raw_property).split(
						UtilConstants.Mark.SPILT_MARK);
				StringBuffer temp = new StringBuffer();
				for (String value : proretys) {
					temp.append(temp.length() > 0 ? "、" : "");
					temp.append(Util.trim(codeMap.get(value)));
				}
				String property = temp.toString();				
				row.put("property", property);
				BigDecimal currentApplyAmt = (BigDecimal)MapUtils.getObject(data_row, "CURRENTAPPLYAMT");
				row.put("currentApplyAmt", NumConverter.addComma(LMSUtil.pretty_numStr(currentApplyAmt)));
				//---
				list.add(row);
			}
		} 
		return new CapMapGridResult(list, list.size());
	}

	public CapMapGridResult queryCntrNo_ctrType_2(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		String brNo = user.getUnitNo();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		if (true) {
			Map<String, String> codeMap = codeTypeService.findByCodeType("lms1405s02_proPerty");
			String l120m01a_docstatus = "";
			if(clsService.is_function_on_codetype("c340m01a_approved_case")){
				l120m01a_docstatus = CreditDocStatusEnum.海外_已核准.getCode();
			}
			for(Map<String, Object> data_row : eloandbBASEService.listCntr_c340m01a_ctrType_2_match(l120m01a_docstatus, brNo, custId, dupNo)){
				Map<String, Object> row = new HashMap<String, Object>();
				row.put("caseMainId", MapUtils.getString(data_row, "CASEMAINID"));
				row.put("caseNo", Util.toSemiCharString(MapUtils.getString(data_row, "CASENO")));
				Date d_endDate = (Date)MapUtils.getObject(data_row, "ENDDATE");
				row.put("endDate", Util.trim(TWNDate.toAD(d_endDate)));
				
				row.put("tabMainId", MapUtils.getString(data_row, "TABMAINID"));
				row.put("custId", MapUtils.getString(data_row, "CUSTID"));
				row.put("dupNo", MapUtils.getString(data_row, "DUPNO"));
				row.put("custName", MapUtils.getString(data_row, "CUSTNAME"));
				row.put("cntrNo", MapUtils.getString(data_row, "CNTRNO"));
				String raw_property = MapUtils.getString(data_row, "PROPERTY"); // 由 代碼 轉 說明
				String[] proretys = Util.trim(raw_property).split(
						UtilConstants.Mark.SPILT_MARK);
				StringBuffer temp = new StringBuffer();
				for (String value : proretys) {
					temp.append(temp.length() > 0 ? "、" : "");
					temp.append(Util.trim(codeMap.get(value)));
				}
				String property = temp.toString();				
				row.put("property", property);
				BigDecimal currentApplyAmt = (BigDecimal)MapUtils.getObject(data_row, "CURRENTAPPLYAMT");
				row.put("currentApplyAmt", NumConverter.addComma(LMSUtil.pretty_numStr(currentApplyAmt)));
				//---
				list.add(row);
			}
		} 
		return new CapMapGridResult(list, list.size());
	}
	
	public CapMapGridResult queryCntrNo_ctrType_3(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		String brNo = user.getUnitNo();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		if (true) {
			Map<String, String> codeMap = codeTypeService.findByCodeType("lms1405s02_proPerty");
			String l120m01a_docstatus = "";
			if(clsService.is_function_on_codetype("c340m01a_approved_case")){
				l120m01a_docstatus = CreditDocStatusEnum.海外_已核准.getCode();
			}
			for(Map<String, Object> data_row : eloandbBASEService.listCntr_c340m01a_ctrType_3_match(l120m01a_docstatus, brNo, custId, dupNo)){
				Map<String, Object> row = new HashMap<String, Object>();
				row.put("caseMainId", MapUtils.getString(data_row, "CASEMAINID"));
				row.put("caseNo", Util.toSemiCharString(MapUtils.getString(data_row, "CASENO")));
				Date d_endDate = (Date)MapUtils.getObject(data_row, "ENDDATE");
				row.put("endDate", Util.trim(TWNDate.toAD(d_endDate)));
				
				row.put("tabMainId", MapUtils.getString(data_row, "TABMAINID"));
				row.put("custId", MapUtils.getString(data_row, "CUSTID"));
				row.put("dupNo", MapUtils.getString(data_row, "DUPNO"));
				row.put("custName", MapUtils.getString(data_row, "CUSTNAME"));
				row.put("cntrNo", MapUtils.getString(data_row, "CNTRNO"));
				String raw_property = MapUtils.getString(data_row, "PROPERTY"); // 由 代碼 轉 說明
				String[] proretys = Util.trim(raw_property).split(
						UtilConstants.Mark.SPILT_MARK);
				StringBuffer temp = new StringBuffer();
				for (String value : proretys) {
					temp.append(temp.length() > 0 ? "、" : "");
					temp.append(Util.trim(codeMap.get(value)));
				}
				String property = temp.toString();				
				row.put("property", property);
				BigDecimal currentApplyAmt = (BigDecimal)MapUtils.getObject(data_row, "CURRENTAPPLYAMT");
				row.put("currentApplyAmt", NumConverter.addComma(LMSUtil.pretty_numStr(currentApplyAmt)));
				//---
				list.add(row);
			}
		} 
		return new CapMapGridResult(list, list.size());
	}
	
	public CapMapGridResult queryCntrNo_ctrType_A(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String brNo = user.getUnitNo();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();


		Map<String, String> codeMap = codeTypeService.findByCodeType("lms1405s02_proPerty");
		String l120m01a_docstatus = "";
		l120m01a_docstatus = CreditDocStatusEnum.海外_已核准.getCode();
		for (Map<String, Object> data_row : eloandbBASEService.listCntr_c340m01a_ctrType_A_match(l120m01a_docstatus,
				brNo, custId, dupNo)) {
			Map<String, Object> row = new HashMap<String, Object>();
			row.put("caseMainId", MapUtils.getString(data_row, "CASEMAINID"));
			row.put("caseNo", Util.toSemiCharString(MapUtils.getString(data_row, "CASENO")));
			Date d_endDate = (Date) MapUtils.getObject(data_row, "ENDDATE");
			row.put("endDate", Util.trim(TWNDate.toAD(d_endDate)));

			row.put("tabMainId", MapUtils.getString(data_row, "TABMAINID"));
			row.put("custId", MapUtils.getString(data_row, "CUSTID"));
			row.put("dupNo", MapUtils.getString(data_row, "DUPNO"));
			row.put("custName", MapUtils.getString(data_row, "CUSTNAME"));
			row.put("cntrNo", MapUtils.getString(data_row, "CNTRNO"));
			String raw_property = MapUtils.getString(data_row, "PROPERTY"); // 由 代碼 轉 說明
			String[] proretys = Util.trim(raw_property).split(UtilConstants.Mark.SPILT_MARK);
			StringBuffer temp = new StringBuffer();
			for (String value : proretys) {
				temp.append(temp.length() > 0 ? "、" : "");
				temp.append(Util.trim(codeMap.get(value)));
			}
			String property = temp.toString();
			row.put("property", property);
			BigDecimal currentApplyAmt = (BigDecimal) MapUtils.getObject(data_row, "CURRENTAPPLYAMT");
			row.put("currentApplyAmt", NumConverter.addComma(LMSUtil.pretty_numStr(currentApplyAmt)));
			//---
			list.add(row);
		}

		return new CapMapGridResult(list, list.size());
	}

	public CapMapGridResult queryCntrNo_ctrType_B(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String brNo = user.getUnitNo();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();


		Map<String, String> codeMap = codeTypeService.findByCodeType("lms1405s02_proPerty");
		String l120m01a_docstatus = "";
		l120m01a_docstatus = CreditDocStatusEnum.海外_已核准.getCode();
		for (Map<String, Object> data_row : eloandbBASEService.listCntr_c340m01a_ctrType_B_match(l120m01a_docstatus,
				brNo, custId, dupNo)) {
			Map<String, Object> row = new HashMap<String, Object>();
			row.put("caseMainId", MapUtils.getString(data_row, "CASEMAINID"));
			row.put("caseNo", Util.toSemiCharString(MapUtils.getString(data_row, "CASENO")));
			Date d_endDate = (Date) MapUtils.getObject(data_row, "ENDDATE");
			row.put("endDate", Util.trim(TWNDate.toAD(d_endDate)));

			row.put("tabMainId", MapUtils.getString(data_row, "TABMAINID"));
			row.put("custId", MapUtils.getString(data_row, "CUSTID"));
			row.put("dupNo", MapUtils.getString(data_row, "DUPNO"));
			row.put("custName", MapUtils.getString(data_row, "CUSTNAME"));
			row.put("cntrNo", MapUtils.getString(data_row, "CNTRNO"));
			String raw_property = MapUtils.getString(data_row, "PROPERTY"); // 由 代碼 轉 說明
			String[] proretys = Util.trim(raw_property).split(UtilConstants.Mark.SPILT_MARK);
			StringBuffer temp = new StringBuffer();
			for (String value : proretys) {
				temp.append(temp.length() > 0 ? "、" : "");
				temp.append(Util.trim(codeMap.get(value)));
			}
			String property = temp.toString();
			row.put("property", property);
			BigDecimal currentApplyAmt = (BigDecimal) MapUtils.getObject(data_row, "CURRENTAPPLYAMT");
			row.put("currentApplyAmt", NumConverter.addComma(LMSUtil.pretty_numStr(currentApplyAmt)));
			//---
			list.add(row);
		}

		return new CapMapGridResult(list, list.size());
	}

	public CapMapGridResult queryCntrNo_ctrType_S(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String brNo = user.getUnitNo();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();


		Map<String, String> codeMap = codeTypeService.findByCodeType("lms1405s02_proPerty");
		Map<String, String> codeMap_custPos = codeTypeService.findByCodeType("L140S01A_custPos");
		String l120m01a_docstatus = "";
		l120m01a_docstatus = CreditDocStatusEnum.海外_已核准.getCode();
		for (Map<String, Object> data_row : eloandbBASEService.listCntr_c340m01a_ctrType_S_match(l120m01a_docstatus,
				brNo, custId, dupNo)) {
			Map<String, Object> row = new HashMap<String, Object>();
			row.put("caseMainId", MapUtils.getString(data_row, "CASEMAINID"));
			row.put("caseNo", Util.toSemiCharString(MapUtils.getString(data_row, "CASENO")));
			Date d_endDate = (Date) MapUtils.getObject(data_row, "ENDDATE");
			row.put("endDate", Util.trim(TWNDate.toAD(d_endDate)));

			row.put("tabMainId", MapUtils.getString(data_row, "TABMAINID"));
			row.put("custId", MapUtils.getString(data_row, "CUSTID"));
			row.put("dupNo", MapUtils.getString(data_row, "DUPNO"));
			row.put("custName", MapUtils.getString(data_row, "CUSTNAME"));
			row.put("cntrNo", MapUtils.getString(data_row, "CNTRNO"));
			String raw_property = MapUtils.getString(data_row, "PROPERTY"); // 由 代碼 轉 說明
			String[] proretys = Util.trim(raw_property).split(UtilConstants.Mark.SPILT_MARK);
			StringBuffer temp = new StringBuffer();
			for (String value : proretys) {
				temp.append(temp.length() > 0 ? "、" : "");
				temp.append(Util.trim(codeMap.get(value)));
			}
			String property = temp.toString();
			row.put("property", property);
			BigDecimal currentApplyAmt = (BigDecimal) MapUtils.getObject(data_row, "CURRENTAPPLYAMT");
			row.put("currentApplyAmt", NumConverter.addComma(LMSUtil.pretty_numStr(currentApplyAmt)));
			row.put("s01a_custId", MapUtils.getString(data_row, "S01A_CUSTID"));
			row.put("s01a_dupNo", MapUtils.getString(data_row, "S01A_DUPNO"));
			row.put("s01a_custName", MapUtils.getString(data_row, "S01A_CUSTNAME"));
			String custPos = MapUtils.getString(data_row, "S01A_CUSTPOS");
			row.put("s01a_custPos", custPos+"-"+LMSUtil.getDesc(codeMap_custPos, custPos));

			//---
			list.add(row);
		}

		return new CapMapGridResult(list, list.size());
	}

	public CapMapGridResult queryCntrNo_ctrType_L(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String brNo = user.getUnitNo();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();


		Map<String, String> codeMap = codeTypeService.findByCodeType("lms1405s02_proPerty");
		String l120m01a_docstatus = "";
		l120m01a_docstatus = CreditDocStatusEnum.海外_已核准.getCode();
		for (Map<String, Object> data_row : eloandbBASEService.listCntr_c340m01a_ctrType_L_match(l120m01a_docstatus,
				brNo, custId, dupNo)) {
			Date d_endDate = (Date) MapUtils.getObject(data_row, "ENDDATE");
			Date laborStartDate=Util.parseDate(ClsUtility.get_labor_bailout_2021_since_ts());
			if(d_endDate.compareTo(laborStartDate)>=0){
				Map<String, Object> row = new HashMap<String, Object>();
				row.put("caseMainId", MapUtils.getString(data_row, "CASEMAINID"));
				row.put("caseNo", Util.toSemiCharString(MapUtils.getString(data_row, "CASENO")));
				row.put("endDate", Util.trim(TWNDate.toAD(d_endDate)));

				row.put("tabMainId", MapUtils.getString(data_row, "TABMAINID"));
				row.put("custId", MapUtils.getString(data_row, "CUSTID"));
				row.put("dupNo", MapUtils.getString(data_row, "DUPNO"));
				row.put("custName", MapUtils.getString(data_row, "CUSTNAME"));
				row.put("cntrNo", MapUtils.getString(data_row, "CNTRNO"));
				String raw_property = MapUtils.getString(data_row, "PROPERTY"); // 由 代碼 轉 說明
				String[] proretys = Util.trim(raw_property).split(UtilConstants.Mark.SPILT_MARK);
				StringBuffer temp = new StringBuffer();
				for (String value : proretys) {
					temp.append(temp.length() > 0 ? "、" : "");
					temp.append(Util.trim(codeMap.get(value)));
				}
				String property = temp.toString();
				row.put("property", property);
				BigDecimal currentApplyAmt = (BigDecimal) MapUtils.getObject(data_row, "CURRENTAPPLYAMT");
				row.put("currentApplyAmt", NumConverter.addComma(LMSUtil.pretty_numStr(currentApplyAmt)));
				//---
				list.add(row);
			}
		}

		return new CapMapGridResult(list, list.size());
	}
	
	public CapMapGridResult accountQuery(ISearch pageSetting,
			PageParameters params) throws CapException {
		String idDupList = Util.trim(params.getString("accountQuery_idDupList"));
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		if(Util.isNotEmpty(idDupList)){
			for(String idDup : idDupList.split("\\|")){			
				String[] arr = idDup.split("\\-");
				if(arr!=null && arr.length==2){
					//ok
				}else{
					continue;
				}
				String custId = Util.trim(arr[0]);	
				String dupNo = Util.trim(arr[1]);
				
				String custName = custId+"-"+dupNo;
				Map<String, Object> latestData = iCustomerService.findByIdDupNo(custId, dupNo);
				if(latestData!=null){
					custName = Util.trim(latestData.get("CNAME"));
				}
				
				for (Map<String, Object> map : dwdbBASEService.findDW_IDDP_DPF_Seqno_ByCustId(custId, dupNo)) {
					StringBuilder sb = new StringBuilder();
					sb.append(Util.trim(map.get("BRNO")));
					sb.append(Util.trim(map.get("APCD")));
					sb.append(Util.trim(map.get("SEQNO")));
					map.put("Account", sb.toString());
					map.put("DP_custId", custId);
					map.put("DP_dupNo", dupNo);
					map.put("DP_custName", custName);
					//~~~~~~~~~~~~~~~~~~~
					list.add(map);
				}
			}
		}

		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);		
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
		
	}

	public CapGridResult queryAttch(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String fieldId = params.getString("fieldId");

		boolean needCngName = params.getBoolean("needCngName");
		boolean needBranch = params.getBoolean("needBranch");
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if(Util.isNotEmpty(Util.trim(fieldId))){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId", fieldId);	
		}
		
		/**
		 * 1. 由系統產生一個 file 2. 下載file,改內容,重上傳
		 * 
		 * 此時, 在 /elnfs 有 2 個檔案 在 DB 也有 2 筆記錄select * from lms.bdocfile where
		 * oid in ( 'AAA','BBB') 但在 lms.bdocfile 的 DELETEDTIME. 1筆有刪除時間, 另1筆null
		 * → 用 $.capFileDownload(...) 去下載時, 只抓得到最新的
		 * 
		 * SimpleFileUploadHandler.java search : "deleteDup"
		 * 
		 * 所以多加上條件 deletedTime is null 讓 grid 呈現的 data row,都是可以下載的
		 */
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		// 此 method 去查 lms.bdocfile. 該table 只有 mainId,fieldId
		Page<DocFile> page = relatedAccountService.queryfile(pageSetting,
				needCngName, needBranch);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	public CapGridResult queryPloanUploadGrid(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		boolean needCngName = false;
		boolean needBranch = false;
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime","");
		// 此 method 去查 lms.bdocfile. 該table 只有 mainId,fieldId
		Page<DocFile> page = relatedAccountService.queryfile(pageSetting,
				needCngName, needBranch);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}
}
