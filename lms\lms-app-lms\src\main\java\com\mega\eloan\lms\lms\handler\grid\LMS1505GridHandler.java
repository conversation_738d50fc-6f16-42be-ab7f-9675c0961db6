/* 
 *  LMS1505GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.grid;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;

import javax.annotation.Resource;


import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.ElsUserDao;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.ElsUser;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.lms.pages.LMS1505M01Page;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1505Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L150M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 小放會會議記錄
 * </pre>
 * 
 * @since 2011/9/6
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/6,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1505gridhandler")
public class LMS1505GridHandler extends AbstractGridHandler {

	@Resource
	LMS1505Service lms1505Service;

	@Resource
	LMS1205Service lms1205Service;

	@Resource
	UserInfoService userInfoService;
	@Resource
	ElsUserDao elsUserDao;

	@Resource
	UserInfoService userservice;

	@Resource
	BranchService branchService;

	/**
	 * 查詢小放會的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult query(ISearch pageSetting, PageParameters params) throws CapException {
		String createType = Util.trim(params.getString("createType"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l150a01a.authUnit", MegaSSOSecurityContext.getUnitNo());
		if (Util.isEmpty(createType)
				|| UtilConstants.Meeting.CreateType.海外.equals(createType)) {
			// 海外可能是空白或者是1或者null ，所以才需要此條件
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "createType",
							UtilConstants.Meeting.CreateType.海外),
					new SearchModeParameter(SearchMode.IS_NULL, "createType",
							""));
		} else {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"createType", createType);
		}

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<L150M01A> page = lms1505Service.findPage(pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 取得案件簽報書的案由
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryCase(ISearch pageSetting, PageParameters params) throws CapException {
		String createType = Util.trim(params.getString("createType"));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (Util.isEmpty(createType)
				|| UtilConstants.Meeting.CreateType.海外.equals(createType)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
					TypCdEnum.海外.getCode());

		} else {

			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
					TypCdEnum.海外.getCode());
			String docType = "";
			if (UtilConstants.Meeting.CreateType.企金.equals(createType)) {
				docType = UtilConstants.Casedoc.DocType.企金;
			} else {
				docType = UtilConstants.Casedoc.DocType.個金;
			}
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
					docType);
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "gist", "");
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// CreditDocStatusEnum.海外_已核准.getCode());
		Page<? extends GenericBean> page = lms1205Service.findPage(
				L120M01A.class, pageSetting);
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A l120m01a : l120m01as) {
			l120m01a.setDocStatus(getMessage("docStatus."
					+ l120m01a.getDocStatus()));
			// 設定顯示名稱 使用者id+重複序號+名稱
			l120m01a.setCustId(StrUtils.concat(l120m01a.getCustId(),
					l120m01a.getDupNo(), " ", l120m01a.getCustName()));
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 取得小放會的出席人員
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryPeople(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "brno",
				user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "leaveFlag",
				"N");
		Page<ElsUser> page = elsUserDao.findPage(pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 篩選L120M01AGrid 外部的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL150m01a1(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1505M01Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));

		HashMap<String, String> replaceMap = new HashMap<String, String>();
		replaceMap.put(" ", "%");

		// 基本條件與GRID相同
		String createType = Util.trim(params.getString("createType"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l150a01a.authUnit", MegaSSOSecurityContext.getUnitNo());
		if (Util.isEmpty(createType)
				|| UtilConstants.Meeting.CreateType.海外.equals(createType)) {
			// 海外可能是空白或者是1或者null ，所以才需要此條件
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "createType",
							UtilConstants.Meeting.CreateType.海外),
					new SearchModeParameter(SearchMode.IS_NULL, "createType",
							""));
		} else {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"createType", createType);
		}

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");

		// 下面為篩選條件*****************************************
		Date meetingDateS = null;
		Date meetingDateE = null;
		meetingDateS = Util.parseDate(Util.nullToSpace(params
				.getString("meetingDateS")));
		meetingDateE = Util.parseDate(Util.nullToSpace(params
				.getString("meetingDateE")));
		if (Util.isNotEmpty(meetingDateS) && Util.isNotEmpty(meetingDateE)) {
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
					"meetingDate", new Object[] { meetingDateS, meetingDateE } );
		}

		String meetingPlace = Util.trim(params.getString("meetingPlace"));
		if (Util.isNotEmpty(meetingPlace)) {
			pageSetting.addSearchModeParameters(
					SearchMode.LIKE,
					"meetingPlace",
					new StringBuffer("%").append(
							Util.replaceWordContent(meetingPlace, replaceMap))
							.append("%"));
		}

		String chairMan = Util.trim(params.getString("chairMan"));
		if (Util.isNotEmpty(chairMan)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "chairMan",
					chairMan);
		}

		String lawsBoss = Util.trim(params.getString("lawsBoss"));
		if (Util.isNotEmpty(lawsBoss)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "lawsBoss",
					lawsBoss);
		}

		String accounting = Util.trim(params.getString("accounting"));
		if (Util.isNotEmpty(accounting)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"accounting", accounting);
		}

		String recorder = Util.trim(params.getString("recorder"));
		if (Util.isNotEmpty(recorder)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "recorder",
					recorder);
		}

		String present = Util.trim(params.getString("present"));
		if (Util.isNotEmpty(present)) {
			pageSetting.addSearchModeParameters(
					SearchMode.LIKE,
					"present",
					new StringBuffer("%").append(
							Util.replaceWordContent(present, replaceMap))
							.append("%"));
		}

		String gist = Util.trim(params.getString("gist"));
		if (Util.isNotEmpty(gist)) {
			pageSetting.addSearchModeParameters(
					SearchMode.LIKE,
					"gist",
					new StringBuffer("%").append(
							Util.replaceWordContent(gist, replaceMap)).append(
							"%"));
		}

		// 注意DB欄位是description 但是畫面欄位是 ckeDescription
		String ckeDescription = Util.trim(params.getString("ckeDescription"));
		if (Util.isNotEmpty(ckeDescription)) {
			pageSetting.addSearchModeParameters(
					SearchMode.LIKE,
					"description",
					new StringBuffer("%")
							.append(Util.replaceWordContent(ckeDescription,
									replaceMap)).append("%"));
		}

		String resolution = Util.trim(params.getString("resolution"));
		if (Util.isNotEmpty(resolution)) {
			pageSetting.addSearchModeParameters(
					SearchMode.LIKE,
					"resolution",
					new StringBuffer("%").append(
							Util.replaceWordContent(resolution, replaceMap))
							.append("%"));
		}

		Page<L150M01A> page = lms1505Service.findPage(pageSetting);

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		// result.setPage(1);
		return result;

	}
}
