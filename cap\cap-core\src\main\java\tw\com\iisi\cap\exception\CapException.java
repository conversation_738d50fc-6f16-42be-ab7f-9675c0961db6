/*
 * CapException.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.exception;

/**
 * <pre>
 * 設置額外錯誤資訊
 * extends Exception
 * </pre>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/12,iristu,new
 *          </ul>
 */
@SuppressWarnings({ "serial" })
public class CapException extends Exception {

    @SuppressWarnings("rawtypes")
    Class causeClass;

    /**
     * 錯誤額外資訊
     */
    private Object extraInformation;

    /**
     * Instantiates a new cap exception.
     */
    public CapException() {
        super();
    }

    /**
     * Instantiates a new cap exception.
     * 
     * @param message
     *            the message
     * @param causeClass
     *            the cause class
     */
    @SuppressWarnings("rawtypes")
    public CapException(String message, Class causeClass) {
        super(message);
        this.causeClass = causeClass;
    }

    /**
     * Instantiates a new cap exception.
     * 
     * @param cause
     *            the cause
     * @param causeClass
     *            the cause class
     */
    @SuppressWarnings("rawtypes")
    public CapException(Throwable cause, Class causeClass) {
        super(cause);
        this.causeClass = causeClass;
    }

    /**
     * Instantiates a new cap exception.
     * 
     * @param message
     *            the message
     * @param cause
     *            the cause
     * @param causeClass
     *            the cause class
     */
    @SuppressWarnings("rawtypes")
    public CapException(String message, Throwable cause, Class causeClass) {
        super(message, cause);
        this.causeClass = causeClass;
    }

    /**
     * get cause class
     * 
     * @return Class
     */
    @SuppressWarnings("rawtypes")
    public Class getCauseClass() {
        return causeClass;
    }

    /**
     * set cause class
     * 
     * @param causeClass
     *            the cause class
     */
    @SuppressWarnings("rawtypes")
    public void setCauseSource(Class causeClass) {
        this.causeClass = causeClass;
    }

    /**
     * 取得額外訊息
     * 
     * @return
     */
    public Object getExtraInformation() {
        return extraInformation;
    }

    /**
     * 設置額外訊息
     * 
     * @param extraInformation
     * @return
     */
    public CapException setExtraInformation(Object extraInformation) {
        this.extraInformation = extraInformation;
        return this;
    }

}
