/* 
 * L120S11ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L120S11ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120M01J;
import com.mega.eloan.lms.model.L120S11A;

/** 本案借款人同時為其他授信戶應收帳款債務人之額度資料 **/
@Repository
public class L120S11ADaoImpl extends LMSJpaDao<L120S11A, String> implements
		L120S11ADao {

	@Override
	public L120S11A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S11A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		
		search.addOrderBy("printSeq", false);
		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		
		
		List<L120S11A> list = createQuery(search).getResultList();

		

		return list;
	}

	@Override
	public L120S11A findByUniqueKey(String mainId, String custId, String dupNo,
			String custId2, String dupNo2) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (custId2 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId2",
					custId2);
		if (dupNo2 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo2", dupNo2);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public L120S11A findByMainIdCustIdAndCustId2(String mainId, String custId,
			String dupNo, String custId2, String dupNo2) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (custId2 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId2",
					custId2);
		if (dupNo2 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo2", dupNo2);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L120S11A> findByMainIdAndCustId(String mainId, String custId,
			String dupNo) {
		ISearch search = createSearchTemplete();
		List<L120S11A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		search.addOrderBy("printSeq", false);
		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S11A> findByMainIdCustIdAndRelType(String mainId,
			String custId, String dupNo, Integer relType) {
		ISearch search = createSearchTemplete();
		List<L120S11A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (relType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "relType",
					relType);

		search.addOrderBy("printSeq", false);
		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public L120S11A findByMainIdCustIdItemSeq(String mainId, String custId,
			String dupNo, Integer itemSeq) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (itemSeq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemSeq",
					itemSeq);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L120S11A> findByMainIdCustId2(String mainId, String custId2,
			String dupNo2) {
		ISearch search = createSearchTemplete();
		List<L120S11A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);

		if (custId2 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId2",
					custId2);
		if (dupNo2 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo2", dupNo2);

	
		search.addOrderBy("printSeq", false);
		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

}