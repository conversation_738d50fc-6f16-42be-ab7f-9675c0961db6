/* 
 * DEB2010R03Page.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.lms.panels.LMS1115S02PanelB5;
import com.mega.eloan.lms.rpt.panels.LMS9550R02Panel;

/**
 * <pre>
 * 報送作業 - 每日傳送卡務檔案作業
 * </pre>
 * 
 * @since 2012/7/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/16,<PERSON><PERSON><PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping(path = "/rpt/lms9550r01")
public class LMS9550R01Page extends AbstractEloanForm {

	public LMS9550R01Page() {
		super();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 */
	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		
		// 依權限設定button
		
//		if (StringUtils.isNotBlank(parameters.getString("branchVar"))) {
//			add(new DEB9910R03Panel("_funcCtx", parameters, getDomainClass()));
//		} else {
//			add(new LMS9550R01Panel("_funcCtx", parameters, getDomainClass()));
//		}
		
		new LMS9550R02Panel("_funcCtx").processPanelData(model, params);
		
		renderJsI18N(LMS9550R01Page.class, new String[] { "msg", "grid" });
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
}
