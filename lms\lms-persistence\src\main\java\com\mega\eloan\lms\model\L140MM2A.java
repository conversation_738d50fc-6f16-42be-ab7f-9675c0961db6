/* 
 * L140MM2A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

import tw.com.iisi.cap.model.IDataObject;

/** 天然及重大災害受災戶住宅補貼主檔 **/
@NamedEntityGraph(name = "L140MM2A-entity-graph", attributeNodes = { @NamedAttributeNode("l140s02l") })
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140MM2A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L140MM2A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;


	/** 序號 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="SEQ", columnDefinition="DEC(5,0)")
	private Integer seq;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 幣別 **/
	@Size(max=3)
	@Column(name="LOANCURR", length=3, columnDefinition="CHAR(3)")
	private String loanCurr;

	/** 金額 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="LOANAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal loanAmt;

	/** 貸款種類 **/
	@Size(max=2)
	@Column(name="LOANTYPE", length=1, columnDefinition="CHAR(01)")
	private String loanType;

	/** 
	 * 天然及重大災害種類<p/>
	 * {01:0206震災受災戶}
	 */
	@Size(max=2)
	@Column(name="DISASTYPE", length=2, columnDefinition="VARCHAR(2)")
	private String disasType;


	/** 取得序號 **/
	public Integer getSeq() {
		return this.seq;
	}
	/** 設定序號 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得幣別 **/
	public String getLoanCurr() {
		return this.loanCurr;
	}
	/** 設定幣別 **/
	public void setLoanCurr(String value) {
		this.loanCurr = value;
	}

	/** 取得金額 **/
	public BigDecimal getLoanAmt() {
		return this.loanAmt;
	}
	/** 設定金額 **/
	public void setLoanAmt(BigDecimal value) {
		this.loanAmt = value;
	}

	/** 取得貸款種類 **/
	public String getLoanType() {
		return this.loanType;
	}
	/** 設定貸款種類 **/
	public void setLoanType(String value) {
		this.loanType = value;
	}

	/** 
	 * 取得天然及重大災害種類<p/>
	 * {01:0206震災受災戶}
	 */
	public String getDisasType() {
		return this.disasType;
	}
	/**
	 *  設定天然及重大災害種類<p/>
	 *  {01:0206震災受災戶}
	 **/
	public void setDisasType(String value) {
		this.disasType = value;
	}
	
	/**
	 * join L140S02L
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "seq", referencedColumnName = "seq", nullable = false, insertable = false, updatable = false)})
	private L140S02L l140s02l;

	public void setL140s02l(L140S02L l140s02l) {
		this.l140s02l = l140s02l;
	}

	public L140S02L getL140s02l() {
		return l140s02l;
	}
}
