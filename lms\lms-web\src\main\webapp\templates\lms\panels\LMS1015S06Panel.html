<html xmlns="http://www.w3.org/1999/xhtml" 
        xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
			<style type="text/css">   
			tr { vertical-align: top; }
			</style>
			<script type="text/javascript">
 				loadScript('pagejs/lms/LMS1015S06Panel');
			</script>

			<!-- ====================================================================== -->
        	<fieldset>
                <legend>
                    <th:block th:text="#{'tab.06'}">主觀評等更新</th:block>
                </legend>
				<div>					
					<th:block th:text="#{'tab06.desc01'}">說明：於決定是否調整評等前，請注意必須確實有充分理由始得調整評等。</th:block>
					<p>					
					<fieldset style='margin-left:15px;'>
						<legend><span id='borrower_list'></span></legend>
						<div>
							<table border='1' class='tb2' width="90%">	
								<tr>
									<td class='hd2'><th:block th:text="#{'label.custPos'}"></th:block>&nbsp;</td>
									<td><span id='c120_custPos'></span>&nbsp;</td>
									<td class='hd2'><th:block th:text="#{'label.custId'}"></th:block>&nbsp;</td>
									<td><span id='c120_custId'></span>&nbsp;</td>
									<td class='hd2'><th:block th:text="#{'label.custName'}"></th:block>&nbsp;</td>
									<td><span id='c120_custName'></span>&nbsp;</td>
								</tr>
							</table>
							<table border='1' class='tb2' width="90%">						
								<tr>
									<td class='hd2' colspan='2'><th:block th:text="#{'tab06.desc02'}">模型評等輸出摘要</th:block></td>							
								</tr>
								<tr>
									<td width='40%' class=''><th:block th:text="#{'C121M01B.pRating'}">初始評等</th:block></td>
									<td><span id="pRating" ></span>&nbsp;</td>
								</tr>
								<tr>
									<td class=''><th:block th:text="#{'C121M01B.sRating'}">獨立評等</th:block>(<th:block th:text="#{'tab07.note.sRating'}">考慮聯徵負面資訊後</th:block>)</td>
									<td><span id="sRating" ></span>&nbsp;</td>
								</tr>
								<tr>
									<td class=''><th:block th:text="#{'C121M01B.sprtRating'}">支援評等</th:block>(<th:block th:text="#{'tab07.note.sprtRating'}">考慮聯徵J10評分後</th:block>)</td>
									<td><span id="sprtRatingDesc" ></span>&nbsp;<input type="hidden" id="sprtRating" name="sprtRating"> </td>
								</tr>
							</table>
							<br>
							<table border='0' width="90%">						
								<tr>
									<td>1.</td>
									<td><th:block th:text="#{'tab06.desc03'}">考慮聯徵J10評分後之支援評等是否符合你的直覺?</th:block>&nbsp;
									<input type='radio' id='noAdj' name='noAdj' codetype='YesNo' class='required'>
									<br>※<th:block th:text="#{'tab06.desc04'}">注意事項：點選「是」者則不必進行主觀評等更新，亦不必說明評等更新之理由。</th:block>&nbsp;
									</td>							
								</tr>
								<tr class='tr_adjustStatus12'>
									<td>2.</td>
									<td>
										<th:block th:text="#{'tab06.desc05'}">進行主觀評等更新</th:block>：
										<label>
											<input type='radio' id='adjustStatus' name='adjustStatus' value='1'>
											<th:block th:text="#{'tab06.desc06'}">調升</th:block>
										</label>
										<label>
											<input type='radio' id='adjustStatus' name='adjustStatus' value='2'>
											<th:block th:text="#{'tab06.desc07'}">調降</th:block>
										</label>
										&nbsp;&nbsp;<select id='adjRating' name='adjRating'>
											<option value=''>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</option>											
											<option value='1'>&nbsp;&nbsp;1&nbsp;&nbsp;</option>
											<option value='2'>&nbsp;&nbsp;2&nbsp;&nbsp;</option>
											<option value='3'>&nbsp;&nbsp;3&nbsp;&nbsp;</option>
											<option value='4'>&nbsp;&nbsp;4&nbsp;&nbsp;</option>
											<option value='5'>&nbsp;&nbsp;5&nbsp;&nbsp;</option>
											<option value='6'>&nbsp;&nbsp;6&nbsp;&nbsp;</option>
											<option value='7'>&nbsp;&nbsp;7&nbsp;&nbsp;</option>
											<option value='8'>&nbsp;&nbsp;8&nbsp;&nbsp;</option>
											<option value='9'>&nbsp;&nbsp;9&nbsp;&nbsp;</option>
										</select> 
										<th:block th:text="#{'tab06.desc08'}">等</th:block>
									</td>
								</tr>
								<tr class='tr_adjustStatus12'>
									<td>3.</td>
									<td><th:block th:text="#{'tab06.desc09'}"></th:block>&nbsp;
									<div id='div_adjustStatus_1' style='display:none; padding-top:1px; padding-left:20px;'>
										<th:block th:text="#{'tab06.desc10'}"></th:block>&nbsp;
										<p>
										<label style="letter-spacing:0px;cursor:pointer;"><input type="radio" id="adjustFlag" name="adjustFlag" value="1" class="required" /><th:block th:text="#{'C121M01B.adjustFlag.1'}">淨資產</th:block></label>
										<label style="letter-spacing:0px;cursor:pointer;"><input type="radio" id="adjustFlag" name="adjustFlag" value="2" class="required" /><th:block th:text="#{'C121M01B.adjustFlag.2'}">職業</th:block></label>
										<label style="letter-spacing:0px;cursor:pointer;"><input type="radio" id="adjustFlag" name="adjustFlag" value="3" class="required" /><th:block th:text="#{'C121M01B.adjustFlag.3'}">其它</th:block></label>
										<br/>
										<table>
											<tr class='tr_adjustFlag tr_adjustFlag_1' style='display:none'>
												<td>
													<font color='#000066'><th:block th:text="#{'tab06.desc11'}"></th:block></font>
													<br><span class='red'><th:block th:text="#{'tab06.desc12'}"></th:block></span>&nbsp;
												</td>
											</tr>
											<tr class='tr_adjustFlag tr_adjustFlag_2' style='display:none'>
												<td>
													<font color='#000066'><th:block th:text="#{'tab06.desc13'}"></th:block></font>
													<br><span class='red'><th:block th:text="#{'tab06.desc14'}"></th:block></span>&nbsp;
												</td>
											</tr>
											<tr class='tr_adjustFlag tr_adjustFlag_3' style='display:none'>
												<td>
													<font color='#000066'><th:block th:text="#{'tab06.desc15'}"></th:block></font>
													<br><span class='red'><th:block th:text="#{'tab06.desc16'}"></th:block></span>&nbsp;
												</td>
											</tr>
										</table>
										</p>								
									</div>
									<div>
									<textarea id="adjustReason" name="adjustReason" cols="80" Rows="3" class="required" readonly="readonly" style='background-color:lightgray; '  maxlength="300" maxlengthC="100"  >										
									</textarea>
									</div>
									</td>
								</tr>
								<tr class='tr_adjustStatus12'>
									<td>4.</td>
									<td><th:block th:text="#{'tab06.desc17.v2'}"></th:block>&nbsp;</td>
								</tr>
								<tr class='tr_adjustStatus12'>
									<td>5.</td>
									<td><th:block th:text="#{'tab06.desc19'}"></th:block>&nbsp;
									
										<button type="button" id="uploadOverrideFile" onclick="_JP_uploadOverrideReportDoc()"><span class="text-only"><th:block th:text="#{'tab06.desc20'}">附加檔案</th:block></span></button>
										<button type="button" id="deleteOverrideFile" onclick="_JP_deleteOverrideReportDoc()"><span class="text-only"><th:block th:text="#{'button.delete'}">刪除</th:block></span></button>
										<br>
										<a href="#" id="getOverrideFile" style="color:red" class="hide" onclick="_JP_downloadOverrideReportDoc()">Open Report</a>
										<input type="text" id="attchFileOid2" name="attchFileOid2" class="hide"/>			
									</td>
								</tr>
								<tr class='tr_adjustStatus12'>
									<td>6.</td>
									<td><th:block th:text="#{'tab06.desc18'}"></th:block>&nbsp;</td>
								</tr>
							</table>	
						</div>
					</fieldset>
					</p>
				</div>
				
			</fieldset>
		</th:block>
    </body>
</html>
