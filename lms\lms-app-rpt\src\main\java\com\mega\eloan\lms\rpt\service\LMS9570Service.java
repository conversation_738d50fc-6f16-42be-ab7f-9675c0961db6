package com.mega.eloan.lms.rpt.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;




/**
 * <pre>
 * 勞工紓困貸款統計報表
 * </pre>
 * 
 * @since 2020
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface LMS9570Service {
	
	public int setBodyContent(WritableSheet sheet, List<Map<String, Object>> dataList) throws RowsExceededException, WriteException;
	
	public void setHeaderContent(WritableSheet sheet, Map<String, Integer> headerMap, Properties prop) throws WriteException;
	
	public List<Map<String, Object>> getStatisticsData();
	
	public List<Map<String, Object>> getDetailReportData();
	
	public int setBodyContentOfDetailReport(WritableSheet sheet, List<Map<String, Object>> dataList) throws RowsExceededException, WriteException;

	// J-110-0142 特定金錢信託案件量統計報表 
	public int setCLS180R42ContentOfDetail(WritableSheet sheet, List<Map<String, Object>> dataList, List<Map<String, Object>> dataList2) throws RowsExceededException, WriteException;
	
	public Map<String, Object> getCLS180R42DetailData(List<Map<String, Object>> dataList2);
	
	public void setHeaderTotal(WritableSheet sheet, Map<String, Integer> headerMap, Properties prop, int rowIndex) throws WriteException;
	
	public int setCLS180R42TotalOfDetail(WritableSheet sheet, Map<String, BigDecimal> loanBalMap, Map<String, BigDecimal> clamtMap, int rowIndex) throws RowsExceededException, WriteException;
	
}
