package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.mega.sso.model.IBranch;

public interface MisRatetblService {
	public Map<String, Object> getNewestByCurr(String curr);
	
	/**
	 * <li>0-幣別 <li>1-日期 <li>2-結帳匯率
	 */
	final String[] RatetblCols = { "CURR", "DATAYMD", "ENDRATE" };

	/**
	 * 取得某日之幣別匯率. 當傳入完整日期(YYYMMDD)則取得當日匯率. 當傳入值為年月(YYYMM)則取得當月最後一個工作日的匯率
	 * 當傳入值為年(YYY)則取得當年最後一個工作日的匯率
	 * 
	 * @param curr
	 *            幣別代碼
	 * @param date
	 *            民國年月日
	 * @return Map<String, Object>
	 */
	Map<String, Object> findByCurrDate(String curr, String date);

	/**
	 * 列出所有幣別最新的匯率(台幣對其它幣別)
	 * 
	 * @return List<Map<String, String>>
	 */
	List<Map<String, Object>> listNewest();

	/**
	 * 取得幣別最新的匯幣
	 * 
	 * @param curr
	 *            幣別代碼
	 * @return List<Map<String, String>>
	 */
	List<Map<String, Object>> findNewestByCurr(String curr);

	/**
	 * 取得幣別最新的營業日
	 * 
	 * @param curr
	 *            幣別代碼
	 * @return Map<String, Object>
	 */
	Map<String, Object> findByCurr(String curr);

	/**
	 * 取得某日之匯率. 當傳入完整日期(YYYMMDD)則取得當日匯率. 當傳入值為年月(YYYMM)則取得當月最後一個工作日的匯率
	 * 當傳入值為年(YYY)則取得當年最後一個工作日的匯率
	 * 
	 * <AUTHOR>
	 * 
	 * @param date
	 *            民國年月(YYYMMDD)
	 * @return Map<String, Object>
	 */
	Map<String, Object> findByDate(String date);

	/**
	 * 取得 上個月最後一個營業日各幣別收盤匯率(補充：另一共用方法findByDate僅取前500筆，此法全取)
	 * @param ROCDateYM
	 *            民國年月
	 * @return Map<String, Object>
	 */
	Map<String, Object> findExRateOfLastWorkingDayOfPreviousMonth(String ROCDateYM);
	
	BigDecimal getCur1ToCur2Rate(String date, IBranch branch,
			String sourceCurr, String destCur);
	
	/**
	 * 取得起迄日期中最新的利率日期
	 * J-105-0185-001 請提供103年度及104年度國內所有分行之新作、增額及續約之授信件數及額度金額(包含企金及消金)
	 * @param begDate
	 * @param endDate
	 * @return
	 */
	public Map<String, Object> findLastDateBetweenRATEYMDRange(String begDate,String endDate);
	
	/**
	 * 取得指定日期之所有利率資料
	 * J-105-0185-001 請提供103年度及104年度國內所有分行之新作、增額及續約之授信件數及額度金額(包含企金及消金)
	 * @param date
	 * @return
	 */
	public List<Map<String, Object>> findByDateForList(String date) ;

}
