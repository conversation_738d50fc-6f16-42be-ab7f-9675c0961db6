/* 
 * C101S01SDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C101S01SDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C101S01S;

/** 個金客戶貸款信用資訊檔 **/
@Repository
public class C101S01SDaoImpl extends LMSJpaDao<C101S01S, String>
	implements C101S01SDao {

	@Override
	public C101S01S findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C101S01S> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C101S01S> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C101S01S> findByList(String mainId, String custId, String dupNo, String dataType) {
		
		ISearch search = createSearchTemplete();
		if (mainId != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		}
		if (custId != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		}
		if (dupNo != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		}
		if (dataType != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "dataType", dataType);
		}
		List<C101S01S> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C101S01S> findByIdDupDataType(String mainId, String custId, String dupNo, String dataType){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "dataType", dataType);
		if(true){
			search.addOrderBy("fileSeq");	
		}
		List<C101S01S> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C101S01S findByUniqueKey(String mainId, String custId, String dupNo, String dataType, String fileSeq){
		ISearch search = createSearchTemplete();
		if (mainId != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		}
		if (custId != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		}
		if (dupNo != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		}
		if (dataType != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "dataType", dataType);
		}
		if (fileSeq != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "fileSeq", fileSeq);
		}
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}
	
	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C101S01S.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}
}