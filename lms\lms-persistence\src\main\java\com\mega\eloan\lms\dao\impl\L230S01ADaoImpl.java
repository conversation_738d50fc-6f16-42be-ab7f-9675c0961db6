/* 
 * L230S01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L230S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;

import com.mega.eloan.lms.model.L230S01A;


/** 簽約未動用額度資訊檔 **/
@Repository
public class L230S01ADaoImpl extends LMSJpaDao<L230S01A, String> implements
		L230S01ADao {

	@Override
	public L230S01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L230S01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L230S01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L230S01A> findByIndex01(String mainId, String srcMainId) {
		ISearch search = createSearchTemplete();
		List<L230S01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (srcMainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "srcMainId",
					srcMainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}
	@Override
	public List<L230S01A> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", CntrNo);
		search.addOrderBy("cntrNo");
		List<L230S01A> list = createQuery(L230S01A.class,search).getResultList();
		
		return list;
	}
	@Override
	public List<L230S01A> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L230S01A> list = createQuery(L230S01A.class,search).getResultList();
		return list;
	}
	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.dao.L230S01ADao#findBySrcMainIdAndDocstatus(java.lang
	 * .String, java.lang.String)
	 */
	@Override
	public L230S01A findBySrcMainIdAndDocstatus(String srcMainId,
			String docstatus) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "srcMainId",
				srcMainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "l230m01a.docStatus",
				docstatus);
		return findUniqueOrNone(search);
	}

	@Override
	public L230S01A findBySrcMainIdAndDocstatusAndMaxdataDate(String srcMainId,
			String docstatus) {
		Query query = getEntityManager().createNamedQuery(
				"L23S01A.findBySrcMainId");
		query.setParameter("srcMainId", srcMainId); // 設置參數
		query.setParameter("docstatus", docstatus); // 設置參數
		return (L230S01A) query.getSingleResult();
	}
}