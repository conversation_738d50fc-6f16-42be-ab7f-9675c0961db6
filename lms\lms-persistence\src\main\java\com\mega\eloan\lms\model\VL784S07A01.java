/* 
 * L784S07A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import javax.persistence.*;

import tw.com.iisi.cap.model.GenericBean;

/** L784S07A的view **/
@Entity
@Table(name = "VL784S07A01")
public class VL784S07A01 extends GenericBean {

	private static final long serialVersionUID = 1L;

	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 單位別
	 * <p/>
	 * MIS.ELCSECNT.BRNO
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "BRNO", length = 3, columnDefinition = "CHAR(03)")
	private String brNo;

	/**
	 * 案件核准年度 (民國年)
	 * <p/>
	 * MIS.ELCSECNT.APPRYY
	 */
	@Column(name = "APPRYY", length = 4, columnDefinition = "CHAR(04)")
	private String apprYY;

	/**
	 * 案件隸屬部門
	 * <p/>
	 * MIS.ELCSECNT.CASEDEPT<br/>
	 * 企金部<br/>
	 * 個金部<br/>
	 * 企金+個金<br/>
	 * ※目前固定為(企金+個金)
	 */
	@Column(name = "CASEDEPT", length = 1, columnDefinition = "CHAR(01)")
	private String caseDept;

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得單位別
	 * <p/>
	 * MIS.ELCSECNT.BRNO
	 */
	public String getBrNo() {
		return this.brNo;
	}

	/**
	 * 設定單位別
	 * <p/>
	 * MIS.ELCSECNT.BRNO
	 **/
	public void setBrNo(String value) {
		this.brNo = value;
	}

	/**
	 * 取得案件核准年度 (民國年)
	 * <p/>
	 * MIS.ELCSECNT.APPRYY
	 */
	public String getApprYY() {
		return this.apprYY;
	}

	/**
	 * 設定案件核准年度 (民國年)
	 * <p/>
	 * MIS.ELCSECNT.APPRYY
	 **/
	public void setApprYY(String value) {
		this.apprYY = value;
	}

	/**
	 * 取得案件隸屬部門
	 * <p/>
	 * MIS.ELCSECNT.CASEDEPT<br/>
	 * 企金部<br/>
	 * 個金部<br/>
	 * 企金+個金<br/>
	 * ※目前固定為(企金+個金)
	 */
	public String getCaseDept() {
		return this.caseDept;
	}

	/**
	 * 設定案件隸屬部門
	 * <p/>
	 * MIS.ELCSECNT.CASEDEPT<br/>
	 * 企金部<br/>
	 * 個金部<br/>
	 * 企金+個金<br/>
	 * ※目前固定為(企金+個金)
	 **/
	public void setCaseDept(String value) {
		this.caseDept = value;
	}

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

}
