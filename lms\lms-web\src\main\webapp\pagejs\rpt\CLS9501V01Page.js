var area;
var pageAction = {
	grid : null,
	viewGrid : null,
	build : function(){
		pageAction.grid = $("#gridview").iGrid({
			handler : 'cls9501v01gridhandler',
			height : 350,
			postData:{
				area:area
			},
			search:true,
			action : "queryBranch",
			rowNum : 15,
			rownumbers:true,
			multiselect : true,
			colModel : [{
				colHeader : "oid",
				name : 'oid',
				hidden : true //是否隱藏
			},{
				colHeader : 'mainId', //文件編號,
				hidden : true, //是否隱藏
				name : 'mainId' //col.id
			},{
				colHeader : 'groupId', //區域中心代碼,
				hidden : true, //是否隱藏
				name : 'groupId' //col.id
			},{
				colHeader : 'isSelected', //是否選取,
				hidden : true, //是否隱藏
				name : 'isSelected' //col.id
			},{
				colHeader : i18n.cls9501v01["C820M01A.brno"], //分行代碼
				align : "center",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'brno' //col.id
			},{
				colHeader : i18n.cls9501v01["C820M01A.brName"], //分行名稱
				align : "center",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'brName' //col.id
			},{
				colHeader : i18n.cls9501v01["C820M01A.dataYM"], //欲產生之年月
				align : "center",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'dataYM' //col.id
			},{
				colHeader : 'creator', //建立人員號碼,
				hidden : true, //是否隱藏
				name : 'creator' //col.id
			},{
				colHeader : 'createTime', //建立日期,
				hidden : true, //是否隱藏
				name : 'createTime' //col.id
			},{
				colHeader : 'updater', //異動人員號碼,
				hidden : true, //是否隱藏
				name : 'updater' //col.id
			},{
				colHeader : 'updateTime', //異動日期,
				hidden : true, //是否隱藏
				name : 'updateTime' //col.id
			}],
			onPaging: function(){
				pageAction.saveThisPage();
			},
			onSortCol:function(){
				pageAction.saveThisPage();
				for(var i=0 ;i<length ; i++){
					var pivot = pageAction.grid.getRowData(i);
					if(pivot.isSelected=='Y'){
						pageAction.grid.setSelection(i, true);
					}
				}
			},
			loadComplete: function(){
				var length = pageAction.grid.getGridParam("records");  
				for(var i=0 ;i<length ; i++){
					var pivot = pageAction.grid.getRowData(i);
					if(pivot.isSelected=='Y'){
						pageAction.grid.setSelection(i, true);
					}
				}
			},
			ondblClickRow: function(rowid){
	        	pageAction.grid.setSelection(rowid, true);//dbclick常會取消勾選
				var data = pageAction.grid.getRowData(rowid);
				pageAction.insertDate(rowid,data,true);
			}		
		});
		//build button 
		//產生報表
		$("#buttonPanel").find("#btnCreateReport").click(function() {
			pageAction.saveThisPage();
			$.capFileDownload({          
		        handler:"lmsdownloadformhandler",
		        data: {
					mainId : pageAction.grid.getRowData(1).mainId,
					fileDownloadName : "收集批覆書額度資訊.xls",
					serviceName : "cls9501v01xlsservice"
			    }
			});
		}).end().find("#btnChange").click(function(){
			var data = pageAction.grid.getSelectData("oid");
			if(data)
				pageAction.insertDate(null,data,false);
		});
	},
	/**
	 * 更新目前瀏覽畫面資料(只改勾選狀態，因為日期只有在insertDate才可做)
	 */
	saveThisPage : function(){
		var rows = pageAction.grid.getGridParam('selarrrow');
  		var selRow = [];
		for (var o in rows) {
			selRow.push(pageAction.grid.getRowData(rows[o]).oid);
		}
		var allRow = pageAction.grid.jqGrid("getRowData");
		var oids=[];
		for(var x in allRow){
			oids[x]=allRow[x].oid;
		}
		$.ajax({
			handler : "cls9501v01formhandler",
			action : 'changeChks',
			data : {
				oids : oids,
				mainId : pageAction.grid.getRowData(1).mainId,
				selects : selRow
			},
			success:function(response){
				
			}
		});
	},
	/**
	 * 顯示區域選擇
	 */
	selArea : function(){
		$("#buttonPanel").hide();
		var obj= CommonAPI.loadCombos("cls9501v01_area");
		$("#area").setItems({
			item:obj.cls9501v01_area,
			format : "{value} - {key}"
		});
		$("div#areaSel").thickbox({
			title : i18n.cls9501v01["areaSelTitle"],
			width : 300,
			height : 100,
			modal : true,
			align : 'center',
			valign: 'bottom',
			i18n: i18n.def, 
			buttons : {
				'sure' : function(){
					area = $("#area").val();
					if(area){
						//代入分行
						$.thickbox.close();
						$("#buttonPanel").show();
						pageAction.build();
					}
					else{
						MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.cls9501v01["plsSelArea"]);
					}
				},
				'close' : function(){//關閉
					$.thickbox.close();
				}
			}
		});
	},
	/**
	 * 開啟日期輸入視窗，並檢核日期格式
	 */
	insertDate : function(rowid,data,isSingle){
		var date = $("input#dataDate");
		if(isSingle)date.val(data.dataYM);
		$("div#dateInsert").thickbox({
			title : i18n.cls9501v01["dateInsertTitle"],
			width : 400,
			height : 100,
			modal : true,
			align : 'center',
			valign: 'bottom',
			i18n: i18n.def, 
			buttons : {
				'sure' : function(){
					var dateYM = date.val();
					if(dateYM=="" || dateYM.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)){
						$.thickbox.close();
						var allRow = pageAction.grid.jqGrid("getRowData");
						var oids=[];
						for(var x in allRow){
							oids[x]=allRow[x].oid;
						}
						$.ajax({
							handler : "cls9501v01formhandler",
							action : 'changeDataYM',
							data : {
								oids: oids,
								dataYM : date.val(),
								mainId : pageAction.grid.getRowData(1).mainId,
								selects : pageAction.grid.getSelectData("oid"),
								isSingle : isSingle
							},
							success:function(response){
								pageAction.grid.reload();
								$.thickbox.close();
							}
						});
					}else{
						MegaApi.showErrorMessage(i18n.def['confirmTitle'],i18n.cls9501v01["dateError"]);
					}
				},
				'close' : function(){//關閉
					$.thickbox.close();
				}
			}
		});
		
	}
}

$(function() {
	pageAction.selArea();
});