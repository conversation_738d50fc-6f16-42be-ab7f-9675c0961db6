/* 
 * C101S01O.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金相關查詢關聯戶貨款明細 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101S01O", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class C101S01O extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 主債務人統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "QUERYDATE", columnDefinition = "DATE")
	private Date queryDate;

	/** 額度序號 **/
	@Size(max = 12)
	@Column(name = "CNTRNO", length = 12, columnDefinition = "VARCHAR(12)")
	private String cntrNo;

	/** 放款帳號 **/
	@Size(max = 14)
	@Column(name = "LOANNO", length = 14, columnDefinition = "VARCHAR(14)")
	private String loanNo;

	/** 銷戶註記 **/
	@Size(max = 1)
	@Column(name = "CANCELFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String cancelFlag;

	/** 銷戶日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CANCELDATE", columnDefinition = "DATE")
	private Date cancelDate;

	/** 主債務人統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID1", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId1;

	/** 主債務人重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO1", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo1;

	/** 主債務人統編名稱 **/
	@Size(max = 120)
	@Column(name = "CUSTNM1", length = 120, columnDefinition = "VARCHAR(120)")
	private String custNm1;

	/** 從債務人統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID2", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId2;

	/** 從債務人重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO2", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo2;

	/** 從債務人統編名稱 **/
	@Size(max = 120)
	@Column(name = "CUSTNM2", length = 120, columnDefinition = "VARCHAR(120)")
	private String custNm2;

	/** 相關身份 **/
	@Size(max = 1)
	@Column(name = "LNGEFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String lngeFlag;

	/** 與主債務人關係 **/
	@Size(max = 2)
	@Column(name = "LNGERE", length = 2, columnDefinition = "CHAR(2)")
	private String lngere;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得主債務人統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定主債務人統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得查詢日期 **/
	public Date getQueryDate() {
		return this.queryDate;
	}

	/** 設定查詢日期 **/
	public void setQueryDate(Date value) {
		this.queryDate = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}

	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得放款帳號 **/
	public String getLoanNo() {
		return this.loanNo;
	}

	/** 設定放款帳號 **/
	public void setLoanNo(String value) {
		this.loanNo = value;
	}

	/** 取得銷戶註記 **/
	public String getCancelFlag() {
		return this.cancelFlag;
	}

	/** 設定銷戶註記 **/
	public void setCancelFlag(String value) {
		this.cancelFlag = value;
	}

	/** 取得銷戶日期 **/
	public Date getCancelDate() {
		return this.cancelDate;
	}

	/** 設定銷戶日期 **/
	public void setCancelDate(Date value) {
		this.cancelDate = value;
	}

	/** 取得主債務人統編 **/
	public String getCustId1() {
		return this.custId1;
	}

	/** 設定主債務人統編 **/
	public void setCustId1(String value) {
		this.custId1 = value;
	}

	/** 取得主債務人重覆序號 **/
	public String getDupNo1() {
		return this.dupNo1;
	}

	/** 設定主債務人重覆序號 **/
	public void setDupNo1(String value) {
		this.dupNo1 = value;
	}

	/** 取得主債務人統編名稱 **/
	public String getCustNm1() {
		return this.custNm1;
	}

	/** 設定主債務人統編名稱 **/
	public void setCustNm1(String value) {
		this.custNm1 = value;
	}

	/** 取得從債務人統編 **/
	public String getCustId2() {
		return this.custId2;
	}

	/** 設定從債務人統編 **/
	public void setCustId2(String value) {
		this.custId2 = value;
	}

	/** 取得從債務人重覆序號 **/
	public String getDupNo2() {
		return this.dupNo2;
	}

	/** 設定從債務人重覆序號 **/
	public void setDupNo2(String value) {
		this.dupNo2 = value;
	}

	/** 取得從債務人統編名稱 **/
	public String getCustNm2() {
		return this.custNm2;
	}

	/** 設定從債務人統編名稱 **/
	public void setCustNm2(String value) {
		this.custNm2 = value;
	}

	/** 取得相關身份 **/
	public String getLngeFlag() {
		return this.lngeFlag;
	}

	/** 設定相關身份 **/
	public void setLngeFlag(String value) {
		this.lngeFlag = value;
	}

	/** 取得與主債務人關係 **/
	public String getLngere() {
		return this.lngere;
	}

	/** 設定與主債務人關係 **/
	public void setLngere(String value) {
		this.lngere = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
