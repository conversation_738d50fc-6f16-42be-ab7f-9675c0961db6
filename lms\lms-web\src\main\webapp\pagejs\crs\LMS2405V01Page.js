var _oversea = true;
$(function(){
	$.ajax({
		type: "POST", handler: "lms2405m01formhandler",
		action: "overSeaProgram", async: false,//用「同步」的方式
	    data: {},
	    }).done(function(json){
	    	if(json.overSeaProgram){
	    		_oversea = true;
	    	}else{
	    		_oversea = false;
	    	}
	});
	//==================
    var regYYYYMM = /^\d{4}\-(0?[1-9]|1[0-2])$/;
    if (userInfo.unitNo != "900") {
        $("#btnMaintain").hide();
    }
    filterChoice();
    var gridview_param = "";
    if( $("#gridview_param").find("[name=grid_flag]").length>0  ){
    	gridview_param = $("#gridview_param").find("[name=grid_flag]").val();
    }
    
    var grid = $("#gridview").iGrid({
        handler: 'lms2405gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        shrinkToFit: true,
		sortname: "expectedRetrialDate|branchId",
        sortorder: "desc|asc",
        multiselect: (_oversea?false:true),
        postData: {
            docStatus: viewstatus,
            formAction: "queryMain", 
            gridview_param : gridview_param
        },
        colModel: [ {
            colHeader: i18n.lms2405v01['C240M01A.expectedRetrialDate'],//"預計複審日",
            name: 'expectedRetrialDate',
            width: 60,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.abstracteloan['doc.branchName'],//"分行名稱",
            name: 'uid',
            width: 80,
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.lms2405v01['C240M01A.reQuantity'],//"覆審筆數",
            name: 'reQuantity',
            width: 40,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms2405v01['C240M01A.dataEndDate'],//"覆審名單截至月",
            name: 'dataEndDate',
            align: "center",
            width: 70,
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            },
            sortable: true
        }, {
            colHeader: i18n.lms2405v01['C240M01A.updater'],//"最後異動人員",
            name: 'updater',
            align: "center",
            width: 60,
            sortable: true
        }, {
            colHeader: i18n.lms2405v01['C240M01A.updateTime'],//"最後異動日期",
            name: 'updateTime',
            align: "center",
            width: 60,
            sortable: true
        }, {
        	//J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
            colHeader: i18n.lms2405v01['C240M01A.status'],//"處理狀態",
            name: 'status',
            align: "center",
            width: 40,
            sortable: true
        }, {
        	//J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
            colHeader: i18n.lms2405v01['C240M01A.rpaDate'],//"RPA執行日期",
            name: 'rpaDate',
            align: "center",
            width: 40,
            sortable: true       
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "branchId",
            name: 'branchId',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    // 整批日期覆蓋
    $('#changeDate').click(function coverDate(){
        if ($("#lotBanksForm").valid()) {
            //起始日期
            var totalDataDate = $("#totalDataDate").val();
            if (!totalDataDate.match(regYYYYMM)) {
                //val.date2=日期格式錯誤(YYYY-MM)
                return CommonAPI.showMessage(i18n.def["val.date2"]);
            }
            var date = $('#totalDataDate').val();
            $("input[name='branchDate']").val(date);
        }
    })
    
    function openDoc(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        
        if(_oversea){
        	 $.form.submit({
                 url: '../crs/lms2405m01/01',
                 data: {
                     formAction: "queryMain",
                     //					transactionCode : transactionCode,
                     oid: rowObject.oid,
                     mainOid: rowObject.oid,
                     mainId: rowObject.mainId,
                     branchId: rowObject.uid,
                     mainDocStatus: viewstatus
                 },
                 target: rowObject.oid
             });
        }else{
        	$.form.submit({
  	  			url:'../crs/lms2401m01/02',
  	  			data:{
  	  				mainOid: rowObject.oid,
  	  				mainId: rowObject.mainId,
  	                mainDocStatus: viewstatus
  	  			},
  	  			target:rowObject.oid
	    	});
        }        
  	};
  	
    if ($("#orderBox").length > 0) {
    
        $.ajax({
            type: "POST",
            handler: "lms2405m01formhandler",
            data: {
                formAction: "queryBranch"
            }
            }).done(function(obj){
            	
            	$.each(obj.itemOrder, function(idx, brNo) {
            		var currobj = {};
            		var brName = obj.item[brNo];
            		currobj[brNo] = brName;
            		//依 itemOrder, 一個一個append, 把 clear 指為 false
            		
            		//select
					$("#order").setItems({ item: currobj, format: "{value} {key}", clear:false, space: false });
				});
            	
            	var col_cnt = 2;
            	var elmArr = [];
            	$.each(obj.itemOrder, function(idx, brNo) {
            		var brName = obj.item[brNo];
            		//依 itemOrder, 一個一個append, 把 clear 指為 false
            		
            		var tdcol = {};
            		tdcol['_a'] = "<label style='letter-spacing:0px;cursor:pointer;font-weight:normal;'><input value='"+brNo+"' id='branchList' name='branchList' type='checkbox'>"+brNo +" "+ brName+"</label>";
            		tdcol['_b'] = "<input id='branch"+brNo+"' type='text' name='branchDate' maxlength='7' size='7'>";
            		elmArr.push(tdcol);            		
				});
            	//===
            	//補empty col
            	var addcnt = (col_cnt - (elmArr.length % col_cnt)); 
            	if(addcnt==col_cnt){
            		addcnt = 0;
            	}
            	for(var i=0;i<addcnt;i++){
            		var tdcol = {};
            		tdcol['_a'] = "&nbsp;";
            		tdcol['_b'] = "&nbsp;";
            		elmArr.push(tdcol);  
            	}
            	
            	var dyna = [];
            	dyna.push("<table width='100%' border='0' cellspacing='0' cellpadding='0'>");
            	dyna.push("<tr>");
            	for(var i=0;i<col_cnt;i++){
            		dyna.push("<td colspan='2' align='center'>分行資料年月(YYYY-MM)</td>");	
            	}
            	dyna.push("</tr>");
            	dyna.push("<tr>");
            	for(var i=0;i<elmArr.length;i++){
            		dyna.push("<td>"+elmArr[i]['_a']+"</td>");
            		dyna.push("<td>"+elmArr[i]['_b']+"</td>");
            		if( (i+1) % col_cnt==0){
            			dyna.push("</tr><tr>");		
            		}
            	}
            	dyna.push("</tr>");
            	dyna.push("</table>");
            	$("#orderBox").append(dyna.join("\n"));
        });
    }
    
    $("#buttonPanel").find("#btnDelete").click(function(){
    	if(_oversea){
    		var rows = $("#gridview").getGridParam('selrow');
            var list = "";
            if (rows != 'undefined' && rows != null && rows != 0) {
                var data = $("#gridview").getRowData(rows);
                list = data.oid;
            }
            if (list == "") {
                CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
                return;
            }
            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    $.ajax({
                        handler: "lms2405m01formhandler",
                        type: "POST",
                        dataType: "json",
                        data: {
                            formAction: "deleteMark",
                            list: list
                        }
                        }).done(function(obj){
                            $("#gridview").trigger("reloadGrid");
                    });
                }
            });	
    	}else{
    		var rowId_arr = $("#gridview").getGridParam('selarrrow');
    		var oid_arr = [];
       	 	for (var i = 0; i < rowId_arr.length; i++) {
    			var data = $("#gridview").getRowData(rowId_arr[i]);
    			oid_arr.push(data.oid);    			
            }
       	 	
       	 	if(oid_arr.length==0){   	 		
       	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
   	 			return;
    	 	}
       	 	
	       	 CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
	             if (b) {
	                 $.ajax({
	                     handler: "lms2401m01formhandler",
	                     type: "POST",
	                     dataType: "json",
	                     data: {
	                         formAction: "deleteMark",
	                         oids: oid_arr.join("|")
	                     }
	                     }).done(function(obj){
	                         $("#gridview").trigger("reloadGrid");
	                 });
	             }
	         });
    	}        
    }).end().find("#btnProducePaper").click(function(){
        $("#newinsertcase").thickbox({
            //'產生覆審名單'
            title: i18n.lms2405v01['produceList'],
            width: 200,
            height: 150,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    $.thickbox.close();
                    if ($("input[name=r888]:checked").val() == 0) {
                        $("#branchForm").reset();
                        $("#chooseBranch").thickbox({
                            title: i18n.lms2405v01['branchAndDataDate'],
                            width: 300,
                            height: 150,
                            align: "center",
                            valign: "bottom",
                            modal: false,
                            i18n: i18n.def,
                            buttons: {
                                "sure": function(){
                                    if ($("#branchForm").valid()) {
                                    	if(_oversea){
                                    		//---
                                            $.ajax({
                                                handler: "lms2405m01formhandler",
                                                type: "POST",
                                                dataType: "json",
                                                data: {
                                                    formAction: "produce",
                                                    check: true,
                                                    branch: $("#order").val(),
                                                    basedate: $("#year").val() + "-" + $("#month").val()
                                                }
                                                }).done(function(obj){
                                                    $.thickbox.close();
                                                    $("#gridview").trigger("reloadGrid");
                                                    if (obj.success) {
                                                        CommonAPI.showMessage(i18n.def["runSuccess"]);
                                                    } else if (obj.msg) {
                                                        CommonAPI.showMessage(obj.msg);
                                                    } else {
                                                        CommonAPI.confirmMessage(i18n.lms2405v01["L180M01A.message1"], function(b){
                                                            if (b) {
                                                                $.ajax({
                                                                    handler: "lms2405m01formhandler",
                                                                    type: "POST",
                                                                    dataType: "json",
                                                                    data: {
                                                                        formAction: "produce",
                                                                        check: false,
                                                                        branch: $("#order").val(),
                                                                        basedate: $("#year").val() + "-" + $("#month").val()
                                                                    }
                                                                    }).done(function(obj){
                                                                        $.thickbox.close();
                                                                        $("#gridview").trigger("reloadGrid");
                                                                        CommonAPI.showMessage(i18n.def["runSuccess"]);
                                                                });
                                                            }
                                                        });
                                                    }
                                            });
                                            //---	
                                    	}else{
                                    		crs_app_url($("#order").val()+"^"+ $("#year").val()+"-"+$("#month").val()).done(function(){
                        	    				$("#gridview").trigger("reloadGrid");
                        	    				$.thickbox.close();
                        	    				API.showMessage(i18n.def.runSuccess);
                        	        		});
                                    	}
                                    }
                                },
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    } else if ($("input[name=r888]:checked").val() == 1) {
                    	var pagesize = tb_getPageSize();
    					var x = pagesize[0] - 60;
    					var y = pagesize[1] - 40;
                        $("#lotBanksForm").reset();
                        $("#lotBanks").thickbox({
                            //'選擇多分行'
                            title: i18n.lms2405v01['chooseBank'],
                            width: x,
                            height: y,
                            align: 'center',
                            valign: 'bottom',
                            i18n: i18n.def,
                            buttons: {
                                "sure": function(){
                                    
                                    var obj = {};
                                    var crs_obj_arr = [];
                                    var owin = $("[name=branchList]:checked").map(function(){                                    	
                                        return DOMPurify.sanitize(String($(this).val()));                                    	
                                    }).toArray();
                                    if (owin == 'undefined' || owin == null || owin == "") {
                                        return CommonAPI.showMessage(i18n.lms2405v01["chooseBank"]);
                                    }
                                    
                                    for (var o in owin) {
                                    	var desc = owin[o]+"分行";
                                        if (!$("#branch" + owin[o]).val().match(regYYYYMM)) {
                                            //val.date2=日期格式錯誤(YYYY-MM)
                                            return CommonAPI.showMessage(desc+i18n.def["val.date2"]);
                                        }
                                        if ($("#branch" + owin[o]).val() ? false : true) {
                                            return CommonAPI.showMessage(desc+i18n.lms2405v01["noDataDate"]);
                                        } else {
                                            obj[owin[o]] = $("#branch" + owin[o]).val();
                                            crs_obj_arr.push(owin[o]+"^"+ $("#branch" + owin[o]).val());
                                        }
                                    }
                                    
                                    if(_oversea){
                                    	//---  		
                                        $.ajax({
                                            handler: "lms2405m01formhandler",
                                            type: "POST",
                                            dataType: "json",
                                            data: {
                                                formAction: "produceMany",
                                                list: JSON.stringify(obj)
                                            }
                                            }).done(function(obj){
                                                $("#gridview").trigger("reloadGrid");
                                                if (!obj.NOTIFY_MESSAGE) {
                                                    $.thickbox.close();
                                                    CommonAPI.showMessage(i18n.def["runSuccess"]);
                                                }
                                        });
                                        //---	
                                    }else{
                                    	var crs_app_url_arr = [];
                              			//在此決定, 1個BR, 1個 request
                              			//或 N個BR, 1個 request
                              			var _merge = true;
                              			if(_merge){                                          				
                              				crs_app_url_arr.push(crs_obj_arr.join("|"));
                              			}else{
                              				$.each(crs_obj_arr, function(k, v_param) { 
                              					crs_app_url_arr.push(v_param);
                              				});
                              			}
                              			
                              			var cnt = 0;
                              			var target = crs_app_url_arr.length;
                              			
                              			var theQueue = $({});
                                		$.each(crs_app_url_arr,function(k, v_url) {                                            			
                                			theQueue.queue('myqueue', function(next) {
                                				crs_app_url(v_url).done(function(){            		
                                					cnt++;
                                  					if(cnt==target){
                                  						$("#gridview").trigger("reloadGrid");
                                      					$.thickbox.close();
                                      					API.showMessage(i18n.def.runSuccess);
                                      				}
                                  					//---
                                  					//把 next() 寫在 finish 的 callback 裡
                                  					//才會 1個 url 抓到 response 後,再get下1個
                                  					//不然會1次跳 N 個出來
                                  					next(); 
                                        		});
                                			});                                            			 
                                		});
                                		theQueue.dequeue('myqueue');
                                    }                                        
                                },
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }).end().find("#btnProduceExcel").click(function(){

    	if(_oversea){
    		var rows = $("#gridview").getGridParam('selrow');
            var list = "";
            if (rows != 'undefined' && rows != null && rows != 0) {
                var data = $("#gridview").getRowData(rows);
                list = data.oid;
            }
            if (list == "") {
                return CommonAPI.showMessage(i18n.def["grid.selrow"]);
            }
            $("input[name=produceExcel]").prop("checked", false);;
            
            $("#chooseExcel").thickbox({
                title: i18n.lms2405v01['chooseExcel'],
                width: 200,
                height: 180,
                align: "center",
                valign: "bottom",
                modal: false,
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        if ($("input[name=produceExcel]:checked")) {                    	
                    		$.ajax({
                            	handler: "lms2405m01formhandler",
                                type: "POST",
                                dataType: "json",
                                data: {
                                    formAction: "produceExcel",
                                    mode: $("input[name=produceExcel]:checked").val(),
                                    oid: list
                                }
                                }).done(function(obj){
                                    $("#gridview").trigger("reloadGrid");
                            });                        	
                        	
                            $.thickbox.close();
                        } else {
                            return;
                        }
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
    	}else{
    		var rowId_arr = $("#gridview").getGridParam('selarrrow');
    		var oid_arr = [];
       	 	for (var i = 0; i < rowId_arr.length; i++) {
    			var data = $("#gridview").getRowData(rowId_arr[i]);
    			oid_arr.push(data.oid);    			
            }
       	 	
       	 	if(oid_arr.length==0){   	 		
       	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
   	 			return;
    	 	}
       	 	
            $("input[name=produceExcel]").prop("checked", false);;
            
            $("#chooseExcel").thickbox({
                title: i18n.lms2405v01['chooseExcel'],
                width: 200,
                height: 180,
                align: "center",
                valign: "bottom",
                modal: false,
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        if ($("input[name=produceExcel]:checked")) {
                        	
                    		$.ajax({
                                handler: "lms2401m01formhandler",
                                type: "POST",
                                dataType: "json",
                                data: {
                                    formAction: "produceExcel",
                                    mode: $("input[name=produceExcel]:checked").val(),
                                    oids: oid_arr.join("|")
                                }
                                }).done(function(obj){
                                    $("#gridview").trigger("reloadGrid");
                            });                        	
                        	
                            $.thickbox.close();
                        } else {
                            return;
                        }
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
    	}
        
        
    }).end().find("#btnSearch").click(function(){
    	if(_oversea){
    		$("#newCust").thickbox({
	            //新增客戶
	            title: i18n.lms2405v01['filterCondition'],
	            width: 250,
	            height: 150,
	            align: "center",
	            valign: "bottom",
	            modal: false,
	            i18n: i18n.def,
	            buttons: {
	                "sure": function(){
	                    if ($("#filterForm").valid()) {
	                        $.ajax({
	                            handler: "lms2405m01formhandler",
	                            type: "POST",
	                            dataType: "json",
	                            data: {
	                                formAction: "showSearchData",
	                                custId: $("#filter_custId").val(),
	                                dupNo: $("#filter_dupNo").val()
	                            }
	                            }).done(function(json){
	                                var htmlContent = "";
	                                var showSearchTable = $("#showSearchTable");
	                                $.thickbox.close();
	                                //$("#BRANCHNAME").val(json.BRANCHNAME);
	                                //帳務檔
	                                htmlContent = $("#showThick01Title").html().replace("CUSTNAMEDATA", json.custNameData);
	                                //                            	showSearchTable.html(showSearchTable.html()	 + $("#showThick01Title").html());
	                                var data491 = json.data491;
	                                for (i = 0; i < data491.length; i++) {
	                                    var temp = data491[i].split("!");
	                                    htmlContent = htmlContent + $("#showThick01").html().replace("LRDATE", temp[0]).replace("CRDATE", temp[1]).replace("NCKDFLAG", temp[2]).replace("NCKDDATE", temp[3]).replace("NCKDMEMO", temp[4]).replace("BRANCH", temp[5]);
	                                    //                            		showSearchTable.html(showSearchTable.html()	 + $("#showThick01").html().replace("LRDATE",temp[0]).replace("CRDATE",temp[1]).replace("NCKDFLAG",temp[2]).replace("NCKDDATE" ,temp[3]).replace("NCKDMEMO",temp[4]).replace("BRANCH",temp[5])	 );
	                                }
	                                if (data491.length == 0) {
	                                    htmlContent = htmlContent + $("#showThick01NoData").html();
	                                    //                            		showSearchTable.html(showSearchTable.html()	 + $("#showThick01NoData").html());
	                                }
	                                //帳務檔
	                                htmlContent = htmlContent + $("#showThick02Title").html();
	                                //                            	showSearchTable.html(showSearchTable.html()	 + $("#showThick02Title").html());
	                                var datadw = json.datadw;
	                                for (i = 0; i < datadw.length; i++) {
	                                    var temp = datadw[i].split("!");
	                                    htmlContent = htmlContent + $("#showThick02").html().replace("CANCEL_DT", temp[0]).replace("FACT_CONTR", temp[1]).replace("BRANCH", temp[2]);
	                                    //                            		showSearchTable.html(showSearchTable.html()	 + $("#showThick02").html().replace("FACT_CONTR",temp[0]).replace("CANCEL_DT",temp[1]).replace("BRANCH",temp[2]));
	                                }
	                                if (datadw.length == 0) {
	                                    htmlContent = htmlContent + $("#showThick02NoData").html();
	                                    //                            		showSearchTable.html(showSearchTable.html()	 + $("#showThick02NoData").html());
	                                }
	                                showSearchTable.html(htmlContent);
	                                //                            	alert(showSearchTable.html());
	                                $("#showSearchData").thickbox({
	                                    //新增客戶
	                                    title: i18n.lms2405v01['list'],
	                                    width: 650,
	                                    height: 400,
	                                    align: "center",
	                                    valign: "bottom",
	                                    modal: false,
	                                    i18n: i18n.def,
	                                    buttons: {
	                                        "sure": function(){
	                                            $.thickbox.close();
	                                        }
	                                    }
	                                });
	                        });
	                    }
	                },
	                "cancel": function(){
	                    $.thickbox.close();
	                }
	            }
	        });
    	}else{
    		type1_queryMenu();
    	}
    }).end().find("#btnMaintain").click(function(){
        $("#cover").thickbox({
            //'覆審控制檔維護'
            title: i18n.abstracteloan['button.Maintain'],
            width: 450,
            height: 300,
            modal: true,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                	//actoin_001=是否執行此動作?
                	CommonAPI.confirmMessage(i18n.def.actoin_001, function(b){
        	            if (b) {
        	            	$.thickbox.close();
                            var chooseVal = $("input[name='radio900Control']:checked").val();
                            if(chooseVal=="A"){
                            	//目前只在br=900才出現,處理海外LMS.LMS491                    
                                $.ajax({
                                    handler: "lms2405m01formhandler",
                                    type: "POST",
                                    dataType: "json",
                                    action : "update490"
                                });
                            }else if(chooseVal=="B"){
                            	//長交易, 可能要跑1小時
                            	var my_timeout = 7200000;//ms
                            	$.ajax({
                                    handler: "lms2411m01formhandler", action: "callBatch", timeout: my_timeout,
                                    data: {'act':'elf490_to_elf491'
                                    	, 'limit_branch': ''
                                    	, 'jq_timeout': (my_timeout/1000)}
                                    }).done(function(json_callBatch){
                                    	if(json_callBatch.r.response==="SUCCESS"){
                                    		API.showMessage(i18n.def.runSuccess);
                                    	}else{
                                    		API.showErrorMessage(json_callBatch.r.response);
                                    	}
                                });
                            }else if(chooseVal=="B1"){
                            	var limit_branch = $("input[name='limit_branch']").val();
                            	//長交易, 可能要跑1小時
                            	var my_timeout = 7200000;//ms
                            	$.ajax({
                                    handler: "lms2411m01formhandler", action: "callBatch", timeout: my_timeout,
                                    data: {'act':'elf490_to_elf491_force'
                                    	, 'limit_branch': limit_branch
                                    	, 'jq_timeout': (my_timeout/1000)}
                                    }).done(function(json_callBatch){
                                    	if(json_callBatch.r.response==="SUCCESS"){
                                    		API.showMessage(i18n.def.runSuccess);
                                    	}else{
                                    		API.showErrorMessage(json_callBatch.r.response);
                                    	}
                                });
                            }else if(chooseVal=="M"){
                            	var overDays = $("input[name='overDays']").val();
                            	//以下寫法只可在 Testing 執行，正式環境有限制IP
                            	var app_url = "../../app/scheduler?input={'serviceId':'crsBatchServiceImpl','request':{'act':'mail_processingOverDays','overDays':'"+overDays+"','skipBrT1':'Y'}}";
                            	$.get( app_url, function(){
                            		API.showMessage(i18n.def.runSuccess);
                            	});
                            }else if(chooseVal=="R"){
                            	var normalMin = $("input[name='normalMin']").val();

                            	var my_timeout = 100000;//ms
                            	$.ajax({
                                    handler: "lms2411m01formhandler", action: "callBatch", timeout: my_timeout,
                                    data: {'act':'procC242M01A','normalMin':normalMin, 'jq_timeout': (my_timeout/1000)}
                                    }).done(function(json_callBatch){
                                    	if(json_callBatch.r.response==="SUCCESS"){
                                    		API.showMessage(i18n.def.runSuccess);
                                    	}else{
                                    		API.showErrorMessage(json_callBatch.r.response);
                                    	}	
                                });                            	
                            }else if(chooseVal=="R1"){
                            	var r1_branch = $("input[name='r1_branch']").val();
                            	var r1_custId = $("input[name='r1_custId']").val();
                            	
                            	var my_timeout = 7200000;//ms
                            	$.ajax({
                                    handler: "lms2411m01formhandler", action: "callBatch", timeout: my_timeout,
                                    data: {'act':'chkR1R2R4_gen','brNo':r1_branch,'custId':r1_custId, 'jq_timeout': (my_timeout/1000)}
                                    }).done(function(json_callBatch){
                                    	if(json_callBatch.r.response==="SUCCESS"){
                                    		API.showMessage(i18n.def.runSuccess);
                                    	}else{
                                    		API.showErrorMessage(json_callBatch.r.response);
                                    	}	
                                });
                            }else if(chooseVal=="RX"){
                            	var cnt1 = $("input[name='rx_cnt1']").val();
                            	var cnt2 = $("input[name='rx_cnt2']").val();
                            	  
                            	var my_timeout = 7200000;//ms
                            	$.ajax({
                                    handler: "lms2411m01formhandler", action: "callBatch", timeout: my_timeout,
                                    data: {'act':'chkR1R2R4_process','cnt1':cnt1,'cnt2':cnt2, 'jq_timeout': (my_timeout/1000)}
                                    }).done(function(json_callBatch){
                                    	if(json_callBatch.r.response==="SUCCESS"){
                                    		API.showMessage(i18n.def.runSuccess);
                                    	}else{
                                    		API.showErrorMessage(json_callBatch.r.response);
                                    	}	
                                });
                            }else if(chooseVal=="TC"){
                            	$.ajax({ handler: 'lms2401m01formhandler', data: {formAction: "test_calc"
                            		, 'tc_flag': '_produce_'
                            		, 'tc_brNo':$("input[name='tc_brNo']").val()
                            		, 'tc_custId':$("input[name='tc_custId']").val()
                            		, 'tc_dupNo':$("input[name='tc_dupNo']").val()
                            		, 'tc_rule_no':$("input[name='tc_rule_no']").val()
                            		, 'tc_rule_no_new':$("input[name='tc_rule_no_new']").val()
                            	}}).done(function(json_tc){
                            		API.showMessage("【rule_decided】"+json_tc.rule_decided
                            				+"<br/>【rule_mode】"+json_tc.rule_mode
                            				+"<br/>【use_rule】"+json_tc.use_rule
                            				+"<br/>【decideInfo】"+json_tc.decideInfo
                            				+"<br/>【reasonDesc】"+json_tc.reasonDesc);
                                });
                            }else if(chooseVal=="U"){
                            	var my_timeout = 100000;//ms
                            	$.ajax({
                                    handler: "lms2411m01formhandler", action: "callBatch", timeout: my_timeout,
                                    data: {'act':'c241m01a_dbu_approveToEnd','userId':'BATCH','unitNo':'900', 'jq_timeout': (my_timeout/1000)}
                                    }).done(function(json_callBatch){
                                    	if(json_callBatch.r.response==="SUCCESS"){
                                    		API.showMessage(i18n.def.runSuccess+"。若覆審類別包含99，將需人工上傳。");	
                                    	}else{
                                    		API.showErrorMessage(json_callBatch.r.response);
                                    	}	
                                });
                            }else if(chooseVal=="Z"){
                            	var my_timeout = 100000;//ms
                            	$.ajax({
                                    handler: "lms2411m01formhandler", action: "callBatch", timeout: my_timeout,
                                    data: {'act':'update491_abnormal', 'jq_timeout': (my_timeout/1000)}
                                    }).done(function(json_callBatch){
                                    	if(json_callBatch.r.response==="SUCCESS"){
                                    		API.showMessage(i18n.def.runSuccess);	
                                    	}else{
                                    		API.showErrorMessage(json_callBatch.r.response);
                                    	}                                    		
                                });                            	
                            }    	
        	            }
        	        });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }).end().find("#btnFilter").click(function(){
        FilterAction.openBox();
    }).end().find("#btnURD0519").click(function(){
        $.ajax({
            handler: "lms2401m01formhandler",
            action: "updateRetrialDataJ1130519"
            }).done(function(json){
                if(json.rows){
                    API.showPopMessage("修改成功Rows:" + json.rows);
                }else{
                    API.showPopMessage("修改失敗");
                }
        });
    });
});

function checkExist010(par_arr){
	var my_dfd = $.Deferred();
	$.ajax({
        handler: "lms2401m01formhandler", action: "checkExist010",
        data: {'par_arr': par_arr, 'unitNo': userInfo.unitNo}
        }).done(function(json_checkExist010){
        	if(json_checkExist010.isMsg=="Y"){
        		API.showPopMessage("", json_checkExist010.msg,function(){
        			my_dfd.reject();
  				});
        	}else{
        		my_dfd.resolve();	
        	}        	        		
    });
	return my_dfd.promise();
}
/**
 * arr.push(data.branchId+"^"+data.baseDate);
 * par_arr : arr.join("|")
 */
function crs_app_url(par_arr){
	var my_dfd = $.Deferred();
	
	checkExist010(par_arr).done(function(){
		
		var my_timeout = 7200000;//ms
		$.ajax({
	        handler: "lms2411m01formhandler", action: "callBatch", timeout: my_timeout,
	        data: {'act':'nonOversea_produceC240M01A'
	        	, 'par_arr': par_arr
	        	, 'userId': userInfo.userId
	        	, 'unitNo': userInfo.unitNo
	        	, 'unitType': userInfo.unitType
	        	, 'jq_timeout': (my_timeout/1000)}
	        }).done(function(json_callBatch){
				if(json_callBatch.r.response==="SUCCESS"){
//				var rObj = jsonParse(json_callBatch.r)
//	        	if(rObj && rObj.response==="SUCCESS"){
	        		my_dfd.resolve();
	        	}	
	    });
		
	});
		
	return my_dfd.promise();
}

function filterChoice(){
    if ($("[name=filterCheck]:checked").val() == '1') {
        //		$("#xxx").val('');
        //		$("#uuu").val('');
        $("#filter_custId").prop("disabled", false);
        $("#filter_dupNo").prop("disabled", false);
        //		$("#xxx").attr("disabled",true);
        //		$("#uuu").attr("disabled",true);
    } else {
        $("#filter_custId").val('');
        $("#filter_dupNo").val('0');
        $("#filter_custId").prop("disabled", true);
        $("#filter_dupNo").prop("disabled", true);
        //		$("#xxx").attr("disabled",false);
        //		$("#uuu").attr("disabled",false);
    
    }
}

function build_submenu(dyna, rdoName, submenu){
	$.each(submenu, function(k, v) { 
		 
    });		
}

function f_q_custIdDupNo($q_result, q_custId, q_dupNo){
	$.ajax({ handler: 'lms2401m01formhandler', data: {formAction: "query_custIdDupNo"
		, 'q_custId':q_custId, 'q_dupNo':q_dupNo} }).done(function(json_q){
		var showAdditional = false;	
		//---
		if (userInfo.unitNo == "900" || userInfo.ssoUnitNo=="900") {
			showAdditional = true;
	    }
		
		var dyna = [];
		dyna.push("<div style='overflow:auto'>");
		dyna.push("<div>客戶："+DOMPurify.sanitize(String(json_q.custName))+"　"+DOMPurify.sanitize(String(json_q.custId))+"-"+DOMPurify.sanitize(String(json_q.dupNo))+"</div>");
		//======
		//ELF490
		if(showAdditional){
			dyna.push("<p>ELF490（"+DOMPurify.sanitize(String(json_q.elf490_data_ym_S))+" ~ "+DOMPurify.sanitize(String(json_q.elf490_data_ym_E))+"）");
			dyna.push("<div style='margin-left:20px'>");
			if(json_q.elf490_size > 0 ){
				dyna.push("<table border='1' width='100%'>");
				//header
				dyna.push("<tr>");
				if (true) {														
					dyna.push("<td>&nbsp;&nbsp;"+"年月"+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+"分行"+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+"舊案類別"+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+"新案類別"+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+"不動產十足擔保特定態樣"+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+"不動產地區金額註記"+"</td>");
				}							
				dyna.push("</tr>");
				$.each( json_q.elf490_data.arr, function(idx, item){						
					dyna.push("<tr>");						
					if (true) {														
						dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.data_ym))+"</td>");
						dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.branch))+"</td>");
						dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.ruleNo))+"</td>");
						dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.ruleNoNew))+"</td>");
						dyna.push("<td>&nbsp;&nbsp;"+(DOMPurify.sanitize(String(item.houseFg))||'')+","+(DOMPurify.sanitize(String(item.rmbinsFg))||'')+","+(DOMPurify.sanitize(String(item.hsCreFg))||'')+"</td>");
						dyna.push("<td>&nbsp;&nbsp;"+(DOMPurify.sanitize(String(item.areaFg))||'')+"</td>");
					}							
					dyna.push("</tr>");
				});
				dyna.push("</table>");
			}else{
				dyna.push("查到"+DOMPurify.sanitize(String(json_q.elf490_size)) +"筆資料");	
			}		
			dyna.push("</div>");
			dyna.push("</p>");
		}
		//======
		//ELF491
		dyna.push("<p>覆審控制檔：");
		dyna.push("<div style='margin-left:20px'>");
		if(json_q.elf491_size > 0 ){
			dyna.push("<table border='1' width='100%'>");
			//header
			dyna.push("<tr>");
			if (true) {														
				dyna.push("<td>&nbsp;&nbsp;"+"分行"+"</td>");
				dyna.push("<td>&nbsp;&nbsp;"+"上次覆審日"+"</td>");
				dyna.push("<td>&nbsp;&nbsp;"+"下次覆審日"+"</td>");
				dyna.push("<td>&nbsp;&nbsp;"+"覆審規則"+"</td>");
				dyna.push("<td>"+"新案註記"+"</td>");
				dyna.push("<td>&nbsp;&nbsp;"+"不覆審原因"+"</td>");
				dyna.push("<td>&nbsp;&nbsp;"+"不覆審註記日期"+"</td>");
				dyna.push("<td>&nbsp;&nbsp;"+"不覆審備註"+"</td>");
			}							
			dyna.push("</tr>");
			$.each( json_q.elf491_data.arr, function(idx, item){						
				dyna.push("<tr>");						
				if (true) {														
					dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.branch))+"</td>");
					dyna.push("<td>&nbsp;&nbsp;(土建融)&nbsp;"+DOMPurify.sanitize(String(item.lastRealDt))+"<br/>&nbsp;&nbsp;(上　次)&nbsp;"+DOMPurify.sanitize(String(item.lrDate))+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.crDate))+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.remomo))+"</td>");
					dyna.push("<td>"+DOMPurify.sanitize(String(item.newflag))+"&nbsp;</td>")
					dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.nckdflag))+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.nckddate))+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.nckdmemo))+"</td>");
				}							
				dyna.push("</tr>");
			});
			dyna.push("</table>");
		}else{
			dyna.push("查到"+DOMPurify.sanitize(String(json_q.elf491_size)) +"筆資料");	
		}		
		dyna.push("</div>");
		dyna.push("</p>");
		//======
		//LNF020、LNF030
		dyna.push("<p>帳務檔：");
		dyna.push("<div style='margin-left:20px'>");
		if(json_q.lnf020_size > 0 ){
			dyna.push("<table border='1' width='100%'>");
			//header
			dyna.push("<tr>");
			if (true) {														
				dyna.push("<td norwap>&nbsp;&nbsp;"+"額度序號"+"</td>");
				dyna.push("<td norwap>"+"循環"+"</td>");
				dyna.push("<td norwap>&nbsp;&nbsp;"+"銷戶日期"+"</td>");
				dyna.push("<td norwap>&nbsp;&nbsp;"+"動用起迄"+"</td>");
				dyna.push("<td norwap>&nbsp;&nbsp;"+"授信起迄"+"</td>");
				dyna.push("<td norwap>&nbsp;&nbsp;"+"帳務資料"+"</td>");
			}							
			dyna.push("</tr>");
			$.each( json_q.lnf020_data.arr, function(idx, item){						
				dyna.push("<tr style='vertical-align:top;'>");						
				if (true) {														
					dyna.push("<td>&nbsp;"+DOMPurify.sanitize(String(item.LNF020_CONTRACT))
									+"<br/>"
									+"&nbsp;建檔日："+DOMPurify.sanitize(String(item.LNF020_CREATE_DT))
									+"&nbsp;</td>");
					dyna.push("<td>"+DOMPurify.sanitize(String(item.LNF020_REVOLVE))+"</td>");
					dyna.push("<td>&nbsp;"+DOMPurify.sanitize(String(item.LNF020_CANCEL_DATE))+"&nbsp;</td>");
					dyna.push("<td>"+DOMPurify.sanitize(String(item.LNF020_BEG_DATE))+"~<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.LNF020_END_DATE))+"&nbsp;</td>");
					dyna.push("<td>"+DOMPurify.sanitize(String(item.LNF020_DURATION_BG))+"~<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.LNF020_DURATION_ED))+"&nbsp;</td>");
					dyna.push("<td>");
						var _size = 0;
						$.each( item.LNF030_DATA, function(idx030, item030){						
							dyna.push("<span>["+DOMPurify.sanitize(String(item030.LNF030_CHARC_CODE))+"]&nbsp;"
									+DOMPurify.sanitize(String(item030.LNF030_LOAN_NO))
									+"&nbsp;產品["+DOMPurify.sanitize(String(item030.LNF030_LOAN_CLASS))+"]"
									+"&nbsp;建檔日："+DOMPurify.sanitize(String(item030.LNF030_OPEN_DATE))
									+"&nbsp;銷戶日："+DOMPurify.sanitize(String(item030.LNF030_CANCEL_DATE))									
									+"<br/>"
									+"&nbsp;首撥日："+DOMPurify.sanitize(String(item030.LNF030_LOAN_DATE))
									+"&nbsp;興建住宅["+DOMPurify.sanitize(String(item030.LNF030_RESIDENTIAL))+"]"
									+"&nbsp;</span><br/>");
							_size ++;
						});
						if(_size==0){
							dyna.push("&nbsp;");
						}
					dyna.push("</td>");
				}							
				dyna.push("</tr>");
			});
			dyna.push("</table>");
		}else{
			dyna.push("查到"+DOMPurify.sanitize(String(json_q.lnf020_size)) +"筆資料");	
		}		
		dyna.push("</div>");
		dyna.push("</p>");
		//======
		//MIS.ELF487
		dyna.push("<p>不動產十足擔保 態樣：");
		dyna.push("<div style='margin-left:20px'>");
		if(json_q.elf487_size > 0 ){
			dyna.push("<table border='1' width='100%'>");
			//header
			dyna.push("<tr style='vertical-align:top;'>");		
			if (true) {														
				dyna.push("<td norwap>&nbsp;&nbsp;"+"分行"+"</td>");
				dyna.push("<td norwap>"+"貸款成數逾八成且<br/>寬限期逾二年且<br/>信用評等經人工調升二(含)等以上者"+"</td>");
				dyna.push("<td norwap>&nbsp;"+"有辦理房貸壽險者"+"</td>")
				dyna.push("<td norwap>&nbsp;"+"敘做房貸，搭配無擔保科目者"+"</td>");
			}							
			dyna.push("</tr>");
			$.each( json_q.elf487_data.arr, function(idx, item){						
				dyna.push("<tr style='vertical-align:top;'>");						
				if (true) {														
					dyna.push("<td>&nbsp;"+DOMPurify.sanitize(String(item.branch))+"&nbsp;</td>");
					dyna.push("<td>&nbsp;"+DOMPurify.sanitize(String(item.have_ELF487_HOUSE_FG))+"&nbsp;</td>");
					dyna.push("<td>&nbsp;"+DOMPurify.sanitize(String(item.have_ELF487_RMBINS_FG))+"&nbsp;</td>");
					dyna.push("<td>&nbsp;"+DOMPurify.sanitize(String(item.have_ELF487_HS_CRE_FG))+"&nbsp;</td>");					
					dyna.push("</td>");
				}							
				dyna.push("</tr>");
			});
			dyna.push("</table>");
		}else{
			dyna.push("查到"+DOMPurify.sanitize(String(json_q.elf487_size)) +"筆資料");	
		}		
		dyna.push("</div>");
		dyna.push("</p>");
		//======
		//MIS.ELF591
		dyna.push("<p>國內消金、現金存入警示戶明細檔：");
		dyna.push("<div style='margin-left:20px'>");
		if(json_q.elf591_size > 0 ){
			dyna.push("<table border='1' width='100%'>");
			//header
			dyna.push("<tr>");
			if (true) {														
				dyna.push("<td norwap>&nbsp;&nbsp;"+"額度序號"+"</td>");
				dyna.push("<td norwap>"+"列於報表"+"</td>");
				dyna.push("<td norwap>&nbsp;&nbsp;"+"銷戶日"+"</td>")
				dyna.push("<td norwap>&nbsp;&nbsp;"+"已覆審日"+"</td>");
			}							
			dyna.push("</tr>");
			$.each( json_q.elf591_data.arr, function(idx, item){						
				dyna.push("<tr style='vertical-align:top;'>");						
				if (true) {														
					dyna.push("<td>&nbsp;"+DOMPurify.sanitize(String(item.cntrNo))+"&nbsp;</td>");
					dyna.push("<td>"+DOMPurify.sanitize(String(item.rptNo))+"&nbsp;</td>");
					dyna.push("<td>&nbsp;"+DOMPurify.sanitize(String(item.cancelDate))+"&nbsp;</td>");
					dyna.push("<td>&nbsp;"+DOMPurify.sanitize(String(item.lrDate))+"&nbsp;</td>");					
					dyna.push("</td>");
				}							
				dyna.push("</tr>");
			});
			dyna.push("</table>");
		}else{
			dyna.push("查到"+DOMPurify.sanitize(String(json_q.elf591_size)) +"筆資料");	
		}		
		dyna.push("</div>");
		dyna.push("</p>");
		//======
		//CMS
		if(showAdditional){
			dyna.push("<p>Web e-Loan擔保品：");
			dyna.push("<div style='margin-left:20px'>");
			if(json_q.cms_size > 0 ){
				dyna.push("<table border='1' width='100%'>");
				//header
				dyna.push("<tr>");
				if (true) {														
					dyna.push("<td>&nbsp;&nbsp;"+"flag"+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+"typCd"+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+"branch"+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+"custId"+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+"dupNo"+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+"collNo"+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+"docStatus"+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+"cntrNo"+"</td>");
				}							
				dyna.push("</tr>");
				$.each( json_q.cms_data.arr, function(idx, item){						
					dyna.push("<tr>");						
					if (true) {														
						dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.flag))+"</td>");
						dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.typCd))+"</td>");
						dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.branch))+"</td>");
						dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.custId))+"</td>");
						dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.dupNo))+"</td>");
						dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.collNo))+"</td>");
						dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.docStatus))+"</td>");
						dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.cntrNo))+"</td>");
					}							
					dyna.push("</tr>");
				});
				dyna.push("</table>");
			}else{
				dyna.push("查到"+DOMPurify.sanitize(String(json_q.cms_size)) +"筆資料");	
			}		
			dyna.push("</div>");
			dyna.push("</p>");
		}
		//======
		dyna.push("</div>");
		$q_result.html(dyna.join("\n"));		
		$q_result.thickbox({ // 使用選取的內容進行彈窗
	        title: '',
	        width: 1000,
	        height: 560,
	        align: "center",
	        valign: "bottom",
	        modal: false,
	        i18n: i18n.def,
	        buttons: {
	            "close": function(){
	                $.thickbox.close();
	            }
	        }
	    });        
    });
}
function f_q_cntrNo($q_result, q_cntrNo){
	$.ajax({ handler: 'lms2401m01formhandler', data: {formAction: "query_cntrNo", 'q_cntrNo':q_cntrNo}
	}).done(function(json_q){
		var dyna = [];
		dyna.push("<div>額度序號:"+DOMPurify.sanitize(String(json_q.cntrNo))+"</div>");
		
		if(json_q._size > 0 ){
			dyna.push("<table border='1' width='100%'>");
			//header
			dyna.push("<tr>");
			if (true) {														
				dyna.push("<td>&nbsp;&nbsp;"+"分行"+"</td>");
				dyna.push("<td>&nbsp;&nbsp;"+"客戶統編"+"</td>");
				dyna.push("<td>&nbsp;&nbsp;"+"重複序號"+"</td>");
				dyna.push("<td>&nbsp;&nbsp;"+"擔保品編號"+"</td>");
			}							
			dyna.push("</tr>");
			$.each( json_q._data.arr, function(idx, item){						
				dyna.push("<tr>");						
				if (true) {														
					dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.BRANCH))+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.CUSTID))+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.DUPNO))+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+DOMPurify.sanitize(String(item.COLLNO))+"</td>");
				}							
				dyna.push("</tr>");
			});
			dyna.push("</table>");
		}
		dyna.push("<div>查到"+DOMPurify.sanitize(String(json_q._size))+"筆資料</div>");
		
		
		$q_result.html(dyna.join("\n"));
		$q_result.thickbox({ // 使用選取的內容進行彈窗
	        title: '',
	        width: 380,
	        height: 250,
	        align: "center",
	        valign: "bottom",
	        modal: false,
	        i18n: i18n.def,
	        buttons: {
	            "close": function(){
	                $.thickbox.close();
	            }
	        }
	    });
    });
}

function type1_queryMenu(){
	var _id = "_div_type1_queryMenu";
	var _form = _id+"_form";
	var rdoName = 'decision_type1_queryMenu';
	
	if ($("#"+_id).length == 0){
		var dyna = [];
		dyna.push("<div id='"+_id+"' style='display:none;' >");		
		dyna.push("<form id='"+_form+"'>");
		
		//menu - 1
		dyna.push("<p>");
		dyna.push("<label><input type='radio' name='"+rdoName+"' value='1' class='required' />依客戶ID查詢</label>");
		dyna.push("<div>統一編號：<input type='text' name='q_custId' value=''  maxlength='10' size='12' />　重複序號：<input type='text' name='q_dupNo' value='0'  maxlength='1' size='1' /></div>");
		dyna.push("</p>");
		
		dyna.push("<br/>");
		
		//menu - 2
		dyna.push("<p>");
		dyna.push("<label><input type='radio' name='"+rdoName+"' value='2' class='required' />依額度序號查詢擔保品設定資料</label>");
		dyna.push("<div>額度序號：<input type='text' name='q_cntrNo' value='' maxlength='12' size='12' /></div>");
		dyna.push("</p>");
		
		
		dyna.push("</form>");
		dyna.push("</div>");
		
	     $('body').append(dyna.join(""));
	}
	//clear data
	//$("#"+_form).reset();
	
	$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
        title: '',
        width: 380,
        height: 250,
        align: "center",
        valign: "bottom",
        modal: false,
        i18n: i18n.def,
        buttons: {
            "sure": function(){
            	var $q_result = $("#q_result").empty();
            	
                var val = $("#"+_form).find("[name='decision_type1_queryMenu']:checked").val();
                var q_custId = $("#"+_form).find("[name='q_custId']").val();
                var q_dupNo = $("#"+_form).find("[name='q_dupNo']").val();
                var q_cntrNo = $("#"+_form).find("[name='q_cntrNo']").val();
                
                if(val=="1"){//依客戶ID查詢
                	$.thickbox.close();
                	f_q_custIdDupNo($q_result, q_custId, q_dupNo);
                	
                }else if(val=="2"){//依額度序號查詢擔保品設定資料
                	$.thickbox.close();
                	f_q_cntrNo($q_result, q_cntrNo);
                	
                }else{
                	API.showErrorMessage("請選擇查詢項目");
                }            
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}
function show_test(){
	$("p._test").show();
}
//upgradetodo
//function jsonParse(str) {
//    try {
//        return JSON.parse(str);
//    } catch(e) {
//        return API.showErrorMessage(e);
//    }
//}
