<html xmlns="http://www.w3.org/1999/xhtml" 
        xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="panelFragmentBody">
            <!-- 個金服務單位檔 -->
			<div id="C101S01BDiv" name="C101S01BDiv" >
				<form id="C101S01BForm" name="C101S01BForm" >
					<table class="tb2" width="100%">
						<tr>
							<td width="18%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01B.comName'}">服務單位名稱</th:block>&nbsp;&nbsp;</td>
							<td colspan="3" ><input type="text" id="comName" name="comName" class="max required" maxlength="250" size="30" />
								<table border='0' class='tb2' width='100%' style='margin:0px;'>
									<tr style='vertical-align:top;'>
										<td class='noborder'>
											<table border='0'>
												<tr>
													<td class='noborder'><th:block th:text="#{'C101S01B.cm1_serve_company.label'}">0024服務單位</th:block>：
													</td>
													<td class='noborder'><span id='cm1_serve_company' name='cm1_serve_company' ></span> &nbsp;
													</td>
												</tr>
												<tr>
													<td class='noborder'><th:block th:text="#{'C101S01B.cm1_job_business_code.label'}">0024服務單位行業對象別</th:block>：
													</td>
													<td class='noborder'><span id='cm1_job_business_InfoStr' name='cm1_job_business_InfoStr' ></span> &nbsp;
													</td>
												</tr>
												<tr>
													<td class='noborder'><th:block th:text="#{'C101S01B.cm1_title_code.label'}">0024職稱代碼</th:block>：
													</td>
													<td class='noborder'><span id='cm1_job_title_InfoStr' name='cm1_job_title_InfoStr' ></span> &nbsp;
													</td>
												</tr>
												
											</table>				
										</td>
										<td class='nowrap noborder'>資料日期：<span  id="cm1_dataDt" name="cm1_dataDt"  style='width:14px;' ></span>  <button type="button" id='btnImpCm1DataBlock' name='btnImpCm1DataBlock'><span class="text-only">引入</span></button>
										</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr>
							<td width="18%" class="hd2" align="right" ><th:block th:text="#{'C101S01B.comAddr'}">服務單位地址</th:block>&nbsp;&nbsp;</td>
							<td colspan="3" >
								<input type="text" id="comCity" name="comCity" style="display:none;" />
								<input type="text" id="comZip" name="comZip" style="display:none;" />
								<input type="text" id="comAddr" name="comAddr" style="display:none;" />
								<a href="#" id="comTargetLink" class="readOnly" ><span id="comTarget" class="field comboSpace" ></span></a>
							</td>
						</tr>
						<tr>
							<td width="18%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01B.comTel'}">服務單位電話</th:block>&nbsp;&nbsp;</td>
							<td width="32%" ><input type="text" id="comTel" name="comTel" class="max" maxlength="150" /></td>
							<td width="18%" class="hd2" align="right" ><th:block th:text="#{'C101S01B.workDate'}">到職日期</th:block>&nbsp;&nbsp;</td>
							<td width="32%" ><input type="text" id="workDate" name="workDate" class="date" /></td>
						</tr>
						<tr>
							<td width="18%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01B.isSameWorkAttributes'}">前職與現職同屬性 (限前一份工作)</th:block>&nbsp;&nbsp;</td>
							<td width="33%">
								<input type="radio" id="isSameWorkAttributes" name="isSameWorkAttributes" value='Y' /><th:block th:text="#{'rdo.yes'}">是</th:block>&nbsp;
								<input type="radio" id="isSameWorkAttributes" name="isSameWorkAttributes" value='N' style='padding-left:30px;' /><th:block th:text="#{'rdo.no'}">否</th:block>&nbsp;													
							</td>
							<td/>
							<td/>
						</tr>
						<tr>
							<td class="hd2" align="right"><span class="text-red" id="taxIDStart"></span><th:block th:text="#{'C101S01B.juId'}">服務單位統一編號</th:block>&nbsp;&nbsp;</td>								
							<td >
								<!--<label><input type="radio" id="ynJuId" name="ynJuId" value="Y"/><th:block th:text="#{'rdo.have'}">有</th:block></label> -->
								<input type="text" id="juId" name="juId" class=" " maxlength="11" size="11" />
								<label style="letter-spacing:0px;cursor:pointer;">
										<input id="isNPO" class="" type="checkbox" value="Y" name="isNPO">
										非營利事業
								</label>
								<!--<br/>   <label><input type="radio" id="ynJuId" name="ynJuId" value="N"/><th:block th:text="#{'nohave'}">無</th:block></label> -->
							</td>
							<td class="hd2" align="right" ><!--<span class="text-red">＊</span>--><th:block th:text="#{'C101S01B.juTotalCapital'}">服務單位資本總額(新台幣元)</th:block>&nbsp;&nbsp;</td>
							<td >
								<!--<label><input type="radio" id="ynJuTotalCapital" name="ynJuTotalCapital" value="Y"/><th:block th:text="#{'rdo.have'}">有</th:block></label> -->
								<label>TWD</label><input type="text" id="juTotalCapital" name="juTotalCapital" class="numeric " integer="19" maxlength="25" size="19" /> <th:block th:text="#{'money.unit.1'}">元</th:block>	
								<!--<br/>  <label><input type="radio" id="ynJuTotalCapital" name="ynJuTotalCapital" value="N"/><th:block th:text="#{'nohave'}">無</th:block></label> -->
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><span class="text-red" id="capitalStart"></span><th:block th:text="#{'C101S01B.juPaidUpCapital'}">服務單位實收資本額(新台幣元)</th:block>&nbsp;&nbsp;</td>
							<td >
								<!--<label><input type="radio" id="ynJuPaidUpCapital" name="ynJuPaidUpCapital" value="Y"/><th:block th:text="#{'rdo.have'}">有</th:block></label> -->
								<label>TWD</label><input type="text" id="juPaidUpCapital" name="juPaidUpCapital" class="numeric " integer="19" maxlength="25" size="19"/> <th:block th:text="#{'money.unit.1'}">元</th:block>
								<label style="letter-spacing:0px;cursor:pointer;">
									<input id="hasJuTotalCapital" name="hasJuTotalCapital" class="" type="checkbox" value="N">
									<th:block th:text="#{'nohave'}">無</th:block>
								</label>
								<!--<br/>  <label><input type="radio" id="ynJuPaidUpCapital" name="ynJuPaidUpCapital" value="N"/><th:block th:text="#{'nohave'}">無</th:block></label> -->
							</td>
							<td class="hd2" align="right" ><!--<span class="text-red">＊</span>--><th:block th:text="#{'C101S01B.juType'}">服務單位組織類型</th:block>&nbsp;&nbsp;</td>
							<td >
								<select id="juType" name="juType" class=" " data-codetype="C101S01B_juType" space="true" ></select>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right" ><!--<span class="text-red">＊</span>--><th:block th:text="#{'C101S01B.ptaFlag'}">薪轉戶註記</th:block>&nbsp;&nbsp;</td>
							<td colspan='3'>
								<label><input type="radio" id="ptaFlag" name="ptaFlag" value="Y" disabled /><th:block th:text="#{'rdo.yes'}">是</th:block>
								</label><th:block th:text="#{'C101S01B.ptaTaxNo.label'}">給薪公司</th:block>：<span  id="ptaTaxNo" name="ptaTaxNo" style='width:12px;' ></span><th:block th:text="#{'C101S01B.ptaGrade.descBf'}">(評等：</th:block><span  id="ptaGrade" name="ptaGrade" style='width:6px;'  ></span><th:block th:text="#{'C101S01B.ptaGrade.descAf'}">)</th:block> 
								資料日期：<span  id="ptaDataDt" name="ptaDataDt"  style='width:14px;' ></span>  <button type="button" id='btnImpPtaFlag' name='btnImpPtaFlag'><span class="text-only">引入</span></button>
								<br/> <!-- * --> <label><input type="radio" id="ptaFlag" name="ptaFlag" value="N" disabled /><th:block th:text="#{'rdo.no'}">否</th:block></label>
							</td>
						</tr>
						<tr id="cls_job_type_fieldset" style="display: none;">
							<td class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01B.clsJobType'}">歡喜信貸行業別</th:block>&nbsp;&nbsp;</td>
							<td >
								<select id="clsJobType1" name="clsJobType1" data-codetype="clsJobType" ></select><br/>
								<select id="clsJobType2" name="clsJobType2" style="width:250px" ></select>
							</td>
							<td class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01B.clsJobTitle'}">歡喜信貸職位別</th:block>&nbsp;&nbsp;</td>
							<td ><select id="clsJobTitle" name="clsJobTitle" data-codetype="clsJobTitle" ></select></td>
						</tr>
						<tr>
							<td class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01B.jobType'}">職業別</th:block>&nbsp;&nbsp;</td>
							<td >
								<select id="jobType1" name="jobType1" class="required" codeType="jobType" ></select><br/>
								<select id="jobType2" name="jobType2" class="required" style="width:250px" ></select>
							</td>
							<td class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01B.jobTitle'}">職稱</th:block>&nbsp;&nbsp;</td>
							<td ><select id="jobTitle" name="jobTitle" class="required" codeType="lms1205s01_jobTitle" ></select></td>
						</tr>
						<tr>
							<td class="hd2" align="right" ><span class="text-red">＊</span><th:block th:utext="#{'C101S01B.seniority'}">年資(同質性工作連續年資)</th:block>&nbsp;&nbsp;</td>
							<td ><input type="text" id="snrY" name="snrY" class="max number required" maxlength="2" size="2" />年
								<input type="text" id="snrM" name="snrM" class="max number required" maxlength="2" size="2" />月
								
								<!-- 本來的 seniority 是 html text input-->
								<input type="hidden" id="seniority" name="seniority" value=""><!-- J-110-0298 , (110)第(2133)號 改成透過 ajax運算，取得結合{snrY、snrM}的結果 -->
							</td>
							<td class="hd2" align="right" ><span class="text-red">＊</span>收入
								<br>
								<button type="button" id="openIncomeView" class="forview"><th:block th:text="#{'C101S01B.openIncomeView'}">編輯收入內容</th:block></button></td>
								<!--<a href="#1" id="openIncomeView">編輯收入內容</a></td>-->
							<td >

								<span class="text-red">＊</span><th:block th:text="#{'C101S01B.payAmt'}">年薪(經常性收入)</th:block>
								<br>
								<select id="payCurr" name="payCurr" class="required" codeType="Common_Currcy" itemStyle="format:{value}-{key}" ></select>
								<input type="text" id="payAmt" name="payAmt" class="max number required" maxlength="13" size="13" readonly="readonly" />
								<th:block th:text="#{'money.unit'}">萬元</th:block>

								<br>
								<th:block th:text="#{'C101S01B.othAmt'}">其他收入(非經常性收入)</th:block>
								<br>
								<select id="othCurr" name="othCurr" codeType="Common_Currcy" itemStyle="format:{value}-{key}" ></select>
								<input type="text" id="othAmt" name="othAmt" class="max number" maxlength="13" size="13" readonly="readonly" />
								<th:block th:text="#{'money.unit'}">萬元</th:block>
							</td>
						</tr>
						<tr>
							<td width="18%" class="hd2" align="right"><th:block th:text="#{'C101S01B.highRiskJobFlag'}">高風險職業(信貸專用)</th:block>&nbsp;&nbsp;</td>
							<td width="33%">
								<input type="radio" id="highRiskJobFlag" name="highRiskJobFlag" value='1' /><th:block th:text="#{'rdo.yes'}">是</th:block>&nbsp;
								<input type="radio" id="highRiskJobFlag" name="highRiskJobFlag" value='0' checked style='padding-left:30px;' /><th:block th:text="#{'rdo.no'}">否</th:block>&nbsp;
								<!-- J-113-0174_12473_B1001 個金基本資料-服務單位-高風險職業移除信貸各類客群定義表
								<a href="/lms-web/img/lms/CLS1131_CreditLoanTermGroupTypeList.pdf" target="_blank" style="font-weight: bold">
								<th:block th:text="#{'C101S01B.creditListOfVariousCustomerGroups'}">信貸各類客群定義表</th:block></a>
								-->													
							</td>
							<td width="18%" class="hd2" align="right" >
								<th:block th:text="#{'C101S01B.esgScore'}">ESG分數(信貸專用)</th:block>&nbsp;&nbsp;
							</td>
							<td width="33%">
                                <input type="radio" id="isHasEsgScore" name="isHasEsgScore" value="Y" />有
                                <input type="radio" id="isHasEsgScore" name="isHasEsgScore" value="N" checked/>無
								<input type="text" id="esgScore" name="esgScore" size="4" maxlength="6" integer="3" fraction="2" class="numeric"/>
								&nbsp;&nbsp;
								<a href="https://irplatform.tdcc.com.tw/ir/zh/" target="_blank" style="font-weight: bold"><th:block th:text="#{'C101S01B.esgScoreInquiry'}">ESG分數查詢</th:block></a>
							</td>
						</tr>
						<!--J-112-0467 新增ELOAN/個金授信，得透過決策平台自動判斷客群之功能-->
						<tr class="tr_isMarkModel3">
		        			<td class="hd1">
		        				<th:block th:text="#{'C101S01B.termGroup'}"><!--歡喜信貸客群--></th:block>
		        			</td>
							<td>
								<select id="termGroup" name="termGroup" space="true" combokey="L140S02A_termGroup" comboType="2" class="required" style="display:none"></select>
								<div style="color:red;font-weight:bold;font-size: 13px;"><span id="span_termGroupRuleResultText"></span></div>
							</td>
						</tr>
						<!--<tr>-->
							<!--<td class="hd2" align="right" rowspan="3" ><th:block th:text="#{'C101S01B.othType'}">其他收入項目</th:block>&nbsp;&nbsp;</td>-->
							<!--<td rowspan="3" ><input type="checkbox" id="othType" name="othType" class="max"  /></td>-->
							<!--<td class="hd2" align="right" ><th:block th:text="#{'C101S01B.othAmt'}">其他收入</th:block>&nbsp;&nbsp;</td>-->
							<!--<td >-->
								<!--<select id="othCurr" name="othCurr" codeType="Common_Currcy" itemStyle="format:{value}-{key}" ></select>-->
								<!--<input type="text" id="othAmt" name="othAmt" class="max number" maxlength="13" size="13" />-->
								<!--<th:block th:text="#{'money.unit'}">萬元</th:block>-->
							<!--</td>-->
						<!--</tr>-->
						<!--<tr>-->
							<!--<td class="hd2" align="right" >&lt;!&ndash; <span class="text-red">＊</span>&ndash;&gt;<th:block th:text="#{'C101S01B.inDoc'}">個人所得證明文件</th:block>&nbsp;&nbsp;</td>-->
							<!--<td ><select id="inDoc" name="inDoc" class=" " codeType="lms1205s01_inDoc" ></select></td>-->
						<!--</tr>-->
						<!--<tr>-->
							<!--<td class="hd2" align="right" ><th:block th:text="#{'C101S01B.experience'}">經歷</th:block>&nbsp;&nbsp;</td>-->
							<!--<td >-->
								<!--&lt;!&ndash; <input type="text" id="experience" name="experience" class="max" maxlength="60" /> &ndash;&gt;-->
								<!--<textarea id="experience" name="experience" class="max" maxlength="250" cols="40" Rows="5" />-->
							<!--</td>-->
						<!--</tr>-->
					</table>
				</form>
			</div>
			<!--
			J-110-0380 依 c101s01b.incomeDetailVer 去組合出 url 是 /cls1131s01bincomev1 或 /cls1131s01bincomev2 或 /cls1131s01bincomev3 ...........  
			-->
			<div id="personalIncomeDetailViewVer" style="display:none"></div>
			
			<!--
			在增加 incomeDetailVer 之前的舊版UI
			-->
			<div id="personalIncomeDetailView" style="display:none">
				<form id="personalIncomeDetailForm" name="personalIncomeDetailForm"  >
				<div>
					<table class="tb2" width="100%">
						<tr>
							<td class="ct hd2"><th:block th:text="#{'C101S01B.positionType.001'}">職位別(單選)</th:block></td>
						</tr>
						<tr>
							<td>
								<label><input type="radio" name="positionType" id="positionType" value="1" class="required editable"><th:block th:text="#{'C101S01B.positionType.1'}">1.非藍領</th:block></label>
								<label><input type="radio" name="positionType" value="2" class="editable"><th:block th:text="#{'C101S01B.positionType.2'}">2.藍領</th:block></label>
								<label><input type="radio" name="positionType" value="3" class="editable"><th:block th:text="#{'C101S01B.positionType.3'}">3.業務職(底薪+獎金者)</th:block></label>
							</td>
						</tr>

						<tr class="personalIncomeDetail hide">
							<td>
								<table class="tb2" width="100%">

									<tr>
										<td colspan="5" class="ct hd2"><th:block th:text="#{'C101S01B.inCome.001'}">經常性收入</th:block></td>
									</tr>
									<tr>
										<td colspan="2" class="ct hd2" width="7%"><th:block th:text="#{'C101S01B.inCome.002'}">項目</th:block></td>
										<td colspan="2" class="ct hd2" width="63%"><th:block th:text="#{'C101S01B.inCome.003'}">內容</th:block></td>
										<td class="ct hd2"><th:block th:text="#{'C101S01B.inCome.004'}">年化後金額</th:block></td>
									</tr>
									<tr class="incomeTypeA">
										<td><input type="radio" id="mainIncomeType" name="mainIncomeType" value="A" class="editable"></td>
										<td>A</td>
										<td class="hd2">
											<span><th:block th:text="#{'C101S01B.mainIncomeTypeA.001'}">所得清單/扣繳憑單</th:block></span>
											<br>
											<span class="color-red"><th:block th:text="#{'C101S01B.mainIncomeTypeA.002'}">須排除以下項目：</th:block></span>
											<ul>
												<li><span class="color-red"><th:block th:text="#{'C101S01B.mainIncomeTypeA.003'}">財產交易(76)(96D)</th:block></span></li>
												<li><span class="color-red"><th:block th:text="#{'C101S01B.mainIncomeTypeA.004'}">競技競賽機會中獎獎金(91)</th:block></span></li>
												<!--<li><th:block th:text="#{'C101S01B.mainIncomeTypeA.005'}">租金收入(51)</th:block></li>-->
												<li><span class="color-red"><th:block th:text="#{'C101S01B.mainIncomeTypeA.006'}">退職所得(93)</th:block></span></li>
											</ul>
										</td>
										<td>
											<input type="text" class="numeric required" integer="13" maxlength="25" size="8" name="itemAvalue" id="itemAvalue"> <th:block th:text="#{'C101S01B.inCome.Year'}">/年</th:block>
											<br>
											<th:block th:text="#{'C101S01B.mainIncomeTypeA.007'}">提醒(1):請確認是否已排除左列項目</th:block>
											<br>
											<th:block th:text="#{'C101S01B.mainIncomeTypeA.008'}">提醒(2):如所得清單/扣繳憑單內有退職所得(93)請改填C類收入</th:block>
											<br/>
											<th:block th:text="#{'C101S01B.mainIncomeTypeA.009'}">提醒(3):留意薪資類所得為零頭情況(可能為假收入)</th:block>
										</td>
										<td><input type="text" class="numeric readonly" integer="13" maxlength="25" size="8" name="itemAvalueYear" id="itemAvalueYear" ></td>
									</tr>
									<tr class="incomeTypeB1">
										<td><input type="radio" name="mainIncomeType" value="B1" class="editable"></td>
										<td>B1</td>
										<td class="hd2">
											<th:block th:text="#{'C101S01B.mainIncomeTypeB1.001'}">薪轉存摺/薪資單</th:block>
										</td>
										<td>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB1value1" id="itemB1value1">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB1value2" id="itemB1value2">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB1value3" id="itemB1value3"> <th:block th:text="#{'C101S01B.inCome.Month'}">/月</th:block>
											<div style="margin: 0;padding: 0;" class="forPositionType3">
												<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB1value4" id="itemB1value4">
												<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB1value5" id="itemB1value5">
												<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB1value6" id="itemB1value6"> <th:block th:text="#{'C101S01B.inCome.Month'}">/月</th:block>
											</div>

											<br>
											<th:block th:text="#{'C101S01B.mainIncomeTypeB1.002'}">三節獎金：</th:block><input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB1HolidayBonus" id="itemB1HolidayBonus"> /年
											<br>
											<th:block th:text="#{'C101S01B.mainIncomeTypeB1.003'}">年終獎金：</th:block><input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB1YearEndBonus" id="itemB1YearEndBonus"> /年
											<br/>
											<th:block th:text="#{'C101S01B.mainIncomeTypeB1.004'}">提醒：留意每期薪轉金額為零頭情況(可能為假收入)</th:block>
										</td>
										<td><input type="text" class="numeric readonly" integer="13" maxlength="25" size="8" name="itemB1valueYear" id="itemB1valueYear" ></td>
									</tr>
									<tr class="incomeTypeB2">
										<td><input type="radio" name="mainIncomeType" value="B2" class="editable"></td>
										<td>B2</td>
										<td class="hd2">
											<th:block th:text="#{'C101S01B.mainIncomeTypeB2.001'}">勞保投保明細/個人投保紀錄</th:block>
										</td>
										<td>
											<input type="text" class="numeric required" integer="13" maxlength="25" size="8" name="itemB2value" id="itemB2value"> <th:block th:text="#{'C101S01B.inCome.Month'}">/月</th:block>
										</td>
										<td><input type="text" class="numeric readonly" integer="13" maxlength="25" size="8" name="itemB2valueYear" id="itemB2valueYear"></td>
									</tr>
									<tr class="incomeTypeB3">
										<td><input type="radio" name="mainIncomeType" value="B3" class="editable"></td>
										<td>B3</td>
										<td class="hd2">
											<th:block th:text="#{'C101S01B.mainIncomeTypeB3.001'}">營利事業所得申報書/銷售額與稅額申報書(401/403)</th:block>
											<br>
											<span class="color-red"><th:block th:text="#{'C101S01B.mainIncomeTypeB3.002'}">限負責人申辦</th:block></span>
										</td>
										<td>
											<th:block th:text="#{'C101S01B.mainIncomeTypeB3.003'}">報表種類：</th:block><label><input type="radio" name="itemB3ReportType" id="itemB3ReportType" value="A" class="required">401 </label>　<label><input type="radio" name="itemB3ReportType" value="B">403</label>
											<br>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB3value1" id="itemB3value1">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB3value2" id="itemB3value2">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB3value3" id="itemB3value3"> <th:block th:text="#{'C101S01B.inCome.Period'}">/期</th:block>

											<!--<div style="margin: 0;padding: 0;" class="forItemB3ReportTypeB">-->
												<!--<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB3value4" id="itemB3value4">-->
												<!--<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB3value5" id="itemB3value5">-->
												<!--<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB3value6" id="itemB3value6"> <th:block th:text="#{'C101S01B.inCome.Period'}">/期</th:block>-->
											<!--</div>-->
											<br>
											<th:block th:text="#{'C101S01B.mainIncomeTypeB3InProfit'}">產業利潤率中之淨利率：</th:block><input type="text" class="numeric required" integer="3" maxlength="3" size="8" name="itemB3InProfit" id="itemB3InProfit" max="100" min="0"> %
											<br>
											<a href="http://web02.mof.gov.tw/std/main.htm" target="_blank" >依稅務行業標準暨同業利潤標準查詢系統公告之利潤率</a>
											<br>
											<th:block th:text="#{'C101S01B.mainIncomeTypeB3Holding'}">持股：</th:block><input type="text" class="numeric required" integer="3" fraction="2" maxlength="6" size="8" name="itemB3Holding" id="itemB3Holding" max="100" min="0"> %
										</td>
										<td><input type="text" class="numeric readonly" integer="13" maxlength="25" size="8" name="itemB3valueYear" id="itemB3valueYear"></td>
									</tr>
									<tr class="incomeTypeB4">
										<td><input type="radio" name="mainIncomeType" value="B4" class="editable"></td>
										<td>B4</td>
										<td class="hd2">
											<th:block th:text="#{'C101S01B.mainIncomeTypeB4.001'}">現金收入</th:block>
										</td>
										<td>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB4value1" id="itemB4value1">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB4value2" id="itemB4value2">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB4value3" id="itemB4value3"> <th:block th:text="#{'C101S01B.inCome.Month'}">/月</th:block>
											<br>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB4value4" id="itemB4value4">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB4value5" id="itemB4value5">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemB4value6" id="itemB4value6"> <th:block th:text="#{'C101S01B.inCome.Month'}">/月</th:block>
											<br>
											<th:block th:text="#{'C101S01B.itemDisRate'}">折算率</th:block>：<input type="text" class="numeric required" integer="3" maxlength="3" size="8" name="itemB4DisRate" id="itemB4DisRate" max="100" min="0">%
											<br>
											<th:block th:text="#{'C101S01B.mainIncomeTypeB4.002'}">提醒：經營業單位實地訪查屬實，並於簽報書內據以分析其營運情況後，得以認列核計個人所得。</th:block>
										</td>
										<td><input type="text" class="numeric readonly" integer="13" maxlength="25" size="8" name="itemB4valueYear" id="itemB4valueYear"></td>
									</tr>
									<tr class="otherTypeC1">
										<td><input type="checkbox" name="otherC1" id="otherC1" value="Y" class="editable"></td>
										<td>C1</td>
										<td class="hd2">
											<th:block th:text="#{'C101S01B.otherTypeC1.001'}">定存利息</th:block>
										</td>
										<td>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC1value1" id="itemC1value1">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC1value2" id="itemC1value2">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC1value3" id="itemC1value3"> <th:block th:text="#{'C101S01B.inCome.Month'}">/月</th:block>
											<br>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC1value4" id="itemC1value4">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC1value5" id="itemC1value5">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC1value6" id="itemC1value6"> <th:block th:text="#{'C101S01B.inCome.Month'}">/月</th:block>
										</td>
										<td><input type="text" class="numeric readonly" integer="13" maxlength="25" size="8" name="itemC1valueYear" id="itemC1valueYear"></td>
									</tr>
									<tr class="otherTypeC2">
										<td><input type="checkbox" name="otherC2" id="otherC2" value="Y" class="editable"></td>
										<td>C2</td>
										<td class="hd2">
											<th:block th:text="#{'C101S01B.otherTypeC2.001'}">租金收入</th:block>
											<br>
											<span class="color-red"><th:block th:text="#{'C101S01B.otherTypeC2.003'}">不得與所得清單/扣繳憑單重複認列</th:block></span>
										</td>
										<td>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC2value1" id="itemC2value1">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC2value2" id="itemC2value2">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC2value3" id="itemC2value3"> <th:block th:text="#{'C101S01B.inCome.Month'}">/月</th:block>
											<br>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC2value4" id="itemC2value4">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC2value5" id="itemC2value5">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC2value6" id="itemC2value6"> <th:block th:text="#{'C101S01B.inCome.Month'}">/月</th:block>
											<br>
											<span><th:block th:text="#{'C101S01B.otherTypeC2.002'}">須一併檢附房屋租賃契約及房屋所有權證明(均須為借款人本人)</th:block></span>
										</td>
										<td><input type="text" class="numeric readonly" integer="13" maxlength="25" size="8" name="itemC2valueYear" id="itemC2valueYear"></td>
									</tr>
									<tr class="otherTypeC3">
										<td><input type="checkbox" name="otherC3" id="otherC3" value="Y" class="editable"></td>
										<td>C3</td>
										<td class="hd2">
											<th:block th:text="#{'C101S01B.otherTypeC3.001'}">退休/退撫/退役收入</th:block>
										</td>
										<td>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC3value1" id="itemC3value1">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC3value2" id="itemC3value2">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC3value3" id="itemC3value3"> <th:block th:text="#{'C101S01B.inCome.Month'}">/月</th:block>
											<br>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC3value4" id="itemC3value4">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC3value5" id="itemC3value5">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemC3value6" id="itemC3value6"> <th:block th:text="#{'C101S01B.inCome.Month'}">/月</th:block>
										</td>
										<td><input type="text" class="numeric readonly" integer="13" maxlength="25" size="8" name="itemC3valueYear" id="itemC3valueYear"></td>
									</tr>
									<tr>
										<td colspan="5" class="ct hd2">
											<th:block th:text="#{'C101S01B.inCome.006'}">非經常性收入(資產折算收入)</th:block>
										</td>
									</tr>
									<tr>
										<td colspan="5">
											<ul>
												<li><th:block th:text="#{'C101S01B.inCome.007'}">以下非經常性收入須無任何抵押借款、排除融資、融券、質借情形。</th:block></li>
												<li><th:block th:text="#{'C101S01B.inCome.008'}">人身保險不包含意外險及旅遊平安險等純保障險種。</th:block></li>
												<li><th:block th:text="#{'C101S01B.inCome.009'}">須提供近六個月資產證明。</th:block></li>
											</ul>
										</td>
									</tr>
									<tr class="otherTypeD4">
										<td><input type="checkbox" name="otherD4" id="otherD4" value="Y" class="editable"></td>
										<td>D4</td>
										<td class="hd2">
											<th:block th:text="#{'C101S01B.otherTypeD4.001'}">活存/定存</th:block>
											<br>
											<span class="color-red"><th:block th:text="#{'C101S01B.inCome.005'}">(請輸入餘額)</th:block></span>
										</td>
										<td>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD4value1" id="itemD4value1">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD4value2" id="itemD4value2">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD4value3" id="itemD4value3"> <th:block th:text="#{'C101S01B.inCome.Dollar'}">/元</th:block>
											<br>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD4value4" id="itemD4value4">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD4value5" id="itemD4value5">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD4value6" id="itemD4value6"> <th:block th:text="#{'C101S01B.inCome.Dollar'}">/元</th:block>
										</td>
										<td><input type="text" class="numeric readonly" integer="13" maxlength="25" size="8" name="itemD4valueYear" id="itemD4valueYear"></td>
									</tr>
									<tr class="otherTypeD5">
										<td><input type="checkbox" name="otherD5" id="otherD5" value="Y" class="editable"></td>
										<td>D5</td>
										<td class="hd2">
											<th:block th:text="#{'C101S01B.otherTypeD5.001'}">基金</th:block>
											<br>
											<span class="color-red"><th:block th:text="#{'C101S01B.inCome.005'}">(請輸入餘額)</th:block></span>
										</td>
										<td>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD5value1" id="itemD5value1">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD5value2" id="itemD5value2">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD5value3" id="itemD5value3"> <th:block th:text="#{'C101S01B.inCome.Dollar'}">/元</th:block>
											<br>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD5value4" id="itemD5value4">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD5value5" id="itemD5value5">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD5value6" id="itemD5value6"> <th:block th:text="#{'C101S01B.inCome.Dollar'}">/元</th:block>
										</td>
										<td><input type="text" class="numeric readonly" integer="13" maxlength="25" size="8" name="itemD5valueYear" id="itemD5valueYear"></td>
									</tr>
									<tr class="otherTypeD6">
										<td><input type="checkbox" name="otherD6" id="otherD6" value="Y" class="editable"></td>
										<td>D6</td>
										<td class="hd2">
											<th:block th:text="#{'C101S01B.otherTypeD6.001'}">公開發行債券</th:block>
											<br>
											<span class="color-red"><th:block th:text="#{'C101S01B.inCome.005'}">(請輸入餘額)</th:block></span>
										</td>
										<td>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD6value1" id="itemD6value1">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD6value2" id="itemD6value2">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD6value3" id="itemD6value3"> <th:block th:text="#{'C101S01B.inCome.Dollar'}">/元</th:block>
											<br>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD6value4" id="itemD6value4">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD6value5" id="itemD6value5">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD6value6" id="itemD6value6"> <th:block th:text="#{'C101S01B.inCome.Dollar'}">/元</th:block>
										</td>
										<td><input type="text" class="numeric readonly" integer="13" maxlength="25" size="8" name="itemD6valueYear" id="itemD6valueYear"></td>
									</tr>
									<tr class="otherTypeD7">
										<td><input type="checkbox" name="otherD7" id="otherD7" value="Y" class="editable"></td>
										<td>D7</td>
										<td class="hd2">
											<th:block th:text="#{'C101S01B.otherTypeD7.001'}">人身保險之保單價值準備</th:block>
											<br>
											<span class="color-red"><th:block th:text="#{'C101S01B.inCome.005'}">(請輸入餘額)</th:block></span>
										</td>
										<td>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD7value1" id="itemD7value1">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD7value2" id="itemD7value2">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD7value3" id="itemD7value3"> <th:block th:text="#{'C101S01B.inCome.Dollar'}">/元</th:block>
											<br>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD7value4" id="itemD7value4">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD7value5" id="itemD7value5">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD7value6" id="itemD7value6"> <th:block th:text="#{'C101S01B.inCome.Dollar'}">/元</th:block>
										</td>
										<td><input type="text" class="numeric readonly" integer="13" maxlength="25" size="8" name="itemD7valueYear" id="itemD7valueYear"></td>
									</tr>
									<tr class="otherTypeD8">
										<td><input type="checkbox" name="otherD8" id="otherD8" value="Y" class="editable"></td>
										<td>D8</td>
										<td class="hd2">
											<th:block th:text="#{'C101S01B.otherTypeD8.001'}">上市(櫃)公司股票</th:block>
											<br>
											<span class="color-red"><th:block th:text="#{'C101S01B.inCome.005'}">(請輸入餘額)</th:block></span>
										</td>
										<td>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD8value1" id="itemD8value1">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD8value2" id="itemD8value2">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD8value3" id="itemD8value3"> <th:block th:text="#{'C101S01B.inCome.Dollar'}">/元</th:block>
											<br>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD8value4" id="itemD8value4">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD8value5" id="itemD8value5">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD8value6" id="itemD8value6"> <th:block th:text="#{'C101S01B.inCome.Dollar'}">/元</th:block>
										</td>
										<td><input type="text" class="numeric readonly" integer="13" maxlength="25" size="8" name="itemD8valueYear" id="itemD8valueYear"></td>
									</tr>
									<tr class="otherTypeD9">
										<td><input type="checkbox" name="otherD9" id="otherD9" value="Y" class="editable"></td>
										<td>D9</td>
										<td class="hd2">
											<th:block th:text="#{'C101S01B.otherTypeD9.001'}">其他</th:block>
										</td>
										<td>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD9value1" id="itemD9value1">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD9value2" id="itemD9value2">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD9value3" id="itemD9value3"> <th:block th:text="#{'C101S01B.inCome.Dollar'}">/元</th:block>
											<br>
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD9value4" id="itemD9value4">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD9value5" id="itemD9value5">
											<input type="text" class="numeric" integer="13" maxlength="25" size="8" name="itemD9value6" id="itemD9value6"> <th:block th:text="#{'C101S01B.inCome.Dollar'}">/元</th:block>
											<br>
											<th:block th:text="#{'C101S01B.itemDisRate'}">折算率</th:block>：<input type="text" class="numeric required" integer="3" maxlength="3" size="8" name="itemD9DisRate" id="itemD9DisRate" max="100" min="0">%
										</td>
										<td><input type="text" class="numeric readonly" integer="13" maxlength="25" size="8" name="itemD9valueYear" id="itemD9valueYear"></td>
									</tr>
								</table>

							</td>
						</tr>
					</table>
				</div>
				</form>
			</div>
    	</th:block>
    </body>
</html>
