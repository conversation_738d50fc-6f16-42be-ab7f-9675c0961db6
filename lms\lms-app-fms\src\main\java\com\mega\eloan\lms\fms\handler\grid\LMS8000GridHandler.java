package com.mega.eloan.lms.fms.handler.grid;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.fms.pages.LMS8000M01Page;
import com.mega.eloan.lms.fms.service.LMS8000Service;
import com.mega.eloan.lms.mfaloan.bean.ELF602;
import com.mega.eloan.lms.mfaloan.service.MisELF447nService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L260M01A;
import com.mega.eloan.lms.model.L260M01C;
import com.mega.eloan.lms.model.L260M01D;
import com.mega.eloan.lms.model.L260S01A;
import com.mega.eloan.lms.model.L260S01C;
import com.mega.eloan.lms.obsdb.service.ObsdbELF601Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 貸後管理作業
 * 
 * </pre>
 * 
 * @since 2020
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms8000gridhandler")
public class LMS8000GridHandler extends AbstractGridHandler {

	@Resource
	ICustomerService customerSrv;

	@Resource
	DocFileService docFileService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	LMS8000Service lms8000Service;

	@Resource
	BranchService branchService;

	@Resource
	ObsdbELF601Service obsdbELF601Service;

	@Resource
	DwdbBASEService dwdbBASEService;

	@Resource
	UserInfoService userService;

	@Resource
	MisELF447nService misELF447nService;

	Properties pop = MessageBundleScriptCreator
			.getComponentResource(LMS8000M01Page.class);

	@SuppressWarnings("rawtypes")
	public CapMapGridResult queryGetCntrno(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brNo).getBrNoFlag());

		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));
		// 1-額度序號 2-額度序號+放款帳號
		String newType = Util.nullToSpace(params.getString("newType"));

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();

		if (!custId.isEmpty() && !dupNo.isEmpty()) {
			List<Map<String, Object>> data = null;
			if (isOverSea) {
				data = dwdbBASEService.findDW_ASLNDNEWOVS_ByBrNoCustId(brNo,
						custId, dupNo, "", "");
			} else {
				data = misdbBASEService.getContractAndLoanNo(custId, dupNo);
			}

			/*
			 * J-113-0159 已審核未動用額度可於貸後新增，但未動用額度不得呈主管覆核 
			 * 
			 * 增加引入已審核但未動用的額度資料
			 */
			data.addAll(misELF447nService.getByIdAndStatus(brNo, custId, dupNo));

			for (Map<String, Object> map : data) {
				Map<String, Object> row = new HashMap<String, Object>();
				String cntrNo = Util.trim(MapUtils.getString(map, "CONTRACT"));
				String loanNo = Util.trim(MapUtils.getString(map, "LOAN_NO"));
				if (Util.equals("2", newType)
						&& Util.isEmpty(Util.trim(loanNo))) {
					continue;
				}
				if (cntrNo.length() < 3) {
					continue;
				}
				// 2021/10/01 金至忠襄理說不得跨分行 所以非該行額度序號不顯示
				if (Util.notEquals(cntrNo.substring(0, 3), user.getUnitNo())) {
					continue;
				}
				row.put("cntrNo", cntrNo);
				if (Util.equals("2", newType)) {
					row.put("loanNo", loanNo);
				} else {
					row.put("loanNo", "");
				}
				list.add(row);
			}

			// 排除重複
			Set<Map> setMap = new HashSet<Map>();
			for (Map<String, Object> chkMap : list) {
				if (setMap.add(chkMap)) {
					newList.add(chkMap);
				}
			}

			// 排序
			Collections.sort(newList, new Comparator<Map<String, Object>>() {
				public int compare(Map<String, Object> o1,
						Map<String, Object> o2) {
					String cntrNo1 = o1.get("cntrNo").toString();
					String cntrNo2 = o2.get("cntrNo").toString();
					int c = cntrNo1.compareTo(cntrNo2);
					if (c != 0) {
						return c;
					}

					String loanNo1 = o1.get("loanNo").toString();
					String loanNo2 = o2.get("loanNo").toString();

					return loanNo1.compareTo(loanNo2);
				}
			});
		}

		Page<Map<String, Object>> pages = LMSUtil.setPageMap(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
		// return new CapMapGridResult(newList, newList.size());
	}

	@SuppressWarnings({ "unchecked", "serial" })
	public CapGridResult queryList(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));
		String fieldId = params.getString("fieldId");
		String mainDocStatus = params.getString("mainDocStatus");

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		if (Util.notEquals(mainDocStatus, CreditDocStatusEnum.海外_呈總行)) {
			pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
					"deletedTime", null);
		}
		pageSetting.setMaxResults(Integer.MAX_VALUE);

		Page<? extends GenericBean> page = null;
		CapGridResult result = new CapGridResult();

		if (Util.equals(fieldId, "l260m01c")) { // 控制檔
			if (Util.notEquals(mainDocStatus, CreditDocStatusEnum.海外_呈總行)) {
				pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
						"status", "C");
			}
			page = lms8000Service.findPage(L260M01C.class, pageSetting);
			Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

			formatter.put("statusForShow", new CodeTypeFormatter(
					codeTypeService, "postLoan_statusForShow",
					CodeTypeFormatter.ShowTypeEnum.Desc));

			formatter.put("staff", new CodeTypeFormatter(codeTypeService,
					"postLoan_staff", CodeTypeFormatter.ShowTypeEnum.Desc));

			formatter.put("checkYN", new IFormatter() {
				@Override
				public String reformat(Object in) throws CapFormatException {
					String txt = (String) in;
					if (Util.equals("Y", txt)) {
						return "O";
					} else if (Util.equals("N", txt)) {
						return "X";
					} else {
						return "";
					}
				}
			});

			// for 畫面Grid顯示用
			formatter.put("byId", new IBeanFormatter() {
				@Override
				public String reformat(Object in) throws CapFormatException {
					String byId = "";
					L260M01C meta = (L260M01C) in;
					if (Util.isEmpty(Util.trim(meta.getCntrNo()))
							&& Util.isEmpty(Util.trim(meta.getLoanNo()))) {
						byId = "V";
					}
					return byId;
				}
			});

			// for 傳入參數用 - 同畫面選擇值 Y/N
			formatter.put("byIdFlag", new IBeanFormatter() {
				@Override
				public String reformat(Object in) throws CapFormatException {
					String byIdFlag = "N";
					L260M01C meta = (L260M01C) in;
					if (Util.isEmpty(Util.trim(meta.getCntrNo()))
							&& Util.isEmpty(Util.trim(meta.getLoanNo()))) {
						byIdFlag = "Y";
					}
					return byIdFlag;
				}
			});

			result = new CapGridResult(page.getContent(), page.getTotalRow(),
					formatter);
		} else if (Util.equals(fieldId, "l260m01d")) { // 紀錄檔
			if (Util.notEquals(mainDocStatus, CreditDocStatusEnum.海外_呈總行)) {
				pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
						"handlingStatus", "4");
			}
			page = lms8000Service.findPage(L260M01D.class, pageSetting);
			Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

			formatter.put("handlingStatus", new CodeTypeFormatter(
					codeTypeService, "postLoan_handlingStatus",
					CodeTypeFormatter.ShowTypeEnum.Desc));

			formatter.put("followStaff", new CodeTypeFormatter(codeTypeService,
					"postLoan_staff", CodeTypeFormatter.ShowTypeEnum.Desc));

			formatter.put("checkYN", new IFormatter() {
				@Override
				public String reformat(Object in) throws CapFormatException {
					String txt = (String) in;
					if (Util.equals("Y", txt)) {
						return "O";
					} else if (Util.equals("N", txt)) {
						return "X";
					} else {
						return "";
					}
				}
			});

			// for 畫面Grid顯示用
			formatter.put("byId", new IBeanFormatter() {
				@Override
				public String reformat(Object in) throws CapFormatException {
					String byId = "";
					L260M01D meta = (L260M01D) in;
					if (Util.isEmpty(Util.trim(meta.getCntrNo()))
							&& Util.isEmpty(Util.trim(meta.getLoanNo()))) {
						byId = "V";
					}
					return byId;
				}
			});

			// for 傳入參數用 - 同畫面選擇值 Y/N
			formatter.put("byIdFlag", new IBeanFormatter() {
				@Override
				public String reformat(Object in) throws CapFormatException {
					String byIdFlag = "N";
					L260M01D meta = (L260M01D) in;
					if (Util.isEmpty(Util.trim(meta.getCntrNo()))
							&& Util.isEmpty(Util.trim(meta.getLoanNo()))) {
						byIdFlag = "Y";
					}
					return byIdFlag;
				}
			});

			result = new CapGridResult(page.getContent(), page.getTotalRow(),
					formatter);
		}

		return result;

		// return new CapGridResult(l260m01aList, page.getTotalRow());
	}

	public CapGridResult queryL260M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		String searchType = Util.nullToSpace(params.getString("searchType"));

		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				UtilConstants.Field.目前編製行, user.getUnitNo());

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = null;
		if (Util.equals(searchType, "filter")) {
			String custId = Util.trim(Util.nullToSpace(params
					.getString("custId")));
			List<String> custIds = new ArrayList<String>();
			if(custId.contains(",")){
				custIds = Arrays.asList(custId.split(","));
			}else{
				custIds.add(custId);
			}
			String cntrNo = Util.trim(Util.nullToSpace(params
					.getString("cntrNo")));
			String loanNo = Util.trim(Util.nullToSpace(params
					.getString("loanNo")));
			if (!Util.isEmpty(custId)) {
				pageSetting.addSearchModeParameters(SearchMode.IN,
						"custId", custIds);
			}
			if (!Util.isEmpty(cntrNo)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"cntrNo", cntrNo);
			}
			if (!Util.isEmpty(loanNo)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"loanNo", loanNo);
			}
			String followUpType = Util.trim(Util.nullToSpace(params
					.getString("followUpType")));
			if(!Util.isEmpty(followUpType)) {
				page = lms8000Service.findPageByFilter(params);
			}
		}
		if(page == null)
			page = lms8000Service.findPage(L260M01A.class, pageSetting);

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		return result;
	}

	public CapGridResult queryFile(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		// String mainId = Util.nullToSpace(params.getString("mainId"));
		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));
		String fieldId = params.getString("fieldId");
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
		// mainId);
		// 因為上傳時是用oid放在 LMS.BDocFile 的 mainId

		// //J-111-0025_05097_B1001 為增進eloan擔保品及貸後管理查詢時價登入作業效率,增加相關作業需求
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
		// oid);
		pageSetting.addSearchModeParameters(SearchMode.OR,
				new SearchModeParameter(SearchMode.EQUALS, "mainId", oid),
				new SearchModeParameter(SearchMode.EQUALS, "pid", oid));

		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		Page<DocFile> page = docFileService.readToGrid(pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	public CapMapGridResult queryPrintList(ISearch pageSetting,
			PageParameters params) throws CapException {
		String oid = Util.trim(params.getString(EloanConstants.OID));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		pageSetting.setDistinct(true);
		Page<Map<String, Object>> page = lms8000Service.getPrintList(oid,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	public CapMapGridResult queryMisList(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brNo).getBrNoFlag());
		List<Map<String, Object>> list = null;
		if (isOverSea) {
			list = obsdbELF601Service.queryElf602List(pageSetting, brNo);
		} else {
						String fo_staffNo = Util.trim(params.getString("fo_staffNo"));
			String ao_staffNo = Util.trim(params.getString("ao_staffNo"));
			list = misdbBASEService.queryElf602List(pageSetting, brNo,
					fo_staffNo, ao_staffNo);
		}
		Map<String, Object> data = null;
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, String> codeMap = codeTypeService
				.findByCodeType("postLoan_followKind");
		if (list != null && list.size() > 0) {
			for (Map<String, Object> map : list) {
				String seq = Util.trim(MapUtils.getString(map, "SEQ"));
				String fullCustId = "";
				String custId = "";
				String dupNo = "";
				// J-111-0238 「查詢未完成案件」名單，新增「最早檢核日期」欄位，以利營業單位掌握辦理進度
				BigDecimal fo_date = null;
				String foDate = "";
				if (isOverSea) {
				custId = Util.trim(MapUtils.getString(map, "CUSTID"));
					dupNo = Util.trim(MapUtils.getString(map, "DUPNO"));
					fullCustId = Util.addSpaceWithValue(custId, 10) + dupNo;	
					fo_date = (BigDecimal) MapUtils.getObject(map, "FOLLOWDATE");
					foDate = (fo_date.compareTo(BigDecimal.ZERO) == 0 ? null
							: CapDate.formatyyyyMMddToDateFormat(
									fo_date.toString(), "yyyy-MM-dd"));
				} else {
					fullCustId = Util.trim(MapUtils.getString(map,
							"CUSTID"));
					custId = Util.trim(Util.getLeftStr(Util.trim(fullCustId),
							10));
					dupNo = Util.getRightStr(Util.trim(fullCustId), 1);
					foDate = Util.trim(MapUtils.getString(map, "FOLLOWDATE"));
				}
				String cntrNo = Util.trim(MapUtils.getString(map,
						"CNTRNO"));
				String loanNo = Util.trim(MapUtils.getString(map,
						"LOANNO"));
				String followKind = Util.trim(MapUtils.getString(map,
						"FOLLOWKIND"));
				String newCustName = Util
						.trim(MapUtils.getString(map, "CNAME"));
				// Map<String, Object> latestData =
				// customerSrv.findByIdDupNo(custId, dupNo);
				// String newCustName = Util.trim(MapUtils.getString(latestData,
				// "CNAME"));
				String custName = Util.toSemiCharString(newCustName); // 轉半形

				data = new HashMap<String, Object>();
				data.put("seq", seq);
				data.put("fullCustId", fullCustId);
				data.put("custId", custId);
				data.put("dupNo", dupNo);
				data.put("cntrNo", cntrNo);
				data.put("loanNo", loanNo);
				data.put("custName", custName);
				data.put("followKind", Util.nullToSpace(codeMap.get(followKind)));
				data.put("followDate", Util.nullToSpace(foDate));
				newList.add(data);
			}
		}

		// .js 的 sortorder 只限於find page使用pageSetting ; 獨立SQL撈出來的List沒用
		// sortname: "seq|custId|cntrNo|loanNo|followDate", sortorder:
		// "asc|asc|asc|asc|asc",
		// 排序
		// Collections.sort(newList, new Comparator<Map<String, Object>>() {
		// public int compare(Map<String, Object> o1, Map<String, Object> o2) {
		// String seq1 = o1.get("seq").toString();
		// String seq2 = o2.get("seq").toString();
		// int a = seq1.compareTo(seq2);
		// if (a != 0) {
		// return a;
		// }
		//
		// String custId1 = o1.get("custId").toString();
		// String custId2 = o2.get("custId").toString();
		// int b = custId1.compareTo(custId2);
		// if (b != 0) {
		// return b;
		// }
		//
		// String cntrNo1 = o1.get("cntrNo").toString();
		// String cntrNo2 = o2.get("cntrNo").toString();
		// int c = cntrNo1.compareTo(cntrNo2);
		// if (c != 0) {
		// return c;
		// }
		//
		// String loanNo1 = o1.get("loanNo").toString();
		// String loanNo2 = o2.get("loanNo").toString();
		// int d = loanNo1.compareTo(loanNo2);
		// if (d != 0) {
		// return d;
		// }
		//
		// String followDate1 = o1.get("followDate").toString();
		// String followDate2 = o2.get("followDate").toString();
		// return followDate1.compareTo(followDate2);
		// }
		// });

		Page<Map<String, Object>> pages = LMSUtil.setPageMap(newList,
				pageSetting);

		return new CapMapGridResult(pages.getContent(), newList.size());
		// return new CapMapGridResult(newList, newList.size());
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryUndoneElf602List(ISearch pageSetting,
			PageParameters params) {
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brNo).getBrNoFlag());
		if (params.containsKey("key")
				&& Util.isNotEmpty(Util.trim(params.getString("key", "")))) { // L260M01A
																				// 的
																				// oid
																				// 去抓該筆資料的CNTRNO和LOANNO
			L260M01A l260m01a = lms8000Service.findModelByOid(L260M01A.class,
					Util.trim(params.getString("key", "")));
			if (l260m01a != null) {
				HashSet<String> m01dUnid = new HashSet<String>();
				List<L260M01D> l260m01dlist = (List<L260M01D>) lms8000Service
						.findListByMainIdNotDel(L260M01D.class,
								l260m01a.getMainId(), true);
				if (l260m01dlist != null && l260m01dlist.size() > 0) {
					for (L260M01D m01d : l260m01dlist) {
						if (Util.equals(
								Util.nullToSpace(m01d.getHandlingStatus()), "4")) {
							continue;
						}
						String unid = Util.nullToSpace(m01d.getUnid());
						if (Util.notEquals(unid, "new")
								&& Util.isNotEmpty(unid)) {
							if (!m01dUnid.contains(unid)) {
								m01dUnid.add(unid);
							}
						}
					}
				}
				Map<String, CapAjaxFormResult> codeMap = codeTypeService
						.findByCodeType(new String[] {
								"postLoan_handlingStatus", "postLoan_staff" });
				String cntrNo = Util.nullToSpace(l260m01a.getCntrNo());
				String loanNo = Util.nullToSpace(l260m01a.getLoanNo());
				List<ELF602> elf602s = null;
				if (Util.isNotEmpty(cntrNo)) { // 避免ID階層資料重複
					if (isOverSea) {
						elf602s = obsdbELF601Service.getElf602ByCntrNoLoanNo(
								brNo, cntrNo, loanNo, true);
					} else {
						elf602s = misdbBASEService.getElf602ByCntrNoLoanNo(
								cntrNo, loanNo, true,
								Util.trim(l260m01a.getOwnBrId()));
					}
				}
				if (elf602s != null && elf602s.size() > 0) {
					for (ELF602 elf602 : elf602s) {
						if (m01dUnid.contains(Util.nullToSpace(elf602
								.getElf602_unid()))) {
							continue;
						}
						data = new HashMap<String, Object>();
						data.put("followContent",
								Util.nullToSpace(elf602.getElf602_fo_content()));
						data.put(
								"followStaff",
								(isOverSea ? "" : Util.nullToSpace(codeMap.get(
										"postLoan_staff").get(
										Util.nullToSpace(elf602
												.getElf602_staff())))));
						data.put("followDate", CapDate.formatDate(
								elf602.getElf602_fo_date(), "yyyy-MM-dd"));
						data.put("handlingStatus", Util.nullToSpace(codeMap
								.get("postLoan_handlingStatus").get(
										Util.nullToSpace(elf602
												.getElf602_status()))));
						data.put("unid",
								Util.nullToSpace(elf602.getElf602_unid()));
						newList.add(data);
					}
				}
				// J-110-0363 By ID
				List<Map<String, Object>> m01dList = lms8000Service
						.findDataById("L260M01D",
								Util.trim(l260m01a.getOwnBrId()),
								Util.trim(l260m01a.getCustId()),
								Util.trim(l260m01a.getDupNo()), new String[] {
										CreditDocStatusEnum.海外_編製中.getCode(),
										CreditDocStatusEnum.海外_待覆核.getCode() });// new
																				// ArrayList<Map<String,
																				// Object>>();
				if (m01dList != null && !m01dList.isEmpty()) {
					for (Map<String, Object> m01dMap : m01dList) {
						String m01Unid = Util.nullToSpace(MapUtils.getString(
								m01dMap, "UNID"));
						String m01Chk = Util.nullToSpace(MapUtils.getString(
								m01dMap, "CHECKYN"));
						if (Util.isEmpty(m01Chk) && !m01dUnid.contains(m01Unid)) {
							m01dUnid.add(m01Unid);
						}
					}
				}
				List<ELF602> elf602sId = null;
				if (isOverSea) {
					elf602sId = obsdbELF601Service.getElf602OnlyById(
							Util.trim(l260m01a.getCustId()),
							Util.trim(l260m01a.getDupNo()), true,
							Util.trim(l260m01a.getOwnBrId()));
				}
				else{
					elf602sId = misdbBASEService.getElf602OnlyById(
							Util.trim(l260m01a.getCustId()),
							Util.trim(l260m01a.getDupNo()), true,
							Util.trim(l260m01a.getOwnBrId()));
				}
				if (elf602sId != null && elf602sId.size() > 0) {
					for (ELF602 elf602Id : elf602sId) {
						if (m01dUnid.contains(Util.nullToSpace(elf602Id
								.getElf602_unid()))) {
							continue;
						}
						data = new HashMap<String, Object>();
						data.put("followContent", Util.nullToSpace(elf602Id
								.getElf602_fo_content()));
						data.put("followStaff", Util.nullToSpace(codeMap.get(
								"postLoan_staff").get(
								Util.nullToSpace(elf602Id.getElf602_staff()))));
						data.put("followDate", CapDate.formatDate(
								elf602Id.getElf602_fo_date(), "yyyy-MM-dd"));
						data.put("handlingStatus", Util.nullToSpace(codeMap
								.get("postLoan_handlingStatus").get(
										Util.nullToSpace(elf602Id
												.getElf602_status()))));
						data.put("unid",
								Util.nullToSpace(elf602Id.getElf602_unid()));
						newList.add(data);
					}
				}
			}
		}

		Page<Map<String, Object>> pages = LMSUtil.setPageMap(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryL260s01aList(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		String oid = Util.trim(params.getString(EloanConstants.OID));
		Map<String, String> codeMap = codeTypeService
				.findByCodeType("postLoan_proType");
		// 列表不顯示 刪除案件
		List<L260S01A> l260s01aList = (List<L260S01A>) lms8000Service
				.findListByMainIdNotDel(L260S01A.class, oid, true);
		if (l260s01aList != null && l260s01aList.size() > 0) {
			for (L260S01A l260s01a : l260s01aList) {
				data = new HashMap<String, Object>();
				data.put("oid", Util.nullToSpace(l260s01a.getOid())); // L260S01A
																		// key
				data.put("proType", Util.nullToSpace(l260s01a.getProType())); // 理財key
				data.put("proTypeStr",
						codeMap.get(Util.nullToSpace(l260s01a.getProType())));
				data.put("accNo", Util.nullToSpace(l260s01a.getAccNo())); // 理財key
				data.put("bankProCode",
						Util.nullToSpace(l260s01a.getBankProCode())); // 理財key
				data.put("bankPro", Util.nullToSpace(l260s01a.getBankProCode())
						+ " " + Util.nullToSpace(l260s01a.getBankProName()));
				data.put("lstBuy",
						Util.nullToSpace(TWNDate.toAD(l260s01a.getLstBuyDt())));
				data.put("lstSell",
						Util.nullToSpace(TWNDate.toAD(l260s01a.getLstSellDt())));
				newList.add(data);
			}
		}

		Page<Map<String, Object>> pages = LMSUtil.setPageMap(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryDwFinProdList(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;

		String custId = Util.trim(params.getString("custId", ""));
		String dupNo = Util.trim(params.getString("dupNo", ""));
		// 2021/02/04 授審張敏麒科長與金至忠襄理確認 只顯示追蹤日後的理財資料
		String bgnDate = Util.trim(params.getString("bgnDate"));

		HashSet<String> s01aKey = new HashSet<String>();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID)); // L260M01D
																				// 的
																				// oid
		if (Util.isNotEmpty(mainId)) {
			List<L260S01A> oldL260s01aList = (List<L260S01A>) lms8000Service
					.findListByMainIdNotDel(L260S01A.class, mainId, true);
			if (!Util.isEmpty(oldL260s01aList)) {
				for (L260S01A oldL260s01a : oldL260s01aList) {
					String key = Util.addSpaceWithValue(
							Util.nullToSpace(oldL260s01a.getCustId()), 10)
							+ Util.addSpaceWithValue(
									Util.nullToSpace(oldL260s01a.getDupNo()), 1)
							+ Util.addSpaceWithValue(
									Util.nullToSpace(oldL260s01a.getProType()),
									6)
							+ Util.addSpaceWithValue(
									Util.nullToSpace(oldL260s01a.getAccNo()),
									32)
							+ Util.addSpaceWithValue(Util
									.nullToSpace(oldL260s01a.getBankProCode()),
									20);
					if (!s01aKey.contains(key)) {
						s01aKey.add(key);
					}
				}
			}
		}

		if (Util.isNotEmpty(custId) && Util.isNotEmpty(dupNo)) {
			List<Map<String, Object>> dwList = lms8000Service
					.findOTS_DW_LNWM_MNT_ById(custId, dupNo, bgnDate);
			if (dwList != null && dwList.size() > 0) {
				Map<String, String> codeMap = codeTypeService
						.findByCodeType("postLoan_proType");
				for (Map<String, Object> dwMap : dwList) {
					String dwCustId = Util.nullToSpace(dwMap.get("CUST_ID"))
							.toUpperCase();
					String dwCustDupNo = Util.nullToSpace(
							dwMap.get("CUST_DUP_NO")).toUpperCase();
					String dwProType = Util.nullToSpace(dwMap.get("PRO_TYPE"))
							.toUpperCase();
					String dwAccNo = Util.nullToSpace(dwMap.get("ACC_NO"))
							.toUpperCase();
					String dwBankProCode = Util.nullToSpace(
							dwMap.get("BANK_PRO_CODE")).toUpperCase();
					String dwKey = Util.addSpaceWithValue(dwCustId, 10)
							+ Util.addSpaceWithValue(dwCustDupNo, 1)
							+ Util.addSpaceWithValue(dwProType, 6)
							+ Util.addSpaceWithValue(dwAccNo, 32)
							+ Util.addSpaceWithValue(dwBankProCode, 20);
					data = new HashMap<String, Object>();
					if (s01aKey.contains(dwKey)) {
						// continue;
						data.put("checked", true);
					} else {
						data.put("checked", false);
					}
					data.put("key", dwKey); // 理財key組成的字串
					data.put("custId", Util.nullToSpace(dwMap.get("CUST_ID"))); // 理財key
					data.put("custDupNo",
							Util.nullToSpace(dwMap.get("CUST_DUP_NO"))); // 理財key
					data.put("proType", Util.nullToSpace(dwMap.get("PRO_TYPE"))); // 理財key
					data.put("proTypeStr", codeMap.get(Util.nullToSpace(dwMap
							.get("PRO_TYPE"))));
					data.put("accNo", Util.nullToSpace(Util.nullToSpace(dwMap
							.get("ACC_NO")))); // 理財key
					data.put("bankProCode", Util.nullToSpace(Util
							.nullToSpace(dwMap.get("BANK_PRO_CODE")))); // 理財key
					data.put(
							"bankPro",
							Util.nullToSpace(Util.nullToSpace(dwMap
									.get("BANK_PRO_CODE")))
									+ " "
									+ Util.nullToSpace(dwMap
											.get("BANK_PRO_NAME")));
					data.put("lstBuy",
							Util.nullToSpace(dwMap.get("LST_BUY_DT")));
					data.put("lstSell",
							Util.nullToSpace(dwMap.get("LST_SELL_DT")));
					newList.add(data);
				}
			}
		}

		Page<Map<String, Object>> pages = LMSUtil.setPageMap(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult query0320List(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;

		Boolean init = params.getAsBoolean("init", false);
		String custId = Util.trim(params.getString("custId", ""));
		String dupNo = Util.trim(params.getString("dupNo", "0"));

		if (init) { // 第一次load畫面先不查
			return new CapMapGridResult(newList, newList.size());
		}

		String[] chkArr = lms8000Service.getODS_Status();
		String chkStatus = chkArr[0];
		String chkMsg = chkArr[1];
		if (Util.notEquals(chkStatus, "Y")) { // ODS 不能用也不撈
			throw new CapMessageException(pop.getProperty("odsMsg" + chkMsg,
					UtilConstants.AJAX_RSP_MSG.執行有誤)
					+ "，"
					+ pop.getProperty("odsMsgToBtt"), getClass());
			// return new CapMapGridResult(newList, newList.size());
		}

		if (Util.isNotEmpty(custId) && Util.isNotEmpty(dupNo)) {
			List<Map<String, Object>> odsList = lms8000Service
					.findODS_0320_ById(custId, dupNo);
			if (odsList != null && odsList.size() > 0) {
				for (Map<String, Object> odsMap : odsList) {
					String odsCustId = Util.nullToSpace(
							odsMap.get("TCIA_CUST_ID_NO")).toUpperCase();
					String odsCustDupNo = Util.nullToSpace(
							odsMap.get("TCIA_CUST_DUP_NO")).toUpperCase();
					String odsFakeActNo = Util.nullToSpace(
							odsMap.get("TP44_ACT_NO")).toUpperCase();
					String odsCurr = Util.nullToSpace(odsMap.get("TP44_CURR"))
							.toUpperCase();
					String odsCurrCode = Util.nullToSpace(
							odsMap.get("CURR_CODE")).toUpperCase();
					String odsRealActNo = Util.nullToSpace(
							odsMap.get("REAL_ACT_NO")).toUpperCase();
					String ods0060Flag = Util.nullToSpace(
							odsMap.get("Q0060FLAG")).toUpperCase();
					data = new HashMap<String, Object>();
					data.put("custId", odsCustId);
					data.put("dupNo", odsCustDupNo);
					data.put("fakeActNo", odsFakeActNo);
					data.put("curr", odsCurr);
					data.put("currCode", odsCurrCode);
					data.put("realActNo", odsRealActNo);
					data.put("q0060Flag", ods0060Flag);
					data.put("q0060Btn", ods0060Flag); // 為了呈現 0060按鈕
					newList.add(data);
				}
			}
		}

		Page<Map<String, Object>> pages = LMSUtil.getMapGirdDataRow(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryElandList(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;

		Boolean init = params.getAsBoolean("init", false);
		String oidL260M01D = Util.trim(params.getString("dataOid", ""));

		if (init) { // 第一次load畫面先不查
			return new CapMapGridResult(newList, newList.size());
		}

		if(Util.isNotEmpty(oidL260M01D)){
			L260M01D l260m01d = lms8000Service.findModelByOid(L260M01D.class, oidL260M01D);
			if (l260m01d != null) {
				String cntrNo = Util.nullToSpace(l260m01d.getCntrNo());
				String datasrc = Util.trim(l260m01d.getDataSrc());
				Date followDate = l260m01d.getFollowDate() == null ? new Date() : l260m01d.getFollowDate();
				// 金襄理說  一個月前到半年內 依據申請日期由小到大
				String bgnDate = TWNDate.toAD(CapDate.addMonth(followDate, -1));
				String endDate = TWNDate.toAD(CapDate.addMonth(followDate, 6));
				String cntrNoBr = cntrNo.substring(0, 3);
				if (lms8000Service.isCaseMark(l260m01d, "01")) {
					List<Map<String, Object>> cmsC101m01DataList = lms8000Service.findC101m01List(cntrNoBr, cntrNo, datasrc, bgnDate, endDate);
					if (cmsC101m01DataList != null && cmsC101m01DataList.size() > 0) {
						for (Map<String, Object> cmsMap : cmsC101m01DataList) {
							String c101m01MainId = Util.nullToSpace(MapUtils.getString(cmsMap, "MAINID"));
							String applyNo = Util.nullToSpace(MapUtils.getString(cmsMap, "APPLYNO"));
							String target = Util.nullToSpace(MapUtils.getString(cmsMap, "TARGET"));
							String status = Util.nullToSpace(MapUtils.getString(cmsMap, "STATUS"));
							String applyDtStr = Util.nullToSpace(MapUtils.getString(cmsMap, "APPLYDT"));
//                                applyDt = (Util.isEmpty(applyDtStr) ? null : CapDate.getDate(applyDtStr, "yyyy-MM-dd"));
							String elandDtStr = Util.nullToSpace(MapUtils.getString(cmsMap, "ELANDDT"));
//                                elandDt = (Util.isEmpty(elandDtStr) ? null : CapDate.getDate(elandDtStr, "yyyy-MM-dd"));
							String isOk = Util.nullToSpace(MapUtils.getString(cmsMap, "ISOK"));
//                                String reason = Util.nullToSpace(MapUtils.getString(cmsMap, "REASON"));
//                                reason = reason.replace("<", "(").replace(">", ")");
							if(Util.equals(isOk, "Y")) {
								data = new HashMap<String, Object>();
								data.put("c101m01MainId", c101m01MainId);
								data.put("applyNo", applyNo);
								data.put("target", target);
								data.put("applyDt", applyDtStr);
								data.put("elandDt", elandDtStr);
								// data.put("isOk", isOk);
								data.put("qElandBtn", isOk); // 為了呈現按鈕
								newList.add(data);
							}
						}
					}
				}
			}
		}

		Page<Map<String, Object>> pages = LMSUtil.getMapGirdDataRow(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryElandDetail(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;

		Boolean init = params.getAsBoolean("init", false);
		String c101m01MainId = Util.trim(params.getString("c101m01MainId", ""));

		if (init) { // 第一次load畫面先不查
			return new CapMapGridResult(newList, newList.size());
		}

		if (Util.isNotEmpty(c101m01MainId)) {
			List<Map<String, Object>> cmsC100s02aDataList = lms8000Service.findC100s02aList(c101m01MainId);
			if (cmsC100s02aDataList != null && cmsC100s02aDataList.size() > 0) {
				for (Map<String, Object> cmsMap : cmsC100s02aDataList) {
					String c100s02aOid = Util.nullToSpace(MapUtils.getString(cmsMap, "OID"));
					String fileCode = Util.nullToSpace(MapUtils.getString(cmsMap, "FILECODE"));
					String fileDesc = Util.nullToSpace(MapUtils.getString(cmsMap, "FILEDESC"));
					String filePath = Util.nullToSpace(MapUtils.getString(cmsMap, "FILEPATH"));

					if(Util.equals(fileCode, "A0201")){	// 土地登記謄本
						if(fileDesc.length() >= 8){
							fileDesc = fileDesc.substring(0, 4) + "-" + fileDesc.substring(4);
						}
					} else if(Util.equals(fileCode, "A0202")){    // 建物登記謄本
						if(fileDesc.length() >= 8){
							fileDesc = fileDesc.substring(0, 5) + "-" + fileDesc.substring(5);
						}
					}

					if (Util.equals(fileCode, "A0201") || Util.equals(fileCode, "A0203") || Util.equals(fileCode, "A0205")) {
						fileDesc = "地號" + fileDesc;
					} else if (Util.equals(fileCode, "A0202") || Util.equals(fileCode, "A0204")) {
						fileDesc = "建號" + fileDesc;
					}

					data = new HashMap<String, Object>();
					data.put("c100s02aOid", c100s02aOid);
					data.put("fileDesc", fileDesc);
//					data.put("filePath", filePath);
					data.put("oElandBtn", Util.isNotEmpty(filePath) ? "Y" : "");
					data.put("pathFlag", Util.isNotEmpty(filePath) ? "Y" : "");
//					data.put("pathFlag", Util.isNotEmpty(filePath) ? (Util.equals(c100s02aOid, "79898eeda0594a63896117a9339c20f0") ? "N" : "Y") : "");
					newList.add(data);
				}
			}
		}

		Page<Map<String, Object>> pages = LMSUtil.getMapGirdDataRow(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryL260s01cList(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		String oidL260M01D = Util.trim(params.getString(EloanConstants.OID));

		List<L260S01C> l260s01cList = (List<L260S01C>) lms8000Service
				.findListByMainIdNotDel(L260S01C.class, oidL260M01D, true);
		if (l260s01cList != null && l260s01cList.size() > 0) {
			for (L260S01C l260s01c : l260s01cList) {
				data = new HashMap<String, Object>();
				data.put("oid", Util.nullToSpace(l260s01c.getOid()));
				data.put("branch", Util.nullToSpace(l260s01c.getBranch()));
				data.put("custId", Util.nullToSpace(l260s01c.getCustId()));
				data.put("custName", Util.nullToSpace(l260s01c.getCustName()));
				data.put("collNo", Util.nullToSpace(l260s01c.getCollNo()));
				data.put("checkYN", Util.nullToSpace(l260s01c.getCheckYN()));
				// data.put("queryDtStr", TWNDate.toAD(l260s01c.getQueryRaspDate()));
				newList.add(data);
			}
		}

		Page<Map<String, Object>> pages = LMSUtil.setPageMap(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryRaspList(ISearch pageSetting,
			PageParameters params) throws CapException {
		DecimalFormat df = new DecimalFormat("###,###,###,###,###,##0");
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;

		Boolean init = params.getAsBoolean("init", false);
		String oidL260M01D = Util.trim(params.getString("dataOid", ""));

		if (init) { // 第一次load畫面先不查
			return new CapMapGridResult(newList, newList.size());
		}

		if(Util.isNotEmpty(oidL260M01D)){
			L260M01D l260m01d = lms8000Service.findModelByOid(L260M01D.class, oidL260M01D);
			if (l260m01d != null) {
				String cntrNo = Util.nullToSpace(l260m01d.getCntrNo());
				String datasrc = Util.trim(l260m01d.getDataSrc());
				Date followDate = l260m01d.getFollowDate() == null ? new Date() : l260m01d.getFollowDate();
				// 一個月前到一個月內 依據申請日期由大到小
				String bgnDate = TWNDate.toAD(CapDate.addMonth(followDate, -1));
				String endDate = TWNDate.toAD(CapDate.addMonth(followDate, 1));
				String cntrNoBr = cntrNo.substring(0, 3);
				if (lms8000Service.isCaseMark(l260m01d, "02")) {
					List<Map<String, Object>> cmsC101m29DataList = lms8000Service.findC101m29List(cntrNoBr, cntrNo, datasrc, bgnDate, endDate);
					if (cmsC101m29DataList != null && cmsC101m29DataList.size() > 0) {
						for (Map<String, Object> cmsMap : cmsC101m29DataList) {
							String c101m29Oid = Util.nullToSpace(MapUtils.getString(cmsMap, "OID"));
							String c101m29MainId = Util.nullToSpace(MapUtils.getString(cmsMap, "MAINID"));
                            String branch =  Util.nullToSpace(MapUtils.getString(cmsMap, "BRANCH"));
                            String custId =  Util.nullToSpace(MapUtils.getString(cmsMap, "CUSTID"));
							String custName = Util.nullToSpace(MapUtils.getString(cmsMap, "CUSTNAME"));
							String collNo = Util.nullToSpace(MapUtils.getString(cmsMap, "COLLNO"));
							String contractPrice = Util.nullToSpace(MapUtils.getString(cmsMap, "CONTRACTPRICE"));
							String qRASPTimeStr = Util.nullToSpace(MapUtils.getString(cmsMap, "CREATETIME"));
							Date queryRASPDate = CapDate.getDate(qRASPTimeStr, "yyyy-MM-dd");
							String recvCode = Util.nullToSpace(MapUtils.getString(cmsMap, "RECVCODE"));
							String filePath = Util.nullToSpace(MapUtils.getString(cmsMap, "RECVFILE_URL"));
							if (Util.equals(recvCode, "00")
									|| Util.equals(recvCode, "A4")) {
								// J-112-0341
								// 貸後管理追蹤系統需判別由本行估價系統回傳本行實價登入資料庫查詢結果資料是否有查得資料,
								// 有查得者,始將其產生附件崁入貸後管理追蹤表中, 否則提示查無資料即可
								// RECVCODE：A4 查無資料，實價登錄清單仍需呈現
								data = new HashMap<String, Object>();
								data.put("c101m29Oid", c101m29Oid);
								data.put("c101m29MainId", c101m29MainId);
								data.put("branch", branch);
								data.put("custId", custId);
								data.put("custName", custName);
								data.put("collNo", collNo);
								data.put("contractPriceStr", contractPrice);
                                data.put("contractPrice", df.format(Util.parseBigDecimal(contractPrice)));
								data.put("queryDtStr", TWNDate.toAD(queryRASPDate));
								data.put("recvCode", recvCode);
								data.put("qRaspBtn", Util.isNotEmpty(filePath) ? "Y" : "");
								data.put("pathFlag", Util.isNotEmpty(filePath) ? "Y" : "");
								newList.add(data);
							}
						}
					}
				}
			}
		}

		Page<Map<String, Object>> pages = LMSUtil.getMapGirdDataRow(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
	}

	public CapMapGridResult queryRaspFileByFileOid(ISearch pageSetting, PageParameters params) throws CapException {
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		Boolean init = params.getAsBoolean("init", false);
		String oidL260S01C = Util.trim(params.getString("dataOid", ""));

		if (init) { // 第一次load畫面先不查
			return new CapMapGridResult(newList, newList.size());
		}

		if(Util.isNotEmpty(oidL260S01C)) {
			L260S01C l260s01c = lms8000Service.findModelByOid(L260S01C.class, oidL260S01C);
			if (l260s01c != null) {
				String raspFileOid = Util.nullToSpace(l260s01c.getRaspFileOid());
				DocFile docFile = docFileService.findByOidAndSysId(raspFileOid, "LMS");
				if (docFile != null) {
					data = new HashMap<String, Object>();
					data.put("srcFileName", docFile.getSrcFileName());
					data.put("fileDesc", docFile.getFileDesc());
					data.put("uploadTime", docFile.getUploadTime());
					data.put("oid", docFile.getOid());
					newList.add(data);
				}
			}
		}

		Page<Map<String, Object>> pages = LMSUtil.getMapGirdDataRow(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
	}
}
