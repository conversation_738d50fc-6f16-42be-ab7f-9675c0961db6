/* 
 * ClsParserL140S02B.java
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2013 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.cls.dc.action;

import org.apache.commons.lang.StringUtils;
import org.w3c.dom.Document;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.bean.L140S02BBean;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * ClsParserL140S02B:流用(長擔)額度序號檔
 * </pre>
 * 
 * @since 2013/3/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/29,Bang,new
 *          </ul>
 */
public class ClsParserL140S02B extends AbstractCLSCustParser {

	/**
	 * @param pid
	 * @param doViewName
	 * @param formGroup
	 */
	public ClsParserL140S02B(String pid, String doViewName, String formGroup) {
		super(pid, doViewName, formGroup);
	}

	/**
	 * 讀取,處理及轉換
	 * 
	 * @param dxlPath
	 *            String : .dxl檔存放路徑
	 * @param dxlName
	 *            :.dxl列表中的.dxl檔名
	 * @param strBrn
	 *            String:分行名稱
	 * @param domDoc
	 *            DOM Document:已轉為DOM Document的.dxl檔
	 */
	@SuppressWarnings("unused")
	protected void transferDXL(String dxlPath, String dxlName, String strBrn,
			Document domDoc, String dxlXml) {
		long t1 = System.currentTimeMillis();
		try {
			String linkKey = this.getItemValue(domDoc, "LinkID");
			String LinkMainId = "";
			String cntrNoValue = "";
			// 1個額度序號最多5組流用(長擔)額度序號，一個審核書最多產生15筆資料(Sno_4不會用到)
			int snoMaxLoop = 3, inMaxLoop = 4;
			for (int i = 1; i <= snoMaxLoop; i++) {
				// Ex:L_Sno_1
				String snoValue = this.getItemValue(domDoc, "Sno_" + i);
				if (StringUtils.isBlank(snoValue)) {
					return;
				} else {
					LinkMainId = linkKey.substring(0, 20) + snoValue;
				}
				if (this.formName
						.equalsIgnoreCase(TextDefine.CLS_L140S02B_FORM_109M01)) {
					cntrNoValue = getItemValue(domDoc, "L_Sno_" + i);
				} else {
					cntrNoValue = getItemValue(domDoc, "c_L_Sno_" + i);
				}
				L140S02BBean L140s = new L140S02BBean();
				L140s.setOid(Util.getOID());
				L140s.setMainId(LinkMainId);
				L140s.setSeq("1");
				L140s.setCntrNo(cntrNoValue);
				if (!cntrNoValue.equals(TextDefine.EMPTY_STRING)) {
					this.txtWrite.println(L140s.toString());
					this.parserTotal++;
				}
				for (int j = 1; j <= inMaxLoop; j++) {
					// Ex:L_Sno_1_1/L_Sno_1_2/L_Sno_1_3/L_Sno_1_4
					L140S02BBean L140si = new L140S02BBean();
					L140si.setOid(Util.getOID());
					L140si.setMainId(LinkMainId);
					L140si.setSeq("1");
					// 流用(長擔)額度序號
					if (this.formName
							.equalsIgnoreCase(TextDefine.CLS_L140S02B_FORM_109M01)) {
						cntrNoValue = getItemValue(domDoc, "L_Sno_" + i + "_"
								+ j);
					} else {
						cntrNoValue = getItemValue(domDoc, "c_L_Sno_" + i + "_"
								+ j);
					}
					L140si.setCntrNo(cntrNoValue);
					if (!cntrNoValue.trim().equals(TextDefine.EMPTY_STRING)) {
						this.txtWrite.println(L140si.toString());
						this.parserTotal++;
					}
				} // end of for (int j = 1; j <= inMaxLoop; j++)
			}// end of for (int i = 0; i < snoMaxLoop; i++)
		} catch (Exception e) {
			String errmsg = "【" + strBrn
					+ "】分行執行ParserL140S02B 之transferDXL時產生錯誤,dxl檔名:" + dxlName
					+ ",dxlPath=" + dxlPath;
			throw new DCException(errmsg, e);
		} finally {
			if (DEBUG && logger.isDebugEnabled()) {
				logger.debug("@@@@@@@@ TOTAL_COST="
						+ (System.currentTimeMillis() - t1));
			}
		}
	}

}
