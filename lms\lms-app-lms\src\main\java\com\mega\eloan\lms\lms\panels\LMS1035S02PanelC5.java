package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.LMSUtil;

/**
 * <pre>
 * 相關查詢資料
 * ⇒ 原始參照 LMS1115S02PanelC5
 * </pre>
 * 
 * @since 2017/2/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/2/1,EL08034,new
 *          </ul>
 */
public class LMS1035S02PanelC5 extends Panel {
	private static final long serialVersionUID = 1L;

	private String branch;

	public LMS1035S02PanelC5(String id) {
		super(id);
	}

	public LMS1035S02PanelC5(String id, boolean updatePanelName, String branch) {
		super(id, updatePanelName);
		this.branch = branch;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		boolean showNCB = true;
		if (LMSUtil.get_VN_BRNO_SET().contains(branch)) {
			showNCB = false;
		}
		model.addAttribute("showNCB", showNCB);
	}
	
	@Override
	protected String getViewName() {
		return getEloanPagePathByClass(getClass());
	}

}
