initDfd.done(function(json){

    var wrapFormId = "#wrapForm";
    var wrapForm = "<form id='wrapForm'></form>";
    
    $("#deleteColl").click(function(){
        var selrow = grid.getGridParam('selrow');
        if (selrow) {
            var ret = grid.getRowData(selrow);
            API.flowConfirmAction({
                message: i18n.def["action_003"],
                handler: "lms1925m01formhandler",
                action: "deleteL192S02A",
                data: {
                    deleteMainOid: ret.oid,
                    deleteMainId: ret.mainId
                },
                success: function(){
                    CommonAPI.showErrorMessage(i18n.def["confirmDeleteSuccess"]);
                    grid.trigger("reloadGrid");
                    setRequiredSave(true);
                    setCloseConfirm(true)
                }
            });
        }
        else {
            CommonAPI.showErrorMessage(i18n.def["action_002"]);
        }
    });
    
    $("#addNewColl").click(function(){
    
    
        $("#col").thickbox({
            title: i18n.lms1925m01['lms1925s04.008'],//'新增擔保品',
            width: 640,
            height: 500,
            align: 'left',
            valign: 'top',
            modal: false,
            open: function(){
                $("#collateral").wrap(wrapForm);
                $(wrapFormId).reset();
            },
            close: function(){
                $("#collateral").unwrap(wrapForm);
            },
            buttons: API.createJSON([{
                key: i18n.def['sure'],
                value: function(){
                    if ($(wrapFormId).valid()) {
                        $.ajax({
                            handler: "lms1925m01formhandler",
                            data: $.extend($("#collateral").serializeData(), {
                                formAction: "addL192S02A"
                            }),
                            success: function(responseData){
                                $.thickbox.close();
                                grid.trigger("reloadGrid");
                                setRequiredSave(true);
                                setCloseConfirm(true);
                            }
                        });
                    }
                }
            }, {
                key: i18n.def['cancel'],
                value: function(){
                    $.thickbox.close();
                }
            }])
        });
    });
    
    var grid = $("#result").iGrid({
        height: 380,
        width: "100%",
        autowidth: true,
        handler: "lms1925gridhandler",
        action: "queryL192S02A",
        rownumbers: true,
        colModel: [{
            colHeader: i18n.lms1925m01['lms1925s04.003'],//"擔保品名稱",
            name: "gteName",
            align: "center",
            formatter: 'click',
            onclick: json.editable == "N" ? openDocNoEdit : openDoc
        }, {
            colHeader: i18n.lms1925m01['lms1925s04.004'],//"鑑價日期",
            name: "estDate",
            align: "center"
        }, {
            colHeader: i18n.lms1925m01['lms1925s04.005'],//"幣別",
            name: "estCurr",
            align: "center"
        }, {
            colHeader: i18n.lms1925m01['lms1925s04.006'],//"估值",
            name: "estAmt",
            align: "center",
            formatter: 'currency',
            formatoptions: {
                decimalSeparator: ",",
                thousandsSeparator: ",",
                decimalPlaces: 0,
                defaultValue: ""
            }
        }, {
            colHeader: i18n.lms1925m01['lms1925s04.005'],//"幣別",
            name: "loanCurr",
            align: "center"
        }, {
            colHeader: i18n.lms1925m01['lms1925s04.007'],//"押值",
            name: "loanAmt",
            align: "center",
            formatter: 'currency',
            formatoptions: {
                decimalSeparator: ",",
                thousandsSeparator: ",",
                decimalPlaces: 0,
                defaultValue: ""
            }
        }, {
            name: "oid",
            hidden: "true"
        }, {
            name: "mainId",
            hidden: "true"
        }],
        ondblClickRow: function(rowid){
            var data = grid.getRowData(rowid);
            json.editable == "N" ? openDocNoEdit(null, null, data) : openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        $("#col").thickbox({
            title: i18n.lms1925m01['lms1925s04.025'],//'修改擔保品',
            width: 640,
            height: 500,
            align: 'left',
            valign: 'top',
            open: function(){			
                $("#collateral").wrap(wrapForm);				
                $(wrapFormId).reset();
                $.ajax({
                    handler: "lms1925m01formhandler",
                    data: {
                        l192s02oid: rowObject.oid,
                        formAction: "getL192S02A"
                    },
                    success: function(responseData){
                        $('#collateral').injectData(responseData);
                    }
                });
                //				$.form.init({
                //			        formHandler: "lms1925m01formhandler",
                //			        formAction: "getL192S02A",
                //			        formPostData:{
                //			        	l192s02oid:rowObject.oid			   
                //			        },
                //			        loadSuccess: function(json){			        		      
                //			        	$('#collateral').injectData(json);			        
                //			        }
                //			    });
            },
            close: function(){
                $("#collateral").unwrap(wrapForm);
            },
            buttons: API.createJSON([{
                key: i18n.def['sure'],
                value: function(){
                    if ($(wrapFormId).valid()) {
                        $.ajax({
                            handler: "lms1925m01formhandler",
                            data: $.extend($("#collateral").serializeData(), {
                                formAction: "updateL192S02A"
                            }),
                            success: function(responseData){
                                $.thickbox.close();
                                grid.trigger("reloadGrid");
                                setRequiredSave(true);
                                setCloseConfirm(true)
                            }
                        });
                    }
                }
            }, {
                key: i18n.def['cancel'],
                value: function(){
                    $.thickbox.close();
                }
            }])
        });
    }
    
    function openDocNoEdit(cellvalue, options, rowObject){
        $("#col").thickbox({
            title: i18n.lms1925m01['lms1925s04.026'],//'檢視擔保品',
            width: 640,
            height: 500,
            align: 'left',
            valign: 'top',
            open: function(){
                $("#collateral").wrap(wrapForm);
                $(wrapFormId).reset();
                $.ajax({
                    handler: "lms1925m01formhandler",
                    data: {
                        l192s02oid: rowObject.oid,
                        formAction: "getL192S02A"
                    },
                    success: function(responseData){
                        $('#collateral').injectData(responseData);
                    }
                });
            },
            close: function(){
                $("#collateral").unwrap(wrapForm);
            },
            buttons: API.createJSON([{
                key: i18n.def['sure'],
                value: function(){
                    $.thickbox.close();
                }
            }])
        });
    }
    
});
