package com.mega.eloan.lms.base.constants;





/**
 * <pre>
 * 日本消金模型
 * </pre>
 * 
 * @since 2015/06/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/06/17,EL08034,new
 *          </ul>
 */
public interface ScoreJP {

	static final String 設定檔_House_JP = "cls/scoreJP.properties";
	static final String 設定檔_House_JP_V2_0 = "cls/scoreJP_V2_0.properties";
	
	static final String 設定檔_notHouse_JP = "cls/scoreJP.properties";
	static final String 設定檔_notHouse_JP_V2_0 = "cls/scoreNotHouseLoanJP_V2_0.properties";
	

	static final String Comma = ",";
	static final String Point = ".";
	static final String Semicolon = ";";

	static final String 分數 = "score";
	static final String 公式 = "formula";
	static final String 欄位 = "columns";
	//參考 table C121M01B
	interface column {
		
		String 評等建立日期 = "grdCDate";
		String 評等調整日期 = "grdTDate";
		String 完成最終評等日期 = "ratingDate";
		String 聯徵查詢日期 = "jcicQDate";
		String 模型版本 = "varVer";		
		String 初始評等 = "pRating";
		String 獨立評等 = "sRating";
		String 支援評等 = "sprtRating";
		String 調整評等 = "adjRating";
		String 最終評等 = "fRating";
		String 原始最終評等 = "orgFr";
		
		String 升降等_依風險點數 = "adj_pts";
		String 升降等_聯徵 = "adj_j10";
		
		String 註記不調整 = "noAdj";
		String 調整狀態 = "adjustStatus";
		String 調整註記 = "adjustFlag";
		String 調整理由 = "adjustReason";
		
		//因子的原始資料
		String 出生日M1 = "raw_m1";		
		String 年薪幣別P2 = "raw_payCurr";
		String 年薪金額P2 = "raw_payAmt";
		String 其它收入幣別P2 = "raw_otherCurr";
		String 其它收入金額P2 = "raw_otherAmt";
		
		String 家庭所得幣別 = "raw_hincomeCurr";
		String 本次新做案下不動產租金收入幣別 = "raw_rincomeCurr";
		String 財富管理_本行幣別 = "raw_invMBalCurr";
		String 財富管理_它行幣別 = "raw_invOBalCurr";
		String 金融機構存款往來情形幣別 = "raw_branAmtCurr";		
		String 轉換匯率_年薪 = "exRate_pay";
		String 轉換匯率_其他收入 = "exRate_oth";
		String 轉換匯率_家庭所得 = "exRate_hincome";
		String 轉換匯率_本次新做案下不動產租金收入 = "exRate_rincome";
		String 轉換匯率_財富管理本行  = "exRate_invMBal";
		String 轉換匯率_財富管理它行  = "exRate_invOBal";
		String 轉換匯率_金融機構存款往來情形 = "exRate_branAmt";
		
		String 月份數A5 = "raw_a5";
		
		//負面資訊
		String 負面資訊_01 = "chkItem1";
		String 負面資訊_01_退票 = "chkItem1a";
		String 負面資訊_01_拒往 = "chkItem1b";
		String 負面資訊_01_信用卡強停 = "chkItem1c";
		String 負面資訊_01_催收呆帳 = "chkItem1d";
		String 負面資訊_02 = "chkItem2";
		String 負面資訊_04 = "chkItem4";
		String 負面資訊_05 = "chkItem5";
		String 負面資訊_07 = "chkItem7";
		String 負面資訊_09 = "chkItem9";
		String 負面資訊_10 = "chkItem10";
		String 負面資訊_11 = "chkItem11";
		String 負面資訊_12 = "chkItem12";
		String 負面資訊_13 = "chkItem13";
		String 累加風險點數 = "sumRiskPt";
		
		//J10
		String J10信用評分種類 = "j10_score_flag";
		String J10信用評分 = "j10_score";
		
		//因子
		String 因子M1_年齡 = "item_m1";
		String 因子M5_職業 = "item_m5";
		String 因子M7_年資 = "item_m7";
		String 因子P2_年收入_日幣仟元 = "item_p2";
		String 因子A5_契約年限 = "item_a5";
		String 因子Z1 = "item_z1";		
		String 因子Z2 = "item_z2";
		String 因子edu_教育程度 = "item_edu";
		String 因子drate_個人負債比率 = "item_drate";
		
		//標準化因子
		String 標準化M1 = "std_m1";
		String 標準化M5 = "std_m5";
		String 標準化M7 = "std_m7";
		String 標準化P2 = "std_p2";
		String 標準化A5 = "std_a5";
		String 標準化Z1 = "std_z1";		
		String 標準化Z2 = "std_z2";
		
		//加權因子
		String 加權M1 = "weight_m1";
		String 加權M5 = "weight_m5";
		String 加權M7 = "weight_m7";
		String 加權P2 = "weight_p2";
		String 加權A5 = "weight_a5";
		String 加權Z1 = "weight_z1";		
		String 加權Z2 = "weight_z2";
		String 加權edu = "weight_edu";
		String 加權drate = "weight_drate";
		
		//權重分數(2.0新增)
		String 權重分數M1 = "weight_scr_m1";
		String 權重分數M5 = "weight_scr_m5";
		String 權重分數M7 = "weight_scr_m7";
		String 權重分數P2 = "weight_scr_p2";
		String 權重分數A5 = "weight_scr_a5";
		String 權重分數Z1 = "weight_scr_z1";		
		String 權重分數Z2 = "weight_scr_z2";
		String 權重分數edu = "weight_scr_edu";
		String 權重分數drate = "weight_scr_drate";
		
		
		String 合計WeightedScore = "scr_core";
		String 核心模型分數 = "std_core";
		
		//2.0版新增
		String 預測壞率 = "pd";
		String 截距 = "interCept";
		String 斜率 = "slope";
	}

	interface type {
		String 日本消金模型基本 = "jpBase";
		String 日本消金模型評等 = "jpGrade";
		String 日本消金模型違約機率 = "jpDR";
	}
	
	/**
	 * <pre>
	 * 日本消金模型基本
	 * 名稱要和 score*.properties 中的設定相同 ，由 interfaceName+"."+fieldName
	 * </pre>
	 */
	interface jpBase {
		String 年齡_M1= "scr_m1";		
		String 職業_M5= "scr_m5";
		String 年資_M7= "scr_m7"; 
		String 年收入_P2 = "scr_p2";
		String 契約年限_A5 = "scr_a5";
		String 擔保品地點及種類_Z1 = "scr_z1";
		String 市場環境及變現性_Z2 = "scr_z2";
	}
	
	/**
	 * <pre>
	 * 日本消金模型基本2.0
	 * 名稱要和 score*.properties 中的設定相同 ，由 interfaceName+"."+fieldName
	 * </pre>
	 */
	interface jpBase_V2_0 {
		String 年齡_M1= "scr_m1";		
		String 職業_M5= "scr_m5";
		String 年資_M7= "scr_m7"; 
		String 年收入_P2 = "scr_p2";
		String 契約年限_A5 = "scr_a5";
		String 擔保品地點及種類_Z1 = "scr_z1";
		String 市場環境及變現性_Z2 = "scr_z2";
		//J-111-0270日本模型新增
		String 教育程度 = "scr_edu";
		String 個人負債比率 = "scr_drate";
	}

	/**
	 * <pre>
	 * 日本消金模型評等等級項目
	 * </pre>
	 */
	interface jpGrade {
		String 等級 = "level";
	}	

	/**
	 * <pre>
	 * 日本消金模型違約機率
	 * </pre>
	 */
	interface jpDR {
		String 違約機率_預估3年期 = "dr_3yr";
		String 違約機率_預估1年期 = "dr_1yr";
	}
	
	/**
	 * <pre>
	 * 日本消金模型違約機率_V2.0
	 * </pre>
	 */
	interface jpDR_V2_0 {
		String 違約機率_預估1年期 = "dr_1yr";
	}
	

}
