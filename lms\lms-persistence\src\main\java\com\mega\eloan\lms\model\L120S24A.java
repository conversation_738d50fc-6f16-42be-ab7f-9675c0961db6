/* 
 * L120S24A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 風控風險權數主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S24A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S24A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * L120M01A.mainId
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 額度明細mainId<p/>
	 * L140M01A.mainId
	 */
	@Size(max=32)
	@Column(name="REFMAINID_S24A", length=32, columnDefinition="CHAR(32)")
	private String refMainId_s24a;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO_S24A", length=12, columnDefinition="CHAR(12)")
	private String cntrNo_s24a;

	/** 客戶姓名 **/
	@Size(max=120)
	@Column(name="CUSTNAME_S24A", length=120, columnDefinition="VARCHAR(120)")
	private String custName_s24a;

	/** 統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID_S24A", length=10, columnDefinition="VARCHAR(10)")
	private String custId_s24a;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO_S24A", length=1, columnDefinition="CHAR(1)")
	private String dupNo_s24a;

	/** 
	 * 版本日期<p/>
	 * 第一版為20220812
	 */
	@Size(max=10)
	@Column(name="VERSIONDATE_S24A", length=10, columnDefinition="VARCHAR(10)")
	private String versionDate_s24a;

	/** 
	 * 現請額度-幣別<p/>
	 * 原幣幣別
	 */
	@Size(max=3)
	@Column(name="CURRENTAPPLYCURR_S24A", length=3, columnDefinition="CHAR(3)")
	private String currentApplyCurr_s24a;

	/** 
	 * 現請額度-金額<p/>
	 * 原幣金額
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CURRENTAPPLYAMT_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal currentApplyAmt_s24a;

	/** 
	 * 匯率資料日期<p/>
	 * 自己DB存著看
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="RATEDT_S24A", columnDefinition="DATE")
	private Date rateDT_s24a;

	/** 結算匯率(放款幣折台幣) **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="RATELOANTOLOC_S24A", columnDefinition="DECIMAL(9,5)")
	private BigDecimal rateLoanToLoc_s24a;

	/** 
	 * 現請額度-折台幣金額<p/>
	 * 折台幣金額
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CURRENTAPPLYAMT_TWD_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal currentApplyAmt_TWD_s24a;

	/** 
	 * 借款人類別<p/>
	 * CODETYPE= bowrrowerClass_s24a<br/>
	 *  【(中央政府/央行、(地方政府/非營利國營事業、(銀行、(企業-中小企業(4000萬以下)、(企業】
	 */
	@Size(max=2)
	@Column(name="BOWRROWERCLASS_S24A", length=2, columnDefinition="VARCHAR(2)")
	private String bowrrowerClass_s24a;

	/** 借款人外部信評評等 **/
	@Size(max=6)
	@Column(name="CRDGRADE_S24A", length=6, columnDefinition="CHAR(6)")
	private String crdGrade_s24a;

	/** 借款人外部信評文字 **/
	@Size(max=500)
	@Column(name="CRDTEXT_S24A", length=500, columnDefinition="VARCHAR(500)")
	private String crdText_s24a;

	/** 國別 **/
	@Size(max=2)
	@Column(name="NTCODE_S24A", length=2, columnDefinition="VARCHAR(2)")
	private String ntCode_s24a;

	/** 發查DW信評_借款人_ CYC_MN **/
	@Size(max=20)
	@Column(name="SENDDW_CYC_MN_S24A", length=20, columnDefinition="VARCHAR(20)")
	private String sendDW_cyc_mn_s24a;

	/** 發查DW信評_借款人_CUSTKEY **/
	@Size(max=20)
	@Column(name="SENDDW_CUSTKEY_S24A", length=20, columnDefinition="VARCHAR(20)")
	private String sendDW_custkey_s24a;

	/** 發查DW信評_借款人_SCORE **/
	@Size(max=10)
	@Column(name="SENDDW_SCORE_S24A", length=10, columnDefinition="VARCHAR(10)")
	private String sendDW_score_s24a;

	/** 發查DW信評_借款人_時間 **/
	@Column(name="SENDDWTIME_S24A", columnDefinition="TIMESTAMP")
	private Timestamp sendDWTime_s24a;

	/** 借款人風險權數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="BOWRROWERRW_S24A", columnDefinition="DECIMAL(5,2)")
	private BigDecimal bowrrowerRW_s24a;

	/** 本額度僅有保證科目、應收信用狀款項科目 **/
	@Size(max=2)
	@Column(name="ONLYGUAR_S24A", length=2, columnDefinition="VARCHAR(2)")
	private String onlyGuar_s24a;

	/** 
	 * 表外項目信用轉換係數(CCF)<p/>
	 * CODETYPE= ccf_s24a<br/>
	 *  【(20%、(50%、(100%】
	 */
	@Digits(integer=7, fraction=2, groups = Check.class)
	@Column(name="CCF_S24A", columnDefinition="DECIMAL(7,2)")
	private BigDecimal ccf_s24a;

	/** 純表外之信用相當額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CCFAMT_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal ccfAmt_s24a;

	/** 
	 * 純表外之信用相當額_TWD<p/>
	 * 折台幣
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CCFAMT_TWD_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal ccfAmt_TWD_s24a;

	/** 本額度有徵提不動產擔保品 **/
	@Size(max=2)
	@Column(name="HASESTATE_S24A", length=2, columnDefinition="VARCHAR(2)")
	private String hasEstate_s24a;

	/** 央行管制暴險 **/
	@Size(max=2)
	@Column(name="ISCBCONTROL_S24A", length=2, columnDefinition="VARCHAR(2)")
	private String isCBControl_s24a;

	/** 
	 * 分類<p/>
	 * CODETYPE= LTVClass_s24a_Y,LTVClass_s24a_N<br/>
	 *  N=否:下拉4項<br/>
	 *  (住宅、(商用、(ADC-符合標準、( ADC-不符合標準<br/>
	 *  N=是:下拉10項<br/>
	 *  (住宅 一般	公司法人購置住宅貸款<br/>
	 *  (住宅 收益	公司法人購置住宅貸款<br/>
	 *  (住宅 收益	自然人購置第三戶以上之住宅貸款<br/>
	 *  (住宅 收益	餘屋貸款<br/>
	 *  (商用 一般	非合格-工業區研製土地抵押貸款<br/>
	 *  (商用 收益	非合格-工業區研製土地抵押貸款<br/>
	 *  (ADC 住宅區	購地貸款<br/>
	 *  (ADC 住宅區	餘屋貸款<br/>
	 *  (ADC 非住宅區 工業區研製土地抵押貸款<br/>
	 *  (ADC 商業區	購地貸款
	 */
	@Size(max=2)
	@Column(name="LTVCLASS_S24A", length=2, columnDefinition="VARCHAR(2)")
	private String LTVClass_s24a;

	/** 
	 * 類別<p/>
	 * 1=一般、2=收益
	 */
	@Size(max=2)
	@Column(name="LTVTYPE_S24A", length=2, columnDefinition="VARCHAR(2)")
	private String LTVType_s24a;

	/** 本額度之不動產擔保品為土地 **/
	@Size(max=2)
	@Column(name="ESTATEISLAND_S24A", length=2, columnDefinition="VARCHAR(2)")
	private String estateIsLand_s24a;

	/** 該筆不動產為農地、林地 **/
	@Size(max=2)
	@Column(name="ISFARMWOOD_S24A", length=2, columnDefinition="VARCHAR(2)")
	private String isFarmWood_s24a;

	/** 
	 * LTV<p/>
	 * 數字，小數點兩位，0~1000
	 */
	@Digits(integer=10, fraction=2, groups = Check.class)
	@Column(name="LTV_S24A", columnDefinition="DECIMAL(10,2)")
	private BigDecimal LTV_s24a;

	/** 交易對手無擔保暴險風險權數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="OPPONENTRW_S24A", columnDefinition="DECIMAL(5,2)")
	private BigDecimal opponentRW_s24a;

	/** LTV法風險權數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="LTVRW_S24A", columnDefinition="DECIMAL(5,2)")
	private BigDecimal LTVRW_s24a;

	/** 本額度抵減前風險權數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="BEFOREDEDUCTRW_S24A", columnDefinition="DECIMAL(5,2)")
	private BigDecimal beforeDeductRW_s24a;

	/** 本額度是否有合格金融擔保品 **/
	@Size(max=2)
	@Column(name="HASQUACOLL_S24A", length=2, columnDefinition="VARCHAR(2)")
	private String hasQuaColl_s24a;

	/** 
	 * 合計_擔保品價值<p/>
	 * 額度之幣別
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="TOTALCOLLAMT_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal totalCollAmt_s24a;

	/** 
	 * 合計_TWD_擔保品價值<p/>
	 * 折台幣
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="TOTALCOLLAMT_TWD_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal totalCollAmt_TWD_s24a;

	/** 
	 * 合計_合格擔保品抵減金額<p/>
	 * 額度之幣別
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="TOTALDISCOLLAMT_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal totalDisCollAmt_s24a;

	/** 
	 * 合計_TWD_合格擔保品抵減金額<p/>
	 * 折台幣
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="TOTALDISCOLLAMT_TWD_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal totalDisCollAmt_TWD_s24a;

	/** 是否有保證 **/
	@Size(max=2)
	@Column(name="HASGUTCLASS_S24A", length=2, columnDefinition="VARCHAR(2)")
	private String hasGutClass_s24a;

	/** 
	 * 保證類別<p/>
	 * CODETYPE= gutClass_s24a<br/>
	 *  (中小企業信保基金、(農業信保基金、(海外信用保證基金、(國際合作發展基金會信用保證、(OECD之官方輸出信用機構、(公庫/主管機關保證-中央政府、(銀行、(其他(如:高鐵三方保證等)
	 */
	@Size(max=2)
	@Column(name="GUTCLASS_S24A", length=2, columnDefinition="VARCHAR(2)")
	private String gutClass_s24a;

	/** 
	 * 保證類別_其他說明<p/>
	 * 保證類別選其他時要輸入的說明
	 */
	@Size(max=300)
	@Column(name="GUTCLASSOTHERDESC_S24A", length=300, columnDefinition="VARCHAR(300)")
	private String gutClassOtherDesc_s24a;

	/** 
	 * 保證機構國別<p/>
	 * combokey=CountryCode
	 */
	@Size(max=2)
	@Column(name="GUTDEPTCOUNTRY_S24A", length=2, columnDefinition="CHAR(2)")
	private String gutDeptCountry_s24a;

	/** 
	 * 保證銀行代碼<p/>
	 * 國內3位代碼、國外SWIFT CODE
	 */
	@Size(max=10)
	@Column(name="GUTDEPTID_S24A", length=10, columnDefinition="VARCHAR(10)")
	private String gutDeptID_s24a;

	/** 
	 * 保證銀行名稱<p/>
	 * 利用gutDeptID_s24a查回來的CUST_NM
	 */
	@Size(max=200)
	@Column(name="GUTDEPTNAME_S24A", length=200, columnDefinition="VARCHAR(200)")
	private String gutDeptName_s24a;

	/** 保證成數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="GUTPERCENT_S24A", columnDefinition="DECIMAL(5,2)")
	private BigDecimal gutPercent_s24a;

	/** 保證機構風險權數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="GUTDEPTRW_S24A", columnDefinition="DECIMAL(5,2)")
	private BigDecimal gutDeptRW_s24a;

	/** 發查DW信評_保證機構_ CYC_MN **/
	@Size(max=20)
	@Column(name="SENDDW_GUT_CYC_MN_S24A", length=20, columnDefinition="VARCHAR(20)")
	private String sendDW_gut_cyc_mn_s24a;

	/** 發查DW信評_保證機構_CUSTKEY **/
	@Size(max=20)
	@Column(name="SENDDW_GUT_CUSTKEY_S24A", length=20, columnDefinition="VARCHAR(20)")
	private String sendDW_gut_custkey_s24a;

	/** 發查DW信評_保證機構_SCORE **/
	@Size(max=10)
	@Column(name="SENDDW_GUT_SCORE_S24A", length=10, columnDefinition="VARCHAR(10)")
	private String sendDW_gut_score_s24a;

	/** 發查DW信評_保證機構_時間 **/
	@Column(name="SENDDW_GUTTIME_S24A", columnDefinition="TIMESTAMP")
	private Timestamp sendDW_gutTime_s24a;

	/** 有徵提評等較佳之連帶保證人 **/
	@Size(max=2)
	@Column(name="EXTRACTBETTERGUAR_S24A", length=2, columnDefinition="VARCHAR(2)")
	private String extractBetterGuar_s24a;

	/** 
	 * 連帶保證保證人ID<p/>
	 * (限法人戶，請選評等最佳者)
	 */
	@Size(max=10)
	@Column(name="GUARID_S24A", length=10, columnDefinition="VARCHAR(10)")
	private String guarId_s24a;

	/** 連帶保證保證人重覆序號 **/
	@Size(max=1)
	@Column(name="GUARDUPNO_S24A", length=1, columnDefinition="CHAR(1)")
	private String guarDupNo_s24a;

	/** 連帶保證人名稱 **/
	@Size(max=120)
	@Column(name="GUARNAME_S24A", length=120, columnDefinition="VARCHAR(120)")
	private String guarName_s24a;

	/** 連帶保證人外部信評 **/
	@Size(max=6)
	@Column(name="GUARCRDGRADE_S24A", length=6, columnDefinition="CHAR(6)")
	private String guarCrdGrade_s24a;

	/** 連帶保證人外部信評文字 **/
	@Size(max=500)
	@Column(name="GUARCRDTEXT_S24A", length=500, columnDefinition="VARCHAR(500)")
	private String guarCrdText_s24a;

	/** 連帶保證人風險權數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="GUARRW_S24A", columnDefinition="DECIMAL(5,2)")
	private BigDecimal guarRW_s24a;

	/** 發查DW信評_連帶保證人_YC_MN **/
	@Size(max=20)
	@Column(name="SENDDW_GUAR_CYC_MN_S24A", length=20, columnDefinition="VARCHAR(20)")
	private String sendDW_guar_cyc_mn_s24a;

	/** 發查DW信評_連帶保證_CUSTKEY **/
	@Size(max=20)
	@Column(name="SENDDW_GUAR_CUSTKEY_S24A", length=20, columnDefinition="VARCHAR(20)")
	private String sendDW_guar_custkey_s24a;

	/** 發查DW信評_連帶保證人_SCORE **/
	@Size(max=10)
	@Column(name="SENDDW_GUAR_SCORE_S24A", length=10, columnDefinition="VARCHAR(10)")
	private String sendDW_guar_score_s24a;

	/** 發查DW信評_連帶保證人_時間 **/
	@Column(name="SENDDW_GUARTIME_S24A", columnDefinition="TIMESTAMP")
	private Timestamp sendDW_guarTime_s24a;

	/** 合格擔保品抵減後暴險額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CALDISCOLLEXPOSUREAMT_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal calDisCollExposureAmt_s24a;

	/** 
	 * 合格擔保品抵減後暴險額_TWD<p/>
	 * 折台幣
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CALDISCOLLEXPOSUREAMT_TWD_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal calDisCollExposureAmt_TWD_s24a;

	/** 信保部位 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CALHASGUTPARTAMT_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal calHasGutPartAmt_s24a;

	/** 
	 * 信保部位_TWD<p/>
	 * 折台幣
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CALHASGUTPARTAMT_TWD_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal calHasGutPartAmt_TWD_s24a;

	/** 信保部位風險性資產 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CALHASGUTDEPTRWA_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal calHasGutDeptRWA_s24a;

	/** 
	 * 信保部位風險性資產_TWD<p/>
	 * 折台幣
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CALHASGUTDEPTRWA_TWD_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal calHasGutDeptRWA_TWD_s24a;

	/** 非信保部位 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CALNOGUTPARTAMT_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal calNoGutPartAmt_s24a;

	/** 
	 * 非信保部位_TWD<p/>
	 * 折台幣
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CALNOGUTPARTAMT_TWD_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal calNoGutPartAmt_TWD_s24a;

	/** 非信保部位風險權數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="CALNOGUTPARTRW_S24A", columnDefinition="DECIMAL(5,2)")
	private BigDecimal calNoGutPartRW_s24a;

	/** 非信保部位風險性資產 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CALNOGUTRWA_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal calNoGutRWA_s24a;

	/** 
	 * 非信保部位風險性資產_TWD<p/>
	 * 折台幣
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CALNOGUTRWA_TWD_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal calNoGutRWA_TWD_s24a;

	/** 抵減後風險性資產 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CALDEDUCTRWA_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal calDeductRWA_s24a;

	/** 
	 * 抵減後風險性資產_TWD<p/>
	 * 折台幣
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CALDEDUCTRWA_TWD_S24A", columnDefinition="DECIMAL(17,2)")
	private BigDecimal calDeductRWA_TWD_s24a;

	/** 抵減後風險權數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="CALDEDUCTRW_S24A", columnDefinition="DECIMAL(5,2)")
	private BigDecimal calDeductRW_s24a;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 特殊融資類別 **/
	@Size(max=3)
	@Column(name="SPECIALFINRISKTYPE_S24A", length=3, columnDefinition="VARCHAR(3)")
	private String specialFinRiskType_s24a;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * L120M01A.mainId
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  L120M01A.mainId
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得額度明細mainId<p/>
	 * L140M01A.mainId
	 */
	public String getRefMainId_s24a() {
		return this.refMainId_s24a;
	}
	/**
	 *  設定額度明細mainId<p/>
	 *  L140M01A.mainId
	 **/
	public void setRefMainId_s24a(String value) {
		this.refMainId_s24a = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo_s24a() {
		return this.cntrNo_s24a;
	}
	/** 設定額度序號 **/
	public void setCntrNo_s24a(String value) {
		this.cntrNo_s24a = value;
	}

	/** 取得客戶姓名 **/
	public String getCustName_s24a() {
		return this.custName_s24a;
	}
	/** 設定客戶姓名 **/
	public void setCustName_s24a(String value) {
		this.custName_s24a = value;
	}

	/** 取得統一編號 **/
	public String getCustId_s24a() {
		return this.custId_s24a;
	}
	/** 設定統一編號 **/
	public void setCustId_s24a(String value) {
		this.custId_s24a = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo_s24a() {
		return this.dupNo_s24a;
	}
	/** 設定重覆序號 **/
	public void setDupNo_s24a(String value) {
		this.dupNo_s24a = value;
	}

	/** 
	 * 取得版本日期<p/>
	 * 第一版為20220812
	 */
	public String getVersionDate_s24a() {
		return this.versionDate_s24a;
	}
	/**
	 *  設定版本日期<p/>
	 *  第一版為20220812
	 **/
	public void setVersionDate_s24a(String value) {
		this.versionDate_s24a = value;
	}

	/** 
	 * 取得現請額度-幣別<p/>
	 * 原幣幣別
	 */
	public String getCurrentApplyCurr_s24a() {
		return this.currentApplyCurr_s24a;
	}
	/**
	 *  設定現請額度-幣別<p/>
	 *  原幣幣別
	 **/
	public void setCurrentApplyCurr_s24a(String value) {
		this.currentApplyCurr_s24a = value;
	}

	/** 
	 * 取得現請額度-金額<p/>
	 * 原幣金額
	 */
	public BigDecimal getCurrentApplyAmt_s24a() {
		return this.currentApplyAmt_s24a;
	}
	/**
	 *  設定現請額度-金額<p/>
	 *  原幣金額
	 **/
	public void setCurrentApplyAmt_s24a(BigDecimal value) {
		this.currentApplyAmt_s24a = value;
	}

	/** 
	 * 取得匯率資料日期<p/>
	 * 自己DB存著看
	 */
	public Date getRateDT_s24a() {
		return this.rateDT_s24a;
	}
	/**
	 *  設定匯率資料日期<p/>
	 *  自己DB存著看
	 **/
	public void setRateDT_s24a(Date value) {
		this.rateDT_s24a = value;
	}

	/** 取得結算匯率(放款幣折台幣) **/
	public BigDecimal getRateLoanToLoc_s24a() {
		return this.rateLoanToLoc_s24a;
	}
	/** 設定結算匯率(放款幣折台幣) **/
	public void setRateLoanToLoc_s24a(BigDecimal value) {
		this.rateLoanToLoc_s24a = value;
	}

	/** 
	 * 取得現請額度-折台幣金額<p/>
	 * 折台幣金額
	 */
	public BigDecimal getCurrentApplyAmt_TWD_s24a() {
		return this.currentApplyAmt_TWD_s24a;
	}
	/**
	 *  設定現請額度-折台幣金額<p/>
	 *  折台幣金額
	 **/
	public void setCurrentApplyAmt_TWD_s24a(BigDecimal value) {
		this.currentApplyAmt_TWD_s24a = value;
	}

	/** 
	 * 取得借款人類別<p/>
	 * CODETYPE= bowrrowerClass_s24a<br/>
	 *  【(中央政府/央行、(地方政府/非營利國營事業、(銀行、(企業-中小企業(4000萬以下)、(企業】
	 */
	public String getBowrrowerClass_s24a() {
		return this.bowrrowerClass_s24a;
	}
	/**
	 *  設定借款人類別<p/>
	 *  CODETYPE= bowrrowerClass_s24a<br/>
	 *  【(中央政府/央行、(地方政府/非營利國營事業、(銀行、(企業-中小企業(4000萬以下)、(企業】
	 **/
	public void setBowrrowerClass_s24a(String value) {
		this.bowrrowerClass_s24a = value;
	}

	/** 取得借款人外部信評評等 **/
	public String getCrdGrade_s24a() {
		return this.crdGrade_s24a;
	}
	/** 設定借款人外部信評評等 **/
	public void setCrdGrade_s24a(String value) {
		this.crdGrade_s24a = value;
	}

	/** 取得借款人外部信評文字 **/
	public String getCrdText_s24a() {
		return this.crdText_s24a;
	}
	/** 設定借款人外部信評文字 **/
	public void setCrdText_s24a(String value) {
		this.crdText_s24a = value;
	}

	/** 取得國別 **/
	public String getNtCode_s24a() {
		return this.ntCode_s24a;
	}
	/** 設定國別 **/
	public void setNtCode_s24a(String value) {
		this.ntCode_s24a = value;
	}

	/** 取得發查DW信評_借款人_ CYC_MN **/
	public String getSendDW_cyc_mn_s24a() {
		return this.sendDW_cyc_mn_s24a;
	}
	/** 設定發查DW信評_借款人_ CYC_MN **/
	public void setSendDW_cyc_mn_s24a(String value) {
		this.sendDW_cyc_mn_s24a = value;
	}

	/** 取得發查DW信評_借款人_CUSTKEY **/
	public String getSendDW_custkey_s24a() {
		return this.sendDW_custkey_s24a;
	}
	/** 設定發查DW信評_借款人_CUSTKEY **/
	public void setSendDW_custkey_s24a(String value) {
		this.sendDW_custkey_s24a = value;
	}

	/** 取得發查DW信評_借款人_SCORE **/
	public String getSendDW_score_s24a() {
		return this.sendDW_score_s24a;
	}
	/** 設定發查DW信評_借款人_SCORE **/
	public void setSendDW_score_s24a(String value) {
		this.sendDW_score_s24a = value;
	}

	/** 取得發查DW信評_借款人_時間 **/
	public Timestamp getSendDWTime_s24a() {
		return this.sendDWTime_s24a;
	}
	/** 設定發查DW信評_借款人_時間 **/
	public void setSendDWTime_s24a(Timestamp value) {
		this.sendDWTime_s24a = value;
	}

	/** 取得借款人風險權數 **/
	public BigDecimal getBowrrowerRW_s24a() {
		return this.bowrrowerRW_s24a;
	}
	/** 設定借款人風險權數 **/
	public void setBowrrowerRW_s24a(BigDecimal value) {
		this.bowrrowerRW_s24a = value;
	}

	/** 取得本額度僅有保證科目、應收信用狀款項科目 **/
	public String getOnlyGuar_s24a() {
		return this.onlyGuar_s24a;
	}
	/** 設定本額度僅有保證科目、應收信用狀款項科目 **/
	public void setOnlyGuar_s24a(String value) {
		this.onlyGuar_s24a = value;
	}

	/** 
	 * 取得表外項目信用轉換係數(CCF)<p/>
	 * CODETYPE= ccf_s24a<br/>
	 *  【(20%、(50%、(100%】
	 */
	public BigDecimal getCcf_s24a() {
		return this.ccf_s24a;
	}
	/**
	 *  設定表外項目信用轉換係數(CCF)<p/>
	 *  CODETYPE= ccf_s24a<br/>
	 *  【(20%、(50%、(100%】
	 **/
	public void setCcf_s24a(BigDecimal value) {
		this.ccf_s24a = value;
	}

	/** 取得純表外之信用相當額 **/
	public BigDecimal getCcfAmt_s24a() {
		return this.ccfAmt_s24a;
	}
	/** 設定純表外之信用相當額 **/
	public void setCcfAmt_s24a(BigDecimal value) {
		this.ccfAmt_s24a = value;
	}

	/** 
	 * 取得純表外之信用相當額_TWD<p/>
	 * 折台幣
	 */
	public BigDecimal getCcfAmt_TWD_s24a() {
		return this.ccfAmt_TWD_s24a;
	}
	/**
	 *  設定純表外之信用相當額_TWD<p/>
	 *  折台幣
	 **/
	public void setCcfAmt_TWD_s24a(BigDecimal value) {
		this.ccfAmt_TWD_s24a = value;
	}

	/** 取得本額度有徵提不動產擔保品 **/
	public String getHasEstate_s24a() {
		return this.hasEstate_s24a;
	}
	/** 設定本額度有徵提不動產擔保品 **/
	public void setHasEstate_s24a(String value) {
		this.hasEstate_s24a = value;
	}

	/** 取得央行管制暴險 **/
	public String getIsCBControl_s24a() {
		return this.isCBControl_s24a;
	}
	/** 設定央行管制暴險 **/
	public void setIsCBControl_s24a(String value) {
		this.isCBControl_s24a = value;
	}

	/** 
	 * 取得分類<p/>
	 * CODETYPE= LTVClass_s24a_Y,LTVClass_s24a_N<br/>
	 *  N=否:下拉4項<br/>
	 *  (住宅、(商用、(ADC-符合標準、( ADC-不符合標準<br/>
	 *  N=是:下拉10項<br/>
	 *  (住宅 一般	公司法人購置住宅貸款<br/>
	 *  (住宅 收益	公司法人購置住宅貸款<br/>
	 *  (住宅 收益	自然人購置第三戶以上之住宅貸款<br/>
	 *  (住宅 收益	餘屋貸款<br/>
	 *  (商用 一般	非合格-工業區研製土地抵押貸款<br/>
	 *  (商用 收益	非合格-工業區研製土地抵押貸款<br/>
	 *  (ADC 住宅區	購地貸款<br/>
	 *  (ADC 住宅區	餘屋貸款<br/>
	 *  (ADC 非住宅區 工業區研製土地抵押貸款<br/>
	 *  (ADC 商業區	購地貸款
	 */
	public String getLTVClass_s24a() {
		return this.LTVClass_s24a;
	}
	/**
	 *  設定分類<p/>
	 *  CODETYPE= LTVClass_s24a_Y,LTVClass_s24a_N<br/>
	 *  N=否:下拉4項<br/>
	 *  (住宅、(商用、(ADC-符合標準、( ADC-不符合標準<br/>
	 *  N=是:下拉10項<br/>
	 *  (住宅 一般	公司法人購置住宅貸款<br/>
	 *  (住宅 收益	公司法人購置住宅貸款<br/>
	 *  (住宅 收益	自然人購置第三戶以上之住宅貸款<br/>
	 *  (住宅 收益	餘屋貸款<br/>
	 *  (商用 一般	非合格-工業區研製土地抵押貸款<br/>
	 *  (商用 收益	非合格-工業區研製土地抵押貸款<br/>
	 *  (ADC 住宅區	購地貸款<br/>
	 *  (ADC 住宅區	餘屋貸款<br/>
	 *  (ADC 非住宅區 工業區研製土地抵押貸款<br/>
	 *  (ADC 商業區	購地貸款
	 **/
	public void setLTVClass_s24a(String value) {
		this.LTVClass_s24a = value;
	}

	/** 
	 * 取得類別<p/>
	 * 1=一般、2=收益
	 */
	public String getLTVType_s24a() {
		return this.LTVType_s24a;
	}
	/**
	 *  設定類別<p/>
	 *  1=一般、2=收益
	 **/
	public void setLTVType_s24a(String value) {
		this.LTVType_s24a = value;
	}

	/** 取得本額度之不動產擔保品為土地 **/
	public String getEstateIsLand_s24a() {
		return this.estateIsLand_s24a;
	}
	/** 設定本額度之不動產擔保品為土地 **/
	public void setEstateIsLand_s24a(String value) {
		this.estateIsLand_s24a = value;
	}

	/** 取得該筆不動產為農地、林地 **/
	public String getIsFarmWood_s24a() {
		return this.isFarmWood_s24a;
	}
	/** 設定該筆不動產為農地、林地 **/
	public void setIsFarmWood_s24a(String value) {
		this.isFarmWood_s24a = value;
	}

	/** 
	 * 取得LTV<p/>
	 * 數字，小數點兩位，0~1000
	 */
	public BigDecimal getLTV_s24a() {
		return this.LTV_s24a;
	}
	/**
	 *  設定LTV<p/>
	 *  數字，小數點兩位，0~1000
	 **/
	public void setLTV_s24a(BigDecimal value) {
		this.LTV_s24a = value;
	}

	/** 取得交易對手無擔保暴險風險權數 **/
	public BigDecimal getOpponentRW_s24a() {
		return this.opponentRW_s24a;
	}
	/** 設定交易對手無擔保暴險風險權數 **/
	public void setOpponentRW_s24a(BigDecimal value) {
		this.opponentRW_s24a = value;
	}

	/** 取得LTV法風險權數 **/
	public BigDecimal getLTVRW_s24a() {
		return this.LTVRW_s24a;
	}
	/** 設定LTV法風險權數 **/
	public void setLTVRW_s24a(BigDecimal value) {
		this.LTVRW_s24a = value;
	}

	/** 取得本額度抵減前風險權數 **/
	public BigDecimal getBeforeDeductRW_s24a() {
		return this.beforeDeductRW_s24a;
	}
	/** 設定本額度抵減前風險權數 **/
	public void setBeforeDeductRW_s24a(BigDecimal value) {
		this.beforeDeductRW_s24a = value;
	}

	/** 取得本額度是否有合格金融擔保品 **/
	public String getHasQuaColl_s24a() {
		return this.hasQuaColl_s24a;
	}
	/** 設定本額度是否有合格金融擔保品 **/
	public void setHasQuaColl_s24a(String value) {
		this.hasQuaColl_s24a = value;
	}

	/** 
	 * 取得合計_擔保品價值<p/>
	 * 額度之幣別
	 */
	public BigDecimal getTotalCollAmt_s24a() {
		return this.totalCollAmt_s24a;
	}
	/**
	 *  設定合計_擔保品價值<p/>
	 *  額度之幣別
	 **/
	public void setTotalCollAmt_s24a(BigDecimal value) {
		this.totalCollAmt_s24a = value;
	}

	/** 
	 * 取得合計_TWD_擔保品價值<p/>
	 * 折台幣
	 */
	public BigDecimal getTotalCollAmt_TWD_s24a() {
		return this.totalCollAmt_TWD_s24a;
	}
	/**
	 *  設定合計_TWD_擔保品價值<p/>
	 *  折台幣
	 **/
	public void setTotalCollAmt_TWD_s24a(BigDecimal value) {
		this.totalCollAmt_TWD_s24a = value;
	}

	/** 
	 * 取得合計_合格擔保品抵減金額<p/>
	 * 額度之幣別
	 */
	public BigDecimal getTotalDisCollAmt_s24a() {
		return this.totalDisCollAmt_s24a;
	}
	/**
	 *  設定合計_合格擔保品抵減金額<p/>
	 *  額度之幣別
	 **/
	public void setTotalDisCollAmt_s24a(BigDecimal value) {
		this.totalDisCollAmt_s24a = value;
	}

	/** 
	 * 取得合計_TWD_合格擔保品抵減金額<p/>
	 * 折台幣
	 */
	public BigDecimal getTotalDisCollAmt_TWD_s24a() {
		return this.totalDisCollAmt_TWD_s24a;
	}
	/**
	 *  設定合計_TWD_合格擔保品抵減金額<p/>
	 *  折台幣
	 **/
	public void setTotalDisCollAmt_TWD_s24a(BigDecimal value) {
		this.totalDisCollAmt_TWD_s24a = value;
	}

	/** 取得是否有保證 **/
	public String getHasGutClass_s24a() {
		return this.hasGutClass_s24a;
	}
	/** 設定是否有保證 **/
	public void setHasGutClass_s24a(String value) {
		this.hasGutClass_s24a = value;
	}

	/** 
	 * 取得保證類別<p/>
	 * CODETYPE= gutClass_s24a<br/>
	 *  (中小企業信保基金、(農業信保基金、(海外信用保證基金、(國際合作發展基金會信用保證、(OECD之官方輸出信用機構、(公庫/主管機關保證-中央政府、(銀行、(其他(如:高鐵三方保證等)
	 */
	public String getGutClass_s24a() {
		return this.gutClass_s24a;
	}
	/**
	 *  設定保證類別<p/>
	 *  CODETYPE= gutClass_s24a<br/>
	 *  (中小企業信保基金、(農業信保基金、(海外信用保證基金、(國際合作發展基金會信用保證、(OECD之官方輸出信用機構、(公庫/主管機關保證-中央政府、(銀行、(其他(如:高鐵三方保證等)
	 **/
	public void setGutClass_s24a(String value) {
		this.gutClass_s24a = value;
	}

	/** 
	 * 取得保證類別_其他說明<p/>
	 * 保證類別選其他時要輸入的說明
	 */
	public String getGutClassOtherDesc_s24a() {
		return this.gutClassOtherDesc_s24a;
	}
	/**
	 *  設定保證類別_其他說明<p/>
	 *  保證類別選其他時要輸入的說明
	 **/
	public void setGutClassOtherDesc_s24a(String value) {
		this.gutClassOtherDesc_s24a = value;
	}

	/** 
	 * 取得保證機構國別<p/>
	 * combokey=CountryCode
	 */
	public String getGutDeptCountry_s24a() {
		return this.gutDeptCountry_s24a;
	}
	/**
	 *  設定保證機構國別<p/>
	 *  combokey=CountryCode
	 **/
	public void setGutDeptCountry_s24a(String value) {
		this.gutDeptCountry_s24a = value;
	}

	/** 
	 * 取得保證銀行代碼<p/>
	 * 國內3位代碼、國外SWIFT CODE
	 */
	public String getGutDeptID_s24a() {
		return this.gutDeptID_s24a;
	}
	/**
	 *  設定保證銀行代碼<p/>
	 *  國內3位代碼、國外SWIFT CODE
	 **/
	public void setGutDeptID_s24a(String value) {
		this.gutDeptID_s24a = value;
	}

	/** 
	 * 取得保證銀行名稱<p/>
	 * 利用gutDeptID_s24a查回來的CUST_NM
	 */
	public String getGutDeptName_s24a() {
		return this.gutDeptName_s24a;
	}
	/**
	 *  設定保證銀行名稱<p/>
	 *  利用gutDeptID_s24a查回來的CUST_NM
	 **/
	public void setGutDeptName_s24a(String value) {
		this.gutDeptName_s24a = value;
	}

	/** 取得保證成數 **/
	public BigDecimal getGutPercent_s24a() {
		return this.gutPercent_s24a;
	}
	/** 設定保證成數 **/
	public void setGutPercent_s24a(BigDecimal value) {
		this.gutPercent_s24a = value;
	}

	/** 取得保證機構風險權數 **/
	public BigDecimal getGutDeptRW_s24a() {
		return this.gutDeptRW_s24a;
	}
	/** 設定保證機構風險權數 **/
	public void setGutDeptRW_s24a(BigDecimal value) {
		this.gutDeptRW_s24a = value;
	}

	/** 取得發查DW信評_保證機構_ CYC_MN **/
	public String getSendDW_gut_cyc_mn_s24a() {
		return this.sendDW_gut_cyc_mn_s24a;
	}
	/** 設定發查DW信評_保證機構_ CYC_MN **/
	public void setSendDW_gut_cyc_mn_s24a(String value) {
		this.sendDW_gut_cyc_mn_s24a = value;
	}

	/** 取得發查DW信評_保證機構_CUSTKEY **/
	public String getSendDW_gut_custkey_s24a() {
		return this.sendDW_gut_custkey_s24a;
	}
	/** 設定發查DW信評_保證機構_CUSTKEY **/
	public void setSendDW_gut_custkey_s24a(String value) {
		this.sendDW_gut_custkey_s24a = value;
	}

	/** 取得發查DW信評_保證機構_SCORE **/
	public String getSendDW_gut_score_s24a() {
		return this.sendDW_gut_score_s24a;
	}
	/** 設定發查DW信評_保證機構_SCORE **/
	public void setSendDW_gut_score_s24a(String value) {
		this.sendDW_gut_score_s24a = value;
	}

	/** 取得發查DW信評_保證機構_時間 **/
	public Timestamp getSendDW_gutTime_s24a() {
		return this.sendDW_gutTime_s24a;
	}
	/** 設定發查DW信評_保證機構_時間 **/
	public void setSendDW_gutTime_s24a(Timestamp value) {
		this.sendDW_gutTime_s24a = value;
	}

	/** 取得有徵提評等較佳之連帶保證人 **/
	public String getExtractBetterGuar_s24a() {
		return this.extractBetterGuar_s24a;
	}
	/** 設定有徵提評等較佳之連帶保證人 **/
	public void setExtractBetterGuar_s24a(String value) {
		this.extractBetterGuar_s24a = value;
	}

	/** 
	 * 取得連帶保證保證人ID<p/>
	 * (限法人戶，請選評等最佳者)
	 */
	public String getGuarId_s24a() {
		return this.guarId_s24a;
	}
	/**
	 *  設定連帶保證保證人ID<p/>
	 *  (限法人戶，請選評等最佳者)
	 **/
	public void setGuarId_s24a(String value) {
		this.guarId_s24a = value;
	}

	/** 取得連帶保證保證人重覆序號 **/
	public String getGuarDupNo_s24a() {
		return this.guarDupNo_s24a;
	}
	/** 設定連帶保證保證人重覆序號 **/
	public void setGuarDupNo_s24a(String value) {
		this.guarDupNo_s24a = value;
	}

	/** 取得連帶保證人名稱 **/
	public String getGuarName_s24a() {
		return this.guarName_s24a;
	}
	/** 設定連帶保證人名稱 **/
	public void setGuarName_s24a(String value) {
		this.guarName_s24a = value;
	}

	/** 取得連帶保證人外部信評 **/
	public String getGuarCrdGrade_s24a() {
		return this.guarCrdGrade_s24a;
	}
	/** 設定連帶保證人外部信評 **/
	public void setGuarCrdGrade_s24a(String value) {
		this.guarCrdGrade_s24a = value;
	}

	/** 取得連帶保證人外部信評文字 **/
	public String getGuarCrdText_s24a() {
		return this.guarCrdText_s24a;
	}
	/** 設定連帶保證人外部信評文字 **/
	public void setGuarCrdText_s24a(String value) {
		this.guarCrdText_s24a = value;
	}

	/** 取得連帶保證人風險權數 **/
	public BigDecimal getGuarRW_s24a() {
		return this.guarRW_s24a;
	}
	/** 設定連帶保證人風險權數 **/
	public void setGuarRW_s24a(BigDecimal value) {
		this.guarRW_s24a = value;
	}

	/** 取得發查DW信評_連帶保證人_YC_MN **/
	public String getSendDW_guar_cyc_mn_s24a() {
		return this.sendDW_guar_cyc_mn_s24a;
	}
	/** 設定發查DW信評_連帶保證人_YC_MN **/
	public void setSendDW_guar_cyc_mn_s24a(String value) {
		this.sendDW_guar_cyc_mn_s24a = value;
	}

	/** 取得發查DW信評_連帶保證_CUSTKEY **/
	public String getSendDW_guar_custkey_s24a() {
		return this.sendDW_guar_custkey_s24a;
	}
	/** 設定發查DW信評_連帶保證_CUSTKEY **/
	public void setSendDW_guar_custkey_s24a(String value) {
		this.sendDW_guar_custkey_s24a = value;
	}

	/** 取得發查DW信評_連帶保證人_SCORE **/
	public String getSendDW_guar_score_s24a() {
		return this.sendDW_guar_score_s24a;
	}
	/** 設定發查DW信評_連帶保證人_SCORE **/
	public void setSendDW_guar_score_s24a(String value) {
		this.sendDW_guar_score_s24a = value;
	}

	/** 取得發查DW信評_連帶保證人_時間 **/
	public Timestamp getSendDW_guarTime_s24a() {
		return this.sendDW_guarTime_s24a;
	}
	/** 設定發查DW信評_連帶保證人_時間 **/
	public void setSendDW_guarTime_s24a(Timestamp value) {
		this.sendDW_guarTime_s24a = value;
	}

	/** 取得合格擔保品抵減後暴險額 **/
	public BigDecimal getCalDisCollExposureAmt_s24a() {
		return this.calDisCollExposureAmt_s24a;
	}
	/** 設定合格擔保品抵減後暴險額 **/
	public void setCalDisCollExposureAmt_s24a(BigDecimal value) {
		this.calDisCollExposureAmt_s24a = value;
	}

	/** 
	 * 取得合格擔保品抵減後暴險額_TWD<p/>
	 * 折台幣
	 */
	public BigDecimal getCalDisCollExposureAmt_TWD_s24a() {
		return this.calDisCollExposureAmt_TWD_s24a;
	}
	/**
	 *  設定合格擔保品抵減後暴險額_TWD<p/>
	 *  折台幣
	 **/
	public void setCalDisCollExposureAmt_TWD_s24a(BigDecimal value) {
		this.calDisCollExposureAmt_TWD_s24a = value;
	}

	/** 取得信保部位 **/
	public BigDecimal getCalHasGutPartAmt_s24a() {
		return this.calHasGutPartAmt_s24a;
	}
	/** 設定信保部位 **/
	public void setCalHasGutPartAmt_s24a(BigDecimal value) {
		this.calHasGutPartAmt_s24a = value;
	}

	/** 
	 * 取得信保部位_TWD<p/>
	 * 折台幣
	 */
	public BigDecimal getCalHasGutPartAmt_TWD_s24a() {
		return this.calHasGutPartAmt_TWD_s24a;
	}
	/**
	 *  設定信保部位_TWD<p/>
	 *  折台幣
	 **/
	public void setCalHasGutPartAmt_TWD_s24a(BigDecimal value) {
		this.calHasGutPartAmt_TWD_s24a = value;
	}

	/** 取得信保部位風險性資產 **/
	public BigDecimal getCalHasGutDeptRWA_s24a() {
		return this.calHasGutDeptRWA_s24a;
	}
	/** 設定信保部位風險性資產 **/
	public void setCalHasGutDeptRWA_s24a(BigDecimal value) {
		this.calHasGutDeptRWA_s24a = value;
	}

	/** 
	 * 取得信保部位風險性資產_TWD<p/>
	 * 折台幣
	 */
	public BigDecimal getCalHasGutDeptRWA_TWD_s24a() {
		return this.calHasGutDeptRWA_TWD_s24a;
	}
	/**
	 *  設定信保部位風險性資產_TWD<p/>
	 *  折台幣
	 **/
	public void setCalHasGutDeptRWA_TWD_s24a(BigDecimal value) {
		this.calHasGutDeptRWA_TWD_s24a = value;
	}

	/** 取得非信保部位 **/
	public BigDecimal getCalNoGutPartAmt_s24a() {
		return this.calNoGutPartAmt_s24a;
	}
	/** 設定非信保部位 **/
	public void setCalNoGutPartAmt_s24a(BigDecimal value) {
		this.calNoGutPartAmt_s24a = value;
	}

	/** 
	 * 取得非信保部位_TWD<p/>
	 * 折台幣
	 */
	public BigDecimal getCalNoGutPartAmt_TWD_s24a() {
		return this.calNoGutPartAmt_TWD_s24a;
	}
	/**
	 *  設定非信保部位_TWD<p/>
	 *  折台幣
	 **/
	public void setCalNoGutPartAmt_TWD_s24a(BigDecimal value) {
		this.calNoGutPartAmt_TWD_s24a = value;
	}

	/** 取得非信保部位風險權數 **/
	public BigDecimal getCalNoGutPartRW_s24a() {
		return this.calNoGutPartRW_s24a;
	}
	/** 設定非信保部位風險權數 **/
	public void setCalNoGutPartRW_s24a(BigDecimal value) {
		this.calNoGutPartRW_s24a = value;
	}

	/** 取得非信保部位風險性資產 **/
	public BigDecimal getCalNoGutRWA_s24a() {
		return this.calNoGutRWA_s24a;
	}
	/** 設定非信保部位風險性資產 **/
	public void setCalNoGutRWA_s24a(BigDecimal value) {
		this.calNoGutRWA_s24a = value;
	}

	/** 
	 * 取得非信保部位風險性資產_TWD<p/>
	 * 折台幣
	 */
	public BigDecimal getCalNoGutRWA_TWD_s24a() {
		return this.calNoGutRWA_TWD_s24a;
	}
	/**
	 *  設定非信保部位風險性資產_TWD<p/>
	 *  折台幣
	 **/
	public void setCalNoGutRWA_TWD_s24a(BigDecimal value) {
		this.calNoGutRWA_TWD_s24a = value;
	}

	/** 取得抵減後風險性資產 **/
	public BigDecimal getCalDeductRWA_s24a() {
		return this.calDeductRWA_s24a;
	}
	/** 設定抵減後風險性資產 **/
	public void setCalDeductRWA_s24a(BigDecimal value) {
		this.calDeductRWA_s24a = value;
	}

	/** 
	 * 取得抵減後風險性資產_TWD<p/>
	 * 折台幣
	 */
	public BigDecimal getCalDeductRWA_TWD_s24a() {
		return this.calDeductRWA_TWD_s24a;
	}
	/**
	 *  設定抵減後風險性資產_TWD<p/>
	 *  折台幣
	 **/
	public void setCalDeductRWA_TWD_s24a(BigDecimal value) {
		this.calDeductRWA_TWD_s24a = value;
	}

	/** 取得抵減後風險權數 **/
	public BigDecimal getCalDeductRW_s24a() {
		return this.calDeductRW_s24a;
	}
	/** 設定抵減後風險權數 **/
	public void setCalDeductRW_s24a(BigDecimal value) {
		this.calDeductRW_s24a = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	
	/** 取得特殊融資類別 **/
	public String getSpecialFinRiskType_s24a() {
		return specialFinRiskType_s24a;
	}
	/** 設定特殊融資類別 **/
	public void setSpecialFinRiskType_s24a(String specialFinRiskType_s24a) {
		this.specialFinRiskType_s24a = specialFinRiskType_s24a;
	}
	
}
