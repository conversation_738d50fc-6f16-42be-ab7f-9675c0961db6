package com.mega.eloan.lms.batch.service.impl;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.fms.service.LMS8300Service;
import com.mega.eloan.lms.mfaloan.service.impl.LNLNF013ServiceImpl;
import com.mega.eloan.lms.model.L830M01A;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;


/**
 * J-110-0330
 * 一次性排成，為補齊先前未同步至LNF013之AO帳務管理原資料
 * http://127.0.0.1:9081/lms-web/app/scheduler?input={"serviceId":"lmsBatchAoManagerServiceImpl",request:{runType:"1"}}
 * 
 */
@Service("lmsBatchAoManagerServiceImpl")
public class LmsBatchAoManagerServiceImpl extends AbstractCapService implements
		WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());
	
	@Resource
	SysParameterService sysParamService;

	@Resource
	LMSService lmsService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	LMS8300Service lms8300Service;
	
	@Resource
	LNLNF013ServiceImpl lnLNF013ServiceImpl;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		// @NonTransactional
		JSONObject result = new JSONObject();
		JSONObject request = json.getJSONObject("request");
		StringBuffer errMsg = new StringBuffer("");
		try {	
			String runType = Util.trim(request.getString("runType"));//1.發查 , 2.接收回傳資料
			String startTime = String.valueOf(CapDate.getCurrentTimestamp());
			if("1".equals(runType)){//其實我沒有type也沒差的樣子
				this.updateLNF013ByL830M01A(request, result, errMsg);
			}else {
				result = WebBatchCode.RC_ERROR;
				result.element(WebBatchCode.P_RESPONSE, "LmsBatchAoManagerServiceImpl-doPostRpaIdentBatch執行失敗！==> runType 參數有誤");
			}
			if (Util.notEquals(errMsg.toString(), "")) {
				result = WebBatchCode.RC_ERROR;
				result.element(WebBatchCode.P_RESPONSE, "LmsBatchAoManagerServiceImpl runType: " + runType + "執行失敗！==>" + errMsg);
			} 
			else{	
				result = WebBatchCode.RC_SUCCESS;
				String endTime = String.valueOf(CapDate.getCurrentTimestamp());
				result.element(WebBatchCode.P_RESPONSE, "LmsBatchAoManagerServiceImpl runType: " + runType + "執行成功！(開始時間："+startTime+" , 結束時間："+endTime+")");
			}
		}catch (Exception ex) {
			logger.error(StrUtils.getStackTrace(ex));
			result = WebBatchCode.RC_ERROR;
			String msg = ex.getMessage();
			result.element(WebBatchCode.P_RESPONSE, "clsBatchSuspectedAgentAppCaseServiceImpl 執行失敗！==>" + msg + " - " + ex.getLocalizedMessage());
		}
		return result;
	}

	@NonTransactional
	public String updateLNF013ByL830M01A(JSONObject request, JSONObject result, StringBuffer errMsg) {
		//由於資料很多，為避免系統問題，一家一家分行做
		List<IBranch> list_brno = new LinkedList<IBranch>();
		list_brno = this.getBrNoList("J-110-0330_BRNO");
		if(list_brno != null && list_brno.size()>0){
			for(int i=0;i<list_brno.size();i++){
				IBranch branchInfo = list_brno.get(i);
				String brNo = branchInfo.getBrNo();
				List<L830M01A> L830List = lms8300Service.findL830m01aByBrno(brNo,"SYSTEM");
				if(L830List != null && L830List.size()>0){
					for(int j=0;j<L830List.size();j++){
						//開始處理
						L830M01A l830m01a = L830List.get(j);
						if(l830m01a != null){
							String custId = l830m01a.getCustId();
							String dupNo = l830m01a.getDupNo();
							String custId_dupNo = custId + dupNo;
							//檢查LNF013是否已存在該資料
							List<Map<String, Object>> lnf013List = lnLNF013ServiceImpl.findByUnidAndCustId(brNo, custId_dupNo,
									UtilConstants.Casedoc.DocType.個金);
							if (lnf013List != null && lnf013List.size() > 0) {
								//LNF013已存在資料，更新L830M01A之MEMO欄位註記
								l830m01a.setMemo("LNF013已存在"+ custId_dupNo + "資料");
								lms8300Service.save(l830m01a);
								logger.trace("LNF013已存在"+ custId_dupNo + "資料");
							}else{
								//無資料INSERT進去
								this.gfnInsertLNF013(l830m01a);
							}
						}
					}
				}
			}
		}
		
		return null;
	}
	
	public void gfnInsertLNF013(L830M01A meta) {
		// 產生LNF013資料，並INSERT
		String dateNow = CapDate.formatDate(new Date(), "yyyy-MM-dd");
		String LNF013_BR_NO = meta.getOwnBrId();
		String LNF013_STAFF_NO = meta.getAOId(); // 6碼
		String LNF013_BEG_DATE = dateNow;
		String LNF013_END_DATE = null;
		String LNF013_CUST_ID = meta.getCustId() + meta.getDupNo();
		String LNF013_CRE_DATE = dateNow;
		String LNF013_CRE_TELLER = meta.getCreator();
		String LNF013_CRE_SUPVNO = meta.getApprover(); // e-loan id是6碼，但現行LNF013都只塞5碼
		String LNF013_UPD_DATE = dateNow;
		String LNF013_UPD_TELLER = "";
		String LNF013_UPD_SUPVNO = "";
		String LNF013_DEL_DATE = null;
		String LNF013_DEL_TELLER = "";
		String LNF013_DEL_SUPVNO = "";
		String LNF013_BUS_PER_FG = UtilConstants.Casedoc.DocType.個金;

		lnLNF013ServiceImpl.insertByCls(LNF013_BR_NO, LNF013_STAFF_NO,
						LNF013_BEG_DATE, LNF013_END_DATE, LNF013_CUST_ID,
						LNF013_CRE_DATE, LNF013_CRE_TELLER, LNF013_CRE_SUPVNO,
						LNF013_UPD_DATE, LNF013_UPD_TELLER, LNF013_UPD_SUPVNO,
						LNF013_DEL_DATE, LNF013_DEL_TELLER, LNF013_DEL_SUPVNO,
						LNF013_BUS_PER_FG);
	}
	
	
	private List<IBranch> getBrNoList(String paramStr){
		List<IBranch> list_brno = new LinkedList<IBranch>();
		Map<String, String> map = codeTypeService.findByCodeType("LMS_FUNC_ON_FLAG","zh_TW");
		if (map.containsKey(paramStr)) { //有設定
			String val = Util.trim(map.get(paramStr));
			String[] arrSplit = val.split(",");
		    for (int i=0; i < arrSplit.length; i++)
		    {
		    	System.out.println(arrSplit[i]);
		    	String branchId = Util.trim(arrSplit[i]);
		    	if(!branchId.isEmpty()){
		    		IBranch iBranch = branchService.getBranch(branchId);
		    		if(iBranch != null){
		    			list_brno.add(iBranch);
		    		}
		    	}
		    }
		}else{ //未設定
			list_brno = branchService.getAllBranch();
		}
		return list_brno;
	}
	

	
}
