$(document).ready(function(){

    // 畫面切換table 所需設定之資料 如無設定 則直接切換
    $.extend(window.tempSave, {
        handler: "lms9515m01formhandler",
        action: "tempSave",
        sendData: function(){
            if (responseJSON.page == "01") {
                // 文件資訊
                return $("#L170M01aForm").serializeData();
            } else if (responseJSON.page == "03") {
            
            } else if (responseJSON.page == "04") {
                return $("#L170M01dForm").serializeData();
            } else if (responseJSON.page == "05") {
                return $("#L170M01F2Form").serializeData();
            }
        }
    });
    
    // 權限
    var auth = (responseJSON ? responseJSON.Auth : {});
    // $("#L170M01aForm").readOnlyChilds(auth.readOnly);
    if (auth.readOnly) {
        // $("#tabForm").readOnlyChilds(true)//.find("button").remove();
    }
    // $.log(JSON.stringify(responseJSON)+"1");
    
    setCloseConfirm(true);/* 設定關閉此畫面時要詢問 */
    if (responseJSON.page == "01") {
        $.extend(window.tempSave, {
            handler: "lms1705m01formhandler",
            action: "tempSave",
            beforeCheck: function(){
                return $("#L170M01aForm").valid();// 執行前的檢核條件
            },
            sendData: function(){
                // 所要送至handler的資料
                return $.extend($("#L170M01aForm").serializeData(), {
                    oid: $("#oid").val()
                });
            }
        });
    }
    $.form.init({
        formHandler: "lms9515m01formhandler",
        formPostData: {
            formAction: "queryL748s07a",
            mainId: responseJSON.mainId,
            custId: responseJSON.custId,
            dupNo: responseJSON.dupNo
        },
        
        loadSuccess: function(json){
            if (responseJSON.page == "01") {
            
            }
            if (responseJSON.page == "02") {
            
            }
            if (responseJSON.page == "03") {
                $("#curapplyfor").localSave();
            }
            if (responseJSON.page == "04") {
            
            }
            if (responseJSON.page == "06") {
                if ($("#id").val() != "") 
                    $("#id").readOnly(true);
                else 
                    $("#id").readOnly(false);
            }
        }
    });
    
    // 儲存按鈕
    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
        var k = DOMPurify.sanitize($("#yearTrue").val());
        var i = $("#sum" + k).val();
        var j = $("#sumAmt" + k).val();
        
        $.ajax({
            type: "POST",
            handler: "lms9515m01formhandler",
            action: "saveL784s07a",
            data: {
                // mainId : responseJSON.oid
                mainOid: $("#oid").val(),
                sum: i,
                sumAmt: j
            
            
            },
            success: function(responseData){
                $('#L170M01aForm').injectData(responseData.L170M01aForm);
                
            }
            
        });//AJAX結束
        // 刪除按鈕
    }).end().find("#btnDelete").click(function(){
        $.ajax({
            type: "POST",
            handler: "lms1705m01formhandler",
            data: {
                formAction: "deleteListL170m01a",
                mainOid: $("#oid").val()
            }
        
        });
    }).end().find("#btnRemover").click(function(){
        flowAction($.extend($("#tabForm").serializeData(), {
            page: responseJSON.page,
            saveData: true
        }));
    }).end().find("#btnAccept").click(function(){
        flowAction({
            flowAction: true
        });
    }).end().find("#btnReturn").click(function(){
        flowAction({
            flowAction: false
        });
    }).end().find("#btnPrint").click(function(){
        printAction({
            printAction: false
        });
    }).end().find("#btnSend").click(function(){
        flowAction({
            flowAction: false
        });
    }).end().find("#btnCheck").click(function(){
        flowAction({
            flowAction: false
        });
    }).end().find("#btnExit").click(function(){
        setCloseConfirm(false);
        window.close();
    }).end().find("#btnComplete").click(function(){
        flowAction($.extend($("#tabForm").serializeData(), {
            page: responseJSON.page,
            saveData: true
        }));
    }).end().find("#btnCalc").click(function(){
        var k = DOMPurify.sanitize($('#yearTrue').val());
        
        var i1 = $('#cItem1Rec' + k).val();
        var i2 = $('#cItem2Rec' + k).val();
        var i3 = $('#cItem3Rec' + k).val();
        var i4 = $('#cItem12Rec' + k).val();
        
        var j1 = $('#cItem1Amt' + k).val();
        var j2 = $('#cItem2Amt' + k).val();
        var j3 = $('#cItem3Amt' + k).val();
        var j4 = $('#cItem4Amt' + k).val();
        
        $.ajax({
            type: "POST",
            handler: "lms9515m01formhandler",
            data: {
                formAction: "sum784s07",
                i1: i1,
                i2: i2,
                i3: i3,
                i4: i4,
                j1: j1,
                j2: j2,
                j3: j3,
                j4: j1
            },
            success: function(responseData){
                $('#sum' + k).html(DOMPurify.sanitize(responseData.sum));
                $('#sumAmt' + k).html(DOMPurify.sanitize(responseData.sumAmt));
            }
            
        });
        
    });
    
    function flowAction(sendData){
        $.ajax({
            type: "POST",
            handler: "lms1705m01formhandler",
            data: $.extend({
                formAction: "flowAction",
                mainOid: $("#oid").val(),
                txCode: responseJSON.txCode
            }, (sendData || {})),
            success: function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                setCloseConfirm(false);
                window.close();
            }
        });
    }
    
    function printAction(sendData){
    	/*
        $.ajax({
            type: "POST",
            handler: "sap170m01formhandler",
            data: $.extend({
                formAction: "printAction",
                mainOid: $("#oid").val()
            }, (sendData || {})),
            success: function(response){
                window.open(response.url + "?fn=" + response.docId + ".pdf");
                alert('got response=' + response.url);
            },
            error: function(XMLHttpRequest, textStatus, errorThrown){
                //printError=產生報表錯誤!!
                CommonAPI.showMessage(i18n.def['printError']);
            }
        });
        */
    }
});
