/*
先在 CLS1131S06Page.java 中指定 js , 該 Java 掛在 path = "/cls/cls1131s06"

然後在 
	●CLS1131S01Page.js 個金徵信
	●CLS1161M02Page.js 中鋼整批
	
	程式碼區塊 
		$('#adjustNotHouseLoanSheet').load(webroot + '/app/cls/cls1131s06', function(){
	去載入頁面
*/
var AdjustNotHouseLoanAction = {
	//有可能 handler 為 cls1161m02formhandler
	handler : 'cls1131formhandler',
	data : {},
	ready : false,
	adjust : false,
	manual : false,
	/**
	 * 初始化
	 */
	init : function() {
		$('#adjustNotHouseLoanForm').setValue(); // reset
		AdjustNotHouseLoanAction.adjust = false;
		AdjustNotHouseLoanAction.manual = false;
	},
	/**
	 * 建置
	 */
	build : function() {
		var $form = $('#adjustNotHouseLoanForm');
		$form.find('input[name=adjustStatus]').click(function() {
			var $fm = $('#adjustNotHouseLoanForm');
			$fm.find('#adjustNotHouseLoanFlagDiv').hide();
			$fm.find('#adjustNotHouseLoanFlagSpan').html('理由為');
			$fm.find('#grade2').val('');
			$fm.find('#grade2').addClass('required').attr('disabled', false);

			//手動時清除資料
			if (AdjustNotHouseLoanAction.manual){
				//$form.serializeData();
				$fm.find('input[name=adjustFlag]').attr('checked', false);
				$fm.find('#adjustReason').val('');
			}

			//addClass required add by fantasy 2013/07/09
			if (!$fm.find('#adjustReason').hasClass('required'))
				$fm.find('#adjustReason').addClass('required');
			
			switch ($(this).val()+''){
				case '1': //升等
					$fm.find('#adjustNotHouseLoanFlagDiv').show();
					$fm.find('#grade2').show();
					$fm.find('#adjustNotHouseLoanFlagSpan').html('勾選升等主要理由，並詳敘升等理由(如相關佐證等)');
					break;
				case '2': //降等
					break;
				case '3': //回復
					$fm.find('#grade2').removeClass('required').attr('disabled', true).val('');
					$fm.find('#adjustReason').removeClass('required');
					break;
			}

			AdjustNotHouseLoanAction.compute();
		});
		$form.find('input[name=adjustFlag]').click(function() {
				switch ($(this).val() + '') {
				case '1': // 淨資產
					$('#adjustNotHouseLoanFlagSpan1').html('淨資產');
					$('#adjustNotHouseLoanFlagSpan2').html(
							'(請以具體數據文字敘明，如年(月)所得金額、存款金額、租金收入金額、其他資產多寡或負債百分比等)');
					break;
				case '2': // 職業
					$('#adjustNotHouseLoanFlagSpan1').html('職業');
					$('#adjustNotHouseLoanFlagSpan2')
							.html('(請詳述，如任職公司職稱、規模、職業類別、年資、擔任公司負責人或工作前景等)');
					break;
				case '3': // 其它
					$('#adjustNotHouseLoanFlagSpan1').html('其它');
					$('#adjustNotHouseLoanFlagSpan2').html('(如與銀行的往來關係、對客戶之綜合評述或非屬前兩類之理由等)');
					break;
				}
				
			  //手動時清除資料
				if (AdjustNotHouseLoanAction.manual){
					$('#adjustNotHouseLoanForm').find('#adjustReason').val('');
				}
		});
		$form.find('#grade2').change(function() {
			AdjustNotHouseLoanAction.compute();
		});
		return true;
	},
	/**
	 * 開啟
	 */
	open : function(data) {
		if (!AdjustNotHouseLoanAction.ready)
			AdjustNotHouseLoanAction.ready = AdjustNotHouseLoanAction.build();
		// 初始化
		AdjustNotHouseLoanAction.init();
		// set data
		AdjustNotHouseLoanAction.data = $.extend(data || {}, {
			noOpenDoc : true,
			'markModel':'2'
		});
		// load data
		AdjustNotHouseLoanAction.load();
	},
	openThinkBox : function() {
		$('#adjustNotHouseLoanThickBox').thickbox({
			title : '調整非房貸申請信用評等',
			width : 800,
			height : 450,
			modal : true,
			align : 'center',
			valign : 'bottom',
			i18n : i18n.def,
			buttons : {
				'sure' : function() {
					if ($('#adjustNotHouseLoanForm').valid()) {
						AdjustNotHouseLoanAction.save();
					}
				},
				'cancel' : function() {
					$.thickbox.close();
				}
			}
		});
	},
	openReadOnlyThinkBox : function() {
		$('#adjustNotHouseLoanForm').readOnlyChilds();
		
		$('#adjustNotHouseLoanThickBox').thickbox({
			title : '調整非房貸申請信用評等',
			width : 800,
			height : 450,
			modal : true,
			align : 'center',
			valign : 'bottom',
			i18n : i18n.def,
			buttons : {
				'cancel' : function() {
					$.thickbox.close();
				}
			}
		});
	},
	/**
	 * 讀取資料
	 */
	load : function() {
		if (!$.isEmptyObject(AdjustNotHouseLoanAction.data)) {
			$.ajax({
				handler : AdjustNotHouseLoanAction.handler,
				action : 'loadAdjust',
				formId : 'empty', //
				data : AdjustNotHouseLoanAction.data,
				success : function(response) {
					if (response.adjustMsg) {
						// showErrorMessage showPopMessage
						MegaApi.showErrorMessage(i18n.def["confirmTitle"],
								response.adjustMsg);
					} else {
						AdjustNotHouseLoanAction.adjust = (response.adjust ? true : false);
						$('#adjustNotHouseLoanForm').setValue(response.adjustNotHouseLoanForm);

						if(DOMPurify.sanitize(response.fromC120M01A)=="Y"){
							$(".area_cls1131s06page_c101").hide();
							$(".area_cls1131s06page_c120").show();
							AdjustNotHouseLoanAction.openReadOnlyThinkBox();
						}else{
							$(".area_cls1131s06page_c101").show();
							$(".area_cls1131s06page_c120").hide();
							if(response.lockAdjust=="Y"){
								AdjustNotHouseLoanAction.openReadOnlyThinkBox();
							}else{
								AdjustNotHouseLoanAction.openThinkBox();
							}							
						}
					}
					AdjustNotHouseLoanAction.manual = true;
				}
			});
		}
	},
	/**
	 * 儲存
	 */
	save : function() {
		if (!$.isEmptyObject(AdjustNotHouseLoanAction.data) && AdjustNotHouseLoanAction.checkGrade()) {
			$.ajax({handler : "lms1015m01formhandler",action : 'validateAdjustReason',				
				data : {'keyStr':'', 'mowType': 'N', 'adjustReason': $("#adjustNotHouseLoanForm").find("#adjustReason").val()},
				success : function(json) {
					procCfmMsg(json.adjRsnFmt_cfmObj).done(function(){
		        		alwaysConfirmAdjReason(json.adjRsnFmt_cnt
		        				, json.adjRsnFmt_alwaysCfmObj).done(function(){
		        			
	        					$.ajax({
	        						handler : AdjustNotHouseLoanAction.handler,
	        						action : 'saveAdjust',
	        						formId : 'adjustNotHouseLoanForm', //
	        						data : AdjustNotHouseLoanAction.data,
	        						success : function(response) {
	        							$('#gradeDiv_markModel_2').setValue(response.gradeDiv_markModel_2);
	        							$.thickbox.close();
	        							MegaApi.showPopMessage(i18n.def["confirmTitle"],
	        									i18n.def["runSuccess"]);
	        						
	        							if(true){
	        								//若在 中鋼整批 去升降等，也要 reloadGrid
	        								if(AdjustNotHouseLoanAction.data.useC120Batch=='Y'){
	        									ilog.debug("refresh grid");
	        									$("#gridview").trigger("reloadGrid");	
	        								}							
	        							}
	        						}
	        					});
		        		});
		        	});
				}
			});
		}
	},
	/**
	 * 計算最終評等
	 */
	compute : function() {
		var $form = $('#adjustNotHouseLoanForm');
		var value = parseInt($form.find('#grade1').html() || '0');
		$form.find('input[name=adjustStatus]:checked').each(function() {
			switch ($(this).val() + '') {
			case '1': // 調升
				value -= parseInt($form.find('#grade2').val() || '0');
				break;
			case '2': // 調降
				value += parseInt($form.find('#grade2').val() || '0');
				break;
			}
		});
		$form.find('#grade3').html(value);
		//AdjustNotHouseLoanAction.checkGrade();
	},
	/**
	 * 最終評等檢核
	 */
	checkGrade : function(){
		var $form = $('#adjustNotHouseLoanForm');
		var grade1 = parseInt($form.find('#grade1').html() || '0');
		var grade3 = parseInt($form.find('#grade3').html() || '0');
		if (grade3 >= 11 || grade3 <= 0) {
			MegaApi.showErrorMessage(i18n.def['confirmTitle'],'最終評等應介於1~10等');
			return false;
		}
		var adjustStatus = $form.find('input[name=adjustStatus]:checked').val()+'';
		if (adjustStatus === '1'){
			var grade = grade1 - grade3;
			var v = AdjustNotHouseLoanAction.adjust ? 3 : 2;
			if (grade > v){
				MegaApi.showErrorMessage(i18n.def['confirmTitle'],'調升不可超過'+v+'等');
				return false;
			}
		}
		return true;
	}
}