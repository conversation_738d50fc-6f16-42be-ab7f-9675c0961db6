package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;

import org.apache.wicket.markup.html.form.Check;

import tw.com.iisi.cap.model.GenericBean;

/** 覆審明細檔 **/
public class ELF492 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/**
	 * 分行代號
	 */
	@Column(name = "ELF492_BRANCH", length = 3, columnDefinition = "CHAR(3)", nullable = false, unique = true)
	private String elf492_branch;

	/**
	 * 借款人統一編號
	 */
	@Column(name = "ELF492_CUSTID", length = 10, columnDefinition = "CHAR(10)", nullable = false, unique = true)
	private String elf492_custid;

	/**
	 * 重複序號
	 */
	@Column(name = "ELF492_DUPNO", length = 1, columnDefinition = "CHAR(1)", nullable = false, unique = true)
	private String elf492_dupno;

	/**
	 * 額度序號
	 */
	@Column(name = "ELF492_CNTRNO", length = 12, columnDefinition = "CHAR(12)", nullable = false, unique = true)
	private String elf492_cntrno;

	/**
	 * 本次複審日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF492_LRDATE", columnDefinition = "DATE", nullable = false, unique = true)
	private Date elf492_lrdate;

	/**
	 * 主要授信戶
	 */
	@Column(name = "ELF492_MAINCUST", length = 1, columnDefinition = "CHAR(1)")
	private String elf492_maincust;

	/**
	 * 授信期間起日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF492_DURBEG", columnDefinition = "DATE")
	private Date elf492_durbeg;

	/**
	 * 授信期間止日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF492_DUREND", columnDefinition = "DATE")
	private Date elf492_durend;

	/**
	 * 動用起日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF492_BEG", columnDefinition = "DATE")
	private Date elf492_beg;

	/**
	 * 動用止日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF492_END", columnDefinition = "DATE")
	private Date elf492_end;

	/**
	 * 等值台幣額度
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF492_QUOTA", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf492_quota;

	/**
	 * 產品種類
	 */
	@Column(name = "ELF492_LNTYPE", length = 100, columnDefinition = "CHAR(100)")
	private String elf492_lntype;

	/**
	 * 科子目
	 */
	@Column(name = "ELF492_LOANTP", length = 100, columnDefinition = "CHAR(100)")
	private String elf492_loantp;

	/**
	 * 覆審類別
	 */
	@Column(name = "ELF492_KIND", length = 100, columnDefinition = "CHAR(100)")
	private String elf492_kind;

	/**
	 * 銷戶日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF492_CANCELDT", columnDefinition = "DATE")
	private Date elf492_canceldt;

	/**
	 * 資料修改人
	 */
	@Column(name = "ELF492_UPDATER", length = 8, columnDefinition = "CHAR(8)")
	private String elf492_updater;

	/**
	 * 資料更新日
	 */
	@Column(name = "ELF492_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf492_tmestamp;

	/**
	 * 主管機關指定覆審案件
	 */
	@Column(name = "ELF492_UCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elf492_uckdline;

	/**
	 * 主管機關通知日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF492_UCKDDT", columnDefinition = "DATE")
	private Date elf492_uckddt;

	/**
	 * 上次土建融實地覆審日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF492_LASTREALDT", columnDefinition = "DATE")
	private Date elf492_lastRealDt;

	/**
	 * 土建融實地覆審註記{elf412_realCkFg:Y/N}
	 */
	@Column(name = "ELF492_REALCKFG", length = 1, columnDefinition = "CHAR(1)")
	private String elf492_realCkFg;

	/** 文件編號UNID */
	@Column(name = "ELF492_UNID", length = 32, columnDefinition = "CHAR(32)")
	private String elf492_unid;

	/**
	 * 覆審經辦
	 */
	@Column(name = "ELF492_APPRID", length = 8, columnDefinition = "CHAR(8)")
	private String elf492_apprId;

	public String getElf492_branch() {
		return elf492_branch;
	}

	public void setElf492_branch(String elf492_branch) {
		this.elf492_branch = elf492_branch;
	}

	public String getElf492_custid() {
		return elf492_custid;
	}

	public void setElf492_custid(String elf492_custid) {
		this.elf492_custid = elf492_custid;
	}

	public String getElf492_dupno() {
		return elf492_dupno;
	}

	public void setElf492_dupno(String elf492_dupno) {
		this.elf492_dupno = elf492_dupno;
	}

	public String getElf492_cntrno() {
		return elf492_cntrno;
	}

	public void setElf492_cntrno(String elf492_cntrno) {
		this.elf492_cntrno = elf492_cntrno;
	}

	public Date getElf492_lrdate() {
		return elf492_lrdate;
	}

	public void setElf492_lrdate(Date elf492_lrdate) {
		this.elf492_lrdate = elf492_lrdate;
	}

	public String getElf492_maincust() {
		return elf492_maincust;
	}

	public void setElf492_maincust(String elf492_maincust) {
		this.elf492_maincust = elf492_maincust;
	}

	public Date getElf492_durbeg() {
		return elf492_durbeg;
	}

	public void setElf492_durbeg(Date elf492_durbeg) {
		this.elf492_durbeg = elf492_durbeg;
	}

	public Date getElf492_durend() {
		return elf492_durend;
	}

	public void setElf492_durend(Date elf492_durend) {
		this.elf492_durend = elf492_durend;
	}

	public Date getElf492_beg() {
		return elf492_beg;
	}

	public void setElf492_beg(Date elf492_beg) {
		this.elf492_beg = elf492_beg;
	}

	public Date getElf492_end() {
		return elf492_end;
	}

	public void setElf492_end(Date elf492_end) {
		this.elf492_end = elf492_end;
	}

	public BigDecimal getElf492_quota() {
		return elf492_quota;
	}

	public void setElf492_quota(BigDecimal elf492_quota) {
		this.elf492_quota = elf492_quota;
	}

	public String getElf492_lntype() {
		return elf492_lntype;
	}

	public void setElf492_lntype(String elf492_lntype) {
		this.elf492_lntype = elf492_lntype;
	}

	public String getElf492_loantp() {
		return elf492_loantp;
	}

	public void setElf492_loantp(String elf492_loantp) {
		this.elf492_loantp = elf492_loantp;
	}

	public String getElf492_kind() {
		return elf492_kind;
	}

	public void setElf492_kind(String elf492_kind) {
		this.elf492_kind = elf492_kind;
	}

	public Date getElf492_canceldt() {
		return elf492_canceldt;
	}

	public void setElf492_canceldt(Date elf492_canceldt) {
		this.elf492_canceldt = elf492_canceldt;
	}

	public String getElf492_updater() {
		return elf492_updater;
	}

	public void setElf492_updater(String elf492_updater) {
		this.elf492_updater = elf492_updater;
	}

	public Timestamp getElf492_tmestamp() {
		return elf492_tmestamp;
	}

	public void setElf492_tmestamp(Timestamp elf492_tmestamp) {
		this.elf492_tmestamp = elf492_tmestamp;
	}

	public String getElf492_uckdline() {
		return elf492_uckdline;
	}

	public void setElf492_uckdline(String elf492_uckdline) {
		this.elf492_uckdline = elf492_uckdline;
	}

	public Date getElf492_uckddt() {
		return elf492_uckddt;
	}

	public void setElf492_uckddt(Date elf492_uckddt) {
		this.elf492_uckddt = elf492_uckddt;
	}

	public Date getElf492_lastRealDt() {
		return elf492_lastRealDt;
	}

	public void setElf492_lastRealDt(Date elf492_lastRealDt) {
		this.elf492_lastRealDt = elf492_lastRealDt;
	}

	public String getElf492_realCkFg() {
		return elf492_realCkFg;
	}

	public void setElf492_realCkFg(String elf492_realCkFg) {
		this.elf492_realCkFg = elf492_realCkFg;
	}

	public String getElf492_unid() {
		return elf492_unid;
	}

	public void setElf492_unid(String elf492_unid) {
		this.elf492_unid = elf492_unid;
	}

	public void setElf492_apprId(String elf492_apprId) {
		this.elf492_apprId = elf492_apprId;
	}

	public String getElf492_apprId() {
		return elf492_apprId;
	}

}
