var initDfd = initDfd || $.Deferred();
var _handler = "cls3501m01formhandler";

var result_s = CommonAPI.loadCombos(["HaveNo", "YesNo", "counties"]);

var Action = {
	_isLoad: false,
	FormData: {},
	_initForm: function(){
        $.form.init({
            formHandler:_handler,
            formAction:'queryC124M01A',
            loadSuccess:function(json){
                $('body').injectData(json);
                Action.FormData = json;
                Action._initData();
				if (checkReadonly()) {
				      $("form").readOnlyChilds();
					_openerLockDoc="1";
				}
            }
        });
    },
    initUser : function(){
        $.ajax({
            handler: "codetypehandler",
            action: "userByBranch",
            async: false,
		}).done(function(json){
			$("#importerNo").setItems({
			    item: json,
			    format: "{value}-{key}",
			    fn: function(k, v){
			        var text = $("#importerNo option:selected").html();
			        var splitText = text.split("-");
			        var no = splitText[0];
			        var name = splitText[1];
			        $("#importerName").val(name);
			    }
			});
		})
    },
    _initEvent: function(){
        $("#city").setItems({
            item: result_s.counties,
            format: "{key}",
            fn: function(k, v){
                Action.changCityValue($("#city"), $("#dist"));
            }
        });
        $("#dist").change(function(k, v){
            if ($("#dist").val() != "") {
                $("#zip").val($("#dist").val());
            }
        });
        $("#isEmail").setItems({
            item: result_s.HaveNo,
            format: "{key}"
        });
        $(".notUse").find("input[type='radio']").prop("disabled", true);
        Action._initForm();
    },
    _initData: function(){
        var obj = Action.FormData;
        $("input[name='isEmail'][value='" + obj.isEmail + "']").prop("checked", true);
        Action.changCityValue($("#city"), $("#dist"));
    },
	_init: function(){
        if (!this._isLoad) {
            this.initUser();
            this._initEvent();
            this._isLoad = true;
        } else {
            this._reloadGrid();
        }
    },
    changCityValue: function(tCity, tArea){
        var value = tCity.val();
        var combos = CommonAPI.loadCombos('counties' + value);

        tArea.setItems({
            item: combos['counties' + value],
            format: '{key}',
            value: Action.FormData['dist'] || "",
        });
    }
}

// 驗證readOnly狀態
function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
        return true;
    }
    return false;
}


$(function(){
	var item;
	var tabForm = $("#mainPanel");
	
	Action._init();
    $("#check1").show();


	var btnPanel = $("#buttonPanel");
	var initControl_lockDoc = false;

	btnPanel.find("#btnSave").click(function(){
        saveData(true);
	}).end().find("#btnPrint").click(function(){
        if (checkReadonly()) {
            printAction();
        }
        else {
            // saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作?
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    saveData(false, printAction);
                }
            });
        }
	}).end().find("#btnSend").click(function(){
	    saveData(false, sendBoss);
	}).end().find("#btnCheck").click(function(){
	    openCheck();
	});

	$("#numPerson").change(function(){
        $('#bossItem').empty();
        var value = $(this).val();
        if (value) {
            var html = '';
            for (var i = 1; i <= value; i++) {
                var name = 'boss' + i;
                html += i + '. '
                // || '授信主管'
                html += '<select id="' + name + '" name="boss"' +
                '" class="required" CommonManager="kind:2;type:2" ></select>';
                html += '<br/>';
            }
            $('#bossItem').append(html).find('select').each(function(){
                $(this).setItems({
                    item: item,
                    format: "{value} {key}"
                });
            });
        }
    });
	
	
	// 儲存的動作
	function saveData(showMsg, tofn){

	    // 為檢查UI的值是否皆無異常
	    if ($("#mainPanel").valid() == false) {
	        return;
	    }

	    $.ajax({
	        handler: _handler,
	        data: $.extend($("#mainPanel").serializeData(), {// 把資料轉成json
	                formAction: "saveMain",
	                oid: responseJSON.oid,
	                showMsg: showMsg
	            }),
			}).done(function(obj){
				$('body').injectData(obj);
	           	// 執行列印
	           	if (!showMsg && tofn) {
	               tofn();
	            }
			});
	}
	
	// 呈主管 - 編製中
	function sendBoss(){
	    $.ajax({
	        handler: _handler,
	        action: "getBossList",
	        data: {},
		}).done(function(json){
			$('#managerItem').empty();
			$('#bossItem').empty();
			item = json.bossList;
			var bhtml = '1. <select id="boss1" name="boss" class="required" CommonManager="kind:2;type:2"></select>';
			$('#bossItem').append(bhtml).find('select').each(function(){
			    $(this).setItems({
			        item: item,
			        format: "{value} {key}"
			    });
			});
			var html = '<select id="manager" name="manager" class="required" CommonManager="kind:2;type:2"></select>';
			$('#managerItem').append(html).find('select').each(function(){
			    $(this).setItems({
			        item: item,
			        format: "{value} {key}"
			    });
			});

			// 是否呈主管覆核？
			CommonAPI.confirmMessage(i18n.cls3501m01["C124M01B.message01"], function(b){
			    if (b) {
			        $("#selectBossBox").thickbox({
			            // 覆核
			            title: i18n.cls3501m01['approve'],
			            width: 500,
			            height: 300,
			            modal: true,
			            readOnly: false,
			            valign: "bottom",
			            align: "center",
			            i18n: i18n.def,
			            buttons: {
			                "sure": function(){

			                    var selectBoss = $("select[name^=boss]").map(function(){
			                        return $(this).val();
			                    }).toArray();

			                    for (var i in selectBoss) {
			                        if (selectBoss[i] == "") {
			                            // 請選擇授信主管
			                            return CommonAPI.showErrorMessage(i18n.cls3501m01['checkSelect'] +
			                            i18n.cls3501m01['C124M01B.bossId']);
			                        }
			                    }
			                    if ($("#manager").val() == "") {
			                        // 請選擇經副襄理
			                        return CommonAPI.showErrorMessage(i18n.cls3501m01['checkSelect'] +
			                        i18n.cls3501m01['C124M01B.managerId']);
			                    }
			                    // 驗證是否有重複的主管
			                    if (checkArrayRepeat(selectBoss)) {
			                        // 主管人員名單重複請重新選擇
			                        return CommonAPI.showErrorMessage(i18n.cls3501m01['C124M01B.message02']);
			                    }

			                    flowAction({
			                        page: responseJSON.page,
			                        saveData: true,
			                        selectBoss: selectBoss,
			                        manager: $("#manager").val()
			                    });
			                    $.thickbox.close();

			                },

			                "cancel": function(){
			                    $.thickbox.close();
			                }
			            }
			        });
			    }
			});
		});
	}
	
	function flowAction(sendData){
	    $.ajax({
	        handler: _handler,
	        data: $.extend({
	            formAction: "flowAction",
	            mainOid: responseJSON.mainOid//$("#mainOid").val()
	        }, (sendData || {})),
		}).done(function(){
			CommonAPI.triggerOpener("gridview", "reloadGrid");
			window.close();
		});
	}
	
	// 檢查陣列內容是否重複
	function checkArrayRepeat(arrVal){
	    var newArray = [];
	    for (var i = arrVal.length; i--;) {
	        var val = arrVal[i];
	        if ($.inArray(val, newArray) == -1) {
	            newArray.push(val);
	        }
	        else {
	            return true;
	        }
	    }
	    return false;
	}
	
	// 待覆核 - 覆核
	function openCheck(){
	    $("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.cls3501m01['approve'],
	        width: 100,
	        height: 100,
	        modal: true,
	        readOnly: false,
	        valign: "bottom",
	        align: "center",
	        i18n: i18n.def,
	        buttons: {
	            "sure": function(){
	                var val = $("[name=checkRadio]:checked").val();
	                if (!val) {
	                    return CommonAPI.showMessage(i18n.cls3501m01['checkSelect']);
	                }
	                $.thickbox.close();
	                switch (val) {
	                    case "1":
	                        // 一般退回到編製中01O
	                        // 該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
	                        CommonAPI.confirmMessage(i18n.cls3501m01['C124M01B.message03'], function(b){
	                            if (b) {
	                                flowAction({
	                                    flowAction: false
	                                });
	                            }
	                        });
	                        break;
	                    case "3":
	                        // 該案件是否確定執行核定作業
	                        CommonAPI.confirmMessage(i18n.cls3501m01['C124M01B.message04'], function(b){
	                            if (b) {
	                                flowAction({
	                                    flowAction: true,
	                                    checkDate: CommonAPI.getToday()//forCheckDate
	                                });
	                            }
	                        });
	                        break;
	                }
	            },
	            "cancel": function(){
	                $.thickbox.close();
	            }
	        }
	    });
	}
	
	function printAction(){
	    $.form.submit({
	        url: "../../simple/FileProcessingService",
	        target: "_blank",
	        data: {
	            mainId: $("#mainId").val(),//responseJSON.mainId,
	            mainOid: responseJSON.oid,
	            fileDownloadName: "cls3501r01.pdf",
	            serviceName: "cls3501r01rptservice"
	        }
	    });
	}
});

