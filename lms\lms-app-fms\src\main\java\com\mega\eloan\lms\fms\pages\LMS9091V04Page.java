package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;

/**
 * <pre>
 * 以房養老搭配額度型試算
 * </pre>
 * 
 * @since 2020/09/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/09/21,Benrison
 *          </ul>
 */
@Controller@RequestMapping(path = "/fms/lms9091v04")
public class LMS9091V04Page extends AbstractEloanInnerView {

	public LMS9091V04Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		// UPGRADE: 後續沒有用到就可以刪掉
		// add(new Label("_buttonPanel"));
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS9091V04Page.js" };
	}
}
