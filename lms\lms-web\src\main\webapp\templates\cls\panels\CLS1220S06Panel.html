<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <script type="text/javascript">loadScript('pagejs/cls/CLS1220S06Panel');</script>
			<!--======================================================-->
            <table class="tb2" width="100%">
            	<tr>
					<td class="hd2" align="right" ><th:block th:text="#{'C122M01A.IncomType'}">進件類型</th:block>&nbsp;&nbsp;</td>
					<td colspan="3" ><span id="IncomTypeStr" class="field" ></span>&nbsp;</td>
				</tr>
				<tr class="docCode5Hide" style='vertical-align:top;' >
                    <td class="hd1">
                    	<span class="text-red">＊</span>
						<th:block th:text="#{'C122M01A.IntroduceSrc'}">引介來源</th:block>
                    </td>
                    <td>
                    	<select name="introduceSrc" id="introduceSrc" comboKey="L140M01A_introductionSource" comboType="2" space="true"></select> 
                    	<!-- <select name="introduceSrc" id="introduceSrc" itemType="L140M01A_introductionSource" ></select> -->
						<div id="employeeDiv" style="display:none;">
							<table>
								<tr>
									<td class="hd1">
										<th:block th:text="#{'C122M01A.megaEmpno'}">引介行員代號</th:block>
									</td>
									<td width="40%">
										<input type="text" id="megaEmpNo" name="megaEmpNo" maxlength="6" size="6" class='numText '
												onblur="if($.trim($(this).val()).length > 0){var var_megaEmpNo = '000000'+$(this).val();$(this).val(var_megaEmpNo.substr(var_megaEmpNo.length-6, 6));}" />
									</td>
								</tr>
	                	 	</table>
						</div>
						<div id="realEstateAgentDiv" style="display:none;">
						<button type="button" id="importRealEstateAgentInfo"><th:block th:text="#{'C122M01A.importRealEstateAgentInfo'}">引進房仲資訊</th:block></button>
                	 		<table>
                	 			<tr>
                	 				<td class="hd1" nowrap><th:block th:text="#{'C122M01A.agntNo'}">引介房仲代號</th:block></td>
									<td><input type="text" id="agntNo" name="agntNo"/></td>
								</tr>
								<tr>
									<td class="hd1" ><th:block th:text="#{'C122M01A.agntChain'}">引介房仲連鎖店類型</th:block></td>
									<td><input type="text" id="agntChain" name="agntChain"/></td>
								</tr>
								<tr>
									<td class="hd1" ><th:block th:text="#{'C122M01A.dealContractNo'}">買賣合約書編號</th:block></td>
									<td><input type="text" id="dealContractNo" name="dealContractNo"/></td>
								</tr>
	                	 	</table>
						</div>
						<div id="megaSubCompanyDiv" style="display:none;">
						<table>
							<tr class="docCode5Hide" >
								<td class="hd1" nowrap><th:block th:text="#{'C122M01A.megaCode'}">引介子公司代號</th:block>
								</td>
								<td>
									<select name="megaCode" id="megaCode" comboKey="L140S02A_megaCode" comboType="2" space="true" class="required"></select> 
									<!--<select id="megaCode" name="megaCode" itemType="L140S02A_megaCode" ></select>-->
								</td>
							</tr>
							<tr class="docCode5Hide" >
								<td class="hd1"><th:block th:text="#{'C122M01A.subUnitNo'}">引介子公司分支代號</th:block>
								</td>
								<td>
									<select id="subUnitNo" name="subUnitNo"  ></select> <!-- J-107-0136 -->
									</td>
							</tr>
							<tr class="docCode5Hide" >
								<td class="hd1"  ><th:block th:text="#{'C122M01A.subEmpNo'}">引介子公司員工編號</th:block>
								</td>
								<td><input type="text" id="subEmpNo" name="subEmpNo" maxlength="6" size="6" class=' '
										onblur="if($.trim($(this).val()).length > 0 && (new RegExp('[a-zA-Z]').test($(this).val())==false)){var var_subEmpNo = '000000'+$(this).val();$(this).val(var_subEmpNo.substr(var_subEmpNo.length-6, 6));}" />
				                </td>
							</tr>
							<tr class="docCode5Hide" >
								<td class="hd1"  ><th:block th:text="#{'C122M01A.subEmpNm'}">引介子公司員工姓名</th:block>
								</td>
								<td><input type="text" id="subEmpNm" name="subEmpNm"  maxlength="10" maxlengthC="10" size="18" />
				                </td>
				            </tr>
						</table>
						</div>
						
						<div id="laaDiv" style="display:none;">
							<table>
								<tr>
									<td>
										<button type="button" id="addLaa">
											<span class="text-only"><th:block th:text="#{'button.add'}">新增</th:block></span>
										</button>
										<button type="button" id="deleteLaa">
											<span class="text-only"><th:block th:text="#{'button.del'}">刪除</th:block></span>
										</button>
										<div id="laaListGrid" style="width:100%;margin-left:0px;margin-right:0px"></div>
									</td>
								</tr>
	                	 	</table>
						</div>
						
						<div id="customerOrCompanyDiv" style="display:none;">
							<button type="button" id="importCustOrComButton"><th:block th:text="#{'C122M01A.importCustomerOrCompanyInfo'}">引進客戶/企業資訊</th:block></button>
                	 	<table>
                	 		<tr>
                	 			<td class="hd1" nowrap><th:block th:text="#{'C122M01A.introCustId'}">客戶ID/企業統編</th:block></td>
								<td>
									<input type="text" id="introCustId" name="introCustId" size="8"/>
									<input type="text" id="introDupNo" name="introDupNo" size="2"/>
								</td>
							</tr>
							<tr>
								<td class="hd1" ><th:block th:text="#{'C122M01A.introCustName'}">客戶名稱/企業名稱</th:block></td>
								<td><input type="text" id="introCustName" name="introCustName"/></td>
							</tr>
						</table>
						</div>
						<div id="batchCodeSbrDiv" style="display:none;">
						<table>
							<tr class="docCode5Hide" >
								<td class="hd1"  ><th:block th:text="#{'C122M01A.batchCodeSbr'}">整批批號</th:block></td>
								<td>
									<input type="text" id="batchCodeSbr" name="batchCodeSbr" maxlength="90" size="30" maxlengthC="30"/>
				                </td>
							</tr>
	                	</table>
						</div>
						
						<div id="buildCaseDiv" style="display:none;">
						<table>
							<tr class="docCode5Hide" >
								<td class="hd1"  ><th:block th:text="#{'C122M01A.builderName'}">建商名稱</th:block></td>
								<td>
									<input type="text" id="introBuildDerName" name="introBuildDerName" maxlength="90" size="30" maxlengthC="30"/>
				                </td>
							</tr>
							<tr class="docCode5Hide" >
								<td class="hd1"  ><th:block th:text="#{'C122M01A.biuldCaseName'}">建案名稱</th:block></td>
				                <td>
				                	<input type="text" id="introBuildCaseName" name="introBuildCaseName" maxlength="90" maxlengthC="30" size="30" />
				                </td>
				           </tr>
	                	</table>
						</div>
					</td>
				</tr>
				
				<tr>
					<td class="hd1">
                    	<span class="text-red">＊</span>
						<th:block th:text="#{'C122M01A.estFlag'}">有無擔保品</th:block>
                    </td>
					<td>
                    	<select name="estFlag" id="estFlag" comboKey="C122M01A_estFlag" comboType="2" space="true"></select> 
						<!-- 指定收件行行員 -->
						<div id="evaMegaEmpView" >
							<p>
								<table>
									<select id="evaMegaEmp" name="evaMegaEmp" space="true" class="editable" disabled="true"></select> 
								</table>
							</p>
						</div>
						
						<!-- 委外估價 -->
						<div id="estUnitView" >
							<p>
								<table>
                              		<td class="hd1" ><!-- hd1的 width 15% -->
                              			<th:block th:text="#{'C122M01F.estUnitName'}"><!--委外估價單位名稱--></th:block>
									</td>
									<td>
										<input type="text" id="estUnitName" name="estUnitName" readonly="true"/>
									</td>
								</table>
							</p>
						</div>
						
						<!-- 擔保品坐落位置 -->
						<div id="reEstView" >
								<p>
									<select name="estAddressCity" id="estAddressCity" comboKey="counties" comboType="2" space="true" class="editable"></select>, 
			                        <select id="estAddressArea" name="estAddressArea"></select>,
									<br/>
									<th:block th:text="#{'C122M01A.common.01'}">路/街/大道：</th:block>
									<input type="text" id="estAddressVillage" name="estAddressVillage" size="10"/>
									<select name="estAddressStreet" id="estAddressStreet" comboKey="cms1010_road" comboType="2" space="true" class="editable"></select>, 
									<input type="text" id="estAddressSection" name="estAddressSection" size="3"/><th:block th:text="#{'C122M01A.common.02'}">段</th:block>,
									<input type="text" id="estAddressLane" name="estAddressLane" size="3"/><th:block th:text="#{'C122M01A.common.03'}">巷</th:block>,
									<input type="text" id="estAddressAlley" name="estAddressAlley" size="3"/><th:block th:text="#{'C122M01A.common.04'}">弄</th:block>,
									<input type="text" id="estAddressNo" name="estAddressNo" size="5"/><th:block th:text="#{'C122M01A.common.05'}">號</th:block>,
									<input type="text" id="estAddressFloor" name="estAddressFloor" size="3"/><th:block th:text="#{'C122M01A.common.06'}">樓</th:block><th:block th:text="#{'C122M01A.common.07'}">之</th:block>
									<input type="text" id="estAddressLastNo" name="estAddressLastNo" size="3"/>
									<br/>(
									<input type="text" id="estAddressRoom" name="estAddressRoom" size="3"/><th:block th:text="#{'C122M01A.common.08'}">室</th:block>)
								</p>
						</div>
					</td>
					
					
				</tr>
					
			</table>
			<div id="openBox_realEstateIntroduction" style="display:none;" >
				<div id="realEstateGrid" ></div>
			</div>
			<div id="laaDetail" class="content" style="display:none;">
				<div id="laaEditDiv" >
					<table class="tb2">
						<tr style="display:none;">
							<td><input type="text" id="C122S01YOid"/></td>
						</tr>
						<tr>
							<td class="hd2" nowrap><th:block th:text="#{'C122M01A.csvCustName'}">地政士姓名</th:block>
							</td>
							<td>
								<input type="text" id="laaName" name="laaName" class="required" maxlength="40" size="12" />
							</td>
						</tr>
					</table>
				</div>
			</div>
        </th:block>
    </body>
</html>