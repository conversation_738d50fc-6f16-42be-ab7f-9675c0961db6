var _M = {
    fhandle: "cls1151m01formhandler",
    ghandle: "cls1151gridhandler",
    boxContextId: "#CLS1151BoxContext",
    
    /**
     * 簽報書mainId
     */
    CaseMainId: responseJSON.CaseMainId,
    itemType: "",
    CLSAction_isReadOnly: "N",
    /**
     * 額度明細表mainId
     */
    tabMainId: "",
    formPrefix: "CLS1151Form",
    fomrIds: ["CLS1151Form01"],
    AllFormData: {
        "01": {},
        "03": {},
        "04": {},
        "05": {}
    },
    /**
     * 不滿兩位的數字補0
     * @param {Object} num 數字
     */
    addZero: function(num){
        return num < 10 ? "0" + num : num;
    },
    /***
     * 針對form塞值
     * @param {Object} page 頁碼
     */
    setFormData: function(page){
    	ilog.debug("@_M > setFormData(page="+(page||'') +")");
    	_M.doUnBindYounCreatYNE();
        var page = _M.addZero(parseInt(page, 10));
        var formName = _M.formPrefix + page;
        var $form = $("#" + formName);
        var pageData = _M.AllFormData[page];
        if ($form.length) {
            $form.injectData(pageData);
        }
        else {
            alert("not have this form ==>" + formName);
        }
        
        //針對畫面觸發處理
        var $boxContext = $(_M.boxContextId);
        switch (page) {
            case "01":
                _M.doBindYounCreatYNE();
                $boxContext.find("#companyCity").trigger("change");
                
                //切換Panel
                if(_M.AllFormData.tr_repayFund == "Y"){
                	$("#tr_repayFund").show();
                }else{
                	$("#tr_repayFund").hide();
                }

                if( $form.find("#introduceSrc").val() == '3'){
                	if( $form.find("select#subUnitNo").find("option").length ==0){ //J-107-0136
//                    	ilog.debug("trigger_megaCode_change_event_for_subUnitNo");
                    	$form.find("select#megaCode").trigger("change");
                    }	
                	//================
                   	$form.find("select#subUnitNo").val(pageData.subUnitNo);
                }

				var str = pageData.marketingNotes;
				var type = typeof str;
				if(str != null && str != '' && type == 'string'){
					str = str.split("|");
					for(var i=0; i < str.length; i++){
						$("input[name=marketingNotes][value=" + str[i] + "]").attr("checked", true);
					}
				}
				
				IntroductionSource.show($form.find("#introduceSrc").val());
				exceptFlagQA.init();//J-112-0082 約定融資額度註記部分，原以表列式勾選方式，改以問答方式
				// J-113-0227 房貸核貸成數新增檢核邏輯，當專案種類為Y1/Y2/Y3新增對應X1功能
				projClass.loadGridInfo();
				projClass.show($form.find("#projClass").val());
				// J-113-0323 花蓮震災
				hualienEearthquake.show();
				
                break;
            case "02":
                break;
            case "03":
                $boxContext.find("[name=headItem5]:radio").trigger("change");
                $boxContext.find("#mRateType").trigger("change");
                break;
            case "04":
            	//切換Panel
                if(_M.AllFormData.tr_unsecureFlag == "Y"){
                	$("#unsecureFlagSpan").show();
                }else{
                	$("#unsecureFlagSpan").hide();
                }
            	$form.find("select").trigger("change", "init");
                break;
            case "05":
                break;
            case "06":
                break;
            case "07":
                //額度本票
                if (pageData.checkQNote) {
                    var checkQNote_val = pageData.checkQNote.split("|");
                    $form.find("[name=checkQNote]").val(checkQNote_val);
                }
                break;
            case "08":
				
				OtherTakingConditonTemplate.init();
				
                break;
            case "09":
                break;
            case "10":
                break;
        }
        
    },
    refresh_M_key: function(respJSON){
    	//參考 CLS1151M01FormHandler :: refreshJs_M
    	if(respJSON.flag_refresh_M=="Y"){
    		$.each(respJSON.flag_refresh_keyArr, function(idx, k) {        		
        		if(respJSON[k]){        			
        			_M.AllFormData[k] = respJSON[k];	
        			ilog.debug("_M :: refresh_M_key["+k+"]="+_M.AllFormData[k]);
        		}
			});
    		
    		if(true){
        		//是否顯示「利害關係人敘作無擔保授信註記」                            
                if(_M.AllFormData.tr_unsecureFlag=="Y"){
                	//ok
                }else{            	
                	_M.AllFormData["04"]["unsecureFlag"] = "";
                	$("#unsecureFlag").val("");
                }	
    		}
    	}
    },
    bindYounCreatYNEvents: function(){
        //       alert("doo");
        var $boxContex = $(_M.boxContextId);
        var $younCreatYShow = $(".younCreatYShow");
        if ($boxContex.find("[name=younCreatYN]:radio:checked").val() == "Y") {
            $younCreatYShow.show();
        }
        else {
            _M.cleanTrHideInput($younCreatYShow);
        }
        _M.resetL140s02aCheckYN();
    },
    doBindYounCreatYNE: function(){
        var $boxContex = $(_M.boxContextId);
        /** 是否屬於青創*/
        $boxContex.find("[name=younCreatYN]:radio").bind("change", _M.bindYounCreatYNEvents);
        
        $boxContex.find("[name=assisType]:radio").bind("change", _M.bindYounCreatYNEvents);
    },
    doUnBindYounCreatYNE: function(){
        var $boxContex = $(_M.boxContextId);
        /** 是否屬於青創*/
        $boxContex.find("[name=younCreatYN]:radio").unbind("change", _M.bindYounCreatYNEvents);
        
        $boxContex.find("[name=assisType]:radio").unbind("change", _M.bindYounCreatYNEvents);
    },
    
    /**
     * 清空欄位值並隱藏
     * @param {Object} $obj jquery 物件
     */
    cleanTrHideInput: function($obj){
        $obj.hide();
        $(["input", "select", "textarea", "span.field"]).each(function(i, v){
            $obj.find(v).each(function(){
                var $this = $(this);
                $this.each(function(){
                    switch (this.nodeName.toLowerCase()) {
                        case 'input':
                            switch (this.type.toLowerCase()) {
                                case "text":
                                case "hidden":
                                case "password":
                                    $this.val("");
                                    break;
                                case "radio":
                                case "checkbox":
                                    $this.removeAttr("checked");
                                    break;
                                default:
                                    $this.val("");
                                    break;
                            }
                            break
                        case 'select':
                            $this.val("").trigger("change");
                            break;
                        case "span":
                            $this.html("");
                            break;
                    }
                });
            });
        });
    },
    boxId: "#cls1151Box",
    codetypeItem: {},
    pageInitAcion: {
        /**
         * 用於每個頁籤的初始化
         */
        "01": {
            isInit: false,
            //頁面的初始動作
            initAction: function(){
            },
            //頁面的後面動作
            afterAction: function(){
            
            }
        }
    
    },
    /**
     *執行ajax的動作
     * @param {String} action 要執行的動作
     * @param {Array} formId
     * @param {Object} data 要傳送的資料
     * @param {Function} success 執行後的動作
     *
     * ex1: _M.doAjax(action:queryL140M01A);
     *
     * ex2:_M.doAjax(action:"queryL140M01A",success:function(obj){
     *      doSomething
     *     });
     * ex3:_M.doAjax(action:"queryL140M01A",formId:"xxxform",success:function(obj){
     *      doSomething
     *     });
     * ex4:_M.doAjax(action:"queryL140M01A",formId:"xxxform",data:obj,success:function(obj){
     *      doSomething
     *     });
     */
    doAjax: function(obj){
        $.ajax({
            async: obj.async == undefined ? true : obj.async,
            handler: _M.fhandle,
            action: obj.action || alert("error not have Action"),
            formId: obj.formId || 'empty',
            
            data: $.extend({
                noOpenDoc: true,
                caseType: _M.itemType || "1",
                mainId: _M.CaseMainId,
                tabFormMainId: _M.tabMainId
            }, obj.data || {}),
            success: function(responseData){
                if (obj.success) {
                    obj.success(responseData);
                }
                
            }
        });
    },
    
    /**
     * 表單是否出初始化
     */
    isInit: false,
    isReadOnly: false,
    /**
     * 設定是否readOnly
     */
    setReadOnly: function(justQurry){
        var isLock = false;
        if (!justQurry) {
            var page01 = _M.AllFormData["01"];
            if ((page01 && page01.docStatus && page01.docStatus != "010") || _openerLockDoc == "1") {
                isLock = true;
            }
            else {
                //額度明細表
                if (_M.itemType == "1") {
                    if (_M.CLSAction_isReadOnly == "Y") {
                        isLock = true;
                    }
                    //額度批覆表
                }
                else 
                    if (_M.itemType == "2") {
                    	if ((_M.mainDocStatus=="02K" ||_M.mainDocStatus=="04K" )&&_M.CLSAction_isReadOnly == "Y") {
                            isLock = true;
                        }
                    }
            }
        }
        else {
            isLock = justQurry;
        }
        
        _M.isReadOnly = isLock;
        var $conText = $(_M.boxContextId);
        //控制按鈕顯示
        $conText.find("button").not(".forview").each(function(){
            if (isLock) {
                $(this).hide();
            }
            else {
                $(this).show();
            }
        });
        
        $conText.find("form").each(function(){
            if (isLock) {
                $(this).lockDoc();
            }
            else {
                //改開分頁後不需要初始化
                //$(this).find("input:not(.caseReadOnly),textarea:not(.caseReadOnly),select").removeAttr("readOnly").removeAttr("disabled");
            }
        });
        
        
    },
    /**
     * 初始化
     * @param {Object} mainId  額度明細表mainId
     */
    init: function(){
        if (!this.isInit) {
            this.initItem("01");
            this.initGrid();
            this.initEven();
            this.isInit = true;
			RealEstateAction.init();
        }
        
        //改為開新頁不需要先清除
        /*
         var $conText = $(_M.boxContextId);
         //讓每次開起box都是第一頁
         $conText.find(".tabs").tabs({
         selected: 0
         });
         
         //清空form內的資訊
         
         for (var i = 1, total = 10; i <= total; i++) {
         var formName = _M.formPrefix + _M.addZero(i);
         var $form = $("#" + formName);
         if ($form.length) {
         $form.reset();
         } else {
         alert("not have this form =>" + formName);
         }
         }
         */
    },
    /**   
     * J-111-0343_05097_B1004 Web e-Loan修改消金額度明細表合計之功能
     *
     * */
    initGlobalVar: function(){
        $.ajax({
            handler: _M.fhandle,
            action: "initGlobalVar",
            data: {//把資料轉成json
                tabFormMainId: _M.tabMainId,
                rptMainId: _M.CaseMainId
            },
            success: function(obj){
            	 
            	if(obj.showLgdTotAmt == "Y"){
            		//J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
                	for (var i = 1; i <= obj.lmsLgdCountTotal; i++) {
                		if(obj["showLgdTotAmt_"+i] == "Y"){
                    		$(".showLgdTotAmt_"+i).show();
        				}else{
        					$(".showLgdTotAmt_"+i).hide();
        				}
                	}		
				}else{
					//J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
                	for (var i = 1; i <= obj.lmsLgdCountTotal; i++) {
                		$(".showLgdTotAmt_"+i).hide();
                	}	
				}
            	 
            }
        });    
    },
    /**
     *設定是否顯示聯貸條件頁籤
     * @param {Object} snoKind 額度控管種類值
     */
    setTab06: function(snoKind){
        var $tab06 = $("#cls1151s06Li");
        if (snoKind == "30") {
            $tab06.show()
        }
        else {
            $tab06.hide()
        }
    },
    /**
     * 開啟額度明細表
     * @param {Object} data 欄位資料
     * @param {Object} setting 設定檔
     */
    openBox: function(data, setting){
    	ilog.debug("@_M > openBox");
        //初始化box
        _M.init();
        $.extend(_M, setting || {});
        //文件鎖定的部分
        _M.CLSAction_isReadOnly = setting.CLSAction_isReadOnly;
        //把文件已開啟的狀態傳進來
        _openerLockDoc = data._openerLockDoc;
        //設定此份額度明細表mainId
        _M.tabMainId = data.mainId || "";
		_M.custId = data.custId;
		_M.dupNo = data.dupNo;
        if (_M.itemType == "2") {
            $("#cls1151s11").show();
            $("#title2").show();
        }
        else {
            $("#title1").show();
        }
        _M.doAjax({
            action: "queryL140M01A",
            success: function(obj){
            	ilog.debug("@_M > queryL140M01A[tabFormMainId="+_M.tabMainId+"]");

				var chkDisplay = obj.chkDisplay;
				
				$("#showCustId").html(DOMPurify.sanitize(obj.showCustId));
				
				var tCaseNo = obj.LastChangCaseno;
				$("#LastChangCaseno").val(tCaseNo);
				if (tCaseNo) {
					$(".LastChangCasenoYShow").show();
				}
				
                //$("#mainTabs").show();
                _M.AllFormData = obj;
                _M.mainDocStatus=obj.mainDocStatus;
                //設定是否文件鎖定
                _M.setReadOnly(data.justQurry);
                //塞入文件資訊
                _M.setFormData("01");
                
                //J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
                //$('#CLS1151Form04').find("#lgdTotAmt").html(obj.lgdTotAmt);
                var lgdTotAmt = $('#CLS1151Form04').find("#lgdTotAmt");
                //lgdTotAmt.html(obj.lgdTotAmt);
                lgdTotAmt.injectData({'lgdTotAmt':obj.lgdTotAmt},false);
				
				$("#clsLgdInfo").injectData({'clsLgdInfo':obj.clsLgdInfo},false);
				$("#expectLgdDesc").text(obj.expectLgdDesc);

                $('#CLS1151Form04').find("#label_lgdTotAmt_T").val(obj.label_lgdTotAmt_T);
                
                //若為舊案 是否屬於青創這個欄位 不可異動
                var $younCreatYN = $("[name=younCreatYN]");
                var $assisType = $("[name=assisType]");
                
                //若為舊案 是否屬於整批分戶貸款這個欄位 不可異動
                var $packLoan = $("[name=packLoan]");
                //若為舊案[是否屬於「0403震災受災戶住宅補貼方案」]相關欄位 不可異動
                var $hualien0403 = $("[name=hualien0403]");
                var $isHouseLoanGu = $("[name=isHouseLoanGu]");
                var $hualienGutPer = $("[name=hualienGutPer]");
                
                if (!_M.isReadOnly) {
                    $younCreatYN.removeAttr("disabled");
                    $assisType.removeAttr("disabled");
                    $packLoan.removeAttr("disabled");
                    $hualien0403.removeAttr("disabled");
                    $isHouseLoanGu.removeAttr("disabled");
                    $hualienGutPer.removeAttr("disabled");
                }
                
                var $boxContex = $(_M.boxContextId);
                var $younCreatYShow = $(".younCreatYShow");
                if ($boxContex.find("[name=younCreatYN]:radio:checked").val() == "Y") {
                    $younCreatYShow.show();
                }
                else {
                    _M.cleanTrHideInput($younCreatYShow);
                }
                
                var page04Data = _M.AllFormData["04"];
                if ((page04Data.dataSrc != "1" && page04Data.dataSrc != "2") || _M.itemType == "2") {
                    $younCreatYN.attr("disabled", "disabled");
                    $assisType.attr("disabled", "disabled");
                    $hualien0403.attr("disabled", "disabled");
                    $isHouseLoanGu.attr("disabled", "disabled");
                    $hualienGutPer.attr("disabled", "disabled");
                }
                
                //				alert(_M.itemType);
                if ((page04Data.dataSrc == "5" || _M.itemType == "2")) {
                    //					alert("1111");
                    $packLoan.attr("disabled", "disabled");
                    $("button[class*='oldcase']").hide();
                    $("input[type='text'][class*='oldcase'],button[class*='oldcase']").attr("disabled", "disabled");
                }
                //設定聯貸條件是否顯示
                _M.setTab06(_M.AllFormData["04"]["snoKind"]);
                var btn = {
                    "button.inculeAccount": function(){
                        _M.btnInculeAccount();
                    },
                    "button.save": function(){
                        _M.verifyCntrNoAction().done(function(json){
                            _M.save_CntrDoc();
                        });
                    },
                    //因 user 容易把刪除額度明細表的 button, 誤認為刪除產品的 button, 所以隱藏此button                   
                    //                    "button.delete": function(){
                    //                        _M.delete_CntrDoc();
                    //                    },
                    "button.diff": function(){
                        _M.btnDiff();
                    },
                    "button.close": function(){
                        window.close();
                        //$.thickbox.close();
                    }
                }
                if (_M.isReadOnly) {
                    delete btn["button.inculeAccount"];
                    delete btn["button.save"];
                    //                    delete btn["button.delete"];
                    thickboxOptions.customButton = ["button.close"];
                }
                else 
                    //if (page04Data.dataSrc != "5" && page04Data.dataSrc != "6" && page04Data.dataSrc != "7") {
					if (!chkDisplay){
                        delete btn["button.diff"];
                    }
                //1.引進帳務資料的功能只提供給分行。
                if (_M.isByHeadAndArea()) {
                    delete btn["button.inculeAccount"];
                }
                //如果為團貸總戶案的處理
                if (_M.AllFormData.docCode == "5") {
                    $(".docCode5Hide").hide();
                    $(".docCode5Show").show();
                }
                else {
                    $(".docCode5Hide").show();
                    $(".docCode5Show").hide();
                }
                
                
                
                $.each(btn, function(key, value){
                    $("<span class='fg-buttonset'><span class='fg-child'><button type='button' class='ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only fg-button'><span class='ui-button-text'><span class='text-only'>" + i18n.cls1151s01[key] + "</span></span></button></span></span>").bind("click", value).appendTo("#buttonPanel");
                });
                /**
                 $(_M.boxId).thickbox({
                 //cls1151s01.title=CLS1151 授信管理系統
                 title: i18n.cls1151s01["cls1151s01.title"],
                 width: 980,
                 height: 500,
                 modal: true,
                 readOnly: _openerLockDoc == "1",
                 i18n: i18n.cls1151s01,
                 buttons: btn
                 });
                 **/
				$("#parentCntrNo").trigger('blur');//整批分戶貸款-扣薪福委會代號 		
				
				
				
				
				
            }
        }); // end - ajax
        
        if(true){ // J-107-0234 都更危老
        	/*
			上述程式在  _M.init(); 之後，才去  _M.tabMainId = data.mainId || "";  
			不把建 grid 的程式寫在 _M.init(); 裡頭
			免得第1次查 grid 時, tabMainId='' 又需要再查第2次
        	*/
        	$("#realEstateBeforeGrid").iGrid({
    			height: 100,
    			handler: "cls1151gridhandler",
    			sortname: 'estateType|estateSubType',
    			sortorder: 'asc|asc',
    			action: "queryL140m01t",
    			postData: {
    				tabFormMainId: _M.tabMainId,
    				flag: "N"
    			},
    			loadComplete : function() {    				
    				$('#realEstateBeforeGrid a').click(function(e) {
    					// 避免<a href="#"> go to top
    					e.preventDefault();
    				});
    			},
    			colModel: [{
    				colHeader: i18n.cls1151s01["L140M01T.estateType"],//類別
    				width: 80,
    				name: 'estateType',
    				sortable: true,
    				formatter: 'click',
    				onclick: RealEstateAction.openBox
    			}, {
    				colHeader: i18n.cls1151s01["L140M01T.checkYN"],//檢核
    				name: 'checkYN',
    				align: 'center',
    				width: 5
    			}, {
    				colHeader: i18n.cls1151s01["L140M01T.mCntrNo"],//L140M01T.mCntrNo=母戶額度序號
    				name: 'mCntrNo',
    				width: 14
    			}, {
    				colHeader: "oid",
    				name: 'oid',
    				hidden: true
    			}, {
    				colHeader: "flag",
    				name: 'flag',
    				hidden: true
    			}],
    			ondblClickRow: function(rowid){
    				var data = $("#realEstateBeforeGrid").getRowData(rowid);
    				RealEstateAction.openBox(null, null, data);
    			}
    		});
    		
    		$("#realEstateAfterGrid").iGrid({
    			height: 100,
    			handler: "cls1151gridhandler",
    			sortname: 'estateType|estateSubType',
    			sortorder: 'asc|asc',
    			needPager: false,        
    	        shrinkToFit: true,
    			action: "queryL140m01t",
    			postData: {
    				tabFormMainId: _M.tabMainId,
    				flag: "Y"
    			},
    			loadComplete : function() {    				
    				$('#realEstateAfterGrid a').click(function(e) {
    					// 避免<a href="#"> go to top
    					e.preventDefault();
    				});
    			},
    			colModel: [{
    				colHeader: i18n.cls1151s01["L140M01T.estateType"],//類別
    				width: 80,
    				name: 'estateType',
    				sortable: true,
    				formatter: 'click',
    				onclick: RealEstateAction.openBox
    			}, {
    				colHeader: i18n.cls1151s01["L140M01T.checkYN"],//檢核
    				name: 'checkYN',
    				align: 'center',
    				width: 5
    			}, {
    				colHeader: i18n.cls1151s01["L140M01T.mCntrNo"],//L140M01T.mCntrNo=母戶額度序號
    				name: 'mCntrNo',
    				width: 14
    			}, {
    				colHeader: "oid",
    				name: 'oid',
    				hidden: true
    			}, {
    				colHeader: "flag",
    				name: 'flag',
    				hidden: true
    			},{
    				colHeader: "isRentOrSell",
    				name: 'isRentOrSell',
    				hidden: true
    			}],
    			ondblClickRow: function(rowid){
    				var data = $("#realEstateAfterGrid").getRowData(rowid);
    				RealEstateAction.openBox(null, null, data);
    			}
    		});
    		
        }
    
        if(true){ // 
        	$("#l140m01yGrid").iGrid({
    			height: 100,
    			handler: "cls1151gridhandler",    			
    			action: "queryL140m01y",
    			// sortname: 'refType|refValue|refModel|refMainId|refOid',    			
    			// sortorder: 'asc|asc|asc|asc|asc',
    			needPager: false,
    			postData: {
    				'tabFormMainId': _M.tabMainId
    			},
    			loadComplete : function() {    				
    				
    			},
    			colModel: [{
    				colHeader: i18n.cls1151s01["L140M01Y.refType"],//註記類別
    				width: 160, sortable: false,
    				name: 'refType'
//    				, formatter: 'click', onclick: L140m01yAction.openBox
    			}, {
    				colHeader: i18n.cls1151s01["L140M01Y.label.valueDesc"],//說明
    				width: 240, sortable: false,
    				name: 'valueDesc'
    			}, {
    				colHeader: i18n.cls1151s01["L140M01Y.label.linkToRefModel"],//
    				width: 40, sortable: false,
    				name: 'refModelDesc', formatter: 'click', onclick: l140m01yGridClickOpenDoc
    			}, {
    				name: 'oid', hidden: true
    			}, {
    				name: 'refModel', hidden: true
    			}, {
    				name: 'refOid', hidden: true
    			}],
    			ondblClickRow: function(rowid){
    				// var data = $("#l140m01yGrid").getRowData(rowid);
    				// l140m01yGridClickOpenDoc(null, null, data);
    			}
    		});    		
        }
        
        //J-111-0343_05097_B1004 Web e-Loan修改消金額度明細表合計之功能
        if(true){ //
        	_M.initGlobalVar();
        }
    },
    /**
     * 確認是否為營運中心或授管處
     */
    isByHeadAndArea: function(){
        var bird = userInfo ? userInfo.unitNo : ""
        var unitType = userInfo ? userInfo.unitType : "";
        if (unitType) {
            //unitType 為2或4即為授管處或營運中心
            if ("4" == unitType || "2" == unitType) {
                return true;
            }
        }
        return false
    },
    /**
     * 初始化下拉選單
     *
     */
    initItem: function(page){
    	ilog.debug("@_M > initItem(page="+(page||'') +")" );
        //第一次開啟box
        //產生下拉選單
        var $div = $(_M.boxContextId).find("#" + _M.formPrefix + page).find("[itemType]");
        var allKey = [];
        $div.each(function(){
            allKey.push($(this).attr("itemType"));
        });
        //_M.codetypeItem = API.loadCombos(["CountryCode", "Common_Currcy", "L140M03A_identityNo", "L140M03A_noticeType", "counties"]);
        _M.codetypeItem = API.loadCombos(allKey);
        $div.each(function(){
            var $obj = $(this);
            var itemType = $obj.attr("itemType");
            if (itemType) {
                var format = $obj.attr("itemFormat") || "{value} - {key}";
                $obj.setItems({
                    space: $obj.attr("space") || true,
                    item: _M.codetypeItem[itemType],
                    format: format,
                    sort: $obj.attr("itemSort") || "asc",
                    size: $obj.attr("itemSize")
                });
            }
        });
    },
    
    /**
     * 初始化grid
     *
     */
    initGrid: function(){

		//J-108-0097 購置高價住宅貸款檢核表
    	grid1 : $('#CLS1151Form01').find("#grid1").iGrid({
			handler: 'lmscommongridhandler',
			action: "queryCheckListOfHighPricedHousingLoan",
			height: 50,
			width: 80,
			autowidth: true,
			shrinkToFit: false,
			async:false,
			needPager: false,
			postData: {
				 l140m01aMainId: responseJSON.mainId,
				 custId: _M.custId,
				 dupNo: _M.dupNo
            },
			colModel: [{
			    colHeader: '類別',
			    name: 'type',
				align: 'left',
				width: 150,
			    sortable: true,
				formatter: 'click',
				onclick : function(cellValue, options, rowObject){
					var isReadonly = _openerLockDoc || _M.isReadOnly;
					var l140m01aMainId = $("#CLS1151Form01").find("#mainId").val();
					HighPricedHousingLoanCheckList.open($("#CLS1151Form01"), isReadonly, l140m01aMainId); 
				}
			},
			{
			    colHeader: '檢核',
			    name: 'check',
				align: 'center',
				width: 50
			},
			{
				name: 'oid',
                hidden: true
			}]
		});
    },
    /**
     * 初始化事件
     *
     */
    initEven: function(){
    	ilog.debug("@_M > initEven");
        //讓ie7的option可以disabple
        $('#pageNumX,#pageNumY').each(function(){
            this.rejectDisabled = function(){
                if (this.options[this.selectedIndex].disabled) {
                    if (this.lastSelectedIndex) {
                        this.selectedIndex = this.lastSelectedIndex;
                    }
                    else {
                        var first_enabled = $(this).children('option:not(:disabled)').get(0);
                        this.selectedIndex = first_enabled ? first_enabled.index : 0;
                    }
                }
                else {
                    this.lastSelectedIndex = this.selectedIndex;
                }
            };
            this.rejectDisabled();
            this.lastSelectedIndex = this.selectedIndex;
            $(this).children('option[disabled]').each(function(){
                $(this).css('color', '#CCC');
            });
            $(this).change(function(){
                this.rejectDisabled();
            });
        });
        var $boxContex = $(_M.boxContextId);
        /** 是否屬於青創*/
        /** 點掉改用其他方法處理
 $boxContex.find("[name=younCreatYN]:radio").change(function(){
 var $younCreatYShow = $(".younCreatYShow");
 if ($boxContex.find("[name=younCreatYN]:radio:checked").val() == "Y") {
 $younCreatYShow.show();
 } else {
 _M.cleanTrHideInput($younCreatYShow);
 }
 });
 */
        $("#assisType1").hide();
        $("#assisType2").hide();
        $boxContex.find("[name=assisType]:radio").change(function(){
            if ($boxContex.find("[name=assisType]:radio:checked").val() == "1") {
                $("#assisType1").show();
                $("#assisType2").hide();
            }
            else {
                $("#assisType2").show();
                $("#assisType1").hide();
            }
        });
        /** 引進同一事業體*/
        $boxContex.find("#pullinCompanyId").click(function(){
            API.openQueryBox({
                defaultCustType: "2",
                forceNewUser: false,
                doNewUser: true,
                divId: "CLS1151Form01",
                autoResponse: { // 是否自動回填資訊 
                    id: "companyId", //   統一編號欄位ID
                    dupno: "companyDupNo",//   重覆編號欄位ID
                    name: "companyName" //同一事業體資訊(名稱)
                },
                fn: function(obj){
                }
            });
        });
        
        /** 引進青創線上申貸資訊*/
        YoungDataInfo.init($boxContex);
		
        IndustryTypeInfo.init($boxContex);
		
        /** 縣市變更*/
        var $companyArea = $boxContex.find("#companyArea");
        $boxContex.find("#companyCity").setItems({
            item: QueryCityCode.getCode("1", ""),
            sort: "asc",
            fn: function(){
                var value = $(this).val();
                if (value) {
                    var obj = QueryCityCode.getCode("2", value);
                    $companyArea.setItems({
                        item: obj,
                        value: _M.AllFormData["01"]["companyArea"] || ""
                    });
                }
            }
        });
        /**查詢央行購置註記 */
        $boxContex.find("#queryL140M01M").click(function(){
            //Src = LMSL140M01MPage.js
            LMS140M01MAction.openBox(_M.tabMainId, true, true, $("#CLS1151Form01"));  
        });
        /**修改央行購置註記*/
        $boxContex.find("#modifyL140M01M").click(function(){
            //Src = LMSL140M01MPage.js
            LMS140M01MAction.openBox(_M.tabMainId, false, true, $("#CLS1151Form01"));   
        });
        $boxContex.find("#modifyL140M01Q").click(function(){
            //Src = LMSL140M01MPage.js
            LMS140M01MAction.openChinaLoan(_M.tabMainId, false);
        });
        
        $boxContex.find("#queryL140M01Q").click(function(){
            //Src = LMSL140M01MPage.js
            LMS140M01MAction.openChinaLoan(_M.tabMainId, true);
        });
		
		$("input[name='cbRuleVersion']").click(function(){
			var checkedVal = $("input[name='cbRuleVersion']:checked").val();
			$("#cbLoanVersion").val(checkedVal);
		})
		
		$boxContex.find("#cbRuleButton").click(function(){
			$(".20240919_ver7").val("20240919_ver7");
			$(".20240614_ver6").val("20240614_ver6");
			$(".20230616_ver5").val("20230616_ver5");
			$(".20211217_ver4").val("20211217_ver4");
			$(".20210924_ver3").val("20210924_ver3");
			$(".20210319_ver2").val("20210319_ver2");
			$(".20201208_ver1").val("20201208_ver1");
			$(".oldVersion").val("oldVersion");
			
			var buttons = {};
			buttons[i18n.def.sure] = function(){
				var tmpValue = $("input[name='cbRuleVersion']:checked").val();
	            $.ajax({
	                    handler: "cls1151m01formhandler",
	                    action: "saveL140M01MVersion",
	                    data: {
	                        tabFormMainId: _M.tabMainId,
							cbRuleVersion: tmpValue
	                    },
	                    success: function(obj){				
	                    }
	                });
					$.thickbox.close();
	        };

           	$("#openBox_CbRuleVersion").thickbox({
                title: '央行對辦理不動產抵押貸款版本',
                width: 350,
                height: 250,
                modal: true,
				align: "center",
				valign: 'bottom',
                i18n: i18n.def,
                buttons: buttons
            });
        });
        
        /** 本案是否屬銀行法72-2條控管對象  */
        $boxContex.find("#applyOnLine772").click(function(){
        	//初始化    		   
     	   $.ajax({
                 async: false,
                 handler: _M.fhandle,
                 data: {
                     formAction: "queryOnLine772Flag",
                     tabFormMainId: _M.tabMainId,
                     cntrNo: _M.AllFormData["04"]["cntrNo"] ||'',
     				 custId: $("#custId").val()||'',
     				 dupNo: $("#dupNo").val()||''
                 },
                 success: function(obj){
                	$.each(['is722OnFlag', 'is722CntrNo', 'is722QDate'
                	        , 'cntrNoChkExistFlag', 'cntrNoChkExistDate'], function(k, v){
                		_M.AllFormData["01"][v] = obj[v];
                	});
                	//~~~~~~~~~~~~~~~~~~~~~~~~
     				if(obj.is722OnFlag ==""){
     					$("[name='is722OnFlag'][value='"+encodeURI($("[name='is722OnFlag']:radio:checked").val())+"']:radio" ).attr( "checked" , false );    
     				}else{
     					$("[name='is722OnFlag'][value='"+obj.is722OnFlag+"']:radio").attr("checked" , "checked" );   
     				}
                     
     			    $("#is722CntrNo").val(obj.is722CntrNo); 
     			    $("#is722QDate").val(obj.is722QDate); 
     			    
     			    //額度序號是否帳務系統已建檔
     			    if(obj.cntrNoChkExistFlag ==""){
     			    	$("[name='cntrNoChkExistFlag'][value='"+encodeURI($("[name='cntrNoChkExistFlag']:radio:checked").val())+"']:radio" ).attr( "checked" , false );    
     				}else{
     					$("[name='cntrNoChkExistFlag'][value='"+obj.cntrNoChkExistFlag+"']:radio").attr("checked" , "checked" );   
     				}
     				
     	            $("#cntrNoChkExistDate").val(obj.cntrNoChkExistDate);
     	            //~~~~~~~~~~~~~~~~~~~~~~~~
     	            if (obj.isInstalmentOn == "") {
     	            	 $("input[name='isInstalmentOn']").attr("checked", false).triggerHandler("change");
     	            }else {
     	            	 $("input[name='isInstalmentOn'][value='" + encodeURI(obj.isInstalmentOn) + "']").attr("checked", true).triggerHandler("change");
     	            }  
     	            //
     	            $("#realEstateBeforeGrid").trigger("reloadGrid");
                 }
             });
        });

        if(true){  // J-107-0234 都更危老
        	
        	 $boxContex.find("#addRealEaste").click(function(){
             	RealEstateAction.quickAddRealEstate();
             });
        	 
             $boxContex.find("#deleteRealEaste").click(function(){
             	RealEstateAction.deleteRealEstate(); 		   
             });
            
             var $realEstateFormDetail = $("#realEstateFormDetail");
             var defaultSelect = "<option value=''>" + i18n.def.comboSpace + "</option>";
                    
             $realEstateFormDetail.find("#estateCityId").setItems({
                 item: QueryCityCode.getCode("1", ""),
                 sort: "asc",
                 value: "",
                 fn: function(k, v){
                 	RealEstateAction.changCityValue( $realEstateFormDetail.find("#estateCityId"),  $realEstateFormDetail.find("#estateAreaId"),  $realEstateFormDetail.find("#estateSit3No"),  $realEstateFormDetail.find("#estateSit4No"), defaultSelect, (!v));
                 }
             });
             $realEstateFormDetail.find("#estateAreaId").change(function(k, v){
     			if (  $realEstateFormDetail.find("#estateAreaId").val() != "") {
     				RealEstateAction.changAreaValue( $realEstateFormDetail.find("#estateCityId"),  $realEstateFormDetail.find("#estateAreaId"),  $realEstateFormDetail.find("#estateSit3No"),  $realEstateFormDetail.find("#estateSit4No"), defaultSelect, (!v));
     			}            
             });
            
             $realEstateFormDetail.find("#estateCityIdXX").setItems({
                 item: QueryCityCode.getCode("1", ""),
                 sort: "asc",
                 value: "",
                 fn: function(k, v){
                 	RealEstateAction.changCityValue( $realEstateFormDetail.find("#estateCityIdXX"),  $realEstateFormDetail.find("#estateAreaIdXX"),  $realEstateFormDetail.find("#estateSit3NoXX"),  $realEstateFormDetail.find("#estateSit4NoXX"), defaultSelect, (!v));
                 }
             });
     		
             $realEstateFormDetail.find("#estateAreaIdXX").change(function(k, v){
     			if ( $realEstateFormDetail.find("#estateAreaIdXX").val() != "") {
     				RealEstateAction.changAreaValue( $realEstateFormDetail.find("#estateCityIdXX"),  $realEstateFormDetail.find("#estateAreaIdXX"),  $realEstateFormDetail.find("#estateSit3NoXX"),  $realEstateFormDetail.find("#estateSit4NoXX"), defaultSelect, (!v));
     			}                 
             });
             
             //J-109-0248_05097_B1001 Web e-Loan授信都更之計畫進度，將已送件未核定納入排除72-2項目
             $realEstateFormDetail.find('#estateStatus').change(function(){
     			var estateSubType = $("input[name='estateSubType']:radio:checked").val();
     			var estateStatus = $(this).val() ;
     			if(estateSubType == '02' && estateStatus == '2' ){
     				$('#showTipsForStatus_2').show();
     			}else{
     				$('#showTipsForStatus_2').hide();
     			}
     			//J-110-0054_10702_B1001 Web e-Loan額度明細表不動產暨72-2相關註記修改
				if(estateSubType == '03'){
					$("#estateSubType03").show();
					$("#estateOwner").addClass("required");
				}
				else{
					$("#estateSubType03").hide();
					$("#estateOwner").removeClass("required");
				}
     		 });
             
             
             
             $realEstateFormDetail.find("input[name='sectKind']").change(function(k, v){			
     			 var useItem = "";
                 var value = $(this).val();
     			
                 if (value == "1") { //都市
                     useItem = CommonAPI.loadCombos(["LandUse21", "cms1010_useKind1"]);
                     $realEstateFormDetail.find("#useSect").setItems({
                         item: useItem.LandUse21,
                         format: "{key}"
                     });
                     $realEstateFormDetail.find("#useKind").setItems({
                         item: useItem.cms1010_useKind1,
                         format: "{key}"
                     });
                 } else if(value == "2") { //非都市
     				useItem = CommonAPI.loadCombos(["LandUse22", "cms1010_useKind2"]);
     				$realEstateFormDetail.find("#useSect").setItems({
                         item: useItem.LandUse22,
                         format: "{key}"
                    });
     				$realEstateFormDetail.find("#useKind").setItems({
                         item: useItem.cms1010_useKind2,
                         format: "{key}"
                    });
     			 }
             });
            
             $realEstateFormDetail.find("input[name=position]:radio").change(function(){
            	 var val = $(this).val();
                 if (val== "0") {
                	 $realEstateFormDetail.find("#commonSiteView.estateView").show();
                	 $realEstateFormDetail.find("#plant_View.estateView").find(".L140M01T_position_0").show();
                 }else{
                	 $realEstateFormDetail.find("#commonSiteView.estateView").hide();
                	 $realEstateFormDetail.find("#plant_View.estateView").find(".L140M01T_position_0").hide(); 
                 }
             });
        
             $realEstateFormDetail.find("input[name='buildWay']").change(function(){
        		if($(this).val() == "02" || $(this).val() == "03"){
        			$realEstateFormDetail.find("#landlordView.estateView").show();
        			$realEstateFormDetail.find("#otherDescView.estateView").hide();
        			$("#otherDesc").val("");
        		}else if($(this).val() == "05"){
        			$realEstateFormDetail.find("#landlordView.estateView").hide();
        			$("#landlordNum").val("");
        			$realEstateFormDetail.find("#otherDescView.estateView").show();
        		}else{
        			$realEstateFormDetail.find("#landlordView.estateView").hide();
        			$realEstateFormDetail.find("#otherDescView.estateView").hide();
        			$("#landlordNum").val("");
        			$("#otherDesc").val("");
        		}
             });
        }
       
        
        if(true){ // J-107-0136
        	$("#CLS1151Form01").find("select#megaCode").change(function(){

        		var val_mega_code = $(this).val();
        		if(val_mega_code==''){
//        			ilog.debug("call[megaCode='']change");
        			$("#CLS1151Form01").find("select#subUnitNo").setItems({});
        			$("#CLS1151Form01").find("#subEmpNo").val("");
        			$("#CLS1151Form01").find("#subEmpNm").val("");
        		}else{
//        			ilog.debug("call[megaCode="+val_mega_code+"]change");
	        		var key_sub_unitNo = ('LNF13E_SUB_UNITNO_' + val_mega_code);
	        		//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	        		var item = CommonAPI.loadCombos(key_sub_unitNo);
	        		$("#CLS1151Form01").find("select#subUnitNo").setItems({ item: item[key_sub_unitNo] , format: "{value} - {key}" });
        		}
        	});	
        	
        	$("#CLS1151Form01").find("#agntNo").change(function(){
        		var val_agntNo = $(this).val();
        		if(val_agntNo==''){
        			$("#CLS1151Form01").find("#agntChain").val("");
        		}
        	});	
        }
        
        if(true){
        	$boxContex.find("#addL140m01y").click(function(){
        		L140m01yAction.addL140m01y();
            });
        	 
            $boxContex.find("#delL140m01y").click(function(){
            	L140m01yAction.delL140m01y(); 		   
            });
        }
        
        /**總處核定 */
        $("#pageNumA").change(function(){
            if ($(this).val() != "") {
                $("#toALoanA").val($("#pageNumA :selected").text());
            }
        });
        
        $("#importL140M01PBt").click(function(){
            var seq = $("#l140m01pSeq").val();
            var cntrNo = _M.AllFormData["04"]["cntrNo"]
            
            if (!seq) {
                // L140M01P.err01=欲修改序號不可0或空白。
                return API.showMessage(i18n.cls1151s01["L140M01P.err01"]);
            } 
            //J-108-0112_10702_B1003 修改敘做條件異動比較表重新編輯功能放大序號範圍及判斷為敘作條件時放大長度限制
			else if (seq > 28){
				// L140M01P.err03=欲修改序號超過範圍。
				return API.showMessage(i18n.cls1151s01["L140M01P.err03"]);
			}
            if(seq == 28){
            	$("#l140m01pBfcData").attr('maxlength','10000');
            }
            else{
            	$("#l140m01pBfcData").attr('maxlength','100');
            }
            
            _M.doAjax({
                action: "reGetL140M01P",
                data: {
                    cntrNo: cntrNo,
                    l140m01pSeq: seq
                },
                success: function(obj2){
                    $("#l140m01pBfsData").val(obj2.l140m01pBfsData);
                    $("#l140m01pBfcData").val(obj2.l140m01pBfcData);
                    $("#importOK").val("Y");
                //											$.thickbox.close(); 
                }
            });
        });
        
        //畫面驗證
        //若針對畫面有特別驗證如:起日不能大於迄日，則加入驗證到相對應的頁籤上
        $boxContex.find("#mainTabs").tabs({
            select: function(event, ui){
				ilog.debug($(ui));
				var page = $(ui.panel).attr("id").substring(8,10);
				//alert(page);
                if (_M.saveNowPage(event)) {
                    //下個選擇的頁面
                    //var selectPage = _M.addZero(ui.index + 1);
					var selectPage = page;
                    //用來初始化每個頁面
                    var action = _M.pageInitAcion[selectPage];
                    if (action) {
                        if (!action.isInit) {
                            action.initAction();
                            action.isInit = true;
                        }
                        else {
                            //如果有初始化後面的動作
                            if (action.afterAction) {
                                action.afterAction();
                            }
                        }
                    }
                    //針對form塞值
                    _M.setFormData(selectPage);
                    if (action && action.afterSetFormDataAction) {
                        action.afterSetFormDataAction();
                    }
                    
                }
            }
        });
        
        $boxContex.find("[name=repayFund]:radio").click(function(){
            if ($boxContex.find("[name=repayFund]:radio:checked").val() == "Y") {
                API.showErrorMessage("須確為以基金贖回款為還款來源之個人短期無擔保放款，方可選擇此項註記。");
            }
        });
		
		initMarketingNotes();
		
		IntroductionSource.initEvent();		
		
		exceptFlagQA.initEvent();//J-112-0082 約定融資額度註記部分，原以表列式勾選方式，改以問答方式
		
		// J-113-0227 房貸核貸成數新增檢核邏輯，當專案種類為Y1/Y2/Y3新增對應X1功能
		projClass.initEvent();
		// J-113-0323 0403花蓮震災
		hualienEearthquake.initEvent();
		
    },
    /**
     * 引進帳務
     */
    btnInculeAccount: function(){
        var $form = $("#" + _M.formPrefix + "04");
        var cntrNo = _M.AllFormData['04']['cntrNo'] || $form.find("#cntrNo").val() || false
        var lastValue = _M.AllFormData['04']['LVCurr'] || $form.find("#LVCurr").val() || false;
        if (!cntrNo) {
            //L140M01A.msg007=請輸入額度序號
            return API.showMessage(i18n.cls1151s01["L140M01A.msg007"]);
        }
        
        _M.doAjax({
            action: "queryMoneyData",
            data: {
                cntrNo: cntrNo
            },
            success: function(obj){
                var nowPage = $("#mainTabs").find("li.subCLS.ui-tabs-selected").attr('pIdx');
                _M.AllFormData = obj;
                //塞入文件資訊
                _M.setFormData(nowPage);
                if (nowPage == "05") {
					L140S02Action._reloadGrid();
				}
				
				$("#gridviewitemChildren").trigger("reloadGrid");
				$("#gridviewitemChildren2").trigger("reloadGrid");
				
				//若為舊案和批覆書 是否屬於青創這個欄位 不可異動
                var $younCreatYN = $("[name=younCreatYN]");
                var $assisType = $("[name=assisType]");
                //若為舊案[是否屬於0403花蓮地震震災貸款]相關欄位 不可異動
                var $hualien0403 = $("[name=hualien0403]");
                var $isHouseLoanGu = $("[name=isHouseLoanGu]");
                var $hualienGutPer = $("[name=hualienGutPer]");
                $younCreatYN.removeAttr("disabled");
                $assisType.removeAttr("disabled");
                $hualien0403.removeAttr("disabled");
                $isHouseLoanGu.removeAttr("disabled");
                $hualienGutPer.removeAttr("disabled");
                var page04Data = _M.AllFormData["04"];
                if ((page04Data.dataSrc != "1" && page04Data.dataSrc != "2") || _M.itemType == "2") {
                    $younCreatYN.attr("disabled", "disabled");
                    $assisType.attr("disabled", "disabled");
                    $hualien0403.attr("disabled", "disabled");
                    $isHouseLoanGu.attr("disabled", "disabled");
                    $hualienGutPer.attr("disabled", "disabled");
                }
                //設定聯貸條件是否顯示
                _M.setTab06(_M.AllFormData["04"]["snoKind"]);
            }
        });
        
    },
    /**
     * 敘做條件異動比較表
     */
    btnDiff: function(){
        var btn = {
            "print": function(){
                $.form.submit({
                    url: "../simple/FileProcessingService",
                    target: "_blank",
                    data: {
                        mainId: responseJSON.mainId,
                        rptOid: "R93" + "^" + responseJSON.oid + "|",
                        fileDownloadName: "CLS1151R04.pdf",
                        serviceName: "cls1141r01rptservice"
                    }
                });
            },
            "reQuery": function(){
            
                _M.doAjax({
                    action: "reloadL140M01P",
                    data: {
                        cntrNo: _M.AllFormData["04"]["cntrNo"]
                    },
                    success: function(obj2){
                        var $btnDiffContent = $("#btnDiffContent");
                        $btnDiffContent.empty();
                        if (obj2.showHtml) {
                            $btnDiffContent.html(DOMPurify.sanitize(obj2.showHtml));
                        }
                    }
                });
            },
            "reModify": function(){
                $("#btnModifyBox").thickbox({
                    // L140M01P.title01=異動前敘做內容
                    title: i18n.cls1151s01['L140M01P.title01'],
                    width: 600,
                    height: 500,
                    align: 'left',
                    valign: 'top',
                    modal: false,
                    open: function(){
                        //$("#btnModifyContent").reset();
                        $("#l140m01pSeq").val("");
                        $("#l140m01pBfsData").val("");
                        $("#l140m01pBfcData").val("");
                        $("#importOK").val("");
                    },
                    buttons: API.createJSON([{
                        key: i18n.def['sure'],
                        value: function(){
                            var seq = $("#l140m01pSeq").val();
                            var importOK = $("#importOK").val();
                            
                            if (!seq) {
                                // L140M01P.err01=欲修改序號不可0或空白。
                                return API.showMessage(i18n.cls1151s01["L140M01P.err01"]);
                            }
                            
                            if (importOK != "Y") {
                                // L140M01P.err02=尚未執行調閱引入原始資料。
                                return API.showMessage(i18n.cls1151s01["L140M01P.err02"]);
                            }
                            
                            _M.doAjax({
                                action: "reModifyL140M01P",
                                data: {
                                    cntrNo: _M.AllFormData["04"]["cntrNo"],
                                    l140m01pSeq: seq,
                                    l140m01pBfcData: $("#l140m01pBfcData").val()
                                },
                                success: function(obj2){
                                    _M.doAjax({
                                        action: "queryL140M01P",
                                        data: {
                                            cntrNo: _M.AllFormData["04"]["cntrNo"]
                                        },
                                        success: function(obj){
                                            var $btnDiffContent = $("#btnDiffContent");
                                            if (obj.showHtml) {
                                                $btnDiffContent.html(DOMPurify.sanitize(obj.showHtml));
                                            }
                                        }
                                    });
                                    $("#importOK").val("");
                                    $.thickbox.close();
                                    
                                }
                            });
                            
                        }
                    }, {
                        key: i18n.def['cancel'],
                        value: function(){
                            $.thickbox.close();
                        }
                    }])
                
                });
            },
            "close": function(){
                $.thickbox.close();
            }
        };
        
        if (_M.isReadOnly) {
            delete btn["reQuery"];
            delete btn["reModify"];
        }
        
        _M.doAjax({
            action: "queryL140M01P",
            data: {
                cntrNo: _M.AllFormData["04"]["cntrNo"]
            },
            success: function(obj){
                var $btnDiffContent = $("#btnDiffContent");
                $btnDiffContent.empty();
                if (obj.showHtml) {
                    $btnDiffContent.html(DOMPurify.sanitize(obj.showHtml));
                }
                $("#btnDiffBox").thickbox({
                    //button.diff=敘做條件異動比較表
                    title: i18n.cls1151s01["button.diff"],
                    width: 850,
                    height: 600,
                    modal: true,
                    readOnly: _openerLockDoc == "1",
                    i18n: i18n.def,
                    buttons: btn
                
                });
            }
        });
        
    },
    
    mainGrid: null,
    /** 更新主檔grid**/
    _triggerMainGrid: function(){
        if (!this.mainGrid) {
            this.mainGrid = $("#gridveiwCntrNoDoc");
        }
        API.triggerOpener("gridveiwCntrNoDoc");
        this.mainGrid.trigger('reloadGrid');
    },
    /**刪除   thickBox  */
    delete_CntrDoc: function(){
        //confirmDelete=是否確定刪除?
        API.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.thickbox.close();
                _M.doAjax({
                    action: "deleteL140m01a",
                    success: function(){
                        _M._triggerMainGrid();
                        window.close();
                    //$.thickbox.close();
                    }
                });
            }
        });
    },
    /**
     * 儲存目前頁面
     * @param {Object} event 事件 用於頁面切換的時候
     */
    saveNowPage: function(event){
        //若針對畫面有
        //目前頁面的inex
		var realPage = $("#mainTabs").find("li.subCLS.ui-tabs-selected").attr('pIdx');
        var $form = $("#" + _M.formPrefix + realPage);
        if ($form.length) {
            switch (realPage) {
                case "01":
                case "03":
                case "04":
                    if (realPage == "04") {
                        if ($form.find("#useDeadline").val() == 1) {
                            var end = $form.find("#moveDurOtEnd").val().split("-");
                            var from = $form.find("#moveDurOtFrom").val().split("-");
                            var endData = new Date(end[0], end[1], end[2]);
                            var fromData = new Date(from[0], from[1], from[2]);
                            if (fromData > endData) {
                                //L140M01a.useDeadline=動用期限
                                //L140M01A.msg005=起始日期不能大於結束日期
                                return API.showErrorMessage(i18n.cls1151s01["L140M01A.useDeadline"] + i18n.cls1151s01["L140M01A.msg005"]);
                            }
                            $form.find("#desp1").val($form.find("#moveDurOtFrom").val() + ' ~ ' + $form.find("#moveDurOtEnd").val());
                        }else if($form.find("#useDeadline").val() == 8){
                             $form.find("#desp1").val('~' + $form.find("#moveDurOtApprEnd").val());
                        }
                        else {                        
                        }
                    }                    
                    if (!$form.valid()) {
                        if (event) {
                            event.preventDefault();
                        }
                        return false;                        
                    }
                    else {
                        $.extend(_M.AllFormData[realPage], $form.serializeData());
                        //刪除key值為空白的物件
                        delete _M.AllFormData[realPage][""];
                    }
                    break;
                case "07":
                    var tempData = [];
                    //額度本票
                    $form.find("[name=checkQNote]:checked").each(function(){
                        tempData.push($(this).val());
                    });
                    $.extend(_M.AllFormData[realPage], {
                        checkQNote: tempData.join("|"),
                        checkNote: $("#checkNote").val()
                    
                    });
                    break;
                default:
                    $.extend(_M.AllFormData[realPage], $form.serializeData());
                    break;
            }
        }
        else {
            alert("not have this form ==>" + formId);
        }
        
        return true;
    },
    /**  儲存 thickBox   */
    save_CntrDoc: function(){
        if (_M.saveNowPage()) {
            _M.doAjax({
                action: "saveL140M01A",
                data: {
                    formData: JSON.stringify(_M.AllFormData),
                    packLoan: _M.AllFormData["01"]["packLoan"],
					deductSalaryWelfareCommitteeNo: $("#deductSalaryWelfareCommitteeNo").val()
                },
                success: function(obj){
                    _M._triggerMainGrid();
                    
                    var nowPage = $("#mainTabs").find("li.subCLS.ui-tabs-selected").attr('pIdx');
                    if (nowPage == "05") {
    					L140S02Action._reloadGrid();
    				}
                    
                    if (obj.msg) {
                        API.showErrorMessage("", obj.msg, function(){
                            //當攤貸時還有餘額，要跳出餘額修改視窗
                            if (obj.l140m01eAmt != 0) {
                                _M.l140m01eAmtBox(obj.l140m01eAmt);
                            }
                        });
                    }
                    
                    if(obj["03"]){
                    	if(true){
	                    	var _headItem7 = obj["03"]["headItem7"] || "";
	                    	_M.AllFormData["03"]["headItem7"] = _headItem7;
	                    	if (nowPage == "03") {
	                    		PanelAction03.setheadItem7(_headItem7);
	        				}
                    	}
                    	//=====================
                    	if(true){
                        	var _headItem8 = obj["03"]["headItem8"] || "";
                        	_M.AllFormData["03"]["headItem8"] = _headItem8;
                        	if (nowPage == "03") {
                        		PanelAction03.setheadItem8(_headItem8);
            				}                    		
                    	}
                    }
                    
                    //提示訊息
                    if (obj.tips) {
                        API.showMessage(obj.tips);
                    }
                    //當為聯貸案更新分行逾放比
                    if (_M.AllFormData["04"]["snoKind"] == "30") {
                        _M.doAjax({
                            action: "queryNPL",
                            data: {
                                cntrNo: _M.AllFormData["04"]["cntrNo"]
                            },
                            success: function(obj){
                                _M.AllFormData["04"]["npl"] = obj.msg;
                                _M.AllFormData["04"]["npldate"] = obj.date;
                                $("#npl").val(obj.msg);
                                $("#npldate").val(obj.date);
                            }
                        });
                    }
                
                    if($("#realEstateAfterGrid").length>0){
                    	$("#realEstateAfterGrid").trigger("reloadGrid");
                    }

					$("#cls_gridviewCollateral").trigger('reloadGrid');
					$("#refX1CntrNoListGrid").trigger("reloadGrid");
                }
            });
        }
    },
    l140m01eAmtGrid: null,
    /** 當現請額度大於攤貸總金額時 會出現 grid選擇  將餘額加至哪筆 */
    l140m01eAmtBox: function(amt){
        //L140M01A.message70=聯行攤貸金額尚餘 {0}元，請選擇要將餘額加進何筆資料!
        $("#cls_l140m01eAmtMsg").html(i18n.cls1151s01['L140M01A.message70'].replace("{0}", amt))
        
        if (this.l140m01eAmtGrid) {
            this.l140m01eAmtGrid.setGridParam({
                postData: {
                    tabFormMainId: _M.tabMainId
                },
                search: true
            }).trigger("reloadGrid");
        }
        else {
            this.l140m01eAmtGrid = $("#cls_l140m01eAmtGrid").iGrid({
                handler: _M.ghandle,
                rowNum: 10,
                postData: {
                    formAction: "queryL140m01e",
                    tabFormMainId: _M.tabMainId
                },
                rowNum: 10,
                autowidth: true,
                colModel: [{
                    name: 'shareRate2',
                    hidden: true
                }, {
                    colHeader: i18n.cls1151s01["L140M01E.shareBrId"],//"攤貸分行",
                    name: 'shareBrId',
                    align: "left",
                    width: 110,
                    sortable: true
                }, {
                    colHeader: i18n.cls1151s01["L140M01E.shareAmt"],//"攤貸金額",
                    name: 'shareAmt',
                    width: 160,
                    sortable: true,
                    align: "right",
                    formatter: 'currency',
                    formatoptions: {
                        thousandsSeparator: ",",
                        decimalPlaces: 0
                    }
                }, {
                    colHeader: i18n.cls1151s01["L140M01E.shareRate1"],//"攤貸比例",
                    width: 140,
                    name: 'showRate',
                    align: "right",
                    sortable: false //在 L140M01E 為 @Transient 欄位
                }, {
                    colHeader: i18n.cls1151s01["L140M01A.cntrNo"],//"額度序號",
                    width: 140,
                    name: 'shareNo',
                    sortable: true
                }, {
                    name: 'oid',
                    hidden: true
                }]
            });
        }
        
        $("#cls_l140m01eAmtBox").thickbox({
            title: "",
            width: 600,
            height: 350,
            align: "center",
            valign: "bottom",
            modal: true,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $grid = $("#cls_l140m01eAmtGrid");
                    var id = $grid.getGridParam('selrow');
                    if (id == null || !id) {
                        //action_005=請先選取一筆以上之資料列
                        return API.showErrorMessage(i18n.def['action_005']);
                    }
                    var oid = $grid.getRowData(id).oid;
                    $.thickbox.close();
                    $.ajax({
                        handler: _M.fhandle,
                        action: "saveL140m01eAmt",
                        data: {
                            oid: oid,
                            amt: util.delComma(amt),
                            tabFormMainId: _M.tabMainId
                        },
                        success: function(responseData){
                            if (CLS_BranchAciton && CLS_BranchAciton.grid) {
                                if (responseData && responseData.drc) {
                                    $("#clsItemDscrC").val(responseData.drc);
                                }
                                CLS_BranchAciton.grid.trigger("reloadGrid");
                            }
                        }
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    verifyCntrNoAction: function(json){
    	var my_dfd = $.Deferred();
    	
        $.ajax($.extend({
            handler: _M.fhandle,
            action: "verifyCntrNo",
            data: {
                tabFormMainId: _M.tabMainId
            },
            success: function(json){
                if (json && json.msg && json.msg != "") {                	
                	$("button").addClass(" ui-state-disabled ").attr("disabled", "true");
                	my_dfd.reject();
                    MegaApi.showErrorMessage(json.msg);
                }else{
                	my_dfd.resolve();
                }
            }
        }, json || {}));
        
        return my_dfd.promise();
    },
    resetL140s02aCheckYN: function(){
        //alert("ajax");
        _M.doAjax({
            action: "resetL140s02aCheckYN",
            data: {},
            success: function(obj){
                //L140M01A.msg102=文件資訊中->青年創業貸款註記有經異動，請重新檢視產品資訊中的內容是否正確？
                API.showErrorMessage(i18n.cls1151s01["L140M01A.msg102"]);
            }
        });
    }
};


var RealEstateAction = {
	
	init: function(){
		$("input[name=isRentOrSell]").change(function(){
			RealEstateAction.isShowRentOrSellTipsMsg($(this).val());
		});
		
	},
	realEstateFormDetailData: {
		
	},
	openBox: function(cellvalue, options, data){
		ilog.debug("RealEstateAction:: openBox(isReadOnly="+_M.CLSAction_isReadOnly+") :: data.oid="+data.oid+", data.flag="+data.flag+", data.estateType="+data.estateType);
	
        var $form = $("#realEstateFormDetail");
        $form.reset();
		RealEstateAction.isShowRentOrSellTipsMsg(data.isRentOrSell);
		
		var buttons = {
			"saveData": function(){
	            if ($form.valid()) {
	                $.ajax({
	                    handler: "cls1151m01formhandler",
	                    formId: "empty",
	                    action: "saveL140M01T",
	                    data: $.extend( $form.serializeData(), {
	                        tabFormMainId: _M.tabMainId,
	                        oid: data.oid
	                    }),
	                    
	                    success: function(obj){				
	                    	$("#realEstateAfterGrid").trigger("reloadGrid");
	                    
	                        //saveSuccess=儲存成功
	                        CommonAPI.confirmMessage(i18n.def["saveSuccess"], function(b){
	                            if (b) {
	                                $.thickbox.close();
	                            }
	                        });
	                        $("input[name='is722Flag']").attr('checked', false);
							$("input[name='is722Flag'][value='"+obj.is722Flag+"']").attr("checked", true);
	                    }
	                });
	            }
	            
	        },
	        "close": function(){
	            $.thickbox.close();
	        }
		};
 
	
        $.ajax({
            handler: "cls1151m01formhandler",
            formId: "empty",
            action: "queryL140M01T",
            data: {
                tOid: data.oid,
				tabFormMainId : _M.tabMainId
				
            },
            success: function(obj){

            	if(obj.runOpenBox=="Y"){ // J-107-0234
            		if(true){
            			RealEstateAction.realEstateFormDetailData = obj;
            		}
            		
            		$form.injectData(obj);
    				$("input[name='sectKind']:checked").triggerHandler("change");
//    				$form.find("select").trigger("change");
    				//在［縣市、區、段、里］要加上 init, 才會去     call changAreaValue
                    $form.find("select").trigger("change", "init");
                    
                    if(obj.showItems && obj.showItems.length>0){
            			$.each(obj.showItems, function(idx, jsonItem) {
            				//ilog.debug("queryL140M01T["+jsonItem+"]");
            				if(jsonItem=="D01_View"){
            					$form.find("select#estateAreaId").val(obj.estateAreaId||'').trigger("change");
            					$form.find("select#estateSit3No").val(obj.estateSit3No||'');
                                $form.find("select#estateSit4No").val(obj.estateSit4No||'');
            				}else if(jsonItem=="plant_View"){ // A0#_View
            					// 依 用地類別 {都市、非都市}
                                //ilog.debug("["+jsonItem+"] set value["+(obj.useSect||'')+" , "+(obj.useKind||'')+"]");
                                $form.find("#useSect").val(obj.useSect||'');
                                $form.find("#useKind").val(obj.useKind||'');
            				}else if(jsonItem=="common_View"){
            					$form.find("select#estateAreaIdXX").val(obj.estateAreaIdXX||'').trigger("change");
            					$form.find("select#estateSit3NoXX").val(obj.estateSit3NoXX||'');
                                $form.find("select#estateSit4NoXX").val(obj.estateSit4NoXX||'');
            				}
            			});			
            		}
                        				
    				$form.find("#estateTypeDesc").val(i18n.cls1151s01["L140M01T.estateType."+obj.estateType] ||(obj.estateType));
                    if(true){
                    	$form.find(".estateView").hide();
                		
                		if(obj.showItems && obj.showItems.length>0){
                			$.each(obj.showItems, function(idx, jsonItem) {
                				// A0#_View
                				$form.find("#"+jsonItem+".estateView").show();		
                			});			
                		}
                    }				
    				
                    var var_isTbReadonly = (_openerLockDoc == "1" || data.flag == "N" || _M.CLSAction_isReadOnly=="Y"); 
                    if (var_isTbReadonly) {
                        delete buttons.saveData;
                        //$form.find(":input").filter(".editable").attr("disabled", true).attr("readonly", true);
                        $form.find(".editable").attr("disabled", true).attr("readonly", true);
                        $form.find(".isTurnOver_editable").attr("disabled", true).attr("readonly", true);
                    }else {
                    	$form.find(".editable").attr("disabled", false).attr("readonly", false);
                    	if(obj.isTurnOver=="Y"){
                    		$form.find(".isTurnOver_editable").attr("disabled", false).attr("readonly", false);
                    	}else{
                    		$form.find(".isTurnOver_editable").attr("disabled", true).attr("readonly", true);
                    	}
                    }	
     
                    
                    $("#realEstateDetailThickbox").thickbox({
                        title: i18n.cls1151s01["L140M01T.dial.001"], //"不動產暨72-2相關資訊註記",
                        width: 800,//$("#_estateType").val() == "001" ? 800 : 200,
                        height: 400,//$("#_estateType").val() == "001" ? 400 : 150,
                        modal: true,
                        readOnly: var_isTbReadonly,
                        i18n: i18n.def,
                        buttons: buttons
                    });
            	}//end  obj.runOpenBox            
            }
        });
	},
	quickAddRealEstate: function(){		
		var $grid = $("#realEstateAfterGrid");
		if($grid.length>0){
			var buttons = {};
	    	buttons[i18n.def.newData] = function(){				
	            $.ajax({
	                handler: "cls1151m01formhandler",
	                action: "quickAddEstateDatas",
	                data: $.extend($("#realEstateForm").serializeData(), 
	                		{ 'tabFormMainId': _M.tabMainId
	                		, 'isInstalment': $("[name=isInstalment]:radio:checked").val()
	                		}
	                ),
	                success: function(obj){
	                	if(obj.rc=="Y"){
		                    $.thickbox.close();
		                    $grid.trigger("reloadGrid");
		                    $("input[name='is722Flag']").attr('checked', false);
	                        $("input[name='is722Flag'][value='" + obj.is722Flag + "']").attr("checked", true);
	                	}
	                }
	            });	    		
	    	};
			buttons[i18n.def.close] = function(){
	    		$.thickbox.close()
	    	};
	    	
	    	if(true){
	    		var _isInstalment = $("[name=isInstalment]:radio:checked").val();
	    		if(_isInstalment=="Y"){
	    			$("#realEstateForm").find("[name=inputMCntrNo]").val("");
	    			$("#realEstateForm_div_mCntrNo").hide();
	    		}else{
	    			$("#realEstateForm_div_mCntrNo").show();
	    		}
	    		
				$("#realEstateThickbox").thickbox({
					title: i18n.cls1151s01["L140M01T.dial.001"], //"不動產暨72-2相關資訊註記",
		    		modal: false,
		    		width:600,
		    		height:500,
		    		open: function(){
		    			
		    		},
		    		buttons:buttons
		    	})
			}
			//===========
			$grid.trigger("reloadGrid");
        }
	},
	deleteRealEstate: function(){	
		var $grid = $("#realEstateAfterGrid");
		if($grid.length>0){
			var gridId = $grid.getGridParam('selrow');			
			if (!gridId) {
				return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
			}
			var data = $grid.getRowData(gridId);
			CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(result){
	            if (result) {
	                $.ajax({
	                    handler: "cls1151m01formhandler",
	                    action: "deleteL140m01t",
	                    data: {
	                        tOid: data.oid,
	                        tabFormMainId: _M.tabMainId
	                    },
	                    success: function(obj){
	                    	$grid.trigger("reloadGrid");
	                    	$("input[name='is722Flag']").attr('checked', false);
	                        $("input[name='is722Flag'][value='" + obj.is722Flag + "']").attr("checked", true);
	                    }
	                });
	            }
		    }); // end confirmMessage
        }
	},
	getSITE3: function(fCity, fZip, loanBuild){
        if (!fCity || !fZip) 
            return {};
        var SITE3 = null;
        $.ajax({
            handler: "lmscommonformhandler",
            action: "querySIET3",
            type: 'post',
            async: false,
            formId: "empty",
            data: {
                fCity: fCity,
                fZip: fZip
            },
            success: function(obj){
                if (loanBuild == "Y") {
                    SITE3NO = obj['SITE3NO'];
                }
                else {
                    SITE3 = obj['SITE3'];
                }
            }
        });
        return SITE3;
    },
    getSITE4: function(fCity, fZip, loanBuild){
        if (!fCity || !fZip) 
            return {};
        var SITE4 = null;
        $.ajax({
            handler: "lmscommonformhandler",
            action: "querySIT4NO",
            type: 'post',
            async: false,
            formId: "empty",
            data: {
                LOCATE1DESC: fCity,
                LOCATE2DESC: fZip
            },
            success: function(obj){
                if (loanBuild == "Y") {
                    SITE4NO = obj['SITE4NO'];
                }
                else {
                    SITE4 = obj['SITE4'];
                }
            }
        });
        return SITE4;
    },
    changCityValue: function(tCity, tArea, tSite3, tSite4, defaultSelect, isClear){

        var value = tCity.val();
        var obj = QueryCityCode.getCode("2", value);
        
        
        tArea.setItems({
            item: obj,
            value: RealEstateAction.realEstateFormDetailData['estateAreaId'] || ""
        });
        
        if (isClear) {
            tSite3.html(defaultSelect);
            tSite4.html(defaultSelect);
            RealEstateAction.realEstateFormDetailData['estateAreaId'] = "";
            RealEstateAction.realEstateFormDetailData['estateSit3No'] = "";
            RealEstateAction.realEstateFormDetailData['estateSit4No'] = "";
        }
    },
    changAreaValue : function (tCity, tArea, tSite3, tSite4, defaultSelect, isClear){
//    	ilog.debug("call changAreaValue["+tCity+","+tArea+","+tSite3+","+tSite4+"]");
        if (isClear) {
            
        }
        
        tSite3.setItems({
            item: RealEstateAction.getSITE3(tCity.find(":selected").text(), tArea.find(":selected").text(), "N"),
            value: util.addZeroBefore($.trim(RealEstateAction.realEstateFormDetailData['estateSit3No']).replace(",", "") || "", 4)            
        });
        
        tSite4.setItems({
            item: RealEstateAction.getSITE4(tCity.find(":selected").text(), tArea.find(":selected").text(), "N"),
            value:RealEstateAction.realEstateFormDetailData['estateSit4No'] || ""
        });
        
    },   
    dummy : function() {
    },
	
	isShowRentOrSellTipsMsg: function(isRentOrSell){

		$("#isShowRentOrSellTipsMsg").hide();
		
		if(isRentOrSell == 'Y'){
			$("#isShowRentOrSellTipsMsg").show();
		}
	}
};


var L140m01yAction = {
	openBox: function(cellvalue, options, data){
		ilog.debug("L140m01yAction:: openBox(isReadOnly="+_M.CLSAction_isReadOnly+") :: data.oid="+data.oid+", data.refType="+data.refType);	
	},
	choseL140m01yRefType: function(paramMap){
		ilog.debug("choseL140m01yRefType{codeType:"+paramMap.codeType+"}");
		var my_dfd = $.Deferred();
		var _id = "_div_btn_choseL140m01yRefType";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).size() == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;'>");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table width='100%' >");
			dyna.push("	<tr><td nowrap style='padding-right:24px;'><input type='radio' id='choseL140m01yRefType' name='choseL140m01yRefType' /></td></tr>");
			dyna.push(" </table>");
			dyna.push(" &nbsp;");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		     
		     var form = $("#_div_btn_choseL140m01yRefType_form");
		     $.ajax({
		    	 type: 'post', handler: "cls1151m01formhandler", async: false, data:$.extend({'formAction':'load_combo_list'}, paramMap),
		         success: function(json_combos){
//		        	 $("#choseL140m01yRefType").setItems({ item: json_combos.rtnData,format: "{key}",size: 1});
		        	 form.find("input[name='choseL140m01yRefType']").setItems({ item: json_combos.rtnData,format: "{key}",size: 1});
		         }
		     });
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	       title: i18n.cls1151s01["L140M01Y.label.desc"],
	       width: 600,
           height: 480,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
                   if (!$("#"+_form).valid()) {
                   	   CommonAPI.showMessage(i18n.def["action_005"]);
                       return;
                   }
                   var val_choseL140m01yRefType = $("#"+_form).find("[name='choseL140m01yRefType']:checked").val();
                   if(val_choseL140m01yRefType){                	   
                   }else{
                	   CommonAPI.showMessage(i18n.def["action_005"]);
                       return;
                   }
                                      
                  $.thickbox.close();
                  my_dfd.resolve( {'l140m01yRefType':val_choseL140m01yRefType } );	
               },
               "cancel": function(){
            	   $.thickbox.close();
               }
           }
		});
		return my_dfd.promise();
	},
	choseL140M01A_projClass_X1Y1Reason: function(){
		var my_dfd = $.Deferred();
		var _id = "_div_btn_choseL140M01A_projClass_X1Y1Reason";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).size() == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;'>");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table width='100%' >");
			dyna.push("	<tr><td nowrap style='padding-right:24px;'><input type='radio' id='choseL140m01yRefType_L140M01A_projClass_X1Y1Reason' name='choseL140m01yRefType_L140M01A_projClass_X1Y1Reason' /></td></tr>");
			dyna.push(" </table>");
			dyna.push(" &nbsp;");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		     
		     var form = $("#_div_btn_choseL140M01A_projClass_X1Y1Reason_form");
		     $.ajax({
		    	 type: 'post', handler: "cls1151m01formhandler", async: false, data:{'formAction':'load_combo_list', 'codeType':'L140M01A_projClass_X1Y1Reason'},
		         success: function(json_combos){	        
		        	 form.find("input[name='choseL140m01yRefType_L140M01A_projClass_X1Y1Reason']").setItems({ item: json_combos.rtnData,format: "{key}",size: 1});
//		         	$("#choseL140m01yRefType_L140M01A_projClass_X1Y1Reason").setItems({ item: json_combos.rtnData,format: "{key}",size: 1});
		         }
		     });
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	       title: i18n.cls1151s01["L140M01Y.refType.L140M01A_projClass_X1Y1"],
	       width: 600,
           height: 280,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
                   if (!$("#"+_form).valid()) {
                   	   CommonAPI.showMessage(i18n.def["action_005"]);
                       return;
                   }
                   var val_choseL140m01yRefType_L140M01A_projClass_X1Y1Reason = $("#"+_form).find("[name='choseL140m01yRefType_L140M01A_projClass_X1Y1Reason']:checked").val();
                   if(val_choseL140m01yRefType_L140M01A_projClass_X1Y1Reason){                	   
                   }else{
                	   CommonAPI.showMessage(i18n.def["action_005"]);
                       return;
                   }
                                      
                  $.thickbox.close();
                  my_dfd.resolve( {'refValue':val_choseL140m01yRefType_L140M01A_projClass_X1Y1Reason } );	
               },
               "cancel": function(){
            	   $.thickbox.close();
               }
           }
		});
		return my_dfd.promise();
		
	},
	choseC122M01A_for_ELF459_srcflag_1: function(){
		var my_dfd = $.Deferred();
		var _id = "_div_area_C122M01A_for_ELF459_srcflag_1";
		var _gridid = "_div_grid_C122M01A_for_ELF459_srcflag_1";
		 	
		if ($("#"+_id).size() == 0){		     
			var dyna = [];
   		 	dyna.push("<div id='"+_id+"' style='display:none;'>");
   		       dyna.push("<div id='"+_gridid+"' >");
   		       dyna.push("</div>");				
			dyna.push("</div>");
				
			$('body').append(dyna.join(""));	 
			//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			$("#"+_gridid).iGrid({
				handler : 'cls1151gridhandler',
				height : 160,
				needPager: false,
				postData : {
		        	'custId': $("#custId").val(),
		            'formAction': "queryC122M01A_for_ELF459_srcflag_1"
				},			
				colModel : [ {
		            colHeader: i18n.cls1220m04['C122M01A.custId'],
		            align: "left", width: 80, sortable:true, name: 'custId'
				}, {
		            colHeader: i18n.cls1220m04['C122M01A.custName'],
		            align: "left", width: 100, sortable: true, name: 'custName'
				}, {
		            colHeader: i18n.cls1220m04['C122M01A.applyTS'],
		            align: "left", width: 120, sortable: true, name: 'applyTS'
				}, {
		        	colHeader: i18n.cls1220m04['grid.ownBrId'],
		            align: "left", width: 80, sortable: true, name: 'ownBrId'
				}, {
		        	colHeader: i18n.cls1220m04['grid.IncomType'],
		            align: "left", width: 60, sortable: true, name: 'IncomType'
				},{
		        	colHeader: i18n.cls1220m04['grid.applyKind'],
		            align: "left", width: 100, sortable: true, name: 'applyKind'
				}, {
		        	colHeader: i18n.cls1220m04['C122M01A.ploanCaseNo'],
		            align: "left", width: 120, sortable: true, name: 'ploanCaseId'
		        }
		        , { name: 'usePlan', hidden: true }
				, { name: 'oid', hidden: true }
		        , { name: 'mainId', hidden: true }
		        ]
			});
		}
		
		if ($("#"+_id).size() > 0){
			//clear data
			$("#"+_gridid).trigger("reloadGrid");
			
			$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
		       title: i18n.cls1151s01["L140M01Y.refType.ELF459_srcflag_1"],
		       width: 760,
	           height: 300,
	           align: "center",
	           valign: "bottom",
	           modal: false,
	           i18n: i18n.def,
	           buttons: {
	               "sure": function(){
	            	   var gridId = $("#"+_gridid).getGridParam('selrow');			
		       			if (!gridId) {
		       				return CommonAPI.showMessage(i18n.def["action_005"]);
		       			}
		       			var data = $("#"+_gridid).getRowData(gridId);
		       			if(data){
		       				$.ajax({handler: "cls1151m01formhandler",action: "get_c122m01a_data",data: {'oid': data.oid}
			    				,success: function(jsonRtnData){
			    					if(jsonRtnData && jsonRtnData.c122m01a){
			    						if(jsonRtnData.c122m01a.ownBrId==userInfo.unitNo){
			    							my_dfd.resolve( {'refMainId': data.mainId , 'refOid': data.oid, 'usePlan': data.usePlan} );
			    			       			$.thickbox.close();			
			    						}else{
			    							CommonAPI.confirmMessage("非本分行案件，是否繼續？", function(b){
			    					            if (b) {
			    					            	my_dfd.resolve( {'refMainId': data.mainId , 'refOid': data.oid} );	
					    			       			$.thickbox.close();		
			    					            }
			    					        });
			    						}
			    					}
			    				}	
			    			});
			       			
		       			}
	               },
	               "cancel": function(){
	            	   $.thickbox.close();
	               }
	           }
			});
			
		}
		return my_dfd.promise();
	},
	injectL140m01yColumn: function(obj){
		var my_dfd = $.Deferred();
		//=======================
		//一般戶
		if(obj.l140m01yRefType=="ELF459_srcflag_1"){
			L140m01yAction.choseC122M01A_for_ELF459_srcflag_1().done(function(rtnJSON){
				my_dfd.resolve({'refType':obj.l140m01yRefType, 'refValue':'', 'refModel':'C122M01A', 'refMainId':rtnJSON.refMainId, 'refOid':rtnJSON.refOid, 'usePlan':rtnJSON.usePlan});
			});			
		}else if(obj.l140m01yRefType=="L140M01A_projClass_X1Y1"){
			L140m01yAction.choseL140M01A_projClass_X1Y1Reason().done(function(rtnJSON){
				my_dfd.resolve({'refType':obj.l140m01yRefType, 'refValue':rtnJSON.refValue, 'refModel':'', 'refMainId':'', 'refOid':''});	
			});
		}else if(obj.l140m01yRefType=="L140S01A_custPos_G"){
			L140m01yAction.choseL140M01A_L140S01A_custPos_G().done(function(rtnJSON){
				my_dfd.resolve({'refType':obj.l140m01yRefType, 'refValue':rtnJSON.refValue, 'refModel':'', 'refMainId':'', 'refOid':'', 'n12_1Flag':rtnJSON.n12_1Flag});	
			});
		}
		//=======================
		//團貸母戶
		if(obj.l140m01yRefType=="GRP_L140S02F_ratePlan_24"){
			my_dfd.resolve({'refType':obj.l140m01yRefType, 'refValue':'', 'refModel':'', 'refMainId':'', 'refOid':''});
		}
		return my_dfd.promise();
	},
	addL140m01y: function(){	
		$.ajax({handler: "cls1151m01formhandler",action: "get_l120m01a_data",data: {'tabFormMainId': _M.tabMainId}
			,success: function(jsonL120M01A){
				var choseL140m01yRefTypeParamMap = {};
				if(jsonL120M01A.l120m01a.docCode == "5"){
					choseL140m01yRefTypeParamMap["codeType"] = "L140M01Y_refType_docCode5";
				}else{
					choseL140m01yRefTypeParamMap["codeType"] = "L140M01Y_refType_docCode1";
				}
				//~~~~~~~~
				L140m01yAction.choseL140m01yRefType(choseL140m01yRefTypeParamMap).done(function(objL140m01yRefType){
					L140m01yAction.injectL140m01yColumn(objL140m01yRefType).done(function(l140m01yFormData){
						ilog.debug("addL140m01y{refType:"+l140m01yFormData.refType+", refValue:"+l140m01yFormData.refValue
								+", refModel:"+l140m01yFormData.refModel+", refMainId:"+l140m01yFormData.refMainId+", refOid:"+l140m01yFormData.refOid
								+", usePlan:"+l140m01yFormData.usePlan+"}");
						
						$.ajax({
				            handler: "cls1151m01formhandler",
				            action: "addL140m01y",
				            data: $.extend({'tabFormMainId': _M.tabMainId}, l140m01yFormData)
				            ,success: function(json){
				            	$("#l140m01yGrid").trigger("reloadGrid");
				            }
				        });	
					});
					
				});
			}
		});
	},
	delL140m01y: function(){	
		var $l140m01yGrid = $("#l140m01yGrid");
		if($l140m01yGrid.length>0){
			var gridId = $l140m01yGrid.getGridParam('selrow');			
			if (!gridId) {
				return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
			}
			var data = $l140m01yGrid.getRowData(gridId);
			CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(result){
	            if (result) {
	                $.ajax({
	                    handler: "cls1151m01formhandler",
	                    action: "deleteL140m01y",
	                    data: {
	                        tOid: data.oid,
	                        tabFormMainId: _M.tabMainId
	                    },
	                    success: function(obj){
	                    	$l140m01yGrid.trigger("reloadGrid");
	                    }
	                });
	            }
		    }); // end confirmMessage
        }
	},
    dummy : function() {
    },
    choseL140M01A_L140S01A_custPos_G: function(){
		var my_dfd = $.Deferred();
		var _id = "_div_btn_choseL140M01A_L140S01A_custPos_G";
		var _form = _id+"_form";
		var content=i18n.cls1151s01["L140M01A.msg166"].replace("{0}","<input type='number' style='width:60px' name='L140m01yRefType_L140S01A_custPos_G_ownHouse' />").replace("{1}","<input type='number' style='width:60px' name='L140m01yRefType_L140S01A_custPos_G_liveHouse' />");
		//J-112-0260  新增「不符銀行法12-1自用住宅規範」選項,若勾選則不受「不得徵連帶保證人」之限制
		var content2 = i18n.cls1151s01["L140M01A.msg176"].replace("{0}","<input type='checkbox' name='L140m01yN12_1Flag_L140S01A_custPos_G_ownHouse'/>");
		if ($("#"+_id).size() == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;'>");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table width='100%' >");
			dyna.push("	<tr><td nowrap style='padding-right:24px;'>"+ content +"</td></tr>");
			dyna.push("	<tr><td nowrap style='padding-right:24px;'>" + "<label style='letter-spacing:0px;cursor:pointer;'>" + content2 + "</label>" + "</td></tr>");
			dyna.push(" </table>");
			dyna.push(" &nbsp;");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		     
//		     $.ajax({
//		    	 type: 'post', handler: "cls1151m01formhandler", async: false, data:{'formAction':'load_combo_list', 'codeType':'L140S01A_custPos_G'},
//		         success: function(json_combos){
//		         	$("#choseL140m01yRefType_L140S01A_custPos_G").setItems({ item: json_combos.rtnData,format: "{key}",size: 1});
//		         }
//		     });
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	       title: i18n.cls1151s01["L140M01Y.refType.L140S01A_custPos_G"],
	       width: 600,
           height: 280,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
                   if (!$("#"+_form).valid()) {
                   	   CommonAPI.showMessage(i18n.def["action_005"]);
                       return;
                   }
                   var L140m01yRefType_L140S01A_custPos_G_ownHouse = $("#"+_form).find("[name='L140m01yRefType_L140S01A_custPos_G_ownHouse']").val();
                   var L140m01yRefType_L140S01A_custPos_G_liveHouse = $("#"+_form).find("[name='L140m01yRefType_L140S01A_custPos_G_liveHouse']").val();
                   //J-112-0260 新增「不符銀行法12-1自用住宅規範」選項,若勾選則不受「不得徵連帶保證人」之限制
                   var L140m01yN12_1Flag_L140S01A_custPos_G_ownHouse = 'N';
                   if($("#"+_form).find("[name='L140m01yN12_1Flag_L140S01A_custPos_G_ownHouse']").attr('checked')){
                	   L140m01yN12_1Flag_L140S01A_custPos_G_ownHouse = 'Y';
                   }
                   if(L140m01yRefType_L140S01A_custPos_G_liveHouse!='' && L140m01yRefType_L140S01A_custPos_G_ownHouse!=''){  
                	   if(L140m01yRefType_L140S01A_custPos_G_ownHouse<0 || L140m01yRefType_L140S01A_custPos_G_liveHouse<0){
                		   CommonAPI.showMessage(i18n.def["val.min"].replace("{0}","0"));
                           return;
                	   }
                   }else{
                	   CommonAPI.showMessage(i18n.def["action_005"]);
                       return;
                   }
                                      
                  $.thickbox.close();
                  my_dfd.resolve( {'refValue':L140m01yRefType_L140S01A_custPos_G_ownHouse+','+L140m01yRefType_L140S01A_custPos_G_liveHouse, 'n12_1Flag':L140m01yN12_1Flag_L140S01A_custPos_G_ownHouse } );	
               },
               "cancel": function(){
            	   $.thickbox.close();
               }
           }
		});
		return my_dfd.promise();
		
	}
};


function l140m01yGridClickOpenDoc(cellvalue, options, rowObject){
	
	if(rowObject && rowObject.refModel && rowObject.refOid){
		ilog.debug("l140m01yGridClick{refModel:"+rowObject.refModel+", refOid:"+rowObject.refOid+"}");
		if(rowObject.refModel=="C122M01A"){
			$.ajax({handler: "cls1151m01formhandler",action: "get_c122m01a_data",data: {'oid': rowObject.refOid}
				,success: function(jsonRtnData){
					if(jsonRtnData && jsonRtnData.c122m01a){
						var _l140m01yGridClick_openURL = "";
						
						if(jsonRtnData.c122m01a.applyKind=="P"){
							_l140m01yGridClick_openURL = encodeURI('../lms/cls1220m04/01'); 
						}else if(jsonRtnData.c122m01a.applyKind=="E"){
							_l140m01yGridClick_openURL = encodeURI('../lms/cls1220m05/01');
						}else if(jsonRtnData.c122m01a.applyKind=="I" || jsonRtnData.c122m01a.applyKind=="J"){
							_l140m01yGridClick_openURL = encodeURI('../lms/cls1220m04/01');
						}else if(jsonRtnData.c122m01a.applyKind=="O"){
							_l140m01yGridClick_openURL = encodeURI('../lms/cls1220m05/01');
						}
						if(_l140m01yGridClick_openURL==""){
							
						}else{
							$.form.submit({
								url : _l140m01yGridClick_openURL,
								data : {
									'noOpenDoc':true,
									'oid': jsonRtnData.c122m01a.oid,
					                'mainOid': jsonRtnData.c122m01a.oid,
					                'mainId': jsonRtnData.c122m01a.mainId,
					                'mainDocStatus': jsonRtnData.c122m01a.docStatus
					            },
					            target: jsonRtnData.c122m01a.oid
							});
						}
					}
				}	
			});
		}
	}
}

var CLS115Action = _M;
$(function(){
    CLS115Action.openBox(responseJSON, {
        itemType: responseJSON.itemType,
        CLSAction_isReadOnly: responseJSON.CLSAction_isReadOnly
    });
    
    var adcGrid = $("#queryAdcGridview").iGrid({
	    handler: 'cls1151gridhandler',
        height: 300,
        width: 350,
        autowidth: false,
        action: "queryAdcList",
        postData: {
        },
        rowNum: 15,
        sortname: "adcCaseNo",
        colModel: [{
            colHeader: i18n.cls1151s01["L140MM3A.adcCaseNo"],
            align: "center",
            name: 'adcCaseNo'
        }]
    });
    
    $("#newAdcCaseNoBt").click(function(){
        $("#newAdcCaseNoBox").thickbox({
            title: i18n.cls1151s01["L140MM3A.adcCaseNo"],
            width: 650,
            height: 300,
            modal: true,
            i18n: i18n.def,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var type = $("input[name=newAdcCaseNoRadio]:checked").val();
                    var adcCaseNo = $("#adcCaseNo").val();
                    if ($.trim(adcCaseNo) != "" && type != "del") {
                        return CommonAPI.showErrorMessage(i18n.cls1151s01["L140MM3A.message14"]);
                    }
                    if(type == "new") {
                        $.ajax({
                            handler: _M.fhandle,
                            action: "queryNewAdcCaseNo",
                            data: {
                                oid: responseJSON.oid,
                                mainId:responseJSON.CaseMainId
                            },
                            success: function(obj){
                                $("#adcCaseNo").val(obj.adcCaseNo);
                                $.thickbox.close();
                            }
                        });
                    } else if(type == "org") {
                        var orgAdcCaseNo = $("#orgAdcCaseNo").val();
                        if ($.trim(orgAdcCaseNo) == "") {
                            return CommonAPI.showErrorMessage(i18n.cls1151s01["checkInput"]);
                        }
                        $.ajax({
                            handler: _M.fhandle,
                            action: "chkAdcCaseNo",
                            data: {
                                adcCaseNo: orgAdcCaseNo,
                                oid: responseJSON.oid,
                                save: true
                            },
                            success: function(obj){
                                if (obj.msg && obj.msg != "") {
                                    return API.showErrorMessage(obj.msg);
                                } else {
                                    $("#adcCaseNo").val(orgAdcCaseNo);
                                    $.thickbox.close();
                                }
                            }
                        });
                    } else if(type == "del") {
                        $("#adcCaseNo").val('');
                        $.thickbox.close();
                    }
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    });

    $("#queryAdcCaseNoBt").click(function(){
        $("#queryCustId").val($("#custId").val());
        $("#queryDupNo").val($("#dupNo").val());
        $("#queryAdcCaseNoBox").thickbox({
            title: i18n.cls1151s01["L140MM3A.adcCaseNo"],
            width: 30,
            height: 100,
            modal: true,
            i18n: i18n.def,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var queryType = $("input[name=queryType]:checked").val();
                    if(queryType == "1") {
                        var queryCustId = $("#queryCustId").val();
                        var queryDupNo = $("#queryDupNo").val();
                        if ($.trim(queryDupNo) == "") {
                            queryDupNo = "0";
                        }
                        if ($.trim(queryCustId) == "") {
                            return CommonAPI.showErrorMessage(i18n.cls1151s01["checkInput"]);
                        }
                    } else if(queryType == "2") {
                        var queryCntrNo = $("#queryCntrNo").val();
                        if ($.trim(queryCntrNo) == "") {
                            return CommonAPI.showErrorMessage(i18n.cls1151s01["checkInput"]);
                        }
                    }
                    $.thickbox.close();
                    adcGrid.jqGrid("setGridParam", {
                        postData: {
                            queryType: queryType,
                            queryCustId: queryCustId,
                            queryDupNo: queryDupNo,
                            queryCntrNo: queryCntrNo
                        },
                        search: true
                    }).trigger("reloadGrid");
                    $("#queryAdcThickBox").thickbox({
                       title: i18n.cls1151s01["checkSelect"]+i18n.cls1151s01["L140MM3A.adcCaseNo"],
                       width: 400,
                       height: 450,
                       align: "center",
                       valign: "bottom",
                       modal: false,
                       i18n: i18n.def,
                       buttons: {
                            "sure": function(){
                                var row = adcGrid.getGridParam('selrow');
                                if (!row) {
                                    return CommonAPI.showMessage(i18n.def["grid_selector"]);
                                }
                                if (row.length > 1) {
                                    CommonAPI.showMessage(i18n.cls1151s01["L140MM3A.error1"]);
                                } else {
                                    CommonAPI.confirmMessage(i18n.cls1151s01["L140MM3A.message15"], function(b){
                                        if (b) {
                                            var adcCaseNo = $("#adcCaseNo").val();
                                            if ($.trim(adcCaseNo) != "") {
                                                return CommonAPI.showErrorMessage(i18n.cls1151s01["L140MM3A.message14"]);
                                            }
                                            var data = adcGrid.getRowData(row);
                                            $("#adcCaseNo").val(data.adcCaseNo);
                                            $.thickbox.close();
                                        }
                                    });
                                }
                            },
                            "close": function(){
                                $.thickbox.close();
                            }
                        }
                    });
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    });

    $("input[name=newAdcCaseNoRadio]").click(function(){
        if ("org" == $(this).val()) {
            $("#originaladcInput").show();
        } else {
            $("#originaladcInput").hide();
            $("#orgAdcCaseNo").val('');
        }
    });

    $("input[name=queryType]").click(function(){
        var queryType = encodeURI($(this).val());
        $(".queryInput").hide();
        $("#queryInput_" + queryType).show();
    });
});

function changedistanceWord(number, mainWidth, otherWidth){
    if ($('#pageNum' + number).val() == '0') {
        $('#itemDscr' + number).attr('distanceWord', mainWidth);
    }
    else {
        $('#itemDscr' + number).attr('distanceWord', otherWidth);
    }
}

function initMarketingNotes(){
	
	var objs = API.loadCombos(["L140M01A_marketingNotes"]);
    $("#marketingNotes").setItems({
        space: false,
        item: objs.L140M01A_marketingNotes,
        format: "{key}"
    });
	
	$("input[name=marketingNotes]").change(function(){
		$("input[name=marketingNotes]:checked").each(function(){
			if($(this).val() == 'N'){
				$("input[name=marketingNotes]").not($(this)).attr('checked', false);
			}
		});
	});
}

var IntroductionSource = {
	
	initEvent: function(){
		$("#introduceSrc").change(function(){
			IntroductionSource.change();
		});

		IntroductionSource.loadGridInfo();

		$("#importRealEstateAgentInfo").click(function(){
			IntroductionSource.openBox();
		})
		
		$("#importCustOrComButton").click(function(){
			IntroductionSource.importCustomerOrCompanyInfo();
		})
		
		$("input[name=packLoan]").change(function(){
			IntroductionSource.processBatchHouseholdLoan($(this).val());
		});
		
		$("#selectBranchLink").click(function(){
			IntroductionSource.selectBranch();
		});
	},
	
	show: function(introduceSrc){

		switch (introduceSrc) {
		    case '1':
		        $("#employeeDiv").show();
		        break;
			case '2':
		        $("#realEstateAgentDiv").show();
				$("#introducerNameDiv").show();
		        break;
		    case '3':
		        $("#megaSubCompanyDiv").show();
		        break;
			case '4':
				$("#introducerNameDiv").show();
				break;
		    case '5':
				$("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
		        break;
		    case '6':
		        $("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
				break;
			case '8':
		        $("#buildCaseDiv").show();
		        break;
			case '9':
				$("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
				break;
			case 'A':
				$("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
				break;
		}
	},
	
	change: function(){
		
		$("#employeeDiv").hide();
		$("#realEstateAgentDiv").hide();
		$("#megaSubCompanyDiv").hide();
		$("#customerOrCompanyDiv").hide();
		$("#importCustOrComSpan").hide();
		$("#buildCaseDiv").hide();
		$("#introducerNameDiv").hide();
		
		//行員引介
		$("#employeeDiv").find("input:text").val("");
		//房仲業者引介
		$("#realEstateAgentDiv").find("input:text").val("");
		//金控子公司員工引介
		$("#megaSubCompanyDiv").find("select").val("");
		$("#megaSubCompanyDiv").find("input:text").val("");
		//往來企金戶所屬員工, 本行客戶引介
		$("#customerOrCompanyDiv").find("input:text").val("");
		//分行自辦小規模整批貸款
		$("#buildCaseDiv").find("input:text").val("");
		//引介人姓名
		$("#introducerNameDiv").find("input:text").val("");
		
		IntroductionSource.show($("#introduceSrc").val());
	},
	
	loadGridInfo: function(){
		
		$("#realEstateGrid").iGrid({
			height: 100,
			handler: "cls1151gridhandler",
			sortname: 'estateType|estateSubType',
			sortorder: 'asc|asc',
			action: "importRealEstateAgentInfo",
			postData: {
				tabFormMainId: _M.tabMainId,
				flag: "N"
			},
			loadComplete : function() {    				
				$('#realEstateGrid tr').click(function(e) {
					//click 帶入資料至畫面後 go to top
					e.preventDefault();
				});
			},
			colModel: [{
				colHeader: i18n.cls1151s01["L140M01A.agntNo"],//房仲代號
				width: 20,
				name: 'AGNTNO',
				sortable: true,
				formatter: 'click',
				onclick: function(cellValue, options, rowObject){
					$("#agntNo").val(rowObject.AGNTNO);//引介房仲代號
					$("#agntChain").val(rowObject.AGNTCHAIN);//引介房仲連鎖店類型
					$("#dealContractNo").val(rowObject.CONTRACTNO);//買賣合約書編號
					$("#introducerName").val(rowObject.AGNTNAME);//引介房仲姓名
					$("#licenseYear").val(rowObject.LICENSEYEAR);//房仲證書(明)字號-年
					$("#licenseWord").val(rowObject.LICENSEWORD);//房仲證書(明)字號-年登字
					$("#licenseNumber").val(rowObject.LICENSENUMBER);//房仲證書(明)字號-編號
					$("#hou").val(rowObject.HOU);//號
					$.thickbox.close();
				}
			}, {
				colHeader: i18n.cls1151s01["L140M01A.agntName"],//房仲名稱
				width: 40,
				name: 'AGENTNAME',
				sortable: true
			}, {
				colHeader: i18n.cls1151s01["L140M01A.agntChain"],//連鎖店類型
				name: 'AGNTCHAIN',
				align: 'center',
				width: 30
			}, {
				colHeader: i18n.cls1151s01["L140M01A.agntChainName"],//連鎖店類型名稱
				name: 'CHAINNAME',
				align: 'center',
				width: 35
			}, {
				colHeader: i18n.cls1151s01["L140M01A.dealContractNo"],//買賣合約書編號
				name: 'CONTRACTNO',
				width: 35
			},
			{
				colHeader: i18n.cls1151s01["L140M01A.introAgntName"],//引介房仲姓名
				name: 'AGNTNAME',
				width: 30
			},{
				colHeader: i18n.cls1151s01["L140M01A.agentCertNo"],//引介房仲證書(明)字號
				name: 'AGENTCERTNO',
				width: 30
			}
			,{name:'LICENSEYEAR', hidden:true}
			,{name:'LICENSEWORD', hidden:true}
			,{name:'LICENSENUMBER', hidden:true}
			,{name:'HOU', hidden:true}
			]
		});
	},

	openBox: function(){

		var buttons = {};
		buttons[i18n.def.close] = function(){				
			$.thickbox.close();
        };
		
       	$("#openBox_realEstateIntroduction").thickbox({
            title: i18n.cls1151s01['page01.title.introductionRealEstateAgentInfo'],
            width: 600,
            height: 250,
            modal: true,
			align: "center",
			valign: 'bottom',
            i18n: i18n.def,
            buttons: buttons
        });
	},
	
	importCustomerOrCompanyInfo: function(){

		AddCustAction.open({
    		handler: 'cls1151m01formhandler',
			action : 'importCustomerOrCompanyInfo',
			data : {
            },
			callback : function(json){					
            	// 關掉 AddCustAction 的 
            	$.thickbox.close();					
				$("#introCustId").val(json.introCustId);
				$("#introDupNo").val(json.introDupNo);
				$("#introCustName").val(json.introCustName);
				var introduceSrc = $("#introduceSrc").val();
				if(introduceSrc == '9' || introduceSrc == 'A'){
					$("#introducerName").val(json.introCustName)
				}
			}
		});
	},
	
	processBatchHouseholdLoan: function(isBatchLoan){

//		$("#introduceSrc").attr("disabled", false);
		if(isBatchLoan == "Y"){
			$("#introduceSrc").val("7").trigger('change');
//			$("#introduceSrc").attr("disabled", true);
		}
	},
	
	selectBranch: function(){
		
		var form = $("#CLS1151Form01");
		$.ajax({
            handler: "cls1220m10formhandler",
            action: "getAllOrgBrId",
         	success:function(responseData){
    			if(responseData.Success){ //成功
    				form.find("#selectBranch").setItems({
                        item: responseData.childMap,
                        space: true,
                        format: "{value} - {key}"
                    });
    			}
    		}
        }).done(function(){
			$("#selectBranchDiv").thickbox({
  	       	 	title: i18n.cls1151s01['page01.title.selectBranch'], width: 350, height: 100, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
  	            buttons: {
  	                "sure": function(){
  	                	var branchStr = $("#selectBranch option:selected").text();
						var branchArray = branchStr.split("-");
						$("#megaEmpBrNo").val($.trim(branchArray[0]));
						$("#megaEmpBrNoName").val($.trim(branchArray[1]));
						$.thickbox.close();
  	                },
  	                "cancel": function(){
  	               		$.thickbox.close();
  	                }
  	            }
  	        });
		});
	}
}

var IndustryTypeInfo = {
	
	init : function($boxContex){
		
		$boxContex.find("#queryCompanyIndustryCodeButton").click(function(){
            IndustryTypeInfo.openBox();
        });
		
		IndustryTypeInfo.initGridInfo();
		
		$boxContex.find("#queryIndustryCodeButton").click(function(){
			IndustryTypeInfo.loadGridInfo();
        });
	},
	
	openBox : function(){
		
		var buttons = {};
		buttons[i18n.def.close] = function(){				
			$.thickbox.close();
	    };
		
		$("#openBox_queryIndustryCode").thickbox({
	        title: i18n.cls1151s01['page01.title.industryCodeInfo'],
	        width: 450,
	        height: 250,
	        modal: true,
			align: "center",
			valign: 'bottom',
	        i18n: i18n.def,
	        buttons: buttons
	    });
	},
	
	initGridInfo : function(){
		
		$("#industryCodeGrid").iGrid({
			height: 80,
			handler: "cls1151gridhandler",
			action: "queryIndustryCode",
			colModel: [{
				colHeader: i18n.cls1151s01["L140M03A.industryCode"],//產業代碼
				width: 20,
				name: 'ECOCD',
				align: 'center',
				sortable: true,
				formatter: 'click',
				onclick: function(cellValue, options, rowObject){
					$("#companyIndustryCode").val(rowObject.ECOCD);//產業代碼
					$("#companyIndustryCodeName").val(rowObject.ECONM);//產業代碼名稱
					$.thickbox.close();
				}
			}, {
				colHeader: i18n.cls1151s01["L140M03A.industryName"],//產業名稱
				name: 'ECONM',
				align: 'left',
				width: 30
			}]
		});
	},
	
	loadGridInfo : function(){
		$("#industryCodeGrid").jqGrid("setGridParam", {
			postData: {
				tabFormMainId: _M.tabMainId,
				industryCode: $("#industryCode").val()
			},
			loadComplete : function() {    				
				$('#industryCodeGrid tr').click(function(e) {
					//click 帶入資料至畫面後 go to top
					e.preventDefault();
				});
			},
            search: true
        }).trigger("reloadGrid");
	}
}

var YoungDataInfo = {
		
		init : function($boxContex){
			
			$boxContex.find("#importYounData").click(function(){
				$.ajax({
		            handler: "cls1151m01formhandler",
		            action: "checkL140m01yELF459_srcflag_1",
		            data: {
	                	tabFormMainId: _M.tabMainId,
	                	refType: "ELF459_srcflag_1"
	                },
		         	success: function(json){
		         		YoungDataInfo.openBox();
		            }
		        });	
	        });
			YoungDataInfo.initGridInfo();
		},
		
		openBox : function(){
			
			var buttons = {};
			buttons[i18n.def.close] = function(){				
				$.thickbox.close();
		    };
			
			$("#openYoungDataBox").thickbox({
		        title: i18n.cls1151s01['page01.title.youngLoanList'],
		        width: 450,
		        height: 250,
		        modal: true,
				align: "center",
				valign: 'bottom',
		        i18n: i18n.def,
		        buttons: buttons
		    });
			YoungDataInfo.loadGridInfo();
		},
		
		initGridInfo : function(){
			
			$("#youngDataGrid").iGrid({
				height: 80,
				handler: "cls1151gridhandler",
				action: "queryYoungData",
				colModel: [{
					colHeader: i18n.cls1151s01["L140M01O.custId"],//統一編號
					width: 20,
					name: 'youngLoanCustId',
					align: 'center',
					sortable: false,
					formatter: 'click',
					onclick: function(cellValue, options, rowObject){
						$("input[name='assisType'][value='3']").attr('checked',true);//青年貸款種類-青年創業及啟動金貸款
						$("#assisType2").show();
						$("#assisType1").hide();
						$("#applicationDate").val(rowObject.applyDate);//申請日期
						$("#capitalAMT").val(rowObject.contribution);//登記出資額
						$("#registerDate").val(rowObject.n_cdate);//設立登記日期
						$("#companyId").val(rowObject.companyId);//事業體統編
						$("#companyDupNo").val(rowObject.companyDupno);//事業體統編重覆序號
						$("#companyName").val(rowObject.companyName);//事業體名稱
						$("#companyIndustryCode").val(rowObject.industryCode);//產業代碼
						$("#companyIndustryCodeName").val(rowObject.industryCname);//產業代碼名稱
						$("#companyCity").val(rowObject.facCity);//企業所在縣/市
						$(_M.boxContextId).find("#companyCity").trigger("change");
						$("#companyArea").val(rowObject.facCityArea);//企業所在區/市/鄉/鎮
						$("#subSidyut").val("01");//青年創業補貼單位
						$("#yoPurpose").val(rowObject.purpose);//青年創業貸款用途
						YoungDataInfo.insertL140m01y(rowObject);
						$.thickbox.close();
					}
				}, {
					colHeader: i18n.cls1151s01["L140M01O.custName"],//客戶名稱
					name: 'youngLoanCustName',
					align: 'left',
					width: 30
				}, {
					colHeader: i18n.cls1151s01["C122M01A.applyCaseType"],//100萬以上案件
					name: 'applyCaseType',
					align: 'center',
					width: 30
				}, {
					colHeader: i18n.cls1151s01["C122M01A.applyTS"],//進件日期
					name: 'applyDate',
					align: 'center',
					width: 30
				}, {name:'c122M01aOid', hidden:true
				}, {name:'c122M01aMainId', hidden:true
				}, {name:'companyId', hidden:true
				}, {name:'companyDupno', hidden:true
				}, {name:'companyName', hidden:true
				}, {name:'industryCode', hidden:true
				}, {name:'industryCname', hidden:true
				}, {name:'n_cdate', hidden:true
				}, {name:'contribution', hidden:true
				}, {name:'facCity', hidden:true
				}, {name:'facCityArea', hidden:true
				}, {name:'purpose', hidden:true
				}]
			});
		},
		
		loadGridInfo : function(){
			$("#youngDataGrid").jqGrid("setGridParam", {
				postData: {
					tabFormMainId: _M.tabMainId,
					industryCode: $("#custId").val()
				},
				loadComplete : function() {    				
					$('#youngDataGrid tr').click(function(e) {
						//click 帶入資料至畫面後 go to top
						e.preventDefault();
					});
				},
	            search: true
	        }).trigger("reloadGrid");
		},
		
		insertL140m01y : function(rtnObject){
			$.ajax({
	            handler: "cls1151m01formhandler",
	            action: "addL140m01y",
	            data: {
                	tabFormMainId: _M.tabMainId,
                	refType: "ELF459_srcflag_1",
                	refValue: "",
                	refModel: "C122M01A",
                	refMainId: rtnObject.c122M01aMainId,
                	refOid: rtnObject.c122M01aOid,
                	usePlan: rtnObject.usePlan
                },
	         	success: function(json){
	            	$("#l140m01yGrid").trigger("reloadGrid");
	            }
	        });	
		}
	}

var exceptFlagQA = {
		firstInit: true,
		showExceptFlagQA: false,
		init: function(){
			if(exceptFlagQA.firstInit){
		        $.ajax({
		            handler: _M.fhandle,
		            action: "initExceptFlag",
		            data: {//把資料轉成json
		                tabFormMainId: _M.tabMainId,
		                mainId: _M.CaseMainId
		            },
		            success: function(obj){
		            	var $boxContex = $(_M.boxContextId);
		            	if(obj.showExceptFlagQA == "Y"){
		            		exceptFlagQA.showExceptFlagQA = true;
		            	}
		            	exceptFlagQA.init2();
		            	exceptFlagQA.firstInit = false;//第一次進畫面要跑的都跑完之後再改狀態
		            }
		        });  
			}else{
				exceptFlagQA.init2();
			}
		},
		
		init2: function(){
			var $boxContex = $(_M.boxContextId);
    		if(exceptFlagQA.showExceptFlagQA){
    			if(exceptFlagQA.firstInit){
    				var exceptFlagQAisY = $boxContex.find("#exceptFlagQAisY").val();
    				var exceptFlag = $boxContex.find("[name=exceptFlag]:radio:checked").val();
    				if(exceptFlagQAisY != ""){
    					for(var i = 1 ; i <= exceptFlagQAisY ; i++){
    						var inputName = "exceptFlagQ"+i;
    						$boxContex.find("#"+inputName+"Tr").show();
    						$("input[name='"+ inputName +"YN'][value='N']").attr('checked',true);
    						if(i == exceptFlagQAisY){
    							$("input[name='"+ inputName +"YN'][value='Y']").attr('checked',true);
    						}
    					}
    				}
    				exceptFlagQA.getExceptFlagStr(exceptFlag);
    			}else{
    				//因為頁籤切換會重新injectData觸發onclick的動作，導致隱藏欄位[exceptFlagQAisY]被清除
    				//非第一次進入頁籤畫面要重新去根據畫面取得資料塞回去
    				exceptFlagQA.initExceptFlagQA(); 
    			}
    			$boxContex.find("#exceptFlagTable1").hide();
    			$boxContex.find("#exceptFlagTable2").show();
    		}else{
    			$boxContex.find("#exceptFlagTable1").show();
    			$boxContex.find("#exceptFlagTable2").hide();	
    		}
    	},
    	
		initEvent : function(){
			var $boxContex = $(_M.boxContextId);
			$boxContex.find("[name=exceptFlagQ1YN]:radio").click(function(){
				if ($boxContex.find("[name=exceptFlagQ1YN]:radio:checked").val() == "Y") {
					$("input[name='exceptFlag'][value='_']").attr('checked',true);//_:N.A.
					$("#exceptFlagQAisY").val("1");
					exceptFlagQA.getExceptFlagStr("_");
					exceptFlagQA.resetItem(["2","3","4","5","6","7","8"]);
				}else{
					$boxContex.find("#exceptFlagQ2Tr").show();
					exceptFlagQA.cleanExceptFlag();
				}
			});
			$boxContex.find("[name=exceptFlagQ2YN]:radio").click(function(){
				if ($boxContex.find("[name=exceptFlagQ2YN]:radio:checked").val() == "Y") {
					exceptFlagQA.resetItem(["3","4","5","6","7","8"]);
					$("input[name='exceptFlag'][value='Y']").attr('checked',true);//Y:無條件可取消融資額度
					$("#exceptFlagQAisY").val("2");
					exceptFlagQA.getExceptFlagStr("Y");
				}else{
					$boxContex.find("#exceptFlagQ3Tr").show();
					exceptFlagQA.cleanExceptFlag();
				}
			});
			$boxContex.find("[name=exceptFlagQ3YN]:radio").click(function(){
				if ($boxContex.find("[name=exceptFlagQ3YN]:radio:checked").val() == "Y") {
					exceptFlagQA.resetItem(["4","5","6","7","8"]);
					$("input[name='exceptFlag'][value='N']").attr('checked',true);//N:不可取消融資額度
					$("#exceptFlagQAisY").val("3");
					exceptFlagQA.getExceptFlagStr("N");
				}else{
					$boxContex.find("#exceptFlagQ4Tr").show();
					exceptFlagQA.cleanExceptFlag();
				}
			});
			$boxContex.find("[name=exceptFlagQ4YN]:radio").click(function(){
				if ($boxContex.find("[name=exceptFlagQ4YN]:radio:checked").val() == "Y") {
					exceptFlagQA.resetItem(["5","6","7","8"]);
					$("input[name='exceptFlag'][value='N']").attr('checked',true);//N:不可取消融資額度
					$("#exceptFlagQAisY").val("4");
					exceptFlagQA.getExceptFlagStr("N");
				}else{
					$boxContex.find("#exceptFlagQ5Tr").show();
					exceptFlagQA.cleanExceptFlag();
				}
			});
			$boxContex.find("[name=exceptFlagQ5YN]:radio").click(function(){
				if ($boxContex.find("[name=exceptFlagQ5YN]:radio:checked").val() == "Y") {
					exceptFlagQA.resetItem(["6","7","8"]);
					$("input[name='exceptFlag'][value='N']").attr('checked',true);//N:不可取消融資額度
					$("#exceptFlagQAisY").val("5");
					exceptFlagQA.getExceptFlagStr("N");
				}else{
					$boxContex.find("#exceptFlagQ6Tr").show();
					exceptFlagQA.cleanExceptFlag();
				}
			});
			$boxContex.find("[name=exceptFlagQ6YN]:radio").click(function(){
				if ($boxContex.find("[name=exceptFlagQ6YN]:radio:checked").val() == "Y") {
					exceptFlagQA.resetItem(["7","8"]);
					$("input[name='exceptFlag'][value='N']").attr('checked',true);//N:不可取消融資額度
					$("#exceptFlagQAisY").val("6");
					exceptFlagQA.getExceptFlagStr("N");
				}else{
					$boxContex.find("#exceptFlagQ7Tr").show();
					exceptFlagQA.cleanExceptFlag();
				}
			});
			$boxContex.find("[name=exceptFlagQ7YN]:radio").click(function(){
				if ($boxContex.find("[name=exceptFlagQ7YN]:radio:checked").val() == "Y") {
					exceptFlagQA.resetItem(["8"]);
					$("input[name='exceptFlag'][value='C']").attr('checked',true);//C:有條件可取消融資額度
					$("#exceptFlagQAisY").val("7");
					exceptFlagQA.getExceptFlagStr("C");
				}else{
					$boxContex.find("#exceptFlagQ8Tr").show();
					exceptFlagQA.cleanExceptFlag();
				}
			});
			$boxContex.find("[name=exceptFlagQ8YN]:radio").click(function(){
				if ($boxContex.find("[name=exceptFlagQ8YN]:radio:checked").val() == "Y") {
					$("input[name='exceptFlag'][value='N']").attr('checked',true);//N:不可取消融資額度
					$("#exceptFlagQAisY").val("8");
					exceptFlagQA.getExceptFlagStr("N");
				}else{
					exceptFlagQA.cleanExceptFlag();
				}
			});
		},
		
		resetItem : function(items){
			var $boxContex = $(_M.boxContextId);
			for(var i = 0 ; i < items.length ; i++ ){
				var inputName = "exceptFlagQ"+items[i];
				$boxContex.find("#"+inputName+"Tr").hide();
				$("input[name='"+ inputName +"YN'][value='Y']").attr('checked',false);
				$("input[name='"+ inputName +"YN'][value='N']").attr('checked',false);
			}
		},
		
		cleanExceptFlag : function(){
			$("input[name='exceptFlag'][value='Y']").attr('checked',false);//Y:無條件可取消融資額度
			$("input[name='exceptFlag'][value='C']").attr('checked',false);//C:有條件可取消融資額度
			$("input[name='exceptFlag'][value='N']").attr('checked',false);//N:不可取消融資額度
			$("input[name='exceptFlag'][value='_']").attr('checked',false);//_:N.A.
			$("#exceptFlagQAisY").val("");
			var exceptFlagDes = $("#exceptFlagDes");
			exceptFlagDes.injectData({'exceptFlagDes':''},false);
		},
		
		initExceptFlagQA : function(){
			var $boxContex = $(_M.boxContextId);
			var showNext = 0;
			for(var i = 1 ; i <= 8 ; i++){
				var inputName = "exceptFlagQ"+i;
				if($boxContex.find("[name='"+inputName+"YN']:radio:checked").val() == "Y"){
					$("#exceptFlagQAisY").val(i);
				}
				if($boxContex.find("[name='"+inputName+"YN']:radio:checked").val() == "N"){
					showNext = i+1;
				}
				if($boxContex.find("[name='"+inputName+"YN']:radio:checked").val() == undefined){
					if( i > 1 ){//如果第一項沒有選的話不要隱藏第一項
						$boxContex.find("#"+inputName+"Tr").hide();
					}
				}	
			}
			if(showNext != 0){
				$boxContex.find("#exceptFlagQ"+showNext+"Tr").show();
			}
			exceptFlagQA.getExceptFlagStr($boxContex.find("[name=exceptFlag]:radio:checked").val());
		},
		
		getExceptFlagStr : function(exceptFlag){
			var exceptFlagDes = $("#exceptFlagDes");
			var exceptFlagStr = "";
			switch (exceptFlag) {
            case "Y":
                // L140M01a.exceptFlag_Y=無條件可取消融資額度
            	exceptFlagStr = i18n.cls1151s01["L140M01a.exceptFlag_Y"]+i18n.cls1151s01["L140M01a.exceptFlag_Y_memo1"];
                break;
            case "C":
            	// L140M01a.exceptFlag_C=有條件可取消融資額度
            	exceptFlagStr = i18n.cls1151s01["L140M01a.exceptFlag_C"]+i18n.cls1151s01["L140M01a.exceptFlag_C_memo1"];
            	break;
            case "N":
                // L140M01a.exceptFlag_N=不可取消融資額度
            	exceptFlagStr = i18n.cls1151s01["L140M01a.exceptFlag_N"]+i18n.cls1151s01["L140M01a.exceptFlag_N_memo1"];
                break;
            case "_":
            	exceptFlagStr = "N.A.";
                break;
            default:
                break;
			}
			exceptFlagDes.injectData({'exceptFlagDes':exceptFlagStr},false);
		}
}


var projClass = {
		firstLoad:true,
		initEvent: function(){
			$("#projClass").change(function(){
				projClass.change();
			});

			$("#addRefX1CntrNo").click(function(){
				projClass.openRefX1CntrNo();
			});
			
			$("#deleteRefX1CntrNo").click(function(){
				projClass.deleteRefX1CntrNo();
			});
		},
		
		show: function(projClass){
			switch (projClass) {
			    case 'Y1':
			        $("#refX1CntrNoDiv").show();
			        break;
				case 'Y2':
					$("#refX1CntrNoDiv").show();
			        break;
			    case 'Y3':
			    	$("#refX1CntrNoDiv").show();
			        break;
				default:
					$("#refX1CntrNoDiv").hide();
					break;
			}
		},
		
		change: function(){
			$("#refX1CntrNoDiv").hide();
			projClass.show($("#projClass").val());
			
		},
		
		loadGridInfo: function(){
			if(projClass.firstLoad){
				$("#refX1CntrNoListGrid").iGrid({
					height: 100,
					handler: _M.ghandle,
					action: "getRefX1CntrNoList",
					postData: {
						tabFormMainId: _M.tabMainId
					},
					loadComplete : function() {    				
						$('#refX1CntrNoListGrid tr').click(function(e) {
							//click 帶入資料至畫面後 go to top
							e.preventDefault();
						});
					},
					colModel: [{
						colHeader: i18n.cls1151s01["L140M01A.cntrNo_X1"],//X1額度序號
						width: 200,
						name: 'cntrNo_x1',
						sortable: false,
						align : "left"
					}, {
						name: 'l140m01aOid_x1',
						hidden: true
					}]
				});
				projClass.firstLoad = false
			}
		},
		
		openRefX1CntrNo: function(cellvalue, options, rowObject){//這邊就是OPEN BOX
			var openBts;
			openBts = API.createJSON([{
				key: i18n.def['sure'],
				value: function(){
					if ($("#refX1CntrNo").val() != "" ) {
						$.ajax({
							type : "POST",
							handler: _M.fhandle,
							action: 'checkRefX1CntrNo',
							data: {
								tabFormMainId: _M.tabMainId,//額度明細表的MAINID
								refX1cntrNo : $("#refX1CntrNo").val()//對應的X1額度序號
							},
							success: function(responseData){
								if (responseData.msg) {
									API.showErrorMessage(responseData.msg);
									return;
								}else if(responseData.confirmUpdate){
									API.confirmMessage(responseData.confirmUpdate, function(b){
										if(b){
											$.ajax({
												type : "POST",
												handler: _M.fhandle,
												action: 'saveRefCntrNo',
												data:{
													tabFormMainId: _M.tabMainId,//額度明細表的MAINID
													x1L140m01aOid: responseData.refX1L140m01aOid,//額度明細表的OID
													refX1cntrNo : $("#refX1CntrNo").val(),//對應的X1額度序號
													projClass : $("#projClass").val()//專案種類
												},
												success: function(responseObj){
													if(!responseObj.NOTIFY_MESSAGE){
														$("#refX1CntrNoListGrid").trigger("reloadGrid");
														API.triggerOpener();
														//執行成功
														CommonAPI.showPopMessage(i18n.def["runSuccess"], function(){
									            			$.thickbox.close();
									            		});
													}
												}
											});
										}
									});
								}else{
									$.ajax({
										type : "POST",
										handler: _M.fhandle,
										action: 'saveRefCntrNo',
										data:{
											tabFormMainId: _M.tabMainId,//額度明細表的MAINID
											x1L140m01aOid: responseData.refX1L140m01aOid,//額度明細表的OID
											refX1cntrNo : $("#refX1CntrNo").val(),//對應的X1額度序號
											projClass : $("#projClass").val()//專案種類
										},
										success: function(responseObj){
											if(!responseObj.NOTIFY_MESSAGE){
												$("#refX1CntrNoListGrid").trigger("reloadGrid");
												API.triggerOpener();
												//執行成功
												CommonAPI.showPopMessage(i18n.def["runSuccess"], function(){
							            			$.thickbox.close();
							            		});
											}
										}
									});
								}
							}
						});
					}
				}
			}, {
				key: i18n.def['cancel'],
				value: function(){
					$("#refCntrNo").val('');
					$.thickbox.close();
				}
			}]);
			$("#openBox_refX1CntrNo").thickbox({
				title : '對應X1額度序號維護',
				height : 100,
				weight : 50,
				align : "center",
				valign : "bottom",
				model : true,
				i18n : i18n.def,
				open: function(){
					$("#refX1CntrNo").val('');
	            },
				buttons : openBts
			});
		},
		deleteRefX1CntrNo: function(){
			var $refX1CntrNoListGrid = $("#refX1CntrNoListGrid");
			if($refX1CntrNoListGrid.length > 0){
				var gridId = $refX1CntrNoListGrid.getGridParam('selrow');			
				if (!gridId) {
					return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
				}
				var data = $refX1CntrNoListGrid.getRowData(gridId);
				CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(result){
		            if (result) {
		                $.ajax({
		                	type : "POST",
		                    handler: _M.fhandle,
		                    action: "deleteRefX1CntrNo",
		                    data: {
		                        l140m01aOid_x1 : data.l140m01aOid_x1
		                    },
		                    success: function(obj){
		                    	$refX1CntrNoListGrid.trigger("reloadGrid");
		                    	API.triggerOpener();
		                    	CommonAPI.showPopMessage(i18n.def["runSuccess"], function(){
		                    		
		                    	});
		                    }
		                });
		            }
			    }); // end confirmMessage
	        }
		}
}

var hualienEearthquake = {
		show: function(){
			var $boxContex = $(_M.boxContextId);
			$boxContex.find("#hualien0403GutDiv").hide();
			if ($boxContex.find("[name=hualien0403]:radio:checked").val() == "Y") {
				$boxContex.find("#hualien0403GutDiv").show();
			}
		},
    	
		initEvent : function(){
			var $boxContex = $(_M.boxContextId);
			$boxContex.find("[name=hualien0403]:radio").click(function(){
				if ($boxContex.find("[name=hualien0403]:radio:checked").val() == "Y") {
					// 選[是] 顯示[房貸信保註記]及[信保成數]欄位
					$boxContex.find("#hualien0403GutDiv").show();
				}else{
					// 選[否]要清除[房貸信保註記]及[信保成數]欄位
					$boxContex.find("#hualien0403GutDiv").hide();
					$("input[name='isHouseLoanGu'][value='Y']").attr('checked',false);
					$("#hualienGutPer").val('');
				}
			});
			
			$("input[name='isHouseLoanGu']").click(function(){
				if($("input[name=isHouseLoanGu]:checked").val() == "Y"){
					//勾選[房貸信保註記]
				}else{
					//清除[信保成數]
					$("#hualienGutPer").val('');
				}
			});
		}
}
