/* 
 * F101S01ADaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.util.CapString;

import com.mega.eloan.lms.dao.F101S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.F101M01A;
import com.mega.eloan.lms.model.F101S01A;

/**
 * <pre>
 * F101S01A dao 實作。
 * </pre>
 * 
 * @since 2011/7/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/7/20,<PERSON><PERSON><PERSON> Wang,new
 *          <li>2011/8/13,<PERSON><PERSON><PERSON> Wang,add
 *          {@link F101S01ADao#deleteByMeta(F101M01A)}</li>
 *          </ul>
 */
@Repository
public class F101S01ADaoImpl extends LMSJpaDao<F101S01A, String> implements
        F101S01ADao {

    /*
     * (non-Javadoc)
     * 
     * @see
     * com.mega.eloan.ces.fss.dao.F101S01ADao#deleteByMeta(com.mega.eloan.ces
     * .fss.model.F101M01A)
     */
    public int deleteByMeta(F101M01A meta) {
        Query query = entityManager
                .createNamedQuery("f101s01a.deleteByMainIdAndPid");

        query.setParameter("mainId", meta.getMainId());
        query.setParameter("pid", meta.getUid());
        return query.executeUpdate();
    }

    @Override
    public List<F101S01A> findF101S01AByMetaAndSubNo(String mainId, String tab,
            String[] subNos) {
        ISearch mySearch = createSearchTemplete();
        mySearch.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
        if (!CapString.isEmpty(tab)) {
            mySearch.addSearchModeParameters(SearchMode.EQUALS, "tab", tab);
        }
        if (subNos != null) {
            mySearch.addSearchModeParameters(SearchMode.IN, "subNo", subNos);
        }
        // 目前一個meta下的3表資料最多有109個，這by F101M04A的資料和畫面上的欄位數目。
        mySearch.setMaxResults(150);
        List<F101S01A> list = find(mySearch);
        return list;
    }

}
