<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
        <wicket:extend>
            <button id="getdata" type="button">
                <span class="text-only">
                    <wicket:message key="L910M01a.getdata">
                        借款人所得有缺資料查詢
                    </wicket:message>
                </span>
            </button>
            <button id="updata" type="button">
                <span class="text-only">
                    <wicket:message key="L910M01a.updata">
                        借款人所得有缺資料更新(整批)
                    </wicket:message>
                </span>
            </button>
            <button id="updatasingle" type="button">
                <span class="text-only">
                    <wicket:message key="L910M01a.updatasingle">
                        借款人所得有缺資料更新(單筆)
                    </wicket:message>
                </span>
            </button>
			<br/>
			<div id="gridfile" />
            <div id="lms9101new" style="display:none;">
                <form id="lms9101tabForm">
                    <table class='tb2' width="100%" cellspacing="0" cellspadding="0">
                        <tr>
                            <td class="hd2" align="right">
                                <span class="text-red">＊</span>
                                <wicket:message key="L910M01a.tdata">
                                    上傳資料
                                </wicket:message>&nbsp;&nbsp;
                            </td>
                            <td>
                                <wicket:message key="L910M01a.tfile">
                                    檔案路徑
                                </wicket:message>
                                <input id="tfile" name="tfile" type="text" size="10" class="required" edittype="file" maxlength="100"/>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
            <div id="lms9101newSingle" style="display:none;">
                <form id="lms9101tabFormSingle">
                    <table class='tb2' width="100%" cellspacing="0" cellspadding="0">
                        <tr>
                            <td class="hd2" align="right" width="20%">
                                <span class="text-red">＊</span>
                                <wicket:message key="L910M01a.custData">
                                    借款人資料
                                </wicket:message>&nbsp;&nbsp;
                            </td>
                            <td width="80%"><span class="text-red">＊</span>
                                <wicket:message key="L910M01a.custId">
                                    借款人統編
                                </wicket:message>
                                <!-- J-111-0125 因應obu可以承做外國自然人授信業務(法規未予限制), 開放eloan(包含個人授信/徵信/信評等系統)相關對外國自然人統編的限制
									前端 html 
										由 class="required _checkID" 
										改  class="required "
									但在 server-side
										LMS9101M01FormHandler :: querysingleUpdate(PageParameters params, Component parent) 去加強檢核
										[O] 19540101LY
										[X] 1954010XLY
								-->
								<input id="custId" name="custId" type="text" size="10" class="required " maxlength="10"/><span class="text-red">＊</span>
                                <wicket:message key="L910M01a.tDup">
                                    重複序號
                                </wicket:message>
                                <input id="tDup" name="tDup" type="text" size="1" class="required digits" maxlength="1"/>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2" align="right" width="20%">
                                <span class="text-red">＊</span>
                                <wicket:message key="L910M01a.payData">
                                    所得資料
                                </wicket:message>&nbsp;&nbsp;
                            </td>
                            <td width="80%">
                                <span class="text-red">＊</span>
                                <wicket:message key="L910M01a.yPay">
                                    年薪
                                </wicket:message>
                                <input id="yPay" name="yPay" type="text" size="5" class="required digits" maxlength="5"/>
                                <wicket:message key="L910M01a.yUnit">
                                    萬元
                                </wicket:message>
                                <span class="text-red">＊</span>
                                <wicket:message key="L910M01a.oMoney">
                                    其他收入所得
                                </wicket:message>
                                <input id="oMoney" name="oMoney" type="text" size="5" class="required digits" maxlength="5"/>
                                <wicket:message key="L910M01a.yUnit">
                                    萬元
                                </wicket:message>
                                <br/>
                                <span class="text-red">＊</span>
                                <wicket:message key="L910M01a.Job">
                                    職業別
                                </wicket:message>
                                <select id="JobClass" name="JobClass" class="required" />
								<br/>
								<select id="Job" name="Job" class="required" width="80%"/>
                                <br/>
                                <span class="text-red">＊</span>
                                <wicket:message key="L910M01a.jTitle">
                                    職稱
                                </wicket:message>
                                <select id="jTitle" name="jTitle" class="required" />
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
        </wicket:extend>
    </body>
</html>