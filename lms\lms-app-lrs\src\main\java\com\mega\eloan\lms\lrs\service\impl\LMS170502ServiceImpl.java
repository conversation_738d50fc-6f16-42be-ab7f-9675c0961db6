/* 
 *  LMS1705ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.service.impl;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.BstblService;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.L170A01ADao;
import com.mega.eloan.lms.dao.L170M01ADao;
import com.mega.eloan.lms.dao.L170M01BDao;
import com.mega.eloan.lms.dao.L170M01CDao;
import com.mega.eloan.lms.dao.L170M01DDao;
import com.mega.eloan.lms.dao.L170M01EDao;
import com.mega.eloan.lms.dao.L170M01FDao;
import com.mega.eloan.lms.dao.L170M01GDao;
import com.mega.eloan.lms.dao.L180M01ADao;
import com.mega.eloan.lms.dao.L180M01BDao;
import com.mega.eloan.lms.dao.L180M01CDao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lrs.constants.lrsConstants;
import com.mega.eloan.lms.lrs.pages.LMS1705M01Page;
import com.mega.eloan.lms.lrs.panels.LMS1705S01Panel;
import com.mega.eloan.lms.lrs.panels.LMS1705S04Panel;
import com.mega.eloan.lms.lrs.service.LMS170502Service;
import com.mega.eloan.lms.lrs.service.LMS1705Service;
import com.mega.eloan.lms.lrs.service.LMS1805Service;
import com.mega.eloan.lms.mfaloan.service.MisLMS422Service;
import com.mega.eloan.lms.mfaloan.service.MisRatetblService;
import com.mega.eloan.lms.model.L170A01A;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.eloan.lms.model.L170M01C;
import com.mega.eloan.lms.model.L170M01D;
import com.mega.eloan.lms.model.L170M01E;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.eloan.lms.model.L170M01G;
import com.mega.eloan.lms.model.L170M01I;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.service.FlowService;

@Service
public class LMS170502ServiceImpl extends AbstractCapService implements
		LMS170502Service {

	protected static final Logger logger = LoggerFactory
			.getLogger(LMS170502ServiceImpl.class);

	@Resource
	TempDataService tempDataService;

	@Resource
	FlowService flowService;

	@Resource
	LMS1805Service Service1805;

	@Resource
	L180M01BDao l180m01bDao;

	@Resource
	L180M01ADao l180m01aDao;

	@Resource
	L180M01CDao l180m01cDao;

	@Resource
	L170A01ADao l170a01aDao;

	@Resource
	L170M01ADao l170m01aDao;

	@Resource
	L170M01BDao l170m01bDao;

	@Resource
	L170M01CDao l170m01cDao;

	@Resource
	L170M01DDao l170m01dDao;

	@Resource
	L170M01FDao l170m01fDao;

	@Resource
	L170M01GDao l170m01gDao;

	@Resource
	DwdbBASEService dwdbService;

	@Resource
	MisLMS422Service misLms422Service;

	@Resource
	BstblService bstblService;

	@Resource
	DocLogService docLogService;

	@Resource
	MisRatetblService misRateblService;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMS1705Service service;

	@Resource
	LMS1805Service service1805;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	L170M01EDao l170m01eDao;

	@Resource
	LMSService lmsService;

	@Resource
	LMS1705Service lms1705Service;

	@Resource
	RetrialService retrialService;

	public final static String YYYY_MM_DD1 = "yyyy-MM-dd";
	public final static String YYYY_MM_DD2 = "yyyyMMdd";

	@Override
	public CapAjaxFormResult tempSave(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tempSave = params.getString("tempSave", "Y");
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);

		if (page == 1) {

			List<GenericBean> list = collectionData3(params, tempSave);

			for (GenericBean modelA : list) {
				try {
					service.save(modelA);
				} catch (Exception e) {
					logger.error("LMS170502ServiceImpl tempSave EXCEPTION!!", e);
					logger.error("[tempSave] service1705.save EXCEPTION!!", e);
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0007"), getClass());
				}
			}
		} else if (page == 4) {

			List<L170M01D> models = collectionData2(params, tempSave);

			if (models != null) {
				try {
					service.saveL170m01dList2(models);
				} catch (Exception e) {
					Properties pop2 = MessageBundleScriptCreator
							.getComponentResource(LMS1705M01Page.class);
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0025",
							pop2.getProperty("L1205G.error1")), getClass());
				}
			}
		} else if (page == 3) {
			GenericBean model = collectionData(params, tempSave);
			if (model != null) {
				try {
					service.save(model);
				} catch (Exception e) {
					Properties pop2 = MessageBundleScriptCreator
							.getComponentResource(LMS1705M01Page.class);
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0025",
							pop2.getProperty("L1205G.error1")), getClass());
				}
			}
		} else if (page == 5) {
			GenericBean model = collectionData(params, tempSave);
			if (model != null) {
				try {
					service.save(model);
				} catch (Exception e) {
					Properties pop2 = MessageBundleScriptCreator
							.getComponentResource(LMS1705M01Page.class);
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0025",
							pop2.getProperty("L1205G.error1")), getClass());
				}
			}
		}

		return result;
	}

	/**
	 * 處理需要暫存的資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return L170M01A
	 * @throws CapException
	 */
	private GenericBean collectionData(PageParameters params,
			String tempSave) throws CapException {
		if ("".equals(Util.trim(tempSave))) {
			tempSave = "N";
		}
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String mainId = params.getString(EloanConstants.MAIN_ID);
		params = convertParameters(params);
		L170M01A l170m01a = service.findModelByMainId(L170M01A.class, mainId);
		l170m01a = CapBeanUtil.map2Bean(params, l170m01a);

		switch (page) {
		case 3:
			L170M01C l170m01c = service.findModelByMainId(L170M01C.class,
					mainId);
			l170m01c = DataParse.toBean(params.getString("L170M01cForm"),
					l170m01c);
			String ratioNo1 = Util.trim(params.getString("ratioNo1"));
			String ratioNo2 = Util.trim(params.getString("ratioNo2"));
			String ratioNo3 = Util.trim(params.getString("ratioNo3"));
			String ratioNo4 = Util.trim(params.getString("ratioNo4"));
			if (l170m01c == null) {
				l170m01c = this
						.setC170M01CDefault(
								mainId,
								l170m01a.getCustId(),
								l170m01a.getDupNo(),
								"".equals(Util.trim(l170m01a.getTotBalCurr())) ? branchService
										.getBranch(
												Util.trim(l170m01a.getOwnBrId()))
										.getUseSWFT()
										: l170m01a.getTotBalCurr(), ratioNo1,
								ratioNo2, ratioNo3, ratioNo4);
			}

			// l170m01c_curr
			if (!CapString.isEmpty(params.getString("finCurr"))) {
				l170m01c.setCurr(Util.trim(params.getString("finCurr")));
			}
			// l170m01c_unit
			if (!CapString.isEmpty(params.getString("finUnit"))) {
				l170m01c.setUnit(new BigDecimal(params.getLong("finUnit", 0)));
			}

			// fromDate1
			if (!CapString.isEmpty(params.getString("fromDate1"))) {
				l170m01c.setFromDate1((Date) params.get("fromDate1"));
			}
			// endDate1
			if (!CapString.isEmpty(params.getString("endDate1"))) {
				l170m01c.setEndDate1((Date) params.get("endDate1"));
			}
			// fromDate2
			if (!CapString.isEmpty(params.getString("fromDate2"))) {
				l170m01c.setFromDate2((Date) params.get("fromDate2"));
			}
			// endDate2
			if (!CapString.isEmpty(params.getString("endDate2"))) {
				l170m01c.setEndDate2((Date) params.get("endDate2"));
			}
			// fromDate3
			if (!CapString.isEmpty(params.getString("fromDate3"))) {
				l170m01c.setFromDate3((Date) params.get("fromDate3"));
			}
			// endDate3
			if (!CapString.isEmpty(params.getString("endDate3"))) {
				l170m01c.setEndDate3((Date) params.get("endDate3"));
			}

			return l170m01c;
		case 5:
			String formL170m01f1 = params.getString("L170M01F1Form");
			String formL170m01f2 = params.getString("L170M01F2Form");

			L170M01F l170m01f = service.findModelByMainId(L170M01F.class,
					mainId);
			if (l170m01f == null) {
				l170m01f = new L170M01F();
				l170m01f.setMainId(l170m01a.getMainId());
				l170m01f.setCustId(l170m01a.getCustId());
				l170m01f.setDupNo(l170m01a.getDupNo());

				DataParse.toBean(formL170m01f1, l170m01f);
				DataParse.toBean(formL170m01f2, l170m01f);

			} else {
				DataParse.toBean(formL170m01f1, l170m01f);
				DataParse.toBean(formL170m01f2, l170m01f);
			}
			// l170m01f = CapBeanUtil.map2Bean(params, l170m01f);

			return l170m01f;
		default:
			break;
		}
		return null;
	}

	/**
	 * 處理需要暫存的資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return L170M01A
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	private List<L170M01D> collectionData2(PageParameters params,
			String tempSave) throws CapException {

		if ("".equals(Util.trim(tempSave))) {
			tempSave = "N";
		}
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String oid = params.getString(EloanConstants.MAIN_OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01A l170m01a = service.findModelByMainId(L170M01A.class, mainId);
		String custId = l170m01a.getCustId();
		String dupNo = l170m01a.getDupNo();

		params = convertParameters(params);

		List<L170M01D> list = new ArrayList<L170M01D>();

		String formL170m01d = params.getString("L170M01dForm");
		JSONObject jobjectD = JSONObject.fromObject(formL170m01d);
		JSONArray itemNo = jobjectD.getJSONArray("itemNo");
		String chkPreReview = params.getString("chkPreReview", "N");
		boolean chkCheck = params.getAsBoolean("chkCheck", false);
		String[] chkResultSeq = new String[itemNo.size()];
		String[] chkTextSeq = new String[itemNo.size()];
		String rptid = jobjectD.getString("rptid");
		l170m01a.setRptId(rptid); // J-107-0128海外改格式
		service.save(l170m01a);

		for (int i = 0; i < itemNo.size(); i++) {
			L170M01D l170m01d = service.findL170m01dByMainId(mainId, custId,
					dupNo, Util.trim(itemNo.getString(i)));
			String chkResult = Util.trim(jobjectD.get("chkResult"
					+ itemNo.getString(i)));
			String chkText = Util.trim(jobjectD.get("chkText"
					+ itemNo.getString(i)));
			l170m01d.setChkResult(Util.trim(chkResult));
			l170m01d.setChkText(chkText);

			if ("A002".equals(l170m01d.getItemNo())) {
				if (!chkCheck) {
					l170m01d.setChkCheck("N");
				} else {
					l170m01d.setChkCheck("Y");
				}
			}
			if (Util.equals("", rptid)) {
				if ("B015".equals(l170m01d.getItemNo())) {
					l170m01d.setChkPreReview(chkPreReview);
				}
			} else {
				if ("B016".equals(l170m01d.getItemNo())) {
					l170m01d.setChkPreReview(chkPreReview);
				}
			}
			list.add(l170m01d);
		}

		return list;
	}

	@SuppressWarnings({ "deprecation" })
	private List<GenericBean> collectionData3(PageParameters params,
			 String tempSave) throws CapException {

		if ("".equals(Util.trim(tempSave))) {
			tempSave = "N";
		}
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		List<GenericBean> list = new ArrayList<GenericBean>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		params = convertParameters(params);
		L170M01A l170m01a = service.findModelByMainId(L170M01A.class, mainId);
		// 信評類別
		String crdType = Util.trim(params.getString("crdType", ""));
		// 評等等級
		String grade = Util.trim(params.getString("grade", ""));
		// 信評內部風險類別
		String crdTypeMow = Util.trim(params.getString("crdTypeMow", ""));
		String mow = Util.trim(params.getString("mow", ""));
		// 信評類別[N,C]
		String crdTypeHiddenCN = params.getString("crdTypeHiddenCN", "");
		eloandbBASEService.deleteL170M01EByTypeCN(mainId);
		String formL170m01a = params.getString("L170M01aForm");
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
		String dateString = sdf.format(new Date());
		// 前次
		String excrdType = Util.trim(params.getString("excrdType", ""));
		String exgrade = Util.trim(params.getString("exgrade", ""));
		String excrdTypeMow = Util.trim(params.getString("excrdTypeMow", ""));
		String exmow = Util.trim(params.getString("exmow", ""));
		String excrdTypeHiddenCN = params.getString("excrdTypeHiddenCN", "");// "NF^B+^00000000000000^2017-01-16^2017";//
		if (l170m01a != null) {
			// 信用評等(一筆)
			List<L170M01E> l170m01elist = service.findL170m01eByCrdType(mainId,
					l170m01a.getCustId(), l170m01a.getDupNo(), 1, "T");

			if (!l170m01elist.isEmpty()) {
				for (L170M01E l170m01e : l170m01elist) {
					l170m01e.setCrdType(crdType);
					l170m01e.setGrade(grade);
					service.save(l170m01e);
				}
			} else {
				if (!"".equals(Util.nullToSpace(crdType))) {
					L170M01E l170m01e = new L170M01E();
					l170m01e.setMainId(l170m01a.getMainId());
					l170m01e.setCustId(l170m01a.getCustId());
					l170m01e.setDupNo(l170m01a.getDupNo());
					l170m01e.setCrdType(crdType);
					l170m01e.setGrade(grade);
					l170m01e.setCrdTYear(new Date());
					l170m01e.setCrdTBR(l170m01a.getOwnBrId());
					l170m01e.setFinYear(dateString);
					// 無額度序號時，以0補滿欄位
					l170m01e.setCntrNo("00000000000000");
					l170m01e.setCreator(user.getUserId());
					l170m01e.setCreateTime(CapDate.getCurrentTimestamp());
					l170m01e.setTimeFlag("T");
					service.save(l170m01e);
				}
			}
			// 信用內部風險部評等(一筆)
			List<L170M01E> l170m01elist2 = service.findL170m01eByCrdType(
					mainId, l170m01a.getCustId(), l170m01a.getDupNo(), 2, "T");
			if (!l170m01elist2.isEmpty()) {
				for (L170M01E l170m01e2 : l170m01elist2) {
					l170m01e2.setCrdType(crdTypeMow);
					l170m01e2.setGrade(mow);
					service.save(l170m01e2);
				}
			} else {
				if (!"".equals(Util.nullToSpace(crdTypeMow))) {
					L170M01E l170m01e = new L170M01E();
					l170m01e.setMainId(l170m01a.getMainId());
					l170m01e.setCustId(l170m01a.getCustId());
					l170m01e.setDupNo(l170m01a.getDupNo());
					// 信用內部風險部評等
					l170m01e.setCrdType(crdTypeMow);
					l170m01e.setGrade(mow);
					l170m01e.setCrdTYear(new Date());
					l170m01e.setCrdTBR(l170m01a.getOwnBrId());
					l170m01e.setFinYear(dateString);
					// 無額度序號時，以0補滿欄位
					l170m01e.setCntrNo("00000000000000");
					l170m01e.setTimeFlag("T");
					service.save(l170m01e);
				}
			}

			// 信用風險部評等[信評類型(N,C)](多筆)
			String datas[] = crdTypeHiddenCN.split("\\|");
			if (Util.trim(crdTypeHiddenCN).length() > 0) {
				for (String data : datas) {
					String[] temp = data.split("\\^");
					L170M01E l170m01e = new L170M01E();
					l170m01e.setMainId(l170m01a.getMainId());
					l170m01e.setCustId(l170m01a.getCustId());
					l170m01e.setDupNo(l170m01a.getDupNo());
					// 信用內部風險部評等
					l170m01e.setCrdType(temp[0]);
					l170m01e.setGrade(temp[1]);
					l170m01e.setCrdTYear("".equals(temp[3]) ? null : new Date(
							temp[3].replace("-", "/")));
					l170m01e.setCrdTBR(l170m01a.getOwnBrId());
					l170m01e.setFinYear(dateString);
					// 無額度序號時，以0補滿欄位
					l170m01e.setCntrNo("00000000000000");
					l170m01e.setTimeFlag("T");
					service.save(l170m01e);

				}
			}

			// 前次
			List<L170M01E> exL170m01elist = lms1705Service
					.findL170m01eByMainId(mainId, "L");
			Map<String, String> CrdType = codetypeService
					.findByCodeType("lms1705s01_crdType");
			boolean excrdTypeEmpty = true;
			boolean excrdTypeMowEmpty = true;
			boolean excrdTypeCNEmpty = true;
			if (!exL170m01elist.isEmpty()) {
				for (L170M01E exLl170m01e : exL170m01elist) {
					if (Util.isNotEmpty(Util.nullToSpace(CrdType.get(Util
							.trim(exLl170m01e.getCrdType()))))
							|| Util.equals(UtilConstants.Type.無資料_C,
									Util.trim(exLl170m01e.getCrdType()))) {
						if (!"".equals(Util.nullToSpace(excrdType))) {
							exLl170m01e.setCrdType(excrdType);
							exLl170m01e.setGrade(exgrade);
							service.save(exLl170m01e);
							excrdTypeEmpty = false;
						}
					} else if (UtilConstants.crdTypeC.泰國GroupA.equals(Util
							.trim(exLl170m01e.getCrdType()))
							|| UtilConstants.crdTypeC.泰國GroupB.equals(Util
									.trim(exLl170m01e.getCrdType()))
							|| UtilConstants.crdTypeC.自訂.equals(Util
									.trim(exLl170m01e.getCrdType()))
							|| UtilConstants.crdTypeC.消金評等.equals(Util
									.trim(exLl170m01e.getCrdType()))
							|| UtilConstants.crdTypeN.Moody.equals(Util
									.trim(exLl170m01e.getCrdType()))
							|| UtilConstants.crdTypeN.中華信評.equals(Util
									.trim(exLl170m01e.getCrdType()))
							|| UtilConstants.crdTypeN.SP.equals(Util
									.trim(exLl170m01e.getCrdType()))
							|| UtilConstants.crdTypeN.Fitch.equals(Util
									.trim(exLl170m01e.getCrdType()))
							|| UtilConstants.crdTypeN.FitchTW.equals(Util
									.trim(exLl170m01e.getCrdType()))
							|| UtilConstants.crdTypeN.KBRA.equals(Util
									.trim(exLl170m01e.getCrdType()))) {
						// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
						if (Util.trim(excrdTypeHiddenCN).length() > 0) {
							String exdatas[] = excrdTypeHiddenCN.split("\\|");
							for (String exdata : exdatas) {
								String[] temp = exdata.split("\\^");
								L170M01E exLl170m01e_F = new L170M01E();
								this.initl170m01e_oV(exLl170m01e_F, l170m01a,
										user.getUserId(), "L");
								exLl170m01e_F.setCrdType(temp[0]);
								exLl170m01e_F.setGrade(temp[1]);
								exLl170m01e_F
										.setCrdTYear("".equals(temp[3]) ? null
												: new Date(temp[3].replace("-",
														"/")));
								service.save(exLl170m01e_F);
							}
							excrdTypeCNEmpty = false;
						}
					} else {
						if (!"".equals(Util.nullToSpace(excrdTypeMow))) {
							exLl170m01e.setCrdType(excrdTypeMow);
							exLl170m01e.setGrade(exmow);
							service.save(exLl170m01e);
							excrdTypeMowEmpty = false;
						}
					}
				}
			}
			if (excrdTypeEmpty && !"".equals(Util.nullToSpace(excrdType))) {
				L170M01E l170m01e = new L170M01E();
				this.initl170m01e_oV(l170m01e, l170m01a, user.getUserId(), "L");
				l170m01e.setCrdType(excrdType);
				l170m01e.setGrade(exgrade);
				service.save(l170m01e);
			}
			if (excrdTypeMowEmpty && !"".equals(Util.nullToSpace(excrdTypeMow))) {
				L170M01E l170m01e2 = new L170M01E();
				this.initl170m01e_oV(l170m01e2, l170m01a, user.getUserId(), "L");
				l170m01e2.setCrdType(excrdTypeMow);
				l170m01e2.setGrade(exmow);
				service.save(l170m01e2);
			}
			if (excrdTypeCNEmpty && (Util.trim(excrdTypeHiddenCN).length() > 0)) {
				String exdatas[] = excrdTypeHiddenCN.split("\\|");
				for (String exdata : exdatas) {
					String[] temp = exdata.split("\\^");
					L170M01E exLl170m01e_F = new L170M01E();
					this.initl170m01e_oV(exLl170m01e_F, l170m01a,
							user.getUserId(), "L");
					exLl170m01e_F.setCrdType(temp[0]);
					exLl170m01e_F.setGrade(temp[1]);
					exLl170m01e_F.setCrdTYear("".equals(temp[3]) ? null
							: new Date(temp[3].replace("-", "/")));
					service.save(exLl170m01e_F);
				}
			}

			// 將FORMF入 MODE
			DataParse.toBean(formL170m01a, l170m01a);
			l170m01a.setDeletedTime(null);
			list.add(l170m01a);
		}
		return list;
	}

	/**
	 * 轉換params裡面的內容
	 * 
	 * @param params
	 *            PageParameters
	 * @return PageParameters
	 */
	private PageParameters convertParameters(PageParameters params) {

		if (!CapString.isEmpty(params.getString("retrialDate"))) {
			params.put("retrialDate", CapDate.getDate(
					params.getString("retrialDate"), YYYY_MM_DD1));
		}
		if (!CapString.isEmpty(params.getString("lastRetrialDate"))) {
			params.put("lastRetrialDate", CapDate.getDate(
					params.getString("lastRetrialDate"), YYYY_MM_DD1));
		}
		// fromDate1
		if (!CapString.isEmpty(params.getString("fromDate1"))) {
			params.put("fromDate1",
					CapDate.getDate(params.getString("fromDate1"), YYYY_MM_DD1));
		}
		// endDate1
		if (!CapString.isEmpty(params.getString("endDate1"))) {
			params.put("endDate1",
					CapDate.getDate(params.getString("endDate1"), YYYY_MM_DD1));
		}
		// fromDate2
		if (!CapString.isEmpty(params.getString("fromDate2"))) {
			params.put("fromDate2",
					CapDate.getDate(params.getString("fromDate2"), YYYY_MM_DD1));
		}
		// endDate2
		if (!CapString.isEmpty(params.getString("endDate2"))) {
			params.put("endDate2",
					CapDate.getDate(params.getString("endDate2"), YYYY_MM_DD1));
		}
		// fromDate3
		if (!CapString.isEmpty(params.getString("fromDate3"))) {
			params.put("fromDate3",
					CapDate.getDate(params.getString("fromDate3"), YYYY_MM_DD1));
		}
		// endDate3
		if (!CapString.isEmpty(params.getString("endDate3"))) {
			params.put("endDate3",
					CapDate.getDate(params.getString("endDate3"), YYYY_MM_DD1));
		}

		return params;
	}

	@SuppressWarnings("unchecked")
	@Override
	public CapAjaxFormResult queryL170m01d(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01A l170m01a = service.findModelByMainId(L170M01A.class, mainId);
		String custId = l170m01a.getCustId();
		String dupNo = l170m01a.getDupNo();
		String deleteData = params.getString("deleteData");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch branchtype = branchService.getBranch(user.getUnitNo());
		Locale locale = LMSUtil.getLocale();

		String newRptId = "";
		String newReviewType = "";
		if ("Y".equals(deleteData)) {
			service.deleteL170m01dList(mainId);
			// l170m01a.setRptId(LrsUtil.V_201809); //J-107-0128海外改格式
			newRptId = retrialService.getLatestRetrialItemVer_OverSea(l170m01a);
			l170m01a.setRptId(newRptId); // J-107-0128海外改格式
		}

		if ("".equals(Util.nullToSpace(l170m01a.getRptId()))) {
			newReviewType = "lms1705s04_reviewType";
		} else {
			// J-108-0888_05097_B1001

			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202406)) {
				// J-113-0204 新增及修正說明文句
				newReviewType = "lms1705s04_reviewTypeV7";				
			} else if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202404)) {
				// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
				newReviewType = "lms1705s04_reviewTypeV6";
			} else if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202307)) {
				// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
				newReviewType = "lms1705s04_reviewTypeV5";
			}
			// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
			else if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202210)) {
				newReviewType = "lms1705s04_reviewTypeV4";
			} else if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_201907)) {
				newReviewType = "lms1705s04_reviewTypeV3";
			} else {
				newReviewType = "lms1705s04_reviewTypeV2";
			}

		}

		List<L170M01D> list = null;
		list = service.findL170m01dByMainId(mainId);
		// 如果沒有此mainId資料
		if (list.isEmpty()) {
			// 找CodeType裡的資料使用TreeMap排序
			List<CodeType> codetypeList = null;
			if ("".equals(Util.nullToSpace(l170m01a.getRptId()))) {
				codetypeList = codetypeService
						.findByCodeTypeList(newReviewType); // lms1705s04_reviewType
			} else { // J-107-0128海外改格式
				if (Util.equals(locale, "en")
						&& UtilConstants.Country.加拿大.equals(branchtype
								.getCountryType())) {
					codetypeList = codetypeService
							.findByCodeTypeList(newReviewType + "_CA"); // lms1705s04_reviewTypeV2_CA
				} else {
					codetypeList = codetypeService
							.findByCodeTypeList(newReviewType); // lms1705s04_reviewTypeV2
				}
			}
			list = new ArrayList<L170M01D>();
			for (CodeType codetype : codetypeList) {
				L170M01D model = new L170M01D();
				model.setMainId(mainId);
				model.setCustId(custId);
				model.setDupNo(dupNo);
				model.setItemNo(codetype.getCodeValue());
				model.setItemContent(codetype.getCodeDesc());
				// 覆審類別 A,B...
				model.setItemType(codetype.getCodeDesc2());
				model.setItemSeq(codetype.getCodeOrder());
				// J-108-0888_05097_B1001
				if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
						LrsUtil.V_O_201907)) {
					model.setItemSeqShow(codetype.getCodeDesc3());
				} else {
					model.setItemSeqShow(null);
				}

				model.setChkResult(null);
				list.add(model);
			}
			service.saveL170m01dList(list);
		}

		List<String> realRpFgNoShow = new ArrayList<String>();
		List<L170M01D> copyList= new ArrayList<L170M01D>();
		if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
				LrsUtil.V_O_202406)) {
			realRpFgNoShow.add("A005");
			realRpFgNoShow.add("Y113");
			realRpFgNoShow.add("Y240");
			realRpFgNoShow.add("Y250");
			realRpFgNoShow.add("Y214");
			realRpFgNoShow.add("Y215");
			realRpFgNoShow.add("Y300");
			realRpFgNoShow.add("Y310");
			realRpFgNoShow.add("Y31A");
			realRpFgNoShow.add("Y320");
			realRpFgNoShow.add("Y330");
			realRpFgNoShow.add("Y33A");
			realRpFgNoShow.add("Y33B");
			realRpFgNoShow.add("Y33C");
			realRpFgNoShow.add("Y33D");
		}	
		String itemNo = "";
		String itemContent = "";
		for (L170M01D l170m01d : list) {

			itemNo = Util.trim(l170m01d.getItemNo());
			itemContent = Util.trim(l170m01d.getItemContent());
			
			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202406)) {
				if (Util.equals("Y", Util.trim(l170m01a.getRealRpFg()))) {

					if (realRpFgNoShow.contains(itemNo)) {
						continue;
					}

					if (Util.equals("Y210", itemNo)) {
						itemContent = "1." + itemContent;
					}
					if (Util.equals("Y220", itemNo)) {
						itemContent = "2." + itemContent;
					}
					if (Util.equals("Y230", itemNo)) {
						itemContent = "3." + itemContent;
					}

				} else {
					if (Util.equals("Y240", l170m01d.getItemNo())) {
						itemContent = "1." + itemContent;
					}
					if (Util.equals("Y250", l170m01d.getItemNo())) {
						itemContent = "2." + itemContent;
					}
					if (Util.equals("Y210", l170m01d.getItemNo())) {
						itemContent = "3." + itemContent;
					}
					if (Util.equals("Y220", l170m01d.getItemNo())) {
						itemContent = "4." + itemContent;
					}
					if (Util.equals("Y230", l170m01d.getItemNo())) {
						itemContent = "5." + itemContent;
					}
				}
			}
			L170M01D l170m01dNew = new L170M01D();
			CapBeanUtil.copyBean(l170m01d, l170m01dNew,CapBeanUtil.getFieldName(L170M01D.class, true));
			l170m01dNew.setItemContent(itemContent);
			copyList.add(l170m01dNew);
		}
						
		JSONArray ja = new JSONArray();
		JSONObject typeJson = new JSONObject();
		for (L170M01D model : copyList) {
			JSONObject data = DataParse.toJSON(model);
			// 覆審類別 A,B...
			String type = model.getItemType();
			int count = Util.parseInt((String) typeJson.get(type));
			typeJson.put(type, String.valueOf(++count));
			ja.add(data);
		}

		// System.out.println("===JA===>" + ja);
		// System.out.println("= typeJson.toString()====>" +
		// typeJson.toString());
		result.set("L170M01DArray", ja);
		result.set("typeJson", typeJson.toString());
		result.set("rptId", Util.nullToSpace(l170m01a.getRptId()));
		result.set("Country", branchtype.getCountryType());
		result.set("realRpFg", Util.trim(l170m01a.getRealRpFg()));
		return result;
	};

	@Override
	public CapAjaxFormResult updateElf412(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01A l170m01a = service.findModelByMainId(L170M01A.class, mainId);
		String custId = l170m01a.getCustId();
		String dupNo = l170m01a.getDupNo();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1705S01Panel.class);
		String branch = Util.trim(l170m01a.getOwnBrId());
		L180M01A l180m01a = null;
		try {
			int k = service.checkDataBeforUpdate412(mainId, custId, dupNo);
			if (k == 0) {
				List<L180M01A> l180m01aList = service.findL180m01a(branch,
						l170m01a.getRetrialDate());

				// 如果有名單才可以更新覆審控制黨
				if (l180m01aList.isEmpty()) {
					// EFD3002=INFO|查無覆審名單|(l180m01a沒有此筆覆審名單)
					result.set("WARMCODE", "EFD3002");
					result.set("retrialDate",
							TWNDate.toAD(l170m01a.getRetrialDate()));
					result.set("check", "N");
				} else if (l180m01aList.size() > 1) {
					// EFD3043=INFO|有多筆複審日期之複審名單
					result.set("WARMCODE", "EFD3043");
					result.set("retrialDate",
							TWNDate.toAD(l170m01a.getRetrialDate()));
					result.set("check", "N");
				} else {
					if (service.updateElf412(l170m01a, mainId, custId, dupNo,
							branch)) {
						// (2)檢查是否有覆審序號及案號
						Integer projectSeq = l170m01a.getProjectSeq();
						String projectNo = l170m01a.getProjectNo();

						List<Object[]> number = null;
						String mainIdMax = "";

						// 重新更新先刪除
						l180m01a = l180m01aList.get(0);
						mainIdMax = l180m01a.getMainId();
						// Insert一筆至L180M01B
						service.insertUpdate180M01B(mainIdMax, custId, dupNo,
								branch, l170m01a.getRetrialDate(), l170m01a,
								l180m01a);
						service1805.deleteL180m01cList(mainIdMax, custId,
								dupNo, l170m01a.getCtlType());
						// 新增一筆至L180M01C
						service.insert180M01C(mainIdMax, custId, dupNo, branch,
								mainIdMax, l170m01a.getCtlType());
						// 至Service1805 取得覆審案號
						number = service1805.l170M01Aproject(mainIdMax);
						if (number != null && number.size() > 0) {
							Object[] object = number.get(0);

							projectSeq = (Integer) object[0];
							projectNo = Util.trim(object[1]);

							l170m01a.setProjectSeq(projectSeq);
							l170m01a.setProjectNo(projectNo);
							service.save(l170m01a);

						} else {

						}
						L170M01F l170m01f = service.findModelByMainId(
								L170M01F.class, mainId);

						if (l170m01f == null) {
							l170m01f = new L170M01F();
							l170m01f.setMainId(mainId);
							l170m01f.setDupNo(dupNo);
							l170m01f.setCustId(custId);
						}

						l170m01f.setUpDate(CapDate.getCurrentTimestamp());
						service.save(l170m01f);
						// 顯示在畫面上的格式
						result.set("upDate",
								TWNDate.toFullAD(l170m01f.getUpDate()));
						// EFD0018=INFO|執行成功|
						result.set("SUCCESSCODE", "EFD0018");
						result.set("check", "Y");
					}// updateElf412-End
				}

			} else {
				switch (k) {
				case 1:
					// 信用評等有,授信資料無資料
					// EFD3007=ERROR|必須先引進$\{colName\}|
					result.set("ERRORCODE", "EFD3007");
					result.set("colName", pop.getProperty("L170M01a.error1"));
				case 2:
					// 信用評等無,授信資料有資料
					// EFD0005=ERROR|$\{colName\}此欄位不可空白| (信用評等)
					result.set("ERRORCODE", "EFD0005");
					result.set("colName", pop.getProperty("L170M01a.credit"));
				default:
					// 信用評等和授信資料皆無資料
					result.set("ERRORCODE", "EFD3007");
					result.set("colName", pop.getProperty("L170M01a.error2"));
				}

			}

		} finally {

		}
		return result;
	}

	@Override
	public CapAjaxFormResult addCredit(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01A l170m01a = service.findModelByMainId(L170M01A.class, mainId);
		String custId = l170m01a.getCustId();
		String dupNo = l170m01a.getDupNo();
		String brNo = l170m01a.getOwnBrId();

		// 重新產生授信資料之前先刪除原先存在的資料(下SQL大量刪除)
		// List<L170M01B> l170m01b = service.findL170m01bList(mainId);
		// l170m01a.setTotQuota(null);
		// l170m01a.setTotBal(null);
		// service.save(l170m01a);
		// List<L170M01B> l170m01bList = new LinkedList<L170M01B>();
		// for(L170M01B bean : l170m01b){
		// if(bean.getLnDataDate() != null){
		// l170m01bList.add(bean);
		// }
		// }
		//
		// if (!l170m01b.isEmpty()) {
		//
		// }
		service.deleteL170m01bListNotLnDataDate(mainId);

		// 再重新產生授信資料
		Map<String, String> balMap = this.saveL170m01bByBrNoCustId(brNo,
				custId, mainId, dupNo, l170m01a);
		if ("Y".equals(balMap.get("RESULT"))) {
			// (1)引進授信資料引進日期,(2)儲存於主table(額度合計,前日結欠餘額合計授信資料引進日期)
			l170m01a.setLnDataDate(LMSUtil.getExMonthLastDay(-1));
			l170m01a.setTotQuota(LMSUtil.toBigDecimal(balMap.get("totQuota")));
			l170m01a.setTotBal(LMSUtil.toBigDecimal(balMap.get("totBal")));
			service.save(l170m01a);
			// 顯示在畫面上的格式
			result.set("lnDataDate",
					TWNDate.toAD(CapDate.getCurrentTimestamp()));
			if (balMap.get("totBal") != null) {
				result.set("totBal", NumConverter.addComma(
						LMSUtil.toBigDecimal(balMap.get("totBal")).divide(
								new BigDecimal(1000)), "#,##0.00"));
			} else {
				result.set("totBal", "0.00");
			}
			if (balMap.get("totQuota") != null) {
				result.set("totQuota", NumConverter.addComma(
						LMSUtil.toBigDecimal(balMap.get("totQuota")).divide(
								new BigDecimal(1000)), "#,##0.00"));
			} else {
				result.set("totQuota", "0.00");
			}
		} else {
			// 查無此帳號
			result.set("WARMCODE", "EFD0036");
			// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
			// .getMainMessage(this.getComponent(), "EFD0036"));
			if (l170m01a.getTotBal() != null) {
				result.set("totBal", NumConverter.addComma(
						LMSUtil.toBigDecimal(l170m01a.getTotBal()).divide(
								new BigDecimal(1000)), "#,##0.00"));
			} else {
				result.set("totBal", "0.00");
			}
			if (l170m01a.getTotQuota() != null) {
				result.set("totQuota", NumConverter.addComma(
						LMSUtil.toBigDecimal(l170m01a.getTotQuota()).divide(
								new BigDecimal(1000)), "#,##0.00"));
			} else {
				result.set("totQuota", "0.00");
			}
		}
		return result;
	}

	@Override
	public CapAjaxFormResult deleteCredit(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = params.getString(EloanConstants.MAIN_ID);
		L170M01A l170m01a = service.findModelByMainId(L170M01A.class, mainId);
		Map<String, String> map = null;
		// 刪除原先存在的資料(下SQL大量刪除)
		boolean deleteResult = service.deleteL170m01bListNotLnDataDate(mainId);
		if (deleteResult) {
			List<L170M01B> l170m01bList = l170m01bDao.findByMainId(mainId);
			map = this.sumL170M01BAllAndSaveL170M01A(l170m01a.getOwnBrId(),
					mainId, l170m01a, l170m01bList, "2");
			result = this.handleRateMap(map, result);
			result.set("SUCCESSCODE", "EFD0019");
			if (map.get("totBal") != null) {
				result.set("totBal", LMSUtil.toBigDecimal(map.get("totBal"))
						.divide(new BigDecimal(1000)));
			} else {
				result.set("totBal", "0.00");
			}
			if (map.get("totQuota") != null) {
				result.set(
						"totQuota",
						LMSUtil.toBigDecimal(map.get("totQuota")).divide(
								new BigDecimal(1000)));
			} else {
				result.set("totQuota", "0.00");
			}

		}
		return result;
	}

	@Override
	public CapAjaxFormResult deleteChkCredit(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String oids = params.getString("oids");
		String[] tempOid = oids.split("\\^");
		L170M01A l170m01a = service.findModelByMainId(L170M01A.class, mainId);
		List<L170M01B> l170m01bList = l170m01bDao.findByMainId(mainId);
		List<L170M01B> list = new LinkedList<L170M01B>();
		for (L170M01B l170m01b : l170m01bList) {
			for (String oid : tempOid) {
				if (oid.equals(l170m01b.getOid())) {
					list.add(l170m01b);
				}
			}
		}
		l170m01bDao.delete(list);
		l170m01bList = l170m01bDao.findByMainId(mainId);
		Map<String, String> map = this.sumL170M01BAllAndSaveL170M01A(
				l170m01a.getOwnBrId(), mainId, l170m01a, l170m01bList, "3");
		result = this.handleRateMap(map, result);
		result.set("SUCCESSCODE", "EFD0019");
		if (map.get("totBal") != null) {
			result.set(
					"totBal",
					NumConverter.addComma(
							LMSUtil.toBigDecimal(map.get("totBal")).divide(
									new BigDecimal(1000)), "#,##0.00"));
		} else {
			result.set("totBal", "0.00");
		}
		if (map.get("totQuota") != null) {
			result.set(
					"totQuota",
					NumConverter.addComma(
							LMSUtil.toBigDecimal(map.get("totQuota")).divide(
									new BigDecimal(1000)), "#,##0.00"));
		} else {
			result.set("totQuota", "0.00");
		}
		return result;
	}

	@Override
	public L170M01C setC170M01CDefault(String mainId, String custId,
			String dupNo, String curr, String ratioNo1, String ratioNo2,
			String ratioNo3, String ratioNo4) {
		L170M01C l170m01c = new L170M01C();
		l170m01c.setMainId(mainId);
		l170m01c.setCustId(custId);
		l170m01c.setDupNo(dupNo);
		l170m01c.setUnit(new BigDecimal(1000));
		l170m01c.setCurr(curr);
		// 預設值
		l170m01c.setRatioNo1(Util.isEmpty(ratioNo1) ? UtilConstants.ratioNo.負債比率
				: ratioNo1);
		l170m01c.setRatioNo2(Util.isEmpty(ratioNo2) ? UtilConstants.ratioNo.流動比率
				: ratioNo2);
		l170m01c.setRatioNo3(Util.isEmpty(ratioNo3) ? UtilConstants.ratioNo.速動比率
				: ratioNo3);
		l170m01c.setRatioNo4(Util.isEmpty(ratioNo4) ? UtilConstants.ratioNo.固定長期適合率
				: ratioNo4);
		return l170m01c;
	}

	@Override
	public CapAjaxFormResult addL170m01a(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1705S01Panel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String tabForm = params.getString("tabForm");
		JSONObject jobject = JSONObject.fromObject(tabForm);
		String custId = jobject.getString("custId");
		custId = custId.toUpperCase();
		String dupNo = jobject.getString("dupNo");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String branchId = user.getUnitNo();
		String docStatus = RetrialDocStatusEnum.編製中.getCode();

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		String ctlType = jobject.optString("ctlType", LrsUtil.CTLTYPE_主辦覆審);

		// J-107-0128海外改格式
		// J-108-0888_05097_B1001
		String newDefaultRptId = retrialService
				.getLatestRetrialItemVer_OverSea(null);
		String rptId = jobject.optString("rptId", newDefaultRptId);

		if (service.findL170M01AByUnkey(custId, dupNo, branchId, docStatus,
				ctlType)) {
			try {
				// 查詢ELF412有無此筆資料,沒有也可以自行新增
				Map<String, Object> dataMap = service
						.findMisByCustIdDupNoBranch(custId, dupNo, branchId);
				if (dataMap == null) {
					dataMap = new LinkedHashMap<String, Object>();
				}
				L170M01A l170m01a = new L170M01A();
				L170A01A l170a01a = new L170A01A();
				L170M01C l170m01c = new L170M01C();
				L170M01F l170m01f = new L170M01F();
				// L170M01G l170m01g = new L170M01G();

				String maiinId = IDGenerator.getUUID();
				// BranchRate branchRate = lmsService.getBranchRate(branchId);
				// ELF412有此筆資料則新增覆審報告表
				if (!dataMap.isEmpty() && dataMap.get("custId").equals(custId)
						&& dataMap.get("dupNo").equals(dupNo)) {
					// 上次覆審日期 lastRetrialDate
					Date lrdate = (Date) dataMap.get("lrdate");
					// 不覆審代碼 nCkdFlag
					String nckdflag = Util.trim((String) dataMap
							.get("nCkdFlag"));
					// 取得本未幣幣別
					l170m01a.setNCkdFlag(nckdflag);
					l170m01a.setLastRetrialDate(lrdate);
					l170m01a.setRetrialDate(CapDate.getCurrentTimestamp());
					l170m01a.setCreateTime(CapDate.getCurrentTimestamp());
				} else {
					// ELF412無此筆資料也可新增覆審報告表
				}
				String curr = branchService.getBranch(branchId).getUseSWFT();
				l170m01a.setMainId(maiinId);
				l170m01a.setCustId(custId);
				l170m01a.setDupNo(dupNo);

				// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				l170m01a.setCtlType(ctlType);
				l170m01a.setTotBalCurr(curr);
				l170m01a.setTotQuotaCurr(curr);
				l170m01a.setTotBal(BigDecimal.ZERO);
				l170m01a.setTotQuota(BigDecimal.ZERO);
				l170m01a.setTypCd(TypCdEnum.海外.getCode());
				l170m01a.setDeletedTime(CapDate.getCurrentTimestamp());
				l170m01a.setRandomCode(IDGenerator.getRandomCode());
				l170m01a.setDocStatus(RetrialDocStatusEnum.編製中);
				l170m01a.setOwnBrId(branchId);
				l170m01a.setRptId(rptId); // J-107-0128海外改格式
				// 新增授權檔 l170a01a
				l170a01a.setOwnUnit(branchId);
				l170a01a.setMainId(maiinId);
				l170a01a.setAuthType(DocAuthTypeEnum.MODIFY.getCode());
				l170a01a.setAuthUnit(branchId);
				l170a01a.setAuthTime(CapDate.getCurrentTimestamp());

				// 新增 l170m01c,l170m01e
				l170m01c = this
						.setC170M01CDefault(
								maiinId,
								custId,
								dupNo,
								"".equals(Util.trim(l170m01a.getTotBalCurr())) ? branchService
										.getBranch(
												Util.trim(l170m01a.getOwnBrId()))
										.getUseSWFT()
										: l170m01a.getTotBalCurr(), "", "", "",
								"");

				l170m01f.setMainId(l170m01a.getMainId());
				l170m01f.setCustId(l170m01a.getCustId());
				l170m01f.setDupNo(l170m01a.getDupNo());

				// l170m01g.setMainId(l170m01a.getMainId());
				// l170m01g.setBranchId(l170m01a.getOwnBrId());
				// l170m01g.setBranchType("1");
				// l170m01g.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
				// l170m01g.setStaffName(user.getUserCName());
				// l170m01g.setStaffNo(user.getUserId());

				service.save(l170a01a);
				service.save(l170m01a);
				service.save(l170m01c);
				service.save(l170m01f);
				// service.save(l170m01g);
				// --------startFlow-------------------------------------------
				service.startFlow(l170m01a.getOid(), "LMS1705Flow");
				// if (params.getAsBoolean("showMsg", true)) {
				// // EFD0035=INFO|新增成功!|
				// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				// .getMainMessage(this.getComponent(), "EFD0035"));
				// }
				result.set(EloanConstants.MAIN_OID,
						CapString.trimNull(l170m01a.getOid()));
				result.set(EloanConstants.MAIN_DOC_STATUS,
						l170m01a.getDocStatus());
				result.set(EloanConstants.MAIN_ID,
						CapString.trimNull(l170m01a.getMainId()));
				result.set(EloanConstants.MAIN_UID,
						CapString.trimNull(l170m01a.getUid()));
				result.set("oid", l170m01a.getOid());
				result.set("docURL", l170m01a.getDocURL());
			} catch (Exception e) {
				logger.error("[addL170m01a] service1705.addLms EXCEPTION!!", e);
				result.set("ERRORCODE", "EFD0025");
			}
		} else {
			// EFD0047=WARN|$\{msg\}已存在，請重新輸入!|
			HashMap<String, String> param = new HashMap<String, String>();
			param.put("msg", pop.getProperty("L170M01a.custName"));
			result.set("ERRORCODE", "EFD0047");
			result.set("msg", pop.getProperty("L170M01a.custName"));

		}

		return result;

	};

	@Override
	public CapAjaxFormResult saveL170m01b(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID, "");
		String oid = params.getString(EloanConstants.MAIN_OID, "");
		String lnDataDateResult = params.getString("lnDataDateResult", "");
		String formL170m01b = params.getString("L170M01BForm");
		L170M01B l170m01b = service.findModelByOid(L170M01B.class, oid);
		if (l170m01b == null)
			l170m01b = new L170M01B();
		L170M01A l170m01a = service.findModelByMainId(L170M01A.class, mainId);
		Map<String, String> map = null;

		// 會計科目對應回授信科目(代碼)
		Map<String, String> subItemToAccountingMap = null;
		// 會計科目對應回授信科目
		Map<String, String> subItmeMap = null;
		BigDecimal thsNumber = new BigDecimal(1000);
		DataParse.toBean(formL170m01b, l170m01b);

		if (l170m01b.getBalAmt() != null) {
			l170m01b.setBalAmt(l170m01b.getBalAmt().multiply(thsNumber));
		}
		if (l170m01b.getQuotaAmt() != null) {
			l170m01b.setQuotaAmt(l170m01b.getQuotaAmt().multiply(thsNumber));
		}
		if (l170m01b.getEstAmt() != null) {
			l170m01b.setEstAmt(l170m01b.getEstAmt().multiply(thsNumber));
		}
		if (l170m01b.getLoanAmt() != null) {
			l170m01b.setLoanAmt(l170m01b.getLoanAmt().multiply(thsNumber));
		}
		if (!"Y".equals(lnDataDateResult)) {
			subItemToAccountingMap = codetypeService
					.findByCodeType("lms1405m01_SubItemToAccounting");
			subItmeMap = codetypeService.findByCodeType("lms1705m01_SubItme");
			// 回授信科目(代碼)
			JSONObject jobjectB = JSONObject.fromObject(formL170m01b);
			String loanTP = Util.nullToSpace(jobjectB.get("loanTP"));
			// DW 會計科目
			String actcd = Util
					.nullToSpace((subItemToAccountingMap.get(loanTP)));
			// 授信科目
			String subject = Util.nullToSpace(subItmeMap.get(actcd));
			l170m01b.setActcd(actcd);
			l170m01b.setSubject(subject);
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			l170m01b.setCustId(user.getUserId());
			l170m01b.setDupNo("0");
			l170m01b.setMainId(mainId);
		}
		service.save(l170m01b);
		result.set("L170M01BForm", DataParse.toResult(l170m01b));
		List<L170M01B> l170m01bList = l170m01bDao.findByMainId(mainId);
		map = this.sumL170M01BAllAndSaveL170M01A(l170m01a.getOwnBrId(), mainId,
				l170m01a, l170m01bList, "");
		result = this.handleRateMap(map, result);
		if (map.get("totBal") != null) {
			result.set(
					"totBal",
					NumConverter.addComma(
							LMSUtil.toBigDecimal(map.get("totBal")).divide(
									new BigDecimal(1000)), "#,##0.00"));
		} else {
			result.set("totBal", "0.00");
		}
		if (map.get("totQuota") != null) {
			result.set(
					"totQuota",
					NumConverter.addComma(
							LMSUtil.toBigDecimal(map.get("totQuota")).divide(
									new BigDecimal(1000)), "#,##0.00"));
		} else {
			result.set("totQuota", "0.00");
		}

		result.set("SUCCESSCODE", "EFD0017");
		result.set("temp_oid", l170m01b.getOid());
		return result;
	}

	@Override
	public CapAjaxFormResult saveAll(PageParameters params)
			throws CapException {
		CapWebUtil.showParams(params);
		CapAjaxFormResult result = new CapAjaxFormResult();
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		int page = Util.parseInt(params.getString("page"));
		String mainId = params.getString(EloanConstants.MAIN_ID, "");
		L170M01A l170m01a = service.findModelByMainId(L170M01A.class, mainId);

		List<GenericBean> list = new ArrayList<GenericBean>();
		GenericBean model = null;
		// 匯率轉換處理
		BranchRate branchRate = lmsService.getBranchRate(l170m01a.getOwnBrId());
		L170M01C l170m01c = null;
		String tempSave = "N";
		switch (page) {
		case 1:
			list = this.collectionData3(params, tempSave);
			for (GenericBean modelA : list) {
				try {
					service.save(modelA);
					l170m01a = (L170M01A) modelA;
				} catch (Exception e) {
					logger.error("[tempSave] service1705.save EXCEPTION!!", e);
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0007"), getClass());
				}
			}
			RetrialDocStatusEnum e = RetrialDocStatusEnum.getEnum(Util
					.trim(l170m01a.getDocStatus()));
			String docStatus = e.name();
			String kind = l170m01a.getTypCd();
			// l170m01a2.setDocStatus(docStatus);
			result.set("L170M01aForm", DataParse.toResult(l170m01a));
			result.set("docStatus", docStatus);
			result.set("randomCode", l170m01a.getRandomCode());
			result.set("updater", Util.nullToSpace(userInfoService
					.getUserName(l170m01a.getUpdater())));
			result.set("updateTime", Util.nullToSpace(TWNDate.toFullAD(l170m01a
					.getUpdateTime())));
			result.set("creator", Util.nullToSpace(userInfoService
					.getUserName(l170m01a.getCreator())));
			result.set("createTime", Util.nullToSpace(TWNDate.toFullAD(l170m01a
					.getCreateTime())));
			// 經辦
			result.set("appraiser",
					lmsService.getUserName(l170m01a.getUpdater()));
			result.set("typCd",
					Util.nullToSpace(TypCdEnum.getEnum(kind).toString()));
			break;
		case 2:
			// J-111-0326 海外覆審作業系統改良第一階段：
			// 6. 額度合計、餘額合計有誤(多算很多)，若無法確保正確，須開放可人工修正。
			/**
			 * 2023/02/08 測試 sumL170M01BAllAndSaveL170M01A 正常 合計數字無誤 先不改
			 * 等他們測試遇到數字誤差很多再來抓為什麼
			 */
			break;
		case 3:
			model = this.collectionData(params, tempSave);
			service.save(model);
			l170m01c = (L170M01C) model;
			JSONObject data = DataParse.toJSON(l170m01c, L170M01C.class,
					"#,##0.00");
			result = new CapAjaxFormResult(data);
			result.set("No1", l170m01c.getRatioNo1());
			result.set("No2", l170m01c.getRatioNo2());
			result.set("No3", l170m01c.getRatioNo3());
			result.set("No4", l170m01c.getRatioNo4());
			result.set("l170m01c_oid", Util.trim(l170m01c.getOid()));
			break;

		case 4:
			List<L170M01D> models = this.collectionData2(params,
					tempSave);

			if (models != null) {
				service.saveL170m01dList2(models);
				if (Util.notEquals("", Util.nullToSpace(l170m01a.getRptId()))) { // 新版才檢查
					if (Util.equals("N", SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						String msg = this.checkS04Data(models);
						if (Util.isNotEmpty(msg)) {
							result.set("IncompleteMsg", msg);
						}
					}
				}
			}

			// String formL170m01d = params.getString("L170M01dForm");
			// JSONObject jobjectD = JSONObject.fromObject(formL170m01d);
			// JSONArray itemNo = jobjectD.getJSONArray("itemNo");
			// String chkPreReview = (String) jobjectD.get("chkPreReview");
			// String chkCheck = (String) jobjectD.get("chkCheck");

			// for (int i = 0; i < itemNo.size(); i++) {
			// L170M01D l170m01d = service.findL170m01dByMainId(mainId,
			// custId, dupNo, Util.trim(itemNo.getString(i)));
			//
			// String count = String.valueOf(i + 1);
			// if ("19".equals(count)) {
			// // System.out.println("!!!!!!!!!");
			// }
			//
			// String chkResult = (String) jobjectD.get("chkResult" + count);
			// String chkText = (String) jobjectD.get("chkText" + count);
			//
			// l170m01d.setChkResult(Util.isEmpty(chkResult) ? "K" : chkResult);
			//
			// l170m01d.setChkText(chkText);
			//
			// if ("A002".equals(l170m01d.getItemNo())) {
			// if ("".equals(chkCheck) || chkCheck == null) {
			// chkCheck = "N";
			// }
			// l170m01d.setChkCheck(chkCheck);
			// }
			// if ("B014".equals(l170m01d.getItemNo())) {
			// if (!Util.isEmpty(chkPreReview)) {
			// l170m01d.setChkPreReview(chkPreReview);
			// }
			// }
			// // 後端驗證長度 (L170M01d.title6=異常內容說明)
			// pop.put("chkText", pop.getProperty("L170M01d.title6"));
			// validate = Util.validateColumnSize(l170m01d, pop, "L170M01d");
			// if (validate != null) {
			// Map<String, String> param = new HashMap<String, String>();
			// param.put("colName", validate);
			// throw new CapMessageException(RespMsgHelper.getMessage(
			// parent, "EFD0007", param), getClass());
			// }
			// // DataParse.toBean(formL170m01d, l170m01d);
			// service.save(l170m01d);
			// }

			break;
		case 5:
			model = this.collectionData(params, tempSave);
			if (model != null) {
				service.save(model);
			}

			// String formL170m01f1 = params.getString("L170M01F1Form");
			// String formL170m01f2 = params.getString("L170M01F2Form");
			//
			// L170M01F l170m01f = service.findModelByMainId(L170M01F.class,
			// mainId);
			// if (l170m01f == null) {
			//
			// l170m01f = new L170M01F();
			// l170m01f.setMainId(l170m01a.getMainId());
			// l170m01f.setCustId(l170m01a.getCustId());
			// l170m01f.setDupNo(l170m01a.getDupNo());
			//
			// DataParse.toBean(formL170m01f1, l170m01f);
			// DataParse.toBean(formL170m01f2, l170m01f);
			//
			// } else {
			//
			// DataParse.toBean(formL170m01f1, l170m01f);
			// DataParse.toBean(formL170m01f2, l170m01f);
			//
			// }
			// // 後端驗證長度 (L170M01d.branchComm=受檢單位洽辦情形),(L170M01F.condition)
			// pop.put("condition", pop.getProperty("L170M01d.condition"));
			// pop.put("branchComm", pop.getProperty("L170M01d.branchComm"));
			// validate = Util.validateColumnSize(l170m01f, pop, "L170M01d");
			// if (validate != null) {
			// Map<String, String> param = new HashMap<String, String>();
			// param.put("colName", validate);
			// throw new CapMessageException(RespMsgHelper.getMessage(parent,
			// "EFD0007", param), getClass());
			// }
			// service.save(l170m01f);
			result.set("typCd1", Util.nullToSpace(TypCdEnum.getEnum(
					l170m01a.getTypCd()).toString()));

			break;
		default:
			break;
		}
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L170M01G l170m01gL1 = service.findL170m01gByBranchTypeStaffJob(mainId,
				lrsConstants.BRANCHTYPE.覆審單位, UtilConstants.STAFFJOB.分行單位主管L6);
		if (l170m01gL1 == null)
			l170m01gL1 = new L170M01G();
		l170m01gL1.setMainId(l170m01a.getMainId());
		l170m01gL1.setBranchId(l170m01a.getOwnBrId());
		l170m01gL1.setBranchType(lrsConstants.BRANCHTYPE.覆審單位);
		l170m01gL1.setStaffJob(UtilConstants.STAFFJOB.分行單位主管L6);
		l170m01gL1.setStaffName(user.getUserCName());
		l170m01gL1.setStaffNo(user.getUserId());
		l170m01gDao.save(l170m01gL1);

		l170m01a.setTotBalCurr(branchRate.getMCurr());
		l170m01a.setTotQuotaCurr(branchRate.getMCurr());
		l170m01a.setDeletedTime(null);
		// l170m01aDao.save(l170m01a);
		lms1705Service.save(l170m01a);
		result.set("totBalCurr", l170m01a.getTotBalCurr());
		if (params.getAsBoolean("showMsg", true)) {
			// EFD0017=INFO|儲存成功|
			result.set("SUCCESSCODE", "EFD0017");
			result.set("staffJobL1", Util.nullToSpace(userInfoService
					.getUserName(Util.nullToSpace(l170m01gL1.getStaffNo()))));
			// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
			// .getMainMessage(this.getComponent(), "EFD0017"));
		}
		return result;
	}

	@Override
	public Map<String, String> saveL170m01bByBrNoCustId(String brNo,
			String custId, String mainId, String dupNo, L170M01A l170m01a)
			throws CapException {
		Map<String, String> map = new LinkedHashMap<String, String>();
		List<Map<String, Object>> rows = null;
		// 會計科目對應回授信科目(代碼)
		Map<String, String> subItemToAccountingMap = null;
		Map<String, String> vauleToKeyMap = new LinkedHashMap<String, String>();
		// 會計科目對應回授信科目
		Map<String, String> subItmeMap = null;
		List<L170M01B> list = null;
		// List<L170M01B> deleteL170M01bList = new ArrayList<L170M01B>();
		try {
			// list = l170m01bDao.findByMainId(mainId);
			// for(L170M01B l170m01b : list){
			// if(l170m01b.getLnDataDate() != null){
			// deleteL170M01bList.add(l170m01b);
			// }
			// }
			// l170m01bDao.delete(deleteL170M01bList);
			rows = dwdbService.findDW_ASLNDAVGOVSJOIN_ByBrNoCustId(brNo,
					custId, dupNo);
			subItemToAccountingMap = codetypeService
					.findByCodeType("lms1405m01_SubItemToAccounting");
			// 會有值重複問題,JASON說蓋掉就好
			for (String key : subItemToAccountingMap.keySet()) {
				vauleToKeyMap.put(subItemToAccountingMap.get(key), key);
			}
			subItmeMap = codetypeService.findByCodeType("lms1705m01_SubItme");
			list = new ArrayList<L170M01B>();
			for (Map<String, Object> dataMap : rows) {
				L170M01B l170m01b = new L170M01B();
				// CUSTID
				String l170m01bCustId = Util.nullToSpace((dataMap
						.get("CUST_KEY")));
				// 額度序號
				String contract = Util.nullToSpace((dataMap.get("CONTRACT")));
				// 帳號
				String loanNo = Util.nullToSpace((dataMap.get("ACCT_KEY")));
				// 資料日期
				String lnDataDate = Util.nullToSpace((dataMap
						.get("DW_DATA_SRC_DT")));
				// DW 會計科目
				String actcd = Util.nullToSpace((dataMap.get("GL_AC_KEY")));
				// 回授信科目(代碼)
				String loanTP = Util.nullToSpace(vauleToKeyMap.get(actcd));
				// 授信科目
				String subject = Util.nullToSpace(subItmeMap.get(actcd));
				// 新貸/舊案
				String newCase = null;
				// 額度(幣別)
				String quotaCurr = Util.nullToSpace((dataMap.get("FACT_SWFT")));
				// 額度(匯率)
				// String quotaRate = null;
				// 額度(金額)
				String quotaAmt = Util.nullToSpace((dataMap.get("FACT_AMT")));
				// 前日結欠餘額(幣別)
				String balCurr = Util.nullToSpace((dataMap.get("CUR_CD")));
				// 前日結欠餘額(匯率)
				// String balRate = null;
				// 前日結欠餘額(金額)
				String balAmt = Util.nullToSpace((dataMap.get("LN_BAL")));
				// 動用期限或授信期間(起)
				String fromDate = Util.nullToSpace((dataMap.get("DURING_BG")));
				// 動用期限或授信期間(迄)
				String endDate = Util.nullToSpace((dataMap.get("DURING_ED")));

				Map<String, ?> cntrnoMap = misLms422Service.findLMS422ByCntrNo(
						custId, dupNo, contract, brNo);
				if (cntrnoMap != null) {
					newCase = Util.nullToSpace(cntrnoMap.get("NEWCASE"));
				}
				l170m01b.setLoanNo(loanNo);
				l170m01b.setLnDataDate("".equals(lnDataDate) ? null : TWNDate
						.valueOf(lnDataDate));
				l170m01b.setLoanTP(loanTP);
				l170m01b.setSubject(subject);
				l170m01b.setActcd(actcd);
				l170m01b.setNewCase(newCase);
				l170m01b.setQuotaCurr(quotaCurr);
				// l170m01b.setQuotaRate(LMSUtil.toBigDecimal(quotaRate));
				if (LMSUtil.toBigDecimal(quotaAmt) == null) {
					l170m01b.setQuotaAmt(LMSUtil.toBigDecimal(0));
				} else {
					l170m01b.setQuotaAmt(LMSUtil.toBigDecimal(quotaAmt)
							.setScale(2, BigDecimal.ROUND_HALF_UP));
				}
				l170m01b.setBalCurr(balCurr);
				// l170m01b.setBalRate(LMSUtil.toBigDecimal(balRate));
				if (LMSUtil.toBigDecimal(balAmt) == null) {
					l170m01b.setBalAmt(LMSUtil.toBigDecimal(0));
				} else {
					l170m01b.setBalAmt(LMSUtil.toBigDecimal(balAmt).setScale(2,
							BigDecimal.ROUND_HALF_UP));
				}

				l170m01b.setFromDate("".equals(fromDate) ? null : TWNDate
						.valueOf(fromDate));
				l170m01b.setEndDate("".equals(endDate) ? null : TWNDate
						.valueOf(endDate));

				l170m01b.setCustId(l170m01bCustId);
				l170m01b.setDupNo("0");
				l170m01b.setCntrNo(contract);
				l170m01b.setMainId(mainId);
				l170m01b.setDupNo(dupNo);

				Map<String, Object> mapIn = service.getCMSData(l170m01a,
						l170m01b.getCntrNo());
				l170m01b.setGuaranteeName(Util.trim(mapIn.get("guaranteeName")));
				l170m01b.setLoanCurr(Util.trim(mapIn.get("loanCurr")));
				l170m01b.setEstCurr(Util.trim(mapIn.get("EstCurr")));
				l170m01b.setLoanAmt(LMSUtil.nullToZeroBigDecimal(mapIn
						.get("loanAmt")));
				l170m01b.setEstAmt(LMSUtil.nullToZeroBigDecimal(mapIn
						.get("estAmt")));

				list.add(l170m01b);
			}
			List<L170M01B> l170m01bList = null;
			if (!list.isEmpty()) {
				// 一次儲存
				l170m01bDao.save(list);
				l170m01bList = l170m01bDao.findByMainId(mainId);
				map = this.sumL170M01BAllAndSaveL170M01A(brNo, mainId,
						l170m01a, l170m01bList, "1");
				map.put("RESULT", "Y");
			} else {
				l170m01bList = new LinkedList<L170M01B>();
				map = this.sumL170M01BAllAndSaveL170M01A(brNo, mainId,
						l170m01a, l170m01bList, "1");
				map.put("RESULT", "N");
			}
		} catch (Exception e) {
			logger.error(
					"LMS170502ServiceImpl saveL170m01bByBrNoCustId EXCEPTION!!",
					e);
			throw new CapException();
		} finally {
			if (rows != null) {
				rows.clear();
			}
			if (subItemToAccountingMap != null) {
				subItemToAccountingMap.clear();
			}
			if (subItmeMap != null) {
				subItmeMap.clear();
			}
		}
		return map;
	}

	@Override
	public Map<String, String> sumL170M01BAllAndSaveL170M01A(String brNo,
			String mainId, L170M01A l170m01a, List<L170M01B> l170m01bList,
			String lnDataDateResult) throws CapException {
		Map<String, String> map = new LinkedHashMap<String, String>();
		// 匯率轉換處理
		BranchRate branchRate = lmsService.getBranchRate(brNo);
		BigDecimal totQuota = BigDecimal.ZERO;
		BigDecimal totBal = BigDecimal.ZERO;
		String totQuotaCurr = null;
		String totBalCurr = null;
		List<Map<String, Object>> currList = null;
		// 儲存有用過的額度序號
		List<String> contrnoList = new LinkedList<String>();
		boolean brFlag = false;
		try {
			if (l170m01a != null) {
				totQuotaCurr = Util.nullToSpace(l170m01a.getTotQuotaCurr());
				totBalCurr = Util.nullToSpace(l170m01a.getTotBalCurr());
				// 國外分行匯率 2=國外
				if ("2".equals(branchService.getBranch(brNo).getBrNoFlag())) {
					brFlag = false;
				} else {
					currList = misRateblService.listNewest();
					brFlag = true;
				}
			} else {
			}
			if ("".equals(totQuotaCurr.trim())) {
				totQuotaCurr = Util.nullToSpace(branchRate.getMCurr());
			}
			if ("".equals(totBalCurr.trim())) {
				totBalCurr = Util.nullToSpace(branchRate.getMCurr());
			}
			for (L170M01B l170m01b : l170m01bList) {
				boolean result = true;
				for (String key : contrnoList) {
					if (l170m01b.getCntrNo().contains(key)) {
						result = false;
					}
				}
				if (l170m01a != null) {
					// 台幣
					if (!brFlag) {
						if (l170m01b.getQuotaAmt() != null
								&& l170m01b.getBalAmt() != BigDecimal.ZERO) {
							try {
								if (result) {
									totQuota = totQuota.add(branchRate
											.toOtherAmt(
													l170m01b.getQuotaCurr(),
													totQuotaCurr,
													l170m01b.getQuotaAmt()));
								}

							} catch (Exception e) {
								map = this.setNoRateCurrMap(map,
										l170m01b.getQuotaCurr());
							}
						}
						if (l170m01b.getBalAmt() != null) {
							try {
								totBal = totBal.add(branchRate.toOtherAmt(
										l170m01b.getBalCurr(), totBalCurr,
										l170m01b.getBalAmt()));
							} catch (Exception e) {
								map = this.setNoRateCurrMap(map,
										l170m01b.getBalCurr());
							}
						}

					} else {
						for (Map<String, Object> currMap : currList) {
							if (currMap.get("CURR").equals(
									l170m01b.getQuotaCurr())) {
								if (l170m01b.getQuotaAmt() != null) {
									try {
										if (result) {
											totQuota = totQuota
													.add(l170m01b
															.getQuotaAmt()
															.multiply(
																	new BigDecimal(
																			Util.nullToSpace(currMap
																					.get("ENDRATE")))));
										}

									} catch (Exception e) {
										map = this.setNoRateCurrMap(map,
												l170m01b.getQuotaCurr());
									}
								}
							}
							if (currMap.get("CURR").equals(
									l170m01b.getBalCurr())) {
								if (l170m01b.getBalAmt() != null) {
									try {
										totBal = totBal
												.add(l170m01b
														.getBalAmt()
														.multiply(
																new BigDecimal(
																		Util.nullToSpace(currMap
																				.get("ENDRATE")))));
									} catch (Exception e) {
										map = this.setNoRateCurrMap(map,
												l170m01b.getBalCurr());
									}
								}
							}
						}
					}
				}
				contrnoList.add(l170m01b.getCntrNo());

			}
			if (l170m01a != null) {
				l170m01a.setTotBal(totBal);
				l170m01a.setTotQuota(totQuota);
				if ("1".equals(lnDataDateResult)) {
					l170m01a.setLnDataDate(LMSUtil.getExMonthLastDay(-1));
				} else if ("2".equals(lnDataDateResult)) {
					l170m01a.setLnDataDate(null);
				}
				l170m01aDao.save(l170m01a);
			}
			map.put("totBal", String.valueOf(totBal.setScale(2,
					BigDecimal.ROUND_HALF_UP)));
			map.put("totQuota", String.valueOf(totQuota.setScale(2,
					BigDecimal.ROUND_HALF_UP)));
		} catch (Exception e) {
			map.put("ERRORCODE", "EFD0025");
			map.put("msg", "執行錯誤" + e.getMessage());
		}

		return map;
	}

	/**
	 * 將無法轉換匯率的幣別記錄到map
	 * 
	 * @param map
	 *            map
	 * @param currTotalMap
	 *            所有幣別
	 * @param l170m01b
	 *            l170m01b
	 * @return
	 */
	private Map<String, String> setNoRateCurrMap(Map<String, String> map,
			String curr) {
		if (map.get("noRateCurr") == null) {
			map.put("noRateCurr", curr);
		} else {
			if (!map.get("noRateCurr").contains(curr)) {
				map.put("noRateCurr", map.get("noRateCurr") + "、" + curr);// currTotalMap.get(curr)
			}

		}
		return map;
	}

	/**
	 * 處理若有無法轉換的幣別匯率 則回傳內容
	 * 
	 * @param map
	 * @return
	 */
	private CapAjaxFormResult handleRateMap(Map<String, String> map,
			CapAjaxFormResult result) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1705M01Page.class);
		if (map.get("noRateCurr") != null) {
			result.set(
					"WARMMSG",
					pop.getProperty("L170M01b.warmMsg01")
							+ map.get("noRateCurr"));
		}
		return result;
	}

	public String checkS04Data(List<L170M01D> l170m01dList) throws CapException {
		Properties popS04 = MessageBundleScriptCreator
				.getComponentResource(LMS1705S04Panel.class);
		List<String> lossList = new ArrayList<String>();
		List<String> errMsg = new ArrayList<String>();

		Map<String, L170M01D> map = new HashMap<String, L170M01D>();

		// J-108-0888_05097_B1001
		String mainId = "";
		for (L170M01D i : l170m01dList) {
			mainId = i.getMainId();
			map.put(i.getItemNo(), i);
		}

		L170M01A l170m01a = null;
		if (Util.notEquals(mainId, "")) {
			l170m01a = service.findModelByMainId(L170M01A.class, mainId);
			if (l170m01a == null) {
				l170m01a = new L170M01A();
			}
		} else {
			l170m01a = new L170M01A();
		}

		for (L170M01D l170m01d : l170m01dList) {
			String[] AttachedTable = new String[] { "Z", "Y", "X" };
			if (Arrays.asList(AttachedTable).contains(l170m01d.getItemType())) {
				continue; // 附表不檢查缺項
			}

			// J-108-0888_05097_B1001
			int itemSeq = 0;
			String itemSeqStr ="";
			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_201907)) {
				if (Util.isNotEmpty(Util.trim(l170m01d.getItemSeqShow()))) {
					itemSeq = Util.parseInt(l170m01d.getItemSeqShow());
				} else {
					itemSeq = l170m01d.getItemSeq();
				}
			} else {
				itemSeq = l170m01d.getItemSeq();
				if (itemSeq >= 12) {
					itemSeq = itemSeq - 1;
				}
			}
			itemSeqStr = Integer.toString(itemSeq);

			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202406)) {
				if (Util.equals("Y", l170m01a.getRealRpFg())) {
					if (Util.equals("A005", l170m01d.getItemNo())) {
						continue;
					}
				} else {
					if (Util.equals("A005", l170m01d.getItemNo())) {
						itemSeqStr = l170m01d.getItemSeqShow();
					}
				}
			}

			if (Util.isEmpty(Util.trim(l170m01d.getChkResult()))) { // 檢查缺項
				lossList.add(itemSeqStr);
			}

			// 土地融資與建築融資案件實地覆審結果，其不動產開發與興建計畫、工程進度及建案銷售等情形是否與核定條件相符。（須另勾選附表）
			if ("B008".equals(l170m01d.getItemNo())) {
				boolean loss = false;
				if (Util.isEmpty(Util.trim(l170m01d.getChkResult()))) {
					loss = true;
				}
				String[] arrayZ = new String[] { "Z100", "Z200", "Z300", "Z400" };
				for (String itemNo : arrayZ) {
					L170M01D itemData = map.get(itemNo);
					if (itemData != null
							&& Util.isEmpty(Util.trim(itemData.getChkResult()))) {
						loss = true;
					}
				}
				if (loss) {
					errMsg.add(MessageFormat.format(
							popS04.getProperty("L170S04.ChkMsg04"), itemSeq,
							getClass())); // 覆審項目第{0}項之附表欄位不得空白
				}
			}
			// 工程預付款及/或履約保證其工程進度及履約情形是否正常？
			if ("B009".equals(l170m01d.getItemNo()) // 「是」或「否」均須註明
					&& Util.notEquals("K", l170m01d.getChkResult())
					&& Util.isEmpty(Util.trim(l170m01d.getChkText()))) {
				errMsg.add(MessageFormat.format(
						popS04.getProperty("L170S04.ChkMsg03"), itemSeq,
						getClass())); // 覆審項目第{0}項之覆審內容說明不得空白
			}

			// 中長期放款之企業授信戶，其產銷情形及獲利能力是否良好?
			// 中長期財務預估與現行財務情形是否無差異過大且轉差(如原預估為獲利而實際為虧損)？(若有應作說明)
			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202404)
					&& "B010".equals(l170m01d.getItemNo())
					&& Util.equals("N", l170m01d.getChkResult())
					&& Util.isEmpty(Util.trim(l170m01d.getChkText()))) {
				// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
				errMsg.add(MessageFormat.format(
						popS04.getProperty("L170S04.ChkMsg03"), itemSeq,
						getClass())); // 覆審項目第{0}項之覆審內容說明不得空白
			}

			if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
					LrsUtil.V_O_202406)
					&& "A005".equals(l170m01d.getItemNo())
					&& Util.equals("Y", l170m01d.getChkResult())
					&& Util.isEmpty(Util.trim(l170m01d.getChkText()))) {
				// J-113-0204 新增及修正說明文句
				errMsg.add(MessageFormat.format(
						popS04.getProperty("L170S04.ChkMsg03"), itemSeqStr,
						getClass())); // 覆審項目第{0}項之覆審內容說明不得空白
			}

			// 授信電腦建檔資料覆核是否確實？（須另勾選附表）
			if ("B011".equals(l170m01d.getItemNo())) {
				boolean loss = false;
				String[] arrayY = new String[] { "Y111", "Y112", "Y121",
						"Y122", "Y123", "Y124", "Y131", "Y132", "Y133", "Y134",
						"Y135", "Y210" };
				String[] arrayY124 = new String[] { "Y12A", "Y12B", "Y12C",
						"Y12D", "Y12D", "Y12F" };
				String[] arrayY210 = new String[] { "Y211", "Y212", "Y213" };
				String[] arrayY22A = new String[] { "Y221" };
				
				List<String> arrayHide = new ArrayList<String>();
			
				// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
				List<String> arrayY230 = new ArrayList<String>();
				arrayY230.add("Y230");
				arrayY230.add("Y23A");
				arrayY230.add("Y23B");
				arrayY230.add("Y23C");
				arrayY230.add("Y23D");
				arrayY230.add("Y234");
				arrayY230.add("Y235");
				arrayY230.add("Y236");
				arrayY230.add("Y238");
				arrayY230.add("Y239");

				List<String> arrayY236 = new ArrayList<String>();
				arrayY236.add("Y23I");
				arrayY236.add("Y23J");
				arrayY236.add("Y23K");
				List<String> arrayY238 = new ArrayList<String>();
				arrayY238.add("Y23L");
				arrayY238.add("Y23M");
				List<String> arrayY239 = new ArrayList<String>();
				arrayY239.add("Y23N");
				arrayY239.add("Y23O");
				List<String> arrayY234 = new ArrayList<String>();
				
				List<String> arrayY310 = new ArrayList<String>();
				
				// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
				if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
						LrsUtil.V_O_202210)) {
					// Y210改Y21A、新增Y22A
					arrayY = new String[] { "Y111", "Y112", "Y121", "Y122",
							"Y123", "Y124", "Y131", "Y132", "Y133", "Y134",
							"Y135", "Y21A", "Y22A" };
				}
				// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
				// 包含上述J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。，以及這次修改的內容
				if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
						LrsUtil.V_O_202307)) {
					arrayY = new String[] { "Y111", "Y112", "Y121", "Y122",
							"Y123", "Y124", "Y131", "Y132", "Y133", "Y134",
							"Y135", "Y21A", "Y22A", "Y230", };

				}

				if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
						LrsUtil.V_O_202406)) {
					// J-113-0204 新增及修正說明文句
					arrayY = new String[] { "Y111", "Y112", "Y113", "Y121",
							"Y122", "Y123", "Y124", "Y131", "Y132", "Y133",
							"Y134", "Y135", "Y21A", "Y22A", "Y230", "Y240",
							"Y250", "Y310", "Y320", "Y33A", "Y33B", "Y33C",
							"Y33D" };

					arrayY210 = new String[] { "Y211", "Y212", "Y213", "Y214",
							"Y215" };

					arrayY310.add("Y31A");
					
					if (Util.equals("Y", l170m01a.getRealRpFg())) {
						arrayHide.add("Y113");
						arrayHide.add("Y240");
						arrayHide.add("Y250");
						arrayHide.add("Y214");
						arrayHide.add("Y215");
						arrayHide.add("Y300");
						arrayHide.add("Y310");
						arrayHide.add("Y31A");
						arrayHide.add("Y320");
						arrayHide.add("Y330");
						arrayHide.add("Y33A");
						arrayHide.add("Y33B");
						arrayHide.add("Y33C");
						arrayHide.add("Y33D");
					}

				}

				for (String itemNo : arrayY) {
					
					if (arrayHide.contains(itemNo)) {
						continue;
					}
					
					L170M01D itemData = map.get(itemNo);
					if (itemData != null
							&& Util.isEmpty(Util.trim(itemData.getChkResult()))) {
						loss = true;
					}
					if ("Y124".equals(itemNo)
							&& Util.equals("Y", itemData.getChkResult())) {
						for (String subItemNo : arrayY124) {
							L170M01D subItemData = map.get(subItemNo);
							if (subItemData != null
									&& Util.isEmpty(Util.trim(subItemData
											.getChkResult()))) {
								loss = true;
							}
						}
					}
					if (("Y210".equals(itemNo) || "Y21A".equals(itemNo))
							&& Util.equals("Y", itemData.getChkResult())) {
						for (String subItemNo : arrayY210) {
							L170M01D subItemData = map.get(subItemNo);
							if (subItemData != null
									&& Util.isEmpty(Util.trim(subItemData
											.getChkResult()))) {
								loss = true;
							}
						}
					}
					if ("Y22A".equals(itemNo)
							&& Util.equals("Y", itemData.getChkResult())) {
						for (String subItemNo : arrayY22A) {
							L170M01D subItemData = map.get(subItemNo);
							if (subItemData != null
									&& Util.isEmpty(Util.trim(subItemData
											.getChkResult()))) {
								loss = true;
							}
						}
					}
					
					if ("Y310".equals(itemNo)
							&& Util.equals("N", itemData.getChkResult())) {
						for (String subItemNo : arrayY310) {
							L170M01D subItemData = map.get(subItemNo);
							if (subItemData != null
									&& Util.isEmpty(Util.trim(subItemData
											.getChkResult()))) {
								loss = true;
							}
						}
					}

				}

				// J-112-0280 新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
				for (String itemNo : arrayY230) {

					if (!map.containsKey(itemNo)) {
						continue;
					}

					L170M01D itemData = map.get(itemNo);
					if (itemData != null
							&& Util.isEmpty(Util.trim(itemData.getChkResult()))) {
						loss = true;
					}

					if ("Y230".equals(itemNo)
							&& Util.equals("K", itemData.getChkResult())) {
						break;
					}

					if ("Y234".equals(itemNo)) {
						if (Util.equals("Y", itemData.getChkResult())) {
							arrayY234.add("Y23E");
						} else if (Util.equals("N", itemData.getChkResult())) {
							arrayY234.add("Y23F");
							arrayY234.add("Y23G");
							arrayY234.add("Y23H");
						}

						for (String subItemNo : arrayY234) {
							L170M01D subItemData = map.get(subItemNo);
							if (subItemData != null
									&& Util.isEmpty(Util.trim(subItemData
											.getChkResult()))) {
								loss = true;
							}
						}

					}

					if ("Y236".equals(itemNo)
							&& Util.equals("Y", itemData.getChkResult())) {
						for (String subItemNo : arrayY236) {
							if (!map.containsKey(itemNo)) {
								continue;
							}
							L170M01D subItemData = map.get(subItemNo);
							if (subItemData != null
									&& Util.isEmpty(Util.trim(subItemData
											.getChkResult()))) {
								loss = true;
							}
						}
					}

					if ("Y238".equals(itemNo)
							&& Util.equals("Y", itemData.getChkResult())) {
						for (String subItemNo : arrayY238) {
							if (!map.containsKey(itemNo)) {
								continue;
							}
							L170M01D subItemData = map.get(subItemNo);
							if (subItemData != null
									&& Util.isEmpty(Util.trim(subItemData
											.getChkResult()))) {
								loss = true;
							}
						}
					}

					if ("Y239".equals(itemNo)
							&& Util.equals("Y", itemData.getChkResult())) {
						for (String subItemNo : arrayY239) {
							if (!map.containsKey(itemNo)) {
								continue;
							}
							L170M01D subItemData = map.get(subItemNo);
							if (subItemData != null
									&& Util.isEmpty(Util.trim(subItemData
											.getChkResult()))) {
								loss = true;
							}
						}
					}

				}

				if (loss) {
					errMsg.add(MessageFormat.format(
							popS04.getProperty("L170S04.ChkMsg04"), itemSeq,
							getClass())); // 覆審項目第{0}項之附表欄位不得空白
				}
			}
			// 借戶是否依照約定條件履行？（核定條件若有「應檢視事項」或「承諾事項」須另勾選附表）
			if ("B013".equals(l170m01d.getItemNo())) {
				boolean loss = false;

				ArrayList<String> arrayX = new ArrayList<String>(Arrays.asList(
						"X110", "X210"));
				ArrayList<String> arrayX11 = new ArrayList<String>(
						Arrays.asList("X111", "X112"));
				ArrayList<String> arrayX21 = new ArrayList<String>(
						Arrays.asList("X211", "X212"));
				for (String itemNo : arrayX) {
					L170M01D itemData = map.get(itemNo);
					if (itemData != null
							&& Util.isEmpty(Util.trim(itemData.getChkResult()))) {
						loss = true;
					}
					if ("X110".equals(itemNo) && itemData != null
							&& Util.equals("Y", itemData.getChkResult())) {
						L170M01D X111Data = map.get("X111");
						L170M01D X112Data = map.get("X112");
						if ((X111Data != null && Util.equals("N",
								X111Data.getChkResult()))
								|| (X112Data != null && Util.equals("N",
										X112Data.getChkResult()))) {
							arrayX11.add("X113");
						}
						for (String subItemNo : arrayX11) {
							L170M01D subItemData = map.get(subItemNo);
							if (subItemData != null
									&& Util.isEmpty(Util.trim(subItemData
											.getChkResult()))) {
								loss = true;
							}
						}
					} else if ("X210".equals(itemNo) && itemData != null
							&& Util.equals("Y", itemData.getChkResult())) {
						L170M01D X211Data = map.get("X211");
						L170M01D X212Data = map.get("X212");
						if ((X211Data != null && Util.equals("N",
								X211Data.getChkResult()))
								|| (X212Data != null && Util.equals("N",
										X212Data.getChkResult()))) {
							arrayX21.add("X213");
						}
						for (String subItemNo : arrayX21) {
							L170M01D subItemData = map.get(subItemNo);
							if (subItemData != null
									&& Util.isEmpty(Util.trim(subItemData
											.getChkResult()))) {
								loss = true;
							}
						}
					}

				}
				if (loss) {
					errMsg.add(MessageFormat.format(
							popS04.getProperty("L170S04.ChkMsg04"), itemSeq,
							getClass())); // 覆審項目第{0}項之附表欄位不得空白
				}
			}
			// 前次覆審有無應行改善事項？
			if ("B016".equals(l170m01d.getItemNo())
					&& "Y".equals(l170m01d.getChkResult())
					&& Util.isEmpty(l170m01d.getChkPreReview())) {
				errMsg.add(MessageFormat.format(
						popS04.getProperty("L170S04.ChkMsg01"), itemSeq,
						getClass()));
			}

			// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
			// 參與同業主辦之聯合授信案件，於本行參貸後，是否無主辦行已於次級市場將其參貸該聯貸案之部分或全部債權出售之情形？(若有應作說明)。
			if ("C003".equals(l170m01d.getItemNo())
					&& Util.equals("N", l170m01d.getChkResult())
					&& Util.isEmpty(Util.trim(l170m01d.getChkText()))) {
				errMsg.add(MessageFormat.format(
						popS04.getProperty("L170S04.ChkMsg03"), itemSeq,
						getClass())); // 覆審項目第{0}項之覆審內容說明不得空白
			}

		}
		if (CollectionUtils.isNotEmpty(lossList)) {
			errMsg.add(MessageFormat.format(
					popS04.getProperty("L170S04.ChkMsg02"),
					StringUtils.join(lossList, "、"), getClass()));
		}

		// J-108-0260 海外覆審檢視表
		L170M01I l170m01i = null;
		if (Util.notEquals(mainId, "")) {
			l170m01i = service.findModelByMainId(L170M01I.class, mainId);
		}

		String checkData = this.chkL170m01i(l170m01i);
		if (checkData.length() == 0) {

		} else {
			errMsg.add(checkData);
		}

		if (errMsg.size() > 0) {
			return StringUtils.join(errMsg, "<br/>");
		} else {
			return "";
		}
	}

	public void initl170m01e_oV(L170M01E l170m01e, L170M01A meta,
			String userId, String timeFlag) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
		String dateString = sdf.format(new Date());
		l170m01e.setMainId(meta.getMainId());
		l170m01e.setCustId(meta.getCustId());
		l170m01e.setDupNo(meta.getDupNo());
		// 與國內不同的預設值
		l170m01e.setCrdTYear(new Date());
		l170m01e.setCrdTBR(meta.getOwnBrId());
		l170m01e.setFinYear(dateString);
		l170m01e.setCntrNo(LrsUtil.M01E_CUST_CNTRNO); // 無額度序號時，以0補滿欄位

		l170m01e.setCreator(userId);
		l170m01e.setCreateTime(CapDate.getCurrentTimestamp());
		l170m01e.setUpdater(l170m01e.getCreator());
		l170m01e.setUpdateTime(l170m01e.getCreateTime());

		l170m01e.setTimeFlag(timeFlag);
	}

	// J-108-0260 海外覆審檢視表
	@Override
	public CapAjaxFormResult saveL170m01i(PageParameters params, L170M01A l170m01a) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID, "");
		String formL170m01i = params.getString("chklistForm");
		L170M01I l170m01i = service.findModelByMainId(L170M01I.class, mainId);
		if (l170m01i == null) {
			l170m01i = new L170M01I();
			l170m01i.setMainId(mainId);
			l170m01i.setCustId(l170m01a.getCustId());
			l170m01i.setDupNo(l170m01a.getDupNo());
		}
		DataParse.toBean(formL170m01i, l170m01i);
		// this.chkL170m01i(l170m01i);
		service.save(l170m01i);
		result.set("chklistForm", DataParse.toResult(l170m01i));
		return result;
	}

	// J-108-0260 海外覆審檢視表
	@Override
	public String chkL170m01i(L170M01I l170m01i) {
		StringBuffer msg = new StringBuffer("");
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1705M01Page.class);
		Properties prop2 = MessageBundleScriptCreator
				.getComponentResource(LMS1705S04Panel.class);
		// L170M01I.msg01=請輸入
		if (l170m01i != null) {
			if (Util.isEmpty(Util.nullToSpace(l170m01i.getHasContract()))) {
				msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
				msg.append(prop.getProperty("L170M01I.msg01")).append(
						prop2.getProperty("L170M01I.0101"));
			} else if (Util.equals("Y", Util.trim(l170m01i.getHasContract()))) {
				if (Util.isEmpty(Util.nullToSpace(l170m01i.getContractDt()))) {
					msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
					msg.append(prop.getProperty("L170M01I.msg01"))
							.append(prop2.getProperty("L170M01I.0101"))
							.append(prop2.getProperty("dt1"));
				}
			}

			if (Util.isEmpty(Util.nullToSpace(l170m01i.getHasNote()))) {
				msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
				msg.append(prop.getProperty("L170M01I.msg01")).append(
						prop2.getProperty("L170M01I.0102"));
			} else if (Util.equals("Y", Util.trim(l170m01i.getHasNote()))) {
				if (Util.isEmpty(Util.nullToSpace(l170m01i.getNoteDt()))) {
					msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
					msg.append(prop.getProperty("L170M01I.msg01"))
							.append(prop2.getProperty("L170M01I.0102"))
							.append(prop2.getProperty("dt2"));
				}
			}

			if (Util.isEmpty(Util.nullToSpace(l170m01i.getHasAttorney()))) {
				msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
				msg.append(prop.getProperty("L170M01I.msg01")).append(
						prop2.getProperty("L170M01I.0103"));
			} else if (Util.equals("Y", Util.trim(l170m01i.getHasAttorney()))) {
				if (Util.isEmpty(Util.nullToSpace(l170m01i.getAttorneyDt()))) {
					msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
					msg.append(prop.getProperty("L170M01I.msg01"))
							.append(prop2.getProperty("L170M01I.0103"))
							.append(prop2.getProperty("dt2"));
				}
			}

			if (Util.isEmpty(Util.nullToSpace(l170m01i.getHasGuar()))) {
				msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
				msg.append(prop.getProperty("L170M01I.msg01")).append(
						prop2.getProperty("L170M01I.0104"));
			} else if (Util.equals("Y", Util.trim(l170m01i.getHasGuar()))) {
				if (Util.isEmpty(Util.nullToSpace(l170m01i.getGuarSignDt()))) {
					msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
					msg.append(prop.getProperty("L170M01I.msg01"))
							.append(prop2.getProperty("L170M01I.0104"))
							.append(prop2.getProperty("dt3"));
				}
				if (Util.isEmpty(Util.nullToSpace(l170m01i.getGuarVerDt()))) {
					msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
					msg.append(prop.getProperty("L170M01I.msg01"))
							.append(prop2.getProperty("L170M01I.0104"))
							.append(prop2.getProperty("dt4"));
				}
			}

			if (Util.notEquals("Y", Util.trim(l170m01i.getReceNA()))) {
				if (Util.isEmpty(Util.nullToSpace(l170m01i.getReceAmt()))) {
					msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
					msg.append(prop.getProperty("L170M01I.msg01")).append(
							prop2.getProperty("L170M01I.0201"));
				}
			}
			if (Util.notEquals("Y", Util.trim(l170m01i.getValRepNA()))) {
				if (Util.isEmpty(Util.nullToSpace(l170m01i.getValRepDt()))) {
					msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
					msg.append(prop.getProperty("L170M01I.msg01")).append(
							prop2.getProperty("L170M01I.0301"));
				}
				if (Util.isEmpty(Util.nullToSpace(l170m01i.getValRepAmt()))) {
					msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
					msg.append(prop.getProperty("L170M01I.msg01")).append(
							prop2.getProperty("L170M01I.0302"));
				}
			}
			if (Util.notEquals("Y", Util.trim(l170m01i.getMtgNA()))) {
				if (Util.isEmpty(Util.nullToSpace(l170m01i.getMtgOrder()))) {
					msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
					msg.append(prop.getProperty("L170M01I.msg01")).append(
							prop2.getProperty("L170M01I.0401"));
				}
				if (Util.isEmpty(Util.nullToSpace(l170m01i.getMtgAmt()))) {
					msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
					msg.append(prop.getProperty("L170M01I.msg01")).append(
							prop2.getProperty("L170M01I.0402"));
				}
			}
			if (Util.notEquals("Y", Util.trim(l170m01i.getInsNA()))) {
				if (Util.isEmpty(Util.nullToSpace(l170m01i.getInsDt()))) {
					msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
					msg.append(prop.getProperty("L170M01I.msg01")).append(
							prop2.getProperty("L170M01I.0501"));
				}
				if (Util.isEmpty(Util.nullToSpace(l170m01i.getInsAmt()))) {
					msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
					msg.append(prop.getProperty("L170M01I.msg01")).append(
							prop2.getProperty("L170M01I.0502"));
				}
				if (Util.isEmpty(Util.nullToSpace(l170m01i.getInsNum()))) {
					msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
					msg.append(prop.getProperty("L170M01I.msg01")).append(
							prop2.getProperty("L170M01I.0503"));
				}
			}

			if (Util.isNotEmpty(msg.toString())) {
				msg.insert(0, prop.getProperty("L170M01I.title") + "：<br/>");
			}
		} else {
			// L170M01I.title=債權確保檢視表
			msg.append(Util.equals(msg.toString(), "") ? "" : "<br/>");
			msg.append(prop.getProperty("L170M01I.msg01")).append(
					prop.getProperty("L170M01I.title"));
		}
		return msg.toString();
	}
}
