---------------------------------------------------------
-- LMS.L180R02A 營運中心授權內外已核准已婉卻授信案件
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L180R02A;
CREATE TABLE LMS.L180R02A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	APPROVER      VARCHAR(18)  ,
	<PERSON><PERSON>O          CHAR(3)      ,
	AREABRANCHID  CHAR(3)      ,
	CASEDATE      DATE         ,
	CASENO        VARCHAR(62)  ,
	CUSTID        VARCHAR(10)  ,
	DUPNO         CHAR(1)      ,
	CUSTNAME      VARCHAR(120) ,
	CNTRNO        CHAR(12)     ,
	DOCTYPE       CHAR(1)      ,
	DOCKIND       CHAR(1)      ,
	LNSUBJECT     VARCHAR(300) ,
	NOUSEAMT      CHAR(1)      ,
	ASCURR        CHAR(3)      ,
	ASAMT         DECIMAL(15,0),
	LV2CURR       CHAR(3)      ,
	LV2AMT        DECIMAL(15,0),
	DESP1         VARCHAR(900) ,
	PROPERTY      VARCHAR(30)  ,
	HQCHECKDATE   DATE         ,
	HQCHECKMEMO   VARCHAR(300) ,
	AUDIT         CHAR(1)      ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,
	constraint P_L180R02A PRIMARY KEY(OID)
) IN EL_DATA_4KTS INDEX IN EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL180R02A01;
CREATE INDEX LMS.XL180R02A01  ON LMS.L180R02A   (MAINID, APPROVER, BRNO);
--DROP INDEX LMS.XL180R02A02;
CREATE INDEX LMS.XL180R02A02  ON LMS.L180R02A   (MAINID, AREABRANCHID, AUDIT);
--DROP INDEX LMS.XL180R02A03;
CREATE INDEX LMS.XL180R02A03  ON LMS.L180R02A   (OID, MAINID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L180R02A IS '營運中心授權內外已核准已婉卻授信案件';
COMMENT ON LMS.L180R02A (
	OID           IS 'oid', 
	MAINID        IS 'mainId', 
	APPROVER      IS '經辦', 
	BRNO          IS '分行別', 
	AREABRANCHID  IS '營運中心代碼', 
	CASEDATE      IS '核定日', 
	CASENO        IS '案件號碼', 
	CUSTID        IS '統一編號', 
	DUPNO         IS '重覆序號', 
	CUSTNAME      IS '客戶名稱', 
	CNTRNO        IS '額度序號', 
	DOCTYPE       IS '企/個金案件', 
	DOCKIND       IS '授權別', 
	LNSUBJECT     IS '授信科目', 
	NOUSEAMT      IS '流用不合計', 
	ASCURR        IS '增減額度幣別', 
	ASAMT         IS '增減額度金額(元)', 
	LV2CURR       IS '前准批覆授信額度－幣別', 
	LV2AMT        IS '前准批覆授信額度－金額', 
	DESP1         IS '動用期限', 
	PROPERTY      IS '性質', 
	HQCHECKDATE   IS '備查日期', 
	HQCHECKMEMO   IS '備註', 
	AUDIT         IS '審核不通過', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
