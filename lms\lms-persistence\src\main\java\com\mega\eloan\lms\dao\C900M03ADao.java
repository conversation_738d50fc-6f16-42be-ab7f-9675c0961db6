package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M03A;

/** FTP_H檔 **/
public interface C900M03ADao extends IGenericDao<C900M03A> {

	public C900M03A findByOid(String oid);
	
	public C900M03A findByFnGenDateGenTime(String fn, String genDate, String genTime);
	
	public C900M03A findByFnMaxGenDate(String fn);
	
	public List<C900M03A> findByFnBfGenDate(String fn, String genDate);
}