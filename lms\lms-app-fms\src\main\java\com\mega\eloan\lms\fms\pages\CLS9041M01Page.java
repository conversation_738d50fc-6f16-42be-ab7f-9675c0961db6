/* 
 *CLS9041M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;

/**
 * <pre>
 * 政策性留學生貸款送保彙報(S1~S3)
 * </pre>
 * 
 * @since 2012/11/01
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/05,Vector Lo,new
 *          </ul>
 */
@Controller
@RequestMapping(path = "/fms/cls9041m01/{page}")
public class CLS9041M01Page extends AbstractEloanForm {

	public CLS9041M01Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		
		renderJsI18N(CLS9041M01Page.class);

	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		// TODO Auto-generated method stub
		return null;
	}
}
