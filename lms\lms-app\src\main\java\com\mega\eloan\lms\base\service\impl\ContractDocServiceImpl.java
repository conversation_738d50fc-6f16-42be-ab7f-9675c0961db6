package com.mega.eloan.lms.base.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.CharUtils;
import org.apache.commons.lang.StringUtils;
import org.aspectj.util.FileUtil;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.dao.BranchDao;
import com.mega.eloan.common.gwclient.PLOAN001;
import com.mega.eloan.common.gwclient.PLOAN004;
import com.mega.eloan.common.gwclient.PLOAN004.PLOAN004_guaranteeInfo;
import com.mega.eloan.common.gwclient.PLOAN004.PLOAN004_loanAcct;
import com.mega.eloan.common.gwclient.PLOAN004.PLOAN004_loanConditionInfo;
import com.mega.eloan.common.gwclient.PLOAN004.PLOAN004_loanConditionInfo_loanPurposeObj;
import com.mega.eloan.common.gwclient.PLOAN004.PLOAN004_rate;
import com.mega.eloan.common.gwclient.PLOAN004.PLOAN004_repayment;
import com.mega.eloan.common.gwclient.PLOAN006;
import com.mega.eloan.common.gwclient.PLOAN014;
import com.mega.eloan.common.gwclient.PLOAN014.PLOAN014_loanAcct;
import com.mega.eloan.common.gwclient.PLOAN016;
import com.mega.eloan.common.gwclient.PLOAN017;
import com.mega.eloan.common.gwclient.PLOAN019;
import com.mega.eloan.common.gwclient.PLOAN019.PLOAN019_cbAfft1Content;
import com.mega.eloan.common.gwclient.PLOAN019.PLOAN019_cbAfft2Content;
import com.mega.eloan.common.gwclient.PLOAN019.PLOAN019_cbAfft3Content;
import com.mega.eloan.common.gwclient.PLOAN019.PLOAN019_cbAfft4Content;
import com.mega.eloan.common.gwclient.PLOAN019.PLOAN019_cbAfft5Content;
import com.mega.eloan.common.gwclient.PLOAN019.PLOAN019_expireInfo;
import com.mega.eloan.common.gwclient.PLOAN019.PLOAN019_guaranteeInfo;
import com.mega.eloan.common.gwclient.PLOAN019.PLOAN019_interestInfo;
import com.mega.eloan.common.gwclient.PLOAN019.PLOAN019_loanAcct;
import com.mega.eloan.common.gwclient.PLOAN019.PLOAN019_loanConditionInfo;
import com.mega.eloan.common.gwclient.PLOAN019.PLOAN019_loanConditionInfo_loanPurposeObj;
import com.mega.eloan.common.gwclient.PLOAN019.PLOAN019_payeeInfo;
import com.mega.eloan.common.gwclient.PLOAN019.PLOAN019_rate;
import com.mega.eloan.common.gwclient.PLOAN019.PLOAN019_repayment;
import com.mega.eloan.common.gwclient.PLOAN019.PLOAN019_repaymentInfo;
import com.mega.eloan.common.gwclient.PLOANGwClient;
import com.mega.eloan.common.model.Branch;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.ContractDocUtil;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ContractDocService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.dao.L140M01RDao;
import com.mega.eloan.lms.dao.L140S02EDao;
import com.mega.eloan.lms.mfaloan.service.MisMislnratService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01B;
import com.mega.eloan.lms.model.C122M01E;
import com.mega.eloan.lms.model.C160S01D;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.C340M01B;
import com.mega.eloan.lms.model.C340M01C;
import com.mega.eloan.lms.model.C900M01A;
import com.mega.eloan.lms.model.C900M01D;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01R;
import com.mega.eloan.lms.model.L140M01Y;
import com.mega.eloan.lms.model.L140M03A;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02C;
import com.mega.eloan.lms.model.L140S02D;
import com.mega.eloan.lms.model.L140S02E;
import com.mega.eloan.lms.model.L140S02F;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.util.CapCommonUtil;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

@Service("ContractDocService")
public class ContractDocServiceImpl 
implements FileDownloadService, ContractDocService {
	private static final String CB_ON_STR = "■";
	private static final String CB_Y_STR = "Y";
	private static final String CB_N_STR = "N";
	private static final String VAL_OTHER = "Other";
	private static final String VAL_MONTH_INTEREST = "MonthInterest";
	private static final String VAL_OVERDRAW = "OverDraw";
	
	protected static final Logger LOGGER = LoggerFactory.getLogger(ContractDocServiceImpl.class);
	private static final int MAXLEN_C340M01A_CONTRNUMBER = StrUtils.getEntityFileldLegth(C340M01A.class, "contrNumber", 60);
	
	private static final String CTRTYPE_A_RATESTR_X_TO_Y = "第{0}期至第{1}期";
	private static final String CTRTYPE_A_RATESTR_RATEFLAG1 = "固定年利率{0}%。";
	private static final String CTRTYPE_A_RATESTR_RATEFLAG3 = "依撥款日乙方公告之{0}{1}%加計年利率{2}%浮動計息，目前合計為年利率{3}%";
	private static final String CTRTYPE_A_RATESTR_RATEFLAG3_202209 = "按乙方公告之消費金融放款指標利率加碼年息{0}%浮動計息";

	private static final String CTRTYPE_B_RATESTR_X_TO_Y = "第{0}期至第{1}期";
	private static final String CTRTYPE_B_RATESTR_RATEFLAG1 = "固定年利率{0}%。";
	private static final String CTRTYPE_B_RATESTR_RATEFLAG3 = "按乙方公告之消費金融放款指標利率加碼年息{0}%浮動計息";
	private static final String CTRTYPE_B_RATESTR_noPPP_1 = "按乙方公告之消費金融放款指標利率加碼年息百分之{0}計算，按日計息；嗣後隨乙方消費金融放款指標利率調整而調整，並自調整之日起按調整後之年利率計算。憑支票、金融卡、網路銀行或存摺與取款憑條動用，以當日透支、支用最高額計息；憑借款支用書動用，以當日最終借款餘額計息（適用於短期/中期循環借款）。";
	private static final String CTRTYPE_B_RATESTR_noPPP_2 = "按乙方撥款當日公告之消費金融放款指標利率加碼年息百分之{0}計算；嗣後隨乙方消費金融放款指標利率調整而調整，並於每屆滿一個月之日按當日調整後之年利率計算（適用於中、長期不循環借款採浮動利率者）。";
	private static final String CTRTYPE_B_RATESTR_noPPP_3 = "按乙方撥款當日公告之消費金融放款指標利率加碼年息百分之{0}計算；嗣後隨乙方消費金融放款指標利率調整而調整，並於每屆滿一個月之日按當日調整後之年利率計算（適用於中、長期不循環借款採浮動利率者）。";
	
	@Resource
	BranchService branchService;
	
	@Resource
	CLSService clsService;	
	
	@Resource
	ICustomerService iCustomerService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	MisMislnratService misMislnratService;
	
	@Resource
	private PLOANGwClient pLoanGwClient;
	
	@Resource
	SysParameterService sysParameterService;

	@Autowired
	BranchDao branchDao;

	@Resource
	L140M01RDao l140m01rDao;

	@Resource
	L140S02EDao l140s02eDao;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		//下載契約書
		if(Util.equals("Y", params.getString("dlEmptyWord"))){
			String dlContract = params.getString("dlContract");
			String fileSrcName = "CLS_ctrType1_V202008.docx";
			if(dlContract.startsWith(ContractDocConstants.C340M01A_CtrType.Type_1)){ //購屋契約
				if(Util.equals("1_V202008", dlContract)){
					fileSrcName = "CLS_ctrType1_V202008.docx";
				}else if(Util.equals("1_V202003", dlContract)){
					fileSrcName = "CLS_ctrType1_V202003.docx";
				}else if(Util.equals("1_V202206", dlContract)){
					fileSrcName = "CLS_ctrType1_V202206.docx";
				}
				else if(Util.equals("1_V202212", dlContract)){
					fileSrcName = "CLS_ctrType1_V202212.docx";
				}
				else if(Util.equals("1_V202304", dlContract)){
					fileSrcName = "CLS_ctrType1_V202304.docx";
				}
				else if(Util.equals("1_V202401", dlContract)){
					fileSrcName = "CLS_ctrType1_V202401.docx";
				}
			}else if(dlContract.startsWith(ContractDocConstants.C340M01A_CtrType.Type_2)){ //信用貸款(DBR22)
				if(Util.equals("2_V202010", dlContract)){
					fileSrcName = "CLS_ctrType2_V202010.docx";
				}else if(Util.equals("2_V202009", dlContract)){
					fileSrcName = "CLS_ctrType2_V202009.docx";
				}else if(Util.equals("2_V202206", dlContract)){
					fileSrcName = "CLS_ctrType2_V202206.docx";
				}
				else if(Util.equals("2_V202212", dlContract)){
					fileSrcName = "CLS_ctrType2_V202212.docx";
				}
				else if(Util.equals("2_V202304", dlContract)){
					fileSrcName = "CLS_ctrType2_V202304.docx";
				}
				else if(Util.equals("2_V202401", dlContract)){
					fileSrcName = "CLS_ctrType2_V202401.docx";
				}
				else if(Util.equals("2_V202409", dlContract)){
					fileSrcName = "CLS_ctrType2_V202409.docx";
				}
			}else if(dlContract.startsWith(ContractDocConstants.C340M01A_CtrType.Type_3)){ //其他類契約
				if(Util.equals("3_V202008", dlContract)){
					fileSrcName = "CLS_ctrType3_V202008.docx";
				}else if(Util.equals("3_V202206", dlContract)){
					fileSrcName = "CLS_ctrType3_V202206.docx";
				}else if(Util.equals("3_V202210", dlContract)){
					fileSrcName = "CLS_ctrType3_V202210.docx";
				}
				else if(Util.equals("3_V202212", dlContract)){
					fileSrcName = "CLS_ctrType3_V202212.docx";
				}
				else if(Util.equals("3_V202304", dlContract)){
					fileSrcName = "CLS_ctrType3_V202304.docx";
				}
				else if(Util.equals("3_V202401", dlContract)){
					fileSrcName = "CLS_ctrType3_V202401.docx";
				}
			}else if(dlContract.startsWith(ContractDocConstants.C340M01A_CtrType.Type_4)){ //歡喜樂活貸款契約書(以房養老)
			}
			 
			String tempPath = PropUtil.getProperty("loadFile.dir")+ "word/"+fileSrcName;
			
			File file = new File(Thread.currentThread().getContextClassLoader()
					.getResource(tempPath).toURI());
			byte[] bytes = FileUtil.readAsByteArray(file);
			return bytes;
		}
		
		
		OutputStream outputStream = null;
		ByteArrayOutputStream baos = null;
		try {
			outputStream = this.creatDoc(params);
			if (outputStream != null) {
				baos = (ByteArrayOutputStream) outputStream;
			}
			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
		} finally {
			if (baos != null) {
				IOUtils.closeQuietly(baos);
			}
		}
		return null;
	}
	
	private OutputStream creatDoc(PageParameters params) 
	throws CapException{
		//===========================
		String c340m01a_oid = params.getString("oid");
		String mock_mode = params.getString("mock_mode");
		String c340m01cDiff = params.getString("c340m01cDiff");
		String printMode = params.getString("printMode");
		C340M01A c340m01a = clsService.findC340M01A_oid(c340m01a_oid);
		if(c340m01a==null){
			throw new CapException("[c340m01a_oid="+c340m01a_oid+"] not found", getClass());
		}
		String traceStr = "【"+c340m01a.getCustId()+", c340m01a.mainId="+c340m01a.getMainId()+"】";
//		_debug(traceStr, "mock_mode="+mock_mode);
		return gen_c340m01a_contract(c340m01a, mock_mode, traceStr , c340m01cDiff,printMode);
	}
	
	private OutputStream gen_c340m01a_contract(C340M01A c340m01a, String mock_mode, String traceStr ,String c340m01cDiff,String printMode)
	throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		String ctrType = Util.trim(c340m01a.getCtrType());
		String templateName = get_templateName(c340m01a, clsService.is_function_on_codetype("c340m01a_template_lock"));
		LinkedHashMap<String, String> paramMap  = get_paramMap(c340m01a, mock_mode);		
		
		try {
			Map<String, String> convert_paramMap = ContractDocUtil.convert_paramValue_for_XML_Predefined_entities(paramMap);

			if(true){// 若memo欄位有換行字元，轉成 word 的換行。
				//轉換動作，要放在 convert_paramValue_for_XML_Predefined_entities(...) 之後，免得<w:br>由「半型空白」被轉成「全型空白」
				//目前在前端的 html，只有以下的欄位是 textarea，其它欄位都是 input（不會有換行）
				Set<String> linefeedColumn = get_wordML_lineFeedColumn_in_textarea(ctrType);
				for(String p : linefeedColumn){
					String raw_str = convert_paramMap.get(p);
					String new_str = raw_str.replaceAll("\n", ContractDocConstants.WordML.換行符號);
					convert_paramMap.put(p, new_str);
				}
			}
			
			String templateStr = Util.getFileContent(Util.trim(PropUtil.getProperty("loadFile.dir")) + "word/" + templateName);
			String outputStr = join_word_template_param(traceStr, templateStr, convert_paramMap);

			//J-110-0212_10702_B1001 Web e-Loan 調整消金契約書購屋貸款契約書邏輯，新增利率、開辦費比對檢核
			if(Util.equals(printMode,"2")){
				String applyFee ="";
				String applyYear ="";
				String applyMonth ="";
				String applyDate ="";
				String applyRate ="";
				String applyNowRate ="";
				List<C340M01C> c340m01c_list = clsService.findC340M01C(c340m01a.getMainId());
				for(C340M01C c340m01c : c340m01c_list) {
					if (true) { //Util.isNotEmpty(c340m01c.getInitialJsonData())
						String jsonData = Util.trim(c340m01c.getJsonData());
						String initialJsonData = Util.trim(c340m01c.getInitialJsonData());
						JSONObject jsonObject = DataParse.toJSON(jsonData);
						JSONObject initialJsonObject = DataParse.toJSON(initialJsonData);
						applyFee = Util.isNotEmpty(jsonObject.optString("eloan_pa_repay_feeNo01")) ? NumConverter.addComma(	Util.parseBigDecimal(jsonObject.optString("eloan_pa_repay_feeNo01")), "#,###,###,###,##0") : "　";
						//固定利率
						if(Util.isNotEmpty(jsonObject.optString("eloan_pa_intr_withPPP_1x3"))){
							applyRate = jsonObject.optString("eloan_pa_intr_withPPP_1x3");
							applyNowRate = jsonObject.optString("eloan_pa_intr_withPPP_1x3");
						}
						else{
							String rateType = "IR+";
							if(Util.isNotEmpty(jsonObject.optString("eloan_pa_intr_withPPP_1y3"))){
								BigDecimal baseRate = Util.parseBigDecimal(jsonObject.optString("eloan_pa_intr_withPPP_baseRate"));
								BigDecimal rate = Util.parseBigDecimal(jsonObject.optString("eloan_pa_intr_withPPP_1y3"));
								applyRate = String.valueOf(rate);
								if(Util.isNotEmpty(jsonObject.optString("eloan_pa_intr_withPPP_1y4"))){
									applyNowRate = jsonObject.optString("eloan_pa_intr_withPPP_1y4");
								}
								else{
									applyNowRate = String.valueOf(baseRate.add(rate));
								}
							}
							else if(Util.isNotEmpty(jsonObject.optString("eloan_pa_intr_noPPP_1t1"))){
								BigDecimal baseRate = Util.parseBigDecimal(jsonObject.optString("eloan_pa_intr_noPPP_baseRate"));
								BigDecimal rate = Util.parseBigDecimal(jsonObject.optString("eloan_pa_intr_noPPP_1t1"));
								applyRate = String.valueOf(rate);
								if(Util.isNotEmpty(jsonObject.optString("eloan_pa_intr_noPPP_1t2"))){
									applyNowRate = jsonObject.optString("eloan_pa_intr_noPPP_1t2");
								}
								else {
									applyNowRate = String.valueOf(baseRate.add(rate));
								}
							}
							else if(Util.isNotEmpty(jsonObject.optString("eloan_pa_intr_noPPP_2t1"))){
								BigDecimal baseRate = Util.parseBigDecimal(jsonObject.optString("eloan_pa_intr_noPPP_baseRate"));
								BigDecimal rate = Util.parseBigDecimal(jsonObject.optString("eloan_pa_intr_noPPP_2t1"));
								applyRate = String.valueOf(rate);
								applyNowRate = String.valueOf(baseRate.add(rate));
							}
							else if(Util.isNotEmpty(jsonObject.optString("eloan_pa_intr_noPPP_3t1"))){
								BigDecimal baseRate = Util.parseBigDecimal(jsonObject.optString("eloan_pa_intr_noPPP_baseRate"));
								BigDecimal rate = Util.parseBigDecimal(jsonObject.optString("eloan_pa_intr_noPPP_3t1"));
								applyRate = String.valueOf(rate);
								applyNowRate = String.valueOf(baseRate.add(rate));
							}
							else if(Util.isNotEmpty(jsonObject.optString("eloan_pa_intr_noPPP_4t1"))){
								BigDecimal baseRate = Util.parseBigDecimal(jsonObject.optString("eloan_pa_intr_noPPP_baseRate"));
								BigDecimal nowRate = Util.parseBigDecimal(jsonObject.optString("eloan_pa_intr_noPPP_4t1"));
								applyRate = String.valueOf(nowRate.subtract(baseRate));
								applyNowRate = String.valueOf(nowRate);
							}
							applyRate = rateType + applyRate;
						}

						L120M01A l120m01a = clsService.findL120M01A_mainId(c340m01a.getCaseMainId());
						if(l120m01a!=null){
							applyYear = Integer.toString(l120m01a.getApproveTime().getYear()+1900);
							applyMonth = Integer.toString(l120m01a.getApproveTime().getMonth()+1);
							applyDate = Integer.toString(l120m01a.getApproveTime().getDate());
						}
					}
				}
				//加入page 1列印合約稿書籤1~5
				Pattern pattern_run = Pattern.compile("<w:bookmarkEnd\\b[^>]*>");
				String[] arr_bookMark_r1 = split_ByBookMark(ContractDocConstants.CtrType1.ELOAN_P1_RPRINTMODE2_R1, outputStr);
				String bookMark_r1="";
				String bookMark_new_r1="";
				if (arr_bookMark_r1.length == 3) {
					String[] arr_bookmarkTagAndContent = split_ByRunText(arr_bookMark_r1[1], pattern_run);
					bookMark_r1 = arr_bookMark_r1[1];
					if (arr_bookmarkTagAndContent.length == 3) {
						bookMark_new_r1=arr_bookmarkTagAndContent[0]
								+"<w:r w:rsidRPr=\"009463B2\"><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\" w:hint=\"eastAsia\"/><w:b/><w:bCs/><w:sz w:val=\"32\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>稿</w:t></w:r>"
								+ arr_bookmarkTagAndContent[1];
						outputStr = outputStr.replaceAll(bookMark_r1,bookMark_new_r1);
					}
				}
				String[] arr_bookMark_r2 = split_ByBookMark(ContractDocConstants.CtrType1.ELOAN_P1_RPRINTMODE2_R2, outputStr);
				String bookMark_r2="";
				String bookMark_new_r2="";
				if (arr_bookMark_r2.length == 3) {
					String[] arr_bookmarkTagAndContent = split_ByRunText(arr_bookMark_r2[1], pattern_run);
					bookMark_r2 = arr_bookMark_r2[1];
					if (arr_bookmarkTagAndContent.length == 3) {
						bookMark_new_r2=arr_bookmarkTagAndContent[0]
								+"<w:r w:rsidRPr=\"009463B2\"><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\" w:hint=\"eastAsia\"/><w:b/><w:bCs/><w:sz w:val=\"32\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>呈</w:t></w:r>"
								+ arr_bookmarkTagAndContent[1];
						outputStr = outputStr.replaceAll(bookMark_r2,bookMark_new_r2);
					}
				}
				String[] arr_bookMark_r3 = split_ByBookMark(ContractDocConstants.CtrType1.ELOAN_P1_RPRINTMODE2_R3, outputStr);
				String bookMark_r3="";
				String bookMark_new_r3="";
				if (arr_bookMark_r3.length == 3) {
					String[] arr_bookmarkTagAndContent = split_ByRunText(arr_bookMark_r3[1], pattern_run);
					bookMark_r3 = arr_bookMark_r3[1];
					if (arr_bookmarkTagAndContent.length == 3) {
						bookMark_new_r3=arr_bookmarkTagAndContent[0]
								+"<w:r w:rsidRPr=\"009463B2\"><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\" w:hint=\"eastAsia\"/><w:b/><w:bCs/><w:sz w:val=\"32\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>核</w:t></w:r>"
								+ arr_bookmarkTagAndContent[1];
						outputStr = outputStr.replaceAll(bookMark_r3,bookMark_new_r3);
					}
				}
				String[] arr_bookMark_r4 = split_ByBookMark(ContractDocConstants.CtrType1.ELOAN_P1_RPRINTMODE2_R4, outputStr);
				String bookMark_r4="";
				String bookMark_new_r4="";
				if (arr_bookMark_r4.length == 3) {
					String[] arr_bookmarkTagAndContent = split_ByRunText(arr_bookMark_r4[1], pattern_run);
					bookMark_r4 = arr_bookMark_r4[1];
					if (arr_bookmarkTagAndContent.length == 3) {
						bookMark_new_r4=arr_bookmarkTagAndContent[0]
								+"<w:r w:rsidRPr=\"009463B2\"><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\" w:hint=\"eastAsia\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>本案於</w:t></w:r><w:permStart w:id=\"1282034614\" w:edGrp=\"everyone\"/><w:r><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>"+applyYear+"</w:t></w:r><w:permEnd w:id=\"1282034614\"/><w:r w:rsidRPr=\"009463B2\"><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\" w:hint=\"eastAsia\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>年</w:t></w:r><w:permStart w:id=\"596070629\" w:edGrp=\"everyone\"/><w:r><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\" w:hint=\"eastAsia\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>"+applyMonth+"</w:t></w:r><w:permEnd w:id=\"596070629\"/><w:r w:rsidRPr=\"009463B2\"><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\" w:hint=\"eastAsia\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>月</w:t></w:r><w:permStart w:id=\"1494040021\" w:edGrp=\"everyone\"/><w:r><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\" w:hint=\"eastAsia\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>"+applyDate+"</w:t></w:r><w:permEnd w:id=\"1494040021\"/><w:r w:rsidRPr=\"009463B2\"><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\" w:hint=\"eastAsia\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>日已獲核定，依核定條件，擬以</w:t></w:r><w:permStart w:id=\"175967692\" w:edGrp=\"everyone\"/><w:r><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\" w:hint=\"eastAsia\"/></w:rPr><w:t>"+ applyRate +"%</w:t></w:r><w:permEnd w:id=\"175967692\"/><w:r w:rsidRPr=\"009463B2\"><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>(</w:t></w:r><w:r w:rsidRPr=\"009463B2\"><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\" w:hint=\"eastAsia\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>目前為</w:t></w:r><w:permStart w:id=\"361038260\" w:edGrp=\"everyone\"/><w:r><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\" w:hint=\"eastAsia\"/></w:rPr><w:t>"+ applyNowRate +"%</w:t></w:r><w:permEnd w:id=\"361038260\"/><w:r w:rsidRPr=\"009463B2\"><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>)</w:t></w:r><w:r w:rsidRPr=\"009463B2\"><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\" w:hint=\"eastAsia\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>為最終敘做利率，最終開辦手續費洽收</w:t></w:r><w:bookmarkStart w:id=\"6\" w:name=\"_GoBack\"/><w:permStart w:id=\"1942501909\" w:edGrp=\"everyone\"/><w:r><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t xml:space=\"preserve\">"+ applyFee +"</w:t></w:r><w:bookmarkEnd w:id=\"6\"/><w:permEnd w:id=\"1942501909\"/><w:r w:rsidRPr=\"009463B2\"><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\" w:hint=\"eastAsia\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>元。</w:t></w:r>"
								+ arr_bookmarkTagAndContent[1];
						outputStr = outputStr.replaceAll(bookMark_r4,bookMark_new_r4);
						outputStr = outputStr.replaceAll("<w:p w14:paraId=\"358E221A\" w14:textId=\"1857EFB3\" w:rsidR=\"004771A0\" w:rsidRDefault=\"004771A0\" w:rsidP=\"004771A0\"><w:pPr><w:ind w:leftChars=\"1200\" w:left=\"2880\"/><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr></w:pPr></w:p>","");
					}

				}
				String[] arr_bookMark_r5 = split_ByBookMark(ContractDocConstants.CtrType1.ELOAN_P1_RPRINTMODE2_R5, outputStr);
				String bookMark_r5="";
				String bookMark_new_r5="";
				if (arr_bookMark_r5.length == 3) {
					String[] arr_bookmarkTagAndContent = split_ByRunText(arr_bookMark_r5[1], pattern_run);
					bookMark_r5 = arr_bookMark_r5[1];
					if (arr_bookmarkTagAndContent.length == 3) {
						bookMark_new_r5=arr_bookmarkTagAndContent[0]
								+"<w:r w:rsidRPr=\"009463B2\"><w:rPr><w:rFonts w:ascii=\"標楷體\" w:eastAsia=\"標楷體\" w:hAnsi=\"標楷體\" w:hint=\"eastAsia\"/><w:lang w:eastAsia=\"zh-HK\"/></w:rPr><w:t>職　　謹呈</w:t></w:r>"
								+arr_bookmarkTagAndContent[1];
						outputStr = outputStr.replaceAll(bookMark_r5,bookMark_new_r5);
					}
				}
			}

			if(true){ //proc 浮水印
				String el_watermark = "";
				if(c340m01a!=null){
					L120M01A l120m01a = clsService.findL120M01A_mainId(c340m01a.getCaseMainId());
					if(l120m01a!=null && !Util.equals(CreditDocStatusEnum.海外_已核准.getCode(), l120m01a.getDocStatus())){
						el_watermark = "未核准簽報書";
					}					
				}
				outputStr =	StringUtils.replace(outputStr, "｛ＥＬ＿ＷＡＴＥＲＭＡＲＫ｝", el_watermark);
			}

			if (true) {//報表亂碼
				outputStr =	StringUtils.replace(outputStr, "｛報表亂碼｝", c340m01a.getRandomCode());
			}

			baos = this.writeWordContent(outputStr);
		} catch (Exception e) {
			LOGGER.error(traceStr);
			LOGGER.error(StrUtils.getStackTrace(e));
			throw new CapMessageException(e.getMessage(), getClass());
		}
		return baos;
	}

	private LinkedHashMap<String, String> get_paramMap(C340M01A c340m01a, String mock_mode)
	throws CapException {
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_1, c340m01a.getCtrType())){
			return get_paramMap_ctrType1(c340m01a, mock_mode);
		}else if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_2, c340m01a.getCtrType())){
			return get_paramMap_ctrType2(c340m01a, mock_mode);
		}else if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_3, c340m01a.getCtrType())){
			return get_paramMap_ctrType3(c340m01a, mock_mode);
		}
		return null;
	}
	private LinkedHashMap<String, String> get_paramMap_ctrType1(C340M01A c340m01a, String mock_mode)
	throws CapException {
		LinkedHashMap<String, String> result = new LinkedHashMap<String, String>();
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_1, c340m01a.getCtrType())){
			if(Util.equals("Y", mock_mode)){
				return mock_ctrType1_paramMap(c340m01a);	
			}

			if(true){
				result.put(ContractDocConstants.CtrType1.ELOAN_P1_CONTR_NO, c340m01a.getContrNumber());
				result.put(ContractDocConstants.CtrType1.ELOAN_P1_CONTR_CNAME, c340m01a.getContrPartyNm());
			}
			for(C340M01C c340m01c: clsService.findC340M01C(c340m01a.getMainId())){
				String jsonData = Util.trim(c340m01c.getJsonData());
				JSONObject jsonObject = DataParse.toJSON(jsonData);
				LMSUtil.addJsonToStringMap(result, jsonObject);
			}
			if(true){ // 把參數 eloan_p***_cb=Y(CB_Y_STR) 的資料，轉換成[V]的 icon
				Set<String> cbColumn_to_icon = new HashSet<String>();
				if(true){
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_A_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_B_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_C_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_DELIV_E_CB);
					//~~~
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_USE_A_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_USE_B_CB);
					//~~~
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_A_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_B_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_E_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_F_CB);
					//~~~
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_NOPPP_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_REPAY_WITHPPP_CB);
					//~~~
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_OTHER_CB);
					//~~~
					if(true){ // pos=G
						cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_B_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_C_CB);
					}
					if(true){ // pos=N
						cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_A_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_B_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_C_CB);
					}
					if (true) {
						cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PC_GNTEE_CB);
					}
					if(true){ 
						cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_1N_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_1G_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_2N_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_P9_SIG_2G_CB);
					}
				}
				for(String p : cbColumn_to_icon){
					if(Util.equals(CB_Y_STR, result.get(p))){
						result.put(p, CB_ON_STR);
					}else if(Util.equals(CB_N_STR, result.get(p))){
						result.put(p, ""); //若N，不勾選
					}else{
						//若 N 或 空白
					}
				}
			}
			
			if(true){
				result.put(ContractDocConstants.CtrType1.ELOAN_PC_SERV_TEL_CB, on_when_has_data(result.get(ContractDocConstants.CtrType1.ELOAN_PC_SERV_TEL_T1), CB_ON_STR));
				result.put(ContractDocConstants.CtrType1.ELOAN_PC_SERV_FAX_CB, on_when_has_data(result.get(ContractDocConstants.CtrType1.ELOAN_PC_SERV_FAX_T1), CB_ON_STR));				
				result.put(ContractDocConstants.CtrType1.ELOAN_PC_SERV_MAIL_CB, on_when_has_data(result.get(ContractDocConstants.CtrType1.ELOAN_PC_SERV_MAIL_T1), CB_ON_STR));				
				result.put(ContractDocConstants.CtrType1.ELOAN_PC_SERV_URL_CB, CB_ON_STR);						
				result.put(ContractDocConstants.CtrType1.ELOAN_PC_SERV_CALL_CB, CB_ON_STR);
				result.put(ContractDocConstants.CtrType1.ELOAN_PC_SERV_OTHER_CB, on_when_has_data(result.get(ContractDocConstants.CtrType1.ELOAN_PC_SERV_OTHER_T1), CB_ON_STR));
			}
			if(true){ //由程式依照輸入的欄位，決定第X目
				if(true){
					LinkedHashMap<String, String[]> noPPP_cols = new LinkedHashMap<String, String[]>();
					if(true){
						noPPP_cols.put("一", new String[]{ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_1T1, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_1T2});
						noPPP_cols.put("二", new String[]{ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_2T1});
						noPPP_cols.put("三", new String[]{ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_3T1});
					}
					LinkedHashSet<String> noPPP_way = new LinkedHashSet<String>();
					for(String way_no : noPPP_cols.keySet()){
						for(String assign_col : noPPP_cols.get(way_no)){
							add_when_has_data(result.get(assign_col), noPPP_way, way_no);	
						}							
					}					
					result.put(ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_WAY, StringUtils.join(noPPP_way, "、"));
				}
				if(true){
					LinkedHashMap<String, String[]> withPPP_cols = new LinkedHashMap<String, String[]>();
					if(true){
						withPPP_cols.put("一", new String[]{ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1X1
								, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1X2
								, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1X3
								, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y1
								, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y2
								, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y3
								, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y4
								, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y5
								, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y6
								, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y7
								, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y8});
						withPPP_cols.put("二", new String[]{ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_2T1});
					}
					LinkedHashSet<String> withPPP_way = new LinkedHashSet<String>();
					for(String way_no : withPPP_cols.keySet()){
						for(String assign_col : withPPP_cols.get(way_no)){
							add_when_has_data(result.get(assign_col), withPPP_way, way_no);	
						}							
					}
					result.put(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_WAY, StringUtils.join(withPPP_way, "、"));
				}
			}
			//J-113-0012_10702_B1002 e-Loan調整紙本契約書邏輯
			if (true) {
				result.put(ContractDocConstants.CtrType2.ELOAN_PC_GNTEE_CB, (Util.equals(result.get("eloan_pb_gnteeG_cb"), CB_ON_STR) || Util.equals(result.get("eloan_pb_gnteeN_cb"), CB_ON_STR))? CB_ON_STR : "");
			}
		}
		return result;		
	}
	
	private LinkedHashMap<String, String> get_paramMap_ctrType2(C340M01A c340m01a, String mock_mode)
	throws CapException {
		LinkedHashMap<String, String> result = new LinkedHashMap<String, String>();
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_2, c340m01a.getCtrType())){
			if(Util.equals("Y", mock_mode)){
				return mock_ctrType2_paramMap(c340m01a);	
			}

			if(true){
				result.put(ContractDocConstants.CtrType2.ELOAN_P1_CONTR_NO, c340m01a.getContrNumber());
				result.put(ContractDocConstants.CtrType2.ELOAN_P1_CONTR_CNAME, c340m01a.getContrPartyNm());
			}
			for(C340M01C c340m01c: clsService.findC340M01C(c340m01a.getMainId())){
				String jsonData = Util.trim(c340m01c.getJsonData());
				JSONObject jsonObject = DataParse.toJSON(jsonData);
				LMSUtil.addJsonToStringMap(result, jsonObject);
			}
			
			
			if(true){ // 把參數 eloan_p***_cb=Y(CB_Y_STR) 的資料，轉換成[V]的 icon
				Set<String> cbColumn_to_icon = new HashSet<String>();
				if(true){
					cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_PA_DELIV_A_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_PA_DELIV_B_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_PA_DELIV_C_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_PA_DELIV_D_CB);					
					//~~~
					cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_NOPPP_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_WITHPPP_CB);
					//~~~
					cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_OTHER_CB);
					//~~~
					if(true){ // pos=G
						cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_E_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_D_CB);
					}
					if(true){ // pos=N
						cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_F_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_G_CB);
					}
					if (true) {
						cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PC_GNTEE_CB);
					}
					if(true){ 
						cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_1N_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_1G_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_2N_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType2.ELOAN_P9_SIG_2G_CB);
					}
				}
				for(String p : cbColumn_to_icon){
					if(Util.equals(CB_Y_STR, result.get(p))){
						result.put(p, CB_ON_STR);
					}else if(Util.equals(CB_N_STR, result.get(p))){
						result.put(p, ""); //若N，不勾選
					}else{
						//若 N 或 空白
					}
				}
			}
			
			if(true){
				result.put(ContractDocConstants.CtrType2.ELOAN_PC_SERV_TEL_CB, on_when_has_data(result.get(ContractDocConstants.CtrType2.ELOAN_PC_SERV_TEL_T1), CB_ON_STR));
				result.put(ContractDocConstants.CtrType2.ELOAN_PC_SERV_FAX_CB, on_when_has_data(result.get(ContractDocConstants.CtrType2.ELOAN_PC_SERV_FAX_T1), CB_ON_STR));				
				result.put(ContractDocConstants.CtrType2.ELOAN_PC_SERV_MAIL_CB, on_when_has_data(result.get(ContractDocConstants.CtrType2.ELOAN_PC_SERV_MAIL_T1), CB_ON_STR));				
				result.put(ContractDocConstants.CtrType2.ELOAN_PC_SERV_URL_CB, CB_ON_STR);						
				result.put(ContractDocConstants.CtrType2.ELOAN_PC_SERV_CALL_CB, CB_ON_STR);
				result.put(ContractDocConstants.CtrType2.ELOAN_PC_SERV_OTHER_CB, on_when_has_data(result.get(ContractDocConstants.CtrType2.ELOAN_PC_SERV_OTHER_T1), CB_ON_STR));
			}
			if(true){ //由程式依照輸入的欄位，決定第X目
				if(true){
					LinkedHashMap<String, String[]> ctrType2_useWay_cols = new LinkedHashMap<String, String[]>();
					if(true){
						ctrType2_useWay_cols.put("一", new String[]{ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T1
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T2
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T3
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T4
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T5
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T6});
						ctrType2_useWay_cols.put("二", new String[]{ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T1
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T2
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T3
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T4
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T5
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T6
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T7
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T8
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T1
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T2
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T3
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T4
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T5
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T6
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T7
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T8
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T9
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T1
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T2
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T3
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T4
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T5
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T6
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T7
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T8
								, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T9});
						ctrType2_useWay_cols.put("三", new String[]{ContractDocConstants.CtrType2.ELOAN_PA_USE_E_T1});
					}
					result.put(ContractDocConstants.CtrType2.ELOAN_PA_USE_WAY, build_choseKey_by_notEmptyKVValue(ctrType2_useWay_cols, "、", result));
				}
				if(true){
					LinkedHashMap<String, String[]> ctrType2_withPPP_cols = new LinkedHashMap<String, String[]>();
					if(true){
						ctrType2_withPPP_cols.put("一", new String[]{ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1X1
								, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1X2
								, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1X3
								, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y1
								, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y2
								, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y3
								, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y5
								, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y6
								, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y7
								, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y9
								, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y10
								, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y11});
						ctrType2_withPPP_cols.put("二", new String[]{ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_2T1});
					}
					result.put(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_WAY, build_choseKey_by_notEmptyKVValue(ctrType2_withPPP_cols, "、", result));
				}
				if(true){
					LinkedHashMap<String, String[]> ctrType2_noPPP_cols = new LinkedHashMap<String, String[]>();
					if(true){
						ctrType2_noPPP_cols.put("一", new String[]{ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_1T1});
						ctrType2_noPPP_cols.put("二", new String[]{ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_2T1});
						ctrType2_noPPP_cols.put("三", new String[]{ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_3T1});
						ctrType2_noPPP_cols.put("四", new String[]{ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_4T1});
						ctrType2_noPPP_cols.put("五", new String[]{ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_5T1});
					}
					result.put(ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_WAY, build_choseKey_by_notEmptyKVValue(ctrType2_noPPP_cols, "、", result));
				}
			}
			//J-113-0012_10702_B1002 e-Loan調整紙本契約書邏輯
			if (true) {
				result.put(ContractDocConstants.CtrType2.ELOAN_PC_GNTEE_CB, (Util.equals(result.get("eloan_pb_gnteeG_cb"), CB_ON_STR) || Util.equals(result.get("eloan_pb_gnteeN_cb"), CB_ON_STR)) && Util.notEquals(result.get("eloan_pa_repay_way"), "一")? CB_ON_STR : "");
			}
		}
		return result;		
	}
	
	private LinkedHashMap<String, String> get_paramMap_ctrType3(C340M01A c340m01a, String mock_mode)
	throws CapException {
		LinkedHashMap<String, String> result = new LinkedHashMap<String, String>();
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_3, c340m01a.getCtrType())){
			if(Util.equals("Y", mock_mode)){
				return mock_ctrType3_paramMap(c340m01a);	
			}

			if(true){
				result.put(ContractDocConstants.CtrType3.ELOAN_P1_CONTR_NO, c340m01a.getContrNumber());
				result.put(ContractDocConstants.CtrType3.ELOAN_P1_CONTR_CNAME, c340m01a.getContrPartyNm());
			}
			for(C340M01C c340m01c: clsService.findC340M01C(c340m01a.getMainId())){
				String jsonData = Util.trim(c340m01c.getJsonData());
				JSONObject jsonObject = DataParse.toJSON(jsonData);
				LMSUtil.addJsonToStringMap(result, jsonObject);
			}
			
			if(true){
				String deliv_D1_t1 = Util.trim(result.get(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T1));
				String deliv_D1_t1A = "";
				String deliv_D1_t1B = "";
				int split_cnt = 6;
				if(deliv_D1_t1.length() > split_cnt){
					deliv_D1_t1A = StringUtils.substring(deliv_D1_t1, 0, split_cnt);
					deliv_D1_t1B = StringUtils.substring(deliv_D1_t1, split_cnt);
				}else{
					deliv_D1_t1A = deliv_D1_t1;
					deliv_D1_t1B = "";
				}
				result.put(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T1A, deliv_D1_t1A);
				result.put(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T1B, deliv_D1_t1B);
			}
			
			if(true){ // 把參數 eloan_p***_cb=Y(CB_Y_STR) 的資料，轉換成[V]的 icon
				Set<String> cbColumn_to_icon = new HashSet<String>();
				if(true){
					cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_A_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_B_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_C_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D2_1CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D2_2CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PA_DELIV_E_CB);
					//~~~
					cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_NOPPP_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_WITHPPP_CB);
					//~~~
					cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_CB);
					cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_OTHER_CB);
					//~~~
					if(true){ // pos=G
						cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_B_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_C_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_D_CB);
					}
					if(true){ // pos=N
						cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_B_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_C_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_D_CB);
					}
					if (true) {
						cbColumn_to_icon.add(ContractDocConstants.CtrType1.ELOAN_PC_GNTEE_CB);
					}
					if(true){ 
						cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_1N_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_1G_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_2N_CB);
						cbColumn_to_icon.add(ContractDocConstants.CtrType3.ELOAN_P9_SIG_2G_CB);
					}
				}
				for(String p : cbColumn_to_icon){
					if(Util.equals(CB_Y_STR, result.get(p))){
						result.put(p, CB_ON_STR);
					}else if(Util.equals(CB_N_STR, result.get(p))){
						result.put(p, ""); //若N，不勾選
					}else{
						//若 N 或 空白
					}
				}
			}
			
			if(true){
				result.put(ContractDocConstants.CtrType3.ELOAN_PC_SERV_TEL_CB, on_when_has_data(result.get(ContractDocConstants.CtrType3.ELOAN_PC_SERV_TEL_T1), CB_ON_STR));
				result.put(ContractDocConstants.CtrType3.ELOAN_PC_SERV_FAX_CB, on_when_has_data(result.get(ContractDocConstants.CtrType3.ELOAN_PC_SERV_FAX_T1), CB_ON_STR));				
				result.put(ContractDocConstants.CtrType3.ELOAN_PC_SERV_MAIL_CB, on_when_has_data(result.get(ContractDocConstants.CtrType3.ELOAN_PC_SERV_MAIL_T1), CB_ON_STR));				
				result.put(ContractDocConstants.CtrType3.ELOAN_PC_SERV_URL_CB, CB_ON_STR);						
				result.put(ContractDocConstants.CtrType3.ELOAN_PC_SERV_CALL_CB, CB_ON_STR);
				result.put(ContractDocConstants.CtrType3.ELOAN_PC_SERV_OTHER_CB, on_when_has_data(result.get(ContractDocConstants.CtrType3.ELOAN_PC_SERV_OTHER_T1), CB_ON_STR));
			}
			if(true){ //由程式依照輸入的欄位，決定第X目
				if(true){
					LinkedHashMap<String, String[]> ctrType3_useWay_cols = new LinkedHashMap<String, String[]>();
					if(true){
						ctrType3_useWay_cols.put("一", new String[]{ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T1
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T2
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T3
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T4
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T5
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T6
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T7
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T8});
						ctrType3_useWay_cols.put("二", new String[]{ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T1
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T2
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T3
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T4
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T5
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T6
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T7
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T8
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T9});
						ctrType3_useWay_cols.put("三", new String[]{ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T1
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T2
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T3
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T4
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T5
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T6
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T7
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T8
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T9});
						ctrType3_useWay_cols.put("四", new String[]{ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T1
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T2
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T3
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T4
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T5
								, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T6});
						ctrType3_useWay_cols.put("五", new String[]{ContractDocConstants.CtrType3.ELOAN_PA_USE_E_T1});
					}
					result.put(ContractDocConstants.CtrType3.ELOAN_PA_USE_WAY, build_choseKey_by_notEmptyKVValue(ctrType3_useWay_cols, "、", result));
				}
				if(true){
					LinkedHashMap<String, String[]> ctrType3_withPPP_cols = new LinkedHashMap<String, String[]>();
					if(true){
						ctrType3_withPPP_cols.put("一", new String[]{ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1X1
								, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1X2
								, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1X3
								, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y1
								, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y2
								, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y3
								, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y5
								, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y6
								, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y7});
						ctrType3_withPPP_cols.put("二", new String[]{ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_2T1});
					}
					result.put(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_WAY, build_choseKey_by_notEmptyKVValue(ctrType3_withPPP_cols, "、", result));
				}
				if(true){
					LinkedHashMap<String, String[]> ctrType3_noPPP_cols = new LinkedHashMap<String, String[]>();
					if(true){
						ctrType3_noPPP_cols.put("一", new String[]{ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_1T1});
						ctrType3_noPPP_cols.put("二", new String[]{ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_2T1});
						ctrType3_noPPP_cols.put("三", new String[]{ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_3T1});
						ctrType3_noPPP_cols.put("四", new String[]{ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_4T1});
					}
					result.put(ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_WAY, build_choseKey_by_notEmptyKVValue(ctrType3_noPPP_cols, "、", result));
				}
			}

			//J-113-0012_10702_B1002 e-Loan調整紙本契約書邏輯
			if (true) {
				result.put(ContractDocConstants.CtrType3.ELOAN_PC_GNTEE_CB, (Util.equals(result.get("eloan_pb_gnteeG_cb"), CB_ON_STR) || Util.equals(result.get("eloan_pb_gnteeN_cb"), CB_ON_STR)) && Util.notEquals(result.get("eloan_pa_repay_way"), "一")? CB_ON_STR : "");
			}
		}
		return result;		
	}
	private String get_templateName(C340M01A c340m01a, boolean c340m01a_template_lock){
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_1, c340m01a.getCtrType())){
			return get_templateName_ctrType1(c340m01a, c340m01a_template_lock);
		}else if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_2, c340m01a.getCtrType())){
			return get_templateName_ctrType2(c340m01a, c340m01a_template_lock);
		}else if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_3, c340m01a.getCtrType())){
			return get_templateName_ctrType3(c340m01a, c340m01a_template_lock);
		}
		return "";
	}
	private String get_templateName_ctrType1(C340M01A c340m01a, boolean c340m01a_template_lock){
		String rptId = Util.trim(c340m01a.getRptId());
		//***********************************
		// TODO word文件【限制編輯】解鎖密碼 【lms.C340M01A】
		// (step 1)先反白，插入書籤   
		// (step 2)限制編輯，例外
		// 在 第五條（利率調整之通知）、第六條(保證條款)，加上可編輯區塊 => 調整換行，讓該頁包含完整的第4條、第5條
		// 在 個別商議條款，加上可編輯區塊
		// 在 中華民國           年  　月  　日 的上面，加上可編輯區塊 => 若因資料太多列而跨頁，還讓 經辦 能把中間的空白列刪掉  
		//***********************************
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_1, c340m01a.getCtrType())){
			
			if(Util.equals(rptId, ContractDocConstants.C340M01A_RptId.V202003)){
				return c340m01a_template_lock?"CLS_ctrType1_V202003.xml":"CLS_ctrType1_V202003.unlock.xml";
			}
			else if(Util.equals(rptId, ContractDocConstants.C340M01A_RptId.V202008)){
				return c340m01a_template_lock?"CLS_ctrType1_V202008.xml":"CLS_ctrType1_V202008.unlock.xml";
			}
			else if(Util.equals(rptId, ContractDocConstants.C340M01A_RptId.V202206)){
				return c340m01a_template_lock?"CLS_ctrType1_V202206.xml":"CLS_ctrType1_V202206.unlock.xml";
			}
			else if(Util.equals(rptId, ContractDocConstants.C340M01A_RptId.V202212)){
				return c340m01a_template_lock?"CLS_ctrType1_V202212.xml":"CLS_ctrType1_V202212.unlock.xml";
			}
			else if(Util.equals(rptId, ContractDocConstants.C340M01A_RptId.V202304)){
				return c340m01a_template_lock?"CLS_ctrType1_V202304.xml":"CLS_ctrType1_V202304.unlock.xml";
			}
			else if(Util.equals(rptId, ContractDocConstants.C340M01A_RptId.V202401)){
				return c340m01a_template_lock?"CLS_ctrType1_V202401.xml":"CLS_ctrType1_V202401.unlock.xml";
			}
		}
		return "";
	}
	private String get_templateName_ctrType2(C340M01A c340m01a, boolean c340m01a_template_lock){
		String rptId = Util.trim(c340m01a.getRptId());
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_2, c340m01a.getCtrType())
				&& Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType2_V202409)){
			return c340m01a_template_lock?"CLS_ctrType2_V202409.xml":"CLS_ctrType2_V202409.xml";
		}
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_2, c340m01a.getCtrType())
				&& Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType2_V202401)){
			return c340m01a_template_lock?"CLS_ctrType2_V202401.xml":"CLS_ctrType2_V202401.xml";
		}
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_2, c340m01a.getCtrType()) 
				&& Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType2_V202304)){
			return c340m01a_template_lock?"CLS_ctrType2_V202304.xml":"CLS_ctrType2_V202304.xml";
		}
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_2, c340m01a.getCtrType()) 
				&& Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType2_V202212)){
			return c340m01a_template_lock?"CLS_ctrType2_V202212.xml":"CLS_ctrType2_V202212.xml";
		}
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_2, c340m01a.getCtrType()) 
				&& Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType2_V202206)){
			return "CLS_ctrType2_V202206.xml";
		}
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_2, c340m01a.getCtrType()) 
				&& Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType2_V202010)){
			return "CLS_ctrType2_V202010.xml";
		}
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_2, c340m01a.getCtrType()) 
				&& Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType2_V202009)){
			return c340m01a_template_lock?"CLS_ctrType2_V202009.xml":"CLS_ctrType2_V202009.xml";
		}
		return "";
	}
	private String get_templateName_ctrType3(C340M01A c340m01a, boolean c340m01a_template_lock){
		String rptId = Util.trim(c340m01a.getRptId());
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_3, c340m01a.getCtrType())
				&& Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType3_V202008)){
			return c340m01a_template_lock?"CLS_ctrType3_V202008.xml":"CLS_ctrType3_V202008.xml";
		}
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_3, c340m01a.getCtrType()) 
				&& Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType3_V202206)){
			return c340m01a_template_lock?"CLS_ctrType3_V202206.xml":"CLS_ctrType3_V202206.xml";
		}
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_3, c340m01a.getCtrType()) 
				&& Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType3_V202210)){
			return c340m01a_template_lock?"CLS_ctrType3_V202210.xml":"CLS_ctrType3_V202210.xml";
		}
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_3, c340m01a.getCtrType()) 
				&& Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType3_V202212)){
			return c340m01a_template_lock?"CLS_ctrType3_V202212.xml":"CLS_ctrType3_V202212.xml";
		}
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_3, c340m01a.getCtrType()) 
				&& Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType3_V202304)){
			return c340m01a_template_lock?"CLS_ctrType3_V202304.xml":"CLS_ctrType3_V202304.xml";
		}
		if(Util.equals(ContractDocConstants.C340M01A_CtrType.Type_3, c340m01a.getCtrType())
				&& Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType3_V202401)){
			return c340m01a_template_lock?"CLS_ctrType3_V202401.xml":"CLS_ctrType3_V202401.xml";
		}
		return "";
	}
	
	private String join_word_template_param(String traceStr, String raw_srcStr, Map<String, String> passed_paramMap) {
		String srcStr = raw_srcStr;

		Pattern pattern_run = Pattern
				.compile("(?s)<w:r\\b[^>]*>(?:(?!<w:r\b).)*?</w:r\\b[^>]*>");
		
		/* 增加【Form Fields】判斷  => 插入的 文字控制項，在print時會出現灰底
		<w:fldSimple w:instr="">
		</w:fldSimple>

		<w:fldChar w:fldCharType="begin">
			<w:ffData>
			<w:name w:val="Text999"/>
			<w:enabled/>
			<w:calcOnExit w:val="0"/>
			<w:textInput/>
			</w:ffData>
		</w:fldChar>			
		<w:r><w:fldChar w:fldCharType="separate"/></w:r>
		<w:r><w:fldChar w:fldCharType="end"/></w:r>
		*/
		// if(clsService.is_function_on_codetype("c340m01a_word_FormFields")){
		// 	pattern_run = Pattern.compile("(?s)<w:r\\b[^>]*>(?:(?!<w:r\b).)*(?:(?!<w:instrText\b).)*(?:(?!<w:fldChar\b).)*?</w:r\\b[^>]*>");	
		// }
		
		Pattern pattern_runTextTagAndContent = Pattern
				.compile("(?s)<w:t\\b[^>]*>(?:(?!<w:t\b).)*?</w:t\\b[^>]*>");
		Pattern pattern_tag_rPr = Pattern
				.compile("(?s)<w:rPr\\b[^>]*>(?:(?!<w:rPr\b).)*?</w:rPr\\b[^>]*>");
		Pattern pattern_tag_w_t = Pattern.compile("<w:t\\b[^>]*>");
		Pattern pattern_tag_w_t_end = Pattern.compile("</w:t\\b[^>]*>");
		//===========
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, String> paramMap = new LinkedHashMap<String, String>();
		for (String k : passed_paramMap.keySet()) {
			String injectParamVal = Util.trim(passed_paramMap.get(k));
			if (Util.isEmpty(injectParamVal)) {
				continue;
			}
			paramMap.put(k, injectParamVal);
		}
		
		if(Util.equals(user.getSsoUnitNo(), "900")){ //為了 debug
			LOGGER.info("join_word_template_param>>>"+paramMap.toString());
		}
		
		for (String k : paramMap.keySet()) {
			String injectParamVal = Util.trim(paramMap.get(k));
			
			String[] arr_bookMark = split_ByBookMark(k, srcStr);
			if (arr_bookMark.length == 3) {
				String befStr_bookmark = arr_bookMark[0];
				String bookmarkTagAndContentStr = arr_bookMark[1];
				String aftStr_bookmark = arr_bookMark[2];
				// =============
				String[] arr_bookmarkTagAndContent = split_ByRunText(
						bookmarkTagAndContentStr, pattern_run);
				if (arr_bookmarkTagAndContent.length == 3) {
					String bookMarkTagBeg = arr_bookmarkTagAndContent[0];
					String runPrAndContentStr = arr_bookmarkTagAndContent[1];
					String bookMarkTagEnd = arr_bookmarkTagAndContent[2];
					// ~~~~~~
					String[] arr_runPrAndContent = ContractDocUtil.split_into_pre_match_aft_byFirstFind(runPrAndContentStr, pattern_runTextTagAndContent);
					if (arr_runPrAndContent.length == 3) {
						String new_runPrAndContent = "";
						String debug_str = "";
						int idx_rPr = arr_runPrAndContent[0].indexOf("<w:rPr");
						int idx_u = arr_runPrAndContent[0].indexOf("<w:u");
						if (idx_rPr > 0 && idx_u > idx_rPr) {
							// 有底線, 例如：帳號_________________
							new_runPrAndContent = addColorAndSetTextWithUnderLine(
									k, arr_runPrAndContent, injectParamVal,
									pattern_tag_w_t, pattern_tag_w_t_end,
									pattern_tag_rPr);
							debug_str = "addColorAndSetTextWithUnderLine";
						} else {
							// 無底線，例如：甲方 ○○○
							new_runPrAndContent = addColorAndSetText(k,
									arr_runPrAndContent, injectParamVal,
									pattern_tag_w_t, pattern_tag_w_t_end,
									pattern_tag_rPr);
							debug_str = "addColorAndSetText";
						}
						String chg_flag = Util.equals(runPrAndContentStr,
								new_runPrAndContent) ? "Eq" : "Diff";
						if (Util.equals(chg_flag, "Eq")) {
							LOGGER.info(traceStr+"[" + k + "][new vs old]["
									+ debug_str + "]=[" + chg_flag + "]");
							LOGGER.info(traceStr+"\t" + runPrAndContentStr);
						} else if (Util.equals(chg_flag, "Diff")) {
							
						}

						srcStr = befStr_bookmark + bookMarkTagBeg
								+ (new_runPrAndContent) + bookMarkTagEnd
								+ aftStr_bookmark;

					} else {
						LOGGER.error(traceStr+"[" + k+ "]arr_runPrAndContent.length="+ arr_runPrAndContent.length);
						LOGGER.error(traceStr+runPrAndContentStr);
					}
				} else {
					LOGGER.error(traceStr+"[" + k+ "]arr_bookmarkTagAndContent.length="+ arr_bookmarkTagAndContent.length);
					LOGGER.error(traceStr+bookmarkTagAndContentStr);
				}
			} else {
				LOGGER.error(traceStr+"[" + k + "]arr_bookMark.length="+ arr_bookMark.length);
				LOGGER.error(traceStr+srcStr);
			}
		}
		return srcStr;
	}
	
	private ByteArrayOutputStream writeWordContent(String content)
	throws CapMessageException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		OutputStreamWriter outWriter = null;
		try {
			outWriter = new OutputStreamWriter(baos, "UTF-8");
			outWriter.write(content);
			outWriter.close();
			return baos;
		} catch (UnsupportedEncodingException e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} catch (IOException i) {
			LOGGER.error(i.getMessage());
			throw new CapMessageException(i.getMessage(), getClass());
		}
	}
	
	private String addColorAndSetTextWithUnderLine(String bookmark_name,
			String[] arr_runPrAndContent, String injectParamVal,
			Pattern pattern_tag_w_t, Pattern pattern_tag_w_t_end,
			Pattern pattern_tag_rPr) {
		String runTagBeg_plus_rPr = arr_runPrAndContent[0]; // <w:r
															// w:rsidRPr="asdf"><w:rPr>...</w:rPr>
		String org_runTagAndContentStr = arr_runPrAndContent[1]; // <w:t> qwert
																	// </w:t>
		String runTagEnd = arr_runPrAndContent[2]; // </w:r>
		// ~~~
		String[] text_arr = ContractDocUtil.split_tag_and_content(org_runTagAndContentStr, pattern_tag_w_t, pattern_tag_w_t_end);
		int org_space_cnt = 0;
		if (text_arr.length == 3) {
			org_space_cnt = text_arr[1].length();
		}

		int cnt_empty_bef = 0;
		int cnt_empty_aft = 0;
		int injectStrValCnt = 0;
		String str_empty_bef = "";
		String str_empty_aft = "";
		if (StringUtils.isAsciiPrintable(injectParamVal)) {
			injectStrValCnt = injectParamVal.length();
		} else {
			int sz = injectParamVal.length();
			for (int i = 0; i < sz; i++) {
				if (CharUtils.isAsciiPrintable(injectParamVal.charAt(i))) {
					++injectStrValCnt;
				} else {
					injectStrValCnt = (injectStrValCnt + 2);
				}
			}
		}
		if (true) {
			if (org_space_cnt > injectStrValCnt) {
				int val = (org_space_cnt - injectStrValCnt) / 2;
				cnt_empty_bef = val;
				cnt_empty_aft = val;
			}

			if (cnt_empty_bef == 0) {
				cnt_empty_bef = 1;
			}
			if (cnt_empty_aft == 0) {
				cnt_empty_aft = 1;
			}

			str_empty_bef = StringUtils.repeat(" ", cnt_empty_bef);
			str_empty_aft = StringUtils.repeat(" ", cnt_empty_aft);
		}
		// ====================================
		// 填入資料分為3段
		// 第1段 黑色底線
		// 第2段 藍色底線，且為injectParamVal
		// 第3段 黑色底線
		String p1 = runTagBeg_plus_rPr
				+ (text_arr[0] + str_empty_bef + text_arr[2]) + runTagEnd;
		String p2 = addColorAndSetText(bookmark_name, arr_runPrAndContent,
				injectParamVal, pattern_tag_w_t, pattern_tag_w_t_end,
				pattern_tag_rPr);
		String p3 = runTagBeg_plus_rPr
				+ (text_arr[0] + str_empty_aft + text_arr[2]) + runTagEnd;

		return p1 + p2 + p3;
	}
	
	private String addColorAndSetText(String bookmark_name,
			String[] arr_runPrAndContent, String injectParamVal,
			Pattern pattern_tag_w_t, Pattern pattern_tag_w_t_end,
			Pattern pattern_tag_rPr) {
		String runTagBeg_plus_rPr = arr_runPrAndContent[0]; // <w:r
															// w:rsidRPr="asdf"><w:rPr>...</w:rPr>
		String org_runTagAndContentStr = arr_runPrAndContent[1]; // <w:t> qwert
																	// </w:t>
		String runTagEnd = arr_runPrAndContent[2]; // </w:r>
		// ~~~
		String[] text_arr = ContractDocUtil.split_tag_and_content(org_runTagAndContentStr, pattern_tag_w_t, pattern_tag_w_t_end);
		if (text_arr.length == 3) {
			// 為了能儘快找到填入的字串，增加 <!-- bm -->
			String new_runTagAndContentStr = text_arr[0] + injectParamVal
					+ text_arr[2] + "<!--{" + bookmark_name + "}-->";
			return _add_blueColor_to_rPr(runTagBeg_plus_rPr, pattern_tag_rPr)
					+ (new_runTagAndContentStr) + runTagEnd;
		}

		return StringUtils.join(arr_runPrAndContent, "");
	}
	
	private String _add_blueColor_to_rPr(String runTagBeg_plus_rPr,
			Pattern pattern_tag_rPr) {
		/* 第1段 <w:r w:rsidRPr="..."> 
		 */
		/* 第2段 <w:rPr><w:rFonts w:ascii="標楷體" w:eastAsia="標楷體" w:hAnsi="標楷體" w:hint="eastAsia"/><w:b/><w:color w:val="FF0000"/><w:sz w:val="28"/><w:szCs w:val="28"/><w:u w:val="single"/></w:rPr>
		 */
		/* 第3段 
		 */
		String[] arr = ContractDocUtil.split_into_pre_match_aft_byFirstFind(runTagBeg_plus_rPr, pattern_tag_rPr);
		if (arr.length == 3) {
			String rPr = arr[1];
			if (rPr.indexOf("<w:color ") >= 0) {			
				int idx = rPr.indexOf("<w:color ");
				String endTag = ">";
				int idx_end = rPr.indexOf(endTag, idx);
				if(idx_end>idx){
					rPr = rPr.substring(0, idx)+rPr.substring(idx_end+endTag.length());
				}				
			}
			
			if (rPr.indexOf("<w:color ") < 0) { // no <w:color
				int idx = rPr.lastIndexOf("</w:rPr");
				if (idx > 0) {
					return arr[0] + (rPr.substring(0, idx)+ " <w:color w:val=\"0000FF\"/>" + rPr.substring(idx)) + arr[2];
				}
			}
		}
		return runTagBeg_plus_rPr;
	}
	
	private String on_when_has_data(String src, String CB_ON_STR){
		if(Util.isNotEmpty(Util.trim(src))){
			return CB_ON_STR;
		}
		return "";
	}
	
	private void add_when_has_data(String src, LinkedHashSet<String> set, String val){
		if(Util.isNotEmpty(Util.trim(src))){
			set.add(val);
		}
	}
	
	private void _injectMap(Map<String, String> map, String k, String v)
	throws CapException {
		if(map.containsKey(k)){
			throw new CapException("ExistKey["+k+"]", getClass());
		}
		map.put(k, v);
	}
	
	/**
	 * <ul>
	 * <li>第1段 beforeStr</li>
	 * <li>第2段 ＜w:bookmarkStart w:id="0" w:name="zxcv"/＞＜w:r
	 * w:rsidRPr="asdf"＞＜w:rPr＞...＜/w:rPr＞＜w:t＞ qwert ＜/w:t＞
	 * ＜/w:r＞＜w:bookmarkEnd w:id="0"/＞</li>
	 * <li>第3段 afterStr</li>
	 * </ul>
	 */
	private String[] split_ByBookMark(String bookMarkName, String srcStr) {
		String[] arr = null;
		String strPattern = "(?s)<w:bookmarkStart\\b[^>]*w:name=\""
				+ bookMarkName + "\"[^>]*>.*<w:bookmarkEnd\\b[^>]*>";
		/*
		 * 參考 https://stackoverflow.com/questions/53646033
		 * 
		 * 若文字為 <w:bookmarkStart w:id="0" w:name="aa1"/><w:bookmarkEnd
		 * w:id="0"/><w:bookmarkStart w:id="1" w:name="aa2"/><w:bookmarkEnd
		 * w:id="1"/> 在加上 (?s) 之後, 若不加上 (?:(?!<w:bookmarkStart\b).)*? 在 parse 時
		 * ● 不會抓到 <w:bookmarkStart w:id="0" w:name="aa1"/><w:bookmarkEnd
		 * w:id="0"/> ● 而會(夾雜另一個tag)抓到 <w:bookmarkStart w:id="0"
		 * w:name="aa1"/><w:bookmarkEnd w:id="0"/><w:bookmarkStart w:id="1"
		 * w:name="aa2"/><w:bookmarkEnd w:id="1"/>
		 */
		strPattern = "(?s)<w:bookmarkStart\\b[^>]*w:name=\""
				+ bookMarkName
				+ "\"[^>]*>(?:(?!<w:bookmarkStart\b).)*?<w:bookmarkEnd\\b[^>]*>";
//		if(srcStr.indexOf(bookMarkName)>0){
			arr = ContractDocUtil.split_into_pre_match_aft_byFirstFind(srcStr, Pattern.compile(strPattern));	
//		}else{
//			arr = new String[]{srcStr};
//		}
		
		if (arr != null && arr.length == 3) {
			// ok
		} else {
			LOGGER.error("split_ByBookMark[" + bookMarkName + "]【"
					+ strPattern + "】【" + srcStr + "】");
		}
		return arr;
	}
	
	/**
	 * <ul>
	 * <li>第A段 ＜w:bookmarkStart w:id="0" w:name="zxcv"/＞</li>
	 * <li>第B~Y段 ＜w:r w:rsidRPr="asdf"＞＜w:rPr＞...＜/w:rPr＞＜w:t＞ qwert ＜/w:t＞
	 * ＜/w:r＞</li>
	 * <li>第Z段 ＜w:bookmarkEnd w:id="0"/＞</li>
	 * </ul>
	 */
	private String[] split_ByRunText(String srcStr, Pattern pattern_run) {
		List<String> list = new ArrayList<String>();
		Matcher matcher = pattern_run.matcher(srcStr);
		int idx_prev_beg = -1;
		int idx_prev_end = -1;
		while (matcher.find()) { // 若一個 bookmark 包含了N個Run
			int idx_beg = matcher.start();
			int idx_end = matcher.end();
			String part = matcher.group();

			if (idx_prev_beg == -1) {
				list.add(srcStr.substring(0, idx_beg)); // 應抓到 <w:bookmarkStart
														// w:id="0"
														// w:name="zxcv"/>
			}
			// ~~~
			idx_prev_beg = idx_beg;
			idx_prev_end = idx_end;
			// ~~~
			list.add(part);
		}

		if (idx_prev_end != -1) {
			list.add(srcStr.substring(idx_prev_end)); // 應抓到 <w:bookmarkEnd
														// w:id="0"/>
		}
		// ============================
		if (list.size() >= 3) {
			String parseStr = StringUtils.join(list, "");
			if (parseStr.length() == srcStr.length()) {
				// ok
				return list.toArray(new String[list.size()]);
			} else {
				LOGGER.error("diff_length[" + parseStr.length()
						+ " vs " + srcStr.length() + "][" + parseStr + "]["
						+ srcStr + "]");
			}
		}
		return new String[] { srcStr };
	}
	
	/** 若1個額度序號底下, 有多個產品 => 需排除 不變、取消
	 * 若混合簽報{房貸、非房貸}, 例如: 房貸+短期循環的歡喜理財家 => => 需排除 短期循環
	 */	
	private List<L140S02A> get_l140s02a_list_exclude_property78_notHouse(L140M01A l140m01a){
		List<L140S02A> result = new ArrayList<L140S02A>();
		
		List<L140S02A> src_list = clsService.findL140S02A(l140m01a);
		for (L140S02A l140s02a : src_list) {
			if(Util.equals(UtilConstants.Cntrdoc.Property.不變, l140s02a.getProperty())
				|| Util.equals(UtilConstants.Cntrdoc.Property.取消, l140s02a.getProperty())){
				continue;
			}
			
			if(!CrsUtil.is_house_loan(l140s02a.getSubjCode(), l140s02a.getProdKind(), l140s02a.getModelKind())){
				continue;
			}
			result.add(l140s02a);
		}
		return result;
	}
	
	
	/** 信貸契約書，排除{擔保科目} */
	private List<L140S02A> get_l140s02a_list_exclude_property78_ctrType2(L140M01A l140m01a){
		List<L140S02A> result = new ArrayList<L140S02A>();
		
		List<L140S02A> src_list = clsService.findL140S02A(l140m01a);
		for (L140S02A l140s02a : src_list) {
			if(Util.equals(UtilConstants.Cntrdoc.Property.不變, l140s02a.getProperty())
				|| Util.equals(UtilConstants.Cntrdoc.Property.取消, l140s02a.getProperty())){
				continue;
			}
			C900M01D c900m01d = clsService.findC900M01D_subjCode(l140s02a.getSubjCode());
			if(c900m01d!=null){
				String loanTP3 = c900m01d.getSubjCode2();
				if(loanTP3.startsWith("2")
						||loanTP3.startsWith("4")
						||loanTP3.startsWith("6")){
					continue;
				}
			}
			result.add(l140s02a);
		}
		return result;
	}
	
	private List<L140S02A> get_l140s02a_list_exclude_property78(L140M01A l140m01a){
		List<L140S02A> result = new ArrayList<L140S02A>();
		
		List<L140S02A> src_list = clsService.findL140S02A(l140m01a);
		for (L140S02A l140s02a : src_list) {
			if(Util.equals(UtilConstants.Cntrdoc.Property.不變, l140s02a.getProperty())
				|| Util.equals(UtilConstants.Cntrdoc.Property.取消, l140s02a.getProperty())){
				continue;
			}
			
			result.add(l140s02a);
		}
		return result;
	}
	
	@Override
	public void init_C340Relate(C340M01A c340m01a, String tabMainIds){
		String l140m01a_custName = "";
		Map<String, L140M01A> c340m01b_map = new HashMap<String, L140M01A>();
		if(Util.isNotEmpty(tabMainIds)){
			//TODO 多個額度, 在帶入條件時, 會很複雜 => 目前先1份契約書, 對應1個額度
			for(String l140m01a_mainId : tabMainIds.split("\\|")){
				L140M01A l140m01a = clsService.findL140M01A_mainId(l140m01a_mainId);
				if(l140m01a==null){
					continue;
				}
				l140m01a_custName = Util.trim(l140m01a.getCustName());
				//~~~~~~~~~~~
				C340M01B c340m01b = new C340M01B();
				c340m01b.setMainId(c340m01a.getMainId());				
				c340m01b.setTabMainId(l140m01a_mainId);
				c340m01b.setCntrNo(l140m01a.getCntrNo());
				clsService.save(c340m01b);
				//~~~~~~~~~~~
				c340m01b_map.put(c340m01b.getTabMainId(), l140m01a);
			}
		}
		if(c340m01b_map.size()>0){
			LinkedHashMap<String, String> param_map = init_paramMap(c340m01a, c340m01b_map);
			C340M01C c340m01c = new C340M01C();
			c340m01c.setMainId(c340m01a.getMainId());
			c340m01c.setItemType(ContractDocConstants.C340M01C_ItemType.TYPE_0);
			String jsonData = "";
//			if(param_map.size()>0){
			if(true){ //即使 param_map 無資料，也產出{}
				JSONObject jsonObject = new JSONObject();
				jsonObject.putAll( param_map );
				jsonData = jsonObject.toString();
			}
			c340m01c.setJsonData(jsonData);
			c340m01c.setInitialJsonData(jsonData);
			c340m01c.setCreator(c340m01a.getCreator());
			c340m01c.setCreateTime(c340m01a.getCreateTime());
			clsService.save(c340m01c);
		}
		if(Util.isNotEmpty(l140m01a_custName)){
			c340m01a.setCustName(l140m01a_custName);
			if(true){
				TreeSet<String> set = new TreeSet<String>();
				for(String tabMainId : c340m01b_map.keySet()){
					set.add(Util.trim(c340m01b_map.get(tabMainId).getCntrNo()));
				}
				c340m01a.setContrNumber(Util.truncateString(StringUtils.join(set, ","), MAXLEN_C340M01A_CONTRNUMBER));
			}			
			c340m01a.setContrPartyNm(l140m01a_custName);
		}
		if(Util.isEmpty(Util.trim(c340m01a.getCustName()))){ //若 custName 空白，以 custId+dupNo 抓0024
			Map<String, Object> latest0024Data = iCustomerService.findByIdDupNo(c340m01a.getCustId(), c340m01a.getDupNo());
			String custName = Util.trim(MapUtils.getString(latest0024Data, "CNAME"));
			c340m01a.setCustName(custName);	
			c340m01a.setContrPartyNm(custName);
		}
		clsService.save(c340m01a);
	}
	
	private String get_SIG_ADDR(String caseMainId, String custId, String dupNo){
		C120S01A c120s01a = clsService.findC120S01A(caseMainId, custId, dupNo);
		if(c120s01a!=null){
			return Util.trim(c120s01a.getFTarget()); //fTarget=戶籍地址
			//coTarget=通訊地址
		}
		return "";
	}
	
	/* public static void main(String[] args) {
		inject_fmt_extendYear(6);
		inject_fmt_extendYear(11);
		inject_fmt_extendYear(12);
		inject_fmt_extendYear(13);
		inject_fmt_extendYear(24);
	}
	private static void inject_fmt_extendYear(int extendCntSinceFirst_cross_prod){
		String[] exend_info_arr = fmt_extendYear(extendCntSinceFirst_cross_prod);
		String c_t1 = exend_info_arr[0];
		String c_t2 = exend_info_arr[1];
		String c_t3 = exend_info_arr[2];
		String c_t4 = exend_info_arr[3];
		//~~~~~~~
		System.out.println("寬限期["+extendCntSinceFirst_cross_prod+"], 前 " +c_t1+" 年 " +c_t2+" 月，自第 " +c_t3+" 年 " +c_t4+" 月起");
	} */	
	private String[] fmt_extendYear(int extendCntSinceFirst_cross_prod){
		String[] arr = new String[]{"", "", "", ""};
		if(true){
			int extendY = extendCntSinceFirst_cross_prod/12;
			int extendM = extendCntSinceFirst_cross_prod%12;
			
			arr[0] = String.valueOf(extendY);
			arr[1] = extendM==0?"":String.valueOf(extendM);
		}
		if(true){
			int nextVal = extendCntSinceFirst_cross_prod;
			int div = nextVal/12;
			int remainder = nextVal%12;			
			if(remainder==0){				
				arr[2] = String.valueOf(div+1);
				arr[3] = "";
			}else if(remainder==11){
				arr[2] = String.valueOf(div);
				arr[3] = String.valueOf(remainder+1);
			}else{
				int i_nextY = div+((remainder+1)/12>0?1:0);				
				arr[2] = String.valueOf(i_nextY);
				arr[3] = String.valueOf(remainder+1);
			}		
		}
		return arr;
	}
	private void _debug(String traceStr, String s){
		
	}
	private TreeSet<Integer> _lnTotMonth_cross_prod(List<L140S02A> l140s02a_list){
		TreeSet<Integer> set = new TreeSet<Integer>();
		for(L140S02A l140s02a : l140s02a_list){
			if (l140s02a.getLnYear() != null
					&& l140s02a.getLnMonth() != null) {
				set.add(l140s02a.getLnYear() * 12 + l140s02a.getLnMonth());
			}	
		}
		return set;
	}
	/* private String get_max_lnTotMonth_cross_prod(List<L140S02A> l140s02a_list){
		TreeSet<Integer> set = _lnTotMonth_cross_prod(l140s02a_list);
		if(set.size()>0){
			Integer max_lnTotMonth = set.last();
			return String.valueOf(max_lnTotMonth);
		}
		return "";
	} */
	private String get_distinct_lnTotMonth_cross_prod(List<L140S02A> l140s02a_list){
		TreeSet<Integer> set = _lnTotMonth_cross_prod(l140s02a_list);
		if(set.size()==1){
			Integer distinct_lnTotMonth = set.last();
			return String.valueOf(distinct_lnTotMonth);
		}
		return ""; 
	}
	
	/** 判斷 本息平均 or 本金平均( ELF501_AVGPAY )( LNF033_RT_TYPE )
	 * @return LNF033_RT_TYPE {2:本息平均攤還, 3:本金平均攤還} 或 Other
	 */
	private String get_distinct_rtType_cross_prod(List<L140S02A> l140s02a_list, Map<Integer, L140S02C> map_l140s02cData, Map<Integer, L140S02E> map_l140s02eData){
		TreeSet<String> _set_LNF033_RT_TYPE = new TreeSet<String>();
		for(L140S02A l140s02a: l140s02a_list){
			Integer seq = l140s02a.getSeq();
			L140S02C l140s02c = map_l140s02cData.get(seq);
			L140S02E l140s02e = map_l140s02eData.get(seq);
			//IntWay{2:期付金}
			if(l140s02c!=null && Util.equals(UtilConstants.L140S02CIntway.期付金, l140s02c.getIntWay()) && l140s02e!=null){
				String l140s02e_payWay = Util.trim(l140s02e.getPayWay());
				// =========
				// 判斷 本息平均 or 本金平均( ELF501_AVGPAY )( LNF033_RT_TYPE )
				if ("1".equals(l140s02e_payWay) || "2".equals(l140s02e_payWay)) {
					_set_LNF033_RT_TYPE.add("2"); // 本息平均攤還
				} else if ("3".equals(l140s02e_payWay) || "4".equals(l140s02e_payWay)) {
					_set_LNF033_RT_TYPE.add("3"); // 本金平均攤還
				} else if ("6".equals(l140s02e_payWay)) {
					// 其它
					_set_LNF033_RT_TYPE.add(VAL_OTHER);
				} else if ("7".equals(l140s02e_payWay)) {
					// 按月付息
					_set_LNF033_RT_TYPE.add(VAL_MONTH_INTEREST);						
				}else{
					_set_LNF033_RT_TYPE.add(VAL_OTHER);
				}			
			}
		}
		if(_set_LNF033_RT_TYPE.size()==1){
			return _set_LNF033_RT_TYPE.last();
		}
		return "";
	}
	
	/** 判斷 月繳 or 雙週繳( ELF501_PAYCLE )( LNF033_INTRT_CYCL )  或 按月計息 或 透支
	 * @return LNF033_INTRT_CYCL {1:月繳, 2:雙繳週} 或 Other
	 */
	private String get_distinct_cycl_cross_prod(List<L140S02A> l140s02a_list, Map<Integer, L140S02C> map_l140s02cData, Map<Integer, L140S02E> map_l140s02eData){
		TreeSet<String> _set_MONTH_INTEREST = new TreeSet<String>();
		TreeSet<String> _set_OVERDRAW = new TreeSet<String>();
		TreeSet<String> _set_LNF033_INTRT_CYCL = new TreeSet<String>();
		for(L140S02A l140s02a: l140s02a_list){
			Integer seq = l140s02a.getSeq();
			L140S02C l140s02c = map_l140s02cData.get(seq);
			L140S02E l140s02e = map_l140s02eData.get(seq);
			//IntWay{2:期付金}
			if(l140s02c!=null && Util.equals(UtilConstants.L140S02CIntway.期付金, l140s02c.getIntWay()) && l140s02e!=null){
				String l140s02e_payWay = Util.trim(l140s02e.getPayWay());
				// =========
				// 繳款週期( ELF501_PAYCLE )( LNF033_INTRT_CYCL ) 
				if ("1".equals(l140s02e_payWay) || "3".equals(l140s02e_payWay)){
					_set_LNF033_INTRT_CYCL.add("1"); //月繳
				}else if ("2".equals(l140s02e_payWay) || "4".equals(l140s02e_payWay)){
					_set_LNF033_INTRT_CYCL.add("2"); //雙週繳
				} else if ("6".equals(l140s02e_payWay)) {
					// 其它
					_set_LNF033_INTRT_CYCL.add(VAL_OTHER);
				} else if ("7".equals(l140s02e_payWay)) {
					// 按月付息
					_set_LNF033_INTRT_CYCL.add(VAL_MONTH_INTEREST);
				}else{
					_set_LNF033_INTRT_CYCL.add(VAL_OTHER);
				}
			}
			else if(l140s02c!=null && Util.equals(UtilConstants.L140S02CIntway.按月計息, l140s02c.getIntWay()) ){
				_set_MONTH_INTEREST.add(VAL_MONTH_INTEREST);
			}
			else if(l140s02c!=null 
					&& ( Util.equals(UtilConstants.L140S02CIntway.透支end, l140s02c.getIntWay()) 
							|| Util.equals(UtilConstants.L140S02CIntway.透支top, l140s02c.getIntWay()) ) ){
				//透支不會有 LNF033
				_set_OVERDRAW.add(VAL_OVERDRAW);
			}
		}
		if(_set_MONTH_INTEREST.size()==0 && _set_OVERDRAW.size()==0 && _set_LNF033_INTRT_CYCL.size()==1){
			return _set_LNF033_INTRT_CYCL.last();
		}
		if(_set_MONTH_INTEREST.size()>0 && _set_OVERDRAW.size()==0 && _set_LNF033_INTRT_CYCL.size()==0){
			return _set_MONTH_INTEREST.last();
		}
		if(_set_MONTH_INTEREST.size()==0 && _set_OVERDRAW.size()>0 && _set_LNF033_INTRT_CYCL.size()==0){
			return _set_OVERDRAW.last();
		}
		return "";
	}
	
	/** @return 自第1期起的期數 or Other(非第1期開始)
	 */
	private String get_distinct_extendCntSinceFirst_cross_prod(List<L140S02A> l140s02a_list, Map<Integer, L140S02C> map_l140s02cData, Map<Integer, L140S02E> map_l140s02eData){
		TreeSet<String> _set = new TreeSet<String>();
		for(L140S02A l140s02a: l140s02a_list){
			Integer seq = l140s02a.getSeq();
			L140S02C l140s02c = map_l140s02cData.get(seq);
			L140S02E l140s02e = map_l140s02eData.get(seq);
			//IntWay{2:期付金}
			if(l140s02c!=null && Util.equals(UtilConstants.L140S02CIntway.期付金, l140s02c.getIntWay()) && l140s02e!=null){			
				// =========
				// 判斷 寬限期，正常且新做的期付金, 是從第1期起算。
				if (Util.equals("Y", l140s02e.getNowExtend())){
					if ( Util.parseInt(l140s02e.getNowFrom()) == 1
							&& Util.parseInt(l140s02e.getNowEnd()) >= 1) {
						int period_cnt = Util.parseInt(l140s02e.getNowEnd()) - Util.parseInt(l140s02e.getNowFrom()) + 1;								
						_set.add(String.valueOf(period_cnt));
					}else{
						//一開始無寬限期，中間才申請
						_set.add(VAL_OTHER);
					}	
				}else{
					_set.add("0");
				}
			}
		}
		if(_set.size()==1){
			return _set.last();
		}
		return "";
	}
	
	private String get_distinct_intEndTermSinceFirst_cross_prod(List<L140S02A> l140s02a_list, TreeMap<Integer, L140S02C> map_l140s02cData, TreeMap<Integer, List<L140S02D>> map_l140s02dData){
		TreeSet<String> _set = new TreeSet<String>();
		for(L140S02A l140s02a: l140s02a_list){
			Integer seq = l140s02a.getSeq();
			L140S02C l140s02c = map_l140s02cData.get(seq);
			List<L140S02D> l140s02d_list = map_l140s02dData.get(seq);
			int l140s02d_size = l140s02d_list.size();
			//IntWay{2:期付金}
			if(l140s02c!=null && Util.equals(UtilConstants.L140S02CIntway.期付金, l140s02c.getIntWay()) && l140s02d_size>0){			
				// =========
				// 判斷 迄期，正常且新做的期付金, 是從第1期起算。
				int begTerm = l140s02d_list.get(0).getBgnNum();
				int endTerm = l140s02d_list.get(l140s02d_size-1).getEndNum();
				
				if (begTerm == 1 && endTerm >= 1) {								
					_set.add(String.valueOf(endTerm));
				}else{
					//第1段，非由第1期開始
					_set.add(VAL_OTHER);
				}
			}
		}
		if(_set.size()==1){
			return _set.last();
		}
		return "";
	}
	
	/*
	[Key]			[0]		[1]		[2]						[3]					[4]					[5]			[6]							[7]								[8]			  [9]	  
	phase(1或2) | bgnNum | endNum | rateType(6R或 M2 或N2) | baseRate DEC(6,4) | pmFlag(P:加, M:減)| pmRate | nowRate(等同baseRate+pmRate) | rateFlag(1:固定,2:機動,3:浮動) | rateChgWay | rateChgWay2
	 
	 其它欄位
		seq(1或2) 
	 	rateUser(自訂利率DEC(6,4)) 
	*/
	private LinkedHashMap<String, LinkedHashSet<String>> get_distinct_l140s02d_intr_info(List<L140S02A> l140s02a_list, Set<Integer> chose_seq_set
			, TreeMap<Integer, L140S02C> map_l140s02cData, TreeMap<Integer, List<L140S02D>> map_l140s02dData){
		LinkedHashMap<String, LinkedHashSet<String>> result = new LinkedHashMap<String, LinkedHashSet<String>>();
		for(Integer seq: chose_seq_set){
			L140S02C l140s02c = map_l140s02cData.get(seq);
			List<L140S02D> l140s02d_list = map_l140s02dData.get(seq);
			int l140s02d_size = l140s02d_list.size();
			//IntWay{2:期付金}
			//if(l140s02c!=null && Util.equals(UtilConstants.L140S02CIntway.期付金, l140s02c.getIntWay()) && l140s02d_size>0){
			if(l140s02c!=null &&  l140s02d_size>0){ // 其它貸款契約書，可能包含{期付金、透支、按月計息} => 不要只限制 期付金
				for(L140S02D l140s02d : l140s02d_list){
					String phase = Util.trim(l140s02d.getPhase());
					if(!result.containsKey(phase)){
						result.put(phase, new LinkedHashSet<String>());
					}	
					result.get(phase).add(build_l140s02d_intr_info(l140s02d));
				}				
			}
		}
		return result;
	}
	
	private String get_distinct_L140S02A_lnPurpose_cross_prod(List<L140S02A> l140s02a_list){
		TreeSet<String> _set = new TreeSet<String>();
		for(L140S02A l140s02a: l140s02a_list){
			_set.add(Util.trim(l140s02a.getLnPurpose()));
		}
		if(_set.size()==1){
			return _set.last();
		}
		return "";
	}
	
	private Set<String> get_wordML_lineFeedColumn_in_textarea(String ctrType){
		Set<String> linefeedColumn = new HashSet<String>();
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_1)){
			linefeedColumn.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_3T1);
			linefeedColumn.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_2T1);
			linefeedColumn.add(ContractDocConstants.CtrType1.ELOAN_PA_INTR_OTHER_T1);					
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_2)){
			linefeedColumn.add(ContractDocConstants.CtrType2.ELOAN_PA_REPAY_WITHPPP_TERM);
			linefeedColumn.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_2T1);
			linefeedColumn.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_5T1);
			linefeedColumn.add(ContractDocConstants.CtrType2.ELOAN_PA_INTR_OTHER_T1);	
		}else if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_3)){
			linefeedColumn.add(ContractDocConstants.CtrType3.ELOAN_PA_REPAY_WITHPPP_TERM);
			linefeedColumn.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_2T1);
			linefeedColumn.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_4T1);
			linefeedColumn.add(ContractDocConstants.CtrType3.ELOAN_PA_INTR_OTHER_T1);	
		}
		return linefeedColumn;
	}  
	
	private boolean is_reuse_financialLoan(L140M01A l140m01a, List<L140S02A> l140s02a_list ){
		if(Util.equals(UtilConstants.Cntrdoc.ReUse.循環使用, l140m01a.getReUse())){
			int cnt_financialLoan = 0;
			int cnt_not_financialLoan = 0;
			for(L140S02A l140s02a : l140s02a_list){
				if(Util.equals(UtilConstants.Cntrdoc.Property.不變, l140s02a.getProperty()) || Util.equals(UtilConstants.Cntrdoc.Property.取消, l140s02a.getProperty())){
					continue;
				}
				String prodKind = l140s02a.getProdKind();
				if((CrsUtil.is_02(prodKind) && !Util.equals("Y", l140s02a.getChkUsed()))
						||CrsUtil.is_03(prodKind)
						||(CrsUtil.is_68(prodKind) && !Util.equals("Y", l140s02a.getChkUsed()))
						||(CrsUtil.is_31(prodKind) && CrsUtil.is_subj_in_403_603(l140s02a.getSubjCode()))){
					++cnt_financialLoan;
				}else{
					++cnt_not_financialLoan;
				}
			}
			if(cnt_financialLoan>0){
				return true;
			}
		}
		return false;
	}
	
	private boolean is_financialLoan_prod02ChkUsedY_or_prod04_prod68ChkUsedY(L140M01A l140m01a, List<L140S02A> l140s02a_list ){
		int cnt_match = 0;
		int cnt_not_match = 0;
		for(L140S02A l140s02a : l140s02a_list){
			if(Util.equals(UtilConstants.Cntrdoc.Property.不變, l140s02a.getProperty()) || Util.equals(UtilConstants.Cntrdoc.Property.取消, l140s02a.getProperty())){
				continue;
			}
			String prodKind = l140s02a.getProdKind();
			if((CrsUtil.is_02(prodKind) && Util.equals("Y", l140s02a.getChkUsed()))
					||CrsUtil.is_04(prodKind)
					||(CrsUtil.is_68(prodKind) && Util.equals("Y", l140s02a.getChkUsed()))){
				++cnt_match;
			}else{
				++cnt_not_match;
			}
		}
		if(cnt_match>0){
			return true;
		}
		//~~~~~~~~~~
		return false;
	}
	
	private boolean is_mapValue_cnt_eq_1(LinkedHashMap<String, LinkedHashSet<String>> map){		
		for(String k: map.keySet()){
			if(map.get(k).size()==1){
				
			}else{
				return false;		
			}
		}
		return true;
	}
	
	private String proc_l140s02a_rateDesc(L140S02A l140s02a){
		String rateDesc = l140s02a.getRateDesc();
		rateDesc = rateDesc.replaceAll("計息方式：期付金。收息方式：期付金。", "");
		rateDesc = rateDesc.replaceAll("<br>", "");
		rateDesc = rateDesc.replaceAll("<br/>", "");
		rateDesc = rateDesc.replaceAll("<BR>", "");
		rateDesc = rateDesc.replaceAll("<BR/>", "");	
		return rateDesc;
	}
	private String build_intr_textStr(List<L140S02A> l140s02a_list, Set<Integer> chose_seq_set
			, TreeMap<Integer, List<L140S02D>> map_l140s02dData, String distinct_cycl_cross_prod){
		List<String> result = new ArrayList<String>();
		if(chose_seq_set.size()==1){
			for(L140S02A l140s02a: l140s02a_list){
				if(!chose_seq_set.contains(l140s02a.getSeq())){
					continue;
				}
				String rateDesc = proc_l140s02a_rateDesc(l140s02a);									
				//----
				result.add(rateDesc);
			}
		}else{
			for(L140S02A l140s02a: l140s02a_list){
				if(!chose_seq_set.contains(l140s02a.getSeq())){
					continue;
				}
				C900M01A c900m01a = clsService.findC900M01A_prodKind(l140s02a.getProdKind());
				String prodDesc = "("+CrsUtil.getProdKindName(c900m01a)+")";
				String rateDesc = proc_l140s02a_rateDesc(l140s02a);									
				//----
				result.add(prodDesc+rateDesc);				
			}	
		}		

		// 在組字時，可能是 6R但三段式、四段式
		// 或是 P7 中華郵政二年期定期儲金額度未達新台幣５００萬元機動利率
		// 或是 M2
		if(true){
			LinkedHashMap<String, String> show_baseRate_rateTypeMap = new LinkedHashMap<String, String>();
			for(Integer seq : chose_seq_set){
				for(L140S02D l140s02d: map_l140s02dData.get(seq)){
					show_baseRate_rateTypeMap.put(l140s02d.getRateType(), LMSUtil.pretty_numStr(l140s02d.getBaseRate()));
				}	
			}
			if(show_baseRate_rateTypeMap.size()>0){
				String[] currs = new String[] { "TWD" };
				HashMap<String, LinkedHashMap<String, String>> _map = misMislnratService
						.findBaseRateByCurrs(currs);
				Map<String, String> rateDesc_map = _map.get("TWD");
				for(String _LR_CODE : show_baseRate_rateTypeMap.keySet()){
					result.add("訂約時"+LMSUtil.getDesc(rateDesc_map, _LR_CODE)+"為"+Util.trim(show_baseRate_rateTypeMap.get(_LR_CODE))+"%。");
				}
			}
		}
		return StringUtils.join(result, "");
	}
	private String build_l140s02d_intr_info(L140S02D l140s02d){
		String bgnNum = Util.trim(l140s02d.getBgnNum()); 
		String endNum = Util.trim(l140s02d.getEndNum());
		String rateType = Util.trim(l140s02d.getRateType()); 
		String baseRate = Util.trim(LMSUtil.pretty_numStr(l140s02d.getBaseRate()));
		String pmFlag = Util.trim(l140s02d.getPmFlag());
		String pmRate = Util.trim(LMSUtil.pretty_numStr(l140s02d.getPmRate()));
		String nowRate = Util.trim(LMSUtil.pretty_numStr(l140s02d.getNowRate()));
		String rateFlag = Util.trim(l140s02d.getRateFlag());
		String rateChgWay = Util.trim(l140s02d.getRateChgWay());
		String rateChgWay2 = Util.trim(l140s02d.getRateChgWay2());
		//~~~~~~
		if (CrsUtil.RATE_TYPE_01.equals(rateType) && Util.equals("C01", l140s02d.getRateUserType())) {

		}else{
			if(!Util.equals("1", l140s02d.getRateFlag())){ //非固定利率，去中心抓最新的代率值
				baseRate = LMSUtil.pretty_numStr(_get_latest_mis_MISLNRAT_currTWD(rateType));
			}
		}
		nowRate = LMSUtil.pretty_numStr(ClsUtility.calc_nowRate(l140s02d.getRateType(), l140s02d.getRateUserType(), l140s02d.getRateFlag(), l140s02d.getNowRate(), Util.parseBigDecimal(baseRate), pmFlag, l140s02d.getPmRate()));
		List<String> list = new ArrayList<String>();
		list.add(bgnNum);
		list.add(endNum);
		list.add(rateType);
		list.add(baseRate);
		list.add(pmFlag);
		list.add(pmRate);
		list.add(nowRate);
		list.add(rateFlag);
		list.add(rateChgWay);
		list.add(rateChgWay2);
		return StringUtils.join(list, "|");		
	}
	private LinkedHashMap<String, String> init_paramMap(C340M01A c340m01a, Map<String, L140M01A> c340m01b_map){
		String traceStr = "【"+c340m01a.getCustId()+", c340m01a.mainId="+c340m01a.getMainId()+"】";
		String ctrType = Util.trim(c340m01a.getCtrType());
		String rptId = Util.trim(c340m01a.getRptId());
		
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();
		try{
			if (Util.equals(ContractDocConstants.C340M01A_CtrType.Type_1, ctrType)
					&& (Util.equals(rptId, ContractDocConstants.C340M01A_RptId.V202003)
					||  Util.equals(rptId, ContractDocConstants.C340M01A_RptId.V202008)
					||  Util.equals(rptId, ContractDocConstants.C340M01A_RptId.V202206)
					||  Util.equals(rptId, ContractDocConstants.C340M01A_RptId.V202212)
					||  Util.equals(rptId, ContractDocConstants.C340M01A_RptId.V202304)
					||  Util.equals(rptId, ContractDocConstants.C340M01A_RptId.V202401)
					)) {
				paramMap = init_paramMap_ctrType1_V202003(c340m01a, c340m01b_map);
			}
			if (Util.equals(ContractDocConstants.C340M01A_CtrType.Type_2, ctrType) 
					&& (Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType2_V202009)||Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType2_V202010)
							||Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType2_V202206)
							||Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType2_V202212)
							||Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType2_V202304)
							||Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType2_V202401)
							||Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType2_V202409)
						)) {
				paramMap = init_paramMap_ctrType2_V202009(c340m01a, c340m01b_map);
			}
			if (Util.equals(ContractDocConstants.C340M01A_CtrType.Type_3, ctrType) 
					&& (Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType3_V202008)
							|| Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType3_V202206)
							|| Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType3_V202210)
							|| Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType3_V202212)
							|| Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType3_V202304)
							|| Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType3_V202401))
				) {
				paramMap = init_paramMap_ctrType3_V202008(c340m01a, c340m01b_map);
			}
		}catch(Exception e){
			LOGGER.error(traceStr);
			LOGGER.error(e.getMessage());
			LOGGER.error(StrUtils.getStackTrace(e));
		}
		return paramMap;
	}
	
	private LinkedHashMap<String, String> init_paramMap_ctrType1_V202003(C340M01A c340m01a, Map<String, L140M01A> c340m01b_map)
	throws CapException{	
//		String eloan_p1_contr_no = "";
//		String eloan_p1_contr_cname = "";
		String eloan_p1_contr_name_m = "";
		String eloan_p1_contr_name_n = "";
		String eloan_p1_contr_name_g = "";
		String eloan_p1_contr_amt = "";
		String eloan_pa_deliv_A_cb = "";
		String eloan_pa_deliv_A_t1 = "活期儲蓄";
		String eloan_pa_deliv_A_t2 = "";
		String eloan_pa_deliv_B_cb = "";
		String eloan_pa_deliv_B_t1 = "";
		String eloan_pa_deliv_B_t2 = "";
		String eloan_pa_deliv_B_t3 = "";
		String eloan_pa_deliv_C_cb = "";
		String eloan_pa_deliv_D_cb = "";
		String eloan_pa_deliv_D_t1 = "";
		String eloan_pa_deliv_D_t2 = "";
		String eloan_pa_deliv_D1_t1 = "";
		String eloan_pa_deliv_D1_t1A = "";
		String eloan_pa_deliv_D1_t1B = "";
		String eloan_pa_deliv_D1_t2 = "";
		String eloan_pa_deliv_D1_t3 = "";
		String eloan_pa_deliv_D1_t4 = "";
		String eloan_pa_deliv_D1_t5 = "";
		String eloan_pa_deliv_D1_t6 = "";
		String eloan_pa_deliv_D1_t7 = "";
		String eloan_pa_deliv_E_cb = "";
		String eloan_pa_deliv_E_t1 = "";
		String eloan_pa_use_A_cb = "";
		String eloan_pa_use_A_t1 = "";
		String eloan_pa_use_A_t2 = "";
		String eloan_pa_use_B_cb = "";
		String eloan_pa_use_B_t1 = "";
		String eloan_pa_use_y = "";
		String eloan_pa_use_m = "";
		String eloan_pa_use_begY = "";
		String eloan_pa_use_begM = "";
		String eloan_pa_use_begD = "";
		String eloan_pa_use_endY = "";
		String eloan_pa_use_endM = "";
		String eloan_pa_use_endD = "";
		String eloan_pa_repay_A_cb = "";
		String eloan_pa_repay_B_cb = "";
		String eloan_pa_repay_C_cb = "";
		String eloan_pa_repay_C_t1 = "";
		String eloan_pa_repay_C_t2 = "";
		String eloan_pa_repay_C_t3 = "";
		String eloan_pa_repay_C_t4 = "";
		String eloan_pa_repay_D_cb = "";
		String eloan_pa_repay_D_t1 = "";
		String eloan_pa_repay_D_t2 = "";
		String eloan_pa_repay_D_t3 = "";
		String eloan_pa_repay_D_t4 = "";
		String eloan_pa_repay_E_cb = "";
		String eloan_pa_repay_E_t1 = "";
		String eloan_pa_repay_E_t2 = "";
		String eloan_pa_repay_F_cb = "";
		String eloan_pa_repay_F_t1 = "";
//		String eloan_pa_repay_noPPP_cb = "";
//		String eloan_pa_repay_withPPP_cb = "";		
		String eloan_pa_repay_actNo = "";
		String eloan_pa_repay_feeNo01 = "";
		String eloan_pa_repay_feeNo02 = "";
		String eloan_pa_repay_feeNo03 = "";
		String eloan_pa_repay_feeNo04 = "";
		String eloan_pa_repay_feeNo06 = "";
		String eloan_pa_repay_feeNo07 = "";
		String eloan_pa_intr_noPPP_cb = "";
		String eloan_pa_intr_noPPP_baseRate = "";
//		String eloan_pa_intr_noPPP_way = "";
		String eloan_pa_intr_noPPP_1t1 = "";
		String eloan_pa_intr_noPPP_1t2 = "";
		String eloan_pa_intr_noPPP_2t1 = "";
		String eloan_pa_intr_noPPP_3t1 = "";
		String eloan_pa_intr_withPPP_cb = "";
		String eloan_pa_intr_withPPP_baseRate = "";
//		String eloan_pa_intr_withPPP_way = "";
		String eloan_pa_intr_withPPP_1x1 = "";
		String eloan_pa_intr_withPPP_1x2 = "";
		String eloan_pa_intr_withPPP_1x3 = "";
		String eloan_pa_intr_withPPP_1y1 = "";
		String eloan_pa_intr_withPPP_1y2 = "";
		String eloan_pa_intr_withPPP_1y3 = "";
		String eloan_pa_intr_withPPP_1y4 = "";
		String eloan_pa_intr_withPPP_1y5 = "";
		String eloan_pa_intr_withPPP_1y6 = "";
		String eloan_pa_intr_withPPP_1y7 = "";
		String eloan_pa_intr_withPPP_1y8 = "";
		String eloan_pa_intr_withPPP_2t1 = "";
//		String eloan_pa_intr_other_cb = "";
		String eloan_pa_intr_other_t1 = "";
		String eloan_pb_gnteeG_cb = "";
		String eloan_pb_gnteeG_B_cb = "";
		String eloan_pb_gnteeG_B_t1 = "";
		String eloan_pb_gnteeG_C_cb = "";
		String eloan_pb_gnteeG_C_t1 = "";
		String eloan_pb_gnteeN_cb = "";
		String eloan_pb_gnteeN_A_cb = "";
		String eloan_pb_gnteeN_B_cb = "";
		String eloan_pb_gnteeN_B_t1 = "";
		String eloan_pb_gnteeN_C_cb = "";
		String eloan_pb_gnteeN_C_t1 = "";
		
//		String eloan_pc_serv_tel_cb = "";
		String eloan_pc_serv_tel_t1 = "";
//		String eloan_pc_serv_fax_cb = "";
		String eloan_pc_serv_fax_t1 = "";
//		String eloan_pc_serv_mail_cb = "";
		String eloan_pc_serv_mail_t1 = "";		
//		String eloan_pc_serv_other_cb = "";
		String eloan_pc_serv_other_t1 = "";
		String eloan_pc_court_loc = "";
		String eloan_pc_copy_cnt = "";
		String eloan_pc_spTerm_yRate = "";
		String eloan_pc_spTerm_note = "";
		
		String eloan_p9_sig_m_name = "";
		String eloan_p9_sig_m_custId = "";
		String eloan_p9_sig_m_addr = "";
//		String eloan_p9_sig_1N_cb = "";
		String eloan_p9_sig_1N_name = "";
//		String eloan_p9_sig_1G_cb = "";
		String eloan_p9_sig_1G_name = "";
		String eloan_p9_sig_1_custId = "";
		String eloan_p9_sig_1_addr = "";
//		String eloan_p9_sig_2N_cb = "";
		String eloan_p9_sig_2N_name = "";
//		String eloan_p9_sig_2G_cb = "";
		String eloan_p9_sig_2G_name = "";
		String eloan_p9_sig_2_custId = "";
		String eloan_p9_sig_2_addr = "";
		String eloan_p9_sig_partyB_agent = "";
		String eloan_p9_sig_partyB_addr = "";
		String eloan_p9_recpt_m_name = "";
		String eloan_p9_recpt_s_name = "";

		//******************************************************************************************
		// 依 額度、產品 決定 param_value
		//******************************************************************************************
		String caseMainId = c340m01a.getCaseMainId();
		L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);
		if(l120m01a!=null){
			//取得分行［電話、傳真］
			IBranch iBranch = branchService.getBranch(l120m01a.getCaseBrId());
			if(iBranch!=null){
				eloan_pc_serv_tel_t1 = Util.trim(iBranch.getTel());
				eloan_pc_serv_fax_t1 = Util.trim(iBranch.getFaxNo());
				
				String brnMgr = Util.trim(iBranch.getBrnMgr());				
				eloan_p9_sig_partyB_agent = Util.trim(iBranch.getNameABBR())+"分公司　經理　"+(brnMgr.length()>=5?userInfoService.getUserName(brnMgr):"");
				eloan_p9_sig_partyB_addr = Util.trim(iBranch.getAddr());
			}
		}
		if(true){
			List<L140M01R> l140m01r_list = clsService.findL140M01R_exclude_feeSrc3(caseMainId);
			if(l140m01r_list.size()>0){
				BigDecimal sum_feeNo01 = BigDecimal.ZERO;
				BigDecimal sum_feeNo02 = BigDecimal.ZERO;
				BigDecimal sum_feeNo03 = BigDecimal.ZERO;
				BigDecimal sum_feeNo04 = BigDecimal.ZERO;
				BigDecimal sum_feeNo06 = BigDecimal.ZERO;
				BigDecimal sum_feeNo07 = BigDecimal.ZERO;
				for(L140M01R l140m01r: l140m01r_list){
					String feeNo = l140m01r.getFeeNo();
					BigDecimal amt = l140m01r.getFeeAmt();
					
					if(Util.equals("01", feeNo)){
						sum_feeNo01 = sum_feeNo01.add(amt);
					}else if(Util.equals("02", feeNo)){
						sum_feeNo02 = sum_feeNo02.add(amt);
					}else if(Util.equals("03", feeNo)){
						sum_feeNo03 = sum_feeNo03.add(amt);
					}else if(Util.equals("04", feeNo)){
						sum_feeNo04 = sum_feeNo04.add(amt);
					}else if(Util.equals("06", feeNo)){
						sum_feeNo06 = sum_feeNo06.add(amt);
					}else if(Util.equals("07", feeNo)){
						sum_feeNo07 = sum_feeNo07.add(amt);
					}else{
						continue;
					}
				}
				eloan_pa_repay_feeNo01 = LMSUtil.pretty_numStr(sum_feeNo01);
				eloan_pa_repay_feeNo02 = LMSUtil.pretty_numStr(sum_feeNo02);
				eloan_pa_repay_feeNo03 = LMSUtil.pretty_numStr(sum_feeNo03);
				eloan_pa_repay_feeNo04 = LMSUtil.pretty_numStr(sum_feeNo04);
				eloan_pa_repay_feeNo06 = LMSUtil.pretty_numStr(sum_feeNo06);
				eloan_pa_repay_feeNo07 = LMSUtil.pretty_numStr(sum_feeNo07);
			}else{
				//若未輸入各項費用，不預設帶0
			}
		}
		
		if(c340m01b_map.size()==1){  // 包含1個額度2個產品{青安+房貸}的狀況
			L140M01A l140m01a = null;
			for (String l140m01a_mainId : c340m01b_map.keySet()) {
				l140m01a = c340m01b_map.get(l140m01a_mainId);
			}
			String tabMainId = l140m01a.getMainId();
			
			if(l140m01a!=null){
				//取得 主借人 資料
				String l140m01a_custName = Util.trim(l140m01a.getCustName());
				// eloan_p1_contr_cname = l140m01a_custName;
				eloan_p1_contr_name_m = l140m01a_custName;
				eloan_p9_sig_m_name = l140m01a_custName;
				eloan_p9_recpt_m_name = l140m01a_custName;
				//~~~~~~
				eloan_p9_sig_m_custId = l140m01a.getCustId();
				eloan_p9_sig_m_addr = get_SIG_ADDR(caseMainId, l140m01a.getCustId(), l140m01a.getDupNo()); 
			}
			//~~~~~~~
			// 額度層的資料
			TreeMap<String, List<L140S01A>> map_l140s01aData = new TreeMap<String, List<L140S01A>>();			
			if(true){ // 從債務人
				List<L140S01A> l140s01a_list = clsService.findL140S01A(l140m01a);
				LinkedHashMap<String, String> idDup_name_map_custPosC = new LinkedHashMap<String, String>();
				LinkedHashMap<String, String> idDup_name_map_custPosN = new LinkedHashMap<String, String>();
				LinkedHashMap<String, String> idDup_name_map_custPosG = new LinkedHashMap<String, String>(); 
				if(l140s01a_list!=null){
					for(L140S01A l140s01a : l140s01a_list){
						String custPos = Util.trim(l140s01a.getCustPos());
						if(!map_l140s01aData.containsKey(custPos)){
							map_l140s01aData.put(custPos, new ArrayList<L140S01A>());
						}
						map_l140s01aData.get(custPos).add(l140s01a);
						
						String idDup = LMSUtil.getCustKey_len10custId(l140s01a.getCustId(), l140s01a.getDupNo());
						String custName = Util.trim(l140s01a.getCustName());
						if(Util.equals(custPos, UtilConstants.lngeFlag.共同借款人)){ // custPos=C
							idDup_name_map_custPosC.put(idDup, custName);
						}else if(Util.equals(custPos, UtilConstants.lngeFlag.ㄧ般保證人)){ // custPos=N
							idDup_name_map_custPosN.put(idDup, custName);
						}else if(Util.equals(custPos, UtilConstants.lngeFlag.連帶保證人)){ // custPos=G
							idDup_name_map_custPosG.put(idDup, custName);
						}
					}
				}
				
				if(idDup_name_map_custPosC.size()>0){ // custPos=C
					String name_c = StringUtils.join(idDup_name_map_custPosC.values(), "、");
					//~~~~~~~
					eloan_p1_contr_name_m = eloan_p1_contr_name_m + "、"+ name_c;
					if(true){
						for(String idDup : idDup_name_map_custPosC.keySet()){
							String custPosC_name = idDup_name_map_custPosC.get(idDup);
							String custPosC_id = Util.trim(StringUtils.substring(idDup, 0, 10));
							String custPosC_dupNo = Util.trim(StringUtils.substring(idDup, 10));
							//~~
							eloan_p9_sig_m_name = eloan_p9_sig_m_name + "、"+ custPosC_name;
							eloan_p9_sig_m_custId = eloan_p9_sig_m_custId + "、"+ custPosC_id;
							eloan_p9_sig_m_addr = eloan_p9_sig_m_addr + "、"+ get_SIG_ADDR(caseMainId, custPosC_id, custPosC_dupNo); 
						}
						
					}
					eloan_p9_recpt_m_name = eloan_p9_recpt_m_name + "、"+ name_c;
				}
				if(idDup_name_map_custPosN.size()>0){ // custPos=N
					eloan_p1_contr_name_n = StringUtils.join(idDup_name_map_custPosN.values(), "、");
					eloan_pb_gnteeN_cb = CB_Y_STR;
				}
				if(idDup_name_map_custPosG.size()>0){ // custPos=G
					eloan_p1_contr_name_g = StringUtils.join(idDup_name_map_custPosG.values(), "、");
					eloan_pb_gnteeG_cb = CB_Y_STR;
				}
				
				LinkedHashMap<String, String> custPosG_N_map = new LinkedHashMap<String, String>();
				custPosG_N_map.putAll(idDup_name_map_custPosG);
				custPosG_N_map.putAll(idDup_name_map_custPosN);
				if(true){
					// 從債務人 簽收單
					eloan_p9_recpt_s_name = StringUtils.join(custPosG_N_map.values(), "、");
					int currentIdx = 0;
					for(String idDup : custPosG_N_map.keySet()){
						String custId = StringUtils.substring(idDup, 0, 10);
						String dupNo = StringUtils.substring(idDup, 10);
						// 從債務人 簽章欄
						if(currentIdx==0){
							// eloan_p9_sig_1N_cb = idDup_name_map_custPosN.containsKey(idDup)?CB_Y_STR:"";
							eloan_p9_sig_1N_name = Util.trim(idDup_name_map_custPosN.containsKey(idDup)?idDup_name_map_custPosN.get(idDup):"");
							// eloan_p9_sig_1G_cb = idDup_name_map_custPosG.containsKey(idDup)?CB_Y_STR:"";
							eloan_p9_sig_1G_name = Util.trim(idDup_name_map_custPosG.containsKey(idDup)?idDup_name_map_custPosG.get(idDup):"");
							eloan_p9_sig_1_custId = custId;
							eloan_p9_sig_1_addr = get_SIG_ADDR(caseMainId, custId, dupNo);
						}else if(currentIdx==1){
							// eloan_p9_sig_2N_cb =
							eloan_p9_sig_2N_name = Util.trim(idDup_name_map_custPosN.containsKey(idDup)?idDup_name_map_custPosN.get(idDup):"");
							// eloan_p9_sig_2G_cb = 
							eloan_p9_sig_2G_name = Util.trim(idDup_name_map_custPosG.containsKey(idDup)?idDup_name_map_custPosG.get(idDup):"");
							eloan_p9_sig_2_custId = custId;
							eloan_p9_sig_2_addr = get_SIG_ADDR(caseMainId, custId, dupNo);
							if(true){
								//目前的表格，從債務人只有2筆
								break;
							}
						}
						//~~~~~~
						++currentIdx;						
					}
				}
			}
			//~~~~~~~
			// 帳號層的資料{mainId + seq}
			List<L140S02A> l140s02a_list = get_l140s02a_list_exclude_property78_notHouse(l140m01a);
			
			TreeMap<Integer, L140S02C> map_l140s02cData = new TreeMap<Integer, L140S02C>();
			TreeMap<Integer, List<L140S02D>> map_l140s02dData = new TreeMap<Integer, List<L140S02D>>();
			TreeMap<Integer, L140S02E> map_l140s02eData = new TreeMap<Integer, L140S02E>();
			TreeMap<Integer, L140S02F> map_l140s02fData = new TreeMap<Integer, L140S02F>();			
			
			Set<Integer> noPPP_seq_set = new LinkedHashSet<Integer>();
			Set<Integer> withPPP_seq_set = new LinkedHashSet<Integer>();
			
			String distinct_cycl_cross_prod = "";
			//~~~~~~~
			for (L140S02A l140s02a : l140s02a_list) {
				Integer l140s02a_seq = l140s02a.getSeq();
				//==============================================				
				L140S02C l140s02c = clsService.findL140S02C(tabMainId, l140s02a_seq);
				List<L140S02D> l140s02d_list = clsService.findL140S02D_orderByPhase(tabMainId, l140s02a_seq, "Y");
				L140S02E l140s02e = clsService.findL140S02E(tabMainId, l140s02a_seq);
				L140S02F l140s02f = clsService.findL140S02F(tabMainId, l140s02a_seq);
				if(l140s02c!=null){
					map_l140s02cData.put(l140s02a_seq, l140s02c);	
				}
				if(l140s02d_list!=null){
					map_l140s02dData.put(l140s02a_seq, l140s02d_list);	
				}
				if (l140s02e != null) {
					map_l140s02eData.put(l140s02a_seq, l140s02e);	
				}
				if (l140s02f != null) {
					map_l140s02fData.put(l140s02a_seq, l140s02f);
					
					//==================================
					// 用 L140S02F.pConBeg1 來區分{無限制清償, 限制清償}
					// 例如：產品種類10, 但有輸入「提前還本管制設定」
					if(ContractDocUtil.belong_no_PPP(l140s02f)){ 
						noPPP_seq_set.add(l140s02a_seq);
					}else{
						withPPP_seq_set.add(l140s02a_seq);
					}
				}
			} //end-for (L140S02A l140s02a : l140s02a_list) {
			
			if(true){
				BigDecimal l140m01a_currentApplyAmt = l140m01a.getCurrentApplyAmt();
				BigDecimal sum_l140s02a_amt = BigDecimal.ZERO;
				for (L140S02A l140s02a : l140s02a_list) {
					sum_l140s02a_amt = sum_l140s02a_amt.add(l140s02a.getLoanAmt());
				}
				BigDecimal choseAmt = (sum_l140s02a_amt.compareTo(BigDecimal.ZERO)>0 && sum_l140s02a_amt.compareTo(l140m01a_currentApplyAmt)<0)?sum_l140s02a_amt:l140m01a_currentApplyAmt;
				//~~~~~	
				eloan_p1_contr_amt = CapCommonUtil.toChineseUpperAmount(LMSUtil.pretty_numStr(choseAmt), true);
				if(Util.equals("3", l140m01a.getUseDeadline())){ // 自簽約日起
					eloan_pa_use_A_cb = CB_Y_STR;
					eloan_pa_use_A_t1 = l140m01a.getDesp1();
					eloan_pa_use_A_t2 = eloan_p1_contr_amt; 
				}
			}
			if(true){
				String distinct_lnTotMonth = get_distinct_lnTotMonth_cross_prod(l140s02a_list);
				if(Util.isNotEmpty(distinct_lnTotMonth)){
					int i_lnTotMonth = Integer.parseInt(distinct_lnTotMonth);
					//~~~~
					eloan_pa_use_y = String.valueOf(i_lnTotMonth/12);
					eloan_pa_use_m = String.valueOf(i_lnTotMonth%12);				
				}
			}
			if(true){
				distinct_cycl_cross_prod = get_distinct_cycl_cross_prod(l140s02a_list, map_l140s02cData, map_l140s02eData);
				String distinct_rtType_cross_prod = get_distinct_rtType_cross_prod(l140s02a_list, map_l140s02cData, map_l140s02eData);				
				String distinct_extendCntSinceFirst_cross_prod = get_distinct_extendCntSinceFirst_cross_prod(l140s02a_list, map_l140s02cData, map_l140s02eData);
				_debug("cntrNo="+l140m01a.getCntrNo(), "{distinct_cycl:"+distinct_cycl_cross_prod
						+", distinct_rtType="+distinct_rtType_cross_prod
						+", distinct_extendCntSinceFirst="+distinct_extendCntSinceFirst_cross_prod
						+", noPPP_cnt="+noPPP_seq_set.size()
						+", withPPP_cnt="+withPPP_seq_set.size()
						+"}");
				if(Util.isNotEmpty(distinct_cycl_cross_prod) 
						&& Util.isNotEmpty(distinct_rtType_cross_prod) 
						&& Util.isNotEmpty(distinct_extendCntSinceFirst_cross_prod)){
					
					if(Util.equals("1", distinct_cycl_cross_prod)){
						//************************************
						//月繳
						if(Util.equals("2", distinct_rtType_cross_prod)){ //本息平均
							if(Util.equals(VAL_OTHER, distinct_extendCntSinceFirst_cross_prod)){
								
							}else if(Util.equals("0", distinct_extendCntSinceFirst_cross_prod)){ //無寬限期
								eloan_pa_repay_A_cb = CB_Y_STR;
							}else{ //有寬限期
								eloan_pa_repay_C_cb = CB_Y_STR;
								int extendCntSinceFirst_cross_prod = Util.parseInt(distinct_extendCntSinceFirst_cross_prod);
								String[] exend_info_arr = fmt_extendYear(extendCntSinceFirst_cross_prod);
								eloan_pa_repay_C_t1 = exend_info_arr[0];
								eloan_pa_repay_C_t2 = exend_info_arr[1];
								eloan_pa_repay_C_t3 = exend_info_arr[2];
								eloan_pa_repay_C_t4 = exend_info_arr[3];
							}
						}else if(Util.equals("3", distinct_rtType_cross_prod)){ //本金平均
							if(Util.equals(VAL_OTHER, distinct_extendCntSinceFirst_cross_prod)){
								
							}else if(Util.equals("0", distinct_extendCntSinceFirst_cross_prod)){ //無寬限期
								eloan_pa_repay_B_cb = CB_Y_STR;
							}else{ //有寬限期
								eloan_pa_repay_D_cb = CB_Y_STR;
								int extendCntSinceFirst_cross_prod = Util.parseInt(distinct_extendCntSinceFirst_cross_prod);
								String[] exend_info_arr = fmt_extendYear(extendCntSinceFirst_cross_prod);
								eloan_pa_repay_D_t1 = exend_info_arr[0];
								eloan_pa_repay_D_t2 = exend_info_arr[1];
								eloan_pa_repay_D_t3 = exend_info_arr[2];
								eloan_pa_repay_D_t4 = exend_info_arr[3];
							}
						}else{
							//期付金{月繳}的攤還方式，但(非本息平均、非本金平均)
						}
					}else if(Util.equals("2", distinct_cycl_cross_prod)){
						//************************************
						//雙週繳
						eloan_pa_repay_E_cb = CB_Y_STR;
						String distinct_intEndTermSinceFirst_cross_prod = get_distinct_intEndTermSinceFirst_cross_prod(l140s02a_list, map_l140s02cData, map_l140s02dData);
						if(Util.equals(VAL_OTHER, distinct_intEndTermSinceFirst_cross_prod)){
							
						}else{
							eloan_pa_repay_E_t1 = distinct_intEndTermSinceFirst_cross_prod;
						}						
					}else if(Util.equals(VAL_MONTH_INTEREST, distinct_cycl_cross_prod)){
						//{房貸}不應有{按月計息}
						eloan_pa_repay_F_cb = CB_Y_STR;
					}else{
						//************************************
						//其它繳款週期(L140S02E_payWayOth)
						eloan_pa_repay_F_cb = CB_Y_STR;
					}
				}else{
					//可能A) 2個產品, 都是 {本息均攤、月繳}, 但從 中間期數 開始申請「寬限期」
					//可能B) 2個產品, {1個是 本息均攤}{另1個是 本金均攤}}
				}
			}
			
			if(true){ //判斷是否有「限制清償期間」				
				if(noPPP_seq_set.size()>0){			
					if(true){
						eloan_pa_intr_noPPP_cb = CB_Y_STR;
					}
					LinkedHashMap<String, LinkedHashSet<String>> l140s02d_phase_map = get_distinct_l140s02d_intr_info(l140s02a_list, noPPP_seq_set, map_l140s02cData, map_l140s02dData);					
					boolean build_intr_text = true;
					String traceStr = "noPPP"
						+", is_mapValue_cnt_eq_1=" + is_mapValue_cnt_eq_1(l140s02d_phase_map)
						+", phase_size=" + l140s02d_phase_map.size()
						+")";	
					//~~~~~~
					_debug(traceStr, "distinct_cycl_cross_prod="+distinct_cycl_cross_prod);
					if(is_mapValue_cnt_eq_1(l140s02d_phase_map)){
						if(l140s02d_phase_map.size()==1){ //一段式利率
							String intr_info = "";
							for(String phase: l140s02d_phase_map.keySet()){ //在此段程式之前，已先限制 is_mapValue_cnt_eq_1==true
								intr_info = StringUtils.join(l140s02d_phase_map.get(phase), "");
							}
							_debug(traceStr, "intr_info="+intr_info);
							if(Util.isNotEmpty(intr_info)){
								String[] intr_info_arr = intr_info.split("\\|");
								String rateType = intr_info_arr[2];
								String baseRate = intr_info_arr[3];
								String pmFlag = intr_info_arr[4];
								String pmRate = intr_info_arr[5];
								String nowRate = intr_info_arr[6];
								String rateFlag = intr_info_arr[7];
								if(Util.equals(CrsUtil.RATE_TYPE_6R, rateType) && (Util.equals("", pmFlag)||Util.equals("P", pmFlag)) ){
									if(Util.equals("3", rateFlag) 
											&& Util.equals("1", intr_info_arr[8])
											&& Util.equals("1", intr_info_arr[9]) ){ //每月浮動(一段式)
										build_intr_text = false;
										if(!build_intr_text){
											eloan_pa_intr_noPPP_1t1 = Util.equals("P", pmFlag)?pmRate:"0";
											eloan_pa_intr_noPPP_1t2 = nowRate;	
											eloan_pa_intr_noPPP_baseRate = baseRate;
										}									
									}else if(Util.equals("1", rateFlag) ){ //固定(一段式)
										build_intr_text = false;
										if(!build_intr_text){
											eloan_pa_intr_noPPP_2t1 = Util.equals("P", pmFlag)?pmRate:"0";
											eloan_pa_intr_noPPP_baseRate = baseRate;
										}
									}								
								}
							}	
						}else{
							//多段式
							build_intr_text = true;
						}	
					}else{
						build_intr_text = true;
					}	
					
					if(!build_intr_text){
						//已填入組字的欄位
					}else{					
						eloan_pa_intr_noPPP_3t1 = build_intr_textStr(l140s02a_list, noPPP_seq_set, map_l140s02dData, distinct_cycl_cross_prod);
					}			
				}
				if(withPPP_seq_set.size()>0){		
					if(true){
						eloan_pa_intr_withPPP_cb = CB_Y_STR;
					}				
					LinkedHashMap<String, LinkedHashSet<String>> l140s02d_phase_map = get_distinct_l140s02d_intr_info(l140s02a_list, withPPP_seq_set, map_l140s02cData, map_l140s02dData);					
					boolean build_intr_text = true;
					String traceStr = "withPPP"
						+", is_mapValue_cnt_eq_1=" + is_mapValue_cnt_eq_1(l140s02d_phase_map)
						+", phase_size=" + l140s02d_phase_map.size()
						+")";
					//~~~~~~
					_debug(traceStr, "distinct_cycl_cross_prod="+distinct_cycl_cross_prod);					
					if(is_mapValue_cnt_eq_1(l140s02d_phase_map) 
							&& Util.equals("1", distinct_cycl_cross_prod)  ){ //月繳
						if(l140s02d_phase_map.size()==1){ //一段式利率
							String intr_info = "";
							for(String phase: l140s02d_phase_map.keySet()){ //在此段程式之前，已先限制 is_mapValue_cnt_eq_1==true
								intr_info = StringUtils.join(l140s02d_phase_map.get(phase), "");
							}
							_debug(traceStr, "intr_info="+intr_info);
							if(Util.isNotEmpty(intr_info)){
								String[] intr_info_arr = intr_info.split("\\|");
								String bgnNum = intr_info_arr[0];
								String endNum = intr_info_arr[1];								
								String rateType = intr_info_arr[2];
								String baseRate = intr_info_arr[3];
								String pmFlag = intr_info_arr[4];
								String pmRate = intr_info_arr[5];								
								String nowRate = intr_info_arr[6];
								String rateFlag = intr_info_arr[7];
								if(Util.equals("1", rateFlag)){ //固定(一段式)
									build_intr_text = false;
									if(!build_intr_text){
										eloan_pa_intr_withPPP_1x1 = bgnNum;
										eloan_pa_intr_withPPP_1x2 = endNum;
										eloan_pa_intr_withPPP_1x3 = nowRate;
									}
								}else if(Util.equals(CrsUtil.RATE_TYPE_6R, rateType) && (Util.equals("", pmFlag)||Util.equals("P", pmFlag)) ){
									if(Util.equals("3", rateFlag) 
											&& Util.equals("1", intr_info_arr[8])
											&& Util.equals("1", intr_info_arr[9]) ){ //每月浮動(一段式)
										build_intr_text = false;
										if(!build_intr_text){
											eloan_pa_intr_withPPP_1y1 = bgnNum;
											eloan_pa_intr_withPPP_1y2 = endNum;
											eloan_pa_intr_withPPP_1y3 = Util.equals("P", pmFlag)?pmRate:"0";
											eloan_pa_intr_withPPP_1y4 = nowRate;	
											
											if(true){
												eloan_pa_intr_withPPP_baseRate = baseRate;
											}
										}									
									}
								}
							}
						}
						if(l140s02d_phase_map.size()==2){//二段式利率
							String[] intr_info_list = new String[]{"", ""};
							int currentIdx = 0;
							for(String phase: l140s02d_phase_map.keySet()){ //在此段程式之前，已先限制 is_mapValue_cnt_eq_1==true
								intr_info_list[currentIdx] = StringUtils.join(l140s02d_phase_map.get(phase), "");
								++currentIdx;
								if(currentIdx>=2){
									break;
								}
							}
							_debug(traceStr, "intr_info_1st="+intr_info_list[0]);
							_debug(traceStr, "intr_info_2nd="+intr_info_list[1]);
							if(Util.isNotEmpty(intr_info_list[0]) && Util.isNotEmpty(intr_info_list[1])){
								String[] intr_info_1st_arr = intr_info_list[0].split("\\|");
								String[] intr_info_2nd_arr = intr_info_list[1].split("\\|");
								String bgnNum_1st = intr_info_1st_arr[0];
								String endNum_1st = intr_info_1st_arr[1];								
								String rateType_1st = intr_info_1st_arr[2];
								String baseRate_1st = intr_info_1st_arr[3];
								String pmFlag_1st = intr_info_1st_arr[4];
								String pmRate_1st = intr_info_1st_arr[5];
								String nowRate_1st = intr_info_1st_arr[6];
								String rateFlag_1st = intr_info_1st_arr[7];
								//~~~~~~
								String bgnNum_2nd = intr_info_2nd_arr[0];
								String endNum_2nd = intr_info_2nd_arr[1];								
								String rateType_2nd = intr_info_2nd_arr[2];
								String baseRate_2nd = intr_info_2nd_arr[3];
								String pmFlag_2nd = intr_info_2nd_arr[4];
								String pmRate_2nd = intr_info_2nd_arr[5];
								String nowRate_2nd = intr_info_2nd_arr[6];
								String rateFlag_2nd = intr_info_2nd_arr[7];
								//~~~~~~
								if(Util.equals(CrsUtil.RATE_TYPE_6R, rateType_1st) && (Util.equals("", pmFlag_1st)||Util.equals("P", pmFlag_1st)) 
									&& Util.equals(CrsUtil.RATE_TYPE_6R, rateType_2nd) && (Util.equals("", pmFlag_2nd)||Util.equals("P", pmFlag_2nd)) ){
									if(Util.equals("3", rateFlag_1st) 
											&& Util.equals("1", intr_info_1st_arr[8])
											&& Util.equals("1", intr_info_1st_arr[9]) 
										&& Util.equals("3", rateFlag_2nd) 
											&& Util.equals("1", intr_info_2nd_arr[8])
											&& Util.equals("1", intr_info_2nd_arr[9]) ){ //每月浮動(二段式)
										build_intr_text = false;
										if(!build_intr_text){
											eloan_pa_intr_withPPP_1y1 = bgnNum_1st;
											eloan_pa_intr_withPPP_1y2 = endNum_1st;
											eloan_pa_intr_withPPP_1y3 = Util.equals("P", pmFlag_1st)?pmRate_1st:"0";
											eloan_pa_intr_withPPP_1y4 = nowRate_1st;
											
											eloan_pa_intr_withPPP_1y5 = bgnNum_2nd;
											eloan_pa_intr_withPPP_1y6 = endNum_2nd;
											eloan_pa_intr_withPPP_1y7 = Util.equals("P", pmFlag_2nd)?pmRate_2nd:"0";
											eloan_pa_intr_withPPP_1y8 = nowRate_2nd;
											
											if(Util.equals(baseRate_1st, baseRate_2nd)){
												eloan_pa_intr_withPPP_baseRate = baseRate_1st;
											}
										}									
									}else{
										//phase1及 phase2 有任一為［6R定期浮動］以外
									}
								}
							}							
						}
					}else{
						build_intr_text = true;
					}	
					
					if(!build_intr_text){
						//已填入組字的欄位
					}else{
						eloan_pa_intr_withPPP_2t1 = build_intr_textStr(l140s02a_list, withPPP_seq_set, map_l140s02dData, distinct_cycl_cross_prod);
					}
				}
			}
			
			if(true){
				String distinct_L140S02A_lnPurpose = get_distinct_L140S02A_lnPurpose_cross_prod(l140s02a_list);
				String traceStr = "[cntrNo="+l140m01a.getCntrNo()+"]GorN";
				_debug(traceStr, "{revolve="+(Util.equals(UtilConstants.Cntrdoc.ReUse.循環使用, l140m01a.getReUse())?"Y":"N")+"}"
						+"{eloan_pb_gnteeG_cb="+eloan_pb_gnteeG_cb+", eloan_pb_gnteeN_cb="+eloan_pb_gnteeN_cb+"}"
						+"{distinct_L140S02A_lnPurpose="+distinct_L140S02A_lnPurpose+"}"
					);
				if(Util.equals(eloan_pb_gnteeG_cb, CB_Y_STR)){					
					if(Util.equals("M", distinct_L140S02A_lnPurpose)){
						// 銀行法 第 12-1 條：銀行辦理自用住宅放款及消費性放款，不得要求借款人提供連帶保證人。 
					}else if(Util.equals("L", distinct_L140S02A_lnPurpose)||Util.equals("2", distinct_L140S02A_lnPurpose)){ //購置住宅貸款（非自用、其他）
						if(Util.equals(UtilConstants.Cntrdoc.ReUse.不循環使用, l140m01a.getReUse())){ //1:不循環使用
							eloan_pb_gnteeG_B_cb = CB_Y_STR;
						}
					}
				}
				if(Util.equals(eloan_pb_gnteeN_cb, CB_Y_STR)){
					if(Util.equals("M", distinct_L140S02A_lnPurpose)){
						if(Util.equals(UtilConstants.Cntrdoc.ReUse.不循環使用, l140m01a.getReUse())){ //1:不循環使用
							eloan_pb_gnteeN_A_cb = CB_Y_STR;
						}
					}else if(Util.equals("L", distinct_L140S02A_lnPurpose)||Util.equals("2", distinct_L140S02A_lnPurpose)){
						eloan_pb_gnteeN_B_cb = CB_Y_STR;
					}
				}
			}
		}else{
			/* 在1份契約書，包含多個額度序號
			 * 可能1)額度A 以 丙先生 當一般保證人，額度B 以 丁小姐 當 連保人 
			*/
			L140M01A oneOfL140M01A = null;
			for (String l140m01a_mainId : c340m01b_map.keySet()) {
				L140M01A l140m01a = c340m01b_map.get(l140m01a_mainId);
				oneOfL140M01A = l140m01a;
			}			
			
			if(oneOfL140M01A!=null){
				String l140m01a_custName = Util.trim(oneOfL140M01A.getCustName());
				// eloan_p1_contr_cname = l140m01a_custName;
				eloan_p1_contr_name_m = l140m01a_custName;
				eloan_p9_sig_m_name = l140m01a_custName;
				eloan_p9_recpt_m_name = l140m01a_custName;
				//~~~~~~
				eloan_p9_sig_m_custId = oneOfL140M01A.getCustId();
				eloan_p9_sig_m_addr = get_SIG_ADDR(caseMainId, oneOfL140M01A.getCustId(), oneOfL140M01A.getDupNo()); 
			}
		}
		//******************************************************************************************
		// 填入 map
		//******************************************************************************************
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();

		//J-110-0486_10702_B1001 Web e-Loan契約書總費用年百分率以系統計算帶入以降低錯誤率
		eloan_pc_spTerm_yRate = this.getRate(c340m01a);

		if(true){
			if (true) {
				// _injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P1_CONTR_NO, eloan_p1_contr_no);
				// _injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P1_CONTR_CNAME, eloan_p1_contr_cname);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P1_CONTR_NAME_M, eloan_p1_contr_name_m);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P1_CONTR_NAME_N, eloan_p1_contr_name_n);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P1_CONTR_NAME_G, eloan_p1_contr_name_g);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P1_CONTR_AMT, eloan_p1_contr_amt);
			}
			//=====================================
			// 貸款金額及交付方式
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_A_CB, eloan_pa_deliv_A_cb);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_A_T1, eloan_pa_deliv_A_t1);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_A_T2, eloan_pa_deliv_A_t2);
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_B_CB, eloan_pa_deliv_B_cb);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_B_T1, eloan_pa_deliv_B_t1);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_B_T2, eloan_pa_deliv_B_t2);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_B_T3, eloan_pa_deliv_B_t3);
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_C_CB, eloan_pa_deliv_C_cb);
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D_CB, eloan_pa_deliv_D_cb);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D_T1, eloan_pa_deliv_D_t1);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D_T2, eloan_pa_deliv_D_t2);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T1, eloan_pa_deliv_D1_t1);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T1A , eloan_pa_deliv_D1_t1A);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T1B , eloan_pa_deliv_D1_t1B);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T2  , eloan_pa_deliv_D1_t2);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T3  , eloan_pa_deliv_D1_t3);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T4  , eloan_pa_deliv_D1_t4);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T5  , eloan_pa_deliv_D1_t5);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T6  , eloan_pa_deliv_D1_t6);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D1_T7  , eloan_pa_deliv_D1_t7);
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_E_CB, eloan_pa_deliv_E_cb);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_E_T1, eloan_pa_deliv_E_t1);
			}
			//=====================================
			// 申請動用方式、動用期限及貸款期間
			if (true) {
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_A_CB, eloan_pa_use_A_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_A_T1, eloan_pa_use_A_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_A_T2, eloan_pa_use_A_t2);
				}
				if (true) { //個別約定
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_B_CB, eloan_pa_use_B_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_B_T1, eloan_pa_use_B_t1);
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_Y, eloan_pa_use_y);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_M, eloan_pa_use_m);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_BEGY, eloan_pa_use_begY);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_BEGM, eloan_pa_use_begM);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_BEGD, eloan_pa_use_begD);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_ENDY, eloan_pa_use_endY);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_ENDM, eloan_pa_use_endM);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_ENDD, eloan_pa_use_endD);
				}
			}
			//=====================================
			// 貸款本息攤還方式之約定 L140S02E.payWay
			if (true) {
				if (true) { //(無寬限期)攤還本息
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_A_CB, eloan_pa_repay_A_cb);
				}
				if (true) { //(無寬限期)攤還本金
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_B_CB, eloan_pa_repay_B_cb);
				}
				if (true) { //寬限期 + 攤還本息
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_CB, eloan_pa_repay_C_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_T1, eloan_pa_repay_C_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_T2, eloan_pa_repay_C_t2);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_T3, eloan_pa_repay_C_t3);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_T4, eloan_pa_repay_C_t4);
				}
				if (true) { //寬限期 + 攤還本金
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_CB, eloan_pa_repay_D_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_T1, eloan_pa_repay_D_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_T2, eloan_pa_repay_D_t2);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_T3, eloan_pa_repay_D_t3);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_T4, eloan_pa_repay_D_t4);
				}
				if (true) { //雙週繳
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_E_CB, eloan_pa_repay_E_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_E_T1, eloan_pa_repay_E_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_E_T2, eloan_pa_repay_E_t2);
				}
				if (true) { //其他方式 
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_F_CB, eloan_pa_repay_F_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_F_T1, eloan_pa_repay_F_t1);
				}
				if (true) {
					//由客戶自行勾選
					// _injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_NOPPP_CB, eloan_pa_repay_noPPP_cb); //prepayment penalty 
					// _injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_WITHPPP_CB, eloan_pa_repay_withPPP_cb);
				}
				
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_ACTNO, eloan_pa_repay_actNo);
				if (true) { 
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO01, eloan_pa_repay_feeNo01); //where codetype='cls1141_feeNo' 01	開辦費
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO02, eloan_pa_repay_feeNo02); //where codetype='cls1141_feeNo' 02	信用查詢費
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO03, eloan_pa_repay_feeNo03); //where codetype='cls1141_feeNo' 03	短期續約作業費
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO04, eloan_pa_repay_feeNo04); //where codetype='cls1141_feeNo' 04	變更授信條件手續費
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO06, eloan_pa_repay_feeNo06); //where codetype='cls1141_feeNo' 06	貸款餘額證明書
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO07, eloan_pa_repay_feeNo07); //where codetype='cls1141_feeNo' 07	補發抵押權塗銷同意書
				}								
			}
			//=====================================
			// 貸款計息方式之約定
			if (true) {
				if (true) { //無限制清償期間
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_CB, eloan_pa_intr_noPPP_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_BASERATE, eloan_pa_intr_noPPP_baseRate);
					// _injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_WAY, eloan_pa_intr_noPPP_way);
					if(true){ //浮動計息
						_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_1T1, eloan_pa_intr_noPPP_1t1);
						_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_1T2, eloan_pa_intr_noPPP_1t2);
					}
					if(true){ //固定利率
						_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_2T1, eloan_pa_intr_noPPP_2t1);
					}
					if(true){ //無限制清償期間_其他
						_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_3T1, eloan_pa_intr_noPPP_3t1);
					}
				}
				
				if(true){ //限制提前清償
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_CB, eloan_pa_intr_withPPP_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_BASERATE, eloan_pa_intr_withPPP_baseRate);
					// _injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_WAY, eloan_pa_intr_withPPP_way);
					if(true){
						if(true){ //固定計息
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1X1, eloan_pa_intr_withPPP_1x1);
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1X2, eloan_pa_intr_withPPP_1x2);
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1X3, eloan_pa_intr_withPPP_1x3);
						}
						if(true){ //浮動計息PartA
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y1, eloan_pa_intr_withPPP_1y1);
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y2, eloan_pa_intr_withPPP_1y2);
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y3, eloan_pa_intr_withPPP_1y3);
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y4, eloan_pa_intr_withPPP_1y4);
						}
						if(true){ //浮動計息PartB
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y5, eloan_pa_intr_withPPP_1y5);
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y6, eloan_pa_intr_withPPP_1y6);
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y7, eloan_pa_intr_withPPP_1y7);
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y8, eloan_pa_intr_withPPP_1y8);
						}
					}
					if(true){ //限制提前清償_其他
						_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_2T1, eloan_pa_intr_withPPP_2t1);
					}
					
				}
				if(true){ //個別約定
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_OTHER_CB, on_when_has_data(eloan_pa_intr_other_t1, CB_Y_STR));
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_OTHER_T1, eloan_pa_intr_other_t1);
				}
			}
			if (true) { //利率調整之通知 => 未約定者，以書面方式為之
				// _injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_notice_1_cb", "");
				// _injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_notice_2_cb", "");
				// _injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_notice_3_cb", "");
			}
			//=====================================
			// 保證條款
			if (true) {
				if (true) { // 連帶保證人保證條款(甲方非屬銀行法第12條之1之貸款對象)
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_CB, eloan_pb_gnteeG_cb);
					//銀行辦理自用住宅放款及消費性放款，不得要求借款人提供連帶保證人。
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_B_CB, eloan_pb_gnteeG_B_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_B_T1, eloan_pb_gnteeG_B_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_C_CB, eloan_pb_gnteeG_C_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_C_T1, eloan_pb_gnteeG_C_t1);
				}
				if (true) { // 一般保證人保證條款
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_CB, eloan_pb_gnteeN_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_A_CB, eloan_pb_gnteeN_A_cb);//自用住宅放款（不循環動用）
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_B_CB, eloan_pb_gnteeN_B_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_B_T1, eloan_pb_gnteeN_B_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_C_CB, eloan_pb_gnteeN_C_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_C_T1, eloan_pb_gnteeN_C_t1);
				}
			}

			//=====================================			
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SERV_TEL_T1, eloan_pc_serv_tel_t1);				
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SERV_FAX_T1, eloan_pc_serv_fax_t1);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SERV_MAIL_T1, eloan_pc_serv_mail_t1);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SERV_OTHER_T1, eloan_pc_serv_other_t1);
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_COURT_LOC, eloan_pc_court_loc);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_COPY_CNT, eloan_pc_copy_cnt);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SPTERM_YRATE, eloan_pc_spTerm_yRate);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SPTERM_NOTE, eloan_pc_spTerm_note);
			}
			
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_M_NAME, eloan_p9_sig_m_name);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_M_CUSTID, eloan_p9_sig_m_custId);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_M_ADDR, eloan_p9_sig_m_addr);
				
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_1N_CB, on_when_has_data(eloan_p9_sig_1N_name, CB_Y_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_1N_NAME, eloan_p9_sig_1N_name);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_1G_CB, on_when_has_data(eloan_p9_sig_1G_name, CB_Y_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_1G_NAME, eloan_p9_sig_1G_name);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_1_CUSTID, eloan_p9_sig_1_custId);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_1_ADDR, eloan_p9_sig_1_addr);

				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_2N_CB, on_when_has_data(eloan_p9_sig_2N_name, CB_Y_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_2N_NAME, eloan_p9_sig_2N_name);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_2G_CB, on_when_has_data(eloan_p9_sig_2G_name, CB_Y_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_2G_NAME, eloan_p9_sig_2G_name);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_2_CUSTID, eloan_p9_sig_2_custId);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_2_ADDR, eloan_p9_sig_2_addr);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_PARTYB_AGENT, eloan_p9_sig_partyB_agent);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_PARTYB_ADDR, eloan_p9_sig_partyB_addr);
			}
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_RECPT_M_NAME, eloan_p9_recpt_m_name);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_RECPT_S_NAME, eloan_p9_recpt_s_name);
			}		
		}
		return paramMap;
	}
	
	private LinkedHashMap<String, String> init_paramMap_ctrType2_V202009(C340M01A c340m01a, Map<String, L140M01A> c340m01b_map)
	throws CapException{
//		String eloan_p1_contr_no = "";
//		String eloan_p1_contr_cname = "";
		String eloan_p1_contr_name_m = "";
		String eloan_p1_contr_name_n = "";
		String eloan_p1_contr_name_g = "";
		String eloan_p1_contr_amt = "";
		String eloan_pa_deliv_A_t1 = "活期儲蓄";
				
		String eloan_pa_purpose_way = "";
		String eloan_pa_purpose_E_t1 = "";
		
		String eloan_pa_use_A_t1 = "";
		String eloan_pa_use_A_t2 = "";
		String eloan_pa_use_A_t3 = "";
		
		String eloan_pa_use_B_t7 = "";
		String eloan_pa_use_B_t8 = "";
		
		String eloan_pa_use_C_t7 = "";
		String eloan_pa_use_C_t8 = "";
		
		String eloan_pa_use_D_t7 = "";
		String eloan_pa_use_D_t8 = "";
		
		String eloan_pa_repay_way = "";
		String eloan_pa_repay_C_t1 = "";
		String eloan_pa_repay_C_t2 = "";
		String eloan_pa_repay_C_t3 = "";
		String eloan_pa_repay_C_t4 = "";

//		String eloan_pa_repay_noPPP_cb = "";
//		String eloan_pa_repay_withPPP_cb = "";		
//		String eloan_pa_repay_withPPP_term = "";
//		String eloan_pa_repay_actNo = "";
		String eloan_pa_repay_feeNo01 = "";
		String eloan_pa_repay_feeNo02 = "";
		String eloan_pa_repay_feeNo03 = "";
		String eloan_pa_repay_feeNo04 = "";
		String eloan_pa_repay_feeNo06 = "";
		// 信貸 無 補發費用    String eloan_pa_repay_feeNo07 = "";

		String eloan_pa_intr_withPPP_cb = "";
		String eloan_pa_intr_withPPP_baseRate = "";
//		String eloan_pa_intr_withPPP_way = "";
		String eloan_pa_intr_withPPP_1x1 = "";
		String eloan_pa_intr_withPPP_1x2 = "";
		String eloan_pa_intr_withPPP_1x3 = "";
		String eloan_pa_intr_withPPP_1y1 = "";
		String eloan_pa_intr_withPPP_1y2 = "";
		String eloan_pa_intr_withPPP_1y3 = "";
//		String eloan_pa_intr_withPPP_1y4 = "";
		String eloan_pa_intr_withPPP_1y5 = "";
		String eloan_pa_intr_withPPP_1y6 = "";
		String eloan_pa_intr_withPPP_1y7 = "";
//		String eloan_pa_intr_withPPP_1y8 = "";
		String eloan_pa_intr_withPPP_1y9 = "";
		String eloan_pa_intr_withPPP_1y10 = "";
		String eloan_pa_intr_withPPP_1y11 = "";
//		String eloan_pa_intr_withPPP_1y12 = "";
		String eloan_pa_intr_withPPP_2t1 = "";
		
		String eloan_pa_intr_noPPP_cb = "";
		String eloan_pa_intr_noPPP_baseRate = "";
//		String eloan_pa_intr_noPPP_way = "";
		String eloan_pa_intr_noPPP_1t1 = "";
		String eloan_pa_intr_noPPP_2t1 = "";
		String eloan_pa_intr_noPPP_3t1 = "";
		String eloan_pa_intr_noPPP_4t1 = "";
		String eloan_pa_intr_noPPP_4t2 = "";
		String eloan_pa_intr_noPPP_5t1 = "";
		
//		String eloan_pa_intr_other_cb = "";
		String eloan_pa_intr_other_t1 = "";
		
		String eloan_pb_gnteeG_cb = "";
		String eloan_pb_gnteeG_E_cb = "";
		String eloan_pb_gnteeG_E_t1 = "";
		String eloan_pb_gnteeG_D_cb = "";
		String eloan_pb_gnteeG_D_t1 = "";
	
		String eloan_pb_gnteeN_cb = "";		
		String eloan_pb_gnteeN_F_cb = "";
		String eloan_pb_gnteeN_F_t1 = "";
		String eloan_pb_gnteeN_G_cb = "";
		String eloan_pb_gnteeN_G_t1 = "";
		
//		String eloan_pc_serv_tel_cb = "";
		String eloan_pc_serv_tel_t1 = "";
//		String eloan_pc_serv_fax_cb = "";
		String eloan_pc_serv_fax_t1 = "";
//		String eloan_pc_serv_mail_cb = "";
		String eloan_pc_serv_mail_t1 = "";		
//		String eloan_pc_serv_other_cb = "";
		String eloan_pc_serv_other_t1 = "";
		String eloan_pc_court_loc = "";
		String eloan_pc_copy_cnt = "";
		String eloan_pc_spTerm_yRate = "";
		String eloan_pc_spTerm_note = "";
		
		String eloan_p9_sig_m_name = "";
		String eloan_p9_sig_m_custId = "";
		String eloan_p9_sig_m_addr = "";
//		String eloan_p9_sig_1N_cb = "";
		String eloan_p9_sig_1N_name = "";
//		String eloan_p9_sig_1G_cb = "";
		String eloan_p9_sig_1G_name = "";
		String eloan_p9_sig_1_custId = "";
		String eloan_p9_sig_1_addr = "";
//		String eloan_p9_sig_2N_cb = "";
		String eloan_p9_sig_2N_name = "";
//		String eloan_p9_sig_2G_cb = "";
		String eloan_p9_sig_2G_name = "";
		String eloan_p9_sig_2_custId = "";
		String eloan_p9_sig_2_addr = "";
		String eloan_p9_sig_partyB_agent = "";
		String eloan_p9_sig_partyB_addr = "";
		String eloan_p9_recpt_m_name = "";
		String eloan_p9_recpt_s_name = "";

		//******************************************************************************************
		// 依 額度、產品 決定 param_value
		//******************************************************************************************
		String caseMainId = c340m01a.getCaseMainId();
		L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);
		if(l120m01a!=null){
			//取得分行［電話、傳真］
			IBranch iBranch = branchService.getBranch(l120m01a.getCaseBrId());
			if(iBranch!=null){
				eloan_pc_serv_tel_t1 = Util.trim(iBranch.getTel());
				eloan_pc_serv_fax_t1 = Util.trim(iBranch.getFaxNo());
				
				String brnMgr = Util.trim(iBranch.getBrnMgr());				
				eloan_p9_sig_partyB_agent = Util.trim(iBranch.getNameABBR())+"分公司　經理　"+(brnMgr.length()>=5?userInfoService.getUserName(brnMgr):"");
				eloan_p9_sig_partyB_addr = Util.trim(iBranch.getAddr());
			}
		}
		if(true){
			List<L140M01R> l140m01r_list = clsService.findL140M01R_exclude_feeSrc3(caseMainId);
			if(l140m01r_list.size()>0){
				BigDecimal sum_feeNo01 = BigDecimal.ZERO;
				BigDecimal sum_feeNo02 = BigDecimal.ZERO;
				BigDecimal sum_feeNo03 = BigDecimal.ZERO;
				BigDecimal sum_feeNo04 = BigDecimal.ZERO;
				BigDecimal sum_feeNo06 = BigDecimal.ZERO;
				BigDecimal sum_feeNo07 = BigDecimal.ZERO;
				for(L140M01R l140m01r: l140m01r_list){
					String feeNo = l140m01r.getFeeNo();
					BigDecimal amt = l140m01r.getFeeAmt();
					
					if(Util.equals("01", feeNo)){
						sum_feeNo01 = sum_feeNo01.add(amt);
					}else if(Util.equals("02", feeNo)){
						sum_feeNo02 = sum_feeNo02.add(amt);
					}else if(Util.equals("03", feeNo)){
						sum_feeNo03 = sum_feeNo03.add(amt);
					}else if(Util.equals("04", feeNo)){
						sum_feeNo04 = sum_feeNo04.add(amt);
					}else if(Util.equals("06", feeNo)){
						sum_feeNo06 = sum_feeNo06.add(amt);
					}else if(Util.equals("07", feeNo)){
						sum_feeNo07 = sum_feeNo07.add(amt);
					}else{
						continue;
					}
				}
				eloan_pa_repay_feeNo01 = LMSUtil.pretty_numStr(sum_feeNo01);
				eloan_pa_repay_feeNo02 = LMSUtil.pretty_numStr(sum_feeNo02);
				eloan_pa_repay_feeNo03 = LMSUtil.pretty_numStr(sum_feeNo03);
				eloan_pa_repay_feeNo04 = LMSUtil.pretty_numStr(sum_feeNo04);
				eloan_pa_repay_feeNo06 = LMSUtil.pretty_numStr(sum_feeNo06);
				// eloan_pa_repay_feeNo07 = LMSUtil.pretty_numStr(sum_feeNo07);
			}else{
				//若未輸入各項費用，不預設帶0
			}
		}
		
		if(true){
			/*
			本借款用途如下列第 款之記載
			一、個人或家庭理財。
			二、修繕房屋。
			三、繳付甲方或甲方子女學費。
			四、購置耐久性消費財。
			五、其他：
			*/
			boolean has_ctrType2_ctrPurpose1 = false;
			boolean has_ctrType2_ctrPurpose2 = false;
			boolean has_ctrType2_ctrPurpose3 = false;
			boolean has_ctrType2_ctrPurpose4 = false;
			boolean has_ctrType2_cls1141_purpose_Other = false;
			LinkedHashSet<String> ctrType2_ctrPurpose5_list = new LinkedHashSet<String>();
			/*			 
			3	其他
			A	購置住宅
			B	修繕房屋
			C	週轉(含行家理財中長期)
			D	購置汽車
			E	購置停車位貸款
			F	出國留學
			G	投資理財
			H	購置自用耐久性消費品
			I	出國旅遊
			J	子女教育
			K	繳稅
			L	青年創業貸款
			M	生活安養所需資金
			*/
			Map<String, String> cls1141_purpose = clsService.get_codeTypeWithOrder("cls1141_purpose");
			for (String purpose : Util.trim(l120m01a.getPurpose()).split(UtilConstants.Mark.SPILT_MARK)) {
				if (Util.isEmpty(purpose)) {
					continue;
				}
				// ---
				if (Util.equals(UtilConstants.Casedoc.purpose.其他, purpose)){
					has_ctrType2_cls1141_purpose_Other = true;
				}else if (Util.equals(UtilConstants.Casedoc.purpose.購置住宅, purpose)){
					ctrType2_ctrPurpose5_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else if (Util.equals(UtilConstants.Casedoc.purpose.修繕房屋, purpose)){
					has_ctrType2_ctrPurpose2 = true;
				}else if (Util.equals(UtilConstants.Casedoc.purpose.週轉_含行家理財中長期, purpose)){
					ctrType2_ctrPurpose5_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else if (Util.equals(UtilConstants.Casedoc.purpose.購置汽車, purpose)){
					ctrType2_ctrPurpose5_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else if (Util.equals(UtilConstants.Casedoc.purpose.購置停車位貸款, purpose)){
					ctrType2_ctrPurpose5_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else if (Util.equals(UtilConstants.Casedoc.purpose.出國留學, purpose)){
					ctrType2_ctrPurpose5_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else if (Util.equals(UtilConstants.Casedoc.purpose.投資理財, purpose)){
					has_ctrType2_ctrPurpose1 = true;
				}else if (Util.equals(UtilConstants.Casedoc.purpose.購置自用耐久性消費品, purpose)){
					has_ctrType2_ctrPurpose4 = true;
				}else if (Util.equals(UtilConstants.Casedoc.purpose.出國旅遊, purpose)){
					ctrType2_ctrPurpose5_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else if (Util.equals(UtilConstants.Casedoc.purpose.子女教育, purpose)){
					has_ctrType2_ctrPurpose3 = true;
				}else if (Util.equals(UtilConstants.Casedoc.purpose.繳稅, purpose)){
					ctrType2_ctrPurpose5_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else if (Util.equals(UtilConstants.Casedoc.purpose.青年創業貸款, purpose)){
					ctrType2_ctrPurpose5_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else if (Util.equals(UtilConstants.Casedoc.purpose.生活安養所需資金, purpose)){ 
					ctrType2_ctrPurpose5_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else {
					ctrType2_ctrPurpose5_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}
			}
			if(has_ctrType2_cls1141_purpose_Other){ //將「其它」置於最後
				ctrType2_ctrPurpose5_list.add(Util.trim(l120m01a.getPurposeOth()));	
			}

			if(true){
				boolean has_ctrType2_ctrPurpose5 = ctrType2_ctrPurpose5_list.size()>0;
				LinkedHashMap<String, Boolean> ctrPurposeMap = new LinkedHashMap<String, Boolean>();
				ctrPurposeMap.put("一", has_ctrType2_ctrPurpose1);
				ctrPurposeMap.put("二", has_ctrType2_ctrPurpose2);
				ctrPurposeMap.put("三", has_ctrType2_ctrPurpose3);
				ctrPurposeMap.put("四", has_ctrType2_ctrPurpose4);
				ctrPurposeMap.put("五", has_ctrType2_ctrPurpose5);
				eloan_pa_purpose_way = build_choseKey_by_booleanVal(ctrPurposeMap, "、");
			}
			eloan_pa_purpose_E_t1 = StringUtils.join(ctrType2_ctrPurpose5_list, "、");
		}
		
		if(c340m01b_map.size()==1){  // 包含1個額度2個產品{有擔、無擔}的狀況，在信用貸款的契約書，應只要抓 {無擔}的資料
			L140M01A l140m01a = null;
			for (String l140m01a_mainId : c340m01b_map.keySet()) {
				l140m01a = c340m01b_map.get(l140m01a_mainId);
			}
			String tabMainId = l140m01a.getMainId();
			
			if(l140m01a!=null){
				//取得 主借人 資料
				String l140m01a_custName = Util.trim(l140m01a.getCustName());
				// eloan_p1_contr_cname = l140m01a_custName;
				eloan_p1_contr_name_m = l140m01a_custName;
				eloan_p9_sig_m_name = l140m01a_custName;
				eloan_p9_recpt_m_name = l140m01a_custName;
				//~~~~~~
				eloan_p9_sig_m_custId = l140m01a.getCustId();
				eloan_p9_sig_m_addr = get_SIG_ADDR(caseMainId, l140m01a.getCustId(), l140m01a.getDupNo()); 
			}
			//~~~~~~~
			// 額度層的資料
			TreeMap<String, List<L140S01A>> map_l140s01aData = new TreeMap<String, List<L140S01A>>();			
			if(true){ // 從債務人
				List<L140S01A> l140s01a_list = clsService.findL140S01A(l140m01a);
				LinkedHashMap<String, String> idDup_name_map_custPosC = new LinkedHashMap<String, String>();
				LinkedHashMap<String, String> idDup_name_map_custPosN = new LinkedHashMap<String, String>();
				LinkedHashMap<String, String> idDup_name_map_custPosG = new LinkedHashMap<String, String>(); 
				if(l140s01a_list!=null){
					for(L140S01A l140s01a : l140s01a_list){
						String custPos = Util.trim(l140s01a.getCustPos());
						if(!map_l140s01aData.containsKey(custPos)){
							map_l140s01aData.put(custPos, new ArrayList<L140S01A>());
						}
						map_l140s01aData.get(custPos).add(l140s01a);
						
						String idDup = LMSUtil.getCustKey_len10custId(l140s01a.getCustId(), l140s01a.getDupNo());
						String custName = Util.trim(l140s01a.getCustName());
						if(Util.equals(custPos, UtilConstants.lngeFlag.共同借款人)){ // custPos=C
							idDup_name_map_custPosC.put(idDup, custName);
						}else if(Util.equals(custPos, UtilConstants.lngeFlag.ㄧ般保證人)){ // custPos=N
							idDup_name_map_custPosN.put(idDup, custName);
						}else if(Util.equals(custPos, UtilConstants.lngeFlag.連帶保證人)){ // custPos=G
							idDup_name_map_custPosG.put(idDup, custName);
						}
					}
				}
				
				if(idDup_name_map_custPosC.size()>0){ // custPos=C
					String name_c = StringUtils.join(idDup_name_map_custPosC.values(), "、");
					//~~~~~~~
					eloan_p1_contr_name_m = eloan_p1_contr_name_m + "、"+ name_c;
					if(true){
						for(String idDup : idDup_name_map_custPosC.keySet()){
							String custPosC_name = idDup_name_map_custPosC.get(idDup);
							String custPosC_id = Util.trim(StringUtils.substring(idDup, 0, 10));
							String custPosC_dupNo = Util.trim(StringUtils.substring(idDup, 10));
							//~~
							eloan_p9_sig_m_name = eloan_p9_sig_m_name + "、"+ custPosC_name;
							eloan_p9_sig_m_custId = eloan_p9_sig_m_custId + "、"+ custPosC_id;
							eloan_p9_sig_m_addr = eloan_p9_sig_m_addr + "、"+ get_SIG_ADDR(caseMainId, custPosC_id, custPosC_dupNo); 
						}
						
					}
					eloan_p9_recpt_m_name = eloan_p9_recpt_m_name + "、"+ name_c;
				}
				if(idDup_name_map_custPosN.size()>0){ // custPos=N
					eloan_p1_contr_name_n = StringUtils.join(idDup_name_map_custPosN.values(), "、");
					eloan_pb_gnteeN_cb = CB_Y_STR;
				}
				if(idDup_name_map_custPosG.size()>0){ // custPos=G
					eloan_p1_contr_name_g = StringUtils.join(idDup_name_map_custPosG.values(), "、");
					eloan_pb_gnteeG_cb = CB_Y_STR;
				}
				
				LinkedHashMap<String, String> custPosG_N_map = new LinkedHashMap<String, String>();
				custPosG_N_map.putAll(idDup_name_map_custPosG);
				custPosG_N_map.putAll(idDup_name_map_custPosN);
				if(true){
					// 從債務人 簽收單
					eloan_p9_recpt_s_name = StringUtils.join(custPosG_N_map.values(), "、");
					int currentIdx = 0;
					for(String idDup : custPosG_N_map.keySet()){
						String custId = StringUtils.substring(idDup, 0, 10);
						String dupNo = StringUtils.substring(idDup, 10);
						// 從債務人 簽章欄
						if(currentIdx==0){
							// eloan_p9_sig_1N_cb = idDup_name_map_custPosN.containsKey(idDup)?CB_Y_STR:"";
							eloan_p9_sig_1N_name = Util.trim(idDup_name_map_custPosN.containsKey(idDup)?idDup_name_map_custPosN.get(idDup):"");
							// eloan_p9_sig_1G_cb = idDup_name_map_custPosG.containsKey(idDup)?CB_Y_STR:"";
							eloan_p9_sig_1G_name = Util.trim(idDup_name_map_custPosG.containsKey(idDup)?idDup_name_map_custPosG.get(idDup):"");
							eloan_p9_sig_1_custId = custId;
							eloan_p9_sig_1_addr = get_SIG_ADDR(caseMainId, custId, dupNo);
						}else if(currentIdx==1){
							// eloan_p9_sig_2N_cb =
							eloan_p9_sig_2N_name = Util.trim(idDup_name_map_custPosN.containsKey(idDup)?idDup_name_map_custPosN.get(idDup):"");
							// eloan_p9_sig_2G_cb = 
							eloan_p9_sig_2G_name = Util.trim(idDup_name_map_custPosG.containsKey(idDup)?idDup_name_map_custPosG.get(idDup):"");
							eloan_p9_sig_2_custId = custId;
							eloan_p9_sig_2_addr = get_SIG_ADDR(caseMainId, custId, dupNo);
							if(true){
								//目前的表格，從債務人只有2筆
								break;
							}
						}
						//~~~~~~
						++currentIdx;						
					}
				}
			}
			//~~~~~~~
			// 帳號層的資料{mainId + seq}
			List<L140S02A> l140s02a_list = get_l140s02a_list_exclude_property78_ctrType2(l140m01a);
			
			TreeMap<Integer, L140S02C> map_l140s02cData = new TreeMap<Integer, L140S02C>();
			TreeMap<Integer, List<L140S02D>> map_l140s02dData = new TreeMap<Integer, List<L140S02D>>();
			TreeMap<Integer, L140S02E> map_l140s02eData = new TreeMap<Integer, L140S02E>();
			TreeMap<Integer, L140S02F> map_l140s02fData = new TreeMap<Integer, L140S02F>();			
			
			Set<Integer> noPPP_seq_set = new LinkedHashSet<Integer>();
			Set<Integer> withPPP_seq_set = new LinkedHashSet<Integer>();
			
			String distinct_cycl_cross_prod = "";
			//~~~~~~~
			for (L140S02A l140s02a : l140s02a_list) {
				Integer l140s02a_seq = l140s02a.getSeq();
				//==============================================				
				L140S02C l140s02c = clsService.findL140S02C(tabMainId, l140s02a_seq);
				List<L140S02D> l140s02d_list = clsService.findL140S02D_orderByPhase(tabMainId, l140s02a_seq, "Y");
				L140S02E l140s02e = clsService.findL140S02E(tabMainId, l140s02a_seq);
				L140S02F l140s02f = clsService.findL140S02F(tabMainId, l140s02a_seq);
				if(l140s02c!=null){
					map_l140s02cData.put(l140s02a_seq, l140s02c);	
				}
				if(l140s02d_list!=null){
					map_l140s02dData.put(l140s02a_seq, l140s02d_list);	
				}
				if (l140s02e != null) {
					map_l140s02eData.put(l140s02a_seq, l140s02e);	
				}
				if (l140s02f != null) {
					map_l140s02fData.put(l140s02a_seq, l140s02f);
					
					//==================================
					// 用 L140S02F.pConBeg1 來區分{無限制清償, 限制清償}
					// 例如：產品種類10, 但有輸入「提前還本管制設定」
					if(ContractDocUtil.belong_no_PPP(l140s02f)){ 
						noPPP_seq_set.add(l140s02a_seq);
					}else{
						withPPP_seq_set.add(l140s02a_seq);
					}
				}
			} //end-for (L140S02A l140s02a : l140s02a_list) {
			
			if(true){
				BigDecimal l140m01a_currentApplyAmt = l140m01a.getCurrentApplyAmt();				
				BigDecimal sum_l140s02a_amt = BigDecimal.ZERO;
				for (L140S02A l140s02a : l140s02a_list) {
					sum_l140s02a_amt = sum_l140s02a_amt.add(l140s02a.getLoanAmt());
				}
				BigDecimal choseAmt = (sum_l140s02a_amt.compareTo(BigDecimal.ZERO)>0 && sum_l140s02a_amt.compareTo(l140m01a_currentApplyAmt)<0)?sum_l140s02a_amt:l140m01a_currentApplyAmt;
				//~~~~~	
				eloan_p1_contr_amt = CapCommonUtil.toChineseUpperAmount(LMSUtil.pretty_numStr(choseAmt), true);
				
				String distinct_lnTotMonth = get_distinct_lnTotMonth_cross_prod(l140s02a_list);				
				if(Util.isNotEmpty(distinct_lnTotMonth)){
					int i_lnTotMonth = Integer.parseInt(distinct_lnTotMonth);
					//~~~~
					String lnYearUnitStr = LMSUtil.pretty_numStr(Arithmetic.div(BigDecimal.valueOf(i_lnTotMonth), BigDecimal.valueOf(12), 2));
					
					if(i_lnTotMonth > 12){
						// 中長期借款
						if(Util.equals(UtilConstants.Cntrdoc.ReUse.不循環使用, l140m01a.getReUse())){
							//大部分的 {303, 321} 會落在這個判斷
							if(Util.equals("3", l140m01a.getUseDeadline())){ // 自簽約日起
								eloan_pa_use_A_t1 = l140m01a.getDesp1();
							}
							eloan_pa_use_A_t2 = eloan_p1_contr_amt; 
							eloan_pa_use_A_t3 = lnYearUnitStr;
						}else{
							// 中長期借款 且 循環(非一次撥付) => 行家理財中期循環 只有擔保科目，未開放 {無擔的中期}
						}
					}else{
						//短期借款
						if(Util.equals(UtilConstants.Cntrdoc.ReUse.循環使用, l140m01a.getReUse())){							
							for (L140S02A l140s02a : l140s02a_list) {
								String subjCode = l140s02a.getSubjCode();
								if(CrsUtil.is_subj_in_102(subjCode)){ 
									eloan_pa_use_D_t7 = distinct_lnTotMonth;
									eloan_pa_use_D_t8 = eloan_p1_contr_amt;
								}else if(CrsUtil.is_subj_in_104(subjCode)){
									eloan_pa_use_C_t7 = distinct_lnTotMonth;
									eloan_pa_use_C_t8 = eloan_p1_contr_amt;
								}else{
									eloan_pa_use_B_t7 = distinct_lnTotMonth;
									eloan_pa_use_B_t8 = eloan_p1_contr_amt;
								}
							}
						}else{
							// 短期借款 且 不循環(一次撥付}
						}
					}
				}
			}
			
			if(true){
				distinct_cycl_cross_prod = get_distinct_cycl_cross_prod(l140s02a_list, map_l140s02cData, map_l140s02eData);
				String distinct_rtType_cross_prod = get_distinct_rtType_cross_prod(l140s02a_list, map_l140s02cData, map_l140s02eData);				
				String distinct_extendCntSinceFirst_cross_prod = get_distinct_extendCntSinceFirst_cross_prod(l140s02a_list, map_l140s02cData, map_l140s02eData);
				_debug("cntrNo="+l140m01a.getCntrNo(), "{distinct_cycl:"+distinct_cycl_cross_prod
						+", distinct_rtType="+distinct_rtType_cross_prod
						+", distinct_extendCntSinceFirst="+distinct_extendCntSinceFirst_cross_prod
						+", noPPP_cnt="+noPPP_seq_set.size()
						+", withPPP_cnt="+withPPP_seq_set.size()
						+"}"); 
				if(Util.isNotEmpty(distinct_cycl_cross_prod) 
						&& Util.isNotEmpty(distinct_rtType_cross_prod) 
						&& Util.isNotEmpty(distinct_extendCntSinceFirst_cross_prod)){
					boolean match_ctrType2_repay1 = false;
					boolean match_ctrType2_repay2 = false;
					boolean match_ctrType2_repay3 = false;
					
					if(Util.equals("1", distinct_cycl_cross_prod)){
						//************************************
						//月繳
						if(Util.equals("2", distinct_rtType_cross_prod)){ //本息平均
							if(Util.equals(VAL_OTHER, distinct_extendCntSinceFirst_cross_prod)){
								
							}else if(Util.equals("0", distinct_extendCntSinceFirst_cross_prod)){ //無寬限期
								match_ctrType2_repay2 = true;
							}else{ //有寬限期
								int extendCntSinceFirst_cross_prod = Util.parseInt(distinct_extendCntSinceFirst_cross_prod);
								String[] exend_info_arr = fmt_extendYear(extendCntSinceFirst_cross_prod);
								eloan_pa_repay_C_t1 = exend_info_arr[0];
								eloan_pa_repay_C_t2 = exend_info_arr[1];
								eloan_pa_repay_C_t3 = exend_info_arr[2];
								eloan_pa_repay_C_t4 = exend_info_arr[3];
								match_ctrType2_repay3 = true;
							}
						}else if(Util.equals("3", distinct_rtType_cross_prod)){ //本金平均
						}else{
							//期付金{月繳}的攤還方式，但(非本息平均、非本金平均)
						}
					}else if(Util.equals("2", distinct_cycl_cross_prod)){
						//************************************
						//雙週繳						
					}else if(Util.equals(VAL_MONTH_INTEREST, distinct_cycl_cross_prod)){//按月計息
						match_ctrType2_repay1 = true;
					}else{
						//************************************
						//其它繳款週期(L140S02E_payWayOth)
					}
					
					if(true){
						LinkedHashMap<String, Boolean> ctrType3_ctrRepayMap = new LinkedHashMap<String, Boolean>();
						ctrType3_ctrRepayMap.put("一", match_ctrType2_repay1);
						ctrType3_ctrRepayMap.put("二", match_ctrType2_repay2);
						ctrType3_ctrRepayMap.put("三", match_ctrType2_repay3);
						eloan_pa_repay_way = build_choseKey_by_booleanVal(ctrType3_ctrRepayMap, "、");
					}
				}else{
					//可能A) 2個產品, 都是 {本息均攤、月繳}, 但從 中間期數 開始申請「寬限期」
					//可能B) 2個產品, {1個是 本息均攤}{另1個是 本金均攤}}
				}
			
				if(Util.isEmpty(eloan_pa_repay_way) && Util.equals(VAL_OVERDRAW, distinct_cycl_cross_prod)){
					eloan_pa_repay_way = "一"; // 如為 透支 ，固定選擇{一、自撥款日起，按月付息一次，到期還清本金。}
				}
				
				if(Util.isEmpty(eloan_pa_repay_way) && Util.equals(VAL_MONTH_INTEREST, distinct_cycl_cross_prod)){
					eloan_pa_repay_way = "一"; // 如為 按月計息 ，固定選擇{一、自撥款日起，按月付息一次，到期還清本金。}
				}
			}
			if(true){ //判斷是否有「限制清償期間」				
				if(noPPP_seq_set.size()>0){			
					if(true){
						eloan_pa_intr_noPPP_cb = CB_Y_STR;
					}
					LinkedHashMap<String, LinkedHashSet<String>> l140s02d_phase_map = get_distinct_l140s02d_intr_info(l140s02a_list, noPPP_seq_set, map_l140s02cData, map_l140s02dData);					
					boolean build_intr_text = true;
					String traceStr = "noPPP"
						+", is_mapValue_cnt_eq_1=" + is_mapValue_cnt_eq_1(l140s02d_phase_map)
						+", phase_size=" + l140s02d_phase_map.size()
						+")";	
					//~~~~~~
					_debug(traceStr, "distinct_cycl_cross_prod="+distinct_cycl_cross_prod);
					if(is_mapValue_cnt_eq_1(l140s02d_phase_map)){
						if(l140s02d_phase_map.size()==1){ //一段式利率
							String intr_info = "";
							for(String phase: l140s02d_phase_map.keySet()){ //在此段程式之前，已先限制 is_mapValue_cnt_eq_1==true
								intr_info = StringUtils.join(l140s02d_phase_map.get(phase), "");
							}
							
							_debug(traceStr, "intr_info="+intr_info);
							if(Util.isNotEmpty(intr_info)){
								String[] intr_info_arr = intr_info.split("\\|");
								String rateType = intr_info_arr[2];
								String baseRate = intr_info_arr[3];
								String pmFlag = intr_info_arr[4];
								String pmRate = intr_info_arr[5];
//								String nowRate = intr_info_arr[6];
								String rateFlag = intr_info_arr[7];
								
								if((Util.equals(CrsUtil.RATE_TYPE_M3, rateType) || Util.equals(CrsUtil.RATE_TYPE_MR, rateType))
										&& (Util.equals("", pmFlag)||Util.equals("P", pmFlag)) ){
									if(Util.equals("3", rateFlag) ){ //定期浮動
										build_intr_text = false;
										if(!build_intr_text){
											eloan_pa_intr_noPPP_4t1 = LMSUtil.pretty_numStr(CrsUtil.parseBigDecimal(baseRate).add(CrsUtil.parseBigDecimal(pmRate)));
											eloan_pa_intr_noPPP_4t2 = pmRate;
											eloan_pa_intr_noPPP_baseRate = baseRate;
										}	
									}									
								}else if(Util.equals(CrsUtil.RATE_TYPE_6R, rateType) && (Util.equals("", pmFlag)||Util.equals("P", pmFlag)) ){
									String distinct_lnTotMonth = get_distinct_lnTotMonth_cross_prod(l140s02a_list);								
									if(Util.isNotEmpty(distinct_lnTotMonth)){
										int i_lnTotMonth = Integer.parseInt(distinct_lnTotMonth);
										if(i_lnTotMonth > 12){
											if(Util.equals("3", rateFlag) 
													&& Util.equals("1", intr_info_arr[8])
													&& Util.equals("1", intr_info_arr[9]) ){ //每月浮動(一段式)
												build_intr_text = false;
												if(!build_intr_text){
													eloan_pa_intr_noPPP_2t1 = Util.equals("P", pmFlag)?pmRate:"0";
													eloan_pa_intr_noPPP_baseRate = baseRate;
												}									
											}else if(Util.equals("1", rateFlag) ){ //中長期、固定(一段式)
												build_intr_text = false;
												if(!build_intr_text){
													eloan_pa_intr_noPPP_3t1 = Util.equals("P", pmFlag)?pmRate:"0";
													eloan_pa_intr_noPPP_baseRate = baseRate;
												}
											}
										}else{
											//短期借款
											build_intr_text = false;
											if(!build_intr_text){
												eloan_pa_intr_noPPP_1t1 = Util.equals("P", pmFlag)?pmRate:"0";
												eloan_pa_intr_noPPP_baseRate = baseRate;
											}
										}
									}else{
										//非制式的文字
									}	
								}
							}	
						}else{
							//多段式
							build_intr_text = true;
						}	
					}else{
						build_intr_text = true;
					}	
					
					if(!build_intr_text){
						//已填入組字的欄位
					}else{					
						eloan_pa_intr_noPPP_5t1 = build_intr_textStr(l140s02a_list, noPPP_seq_set, map_l140s02dData, distinct_cycl_cross_prod);
					}			
				}
				if(withPPP_seq_set.size()>0){		
					if(true){
						eloan_pa_intr_withPPP_cb = CB_Y_STR;
					}				
					LinkedHashMap<String, LinkedHashSet<String>> l140s02d_phase_map = get_distinct_l140s02d_intr_info(l140s02a_list, withPPP_seq_set, map_l140s02cData, map_l140s02dData);					
					boolean build_intr_text = true;
					String traceStr = "withPPP"
						+", is_mapValue_cnt_eq_1=" + is_mapValue_cnt_eq_1(l140s02d_phase_map)
						+", phase_size=" + l140s02d_phase_map.size()
						+")";
					//~~~~~~
					_debug(traceStr, "distinct_cycl_cross_prod="+distinct_cycl_cross_prod);					
					if(is_mapValue_cnt_eq_1(l140s02d_phase_map) 
							&& Util.equals("1", distinct_cycl_cross_prod)  ){ //月繳
						if(l140s02d_phase_map.size()==1){ //一段式利率
							String intr_info = "";
							for(String phase: l140s02d_phase_map.keySet()){ //在此段程式之前，已先限制 is_mapValue_cnt_eq_1==true
								intr_info = StringUtils.join(l140s02d_phase_map.get(phase), "");
							}
							_debug(traceStr, "intr_info="+intr_info);
							if(Util.isNotEmpty(intr_info)){
								String[] intr_info_arr = intr_info.split("\\|");
								String bgnNum = intr_info_arr[0];
								String endNum = intr_info_arr[1];								
								String rateType = intr_info_arr[2];
								String baseRate = intr_info_arr[3];
								String pmFlag = intr_info_arr[4];
								String pmRate = intr_info_arr[5];								
								String nowRate = intr_info_arr[6];
								String rateFlag = intr_info_arr[7];
								if(Util.equals("1", rateFlag)){ //固定(一段式)
									build_intr_text = false;
									if(!build_intr_text){
										eloan_pa_intr_withPPP_1x1 = bgnNum;
										eloan_pa_intr_withPPP_1x2 = endNum;
										eloan_pa_intr_withPPP_1x3 = nowRate;
									}
								}else if(Util.equals(CrsUtil.RATE_TYPE_6R, rateType) && (Util.equals("", pmFlag)||Util.equals("P", pmFlag)) ){
									if(Util.equals("3", rateFlag) 
											&& Util.equals("1", intr_info_arr[8])
											&& Util.equals("1", intr_info_arr[9]) ){ //每月浮動(一段式)
										build_intr_text = false;
										if(!build_intr_text){
											eloan_pa_intr_withPPP_1y1 = bgnNum;
											eloan_pa_intr_withPPP_1y2 = endNum;
											eloan_pa_intr_withPPP_1y3 = Util.equals("P", pmFlag)?pmRate:"0";
//											eloan_pa_intr_withPPP_1y4 = nowRate;	
											
											if(true){
												eloan_pa_intr_withPPP_baseRate = baseRate;
											}
										}									
									}
								}
							}
						}
						if(l140s02d_phase_map.size()==2){//二段式利率
							String[] intr_info_list = new String[]{"", ""};
							int currentIdx = 0;
							for(String phase: l140s02d_phase_map.keySet()){ //在此段程式之前，已先限制 is_mapValue_cnt_eq_1==true
								intr_info_list[currentIdx] = StringUtils.join(l140s02d_phase_map.get(phase), "");
								++currentIdx;
								if(currentIdx>=2){
									break;
								}
							}
							_debug(traceStr, "intr_info_1st="+intr_info_list[0]);
							_debug(traceStr, "intr_info_2nd="+intr_info_list[1]);
							if(Util.isNotEmpty(intr_info_list[0]) && Util.isNotEmpty(intr_info_list[1])){
								String[] intr_info_1st_arr = intr_info_list[0].split("\\|");
								String[] intr_info_2nd_arr = intr_info_list[1].split("\\|");
								String bgnNum_1st = intr_info_1st_arr[0];
								String endNum_1st = intr_info_1st_arr[1];								
								String rateType_1st = intr_info_1st_arr[2];
								String baseRate_1st = intr_info_1st_arr[3];
								String pmFlag_1st = intr_info_1st_arr[4];
								String pmRate_1st = intr_info_1st_arr[5];
//								String nowRate_1st = intr_info_1st_arr[6];
								String rateFlag_1st = intr_info_1st_arr[7];
								//~~~~~~
								String bgnNum_2nd = intr_info_2nd_arr[0];
								String endNum_2nd = intr_info_2nd_arr[1];								
								String rateType_2nd = intr_info_2nd_arr[2];
								String baseRate_2nd = intr_info_2nd_arr[3];
								String pmFlag_2nd = intr_info_2nd_arr[4];
								String pmRate_2nd = intr_info_2nd_arr[5];
//								String nowRate_2nd = intr_info_2nd_arr[6];
								String rateFlag_2nd = intr_info_2nd_arr[7];
								//~~~~~~
								if(Util.equals(CrsUtil.RATE_TYPE_6R, rateType_1st) && (Util.equals("", pmFlag_1st)||Util.equals("P", pmFlag_1st)) 
									&& Util.equals(CrsUtil.RATE_TYPE_6R, rateType_2nd) && (Util.equals("", pmFlag_2nd)||Util.equals("P", pmFlag_2nd)) ){
									if(Util.equals("3", rateFlag_1st) 
											&& Util.equals("1", intr_info_1st_arr[8])
											&& Util.equals("1", intr_info_1st_arr[9]) 
										&& Util.equals("3", rateFlag_2nd) 
											&& Util.equals("1", intr_info_2nd_arr[8])
											&& Util.equals("1", intr_info_2nd_arr[9]) ){ //每月浮動(二段式)
										build_intr_text = false;
										if(!build_intr_text){
											eloan_pa_intr_withPPP_1y1 = bgnNum_1st;
											eloan_pa_intr_withPPP_1y2 = endNum_1st;
											eloan_pa_intr_withPPP_1y3 = Util.equals("P", pmFlag_1st)?pmRate_1st:"0";
//											eloan_pa_intr_withPPP_1y4 = nowRate_1st;
											
											eloan_pa_intr_withPPP_1y5 = bgnNum_2nd;
											eloan_pa_intr_withPPP_1y6 = endNum_2nd;
											eloan_pa_intr_withPPP_1y7 = Util.equals("P", pmFlag_2nd)?pmRate_2nd:"0";
//											eloan_pa_intr_withPPP_1y8 = nowRate_2nd;
											
											if(Util.equals(baseRate_1st, baseRate_2nd)){
												eloan_pa_intr_withPPP_baseRate = baseRate_1st;
											}
										}									
									}else{
										//phase1及 phase2 有任一為［6R定期浮動］以外
									}
								}
							}							
						}
						if(l140s02d_phase_map.size()==3){//三段式利率
							String[] intr_info_list = new String[]{"", "", ""};
							int currentIdx = 0;
							for(String phase: l140s02d_phase_map.keySet()){ //在此段程式之前，已先限制 is_mapValue_cnt_eq_1==true
								intr_info_list[currentIdx] = StringUtils.join(l140s02d_phase_map.get(phase), "");
								++currentIdx;
								if(currentIdx>=3){
									break;
								}
							}
							_debug(traceStr, "intr_info_1st="+intr_info_list[0]);
							_debug(traceStr, "intr_info_2nd="+intr_info_list[1]);
							_debug(traceStr, "intr_info_3rd="+intr_info_list[2]);
							if(Util.isNotEmpty(intr_info_list[0]) && Util.isNotEmpty(intr_info_list[1]) && Util.isNotEmpty(intr_info_list[2])){
								String[] intr_info_1st_arr = intr_info_list[0].split("\\|");
								String[] intr_info_2nd_arr = intr_info_list[1].split("\\|");
								String[] intr_info_3rd_arr = intr_info_list[2].split("\\|");
								String bgnNum_1st = intr_info_1st_arr[0];
								String endNum_1st = intr_info_1st_arr[1];								
								String rateType_1st = intr_info_1st_arr[2];
								String baseRate_1st = intr_info_1st_arr[3];
								String pmFlag_1st = intr_info_1st_arr[4];
								String pmRate_1st = intr_info_1st_arr[5];
//								String nowRate_1st = intr_info_1st_arr[6];
								String rateFlag_1st = intr_info_1st_arr[7];
								//~~~~~~
								String bgnNum_2nd = intr_info_2nd_arr[0];
								String endNum_2nd = intr_info_2nd_arr[1];								
								String rateType_2nd = intr_info_2nd_arr[2];
								String baseRate_2nd = intr_info_2nd_arr[3];
								String pmFlag_2nd = intr_info_2nd_arr[4];
								String pmRate_2nd = intr_info_2nd_arr[5];
//								String nowRate_2nd = intr_info_2nd_arr[6];
								String rateFlag_2nd = intr_info_2nd_arr[7];
								//~~~~~~
								String bgnNum_3rd = intr_info_3rd_arr[0];
								String endNum_3rd = intr_info_3rd_arr[1];								
								String rateType_3rd = intr_info_3rd_arr[2];
								String baseRate_3rd = intr_info_3rd_arr[3];
								String pmFlag_3rd = intr_info_3rd_arr[4];
								String pmRate_3rd = intr_info_3rd_arr[5];
//								String nowRate_3rd = intr_info_3rd_arr[6];
								String rateFlag_3rd = intr_info_3rd_arr[7];
								//~~~~~~
								if(Util.equals(CrsUtil.RATE_TYPE_6R, rateType_1st) && (Util.equals("", pmFlag_1st)||Util.equals("P", pmFlag_1st)) 
									&& Util.equals(CrsUtil.RATE_TYPE_6R, rateType_2nd) && (Util.equals("", pmFlag_2nd)||Util.equals("P", pmFlag_2nd))
									&& Util.equals(CrsUtil.RATE_TYPE_6R, rateType_3rd) && (Util.equals("", pmFlag_3rd)||Util.equals("P", pmFlag_3rd)) ){
									if(Util.equals("3", rateFlag_1st) 
											&& Util.equals("1", intr_info_1st_arr[8])
											&& Util.equals("1", intr_info_1st_arr[9]) 
										&& Util.equals("3", rateFlag_2nd) 
											&& Util.equals("1", intr_info_2nd_arr[8])
											&& Util.equals("1", intr_info_2nd_arr[9]) 
										&& Util.equals("3", rateFlag_3rd) 
											&& Util.equals("1", intr_info_3rd_arr[8])
											&& Util.equals("1", intr_info_3rd_arr[9]) ){ //每月浮動(三段式)
										build_intr_text = false;
										if(!build_intr_text){ 
											eloan_pa_intr_withPPP_1y1 = bgnNum_1st;
											eloan_pa_intr_withPPP_1y2 = endNum_1st;
											eloan_pa_intr_withPPP_1y3 = Util.equals("P", pmFlag_1st)?pmRate_1st:"0";
//											eloan_pa_intr_withPPP_1y4 = nowRate_1st;
											
											eloan_pa_intr_withPPP_1y5 = bgnNum_2nd;
											eloan_pa_intr_withPPP_1y6 = endNum_2nd;
											eloan_pa_intr_withPPP_1y7 = Util.equals("P", pmFlag_2nd)?pmRate_2nd:"0";
//											eloan_pa_intr_withPPP_1y8 = nowRate_2nd;
											
											eloan_pa_intr_withPPP_1y9 = bgnNum_3rd;
											eloan_pa_intr_withPPP_1y10 = endNum_3rd;
											eloan_pa_intr_withPPP_1y11 = Util.equals("P", pmFlag_3rd)?pmRate_3rd:"0";
//											eloan_pa_intr_withPPP_1y12 = nowRate_3rd;
											
											if(Util.equals(baseRate_1st, baseRate_2nd) && Util.equals(baseRate_1st, baseRate_3rd)){
												eloan_pa_intr_withPPP_baseRate = baseRate_1st;
											}
										}									
									}else{
										//phase1及 phase2及 phase3  有任一為［6R定期浮動］以外
									}
								}
							}							
						}
					}else{
						build_intr_text = true;
					}	
					
					if(!build_intr_text){
						//已填入組字的欄位
					}else{
						eloan_pa_intr_withPPP_2t1 = build_intr_textStr(l140s02a_list, withPPP_seq_set, map_l140s02dData, distinct_cycl_cross_prod);
					}
				}
			}
			
			if(true){
				String traceStr = "[cntrNo="+l140m01a.getCntrNo()+"]GorN";
				_debug(traceStr, "ctrType2{revolve="+(Util.equals(UtilConstants.Cntrdoc.ReUse.循環使用, l140m01a.getReUse())?"Y":"N")+"}"
						+"{eloan_pb_gnteeG_cb="+eloan_pb_gnteeG_cb+", eloan_pb_gnteeN_cb="+eloan_pb_gnteeN_cb+"}"
					);
				if(Util.equals(eloan_pb_gnteeG_cb, CB_Y_STR)){					
					if(Util.equals(UtilConstants.Cntrdoc.ReUse.不循環使用, l140m01a.getReUse())){
						eloan_pb_gnteeG_E_cb = CB_Y_STR;
					}
					if(Util.equals(UtilConstants.Cntrdoc.ReUse.循環使用, l140m01a.getReUse())){
						eloan_pb_gnteeG_D_cb = CB_Y_STR;
					}
				}
				if(Util.equals(eloan_pb_gnteeN_cb, CB_Y_STR)){
					if(Util.equals(UtilConstants.Cntrdoc.ReUse.不循環使用, l140m01a.getReUse())){ 
						eloan_pb_gnteeN_F_cb = CB_Y_STR;
					}
					if(Util.equals(UtilConstants.Cntrdoc.ReUse.循環使用, l140m01a.getReUse())){
						eloan_pb_gnteeN_G_cb = CB_Y_STR;
					}
				}
			}			
		}else{
			/* 在1份契約書，包含多個額度序號
			 * 可能1)額度A 以 丙先生 當一般保證人，額度B 以 丁小姐 當 連保人 
			*/
			L140M01A oneOfL140M01A = null;
			for (String l140m01a_mainId : c340m01b_map.keySet()) {
				L140M01A l140m01a = c340m01b_map.get(l140m01a_mainId);
				oneOfL140M01A = l140m01a;
			}			
			
			if(oneOfL140M01A!=null){
				String l140m01a_custName = Util.trim(oneOfL140M01A.getCustName());
				// eloan_p1_contr_cname = l140m01a_custName;
				eloan_p1_contr_name_m = l140m01a_custName;
				eloan_p9_sig_m_name = l140m01a_custName;
				eloan_p9_recpt_m_name = l140m01a_custName;
				//~~~~~~
				eloan_p9_sig_m_custId = oneOfL140M01A.getCustId();
				eloan_p9_sig_m_addr = get_SIG_ADDR(caseMainId, oneOfL140M01A.getCustId(), oneOfL140M01A.getDupNo()); 
			}
		}
		//******************************************************************************************
		// 填入 map
		//******************************************************************************************
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();

		//J-110-0486_10702_B1001 Web e-Loan契約書總費用年百分率以系統計算帶入以降低錯誤率
		eloan_pc_spTerm_yRate = this.getRate(c340m01a);

		if(true){
			if (true) {
				// _injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P1_CONTR_NO, eloan_p1_contr_no);
				// _injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P1_CONTR_CNAME, eloan_p1_contr_cname);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P1_CONTR_NAME_M, eloan_p1_contr_name_m);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P1_CONTR_NAME_N, eloan_p1_contr_name_n);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P1_CONTR_NAME_G, eloan_p1_contr_name_g);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P1_CONTR_AMT, eloan_p1_contr_amt);
			}
			//=====================================
			// 貸款金額及交付方式
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_A_CB, "");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_A_T1, eloan_pa_deliv_A_t1);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_A_T2, "");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_B_CB, "");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_C_CB, "");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_C_T1, "");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_D_CB, "");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_D_T1, "");
			}
			//=====================================
			// 借款用途
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_PURPOSE_WAY, eloan_pa_purpose_way);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_PURPOSE_E_T1, eloan_pa_purpose_E_t1);
			}
			//=====================================
			// 申請動用方式、動用期限及貸款期間
			if (true) {
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T1, eloan_pa_use_A_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T2, eloan_pa_use_A_t2);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T3, eloan_pa_use_A_t3);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T4, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T5, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T6, "");
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T1, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T2, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T3, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T4, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T5, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T6, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T7, eloan_pa_use_B_t7);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T8, eloan_pa_use_B_t8);
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T1, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T2, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T3, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T4, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T5, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T6, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T7, eloan_pa_use_C_t7);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T8, eloan_pa_use_C_t8);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T9, "");
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T1, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T2, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T3, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T4, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T5, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T6, "");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T7, eloan_pa_use_D_t7);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T8, eloan_pa_use_D_t8);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T9, "");
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_E_T1, "");
				}
			}
			//=====================================
			// 貸款本息攤還方式之約定 L140S02E.payWay
			if (true) {		
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_WAY, eloan_pa_repay_way);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_A_T1, "");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_A_T2, "");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_A_T3, "");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_C_T1, eloan_pa_repay_C_t1);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_C_T2, eloan_pa_repay_C_t2);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_C_T3, eloan_pa_repay_C_t3);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_C_T4, eloan_pa_repay_C_t4);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_D_T1, "");
				
				if (true) {
					//由客戶自行勾選
					// _injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_NOPPP_CB, eloan_pa_repay_noPPP_cb); //prepayment penalty 
					// _injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_WITHPPP_CB, eloan_pa_repay_withPPP_cb);
				}
				
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_WITHPPP_TERM, "");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_ACTNO, "");
				if (true) { 
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_FEENO01, eloan_pa_repay_feeNo01); //where codetype='cls1141_feeNo' 01	開辦費
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_FEENO02, eloan_pa_repay_feeNo02); //where codetype='cls1141_feeNo' 02	信用查詢費
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_FEENO03, eloan_pa_repay_feeNo03); //where codetype='cls1141_feeNo' 03	短期續約作業費
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_FEENO04, eloan_pa_repay_feeNo04); //where codetype='cls1141_feeNo' 04	變更授信條件手續費
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_FEENO06, eloan_pa_repay_feeNo06); //where codetype='cls1141_feeNo' 06	貸款餘額證明書
				}					
			}
			//=====================================
			// 貸款計息方式之約定
			if (true) {

				if (true) { //無限制清償期間
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_CB, eloan_pa_intr_noPPP_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_BASERATE, eloan_pa_intr_noPPP_baseRate);
					// _injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_WAY, eloan_pa_intr_noPPP_way);
					
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_1T1, eloan_pa_intr_noPPP_1t1);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_2T1, eloan_pa_intr_noPPP_2t1);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_3T1, eloan_pa_intr_noPPP_3t1);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_4T1, eloan_pa_intr_noPPP_4t1);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_4T2, eloan_pa_intr_noPPP_4t2);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_5T1, eloan_pa_intr_noPPP_5t1);
				}
				
				if(true){ //限制提前清償
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_CB, eloan_pa_intr_withPPP_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_BASERATE, eloan_pa_intr_withPPP_baseRate);
					// _injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_WAY, eloan_pa_intr_withPPP_way);
					if(true){
						if(true){ //固定計息
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1X1, eloan_pa_intr_withPPP_1x1);
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1X2, eloan_pa_intr_withPPP_1x2);
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1X3, eloan_pa_intr_withPPP_1x3);
						}
						if(true){ //浮動計息PartA
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y1, eloan_pa_intr_withPPP_1y1);
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y2, eloan_pa_intr_withPPP_1y2);
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y3, eloan_pa_intr_withPPP_1y3);
							//_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y4, eloan_pa_intr_withPPP_1y4);
						}
						if(true){ //浮動計息PartB
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y5, eloan_pa_intr_withPPP_1y5);
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y6, eloan_pa_intr_withPPP_1y6);
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y7, eloan_pa_intr_withPPP_1y7);
							//_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y8, eloan_pa_intr_withPPP_1y8);
						}
						if(true){ //浮動計息PartC
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y9, eloan_pa_intr_withPPP_1y9);
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y10, eloan_pa_intr_withPPP_1y10);
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y11, eloan_pa_intr_withPPP_1y11);
							//_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y12, eloan_pa_intr_withPPP_1y12);
						}
					}
					if(true){ //限制提前清償_其他
						_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_2T1, eloan_pa_intr_withPPP_2t1);
					}
					
				}
				if(true){ //個別約定
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_OTHER_CB, on_when_has_data(eloan_pa_intr_other_t1, CB_Y_STR));
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_OTHER_T1, eloan_pa_intr_other_t1);
				}
				
			}
			if (true) { //利率調整之通知 => 未約定者，以書面方式為之
				// _injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_notice_1_cb", "");
				// _injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_notice_2_cb", "");
				// _injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_notice_3_cb", "");
			}
			//=====================================
			// 保證條款
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_CB, eloan_pb_gnteeG_cb);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_E_CB, eloan_pb_gnteeG_E_cb); //E:理財型貸款(不循環動用)
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_E_T1, eloan_pb_gnteeG_E_t1);		
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_D_CB, eloan_pb_gnteeG_D_cb); //D:理財型貸款(循環動用)
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_D_T1, eloan_pb_gnteeG_D_t1);
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_CB, eloan_pb_gnteeN_cb);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_F_CB, eloan_pb_gnteeN_F_cb); //F:消費性放款（不循環動用）
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_F_T1, eloan_pb_gnteeN_F_t1);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_G_CB, eloan_pb_gnteeN_G_cb); //G:理財型貸款或消費性放款（循環動用）
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_G_T1, eloan_pb_gnteeN_G_t1);
			}
			//=====================================			
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SERV_TEL_T1, eloan_pc_serv_tel_t1);				
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SERV_FAX_T1, eloan_pc_serv_fax_t1);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SERV_MAIL_T1, eloan_pc_serv_mail_t1);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SERV_OTHER_T1, eloan_pc_serv_other_t1);
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_COURT_LOC, eloan_pc_court_loc);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_COPY_CNT, eloan_pc_copy_cnt);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SPTERM_YRATE, eloan_pc_spTerm_yRate);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SPTERM_NOTE, eloan_pc_spTerm_note);
			}
			
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_M_NAME, eloan_p9_sig_m_name);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_M_CUSTID, eloan_p9_sig_m_custId);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_M_ADDR, eloan_p9_sig_m_addr);
				
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_1N_CB, on_when_has_data(eloan_p9_sig_1N_name, CB_Y_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_1N_NAME, eloan_p9_sig_1N_name);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_1G_CB, on_when_has_data(eloan_p9_sig_1G_name, CB_Y_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_1G_NAME, eloan_p9_sig_1G_name);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_1_CUSTID, eloan_p9_sig_1_custId);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_1_ADDR, eloan_p9_sig_1_addr);

				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_2N_CB, on_when_has_data(eloan_p9_sig_2N_name, CB_Y_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_2N_NAME, eloan_p9_sig_2N_name);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_2G_CB, on_when_has_data(eloan_p9_sig_2G_name, CB_Y_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_2G_NAME, eloan_p9_sig_2G_name);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_2_CUSTID, eloan_p9_sig_2_custId);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_2_ADDR, eloan_p9_sig_2_addr);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_PARTYB_AGENT, eloan_p9_sig_partyB_agent);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_PARTYB_ADDR, eloan_p9_sig_partyB_addr);
			}
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_RECPT_M_NAME, eloan_p9_recpt_m_name);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_RECPT_S_NAME, eloan_p9_recpt_s_name);
			}	
		}

		return paramMap;
	}
	
	private LinkedHashMap<String, String> init_paramMap_ctrType3_V202008(C340M01A c340m01a, Map<String, L140M01A> c340m01b_map)
	throws CapException{
//		String eloan_p1_contr_no = "";
//		String eloan_p1_contr_cname = "";
		String eloan_p1_contr_name_m = "";
		String eloan_p1_contr_name_n = "";
		String eloan_p1_contr_name_g = "";
		String eloan_p1_contr_amt = "";
		String eloan_pa_deliv_A_cb = "";
		String eloan_pa_deliv_A_t1 = "活期儲蓄";
		String eloan_pa_deliv_A_t2 = "";
		String eloan_pa_deliv_B_cb = "";
		String eloan_pa_deliv_C_cb = "";
		String eloan_pa_deliv_C_t1 = "";
		String eloan_pa_deliv_D_cb = "";
		String eloan_pa_deliv_D_t1 = "";
		String eloan_pa_deliv_D_t2 = "";
		String eloan_pa_deliv_E_cb = "";
		String eloan_pa_deliv_E_t1 = "";
		String eloan_pa_purpose_way = "";
		String eloan_pa_purpose_G_t1 = "";
		
		String eloan_pa_use_A_t1 = "";
		String eloan_pa_use_A_t2 = "";
		String eloan_pa_use_A_t3 = "";
		String eloan_pa_use_A_t4 = "";
		String eloan_pa_use_A_t5 = "";
		String eloan_pa_use_A_t6 = "";
		String eloan_pa_use_A_t7 = "";
		String eloan_pa_use_A_t8 = "";
		String eloan_pa_use_B_t1 = "";
		String eloan_pa_use_B_t2 = "";
		String eloan_pa_use_B_t3 = "";
		String eloan_pa_use_B_t4 = "";
		String eloan_pa_use_B_t5 = "";
		String eloan_pa_use_B_t6 = "";
		String eloan_pa_use_B_t7 = "";
		String eloan_pa_use_B_t8 = "";
		String eloan_pa_use_B_t9 = "";
		String eloan_pa_use_C_t1 = "";
		String eloan_pa_use_C_t2 = "";
		String eloan_pa_use_C_t3 = "";
		String eloan_pa_use_C_t4 = "";
		String eloan_pa_use_C_t5 = "";
		String eloan_pa_use_C_t6 = "";
		String eloan_pa_use_C_t7 = "";
		String eloan_pa_use_C_t8 = "";
		String eloan_pa_use_C_t9 = "";
		String eloan_pa_use_D_t1 = "";
		String eloan_pa_use_D_t2 = "";
		String eloan_pa_use_D_t3 = "";
		String eloan_pa_use_D_t4 = "";
		String eloan_pa_use_D_t5 = "";
		String eloan_pa_use_D_t6 = "";
		String eloan_pa_use_E_t1 = "";
		
		String eloan_pa_repay_way = "";
		String eloan_pa_repay_D_t1 = "";
		String eloan_pa_repay_D_t2 = "";
		String eloan_pa_repay_D_t3 = "";
		String eloan_pa_repay_D_t4 = "";
		String eloan_pa_repay_E_t1 = "";
		String eloan_pa_repay_E_t2 = "";
		String eloan_pa_repay_E_t3 = "";
		String eloan_pa_repay_E_t4 = "";
		String eloan_pa_repay_F_t1 = "";
		String eloan_pa_repay_F_t2 = "";
		String eloan_pa_repay_G_t1 = "";
//		String eloan_pa_repay_noPPP_cb = "";
//		String eloan_pa_repay_withPPP_cb = "";		
		String eloan_pa_repay_withPPP_term = "";
		String eloan_pa_repay_actNo = "";
		String eloan_pa_repay_feeNo01 = "";
		String eloan_pa_repay_feeNo02 = "";
		String eloan_pa_repay_feeNo03 = "";
		String eloan_pa_repay_feeNo04 = "";
		String eloan_pa_repay_feeNo06 = "";
		String eloan_pa_repay_feeNo07 = "";
		String eloan_pa_intr_noPPP_cb = "";
		String eloan_pa_intr_noPPP_baseRate = "";
//		String eloan_pa_intr_noPPP_way = "";
		String eloan_pa_intr_noPPP_1t1 = "";
//		String eloan_pa_intr_noPPP_1t2 = "";
		String eloan_pa_intr_noPPP_2t1 = "";
		String eloan_pa_intr_noPPP_3t1 = "";
		String eloan_pa_intr_noPPP_4t1 = "";
		String eloan_pa_intr_withPPP_cb = "";
		String eloan_pa_intr_withPPP_baseRate = "";
//		String eloan_pa_intr_withPPP_way = "";
		String eloan_pa_intr_withPPP_1x1 = "";
		String eloan_pa_intr_withPPP_1x2 = "";
		String eloan_pa_intr_withPPP_1x3 = "";
		String eloan_pa_intr_withPPP_1y1 = "";
		String eloan_pa_intr_withPPP_1y2 = "";
		String eloan_pa_intr_withPPP_1y3 = "";
//		String eloan_pa_intr_withPPP_1y4 = "";
		String eloan_pa_intr_withPPP_1y5 = "";
		String eloan_pa_intr_withPPP_1y6 = "";
		String eloan_pa_intr_withPPP_1y7 = "";
//		String eloan_pa_intr_withPPP_1y8 = "";
		String eloan_pa_intr_withPPP_2t1 = "";
//		String eloan_pa_intr_other_cb = "";
		String eloan_pa_intr_other_t1 = "";
		String eloan_pb_gnteeG_cb = "";
		String eloan_pb_gnteeG_B_cb = "";
		String eloan_pb_gnteeG_B_t1 = "";
		String eloan_pb_gnteeG_C_cb = "";
		String eloan_pb_gnteeG_C_t1 = "";
		String eloan_pb_gnteeG_D_cb = "";
		String eloan_pb_gnteeN_cb = "";		
		String eloan_pb_gnteeN_B_cb = "";
		String eloan_pb_gnteeN_B_t1 = "";
		String eloan_pb_gnteeN_C_cb = "";
		String eloan_pb_gnteeN_C_t1 = "";
		String eloan_pb_gnteeN_D_cb = "";
		
//		String eloan_pc_serv_tel_cb = "";
		String eloan_pc_serv_tel_t1 = "";
//		String eloan_pc_serv_fax_cb = "";
		String eloan_pc_serv_fax_t1 = "";
//		String eloan_pc_serv_mail_cb = "";
		String eloan_pc_serv_mail_t1 = "";		
//		String eloan_pc_serv_other_cb = "";
		String eloan_pc_serv_other_t1 = "";
		String eloan_pc_court_loc = "";
		String eloan_pc_copy_cnt = "";
		String eloan_pc_spTerm_yRate = "";
		String eloan_pc_spTerm_note = "";
		
		String eloan_p9_sig_m_name = "";
		String eloan_p9_sig_m_custId = "";
		String eloan_p9_sig_m_addr = "";
//		String eloan_p9_sig_1N_cb = "";
		String eloan_p9_sig_1N_name = "";
//		String eloan_p9_sig_1G_cb = "";
		String eloan_p9_sig_1G_name = "";
		String eloan_p9_sig_1_custId = "";
		String eloan_p9_sig_1_addr = "";
//		String eloan_p9_sig_2N_cb = "";
		String eloan_p9_sig_2N_name = "";
//		String eloan_p9_sig_2G_cb = "";
		String eloan_p9_sig_2G_name = "";
		String eloan_p9_sig_2_custId = "";
		String eloan_p9_sig_2_addr = "";
		String eloan_p9_sig_partyB_agent = "";
		String eloan_p9_sig_partyB_addr = "";
		String eloan_p9_recpt_m_name = "";
		String eloan_p9_recpt_s_name = "";

		//******************************************************************************************
		// 依 額度、產品 決定 param_value
		//******************************************************************************************
		String caseMainId = c340m01a.getCaseMainId();
		L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);
		if(l120m01a!=null){
			//取得分行［電話、傳真］
			IBranch iBranch = branchService.getBranch(l120m01a.getCaseBrId());
			if(iBranch!=null){
				eloan_pc_serv_tel_t1 = Util.trim(iBranch.getTel());
				eloan_pc_serv_fax_t1 = Util.trim(iBranch.getFaxNo());
				
				String brnMgr = Util.trim(iBranch.getBrnMgr());				
				eloan_p9_sig_partyB_agent = Util.trim(iBranch.getNameABBR())+"分公司　經理　"+(brnMgr.length()>=5?userInfoService.getUserName(brnMgr):"");
				eloan_p9_sig_partyB_addr = Util.trim(iBranch.getAddr());
			}
		}
		if(true){
			List<L140M01R> l140m01r_list = clsService.findL140M01R_exclude_feeSrc3(caseMainId);
			if(l140m01r_list.size()>0){
				BigDecimal sum_feeNo01 = BigDecimal.ZERO;
				BigDecimal sum_feeNo02 = BigDecimal.ZERO;
				BigDecimal sum_feeNo03 = BigDecimal.ZERO;
				BigDecimal sum_feeNo04 = BigDecimal.ZERO;
				BigDecimal sum_feeNo06 = BigDecimal.ZERO;
				BigDecimal sum_feeNo07 = BigDecimal.ZERO;
				for(L140M01R l140m01r: l140m01r_list){
					String feeNo = l140m01r.getFeeNo();
					BigDecimal amt = l140m01r.getFeeAmt();
					
					if(Util.equals("01", feeNo)){
						sum_feeNo01 = sum_feeNo01.add(amt);
					}else if(Util.equals("02", feeNo)){
						sum_feeNo02 = sum_feeNo02.add(amt);
					}else if(Util.equals("03", feeNo)){
						sum_feeNo03 = sum_feeNo03.add(amt);
					}else if(Util.equals("04", feeNo)){
						sum_feeNo04 = sum_feeNo04.add(amt);
					}else if(Util.equals("06", feeNo)){
						sum_feeNo06 = sum_feeNo06.add(amt);
					}else if(Util.equals("07", feeNo)){
						sum_feeNo07 = sum_feeNo07.add(amt);
					}else{
						continue;
					}
				}
				eloan_pa_repay_feeNo01 = LMSUtil.pretty_numStr(sum_feeNo01);
				eloan_pa_repay_feeNo02 = LMSUtil.pretty_numStr(sum_feeNo02);
				eloan_pa_repay_feeNo03 = LMSUtil.pretty_numStr(sum_feeNo03);
				eloan_pa_repay_feeNo04 = LMSUtil.pretty_numStr(sum_feeNo04);
				eloan_pa_repay_feeNo06 = LMSUtil.pretty_numStr(sum_feeNo06);
				eloan_pa_repay_feeNo07 = LMSUtil.pretty_numStr(sum_feeNo07);
			}else{
				//若未輸入各項費用，不預設帶0
			}
		}
		if(true){
			/*
			本借款用途如下列第 款之記載
			一、個人或家庭理財。
			二、購買房屋。
			三、修繕房屋。
			四、購買汽車。
			五、繳付甲方或甲方子女學費。
			六、購置耐久性消費財。
			七、其他：
			*/
			boolean has_ctrType3_ctrPurpose1 = false;
			boolean has_ctrType3_ctrPurpose2 = false;
			boolean has_ctrType3_ctrPurpose3 = false;
			boolean has_ctrType3_ctrPurpose4 = false;
			boolean has_ctrType3_ctrPurpose5 = false;
			boolean has_ctrType3_ctrPurpose6 = false;
			boolean has_ctrType3_cls1141_purpose_Other = false;
			LinkedHashSet<String> ctrType3_ctrPurpose7_list = new LinkedHashSet<String>();
			/*			 
			3	其他
			A	購置住宅
			B	修繕房屋
			C	週轉(含行家理財中長期)
			D	購置汽車
			E	購置停車位貸款
			F	出國留學
			G	投資理財
			H	購置自用耐久性消費品
			I	出國旅遊
			J	子女教育
			K	繳稅
			L	青年創業貸款
			M	生活安養所需資金
			*/
			Map<String, String> cls1141_purpose = clsService.get_codeTypeWithOrder("cls1141_purpose");
			for (String purpose : Util.trim(l120m01a.getPurpose()).split(UtilConstants.Mark.SPILT_MARK)) {
				if (Util.isEmpty(purpose)) {
					continue;
				}
				// ---
				if (Util.equals(UtilConstants.Casedoc.purpose.其他, purpose)){
					has_ctrType3_cls1141_purpose_Other = true;
				}else if (Util.equals(UtilConstants.Casedoc.purpose.購置住宅, purpose)){
					has_ctrType3_ctrPurpose2 = true;
				}else if (Util.equals(UtilConstants.Casedoc.purpose.修繕房屋, purpose)){
					has_ctrType3_ctrPurpose3 = true;
				}else if (Util.equals(UtilConstants.Casedoc.purpose.週轉_含行家理財中長期, purpose)){
					ctrType3_ctrPurpose7_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else if (Util.equals(UtilConstants.Casedoc.purpose.購置汽車, purpose)){
					has_ctrType3_ctrPurpose4 = true;
				}else if (Util.equals(UtilConstants.Casedoc.purpose.購置停車位貸款, purpose)){
					ctrType3_ctrPurpose7_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else if (Util.equals(UtilConstants.Casedoc.purpose.出國留學, purpose)){
					ctrType3_ctrPurpose7_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else if (Util.equals(UtilConstants.Casedoc.purpose.投資理財, purpose)){
					has_ctrType3_ctrPurpose1 = true;
				}else if (Util.equals(UtilConstants.Casedoc.purpose.購置自用耐久性消費品, purpose)){
					has_ctrType3_ctrPurpose6 = true;
				}else if (Util.equals(UtilConstants.Casedoc.purpose.出國旅遊, purpose)){
					ctrType3_ctrPurpose7_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else if (Util.equals(UtilConstants.Casedoc.purpose.子女教育, purpose)){
					has_ctrType3_ctrPurpose5 = true;
				}else if (Util.equals(UtilConstants.Casedoc.purpose.繳稅, purpose)){
					ctrType3_ctrPurpose7_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else if (Util.equals(UtilConstants.Casedoc.purpose.青年創業貸款, purpose)){
					ctrType3_ctrPurpose7_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else if (Util.equals(UtilConstants.Casedoc.purpose.生活安養所需資金, purpose)){ 
					ctrType3_ctrPurpose7_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}else {
					ctrType3_ctrPurpose7_list.add(Util.trim(cls1141_purpose.get(purpose)));
				}
			}
			if(has_ctrType3_cls1141_purpose_Other){ //將「其它」置於最後
				ctrType3_ctrPurpose7_list.add(Util.trim(l120m01a.getPurposeOth()));	
			}

			if(true){
				boolean has_ctrType3_ctrPurpose7 = ctrType3_ctrPurpose7_list.size()>0;
				LinkedHashMap<String, Boolean> ctrPurposeMap = new LinkedHashMap<String, Boolean>();
				ctrPurposeMap.put("一", has_ctrType3_ctrPurpose1);
				ctrPurposeMap.put("二", has_ctrType3_ctrPurpose2);
				ctrPurposeMap.put("三", has_ctrType3_ctrPurpose3);
				ctrPurposeMap.put("四", has_ctrType3_ctrPurpose4);
				ctrPurposeMap.put("五", has_ctrType3_ctrPurpose5);
				ctrPurposeMap.put("六", has_ctrType3_ctrPurpose6);
				ctrPurposeMap.put("七", has_ctrType3_ctrPurpose7);
				eloan_pa_purpose_way = build_choseKey_by_booleanVal(ctrPurposeMap, "、");
			}
			eloan_pa_purpose_G_t1 = StringUtils.join(ctrType3_ctrPurpose7_list, "、");
		}
		if(c340m01b_map.size()==1){  // 包含1個額度2個產品{青安+房貸}的狀況
			L140M01A l140m01a = null;
			for (String l140m01a_mainId : c340m01b_map.keySet()) {
				l140m01a = c340m01b_map.get(l140m01a_mainId);
			}
			String tabMainId = l140m01a.getMainId();
			
			if(l140m01a!=null){
				//取得 主借人 資料
				String l140m01a_custName = Util.trim(l140m01a.getCustName());
				// eloan_p1_contr_cname = l140m01a_custName;
				eloan_p1_contr_name_m = l140m01a_custName;
				eloan_p9_sig_m_name = l140m01a_custName;
				eloan_p9_recpt_m_name = l140m01a_custName;
				//~~~~~~
				eloan_p9_sig_m_custId = l140m01a.getCustId();
				eloan_p9_sig_m_addr = get_SIG_ADDR(caseMainId, l140m01a.getCustId(), l140m01a.getDupNo()); 
			}
			//~~~~~~~
			// 額度層的資料
			TreeMap<String, List<L140S01A>> map_l140s01aData = new TreeMap<String, List<L140S01A>>();			
			if(true){ // 從債務人
				List<L140S01A> l140s01a_list = clsService.findL140S01A(l140m01a);
				LinkedHashMap<String, String> idDup_name_map_custPosC = new LinkedHashMap<String, String>();
				LinkedHashMap<String, String> idDup_name_map_custPosN = new LinkedHashMap<String, String>();
				LinkedHashMap<String, String> idDup_name_map_custPosG = new LinkedHashMap<String, String>(); 
				if(l140s01a_list!=null){
					for(L140S01A l140s01a : l140s01a_list){
						String custPos = Util.trim(l140s01a.getCustPos());
						if(!map_l140s01aData.containsKey(custPos)){
							map_l140s01aData.put(custPos, new ArrayList<L140S01A>());
						}
						map_l140s01aData.get(custPos).add(l140s01a);
						
						String idDup = LMSUtil.getCustKey_len10custId(l140s01a.getCustId(), l140s01a.getDupNo());
						String custName = Util.trim(l140s01a.getCustName());
						if(Util.equals(custPos, UtilConstants.lngeFlag.共同借款人)){ // custPos=C
							idDup_name_map_custPosC.put(idDup, custName);
						}else if(Util.equals(custPos, UtilConstants.lngeFlag.ㄧ般保證人)){ // custPos=N
							idDup_name_map_custPosN.put(idDup, custName);
						}else if(Util.equals(custPos, UtilConstants.lngeFlag.連帶保證人)){ // custPos=G
							idDup_name_map_custPosG.put(idDup, custName);
						}
					}
				}
				
				if(idDup_name_map_custPosC.size()>0){ // custPos=C
					String name_c = StringUtils.join(idDup_name_map_custPosC.values(), "、");
					//~~~~~~~
					eloan_p1_contr_name_m = eloan_p1_contr_name_m + "、"+ name_c;
					if(true){
						for(String idDup : idDup_name_map_custPosC.keySet()){
							String custPosC_name = idDup_name_map_custPosC.get(idDup);
							String custPosC_id = Util.trim(StringUtils.substring(idDup, 0, 10));
							String custPosC_dupNo = Util.trim(StringUtils.substring(idDup, 10));
							//~~
							eloan_p9_sig_m_name = eloan_p9_sig_m_name + "、"+ custPosC_name;
							eloan_p9_sig_m_custId = eloan_p9_sig_m_custId + "、"+ custPosC_id;
							eloan_p9_sig_m_addr = eloan_p9_sig_m_addr + "、"+ get_SIG_ADDR(caseMainId, custPosC_id, custPosC_dupNo); 
						}
						
					}
					eloan_p9_recpt_m_name = eloan_p9_recpt_m_name + "、"+ name_c;
				}
				if(idDup_name_map_custPosN.size()>0){ // custPos=N
					eloan_p1_contr_name_n = StringUtils.join(idDup_name_map_custPosN.values(), "、");
					eloan_pb_gnteeN_cb = CB_Y_STR;
				}
				if(idDup_name_map_custPosG.size()>0){ // custPos=G
					eloan_p1_contr_name_g = StringUtils.join(idDup_name_map_custPosG.values(), "、");
					eloan_pb_gnteeG_cb = CB_Y_STR;
				}
				
				LinkedHashMap<String, String> custPosG_N_map = new LinkedHashMap<String, String>();
				custPosG_N_map.putAll(idDup_name_map_custPosG);
				custPosG_N_map.putAll(idDup_name_map_custPosN);
				if(true){
					// 從債務人 簽收單
					eloan_p9_recpt_s_name = StringUtils.join(custPosG_N_map.values(), "、");
					int currentIdx = 0;
					for(String idDup : custPosG_N_map.keySet()){
						String custId = StringUtils.substring(idDup, 0, 10);
						String dupNo = StringUtils.substring(idDup, 10);
						// 從債務人 簽章欄
						if(currentIdx==0){
							// eloan_p9_sig_1N_cb = idDup_name_map_custPosN.containsKey(idDup)?CB_Y_STR:"";
							eloan_p9_sig_1N_name = Util.trim(idDup_name_map_custPosN.containsKey(idDup)?idDup_name_map_custPosN.get(idDup):"");
							// eloan_p9_sig_1G_cb = idDup_name_map_custPosG.containsKey(idDup)?CB_Y_STR:"";
							eloan_p9_sig_1G_name = Util.trim(idDup_name_map_custPosG.containsKey(idDup)?idDup_name_map_custPosG.get(idDup):"");
							eloan_p9_sig_1_custId = custId;
							eloan_p9_sig_1_addr = get_SIG_ADDR(caseMainId, custId, dupNo);
						}else if(currentIdx==1){
							// eloan_p9_sig_2N_cb =
							eloan_p9_sig_2N_name = Util.trim(idDup_name_map_custPosN.containsKey(idDup)?idDup_name_map_custPosN.get(idDup):"");
							// eloan_p9_sig_2G_cb = 
							eloan_p9_sig_2G_name = Util.trim(idDup_name_map_custPosG.containsKey(idDup)?idDup_name_map_custPosG.get(idDup):"");
							eloan_p9_sig_2_custId = custId;
							eloan_p9_sig_2_addr = get_SIG_ADDR(caseMainId, custId, dupNo);
							if(true){
								//目前的表格，從債務人只有2筆
								break;
							}
						}
						//~~~~~~
						++currentIdx;						
					}
				}
			}
			//~~~~~~~
			// 帳號層的資料{mainId + seq}
			List<L140S02A> l140s02a_list = get_l140s02a_list_exclude_property78(l140m01a);
			
			TreeMap<Integer, L140S02C> map_l140s02cData = new TreeMap<Integer, L140S02C>();
			TreeMap<Integer, List<L140S02D>> map_l140s02dData = new TreeMap<Integer, List<L140S02D>>();
			TreeMap<Integer, L140S02E> map_l140s02eData = new TreeMap<Integer, L140S02E>();
			TreeMap<Integer, L140S02F> map_l140s02fData = new TreeMap<Integer, L140S02F>();			
			
			Set<Integer> noPPP_seq_set = new LinkedHashSet<Integer>();
			Set<Integer> withPPP_seq_set = new LinkedHashSet<Integer>();
			
			String distinct_cycl_cross_prod = "";
			//~~~~~~~
			for (L140S02A l140s02a : l140s02a_list) {
				Integer l140s02a_seq = l140s02a.getSeq();
				//==============================================				
				L140S02C l140s02c = clsService.findL140S02C(tabMainId, l140s02a_seq);
				List<L140S02D> l140s02d_list = clsService.findL140S02D_orderByPhase(tabMainId, l140s02a_seq, "Y");
				L140S02E l140s02e = clsService.findL140S02E(tabMainId, l140s02a_seq);
				L140S02F l140s02f = clsService.findL140S02F(tabMainId, l140s02a_seq);
				if(l140s02c!=null){
					map_l140s02cData.put(l140s02a_seq, l140s02c);	
				}
				if(l140s02d_list!=null){
					map_l140s02dData.put(l140s02a_seq, l140s02d_list);	
				}
				if (l140s02e != null) {
					map_l140s02eData.put(l140s02a_seq, l140s02e);	
				}
				if (l140s02f != null) {
					map_l140s02fData.put(l140s02a_seq, l140s02f);
					
					//==================================
					// 用 L140S02F.pConBeg1 來區分{無限制清償, 限制清償}
					// 例如：產品種類10, 但有輸入「提前還本管制設定」
					if(ContractDocUtil.belong_no_PPP(l140s02f)){ 
						noPPP_seq_set.add(l140s02a_seq);
					}else{
						withPPP_seq_set.add(l140s02a_seq);
					}
				}
			} //end-for (L140S02A l140s02a : l140s02a_list) {
			boolean match_eloan_pa_use_D = false; //適用於中、長期不循環借款 
			if(l140m01a!=null && Util.equals(UtilConstants.Cntrdoc.ReUse.不循環使用, l140m01a.getReUse())){
				String distinct_lnTotMonth = get_distinct_lnTotMonth_cross_prod(l140s02a_list);
				if(Util.isNotEmpty(distinct_lnTotMonth)){
					int i_lnTotMonth = Integer.parseInt(distinct_lnTotMonth);
					//~~~~
					if(i_lnTotMonth > 12){ //月份>12 ，表示中、長期
						match_eloan_pa_use_D = true;
					}								
				}
			}
			if(true){
				BigDecimal l140m01a_currentApplyAmt = l140m01a.getCurrentApplyAmt();
				BigDecimal sum_l140s02a_amt = BigDecimal.ZERO;
				for (L140S02A l140s02a : l140s02a_list) {
					sum_l140s02a_amt = sum_l140s02a_amt.add(l140s02a.getLoanAmt());
				}
				BigDecimal choseAmt = (sum_l140s02a_amt.compareTo(BigDecimal.ZERO)>0 && sum_l140s02a_amt.compareTo(l140m01a_currentApplyAmt)<0)?sum_l140s02a_amt:l140m01a_currentApplyAmt;
				//~~~~~	
				eloan_p1_contr_amt = CapCommonUtil.toChineseUpperAmount(LMSUtil.pretty_numStr(choseAmt), true);
				if(match_eloan_pa_use_D && Util.equals("3", l140m01a.getUseDeadline())){ // 自簽約日起
					eloan_pa_use_D_t1 = l140m01a.getDesp1();
					eloan_pa_use_D_t2 = eloan_p1_contr_amt; 
				}
			}
			if(true){
				String distinct_lnTotMonth = get_distinct_lnTotMonth_cross_prod(l140s02a_list);
				if(Util.isNotEmpty(distinct_lnTotMonth)){
					int i_lnTotMonth = Integer.parseInt(distinct_lnTotMonth);
					//~~~~
					if(match_eloan_pa_use_D && Util.equals("3", l140m01a.getUseDeadline()) && Util.isNotEmpty(eloan_pa_use_D_t1)){ // 自簽約日起
						eloan_pa_use_D_t3 = LMSUtil.pretty_numStr(Arithmetic.div(BigDecimal.valueOf(i_lnTotMonth), BigDecimal.valueOf(12), 2));
					}
					// eloan_pa_use_y = String.valueOf(i_lnTotMonth/12);
					// eloan_pa_use_m = String.valueOf(i_lnTotMonth%12);				
				}
			}			
			if(true){
				distinct_cycl_cross_prod = get_distinct_cycl_cross_prod(l140s02a_list, map_l140s02cData, map_l140s02eData);
				String distinct_rtType_cross_prod = get_distinct_rtType_cross_prod(l140s02a_list, map_l140s02cData, map_l140s02eData);				
				String distinct_extendCntSinceFirst_cross_prod = get_distinct_extendCntSinceFirst_cross_prod(l140s02a_list, map_l140s02cData, map_l140s02eData);
				_debug("cntrNo="+l140m01a.getCntrNo(), "{distinct_cycl:"+distinct_cycl_cross_prod
						+", distinct_rtType="+distinct_rtType_cross_prod
						+", distinct_extendCntSinceFirst="+distinct_extendCntSinceFirst_cross_prod
						+", noPPP_cnt="+noPPP_seq_set.size()
						+", withPPP_cnt="+withPPP_seq_set.size()
						+"}"); 
				if(Util.isNotEmpty(distinct_cycl_cross_prod) 
						&& Util.isNotEmpty(distinct_rtType_cross_prod) 
						&& Util.isNotEmpty(distinct_extendCntSinceFirst_cross_prod)){
					boolean match_ctrType3_repay1 = false;
					boolean match_ctrType3_repay2 = false;
					boolean match_ctrType3_repay3 = false;
					boolean match_ctrType3_repay4 = false;
					boolean match_ctrType3_repay5 = false;
					boolean match_ctrType3_repay6 = false;
					if(Util.equals("1", distinct_cycl_cross_prod)){
						//************************************
						//月繳
						if(Util.equals("2", distinct_rtType_cross_prod)){ //本息平均
							if(Util.equals(VAL_OTHER, distinct_extendCntSinceFirst_cross_prod)){
								
							}else if(Util.equals("0", distinct_extendCntSinceFirst_cross_prod)){ //無寬限期
								match_ctrType3_repay2 = true;
							}else{ //有寬限期
								int extendCntSinceFirst_cross_prod = Util.parseInt(distinct_extendCntSinceFirst_cross_prod);
								String[] exend_info_arr = fmt_extendYear(extendCntSinceFirst_cross_prod);
								eloan_pa_repay_D_t1 = exend_info_arr[0];
								eloan_pa_repay_D_t2 = exend_info_arr[1];
								eloan_pa_repay_D_t3 = exend_info_arr[2];
								eloan_pa_repay_D_t4 = exend_info_arr[3];
								match_ctrType3_repay4 = true;
							}
						}else if(Util.equals("3", distinct_rtType_cross_prod)){ //本金平均
							if(Util.equals(VAL_OTHER, distinct_extendCntSinceFirst_cross_prod)){
								
							}else if(Util.equals("0", distinct_extendCntSinceFirst_cross_prod)){ //無寬限期
								match_ctrType3_repay3 = true;
							}else{ //有寬限期
								int extendCntSinceFirst_cross_prod = Util.parseInt(distinct_extendCntSinceFirst_cross_prod);
								String[] exend_info_arr = fmt_extendYear(extendCntSinceFirst_cross_prod);
								eloan_pa_repay_E_t1 = exend_info_arr[0];
								eloan_pa_repay_E_t2 = exend_info_arr[1];
								eloan_pa_repay_E_t3 = exend_info_arr[2];
								eloan_pa_repay_E_t4 = exend_info_arr[3];
								match_ctrType3_repay5 = true;
							}
						}else{
							//期付金{月繳}的攤還方式，但(非本息平均、非本金平均)
						}
					}else if(Util.equals("2", distinct_cycl_cross_prod)){
						//************************************
						//雙週繳
						String distinct_intEndTermSinceFirst_cross_prod = get_distinct_intEndTermSinceFirst_cross_prod(l140s02a_list, map_l140s02cData, map_l140s02dData);
						if(Util.equals(VAL_OTHER, distinct_intEndTermSinceFirst_cross_prod)){
							
						}else{
							eloan_pa_repay_F_t1 = distinct_intEndTermSinceFirst_cross_prod;
							match_ctrType3_repay6 = true;
						}
					}else if(Util.equals(VAL_MONTH_INTEREST, distinct_cycl_cross_prod)){//按月計息
						match_ctrType3_repay1 = true;
					}else{
						//************************************
						//其它繳款週期(L140S02E_payWayOth)
					}
					
					if(true){
						LinkedHashMap<String, Boolean> ctrType3_ctrRepayMap = new LinkedHashMap<String, Boolean>();
						ctrType3_ctrRepayMap.put("一", match_ctrType3_repay1);
						ctrType3_ctrRepayMap.put("二", match_ctrType3_repay2);
						ctrType3_ctrRepayMap.put("三", match_ctrType3_repay3);
						ctrType3_ctrRepayMap.put("四", match_ctrType3_repay4);
						ctrType3_ctrRepayMap.put("五", match_ctrType3_repay5);
						ctrType3_ctrRepayMap.put("六", match_ctrType3_repay6);
						eloan_pa_repay_way = build_choseKey_by_booleanVal(ctrType3_ctrRepayMap, "、");
					}
				}else{
					//可能A) 2個產品, 都是 {本息均攤、月繳}, 但從 中間期數 開始申請「寬限期」
					//可能B) 2個產品, {1個是 本息均攤}{另1個是 本金均攤}}
				}
			
				if(Util.isEmpty(eloan_pa_repay_way) && Util.equals(VAL_OVERDRAW, distinct_cycl_cross_prod)){
					eloan_pa_repay_way = "一"; // 如為 透支 ，固定選擇{一、自撥款日起，按月付息一次，到期還清本金。}
				}
				
				if(Util.isEmpty(eloan_pa_repay_way) && Util.equals(VAL_MONTH_INTEREST, distinct_cycl_cross_prod)){
					eloan_pa_repay_way = "一"; // 如為 按月計息 ，固定選擇{一、自撥款日起，按月付息一次，到期還清本金。}
				}
			}
			
			if(true){ //判斷是否有「限制清償期間」				
				if(noPPP_seq_set.size()>0){			
					if(true){
						eloan_pa_intr_noPPP_cb = CB_Y_STR;
					}
					LinkedHashMap<String, LinkedHashSet<String>> l140s02d_phase_map = get_distinct_l140s02d_intr_info(l140s02a_list, noPPP_seq_set, map_l140s02cData, map_l140s02dData);					
					boolean build_intr_text = true;
					String traceStr = "noPPP"
						+", is_mapValue_cnt_eq_1=" + is_mapValue_cnt_eq_1(l140s02d_phase_map)
						+", phase_size=" + l140s02d_phase_map.size()
						+")";	
					//~~~~~~
					_debug(traceStr, "distinct_cycl_cross_prod="+distinct_cycl_cross_prod);
					if(is_mapValue_cnt_eq_1(l140s02d_phase_map)){
						if(l140s02d_phase_map.size()==1){ //一段式利率
							String intr_info = "";
							for(String phase: l140s02d_phase_map.keySet()){ //在此段程式之前，已先限制 is_mapValue_cnt_eq_1==true
								intr_info = StringUtils.join(l140s02d_phase_map.get(phase), "");
							}
							_debug(traceStr, "intr_info="+intr_info);
							if(Util.isNotEmpty(intr_info)){
								String[] intr_info_arr = intr_info.split("\\|");
								String rateType = intr_info_arr[2];
								String baseRate = intr_info_arr[3];
								String pmFlag = intr_info_arr[4];
								String pmRate = intr_info_arr[5];
//								String nowRate = intr_info_arr[6];
								String rateFlag = intr_info_arr[7];
								if(Util.equals(CrsUtil.RATE_TYPE_6R, rateType) && (Util.equals("", pmFlag)||Util.equals("P", pmFlag)) ){
									if(Util.equals("2", rateFlag)){ //按日計息 => 機動
										build_intr_text = false;
										if(!build_intr_text){
											eloan_pa_intr_noPPP_1t1 = Util.equals("P", pmFlag)?pmRate:"0";
											eloan_pa_intr_noPPP_baseRate = baseRate;
										}
									}else if(Util.equals("3", rateFlag) 
											&& Util.equals("1", intr_info_arr[8])
											&& Util.equals("1", intr_info_arr[9]) ){ //每月浮動(一段式)
										build_intr_text = false;
										if(!build_intr_text){
											eloan_pa_intr_noPPP_2t1 = Util.equals("P", pmFlag)?pmRate:"0";
											eloan_pa_intr_noPPP_baseRate = baseRate;
										}									
									}else if(Util.equals("1", rateFlag) ){ //固定(一段式)
										build_intr_text = false;
										if(!build_intr_text){
											eloan_pa_intr_noPPP_3t1 = Util.equals("P", pmFlag)?pmRate:"0";
											eloan_pa_intr_noPPP_baseRate = baseRate;
										}
									}								
								}
							}	
						}else{
							//多段式
							build_intr_text = true;
						}	
					}else{
						build_intr_text = true;
					}	
					
					if(!build_intr_text){
						//已填入組字的欄位
					}else{					
						eloan_pa_intr_noPPP_4t1 = build_intr_textStr(l140s02a_list, noPPP_seq_set, map_l140s02dData, distinct_cycl_cross_prod);
					}			
				}
				if(withPPP_seq_set.size()>0){		
					if(true){
						eloan_pa_intr_withPPP_cb = CB_Y_STR;
					}				
					LinkedHashMap<String, LinkedHashSet<String>> l140s02d_phase_map = get_distinct_l140s02d_intr_info(l140s02a_list, withPPP_seq_set, map_l140s02cData, map_l140s02dData);					
					boolean build_intr_text = true;
					String traceStr = "withPPP"
						+", is_mapValue_cnt_eq_1=" + is_mapValue_cnt_eq_1(l140s02d_phase_map)
						+", phase_size=" + l140s02d_phase_map.size()
						+")";
					//~~~~~~
					_debug(traceStr, "distinct_cycl_cross_prod="+distinct_cycl_cross_prod);					
					if(is_mapValue_cnt_eq_1(l140s02d_phase_map) 
							&& Util.equals("1", distinct_cycl_cross_prod)  ){ //月繳
						if(l140s02d_phase_map.size()==1){ //一段式利率
							String intr_info = "";
							for(String phase: l140s02d_phase_map.keySet()){ //在此段程式之前，已先限制 is_mapValue_cnt_eq_1==true
								intr_info = StringUtils.join(l140s02d_phase_map.get(phase), "");
							}
							_debug(traceStr, "intr_info="+intr_info);
							if(Util.isNotEmpty(intr_info)){
								String[] intr_info_arr = intr_info.split("\\|");
								String bgnNum = intr_info_arr[0];
								String endNum = intr_info_arr[1];								
								String rateType = intr_info_arr[2];
								String baseRate = intr_info_arr[3];
								String pmFlag = intr_info_arr[4];
								String pmRate = intr_info_arr[5];								
								String nowRate = intr_info_arr[6];
								String rateFlag = intr_info_arr[7];
								if(Util.equals("1", rateFlag)){ //固定(一段式)
									build_intr_text = false;
									if(!build_intr_text){
										eloan_pa_intr_withPPP_1x1 = bgnNum;
										eloan_pa_intr_withPPP_1x2 = endNum;
										eloan_pa_intr_withPPP_1x3 = nowRate;
									}
								}else if(Util.equals(CrsUtil.RATE_TYPE_6R, rateType) && (Util.equals("", pmFlag)||Util.equals("P", pmFlag)) ){
									if(Util.equals("3", rateFlag) 
											&& Util.equals("1", intr_info_arr[8])
											&& Util.equals("1", intr_info_arr[9]) ){ //每月浮動(一段式)
										build_intr_text = false;
										if(!build_intr_text){
											eloan_pa_intr_withPPP_1y1 = bgnNum;
											eloan_pa_intr_withPPP_1y2 = endNum;
											eloan_pa_intr_withPPP_1y3 = Util.equals("P", pmFlag)?pmRate:"0";
//											eloan_pa_intr_withPPP_1y4 = nowRate;	
											
											if(true){
												eloan_pa_intr_withPPP_baseRate = baseRate;
											}
										}									
									}
								}
							}
						}
						if(l140s02d_phase_map.size()==2){//二段式利率
							String[] intr_info_list = new String[]{"", ""};
							int currentIdx = 0;
							for(String phase: l140s02d_phase_map.keySet()){ //在此段程式之前，已先限制 is_mapValue_cnt_eq_1==true
								intr_info_list[currentIdx] = StringUtils.join(l140s02d_phase_map.get(phase), "");
								++currentIdx;
								if(currentIdx>=2){
									break;
								}
							}
							_debug(traceStr, "intr_info_1st="+intr_info_list[0]);
							_debug(traceStr, "intr_info_2nd="+intr_info_list[1]);
							if(Util.isNotEmpty(intr_info_list[0]) && Util.isNotEmpty(intr_info_list[1])){
								String[] intr_info_1st_arr = intr_info_list[0].split("\\|");
								String[] intr_info_2nd_arr = intr_info_list[1].split("\\|");
								String bgnNum_1st = intr_info_1st_arr[0];
								String endNum_1st = intr_info_1st_arr[1];								
								String rateType_1st = intr_info_1st_arr[2];
								String baseRate_1st = intr_info_1st_arr[3];
								String pmFlag_1st = intr_info_1st_arr[4];
								String pmRate_1st = intr_info_1st_arr[5];
//								String nowRate_1st = intr_info_1st_arr[6];
								String rateFlag_1st = intr_info_1st_arr[7];
								//~~~~~~
								String bgnNum_2nd = intr_info_2nd_arr[0];
								String endNum_2nd = intr_info_2nd_arr[1];								
								String rateType_2nd = intr_info_2nd_arr[2];
								String baseRate_2nd = intr_info_2nd_arr[3];
								String pmFlag_2nd = intr_info_2nd_arr[4];
								String pmRate_2nd = intr_info_2nd_arr[5];
//								String nowRate_2nd = intr_info_2nd_arr[6];
								String rateFlag_2nd = intr_info_2nd_arr[7];
								//~~~~~~
								if(Util.equals(CrsUtil.RATE_TYPE_6R, rateType_1st) && (Util.equals("", pmFlag_1st)||Util.equals("P", pmFlag_1st)) 
									&& Util.equals(CrsUtil.RATE_TYPE_6R, rateType_2nd) && (Util.equals("", pmFlag_2nd)||Util.equals("P", pmFlag_2nd)) ){
									if(Util.equals("3", rateFlag_1st) 
											&& Util.equals("1", intr_info_1st_arr[8])
											&& Util.equals("1", intr_info_1st_arr[9]) 
										&& Util.equals("3", rateFlag_2nd) 
											&& Util.equals("1", intr_info_2nd_arr[8])
											&& Util.equals("1", intr_info_2nd_arr[9]) ){ //每月浮動(二段式)
										build_intr_text = false;
										if(!build_intr_text){
											eloan_pa_intr_withPPP_1y1 = bgnNum_1st;
											eloan_pa_intr_withPPP_1y2 = endNum_1st;
											eloan_pa_intr_withPPP_1y3 = Util.equals("P", pmFlag_1st)?pmRate_1st:"0";
//											eloan_pa_intr_withPPP_1y4 = nowRate_1st;
											
											eloan_pa_intr_withPPP_1y5 = bgnNum_2nd;
											eloan_pa_intr_withPPP_1y6 = endNum_2nd;
											eloan_pa_intr_withPPP_1y7 = Util.equals("P", pmFlag_2nd)?pmRate_2nd:"0";
//											eloan_pa_intr_withPPP_1y8 = nowRate_2nd;
											
											if(Util.equals(baseRate_1st, baseRate_2nd)){
												eloan_pa_intr_withPPP_baseRate = baseRate_1st;
											}
										}									
									}else{
										//phase1及 phase2 有任一為［6R定期浮動］以外
									}
								}
							}							
						}
					}else{
						build_intr_text = true;
					}	
					
					if(!build_intr_text){
						//已填入組字的欄位
					}else{
						eloan_pa_intr_withPPP_2t1 = build_intr_textStr(l140s02a_list, withPPP_seq_set, map_l140s02dData, distinct_cycl_cross_prod);
					}
				}
			}
			
			if(true){
				String distinct_L140S02A_lnPurpose = get_distinct_L140S02A_lnPurpose_cross_prod(l140s02a_list);
				String traceStr = "[cntrNo="+l140m01a.getCntrNo()+"]GorN";
				_debug(traceStr, "{revolve="+(Util.equals(UtilConstants.Cntrdoc.ReUse.循環使用, l140m01a.getReUse())?"Y":"N")+"}"
						+"{eloan_pb_gnteeG_cb="+eloan_pb_gnteeG_cb+", eloan_pb_gnteeN_cb="+eloan_pb_gnteeN_cb+"}"
						+"{distinct_L140S02A_lnPurpose="+distinct_L140S02A_lnPurpose+"}"
					);
				if(Util.equals(eloan_pb_gnteeG_cb, CB_Y_STR)){					
					if(Util.equals("L", distinct_L140S02A_lnPurpose)||Util.equals("2", distinct_L140S02A_lnPurpose)){ //購置住宅貸款（非自用、其他）
						if(Util.equals(UtilConstants.Cntrdoc.ReUse.不循環使用, l140m01a.getReUse())){ //1:不循環使用
							eloan_pb_gnteeG_B_cb = CB_Y_STR;
						}
					}
					if(is_reuse_financialLoan(l140m01a, l140s02a_list)){
						eloan_pb_gnteeG_D_cb = CB_Y_STR;
					}
					if(is_financialLoan_prod02ChkUsedY_or_prod04_prod68ChkUsedY(l140m01a, l140s02a_list)){
						eloan_pb_gnteeG_C_cb = CB_Y_STR;
					}
				}
				if(Util.equals(eloan_pb_gnteeN_cb, CB_Y_STR)){
					if(Util.equals("L", distinct_L140S02A_lnPurpose)||Util.equals("2", distinct_L140S02A_lnPurpose)){ //購置住宅貸款（非自用、其他）
						if(Util.equals(UtilConstants.Cntrdoc.ReUse.不循環使用, l140m01a.getReUse())){ //1:不循環使用
							eloan_pb_gnteeN_B_cb = CB_Y_STR;
						}
					}
					if(is_reuse_financialLoan(l140m01a, l140s02a_list)){
						eloan_pb_gnteeN_D_cb = CB_Y_STR;
					}
					if(is_financialLoan_prod02ChkUsedY_or_prod04_prod68ChkUsedY(l140m01a, l140s02a_list)){
						eloan_pb_gnteeN_C_cb = CB_Y_STR;
					}
				}
			}
		}else{
			/* 在1份契約書，包含多個額度序號
			 * 可能1)額度A 以 丙先生 當一般保證人，額度B 以 丁小姐 當 連保人 
			*/
			L140M01A oneOfL140M01A = null;
			for (String l140m01a_mainId : c340m01b_map.keySet()) {
				L140M01A l140m01a = c340m01b_map.get(l140m01a_mainId);
				oneOfL140M01A = l140m01a;
			}			
			
			if(oneOfL140M01A!=null){
				String l140m01a_custName = Util.trim(oneOfL140M01A.getCustName());
				// eloan_p1_contr_cname = l140m01a_custName;
				eloan_p1_contr_name_m = l140m01a_custName;
				eloan_p9_sig_m_name = l140m01a_custName;
				eloan_p9_recpt_m_name = l140m01a_custName;
				//~~~~~~
				eloan_p9_sig_m_custId = oneOfL140M01A.getCustId();
				eloan_p9_sig_m_addr = get_SIG_ADDR(caseMainId, oneOfL140M01A.getCustId(), oneOfL140M01A.getDupNo()); 
			}
		}
		//******************************************************************************************
		// 填入 map
		//******************************************************************************************
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();

		//J-110-0486_10702_B1001 Web e-Loan契約書總費用年百分率以系統計算帶入以降低錯誤率
		eloan_pc_spTerm_yRate = this.getRate(c340m01a);

		if(true){
			if (true) {
				// _injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P1_CONTR_NO, eloan_p1_contr_no);
				// _injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P1_CONTR_CNAME, eloan_p1_contr_cname);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P1_CONTR_NAME_M, eloan_p1_contr_name_m);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P1_CONTR_NAME_N, eloan_p1_contr_name_n);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P1_CONTR_NAME_G, eloan_p1_contr_name_g);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P1_CONTR_AMT, eloan_p1_contr_amt);
			}
			//=====================================
			// 貸款金額及交付方式
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_A_CB, eloan_pa_deliv_A_cb);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_A_T1, eloan_pa_deliv_A_t1);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_A_T2, eloan_pa_deliv_A_t2);
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_B_CB, eloan_pa_deliv_B_cb);
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_C_CB, eloan_pa_deliv_C_cb);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_C_T1, eloan_pa_deliv_C_t1);
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D_CB, eloan_pa_deliv_D_cb);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D_T1, eloan_pa_deliv_D_t1);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D_T2, eloan_pa_deliv_D_t2);

				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T1, "");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T2, "");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T3, "");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T4, "");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T5, "");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T6, "");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T7, "");
				
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D2_1CB, "");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D2_2CB, "");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D3_T1, "");
				
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_E_CB, eloan_pa_deliv_E_cb);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_E_T1, eloan_pa_deliv_E_t1);
			}
			//=====================================
			// 借款用途
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_PURPOSE_WAY, eloan_pa_purpose_way);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_PURPOSE_G_T1, eloan_pa_purpose_G_t1);
			}
			//=====================================
			// 申請動用方式、動用期限及貸款期間
			if (true) {
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T1, eloan_pa_use_A_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T2, eloan_pa_use_A_t2);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T3, eloan_pa_use_A_t3);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T4, eloan_pa_use_A_t4);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T5, eloan_pa_use_A_t5);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T6, eloan_pa_use_A_t6);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T7, eloan_pa_use_A_t7);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T8, eloan_pa_use_A_t8);
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T1, eloan_pa_use_B_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T2, eloan_pa_use_B_t2);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T3, eloan_pa_use_B_t3);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T4, eloan_pa_use_B_t4);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T5, eloan_pa_use_B_t5);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T6, eloan_pa_use_B_t6);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T7, eloan_pa_use_B_t7);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T8, eloan_pa_use_B_t8);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T9, eloan_pa_use_B_t9);
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T1, eloan_pa_use_C_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T2, eloan_pa_use_C_t2);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T3, eloan_pa_use_C_t3);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T4, eloan_pa_use_C_t4);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T5, eloan_pa_use_C_t5);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T6, eloan_pa_use_C_t6);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T7, eloan_pa_use_C_t7);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T8, eloan_pa_use_C_t8);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T9, eloan_pa_use_C_t9);
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T1, eloan_pa_use_D_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T2, eloan_pa_use_D_t2);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T3, eloan_pa_use_D_t3);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T4, eloan_pa_use_D_t4);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T5, eloan_pa_use_D_t5);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T6, eloan_pa_use_D_t6);
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_E_T1, eloan_pa_use_E_t1);
				}
			}
			//=====================================
			// 貸款本息攤還方式之約定 L140S02E.payWay
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_WAY, eloan_pa_repay_way);
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_D_T1, eloan_pa_repay_D_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_D_T2, eloan_pa_repay_D_t2);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_D_T3, eloan_pa_repay_D_t3);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_D_T4, eloan_pa_repay_D_t4);
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_E_T1, eloan_pa_repay_E_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_E_T2, eloan_pa_repay_E_t2);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_E_T3, eloan_pa_repay_E_t3);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_E_T4, eloan_pa_repay_E_t4);
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_F_T1, eloan_pa_repay_F_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_F_T2, eloan_pa_repay_F_t2);
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_G_T1, eloan_pa_repay_G_t1);
				}
				if (true) {
					//由客戶自行勾選
					// _injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_NOPPP_CB, eloan_pa_repay_noPPP_cb); //prepayment penalty 
					// _injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_WITHPPP_CB, eloan_pa_repay_withPPP_cb);
				}
				
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_WITHPPP_TERM, eloan_pa_repay_withPPP_term);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_ACTNO, eloan_pa_repay_actNo);
				if (true) { 
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO01, eloan_pa_repay_feeNo01); //where codetype='cls1141_feeNo' 01	開辦費
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO02, eloan_pa_repay_feeNo02); //where codetype='cls1141_feeNo' 02	信用查詢費
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO03, eloan_pa_repay_feeNo03); //where codetype='cls1141_feeNo' 03	短期續約作業費
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO04, eloan_pa_repay_feeNo04); //where codetype='cls1141_feeNo' 04	變更授信條件手續費
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO06, eloan_pa_repay_feeNo06); //where codetype='cls1141_feeNo' 06	貸款餘額證明書
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO07, eloan_pa_repay_feeNo07); //where codetype='cls1141_feeNo' 07	補發抵押權塗銷同意書
				}								
			}
			//=====================================
			// 貸款計息方式之約定
			if (true) {
				if (true) { //無限制清償期間
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_CB, eloan_pa_intr_noPPP_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_BASERATE, eloan_pa_intr_noPPP_baseRate);
					// _injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_WAY, eloan_pa_intr_noPPP_way);
					if(true){ //機動計息
						_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_1T1, eloan_pa_intr_noPPP_1t1);
					}
					if(true){ //浮動計息
						_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_2T1, eloan_pa_intr_noPPP_2t1);
					}
					if(true){ //固定利率
						_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_3T1, eloan_pa_intr_noPPP_3t1);
					}
					if(true){ //無限制清償期間_其他
						_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_4T1, eloan_pa_intr_noPPP_4t1);
					}
				}
				
				if(true){ //限制提前清償
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_CB, eloan_pa_intr_withPPP_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_BASERATE, eloan_pa_intr_withPPP_baseRate);
					// _injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_WAY, eloan_pa_intr_withPPP_way);
					if(true){
						if(true){ //固定計息
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1X1, eloan_pa_intr_withPPP_1x1);
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1X2, eloan_pa_intr_withPPP_1x2);
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1X3, eloan_pa_intr_withPPP_1x3);
						}
						if(true){ //浮動計息PartA
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y1, eloan_pa_intr_withPPP_1y1);
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y2, eloan_pa_intr_withPPP_1y2);
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y3, eloan_pa_intr_withPPP_1y3);
							//_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y4, eloan_pa_intr_withPPP_1y4);
						}
						if(true){ //浮動計息PartB
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y5, eloan_pa_intr_withPPP_1y5);
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y6, eloan_pa_intr_withPPP_1y6);
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y7, eloan_pa_intr_withPPP_1y7);
							//_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y8, eloan_pa_intr_withPPP_1y8);
						}
					}
					if(true){ //限制提前清償_其他
						_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_2T1, eloan_pa_intr_withPPP_2t1);
					}
					
				}
				if(true){ //個別約定
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_OTHER_CB, on_when_has_data(eloan_pa_intr_other_t1, CB_Y_STR));
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_OTHER_T1, eloan_pa_intr_other_t1);
				}
			}
			if (true) { //利率調整之通知 => 未約定者，以書面方式為之
				// _injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_notice_1_cb", "");
				// _injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_notice_2_cb", "");
				// _injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_notice_3_cb", "");
			}
			//=====================================
			// 保證條款
			if (true) {
				if (true) { // 連帶保證人保證條款(甲方非屬銀行法第12條之1之貸款對象)
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_CB, eloan_pb_gnteeG_cb);
					//銀行辦理自用住宅放款及消費性放款，不得要求借款人提供連帶保證人。
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_B_CB, eloan_pb_gnteeG_B_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_B_T1, eloan_pb_gnteeG_B_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_C_CB, eloan_pb_gnteeG_C_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_C_T1, eloan_pb_gnteeG_C_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_D_CB, eloan_pb_gnteeG_D_cb);
				}
				if (true) { // 一般保證人保證條款
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_CB, eloan_pb_gnteeN_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_B_CB, eloan_pb_gnteeN_B_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_B_T1, eloan_pb_gnteeN_B_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_C_CB, eloan_pb_gnteeN_C_cb);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_C_T1, eloan_pb_gnteeN_C_t1);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_D_CB, eloan_pb_gnteeN_D_cb);
				}
			}
			//=====================================			
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SERV_TEL_T1, eloan_pc_serv_tel_t1);				
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SERV_FAX_T1, eloan_pc_serv_fax_t1);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SERV_MAIL_T1, eloan_pc_serv_mail_t1);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SERV_OTHER_T1, eloan_pc_serv_other_t1);
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_COURT_LOC, eloan_pc_court_loc);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_COPY_CNT, eloan_pc_copy_cnt);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SPTERM_YRATE, eloan_pc_spTerm_yRate);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SPTERM_NOTE, eloan_pc_spTerm_note);
			}
			
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_M_NAME, eloan_p9_sig_m_name);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_M_CUSTID, eloan_p9_sig_m_custId);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_M_ADDR, eloan_p9_sig_m_addr);
				
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_1N_CB, on_when_has_data(eloan_p9_sig_1N_name, CB_Y_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_1N_NAME, eloan_p9_sig_1N_name);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_1G_CB, on_when_has_data(eloan_p9_sig_1G_name, CB_Y_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_1G_NAME, eloan_p9_sig_1G_name);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_1_CUSTID, eloan_p9_sig_1_custId);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_1_ADDR, eloan_p9_sig_1_addr);

				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_2N_CB, on_when_has_data(eloan_p9_sig_2N_name, CB_Y_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_2N_NAME, eloan_p9_sig_2N_name);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_2G_CB, on_when_has_data(eloan_p9_sig_2G_name, CB_Y_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_2G_NAME, eloan_p9_sig_2G_name);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_2_CUSTID, eloan_p9_sig_2_custId);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_2_ADDR, eloan_p9_sig_2_addr);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_PARTYB_AGENT, eloan_p9_sig_partyB_agent);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_PARTYB_ADDR, eloan_p9_sig_partyB_addr);
			}
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_RECPT_M_NAME, eloan_p9_recpt_m_name);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_RECPT_S_NAME, eloan_p9_recpt_s_name);
			}		
		}

		return paramMap;
	}
	
	public String build_choseKey_by_booleanVal(LinkedHashMap<String, Boolean> dataMap, String splitStr){
		List<String> r_list = new ArrayList<String>();
		for(String itemNameDesc: dataMap.keySet()){
			if(dataMap.get(itemNameDesc)==true){
				r_list.add(itemNameDesc);
			}
		}
		return StringUtils.join(r_list, splitStr);
	}
	
	private String build_choseKey_by_notEmptyKVValue(LinkedHashMap<String, String[]> dataMap, String splitStr, Map<String, String> result){
		LinkedHashMap<String, Boolean> copyMap = new LinkedHashMap<String, Boolean>(); 
		for(String itemNameDesc: dataMap.keySet()){
			LinkedHashSet<String> choseWay = new LinkedHashSet<String>(); 
			for(String assign_key : dataMap.get(itemNameDesc)){
				add_when_has_data(result.get(assign_key), choseWay, itemNameDesc);	
			}
			copyMap.put(itemNameDesc, choseWay.size()>0);
		}		
		return build_choseKey_by_booleanVal(copyMap, splitStr);
	}
	
	/*
		$.form.submit({url: __ajaxHandler, target : "_blank",data : {'_pa' : 'lmsdownloadformhandler' ,'serviceName': "ContractDocService" ,'oid' : responseJSON.mainOid , 'fileDownloadName': 'test.doc', 
			'mock_mode' : 'Y'}});
	*/
	private LinkedHashMap<String, String> mock_ctrType1_paramMap(C340M01A c340m01a)
	throws CapException {
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();
		String rptId = Util.trim(c340m01a.getRptId());
		if (Util.equals(rptId, ContractDocConstants.C340M01A_RptId.V202003) 
			|| Util.equals(rptId, ContractDocConstants.C340M01A_RptId.V202008)) {
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P1_CONTR_NO, "201902-001");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P1_CONTR_CNAME, "陳小姐");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P1_CONTR_NAME_M, "陳小姐");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P1_CONTR_NAME_N, "陳○瑤");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P1_CONTR_NAME_G, "測");
//				String s = NumConverter.numberToChineseFull(16200000)); //壹陸貳零零零零零
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P1_CONTR_AMT, CapCommonUtil.toChineseUpperAmount(String.valueOf(16200000), true)); //壹仟陸佰貳拾萬
			}
			//=====================================
			// 貸款金額及交付方式
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_A_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_A_T1, "活期儲蓄");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_A_T2, "017");
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_B_CB, "");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_B_T1, "X");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_B_T2, "Y");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_B_T3, "Z");
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_C_CB, CB_ON_STR);
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D_CB, "");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D_T1, "X");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_D_T2, "Y");
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_E_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_DELIV_E_T1, "wxyz");
			}
			//=====================================
			// 申請動用方式、動用期限及貸款期間
			if (true) {
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_A_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_A_T1, "3.5");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_A_T2, CapCommonUtil.toChineseUpperAmount(String.valueOf(16200000), true));
				}
				if (true) { //個別約定
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_B_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_B_T1, "甲乙丙丁");
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_Y, "10");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_M, "7");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_BEGY, "107");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_BEGM, "11");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_BEGD, "12");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_ENDY, "117");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_ENDM, "12");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_USE_ENDD, "10");
				}
			}
			//=====================================
			// 貸款本息攤還方式之約定 L140S02E.payWay
			if (true) {
				if (true) { //(無寬限期)攤還本息
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_A_CB, CB_ON_STR);
				}
				if (true) { //(無寬限期)攤還本金
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_B_CB, CB_ON_STR);
				}
				if (true) { //寬限期 + 攤還本息
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_CB, "");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_T1, "1");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_T2, "0");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_T3, "2");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_C_T4, "1");
				}
				if (true) { //寬限期 + 攤還本金
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_CB, "");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_T1, "2");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_T2, "0");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_T3, "3");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_D_T4, "1");
				}
				if (true) { //雙週繳
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_E_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_E_T1, "240");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_E_T2, "20");
				}
				if (true) { //其他方式 
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_F_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_F_T1, "wxyz");
				}
				if (true) { 
					if (true) { 
						_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_NOPPP_CB, CB_ON_STR); //prepayment penalty
					}
					if (true) { 
						_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_WITHPPP_CB, CB_ON_STR);
					}
				}
				String repay_actNo = "";
//				repay_actNo = "00702123456789";
				repay_actNo = "00702123456";
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_ACTNO, repay_actNo);
				if (true) { 
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO01, "0"); //where codetype='cls1141_feeNo' 01	開辦費
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO02, "0"); //where codetype='cls1141_feeNo' 02	信用查詢費
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO03, "0"); //where codetype='cls1141_feeNo' 03	短期續約作業費
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO04, "2000"); //where codetype='cls1141_feeNo' 04	變更授信條件手續費
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO06, "100"); //where codetype='cls1141_feeNo' 06	貸款餘額證明書
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_REPAY_FEENO07, "500");
				}								
			}
			//=====================================
			// 貸款計息方式之約定
			if (true) {
				if (true) { //無限制清償期間
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_BASERATE, "2.3");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_WAY, "A");
					if(true){ //浮動計息
						_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_1T1, "1.1");
						_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_1T2, "2.1");
					}
					if(true){ //固定利率
						_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_2T1, "1.3");
					}
					if(true){ //無限制清償期間_其他
						_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_NOPPP_3T1, "ctrType1_無限制清償期間_其他");
					}
				}
				
				if(true){ //限制提前清償
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_BASERATE, "1.24");
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_WAY, "B");
					if(true){
						if(true){ //固定計息
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1X1, "3");
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1X2, "5");
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1X3, "1.23");
						}
						if(true){ //浮動計息PartA
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y1, "1");
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y2, "12");
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y3, "1.05");
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y4, "2.05");
						}
						if(true){ //浮動計息PartB
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y5, "13");
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y6, "240");
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y7, "1.07");
							_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_1Y8, "2.07");
						}
					}
					if(true){ //限制提前清償_其他
						_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_WITHPPP_2T1, "ctrType1_限制清償期間_其他");
					}
					
				}
				if(true){ //個別約定
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_OTHER_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_INTR_OTHER_T1, "ctrType1_甲乙雙方個別約定之借款計息方式");
				}
			}
			if (true) { //利率調整之通知 => 未約定者，以書面方式為之
				// _injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_notice_1_cb", "");
				// _injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_notice_2_cb", "");
				// _injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PA_notice_3_cb", "");
			}
			//=====================================
			// 保證條款
			if (true) {
				if (true) { // 連帶保證人保證條款(甲方非屬銀行法第12條之1之貸款對象)
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_CB, CB_ON_STR);
					//銀行辦理自用住宅放款及消費性放款，不得要求借款人提供連帶保證人。
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_B_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_B_T1, CapCommonUtil.toChineseUpperAmount(String.valueOf(12340000), true));
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_C_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEG_C_T1, CapCommonUtil.toChineseUpperAmount(String.valueOf(43210000), true));
				}
				if (true) { // 一般保證人保證條款
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_A_CB, CB_ON_STR);//自用住宅放款（不循環動用）
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_B_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_B_T1, CapCommonUtil.toChineseUpperAmount(String.valueOf(56780000), true));
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_C_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PB_GNTEEN_C_T1, CapCommonUtil.toChineseUpperAmount(String.valueOf(67890000), true));
				}
			}
			//=====================================			
			if (true) {
				String br_tel = "02-2571-2568";
				String br_fax = "02-25427152";		
				String br_mail = "<EMAIL>";
				String serv_otherTxt = "甲乙丙丁";
//				br_tel = "";
//				br_fax = "";
				br_mail = "";
				String space_21 = "                     ";
				int width_tel = 12;
				int width_mail = 21;
				if(br_tel.length()<width_tel){
					br_tel = Util.getLeftStr(br_tel+space_21, width_tel);
				}
				if(br_fax.length()<width_tel){
					br_fax = Util.getLeftStr(br_fax+space_21, width_tel);
				}
				if(br_mail.length()<width_mail){
					br_mail = Util.getLeftStr(br_mail+space_21, width_mail);
				}
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SERV_TEL_CB, on_when_has_data(br_tel, CB_ON_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SERV_TEL_T1, br_tel);

				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SERV_FAX_CB, on_when_has_data(br_fax, CB_ON_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SERV_FAX_T1, br_fax);
				
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SERV_MAIL_CB, on_when_has_data(br_mail, CB_ON_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SERV_MAIL_T1, br_mail);
				
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SERV_URL_CB, CB_ON_STR);						
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SERV_CALL_CB, CB_ON_STR);
				
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SERV_OTHER_CB, on_when_has_data(serv_otherTxt, CB_ON_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SERV_OTHER_T1, serv_otherTxt);
			}
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_COURT_LOC, "臺灣花蓮地方");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_COPY_CNT, CapCommonUtil.toChineseUpperAmount(String.valueOf(2), true));
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_PC_SPTERM_YRATE, "1.75");
			}
			
			
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_M_NAME, "陳小姐");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_M_CUSTID, "A123456789");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_M_ADDR, "台北市大安區信義路三段182號1樓");
				
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_1N_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_1N_NAME, "王先生");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_1G_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_1G_NAME, "測甲乙");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_1_CUSTID, "B123456789");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_1_ADDR, "台北市大安區信義路三段182號2樓");

				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_2N_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_2N_NAME, "測丙丁");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_2G_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_2G_NAME, "林小姐");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_2_CUSTID, "C123456789");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_2_ADDR, "台北市大安區信義路三段182號3樓");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_PARTYB_AGENT, "南京東路分公司　經理　測先生");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_SIG_PARTYB_ADDR, "台北市大安區信義路三段182號2樓");
			}
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_RECPT_M_NAME, "陳小姐");
				_injectMap(paramMap, ContractDocConstants.CtrType1.ELOAN_P9_RECPT_S_NAME, "王先生");
			}
		}
		return paramMap;
	}

	private LinkedHashMap<String, String> mock_ctrType2_paramMap(C340M01A c340m01a)
	throws CapException {
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();
		String rptId = Util.trim(c340m01a.getRptId());
		if (Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType2_V202009)) {
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P1_CONTR_NO, "201902-001");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P1_CONTR_CNAME, "陳小姐");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P1_CONTR_NAME_M, "陳小姐");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P1_CONTR_NAME_N, "陳○瑤");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P1_CONTR_NAME_G, "測");
//				String s = NumConverter.numberToChineseFull(16200000)); //壹陸貳零零零零零
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P1_CONTR_AMT, CapCommonUtil.toChineseUpperAmount(String.valueOf(16200000), true)); //壹仟陸佰貳拾萬
			}
			//=====================================
			// 貸款金額及交付方式
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_A_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_A_T1, "活期儲蓄");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_A_T2, "01702123456");
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_B_CB, CB_ON_STR);
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_C_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_C_T1, "01799123456");
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_D_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_DELIV_D_T1, "wxyz");
			}
			//=====================================
			// 借款用途
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_PURPOSE_WAY, "一、五");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_PURPOSE_E_T1, "汽車頭期款");
			}
			//=====================================
			// 申請動用方式、動用期限及貸款期間
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_WAY, "九、一");
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T1, "12");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T2, "111萬");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T3, "25");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T4, "124");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T5, "12");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_A_T6, "31");
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T1, "202");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T2, "11");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T3, "20");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T4, "203");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T5, "11");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T6, "19");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T7, "12");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_B_T8, "222萬");
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T1, "303");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T2, "12");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T3, "17");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T4, "304");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T5, "12");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T6, "16");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T7, "12");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T8, "333萬");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_C_T9, "01703333333");
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T1, "403");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T2, "09");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T3, "05");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T4, "404");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T5, "09");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T6, "04");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T7, "12");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T8, "444萬");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_D_T9, "01744222333");
				}
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_USE_E_T1, "wxuyz");
			}
			//=====================================
			// 貸款本息攤還方式之約定 L140S02E.payWay
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_WAY, "2/3");
				if (true) { 
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_A_T1, "110");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_A_T2, "12");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_A_T3, "31");
				}
				if (true) { 
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_C_T1, "6");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_C_T2, "3");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_C_T3, "7");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_C_T4, "8");
				}
				if (true) { 
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_D_T1, "wwwxyz");
				}
				if (true) { 
					if (true) { 
						_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_NOPPP_CB, CB_ON_STR); //prepayment penalty
					}
					if (true) { 
						_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_WITHPPP_CB, CB_ON_STR);
					}
				}
				String repay_actNo = "00702123456";
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_WITHPPP_TERM, "ctrType2_限制清償期間_人工勾選_違約金自訂條款");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_ACTNO, repay_actNo);
				if (true) { 
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_FEENO01, "3000"); //where codetype='cls1141_feeNo' 01	開辦費
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_FEENO02, "300"); //where codetype='cls1141_feeNo' 02	信用查詢費
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_FEENO03, "30"); //where codetype='cls1141_feeNo' 03	短期續約作業費
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_FEENO04, "2000"); //where codetype='cls1141_feeNo' 04	變更授信條件手續費
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_REPAY_FEENO06, "100"); //where codetype='cls1141_feeNo' 06	貸款餘額證明書					
				}								
			}
			//=====================================
			// 貸款計息方式之約定
			if (true) {
				if(true){ //限制提前清償
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_BASERATE, "1.24");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_WAY, "A");
					if(true){
						if(true){ //固定計息
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1X1, "1");
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1X2, "12");
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1X3, "1.12");
						}
						if(true){ //浮動計息PartA
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y1, "13");
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y2, "24");
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y3, "1.25");
						}
						if(true){ //浮動計息PartB
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y5, "25");
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y6, "36");
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y7, "1.77");
						}
						if(true){ //浮動計息PartC
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y9, "37");
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y10, "48");
							_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_1Y11, "1.99");
						}
					}
					if(true){ //限制提前清償_其他
						_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_WITHPPP_2T1, "ctrType2_限制提前清償_其他");
					}
					
				}
				if (true) { //無限制清償期間
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_BASERATE, "2.3");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_WAY, "B");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_1T1, "1.1");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_2T1, "2.2");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_3T1, "3.3");
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_4T1, "4.4");
					if(true){ //無限制清償期間_其他
						_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_NOPPP_5T1, "ctrType2_無限制清償期間_其他");
					}
				}
				
				if(true){ //個別約定
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_OTHER_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PA_INTR_OTHER_T1, "ctrType2_甲乙雙方個別約定之借款計息方式");
				}
			}
			if (true) { //利率調整之通知 => 未約定者，以書面方式為之
				// _injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_notice_1_cb", "");
				// _injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_notice_2_cb", "");
				// _injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_notice_3_cb", "");
			}
			//=====================================
			// 保證條款
			if (true) {
				if (true) { // 連帶保證人保證條款
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_CB, CB_ON_STR);

					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_E_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_E_T1, CapCommonUtil.toChineseUpperAmount(String.valueOf(12340000), true));
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_D_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEG_D_T1, CapCommonUtil.toChineseUpperAmount(String.valueOf(43210000), true));
				}
				if (true) { // 一般保證人保證條款
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_F_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_F_T1, CapCommonUtil.toChineseUpperAmount(String.valueOf(56780000), true));
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_G_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PB_GNTEEN_G_T1, CapCommonUtil.toChineseUpperAmount(String.valueOf(67890000), true));
				}
			}
			//=====================================			
			if (true) {
				String br_tel = "02-2571-2568";
				String br_fax = "02-25427152";		
				String br_mail = "<EMAIL>";
				String serv_otherTxt = "甲乙丙丁";

				String space_21 = "                     ";
				int width_tel = 12;
				int width_mail = 21;
				if(br_tel.length()<width_tel){
					br_tel = Util.getLeftStr(br_tel+space_21, width_tel);
				}
				if(br_fax.length()<width_tel){
					br_fax = Util.getLeftStr(br_fax+space_21, width_tel);
				}
				if(br_mail.length()<width_mail){
					br_mail = Util.getLeftStr(br_mail+space_21, width_mail);
				}
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SERV_TEL_CB, on_when_has_data(br_tel, CB_ON_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SERV_TEL_T1, br_tel);

				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SERV_FAX_CB, on_when_has_data(br_fax, CB_ON_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SERV_FAX_T1, br_fax);
				
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SERV_MAIL_CB, on_when_has_data(br_mail, CB_ON_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SERV_MAIL_T1, br_mail);
				
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SERV_URL_CB, CB_ON_STR);						
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SERV_CALL_CB, CB_ON_STR);
				
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SERV_OTHER_CB, on_when_has_data(serv_otherTxt, CB_ON_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SERV_OTHER_T1, serv_otherTxt);
			}
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_COURT_LOC, "臺灣花蓮地方");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_COPY_CNT, CapCommonUtil.toChineseUpperAmount(String.valueOf(2), true));
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_PC_SPTERM_YRATE, "1.75");
			}
			
			
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_M_NAME, "陳小姐");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_M_CUSTID, "A123456789");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_M_ADDR, "台北市大安區信義路三段182號1樓");
				
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_1N_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_1N_NAME, "王先生");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_1G_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_1G_NAME, "測甲乙");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_1_CUSTID, "B123456789");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_1_ADDR, "台北市大安區信義路三段182號2樓");

				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_2N_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_2N_NAME, "測丙丁");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_2G_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_2G_NAME, "林小姐");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_2_CUSTID, "C123456789");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_2_ADDR, "台北市大安區信義路三段182號3樓");
				
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_PARTYB_AGENT, "南京東路分公司　經理　測先生");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_SIG_PARTYB_ADDR, "台北市大安區信義路三段182號2樓");
			}
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_RECPT_M_NAME, "陳小姐");
				_injectMap(paramMap, ContractDocConstants.CtrType2.ELOAN_P9_RECPT_S_NAME, "王先生");
			}
		}
		return paramMap;
	}
	
	private LinkedHashMap<String, String> mock_ctrType3_paramMap(C340M01A c340m01a)
	throws CapException {
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();
		String rptId = Util.trim(c340m01a.getRptId());
		if (Util.equals(rptId, ContractDocConstants.C340M01A_RptId.CtrType3_V202008)) {
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P1_CONTR_NO, "201902-001");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P1_CONTR_CNAME, "陳小姐");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P1_CONTR_NAME_M, "陳小姐");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P1_CONTR_NAME_N, "陳○瑤");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P1_CONTR_NAME_G, "測");
//				String s = NumConverter.numberToChineseFull(16200000)); //壹陸貳零零零零零
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P1_CONTR_AMT, CapCommonUtil.toChineseUpperAmount(String.valueOf(16200000), true)); //壹仟陸佰貳拾萬
			}
			//=====================================
			// 貸款金額及交付方式
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_A_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_A_T1, "活期儲蓄");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_A_T2, "01702123456");
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_B_CB, CB_ON_STR);
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_C_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_C_T1, "01702123456");
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D_T1, "X");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D_T2, "Y");
				
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T1  , "台北市大安區信義路三段182號1F");				
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T1A , "台北市大安區");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T1B , "信義路三段182號1F");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T2  , "100,000,000");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T3  , "台灣銀行");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T4  , "109");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T5  , "12");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T6  , "31");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D1_T7  , "99,000,000");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D2_1CB , CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D2_2CB , CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_D3_T1  , "00788123456");
				//~~~~~~
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_E_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_DELIV_E_T1, "wxyz");
			}
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_PURPOSE_WAY, "一、七");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_PURPOSE_G_T1, "汽車頭期款");
			}
			//=====================================
			// 申請動用方式、動用期限及貸款期間
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_WAY, "九、一");
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T1, "109");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T2, "01");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T3, "02");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T4, "110");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T5, "03");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T6, "04");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T7, "x");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_A_T8, "111萬");
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T1, "202");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T2, "10");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T3, "20");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T4, "222");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T5, "13");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T6, "24");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T7, "y");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T8, "222萬");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_B_T9, "01702222222");
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T1, "303");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T2, "36");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T3, "37");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T4, "333");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T5, "38");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T6, "39");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T7, "z");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T8, "333萬");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_C_T9, "01703333333");
				}
				if (true) {
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T1, "六");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T2, "444萬");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T3, "24");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T4, "104");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T5, "24");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_D_T6, "34");
				}
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_USE_E_T1, "wxuyz");
			}
			//=====================================
			// 貸款本息攤還方式之約定 L140S02E.payWay
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_WAY, "2/3");
				if (true) { 
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_D_T1, "1");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_D_T2, "2");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_D_T3, "3");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_D_T4, "4");
				}
				if (true) { 
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_E_T1, "5");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_E_T2, "6");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_E_T3, "7");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_E_T4, "8");
				}
				if (true) { 
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_F_T1, "9");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_F_T2, "20");
				}
				if (true) { 
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_G_T1, "wwwxyz");
				}
				if (true) { 
					if (true) { 
						_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_NOPPP_CB, CB_ON_STR); //prepayment penalty
					}
					if (true) { 
						_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_WITHPPP_CB, CB_ON_STR);
					}
				}
				String repay_actNo = "";
//				repay_actNo = "00702123456789";
				repay_actNo = "00702123456";
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_WITHPPP_TERM, "ctrType3_限制清償期間_人工勾選_違約金自訂條款");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_ACTNO, repay_actNo);
				if (true) { 
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO01, "3000"); //where codetype='cls1141_feeNo' 01	開辦費
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO02, "300"); //where codetype='cls1141_feeNo' 02	信用查詢費
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO03, "30"); //where codetype='cls1141_feeNo' 03	短期續約作業費
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO04, "2000"); //where codetype='cls1141_feeNo' 04	變更授信條件手續費
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO06, "100"); //where codetype='cls1141_feeNo' 06	貸款餘額證明書
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_REPAY_FEENO07, "500");
				}								
			}
			//=====================================
			// 貸款計息方式之約定
			if (true) {
				if(true){ //限制提前清償
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_BASERATE, "1.24");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_WAY, "A");
					if(true){
						if(true){ //固定計息
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1X1, "1");
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1X2, "12");
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1X3, "1.12");
						}
						if(true){ //浮動計息PartA
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y1, "13");
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y2, "24");
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y3, "1.25");
						}
						if(true){ //浮動計息PartB
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y5, "25");
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y6, "240");
							_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_1Y7, "1.77");
						}
					}
					if(true){ //限制提前清償_其他
						_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_WITHPPP_2T1, "ctrType3_限制提前清償_其他");
					}
					
				}
				if (true) { //無限制清償期間
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_BASERATE, "2.3");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_WAY, "B");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_1T1, "1.1");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_2T1, "2.2");
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_3T1, "3.3");
					if(true){ //無限制清償期間_其他
						_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_NOPPP_4T1, "ctrType3_無限制提前清償_其他");
					}
				}
				
				if(true){ //個別約定
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_OTHER_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_INTR_OTHER_T1, "ctrType3_甲乙雙方個別約定之借款計息方式");
				}
			}
			if (true) { //利率調整之通知 => 未約定者，以書面方式為之
				// _injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_notice_1_cb", "");
				// _injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_notice_2_cb", "");
				// _injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PA_notice_3_cb", "");
			}
			//=====================================
			// 保證條款
			if (true) {
				if (true) { // 連帶保證人保證條款(甲方非屬銀行法第12條之1之貸款對象)
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_CB, CB_ON_STR);
					//銀行辦理自用住宅放款及消費性放款，不得要求借款人提供連帶保證人。
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_B_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_B_T1, CapCommonUtil.toChineseUpperAmount(String.valueOf(12340000), true));
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_C_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_C_T1, CapCommonUtil.toChineseUpperAmount(String.valueOf(43210000), true));
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEG_D_CB, CB_ON_STR);
				}
				if (true) { // 一般保證人保證條款
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_B_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_B_T1, CapCommonUtil.toChineseUpperAmount(String.valueOf(56780000), true));
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_C_CB, CB_ON_STR);
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_C_T1, CapCommonUtil.toChineseUpperAmount(String.valueOf(67890000), true));
					_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PB_GNTEEN_D_CB, CB_ON_STR);
				}
			}
			//=====================================			
			if (true) {
				String br_tel = "02-2571-2568";
				String br_fax = "02-25427152";		
				String br_mail = "<EMAIL>";
				String serv_otherTxt = "甲乙丙丁";

				String space_21 = "                     ";
				int width_tel = 12;
				int width_mail = 21;
				if(br_tel.length()<width_tel){
					br_tel = Util.getLeftStr(br_tel+space_21, width_tel);
				}
				if(br_fax.length()<width_tel){
					br_fax = Util.getLeftStr(br_fax+space_21, width_tel);
				}
				if(br_mail.length()<width_mail){
					br_mail = Util.getLeftStr(br_mail+space_21, width_mail);
				}
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SERV_TEL_CB, on_when_has_data(br_tel, CB_ON_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SERV_TEL_T1, br_tel);

				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SERV_FAX_CB, on_when_has_data(br_fax, CB_ON_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SERV_FAX_T1, br_fax);
				
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SERV_MAIL_CB, on_when_has_data(br_mail, CB_ON_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SERV_MAIL_T1, br_mail);
				
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SERV_URL_CB, CB_ON_STR);						
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SERV_CALL_CB, CB_ON_STR);
				
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SERV_OTHER_CB, on_when_has_data(serv_otherTxt, CB_ON_STR));
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SERV_OTHER_T1, serv_otherTxt);
			}
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_COURT_LOC, "臺灣花蓮地方");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_COPY_CNT, CapCommonUtil.toChineseUpperAmount(String.valueOf(2), true));
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_PC_SPTERM_YRATE, "1.75");
			}
			
			
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_M_NAME, "陳小姐");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_M_CUSTID, "A123456789");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_M_ADDR, "台北市大安區信義路三段182號1樓");
				
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_1N_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_1N_NAME, "王先生");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_1G_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_1G_NAME, "測甲乙");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_1_CUSTID, "B123456789");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_1_ADDR, "台北市大安區信義路三段182號2樓");

				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_2N_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_2N_NAME, "測丙丁");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_2G_CB, CB_ON_STR);
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_2G_NAME, "林小姐");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_2_CUSTID, "C123456789");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_2_ADDR, "台北市大安區信義路三段182號3樓");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_PARTYB_AGENT, "南京東路分公司　經理　測先生");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_SIG_PARTYB_ADDR, "台北市大安區信義路三段182號2樓");
			}
			if (true) {
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_RECPT_M_NAME, "陳小姐");
				_injectMap(paramMap, ContractDocConstants.CtrType3.ELOAN_P9_RECPT_S_NAME, "王先生");
			}
		}
		return paramMap;
	}
	
	private String ploan_param_contractVersion(C340M01A c340m01a){
		if(Util.equals(c340m01a.getCtrType(), ContractDocConstants.C340M01A_CtrType.Type_A)){
			if(Util.equals(c340m01a.getRptId(), ContractDocConstants.C340M01A_RptId.CtrTypeA_V202401)){
				return "202401";
			}else if(Util.equals(c340m01a.getRptId(), ContractDocConstants.C340M01A_RptId.CtrTypeA_V202307)){
				return "202307";
			}else if(Util.equals(c340m01a.getRptId(), ContractDocConstants.C340M01A_RptId.CtrTypeA_V202304)){
				return "202304";
			}else if(Util.equals(c340m01a.getRptId(), ContractDocConstants.C340M01A_RptId.CtrTypeA_V202212)){
				return "V202212";
			}else if(Util.equals(c340m01a.getRptId(), ContractDocConstants.C340M01A_RptId.CtrTypeA_V202209)){
				return "V202209";
			}else if(Util.equals(c340m01a.getRptId(), ContractDocConstants.C340M01A_RptId.CtrTypeA_V202206)){
				return "V202206";
			}else if(Util.equals(c340m01a.getRptId(), ContractDocConstants.C340M01A_RptId.CtrTypeA_V202011)){
				return "V202011";
			}else if(Util.equals(c340m01a.getRptId(), ContractDocConstants.C340M01A_RptId.CtrTypeA_V202008)){
				return "V202008";
			}else if(Util.isNotEmpty(c340m01a.getRptId())){
				return c340m01a.getRptId().replace("CtrTypeA_","");
			}
			return "wrongVersion";
		}else if(Util.equals(c340m01a.getCtrType(), ContractDocConstants.C340M01A_CtrType.Type_B)){
			if(Util.equals(c340m01a.getRptId(), ContractDocConstants.C340M01A_RptId.CtrTypeB_V202401)){
				return "202401";
			}else if(Util.equals(c340m01a.getRptId(), ContractDocConstants.C340M01A_RptId.CtrTypeB_V202309)){
				return "202309";
			}
			return "wrongVersion";
		}else{
			return "wrongType";	
		}		
	}
	
	private String ploan_param_laborContractVersion(C340M01A c340m01a){
		if(Util.equals(c340m01a.getCtrType(), ContractDocConstants.C340M01A_CtrType.Type_L)){
			if(Util.equals(c340m01a.getRptId(), ContractDocConstants.C340M01A_RptId.CtrTypeL_V202106)){
				return "V202106";
			}
			return "wrongVersion";
		}else{
			return "wrongType";	
		}		
	}
	
	@Override
	public JSONObject ploan_sendSigningContract(C340M01A c340m01a)
	throws CapException{
		if(Util.equals(c340m01a.getCtrType(), ContractDocConstants.C340M01A_CtrType.Type_A)){
			return _ploan_sendSigningContract_ctrType_A(c340m01a);
		}else if(Util.equals(c340m01a.getCtrType(), ContractDocConstants.C340M01A_CtrType.Type_B)){
			return _ploan_sendSigningContract_ctrType_B(c340m01a);
		}else if(Util.equals(c340m01a.getCtrType(), ContractDocConstants.C340M01A_CtrType.Type_L)){
			return _ploan_sendSigningContract_ctrType_L(c340m01a);
		}
		return null;
	}
	private JSONObject _ploan_sendSigningContract_ctrType_A(C340M01A c340m01a) throws CapException {
		String l120m01a_mainId = c340m01a.getCaseMainId();
		String custId = c340m01a.getCustId();
		String dupNo = c340m01a.getDupNo();		
		C120S01A c120s01a_MainBorrower = clsService.findC120S01A(l120m01a_mainId, custId, dupNo);
		List<C340M01B> c340m01b_list = clsService.findC340M01B(c340m01a.getMainId());
		//=================
		PLOAN004 ploanObj = new PLOAN004();
		ploanObj.setContractNo(c340m01a.getPloanCtrNo());
		ploanObj.setContractVersion(ploan_param_contractVersion(c340m01a));		
		ploanObj.setBranchCode(c340m01a.getOwnBrId());
		ploanObj.setBorrowerName(Util.trim(c340m01a.getCustName()));
		ploanObj.setBorrowerId(custId);
		if(c120s01a_MainBorrower!=null){
			ploanObj.setBorrowerBirthDate(convert_to_ploan_dateColumn(c120s01a_MainBorrower.getBirthday()));
			//ploanObj.setBorrowerMobileNumber(Util.trim(c120s01a_MainBorrower.getMTel()));
			//ploanObj.setBorrowerEmail(Util.trim(c120s01a_MainBorrower.getEmail()));
		}
		
		if(c340m01b_list.size()>0){
			C340M01B c340m01b = c340m01b_list.get(0);
			L140M01A l140m01a = clsService.findL140M01A_mainId(c340m01b.getTabMainId());
			if(l140m01a!=null){
				List<L140S01A> l140s01a_list = clsService.findL140S01A(l140m01a);
				if(l140s01a_list.size()>0){
					L140S01A l140s01a = l140s01a_list.get(0);
					if(true){ //!Util.equals(l140s01a.getCustPos(), "S")){ //排除
						String secBorrower_id = Util.trim(l140s01a.getCustId());
						C120S01A c120s01a_SecBorrower = clsService.findC120S01A(l120m01a_mainId, secBorrower_id, l140s01a.getDupNo());
						if(c120s01a_SecBorrower!=null){
							ploanObj.setRelatedPersonType(Util.trim(l140s01a.getCustPos()));
							ploanObj.setRelatedPersonName(Util.trim(l140s01a.getCustName()));
							ploanObj.setRelatedPersonId(secBorrower_id);
							ploanObj.setRelatedPersonBirthDate(convert_to_ploan_dateColumn(c120s01a_SecBorrower.getBirthday()));
							ploanObj.setRelatedPersonMobileNumber(Util.trim(c120s01a_SecBorrower.getMTel()));
							ploanObj.setRelatedPersonEmail(Util.trim(c120s01a_SecBorrower.getEmail()));
						}
					}
				}
				L140M03A l140m03a = l140m01a.getL140M03A();
				if(l140m03a!=null){//J-112-0206 歡喜信貸員工認股貸款適用條款
					ploanObj.setStaffRule(Util.trim(l140m03a.getStaffRuleYN()));
				}
			}
		}
		
		ploanObj.setProductCode("PA"); //{PA:信貸}
		ploanObj.setExpiredDate(convert_to_ploan_dateColumn(c340m01a.getPloanCtrExprDate()));



		Map<String, Object> map = new HashMap<String, Object>();
		JSONObject jsonObject = null;
		List<C340M01C> c340m01c_list = clsService.findC340M01C(c340m01a.getMainId());
		for (C340M01C c340m01c : c340m01c_list) {
			String c340m01c_jsonData = Util.trim(c340m01c.getJsonData());
			try {
				jsonObject = DataParse.toJSON(c340m01c_jsonData);
			} catch (CapException e) {
				e.printStackTrace();
			}

			LMSUtil.addJsonToMap(map, jsonObject);
		}
		//若經辦有更改過，以{更改過}的為主
		ploanObj.setBorrowerMobileNumber(Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_BORROWERMOBILENUMBER)));
		ploanObj.setBorrowerEmail(Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_BORROWEREMAIL)));

		if(true) {
			String courtName = Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_COURT_NAME));
			if(Util.isEmpty(courtName)){
				String html_space = "&nbsp;";
				courtName = StringUtils.repeat(html_space, 6);
			}
			ploanObj.setCourtName(courtName);
		}
		if(true) {
			PLOAN004_loanConditionInfo loanConditionInfo = ploanObj.getLoanConditionInfo();
			loanConditionInfo.setLoanAmt(CapMath.getBigDecimal(MapUtils.getString(map, "loanAmt")));
			loanConditionInfo.setLoanPeriod(CapMath.getBigDecimal(MapUtils.getString(map, "loanPeriod")));
			//分隔字串 
			// String split_loanPurpose = "|"; //UtilConstants.Mark.SPILT_MARK			
			// loanConditionInfo.setLoanPurpose(StringUtils.join(StringUtils.split(MapUtils.getString(map, "loanPurpose"), UtilConstants.Mark.MARKDAN), split_loanPurpose));
			if(true){
				if( clsService.is_function_on_codetype("CtrTypeA_V202209")){
					loanConditionInfo.getLoanPurposeInfo().add(new PLOAN004_loanConditionInfo_loanPurposeObj("個人週轉金", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_N))?"Y":"N"));
					loanConditionInfo.getLoanPurposeInfo().add(new PLOAN004_loanConditionInfo_loanPurposeObj("個人消費性用途", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_O))?"Y":"N"));
					loanConditionInfo.getLoanPurposeInfo().add(new PLOAN004_loanConditionInfo_loanPurposeObj("投資理財", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_G))?"Y":"N"));
					loanConditionInfo.getLoanPurposeInfo().add(new PLOAN004_loanConditionInfo_loanPurposeObj("企業員工認購股票", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_P))?"Y":"N"));
				}
				else{
					loanConditionInfo.getLoanPurposeInfo().add(new PLOAN004_loanConditionInfo_loanPurposeObj("週轉", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_C))?"Y":"N"));
					loanConditionInfo.getLoanPurposeInfo().add(new PLOAN004_loanConditionInfo_loanPurposeObj("投資理財", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_G))?"Y":"N"));
					loanConditionInfo.getLoanPurposeInfo().add(new PLOAN004_loanConditionInfo_loanPurposeObj("購置耐久性消費品", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_H))?"Y":"N"));
					loanConditionInfo.getLoanPurposeInfo().add(new PLOAN004_loanConditionInfo_loanPurposeObj("子女教育", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_J))?"Y":"N"));
					loanConditionInfo.getLoanPurposeInfo().add(new PLOAN004_loanConditionInfo_loanPurposeObj("繳稅", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_K))?"Y":"N"));
					loanConditionInfo.getLoanPurposeInfo().add(new PLOAN004_loanConditionInfo_loanPurposeObj("留學/遊學", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_F))?"Y":"N"));
				}
				String loanPurpose_otherDesc = Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_OTHERDESC));
				if(Util.isEmpty(loanPurpose_otherDesc)){
					loanConditionInfo.getLoanPurposeInfo().add(new PLOAN004_loanConditionInfo_loanPurposeObj("其他", "N"));	
				}else{
					loanConditionInfo.getLoanPurposeInfo().add(new PLOAN004_loanConditionInfo_loanPurposeObj("其他："+loanPurpose_otherDesc, "Y"));
				}
			}
			loanConditionInfo.setDrawDownType(MapUtils.getString(map, "drawDownType"));
			loanConditionInfo.setPreliminaryFee(CapMath.getBigDecimal(MapUtils.getString(map, "preliminaryFee")));
			loanConditionInfo.setCreditCheckFee(CapMath.getBigDecimal(MapUtils.getString(map, "creditCheckFee")));
			loanConditionInfo.setOneTimeFee(CapMath.getBigDecimal(MapUtils.getString(map, "preliminaryFee"))
					.add(CapMath.getBigDecimal(MapUtils.getString(map, "creditCheckFee"))));
			loanConditionInfo.setRepaymentMethod(MapUtils.getString(map, "repaymentMethod"));
			String lendingPlanInfo_showOption = MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_LENDING_PLAN_OPTION);
			if(true){
				loanConditionInfo.setLendingPlan(LMSUtil.getDesc(get_ploan_lendingPlanInfo_showOption(), lendingPlanInfo_showOption));	
			}			
			BigDecimal annualPercentageRate = CapMath.getBigDecimal(MapUtils.getString(map, "annualPercentageRate"));
			List<String> rateDescDefaultList = this.getCtrTypeA_rateDesc_default();
			List<String> rateDescChoseList = this.geCtrTypeA_rateDesc(jsonObject);
			
			PLOAN004.PLOAN004_loanConditionInfo_lendingPlanInfo lendingPlanInfo = loanConditionInfo.getLendingPlanInfo();
			lendingPlanInfo.setShowOption(lendingPlanInfo_showOption);
			//~~~~~
			lendingPlanInfo.setAdvancedRedemptionTitle(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_ADVANCED_REDEMPT_TITLE));						
			lendingPlanInfo.setAdvancedRedemptionDesc(Arrays.asList(StringUtils.split(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_ADVANCED_REDEMPT_DESC), "\n"))); //傳送 「已拆分換行後」 的文字
			lendingPlanInfo.setAdvancedRateTitle(MapUtils.getString(map, "advancedRateTitle"));
			lendingPlanInfo.setAdvancedRateDesc(rateDescDefaultList);
			lendingPlanInfo.setAdvancedAPR(null);
			if(Util.equals("1", lendingPlanInfo_showOption)){
				lendingPlanInfo.setAdvancedRateDesc(rateDescChoseList);
				lendingPlanInfo.setAdvancedAPR(annualPercentageRate);	
			}
			//~~~~~
			lendingPlanInfo.setLimitedRedemptionTitle(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_LIMITED_REDEMPT_TITLE));
			lendingPlanInfo.setLimitedRedemptionDesc(Arrays.asList(StringUtils.split(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_LIMITED_REDEMPT_DESC), "\n"))); //傳送 「已拆分換行後」 的文字
			lendingPlanInfo.setLimitedRateTitle(MapUtils.getString(map, "limitedRateTitle"));
			lendingPlanInfo.setLimitedRateDesc(rateDescDefaultList);
			lendingPlanInfo.setLimitedAPR(null);
			if(Util.equals("2", lendingPlanInfo_showOption)){
				lendingPlanInfo.setLimitedRateDesc(rateDescChoseList);
				lendingPlanInfo.setLimitedAPR(annualPercentageRate);	
			}
			//===========================
			if(true){
				lendingPlanInfo.setOtherInfoTitle("");
				lendingPlanInfo.setOtherInfoDesc(Arrays.asList(""));	
			}			
			boolean add_OtherInfoDesc_to_rateDesc = clsService.is_function_on_codetype("add_OtherInfoDesc_to_rateDesc");
			String otherInfoTitle = Util.trim(MapUtils.getString(map, "otherInfoTitle", ""));
			if(add_OtherInfoDesc_to_rateDesc){ //將 行員信貸方案備註 加到 利率文字 下面

				if(Util.isNotEmpty(otherInfoTitle)){ //有「方案備註」
					List<Object> otherInfoDesc_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsonObject, ContractDocConstants.CtrTypeA.PLOAN_OTHERINFODESC);
					List<String> tmp_list = new ArrayList<String>();
					for(Object otherInfoDesc : otherInfoDesc_list){
						String otherInfoDesc_trim = Util.trim(otherInfoDesc);
						if(Util.isNotEmpty(otherInfoDesc_trim)){
							tmp_list.add(otherInfoDesc_trim);	
						}
						
					}
					if(tmp_list.size()>0){
						if(Util.equals("1", lendingPlanInfo_showOption)){
							lendingPlanInfo.getAdvancedRateDesc().addAll(tmp_list);
						}else if(Util.equals("2", lendingPlanInfo_showOption)){
							lendingPlanInfo.getLimitedRateDesc().addAll(tmp_list);
						}	
					}					
				}
			}else{
				//原模式
				lendingPlanInfo.setOtherInfoTitle(otherInfoTitle);
				if(Util.isNotEmpty(Util.trim(lendingPlanInfo.getOtherInfoTitle()))){
					List<Object> otherInfoDesc_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsonObject, ContractDocConstants.CtrTypeA.PLOAN_OTHERINFODESC);
					List<String> tmp_list = new ArrayList<String>();
					for(Object otherInfoDesc : otherInfoDesc_list){
						String otherInfoDesc_trim = Util.trim(otherInfoDesc);
						if(Util.isNotEmpty(otherInfoDesc_trim)){
							tmp_list.add(otherInfoDesc_trim);	
						}
					}	
					if(tmp_list.size()>0){
						lendingPlanInfo.setOtherInfoDesc(tmp_list);
					}
				}else{
					lendingPlanInfo.setOtherInfoDesc(Arrays.asList(""));	
				}	
			}
						
		}
		if(true) {
			PLOAN004_guaranteeInfo guaranteeInfo = ploanObj.getGuaranteeInfo();
			guaranteeInfo.setGeneralGuaranteePlan(MapUtils.getString(map, "generalGuaranteePlan"));			
			guaranteeInfo.setGeneralGuaranteePlanInfo(Arrays.asList(StringUtils.split(MapUtils.getString(map, "generalGuaranteePlanInfo"), "\n")));
			guaranteeInfo.setJointGuaranteePlan(MapUtils.getString(map, "jointGuaranteePlan"));
			guaranteeInfo.setJointGuaranteePlanInfo(Arrays.asList(StringUtils.split(MapUtils.getString(map, "jointGuaranteePlanInfo"), "\n")));
			Object val_guaranteeAmt = MapUtils.getObject(map, ContractDocConstants.CtrTypeA.PLOAN_GUARANTEEAMT);
			if(Util.isNotEmpty(val_guaranteeAmt)){
				guaranteeInfo.setGuaranteeAmt(CrsUtil.parseBigDecimal(val_guaranteeAmt));	
			}			
		}
		if(true) {
			List<Object> acctNo_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsonObject, ContractDocConstants.CtrTypeA.PLOAN_ACCTNO_LIST);
			for(Object acctNo : acctNo_list){
				ploanObj.getLoanAcct().add(new PLOAN004_loanAcct(UtilConstants.兆豐銀行代碼, Util.trim(acctNo)));
			}
		}
		
		if(Util.equals("Y", MapUtils.getString(map,ContractDocConstants.CtrTypeA.PLOAN_IS_CNTRNO_BELONG_CO70647919_C101))){
			/*
			  	以 ContractDocConstants.CtrTypeA.PLOAN_IS_CNTRNO_BELONG_CO70647919_C101 去查找
			  	某些已知欄位(givenApprBegDate , givenApprEndDate) 在   CLS3401ServiceImpl::init_C340RelateCtrTypeA(...) 就已填入
			 */
			ploanObj.setLoanPlan(MapUtils.getString(map, "loanPlan"));
			ploanObj.setGrpCntrNo(MapUtils.getString(map, "grpCntrNo"));
			ploanObj.setGivenApprBegDate(convert_to_ploan_dateColumn(TWNDate.valueOf(MapUtils.getString(map, "givenApprBegDate"))));
			ploanObj.setGivenApprEndDate(convert_to_ploan_dateColumn(TWNDate.valueOf(MapUtils.getString(map, "givenApprEndDate"))));
			ploanObj.setPayeeBankCode(MapUtils.getString(map, "payeeBankCode"));
			ploanObj.setPayeeBankAccountNo(MapUtils.getString(map, "payeeBankAccountNo"));
			ploanObj.setPayeeBankAccountName(MapUtils.getString(map, "payeeBankAccountName"));
			ploanObj.setPayeeTotalAmt(CrsUtil.parseBigDecimal(MapUtils.getObject(map, "payeeTotalAmt")));
			ploanObj.setPayeeRemittance(CrsUtil.parseBigDecimal(MapUtils.getObject(map, "payeeRemittance")));
			ploanObj.setPayeeSelfProvide(CrsUtil.parseBigDecimal(MapUtils.getObject(map, "payeeSelfProvide")));;
		}

		//J-111-0227 增加基礎利率
		if(true){
			BigDecimal baseRate = null;
			if(Util.isNotEmpty(jsonObject.optString("rateRange1Param05"))){
				baseRate = Util.parseBigDecimal(jsonObject.optString("rateRange1Param05"));
			}else if(Util.isNotEmpty(jsonObject.optString("rateRange2Param05"))){
				baseRate = Util.parseBigDecimal(jsonObject.optString("rateRange2Param05"));
			}

			ploanObj.setBaseRate(baseRate);
		}

		//J-111-0378 PLOAN對保契約書增加攤還表
		if (true) {


			String rateRange1 = jsonObject.optString(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1);
			String rateRange2 = jsonObject.optString(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2);

		/*
			rateRange?Param01~rateRange?Param02 第X期~第Y期

			rateRange?Param03(固定利率的利率值)
			rateRange?Param04(利率的中文描述)
			rateRange?Param05(baseRate) + rateRange?Param06(plusRate) = rateRange?Param07(finalRate)
		 */
			String val_rateRange1Type01 = jsonObject.optString(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_TYPE01);
			String val_rateRange2Type01 = jsonObject.optString(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2_TYPE01);
			if ("Y".equals(rateRange1)) {
				C340M01B c340m01b = c340m01b_list.get(0);
				L140M01A l140m01a = clsService.findL140M01A_mainId(c340m01b.getTabMainId());
				L140S02A l140s02a = clsService.findL140S02A(l140m01a).get(0);
				L140S02D l140s02d = l140s02a != null ? clsService.findL140S02D_orderByPhase(l140m01a.getMainId(), l140s02a.getSeq(),
						"Y").get(0) : null;
				PLOAN004_rate rateRange = new PLOAN004.PLOAN004_rate();
				String rateRange1_rateDesc_M3 = Util.trim(jsonObject.optString(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_RATEDESC_M3));

				rateRange.setRate_Type(Util.isNotEmpty(l140s02d)?l140s02d.getRateType() : null);
				if (Util.isNotEmpty(rateRange1_rateDesc_M3)) {
					rateRange.setRate_Bgn("1");
					rateRange.setRate_End(jsonObject.optString("loanPeriod"));
					List<L140S02D> l140s02ds = new ArrayList<L140S02D>();
					l140s02ds.add(l140s02d);
					boolean is_only_MR_and_pmRate_0 = ClsUtility.is_only_MR_and_pmRate_0(l140s02ds);
//					if (Util.isNotEmpty(l140s02d)){
//						rateRange.setRate(l140s02d.getNowRate().toString());
//					}
//					else{
						List<Map<String, Object>> lrRate_list = misMislnratService.findMislnratByLRRate(CrsUtil.RATE_TYPE_M3, "TWD" );
						if (is_only_MR_and_pmRate_0) {
							lrRate_list = misMislnratService.findMislnratByLRRate(CrsUtil.RATE_TYPE_MR, "TWD" );
						}
						String rateValue = "";
						if(lrRate_list.size() > 0){
							rateValue = LMSUtil.pretty_numStr(CrsUtil.parseBigDecimal(MapUtils.getObject(lrRate_list.get(0), "LR_RATE")));
						}
						rateRange.setRate(rateValue);
//					}

				} else {
					if ("Y".equals(val_rateRange1Type01)) {
						//固定利率
						rateRange.setRate_Bgn(jsonObject.optString("rateRange1Param01"));
						rateRange.setRate_End(jsonObject.optString("rateRange1Param02"));
						rateRange.setRate(jsonObject.optString("rateRange1Param03"));
					} else {
						//浮動利率
						rateRange.setRate_Bgn(jsonObject.optString("rateRange1Param01"));
						rateRange.setRate_End(jsonObject.optString("rateRange1Param02"));
						rateRange.setRate(jsonObject.optString("rateRange1Param07"));
					}
				}
				ploanObj.getRateList().add(rateRange);
			}
			if ("Y".equals(rateRange2)) {
				C340M01B c340m01b = c340m01b_list.get(0);
				L140M01A l140m01a = clsService.findL140M01A_mainId(c340m01b.getTabMainId());
				L140S02A l140s02a = clsService.findL140S02A(l140m01a).get(0);
				L140S02D l140s02d = l140s02a != null ? clsService.findL140S02D_orderByPhase(l140m01a.getMainId(), l140s02a.getSeq(),
						"Y").get(1) : null;
				PLOAN004_rate rateRange = new PLOAN004.PLOAN004_rate();

				rateRange.setRate_Type(Util.isNotEmpty(l140s02d)?l140s02d.getRateType() : null);
				if ("Y".equals(val_rateRange2Type01)) {
					//固定利率
					rateRange.setRate_Bgn(jsonObject.optString("rateRange2Param01"));
					rateRange.setRate_End(jsonObject.optString("rateRange2Param02"));
					rateRange.setRate(jsonObject.optString("rateRange2Param03"));
				} else {
					//浮動利率
					rateRange.setRate_Bgn(jsonObject.optString("rateRange2Param01"));
					rateRange.setRate_End(jsonObject.optString("rateRange2Param02"));
					rateRange.setRate(jsonObject.optString("rateRange2Param07"));
				}
				ploanObj.getRateList().add(rateRange);
			}
		}

		//J-112-0205_10702_B1001 Web e-Loan新增代償相關欄位
		String isRepayment = Util.trim(MapUtils.getString(map, "isRepayment"));
		if (Util.isEmpty(isRepayment)) {
			isRepayment = UtilConstants.DEFAULT.否;
		}
		ploanObj.setIsRepayment(isRepayment);
		if (Util.equals(isRepayment, UtilConstants.DEFAULT.是)) {
			List<PLOAN004_repayment> repaymentList =  ploanObj.getRepaymentList();
			String repayList =Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_REPAYMENT_LIST));
			if (Util.isNotEmpty(repayList)) {
				List<Object> repayment_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsonObject, ContractDocConstants.CtrTypeA.PLOAN_REPAYMENT_LIST);
				for (Object ob : repayment_list) {
					PLOAN004_repayment repayment = this.to_outputObj(ob.toString(), PLOAN004_repayment.class);
					ploanObj.getRepaymentList().add(repayment);
				}
			}
		}

		//J-113-0435 web eLoan增加年輕族群註記
		int age = clsService.getAge(c120s01a_MainBorrower.getBirthday());
		String isBorrowerYouth = "N";
		if(18 <= age && age < 22){
			isBorrowerYouth = "Y";
		}
		ploanObj.setIsBorrowerYouth(isBorrowerYouth);

		JSONObject jsonObj = pLoanGwClient.send_contract(ploanObj);
		return jsonObj;
	}

	private JSONObject _ploan_sendSigningContract_ctrType_B(C340M01A c340m01a) throws CapException {
		String html_space = "&nbsp;";
		String html_space_3 = StringUtils.repeat(html_space, 3);
		String html_space_6 = StringUtils.repeat(html_space, 3); // 6);

		String l120m01a_mainId = c340m01a.getCaseMainId();
		String custId = c340m01a.getCustId();
		String dupNo = c340m01a.getDupNo();
		C120S01A c120s01a_MainBorrower = clsService.findC120S01A(l120m01a_mainId, custId, dupNo);
		List<C340M01B> c340m01b_list = clsService.findC340M01B(c340m01a.getMainId());
		//=================
		PLOAN019 ploanObj = new PLOAN019();
		ploanObj.setContractNo(c340m01a.getPloanCtrNo());
		ploanObj.setContractVersion(ploan_param_contractVersion(c340m01a));
		ploanObj.setBranchCode(c340m01a.getOwnBrId());
		ploanObj.setBorrowerName(Util.trim(c340m01a.getCustName()));
		ploanObj.setBorrowerId(custId);
		if(c120s01a_MainBorrower!=null){
			ploanObj.setBorrowerBirthDate(convert_to_ploan_dateColumn(c120s01a_MainBorrower.getBirthday()));
			//ploanObj.setBorrowerMobileNumber(Util.trim(c120s01a_MainBorrower.getMTel()));
			//ploanObj.setBorrowerEmail(Util.trim(c120s01a_MainBorrower.getEmail()));
		}

		if(c340m01b_list.size()>0){
			C340M01B c340m01b = c340m01b_list.get(0);
			L140M01A l140m01a = clsService.findL140M01A_mainId(c340m01b.getTabMainId());
			if(l140m01a!=null){
				List<L140S01A> l140s01a_list = clsService.findL140S01A(l140m01a);
				if(l140s01a_list.size()>0){
					L140S01A l140s01a = l140s01a_list.get(0);
					if(true){ //!Util.equals(l140s01a.getCustPos(), "S")){ //排除
						String secBorrower_id = Util.trim(l140s01a.getCustId());
						C120S01A c120s01a_SecBorrower = clsService.findC120S01A(l120m01a_mainId, secBorrower_id, l140s01a.getDupNo());
						if(c120s01a_SecBorrower!=null){
							ploanObj.setRelatedPersonType(Util.trim(l140s01a.getCustPos()));
							ploanObj.setRelatedPersonName(Util.trim(l140s01a.getCustName()));
							ploanObj.setRelatedPersonId(secBorrower_id);
							ploanObj.setRelatedPersonBirthDate(convert_to_ploan_dateColumn(c120s01a_SecBorrower.getBirthday()));
							ploanObj.setRelatedPersonMobileNumber(Util.trim(c120s01a_SecBorrower.getMTel()));
							ploanObj.setRelatedPersonEmail(Util.trim(c120s01a_SecBorrower.getEmail()));
						}
					}
				}
			}
		}

		ploanObj.setProductCode("HA"); //{HA:房貸}
		ploanObj.setExpiredDate(convert_to_ploan_dateColumn(c340m01a.getPloanCtrExprDate()));

		Map<String, Object> map = new HashMap<String, Object>();
		JSONObject jsonObject = null;
		C340M01C c340m01c = clsService.findC340M01C(c340m01a.getMainId(), ContractDocConstants.C340M01C_ItemType.TYPE_0);
		if (c340m01c != null) {
			String c340m01c_jsonData = Util.trim(c340m01c.getJsonData());
			try {
				jsonObject = DataParse.toJSON(c340m01c_jsonData);
			} catch (CapException e) {
				e.printStackTrace();
			}

			LMSUtil.addJsonToMap(map, jsonObject);
		}
		//若經辦有更改過，以{更改過}的為主
		ploanObj.setBorrowerMobileNumber(Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_BORROWERMOBILENUMBER)));
		ploanObj.setBorrowerEmail(Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_BORROWEREMAIL)));

		if(true) {
			String courtName = Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_COURT_NAME));
			if(Util.isEmpty(courtName)){
				courtName = html_space_6;
			}
			ploanObj.setCourtName(courtName);
		}
		if(true) {
			PLOAN019_loanConditionInfo loanConditionInfo = ploanObj.getLoanConditionInfo();
			loanConditionInfo.setLoanAmt(CapMath.getBigDecimal(MapUtils.getString(map, "loanAmt")));
			loanConditionInfo.setLoanPeriod(CapMath.getBigDecimal(MapUtils.getString(map, "loanPeriod")));
			//分隔字串
			// String split_loanPurpose = "|"; //UtilConstants.Mark.SPILT_MARK
			// loanConditionInfo.setLoanPurpose(StringUtils.join(StringUtils.split(MapUtils.getString(map, "loanPurpose"), UtilConstants.Mark.MARKDAN), split_loanPurpose));
			if(true){
				loanConditionInfo.getLoanPurposeInfo().add(new PLOAN019_loanConditionInfo_loanPurposeObj("個人或家庭理財", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_C))?"Y":"N"));
				loanConditionInfo.getLoanPurposeInfo().add(new PLOAN019_loanConditionInfo_loanPurposeObj("購買房屋", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_A))?"Y":"N"));
				loanConditionInfo.getLoanPurposeInfo().add(new PLOAN019_loanConditionInfo_loanPurposeObj("修繕房屋", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_B))?"Y":"N"));
				loanConditionInfo.getLoanPurposeInfo().add(new PLOAN019_loanConditionInfo_loanPurposeObj("購買汽車", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_D))?"Y":"N"));
				loanConditionInfo.getLoanPurposeInfo().add(new PLOAN019_loanConditionInfo_loanPurposeObj("繳付甲方或甲方子女學費", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_J))?"Y":"N"));
				loanConditionInfo.getLoanPurposeInfo().add(new PLOAN019_loanConditionInfo_loanPurposeObj("購置耐久性消費財", Util.equals("Y", MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_H))?"Y":"N"));

				String loanPurpose_otherDesc = Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LOANPURPOSE_OTHERDESC));
				if(Util.isEmpty(loanPurpose_otherDesc)){
					loanConditionInfo.getLoanPurposeInfo().add(new PLOAN019_loanConditionInfo_loanPurposeObj("其他", "N"));
				}else{
					loanConditionInfo.getLoanPurposeInfo().add(new PLOAN019_loanConditionInfo_loanPurposeObj("其他："+loanPurpose_otherDesc, "Y"));
				}
			}
			loanConditionInfo.setDrawDownType(MapUtils.getString(map, "drawDownType"));
			loanConditionInfo.setPreliminaryFee(CapMath.getBigDecimal(MapUtils.getString(map, "preliminaryFee")));
			loanConditionInfo.setCreditCheckFee(CapMath.getBigDecimal(MapUtils.getString(map, "creditCheckFee")));
			loanConditionInfo.setOneTimeFee(CapMath.getBigDecimal(MapUtils.getString(map, "preliminaryFee"))
					.add(CapMath.getBigDecimal(MapUtils.getString(map, "creditCheckFee"))));
			loanConditionInfo.setRenewFee(CapMath.getBigDecimal(MapUtils.getString(map, "renewFee")));
			loanConditionInfo.setChangeFee(CapMath.getBigDecimal(MapUtils.getString(map, "changeFee")));
			loanConditionInfo.setCertFee(CapMath.getBigDecimal(MapUtils.getString(map, "certFee")));
			loanConditionInfo.setReissueFee(CapMath.getBigDecimal(MapUtils.getString(map, "reissueFee")));

			loanConditionInfo.setRepaymentMethod(MapUtils.getString(map, "repaymentMethod"));
			String lendingPlanInfo_showOption = MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_LENDING_PLAN_OPTION);
			if(true){
				loanConditionInfo.setLendingPlan(LMSUtil.getDesc(get_ploan_lendingPlanInfo_showOption(), lendingPlanInfo_showOption));
			}
			BigDecimal annualPercentageRate = CapMath.getBigDecimal(MapUtils.getString(map, "annualPercentageRate"));
			List<String> rateDescDefaultList = this.getCtrTypeB_rateDesc_default();
			List<String> rateDescChoseList = this.geCtrTypeB_rateDesc(jsonObject);

			PLOAN019.PLOAN019_loanConditionInfo_lendingPlanInfo lendingPlanInfo = loanConditionInfo.getLendingPlanInfo();
			lendingPlanInfo.setShowOption(lendingPlanInfo_showOption);
			//~~~~~
			lendingPlanInfo.setAdvancedRedemptionTitle(MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_RATE_ADVANCED_REDEMPT_TITLE));
			lendingPlanInfo.setAdvancedRedemptionDesc(Arrays.asList(StringUtils.split(MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_RATE_ADVANCED_REDEMPT_DESC), "\n"))); //傳送 「已拆分換行後」 的文字
			lendingPlanInfo.setAdvancedRateTitle(MapUtils.getString(map, "advancedRateTitle"));
			lendingPlanInfo.setAdvancedRateDesc(rateDescDefaultList);
			lendingPlanInfo.setAdvancedAPR(null);
			if(Util.equals("1", lendingPlanInfo_showOption)){
				lendingPlanInfo.setAdvancedRateDesc(rateDescChoseList);
				lendingPlanInfo.setAdvancedAPR(annualPercentageRate);
			}
			//~~~~~
			lendingPlanInfo.setLimitedRedemptionTitle(MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_RATE_LIMITED_REDEMPT_TITLE));
			lendingPlanInfo.setLimitedRedemptionDesc(Arrays.asList(StringUtils.split(MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_RATE_LIMITED_REDEMPT_DESC), "\n"))); //傳送 「已拆分換行後」 的文字
			lendingPlanInfo.setLimitedRateTitle(MapUtils.getString(map, "limitedRateTitle"));
			lendingPlanInfo.setLimitedRateDesc(rateDescDefaultList);
			lendingPlanInfo.setLimitedAPR(null);
			if(Util.equals("2", lendingPlanInfo_showOption)){
				lendingPlanInfo.setLimitedRateDesc(rateDescChoseList);
				lendingPlanInfo.setLimitedAPR(annualPercentageRate);
			}
			//===========================
			if(true){
				lendingPlanInfo.setOtherInfoTitle("");
				lendingPlanInfo.setOtherInfoDesc(Arrays.asList(""));
			}
			boolean add_OtherInfoDesc_to_rateDesc = clsService.is_function_on_codetype("add_OtherInfoDesc_to_rateDesc");
			String otherInfoTitle = Util.trim(MapUtils.getString(map, "otherInfoTitle", ""));
			if(add_OtherInfoDesc_to_rateDesc){ //將 行員信貸方案備註 加到 利率文字 下面

				if(Util.isNotEmpty(otherInfoTitle)){ //有「方案備註」
					List<Object> otherInfoDesc_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsonObject, ContractDocConstants.CtrTypeB.PLOAN_OTHERINFODESC);
					List<String> tmp_list = new ArrayList<String>();
					for(Object otherInfoDesc : otherInfoDesc_list){
						String otherInfoDesc_trim = Util.trim(otherInfoDesc);
						if(Util.isNotEmpty(otherInfoDesc_trim)){
							tmp_list.add(otherInfoDesc_trim);
						}

					}
					if(tmp_list.size()>0){
						if(Util.equals("1", lendingPlanInfo_showOption)){
							lendingPlanInfo.getAdvancedRateDesc().addAll(tmp_list);
						}else if(Util.equals("2", lendingPlanInfo_showOption)){
							lendingPlanInfo.getLimitedRateDesc().addAll(tmp_list);
						}
					}
				}
			}else{
				//原模式
				lendingPlanInfo.setOtherInfoTitle(otherInfoTitle);
				if(Util.isNotEmpty(Util.trim(lendingPlanInfo.getOtherInfoTitle()))){
					List<Object> otherInfoDesc_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsonObject, ContractDocConstants.CtrTypeB.PLOAN_OTHERINFODESC);
					List<String> tmp_list = new ArrayList<String>();
					for(Object otherInfoDesc : otherInfoDesc_list){
						String otherInfoDesc_trim = Util.trim(otherInfoDesc);
						if(Util.isNotEmpty(otherInfoDesc_trim)){
							tmp_list.add(otherInfoDesc_trim);
						}
					}
					if(tmp_list.size()>0){
						lendingPlanInfo.setOtherInfoDesc(tmp_list);
					}
				}else{
					lendingPlanInfo.setOtherInfoDesc(Arrays.asList(""));
				}
			}

		}
		if(true) {
			PLOAN019_guaranteeInfo guaranteeInfo = ploanObj.getGuaranteeInfo();
			guaranteeInfo.setGeneralGuaranteePlan(MapUtils.getString(map, "generalGuaranteePlan"));
			guaranteeInfo.setGeneralGuaranteePlanInfo(Arrays.asList(StringUtils.split(MapUtils.getString(map, "generalGuaranteePlanInfo"), "\n")));
			guaranteeInfo.setJointGuaranteePlan(MapUtils.getString(map, "jointGuaranteePlan"));
			guaranteeInfo.setJointGuaranteePlanInfo(Arrays.asList(StringUtils.split(MapUtils.getString(map, "jointGuaranteePlanInfo"), "\n")));
			Object val_guaranteeAmt = MapUtils.getObject(map, ContractDocConstants.CtrTypeB.PLOAN_GUARANTEEAMT);
			if(Util.isNotEmpty(val_guaranteeAmt)){
				guaranteeInfo.setGuaranteeAmt(CrsUtil.parseBigDecimal(val_guaranteeAmt));
			}
		}
		if(true) {
			List<Object> acctNo_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsonObject, ContractDocConstants.CtrTypeB.PLOAN_ACCTNO_LIST);
			for(Object acctNo : acctNo_list){
				ploanObj.getLoanAcct().add(new PLOAN019_loanAcct(UtilConstants.兆豐銀行代碼, Util.trim(acctNo)));
			}
		}

		//J-111-0227 增加基礎利率
		if(true){
			BigDecimal baseRate = null;
			if(Util.isNotEmpty(jsonObject.optString("rateRange1Param05"))){
				baseRate = Util.parseBigDecimal(jsonObject.optString("rateRange1Param05"));
			}else if(Util.isNotEmpty(jsonObject.optString("rateRange2Param05"))){
				baseRate = Util.parseBigDecimal(jsonObject.optString("rateRange2Param05"));
			}

			ploanObj.setBaseRate(baseRate);
		}

		//J-111-0378 PLOAN對保契約書增加攤還表
		C340M01B c340m01b = c340m01b_list.get(0);
		L140M01A l140m01a = clsService.findL140M01A_mainId(c340m01b.getTabMainId());
		L140S02A l140s02a = clsService.findL140S02A(l140m01a).get(0);
		if (true) {


			String rateRange1 = jsonObject.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1);
			String rateRange2 = jsonObject.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2);

		/*
			rateRange?Param01~rateRange?Param02 第X期~第Y期

			rateRange?Param03(固定利率的利率值)
			rateRange?Param04(利率的中文描述)
			rateRange?Param05(baseRate) + rateRange?Param06(plusRate) = rateRange?Param07(finalRate)
		 */
			String val_rateRange1Type01 = jsonObject.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_TYPE01);
			String val_rateRange2Type01 = jsonObject.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2_TYPE01);
			if ("Y".equals(rateRange1)) {
//				C340M01B c340m01b = c340m01b_list.get(0);
//				L140M01A l140m01a = clsService.findL140M01A_mainId(c340m01b.getTabMainId());
//				L140S02A l140s02a = clsService.findL140S02A(l140m01a).get(0);
				L140S02D l140s02d = l140s02a != null ? clsService.findL140S02D_orderByPhase(l140m01a.getMainId(), l140s02a.getSeq(),
						"Y").get(0) : null;
				PLOAN019_rate rateRange = new PLOAN019.PLOAN019_rate();
				String rateRange1_rateDesc = Util.trim(jsonObject.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RATEDESC));

				rateRange.setRate_Type(Util.isNotEmpty(l140s02d)?l140s02d.getRateType() : null);
				if (Util.isNotEmpty(rateRange1_rateDesc)) {
					rateRange.setRate_Bgn("1");
					rateRange.setRate_End(jsonObject.optString("loanPeriod"));
//					if (Util.isNotEmpty(l140s02d)){
//						rateRange.setRate(l140s02d.getNowRate().toString());
//					}
//					else{
					List<Map<String, Object>> lrRate_list = misMislnratService.findMislnratByLRRate(Util.trim(rateRange.getRate_Type()), "TWD" );
					String rateValue = "";
					if(lrRate_list.size() > 0){
						rateValue = LMSUtil.pretty_numStr(CrsUtil.parseBigDecimal(MapUtils.getObject(lrRate_list.get(0), "LR_RATE")));
					}
					rateRange.setRate(rateValue);
//					}

				} else {
					if ("Y".equals(val_rateRange1Type01)) {
						//固定利率
						rateRange.setRate_Bgn(jsonObject.optString("rateRange1Param01"));
						rateRange.setRate_End(jsonObject.optString("rateRange1Param02"));
						rateRange.setRate(jsonObject.optString("rateRange1Param03"));
					} else {
						//浮動利率
						rateRange.setRate_Bgn(jsonObject.optString("rateRange1Param01"));
						rateRange.setRate_End(jsonObject.optString("rateRange1Param02"));
						rateRange.setRate(jsonObject.optString("rateRange1Param07"));
					}
				}
				ploanObj.getRateList().add(rateRange);
			}
			if ("Y".equals(rateRange2)) {
//				C340M01B c340m01b = c340m01b_list.get(0);
//				L140M01A l140m01a = clsService.findL140M01A_mainId(c340m01b.getTabMainId());
//				L140S02A l140s02a = clsService.findL140S02A(l140m01a).get(0);
				L140S02D l140s02d = l140s02a != null ? clsService.findL140S02D_orderByPhase(l140m01a.getMainId(), l140s02a.getSeq(),
						"Y").get(1) : null;
				PLOAN019_rate rateRange = new PLOAN019.PLOAN019_rate();

				rateRange.setRate_Type(Util.isNotEmpty(l140s02d)?l140s02d.getRateType() : null);
				if ("Y".equals(val_rateRange2Type01)) {
					//固定利率
					rateRange.setRate_Bgn(jsonObject.optString("rateRange2Param01"));
					rateRange.setRate_End(jsonObject.optString("rateRange2Param02"));
					rateRange.setRate(jsonObject.optString("rateRange2Param03"));
				} else {
					//浮動利率
					rateRange.setRate_Bgn(jsonObject.optString("rateRange2Param01"));
					rateRange.setRate_End(jsonObject.optString("rateRange2Param02"));
					rateRange.setRate(jsonObject.optString("rateRange2Param07"));
				}
				ploanObj.getRateList().add(rateRange);
			}
		}

		//J-112-0205_10702_B1001 Web e-Loan新增代償相關欄位
		String isRepayment = Util.trim(MapUtils.getString(map, "isRepayment"));
		if (Util.isEmpty(isRepayment)) {
			isRepayment = UtilConstants.DEFAULT.否;
		}
		ploanObj.setIsRepayment(isRepayment);
		if (Util.equals(isRepayment, UtilConstants.DEFAULT.是)) {
			List<PLOAN019_repayment> repaymentList =  ploanObj.getRepaymentList();
			String repayList =Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeB.PLOAN_REPAYMENT_LIST));
			if (Util.isNotEmpty(repayList)) {
				List<Object> repayment_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsonObject, ContractDocConstants.CtrTypeB.PLOAN_REPAYMENT_LIST);
				for (Object ob : repayment_list) {
					PLOAN019_repayment repayment = this.to_outputObj(ob.toString(), PLOAN019_repayment.class);
					ploanObj.getRepaymentList().add(repayment);
				}
			}
		}

		if (Util.isNotEmpty(ploanObj.getRelatedPersonId())) {
			//待開放保證人在填入
			ploanObj.setGuaranteeType("");
			ploanObj.setWitness("");
		}

		if(true) {
			PLOAN019_payeeInfo payeeInfo = ploanObj.getPayeeInfo();
			String payeeInfoType = "1";
			if (Util.isNotEmpty(jsonObject.optString("eloan_pa_deliv_B_cb"))) {
				if (Util.equals(jsonObject.optString("eloan_pa_deliv_B_cb"),CB_Y_STR)) {
					payeeInfoType = "2";
				}
			}
			payeeInfo.setPayeeInfoType(payeeInfoType);
			payeeInfo.setPayeeInfoAccountType(jsonObject.optString("eloan_pa_deliv_A_t1"));
		}
		if(true) {
			PLOAN019_expireInfo expireInfo = ploanObj.getExpireInfo();
			String expireInfoType = "";
			if (Util.isNotEmpty(jsonObject.optString("eloan_pa_use_A"))) {
				if (Util.equals(jsonObject.optString("eloan_pa_use_A"),CB_Y_STR)) {
					expireInfoType = "1";
					expireInfo.setDuration1(jsonObject.optString("eloan_pa_use_A_t7"));
				}
			}
			if (Util.isNotEmpty(jsonObject.optString("eloan_pa_use_B"))) {
				if (Util.equals(jsonObject.optString("eloan_pa_use_B"),CB_Y_STR)) {
					expireInfoType = "2";
					expireInfo.setDuration2(jsonObject.optString("eloan_pa_use_B_t7"));
				}
			}
			if (Util.isNotEmpty(jsonObject.optString("eloan_pa_use_C"))) {
				if (Util.equals(jsonObject.optString("eloan_pa_use_C"),CB_Y_STR)) {
					expireInfoType = "3";
					expireInfo.setDuration3(jsonObject.optString("eloan_pa_use_C_t7"));
				}
			}
			if (Util.isNotEmpty(jsonObject.optString("eloan_pa_use_D"))) {
				if (Util.equals(jsonObject.optString("eloan_pa_use_D"),CB_Y_STR)) {
					expireInfoType = "4";
					expireInfo.setDuration4(jsonObject.optString("eloan_pa_use_D_t1"));
					expireInfo.setMaxYear4(jsonObject.optString("eloan_pa_use_D_t3"));
					expireInfo.setExpireYear4(jsonObject.optString("eloan_pa_use_D_t4"));
					expireInfo.setExpireMonth4(jsonObject.optString("eloan_pa_use_D_t5"));
					expireInfo.setExpireDay4(jsonObject.optString("eloan_pa_use_D_t6"));
				}
			}
			expireInfo.setExpireInfoType(expireInfoType);
		}
		if(true) {
			PLOAN019_repaymentInfo repaymentInfo = ploanObj.getRepaymentInfo();
			String repaymentInfoType ="";
			if (Util.isNotEmpty(jsonObject.optString("eloan_pa_repay_A"))) {
				if (Util.equals(jsonObject.optString("eloan_pa_repay_A"),CB_Y_STR)) {
					repaymentInfoType = "1";
				}
			}
			if (Util.isNotEmpty(jsonObject.optString("eloan_pa_repay_B"))) {
				if (Util.equals(jsonObject.optString("eloan_pa_repay_B"),CB_Y_STR)) {
					repaymentInfoType = "2";
				}
			}
			if (Util.isNotEmpty(jsonObject.optString("eloan_pa_repay_C"))) {
				if (Util.equals(jsonObject.optString("eloan_pa_repay_C"),CB_Y_STR)) {
					repaymentInfoType = "3";
				}
			}
			if (Util.isNotEmpty(jsonObject.optString("eloan_pa_repay_D"))) {
				if (Util.equals(jsonObject.optString("eloan_pa_repay_D"),CB_Y_STR)) {
					repaymentInfoType = "4";
					repaymentInfo.setLimtedYear4(jsonObject.optString("eloan_pa_repay_D_t1"));
					repaymentInfo.setLimtedMonth4(jsonObject.optString("eloan_pa_repay_D_t2"));
					repaymentInfo.setYear4(jsonObject.optString("eloan_pa_repay_D_t3"));
					repaymentInfo.setMonth4(jsonObject.optString("eloan_pa_repay_D_t4"));
				}
			}
			if (Util.isNotEmpty(jsonObject.optString("eloan_pa_repay_E"))) {
				if (Util.equals(jsonObject.optString("eloan_pa_repay_E"),CB_Y_STR)) {
					repaymentInfoType = "5";
					repaymentInfo.setLimtedYear5(jsonObject.optString("eloan_pa_repay_E_t1"));
					repaymentInfo.setLimtedMonth5(jsonObject.optString("eloan_pa_repay_E_t2"));
					repaymentInfo.setYear5(jsonObject.optString("eloan_pa_repay_E_t3"));
					repaymentInfo.setMonth5(jsonObject.optString("eloan_pa_repay_E_t4"));
				}
			}
			if (Util.isNotEmpty(jsonObject.optString("eloan_pa_repay_F"))) {
				if (Util.equals(jsonObject.optString("eloan_pa_repay_F"),CB_Y_STR)) {
					repaymentInfoType = "6";
					repaymentInfo.setPeriod6(jsonObject.optString("eloan_pa_repay_F_t1"));
					repaymentInfo.setYear6(jsonObject.optString("eloan_pa_repay_F_t2"));
				}
			}
			if (Util.isNotEmpty(jsonObject.optString("eloan_pa_repay_G"))) {
				if (Util.equals(jsonObject.optString("eloan_pa_repay_G"),CB_Y_STR)) {
					repaymentInfoType = "7";
				}
			}
			repaymentInfo.setRepaymentInfoType(repaymentInfoType);
			repaymentInfo.setFirstRate(jsonObject.optString("advancedFirstRate"));
			repaymentInfo.setSecondRate(jsonObject.optString("advancedSecondRate"));
			repaymentInfo.setHouseRedemption(jsonObject.optString("lendingPlanInfo_showOption"));
		}
		if(true) {
			PLOAN019_interestInfo interestInfo = ploanObj.getInterestInfo();
			String interestInfoType = "" ;
			if (Util.isNotEmpty(jsonObject.optString("eloan_pa_intr_withPPP_cb"))) {
				if (Util.equals(jsonObject.optString("eloan_pa_intr_withPPP_cb"),CB_Y_STR)) {
					interestInfoType = "1" ;
					interestInfo.setFormula1("一");
					interestInfo.setFirstPeriodFrom1(jsonObject.optString("eloan_pa_intr_withPPP_1x1"));
					interestInfo.setFirstPeriodTo1(jsonObject.optString("eloan_pa_intr_withPPP_1x2"));
					interestInfo.setFirstPeriodRate1(jsonObject.optString("eloan_pa_intr_withPPP_1x3"));

					interestInfo.setSecondPeriodFrom1(jsonObject.optString("eloan_pa_intr_withPPP_1y1"));
					interestInfo.setSecondPeriodTo1(jsonObject.optString("eloan_pa_intr_withPPP_1y2"));
					interestInfo.setSecondPeriodRate1(jsonObject.optString("eloan_pa_intr_withPPP_1y3"));

					interestInfo.setThirdPeriodFrom1(jsonObject.optString("eloan_pa_intr_withPPP_1y5"));
					interestInfo.setThirdPeriodTo1(jsonObject.optString("eloan_pa_intr_withPPP_1y6"));
					interestInfo.setThirdPeriodRate1(jsonObject.optString("eloan_pa_intr_withPPP_1y7"));

					interestInfo.setRate1(jsonObject.optString("eloan_pa_intr_withPPP_baseRate"));

					if (Util.isNotEmpty(jsonObject.optString("eloan_pa_intr_withPPP_2t1"))) {
						interestInfo.setOther1(jsonObject.optString("eloan_pa_intr_withPPP_2t1"));
						interestInfo.setFormula1("二");
					}
				}
			}
			if (Util.isNotEmpty(jsonObject.optString("eloan_pa_intr_noPPP_cb"))) {
				if (Util.equals(jsonObject.optString("eloan_pa_intr_noPPP_cb"),CB_Y_STR)) {
					interestInfoType = "2" ;
					interestInfo.setRate2_1(jsonObject.optString("eloan_pa_intr_noPPP_1t1"));
					interestInfo.setRate2_2(jsonObject.optString("eloan_pa_intr_noPPP_2t1"));
					interestInfo.setRate2_3_1(jsonObject.optString("eloan_pa_intr_noPPP_3t1"));
					interestInfo.setRate2_3_2(jsonObject.optString("eloan_pa_intr_noPPP_baseRate"));
					interestInfo.setOther2(jsonObject.optString("eloan_pa_intr_noPPP_4t1"));
					String formula2 ="";
					if (Util.isNotEmpty(jsonObject.optString("eloan_pa_intr_noPPP_1t1"))){
						formula2 = "一";
					}else if (Util.isNotEmpty(jsonObject.optString("eloan_pa_intr_noPPP_2t1"))){
						formula2 = "二";
					}else if (Util.isNotEmpty(jsonObject.optString("eloan_pa_intr_noPPP_3t1"))){
						formula2 = "三";
					}else if (Util.isNotEmpty(jsonObject.optString("eloan_pa_intr_noPPP_4t1"))){
						formula2 = "四";
					}
					interestInfo.setFormula2(formula2);
				}
			}

			interestInfo.setInterestInfoType(interestInfoType);
		}
		//帶入分行聯絡方式
		if (Util.isNotEmpty(c340m01a.getOwnBrId())) {
			Branch br = branchDao.findByBrno(c340m01a.getOwnBrId());
			ploanObj.setBrNoTel(br.getTel());
			ploanObj.setBrNoFax(br.getFaxNo());
		}
		
		//J-112-0502 新增產品種類、撥款日
		ploanObj.setProdKind(l140s02a != null ? l140s02a.getProdKind() : "");
		if(l140m01a != null){
			String useDeadline = l140m01a.getUseDeadline();
			if("1".equals(useDeadline)){ // 動用期限選項=1，放起日
				String date = Util.trim(l140m01a.getDesp1()).split("~")[0];
				if(Util.isNotEmpty(date)){
					try {
						Date startDate = new SimpleDateFormat("yyyy-MM-dd").parse(date);
						ploanObj.setLnDate(convert_to_ploan_dateColumn(startDate));
					} catch (ParseException e) {
						e.printStackTrace();
					}
				}
			}else if("2".equals(useDeadline) || "8".equals(useDeadline)){ // 動用期限選項=2 or 8，放核准日
				L120M01A l120m01a = clsService.findL120M01A_mainId(l120m01a_mainId);
				ploanObj.setLnDate(convert_to_ploan_dateColumn(l120m01a.getApproveTime()));
			}else{
				ploanObj.setLnDate(null);
			}
		}
		
		//J-113-0050 約據相關資料
		ploanObj.setConsentVer(jsonObject.optString(ContractDocConstants.CtrTypeB.CONSENTVER));
		ploanObj.setCollateralBuildingAddr_1(jsonObject.optString(ContractDocConstants.CtrTypeB.COLLATERALBUILDINGADDR_1));
		
		BigDecimal mortgageMaxAmt_1 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.MORTGAGEMAXAMT_1)) ?
				new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.MORTGAGEMAXAMT_1)) : null;
		ploanObj.setMortgageMaxAmt_1(mortgageMaxAmt_1);
		
		ploanObj.setFirstLoanDate_year(jsonObject.optString(ContractDocConstants.CtrTypeB.FIRSTLOANDATE_YEAR));
		ploanObj.setFirstLoanDate_mth(jsonObject.optString(ContractDocConstants.CtrTypeB.FIRSTLOANDATE_MTH));
		ploanObj.setFirstLoanDate_day(jsonObject.optString(ContractDocConstants.CtrTypeB.FIRSTLOANDATE_DAY));
		
		BigDecimal firstLoanAmt_1 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.FIRSTLOANAMT_1)) ?
				new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.FIRSTLOANAMT_1)) : null;
		ploanObj.setFirstLoanAmt_1(firstLoanAmt_1);
		
		ploanObj.setCollateralBuildingAddr_2(jsonObject.optString(ContractDocConstants.CtrTypeB.COLLATERALBUILDINGADDR_2));
		
		BigDecimal mortgageMaxAmt_2 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.MORTGAGEMAXAMT_2)) ?
				new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.MORTGAGEMAXAMT_2)) : null;
		ploanObj.setMortgageMaxAmt_2(mortgageMaxAmt_2);
		
		BigDecimal firstLoanAmt_2 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.FIRSTLOANAMT_2)) ?
				new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.FIRSTLOANAMT_2)) : null;
		ploanObj.setFirstLoanAmt_2(firstLoanAmt_2);
		
		ploanObj.setHouseLoanContractNo(jsonObject.optString(ContractDocConstants.CtrTypeB.HOUSELOANCONTRACTNO));
		
		String collateralContractTerms = jsonObject.optString(ContractDocConstants.CtrTypeB.COLLATERALCONTRACTTERMS);
		List<String> collateralContractTermsList = jsonStrToArrayList(collateralContractTerms);
		ploanObj.setCollateralContractTerms(collateralContractTermsList);
		
		ploanObj.setUnregisteredBuildingDesc(jsonObject.optString(ContractDocConstants.CtrTypeB.UNREGISTEREDBUILDINGDESC));
		
		String coTarget = c120s01a_MainBorrower.getCoTarget();
		ploanObj.setCoTarget(coTarget);
		
		//J-113-0050 央行切結書相關資料
		String cbAfftTerms = jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFTTERMS);
		ploanObj.setCbAfftTerms(cbAfftTerms);
		if("1".equals(cbAfftTerms)){
			PLOAN019_cbAfft1Content cbAfft1Content = ploanObj.getCbAfft1Content();
			cbAfft1Content.setCbAfft1_1(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_1));
			cbAfft1Content.setCbAfft1_2(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_2));
			cbAfft1Content.setCbAfft1_3_year(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_3_YEAR));
			cbAfft1Content.setCbAfft1_3_mth(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_3_MTH));
			cbAfft1Content.setCbAfft1_3_day(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_3_DAY));
			
			BigDecimal CbAfft1_4 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_4)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_4)) : null;
			cbAfft1Content.setCbAfft1_4(CbAfft1_4);
			
			String cbAfft1_5 = jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_5);
			List<String> cbAfft1_5tList = jsonStrToArrayList(cbAfft1_5);
			cbAfft1Content.setCbAfft1_5(cbAfft1_5tList);
			
			cbAfft1Content.setCbAfft1_6(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_6));
			
			BigDecimal CbAfft1_7 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_7)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_7)) : null;
			cbAfft1Content.setCbAfft1_7(CbAfft1_7);
			
			BigDecimal CbAfft1_8 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_8)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_8)) : null;
			cbAfft1Content.setCbAfft1_8(CbAfft1_8);
			
			cbAfft1Content.setCbAfft1_9_year(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_9_YEAR));
			cbAfft1Content.setCbAfft1_9_mth(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_9_MTH));
			cbAfft1Content.setCbAfft1_9_day(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_9_DAY));
			cbAfft1Content.setCbAfft1_10_year(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_10_YEAR));
			cbAfft1Content.setCbAfft1_10_mth(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_10_MTH));
			cbAfft1Content.setCbAfft1_10_day(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_10_DAY));
			cbAfft1Content.setCbAfft1_10_no(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_10_NO));
			
			BigDecimal CbAfft1_11 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_11)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_11)) : null;
			cbAfft1Content.setCbAfft1_11(CbAfft1_11);
			
			BigDecimal CbAfft1_12 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_12)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_12)) : null;
			cbAfft1Content.setCbAfft1_12(CbAfft1_12);
			
			BigDecimal CbAfft1_13 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_13)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_13)) : null;
			cbAfft1Content.setCbAfft1_13(CbAfft1_13);
			
			BigDecimal CbAfft1_14 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_14)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_14)) : null;
			cbAfft1Content.setCbAfft1_14(CbAfft1_14);
			
			BigDecimal CbAfft1_15 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_15)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_15)) : null;
			cbAfft1Content.setCbAfft1_15(CbAfft1_15);
			
			cbAfft1Content.setCbAfft1_16(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT1_16));
		} else if("2".equals(cbAfftTerms)){
			PLOAN019_cbAfft2Content cbAfft2Content = ploanObj.getCbAfft2Content();
			cbAfft2Content.setCbAfft2_1(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_1));
			cbAfft2Content.setCbAfft2_2(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_2));
			cbAfft2Content.setCbAfft2_3_year(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_3_YEAR));
			cbAfft2Content.setCbAfft2_3_mth(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_3_MTH));
			cbAfft2Content.setCbAfft2_3_day(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_3_DAY));
			
			BigDecimal CbAfft2_4 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_4)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_4)) : null;
			cbAfft2Content.setCbAfft2_4(CbAfft2_4);
			
			BigDecimal CbAfft2_5 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_5)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_5)) : null;
			cbAfft2Content.setCbAfft2_5(CbAfft2_5);
			
			BigDecimal CbAfft2_6 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_6)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_6)) : null;
			cbAfft2Content.setCbAfft2_6(CbAfft2_6);
			
			BigDecimal CbAfft2_7 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_7)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_7)) : null;
			cbAfft2Content.setCbAfft2_7(CbAfft2_7);
			
			BigDecimal CbAfft2_8 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_8)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_8)) : null;
			cbAfft2Content.setCbAfft2_8(CbAfft2_8);
			
			BigDecimal CbAfft2_9 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_9)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_9)) : null;
			cbAfft2Content.setCbAfft2_9(CbAfft2_9);
			
			BigDecimal CbAfft2_10 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_10)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT2_10)) : null;
			cbAfft2Content.setCbAfft2_10(CbAfft2_10);
		} else if("3".equals(cbAfftTerms)){
			PLOAN019_cbAfft3Content cbAfft3Content = ploanObj.getCbAfft3Content();
			cbAfft3Content.setCbAfft3_1(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT3_1));
			cbAfft3Content.setCbAfft3_2(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT3_2));
			cbAfft3Content.setCbAfft3_3_year(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT3_3_YEAR));
			cbAfft3Content.setCbAfft3_3_mth(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT3_3_MTH));
			cbAfft3Content.setCbAfft3_3_day(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT3_3_DAY));
			
			BigDecimal CbAfft3_4 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT3_4)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT3_4)) : null;
			cbAfft3Content.setCbAfft3_4(CbAfft3_4);
			
			BigDecimal CbAfft3_5 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT3_5)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT3_5)) : null;
			cbAfft3Content.setCbAfft3_5(CbAfft3_5);
		} else if("4".equals(cbAfftTerms)){
			PLOAN019_cbAfft4Content cbAfft4Content = ploanObj.getCbAfft4Content();
			cbAfft4Content.setCbAfft4_1(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT4_1));
			cbAfft4Content.setCbAfft4_2_year(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT4_2_YEAR));
			cbAfft4Content.setCbAfft4_2_mth(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT4_2_MTH));
			cbAfft4Content.setCbAfft4_2_day(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT4_2_DAY));
			
			BigDecimal CbAfft4_3 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT4_3)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT4_3)) : null;
			cbAfft4Content.setCbAfft4_3(CbAfft4_3);
			
			cbAfft4Content.setCbAfft4_4(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT4_4));
			
			BigDecimal CbAfft4_5 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT4_5)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT4_5)) : null;
			cbAfft4Content.setCbAfft4_5(CbAfft4_5);
			
			BigDecimal CbAfft4_6 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT4_6)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT4_6)) : null;
			cbAfft4Content.setCbAfft4_6(CbAfft4_6);
			
			BigDecimal CbAfft4_7 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT4_7)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT4_7)) : null;
			cbAfft4Content.setCbAfft4_7(CbAfft4_7);
			
		} else if("5".equals(cbAfftTerms)){
			PLOAN019_cbAfft5Content cbAfft5Content = ploanObj.getCbAfft5Content();
			cbAfft5Content.setCbAfft5_1(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_1));
			cbAfft5Content.setCbAfft5_2(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_2));
			cbAfft5Content.setCbAfft5_3_year(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_3_YEAR));
			cbAfft5Content.setCbAfft5_3_mth(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_3_MTH));
			cbAfft5Content.setCbAfft5_3_day(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_3_DAY));
			
			BigDecimal CbAfft5_4 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_4)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_4)) : null;
			cbAfft5Content.setCbAfft5_4(CbAfft5_4);
			
			String cbAfft5_5 = jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_5);
			List<String> cbAfft5_5tList = jsonStrToArrayList(cbAfft5_5);
			cbAfft5Content.setCbAfft5_5(cbAfft5_5tList);
			
			BigDecimal CbAfft5_6 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_6)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_6)) : null;
			cbAfft5Content.setCbAfft5_6(CbAfft5_6);
			
			BigDecimal CbAfft5_7 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_7)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_7)) : null;
			cbAfft5Content.setCbAfft5_7(CbAfft5_7);
			
			BigDecimal CbAfft5_8 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_8)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_8)) : null;
			cbAfft5Content.setCbAfft5_8(CbAfft5_8);
			
			BigDecimal CbAfft5_9 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_9)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_9)) : null;
			cbAfft5Content.setCbAfft5_9(CbAfft5_9);
			
			BigDecimal CbAfft5_10 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_10)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_10)) : null;
			cbAfft5Content.setCbAfft5_10(CbAfft5_10);
			
			BigDecimal CbAfft5_11 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_11)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_11)) : null;
			cbAfft5Content.setCbAfft5_11(CbAfft5_11);
			
			cbAfft5Content.setCbAfft5_12(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_12));
			
			BigDecimal CbAfft5_13 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_13)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_13)) : null;
			cbAfft5Content.setCbAfft5_13(CbAfft5_13);
			
			BigDecimal CbAfft5_14 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_14)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_14)) : null;
			cbAfft5Content.setCbAfft5_14(CbAfft5_14);
			
			BigDecimal CbAfft5_15 = Util.isNotEmpty(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_15)) ?
					new BigDecimal(jsonObject.optString(ContractDocConstants.CtrTypeB.CBAFFT5_15)) : null;
			cbAfft5Content.setCbAfft5_15(CbAfft5_15);
		}
		//J-113-0482 新增央行切結書版本
		String cbAfftVersion = Util.trim(sysParameterService.getParamValue("J_113_0482_cbAfftVersion"));
		ploanObj.setCbAfftVersion(cbAfftVersion);

		//J-113-0435 web eLoan增加年輕族群註記
		int age = clsService.getAge(c120s01a_MainBorrower.getBirthday());
		String isBorrowerYouth = "N";
		if(18 <= age && age < 22){
			isBorrowerYouth = "Y";
		}
		ploanObj.setIsBorrowerYouth(isBorrowerYouth);

		JSONObject jsonObj = pLoanGwClient.send_contract_ctrTypeB(ploanObj);
		return jsonObj;
	}
	
	private JSONObject _ploan_sendSigningContract_ctrType_L(C340M01A c340m01a)
	throws CapException{
		String l120m01a_mainId = c340m01a.getCaseMainId();
		String custId = c340m01a.getCustId();
		String dupNo = c340m01a.getDupNo();		
		C120S01A c120s01a_MainBorrower = clsService.findC120S01A(l120m01a_mainId, custId, dupNo);
		// List<C340M01B> c340m01b_list = clsService.findC340M01B(c340m01a.getMainId());
		//=================
		PLOAN014 ploanObj = new PLOAN014();
		ploanObj.setContractNo(c340m01a.getPloanCtrNo());
		ploanObj.setContractVersion(ploan_param_laborContractVersion(c340m01a));		
		ploanObj.setBranchCode(c340m01a.getOwnBrId());
		ploanObj.setBorrowerName(Util.trim(c340m01a.getCustName()));
		ploanObj.setBorrowerId(custId);
		if(c120s01a_MainBorrower!=null){
			ploanObj.setBorrowerBirthDate(convert_to_ploan_dateColumn(c120s01a_MainBorrower.getBirthday()));
			//ploanObj.setBorrowerMobileNumber(Util.trim(c120s01a_MainBorrower.getMTel()));
			//ploanObj.setBorrowerEmail(Util.trim(c120s01a_MainBorrower.getEmail()));
		}
		
		ploanObj.setExpiredDate(convert_to_ploan_dateColumn(c340m01a.getPloanCtrExprDate()));

		Map<String, Object> map = new HashMap<String, Object>();
		JSONObject jsonObject = null;
		List<C340M01C> c340m01c_list = clsService.findC340M01C(c340m01a.getMainId());
		for (C340M01C c340m01c : c340m01c_list) {
			String c340m01c_jsonData = Util.trim(c340m01c.getJsonData());
			try {
				jsonObject = DataParse.toJSON(c340m01c_jsonData);
			} catch (CapException e) {
				e.printStackTrace();
			}

			LMSUtil.addJsonToMap(map, jsonObject);
		}
		//若經辦有更改過，以{更改過}的為主
		ploanObj.setBorrowerMobileNumber(Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_BORROWERMOBILENUMBER)));
		ploanObj.setBorrowerEmail(Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_BORROWEREMAIL)));

		if(true) {
			String courtName = Util.trim(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_COURT_NAME));
			ploanObj.setCourtName(courtName);
		}
		if(true) {
			ploanObj.setLoanAmt(CapMath.getBigDecimal(MapUtils.getString(map, "loanAmt")));
			ploanObj.setLoanPeriod(CapMath.getBigDecimal(MapUtils.getString(map, "loanPeriod")));
			List<String> rateDescChoseList = this.geCtrTypeA_rateDesc(jsonObject);
			if(true){
				ploanObj.setAdvancedRedemptionTitle(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_ADVANCED_REDEMPT_TITLE));						
				ploanObj.setAdvancedRedemptionDesc(Arrays.asList(StringUtils.split(MapUtils.getString(map, ContractDocConstants.CtrTypeA.PLOAN_RATE_ADVANCED_REDEMPT_DESC), "\n"))); //傳送 「已拆分換行後」 的文字
				ploanObj.setAdvancedRateTitle(MapUtils.getString(map, "advancedRateTitle"));
				ploanObj.setAdvancedRateDesc(rateDescChoseList);
			}		
		}
		
		if(true) {
			List<Object> acctNo_flag_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsonObject, ContractDocConstants.CtrTypeL.PLOAN_ACCTNO_FLAG_LIST);
			for(Object acctNo_flag_raw : acctNo_flag_list){
				String acctNo_flag  = Util.trim(acctNo_flag_raw);
				String[] dataArr = StringUtils.split(acctNo_flag, "^");
				String acctNo = "";
				String flag = "";
				if(Util.isNotEmpty(acctNo_flag) && dataArr.length ==2){
					acctNo = dataArr[0];
					flag = dataArr[1]; 
				}else if(Util.isNotEmpty(acctNo_flag) && dataArr.length ==1 && acctNo_flag.endsWith("^")){					
					acctNo = dataArr[0];
					flag = ""; 
				}else {
					throw new CapException("acctNo_flag=["+acctNo_flag+"]"
							+",dataArr.length=["+dataArr.length+"]"
							+",src=["+jsonObject.optString(ContractDocConstants.CtrTypeL.PLOAN_ACCTNO_FLAG_LIST)+"]", getClass());
				}
				ploanObj.getLoanAcct().add(new PLOAN014_loanAcct(UtilConstants.兆豐銀行代碼, Util.trim(acctNo), Util.trim(flag)));
			}
		}
		
		JSONObject jsonObj = pLoanGwClient.send_laborContract(ploanObj);
		return jsonObj;
	}
	
	private String convert_to_ploan_dateColumn(Date date){
		return StringUtils.replace(Util.trim(TWNDate.toAD(date)), "-", "/");
	}
	
	@Override
	public JSONObject ploan_discardContract(C340M01A c340m01a){
		PLOAN006 ploanObj = new PLOAN006();
		ploanObj.setContractNo(c340m01a.getPloanCtrNo());
		JSONObject jsonObj = pLoanGwClient.send_discard_contract(ploanObj);
		return jsonObj;
	}

	@Override
	public String get_ploanIdentityType(C122M01A c122m01a){
		String c122m01a_ploanIdentityType = Util.trim(c122m01a.getPloanIdentityType());
		if(Util.isNotEmpty(c122m01a_ploanIdentityType)){
			return c122m01a_ploanIdentityType;
		}
		String itemType = "0";
		C122M01B c122m01b = clsService.findC122M01B_byMainIdItemType(c122m01a.getMainId(), itemType);
		String applyKind = c122m01a.getApplyKind();
		if(c122m01b!=null 
				&& (Util.equals(UtilConstants.C122_ApplyKind.P, applyKind) || Util.equals(UtilConstants.C122_ApplyKind.Q, applyKind)) 
				&& Util.equals("PLOAN001", c122m01b.getJsonVoClass())){
			ObjectMapper objectMapper = new ObjectMapper();
			PLOAN001 ploan_obj = null;
			try {
				ploan_obj = objectMapper.readValue(JSONObject.fromObject(c122m01b.getJsonData()).toString(), PLOAN001.class);
			} catch (Exception e) {
								
			}
			if(ploan_obj!=null){
				return Util.trim(ploan_obj.getIdentityType());
			}
		}
		return "";
		
	}
	
	@Override
	public JSONObject ploan_sendCtrTypeC(C340M01A c340m01a, byte[] mergedPdf, C160S01D c160s01d, C122M01A c122m01a, C122M01E c122m01e, C120M01A c120m01a, C120S01A c120s01a) throws CapException{
		String custId = c340m01a.getCustId();
				
		Map<String, String> ctrParam_from_C122M01A = ClsUtility.get_ctrParam_from_C122M01A(c122m01a);
		
		String c160s01d_accNo = Util.trim(c160s01d.getAccNo());
		String bankAcctCode = "";
		String bankAcctNo = "";
		String borrowerAgreeCrossSelling = "";
		if( StringUtils.isNotBlank(c160s01d_accNo)){ //撥至 本行帳號
			bankAcctCode = "017";
			bankAcctNo = c160s01d_accNo;
		}else{
			String c122m01e_appnWay = c122m01e!=null?Util.trim(c122m01e.getAppnWay()):"";
			String c122m01e_appnBankCode = c122m01e!=null?Util.trim(c122m01e.getAppnBankCode()):"";
			String c122m01e_dpAcct = c122m01e!=null?Util.trim(c122m01e.getDpAcct()):"";
			
			//非自動進帳 => 應該是 中鋼總公司(支票或郵局)
			if(Util.equals("C", c122m01e_appnWay)){
				//
			}else if(Util.equals("B", c122m01e_appnWay)){
				bankAcctCode = c122m01e_appnBankCode;
				bankAcctNo = c122m01e_dpAcct;	
			}
		}
		//=================
		PLOAN016 ploanObj = new PLOAN016();
		ploanObj.setContractNo(c340m01a.getPloanCtrNo());
		ploanObj.setBankAcctCode(bankAcctCode);
		ploanObj.setBankAcctNo(bankAcctNo);	
		ploanObj.setBorrowerIPAddr(Util.trim(c122m01a.getAgreeQueryEJIp()));
		ploanObj.setBorrowerIdentityType(get_ploanIdentityType(c122m01a));
		ploanObj.setLoanBeginDate(ClsUtility.convert_to_ploanDateStr(c160s01d.getLnFromDate()));
		ploanObj.setLoanEndDate(ClsUtility.convert_to_ploanDateStr(c160s01d.getLnEndDate()));
		ploanObj.setRateAdjustInformMethod(Util.trim(c122m01a.getRateAdjNotify()));  //{1:簡訊, 2:書面, 3:電子郵件, 9:不通知} 在 L517 需另增加{4:網路銀行登入, 5:存摺登錄}
		if(c122m01a.getAgreeQueryEJTs()!=null){
			ploanObj.setBorrowerSingingDate(c122m01a.getAgreeQueryEJTs().getTime());
		}
		ploanObj.setContractCheckDate(ClsUtility.convert_to_ploanDateStr(CapDate.parseDate(MapUtils.getString(ctrParam_from_C122M01A, "ctrCheckDate"))));
		if(Util.equals("Y", c122m01a.getCrossMarket())){
			borrowerAgreeCrossSelling = "1";
		}else if(Util.equals("N", c122m01a.getCrossMarket())){
			borrowerAgreeCrossSelling = "0";
		}
		ploanObj.setBorrowerAgreeCrossSelling(borrowerAgreeCrossSelling);
		ploanObj.setBranchCode(c340m01a.getOwnBrId());
		ploanObj.setBorrowerId(custId);
		ploanObj.setBorrowerBirthDate(ClsUtility.convert_to_ploanDateStr(c120s01a.getBirthday()));
		ploanObj.setBorrowerName(Util.trim(c340m01a.getCustName()));
		ploanObj.setBorrowerMobileNumber(Util.trim(c120s01a.getMTel()));
		ploanObj.setBorrowerEmail(Util.trim(c120s01a.getEmail()));
		ploanObj.setProductCode("PA");
		ploanObj.setContractVersion("");
		ploanObj.setLoanAmt(ClsUtility.get_floorValue_divideWAN(c160s01d.getLoanTotAmt()));
		ploanObj.setLoanPeriod(BigDecimal.valueOf(c160s01d.getMonth()));
		ploanObj.setDrawDownType("一次撥付");
		ploanObj.setOneTimeFee(BigDecimal.ZERO);
		ploanObj.setPreliminaryFee(CrsUtil.parseBigDecimal(MapUtils.getString(ctrParam_from_C122M01A, "preliminaryFee")));
		ploanObj.setCreditCheckFee(CrsUtil.parseBigDecimal(MapUtils.getString(ctrParam_from_C122M01A, "creditCheckFee")));
		ploanObj.setRepaymentMethod("本息平均攤還");
		ploanObj.setAdvancedRateDesc(MapUtils.getString(ctrParam_from_C122M01A, "advancedBaseRateChineseName")
				+MapUtils.getString(ctrParam_from_C122M01A, "advancedBaseRate")+"%"
				+"加計年利率"+MapUtils.getString(ctrParam_from_C122M01A, "advancedPmRate")+"%"
				+"，目前合計為年利率"+MapUtils.getString(ctrParam_from_C122M01A, "advancedNowRate")+"%");
		ploanObj.setAdvancedAPR(CrsUtil.parseBigDecimal(MapUtils.getString(ctrParam_from_C122M01A, "advancedAPR")));
		ploanObj.setLimitedRateDesc("");
		ploanObj.setLimitedAPR(null);
		ploanObj.setShowOption("1"); //呈現給客戶看的方案{1: 無限制清償, 2:限制清償}
		ploanObj.setCourtName(MapUtils.getString(ctrParam_from_C122M01A, "courtName"));
		if(true){
			ploanObj.setFile(new String(Base64.encodeBase64(mergedPdf)));
		}
		
		JSONObject jsonObj = pLoanGwClient.send_contract_ctrTypeC(ploanObj);
		return jsonObj;			
	}
	
	@Override
	public JSONObject ploan_getInfo(String key1, String key2, String key3, String key4, String key5){
		PLOAN017 ploanObj = new PLOAN017(key1, key2, key3, key4, key5);
		return pLoanGwClient.send_get_info(ploanObj);
	}
	
	@Override
	public List<String> geCtrTypeA_rateDesc(JSONObject jsContent) {
		StringBuffer rateDes = new StringBuffer();
		String rateRange1 = jsContent.optString(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1);
		String rateRange2 = jsContent.optString(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2);
		
		/*
			rateRange?Param01~rateRange?Param02 第X期~第Y期
			
			rateRange?Param03(固定利率的利率值)
			rateRange?Param04(利率的中文描述)
			rateRange?Param05(baseRate) + rateRange?Param06(plusRate) = rateRange?Param07(finalRate) 
		 */
		String val_rateRange1Type01 = jsContent.optString(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_TYPE01);
		String val_rateRange2Type01 = jsContent.optString(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2_TYPE01);
		if ("Y".equals(rateRange1)) {
			String rateRange1_rateDesc_M3 = Util.trim(jsContent.optString(ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_RATEDESC_M3));
			if(Util.isNotEmpty(rateRange1_rateDesc_M3)) {
				List<String>  result_M3_list = new ArrayList<String>();
				result_M3_list.add(rateRange1_rateDesc_M3);
				return result_M3_list;
			}
			
			build_ctrTypeA_rate_desc(rateDes, val_rateRange1Type01, jsContent, "rateRange1Param01", "rateRange1Param02"
					, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_FIXED_RATE, "rateRange1Param04", "rateRange1Param05"
					, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_PLUSRATE
					, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_RESULTRATE );
		}
		if ("Y".equals(rateRange2)) {
			if (!CapString.isEmpty(rateDes.toString())) {
				rateDes.append("\n");
			}

			build_ctrTypeA_rate_desc(rateDes, val_rateRange2Type01, jsContent, "rateRange2Param01", "rateRange2Param02"
					, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2_FIXED_RATE, "rateRange2Param04", "rateRange2Param05"
					, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2_PLUSRATE
					, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE2_RESULTRATE );
					
		}
		
		List<String>  result_list = new ArrayList<String>();
		for(String s : StringUtils.split(rateDes.toString(), "\n")){
			result_list.add(s);
		}
		return result_list;
	}

	private void build_ctrTypeA_rate_desc(StringBuffer rateDes, String rateRangeXType01, JSONObject jsContent, String rateRangeXParam01, String rateRangeXParam02
			, String rateRangeXParam03, String rateRangeXParam04, String rateRangeXParam05, String rateRangeXParam06, String rateRangeXParam07 ){
		
		if ("Y".equals(rateRangeXType01)) {
			//固定利率
			String periodBeg = jsContent.optString(rateRangeXParam01);
			String periodEnd = jsContent.optString(rateRangeXParam02);
			if(Util.isNotEmpty(periodBeg) || Util.isNotEmpty(periodEnd)){
				rateDes.append(MessageFormat.format(CTRTYPE_A_RATESTR_X_TO_Y, periodBeg, periodEnd));
				rateDes.append("\n");
			}
			rateDes.append(MessageFormat.format(CTRTYPE_A_RATESTR_RATEFLAG1, jsContent.optString(rateRangeXParam03)));
		} else {
			//浮動利率
			String periodBeg = jsContent.optString(rateRangeXParam01);
			String periodEnd = jsContent.optString(rateRangeXParam02);
			rateDes.append(MessageFormat.format(CTRTYPE_A_RATESTR_X_TO_Y, periodBeg, periodEnd));
			rateDes.append("\n");
			if( clsService.is_function_on_codetype("CtrTypeA_V202209")){
				rateDes.append(MessageFormat.format(CTRTYPE_A_RATESTR_RATEFLAG3_202209, CapMath.getBigDecimal(jsContent.optString(rateRangeXParam06))));
			}else{
				rateDes.append(MessageFormat.format(CTRTYPE_A_RATESTR_RATEFLAG3, jsContent.optString(rateRangeXParam04), jsContent.optString(rateRangeXParam05)
						, CapMath.getBigDecimal(jsContent.optString(rateRangeXParam06)), jsContent.optString(rateRangeXParam07) ));
			}
		}
	}

	private List<String> getCtrTypeA_rateDesc_default() {
		String html_space = "&nbsp;";
		String html_space_3 = StringUtils.repeat(html_space, 3);
		String html_space_6 = StringUtils.repeat(html_space, 3); // 6);

		List<String>  result_list = new ArrayList<String>();
		result_list.add(MessageFormat.format(CTRTYPE_A_RATESTR_X_TO_Y, html_space_3, html_space_3));//第x期至第x期
		result_list.add(MessageFormat.format(CTRTYPE_A_RATESTR_RATEFLAG1, html_space_6));
		if( clsService.is_function_on_codetype("CtrTypeA_V202209")){
			result_list.add(MessageFormat.format(CTRTYPE_A_RATESTR_RATEFLAG3_202209,  html_space_6 ));
		}else{
			result_list.add(MessageFormat.format(CTRTYPE_A_RATESTR_RATEFLAG3, "消費金融放款指標利率", html_space_6, html_space_6, html_space_6 ));
		}


		return result_list;
	}

	@Override
	public List<String> geCtrTypeB_rateDesc(JSONObject jsContent) {
		StringBuffer rateDes = new StringBuffer();
		String withPPP = jsContent.optString(ContractDocConstants.CtrTypeB.ELOAN_PA_INTR_WITHPPP_CB);
		String rateRange1 = jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1);
		String rateRange2 = jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2);

		//限制清償
		if (Util.equals("2", jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_LENDING_PLAN_OPTION))) {
			String val_rateRange1Type01 = jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_TYPE01);
			String val_rateRange2Type01 = jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE2_TYPE01);
			if ("Y".equals(rateRange1)) {
				String rateRange1_rateDesc = Util.trim(jsContent.optString(ContractDocConstants.CtrTypeB.PLOAN_RATE_RANGE1_RATEDESC));
				if (Util.isNotEmpty(rateRange1_rateDesc)) {
					List<String> result_list = new ArrayList<String>();
					result_list.add(rateRange1_rateDesc);
					return result_list;
				}

				build_ctrTypeB_rate_desc(rateDes, val_rateRange1Type01, jsContent, "rateRange1Param01", "rateRange1Param02"
						, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_FIXED_RATE, "rateRange1Param04", "rateRange1Param05"
						, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_PLUSRATE
						, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_RESULTRATE );
			}
			if ("Y".equals(rateRange2)) {
				if (!CapString.isEmpty(rateDes.toString())) {
					rateDes.append("\n");
				}

				build_ctrTypeB_rate_desc(rateDes, val_rateRange1Type01, jsContent, "rateRange1Param01", "rateRange1Param02"
						, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_FIXED_RATE, "rateRange1Param04", "rateRange1Param05"
						, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_PLUSRATE
						, ContractDocConstants.CtrTypeA.PLOAN_RATE_RANGE1_RESULTRATE );

			}
		} else {
			if (Util.isNotEmpty(jsContent.optString("eloan_pa_intr_noPPP_1t1"))) {
				rateDes.append(MessageFormat.format(CTRTYPE_B_RATESTR_noPPP_1, CapMath.getBigDecimal(jsContent.optString("eloan_pa_intr_noPPP_1t1"))));
			} else if (Util.isNotEmpty(jsContent.optString("eloan_pa_intr_noPPP_2t1"))) {
				rateDes.append(MessageFormat.format(CTRTYPE_B_RATESTR_noPPP_2, CapMath.getBigDecimal(jsContent.optString("eloan_pa_intr_noPPP_2t1"))));
			} else if (Util.isNotEmpty(jsContent.optString("eloan_pa_intr_noPPP_3t1"))) {
				rateDes.append(MessageFormat.format(CTRTYPE_B_RATESTR_noPPP_3, CapMath.getBigDecimal(jsContent.optString("eloan_pa_intr_noPPP_3t1"))));			
			} else if (Util.isNotEmpty(jsContent.optString("eloan_pa_intr_noPPP_4t1"))) {
				rateDes.append(jsContent.optString("eloan_pa_intr_noPPP_4t1"));
			}
		}


		List<String>  result_list = new ArrayList<String>();
		for(String s : StringUtils.split(rateDes.toString(), "\n")){
			result_list.add(s);
		}
		return result_list;
	}

	private void build_ctrTypeB_rate_desc(StringBuffer rateDes, String rateRangeXType01, JSONObject jsContent, String rateRangeXParam01, String rateRangeXParam02
			, String rateRangeXParam03, String rateRangeXParam04, String rateRangeXParam05, String rateRangeXParam06, String rateRangeXParam07 ){

		if ("Y".equals(rateRangeXType01)) {
			//固定利率
			String periodBeg = jsContent.optString(rateRangeXParam01);
			String periodEnd = jsContent.optString(rateRangeXParam02);
			if(Util.isNotEmpty(periodBeg) || Util.isNotEmpty(periodEnd)){
				rateDes.append(MessageFormat.format(CTRTYPE_B_RATESTR_X_TO_Y, periodBeg, periodEnd));
				rateDes.append("\n");
			}
			rateDes.append(MessageFormat.format(CTRTYPE_B_RATESTR_RATEFLAG1, jsContent.optString(rateRangeXParam03)));
		} else {
			//浮動利率
			String periodBeg = jsContent.optString(rateRangeXParam01);
			String periodEnd = jsContent.optString(rateRangeXParam02);
			if(Util.isNotEmpty(periodBeg) || Util.isNotEmpty(periodEnd)){
				if (!periodBeg.equals("0") && !periodEnd.equals("0")) {
					rateDes.append(MessageFormat.format(CTRTYPE_B_RATESTR_X_TO_Y, periodBeg, periodEnd));
					rateDes.append("\n");
				}
			}
			rateDes.append(MessageFormat.format(CTRTYPE_B_RATESTR_RATEFLAG3, CapMath.getBigDecimal(jsContent.optString(rateRangeXParam06))));
		}
	}

	private List<String> getCtrTypeB_rateDesc_default() {
		String html_space = "&nbsp;";
		String html_space_3 = StringUtils.repeat(html_space, 3);
		String html_space_6 = StringUtils.repeat(html_space, 3); // 6);

		List<String>  result_list = new ArrayList<String>();
		result_list.add(MessageFormat.format(CTRTYPE_B_RATESTR_X_TO_Y, html_space_3, html_space_3));//第x期至第x期
		result_list.add(MessageFormat.format(CTRTYPE_B_RATESTR_RATEFLAG1, html_space_6));
		result_list.add(MessageFormat.format(CTRTYPE_B_RATESTR_RATEFLAG3,  html_space_6 ));


		return result_list;
	}
	
	@Override
	public Map<String, String> get_ploan_lendingPlanInfo_showOption(){
		HashMap<String, String> r = new HashMap<String, String>();
		r.put("1", "無限制清償期");
		r.put("2", "有限制清償期");
		return r; 
	}

	@Override
	public Map<String, String> is_cntrNo_belong_co70647919_c101(String tabMainId){
		L140M01A l140m01a = clsService.findL140M01A_mainId(tabMainId);
		if(l140m01a!=null){
			L140M03A l140m03a = clsService.findL140M03A(l140m01a);
			if(l140m03a!=null && Util.equals(l140m03a.getGrpCntrNo(), "918111000393")){
				L140M01Y l140m01y_elf459Srcflag1 = clsService.findL140M01Y_refTypeELF459Srcflag1_1stItem(l140m01a.getMainId()); 
				C122M01A c122m01a = clsService.findC122M01A_fromL140M01Y_refTypeDocCode1ELF459Srcflag1_refModelC122M01A(l140m01y_elf459Srcflag1);
				if(c122m01a!=null){
					String c122m01a_ploanPlan = Util.trim(c122m01a.getPloanPlan());
					if(Util.equals(ClsUtility.get_ploanPlan_C101(), c122m01a_ploanPlan)){
						C122M01E c122m01e = clsService.findC122M01E_refMainId(c122m01a.getMainId());
						if(c122m01e!=null){
							C120S01A c120s01a = clsService.findC120S01A(c122m01a.getMainId(), c122m01a.getCustId(), c122m01a.getDupNo());
							if(c120s01a!=null){
								Map<String, String> result = new HashMap<String, String>();
								result.put("grpCntrNo", l140m03a.getGrpCntrNo());
								result.put("loanPlan", c122m01a_ploanPlan);
								result.put("appnDpAcct", Util.trim(c120s01a.getDpAcct()));
								result.put("subscribeAmt", LMSUtil.pretty_numStr(c122m01e.getSubscribeAmt()));
								return result;
							}
						}
					}else{
						return null; //線上進件,但「行銷方案」!=ClsUtility.get_ploanPlan_C101()
					}
				}
			}
		}
		return null;
	}

	private List<L140M01R> getL140M01RbyMainId(String mainId) {
		return l140m01rDao.findByMainId(mainId);
	}
	private String getRate(C340M01A c340m01a)throws CapException{
		//J-110-0486_10702_B1001 Web e-Loan契約書總費用年百分率以系統計算帶入以降低錯誤率
		String eloan_pc_spTerm_yRate="";
		List<C340M01B> c340m01b_list = clsService.findC340M01B(c340m01a.getMainId());
		Map<String, Object> map = new HashMap<String, Object>();
		List<String> list = new ArrayList<String>();
		Integer loan = 0;
		Integer bufferMonth = 0;
		Integer cal_Type = 1 ;
		Integer limitMonth = 0;
		String rate = "";
		boolean needCountRate=false;

		List<Double> multipleRate= new ArrayList<Double>();
		List<Integer> multipleRate_TimeStart= new ArrayList<Integer>();
		List<Integer> multipleRate_TimeEnd= new ArrayList<Integer>();
		List<Integer> costList = new ArrayList<Integer>();

		C340M01C c340m01c = clsService.findC340M01C(c340m01a.getMainId(), ContractDocConstants.C340M01C_ItemType.TYPE_0);
		if (c340m01c != null) {
			String jsonData = Util.trim(c340m01c.getJsonData());
			JSONObject jsonObject = DataParse.toJSON(jsonData);

			LMSUtil.addJsonToMap(map, jsonObject);
		}

		if (c340m01b_list != null) {
			//產品種類
			String tabMainId = c340m01b_list.get(0).getTabMainId();
			L140M01A l140m01a = clsService.findL140M01A_mainId(tabMainId);
			if(Util.isNotEmpty(l140m01a)){
				loan = l140m01a.getCurrentApplyAmt().intValue();
				List<L140S02A> l140s02as = clsService.findL140S02A(l140m01a);
				L140S02A l140s02a = l140s02as.get(0);
				List<L140S02D> l140s02ds = clsService.findL140S02D_orderByPhase(tabMainId, l140s02a.getSeq(),
						"Y");
				boolean is_only_M3_and_pmRate_0 = ClsUtility.is_only_M3_and_pmRate_0(l140s02ds);
				boolean is_only_MR_and_pmRate_0 = ClsUtility.is_only_MR_and_pmRate_0(l140s02ds);
				//利率資訊
				if(is_only_M3_and_pmRate_0 || is_only_MR_and_pmRate_0){
					List<Map<String, Object>> lrRate_list = misMislnratService.findMislnratByLRRate(CrsUtil.RATE_TYPE_M3, "TWD" );
					if(is_only_MR_and_pmRate_0){
						lrRate_list = misMislnratService.findMislnratByLRRate(CrsUtil.RATE_TYPE_MR, "TWD" );
					}
					String rateValue = "";
					if(lrRate_list.size() > 0){
						rateValue = LMSUtil.pretty_numStr(CrsUtil.parseBigDecimal(MapUtils.getObject(lrRate_list.get(0), "LR_RATE")));
					}
					String bgnNum = "";
					String endNum = "";
					if(l140s02ds.size()>0){
						multipleRate_TimeStart.add((l140s02ds.get(0).getBgnNum()));
						multipleRate_TimeEnd.add((l140s02ds.get(l140s02ds.size()-1).getEndNum()));
						limitMonth=(l140s02ds.get(l140s02ds.size()-1).getEndNum());
					}
					multipleRate.add(Util.parseDouble(rateValue));

				}else{
					if(l140s02ds.size()>0){
						for (L140S02D l140s02d : l140s02ds) {
							Integer phase = l140s02d.getPhase();
							String l140s02dOid = l140s02d.getOid();
							BigDecimal l140s02dNowRate = l140s02d.getNowRate() == null ? BigDecimal.ZERO : l140s02d.getNowRate();
							BigDecimal l140s02d_rateUser = l140s02d.getRateUser() == null ? BigDecimal.ZERO : l140s02d.getRateUser();
							String pmFlag = l140s02d.getPmFlag();
							BigDecimal pmRate = l140s02d.getPmRate();
							String rateType = l140s02d.getRateType();
							BigDecimal baseRate = l140s02d.getBaseRate();
							if (CrsUtil.RATE_TYPE_01.equals(rateType) && Util.equals("C01", l140s02d.getRateUserType())) {

							}else{
								if(!Util.equals("1", l140s02d.getRateFlag())){ //非固定利率，去中心抓最新的代率值
									baseRate = _get_latest_mis_MISLNRAT_currTWD(rateType);
								}
							}
							l140s02dNowRate = ClsUtility.calc_nowRate(l140s02d.getRateType(), l140s02d.getRateUserType(), l140s02d.getRateFlag(), l140s02d.getNowRate(), baseRate, pmFlag, pmRate);
							if (phase == 1) {
								if(CrsUtil.RATE_TYPE_01.equals(l140s02d.getRateType()) && Util.equals("C01", l140s02d.getRateUserType())){
									multipleRate.add(l140s02d_rateUser.doubleValue());
								}else{
									multipleRate.add(l140s02dNowRate.doubleValue());
								}
								multipleRate_TimeStart.add(l140s02d.getBgnNum());
								multipleRate_TimeEnd.add(l140s02d.getEndNum());
							} else if (phase == 2) {
								if(CrsUtil.RATE_TYPE_01.equals(l140s02d.getRateType()) && Util.equals("C01", l140s02d.getRateUserType())){
									multipleRate.add(l140s02d_rateUser.doubleValue());
								}else{
									multipleRate.add(l140s02dNowRate.doubleValue());
								}
								multipleRate_TimeStart.add(l140s02d.getBgnNum());
								multipleRate_TimeEnd.add(l140s02d.getEndNum());
							}
						}
						limitMonth=(l140s02ds.get(l140s02ds.size()-1).getEndNum());
					}
				}

				List<L140M01R> l140m01rs = clsService.findL140M01R_exclude_feeSrc3(c340m01a.getCaseMainId());
				for (L140M01R l140m01r : l140m01rs) {
					if(l140m01r.getFeeAmt().compareTo(BigDecimal.ZERO)>0){
						costList.add(l140m01r.getFeeAmt().intValue());
					}
				}
				L140S02E l140s02e = l140s02eDao.findByUniqueKey(tabMainId,
						l140s02a.getSeq());
				if(Util.isNotEmpty(l140s02e)){
					if(Util.equals(l140s02e.getNowExtend(),UtilConstants.DEFAULT.是)){
						bufferMonth = l140s02e.getNowEnd();
					}
					if(Util.equals(l140s02e.getPayWay(),"1") || Util.equals(l140s02e.getPayWay(),"2")){
						cal_Type=1;
					}else if(Util.equals(l140s02e.getPayWay(),"3") || Util.equals(l140s02e.getPayWay(),"4")){
						cal_Type=2;
					}else if(Util.equals(l140s02e.getPayWay(),"7")){
						cal_Type=3;
					}
				}

				L140S02C l140s02c = clsService.findL140S02C(tabMainId, l140s02a.getSeq());
				//如果償還方式選擇本息平均攤還(雙週繳款)或是本金平均攤還(雙週繳款，每期攤還本金：元)，就不自動計算年百分率
				//因雙週繳期數會不正確，官網計算公式僅能計算按月計息
				if(l140s02c!=null && Util.equals(UtilConstants.L140S02CIntway.期付金, l140s02c.getIntWay()) && l140s02ds.size()>0 && Util.notEquals(l140s02e.getPayWay(),"2") && Util.notEquals(l140s02e.getPayWay(),"4")){
					needCountRate = true;
				}
			}
		}
		try {
			if(needCountRate){
				rate = clsService.calculateRate(loan,limitMonth,bufferMonth,cal_Type,multipleRate,multipleRate_TimeStart,multipleRate_TimeEnd,costList);
				if(Util.isNotEmpty(rate) && list.size()==0){
					eloan_pc_spTerm_yRate =rate;
				}
			}
		}
		catch(Exception ex){ //可能遇到 java.lang.NumberFormatException
			LOGGER.error("calculateRate@"+Thread.currentThread().getStackTrace()[1].getMethodName()+" input{"
					+"loan:"+loan
					+", limitMonth:"+limitMonth
					+", bufferMonth:"+bufferMonth
					+", cal_Type:"+cal_Type
					+", multipleRate:"+multipleRate
					+", multipleRate_TimeStart:"+multipleRate_TimeStart
					+", multipleRate_TimeEnd:"+multipleRate_TimeEnd
					+", costList:"+costList
					+"}");
			LOGGER.error(StrUtils.getStackTrace(ex));
			eloan_pc_spTerm_yRate ="";
		}
		return eloan_pc_spTerm_yRate;
	}
	
	//J-113-0050 字串轉ArrayList
	private ArrayList<String> jsonStrToArrayList(String jsonStr){
		ArrayList<String> result = new ArrayList<String>();
		if(!Util.isEmpty(jsonStr)){
			if(jsonStr.contains("[")){
				jsonStr = jsonStr.substring(1, jsonStr.length() - 1);
			}
			jsonStr = jsonStr.replace("\"", "");
			String[] arr = jsonStr.split(",");
			result = new ArrayList<String>(Arrays.asList(arr));
		}
		return result;
	}
	
	private BigDecimal _get_latest_mis_MISLNRAT_currTWD(String lrCode){
		List<Map<String, Object>> rowDatas = misMislnratService.findByCurr("TWD");
		for (Map<String, Object> data : rowDatas) {
			String key = Util.trim(data.get("LR_CODE"));
			BigDecimal rate = new BigDecimal(Util.trim(data.get("LR_RATE")));
			//=====
			if(Util.equals(key, lrCode)){
				return rate;
			}
		}
		return null;
	}
	public  <T> T  to_outputObj(String jsonStr, Class<T> assign_class)
			throws CapException{
		ObjectMapper objectMapper = new ObjectMapper();
		try{
			return objectMapper.readValue(jsonStr, assign_class);
		}catch(Exception e){
			throw new CapException(e, getClass());
		}
	}
}
