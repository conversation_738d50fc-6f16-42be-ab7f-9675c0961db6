<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
		<div id="filterDialog" th:title="#{'v0015.title01'}" class="popup_cont" style="display: none">
			<form id="filterForm" name="filterForm" onsubmit="return false;">
				<table width="100%" class="tb1" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td class="color-black">
							<b><span class="color-black"><th:block th:text="#{'v0015.title02'}"></th:block>：</span></b>
							<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="text-red">＊</span><th:block th:text="#{'v0015.title03'}"></th:block>：
							<select id="selectCase" name="selectCase" comboKey="lms0015v00_caseType" combotype="2" space="true"></select>
							<th:block th:utext="#{'v0015.title04'}"></th:block>：
							<select id="selectDoc" name="selectDoc" comboKey="lms0015v00_docType" combotype="2" space="true"></select>
							<br><br><hr class="hr1">
							
							<b><span class="color-black"><th:block th:text="#{'v0015.title05'}"></th:block>：</span></b><br>
							<label>
								<input type="radio" name="selectRadio"  value="1">
								<b><th:block th:text="#{'v0015.title06'}"></th:block>：</b>
							</label><br>

							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="text-red">＊</span><th:block th:text="#{'v0015.title07'}"></th:block>
							<input id="startDate" name="startDate" type="text" class="date" value="YYYY-MM-DD">~
							<input id="endDate" name="endDate" type="text" class="date" value="YYYY-MM-DD"><br>
							<label>
								<input type="radio" name="selectRadio" value="2">
								<b><th:block th:text="#{'v0015.title08'}"></th:block>：</b>
							</label><br>

							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="text-red">＊</span><th:block th:text="#{'v0015.title09'}"></th:block>
							<input id="custId" name="custId" type="text" value="" size="8" maxlength="10" class="upText"><br>
							<label>
								<input type="radio" name="selectRadio"  value="3">
								<b><th:block th:text="#{'v0015.title10'}"></th:block>：</b>
							</label><br>
							
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="text-red">＊</span><th:block th:text="#{'v0015.title11'}"></th:block>
							<select id="selectManger" name="selectManger"></select>
							<br><br><hr class="hr1">
							
							<b>
								<span class="color-black"><th:block th:text="#{'v0015.title12'}"></th:block>：
									<label>
										<input name="sortType" type="radio" value="1" checked="checked">
										<th:block th:text="#{'v0015.title14'}"></th:block>
									</label>
									<label>
										<input name="sortType" type="radio" value="2">
										<th:block th:text="#{'v0015.title15'}"></th:block>
									</label>
									&nbsp;&nbsp;
									<th:block th:text="#{'v0015.title13'}"></th:block>
								</span>
							</b>
							<br><br>
						</td>
					</tr>
				</table>
				<p>
					<label>
						<input type="checkbox" id="checkShow" name="checkShow" value="Y">
						<th:block th:text="#{'v0015.title16'}"></th:block>
					</label>
				</p>
			</form>
		</div>
		<div id="changeBox" style="display:none">
			<span>
				<th:block th:text="#{'v0015.title16'}"></th:block><br>
				<select id="changePeople"></select>
			</span>
		</div>
	</th:block>
</body>
</html>