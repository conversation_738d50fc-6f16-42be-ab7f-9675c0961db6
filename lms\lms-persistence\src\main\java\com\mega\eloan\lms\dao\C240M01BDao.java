/* 
 * C240M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C240M01B;

/** 消金覆審名單關聯檔 **/
public interface C240M01BDao extends IGenericDao<C240M01B> {

	C240M01B findByOid(String oid);
	
	List<C240M01B> findByMainId(String mainId);
	
	C240M01B findByUniqueKey(String mainId, String refMainId);

	List<C240M01B> findByIndex01(String mainId, String refMainId);

	/**
	 * 利用C240M01A mainId刪除
	 * @param mainId
	 */
	void deleteByC240M01AMainid(String mainId);
}