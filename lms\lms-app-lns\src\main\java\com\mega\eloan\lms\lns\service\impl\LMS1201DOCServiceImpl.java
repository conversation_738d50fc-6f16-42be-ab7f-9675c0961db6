/* 
 * LMS1201DOCServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.service.impl;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.MissingResourceException;
import java.util.Properties;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.gwclient.EloanHttpGetClient;
import com.mega.eloan.common.gwclient.EloanHttpGetReqMessage;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.NGFlagHelper;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.constants.UtilConstants.Casedoc;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120S16ADao;
import com.mega.eloan.lms.dao.L140S01ADao;
import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.pages.LMS1205V01Page;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.lms.report.impl.LMS1205R01RptServiceImpl;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.mfaloan.service.LNLNF070Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF022Service;
import com.mega.eloan.lms.mfaloan.service.MisMislnratService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C140SFFF;
import com.mega.eloan.lms.model.C900M01A;
import com.mega.eloan.lms.model.C900M01D;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01D;
import com.mega.eloan.lms.model.L120M01E;
import com.mega.eloan.lms.model.L120M01H;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.eloan.lms.model.L120S01G;
import com.mega.eloan.lms.model.L120S16A;
import com.mega.eloan.lms.model.L120S16B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140M01R;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02C;
import com.mega.eloan.lms.model.L140S02D;
import com.mega.eloan.lms.model.L140S02E;
import com.mega.eloan.lms.model.L140S02G;
import com.mega.eloan.lms.model.L140S02H;
import com.mega.eloan.lms.model.L140S07A;
import com.mega.eloan.lms.model.L140S11A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.XmlTool;

/**
 * <pre>
 * 產Word Service
 * </pre>
 * 
 * @since 2011/12/8
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/12/9,Miller Lin
 *          </ul>
 */
@Service("lms1201docservice")
public class LMS1201DOCServiceImpl extends AbstractFormHandler implements
		FileDownloadService {
	// 復原TFS J-111-0636_05097_B100X
	@Resource
	LMS1201Service service1201;

	@Resource
	LMS1401Service service1401;

	@Resource
	BranchService branch;

	@Resource
	UserInfoService userSrv;

	@Resource
	CodeTypeService codeService;

	@Resource
	MisLNF022Service lnLnf022Service;
	@Resource
	L140S02ADao l140s02aDao;

	@Resource
	L140S01ADao l140s01aDao;

	@Resource
	LMSService lmsService;

	@Resource
	MisMislnratService misMislnratService;

	@Resource
	LNLNF070Service lnlnf070Service;

	@Resource
	DocFileService docFileService;

	@Resource
	CLSService clsService;

	@Resource
	L120S16ADao l120s16aDao;

	@Resource
	SysParameterService sysParamService;

	@Resource
	EloanHttpGetClient eloanHttpGetClient;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	LMS1205Service service1205;

	private final static String 換行符號 = "<w:br/>";
	private final static String 換頁符號 = "<w:r><w:br w:type=\"page\"/></w:r>";

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1201DOCServiceImpl.class);

	DecimalFormat dfMoney = new DecimalFormat("#,###,###,###,##0");
	DecimalFormat dfRate = new DecimalFormat("#,###,###,###,##0.00");
	private final String DATEYYYYMMDD = "yyyy-MM-dd";
	// 開會時間
	private final String caseTime = "13時30分";
	// 開會地點
	private final String casePlace = "本行801會議室";
	// Word XML 換行語法
	private final String strEnter = "</w:t></w:r></w:p>"
			+ "<w:p wsp:rsidR='00D3657A' wsp:rsidRDefault='00D3657A' "
			+ "wsp:rsidP='007E18F7'><w:pPr><w:jc "
			+ "w:val='both'/><w:rPr><w:rFonts w:ascii='標楷體' "
			+ "w:fareast='標楷體' w:h-ansi='標楷體'/>"
			+ "<wx:font wx:val='標楷體'/></w:rPr></w:pPr>"
			+ "<w:r><w:rPr><w:rFonts w:ascii='標楷體' "
			+ "w:fareast='標楷體' w:h-ansi='標楷體' "
			+ "w:hint='fareast'/><wx:font wx:val='標楷體'/></w:rPr><w:t>";
	// Word XML 表格換行語法
	private final String tblEnter = "<w:p wsp:rsidR='00810A66' wsp:rsidRDefault='00810A66'>"
			+ "<w:pPr><w:rPr><w:rFonts w:fareast='標楷體'/>"
			+ "<w:b/><w:b-cs/><w:spacing w:val='10'/>"
			+ "<w:sz w:val='26'/></w:rPr></w:pPr></w:p>";
	// Word XML 換頁語法
	// private final String tblPage =
	// "<w:p wsp:rsidR='00F229D6' wsp:rsidRDefault='00F50453'>" +
	// "<w:r><w:rPr><w:rFonts w:ascii='標楷體' w:fareast='標楷體' w:h-ansi='標楷體'/>" +
	// "<wx:font wx:val='標楷體'/></w:rPr><w:br w:type='page'/></w:r></w:p>";
	// Word 換頁語法
	private final String wordPage = "<br clear=all style='page-break-before:always'>";
	// // 授審會交易代碼
	// private final String 授審會 = "339062";
	// // 催收會交易代碼
	// private final String 催收會 = "339063";
	// // 常董會交易代碼
	// private final String 常董會 = "339064";

	private final String 國外部 = "007";
	// private final String 金控總部 = "201";
	// private final String 國金部 = "025";
	// 過濾所有以<開頭以>結尾的標籤
	private final static String regxpForHtml = "<([^>]*)>";
	// 找出IMG標籤
	@SuppressWarnings("unused")
	private final static String regxpForImgTag = "<\\s*img\\s+([^>]*)\\s*>";
	// 找出IMG標籤的SRC屬性
	@SuppressWarnings("unused")
	private final static String regxpForImaTagSrcAttrib = "src=\"([^\"]+)\"";
	// J-110-0368_11557_B1001 常董會/董事會提案稿所需之相關文件
	private final String fromCESFileError = "取得常董會附件失敗";

	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		OutputStream outputStream = null;
		ByteArrayOutputStream baos = null;
		try {
			outputStream = this.creatDoc(params);
			if (outputStream != null) {
				baos = (ByteArrayOutputStream) outputStream;
			}
			return baos.toByteArray();

		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}

		}
		return null;
	}

	public OutputStream creatDoc(PageParameters params) {
		OutputStream outputStream = null;
		String docTempType = params.getString("docTempType");
		boolean isCls = params.getBoolean("isCls");
		try {
			if ("LMSDoc1".equals(docTempType)) {
				outputStream = this.getWord1b(params);
			} else if ("LMSDoc2".equals(docTempType)) {
				outputStream = this.getWord2a(params);
			} else if ("LMSDoc4".equals(docTempType)) {
				outputStream = this.getWord3a(params);
			} else if ("LMSDoc4_V202009".equals(docTempType)) {
				outputStream = this.getWord3a_v202009(params);
			} else if ("LMSDoc4_V202011".equals(docTempType)) {
				outputStream = this.getWord3a_v202011(params);
			} else if ("LMSDoc5".equals(docTempType)) {
				outputStream = this.getWord3b(params);
			} else if ("LMSDoc6".equals(docTempType)) {
				outputStream = this.getWord3c(params);
			} else if ("LMSDoc7".equals(docTempType)) {
				outputStream = this.getWord4(params);
			} else if ("LMSDoc8".equals(docTempType)) {
				if (isCls) {
					outputStream = this.getWord5ByCls(params);
				} else {
					outputStream = this.getWord5(params);
				}

			} else if ("LMSDoc80".equals(docTempType)) {
				outputStream = this.getWord6(params);
			} else if ("CLSDoc1".equals(docTempType)) {
				outputStream = this.getCLSDoc1(params);
			} else if ("LMSDoc22".equals(docTempType)) {
				// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
				outputStream = this.getWord22a(params);
			} else if ("LMSDoc23".equals(docTempType)) {
				// J-110-0386_05097_B1001 Web
				// e-Loan國內與海外企金授信「相關文件」頁籤新增「授信額度主要敘做條件彙總表」
				outputStream = this.getWord23a(params);
			} else if ("LMSDoc07A".equals(docTempType)) {
				outputStream = this.getWord07A(params);
			} else if ("LMSDoc9Z".equals(docTempType)) {
				// J-110-0499 配合作業合理化提案，調整會議決議及相關A、B、C版
				outputStream = this.getLMSDoc9(params);
			} else if ("LMSDoc9A".equals(docTempType)) {
				// J-110-0499 配合作業合理化提案，調整會議決議及相關A、B、C版
				outputStream = this.getLMSDoc9(params);
			} else if ("LMSDoc9B".equals(docTempType)) {
				// J-110-0499 配合作業合理化提案，調整會議決議及相關A、B、C版
				outputStream = this.getLMSDoc9(params);
			} else if ("LMSDoc9C".equals(docTempType)) {
				// J-110-0499 配合作業合理化提案，調整會議決議及相關A、B、C版
				outputStream = this.getLMSDoc9(params);
			} else if ("LMSDoc11A".equals(docTempType)) {
				// J-112-0357 新增敘做條件異動比較表
				outputStream = this.getWord11A(params);
			} else if ("LMSDoc24".equals(docTempType)) {
				// J-112-0357 新增常董會討論事項提案檢核表
				outputStream = this.getWord24(params);
			}
		} catch (Exception e) {

		}
		return outputStream;
	}

	/**
	 * 將產生的WORD直接儲存到DOCFILE，不要回傳回前端
	 * 
	 * J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveCreatDoc(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();

		CapAjaxFormResult result = new CapAjaxFormResult();
		Map<String, String> map = new LinkedHashMap<String, String>();
		Locale locale = null;

		byte[] saveFile = this.getContent(params);
		String errorMsg = this.saveToFile(params, saveFile);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	/**
	 * 列印催收會決議事項
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord6(PageParameters params)	throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得mainId
		String mainId = params.getString("mainId");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		try {
			// FileUtils.readFileToString(file, "UTF-8");
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			// String str = FileUtils.readFileToString(file, "UTF-8");
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 找尋資料區塊開始字串
			String bodStr = "<div class=Section1";
			// 找尋資料區塊結束字串
			String bodEnd = "</body>";
			// 資料區塊開始位置
			int strBod = sbWord.indexOf(bodStr, 0);
			// 資料區塊結束位置
			int endBod = sbWord.indexOf(bodEnd, strBod);

			// 表頭區塊
			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(sbWord.substring(0, strBod));
			// 資料區塊
			StringBuilder sbData = new StringBuilder();
			sbData.append(sbWord.substring(strBod, endBod));
			// 表尾區塊
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(sbWord.substring(endBod));
			// 稽核資料是否都順利取得
			if (sbHeader == null || sbData == null || sbFooter == null) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1205V01Page.class);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", pop.getProperty("l120v01.error1")),
						getClass());
			}

			L120M01A model = service1201.findL120m01aByMainId(mainId);
			if (model == null) {
				model = new L120M01A();
			}
			L120M01H l120m01h = service1201.findL120m01hByUniqueKey(
					model.getMainId(), "B");
			// 取得額度明細表
			List<L140M01A> list = service1401.findL140m01aListByL120m01cMainId(
					model.getMainId(), "1");
			List<String> oldVal = new ArrayList<String>();
			// 分行別
			oldVal.add("%Branch_Name%");
			// 借款人名稱
			oldVal.add("%CName%");
			// 開會日期
			oldVal.add("%RPT_Title1%");
			// 授信概況科目
			oldVal.add("%ACCTNAME%");
			// 授信概況額度
			oldVal.add("%AMT1%");
			// 授信概況結欠
			oldVal.add("%AMT2%");
			// 擔保品概況
			oldVal.add("%COLSTATUS%");
			// 保證人
			oldVal.add("%GUARSTATUS%");
			// 決議事項
			oldVal.add("%HQComment1%");
			List<String> newVal = new ArrayList<String>();

			StringBuilder oldData = new StringBuilder();
			oldData.append(sbData);
			// 將資料區塊清空以便開始Append
			sbData.setLength(0);
			StringBuilder tempData = new StringBuilder();

			int count = 0;
			docName = "LMSDoc80";

			StringBuilder sbCustName = new StringBuilder();
			sbCustName.setLength(0);
			StringBuilder sbSub = new StringBuilder();
			sbSub.setLength(0);
			StringBuilder sbLnAmt = new StringBuilder();
			sbLnAmt.setLength(0);
			StringBuilder sbUnPay = new StringBuilder();
			sbUnPay.setLength(0);
			StringBuilder sbDanBow = new StringBuilder();
			sbDanBow.setLength(0);
			StringBuilder sbGuar = new StringBuilder();
			sbGuar.setLength(0);
			list = getSorted140(list, model);
			for (L140M01A l140m01a : list) {
				// 借款人名稱
				sbCustName.append(
						(sbCustName.length() > 0) ? " "
								: UtilConstants.Mark.SPACE).append(
						Util.trim(l140m01a.getCustName()));
				List<L140M01C> list2 = service1401
						.findL140m01cListByMainId(Util.trim(l140m01a
								.getMainId()));
				for (L140M01C l140m01c : list2) {
					// 授信額度種類(授信科目)
					// 額度明細表若有多個科目，請比照NOTES只要抓第一個科目，後面加等(只有一個科目就不用加等)
					sbSub.append(
							(sbSub.length() > 0) ? "<br/>"
									: UtilConstants.Mark.SPACE)
							.append(codeService.getDescOfCodeType(
									"lms1405m01_SubItem",
									Util.trim(l140m01c.getLoanTP())))
							.append((list2.size() > 1) ? "等" : "");
					break;
				}

				// 現請額度(單位：仟元)
				if (Util.isEmpty(Util.trim(l140m01a.getCurrentApplyCurr()))) {
					sbLnAmt.append("0");
				} else {
					sbLnAmt.append(
							(sbLnAmt.length() > 0) ? "<br/>"
									: UtilConstants.Mark.SPACE)
							.append(Util.trim(l140m01a.getCurrentApplyCurr()))
							.append(" ")
							.append(Util.trim(l140m01a
									.getCurrentApplyAmt()
									.divide(new BigDecimal("1000"),
											BigDecimal.ROUND_HALF_UP)
									.toString()));
				}

				// 餘額(單位：仟元) -- 結欠
				if (Util.isEmpty(Util.trim(l140m01a.getBLAmt()))) {
					sbUnPay.append(
							(sbUnPay.length() > 0) ? "<br/>"
									: UtilConstants.Mark.SPACE)
							.append((Util.isEmpty(Util.trim(l140m01a
									.getCurrentApplyCurr()))) ? "" : Util
									.trim(l140m01a.getCurrentApplyCurr()))
							.append("0");
				} else {
					sbUnPay.append(
							(sbUnPay.length() > 0) ? "<br/>"
									: UtilConstants.Mark.SPACE)
							.append(Util.trim(l140m01a.getBLCurr()))
							.append(" ")
							.append(Util.trim(l140m01a
									.getBLAmt()
									.divide(new BigDecimal("1000"),
											BigDecimal.ROUND_HALF_UP)
									.toString()));
				}

				// 擔保品
				L140M01B danbow = service1401.findL140m01bUniqueKey(
						Util.trim(l140m01a.getMainId()),
						UtilConstants.Cntrdoc.l140m01bItemType.擔保品);
				if (danbow != null) {
					danbow.getItemDscr();
					sbDanBow.append(
							(sbDanBow.length() > 0) ? "<br/>"
									: UtilConstants.Mark.SPACE).append(
							Util.getDocRealImgPath(Util.trim(danbow
									.getItemDscr())));
				} else {
					// 若無資料則設為空
					sbDanBow.append(UtilConstants.Mark.SPACE);
				}

				// 保證人/連保人
				sbGuar.append(
						(sbGuar.length() > 0) ? "<br/>"
								: UtilConstants.Mark.SPACE).append(
						l140m01a.getGuarantor());
			}
			// 開始替換資料
			// 將暫存內容初始化
			tempData.setLength(0);
			// 將保留區塊移到暫存
			tempData.append(oldData);

			// 清除要替換的資料內容
			newVal.clear();
			// 替換分行別
			newVal.add(branch.getBranchName(Util.trim(model.getCaseBrId())));
			// 替換借款人名稱
			newVal.add(sbCustName.toString());
			// 替換開會日期
			newVal.add(Util.trim(model.getRptTitle1()) + " 地點：801室");
			// 替換授信科目
			newVal.add(sbSub.toString());
			// 替換現請額度
			newVal.add(sbLnAmt.toString());
			// 替換結欠
			newVal.add(sbUnPay.toString());
			// 替換擔保品
			newVal.add(sbDanBow.toString());
			// 替換保證人
			newVal.add(sbGuar.toString());
			// 替換會議決議
			newVal.add((l120m01h == null) ? UtilConstants.Mark.SPACE : Util
					.trim(l120m01h.getMeetingNote()));

			// 替換表頭
			replaceStrB(tempData, newVal, oldVal);
			// 完成處理表頭資料
			// 將修改過得資料存進去
			sbData.append(tempData);

			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord.setLength(0);
			sbWord.append(sbHeader).append(sbData).append(sbFooter);

			// OutputStreamWriter outWriter = new OutputStreamWriter(baos,
			// "UTF-8");
			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "BIG5");
			outWriter.write(sbWord.toString());
			outWriter.close();
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 列印核貸通知書
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord5(PageParameters params)	throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		StringBuilder fileName942 = new StringBuilder();
		String fName = params.getString("fileName");

		// J-110-0097_05097_B1001 Web e-Loan企金授信修改出口押匯額度之「核貸通知書」格式
		String fName942 = Util.trim(params.getString("fileName2")); // LMSDoc15_942.htm

		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));

		// J-110-0097_05097_B1001 Web e-Loan企金授信修改出口押匯額度之「核貸通知書」格式
		fileName942.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName942));

		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得oid
		String oid = params.getString("oid");
		// 取得文件狀態
		String txCode = params.getString("txCode");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;

		// J-110-0097_05097_B1001 Web e-Loan企金授信修改出口押匯額度之「核貸通知書」格式
		// 出口押匯
		StringBuilder sbWord942 = new StringBuilder();
		FileInputStream fileInputStream942 = null;
		InputStreamReader inputStreamReader942 = null;
		BufferedReader reader942 = null;

		try {
			// FileUtils.readFileToString(file, "UTF-8");
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			// String str = FileUtils.readFileToString(file, "UTF-8");
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 找尋資料區塊開始字串
			String bodStr = "<div class=Section1";
			// 找尋資料區塊結束字串
			String bodEnd = "</body>";
			// 資料區塊開始位置
			int strBod = sbWord.indexOf(bodStr, 0);
			// 資料區塊結束位置
			int endBod = sbWord.indexOf(bodEnd, strBod);

			// 表頭區塊
			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(sbWord.substring(0, strBod));
			// 資料區塊
			StringBuilder sbData = new StringBuilder();
			sbData.append(sbWord.substring(strBod, endBod));
			// 表尾區塊
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(sbWord.substring(endBod));
			// 稽核資料是否都順利取得
			if (sbHeader == null || sbData == null || sbFooter == null) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1205V01Page.class);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", pop.getProperty("l120v01.error1")),
						getClass());
			}

			// J-110-0097_05097_B1001 Web e-Loan企金授信修改出口押匯額度之「核貸通知書」格式
			// 出口押匯
			URL urlRpt942 = null;
			urlRpt942 = Thread.currentThread().getContextClassLoader()
					.getResource(fileName942.toString());
			File file942 = new File(urlRpt942.toURI());
			fileInputStream942 = new FileInputStream(file942);
			inputStreamReader942 = new InputStreamReader(fileInputStream942);
			reader942 = new BufferedReader(inputStreamReader942);
			// String str = FileUtils.readFileToString(file, "UTF-8");
			String str942 = FileUtils.readFileToString(file942, "BIG5");
			sbWord942.append(str942);
			if (inputStreamReader942 != null) {
				inputStreamReader942.close();
			}

			// 找尋資料區塊開始字串
			String bodStr942 = "<div class=Section1";
			// 找尋資料區塊結束字串
			String bodEnd942 = "</body>";
			// 資料區塊開始位置
			int strBod942 = sbWord942.indexOf(bodStr942, 0);
			// 資料區塊結束位置
			int endBod942 = sbWord942.indexOf(bodEnd942, strBod942);

			// 表頭區塊
			StringBuilder sbHeader942 = new StringBuilder();
			sbHeader942.append(sbWord942.substring(0, strBod942));
			// 資料區塊
			StringBuilder sbData942 = new StringBuilder();
			sbData942.append(sbWord942.substring(strBod942, endBod942));
			// 表尾區塊
			StringBuilder sbFooter942 = new StringBuilder();
			sbFooter942.append(sbWord942.substring(endBod942));
			// 稽核資料是否都順利取得
			if (sbHeader942 == null || sbData942 == null || sbFooter942 == null) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1205V01Page.class);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", pop.getProperty("l120v01.error1")),
						getClass());
			}
			// **************************************************************

			L120M01A model = service1201.findL120m01aByOid(oid);
			// 取得額度批覆表
			List<L140M01A> list = service1401.findL140m01aListByL120m01cMainId(
					model.getMainId(), "2");
			// 讀不到批覆表再讀額度明細表
			if (list.isEmpty()) {
				list = service1401.findL140m01aListByL120m01cMainId(
						model.getMainId(), "1");
			}

			List<String> oldVal = new ArrayList<String>();
			oldVal.add("%LMSVAR001%");
			oldVal.add("%LMSVAR002%");
			oldVal.add("%LMSVAR003%");
			oldVal.add("%LMSVAR004%");
			oldVal.add("%LMSVAR005%");
			oldVal.add("%LMSVAR006%");
			oldVal.add("%LMSVAR007%");
			oldVal.add("%LMSVAR008%");
			oldVal.add("%LMSVAR009%");
			oldVal.add("%LMSVAR010%");
			oldVal.add("%LMSVAR011%");
			oldVal.add("%LMSVAR012%");

			// J-110-0097_05097_B1001 Web e-Loan企金授信修改出口押匯額度之「核貸通知書」格式
			List<String> oldVal942 = new ArrayList<String>();
			oldVal942.add("%LMSVAR001%");
			oldVal942.add("%LMSVAR002%");
			oldVal942.add("%LMSVAR003%");
			oldVal942.add("%LMSVAR004%");
			oldVal942.add("%LMSVAR005%");
			oldVal942.add("%LMSVAR006%");
			oldVal942.add("%LMSVAR007%");
			oldVal942.add("%LMSVAR008%");
			oldVal942.add("%LMSVAR009%");
			oldVal942.add("%LMSVAR010%");
			oldVal942.add("%LMSVAR011%");
			oldVal942.add("%LMSVAR012%");
			oldVal942.add("%LMSVAR013%");

			List<String> newVal = new ArrayList<String>();

			StringBuilder oldData = new StringBuilder();
			oldData.append(sbData);

			// J-110-0097_05097_B1001 Web e-Loan企金授信修改出口押匯額度之「核貸通知書」格式
			StringBuilder oldData942 = new StringBuilder();
			oldData942.append(sbData942);

			// 將資料區塊清空以便開始Append
			sbData.setLength(0);
			StringBuilder tempData = new StringBuilder();

			int count = 0;
			docName = "LMSDoc15";
			// 開始替換資料
			for (L140M01A l140m01a : list) {
				// 將暫存內容初始化
				tempData.setLength(0);

				String checkItem18 = "942"; // 出口押匯

				Set<L140M01C> l140m01cs = l140m01a.getL140m01c();

				HashMap<String, String> itemMap = new HashMap<String, String>();
				for (L140M01C l140m01c : l140m01cs) {
					itemMap.put(l140m01c.getLoanTP(), "");
				}

				// I-108-0259 特定分行額度明細表性質非不變或取消，不可以簽科目左邊三碼為 941 942 944 715 950
				// 971
				boolean hasCheckItem18 = false;

				// I-108-0259 特定分行額度明細表性質非不變或取消，不可以簽科目左邊三碼為 941 942 944 715 950
				String[] item18 = checkItem18.split(",");
				List<String> asList18 = Arrays.asList(item18);

				for (String key : itemMap.keySet()) {
					// I-108-0259 特定分行額度明細表性質非不變或取消，不可以簽科目左邊三碼為 941 942 944 715
					// 950
					if (asList18.contains(Util.getLeftStr(key, 3))) {
						hasCheckItem18 = true;
					}

				}

				// 將保留區塊移到暫存
				// J-110-0097_05097_B1001 Web e-Loan企金授信修改出口押匯額度之「核貸通知書」格式
				if (!hasCheckItem18) {
					// 非出口押匯
					tempData.append(oldData);
				} else {
					// 出口押匯
					tempData.append(oldData942);
				}

				// 清除要替換的資料內容
				newVal.clear();

				// 編製單位
				// newVal.add(XmlTool.replaceXMLReservedWord(branch.getBranchName
				// (Util.trim(l140m01a.getOwnBrId())),true));
				newVal.add(branch.getBranchName(Util.trim(l140m01a.getOwnBrId())));
				// 日期
				// newVal.add(XmlTool.replaceXMLReservedWord(CapDate.getCurrentDate("yyyy/MM/dd"),true));
				newVal.add(CapDate.getCurrentDate("yyyy/MM/dd"));

				// 授信總額度(單位：仟元)
				StringBuilder loanTot = new StringBuilder();
				if (Util.isEmpty(Util.trim(l140m01a.getLoanTotCurr()))) {
					loanTot.append("TWD").append("0");
				} else {
					// loanTot.append(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getLoanTotCurr()),true))
					// .append(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getLoanTotAmt()
					// .divide(new BigDecimal("1000")).toString()),true));
					loanTot.append(Util.trim(l140m01a.getLoanTotCurr()))
							.append(Util.trim(NumConverter.addComma(l140m01a
									.getLoanTotAmt().divide(
											new BigDecimal("1000"),
											BigDecimal.ROUND_HALF_UP))))
							.append("仟元整");
				}
				newVal.add(loanTot.toString());

				// 授信額度種類(授信科目)
				// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getLnSubject()),true));
				// newVal.add(Util.trim(l140m01a.getLnSubject()));
				L140M01B l140m01b1 = service1401.findL140m01bUniqueKey(
						Util.trim(l140m01a.getMainId()), "1");

				// (出口押匯額度 USD5,000,000元，瑕疵單據押匯限額USD3,000,000元)
				StringBuffer lnSubjectBuf = new StringBuffer(Util.trim(l140m01a
						.getLnSubject()));
				// J-110-0097_05097_B1001 Web e-Loan企金授信修改出口押匯額度之「核貸通知書」格式
				if (hasCheckItem18) {
					lnSubjectBuf.append("(出口押匯額度");
					lnSubjectBuf.append(Util.trim(l140m01a
							.getCurrentApplyCurr()));
					lnSubjectBuf.append(
							Util.trim(NumConverter.addComma(l140m01a
									.getCurrentApplyAmt()))).append("元");
					if (Util.equals(Util.trim(l140m01a.getFlaw_fg()), "1")) {
						lnSubjectBuf.append("，瑕疵單據押匯限額同出口押匯額度");
					} else if (Util.equals(Util.trim(l140m01a.getFlaw_fg()),
							"2")) {

						lnSubjectBuf.append("，瑕疵單據押匯限額");
						lnSubjectBuf.append(Util.trim(l140m01a
								.getCurrentApplyCurr()));
						lnSubjectBuf.append(
								NumConverter.addComma(l140m01a.getFlaw_amt()))
								.append("元");
					} else if (Util.equals(Util.trim(l140m01a.getFlaw_fg()),
							"3")) {

						lnSubjectBuf.append("，不得瑕疵單據押匯");
					}
					lnSubjectBuf.append(")");
				}

				if (l140m01b1 != null) {
					// l140m01b1.getItemDscr();
					newVal.add(lnSubjectBuf.toString()
							+ "<br/>"
							+ Util.trim(l140m01b1.getItemDscr()).replace("\r",
									"<br/>"));
				} else {
					// 若無資料則設為空
					newVal.add(lnSubjectBuf.toString());
				}

				// 動用方式
				if ("2".equals(Util.trim(l140m01a.getReUse()))) {
					newVal.add("循環動用");
				} else {
					newVal.add("不循環動用");
				}

				// 動用期限
				StringBuilder sbUseDead = new StringBuilder();
				if (Util.isEmpty(Util.trim(l140m01a.getUseDeadline()))) {
					newVal.add("");
				} else {
					newVal.add(getUseDeadline(
							Util.trim(l140m01a.getUseDeadline()),
							Util.trim(l140m01a.getDesp1())));
				}

				// 利(費)率
				L140M01B l140m01b = service1401.findL140m01bUniqueKey(
						Util.trim(l140m01a.getMainId()), "2");

				// J-111-0189_05097_B1001 Web
				// e-Loan企金授信管理系統修改「列印核貸通知書」增加「授信作業手續費」之計收條款等程式。
				String rateStr = "";
				if (l140m01b != null) {
					rateStr = Util.getDocRealImgPath(Util.trim(l140m01b
							.getItemDscr()));

					// 因為是CKeditor所以要過濾掉不要的標籤
					// newVal.add("<![CDATA["
					// +
					// XmlTool.replaceXMLReservedWord(filterHtml(Util.trim(l140m01b
					// .getItemDscr())) + "]]>",true));

				} else {
					// 若無資料則設為空
					// newVal.add("");
				}

				// J-111-0189_05097_B1001 Web
				// e-Loan企金授信管理系統修改「列印核貸通知書」增加「授信作業手續費」之計收條款等程式。
				if (Util.equals(Util.trim(l140m01a.getIsOperationFee()), "Y")) {

					String opFeeStr = "授信作業手續費：【       】元，應自簽約日起算5個營業日內一次繳付，未來如有續約、增額、變更授信條件等情形，另行繳付授信作業手續費。";
					if (Util.isEmpty(rateStr)) {
						rateStr = opFeeStr;
					} else {
						if (Util.equals(Util.getRightStr(rateStr, 1), "。")) {
							rateStr = Util.getLeftStr(rateStr,
									StringUtils.length(rateStr) - 1)
									+ "，" + opFeeStr;
						}

					}
				}

				newVal.add(rateStr);

				// 償還期限(清償期限)
				// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getPayDeadline()),true));
				newVal.add(Util.trim(l140m01a.getPayDeadline()));

				// 擔保品
				L140M01B danbow = service1401.findL140m01bUniqueKey(
						Util.trim(l140m01a.getMainId()),
						UtilConstants.Cntrdoc.l140m01bItemType.擔保品);
				if (danbow != null) {
					danbow.getItemDscr();
					// 因為是CKeditor所以要過濾掉不要的標籤
					// newVal.add("<![CDATA["
					// +
					// XmlTool.replaceXMLReservedWord(filterHtml(Util.trim(danbow
					// .getItemDscr())) + "]]>",true));
					newVal.add(Util.getDocRealImgPath(Util.trim(danbow
							.getItemDscr())));
					// newVal.add(Util.trim(danbow.getItemDscr()));
				} else {
					// 若無資料則設為空
					newVal.add("");
				}

				// 連帶保證人
				// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getGuarantor()),true));
				newVal.add(Util.trim(l140m01a.getGuarantor()));

				// 其他條件(其他敘做條件)
				L140M01B otherIf = service1401.findL140m01bUniqueKey(
						Util.trim(l140m01a.getMainId()),
						UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件);
				if (otherIf != null) {
					otherIf.getItemDscr();
					// 因為是CKeditor所以要過濾掉不要的標籤
					// newVal.add("<![CDATA["
					// +
					// XmlTool.replaceXMLReservedWord(filterHtml(Util.trim(otherIf
					// .getItemDscr())) + "]]>",true));
					newVal.add(Util.getDocRealImgPath(Util.trim(otherIf
							.getItemDscr())));
					// newVal.add(Util.trim(otherIf.getItemDscr()));
				} else {
					// 若無資料則設為空
					newVal.add("");
				}

				// 主要借款人
				// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getCustName()),true));
				newVal.add(Util.trim(l140m01a.getCustName()));

				// 本票
				// J-110-0097_05097_B1001 Web e-Loan企金授信修改出口押匯額度之「核貸通知書」格式
				if (hasCheckItem18) {
					// 出口押匯
					newVal.add(Util.trim(l140m01a.getCheckNote()));
				}

				if (count < list.size() - 1) {
					// 如不是最後一筆資料則插入換頁符號
					// tempData.append(tblPage);
					// tempData.append('<p
					// style="page-break-before:always"></p>');
					tempData.append(wordPage);
				}

				// 替換表頭
				// J-110-0097_05097_B1001 Web e-Loan企金授信修改出口押匯額度之「核貸通知書」格式
				if (!hasCheckItem18) {
					// 非出口押匯
					replaceStrB(tempData, newVal, oldVal);
				} else {
					// 出口押匯
					replaceStrB(tempData, newVal, oldVal942);
				}

				// 完成處理表頭資料
				// 將修改過得資料存進去
				sbData.append(tempData);
				count++;
			}

			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord.setLength(0);
			sbWord.append(sbHeader).append(sbData).append(sbFooter);

			// OutputStreamWriter outWriter = new OutputStreamWriter(baos,
			// "UTF-8");
			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "BIG5");
			outWriter.write(sbWord.toString());
			outWriter.close();
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 取得授信案件明細Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord4(PageParameters params)	throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String strSign = Util.nullToSpace(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(strSign);
		// 取得文件狀態
		String txCode = params.getString("txCode");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;

		try {
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			// 第一個變數字串
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 找尋資料區塊開始字串
			// String bodStr = "<div class=Section1";
			String bodStr = "%HeadEnd%";
			// 找尋資料區塊結束字串
			String bodEnd = "</table>";
			// 資料區塊開始位置
			int strBod = sbWord.indexOf(bodStr, 0) + bodStr.length();
			;
			// 資料區塊結束位置
			int endBod = sbWord.indexOf(bodEnd, strBod);

			// 表頭區塊
			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(sbWord.substring(0, strBod));
			// 資料區塊
			StringBuilder sbData = new StringBuilder();
			sbData.append(sbWord.substring(strBod, endBod));
			// 表尾區塊
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(sbWord.substring(endBod));

			// 稽核資料是否都順利取得
			if (sbHeader == null || sbData == null || sbFooter == null) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1205V01Page.class);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", pop.getProperty("l120v01.error1")),
						getClass());
			}

			// 餘額日期
			String var001B = "";
			// 製表日期
			StringBuilder var002B = new StringBuilder();
			docName = "LMSDoc71";

			List<String> oldVal = new ArrayList<String>();
			List<String> newVal = new ArrayList<String>();

			// 開始處理明細資料區塊
			StringBuilder oldData = new StringBuilder();
			oldData.append(sbData);
			// 將資料區塊清空以便開始Append
			sbData.setLength(0);
			StringBuilder tempData = new StringBuilder();
			oldVal.clear();
			oldVal.add("%LMSREP001%");
			oldVal.add("%LMSREP002%");
			oldVal.add("%LMSREP003%");
			oldVal.add("%LMSREP004%");
			oldVal.add("%LMSREP005%");
			oldVal.add("%LMSREP006%");
			oldVal.add("%LMSREP007%");

			for (int i = 0; i < oidArray.length; i++) {
				// 將暫存內容初始化
				tempData.setLength(0);
				newVal.clear();
				// 承辦單位
				newVal.add("");
				// 客戶名稱
				newVal.add("");
				// 授信科目
				newVal.add("");
				// 額度(單位：仟元)
				newVal.add("");
				// 餘額(單位：仟元)
				newVal.add("");
				// 擔保品
				newVal.add("");
				// 備註
				newVal.add("");
				// 將保留區塊移到暫存
				tempData.append(oldData);
				L120M01A model = service1201.findL120m01aByOid(oidArray[i]);
				List<L140M01A> list = service1401
						.findL140m01aListByL120m01cMainId(model.getMainId(),
								"2");
				if (list.isEmpty()) {
					list = service1401.findL140m01aListByL120m01cMainId(
							model.getMainId(), "1");
				}
				// 開始替換資料...
				for (L140M01A l140m01a : list) {
					if (FlowDocStatusEnum.已核准.toString().equals(
							l140m01a.getDocStatus())) {
						newVal.clear();
						// 2為國外 ，當行別為國外時查DW 其他查 mis
						if (UtilConstants.BrNoType.國外
								.equals(branch.getBranch(
										Util.trim(l140m01a.getCntrNo())
												.substring(0, 3)).getBrNoFlag())) {
							// 抓DW
							var001B = service1201.getLoanDate2(l140m01a,
									var001B);
						} else {
							// 抓MIS
							var001B = service1201
									.getLoanDate(l140m01a, var001B);
						}

						// 承辦單位
						newVal.add(branch.getBranchName(Util.trim(l140m01a
								.getOwnBrId())));
						// 客戶名稱
						newVal.add(Util.trim(l140m01a.getCustName()));
						// 授信科目
						newVal.add(Util.trim(l140m01a.getLnSubject()));
						// 額度(單位：仟元)
						StringBuilder sbCur = new StringBuilder();
						if (Util.isEmpty(Util.trim(l140m01a
								.getCurrentApplyCurr()))) {
							sbCur.append("TWD").append("0");
						} else {
							sbCur.append(
									Util.trim(l140m01a.getCurrentApplyCurr()))
									.append(NumConverter.addComma(Util
											.trim(l140m01a
													.getCurrentApplyAmt()
													.divide(new BigDecimal(
															"1000")).toString())));
						}
						newVal.add(sbCur.toString());
						// 餘額(單位：仟元)
						StringBuilder sbBl = new StringBuilder();
						if (Util.isEmpty(Util.trim(l140m01a.getBLCurr()))) {
							sbBl.append("TWD").append("0");
						} else {
							sbBl.append(Util.trim(l140m01a.getBLCurr()))
									.append(NumConverter.addComma(Util
											.trim(l140m01a
													.getBLAmt()
													.divide(new BigDecimal(
															"1000")).toString())));
						}
						newVal.add(sbBl.toString());
						// 擔保品
						L140M01B danbow = service1401.findL140m01bUniqueKey(
								l140m01a.getMainId(), "3");
						if (danbow != null) {
							// 因為是CKeditor所以要過濾掉不要的標籤
							newVal.add(Util.getDocRealImgPath(Util.trim(danbow
									.getItemDscr())));
							// newVal.add(Util.trim(danbow.getItemDscr()));
						} else {
							newVal.add("");
						}
						// 備註
						L140M01B psCon = service1401.findL140m01bUniqueKey(
								l140m01a.getMainId(), "4");
						if (psCon != null) {
							// 因為是CKeditor所以要過濾掉不要的標籤
							newVal.add(Util.getDocRealImgPath(Util.trim(psCon
									.getItemDscr())));
							// newVal.add(Util.trim(psCon.getItemDscr()));
						} else {
							newVal.add("");
						}
					}
				}
				replaceStrB(tempData, newVal, oldVal);
				// 將修改過的資料區塊存進去
				sbData.append(tempData.toString());
			}

			// 開始替換資料(表頭)
			newVal.clear();
			oldVal.clear();
			var002B.append(CapDate.getCurrentDate("yyyy/MM/dd"));
			newVal.add(var001B);
			newVal.add(var002B.toString());

			oldVal.add("%LMSVAR001%");
			oldVal.add("%LMSVAR002%");
			// 替換表頭
			replaceStrB(sbHeader, newVal, oldVal);
			// 完成處理表頭資料

			// 替換表頭日期
			replaceStrB(sbData, newVal, oldVal);

			// 最後把表頭關鍵Key清掉避免殘留
			newVal.clear();
			oldVal.clear();
			newVal.add("");
			oldVal.add(bodStr);
			replaceStrB(sbHeader, newVal, oldVal);
			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord.setLength(0);
			sbWord.append(sbHeader).append(sbData).append(sbFooter);

			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "BIG5");
			outWriter.write(sbWord.toString());
			outWriter.close();

		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 取得議程Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord1b(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String strSign = Util.nullToSpace(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(strSign);
		// 取得文件狀態
		String txCode = params.getString("txCode");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		try {
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);

			// 找尋資料區塊開始字串
			String bodStr = "<tr class='dataHtml'";
			// 找尋資料區塊結束字串
			String bodEnd = "</table>";
			// // 第一個變數字串
			// String strChk = "%LMSREP001%";
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 資料區塊開始位置
			int bgnIndex = sbWord.indexOf(bodStr, 0);
			// 資料區塊結束位置
			int endIndex = sbWord.indexOf(bodEnd, bgnIndex);

			// 表頭區塊
			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(sbWord.substring(0, bgnIndex));
			// 資料區塊
			StringBuilder sbData = new StringBuilder();
			sbData.append(sbWord.substring(bgnIndex, endIndex));
			// 表尾區塊
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(sbWord.substring(endIndex));

			// 稽核資料是否都順利取得
			if (sbHeader == null || sbData == null || sbFooter == null) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1205V01Page.class);
				throw new CapMessageException(RespMsgHelper.getMessage(
						"EFD0025", pop.getProperty("l120v01.error1")),getClass());
			}

			// 開始替換資料(表頭)
			L120M01A model = service1201.findL120m01aByOid(oidArray[0]);
			List<L120S01A> list = null;
			L120S01A mainL120s01a = null;
			String title = "";
			String hqMeetFlag = Util.trim(model.getHqMeetFlag());
			// 稽核會期資料是否存在
			if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				title = model.getRptTitle2();
			} else {
				title = model.getRptTitle1();
			}
			if (Util.isEmpty(title)) {
				// Properties pop = MessageBundleScriptCreator
				// .getComponentResource(LMS1205V01Page.class);
				// throw new CapMessageException(RespMsgHelper.getMessage(
				// parent, "EFD0025",
				// pop.getProperty("l120v01.error5")), getClass());
			}
			// YYY年度第99次授信審議小組（委員會）會議議程
			StringBuilder var001B = new StringBuilder();
			String title1 = "";
			String title2 = "";
			if ("1".equals(hqMeetFlag) || "2".equals(hqMeetFlag)
					|| "A".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
				// 授審會
				docName = "LMSDoc11";
				title1 = Util.trim(model.getRptTitle1()).substring(0, 4);
				title2 = Util.trim(model.getRptTitle1()).substring(10);
			} else if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				// 常董會
				docName = "LMSDoc1";
				title1 = Util.trim(model.getRptTitle2()).substring(0, 10);
				title2 = Util.trim(model.getRptTitle2()).substring(10);
			}
			var001B.append(XmlTool.replaceXMLReservedWord(title1, true))
					.append(XmlTool.replaceXMLReservedWord(title2, true))
					.append("會議議程");
			List<String> oldVal = new ArrayList<String>();
			List<String> newVal = new ArrayList<String>();

			if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				newVal.add(var001B.toString());
				oldVal.add("%LMSVAR001%");
			} else {
				// 開會時間：YYY年MM月DD日XX時XX分　　　 開會地點：ＸＸＸＸＸＸＸ
				StringBuilder var002B = new StringBuilder();
				var002B.append("開會時間：")
						.append(XmlTool.replaceXMLReservedWord(
								Util.trim(model.getRptTitle1()), true)
								.substring(0, 10)).append(caseTime)
						.append("　　　　　").append("開會地點：").append(casePlace);

				newVal.add(var001B.toString());
				newVal.add(var002B.toString());
				oldVal.add("%LMSVAR001%");
				oldVal.add("%LMSVAR002%");
			}
			// 替換表頭
			replaceStrB(sbHeader, newVal, oldVal);
			// 完成處理表頭資料

			// 開始處理明細資料區塊
			StringBuilder oldData = new StringBuilder();
			oldData.append(sbData);
			// 將資料區塊清空以便開始Append
			sbData.setLength(0);
			StringBuilder tempData = new StringBuilder();
			oldVal.clear();
			if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				oldVal.add("%LMSREP001%");
			} else {
				oldVal.add("%LMSREP001%");
				oldVal.add("%LMSREP002%");
				oldVal.add("%LMSREP003%");
				oldVal.add("%LMSREP004%");
			}

			if ("2".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
				// 催收會第1筆資料固定給授管處用，記錄上次催收會討論事項
				// 將暫存內容初始化
				tempData.setLength(0);
				newVal.clear();
				// 將保留區塊移到暫存
				tempData.append(oldData);
				L120M01A l120m01a = service1201.findL120m01aByOid(oidArray[0]);
				// 開始替換資料...
				// 提案單位別
				// UFO@20130618:調整以NGFLAG判斷部門名稱
				newVal.add(NGFlagHelper.getDeptName(user, branch));
				// 客戶別/案由
				// 本次會期次數-1
				int indexTime = Util.trim(l120m01a.getRptTitle1()).indexOf("次");
				String times = ("".equals(Util.trim(l120m01a.getRptTitle1())
						.substring(10, indexTime))) ? "0" : String.valueOf(Util
						.parseInt(Util.trim(l120m01a.getRptTitle1()).substring(
								11, indexTime)) - 1);
				StringBuilder sbCase = new StringBuilder();
				sbCase.append("第" + XmlTool.replaceXMLReservedWord(times, true)
						+ "次催收會決議事項報請鑒察案");
				newVal.add(sbCase.toString());
				// 經辦同仁
				newVal.add("");
				// 覆審主管
				newVal.add("");
				replaceStrB(tempData, newVal, oldVal);
				// 將修改過的資料區塊存進去
				sbData.append(tempData.toString());
			}
			for (int i = 0; i < oidArray.length; i++) {
				if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
					// 將暫存內容初始化
					tempData.setLength(0);
					newVal.clear();
					// 將保留區塊移到暫存
					tempData.append(oldData);
					L120M01A l120m01a = service1201
							.findL120m01aByOid(oidArray[i]);
					// 開始替換資料...
					// 案由(預設簽報書案由)
					StringBuilder sbCase = new StringBuilder();
					sbCase.append(
							Util.trim(l120m01a.getGist())
									.replace("\n", "<br/>")).append("<br/>");
					// newVal.add(XmlTool.replaceXMLReservedWord(sbCase.toString());
					if (國外部.equals(user.getUnitNo())) {
						// 國外部提
						sbCase.append("(國外部提)");
					} else {
						// UFO@20130618:調整判斷授管處OR債管處，並取消判斷企個金
						String gistTitle = NGFlagHelper.getDeptName(user,
								branch) + "提\\";
						// if ("2".equals(Util.trim(l120m01a.getDocType()))) {
						// // 個金
						// gistTitle = "個人金融處提\\";
						// } else {
						// // 企金
						// gistTitle = "授信管理處提\\";
						// }

						sbCase.append("（")
								.append(gistTitle)
								.append(XmlTool.replaceXMLReservedWord(branch
										.getBranchName(Util.trim(l120m01a
												.getCaseBrId())), true))
								.append("承做").append("）");
					}
					newVal.add(sbCase.toString());
					replaceStrB(tempData, newVal, oldVal);
					// 將修改過的資料區塊存進去
					sbData.append(tempData.toString());
				} else {
					// 將暫存內容初始化
					tempData.setLength(0);
					newVal.clear();
					// 將保留區塊移到暫存
					tempData.append(oldData);
					L120M01A l120m01a = service1201
							.findL120m01aByOid(oidArray[i]);
					list = service1201.findL120s01aByMainId(Util.trim(l120m01a
							.getMainId()));
					mainL120s01a = service1201.findL120s01aByUniqueKey(
							Util.trim(l120m01a.getMainId()),
							Util.trim(l120m01a.getCustId()),
							Util.trim(l120m01a.getDupNo()));
					StringBuilder tempSb = new StringBuilder();
					StringBuilder tempMainSb = new StringBuilder();
					tempSb.setLength(0);
					tempMainSb.setLength(0);
					for (L120S01A l120s01a : list) {
						if (Util.trim(l120m01a.getCustId()).equals(
								l120s01a.getCustId())
								&& Util.trim(l120m01a.getDupNo()).equals(
										l120s01a.getDupNo())) {
							tempMainSb
									.append(Util.trim(l120s01a.getCustName()));
						} else {
							tempSb.append(
									(tempSb.length() > 0) ? "，"
											: UtilConstants.Mark.SPACE).append(
									Util.trim(l120s01a.getCustName()));
						}
					}
					tempMainSb.append((tempSb.length() > 0) ? "，" : "").append(
							tempSb.toString());
					// 開始替換資料...
					// 提案單位別
					newVal.add(XmlTool.replaceXMLReservedWord(branch
							.getBranchName(Util.trim(l120m01a.getCaseBrId())),
							true));
					// 客戶別/案由
					newVal.add(XmlTool.replaceXMLReservedWord(
							tempMainSb.toString(), true));
					// 經辦同仁
					newVal.add(XmlTool.replaceXMLReservedWord(
							getPerName(Util.trim(l120m01a.getCreator())), true));
					// 覆審主管
					newVal.add(XmlTool.replaceXMLReservedWord(
							getPerName(Util.trim(l120m01a.getApprover())), true));
					replaceStrB(tempData, newVal, oldVal);
					// 將修改過的資料區塊存進去
					sbData.append(tempData.toString());
				}
			}

			// 全部完成後塞空白行
			// 將暫存內容初始化
			tempData.setLength(0);
			newVal.clear();
			// 將保留區塊移到暫存
			tempData.append(oldData);
			if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				newVal.add(XmlTool.replaceXMLReservedWord(
						UtilConstants.Mark.SPACE, true));
			} else {
				newVal.add(XmlTool.replaceXMLReservedWord(
						UtilConstants.Mark.SPACE, true));
				newVal.add(XmlTool.replaceXMLReservedWord(
						UtilConstants.Mark.SPACE, true));
				newVal.add(XmlTool.replaceXMLReservedWord(
						UtilConstants.Mark.SPACE, true));
				newVal.add(XmlTool.replaceXMLReservedWord(
						UtilConstants.Mark.SPACE, true));
			}
			replaceStrB(tempData, newVal, oldVal);
			// 將修改過的資料區塊存進去
			sbData.append(tempData.toString());

			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord.setLength(0);
			sbWord.append(sbHeader).append(sbData).append(sbFooter);

			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "BIG5");
			outWriter.write(sbWord.toString());
			outWriter.close();

		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * 
	 * @param id
	 *            使用者id
	 * @return 空值: 使用者id 非空值: 使用者名稱
	 */
	public String getPerName(String id) {
		return (Util.isNotEmpty(userSrv.getUserName(id)) ? userSrv
				.getUserName(id) : UtilConstants.Mark.SPACE);
	}

	/**
	 * 取得決議錄
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	public OutputStream getWord2a(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();

		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		boolean isArea = params.getBoolean("isArea");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String selOid = params.getString("selOid");
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String strSign = Util.nullToSpace(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(strSign);
		// 取得文件狀態
		String txCode = params.getString("txCode");
		List<String> listSOid = new ArrayList<String>();
		// 決議錄(同會期之全部文件適用)
		if (!Util.isEmpty(selOid)) {
			L120M01A selModel = service1201.findL120m01aByOid(selOid);
			String hqMeetFlag = Util.trim(selModel.getHqMeetFlag());
			for (int i = 0; i < oidArray.length; i++) {
				L120M01A model = service1201.findL120m01aByOid(oidArray[i]);
				if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
					if (Util.trim(selModel.getRptTitle2()).equals(
							Util.trim(model.getRptTitle2()))) {
						listSOid.add(oidArray[i]);
					}
				} else {
					if (isArea) {
						// 營運中心所有提會案件產決議錄
						if (Util.trim(selModel.getRptTitleArea1()).equals(
								Util.trim(model.getRptTitleArea1()))) {
							listSOid.add(oidArray[i]);
						}
					} else if (Util.trim(selModel.getRptTitle1()).equals(
							Util.trim(model.getRptTitle1()))) {
						listSOid.add(oidArray[i]);
					}
				}
			}
		}
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		try {
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			// // 表頭開始字串
			// String bgnStr = "<w:tr";
			// // 表尾開始字串
			// String endStr = "</w:tr>";
			// // 第一個變數字串
			// String strChk = "%LMSREP001%";
			// 找尋資料區塊開始字串
			String bodStr = "<tr class='dataHtml'";
			// 找尋資料區塊結束字串
			String bodEnd = "</table>";
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 資料區塊開始位置
			int bgnIndex = sbWord.indexOf(bodStr, 0);
			// 資料區塊結束位置
			int endIndex = sbWord.indexOf(bodEnd, bgnIndex);

			// 表頭區塊
			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(sbWord.substring(0, bgnIndex));
			// 資料區塊
			StringBuilder sbData = new StringBuilder();
			sbData.append(sbWord.substring(bgnIndex, endIndex));
			// 表尾區塊
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(sbWord.substring(endIndex));

			// 開始替換資料(表頭)
			L120M01A model;
			if (!listSOid.isEmpty()) {
				model = service1201.findL120m01aByOid(listSOid.get(0));
			} else {
				model = service1201.findL120m01aByOid(oidArray[0]);
			}
			String hqMeetFlag = Util.trim(model.getHqMeetFlag());
			String title = "";
			// 稽核會期資料是否存在
			if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				title = model.getRptTitle2();
			} else {
				if (isArea) {
					title = model.getRptTitleArea1();
				} else {
					title = model.getRptTitle1();
				}
			}
			// if (Util.isEmpty(title)) {
			// Properties pop = MessageBundleScriptCreator
			// .getComponentResource(LMS1205V01Page.class);
			// throw new CapMessageException(RespMsgHelper.getMessage(
			// parent, "EFD0025",
			// pop.getProperty("l120v01.error5")), getClass());
			// }
			// YYY年度第12屆第2次授審會會議決議錄
			StringBuilder var001B = new StringBuilder();
			String title1 = "";
			String title2 = "";
			if (isArea) {
				docName = "LMSDoc2";
				title1 = Util.trim(model.getRptTitleArea1()).substring(0, 4);
				title2 = Util.trim(model.getRptTitleArea1()).substring(10);
			} else if ("1".equals(hqMeetFlag) || "A".equals(hqMeetFlag)) {
				// 授審會
				docName = "LMSDoc2";
				title1 = Util.trim(model.getRptTitle1()).substring(0, 10);
				title2 = Util.trim(model.getRptTitle1()).substring(10);
			} else if ("2".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
				// 催收會
				docName = "LMSDoc21";
				title1 = Util.trim(model.getRptTitle1()).substring(0, 4)
						+ ("度");
				title2 = Util.trim(model.getRptTitle1()).substring(10);
			} else if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				// 常董會
				docName = "LMSDoc3";
				title1 = Util.trim(model.getRptTitle2()).substring(0, 10);
				title2 = Util.trim(model.getRptTitle2()).substring(10);
			}
			var001B.append(XmlTool.replaceXMLReservedWord(title1, true))
					.append(title2).append("會議決議錄");
			// 開會時間：YYY年MM月DD日XX時XX分　　　 開會地點：ＸＸＸＸＸＸＸ
			StringBuilder var002B = new StringBuilder();
			if (!"3".equals(hqMeetFlag) && !"C".equals(hqMeetFlag)) {
				if (isArea) {
					var002B.append("開會時間：")
							.append(XmlTool.replaceXMLReservedWord(
									Util.trim(model.getRptTitleArea1())
											.substring(0, 10), true))
							.append(caseTime).append("\t\t\t\t")
							.append("開會地點：").append(casePlace);
				} else {
					var002B.append("開會時間：")
							.append(XmlTool.replaceXMLReservedWord(
									Util.trim(model.getRptTitle1()).substring(
											0, 10), true)).append(caseTime)
							.append("\t\t\t\t").append("開會地點：")
							.append(casePlace);
				}
			}
			List<String> oldVal = new ArrayList<String>();
			List<String> newVal = new ArrayList<String>();
			newVal.add(var001B.toString());
			if ("2".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
				// 催收會範本有兩個表頭變數
				oldVal.add("%LMSVAR001%");
				oldVal.add("%LMSVAR002%");
				// 將第二個表頭變數值加入
				newVal.add(var002B.toString());
			} else {
				oldVal.add("%LMSVAR001%");
			}
			// 替換表頭
			replaceStrB(sbHeader, newVal, oldVal);
			// 完成處理表頭資料

			// 開始處理明細資料區塊
			StringBuilder oldData = new StringBuilder();
			oldData.append(sbData);
			// 將資料區塊清空以便開始Append
			sbData.setLength(0);
			StringBuilder tempData = new StringBuilder();
			oldVal.clear();
			if ("1".equals(hqMeetFlag) || isArea || "A".equals(hqMeetFlag)) {
				// 授審會範本有四個資料變數
				oldVal.add("%LMSREP001%");
				oldVal.add("%LMSREP002%");
				oldVal.add("%LMSREP003%");
				oldVal.add("%LMSREP004%");
			} else if ("2".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
				// 催收會範本有三個資料變數
				oldVal.add("%LMSREP001%");
				oldVal.add("%LMSREP002%");
				oldVal.add("%LMSREP003%");
			} else if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				// 常董會有兩個資料變數
				oldVal.add("%LMSREP001%");
				oldVal.add("%LMSREP002%");
			}
			if ("2".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
				// 催收會第1筆資料固定給授管處用，記錄上次催收會討論事項
				// 將暫存內容初始化
				tempData.setLength(0);
				newVal.clear();
				// 將保留區塊移到暫存
				tempData.append(oldData);
				L120M01A l120m01a = service1201.findL120m01aByOid(oidArray[0]);
				// 開始替換資料...
				// 承辦營業單位
				// UFO@20130618:調整以NGFLAG判斷部門名稱
				newVal.add(NGFlagHelper.getDeptName(user, branch));
				// 客戶別/案由
				// 本次會期次數-1
				int indexTime = Util.trim(l120m01a.getRptTitle1()).indexOf("次");
				String times = ("".equals(Util.trim(l120m01a.getRptTitle1())
						.substring(10, indexTime))) ? "0" : String.valueOf(Util
						.parseInt(Util.trim(l120m01a.getRptTitle1()).substring(
								11, indexTime)) - 1);
				StringBuilder sbCase = new StringBuilder();
				sbCase.append("第" + times + "次催收會決議事項報請鑒察案");
				newVal.add(XmlTool.replaceXMLReservedWord(sbCase.toString(),
						true));
				// 結論
				newVal.add("確認。");
				replaceStrB(tempData, newVal, oldVal);
				// 將修改過的資料區塊存進去
				sbData.append(tempData.toString());
			}
			if (!listSOid.isEmpty()) {
				for (int i = 0; i < listSOid.size(); i++) {
					// 將暫存內容初始化
					tempData.setLength(0);
					newVal.clear();
					// 將保留區塊移到暫存
					tempData.append(oldData);
					L120M01A l120m01a = service1201.findL120m01aByOid(listSOid
							.get(i));
					// 開始替換資料...
					if (isArea) {
						L120M01H l120m01h = service1201
								.findL120m01hByUniqueKey(l120m01a.getMainId(),
										UtilConstants.Casedoc.MeetingType.營運中心);
						if (l120m01h != null) {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 案由
							newVal.add(Util.trim(l120m01h.getGist()).replace(
									"\n", "<br/>"));
							// 決議
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getDispWord()), true));
							// 核准額度較前准增、減金額
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getQuotaDesrc()), true));
						} else {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 案由
							newVal.add(Util.trim(l120m01a.getGist()).replace(
									"\n", "<br/>"));
							// 決議
							newVal.add("");
							// 核准額度較前准增、減金額
							newVal.add("");
						}
					} else if ("1".equals(hqMeetFlag) || "A".equals(hqMeetFlag)) {
						L120M01H l120m01h = service1201
								.findL120m01hByUniqueKey(l120m01a.getMainId(),
										hqMeetFlag);
						if (l120m01a != null) {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
						} else {
							// 承辦營業單位
							newVal.add("");
						}
						if (l120m01h != null) {
							// 案由
							newVal.add(Util.trim(l120m01h.getGist()).replace(
									"\n", "<br/>"));
							// 決議
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getDispWord()), true));
							// 核准額度較前准增、減金額
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getQuotaDesrc()), true));
						} else {
							// 案由
							newVal.add(Util.trim(l120m01a.getGist()).replace(
									"\n", "<br/>"));
							// 決議
							newVal.add("");
							// 核准額度較前准增、減金額
							newVal.add("");
						}

					} else if ("2".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
						L120M01H l120m01h = service1201
								.findL120m01hByUniqueKey(l120m01a.getMainId(),
										hqMeetFlag);
						if (l120m01a != null) {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
						} else {
							// 承辦營業單位
							newVal.add("");
						}
						if (l120m01h != null) {
							// 客戶別/案由
							StringBuilder sbCase = new StringBuilder();
							sbCase.append(Util.trim(l120m01a.getCustName())
									+ "/"
									+ Util.trim(l120m01h.getGist()).replace(
											"\n", "<br/>"));
							newVal.add(sbCase.toString());
							// 決議
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getDispWord()), true));
						} else {
							// 案由
							newVal.add(Util.trim(l120m01a.getGist()).replace(
									"\n", "<br/>"));
							// 決議
							newVal.add("");
						}
					} else if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
						// 案由(預設簽報書案由)
						StringBuilder sbCase = new StringBuilder();
						sbCase.append(Util.trim(l120m01a.getGist()).replace(
								"\n", "<br/>"));
						newVal.add(sbCase.toString());
						// 決議
						StringBuilder sbDec = new StringBuilder();
						if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
							// 如果是授管處的常董會->執行此條件
							sbDec.append("(")
									.append(XmlTool.replaceXMLReservedWord(
											branch.getBranchName(Util
													.trim(l120m01a
															.getCaseBrId())),
											true)).append("提)");
							newVal.add(sbDec.toString());
						} else {
							// UFO@20130618:調整判斷授管處OR債管處，並取消判斷企個金
							String caseBridTitle = NGFlagHelper.getDeptName(
									user, branch) + "提\\";
							// 如果是其他分行的常董會->執行此條件
							// if ("2".equals(Util.trim(l120m01a.getDocType())))
							// {
							// // 個金
							// caseBridTitle = "個人金融處提\\";
							// } else {
							// // 企金
							// caseBridTitle = "法人金融處提\\";
							// }
							sbDec.append(caseBridTitle)
									.append(branch.getBranchName(Util
											.trim(l120m01a.getCaseBrId())))
									.append("承做");
						}
					}
					replaceStrB(tempData, newVal, oldVal);
					// 將修改過的資料區塊存進去
					sbData.append(tempData.toString());
				}
			} else {
				for (int i = 0; i < oidArray.length; i++) {
					// 將暫存內容初始化
					tempData.setLength(0);
					newVal.clear();
					// 將保留區塊移到暫存
					tempData.append(oldData);
					L120M01A l120m01a = service1201
							.findL120m01aByOid(oidArray[i]);
					// 開始替換資料...
					if (isArea) {
						L120M01H l120m01h = service1201
								.findL120m01hByUniqueKey(l120m01a.getMainId(),
										UtilConstants.Casedoc.MeetingType.營運中心);
						if (l120m01h != null) {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 案由
							newVal.add(Util.trim(l120m01h.getGist()).replace(
									"\n", "<br/>"));
							// 決議
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getDispWord()), true));
							// 核准額度較前准增、減金額
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getQuotaDesrc()), true));
						} else {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 案由
							newVal.add(Util.trim(l120m01a.getGist()).replace(
									"\n", "<br/>"));
							// 決議
							newVal.add("");
							// 核准額度較前准增、減金額
							newVal.add("");
						}
					} else if ("1".equals(hqMeetFlag) || "A".equals(hqMeetFlag)) {
						L120M01H l120m01h = service1201
								.findL120m01hByUniqueKey(l120m01a.getMainId(),
										hqMeetFlag);
						if (l120m01h != null) {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 案由
							newVal.add(Util.trim(l120m01h.getGist()).replace(
									"\n", "<br/>"));
							// 決議
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getDispWord()), true));
							// 核准額度較前准增、減金額
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getQuotaDesrc()), true));
						} else {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 案由
							newVal.add(Util.trim(l120m01a.getGist()).replace(
									"\n", "<br/>"));
							// 決議
							newVal.add("");
							// 核准額度較前准增、減金額
							newVal.add("");
						}

					} else if ("2".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
						L120M01H l120m01h = service1201
								.findL120m01hByUniqueKey(l120m01a.getMainId(),
										hqMeetFlag);
						if (l120m01h != null) {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 客戶別/案由
							StringBuilder sbCase = new StringBuilder();
							sbCase.append(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01a.getCustName()), true)
									+ "/" + Util.trim(l120m01a.getGist()));
							newVal.add(sbCase.toString());
							// 決議
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getDispWord()), true));
						} else {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 案由
							newVal.add(Util.trim(l120m01a.getGist()).replace(
									"\n", "<br/>"));
							// 決議
							newVal.add("");
						}
					} else if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
						// 案由(預設簽報書案由)
						StringBuilder sbCase = new StringBuilder();
						sbCase.append(Util.trim(l120m01a.getGist()).replace(
								"\n", "<br/>"));
						newVal.add(sbCase.toString());
						// 決議
						StringBuilder sbDec = new StringBuilder();
						if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
							// 如果是授管處的常董會->執行此條件
							sbDec.append("(")
									.append(branch.getBranchName(Util
											.trim(l120m01a.getCaseBrId())))
									.append("提)");
							newVal.add(XmlTool.replaceXMLReservedWord(
									sbDec.toString(), true));
						} else {
							// 如果是其他分行的常董會->執行此條件
							// UFO@20130618:調整判斷授管處OR債管處，並取消判斷企個金
							String caseBridTitle = NGFlagHelper.getDeptName(
									user, branch) + "提\\";
							// if ("2".equals(Util.trim(l120m01a.getDocType())))
							// {
							// // 個金
							// caseBridTitle = "個人金融處提\\";
							// } else {
							// // 企金
							// caseBridTitle = "法人金融處提\\";
							// }
							sbDec.append(caseBridTitle)
									.append(branch.getBranchName(Util
											.trim(l120m01a.getCaseBrId())))
									.append("承做");
						}
					}
					replaceStrB(tempData, newVal, oldVal);
					// 將修改過的資料區塊存進去
					sbData.append(tempData.toString());
				}
			}
			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord.setLength(0);
			sbWord.append(sbHeader).append(sbData).append(sbFooter);

			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "BIG5");
			outWriter.write(sbWord.toString());
			outWriter.close();

		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (MissingResourceException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 取得常董稿(個案討論)
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord3a(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得使用者所選擇資料之oid
		String oid = params.getString("oid");
		// 取得文件狀態
		String txCode = params.getString("txCode");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;

		try {
			// 開始讀取第一範本檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);

			// 讀取第一範本資料
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 存取要被取代成第二範本資料內容的資料內容
			StringBuilder bgData = new StringBuilder();
			bgData.append(sbWord.toString());

			// 開始替換第一範本資料
			if (!oid.isEmpty()) {
				L120M01A model = service1201.findL120m01aByOid(oid);
				// 取得第二XML範本檔案名稱
				StringBuilder fileName2 = new StringBuilder();
				String fName2 = (UtilConstants.Casedoc.typCd.DBU.equals(Util
						.trim(model.getTypCd()))) ? "LMSDoc42.htm" : params
						.getString("fileName2");
				fileName2
						.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
						.append("word/").append(Util.trim(fName2));
				List<String> oldVal = new ArrayList<String>();
				List<String> newVal = new ArrayList<String>();
				oldVal.add("%LMSVAR001%");
				oldVal.add("%LMSVAR002%");
				oldVal.add("%LMSVAR003%");
				oldVal.add("%LMSVAR004%");
				oldVal.add("%LMSVAR005%");
				oldVal.add("%LMSVAR006%");
				StringBuilder sbDec = new StringBuilder();
				docName = "LMSDoc4";
				String hqMeetFlag = Util.trim(model.getHqMeetFlag());
				// 案由part1
				L120M01H l120m01h = service1201.findL120m01hByUniqueKey(
						model.getMainId(), hqMeetFlag);
				sbDec.setLength(0);
				if (Util.isEmpty(model.getGist()) || "".equals(model.getGist())) {
					if (l120m01h != null) {
						sbDec.append(Util.trim(l120m01h.getDispWord()));
					}
				} else {
					if (l120m01h != null) {
						sbDec.append(
								Util.trim(model.getGist()).replace("\n",
										"<br/>")).append("<br/>")
								.append("<br/>")
								.append(Util.trim(l120m01h.getDispWord()));
					} else {
						sbDec.append(
								Util.trim(model.getGist()).replace("\n",
										"<br/>")).append("<br/>")
								.append("<br/>");
					}
				}
				newVal.add(sbDec.toString());
				// 案由part2
				sbDec.setLength(0);
				if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
					// 如果是授管處的常董會->執行此條件
					// 一般分行與總處分行不一樣，總處分行是自行提常董會，
					// 所以為( 國金部提)，但是一般分行是( 授信管理處提＼忠孝分行承做 )
					if (LMSUtil.isSpecialBranch(Util.trim(model.getCaseBrId()))) {
						sbDec.append(
								branch.getBranchName(Util.trim(model
										.getCaseBrId()))).append("提");
					} else {
						// UFO@20130618:調整判斷授管處OR債管處
						String deptName = NGFlagHelper
								.getDeptName(user, branch) + "提\\";
						sbDec.append(deptName)
								.append(branch.getBranchName(Util.trim(model
										.getCaseBrId()))).append("承做");
					}
				} else {
					String title = "";
					// 若為OBU案件則需再加國金部字眼
					if ("4".equals(Util.trim(model.getTypCd()))) {
						// OBU
						title = "\\國金部承做";
					} else {
						title = "承做";
					}
					// UFO@20130618:調整判斷授管處OR債管處
					String deptName = NGFlagHelper.getDeptName(user, branch)
							+ "提\\";
					sbDec.append(deptName)
							.append(branch.getBranchName(Util.trim(model
									.getCaseBrId()))).append(title);
				}
				newVal.add(sbDec.toString());
				// 客戶資料(第二範本區塊內容)
				newVal.add(getWord2(oid, fileName2.toString(), "", "", true));
				// 敘做理由
				sbDec.setLength(0);
				// 若為授權外其他，則為空白
				if ("2".equals(Util.trim(model.getDocKind()))
						&& ("2".equals(Util.trim(model.getDocCode())) || "3"
								.equals(Util.trim(model.getDocCode())))) {
					sbDec.append("無");
				} else {
					L120M01D l120m01d = service1201.findL120m01dByUniqueKey(
							model.getMainId(), "4");
					if (l120m01d != null) {
						sbDec.append(Util.trim(l120m01d.getItemDscr()));
					} else {
						sbDec.append("無");
					}
				}
				newVal.add(Util.getDocRealImgPath(sbDec.toString()));
				// newVal.add(sbDec.toString());
				// 分行逾放比
				sbDec.setLength(0);

				List<L140M01A> listL140m01a = service1401
						.findL140m01aListByL120m01cMainId(model.getMainId(),
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				if (!listL140m01a.isEmpty()) {
					Date nplDate = null;
					HashMap<String, String> tempMap = new HashMap<String, String>();
					for (L140M01A l140m01a : listL140m01a) {
						String npl = Util.trim(l140m01a.getNpl());
						nplDate = l140m01a.getNpldate();
						if (Util.isNotEmpty(npl) && Util.isNotEmpty(nplDate)) {
							String[] nplArray = npl.split("、");
							for (String key : nplArray) {
								String tempKey = Util.trim(key);
								if (!tempMap.containsKey(tempKey)) {
									sbDec.append(sbDec.length() > 0 ? "，" : "");
									sbDec.append(tempKey);
									tempMap.put(tempKey, "");
								}
							}

							// sbDec.append("資料年月：")
							// .append(CapDate
							// .convertDateToTaiwanYear(TWNDate
							// .toAD(l140m01a.getNpldate())
							// .subSequence(0, 4)
							// .toString()))
							// .append("/")
							// .append(TWNDate.toAD(l140m01a.getNpldate())
							// .subSequence(5, 7).toString())
							// .append(" ")
							// .append(Util.trim(l140m01a.getNpl()))
							// .append("%").append(strEnter);

						}
					}
					sbDec.append(" ");
					sbDec.append("資料年月：");
					sbDec.append(CapDate.convertDateToTaiwanYear(TWNDate
							.toAD(nplDate).subSequence(0, 4).toString()));
					sbDec.append("/");
					sbDec.append(TWNDate.toAD(nplDate).subSequence(5, 7)
							.toString());
					sbDec.append(strEnter);
				}
				newVal.add(sbDec.toString());

				// 總處審查意見
				L120M01H l120m01h02 = service1201.findL120m01hByUniqueKey(
						model.getMainId(), "1");
				L120M01H l120m01h0a = service1201.findL120m01hByUniqueKey(
						model.getMainId(), "A");
				sbDec.setLength(0);
				// 國外部007 金控總部201 國金部025
				if (LMSUtil.isSpecialBranch(user.getUnitNo())) {
					if (UtilConstants.Casedoc.AreaChk.送會簽.equals(Util
							.trim(model.getAreaChk()))) {
						// 不提會, 抓法金處/個金處會簽意見
						// 授管處會簽意見
						L120M01D l120m01d = service1201
								.findL120m01dByUniqueKey(model.getMainId(), "C");
						if (l120m01d != null) {
							sbDec.append(Util.trim(l120m01d.getItemDscr()));
						}
					} else if (UtilConstants.Casedoc.AreaChk.送初審.equals(Util
							.trim(model.getAreaChk()))) {
						// (108)第 3230 號
						// 不提會, 抓法金處/個金處會簽意見
						// 授管處會簽意見
						L120M01D l120m01d = service1201
								.findL120m01dByUniqueKey(model.getMainId(), "C");
						if (l120m01d != null) {
							sbDec.append(Util.trim(l120m01d.getItemDscr()));
						}
					} else {
						String meetingNote = "";
						if (l120m01h0a != null) {
							// 提會, 抓授審會決議
							meetingNote = Util
									.trim(l120m01h0a.getMeetingNote());
						} else {
							meetingNote = "";
						}
						sbDec.append("本案經提")
								.append(Util.trim(model.getRptTitle1()))
								.append("討論，結論：").append("\t")
								.append(Util.getDocRealImgPath(meetingNote));
					}
				} else {
					// 若在法金處/個金處產生時，則國外部抓會簽, 一般分行抓法金處/個金處審查意見
					if ("".equals(model.getRptTitle1())
							|| Util.isEmpty(model.getRptTitle1())) {
						// 不提會, 抓授管處審查意見
						L120M01D l120m01d = service1201
								.findL120m01dByUniqueKey(model.getMainId(), "B");
						if (l120m01d != null) {
							sbDec.append(Util.trim(l120m01d.getItemDscr()));
						}
					} else {
						String meetingNote = "";
						if (CreditDocStatusEnum.會簽後修改編製中.getCode().equals(
								Util.trim(model.getDocStatus()))) {
							if (l120m01h0a != null) {
								// 提會, 抓授審會決議
								meetingNote = Util.trim(l120m01h0a
										.getMeetingNote());
							} else {
								// 提會, 抓授審會決議
								meetingNote = "";
							}
						} else {
							if (l120m01h02 != null) {
								// 提會, 抓授審會決議
								meetingNote = Util.trim(l120m01h02
										.getMeetingNote());
							} else {
								// 提會, 抓授審會決議
								meetingNote = "";
							}
						}
						sbDec.append("本案經提")
								.append(Util.trim(model.getRptTitle1()))
								.append("討論，結論：").append("\t")
								.append(Util.getDocRealImgPath(meetingNote));
					}
				}
				newVal.add(sbDec.toString());
				// 替換第一範本資料
				replaceStrB(sbWord, newVal, oldVal);
				// 完成處理

				OutputStreamWriter outWriter = new OutputStreamWriter(baos,
						"BIG5");
				outWriter.write(sbWord.toString());
				outWriter.close();

			} else {
				// 印出找不到資料錯誤
				// 查無資料
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
			}
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (MissingResourceException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 取得常董稿第二範本內容(個案討論)(已替換完成)
	 * 
	 * @param oid
	 *            使用者選擇文件oid
	 * @param fileName
	 *            第二範本路徑
	 * @param bgnData
	 *            第二範本資料開始字串
	 * @param endData
	 *            第二範本資料結束字串
	 * @return 常董稿第二範本內容
	 */
	@SuppressWarnings("unused")
	private String getWord2(String oid, String fileName, String bgnData,
			String endData, boolean addNewLine) throws CapException {
		// 完整第二範本資料內容
		StringBuilder sbDoc = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		try {
			// 開始讀取第二範本檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			// fileInputStream = new FileInputStream(fileName);
			// inputStreamReader = new InputStreamReader(fileInputStream);
			// reader = new BufferedReader(inputStreamReader);
			// 存取範本內容
			StringBuilder getDoc = new StringBuilder();
			String str = FileUtils.readFileToString(file, "BIG5");
			getDoc.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}
			// 找尋資料區塊開始字串
			String bodStr = "<div class=Section1";
			// 找尋資料區塊結束字串
			String bodEnd = "</body>";
			// 資料區塊開始位置
			int bgnIndex = getDoc.indexOf(bodStr, 0);
			// 資料區塊結束位置
			int endIndex = getDoc.indexOf(bodEnd, bgnIndex);
			StringBuilder dataDoc = new StringBuilder();
			dataDoc.append(getDoc.substring(bgnIndex, endIndex));
			// 存取暫存內容
			StringBuilder tmpDoc = new StringBuilder();
			// 存取被替換的資料
			List<String> listOld = new ArrayList<String>();
			listOld.add("%LMSREP001%");
			listOld.add("%LMSREP002%");
			listOld.add("%LMSREP003%");
			listOld.add("%LMSREP004%");
			listOld.add("%LMSREP005%");
			listOld.add("%LMSREP006%");
			listOld.add("%LMSREP007%");
			listOld.add("%LMSREP008%");
			listOld.add("%LMSREP009%");
			listOld.add("%LMSREP010%");
			listOld.add("%LMSREP011%");
			listOld.add("%LMSREP012%");
			listOld.add("%LMSREP013%");
			listOld.add("%LMSREP014%");
			listOld.add("%LMSREP015%");
			listOld.add("%LMSREP016%");
			listOld.add("%LMSREP017%");
			listOld.add("%LMSREP018%");
			listOld.add("%LMSREP019%");
			listOld.add("%LMSREP020%");
			listOld.add("%LMSREP021%");
			listOld.add("%LMSREP022%");
			listOld.add("%LMSREP023%");
			listOld.add("%LMSREP024%");
			listOld.add("%LMSREP025%");
			listOld.add("%LMSREP026%");
			listOld.add("%LMSREP027%");
			listOld.add("%LMSREP028%");
			listOld.add("%LMSREP028A%");
			listOld.add("%LMSREP028B%");
			listOld.add("%LMSREP028C%");
			listOld.add("%LMSREP028D%");
			listOld.add("%LMSREP028E%");
			listOld.add("%LMSREP029%");
			listOld.add("%LMSREP030%");
			listOld.add("%LMSREP031%");
			listOld.add("%LMSREP032%");
			listOld.add("%LMSREP033%");
			listOld.add("%LMSREP034%");
			// 存取替換後的資料
			List<String> listNew = new ArrayList<String>();
			L120M01A model = service1201.findL120m01aByOid(oid);
			if (model != null) {
				String mainId = Util.trim(model.getMainId());
				List<L120S01A> listL120s01a = service1201
						.findL120s01aByMainId(mainId);
				List<L140M01A> listL140m01a = service1401
						.findL140m01aListByL120m01cMainId(mainId,
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				if (listL120s01a.isEmpty()) {
					// 錯誤訊息
				} else {
					listL120s01a = getSorted120(listL120s01a, model);
					// 開始替換資料...
					for (L120S01A l120s01a : listL120s01a) {
						// 初始化暫存變數
						tmpDoc.setLength(0);
						// 初始化替換後資料
						listNew.clear();
						// 取得第二範本資料區塊並存到暫存變數中
						tmpDoc.append(dataDoc.toString());
						L120S01B l120s01b = service1201
								.findL120s01bByUniqueKey(mainId,
										l120s01a.getCustId(),
										l120s01a.getDupNo());
						if (l120s01b == null) {
							l120s01b = new L120S01B();
						}
						L120S01D l120s01d = service1201
								.findL120s01dByUniqueKey(mainId,
										l120s01a.getCustId(),
										l120s01a.getDupNo());
						if (l120s01d == null) {
							l120s01d = new L120S01D();
						}
						// 客戶名稱
						listNew.add(Util.trim(l120s01a.getCustName()));
						// 信用評等
						List<L120S01C> listL120s01c = service1201
								.findL120s01cByCustId(mainId,
										l120s01a.getCustId(),
										l120s01a.getDupNo());
						if (listL120s01c.isEmpty()) {
							listNew.add("無");
						} else {
							StringBuilder sbScore = new StringBuilder();
							sbScore.setLength(0);
							// 取得信用評等
							sbScore.append(getL120S01CData(listL120s01c));
							if (Util.isEmpty(sbScore.toString())) {
								listNew.add("無");
							} else {
								listNew.add(sbScore.toString());
							}
						}
						// 利害關係人授信
						listNew.add("1".equals(Util.trim(l120s01d.getMbRlt())) ? "有"
								: ("2".equals(Util.trim(l120s01d.getMbRlt())) ? "無"
										: "不適用"));
						listNew.add("1".equals(Util.trim(l120s01d.getMbRlt())) ? Util
								.trim(l120s01d.getMbRltDscr()) : "");
						// 負責人
						listNew.add("1".equals(Util.trim(l120s01b.getPosType())) ? "董事長"
								: "2".equals(Util.trim(l120s01b.getPosType())) ? "董事"
										: "3".equals(Util.trim(l120s01b
												.getPosType())) ? "負責人" : "9"
												.equals(Util.trim(l120s01b
														.getPosType())) ? "其他"
												: "無");
						listNew.add("".equals(Util.trim(l120s01b.getChairman())) ? "無"
								: Util.trim(l120s01b.getChairman()));
						// 總經理
						listNew.add("".equals(Util.trim(l120s01b.getGManager())) ? "無"
								: Util.trim(l120s01b.getGManager()));
						// 資本額登記幣別
						listNew.add("".equals(Util.trim(l120s01b.getRgtCurr())) ? "TWD"
								: Util.trim(l120s01b.getRgtCurr()));
						// 實收幣別
						listNew.add("".equals(Util.trim(l120s01b.getCptlCurr())) ? "TWD"
								: Util.trim(l120s01b.getCptlCurr()));
						// 資本額金額
						listNew.add("".equals(Util.trim(Util
								.nullToSpace(l120s01b.getRgtAmt()))) ? "0"
								: NumConverter.addComma(Util.trim(Util
										.nullToSpace(l120s01b.getRgtAmt()))));
						// 資本額單位
						listNew.add("1".equals(Util.trim(Util
								.nullToSpace(l120s01b.getRgtUnit()))) ? "元"
								: "1000".equals(Util.trim(Util
										.nullToSpace(l120s01b.getRgtUnit()))) ? "仟元"
										: "10000".equals(Util.trim(Util
												.nullToSpace(l120s01b
														.getRgtUnit()))) ? "萬元"
												: "1000000".equals(Util.trim(Util
														.nullToSpace(l120s01b
																.getRgtUnit()))) ? "百萬元"
														: "元");
						// 實作金額
						listNew.add("".equals(Util.trim(Util
								.nullToSpace(l120s01b.getCptlAmt()))) ? "0"
								: NumConverter.addComma(Util.trim(Util
										.nullToSpace(l120s01b.getCptlAmt()))));
						// 實作單位
						listNew.add("1".equals(Util.trim(Util
								.nullToSpace(l120s01b.getCptlUnit()))) ? "元"
								: "1000".equals(Util.trim(Util
										.nullToSpace(l120s01b.getCptlUnit()))) ? "千元"
										: "10000".equals(Util.trim(Util
												.nullToSpace(l120s01b
														.getCptlUnit()))) ? "萬元"
												: "1000000".equals(Util.trim(Util
														.nullToSpace(l120s01b
																.getCptlUnit()))) ? "百萬元"
														: "元");
						// 成立日期
						listNew.add("".equals(Util.trim(CapDate.formatDate(
								l120s01b.getEstDate(), DATEYYYYMMDD))) ? "無"
								: Util.trim(CapDate.formatDate(
										l120s01b.getEstDate(), DATEYYYYMMDD)));
						// 註冊地
						if (Util.isEmpty(Util.trim(l120s01b.getNtCode()))) {
							listNew.add("無");
						} else {
							listNew.add(codeService.findByCodeTypeAndCodeValue(
									"CountryCode",
									Util.trim(l120s01b.getNtCode()))
									.getCodeDesc());
						}
						// 主要營業項目
						if (l120s01a.getCustId().equals(l120s01b.getCustId())
								&& l120s01a.getDupNo().equals(
										l120s01b.getDupNo())) {
							if (Util.isEmpty(l120s01b.getBussItem())) {
								listNew.add("");
							} else {
								listNew.add(Util.trim(l120s01b.getBussItem()));
							}
						} else {
							listNew.add("");
						}
						// 股票
						if (Util.isEmpty(l120s01b.getStockStatus())) {
							listNew.add("無");
						} else {
							int stockStatus = Util.parseInt(l120s01b
									.getStockStatus());
							switch (stockStatus) {
							case 1:
								listNew.add("上市");
								break;
							case 2:
								listNew.add("上櫃");
								break;
							case 3:
								listNew.add("公開發行");
								break;
							case 4:
								listNew.add("非公開發行");
								break;
							case 5:
								listNew.add("興櫃");
								break;
							default:
								listNew.add("無");
							}
						}
						// 隸屬企業集團
						if (Util.isEmpty(l120s01b.getGroupNo())) {
							listNew.add("");
							listNew.add("無");
						} else {
							listNew.add("");
							listNew.add(Util.trim(l120s01b.getGroupName()));
						}

						// 大陸投資概況
						listNew.add("1".equals(Util.trim(l120s01b.getInvMFlag())) ? "有"
								: "2".equals(Util.trim(l120s01b.getInvMFlag())) ? "無"
										: "不適用");
						// 赴大陸投資金額
						listNew.add("1".equals(Util.trim(l120s01b.getInvMFlag())) ? Util
								.trim(l120s01b.getInvMCurr())
								+ " "
								+ NumConverter.addComma(Util.trim(Util
										.nullToSpace(l120s01b.getInvMAmt())))
								: "0");
						// 經濟部投審會核准金額
						listNew.add("1".equals(Util.trim(l120s01b.getInvMFlag())) ? Util
								.trim(l120s01b.getAprCurr())
								+ " "
								+ NumConverter.addComma(Util.trim(Util
										.nullToSpace(l120s01b.getAprAmt())))
								: "0");
						// 營運概況簡評 & 財務概況簡評
						// 若為授權外一般才處理
						if ("2".equals(model.getDocKind())) {
							L120S01G l120s01g1 = service1201
									.findL120s01gByUniqueKey(mainId,
											l120s01a.getCustId(),
											l120s01a.getDupNo(), "1");
							L120S01G l120s01g2 = service1201
									.findL120s01gByUniqueKey(mainId,
											l120s01a.getCustId(),
											l120s01a.getDupNo(), "2");
							if (l120s01g1 == null) {
								l120s01g1 = new L120S01G();
							}
							if (l120s01g2 == null) {
								l120s01g2 = new L120S01G();
							}
							listNew.add(Util.trim(l120s01g1.getDataDscr()));
							listNew.add(Util.trim(l120s01g2.getDataDscr()));
						} else {
							listNew.add("無");
							listNew.add("無");
						}
						boolean existData = false;
						if (!"[]".equals(listL140m01a.toString())) {
							for (L140M01A l140m01a : listL140m01a) {
								if (l120s01a.getCustId().equals(
										l140m01a.getCustId())
										&& l120s01a.getDupNo().equals(
												l140m01a.getDupNo())
										&& !existData) {
									// 前准額度幣別(額度明細表)
									listNew.add(Util.isEmpty(Util.trim(l140m01a
											.getLVTotCurr())) ? "TWD" : Util
											.trim(l140m01a.getLVTotCurr()));
									// 前准餘額幣別
									listNew.add((Util.isEmpty(Util
											.trim(l140m01a.getBLCurr()))) ? "TWD"
											: Util.trim(l140m01a.getBLCurr()));
									// 前准額度金額(額度明細表)
									listNew.add(Util.isEmpty(l140m01a
											.getLVTotAmt()) ? "0"
											: NumConverter.addComma(Util.trim(Util
													.nullToSpace(l140m01a
															.getLVTotAmt()))));
									// 前准餘額金額(額度明細表)
									listNew.add(Util.isEmpty(l140m01a
											.getBLAmt()) ? "0" : NumConverter
											.addComma(Util.trim(Util
													.nullToSpace(l140m01a
															.getBLAmt()))));
									// 增/減額
									BigDecimal netTamt1 = (Util
											.isEmpty(Util.trim(l140m01a
													.getIncApplyTotAmt()))) ? BigDecimal.ZERO
											: l140m01a.getIncApplyTotAmt();
									BigDecimal netTamt2 = (Util
											.isEmpty(Util.trim(l140m01a
													.getDecApplyTotAmt()))) ? BigDecimal.ZERO
											: l140m01a.getDecApplyTotAmt();
									StringBuilder plusOrMin = new StringBuilder();
									plusOrMin.setLength(0);
									if (netTamt1.doubleValue() >= 0) {
										plusOrMin
												.append((plusOrMin.length() > 0) ? "<br/>"
														: "")
												.append("增額")
												.append(l140m01a
														.getIncApplyTotCurr())
												.append(" ")
												.append(NumConverter.addComma(Util.trim(Util
														.nullToSpace(netTamt1))));
									}
									if (netTamt2.doubleValue() >= 0) {
										plusOrMin
												.append((plusOrMin.length() > 0) ? "<br/>"
														: "")
												.append("減額")
												.append(l140m01a
														.getDecApplyTotCurr())
												.append(" ")
												.append(NumConverter.addComma(Util.trim(Util
														.nullToSpace(netTamt2))));
									}
									listNew.add(plusOrMin.toString());

									// 授信總額度申請合計幣別(額度明細表)
									listNew.add(Util.trim(l140m01a
											.getLoanTotCurr()));
									// 授信總額度申請合計金額(額度明細表)
									listNew.add(NumConverter.addComma(Util
											.trim(Util.nullToSpace(l140m01a
													.getLoanTotAmt()))));
									// 授信總額度其中擔保合計幣別(額度明細表)
									listNew.add(Util.trim(l140m01a
											.getAssureTotCurr()));
									// 授信總額度其中擔保合計金額(額度明細表)
									listNew.add(NumConverter.addComma(Util
											.trim(Util.nullToSpace(l140m01a
													.getAssureTotAmt()))));
									// 前貸期間平均動用率
									if (Util.isEmpty(l140m01a.getUsePar())) {
										listNew.add("0%");
									} else {
										listNew.add(Util.trim(Util
												.nullToSpace(l140m01a
														.getUsePar()))
												+ "%");
									}
									existData = true;
								}
							}
							if (!existData) {
								// 前准額度幣別(額度明細表)
								listNew.add("TWD");
								// 前准餘額幣別
								listNew.add("TWD");
								// 前准額度金額(額度明細表)
								listNew.add("0");
								// 前准餘額金額(額度明細表)
								listNew.add("0");
								// 增/減額
								listNew.add("");
								// 授信總額度申請合計幣別(額度明細表)
								listNew.add("TWD");
								// 授信總額度申請合計金額(額度明細表)
								listNew.add("0");
								// 授信總額度其中擔保合計幣別(額度明細表)
								listNew.add("TWD");
								// 授信總額度其中擔保合計金額(額度明細表)
								listNew.add("0");
								// 前貸期間平均動用率
								listNew.add("0%");
							}
						} else {
							// 前准額度幣別(額度明細表)
							listNew.add("TWD");
							// 前准餘額幣別
							listNew.add("TWD");
							// 前准額度金額(額度明細表)
							listNew.add("0");
							// 前准餘額金額(額度明細表)
							listNew.add("0");
							// 增/減額
							listNew.add("");
							// 授信總額度申請合計幣別(額度明細表)
							listNew.add("TWD");
							// 授信總額度申請合計金額(額度明細表)
							listNew.add("0");
							// 授信總額度其中擔保合計幣別(額度明細表)
							listNew.add("TWD");
							// 授信總額度其中擔保合計金額(額度明細表)
							listNew.add("0");
							// 前貸期間平均動用率
							listNew.add("0%");
						}

						// // 前貸期間平均動用率
						// L120S01F l120s01f = service1201
						// .findL120s01fByUniqueKey(mainId,
						// l120s01a.getCustId(),
						// l120s01a.getDupNo());
						// if (l120s01f == null) {
						// listNew.add("無");
						// } else {
						// listNew.add(Util.trim(Util.nullToSpace(l120s01f
						// .getAvgURate())) + "%");
						// }
						// 中長期償債能力分析(主要借款人才有)
						if (l120s01a.getCustId().equals(model.getCustId())
								&& l120s01a.getDupNo().equals(model.getDupNo())) {
							C140SFFF c140sfff = service1201.getC140SFFF(mainId,
									mainId, "bfp_note1_1", "A1");
							if (c140sfff == null) {
								listNew.add("無");
							} else {
								listNew.add(Util.getDocRealImgPath(c140sfff
										.getFfbody()));
							}
						} else {
							listNew.add("無");
						}

						// Miller added at 2012/08/31
						// 利害關係人應收帳款承購無追索權-買方
						if ("2".equals(Util.trim(model.getDocType()))) {
							// 個金預設塞空白
							listNew.add("");
							listNew.add("");
						} else {
							// 企金
							if ("1".equals(Util.trim(l120s01d.getFctMbRlt()))
									|| "1".equals(Util.trim(l120s01d
											.getFctMhRlt()))) {
								// 有
								listNew.add("有");
								listNew.add(Util.trim(l120s01d
										.getFctMhRltDscr()));
							} else if ("2".equals(Util.trim(l120s01d
									.getFctMbRlt()))
									|| "2".equals(Util.trim(l120s01d
											.getFctMhRlt()))) {
								// 無
								listNew.add("無");
								listNew.add("");
							} else {
								// 不適用
								listNew.add("不適用");
								listNew.add("");
							}
						}

						// 利害關係人授信
						listNew.add("1".equals(Util.trim(l120s01d.getMbRlt33())) ? "有"
								: ("2".equals(Util.trim(l120s01d.getMbRlt33())) ? "無"
										: "不適用"));
						listNew.add("1".equals(Util.trim(l120s01d.getMbRlt33())) ? Util
								.trim(l120s01d.getMbRltDscr33()) : "");

						// 將範本資料內容替換成暫存資料內容
						replaceStrB(tmpDoc, listNew, listOld);
						// 將替換好的資料存到完整資料變數中
						sbDoc.append(tmpDoc.toString());
						// 換行
						if (addNewLine) {
							sbDoc.append("<br/>");
						}

					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage());
		}
		return sbDoc.toString();
	}

	/**
	 * 取得常董稿(彙總討論)
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord3b(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		// 取得第二XML範本檔案名稱
		StringBuilder fileName2 = new StringBuilder();
		String fName2 = params.getString("fileName2");
		fileName2.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName2));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得使用者所選擇資料之oid
		String oid = params.getString("oid");
		// 取得文件狀態
		String txCode = params.getString("txCode");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;

		try {
			// 開始讀取第一範本檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);

			// 讀取第一範本資料
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 開始替換第一範本資料
			if (!oid.isEmpty()) {
				List<String> oldVal = new ArrayList<String>();
				List<String> newVal = new ArrayList<String>();
				// 替換表格的關係要連同標籤一起取代
				oldVal.add("%LMSVAR001%");
				docName = "LMSDoc51";

				// 客戶資料(第二範本區塊內容)
				newVal.add(getWord2b(oid, fileName2.toString(), "", "", txCode));

				// 替換第一範本資料
				replaceStrB(sbWord, newVal, oldVal);
				// 完成處理
				OutputStreamWriter outWriter = new OutputStreamWriter(baos,
						"BIG5");
				outWriter.write(sbWord.toString());
				outWriter.close();
			} else {
				// 印出找不到資料錯誤
				// 查無資料
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
			}
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (MissingResourceException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 取得常董稿第二範本內容(彙總討論)(已替換完成)
	 * 
	 * @param oid
	 *            使用者選擇文件oid
	 * @param fileName
	 *            第二範本路徑
	 * @param bgnData
	 *            第二範本資料開始字串
	 * @param endData
	 *            第二範本資料結束字串
	 * @param txCode
	 *            文件狀態
	 * @return 常董稿第二範本內容
	 */
	@SuppressWarnings("unused")
	private String getWord2b(String oid, String fileName, String bgnData,
			String endData, String txCode) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 完整第二範本資料內容
		StringBuilder sbDoc = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		try {
			// 開始讀取第二範本檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			// fileInputStream = new FileInputStream(fileName);
			// inputStreamReader = new InputStreamReader(fileInputStream);
			// reader = new BufferedReader(inputStreamReader);
			// 存取範本內容
			StringBuilder getDoc = new StringBuilder();
			String str = FileUtils.readFileToString(file, "BIG5");
			getDoc.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 找尋資料區塊開始字串
			String bodStr = "<div class=Section1";
			// 找尋資料區塊結束字串
			String bodEnd = "</body>";
			// 資料區塊開始位置
			int bgnIndex = getDoc.indexOf(bodStr, 0);
			// 資料區塊結束位置
			int endIndex = getDoc.indexOf(bodEnd, bgnIndex);
			StringBuilder dataDoc = new StringBuilder();
			dataDoc.append(getDoc.substring(bgnIndex, endIndex));
			// 存取暫存內容
			StringBuilder tmpDoc = new StringBuilder();
			// 存取被替換的資料
			List<String> listOld = new ArrayList<String>();
			listOld.add("%LMSREP001%");
			listOld.add("%LMSREP002%");
			listOld.add("%LMSREP003%");
			listOld.add("%LMSREP004%");
			listOld.add("%LMSREP005%");
			listOld.add("%LMSREP006%");
			listOld.add("%LMSREP007%");
			listOld.add("%LMSREP008%");
			listOld.add("%LMSREP009%");
			listOld.add("%LMSREP010%");
			listOld.add("%LMSREP011%");
			listOld.add("%LMSREP012%");
			listOld.add("%LMSREP013%");
			listOld.add("%LMSREP014%");
			listOld.add("%LMSREP015%");
			listOld.add("%LMSREP016%");
			listOld.add("%LMSREP017%");
			listOld.add("%LMSREP018%");
			listOld.add("%LMSREP019%");
			// 存取替換後的資料
			List<String> listNew = new ArrayList<String>();
			L120M01A model = service1201.findL120m01aByOid(oid);
			if (model != null) {
				String hqMeetFlag = Util.trim(model.getHqMeetFlag());
				String mainId = Util.trim(model.getMainId());
				List<L140M01A> listL140m01a = service1401
						.findL140m01aListByL120m01cMainId(mainId,
								UtilConstants.Cntrdoc.ItemType.額度批覆表);
				if (listL140m01a == null || listL140m01a.isEmpty()) {
					listL140m01a = service1401
							.findL140m01aListByL120m01cMainId(mainId,
									UtilConstants.Cntrdoc.ItemType.額度明細表);
				}
				int index = 0;
				if (listL140m01a.isEmpty()) {
					// 錯誤訊息
				} else {
					// 換行
					sbDoc.append(tblEnter);
					// 開始替換資料...
					listL140m01a = getSorted140(listL140m01a, model);
					for (L140M01A l140m01a : listL140m01a) {
						String l140MainId = Util.trim(l140m01a.getMainId());
						// 初始化暫存變數
						tmpDoc.setLength(0);
						// 初始化替換後資料
						listNew.clear();
						// 取得第二範本資料區塊並存到暫存變數中
						tmpDoc.append(dataDoc.toString());
						L120S01D l120s01d = service1201
								.findL120s01dByUniqueKey(mainId,
										l140m01a.getCustId(),
										l140m01a.getDupNo());
						if (l120s01d == null) {
							l120s01d = new L120S01D();
						}
						StringBuilder sb = new StringBuilder();
						sb.setLength(0);
						L140M01B l140m01b1 = service1401.findL140m01bUniqueKey(
								l140MainId, "3");
						L140M01B l140m01b2 = service1401.findL140m01bUniqueKey(
								l140MainId, "4");
						L140M01B l140m01b3 = service1401.findL140m01bUniqueKey(
								l140MainId, "5");
						L140M01B l140m01b4 = service1401.findL140m01bUniqueKey(
								l140MainId, "2");
						if (l140m01b1 == null) {
							l140m01b1 = new L140M01B();
						}
						if (l140m01b2 == null) {
							l140m01b2 = new L140M01B();
						}
						if (l140m01b3 == null) {
							l140m01b3 = new L140M01B();
						}
						if (l140m01b4 == null) {
							l140m01b4 = new L140M01B();
						}
						// 營業單位
						listNew.add(branch.getBranchName(Util.trim(l140m01a
								.getOwnBrId())));
						// 客戶名稱
						listNew.add(Util.trim(l140m01a.getCustName()));
						// 銀行法所稱利害關係人
						// 如果為企金則讀取有無利害關係人
						if ("1".equals(model.getDocType())) {
							listNew.add(XmlTool.replaceXMLReservedWord(
									Util.trim("1".equals(l120s01d.getMbRlt()) ? "有"
											: "2".equals(l120s01d.getMbRlt()) ? "無"
													: "不適用"), true));
						} else {
							listNew.add("無");
						}
						// 信用評等
						List<L120S01C> listL120s01c = service1201
								.findL120s01cByCustId(mainId,
										l140m01a.getCustId(),
										l140m01a.getDupNo());
						if (listL120s01c.isEmpty()) {
							listNew.add("無");
						} else {
							StringBuilder sbScore = new StringBuilder();
							sbScore.setLength(0);
							// 取得信用評等
							sbScore.append(getL120S01CData(listL120s01c));
							if (Util.isEmpty(sbScore.toString())) {
								listNew.add("無");
							} else {
								listNew.add(sbScore.toString());
							}
						}
						// 授信科目
						listNew.add(Util.trim(l140m01a.getLnSubject()));
						// 授信額度
						listNew.add(Util.trim(l140m01a.getCurrentApplyCurr()));
						listNew.add(NumConverter.addComma(Util.trim(Util
								.nullToSpace(l140m01a.getCurrentApplyAmt()))));
						listNew.add("元");
						// 擔保品
						listNew.add(Util.trim(l140m01b1.getItemDscr()));
						// 其他敘做條件
						sb.append("本票：")
								.append(Util.trim(l140m01a.getCheckNote()))
								.append("<br/><br/>").append("其他敘作條件：")
								.append(Util.trim(l140m01b2.getItemDscr()))
								.append("<br/><br/>").append("利費率：")
								.append("<br/>")
								.append(Util.trim(l140m01b4.getItemDscr()));
						listNew.add(sb.toString());
						// 動用期限
						listNew.add(getUseDeadline(
								Util.trim(l140m01a.getUseDeadline()),
								Util.trim(l140m01a.getDesp1())));
						// 清償期限
						listNew.add(Util.trim(l140m01a.getPayDeadline()));
						// 保證人
						listNew.add(Util.trim(l140m01a.getGuarantor()));
						// 申請續約、敘作條件異動情形
						listNew.add(Util.trim(l140m01b3.getItemDscr()));
						// 授信總額度
						listNew.add(Util.trim(l140m01a.getLoanTotCurr()));
						listNew.add(NumConverter.addComma(Util.trim(Util
								.nullToSpace(l140m01a.getLoanTotAmt()))));
						listNew.add("元");
						// XX審核意見
						listNew.add("總處審核意見");
						// if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag))
						// {
						// listNew.add("營業單位敘做理由");
						// } else {
						// listNew.add("總處審核意見");
						// }
						// 審核意見內容
						L120M01H l120m01h02 = service1201
								.findL120m01hByUniqueKey(mainId, "1");
						L120M01H l120m01h0a = service1201
								.findL120m01hByUniqueKey(mainId, "A");
						if (index > 0) {
							listNew.add("同前頁");
							index++;
						} else {
							if (LMSUtil.isSpecialBranch(user.getUnitNo())) {
								if ("".equals(model.getRptTitle1())
										|| Util.isEmpty(model.getRptTitle1())) {
									// 不提會, 抓綜合評估敘作理由
									L120M01D l120m01d = service1201
											.findL120m01dByUniqueKey(
													model.getMainId(), "4");
									if (l120m01d != null) {
										listNew.add(Util.trim(l120m01d
												.getItemDscr()));
									} else {
										// 若找不到決議內容則設為無
										listNew.add("無");
									}
								} else {
									if (l120m01h0a != null) {
										// 提會, 抓授審會決議
										listNew.add(Util.trim(l120m01h0a
												.getMeetingNote()));
									} else {
										// 若找不到決議內容則設為無
										listNew.add("無");
									}
								}
							} else {
								// 若在法金處/個金處產生時，則國外部抓會簽, 一般分行抓法金處/個金處審查意見
								if ("".equals(model.getRptTitle1())
										|| Util.isEmpty(model.getRptTitle1())) {
									// 不提會, 抓授管處審查意見
									L120M01D l120m01d = service1201
											.findL120m01dByUniqueKey(
													model.getMainId(), "B");
									if (l120m01d != null) {
										listNew.add(Util.trim(l120m01d
												.getItemDscr()));
									} else {
										// 若找不到決議內容則設為無
										listNew.add("無");
									}
								} else {
									if (CreditDocStatusEnum.會簽後修改編製中.getCode()
											.equals(Util.trim(model
													.getDocStatus()))) {
										if (l120m01h0a != null) {
											// 提會, 抓授審會決議
											listNew.add(Util.trim(l120m01h0a
													.getMeetingNote()));
										} else {
											// 若找不到決議內容則設為無
											listNew.add("無");
										}
									} else {
										if (l120m01h02 != null) {
											// 提會, 抓授審會決議
											listNew.add(Util.trim(l120m01h02
													.getMeetingNote()));
										} else {
											// 若找不到決議內容則設為無
											listNew.add("無");
										}
									}
								}
							}
							index++;
						}

						// 將範本資料內容替換成暫存資料內容
						replaceStrB(tmpDoc, listNew, listOld);
						// 將替換好的資料存到完整資料變數中
						sbDoc.append(tmpDoc.toString());
						// 換行
						sbDoc.append(tblEnter);
					}
				}
			}
		} catch (Exception e) {

		}
		return sbDoc.toString();
	}

	/**
	 * 將額度明細表主借款人排序第一筆，剩下依(custId+dupNo)ASC排序
	 * 
	 * @param allList
	 *            未排序額度明細表資料
	 * @param meta
	 *            簽報書主檔
	 * @return 排序後的額度明細表
	 */
	private List<L140M01A> getSorted140(List<L140M01A> allList, L120M01A meta) {
		// 主借款人額度明細資料
		List<L140M01A> mainList = new ArrayList<L140M01A>();
		// 共同借款人額度明細資料
		List<L140M01A> dataList = new ArrayList<L140M01A>();
		// 排序好的額度明細資料
		List<L140M01A> sortedList = new ArrayList<L140M01A>();
		if (meta != null) {
			// 先將主借款人資料與共同借款人分類
			for (L140M01A model : allList) {
				if (Util.trim(model.getCustId()).equals(
						Util.trim(meta.getCustId()))
						&& Util.trim(model.getDupNo()).equals(
								Util.trim(meta.getDupNo()))) {
					mainList.add(model);
				} else {
					dataList.add(model);
				}
			}
			// 開始進行排序
			Collections.sort(dataList, new Comparator<L140M01A>() {
				public int compare(L140M01A model1, L140M01A model2) {
					StringBuilder sb1 = new StringBuilder();
					StringBuilder sb2 = new StringBuilder();
					sb1.setLength(0);
					sb2.setLength(0);
					sb1.append(Util.trim(model1.getCustId())).append(
							Util.trim(model1.getDupNo()));
					sb2.append(Util.trim(model2.getCustId())).append(
							Util.trim(model2.getDupNo()));
					if (sb1.toString().equals(sb2.toString())) {
						return 0;
					} else {
						return ((Comparable<String>) sb1.toString())
								.compareTo(sb2.toString());
					}
				}
			});
			sortedList.addAll(mainList);
			sortedList.addAll(dataList);
		}
		return sortedList;
	}

	/**
	 * 將借款人主檔主借款人排序第一筆，剩下依(custId+dupNo)ASC排序
	 * 
	 * @param allList
	 *            未排序借款人主檔資料
	 * @param meta
	 *            簽報書主檔
	 * @return 排序後的借款人主檔資料
	 */
	private List<L120S01A> getSorted120(List<L120S01A> allList, L120M01A meta) {
		// 主借款人額度明細資料
		List<L120S01A> mainList = new ArrayList<L120S01A>();
		// 共同借款人額度明細資料
		List<L120S01A> dataList = new ArrayList<L120S01A>();
		// 排序好的額度明細資料
		List<L120S01A> sortedList = new ArrayList<L120S01A>();
		if (meta != null) {
			// 先將主借款人資料與共同借款人分類
			for (L120S01A model : allList) {
				if (Util.trim(model.getCustId()).equals(
						Util.trim(meta.getCustId()))
						&& Util.trim(model.getDupNo()).equals(
								Util.trim(meta.getDupNo()))) {
					mainList.add(model);
				} else {
					dataList.add(model);
				}
			}
			// 開始進行排序
			Collections.sort(dataList, new Comparator<L120S01A>() {
				public int compare(L120S01A model1, L120S01A model2) {
					StringBuilder sb1 = new StringBuilder();
					StringBuilder sb2 = new StringBuilder();
					sb1.setLength(0);
					sb2.setLength(0);
					sb1.append(Util.trim(model1.getCustId())).append(
							Util.trim(model1.getDupNo()));
					sb2.append(Util.trim(model2.getCustId())).append(
							Util.trim(model2.getDupNo()));
					if (sb1.toString().equals(sb2.toString())) {
						return 0;
					} else {
						return ((Comparable<String>) sb1.toString())
								.compareTo(sb2.toString());
					}
				}
			});
			sortedList.addAll(mainList);
			sortedList.addAll(dataList);
		}
		return sortedList;
	}

	/**
	 * 取得常董稿(常董稿常董會授權總經理逕行核定案件)
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord3c(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName1 = "";
		String docName2 = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		StringBuilder fileName2 = new StringBuilder();
		String fName2 = params.getString("fileName2");
		fileName2.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName2));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String strSign = Util.nullToSpace(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(strSign);
		// 取得文件狀態
		// String txCode = params.getString("txCode");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		StringBuilder sbWord2 = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;

		FileInputStream fileInputStream2 = null;
		InputStreamReader inputStreamReader2 = null;
		BufferedReader reader2 = null;
		Map<String, String> proPertyMap = null;
		proPertyMap = codeService
				.findByCodeType("lms1405s02_proPerty", "zh_TW");
		if (proPertyMap == null)
			proPertyMap = new LinkedHashMap<String, String>();

		try {
			// 開始讀取範本檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			URL urlRpt2 = null;
			urlRpt2 = Thread.currentThread().getContextClassLoader()
					.getResource(fileName2.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);

			File file2 = new File(urlRpt2.toURI());
			fileInputStream2 = new FileInputStream(file2);
			inputStreamReader2 = new InputStreamReader(fileInputStream2);
			reader2 = new BufferedReader(inputStreamReader2);

			// 讀取第一範本資料
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}
			docName1 = "LMSDoc61";
			// 讀取第二範本資料
			String str2 = FileUtils.readFileToString(file2, "BIG5");
			sbWord2.append(str2);
			if (inputStreamReader2 != null) {
				inputStreamReader2.close();
			}
			docName2 = "LMSDoc62";

			// 找尋資料區塊開始字串
			String bodStr = "</thead>";
			// 找尋資料區塊結束字串
			String bodEnd = "</table>";
			// 資料區塊開始位置
			int strBod = sbWord2.indexOf(bodStr, 0) + bodStr.length();
			// 資料區塊結束位置
			int endBod = sbWord2.indexOf(bodEnd, strBod);

			// 表頭區塊
			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(sbWord2.substring(0, strBod));
			// 資料區塊
			StringBuilder sbData = new StringBuilder();
			sbData.append(sbWord2.substring(strBod, endBod));
			// 表尾區塊
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(sbWord2.substring(endBod));

			// 開始替換第二範本資料
			List<String> oldVal = new ArrayList<String>();
			oldVal.add("%LMSREP001%");
			oldVal.add("%LMSREP002%");
			oldVal.add("%LMSREP003%");
			oldVal.add("%LMSREP004%");
			oldVal.add("%LMSREP005%");
			oldVal.add("%LMSREP006%");
			oldVal.add("%LMSREP007%");
			oldVal.add("%LMSREP008%");
			List<String> newVal = new ArrayList<String>();
			StringBuilder getData = new StringBuilder();
			getData.append(sbData.toString());
			sbData.setLength(0);
			StringBuilder tempData = new StringBuilder();
			for (int i = 0; i < oidArray.length; i++) {
				L120M01A model = service1201.findL120m01aByOid(oidArray[i]);
				List<L140M01A> listL140M01A = service1401
						.findL140m01aListByL120m01cMainId(model.getMainId(),
								UtilConstants.Cntrdoc.ItemType.額度批覆表);
				if (listL140M01A == null || listL140M01A.isEmpty()) {
					// 額度批覆表為空改抓額度明細表
					listL140M01A = service1401
							.findL140m01aListByL120m01cMainId(
									model.getMainId(),
									UtilConstants.Cntrdoc.ItemType.額度明細表);
				}
				if (listL140M01A == null || listL140M01A.isEmpty()) {
					newVal.clear();
					tempData.setLength(0);
					tempData.append(getData.toString());
					// 承辦營業單位
					newVal.add("");
					// 戶名
					newVal.add("");
					// 授信科目
					newVal.add("");
					// 額度(單位：元)
					newVal.add("");
					// 動用期限
					newVal.add("");
					// 擔保品
					newVal.add("");
					// 連保人
					newVal.add("");
					// 續約、敘做條件異動情形
					newVal.add("");

					// 將範本資料內容替換成暫存資料內容
					replaceStrB(tempData, newVal, oldVal);
					// 將替換好的資料存到完整資料變數中
					sbData.append(tempData.toString());
				} else {
					listL140M01A = getSorted140(listL140M01A, model);
					for (L140M01A l140m01a : listL140M01A) {
						newVal.clear();
						tempData.setLength(0);
						tempData.append(getData.toString());
						StringBuilder sb = new StringBuilder();
						sb.setLength(0);
						L140M01B l140m01b1 = service1401.findL140m01bUniqueKey(
								l140m01a.getMainId(), "3");
						L140M01B l140m01b2 = service1401.findL140m01bUniqueKey(
								l140m01a.getMainId(), "5");
						L140M01B l140m01b3 = service1401.findL140m01bUniqueKey(
								l140m01a.getMainId(), "2"); // 利費率
						if (l140m01b1 == null) {
							l140m01b1 = new L140M01B();
						}
						if (l140m01b2 == null) {
							l140m01b2 = new L140M01B();
						}
						if (l140m01b3 == null) {
							l140m01b3 = new L140M01B();
						}
						// 承辦營業單位
						newVal.add(branch.getBranchName(Util.trim(l140m01a
								.getOwnBrId())));
						// 戶名
						newVal.add(Util.trim(l140m01a.getCustName()));
						// 授信科目
						newVal.add(Util.trim(l140m01a.getLnSubject()));
						// 額度(單位：元)
						newVal.add(Util.trim(Util.nullToSpace(l140m01a
								.getCurrentApplyCurr()))
								+ NumConverter.addComma(Util.trim(Util
										.nullToSpace(l140m01a
												.getCurrentApplyAmt()))));
						// 動用期限
						// newVal.add(getUseDeadline(
						// Util.trim(l140m01a.getUseDeadline()),
						// Util.trim(l140m01a.getDesp1())));

						// 利費率
						newVal.add(Util.trim(l140m01b3.getItemDscr()));

						// 擔保品
						newVal.add(Util.trim(l140m01b1.getItemDscr()));
						// 連保人
						newVal.add(Util.trim(l140m01a.getGuarantor()));
						// 續約、敘做條件異動情形
						sb.append(
								this.getProPerty(
										Util.trim(l140m01a.getProPerty()),
										proPertyMap)).append("<br/>")
								.append(Util.trim(l140m01b2.getItemDscr()));
						newVal.add(sb.toString());

						// 將範本資料內容替換成暫存資料內容
						replaceStrB(tempData, newVal, oldVal);
						// 將替換好的資料存到完整資料變數中
						sbData.append(tempData.toString());
					}
				}
			}
			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord2.setLength(0);
			sbWord2.append(sbHeader).append(sbData).append(sbFooter);

			String docTempFile = params.getString("docTempFile");
			if ("LMSDoc61.doc".equals(docTempFile)) {
				OutputStreamWriter outWriter = new OutputStreamWriter(baos,
						"BIG5");
				outWriter.write(sbWord.toString());
				outWriter.close();
			} else {
				OutputStreamWriter outWriter = new OutputStreamWriter(baos,
						"BIG5");
				outWriter.write(sbWord2.toString());
				outWriter.close();
			}
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (MissingResourceException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 取得property對應
	 * 
	 * @param proPerty
	 *            property
	 * @return property對應
	 */
	private String getProPerty(String proPerty, Map<String, String> proPertyMap) {
		StringBuffer str = new StringBuffer();
		String[] temp = proPerty.split("\\|");
		for (String perty : temp) {
			str.append(Util.nullToSpace(proPertyMap.get(perty))).append("、");
		}
		if (str.length() == 0) {
			str.append("、");
		}

		return str.toString().substring(0, str.length() - 1);
	}

	/**
	 * Common 取得議程文件區塊
	 * 
	 * @param rcd
	 *            整份XML文件
	 * @param strFlag
	 *            欲取得的區塊內容(Header: H, Data: D, Footer: F)
	 * @param bgnStr
	 *            表頭開始字串
	 * @param endStr
	 *            表尾開始字串
	 * @param strChk
	 *            第一變數開始字串
	 * @return 區塊內容
	 */
	public StringBuilder getDocXML(StringBuilder rcd, String strFlag,
			String bgnStr, String endStr, String strChk) {
		// 負責存取資料區塊內容
		StringBuilder sbChk = new StringBuilder();
		// 負責存取結果區塊內容
		StringBuilder sbRtn = new StringBuilder();
		// 初始化表頭位置點
		int bgnPos = 0;
		// 初始化表尾位置點
		int endPos = 0;
		// 初始化檢查第一個變數字串位置點
		int chkPos = 0;
		// 設定檢查點(是否找不到第一個變數字串位置？找不到為真，找到為假)為真
		boolean bChk = true;

		// 增加防錯措施
		chkPos = rcd.indexOf(strChk, 0);
		if (chkPos < 0) {
			bChk = false;
		}

		// 初始化結果區塊內容
		sbRtn.setLength(0);

		// 當檢查點為真(即找不到第一個變數字串位置時)執行此迴圈
		while (bChk) {
			// 初始化資料區塊內容
			sbChk.setLength(0);
			// 依照表頭開始字串找到表頭開始位置
			bgnPos = rcd.indexOf(bgnStr, bgnPos);
			// 依照表尾開始字串找到表尾開始位置
			endPos = rcd.indexOf(endStr, bgnPos) + endStr.length();
			// 表頭位置和表尾位置之間的內容(資料區塊內容)存取下來
			sbChk.append(rcd.substring(bgnPos, endPos));
			// 依照第一變數字串找到其存在位置點
			chkPos = sbChk.indexOf(strChk, 0);
			// 若第一變數字串實際存在則會大於零，否則會小於零
			if (chkPos > 0) {
				// 第一變數字串實際存在設定檢查點為假
				bChk = false;
			}
			// 第一變數字串實際存在時執行此迴圈
			if (!bChk) {
				// 取得表頭區塊資料內容
				if ("H".equals(strFlag)) {
					sbRtn.append(rcd.substring(0, bgnPos));
				}
				// 取得表尾區塊資料內容
				if ("F".equals(strFlag)) {
					sbRtn.append(rcd.substring(endPos));
				}
				// 取得資料區塊內容
				if ("D".equals(strFlag)) {
					sbRtn.append(sbChk.toString());
				}
			}
			// 將表頭位置設成表尾位置(目的在於讓檢查點找不到第一變數位置，因為已經完成任務)
			if (bgnPos > 0) {
				bgnPos = endPos;
			} else {
				bChk = false;
			}
		}
		// 將結果區塊資料內容回傳
		return sbRtn;
	}

	/**
	 * 取代StringBuilder字串內容(不需輸入取代位置且迴圈替換)
	 * 
	 * @param strB
	 *            StringBuilder內容
	 * @param oldVal
	 *            原字串
	 * @param newVal
	 *            取代後的字串
	 */
	private void replaceStrB(StringBuilder strB, List<String> newVal,
			List<String> oldVal) {
		String str = strB.toString();
		for (int i = 0; i < oldVal.size(); i++) {
			str = str.replace(oldVal.get(i), newVal.get(i));
		}
		strB.replace(0, strB.length(), str);
	}

	/**
	 * 
	 * 基本功能：替換標記以正常顯示
	 * <p>
	 * 
	 * @param input
	 *            String
	 * @return String
	 */
	public String replaceTag(String input) {
		if (!hasSpecialChars(input)) {
			return input;
		}
		StringBuffer filtered = new StringBuffer(input.length());
		char c;
		for (int i = 0; i <= input.length() - 1; i++) {
			c = input.charAt(i);
			switch (c) {
			case '<':
				filtered.append("&lt;");
				break;
			case '>':
				filtered.append("&gt;");
				break;
			case '"':
				filtered.append("&quot;");
				break;
			case '&':
				filtered.append("&amp;");
				break;
			default:
				filtered.append(c);
			}

		}
		return (filtered.toString());
	}

	/**
	 * 
	 * 基本功能：判斷標記是否存在
	 * <p>
	 * 
	 * @param input
	 *            String
	 * @return boolean
	 */
	public boolean hasSpecialChars(String input) {
		boolean flag = false;
		if ((input != null) && (input.length() > 0)) {
			char c;
			for (int i = 0; i <= input.length() - 1; i++) {
				c = input.charAt(i);
				switch (c) {
				case '>':
					flag = true;
					break;
				case '<':
					flag = true;
					break;
				case '"':
					flag = true;
					break;
				case '&':
					flag = true;
					break;
				}
			}
		}
		return flag;
	}

	/**
	 * 
	 * 基本功能：過濾所有以"<"開頭以">"結尾的標籤
	 * <p>
	 * 
	 * @param str
	 *            String
	 * @return String
	 */
	public static String filterHtml(String str) {
		Pattern pattern = Pattern.compile(regxpForHtml);
		Matcher matcher = pattern.matcher(str);
		StringBuffer sb = new StringBuffer();
		boolean result1 = matcher.find();
		while (result1) {
			matcher.appendReplacement(sb, "");
			result1 = matcher.find();
		}
		matcher.appendTail(sb);
		return sb.toString();
	}

	/**
	 * 
	 * 基本功能：過濾指定標籤
	 * <p>
	 * 
	 * @param str
	 *            String
	 * @param tag
	 *            指定標籤
	 * @return String
	 */
	public static String fiterHtmlTag(String str, String tag) {
		String regxp = "<\\s*" + tag + "\\s+([^>]*)\\s*>";
		Pattern pattern = Pattern.compile(regxp);
		Matcher matcher = pattern.matcher(str);
		StringBuffer sb = new StringBuffer();
		boolean result1 = matcher.find();
		while (result1) {
			matcher.appendReplacement(sb, "");
			result1 = matcher.find();
		}
		matcher.appendTail(sb);
		return sb.toString();
	}

	/**
	 * 
	 * 基本功能：替換指定的標籤
	 * <p>
	 * 
	 * @param str
	 * @param beforeTag
	 *            要替換的標籤
	 * @param tagAttrib
	 *            要替換的標籤屬性值
	 * @param startTag
	 *            新標籤開始標記
	 * @param endTag
	 *            新標籤結束標記
	 * @return String
	 * @如：替換img標籤的src屬性值為[img]屬性值[/img]
	 */
	public static String replaceHtmlTag(String str, String beforeTag,
			String tagAttrib, String startTag, String endTag) {
		String regxpForTag = "<\\s*" + beforeTag + "\\s+([^>]*)\\s*>";
		String regxpForTagAttrib = tagAttrib + "=\"([^\"]+)\"";
		Pattern patternForTag = Pattern.compile(regxpForTag);
		Pattern patternForAttrib = Pattern.compile(regxpForTagAttrib);
		Matcher matcherForTag = patternForTag.matcher(str);
		StringBuffer sb = new StringBuffer();
		boolean result = matcherForTag.find();
		while (result) {
			StringBuffer sbreplace = new StringBuffer();
			Matcher matcherForAttrib = patternForAttrib.matcher(matcherForTag
					.group(1));
			if (matcherForAttrib.find()) {
				matcherForAttrib.appendReplacement(sbreplace, startTag
						+ matcherForAttrib.group(1) + endTag);
			}
			matcherForTag.appendReplacement(sb, sbreplace.toString());
			result = matcherForTag.find();
		}
		matcherForTag.appendTail(sb);
		return sb.toString();
	}

	/**
	 * 取得useDeadline對應
	 * 
	 * @param useDeadline
	 *            useDeadline
	 * @param desp1
	 *            desp1
	 * @return 取得useDeadline對應
	 */
	private String getUseDeadline(String useDeadline, String desp1) {

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		return LMSUtil.getUseDeadline(useDeadline, desp1, prop);
	}

	/**
	 * 取得信評資料
	 * 
	 * @param l120s01a
	 *            L120S01A的資料
	 * @param l120s01cList
	 *            LIST<L120S01C>的資料
	 * @return 信評資料
	 */
	@SuppressWarnings("unused")
	private String getL120S01CData(List<L120S01C> l120s01cList) {
		String resultData = "";
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1205R01RptServiceImpl.class);
		Map<String, String> crdTypeMap = codeService.findByCodeType("CRDType",
				LMSUtil.getLocale().toString());
		StringBuffer str1 = new StringBuffer();
		StringBuffer str2 = new StringBuffer();
		StringBuffer str3 = new StringBuffer();
		StringBuffer str4 = new StringBuffer();
		// 免辦
		boolean noResult = false;
		boolean naResult = false;
		StringBuffer tempGrade = new StringBuffer();
		for (L120S01C l120s01c : l120s01cList) {
			// if (Util.nullToSpace(l120s01a.getCustId()).equals(
			// Util.nullToSpace(l120s01c.getCustId()))
			// && Util.nullToSpace(l120s01a.getDupNo()).equals(
			// Util.nullToSpace(l120s01c.getDupNo()))) {
			String crdType = Util.trim(l120s01c.getCrdType());
			String grade = Util.trim(l120s01c.getGrade());
			tempGrade.setLength(0);
			if ("NA".equals(crdType)) {
				naResult = true;
				// str.append(prop.getProperty("L120S01C.CRDTITLE01"))
				// .append(prop.getProperty("L120S05A.GRPGRRDN"))
				// .append("、");
			} else if ("DB".equals(crdType) || "DL".equals(crdType)
					|| "OU".equals(crdType)) {
				if (str3.length() != 0) {
					str3.append("、");
				}
				str3.append(prop.getProperty("L120S01C.CRDTITLE01"))
						.append(grade)
						.append("【")
						.append(prop.getProperty("L120S01C.CRDTITLE02"))
						.append(Util.nullToSpace(TWNDate.toAD(l120s01c
								.getCrdTYear())))
						.append(" ")
						.append(prop.getProperty("L120S01C.CRDTITLE03"))
						.append(Util.nullToSpace(branch.getBranchName(Util
								.nullToSpace(l120s01c.getCrdTBR()))))
						.append("】");
			} else if ("NO".equals(crdType)) {
				noResult = true;
				// str.append(prop.getProperty("L120S01C.CRDTITLE04"))
				// .append(prop.getProperty("L120S01C.NOCRD01"))
				// .append("、");
			} else if ("M".equals(Util.getLeftStr(crdType, 1))) {

				if (Util.isNumeric(grade)) {
					tempGrade.append(grade)
							.append(prop.getProperty("tempGrade")).append(" ");
				}

				// 取得MOW等級之說明
				tempGrade.append(lmsService.getMowGradeName(prop, crdType,
						grade));

				if (str2.length() != 0) {
					str2.append("、");
				}
				str2.append(Util.nullToSpace(crdTypeMap.get(crdType)))
						.append(" : ")
						.append(tempGrade.toString())
						.append("【")
						.append(prop.getProperty("L120S01C.CRDTITLE02"))
						.append(Util.nullToSpace(TWNDate.toAD(l120s01c
								.getCrdTYear())))
						.append(" ")
						.append(prop.getProperty("L120S01C.CRDTITLE03"))
						.append(Util.nullToSpace(branch.getBranchName(Util
								.nullToSpace(l120s01c.getCrdTBR()))))
						.append("】");
			} else if (UtilConstants.Casedoc.CrdType.MOODY.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.SAndP.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.Fitch.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.FitchTW.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.KBRA.equals(crdType)) {
				// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
				if (str1.length() != 0) {
					str1.append("、");
				}
				str1.append(grade)
						.append("【")
						.append(prop.getProperty("L120S01C.CRDTITLE02"))
						.append(Util.nullToSpace(TWNDate.toAD(l120s01c
								.getCrdTYear())))
						.append(" ")
						.append(prop.getProperty("L120S01C.CRDTITLE03"))
						.append(Util.nullToSpace(crdTypeMap.get(l120s01c
								.getCrdType()))).append("】");
			} else if (crdType.startsWith("C") && Util.notEquals(crdType, "CS")) {
				if (str4.length() != 0) {
					str4.append("、");
				}
				str4.append(Util.nullToSpace(crdTypeMap.get(crdType)))
						.append(" : ")
						.append(grade)
						.append("【")
						.append(prop.getProperty("L120S01C.CRDTITLE02"))
						.append(Util.nullToSpace(TWNDate.toAD(l120s01c
								.getCrdTYear())))
						.append(" ")
						.append(prop.getProperty("L120S01C.CRDTITLE03"))
						.append(" ")
						.append(l120s01c.getCrdTBR())
						.append(" ")
						.append(Util.nullToSpace(branch.getBranchName(Util
								.nullToSpace(l120s01c.getCrdTBR()))))
						.append("】");
			}
			// }
		}

		/*
		 * 狀況1:MX+NA 狀況2:DX+NO 狀況3:NA+NO 狀況4:空 最後在加外部NM,NS,NP
		 */
		// 外部平等一定要串
		boolean result = false;
		StringBuffer total = new StringBuffer();
		// L120S01C.CRDTITLE04=模型評等 :
		if (str2.length() > 0) {

			// MXXX+外部
			// rptVariableMap.put("L120S01C.CRD",str2.toString());
			total.append(prop.getProperty("L120S01C.CRDTITLE04") + " " + str2);
			result = true;
		}
		// L120S01C.CRDTITLE01=信用評等 :
		if (str3.length() > 0) {
			// DXXX+外部
			total.append(total.length() > 0 ? "<br/>" : "");
			total.append(str3.toString());
			// rptVariableMap.put("L120S01C.CRD",str3.toString() + " " +
			// prop.getProperty("L120S01C.CRDTITLE04"));
			result = true;
		}

		// L120S01C.CRDTITLE05=外部評等 :
		if (str1.length() > 0) {
			total.append(total.length() > 0 ? "<br/>" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE05")
					+ str1.toString());
		}
		if (total.length() == 0) {
			// rptVariableMap.put("L120S01C.CRD",prop.getProperty("L120S01C.NOCRD01"));
			total.append(prop.getProperty("L120S01C.NOCRD01"));
			result = true;
		}
		// rptVariableMap.put("L120S01C.CRD",(!result ? "" :
		// (rptVariableMap.get("L120S01C.CRD") + "\n"))+crdtitle05 +
		// str1.toString());
		resultData = total.toString();
		return resultData;
	}

	/**
	 * 列印核貸通知書(個金)
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord5ByCls(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = "CLSDoc15.htm";
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得oid
		String oid = params.getString("oid");
		// 取得文件狀態
		String txCode = params.getString("txCode");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		// 借保人種類
		Map<String, String> custPosMap = codeService
				.findByCodeType("L140S01A_custPos");
		try {
			// FileUtils.readFileToString(file, "UTF-8");
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			// String str = FileUtils.readFileToString(file, "UTF-8");
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 找尋資料區塊開始字串
			String bodStr = "<div class=Section1";
			// 找尋資料區塊結束字串
			String bodEnd = "</body>";
			// 資料區塊開始位置
			int strBod = sbWord.indexOf(bodStr, 0);
			// 資料區塊結束位置
			int endBod = sbWord.indexOf(bodEnd, strBod);

			// 表頭區塊
			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(sbWord.substring(0, strBod));
			// 資料區塊
			StringBuilder sbData = new StringBuilder();
			sbData.append(sbWord.substring(strBod, endBod));
			// 表尾區塊
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(sbWord.substring(endBod));
			// 稽核資料是否都順利取得
			if (sbHeader == null || sbData == null || sbFooter == null) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1205V01Page.class);
				throw new CapMessageException(RespMsgHelper.getMessage(
						"EFD0025", pop.getProperty("l120v01.error1")),getClass());
			}

			L120M01A model = service1201.findL120m01aByOid(oid);
			boolean isParentCase = LMSUtil.isParentCase(model);
			// 取得額度批覆表
			List<L140M01A> list = service1401.findL140m01aListByL120m01cMainId(
					model.getMainId(), "2");
			// 讀不到批覆表再讀額度明細表
			if (list.isEmpty()) {
				list = service1401.findL140m01aListByL120m01cMainId(
						model.getMainId(), "1");
			}

			List<String> oldVal = new ArrayList<String>();
			oldVal.add("%LMSVAR001%");
			oldVal.add("%LMSVAR002%");
			oldVal.add("%LMSVAR003%");
			oldVal.add("%LMSVAR004%");
			oldVal.add("%LMSVAR005%");
			oldVal.add("%LMSVAR006%");
			oldVal.add("%LMSVAR007%");
			oldVal.add("%LMSVAR008%");
			oldVal.add("%LMSVAR009%");
			oldVal.add("%LMSVAR010%");
			oldVal.add("%LMSVAR011%");
			oldVal.add("%LMSVAR012%");
			List<String> newVal = new ArrayList<String>();

			StringBuilder oldData = new StringBuilder();
			oldData.append(sbData);
			// 將資料區塊清空以便開始Append
			sbData.setLength(0);
			StringBuilder tempData = new StringBuilder();

			int count = 0;
			docName = "LMSDoc15";
			// 開始替換資料
			for (L140M01A l140m01a : list) {
				// 將暫存內容初始化
				tempData.setLength(0);
				// 將保留區塊移到暫存
				tempData.append(oldData);

				// 清除要替換的資料內容
				newVal.clear();

				// 編製單位
				// newVal.add(XmlTool.replaceXMLReservedWord(branch.getBranchName
				// (Util.trim(l140m01a.getOwnBrId())),true));
				newVal.add(branch.getBranchName(Util.trim(l140m01a.getOwnBrId())));
				// 日期
				// newVal.add(XmlTool.replaceXMLReservedWord(CapDate.getCurrentDate("yyyy/MM/dd"),true));
				newVal.add(CapDate.getCurrentDate("yyyy/MM/dd"));

				// 授信總額度(單位：仟元)
				StringBuilder loanTot = new StringBuilder();
				if (Util.isEmpty(Util.trim(l140m01a.getLoanTotCurr()))) {
					loanTot.append("TWD").append("0");
				} else {
					// loanTot.append(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getLoanTotCurr()),true))
					// .append(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getLoanTotAmt()
					// .divide(new BigDecimal("1000")).toString()),true));
					loanTot.append(Util.trim(l140m01a.getLoanTotCurr()))
							.append(NumConverter.addComma(Util.trim(l140m01a
									.getLoanTotAmt()
									.divide(new BigDecimal("1000"),
											BigDecimal.ROUND_HALF_UP)
									.toString()))).append("仟元整");
				}
				newVal.add(loanTot.toString());

				// 授信額度種類(授信科目)
				// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getLnSubject()),true));
				// newVal.add(Util.trim(l140m01a.getLnSubject()));
				L140M01B l140m01b1 = service1401.findL140m01bUniqueKey(
						Util.trim(l140m01a.getMainId()), "1");
				if (l140m01b1 != null) {
					l140m01b1.getItemDscr();
					newVal.add(Util.trim(l140m01a.getLnSubject())
							+ "<br/>"
							+ Util.trim(l140m01b1.getItemDscr()).replace("\r",
									"<br/>"));
				} else {
					// 若無資料則設為空
					newVal.add(Util.trim(l140m01a.getLnSubject()));
				}

				// 動用方式
				if ("2".equals(Util.trim(l140m01a.getReUse()))) {
					newVal.add("循環動用");
				} else {
					newVal.add("不循環動用");
				}

				// 動用期限
				StringBuilder sbUseDead = new StringBuilder();
				if (Util.isEmpty(Util.trim(l140m01a.getUseDeadline()))) {
					newVal.add("");
				} else {
					newVal.add(getUseDeadline(
							Util.trim(l140m01a.getUseDeadline()),
							Util.trim(l140m01a.getDesp1())));
				}
				List<L140S02A> l140s02as = l140s02aDao.findByMainId(l140m01a
						.getMainId());
				StringBuffer rateStr = new StringBuffer();
				StringBuffer payDeadLineStr = new StringBuffer();
				for (L140S02A l140s02a : l140s02as) {
					rateStr.append(rateStr.length() > 0 ? "<br/>" : "");
					rateStr.append(l140s02a.getRateDesc());
					payDeadLineStr.append(rateStr.length() > 0 ? "<br/>" : "");
					String lnOther = Util.trim(l140s02a.getLnOther());
					if (isParentCase) {
						payDeadLineStr.append(lnOther);
					} else {
						payDeadLineStr.append(l140s02a.getLnYear());
						payDeadLineStr.append("年");
						payDeadLineStr.append(l140s02a.getLnMonth());
						payDeadLineStr.append("月");
						if (Util.isNotEmpty(lnOther)) {
							payDeadLineStr.append("(");
							payDeadLineStr.append(lnOther);
							payDeadLineStr.append(")");
						}
					}

				}

				newVal.add(rateStr.toString());

				// 償還期限(清償期限)
				// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getPayDeadline()),true));
				newVal.add(payDeadLineStr.toString());

				// 擔保品
				L140M01B danbow = service1401.findL140m01bUniqueKey(
						Util.trim(l140m01a.getMainId()),
						UtilConstants.Cntrdoc.l140m01bItemType.擔保品);
				if (danbow != null) {
					danbow.getItemDscr();
					// 因為是CKeditor所以要過濾掉不要的標籤
					// newVal.add("<![CDATA["
					// +
					// XmlTool.replaceXMLReservedWord(filterHtml(Util.trim(danbow
					// .getItemDscr())) + "]]>",true));
					newVal.add(Util.getDocRealImgPath(Util.trim(danbow
							.getItemDscr())));
					// newVal.add(Util.trim(danbow.getItemDscr()));
				} else {
					// 若無資料則設為空
					newVal.add("");
				}

				// 連帶保證人
				// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getGuarantor()),true));
				List<L140S01A> l140s01as = l140s01aDao.findByMainId(l140m01a
						.getMainId());
				StringBuffer guarantorStr = new StringBuffer();
				for (L140S01A l140s01a : l140s01as) {
					guarantorStr.append(guarantorStr.length() > 0 ? "、" : "");
					guarantorStr.append(l140s01a.getCustName());
					guarantorStr.append("(");
					guarantorStr.append(Util.trim(custPosMap.get(l140s01a
							.getCustPos())));
					guarantorStr.append(")");
				}
				Set<L140M01B> l140m01bs = l140m01a.getL140m01b();
				String itemDscrD = "";
				if (l140m01bs != null) {
					for (L140M01B l140m01b : l140m01bs) {
						if (UtilConstants.Cntrdoc.l140m01bItemType.國內個金團貸借保人說明
								.equals(l140m01b.getItemType())) {
							itemDscrD = Util.trim(l140m01b.getItemDscr());
						}
					}
				}
				String guarantorResult = "";
				if (Util.isEmpty(itemDscrD) && guarantorStr.length() == 0) {
					guarantorResult = "無保證人";
				} else {
					guarantorResult = guarantorStr.append("<br/>")
							.append(itemDscrD).toString();
				}

				newVal.add(guarantorResult);
				// 其他條件(其他敘做條件)
				L140M01B otherIf = service1401.findL140m01bUniqueKey(
						Util.trim(l140m01a.getMainId()),
						UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件);
				if (otherIf != null) {
					otherIf.getItemDscr();
					// 因為是CKeditor所以要過濾掉不要的標籤
					// newVal.add("<![CDATA["
					// +
					// XmlTool.replaceXMLReservedWord(filterHtml(Util.trim(otherIf
					// .getItemDscr())) + "]]>",true));
					newVal.add(Util.getDocRealImgPath(Util.trim(otherIf
							.getItemDscr())));
					// newVal.add(Util.trim(otherIf.getItemDscr()));
				} else {
					// 若無資料則設為空
					newVal.add("");
				}

				// 主要借款人
				// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getCustName()),true));
				newVal.add(Util.trim(l140m01a.getCustName()));

				if (count < list.size() - 1) {
					// 如不是最後一筆資料則插入換頁符號
					// tempData.append(tblPage);
					// tempData.append('<p
					// style="page-break-before:always"></p>');
					tempData.append(wordPage);
				}
				// 替換表頭
				replaceStrB(tempData, newVal, oldVal);
				// 完成處理表頭資料
				// 將修改過得資料存進去
				sbData.append(tempData);
				count++;
			}

			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord.setLength(0);
			sbWord.append(sbHeader).append(sbData).append(sbFooter);

			// OutputStreamWriter outWriter = new OutputStreamWriter(baos,
			// "UTF-8");
			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "BIG5");
			outWriter.write(sbWord.toString());
			outWriter.close();
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 列印貸放前照會借款人作業檢核表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getCLSDoc1(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		String templateName = "CLSDoc1.xml";

		try {
			String oid = params.getString("oid");
			String defaultBox = "□";
			String chooseBox = "■";
			// 讀檔
			String template_content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir")) + "word/" + templateName);
			String final_content = "";
			String part_before = "";
			String part_repeat = "";
			String part_after = "";
			int idx_beg = template_content.indexOf("<wx:sect>")
					+ "<wx:sect>".length();
			int idx_end = template_content.lastIndexOf("<w:sectPr");
			part_before = template_content.substring(0, idx_beg);
			part_repeat = template_content.substring(idx_beg, idx_end);
			part_after = template_content.substring(idx_end);
			// ===================
			String sysDate = TWNDate.toAD(CapDate.getCurrentTimestamp());
			String Field_yearRoc = String.valueOf(Util.parseInt(StringUtils
					.substring(sysDate, 0, 4)) - 1911);
			String Field_MM = StringUtils.substring(sysDate, 5, 7);
			String Field_dd = StringUtils.substring(sysDate, 8, 10);

			String[] codeType = { UtilConstants.CodeTypeItem.企業關係,
					UtilConstants.CodeTypeItem.親屬關係,
					UtilConstants.CodeTypeItem.綜合關係_企業,
					UtilConstants.CodeTypeItem.綜合關係_親屬 };
			Map<String, CapAjaxFormResult> l140s01a_rKindD_codeMap = codeService
					.findByCodeType(codeType);
			// ===================
			L120M01A l120m01a = service1201.findL120m01aByOid(oid);
			// 取得額度批覆表
			List<L140M01A> list = service1401.findL140m01aListByL120m01cMainId(
					l120m01a.getMainId(), "2");
			// 讀不到批覆表再讀額度明細表
			if (list.isEmpty()) {
				list = service1401.findL140m01aListByL120m01cMainId(
						l120m01a.getMainId(), "1");
			}
			String Field_brNo = l120m01a.getCaseBrId();
			String Field_brName = branch.getBranchName(Field_brNo);
			String Field_staff = userSrv.getUserName(l120m01a.getUpdater());
			List<Map<String, String>> cntrNo_paramMap_list = new ArrayList<Map<String, String>>();

			LinkedHashMap<String, String> rateMap = misMislnratService
					.findBaseRateByCurrs(new String[] { "TWD" }).get("TWD");
			rateMap.put("01", "自訂利率"); // L140S02D.rateUser=自訂利率
			Map<String, CapAjaxFormResult> codeMap = codeService
					.findByCodeType(new String[] { "L140S02C_intWay",
							"L140S02C_rIntWay", "L140S02C_decFlag",
							"L140S02D_pmFlag", "lms1405s0204_rateKind",
							"L140S02D_rateChgWay", "L140S02D_rateChgWay2" });
			LinkedHashMap<String, String> rateUserTypeMap = lnlnf070Service
					.getPrRate("TWD");
			HashMap<String, String> rateBy070 = null;
			String show_fee_at_cntrNo = "";
			List<L140M01R> l140m01r_list = clsService
					.findL140M01R_exclude_feeSrc3(l120m01a.getMainId());
			for (L140M01A l140m01a : list) {
				if (Util.equals(l140m01a.getProPerty(),
						UtilConstants.Cntrdoc.Property.不變)
						|| Util.equals(l140m01a.getProPerty(),
								UtilConstants.Cntrdoc.Property.取消)) {
					continue;
				}
				LinkedHashSet<String> subject_name_set = new LinkedHashSet<String>();
				LinkedHashSet<String> subject2_guarantee = new LinkedHashSet<String>();
				int maxLnYear = 0;
				int maxLnMonth = 0;
				boolean cms_01 = false;
				boolean cms_03_07 = false;
				boolean cms_others = false;
				LinkedHashSet<String> cms_others_desc = new LinkedHashSet<String>();
				int l140s02g_chgOther_cnt = 0;
				LinkedHashSet<String> l140s02h_bankName_desc = new LinkedHashSet<String>();
				BigDecimal nowExtendYear = BigDecimal.ZERO;
				LinkedHashMap<String, LinkedHashSet<String>> rate_format_desc = new LinkedHashMap<String, LinkedHashSet<String>>();
				LinkedHashMap<String, LinkedHashSet<String>> rate_free_desc = new LinkedHashMap<String, LinkedHashSet<String>>();
				boolean payway_1 = false;
				boolean payway_2 = false;
				boolean payway_3 = false;
				boolean payway_4 = false;
				boolean payway_7 = false;
				boolean payway_6 = false;
				LinkedHashSet<String> payway_6_desc = new LinkedHashSet<String>();
				LinkedHashMap<String, String> custPos_N_name_relate = new LinkedHashMap<String, String>();
				LinkedHashMap<String, String> custPos_G_name_relate = new LinkedHashMap<String, String>();

				List<L140S02A> l140s02a_list = clsService
						.findL140S02A(l140m01a);
				for (L140S02A l140s02a : l140s02a_list) {
					C900M01D c900m01d = clsService
							.findC900M01D_subjCode(l140s02a.getSubjCode());
					L140S02C l140s02c = clsService.findL140S02C(
							l140m01a.getMainId(), l140s02a.getSeq());
					L140S02E l140s02e = clsService.findL140S02E(
							l140m01a.getMainId(), l140s02a.getSeq());
					L140S02G l140s02g = clsService.findL140S02G(
							l140s02a.getMainId(), l140s02a.getSeq());

					String subject_name = Util.trim(l140s02a.getSubjCode());

					String product_name = l140s02a.getProdKind();
					C900M01A c900m01a = clsService
							.findC900M01A_prodKind(l140s02a.getProdKind());
					if (c900m01a != null) {
						product_name = CrsUtil.getProdKindName(c900m01a);
					}
					if (true) {
						if (c900m01d != null) {
							subject_name = Util.trim(c900m01d.getSubjNm());
							// ==============
							if (Integer.valueOf(c900m01d.getSubjCode2()
									.substring(0, 1)) % 2 == 1) {
								// 無擔
							} else {
								subject2_guarantee.add(Util.trim(c900m01d
										.getSubjCode2()));
							}
						}
						subject_name_set.add(subject_name);
					}
					if (l140s02a.getLnYear() != null
							&& l140s02a.getLnMonth() != null) {
						boolean use_record = false;
						if (maxLnYear == 0 && maxLnMonth == 0) {
							use_record = true;
						} else {
							if ((maxLnYear * 12 + maxLnMonth) < (l140s02a
									.getLnYear() * 12 + l140s02a.getLnMonth())) {
								use_record = true;
							}
						}
						if (use_record) {
							maxLnYear = l140s02a.getLnYear();
							maxLnMonth = l140s02a.getLnMonth();
						}
					}

					if (l140s02c != null) {
						// =========
						// 判斷 利率 => 比照 CLS1151ServiceImpl:: setL140S02ARateDesc
						String rate_key = _CLSDoc1_rate_key(product_name,
								subject_name);

						List<L140S02D> l140s02d_list = clsService
								.findL140S02D_orderByPhase(
										l140s02a.getMainId(),
										l140s02a.getSeq(), "Y");
						if (l140s02d_list.size() > 0) {
							List<String> rate_format_list = new ArrayList<String>();
							for (L140S02D l140s02d : l140s02d_list) {

								String baseDesc = _CLSDoc1_rate_desc(l140s02d,
										rateMap, codeMap, rateUserTypeMap,
										l140s02c, rateBy070);
								if (Util.isNotEmpty(baseDesc)) {
									rate_format_list.add(baseDesc);
								}
							}
							if (true) {
								if (!rate_format_desc.containsKey(rate_key)) {
									rate_format_desc.put(rate_key,
											new LinkedHashSet<String>());
								}
								rate_format_desc.get(rate_key)
										.add(StringUtils.join(rate_format_list,
												"；"));
							}
						}

						if (Util.equals(l140s02c.getIsInputDesc(), "Y")) {
							// rate_free_desc
							if (!rate_free_desc.containsKey(rate_key)) {
								rate_free_desc.put(rate_key,
										new LinkedHashSet<String>());
							}
							rate_free_desc.get(rate_key).add(
									Util.trim(l140s02c.getDesc()));
						}
					}

					if (l140s02e != null) {
						// =========
						// 判斷 寬限期
						String payWay = Util.trim(l140s02e.getPayWay());
						if (("1".equals(payWay) || "3".equals(payWay))
								&& Util.equals("Y", l140s02e.getNowExtend())
								&& Util.parseInt(l140s02e.getNowEnd()) > 1) {
							int period_cnt = Util
									.parseInt(l140s02e.getNowEnd())
									- Util.parseInt(l140s02e.getNowFrom()) + 1;
							BigDecimal currentVal = Arithmetic.div_floor(
									BigDecimal.valueOf(period_cnt),
									BigDecimal.valueOf(12), 2);
							if (currentVal != null
									&& currentVal.compareTo(nowExtendYear) > 0) {
								nowExtendYear = currentVal;
							}
						}
						// =========
						// 判斷 貸款本息攤還方式
						if ("1".equals(payWay)) {
							payway_1 = true;
						} else if ("2".equals(payWay)) {
							payway_2 = true;
						} else if ("3".equals(payWay)) {
							payway_3 = true;
						} else if ("4".equals(payWay)) {
							payway_4 = true;
						} else if ("7".equals(payWay)) {
							payway_7 = true;
						} else if ("6".equals(payWay)) {
							payway_6 = true;
							// 其他
							// ========
							String payWayOth = Util.trim(l140s02e
									.getPayWayOth());
							if (Util.isNotEmpty(payWayOth)) {
								payway_6_desc.add(payWayOth);
							}
						}
					}

					if (l140s02g != null) {
						if (Util.equals(l140s02g.getChgOther(), "Y")) {
							// =========
							// 是否為代償案件
							++l140s02g_chgOther_cnt;
							// =====
							for (L140S02H l140s02h : clsService.findL140S02H(
									l140s02a.getMainId(), l140s02a.getSeq())) {
								String l140s02h_bankName = Util.trim(l140s02h
										.getBankName());
								if (Util.isNotEmpty(l140s02h_bankName)) {
									l140s02h_bankName_desc
											.add(l140s02h_bankName);
								}
							}
						}
					}
				} // end-loop L140S02A
					// ===========================
				if (true) {
					for (L140M01O l140m01o : clsService.findL140M01O(l140m01a
							.getMainId())) {
						String collTyp1 = l140m01o.getCollTyp1();
						String collTyp2 = l140m01o.getCollTyp2();
						if (Util.equals(UtilConstants.CollTyp1.不動產, collTyp1)) {
							cms_01 = true;
						} else if (Util.equals(UtilConstants.CollTyp1.權利質權,
								collTyp1)
								&& Util.equals(UtilConstants.PLEDGEOFRIGHTS.股票,
										collTyp2)) {
							cms_03_07 = true;
						} else {
							cms_others = true;
							if (Util.equals(UtilConstants.CollTyp1.動產, collTyp1)) {
								cms_others_desc.add("動產");
							} else if (Util.equals(UtilConstants.CollTyp1.權利質權,
									collTyp1)) {
								if (Util.equals(
										UtilConstants.PLEDGEOFRIGHTS.銀行定存單,
										collTyp2)) { // 03-01
									cms_others_desc.add("定存單");
								} else if (Util.equals(
										UtilConstants.PLEDGEOFRIGHTS.公債,
										collTyp2)) { // 03-03
									cms_others_desc.add("公債");
								} else if (Util.equals(
										UtilConstants.PLEDGEOFRIGHTS.公司債,
										collTyp2)) { // 03-06
									cms_others_desc.add("公司債");
								} else if (Util.equals(
										UtilConstants.PLEDGEOFRIGHTS.開放型基金,
										collTyp2)) { // 03-08
									cms_others_desc.add("開放型基金");
								} else {
									cms_others_desc.add("權利質權");
								}
							} else if (Util.equals(UtilConstants.CollTyp1.動產質權,
									collTyp1)) {
								cms_others_desc.add("動產質權");
							} else if (Util.equals(UtilConstants.CollTyp1.保證,
									collTyp1)) {
								cms_others_desc.add("保證");
							} else if (Util
									.equals(UtilConstants.CollTyp1.額度本票分期償還票據,
											collTyp1)) {
								cms_others_desc.add("額度本票/備償票據");
							} else if (Util.equals(UtilConstants.CollTyp1.融資客票,
									collTyp1)) {
								cms_others_desc.add("融資客票");
							} else if (Util.equals(UtilConstants.CollTyp1.信託占有,
									collTyp1)) {
								cms_others_desc.add("信託占有");
							} else if (Util.equals(UtilConstants.CollTyp1.參貸他行,
									collTyp1)) {
								cms_others_desc.add("參貸他行");
							} else if (Util.equals(UtilConstants.CollTyp1.其他,
									collTyp1)) {
								cms_others_desc.add("其他/參貸案");
							}
						}
					}

					if (cms_others_desc.size() > 0) {
						cms_others = true;
					}
					if (subject2_guarantee.size() > 0
							&& (cms_01 == false && cms_03_07 == false && cms_others == false)) {
						// 有擔科目, 但未引入擔保品
						if (subject2_guarantee.contains("473")
								|| subject2_guarantee.contains("673")
								|| subject2_guarantee.contains("474")
								|| subject2_guarantee.contains("674")) {
							cms_01 = true;
						} else {
							cms_others = true;
						}
					}
				}
				// ===========================
				for (L140S01A l140s01a : clsService.findL140S01A(l140m01a)) {
					String relateName = Util.trim(l140s01a.getCustName());
					String rKindD = Util.trim(l140s01a.getRKindD());
					if (Util.equals(UtilConstants.lngeFlag.ㄧ般保證人,
							l140s01a.getCustPos())) {
						custPos_N_name_relate.put(relateName, rKindD);
					} else if (Util.equals(UtilConstants.lngeFlag.連帶保證人,
							l140s01a.getCustPos())) {
						custPos_G_name_relate.put(relateName, rKindD);
					}
				}
				// ===========================
				Map<String, String> map = _CLSDoc1_default(defaultBox,
						Field_yearRoc, Field_MM, Field_dd, Field_brNo,
						Field_brName, Field_staff);
				if (true) {
					map.put("Field_cntrNo", l140m01a.getCntrNo());
					map.put("Field_custId", Util.trim(l140m01a.getCustId()));
					map.put("Field_custName", Util.trim(l140m01a.getCustName()));
					if (true) { // 授信科目：[Field_1_11]
						map.put("Field_1_11",
								StringUtils.join(subject_name_set, "、"));
					}
					if (true) { // 貸款額度
						if (!Util.equals("TWD", l140m01a.getCurrentApplyCurr())) {
							map.put("Field_2_11",
									l140m01a.getCurrentApplyCurr());
						}
						map.put("Field_2_12", NumConverter.addComma(LMSUtil
								.pretty_numStr(l140m01a.getCurrentApplyAmt())));
					}
					if (!(maxLnYear == 0 && maxLnMonth == 0)) { // 貸款期限[Field_3_11]年[Field_3_12]月
						map.put("Field_3_11", String.valueOf(maxLnYear));
						map.put("Field_3_12", String.valueOf(maxLnMonth));
					}

					if (true) { // 4.寬限期：□無　　□有， 年
						if (nowExtendYear != null
								&& nowExtendYear.compareTo(BigDecimal.ZERO) > 0) {
							map.put("Field_4_2BOX", chooseBox);
							map.put("Field_4_21",
									LMSUtil.pretty_numStr(nowExtendYear));
						} else {
							map.put("Field_4_1BOX", chooseBox);
						}
					}

					if (true) { // 5.擔保品：□無　　□不動產　　□股票　　□其他
						if (cms_01 == false && cms_03_07 == false
								&& cms_others == false) {
							map.put("Field_5_1BOX", chooseBox);
						} else {
							if (cms_01) {
								map.put("Field_5_2BOX", chooseBox);
							}
							if (cms_03_07) {
								map.put("Field_5_3BOX", chooseBox);
							}
							if (cms_others) {
								map.put("Field_5_4BOX", chooseBox);
								map.put("Field_5_41",
										StringUtils.join(cms_others_desc, ", "));
							}
						}
					}

					if (true) { // 6.是否為代償案件：□否　　□是，　　　　　　　　銀行
						if (l140s02g_chgOther_cnt == 0) {
							map.put("Field_6_1BOX", chooseBox);
						} else {
							map.put("Field_6_2BOX", chooseBox);
							if (l140s02h_bankName_desc.size() > 0) {
								map.put("Field_6_21", StringUtils.join(
										l140s02h_bankName_desc, ", "));
							}
						}
					}

					if (true) { // 7.撥款方式：
						// 在 e-loan 尚不確定
					}

					if (true) { // 8.貸款利率：
						if (rate_format_desc.size() > 0) {
							map.put("Field_8_1BOX", chooseBox);
							map.put("Field_8_11",
									replace_word_br_char(_CLSDoc1_rate_desc_join(
											rate_format_desc,
											l140s02a_list.size() == 1)));
						}
						if (rate_free_desc.size() > 0) {
							map.put("Field_8_2BOX", chooseBox);
							map.put("Field_8_21",
									replace_word_br_char(_CLSDoc1_rate_desc_join(
											rate_free_desc,
											l140s02a_list.size() == 1)));
						}
					}

					if (true) { // 9.貸款本息攤還方式：
						if (payway_1) {
							map.put("Field_9_1BOX", chooseBox);
						}
						if (payway_2) {
							map.put("Field_9_2BOX", chooseBox);
						}
						if (payway_3) {
							map.put("Field_9_3BOX", chooseBox);
						}
						if (payway_4) {
							map.put("Field_9_4BOX", chooseBox);
						}
						if (payway_6) {
							map.put("Field_9_6BOX", chooseBox);
						}
						if (payway_7) {
							map.put("Field_9_7BOX", chooseBox);
						}
						if (payway_6_desc.size() > 0) {
							map.put("Field_9_61",
									replace_word_br_char(StringUtils.join(
											payway_6_desc, " , ")));
						}
					}
					if (true) { // 10.保證人：
						if (custPos_N_name_relate.size() == 0
								&& custPos_G_name_relate.size() == 0) {
							map.put("Field_10_1BOX", chooseBox);
						} else {
							if (custPos_N_name_relate.size() > 0) {
								map.put("Field_10_2BOX", chooseBox);
								map.put("Field_10_21",
										_CLSDoc1_l140s01a(
												custPos_N_name_relate,
												l140s01a_rKindD_codeMap));
							}
							if (custPos_G_name_relate.size() > 0) {
								map.put("Field_10_3BOX", chooseBox);
								map.put("Field_10_31",
										_CLSDoc1_l140s01a(
												custPos_G_name_relate,
												l140s01a_rKindD_codeMap));
							}
						}
					}

					// 簽報書若有多筆額度序號，「各項費用」請顯示於第一筆額度序號之照會結果表中
					if (l140m01r_list.size() == 0) {
						map.put("Field_11_1BOX", chooseBox);
					} else {
						if (cntrNo_paramMap_list.size() == 0) { // 11.各項費用：
							BigDecimal sum_feeNo01 = BigDecimal.ZERO;
							BigDecimal sum_feeNo02 = BigDecimal.ZERO;
							BigDecimal sum_feeNo_oth = BigDecimal.ZERO;
							int cnt__feeNo01 = 0;
							int cnt__feeNo02 = 0;
							int cnt__feeNo_oth = 0;
							for (L140M01R l140m01r : l140m01r_list) {
								String feeNo = l140m01r.getFeeNo();
								BigDecimal amt = l140m01r.getFeeAmt();

								if (Util.equals("01", feeNo)) {
									sum_feeNo01 = sum_feeNo01.add(amt);
									++cnt__feeNo01;
								} else if (Util.equals("02", feeNo)) {
									sum_feeNo02 = sum_feeNo02.add(amt);
									++cnt__feeNo02;
								} else {
									sum_feeNo_oth = sum_feeNo_oth.add(amt);
									++cnt__feeNo_oth;
								}
							}
							if (cnt__feeNo01 > 0) {
								map.put("Field_11_2BOX", chooseBox);
								map.put("Field_11_21", NumConverter
										.addComma(LMSUtil
												.pretty_numStr(sum_feeNo01)));
							}
							if (cnt__feeNo02 > 0) {
								map.put("Field_11_3BOX", chooseBox);
								map.put("Field_11_31", NumConverter
										.addComma(LMSUtil
												.pretty_numStr(sum_feeNo02)));
							}
							if (cnt__feeNo_oth > 0) {
								map.put("Field_11_4BOX", chooseBox);
								map.put("Field_11_41", NumConverter
										.addComma(LMSUtil
												.pretty_numStr(sum_feeNo_oth)));
							}
							// ~~~~~
							show_fee_at_cntrNo = "同額度序號" + l140m01a.getCntrNo();
						} else {
							map.put("Field_11_51", show_fee_at_cntrNo);
						}
					}

				}
				cntrNo_paramMap_list.add(map);
			} // end-loop for (L140M01A l140m01a : list) {

			if (cntrNo_paramMap_list.size() == 0) {
				// 陳復 or 其它原因, 至少填入1筆
				cntrNo_paramMap_list.add(_CLSDoc1_default(defaultBox,
						Field_yearRoc, Field_MM, Field_dd, Field_brNo,
						Field_brName, Field_staff));
			}

			if (true) {
				final_content += part_before;
			}
			if (true) {
				int size = cntrNo_paramMap_list.size();
				for (int i = 0; i < size; i++) {
					Map<String, String> paramMap = cntrNo_paramMap_list.get(i);
					String str = Util.replaceWordContent(part_repeat, paramMap,
							"[", "]", "+UAAAA");

					if (i > 0) {
						// 中間插入 page-break
						int idx = str.indexOf("<w:r ");
						str = str.substring(0, idx) + 換頁符號 + str.substring(idx);
					}
					/*
					 * 若直接寫 += (str+"<w:p>"+換頁符號+"</w:p>") 在第2頁看到的位置, 會比第1次往下移一行
					 */
					final_content += str;
				}
			}
			if (true) {
				final_content += part_after;
			}
			baos = this.writeWordContent(final_content);
		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e));
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	private String _CLSDoc1_rate_key(String productName, String subjectName) {
		return productName + "(" + subjectName + ")";
	}

	// ref ClsUtil :: toWordByL140S02D
	private String _CLSDoc1_rate_desc(L140S02D l140s02d,
			LinkedHashMap<String, String> rateMap,
			Map<String, CapAjaxFormResult> codeMap,
			LinkedHashMap<String, String> rateUserTypeMap, L140S02C l140s02c,
			HashMap<String, String> rateBy070) {

		StringBuffer result = new StringBuffer();
		String taxRateStr = "";
		BigDecimal taxRate = Util.parseBigDecimal(l140s02c.getTaxRate());

		if (BigDecimal.ZERO.compareTo(taxRate) != 0
				&& BigDecimal.ONE.compareTo(taxRate) != 0) {
			// L140M01A.msg067=除以
			taxRateStr = "除以" + taxRate.toString();
		}
		// }
		// page5.065=第
		String 第 = "第";
		// page5.054=期
		String 期 = "期";
		String rateType = Util.trim(l140s02d.getRateType());
		String pmFlag = Util.trim(l140s02d.getPmFlag());
		// 第1期～第12期按行員消貸利率1.62%，機動利率；
		if (Util.isNotEmpty(l140s02d.getBgnNum()) && l140s02d.getBgnNum() != 0) {

			result.append(第).append(l140s02d.getBgnNum()).append(期);
			result.append("~");
			result.append(第).append(l140s02d.getEndNum()).append(期);
		}

		String rateUserTypeStr = "";
		if (CrsUtil.RATE_TYPE_01.equals(rateType)) {
			result.append(rateMap.get(rateType));
			result.append("【")
					.append(NumConverter.addComma(l140s02d.getRateUser()))
					.append("％").append("】");
			result.append(taxRateStr);
			String rateUserType = Util.trim(l140s02d.getRateUserType());
			// L140S02D.rateUserType=自訂利率參考指標
			if (Util.isNotEmpty(rateUserType)) {
				rateUserTypeStr = StrUtils.concat("，", "自訂利率參考指標", " ",
						rateUserType, " ",
						Util.trim(rateUserTypeMap.get(rateUserType)));
			}

		} else {
			if (rateMap.containsKey(rateType)) {
				/*
				 * 2015.10.12授管處王惜齡襄理 接到分行反應 　　在 7D 時，印出的說明文字只有 6R 的代碼，但無7D的代碼
				 * 　　容易使人誤解
				 * 
				 * 7D-新兆豐金控集團職員貸款利率（６Ｒ）
				 * 
				 * 在特定利率代碼(EX：7D）時，一併印出代碼
				 */
				String[] decoArr = { "7D" };
				String rateDesc = rateMap.get(rateType);
				if (CrsUtil.inCollection(rateType, decoArr)) {
					rateDesc = (rateType + "-") + rateDesc;
				}
				result.append(rateDesc);

			} else {
				// 當利率不存在於MISLNRAT
				if (Util.isNotEmpty(rateType) && rateBy070 != null
						&& rateBy070.containsKey("00" + rateType)) {
					result.append(rateBy070.get("00" + rateType));
				}
			}

			if (Util.isNotEmpty(pmFlag)) {
				result.append(codeMap.get("L140S02D_pmFlag").get(pmFlag));
				result.append(
						NumConverter.addComma(NumConverter.addComma(Util
								.parseBigDecimal(l140s02d.getPmRate()))))
						.append("％");
			} else {
				// if (l140s02d.getBaseRate() != null) {
				// result.append(Util.parseBigDecimal(l140s02d.getBaseRate()))
				// .append("％");
				// }
			}

			result.append(taxRateStr);
			BigDecimal nowRate = l140s02d.getNowRate();
			if (nowRate != null) {
				// page5.071=目前為
				result.append("(").append("目前為");
				result.append("【").append(NumConverter.addComma(nowRate))
						.append("％").append("】");
				result.append(")");
			}

		}
		result.append(rateUserTypeStr);
		return result.toString();
	}

	private String _CLSDoc1_rate_desc_join(
			Map<String, LinkedHashSet<String>> map, boolean singleL140S02A) {
		if (singleL140S02A) {
			for (String rate_key : map.keySet()) {
				return StringUtils.join(map.get(rate_key), "\n");
			}
			return "";
		} else {
			List<String> list = new ArrayList<String>();
			for (String rate_key : map.keySet()) {
				list.add(rate_key + "\n"
						+ StringUtils.join(map.get(rate_key), "\n"));
			}
			return StringUtils.join(list, "\n");
		}
	}

	private Map<String, String> _CLSDoc1_default(String defaultBox,
			String Field_yearRoc, String Field_MM, String Field_dd,
			String Field_brNo, String Field_brName, String Field_staff) {
		Map<String, String> map = new HashMap<String, String>();

		String Field_cntrNo = "";
		String Field_custId = "";
		String Field_custName = "";
		String Field_1_11 = "_________________________";
		String Field_2_11 = "新臺幣";
		String Field_2_12 = "____________";
		String Field_3_11 = "_____";
		String Field_3_12 = "_____";
		;
		String Field_4_1BOX = defaultBox;
		String Field_4_2BOX = defaultBox;
		String Field_4_21 = "_____";
		String Field_5_1BOX = defaultBox;
		String Field_5_2BOX = defaultBox;
		String Field_5_3BOX = defaultBox;
		String Field_5_4BOX = defaultBox;
		String Field_5_41 = "，_________";
		String Field_6_1BOX = defaultBox;
		String Field_6_2BOX = defaultBox;
		String Field_6_21 = "__________";
		String Field_7_1BOX = defaultBox; // 撥付借款人在本行開設之__________存款第_____________________號帳戶
		String Field_7_11 = "____________";
		String Field_7_12 = "_________________________";
		String Field_7_2BOX = defaultBox;
		String Field_7_21 = "";
		String Field_8_1BOX = defaultBox;
		String Field_8_11 = "第___個月至___個月按本行消費金融放款指標利率加_____%(目前為：_____%)";
		String Field_8_2BOX = defaultBox;
		String Field_8_21 = "";
		String Field_9_1BOX = defaultBox;
		String Field_9_2BOX = defaultBox;
		String Field_9_3BOX = defaultBox;
		String Field_9_4BOX = defaultBox;
		String Field_9_6BOX = defaultBox;
		String Field_9_7BOX = defaultBox;
		String Field_9_61 = "";
		String Field_10_1BOX = defaultBox;
		String Field_10_2BOX = defaultBox;
		String Field_10_21 = "____________";
		String Field_10_3BOX = defaultBox;
		String Field_10_31 = "____________";
		String Field_11_1BOX = defaultBox;
		String Field_11_2BOX = defaultBox;
		String Field_11_21 = "____________";
		String Field_11_3BOX = defaultBox;
		String Field_11_31 = "__________";
		String Field_11_4BOX = defaultBox;
		String Field_11_41 = "__________";
		String Field_11_51 = "";
		map.put("Field_yearRoc", Field_yearRoc);
		map.put("Field_MM", Field_MM);
		map.put("Field_dd", Field_dd);
		map.put("Field_brNo", Field_brNo);
		map.put("Field_brName", Field_brName);
		map.put("Field_staff", Field_staff);
		map.put("Field_cntrNo", Field_cntrNo);
		map.put("Field_custId", Field_custId);
		map.put("Field_custName", Field_custName);
		map.put("Field_1_11", Field_1_11);
		map.put("Field_2_11", Field_2_11);
		map.put("Field_2_12", Field_2_12);
		map.put("Field_3_11", Field_3_11);
		map.put("Field_3_12", Field_3_12);
		map.put("Field_4_1BOX", Field_4_1BOX);
		map.put("Field_4_2BOX", Field_4_2BOX);
		map.put("Field_4_21", Field_4_21);
		map.put("Field_5_1BOX", Field_5_1BOX);
		map.put("Field_5_2BOX", Field_5_2BOX);
		map.put("Field_5_3BOX", Field_5_3BOX);
		map.put("Field_5_4BOX", Field_5_4BOX);
		map.put("Field_5_41", Field_5_41);
		map.put("Field_6_1BOX", Field_6_1BOX);
		map.put("Field_6_2BOX", Field_6_2BOX);
		map.put("Field_6_21", Field_6_21);
		map.put("Field_7_1BOX", Field_7_1BOX);
		map.put("Field_7_11", Field_7_11);
		map.put("Field_7_12", Field_7_12);
		map.put("Field_7_2BOX", Field_7_2BOX);
		map.put("Field_7_21", Field_7_21);
		map.put("Field_8_1BOX", Field_8_1BOX);
		map.put("Field_8_11", Field_8_11);
		map.put("Field_8_2BOX", Field_8_2BOX);
		map.put("Field_8_21", Field_8_21);
		map.put("Field_9_1BOX", Field_9_1BOX);
		map.put("Field_9_2BOX", Field_9_2BOX);
		map.put("Field_9_3BOX", Field_9_3BOX);
		map.put("Field_9_4BOX", Field_9_4BOX);
		map.put("Field_9_6BOX", Field_9_6BOX);
		map.put("Field_9_7BOX", Field_9_7BOX);
		map.put("Field_9_61", Field_9_61);
		map.put("Field_10_1BOX", Field_10_1BOX);
		map.put("Field_10_2BOX", Field_10_2BOX);
		map.put("Field_10_21", Field_10_21);
		map.put("Field_10_3BOX", Field_10_3BOX);
		map.put("Field_10_31", Field_10_31);
		map.put("Field_11_1BOX", Field_11_1BOX);
		map.put("Field_11_2BOX", Field_11_2BOX);
		map.put("Field_11_21", Field_11_21);
		map.put("Field_11_3BOX", Field_11_3BOX);
		map.put("Field_11_31", Field_11_31);
		map.put("Field_11_4BOX", Field_11_4BOX);
		map.put("Field_11_41", Field_11_41);
		map.put("Field_11_51", Field_11_51);
		return map;
	}

	private String _CLSDoc1_l140s01a(Map<String, String> map,
			Map<String, CapAjaxFormResult> l140s01a_rKindD_codeMap) {
		List<String> list = new ArrayList<String>();
		for (String name : map.keySet()) {
			list.add("姓名："
					+ name
					+ "，關係類別："
					+ LMSUtil.changeCustRlt(map.get(name),
							l140s01a_rKindD_codeMap) + "。");
		}
		return StringUtils.join(list, " ");
	}

	/**
	 * 參考 LMS9990DOC02ServiceImpl :: writeWordContent(...)
	 * 
	 * @param content
	 * @return
	 * @throws CapMessageException
	 */
	private ByteArrayOutputStream writeWordContent(String content)
			throws CapMessageException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		OutputStreamWriter outWriter = null;
		try {
			outWriter = new OutputStreamWriter(baos, "UTF-8");
			outWriter.write(content);
			outWriter.close();
			return baos;
		} catch (UnsupportedEncodingException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException i) {
			logger.error(i.getMessage());
			throw new CapMessageException(getMessage(i.getMessage()),
					getClass());
		}
	}

	private String replace_word_br_char(String str) {
		return str == null ? "" : str.replaceAll("\n", 換行符號);
	}

	/**
	 * 授信額度檢視表
	 * 
	 * J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord22a(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得簽報書mainId
		String mainId = params.getString("mainId");

		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;

		Map<String, String> proPertyMap = codeService.findByCodeType(
				"lms1405s02_proPerty", LMSUtil.getLocale().toString());
		DecimalFormat df = new DecimalFormat("###,###,###,###,###,###,###,##0");

		try {
			// FileUtils.readFileToString(file, "UTF-8");
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			// String str = FileUtils.readFileToString(file, "UTF-8");
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			String bodStr1 = "<div class=WordSection1";

			// 找尋資料區塊開始字串
			String bodStr2 = "<tr class='dataHtml'";

			String bodStr3 = "<tr class='dataHtm2'";

			// 找尋資料區塊結束字串

			String dataEnd = "</table>";

			String bodEnd = "</body>";

			// 資料區塊1開始位置
			int strBod1 = sbWord.indexOf(bodStr1, 0);
			// 資料區塊2開始位置
			int strBod2 = sbWord.indexOf(bodStr2, strBod1);
			// 資料區塊2開始位置
			int strBod3 = sbWord.indexOf(bodStr3, strBod2);
			// 資料區塊2結束位置
			int endData = sbWord.indexOf(dataEnd, strBod3);

			// 資料區塊2結束位置
			int endBod = sbWord.indexOf(bodEnd, endData);

			// 表頭區塊
			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(sbWord.substring(0, strBod1));

			// 資料區塊1
			StringBuilder sbData1 = new StringBuilder();
			sbData1.append(sbWord.substring(strBod1, strBod2));

			// 資料區塊2
			StringBuilder sbData2 = new StringBuilder();
			sbData2.append(sbWord.substring(strBod2, strBod3));

			// 資料區塊3
			StringBuilder sbData3 = new StringBuilder();
			sbData3.append(sbWord.substring(strBod3, endData));

			// 資料區塊4
			StringBuilder sbData4 = new StringBuilder();
			sbData4.append(sbWord.substring(endData, endBod));

			StringBuilder sbDataFinal = new StringBuilder();

			// 表尾區塊
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(sbWord.substring(endBod));

			// 稽核資料是否都順利取得
			if (sbHeader == null || sbData1 == null || sbData2 == null
					|| sbFooter == null) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1205V01Page.class);
				throw new CapMessageException(RespMsgHelper.getMessage(
						"EFD0025", pop.getProperty("l120v01.error1")),getClass());
			}

			L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
			// 取得額度明細表
			List<L140M01A> list = service1401.findL140m01aListByL120m01cMainId(
					l120m01a.getMainId(), "1");

			List<String> oldVal1 = new ArrayList<String>();
			List<String> newVal1 = new ArrayList<String>();

			oldVal1.add("%LMSVAR001%");

			StringBuilder oldData1 = new StringBuilder();
			oldData1.append(sbData1);

			StringBuilder oldData2 = new StringBuilder();
			oldData2.append(sbData2);

			StringBuilder oldData3 = new StringBuilder();
			oldData3.append(sbData3);

			// 將資料區塊清空以便開始Append
			StringBuilder tempData1 = new StringBuilder();
			StringBuilder tempData2 = new StringBuilder();
			StringBuilder tempData3 = new StringBuilder();

			int count = 0;
			docName = "LMSDoc22";

			String gCustId = "";
			boolean isNewPage = false;

			List<String> oldVal2 = new ArrayList<String>();
			List<String> newVal2 = new ArrayList<String>();

			// 授審會範本有四個資料變數
			oldVal2.add("%LMSREP001%");
			oldVal2.add("%LMSREP002%");
			oldVal2.add("%LMSREP003%");
			oldVal2.add("%LMSREP004%");
			oldVal2.add("%LMSREP005%");

			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord.setLength(0);

			int allCount = 0;
			gCustId = "";
			for (L140M01A l140m01a : list) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());

				if (Util.notEquals(gCustId, custId)) {
					gCustId = custId;
					allCount = allCount + 1;
				} else {
					continue;
				}
			}

			// 開始替換資料
			gCustId = "";
			for (L140M01A l140m01a : list) {

				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());

				if (Util.notEquals(gCustId, custId)) {
					gCustId = custId;
					count = count + 1;
				} else {
					continue;
				}

				sbData1.setLength(0);
				sbData2.setLength(0);
				sbData3.setLength(0);

				// 清除要替換的資料內容

				// 將暫存內容初始化
				tempData1.setLength(0);
				// 將保留區塊移到暫存
				tempData1.append(oldData1);
				// 清除要替換的資料內容

				newVal1.clear();
				newVal1.add(Util.trim(l140m01a.getCustName()));

				// 替換表頭
				replaceStrB(tempData1, newVal1, oldVal1);

				sbData1.append(tempData1);

				// 完成處理表頭資料

				// 開始處理明細資料區塊
				// 處理sbData 資料區塊
				List<L140M01A> listl140m01a = service1401
						.findL140m01aListByMainIdCustId(l120m01a.getMainId(),
								custId, dupNo, "1");

				StringBuffer memo = new StringBuffer("");
				if (listl140m01a != null && !listl140m01a.isEmpty()) {

					L140M01A totalL140m01a = null;
					for (L140M01A tl140m01a : listl140m01a) {

						// 備註
						String rmk = Util.trim(tl140m01a.getRmk()).replace(
								"\r", "<br/>");
						if (Util.equals(memo.toString(), "")) {
							memo.append(rmk);
						} else {
							memo.append("<br/>").append(rmk);
						}

						if (totalL140m01a == null) {
							totalL140m01a = tl140m01a;
						}

						// 將暫存內容初始化
						tempData2.setLength(0);
						// 將保留區塊移到暫存
						tempData2.append(oldData2);
						// 清除要替換的資料內容
						newVal2.clear();

						// 編製單位
						// newVal.add(XmlTool.replaceXMLReservedWord(branch.getBranchName
						// (Util.trim(l140m01a.getOwnBrId())),true));
						newVal2.add(tl140m01a.getLnSubject());
						newVal2.add(this.getProPerty(
								Util.nullToSpace(tl140m01a.getProPerty()),
								proPertyMap));

						newVal2.add(tl140m01a.getLV2Amt() == null ? "N.A."
								: tl140m01a.getLV2Curr()
										+ (tl140m01a.getLV2Amt().compareTo(
												BigDecimal.ZERO) == 0 ? "0"
												: df.format(tl140m01a
														.getLV2Amt()
														.divide(new BigDecimal(
																"1000"),
																0,
																BigDecimal.ROUND_HALF_UP))));
						newVal2.add(tl140m01a.getCurrentApplyAmt() == null ? "N.A."
								: tl140m01a.getCurrentApplyCurr()
										+ (tl140m01a.getCurrentApplyAmt()
												.compareTo(BigDecimal.ZERO) == 0 ? "0"
												: df.format(tl140m01a
														.getCurrentApplyAmt()
														.divide(new BigDecimal(
																"1000"),
																0,
																BigDecimal.ROUND_HALF_UP))));

						// 說明(從L120S16A) 「六、主要申請敘做條件」之(一)主要敘做條件維護功能,
						// 增加額度檢視表所需「說明」欄位
						L120S16B l120s16b = service1201
								.findL120s16bByUniqueKey(
										mainId,
										"1",
										tl140m01a.getCustId(),
										tl140m01a.getDupNo(),
										tl140m01a.getCntrNo(),
										UtilConstants.Casedoc.L120s16bItemType.說明);
						if (l120s16b != null) {
							newVal2.add(Util.trim(l120s16b.getItemDscr())
									.replace("\n", "<br/>"));
						} else {
							newVal2.add("");
						}

						// 替換資料
						replaceStrB(tempData2, newVal2, oldVal2);
						// 完成處理表頭資料
						// 將修改過得資料存進去
						sbData2.append(tempData2);

					}

					// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
					// 新增一列額度合計************************************************

					// 將暫存內容初始化
					tempData2.setLength(0);
					// 將保留區塊移到暫存
					tempData2.append(oldData2);
					// 清除要替換的資料內容
					newVal2.clear();
					newVal2.add("額度合計");
					newVal2.add("");
					newVal2.add(totalL140m01a.getLVTotAmt() == null ? "N.A."
							: totalL140m01a.getLVTotCurr()
									+ (totalL140m01a.getLVTotAmt().compareTo(
											BigDecimal.ZERO) == 0 ? "0"
											: df.format(totalL140m01a
													.getLVTotAmt()
													.divide(new BigDecimal(
															"1000"),
															0,
															BigDecimal.ROUND_HALF_UP))));
					newVal2.add(totalL140m01a.getLoanTotAmt() == null ? "N.A."
							: totalL140m01a.getLoanTotCurr()
									+ (totalL140m01a.getLoanTotAmt().compareTo(
											BigDecimal.ZERO) == 0 ? "0"
											: df.format(totalL140m01a
													.getLoanTotAmt()
													.divide(new BigDecimal(
															"1000"),
															0,
															BigDecimal.ROUND_HALF_UP))));
					newVal2.add("");
					// 替換資料
					replaceStrB(tempData2, newVal2, oldVal2);
					// 完成處理表頭資料
					// 將修改過得資料存進去
					sbData2.append(tempData2);

					// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
					// 新增一列擔保額度合計************************************************

					// 將暫存內容初始化
					tempData2.setLength(0);
					// 將保留區塊移到暫存
					tempData2.append(oldData2);
					// 清除要替換的資料內容
					newVal2.clear();
					newVal2.add("擔保額度合計");
					newVal2.add("");
					// 前次
					newVal2.add(totalL140m01a.getLVAssTotAmt() == null ? "N.A."
							: totalL140m01a.getLVAssTotCurr()
									+ (totalL140m01a.getLVAssTotAmt()
											.compareTo(BigDecimal.ZERO) == 0 ? "0"
											: df.format(totalL140m01a
													.getLVAssTotAmt()
													.divide(new BigDecimal(
															"1000"),
															0,
															BigDecimal.ROUND_HALF_UP))));
					// 本次
					newVal2.add(totalL140m01a.getAssureTotAmt() == null ? "N.A."
							: totalL140m01a.getAssureTotCurr()
									+ (totalL140m01a.getAssureTotAmt()
											.compareTo(BigDecimal.ZERO) == 0 ? "0"
											: df.format(totalL140m01a
													.getAssureTotAmt()
													.divide(new BigDecimal(
															"1000"),
															0,
															BigDecimal.ROUND_HALF_UP))));
					newVal2.add("");
					// 替換資料
					replaceStrB(tempData2, newVal2, oldVal2);
					// 完成處理表頭資料
					// 將修改過得資料存進去
					sbData2.append(tempData2);

				}

				// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
				List<String> oldVal3 = new ArrayList<String>();
				List<String> newVal3 = new ArrayList<String>();

				// 將暫存內容初始化
				tempData3.setLength(0);
				// 將保留區塊移到暫存
				tempData3.append(oldData3);
				// 清除要替換的資料內容
				newVal3.clear();

				// 授審會範本有四個資料變數
				oldVal3.add("%LMSREP006%");
				newVal3.add(memo.toString());
				replaceStrB(tempData3, newVal3, oldVal3);
				sbData3.append(tempData3);

				sbDataFinal.append(sbData1).append(sbData2).append(sbData3)
						.append(sbData4);

				if (count < allCount) {
					sbDataFinal.append(wordPage);
				}

			}

			sbWord.append(sbHeader).append(sbDataFinal).append(sbFooter);

			// OutputStreamWriter outWriter = new OutputStreamWriter(baos,
			// "UTF-8");
			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "BIG5");
			outWriter.write(sbWord.toString());
			outWriter.close();
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 取得常董稿(個案討論)
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord3a_v202009(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得使用者所選擇資料之oid
		String oid = params.getString("oid");
		// 取得文件狀態
		String txCode = params.getString("txCode");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;

		try {
			// 開始讀取第一範本檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);

			// 讀取第一範本資料
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 存取要被取代成第二範本資料內容的資料內容
			StringBuilder bgData = new StringBuilder();
			bgData.append(sbWord.toString());

			// 開始替換第一範本資料
			if (!oid.isEmpty()) {
				L120M01A model = service1201.findL120m01aByOid(oid);

				IBranch iBranch = branch.getBranch(model.getCaseBrId());
				String brnMgr = Util.trim(iBranch.getBrnMgr());
				String brnMgrName = brnMgr.length() >= 5 ? userSrv
						.getUserName(brnMgr) : "";

				// 取得第二XML範本檔案名稱
				StringBuilder fileName2 = new StringBuilder();
				String fName2 = (UtilConstants.Casedoc.typCd.DBU.equals(Util
						.trim(model.getTypCd()))) ? "LMSDoc42_V202009.htm"
						: params.getString("fileName2");
				fileName2
						.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
						.append("word/").append(Util.trim(fName2));
				List<String> oldVal = new ArrayList<String>();
				List<String> newVal = new ArrayList<String>();
				oldVal.add("%LMSVAR001%");
				oldVal.add("%LMSVAR002%");
				oldVal.add("%LMSVAR003%");
				oldVal.add("%LMSVAR004%");
				oldVal.add("%LMSVAR005%");
				oldVal.add("%LMSVAR006%");
				StringBuilder sbDec = new StringBuilder();
				docName = "LMSDoc4";
				String hqMeetFlag = Util.trim(model.getHqMeetFlag());
				// 案由part1
				L120M01H l120m01h = service1201.findL120m01hByUniqueKey(
						model.getMainId(), hqMeetFlag);
				sbDec.setLength(0);
				if (Util.isEmpty(model.getGist()) || "".equals(model.getGist())) {
					if (l120m01h != null) {
						sbDec.append(Util.trim(l120m01h.getDispWord()));
					}
				} else {
					if (l120m01h != null) {
						sbDec.append(
								Util.trim(model.getGist()).replace("\n",
										"<br/>")).append("<br/>")
								.append("<br/>")
								.append(Util.trim(l120m01h.getDispWord()));
					} else {
						sbDec.append(
								Util.trim(model.getGist()).replace("\n",
										"<br/>")).append("<br/>")
								.append("<br/>");
					}
				}
				newVal.add(sbDec.toString());
				// 案由part2
				sbDec.setLength(0);
				if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
					// 如果是授管處的常董會->執行此條件
					// 一般分行與總處分行不一樣，總處分行是自行提常董會，
					// 所以為( 國金部提)，但是一般分行是( 授信管理處提＼忠孝分行承做 )
					if (LMSUtil.isSpecialBranch(Util.trim(model.getCaseBrId()))) {
						sbDec.append(
								branch.getBranchName(Util.trim(model
										.getCaseBrId()))).append("提");
					} else {
						// UFO@20130618:調整判斷授管處OR債管處
						String deptName = NGFlagHelper
								.getDeptName(user, branch) + "提\\";
						sbDec.append(deptName)
								.append(branch.getBranchName(Util.trim(model
										.getCaseBrId()))).append("承做")
								.append("，經理人：").append(brnMgrName);
					}
				} else {
					String title = "";
					// 若為OBU案件則需再加國金部字眼
					if ("4".equals(Util.trim(model.getTypCd()))) {
						// OBU
						title = "\\國金部承做";
					} else {
						title = "承做";
					}
					// UFO@20130618:調整判斷授管處OR債管處
					String deptName = NGFlagHelper.getDeptName(user, branch)
							+ "提\\";
					sbDec.append(deptName)
							.append(branch.getBranchName(Util.trim(model
									.getCaseBrId()))).append(title)
							.append("，經理人：").append(brnMgrName);
				}
				newVal.add(sbDec.toString());
				// 客戶資料(第二範本區塊內容)
				newVal.add(getWord2(oid, fileName2.toString(), "", "", false));
				// 敘做理由
				sbDec.setLength(0);
				// 若為授權外其他，則為空白
				if ("2".equals(Util.trim(model.getDocKind()))
						&& ("2".equals(Util.trim(model.getDocCode())) || "3"
								.equals(Util.trim(model.getDocCode())))) {
					sbDec.append("無");
				} else {
					L120M01D l120m01d = service1201.findL120m01dByUniqueKey(
							model.getMainId(), "4");
					if (l120m01d != null) {
						sbDec.append(Util.trim(l120m01d.getItemDscr()));
					} else {
						sbDec.append("無");
					}
				}
				newVal.add(Util.getDocRealImgPath(sbDec.toString()));
				// newVal.add(sbDec.toString());
				// 分行逾放比
				sbDec.setLength(0);

				List<L140M01A> listL140m01a = service1401
						.findL140m01aListByL120m01cMainId(model.getMainId(),
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				if (!listL140m01a.isEmpty()) {
					Date nplDate = null;
					HashMap<String, String> tempMap = new HashMap<String, String>();
					for (L140M01A l140m01a : listL140m01a) {
						String npl = Util.trim(l140m01a.getNpl());
						nplDate = l140m01a.getNpldate();
						if (Util.isNotEmpty(npl) && Util.isNotEmpty(nplDate)) {
							String[] nplArray = npl.split("、");
							for (String key : nplArray) {
								String tempKey = Util.trim(key);
								if (!tempMap.containsKey(tempKey)) {
									sbDec.append(sbDec.length() > 0 ? "，" : "");
									sbDec.append(tempKey);
									tempMap.put(tempKey, "");
								}
							}

							// sbDec.append("資料年月：")
							// .append(CapDate
							// .convertDateToTaiwanYear(TWNDate
							// .toAD(l140m01a.getNpldate())
							// .subSequence(0, 4)
							// .toString()))
							// .append("/")
							// .append(TWNDate.toAD(l140m01a.getNpldate())
							// .subSequence(5, 7).toString())
							// .append(" ")
							// .append(Util.trim(l140m01a.getNpl()))
							// .append("%").append(strEnter);

						}
					}
					sbDec.append(" ");
					sbDec.append("資料年月：");
					sbDec.append(CapDate.convertDateToTaiwanYear(TWNDate
							.toAD(nplDate).subSequence(0, 4).toString()));
					sbDec.append("/");
					sbDec.append(TWNDate.toAD(nplDate).subSequence(5, 7)
							.toString());
					sbDec.append(strEnter);
				}
				newVal.add(sbDec.toString());

				// 總處審查意見
				L120M01H l120m01h02 = service1201.findL120m01hByUniqueKey(
						model.getMainId(), "1");
				L120M01H l120m01h0a = service1201.findL120m01hByUniqueKey(
						model.getMainId(), "A");
				sbDec.setLength(0);
				// 國外部007 金控總部201 國金部025
				if (LMSUtil.isSpecialBranch(user.getUnitNo())) {
					if (UtilConstants.Casedoc.AreaChk.送會簽.equals(Util
							.trim(model.getAreaChk()))) {
						// 不提會, 抓法金處/個金處會簽意見
						// 授管處會簽意見
						L120M01D l120m01d = service1201
								.findL120m01dByUniqueKey(model.getMainId(), "C");
						if (l120m01d != null) {
							sbDec.append(Util.trim(l120m01d.getItemDscr()));
						}
					} else if (UtilConstants.Casedoc.AreaChk.送初審.equals(Util
							.trim(model.getAreaChk()))) {
						// (108)第 3230 號
						// 不提會, 抓法金處/個金處會簽意見
						// 授管處會簽意見
						L120M01D l120m01d = service1201
								.findL120m01dByUniqueKey(model.getMainId(), "C");
						if (l120m01d != null) {
							sbDec.append(Util.trim(l120m01d.getItemDscr()));
						}
					} else {
						String meetingNote = "";
						if (l120m01h0a != null) {
							// 提會, 抓授審會決議
							meetingNote = Util
									.trim(l120m01h0a.getMeetingNote());
						} else {
							meetingNote = "";
						}
						sbDec.append("本案經提")
								.append(Util.trim(model.getRptTitle1()))
								.append("討論，結論：").append("\t")
								.append(Util.getDocRealImgPath(meetingNote));
					}
				} else {
					// 若在法金處/個金處產生時，則國外部抓會簽, 一般分行抓法金處/個金處審查意見
					if ("".equals(model.getRptTitle1())
							|| Util.isEmpty(model.getRptTitle1())) {
						// 不提會, 抓授管處審查意見
						L120M01D l120m01d = service1201
								.findL120m01dByUniqueKey(model.getMainId(), "B");
						if (l120m01d != null) {
							sbDec.append(Util.trim(l120m01d.getItemDscr()));
						}
					} else {
						String meetingNote = "";
						if (CreditDocStatusEnum.會簽後修改編製中.getCode().equals(
								Util.trim(model.getDocStatus()))) {
							if (l120m01h0a != null) {
								// 提會, 抓授審會決議
								meetingNote = Util.trim(l120m01h0a
										.getMeetingNote());
							} else {
								// 提會, 抓授審會決議
								meetingNote = "";
							}
						} else {
							if (l120m01h02 != null) {
								// 提會, 抓授審會決議
								meetingNote = Util.trim(l120m01h02
										.getMeetingNote());
							} else {
								// 提會, 抓授審會決議
								meetingNote = "";
							}
						}
						sbDec.append("本案經提")
								.append(Util.trim(model.getRptTitle1()))
								.append("討論，結論：").append("\t")
								.append(Util.getDocRealImgPath(meetingNote));
					}
				}
				newVal.add(sbDec.toString());
				// 替換第一範本資料
				replaceStrB(sbWord, newVal, oldVal);
				// 完成處理

				OutputStreamWriter outWriter = new OutputStreamWriter(baos,
						"BIG5");
				outWriter.write(sbWord.toString());
				outWriter.close();

			} else {
				// 印出找不到資料錯誤
				// 查無資料
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
			}
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (MissingResourceException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 取得常董稿(個案討論-審查意見)
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord3a_v202011(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得使用者所選擇資料之oid
		String oid = params.getString("oid");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;

		// 文件名稱
		String docName = "";
		// 取得文件狀態
		String txCode = params.getString("txCode");

		try {
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);

			if (!oid.isEmpty()) {
				List<String> oldVal = new ArrayList<String>();
				List<String> newVal = new ArrayList<String>();
				oldVal.add("%LMSVAR001%");

				L120M01A model = service1201.findL120m01aByOid(oid);
				// 總處審查意見
				StringBuilder sbDec = new StringBuilder();
				L120M01H l120m01h02 = service1201.findL120m01hByUniqueKey(
						model.getMainId(), "1");
				L120M01H l120m01h0a = service1201.findL120m01hByUniqueKey(
						model.getMainId(), "A");
				sbDec.setLength(0);
				// 國外部007 金控總部201 國金部025
				if (LMSUtil.isSpecialBranch(user.getUnitNo())) {
					if (UtilConstants.Casedoc.AreaChk.送會簽.equals(Util
							.trim(model.getAreaChk()))) {
						// 不提會, 抓法金處/個金處會簽意見
						// 授管處會簽意見
						L120M01D l120m01d = service1201
								.findL120m01dByUniqueKey(model.getMainId(), "C");
						if (l120m01d != null) {
							sbDec.append(Util.trim(l120m01d.getItemDscr()));
						}
					} else if (UtilConstants.Casedoc.AreaChk.送初審.equals(Util
							.trim(model.getAreaChk()))) {
						// (108)第 3230 號
						// 不提會, 抓法金處/個金處會簽意見
						// 授管處會簽意見
						L120M01D l120m01d = service1201
								.findL120m01dByUniqueKey(model.getMainId(), "C");
						if (l120m01d != null) {
							sbDec.append(Util.trim(l120m01d.getItemDscr()));
						}
					} else {
						String meetingNote = "";
						if (l120m01h0a != null) {
							// 提會, 抓授審會決議
							meetingNote = Util
									.trim(l120m01h0a.getMeetingNote());
						} else {
							meetingNote = "";
						}
						sbDec.append("本案經提")
								.append(Util.trim(model.getRptTitle1()))
								.append("討論，結論：").append("\t")
								.append(Util.getDocRealImgPath(meetingNote));
					}
				} else {
					// 若在法金處/個金處產生時，則國外部抓會簽, 一般分行抓法金處/個金處審查意見
					if ("".equals(model.getRptTitle1())
							|| Util.isEmpty(model.getRptTitle1())) {
						// 不提會, 抓授管處審查意見
						L120M01D l120m01d = service1201
								.findL120m01dByUniqueKey(model.getMainId(), "B");
						if (l120m01d != null) {
							sbDec.append(Util.trim(l120m01d.getItemDscr()));
						}
					} else {
						String meetingNote = "";
						if (CreditDocStatusEnum.會簽後修改編製中.getCode().equals(
								Util.trim(model.getDocStatus()))) {
							if (l120m01h0a != null) {
								// 提會, 抓授審會決議
								meetingNote = Util.trim(l120m01h0a
										.getMeetingNote());
							} else {
								// 提會, 抓授審會決議
								meetingNote = "";
							}
						} else {
							if (l120m01h02 != null) {
								// 提會, 抓授審會決議
								meetingNote = Util.trim(l120m01h02
										.getMeetingNote());
							} else {
								// 提會, 抓授審會決議
								meetingNote = "";
							}
						}
						sbDec.append("本案經提")
								.append(Util.trim(model.getRptTitle1()))
								.append("討論，結論：").append("\t")
								.append(Util.getDocRealImgPath(meetingNote));
					}
				}
				newVal.add(sbDec.toString());
				// 替換資料
				replaceStrB(sbWord, newVal, oldVal);
				// 完成處理

				OutputStreamWriter outWriter = new OutputStreamWriter(baos,
						"BIG5");
				outWriter.write(sbWord.toString());
				outWriter.close();
			} else {
				// 印出找不到資料錯誤
				// 查無資料
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
			}
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (MissingResourceException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * J-110-0499 配合作業合理化提案，調整會議決議及相關A、B、C版 列印會議決議及相關A、B、C版
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getLMSDoc9(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得mainId
		String mainId = params.getString("mainId");
		String l120m01hOid = params.getString("l120m01hOid");
		String type = Util.nullToSpace(params.getString("type"));
		String caseLvlVal = Util.nullToSpace(params.getString("caseLvlVal"));
		// caseLvlVal: 1-個案討論 2-彙總討論
		String caseLvlStr = "";
		if (Util.equals(caseLvlVal, "1")) {
			caseLvlStr = "個案討論";
		} else if (Util.equals(caseLvlVal, "2")) {
			caseLvlStr = "彙總討論";
		}
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		try {
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);

			// 找尋資料區塊開始字串
			String bodStr = "<div class=Section1";
			// 找尋資料區塊結束字串
			String bodEnd = "</body>";
			// 資料區塊開始位置
			int strBod = sbWord.indexOf(bodStr, 0);
			// 資料區塊結束位置
			int endBod = sbWord.indexOf(bodEnd, strBod);

			// 表頭區塊
			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(sbWord.substring(0, strBod));
			// 資料區塊
			StringBuilder sbData = new StringBuilder();
			sbData.append(sbWord.substring(strBod, endBod));
			// 表尾區塊
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(sbWord.substring(endBod));
			// 稽核資料是否都順利取得
			if (sbHeader == null || sbData == null || sbFooter == null) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1205V01Page.class);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", pop.getProperty("l120v01.error1")),
						getClass());
			}

			L120M01A model = service1201.findL120m01aByMainId(mainId);
			if (model == null) {
				model = new L120M01A();
			}
			L120M01H l120m01h = service1201.findL120m01hByOid(l120m01hOid);
			if (l120m01h == null) {
				l120m01h = new L120M01H();
			}

			List<String> oldVal = new ArrayList<String>();
			List<String> newVal = new ArrayList<String>();
			if (Util.equals(type, "Z")) { // 決議
				oldVal.add("%LMSVAR001%");
				oldVal.add("%LMSVAR002%");
				oldVal.add("%LMSVAR003%");
				oldVal.add("%LMSVAR004%");
				oldVal.add("%LMSVAR005%");
				oldVal.add("%LMSVAR006%");
				oldVal.add("%LMSVAR007%");

				Map<String, String> caseLvlMap = codeService
						.findByCodeType("lms1205m01_caseLvl");
				if (caseLvlMap == null)
					caseLvlMap = new LinkedHashMap<String, String>();
				// caseLvlVal: 1-個案討論 2-彙總討論
				newVal.add("("
						+ Util.nullToSpace(caseLvlMap.get(Util
								.nullToSpace(model.getCaseLvl())))
						+ (Util.isNotEmpty(caseLvlStr) ? ("-" + caseLvlStr)
								: "") + ")");
				newVal.add("敘做分行："
						+ branch.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
				newVal.add("案號："
						+ Util.toSemiCharString(Util.nullToSpace(model
								.getCaseNo())));
				newVal.add("借款人：" + Util.nullToSpace(model.getCustName()));
				newVal.add(this.getRptTitle(model) + "會議決議");
				newVal.add(lmsService.getLMSDoc9MainData(l120m01h, type));
				if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
					newVal.add("經辦　　　　　襄理　　　　　副處長　　　　　　　處長　　　　　　　　　");
				} else if (UtilConstants.BankNo.國外部.equals(user.getUnitNo())
						|| UtilConstants.BankNo.國金部.equals(user.getUnitNo())
						|| UtilConstants.BankNo.金控總部分行.equals(user.getUnitNo())
						|| UtilConstants.BankNo.私銀處作業組.equals(user.getUnitNo())) {
					newVal.add("<br/>　　　敬會　授信審查處<br/><br/>經辦　　　　　乙級　　　　　甲級　　　　　　　單位主管　　　　　　　　　");
				} else {
					newVal.add("經辦　　　　　襄理　　　　　副營運長　　　　　　　營運長　　　　　　　　　");
				}
			} else if (Util.equals(type, "A") || Util.equals(type, "B")
					|| Util.equals(type, "C")) { // ABC表
				oldVal.add("%LMSVAR001%");
				oldVal.add("%LMSVAR002%");

				newVal.add(lmsService.getLMSDoc9MainData(l120m01h, type));
				newVal.add(this.getRptTitle(model) + "會議決議：");

				if (Util.equals(type, "A")) {
					oldVal.add("%LMSVAR003%");
					newVal.add(branch.getBranchName(Util.nullToSpace(model
							.getCaseBrId())));
				}
			}
			// 替換資料
			replaceStrB(sbWord, newVal, oldVal);
			// 完成處理表頭資料

			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "BIG5");
			outWriter.write(sbWord.toString());
			outWriter.close();
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	private String getLMSDoc9MainData(L120M01H l120m01h, String type) {
		StringBuffer str = new StringBuffer();
		String[] chkCols = new String[] {};
		boolean isLevel1 = true; // true: 一、 false:(一)
		String font = "標楷體"; // 預設字體

		if (Util.equals(type, "Z")) { // 決議
			// 案由、負面意見、同意理由、擬辦、另囑、核准額度較前准增/減金額、授權等級
			chkCols = new String[] { "gist", "negOpinion", "consent",
					"dispWord", "another", "quotaDesrc", "authLvlStr" };
			font = "新細明體";
		} else if (Util.equals(type, "A") || Util.equals(type, "B")
				|| Util.equals(type, "C")) { // ABC表
			// 負面意見、同意理由、擬辦
			chkCols = new String[] { "negOpinion", "consent", "dispWord" };
			isLevel1 = false;
		}

		Map<String, String> datas = new HashMap<String, String>();
		datas.put("gist",
				Util.isEmpty(Util.nullToSpace(l120m01h.getGist())) ? ""
						: l120m01h.getGist()); // 案由
		datas.put("negOpinion", Util.isEmpty(Util.nullToSpace(l120m01h
				.getNegOpinion())) ? "" : l120m01h.getNegOpinion()); // 負面意見
		datas.put("consent", Util.isEmpty(Util.nullToSpace(l120m01h
				.getConsent())) ? "" : l120m01h.getConsent()); // 同意理由
		datas.put("dispWord", Util.isEmpty(Util.nullToSpace(l120m01h
				.getDispWord())) ? "" : l120m01h.getDispWord()); // 擬辦
		datas.put("another", Util.isEmpty(Util.nullToSpace(l120m01h
				.getAnother())) ? "" : l120m01h.getAnother()); // 另囑
		datas.put("quotaDesrc", Util.isEmpty(Util.nullToSpace(l120m01h
				.getQuotaDesrc())) ? "" : l120m01h.getQuotaDesrc()); // 核准額度較前准增/減金額
		datas.put("authLvlStr", Util.isEmpty(Util.nullToSpace(l120m01h
				.getAuthLvlStr())) ? "" : l120m01h.getAuthLvlStr()); // 授權等級

		String[] seqArr = new String[] { "(一", "(二", "(三", "(四", "(五", "(六",
				"(七", "(八", "(九", "(十" };
		String[] seqArr2 = new String[] { "\\(一", "\\(二", "\\(三", "\\(四",
				"\\(五", "\\(六", "\\(七", "\\(八", "\\(九", "\\(十" };
		List<String> seqList = new ArrayList<String>(Arrays.asList(seqArr2));
		boolean hasSeq = false;
		for (String col : chkCols) {
			if (datas.containsKey(col)) {
				String data = datas.get(col);
				if (Util.isNotEmpty(data)) {
					for (String seq : seqArr) {
						if (data.indexOf(seq) != -1) {
							hasSeq = true;
							break;
						}
					}
				}
			}
			if (hasSeq) {
				break;
			}
		}

		int i = 1;
		for (String col : chkCols) {
			if (datas.containsKey(col)) {
				boolean thisHasSeq = false;
				String data = datas.get(col);
				if (Util.isNotEmpty(data)) {
					for (String seq : seqArr) {
						if (data.indexOf(seq) != -1) {
							thisHasSeq = true;
							break;
						}
					}
					str.append("<tr><td valign='top' style='font-family:"
							+ font + ";'>");
					str.append(isLevel1 ? "" : "(")
							.append(NumConverter.numberToChinese(i))
							.append(isLevel1 ? "、" : ")");
					str.append("</td><td")
							.append(hasSeq ? " colspan='2' " : " ")
							.append(" style='font-family:" + font + ";'>");
					if (thisHasSeq) {
						data = data.replace("\n", "<br/>");
						Pattern pattern = Pattern.compile(StringUtils.join(
								seqList, "|"));
						Matcher matcher = pattern.matcher(data);
						boolean matchFound = matcher.find();
						while (matchFound) {
							str.append(data.substring(0, matcher.start()));
							data = data.substring(matcher.start());
							matcher = pattern.matcher(data);
							matchFound = matcher.find();
							if (matchFound) {
								str.append("</td></tr><tr><td></td><td valign='top' style='font-family:"
										+ font + ";'>");
								int endPoint = data.indexOf(")");
								String secTitle = "";
								if (endPoint > 0
										&& (endPoint + 1 <= data.length())) {
									secTitle = data.substring(matcher.start(),
											endPoint + 1);
								} else {
									secTitle = data.substring(matcher.start());
								}
								Double secTitleNum = NumConverter
										.chineseToNumber(secTitle.substring(1,
												secTitle.length() - 1));
								str.append(isLevel1 ? secTitle : (secTitleNum
										.intValue() + "."));
								str.append("</td><td>");
								if (endPoint + 1 <= data.length()) {
									data = data.substring(endPoint + 1);
								} else {
									data = data.substring(matcher.end());
								}
								matcher = pattern.matcher(data);
								matchFound = matcher.find();
							}
						}
						str.append(data);
						str.append("</td></tr>");
					} else {
						str.append(data.replace("\n", "<br/>")).append(
								"</td></tr>");
					}

					i++;
				}
			}
		}
		if (str.length() > 0) {
			str.insert(0, "<table border='0' style='font-family:" + font
					+ ";'><tbody>");
			str.append("</tbody></table>");
		}

		return str.toString();
	}

	private String getRptTitle(L120M01A model) {
		String rptTitle = "年 月 日第 次授信審議委員會";

		if (model != null) {
			if (!Util.isEmpty(Util.nullToSpace(model.getRptTitle1()))) {
				String tmpStr = model.getRptTitle1();
				rptTitle = tmpStr; // 預設為DB值

				// 再做民國轉西元年 參照 formatRptTitle1(
				int yearIndex = tmpStr.indexOf("年");
				if (yearIndex == 3) {
					String printAdYear = Util
							.trim(lmsService
									.getSysParamDataValue("LMS_RPTTITLE1_PRINT_AD_YEAR"));
					if (Util.equals(printAdYear, "Y")) {
						rptTitle = (Util.parseInt(Util.getLeftStr(tmpStr, 3)) + 1911)
								+ Util.getRightStr(tmpStr, tmpStr.length() - 3);
					}
				}
			}
		}

		return rptTitle;
	}

	/**
	 * 取得敘作條件異動比較表
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	public OutputStream getWord07A(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// String type = params.getString("type","");
		// String mainId = params.getString("mainId","");
		String[] oids = params.getStringArray("oids");

		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		try {
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);

			// 分成五區塊(依序)：文件表頭、內容表頭、內容、內容結尾、文件結尾

			String bodyStr = "<div class=Section1"; // 找尋資料區塊開始字串
			String bodyEnd = "</body>"; // 找尋資料區塊結束字串
			int bodyBgnIndex = sbWord.indexOf(bodyStr, 0); // 資料區塊開始位置
			int bodyEndIndex = sbWord.indexOf(bodyEnd, bodyBgnIndex); // 資料區塊結束位置

			StringBuilder fileHeader = new StringBuilder(); // 表頭區塊
			fileHeader.append(sbWord.substring(0, bodyBgnIndex));
			StringBuilder fileFooter = new StringBuilder(); // 表尾區塊
			fileFooter.append(sbWord.substring(bodyEndIndex));

			String subStr = "<tr class='dataHtml'"; // 找尋List Table資料區塊開始字串
			String subEnd = "</table>"; // 找尋List Table資料區塊結束字串
			int tableBgnIndex = sbWord.indexOf(subStr, 0); // List Table開始位置
			int tableEndIndex = sbWord.indexOf(subEnd, tableBgnIndex); // List
																		// Table結束位置

			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(sbWord.substring(bodyBgnIndex, tableBgnIndex));
			StringBuilder sbData = new StringBuilder();
			sbData.append(sbWord.substring(tableBgnIndex, tableEndIndex));
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(sbWord.substring(tableEndIndex, bodyEndIndex));

			StringBuilder sbCase = new StringBuilder();
			int count = 0;
			List<L140M01A> l140m01aList = service1401.findL140m01aByOids(oids);
			for (L140M01A l140m01a : l140m01aList) {
				count++;
				List<String> oldVal = new ArrayList<String>();
				List<String> newVal = new ArrayList<String>();

				// 開始替換資料(表頭)
				StringBuilder listHeader = new StringBuilder();
				listHeader.append(sbHeader);
				oldVal.add("%LMSVAR001%");
				newVal.add("敘作條件異動比較表");
				oldVal.add("%LMSVAR005%");
				newVal.add(Util.trim(l140m01a.getCntrNo()));
				// 替換表頭
				replaceStrB(listHeader, newVal, oldVal);
				// 完成處理表頭資料

				// 開始處理明細資料區塊
				// 將資料區塊清空以便開始Append
				oldVal.clear();
				oldVal.add("%LMSREP001%");
				oldVal.add("%LMSREP002%");
				oldVal.add("%LMSREP003%");
				oldVal.add("%LMSREP004%");
				StringBuilder tempData = new StringBuilder();
				StringBuilder listData = new StringBuilder();

				String mainId = Util.trim(l140m01a.getMainId());
				if (!Util.isEmpty(mainId)) {
					List<L140S07A> list = lmsService
							.findL140s07aByMainId(mainId);
					for (L140S07A l140s07a : list) {
						tempData.setLength(0);
						newVal.clear();
						tempData.append(sbData);
						newVal.add(l140s07a.getItem());
						newVal.add(l140s07a.getBefText());
						newVal.add(l140s07a.getAftText());
						newVal.add("");
						replaceStrB(tempData, newVal, oldVal);
						listData.append(tempData);
					}
				}
				sbCase.append(listHeader).append(listData).append(sbFooter);
				if (count < l140m01aList.size()) {
					sbCase.append(wordPage);
				}
			}

			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord.setLength(0);
			sbWord.append(fileHeader).append(sbCase).append(fileFooter);

			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "BIG5");
			outWriter.write(sbWord.toString());
			outWriter.close();
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (MissingResourceException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 取得敘作條件異動比較表
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	public OutputStream getWord11A(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得簽報書mainId
		String mainId = Util.nullToSpace(params.getString("mainId"));

		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		try {
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);

			// 分成五區塊(依序)：文件表頭、內容表頭、內容、內容結尾、文件結尾

			String bodyStr = "<div class=Section1"; // 找尋資料區塊開始字串
			String bodyEnd = "</body>"; // 找尋資料區塊結束字串
			int bodyBgnIndex = sbWord.indexOf(bodyStr, 0); // 資料區塊開始位置
			int bodyEndIndex = sbWord.indexOf(bodyEnd, bodyBgnIndex); // 資料區塊結束位置

			StringBuilder fileHeader = new StringBuilder(); // 表頭區塊
			fileHeader.append(sbWord.substring(0, bodyBgnIndex));
			StringBuilder fileFooter = new StringBuilder(); // 表尾區塊
			fileFooter.append(sbWord.substring(bodyEndIndex));

			String subStr = "<span class='dataHtml'"; // 找尋List Table資料區塊開始字串
			String subEnd = "</span>"; // 找尋List Table資料區塊結束字串
			int tableBgnIndex = sbWord.indexOf(subStr, 0); // List Table開始位置
			int tableEndIndex = sbWord.indexOf(subEnd, tableBgnIndex); // List
			// Table結束位置

			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(sbWord.substring(bodyBgnIndex, tableBgnIndex));
			StringBuilder sbData = new StringBuilder();
			sbData.append(sbWord.substring(tableBgnIndex, tableEndIndex));
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(sbWord.substring(tableEndIndex, bodyEndIndex));

			StringBuilder sbCase = new StringBuilder();
			int count = 0;

			List<Map<String, Object>> list = null;
			if (Util.isNotEmpty(mainId)) {
				list = eloandbBASEService.listLMSDoc11A(mainId);
			}

			Map<String, Object> custCountMap = new LinkedHashMap<String, Object>();
			if (list != null && !list.isEmpty()) {
				for (Map<String, Object> map : list) {
					BigDecimal seq = BigDecimal.valueOf(MapUtils.getInteger(
							map, "SEQ", 1));
					String l140m01a_custId = Util.nullToSpace(MapUtils
							.getString(map, "CUSTID", ""));

					custCountMap.put(l140m01a_custId, seq);
				}
			}

			if (list != null && !list.isEmpty()) {
				String tempCustId = null;
				BigDecimal maxSeq = null;
				int custCount = 0;
				List<L140S11A> alls11aList = new ArrayList<L140S11A>();
				Map<String, Object[]> cntrNoMap = new LinkedHashMap<String, Object[]>();
				for (Map<String, Object> map : list) {
					BigDecimal seq = BigDecimal.valueOf(MapUtils.getInteger(
							map, "SEQ", 1));
					String l140m01a_oid = Util.nullToSpace(MapUtils.getString(
							map, "OID", ""));
					String l140m01a_mainId = Util.nullToSpace(MapUtils
							.getString(map, "MAINID", ""));
					String l140m01a_custId = Util.nullToSpace(MapUtils
							.getString(map, "CUSTID", ""));
					String l140m01a_custName = Util.nullToSpace(MapUtils
							.getString(map, "CUSTNAME", ""));

					if (seq.compareTo(BigDecimal.ONE) == 0) {
						if (Util.notEquals(tempCustId, l140m01a_custId)) {
							tempCustId = l140m01a_custId;
							if (custCountMap != null && !custCountMap.isEmpty()) {
								if (custCountMap.containsKey(l140m01a_custId)) {
									maxSeq = BigDecimal.valueOf(MapUtils
											.getInteger(custCountMap,
													l140m01a_custId, 1));
								}
							}
							custCount = 0;
							alls11aList = new ArrayList<L140S11A>();
						}
					}

					if (Util.isNotEmpty(l140m01a_mainId)) {
						L140M01A l140m01a = service1401
								.findL140m01aByMainId(l140m01a_mainId);
						if (l140m01a != null) {
							custCount++;
							boolean check = true;
							if (Util.equals(l140m01a.getProPerty(),
									UtilConstants.Cntrdoc.Property.不變)) {
								check = false;
							}

							List<L140S11A> l140s11aList = lmsService
									.findL140s11aListByMainId(l140m01a_mainId);
							if (l140s11aList == null || l140s11aList.isEmpty()) {
								check = false;
							}

							if(check) {
								alls11aList.addAll(l140s11aList);
								Object[] cntrNoData = {
										Util.nullToSpace(l140m01a.getCntrNo()),
										l140s11aList.size()};
								cntrNoMap.put(l140m01a_mainId, cntrNoData);

								count++;
							}

							if (BigDecimal.valueOf(custCount).compareTo(maxSeq) == 0) {
								if(sbCase.length() > 0){
									sbCase.append(wordPage);
								}
								List<String> oldVal = new ArrayList<String>();
								List<String> newVal = new ArrayList<String>();

								// 開始替換資料(表頭)
								StringBuilder listHeader = new StringBuilder();
								listHeader.append(sbHeader);
								oldVal.add("%LMSVAR001%");
								newVal.add(l140m01a_custName + "敘做條件異動比較表");
								// 替換表頭
								replaceStrB(listHeader, newVal, oldVal);
								// 完成處理表頭資料

								// 開始處理明細資料區塊
								// 將資料區塊清空以便開始Append
								oldVal.clear();
								oldVal.add("%LMSREP001%");
								StringBuilder tempData = new StringBuilder();
								StringBuilder listData = new StringBuilder();

								if (alls11aList != null
										&& !alls11aList.isEmpty()) {
									String s11aTableStr = lmsService
											.getL140s11aStrForDoc(alls11aList,
													cntrNoMap);

									tempData.setLength(0);
									newVal.clear();
									tempData.append(sbData);
									newVal.add(s11aTableStr);
									replaceStrB(tempData, newVal, oldVal);
									listData.append(tempData);
								}

								sbCase.append(listHeader).append(listData)
										.append(sbFooter);
							}
						}
					}
				}
			}

			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord.setLength(0);
			sbWord.append(fileHeader).append(sbCase).append(fileFooter);

			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "BIG5");
			outWriter.write(sbWord.toString());
			outWriter.close();
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (MissingResourceException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
	public String saveToFile(PageParameters params, byte[] saveFile) {
		String errorMsg = "";
		String docTempType = params.getString("docTempType");
		String mainId = Util.trim(params.getString("mainId"));
		String fieldId = Util.trim(params.getString("fieldId"));
		String fileDownloadName = Util.trim(params
				.getString("fileDownloadName"));

		L120M01A model = service1201.findL120m01aByMainId(mainId);
		if (model == null) {
			model = new L120M01A();
		}

		// 直接儲存
		if ("LMSDoc22".equals(docTempType)) {
			DocFile docFile = new DocFile();
			docFile.setBranchId(model.getCaseBrId());
			docFile.setContentType("application/msword");
			docFile.setMainId(mainId);
			docFile.setPid(null);
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId(fieldId);
			docFile.setDeletedTime(null);
			docFile.setSrcFileName(fileDownloadName);
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId(docFileService.getSysId());
			// docFile.setFileSize(bytes1.length);
			docFile.setFileDesc("額度檢視表");
			docFile.setData(saveFile);
			docFileService.save(docFile, true);
		} else if ("LMSDoc23".equals(docTempType)) {
			DocFile docFile = new DocFile();
			docFile.setBranchId(model.getCaseBrId());
			docFile.setContentType("application/msword");
			docFile.setMainId(mainId);
			docFile.setPid(null);
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId(fieldId);
			docFile.setDeletedTime(null);
			docFile.setSrcFileName(fileDownloadName);
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId(docFileService.getSysId());
			// docFile.setFileSize(bytes1.length);
			docFile.setFileDesc("授信額度主要敘做條件彙總表");
			docFile.setData(saveFile);
			docFileService.save(docFile, true);
		} else if ("LMSDoc11A".equals(docTempType)) {
			// J-112-0357 新增敘做條件異動比較表
			DocFile docFile = new DocFile();
			docFile.setBranchId(model.getCaseBrId());
			docFile.setContentType("application/msword");
			docFile.setMainId(mainId);
			docFile.setPid(null);
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId(fieldId);
			docFile.setDeletedTime(null);
			docFile.setSrcFileName(fileDownloadName);
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId(docFileService.getSysId());
			docFile.setFileDesc("敘做條件異動比較表");
			docFile.setData(saveFile);
			docFileService.save(docFile, true);
		} else if ("LMSDoc24".equals(docTempType)) {
			DocFile docFile = new DocFile();
			docFile.setBranchId(model.getCaseBrId());
			docFile.setContentType("application/msword");
			docFile.setMainId(mainId);
			docFile.setPid(null);
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId(fieldId);
			docFile.setDeletedTime(null);
			docFile.setSrcFileName(fileDownloadName);
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId(docFileService.getSysId());
			// docFile.setFileSize(bytes1.length);
			docFile.setFileDesc("董事會/常董會討論事項提案檢核表");
			docFile.setData(saveFile);
			docFileService.save(docFile, true);
		}
		return errorMsg;
	}

	/**
	 * 授信額度檢視表
	 * 
	 * J-110-0386_05097_B1001 Web e-Loan國內與海外企金授信「相關文件」頁籤新增「授信額度主要敘做條件彙總表」
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord23a(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得簽報書mainId
		String mainId = params.getString("mainId");

		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;

		Map<String, String> proPertyMap = codeService.findByCodeType(
				"lms1405s02_proPerty", LMSUtil.getLocale().toString());
		DecimalFormat df = new DecimalFormat("###,###,###,###,###,###,###,##0");

		try {
			// FileUtils.readFileToString(file, "UTF-8");
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			// String str = FileUtils.readFileToString(file, "UTF-8");
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			String bodStr1 = "<div class=WordSection1";

			// 找尋資料區塊開始字串
			String bodStr2 = "<tr class='dataHtml'";

			// 找尋資料區塊結束字串

			String dataEnd = "</table>";

			String bodEnd = "</body>";

			// 資料區塊1開始位置
			int strBod1 = sbWord.indexOf(bodStr1, 0);
			// 資料區塊2開始位置
			int strBod2 = sbWord.indexOf(bodStr2, strBod1);

			// 資料區塊2結束位置
			int endData = sbWord.indexOf(dataEnd, strBod2);

			// 資料區塊2結束位置
			int endBod = sbWord.indexOf(bodEnd, endData);

			// 表頭區塊
			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(sbWord.substring(0, strBod1));

			// 資料區塊1
			StringBuilder sbData1 = new StringBuilder();
			sbData1.append(sbWord.substring(strBod1, strBod2));

			// 資料區塊2
			StringBuilder sbData2 = new StringBuilder();
			sbData2.append(sbWord.substring(strBod2, endData));

			// 資料區塊4
			StringBuilder sbData4 = new StringBuilder();
			sbData4.append(sbWord.substring(endData, endBod));

			StringBuilder sbDataFinal = new StringBuilder();

			// 表尾區塊
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(sbWord.substring(endBod));

			// 稽核資料是否都順利取得
			if (sbHeader == null || sbData1 == null || sbData2 == null
					|| sbFooter == null) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1205V01Page.class);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", pop.getProperty("l120v01.error1")),
						getClass());
			}

			L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
			// 取得額度明細表
			List<L140M01A> list = service1401.findL140m01aListByL120m01cMainId(
					l120m01a.getMainId(), "1");

			List<String> oldVal1 = new ArrayList<String>();
			List<String> newVal1 = new ArrayList<String>();

			oldVal1.add("%LMSVAR001%");

			StringBuilder oldData1 = new StringBuilder();
			oldData1.append(sbData1);

			StringBuilder oldData2 = new StringBuilder();
			oldData2.append(sbData2);

			// 將資料區塊清空以便開始Append
			StringBuilder tempData1 = new StringBuilder();
			StringBuilder tempData2 = new StringBuilder();
			StringBuilder tempData3 = new StringBuilder();

			int count = 0;
			docName = "LMSDoc23";

			String gCustId = "";
			boolean isNewPage = false;

			List<String> oldVal2 = new ArrayList<String>();
			List<String> newVal2 = new ArrayList<String>();

			// 授審會範本有四個資料變數
			oldVal2.add("%LMSREP001%");
			oldVal2.add("%LMSREP002%");
			oldVal2.add("%LMSREP003%");
			oldVal2.add("%LMSREP004%");
			oldVal2.add("%LMSREP005%");
			oldVal2.add("%LMSREP006%");

			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord.setLength(0);

			int allCount = 0;
			gCustId = "";

			List<L120S16A> l120s16as = l120s16aDao.findByMainId(mainId);

			for (L120S16A l120s16a : l120s16as) {
				String custId = Util.trim(l120s16a.getCustId());
				String dupNo = Util.trim(l120s16a.getDupNo());

				if (Util.notEquals(gCustId, custId)) {
					gCustId = custId;
					allCount = allCount + 1;
				} else {
					continue;
				}
			}

			// 開始替換資料
			gCustId = "";
			for (L120S16A l120s16a : l120s16as) {

				String custId = Util.trim(l120s16a.getCustId());
				String dupNo = Util.trim(l120s16a.getDupNo());

				if (Util.notEquals(gCustId, custId)) {
					gCustId = custId;
					count = count + 1;
				} else {
					continue;
				}

				sbData1.setLength(0);
				sbData2.setLength(0);

				// 清除要替換的資料內容

				// 將暫存內容初始化
				tempData1.setLength(0);
				// 將保留區塊移到暫存
				tempData1.append(oldData1);
				// 清除要替換的資料內容

				L140M01A l140m01a = service1401
						.findL140m01aByCaseMainIdCustIdCntrNoItemType(
								l120m01a.getMainId(), custId, dupNo,
								l120s16a.getCntrNo(), "1");

				newVal1.clear();
				newVal1.add(Util.trim(l140m01a.getCustName()));

				// 替換表頭
				replaceStrB(tempData1, newVal1, oldVal1);

				sbData1.append(tempData1);

				// 完成處理表頭資料

				// 開始處理明細資料區塊
				// 處理sbData 資料區塊
				List<L120S16A> listl120s16a = l120s16aDao.findByIndex01(
						l120m01a.getMainId(), custId, dupNo, null);

				StringBuffer memo = new StringBuffer("");
				if (listl120s16a != null && !listl120s16a.isEmpty()) {

					L140M01A totalL140m01a = null;
					for (L120S16A tl120s16a : listl120s16a) {

						// 將暫存內容初始化
						tempData2.setLength(0);
						// 將保留區塊移到暫存
						tempData2.append(oldData2);
						// 清除要替換的資料內容
						newVal2.clear();

						newVal2.add(tl120s16a.getLnSubject().replace("\n",
								"<br/>"));

						L120S16B l120s16b_2 = service1201
								.findL120s16bByUniqueKey(
										mainId,
										"1",
										tl120s16a.getCustId(),
										tl120s16a.getDupNo(),
										tl120s16a.getCntrNo(),
										UtilConstants.Casedoc.L120s16bItemType.現請額度);

						if (l120s16b_2 != null) {
							// J-111-0466 [2022/12/26 下午 04:33] 李易穎(國際金融業務分行,專員)
							// 了解~就程式是用"("為判斷
							String itemDscr6 = Util.trim(
									l120s16b_2.getItemDscr()).replace("\n",
									"<br/>");
							int index = StringUtils.indexOf(itemDscr6, "(");
							if (index == 0) {
								itemDscr6 = "仟元" + itemDscr6;
							} else if (index > 0) {
								itemDscr6 = StringUtils.substring(itemDscr6, 0,
										index)
										+ "仟元"
										+ StringUtils.substring(itemDscr6,
												index);
							}
							newVal2.add(itemDscr6);
						} else {
							newVal2.add("");
						}

						// J-111-0466 "授信額度主要條件彙總表": 調整表頭、部分欄位名稱/內容/位置
						/*
						 * 性質別為 新做，顯示空白 若該額度序號無L201資料(LNF020_DURATION_BG,
						 * LNF020_DURATION_ED)，顯示空白； 若有L201資料： 性質別為
						 * 不變、取消，帶入"L201 授信期間" 其餘性質別，帶入"L201 授信期間" 並顯示警語
						 * "請再次確認授信期間"
						 */
						if (LMSUtil.isContainValue(
								Util.trim(tl120s16a.getProperty()),
								UtilConstants.Cntrdoc.Property.新做)) {
							newVal2.add("");
						} else {
							List<Map<String, Object>> lnf020List = misdbBASEService
									.findLnf020(Util.trim(tl120s16a.getCntrNo()));
							if (lnf020List != null && !lnf020List.isEmpty()
									&& lnf020List.size() > 0) {
								boolean cancelFlag = false;
								String LNF020_DURATION_BG = "";
								String LNF020_DURATION_ED = "";
								for (Map<String, Object> lnf020Map : lnf020List) {
									String LNF020_CANCEL_DATE = Util
											.trim(MapUtils.getString(lnf020Map,
													"LNF020_CANCEL_DATE"));
									if (Util.notEquals(LNF020_CANCEL_DATE, "")
											&& Util.notEquals(
													LNF020_CANCEL_DATE,
													"0001-01-01")) {
										cancelFlag = true;
										break;
									} else {
										LNF020_DURATION_BG = Util.trim(MapUtils
												.getString(lnf020Map,
														"LNF020_DURATION_BG"));
										LNF020_DURATION_ED = Util.trim(MapUtils
												.getString(lnf020Map,
														"LNF020_DURATION_ED"));
									}
								}
								if (cancelFlag) {
									newVal2.add("");
								} else {
									StringBuffer PayDeadlineSb = new StringBuffer(
											"");
									PayDeadlineSb
											.append(Util.nullToSpace(CapDate
													.formatDateFromF1ToF2(
															LNF020_DURATION_BG,
															"yyyy-MM-dd",
															"yyyy/MM/dd")))
											.append("~")
											.append(Util.nullToSpace(CapDate
													.formatDateFromF1ToF2(
															LNF020_DURATION_ED,
															"yyyy-MM-dd",
															"yyyy/MM/dd")));
									if (LMSUtil.isContainValue(
											Util.trim(tl120s16a.getProperty()),
											UtilConstants.Cntrdoc.Property.不變)
											|| LMSUtil
													.isContainValue(
															Util.trim(tl120s16a
																	.getProperty()),
															UtilConstants.Cntrdoc.Property.取消)) {

									} else {
										PayDeadlineSb.append("<br/>").append(
												"請再次確認授信期間");
									}
									newVal2.add(PayDeadlineSb.toString());
								}
							} else {
								newVal2.add("");
							}
						}

						// 說明(從L120S16A) 「六、主要申請敘做條件」之(一)主要敘做條件維護功能,
						// 增加額度檢視表所需「說明」欄位
						L120S16B l120s16b_6 = service1201
								.findL120s16bByUniqueKey(
										mainId,
										"1",
										tl120s16a.getCustId(),
										tl120s16a.getDupNo(),
										tl120s16a.getCntrNo(),
										UtilConstants.Casedoc.L120s16bItemType.利_費_率);
						if (l120s16b_6 != null) {
							newVal2.add(Util.trim(l120s16b_6.getItemDscr())
									.replace("\n", "<br/>"));
						} else {
							newVal2.add("");
						}

						newVal2.add(Util.trim(tl120s16a.getCollateral())
								.replace("\n", "<br/>"));

						newVal2.add(Util.trim(tl120s16a.getGuarantor())
								.replace("\n", "<br/>"));

						// 替換資料
						replaceStrB(tempData2, newVal2, oldVal2);
						// 完成處理表頭資料

						// 將修改過得資料存進去
						sbData2.append(tempData2);

					}

				}

				sbDataFinal.append(sbData1).append(sbData2).append(sbData4);

				if (count < allCount) {
					sbDataFinal.append(wordPage);
				}

			}

			sbWord.append(sbHeader).append(sbDataFinal).append(sbFooter);

			// OutputStreamWriter outWriter = new OutputStreamWriter(baos,
			// "UTF-8");
			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "BIG5");
			outWriter.write(sbWord.toString());
			outWriter.close();
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 將產生的檔案FROM CES 直接儲存到DOCFILE，不要回傳回前端 J-110-0368_11557_B1001
	 * 常董會/董事會提案稿所需之相關文件
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveCreatDocFromCES(PageParameters params) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String docTempType = Util.trim(params.getString("docTempType"));// 1為跟CES要1+2，3為跟CES要3，4為跟CES要4
		String errorMessage = "";// 收集錯誤訊息，某一筆噴錯還會繼續往下跑其他筆

		// From CES是拿到檔案路徑->file實體，所以走自己一套
		List<L120M01E> l120m01eList = null;
		if ("1and2".equals(docTempType)) {
			l120m01eList = service1201.findL120M01EByByMainIdAndDocType(mainId,
					"1");// 1.徵信報告書.mainId->丟給CES
			for (L120M01E l120m01e : l120m01eList) {
				// 1.提供特定徵信報告書之綜合評述, WORD檔或PDF檔(依原始資料來源)
				try {
					File saveFile1 = this.getCESForLMS(l120m01e.getDocOid(),
							"1");
					this.saveToFileFromCES(mainId, "1", saveFile1);
				} catch (CapMessageException e) {
					LOGGER.error(e.getMessage(), getClass());
					errorMessage = concatErrorMessage(errorMessage,
							e.getMessage(), l120m01e, "1");
				}

				// 2.提供特定徵信報告書之財務報表(4報表, 合併+個體 8報表)
				try {
					File saveFile2 = this.getCESForLMS(l120m01e.getDocOid(),
							"2");
					this.saveToFileFromCES(mainId, "2", saveFile2);
				} catch (CapMessageException e) {
					LOGGER.error(e.getMessage(), getClass());
					errorMessage = concatErrorMessage(errorMessage,
							e.getMessage(), l120m01e, "2");
				}
			}
		} else if ("3".equals(docTempType)) {
			l120m01eList = service1201.findL120M01EByByMainIdAndDocType(mainId,
					"2");// 2.資信簡表.mainId->丟給CES
			for (L120M01E l120m01e : l120m01eList) {
				// 3.提供特定對象最新資信簡表之全套資料(資信簡表、營運情形、金融機構存借款往來、外匯往來實績、授信往來情形)
				try {
					File saveFile3 = this.getCESForLMS(l120m01e.getDocOid(),
							"3");
					this.saveToFileFromCES(mainId, "3", saveFile3);
				} catch (CapMessageException e) {
					LOGGER.error(e.getMessage(), getClass());
					errorMessage = concatErrorMessage(errorMessage,
							e.getMessage(), l120m01e, "3");
				}
			}
			
			//J-113-0056_12473_B1001  新增「常董會附件-借戶/連保人/AR債務人/設質股票公司之資信簡表」的產生機制
			if(l120m01eList == null || l120m01eList.isEmpty()){
				errorMessage = this.getCESDataByMainIdAndDocType1(mainId, "3", errorMessage);
			}
			
		} else if ("4".equals(docTempType)) {
			l120m01eList = service1201.findL120M01EByByMainIdAndDocType(mainId,
					"2");// 2.資信簡表.mainId->丟給CES
			for (L120M01E l120m01e : l120m01eList) {
				// 4.提供特定對象最新資信簡表之半套資料(資信簡表、營運情形)
				try {
					File saveFile4 = this.getCESForLMS(l120m01e.getDocOid(),
							"4");
					this.saveToFileFromCES(mainId, "4", saveFile4);
				} catch (CapMessageException e) {
					LOGGER.error(e.getMessage(), getClass());
					errorMessage = concatErrorMessage(errorMessage,
							e.getMessage(), l120m01e, "4");
				}
			}
			
			//J-113-0056_12473_B1001  新增「常董會附件-借戶/連保人/AR債務人/設質股票公司之資信簡表」的產生機制
			if(l120m01eList == null || l120m01eList.isEmpty()){
				errorMessage = this.getCESDataByMainIdAndDocType1(mainId, "4", errorMessage);
			}
			
		}

		if (Util.isEmpty(errorMessage)) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
			result.set("errorMessage", "");
		} else {
			result.set("errorMessage", errorMessage);
		}
		return result;
	}

	private String concatErrorMessage(String errorMessage, String thisError,
			L120M01E l120m01e, String type) {
		// ex:2015-04-24 2015北二區境外字第00392號 茂CNZ0012499，取得常董會附件失敗，找不到檔案。
		if (thisError.contains(fromCESFileError)) {
			String fileDesc = "";

			if ("1".equals(type)) {
				fileDesc = "徵信報告書之綜合評述";
			} else if ("2".equals(type)) {
				fileDesc = "徵信報告書之財務報表";
			} else if ("3".equals(type)) {
				fileDesc = "資信簡表之全套資料";
			} else if ("4".equals(type)) {
				fileDesc = "資信簡表之半套資料";
			}
			thisError = thisError.replace(fromCESFileError, fromCESFileError
					+ "(" + fileDesc + ")");
		}

		errorMessage = errorMessage + "<BR/>" + l120m01e.getDocDscr() + "，"
				+ thisError;
		return errorMessage;
	}
	
	/**
	 * J-113-0056_12473_B1001  新增「常董會附件-借戶/連保人/AR債務人/設質股票公司之資信簡表」的產生機制，只有徵信報告時取得隸屬資信簡表來產出
	 */
	private String getCESDataByMainIdAndDocType1(String mainId, String type, String errorMessage){
		List<L120M01E> l120m01eDocType1List = service1201.findL120M01EByByMainIdAndDocType(mainId, "1"); //1.徵信報告書
		for(L120M01E l120m01e : l120m01eDocType1List){
			List<Map<String, Object>> tmpMapList = eloandbBASEService.C140M01A_selCustname2(l120m01e.getDocOid());//取得徵信報告書隸屬資信簡表
			for(Map<String, Object> tmpMap : tmpMapList){
				try {
					File saveFile = this.getCESForLMS(Util.trim(tmpMap.get("C120M01A_MAINID")), type);
					this.saveToFileFromCES(mainId, type, saveFile);
				} catch (CapMessageException e) {
					LOGGER.error(e.getMessage(), getClass());
					errorMessage = concatErrorMessage(errorMessage, e.getMessage(), l120m01e, type);
				}
			}
		}
		return errorMessage;
	}

	/**
	 * J-110-0368_11557_B1001 常董會/董事會提案稿所需之相關文件
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 * @throws FileNotFoundException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public File getCESForLMS(String mainId, String type) throws CapMessageException {
		// 參考LMSCOMMONFormHandler.printCes的取url方式，不然其他取法有可能會有ip在url中，又會影響是否有在ip白名單中
		String url = sysParamService.getParamValue("HTTP_URL_CES")
				+ "/app/api/";

		String token = "";
		List<CodeType> codeTypeList = codeService
				.findByCodeTypeList("API_TOKEN");

		if (codeTypeList == null || codeTypeList.size() == 0) {
			// 未設定API_TOKEN Codetype
			throw new CapMessageException("未設定API_TOKEN Codetype,請聯絡MEGA IT。",
					getClass());
		} else {
			for (CodeType codeType : codeTypeList) {
				if (codeType.getCodeOrder() == 1) {
					// 取第一組來使用
					token = codeType.getCodeValue();
					break;
				}
			}
		}
		File file = null;

		String input = "{TOKEN:\"" + token + "\",MAINID:\"" + mainId
				+ "\",\"TYPE\":\"" + type + "\"}";
		EloanHttpGetReqMessage ehcrm = new EloanHttpGetReqMessage();
		ehcrm.setUrl(url);
		ehcrm.setServiceId("CESForLMSService");
		ehcrm.setTimeout(60);
		ehcrm.setLocalUrl(true);
		try {
			ehcrm.setRequest("INPUT=" + URLEncoder.encode(input, "utf-8"));
		} catch (UnsupportedEncodingException ex) {
			LOGGER.error("[encode error] Exception!!", ex);
			throw new CapMessageException("資料編碼錯誤，請稍後再試。", getClass());
		}

		try {
			// 有可能是httpGet那邊eloanHttpGetClient，也有可能是File出exception
			JSONObject json = eloanHttpGetClient.send(ehcrm);
			String code = "";
			String message = "";
			if (json != null) {
				code = json.getString("CODE");
				message = json.getString("MESSAGE");

				if (!"00".equals(code)) {
					// {"CODE":"07","MESSAGE":"查無該戶資信簡表或徵信報告!"}
					throw new CapMessageException(fromCESFileError + "，" + code
							+ ":" + message + "。", getClass());
				}
				// code為00(成功的才去取檔案)
				JSONObject jsonData = json.getJSONObject("DATA");
				if (jsonData != null) {
					file = new File(jsonData.getString("SUCCESS"));
				}
			}
		} catch (CapMessageException e) {
			// 如果是包裝好的CapMessageException就直接再往外拋即可
			throw e;
		} catch (Exception e) {
			LOGGER.error("[httpGet] Exception!!", e);
			throw new CapMessageException(fromCESFileError + "，請稍後再試。",
					getClass());
		}
		return file;
	}

	// J-110-0368_11557_B1001 常董會/董事會提案稿所需之相關文件
	public String saveToFileFromCES(String mainId, String docTempType, File saveFile) throws CapMessageException {
		String errorMsg = "";
		String contentType = "application/pdf";// 預設為PDF

		L120M01A model = service1201.findL120m01aByMainId(mainId);
		if (model == null) {
			model = new L120M01A();
		}

		String name = saveFile.getName();
		String fileType = name.substring(name.lastIndexOf(".") + 1)
				.toLowerCase();

		if (!"pdf".equalsIgnoreCase(fileType)
				&& !"doc".equalsIgnoreCase(fileType)
				&& !"docx".equalsIgnoreCase(fileType)) {
			throw new CapMessageException("only pdf or doc could import",
					this.getClass());
		} else if ("pdf".equalsIgnoreCase(fileType)) {
			contentType = "application/pdf";
		} else if ("docx".equalsIgnoreCase(fileType)) {
			contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
		} else if ("doc".equalsIgnoreCase(fileType)) {
			contentType = "application/word";
		}

		// file -> byte[]
		byte[] fileData = null;
		try {
			fileData = FileUtils.readFileToByteArray(saveFile);
		} catch (FileNotFoundException ex) {
			LOGGER.error("[FileNotFoundException!!]", ex);
			throw new CapMessageException(fromCESFileError + "，找不到檔案。",
					getClass());
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
			throw new CapMessageException(fromCESFileError + "，請稍後再試。",
					this.getClass());
		}

		// 直接儲存
		// 1.提供特定徵信報告書之綜合評述, WORD檔或PDF檔(依原始資料來源)
		// 2.提供特定徵信報告書之財務報表(4報表, 合併+個體 8報表)
		// 3.提供特定對象最新資信簡表之全套資料(資信簡表、營運情形、金融機構存借款往來、外匯往來實績、授信往來情形)
		// 4.提供特定對象最新資信簡表之半套資料(資信簡表、營運情形)
		DocFile docFile = new DocFile();
		docFile.setBranchId(model.getCaseBrId());
		docFile.setContentType(contentType);
		docFile.setMainId(mainId);
		docFile.setPid(null);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setDeletedTime(null);
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId(docFileService.getSysId());
		docFile.setData(fileData);

		if ("1".equals(docTempType)) {
			docFile.setFieldId("CESForLMS_1");
			docFile.setSrcFileName("CESForLMS_1" + "." + fileType);// 固定看到的檔名
			docFile.setFileDesc("徵信報告書之綜合評述");
		} else if ("2".equals(docTempType)) {
			docFile.setFieldId("CESForLMS_2");
			docFile.setSrcFileName("CESForLMS_2" + "." + fileType);// 固定看到的檔名
			docFile.setFileDesc("徵信報告書之財務報表");
		} else if ("3".equals(docTempType)) {
			docFile.setFieldId("CESForLMS_3");
			docFile.setSrcFileName("CESForLMS_3" + "." + fileType);// 固定看到的檔名
			docFile.setFileDesc("資信簡表之全套資料");
		} else if ("4".equals(docTempType)) {
			docFile.setFieldId("CESForLMS_4");
			docFile.setSrcFileName("CESForLMS_4" + "." + fileType);// 固定看到的檔名
			docFile.setFileDesc("資信簡表之半套資料");
		}

		docFileService.save(docFile, true);
		return errorMsg;
	}

	/**
	 * 董事會/常董會討論事項提案檢核表
	 * 
	 * J-112-0357_11850_B1001 新增 董事會/常董會討論事項提案檢核表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord24(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();

		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;

		try {
			// 開始讀取第一範本檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);

			// 讀取範本資料
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}
			// 存取要被取代資料內容
			StringBuilder bgData = new StringBuilder();
			bgData.append(sbWord.toString());

			// 需替換的參數
			List<String> oldVal = new ArrayList<String>();
			// 預定提報會期 => 第　XX　屆　第　XX　次
			oldVal.add("%LMSVAR001%");
			// 董事會 or 常董會
			oldVal.add("%LMSVAR002%");
			oldVal.add("%LMSVAR003%");
			// 案由摘要
			oldVal.add("%LMSVAR004%");
			// 提案單位
			oldVal.add("%LMSVAR005%");
			// 提案單位
			oldVal.add("%LMSVAR006%");
			// 替換後的參數
			List<String> newVal = new ArrayList<String>();

			// 取得oid
			String oid = params.getString("oid");
			L120M01A l120m01a = service1201.findL120m01aByOid(oid);
			String rpttitle2 = Util.isEmpty(l120m01a.getRptTitle2()) ? ""
					: l120m01a.getRptTitle2();
			String uncheck = "□";
			String ischeck = "■";
			String LMSVAR001 = "  ";
			String LMSVAR002 = "  ";
			String LMSVAR003 = uncheck;// 董事會
			String LMSVAR004 = uncheck;// 常董會
			String LMSVAR005 = "  ";// 案由摘要
			String LMSVAR006 = "  ";// 提案單位
			if (rpttitle2.length() > 0) {
				// 103年10月03日第14屆第60次常董會
				LMSVAR001 = rpttitle2.substring(rpttitle2.indexOf("第") + 1,
						rpttitle2.indexOf("屆"));// 第XX屆 的XX
				LMSVAR002 = rpttitle2.substring(
						rpttitle2.indexOf("第", rpttitle2.indexOf("第") + 1) + 1,
						rpttitle2.indexOf("次"));// 第XX次 的XX

				// 常董會or 董事會 只能擷取最後三個字來判斷
				String meetdesc = rpttitle2.substring(rpttitle2.length() - 3,
						rpttitle2.length());
				if ("董事會".equals(meetdesc)) {
					LMSVAR003 = ischeck;
				} else if ("常董會".equals(meetdesc)) {
					LMSVAR004 = ischeck;
				}
			}
			// 常董稿案由
			L120M01D l120m01dY = service1205.findL120m01dByUniqueKey(
					l120m01a.getMainId(),
					UtilConstants.Casedoc.L120m01dItemType.常董會案由);
			if (l120m01dY != null
					&& Util.isNotEmpty(Util.trim(l120m01dY.getItemDscr()))) {
				LMSVAR005 = Util.trim(l120m01dY.getItemDscr());
			}

			// 這是海外
			L120M01D l120m01dZ = service1205.findL120m01dByUniqueKey(
					l120m01a.getMainId(), Casedoc.L120m01dItemType.常董會中文案由);
			if (l120m01dZ != null
					&& Util.isNotEmpty(Util.trim(l120m01dZ.getItemDscr()))) {
				if (LMSVAR005.length() > 0) {
					LMSVAR005 += "<br> <br>";
				}
				LMSVAR005 += Util.trim(l120m01dZ.getItemDscr());
			}
			// 提案單位
			String hqMeetFlag = Util.trim(l120m01a.getHqMeetFlag());
			if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {// 提常董會

				// 如果是授管處的常董會->執行此條件
				// 一般分行與總處分行不一樣，總處分行是自行提常董會，
				// 所以為( 國金部提)，但是一般分行是( 授信管理處提＼忠孝分行承做 )
				if (LMSUtil.isSpecialBranch(Util.trim(l120m01a.getCaseBrId()))) {
					// 2021/08/13 授審 吳宜真 襄理： 國外部、國際金融業務分行及金控總部分行的常董稿 提案單位請改為
					// 授信審查處
					if (UtilConstants.BankNo.國外部.equals(Util.trim(l120m01a
							.getCaseBrId()))
							|| UtilConstants.BankNo.國金部.equals(Util
									.trim(l120m01a.getCaseBrId()))
							|| UtilConstants.BankNo.金控總部分行.equals(Util
									.trim(l120m01a.getCaseBrId()))
							|| UtilConstants.BankNo.私銀處作業組.equals(Util
									.trim(l120m01a.getCaseBrId()))) {
						LMSVAR006 = branch
								.getBranchName(UtilConstants.BankNo.授管處);
					} else {
						LMSVAR006 = branch.getBranchName(Util.trim(l120m01a
								.getCaseBrId()));
					}
				} else {
					LMSVAR006 = branch.getBranchName(UtilConstants.BankNo.授管處);
				}
			}
			newVal.add(LMSVAR001);
			newVal.add(LMSVAR002);
			newVal.add(LMSVAR003);
			newVal.add(LMSVAR004);
			newVal.add(LMSVAR005);
			newVal.add(LMSVAR006);
			// 替換
			replaceStrB(bgData, newVal, oldVal);
			// 完成處理
			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "BIG5");
			outWriter.write(bgData.toString());
			outWriter.close();

		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

}
