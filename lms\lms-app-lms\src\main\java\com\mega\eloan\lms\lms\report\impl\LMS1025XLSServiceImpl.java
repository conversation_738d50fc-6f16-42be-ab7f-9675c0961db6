package com.mega.eloan.lms.lms.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.lms.lms.report.LMS1025XLSService;
import com.mega.eloan.lms.lms.service.LMS1025Service;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.Util;

@Service("lms1025xlsservice")
public class LMS1025XLSServiceImpl implements LMS1025XLSService {

	@Resource
	LMS1025Service lms1025Service;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			String mode = Util.trim(params.getString("mode"));
			if(Util.equals("expXls", mode)){
				baos = (ByteArrayOutputStream) expXls(params);	
			}
			if(baos==null){
				return null;
			}else{
				return baos.toByteArray();	
			}			
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	private ByteArrayOutputStream expXls(PageParameters params) throws IOException, Exception {
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		if(true){						
			lms1025Service.expXls_modelCompareSrcData(outputStream);		
		}
		if(outputStream!=null){
			outputStream.flush();	
		}		
		return outputStream;
	}
}
