/* 
 * ScoreServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.MathContext;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.PropertyResourceBundle;
import java.util.Set;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.Score;
import com.mega.eloan.lms.base.constants.ScoreCardLoan;
import com.mega.eloan.lms.base.constants.ScoreNotHouseLoan;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.impl.AbstractEloandbJdbc;
import com.mega.eloan.lms.etch.service.EtchService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C101S01Q;
import com.mega.eloan.lms.model.C101S01R;
import com.mega.eloan.lms.model.C120S01E;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 評分 ServiceImpl
 * </pre>
 * 
 * @since 2012/10/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/30,Fantasy,new
 *          </ul>
 */
@Service("ScoreService")
public class ScoreServiceImpl extends AbstractEloandbJdbc implements
		ScoreService {

	private static final Logger logger = LoggerFactory
			.getLogger(ScoreServiceImpl.class);
	private static final String JSON_NULL = "";
	private HashMap<String, JSONObject> container_houseLoan = new LinkedHashMap<String, JSONObject>();
	private ScriptEngineManager scriptEngineManager = new ScriptEngineManager();

	private static final int OVERSEA_SCALE = 5;
	
	@Resource
	EjcicService ejcicService;

	@Resource
	EtchService etchService;
	
	@Resource
	DwdbBASEService dwdbBASEService;
	
	@Resource
	MisdbBASEService misdbBaseService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	CLSService clsService;
	
	@PostConstruct
	@Override
	public synchronized void init() {
		try {
			if (container_houseLoan != null) {
				container_houseLoan.clear();
				String fileLoc = Score.設定檔_V1_3;
				container_houseLoan.put(Score.type.基本, parse(Score.base.class, fileLoc));
				container_houseLoan.put(Score.type.無擔, parse(Score.unassure.class, fileLoc));
				container_houseLoan.put(Score.type.評等, parse(Score.grade.class, fileLoc));
				container_houseLoan.put(Score.type.房貸違約機率, parse(Score.houseLoanDR.class, fileLoc));
				logger.info("init finish");
			}
						
		} catch (Exception e) {
			logger.error("init error!");
			logger.error("[ScoreServiceImpl.init]",e);
		}
	}
	
	private void _getEjcicData_G_1_3(JSONObject result, String custId, String dupNo, String prodId, String QDATE, String tGetQdate, String t6Date, String t12Date, String highDate){
		// D07 聯徵查詢月份前一月（或二個月）之無擔保授信餘額（千元） chkAmt01
		if (true) {
			String effect = "";
			Map<String, Object> map = ejcicService.getD07_G_V_1_3(custId, prodId, QDATE);
			if (map != null){
				Object loanAmt = map.get("LoanAmt");
				//~~~~~~
				effect = Util.trim(loanAmt);
			}
			result.put(Score.column.D07聯徵查詢月份前一月或二個月之無擔保授信餘額, effect);
		}
		
		// * 近六個月平均的月信用卡循環信用（元） chkAmt02
		if (true) {
			String effect = "";
			Map<String, Object> map = ejcicService.getKRM040AvgRev_G_V_1_3(custId, prodId, QDATE,
					t6Date, highDate);
			if (map != null){
				effect = Util.trim(map.get("RevolBal"));
			}
			result.put(Score.column.近六個月平均的月信用卡循環信用, effect);
		}
		
		// 十二個月新業務申請查詢總家數 inqQty
		if (true) {
			String effect = "";
			List<Map<String, Object>> list = ejcicService.getN06_G_V_1_3(
					custId, prodId, QDATE, tGetQdate, t12Date, highDate);
			if (!list.isEmpty()) {
				effect = Util.trim(list.get(0).get("N06"));
			}
			// N06因子在房貸1.3版(inqQty), 非房貸2.0版(nochkItemN06) 都存在
			result.put(Score.column.N06十二個月新業務申請查詢總家數, effect);
		}
		
		// * 近十二個月信用卡（每筆）循環信用平均使用率 avgRate01
		if (true) {
			String effect = "";
			Map<String, Object> map = ejcicService.getR01_G_1_3(
					custId, prodId, QDATE);
			if (map != null){
				effect = Util.trim(map.get("RevolRate"));
			}
			result.put(Score.column.近十二個月信用卡_每筆_循環信用平均使用率, effect);
		}
		// * 聯徵查詢月份前一月之信用卡循環信用使用率 avgRate02
		if (true) {
			String effect = "";
			Map<String, Object> map = ejcicService.getKRM040RevolRatio_G_V_1_3(
					custId, prodId, QDATE); //
			if (map != null){
				effect = Util.trim(map.get("r10_cc_revol_ratio"));
			}
			result.put(Score.column.聯徵查詢月份前一月之信用卡循環信用使用率, effect);
		}
		
		// * 近六個月信用卡繳款狀況出現全額繳清無延遲次數 chkNum1
		if (true) {
			String effect = "";
			Map<String, Object> map = ejcicService.getP25_V_1_3(custId, prodId, QDATE);
			if (map != null){
				effect = Util.trim(map.get("p25"));
			}
			result.put(Score.column.P25近六個月信用卡繳款狀況出現全額繳清無延遲次數, effect);
		}
		
		// *近十二個月信用卡繳款狀況出現(全額繳清無延遲次數, 不良紀錄或使用循環信用次數) chkNum2, chkNum3
		if (true) {
			result.put(Score.column.P69近十二個月信用卡繳款狀況出現不良紀錄或使用循環信用次數, "");
			result.put(Score.column.P19近十二個月信用卡繳款狀況出現全額繳清無延遲次數, "");
			Map<String, Object> map = ejcicService.getP69P19(custId, prodId, QDATE);
			if (map != null){
				if (Util.isNotEmpty(map.get("idn_ban"))) {
					String p19 = Util.trim(map.get("P19"));
					String p69 = Util.trim(map.get("P69"));
					result.put(Score.column.P19近十二個月信用卡繳款狀況出現全額繳清無延遲次數, p19);
					result.put(Score.column.P69近十二個月信用卡繳款狀況出現不良紀錄或使用循環信用次數,
							p69);
				}
			}
		}
	}
	private void _getEjcicData_G_2_0(JSONObject result, String custId, String dupNo, String prodId, String QDATE, String tGetQdate, String t6Date, String t12Date, String highDate, JSONObject input_factor, String version_G){

		BigDecimal D07 = null;
		
		// D07 聯徵查詢月份前一月（或二個月）之無擔保授信餘額（千元） chkAmt01
		if (true) {
			String effect = "";
			Map<String, Object> map = ejcicService.getD07_G_V_2_0(custId, prodId, QDATE);
			if (map != null){
				Object loanAmt = map.get("LoanAmt");
				//~~~~~~
				effect = Util.trim(loanAmt);
				D07 = (BigDecimal) loanAmt;
			}
			result.put(Score.column.D07聯徵查詢月份前一月或二個月之無擔保授信餘額, effect);
		}
		
		 // D07_DIV_PINCOME因子 
		if(true){ // 計算方式== (d07_ln_nos_tamt*1000)/(pincome*10000/12)
			// == (d07*6) / (pincome*5)
			BigDecimal D07_DIV_PINCOME = null;
			BigDecimal pIncome = CrsUtil.parseBigDecimal(input_factor.opt(Score.column.個人年所得));
			/*
			 	分子      分母	結果
				NULL	 0	0
				0		 0	NULL
				30		 0	NULL
				-------------------
				NULL	72	0
				0		72	0
				30		72	=30/72
			 */
			if(pIncome==null || pIncome.compareTo(BigDecimal.ZERO)<=0){
				if(D07==null){
					D07_DIV_PINCOME = BigDecimal.ZERO;
				}else{
					D07_DIV_PINCOME = null;	
				}						
			}else{
				int scale = 4;
				//D07可能是 null，用 CrsUtil.parseBigDecimal(...) 去轉成0
				D07_DIV_PINCOME = Arithmetic.div(
						CrsUtil.parseBigDecimal(D07).multiply(new BigDecimal("6")), 
						pIncome.multiply(new BigDecimal("5")), scale); 
			}					
			result.put(Score.column.D07_DIV_PINCOME因子, D07_DIV_PINCOME==null?"":D07_DIV_PINCOME);					
		}
		
		
		
		if(Util.equals(ClsScoreUtil.V2_0_HOUSE_LOAN, version_G)){
			//N18次數因子
			if(true){ 
				Map<String, Object> map = ejcicService.getN18_INQ12_BY30D(custId, prodId, QDATE);
				Object n18 = MapUtils.getObject(map, "N18", JSON_NULL);
				result.put(Score.column.N18次數因子, n18);
			}
		}else if(Util.equals(ClsScoreUtil.V2_1_HOUSE_LOAN, version_G)){
			//N22次數因子
			Map<String, Object> map = ejcicService.getN22_INQ12_BY30D(custId, prodId, QDATE);
			Object n22 = MapUtils.getObject(map, "N22", JSON_NULL);
			result.put(Score.column.N22次數因子, n22);
		}
		
		
		// P25 (chkNum1)
		if (true) { 
			String effect = "";
			Map<String, Object> map = ejcicService.getP25_V_2_0(custId, prodId, QDATE);
			if (map != null){
				effect = Util.trim(map.get("p25"));
			}
			result.put(Score.column.P25近六個月信用卡繳款狀況出現全額繳清無延遲次數, effect);
		}
		
		// p68_p19因子 (itemP68, chkNum3)
		if(true){ // 房貸的P19只有[1N]
			Map<String, Object> map = ejcicService.getP68P19_G_2_0(custId, prodId, QDATE);
			Object p68 = MapUtils.getObject(map, "P68", JSON_NULL);
			Object p19 = MapUtils.getObject(map, "P19", JSON_NULL);
			result.put(Score.column.P68近六個月信用卡繳款狀況出現不良紀錄或使用循環信用次數, p68);
			result.put(Score.column.P19近十二個月信用卡繳款狀況出現全額繳清無延遲次數, p19);
		}
		
		// Z03因子
		if(true){
			Map<String, Object> map = ejcicService.getZ03(custId, prodId, QDATE);
			Object z03 = MapUtils.getObject(map, "Z03", JSON_NULL);
			result.put(Score.column.Z03因子, z03);
		}
	}
	
	private void _getEjcicData_G_3_0(JSONObject result, String custId, String dupNo, String prodId, String QDATE){
//		, String tGetQdate, String t6Date, String t12Date, String highDate, JSONObject input_factor
		// N18因子
//		if(true){ 
//			Map<String, Object> map = ejcicService.getN18_INQ12_BY30D(custId, prodId, QDATE);
//			Object n18 = MapUtils.getObject(map, "N18", JSON_NULL);
//			result.put(Score.column.v3_N18因子, n18);
//		}
		// N22因子
		if(true){ 
			//N22次數因子
			Map<String, Object> map = ejcicService.getN22_INQ12_BY30D(custId, prodId, QDATE);
			Object n22 = MapUtils.getObject(map, "N22", JSON_NULL);
			result.put(Score.column.N22次數因子, n22);
		}
		
		//D42因子
		if(true){ 
			Map<String, Object> map = ejcicService.getD42_G_3_0(custId, prodId, QDATE);
			String d42_src = MapUtils.getString(map, "D42", JSON_NULL);
			if(!Util.isEmpty(d42_src)){
				//設定小數點到第五位，測試用SQL取時，過長的尾數會怪怪的
				BigDecimal d42 = new BigDecimal(d42_src);
				result.put(Score.column.v3_D42因子, d42.setScale(5, BigDecimal.ROUND_HALF_UP));
			}else{
				Object d42 = MapUtils.getObject(map, "D42", JSON_NULL);
				result.put(Score.column.v3_D42因子, d42);
			}
		}
		
		// p68_p19因子 (itemP68, chkNum3)
		if(true){ // 房貸的P19只有[1N]
			Map<String, Object> map = ejcicService.getP68P19_G_2_0(custId, prodId, QDATE);
			Object p68 = MapUtils.getObject(map, "P68", "0"); //房貸3.0特殊規則，顧問>>若為null轉為0
			Object p19 = MapUtils.getObject(map, "P19", JSON_NULL);
			result.put(Score.column.v3_P68因子, p68);
			result.put(Score.column.v3_P19因子, p19);
		}
		
		//N01因子
		if(true){ 
			Map<String, Object> map = ejcicService.getN01_G_3_0(custId, prodId, QDATE);
			Object n01 = MapUtils.getObject(map, "N01", JSON_NULL);
			result.put(Score.column.v3_N01因子, n01);
		}
		
		//R01因子
		if(true){ 
			String effect = "";
			Map<String, Object> map = ejcicService.getR01_G_3_0(
					custId, prodId, QDATE);
			if (map != null){
				effect = Util.trim(map.get("RevolRate"));
			}
			result.put(Score.column.v3_R01因子, effect);		
		}
	}
	
	private void _getEjcicData_Q_1_0(JSONObject result, String custId, String dupNo, String prodId, String QDATE, String tGetQdate, String t6Date, String t12Date, String highDate){
		if (true) {
			Map<String, Object> map = ejcicService.getD63LnNosBank(custId, prodId, QDATE);
			Object d63_ln_nos_bank = MapUtils.getObject(map, "D63", JSON_NULL);
			result.put(ScoreNotHouseLoan.column.聯徵查詢月份當時無擔保授信往來家數, d63_ln_nos_bank);
		}
		if (true) {				
			Map<String, Object> map = ejcicService.getA21Cc6RcUseMonth_Q_V_1_0(custId, prodId, QDATE);
			Object a21_cc6_rc_use_month = MapUtils.getObject(map, "A21", JSON_NULL);
			result.put(ScoreNotHouseLoan.column.近6個月信用卡使用循環信用月份數, a21_cc6_rc_use_month);
		}
		if (true) {				 
			Map<String, Object> map = ejcicService.getAllCc6RcUseBank_Q_V_1_0(custId, prodId, QDATE);
			Object a11_cc6_rc_use_bank = MapUtils.getObject(map, "A11", JSON_NULL);
			result.put(ScoreNotHouseLoan.column.近6個月信用卡使用循環信用家數, a11_cc6_rc_use_bank);
		}
		if (true) {				
			Map<String, Object> map = ejcicService.getD53Ln6TimesFlag(custId, prodId, QDATE);
			Object d53_ln_6_times_flag = MapUtils.getObject(map, "D53", JSON_NULL);
			result.put(ScoreNotHouseLoan.column.聯徵查詢月份當月授信繳款記錄小於等於6次旗標, d53_ln_6_times_flag);
		}
	}
	private void _getEjcicData_Q_2_0(JSONObject result, String custId, String dupNo, String prodId, String QDATE, String tGetQdate, String t6Date, String t12Date, String highDate){
		if (true) {
			//d63_ln_nos_bank 原本V1_0[J-102-0196]也有
			Map<String, Object> map = ejcicService.getD63LnNosBank(custId, prodId, QDATE);
			Object d63_ln_nos_bank = MapUtils.getObject(map, "D63", JSON_NULL);
			result.put(ScoreNotHouseLoan.column.聯徵查詢月份當時無擔保授信往來家數, d63_ln_nos_bank);
		}
		if(true){
			//a11_cc6_rc_use_bank 原本V1_0[J-102-0196]也有，但SQL變了								 
			Map<String, Object> map = ejcicService.getAllCc6RcUseBank_V2_0(custId, prodId, QDATE);
			Object a11 = MapUtils.getObject(map, "A11", JSON_NULL);
			result.put(ScoreNotHouseLoan.column.近6個月信用卡使用循環信用家數, a11);					
		}
		if(true){
			//d07_ln_nos_tamt 同房貸模型1.3版
			
			String effect = "";
			Map<String, Object> map = ejcicService.getD07_G_V_1_3(custId, prodId, QDATE);
			if (map != null){
				Object loanAmt = map.get("LoanAmt");
				//~~~~~~
				effect = Util.trim(loanAmt);
			}					
			result.put(ScoreNotHouseLoan.column.D07因子, effect);					
		}
		if(true){
			//n06_inq12_napp_bank 同房貸模型1.3版
			
			// 十二個月新業務申請查詢總家數 inqQty
			String effect = "";
			List<Map<String, Object>> list = ejcicService.getN06_G_V_1_3(
					custId, prodId, QDATE, tGetQdate, t12Date, highDate);
			if (!list.isEmpty()) {
				effect = Util.trim(list.get(0).get("N06"));
			}
			
			result.put(ScoreNotHouseLoan.column.N06因子, effect);
		}				
		if(true){
			//p68_p19, 非房貸的P19包含[1N, 10]
			Map<String, Object> map = ejcicService.getP68P19_Q_2_0(custId, prodId, QDATE);
			Object p68 = MapUtils.getObject(map, "P68", JSON_NULL);
			Object p19 = MapUtils.getObject(map, "P19", JSON_NULL);
			result.put(ScoreNotHouseLoan.column.P68因子, p68);
			result.put(ScoreNotHouseLoan.column.P19因子, p19);
		}
	}
	private void _getEjcicData_Q_3_0(JSONObject result, String custId, String dupNo, String prodId, String QDATE, String tGetQdate, String t6Date, String t12Date, String highDate){

		if(true){
			//d07因子 同［房貸模型2.0版］
			
			String effect = "";
			Map<String, Object> map = ejcicService.getD07_G_V_2_0(custId, prodId, QDATE);
			if (map != null){
				Object loanAmt = map.get("LoanAmt");
				//~~~~~~
				effect = Util.trim(loanAmt);
			}					
			result.put(ScoreNotHouseLoan.column.D07因子, effect);					
		}
		if(true){
			String cnt = "";
			Map<String, Object> map = ejcicService.getN06_Q_V_3_0(custId, prodId, QDATE);
			if (map != null){
				Object N06 = map.get("N06");
				//~~~~~~
				cnt = Util.trim(N06);
			}					
			result.put(ScoreNotHouseLoan.column.N06因子, cnt);
		}
		
		if (true) {
			String effect = "";
			Map<String, Object> map = ejcicService.getP25_V_1_3(custId, prodId, QDATE);
			if (map != null){
				effect = Util.trim(map.get("p25"));
			}
			result.put(ScoreNotHouseLoan.column.P25因子, effect);
		}
		if (true) { //P69用房貸1.3 ，P19用房貸2.0
			if (true) {
				result.put(ScoreNotHouseLoan.column.P69因子, "");
				result.put(ScoreNotHouseLoan.column.P19因子, "");
				//=============================
				//
				if(true){
					Map<String, Object> map = ejcicService.getP69P19(custId, prodId, QDATE);
					if (map != null){
						if (Util.isNotEmpty(map.get("idn_ban"))) {
//							String p69 = Util.trim(map.get("P69"));
							Object p69 = MapUtils.getObject(map, "P69", JSON_NULL);
							result.put(ScoreNotHouseLoan.column.P69因子, p69);
						}
					}
				}
				//=============================
				// p68_p19因子 (itemP68, chkNum3)
				if(true){ // 房貸2.0 的P19只有[1N]
					Map<String, Object> map = ejcicService.getP68P19_G_2_0(custId, prodId, QDATE);
					Object p19 = MapUtils.getObject(map, "P19", JSON_NULL);
					result.put(ScoreNotHouseLoan.column.P19因子, p19);
				}
			}			
		}
		if(true){ //R01
			String effect = "";
			Map<String, Object> map = ejcicService.getR01_Q_3_0(
					custId, prodId, QDATE);
			if (map != null){
				effect = Util.trim(map.get("RevolRate"));
			}
			result.put(ScoreNotHouseLoan.column.R01因子, effect);		
		}
	}
	//J-111-0373 非房貸4.0
	private void _getEjcicData_Q_4_0(JSONObject result, String custId, String dupNo, String prodId, String QDATE, String tGetQdate, String t6Date, String t12Date, String highDate){
		if(true){
			//P01 近 12 個月授信帳戶繳款狀況出現 0 的總次數，不含本行
			Map<String, Object> map = ejcicService.getP01_Q_V_4_0(custId, prodId, QDATE);
			Object p01 = MapUtils.getObject(map, "P01", JSON_NULL);
			result.put(ScoreNotHouseLoan.column.nv4_P01因子, p01);
		}
		
//		if(true){
//			//P01 近 12 個月授信帳戶繳款狀況出現 0 的總次數，不含本行
//			Map<String, Object> map = misdbBaseService.getLNF022_loanBalSByid(custId+dupNo);
//			Object loanBal = MapUtils.getObject(map, "LOANBAL_S", JSON_NULL);
//			result.put(ScoreNotHouseLoan.column.nv4_loanBalSByid因子, loanBal);
//		}
		
		if(true){
			//R01 近12個月信用卡(每筆)循環信用平均使用率
			Map<String, Object> map = ejcicService.getR01_Q_V_4_0(custId, prodId, QDATE);
			Object r01 = MapUtils.getObject(map, "R01", JSON_NULL);
			result.put(ScoreNotHouseLoan.column.nv4_MaxR01因子, r01);
		}
	}

	
	/**
	 * 在進行 scoreService.score 之前，先把因子抓出
	 * @param custId
	 * @param dupNo
	 * @param keepQDateList
	 * @param version_NotHouseLoan
	 * @return
	 */
	private JSONObject _getEjcicData(String custId, String dupNo, String prodId
			, List<String> keepQDateList
			, String version_G, String version_Q, String version_R
			, JSONObject input_factor) {		
		
		JSONObject result = new JSONObject();		
		
		if(true){			
			result.put(ClsScoreUtil.PRODID_KEY, prodId);
		}
		// 資料查詢日期		
		Map<String, Object> dateMap = ejcicService.getDate(custId, prodId);
		if (dateMap != null) {
			result.put(Score.column.聯徵資料日期, dateMap.get("DATADATE2AD"));
			result.put(Score.column.聯徵查詢日期, dateMap.get("QDATE2AD"));

			// 取得相關日期
			String QDATE = Util.trim(dateMap.get("QDATE"));
			String tGetQdate = Util
					.trim(Util.parseInt(dateMap.get("tGetQdate")));
			// String t1 = Util.trim(dateMap.get("t1"));
			String t6Date = Util.trim(dateMap.get("t6"));
			String t12Date = Util.trim(dateMap.get("t12"));
			String highDate = Util.trim(dateMap.get("highDate"));

			// QDATE=102/01/29
			// t1=1011201
			// t6=1010701
			// t12=1010201
			// tGetQdate=1020129
			// highDate=1020131
			
			keepQDateList.add(QDATE);

			
			//============================= 聯徵8項負面資訊
			// 第1項在 _getEtchData(...)
			// 第2~7項，在以下的區塊
			//============================= 

			// //近12個月授信帳戶出現延遲二次（含）以上 chkItem3
			if (true) {
				String effect = UtilConstants.haveNo.NA;
				Map<String, Object> map = ejcicService
						.getBAM087DelayPayLoan(custId, prodId, QDATE);
				if (map != null){
					effect = (Util.parseInt(map.get("Counts")) >= 2 ? UtilConstants.haveNo.有
							: UtilConstants.haveNo.無);
				}
				result.put(Score.column.近12個月授信帳戶出現延遲二次以上, effect);
			}
			// 近12個月信用卡繳款狀況出現（循環信用有延遲）二次（含）以上 chkItem4
			if (true) {
				String effect = UtilConstants.haveNo.NA;
				Map<String, Object> map = ejcicService.getKRM040CardPayCode2(
						custId, prodId, QDATE, t12Date, highDate);
				effect = (map != null ? UtilConstants.haveNo.有
						: UtilConstants.haveNo.無);
				result.put(Score.column.近12個月信用卡繳款狀況出現_循環信用有延遲_二次以上, effect);
			}
			// 近12個月信用卡繳款狀況出現（未繳足最低金額）二次（含）以上 chkItem5
			if (true) {
				String effect = UtilConstants.haveNo.NA;
				Map<String, Object> map = ejcicService.getKRM040CardPayCode3(
						custId, prodId, QDATE, t12Date, highDate);
				effect = (map != null ? UtilConstants.haveNo.有
						: UtilConstants.haveNo.無);
				result.put(Score.column.近12個月信用卡繳款狀況出現_未繳足最低金額_二次以上, effect);
			}
			// 近12個月信用卡繳款狀況出現（全額逾期連續未繳）二次（含）以上 chkItem6
			if (true) {
				String effect = UtilConstants.haveNo.NA;				
				effect = (chkItem6(custId, prodId, QDATE, t12Date, highDate) ? UtilConstants.haveNo.有
						: UtilConstants.haveNo.無);
				result.put(Score.column.近12個月信用卡繳款狀況出現_全額逾期連續未繳_二次以上, effect);
			}
			// 近12個月信用卡有預借現金餘額二次（含）以上 chkItem7
			if (true) {
				String effect = UtilConstants.haveNo.NA;
				Map<String, Object> map = ejcicService
						.getKRM040CashAdvance(custId, prodId, QDATE);
				if (map != null){
					effect = (Util.parseInt(map.get("Counts")) >= 2 ? UtilConstants.haveNo.有
							: UtilConstants.haveNo.無);
				}
				result.put(Score.column.近12個月信用卡有預借現金餘額二次以上, effect);
			}
			// 近12個月現金卡有動用紀錄 chkItem8
			if (true) {
				String effect = UtilConstants.haveNo.NA;
				Map<String, Object> map = ejcicService
						.getBAM087CashCard(custId, prodId, QDATE);
				effect = (map != null ? UtilConstants.haveNo.有
						: UtilConstants.haveNo.無);
				result.put(Score.column.近12個月現金卡有動用紀錄, effect);
			}

			// 有消債條例信用註記、銀行公會債務協商註記或其他補充註記 chkItem2
			if (true) {
				String effect = UtilConstants.haveNo.NA;
				boolean chk = false;
				// MIS.VAM106 消債條例信用註記資訊
				if (true) {
					boolean check = false;
					Map<String, Object> map = ejcicService.getVAM106Data(
							custId, prodId, QDATE);
					if (map != null){
						chk = check = true;
					}
					// add by fantasy 2013/06/14
					result.put(Score.column.消債條例信用註記,
							check ? UtilConstants.haveNo.有
									: UtilConstants.haveNo.無);
				}
				// MIS.VAM107 銀行公會消金案件債務協商補充註記
				if (true) {
					boolean check = false;
					Map<String, Object> map = ejcicService.getVAM107Data(
							custId, prodId, QDATE);
					if (map != null){
						chk = check = true;
					}
					// add by fantasy 2013/06/14
					result.put(Score.column.銀行公會債務協商註記,
							check ? UtilConstants.haveNo.有
									: UtilConstants.haveNo.無);
				}
				// MIS.VAM108 非屬消債條例及銀行公會債務協商之註記資訊(其他補充註記)
				if (true) {
					boolean check = false;
					Map<String, Object> map = ejcicService.getVAM108Data(
							custId, prodId, QDATE);
					if (map != null){
						chk = check = true;
					}
					// add by fantasy 2013/06/14
					result.put(Score.column.其他補充註記,
							check ? UtilConstants.haveNo.有
									: UtilConstants.haveNo.無);
				}
				// 有消債條例信用註記、銀行公會債務協商註記或其他補充註記 chkItem2
				effect = (chk ? UtilConstants.haveNo.有 : UtilConstants.haveNo.無);
				result.put(Score.column.有消債條例信用註記_銀行公會債務協商註記或其他補充註記, effect);
			}
			// 擔保品類別
			if (true) {
				List<Map<String, Object>> list = ejcicService
						.getBAM095_IS_KIND(custId, prodId, QDATE);
				String isKindFlag = "N";
				for (Map<String, Object> map : list) {// J-112-0367 任一筆擔保品類別非為純信或信保或無值，畫面上"具擔保放款(不含信保)註記"就為Y
					String isKind = (String) map.get("IS_KIND");
					if (!("00".equals(isKind) || "05".equals(isKind) || StringUtils.isEmpty(Util.trim(isKind)))) {// 00-純信用；05-中小企業信用保證基金保證
						isKindFlag = "Y";
						break;
					}
				}
				result.put(Score.column.擔保品類別, isKindFlag);
			}

			//
			//============================= 聯徵8項負面資訊
			
			
			
			// 未持有信用卡
//			if (Util.isEmpty(result.get(Score.column.近六個月平均的月信用卡循環信用))
//					&& Util.isEmpty(result
//							.get(Score.column.近十二個月信用卡_每筆_循環信用平均使用率))
//					&& Util.isEmpty(result
//							.get(Score.column.聯徵查詢月份前一月之信用卡循環信用使用率))
//					&& Util.isEmpty(result
//							.get(Score.column.近六個月信用卡繳款狀況出現全額繳清無延遲次數))
//					&& Util.isEmpty(result
//							.get(Score.column.近十二個月信用卡繳款狀況出現不良紀錄或使用循環信用次數))
//					&& Util.isEmpty(result
//							.get(Score.column.近十二個月信用卡繳款狀況出現全額繳清無延遲次數))) {
//				result.put(Score.column.未持有信用卡, UtilConstants.DEFAULT.是);
//			} else {
//				result.put(Score.column.未持有信用卡, UtilConstants.DEFAULT.否);
//			}
			

			// 用途別 add by fantasy 2013/02/22
			result.put(Score.column.用途別, ejcicService.getPurposeCode(custId, prodId, QDATE));

			//===============================================
			if(Util.equals(ClsScoreUtil.V1_3_HOUSE_LOAN, version_G)){
				_getEjcicData_G_1_3(result, custId, dupNo, prodId, QDATE, tGetQdate, t6Date, t12Date, highDate);
			}//--END 房貸1.3版

			if(Util.equals(ClsScoreUtil.V2_0_HOUSE_LOAN, version_G) 
					|| Util.equals(ClsScoreUtil.V2_1_HOUSE_LOAN, version_G)){
				_getEjcicData_G_2_0(result, custId, dupNo, prodId, QDATE, tGetQdate, t6Date, t12Date, highDate, input_factor, version_G);
			}//--END 房貸2.0版
			
			if(Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN, version_G)){
				_getEjcicData_G_3_0(result, custId, dupNo, prodId, QDATE);
			}//--END 房貸3.0版
			
			
			//===============================================
			if(Util.equals(ClsScoreUtil.V1_0_NOT_HOUSE_LOAN, version_Q)){
				_getEjcicData_Q_1_0(result, custId, dupNo, prodId, QDATE, tGetQdate, t6Date, t12Date, highDate);					
			}//--END 非房貸1.0版

			if(Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN, version_Q)
					|| Util.equals(ClsScoreUtil.V2_1_NOT_HOUSE_LOAN, version_Q)){
				_getEjcicData_Q_2_0(result, custId, dupNo, prodId, QDATE, tGetQdate, t6Date, t12Date, highDate);
					
			}//--END 非房貸2.0版
			
			if(Util.equals(ClsScoreUtil.V3_0_NOT_HOUSE_LOAN, version_Q) 
					|| Util.equals(ClsScoreUtil.V3_1_NOT_HOUSE_LOAN, version_Q)){
				_getEjcicData_Q_3_0(result, custId, dupNo, prodId, QDATE, tGetQdate, t6Date, t12Date, highDate);
			}
			
			if(Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, version_Q)){
				_getEjcicData_Q_4_0(result, custId, dupNo, prodId, QDATE, tGetQdate, t6Date, t12Date, highDate);
			}//--END 非房貸4.0版
			
			//===============================================
			if(Util.equals(ClsScoreUtil.V2_1_CARD_LOAN, version_R)
					|| Util.equals(ClsScoreUtil.V3_0_CARD_LOAN, version_R) || Util.equals(ClsScoreUtil.V3_1_CARD_LOAN, version_R) 
					|| Util.equals(ClsScoreUtil.V4_0_CARD_LOAN, version_R)){
				//抓 J10 因子
				Map<String, Object> kcs003_map = null;
				if(MapUtils.isEmpty(kcs003_map)){
					List<Map<String, Object>> kcs003_list = ejcicService.getKCS003_data_ordByQdateDesc(custId);				
					if(kcs003_list.size()>0){						
						Date qDate = TWNDate.valueOf(Util.trim(kcs003_list.get(0).get("QDATE")));
						Date cmpDate = CapDate.shiftDays(CapDate.getCurrentTimestamp(), -32);
						boolean J10_InOneMonthCheck = clsService.is_function_on_codetype("J10_InOneMonthCheck");
						if(J10_InOneMonthCheck){
							if(CrsUtil.isNOT_null_and_NOTZeroDate(qDate) && LMSUtil.cmpDate(qDate, ">=", cmpDate)){
								kcs003_map = kcs003_list.get(0);	
							}else{
								//為1個月前的資料
							}
						}else{
							kcs003_map = kcs003_list.get(0);	
						}
					}	
				}
				
				if(MapUtils.isNotEmpty(kcs003_map)){
					result.put(ScoreCardLoan.column.J10信用評分, Util.trim(kcs003_map.get("SCORE")));
					result.put(ScoreCardLoan.column.KCS003信用評分狀態, Util.trim(kcs003_map.get("SCORE_STAT")));
					result.put(ScoreCardLoan.column.KCS003理由筆數, Util.trim(kcs003_map.get("REASON_CNT")));
					result.put(ScoreCardLoan.column.KCS003理由代碼一, Util.trim(kcs003_map.get("REASON_CODE_1")));
					result.put(ScoreCardLoan.column.KCS003理由代碼二, Util.trim(kcs003_map.get("REASON_CODE_2")));
					result.put(ScoreCardLoan.column.KCS003理由代碼三, Util.trim(kcs003_map.get("REASON_CODE_3")));
					result.put(ScoreCardLoan.column.KCS003理由代碼四, Util.trim(kcs003_map.get("REASON_CODE_4")));
					result.put(ScoreCardLoan.column.KCS003理由說明一, Util.trim(kcs003_map.get("REASON_EXPR_1")));
					result.put(ScoreCardLoan.column.KCS003理由說明二, Util.trim(kcs003_map.get("REASON_EXPR_2")));
					result.put(ScoreCardLoan.column.KCS003理由說明三, Util.trim(kcs003_map.get("REASON_EXPR_3")));
					result.put(ScoreCardLoan.column.KCS003理由說明四, Util.trim(kcs003_map.get("REASON_EXPR_4")));
					result.put(ScoreCardLoan.column.KCS003附加理由代碼, Util.trim(kcs003_map.get("ADDL_TEXT_CODE")));
					result.put(ScoreCardLoan.column.KCS003附加理由說明, Util.trim(kcs003_map.get("ADDL_TEXT_DESC")));
					result.put(ScoreCardLoan.column.KCS003_QDATE, Util.trim(kcs003_map.get("QDATE")));
					result.put(ScoreCardLoan.column.KCS003_QEMPCODE, Util.trim(kcs003_map.get("QEMPCODE")));
					result.put(ScoreCardLoan.column.KCS003_QEMPNAME, Util.trim(kcs003_map.get("QEMPNAME")));
					result.put(ScoreCardLoan.column.KCS003_QBRANCH, Util.trim(kcs003_map.get("QBRANCH")));
				}
			}
			
			//===============================================
			if(true){
				String krm040Flg = "";
				String bam095Flg = "";
				String krs008Flag = "";
				String primary_card = "";
				String additional_card = "";
				String business_or_p_card = "";
				String holdMegaCardDt_str = "";
				if(true){
					Map<String, Object> map = ejcicService.getC101M01A_JcicFlag(custId, prodId, QDATE);
					Integer i_krm040 = MapUtils.getInteger(map, "KRM040_CNT");
					Integer i_bam095 = MapUtils.getInteger(map, "BAM095_CNT");
					krs008Flag = Util.trim(MapUtils.getString(map, "KRS008_HAS_CARD"));
					
					krm040Flg = _parseJcicFlag(i_krm040);
					bam095Flg = _parseJcicFlag(i_bam095);
					if(Util.isEmpty(krs008Flag)){
						krs008Flag = "N";
					}
				
					primary_card = Util.trim(MapUtils.getString(map, "PRIMARY_CARD", ""));
					additional_card = Util.trim(MapUtils.getString(map, "ADDITIONAL_CARD", ""));
					business_or_p_card = Util.trim(MapUtils.getString(map, "BUSINESS_OR_P_CARD", ""));					
					//~~~~~~					
					Map<String, Object> mega_card_map = ejcicService.getKRM046Data_getNotStopMegaCard_Min_StartDate(custId);
					if(MapUtils.isNotEmpty(mega_card_map)){
						//格式=1070502
						String start_date_roc = MapUtils.getString(mega_card_map, "START_DATE");
						holdMegaCardDt_str = Util.trim(TWNDate.toAD(TWNDate.valueOf(start_date_roc)));
					}
				}
				
				//KRS008持有信用卡紀錄 --相反-- Score.column.未持有信用卡
				result.put(Score.column.未持有信用卡, Util.equals(krs008Flag, "N")?UtilConstants.DEFAULT.是:UtilConstants.DEFAULT.否);
				
				//第1碼krm040，第2碼bam095，第3碼KRS008
				result.put(ClsUtility.C101M01A_JCICFLG, krm040Flg+bam095Flg+krs008Flag);
				
				//J-106-0181 只持有附卡，房貸申請評等，也可升3等
				result.put(ClsUtility.C101M01A_PRIMARY_CARD, primary_card);
				result.put(ClsUtility.C101M01A_ADDITIONAL_CARD, additional_card);
				result.put(ClsUtility.C101M01A_BUSINESS_OR_P_CARD, business_or_p_card);						
				result.put(ClsUtility.C101M01A_HOLD_MEGA_CARD_DT, holdMegaCardDt_str);
			}
		
			if(true){
				String revol_cnt = "";
				Map<String, Object> map = ejcicService.getKRM040_revol_cnt(custId, prodId, QDATE);
				
				revol_cnt = Util.trim(MapUtils.getString(map, "CNT", "0"));
				
				result.put(Score.column.信用卡循環信用次數, revol_cnt);
			}
		
		} else {
			result.put(Score.column.聯徵資料日期, "");
			result.put(Score.column.聯徵查詢日期, "");
		}
		return result;
	}
	private String _parseJcicFlag(Integer i){
		if(i==null){
			return "-";
		}else{
			return i>0?"Y":"N";
		}
	}

	private JSONObject _getEtchData(String custId, String dupNo, String jcic_prodId, String qDate) {
		JSONObject result = new JSONObject();
		boolean chk = false;
		if(true){
			// MIS.KRM001 強停註記
			if (true) {
				boolean check = false;
				Map<String, Object> map = ejcicService
						.getKRM001StopCreditCard(custId, jcic_prodId, qDate);
				if (map != null){
					chk = check = true;
				}
				// add by fantasy 2013/06/14
				result.put(Score.column.信用卡強停,
						check ? UtilConstants.haveNo.有
								: UtilConstants.haveNo.無);
			}
			// MIS.BAM087 查詢申貸戶 所有逾期呆帳紀錄筆數
			// if (true) {
			// Map<String, Object> map = ejcicService
			// .getBAM087CollectionInfo1(custId);
			// if (map != null) {
			// if (Util.parseDouble(map.get("TOT_PASS")) > 0)
			// chk = true;
			// }
			// }
			// // MIS.BAM303 查詢所有董監事的從債務 逾期呆帳紀錄筆數
			// if (true) {
			// Map<String, Object> map = ejcicService
			// .getBAM303CollectionInfo1(custId);
			// if (map != null) {
			// if (Util.parseDouble(map.get("TOT_PASS")) > 0)
			// chk = true;
			// }
			// }
			// 催收呆帳紀錄
			if (true) {
				boolean check1d = false;
				Map<String, Object> map = ejcicService
						.getBAM101CollectionLog(custId, jcic_prodId, qDate);
				if (map != null){
					chk = check1d = true;
				}
				// add by fantasy 2013/06/14
				result.put(Score.column.催收呆帳,
						check1d ? UtilConstants.haveNo.有
								: UtilConstants.haveNo.無);
			}
			
			// 逾期金額 
			if (true) {
				boolean check1e = false;
				Map<String, Object> map = ejcicService
						.getBAM101PassDueAmt(custId, jcic_prodId, qDate);
				if (map != null){
					chk = check1e = true;
				}
				// add 2021/10/04
				result.put(Score.column.逾期放款,
						check1e ? UtilConstants.haveNo.有
								: UtilConstants.haveNo.無);
			}
			
			//初步，先指定 chkItem1=Y|NA
			//之後，再加入票信的資料去判斷是Y|N
			result.put(Score.column.有退票_拒往_信用卡強停或催收呆帳紀錄, 
					chk?UtilConstants.haveNo.有:UtilConstants.haveNo.NA);
		}
		if (true) {
			Map<String, Object> dateMap = etchService.getDate(custId);
			if (dateMap != null) {
				result.put(Score.column.票信資料日期, dateMap.get("END_DATE2AD"));
				result.put(Score.column.票信查詢日期, dateMap.get("QDATE2AD"));

				// 有退票、拒往、信用卡強停或催收呆帳紀錄 chkItem1
				if (true) {
					
					// MIS.MSG_004 大額退票、拒絕往來情形
					if (true) {
						boolean check1a = false;
						boolean check1b = false;
						Map<String, Object> map = etchService
								.getMSG001Data(custId);
						if (map != null) {
							// 退票
							int ued_dcc = Util.parseInt(map.get("ued_dcc"));
							int ums_bcc = Util.parseInt(map.get("ums_bcc"));
							int sd_bcc = Util.parseInt(map.get("sd_bcc"));
							int cpe_bcc = Util.parseInt(map.get("cpe_bcc"));
							
							//J-111-0287 調整退票檢核，排除以清償之退跳票紀錄
//							int por_ued_bcc = Util.parseInt(map.get("por_ued_bcc"));
//							int por_ums_bcc = Util.parseInt(map.get("por_ums_bcc"));
//							int por_sd_bcc = Util.parseInt(map.get("por_sd_bcc"));
//							int por_cpe_bcc = Util.parseInt(map.get("por_cpe_bcc"));
//							if (ued_dcc > 0 || ums_bcc > 0 || sd_bcc > 0
//									|| cpe_bcc > 0 || por_ued_bcc>0 || por_ums_bcc>0
//									|| por_sd_bcc > 0 || por_cpe_bcc > 0)
							
							if (ued_dcc > 0 || ums_bcc > 0 || sd_bcc > 0
									|| cpe_bcc > 0)
								chk = check1a = true;
							// 拒絕往來情形
							if (!"00000000".equals(Util.trim(map
									.get("reject_date"))))
								chk = check1b = true;
						}
						// add by fantasy 2013/06/14
						result.put(Score.column.退票,
								check1a ? UtilConstants.haveNo.有
										: UtilConstants.haveNo.無);
						// add by fantasy 2013/06/14
						result.put(Score.column.拒往,
								check1b ? UtilConstants.haveNo.有
										: UtilConstants.haveNo.無);
					}
					
					// 有退票、拒往、信用卡強停或催收呆帳紀錄 chkItem1
					result.put(Score.column.有退票_拒往_信用卡強停或催收呆帳紀錄,
							chk ? UtilConstants.haveNo.有
									: UtilConstants.haveNo.無);
				}
			} else {
				result.put(Score.column.票信資料日期, "");
				result.put(Score.column.票信查詢日期, "");
			
				if(true){ //勞工紓困案件, 可能有 B36+D10
					Map<String, String> dam001_dam003_map = ejcicService.get_DAM001_DAM003_relateData(custId);
					String eChkDDate = Util.trim(MapUtils.getString(dam001_dam003_map, "eChkDDate"));
					String eChkQDate = Util.trim(MapUtils.getString(dam001_dam003_map, "eChkQDate"));			
					if(Util.isNotEmpty(eChkDDate) && Util.isNotEmpty(eChkQDate)){
						result.put(Score.column.票信資料日期, eChkDDate);
						result.put(Score.column.票信查詢日期, eChkQDate);			
						//===========================
						boolean check1a = false;
						boolean check1b = false;
						String totCnt = MapUtils.getString(dam001_dam003_map, "TOTCNT");
						String porCnt = MapUtils.getString(dam001_dam003_map, "PORCNT");
						if(Util.isNotEmpty(totCnt) && Util.parseInt(totCnt)>0){
							chk = check1a = true;
						}
						if(Util.isNotEmpty(porCnt) && Util.parseInt(porCnt)>0){
							chk = check1a = true;
						}
						//~~~~~~~~~~~~~~
						if (!"00000000".equals(Util.trim(MapUtils.getString(dam001_dam003_map, "reject_date")))){
							chk = check1b = true;
						}
						//~~~~~~~~~~~~~~
						result.put(Score.column.退票, check1a? UtilConstants.haveNo.有 : UtilConstants.haveNo.無);
						result.put(Score.column.拒往, check1b? UtilConstants.haveNo.有 : UtilConstants.haveNo.無);
						
						// 有退票、拒往、信用卡強停或催收呆帳紀錄 chkItem1
						result.put(Score.column.有退票_拒往_信用卡強停或催收呆帳紀錄, chk ? UtilConstants.haveNo.有 : UtilConstants.haveNo.無);
					}
				}
			}
		}
		return result;
	}

	@Override
	public JSONObject getData(String custId, String dupNo, String versionG, String versionQ, String versionR, JSONObject input_factor) {
		JSONObject result = new JSONObject();
		
		//非房貸1.9版
		if(Util.equals(ClsScoreUtil.V1_9_NOT_HOUSE_LOAN, versionQ)){
			
			if (true) {
				Map<String, Object> map = dwdbBASEService.test_DwBam095Cc_getD63LnNosBank(custId);
				Object d63_ln_nos_bank = MapUtils.getObject(map, "D63", JSON_NULL);
				result.put(ScoreNotHouseLoan.column.聯徵查詢月份當時無擔保授信往來家數, d63_ln_nos_bank);
			}

			if (true) {				 
				Map<String, Object> map = dwdbBASEService.test_DwKrm040Cc_getAllCc6RcUseBank(custId);
				Object a11_cc6_rc_use_bank = MapUtils.getObject(map, "A11", JSON_NULL);
				result.put(ScoreNotHouseLoan.column.近6個月信用卡使用循環信用家數, a11_cc6_rc_use_bank);
			}
			
			if(true){
				//d07_ln_nos_tamt 同房貸模型1.3版
				
				String effect = "";
				Map<String, Object> map = dwdbBASEService.test_DwBam095Cc_getD07_G_V_1_3(custId);
				if (map != null){
					Object loanAmt = map.get("D07");
					//~~~~~~
					effect = Util.trim(loanAmt);
				}					
				result.put(ScoreNotHouseLoan.column.D07因子, effect);					
			}
			if(true){
				//n06_inq12_napp_bank 同房貸模型1.3版
				
				// 十二個月新業務申請查詢總家數 inqQty
				String effect = "";
				
				result.put(ScoreNotHouseLoan.column.N06因子, effect);
			}				
			if(true){
				//p68_p19, 非房貸的P19包含[1N, 10]
				Map<String, Object> map = dwdbBASEService.test_DwKrm040Cc_getP68P19_Q(custId);
				Object p68 = MapUtils.getObject(map, "P68", JSON_NULL);
				Object p19 = MapUtils.getObject(map, "P19", JSON_NULL);
				result.put(ScoreNotHouseLoan.column.P68因子, p68);
				result.put(ScoreNotHouseLoan.column.P19因子, p19);
			}	
			
		}//--END 非房貸1.9版
		
		if(Util.equals(ClsScoreUtil.V1_9_NOT_HOUSE_LOAN, versionQ)){
			
			if(true){
				String effect = UtilConstants.haveNo.NA;
				boolean chk = false;
				
				//(第2項) 有消債條例信用註記、銀行公會債務協商註記或其他補充註記
				boolean check = false;
				Map<String, Object> map = dwdbBASEService.test_DwVam106Cc_getVAM106Data(custId);
				if (map != null){
					chk = check = true;
				}
				// add by fantasy 2013/06/14
				result.put(Score.column.消債條例信用註記, check ? UtilConstants.haveNo.有 : UtilConstants.haveNo.無);
				
				check = false;
				//● 銀行公會債務協商註記
				map = dwdbBASEService.test_DwVam107Cc_getVAM107Data(custId);
				if (map != null){
					chk = check = true;
				}
				// add by fantasy 2013/06/14
				result.put(Score.column.銀行公會債務協商註記, check ? UtilConstants.haveNo.有 : UtilConstants.haveNo.無);

				check = false;
				//● 其他補充註記
				map = dwdbBASEService.test_DwVam108Cc_getVAM108Data(custId);
				if (map != null){
					chk = check = true;
				}
				// add by fantasy 2013/06/14
				result.put(Score.column.其他補充註記, check ? UtilConstants.haveNo.有 : UtilConstants.haveNo.無);
				
				effect = (chk ? UtilConstants.haveNo.有 : UtilConstants.haveNo.無);
				result.put(Score.column.有消債條例信用註記_銀行公會債務協商註記或其他補充註記, effect);
			}
			
			if(true){
				//(第3項) 近12個月授信帳戶出現延遲二次（含）以上 有無N.A 
				String effect = UtilConstants.haveNo.NA;
				Map<String, Object> map = dwdbBASEService.test_DwBam095Cc_getBAM087DelayPayLoan(custId);
				if (map != null){
					effect = (Util.parseInt(map.get("Counts")) >= 2 ? UtilConstants.haveNo.有 : UtilConstants.haveNo.無);
				}
				result.put(Score.column.近12個月授信帳戶出現延遲二次以上, effect);
			}
			
			if(true){
				//(第4項) 近12個月信用卡繳款狀況出現（循環信用有延遲）二次（含）以上 
				String effect = UtilConstants.haveNo.NA;
				Map<String, Object> map = dwdbBASEService.test_DwKrm040Cc_getKRM040CardPayCode2(custId);
				effect = (map != null ? UtilConstants.haveNo.有 : UtilConstants.haveNo.無);
				result.put(Score.column.近12個月信用卡繳款狀況出現_循環信用有延遲_二次以上, effect);
			}
			
			if(true){
				//(第5項) 近12個月信用卡繳款狀況出現（未繳足最低金額）二次（含）以上 
				String effect = UtilConstants.haveNo.NA;
				Map<String, Object> map = dwdbBASEService.test_DwKrm040Cc_getKRM040CardPayCode3(custId);
				effect = (map != null ? UtilConstants.haveNo.有 : UtilConstants.haveNo.無);
				result.put(Score.column.近12個月信用卡繳款狀況出現_未繳足最低金額_二次以上, effect);
			}
			
			if(true){
				//(第6項) 近12個月信用卡繳款狀況出現（全額逾期連續未繳）二次（含）以上
				String effect = (test_chkItem6(custId) ? UtilConstants.haveNo.有 : UtilConstants.haveNo.無);
				result.put(Score.column.近12個月信用卡繳款狀況出現_全額逾期連續未繳_二次以上, effect);
			}
			
			if(true){
				//(第7項) 近12個月信用卡有預借現金餘額二次（含）以上
				String effect = UtilConstants.haveNo.NA;
				Map<String, Object> map = dwdbBASEService.test_DwKrm040Cc_getKRM040CashAdvance(custId);
				if (map != null){
					effect = (Util.parseInt(map.get("Counts")) >= 2 ? UtilConstants.haveNo.有 : UtilConstants.haveNo.無);
				}
				
				result.put(Score.column.近12個月信用卡有預借現金餘額二次以上, effect);
			}

			if(true){
				//(第8項) 近12個月現金卡有動用紀錄 
				String effect = UtilConstants.haveNo.NA;
				Map<String, Object> map = dwdbBASEService.test_DwBam095Cc_getBAM087CashCard(custId);
				effect = (map != null ? UtilConstants.haveNo.有 : UtilConstants.haveNo.無);
				result.put(Score.column.近12個月現金卡有動用紀錄, effect);
			}
			
			result.put(Score.column.聯徵資料日期, "2019-03-08");
			result.put(Score.column.聯徵查詢日期, "2019-03-08");
			return result;
		}
		
		List<String> keepQDateList = new ArrayList<String>();
		String prodId = ejcicService.get_cls_PRODID(custId);
		result.putAll(_getEjcicData(custId, dupNo, prodId, keepQDateList, versionG, versionQ, versionR, input_factor));
		// 必需要有聯徵資料
		if (keepQDateList.size()>0) {
			result.putAll(_getEtchData(custId, dupNo, prodId, keepQDateList.get(0)));
		}
		return result;
	}
	
	//TODO doScoreCalculate
	public boolean scoreDoubleTrack() {
		//是否執行雙軌
		boolean scoreDoubleTrackFlag = false;
		//取得雙軌起迄日(COM.BCODETYPE >> scoreDoubleTrack)
		String startDate = "9999-01-01";
		String endDate = "9999-01-01";
		CodeType scoreDoubleTrack = codeTypeService.findByCodeTypeAndCodeValue("scoreDoubleTrack", "", "zh_TW");
		if(scoreDoubleTrack != null){
			startDate = scoreDoubleTrack.getCodeDesc2();
			endDate = scoreDoubleTrack.getCodeDesc3();
		}
		if(LMSUtil.cmpDate(new Date(), ">=", CapDate.parseDate(startDate))
				&& LMSUtil.cmpDate(new Date(), "<=", CapDate.parseDate(endDate))){
			scoreDoubleTrackFlag = true;
		}
		
		return scoreDoubleTrackFlag;
	}
	
	//TODO score
	@Override
	public JSONObject score(String type, JSONObject data, String varVer) {
		JSONObject result = new JSONObject();

		JSONObject items = null;
		String fileLoc = Score.設定檔_V1_3;
		if(Util.equals(ClsScoreUtil.V2_0_HOUSE_LOAN, varVer)){
			fileLoc = Score.設定檔_V2_0;				
		}
		if(Util.equals(ClsScoreUtil.V2_1_HOUSE_LOAN, varVer)){
			fileLoc = Score.設定檔_V2_1;				
		}
		if(Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN, varVer)){
			fileLoc = Score.設定檔_V3_0;				
		}
		
		if(true){
			if (Score.type.基本.equals(type)){
				dump_input_val(data);
				if (Util.equals(ClsScoreUtil.V1_3_HOUSE_LOAN, varVer)){
					items = parse(Score.base.class, fileLoc);	
				}else if (Util.equals(ClsScoreUtil.V2_0_HOUSE_LOAN, varVer)){
					items = parse(Score.baseG_V2_0.class, fileLoc);
				}else if (Util.equals(ClsScoreUtil.V2_1_HOUSE_LOAN, varVer)){
					items = parse(Score.baseG_V2_1.class, fileLoc);
				}else if (Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN, varVer)){
					items = parse(Score.baseG_V3_0.class, fileLoc);
				}else{
					items = new JSONObject();
				}
			}
			if (Util.equals(ClsScoreUtil.V1_3_HOUSE_LOAN, varVer) && Score.type.無擔.equals(type)){
				items = parse(Score.unassure.class, fileLoc);
			}
			if (Score.type.評等.equals(type)){
				items = parse(Score.grade.class, fileLoc);			
			}
			if (Score.type.房貸違約機率.equals(type)){
				items = parse(Score.houseLoanDR.class, fileLoc);
			}
		}

		if (items != null) {
			_debug("[scoreHouseLoan][varVer="+varVer+"]input_param:"+data);
			@SuppressWarnings("unchecked")
			Set<String> set = (Set<String>) items.keySet();
			BigDecimal total = BigDecimal.ZERO;
			for (String key : set) {
				JSONObject json = (JSONObject) items.get(key);
				_debug("[scoreHouseLoan][varVer="+varVer+"]---bf["+key+"]");
				Object determine_val = determine(key, json, data);
				_debug("[scoreHouseLoan][varVer="+varVer+"]---af["+key+"]"+determine_val);
				result.put(key, determine_val);
				total = total.add(Util.parseBigDecimal(result.get(key)));
			}
			if(Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN, varVer)){ //消金房貸評等3.0 四捨五入到小數第二位
				total=total.setScale(2, BigDecimal.ROUND_HALF_UP);
			}
			result.put(Score.column.合計變量得分, total);
		}

		// 不是評等項目時執行評等
		if (Score.type.基本.equals(type) || Score.type.無擔.equals(type)){
			if(Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN, varVer)){ //消金房貸評等3.0
				process_houseLoan_G_3_0(result, fileLoc, varVer);//裡面 call 評等
			}else{
				process_houseLoan(result, fileLoc, varVer);//裡面 call 評等
			}
		}
		
		return result;
	}
	
	
	
	@SuppressWarnings("unchecked")
	@Override
	public JSONObject score(String type, JSONObject data, boolean immediate) {
		JSONObject result = new JSONObject();

		JSONObject items = null;
		String fileLoc = Score.設定檔_V1_3;
		/*
	 		目前只有在 clsBatchServiceImpl，才有傳入 immediate = false 的情況
	 		=> scoreNotHouseLoan(...) 的寫法，就沒有 immediate 的參數
		*/
		if (immediate) {
			if (Score.type.基本.equals(type)){
				items = parse(Score.base.class, fileLoc);
			}else if (Score.type.無擔.equals(type)){
				items = parse(Score.unassure.class, fileLoc);
			}else if (Score.type.評等.equals(type)){
				items = parse(Score.grade.class, fileLoc);			
			}else if (Score.type.房貸違約機率.equals(type)){
				items = parse(Score.houseLoanDR.class, fileLoc);
			}
		} else {
			items = container_houseLoan.get(type);
		}

		if (items != null) {
			_debug("[scoreHouseLoan][immediate="+immediate+"]input_param:"+data);
			Set<String> set = (Set<String>) items.keySet();
			BigDecimal total = BigDecimal.ZERO;
			for (String key : set) {
				JSONObject json = (JSONObject) items.get(key);
				_debug("[scoreHouseLoan][immediate="+immediate+"]---bf["+key+"]");
				Object determine_val = determine(key, json, data);
				_debug("[scoreNotHouseLoan][immediate="+immediate+"]---af["+key+"]"+determine_val);
				result.put(key, determine_val);
				total = total.add(Util.parseBigDecimal(result.get(key)));
			}

			result.put(Score.column.合計變量得分, total);
		}

		// 不是評等項目時執行評等
		if (Score.type.基本.equals(type) || Score.type.無擔.equals(type)){
			String varVer = ClsScoreUtil.V1_3_HOUSE_LOAN;
			process_houseLoan(result, fileLoc, varVer);//裡面 call 評等
		}
		
		return result;
	}
	
	//TODO scoreNotHouseLoan
	@SuppressWarnings("unchecked")
	@Override
	public JSONObject scoreNotHouseLoan(String type, JSONObject data, String varVer) {
		JSONObject result = new JSONObject();

		JSONObject items = null;
		String fileLoc = ScoreNotHouseLoan.設定檔_V1_0;		
		if(Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN, varVer)){
			fileLoc = ScoreNotHouseLoan.設定檔_V2_0;				
		}else if(Util.equals(ClsScoreUtil.V2_1_NOT_HOUSE_LOAN, varVer)){
			fileLoc = ScoreNotHouseLoan.設定檔_V2_1;				
		}else if(Util.equals(ClsScoreUtil.V3_0_NOT_HOUSE_LOAN, varVer)){
			fileLoc = ScoreNotHouseLoan.設定檔_V3_0;				
		}else if(Util.equals(ClsScoreUtil.V3_1_NOT_HOUSE_LOAN, varVer)){
			fileLoc = ScoreNotHouseLoan.設定檔_V3_1;				
		}else if(Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, varVer)){
			fileLoc = ScoreNotHouseLoan.設定檔_V4_0;				
		}
//		else if(Util.equals(LMSUtil.V1_9_NOT_HOUSE_LOAN, varVer)){
//			fileLoc = ScoreNotHouseLoan.設定檔_V1_9;				
//		}

		/*
		 * 邏輯應該是
		 * <1>先有 base基本 → 再算出「初始」評等
		 * <2>依評等 → 對應 DR (若有人工調整評等，要以調整後的評等，去對應)
		 */
		if(Util.equals(type, ScoreNotHouseLoan.type.非房貸基本)){
			dump_input_val(data);
			if(Util.equals(ClsScoreUtil.V1_0_NOT_HOUSE_LOAN, varVer)){
				items = parse(ScoreNotHouseLoan.notHouseLoanBase.class, fileLoc);
			}else if(Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN, varVer)){
				items = parse(ScoreNotHouseLoan.notHouseLoanBase_V2_0.class, fileLoc);				
			}else if(Util.equals(ClsScoreUtil.V2_1_NOT_HOUSE_LOAN, varVer)){
				items = parse(ScoreNotHouseLoan.notHouseLoanBase_V2_1.class, fileLoc);
			}else if(Util.equals(ClsScoreUtil.V3_0_NOT_HOUSE_LOAN, varVer)){
				items = parse(ScoreNotHouseLoan.notHouseLoanBase_V3_0.class, fileLoc);
			}else if(Util.equals(ClsScoreUtil.V3_1_NOT_HOUSE_LOAN, varVer)){
				items = parse(ScoreNotHouseLoan.notHouseLoanBase_V3_1.class, fileLoc);
//			else if(Util.equals(LMSUtil.V1_9_NOT_HOUSE_LOAN, varVer)){
//				items = parse(ScoreNotHouseLoan.notHouseLoanBase_V1_9.class, fileLoc);
			}else if(Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, varVer)){
				items = parse(ScoreNotHouseLoan.notHouseLoanBase_V4_0.class, fileLoc);
			}
		}else if(Util.equals(type, ScoreNotHouseLoan.type.非房貸評等)){
			items = parse(ScoreNotHouseLoan.notHouseLoanGrade.class, fileLoc);
		}else if(Util.equals(type, ScoreNotHouseLoan.type.非房貸違約機率)){	
			items = parse(ScoreNotHouseLoan.notHouseLoanDR.class, fileLoc);
		}

		if (items != null) {	
			_debug("[scoreNotHouseLoan]input_param:"+data);
			Set<String> set = (Set<String>) items.keySet();
			BigDecimal total = BigDecimal.ZERO;
			for (String key : set) {
				JSONObject json = (JSONObject) items.get(key);
				Object determine_val = determine(key, json, data);
				_debug("[scoreNotHouseLoan]---af["+key+"]"+determine_val);
				result.put(key, determine_val);				
				total = total.add(Util.parseBigDecimal(result.get(key)));
			}
			if(Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, varVer)){ //消金非房貸評等4.0 四捨五入到小數第二位
				total=total.setScale(2, BigDecimal.ROUND_HALF_UP);
			}
			result.put(ScoreNotHouseLoan.column.合計變量得分, total);
		}

		if (ScoreNotHouseLoan.type.非房貸基本.equals(type)){
			if(Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, varVer)){ //消金非房貸4.0
				process_NotHouseLoan_Q_4_0(result, fileLoc, varVer);//裡面 call 計算[評等、違約機率]
			}else{
				process_NotHouseLoan(result, fileLoc, varVer);//裡面 call 計算[評等、違約機率]		
			}
						
		}
				
		return result;
	}
	
	//TODO scoreCardLoan
	@SuppressWarnings("unchecked")
	@Override
	public JSONObject scoreCardLoan(String type, JSONObject data, String varVer) {
		JSONObject result = new JSONObject();

		JSONObject items = null;
		String fileLoc = ScoreCardLoan.設定檔_V2_1;		
		if(Util.equals(ClsScoreUtil.V3_0_CARD_LOAN, varVer)){
			fileLoc = ScoreCardLoan.設定檔_V3_0;				
		}else if(Util.equals(ClsScoreUtil.V3_1_CARD_LOAN, varVer)){
			fileLoc = ScoreCardLoan.設定檔_V3_1;				
		}else if(Util.equals(ClsScoreUtil.V4_0_CARD_LOAN, varVer)){
			fileLoc = ScoreCardLoan.設定檔_V4_0;				
		}

		/*
		 * 邏輯應該是
		 * <1>先有 base基本 → 再算出「初始」評等
		 * <2>依評等 → 對應 DR (若有人工調整評等，要以調整後的評等，去對應)
		 */
		if(Util.equals(type, ScoreCardLoan.type.卡友貸基本)){
			dump_input_val(data);
			if(Util.equals(ClsScoreUtil.V2_1_CARD_LOAN, varVer)){
				items = parse(ScoreCardLoan.cardLoanBase_V2_1.class, fileLoc);			
			}else if(Util.equals(ClsScoreUtil.V3_0_CARD_LOAN, varVer)){
				items = parse(ScoreCardLoan.cardLoanBase_V3_0.class, fileLoc);			
			}else if(Util.equals(ClsScoreUtil.V3_1_CARD_LOAN, varVer)){
				items = parse(ScoreCardLoan.cardLoanBase_V3_1.class, fileLoc);			
			}else if(Util.equals(ClsScoreUtil.V4_0_CARD_LOAN, varVer)){
				items = parse(ScoreCardLoan.cardLoanBase_V4_0.class, fileLoc);			
			}
		}else if(Util.equals(type, ScoreCardLoan.type.卡友貸評等)){
			items = parse(ScoreCardLoan.cardLoanGrade.class, fileLoc);
		}else if(Util.equals(type, ScoreCardLoan.type.卡友貸違約機率)){	
			items = parse(ScoreCardLoan.cardLoanDR.class, fileLoc);
		}

		if (items != null) {		
			_debug("[scoreCardLoan]input_param:"+data);
			Set<String> set = (Set<String>) items.keySet();
			BigDecimal total = BigDecimal.ZERO;
			for (String key : set) {
				JSONObject json = (JSONObject) items.get(key);
				Object determine_val = determine(key, json, data);
				_debug("[scoreCardLoan]---af["+key+"]"+determine_val);
				result.put(key, determine_val);				
				total = total.add(Util.parseBigDecimal(result.get(key)));
			}
			if(Util.equals(ClsScoreUtil.V4_0_CARD_LOAN, varVer)){ //專案信貸(非團體)評等4.0 四捨五入到小數第二位
				total=total.setScale(2, BigDecimal.ROUND_HALF_UP);
			}
			result.put(ScoreCardLoan.column.合計變量得分, total);
		}

		if (ScoreCardLoan.type.卡友貸基本.equals(type)){
			process_CardLoan(data, result, fileLoc, varVer);//裡面 call 計算[評等、違約機率]			
		}
				
		return result;
	}
	
	
	private void _debug(String s){
		
	}

	/**
	 * 判斷分數
	 * 
	 * @param item
	 * @param data
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public String determine(String name, JSONObject item, JSONObject data) {
		String result = null;

		if (item != null && data != null) {
			StringBuilder sb = new StringBuilder();
			ScriptEngine se = scriptEngineManager.getEngineByName("javascript");

			Set<String> set = (Set<String>) item.keySet();
			for (String key : set) {
				if (result == null) {
					JSONObject json = (JSONObject) item.get(key);
					String formula = (String) json.get(Score.公式);
					JSONArray columns = (JSONArray) json.get(Score.欄位);
					if (formula != null && columns != null) {
						sb.append(name).append(Score.Point).append(key)
								.append(" formula").append("[").append(formula)
								.append("]");

						int size = columns.size();
						Object[] args = new Object[size];
						sb.append(" values");
						for (int i = 0; i < size; i++) {
							String columnName = Util.trim(columns.get(i));
							String value = Util.trim(data.get(columnName))
									.replaceAll(",", "");
							sb.append("[").append(columnName).append("=")
									.append(value).append("]");
							args[i] = Util.isEmpty(value) ? "Null" : value;
						}
						MessageFormat mf = new MessageFormat(formula);
						String format = mf.format(args);
						sb.append(" format").append("[").append(format)
								.append("]");
						try {
							Object raw_val = se.eval(format);
							Boolean value = (Boolean) raw_val; 
							if (value) {
								result = (String) json.get(Score.分數);
								sb.append(" score").append("[").append(result)
										.append("]");
							}
							sb.append(" result[").append(value).append("]");
							logger.info(sb.toString());
						} catch (ScriptException e) {
							sb.append(" result_error[ScriptException][name="+name+"][key="+key+"]"+StrUtils.getStackTrace(e));
							logger.error(sb.toString());
						} catch (ClassCastException e) {
							sb.append(" result_error[ClassCastException][name="+name+"][key="+key+"][formula="+formula+"]"+StrUtils.getStackTrace(e));
							logger.error(sb.toString());
							throw e;
						} finally {
							sb.delete(0, sb.length());
						}
					}
				}
			}
		}

		return result;
	}

	/**
	 * 解析Property
	 * 
	 * @param clazz
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public JSONObject parse(Class clazz, String fileLoc) {
		PropertyResourceBundle bundle = getPropertyResourceBundle(fileLoc);

		JSONObject result = new JSONObject();
		if (clazz != null && bundle != null) {
			String simpleName = Util.trim(clazz.getSimpleName());
			Field[] fields = clazz.getDeclaredFields();
			if (fields != null) {
				for (Field f : fields) {
					String key = getFieldValue(f);
					if (result.get(key) == null)
						result.put(key, new JSONObject());

					JSONObject item = (JSONObject) result.get(key);
					int count = 0;
					String[] values = getPropertyStringArray(bundle, simpleName
							+ Score.Point + key);
					for (String value : values) {
						String[] s = value.split(Score.Comma);
						if (s.length >= 3) {
							String group = Util.addZeroWithValue(++count, 2);
							if (item.get(group) == null)
								item.put(group, new JSONObject());

							JSONObject subItem = (JSONObject) item.get(group);
							subItem.put(Score.分數, s[0]);
							subItem.put(Score.公式, s[1]);
							subItem.put(Score.欄位, s[2].split(Score.Semicolon));
						}
					}
				}
			}
		}

		return result;
	}

	/**
	 * 取得欄位值
	 * 
	 * @param field
	 * @return
	 */
	private String getFieldValue(Field field) {
		String result = null;
		if (field != null) {
			try {
				result = Util.trim(field.get(null));
			} catch (IllegalArgumentException e) {
				logger.error("[field.get]",e);
			} catch (IllegalAccessException e) {
				logger.error("[field.get]",e);
			}
		}
		return result;
	}

	/**
	 * process_houseLoan
	 * 
	 * @param data
	 */
	private void process_houseLoan(JSONObject data, String fileLoc, String varVer) {

		Properties prop = getProperty(fileLoc);

		if (prop != null) {
			data.put(Score.column.評等建立日期,
					CapDate.getCurrentDate(DataParse.DateFormat));
			data.put(Score.column.報表亂碼, IDGenerator.getUUID());
			BigDecimal varA = Util
					.parseBigDecimal(prop.getProperty(Score.column.基準底分A));
			BigDecimal varB = Util
					.parseBigDecimal(prop.getProperty(Score.column.基準底分B));
			BigDecimal varC = Util.parseBigDecimal(prop
					.getProperty(Score.column.基準底分常數項));
			data.put(Score.column.基準底分A, varA);
			data.put(Score.column.基準底分B, varB);
			data.put(Score.column.基準底分常數項, varC);
			data.put(Score.column.基準底分版本,
					Util.trim(prop.getProperty(Score.column.基準底分版本)));

			BigDecimal scrNum11 = Util.parseBigDecimal(data.get(Score.column.合計變量得分));
			BigDecimal scrNum12 = ( varA.add( (varB.multiply(varC)) )).setScale(4, BigDecimal.ROUND_HALF_UP);
			BigDecimal scrNum13 = scrNum11.add(scrNum12);
			data.put(Score.column.基準底分, scrNum12);
			data.put(Score.column.初始評分, scrNum13);
			
			double pd = (1 / (1 + Math.exp(  ((scrNum13.subtract(varA)).divide(varB, MathContext.DECIMAL32)).doubleValue()   )));			
			data.put(Score.column.預測壞率, Util.Overflow(pd, 4));

			if(true){//評等
				JSONObject level = score(Score.type.評等, data, varVer);
				data.put(Score.column.初始評等, level.get(Score.grade.等級));
				data.put(Score.column.調整評等, "");
				data.put(Score.column.評等調整日期, "");
				data.put(Score.column.最終評等, level.get(Score.grade.等級));

				data.put(Score.column.調整狀態, "");
				data.put(Score.column.調整註記, "");
				data.put(Score.column.調整理由, "");	
			}
			if(true){//違約機率
				JSONObject houseLoanDR = score(Score.type.房貸違約機率, data, varVer);
				setHouseLoanDR(houseLoanDR, data);
			}
			// clear
			prop.clear();
			prop = null;
		}		
	}
	/**
	 * process_houseLoan_G_3_0
	 * 
	 * @param data
	 */
	private void process_houseLoan_G_3_0(JSONObject data, String fileLoc, String varVer) {
		Properties prop = getProperty(fileLoc);
		if (prop != null) {
			data.put(Score.column.評等建立日期,
					CapDate.getCurrentDate(DataParse.DateFormat));
			data.put(Score.column.報表亂碼, IDGenerator.getUUID());
			data.put(Score.column.基準底分A, "");
			data.put(Score.column.基準底分B, "");
			data.put(Score.column.基準底分常數項, "");
			data.put(Score.column.基準底分版本,
					Util.trim(prop.getProperty(Score.column.基準底分版本)));
			BigDecimal slope = Util.parseBigDecimal(prop.getProperty(Score.column.斜率));
			BigDecimal interCept = Util.parseBigDecimal(prop.getProperty(Score.column.截距));
			data.put(Score.column.截距, interCept);
			data.put(Score.column.斜率, slope);
			
			BigDecimal scrNum11 = Util.parseBigDecimal(data.get(Score.column.合計變量得分));
//			data.put(Score.column.基準底分, scrNum11);
			data.put(Score.column.基準底分, "");
			BigDecimal scrNum13 = scrNum11.setScale(1, BigDecimal.ROUND_HALF_UP);
			data.put(Score.column.初始評分, scrNum13);
			
			double pd = (1 / (1 + Math.exp( interCept.add(slope.multiply(scrNum11)).doubleValue()  )));			
			data.put(Score.column.預測壞率, Util.Overflow(pd, 4));
			if(true){//評等
				JSONObject level = score(Score.type.評等, data, varVer);
				data.put(Score.column.初始評等, level.get(Score.grade.等級));
				data.put(Score.column.調整評等, "");
				data.put(Score.column.評等調整日期, "");
				data.put(Score.column.最終評等, level.get(Score.grade.等級));

				data.put(Score.column.調整狀態, "");
				data.put(Score.column.調整註記, "");
				data.put(Score.column.調整理由, "");	
			}
			if(true){//違約機率
				JSONObject houseLoanDR = score(Score.type.房貸違約機率, data, varVer);
				setHouseLoanDR(houseLoanDR, data);
			}
			// clear
			prop.clear();
			prop = null;
		}		
	}

	
	
	private void process_NotHouseLoan(JSONObject data, String fileLoc, String varVer) {
		Properties prop = getProperty(fileLoc);
		if (prop != null) {
			data.put(ScoreNotHouseLoan.column.評等建立日期,
					CapDate.getCurrentDate(DataParse.DateFormat));
			data.put(ScoreNotHouseLoan.column.報表亂碼, IDGenerator.getUUID());
			BigDecimal varA = Util
					.parseBigDecimal(prop.getProperty(ScoreNotHouseLoan.column.基準底分A));
			BigDecimal varB = Util
					.parseBigDecimal(prop.getProperty(ScoreNotHouseLoan.column.基準底分B));
			BigDecimal varC = Util.parseBigDecimal(prop
					.getProperty(ScoreNotHouseLoan.column.基準底分常數項));
			data.put(ScoreNotHouseLoan.column.基準底分A, varA);
			data.put(ScoreNotHouseLoan.column.基準底分B, varB);
			data.put(ScoreNotHouseLoan.column.基準底分常數項, varC);
			data.put(ScoreNotHouseLoan.column.基準底分版本,
					Util.trim(prop.getProperty(ScoreNotHouseLoan.column.基準底分版本)));

			BigDecimal scrNum11 = Util.parseBigDecimal(data.get(ScoreNotHouseLoan.column.合計變量得分));
			BigDecimal scrNum12 = ( varA.add( (varB.multiply(varC)) )).setScale(4, BigDecimal.ROUND_HALF_UP);
			BigDecimal scrNum13 = scrNum11.add(scrNum12);
			data.put(ScoreNotHouseLoan.column.基準底分, scrNum12);
			data.put(ScoreNotHouseLoan.column.初始評分, scrNum13);
			
			double pd = (1 / (1 + Math.exp(  ((scrNum13.subtract(varA)).divide(varB, MathContext.DECIMAL32)).doubleValue()   )));
			data.put(ScoreNotHouseLoan.column.預測壞率, Util.Overflow(pd, 4));
			if(true){//評等
				JSONObject level = scoreNotHouseLoan(ScoreNotHouseLoan.type.非房貸評等, data, varVer);
				data.put(ScoreNotHouseLoan.column.初始評等, level.get(ScoreNotHouseLoan.notHouseLoanGrade.等級));
				data.put(ScoreNotHouseLoan.column.調整評等, "");
				data.put(ScoreNotHouseLoan.column.評等調整日期, "");
				data.put(ScoreNotHouseLoan.column.最終評等, level.get(ScoreNotHouseLoan.notHouseLoanGrade.等級));
	
				data.put(ScoreNotHouseLoan.column.調整狀態, "");
				data.put(ScoreNotHouseLoan.column.調整註記, "");
				data.put(ScoreNotHouseLoan.column.調整理由, "");
			}
			if(true){//違約機率
				JSONObject notHouseLoanDR = scoreNotHouseLoan(ScoreNotHouseLoan.type.非房貸違約機率, data, varVer);
				setNotHouseLoanDR(notHouseLoanDR, data);
			}
			// clear
			prop.clear();
			prop = null;
		}
	}
	
	private void process_NotHouseLoan_Q_4_0(JSONObject data, String fileLoc, String varVer) {
		Properties prop = getProperty(fileLoc);
		if (prop != null) {
			data.put(ScoreNotHouseLoan.column.評等建立日期,
					CapDate.getCurrentDate(DataParse.DateFormat));
			data.put(ScoreNotHouseLoan.column.報表亂碼, IDGenerator.getUUID());
			data.put(ScoreNotHouseLoan.column.基準底分A, "");
			data.put(ScoreNotHouseLoan.column.基準底分B, "");
			data.put(ScoreNotHouseLoan.column.基準底分常數項, "");
			data.put(ScoreNotHouseLoan.column.基準底分版本,
					Util.trim(prop.getProperty(ScoreNotHouseLoan.column.基準底分版本)));
			
			BigDecimal slope = Util.parseBigDecimal(prop.getProperty(Score.column.斜率));
			BigDecimal interCept = Util.parseBigDecimal(prop.getProperty(Score.column.截距));
			data.put(ScoreNotHouseLoan.column.截距, interCept);
			data.put(ScoreNotHouseLoan.column.斜率, slope);

			BigDecimal scrNum11 = Util.parseBigDecimal(data.get(ScoreNotHouseLoan.column.合計變量得分));
			data.put(ScoreNotHouseLoan.column.基準底分, "");
			
			BigDecimal scrNum13 = scrNum11.setScale(1, BigDecimal.ROUND_HALF_UP);
			data.put(ScoreNotHouseLoan.column.初始評分, scrNum13);
			
			double pd = (1 / (1 + Math.exp( interCept.add(slope.multiply(scrNum11)).doubleValue()  )));	
			data.put(ScoreNotHouseLoan.column.預測壞率, Util.Overflow(pd, 4));
			if(true){//評等
				JSONObject level = scoreNotHouseLoan(ScoreNotHouseLoan.type.非房貸評等, data, varVer);
				data.put(ScoreNotHouseLoan.column.初始評等, level.get(ScoreNotHouseLoan.notHouseLoanGrade.等級));
				data.put(ScoreNotHouseLoan.column.調整評等, "");
				data.put(ScoreNotHouseLoan.column.評等調整日期, "");
				data.put(ScoreNotHouseLoan.column.最終評等, level.get(ScoreNotHouseLoan.notHouseLoanGrade.等級));
	
				data.put(ScoreNotHouseLoan.column.調整狀態, "");
				data.put(ScoreNotHouseLoan.column.調整註記, "");
				data.put(ScoreNotHouseLoan.column.調整理由, "");
			}
			if(true){//違約機率
				data.put(ScoreCardLoan.column.違約機率_預估2年期_短, "");			
				data.put(ScoreCardLoan.column.違約機率_預估3年期_中長, "");
				data.put(ScoreCardLoan.column.違約機率_預估1年期_短, "");
				data.put(ScoreCardLoan.column.違約機率_預估1年期_中長, "");
				JSONObject notHouseLoanDR = scoreNotHouseLoan(ScoreNotHouseLoan.type.非房貸違約機率, data, varVer);
				setNotHouseLoanDR(notHouseLoanDR, data);				
			}
			// clear
			prop.clear();
			prop = null;
		}
	}
	
	private void process_CardLoan(JSONObject data, JSONObject result, String fileLoc, String varVer) {
		String j10_score = Util.trim(data.get(ScoreCardLoan.column.J10信用評分));
		String kcs003_reason_code1 = Util.trim(data.get(ScoreCardLoan.column.KCS003理由代碼一));
		
		if(Util.isEmpty(j10_score) && Util.isEmpty(kcs003_reason_code1)){			
			result.put(ScoreCardLoan.column.評等建立日期, "");
			result.put(ScoreCardLoan.column.報表亂碼, "");			
			result.put(ScoreCardLoan.column.基準底分A, "");
			result.put(ScoreCardLoan.column.基準底分B, "");
			result.put(ScoreCardLoan.column.基準底分常數項, "");
			result.put(ScoreCardLoan.column.基準底分版本, "");
			result.put(ScoreCardLoan.column.基準底分, "");
			result.put(ScoreCardLoan.column.初始評分, "");
			result.put(ScoreCardLoan.column.預測壞率, "");
			if(true){//評等			
				if(true){
					result.put(ScoreCardLoan.column.升降等_聯徵J10, "");
					result.put(ScoreCardLoan.column.支援評等, "");
				}	
				result.put(ScoreCardLoan.column.初始評等, "");
				result.put(ScoreCardLoan.column.調整評等, "");
				result.put(ScoreCardLoan.column.評等調整日期, "");
				result.put(ScoreCardLoan.column.最終評等, "");
	
				result.put(ScoreNotHouseLoan.column.調整狀態, "");
				result.put(ScoreNotHouseLoan.column.調整註記, "");
				result.put(ScoreNotHouseLoan.column.調整理由, "");
			}
			if(true){//違約機率
				result.put(ScoreCardLoan.column.違約機率_預估2年期_短, "");			
				result.put(ScoreCardLoan.column.違約機率_預估3年期_中長, "");
				result.put(ScoreCardLoan.column.違約機率_預估1年期_短, "");
				result.put(ScoreCardLoan.column.違約機率_預估1年期_中長, "");
			}
		}else{
			Properties prop = getProperty(fileLoc);
			if (prop != null) {
				if(Util.equals(ClsScoreUtil.V4_0_CARD_LOAN, varVer)){ //J-111-0373
					result.put(ScoreCardLoan.column.評等建立日期,CapDate.getCurrentDate(DataParse.DateFormat));
					result.put(ScoreCardLoan.column.報表亂碼, IDGenerator.getUUID());
					result.put(ScoreCardLoan.column.基準底分A, "");
					result.put(ScoreCardLoan.column.基準底分B, "");
					result.put(ScoreCardLoan.column.基準底分常數項, "");
					result.put(ScoreCardLoan.column.基準底分版本,Util.trim(prop.getProperty(ScoreCardLoan.column.基準底分版本)));
					BigDecimal slope = Util.parseBigDecimal(prop.getProperty(Score.column.斜率));
					BigDecimal interCept = Util.parseBigDecimal(prop.getProperty(Score.column.截距));
					result.put(ScoreCardLoan.column.截距, interCept);
					result.put(ScoreCardLoan.column.斜率, slope);
					BigDecimal scrNum11 = Util.parseBigDecimal(result.get(ScoreCardLoan.column.合計變量得分));
					result.put(ScoreCardLoan.column.基準底分, "");
					
					BigDecimal scrNum13 = scrNum11.setScale(1, BigDecimal.ROUND_HALF_UP);
					result.put(ScoreCardLoan.column.初始評分, scrNum13);
					double pd = (1 / (1 + Math.exp( interCept.add(slope.multiply(scrNum11)).doubleValue()  )));	
					result.put(ScoreCardLoan.column.預測壞率, Util.Overflow(pd, 4));
					
					if(true){//評等
						JSONObject level = scoreCardLoan(ScoreCardLoan.type.卡友貸評等, result, varVer);
						String pRating = Util.trim(level.get(ScoreCardLoan.cardLoanGrade.等級));
						
						Integer adj_j10 = 0;
						String sprtRating = "";
						if(Util.isNotEmpty(pRating) && Util.isInteger(pRating)){
							if(Util.equals("0",j10_score) || Util.equals("200",j10_score)){
								sprtRating = "15"; //新版評等，最差為15等
							}else{
								adj_j10 = get_CardLoan_adj_j10_step1(j10_score, kcs003_reason_code1 );							
								sprtRating = Util.trim(OverSeaUtil.rating_min1_max15(Util.parseInt(pRating) - adj_j10));
							}
							adj_j10 = Util.parseInt(pRating) - Util.parseInt(sprtRating);
						}			
						if(true){
							result.put(ScoreCardLoan.column.升降等_聯徵J10, Util.trim(adj_j10));
							result.put(ScoreCardLoan.column.支援評等, Util.trim(sprtRating));
						}		
						result.put(ScoreCardLoan.column.初始評等, pRating);
						result.put(ScoreCardLoan.column.調整評等, "");
						result.put(ScoreCardLoan.column.評等調整日期, "");
						result.put(ScoreCardLoan.column.最終評等, Util.trim(sprtRating));
			
						result.put(ScoreCardLoan.column.調整狀態, "");
						result.put(ScoreCardLoan.column.調整註記, "");
						result.put(ScoreCardLoan.column.調整理由, "");
					}
					if(true){//違約機率
						JSONObject cardLoanDR = scoreCardLoan(ScoreCardLoan.type.卡友貸違約機率, result, varVer);
						setCardLoanDR(cardLoanDR, result);
					}
				}else{
					result.put(ScoreCardLoan.column.評等建立日期,CapDate.getCurrentDate(DataParse.DateFormat));
					result.put(ScoreCardLoan.column.報表亂碼, IDGenerator.getUUID());
					BigDecimal varA = Util.parseBigDecimal(prop.getProperty(ScoreCardLoan.column.基準底分A));
					BigDecimal varB = Util.parseBigDecimal(prop.getProperty(ScoreCardLoan.column.基準底分B));
					BigDecimal varC = Util.parseBigDecimal(prop.getProperty(ScoreCardLoan.column.基準底分常數項));
					result.put(ScoreCardLoan.column.基準底分A, varA);
					result.put(ScoreCardLoan.column.基準底分B, varB);
					result.put(ScoreCardLoan.column.基準底分常數項, varC);
					result.put(ScoreCardLoan.column.基準底分版本, Util.trim(prop.getProperty(ScoreCardLoan.column.基準底分版本)));
					BigDecimal scrNum11 = Util.parseBigDecimal(result.get(ScoreCardLoan.column.合計變量得分));
					BigDecimal scrNum12 = ( varA.add( (varB.multiply(varC)) )).setScale(4, BigDecimal.ROUND_HALF_UP);
					BigDecimal scrNum13 = scrNum11.add(scrNum12);
					result.put(ScoreCardLoan.column.基準底分, scrNum12);
					result.put(ScoreCardLoan.column.初始評分, scrNum13);
					
					double pd = (1 / (1 + Math.exp(  ((scrNum13.subtract(varA)).divide(varB, MathContext.DECIMAL32)).doubleValue()   )));
					result.put(ScoreCardLoan.column.預測壞率, Util.Overflow(pd, 4));
					
					if(true){//評等
						JSONObject level = scoreCardLoan(ScoreCardLoan.type.卡友貸評等, result, varVer);
						String pRating = Util.trim(level.get(ScoreCardLoan.cardLoanGrade.等級));
						
						Integer adj_j10 = 0;
						String sprtRating = "";
						if(Util.isNotEmpty(pRating) && Util.isInteger(pRating)){
							if(Util.equals("0",j10_score) || Util.equals("200",j10_score)){
								sprtRating = "10";
							}else{
								adj_j10 = get_CardLoan_adj_j10_step1(j10_score, kcs003_reason_code1 );							
								sprtRating = Util.trim(OverSeaUtil.rating_min1_max10(Util.parseInt(pRating) - adj_j10));
							}
							
							adj_j10 = Util.parseInt(pRating) - Util.parseInt(sprtRating);
						}			
						
						if(true){
							result.put(ScoreCardLoan.column.升降等_聯徵J10, Util.trim(adj_j10));
							result.put(ScoreCardLoan.column.支援評等, Util.trim(sprtRating));
						}					
						
						result.put(ScoreCardLoan.column.初始評等, pRating);
						result.put(ScoreCardLoan.column.調整評等, "");
						result.put(ScoreCardLoan.column.評等調整日期, "");
						result.put(ScoreCardLoan.column.最終評等, Util.trim(sprtRating));
			
						result.put(ScoreCardLoan.column.調整狀態, "");
						result.put(ScoreCardLoan.column.調整註記, "");
						result.put(ScoreCardLoan.column.調整理由, "");
					}
					if(true){//違約機率
						result.put(ScoreCardLoan.column.違約機率_預估2年期_短, "");			
						result.put(ScoreCardLoan.column.違約機率_預估3年期_中長, "");
						result.put(ScoreCardLoan.column.違約機率_預估1年期_短, "");
						result.put(ScoreCardLoan.column.違約機率_預估1年期_中長, "");
						JSONObject cardLoanDR = scoreCardLoan(ScoreCardLoan.type.卡友貸違約機率, result, varVer);
						setCardLoanDR(cardLoanDR, result);
					}
				}
				// clear
				prop.clear();
				prop = null;
			}	
		}
		
	}	
	
	/**
		>660, 不降等
		>580 且 <=660, 由初始評等降1等
		>500 且 <=580, 由初始評等降2等
		>420 且 <=500, 由初始評等降3等
		<=420但不是固定或無法評分, 由初始評等降4等
	*/
	private int get_CardLoan_adj_j10_step1(String s_j10_score, String kcs003_reason_code1){
		if(Util.isEmpty(s_j10_score) && Util.isEmpty(kcs003_reason_code1)){
			return 0;
		}
		
		int j10_score = Util.parseInt(s_j10_score);
		if(j10_score>660){
			return 0;
		}else if(j10_score>580 && j10_score<=660){
			return -1;
		}else if(j10_score>500 && j10_score<=580){
			return -2;
		}else if(j10_score>420 && j10_score<=500){
			return -3;
		}else if(j10_score<=420){
			if(j10_score==0){ // 無法評分
				return -10;	
			}else if(j10_score==200){ // 固定評分
				return -10;	
			}
			return -4;
		}
		return -10;	
	}
	
	
	/**
	 * 依keyPrefix取得Property字串陣列
	 * 
	 * @param bundle
	 * @param keyPrefix
	 * @return
	 */
	private String[] getPropertyStringArray(PropertyResourceBundle bundle,
			String keyPrefix) {

		Enumeration<String> keys = bundle.getKeys();
		ArrayList<String> temp = new ArrayList<String>();

		for (Enumeration<String> e = keys; keys.hasMoreElements();) {
			String key = e.nextElement();
			if (key.startsWith(keyPrefix)) {
				temp.add(key);
			}
		}

		Collections.sort(temp);

		int size = temp.size();
		String[] result = new String[size];
		for (int i = 0; i < size; i++) {
			result[i] = bundle.getString(temp.get(i));
		}

		return result;
	}

	/**
	 * 取得Property Resource
	 * 
	 * @return
	 */
	private PropertyResourceBundle getPropertyResourceBundle(String fileLoc) {
		PropertyResourceBundle result = null;

		InputStream stream = Thread.currentThread().getContextClassLoader()
				.getResourceAsStream(fileLoc);

		if (stream != null) {
			try {
				result = new PropertyResourceBundle(stream);
			} catch (IOException e) {
				logger.error("[PropertyResourceBundle]",e);
			} finally {
				try {
					stream.close();
				} catch (IOException e) {
					logger.error("[stream.close]",e);
				}
			}
		}
		return result;
	}

	private static Properties getProperty(String name) {
		Properties prop = new Properties();
		InputStream stream = Thread.currentThread().getContextClassLoader()
				.getResourceAsStream(name);
		try {
			prop.load(stream);
		} catch (IOException e) {
			logger.error("[Properties.load]",e);
		} finally {
			try {
				if (stream != null)
					stream.close();
			} catch (IOException e) {
				logger.error("[InputStream.close]",e);
			}
		}

		return prop;
	}
	
	/**
	 * 判斷是否連續2期
	 * 
	 * @param custId
	 * @param t12Date
	 * @param highDate
	 * @return
	 */
	private boolean chkItem6(String custId, String prodId, String QDATE,
			String t12Date, String highDate){
		List<Map<String, Object>> list = ejcicService.getKRM040CardPayCode4(custId, prodId, QDATE, t12Date, highDate);
		
		if(list.size() >= 2){
			HashSet<String> yyymm = new HashSet<String>();
			for(Map<String, Object> map:list ){
				//取出的資料為 1010301, 取左邊的5碼(民國年月)
				yyymm.add(StringUtils.left(Util.trim(map.get("BILL_DATE1")), 5));
			}
			for(Map<String, Object> map:list ){
				TWNDate rocDate = TWNDate.valueOf(Util.trim(map.get("BILL_DATE1")));
				rocDate.add(Calendar.MONTH, 1);
				String nextM = StringUtils.left(rocDate.toTW(),5);
				if(yyymm.contains(nextM)){
					return true;
				}
			}
		}
		return false;
	}
	
	/**
	 * 判斷是否連續2期
	 * 
	 * @param custId
	 * @param t12Date
	 * @param highDate
	 * @return
	 */
	private boolean test_chkItem6(String custId){
		List<Map<String, Object>> list = dwdbBASEService.test_DwKrm040Cc_getKRM040CardPayCode4(custId);
		if(list.size() >= 2){
			HashSet<String> yyymm = new HashSet<String>();
			for(Map<String, Object> map:list ){
				//取出的資料為 1010301, 取左邊的5碼(民國年月)
				yyymm.add(StringUtils.left(Util.trim(map.get("BILL_DATE1")), 5));
			}
			for(Map<String, Object> map:list ){
				TWNDate rocDate = TWNDate.valueOf(Util.trim(map.get("BILL_DATE1")));
				rocDate.add(Calendar.MONTH, 1);
				String nextM = StringUtils.left(rocDate.toTW(),5);
				if(yyymm.contains(nextM)){
					return true;
				}
			}
		}
		return false;
	}

	@Override
	public void setHouseLoanDR(JSONObject houseLoanDR, JSONObject target) {
		target.put(Score.column.違約機率_預估3年期, houseLoanDR.get(Score.houseLoanDR.違約機率_預估3年期));			
		target.put(Score.column.違約機率_預估1年期, houseLoanDR.get(Score.houseLoanDR.違約機率_預估1年期));
	}

	@Override
	public void setNotHouseLoanDR(JSONObject notHouseLoanDR, JSONObject target) {
		target.put(ScoreNotHouseLoan.column.違約機率_預估2年期_短, notHouseLoanDR.get(ScoreNotHouseLoan.notHouseLoanDR.違約機率_預估2年期_短));			
		target.put(ScoreNotHouseLoan.column.違約機率_預估3年期_中長, notHouseLoanDR.get(ScoreNotHouseLoan.notHouseLoanDR.違約機率_預估3年期_中長));
		target.put(ScoreNotHouseLoan.column.違約機率_預估1年期_短, notHouseLoanDR.get(ScoreNotHouseLoan.notHouseLoanDR.違約機率_預估1年期_短));
		target.put(ScoreNotHouseLoan.column.違約機率_預估1年期_中長, notHouseLoanDR.get(ScoreNotHouseLoan.notHouseLoanDR.違約機率_預估1年期_中長));
	}
	
	@Override
	public void setCardLoanDR(JSONObject cardLoanDR, JSONObject target) {
		target.put(ScoreCardLoan.column.違約機率_預估2年期_短, cardLoanDR.get(ScoreCardLoan.cardLoanDR.違約機率_預估2年期_短));			
		target.put(ScoreCardLoan.column.違約機率_預估3年期_中長, cardLoanDR.get(ScoreCardLoan.cardLoanDR.違約機率_預估3年期_中長));
		target.put(ScoreCardLoan.column.違約機率_預估1年期_短, cardLoanDR.get(ScoreCardLoan.cardLoanDR.違約機率_預估1年期_短));
		target.put(ScoreCardLoan.column.違約機率_預估1年期_中長, cardLoanDR.get(ScoreCardLoan.cardLoanDR.違約機率_預估1年期_中長));
	}
	
	
	@Override	
	public void clear_unUsedColumn(C101S01G model){
		if(model==null){
			return;
		}
		boolean clearV1_3 = false;
		boolean clearV2_0 = false;
		boolean clearV2_1 = false;
		boolean clearV3_0 = false;
		if (Util.equals(ClsScoreUtil.V1_3_HOUSE_LOAN, model.getVarVer())) {
			clearV2_0 = true;
			clearV2_1 = true;
			clearV3_0 = true;
		}else if (Util.equals(ClsScoreUtil.V2_0_HOUSE_LOAN, model.getVarVer())
				|| Util.equals(ClsScoreUtil.V2_1_HOUSE_LOAN, model.getVarVer())) {
			clearV1_3 = true;
			clearV3_0 = true;
		}else if (Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN, model.getVarVer())) {
			clearV1_3 = true;
			clearV2_0 = true;
			model.setDr_3YR(null);	
		}
		if(!Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN, model.getVarVer())){
			//非4.0項目，把截距、斜率清掉
			model.setInterCept(null);
			model.setSlope(null);
		}
		
		if(clearV1_3){ //清除1.3相關因子、分數欄位
			model.setScrNum02(null);
			model.setScrNum03(null);
			//由 D07(chkAmt01)決定, 保留因子(因房貸1.3及2.0都有用到), 清除 Scr 的值 
//			model.setChkAmt01(null);//聯徵查詢月份前一月或二個月之無擔保授信餘額
			model.setScrNum04(null);
			model.setChkAmt02(null);//D15
			model.setScrNum05(null);
			model.setInqQty(null);//N06
			model.setScrNum06(null);
			model.setAvgRate02(null);//R10
			model.setScrNum08(null);
			model.setScrNum10(null);//P69_P19
			model.setChkNum2(null);
		}
		
		if(clearV2_0){ //清除2.0相關因子、分數欄位
			model.setScrSeniority_G(null);//年資分數
			model.setSeniority(null);
			model.setItemD07_DIV_PINCOME(null);//D07
			model.setScrD07_DIV_PINCOME(null);
			model.setItemN18(null);//N18
			model.setScrN18(null);
			model.setScrP68P19(null);//P68_P19(分數)
			model.setScrZ03(null);//Z03
			model.setItemZ03(null);
		}
		
		if(clearV3_0){ //清除3.0相關因子、分數欄位
			model.setYRate(null);//夫妻負債比率
			model.setScrYRate(null);
			model.setScrJobTitle(null);//職稱分數
			model.setItemD42(null);//當月有效信用卡主卡平均信用額度(仟元)
			model.setScrD42(null);
			model.setItemN01(null);//近3個月非Z類申請查詢總次數(近三個月本行查詢不列入計算)
			model.setScrEdu(null);//學歷分數
			model.setScrP68(null);//近6個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用的次數
			model.setScrP19(null);	//近12個月信用卡繳款狀況出現全額繳清無延遲次數(不含無須繳款)
		}
		
		
		
		//------處理不同模型共用欄位------
		if(clearV1_3 && clearV2_0){ //1.3、2.0共用欄位
			model.setScrNum01(null); 
			model.setYFamAmt(null);
			model.setChkNum1(null);
			model.setScrNum09(null);
		}
		
		if(clearV1_3 && clearV3_0){ //1.3、3.0共用欄位
			model.setAvgRate01(null);
			model.setScrNum07(null);
//			model.setJobType1("");
		}
		
		if(clearV1_3 && clearV3_0){ //2.0、3.0共用欄位
//			model.setItemP68(null);
//			model.setChkNum3(null);
		}
		if(clearV2_1 && clearV3_0){ //2.1、3.0共用欄位
			model.setItemN22(null);
			model.setScrN22(null);
		}
	}
	
	@Override
	public void clear_unUsedColumn(C101S01Q model){
		if(model==null){
			return;
		}
		boolean clearV1_0 = false;
		boolean clearV2_0 = false; //(1.9、2.0、2.1)
		boolean clearV3_0 = false; //(3.0、3.1)
		boolean clearV4_0 = false;
		
		if(Util.equals(ClsScoreUtil.V1_0_NOT_HOUSE_LOAN, model.getVarVer())){
			clearV2_0 = true;
			clearV3_0 = true;
			clearV4_0 = true;
		}else if(Util.equals(ClsScoreUtil.V1_9_NOT_HOUSE_LOAN, model.getVarVer()) 
				|| Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN, model.getVarVer()) 
				|| Util.equals(ClsScoreUtil.V2_1_NOT_HOUSE_LOAN, model.getVarVer())){
			clearV1_0 = true;
			clearV3_0 = true;
			clearV4_0 = true;
		}else if(Util.equals(ClsScoreUtil.V3_0_NOT_HOUSE_LOAN, model.getVarVer()) 
				|| Util.equals(ClsScoreUtil.V3_1_NOT_HOUSE_LOAN, model.getVarVer())){
			clearV1_0 = true;
			clearV2_0 = true;
			clearV4_0 = true;
		}else if(Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, model.getVarVer())){
			clearV1_0 = true;
			clearV2_0 = true;
			clearV3_0 = true;	
			//清除DR_2YR、DR_3YR
			model.setDr_2YR(null);
			model.setDr_3YR(null);	
		}
		if(!Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, model.getVarVer())){
			//非4.0項目，把截距、斜率清掉
			model.setInterCept(null);
			model.setSlope(null);
		}
		
		//避免和房貸的P69_P19重複
		model.setGrpNum10(null);
		if(clearV1_0){
			model.setYPay(null); //個人年收入分數
			model.setScrypay(null);
			model.setNochkItem02(null); //A21因子
			model.setNoscrItem02(null);
			model.setNochkItem04(null); //D53因子
			model.setNoscrItem04(null);
		}
		if(clearV2_0){
			//2.0、2.1版本的因子，都跟1.0、3.0有重複，無獨立項目
		}
		if(clearV3_0){
			model.setNochkItemP25(null);//P25
			model.setNoscrItemP25(null);
			model.setNochkItemR01(null);//R01
			model.setNoscrItemR01(null);
		}
		if(clearV4_0){
			model.setItemP01(null);//P01
			model.setScrP01(null);
			model.setLoanBalSByid(null);//有擔餘額
			model.setScrLoanBalSByid(null);
			model.setItemMaxR01(null);//R01
			model.setScrMaxR01(null);
		}
		if(clearV2_0 && clearV4_0){//2.0、4.0共用欄位
			model.setEducation(null);
			model.setScreducation(null);
		}
		if(clearV1_0 && clearV2_0){//1.0、2.0共用欄位
			model.setSeniority(null); 
			model.setScrseniority(null);
			model.setNochkItem01(null); //D63因子
			model.setNoscrItem01(null);
			model.setNochkItem03(null);//A11
			model.setNoscrItem03(null);
		}
		if(clearV1_0 && clearV4_0){//1.0、4.0共用欄位
			model.setEducation(null);
			model.setScreducation(null);
		}
		
		if(clearV2_0 && clearV3_0){//2.0、3.0共用欄位
//			model.setPIncome(null);
			model.setScrPIncome(null);
			model.setNochkItemD07(null);
			model.setNoscrItemD07(null);
			model.setNochkItemN06(null);
			model.setNoscrItemN06(null);
			model.setNochkItemP68(null);
			model.setNochkItemP19(null);
			model.setNoscrItemP68P19(null);
			model.setGrpP68P19(null);
		}
		if(clearV3_0 && clearV4_0){//3.0、4.0共用欄位
			model.setNochkItemDrate(null);
			model.setNoscrItemDrate(null);
		}
		
	}
	
	@Override
	public void clear_unUsedColumn(C101S01R model){
		if(model==null){
			return;
		}
		
		boolean clearV2_0 = false; //(2.1)
		boolean clearV3_0 = false; //(3.0、3.1)
		boolean clearV4_0 = false;
		
		if( Util.equals(ClsScoreUtil.V2_1_CARD_LOAN, model.getVarVer())){
			clearV3_0 = true;
			clearV4_0 = true;
		}else if(Util.equals(ClsScoreUtil.V3_0_CARD_LOAN, model.getVarVer()) 
				|| Util.equals(ClsScoreUtil.V3_1_CARD_LOAN, model.getVarVer())){
			clearV2_0 = true;
			clearV4_0 = true;
		}else if(Util.equals(ClsScoreUtil.V4_0_CARD_LOAN, model.getVarVer())){
			clearV2_0 = true;
			clearV3_0 = true;	
			//清除DR_2YR、DR_3YR
			model.setDr_2YR(null);
			model.setDr_3YR(null);	
		}
		if(!Util.equals(ClsScoreUtil.V4_0_CARD_LOAN, model.getVarVer())){
			//非4.0項目，把截距、斜率清掉
			model.setInterCept(null);
			model.setSlope(null);
		}
		
		//避免和房貸的P69_P19重複
		model.setGrpNum10(null);
		if(clearV2_0){
			model.setSeniority(null); 
			model.setScrseniority(null);
			model.setNochkItem01(null); //D63因子
			model.setNoscrItem01(null);
			model.setNochkItem03(null);//A11
			model.setNoscrItem03(null);
		}
		if(clearV3_0){
			model.setNochkItemP25(null);//P25
			model.setNoscrItemP25(null);
			model.setNochkItemR01(null);//R01
			model.setNoscrItemR01(null);
		}
		if(clearV4_0){
			model.setEducation(null);
			model.setScreducation(null);
			model.setItemP01(null);//P01
			model.setScrP01(null);
			model.setLoanBalSByid(null);//有擔餘額
			model.setScrLoanBalSByid(null);
			model.setItemMaxR01(null);//R01
			model.setScrMaxR01(null);
		}
		if(clearV2_0 && clearV4_0){//2.0、4.0共用欄位
			model.setEducation(null);
			model.setScreducation(null);
		}
		if(clearV2_0 && clearV3_0){//2.0、3.0共用欄位
			model.setPIncome(null);
			model.setScrPIncome(null);
			model.setNochkItemD07(null);
			model.setNoscrItemD07(null);
			model.setNochkItemN06(null);
			model.setNoscrItemN06(null);
			model.setNochkItemP68(null);
			model.setNochkItemP19(null);
			model.setNoscrItemP68P19(null);
			model.setGrpP68P19(null);
		}
		if(clearV3_0 && clearV4_0){//3.0、4.0共用欄位
			model.setNochkItemDrate(null);
			model.setNoscrItemDrate(null);
		}
	}
	
	
	private void dump_input_val(JSONObject data){
		dump_input_val(data, false);
	}
	private void dump_input_val(JSONObject data, boolean show){
		Map<String, Object> m = LMSUtil.jsonobject_toMap(data);
		/*
		//===================================
		//房貸2.0 因子 
		String[] keyArr = {"yFamAmt"
				, "seniority"
				, "chkAmt01", "pIncome"
				, "itemD07_DIV_PINCOME"
				, "itemN18", "jobTitle"
				, "chkNum1"  //P25
				, "itemP68", "chkNum3"
				, "itemZ03"};
		*/
		
		//===================================
		//非房貸 3.0 因子
		String[] keyArr = ClsScoreUtil.get_input_factor("Q", ClsScoreUtil.V3_0_NOT_HOUSE_LOAN);
		
		if(keyArr==null || keyArr.length==0){
			return;
		}
		List<String> r = new ArrayList<String>();
		r.add("ScoreServiceImpl => dump_input_val(JSONObject data)");
		for(String key: keyArr){
			r.add("["+Util.addSpaceWithValue(key, 22)+"]"+m.get(key));
		}
		if(show){
			logger.debug(StringUtils.join(r, "\r\n"));	
		}		
	}
	
	@Override
	public String get_Version_HouseLoan(){
		if(LMSUtil.cmpDate(new Date(), ">=", this.clsScore_ActiveDate("G", ClsScoreUtil.V3_0_HOUSE_LOAN))){
			return ClsScoreUtil.V3_0_HOUSE_LOAN;
		}else if(LMSUtil.cmpDate(new Date(), ">=", this.clsScore_ActiveDate("G", ClsScoreUtil.V2_1_HOUSE_LOAN))){
			return ClsScoreUtil.V2_1_HOUSE_LOAN;
		}else if(LMSUtil.cmpDate(new Date(), ">=", this.clsScore_ActiveDate("G", ClsScoreUtil.V2_0_HOUSE_LOAN))){
			return ClsScoreUtil.V2_0_HOUSE_LOAN;
		}else{
			return ClsScoreUtil.V1_3_HOUSE_LOAN;
		}		
	}
	
	@Override
	public String get_Version_NotHouseLoan(){
		if(LMSUtil.cmpDate(new Date(), ">=", this.clsScore_ActiveDate("Q", ClsScoreUtil.V4_0_NOT_HOUSE_LOAN))){
			//自2022-MM-DD後，改用非房貸模型 4.0 
			return ClsScoreUtil.V4_0_NOT_HOUSE_LOAN;
		}else if(LMSUtil.cmpDate(new Date(), ">=", this.clsScore_ActiveDate("Q", ClsScoreUtil.V3_1_NOT_HOUSE_LOAN))){
			//自2019-MM-DD後，改用非房貸模型 3.1 
			return ClsScoreUtil.V3_1_NOT_HOUSE_LOAN;
		}else if(LMSUtil.cmpDate(new Date(), ">=", this.clsScore_ActiveDate("Q", ClsScoreUtil.V3_0_NOT_HOUSE_LOAN))){
			//自2019-MM-DD後，改用非房貸模型 3.0 
			return ClsScoreUtil.V3_0_NOT_HOUSE_LOAN;
		}else if(LMSUtil.cmpDate(new Date(), ">=", this.clsScore_ActiveDate("Q", ClsScoreUtil.V2_1_NOT_HOUSE_LOAN))){
			//自2019-08-26後，改用非房貸模型 2.1 
			return ClsScoreUtil.V2_1_NOT_HOUSE_LOAN;
		}else if(LMSUtil.cmpDate(new Date(), ">=", this.clsScore_ActiveDate("Q", ClsScoreUtil.V2_0_NOT_HOUSE_LOAN))){
			//自2015-01-01後，改用非房貸模型 2.0
			return ClsScoreUtil.V2_0_NOT_HOUSE_LOAN; 
		}else{
			return ClsScoreUtil.V1_0_NOT_HOUSE_LOAN;
		}

		//return LMSUtil.V1_9_NOT_HOUSE_LOAN;		
	}

	@Override
	public String get_Version_CardLoan(){
		if(LMSUtil.cmpDate(new Date(), ">=", this.clsScore_ActiveDate("R", ClsScoreUtil.V4_0_CARD_LOAN))){
			//自2019-MM-DD後，改用模型 3.0 
			return ClsScoreUtil.V4_0_CARD_LOAN;
		}else if(LMSUtil.cmpDate(new Date(), ">=", this.clsScore_ActiveDate("R", ClsScoreUtil.V3_1_CARD_LOAN))){
			//自2019-MM-DD後，改用模型 3.1
			return ClsScoreUtil.V3_1_CARD_LOAN;
		}else if(LMSUtil.cmpDate(new Date(), ">=", this.clsScore_ActiveDate("R", ClsScoreUtil.V3_0_CARD_LOAN))){
			//自2019-MM-DD後，改用模型 3.0 
			return ClsScoreUtil.V3_0_CARD_LOAN;
		}else {
			return ClsScoreUtil.V2_1_CARD_LOAN; //J-108-0105
		}
	}
	
	private Date[] _clsScore_configDate(String tbl, String varVer) {
		String d1 = "0001-01-01";
		String d2 = "9999-12-31";// 生效日
		String d3 = "1911-01-01";// 預設無 buffer(過渡迄日)
		if (Util.equals("G", tbl)) {
			if (Util.equals(ClsScoreUtil.V2_0_HOUSE_LOAN, varVer)) {
				/*
				本行新改版「消金房貸申請信用評等模型」訂於106年11月30日上線
				有關房貸在途案件之處理，考量營業單位承辦案件之作業時程，聯徵中心及票信查詢日期為106年11月20日至106年11月29日編製中之簽報書，106年12月15日前仍可送呈覆核，106年12月16日起均須以新版模型評等後，才能送呈覆核。
				*/
				d1 = "2017-11-20";
				d2 = "2017-11-30";// G2.0的生效日
				d3 = "2017-12-15";
			}else if(Util.equals(ClsScoreUtil.V2_1_HOUSE_LOAN, varVer)){
				d1 = "2023-10-31";
				d2 = "2023-10-31";// G2.1的生效日
				d3 = "2023-10-31";
			}else if(Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN, varVer)){
//				d1 = "2024-04-19";
//				d2 = "2024-04-19";// G3.0的生效日
//				d3 = "2024-04-19";
				CodeType scoreHouse_3_0 = codeTypeService.findByCodeTypeAndCodeValue("scoreHouse_3_0_Date", ClsScoreUtil.V3_0_HOUSE_LOAN, "zh_TW");
				d1 = scoreHouse_3_0.getCodeDesc();
				d2 = scoreHouse_3_0.getCodeDesc2(); //啟用日期
				d3 = scoreHouse_3_0.getCodeDesc3(); //
			}
		} else if (Util.equals("Q", tbl)) {
			if (Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN, varVer)) {
				d1 = "2014-12-21";
				d2 = "2015-01-01";// Q2.0的生效日
				d3 = "2015-01-15";
			}else if (Util.equals(ClsScoreUtil.V2_1_NOT_HOUSE_LOAN, varVer)) {
				d1 = "2019-08-23"; //J-108-0105	
				d2 = "2019-08-30";// Q2.1的生效日
				d3 = "2019-09-15";
			}else if (Util.equals(ClsScoreUtil.V3_0_NOT_HOUSE_LOAN, varVer)) {
				d1 = "2019-12-02";
				d2 = "2019-12-16"; 
				d3 = "2019-12-29";
			}else if (Util.equals(ClsScoreUtil.V3_1_NOT_HOUSE_LOAN, varVer)) {
				d1 = "2023-10-31"; 
				d2 = "2023-10-31"; 
				d3 = "2023-10-31"; 
			}else if (Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, varVer)) {
				//改用DB進行設定
				CodeType scoreNotHouse_4_0 = codeTypeService.findByCodeTypeAndCodeValue("scoreNotHouse_4_0_Date", ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, "zh_TW");
				d1 = scoreNotHouse_4_0.getCodeDesc();
				d2 = scoreNotHouse_4_0.getCodeDesc2(); //啟用日期
				d3 = scoreNotHouse_4_0.getCodeDesc3(); //
			}
		} else if (Util.equals("R", tbl)) {
			if (Util.equals(ClsScoreUtil.V2_1_CARD_LOAN, varVer)) {
				d2 = "2019-08-30";
			}else if (Util.equals(ClsScoreUtil.V3_0_CARD_LOAN, varVer)) {
				d1 = "2019-12-02"; 
				d2 = "2019-12-16"; 
				d3 = "2019-12-29";
			}else if (Util.equals(ClsScoreUtil.V3_1_CARD_LOAN, varVer)) {
				d1 = "2023-10-31"; 
				d2 = "2023-10-31"; 
				d3 = "2023-10-31"; 
			}else if (Util.equals(ClsScoreUtil.V4_0_CARD_LOAN, varVer)) {
				CodeType scoreCard_4_0 = codeTypeService.findByCodeTypeAndCodeValue("scoreCard_4_0_Date", ClsScoreUtil.V4_0_CARD_LOAN, "zh_TW");
				d1 = scoreCard_4_0.getCodeDesc();
				d2 = scoreCard_4_0.getCodeDesc2(); //啟用日期
				d3 = scoreCard_4_0.getCodeDesc3(); //
			}
		}
		return new Date[] { CapDate.parseDate(d1), CapDate.parseDate(d2),
				CapDate.parseDate(d3) };
	}
	
	
	public Date clsScore_ActiveDate(String tbl, String varVer) {
		return _clsScore_configDate(tbl, varVer)[1];
	}

	// ==================================================
	public boolean clsScore_inBufferPeriod(String tbl, String varVer) {
		Date buffer_end_date = _clsScore_configDate(tbl, varVer)[2];

		if (LMSUtil.cmpDate(new Date(), ">=", clsScore_ActiveDate(tbl, varVer))
				&& LMSUtil.cmpDate(new Date(), "<=", buffer_end_date)) {
			return true;
		}

		return false;
	}

	// ==================================================
	public boolean clsScore_inBufferPeriodQdateExpired(String tbl,
			String varVer, List<C120S01E> list) {
		String cmpSign = "<";
		Date cmp_qDate = _clsScore_configDate(tbl, varVer)[0];

		for (C120S01E c120s01e : list) {
			// 檢查 QDATE 是否在允許區間
			if ((c120s01e.getEJcicQDate() != null && LMSUtil.cmpDate(
					c120s01e.getEJcicQDate(), cmpSign, cmp_qDate))
					|| (c120s01e.getEChkQDate() != null && LMSUtil.cmpDate(
							c120s01e.getEChkQDate(), cmpSign, cmp_qDate))) {
				return true;
			}
		}
		return false;
	}
	
}