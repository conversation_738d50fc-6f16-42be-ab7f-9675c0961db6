<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="
http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-2.0.xsd">
 
	<util:map id="pureSql" map-class="java.util.HashMap" key-type="java.lang.String" >

        <!-- J-103-0165 2014/06/12 begin Web e-Loan授信管理配合額度序號相關修改。 -->
		<entry key="J-103-0165-001.SELECT.Before.01">
		    <value>
		   	    select mainid,custid,dupno,cntrno,commSno from LMS.l140m01A where mainid IN ('0d4fb20e275647fc87cb2d4cab90eb76','4829d4c483154832bdbb54c85727613a','d8a174bba8b64668a7fa1954871ecd29','28755643e2e243b5a27508de35c48777','eedf0e7dec6642a49ce4f0ef9cba00f5','f93215991ba0410ab855f9d6c1be9a6f')
			</value>
		</entry>
		<entry key="J-103-0165-001.SELECT.Before.02">
			<value>
				select mainid,shareNo from LMS.l140m01E where mainid IN ('0d4fb20e275647fc87cb2d4cab90eb76','4829d4c483154832bdbb54c85727613a','d8a174bba8b64668a7fa1954871ecd29','28755643e2e243b5a27508de35c48777','eedf0e7dec6642a49ce4f0ef9cba00f5','f93215991ba0410ab855f9d6c1be9a6f')
			</value>
		</entry>
		 
		
		<entry key="J-103-0165-001.01">
			<value>update lms.l140m01a set cntrno = '201109600171',commSno='201109900282' where mainid = '0d4fb20e275647fc87cb2d4cab90eb76' and cntrno = '201110300102'</value>
		</entry>
		<entry key="J-103-0165-001.02">
			<value>update lms.l140m01a set cntrno = '201109600171',commSno='201109900282' where mainid = 'f93215991ba0410ab855f9d6c1be9a6f' and cntrno = '201110300102'</value>
		</entry>
		<entry key="J-103-0165-001.03">
			<value>update lms.l140m01a set cntrno = '201109900282',commSno='201109600171' where mainid = 'd8a174bba8b64668a7fa1954871ecd29' and cntrno = '201110300101'</value>
		</entry>
		<entry key="J-103-0165-001.04">
			<value>update lms.l140m01a set cntrno = '201109900282',commSno='201109600171' where mainid = 'eedf0e7dec6642a49ce4f0ef9cba00f5' and cntrno = '201110300101'</value>
		</entry>
		<entry key="J-103-0165-001.05">
			<value>update lms.l140m01a set cntrno = '201110300012'  where mainid = '4829d4c483154832bdbb54c85727613a' and cntrno = '201110300100'</value>
		</entry>
		<entry key="J-103-0165-001.06">
			<value>update lms.l140m01a set cntrno = '201110300012'  where mainid = '28755643e2e243b5a27508de35c48777' and cntrno = '201110300100'</value>
		</entry>
		<entry key="J-103-0165-001.07">
			<value>update lms.l140m01e set shareNo = '201109600171' where mainid = '0d4fb20e275647fc87cb2d4cab90eb76' and shareNo = '201110300102'</value>
		</entry>
		<entry key="J-103-0165-001.08">
			<value>update lms.l140m01e set shareNo = '201109600171' where mainid = 'f93215991ba0410ab855f9d6c1be9a6f' and shareNo = '201110300102'</value>
		</entry>
		
		<entry key="J-103-0165-001.SELECT.After.01">
		    <value>
		   	    select mainid,custid,dupno,cntrno,commSno from LMS.l140m01A where mainid IN ('0d4fb20e275647fc87cb2d4cab90eb76','4829d4c483154832bdbb54c85727613a','d8a174bba8b64668a7fa1954871ecd29','28755643e2e243b5a27508de35c48777','eedf0e7dec6642a49ce4f0ef9cba00f5','f93215991ba0410ab855f9d6c1be9a6f')
			</value>
		</entry>
		<entry key="J-103-0165-001.SELECT.After.02">
			<value>
				select mainid,shareNo from LMS.l140m01E where mainid IN ('0d4fb20e275647fc87cb2d4cab90eb76','4829d4c483154832bdbb54c85727613a','d8a174bba8b64668a7fa1954871ecd29','28755643e2e243b5a27508de35c48777','eedf0e7dec6642a49ce4f0ef9cba00f5','f93215991ba0410ab855f9d6c1be9a6f')
			</value>
		</entry>
		
		<!-- J-103-0165 2014/06/12 end -->
		
		
		
		<!-- J-103-0180 2014/07/01 begin Web e-Loan授信管理配合額度序號相關修改。 -->
		<!-- 寶輝建設股份有限公司(86623456)授信額度之E-LOAN額度序號(004109200079)更改為(0041092A0079)。-->
		<entry key="J-103-0180-001.SELECT.Before.01">
		    <value>
		   	    select mainid,custid,dupno,cntrno,proPerty from LMS.l140m01A where mainid IN ('2716a8ec82ee439db5c7c7b36914505b','09ed478d25144ab79638c6f2d7c4d805')
			</value>
		</entry>
		

		<entry key="J-103-0180-001.UPDATE.01">
			<value>update lms.l140m01a set cntrno = '0041092A0079',proPerty='1' where mainid IN ('2716a8ec82ee439db5c7c7b36914505b','09ed478d25144ab79638c6f2d7c4d805') and cntrno = '004109200079'</value>
		</entry>
		 
		
		<entry key="J-103-0180-001.SELECT.After.01">
		    <value>
		   	    select mainid,custid,dupno,cntrno,proPerty from LMS.l140m01A where mainid IN ('2716a8ec82ee439db5c7c7b36914505b','09ed478d25144ab79638c6f2d7c4d805')
			</value>
		</entry>
		
		
		<!-- J-103-0180 2014/07/01 end -->
		
		<!-- J-103-0287 2014/12/18 begin Web e-Loan授信管理配合額度序號相關修改。 -->
		
		<!--
		更新資料前查詢
		select custid,dupno,cntrno,COMMSNO from LMS.l140m01A where MAINID IN 
		(
			'c8d7440d85f9481490af186d1dc4230f',
			'ede929aae5ae4ba5ae9c878c6a7f912e',
			'7b4e1120b1d545babd8ed03a2278598e',
			'7e3ae6e9e9a74e7799601056a10a2385',
			'a48864fd834c4782a87b773f1ba84f70',
			'a56db634f58a4614b1e06fb4069d6ec8',
			'a3e2e6a73d0c4d37be480832b2f14954',
			'f70d5c5b7e224d90bd601017ea30f3ed',
			'18d5c38aee394222b30a09a92383b7cb',
			'7ac377a6c5cd446eb381d28311c16cb8',
			'185e357585ba44909704bae0b607d604',
			'186b683b3a0d4f5bac800c21d228cf01',
			'e12e94ac5bad4b928e3e29ff107b9baa',
			'2662ac10172144a0a11a2ea43bb5c486',
			'c9d1619acab54afd92a1a20938aaa152',
			'513d3bd633434c789f2c344674bf70d7'
		);
		
		select * from LMS.l140m01E where MAINID IN 
		(
			'c8d7440d85f9481490af186d1dc4230f',
			'ede929aae5ae4ba5ae9c878c6a7f912e',
			'7b4e1120b1d545babd8ed03a2278598e',
			'7e3ae6e9e9a74e7799601056a10a2385',
			'a48864fd834c4782a87b773f1ba84f70',
			'a56db634f58a4614b1e06fb4069d6ec8',
			'a3e2e6a73d0c4d37be480832b2f14954',
			'f70d5c5b7e224d90bd601017ea30f3ed',
			'18d5c38aee394222b30a09a92383b7cb',
			'7ac377a6c5cd446eb381d28311c16cb8',
			'185e357585ba44909704bae0b607d604',
			'186b683b3a0d4f5bac800c21d228cf01',
			'e12e94ac5bad4b928e3e29ff107b9baa',
			'2662ac10172144a0a11a2ea43bb5c486',
			'c9d1619acab54afd92a1a20938aaa152',
			'513d3bd633434c789f2c344674bf70d7'
		);
		
		select * from LMS.l120m01A where MAINID IN 
		(
			'd01f388b3ad14014a95d0d9143a033db',
			'0fbbd971872e49c1b07d22b724e82613',
			'478157354f6942f08f1c6ed5f8863226',
			'10c09da54c9146d4a02d35a4085e1aae',
			'b6ca6ed7a32646cfbd3b93d6ade2a8b4',
			'20a12c9420b245c79b10a0d753adf3e8',
			'20a12c9420b245c79b10a0d753adf3e8',
			'42bead965ab8446bb8c42d75b2f96e6d'
		);
		
		select * from LMS.L120S03A where MAINID IN 
		(
			'd01f388b3ad14014a95d0d9143a033db',
			'0fbbd971872e49c1b07d22b724e82613',
			'478157354f6942f08f1c6ed5f8863226',
			'10c09da54c9146d4a02d35a4085e1aae',
			'b6ca6ed7a32646cfbd3b93d6ade2a8b4',
			'20a12c9420b245c79b10a0d753adf3e8',
			'20a12c9420b245c79b10a0d753adf3e8',
			'42bead965ab8446bb8c42d75b2f96e6d'
		);
		
		select * from LMS.L120S06A where MAINID IN 
		(
			'd01f388b3ad14014a95d0d9143a033db',
			'0fbbd971872e49c1b07d22b724e82613',
			'478157354f6942f08f1c6ed5f8863226',
			'10c09da54c9146d4a02d35a4085e1aae',
			'b6ca6ed7a32646cfbd3b93d6ade2a8b4',
			'20a12c9420b245c79b10a0d753adf3e8',
			'20a12c9420b245c79b10a0d753adf3e8',
			'42bead965ab8446bb8c42d75b2f96e6d'
		);
		
		select * from LMS.L120S06B where MAINID IN 
		(
			'd01f388b3ad14014a95d0d9143a033db',
			'0fbbd971872e49c1b07d22b724e82613',
			'478157354f6942f08f1c6ed5f8863226',
			'10c09da54c9146d4a02d35a4085e1aae',
			'b6ca6ed7a32646cfbd3b93d6ade2a8b4',
			'20a12c9420b245c79b10a0d753adf3e8',
			'20a12c9420b245c79b10a0d753adf3e8',
			'42bead965ab8446bb8c42d75b2f96e6d'
		);
		
		select * from LMS.l140m01I where MAINID IN 
		(	
			'a3e2e6a73d0c4d37be480832b2f14954',
			'f70d5c5b7e224d90bd601017ea30f3ed'
		);
		
		select * from LMS.l140m01J where MAINID IN 
		(	
			'a3e2e6a73d0c4d37be480832b2f14954',
			'f70d5c5b7e224d90bd601017ea30f3ed'
		);
		
		
		SELECT * FROM LMS.L120S01A  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01B  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01C  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01D  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01E  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01F  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01G  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01M  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01N  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01O  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S04A  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S05B  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S05D  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		
		修改資料開始***********************************************************************
		
		A.改額度序號
		額度明細表/額度序號
		UPDATE LMS.L140M01A SET CNTRNO = '0C5501450017' WHERE CNTRNO = '0C5501000024'	AND MAINID = 'c8d7440d85f9481490af186d1dc4230f' AND CUSTID = 'AUZ0032798';
		UPDATE LMS.L140M01A SET CNTRNO = '0C5501450017' WHERE CNTRNO = '0C5501000024'	AND MAINID = 'ede929aae5ae4ba5ae9c878c6a7f912e' AND CUSTID = 'AUZ0032798';
		UPDATE LMS.L140M01A SET CNTRNO = '0C5501450018' WHERE CNTRNO = '0C5501000025'	AND MAINID = '7b4e1120b1d545babd8ed03a2278598e' AND CUSTID = 'AUZ0032805';
		UPDATE LMS.L140M01A SET CNTRNO = '0C5501450018' WHERE CNTRNO = '0C5501000025'	AND MAINID = '7e3ae6e9e9a74e7799601056a10a2385' AND CUSTID = 'AUZ0032805';
		UPDATE LMS.L140M01A SET CNTRNO = '0C5500800019' WHERE CNTRNO = '0C5501450001'	AND MAINID = 'a48864fd834c4782a87b773f1ba84f70' AND CUSTID = 'AUZ0031019';
		UPDATE LMS.L140M01A SET CNTRNO = '0C5500800019' WHERE CNTRNO = '0C5501450001'	AND MAINID = 'a56db634f58a4614b1e06fb4069d6ec8' AND CUSTID = 'AUZ0031019';
		UPDATE LMS.L140M01A SET CNTRNO = '0C5500900013' WHERE CNTRNO = '0C5501450007' AND MAINID = 'a3e2e6a73d0c4d37be480832b2f14954' AND CUSTID = 'AUZ0034466';
		UPDATE LMS.L140M01A SET CNTRNO = '0C5500900013' WHERE CNTRNO = '0C5501450007' AND MAINID = 'f70d5c5b7e224d90bd601017ea30f3ed' AND CUSTID = 'AUZ0034466';
		UPDATE LMS.L140M01A SET CNTRNO = '0C5500900014' WHERE CNTRNO = '0C5501450008'	AND MAINID = '18d5c38aee394222b30a09a92383b7cb' AND CUSTID = 'AUZ0032185';
		UPDATE LMS.L140M01A SET CNTRNO = '0C5500900014' WHERE CNTRNO = '0C5501450008'	AND MAINID = '7ac377a6c5cd446eb381d28311c16cb8' AND CUSTID = 'AUZ0032185';
		UPDATE LMS.L140M01A SET CNTRNO = '0C5500700051' WHERE CNTRNO = '0C5501450009'	AND MAINID = '185e357585ba44909704bae0b607d604' AND CUSTID = '19551001AN';
		UPDATE LMS.L140M01A SET CNTRNO = '0C5500700051' WHERE CNTRNO = '0C5501450009'	AND MAINID = '186b683b3a0d4f5bac800c21d228cf01' AND CUSTID = '19551001AN';
		UPDATE LMS.L140M01A SET CNTRNO = '0C5501200016' WHERE CNTRNO = '0C5501450010'	AND MAINID = 'e12e94ac5bad4b928e3e29ff107b9baa' AND CUSTID = '19551001AN';
		UPDATE LMS.L140M01A SET CNTRNO = '0C5501200016' WHERE CNTRNO = '0C5501450010'	AND MAINID = '2662ac10172144a0a11a2ea43bb5c486' AND CUSTID = '19551001AN';
		UPDATE LMS.L140M01A SET CNTRNO = '0C5501000023' WHERE CNTRNO = '0C5501450013'	AND MAINID = 'c9d1619acab54afd92a1a20938aaa152' AND CUSTID = '19810211WI';
		UPDATE LMS.L140M01A SET CNTRNO = '0C5501000023' WHERE CNTRNO = '0C5501450013'	AND MAINID = '513d3bd633434c789f2c344674bf70d7' AND CUSTID = '19810211WI';
		
		UPDATE LMS.L140M01A SET COMMSNO = '0C5501450017' WHERE COMMSNO = '0C5501000024'	AND MAINID = 'c8d7440d85f9481490af186d1dc4230f' AND CUSTID = 'AUZ0032798';
		UPDATE LMS.L140M01A SET COMMSNO = '0C5501450017' WHERE COMMSNO = '0C5501000024'	AND MAINID = 'ede929aae5ae4ba5ae9c878c6a7f912e' AND CUSTID = 'AUZ0032798';
		UPDATE LMS.L140M01A SET COMMSNO = '0C5501450018' WHERE COMMSNO = '0C5501000025'	AND MAINID = '7b4e1120b1d545babd8ed03a2278598e' AND CUSTID = 'AUZ0032805';
		UPDATE LMS.L140M01A SET COMMSNO = '0C5501450018' WHERE COMMSNO = '0C5501000025'	AND MAINID = '7e3ae6e9e9a74e7799601056a10a2385' AND CUSTID = 'AUZ0032805';
		UPDATE LMS.L140M01A SET COMMSNO = '0C5500800019' WHERE COMMSNO = '0C5501450001'	AND MAINID = 'a48864fd834c4782a87b773f1ba84f70' AND CUSTID = 'AUZ0031019';
		UPDATE LMS.L140M01A SET COMMSNO = '0C5500800019' WHERE COMMSNO = '0C5501450001'	AND MAINID = 'a56db634f58a4614b1e06fb4069d6ec8' AND CUSTID = 'AUZ0031019';
		UPDATE LMS.L140M01A SET COMMSNO = '0C5500900013' WHERE COMMSNO = '0C5501450007' AND MAINID = 'a3e2e6a73d0c4d37be480832b2f14954' AND CUSTID = 'AUZ0034466';
		UPDATE LMS.L140M01A SET COMMSNO = '0C5500900013' WHERE COMMSNO = '0C5501450007' AND MAINID = 'f70d5c5b7e224d90bd601017ea30f3ed' AND CUSTID = 'AUZ0034466';
		UPDATE LMS.L140M01A SET COMMSNO = '0C5500900014' WHERE COMMSNO = '0C5501450008'	AND MAINID = '18d5c38aee394222b30a09a92383b7cb' AND CUSTID = 'AUZ0032185';
		UPDATE LMS.L140M01A SET COMMSNO = '0C5500900014' WHERE COMMSNO = '0C5501450008'	AND MAINID = '7ac377a6c5cd446eb381d28311c16cb8' AND CUSTID = 'AUZ0032185';
		UPDATE LMS.L140M01A SET COMMSNO = '0C5500700051' WHERE COMMSNO = '0C5501450009'	AND MAINID = '185e357585ba44909704bae0b607d604' AND CUSTID = '19551001AN';
		UPDATE LMS.L140M01A SET COMMSNO = '0C5500700051' WHERE COMMSNO = '0C5501450009'	AND MAINID = '186b683b3a0d4f5bac800c21d228cf01' AND CUSTID = '19551001AN';
		UPDATE LMS.L140M01A SET COMMSNO = '0C5501200016' WHERE COMMSNO = '0C5501450010'	AND MAINID = 'e12e94ac5bad4b928e3e29ff107b9baa' AND CUSTID = '19551001AN';
		UPDATE LMS.L140M01A SET COMMSNO = '0C5501200016' WHERE COMMSNO = '0C5501450010'	AND MAINID = '2662ac10172144a0a11a2ea43bb5c486' AND CUSTID = '19551001AN';
		UPDATE LMS.L140M01A SET COMMSNO = '0C5501000023' WHERE COMMSNO = '0C5501450013'	AND MAINID = 'c9d1619acab54afd92a1a20938aaa152' AND CUSTID = '19810211WI';
		UPDATE LMS.L140M01A SET COMMSNO = '0C5501000023' WHERE COMMSNO = '0C5501450013'	AND MAINID = '513d3bd633434c789f2c344674bf70d7' AND CUSTID = '19810211WI';
		
		
		額度明細表/聯貸攤貸比率
		UPDATE LMS.L140M01E SET SHARENO = '0C5501450017' WHERE SHARENO = '0C5501000024'	AND MAINID = 'c8d7440d85f9481490af186d1dc4230f';  
		UPDATE LMS.L140M01E SET SHARENO = '0C5501450017' WHERE SHARENO = '0C5501000024'	AND MAINID = 'ede929aae5ae4ba5ae9c878c6a7f912e';  
		UPDATE LMS.L140M01E SET SHARENO = '0C5501450018' WHERE SHARENO = '0C5501000025'	AND MAINID = '7b4e1120b1d545babd8ed03a2278598e';  
		UPDATE LMS.L140M01E SET SHARENO = '0C5501450018' WHERE SHARENO = '0C5501000025'	AND MAINID = '7e3ae6e9e9a74e7799601056a10a2385';  
		UPDATE LMS.L140M01E SET SHARENO = '0C5500800019' WHERE SHARENO = '0C5501450001'	AND MAINID = 'a48864fd834c4782a87b773f1ba84f70';  
		UPDATE LMS.L140M01E SET SHARENO = '0C5500800019' WHERE SHARENO = '0C5501450001'	AND MAINID = 'a56db634f58a4614b1e06fb4069d6ec8';  
		UPDATE LMS.L140M01E SET SHARENO = '0C5500900013' WHERE SHARENO = '0C5501450007'	AND MAINID = 'a3e2e6a73d0c4d37be480832b2f14954';  
		UPDATE LMS.L140M01E SET SHARENO = '0C5500900013' WHERE SHARENO = '0C5501450007'	AND MAINID = 'f70d5c5b7e224d90bd601017ea30f3ed';  
		UPDATE LMS.L140M01E SET SHARENO = '0C5500900014' WHERE SHARENO = '0C5501450008'	AND MAINID = '18d5c38aee394222b30a09a92383b7cb';  
		UPDATE LMS.L140M01E SET SHARENO = '0C5500900014' WHERE SHARENO = '0C5501450008'	AND MAINID = '7ac377a6c5cd446eb381d28311c16cb8';  
		UPDATE LMS.L140M01E SET SHARENO = '0C5500700051' WHERE SHARENO = '0C5501450009'	AND MAINID = '185e357585ba44909704bae0b607d604';  
		UPDATE LMS.L140M01E SET SHARENO = '0C5500700051' WHERE SHARENO = '0C5501450009'	AND MAINID = '186b683b3a0d4f5bac800c21d228cf01';  
		UPDATE LMS.L140M01E SET SHARENO = '0C5501200016' WHERE SHARENO = '0C5501450010'	AND MAINID = 'e12e94ac5bad4b928e3e29ff107b9baa';  
		UPDATE LMS.L140M01E SET SHARENO = '0C5501200016' WHERE SHARENO = '0C5501450010'	AND MAINID = '2662ac10172144a0a11a2ea43bb5c486';  
		UPDATE LMS.L140M01E SET SHARENO = '0C5501000023' WHERE SHARENO = '0C5501450013'	AND MAINID = 'c9d1619acab54afd92a1a20938aaa152';  
		UPDATE LMS.L140M01E SET SHARENO = '0C5501000023' WHERE SHARENO = '0C5501450013'	AND MAINID = '513d3bd633434c789f2c344674bf70d7';  
		
		簽報書/資本適足率影響數計算
		UPDATE LMS.L120S03A SET CNTRNO = '0C5501450017' WHERE CNTRNO = '0C5501000024'	AND MAINID = 'd01f388b3ad14014a95d0d9143a033db';  
		UPDATE LMS.L120S03A SET CNTRNO = '0C5501450018' WHERE CNTRNO = '0C5501000025'	AND MAINID = '0fbbd971872e49c1b07d22b724e82613';  
		UPDATE LMS.L120S03A SET CNTRNO = '0C5500800019' WHERE CNTRNO = '0C5501450001'	AND MAINID = '478157354f6942f08f1c6ed5f8863226';  
		UPDATE LMS.L120S03A SET CNTRNO = '0C5500900013' WHERE CNTRNO = '0C5501450007' AND MAINID = '10c09da54c9146d4a02d35a4085e1aae'; 
		UPDATE LMS.L120S03A SET CNTRNO = '0C5500900014' WHERE CNTRNO = '0C5501450008'	AND MAINID = 'b6ca6ed7a32646cfbd3b93d6ade2a8b4';  
		UPDATE LMS.L120S03A SET CNTRNO = '0C5500700051' WHERE CNTRNO = '0C5501450009'	AND MAINID = '20a12c9420b245c79b10a0d753adf3e8';  
		UPDATE LMS.L120S03A SET CNTRNO = '0C5501200016' WHERE CNTRNO = '0C5501450010'	AND MAINID = '20a12c9420b245c79b10a0d753adf3e8';  
		UPDATE LMS.L120S03A SET CNTRNO = '0C5501000023' WHERE CNTRNO = '0C5501450013'	AND MAINID = '42bead965ab8446bb8c42d75b2f96e6d';
		 
		簽報書/利害關係人授信條件對照表檔
		UPDATE LMS.L120S06A SET CNTRNO = '0C5501450017' WHERE CNTRNO = '0C5501000024'	AND MAINID = 'd01f388b3ad14014a95d0d9143a033db';  
		UPDATE LMS.L120S06A SET CNTRNO = '0C5501450018' WHERE CNTRNO = '0C5501000025'	AND MAINID = '0fbbd971872e49c1b07d22b724e82613';  
		UPDATE LMS.L120S06A SET CNTRNO = '0C5500800019' WHERE CNTRNO = '0C5501450001'	AND MAINID = '478157354f6942f08f1c6ed5f8863226';  
		UPDATE LMS.L120S06A SET CNTRNO = '0C5500900013' WHERE CNTRNO = '0C5501450007' AND MAINID = '10c09da54c9146d4a02d35a4085e1aae'; 
		UPDATE LMS.L120S06A SET CNTRNO = '0C5500900014' WHERE CNTRNO = '0C5501450008'	AND MAINID = 'b6ca6ed7a32646cfbd3b93d6ade2a8b4';  
		UPDATE LMS.L120S06A SET CNTRNO = '0C5500700051' WHERE CNTRNO = '0C5501450009'	AND MAINID = '20a12c9420b245c79b10a0d753adf3e8';  
		UPDATE LMS.L120S06A SET CNTRNO = '0C5501200016' WHERE CNTRNO = '0C5501450010'	AND MAINID = '20a12c9420b245c79b10a0d753adf3e8';  
		UPDATE LMS.L120S06A SET CNTRNO = '0C5501000023' WHERE CNTRNO = '0C5501450013'	AND MAINID = '42bead965ab8446bb8c42d75b2f96e6d';
		
		UPDATE LMS.L120S06A SET CNTRNO2 = '0C5501450017' WHERE CNTRNO2 = '0C5501000024'	AND MAINID = 'd01f388b3ad14014a95d0d9143a033db';  
		UPDATE LMS.L120S06A SET CNTRNO2 = '0C5501450018' WHERE CNTRNO2 = '0C5501000025'	AND MAINID = '0fbbd971872e49c1b07d22b724e82613';  
		UPDATE LMS.L120S06A SET CNTRNO2 = '0C5500800019' WHERE CNTRNO2 = '0C5501450001'	AND MAINID = '478157354f6942f08f1c6ed5f8863226';  
		UPDATE LMS.L120S06A SET CNTRNO2 = '0C5500900013' WHERE CNTRNO2 = '0C5501450007' AND MAINID = '10c09da54c9146d4a02d35a4085e1aae'; 
		UPDATE LMS.L120S06A SET CNTRNO2 = '0C5500900014' WHERE CNTRNO2 = '0C5501450008'	AND MAINID = 'b6ca6ed7a32646cfbd3b93d6ade2a8b4';  
		UPDATE LMS.L120S06A SET CNTRNO2 = '0C5500700051' WHERE CNTRNO2 = '0C5501450009'	AND MAINID = '20a12c9420b245c79b10a0d753adf3e8';  
		UPDATE LMS.L120S06A SET CNTRNO2 = '0C5501200016' WHERE CNTRNO2 = '0C5501450010'	AND MAINID = '20a12c9420b245c79b10a0d753adf3e8';  
		UPDATE LMS.L120S06A SET CNTRNO2 = '0C5501000023' WHERE CNTRNO2 = '0C5501450013'	AND MAINID = '42bead965ab8446bb8c42d75b2f96e6d';
		
		簽報書/利害關係人授信條件對照表明細檔
		UPDATE LMS.L120S06B SET CNTRNO = '0C5501450017' WHERE CNTRNO = '0C5501000024'	AND MAINID = 'd01f388b3ad14014a95d0d9143a033db';  
		UPDATE LMS.L120S06B SET CNTRNO = '0C5501450018' WHERE CNTRNO = '0C5501000025'	AND MAINID = '0fbbd971872e49c1b07d22b724e82613';  
		UPDATE LMS.L120S06B SET CNTRNO = '0C5500800019' WHERE CNTRNO = '0C5501450001'	AND MAINID = '478157354f6942f08f1c6ed5f8863226';  
		UPDATE LMS.L120S06B SET CNTRNO = '0C5500900013' WHERE CNTRNO = '0C5501450007' AND MAINID = '10c09da54c9146d4a02d35a4085e1aae'; 
		UPDATE LMS.L120S06B SET CNTRNO = '0C5500900014' WHERE CNTRNO = '0C5501450008'	AND MAINID = 'b6ca6ed7a32646cfbd3b93d6ade2a8b4';  
		UPDATE LMS.L120S06B SET CNTRNO = '0C5500700051' WHERE CNTRNO = '0C5501450009'	AND MAINID = '20a12c9420b245c79b10a0d753adf3e8';  
		UPDATE LMS.L120S06B SET CNTRNO = '0C5501200016' WHERE CNTRNO = '0C5501450010'	AND MAINID = '20a12c9420b245c79b10a0d753adf3e8';  
		UPDATE LMS.L120S06B SET CNTRNO = '0C5501000023' WHERE CNTRNO = '0C5501450013'	AND MAINID = '42bead965ab8446bb8c42d75b2f96e6d';
		
		B.改ID   AUZ0034466->AUZ0037490
		
		額度明細表
		UPDATE LMS.L140M01A SET CUSTID = 'AUZ0037490' WHERE MAINID = 'a3e2e6a73d0c4d37be480832b2f14954' AND CUSTID = 'AUZ0034466';
		UPDATE LMS.L140M01A SET CUSTID = 'AUZ0037490' WHERE MAINID = 'f70d5c5b7e224d90bd601017ea30f3ed' AND CUSTID = 'AUZ0034466';
		
		額度明細表/L140M01I．連保人資料檔
		UPDATE LMS.L140M01I SET RID = 'AUZ0037490' WHERE MAINID = 'a3e2e6a73d0c4d37be480832b2f14954' AND RID = 'AUZ0034466';
		UPDATE LMS.L140M01I SET RID = 'AUZ0037490' WHERE MAINID = 'f70d5c5b7e224d90bd601017ea30f3ed' AND RID = 'AUZ0034466';
		
		額度明細表/L140M01J．共同借款人檔
		UPDATE LMS.L140M01J SET CUSTID = 'AUZ0037490' WHERE MAINID = 'a3e2e6a73d0c4d37be480832b2f14954' AND CUSTID = 'AUZ0034466';
		UPDATE LMS.L140M01J SET CUSTID = 'AUZ0037490' WHERE MAINID = 'f70d5c5b7e224d90bd601017ea30f3ed' AND CUSTID = 'AUZ0034466';
		
		簽報書
		UPDATE LMS.L120M01A SET CUSTID = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID = 'AUZ0034466';
		
		借款人基本資料
		UPDATE LMS.L120S01A SET CUSTID = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID = 'AUZ0034466';
		UPDATE LMS.L120S01B SET CUSTID = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID = 'AUZ0034466';
		UPDATE LMS.L120S01C SET CUSTID = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID = 'AUZ0034466';
		UPDATE LMS.L120S01D SET CUSTID = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID = 'AUZ0034466';
		UPDATE LMS.L120S01E SET CUSTID = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID = 'AUZ0034466';
		UPDATE LMS.L120S01F SET CUSTID = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID = 'AUZ0034466';
		UPDATE LMS.L120S01G SET CUSTID = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID = 'AUZ0034466';
		UPDATE LMS.L120S01M SET CUSTID = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID = 'AUZ0034466';
		UPDATE LMS.L120S01N SET CUSTID = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID = 'AUZ0034466';
		UPDATE LMS.L120S01O SET CUSTID = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID = 'AUZ0034466';
		
		關係戶於本行各項業務往來明細檔
		UPDATE LMS.L120S04A SET CUSTID = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID = 'AUZ0034466';
		
		L120S05B．借款人集團授信明細檔
		UPDATE LMS.L120S05B SET CUSTID = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID = 'AUZ0034466';
		
		L120S05D．借款人關係企業授信明細檔
		UPDATE LMS.L120S05D SET CUSTID = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID = 'AUZ0034466';
		
		L120S06A．利害關係人授信條件對照表檔
		UPDATE LMS.L120S06A SET CUSTID  = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID  = 'AUZ0034466';
		UPDATE LMS.L120S06A SET CUSTID2 = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID2 = 'AUZ0034466';
		
		L120S06B．利害關係人授信條件對照表明細檔
		UPDATE LMS.L120S06B SET CUSTID = 'AUZ0037490' WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' AND CUSTID = 'AUZ0034466';
		
		更新資料後查詢
		select custid,dupno,cntrno,COMMSNO from LMS.l140m01A where MAINID IN 
		(
			'c8d7440d85f9481490af186d1dc4230f',
			'ede929aae5ae4ba5ae9c878c6a7f912e',
			'7b4e1120b1d545babd8ed03a2278598e',
			'7e3ae6e9e9a74e7799601056a10a2385',
			'a48864fd834c4782a87b773f1ba84f70',
			'a56db634f58a4614b1e06fb4069d6ec8',
			'a3e2e6a73d0c4d37be480832b2f14954',
			'f70d5c5b7e224d90bd601017ea30f3ed',
			'18d5c38aee394222b30a09a92383b7cb',
			'7ac377a6c5cd446eb381d28311c16cb8',
			'185e357585ba44909704bae0b607d604',
			'186b683b3a0d4f5bac800c21d228cf01',
			'e12e94ac5bad4b928e3e29ff107b9baa',
			'2662ac10172144a0a11a2ea43bb5c486',
			'c9d1619acab54afd92a1a20938aaa152',
			'513d3bd633434c789f2c344674bf70d7'
		);
		
		select * from LMS.l140m01E where MAINID IN 
		(
			'c8d7440d85f9481490af186d1dc4230f',
			'ede929aae5ae4ba5ae9c878c6a7f912e',
			'7b4e1120b1d545babd8ed03a2278598e',
			'7e3ae6e9e9a74e7799601056a10a2385',
			'a48864fd834c4782a87b773f1ba84f70',
			'a56db634f58a4614b1e06fb4069d6ec8',
			'a3e2e6a73d0c4d37be480832b2f14954',
			'f70d5c5b7e224d90bd601017ea30f3ed',
			'18d5c38aee394222b30a09a92383b7cb',
			'7ac377a6c5cd446eb381d28311c16cb8',
			'185e357585ba44909704bae0b607d604',
			'186b683b3a0d4f5bac800c21d228cf01',
			'e12e94ac5bad4b928e3e29ff107b9baa',
			'2662ac10172144a0a11a2ea43bb5c486',
			'c9d1619acab54afd92a1a20938aaa152',
			'513d3bd633434c789f2c344674bf70d7'
		);
		
		select * from LMS.l120m01A where MAINID IN 
		(
			'd01f388b3ad14014a95d0d9143a033db',
			'0fbbd971872e49c1b07d22b724e82613',
			'478157354f6942f08f1c6ed5f8863226',
			'10c09da54c9146d4a02d35a4085e1aae',
			'b6ca6ed7a32646cfbd3b93d6ade2a8b4',
			'20a12c9420b245c79b10a0d753adf3e8',
			'20a12c9420b245c79b10a0d753adf3e8',
			'42bead965ab8446bb8c42d75b2f96e6d'
		);
		
		select * from LMS.L120S03A where MAINID IN 
		(
			'd01f388b3ad14014a95d0d9143a033db',
			'0fbbd971872e49c1b07d22b724e82613',
			'478157354f6942f08f1c6ed5f8863226',
			'10c09da54c9146d4a02d35a4085e1aae',
			'b6ca6ed7a32646cfbd3b93d6ade2a8b4',
			'20a12c9420b245c79b10a0d753adf3e8',
			'20a12c9420b245c79b10a0d753adf3e8',
			'42bead965ab8446bb8c42d75b2f96e6d'
		);
		
		select * from LMS.L120S06A where MAINID IN 
		(
			'd01f388b3ad14014a95d0d9143a033db',
			'0fbbd971872e49c1b07d22b724e82613',
			'478157354f6942f08f1c6ed5f8863226',
			'10c09da54c9146d4a02d35a4085e1aae',
			'b6ca6ed7a32646cfbd3b93d6ade2a8b4',
			'20a12c9420b245c79b10a0d753adf3e8',
			'20a12c9420b245c79b10a0d753adf3e8',
			'42bead965ab8446bb8c42d75b2f96e6d'
		);
		
		select * from LMS.L120S06B where MAINID IN 
		(
			'd01f388b3ad14014a95d0d9143a033db',
			'0fbbd971872e49c1b07d22b724e82613',
			'478157354f6942f08f1c6ed5f8863226',
			'10c09da54c9146d4a02d35a4085e1aae',
			'b6ca6ed7a32646cfbd3b93d6ade2a8b4',
			'20a12c9420b245c79b10a0d753adf3e8',
			'20a12c9420b245c79b10a0d753adf3e8',
			'42bead965ab8446bb8c42d75b2f96e6d'
		);
		
		select * from LMS.l140m01I where MAINID IN 
		(	
			'a3e2e6a73d0c4d37be480832b2f14954',
			'f70d5c5b7e224d90bd601017ea30f3ed'
		);
		
		select * from LMS.l140m01J where MAINID IN 
		(	
			'a3e2e6a73d0c4d37be480832b2f14954',
			'f70d5c5b7e224d90bd601017ea30f3ed'
		);
		
		
		SELECT * FROM LMS.L120S01A  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01B  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01C  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01D  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01E  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01F  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01G  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01M  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01N  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S01O  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S04A  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S05B  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		SELECT * FROM LMS.L120S05D  WHERE MAINID = '10c09da54c9146d4a02d35a4085e1aae' ;
		 -->
		<!-- J-103-0180 2014/12/18 end -->
		
		<!--   鄭天明 05974(墨爾本分行)  
		UPDATE LMS.L140M01A SET CNTRNO = '0C5501000024' WHERE CNTRNO = '0C5501450017' AND MAINID = 'c8d7440d85f9481490af186d1dc4230f'  
		UPDATE LMS.L140M01A SET CNTRNO = '0C5501000024' WHERE CNTRNO = '0C5501450017' AND MAINID = 'ede929aae5ae4ba5ae9c878c6a7f912e'  
		UPDATE LMS.L140M01A SET CNTRNO = '0C5501000025' WHERE CNTRNO = '0C5501450018' AND MAINID = '7b4e1120b1d545babd8ed03a2278598e'  
		UPDATE LMS.L140M01A SET CNTRNO = '0C5501000025' WHERE CNTRNO = '0C5501450018' AND MAINID = '7e3ae6e9e9a74e7799601056a10a2385'  
		 
		UPDATE LMS.L120S03A SET CNTRNO = '0C5501000024' WHERE CNTRNO = '0C5501450017' AND MAINID = 'd01f388b3ad14014a95d0d9143a033db' 
		UPDATE LMS.L120S03A SET CNTRNO = '0C5501000025' WHERE CNTRNO = '0C5501450018' AND MAINID = '0fbbd971872e49c1b07d22b724e82613'
		
		UPDATE LMS.L140M01A SET CNTRNO = '0C5511000025' WHERE CNTRNO = '0C5501450018' AND MAINID = '37f6932b0a404e5c8d17f61accb21ea9'  
        UPDATE LMS.L140M01A SET CNTRNO = '0C5511000025' WHERE CNTRNO = '0C5501450018' AND MAINID = '49e0b65e378740d1b6539540db537cdd' 
		-->
		
		<!-- J-104-0005-001  修改企金2014國外部(兆)授字第00687號簽報書統編與戶名
	
		    //更新前查詢資料************************************************************************************************
			
			//額度明細表
			SELECT *  FROM LMS.L140M01A  WHERE MAINID = 'a0350672b5864fda96504170b538a581' AND CUSTID = 'BVZ0006644' AND CNTRNO = '007410300595'
			SELECT *  FROM LMS.L140M01A  WHERE MAINID = 'db0c75049a2c45c88c917914bac6f7fa' AND CUSTID = 'BVZ0006644' AND CNTRNO = '007410300594'
			SELECT *  FROM LMS.L140M01A  WHERE MAINID = 'e89e40e3d77643adbae5cf3aeaa6ff97' AND CUSTID = 'USZ0175042' AND CNTRNO = '007410300668'
			 
			
			//額度明細表/L140M01I．連保人資料檔
			SELECT *  FROM LMS.L140M01I WHERE MAINID = 'a0350672b5864fda96504170b538a581' AND RID = 'USZ0175021'
			SELECT *  FROM LMS.L140M01I WHERE MAINID = 'db0c75049a2c45c88c917914bac6f7fa' AND RID = 'USZ0175021'
			SELECT *  FROM LMS.L140M01I WHERE MAINID = 'e89e40e3d77643adbae5cf3aeaa6ff97' AND RID = 'USZ0175057'
			
			//額度明細表/L140M01J．共同借款人檔
			//SELECT *  FROM LMS.L140M01J WHERE MAINID = 'a3e2e6a73d0c4d37be480832b2f14954' AND CUSTID = 'AUZ0034466'
			//SELECT *  FROM LMS.L140M01J WHERE MAINID = 'f70d5c5b7e224d90bd601017ea30f3ed' AND CUSTID = 'AUZ0034466'
			
			//簽報書
			SELECT *  FROM LMS.L120M01A  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID    = 'BVZ0006644'
			SELECT *  FROM LMS.L120M01A  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND cesCustId = 'BVZ0006644'
			
			//借款人基本資料
			SELECT *  FROM LMS.L120S01A WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01B WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01C WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01D WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01E WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01F WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01G WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01M WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01N WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01O WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01O WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND RCUSTID = 'BVZ0006644'
			
			SELECT *  FROM LMS.L120S01A WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01B WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01C WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01D WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01E WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01F WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01G WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01M WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01N WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01O WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01O WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND RCUSTID = 'USZ0175042'
			
			//關係戶於本行各項業務往來明細檔
			SELECT *  FROM LMS.L120S04A  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S04A  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			
			//L120S05B．借款人集團授信明細檔
			SELECT *  FROM LMS.L120S05B  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S05B  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			 
			//L120S05D．借款人關係企業授信明細檔
			SELECT *  FROM LMS.L120S05D  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S05D  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			
			//L120S06A．利害關係人授信條件對照表檔
			SELECT *  FROM LMS.L120S06A WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID  = 'BVZ0006644'
			SELECT *  FROM LMS.L120S06A WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID2 = 'BVZ0006644'
			SELECT *  FROM LMS.L120S06A WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID  = 'USZ0175042'
			SELECT *  FROM LMS.L120S06A WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID2 = 'USZ0175042'
			
			//L120S06B．利害關係人授信條件對照表明細檔
			SELECT *  FROM LMS.L120S06B WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S06B WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'


		    //更新資料************************************************************************************************ 
		
			//額度明細表
			UPDATE LMS.L140M01A SET CUSTID = 'KYZ0008930',CUSTNAME='JUPITER INTERMEDIATE LIMITED' WHERE MAINID = 'a0350672b5864fda96504170b538a581' AND CUSTID = 'BVZ0006644' AND CNTRNO = '007410300595';
			UPDATE LMS.L140M01A SET CUSTID = 'KYZ0008930',CUSTNAME='JUPITER INTERMEDIATE LIMITED' WHERE MAINID = 'db0c75049a2c45c88c917914bac6f7fa' AND CUSTID = 'BVZ0006644' AND CNTRNO = '007410300594';
			UPDATE LMS.L140M01A SET CUSTID = 'KYZ0008946',CUSTNAME='GANYMEDE INTERMEDIATE LIMITED' WHERE MAINID = 'e89e40e3d77643adbae5cf3aeaa6ff97' AND CUSTID = 'USZ0175042' AND CNTRNO = '007410300668';
			 
			
			//額度明細表/L140M01I．連保人資料檔
			UPDATE LMS.L140M01I SET RID = 'USZ0175401',RNAME='JUPITER INVESTMENT HOLDINGS, L.L.C.' WHERE MAINID = 'a0350672b5864fda96504170b538a581' AND RID = 'USZ0175021';
			UPDATE LMS.L140M01I SET RID = 'USZ0175401',RNAME='JUPITER INVESTMENT HOLDINGS, L.L.C.' WHERE MAINID = 'db0c75049a2c45c88c917914bac6f7fa' AND RID = 'USZ0175021';
			UPDATE LMS.L140M01I SET RID = 'USZ0175417',RNAME='GANYMEDE INVESTMENT HOLDINGS, L.L.C.' WHERE MAINID = 'e89e40e3d77643adbae5cf3aeaa6ff97' AND RID = 'USZ0175057';
			
			//額度明細表/L140M01J．共同借款人檔
			//UPDATE LMS.L140M01J SET CUSTID = 'AUZ0037490' WHERE MAINID = 'a3e2e6a73d0c4d37be480832b2f14954' AND CUSTID = 'AUZ0034466';
			//UPDATE LMS.L140M01J SET CUSTID = 'AUZ0037490' WHERE MAINID = 'f70d5c5b7e224d90bd601017ea30f3ed' AND CUSTID = 'AUZ0034466';
			
			//簽報書
			UPDATE LMS.L120M01A SET CUSTID    = 'KYZ0008930',CUSTNAME='JUPITER INTERMEDIATE LIMITED' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID    = 'BVZ0006644';
			UPDATE LMS.L120M01A SET cesCustId = 'KYZ0008930'  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND cesCustId = 'BVZ0006644';
			
			//借款人基本資料
			UPDATE LMS.L120S01A SET CUSTID = 'KYZ0008930',CUSTNAME='JUPITER INTERMEDIATE LIMITED' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644';
			UPDATE LMS.L120S01B SET CUSTID = 'KYZ0008930' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644';
			UPDATE LMS.L120S01C SET CUSTID = 'KYZ0008930' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644';
			UPDATE LMS.L120S01D SET CUSTID = 'KYZ0008930' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644';
			UPDATE LMS.L120S01E SET CUSTID = 'KYZ0008930' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644';
			UPDATE LMS.L120S01F SET CUSTID = 'KYZ0008930' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644';
			UPDATE LMS.L120S01G SET CUSTID = 'KYZ0008930' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644';
			UPDATE LMS.L120S01M SET CUSTID = 'KYZ0008930' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644';
			UPDATE LMS.L120S01N SET CUSTID = 'KYZ0008930' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644';
			UPDATE LMS.L120S01O SET CUSTID = 'KYZ0008930' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644';
			UPDATE LMS.L120S01O SET RCUSTID = 'KYZ0008930',RCUSTNAME='JUPITER INTERMEDIATE LIMITED' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND RCUSTID = 'BVZ0006644';
			
			UPDATE LMS.L120S01A SET CUSTID = 'KYZ0008946',CUSTNAME='GANYMEDE INTERMEDIATE LIMITED' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042';
			UPDATE LMS.L120S01B SET CUSTID = 'KYZ0008946',NTCODE='KY' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042';
			UPDATE LMS.L120S01C SET CUSTID = 'KYZ0008946' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042';
			UPDATE LMS.L120S01D SET CUSTID = 'KYZ0008946' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042';
			UPDATE LMS.L120S01E SET CUSTID = 'KYZ0008946' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042';
			UPDATE LMS.L120S01F SET CUSTID = 'KYZ0008946' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042';
			UPDATE LMS.L120S01G SET CUSTID = 'KYZ0008946' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042';
			UPDATE LMS.L120S01M SET CUSTID = 'KYZ0008946' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042';
			UPDATE LMS.L120S01N SET CUSTID = 'KYZ0008946' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042';
			UPDATE LMS.L120S01O SET CUSTID = 'KYZ0008946' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042';
			UPDATE LMS.L120S01O SET RCUSTID = 'KYZ0008946',RCUSTNAME='GANYMEDE INTERMEDIATE LIMITED' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND RCUSTID = 'USZ0175042';
			
			//關係戶於本行各項業務往來明細檔
			UPDATE LMS.L120S04A SET CUSTID = 'KYZ0008930',CUSTNAME='JUPITER INTERMEDIATE LIMITED' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644';
			UPDATE LMS.L120S04A SET CUSTID = 'KYZ0008946',CUSTNAME='GANYMEDE INTERMEDIATE LIMITED' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042';
			
			//L120S05B．借款人集團授信明細檔
			UPDATE LMS.L120S05B SET CUSTID = 'KYZ0008930',CUSTNAME='JUPITER INTERMEDIATE LIMITED' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644';
			UPDATE LMS.L120S05B SET CUSTID = 'KYZ0008946',CUSTNAME='GANYMEDE INTERMEDIATE LIMITED' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042';
			 
			//L120S05D．借款人關係企業授信明細檔
			UPDATE LMS.L120S05D SET CUSTID = 'KYZ0008930',CUSTNAME='JUPITER INTERMEDIATE LIMITED' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644';
			UPDATE LMS.L120S05D SET CUSTID = 'KYZ0008946',CUSTNAME='GANYMEDE INTERMEDIATE LIMITED' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042';
			
			//L120S06A．利害關係人授信條件對照表檔
			UPDATE LMS.L120S06A SET CUSTID  = 'KYZ0008930',CUSTNAME='JUPITER INTERMEDIATE LIMITED' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID  = 'BVZ0006644';
			UPDATE LMS.L120S06A SET CUSTID2 = 'KYZ0008930',CUSTNAME2='JUPITER INTERMEDIATE LIMITED' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID2 = 'BVZ0006644';
			UPDATE LMS.L120S06A SET CUSTID  = 'KYZ0008946',CUSTNAME='GANYMEDE INTERMEDIATE LIMITED' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID  = 'USZ0175042';
			UPDATE LMS.L120S06A SET CUSTID2 = 'KYZ0008946',CUSTNAME2='GANYMEDE INTERMEDIATE LIMITED' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID2 = 'USZ0175042';
			
			//L120S06B．利害關係人授信條件對照表明細檔
			UPDATE LMS.L120S06B SET CUSTID = 'KYZ0008930' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644';
			UPDATE LMS.L120S06B SET CUSTID = 'KYZ0008946' WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042';
		
		    
			//更新後查詢資料************************************************************************************************
			
			//額度明細表
			SELECT *  FROM LMS.L140M01A  WHERE MAINID = 'a0350672b5864fda96504170b538a581' AND CUSTID = 'BVZ0006644' AND CNTRNO = '007410300595'
			SELECT *  FROM LMS.L140M01A  WHERE MAINID = 'db0c75049a2c45c88c917914bac6f7fa' AND CUSTID = 'BVZ0006644' AND CNTRNO = '007410300594'
			SELECT *  FROM LMS.L140M01A  WHERE MAINID = 'e89e40e3d77643adbae5cf3aeaa6ff97' AND CUSTID = 'USZ0175042' AND CNTRNO = '007410300668'
			 
			
			//額度明細表/L140M01I．連保人資料檔
			SELECT *  FROM LMS.L140M01I WHERE MAINID = 'a0350672b5864fda96504170b538a581' AND RID = 'USZ0175021'
			SELECT *  FROM LMS.L140M01I WHERE MAINID = 'db0c75049a2c45c88c917914bac6f7fa' AND RID = 'USZ0175021'
			SELECT *  FROM LMS.L140M01I WHERE MAINID = 'e89e40e3d77643adbae5cf3aeaa6ff97' AND RID = 'USZ0175057'
			
			//額度明細表/L140M01J．共同借款人檔
			//SELECT *  FROM LMS.L140M01J WHERE MAINID = 'a3e2e6a73d0c4d37be480832b2f14954' AND CUSTID = 'AUZ0034466'
			//SELECT *  FROM LMS.L140M01J WHERE MAINID = 'f70d5c5b7e224d90bd601017ea30f3ed' AND CUSTID = 'AUZ0034466'
			
			//簽報書
			SELECT *  FROM LMS.L120M01A  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID    = 'BVZ0006644'
			SELECT *  FROM LMS.L120M01A  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND cesCustId = 'BVZ0006644'
			
			//借款人基本資料
			SELECT *  FROM LMS.L120S01A WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01B WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01C WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01D WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01E WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01F WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01G WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01M WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01N WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01O WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S01O WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND RCUSTID = 'BVZ0006644'
			
			SELECT *  FROM LMS.L120S01A WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01B WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01C WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01D WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01E WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01F WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01G WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01M WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01N WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01O WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			SELECT *  FROM LMS.L120S01O WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND RCUSTID = 'USZ0175042'
			
			//關係戶於本行各項業務往來明細檔
			SELECT *  FROM LMS.L120S04A  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S04A  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			
			//L120S05B．借款人集團授信明細檔
			SELECT *  FROM LMS.L120S05B  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S05B  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			 
			//L120S05D．借款人關係企業授信明細檔
			SELECT *  FROM LMS.L120S05D  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S05D  WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			
			//L120S06A．利害關係人授信條件對照表檔
			SELECT *  FROM LMS.L120S06A WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID  = 'BVZ0006644'
			SELECT *  FROM LMS.L120S06A WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID2 = 'BVZ0006644'
			SELECT *  FROM LMS.L120S06A WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID  = 'USZ0175042'
			SELECT *  FROM LMS.L120S06A WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID2 = 'USZ0175042'
			
			//L120S06B．利害關係人授信條件對照表明細檔
			SELECT *  FROM LMS.L120S06B WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'BVZ0006644'
			SELECT *  FROM LMS.L120S06B WHERE MAINID = 'a9748798f6834ff6bdef6094c91dcb87' AND CUSTID = 'USZ0175042'
			
			
			
		J-104-0005-001 END -->
		
		<!--申請單位：(0B9)雪梨分行 申請人：(05127)施富鐘  申請日期：2015/05/04 為辦理當地信評評等與總處信評結果比較，撈取澳洲地區三家分行之WEB-ELOAN信評資訊-->
		<entry key="SP_SQL_EXEC_001">
		    <value>
		   	    EXPORT TO result.csv OF DEL MODIFIED BY NOCHARDEL codepage=950 
				WITH COPOTABLE AS(
					SELECT T1.CASEBRID AS CASEBRID,(CASE WHEN TA.custNo != '' THEN TA.custNo ELSE TA.localCustId END) AS custNo,
					TA.custName AS custName,TA.busCode AS busCode,TA.subCode AS subCode, 
					TC1.grade AS gradeTC1,CHAR(TC1.crdTYear) AS crdTYearTC1,TC2.grade AS gradeTC2,CHAR(TC2.crdTYear) AS crdTYearTC2,
					TC3.grade AS gradeTC3,CHAR(TC3.crdTYear) AS crdTYearTC3,TC4.grade AS gradeTC4,CHAR(TC4.crdTYear) AS crdTYearTC4,
					T1.caseNo AS caseNo,T1.CUSTID AS CUSTID
					FROM
					(SELECT * FROM LMS.L120M01A WHERE DOCSTATUS = '05O' AND CASEBRID IN ('0B9','0C5','0C2') AND docType = '1') AS T1
					LEFT OUTER JOIN
					LMS.L120S01A AS TA
					ON 
					T1.MAINID = TA.MAINID AND
					TA.KEYMAN = 'Y'
					--信用評等
					LEFT OUTER JOIN
					LMS.L120S01C AS TC1 
					ON 
					TA.MAINID = TC1.MAINID AND 
					TA.CUSTID = TC1.CUSTID AND 
					TA.DUPNO  = TC1.DUPNO AND
					TA.MAINID IS NOT NULL AND
					TC1.crdType IN ('DB','DL','OU','OB')
					--信用風險內部評等
					LEFT OUTER JOIN
					LMS.L120S01C AS TC2 
					ON 
					TA.MAINID = TC2.MAINID AND 
					TA.CUSTID = TC2.CUSTID AND 
					TA.DUPNO  = TC2.DUPNO AND
					TA.MAINID IS NOT NULL AND
					LEFT(TC2.crdType,1) IN ('M','A')
					--信用風險外部評等
					LEFT OUTER JOIN
					LMS.L120S01C AS TC3 
					ON 
					TA.MAINID = TC3.MAINID AND 
					TA.CUSTID = TC3.CUSTID AND 
					TA.DUPNO  = TC3.DUPNO AND
					TA.MAINID IS NOT NULL AND
					TC3.crdType IN ('NM','NS','NF','NC')
					--當地信評評等
					LEFT OUTER JOIN
					LMS.L120S01C AS TC4 
					ON 
					TA.MAINID = TC4.MAINID AND 
					TA.CUSTID = TC4.CUSTID AND 
					TA.DUPNO  = TC4.DUPNO AND
					TA.MAINID IS NOT NULL AND	
					LEFT(TC4.crdType,1) = 'C'
					WHERE TA.MAINID IS NOT NULL
				),
				PEOTABLE AS(
				  SELECT T1.CASEBRID AS CASEBRID,TA.custNo AS custNo,
					TA.custName AS custName,TS.busCode AS busCode,'' AS subCode, 
					TS.O_GRADE AS gradeTC1,'' AS crdTYearTC1,'' AS gradeTC2,'' AS crdTYearTC2,
					'' AS gradeTC3,'' AS crdTYearTC3,'' AS gradeTC4,'' AS crdTYearTC4,
					T1.caseNo AS caseNo,T1.CUSTID AS CUSTID
					FROM
					(SELECT * FROM LMS.L120M01A WHERE DOCSTATUS = '05O' AND CASEBRID IN ('0B9','0C5','0C2') AND docType = '2') AS T1
					LEFT OUTER JOIN
					LMS.C120M01A AS TA
					ON 
					T1.MAINID = TA.MAINID AND
					TA.KEYMAN = 'Y'
					LEFT OUTER JOIN
					LMS.C120S01A AS TS
					ON 
					TA.MAINID = TS.MAINID AND 
					TA.CUSTID = TS.CUSTID AND 
					TA.DUPNO  = TS.DUPNO  AND
					TA.MAINID IS NOT NULL 
					WHERE TA.MAINID IS NOT NULL
				)
				SELECT CASEBRID AS 分行,custNo AS 客戶編號,custName AS 戶名,busCode AS 行業別,subCode AS 次產業別, 
				gradeTC1 AS 信用評等,crdTYearTC1 AS 信用評等日期,gradeTC2 AS 信用風險內部評等,crdTYearTC2 AS 信用風險內部評等日期,
				gradeTC3 AS 信用風險外部評等,crdTYearTC3 AS 信用風險外部評等日期,gradeTC4 AS 當地信評評等,crdTYearTC4 AS 當地信評評等日期 ,
				caseNo AS 案號,CUSTID AS MEGA_ID
				FROM
				(
				 SELECT * FROM COPOTABLE
				 UNION ALL
				 SELECT * FROM PEOTABLE
				)
				ORDER BY CASEBRID ASC,CUSTID ASC,caseNo ASC ,custNo ASC    Fetch First 50000 rows only
			</value>
		</entry>
		
		 
		<!-- J-104-0159-001 更正授信戶 EXPRESS SCRIPTS COMPANY 在E-LOAN系統的 MEGA ID 、完整的名稱和 INDUSTRY CODE。需和AS400系統一致。-->
	    <entry key="SP_SQL_EXEC_001">
		    <value>
		  --更新前查詢資料************************************************************************************************

		  --額度明細表
			select lms.l140M01A.CASENO,lms.l140M01A.CNTRNO,lms.l140M01A.CUSTID,lms.l140M01A.MAINID from
	      (select * from lms.l120M01C where mainid = 'b173a48dbce84234a5fe95d2fa312c59') AS T1
	      left outer join
	      lms.l140M01A
	      on
	      T1.REFMAINID =  lms.l140M01A.mainid; 
		 
			--簽報書
			SELECT *  FROM LMS.L120M01A  WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59';  
			 
			--借款人基本資料
			SELECT *  FROM LMS.L120S01A WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			SELECT *  FROM LMS.L120S01B WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			SELECT *  FROM LMS.L120S01C WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			SELECT *  FROM LMS.L120S01D WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			SELECT *  FROM LMS.L120S01E WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			SELECT *  FROM LMS.L120S01F WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			SELECT *  FROM LMS.L120S01G WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			SELECT *  FROM LMS.L120S01M WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			SELECT *  FROM LMS.L120S01N WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			SELECT *  FROM LMS.L120S01O WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			SELECT *  FROM LMS.L120S01O WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND RCUSTID = 'USZ0176936';
			
	

		    --更新資料************************************************************************************************ 
		
			--額度明細表
			UPDATE LMS.L140M01A SET CUSTID = 'USZ0169175',CUSTNAME='EXPRESS SCRIPTS HOLDING COMPANY' WHERE MAINID IN
			(
			    select lms.l140M01A.CASENO,lms.l140M01A.CNTRNO,lms.l140M01A.CUSTID,lms.l140M01A.MAINID from
		        (select * from lms.l120M01C where mainid = 'b173a48dbce84234a5fe95d2fa312c59') AS T1
		        left outer join
		        lms.l140M01A
		        on
		        T1.REFMAINID =  lms.l140M01A.mainid 
		        where
		        lms.l140M01A.custid = 'USZ0176936'
		    );
			 
		
			--簽報書
			UPDATE LMS.L120M01A SET CUSTID    = 'USZ0169175',CUSTNAME='EXPRESS SCRIPTS HOLDING COMPANY' WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID    = 'USZ0176936';
			
			--借款人基本資料
			UPDATE LMS.L120S01A SET CUSTID = 'USZ0169175',CUSTNAME='EXPRESS SCRIPTS HOLDING COMPANY' WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			UPDATE LMS.L120S01B SET CUSTID = 'USZ0169175',busCode='014571',ecoNm='藥品及醫療用品批發業'  WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			UPDATE LMS.L120S01C SET CUSTID = 'USZ0169175' WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			UPDATE LMS.L120S01D SET CUSTID = 'USZ0169175' WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			UPDATE LMS.L120S01E SET CUSTID = 'USZ0169175' WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			UPDATE LMS.L120S01F SET CUSTID = 'USZ0169175' WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			UPDATE LMS.L120S01G SET CUSTID = 'USZ0169175' WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			UPDATE LMS.L120S01M SET CUSTID = 'USZ0169175' WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			UPDATE LMS.L120S01N SET CUSTID = 'USZ0169175' WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			UPDATE LMS.L120S01O SET CUSTID = 'USZ0169175' WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0176936';
			  
			 
			--更新後查詢資料************************************************************************************************
			
			--額度明細表
			  select lms.l140M01A.CASENO,lms.l140M01A.CNTRNO,lms.l140M01A.CUSTID,lms.l140M01A.MAINID from
		      (select * from lms.l120M01C where mainid = 'b173a48dbce84234a5fe95d2fa312c59') AS T1
		      left outer join
		      lms.l140M01A
		      on
		      T1.REFMAINID =  lms.l140M01A.mainid ;
			 

			--簽報書
			SELECT *  FROM LMS.L120M01A  WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59'  ;
			 
			
			--借款人基本資料
			SELECT *  FROM LMS.L120S01A WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0169175';
			SELECT *  FROM LMS.L120S01B WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0169175';
			SELECT *  FROM LMS.L120S01C WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0169175';
			SELECT *  FROM LMS.L120S01D WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0169175';
			SELECT *  FROM LMS.L120S01E WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0169175';
			SELECT *  FROM LMS.L120S01F WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0169175';
			SELECT *  FROM LMS.L120S01G WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0169175';
			SELECT *  FROM LMS.L120S01M WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0169175';
			SELECT *  FROM LMS.L120S01N WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0169175';
			SELECT *  FROM LMS.L120S01O WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND CUSTID = 'USZ0169175';
			SELECT *  FROM LMS.L120S01O WHERE MAINID = 'b173a48dbce84234a5fe95d2fa312c59' AND RCUSTID = 'USZ0169175';

		</value>
		</entry> 
		<!-- J-104-0159-001 END -->
		 
		<!-- J-104-0106-001 BGN --> 
		<entry key="J-104-0106-001">
		    <value>
		          <!--
				       申請單位：(924)債權管理處
				      申請人：(05933)陳永結
				      程式修改申請編號 : (104) 第 0766 號
				  J-104-0106-001 原924債權管理處逾催管理系統SERVER主機，移回資訊處集中管理。
				  -->
		   </value>
		</entry> 
		<!-- J-104-0106-001 END -->
		
		<!-- J-104-0225-001 BGN --> 
		<entry key="J-104-0225-001">
		    <value>
		          <!--
				       申請單位：(025)國際金融部
				      申請人：(06545)榮懋祺 
				      程式修改申請編號 : (104) 第 1868 號
				  J-104-0225-001 2015馬尼拉(兆)授權字第00031號San Miguel Corporation簽報書申請更換額度序號
				  -->
				  --更新前查詢資料***
				    select * from lms.l140m01a where cntrno = '0B2501550014';
                    select * from lms.l140m01e where cntrno = '0B2501550014';          
				  --更新資料***
				    update lms.l140m01a set cntrno = '025410400167' where cntrno = '0B2501550014'
                    update lms.l140m01e set shareno = '025410400167' where shareno= '0B2501550014' and SHAREBRID = '025'
				  --更新後查詢資料***
				    select * from lms.l140m01a where cntrno = '025410400167';
                    select * from lms.l140m01e where cntrno = '025410400167';          
		   </value>
		</entry> 
		<!-- J-104-0225-001 END -->
		
		
		<!-- (104)第 2108 號 BGN
			程式修改申請編號：(104)第 2108 號
			申請單位：胡志明市分行
			申請人：許哲明
			申請單位編號：
			修改事項：海外客戶額度序號誤建為DBU額度 ，致國外部無法完成動審於A-LOAN引入，因本案屬分行授權外(副總權限)，須煩請  尊處協助系統修改。
			附加說明文件：電子檔
			修改事項：海外客戶額度序號誤建為DBU額度 ，致國外部無法完成動審於A-LOAN引入，因本案屬分行授權外(副總權限)，須煩請  尊處協助系統修改。
                                修改內容：借戶TAI YUEN TEXTILE(VIETNAM) CO.,LTD.(客戶編號VNZ00331840)案號 ２０１５胡志明(兆)授字第００１０１號項下額度序號007110400247，因簽報時誤選為DBU額度，致國外部無法完成動審。

		-->
		<entry key="(104)第 2108 號">
		    <value>
--更新前查詢資料***
select * from lms.l140m01a where cntrno = '007110400247';
select * from lms.l140m01e where shareno = '007110400247';          
--更新資料***
update lms.l140m01a set cntrno = '007410400247' where cntrno = '007110400247';
update lms.l140m01e set shareno = '007410400247' where shareno= '007110400247' and SHAREBRID = '007';
--更新後查詢資料***
select * from lms.l140m01a where cntrno = '007410400247';
select * from lms.l140m01e where shareno = '007410400247';            
		   </value>
		</entry> 
		<!-- (104)第 2108 號 END -->
		
		<!-- J-104-0301
		insert into lms.c900s01a(OID,CHECKCODE,CHECKSEQ,CHECKTYPE,CHECKCONTENT,PRODTYPE,CHECKRMK,CREATOR,CREATETIME,UPDATER,UPDATETIME) 
		values (get_oid(), '141', 141, '6', '貸款年限≦1年','','如否，應專案陳報總處核定','008034',current timestamp,'008034',current timestamp);

		insert into lms.c900s01a(OID,CHECKCODE,CHECKSEQ,CHECKTYPE,CHECKCONTENT,PRODTYPE,CHECKRMK,CREATOR,CREATETIME,UPDATER,UPDATETIME) 
		values (get_oid(), '142', 142, '2', '1.借款人具完全行為能力之我國國民，其年齡加計貸款年限是否不超過六十五者2.借款人是否為任職於「消費性貸款作業要點」第四條第二款所列之機構服務滿一年、或雖未滿乙年惟含前任相同性質工作滿三年以上之正式員工。3.借款人年薪是否達新台幣六十萬元以上者。','','如否，應專案陳報總處核定','008034',current timestamp,'008034',current timestamp);

		insert into lms.c900s01a(OID,CHECKCODE,CHECKSEQ,CHECKTYPE,CHECKCONTENT,PRODTYPE,CHECKRMK,CREATOR,CREATETIME,UPDATER,UPDATETIME) 
		values (get_oid(), '143', 143, '2', '留學生本人為申請人及借款人，留學生應具有中華民國國籍且具完全行為能力、無不良信用，出國全時修讀符合教育部採認規定(即在教育部所編參考名冊所列或經教育部確認)之國外大學校院碩、博士學位。','','如否，應專案陳報總處核定','008034',current timestamp,'008034',current timestamp);

		insert into lms.c900s01a(OID,CHECKCODE,CHECKSEQ,CHECKTYPE,CHECKCONTENT,PRODTYPE,CHECKRMK,CREATOR,CREATETIME,UPDATER,UPDATETIME) 
		values (get_oid(), '144', 144, '3', '是否曾貸有教育部其他就學貸款尚未結清;或領有我國政府提供之各項公費或留學獎助學金，其領受公費或留學獎助學金期間尚未結束;或貸有各地方政府辦理之留學貸款。前項資料以聯合徵信中心查詢Ｂ９９資料為準。','','如是，不得申請本貸款','008034',current timestamp,'008034',current timestamp);

		insert into lms.c900s01a(OID,CHECKCODE,CHECKSEQ,CHECKTYPE,CHECKCONTENT,PRODTYPE,CHECKRMK,CREATOR,CREATETIME,UPDATER,UPDATETIME) 
		values (get_oid(), '145', 145, '6', '是否符合攻讀博士學位最長16年(含寬限期最長5年)，攻讀碩士最長10年(含寬限期最長3年)。','','','008034',current timestamp,'008034',current timestamp);
		
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','13500100','1','143' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','13500100','1','30' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','13500100','1','31' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','13500100','1','32' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','13500100','1','144' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','13500100','1','57' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','13500100','1','145' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','13500100','1','94' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','13500100','1','104' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','14501000','1','143' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','14501000','1','30' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','14501000','1','31' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','14501000','1','32' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','14501000','1','144' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','14501000','1','57' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','14501000','1','145' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','14501000','1','94' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '36','14501000','1','104' ,'008034',current timestamp,'008034',current timestamp);
		###		
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100100','1','142' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100100','1','30' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100100','1','31' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100100','1','32' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100100','1','33' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100100','1','34' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100100','1','45' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100100','1','54' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100100','1','53' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100100','1','52' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100100','1','141' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100100','1','85' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100100','1','104' ,'008034',current timestamp,'008034',current timestamp);
		###
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100200','1','142' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100200','1','30' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100200','1','31' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100200','1','32' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100200','1','33' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100200','1','34' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100200','1','45' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100200','1','54' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100200','1','53' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100200','1','52' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100200','1','141' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100200','1','85' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','12100200','1','104' ,'008034',current timestamp,'008034',current timestamp);
		###
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13101000','1','142' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13101000','1','30' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13101000','1','31' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13101000','1','32' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13101000','1','33' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13101000','1','34' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13101000','1','45' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13101000','1','54' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13101000','1','53' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13101000','1','52' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13101000','1','72' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13101000','1','85' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13101000','1','104' ,'008034',current timestamp,'008034',current timestamp);
		###
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13502000','1','142' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13502000','1','30' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13502000','1','31' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13502000','1','32' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13502000','1','33' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13502000','1','34' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13502000','1','45' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13502000','1','54' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13502000','1','53' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13502000','1','52' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13502000','1','72' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13502000','1','85' ,'008034',current timestamp,'008034',current timestamp);
		insert into lms.c900m01c(OID,PRODKIND,SUBJCODE,TYPE,SOUSECODE,CREATOR,CREATETIME,UPDATER,UPDATETIME ) values (get_oid(), '07','13502000','1','104' ,'008034',current timestamp,'008034',current timestamp);
 		-->
	
		<entry key="(105)第 0022 號">
		    <!-- J-105-0004 -->
			<value>
            --從 e-loan 查出外交部員工的額度序號
			select distinct m.cntrno from 
			( select mainid,custid,dupNo from lms.C120S01B where comname like '%外交部%' 
			  and mainid in (select mainid from lms.l120m01a where docstatus like '05%')
			) a left outer join lms.l120m01c c 
			on a.mainid=c.mainid left outer join lms.l140m01a m on c.refmainid=m.mainid 
			where m.approveTime IS not NULL AND m.DELETEDTIME IS NULL and docstatus like '03%' 
			and a.custid=m.custid and a.dupno=m.dupno

			--從LNF155查出資料
			select * from ln.lnf155 
			where lnf155_contract in (?) 
			and LNF155_DATA_YM='2015-12' and LNF155_LOAN_BAL_TW>0 and LNF155_BR_NO='070'
		    </value>
		</entry> 
		
		<entry key="J-104-0292-001">
		    <!-- Web e-Loan 企金動用審核表「五、其他事項」增加「保證公司之董事會決議錄(含保證公司為提供物保保證者)」之檢核選項 -->
			<value>
	            INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT, 
		        ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) 
		    	VALUES (GET_OID(), '918', CURRENT TIMESTAMP, '918001', '保證公司之董事會決議錄(含保證公司為提供物保保證者)', '', 26, '3', 'zh_TW', CURRENT TIMESTAMP, '918001');
		    </value>
		</entry> 
		
		<!-- J-105-0018
		原本 checkType=7,checkseq=78的項目，由 78 改146
		
		insert into lms.c900s01a(OID,CHECKCODE,CHECKSEQ,CHECKTYPE,CHECKCONTENT,PRODTYPE,CHECKRMK,CREATOR,CREATETIME,UPDATER,UPDATETIME) 
		values (get_oid(), '146', 78, '7', '是否為合宜住宅','','如是，應注意是否符合合宜住宅相關規定','008034',current timestamp,'008034',current timestamp);

		update lms.C900M01C set sousecode='146' where sousecode='78'

		-->
		
		
		<entry key="J-105-0041-001">
		    <!-- Web e-Loan授信、徵信系統配合泰子行曼谷總行E LOAN系統授信戶MEGAID:THZ0080783 T.S.M, RUBBER CO.LTD，新增移轉全部授信檔轉至萬磅分行(因該戶為萬磅分行之授信戶，業務更改後轉檔分行漏列)。 -->
			<value>
	            INSERT INTO  COM.BRTOBR01 (CUSTID,DUPNO,EXDATE,FBRANCH,FCUSTID,FCUSTNO,TBRANCH,TCUSTID,TCUSTNO,CNAME) VALUES ('THZ0080783','0','2016-02-19','Y01',0	,0	,'Y04',7000261,7000261,'T.S.M, RUBBER CO.LTD'); 				
				INSERT INTO LMS.L120A01A  
				(     
					SELECT GET_OID(),MAINID,PID,'Y04','Y04A2',AUTHTIME,AUTHTYPE,'Y04' FROM LMS.L120A01A WHERE MAINID IN
					(     
					  SELECT MAINID FROM 
					  LMS.L120M01A,COM.BRTOBR01 
					  WHERE 
					  LMS.L120M01A.CUSTID =COM.BRTOBR01.CUSTID AND
					  LMS.L120M01A.DUPNO = COM.BRTOBR01.DUPNO AND  
					  LMS.L120M01A.CASEBRID = 'Y01' AND 
					  COM.BRTOBR01.EXDATE ='2016-02-19' AND
					  LMS.L120M01A.DOCSTATUS = '05O' AND
					  LMS.L120M01A.DELETEDTIME IS NULL
					) AND OWNUNIT = 'Y01' AND AUTHUNIT = 'Y01'
				);                
		    </value>
		</entry> 
		
		
		
		<entry key="J-105-0051-001">
		    <!-- Web e-Loan海外授信管理系統動用審核表修改第13項查核內容 -->
			<value>
	            update LMS.L901M01A set ITEMCONTENT = '銀行法及金控法利害關係人查詢紀錄(簽約時應再向借戶徵提最新董監事與持股10%以上股東名單，並確認其董監事任期起日在簽約日之前或之後，再據以重新查詢本項紀錄)' where ITEMTYPE = '1' and LOCALE = 'zh_TW' and ITEMSEQ = 13;
				update LMS.L901M01A set ITEMCONTENT = '銀行法及金控法利害關係人查詢紀錄(簽約時應再向借戶徵提最新董監事與持股10%以上股東名單，並確認其董監事任期起日在簽約日之前或之後，再據以重新查詢本項紀錄)' where ITEMTYPE = '1' and LOCALE = 'en' and ITEMSEQ = 13;
				update LMS.L901M01A set ITEMCONTENT = '银行法及金控法利害关系人查询纪录(签约时应再向借户征提最新董监事与持股10%以上股东名单，并确认其董监事任期起日在签约日之前或之后，再据以重新查询本项纪录)' where ITEMTYPE = '1' and LOCALE = 'zh_CN' and ITEMSEQ = 13;
		    </value>
		</entry> 
		
		<entry key="J-105-0026-001">
		    <!--Web e-Loan企金授信管理系統額度明細表專案種類新增外銷貸款優惠信用保證方案 -->
			<value>
	            insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_lnType', '03','03.外銷貸款優惠信用保證方案',03,'','','zh_TW','system',current timestamp);
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_lnType', '03','03.外销贷款优惠信用保证方案',03,'','','zh_CN','system',current timestamp);
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_lnType', '03','03.Export Loans Preferential Credit guarantee',03,'','','en','system',current timestamp);
		    </value>
		</entry> 
		
		<entry key="J-105-0040-001">
		    <!--Web e-Loan企金授信管理系統額度明細表專案種類新增中小企業災害復舊專案貸款 -->
			<value>
	            insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_lnType', '04','04.中小企業災害復舊專案貸款',04,'','','zh_TW','system',current timestamp);
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_lnType', '04','04.中小企业灾害复旧专案贷款',04,'','','zh_CN','system',current timestamp);
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_lnType', '04','04.SME disaster involution project loans',04,'','','en','system',current timestamp);
		    </value>
		</entry>
		<entry key="J-105-0141-001">
		    <!--配合金邊分行需求篩選Web e-Loan企金授信業務之編制中、已核准之簽案額度序號、案件編號、客戶名稱、Mega ID、客戶編號及保證人之csv檔案格式資料(資料起迄由2011年開行至2016/5/20)。 -->
			<value>
	            EXPORT TO 1050141-007625-01.csv OF DEL MODIFIED BY NOCHARDEL codepage=950 
				select  '額度序號','案件編號', 'megaid','客戶名稱','客戶編號','連保人ID','連保人姓名' from  sysibm.sysdummy1
				union
				(
				select c.cntrno,c.caseNo,c.custid, c.custname,c.custno,c.RID,c.RNAME
				from  (
				-- 企金已核準
				select c.cntrno,a.caseNo,c.custid, c.custname,d.custno ,c.guarantor,e.RID,e.RNAME
				from lms.l120m01a a 
				inner join lms.l120m01c b on a.mainId=b.mainid
				inner join lms.l140m01a c on b.refmainid=c.mainid and c.deletedTime is null and c.docstatus in ('030','040')
				inner join lms.l120s01a d on a.mainid=d.mainid and d.custid=c.custid
				left join lms.l140m01i e on c.mainid=e.mainid
				where a.casebrid in ('0C7','0C9','0D2','0D4') and a.docType= '1'  and a.deletedTime is null  and a.docstatus in ('05O','06O')
				
				union
				-- 企金未核準
				select c.cntrno,a.caseNo,c.custid, c.custname,d.custno ,c.guarantor, e.RID,e.RNAME
				from lms.l120m01a a 
				inner join lms.l120m01c b on a.mainId=b.mainid   and b.itemtype='1'
				inner join lms.l140m01a c on b.refmainid=c.mainid and c.deletedTime is null 
				inner join lms.l120s01a d on a.mainid=d.mainid and d.custid=c.custid
				left join lms.l140m01i e on c.mainid=e.mainid
				where a.casebrid in ('0C7','0C9','0D2','0D4') and a.docType= '1'  and a.deletedTime is null  and a.docstatus not in ('05O','06O')
				
				union
				
				-- 消金已核準
				select c.cntrno,a.caseNo,c.custid, c.custname,d.custno ,c.guarantor,e.RID,e.RNAME
				from lms.l120m01a a 
				inner join lms.l120m01c b on a.mainId=b.mainid
				inner join lms.l140m01a c on b.refmainid=c.mainid and c.deletedTime is null and c.docstatus in ('030','040')
				inner join lms.c120m01a d on a.mainid=d.mainid and d.custid=c.custid
				left join lms.l140m01i e on c.mainid=e.mainid
				where a.casebrid in ('0C7','0C9','0D2','0D4') and a.docType= '2'  and a.deletedTime is null and a.docstatus in ('05O','06O')
				union
				-- 消金未核準
				select c.cntrno,a.caseNo,c.custid, c.custname,d.custno ,c.guarantor,e.RID,e.RNAME
				from lms.l120m01a a 
				inner join lms.l120m01c b on a.mainId=b.mainid and b.itemtype='1'
				inner join lms.l140m01a c on b.refmainid=c.mainid and c.deletedTime is null
				inner join lms.c120m01a d on a.mainid=d.mainid and d.custid=c.custid
				left join lms.l140m01i e on c.mainid=e.mainid
				where a.casebrid in ('0C7','0C9','0D2','0D4') and a.docType= '2'  and a.deletedTime is null  and a.docstatus not in ('05O','06O')
				
				
				) c order by caseno) order by 2 with ur
		    </value>
		</entry> 
		
		
		<entry key="J-105-0144-001">
		    <!--Web e-Loan企金授信管理系統， 謹請修改Enbridge Inc.於e-Loan之Identity No.，由原「USZ01803120」改為「CAZ00584030」-->
			<value>
	        --額度明細表
			SELECT *  FROM LMS.L140M01A  WHERE MAINID = '56b11486b10c499a9935aa6961eff78d'  ;
			SELECT *  FROM LMS.L140M01A  WHERE MAINID = 'b99a4cd1eeb847948adbf71785fffab7'  ;
	 
			--額度明細表/L140M01I．連保人資料檔
			--SELECT *  FROM LMS.L140M01I WHERE MAINID = '56b11486b10c499a9935aa6961eff78d' AND RID = 'USZ0180312';
			--SELECT *  FROM LMS.L140M01I WHERE MAINID = 'b99a4cd1eeb847948adbf71785fffab7' AND RID = 'USZ0180312';
			 
			--額度明細表/L140M01J．共同借款人檔
			--SELECT *  FROM LMS.L140M01J WHERE MAINID = '56b11486b10c499a9935aa6961eff78d' AND CUSTID = 'USZ0180312';
			--SELECT *  FROM LMS.L140M01J WHERE MAINID = 'b99a4cd1eeb847948adbf71785fffab7' AND CUSTID = 'USZ0180312';
			
			--簽報書
			SELECT *  FROM LMS.L120M01A  WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID    = 'USZ0180312';
			SELECT *  FROM LMS.L120M01A  WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND cesCustId = 'USZ0180312';
			
			--借款人基本資料
			SELECT *  FROM LMS.L120S01A WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			SELECT *  FROM LMS.L120S01B WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			SELECT *  FROM LMS.L120S01C WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			SELECT *  FROM LMS.L120S01D WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			SELECT *  FROM LMS.L120S01E WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			SELECT *  FROM LMS.L120S01F WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			SELECT *  FROM LMS.L120S01G WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			SELECT *  FROM LMS.L120S01M WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			SELECT *  FROM LMS.L120S01N WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			SELECT *  FROM LMS.L120S01O WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			SELECT *  FROM LMS.L120S01O WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND RCUSTID = 'USZ0180312';
	
			--關係戶於本行各項業務往來明細檔
			SELECT *  FROM LMS.L120S04A  WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			
			--L120S05B．借款人集團授信明細檔
			SELECT *  FROM LMS.L120S05B  WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			
			--L120S05D．借款人關係企業授信明細檔
			SELECT *  FROM LMS.L120S05D  WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			
			--L120S06A．利害關係人授信條件對照表檔
			SELECT *  FROM LMS.L120S06A WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID  = 'USZ0180312';
			SELECT *  FROM LMS.L120S06A WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID2 = 'USZ0180312';
		
			--L120S06B．利害關係人授信條件對照表明細檔
			SELECT *  FROM LMS.L120S06B WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			

		  --更新資料************************************************************************************************ 
		
			--額度明細表
			UPDATE LMS.L140M01A SET CUSTID = 'CAZ0058403' WHERE MAINID = '56b11486b10c499a9935aa6961eff78d' AND CUSTID = 'USZ0180312' AND CNTRNO = '0A4501650023';
			UPDATE LMS.L140M01A SET CUSTID = 'CAZ0058403' WHERE MAINID = 'b99a4cd1eeb847948adbf71785fffab7' AND CUSTID = 'USZ0180312' AND CNTRNO = '0A4501650023';
			 
			
			--額度明細表/L140M01I．連保人資料檔
			UPDATE LMS.L140M01I SET RID = 'CAZ0058403'  WHERE MAINID = '56b11486b10c499a9935aa6961eff78d' AND RID = 'USZ0180312';
			UPDATE LMS.L140M01I SET RID = 'CAZ0058403'  WHERE MAINID = 'b99a4cd1eeb847948adbf71785fffab7' AND RID = 'USZ0180312';
			 
			--額度明細表/L140M01J．共同借款人檔
			--UPDATE LMS.L140M01J SET CUSTID = 'CAZ0058403' WHERE MAINID = '56b11486b10c499a9935aa6961eff78d' AND CUSTID = 'USZ0180312';
			--UPDATE LMS.L140M01J SET CUSTID = 'CAZ0058403' WHERE MAINID = 'b99a4cd1eeb847948adbf71785fffab7' AND CUSTID = 'USZ0180312';
			
			--簽報書
			UPDATE LMS.L120M01A SET CUSTID    = 'CAZ0058403'  WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID    = 'USZ0180312';
			UPDATE LMS.L120M01A SET cesCustId = 'CAZ0058403'  WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND cesCustId = 'USZ0180312';
			
			--借款人基本資料
			UPDATE LMS.L120S01A SET CUSTID = 'CAZ0058403' WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			UPDATE LMS.L120S01B SET CUSTID = 'CAZ0058403' WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			UPDATE LMS.L120S01C SET CUSTID = 'CAZ0058403' WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			UPDATE LMS.L120S01D SET CUSTID = 'CAZ0058403' WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			UPDATE LMS.L120S01E SET CUSTID = 'CAZ0058403' WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			UPDATE LMS.L120S01F SET CUSTID = 'CAZ0058403' WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			UPDATE LMS.L120S01G SET CUSTID = 'CAZ0058403' WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			UPDATE LMS.L120S01M SET CUSTID = 'CAZ0058403' WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			UPDATE LMS.L120S01N SET CUSTID = 'CAZ0058403' WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			UPDATE LMS.L120S01O SET CUSTID = 'CAZ0058403' WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			UPDATE LMS.L120S01O SET RCUSTID = 'CAZ0058403' WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND RCUSTID = 'USZ0180312';
			
			--關係戶於本行各項業務往來明細檔
			UPDATE LMS.L120S04A SET CUSTID = 'CAZ0058403' WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			
			--L120S05B．借款人集團授信明細檔
			UPDATE LMS.L120S05B SET CUSTID = 'CAZ0058403' WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			
			--L120S05D．借款人關係企業授信明細檔
			UPDATE LMS.L120S05D SET CUSTID = 'CAZ0058403' WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';
			
			--L120S06A．利害關係人授信條件對照表檔
			UPDATE LMS.L120S06A SET CUSTID  = 'CAZ0058403'  WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID  = 'USZ0180312';
			UPDATE LMS.L120S06A SET CUSTID2 = 'CAZ0058403'  WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID2 = 'USZ0180312';
			 
			--L120S06B．利害關係人授信條件對照表明細檔
			UPDATE LMS.L120S06B SET CUSTID = 'CAZ0058403' WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'USZ0180312';

			--更新後查詢資料************************************************************************************************
			
			--額度明細表
			SELECT *  FROM LMS.L140M01A  WHERE MAINID = '56b11486b10c499a9935aa6961eff78d'  ;
			SELECT *  FROM LMS.L140M01A  WHERE MAINID = 'b99a4cd1eeb847948adbf71785fffab7'  ;
	 
			--額度明細表/L140M01I．連保人資料檔
			--SELECT *  FROM LMS.L140M01I WHERE MAINID = '56b11486b10c499a9935aa6961eff78d' AND RID = 'CAZ0058403';
			--SELECT *  FROM LMS.L140M01I WHERE MAINID = 'b99a4cd1eeb847948adbf71785fffab7' AND RID = 'CAZ0058403';
			 
			--額度明細表/L140M01J．共同借款人檔
			--SELECT *  FROM LMS.L140M01J WHERE MAINID = '56b11486b10c499a9935aa6961eff78d' AND CUSTID = 'CAZ0058403';
			--SELECT *  FROM LMS.L140M01J WHERE MAINID = 'b99a4cd1eeb847948adbf71785fffab7' AND CUSTID = 'CAZ0058403';
			
			--簽報書
			SELECT *  FROM LMS.L120M01A  WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID    = 'CAZ0058403';
			SELECT *  FROM LMS.L120M01A  WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND cesCustId = 'CAZ0058403';
			
			--借款人基本資料
			SELECT *  FROM LMS.L120S01A WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'CAZ0058403';
			SELECT *  FROM LMS.L120S01B WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'CAZ0058403';
			SELECT *  FROM LMS.L120S01C WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'CAZ0058403';
			SELECT *  FROM LMS.L120S01D WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'CAZ0058403';
			SELECT *  FROM LMS.L120S01E WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'CAZ0058403';
			SELECT *  FROM LMS.L120S01F WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'CAZ0058403';
			SELECT *  FROM LMS.L120S01G WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'CAZ0058403';
			SELECT *  FROM LMS.L120S01M WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'CAZ0058403';
			SELECT *  FROM LMS.L120S01N WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'CAZ0058403';
			SELECT *  FROM LMS.L120S01O WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'CAZ0058403';
			SELECT *  FROM LMS.L120S01O WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND RCUSTID = 'CAZ0058403';
	
			--關係戶於本行各項業務往來明細檔
			SELECT *  FROM LMS.L120S04A  WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'CAZ0058403';
			
			--L120S05B．借款人集團授信明細檔
			SELECT *  FROM LMS.L120S05B  WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'CAZ0058403';
			
			--L120S05D．借款人關係企業授信明細檔
			SELECT *  FROM LMS.L120S05D  WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'CAZ0058403';
			
			--L120S06A．利害關係人授信條件對照表檔
			SELECT *  FROM LMS.L120S06A WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID  = 'CAZ0058403';
			SELECT *  FROM LMS.L120S06A WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID2 = 'CAZ0058403';
		
			--L120S06B．利害關係人授信條件對照表明細檔
			SELECT *  FROM LMS.L120S06B WHERE MAINID = '0756c68c20ff402b8fbae823a9b58218' AND CUSTID = 'CAZ0058403';
		    </value>
		</entry> 
		
		
		<entry key="J-105-0089-001">
		    <!--Web e-Loan授信管理系統修改美國地區分行動審表-->
			<value>
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'l160m01a.avoidDfCountry', 'US','9|11|16|17|19|20',1,'','','zh_TW','system',current timestamp);

				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0A2', CURRENT TIMESTAMP, '918001', 'Article of Incorporation', '', 1, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0A2', CURRENT TIMESTAMP, '918001', 'Article of Incorporation', '', 1, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0A2', CURRENT TIMESTAMP, '918001', 'Article of Incorporation', '', 1, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0A2', CURRENT TIMESTAMP, '918001', 'Policy of Title Insurance(Deed of Trust)', '', 2, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0A2', CURRENT TIMESTAMP, '918001', 'Policy of Title Insurance(Deed of Trust)', '', 2, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0A2', CURRENT TIMESTAMP, '918001', 'Policy of Title Insurance(Deed of Trust)', '', 2, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0A2', CURRENT TIMESTAMP, '918001', 'UCC Filing', '', 3, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0A2', CURRENT TIMESTAMP, '918001', 'UCC Filing', '', 3, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0A2', CURRENT TIMESTAMP, '918001', 'UCC Filing', '', 3, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				SELECT * FROM LMS.L901M01A WHERE BRANCHID = '0A3' AND ITEMTYPE = '2';
				SELECT * FROM LMS.L901M01A WHERE BRANCHID = '0A4' AND ITEMTYPE = '2';
				SELECT * FROM LMS.L901M01A WHERE BRANCHID = '0M2' AND ITEMTYPE = '2';
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) SELECT GET_OID(), '0A3', CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER  FROM LMS.L901M01A WHERE BRANCHID = '0A2' AND ITEMTYPE = '2';
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) SELECT GET_OID(), '0A4', CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER  FROM LMS.L901M01A WHERE BRANCHID = '0A2' AND ITEMTYPE = '2';
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) SELECT GET_OID(), '0M2', CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER  FROM LMS.L901M01A WHERE BRANCHID = '0A2' AND ITEMTYPE = '2';

			</value>
		</entry> 
		
		<entry key="J-105-0107-001">
		    <!--Web e-Loan授信管理系統修改馬尼拉分行動審表。-->
			<value>
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'l160m01a.avoidDfCountry', 'PH','7|9|10|15|16|17|18',1,'','','zh_TW','system',current timestamp);

				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '秘書公證', '', 1, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '秘書公證', '', 1, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '秘書公證', '', 1, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '綜合授信契約對保', '', 2, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '綜合授信契約對保', '', 2, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '綜合授信契約對保', '', 2, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '不動產/動產抵押權設定契約書', '', 3, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '不動產/動產抵押權設定契約書', '', 3, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '不動產/動產抵押權設定契約書', '', 3, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				 
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '不動產所有權狀', '', 4, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '不動產所有權狀', '', 4, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '不動產所有權狀', '', 4, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '不動產稅籍申報書', '', 5, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '不動產稅籍申報書', '', 5, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '不動產稅籍申報書', '', 5, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '不動產繳稅證明書', '', 6, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '不動產繳稅證明書', '', 6, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '0B2', CURRENT TIMESTAMP, '918001', '不動產繳稅證明書', '', 6, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');

			</value>
		</entry>

		<entry key="J-105-0176">
		    <!--消金增加產品63、64、65-->
			<value>
			--個金產品名稱檔
			insert into lms.c900m01a (OID,PRODKIND, PRODNM1,PRODNM2,CREATOR, CREATETIME,UPDATER, UPDATETIME)
			values (get_oid(), '63', '天然及重大災害受災戶','購屋', 'sys',current timestamp, 'sys',current timestamp);
			
			insert into lms.c900m01a (OID,PRODKIND, PRODNM1,PRODNM2,CREATOR, CREATETIME,UPDATER, UPDATETIME)
			values (get_oid(), '64', '天然及重大災害受災戶','重建', 'sys',current timestamp, 'sys',current timestamp);
			
			insert into lms.c900m01a (OID,PRODKIND, PRODNM1,PRODNM2,CREATOR, CREATETIME,UPDATER, UPDATETIME)
			values (get_oid(), '65', '天然及重大災害受災戶','修繕', 'sys',current timestamp, 'sys',current timestamp);
			
			--產品種類對應表
			insert into lms.c900m01b (OID,PRODKIND, SUBJCODE, RINTWAY,ISCANCEL, CREATOR, CREATETIME,UPDATER, UPDATETIME )
			values(get_oid(), '63', '13506200', '2', 'N', 'sys',current timestamp, 'sys',current timestamp);
			
			insert into lms.c900m01b (OID,PRODKIND, SUBJCODE, RINTWAY,ISCANCEL, CREATOR, CREATETIME,UPDATER, UPDATETIME )
			values(get_oid(), '63', '14501500', '2', 'N', 'sys',current timestamp, 'sys',current timestamp);
			
			insert into lms.c900m01b (OID,PRODKIND, SUBJCODE, RINTWAY,ISCANCEL, CREATOR, CREATETIME,UPDATER, UPDATETIME )
			values(get_oid(), '64', '13506200', '2', 'N', 'sys',current timestamp, 'sys',current timestamp);
			
			insert into lms.c900m01b (OID,PRODKIND, SUBJCODE, RINTWAY,ISCANCEL, CREATOR, CREATETIME,UPDATER, UPDATETIME )
			values(get_oid(), '64', '14501500', '2', 'N', 'sys',current timestamp, 'sys',current timestamp);
			
			insert into lms.c900m01b (OID,PRODKIND, SUBJCODE, RINTWAY,ISCANCEL, CREATOR, CREATETIME,UPDATER, UPDATETIME )
			values(get_oid(), '65', '13506300', '2', 'N', 'sys',current timestamp, 'sys',current timestamp);
			
			insert into lms.c900m01b (OID,PRODKIND, SUBJCODE, RINTWAY,ISCANCEL, CREATOR, CREATETIME,UPDATER, UPDATETIME )
			values(get_oid(), '65', '14502000', '2', 'N', 'sys',current timestamp, 'sys',current timestamp);
			
			
			--[產品種類+會計科目]與[查核事項]關聯檔
			insert into lms.c900m01c select get_oid(),'63',SUBJCODE, TYPE, SOUSECODE,'sys',current timestamp,'sys',current timestamp from LMS.c900m01c where PRODKIND='10';
			insert into lms.c900m01c select get_oid(),'64',SUBJCODE, TYPE, SOUSECODE,'sys',current timestamp,'sys',current timestamp from LMS.c900m01c where PRODKIND='10';
			insert into lms.c900m01c select get_oid(),'65',SUBJCODE, TYPE, SOUSECODE,'sys',current timestamp,'sys',current timestamp from LMS.c900m01c where PRODKIND='12';
			
			--查核事項檔(條文) lms.c900s01a
			--本次無新增
			
			--災害名稱  01(0206震災受災戶)
			insert into com.bcodetype (oid,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) values( get_oid(), 'val_elf501_disas_type', '01', '0206震災受災戶','zh_TW', 100, 'sys', current timestamp);
			insert into com.bcodetype (oid,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) values( get_oid(), 'val_elf501_disas_type', '01', '0206震災受災戶','zh_CN', 100, 'sys', current timestamp);
			insert into com.bcodetype (oid,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) values( get_oid(), 'val_elf501_disas_type', '01', '0206震災受災戶','en', 100, 'sys', current timestamp);

			</value>
		</entry> 
				
		<entry key="J-105-0108-001">
		    <!--Web e-Loan授信管理系統修改加拿大地區分行動審表。-->
			<value>
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'l160m01a.avoidDfCountry', 'CA','6|7|8|9|10|11|15|16|17|18|19|22|23|24',1,'','','zh_TW','system',current timestamp);

				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Environmental Indemnity Agreement', '', 1, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Environmental Indemnity Agreement', '', 1, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Environmental Indemnity Agreement', '', 1, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Assignment and Postponement', '', 2, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Assignment and Postponement', '', 2, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Assignment and Postponement', '', 2, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Postponement of Claims', '', 3, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Postponement of Claims', '', 3, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Postponement of Claims', '', 3, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Lawyer’s Preliminary Letter', '', 4, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Lawyer’s Preliminary Letter', '', 4, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Lawyer’s Preliminary Letter', '', 4, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Registration Draft Documents', '', 5, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Registration Draft Documents', '', 5, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Registration Draft Documents', '', 5, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'PPSA Draft', '', 6, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'PPSA Draft', '', 6, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'PPSA Draft', '', 6, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Title Insurance', '', 7, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Title Insurance', '', 7, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Title Insurance', '', 7, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Tax Dept.(Tax Bill)', '', 8, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Tax Dept.(Tax Bill)', '', 8, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Tax Dept.(Tax Bill)', '', 8, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Bldg.Dept.(Work Order)', '', 9, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Bldg.Dept.(Work Order)', '', 9, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Bldg.Dept.(Work Order)', '', 9, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Base on the Lawyer’s Preliminary Letter/Credit Committee’s/Risk Management Committee’s Approval and all related documents and received in order, the Bank will process the funding /changes on the Closing Date/Maturity Date/Effective Date', '', 10, '2', 'en', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Base on the Lawyer’s Preliminary Letter/Credit Committee’s/Risk Management Committee’s Approval and all related documents and received in order, the Bank will process the funding /changes on the Closing Date/Maturity Date/Effective Date', '', 10, '2', 'zh_CN', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), 'Z01', CURRENT TIMESTAMP, '918001', 'Base on the Lawyer’s Preliminary Letter/Credit Committee’s/Risk Management Committee’s Approval and all related documents and received in order, the Bank will process the funding /changes on the Closing Date/Maturity Date/Effective Date', '', 10, '2', 'zh_TW', CURRENT TIMESTAMP, '918001');
				
				
				SELECT * FROM LMS.L901M01A WHERE BRANCHID = 'Z02' AND ITEMTYPE = '2';
				SELECT * FROM LMS.L901M01A WHERE BRANCHID = 'Z03' AND ITEMTYPE = '2';
				SELECT * FROM LMS.L901M01A WHERE BRANCHID = 'Z05' AND ITEMTYPE = '2';
				
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) SELECT GET_OID(), 'Z02', CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER  FROM LMS.L901M01A WHERE BRANCHID = 'Z01' AND ITEMTYPE = '2';
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) SELECT GET_OID(), 'Z03', CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER  FROM LMS.L901M01A WHERE BRANCHID = 'Z01' AND ITEMTYPE = '2';
				INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) SELECT GET_OID(), 'Z05', CREATETIME, CREATOR, ITEMCONTENT,ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER  FROM LMS.L901M01A WHERE BRANCHID = 'Z01' AND ITEMTYPE = '2';

			</value>
		</entry> 
		
		<entry key="J-105-0191-001">
		    <!-- 配合額度明細表之科目顯示需求，修改簽報書案號2016台南(兆)授字第00067之額度明細表(213109400434)保證科目顯示-->
			<value>
				 select * from lms.l140m01a where mainid = '97b73ecbb380428c91354a49e10d579c';
				 update lms.l140m01a set lnSubject = 'A.應收款保證及承兌墊款(一般履約保證)' where mainid = '97b73ecbb380428c91354a49e10d579c';
			</value>
		</entry>
				
		<entry key="J-105-0217_1">
		    <!-- 金管會105.9.5對本行一般查核，消金大額暴險-->
			<value>
				<![CDATA[
				WITH 
				T0 AS (
					select distinct LNF155_CUST_ID AS T0_ID, LNF155_CUST_DUP AS T0_DUP from(
					   select LNF155_CUST_ID,LNF155_CUST_DUP, LNF155_CLASS_CODE, sum(LNF155_LOAN_BAL_TW)  as AMT 
					   from ln.LNF155 
					   where LNF155_DATA_YM='2016-07'  and LNF155_LOAN_CD='1' and LNF155_CLASS_CODE in ('C','CS') 
					      group by LNF155_CUST_ID,LNF155_CUST_DUP, LNF155_CLASS_CODE
					   ) T_T1 where (LNF155_CLASS_CODE='C' and AMT>10000000) or (LNF155_CLASS_CODE='CS' and AMT>30000000)
				),
				T_TOT AS
				(
					select LNF155_CUST_ID AS ID ,LNF155_CUST_DUP AS DUP, SUM(LNF155_LOAN_BAL_TW) AS TOT_AMT 
					from ln.lnf155
					where LNF155_DATA_YM='2016-07' and LNF155_LOAN_CD='1' and LNF155_CLASS_CODE in ('C','CS') 
				   group by LNF155_CUST_ID,LNF155_CUST_DUP
				), 
				TT AS
				(
				select m.*,LNF040_ACT_NAME_S , CNAME, TOT_AMT from
				ln.lnf155 m
				left outer join T_TOT on LNF155_CUST_ID=ID and LNF155_CUST_DUP=DUP
				left outer join mis.custData c on LNF155_CUST_ID=c.CUSTID and LNF155_CUST_DUP=c.DUPNO
				left outer join LN.LNF040 on SUBSTR(LNF155_LOAN_NO,5,3) = LNF040_LNAP_CODE
				inner join T0 on T0_ID=ID and T0_DUP=DUP
					where LNF155_DATA_YM='2016-07' and LNF155_LOAN_CD='1' and LNF155_CLASS_CODE in ('C','CS') 
				)
				select 
				LNF155_CUST_ID AS 統編
				,LNF155_CUST_DUP AS 重複碼
				,CNAME AS 姓名 
				,LNF155_CONTRACT AS 額度序號
				,LNF155_LOAN_NO AS 帳號
				,LNF155_BR_NO AS 帳掛營業單位  
				,SUBSTR(LNF155_LOAN_NO,5,3) AS  科目別
				,LNF040_ACT_NAME_S AS   科目名稱
				,LNF155_LOAN_BAL_TW AS 放款餘額
				,TOT_AMT AS 全行合計餘額  
				,LNF155_USE_DATE AS  初貸日
				,LNF155_USE_AMT AS  首撥金額
				,LNF155_PROD_CLASS AS  產品別
				,LNF155_LOAN_USE AS  用途別
				,LNF155_LN_PURPOSE AS   融資業務分類 
				,LNF155_RATE AS  利率
				,LNF155_AUTH AS  授權等級
				,LNF155_BEG_DATE AS  契約起日
				,LNF155_END_DATE AS  契約迄日
				,LNF155_FACT_SWFT AS  額度幣別
				,LNF155_FACT_AMT  AS  額度原幣金額
				,LNF155_FACT_AMT_TW AS   額度折台幣
				from TT order by LNF155_CUST_ID, LNF155_LOAN_NO
				]]> 
			</value>
		</entry>
		
		<entry key="J-105-0217_2">
		    <!-- 金管會105.9.5對本行一般查核，消金屬同一保證人或關係人之關係關聯戶-->
			<value>
				<![CDATA[	
				WITH T0_A AS (
				select LNF155_CUST_ID,LNF155_CUST_DUP,LNF155_CONTRACT, LNF155_LOAN_NO, LNF155_LOAN_BAL_TW
				,LNGEID,DUPNO1,LNGENM, LNGEKIND,LNGERE  
				from ln.LNF155, mis.ELLNGTEE
					   where LNF155_DATA_YM='2016-07' and LNF155_LOAN_CD='1' and LNF155_CLASS_CODE in ('C','CS') 
					      and LNGEFLAG  in ('C','G','N') AND LNF155_CONTRACT=CNTRNO AND LNF155_CUST_ID!=LNGEID
				) ,
				T0_B AS
				(  
				  select LNGEID as G_ID,DUPNO1 as G_DUP from
				   (select distinct LNGEID,DUPNO1,LNF155_CUST_ID,LNF155_CUST_DUP from T0_A ) t_t0_B
				   group by LNGEID,DUPNO1 having count(*)>1
				), 
				T0_R AS (
					select distinct LNGEID as S_ID, DUPNO1 as S_DUP, LNGENM as S_NM
					, LNF155_CUST_ID as REL_ID,LNF155_CUST_DUP   as REL_DUP, c.cname as REL_NM
					,LNF155_CONTRACT , LNGEKIND,LNGERE ,'' as CTYPE ,'' as APPT
					from t0_A inner join t0_B on t0_A.LNGEID=t0_B.G_ID and t0_A.DUPNO1=t0_B.G_DUP
					left outer join mis.custdata c on LNF155_CUST_ID =c.custId and LNF155_CUST_DUP=c.dupNo
				),
				T1 AS (
				select a.S_ID , a.S_DUP , c.CNAME as S_NM  , a.REL_ID , a.REL_DUP , a.REL_NM
				,LNF155_CONTRACT , '' as LNGEKIND ,'' as LNGERE  , CTYPE , APPT
				from
				( select CUSTID as S_ID,DUPNO as S_DUP, RCUSTID as REL_ID, DUPNO1 as REL_DUP
				        , CNAME as REL_NM, CTYPE, APPT from mis.ELCREPER
				   union 
				   (
					select distinct m1.custid as s_id, m1.dupno as s_dup, m1.mateId as rel_id , m2.dupno as rel_dup, m2.cname as rel_nm
					,'' as ctype, 'Z' as appt
					from mis.ELCREPER c
					inner join mis.custdata m1 on c.custid=m1.custid and c.dupno=m1.dupno 
					left outer join mis.custdata m2 on m1.mateid=m2.custid and m1.dupno=m2.dupno
					where m1.mateid>'' and m1.custid!=m1.mateid
				   )
				) 
				a inner join ln.lnf155 b on a.REL_ID=LNF155_CUST_ID and a.REL_DUP=LNF155_CUST_DUP
				left outer join mis.custdata c on a.S_ID=c.custId and a.S_DUP=c.dupNo
				where LNF155_DATA_YM='2016-07'
				      and LNF155_LOAN_CD='1'
				      and LNF155_CLASS_CODE in ('C','CS') 
				)      
				, T2 AS (
					select distinct S_ID,S_DUP, S_NM, REL_ID, REL_DUP,REL_NM,LNF155_CONTRACT as REL_CNTRNO from(
					select * from T0_R 
					union
					select * from T1
					) tmp
				)
				, T4 AS (
					select T2.*,T0_R.LNGEKIND,T0_R.LNGERE, T1.CTYPE, T1.APPT 
					from T2 
					left outer join T0_R  on T2.S_ID=T0_R.S_ID and T2.S_DUP=T0_R.S_DUP and T2.REL_ID=T0_R.REL_ID and T2.REL_DUP=T0_R.REL_DUP and T2.REL_CNTRNO=T0_R.LNF155_CONTRACT
					left outer join T1      on T2.S_ID=T1.S_ID and T2.S_DUP=T1.S_DUP and T2.REL_ID=T1.REL_ID and T2.REL_DUP=T1.REL_DUP and T2.REL_CNTRNO=T1.LNF155_CONTRACT
				)
				, T_RESULT_0 AS (
					select T4.*, 'GS' as sortFg from T4 
					union
					(
					   select S_ID,S_DUP, S_NM, REL_ID, REL_DUP,  REL_NM, LNF155_CONTRACT as REL_CNTRNO, LNGEKIND, LNGERE, CTYPE, APPT,  sortFg
					    from
					    (
						    select S_ID, S_DUP,S_NM, S_ID as REL_ID,S_DUP as REL_DUP, S_NM as REL_NM
						    ,'' as LNGEKIND,'' as LNGERE,'' as CTYPE,'' as APPT, 'GM' as sortFg 
						    from 
						    (
							select distinct S_ID,S_DUP, S_NM from t2
						    ) tm
					    ) t left outer join ln.lnf155 on t.S_ID=LNF155_CUST_ID and t.S_DUP=LNF155_CUST_DUP
					    and LNF155_DATA_YM='2016-07' and LNF155_LOAN_CD='1' and LNF155_CLASS_CODE in ('C','CS') 
					)
				), T_RESULT as (
					select * from T_RESULT_0 where (S_ID, S_DUP) in 
					(
					select S_ID as J_ID, S_DUP as J_DUP from(
					 select distinct S_ID,S_DUP, S_NM, REL_ID, REL_DUP,  REL_NM from T_RESULT_0
					) TJ_TAB group by S_ID,S_DUP having count(*)>1
					)
				),
				T_TOT AS
				(
					select LNF155_CUST_ID AS ID ,LNF155_CUST_DUP AS DUP, SUM(LNF155_LOAN_BAL_TW) AS TOT_AMT 
					from ln.lnf155
					where LNF155_DATA_YM='2016-07'  and LNF155_LOAN_CD='1' and LNF155_CLASS_CODE in ('C','CS') 
					group by LNF155_CUST_ID,LNF155_CUST_DUP
				), 
				TT AS
				(
				select t.*, TOT_AMT, m.*,LNF040_ACT_NAME_S from
				T_RESULT t
				left outer join T_TOT on REL_ID=ID and REL_DUP=DUP
				inner join ln.lnf155 m on REL_ID=LNF155_CUST_ID and REL_DUP=LNF155_CUST_DUP and REL_CNTRNO=LNF155_CONTRACT
					and LNF155_DATA_YM='2016-07'  and LNF155_LOAN_CD='1' and LNF155_CLASS_CODE in ('C','CS') 
				left outer join LN.LNF040 on SUBSTR(LNF155_LOAN_NO,5,3) = LNF040_LNAP_CODE	
				)
				select 
				S_ID  as 同一保證人或關係人統編
				,S_DUP  as 同一保證人或關係人重複碼
				,S_NM  as 同一保證人或關係人姓名 
				,REL_ID as 關係戶統編
				,REL_DUP  as 關係戶重複碼
				,REL_NM  as 關係戶姓名
				,LNGERE as 保證人關係代碼
				, (case LNGERE
				when '1D' then '授信企業之負責人-兄弟姐妹'
				when '1K' then '授信企業之負責人-其他親屬'
				when '1L' then '授信企業之負責人-其他非親屬自然人'
				when '1X' then '授信企業之負責人'
				when '2L' then '授信企業之董事-其他非親屬自然人'
				when '2X' then '授信企業之董事'
				when '4X' then '授信企業之監察人'
				when '6K' then '授信企業之股東-其他親屬'
				when '6L' then '授信企業之股東-其他非親屬自然人'
				when '6X' then '授信企業之股東'
				when '7X' then '授信企業之總經理'
				when '8L' then '授信企業之其他經理人-其他非親屬自然人'
				when '8X' then '授信企業之其他經理人'
				when '9X' then '授信企業之關係企業'
				when 'AX' then '其他企業'
				when 'BX' then '授信戶擔任負責人之企業'
				when 'X0' then '本人'
				when 'XA'then '配偶'
				when 'XB' then '父母'
				when 'XC' then '子女'
				when 'XD' then '兄弟姐妹'
				when 'XI' then '配偶之父母'
				when 'XJ' then '配偶之兄弟姐妹'
				when 'XK' then '其他親屬'
				when 'XL' then '其他非親屬自然人'
				end) as 保證人關係
				,APPT as 關係戶稱謂代碼
				, (case APPT
				when '1' then '祖(外祖)父母'
				when '2' then '父母'
				when '3' then '兄弟姊妹'
				when '4' then '子女'
				when '5' then '孫(外孫)子女'
				when 'Z' then '配偶'
				end) as 關係戶稱謂
				,LNF155_CONTRACT AS 額度序號
				,LNF155_LOAN_NO AS 帳號
				,LNF155_BR_NO AS 帳掛營業單位  
				,SUBSTR(LNF155_LOAN_NO,5,3) AS  科目別
				,LNF040_ACT_NAME_S AS   科目名稱
				,LNF155_LOAN_BAL_TW AS 放款餘額
				,TOT_AMT AS 全行合計餘額  
				,LNF155_USE_DATE AS  初貸日
				,LNF155_USE_AMT AS  首撥金額
				,LNF155_PROD_CLASS AS  產品別
				,LNF155_LOAN_USE AS  用途別
				,LNF155_LN_PURPOSE AS   融資業務分類 
				,LNF155_RATE AS  利率
				,LNF155_AUTH AS  授權等級
				,LNF155_BEG_DATE AS  契約起日
				,LNF155_END_DATE AS  契約迄日
				,LNF155_FACT_SWFT AS  額度幣別
				,LNF155_FACT_AMT  AS  額度原幣金額
				,LNF155_FACT_AMT_TW AS   額度折台幣
				
				from TT  order by s_id, s_dup, sortFg, rel_id,LNF155_CONTRACT
				]]>
			</value>
		</entry>
		
		<entry key="J-105-0217_3">
		    <!-- 金管會105.9.5對本行一般查核，消金觀察預警戶、進件評分欠佳案件-->
			<value>
				<![CDATA[	
				WITH 
				T_WARN AS (
					select distinct LNFE0854_CUSTID, LNFE0854_DUPNO, 'V' as lnfe_data  from ln.LNFE0854 where LNFE0854_DOC_NO like '%CLS%' 
					or ((ascii(LNFE0854_CUSTID) between 65 and 90)  AND (LNFE0854_CUSTID like '_1%' or LNFE0854_CUSTID like '_2%' ))
				),
				T_DW AS(
					select distinct lnf020_contract as rk_cntrno, 'V' as dw_data from ln.lnf020 where lnf020_contract in (
						'002108790105','002109300442','002109300443','002109300507','002109900068','002109900336','002109900365','002109901338','002110000225','002110100149','002110300483','002110500124','0031092T0326','003109400344','0031094T0262','003109900066','003110000067','003110200198','003110200218','003110300222','003110300246','003110500085','003110500086','004108590134','004109200511','004109301504','004109301505','004109400016','004109400874','004109500746','004109600263','004109700533','004109700534','004109800210','004109800353','004109900011','004109900012','004109900105','004109900106','004109900107','004109900108','004109900157','004109900159','004109900486','004109900495','004109900496','004109900511','004109900584','004109900592','004109900642','004109900677','004109900686','004109900699','004109900776','004109900818','004109900825','004109900837','004110000005','004110000006','004110000015','004110000017','004110000018','004110000050','004110000053','004110000054','004110000055','004110000239','004110000249','004110000264','004110000329','004110000344','004110000355','004110000384','004110000441','004110000442','004110000516','004110100004','004110100024','004110100068','004110100088','004110100133','004110100134','004110100170','004110100179','004110100183','004110100184','004110100196','004110100203','004110100204','004110100308','004110100310','004110100386','004110200005','004110200102','004110200253','004110200258','004110200310','004110200332','004110200375','004110200377','004110200378','004110200517','004110200523','004110200524','004110200539','004110200543','004110200544','004110300119','004110300122','004110300152','004110300154','004110300156','004110300177','004110300183','004110300184','004110300201','004110300247','004110300291','004110300300','004110300330','004110300357','004110300360','004110300368','004110300370','004110300379','004110400084','004110400128','004110400203','004110400229','004110400295','004110500067','005109900051','005109900071','005110100007','005110100135','005110100136','005110200015','005110200016','005110300136','005110300151','005110300152','006108490063','006109190174','006109300004','006109800111','006110000028','006110200115','006110200116','006110300046','006110400041','006110400110','006110500008','008109600052','008109600221','008109700036','008109800074','008109800075','008109800086','008109900223','008109900359','008110200017','008110200046','008110200260','008110200355','008110300075','008110300176','008110300224','008110400030','008110400120','008110500039','010108890206','010109190138','010109800127','010109880011','010109900104','010109900117','010109900248','010109900263','010109900369','010109900376','010109900377','010110000091','010110000270','010110000328','010110000329','010110000336','010110000345','010110000346','010110200009','010110200549','010110200630','010110200704','012109400177','012109900065','012109900157','012109900207','012109900249','012109900296','012109900339','012110000003','012110000057','012110000183','012110000198','012110000217','012110000293','012110100066','012110200151','012110300043','012110300067','012110300142','013109290124','013109400019','013109600008','013109900069','013109900082','013109900095','013110000010','013110000022','013110000049','013110000111','013110000112','013110000115','013110100036','013110100148','013110200150','013110200151','013110200223','013110400026','013110400029','013110400067','014109900048','014109900120','014109900196','014109900217','014110000023','014110000048','014110000049','014110000172','014110000222','014110000246','014110000261','014110000276','014110000277','014110000291','014110100003','014110100033','014110100104','014110100105','014110100180','014110200001','014110200114','014110200115','014110200121','014110200122','014110200149','014110200181','014110200182','014110200185','014110200192','014110200195','014110200198','014110200200','014110200208','014110200209','014110200216','014110200226','014110200248','014110200260','014110200266','014110200271','014110200287','014110200289','014110200291','014110200292','014110200334','014110200350','014110300005','014110300027','014110300046','014110300053','014110300083','014110300088','014110300122','014110300142','014110500022','014110500096','015109600110','015109900106','015109900112','015109900113','015109900115','015109900117','015110000015','015110000019','015110000020','015110000128','015110100038','015110100098','015110100106','015110100158','015110200051','015110200265','015110300147','016108990097','016109900086','016110000008','016110000057','017109300995','017109800077','017109900041','017109900052','017109900129','017110000020','017110000062','017110000097','017110000098','017110000157','017110000161','017110000166','017110000205','017110100157','017110200031','017110200032','017110200034','017110200052','017110200447','017110200448','017110200513','018109500036','018109500137','018109500169','018109500170','018109600134','018109600135','018109600136','018109600137','018109700081','018109700101','018109700103','018109700104','018109800072','018109900018','018109900096','018109900118','018110000086','018110000108','018110000109','018110000165','018110100022','018110100027','018110100029','018110100077','018110100114','018110100124','018110200018','018110200019','018110200056','019109300060','019109300261','019109300343','019109500234','019109700035','019109700036','019109700192','019109700193','019109800200','019109800294','019109900049','019109900068','019109900214','019109900248','019110000008','019110000015','019110000075','019110000092','019110000093','019110000197','019110000207','019110100212','019110200013','019110200143','019110200149','019110200150','019110200193','019110300007','019110400132','019110500004','019110500034','019110500035','020109290188','020109500043','020109700150','020109900246','020110100025','020110400028','020110400066','020110400071','020110400099','020110500021','021109700146','021109900059','021109900148','021110000112','021110100088','021110300069','022109500215','022109700241','022109800009','022109800134','022109800227','022109900049','022109900063','022109900066','022109900092','022109900103','022109900111','022109900112','022109900115','022109900166','022109900193','022109900205','022109900207','022109900279','022109900296','022109900297','022110000134','022110000167','022110000184','022110000217','022110000218','022110100009','022110100010','022110100241','022110300096','0221A9700260','023109600107','023109800123','023109900005','023109900023','023109900045','023109900236','023109900242','023109900245','023109900252','023109900257','023109900295','023110000050','023110000060','023110000065','023110000120','023110000140','023110000141','023110000146','023110000169','023110000178','023110000191','023110100081','023110200044','023110200070','023110200071','023110200072','023110400159','026109300192','026109300341','026109400388','026109500334','026109500335','026109600191','026109600212','026109800065','026109800179','026109900056','026109900058','026109900095','026109900108','026109900152','026109900156','026109900228','026109900261','026109900301','026109900343','026109900348','026109900367','026109900369','026109900386','026110000046','026110000095','026110000112','026110100120','026110200035','026110200086','026110200094','026110300056','026110300152','026110500029','027110000115','027110000116','027110100097','027110200202','027110300150','027110400260','028109200248','028109500038','028109600119','028109600187','028109800167','028109900002','028109900014','028109900087','028109900088','028109900110','028109900144','028110000013','028110000015','028110000016','028110000017','028110300176','028110400165','028110500004','028110500009','028110500043','028110500045','028110500091','028110500101','028110500102','029108890437','029109090074','029109300132','029109300133','029109400385','029109480015','029109500248','029109900016','029109900028','029109900032','029109900073','029109900081','029109900082','029109900111','029110000012','029110000056','029110000097','029110100136','029110100137','029110100139','029110100160','029110200001','029110300016','029110300043','029110400086','030109301204','030109900058','030109900066','030109900067','030109900106','030110000056','030110000070','030110000206','030110200055','030110200056','030110300043','031109800098','031109900043','031109900045','031109900073','031109900100','031110000023','031110000094','031110000135','031110100043','031110100054','031110100082','031110200075','031110200100','032109290174','032109290232','032109290263','032109290277','032109300386','032109400089','032109500101','032109600129','032109600130','032109600226','032109800110','032109800127','032109900007','032109900055','032109900063','032109900109','032109900110','032109900136','032109900158','032109900161','032109900168','032109900183','032110000004','032110000007','032110000012','032110000013','032110000034','032110000036','032110000059','032110000076','032110000080','032110100008','032110100146','032110200137','032110200159','032110200190','032110200206','032110300033','032110300070','032110300122','032110300143','032110400006','032110400136','032110400144','032110500020','034109600378','034109700156','034109700261','034109700277','034109800163','034109900079','034109900094','034109900119','034109900148','034109900155','034109900216','034110000012','034110000013','034110000026','034110000059','034110000099','034110000116','034110200227','035109200772','035109290697','035109290705','035109290713','035109500051','035109700024','035109700141','035109900006','035109900176','035109900294','035110000002','035110000029','035110000031','035110000052','035110000104','035110000125','035110000138','035110000155','035110000192','035110200010','035110200031','035110200124','035110200129','035110200145','035110200167','035110200171','035110200251','035110300019','035110300020','035110300147','035110300193','035110400008','035110400020','035110400073','035110400130','035110400134','035110400193','035110400195','035110500061','035110500107','036109500016','036109700039','036109900077','036109900080','036110000046','036110000056','036110100004','036110100036','036110200100','036110300002','037109290721','037109400401','037109400642','037109400643','037109500664','037109500676','037109500678','037109600063','037109600472','037109700141','037109700232','037109700282','037109800314','037109800351','037109800390','037109800405','037109900001','037109900003','037109900261','037109900457','037110000019','037110000190','037110000199','037110000298','037110000338','037110000365','037110000387','037110100172','037110200233','037110300066','037110300116','037110300138','037110300287','037110300288','037110400078','037110400083','037110500069','037110500105','038109190017','038109290047','038109300085','038109300110','038109400116','0381095A0251','038109700142','038109900075','038109900139','038109900141','038109900142','038109900163','038110000010','038110000019','038110000053','038110100025','038110100061','038110100062','038110200017','038110200027','038110300026','038110300027','039109500040','039109900059','039109900069','039110000060','039110300155','040109500097','040109900035','040109900036','040109900040','040109900041','040109900042','040110200009','040110200092','040110300036','041109290112','041109700114','041109800337','041109900047','041109900059','041109900084','041109900090','041109900106','041109900223','041109900245','041109900341','041109900344','041109900370','041110000003','041110000123','041110000273','041110000274','041110000330','041110000344','041110100077','041110200174','041110400173','042109300054','042109600064','042109700101','042109900020','042110000021','042110000022','042110000051','042110000104','042110100025','042110100056','042110100094','042110200016','042110200027','042110200039','042110200179','042110300018','042110400018','043109290027','043109500191','043109900029','043109900030','043109900031','043109900041','043109900042','043109900097','043110000068','043110000069','043110000084','043110000088','043110100044','043110100080','043110100132','043110200032','043110400155','044109500166','044109500181','044109800091','044109900089','044109900090','044109900092','044109900093','044109900117','044109900153','044109900163','044109900211','044109900245','044110000050','044110000100','044110000169','044110000209','044110100126','044110200005','044110200008','044110200198','044110300018','044110300045','044110300046','044110300047','045109900036','045109900053','045109900151','045110000077','045110000100','045110200003','045110300094','046109800287','046109900044','046109900068','046109900071','046109900079','046109900088','046109900095','046109900168','046109900179','046109900188','046109900191','046110000023','046110000028','046110000029','046110000166','046110000170','046110200203','046110400127','047110000070','047110100023','047110300083','048109900001','048109900008','048109900029','048110000067','048110200239','049109400371','049109900035','049109900052','049110000007','049110000008','049110000010','049110000033','049110200136','050109900018','050109900152','050110200117','050110300024','051109900160','051109900204','051110200087','051110200088','051110300078','052109300136','052109500152','052109900036','052109900080','052109900088','052110000036','052110000046','052110000059','052110000080','052110000097','052110100014','052110100063','052110200065','052110300038','052110300039','053108590202','053109300236','053109900058','053109900071','053109900092','053110000003','053110100010','053110200085','055109900065','055109900070','055110400078','056109900007','056109900035','056110100048','056110200059','057109500013','057109600091','057109700165','057109700170','057109800120','057109800121','057109800139','057109900036','057109900072','057109900073','057109900102','057110000054','057110100013','057110200065','057110200075','057110400010','057110400021','058109300194','058109400689','058109400690','058109500044','058109500062','058109600060','058109700005','058109700034','058109900043','058109900069','058109900084','058110000073','058110000074','058110100016','058110100051','058110100052','058110100132','058110200005','058110200164','058110200214','058110400053','058110500005','058110500006','059108690189','059108690217','059110200014','059110200086','059110200152','059110300072','060109290131','060109300016','060109600141','060109900031','060109900032','060109900038','060109900039','060110000144','060110000148','060110000163','060110000188','060110100038','060110100073','060110200022','060110200031','060110200070','060110200093','060110200096','060110500032','060110500038','061109500159','061109700183','061109900002','061109900010','061109900055','061109900063','061109900069','061109900077','061110000062','061110000092','061110100048','061110100103','061110100142','061110200002','061110200003','061110200024','061110200031','061110200046','061110200047','061110200054','061110200138','061110200143','061110200154','061110200160','061110400001','061110400049','061110500024','062109600054','062109800219','062109800254','062109900057','062109900095','062109900114','062109900142','062110000011','062110000012','062110000105','062110100078','062110100113','062110200032','062110200033','062110200039','062110200065','062110200076','062110200125','062110200178','062110300049','062110400060','062110400073','062110400110','063109290032','063109700065','063109700088','063109800074','063109800082','063109800087','063109900086','063109900097','063110000013','063110000056','063110100060','063110200058','063110300018','063110400043','063110400044','063110400096','063110400103','064108890119','064109500031','064109500090','064109600123','064109700095','064109900039','064109900061','064109900090','064110000010','064110000026','064110000032','064110000033','064110000034','064110100002','064110100004','064110100005','064110100009','064110100010','064110100034','064110100070','064110200009','064110200013','064110200017','064110200020','064110200036','064110200037','064110200050','064110200051','064110200062','064110200065','064110300022','064110300027','064110300036','064110300046','064110300051','064110300061','064110300063','064110400007','064110400010','064110500006','064110500043','065109290225','065109800041','065109800094','065109900053','065109900085','065109900126','065109900131','065109900132','065110000029','065110000059','065110000108','065110100044','065110200002','065110200059','065110300020','066109300023','066109900162','066110000044','066110000741','066110200026','066110200110','066110200174','067108990210','067109800010','067109900019','067110400043','068109700072','068109800040','068109800041','068109900031','068109900032','068109900048','068109900061','068109900080','068109900102','068109900129','068110000011','068110000031','068110000089','068110000115','068110000117','068110000127','068110100019','068110100083','068110100139','068110100141','068110100150','068110100152','068110100172','068110200037','068110200039','068110200040','068110200050','068110200057','068110200058','068110200060','068110200064','068110200067','068110200084','068110200108','068110200114','068110200115','068110200125','069109900064','069109900072','069109900076','069109900123','069109900167','069109900182','069109900268','069109900273','069109900331','069109900381','069109900484','069110000107','069110000179','069110000182','069110000208','069110000211','069110000247','069110000264','069110000298','069110000316','069110000362','069110000366','069110000381','069110000395','069110000409','069110000562','069110000604','069110000612','069110100112','069110100134','069110100209','069110100339','069110100492','069110200016','069110200252','069110200253','069110200363','069110200419','069110300088','069110300390','069110400199','069110400259','070109700060','070109900152','070109900168','070109900178','070109900197','070109900281','070109900304','070109900306','070109900399','070109900402','070110000141','070110000174','070110000176','070110000178','070110000267','070110000298','070110100029','070110100194','070110100399','070110200145','070110200147','070110200382','070110200384','070110200387','070110200456','070110300184','070110400176','071109300120','071109300277','071109500171','071109700020','071109700041','071109800121','071109800161','071109900047','071109900097','071109900098','071109900105','071109900135','071109900136','071110000096','071110000124','071110000156','071110100009','071110100075','071110100080','071110100111','071110200043','071110200155','071110300002','071110300006','071110300022','071110300062','071110300065','071110300078','071110300082','071110300093','071110300170','071110300174','071110400010','072109500076','072109500092','072109700043','072109700046','072109800001','072109900014','072109900017','072109900022','072110100063','072110200054','072110200055','074109800148','074109900074','074109900075','074109900086','074109900093','074109900107','074109900121','074109900142','074109900158','074109900189','074109900206','074110000030','074110100021','074110100052','074110100053','074110200025','074110200092','075110100013','075110200112','075110400049','076110100052','076110100114','076110100121','076110200131','076110300012','076110300013','076110300032','0771098T0135','0771098T0222','0771099T0047','077110000005','077110100019','077110100022','077110100023','077110100064','077110100066','077110200007','077110200036','077110200037','077110200038','077110200048','077110200095','077110200096','077110300021','077110300022','077110300031','077110300032','077110300124','077110300125','077110400051','077110400054','077110400083','077110400086','077110400087','079110300026','080109900060','080109900118','080109900119','080109900124','080109900177','080110000102','080110000103','080110100153','080110200033','080110200129','080110200184','201109300192','201109400091','201109700286','201109800088','201109900335','201109900472','201109900525','201110000032','201110000039','201110000074','201110000075','201110000208','201110000408','201110400026','201110400027','201110400032','202109600014','202109900042','202109900043','202109900050','202109900051','202109900219','202109900220','202110000025','202110000026','202110000069','202110000112','202110000129','202110000176','202110000177','202110100024','202110300067','203109700123','203109900028','203109900089','203110000013','203110000017','203110000035','203110200073','203110300157','204109900054','204109900090','204109900143','204110000013','204110000025','204110100048','204110100127','204110200008','204110200032','204110200066','204110200109','204110200208','204110200211','204110200221','204110200248','204110200262','204110300040','204110300062','204110300136','205109900097','205110000017','205110000025','205110000031','205110000055','205110000086','205110200009','206109600092','206109900040','206109900091','206110100001','206110100003','206110100127','206110300086','207109300205','207109300346','207109400287','207109900023','207109900027','2071099A0302','2071099A0306','2071099A0307','207110000249','207110100151','207110100184','207110200196','207110200286','208109800085','208109900035','208110200059','208110300005','208110300016','210109900002','210109900017','210109900059','210109900064','210109900080','210109900131','210109900132','210110000059','210110100051','210110100052','210110200214','210110400081','212109200333','212109200384','212109200488','212109400249','212109500205','212109550027','212109900034','212109900050','212109900072','212109900145','212110000069','212110100063','212110200010','212110200086','213109500035','213109900001','213109900018','213109900088','213110000063','213110000064','213110000085','213110100161','213110200138','214109600177','214109700055','214109800034','214109900029','214109900041','214109900042','214109900045','214110000070','214110100046','214110100047','214110100048','214110100104','214110200005','214110300037','214110300048','214110300076','214110300087','214110300125','214110300131','214110500001','215109900124','215109900135','215109900335','215110200092','215110200107','215110200108','215110200138','216109900070','216110000035','216110100114','216110200165','216110200172','216110500022','216110500025','219110100172','219110100173','219110100174','219110100175','219110400049','220109900039','220109900040','220110000002','220110000011','220110000012','220110000042','220110100016','220110100045','220110100060','220110100068','220110100069','220110100071','220110100072','220110200013','220110200032','220110200083','220110300029','220110300030','220110300058','220110300068','220110300080','220110400028','220110500007','220110500010','220110500016','220110500036','226109300420','226109800040','226109800041','226109800188','226109800189','226109900012','226109900024','226109900033','226109900118','226109900127','226109900203','226109900206','226109900207','226109900212','226110000021','226110000032','226110300038','226110400026','227109800026','227109900019','227109900030','227109900076','227110000028','227110100010','227110100017','227110100021','227110200031','228109600030','228109600046','228109600124','228109600135','228109600138','228109600181','228109600190','228109700058','228109700121','228109700131','228109700135','228109800016','228109800049','228109800051','228109800052','228109900025','228109900061','228109900072','228109900074','228109900075','228109900079','228109900095','228109900098','228109900105','228109900126','228109900137','228109900145','228109900147','228110000002','228110000006','228110000008','228110000027','228110000031','228110000039','228110000053','228110000054','228110000091','228110100017','228110100026','228110100027','228110100031','228110100039','228110100059','228110100061','228110100105','228110100147','228110100153','228110100154','228110100218','228110100219','228110200009','228110200013','228110200035','228110200070','228110200082','228110200092','228110200104','228110400071','2281A9700127','229109900052','229109900071','229109900080','229109900107','229109900196','229109900198','229109900201','229109900358','229110000060','229110000155','229110000344','229110100654','229110200193','229110200224','229110300093','229110500144','231108800009','231109900027','231109900061','231110100012','231110100013','231110100031','231110200207','232109300023','232109600014','232109600149','232109700006','232109800063','23210990005A','232109900088','232109900101','232110000071','232110000079','232110100028','232110100035','232110100056','232110100067','232110200005','232110200039','232110200047','232110200120','232110200125','232110200136','232110200137','232110200142','232110300014','232110300027','232110300083','232110300087','232110300097','232110300107','232110500020','232110500021','233109600171','233109700003','233109700020','233109900064','233110000009','233110000010','233110000070','233110100084','233110200072','233110200103','233110300039','233110300050','234109900088','234110000199','234110300114','235109600210','235109700448','235109700519','235109900143','235109900144','235109900242','235109900243','235109900269','235109900467','235109900468','235109900480','235109900506','235109900571','235109900620','235109900787','235109900788','235109900938','235110000318','235110000352','235110000398','235110000428','235110000431','235110000445','235110000547','235110000579','235110100103','235110200015','235110200059','235110200477','235110200478','235110300034','2361097A0003','236109900097','236109900110','236110000083','236110000112','236110000186','236110000200','236110100021','236110100120','236110200032','236110200040','236110200044','236110200048','236110200081','236110200090','236110200091','236110200096','236110200097','236110200140','236110200145','236110200152','236110200160','236110200172','236110200179','236110200180','237109500490','237109900007','237109900013','237109900020','237109900058','237109900066','237109900094','237110000023','237110000026','237110000097','237110300067','237110400004','238109800052','238109800053','238109900062','238109900066','238109900067','238109900069','238109900070','238109900168','238109900176','238109900183','238109900193','238109900214','238109900305','238109900358','238110000010','238110000036','238110000058','238110000107','238110000192','238110000205','238110000323','238110100335','238110100336','238110200020','238110200296','238110200399','238110400096','240109300326','240109600203','240109800129','240109900047','240109900100','240109900107','240109900108','240109900175','240109900292','240110000029','240110000037','240110300138','240110400033','241109190010','241109600145','241109900161','241109900166','241110000148','241110000153','241110100007','241110100118','241110100119','241110100151','241110200071','241110200072','241110300113','241110500023','242109600146','242109900007','242110000063','242110100015','242110100084','242110200005','242110200027','242110200028','242110400060'
					)
				),
				T0 AS (
					select distinct LNF155_CUST_ID AS T0_ID, LNF155_CUST_DUP AS T0_DUP, LNF155_CONTRACT as T0_CNTRNO,lnfe_data, dw_data  
					from ln.LNF155 
					left outer join T_WARN ON LNF155_CUST_ID=LNFE0854_CUSTID and LNF155_CUST_DUP=LNFE0854_DUPNO
					left outer join T_DW on LNF155_CONTRACT=rk_cntrno
					 where  LNF155_DATA_YM='2016-07'  and LNF155_LOAN_CD='1' and LNF155_CLASS_CODE in ('C','CS') 
					 and (lnfe_data>'' or dw_data>'')
				),
				T_TOT AS
				(
					select LNF155_CUST_ID AS ID ,LNF155_CUST_DUP AS DUP, SUM(LNF155_LOAN_BAL_TW) AS TOT_AMT 
					from ln.lnf155
					where LNF155_DATA_YM='2016-07'  and LNF155_LOAN_CD='1' and LNF155_CLASS_CODE in ('C','CS') 
				   group by LNF155_CUST_ID,LNF155_CUST_DUP
				),
				TT AS(
				select m.*,lnfe_data, dw_data ,LNF040_ACT_NAME_S , CNAME, TOT_AMT from 
				ln.lnf155 m
				left outer join T_TOT on LNF155_CUST_ID=ID and LNF155_CUST_DUP=DUP
				left outer join mis.custData c on LNF155_CUST_ID=c.CUSTID and LNF155_CUST_DUP=c.DUPNO
				left outer join LN.LNF040 on SUBSTR(LNF155_LOAN_NO,5,3) = LNF040_LNAP_CODE
				left outer join t0  on T0_ID=LNF155_CUST_ID and T0_DUP=LNF155_CUST_DUP AND T0_CNTRNO=LNF155_CONTRACT
					where LNF155_DATA_YM='2016-07'  and LNF155_LOAN_CD='1' and LNF155_CLASS_CODE in ('C','CS') 
					      and (LNF155_CUST_ID,LNF155_CUST_DUP) in (select T0_ID,  T0_DUP from T0)
				)
				 select 
				LNF155_CUST_ID AS 統編
				,LNF155_CUST_DUP AS 重複碼
				,CNAME AS 姓名 
				,lnfe_data AS 異常通報
				,dw_data AS 評等欠佳 
				,LNF155_CONTRACT AS 額度序號
				,LNF155_LOAN_NO AS 帳號
				,LNF155_BR_NO AS 帳掛營業單位  
				,SUBSTR(LNF155_LOAN_NO,5,3) AS  科目別
				,LNF040_ACT_NAME_S AS   科目名稱
				,LNF155_LOAN_BAL_TW AS 放款餘額
				,TOT_AMT AS 全行合計餘額  
				,LNF155_USE_DATE AS  初貸日
				,LNF155_USE_AMT AS  首撥金額
				,LNF155_PROD_CLASS AS  產品別
				,LNF155_LOAN_USE AS  用途別
				,LNF155_LN_PURPOSE AS   融資業務分類 
				,LNF155_RATE AS  利率
				,LNF155_AUTH AS  授權等級
				,LNF155_BEG_DATE AS  契約起日
				,LNF155_END_DATE AS  契約迄日
				,LNF155_FACT_SWFT AS  額度幣別
				,LNF155_FACT_AMT  AS  額度原幣金額
				,LNF155_FACT_AMT_TW AS   額度折台幣
				from TT order by LNF155_CUST_ID, LNF155_LOAN_NO
				]]>
			</value>
		</entry>
		
		<entry key="J-105-0230_3">
		    <!-- 金管會105.9.5對本行一般查核，企業戶大額曝險 -->
			<value>
				<![CDATA[	
				select  substr(LNF155_CUST_ID,1,10)|| LNF155_CUST_DUP as IdDup ,'V' as FG from(
				   select LNF155_CUST_ID,LNF155_CUST_DUP, LNF155_CLASS_CODE, sum(LNF155_LOAN_BAL_TW)  as AMT from ln.LNF155 
				   where  LNF155_DATA_YM='2016-07'  and LNF155_LOAN_CD='1' and LNF155_CLASS_CODE in ('B','BS') 
				   group by LNF155_CUST_ID,LNF155_CUST_DUP, LNF155_CLASS_CODE
				) T_T1 where (LNF155_CLASS_CODE='B' and AMT>50000000) or (LNF155_CLASS_CODE='BS' and AMT>100000000)
				]]>
			</value>
		</entry>
		
		<entry key="J-105-0230_4">
		    <!-- 金管會105.9.5對本行一般查核，103.5至105.7增加超過TWD1億餘額之企金明細 -->
			<value>
				<![CDATA[	
				WITH 
				T0 AS 
				(                                        
				   select LNF155_CUST_ID AS ID ,LNF155_CUST_DUP AS DUP, SUM(LNF155_LOAN_BAL_TW) AS TOT_AMT_2014 
				   from ln.lnf155
					where  LNF155_DATA_YM='2014-04'  and LNF155_LOAN_CD='1' 
					and LNF155_CLASS_CODE in ('B','BS')  and LNF155_LOAN_DEP='LN'
				   group by LNF155_CUST_ID,LNF155_CUST_DUP
				),
				T1 AS
				(                                        
				   select LNF155_CUST_ID AS ID ,LNF155_CUST_DUP AS DUP, SUM(LNF155_LOAN_BAL_TW) AS TOT_AMT_201607
				   from ln.lnf155
					where  LNF155_DATA_YM='2016-07'  and LNF155_LOAN_CD='1' 
					and LNF155_CLASS_CODE in ('B','BS')  and LNF155_LOAN_DEP='LN'
				   group by LNF155_CUST_ID,LNF155_CUST_DUP
				), 
				T2 AS(
				select T1.ID, T1.DUP, TOT_AMT_201607, value(TOT_AMT_2014,0) as TOT_AMT_2014 
				from T1 left outer join T0 ON T1.ID=T0.ID AND T1.DUP=T0.DUP
				), 
				TT AS(
				select  T2.*, c.CNAME, (TOT_AMT_201607 - TOT_AMT_2014) as DIFF from T2 
				left outer join mis.custData c on t2.ID=c.CUSTID and T2.DUP=c.DUPNO
				where TOT_AMT_201607 - TOT_AMT_2014>=100000000
				)
				 select substr(ID,1,10)|| DUP as IDDUP, 'V' as FG, CNAME AS 姓名 ,ID AS 統編,DUP AS 重複碼 
				 ,TOT_AMT_2014 ,TOT_AMT_201607, DIFF
				from TT order by ID
				]]>
			</value>
		</entry>
		
		<entry key="J-105-0185-001">
		    <!-- 請提供103年度及104年度國內所有分行之新作、增額及續約之授信件數及額度金額(包含企金及消金)-->
			<value>
				<![CDATA[
					EXPORT TO result2015.csv OF DEL MODIFIED BY NOCHARDEL codepage=950
					SELECT L140A.CUSTID,L140A.DUPNO,L140A.CUSTNAME,L140A.CNTRNO,
					L140A.CURRENTAPPLYCURR,L140A.CURRENTAPPLYAMT,
					RATE.ENDRATE AS TWDRATE,
					'TWD' AS CURR,
					(CASE WHEN L140A.CURRENTAPPLYCURR = 'TWD' THEN DECIMAL(L140A.CURRENTAPPLYAMT) ELSE DECIMAL(L140A.CURRENTAPPLYAMT * RATE.ENDRATE) END) AS CURRENTAPPLYAMT_TWD,
					L120A.CASELVL,L120A.ENDDATE,L120A.CASENO,L120A.CASEBRID,l140A.PROPERTY
					FROM 
					(SELECT * FROM LMS.L120M01A WHERE DOCSTATUS = '05O' AND ENDDATE BETWEEN '2015-01-01' AND '2015-12-31' AND DELETEDTIME IS NULL) AS L120A
					LEFT OUTER JOIN
					LMS.L120M01C AS L120C
					ON 
					L120C.MAINID = L120A.MAINID
					LEFT OUTER JOIN
					LMS.L140M01A AS L140A
					ON
					L120C.REFMAINID = L140A.MAINID
					LEFT OUTER JOIN
					(SELECT * FROM LMS.RATETBL WHERE DATAYMD = '1041231' ) AS RATE
					ON
					L140A.CURRENTAPPLYCURR = RATE.CURR
					WHERE
					L140A.DOCSTATUS = '030' AND
					L140A.DELETEDTIME IS NULL AND
					SUBSTR(L140A.CNTRNO,1,1) between '0' and '9' AND
					SUBSTR(L140A.CNTRNO,2,1) between '0' and '9' AND
					SUBSTR(L140A.CNTRNO,1,3) <> '918' AND
					((L140A.PROPERTY LIKE '%1%' OR L140A.PROPERTY LIKE '%5%' OR L140A.PROPERTY LIKE '%2%' ) AND (L140A.PROPERTY NOT LIKE '%10%'  AND L140A.PROPERTY NOT LIKE '%12%' AND L140A.PROPERTY NOT LIKE '%13%'))
					ORDER BY L140A.CNTRNO ASC FETCH FIRST 999999 ROWS ONLY;
				]]>
			</value>
		</entry>
		
		<entry key="J-105-0221-001">
		    <!-- 配合金管會105.9.5對本行一般查核，提供異常通報案件列管客戶名單-->
			<value>
				<![CDATA[
					select LNFE0854_CUSTID,LNFE0854_DUPNO from ln.lnfe0854 where LNFE0854_CLOSEFG <> 'Y' group by LNFE0854_CUSTID,LNFE0854_DUPNO order by LNFE0854_CUSTID ASC;
				]]>
			</value>
		</entry>
		
		<entry key="J-105-0272-001">
		    <!--配合大里行台灣力得衛宇龍科技公司80120858個案核定條件,同意其提供十成本行存款設質時,得由分行授權內辦理,暫免受異常通報管制。-->
			<value>
				<![CDATA[
					UPDATE LMS.L120S01A INCLUDE (OLD_ABNORMALSTATUS char,OLD_ABNORMALSTATUS1 char) SET OLD_ABNORMALSTATUS = ABNORMALSTATUS, ABNORMALSTATUS = 'Y', OLD_ABNORMALSTATUS1 = ABNORMALSTATUS1, ABNORMALSTATUS1 = ''  WHERE OID = '066EACDCF69744428461D6B59886323D');
					UPDATE LMS.L120S01A INCLUDE (OLD_ABNORMALSTATUS char,OLD_ABNORMALSTATUS1 char) SET OLD_ABNORMALSTATUS = ABNORMALSTATUS, ABNORMALSTATUS = 'N', OLD_ABNORMALSTATUS1 = ABNORMALSTATUS1, ABNORMALSTATUS1 = ''  WHERE OID = '066EACDCF69744428461D6B59886323D');
				]]>
			</value>
		</entry>
		
		
		<entry key="J-105-0319-001">
		    <!--J-105-0319-001 Web e-Loan修改201409900043約定融資額度註記為有條件可取消。-->
			<value>
				<![CDATA[
					update lms.l140m01a set EXCEPTFLAG = 'C' where mainid = '7a639c89ff7948b3891be1dd2427a6d6' and cntrno = '201409900043'
				]]>
			</value>
		</entry>
		
 
		<entry key="J-106-0069-001">
		    <!--J-106-0069-001 Web e-Loan提供授信案件核定層級彙總表，資料日期為2016年全年度及2017年1~2月彙總資料。 -->
			<value>
				<![CDATA[
					EXPORT TO 1060703_005097_01.csv OF DEL MODIFIED BY NOCHARDEL codepage=950 
					WITH L120TBL AS(
						SELECT T1.MAINID AS RPTMAINID,LMS.L120M01C.REFMAINID AS CNTRMAINID ,T1.CASELVL,T1.DOCTYPE
						FROM
						(
						  SELECT MAINID,CASELVL,DOCTYPE FROM LMS.L120M01A 
						  WHERE 
						  DOCSTATUS = '05O' AND 
						  ENDDATE BETWEEN '2016-01-01' AND '2016-12-31' AND   --ENDDATE BETWEEN '2017-01-01' AND '2017-02-28' AND
						  DOCCODE IN ('1','2') 
						) AS T1
						LEFT OUTER JOIN
						LMS.L120M01C
						ON
						T1.MAINID =  LMS.L120M01C.MAINID
					),
					L140TMP AS(
					  SELECT * FROM 
					  (
						  SELECT ROW_NUMBER() OVER (PARTITION BY L120TBL.RPTMAINID,T2.CUSTID,T2.DUPNO ORDER BY L120TBL.RPTMAINID,T2.CUSTID,T2.DUPNO,PRINTSEQ ASC ) AS SEQ,
						  L120TBL.RPTMAINID,L120TBL.CASELVL,L120TBL.DOCTYPE,LOANTOTCURR,LOANTOTAMT,incApplyTotAmt
						  FROM 
						  LMS.L140M01A AS T2,L120TBL
						  WHERE 
						  T2.MAINID =  L120TBL.CNTRMAINID AND
						  DOCSTATUS = '030'
					  ) AS X1
					  WHERE SEQ = '1'
					),
					L140SUM AS(
					  SELECT X2.RPTMAINID,X2.CASELVL,X2.DOCTYPE,SUM(X2.TWD_LOANTOTAMT) AS TOTAL_LOANTOTAMT,SUM(X2.TWD_LOANICRAMT) AS TOTAL_LOANICRAMT FROM
					  (
						  SELECT 
						  L140TMP.RPTMAINID,L140TMP.CASELVL,L140TMP.DOCTYPE,'TWD' AS TWD_LOANTOTCURR,
						  (CASE WHEN LOANTOTCURR = 'TWD' THEN LOANTOTAMT ELSE LOANTOTAMT * ENDRATE END ) AS TWD_LOANTOTAMT ,
						  (CASE WHEN LOANTOTCURR = 'TWD' THEN incApplyTotAmt ELSE incApplyTotAmt * ENDRATE END ) AS TWD_LOANICRAMT 
						  FROM 
						  L140TMP 
						  LEFT OUTER JOIN
						  (SELECT * FROM LMS.RATETBL WHERE DATAYMD = (SELECT MAX(DATAYMD) FROM LMS.RATETBL) ) AS T3
						  ON
						  L140TMP.LOANTOTCURR= T3.CURR
						) AS X2  
						GROUP BY X2.RPTMAINID,X2.CASELVL,X2.DOCTYPE
					)
					SELECT
					COUNT_A,AMT_A,AMT_A_PLUS,
					COUNT_B,AMT_B,AMT_B_PLUS,
					COUNT_C,AMT_C,AMT_C_PLUS,
					COUNT_D,AMT_D,AMT_D_PLUS,
					COUNT_E,AMT_E,AMT_E_PLUS,
					COUNT_F,AMT_F,AMT_F_PLUS,
					COUNT_CA,AMT_CA,AMT_CA_PLUS,
					COUNT_CB,AMT_CB,AMT_CB_PLUS,
					COUNT_CC,AMT_CC,AMT_CC_PLUS,
					COUNT_CD,AMT_CD,AMT_CD_PLUS,
					COUNT_CE,AMT_CE,AMT_CE_PLUS,
					COUNT_CF,AMT_CF,AMT_CF_PLUS
					FROM
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_A,SUM(TOTAL_LOANTOTAMT) AS AMT_A,SUM(TOTAL_LOANICRAMT) AS AMT_A_PLUS
						FROM L140SUM 
						WHERE 
						( CASELVL IS NULL OR CASELVL IN ('9','')) AND
						DOCTYPE = '1'
					)	AS A
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_B,SUM(TOTAL_LOANTOTAMT) AS AMT_B,SUM(TOTAL_LOANICRAMT) AS AMT_B_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('B') AND
						DOCTYPE = '1'
					)	AS B
					ON
					A.KEY = B.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_C,SUM(TOTAL_LOANTOTAMT) AS AMT_C,SUM(TOTAL_LOANICRAMT) AS AMT_C_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('8','4') AND
						DOCTYPE = '1'
					)	AS C
					ON
					A.KEY = C.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_D,SUM(TOTAL_LOANTOTAMT) AS AMT_D,SUM(TOTAL_LOANICRAMT) AS AMT_D_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('7') AND
						DOCTYPE = '1'
					)	AS D
					ON
					A.KEY = D.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_E,SUM(TOTAL_LOANTOTAMT) AS AMT_E,SUM(TOTAL_LOANICRAMT) AS AMT_E_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('6') AND
						DOCTYPE = '1'
					)	AS E
					ON
					A.KEY = E.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_F,SUM(TOTAL_LOANTOTAMT) AS AMT_F,SUM(TOTAL_LOANICRAMT) AS AMT_F_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('1','2','3','5','A','C') AND
						DOCTYPE = '1'
					)	AS F
					ON
					A.KEY = F.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_CA,SUM(TOTAL_LOANTOTAMT) AS AMT_CA,SUM(TOTAL_LOANICRAMT) AS AMT_CA_PLUS
						FROM L140SUM 
						WHERE 
						( CASELVL IS NULL OR CASELVL IN ('9','')) AND
						DOCTYPE = '2'
					)	AS CA
					ON
					A.KEY = CA.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_CB,SUM(TOTAL_LOANTOTAMT) AS AMT_CB,SUM(TOTAL_LOANICRAMT) AS AMT_CB_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('B') AND
						DOCTYPE = '2'
					)	AS CB
					ON
					A.KEY = CB.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_CC,SUM(TOTAL_LOANTOTAMT) AS AMT_CC,SUM(TOTAL_LOANICRAMT) AS AMT_CC_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('8','4') AND
						DOCTYPE = '2'
					)	AS CC
					ON
					A.KEY = CC.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_CD,SUM(TOTAL_LOANTOTAMT) AS AMT_CD,SUM(TOTAL_LOANICRAMT) AS AMT_CD_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('7') AND
						DOCTYPE = '2'
					)	AS CD
					ON
					A.KEY = CD.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_CE,SUM(TOTAL_LOANTOTAMT) AS AMT_CE,SUM(TOTAL_LOANICRAMT) AS AMT_CE_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('6') AND
						DOCTYPE = '2'
					)	AS CE
					ON
					A.KEY = CE.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_CF,SUM(TOTAL_LOANTOTAMT) AS AMT_CF,SUM(TOTAL_LOANICRAMT) AS AMT_CF_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('1','2','3','5','A','C') AND
						DOCTYPE = '2'
					)	AS CF
					ON
					A.KEY = CF.KEY FETCH FIRST 9999 ROWS ONLY;
				]]>
			</value>
		</entry>
		
 
		
		<entry key="J-106-0091-001">
		    <!-- J-106-0091 , 由J120049244 改 B125588258 -->
			<value>
				<![CDATA[
					update lms.l120m01a set custId='B125588258' where custId='J120049244'
				]]>
			</value>
		</entry>
		
		
		
		<entry key="J-106-0079-001">
		    <!--J-106-0079-001 Web e-Loan提供授信案件核定層級彙總表，資料日期為2013到2015年彙總資料。-->
			<value>
				<![CDATA[
					EXPORT TO 1060824_005097_13.csv OF DEL MODIFIED BY NOCHARDEL codepage=950 
					WITH L120TBL AS(
						SELECT T1.MAINID AS RPTMAINID,LMS.L120M01C.REFMAINID AS CNTRMAINID ,T1.CASELVL,T1.DOCTYPE
						FROM
						(
						  SELECT MAINID,CASELVL,DOCTYPE FROM LMS.L120M01A 
						  WHERE 
						  DOCSTATUS = '05O' AND 
						  ENDDATE BETWEEN '2013-01-01' AND '2013-12-31' AND
						  DOCCODE IN ('1','2') 
						) AS T1
						LEFT OUTER JOIN
						LMS.L120M01C
						ON
						T1.MAINID =  LMS.L120M01C.MAINID
					),
					L140TMP AS(
					  SELECT * FROM 
					  (
						  SELECT ROW_NUMBER() OVER (PARTITION BY L120TBL.RPTMAINID,T2.CUSTID,T2.DUPNO ORDER BY L120TBL.RPTMAINID,T2.CUSTID,T2.DUPNO,PRINTSEQ ASC ) AS SEQ,
						  L120TBL.RPTMAINID,L120TBL.CASELVL,L120TBL.DOCTYPE,LOANTOTCURR,LOANTOTAMT,incApplyTotAmt
						  FROM 
						  LMS.L140M01A AS T2,L120TBL
						  WHERE 
						  T2.MAINID =  L120TBL.CNTRMAINID AND
						  DOCSTATUS = '030'
					  ) AS X1
					  WHERE SEQ = '1'
					),
					L140SUM AS(
					  SELECT X2.RPTMAINID,X2.CASELVL,X2.DOCTYPE,SUM(X2.TWD_LOANTOTAMT) AS TOTAL_LOANTOTAMT,SUM(X2.TWD_LOANICRAMT) AS TOTAL_LOANICRAMT FROM
					  (
						  SELECT 
						  L140TMP.RPTMAINID,L140TMP.CASELVL,L140TMP.DOCTYPE,'TWD' AS TWD_LOANTOTCURR,
						  (CASE WHEN LOANTOTCURR = 'TWD' THEN LOANTOTAMT ELSE LOANTOTAMT * ENDRATE END ) AS TWD_LOANTOTAMT ,
						  (CASE WHEN LOANTOTCURR = 'TWD' THEN incApplyTotAmt ELSE incApplyTotAmt * ENDRATE END ) AS TWD_LOANICRAMT 
						  FROM 
						  L140TMP 
						  LEFT OUTER JOIN
						  (SELECT * FROM LMS.RATETBL WHERE DATAYMD = (SELECT MAX(DATAYMD) FROM LMS.RATETBL) ) AS T3
						  ON
						  L140TMP.LOANTOTCURR= T3.CURR
						) AS X2  
						GROUP BY X2.RPTMAINID,X2.CASELVL,X2.DOCTYPE
					)
					SELECT
					COUNT_A,AMT_A,AMT_A_PLUS,
					COUNT_B,AMT_B,AMT_B_PLUS,
					COUNT_C,AMT_C,AMT_C_PLUS,
					COUNT_D,AMT_D,AMT_D_PLUS,
					COUNT_E,AMT_E,AMT_E_PLUS,
					COUNT_F,AMT_F,AMT_F_PLUS,
					COUNT_CA,AMT_CA,AMT_CA_PLUS,
					COUNT_CB,AMT_CB,AMT_CB_PLUS,
					COUNT_CC,AMT_CC,AMT_CC_PLUS,
					COUNT_CD,AMT_CD,AMT_CD_PLUS,
					COUNT_CE,AMT_CE,AMT_CE_PLUS,
					COUNT_CF,AMT_CF,AMT_CF_PLUS
					FROM
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_A,SUM(TOTAL_LOANTOTAMT) AS AMT_A,SUM(TOTAL_LOANICRAMT) AS AMT_A_PLUS
						FROM L140SUM 
						WHERE 
						( CASELVL IS NULL OR CASELVL IN ('9','')) AND
						DOCTYPE = '1'
					)	AS A
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_B,SUM(TOTAL_LOANTOTAMT) AS AMT_B,SUM(TOTAL_LOANICRAMT) AS AMT_B_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('B') AND
						DOCTYPE = '1'
					)	AS B
					ON
					A.KEY = B.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_C,SUM(TOTAL_LOANTOTAMT) AS AMT_C,SUM(TOTAL_LOANICRAMT) AS AMT_C_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('8','4') AND
						DOCTYPE = '1'
					)	AS C
					ON
					A.KEY = C.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_D,SUM(TOTAL_LOANTOTAMT) AS AMT_D,SUM(TOTAL_LOANICRAMT) AS AMT_D_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('7') AND
						DOCTYPE = '1'
					)	AS D
					ON
					A.KEY = D.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_E,SUM(TOTAL_LOANTOTAMT) AS AMT_E,SUM(TOTAL_LOANICRAMT) AS AMT_E_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('6') AND
						DOCTYPE = '1'
					)	AS E
					ON
					A.KEY = E.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_F,SUM(TOTAL_LOANTOTAMT) AS AMT_F,SUM(TOTAL_LOANICRAMT) AS AMT_F_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('1','2','3','5','A','C') AND
						DOCTYPE = '1'
					)	AS F
					ON
					A.KEY = F.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_CA,SUM(TOTAL_LOANTOTAMT) AS AMT_CA,SUM(TOTAL_LOANICRAMT) AS AMT_CA_PLUS
						FROM L140SUM 
						WHERE 
						( CASELVL IS NULL OR CASELVL IN ('9','')) AND
						DOCTYPE = '2'
					)	AS CA
					ON
					A.KEY = CA.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_CB,SUM(TOTAL_LOANTOTAMT) AS AMT_CB,SUM(TOTAL_LOANICRAMT) AS AMT_CB_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('B') AND
						DOCTYPE = '2'
					)	AS CB
					ON
					A.KEY = CB.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_CC,SUM(TOTAL_LOANTOTAMT) AS AMT_CC,SUM(TOTAL_LOANICRAMT) AS AMT_CC_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('8','4') AND
						DOCTYPE = '2'
					)	AS CC
					ON
					A.KEY = CC.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_CD,SUM(TOTAL_LOANTOTAMT) AS AMT_CD,SUM(TOTAL_LOANICRAMT) AS AMT_CD_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('7') AND
						DOCTYPE = '2'
					)	AS CD
					ON
					A.KEY = CD.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_CE,SUM(TOTAL_LOANTOTAMT) AS AMT_CE,SUM(TOTAL_LOANICRAMT) AS AMT_CE_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('6') AND
						DOCTYPE = '2'
					)	AS CE
					ON
					A.KEY = CE.KEY
					LEFT OUTER JOIN
					(
						SELECT 'AAA' AS KEY,COUNT(*) AS COUNT_CF,SUM(TOTAL_LOANTOTAMT) AS AMT_CF,SUM(TOTAL_LOANICRAMT) AS AMT_CF_PLUS
						FROM L140SUM 
						WHERE 
						CASELVL IN ('1','2','3','5','A','C') AND
						DOCTYPE = '2'
					)	AS CF
					ON
					A.KEY = CF.KEY FETCH FIRST 9999 ROWS ONLY;
				]]>
			</value>
		</entry>
		
		<entry key="J-106-0077-001">
		    <!--J-106-0077-001 Web e-Loan修改2016國金部第00117號簽報書，原Mega ID USZ0209177修改為USZ0209205。-->
			<value>
				<![CDATA[
					update lms.l120m01a set custId='USZ0209205' where custId='USZ0209177' and MAINID ='1d0f9fc66c2d4e88b6e4c9d19b18a9a7'
				]]>
			</value>
		</entry>
		
		
		<entry key="J-106-0183-001">
		    <!--
              	修改事項：經總行稽核指正，敬請  貴處協助修改已核准之簽報書行業別。
				修改內容：欲修改之額度明細表如附件，借款人統編：19490401AB 0 ，額度序號0C5500700048
				select busCode,ecoNm from lms.c120s01a where mainid = '1d804859edb444ceb5a30d8f9f9c9e8d'
				BUSCODE ECONM 
                130300  國外非金融機構－在台無住所外國人 
            -->
			<value>
				<![CDATA[
					update lms.c120s01a set busCode='060000',ecoNm='私人' where custId='19490401AB' and MAINID in('1d804859edb444ceb5a30d8f9f9c9e8d','684922302cbc44ffa9dc98884dedf840')
				]]>
			</value>
		</entry>
		
		
		<entry key="J-106-0188-001">
		    <!--J-106-0077-001 Web e-Loan修改敝處授信客戶-BATTERSEA PROJECT HOLDING CO LTD(JEZ0000445)E-LOAN額度明細表之INDUSTRY: 016701(不動產開發業)與AS400 SCR: 1100之HO INDUSTRY: 016700一致。-->
			<value>
				<![CDATA[
					UPDATE LMS.L120S01B SET busCode='016700',ecoNm=''  WHERE MAINID = '4740935c08494ece85ba31dca8b2d895' AND CUSTID = 'JEZ0000445';
				]]>
			</value>
		</entry>
		
		<entry key="J-106-0270-001">
		    <!--J-106-0270-001 Web e-Loan調整中鋼集團消貸引入非房貸評等的有效期限參數設定-->
			<value>
				<![CDATA[
					insert into com.bsysparam (OID, PARAM, PARAMVALUE ,PARAMDESC ,LASTMODIFYBY, LASTMODIFYTIME) values (get_oid(), 'LMS_J1060187_S01Q', '2', '中鋼集團消貸引入非房貸評等的有效期限', 'system',current timestamp);
				]]>
			</value>
		</entry>
		
		
		<entry key="J-106-0244-001-locationCd-site3No">
		    <!--J-106-0244-001 Web e-Loan授信管理系統配合地政資訊代碼調整修改額度明細表相關資料
			土建融資訊
			ELF447N
			locationCd     ELF447N_LOCATE_CD CHAR(3)
			site3No        ELF447N_SITE3NO   DECIMAL(4, 0)
			-->
			<value>
				UPDATE lms.l140m01m AS t1
				SET (locationCd,site3No,TFMEMO) = (SELECT NEWAREAID, INTEGER(NEWIR48),'********-01'
				                       FROM 
				                       (
					                       	select mainid,locationCd,site3No,OLDAREAID,OLDIR48,NEWAREAID,NEWIR48 from 
																	cms.tempelandlist 
																	left outer join
																	lms.l140m01m
																	on 
																	locationCd = OLDAREAID and 
																	right('0000' || site3No ,4) = OLDIR48 
																	where MAINID is not null 
				                       ) t2 
				                       WHERE (t1.mainid = t2.mainid  )  )
				WHERE EXISTS (SELECT NEWAREAID, INTEGER(NEWIR48),'********-01'
				              FROM 
				              (
				               	select mainid,locationCd,site3No,OLDAREAID,OLDIR48,NEWAREAID,NEWIR48 from 
												cms.tempelandlist 
												left outer join
												lms.l140m01m
												on 
												locationCd = OLDAREAID and 
												right('0000' || site3No ,4) = OLDIR48 
												where MAINID is not null 
				              ) t2 
				              WHERE (t1.mainid = t2.mainid  ) );
			</value>
		</entry>
		
		<entry key="J-106-0244-001-remainLoanLocationCd-remainLoanSite3No">
		    <!--J-106-0244-001 Web e-Loan授信管理系統配合地政資訊代碼調整修改額度明細表相關資料
			建案完工未出售房屋融資註記(本次)
			ELF517
			remainLoanLocationCd     ELF517_SITE1NO(left(remainLoanLocationCd,1)  CHAR(01)     ELF517_SITE2NO(right(remainLoanLocationCd,2)  DECIMAL(02,0)
			remainLoanSite3No        ELF517_SITE3NO  DECIMAL(04,0)
			-->
			<value>
				UPDATE lms.l140m01m AS t1
				SET (remainLoanLocationCd,remainLoanSite3No,TFMEMO) = (SELECT NEWAREAID, INTEGER(NEWIR48),'********-01'
				                       FROM 
				                       (
					                       	select mainid,remainLoanLocationCd,remainLoanSite3No,OLDAREAID,OLDIR48,NEWAREAID,NEWIR48 from 
																	cms.tempelandlist 
																	left outer join
																	lms.l140m01m
																	on 
																	remainLoanLocationCd = OLDAREAID and 
																	right('0000' || remainLoanSite3No ,4) = OLDIR48 
																	where MAINID is not null 
				                       ) t2 
				                       WHERE (t1.mainid = t2.mainid  ) )
				WHERE EXISTS (SELECT NEWAREAID, INTEGER(NEWIR48),'********-01'
				              FROM 
				              (
				               	select mainid,remainLoanLocationCd,remainLoanSite3No,OLDAREAID,OLDIR48,NEWAREAID,NEWIR48 from 
												cms.tempelandlist 
												left outer join
												lms.l140m01m
												on 
												remainLoanLocationCd = OLDAREAID and 
												right('0000' || remainLoanSite3No ,4) = OLDIR48 
												where MAINID is not null 
				              ) t2 
				              WHERE (t1.mainid = t2.mainid  ) );
			</value>
		</entry>
		
		
		<entry key="J-106-0244-001-bremainLoanLocationCd-bremainLoanSite3No">
		    <!--J-106-0244-001 Web e-Loan授信管理系統配合地政資訊代碼調整修改額度明細表相關資料
			建案完工未出售房屋融資註記(前次)              
			bremainLoanLocationCd
			bremainLoanSite3No    			
			-->
			<value>
				UPDATE lms.l140m01m AS t1
				SET (bremainLoanLocationCd,bremainLoanSite3No,TFMEMO) = (SELECT NEWAREAID, INTEGER(NEWIR48),'********-01'
				                       FROM 
				                       (
					                       	select mainid,bremainLoanLocationCd,bremainLoanSite3No,OLDAREAID,OLDIR48,NEWAREAID,NEWIR48 from 
																	cms.tempelandlist 
																	left outer join
																	lms.l140m01m
																	on 
																	bremainLoanLocationCd = OLDAREAID and 
																	right('0000' || bremainLoanSite3No ,4) = OLDIR48 
																	where MAINID is not null 
				                       ) t2 
				                       WHERE (t1.mainid = t2.mainid  ) )
				WHERE EXISTS (SELECT NEWAREAID, INTEGER(NEWIR48),'********-01'
				              FROM 
				              (
				               	select mainid,bremainLoanLocationCd,bremainLoanSite3No,OLDAREAID,OLDIR48,NEWAREAID,NEWIR48 from 
												cms.tempelandlist 
												left outer join
												lms.l140m01m
												on 
												bremainLoanLocationCd = OLDAREAID and 
												right('0000' || bremainLoanSite3No ,4) = OLDIR48 
												where MAINID is not null 
				              ) t2 
				              WHERE (t1.mainid = t2.mainid  ) );  
			</value>
		</entry>
		
		
		<entry key="J-106-0244-001-areaId-sit3No">
		    <!--J-106-0244-001 Web e-Loan授信管理系統配合地政資訊代碼調整修改額度明細表相關資料
			央行購住/空地/興建房屋統計報表用相關資訊              
			ELF383              
			areaId    ELF383_LOCATION_CD
			sit3No    ELF383_SITE3NO
			-->
			<value>
				UPDATE lms.l140m01m AS t1
				SET (areaId,sit3No,TFMEMO) = (SELECT NEWAREAID, INTEGER(NEWIR48),'********-01'
				                       FROM 
				                       (
					                       	select mainid,areaId,sit3No,OLDAREAID,OLDIR48,NEWAREAID,NEWIR48 from 
																	cms.tempelandlist 
																	left outer join
																	lms.l140m01m
																	on 
																	areaId = OLDAREAID and 
																	right('0000' || sit3No ,4) = OLDIR48 
																	where MAINID is not null 
				                       ) t2 
				                       WHERE (t1.mainid = t2.mainid  ) ) 
				WHERE EXISTS (SELECT NEWAREAID, INTEGER(NEWIR48),'********-01'
				              FROM 
				              (
				               	select mainid,areaId,sit3No,OLDAREAID,OLDIR48,NEWAREAID,NEWIR48 from 
												cms.tempelandlist 
												left outer join
												lms.l140m01m
												on 
												areaId = OLDAREAID and 
												right('0000' || sit3No ,4) = OLDIR48 
												where MAINID is not null 
				              ) t2 
				              WHERE (t1.mainid = t2.mainid  ) );     
			</value>
		</entry>
		
		<entry key="J-106-0244-001-ELF447N_LOCATE_CD">
		    <!--J-106-0244-001 Web e-Loan授信管理系統配合地政資訊代碼調整修改額度明細表相關資料
			土建融預約額度資訊              
			ELF447N
			ELF447N_LOCATE_CD  擔保品座落區  CHAR(3)
			先減50再由ALOAN轉換    
			-->
			<value>
				UPDATE MIS.ELF447N SET
				ELF447N_LOCATE_CD = 
				(case when left(ELF447N_LOCATE_CD,1) = 'B' then 'L' else 'R' end) || right('00'||(cast (substr(ELF447N_LOCATE_CD,2) as decimal(2) ) - 50 ),2)
				where 
				left(ELF447N_LOCATE_CD,1) in ('B','D') and 
				cast (substr(ELF447N_LOCATE_CD,2) as decimal(2) ) > 50    
			</value>
		</entry>
		
		<entry key="J-107-0XXX-001-20180131">
		    <!--
               一.依金管會102.9.9金管銀票字第10200184800號函釋：有條件可取消融資額度為當借款人信用貶落時，銀行有權自動取消之承諾（詳附件）。
	二.本行客戶如OLAM  INTERNATIONAL LIMITED等10戶(詳附件二)目前額度明細檢核附表之約定融資額度註記為不可取消融資額度；惟經查其均屬於有財務承諾之約定融資額度，且依合約約定若信用貶落時，違反財務承諾，可通知借款人立即取消所有額度，且其承諾費非一次性無條件支付，而是有條件之支付(如未動用時才支付等)，故應屬於有條件可取消融資額度。
	三.依金管會102.9.9金管銀票字第10200184800號函釋及新加坡金管會(MAS)之29 December 2017發布之MAS 612之第14頁Appendix C(詳附件三)依「當借款人信用貶落時，銀行有權自動取消之承諾」所適用 0％之信用轉換係數情形，故敬請核准同意修改OLAM  INTERNATIONAL LIMITED等戶之額度明細檢核附表之約定融資額度註記為有條件可取消融資額度，以符合LCR及MAS之規定，謹呈請 鑒核。
            -->
			<value>
				update lms.l140m01a set EXCEPTFLAG='C' where custid in (
					'SGZ0037739',
					'SGZ0043514',
					'IDZ0000472',
					'SGZ0045344',
					'IDZ0001726',
					'IDZ0001471',
					'NLZ0014692',
					'SGZ0047743',
					'KYZ0010963',
					'SGZ0011745'
				) and LEFT(CNTRNO,3) in ('007','025','0B8') and EXCEPTFLAG != 'C'
			</value>
		</entry>
		
		
		<entry key="J-107-0028-001">
		    <!--J-107-0028-001 修改簽報書編號：2016巴黎(兆)授字第00052號，風險歸屬國別為「NL 荷蘭」
			修正授信管理系統：簽報書->額度明細表->文件資訊->「＊本額度風險歸屬國別」欄位,將「本額度風險歸屬國別」欄位值由「AT 奧地利」修改為「NL 荷蘭」
			簽報書編號：2016巴黎(兆)授字第00052號
			系統開發問題支援申請單 編號：2018-0088
			-->
			<value>
				update lms.L140M01A set RISKAREA = 'NL' where cntrno = '0B0501550019' and mainid in ('22878e2772804da89eaf3b2da2e28216','6b9f3d2c2d9642c9a19e3815d9f5b48f')
			</value>
		</entry>
		
		<entry key="M-107-0165-001">
		    <!-- M-107-0165-001 程式修改申請(107)第1044號
			-->
			<value>
				<![CDATA[
				with t0 as (
				select t.*, char(c.cname) as cname, c.buscd,char(lnf040_act_name) as lnf040_act_name from (
				select lnf030_cust_id,  substr(lnf030_cust_id, 1, 10)  as custId,  substr(lnf030_cust_id, 11, 1)  as dupno,  lnf030_loan_no,  substr(lnf030_loan_no, 5, 3)  as lnap , lnf030_contract, lnf030_loan_date, lnf030_loan_bal, lnf030_cancel_date
				, LNF030_USE_TYPE, 	LNF030_LN_PURPOSE
				, lnf020_cancel_date , lnf020_revolve, LNF020_SWFT, lnf020_fact_amt
				, LNF010_JOB_CLASS                             
				from ln.lnf030  left outer join ln.lnf020 on lnf030_contract=lnf020_contract
				left outer join ln.lnf010 on lnf030_cust_id=lnf010_cust_id
				where (lnf030_loan_date between '2017-03-01' and '2018-02-28') and  (LNF020_FACT_TYPE<>'31' and LNF030_CHARC_CODE<>'31') and (lnf030_loan_bal =0)
				) t left outer join mis.custdata c on t.custid=c.custid and t.dupno=c.dupno
				left outer join ln.lnf040 on lnap=LNF040_LNAP_CODE
				where (lnap not in ('205','405', '102','104','202','204') )  and c.buscd in ('060000','130300')
				)
				, D90 as (
				select lnf090_loan_no, max(lnf090_txn_date) as  lnf090_txn_date_d, sum(lnf090_txn_amt) as lnf090_txn_amt_d from ln.lnf090 where lnf090_status='0' and lnf090_dr_cr='D' and lnf090_loan_no in (select lnf030_loan_no from t0)
				group by lnf090_loan_no
				)
				, C90A as (
				select lnf090_loan_no, min(lnf090_txn_date) as  lnf090_txn_date_c from ln.lnf090 where lnf090_status='0' and lnf090_dr_cr='C' and lnf090_loan_no in (select lnf030_loan_no from t0) and lnf090_loan_bal=0
				group by lnf090_loan_no
				)
				, C90 as (
				select C90A.lnf090_loan_no,  lnf090_txn_date_c, sum(lnf090_txn_amt) as lnf090_txn_amt_c from C90A left outer join ln.lnf090 t on C90A.lnf090_loan_no=t.lnf090_loan_no and lnf090_txn_date_c=lnf090_txn_date and lnf090_status='0' and lnf090_dr_cr='C' and lnf090_loan_bal=0
				where C90A.lnf090_loan_no in (select lnf030_loan_no from t0)
				group by C90A.lnf090_loan_no,  lnf090_txn_date_c
				)
				, tmp as (select
				substr(LNF030_CONTRACT, 1, 3) as brNo
				,LNF030_CUST_ID
				,CUSTID
				,DUPNO
				,CNAME
				,LNF010_JOB_CLASS
				,LNF030_CONTRACT
				,LNF030_LOAN_NO
				,LNAP
				,LNF040_ACT_NAME
				,LNF030_USE_TYPE
				,LNF030_LN_PURPOSE
				,LNF030_LOAN_DATE as final_loan_date
				, (case when lnf020_revolve='Y' then lnf020_fact_amt else lnf090_txn_amt_d end ) as final_loan_amt 
				, value((case when lnf020_revolve='Y' then lnf030_cancel_date else lnf090_txn_date_c end ), lnf090_txn_date_c) as final_clear_date
				, lnf090_txn_amt_c as final_clear_amt
				,LNF030_LOAN_DATE
				,LNF030_LOAN_BAL
				,LNF030_CANCEL_DATE
				,LNF020_CANCEL_DATE
				,LNF020_REVOLVE
				,LNF020_SWFT
				,LNF020_FACT_AMT
				, lnf090_txn_amt_d, lnf090_txn_date_c, lnf090_txn_amt_c
				from t0 
				left outer join D90 on lnf030_loan_no=D90.lnf090_loan_no
				left outer join C90 on lnf030_loan_no=C90.lnf090_loan_no
				)
				select * from tmp where ((final_clear_date is null and days(lnf090_txn_date_c)-days(final_loan_date) <=31 ) or (final_clear_date is not null and (days(final_clear_date) - days(final_loan_date) <=31)))					
				]]> 
			</value>
		</entry>
		
		<entry key="J-107-0078">
		    <!--Web e-Loan個金授信管理系統修改消金戶-簽約前模擬動審檢核表。-->
			<value>
				<![CDATA[
					update LMS.L250S01B set SUBTITLE='非銀行法12條之1連帶保證人宣告書' where TYPE=1 and GROUP=17 and SUBITEM=3;
					update LMS.L250S01B set SUBORDER=3 where TYPE=1 and GROUP=17 and SUBITEM=3;
					update LMS.L250S01B set SUBORDER=4 where TYPE=1 and GROUP=17 and SUBITEM=2;
					insert into LMS.L250S01B (OID,TYPE,GROUP,GROUPORDER,SUBITEM,SUBORDER,SUBTITLE) values (GET_OID(),1, 17, 17, 4, 2, '非銀行法12條之1一般保證人宣告書');
					
					update LMS.L250S01B set GROUPORDER=23 where TYPE=1 and GROUP=22;
					update LMS.L250S01B set GROUPORDER=24 where TYPE=1 and GROUP=23;
					insert into LMS.L250S01B (OID,TYPE,GROUP,GROUPORDER,SUBITEM,SUBORDER,SUBTITLE) values (GET_OID(),1, 24, 22, 1, 1, '一般保證人出具同意保證期限逾15年之同意書');
					insert into LMS.L250S01B (OID,TYPE,GROUP,GROUPORDER,SUBITEM,SUBORDER,SUBTITLE) values (GET_OID(),1, 24, 22, 2, 2, '不適用');
					insert into LMS.L250S01B (OID,TYPE,GROUP,GROUPORDER,SUBITEM,SUBORDER,SUBTITLE) values (GET_OID(),1, 24, 22, 3, 3, '無保證人');
					
					update LMS.L250S01B set SUBTITLE='主動提供擔任一般保證人同意書或申請書' where TYPE=1 and GROUP=19 and SUBITEM=1;
				]]>
			</value>
		</entry>
		
		<!--J-107-0126_05097_B1001 Web e-Loan 國內授信動用審核表刪除第12項恐怖分子黑名單的查詢紀錄欄位。-->
		<entry key="J-107-0126-001">
		    <!--Web e-Loan 國內授信動用審核表刪除第12項恐怖分子黑名單的查詢紀錄欄位。-->
			<value>
				 DELETE FROM LMS.L901M01A WHERE  BRANCHID = '918' AND  ITEMTYPE = '3' AND ITEMSEQ = '12'
			</value>
		</entry>
 
 		<entry key="J-107-0141">
		    <!--Web e-Loan個金 配合泰子行因應泰央行查核需要，產出2009年度-2017年度新舊信用評等等資料。-->
			<value>
				<![CDATA[
				select caseBrId as 分行別, custid as 客戶統編, dupno as 客戶重複碼, custname as 客戶名稱, ratingDate as 評等日期, grade as 評等, caseNo as 簽報書案號 from (
				    select caseBrId, custid, dupno, custname, caseDate as ratingDate, o_grade as grade, caseNo from (
					select m.*, s.custid, s.dupno, ma.custname, s.o_grade 
					from (select mainId as case_mainId, caseBrId, caseDate, caseNo from lms.l120m01a where ownbrId like 'Y%' and docstatus='05O' and docType='2' and ratingFlag is null) m 
					left outer join lms.c120s01a s on case_mainId=s.mainId
					 left outer join lms.c120m01a ma on s.mainId=ma.mainId and s.custid=ma.custId  and s.dupno=ma.dupno
					where s.o_grade > ' '
					)
					union all
					select distinct caseBrId, custid, dupno, custname,  ratingDate, fRating as grade, caseNo from (
					select m.*, c120.custid, c120.dupno, c120.custname, c120.mainId as ratingId, g.ratingId as ratingid_raw, g.ratingDate,g.fRating 
					from (select mainId as case_mainId, caseBrId, caseDate, caseNo from lms.l120m01a where ownbrId like 'Y%' and docstatus='05O' and docType='2' and ratingFlag>' ') m 
					left outer join lms.c120m01a c120
					on case_mainId=c120.caseId
					left outer join lms.c121m01d g
					on c120.mainId=g.mainId and c120.custid=g.custId and c120.dupno=g.dupno
					where g.oid > ' '
					) 
				) tmp

				]]>
			</value>
		</entry>
		
		<entry key="J-107-0166">
		    <!--Web e-Loan 產出住商公司引介明細-->
			<value>
				<![CDATA[
				select m.*, s.custname from lms.c900s02a m 
				left outer join lms.l140m01a s 
				on m.cntrno=s.cntrno and m.custid=s.custid and m.dupno=s.dupno 
				where companyid5='55555' and s.property='1' and s.docstatus not like '06%' 
				order by brno, txnDate
				]]>
			</value>
		</entry>
		
		<entry key="J-107-0166">
		    <!--Web e-Loan 產出住商公司引介明細-->
			<value>
				<![CDATA[
				select LNF030_CONTRACT,LNF030_LOAN_CLASS ,LNF030_LOAN_NO, lnf033_rate_plan, 
				substr(LNF030_CUST_ID, 1,10) as custId,	substr(LNF030_CUST_ID, 11,1) as dupno,cname  
				from ln.lnf030  
				left outer join ln.lnf033 on lnf030_loan_no=lnf033_loan_no 
				left outer join mis.custdata on substr(LNF030_CUST_ID, 1,10)=custId and substr(LNF030_CUST_ID, 11,1)=dupno
				where lnf030_loan_no in (?)
				]]>
			</value>
		</entry>
		


		
		<entry key="J-107-0152">
		    <!--J-107-0152_05097_B1001 產生金控總部分行是否有逾期尚未辦理覆審之名單。-->
			<value>
				<![CDATA[
				    EXPORT TO result.csv OF DEL MODIFIED BY NOCHARDEL codepage=950 
				 	WITH LISTTBL AS(
					     SELECT ELF412_BRANCH,ELF412_CUSTID,ELF412_DUPNO,(LEFT(ELF412_CUSTID || '        ',10) || ELF412_DUPNO) AS ELF412_FCUSTID,
					     ELF412_CRDTTBL,ELF412_MOWTYPE,ELF412_MOWTBL1,ELF412_LRDATE,ELF412_MAINCUST,ELF412_NEWADD,ELF412_NEWDATE
					     FROM MIS.ELF412 WHERE
					     ELF412_BRANCH = '201' AND ELF412_NCKDFLAG IN ('','0','8') AND
					     (
					          (ELF412_CRDTTBL IN ('D','E')) OR
					          (ELF412_MOWTYPE IN ('1','H','Q') AND ELF412_MOWTBL1 IN ('9','10','11','12','13','DF') )  OR
					          (ELF412_MOWTYPE IN ('5','F','O','j','k') AND ELF412_MOWTBL1 IN ('4') )   OR
					          (ELF412_MOWTYPE NOT IN ('1','H','Q','5','F','O','j','k') AND ELF412_MOWTBL1 IN ('10','11','12','13','DF') )
					     )
					),
					MAIN022 AS (
					   SELECT LNF022_CUST_ID,VALUE(SUM(LNF022_ADJ_FAMT_T),0) AS M_ADJ_FAMT_T        
					   FROM
					   LISTTBL LEFT OUTER JOIN LN.LNF022                             
					   ON
					   ELF412_FCUSTID = LNF022_CUST_ID  AND
					   (LNF022_BR_NO = '201' OR  LEFT(LNF022_CONTRACT,3) = '201' )
					   GROUP BY LNF022_CUST_ID
					)
					,
					REL022 AS (
					   SELECT MAIN_FCUSTID,VALUE(SUM(LNF022_ADJ_FAMT_T),0) AS R_ADJ_FAMT_T        
					   FROM
					   (
					         SELECT DISTINCT
					         CUSTID,DUPNO,(LEFT(CUSTID || '        ',10) || DUPNO) AS MAIN_FCUSTID,
					         RCUSTID,DUPNO1,(LEFT(RCUSTID || '        ',10) || DUPNO1) AS ELCRECOM_FCUSTID         
					         FROM
					         LISTTBL
					         LEFT OUTER JOIN
					         MIS.ELCRECOM                          
					         ON  CUSTID = ELF412_CUSTID                
					         AND  DUPNO = ELF412_DUPNO                
					         WHERE
					         CUSTID <> RCUSTID AND
					         (R01 = 'Y' OR  R02 = 'Y')
					   ) AS T1                           
					   LEFT OUTER JOIN LN.LNF022                             
					   ON
					   ELCRECOM_FCUSTID = LNF022_CUST_ID  AND
					   (LNF022_BR_NO = '201' OR  LEFT(LNF022_CONTRACT,3) = '201' )
					   GROUP BY MAIN_FCUSTID
					),
					TOTALTBL AS (
					  SELECT ELF412_BRANCH,ELF412_CUSTID,ELF412_DUPNO,ELF412_CRDTTBL,ELF412_MOWTYPE,ELF412_MOWTBL1,ELF412_LRDATE,ELF412_MAINCUST,ELF412_NEWADD,ELF412_NEWDATE,
					  M_ADJ_FAMT_T,R_ADJ_FAMT_T,(M_ADJ_FAMT_T+R_ADJ_FAMT_T) AS TOTALAMT
					  FROM
					  LISTTBL
					  LEFT OUTER JOIN
					  MAIN022
					  ON
					  LISTTBL.ELF412_FCUSTID = MAIN022.LNF022_CUST_ID
					  LEFT OUTER JOIN
					  REL022
					  ON
					  LISTTBL.ELF412_FCUSTID = REL022.MAIN_FCUSTID
					
					)
					SELECT ELF412_CUSTID,ELF412_DUPNO,CNAME,ELF412_MOWTYPE,ELF412_MOWTBL1,ELF412_MAINCUST,
					ELF412_LRDATE,(CASE WHEN ELF412_LRDATE IS NULL OR ELF412_LRDATE = '0001-01-01' THEN '0001-01-01' ELSE ELF412_LRDATE + 6 MONTH END ) AS DUELRDATE,
					M_ADJ_FAMT_T,R_ADJ_FAMT_T,TOTALAMT,
					ELF412_NEWADD,ELF412_NEWDATE
					FROM TOTALTBL
					LEFT OUTER JOIN
					MIS.CUSTDATA
					ON
					ELF412_CUSTID = CUSTID AND
					ELF412_DUPNO = DUPNO
					WHERE TOTALTBL.TOTALAMT >= 100000000
					ORDER BY ELF412_BRANCH,ELF412_CUSTID,ELF412_DUPNO ASC FETCH FIRST 1000 ROWS ONLY
				]]>
			</value>
		</entry>
		
		
		<entry key="J-107-0169">
		    <!--J-107-0169_05097_B1001 產生國內、外各分行及子行截至107年6月28日止尚未完成屬董事會（或常董會）權限核定之企金戶實地覆審名單，內容包括分行代號與名稱、應實地覆審借戶名稱與ID及最遲應覆審日。-->
			<value>
				<![CDATA[
				    EXPORT TO result.csv OF DEL MODIFIED BY NOCHARDEL codepage=950 
				 	select elf412b_branch,elf412b_custid,elf412b_dupNo,ELF412B_NEWADD ,ELF412B_NEWDATE,trim(cname ) as cname,
					(case ELF412B_NEWDATE  when '010606' then  '2018-06-30'  else '2018-07-31'  end) lastDate,ELF411_FLAG
					from
					(
					select elf412b_branch,elf412b_custid,elf412b_dupNo,ELF412B_NEWADD ,ELF412B_NEWDATE from mis.elf412b where ELF412B_LRDATE = '0001-01-01' and ELF412B_NEWADD = 'N' and ELF412B_NEWDATE <= '010607'  and ELF412B_NCKDFLAG  in ('','0','8') 
					) as A
					left outer join
					( select distinct ELF411_BRNO,ELF411_CUSTID,ELF411_DUPNO,'Y' AS ELF411_FLAG from mis.elf411 where ELF411_DATAYM in ('010607') ) AS B
					ON
					ELF411_BRNO =elf412b_branch and
					ELF411_CUSTID = elf412b_custid and
					ELF411_DUPNO = elf412b_dupNo
					left outer join
					mis.custdata
					on
					custid = elf412b_custid and
					dupno = elf412b_dupNo
					where ELF411_FLAG is null
					order by ELF412B_BRANCH ASC,lastDate ASC,elf412b_custid  ASC Fetch First 1000 rows only
				]]>
			</value>
		</entry>
		
		
		<entry key="J-107-0188-1">
		    <!--J-107-0188_05097_B1001 產生e-Loan授信簽案一次性統計資料。-->
			<!--1.桃竹苗區營運中心轄下14家分行-->
			<value>
                    EXPORT TO result_1.csv OF DEL MODIFIED BY NOCHARDEL codepage=950 
				    WITH SOURCEBR AS (
					   SELECT BRNO,TRIM(BRNAME) AS BRNAME FROM COM.BELSBRN WHERE brnGroup = '933'
					),
					KIND1 AS(
					   SELECT BRNO,BRNAME,COUNT(*) AS TOT_1
					   FROM SOURCEBR
					   LEFT OUTER JOIN
					   LMS.L120M01A
					   ON
					   SOURCEBR.BRNO = LMS.L120M01A.CASEBRID
					   WHERE
					   DOCSTATUS = '05O' AND DELETEDTIME IS NULL AND
					   ENDDATE BETWEEN '2017-07-01' AND '2018-06-30' AND
					   DOCCODE != 4 AND
					   caseLvl IN ('1','2','3','4','5','6','7','A','C')
					   GROUP BY BRNO,BRNAME
					),
					KIND2 AS(
					   SELECT BRNO,BRNAME,COUNT(*) AS TOT_2
					   FROM SOURCEBR
					   LEFT OUTER JOIN
					   LMS.L120M01A
					   ON
					   SOURCEBR.BRNO = LMS.L120M01A.CASEBRID
					   WHERE
					   DOCSTATUS = '05O' AND DELETEDTIME IS NULL AND
					   ENDDATE BETWEEN '2017-07-01' AND '2018-06-30' AND
					   DOCCODE = 4
					   GROUP BY BRNO,BRNAME
					),
					KIND3 AS(
					   SELECT BRNO,BRNAME,COUNT(*) AS TOT_3
					   FROM SOURCEBR
					   LEFT OUTER JOIN
					   LMS.L120M01A
					   ON
					   SOURCEBR.BRNO = LMS.L120M01A.CASEBRID
					   WHERE
					   DOCSTATUS = '05O' AND DELETEDTIME IS NULL AND
					   ENDDATE BETWEEN '2017-07-01' AND '2018-06-30' AND
					   DOCCODE != 4 AND
					   caseLvl IN ('1','2','3','5','A','C')
					   GROUP BY BRNO,BRNAME
					)
					SELECT SOURCEBR.BRNO AS 分行代號,SOURCEBR.BRNAME AS 分行名稱,COALESCE(TOT_1,0) AS 副總,COALESCE(TOT_2,0) AS 異常,COALESCE(TOT_3,0) AS 常董,(COALESCE(TOT_1,0) + COALESCE(TOT_2,0) + COALESCE(TOT_3,0) ) AS 合計 FROM SOURCEBR
					LEFT OUTER JOIN
					KIND1
					ON
					SOURCEBR.BRNO = KIND1.BRNO
					LEFT OUTER JOIN
					KIND2
					ON
					SOURCEBR.BRNO = KIND2.BRNO
					LEFT OUTER JOIN
					KIND3
					ON
					SOURCEBR.BRNO = KIND3.BRNO
					ORDER BY SOURCEBR.BRNO ASC
			</value>
		</entry>
		
		
		<entry key="J-107-0188-2">
		    <!--J-107-0188_05097_B1001 產生e-Loan授信簽案一次性統計資料。-->
			<!--2.北一區永和(034)、板橋(206)等2家分行-->
			<value>
				    EXPORT TO result_2.csv OF DEL MODIFIED BY NOCHARDEL codepage=950 
				    WITH SOURCEBR AS (
					   SELECT BRNO,TRIM(BRNAME) AS BRNAME FROM COM.BELSBRN WHERE BRNO IN ('034','206')
					),
					KIND1 AS(
					   SELECT BRNO,BRNAME,COUNT(*) AS TOT_1
					   FROM SOURCEBR
					   LEFT OUTER JOIN
					   LMS.L120M01A
					   ON
					   SOURCEBR.BRNO = LMS.L120M01A.CASEBRID
					   WHERE
					   DOCSTATUS = '05O' AND DELETEDTIME IS NULL AND
					   ENDDATE BETWEEN '2017-07-01' AND '2018-06-30' AND
					   DOCCODE != 4 AND
					   caseLvl IN ('1','2','3','4','5','6','7','A','C')
					   GROUP BY BRNO,BRNAME
					),
					KIND2 AS(
					   SELECT BRNO,BRNAME,COUNT(*) AS TOT_2
					   FROM SOURCEBR
					   LEFT OUTER JOIN
					   LMS.L120M01A
					   ON
					   SOURCEBR.BRNO = LMS.L120M01A.CASEBRID
					   WHERE
					   DOCSTATUS = '05O' AND DELETEDTIME IS NULL AND
					   ENDDATE BETWEEN '2017-07-01' AND '2018-06-30' AND
					   DOCCODE = 4
					   GROUP BY BRNO,BRNAME
					),
					KIND3 AS(
					   SELECT BRNO,BRNAME,COUNT(*) AS TOT_3
					   FROM SOURCEBR
					   LEFT OUTER JOIN
					   LMS.L120M01A
					   ON
					   SOURCEBR.BRNO = LMS.L120M01A.CASEBRID
					   WHERE
					   DOCSTATUS = '05O' AND DELETEDTIME IS NULL AND
					   ENDDATE BETWEEN '2017-07-01' AND '2018-06-30' AND
					   DOCCODE != 4 AND
					   caseLvl IN ('1','2','3','5','A','C')
					   GROUP BY BRNO,BRNAME
					)
					SELECT SOURCEBR.BRNO AS 分行代號,SOURCEBR.BRNAME AS 分行名稱,COALESCE(TOT_1,0) AS 副總,COALESCE(TOT_2,0) AS 異常,COALESCE(TOT_3,0) AS 常董,(COALESCE(TOT_1,0) + COALESCE(TOT_2,0) + COALESCE(TOT_3,0) ) AS 合計 FROM SOURCEBR
					LEFT OUTER JOIN
					KIND1
					ON
					SOURCEBR.BRNO = KIND1.BRNO
					LEFT OUTER JOIN
					KIND2
					ON
					SOURCEBR.BRNO = KIND2.BRNO
					LEFT OUTER JOIN
					KIND3
					ON
					SOURCEBR.BRNO = KIND3.BRNO
					ORDER BY SOURCEBR.BRNO ASC

			</value>
		</entry>
		
		<entry key="J-107-0188-3">
		    <!--J-107-0188_05097_B1001 產生e-Loan授信簽案一次性統計資料。-->
			<!--3.中區營運中心轄下21家分行-->
			<value>
                    EXPORT TO result_3.csv OF DEL MODIFIED BY NOCHARDEL codepage=950 
				    WITH SOURCEBR AS (
					   SELECT BRNO,TRIM(BRNAME) AS BRNAME FROM COM.BELSBRN WHERE brnGroup = '934'
					),
					KIND1 AS(
					   SELECT BRNO,BRNAME,COUNT(*) AS TOT_1
					   FROM SOURCEBR
					   LEFT OUTER JOIN
					   LMS.L120M01A
					   ON
					   SOURCEBR.BRNO = LMS.L120M01A.CASEBRID
					   WHERE
					   DOCSTATUS = '05O' AND DELETEDTIME IS NULL AND
					   ENDDATE BETWEEN '2017-07-01' AND '2018-06-30' AND
					   DOCCODE != 4 AND
					   caseLvl IN ('1','2','3','4','5','6','7','A','C')
					   GROUP BY BRNO,BRNAME
					),
					KIND2 AS(
					   SELECT BRNO,BRNAME,COUNT(*) AS TOT_2
					   FROM SOURCEBR
					   LEFT OUTER JOIN
					   LMS.L120M01A
					   ON
					   SOURCEBR.BRNO = LMS.L120M01A.CASEBRID
					   WHERE
					   DOCSTATUS = '05O' AND DELETEDTIME IS NULL AND
					   ENDDATE BETWEEN '2017-07-01' AND '2018-06-30' AND
					   DOCCODE = 4
					   GROUP BY BRNO,BRNAME
					),
					KIND3 AS(
					   SELECT BRNO,BRNAME,COUNT(*) AS TOT_3
					   FROM SOURCEBR
					   LEFT OUTER JOIN
					   LMS.L120M01A
					   ON
					   SOURCEBR.BRNO = LMS.L120M01A.CASEBRID
					   WHERE
					   DOCSTATUS = '05O' AND DELETEDTIME IS NULL AND
					   ENDDATE BETWEEN '2017-07-01' AND '2018-06-30' AND
					   DOCCODE != 4 AND
					   caseLvl IN ('1','2','3','5','A','C')
					   GROUP BY BRNO,BRNAME
					)
					SELECT SOURCEBR.BRNO AS 分行代號,SOURCEBR.BRNAME AS 分行名稱,COALESCE(TOT_1,0) AS 副總,COALESCE(TOT_2,0) AS 異常,COALESCE(TOT_3,0) AS 常董,(COALESCE(TOT_1,0) + COALESCE(TOT_2,0) + COALESCE(TOT_3,0) ) AS 合計 FROM SOURCEBR
					LEFT OUTER JOIN
					KIND1
					ON
					SOURCEBR.BRNO = KIND1.BRNO
					LEFT OUTER JOIN
					KIND2
					ON
					SOURCEBR.BRNO = KIND2.BRNO
					LEFT OUTER JOIN
					KIND3
					ON
					SOURCEBR.BRNO = KIND3.BRNO
					ORDER BY SOURCEBR.BRNO ASC
			</value>
		</entry>
		
		
		<!--
		          連線作業改善建議表_D-2018-0021
		    HelpDesk服務單/IT訊息通知單編號：20181025F000047
			填報人員：(07153)張琬雅
			填表日期 ：2018/10/26 09:12
			編號：D-2018-0021
			通報表別：	連線作業改善建議表  
			問題發生時間：	2018/10/25 12:11
			問題結束時間：	 
			問題發生地點：	大里分行
			問題摘要：	1.大里分行反映科目代號813之E-LOAN授信系統顯示為「預付款保證」,與A-LOAN為「工程預付款保證」不一致問題。
			2.719,819科目為「一般預付款」，713,813科目為「工程預付款」，為提供正確的資訊，請修改正確科目名稱。
			
			系支單編號：編號：2018-0868
		-->
		<entry key="D-2018-0021">
			<value>
                    update com.bcodetype set CODEDESC = 'Project advance payment guarantee' where codetype = 'lms1405m01_SubItem'  and codevalue = '713' and LOCALE = 'en';
					update com.bcodetype set CODEDESC = '工程预付款保证' where codetype = 'lms1405m01_SubItem'  and codevalue = '713' and LOCALE = 'zh_CN';
					update com.bcodetype set CODEDESC = '工程預付款保證' where codetype = 'lms1405m01_SubItem'  and codevalue = '713' and LOCALE = 'zh_TW';
					
					update com.bcodetype set CODEDESC = 'Secured project advance payment guarantee' where codetype = 'lms1405m01_SubItem'  and codevalue = '813' and LOCALE = 'en';
					update com.bcodetype set CODEDESC = '工程预付款担保保证' where codetype = 'lms1405m01_SubItem'  and codevalue = '813' and LOCALE = 'zh_CN';
					update com.bcodetype set CODEDESC = '工程預付款擔保保證' where codetype = 'lms1405m01_SubItem'  and codevalue = '813' and LOCALE = 'zh_TW';
			</value>
		</entry>
		
		
		<entry key="J-107-0372">
		    <!--個金授信管理系統修改「歡喜樂活」查核事項。-->
			<value>
			insert into lms.c900s01a(OID,CHECKCODE,CHECKSEQ,CHECKTYPE,CHECKCONTENT,PRODTYPE,CHECKRMK,CREATOR,CREATETIME,UPDATER,UPDATETIME) values (get_oid(), '162', 147, '2', '為具完全行為能力之中華民國國民，年齡滿60歲以上，加計貸款年限須超過90歲'                                ,'','如否，應專案陳報總處核定','sys',current timestamp,'sys',current timestamp)
			insert into lms.c900s01a(OID,CHECKCODE,CHECKSEQ,CHECKTYPE,CHECKCONTENT,PRODTYPE,CHECKRMK,CREATOR,CREATETIME,UPDATER,UPDATETIME) values (get_oid(), '163', 148, '2', '借款人是否有聯徵負面資訊、使用現金卡或信用卡循環信用'                                             ,'','如是，依本行相關規定辦理或專案陳報總處核定','sys',current timestamp,'sys',current timestamp)
			
			UPDATE LMS.C900S01A SET CHECKSEQ='70' where CHECKCODE='149';
			UPDATE LMS.C900S01A SET CHECKSEQ='71' where CHECKCODE='159';
			UPDATE LMS.C900S01A SET CHECKSEQ='76' where CHECKCODE='78';
			UPDATE LMS.C900S01A SET CHECKSEQ='76' where CHECKCODE='146';
			UPDATE LMS.C900S01A SET CHECKSEQ='78' where CHECKCODE='76';
			
			UPDATE LMS.C900M01C SET SOUSECODE='162' where PRODKIND='67' AND TYPE='1' AND SOUSECODE='157';
			
			insert into lms.c900m01c(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME ) values(get_oid(), '67', '13500100', '1', '163', 'sys',current timestamp, 'sys',current timestamp);
			insert into lms.c900m01c(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME ) values(get_oid(), '67', '14501000', '1', '163', 'sys',current timestamp, 'sys',current timestamp);
			</value>
		</entry> 		
		
		<entry key="J-107-0391">
		    <!--消金動審表增加「不動產使用狀況暨擔保借款抵押權設定種類聲明書」-->
			<value>
			INSERT INTO LMS.C900S01B (OID,ITEMCODE,ITEMSEQ,ITEMTYPE,ITEMCONTENT,ITEMFORMAT,CREATOR,CREATETIME,UPDATER,UPDATETIME) VALUES (get_oid(),'43',43,'1','不動產使用狀況暨擔保借款抵押權設定種類聲明書','','system', current timestamp,'system', current timestamp)
			</value>
		</entry> 		
		<entry key="J-107-0393">
		    <!--配合「簡化消金契據及對保作業」修改「簽約前模擬動審檢核表」-->
			<value>
			UPDATE LMS.L250S01B SET  SUBTITLE ='擔保借款抵押權設定種類約款(購屋專用版本)﹙適用採舊版契約訂約者﹚' where TYPE='1' AND GROUP='9'  AND SUBITEM='1' ;
			UPDATE LMS.L250S01B SET  SUBTITLE ='擔保借款抵押權設定種類約款(非購屋專用版本)﹙適用採舊版契約訂約者﹚' where TYPE='2' AND GROUP='11'  AND SUBITEM='1' ;
			UPDATE LMS.L250S01B SET  SUBTITLE ='租賃無租賃等切結同意書﹙適用採舊版契約訂約者﹚' where TYPE='1' AND GROUP='13'  AND SUBITEM='1' ;
			UPDATE LMS.L250S01B SET  SUBTITLE ='租賃無租賃等切結同意書﹙適用採舊版契約訂約者﹚' where TYPE='2' AND GROUP='13'  AND SUBITEM='1' ;
			insert into LMS.L250S01B (OID, TYPE, GROUP, GROUPORDER, SUBITEM, SUBORDER, SUBTITLE) values (get_oid(), 1, 29, 29, 1, 1, '不動產使用狀況暨擔保借款抵押權設定種類聲明書﹙適用採新版(108.01版)契約訂約者﹚');
			insert into LMS.L250S01B (OID, TYPE, GROUP, GROUPORDER, SUBITEM, SUBORDER, SUBTITLE) values (get_oid(), 2, 29, 29, 1, 1, '不動產使用狀況暨擔保借款抵押權設定種類聲明書﹙適用採新版(108.01版)契約訂約者﹚');
			</value>
		</entry> 		
		
		<entry key="J-107-0379">
		    <!--消金動審表增列 簽案或動審時是否至少擇一填寫「銀行公會疑似洗錢或資恐交易態樣檢核表-授信」-->
			<value>
			INSERT INTO LMS.C900S01B (OID,ITEMCODE,ITEMSEQ, ITEMTYPE, ITEMCONTENT,ITEMFORMAT,CREATOR,CREATETIME,UPDATER, UPDATETIME) VALUES (GET_OID(),'44',44,'1','簽案或動審時是否至少擇一填寫「銀行公會疑似洗錢或資恐交易態樣檢核表-授信」','', 'system',current timestamp, 'system',current timestamp)
			</value>
		</entry> 
		
		
		<entry key="J-108-0023_05097_B1001">
		    <!--經查南京東路分行授信戶 OPHIRA FINANCE LTD. 與 LOFTY SUCCESS GROUP LIMITED 係屬聯行攤貸案非擔任管理行， 請直接修改為不覆審。-->
			<value>
			update mis.elf412 set ELF412_NCKDFLAG='1',ELF412_NCKDDATE = '2018-12-03',ELF412_NCKDMEMO='香港行擔任管理行' where elf412_custid in ('VGZ0084795','WSZ0032615') and elf412_branch = '070'
			</value>
		</entry> 
		
		<entry key="J-108-0052_09301_B1001">
		    <!--Web e-Loan授信管理系統，企金授信之模擬動審表和動用審核表字句更新-->
			<value>
			update LMS.L250S02A set SUB1TITLE = '實質受益人暨無記名股票聲明書' where GROUP=5 and SUB1ITEM=5
			</value>
		</entry>
		
		<entry key="J-108-0077_08034_B1001">
		    <!--Web e-Loan配合授審處為防杜人頭戶及代辦案件,於E-LOAN系統之查核內容，增列檢核項目等修改-->
			<value>
			INSERT INTO LMS.C900S01A (OID,CHECKCODE,CHECKSEQ,CHECKTYPE,CHECKCONTENT,PRODTYPE,CHECKRMK,CREATOR,CREATETIME,UPDATER,UPDATETIME) VALUES (get_oid(),'178', 178,'3','借戶短期間內於聯徵中心被查詢紀錄密集，是否有照會借戶並確認其合理性 ','','應於簽報書提出合理說明','sys',current timestamp,'sys',current timestamp);
			INSERT INTO LMS.C900S01A (OID,CHECKCODE,CHECKSEQ,CHECKTYPE,CHECKCONTENT,PRODTYPE,CHECKRMK,CREATOR,CREATETIME,UPDATER,UPDATETIME) VALUES (get_oid(),'179', 179,'3','借戶居住地、工作地與案件來源是否有地緣關係','','若否，應於簽報書提出合理說明','sys',current timestamp,'sys',current timestamp);
			INSERT INTO LMS.C900S01A (OID,CHECKCODE,CHECKSEQ,CHECKTYPE,CHECKCONTENT,PRODTYPE,CHECKRMK,CREATOR,CREATETIME,UPDATER,UPDATETIME) VALUES (get_oid(),'180', 180,'3','借戶提供資料是否與聯徵中心及本行資料庫交叉比對其真實性? (例如借戶檢附之工作、聯絡資料與聯徵中心揭露或與本行內部留存借戶以往資料是否相符) ','','若否，應於簽報書提出合理說明','sys',current timestamp,'sys',current timestamp);
			INSERT INTO LMS.C900S01A (OID,CHECKCODE,CHECKSEQ,CHECKTYPE,CHECKCONTENT,PRODTYPE,CHECKRMK,CREATOR,CREATETIME,UPDATER,UPDATETIME) VALUES (get_oid(),'181', 181,'3','借戶是否職業變動頻繁或任職之工作為期短暫','','如是，應查證並確認有無代辦業者捏造之情形','sys',current timestamp,'sys',current timestamp);

			insert into lms.c900m01c select get_oid(), prodkind, subjcode , '1', '178'  ,'sys',current timestamp,'sys',current timestamp from lms.c900m01b where prodKind!='ZZ' and subjcode!='';
			insert into lms.c900m01c select get_oid(), prodkind, subjcode , '1', '179'  ,'sys',current timestamp,'sys',current timestamp from lms.c900m01b where prodKind!='ZZ' and subjcode!='';
			insert into lms.c900m01c select get_oid(), prodkind, subjcode , '1', '180'  ,'sys',current timestamp,'sys',current timestamp from lms.c900m01b where prodKind!='ZZ' and subjcode!='';
			insert into lms.c900m01c select get_oid(), prodkind, subjcode , '1', '181'  ,'sys',current timestamp,'sys',current timestamp from lms.c900m01b where prodKind!='ZZ' and subjcode!='';
			</value>
		</entry>
		
		<entry key="J-108-0186_08034_B1001">
		    <!--Web e-Loan依授審處要求，提供107.6.1-108.6.30期間本行消金案件簽辦處理情形報表-->
			<value>
	        <![CDATA[
			with t0 as (			
				select tmp.* from (
				select L120.*
				, L140.mainId as tabMainId, L140.custId, L140.dupNo, L140.custName, C120S01A.busCode, L140.cntrNo, L140.commSno, L140.snoKind, L140.reUse, L140.currentApplyCurr,L140.currentApplyAmt,  L140.LoanTotCurr, L140.LoanTotAmt
				, S02A.prodKind, S02A.subjCode, S02A.modelKind, S02A.seq as S02A_SEQ, 
				S02A.property, 
				(case 
				when S02A.property='1' then '新做'
				when S02A.property='2' then '續約'
				when S02A.property='3' then '變更條件'
				when S02A.property='4' then '流用'
				when S02A.property='5' then '增額'
				when S02A.property='6' then '減額'
				when S02A.property='7' then '不變'
				when S02A.property='8' then '取消'
				when S02A.property='9' then '展期'
				when S02A.property='10' then '紓困'
				when S02A.property='11' then '提前續約'
				when S02A.property='12' then '協議清償'
				when S02A.property='13' then '報價'
				else S02A.property end
				) AS PropertyDesc, S02A.loanAmt as S02A_loanAmt  from 
				( 
				select l120m01a.oid, mainId, caseBrId, BRNAME,BRNGROUP, 
					custId as case_custId, dupno as case_dupNo,custName as case_custName, 
					caseNo, docStatus, caseDate, date(sendFirstTime) as sendFirstTime, date(sendLastTime) as sendLastTime, endDate,docKind, 
					(case when authLvl in ('1','2') then '1_InBr' when authLvl='3' then '2_InArea'  else '3_Exceed' end) as my_flag, 
					(case when endDate is null then (-1) else  (days(endDate) - days(caseDate)) end) as my_diff, 
					authLvl, (case 
						when authLvl='1' then '分行授權內'
						when authLvl='2' then '總行授權內'
						when authLvl='3' then '營運中心授權內'
						when authLvl='4' then '母行授權內'
						when authLvl='5' then '分行授權外'
						when authLvl='6' then '營運中心授權外'
						else authLvl end) as authLvlDesc, 
					caseLvl,( case	
						when caseLvl='1' then '常董會權限'
						 when caseLvl='2' then '常董會權限簽奉總經理核批'
						 when caseLvl='3' then '常董會權限簽准由副總經理核批'
						 when caseLvl='4' then '利費率變更案件由總處經理核定'
						 when caseLvl='5' then '屬常董會授權總經理逕核案件'
						 when caseLvl='6' then '總經理權限內'
						 when caseLvl='7' then '副總經理權限'
						 when caseLvl='8' then '處長權限'
						 when caseLvl='9' then '其他(經理)'
						 when caseLvl='A' then '董事會權限'
						 when caseLvl='B' then '營運中心營運長/副營運長權限'
						 when caseLvl='C' then '利費率變更案件由董事長核定'
						 when caseLvl='D' then '個金處經理權限'
						else caseLvl end) as caseLvlDesc, 
						caseLvlReason
						,( case	
						when caseLvlReason='1' then '利率'
						when caseLvlReason='2' then '額度'
						 when caseLvlReason='3' then '成數'
						 when caseLvlReason='4' then '年限'
						 when caseLvlReason='5' then '連保人'
						 when caseLvlReason='6' then '聯徵負面資訊'
						 when caseLvlReason='7' then reasonDesc
						else reasonDesc end) as reasonDesc
						,docRslt
				from lms.l120m01a l120m01a left outer join com.belsbrn on caseBrId=brNo
				where docType='2' and docCode in('1','2') and deletedtime is null 
				and ( endDate between '2018-06-01' AND '2019-06-30' )
				) L120
				left outer join lms.l120m01c C      on L120.mainId=C.mainId
				left outer join lms.l140m01a L140 on C.refmainId=L140.mainId
				left outer join lms.l140s02a S02A  on L140.mainId=S02A.mainId
				left outer join lms.l120m01g L120M01G on L140.mainId=L120M01G.mainId
				left outer join lms.c120s01a C120S01A  on L120.mainId=C120S01A.mainId and L140.custId=C120S01A.custId and L140.dupNo=C120S01A.dupNo
				where L140.property!='7' and L140.property!='8' and L140.deletedtime is null and L140.docstatus <> '060'
				and S02A.PRODKIND > ' ' and S02A.PRODKIND not in ('99', 'ZZ')
				and S02A.property!='7' and S02A.property!='8' 
				and (C120S01A.busCode is null or C120S01A.busCode in ('060000','130300', ''))			
				) tmp
			) , 
			t1 as (
				select td2.mainid, td2.seq, td2.phase, td2.nowRate from
				(select mainid, seq, min(phase) as min_phase  from lms.l140s02d
				where mainid in(select tabMainId from t0) and isUseBox='Y' 
				group by mainid, seq
				) td1 left outer join  lms.l140s02d td2 on td1.mainid=td2.mainid and td1.seq=td2.seq and td1.min_phase=td2.phase
			)
			select 
			 t0.CASEBRID	, t0.BRNAME		, t0.BRNGROUP		, t0.CUSTID	, t0.DUPNO	, t0.CUSTNAME
			, t0.CNTRNO	, t0.S02A_LOANAMT	, nowRate 			, t0.PRODKIND	, t0.SUBJCODE	, t0.PROPERTY
			, t0.PROPERTYDESC, t0.CASELVLREASON, t0.REASONDESC, t0.AUTHLVL, t0.AUTHLVLDESC, t0.CASELVL
			, t0.CASELVLDESC, t0.CASEDATE, t0.SENDFIRSTTIME, t0.ENDDATE, t0.MY_DIFF, t0.CASENO
			, t0.MY_FLAG
			, t0.OID , t0.MAINID, t0.CASE_CUSTID, t0.CASE_DUPNO, t0.CASE_CUSTNAME
			, t0.DOCSTATUS, t0.SENDLASTTIME, t0.DOCKIND, t0.DOCRSLT, t0.TABMAINID
			, t0.BUSCODE, t0.COMMSNO, t0.SNOKIND, t0.REUSE, t0.CURRENTAPPLYCURR
			, t0.CURRENTAPPLYAMT, t0.LOANTOTCURR, t0.LOANTOTAMT, t0.MODELKIND, t0.S02A_SEQ, phase
			from t0 left outer join t1 on t0.tabMainId=t1.mainId and t0.S02A_SEQ=t1.seq
			order by my_flag, caseBrId, endDate,CNTRNO,TABMAINID  with ur ;
			]]>
			</value>
		</entry>
        <entry key="J-108-0323_07625_B1001">
            <value>
            update lms.L120S01B set ntcode = 'US' where mainid = 'b36779a06eec491cb3d707e9fb13a591' and oid = '29F0B6F0150211E999178ED40A70FE61'
            </value>
        </entry>
		<entry key="J-109-0023_08034_B1001">
		    <!--Web e-Loan配合整併歡喜房貸方案修訂，調整查核事項條文-->
			<value>
			INSERT INTO LMS.C900S01A (OID,CHECKCODE,CHECKSEQ,CHECKTYPE,CHECKCONTENT,PRODTYPE,CHECKRMK,CREATOR,CREATETIME,UPDATER,UPDATETIME) VALUES (get_oid(),'188', 71,'6','貸款年限＜=20年或＜=30年(屬銀行法第12條之1規範之自用住宅貸款、非首購但聯徵無主從債務(係指有房無房貸))，且擔保品屋齡＜=(不動產耐用年限+15年) ','','不動產耐用年限加強磚造35年鋼筋混凝土50年，房貸核貸成數採加成成數者，擔保品放款值應先預扣折舊，惟符合條件之不動產業簽約案件可除外。','sys',current timestamp,'sys',current timestamp);

			UPDATE LMS.C900M01C SET SOUSECODE = '188'  WHERE SOUSECODE = '71' ;
			</value>
		</entry>
		
		
		<entry key="J-109-0050_05097_B1001">
		    <!--
			(109)第 0498 號
			將借款人Reliance Jio Infocomm limited(Mega ID:INZ0002372)名下所有額度序號(額度序號一:025410600148-額度序號一有納閩參貸，納閩參貸之額度序號:0C1501750018及額度序號二:025410800099)之借款人，更換為戶名:Reliance Industries Limited(Mega ID:INZ0000120) 
			-->
			<value>
			  	UPDATE LMS.L140M01A
				SET CUSTID='INZ0000120',CUSTNAME='RELIANCE JIO INFOCOMM LIMITED' 
				WHERE MAINID IN
				(
				  SELECT REFMAINID FROM LMS.L120M01C WHERE MAINID = '44a092d5a7d2400d99a871a6f7c80c50'
				) AND 
				CNTRNO IN
				(
					'025410600148',
					'025410800099'
				)
			</value>
		</entry>

        <entry key="J-109-0101_09301_B1001">
            <!--
                核定日期:2020-02-20  / 核定文號授審字第兆02-2004號
                    1.額度序號2131098Y2018:瑕疵額度控管方式:限額USD7,000仟元
                    2.額度序號0061098Y2022:瑕疵額度控管方式:限額USD3,500仟元
            -->
            <value>
                UPDATE LMS.L140M01A SET FLAW_FG='2',FLAW_AMT='7000000' WHERE MAINID='9c8605edce774c38b3969da8f5374e15'
                UPDATE LMS.L140M01A SET FLAW_FG='2',FLAW_AMT='3500000' WHERE MAINID='692e2c34d5aa41cba6288164818a0a2a'
            </value>
        </entry>
		
		
		<entry key="J-109-0024_05097_B1001">
            <!--
                  Web e-Loan授信系統撈取馬尼拉分行洗錢防制掃瞄名單
            -->
            <value>
                EXPORT TO LMS0B2.csv OF DEL MODIFIED BY NOCHARDEL codepage=950 
				SELECT CUSTNAME,CUSTENAME,
				REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(CUSTRELATION,'11','具控制權人'),'10','高階管理人員'),'9','應收帳款承購無追索權-買方'),'8','一般保證人'),'7','實際受益人'),'6','關係企業'),'5','擔保品提供人'),'4','連保人'),'3','借戶負責人'),'2','共同借款人'),'1','借戶'),',','、') AS CUSTRELATIONDSCR
				,CASENO,SYSTYPE
				FROM 
				(
					SELECT T1.CUSTNAME,T1.CUSTENAME,T1.CUSTRELATION,T2.CASENO,'LMS' AS SYSTYPE
					FROM 
					LMS.L120S09A AS T1
					LEFT OUTER JOIN 
					LMS.L120M01A AS T2 
					on
					T1.MAINID = T2.MAINID
					WHERE
					T2.MAINID IS NOT NULL AND
					T2.CASEBRID = '0B2' AND 
					T2.DOCSTATUS IN ('05O','06O') and 
					T2.deletedTime IS NULL 
					UNION ALL
					SELECT T1.CUSTNAME,T1.CUSTENAME,T1.CUSTRELATION,T2.CASENO,'DLMS' AS SYSTYPE
					FROM 
					LMS.L120S09A AS T1
					LEFT OUTER JOIN 
					LMS.L160M01A AS T2 
					on
					T1.MAINID = T2.MAINID
					WHERE
					T2.MAINID IS NOT NULL AND
					T2.OWNBRID = '0B2' AND 
					T2.DOCSTATUS IN ('05O','06O') and 
					T2.deletedTime IS NULL 
				) AS X1
				ORDER BY SYSTYPE DESC FETCH FIRST 99999 ROWS ONLY;
            </value>
        </entry>
		
		
		<entry key="J-109-0063_05097_B1001_LMS">
            <!--
                 LMS Web e-Loan授信系統撈取馬尼拉分行洗錢防制掃瞄名單
            -->
            <value>
                EXPORT TO LMS0B6.csv OF DEL MODIFIED BY NOCHARDEL codepage=950 
				SELECT CUSTNAME,CUSTENAME,
				REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(CUSTRELATION,'11','具控制權人'),'10','高階管理人員'),'9','應收帳款承購無追索權-買方'),'8','一般保證人'),'7','實際受益人'),'6','關係企業'),'5','擔保品提供人'),'4','連保人'),'3','借戶負責人'),'2','共同借款人'),'1','借戶'),',','、') AS CUSTRELATIONDSCR
				,CASENO,SYSTYPE
				FROM 
				(
					SELECT T1.CUSTNAME,T1.CUSTENAME,T1.CUSTRELATION,T2.CASENO,'LMS' AS SYSTYPE
					FROM 
					LMS.L120S09A AS T1
					LEFT OUTER JOIN 
					LMS.L120M01A AS T2 
					on
					T1.MAINID = T2.MAINID
					WHERE
					T2.MAINID IS NOT NULL AND
					T2.CASEBRID = '0B6' AND 
					T2.DOCSTATUS IN ('05O','06O') and 
					T2.deletedTime IS NULL 
					UNION ALL
					SELECT T1.CUSTNAME,T1.CUSTENAME,T1.CUSTRELATION,T2.CASENO,'DLMS' AS SYSTYPE
					FROM 
					LMS.L120S09A AS T1
					LEFT OUTER JOIN 
					LMS.L160M01A AS T2 
					on
					T1.MAINID = T2.MAINID
					WHERE
					T2.MAINID IS NOT NULL AND
					T2.OWNBRID = '0B6' AND 
					T2.DOCSTATUS IN ('05O','06O') and 
					T2.deletedTime IS NULL 
				) AS X1
				ORDER BY SYSTYPE DESC FETCH FIRST 99999 ROWS ONLY;
            </value>
        </entry>
		<entry key="J-109-0063_05097_B1001_CES">
            <!--
                CES  Web e-Loan授信系統撈取馬尼拉分行洗錢防制掃瞄名單
            -->
            <value>
                EXPORT TO CES0B6.csv OF DEL MODIFIED BY NOCHARDEL codepage=950
				SELECT T2.CUSTID,T2.CUSTNAME,T2.CUSTENAME,
				REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(CUSTRELATION,'11','具控制權人'),'10','高階管理人員'),'9','應收帳款承購無追索權-買方'),'8','一般保證人'),'7','實際受益人'),'6','關係企業'),'5','擔保品提供人'),'4','連保人'),'3','借戶負責人'),'2','共同借款人'),'1','借戶') AS CUSTRELATIONDSCR,
				T1.SN,'CES' AS SYSTYPE
				FROM 
				CES.C120M01A AS T1
				LEFT OUTER JOIN 
				CES.C120S01D AS T2 
				ON
				T1.MAINID = T2.MAINID
				WHERE
				T2.MAINID IS NOT NULL AND
				T1.OWNBRID = '0B6' AND 
				T1.DOCSTATUS IN ('230','23H') and 
				T1.deletedTime IS NULL 
				ORDER BY SN FETCH FIRST 99999 ROWS ONLY;
            </value>
        </entry>
		<entry key="J-109-0102_05097_B1001">
            <!--
                                           為爭覽管理委員會存款，請盡速協助提供過去二年本行土建融案資料。
				請依Excel提供之欄位，提供土建融建案名稱、地址及土建融原承做分行等資料。
            -->
            <value>
                WITH PROD3334 AS
				(
				  SELECT ELF447N_CUSTID ,ELF447N_DUPNO,ELF447N_CONTRACT AS LNF154_CONTRACT  FROM 
				  (
					  SELECT 
					  ROW_NUMBER() OVER (PARTITION BY ELF447N_CUSTID ,ELF447N_DUPNO,ELF447N_CONTRACT ORDER BY ELF447N_ENDDATE DESC ) AS SEQ,
					  ELF447N_CONTRACT ,ELF447N_CUSTID ,ELF447N_DUPNO
					  FROM mis.elf447n 
					  WHERE ELF447N_PROD_CLASS IN ('33','34') AND ELF447N_ENDDATE IS NOT NULL AND ELF447N_ENDDATE >= '2018-01-01' 
				  ) AS P1
				  WHERE SEQ = 1
				)
				SELECT * FROM 
				(
				  SELECT 
				  CNTR_BRANCH, LNF154_CONTRACT, ELF447N_CUSTID, ELF447N_DUPNO, CNAME,BRANCH, TYPCD, Z1.CUSTID, Z1.DUPNO, COLLNO, SITE1, SITE2, SITE3, SITE4, SITE5, SITE6, SITE7, SITE8
				  FROM 
				  (
					  SELECT 
					  DISTINCT 
					  LEFT(T1.LNF154_CONTRACT,3) AS CNTR_BRANCH,T1.LNF154_CONTRACT,T1.ELF447N_CUSTID,T1.ELF447N_DUPNO,
					  T2.BRANCH,T2.TYPCD,T2.CUSTID,T2.DUPNO,T2.COLLNO,SITE1, SITE2, SITE3, SITE4, SITE5, SITE6, SITE7, SITE8
					  FROM
						(
						   select * from 
						   PROD3334
						   LEFT OUTER JOIN
						   mis.collcntr
						   ON
						   cntrno = LNF154_CONTRACT 
						   WHERE cntrno IS NOT NULL
						) AS T1
						left outer join
						mis.coll0102 AS T2
						on 
						T1.BRANCH = T2.BRANCH AND
						T1.TYPCD = T2.TYPCD AND
						T1.CUSTID = T2.CUSTID AND
						T1.DUPNO = T2.DUPNO AND
						T1.COLLNO = T2.COLLNO 
						WHERE T2.CUSTID IS NOT NULL
					) AS Z1
					LEFT OUTER JOIN
					MIS.CUSTDATA
					ON
					MIS.CUSTDATA.CUSTID = ELF447N_CUSTID AND
					MIS.CUSTDATA.DUPNO = ELF447N_DUPNO
				) AS X1 FETCH FIRST 99999 ROWS only
            </value>
        </entry>
		<entry key="J-109-0228_08034_B1001">
            <!--
            	(109)第(1814)號 e-Loan授信管理系統產出消金授信戶潛在小規模營業人行銷名單等資料。
            -->
            <value>
                with t0 as (
				    select case.caseBrId as caseBrId, case.mainId as caseMainId, case.caseNo, case.endDate
					       , tab.mainid as tabMainId, tab.cntrNo, lnf022_contract, lnf022_loan_date, tab.currentApplyAmt
					       , tab.custId as tab_custId , tab.dupNo as tab_dupNo , tab.custName as tab_custName
					       from lms.l120m01a case
					       left outer join lms.l120m01c c on c.mainId=case.mainid
					       left outer join lms.l140m01a  tab  on tab.mainid=c.refmainId	       
					       left outer join (
							select distinct lnf022_contract, lnf022_loan_date from cms.lnf022 where  (( ascii(LNF022_CUST_ID) between 65 and 90) 
							AND (LNF022_CUST_ID like '_1%' or LNF022_CUST_ID like '_2%' ))
						) lnf022 on tab.cntrNo=lnf022_contract
					       where case.doctype='2'  and case.deletedtime is null  and case.docStatus='05O' 
						       and tab.deletedtime is null and tab.docstatus!='060' and tab.property not in ('7','8')
						       and lnf022_contract is not null  
				)
				select t0.caseBrId , tab_custId 
				,  tab_dupNo 
				,  tab_custName as 客戶姓名
				, s01b.jobType1,(s01b.jobType1 || s01b.jobType2) as jobType3
				, mJobType.codedesc as 職業大類 , mJobType3.codedesc as 職業小類
				, s01b.jobTitle, mJobTitle.CODEDESC as 職稱
				,  s01b.JuId as 公司統編, s01b.comName as 公司名稱
				,  lnf022_contract
				, lnf022_loan_date as 撥款日
				, s01b.comTel as 公司電話, s01a.coTel as 通訊電話, s01a.mTel as 手機
				from t0 
				inner join (select cntrno, max(endDate) as endDate from t0 group by cntrNo) t1 on t0.cntrNo=t1.cntrNo and t0.endDate=t1.endDate
				left outer join lms.c120s01b s01b on t0.caseMainId=s01b.mainid and t0.tab_custId=s01b.custid and t0.tab_dupNo=s01b.dupno
				left outer join lms.c120s01a s01a on t0.caseMainId=s01a.mainid and t0.tab_custId=s01a.custid and t0.tab_dupNo=s01a.dupno
				left outer join (select codevalue,codedesc from com.bcodetype where codetype='jobType' and locale='zh_TW') mJobType on s01b.jobType1= mJobType.codevalue
				left outer join (select substr(codetype, 8,2)||codevalue as codevalue,codedesc from com.bcodetype where (codetype like 'jobType0%' or codetype like 'jobType1%' ) and locale='zh_TW') mJobType3  on (s01b.jobType1 || s01b.jobType2) = mJobType3.codevalue
				left outer join (select CODEVALUE,CODEDESC from com.bcodetype where codetype='lms1205s01_jobTitle' and locale='zh_TW') mJobTitle on s01b.jobTitle=mJobTitle.CODEVALUE
				where jobType1 in ('06','07','08','09','10','11') and jobTitle not in ('a','g','h') 
				order by caseBrid, tab_custId, lnf022_contract ;
            </value>
        </entry>
		<entry key="J-109-0236_10702_B1001">
            <!--
               		(109) 第 1812 號 e-Loan授信管理系統產出紓困貸款進件等資料。
            -->
            <value>
				EXPORT TO "data2.csv" OF DEL MODIFIED BY datesiso 
				with t0 as (
				select m.applyKind as 進件別, m.ownBrId as 分行別, brname as 分行名稱, m.custid as 統編, m.dupno as 重複碼, m.custname as 姓名,'' as ００２４姓名, date(applyTS) as 線上申請日期
				, applyTS as 線上申請日期時間
				, (case when applyKind='B' then applyAmt else 10 end)*10000 as 申請金額
				, notifyMemo  as 備註
				, statFlag || (case when statFlag='0' then '受理中'
				        when statFlag='1' then '審核中'
				        when statFlag='2' then '已核准'
				        when statFlag='A' then '不承作-票債信不良	'
				        when statFlag='B' then '不承作-評分未達60分'
				        when statFlag='C' then '不承作-信保未承作'
				        when statFlag='D' then '不承作-客戶撤件'
				        else '' end) as 申貸進度
				, a.birthday as 生日, a.mtel , a.email
				--, applyIPAddr 
				, comName as 公司名稱, comTel as 公司電話
				, applyTS 
				from lms.c122m01a m  
				left outer join lms.c120s01a a on m.mainid=a.mainid and m.custid=a.custid and m.dupNo=a.dupNo
				left outer join lms.c120s01b b on m.mainid=b.mainid and m.custid=b.custid and m.dupNo=b.dupNo
				left outer join com.belsbrn brn on m.ownbrid=brn.brNo
				where applyKind in ('B','D') and deletedtime is null
				)
				select * from t0 where t0.email in (select email from t0 where email like '%@%' group by email having count(*)>1) order by email, applyTS;
				
				EXPORT TO "data3.csv" OF DEL MODIFIED BY datesiso 
				with t0 as (
				select m.applyKind as 進件別, m.ownBrId as 分行別, brname as 分行名稱, m.custid as 統編, m.dupno as 重複碼, m.custname as 姓名,'' as ００２４姓名, date(applyTS) as 線上申請日期
				, applyTS as 線上申請日期時間
				, (case when applyKind='B' then applyAmt else 10 end)*10000 as 申請金額
				, notifyMemo  as 備註
				, statFlag || (case when statFlag='0' then '受理中'
				        when statFlag='1' then '審核中'
				        when statFlag='2' then '已核准'
				        when statFlag='A' then '不承作-票債信不良	'
				        when statFlag='B' then '不承作-評分未達60分'
				        when statFlag='C' then '不承作-信保未承作'
				        when statFlag='D' then '不承作-客戶撤件'
				        else '' end) as 申貸進度
				, a.birthday as 生日, a.mtel , a.email
				--, applyIPAddr 
				, comName as 公司名稱, comTel as 公司電話
				, applyTS 
				from lms.c122m01a m 
				left outer join lms.c120s01a a on m.mainid=a.mainid and m.custid=a.custid and m.dupNo=a.dupNo
				left outer join lms.c120s01b b on m.mainid=b.mainid and m.custid=b.custid and m.dupNo=b.dupNo
				left outer join com.belsbrn brn on m.ownbrid=brn.brNo
				where applyKind in ('B','D') and deletedtime is null
				)
				select * from t0 where t0.mtel in (select mtel from t0 where mtel > ' 'group by mtel having count(*)>1) order by mtel, applyTS;
				
				EXPORT TO "data4.csv" OF DEL MODIFIED BY datesiso 
				with t0 as (
				select m.applyKind as 進件別, m.ownBrId as 分行別, brname as 分行名稱, m.custid as 統編, m.dupno as 重複碼, m.custname as 姓名,'' as ００２４姓名, date(applyTS) as 線上申請日期
				, applyTS as 線上申請日期時間
				, (case when applyKind='B' then applyAmt else 10 end)*10000 as 申請金額
				, notifyMemo  as 備註
				, statFlag || (case when statFlag='0' then '受理中'
				        when statFlag='1' then '審核中'
				        when statFlag='2' then '已核准'
				        when statFlag='A' then '不承作-票債信不良	'
				        when statFlag='B' then '不承作-評分未達60分'
				        when statFlag='C' then '不承作-信保未承作'
				        when statFlag='D' then '不承作-客戶撤件'
				        else '' end) as 申貸進度
				, a.birthday as 生日, a.mtel , a.email
				--, applyIPAddr 
				, comName as 公司名稱, comTel as 公司電話
				, applyTS 
				from lms.c122m01a m 
				left outer join lms.c120s01a a on m.mainid=a.mainid and m.custid=a.custid and m.dupNo=a.dupNo
				left outer join lms.c120s01b b on m.mainid=b.mainid and m.custid=b.custid and m.dupNo=b.dupNo
				left outer join com.belsbrn brn on m.ownbrid=brn.brNo
				where applyKind in ('B','D') and deletedtime is null
				)
				select * from t0;
            </value>
        </entry>
		<entry key="J-109-0177_10702_B1001">
            <!--
               		(109) 第 1481 號 Web e-Loan 授信管理系統更新地政士證號、姓名等資料
            -->
            <value>
            	select '007' from SYSIBM.SYSDUMMY1;
				select char(repeat('=',80), 80) from SYSIBM.SYSDUMMY1;
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='陳千枝', laaYear='79', laaWord='台內地登', laaNo='003401', laaOffice=''  where oid='66ED6050464840729575032004B7D876');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='李甲乙', laaYear='79', laaWord='台內地登', laaNo='003245', laaOffice=''  where oid='88C1A8850BC74A95801552856EF21D1D');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='江榮輝', laaYear='85', laaWord='台內地登', laaNo='017656', laaOffice=''  where oid='61B417EBF3044948B5322BE12553A7B1');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='陳學海', laaYear='79', laaWord='台內地登', laaNo='003828', laaOffice=''  where oid='BD18CB2A97D8423DB55353EBDE602A47');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='鄭智介', laaYear='86', laaWord='台內地登', laaNo='021198', laaOffice=''  where oid='723A06593D3946419811A3643241DCB8');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='朱世弘', laaYear='80', laaWord='台內地登', laaNo='007777', laaOffice=''  where oid='89612AB66B144486B051B2757754B764');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='王曉虹', laaYear='87', laaWord='台內地登', laaNo='021760', laaOffice=''  where oid='FE8E875BA403497AAFA5503623B0BCB8');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='蘇家豐', laaYear='79', laaWord='台內地登', laaNo='000676', laaOffice=''  where oid='2BB7588150D041F2858D60BC91358A7A');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='游阿梅', laaYear='79', laaWord='台內地登', laaNo='005012', laaOffice=''  where oid='166C77DD02E14D0F8387E241DFC6FE51');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='洪鳳蓁', laaYear='85', laaWord='台內地登', laaNo='018927', laaOffice=''  where oid='266368FF21184F6B81167A9B45A056F7');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='連滋培', laaYear='84', laaWord='台內地登', laaNo='014004', laaOffice=''  where oid='2ABE08DD0E42411F8553332D2CAC482F');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='李中屏', laaYear='94', laaWord='台內地登', laaNo='025400', laaOffice=''  where oid='05A913B12B254167B05E38EA14306A9B');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='張瓊惠', laaYear='85', laaWord='台內地登', laaNo='018401', laaOffice=''  where oid='28F1D857CD5343DD9EC81D41B678CF94');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='王美玉', laaYear='79', laaWord='台內地登', laaNo='002376', laaOffice=''  where oid='0C86358C5A8C4EFB9CE28616228CDD1B');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='張永志', laaYear='84', laaWord='台內地登', laaNo='013859', laaOffice=''  where oid='199B8BD324BD49A3AC423B92CE6BA9AA');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='陳柏宏', laaYear='84', laaWord='台內地登', laaNo='014964', laaOffice=''  where oid='4A244CA21A7A432CBE8407DA3480870E');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='劉水妹', laaYear='84', laaWord='台內地登', laaNo='014572', laaOffice=''  where oid='70C95FC7DA994B73ADC689FD69D7BA2F');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='謝銘泉', laaYear='79', laaWord='台內地登', laaNo='003872', laaOffice=''  where oid='697470CEF70E4EE8B97FF06BFB39CB7F');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='吳秋發', laaYear='82', laaWord='台內地登', laaNo='011465', laaOffice=''  where oid='BEA7F9D51838405CBFD5EA3D9132AD5F');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='楊茂喜', laaYear='89', laaWord='台內地登', laaNo='023945', laaOffice=''  where oid='EB59ABAB26954D82A510F88BE7BB0362');
            	
				select '201' from SYSIBM.SYSDUMMY1;
				select char(repeat('=',80), 80) from SYSIBM.SYSDUMMY1;
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='鄭志驊', laaYear='101', laaWord='台內地登', laaNo='026873', laaOffice='文鼎地政士事務所'  where oid='166F125CA7CE4EC69B155BC0F7324FCB');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='F0A192C8055E47A9ACB1AE8B154A03A7');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='林尤敏', laaYear='97', laaWord='台內地登', laaNo='025901', laaOffice='林懇伶地政士事務所'  where oid='A07DB3BE830C487286F90DE954C4A1C6');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='梁嘉德', laaYear='100', laaWord='台內地登', laaNo='026535', laaOffice='公正地政士事務所'  where oid='BD1445CE331C411DB85822F04041E15C');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='林尤敏', laaYear='97', laaWord='台內地登', laaNo='025901', laaOffice='林懇伶地政士事務所'  where oid='DBF46A27E39641778E9F796B7BCC9425');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='張莉莉', laaYear='79', laaWord='台內地登', laaNo='003247', laaOffice='張莉莉地政士事務所'  where oid='EDA485CB11C04F1A8C0280C012CBE5A4');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='林美慧', laaYear='93', laaWord='台內地登', laaNo='025347', laaOffice='福正地政士事務所'  where oid='077F3CB3A5E748CF9D399208E8030492');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='8EB2BA2D6AEF48DEA06F3840DBCF99F6');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='許景為', laaYear='100', laaWord='台內地登', laaNo='026490', laaOffice='麗昇地政士事務所'  where oid='AF58093227234EBDA71B69E8FBDBE875');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='吳信宏', laaYear='96', laaWord='台內地登', laaNo='025807', laaOffice='信義地政士事務所'  where oid='847D994D89A4460A850D597D1C68A31A');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='66041C0D53154EDB98B4D9B774F3007D');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='鄭東源', laaYear='96', laaWord='台內地登', laaNo='025840', laaOffice='宏信地政士事務所'  where oid='C93E5F97F5E1466AA4B23386F07677D6');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='梁嘉德', laaYear='100', laaWord='台內地登', laaNo='026535', laaOffice='公正地政士事務所'  where oid='DAA3E264216C485CACF1397757CDB1ED');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='', laaYear='84', laaWord='台內地登', laaNo='013986', laaOffice='鄭文清地政士事務所鄭文清'  where oid='DE4D5E48A44F47398F39246A465F725B');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='梁嘉德', laaYear='100', laaWord='台內地登', laaNo='026535', laaOffice='公正地政士事務所'  where oid='1AD094437086404BB1285B2F71781436');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='9FF3CD3D15494D9A954C66A65C1F6628');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='7A2D1405276A447EA2549341A37D9078');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='陳泰州', laaYear='86', laaWord='台內地登', laaNo='020106', laaOffice='陳泰州地政士事務所'  where oid='321D0FE0AD4247DB8FADC5F0C762BE03');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='溫中正', laaYear='86', laaWord='台內地登', laaNo='021241', laaOffice='信義地政士事務所'  where oid='C3B79E7827F84013A753718310C44843');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='F2E031DC39304B2CB7DE37D290F30F5D');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='8B42FB1B7C2549B1931B4B2B8B002C8E');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='E15ACE5173884E12B73E49C9B2A1DE05');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='張永圳', laaYear='99', laaWord='台內地登', laaNo='026387', laaOffice='公正地政士事務所'  where oid='A120D414A69C4ED396776144D49D620B');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='黃仁成', laaYear='85', laaWord='台內地登', laaNo='018027', laaOffice='黃仁成地政士事務所'  where oid='B574DAD1D6FD41DCA91BE30B59F931D3');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='吳心彤', laaYear='99', laaWord='台內地登', laaNo='026381', laaOffice='信義地政士事務所'  where oid='567E208EA72345D2AF36B1DF9A602D1D');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='姜菀玲', laaYear='97', laaWord='台內地登', laaNo='025932', laaOffice='磐石地政士事務所'  where oid='2621EFCF57F045529D91F937E17908B9');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='林俊魁', laaYear='98', laaWord='台內地登', laaNo='026031', laaOffice='信義地政士事務所'  where oid='AB8E5E3B7FE247548617D3E50C9FF4F6');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='毛欣怡', laaYear='85', laaWord='台內地登', laaNo='019715', laaOffice='興國地政士事務所'  where oid='D97C712FAAC04C95BC1DA139853DC1F0');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='2CC86230D8EE41C0B0C264075E7436F2');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='DF7ED7D2BB90416A819131664C4A78CF');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='高培育', laaYear='98', laaWord='台內地登', laaNo='026207', laaOffice='高培育地政士事務所'  where oid='5DC92218F7F84A33A6128681485BBFC3');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='林志豪', laaYear='82', laaWord='台內地登', laaNo='010900', laaOffice='林志豪地政士事務所'  where oid='F82CC3D42BAF4F4695C06DF625D762DC');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='楊國彬', laaYear='98', laaWord='台內地登', laaNo='026144', laaOffice='信義地政士事務所'  where oid='0FE86115108F4A8B9CEB28D146641849');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='楊和棟', laaYear='89', laaWord='台內地登', laaNo='023985', laaOffice='政大地政士事務所'  where oid='6AD5F08BF92748D28B639B8171033F48');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='楊和棟', laaYear='89', laaWord='台內地登', laaNo='023985', laaOffice='政大地政士事務所'  where oid='50E2B00350BD487DAA44A54CA031950A');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='楊和棟', laaYear='89', laaWord='台內地登', laaNo='023985', laaOffice='政大地政士事務所'  where oid='67DFB3379E7F431DB33705A0672A7DEA');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='倪伯瑜', laaYear='100', laaWord='台內地登', laaNo='026643', laaOffice='文鼎地政士事務所'  where oid='40CCC613DBBF41C290077742254ECABD');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='楊朝輝', laaYear='79', laaWord='台內地登', laaNo='004410', laaOffice='世一地政士事務所'  where oid='4632291C5C7C4F6A9398C35BC2FB9614');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='A85169629DEB45E6B0F8F4660B71FA59');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='朱世弘', laaYear='80', laaWord='台內地登', laaNo='007777', laaOffice='朱世弘地政士事務所'  where oid='C3BCE9EB0AD74C73B65CAB63A20E766D');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='46FD08A5761C4CADAD2C6C2BC9B302A8');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='4A4F077D8C8D4CAABE2AB5D307050B27');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='黃旻萁', laaYear='96', laaWord='台內地登', laaNo='025700', laaOffice='信義地政士事務所'  where oid='E45A28EF6A374C22B19FBCFB25C77F06');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='李俊杰', laaYear='98', laaWord='台內地登', laaNo='026194', laaOffice='正業桃園特區地政士事務所'  where oid='D6DC36CDB87649D28324F478D03AD5D8');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='莊苡均', laaYear='98', laaWord='台內地登', laaNo='023012', laaOffice='信義地政士事務所'  where oid='21F4691C73504014A57062F6EC6CA310');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='EC2960C36CF44308AEB31F1F69E689FF');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='梁嘉德', laaYear='100', laaWord='台內地登', laaNo='026535', laaOffice='公正地政士事務所'  where oid='35BADCA1ACC44EF8A060512CB5864F8B');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='吳秋榮', laaYear='82', laaWord='台內地登', laaNo='011465', laaOffice='佳昌地政士事務所'  where oid='3A202B30E3C24CE4B5F1212F71167949');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='4D6B923155FC4167A5F93BC3720F6EE0');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='84A14A14F9364FDEB8F19ACFB11661ED');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='8EBE2A57F49B478DADA70FC27A8436BC');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='62E37EDD417C4C51A25B4FD8B23C9392');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='張家偉', laaYear='100', laaWord='台內地登', laaNo='026638', laaOffice='信義地政士事務所'  where oid='0E5B7716478C48DCAC628ACC3C42F3FC');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='張烔樹', laaYear='88', laaWord='台內地登', laaNo='023198', laaOffice='大禾地政士事務所'  where oid='552ABEC58B534E869391C5B3DE2E2D7B');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='CB8D762E2D45425988BCB33EBD243990');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='39D6A4311089441DA4805D8F4EED06E9');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='55599F579E2245C5B0808BED9A2AC36B');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='33FC6F0F512D4B33B5CB5A2DCEC0C899');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='BCEA5C8A5E3F48A292B3BC851A130FB4');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='833A1143FB814B1CA0D8FE60C7A3EACE');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='許育瑋', laaYear='100', laaWord='台內地登', laaNo='026538', laaOffice='許育瑋地政士事務所'  where oid='6617A29C5A24459982B2825BF48435E0');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='石雪卿', laaYear='103', laaWord='台內地登', laaNo='027342', laaOffice='石雪卿地政士事務所'  where oid='330C043E3D0C42D19B4E4C111AD57902');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='許益瑞', laaYear='84', laaWord='台內地登', laaNo='15249', laaOffice='信義地政士事務所'  where oid='69DBC38A8D054534B19E491BB5590354');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='吳碧鳳', laaYear='99', laaWord='台內地登', laaNo='026402', laaOffice='公正地政士事務所'  where oid='4321A3281BDC4827B6587F5054B5AC82');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='51E8FA42625F4468A409531E82833309');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='27CF7BC83F084B47A3F42E623153D939');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='B29849E2728143958079AA63CE93F13D');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='4D60285572704735BEBE8CF9F04F8926');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='黃瀅苓', laaYear='100', laaWord='台內地登', laaNo='026610', laaOffice='信義地政士事務所'  where oid='AED4ACCC2FA64233AFFB49858DF21A34');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='林美慧', laaYear='93', laaWord='台內地登', laaNo='025347', laaOffice='永慶地政士事務所'  where oid='5C8BFB01733B46399360EC583D58E357');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='吳心彤', laaYear='99', laaWord='台內地登', laaNo='026384', laaOffice='信義地政士事務所'  where oid='F46298F8CDC24018B518B37B975BD86E');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='蔡錦珠', laaYear='79', laaWord='台內地登', laaNo='002736', laaOffice='蔡錦珠地政士事務所'  where oid='49A8CBC5750B4558B59739E62812E0FE');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='林曉莉', laaYear='96', laaWord='台內地登', laaNo='025808', laaOffice='永泰地政士事務所'  where oid='6DF2CD5858934D5B908FCF173A421465');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='1531AC5A092041ADA4581F7AD36BCB52');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='658CF56C736546EDAA60DA3F1A051465');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='張世樟', laaYear='84', laaWord='台內地登', laaNo='014563', laaOffice='嘉信地政士事務所'  where oid='B4989A0550654D8BB474D738C7BCBB88');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='王梓譯', laaYear='95', laaWord='台內地登', laaNo='025696', laaOffice='御淇地政士事務所'  where oid='1166A0504CC5486F922C9870432D0F0C');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='AAD70B927D6D495497D56FC5461A7D23');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='B2ECDEFE4F83439B9A13AFCE52EE8285');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='7BBA9E1108964CE1BC5D883CF2517A77');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='毛欣怡', laaYear='85', laaWord='台內地登', laaNo='019715', laaOffice='興國地政士事務所'  where oid='5FCF26449A954B3C89B9F9A7606FA2DA');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='白美粧', laaYear='82', laaWord='台內地登', laaNo='010383', laaOffice='白地政士事務所'  where oid='ED50C161DFF74B4A94F9CE804DC37180');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='43CB67F4E98C441CA93E4C6E87932FE5');
				SELECT oid ||CAST(' ' AS CHAR(1))|| PRE_laaName ||CAST(' ' AS CHAR(1)) || laaName||CAST(' ' AS CHAR(1))|| PRE_laaYear||CAST(' ' AS CHAR(1))|| laaYear||CAST(' ' AS CHAR(1))||PRE_laaWord||CAST(' ' AS CHAR(1))||laaWord||CAST(' ' AS CHAR(1))  || PRE_laaNo||CAST(' ' AS CHAR(1))  || laaNo ||CAST(' ' AS CHAR(1))||PRE_laaOffice||CAST(' ' AS CHAR(1))||laaOffice   FROM FINAL TABLE(update  lms.c120s01e INCLUDE (PRE_laaName CHAR(10), PRE_laaYear CHAR(4),PRE_laaWord VARCHAR(40),PRE_laaNo CHAR(20),PRE_laaOffice VARCHAR(150) ) set PRE_laaName=laaName,PRE_laaYear=laaYear,PRE_laaWord=laaWord,PRE_laaNo=laaNo,PRE_laaOffice=laaOffice, laaName='周月桂', laaYear='84', laaWord='台內地登', laaNo='015211', laaOffice='周月桂地政士事務所'  where oid='AB55480CF1544F9FB269CCC411E31E3C');
			</value>
        </entry>
		<entry key="J-109-0272_10173_B1002">
            <!--  Web e-Loan 動審表新增-房貸壽險案件檢核表(簽報前及繳納保費前)-檢附項目 -->
            <value>
            	SELECT * FROM FINAL TABLE(INSERT INTO lms.C900S01B(OID, ITEMCODE, ITEMSEQ, ITEMTYPE, ITEMCONTENT, ITEMFORMAT, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '51', '51', '1', '房貸壽險案件檢核表(簽報前及繳納保費前)', '', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)  VALUES(GET_OID(), '01', '13101000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)  VALUES(GET_OID(), '02', '12100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '02', '12100200', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '02', '12400000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '02', '12600100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '02', '12600200', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)  VALUES(GET_OID(), '02', '12600400', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)  VALUES(GET_OID(), '02', '12600500', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '02', '12800000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)  VALUES(GET_OID(), '03', '13100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '03', '13500100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '03', '14100500', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '03', '14501000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '04', '12100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '04', '12100200', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '04', '12400000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '04', '12600100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '04', '12600200', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '04', '12800000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '05', '13103000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '06', '13101000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)  VALUES(GET_OID(), '07', '12100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '07', '12100200', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '07', '13101000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '07', '13502000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '16', '13506200', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '16', '14501500', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '17', '13506200', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '17', '14501500', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '18', '13506300', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '18', '14501500', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '18', '14502000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '19', '13506200', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '19', '14501500', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)  VALUES(GET_OID(), '22', '14100500', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '23', '14100500', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '24', '14100500', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '27', '14100500', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '30', '13500100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '30', '14501000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '31', '13500100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '31', '14501000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '32', '12100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '32', '13101000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '33', '12100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '33', '13100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '33', '14100500', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '34', '12100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '34', '13104000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '34', '14101000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '36', '13500100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '36', '14501000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '37', '13101000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '48', '12100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '49', '12100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '49', '13100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '49', '14100500', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '50', '12100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '50', '13100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '53', '13100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '54', '12100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '62', '13101000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '67', '13500100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '67', '14501000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '68', '13500100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '68', '13500200', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)	VALUES(GET_OID(), '69', '13500100', '2', '51', 'system', current timestamp, 'system', current timestamp));
            </value>
        </entry>
		<entry key="J-109-0288_10173_B1001">
            <!--  Web e-Loan 簽報書新增查核事項-是否查詢聯徵A11 -->
            <value>
            	SELECT * FROM FINAL TABLE(INSERT INTO LMS.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME)  VALUES(GET_OID(), '194', 194, '3', '辦理個人戶新台幣3千萬以上授信案件，是否查詢聯徵中心借戶及配偶個人任職董監事/經理人及獨資/合夥事業負責人企業名錄資訊。', '', '', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '01', '13101000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '02', '12100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '02', '12100200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '02', '12400000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '02', '12600100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '02', '12600200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '02', '12600300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '02', '12600400', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '02', '12600500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '02', '12800000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '03', '13100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '03', '13500100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '03', '14100500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '03', '14501000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '04', '12100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '04', '12100200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '04', '12400000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '04', '12600100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '04', '12600200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '04', '12800000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '05', '13103000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '05', '13504000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '06', '13101000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '06', '13502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '07', '12100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '07', '12100200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '07', '13101000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '07', '13502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '08', '13100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '08', '13101000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '09', '12600100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '09', '13500100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '09', '14501000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '10', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '10', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '11', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '11', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '12', '13506300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '12', '14502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '13', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '13', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '14', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '14', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '15', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '15', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '16', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '16', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '17', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '17', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '18', '13506300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '18', '14502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '19', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '19', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '20', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '20', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '21', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '21', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '22', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '22', '14100500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '22', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '23', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '23', '14100500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '23', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '24', '13506300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '24', '14100500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '24', '14502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '25', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '25', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '26', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '26', '13506300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '26', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '26', '14502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '27', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '27', '13506300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '27', '14100500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '27', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '27', '14502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '28', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '28', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '30', '13500100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '30', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '30', '13506300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '30', '14501000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '30', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '30', '14502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '31', '13500100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '31', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '31', '13506300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '31', '14501000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '31', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '31', '14502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '32', '12100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '32', '13101000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '33', '12100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '33', '12600100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '33', '13100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '33', '13500100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '33', '14100500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '33', '14501000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '34', '12100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '34', '12600100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '34', '13104000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '34', '13505000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '34', '14101000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '34', '14502500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '35', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '35', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '36', '13500100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '36', '14501000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '37', '13101000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '38', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '38', '13506300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '38', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '38', '14502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '39', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '39', '13506300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '39', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '39', '14502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '40', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '40', '13506300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '40', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '40', '14502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '41', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '41', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '42', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '42', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '43', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '43', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '44', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '44', '13506300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '44', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '44', '14502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '45', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '45', '13506300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '45', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '45', '14502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '46', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '46', '13506300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '46', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '46', '14502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '47', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '47', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '48', '12100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '49', '12100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '49', '12600100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '49', '13100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '49', '13500100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '49', '14100500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '50', '12100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '50', '13100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '51', '12600100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '51', '13500100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '51', '14501000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '52', '13500100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '52', '14501000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '53', '13100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '54', '12100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '55', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '55', '13506300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '55', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '55', '14502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '56', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '56', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '57', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '57', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '58', '13100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '58', '13500100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '58', '14501000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '59', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '59', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '60', '13100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '60', '13500100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '60', '14501000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '61', '13100100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '61', '13500100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '61', '14501000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '62', '13101000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '63', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '63', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '64', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '64', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '65', '13506300', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '65', '14502000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '66', '13506200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '66', '14501500', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '67', '13500100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '67', '14501000', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '68', '13500100', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '68', '13500200', '1', '194', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '69', '13500100', '1', '194', 'system', current timestamp, 'system', current timestamp));
            </value>
        </entry>
		<entry key="J-109-0283_10702_B1001">
            <!-- Web e-Loan 修改兆豐產險分公司參數 -->
            <value>
            	select * from final table(update com.bcodetype set codedesc='個人保險營業部' where codetype like 'LNF13E_SUB_UNITNO_90002' and codevalue='77');
				select * from final table(update com.bcodetype set codedesc='基隆通訊處' where codetype like 'LNF13E_SUB_UNITNO_90002' and codevalue='40');
				select * from final table(update com.bcodetype set codedesc='信義分公司' where codetype like 'LNF13E_SUB_UNITNO_90002' and codevalue='45');
				select * from final table(insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'LNF13E_SUB_UNITNO_90002','23','意外保險部',44,'','','zh_TW','system',current timestamp));
				select * from final table(insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'LNF13E_SUB_UNITNO_90002','87','新莊通訊處',45,'','','zh_TW','system',current timestamp));
				select * from final table(insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'LNF13E_SUB_UNITNO_90002','88','數位行銷部',46,'','','zh_TW','system',current timestamp));
            </value>
        </entry>
		<entry key="J-109-0272_10173_B1002">
            <!-- Web e-Loan 動審表新增-房貸壽險案件檢核表(簽報前及繳納保費前)-檢附項目 -->
            <value>
            	SELECT * FROM FINAL TABLE(INSERT INTO lms.C900S01B(OID, ITEMCODE, ITEMSEQ, ITEMTYPE, ITEMCONTENT, ITEMFORMAT, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '51', '51', '2', '房貸壽險案件檢核表(簽報前及繳納保費前)', '', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '03', '13100100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '03', '13500100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '03', '14100500', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '03', '14501000', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '31', '13500100', '2', '51', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '31', '14501000', '2', '51', 'system', current timestamp, 'system', current timestamp));
            </value>
        </entry>
		
		
		<entry key="J-109-0284_05097_B1001">
            <!-- Web e-Loan企金授信動用審核表增列檢核項目第29項-->
            <value>
            	INSERT INTO LMS.L901M01A (OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT, ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES (GET_OID(), '918', CURRENT TIMESTAMP, '918001', '依本行估價作業細則第九條規定，移送徵信處辦理估價報告書書面審查', '', 29, '3', 'zh_TW', CURRENT TIMESTAMP, '918001');
            </value>
        </entry>

		<entry key="J-109-0284_08034_B1001">
            <!-- Web e-Loan消金授信動用審核表增列檢核項目{依本行估價作業細則第九條規定，移送徵信處辦理估價報告書書面審查}-->
            <value>
            	SELECT * FROM FINAL TABLE(INSERT INTO lms.C900S01B(OID, ITEMCODE, ITEMSEQ, ITEMTYPE, ITEMCONTENT, ITEMFORMAT, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '52', 52, '1', '依本行估價作業細則第九條規定，移送徵信處辦理估價報告書書面審查', '', 'system', current timestamp, 'system', current timestamp));
            </value>
        </entry>
	
		<entry key="J-109-0373_08034_B1001">
            <!--  配合稽核處，e-Loan個金授信管理系統產出截至109年8月底所有金門代書事務所地政士承辦之房貸案資料。 -->
            <value>
          	<![CDATA[
            	with t0 as (
					select * from mis.elf457 where (elf457_cntrno in ('046110400112','046110400142','206110500033','206110500094','206110600008','206110600013') 
						or (ELF457_LANAME in ('許育瑋','蕭琪男','蕭琪琳','林素卿','江秀子','賴文琪','余宜霞','朱政勳','陳柏豪','蕭百芳','江岳陽','許家豪','江漢聲','沈冠文','林怡','陳宣宇','鄒宗龍','張亞穆','顏秀姍','李柏緯','李育丞','周漢羿','王大任','王碩璽','陳威廷','李沛繡','林東振','陳台金','林婉真','王涓竹','許育緯','殷若豪','陳槿棠','梁海瀅','郭哲男','潘正威','蕭越','姚玫伶','劉庭佑','陳湘琪','陳怡雯','林宜靜','黃筠雅','簡品錞','許雅鈴','王意綺','田汶玉','張羽潔','黃敏茜','洪貴萍','劉玉麟','葉惠松','游竣翔','楊淳淵','李欣峰','劉三郎'))
						or (ELF457_LANAME like '林%怡%' or ELF457_LANAME like '蕭%越%') and ELF457_LANAME not in ('林怡均')
					)
					union all select '046110400112' as elf457_cntrno,'' as ELF457_LAYEAR,'' as ELF457_LAWORD,'' as ELF457_LANO,'王大任' as ELF457_LANAME,'' as ELF457_LOID,'金門地政士' as ELF457_LONAME,'' as ELF457_CASESRCFLAG  FROM SYSIBM.SYSDUMMY1
					union all select '206110500033' as elf457_cntrno,'' as ELF457_LAYEAR,'' as ELF457_LAWORD,'' as ELF457_LANO,'林怡' as ELF457_LANAME,'' as ELF457_LOID,'金門地政士' as ELF457_LONAME,'' as ELF457_CASESRCFLAG  FROM SYSIBM.SYSDUMMY1
					union all select '206110500094' as elf457_cntrno,'' as ELF457_LAYEAR,'' as ELF457_LAWORD,'' as ELF457_LANO,'林怡' as ELF457_LANAME,'' as ELF457_LOID,'金門' as ELF457_LONAME,'' as ELF457_CASESRCFLAG  FROM SYSIBM.SYSDUMMY1	
					union all select '206110600008' as elf457_cntrno,'' as ELF457_LAYEAR,'' as ELF457_LAWORD,'' as ELF457_LANO,'林怡' as ELF457_LANAME,'' as ELF457_LOID,'金門' as ELF457_LONAME,'' as ELF457_CASESRCFLAG  FROM SYSIBM.SYSDUMMY1	
					union all select '206110600013' as elf457_cntrno,'' as ELF457_LAYEAR,'' as ELF457_LAWORD,'' as ELF457_LANO,'林怡' as ELF457_LANAME,'' as ELF457_LOID,'金門地政士' as ELF457_LONAME,'' as ELF457_CASESRCFLAG  FROM SYSIBM.SYSDUMMY1	
				), t1 as (
					select distinct lnf020_contract, lnf020_cust_id, substr(lnf020_cust_id, 1, 10) as lnf020_id, substr(lnf020_cust_id, 11, 1) as lnf020_dup , lnf020_fact_amt, lnf020_cancel_date
					from mis.misln20 where (lnf020_contract in (select ELF457_CNTRNO from t0 ) 
					)and LNF020_FACT_TYPE!='31'
				)
				select substr(ELF457_CNTRNO, 1, 3 ) as brno
				, (CASE WHEN ELF339_BRNO IN ('007', '201') THEN ELF339_BRNO 
							WHEN ELF339_BRNO_AREA='1' then '931'
							WHEN ELF339_BRNO_AREA='5' then '932'
							WHEN ELF339_BRNO_AREA='6' then '933'
							WHEN ELF339_BRNO_AREA='2' then '934'
							WHEN ELF339_BRNO_AREA='3' then '935'
							ELSE ELF339_BRNO_AREA END) AS BRNGROUP
				, lnf020_id, char(cname) as cname, t0.*, lnf020_fact_amt, lnf020_cancel_date, lnf030_loan_no , lnf030_cancel_date, LNF030_LOAN_DATE, LNF030_1ST_LN_AMT,lnf155_loan_bal_tw, Lnf033_allow_end
				from t0
				left outer join t1 on t0.ELF457_CNTRNO=t1.lnf020_contract
				left outer join mis.custdata on lnf020_id=custId and lnf020_dup=dupno
				left outer join MIS.ELF339 on SUBSTR(ELF457_CNTRNO, 1,3)=ELF339_BRNO
				left outer join ln.lnf030 on ELF457_CNTRNO=LNF030_CONTRACT and LNF030_CHARC_CODE!='30'
				left outer join ln.lnf033 on lnf030_loan_no=lnf033_loan_no
				left outer join ln.lnf155 tb on LNF030_CONTRACT=LNF155_CONTRACT and lnf030_loan_no=lnf155_loan_no and lnf155_data_ym='2020-08'  
				where lnf030_loan_no is not null and (LNF030_LOAN_DATE is null or  (LNF030_LOAN_DATE is not null and LNF030_LOAN_DATE <= '2020-08-31'))
				and lnf030_loan_no not in ('20616730014220','20616730014711','20616730014850','20616730011206','20616730012490','20616730012628','20616030001801')
				and not (LNF030_LOAN_DATE is null and lnf030_cancel_date is not null) 
        	]]>
            </value>
        </entry>
		
		
		<entry key="J-109-0024_05097_B1001_LMS">
            <!-- 配合洗防處申請馬尼拉分行E-Loan系統各關係人之戶名及英文戶名等作業修改。-->
            <value>
            	EXPORT TO LMS0B6.csv OF DEL MODIFIED BY NOCHARDEL codepage=950 
				SELECT CUSTNAME,CUSTENAME,
				REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(CUSTRELATION,'11','具控制權人'),'10','高階管理人員'),'9','應收帳款承購無追索權-買方'),'8','一般保證人'),'7','實際受益人'),'6','關係企業'),'5','擔保品提供人'),'4','連保人'),'3','借戶負責人'),'2','共同借款人'),'1','借戶'),',','、') AS CUSTRELATIONDSCR
				,CASENO,SYSTYPE
				FROM 
				(
					SELECT T1.CUSTNAME,T1.CUSTENAME,T1.CUSTRELATION,T2.CASENO,'LMS' AS SYSTYPE
					FROM 
					LMS.L120S09A AS T1
					LEFT OUTER JOIN 
					LMS.L120M01A AS T2 
					on
					T1.MAINID = T2.MAINID
					WHERE
					T2.MAINID IS NOT NULL AND
					T2.CASEBRID = '0B2' AND 
					T2.DOCSTATUS IN ('05O','06O') and 
					T2.deletedTime IS NULL 
					UNION ALL
					SELECT T1.CUSTNAME,T1.CUSTENAME,T1.CUSTRELATION,T2.CASENO,'DLMS' AS SYSTYPE
					FROM 
					LMS.L120S09A AS T1
					LEFT OUTER JOIN 
					LMS.L160M01A AS T2 
					on
					T1.MAINID = T2.MAINID
					WHERE
					T2.MAINID IS NOT NULL AND
					T2.OWNBRID = '0B6' AND 
					T2.DOCSTATUS IN ('05O','06O') and 
					T2.deletedTime IS NULL 
				) AS X1
				ORDER BY SYSTYPE DESC
            </value>
        </entry>
		
		<entry key="J-109-0024_05097_B1001_CES">
            <!-- 配合洗防處申請馬尼拉分行E-Loan系統各關係人之戶名及英文戶名等作業修改。-->
            <value>
            	EXPORT TO CES0B6.csv OF DEL MODIFIED BY NOCHARDEL codepage=950
				SELECT T2.CUSTID,T2.CUSTNAME,T2.CUSTENAME,
				REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(CUSTRELATION,'11','具控制權人'),'10','高階管理人員'),'9','應收帳款承購無追索權-買方'),'8','一般保證人'),'7','實際受益人'),'6','關係企業'),'5','擔保品提供人'),'4','連保人'),'3','借戶負責人'),'2','共同借款人'),'1','借戶') AS CUSTRELATIONDSCR,
				T1.SN,'CES' AS SYSTYPE
				FROM 
				CES.C120M01A AS T1
				LEFT OUTER JOIN 
				CES.C120S01D AS T2 
				ON
				T1.MAINID = T2.MAINID
				WHERE
				T2.MAINID IS NOT NULL AND
				T1.OWNBRID = '0B2' AND 
				T1.DOCSTATUS IN ('230','23H') and 
				T1.deletedTime IS NULL 
				ORDER BY SN
            </value>
        </entry>
		
		
		<entry key="J-109-0063_05097_B1001_LMS">
            <!-- 配合胡志明市分行申請E-Loan系統各關係人之戶名及英文戶名等修改。-->
            <value>
            	EXPORT TO LMS0B6.csv OF DEL MODIFIED BY NOCHARDEL codepage=950 
				SELECT CUSTNAME,CUSTENAME,
				REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(CUSTRELATION,'11','具控制權人'),'10','高階管理人員'),'9','應收帳款承購無追索權-買方'),'8','一般保證人'),'7','實際受益人'),'6','關係企業'),'5','擔保品提供人'),'4','連保人'),'3','借戶負責人'),'2','共同借款人'),'1','借戶'),',','、') AS CUSTRELATIONDSCR
				,CASENO,SYSTYPE
				FROM 
				(
					SELECT T1.CUSTNAME,T1.CUSTENAME,T1.CUSTRELATION,T2.CASENO,'LMS' AS SYSTYPE
					FROM 
					LMS.L120S09A AS T1
					LEFT OUTER JOIN 
					LMS.L120M01A AS T2 
					on
					T1.MAINID = T2.MAINID
					WHERE
					T2.MAINID IS NOT NULL AND
					T2.CASEBRID = '0B6' AND 
					T2.DOCSTATUS IN ('05O','06O') and 
					T2.deletedTime IS NULL 
					UNION ALL
					SELECT T1.CUSTNAME,T1.CUSTENAME,T1.CUSTRELATION,T2.CASENO,'DLMS' AS SYSTYPE
					FROM 
					LMS.L120S09A AS T1
					LEFT OUTER JOIN 
					LMS.L160M01A AS T2 
					on
					T1.MAINID = T2.MAINID
					WHERE
					T2.MAINID IS NOT NULL AND
					T2.OWNBRID = '0B6' AND 
					T2.DOCSTATUS IN ('05O','06O') and 
					T2.deletedTime IS NULL 
				) AS X1
				ORDER BY SYSTYPE DESC
            </value>
        </entry>
		
		<entry key="J-109-0063_05097_B1001_CES">
            <!-- 配合胡志明市分行申請E-Loan系統各關係人之戶名及英文戶名等修改。-->
            <value>
            	EXPORT TO CES0B6.csv OF DEL MODIFIED BY NOCHARDEL codepage=950
				SELECT T2.CUSTID,T2.CUSTNAME,T2.CUSTENAME,
				REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(CUSTRELATION,'11','具控制權人'),'10','高階管理人員'),'9','應收帳款承購無追索權-買方'),'8','一般保證人'),'7','實際受益人'),'6','關係企業'),'5','擔保品提供人'),'4','連保人'),'3','借戶負責人'),'2','共同借款人'),'1','借戶') AS CUSTRELATIONDSCR,
				T1.SN,'CES' AS SYSTYPE
				FROM 
				CES.C120M01A AS T1
				LEFT OUTER JOIN 
				CES.C120S01D AS T2 
				ON
				T1.MAINID = T2.MAINID
				WHERE
				T2.MAINID IS NOT NULL AND
				T1.OWNBRID = '0B6' AND 
				T1.DOCSTATUS IN ('230','23H') and 
				T1.deletedTime IS NULL 
				ORDER BY SN
            </value>
        </entry>

		<entry key="J-110-0243_11565_B1001">
            <!-- 配合授審處，e-Loan授信管理系統企金及蕭金動用審核表之其他事項增加一選項「免辦理面簽對保」等程式修改 -->
            <value>
            	INSERT INTO LMS.L901M01A(OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT, ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES(GET_OID(), '918', CURRENT TIMESTAMP, '918001', '免辦理面簽對保', '', 30, '1', 'zh_TW', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.L901M01A(OID, BRANCHID, CREATETIME, CREATOR, ITEMCONTENT, ITEMMEMO, ITEMSEQ, ITEMTYPE, LOCALE, UPDATETIME, UPDATER) VALUES(GET_OID(), '918', CURRENT TIMESTAMP, '918001', '免辦理面簽對保', '', 30, '3', 'zh_TW', CURRENT TIMESTAMP, '918001');
				INSERT INTO LMS.C900S01B(OID, ITEMCODE, ITEMSEQ, ITEMTYPE, ITEMCONTENT, ITEMFORMAT, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '53', 53, '1', '免辦理面簽對保', '', 'system', CURRENT TIMESTAMP, 'system', CURRENT TIMESTAMP);
				INSERT INTO COM.BCodeType(OID, CODEDESC, CODEDESC2, CODEDESC3, CODEORDER, CODETYPE, CODEVALUE, LASTMODIFYBY, LASTMODIFYTIME, LOCALE) VALUES(GET_OID(), '免辦理面簽對保', '', '', 20, 'lmsl901m01a_checkList', '20', 'system', CURRENT TIMESTAMP, 'zh_TW'); 
			</value>
        </entry>
		
		<entry key="J-110-0258_05097_B1001">
            <!-- (940)企金業務處  (07114)何坤霖  J-110-0258_05097_B1001 Web e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位 -->
            <value>
            	insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_rescueItem', 'A07' ,'經濟部-１１０年受影響事業貸款方案(搭配國發基金新創事業加碼方案)',4,'','','zh_TW','system',current timestamp);
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_rescueItem', 'A07' ,'經濟部-１１０年受影響事業貸款方案(搭配國發基金新創事業加碼方案)',4,'','','zh_CN','system',current timestamp);
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_rescueItem', 'A07' ,'經濟部-１１０年受影響事業貸款方案(搭配國發基金新創事業加碼方案)',4,'','','en','system',current timestamp);
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_rescueItem', 'H01' ,'國發基金-新創事業紓困加碼方案(非屬經濟部受影響事業貸款方案)',71,'','','zh_TW','system',current timestamp);
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_rescueItem', 'H01' ,'國發基金-新創事業紓困加碼方案(非屬經濟部受影響事業貸款方案)',71,'','','zh_CN','system',current timestamp);
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_rescueItem', 'H01' ,'國發基金-新創事業紓困加碼方案(非屬經濟部受影響事業貸款方案)',71,'','','en','system',current timestamp);
				UPDATE com.BCodeType SET codeOrder = 5  WHERE  codeType = 'lms140_rescueItem'  AND codeValue = 'A01';
				UPDATE com.BCodeType SET codeOrder = 6  WHERE  codeType = 'lms140_rescueItem'  AND codeValue = 'A02';
				UPDATE com.BCodeType SET codeOrder = 7  WHERE  codeType = 'lms140_rescueItem'  AND codeValue = 'A03'; 
			</value>
        </entry>
		<entry key="J-110-0269_10173_B1001">
            <!-- (041)新莊分行  (07512)林盈如 Web e-Loan 個金授信管理系統中個金授信案件簽報書，借款人郭行山(Q101446120)風險權數建檔有誤資料修改 -->
            <value>
				update lms.L120S03A set rskRatio = 100, rskAmt1 = 15000 , RSKR1 = 100 where CNTRNO = '041111000015';
				update lms.l140m01a set itemC = 100, rItemD = 100 where CNTRNO = '041111000015';
			</value>
        </entry>
		<entry key="J-110-0297_10173_B1002">
            <!-- (授信審查處 03793 張敏麒) 新增本案資金用途是否屬股權收購，且併購之公司主要價值相當該公司名下不動產標的(建物符合央行購置高價住宅定義)查核事項 -->
            <value>
				SELECT * FROM FINAL TABLE(INSERT INTO LMS.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME) 
				    VALUES(GET_OID(), '216', 216, '3', '本案資金用途是否屬股權收購，且併購之公司主要價值相當該公司名下不動產標的(建物符合央行購置高價住宅定義)', '', '如是，本案應比照央行購置高價住宅貸款之授信條件辦理。', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '02', '12100100', '1', '216', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '02', '12100200', '1', '216', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '02', '12400000', '1', '216', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '02', '12600100', '1', '216', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '02', '12600200', '1', '216', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '02', '12600300', '1', '216', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '02', '12600400', '1', '216', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '02', '12600500', '1', '216', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '02', '12800000', '1', '216', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '03', '13100100', '1', '216', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '03', '13500100', '1', '216', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '03', '14100500', '1', '216', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '03', '14501000', '1', '216', 'system', current timestamp, 'system', current timestamp));
			</value>
        </entry>		
		<entry key="J-110-0243_11565_B1001">
            <!-- (授信審查處 006929 陳眉如) 調整07 一般消費含團體消貸-額度明細表查核事項-->
            <value>
            	select * from final table (INSERT INTO LMS.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '217', 32, '4', '中期放款：每一借款人貸款金額於三百萬元以內。借款人每人每月平均攤還本息應不超過其月薪1/3', '', '如否，應專案陳報總處核定', 'system', CURRENT TIMESTAMP, 'system', CURRENT TIMESTAMP));
				select * from final table (INSERT INTO LMS.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '218', 33, '4', '每一借款人之前述二項貸款金額於三百萬元以內。', '', '如否，應專案陳報總處核定', 'system', CURRENT TIMESTAMP, 'system', CURRENT TIMESTAMP));
				select * from final table (UPDATE LMS.C900M01C SET SOUSECODE = '217' WHERE PRODKIND='07' AND SUBJCODE='12100100' AND TYPE = '1' AND SOUSECODE = '45');
				select * from final table (UPDATE LMS.C900M01C SET SOUSECODE = '218' WHERE PRODKIND='07' AND SUBJCODE='12100100' AND TYPE = '1' AND SOUSECODE = '53');
				select * from final table (UPDATE LMS.C900M01C SET SOUSECODE = '217' WHERE PRODKIND='07' AND SUBJCODE='12100200' AND TYPE = '1' AND SOUSECODE = '45');
				select * from final table (UPDATE LMS.C900M01C SET SOUSECODE = '218' WHERE PRODKIND='07' AND SUBJCODE='12100200' AND TYPE = '1' AND SOUSECODE = '53');
				select * from final table (UPDATE LMS.C900M01C SET SOUSECODE = '217' WHERE PRODKIND='07' AND SUBJCODE='13101000' AND TYPE = '1' AND SOUSECODE = '45');
				select * from final table (UPDATE LMS.C900M01C SET SOUSECODE = '218' WHERE PRODKIND='07' AND SUBJCODE='13101000' AND TYPE = '1' AND SOUSECODE = '53');
				select * from final table (UPDATE LMS.C900M01C SET SOUSECODE = '217' WHERE PRODKIND='07' AND SUBJCODE='13502000' AND TYPE = '1' AND SOUSECODE = '45');
				select * from final table (UPDATE LMS.C900M01C SET SOUSECODE = '218' WHERE PRODKIND='07' AND SUBJCODE='13502000' AND TYPE = '1' AND SOUSECODE = '53');
			</value>
        </entry>		
        	
	
		<entry key="J-110-0460_05097_B1001">
            <!-- J-110-0460_05097_B1001 Web e-Loan國內企金授信額度明細表「專案種類」增列「國家融資保證機制」 -->
            <value>
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_projClass', '17' ,'17.國家融資保證機制',17,'','','zh_TW','system',current timestamp);
                insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_projClass', '17' ,'17.國家融資保證機制',17,'','','zh_CN','system',current timestamp);
                insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_projClass', '17' ,'17.國家融資保證機制',17,'','','en','system',current timestamp);
			</value>
        </entry>	
			
		<entry key="J-110-0468_05097_B1001">
            <!-- J-110-0468_05097_B1001 Web e-Loan國內企金授信額度明細表「專案種類」增列「台電數位供應鏈融資專案」 -->
            <value>
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_projClass', '18' ,'18.台電數位供應鏈融資專案',18,'','','zh_TW','system',current timestamp);
                insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_projClass', '18' ,'18.台電數位供應鏈融資專案',18,'','','zh_CN','system',current timestamp);
                insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_projClass', '18' ,'18.台電數位供應鏈融資專案',18,'','','en','system',current timestamp);
			</value>
        </entry>	
		
		<entry key="J-108-0359_05097_B1001">
            <!-- J-108-0359_05097_B1001 Web e-Loan修改額度序號0A4500001196為0A4501901196 -->
            <value>
				UPDATE lms.l140m01a SET cntrno = '0A4501901196' WHERE cntrno = '0A4500001196';
			</value>
        </entry>	
		
			
		<entry key="J-110-0503_05097_B1001">
            <!-- J-110-0503_05097_B1001 為配合客戶更改戶名..敬請協助將客戶戶名    G.C.T.O REAL ESTATE & CONSTRUCTION CO., LTD.  更改為  KINGSTOWN REAL ESTATE & CONSTRUCTION CO., LTD.   惟MEGA ID   KHZ0005130    維持不變 -->
			<!--
				SELECT * FROM LMS.L140M01A  WHERE CUSTID = 'KHZ0005130' AND MAINID IN (SELECT REFMAINID FROM LMS.L120M01C WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366'));
				SELECT * FROM LMS.L140M01I  WHERE  RID = 'KHZ0005130' AND MAINID IN (SELECT REFMAINID FROM LMS.L120M01C WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366'));
				SELECT * FROM LMS.L120M01A  WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND CUSTID    = 'KHZ0005130';
				SELECT * FROM LMS.L120S01A WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND CUSTID = 'KHZ0005130';
				SELECT * FROM LMS.L120S01O  WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND RCUSTID = 'KHZ0005130';
				SELECT * FROM LMS.L120S04A  WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND CUSTID = 'KHZ0005130';			
				SELECT * FROM LMS.L120S05B  WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND CUSTID = 'KHZ0005130';
				SELECT * FROM LMS.L120S05D  WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND CUSTID = 'KHZ0005130';
				SELECT * FROM LMS.L120S06A  WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND CUSTID  = 'KHZ0005130';
				SELECT * FROM LMS.L120S06A  WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND CUSTID2 = 'KHZ0005130';

			-->
            <value>
            	<!--
				//額度明細表
				UPDATE LMS.L140M01A SET CUSTNAME='KINGSTOWN REAL ESTATE & CONSTRUCTION CO., LTD.'  WHERE CUSTID = 'KHZ0005130' AND MAINID IN (SELECT REFMAINID FROM LMS.L120M01C WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366'));
				 
				//額度明細表/L140M01I．連保人資料檔
				UPDATE LMS.L140M01I SET RNAME='KINGSTOWN REAL ESTATE & CONSTRUCTION CO., LTD.' WHERE  RID = 'KHZ0005130' AND MAINID IN (SELECT REFMAINID FROM LMS.L120M01C WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366'));
				 
				//簽報書
				UPDATE LMS.L120M01A SET  CUSTNAME='KINGSTOWN REAL ESTATE & CONSTRUCTION CO., LTD.' WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND CUSTID    = 'KHZ0005130';
				 
				//借款人基本資料
				UPDATE LMS.L120S01A SET  CUSTNAME='KINGSTOWN REAL ESTATE & CONSTRUCTION CO., LTD.' WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND CUSTID = 'KHZ0005130';
				UPDATE LMS.L120S01O SET  RCUSTNAME='KINGSTOWN REAL ESTATE & CONSTRUCTION CO., LTD.' WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND RCUSTID = 'KHZ0005130';
				
				//關係戶於本行各項業務往來明細檔
				UPDATE LMS.L120S04A SET CUSTNAME='KINGSTOWN REAL ESTATE & CONSTRUCTION CO., LTD.' WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND CUSTID = 'KHZ0005130';
				 
				//L120S05B．借款人集團授信明細檔
				UPDATE LMS.L120S05B SET CUSTNAME='KINGSTOWN REAL ESTATE & CONSTRUCTION CO., LTD.' WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND CUSTID = 'KHZ0005130';
				 
				//L120S05D．借款人關係企業授信明細檔
				UPDATE LMS.L120S05D SET CUSTNAME='KINGSTOWN REAL ESTATE & CONSTRUCTION CO., LTD.' WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND CUSTID = 'KHZ0005130';
				 
				//L120S06A．利害關係人授信條件對照表檔
				UPDATE LMS.L120S06A SET CUSTNAME='KINGSTOWN REAL ESTATE & CONSTRUCTION CO., LTD.' WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND CUSTID  = 'KHZ0005130';
				UPDATE LMS.L120S06A SET CUSTNAME2='KINGSTOWN REAL ESTATE & CONSTRUCTION CO., LTD.' WHERE MAINID IN ('94ae9c2ba62946f8928808363309f340','8ff16e0c3fbc40959b606dd3fec76366') AND CUSTID2 = 'KHZ0005130';
                -->
			</value>
        </entry>
		
		<entry key="J-111-0050_10173_B1001">
            <!-- J-108-0359_05097_B1001 Web e-Loan 修改產品種類代碼07，代碼142消金簽報書查核事項內容 -->
            <value>
				update lms.C900S01A set CHECKCONTENT = '1.借款人具完全行為能力之我國國民，其年齡加計貸款年限不超過六十五，且為「消費性貸款作業要點」第五條第二款所列人員，且借款人年薪達新台幣六十萬元以上者。2.或為專案貸款(例：團體消費性貸款、本行員工消費性貸款...等)，借款人對象及條件是否從其規定。' where CHECKTYPE = '2' AND CHECKCODE = '142';
			</value>
        </entry>
		<entry key="J-111-0152_10702_B1001">
			<!-- J-111-0152_10702_B1001 Web e-Loan調整行員地政士黑名單參數 -->
			<value>
				update lms.C101M01A set importFlag='Y'where mainid='9e7b10bd6cb447cfaa6e0f21a84ad918';
				update lms.c101s01j set LAACTLFLAG='' where mainid='9e7b10bd6cb447cfaa6e0f21a84ad918';
			</value>
		</entry>
		
		<entry key="J-111-0172_11850_B1001">
			<!-- J-111-0172_11850_B1001 稽核處進入分行e-loan個金授信系統時，缺少消金契約書、消金模擬動審二功能，因查核之需，申請開放 -->
			<value>
				insert into com.BELSRLF values ('LBEL01G', '3',  '328017', '5', null, 'Y', '011850', current timestamp);
				insert into com.BELSRLF values ('LBEL01G', '3',  '338049', '5', null, 'Y', '011850', current timestamp);
				insert into com.BELSRLF values ('LBEL01G', '3',  '338050', '5', null, 'Y', '011850', current timestamp);
				insert into com.BELSRLF values ('LBEL01G', '3',  '338051', '5', null, 'Y', '011850', current timestamp);
                                                                                    
				insert into com.BELSRLF values ('LBEL01G', '3',  '328021', '5', null, 'Y', '011850', current timestamp);
				insert into com.BELSRLF values ('LBEL01G', '3',  '338100', '5', null, 'Y', '011850', current timestamp);
				insert into com.BELSRLF values ('LBEL01G', '3',  '338101', '5', null, 'Y', '011850', current timestamp);
				insert into com.BELSRLF values ('LBEL01G', '3',  '338102', '5', null, 'Y', '011850', current timestamp);				                                                                                                      
                                                                                  
				insert into com.BELSRLF values ('LBEL02G', '3',  '328017', '7', null, 'Y', '011850', current timestamp);
				insert into com.BELSRLF values ('LBEL02G', '3',  '338049', '7', null, 'Y', '011850', current timestamp);
				insert into com.BELSRLF values ('LBEL02G', '3',  '338050', '7', null, 'Y', '011850', current timestamp);
				insert into com.BELSRLF values ('LBEL02G', '3',  '338051', '7', null, 'Y', '011850', current timestamp);
                                                                                    
				insert into com.BELSRLF values ('LBEL02G', '3',  '328021', '7', null, 'Y', '011850', current timestamp);
				insert into com.BELSRLF values ('LBEL02G', '3',  '338100', '7', null, 'Y', '011850', current timestamp);
				insert into com.BELSRLF values ('LBEL02G', '3',  '338101', '7', null, 'Y', '011850', current timestamp);
				insert into com.BELSRLF values ('LBEL02G', '3',  '338102', '7', null, 'Y', '011850', current timestamp);
			</value>
		</entry>
		
		<entry key="J-111-0330_11850_B1001">
			<!-- J-111-0330 為稽核業務之需，請開放稽核處得查詢e-loan之央行註記異動作業 -->
			<value>
				INSERT INTO com.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, PGMDEPT, AUTOFLAG, UPDATER, UPDTIME) VALUES('LBEL02G', '3', 338094, 4, NULL, 'Y', '011850', current timestamp);
				INSERT INTO com.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, PGMDEPT, AUTOFLAG, UPDATER, UPDTIME) VALUES('LBEL02G', '3', 338095, 4, NULL, 'Y', '011850', current timestamp);
				INSERT INTO com.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, PGMDEPT, AUTOFLAG, UPDATER, UPDTIME) VALUES('LBEL02G', '3', 338096, 4, NULL, 'Y', '011850', current timestamp);

				INSERT INTO com.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, PGMDEPT, AUTOFLAG, UPDATER, UPDTIME) VALUES('LBEL01G', '3', 338094, 4, NULL, 'Y', '011850', current timestamp);
				INSERT INTO com.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, PGMDEPT, AUTOFLAG, UPDATER, UPDTIME) VALUES('LBEL01G', '3', 338095, 4, NULL, 'Y', '011850', current timestamp);
				INSERT INTO com.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH, PGMDEPT, AUTOFLAG, UPDATER, UPDTIME) VALUES('LBEL01G', '3', 338096, 4, NULL, 'Y', '011850', current timestamp);
			</value>
		</entry>

		<entry key="J-111-0367_09301_B1001">
			<!-- J-111-0367_09301_B1001 因覆審業務所需，請開放授審處有登入各授信管理分處之e-loan系統使用考評表之權限 -->
			<value>
				INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH,PGMDEPT,AUTOFLAG,UPDATER,UPDTIME) values('LAEL01S','3',326100,5,null,'Y','system', current timestamp);
				INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH,PGMDEPT,AUTOFLAG,UPDATER,UPDTIME) values('LAEL01S','3',336101,5,null,'Y','system', current timestamp);
				INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH,PGMDEPT,AUTOFLAG,UPDATER,UPDTIME) values('LAEL01S','3',336102,5,null,'Y','system', current timestamp);
				INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH,PGMDEPT,AUTOFLAG,UPDATER,UPDTIME) values('LAEL01S','3',336103,5,null,'Y','system', current timestamp);
				INSERT INTO COM.BELSRLF(ROLCODE, DATASTU, PGMCODE, PGMAUTH,PGMDEPT,AUTOFLAG,UPDATER,UPDTIME) values('LAEL01S','3',336104,5,null,'Y','system', current timestamp);
			</value>
		</entry>

		<entry key="J-111-0152_10702_B1001">
			<!-- J-111-0152_10702_B1001 Web e-Loan調整行員地政士黑名單參數 -->
			<value>
				update lms.C101M01A set importFlag='Y'where mainid='96AB7CC87F629CC048257B3D001BEA1C';
				update lms.c101s01j set LAACTLFLAG='' where mainid='96AB7CC87F629CC048257B3D001BEA1C';
			</value>
		</entry>
		<entry key="J-111-0392_11850_B1001">
			<!-- J-111-0152_10702_B1001 因111年9月1日起消金業務處「電銷小組」併入信用卡處，擬於ELOAN子系統:EX01-電銷經辦與EX02-電銷主管，將信用卡處加入使用單位 -->
			<value>
				INSERT INTO com.BASSIGNGROUP(OID, GROUPID, GROUPNAME, LASTMODIFYBY, LASTMODIFYTIME)  VALUES('406CF77016B811EDB220814DC0A89955', 'GRPEX0', '電銷人員', '011850', current timestamp);
				INSERT INTO com.BASSIGNREL(OID, GROUPID, BRNO, LASTMODIFYBY, LASTMODIFYTIME)  VALUES('2827D190178911EDB220814DC0A89955', 'GRPEX0', '027', '011850', current timestamp);
				INSERT INTO com.BASSIGNREL(OID, GROUPID, BRNO, LASTMODIFYBY, LASTMODIFYTIME)  VALUES('282846C0178911EDB220814DC0A89955', 'GRPEX0', '070', '011850', current timestamp);
				INSERT INTO com.BASSIGNREL(OID, GROUPID, BRNO, LASTMODIFYBY, LASTMODIFYTIME)  VALUES('28286DD0178911EDB220814DC0A89955', 'GRPEX0', '109', '011850', current timestamp);
				INSERT INTO com.BASSIGNREL(OID, GROUPID, BRNO, LASTMODIFYBY, LASTMODIFYTIME)  VALUES('282894E0178911EDB220814DC0A89955', 'GRPEX0', '229', '011850', current timestamp);
				update com.BASSIGNAGENT set GROUPID = 'GRPEX0' where BRANCHID = '109';
			</value>
		</entry>
		<entry key="J-111-0476_11850_B1001">
			<!-- 請於功能(授信管理系統-個金授信-個金徵信作業-基本資料)"現住房屋 " 新增選項 7-親屬所有。 -->
			<value>
				insert into com.BCODETYPE values (GET_OID(), 'lms1205s01_houseStatus', '7','Owned by relatives', 'en', '', '', '7', 'system', current timestamp);
				insert into com.BCODETYPE values (GET_OID(), 'lms1205s01_houseStatus', '7','亲属所有', 'zh_CN', '', '', '7', 'system', current timestamp);
				insert into com.BCODETYPE values (GET_OID(), 'lms1205s01_houseStatus', '7','親屬所有', 'zh_TW', '', '', '7', 'system', current timestamp);
			</value>
		</entry>

		<entry key="J-111-0123_10173_B1001">
			<!-- J-111-0123_10173_B1001 修改高雄機場分公司為高雄國際機場分公司 -->
			<value>
				select * from com.bcodetype where codedesc like '%歡喜房貸%';
			</value>
		</entry>

		<entry key="J-111-0546_10702_B1001">
			<!-- J-111-0546_10702_B1001 Web e-Loan調整行員地政士黑名單參數 -->
			<value>
				update lms.C101M01A set importFlag='Y'where mainid='00bace40ed124762a30f8911932227b1';
				update lms.c101s01j set LAACTLFLAG='' where mainid='00bace40ed124762a30f8911932227b1';
			</value>
		</entry>

		<entry key="J-111-0558_10702_B1001">
			<!-- J-111-0558_10702_B1001 Web e-Loan調整行員地政士黑名單參數 -->
			<value>
				update lms.C101M01A set importFlag='Y'where mainid='9ec394cb0ced4d178e40f0c70edf846a';
				update lms.c101s01j set LAACTLFLAG='' where mainid='9ec394cb0ced4d178e40f0c70edf846a';
			</value>
		</entry>

		<entry key="J-111-0623_12313_B1001">
			<!-- J-111-0623 簽案或動審時是否至少擇一填寫「兆豐商銀疑似洗錢或資恐交易態樣檢核表-授信」改為是否填寫「兆豐商銀疑似洗錢或資恐交易態樣檢核表-授信」 -->
			<value>
				UPDATE LMS.L901M01A SET ITEMCONTENT = '是否填寫「兆豐商銀疑似洗錢或資恐交易態樣檢核表-授信」' WHERE OID = '9AAFCB100FDA11E98E14D2F10A70FE61';--國內企金
				UPDATE LMS.L901M01A SET ITEMCONTENT = '是否填寫「兆豐商銀疑似洗錢或資恐交易態樣檢核表-授信」' WHERE OID = 'D3AF50C00FDA11E98E14D2F10A70FE61';--全行共同項目(適用如國外分行)
				UPDATE LMS.L901M01A SET ITEMCONTENT = '是否填写「兆丰商银疑似洗钱或资恐交易态样检核表-授信」' WHERE OID = 'DA9425A00FDA11E98E14D2F10A70FE61';--企金(簡體)
				UPDATE LMS.C900S01B SET ITEMCONTENT = '是否填寫「兆豐商銀疑似洗錢或資恐交易態樣檢核表-授信」' WHERE OID = '534C8BE4555C4CEFBD80427BBE33313A';--個金
			</value>
		</entry>
		
		
		<entry key="J-111-0604_05097_B1001">
			<!-- J-111-0604_05097_B1001 Web e-Loan授信系統額度明細表專案總類欄位內新增「長照機構優惠貸款專案」選項。 -->
			<value>
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_projClass', '19','19.長照機構優惠貸款專案',19,'','','zh_TW','system',current timestamp);
                insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_projClass', '19','19.长照机构优惠贷款专案',19,'','','zh_CN','system',current timestamp);
                insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_projClass', '19','19.Long-term care institution preferential loan project',19,'','','en','system',current timestamp);
			</value>
		</entry>
		
		<entry key="J-111-0628_05097_B1001">
			<!-- J-111-0628_05097_B1001 Web e-Loan授信系統額度明細表附檔所列額度序號約定融資額度註記修正為「不可取消」 -->
			<value>
				UPDATE LMS.L140M01A SET EXCEPTFLAG = 'N' WHERE VALUE(EXCEPTFLAG,'') != 'N' AND CNTRNO IN 
				(
					'0B8502250019',
					'002110600140',
					'004110800112',
					'007410700192',
					'007410900118',
					'015111100026',
					'015111100029',
					'016110900021',
					'019111000053',
					'019111000054',
					'019111000055',
					'019111100037',
					'019111100038',
					'019111100039',
					'025411100041',
					'025411100043',
					'025411100045',
					'025411100047',
					'027111100055',
					'029111100042',
					'031110700053',
					'042110800019',
					'046110900726',
					'050110800020',
					'059110800005',
					'059110800006',
					'0B6502050060',
					'0B8501950050',
					'0B8501950051',
					'0B8502250035',
					'0B9501500013',
					'0B9501850010',
					'0B9501950022',
					'0B9501950069',
					'0B9501950070',
					'0B9502150031',
					'0B9502150033',
					'0B9502250047',
					'0B9502250048',
					'0B9502250063',
					'0C2501950025',
					'0C2501950076',
					'0C2501950077',
					'0C3501750078',
					'0C3501850035',
					'0C3502050032',
					'0C5501950012',
					'0C5502150022',
					'0C5502250032',
					'0D7501950006',
					'0D7502150006',
					'0D8502150008',
					'202111100079',
					'202111100080',
					'207110800313',
					'207110800316',
					'207111000101',
					'207410900003',
					'208110800004'
				);
			</value>
		</entry>

		<entry key="J-112-0001_09301_B1001">
			<value>
				UPDATE COM.BSYSPARAM SET PARAMVALUE = '"2019-02":"029","2020-01":"103,031,051","2021-02":"013,038,052,053,066,072,212,227,242","2023-01":"013,038,040,047,059,072,103,227"' WHERE PARAM = 'LMS_J1080036_ADJUST_LRDATE'
			</value>
		</entry>

		<entry key="J-112-0007_09301_B1001">
			<value>
				UPDATE COM.BSYSPARAM SET PARAMVALUE = '"2019-02":"029","2020-01":"103,031,051","2021-02":"013,038,052,053,066,072,212,227,242","2023-01":"013,038,040,047,059,072,103,227,201"' WHERE PARAM = 'LMS_J1080036_ADJUST_LRDATE'
			</value>
		</entry>

		<entry key="J-113-0408">
			<value>
				UPDATE COM.BSYSPARAM SET PARAMVALUE = '2024-12-01' WHERE PARAM = 'LMS_CHK_L120S01Q_VER05'
			</value>
		</entry>
		
		<entry key="J-111-0621_12313_B1001">
			<!-- J-111-0621 歡喜管理報表[拒絕碼]一欄之代號新增項目、定義文字調整 -->
			<value>
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '信用不良：信用卡曾有催呆或協商之紀錄' WHERE CODETYPE = 'c122m01a_docstatus_G' AND CODEVALUE = 'G20');
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '信用不良：授信曾有催呆或協商之紀錄' WHERE CODETYPE = 'c122m01a_docstatus_G' AND CODEVALUE = 'G21');
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '聯徵查詢紀錄：近期申請他行信貸或信用卡未過' WHERE CODETYPE = 'c122m01a_docstatus_G' AND CODEVALUE = 'G24');
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '大數據風險資料庫：評等警訊-D等或E等' WHERE CODETYPE = 'c122m01a_docstatus_G' AND CODEVALUE = 'G25');
				SELECT * FROM FINAL TABLE(INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'c122m01a_docstatus_G', 'G31', '資金用途：資金用途不符', 'zh_TW', 'J-111-0621_12313_B1001', '', '31', '012313', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'c122m01a_docstatus_G', 'G32', '信用不良：照會曾有債協紀錄', 'zh_TW', 'J-111-0621_12313_B1001', '', '32', '012313', current timestamp));
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '客戶撤件：已申貸他行(未照會)' WHERE CODETYPE = 'c122m01a_docstatus_I' AND CODEVALUE = 'I01');
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '客戶撤件：客戶暫不申貸(已照會)' WHERE CODETYPE = 'c122m01a_docstatus_I' AND CODEVALUE = 'I04');
				SELECT * FROM FINAL TABLE(INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'c122m01a_docstatus_I', 'I13', '利率不符需求：利率不符需求(最適利率)', 'zh_TW', 'J-111-0621_12313_B1001', '', '13', '012313', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'c122m01a_docstatus_I', 'I14', '利率不符需求：利率不符需求(二次議價)', 'zh_TW', 'J-111-0621_12313_B1001', '', '14', '012313', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'c122m01a_docstatus_I', 'I15', '利率不符需求：利率不符需求(評等警訊-D等或E等)', 'zh_TW', 'J-111-0621_12313_B1001', '', '15', '012313', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'c122m01a_docstatus_I', 'I16', '額度不符需求：額度不符需求(欲代償他行)', 'zh_TW', 'J-111-0621_12313_B1001', '', '16', '012313', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'c122m01a_docstatus_I', 'I17', '額度不符需求：額度不符需求(小額客群)', 'zh_TW', 'J-111-0621_12313_B1001', '', '17', '012313', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'c122m01a_docstatus_I', 'I18', '額度不符需求：額度不符需求(綜合風險考量)', 'zh_TW', 'J-111-0621_12313_B1001', '', '18', '012313', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'c122m01a_docstatus_I', 'I19', '額度不符需求：額度不符需求(評等警訊-D等或E等)', 'zh_TW', 'J-111-0621_12313_B1001', '', '19', '012313', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'c122m01a_docstatus_I', 'I20', '條件不符需求：年限不符需求', 'zh_TW', 'J-111-0621_12313_B1001', '', '20', '012313', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'c122m01a_docstatus_I', 'I21', '條件不符需求：手續費不符需求 ', 'zh_TW', 'J-111-0621_12313_B1001', '', '21', '012313', current timestamp));
			</value>
		</entry>

		<entry key="J-111-0619_10702_B1001">
			<!-- J-111-0619 Web e-Loan調整報表收件人設定 -->
			<value>
				//青年安心成家優惠貸款v2優惠房貸統計數
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '<EMAIL>' WHERE codeType='lms9541v02_mailSites' and codeValue=9);
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '<EMAIL>' WHERE codeType='lms9541v02_mailSites' and codeValue=11);
				SELECT * FROM FINAL TABLE(INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'lms9541v02_mailSites', '12', '<EMAIL>', 'zh_TW', 'J-111-0619 Web e-Loan調整報表收件人設定', '', '31', '010702', current timestamp));
				//房貸引介人員-55555住商公司專案 統計數
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '<EMAIL>' WHERE codeType='importId55555_mailSites' and codeValue=1);
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '<EMAIL>' WHERE codeType='importId55555_mailSites' and codeValue=7);
				SELECT * FROM FINAL TABLE(INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'importId55555_mailSites', '9', '<EMAIL>', 'zh_TW', 'J-111-0619 Web e-Loan調整報表收件人設定', '', '31', '010702', current timestamp));
				//房貸利率方案16.永慶及信義房仲 統計數
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '<EMAIL>' WHERE codeType='ratePlan16_mailSites' and codeValue=1);
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '<EMAIL>' WHERE codeType='ratePlan16_mailSites' and codeValue=7);
				SELECT * FROM FINAL TABLE(INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'ratePlan16_mailSites', '9', '<EMAIL>', 'zh_TW', 'J-111-0619 Web e-Loan調整報表收件人設定', '', '31', '010702', current timestamp));
				//自住型房貸成長方案統計數
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '<EMAIL>' WHERE codeType='ratePlan20_mailSites' and codeValue=3);
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '<EMAIL>' WHERE codeType='ratePlan20_mailSites' and codeValue=6);
			</value>
		</entry>
		
		<entry key="J-112-0027_11879_B1001">
			<!-- J-112-0027 Web e-Loan 產出111年12月歡喜信貸撥款及對保資料資料 -->
			<value>
				select custid as 借款人ID, custname as 借款人姓名, CONTRNUMBER as 額度序號, PLOANCTRNO as 契約編號 
				, PLOANCTRSIGNTIMEM as 主借人線上簽約時間, PLOANCTRSIGNTIME1 as 保證人線上簽約時間 
				, PLOANCTRDCTIME as 作廢契約時間, PLOANCTRDCUSER  as 作廢契約人員 
				, mainId 
				from lms.c340m01a where custid like '%' 
				and deletedtime is null and PLOANCTRBEGDATE is not null and ctrtype='A' and ( 
				(PLOANCTRSIGNTIME1 is null and PLOANCTRSIGNTIMEM between '2022-12-01' and '2023-01-01') 
				or (PLOANCTRSIGNTIME1 is not null and (PLOANCTRSIGNTIME1 between '2022-12-01' and '2023-01-01')) 
				) Fetch First 5000 rows only 
			</value>
		</entry>
		
		
		<entry key="J-112-0035_05097_B1001">
			<!--J-112-0035_05097_B1001 Web e-Loan企金授信「不動產授信例外管理報表」內容新增點選「授信利率暨保證及承兌手續費計收標準實施要點」之案件 -->
			<value>
				UPDATE com.BSYSPARAM SET PARAMVALUE = '5,32,36,47,48' WHERE PARAM = 'LMS_L140M01A_NEED_INTREGREASON';
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_intReg', '48' ,'辦理危險及老舊建築物重建貸款要點',48,'','','zh_TW','system',current timestamp);
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_intReg', '48' ,'辦理危險及老舊建築物重建貸款要點',48,'','','zh_CN','system',current timestamp);
				insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'lms140_intReg', '48' ,'辦理危險及老舊建築物重建貸款要點',48,'','','en','system',current timestamp);
			</value>
		</entry>
				
		<entry key="J-112-0066_12313_B1001">
			<!--J-112-0066 Web e-Loan調整報表收件人設定-->
			<value>
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '<EMAIL>', CODEDESC2 = '張菁蘭', CODEDESC3 = 'J-112-0066' WHERE CODETYPE = 'lms9541v02_mailSites' AND CODEVALUE = 10);
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '<EMAIL>', CODEDESC2 = '張菁蘭', CODEDESC3 = 'J-112-0066' WHERE CODETYPE = 'importId55555_mailSites' AND CODEVALUE = 3);
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '<EMAIL>', CODEDESC2 = '張菁蘭', CODEDESC3 = 'J-112-0066' WHERE CODETYPE = 'ratePlan16_mailSites' AND CODEVALUE = 3);
				SELECT * FROM FINAL TABLE(UPDATE COM.BCODETYPE SET CODEDESC = '<EMAIL>', CODEDESC2 = '張菁蘭', CODEDESC3 = 'J-112-0066', LASTMODIFYBY = '012313' WHERE CODETYPE = 'ratePlan20_mailSites' AND CODEVALUE = 2);
			</value>
		</entry>
		
		<entry key="J-112-0077_05097_B1001">
			<!--J-112-0077_05097_B1001 Web e-Loan授信管理系統之企金額度LGD計算結果改為必填欄位 -->
			<value>
				UPDATE com.BSYSPARAM SET PARAMVALUE = 'Y' WHERE PARAM = 'LMS_LGD_CHK_SEND_BOSS';
			</value>
		</entry>
		
		<entry key="J-112-0099_12313_B1001">
			<!--J-112-0099_12313_B1001 歡喜信貸用於分案電銷人員維護案件的報表製作(報表名稱「歡喜信貸KYC分案報表」 -->
			<value>
				SELECT * FROM FINAL TABLE(INSERT INTO COM.BCODETYPE(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'lms9511v01_docType2', 'CLS180R27C', '歡喜信貸KYC分案報表', 'zh_TW', 'J-112-0099_12313_B1001', '', '294', '012313', current timestamp));
			</value>
		</entry>
		
		<!--J-112-0140_10173_B1001 修改簽報書-客戶章明珠H220725615-額度序號070110900057於呈核修改為經權內-非屬聯徵負面資訊等-->
		<entry key="J-112-0099_12313_B1001">
			<value>
				update lms.c120s01g set CHKITEM2 = '1' where mainId = '4b1a2a1335504573910bd38aff5d027d';
			</value>
		</entry>
		
		<!--J-112-0213_10173_B1001 修改主借人D120449552及其保證人Q221939128-額度序號022111200049為非屬聯徵負面資訊, 可於經權內敘做-->
		<entry key="J-112-0213_12313_B1001">
			<value>
				update lms.C120S01G SET CHKITEM2 = '2' where oid in ('5D5DF2A8A9FF4453A44C047607B81040', '62DF3D1AFF2B43898C1580ECCFEE710E')
				update lms.C101S01G SET CHKITEM2 = '2' where oid in ('37148B86BF2211ED8B5DA1440A70FE60', '6BF4A166DFF111EDBE6BBAB10A70FE60')
			</value>
		</entry>

		<entry key="J-112-0223_10702_B1001">
			<!--J-112-0223_10702_B1001 Web e-Loan產出111年5月至112年4月之產品種類57、59、61等青創貸款餘額資料-->
			<value>
				select case when LNF030_Loan_class='57' then '(內政部)青年安心成家前二年零利率' when LNF030_Loan_class='59' then '(財政部)青年安心成家貸款' when LNF030_Loan_class='61' then '青年創業及啟動金貸款' else LNF030_Loan_class end "產品種類"
				,sum(LNF030_1ST_LN_AMT) as "累計撥貸金額",sum(LNF030_LOAN_BAL) AS "貸款餘額"
				From LN.LNF030
				where LNF030_Loan_class !=''
				and LNF030_Loan_class  in('57','58','59','61')
				AND LNF030_CHARC_CODE != '31'
				and LNF030_LOAN_DATE is not null
				and LNF030_LOAN_DATE between '2022-05-01' and '2023-04-30'
				group by LNF030_Loan_class
			</value>
		</entry>
		
		<entry key="J-112-0171_12313_B1001">
			<!--J-112-0171_12313_B1001 歡喜信貸婉拒案件自動發送簡訊失敗顧客清單 -->
			<value>
				SELECT * FROM FINAL TABLE(INSERT INTO COM.BCODETYPE(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(GET_OID(), 'lms9511v01_docType2', 'CLS180R27D', '歡喜信貸婉拒案件自動發送簡訊失敗顧客清單', 'zh_TW', 'J-112-0171_12313_B1001', '', '295', '012313', current timestamp));
			</value>
		</entry>
		
		<entry key="J-112-0219_10173_B1001">
			<!-- J-112-0219_10173_B1001 增加動用審核表-檢附文件-內政部整合住宅方案-已購置之住宅是否已達基本居住水準 -->
			<value>
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900m01c(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				VALUES(GET_OID(), '31', '13506200', '2', '57', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900m01c(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				VALUES(GET_OID(), '38', '13506200', '2', '57', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900m01c(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				VALUES(GET_OID(), '39', '13506200', '2', '57', 'system', current timestamp, 'system', current timestamp));
				
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900m01c(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				VALUES(GET_OID(), '31', '13506300', '2', '57', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900m01c(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				VALUES(GET_OID(), '38', '13506300', '2', '57', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900m01c(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				VALUES(GET_OID(), '39', '13506300', '2', '57', 'system', current timestamp, 'system', current timestamp));
				
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900m01c(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				VALUES(GET_OID(), '31', '14501500', '2', '57', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900m01c(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				VALUES(GET_OID(), '38', '14501500', '2', '57', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900m01c(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				VALUES(GET_OID(), '39', '14501500', '2', '57', 'system', current timestamp, 'system', current timestamp));
				
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900m01c(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				VALUES(GET_OID(), '31', '14502000', '2', '57', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900m01c(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				VALUES(GET_OID(), '38', '14502000', '2', '57', 'system', current timestamp, 'system', current timestamp));
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900m01c(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				VALUES(GET_OID(), '39', '14502000', '2', '57', 'system', current timestamp, 'system', current timestamp));
				
				SELECT * FROM FINAL TABLE(INSERT INTO lms.C900S01B(OID, ITEMCODE, ITEMSEQ, ITEMTYPE, ITEMCONTENT, ITEMFORMAT, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				VALUES(GET_OID(), '57', '57', '2', '「內政部整合住宅方案(產品代碼38，39)，查詢【住宅補貼評點及查核系統】已購置之住宅是否已達基本居住水準」。(請注意，補貼利息核定戶應以申請人為借款人，如以配偶或直系親屬持有之住宅辦理貸款者，原申請人應向原受理直轄市、縣（市）主管機關辦理申請人變更)', null, 'system', current timestamp, 'system', current timestamp));
			</value>
		</entry>
		
		<entry key="J-112-0233_10173_B1001">
			<!--J-112-0233 修改劉文龍H121463452-039中壢分行-額度序號039111200024為非屬聯徵負面資訊-->
			<value>
				update lms.C101S01G SET CHKITEM2 = '2' where oid = 'A6AAA206EAB611EBB3688E800A70FE5E';
			</value>
		</entry>
		
		<entry key="J-112-0243_11565_B1001">
			<!--J-112-0243 協助提供歡喜信貸明細表隱碼資料-->
			<value>
				SELECT C122A.CUSTID,C122A.OWNBRID,C122A.CREATETIME,C122A.DOCSTATUS,M01A.UPDATETIME, 
				S01A.EDU,S01A.MARRY, S01B.JOBTYPE1 || S01B.JOBTYPE2 || S01B.JOBTITLE, S01B.PAYAMT, S01B.OTHAMT, S01C.DRATE, S01C.YRATE,
				S01C.YFAMAMT, S01R.SCRNUM13,S01R.GRADE1,S01R.GRADE2,S01R.GRADE3,S01R.J10_SCORE
				FROM LMS.C122M01A C122A
				LEFT JOIN LMS.C101M01A M01A ON M01A.CUSTID = C122A.CUSTID AND M01A.OWNBRID = C122A.OWNBRID AND M01A.UPDATETIME >= '2023-01-01'
				LEFT JOIN LMS.C101S01A S01A ON S01A.MAINID = M01A.MAINID
				LEFT JOIN LMS.C101S01B S01B ON S01B.MAINID = M01A.MAINID
				LEFT JOIN LMS.C101S01C S01C ON S01C.MAINID = M01A.MAINID
				LEFT JOIN LMS.C101S01R S01R ON S01R.MAINID = M01A.MAINID
				WHERE C122A.APPLYKIND IN ('C','P') AND C122A.STATFLAG IS NOT NULL AND C122A.DELETEDTIME IS NULL
				AND C122A.APPLYTS BETWEEN '2023-01-01' AND '2023-05-23'
				ORDER BY C122A.OWNBRID
			</value>
		</entry>
		
		<entry key="H-111-0199_12313_B1001">
			<!--H-111-0199 C3憑證用於PLOAN申貸同意本行透過「聯徵中心」查詢「公務機關」財力資料-->
			<!--新增正規表達式，用以比對檔名-->
			<value>
				INSERT INTO COM.BCodeType(oid, codeType, codeValue, CodeDesc, codeOrder, CodeDesc2, CodeDesc3, locale, lastModifyBy, lastModifyTime) VALUES(GET_OID(),'autoRecvXmlJCIC', 'egres','.*\.egres',1,'','','zh_TW','012313',current timestamp);
			</value>
		</entry>
		
		<entry key="J-112-0364_12455_B1001">
			<!--J-112-0364 e-Loan授信系統貸後管理追蹤項目之提示文字請配合法規修改-->
			<value>
				select * from final table ( update com.bcodetype  set CODEDESC='檢附「興建計劃書」之工業區土地抵押貸款案件至少每六個月實地勘查乙次。'  where   CODETYPE='postLoan_wording' and codevalue='1'  and  LOCALE='en'      );
                select * from final table ( update com.bcodetype  set CODEDESC='检附「兴建计划书」之工业区土地抵押贷款案件至少每六个月实地勘查乙次。'  where   CODETYPE='postLoan_wording' and codevalue='1'  and  LOCALE='zh_CN'   );
                select * from final table ( update com.bcodetype  set CODEDESC='檢附「興建計劃書」之工業區土地抵押貸款案件至少每六個月實地勘查乙次。'  where   CODETYPE='postLoan_wording' and codevalue='1'  and  LOCALE='zh_TW'   );
			</value>
		</entry>
		
		<entry key="J-112-0134_12313_B1001">
			<!--J-112-0134 為加強本行異常通報案件之管理機制及符合「授信戶往來異常通報作業須知」第七條關係戶通報，eloan企金、個金簽報書「授信戶往來異常通報表」之「陳報及說明事項」增加關係戶定義提醒及清單供檢視。-->
			<!--新增關係CODETYPE：關係人、關係企業、主從債務人-->
			<value>
				select * from final table (INSERT INTO COM.BCodeType(oid, codeType, codeValue, CodeDesc, codeOrder, CodeDesc2, CodeDesc3, locale, lastModifyBy, lastModifyTime) VALUES(GET_OID(),'RelationPartyAndJointDebtor', '1','二親等以內血親',1,'關係人','','zh_TW','012313',current timestamp));
				select * from final table (INSERT INTO COM.BCodeType(oid, codeType, codeValue, CodeDesc, codeOrder, CodeDesc2, CodeDesc3, locale, lastModifyBy, lastModifyTime) VALUES(GET_OID(),'RelationPartyAndJointDebtor', '2','本人擔任負責人之企業',2,'關係人','','zh_TW','012313',current timestamp));
				select * from final table (INSERT INTO COM.BCodeType(oid, codeType, codeValue, CodeDesc, codeOrder, CodeDesc2, CodeDesc3, locale, lastModifyBy, lastModifyTime) VALUES(GET_OID(),'RelationPartyAndJointDebtor', '3','配偶擔任負責人之企業',3,'關係人','','zh_TW','012313',current timestamp));
				select * from final table (INSERT INTO COM.BCodeType(oid, codeType, codeValue, CodeDesc, codeOrder, CodeDesc2, CodeDesc3, locale, lastModifyBy, lastModifyTime) VALUES(GET_OID(),'RelationPartyAndJointDebtor', '4','配偶',4,'關係人','','zh_TW','012313',current timestamp));
				select * from final table (INSERT INTO COM.BCodeType(oid, codeType, codeValue, CodeDesc, codeOrder, CodeDesc2, CodeDesc3, locale, lastModifyBy, lastModifyTime) VALUES(GET_OID(),'RelationPartyAndJointDebtor', 'R01','有控制與從屬關係',5,'關係企業','','zh_TW','012313',current timestamp));
				select * from final table (INSERT INTO COM.BCodeType(oid, codeType, codeValue, CodeDesc, codeOrder, CodeDesc2, CodeDesc3, locale, lastModifyBy, lastModifyTime) VALUES(GET_OID(),'RelationPartyAndJointDebtor', 'R02','相互投資關係',6,'關係企業','','zh_TW','012313',current timestamp));
				select * from final table (INSERT INTO COM.BCodeType(oid, codeType, codeValue, CodeDesc, codeOrder, CodeDesc2, CodeDesc3, locale, lastModifyBy, lastModifyTime) VALUES(GET_OID(),'RelationPartyAndJointDebtor', 'R03','董事長或總經理與他公司之董事長或總經理為同一人或具有配偶之關係',7,'關係企業','','zh_TW','012313',current timestamp));
				select * from final table (INSERT INTO COM.BCodeType(oid, codeType, codeValue, CodeDesc, codeOrder, CodeDesc2, CodeDesc3, locale, lastModifyBy, lastModifyTime) VALUES(GET_OID(),'RelationPartyAndJointDebtor', 'C','共同借款人',8,'主從債務關係','','zh_TW','012313',current timestamp));
				select * from final table (INSERT INTO COM.BCodeType(oid, codeType, codeValue, CodeDesc, codeOrder, CodeDesc2, CodeDesc3, locale, lastModifyBy, lastModifyTime) VALUES(GET_OID(),'RelationPartyAndJointDebtor', 'D','共同發票人',9,'主從債務關係','','zh_TW','012313',current timestamp));
				select * from final table (INSERT INTO COM.BCodeType(oid, codeType, codeValue, CodeDesc, codeOrder, CodeDesc2, CodeDesc3, locale, lastModifyBy, lastModifyTime) VALUES(GET_OID(),'RelationPartyAndJointDebtor', 'E','票據債務人(指)金融交易之擔保背書',10,'主從債務關係','','zh_TW','012313',current timestamp));
				select * from final table (INSERT INTO COM.BCodeType(oid, codeType, codeValue, CodeDesc, codeOrder, CodeDesc2, CodeDesc3, locale, lastModifyBy, lastModifyTime) VALUES(GET_OID(),'RelationPartyAndJointDebtor', 'G','連帶保證人，擔保品提供人兼連帶保證人',11,'主從債務關係','','zh_TW','012313',current timestamp));
				select * from final table (INSERT INTO COM.BCodeType(oid, codeType, codeValue, CodeDesc, codeOrder, CodeDesc2, CodeDesc3, locale, lastModifyBy, lastModifyTime) VALUES(GET_OID(),'RelationPartyAndJointDebtor', 'L','連帶借款人，連帶債務人，擔保品提供人兼連帶債務人',12,'主從債務關係','','zh_TW','012313',current timestamp));
				select * from final table (INSERT INTO COM.BCodeType(oid, codeType, codeValue, CodeDesc, codeOrder, CodeDesc2, CodeDesc3, locale, lastModifyBy, lastModifyTime) VALUES(GET_OID(),'RelationPartyAndJointDebtor', 'N','一般保證人',13,'主從債務關係','','zh_TW','012313',current timestamp));
				select * from final table (INSERT INTO COM.BCodeType(oid, codeType, codeValue, CodeDesc, codeOrder, CodeDesc2, CodeDesc3, locale, lastModifyBy, lastModifyTime) VALUES(GET_OID(),'RelationPartyAndJointDebtor', 'S','擔保品提供人',14,'主從債務關係','','zh_TW','012313',current timestamp));
			</value>
		</entry>
		
		<entry key="J-112-0372_11850_B1001">
			<!--J-112-0372 e-Loan消金授信管理系統簽報書之查核事項中有關行家理財貸款-短期擔保放款之貸款金額項目的敘述由「以本行定存單或可轉讓定存單擔保者，貸放金額在NT$1億元以內，除本行定存單或可轉讓定存單之擔保外，貸放金額在NT$2,000萬元以內」修改為「以本行定存單或可轉讓定存單擔保者，貸放金額在NT$1億元以內，除本行定存單或可轉讓定存單外且非以不動產擔保者，貸放金額在NT$2,000萬元以內-->
			<value>
				INSERT INTO lms.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME)
 				VALUES(GET_OID(), '234', 234, '4', '以本行定存單或可轉讓定存單擔保者，貸放金額在NT$1億元以內，除本行定存單或可轉讓定存單外且非以不動產擔保者，貸放金額在NT$2,000萬元以內', '', '超出限額應專案陳報總處核定', 'system',  current timestamp, 'system', current timestamp);

				update lms.C900M01C set SOUSECODE = '234'   where PRODKIND = '02' and SOUSECODE = '46' and TYPE = '1';
			</value>
		</entry>
		
		<entry key="J-112-0367_12313_B1001">
			<!--J-112-0367 因應金檢查核需求，申請新增「個金徵信」及「歡喜信貸案件明細表」欄位-->
			<value>
				ALTER TABLE LMS.C101S01G ADD ISKIND CHAR(2);
				ALTER TABLE LMS.C101S01Q ADD ISKIND CHAR(2);
				ALTER TABLE LMS.C101S01R ADD ISKIND CHAR(2);
				COMMENT ON LMS.C101S01G (ISKIND IS '擔保品類別');
				COMMENT ON LMS.C101S01Q (ISKIND IS '擔保品類別');
				COMMENT ON LMS.C101S01R (ISKIND IS '擔保品類別');
				
				ALTER TABLE LMS.C120S01G ADD ISKIND CHAR(2);
				ALTER TABLE LMS.C120S01Q ADD ISKIND CHAR(2);
				ALTER TABLE LMS.C120S01R ADD ISKIND CHAR(2);
				COMMENT ON LMS.C120S01G (ISKIND IS '擔保品類別');
				COMMENT ON LMS.C120S01Q (ISKIND IS '擔保品類別');
				COMMENT ON LMS.C120S01R (ISKIND IS '擔保品類別');
			</value>
		</entry>
		
		<entry key="J-112-0385_11850_B1001">
			<!-- J-112-0385 配合財政部青年安心成家方案，更新eloan查核項目-->
			<value>
				<![CDATA[
				INSERT INTO LMS.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME)
  				VALUES(GET_OID(), '235', 71, '6', '貸款年限<=20年或<=30年(屬銀行法第12條之1規範之自用住宅貸款、非首購但聯徵中心主、從債務均無房貸且符合「個人負債比率貸放控管原則」免減成之對象)，且擔保品屋齡<=(不動產耐用年限+15年)。符合109年10月23日兆銀消金字第1090000267號函條件、112年8月1日起青年安心成家貸款及逾青安貸款搭配方案年限<=40年。若本案非屬上列專案，應確認貸款年限符合該專案貸款年限規定。', '', '不動產耐用年限加強磚造35年鋼筋混凝土50年。', 'sys   ', '2020-02-14 16:23:28.64404', 'sys   ', current timestamp);

				update lms.C900M01C set SOUSECODE = '235' where  SOUSECODE = '188';
				]]> 
			</value>
		</entry>
		
		<entry key="J-112-0403_10173_B1001">
			<!-- J-112-0403 個案解控N221734121黃秋萍-032員林分行-受管控地政士黑名單 -->
			<value>
				<![CDATA[
					update lms.c900m01h set CTLFLAG = '0' where oid = '790DF210329F11EE84C13DF20A70FE61'
				]]>
			</value>
		</entry>
		<entry key="J-112-0407_11850_B1001">
			<!-- J-112-0407 調整eloan查核事項內容，如附件-->
			<value>
				<![CDATA[
				INSERT INTO lms.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				 VALUES(GET_OID(), '236', 2, '1', '屬自房屋所有權登記之日起算三年內或轉貸本行前於他行已辦理「購置住宅貸款」經提出書面證明者', '', '如否，應依實際貸款用途選擇申辦之貸款種類(例如：行家理財貸款、房屋修繕貸款…等)。', 'system', current timestamp, 'system', current timestamp);
				INSERT INTO lms.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				 VALUES(GET_OID(), '237', 161, '3', '個人負債比率＞60%，且不符合免減成條件者。', '', '如是，應按本行房貸「核貸成數彙總表」之核貸成數減成。', 'system', current timestamp, 'system', current timestamp);
				
				update lms.C900M01C set SOUSECODE = '236' where SOUSECODE = '2' and TYPE = '1'
				update lms.C900M01C set SOUSECODE = '237' where SOUSECODE = '161' and TYPE = '1'
				]]> 
			</value>
		</entry>
		
		<entry key="J-113-0173_11879_B1001">
			<!-- J-113-0173 E-LOAN授信管理系統，產品種類67及70以房養老-查核事項修改-->
			<value>
				<![CDATA[
				INSERT INTO LMS.C900S01A (OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				 VALUES (db2inst1.GET_OID(), '245', '147', '2', '為具完全行為能力之中華民國國民，年齡滿60歲以上，加計貸款年限≧90歲','', '如否，應專案陳報總處核定', 'system', current timestamp, 'system', current timestamp );
				INSERT INTO LMS.C900S01A (OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				 VALUES (db2inst1.GET_OID(), '246', '148', '2', '借款人是否有聯徵繳款資訊限制、使用現金卡或信用卡循環信用','', '如是，依本行相關規定辦理或專案陳報總處核定', 'system', current timestamp, 'system', current timestamp );
				INSERT INTO LMS.C900S01A (OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				 VALUES (db2inst1.GET_OID(), '247', '158', '6', '貸款期間為三十年以內，最短為10年，且擔保品不逾法定耐用年限，屋齡加計貸款年限≦70','', '不動產耐用年限加強磚造35年，鋼筋混凝土50年如否，應專案陳報總處核定', 'system', current timestamp, 'system', current timestamp );
				INSERT INTO LMS.C900S01A (OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME)
				 VALUES (db2inst1.GET_OID(), '248', '71', '7', '建物謄本登記用途為住、商、公寓或含「住」字樣，經勘查其實際用途為住宅使用，且不得為工業住宅或15坪以下套房，且擔保品座落區域應為本行「個人房屋貸款不動產分區」B區以上','', '如否，應專案陳報總處核定', 'system', current timestamp, 'system', current timestamp );
				UPDATE LMS.C900M01C SET SOUSECODE = '245' where TYPE='1' AND PRODKIND in ('67','70') AND SOUSECODE = '162';
				UPDATE LMS.C900M01C SET SOUSECODE = '246' where TYPE='1' AND PRODKIND in ('67','70') AND SOUSECODE = '163';
				UPDATE LMS.C900M01C SET SOUSECODE = '247' where TYPE='1' AND PRODKIND in ('67','70') AND SOUSECODE = '158';
				UPDATE LMS.C900M01C SET SOUSECODE = '248' where TYPE='1' AND PRODKIND in ('67','70') AND SOUSECODE = '159';
				]]> 
			</value>
		</entry>
		
		<entry key="J-113-0198_11879_B1001">
			<!-- J-113-0198 配合 1.113年4月30日兆銀總授審字第1130018985號函，購置/修繕房屋貸款及提供房地產為擔保之貸款年限由20年修訂為30年。 2.112年8月1日起青年安心成家貸款及逾青安貸款搭配方案貸款年限最長得為40年 。 修改相關查核項目-->
			<value>
				<![CDATA[
				INSERT INTO LMS.C900S01A (OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME) 
				 VALUES (db2inst1.GET_OID(), '250', 74, '6', '貸款期間為30年以內，且擔保品屋齡≦(不動產耐用年限+15年)。','', '不動產耐用年限加強磚造35年，鋼筋混凝土50年如否，應專案陳報總處核定', 'system', current timestamp, 'system', current timestamp );
                INSERT INTO LMS.C900S01A (OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME) 
				 VALUES (db2inst1.GET_OID(), '251', 71, '6', '貸款年限<=30年且擔保品屋齡<=(不動產耐用年限+15年)。符合109年10月23日兆銀消金字第1090000267號函條件、112年8月1日起青年安心成家貸款及逾青安貸款搭配方案年限<=40年。若本案非屬上述專案，應確認貸款年限符合該專案貸款年限規定。','', '不動產耐用年限加強磚造35年鋼筋混凝土50年。', 'system', current timestamp, 'system', current timestamp );
				INSERT INTO LMS.C900S01A (OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME) 
				 VALUES (db2inst1.GET_OID(), '252', 71, '6', '貸款年限<=30年且擔保品屋齡<=(不動產耐用年限+15年)。符合109年10月23日兆銀消金字第1090000267號函條件、112年8月1日起青年安心成家貸款及逾青安貸款搭配方案年限<=40年。若本案非屬上述專案，應確認貸款年限符合該專案貸款年限規定。若屬投資型之房貸、借款人為外國人購屋貸款者，最長20年。','', '不動產耐用年限加強磚造35年鋼筋混凝土50年。', 'system', current timestamp, 'system', current timestamp );

				UPDATE LMS.C900M01C SET SOUSECODE = '250' where TYPE='1' AND PRODKIND in ('03','30','31') AND SOUSECODE = '74';
				UPDATE LMS.C900M01C SET SOUSECODE = '251' where TYPE='1' AND PRODKIND in ('57','59') AND SOUSECODE = '235';
				UPDATE LMS.C900M01C SET SOUSECODE = '252' where TYPE='1' AND PRODKIND in ('31') AND SOUSECODE = '244';
				]]> 
			</value>
		</entry>
		
		<entry key="J-113-0308_11879_B1001">
			<!-- J-113-0308 配合本行行家理財貸款暨綜合理財房屋貸款作業須知修改，請修改E-LOAN簽報書內短期擔保放款之查核事項內容。 -->
			<value>
				<![CDATA[
				INSERT INTO LMS.C900S01A (OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME) 
				 VALUES (db2inst1.GET_OID(), '253', 88, '8', '非自用住宅貸款（含以非房地產為擔保者）之申貸金額為新台幣80萬元以上','', '如是，如需徵提保證人以一般保證人為原則；惟確認非屬銀行法第12條之1規範之貸款，為加強擔保，得徵提連保人', 'system', current timestamp, 'system', current timestamp);

				UPDATE LMS.C900M01C SET SOUSECODE = '253' where TYPE='1' AND PRODKIND in ('02') AND SOUSECODE = '88';
				DELETE FROM LMS.C900M01C where TYPE='1' AND PRODKIND in ('02') AND SOUSECODE = '92';
				DELETE FROM LMS.C900M01C where TYPE='1' AND PRODKIND in ('02') AND SOUSECODE = '135';
				]]> 
			</value>
		</entry>
		
		<entry key="J-113-0501_11879_B1001">
			<!-- J-113-0501 配合113年9月3日兆銀消金字第1130000205號函，修訂「消金授信個人負債比率控管規定」修改產品種類03及10之查核項目。 -->
			<value>
				<![CDATA[
				UPDATE LMS.C900M01C SET SOUSECODE = '254' where TYPE='1' AND PRODKIND in ('03','10') AND SOUSECODE = '237';
				]]> 
			</value>
		</entry>
		
		<entry key="J-111-0613_10173_B1002">
			<!-- J-111-0613_10173_B1002 配合分行修改新店行授信戶莊富傑(F126440760)額度明細表及額度批覆表-核貸成數為79.94% -->
			<value>
				<![CDATA[
					update lms.l140m01o set PAYPERCENT = 79.94 where oid = '5D1EA610DF5411EDBCF0D4550A70FE60'
					update lms.l140m01a set APPROVEDPERCENT = 79.94 where oid = 'BC8C12A0DF4411EDBCF0D4550A70FE60'
					update lms.l140m01o set PAYPERCENT = 79.94 where oid = '197B8E20F6BB43A69971C6E9B9A68ACD'
					update lms.l140m01a set APPROVEDPERCENT = 79.94 where oid = '5CC5B830EE1C11ED91CAFE5A0A70FE61'
				]]>
			</value>
		</entry>

		<entry key="J-113-0193_09301_B1001">
			<value>
				<![CDATA[
					select * from final table (UPDATE COM.BSYSPARAM SET PARAMVALUE = '2024-10-01' WHERE PARAM = 'LMS_CHK_L120S01Q_VER05');
				]]>
			</value>
		</entry>

		<entry key="J-113-0348_09301_B1001">
			<value>
				<![CDATA[
					SELECT CASEBRID,BRNAME,custid,CUSTNAME,cntrno,lnsubject,ORGPROPERTY,LNTYPE,CURRENTAPPLYCURR,CURRENTAPPLYAMT ,rateDscr,nuseMemo,signDate,enddate
					FROM
					(
						SELECT X2.*,S3.nuseMemo,S3.signDate
						FROM
						(
							SELECT ROW_NUMBER() OVER (PARTITION BY X1.CNTRNO ORDER BY X1.ENDDATE DESC ) AS SEQ,x1.*
							FROM
							(
								SELECT t3.custid,t3.dupno,t3.CUSTNAME, LMS.L120M01A.CASEBRID,COM.BELSBRN.BRNAME,t3.CNTRNO,t3.LNSUBJECT,t3.CURRENTAPPLYCURR,t3.CURRENTAPPLYAMT,
									t3.useDeadline,t3.DESP1, LMS.L120M01A.ENDDATE,t3.MAINID AS CNTRMAINID,tb.itemdscr as rateDscr,custClass,'|'||T3.PROPERTY||'|' AS PROPERTY,
									T3.PROPERTY AS ORGPROPERTY, busCode,LMS.L120M01A.MAINID AS RPTMAINID,isBuy,lntype,residential,REUSE,IS722FLAG
								FROM lms.l140m01a AS t3
								LEFT OUTER JOIN LMS.L120M01C ON LMS.L120M01C.REFMAINID = t3.MAINID
								LEFT OUTER JOIN LMS.L120M01A ON LMS.L120M01C.MAINID = LMS.L120M01A.MAINID
								LEFT OUTER JOIN COM.BELSBRN ON LMS.L120M01A.CASEBRID =  COM.BELSBRN.BRNO
								LEFT OUTER JOIN LMS.L120S01B AS ta ON LMS.L120M01A.mainid = ta.mainid and t3.custId = ta.custId and t3.dupNo = ta.dupNo
								LEFT OUTER JOIN (SELECT * FROM LMS.L140M01B WHERE ITEMTYPE = '2') AS tb ON t3.mainid = tb.mainid
								WHERE LMS.L120M01A.DOCSTATUS = '05O' AND lms.l120m01a.ENDDATE IS NOT NULL AND lms.l120m01a.DELETEDTIME IS NULL AND
									lms.l120m01a.ENDDATE between '2024-01-01' AND '2024-08-31' AND t3.DOCSTATUS ='030' ANDt3.DELETEDTIME IS NULL AND ta.mainid IS NOT NULL
							) AS X1
							WHERE PROPERTY LIKE '%|1|%' OR PROPERTY LIKE '%|5|%'
						) AS X2
						LEFT OUTER JOIN
						(
							SELECT * FROM
							(
								SELECT ROW_NUMBER() OVER (PARTITION BY S1.UID,S1.CNTRNO ORDER BY S1.approveTime DESC ) AS SEQ,S1.UID,S1.cntrNo,S1.nuseMemo,S1.signDate
								FROM
								(
									SELECT LMS.L230M01A.UID,LMS.L230M01A.approveTime, LMS.L230S01A.CNTRNO,LMS.L230S01A.nuseMemo,LMS.L230S01A.signDate
									FROM LMS.L230M01A
									LEFT OUTER JOIN LMS.L230S01A ON LMS.L230M01A.MAINID = LMS.L230S01A.MAINID
									WHERE LMS.L230M01A.docStatus = '05O' AND LMS.L230M01A.deletedTime IS NULL
								) AS S1
							) AS S2
							WHERE SEQ = 1
						) AS S3 ON S3.UID= X2.RPTMAINID AND S3.cntrNo = X2.CNTRNO
					) as Z1
					ORDER BY ENDDATE ASC  Fetch First 9999 rows only
				]]>
			</value>
		</entry>

		<entry key="J-112-0505_09301_B1001">
            <value>
                <![CDATA[
					select * from final table (INSERT INTO com.BSYSPARAM(OID, PARAM, PARAMVALUE, PARAMDESC, LASTMODIFYBY, LASTMODIFYTIME) VALUES (GET_OID(),'LMS_CHK_L120S01Q_BY_COLUMN','Y','L120S01Q逐項檢核','system',CURRENT TIMESTAMP));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_D1_02','7','無發生違規事項(免填下題)','zh_TW','','',7,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_D1_02','7','無發生違規事項(免填下題)','zh_CN','','',7,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_D1_02','7','無發生違規事項(免填下題)','en','','',7,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_D2_02','7','發生重大職災、工安意外','zh_TW','','',7,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_D2_02','7','發生重大職災、工安意外','zh_CN','','',7,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_D2_02','7','發生重大職災、工安意外','en','','',7,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_D2_02','8','無涉及危害社會公益活動或事件','zh_TW','','',8,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_D2_02','8','無涉及危害社會公益活動或事件','zh_CN','','',8,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_D2_02','8','無涉及危害社會公益活動或事件','en','','',8,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_D3_02','6','無涉及侵害人權行為或事件','zh_TW','','',6,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_D3_02','6','無涉及侵害人權行為或事件','zh_CN','','',6,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_D3_02','6','無涉及侵害人權行為或事件','en','','',6,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','1','營運所在地屬貪腐高風險之國家','zh_TW','','',1,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','1','營運所在地屬貪腐高風險之國家','zh_CN','','',1,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','1','營運所在地屬貪腐高風險之國家','en','','',1,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','2','所營業務屬賄絡高風險之行業','zh_TW','','',2,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','2','所營業務屬賄絡高風險之行業','zh_CN','','',2,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','2','所營業務屬賄絡高風險之行業','en','','',2,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','3','曾涉賄絡或非法政治獻金','zh_TW','','',3,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','3','曾涉賄絡或非法政治獻金','zh_CN','','',3,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','3','曾涉賄絡或非法政治獻金','en','','',3,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','4','曾涉詐欺情事','zh_TW','','',4,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','4','曾涉詐欺情事','zh_CN','','',4,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','4','曾涉詐欺情事','en','','',4,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','5','其他涉及不誠信之行為或事件','zh_TW','','',5,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','5','其他涉及不誠信之行為或事件','zh_CN','','',5,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','5','其他涉及不誠信之行為或事件','en','','',5,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','6','無涉及不誠信之行為或事項','zh_TW','','',6,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','6','無涉及不誠信之行為或事項','zh_CN','','',6,'system',CURRENT DATE));
                    select * from final table (INSERT INTO COM.BCodeType(OID,CODETYPE,CODEVALUE,CODEDESC,LOCALE,CODEDESC2,CODEDESC3,CODEORDER,LASTMODIFYBY,LASTMODIFYTIME) VALUES (db2inst1.get_oid(),'LMSS07_badFaithItem','6','無涉及不誠信之行為或事項','en','','',6,'system',CURRENT DATE));
				]]>
            </value>
        </entry>
		<entry key="J-113-0180 72-2註記 請開放D03、D04、D05、A03、A0N，供營業單位及授審處可選擇">
			<value>
				<![CDATA[
				UPDATE com.BSYSPARAM SET PARAMVALUE = PARAMVALUE ||',D03,D04,D05,A03,A0N' WHERE PARAM = '722_ESTATE_TYPE';
				]]>
			</value>
		</entry>
		<entry key="J-112-0491_05097_B1001">
			<!-- J-112-0491_05097_B1001 Web e-Loan授信管理系統額度明細表中專案種類新增「進駐園區有我兆」專案  -->
			<value>
				<![CDATA[
					INSERT INTO COM.BCODETYPE(OID, CODETYPE, CODEVALUE, CODEDESC, "LOCALE", CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME)VALUES(db2inst1.GET_OID(), 'lms140_projClass', '21', '21.進駐園區有我兆專案', 'en', '', '', 21, 'system', '2022-12-12 10:49:57.283938');
					INSERT INTO COM.BCODETYPE(OID, CODETYPE, CODEVALUE, CODEDESC, "LOCALE", CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME)VALUES(db2inst1.GET_OID(), 'lms140_projClass', '21', '21.進駐園區有我兆專案', 'zh_CN', '', '', 21, 'system', '2022-12-12 10:49:57.233752');
					INSERT INTO COM.BCODETYPE(OID, CODETYPE, CODEVALUE, CODEDESC, "LOCALE", CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME)VALUES(db2inst1.GET_OID(), 'lms140_projClass', '21', '21.進駐園區有我兆專案', 'zh_TW', '', '', 21, 'system', '2022-12-12 10:49:57.155124');
				]]>
			</value>
		</entry>
		<!-- J-112-0498 配合本行「辦理教育部補助留學生就學貸款作業須知」修訂，修改簽報書之查核事項及ALOAN、ELOAN系統檢核。 -->
		<entry key="J-112-0491_05097_B1001">
			<value>
				<![CDATA[
					INSERT INTO lms.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(db2inst1.GET_OID(), '238', 145, '6', '是否符合攻讀博士學位最長16年(含寬限期最長5年)，攻讀碩士最長12年(含寬限期最長3年)。', '', '', 'system', current timestamp, 'system', current timestamp);
					INSERT INTO lms.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(db2inst1.GET_OID(), '239', 57, '4', '攻讀博士學位在NT$240萬以內，碩士學位在NT$120萬以內。', '', '', 'system', current timestamp, 'system', current timestamp);
					update lms.C900M01C set SOUSECODE= '239'  where PRODKIND = '36' and SOUSECODE = '57'
					update lms.C900M01C set SOUSECODE= '238'  where PRODKIND = '36' and SOUSECODE = '145'
				]]>
			</value>
		</entry>
		<entry key="J-112-0527_10702_B1001">
			<value>
				<![CDATA[
					update lms.L140M01A set desp1='12' where mainid in('9f304955dfa8446ab51442268c1d5d48','6e06523814b74c93abb8e212c9d8b4b4','bac754cdd6cf4708aa58191c061494db','fcf96c6ae7a045f2bba45faf9d2eeb2b','1b937f4089594ce1acd8a2c74abad030','ca63c753ae25447482eab7091cbb1590','0597eacd3f6244b981f5b8219eac7d7c','eb2793b182bd4568a771013a2bd89aeb','4be3d23306154d3eb1086f3deab6c15d','78055f09823040b997c63eff79db2452','1b1964cdb5b3440d81f682205069ca81','7ade819637f04723bc7d04d43566d6a7','628bfb22fba7461a9cefd10895cfc8be','5206dcc404c5460ba02aedeaafe443b1','3e2f9888251f427e862d02861a3acfcb','18b2360a991c4dfc831faf045c36be07','cefc2757c8ca4f368b3670b045198bea','85fdafe7af0b4c9498e4a4361b3874f0','b551c18750a34e62a38b27eb6b060813','ec12199640e0475b9c2f87f145f80eff','aa8d800bbee54b529bad9fc914c6ee44','8e408e14afa847abb00caa0ff743c27d','98b3f59b329e4eaf8a6624884656d3e5','fc6ca839398149e598c4290060d3eb9c','608871a956274be1b509ecbb37469ae3','699f3d9b9c9e4a54b497068a2868bccd','0b6dda63cf6d451f882b07b9e2394abd','bead25bbe83047328ef6acdc32d236c1','c14149a26af44979a10e9450efcf73e7','27395467341742a5b5b73c54a6d23a9d','7a3777bd6c164313aebe30205172f8ba','df93d1f8b4594295a5a084f5e7f9edeb','b207e03968114b6f9b94890e1ee2e350','fa5bb8512f6f4951aec63bdcbf3de097','facbb59991bb4f9fa350de567f863525','f08603dc202b4c3eae9bc6c35456073e','423238ee58914f37a854b7fd88723ebe','f7b1c9384b30443a819f4192c1241982','988a2639c9bf4c0381d8864fe5011141','4296a379aa53470b83c97bd23929d9f9','4a436cbab546419da9a68293d6e81c33','980f0dd597d943b9b0845467b0b2d96c','58535c68746449daa399e9924d6290b9','51b44094f64d4777b57f49d849e09ea3','9d795f5f810a401ab75325b1242490e5')
					update lms.L140M01A set desp1='2023-05-15 ~ 2024-05-14',USEDEADLINE='1' where mainid in('8fd6f8df327c456fbe2bb852e2cac69d','26edc5c63cb8473cab9c368755b5ea54','2941d449746a40b3a03f5491ebfbf559','2ee0e71bef4a4f32b98f2af6cccad6fe')
				]]>
			</value>
		</entry>
		
		<!-- J-112-0512_10173_B1001 修改央行房貸註記-購地貸款興建房屋及購買土地及其地上建物(建物已鮮少價值)名稱 -->
		<entry key="J-112-0512_10173_B1001">
			<value>
				<![CDATA[
					SELECT * FROM FINAL TABLE(update com.bcodetype set codedesc = '購置土地含興建建物' where CODETYPE = 'L140M01M_purposeType' and codevalue = '1');
					SELECT * FROM FINAL TABLE(update com.bcodetype set codedesc = '購買不需興建之土地及其地上建物(建物已鮮少價值)' where CODETYPE = 'L140M01M_purposeType' and codevalue = '4');
				]]>
			</value>
		</entry>
		
		<!-- J-112-0535_12473_B1001 配合中期循環規定修訂，調整eloan查核事項檢核項目 -->
		<entry key="J-112-0535_12473_B1001">
			<value>
				<![CDATA[
					select * from final table (INSERT INTO lms.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(db2inst1.GET_OID(), '240', 152, '6', '貸款期間最長不得超過五年，且擔保品屋齡≦(不動產耐用年限+15年)', '', '不動產耐用年限加強磚造35年，鋼筋混凝土50年，如否，應專案陳報總處核定', 'system', current timestamp, 'system', current timestamp));
					select * from final table (INSERT INTO lms.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(db2inst1.GET_OID(), '241', 72, '7', '擔保品若為不動產，其建物產權完整且持有土地持份，另不得為素地或不動產謄本登記用途為廠房者', '', '如否，應專案陳報總處核定', 'system', current timestamp, 'system', current timestamp));
					select * from final table (INSERT INTO lms.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(db2inst1.GET_OID(), '242', 242, '7', '擔保品若為本行定存單，應依規設定質權予本行且以出質人辦妥自動展期之定存單為限。', '', '若擔保品放款值幣別與本案貸款幣別不一致時，放款值應予折減，最高以9.5成為限。', 'system', current timestamp, 'system', current timestamp));
					select * from final table (INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(db2inst1.GET_OID(), '68', '13500100', '1', '242', 'system', current timestamp, 'system', current timestamp));
					select * from final table (INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(db2inst1.GET_OID(), '68', '13500200', '1', '242', 'system', current timestamp, 'system', current timestamp));
					select * from final table (update lms.C900M01C set SOUSECODE = '240', UPDATETIME = current timestamp where PRODKIND = '68' and SOUSECODE = '152');
					select * from final table (update lms.C900M01C set SOUSECODE = '241', UPDATETIME = current timestamp where PRODKIND = '68' and SOUSECODE = '153');
				]]>
			</value>
		</entry>

		<!-- J-112-0575_10702_B1001 Web e-Loan提供綠建築歷史撥款案件之開辦費相關資訊 -->
		<entry key="J-112-0575_10702_B1001">
			<value>
				<![CDATA[
					select l140.cntrno,sum(feeAmt) from lms.l140m01a l140
					inner join lms.l120m01c c on l140.mainid=c.refmainid and ITEMTYPE=1
					left outer join lms.l140s02a S02A  on L140.mainId=S02A.mainId
					left join lms.l140m01r r on c.mainid=r.mainid
					where l140.deletedTime is null and S02A.property='1'  and r.FeeNO in('01','02')
					and l140.cntrno in('002111000616','004110900653','004111100349','004111100350','005111000088','005111000089','005111000102','005111000404','005111000406','005111000407','010111200098','010111200099','010111200291','010111200295','012111000420','012111200038','012111200120','012111200121','012111200147','014111000611','014111000804','014111000805','014111000866','014111100316','015111000195','015111000337','015111000338','015111000366','015111000371','015111000372','015111000373','015111000374','015111000375','015111000379','015111000380','015111000381','015111000383','015111000384','015111000388','015111000389','015111000391','015111000392','015111000393','015111000396','015111000397','015111000398','015111000401','015111000403','015111000404','015111000405','015111000409','015111000410','015111000411','015111000412','015111000413','015111000415','015111000416','015111000417','015111000418','015111000420','015111000421','015111000423','015111000424','015111000425','015111000428','015111000430','015111000431','015111000432','015111000433','015111000435','015111000438','015111000446','015111000448','015111000455','015111000456','015111000462','015111000464','015111000466','015111000481','015111000483','015111000485','015111000486','015111000487','015111000492','015111000493','015111000494','015111000495','015111000498','015111000499','015111000504','015111000507','015111000510','015111000511','015111000521','015111000522','015111000528','015111000529','015111000531','015111000534','015111000538','015111000539','015111000543','015111000544','015111000546','015111000547','015111000548','015111000550','015111000552','015111000553','015111000554','015111000559','015111000564','015111000565','015111000574','015111000577','015111000588','015111000589','015111000599','015111000601','015111000602','015111000604','015111000607','015111000609','015111000610','015111000612','015111000613','015111000614','015111000615','015111000620','015111000626','015111000627','015111000630','015111000634','015111000638','015111000647','015111000648','015111000651','015111000653','015111000656','015111000658','015111000678','015111000681','015111000688','015111000689','015111000693','015111000699','015111000702','015111000703','015111000722','015111100335','015111200022','015111200025','015111200027','015111200036','015111200117','015111200118','015111200299','015111200314','015111200315','017111000326','018111200029','019111000228','019111200079','019111200080','019111200094','020111000397','020111100262','020111200084','022111200133','027111000776','027111000814','029111000256','029111000638','029111000657','029111000721','029111000737','029111000757','029111000768','029111000769','029111000775','029111000777','029111000779','029111000780','029111000781','029111000789','029111000799','029111000802','029111000832','029111000864','029111200038','029111200102','029111200153','029111200156','030111000143','030111000165','030111000180','030111000181','030111000513','030111200103','034111200020','037111200213','039111000075','039111000616','039111000621','039111100125','039111100130','041111200141','042111200003','042111200012','042111200021','043111200083','046111000094','046111000434','046111200114','046111200115','048111200055','051111000260','051111000277','051111000470','051111000574','051111100194','051111100300','051111100301','051111200013','051111200061','051111200084','051111200092','051111200113','051111200118','051111200120','051111200121','051111200153','051111200157','051111200208','051111200233','053111000551','055111000163','055111000298','056111200079','056111200094','056111200112','056111200113','056111200118','057111000107','057111200105','057111200108','058111200155','062111200016','069111000333','069111000355','069111000448','069111000465','069111000788','069111000799','069111000878','069111000920','069111000933','069111001036','069111200079','069111200091','069111200172','069111200173','069111200288','069111200289','070111000310','070111000312','0701110T0194','072111000405','074111000434','074111000477','074111100221','074111200008','074111200037','074111200079','080111000026','080111000031','080111000035','080111000044','080111000045','080111000046','080111000052','080111000101','080111200129','080111200130','201111000008','201111000009','201111100330','201111100367','201111200092','202111000065','202111000086','202111000433','202111200041','202111200049','202111200141','202111200184','203111000370','203111000400','203111000401','203111000402','203111000405','203111000406','203111000409','203111000411','203111000413','203111000414','203111000417','203111000418','203111000419','203111000420','203111000421','203111000422','203111000423','203111000424','203111000426','203111000427','203111000428','203111000430','203111000431','203111000432','203111000433','203111000434','203111000435','203111000436','203111000437','203111000438','203111000439','203111000440','203111000441','203111000442','203111000444','203111000445','203111000446','203111000447','203111000448','203111000449','203111000453','206111000131','206111000492','206111000502','206111000526','206111000594','206111200113','206111200131','206111200215','207111000317','207111000323','207111000333','207111000334','207111000346','207111000362','207111000363','207111000364','207111000365','207111000367','207111000368','207111000369','207111000386','207111000389','207111000415','207111000417','207111000418','207111000420','207111000442','207111000444','207111000448','207111000450','207111000464','207111000471','207111000474','207111000481','207111000483','207111000489','207111000495','207111000498','207111000512','207111000519','207111000521','207111000552','207111000582','207111000683','207111000684','207111000686','207111100344','207111200004','208111000037','208111000038','210111000065','210111000198','210111000199','213111000423','213111000452','213111000453','213111000461','213111000462','213111000467','213111000469','213111000470','213111000475','213111000477','213111000478','213111000479','213111000481','213111000483','214111000501','215111000001','215111200088','215111200089','215111200097','215111200098','216111200027','219111000095','226111100080','226111200054','226111200163','228111200060','228111200061','229111000266','229111000901','229111001435','229111001571','229111202533','231111200069','231111200070','231111200071','231111200146','231111200147','234111100264','234111100265','234111100267','234111200077','234111200078','234111200085','235111000611','235111000644','235111200103','235111200220','235111200249','235111200261','235111200386','235111200389','236111000057','236111000371','236111000372','236111200020','236111200021','236111200022','236111200032','236111200040','236111200041','236111200083','236111200084','236111200086','236111200087','236111200088','236111200092','236111200093','236111200094','236111200098','237111000386','237111200014','237111200083','237111200085','240111000305','240111100140','240111200120','240111200121','240111200122','240111200151','240111200152')
					group by l140.cntrno
				]]>
			</value>
		</entry>

		<!-- J-112-0567_10702_B1001 Web e-Loan新增台電第6次員工消費性貸款設定 -->
		<entry key="J-112-0567_10702_B1001">
			<value>
				<![CDATA[
					select * from final table (insert into com.BCodeType(oid,codeType,codeValue,CodeDesc,codeOrder,CodeDesc2,CodeDesc3,locale,lastModifyBy,lastModifyTime) values(GET_OID(),'grpCntrNo_TPC_321', '918111200196' ,'台電第6次員工消費性貸款',60,'','','zh_TW','system',current timestamp));
				]]>
			</value>
		</entry>
		
		<!-- J-112-0583_11850_B1001 修改E-LOAN授信系統-整批房貸400億元成長專案 -->
		<entry key="J-112-0567_10702_B1001">
			<value>
				<![CDATA[
					update com.BCODETYPE set CODEDESC = '整批房貸400億元成長方案' where CODETYPE = 'L140M01Y_refType_docCode5' and CODEVALUE='GRP_L140S02F_ratePlan_24';
				]]>
			</value>
		</entry>

		<!-- J-112-0565 因應分析需求，擬於歡喜信貸進件管理作業檔(DW_ELOAN_APPLY)表格增加拒絕碼欄位 -->
		<entry key="J-112-0565_12473_B1001">
			<value>
				<![CDATA[
					ALTER TABLE DWADM.DW_ELOAN_APPLY ADD REJECTCODE VARCHAR(100);
					ALTER TABLE DWADM.DW_ELOAN_APPLY ADD CNTRNO CHAR(12);
					COMMENT ON COLUMN DWADM.DW_ELOAN_APPLY.REJECTCODE IS '拒絕碼';
					COMMENT ON COLUMN DWADM.DW_ELOAN_APPLY.CNTRNO IS '額度序號';
				]]>
			</value>
		</entry>
		
		<!-- H-111-0199 消金iXML授權書上傳聯徵，壓縮檔名eLoan(數字開頭)與iLoan(英文開頭)做出區隔 -->
		<!-- 修改FTP Server諸檔案過濾規則，改成：檔名第11個字元為非英文字母，且檔名尾部為egres -->
		<entry key="J-112-0565_12473_B1001">
			<value>
				<![CDATA[
					UPDATE COM.BCODETYPE SET CODEDESC = '^.{10}[^a-zA-Z].*\.egres' WHERE CODETYPE = 'autoRecvXmlJCIC';
				]]>
			</value>
		</entry>

		<!-- J-112-0568 為爭攬企業戶購置廠辦整批分戶貸款商機，請於企金授信簽報系統，新增專案種類-22.辦理企業戶購置廠辦整批分戶貸款及相關檢核功能。-->
		<entry key="J-112-0568_12313_B1001">
			<value>
				<![CDATA[
					INSERT INTO COM.BCODETYPE(OID, CODETYPE, CODEVALUE, CODEDESC, "LOCALE", CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(db2inst1.GET_OID(), 'lms140_projClass', '22', '22.辦理企業戶購置廠辦整批分戶貸款', 'en', '', '', 22, 'system', current timestamp);
					INSERT INTO COM.BCODETYPE(OID, CODETYPE, CODEVALUE, CODEDESC, "LOCALE", CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(db2inst1.GET_OID(), 'lms140_projClass', '22', '22.辦理企業戶購置廠辦整批分戶貸款', 'zh_CN', '', '', 22, 'system', current timestamp);
					INSERT INTO COM.BCODETYPE(OID, CODETYPE, CODEVALUE, CODEDESC, "LOCALE", CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME) VALUES(db2inst1.GET_OID(), 'lms140_projClass', '22', '22.辦理企業戶購置廠辦整批分戶貸款', 'zh_TW', '', '', 22, 'system', current timestamp);
					ALTER TABLE LMS.L140M01A ADD ISINFORMMBRCHUSEDAMT CHAR(1);
					COMMENT ON LMS.L140M01A (ISINFORMMBRCHUSEDAMT IS '是否已向管理行(母戶簽報行)通報占用貸款額度');
				]]>
			</value>
		</entry>

		<!-- J-112-0581 修改薛文隆A127668507-207桃興分行-額度序號207111200484為非屬聯徵負面資訊 -->
		<entry key="J-112-0567_10702_B1001">
			<value>
				<![CDATA[
					update lms.C120S01G set CHKITEM2 = '2' where oid = '6DE8F3F831484DFB9755241F1DBE831C';
					update lms.C120S01Q set CHKITEM2 = '2' where oid = '540CB19560A74445BCCC80984E5B9CD9';
				]]>
			</value>
		</entry>
		<!-- J-113-0085_11850_B1001  配合「歡喜房貸方案」一覽表修訂，產品種類31歡喜房貸-查核事項-3及22酌修文字 -->
		<entry key="J-113-0085_11850_B1001">
			<value>
				<![CDATA[
					INSERT INTO lms.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME) VALUES(GET_OID(), '243', 5, '2', '1.為具完全行為能力之中華民國國民，有正當職業、固定收入、或具還款能力貸款。<br>2.購屋貸款之借款人(或共同借款人)如為外國人，應「持有中華民國居留證且具有完全行為能力之完全平等互惠國之外國自然人」。', '', '如否，應專案陳報總處核定', 'system', current timestamp, 'system', current timestamp);
					INSERT INTO lms.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME)VALUES(GET_OID(), '244', 71, '6', '1.貸款年限<=20年或<=30年(屬銀行法第12條之1規範之自用住宅貸款、非首購但聯徵中心主、從債務均無房貸且符合「個人負債比率貸放控管原則」免減成之對象)，且擔保品屋齡<=(不動產耐用年限+15年)。符合109年10月23日兆銀消金字第1090000267號函條件、112年8月1日起青年安心成家貸款及逾青安貸款搭配方案年限<=40年。若本案非屬上列專案，應確認貸款年限符合該專案貸款年限規定。<br>2.如為外國人購屋貸款，最長20年。', '', '不動產耐用年限加強磚造35年鋼筋混凝土50年。', 'sys   ', current timestamp, 'sys   ', current timestamp);
					UPDATE lms.C900M01C SET SOUSECODE = '243' where SOUSECODE = '5' and PRODKIND='31' and TYPE = '1';
					UPDATE lms.C900M01C SET SOUSECODE = '244' where SOUSECODE = '235' and PRODKIND='31' and TYPE = '1';
				]]>
			</value>
		</entry>
		<!-- J-113-0134_11850_B1001   於簽報書呈核時提示聯徵資訊應非屬負面資訊) -->
		<entry key="J-113-0134_11850_B1001">
			<value>
				<![CDATA[
					update lms.C120S01G SET CHKITEM2 = '2' where oid in ('D48863D215CF47A89968E3379DBC876A');
				]]>
			</value>
		</entry>
		
		<!-- J-113-0135_12473_B1001   調整E-LOAN簽報書擔保品欄位為徵提股票設質案件之警訊視窗文字 -->
		<entry key="J-113-0135_12473_B1001">
			<value>
				<![CDATA[
					UPDATE COM.BCODETYPE SET CODEDESC='暫停辦理慧洋(集團代號0350)、四維航(集團代號0337)、中信造船(集團代號0277)及義聯(集團代號0216)等四集團旗下之相關企業股票【含上市、上櫃、興櫃及未上市(櫃)】為擔保品或副擔保授信案，如有續約案件則呈報至授信審查處、區域營運中心以上層級核定。' WHERE CODETYPE='LMS_CASEREPORT_SHOW_MSG'
				]]>
			</value>
		</entry>
		
		<!-- J-113-0045_11850_B1001   請協助新增ELOAN系統權限-子系統權限：eloan 直效行銷人員經辦，供本行直效行銷人員使用，相關內容詳如下說明-->
		<entry key="J-113-0045_11850_B1001">
			<value>
				<![CDATA[
					INSERT INTO COM.BSYSPARAM(OID, PARAM, PARAMVALUE, PARAMDESC, LASTMODIFYBY, LASTMODIFYTIME)
					  VALUES(db2inst1.GET_OID(), 'SSO_SYS_DS01', 'LMS,CMS,RPS', 'EL05-SSO角色對映子系統', 'SYSTEM', '2024-04-23 16:47:50.755996');
					INSERT INTO COM.BSYSPARAM(OID, PARAM, PARAMVALUE, PARAMDESC, LASTMODIFYBY, LASTMODIFYTIME)
					  VALUES(db2inst1.GET_OID(), 'SSO_SYS_DS02', 'LMS,CMS,RPS', 'EL05-SSO角色對映子系統', 'SYSTEM', '2024-04-23 16:47:50.767292');
					INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME)
					  VALUES('CBDS01', '3', 'C', 'B', '0', 'DS經辦', NULL, '005097', '2024-04-23 16:55:29.910244', '005097', NULL);
					INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME)
					  VALUES('CBDS02', '3', 'C', 'B', '0', 'DS主管', NULL, '005097', '2024-04-23 16:55:39.989501', '005097', NULL);
					INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME)
					  VALUES('LBDS01', '3', 'L', 'B', '0', 'DS經辦', NULL, '003738', '2024-04-23 13:33:44.763947', '003738', NULL);
					INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME)
					  VALUES('LBDS02', '3', 'L', 'B', '0', 'DS主管', NULL, '003738', '2024-04-23 10:23:12.542534', '005097', NULL);
					INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME)
					  VALUES('RBDS01', '3', 'R', 'B', '0', 'DS經辦', NULL, '005097', '2024-04-23 19:20:15.562595', '005097', NULL);
					INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME)
					  VALUES('RBDS02', '3', 'R', 'B', '0', 'DS主管', NULL, '005097', '2024-04-23 19:20:27.623884', '005097', NULL);

				]]>
			</value>
		</entry>
		
		<!-- J-113-0177_10173_B1001 前准額度與現請額度不一致-且主要性質或次要性質未勾選增額或減額時-增加提醒訊息 -->
		<entry key="J-113-0177_10173_B1001">
			<value>
			</value>
		</entry>
		
		<!-- J-113-0230_11557_B1001 請刪除BIS「非營利國營事業及中央政府行政法人名單」中之07524729 交通部臺灣鐵路管理局(現為國營臺灣鐵路(股)公司) -->
		<entry key="J-113-0230_11557_B1001">
			<value>
				DELETE FROM COM.BCODETYPE WHERE CODETYPE = 'l120s24a_nonPro' AND CODEVALUE = '07524729'
			</value>
		</entry>
		
		<!-- J-113-0269 借款人申請整批房貸額度於2024/2/22經世貿分行核准在案，後因客戶另洽他行申貸，向本行申請取消本案額度申請，為避免佔用整批房貸「400億專案額度」，前於2024/3/7洽資訊處將已核准簽報書退回至分行編制中，惟於2024/6/30之SRMRP256報表中已退回之額度仍出現於該報表，且無法於個金授信系統>已核准授信額度辦理狀態報送作業中取消該額度 -->
		<entry key="J-113-0269_10173_B1001">
			<value>
				update lms.l140m01a set DOCSTATUS = '010' where oid = 'A6DB53D0CD5511EE9863388F0A70FE61'
			</value>
		</entry>
		
		<!-- J-113-0307_11557_B1001 eloan系統簽報有關衍生性金融商品交易業務之遠匯業務時, 買入及賣出交易對應科目之選用應有相對性,以利營業單位正確區分 -->
		<entry key="J-113-0307_11557_B1001">
			<value>
				UPDATE  COM.BCODETYPE SET CODEDESC = '應付遠匯' WHERE CODETYPE = 'lms1405m01_SubItem' AND CODEVALUE = '962' AND LOCALE = 'zh_TW';
				UPDATE  COM.BCODETYPE SET CODEDESC = '应付远汇' WHERE CODETYPE = 'lms1405m01_SubItem' AND CODEVALUE = '962' AND LOCALE = 'zh_CN';
				UPDATE  COM.BCODETYPE SET CODEDESC = 'Forward Exchange Payable' WHERE CODETYPE = 'lms1405m01_SubItem' AND CODEVALUE = '962' AND LOCALE = 'en';

				UPDATE  COM.BCODETYPE SET CODEDESC = 'NDF Forward Exchange Receivable' WHERE CODETYPE = 'lms1405m01_SubItem' AND CODEVALUE = '963' AND LOCALE = 'en';

				UPDATE  COM.BCODETYPE SET CODEDESC = '應付遠匯ＮＤＦ' WHERE CODETYPE = 'lms1405m01_SubItem' AND CODEVALUE = '964' AND LOCALE = 'zh_TW';
				UPDATE  COM.BCODETYPE SET CODEDESC = '应付远汇ＮＤＦ' WHERE CODETYPE = 'lms1405m01_SubItem' AND CODEVALUE = '964' AND LOCALE = 'zh_CN';
				UPDATE  COM.BCODETYPE SET CODEDESC = 'NDF Forward Exchange Payable' WHERE CODETYPE = 'lms1405m01_SubItem' AND CODEVALUE = '964' AND LOCALE = 'en';
				
				UPDATE  COM.BCODETYPE SET CODEDESC = '應付遠匯' WHERE CODETYPE = 'lms1705m01_SubItme' AND CODEVALUE = '15630100' AND LOCALE = 'zh_TW';
				UPDATE  COM.BCODETYPE SET CODEDESC = '应付远汇' WHERE CODETYPE = 'lms1705m01_SubItme' AND CODEVALUE = '15630100' AND LOCALE = 'zh_CN';
				UPDATE  COM.BCODETYPE SET CODEDESC = 'Forward Exchange Payable' WHERE CODETYPE = 'lms1705m01_SubItme' AND CODEVALUE = '15630100' AND LOCALE = 'en';
				
				UPDATE  COM.BCODETYPE SET CODEDESC = 'NDF Forward Exchange Receivable' WHERE CODETYPE = 'lms1705m01_SubItme' AND CODEVALUE = '15610900' AND LOCALE = 'en';
				
				UPDATE  COM.BCODETYPE SET CODEDESC = '應付遠匯ＮＤＦ' WHERE CODETYPE = 'lms1705m01_SubItme' AND CODEVALUE = '15630900' AND LOCALE = 'zh_TW';
				UPDATE  COM.BCODETYPE SET CODEDESC = '应付远汇ＮＤＦ' WHERE CODETYPE = 'lms1705m01_SubItme' AND CODEVALUE = '15630900' AND LOCALE = 'zh_CN';
				UPDATE  COM.BCODETYPE SET CODEDESC = 'NDF Forward Exchange Payable' WHERE CODETYPE = 'lms1705m01_SubItme' AND CODEVALUE = '15630900' AND LOCALE = 'en';
			</value>
		</entry>
		<!-- J-113-0227_10702_B1001 調整歡喜信貸行業別對應邏輯 -->
		<entry key="J-113-0227_10702_B1001">
			<value>
				<![CDATA[
					update COM.BCODETYPE set codeDesc='公立學校' WHERE codetype ='clsJobTypeO' and codevalue Like 'O01' and locale='zh_TW' ;
					update COM.BCODETYPE set codeDesc='私立學校' WHERE codetype ='clsJobTypeO' and codevalue Like 'O02' and locale='zh_TW' ;
				]]>
			</value>
		</entry>


		<!-- J-113-0497_10173_B1001 開放行家理財產品種類02-03-31-68貸款-取消須先跟消金處預約額度之限制 -->
		<entry key="J-113-0497_10173_B1001">
			<value>
				<![CDATA[
					update com.BSYSPARAM set PARAMVALUE = '2099-09-06' where PARAM = 'PRODKIND_GET_QUOTA_LIMIT_DATE';
				]]>
			</value>
		</entry>
		
		<!-- J-113-0418_10173_B1002 新增及修改非屬央行管制戶繼承房產選項 -->
		<entry key="J-113-0418_10173_B1002">
			<value>
				<![CDATA[
					update com.bcodetype set CODEDESC = replace(CODEDESC, '同業', '同業尚未屆期') where codetype = 'L140M01M_plusReason_20240919' and codevalue = '0' and LOCALE = 'zh_TW';
					INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME)
					VALUES(GET_OID(), 'L140M01M_plusReason_20240919', 'C', '自然人「全國財產稅總歸戶財產查詢清單」名下建物係繼承取得', 'zh_TW', 'C', null, '10', 'system', current timestamp);
					INSERT INTO com.bcodetype(OID, CODETYPE, CODEVALUE, CODEDESC, LOCALE, CODEDESC2, CODEDESC3, CODEORDER, LASTMODIFYBY, LASTMODIFYTIME)
					VALUES(GET_OID(), 'L140M01M_plusReason_20240919', 'D', '自然人名下以房屋為抵押之購置不動產擔保放款，係因繼承而承受之房貸', 'zh_TW', 'D', null, '11', 'system', current timestamp);
				]]>
			</value>
		</entry>
		
		<!-- 修改簽報書及動審表各項費用短期續約作業費名稱-為續約作業費 -->
		<entry key="J-113-0517_10173_B1001">
			<value>
				<![CDATA[
					update com.bcodetype set CODEDESC = '續約作業費' where codetype = 'cls1141_feeNo' and CODEVALUE = '03' and LOCALE = 'zh_TW';
					update com.bcodetype set CODEDESC = '续约作业费' where codetype = 'cls1141_feeNo' and CODEVALUE = '03' and LOCALE = 'zh_CN';
				]]>
			</value>
		</entry>
		
		<!-- 增加擔保品購買用途為出租-營業使用-預備短期套利出售使用應以投資型房貸敘做的查核事項 -->
		<entry key="J-113-0427_10173_B1001">
			<value>
				<![CDATA[
					INSERT INTO lms.C900S01A(OID, CHECKCODE, CHECKSEQ, CHECKTYPE, CHECKCONTENT, PRODTYPE, CHECKRMK, CREATOR, CREATETIME, UPDATER, UPDATETIME)
					VALUES(db2inst1.GET_OID(), '267', '267', '7', '擔保品之購買用途是否做為出租、營業使用、預備短期套利出售使用者；或屬擔保品提供人名下第1、2戶房屋，並加計其配偶達第3戶以上購屋貸款，且無法提供自住佐證資料者；或屬擔保品提供人名下第3戶以上購屋貸款者。', 'system', current timestamp, 'system', current timestamp);
					INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
					VALUES(db2inst1.GET_OID(), '31', '14501500', '1', '267', 'system', current timestamp, 'system', current timestamp);
					INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
					VALUES(db2inst1.GET_OID(), '31', '13506200', '1', '267', 'system', current timestamp, 'system', current timestamp);
					INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
					INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
					VALUES(db2inst1.GET_OID(), '72', '14501500', '1', '267', 'system', current timestamp, 'system', current timestamp);
					INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
					VALUES(db2inst1.GET_OID(), '72', '13506200', '1', '267', 'system', current timestamp, 'system', current timestamp);
					INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
					INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
					VALUES(db2inst1.GET_OID(), '10', '14501500', '1', '267', 'system', current timestamp, 'system', current timestamp);
					INSERT INTO lms.C900M01C(OID, PRODKIND, SUBJCODE, TYPE, SOUSECODE, CREATOR, CREATETIME, UPDATER, UPDATETIME)
					VALUES(db2inst1.GET_OID(), '10', '13506200', '1', '267', 'system', current timestamp, 'system', current timestamp);
				]]>
			</value>
		</entry>
		
	</util:map>
</beans>
