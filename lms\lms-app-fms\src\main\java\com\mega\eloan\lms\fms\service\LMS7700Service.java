package com.mega.eloan.lms.fms.service;

import java.util.List;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L140MM5A;
import com.mega.eloan.lms.model.L140MM5B;
import com.mega.eloan.lms.model.L140MM5C;

/**
 * <pre>
 *電子文件維護作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface LMS7700Service extends AbstractService {
	
	List<L140MM5C> findL140mm5csByMainId(String mainId);
	
	public void saveL140MM5Cs(List<L140MM5C> l140mm5cs);
	
	public boolean deleteL140mm5cs(String[] oids);
	
	public void updL140mm5aCnt(String oid);
	
	public void deleteL140mm5bs(List<L140MM5B> l140mm5bs, boolean isAll);
	
	public void saveL140mm5bList(List<L140MM5B> list);
	
	public L140MM5B findL140mm5b(String mainId, String branchType, String branchId,
			String staffNo, String staffJob);

	public void flowAction(String mainOid, L140MM5A model,
			boolean setResult, boolean resultType, boolean upCom)
			throws Throwable;
}
