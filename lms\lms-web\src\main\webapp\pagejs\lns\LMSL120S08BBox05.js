var init120s08 = {
	fhandle : "lms1201formhandler",
	versionDate : "2023-03-01",
	defButton : {
		"close" : function() {
			$.thickbox.close();
		}
	}
};
var API_2023 = {
    /** 明細檔 */
    openDetailBox : function(type, docOid, data) {
		//showRiskAdd,showCompetition

		var verNo = init120s08.versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);
		var $inputL120s08Box = $("#inputL120s08Box_"+verNo);
        var curr = $L120S08Form.find('#curr').val();
		
		$(".showN").show();
		$("#showTitleSY").show();
		$("#showTitleSN").hide();
		if (curr == "TWD") {
//			$(".showN").show();
//			$("#showTitleSY").show();
//			$("#showTitleSN").hide();
			$("#showMsgTWD").show();
			$("#showMsgUSD").hide();
		} else {
//			$(".showN").hide();
//			$("#showTitleSY").hide();
//			$("#showTitleSN").show();
			$("#showMsgTWD").hide();
			$("#showMsgUSD").show();
		}

		// 授權內且為分行內的案件
		// 才要出現本案未符合經權內之加減碼，仍於經權內敘作
		$L120S08Form.find('#isOverAuthDiv').hide();
		if (responseJSON.docKind && responseJSON.docKind == "1"){
			if(responseJSON.authLvl && responseJSON.authLvl == "1" ) {
				$L120S08Form.find('#isOverAuthDiv').show();
			}
		}
		var docOid = "";
		if (data) {
			docOid = data.oid;
		}

		if (type == 'new') {
			$L120S08Form.find('#memo').val(''); 
		}		

		var buttons = {};
		if (!thickboxOptions.readOnly) {
			buttons["calculate"] = function(){
			    if (!API_2023.btnCaculate(init120s08.versionDate)) {
		            CommonAPI.showErrorMessage(i18n.lms1401s03["L120S08A.error16"]);
		        }
			};
			buttons["sure"] = function() {
				var $form = $L120S08Form;

				// 用畫面上的預期損失個案差異率來判斷，這個span有值，一定是用程式引進的!!
				if ($L120S08Form.find('#spanPDLostDiffRate_dscr').val() == '') {
					// L120S08A.error19=請先引進申請利率
					CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error19']);
					return false;
				}
				
				// 先計算可以先將數字欄位塞0
				if (!API_2023.btnCaculate(init120s08.versionDate)) {
					CommonAPI.showErrorMessage(i18n.lms1401s03["L120S08A.error16"]);
					return false;
				}

				if ($L120S08Form.find('[name=isOverAuth_dscr]:checked').val() == 'Y' && !$L120S08Form.find('#isOverAuthDesc_dscr').val()) {
					// L120S08A.error18=本案未符合經權內之加減碼，仍於經權內敘作之原因
					CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error12']
					+ i18n.lms1401s03["L120S08A.error18"]);
					return false;
				}
				
				if (!$form.valid()) {
					return false;
				}

				// 如果畫面根本沒有"本案未符合經權內之加減碼"，則清掉checkbox和原因
				// 代表他不是經權案件了!!
				if($L120S08Form.find('[name=isOverAuth_dscr]').is(":visible")){
					// 有->不做事
				}else{
					$L120S08Form.find('[name=isOverAuth_dscr]').prop('checked', false);
					$L120S08Form.find('#isOverAuthDesc_dscr').val('');
				}
				
				// 如果沒打勾"本案未符合經權內之加減碼"，則清掉原因
				if($L120S08Form.find('[name=isOverAuth_dscr]:checked').val() != 'Y'){
					$L120S08Form.find('#isOverAuthDesc_dscr').val('')
				}
				
				
				
				$.ajax({
					handler : init120s08.fhandle,
					data : {// 把資料轉成json
						formAction : "saveL120s08Data_20230301",
						docOid : docOid,
						mainId : responseJSON.mainId,
						curr : curr,
						versionDate:init120s08.versionDate,
					    verNo:verNo,
						L120S08Form : JSON.stringify($L120S08Form.serializeData())
					}
				}).done(function(data) {
					if(data.checkMarkSb){
						$.thickbox.close();
						API.showMessage(data.checkMarkSb);
					}else{
						$.thickbox.close();
					}
					L120s08BoxAPI._triggerMainGrid();
				}); // close ajax
			};

		} else {
			$L120S08Form.readOnlyChilds(true);
			$L120S08Form.find("button").hide();
		}

		buttons["cancel"] = function() {
			$.thickbox.close();
		};

		$inputL120s08Box.thickbox({
			// L120S08A.title2=合理性分析表
			title : i18n.lms1401s03['L120S08A.title2'],
			width : 1100,
			height : 600,
			modal : true,
			readOnly : thickboxOptions.readOnly,
			align : "center",
			i18n : i18n.def,
			valign : "bottom",
			buttons : buttons
		});
	},
	/**
	 * 引進關係戶往來彙總二維表集團評等與貢獻度合計 return true ,false
	 */
	btnApplyGroup : function(versionDate) {

		var verNo = versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);
		
		var nowDate = new Date();
		var MM = nowDate.getMonth();
		var YY = nowDate.getFullYear();
		var SMM;
		var SYY;
		if (MM == 0) {
			MM = 12;
		}

		if (MM == 12) {
			SMM = MM - 5;
			YY = YY - 1;
			SYY = YY;
		} else if (MM > 5 && MM < 12) {
			SMM = MM - 5;
			SYY = YY;
		} else {
			SMM = MM + 12 - 5;
			SYY = YY - 1;
		}

		var $tL120S08Form1a = $("#tL120S08Form1a_"+verNo);
		$tL120S08Form1a.find("#queryDateE0").val(YY);
		$tL120S08Form1a.find("#queryDateE1").val(MM);

		$("#inputSearch_"+verNo).thickbox({ // 使用選取的內容進行彈窗
			title : i18n.lms1401s03["L120S08A.fieldTitle48"]+"/"+i18n.lms1401s03["L120S08A.fieldTitle49"], // L120S08A.title4=貢獻度查詢期間
			width : 600,
			height : 300,
			modal : true,
			align : 'center',
			valign : 'bottom',
			i18n : i18n.def,
			buttons : {
				"sure" : function() {					 
					if ($tL120S08Form1a.valid()) {
						if ($tL120S08Form1a.find("#queryDateE1").val() < 1
								|| $tL120S08Form1a.find("#queryDateE1").val() > 12) {
							CommonAPI.showMessage(i18n.lms1401s03["L120S08A.error07"]);
							return;
						} else if ($tL120S08Form1a.find("#queryDateE0").val() <= 0) {
							CommonAPI.showMessage(i18n.lms1401s03["L120S08A.error08"]);
							return;
						} else {							
							var selectKind = $tL120S08Form1a.find("#selectKind").val();							
							$.thickbox.close();
							
							var qCustId = $L120S08Form.find("#custId").val();
							var qDupNo = $L120S08Form.find("#dupNo").val();

							$.ajax({
								handler : init120s08.fhandle,
								type : "POST",
								dataType : "json",
								data : {
									formAction : "queryL120S08BDWData_20230301",
									mainId : responseJSON.mainid,
									queryDateE0 : $tL120S08Form1a.find("#queryDateE0").val(),
									queryDateE1 : $tL120S08Form1a.find("#queryDateE1").val(),
									qCustId:qCustId,
									qDupNo:qDupNo
								}
							}).done(function(data) {
								$L120S08Form.find("#oneYearTotal_dscr").val(obj120.oneYearAll_dscr);
								$L120S08Form.find("#oneYearTotal_thousand_dscr").val(obj120.oneYearAll_thousand_dscr);

								$L120S08Form.find("#oneYearOutLoanP_dscr").val(obj120.oneYearOutLoanP_dscr);
								$L120S08Form.find("#oneYearOutLoanP_thousand_dscr").val(obj120.oneYearOutLoanP_thousand_dscr);
								$L120S08Form.find("#oneYearFnDP_dscr").val(obj120.oneYearFnDP_dscr);
								$L120S08Form.find("#oneYearFnDP_thousand_dscr").val(obj120.oneYearFnDP_thousand_dscr);
								$L120S08Form.find("#oneYearWealthP_dscr").val(obj120.oneYearWealthP_dscr);
								$L120S08Form.find("#oneYearWealthP_thousand_dscr").val(obj120.oneYearWealthP_thousand_dscr);

								$L120S08Form.find("#oneYearOutLoanG_dscr").val(obj120.oneYearOutLoanG_dscr);
								$L120S08Form.find("#oneYearOutLoanG_thousand_dscr").val(obj120.oneYearOutLoanG_thousand_dscr);
								$L120S08Form.find("#oneYearFnDG_dscr").val(obj120.oneYearFnDG_dscr);
								$L120S08Form.find("#oneYearFnDG_thousand_dscr").val(obj120.oneYearFnDG_thousand_dscr);
								$L120S08Form.find("#oneYearWealthG_dscr").val(obj120.oneYearWealthG_dscr);
								$L120S08Form.find("#oneYearWealthG_thousand_dscr").val(obj120.oneYearWealthG_thousand_dscr);

								$L120S08Form.find("#mainGrpAvgRateR_dscr").val(obj120.mainGrpAvgRate_dscr);
								$L120S08Form.find("#mainGrpAvgRateR_rate_dscr").val(obj120.mainGrpAvgRate_rate_dscr);
							});
						}
					}
				},
				"cancel" : function() {
					$.thickbox.close();
				}
			}
		});
	},
	/** 計算 */
	btnCaculate : function(versionDate) {
		var verNo = versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);		
		
		if (!$L120S08Form.find("#baseRate1").val() 
				|| !$L120S08Form.find("#baseRate2").val() 
				|| !$L120S08Form.find("#baseRate3").val()) {
			// L120S08A.error12=請先輸入
			// L120S08A.baseRate=基礎放款利利率
			CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error12']
					+ i18n.lms1401s03["L120S08A.baseRate"]);
			return false;
		}
		
		if (!$L120S08Form.find("#lostDiffRate").val()) {
			// L120S08A.error12=請先輸入
			// L120S08A.2023_lostDiffRate=預期損失差異率
			CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error12']
					+ i18n.lms1401s03["L120S08A.2023_lostDiffRate"]);
			return false;
		}
		
		var hasError = false;

		// 檢查有擔無擔是否要計算
        var hasSShort = false;   // 擔保短期
        var hasSLong = false;   // 擔保中長期
        var hasNShort = false;   // 非擔保短期
        var hasNLong = false;   // 非擔保中長期
        $L120S08Form.find(".plus, .minus").find("input[id*='_short_disYearRateS']").each(function() {
            if ($(this).val()) {
            	hasSShort = true;
            }
        });
        $L120S08Form.find(".plus, .minus").find("input[id*='_long_disYearRateS']").each(function() {
            if ($(this).val()) {
            	hasSLong = true;
            }
        });
        $L120S08Form.find(".plus, .minus").find("input[id*='_short_disYearRateN']").each(function() {
            if ($(this).val()) {
            	hasNShort = true;
            }
        });
        $L120S08Form.find(".plus, .minus").find("input[id*='_long_disYearRateN']").each(function() {
            if ($(this).val()) {
            	hasNLong = true;
            }
        });

		// 數值欄位若無值先補0 避免計算錯誤
		$L120S08Form.find(".caculate").find("input:not([id*='_dscr'])").each(function() {
		    var thisId = this.id;
		    if (thisId.match(/_disYearRateS$/)) {   // 擔保
		    	if(hasSShort){
		    		if (thisId.match(/_short_disYearRateS$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}else if(!thisId.match(/_long_disYearRateS$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}
		    	}
		    	if(hasSLong){
		    		if (thisId.match(/_long_disYearRateS$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}else if(!thisId.match(/_short_disYearRateS$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}
		    	}
		    } else if (thisId.match(/_disYearRateN$/)) {    // 非擔保
		    	if(hasNShort){
		    		if (thisId.match(/_short_disYearRateN$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}else if(!thisId.match(/_long_disYearRateN$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}
		    	}
		    	if(hasNLong){
		    		if (thisId.match(/_long_disYearRateN$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}else if(!thisId.match(/_short_disYearRateN$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}
		    	}
		    } else {
		        if ($(this).val()) {
                } else {
                    $(this).val(0);
                }
		    }
		});

		var baseRate1 = parseFloat(0);	// 資金成本率
		baseRate1 = parseFloat($L120S08Form.find("#baseRate1").val(), 10);
		var baseRate2 = parseFloat(0);	// 營運成本率
		baseRate2 = parseFloat($L120S08Form.find("#baseRate2").val(), 10);
		var baseRate3 = parseFloat(0);	// 平均預期損失率
		baseRate3 = parseFloat($L120S08Form.find("#baseRate3").val(), 10);
		
		var baseRate = parseFloat(0);	// 基礎放款利利率		
		baseRate = baseRate1 + baseRate2 + baseRate3;
		
		// 資金成本率抄到下面計算區塊的資金成本率
		
		// 營運成本率抄到下面計算區塊的營運成本率
		
		
		// 預期損失個案差異率
		var lostDiffRate = parseFloat(0);
		lostDiffRate = parseFloat($L120S08Form.find("#lostDiffRate").val(), 10);
		
		// =============================加碼小計 =========================================
		var plusTotal_S_short = parseFloat(0);	// 加碼小計-擔保_短期
		var plusTotal_S_long = parseFloat(0);	// 加碼小計-擔保_中長期
		var plusTotal_N_short = parseFloat(0);	// 加碼小計-非擔保_短期
		var plusTotal_N_long = parseFloat(0);	// 加碼小計-非擔保_中長期
		
		// =============================加碼後利率(A)=========================================
		var subTotalA_S_short = parseFloat(0);	// 加碼後利率(A)-擔保_短期
		var subTotalA_S_long = parseFloat(0);	// 加碼後利率(A)-擔保_中長期
		var subTotalA_N_short = parseFloat(0);	// 加碼後利率(A)-非擔保_短期
		var subTotalA_N_long = parseFloat(0);	// 加碼後利率(A)-非擔保_中長期
		
		
		// 擔保短期
		$L120S08Form.find("#subTotalA_short_disYearRateS").val('');
		if(hasSShort){
			// 排掉有擔中長期的不計算
            $L120S08Form.find(".plus").find("input[id*='_disYearRateS']").not("[id*='_long_disYearRateS']").each(function() {
                var eachTotalA_S = parseFloat(0);
                if ($(this).val()) {
                    eachTotalA_S = parseFloat($(this).val(), 10);
                } else {
                    $(this).val(0);
                    eachTotalA_S = parseFloat(0);
                }
                plusTotal_S_short = plusTotal_S_short + eachTotalA_S;
            });
            plusTotal_S_short = lostDiffRate + plusTotal_S_short;
            $L120S08Form.find("#plusTotal_short_disYearRateS").val(plusTotal_S_short.toFixed(3));
            
            subTotalA_S_short = baseRate + plusTotal_S_short;
            $L120S08Form.find("#subTotalA_short_disYearRateS").val(subTotalA_S_short.toFixed(3));
		}
		
		// 擔保中長期
		$L120S08Form.find("#subTotalA_long_disYearRateS").val('');
		if(hasSLong){
			// 排掉有擔短期的不計算
            $L120S08Form.find(".plus").find("input[id*='_disYearRateS']").not("[id*='_short_disYearRateS']").each(function() {
                var eachTotalA_S = parseFloat(0);
                if ($(this).val()) {
                    eachTotalA_S = parseFloat($(this).val(), 10);
                } else {
                    $(this).val(0);
                    eachTotalA_S = parseFloat(0);
                }
                plusTotal_S_long = plusTotal_S_long + eachTotalA_S;
            });
            plusTotal_S_long = lostDiffRate + plusTotal_S_long;
            $L120S08Form.find("#plusTotal_long_disYearRateS").val(plusTotal_S_long.toFixed(3));
            
            subTotalA_S_long = baseRate + plusTotal_S_long;
            $L120S08Form.find("#subTotalA_long_disYearRateS").val(subTotalA_S_long.toFixed(3));
		}
		
		// 非擔保短期
		$L120S08Form.find("#subTotalA_short_disYearRateN").val('');
		if(hasNShort){
			// 排掉無擔中長期的不計算
            $L120S08Form.find(".plus").find("input[id*='_disYearRateN']").not("[id*='_long_disYearRateN']").each(function() {
                var eachTotalA_N = parseFloat(0);
                if ($(this).val()) {
                    eachTotalA_N = parseFloat($(this).val(), 10);
                } else {
                    $(this).val(0);
                    eachTotalA_N = parseFloat(0);
                }
                plusTotal_N_short = plusTotal_N_short + eachTotalA_N;
            });
            plusTotal_N_short = lostDiffRate + plusTotal_N_short;
            $L120S08Form.find("#plusTotal_short_disYearRateN").val(plusTotal_N_short.toFixed(3));
            
            subTotalA_N_short = baseRate + plusTotal_N_short;
            $L120S08Form.find("#subTotalA_short_disYearRateN").val(subTotalA_N_short.toFixed(3));
		}
		
		// 非擔保中長期
		$L120S08Form.find("#subTotalA_long_disYearRateN").val('');
		if(hasNLong){
			// 排掉無擔短期的不計算
            $L120S08Form.find(".plus").find("input[id*='_disYearRateN']").not("[id*='_short_disYearRateN']").each(function() {
                var eachTotalA_N = parseFloat(0);
                if ($(this).val()) {
                    eachTotalA_N = parseFloat($(this).val(), 10);
                } else {
                    $(this).val(0);
                    eachTotalA_N = parseFloat(0);
                }
                plusTotal_N_long = plusTotal_N_long + eachTotalA_N;
            });
            plusTotal_N_long = lostDiffRate + plusTotal_N_long;
            $L120S08Form.find("#plusTotal_long_disYearRateN").val(plusTotal_N_long.toFixed(3));
            
            subTotalA_N_long = baseRate + plusTotal_N_long;
            $L120S08Form.find("#subTotalA_long_disYearRateN").val(subTotalA_N_long.toFixed(3));
		}
		
		// =============================減碼=========================================
		var subTotalB_S_short = parseFloat(0);	// 小計(B)-擔保_短期
		subTotalB_S_short = parseFloat($L120S08Form.find("#subTotalB_short_disYearRateS").val(), 10);

		var subTotalB_S_long = parseFloat(0);	// 小計(B)-擔保_中長期
		subTotalB_S_long = parseFloat($L120S08Form.find("#subTotalB_long_disYearRateS").val(), 10);
		
		var subTotalB_N_short = parseFloat(0);	// 小計(B)-非擔保_短期
		subTotalB_N_short = parseFloat($L120S08Form.find("#subTotalB_short_disYearRateN").val(), 10);
		
		var subTotalB_N_long = parseFloat(0);	// 小計(B)-非擔保_中長期
		subTotalB_N_long = parseFloat($L120S08Form.find("#subTotalB_long_disYearRateN").val(), 10);
		
		// =============================本案申請利率(A-B)=========================================
		var caseRate_S_short = parseFloat(0);// 有擔短期
		var caseRate_S_long = parseFloat(0);// 有擔中長期
        var caseRate_N_short = parseFloat(0);// 無擔短期
        var caseRate_N_long = parseFloat(0);// 無擔中長期

        $L120S08Form.find("#caseRate_short_disYearRateS").val('');
		if(hasSShort){
			caseRate_S_short = subTotalA_S_short - subTotalB_S_short;
		    $L120S08Form.find("#caseRate_short_disYearRateS").val(caseRate_S_short.toFixed(3));
		}

		$L120S08Form.find("#caseRate_long_disYearRateS").val('');
		if(hasSLong){
			caseRate_S_long = subTotalA_S_long - subTotalB_S_long;
		    $L120S08Form.find("#caseRate_long_disYearRateS").val(caseRate_S_long.toFixed(3));
		}
		
		$L120S08Form.find("#caseRate_short_disYearRateN").val('');
		if(hasNShort){
            caseRate_N_short = subTotalA_N_short - subTotalB_N_short;
            $L120S08Form.find("#caseRate_short_disYearRateN").val(caseRate_N_short.toFixed(3));
		}
		
		$L120S08Form.find("#caseRate_long_disYearRateN").val('');
		if(hasNLong){
            caseRate_N_long = subTotalA_N_long - subTotalB_N_long;
            $L120S08Form.find("#caseRate_long_disYearRateN").val(caseRate_N_long.toFixed(3));
		}

		// =============================經權不得低於「基礎放款利率+預期損失差異率+0.15%」=========================================
		var spanMangerRate = parseFloat(0);
		$L120S08Form.find("#spanMangerRate_dscr").val('');
		if(!isNaN(baseRate) && !isNaN(lostDiffRate)){
			spanMangerRate = baseRate + lostDiffRate + 0.15;
            $L120S08Form.find("#spanMangerRate_dscr").val(spanMangerRate.toFixed(3));
		}
		
		// =============================預估=========================================
		// ①資金成本率(計算區塊的)
		$L120S08Form.find("#minusBaseRate1").val(baseRate1);
		var minusBaseRate1 = baseRate1;
			
		// ②FTP
		var ftpRate_S_short = parseFloat(0);	// FTP-擔保_短期
		ftpRate_S_short = parseFloat($L120S08Form.find("#ftpRate_short_disYearRateS").val(), 10);
		var ftpRate_S_long = parseFloat(0);	// FTP-擔保_中長期
		ftpRate_S_long = parseFloat($L120S08Form.find("#ftpRate_long_disYearRateS").val(), 10);

		var ftpRate_N_short = parseFloat(0);	// FTP-非擔保_短期
		ftpRate_N_short = parseFloat($L120S08Form.find("#ftpRate_short_disYearRateN").val(), 10);
		var ftpRate_N_long = parseFloat(0);	// FTP-非擔保_中長期
		ftpRate_N_long = parseFloat($L120S08Form.find("#ftpRate_long_disYearRateN").val(), 10);
		
		// 營運成本率(計算區塊的)
		$L120S08Form.find("#minusBaseRate2").val(baseRate2);
		var minusBaseRate2 = baseRate2;
		
		// 個案預期損失率(平均預期損失率+預期損失差異率)	
		var badDebtLostRate = parseFloat(0);
		$L120S08Form.find("#badDebtLostRate").val('');
		if(!isNaN(lostDiffRate) && !isNaN(baseRate3)){
            badDebtLostRate = baseRate3 + lostDiffRate;
            $L120S08Form.find("#badDebtLostRate").val(badDebtLostRate.toFixed(3));
		}

		// ①全行基礎收益率
		var estIncomeBaseRate_S_short = parseFloat(0);	// 預估收益率-擔保_短期
		$L120S08Form.find("#estIncomeBaseRate_short_disYearRateS").val('');
		if(hasSShort){
			estIncomeBaseRate_S_short = caseRate_S_short - minusBaseRate1 - minusBaseRate2 - badDebtLostRate;
            $L120S08Form.find("#estIncomeBaseRate_short_disYearRateS").val(estIncomeBaseRate_S_short.toFixed(3));
		}
		
		var estIncomeBaseRate_S_long = parseFloat(0);	// 預估收益率-擔保_中長期
		$L120S08Form.find("#estIncomeBaseRate_long_disYearRateS").val('');
		if(hasSLong){
            estIncomeBaseRate_S_long = caseRate_S_long - minusBaseRate1 - minusBaseRate2 - badDebtLostRate;
            $L120S08Form.find("#estIncomeBaseRate_long_disYearRateS").val(estIncomeBaseRate_S_long.toFixed(3));
		}
		
		var estIncomeBaseRate_N_short = parseFloat(0);	// 預估收益率-非擔保_短期
		$L120S08Form.find("#estIncomeBaseRate_short_disYearRateN").val('');
		if(hasNShort){
            estIncomeBaseRate_N_short = caseRate_N_short - minusBaseRate1 - minusBaseRate2 - badDebtLostRate;
            $L120S08Form.find("#estIncomeBaseRate_short_disYearRateN").val(estIncomeBaseRate_N_short.toFixed(3));
		}
		
		var estIncomeBaseRate_N_short = parseFloat(0);	// 預估收益率-非擔保_中長期
		$L120S08Form.find("#estIncomeBaseRate_long_disYearRateN").val('');
		if(hasNLong){
            estIncomeBaseRate_N_long = caseRate_N_long - minusBaseRate1 - minusBaseRate2 - badDebtLostRate;
            $L120S08Form.find("#estIncomeBaseRate_long_disYearRateN").val(estIncomeBaseRate_N_long.toFixed(3));
		}
		
		// ②分行FTP計價收益率
		var estIncomeRate_S_short = parseFloat(0);	// 預估收益率-擔保_短期
		$L120S08Form.find("#estIncomeRate_short_disYearRateS").val('');
		if(hasSShort){
            estIncomeRate_S_short = caseRate_S_short - ftpRate_S_short - minusBaseRate2 - badDebtLostRate;
            $L120S08Form.find("#estIncomeRate_short_disYearRateS").val(estIncomeRate_S_short.toFixed(3));
		}
		
		var estIncomeRate_S_long = parseFloat(0);	// 預估收益率-擔保_中長期
		$L120S08Form.find("#estIncomeRate_long_disYearRateS").val('');
		if(hasSLong){
            estIncomeRate_S_long = caseRate_S_long - ftpRate_S_long - minusBaseRate2 - badDebtLostRate;
            $L120S08Form.find("#estIncomeRate_long_disYearRateS").val(estIncomeRate_S_long.toFixed(3));
		}
		
		var estIncomeRate_N_short = parseFloat(0);	// 預估收益率-非擔保_短期
		$L120S08Form.find("#estIncomeRate_short_disYearRateN").val('');
		if(hasNShort){
            estIncomeRate_N_short = caseRate_N_short - ftpRate_N_short - minusBaseRate2 - badDebtLostRate;
            $L120S08Form.find("#estIncomeRate_short_disYearRateN").val(estIncomeRate_N_short.toFixed(3));
		}
		
		var estIncomeRate_N_short = parseFloat(0);	// 預估收益率-非擔保_中長期
		$L120S08Form.find("#estIncomeRate_long_disYearRateN").val('');
		if(hasNLong){
            estIncomeRate_N_long = caseRate_N_long - ftpRate_N_long - minusBaseRate2 - badDebtLostRate;
            $L120S08Form.find("#estIncomeRate_long_disYearRateN").val(estIncomeRate_N_long.toFixed(3));
		}
			
		if (hasError) {
			return false;
		} else {
			return true;
		}
	},
	btnClearWithTerm : function(sType, versionDate, term) {
		var verNo = versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);
		
		$("form[id='"+L120S08FormName+"'] input[id*='_" + term + "_disYearRate" + sType + "']").each(function() {
			$(this).val('');
		});
		
		// J-110-0498 優化eloan企金授信系統之額度明細表利率合理性分析編輯功能
		// 開舊版起來因為是跑each，沒有符合的span也不會出錯
		$("form[id='"+L120S08FormName+"'] span[id*='_" + term + "_disYearRate" + sType + "_rmk']").each(function() {
			$(this).val('');
		});
	}
};

initDfd.done(function(json) {
    $("#btnApplyRateDscr").click(function() {
        L120s08BoxAPI.btnApplyRateDscr(init120s08.versionDate);
    });
    $("#btnApplyGroup").click(function() {
        API_2023.btnApplyGroup(init120s08.versionDate);
    });
    $("#btnClearS").click(function() {
        L120s08BoxAPI.btnClear('S', init120s08.versionDate);
    });
    $("#btnClearN").click(function() {
        L120s08BoxAPI.btnClear('N', init120s08.versionDate);
    });
    
    $("#btnClearSShort").click(function() {
    	API_2023.btnClearWithTerm('S', init120s08.versionDate, 'short');
    });
    $("#btnClearSLong").click(function() {
    	API_2023.btnClearWithTerm('S', init120s08.versionDate, 'long');
    });
    $("#btnClearNShort").click(function() {
    	API_2023.btnClearWithTerm('N', init120s08.versionDate, 'short');
    });
    $("#btnClearNLong").click(function() {
    	API_2023.btnClearWithTerm('N', init120s08.versionDate, 'long');
    });

    L120s08BoxAPI.gridviewConnomOtherSelect();
    
    $("#ftpRateGrid").iGrid({
        handler: "lms1201gridhandler",//inits.ghandle,
        needPager: false,
        multiselect: false,
        sortname: 'insCdCnm',
        sortorder: 'asc',
        postData : {
        	formAction: "queryFtpRate"
		},
        colModel: [{
            colHeader: i18n.lms1401s03["L120S08A.fieldTitle1"],//"項目",
            name: 'insCdCnm',
            align: "center",
            width: 80,
            sortable: false
        }, {
            colHeader: " ",
            name: 'insRt',
            align: "center",
            width: 20,
            sortable: false
        }]
    });
});

function impFtpRate(type, term) {
    // type： S-擔保   N-非擔保       供後續回壓欄位用
    var verNo = init120s08.versionDate.replace(/-/g, "");
    var L120S08FormName = "L120S08Form_"+verNo;
    var $L120S08Form = $("#"+L120S08FormName);
    var curr = $L120S08Form.find('#curr').val();

    $("#ftpRateGrid").jqGrid("setGridParam", {
        postData: {
        	curr: curr
        },
        search: true
    }).trigger("reloadGrid");
    
    $("#impFtpRateThickBox").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.def['grid_selector'],
        width: 500,
        height: 250,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
            	var rowId = $("#ftpRateGrid").getGridParam('selrow');
            	if (rowId) {
            		var data = $("#ftpRateGrid").getRowData(rowId);
            		$L120S08Form.find("#ftpRate_" + term + "_disYearRate" + type).val(roundDecimal(data.insRt, 3));
            		$.thickbox.close();
            	} else {
                    CommonAPI.showMessage(i18n.def['grid.selrow']);
                }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function roundDecimal(val, precision) {
	  return Math.round(Math.round(val * Math.pow(10, (precision || 0) + 1)) / 10) / Math.pow(10, (precision || 0));
}