package com.mega.eloan.lms.cls.handler.form;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.pages.CLS3101M01Page;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.model.C310M01A;
import com.mega.eloan.lms.model.C310M01E;
import com.mega.eloan.lms.model.C900M03A;
import com.mega.eloan.lms.model.C900S02E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 同一通訊處註記
 * </pre>
 * 
 * @since 2019/02/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/02/19,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("cls3101m01formhandler")
@DomainClass(C310M01A.class)
public class CLS3101M01FormHandler extends AbstractFormHandler {

	private static final int MAXLEN_C310M01A_CHK_MEMO = StrUtils.getEntityFileldLegth(C310M01A.class, "chk_memo", 300);
	
	@Resource
	BranchService branchService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	DwdbBASEService dwdbBASEService;
	
	@Resource
	DocCheckService docCheckService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	UserInfoService userInfoService;
	
	Properties prop_cls3101m01 = MessageBundleScriptCreator.getComponentResource(CLS3101M01Page.class);
	Properties prop_abstractEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newC310M01A(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		C900S02E c900s02e = null;		
		if(true){
			String refMainId = Util.trim(params.getString("refMainId"));
			c900s02e = clsService.findC900S02E_mainId(refMainId);
			if(c900s02e==null){
				throw new CapMessageException("["+refMainId+"]查無資料", getClass());
			}
		}
		C310M01A c310m01a = new C310M01A();
		c310m01a.setMainId(IDGenerator.getUUID());
		c310m01a.setTypCd(UtilConstants.Casedoc.typCd.DBU);
		c310m01a.setCustId(c900s02e.getCustId());
		c310m01a.setDupNo(c900s02e.getDupNo());
		c310m01a.setCustName(c900s02e.getCname());		
		c310m01a.setOwnBrId(user.getUnitNo());
		c310m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());

		String txCode = Util.trim(params.getString(EloanConstants.TRANSACTION_CODE));
		c310m01a.setTxCode(txCode);
		// UPGRADE: 待確認，URL是否正確
		c310m01a.setDocURL(params.getString("docUrl"));
		c310m01a.setDeletedTime(CapDate.getCurrentTimestamp());
		c310m01a.setRefMainId(c900s02e.getMainId());		
		c310m01a.setCreator(user.getUserId());
		c310m01a.setCreateTime(CapDate.getCurrentTimestamp());
		clsService.save(c310m01a);

		return defaultResult(params, c310m01a, result);
	}
	
	@DomainAuth(value = AuthType.Query)
	public IResult queryC310M01A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C310M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC310M01A_oid(mainOid);
			C900S02E c900s02e = clsService.findC900S02E_mainId(meta.getRefMainId());
			String page = params.getString(EloanConstants.PAGE);
			if ("01".equals(page)) {
				if (true) {
					LMSUtil.addMetaToResult(result, meta, new String[] {
							"custId", "dupNo", "custName",							
							"randomCode", "refMainId", "chk_result", "chk_memo" });					
				}
				String ownBrId = meta.getOwnBrId();
				result.set("ownBrId",StrUtils.concat(ownBrId, " ",branchService.getBranchName(ownBrId)));
				result.set("docStatus",getMessage("docStatus." + meta.getDocStatus()));
				result.set("typCd", getMessage("typCd." + meta.getTypCd()));
				result.set("creator", _id_name(meta.getCreator()));
				result.set("createTime",Util.trim(TWNDate.valueOf(meta.getCreateTime())));
				result.set("updater", _id_name(meta.getUpdater()));
				result.set("updateTime",Util.trim(TWNDate.valueOf(meta.getUpdateTime())));
				result.set("c900s02e_mainId", meta.getRefMainId());
				
				result.set("cyc_mn", StringUtils.substring(TWNDate.toAD(c900s02e.getCyc_mn()), 0, 7));
				result.set("text", c900s02e.getText());
				
				if(true){
					List<C310M01E> c310m01e_list = clsService.findC310M01E(meta.getMainId());
					if (!Util.isEmpty(c310m01e_list)) {
						// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
						// L7.總行主管
						StringBuilder bossId = new StringBuilder("");
						for (C310M01E c310m01e : c310m01e_list) {
							// 要加上人員代碼
							String type = Util.trim(c310m01e.getStaffJob());
							String userId = Util.trim(c310m01e.getStaffNo());
							String value = Util.trim(lmsService.getUserName(userId));
							if ("L1".equals(type)) {
								result.set("showApprId", userId + " " + value);
							} else if ("L3".equals(type)) {
								bossId.append(bossId.length() > 0 ? "<br/>" : "");
								bossId.append(userId);
								bossId.append(" ");
								bossId.append(value);
							} else if ("L4".equals(type)) {
								result.set("reCheckId", userId + " " + value);
							} else if ("L5".equals(type)) {
								result.set("managerId", userId + " " + value);
							} else if ("L6".equals(type)) {
								result.set("mainApprId", userId + " " + value);
							} else if ("L7".equals(type)) {
								result.set("mainReCheckId", userId + " " + value);
							}
						}
						result.set("bossId", bossId.toString());
					}
				}
			} //end page-01
		}

		return defaultResult(params, meta, result);
	}
	
	private String _id_name(String raw_id) {
		String id = Util.trim(raw_id);
		return Util.trim(id + " "
					+ Util.trim(userInfoService.getUserName(id)));
	}
	
	/**
	 * 儲存
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params) throws CapException {
		return _saveAction(params, "N");
	}
	
	private CapAjaxFormResult _saveAction(PageParameters params,
			String tempSave) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y", params.getString("allowIncomplete"));
		//===
		String KEY = "saveOkFlag";
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String chk_memo = Util.trim(params.getString("chk_memo"));
		C310M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try{
				meta = clsService.findC310M01A_oid(mainOid);
				
				String page = params.getString(EloanConstants.PAGE);
				if ("01".equals(page)) {
					CapBeanUtil.map2Bean(params, meta, new String[] {
							"chk_result"});
					meta.setChk_memo(Util.truncateString(chk_memo, MAXLEN_C310M01A_CHK_MEMO));
				}
				meta.setDeletedTime(null);
				meta.setUpdater(user.getUserId());
				meta.setUpdateTime(CapDate.getCurrentTimestamp());
				meta.setRandomCode(IDGenerator.getRandomCode());
				//---
				clsService.save(meta);				
				// ===
				if (Util.notEquals("Y",
						SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
					// 在tempSave<>Y,若有未填欄位,丟 CapMessageException, 讓
					// saveOkFlag==false

					String msg = checkIncompleteMsg(meta, chk_memo);
					if (Util.isNotEmpty(msg)) {
						if (allowIncomplete) {
							result.set("IncompleteMsg", msg);
						} else {
							throw new CapMessageException(msg, getClass());
						}
					}
				}
				result.set(KEY, true);	
			}catch(Exception e){
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}		
		}
		
		return defaultResult( params, meta, result);
	}
	
	private String checkIncompleteMsg(C310M01A meta, String chk_memo){
		if(true){
			if (Util.isEmpty(chk_memo)) {
				return "請輸入"+prop_cls3101m01.getProperty("C310M01A.chk_memo");
			}else{
				if (Util.notEquals(meta.getChk_memo(), chk_memo)) {
					return prop_cls3101m01.getProperty("C310M01A.chk_memo")+"長度超過限制";
				}				
			}

			if (Util.isEmpty(meta.getChk_result())) {
				return "請輸入"+prop_cls3101m01.getProperty("C310M01A.chk_result");
			}
		}		
		return "";
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params) throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;

	}
	
	/**
	 * 呈主管覆核
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		
		C310M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC310M01A_oid(mainOid);
			
			String errMsg = "";

			String docStatus = Util.trim(meta.getDocStatus());
			
			
			String nextStatus = "";
			DocLogEnum _DocLogEnum = null;
			if(Util.equals(CreditDocStatusEnum.海外_編製中.getCode(), docStatus)){
				if(Util.isEmpty(errMsg)){
//					errMsg = check_c243m01a_elf491(meta, elf491);
				}
				String[] formSelectBoss = params.getStringArray("selectBoss");
				String manager = Util.trim(params.getString("manager"));
				if(formSelectBoss==null||formSelectBoss.length==0){
					errMsg = "請輸入簽章欄";
				}
//				if(Util.isEmpty(Util.trim(manager))){
//					errMsg = "請輸入簽章欄";
//				}
				if(Util.isEmpty(errMsg)){
					save_c310m01e(meta.getMainId(), user, formSelectBoss,  manager);
					
					nextStatus = CreditDocStatusEnum.海外_待覆核.getCode();
					_DocLogEnum = DocLogEnum.FORWARD;
				}
			}else if(Util.equals(CreditDocStatusEnum.海外_待覆核.getCode(), docStatus)){
				//核定、退回
				if(Util.equals("核定", decisionExpr)){	
					if(Util.isEmpty(errMsg)){
//						errMsg = check_c243m01a_elf491(meta, elf491);
					}
					//===============
					if(Util.isEmpty(errMsg)){
						if(Util.equals(user.getUserId(), meta.getUpdater())){
							/* select * from com.berrorcode where code='EFD0053' 覆核人員不可與「經辦人員或其它覆核人員」為同一人
							 */
							errMsg = RespMsgHelper.getMessage("EFD0053");
						}else{
							nextStatus = CreditDocStatusEnum.海外_已核准.getCode();
							_DocLogEnum = DocLogEnum.ACCEPT;	
						}
					}
					
				}else if(Util.equals("退回", decisionExpr)){
					nextStatus = CreditDocStatusEnum.海外_編製中.getCode();
					_DocLogEnum = DocLogEnum.BACK;
				}
			}else if(Util.equals(CreditDocStatusEnum.海外_已核准.getCode(), docStatus)){	

			}
				
			if(Util.isNotEmpty(errMsg)){				
				throw new CapMessageException(errMsg, getClass());
			}else{
				if(Util.isEmpty(nextStatus)){
					throw new CapMessageException("流程異常["+docStatus+"]", getClass());
				}	
			}
			//=============================================
			if(true){				
				List<C900S02E> update_list = new ArrayList<C900S02E>();
				if(Util.equals(nextStatus, CreditDocStatusEnum.海外_已核准.getCode())){
					Timestamp nowTS = CapDate.getCurrentTimestamp();
					meta.setApprover(user.getUserId());
					meta.setApproveTime(nowTS);
					
					if(true){ //L4
						List<C310M01E> exist_m01e_list = clsService.findC310M01E(meta.getMainId());
						C310M01E managerC310M01E = get_single_staffJob(exist_m01e_list, UtilConstants.STAFFJOB.執行覆核主管L4);
						if(managerC310M01E==null){
							managerC310M01E = new C310M01E();
							
							managerC310M01E.setMainId(meta.getMainId());
							managerC310M01E.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);					
							managerC310M01E.setBranchType(user.getUnitType());
							managerC310M01E.setBranchId(user.getUnitNo());					
						}
						managerC310M01E.setStaffNo(meta.getApprover());
						managerC310M01E.setCreator(user.getUserId());
						managerC310M01E.setCreateTime(CapDate.getCurrentTimestamp());
						
						clsService.save(managerC310M01E);
					}
					
					C900S02E c900s02e = clsService.findC900S02E_mainId(meta.getRefMainId());
					if(c900s02e!=null){
						c900s02e.setChk_result(meta.getChk_result());
						c900s02e.setChk_memo(meta.getChk_memo());
						c900s02e.setUpdater(meta.getUpdater());
						c900s02e.setApprover(meta.getApprover());
						c900s02e.setApproveTime(meta.getApproveTime());
						//======
						update_list.add(c900s02e);
					}
				}else if(Util.equals(nextStatus, CreditDocStatusEnum.海外_編製中.getCode())){
					meta.setApprover(null);
					meta.setApproveTime(null);
					
					if(true){ //L4
						List<C310M01E> exist_m01e_list = clsService.findC310M01E(meta.getMainId());
						C310M01E c310m01e = get_single_staffJob(exist_m01e_list, UtilConstants.STAFFJOB.執行覆核主管L4);
						if(c310m01e!=null){
							clsService.daoDelete(c310m01e);			
						}
					}
				}else if(Util.equals(nextStatus, CreditDocStatusEnum.海外_待覆核.getCode())){
					meta.setApprover(null);
					meta.setApproveTime(null);
				}
				meta.setDocStatus(nextStatus);
				//用 daoSave 避免把 approver 寫到 updater
				clsService.daoSave(meta);
				
				if(true){
					for(C900S02E c900s02e : update_list){
						clsService.daoSave(c900s02e);						
					}
				}

				if(_DocLogEnum!=null){
					docLogService.record(meta.getOid(), _DocLogEnum);	
				}				
			}			
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(), user.getUserId());
		}
		return defaultResult( params, meta, result);
	}
	
	private void save_c310m01e(String mainId, MegaSSOUserDetails user , String[] formSelectBoss, String manager){
		
		List<C310M01E> exist_m01e_list = clsService.findC310M01E(mainId);
		List<C310M01E> del_m01e = new ArrayList<C310M01E>();
		List<C310M01E> save_m01e = new ArrayList<C310M01E>();
		
		if(true){
			List<C310M01E> L3_list = get_list_staffJob( exist_m01e_list, UtilConstants.STAFFJOB.授信主管L3);
			Set<String> existSet = new HashSet<String>();
			Set<String> newSet = new HashSet<String>();
			for(C310M01E exist: L3_list){
				String empNo = Util.trim(exist.getStaffNo());
				existSet.add(empNo);
			}
			for(String people : formSelectBoss) {
				newSet.add(Util.trim(people));
			}
			Set<String> delSet = LMSUtil.elm_onlyLeft(existSet, newSet);
			Set<String> addSet = LMSUtil.elm_onlyLeft(newSet, existSet);
			if(true){
				for(C310M01E c310m01e: exist_m01e_list){
					if(delSet.contains(c310m01e.getStaffNo())){
						del_m01e.add(c310m01e);
					}
				}
				Integer seq = 1;
				for(String empNo: formSelectBoss){
					if(!addSet.contains(empNo)){
						continue;
					}
					C310M01E c310m01e = new C310M01E();
					c310m01e.setMainId(mainId);
					// L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
					c310m01e.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
					c310m01e.setBranchType(user.getUnitType());
					c310m01e.setBranchId(user.getUnitNo());
					//=========
					c310m01e.setCreator(user.getUserId());
					c310m01e.setCreateTime(CapDate.getCurrentTimestamp());
					c310m01e.setStaffNo(empNo);
					c310m01e.setSeq(BigDecimal.valueOf(seq++));
					//=========
					save_m01e.add(c310m01e);
				}
			}
		}
		if(Util.isNotEmpty(Util.trim(manager))){
			C310M01E managerC310M01E = get_single_staffJob(exist_m01e_list, UtilConstants.STAFFJOB.單位授權主管L5);
			if(managerC310M01E==null){
				managerC310M01E = new C310M01E();
				
				managerC310M01E.setMainId(mainId);
				managerC310M01E.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);					
				managerC310M01E.setBranchType(user.getUnitType());
				managerC310M01E.setBranchId(user.getUnitNo());					
			}
			managerC310M01E.setStaffNo(manager);
			managerC310M01E.setCreator(user.getUserId());
			managerC310M01E.setCreateTime(CapDate.getCurrentTimestamp());
			
			save_m01e.add(managerC310M01E);
		}
		if(true){
			C310M01E apprC310M01E = get_single_staffJob(exist_m01e_list, UtilConstants.STAFFJOB.經辦L1);
			if(apprC310M01E==null){
				apprC310M01E = new C310M01E();
				
				apprC310M01E.setMainId(mainId);
				apprC310M01E.setStaffJob(UtilConstants.STAFFJOB.經辦L1);					
				apprC310M01E.setBranchType(user.getUnitType());
				apprC310M01E.setBranchId(user.getUnitNo());					
			}
			apprC310M01E.setStaffNo(user.getUserId());
			apprC310M01E.setCreator(user.getUserId());
			apprC310M01E.setCreateTime(CapDate.getCurrentTimestamp());
			
			save_m01e.add(apprC310M01E);
		}
		
		
		for(C310M01E c310m01e: save_m01e){
			clsService.save(c310m01e);
		}
		for(C310M01E c310m01e: del_m01e){
			clsService.daoDelete(c310m01e);
		}
	}
	
	private C310M01E get_single_staffJob(List<C310M01E> exist_m01e_list, String staffJob){
		List<C310M01E> list = get_list_staffJob(exist_m01e_list, staffJob);
		if(list.size()>0){
			return list.get(0);
		}
		return null;
	}
	
	private List<C310M01E> get_list_staffJob(List<C310M01E> exist_m01e_list, String staffJob){
		List<C310M01E> r = new ArrayList<C310M01E>(); 
		for(C310M01E c310m01e : exist_m01e_list){
			if(Util.equals(staffJob, c310m01e.getStaffJob())){
				r.add(c310m01e);
			}
		}
		return r;
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult delC310M01A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C310M01A c310m01a = clsService.findC310M01A_oid(mainOid);
		if(c310m01a!=null){
			clsService.delC310M01A(c310m01a);
		}		
		return result;
	}
	
	private CapAjaxFormResult defaultResult(PageParameters params,
			C310M01A meta, CapAjaxFormResult result) throws CapException {
		// required information
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));		
		result.set("custInfo",
				Util.trim(meta.getCustId()) + " " + Util.trim(meta.getDupNo())
						+ " " + Util.trim(meta.getCustName()));
		return result;
	}

	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public IResult test_record(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
			/*
$.ajax({
    handler: "cls3101m01formhandler", action: "test_record",
    data: {'cyc_mn':'2018-12-01', 'dupCnt_val':'30'},
    success: function(json){
	
    }
});
			 */
		String cyc_mn = Util.trim(params.getString("cyc_mn"));
		Integer dupCnt_val = params.getInt("dupCnt_val", 10);
		if(Util.isEmpty(cyc_mn)){
			cyc_mn = TWNDate.toAD(CapDate.addMonth(CrsUtil.get_sysMonth_1st(), -1));
		}
		
		
		result.set("cyc_mn", cyc_mn);		
		if(true){
			CapAjaxFormResult result_dw = new CapAjaxFormResult();
			
			Map<String, Object> latest_done_map = dwdbBASEService.findLnCustRel_done_history(CrsUtil.FN_KEY_C900S03C_DONE_CUST_KEY);
			Date done_cyc_mn = (Date)MapUtils.getObject(latest_done_map, "CYC_MN");			
			String str_done_cyc_mn = TWNDate.toAD(done_cyc_mn);
			result_dw.set("dw_latestLnCustRel", StringUtils.substring(str_done_cyc_mn, 0, 7));
			result_dw.set("dw_isLnCustRel_done", dwdbBASEService.isLnCustRel_done(cyc_mn, CrsUtil.FN_KEY_C900S03C_DONE_CUST_KEY));
			result_dw.set("dw_countLnCustRel", dwdbBASEService.countLnCustRel_done_cust_key(cyc_mn, CrsUtil.FN_KEY_C900S03C_DONE_CUST_KEY));
			result_dw.set("dw_countLnCustBr", dwdbBASEService.countLnCustBr(cyc_mn));
			
			String[] rel_type_arr = {"1", "2", "3"};
			int max_show_size = 20;
			for(String rel_type : rel_type_arr){
				result_dw.set("dw_countLnCustRel_"+rel_type,  dwdbBASEService.countLnCustRel_rel_type(cyc_mn,rel_type));
				
				List<Map<String, Object>> top_dupCnt_list = dwdbBASEService.findLnCustRel_top_dup_cnt(cyc_mn,rel_type, dupCnt_val);
				HashMap<String, Object> rel_data_map = new HashMap<String, Object>();
				JSONArray jsonArray = new JSONArray();
				int cnt = 0;
				for(Map<String, Object> dupCnt_map: top_dupCnt_list){					
					JSONObject obj = new JSONObject();								
					obj.put("TEXT", MapUtils.getString(dupCnt_map, "TEXT"));
					obj.put("DUP_CNT", MapUtils.getString(dupCnt_map, "DUP_CNT"));
					jsonArray.add(obj);	
					++cnt;
					if(cnt>=max_show_size){
						break;
					}
				}
				
				rel_data_map.put("arr_top_"+max_show_size, jsonArray);
				rel_data_map.put("arr_tot_cnt", top_dupCnt_list.size());
				result_dw.set("dw_lnCustRel_dupCnt"+rel_type,  new CapAjaxFormResult(rel_data_map));
			}
			// =====
			result.set("result_dw", result_dw);
		}	
		
		if(true){
			CapAjaxFormResult result_eloan = new CapAjaxFormResult();
			BigDecimal cnt_c900s03c = BigDecimal.valueOf(clsService.count_C900M03A_dtl(CrsUtil.FN_KEY_C900S03C, cyc_mn, ""));
			BigDecimal cnt_c900s03d = BigDecimal.valueOf(clsService.count_C900M03A_dtl(CrsUtil.FN_KEY_C900S03D, cyc_mn, ""));
			BigDecimal cnt_c900s03e = BigDecimal.valueOf(clsService.count_C900M03A_dtl(CrsUtil.FN_KEY_C900S03E, cyc_mn, ""));
			result_eloan.set("cnt_c900s03c", cnt_c900s03c);
			result_eloan.set("cnt_c900s03d", cnt_c900s03d);
			result_eloan.set("cnt_c900s03e", cnt_c900s03e);
			
			String fn = CrsUtil.FN_KEY_C900S02E;
			String genDate = StringUtils.replace(cyc_mn, "-", "");
			String genTime="000000";
			C900M03A c900m03a_ctl = clsService.findC900M03A_fnGenDateGenTime(fn, genDate, genTime);
			result_eloan.set("ctl_"+fn, c900m03a_ctl!=null?"done":"not yet");
			// =====
			result.set("result_eloan", result_eloan);
		}				
		return result;
	}
}
