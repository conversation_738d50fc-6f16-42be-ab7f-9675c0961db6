/*
 * L140M01V.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import com.mega.eloan.lms.validation.group.Check;
import tw.com.iisi.cap.model.GenericBean;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.sql.Timestamp;

/** 連鎖加盟貸款資訊檔 **/
@Entity
//@EntityListeners({DocumentModifyListener.class})
@Table(name="L140M01V", uniqueConstraints = @UniqueConstraint(columnNames = {"cntrNo"}))
public class L140M01V extends GenericBean {

    private static final long serialVersionUID = 1L;

    /**
     * 額度序號<p/>
     * 主事業體：cntrNo = mCntrNo<br/>
     *  加盟：cntrNo != mCntrNo
     */
    @Id
//    @GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
    @Size(max=12)
    @Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)", nullable=false)
    private String cntrNo;

    /** 統一編號 **/
    @Size(max=10)
    @Column(name="BIZID", length=10, columnDefinition="VARCHAR(10)")
    private String bizId;

    /** 重覆序號 **/
    @Size(max=1)
    @Column(name="BIZDUPNO", length=1, columnDefinition="CHAR(1)")
    private String bizDupNo;

    /** 主事業體_額度序號 **/
    @Size(max=12)
    @Column(name="MCNTRNO", length=12, columnDefinition="CHAR(12)")
    private String mCntrNo;

    /** 主事業體_統一編號 **/
    @Size(max=10)
    @Column(name="MAINBIZID", length=10, columnDefinition="VARCHAR(10)")
    private String mainBizId;

    /** 主事業體_重覆序號 **/
    @Size(max=1)
    @Column(name="MAINBIZDUPNO", length=1, columnDefinition="CHAR(1)")
    private String mainBizDupNo;

    /** 主事業體_客戶名稱 **/
    @Size(max=120)
    @Column(name="MAINBIZNAME", length=120, columnDefinition="VARCHAR(120)")
    private String mainBizName;

    /** 加盟戶數 **/
    @Digits(integer=5, fraction=0, groups = Check.class)
    @Column(name="JOINNUM", columnDefinition="DECIMAL(5,0)")
    private Integer joinNum;

    /**
     * 現請額度－幣別<p/>
     * 同L140M01A
     */
    @Size(max=3)
    @Column(name="CURRENTAPPLYCURR", length=3, columnDefinition="CHAR(3)")
    private String currentApplyCurr;

    /**
     * 現請額度－金額<p/>
     * 同L140M01A
     */
    @Digits(integer=17, fraction=2, groups = Check.class)
    @Column(name="CURRENTAPPLYAMT", columnDefinition="DECIMAL(17,2)")
    private BigDecimal currentApplyAmt;

    /** 建立人員號碼 **/
    @Size(max=6)
    @Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
    private String creator;

    /** 建立日期 **/
    @Column(name="CREATETIME", columnDefinition="TIMESTAMP")
    private Timestamp createTime;

    /** 異動人員號碼 **/
    @Size(max=6)
    @Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
    private String updater;

    /** 異動日期 **/
    @Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
    private Timestamp updateTime;

    /** 刪除時間 **/
    @Column(name="DELETETIME", columnDefinition="TIMESTAMP")
    private Timestamp deleteTime;

    /**
     * 取得額度序號<p/>
     * 主事業體：cntrNo = mCntrNo<br/>
     *  加盟：cntrNo != mCntrNo
     */
    public String getCntrNo() {
        return this.cntrNo;
    }
    /**
     *  設定額度序號<p/>
     *  主事業體：cntrNo = mCntrNo<br/>
     *  加盟：cntrNo != mCntrNo
     **/
    public void setCntrNo(String value) {
        this.cntrNo = value;
    }

    /** 取得統一編號 **/
    public String getBizId() {
        return this.bizId;
    }
    /** 設定統一編號 **/
    public void setBizId(String value) {
        this.bizId = value;
    }

    /** 取得重覆序號 **/
    public String getBizDupNo() {
        return this.bizDupNo;
    }
    /** 設定重覆序號 **/
    public void setBizDupNo(String value) {
        this.bizDupNo = value;
    }

    /** 取得主事業體_額度序號 **/
    public String getMCntrNo() {
        return this.mCntrNo;
    }
    /** 設定主事業體_額度序號 **/
    public void setMCntrNo(String value) {
        this.mCntrNo = value;
    }

    /** 取得主事業體_統一編號 **/
    public String getMainBizId() {
        return this.mainBizId;
    }
    /** 設定主事業體_統一編號 **/
    public void setMainBizId(String value) {
        this.mainBizId = value;
    }

    /** 取得主事業體_重覆序號 **/
    public String getMainBizDupNo() {
        return this.mainBizDupNo;
    }
    /** 設定主事業體_重覆序號 **/
    public void setMainBizDupNo(String value) {
        this.mainBizDupNo = value;
    }

    /** 取得主事業體_客戶名稱 **/
    public String getMainBizName() {
        return this.mainBizName;
    }
    /** 設定主事業體_客戶名稱 **/
    public void setMainBizName(String value) {
        this.mainBizName = value;
    }

    /** 取得加盟戶數 **/
    public Integer getJoinNum() {
        return this.joinNum;
    }
    /** 設定加盟戶數 **/
    public void setJoinNum(Integer value) {
        this.joinNum = value;
    }

    /**
     * 取得現請額度－幣別<p/>
     * 同L140M01A
     */
    public String getCurrentApplyCurr() {
        return this.currentApplyCurr;
    }
    /**
     *  設定現請額度－幣別<p/>
     *  同L140M01A
     **/
    public void setCurrentApplyCurr(String value) {
        this.currentApplyCurr = value;
    }

    /**
     * 取得現請額度－金額<p/>
     * 同L140M01A
     */
    public BigDecimal getCurrentApplyAmt() {
        return this.currentApplyAmt;
    }
    /**
     *  設定現請額度－金額<p/>
     *  同L140M01A
     **/
    public void setCurrentApplyAmt(BigDecimal value) {
        this.currentApplyAmt = value;
    }

    /** 取得建立人員號碼 **/
    public String getCreator() {
        return this.creator;
    }
    /** 設定建立人員號碼 **/
    public void setCreator(String value) {
        this.creator = value;
    }

    /** 取得建立日期 **/
    public Timestamp getCreateTime() {
        return this.createTime;
    }
    /** 設定建立日期 **/
    public void setCreateTime(Timestamp value) {
        this.createTime = value;
    }

    /** 取得異動人員號碼 **/
    public String getUpdater() {
        return this.updater;
    }
    /** 設定異動人員號碼 **/
    public void setUpdater(String value) {
        this.updater = value;
    }

    /** 取得異動日期 **/
    public Timestamp getUpdateTime() {
        return this.updateTime;
    }
    /** 設定異動日期 **/
    public void setUpdateTime(Timestamp value) {
        this.updateTime = value;
    }

    /** 取得刪除時間 **/
    public Timestamp getDeleteTime() {
        return this.deleteTime;
    }
    /** 設定刪除時間 **/
    public void setDeleteTime(Timestamp value) {
        this.deleteTime = value;
    }
}
