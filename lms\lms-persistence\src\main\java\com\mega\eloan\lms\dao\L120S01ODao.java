/* 
 * L120S01ODao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S01O;

/** 信用風險管理關係戶帳務明細檔 **/
public interface L120S01ODao extends IGenericDao<L120S01O> {

	L120S01O findByOid(String oid);

	List<L120S01O> findByMainId(String mainId);

	List<L120S01O> findByIndex01(String mainId, String custId, String dupNo,
			String relType);

	List<L120S01O> findByIndex02(String mainId, String custId, String dupNo);

	public List<L120S01O> findByCustId(String mainId, String custId,
			String dupNo);

	List<L120S01O> findByCustIdRelType(String mainId, String custId,
			String dupNo, String relType);
	int deleteByKey(String mainId, String custId, String dupNo);
}