
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L140MM3CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140MM3C;


@Repository
public class L140MM3CDaoImpl extends LMSJpaDao<L140MM3C, String> implements
		L140MM3CDao {
	
	
	@Override
	public L140MM3C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	
	@Override
	public List<L140MM3C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("createTime", false);
		search.setMaxResults(Integer.MAX_VALUE);

		List<L140MM3C> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L140MM3C> findCurrentByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "flag", "Y");
		search.addOrderBy("createTime", false);
		search.setMaxResults(Integer.MAX_VALUE);

		List<L140MM3C> list = createQuery(search).getResultList();
		return list;
	}
	
	
	@Override
	public List<L140MM3C> findLastByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "flag", "N");
		search.addOrderBy("createTime", false);
		search.setMaxResults(Integer.MAX_VALUE);

		List<L140MM3C> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L140MM3C findByMainIdEstateType(String mainId, String estateType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "flag", "Y");
		search.addSearchModeParameters(SearchMode.EQUALS, "estateType", estateType);
		 
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<L140MM3C> findLastestByMainIdEstateType(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "flag", "N");
		search.addOrderBy("createTime", true);
		search.setMaxResults(Integer.MAX_VALUE);

		List<L140MM3C> list = createQuery(search).getResultList();
		return list;
	}
}