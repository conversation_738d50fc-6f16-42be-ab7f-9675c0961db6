/* 
 * LMS9511V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;

/**
 * <pre>
 * 管理報表  最新資料(國內)
 * </pre>
 * 
 * @since 2013/1/3
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/3,ice,new
 *          </ul>
 */
@Controller
@RequestMapping("/rpt/lms9511v01")
public class LMS9511V01Page extends AbstractEloanInnerView {

	public LMS9511V01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 外Grid上的Button
		addToButtonPanel(model, 
				LmsButtonEnum.Filter,LmsButtonEnum.View,
				LmsButtonEnum.PullinReport,LmsButtonEnum.Print,
				LmsButtonEnum.Upload,LmsButtonEnum.CreateReport,
				LmsButtonEnum.SendDocTypeReport,LmsButtonEnum.LongError,
				LmsButtonEnum.LongViewMemo,LmsButtonEnum.ReturnPage
				);
		
		renderJsI18N(LMS9511V01Page.class);
	}// ;
}
