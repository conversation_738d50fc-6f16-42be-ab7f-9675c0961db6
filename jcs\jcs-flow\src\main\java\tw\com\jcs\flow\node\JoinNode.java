package tw.com.jcs.flow.node;

import java.util.List;

import tw.com.jcs.flow.core.FlowInstanceImpl;
import tw.com.jcs.flow.core.FlowPersistence;

/**
 * <pre>
 * JOIN節點的處理邏輯：<br>
 *
 * 1. 所有流程進入JOIN節點，有子流程，且還在執行中 handle(如果還沒handle過)，等待<br>
 *
 * 2. 父流程進入JOIN節點，無子流程(或是子流程都已執行完) handle(如果還沒handle過)，直接換到JOIN節點，並next<br>
 *
 * 3. 子流程進入JOIN節點，無次子流程(或是次子流程都已執行完) handle(如果還沒handle過)，直接換到JOIN節點 然後判斷自己是否為父流程中，該JOIN對應FORK節點的子流程 (1) 是，則結束 (2) 否，則next 呼叫父流程的next<br>
 *
 * P.S. 子流程的定義為該JOIN節點對應的FORK所產生的子流程 子流程應該在FORK的對應JOIN節點結束(父流程則等待)
 * </pre>
 */
public class JoinNode extends FlowNode {

    static final String HANDLE_DONE = "_handle_done";

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.node.FlowNode#next(tw.com.jcs.flow.core.FlowInstanceImpl)
     */
    @Override
    public void next(FlowInstanceImpl instance) {
        handleOrNot(instance);
        FlowPersistence persist = instance.getEngine().getPersistence();

        List<Object> subList = null;
        String forkName = ForkNode.peekFromForkStack(instance);
        if (forkName != null) {
            subList = instance.getSubInstanceList().get(forkName);
        }
        if (subList == null || persist.queryForInstance(subList.toArray(new Object[subList.size()])).size() == 0) {
            removeHandle(instance);
            finishCurrentNode(instance);
            changeToThisNode(instance);

            if (forkName != null) {
                ForkNode.popFromForkStack(instance);
                instance.getSubInstanceList().remove(forkName);
            }

            if (instance.getParentInstanceId() == null) {
                instance.next();
            } else {
                if (isRightNode(instance)) {
                    finishCurrentNode(instance);
                } else {
                    finishCurrentNode(instance);
                    instance.next();
                }
            }
        }
    }

    /**
     * 設定節點屬性
     * 
     * @param instance
     */
    void handleOrNot(FlowInstanceImpl instance) {
        String hdle = "_" + name + HANDLE_DONE;
        if (instance.getAttribute(hdle) == null) {
            instance.handle();
            instance.setAttribute(hdle, true);
        }
    }

    /**
     * 移除節點屬性
     * 
     * @param instance
     */
    void removeHandle(FlowInstanceImpl instance) {
        String hdle = "_" + name + HANDLE_DONE;
        instance.removeAttribute(hdle);
    }

    /**
     * 取得所有子流程的ID，判斷instance是否包含在其中
     * 
     * @param instance
     * @return
     */
    boolean isRightNode(FlowInstanceImpl instance) {
        FlowPersistence persist = instance.getEngine().getPersistence();
        FlowInstanceImpl pInst = (FlowInstanceImpl) persist.getInstance(instance.getParentInstanceId());
        List<Object> list = pInst.getSubInstanceList().get(ForkNode.peekFromForkStack(pInst));
        return list.contains(instance.getId());
    }

}
