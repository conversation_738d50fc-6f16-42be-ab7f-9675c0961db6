var initDfd = initDfd || new $.Deferred();
initDfd.done(function(json){
	if(json.page=="02"){
		if(json.mainDocStatus=="010"){
			$("#btn_produce8_1").show();
		}else{
			$("#btn_produce8_1").hide();
			$("#thisSamplingRate").prop('disabled', true);
		}
		build_c240m01c(json);
		build_attch(json);
		
		$("#filter_gridProjectCreditLoanBtn").click(function(){	
			
			var _form = "div_detail_projectCreditLoan_form";
			var grid_id = "gridProjectCreditLoan";
					
			if($("#"+grid_id+".ui-jqgrid-btable").length >0){
				$("#"+grid_id).jqGrid("setGridParam", {
					postData : $("#"+_form).serializeData(),
					search : true
				}).trigger("reloadGrid");
			}
		});
	}	
	//===================================================
	function build_c240m01c(jsonParam){
		if(true ){			
			if(jsonParam.c240m01c_R95_1_oid && jsonParam.c240m01c_R95_1_oid !=""){
				$("#tr_c240m01c_R95_1").show();
				
				// J-109-0372, 申請單號 (109) 第 2669 號, 109.8.25兆銀總授審字第1090045985號函,疑似人頭戶專案覆審
				//(1)第一年全面辦理專案覆審 (2)嗣後逐年按受檢追蹤對象之10%範圍內
				if(jsonParam.c240m01c_R95_1.hasData == "Y"){
					var elementDom = $("#c240m01c_R95_1_div")
					elementDom.html(build_str_c240m01c_R95_1_withJSON(jsonParam.c240m01c_R95_1) );
					//$("#c240m01c_R95_1_div").html(build_str_c240m01c_R95_1_withJSON(passParam.c240m01c_R95_1) );	
					//$("#c240m01c_R95_1_div").injectData(build_str_c240m01c_R95_1_withJSON(passParam.c240m01c_R95_1) );
					$("#btn_produceR95_1").show();
				}else{
					$("#c240m01c_R95_1_div").html("此分行無「疑似人頭戶」追蹤對象");
					$("#btn_produceR95_1").hide();
				}			
			}else{
				// 維持隱藏 $("#c240m01c_R95_1_div").closest( "tr" ).hide();
			}
		}
		//~~~~~~~~
		if(true){
			if(jsonParam.c240m01c_R1R2S_oid && jsonParam.c240m01c_R1R2S_oid !=""){
				var elementDom = $("#c240m01c_R1R2S_div");
				elementDom.html(build_str_c240m01c_R1R2S_withJSON(jsonParam.c240m01c_R1R2S) );
				//$("#c240m01c_R1R2S_div").html(build_str_c240m01c_R1R2S_withJSON(passParam.c240m01c_R1R2S) );
				//$("#c240m01c_R1R2S_div").injectData(build_str_c240m01c_R1R2S_withJSON(passParam.c240m01c_R1R2S) );
			}else{
				$("#c240m01c_R1R2S_div").closest( "tr" ).hide();
			}	
		}		
		//~~~~~~~~
		if(true){
			if(jsonParam.c240m01c_R14_oid && jsonParam.c240m01c_R14_oid !=""){
				var elementDom = $("#c240m01c_R14_div");
				elementDom.html(build_str_c240m01c_R1R2S_withJSON(jsonParam.c240m01c_R14) );
				//$("#c240m01c_R14_div").html(build_str_c240m01c_R1R2S_withJSON(passParam.c240m01c_R14) );
				//$("#c240m01c_R14_div").injectData(build_str_c240m01c_R1R2S_withJSON(passParam.c240m01c_R14) );
			}else{
				$("#c240m01c_R14_div").closest( "tr" ).hide();
			}	
		}		
		//~~~~~~~~
		if(true){
			if(jsonParam.c240m01c_R_projectCreditLoan_oid && jsonParam.c240m01c_R_projectCreditLoan_oid !=""){
				var elementDom = $("#c240m01c_R_projectCreditLoan_div");
				elementDom.html(build_str_c240m01c_R_projectCreditLoan_withJSON(jsonParam.c240m01c_R_projectCreditLoan) );
				//$("#c240m01c_R_projectCreditLoan_div").html(build_str_c240m01c_R_projectCreditLoan_withJSON(passParam.c240m01c_R_projectCreditLoan) );
				//$("#c240m01c_R_projectCreditLoan_div").injectData(build_str_c240m01c_R_projectCreditLoan_withJSON(passParam.c240m01c_R_projectCreditLoan) );	
			}else{
				$("#c240m01c_R_projectCreditLoan_div").closest( "tr" ).hide();
			}			
		}
	}
	function build_str_c240m01c_R95_1_withJSON(rtnObj){
		return build_str_c240m01c_R95_1(rtnObj.allCnt, rtnObj.baseYM, rtnObj.baseYM_cnt, rtnObj.activeCnt 
				, rtnObj.doneBegDate, rtnObj.doneEndDate, rtnObj.doneCnt, rtnObj.doneRate, rtnObj.c240_totRetrialCnt); 
	}
	function build_str_c240m01c_R95_1(allCnt, baseYM, baseYM_cnt, activeCnt, doneBegDate, doneEndDate, doneCnt, doneRate, c240_totRetrialCnt){
		return "此分行追蹤對象共 "+encodeHTML(allCnt)+" 筆"
		+"(年度基準日 "+encodeHTML(baseYM)+" 有效戶數："+encodeHTML(baseYM_cnt)+" 筆)"
		+"<br/>"
		+"【"+encodeHTML(doneBegDate)+" ~ "+encodeHTML(doneEndDate)+" 】 已抽樣( "+encodeHTML(doneCnt)+" 筆)"
		+"&nbsp;"
		+"比率 "+encodeHTML(doneRate)+" ％"
		+"&nbsp;"+"&nbsp;"+"&nbsp;"
		+" (本次抽樣戶數為："+encodeHTML(c240_totRetrialCnt)+" 筆)";
	}
	
	function build_str_c240m01c_R1R2S_withJSON(rtnObj){
		return build_str_c240m01c_R1R2S(rtnObj.allYearStr, rtnObj.allCnt, rtnObj.doneCnt, rtnObj.doneRate, rtnObj.c240_totRetrialCnt); 
	}
	function build_str_c240m01c_R1R2S(allYearStr, allCnt,  doneCnt, doneRate, c240_totRetrialCnt){
		return "【"+encodeHTML(allYearStr)+" 】年共 "+encodeHTML(allCnt)+" 筆；"
		+"已抽樣( "+encodeHTML(doneCnt)+" 筆)"
		+"&nbsp;"
		+"比率 "+encodeHTML(doneRate)+" ％。"
		+"<br/>"
		+" (本次抽樣戶數為："+encodeHTML(c240_totRetrialCnt)+" 筆)";
	}
	
	function build_str_c240m01c_R_projectCreditLoan_withJSON(rtnObj){
		return build_str_c240m01c_R_projectCreditLoan(
				rtnObj.allBegDate, rtnObj.allEndDate, rtnObj.allCnt  
				, rtnObj.doneCnt, rtnObj.doneRate
				, rtnObj.activeCnt, rtnObj.c240_totRetrialCnt);
	}
	function build_str_c240m01c_R_projectCreditLoan(allBegDate, allEndDate, allCnt,  doneCnt, doneRate, activeCnt, c240_totRetrialCnt){
		return ""
		+"【"+encodeHTML(allBegDate)+" ~ "+encodeHTML(allEndDate)+" 】共 "+encodeHTML(allCnt)+" 筆；"
		+"已抽樣( "+encodeHTML(doneCnt)+" 筆)"
		+"&nbsp;"
		+"比率 "+encodeHTML(doneRate)+" ％。"
		+"(現有效戶數為："+encodeHTML(activeCnt)+" 筆)"
		+"<br/>"
		+" (本次抽樣戶數為："+encodeHTML(c240_totRetrialCnt)+" 筆)";
	}
	//===================================================
	function build_attch(passParam){
		$.each(['excelFile', 'branchFile', 'chkExcelFile', 'ejcicFile'], function(idx, divId) {
			var dyna = [];
			{
				var arr = passParam.attch[divId];
				$.each(arr, function(idx, jsonItem) {
					dyna.push("<span class='linkDocFile' oid='"+jsonItem.oid+"'>"+jsonItem.srcFileName+"</span>&nbsp;&nbsp;&nbsp;&nbsp;"
					+"("+jsonItem.uploadTime+")<sub class='delDocFile' oid='"+jsonItem.oid+"'>刪</sub>");					
				});	
			}
			var elementDom = $("#"+divId);
			elementDom.html(dyna.join("<br/>"));
			//$("#"+divId).html(dyna.join("<br/>"));
			//$("#"+divId).injectData(dyna.join("<br/>"));
		});	
		$('span.linkDocFile').click(function(){
			var oid = jQuery(this).attr("oid");
			
			$.capFileDownload({
		        handler:"simplefiledwnhandler",
		        data : {
		            fileOid:oid
		        }
			});				
		});
		$('sub.delDocFile').click(function(){
			var oid = jQuery(this).attr("oid");
			//ui_lms2401.msg05=是否刪除檔案?
			CommonAPI.confirmMessage(i18n.lms2401m01["ui_lms2401.msg05"], function(b){
	            if (b) {
	            	$.ajax({
                        handler: _handler,
                        data: {
                            formAction: "deleteUploadFile",
                            fileOid: oid
                        }
                        }).done(function(obj){
                        	build_attch(obj);
                    });
	            }
	        });
							
		});
	}
	function _grid_click(rowObject){
		$.ajax({ handler: 'lms2411m01formhandler', data: {formAction: "checkFlow", 'mainOid':rowObject.oid, 'act':'initFlow'} }).done(function(json_checkFlow){
			if(json_checkFlow.passedFlag=="Y"){
				$.ajax({ handler: 'lms2411m01formhandler', data: {formAction: "openPageParam", 'mainOid':rowObject.oid}
	                }).done(function(json_openPageParam){  
	                	var postData = {
	                		'mainOid': json_openPageParam.mainOid, 
	                		'mainId': json_openPageParam.mainId,
	                		'mainDocStatus': json_openPageParam.mainDocStatus
	                	}
	                	//若 A 已開啟工作底稿的001號覆審報告表,而 B 要開啟002號覆審報告表
	                	//在 B 的json_openPageParam 會多出{openerLockDoc:true}
	                	$.form.submit({ url:'../lms2411m01/02', data:postData, target:rowObject.oid});
	            });	
			}            
        });
	}
	function gridClick(cellvalue, options, rowObject){
		_grid_click(rowObject);
	}
	function gridClickUnderGrpCntrNo(cellvalue, options, rowObject){
		_grid_click(rowObject);
	}
	//===================================================
	var $gridview = $("#gridview").iGrid({
        handler: 'lms2401gridhandler',        
        height: 370,
        postData: {
            mainId: json.mainId,
            formAction: "queryList"
        },
        needPager: false,        
        shrinkToFit: false,
        multiselect: true,        
        colModel: [
          {//"序號",
            colHeader:i18n.lms2401m01['grid.projectNo'], name: 'projectNo', width: 20, sortable: true, align: "center" 
          }, {//"覆審",
            colHeader: i18n.lms2401m01['grid.retrialYN'], name: 'retrialYN_NCreatData', width: 20, sortable: true, align: "center",
            formatter: function(cellvalue, options, rowObject){
                if (cellvalue == "Y|SYS") {
                    return "<img src='webroot/img/lms/V.png'>";
                }else if (cellvalue == "Y|PEO") {
                    return "<img src='webroot/img/lms/V2.png'>";
                }else if (cellvalue == "N|SYS") {
                	return "<img src='webroot/img/lms/X.png'>";
                }else{
                	return cellvalue;
                }
            } 
          },{//"客戶統編"
            colHeader: i18n.lms2401m01['grid.custId'], name: 'custId_dupNo', width: 90, sortable: true, align: "left", formatter: 'click', onclick: gridClick
          },{//"客戶姓名",
            colHeader: i18n.lms2401m01['grid.custName'], name: 'custName', width: 120, sortable: true, align: "left"
          },{//"額度"
            colHeader: i18n.lms2401m01['grid.totQuota'], name: 'totQuota', width: 85, sortable: true, align: "right"
          }, {//"上次覆審日期",
            colHeader: i18n.lms2401m01['C241M01A.lastRetrialDate'], name: 'lastRetrialDate', width: 65, sortable: true, align: "center"
          }, {//"最遲應覆審日期",
            colHeader: i18n.lms2401m01['C241M01A.shouldReviewDate'], name: 'shouldReviewDate', width: 65, sortable: true, align: "center"
          }, { //"規則",
        	colHeader: i18n.lms2401m01['grid.retrialKind'], name: 'retrialKind', width: 50, sortable: true, align: "left"
        }, {//"覆審流程點",
            colHeader: i18n.lms2401m01['grid.docStatusDesc'], name: 'docStatusDesc',width: 100, sortable: true,align: "left"            
        }, {//"上傳註記",
            colHeader: i18n.lms2401m01['grid.uploadFlag'], name: 'uploadFlag',width: 15, sortable: true,align: "left"
        }, {//"上傳人員",
            colHeader: i18n.lms2401m01['label.c241m01a_approver'], name: 'approverStaff',width: 60, sortable: true,align: "left"
        }, {//"覆審人員",
            colHeader: i18n.lms2401m01['grid.retrialStaff'], name: 'retrialStaff',width: 60, sortable: true,align: "left"
        }, {
            colHeader: "實地", name: '_realFmt',width: 30, sortable: false, align: "center"
        }, {//"上次覆審日期",
            colHeader: i18n.lms2401m01['C241M01A.lastRealDt'], name: 'lastRealDt', width: 65, sortable: true, align: "center"          
        }, {//"新/舊/人工/團貸",
            colHeader: " ", name: 'newCase',width: 50, sortable: false, align: "center",
            formatter: function(cellvalue, options, rowObject){
            	if(cellvalue=="【團貸】"){
            		return "<u>"+cellvalue+"</u>";
            	}else{
            		return cellvalue;	
            	}                                
            }
        }, {//"不覆審原因",
            colHeader: i18n.lms2401m01['grid.nCkdFlag'], name: 'nCkdFlag',width: 90, sortable: true,align: "left"            
        }, {//"不覆審說明",
            colHeader: i18n.lms2401m01['grid.nckdDetail'], name: 'nckdDetail',width: 80, sortable: true,align: "left"
        }, {//"覆審意見",
            colHeader: i18n.lms2401m01['grid.condition'], name: 'condition',width: 90, sortable: true,align: "left"
        }, {//"覆審意見",
        	//J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
            colHeader: "RPA結果", name: 'status',width: 60, sortable: true,align: "left"    	
        }
        , { name: 'c240m01a_mainId', hidden: true }
        , { name: 'movetobr_flag', hidden: true }
        , { name: 'docKind', hidden: true }
        , { name: 'grpCntrNo', hidden: true }
        , { name: 'retrialYN', hidden: true }
        , { name: 'nCreatData', hidden: true }
        , { name: 'custId', hidden: true }
        , { name: 'dupNo', hidden: true }        
        , { name: 'oid', hidden: true }
        , { name: 'mainId', hidden: true }
        ],        
        ondblClickRow: function(rowid){
        	gridClick(null, null, $("#gridview").getRowData(rowid));
        },
        onCellSelect : function(rowId, iCol, cellcontent) {   
        	/*
        	 * 在 formatter 判斷值為【團貸】 用<u> </u> 包住
        	 * 但 firefox 是小寫 <u>
        	 * 而  IE 10       是大寫 <U>
        	 * 所以用 .text()來判斷
        	 */
        	if($(cellcontent).text()=="【團貸】"){        		
        		$("#gridview").jqGrid ('toggleSubGridRow', rowId);
        	}
        },
        subGrid: true,
        subGridRowExpanded: function(subgrid_id, row_id) {
           var subgrid_table_id  = subgrid_id+"_t";
           var row_data = $("#gridview").getRowData(row_id);
           var elementDom = $("#"+subgrid_id).closest("td").before("<td colspan='2'>&nbsp;</td>" ).attr("colspan", 7);
           elementDom.html("<div id='"+subgrid_id+"_info'/><div id='"+subgrid_table_id+"' style='margin-left:60px;' />");
           /*
           $("#"+subgrid_id).closest("td")
           .before("<td colspan='2'>&nbsp;</td>" )
           .attr("colspan", 7)
           */
           //.html("<div id='"+subgrid_id+"_info'/><div id='"+subgrid_table_id+"' style='margin-left:60px;' />");
           //.injectData("<div id='"+subgrid_id+"_info'/><div id='"+subgrid_table_id+"' style='margin-left:60px;' />");
           //jqGrid#BBCCFF
           var elementDom2 = $("#"+subgrid_id+"_info");
           elementDom2.html("團貸編號:"+row_data.grpCntrNo);
           //$("#"+subgrid_id+"_info").html("團貸編號:"+row_data.grpCntrNo);
           //$("#"+subgrid_id+"_info").injectData("團貸編號:"+row_data.grpCntrNo);
           /*
           click subgrid,會連 母戶 都一併開啟,先停用
           $("#"+subgrid_table_id).iGrid({
		        handler: 'lms2401gridhandler',
		        height: 120, //設定高度
				//groupToDetailOption: groupToDetailOption,
		        postData: {
		            formAction: "queryListUnderGrpCntrNo",
		            c240m01a_mainId: row_data.c240m01a_mainId,
		            grpCntrNo: row_data.grpCntrNo
		        },
		        colModel: [
		         {//"客戶統編",
		            colHeader: i18n.lms2401m01['grid.custId'],name: 'custId_dupNo',width: 50,sortable: true,align: "left"
		            	, formatter: 'click', onclick: gridClickUnderGrpCntrNo
		        }, {//"客戶姓名",
		            colHeader: i18n.lms2401m01['grid.custName'],name: 'custName',width: 90,sortable: true, align: "left"
		        }, {
		            colHeader: i18n.lms2401m01['grid.curr'],name: 'totBalCurr',width: 50,sortable: true, align: "center"
		        }, {
		            colHeader: i18n.lms2401m01['grid.totBal'],name: 'totBal',width: 50,sortable: true,align: "right"        
		        }		        
		        , { name: 'oid', hidden: true }
		        , { name: 'mainId', hidden: true }
		        ],
	            ondblClickRow: function(rowid, status, e) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
	            	gridClickUnderGrpCntrNo(null, null, $("#"+subgrid_table_id).getRowData(rowid));
	            }
		    }); 
		    */           
        }        
    });
	$('#gridview').jqGrid('hideCol', 'subgrid');
		
	$("#btn_produce8_1").click(function(){		
		var tabForm = $("#tabForm");
		if (tabForm.valid()) {
            $.ajax({
                type: "POST",
                handler: _handler,
                data: $.extend({}, tabForm.serializeData(), {
                    formAction: "produce8_1",
                    mainOid: json.mainOid
                })
                }).done(function(responseData){
                	tabForm.injectData(responseData);
                	//更新LMS2401M01的 Grid
                	$gridview.trigger("reloadGrid");
                	//更新 LMS2405V01 的 Grid
                	CommonAPI.triggerOpener("gridview", "reloadGrid");
            });
        }	
	});
	
	$("#btn_produceR95_1").click(function(){ //J-109-0372 疑似人頭戶專案覆審(1)第一年全面辦理專案覆審 (2)嗣後逐年按受檢追蹤對象之10%範圍內
    	proc_Rule95_1();
	});
	
	$("#btn_produceR1R2S").click(function(){		
		proc_produceR1R2S();
	});
	
	$("#btn_produceR14").click(function(){		
		proc_produceR14();
	});

	$("#btn_produceR_projectCreditLoan").click(function(){ //J-109-0213 新增「專案信貸」得由覆審人員抽樣覆審客戶
    	proc_projectCreditLoan();	
	});
	
	function produceNew(){
		AddCustAction.open({
    		handler: _handler,
			action : 'produceNew',
			data : {
                mainOid: json.mainOid
            },
			callback : function(responseData){
				var tabForm = $("#tabForm");
				tabForm.injectData(responseData);
            	//更新LMS2401M01的 Grid
            	$gridview.trigger("reloadGrid");
            	//更新 LMS2405V01 的 Grid
            	CommonAPI.triggerOpener("gridview", "reloadGrid");
            	
            	//關掉 AddCustAction 的 thickbox
            	$.thickbox.close();
			}
		});
	}
	
	function chose_nCkdFlag(has_nCreatData_SYS){
		var my_dfd = $.Deferred();
		if(has_nCreatData_SYS){
			var _id = "_div_btn_crud_dc";
			var _form = _id+"_form";
			 	
			if ($("#"+_id).length == 0){
				var dyna = [];
				dyna.push("<div id='"+_id+"' style='display:none;'>");
				dyna.push("<form id='"+_form+"'>");
				dyna.push("	<table width='100%' >");
				dyna.push("	<tr><td>"+i18n.lms2401m01["grid.nCkdFlag"]+"</td></tr>");
				dyna.push("	<tr><td nowrap style='padding-right:24px;'><input type='radio' id='nCkdFlag' name='nCkdFlag' /></td></tr>");
				dyna.push(" </table>");
				dyna.push(" &nbsp;");
				dyna.push(" <div>");
				dyna.push(i18n.lms2401m01["grid.nckdDetail"]+" <br/>");
				dyna.push(" <input type='text' id='nckdDetail' name='nckdDetail' size='60'>");
				dyna.push(" </div>");
				dyna.push("</form>");
				
				dyna.push("</div>");
				
			     $('body').append(dyna.join(""));
			     
			     $.ajax({
			    	 type: 'post', handler: _handler, data:{'formAction':'load_nckdFlag'}
			         }).done(function(json_combos){
			        	 var nCkdFlag = $("#nCkdFlag");
			        	 nCkdFlag.setItems({ item: json_combos.nckdFlag,format: "{value} {key}",size: 1});
			     });
			}
			//clear data
			$("#"+_form).reset();
			
			$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
				//ui_lms2401.msg02=請選擇不覆審原因
		       title: i18n.lms2401m01["ui_lms2401.msg02"],
		       width: 600,
	           height: 480,
	           align: "center",
	           valign: "bottom",
	           modal: false,
	           i18n: i18n.def,
	           buttons: {
	               "sure": function(){
	                   if (!$("#"+_form).valid()) {
	                   	   CommonAPI.showMessage(i18n.lms2401m01["ui_lms2401.msg02"]);
	                       return;
	                   }
	                   var nCkdFlag = $("#"+_form).find("[name='nCkdFlag']:checked").val();
	                   var nckdDetail = $("#"+_form).find("#nckdDetail").val();
	                   
	                   if(nCkdFlag){                	   
	                   }else{
	                	   CommonAPI.showMessage(i18n.lms2401m01["ui_lms2401.msg02"]);
	                       return;
	                   }
	                                      
	                  $.thickbox.close();
	                  my_dfd.resolve( {'nCkdFlag':nCkdFlag, 'nCkdDetail':nckdDetail } );	
	               },
	               "cancel": function(){
	            	   $.thickbox.close();
	               }
	           }
			});
		}else{
			//不包含SYS, 直接刪, 不用選原因
			my_dfd.resolve({'nCkdFlag':'A'});
		}
		return my_dfd.promise();
	}
	
	function saveNoCTL(){		
		var rowId_arr = $gridview.getGridParam('selarrrow');
		var has_nCreatData_SYS = false;
   	 	var oid_arr = [];
   	 	for (var i = 0; i < rowId_arr.length; i++) {
			var data = $gridview.getRowData(rowId_arr[i]);
			//本來不覆審原因-A, 也可以改成-G. 不限制 retrialYN
			oid_arr.push(data.oid);
			
			if(data.nCreatData=="SYS"){
				has_nCreatData_SYS = true;
			}
        }

   	 	if(oid_arr.length==0){   	 		
   	 		API.showMessage(i18n.def.action_002);//請先選擇需「修改/刪除」之資料列。"
   	 		return;
   	 	}
   	
   	 	chose_nCkdFlag(has_nCreatData_SYS).done(function(json_nCkdObj){
   	 		//actoin_001=是否執行此動作?
	   	 	CommonAPI.confirmMessage(i18n.def.actoin_001, function(b){
	            if (b) {
	            	$.ajax({
	    	            type: "POST",
	    	            handler: _handler,
	    	            data: $.extend(
	    	            	{
	    	            		formAction: "saveNoCTL",
	    	            		'oid_arr': oid_arr,
	    	            		mainOid: json.mainOid
	    	            	}
	    	            	, json_nCkdObj||{}
	    	            )
	    	            }).done(function(responseData){                    	   
	    	             var tabForm = $("#tabForm");
	    					tabForm.injectData(responseData);
	    	            	if(true){
	    	            		if( $("#c240m01c_R95_1_div").is(":visible") && responseData.c240m01c_R95_1 &&(responseData.c240m01c_R95_1.hasData == "Y")){
	    	            			var elementDom = $("#c240m01c_R95_1_div");
	    	            			elementDom.html(build_str_c240m01c_R95_1_withJSON(responseData.c240m01c_R95_1) );
	    	            			//$("#c240m01c_R95_1_div").html(build_str_c240m01c_R95_1_withJSON(responseData.c240m01c_R95_1) );
	    	            			//$("#c240m01c_R95_1_div").injectData(build_str_c240m01c_R95_1_withJSON(responseData.c240m01c_R95_1) );
	    	            		}
	    	            		//~~~~~~~~
	    	            		if( $("#c240m01c_R1R2S_div").is(":visible") && responseData.c240m01c_R1R2S){
	    	            			var elementDom = $("#c240m01c_R1R2S_div");
	    	            			elementDom.html(build_str_c240m01c_R1R2S_withJSON(responseData.c240m01c_R1R2S) );
	    	            			//$("#c240m01c_R1R2S_div").html(build_str_c240m01c_R1R2S_withJSON(responseData.c240m01c_R1R2S) );
	    	            			//$("#c240m01c_R1R2S_div").injectData(build_str_c240m01c_R1R2S_withJSON(responseData.c240m01c_R1R2S) );
	    	            		}		
	    	            		//~~~~~~~~
	    	            		if( $("#c240m01c_R_projectCreditLoan_div").is(":visible") && responseData.c240m01c_R_projectCreditLoan){
	    	            			var elementDom = $("#c240m01c_R_projectCreditLoan_div");
	    	            			elementDom.html(build_str_c240m01c_R_projectCreditLoan_withJSON(responseData.c240m01c_R_projectCreditLoan) );
	    	            			//$("#c240m01c_R_projectCreditLoan_div").html(build_str_c240m01c_R_projectCreditLoan_withJSON(responseData.c240m01c_R_projectCreditLoan) );
	    	            			//$("#c240m01c_R_projectCreditLoan_div").injectData(build_str_c240m01c_R_projectCreditLoan_withJSON(responseData.c240m01c_R_projectCreditLoan) );
	    	            		}
	    	            		//~~~~~~~~
	    	            		if( $("#c240m01c_R14_div").is(":visible") && responseData.c240m01c_R14){
	    	            			var elementDom = $("#c240m01c_R14_div");
	    	            			elementDom.html(build_str_c240m01c_R1R2S_withJSON(responseData.c240m01c_R14) );
	    	            			//$("#c240m01c_R14_div").html(build_str_c240m01c_R1R2S_withJSON(responseData.c240m01c_R14) );
	    	            			//$("#c240m01c_R14_div").injectData(build_str_c240m01c_R1R2S_withJSON(responseData.c240m01c_R14) );
	    	            		}
	    	            	}
	    					
	    					//更新LMS2401M01的 Grid
	    	            	$gridview.trigger("reloadGrid");
	    	            	//更新 LMS2405V01 的 Grid
	    	            	CommonAPI.triggerOpener("gridview", "reloadGrid");
	    	            	$.thickbox.close();
	    	            	
	    	            	if(responseData.notifyMsgYN=="Y"){
	    	            		API.showPopMessage(responseData.notifyMsg);
	    	            	}
	    	        });
	            }
	        });
        });
	}
	
	function saveReCTL(){
		var rowId_arr = $gridview.getGridParam('selarrrow');
      	var oid_arr = [];
      	for (var i = 0; i < rowId_arr.length; i++) {
      		 var data = $gridview.getRowData(rowId_arr[i]);
      		 if (data.retrialYN == "N") {
      		 }else{
      			 //ui_lms2401.msg01=「非」不覆審客戶
      			 API.showMessage(data.custId+" "+data.custName+" "+i18n.lms2401m01['ui_lms2401.msg01']);
      			 return;
      		 }
      		 oid_arr.push(data.oid);
        }

      	if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_002);//請先選擇需「修改/刪除」之資料列。"
      		return;
      	}
      	 
      	$.ajax({
           type: "POST",
           handler: _handler,
           data: {
               formAction: "saveReCTL",
               'oid_arr': oid_arr,
               mainOid: json.mainOid
           }
           }).done(function(responseData){
           	var tabForm = $("#tabForm");
			tabForm.injectData(responseData);
           	//更新LMS2401M01的 Grid
           	$gridview.trigger("reloadGrid");
           	//更新 LMS2405V01 的 Grid
           	CommonAPI.triggerOpener("gridview", "reloadGrid");           	
       });
	}
	
	function escrowDate(var_type){
		var my_dfd = $.Deferred();
		if(var_type=="1"){
			var _id = "_div_btnEscrow1";
			var _form = _id+"_form";
			
			//clear data
			$("#"+_form).reset();
			
			$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
		        title: "",
		        width: 380,
	            height: 100,
	            align: "center",
	            valign: "bottom",
	            modal: false,
	            i18n: i18n.def,
	            buttons: {
	                "sure": function(){
	                    if (!$("#"+_form).valid()) {
	                        return;
	                    }
	                    
	    				$.thickbox.close();  
	    				
	    				my_dfd.resolve( {'type':var_type, 'sDate':$("#"+_form).find("#escrowSDate").val() } );
	                },
	                "cancel": function(){
	                    $.thickbox.close();
	                    
	                    my_dfd.reject();
	                }
	            }
		    });
		}else if(var_type=="2" || var_type=="3"){
			my_dfd.resolve( {'type':var_type, 'sDate':''} );
		}
		return my_dfd.promise();
	}
	
	function escrowComIdList(){
		var my_dfd = $.Deferred();
		
		//===================================
		//清空,再載入 comId
		$("#_div_btnEscrowComIdList").empty();
		$("#_div_btnEscrowComIdList").html("<div id='gridEscrowComIdList'></div>");
		//$("#_div_btnEscrowComIdList").injectData("<div id='gridEscrowComIdList'></div>");
		$gridEscrowComIdList = $("#gridEscrowComIdList").iGrid({
	        handler: 'lms2401gridhandler',        
	        height: 300,
	        postData: {
	            formAction: "queryEscrowComIdList"
	        },
	        needPager: false,        
	        shrinkToFit: false,       
	        colModel: [
	          {//合作仲介名稱
	        	  colHeader: i18n.lms2401m01['label.comInfo'], name: 'comName', width: 150, sortable: true, align: "left" 
	          },{//額度序號
	            colHeader: i18n.lms2401m01['C241M01B.quotaNo'], name: 'cntrNo', width: 120, sortable: true, align: "left"          
	          }, {//合作仲介統編
			    colHeader: i18n.lms2401m01['label.comId'], name: 'comId', width: 100, sortable: true, align: "left"
	          }, {//代收代號
	  		    colHeader: i18n.lms2401m01['label.DP_AC'], name: 'DP_AC', width: 80, sortable: true, align: "left"
	          }, {//保證種類
	   		    colHeader: i18n.lms2401m01['label.lnf660_loan_class.grid'], name: 'lnf660_loan_class', width: 80, sortable: true, align: "left"
	         }  
	        , { name: 'hid_xxx', hidden: true }
	        ],        
	        ondblClickRow: function(rowid){
	        }        
	    });	
		
		$("#_div_btnEscrowComIdList").thickbox({
	        title: i18n.lms2401m01["ui_lms2401.msg10"],//請選取價金履約保證覆審企業
	        width: 650,
            height: 450,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                	 var data = $gridEscrowComIdList.getSingleData();
                     if (data) {
                    	 $.thickbox.close();
                    	 my_dfd.resolve( data );                         
                     }
                },
                "cancel": function(){
                    $.thickbox.close();
                    
                    my_dfd.reject();
                }
            }
	    });
		return my_dfd.promise();
	}
	
	function escrowList(param){
		/*
		 * param 的內容為 
		type : 1/2/3
		sDate : 2014-04-03
		---
		cntrNo : 9181093T0649
		lnf660_loan_class : B2
		DP_AC : D32
		comId : 80293513 0
		comName : 鴻８０２９３５１３
		*/
		
		var v_height = 350;
		//===================================
		//清空,再載入 comId
		$("#_div_btnEscrowList").empty();
		var elementDom = $("#_div_btnEscrowList");
		elementDom.html("<div>"
				+i18n.lms2401m01['label.comInfo']+"："+param.comId+" "+param.comName
				+"</div><div id='gridEscrowList'></div>");
		/*
		$("#_div_btnEscrowList").html("<div>"
				+i18n.lms2401m01['label.comInfo']+"："+param.comId+" "+param.comName
				+"</div><div id='gridEscrowList'></div>");
		*/
		/*
		$("#_div_btnEscrowList").injectData("<div>"
				+i18n.lms2401m01['label.comInfo']+"："+param.comId+" "+param.comName
				+"</div><div id='gridEscrowList'></div>");
		*/
		$gridEscrowList = $("#gridEscrowList").iGrid({
	        handler: 'lms2401gridhandler',        
	        height: v_height,
	        postData: $.extend({
	        	mainOid: $("#mainOid").val(),
	            formAction: "queryEscrowList"
	        }, param),
	        //needPager: false,        
	        rowNum:15,
	        shrinkToFit: false,
	        multiselect: true,       
	        colModel: [
	          {//賣方統編
			    colHeader: i18n.lms2401m01['label.sellerId'], name: 'custId', width: 120, sortable: true, align: "left"
	          }, {//賣方名稱
	  		    colHeader: i18n.lms2401m01['label.sellerInfo'], name: 'custName', width: 170, sortable: true, align: "left"
	          }, {//額度序號
	        	  colHeader: i18n.lms2401m01['C241M01B.quotaNo'], name: 'LNF020_CONTRACT', width: 120, sortable: true, align: "left"
	          }, {//履保餘額
	            colHeader: i18n.lms2401m01['label.escrow_bal'], name: 'show_escrow_bal', width: 120, sortable: true, align: "right"
	          }, {//備註
	   		    colHeader: i18n.lms2401m01['label.escrow_memo'], name: 'escrow_memo', width: 190, sortable: true, align: "left"
	         }
	        , { name: 'comId', hidden: true }
	        , { name: 'comName', hidden: true }
	        , { name: 'LNF034_LC_NO', hidden: true }
	        ],        
	        ondblClickRow: function(rowid){
	        },
            loadComplete: function () {
            	
            	if( $gridEscrowList.getGridParam("records")>0){
            		//
            	}else{
            		$.thickbox.close();
            		API.showMessage("你所選的仲介商在本分行找不到符合的價金履約保證覆審案");
            	}
            }        
	    });	
			
		$("#_div_btnEscrowList").thickbox({
	        title: "",
	        width: 850,
            height: v_height+150,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                	
                	var rowId_arr = $gridEscrowList.getGridParam('selarrrow');
                	var cntrNoLcNo_arr = [];
               	 	for (var i = 0; i < rowId_arr.length; i++) {
            			var data = $gridEscrowList.getRowData(rowId_arr[i]);
            			cntrNoLcNo_arr.push(data.LNF020_CONTRACT+"^"+data.LNF034_LC_NO);
                    }

               	 	if(cntrNoLcNo_arr.length==0){   	 		
               	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
               	 		return;
               	 	}
                     
	               	 $.ajax({
                         type: "POST", handler: _handler,
                         data: $.extend({
                        	 mainOid: $("#mainOid").val(),
                             formAction: "saveR97",	                                 
                             cntrNoLcNo_arr: cntrNoLcNo_arr
            	        }, param)
                         }).done(function(responseData){
                        	 $("#tabForm").injectData(responseData);
                        	 
                        	 //更新 LMS2401S02 的 grid
                        	 $("#gridview").trigger("reloadGrid");
	                         
	                     	 //更新 LMS2405V01 的 Grid
	                     	 CommonAPI.triggerOpener("gridview", "reloadGrid");
	                     	 
	                     	$.thickbox.close();
                     });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
		
	}
	
	function proc_escrow(){
    	escrowType().done(function( var_escrowType){
    		if(var_escrowType=="4"){
    			var _id = "_div_btnEscrowMenu4";
    			var _form = _id+"_form";
    			
    			//clear data
    			$("#"+_form).reset();
    			
    			$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
    		        title: "",
    		        width: 380,
    	            height: 100,
    	            align: "center",
    	            valign: "bottom",
    	            modal: false,
    	            i18n: i18n.def,
    	            buttons: {
    	                "sure": function(){
    	                    if (!$("#"+_form).valid()) {
    	                        return;
    	                    }
    	                    
    	                    $.ajax({
    	                         type: "POST", handler: _handler,
    	                         data: $.extend({
    	                        	 mainOid: $("#mainOid").val(),
    	                             formAction: "saveR97_byLcNo"}
    	                         	, $("#"+_form).serializeData())
    	                         }).done(function(responseData){
    	                        	 $("#tabForm").injectData(responseData);
    	                        	 
    	                        	 //更新 LMS2401S02 的 grid
    	                        	 $("#gridview").trigger("reloadGrid");
    		                         
    		                     	 //更新 LMS2405V01 的 Grid
    		                     	 CommonAPI.triggerOpener("gridview", "reloadGrid");
    		                     	
    		                     	 $.thickbox.close();
    		                     	 //=======
    		                     	 API.showMessage(i18n.def.runSuccess);    		                     	
    	                     });
    	                },
    	                "cancel": function(){
    	                    $.thickbox.close();
    	                }
    	            }
    		    });
    		}else{
    			
    		escrowDate(var_escrowType).done(function( json_typeSDate){
    			escrowComIdList().done(function( chose_gridrow){
    				var param = $.extend(json_typeSDate, chose_gridrow);
    				escrowList(param);    
    			});
    		});
    			
    		}
		});
	}
	
	function escrowType(){
		var my_dfd = $.Deferred();
		
		var _id = "_div_btn_escrowType";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");		
			dyna.push("<form id='"+_form+"'>");
			
			var submenu = {
					  '1':"1-價金履約保證覆審案件"
					, '2':"2-自開出價金履約保證書之日起，逾一年仍未結案案件"
					, '3':"3-自開出價金履約保證書之日起，逾四個月未結案之案件"
					, '4':"4-依保證編號"
					};
			build_submenu(dyna, 'decision_escrow', submenu);
			
			dyna.push("</form>");
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: "",
	        width: 550,
            height: 250,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decision_escrow']:checked").val();
                    $.thickbox.close();
                    
                    if(val=="1" || val=="2" || val=="3" || val=="4"){
                    	my_dfd.resolve(val);             
                    }else{
                    	my_dfd.reject();	
                    }                
                },
                "cancel": function(){
                	$.thickbox.close();
                	
                	my_dfd.reject();
                }
            }
	    });
		
		return my_dfd.promise();
	}
	
	function proc_DocKindS(){
		queryDocKindS_param_pa_ym().done(function( var_pa_ym ){
			queryDocKindS_choose(var_pa_ym).done(function( var_rtn ){
				if(var_rtn.decision_pa == "1"){ //依覆審對象第1~6項篩選
					/*
					 	在 LMS2401M01FormHandler :: queryDocKindS_param_pa_trg_empNo(...) 裡
							o.put("k", elf490b_rule_no+"|"+empNo); 
						註：在 server 端，用 | 來區分 N個欄位 
					*/
					var pa_trg = var_rtn.pa_trg_empNo.split("|")[0];
					var empNo =  var_rtn.pa_trg_empNo.split("|")[1];
					//~~~
					var param = $.extend({'pa_trg': pa_trg, 'empNo':empNo}, var_pa_ym);
					if(pa_trg=="R6"){
						queryDocKindS_R6(param);
					}else{
						queryDocKindS_R1toR5(param);
					} 
				}else if(var_rtn.decision_pa == "2"){	//依指定承辦行員篩選
					var pa_trg = "";
					var empNo =  var_rtn.pa_detail_specificEmpNo;
					//~~~
					var param = $.extend({'pa_trg': pa_trg, 'empNo':empNo}, var_pa_ym);
					queryDocKindS_specificEmpNo(param);
				}else if(var_rtn.decision_pa == "3"){	//指定客戶統編
	    			AddCustAction.open({
	    	    		handler: _handler,
	    				action : 'produce96',
	    				data : {
	    	                mainOid: json.mainOid
	    	                , 'pa_ym' : var_pa_ym.pa_ym
	    	            },
	    				callback : function(responseData){
	    					var tabForm = $("#tabForm");
	    					tabForm.injectData(responseData);
	    	            	//更新LMS2401M01的 Grid
	    	            	$gridview.trigger("reloadGrid");
	    	            	//更新 LMS2405V01 的 Grid
	    	            	CommonAPI.triggerOpener("gridview", "reloadGrid");
	    	            	
	    	            	//關掉 AddCustAction 的 thickbox
	    	            	$.thickbox.close();
	    				}
	    			});
	    		}else{
	    			API.showErrorMessage("選項="+var_rtn.decision_pa);
	    		}
			});
		});		
	}
	
	function queryDocKindS_param_pa_ym(){		
		var my_dfd = $.Deferred();
		if(true){
			$.ajax({
	            type: "POST", handler: _handler,
	            // async: false,//用「同步」的方式
	            data: {
	                formAction: "queryDocKindS_param_pa_ym"
	                , mainOid: $("#mainOid").val()
	            }
	            }).done(function(json){
	            	if(json.selBox_pa_period.key && json.selBox_pa_period.key.length==1){
	            		var jsonItem = json.selBox_pa_period.key[0]; 
	            		var rtn = {};
	            		rtn['pa_ym'] = jsonItem.k;
	            		rtn['show_text'] = (i18n.lms2401m01["label.docKindS_pa_period"]+"：["+jsonItem.v+"]");
	        			my_dfd.resolve(rtn);
	            	}else{
		            	var _id = "_div_DocKindS_pa_ym";
		        		var _form = _id+"_form";		        		
		        		if ($("#"+_id).length == 0){			
		                	var dyna = [];
		        			dyna.push("<div id='"+_id+"' style='display:none;' >");		
		        			dyna.push("<form id='"+_form+"'>");
		        			
		        			dyna.push("<table>");
		        			if(json.selBox_pa_period){
		        				dyna.push("<tr>");
		        				dyna.push("<td>"+i18n.lms2401m01["label.docKindS_pa_period"]+"："+"</td>");
		        				dyna.push("<td>");
			        			dyna.push("<select name='pa_ym' >");
		        				$.each(json.selBox_pa_period.key, function(idx, jsonItem) {
		        					dyna.push("<option value='"+(jsonItem.k||'')+"' >"+(jsonItem.v||'')+"&nbsp;</option>");        					
		        				});	
		        				dyna.push("</select>");
		        				dyna.push("</td>");
		        				dyna.push("</tr>");
		        			}
		        			dyna.push("</table>");        			
		        			   
		        			dyna.push("</form>");
		        			dyna.push("</div>");
		        			
		        		     $('body').append(dyna.join(""));        	
		                }	
		                    
		                //clear data
		        		$("#"+_form).reset();
		        		
		        		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
		        	        title: "",
		        	        width: 450,
		                    height: 200,
		                    align: "center",
		                    valign: "bottom",
		                    modal: false,
		                    i18n: i18n.def,
		                    buttons: {
		                        "sure": function(){//                           
		                        	var rtn = $("#"+_form).serializeData();
		                        	var pa_ym = rtn.pa_ym||'';
		                            if(pa_ym==""){
		                            	return;         
		                            }else{	
		                            	//在下一層的 tb 顯示選擇的「覆審基準期間」
		                            	rtn['show_text'] =  i18n.lms2401m01["label.docKindS_pa_period"]+"："+$("#"+_form).find("name='pa_ym']:selected").text();                            	
		                            	//console.dir(rtn);		
		                            	$.thickbox.close();
		                            	my_dfd.resolve(rtn); 
		                            }                
		                        },
		                        "cancel": function(){
		                        	$.thickbox.close();
		                        	
		                        	my_dfd.reject();
		                        }
		                    }
		        	    });
	            		
	            	}
			});
		}
		return my_dfd.promise();
	}
	
	function queryDocKindS_choose(opt){
		var my_dfd = $.Deferred();
		
		$.ajax({
            type: "POST",
            handler: _handler,
            // async: false,//用「同步」的方式
            data: $.extend({
                formAction: "queryDocKindS_param_pa_trg_empNo"
                , mainOid: $("#mainOid").val()
            }, opt||{})
            }).done(function(json){	 
            	var _id = "div_DocKindS_pa_choose";
				var _form = _id+"_form";
				var _renew_div =  _form+"__renew_div";
				
				if ($("#"+_id).length == 0){			
		        	var dyna = [];
					dyna.push("<div id='"+_id+"' style='display:none;' >");		
					dyna.push("<form id='"+_form+"'>");
					
					dyna.push("<p><label id='_itemMenu_decision_pa_1'><input name='decision_pa' value='1' class='required' type='radio' "
							+((json.selBox_pa_trg_empNo && json.selBox_pa_trg_empNo.key)?"":" disabled ")
							+">1-依覆審對象第1~6項篩選</label>");
					dyna.push("<div id='"+_renew_div+"'>");
					dyna.push("</div>");
					dyna.push("</p>");
					dyna.push("<p><label id='_itemMenu_decision_pa_2'><input name='decision_pa' value='2' class='required' type='radio'>2-依指定承辦行員篩選</label>"
							+"<input type='text' name='pa_detail_specificEmpNo' maxlength='6' size='7' >"
							+"</p>");
					dyna.push("<p><label id='_itemMenu_decision_pa_3'><input name='decision_pa' value='3' class='required' type='radio'>3-指定客戶統編</label>"
							+"<div style='margin-left:24px; ' >"
							+"適用於：「疑似代辦案件註記」項目為「撥款後回查有其他金融機構短期內接續撥款情形」案件 "
							+"</div>"
							+"</p>");
					   
					dyna.push("</form>");
					dyna.push("</div>");
					
				     $('body').append(dyna.join(""));        	
		        }
				
				if (true){
        			$("#"+_renew_div).empty(); //切換不同的 pa_ym
        			
        			var sel_dyna = [];
        			sel_dyna.push("<select name='pa_trg_empNo' style='margin-left:30px; '>");
					if(json.selBox_pa_trg_empNo){
						$.each(json.selBox_pa_trg_empNo.key, function(idx, jsonItem) {
							sel_dyna.push("<option value='"+(jsonItem.k||'')+"' >"+(jsonItem.v||'')+"&nbsp;</option>");							
						});	
					}		
					sel_dyna.push("</select>");
    				
					var elementDom = $("#"+_renew_div);
					elementDom.html(sel_dyna.join(""));
    				//$("#"+_renew_div).html(sel_dyna.join(""));
    				//$("#"+_renew_div).injectData(sel_dyna.join(""));
        		}
		            
		        //clear data
				$("#"+_form).reset();
				
				$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
			        title: opt.show_text||"",
			        width: 600,
		            height: 250,
		            align: "center",
		            valign: "bottom",
		            modal: false,
		            i18n: i18n.def,
		            buttons: {
		                "sure": function(){
		//                    if (!$("#"+_form).valid()) {
		//                        return;
		//                    }
		                	var rtn = $("#"+_form).serializeData();
		                   
		                    
		                    var flagVal = rtn.decision_pa||'';
		                    if(flagVal==""){
		                    	API.showErrorMessage("請選擇查詢條件");
		                    	return;         
		                    }else{	
		                    	if(flagVal=="2" && rtn.pa_detail_specificEmpNo==""){
		                    		API.showErrorMessage("請輸入「承辦行員」行員編號");
		                    		return;
		                    	}	 
		                    	$.thickbox.close();
		                    	// console.dir(rtn);
		                    	my_dfd.resolve(rtn); 
		                    }                
		                },
		                "cancel": function(){
		                	$.thickbox.close();
		                	
		                	my_dfd.reject();
		                }
		            }
			    });            	
		});
		
		return my_dfd.promise();
	}
	
	function queryDocKindS_R1toR5(param){
		var v_height = 350;
		//===================================
		//清空,再載入 comId
		$("#_div_DocKindS_R1toR5").empty();
		var elementDom = $("#_div_DocKindS_R1toR5");
		elementDom.html("<div id='gridDocKindS_R1toR5'></div>");
		//$("#_div_DocKindS_R1toR5").html("<div id='gridDocKindS_R1toR5'></div>");
		//$("#_div_DocKindS_R1toR5").injectData("<div id='gridDocKindS_R1toR5'></div>");
		var $gridDocKindS_R1toR5 = $("#gridDocKindS_R1toR5").iGrid({
	        handler: 'lms2401gridhandler',        
	        height: v_height,
	        postData: $.extend({
	        	mainOid: $("#mainOid").val(),
	            formAction: "queryDocKindS_R1toR5"
	        }, param),
	        needPager: false,        
	        //rowNum:15,
	        shrinkToFit: false,
	        multiselect: true,       
	        colModel: [
	          {//"客戶統編"
	        	  colHeader: i18n.lms2401m01['grid.custId'], name: 'custId_dupNo', width: 90, sortable: true, align: "left"
	          },{//"客戶姓名",
	        	  colHeader: i18n.lms2401m01['grid.custName'], name: 'custName', width: 180, sortable: true, align: "left"
	          }
	        ],        
	        ondblClickRow: function(rowid){
	        },
            loadComplete: function () {
            	if( $gridDocKindS_R1toR5.getGridParam("records")>0){
            		//
            	}else{
            		$.thickbox.close();
            		API.showMessage("所選的承辦行員，在覆審基準期間，找不到符合的案件");
            	}
            }        
	    });	
			
		$("#_div_DocKindS_R1toR5").thickbox({
	        title: "",
	        width: 420,
            height: v_height+150,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                	
                	var rowId_arr = $gridDocKindS_R1toR5.getGridParam('selarrrow');
                	var choose_arr = [];
               	 	for (var i = 0; i < rowId_arr.length; i++) {
            			var data = $gridDocKindS_R1toR5.getRowData(rowId_arr[i]);
            			choose_arr.push(data.custId_dupNo);
                    }

               	 	if(choose_arr.length==0){   	 		
               	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
               	 		return;
               	 	}
               	 	
	               	 $.ajax({
                         type: "POST", handler: _handler,
                         data: $.extend({
                        	 mainOid: $("#mainOid").val(),
                             formAction: "produce96_multiple",	                                 
                             choose_arr: choose_arr
            	        }, param)
                         }).done(function(responseData){
                        	 $("#tabForm").injectData(responseData);
                        	 
                        	 //更新 LMS2401S02 的 grid
                        	 $("#gridview").trigger("reloadGrid");
	                         
	                     	 //更新 LMS2405V01 的 Grid
	                     	 CommonAPI.triggerOpener("gridview", "reloadGrid");
	                     	 
	                     	$.thickbox.close();
                     });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });		
	}
	
	function queryDocKindS_R6(param){
		var v_height = 350;
		//===================================
		//清空,再載入 comId
		$("#_div_DocKindS_R6").empty();
		var elementDom = $("#_div_DocKindS_R6");
		elementDom.html("<div id='gridDocKindS_R6'></div>");
		//$("#_div_DocKindS_R6").html("<div id='gridDocKindS_R6'></div>");
		//$("#_div_DocKindS_R6").injectData("<div id='gridDocKindS_R6'></div>");
		var $gridDocKindS_R6 = $("#gridDocKindS_R6").iGrid({
	        handler: 'lms2401gridhandler',        
	        height: v_height,
	        postData: $.extend({
	        	mainOid: $("#mainOid").val(),
	            formAction: "queryDocKindS_R6"
	        }, param),
	        needPager: false,        
	        //rowNum:15,
	        shrinkToFit: false,
	        multiselect: true,       
	        colModel: [
	          {//"客戶統編"
	        	  colHeader: i18n.lms2401m01['grid.custId'], name: 'custId_dupNo', width: 90, sortable: true, align: "left"
	          },{//"客戶姓名",
	        	  colHeader: i18n.lms2401m01['grid.custName'], name: 'custName', width: 180, sortable: true, align: "left"
	          }
	        ],        
	        ondblClickRow: function(rowid){
	        },
            loadComplete: function () {
            	if( $gridDocKindS_R6.getGridParam("records")>0){
            		//
            	}else{
            		$.thickbox.close();
            		API.showMessage("所選的承辦行員，在覆審基準期間，找不到符合的案件");
            	}
            }        
	    });	
			
		$("#_div_DocKindS_R6").thickbox({
	        title: "",
	        width: 420,
            height: v_height+150,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                	
                	var rowId_arr = $gridDocKindS_R6.getGridParam('selarrrow');
                	var choose_arr = [];
               	 	for (var i = 0; i < rowId_arr.length; i++) {
            			var data = $gridDocKindS_R6.getRowData(rowId_arr[i]);
            			choose_arr.push(data.custId_dupNo);
                    }

               	 	if(choose_arr.length==0){   	 		
               	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
               	 		return;
               	 	}
               	 	
	               	 $.ajax({
                         type: "POST", handler: _handler,
                         data: $.extend({
                        	 mainOid: $("#mainOid").val(),
                             formAction: "produce96_multiple",	                                 
                             choose_arr: choose_arr
            	        }, param)
                         }).done(function(responseData){
                        	 $("#tabForm").injectData(responseData);
                        	 
                        	 //更新 LMS2401S02 的 grid
                        	 $("#gridview").trigger("reloadGrid");
	                         
	                     	 //更新 LMS2405V01 的 Grid
	                     	 CommonAPI.triggerOpener("gridview", "reloadGrid");
	                     	 
	                     	$.thickbox.close();
                     });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });		
	}
	function queryDocKindS_specificEmpNo(param){
		var v_height = 350;
		//===================================
		//清空,再載入 comId
		$("#_div_DocKindS_empNo").empty();
		var elementDom = $("#_div_DocKindS_empNo");
		elementDom.html("<div id='gridDocKindS_empNo'></div>");
		//$("#_div_DocKindS_empNo").html("<div id='gridDocKindS_empNo'></div>");
		//$("#_div_DocKindS_empNo").injectData("<div id='gridDocKindS_empNo'></div>");
		var $gridDocKindS_empNo = $("#gridDocKindS_empNo").iGrid({
	        handler: 'lms2401gridhandler',        
	        height: v_height,
	        postData: $.extend({
	        	mainOid: $("#mainOid").val(),
	            formAction: "queryDocKindS_specificEmpNo"
	        }, param),
	        needPager: false,        
	        //rowNum:15,
	        shrinkToFit: false,
	        multiselect: true,       
	        colModel: [
	          {//"客戶統編"
	        	  colHeader: i18n.lms2401m01['grid.custId'], name: 'custId_dupNo', width: 90, sortable: true, align: "left"
	          },{//"客戶姓名",
	        	  colHeader: i18n.lms2401m01['grid.custName'], name: 'custName', width: 180, sortable: true, align: "left"
	          }
	        ],        
	        ondblClickRow: function(rowid){
	        },
            loadComplete: function () {
            	if( $gridDocKindS_empNo.getGridParam("records")>0){
            		//
            	}else{
            		$.thickbox.close();
            		API.showMessage("所選的承辦行員，在覆審基準期間，找不到符合的案件");
            	}
            }        
	    });	
			
		$("#_div_DocKindS_empNo").thickbox({
	        title: "",
	        width: 420,
            height: v_height+150,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                	
                	var rowId_arr = $gridDocKindS_empNo.getGridParam('selarrrow');
                	var choose_arr = [];
               	 	for (var i = 0; i < rowId_arr.length; i++) {
            			var data = $gridDocKindS_empNo.getRowData(rowId_arr[i]);
            			choose_arr.push(data.custId_dupNo);
                    }

               	 	if(choose_arr.length==0){   	 		
               	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
               	 		return;
               	 	}
               	 	
	               	 $.ajax({
                         type: "POST", handler: _handler,
                         data: $.extend({
                        	 mainOid: $("#mainOid").val(),
                             formAction: "produce96_multiple",	                                 
                             choose_arr: choose_arr
            	        }, param)
                         }).done(function(responseData){
                        	 $("#tabForm").injectData(responseData);
                        	 
                        	 //更新 LMS2401S02 的 grid
                        	 $("#gridview").trigger("reloadGrid");
	                         
	                     	 //更新 LMS2405V01 的 Grid
	                     	 CommonAPI.triggerOpener("gridview", "reloadGrid");
	                     	 
	                     	$.thickbox.close();
                     });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });		
	}
	
	function proc_Rule95_1(){
		$.ajax({
            type: "POST",
            handler: _handler,
            data: {formAction: "rule95_1_prepareData", mainOid: json.mainOid}
            }).done(function(json_obj){ 
            	if(json_obj.hasData == "Y"){
            		detail_Rule95_1(json_obj.rtn);            		
            	}else{
            		API.showMessage("此分行無「疑似人頭戶」追蹤對象");
            	}
        });
	}
	
	function detail_Rule95_1(param){
		var v_height = 350;		
		//===================================
		//清空
		$("#_div_detail_Rule95_1").empty();
		var elementDom = $("<div>" + "「疑似人頭戶」 " + "</div>" + "<div id='gridRule95_1'></div>")
		$("#_div_detail_Rule95_1").html(elementDom);
		/* 若在 06-01 產生工作底稿
		+ 可能一：要在 06-25 覆審
		+ 可能二：要在 07-02 覆審
		=> 疑似人頭戶的 抽樣是以 {6月底}為基礎去劃分
		=> 若覆審人員 一直切換/調整{預計覆審日} 看到的統計資料 若不同，反倒造成困擾
		//+build_str_c240m01c_R95_1(param.allCnt, param.activeCnt, param.doneBegDate, param.doneEndDate, param.doneCnt, param.doneRate, param.c240_totRetrialCnt)
		$("#_div_detail_Rule95_1").html("<div>"
				+"「疑似人頭戶」 "
				+"</div>"
			+"<div id='gridRule95_1'></div>");
		*/
		
		var grid_id = "gridRule95_1";
		if($("#"+grid_id+".ui-jqgrid-btable").length >0){
			$("#"+grid_id).trigger("reloadGrid");	        		
		}else{
			var $gridRule95_1 = $("#gridRule95_1").iGrid({
		        handler: 'lms2401gridhandler',        
		        height: v_height,
		        postData:{
		        	'mainOid': $("#mainOid").val(), 
		            'formAction': "queryRule95_1"
		        },
		        needPager: false,        
		        //rowNum:15,
		        shrinkToFit: false,
		        multiselect: true,       
		        colModel: [
		          {//"客戶統編"
		        	  colHeader: i18n.lms2401m01['grid.custId'], name: 'custId_dupNo', width: 90, sortable: false, align: "left"
		          },{//"客戶姓名",
		        	  colHeader: i18n.lms2401m01['grid.custName'], name: 'custName', width: 180, sortable: false, align: "left"
		          },{//"前次抽樣覆審日",
		        	  colHeader: i18n.lms2401m01['grid.elf491c_lrDate'], name: 'ELF491C_LRDATE', width: 100, sortable: false, align: "left"
		          }
		        ],        
		        ondblClickRow: function(rowid){
		        },
	            loadComplete: function () {
	            	if( $gridRule95_1.getGridParam("records")>0){
	            		//
	            	}else{
	            		//$.thickbox.close();
	            		API.showMessage("無符合條件的案件");
	            	}
	            }        
		    });
		}
		
		$("#_div_detail_Rule95_1").thickbox({
	        title: "「疑似人頭戶」追蹤對象之10%範圍內逐年辦理「專案查核」",
	        width: 620,
            height: v_height + 180,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){                	
                	var rowId_arr = $gridRule95_1.getGridParam('selarrrow');
                	var choose_arr = [];
               	 	for (var i = 0; i < rowId_arr.length; i++) {
            			var data = $gridRule95_1.getRowData(rowId_arr[i]);
            			choose_arr.push(data.custId_dupNo);
                    }

               	 	if(choose_arr.length==0){   	 		
               	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
               	 		return;
               	 	}
               	 	
	               	 $.ajax({
                         type: "POST", handler: _handler,
                         data: $.extend({
                        	 mainOid: $("#mainOid").val(),
                             formAction: "produce95_1_multiple",	                                 
                             choose_arr: choose_arr
            	        }, param)
                         }).done(function(responseData){
                        	 $("#tabForm").injectData(responseData);
                        	 if(responseData.c240m01c_R95_1){
                        		var elementDom = $("#c240m01c_R95_1_div");
                        		elementDom.html(build_str_c240m01c_R95_1_withJSON(responseData.c240m01c_R95_1) );
              					//$("#c240m01c_R95_1_div").html(build_str_c240m01c_R95_1_withJSON(responseData.c240m01c_R95_1) );
              					//$("#c240m01c_R95_1_div").injectData(build_str_c240m01c_R95_1_withJSON(responseData.c240m01c_R95_1) );
                         	 }
                        	 
                        	 //更新 LMS2401S02 的 grid
                        	 $("#gridview").trigger("reloadGrid");
	                         
	                     	 //更新 LMS2405V01 的 Grid
	                     	 CommonAPI.triggerOpener("gridview", "reloadGrid");
	                     	 
	                     	$.thickbox.close();
                     });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });	
	}
	
	
	function proc_produceR1R2S(){
		detail_produceR1R2S({});
	}
	function detail_produceR1R2S(param){
		var v_height = 50;		
		//===================================
		var _id = "_div_detail_R1R2S";
    	
		$("#"+_id).thickbox({
	        title: "不動產十足擔保  雙北:3000萬元；其它:1500萬元(含)以下",
	        width: 400,
            height: v_height + 100,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
               	 	
	               	 $.ajax({
                         type: "POST", handler: _handler,
                         data: $.extend({
                        	 mainOid: $("#mainOid").val()
                             , formAction: "produce_R1R2S"
                             , 'samplingCnt_R1R2S': $("#samplingCnt_R1R2S").val()
                             // , 'samplingRate_R1R2S': $("#samplingRate_R1R2S").val()
            	        }, param)
                         }).done(function(responseData){
                        	 $("#tabForm").injectData(responseData);
                        	 if(responseData.c240m01c_R1R2S){
                        		var elementDom = $("#c240m01c_R1R2S_div");
                        		elementDom.html(build_str_c240m01c_R1R2S_withJSON(responseData.c240m01c_R1R2S) );
               					//$("#c240m01c_R1R2S_div").html(build_str_c240m01c_R1R2S_withJSON(responseData.c240m01c_R1R2S) );
               					//$("#c240m01c_R1R2S_div").injectData(build_str_c240m01c_R1R2S_withJSON(responseData.c240m01c_R1R2S) );
                          	 }
                        	 
                        	 //更新 LMS2401S02 的 grid
                        	 $("#gridview").trigger("reloadGrid");
	                         
	                     	 //更新 LMS2405V01 的 Grid
	                     	 CommonAPI.triggerOpener("gridview", "reloadGrid");
	                     	 
	                     	$.thickbox.close();
                     });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });	
	
	}
	
	function proc_produceR14(){
		detail_produceR14({});
	}
	function detail_produceR14(param){
		var v_height = 50;		
		//===================================
		var _id = "_div_detail_R14";
    	
		$("#"+_id).thickbox({
	        title: "新臺幣一千萬元以下單一額度項下倘含循環動用者",
	        width: 400,
            height: v_height + 100,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
               	 	
	               	 $.ajax({
                         type: "POST", handler: _handler,
                         data: $.extend({
                        	 mainOid: $("#mainOid").val()
                             , formAction: "produce_R14"
                             , 'samplingCnt_R14': $("#samplingCnt_R14").val()
            	        }, param)
                         }).done(function(responseData){
                        	 $("#tabForm").injectData(responseData);
                        	 if(responseData.c240m01c_R14){
                        		var elementDom = $("#c240m01c_R14_div");
                        		elementDom.html(build_str_c240m01c_R1R2S_withJSON(responseData.c240m01c_R14) );
               					//$("#c240m01c_R14_div").html(build_str_c240m01c_R1R2S_withJSON(responseData.c240m01c_R14) );
               					//$("#c240m01c_R14_div").injectData(build_str_c240m01c_R1R2S_withJSON(responseData.c240m01c_R14) );
                          	 }
                        	 
                        	 //更新 LMS2401S02 的 grid
                        	 $("#gridview").trigger("reloadGrid");
	                         
	                     	 //更新 LMS2405V01 的 Grid
	                     	 CommonAPI.triggerOpener("gridview", "reloadGrid");
	                     	 
	                     	$.thickbox.close();
                     });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });	
	
	}
	
	function proc_projectCreditLoan(){
		$.ajax({
            type: "POST",
            handler: _handler,
            data: {formAction: "prepare_projectCreditLoan_param", mainOid: json.mainOid}
            }).done(function(json_summary){ 
            	detail_projectCreditLoan(json_summary);
        });
		
	}

	function detail_projectCreditLoan(param){
		var v_height = 350;		
		//===================================
		var _id = "_div_detail_projectCreditLoan";
    	var _form = "div_detail_projectCreditLoan_form";
		var grid_id = "gridProjectCreditLoan";
		
		//clear data
		$("#"+_form).reset();
		$("#"+_form).injectData(param);
		
		if(param.rtn){
			var elementDom = $("#statInfoProjectCreditLoan");
			elementDom.html(build_str_c240m01c_R_projectCreditLoan_withJSON(param.rtn));
			//$("#statInfoProjectCreditLoan").html(build_str_c240m01c_R_projectCreditLoan_withJSON(param.rtn));
			//$("#statInfoProjectCreditLoan").injectData(build_str_c240m01c_R_projectCreditLoan_withJSON(param.rtn));
		}
		
		if($("#"+grid_id+".ui-jqgrid-btable").length >0){
			$("#"+grid_id).jqGrid("setGridParam", {
				postData : $("#"+_form).serializeData(),
				search : true
			}).trigger("reloadGrid");	        		
		}else{
			$gridProjectCreditLoan = $("#"+grid_id).iGrid({
				handler : 'lms2401gridhandler',
				height : v_height,
				postData : $.extend( {'mainOid': $("#mainOid").val(),'formAction': "queryProjectCreditLoan"} , $("#"+_form).serializeData() ),	
		        needPager: false,        
		        //rowNum:15,
		        shrinkToFit: false,
		        multiselect: true,		
				colModel : [ 
		          {//"客戶統編"
		        	  colHeader: i18n.lms2401m01['grid.custId'], name: 'custId_dupNo', width: 90, sortable: false, align: "left"
		          },{//"客戶姓名",
		        	  colHeader: i18n.lms2401m01['grid.custName'], name: 'custName', width: 140, sortable: false, align: "left"
		          },{//"額度序號",
		        	  colHeader: i18n.lms2401m01['grid.cntrNo'], name: 'LNF030_CONTRACT', width: 100, sortable: false, align: "left"
		          },{//"首撥日",
		        	  colHeader: i18n.lms2401m01['grid.loanDate'], name: 'LNF030_LOAN_DATE', width: 90, sortable: false, align: "left"
		          },{//"餘額",
		        	  colHeader: i18n.lms2401m01['grid.bal'], name: 'LNF030_LOAN_BAL', width: 100, sortable: false, align: "right", formatter: 'currency', formatoptions: {thousandsSeparator: ",",decimalPlaces: 0}
		          },{//"前次覆審日",
		        	  colHeader: i18n.lms2401m01['grid.elf491_lrDate'], name: 'ELF491_LRDATE', width: 90, sortable: false, align: "left"
		          }
		        ],
	            loadComplete: function () {
	            	if( $gridProjectCreditLoan.getGridParam("records")>0){
	            		//
	            	}else{
	            		//$.thickbox.close();
	            		API.showMessage("無符合條件的案件");
	            	}
	            }    
			});
		}
		
		$("#"+_id).thickbox({
	        title: "「專案信貸」得由覆審人員抽樣覆審客戶",
	        width: 920,
            height: v_height + 250,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){                	
                	var rowId_arr = $gridProjectCreditLoan.getGridParam('selarrrow');
                	var choose_arr = [];
               	 	for (var i = 0; i < rowId_arr.length; i++) {
            			var data = $gridProjectCreditLoan.getRowData(rowId_arr[i]);
            			choose_arr.push(data.custId_dupNo);
                    }

               	 	if(choose_arr.length==0){   	 		
               	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
               	 		return;
               	 	}
               	 	
	               	 $.ajax({
                         type: "POST", handler: _handler,
                         data: $.extend({
                        	 mainOid: $("#mainOid").val(),
                             formAction: "produce_projectCreditLoan",	                                 
                             choose_arr: choose_arr
            	        }, param)
                         }).done(function(responseData){
                        	 $("#tabForm").injectData(responseData);
                        	 if(responseData.c240m01c_R_projectCreditLoan){
                        		var elementDom = $("#c240m01c_R_projectCreditLoan_div");
                        		elementDom.html(build_str_c240m01c_R_projectCreditLoan_withJSON(responseData.c240m01c_R_projectCreditLoan) );
              					//$("#c240m01c_R_projectCreditLoan_div").html(build_str_c240m01c_R_projectCreditLoan_withJSON(responseData.c240m01c_R_projectCreditLoan) );
              					//$("#c240m01c_R_projectCreditLoan_div").injectData(build_str_c240m01c_R_projectCreditLoan_withJSON(responseData.c240m01c_R_projectCreditLoan) );
                         	 }
                        	 
                        	 //更新 LMS2401S02 的 grid
                        	 $("#gridview").trigger("reloadGrid");
	                         
	                     	 //更新 LMS2405V01 的 Grid
	                     	 CommonAPI.triggerOpener("gridview", "reloadGrid");
	                     	 
	                     	$.thickbox.close();
                     });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });	
	}
	
	function moveFromAreaWaitApproveToEnd(){
		//ui_lms2401.msg15=是否覆核結案？
		API.confirmMessage(i18n.lms2401m01["ui_lms2401.msg15"], function(result){
	        if(result){
				var c241m01aOidArray = [];
				var ids = $gridview.jqGrid('getGridParam','selarrrow');
				for(var i=0; i<ids.length; i++){
					var obj = $gridview.getRowData(ids[i]);
					c241m01aOidArray.push(obj.oid);
				}
				
				var rtnOidArray = checkC241M01AFromAreaWaitApproveToEnd(c241m01aOidArray);
				if(typeof rtnOidArray != undefined && rtnOidArray.length>0){
					
					var param = {'mainDocStatus':'02A', 'decisionExpr':'to_已覆核已核定'};      	
			      	//===================================
			      	var cnt = 0;
					var target = rtnOidArray.length;
						
					var theQueue = $({});
					$.each(rtnOidArray,function(k, v_oid) {                                            			
						theQueue.queue('myqueue', function(next) {
							$.ajax({
					            type: "POST",
					            handler: "lms2411m01formhandler", action: "flowAction",
					            data:$.extend( { mainOid: v_oid } , param )                
					            }).done(function(json){ 
					        }).always(function(){//用 complete(不論done, fail)
					        	cnt++;
			  					if(cnt==target){  						
			  						//更新LMS2401M01的 Grid
			  		            	$gridview.trigger("reloadGrid");
			  		            	//更新 LMS2405V01 的 Grid
			  		            	CommonAPI.triggerOpener("gridview", "reloadGrid");  
			      				}
			  					//---
			  					//把 next() 寫在 finish 的 callback 裡
			  					//才會 1個 url 抓到 response 後,再get下1個
			  					//不然會1次跳 N 個出來
			  					next();
					        });				
						});                                            			 
					});
					theQueue.dequeue('myqueue');
				}
	    	}
		});
	}
	var checkC241M01AFromAreaWaitApproveToEnd = function(oidArray){
		var cnt = 0;
		var target = oidArray.length;
		var param = {};
		var theQueue = $({});
		var rtnOidArray = [];
		$.each(oidArray, function(k, v_oid){
			
			theQueue.queue('myqueue', function(next){
				$.ajax({
					type: "POST",
					async:false,
					handler: "lms2411m01formhandler",
					action: "checkC241M01AFromAreaWaitApproveToEnd",
					data: $.extend({
						mainOid: v_oid
					}, param)
					}).done(function(json){
						if(json.isCheckOk == "Y" ){
							rtnOidArray.push(json.c241m01aOid);
						}
				}).always(function(){//用 complete(不論done, fail)
					cnt++;
					if (cnt == target) {
						//更新LMS2401M01的 Grid
//						$gridview.trigger("reloadGrid");
						//更新 LMS2405V01 的 Grid
//						CommonAPI.triggerOpener("gridview", "reloadGrid");
					}
					//---
					//把 next() 寫在 finish 的 callback 裡
					//才會 1個 url 抓到 response 後,再get下1個
					//不然會1次跳 N 個出來
					next();
				});
			});
		});
		theQueue.dequeue('myqueue');
		
		if (cnt == target)
			return rtnOidArray;
	}

	
	function genCrsProjectNo(){
		$.ajax({
	           type: "POST",
	           handler: _handler,
	           data: {
	               formAction: "genCrsProjectNo",
	               mainOid: json.mainOid
	           }
	           }).done(function(responseData){
	           	var tabForm = $("#tabForm");
				tabForm.injectData(responseData);
	           	//更新LMS2401M01的 Grid
	           	$gridview.trigger("reloadGrid");
	           	//更新 LMS2405V01 的 Grid
	           	CommonAPI.triggerOpener("gridview", "reloadGrid");           	
		});
	}
	
	function movetoNextReviewTeamSupervisor(){
		//ui_lms2401.msg13=是否送覆審組主管？
		API.confirmMessage(i18n.lms2401m01["ui_lms2401.msg13"], function(result){
	        if(result){
				var c241m01aOidArray = [];
				var ids = $gridview.jqGrid('getGridParam','selarrrow');
				for(var i=0; i<ids.length; i++){
					var obj = $gridview.getRowData(ids[i]);
					c241m01aOidArray.push(obj.oid);
				}
				
				var rtnOidArray = checkC241M01A(c241m01aOidArray);
				if(typeof rtnOidArray != undefined && rtnOidArray.length){
					signContentC241M01E('B', rtnOidArray).done(function(){
						toNextReviewTeam(rtnOidArray, {'mainDocStatus':'01A', 'decisionExpr':'to_待覆核_覆審組'});
         	   		});
				}
	    	}
		});
	}

	var checkC241M01A = function(oidArray){
		var cnt = 0;
		var target = oidArray.length;
		var param = {'mainDocStatus':'01A'};
		var theQueue = $({});
		var rtnOidArray = [];
		$.each(oidArray, function(k, v_oid){
			
			theQueue.queue('myqueue', function(next){
				$.ajax({
					type: "POST",
					async:false,
					handler: "lms2411m01formhandler",
					action: "checkC241M01A",
					data: $.extend({
						mainOid: v_oid
					}, param)
					}).done(function(json){
						if(json.isCheckOk == "Y" ){
							rtnOidArray.push(json.c241m01aOid);
						}
				}).always(function(){//用 complete(不論done, fail)
					cnt++;
					if (cnt == target) {
						//更新LMS2401M01的 Grid
//						$gridview.trigger("reloadGrid");
						//更新 LMS2405V01 的 Grid
//						CommonAPI.triggerOpener("gridview", "reloadGrid");
					}
					//---
					//把 next() 寫在 finish 的 callback 裡
					//才會 1個 url 抓到 response 後,再get下1個
					//不然會1次跳 N 個出來
					next();
				});
			});
		});
		theQueue.dequeue('myqueue');
		
		if (cnt == target)
			return rtnOidArray;
	}

	var toNextReviewTeam = function(c241m01aOidArray, param){
		var cnt = 0;
		var target = c241m01aOidArray.length;
		var theQueue = $({});
		$.each(c241m01aOidArray, function(k, v_oid){
			
			theQueue.queue('myqueue', function(next){
				$.ajax({
					type: "POST",
					handler: "lms2411m01formhandler",
					action: "flowAction",
					data: $.extend({
						mainOid: v_oid
					}, param)
					}).done(function(json){
				}).always(function(){//用 complete(不論done, fail)
					cnt++;
					if (cnt == target) {
						//更新LMS2401M01的 Grid
						$gridview.trigger("reloadGrid");
						//更新 LMS2405V01 的 Grid
						CommonAPI.triggerOpener("gridview", "reloadGrid");
					}
					//---
					//把 next() 寫在 finish 的 callback 裡
					//才會 1個 url 抓到 response 後,再get下1個
					//不然會1次跳 N 個出來
					next();
				});
			});
		});
		theQueue.dequeue('myqueue');
	}

	$("#sign_cntB").change(function(){
        var $target = $("#new_sign_B_L4Span");
        //清空原本的
        $target.empty();
        
        var newdiv = "";
        var val = parseInt($(this).val(), 10);
        if (val > 1) {
            for (var i = 2, count = val + 1; i < count; i++) {
            	var idName = "sign_B_L4_"+i;
            	
                newdiv+=  
                ("<div>" 
                + i18n.lms2411m01['label.signSeqDescPrefix'] + i +i18n.lms2411m01['label.signSeqDescPostfix']
                +"&nbsp;&nbsp;&nbsp;<select id='"+idName+"' name='"+idName+"' class='selectSign_B_L4'/>&nbsp;"
                +"</div>");
            }
            $target.append(newdiv);
            
            var copyFrom = $("#sign_B_L4_1").html();//get srcElm html content
            var elementDom = $(".selectSign_B_L4");
            elementDom.html(copyFrom);
            //$(".selectSign_B_L4").html(copyFrom);
            //$(".selectSign_B_L4").injectData(copyFrom);
        }
    });
	
	var signContentC241M01E = function (signRole, oidArray){
		
		var param = {'mainOid':$("#mainOid").val(), 'signRole': signRole, 'mainOidArray':oidArray};
		var my_dfd = $.Deferred();
		$.ajax({ type: "POST", handler: _handler
			, data: $.extend( {formAction: "getSignList"}, param)
			}).done(function(json_signContent){
				if(json_signContent.canSkip && json_signContent.canSkip=="Y"){
					my_dfd.resolve();
				}else{
					var condL4 = null;
					var condL5 = null;
					var $div = null;
					var tb_title = "";
					if(signRole=="A"){
						condL4 = ".selectSign_A_L4"; 
						condL5 = ".selectSign_A_L5"; 
						$div = $("#divSignContent_A"); 
						tb_title = i18n.lms2411m01["reviewBrn.A"];//受檢單位
					}else if(signRole=="B"){
						condL4 = ".selectSign_B_L4"; 
						condL5 = ".selectSign_B_L5"; 
						$div = $("#divSignContent_B"); 
						tb_title = i18n.lms2411m01["reviewBrn.B"];//覆審單位
					}
					var selectSignL4 = $(condL4);
					var selectSignL5 = $(condL5);
					selectSignL4.setItems({ item: json_signContent.l4_list, space: true });
					selectSignL5.setItems({ item: json_signContent.l5_list, space: true });
	            	//=========
					$div.thickbox({
	        	        title: i18n.lms2411m01["label.signature"]+"("+tb_title+")",
	        	        width: 580, height: 250, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
	                    buttons: {
	                        "sure": function(){
	                        	var l4Arr = [];
	                        	var l5Arr = [];
	                        	                        	
	                        	$.each( selectSignL4, function(idx,obj){
	                        		var val = $(obj).val();                        	
	                        		l4Arr.push( val );
	                        	});
	                        	$.each( selectSignL5, function(idx,obj){
	                        		var val = $(obj).val();                        		
	                        		l5Arr.push( val );
	                        	});
	                        	                        		
	                        	$.ajax({ type: "POST", handler: _handler
	                    			, data:$.extend( {
	                    					formAction: "saveSignList"
	                    					,'l4Arr':l4Arr.join("|") 
	                    					,'l5Arr':l5Arr.join("|")
	                    			  }, param)
	                    			}).done(function(json_saveSignList){
	                    				if(json_saveSignList.saveSignFlag=="Y"){                    					
	                    					var tabForm = $("#tabForm");
	                        				tabForm.injectData(json_saveSignList);
	                        				
	                        				my_dfd.resolve();
	                        				$.thickbox.close();
	                    				}
	                        	});
	                        	
	                        },
	                        "cancel": function(){
	                        	my_dfd.reject();
	                        	$.thickbox.close();
	                        }
	                    }
	        	    });   
				}
		});		
		return my_dfd.promise();
	}
	
	function movetobr(){
		//整批送受檢單位登錄
		var rowId_arr = $gridview.getGridParam('selarrrow');
      	var oid_arr = [];
      	var not_oid_arr = [];
      	var not_oid_docKindS_arr = [];
      	for (var i = 0; i < rowId_arr.length; i++) {
      		var data = $gridview.getRowData(rowId_arr[i]);
      		if(data.movetobr_flag=="Y"){
      			oid_arr.push(data.oid);
      		}else{
      			if(data.docKind=="S"){
      				not_oid_docKindS_arr.push(data.custId+" "+data.custName);
      			}else{
      				not_oid_arr.push(data.custId+" "+data.custName);	
      			}      			
      		}
        }
      	
      	if(not_oid_arr.length > 0 ){
      	    //ui_lms2401.msg08=未維護覆審報告表
      		API.showMessage(not_oid_arr.join("、")+i18n.lms2401m01['ui_lms2401.msg08']);
      		return;
      	}
      	if(not_oid_docKindS_arr.length > 0 ){
      	    //ui_lms2401.msg14=格式為防杜代辦覆審
      		API.showMessage(not_oid_docKindS_arr.join("、")+i18n.lms2401m01['ui_lms2401.msg14']);
      		return;
      	}
      	if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
      		return;
      	}
      	//區中心_編製中 的代碼為 01A
      	//區中心_待覆核 的代碼為 02A
      	var param = {'mainDocStatus':'02A', 'decisionExpr':'to_編製中_分行端', 'addMetaDesc':'Y'};      	
      	//===================================
      	var cnt = 0;
		var target = oid_arr.length;
			
		var theQueue = $({});
		$.each(oid_arr,function(k, v_oid) {                                            			
			theQueue.queue('myqueue', function(next) {
				$.ajax({
		            type: "POST",
		            handler: "lms2411m01formhandler", action: "flowAction",
		            data:$.extend( { mainOid: v_oid } , param )                
		            }).done(function(json){ 
		        }).always(function(){//用 complete(不論done, fail)
		        	cnt++;
  					if(cnt==target){  						
  						//更新LMS2401M01的 Grid
  		            	$gridview.trigger("reloadGrid");
  		            	//更新 LMS2405V01 的 Grid
  		            	CommonAPI.triggerOpener("gridview", "reloadGrid");  
      				}
  					//---
  					//把 next() 寫在 finish 的 callback 裡
  					//才會 1個 url 抓到 response 後,再get下1個
  					//不然會1次跳 N 個出來
  					next();
		        });				
			});                                            			 
		});
		theQueue.dequeue('myqueue');
	}
	
	function importLN(){
		var rowId_arr = $gridview.getGridParam('selarrrow');
      	var oid_arr = [];
      	for (var i = 0; i < rowId_arr.length; i++) {
      		var data = $gridview.getRowData(rowId_arr[i]);
      		oid_arr.push(data.oid);
        }

      	if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
      		return;
      	}
      	 
      	$.ajax({
           type: "POST",
           handler: _handler,
           data: {
               formAction: "importLN",
               'oid_arr': oid_arr,
               mainOid: json.mainOid
           }
           }).done(function(responseData){
           	var tabForm = $("#tabForm");
			tabForm.injectData(responseData);
           	//更新LMS2401M01的 Grid
           	$gridview.trigger("reloadGrid");
           	//更新 LMS2405V01 的 Grid
           	CommonAPI.triggerOpener("gridview", "reloadGrid");           	
       });
	}
	function trans99(){
		$.ajax({
	           type: "POST",
	           handler: _handler,
	           data: {
	               formAction: "deriveFrom99",
	               mainOid: json.mainOid
	           }
	           }).done(function(responseData){
	           	var tabForm = $("#tabForm");
				tabForm.injectData(responseData);
	           	//更新LMS2401M01的 Grid
	           	$gridview.trigger("reloadGrid");
	           	//更新 LMS2405V01 的 Grid
	           	CommonAPI.triggerOpener("gridview", "reloadGrid");           	
	       });
	}
	function oid_end_to_01A(){
		var rowId_arr = $gridview.getGridParam('selarrrow');
      	var raw_oid_arr = [];
      	for (var i = 0; i < rowId_arr.length; i++) {
      		var data = $gridview.getRowData(rowId_arr[i]);
      		raw_oid_arr.push(data.oid);
        }

      	if(raw_oid_arr.length==0){
      		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
      		return;
      	}
      	
      	$.ajax({
	           type: "POST",
	           handler: _handler,
	           data: {
	                formAction: "oid_end_to_01A",
	                'oid_arr': raw_oid_arr,
	                mainOid: json.mainOid
	           }
	    }).done(function(json){
	    	choose_end_to_01A( $("#is_flowClass_throughBr").val()=="Y" ).done(function(json_choose_end_to_01A){
      		var oid_arr = json.choseOid;
      		var cnt = 0;
      		var target = oid_arr.length;
      		
      		var theQueue = $({});
       		$.each(oid_arr,function(k, v_oid) {                                            			
       			theQueue.queue('myqueue', function(next) {
       				$.ajax({
       		            type: "POST",
       		            handler: "lms2411m01formhandler", action: "checkFlow",
       		            data: $.extend({ 'mainOid': v_oid, 'act':'end_to_01A', 'forceFlag':'Y' }
       		            	, json_choose_end_to_01A
       		       		)                
       		            }).done(function(json_checkFlow){        				
       		        }).always(function(){//用 complete(不論done, fail)
       		        	cnt++;
       		        	if(cnt==target){
     			           	//更新LMS2401M01的 Grid
     			           	$gridview.trigger("reloadGrid");
     			           	//更新 LMS2405V01 的 Grid
     			           	CommonAPI.triggerOpener("gridview", "reloadGrid");
         				}
     					//---
     					//把 next() 寫在 finish 的 callback 裡
     					//才會 1個 url 抓到 response 後,再get下1個
     					//不然會1次跳 N 個出來
     					next();
       		        });				
       			});                                            			 
       		});
       		theQueue.dequeue('myqueue'); 
	    	});	
      	});
	}

	function update_ptMgrId(){
		var rowId_arr = $gridview.getGridParam('selarrrow');
      	var oid_arr = [];
      	for (var i = 0; i < rowId_arr.length; i++) {
      		var data = $gridview.getRowData(rowId_arr[i]);
      		oid_arr.push(data.oid);
        }

      	if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
      		return;
      	}
      	
      	var options = {};
		var my_dfd = $.Deferred();				
		my_dfd.done(function(rtnPtMgrIdJSON){					
			$.ajax({
	           type: "POST",
	           handler: _handler,
	           data: {
	               formAction: "update_ptMgrId",
	               'oid_arr': oid_arr,
	               mainOid: json.mainOid,
	               'ptMgrId':rtnPtMgrIdJSON.ptMgrId
	           }
	           }).done(function(responseData){
	        	   //更新LMS2401M01的 Grid
	        	   $gridview.trigger("reloadGrid");
	          	    
	        	   API.showMessage(i18n.def.runSuccess);       	
	       });
    	});
		RetrialPtMgrIdPanelAction.open(options, my_dfd);
	}

	function update_docFmt(){
		var rowId_arr = $gridview.getGridParam('selarrrow');
      	var oid_arr = [];
      	for (var i = 0; i < rowId_arr.length; i++) {
      		var data = $gridview.getRowData(rowId_arr[i]);
      		oid_arr.push(data.oid);
        }

      	if(oid_arr.length==0){
      		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
      		return;
      	}
      	
      	$.ajax({
           type: "POST",
           handler: _handler,
           data: {
               formAction: "update_docFmt",
               'oid_arr': oid_arr,
               mainOid: json.mainOid
           }
           }).done(function(responseData){
        	   //更新LMS2401M01的 Grid
        	   $gridview.trigger("reloadGrid");
          	    
        	   API.showMessage(i18n.def.runSuccess);       	
       });
	}

	function build_submenu(dyna, rdoName, submenu){
		$.each(submenu, function(k, v) { 
			dyna.push("   <p ><label id='_itemMenu_"+rdoName+"_"+k+"'><input type='radio' name='"+rdoName+"' value='"+k+"' class='required' />"+v+"</label></p>"); 
        });		
	}
	$("#btn_crud").click(function(){
		var _id = "_div_btn_crud";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");		
			dyna.push("<form id='"+_form+"'>");
			
			var submenu = {
				  '1':i18n.lms2401m01["btn.crud_add"]
				, '2':i18n.lms2401m01["btn.crud_dc"]
				, '3':i18n.lms2401m01["btn.crud_undc"]
				, '4':i18n.lms2401m01["btn.crud_escrow"]
				, '5':i18n.lms2401m01["btn.crud_pa"]
				};
			build_submenu(dyna, 'decision_crud', submenu);
			
			dyna.push("</form>");
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.lms2401m01["btn.crud"],
	        width: 660,
            height: 350,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decision_crud']:checked").val();
                    $.thickbox.close();
                    
                    if(val=="1"){//新增覆審客戶
                    	produceNew();                    	
                    }else if(val=="2"){//刪除(註記)不覆審客戶
                    	saveNoCTL();
                    }else if(val=="3"){//還原不覆審客戶
                    	saveReCTL();                    	 
                    }else if(val=="4"){//新增價金履約保證覆審客戶
                    	proc_escrow();
                    }else if(val=="5"){//新增防杜代辦覆審客戶
                    	proc_DocKindS();
                    }                
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	});
	
	$("#btn_print").click(function(){
		var rowId_arr = $gridview.getGridParam('selarrrow');
   	 	var oid_arr = [];
   	 	for (var i = 0; i < rowId_arr.length; i++) {
			var data = $gridview.getRowData(rowId_arr[i]);
			oid_arr.push(data.oid+"^"+data.mainId);
        }

   	 	if(oid_arr.length==0){   	 		
   	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
   	 		return;
   	 	}
   	 	printC241M01A( oid_arr.join("|") );
	});
	
	$("#btn_printOnlyY").click(function(){
		var rowId_arr = $gridview.jqGrid('getRowData');
		var oid_arr = [];
   	 	for (var i = 0; i < rowId_arr.length; i++) {
   	 		var data = rowId_arr[i];
			if (data.retrialYN == "Y") {
				oid_arr.push(data.oid+"^"+data.mainId);	
			}
        }

   	 	if(oid_arr.length==0){   	 		
   	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
   	 		return;
   	 	}
   	 	printC241M01A( oid_arr.join("|") );
	});
	
	//整批作業
	$("#btn_batchtask").click(function(){
		var _id = "_div_btn_batchtask";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");		
			dyna.push("<form id='"+_form+"'>");
			
			var submenu = {
				  '1':i18n.lms2401m01["btn.batchtask_projectNo"]
				, '2':i18n.lms2401m01["btn.batchtask_movetoNextReviewTeamSupervisor"]
				, '3':i18n.lms2401m01["btn.batchtask_movetobr"]
				, '4':i18n.lms2401m01["btn.batchtask_waitApprove_to_end"]
				, '5':i18n.lms2401m01["btn.batchtask_importLN"]
				, '6':i18n.lms2401m01["btn.batchtask_trans99"]
				, '7':i18n.lms2401m01["btn.batchtask_end_to_01A"]
				, '8':i18n.lms2401m01["btn.batchtask_update_ptMgrId"]
				, '9':i18n.lms2401m01["btn.batchtask_update_docFmt"]
				};
			if(json.is_flowClass_throughBr=="N"){
				/*
			  		目前選項 2, 3, 4 只有931 才有
				 */
				delete submenu['2'];
				delete submenu['3'];
				delete submenu['4'];
			}
			
			build_submenu(dyna, 'decision_batchtask', submenu);
			
			dyna.push("</form>");
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		     
//		     if(json.is_flowClass_throughBr=="N"){
//					$("#_itemMenu_decision_batchtask_2").find("input").attr("disabled", "true").css({opacity:0 });
//					$("#_itemMenu_decision_batchtask_3").find("input").attr("disabled", "true").css({opacity:0 });
//					$("#_itemMenu_decision_batchtask_4").find("input").attr("disabled", "true").css({opacity:0 });
//			 }
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.lms2401m01["btn.batchtask"],
	        width: 640,
            height: 380,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
					
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decision_batchtask']:checked").val();
                    $.thickbox.close();
                    
                    if(val=="1"){ 
                    	genCrsProjectNo();//重新編排覆審序號
                    }else if(val=="2"){
                    	movetoNextReviewTeamSupervisor(); //勾選資料(覆審組)覆審人員 送 (覆審組)主管
                    }else if(val=="3"){
                    	movetobr();//勾選(一般/土建融)覆審資料(覆審組)主管 移 受檢單位登錄
                    }else if(val=="4"){
                    	moveFromAreaWaitApproveToEnd(); // 勾選(防杜代辦)覆審資料(覆審組)主管 覆核結案
                    }else if(val=="5"){
                    	importLN();//勾選資料重引帳務
                    }else if(val=="6"){
                    	trans99();//重新整理類別99資料
                    }else if(val=="7"){
                    	oid_end_to_01A();//勾選資料依授管處意見修改覆審意見
                    }else if(val=="8"){
                    	update_ptMgrId();//重引覆審項目「應負責經理」
                    }else if(val=="9"){
                    	update_docFmt();//引入最新version的docFmt
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	});
	
	var choose_end_to_01A = function (isThrougrBr){
		var my_dfd = $.Deferred();
		//ui_lms2411.msg18=退回「覆審單位」覆審人員 
		//ui_lms2411.msg19=退回「受檢單位」經辦
		var _id = "_div_choose_end_to_01A";
		var _form = _id+"_form";
				
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.lms2411m01['ui_lms2411.msg18']+"</label></p>");
			if(isThrougrBr){
				dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.lms2411m01['ui_lms2411.msg19']+"</label></p>");
				/*
                // 2022/04/18 授審處連喬凱來電  分處對覆審報告表有回頭打考評表之需求
                // 1. 「已覆核已核定」之覆審報告表可以退為「已覆核未核定」修改_限制為有考評表且有傳送至分行的覆審報告表
                // 2. 不限上傳者本人，任何人都可以修改
                */
                dyna.push("		<p><label><input type='radio' name='decisionExpr' value='3' class='required' />"+i18n.lms2411m01['ui_lms2411.msg35']+"</label></p>");
			}
			dyna.push("</form>");				
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({
	       title: '', width: 280, height: 180, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
           buttons: {
               "sure": function(){
            	   
                   if (!$("#"+_form).valid()) {
                       return;
                   }
                   var decisionExpr = $("#"+_form).find("[name='decisionExpr']:checked").val();
                   if(decisionExpr=='2'){
                	   my_dfd.resolve({});
                   }else if(decisionExpr=='1'){
                	   my_dfd.resolve({'targetDocStatus':'010'});
                   }else if(decisionExpr=='3'){
                       my_dfd.resolve({'targetDocStatus':'050'});
                   }else{
                	   my_dfd.reject();   
                   }
                   $.thickbox.close();
               },
               "cancel": function(){
            	   my_dfd.reject();
            	   $.thickbox.close();
               }
           }   
        });		
		return my_dfd.promise();
	}
	
});

/**
 * 同時印多份: 
 * oid_arr.push(data.oid+"^"+data.mainId);
 * rptOid: oid_arr.join("|")
 */
function printC241M01A(rptOid){
	var _id = "_div_printC241M01A";	
	var _form = _id+"_form";
	var rdoName = "rdo_printC241M01A";
	var option = {
		'X':'經副襄理' ,
		'Y':'正副營運長'
	};	
	if ($("#"+_id).length == 0){
		var dyna = [];
		dyna.push("<div id='"+_id+"' style='display:none;' >");
		dyna.push("<form id='"+_form+"' >");
		$.each(option, function(k, v) { 
			dyna.push("   <p><label><input type='radio' name='"+rdoName+"' value='"+k+"' class='required' />"+v+"</label></p>"); 
        });
		dyna.push("</form>");
		dyna.push("</div>");
	    $('body').append(dyna.join(""));
	}

	$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
       title: "",
       width: 250,
       height: 160,
       align: "center",
       valign: "bottom",
       modal: false,
       readOnly: false,//當UI被另一人開啟時,也開放勾選列的功能
       i18n: i18n.def,
       buttons: {
           "sure": function(){
        	   if (!$("#"+_form).valid()) {
                   return;
               }
               var L5DescFlag = $("#"+_form).find("[name='"+rdoName+"']:checked").val();
               
               $.form.submit({
                   url: "../../simple/FileProcessingService",
                   target: "_blank",
                   data: {
                       'rptOid': rptOid,
                       'fileDownloadName': "lms2411r01.pdf",
                       'L5DescFlag' : L5DescFlag,
                       'isTwoPhase':'Y',
                       serviceName: "lms2411r01rptservice"            
                   }
               });
               $.thickbox.close();
           }
       }
	});
	
}

//將特殊字元轉換成 HTML 實體，例如 < 轉換成 &lt;
//避免在網頁中執行不受信任的 JavaScript 代碼，從而保護使用者的資訊安全。
function encodeHTML(str) {
	var isNumber = /^\d+$/.test(str);
	if (isNumber) {
		str = encodeURI(str);
	}
	str = str.replace(/[\&\<\>\"\'\/]/g, function(match) {
		switch (match) {
		case '&':
			return '&amp;';
		case '<':
			return '&lt;';
		case '>':
			return '&gt;';
		case '"':
			return '&quot;';
		case '\'':
			return '&#039;';
		default:
			return match;
		}
	});
	if (isNumber) {
		str = Number(encodeURI(str));
	}
	return str;
}
