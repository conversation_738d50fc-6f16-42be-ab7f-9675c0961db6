// J-112-0357 新增敘做條件異動比較表
initAll.done(function(){

    /**  敘做條件異動情形 **/
    initL140s11aGrid();
    // 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
    var isItemType1 = true;
    if (inits.itemType != "1") {
        isItemType1 = false;
        $("#btnOpenL140s11a").hide();
        $("#btnPreviewL140s11a").hide();
        $("#addL140s11a").hide();
        $("#deleteL140s11a").hide();
        $("#importL140s11a").hide();
    }

    function initL140s11aGrid(){
        $("#l140s11aGrid").iGrid({
            handler: "lms1401gridhandler",
            needPager: false,
            height: 100,
            sortname: 'seqNum|createTime',
            sortorder: 'asc|asc',
            postData: {
                formAction: "queryL140s11aList",
                tabFormMainId: $("#tabFormMainId").val()
            },
            loadComplete: function(){
                $('#l140s11aGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
                colHeader: i18n.lms1405s02["L140S11A.seqNum"],
                name: 'seqNum',
                width: 15,
                sortable : false,
                align: "center",
                hidden: true
            },{
                colHeader: i18n.lms1405s02["L140S11A.applyItem"],
                name: 'applyItem',
                width: 100,
                sortable : false,
                align: "center"
            },{
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){
                var data = $("#l140s11aGrid").getRowData(rowid);
                openL140S11aDetailBox(null, null, data);
            }
        }).trigger("reloadGrid");
        
        $("#importL140s11aGrid").iGrid({
            handler: "lms1405gridhandler",
            needPager: false,
            height: 150,
            hideMultiselect : true,
            sortname: 'cntrNo',
            sortorder: 'asc',
            postData: {
                formAction: "queryImportL140s11aList",
                tabFormMainId: $("#tabFormMainId").val(),
                mainId: $("#mainId").val()
            },
            loadComplete: function(){
                $('#importL140s11aGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
                colHeader: i18n.lms1405s02["L140M01a.cntrNo"],
                name: 'cntrNo',
                width: 80,
                sortable : false,
                align: "center"
            },{
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){
            }
        }).trigger("reloadGrid");
    }

    function openL140S11aDetailBox(cellvalue, options, data){
        var l140s11aFormDetail = $("#l140s11aFormDetail");
        var l140s11aDetailThickbox = $("#l140s11aDetailThickbox");
        l140s11aFormDetail.find("textarea").prop("readonly", false).val("");

        // 當新增時 data 會是 null
        var hasData = false;
        if (data != 'undefined' && data != null && data != 0) {
            hasData = true;
        }
        
        var buttons = {
        	"saveData": function(){
            	$.ajax({
                	handler: "lms1401m01formhandler",  //inits.fhandle,
                	action: "saveL140s11a",
                    data: $.extend(l140s11aFormDetail.serializeData(), {
                    oid: (hasData ? data.oid : ""),    // $("#l140s11aOid").val()
                    	tabFormMainId: $("#tabFormMainId").val()
                    }),
                    success: function(obj){
                    	$("#l140s11aGrid").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
                        	postData : {
                            	formAction: "queryL140s11aList",
                            	tabFormMainId: $("#tabFormMainId").val()
                            },
                            search : true
                        }).trigger("reloadGrid");

                        //saveSuccess=儲存成功
                        CommonAPI.confirmMessage(i18n.def["saveSuccess"], function(b){
                        	if (b) {
                            	$.thickbox.close();
                            }
                        });
                    }
                });
            }
        }
        if (inits.toreadOnly || !isItemType1) {
            delete buttons.saveData;
            l140s11aFormDetail.find("textarea").prop("readonly", true);
        }

        l140s11aDetailThickbox.thickbox({
            title: "",
            width: 1200,
            height: 600,
            modal: false,   // 會有X關閉視窗
            i18n: i18n.def,
            align: "center",
            valign: "bottom",
            buttons: buttons,
            open: function(){
            	//前准
            	if(!$.isEmptyObject(CKEDITOR.instances["befApply"])){
            		CKEDITOR.instances["befApply"].resize(360, 400);
            	}
                //本次申請
            	if(!$.isEmptyObject(CKEDITOR.instances["aftApply"])){
            		CKEDITOR.instances["aftApply"].resize(360, 400);
            	}
                //備註
                if(!$.isEmptyObject(CKEDITOR.instances["applyRemark"])){
                	CKEDITOR.instances["applyRemark"].resize(360, 400);
                }
                $.ajax({
                    handler: "lms1401m01formhandler",  //inits.fhandle,
                    action: "queryL140s11aDetail",
                    data: {
                        oid: (hasData ? data.oid : "")
                    },
                    success: function(obj){
                        l140s11aFormDetail.injectData(obj);
                    }
                });
            }
        });
    }
    //敘做條件異動情形 > 分項表格向上或向下移動
    function L140S11aUpDownBox(upDown){
    	//向上=true 向下=false
    	var row = $("#l140s11aGrid").getGridParam('selrow');
        if (!row || row == "") {
            return CommonAPI.showMessage(i18n.def["grid.selrow"])
        }  
        var data;
        if (row != 'undefined' && row != null) {
            data = $("#l140s11aGrid").getRowData(row);
        }
        //更新排序
        $.ajax({
            type: "POST",
            handler: "lms1405m01formhandler",
            data: {
                formAction: "changel140s11aSeqNum",
                detailOid: data.oid,
                tabFormMainId: $("#tabFormMainId").val(),
                upOrDown: upDown
            },
            success: function(){ 
                $("#l140s11aGrid").trigger("reloadGrid");
            }
        });
    }

    $("#btnOpenL140s11a").click(function(){
        var L140M01BForm = $("#L140M01BForm");
        var l140s11aThickbox = $("#l140s11aThickbox");

        var thickButtons = {
            "close": function(){
                $.thickbox.close();
            }
        }

        l140s11aThickbox.thickbox({
            title: i18n.lms1405s02["btn.openL140s11a"],
            width: 800,
            height: 300,
            align: "center",
            valign: "bottom",
            buttons: thickButtons
        });
        $("#l140s11aGrid").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
            postData : {
                formAction: "queryL140s11aList",
                tabFormMainId: $("#tabFormMainId").val()
            },
            search : true
        }).trigger("reloadGrid");
    });

    $("#btnPreviewL140s11a").click(function(){
        var previewL140s11aBox = $("#previewL140s11aBox");
        var previewL140s11aSpan = $("#previewL140s11aSpan");
        //J-113-0241 ELOAN-額度明細表-敘做條件異動情形-開啟分項表格編輯功能調整
        previewL140s11aBox.thickbox({
            title: "",
            align: "center",
            valign: "bottom",
            width: 700,
            height: 500,
            buttons: {
                "close": function(){
                    $.thickbox.close();
                }
            },
            open: function(){
            	$.ajax({
                    handler: "lms1401m01formhandler",   //inits.fhandle,
                    action: "previewL140s11a",
                    data: {
                        tabFormMainId: $("#tabFormMainId").val()
                    },
                    success: function(obj){
                        // 弱掃高風險 previewL140s11aSpan.html(obj.previewL140s11aStr);
                        previewL140s11aSpan.injectData({'previewL140s11aSpan':obj.previewL140s11aStr});
                    }
                });
            }
        }); 
    });

    $("#addL140s11a").click(function(){
        openL140S11aDetailBox(null, "add", null);
    });
    $("#deleteL140s11a").click(function(){
        var row = $("#l140s11aGrid").getGridParam('selrow');

        if (!row) {
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        else {
            // confirmDelete=是否確定刪除?
            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    var data = $("#l140s11aGrid").getRowData(row);
                    var oid = data.oid;
                    $.ajax({
                        type: "POST",
                        handler: "lms1401m01formhandler",  //inits.fhandle,
                        data: {
                            formAction: "deleteL140s11a",
                            oid: oid
                        },
                        success: function(responseData){
                            $("#l140s11aGrid").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
                                postData : {
                                    formAction: "queryL140s11aList",
                                    tabFormMainId: $("#tabFormMainId").val()
                                },
                                search : true
                            }).trigger("reloadGrid");
                        }
                    });
                }
            });
        }
    });
    $("#importL140s11a").click(function(){
    	var thickButtons = {
			"import": function(){
				var row = $("#importL140s11aGrid").getGridParam('selrow');
				if (!row) {
		            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
		        } else {
		        	var data = $("#importL140s11aGrid").getRowData(row);
                    var oid = data.oid;
                    $.thickbox.close();
                    $.ajax({
                        handler: "lms1405m01formhandler",
                        data: {
                            formAction: "importL140s11a",
                            oid: oid,
                            tabFormMainId: $("#tabFormMainId").val()
                        },
                        success: function(obj){
                        	$("#l140s11aGrid").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
                                postData : {
                                    formAction: "queryL140s11aList",
                                    tabFormMainId: $("#tabFormMainId").val()
                                },
                                search : true
                            }).trigger("reloadGrid");
                        }
                    });
		        }
			},
			"close": function(){
                $.thickbox.close();
            }
        }

    	if (inits.toreadOnly || !isItemType1) {
            delete thickButtons.import;
        }
    	
        $("#importL140s11aThickbox").thickbox({
            title: i18n.lms1405s02["btn.importOtherL140s11a"],
            width: 100,
            height: 300,
            align: "center",
            valign: "bottom",
            buttons: thickButtons
        });

        $("#importL140s11aGrid").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
            postData : {
                formAction: "queryImportL140s11aList",
                tabFormMainId: $("#tabFormMainId").val(),
                mainId: $("#mainId").val()
            },
            search : true
        }).trigger("reloadGrid");
    });
    //向上移動
    $("#upL140s11aSeq").click(function(){
    	L140S11aUpDownBox(true);
    });
    //向下移動
    $("#downL140s11aSeq").click(function(){
    	L140S11aUpDownBox(false);
    });
});
