/* 
 * LMS2405GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.handler.grid;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.crs.pages.LMS2405M01Page;
import com.mega.eloan.lms.crs.service.LMS2405Service;
import com.mega.eloan.lms.crs.service.LMS2415Service;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 覆審交易
 * </pre>
 * 
 * @since 2011/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/6,irene
 *          </ul>
 */
@Scope("request")
@Controller("lms2405gridhandler")
public class LMS2405GridHandler extends AbstractGridHandler {

	@Resource
	UserInfoService userservice;

	@Resource
	LMS2405Service lms2405service;
	@Resource
	LMS2415Service lms2415service;
	@Resource
	BranchService branch;

	Properties prop_lms2405m01 = MessageBundleScriptCreator
			.getComponentResource(LMS2405M01Page.class);

	/**
	 * 查詢Grid 覆審名單檔 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public CapGridResult queryMain(ISearch pageSetting, PageParameters params) throws CapException {

		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		String gridview_param = Util.trim(params.getString("gridview_param"));
		String filetData = Util.trim(params.getString("filetData"));
		if (Util.isNotEmpty(filetData)) {
			JSONObject jsoniletData = JSONObject.fromObject(filetData);
			String checkDateStart = Util.trim(jsoniletData
					.getString("checkDateStart"));
			if (Util.isNotEmpty(checkDateStart)) {
				pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
						"dataEndDate", CapDate.parseDate(checkDateStart+"-01"));
				pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS,
						"dataEndDate", CapDate.parseDate(CrsUtil.getDataEndDate(checkDateStart)));
			}
			String brId = Util.trim(jsoniletData.getString("brIdFilter"));
			if (Util.isNotEmpty(brId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"branchId", brId);
			}
		}
		if (Util.isNotEmpty(gridview_param)) {
			if (Util.equals(gridview_param, "THIS_MONTH")) {
				String s = Util.trim(StringUtils.substring(
						TWNDate.toAD(new Date()), 0, 7));
				pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
						"expectedRetrialDate", Util.parseDate(s + "-01"));
				pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS,
						"expectedRetrialDate",CapDate.parseDate(CrsUtil.getDataEndDate(s)));

			}
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.DOC_STATUS, docStatus);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"c240a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);

		Page page = lms2405service.findPage(C240M01A.class, pageSetting);
		List<C240M01A> list = page.getContent();
		for (C240M01A c240m01a : list) {
			// c240m01a.setBranchId(c240m01a.getBranchId()
			// + branch.getBranchName(c240m01a.getBranchId()));
			c240m01a.setUid(c240m01a.getBranchId()
					+ branch.getBranchName(c240m01a.getBranchId()));
			c240m01a.setUpdater(!Util.isEmpty(userservice.getUserName(c240m01a
					.getUpdater())) ? userservice.getUserName(c240m01a
					.getUpdater()) : c240m01a.getUpdater());

			// J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
			// RPA狀態*****************************************************
			if (!Util.isEmpty(Util.trim(c240m01a.getStatus()))) {
				c240m01a.setStatus(prop_lms2405m01.getProperty("rpa.status."
						+ Util.trim(c240m01a.getStatus())));

			} else {
				c240m01a.setStatus("");
			}

		}

		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢Grid 覆審明細
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryList(ISearch pageSetting, PageParameters params) throws CapException {
		ISearch search = createSearchTemplete();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		search.addSearchModeParameters(SearchMode.EQUALS, "c240m01b.mainId",
				mainId);

		pageSetting.addSearchModeParameters(search);
		Page page = lms2405service.findPage(C241M01A.class, pageSetting);
		List<C241M01A> list = page.getContent();
		for (C241M01A c240m01a : list) {
			if (c240m01a.getProjectNo() != null) {
				int lengh = c240m01a.getProjectNo().length();
				if (lengh > 6) {
					c240m01a.setProjectNo(c240m01a.getProjectNo().substring(
							lengh - 4, lengh - 1));
				}
			}
			if (!Util.isEmpty(c240m01a.getCustId())
					&& !Util.isEmpty(c240m01a.getDupNo())) {
				c240m01a.setCustId(c240m01a.getCustId() + " "
						+ c240m01a.getDupNo());
			}
			if (!Util.isEmpty(c240m01a.getDocStatus())) {
				c240m01a.setDocStatus(RetrialDocStatusEnum.getEnum(
						c240m01a.getDocStatus()).name());
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢需列印的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryPrint(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("oids"));
		// pageSetting
		// .addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.setDistinct(true);
		Page<Map<String, Object>> page = lms2415service.getBorrows(mainId,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryDocFile(ISearch pageSetting,
			PageParameters params) throws CapException {
		ISearch search = createSearchTemplete();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(search);
		Page page = lms2405service.findPage(DocFile.class, pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}
}
