
package com.mega.eloan.lms.cls.panels;

import org.kordamp.json.JSONObject;
import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.C340M01A;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 消金線上對保契約書
 * </pre>
 * 
 * @since 2021/05/31
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/05/31,008034,new
 *          </ul>
 */
public class CLS3401S061Panel extends Panel {

	private C340M01A meta;

	private JSONObject jsonObject;

	public CLS3401S061Panel(String id) {
		super(id);
	}

	public CLS3401S061Panel(String id, boolean updatePanelName, C340M01A meta,
			JSONObject jsonObject) {
		super(id, updatePanelName);
		this.meta = meta;
		this.jsonObject = jsonObject;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		new DocLogPanel("_docLog").processPanelData(model, params);
		
		boolean is_send_ploan = (meta != null && Util.equals(
				CreditDocStatusEnum.海外_已核准.getCode(), meta.getDocStatus()));
		model.addAttribute("showPloanColumn", is_send_ploan);

		boolean showOtherInfoTitle = false;
		if(jsonObject!=null){
			showOtherInfoTitle = Util.isNotEmpty(Util.trim(jsonObject.optString("otherInfoTitle"))); 
		}
		model.addAttribute("showOtherInfoTitle", showOtherInfoTitle);
	}

	private static final long serialVersionUID = 1L;
}
