/* 
 * L161S01EDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L161S01E;

/** 動審表查詢名單明細檔 **/
public interface L161S01EDao extends IGenericDao<L161S01E> {

	L161S01E findByOid(String oid);
	
	List<L161S01E> findByMainId(String mainId);

	List<L161S01E> findByIndex01(String mainId);
	
	List<L161S01E> findByIndex02(String mainId, String custId, String dupNo);
	
	// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
	List<L161S01E> findByOid(String[] oids);
}