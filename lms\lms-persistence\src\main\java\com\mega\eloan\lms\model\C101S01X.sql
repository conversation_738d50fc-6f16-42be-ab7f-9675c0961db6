---------------------------------------------------------
-- LMS.C101S01X 勞工紓困4.0簡易信用評分檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.C101S01X;
CREATE TABLE LMS.C101S01X (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	CUSTID        VARCHAR(10)   not null,
	<PERSON>UPNO         CHAR(1)       not null,
	ISOTHERASSETS CHAR(1)      ,
	APPLYQUOTA    DECIMAL(2,0) ,
	APPLYDATE     DATE         ,
	INSUREDMONTH  DECIMAL(2,0) ,
	UPTO23800MONTH DECIMAL(2,0) ,
	A1SCORE       DECIMAL(3,0) ,
	B1SCORE       DECIMAL(3,0) ,
	C1SCORE       DECIMAL(3,0) ,
	TOTALSCORE    DECIMAL(3,0) ,
	NOTALLOWA     DECIMAL(1,0) ,
	NOTALLOWB     DECIMAL(1,0) ,
	NOTALLOWC     DECIMAL(1,0) ,
	NOTALLOWD     DECIMAL(1,0) ,
	NOTALLOWE     DECIMAL(1,0) ,
	ISAPPROVED    CHAR(1)      ,
	FLOWFLAG      CHAR(1)      ,
	ISPTAFLAG     CHAR(1)      ,
	ISSTAKEHOLDER CHAR(1)      ,
	ISOTHERADDMARK CHAR(1)      ,
	ISIDCHANGEDMARK CHAR(1)      ,
	ISCASENOTIFIEDMARK CHAR(1)      ,
	IP            VARCHAR(20)  ,
	IPCOUNT       DECIMAL(4,0) ,
	TEL           VARCHAR(15)  ,
	TELCOUNT      DECIMAL(4,0) ,
	ISEATTACHEDPASS CHAR(1)      ,
	CREATOR       CHAR(06)     ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(06)     ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_C101S01X PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC101S01X01;
CREATE UNIQUE INDEX LMS.XC101S01X01 ON LMS.C101S01X (MAINID, CUSTID, DUPNO);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C101S01X IS '勞工紓困4.0簡易信用評分檔';
COMMENT ON LMS.C101S01X (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	CUSTID        IS '身分證號', 
	DUPNO         IS '身分證統編重複碼', 
	ISOTHERASSETS IS '是否有其他財力或資產證明', 
	APPLYQUOTA    IS '申請額度', 
	APPLYDATE     IS '申請日期', 
	INSUREDMONTH  IS '投保月數', 
	UPTO23800MONTH IS '月投保薪資達23800月數', 
	A1SCORE       IS 'A1得分', 
	B1SCORE       IS 'B1得分', 
	C1SCORE       IS 'C1得分', 
	TOTALSCORE    IS '總分', 
	NOTALLOWA     IS '不核可項目1', 
	NOTALLOWB     IS '不核可項目2', 
	NOTALLOWC     IS '不核可項目3', 
	NOTALLOWD     IS '不核可項目4', 
	NOTALLOWE     IS '不核可項目5', 
	ISAPPROVED    IS '是否已核可', 
	FLOWFLAG      IS '流程註記', 
	ISPTAFLAG     IS '是否為薪轉戶', 
	ISSTAKEHOLDER IS '是否為利害關係人', 
	ISOTHERADDMARK IS '其它補充註記', 
	ISIDCHANGEDMARK IS '身分證號更改紀錄', 
	ISCASENOTIFIEDMARK IS '案件通報紀錄', 
	IP            IS 'Ip', 
	IPCOUNT       IS '相同Ip數量', 
	TEL           IS '電話', 
	TELCOUNT      IS '相同電話數量', 
	ISEATTACHEDPASS IS '是否符合不核可項目5附帶條件', 
	CREATOR       IS '文件建立者', 
	CREATETIME    IS '文件建立日期', 
	UPDATER       IS '資料修改人(行編)', 
	UPDATETIME    IS '資料修改日期'
);
