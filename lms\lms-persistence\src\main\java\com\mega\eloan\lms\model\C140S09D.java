package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.RelativeMeta;


/**
 * <pre>
 * C140S09D model.
 * </pre>
 * 
 * @since 2011/10/27
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/27,<PERSON>,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "C140S09D-entity-graph", attributeNodes = { @NamedAttributeNode("c140m01a") })
@Entity
@Table(name="C140S09D", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C140S09D extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;

	@Column(name="GBA_AGM1", precision=12)
	private BigDecimal gbaAgm1;

	//J-105-0080-001 Web e-Loan授信管理系統集團轄下公司名稱欄位放大為38個全形字
	@Column(name="GBA_NA1", length=120)
	private String gbaNa1;

	@Column(name="GBA_ODM1", precision=12)
	private BigDecimal gbaOdm1;

	@Column(name="GBA_RAMT", precision=12)
	private BigDecimal gbaRamt;

	@Column(name="GBA_XDM1", precision=12)
	private BigDecimal gbaXdm1;

	//bi-directional many-to-one association to C140M01A
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({ @JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
        @JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false) })
	private C140M01A c140m01a;

	public BigDecimal getGbaAgm1() {
		return this.gbaAgm1;
	}

	public void setGbaAgm1(BigDecimal gbaAgm1) {
		this.gbaAgm1 = gbaAgm1;
	}

	public String getGbaNa1() {
		return this.gbaNa1;
	}

	public void setGbaNa1(String gbaNa1) {
		this.gbaNa1 = gbaNa1;
	}

	public BigDecimal getGbaOdm1() {
		return this.gbaOdm1;
	}

	public void setGbaOdm1(BigDecimal gbaOdm1) {
		this.gbaOdm1 = gbaOdm1;
	}

	public BigDecimal getGbaRamt() {
		return this.gbaRamt;
	}

	public void setGbaRamt(BigDecimal gbaRamt) {
		this.gbaRamt = gbaRamt;
	}

	public BigDecimal getGbaXdm1() {
		return this.gbaXdm1;
	}

	public void setGbaXdm1(BigDecimal gbaXdm1) {
		this.gbaXdm1 = gbaXdm1;
	}

	public C140M01A getC140m01a() {
		return this.c140m01a;
	}

	public void setC140m01a(C140M01A c140m01a) {
		this.c140m01a = c140m01a;
	}
	
}