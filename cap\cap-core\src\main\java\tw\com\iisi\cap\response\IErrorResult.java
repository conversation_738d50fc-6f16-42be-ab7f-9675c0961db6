/* 
 * IErrorResult.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */

package tw.com.iisi.cap.response;

import com.iisigroup.cap.component.PageParameters;

/**
 * <pre>
 * 錯誤訊息處理
 * </pre>
 * 
 * @since 2011/1/28
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011/1/28,iristu,new
 *          </ul>
 */
public interface IErrorResult extends IResult {

    /**
     * 錯誤訊息處理
     * 
     * @param request
     *            請求
     * @param e
     *            例外
     */
    void putError(PageParameters request, Exception e);

}
