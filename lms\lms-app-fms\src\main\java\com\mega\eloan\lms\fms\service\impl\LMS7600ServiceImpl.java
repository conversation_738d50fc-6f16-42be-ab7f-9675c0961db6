package com.mega.eloan.lms.fms.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.dao.L140MM4ADao;
import com.mega.eloan.lms.dao.L140MM4BDao;
import com.mega.eloan.lms.dao.L140MM4CDao;
import com.mega.eloan.lms.fms.pages.LMS7600M01Page;
import com.mega.eloan.lms.fms.service.LMS7600Service;
import com.mega.eloan.lms.mfaloan.bean.ELF600;
import com.mega.eloan.lms.mfaloan.service.MisELF600Service;
import com.mega.eloan.lms.model.L140MM4A;
import com.mega.eloan.lms.model.L140MM4B;
import com.mega.eloan.lms.model.L140MM4C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 空地貸款維護作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class LMS7600ServiceImpl extends AbstractCapService implements
		LMS7600Service {
	private static final Logger logger = LoggerFactory.getLogger(LMS7600ServiceImpl.class);
	
	@Resource
	L140MM4ADao l140mm4aDao;
	
	@Resource
	L140MM4BDao l140mm4bDao;
	
	@Resource
	L140MM4CDao l140mm4cDao;
	
	@Resource
	DocLogService docLogService;

	@Resource
	FlowService flowService;
	
	@Resource
	TempDataService tempDataService;

	@Resource
	MisELF600Service misELF600Service;

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L140MM4A.class) {
			return l140mm4aDao.findPage(search);
		} else if(clazz == L140MM4B.class){
			return l140mm4bDao.findPage(search);
		} else if(clazz == L140MM4C.class){
			return l140mm4cDao.findPage(search);
		}
		return null;
	}
	
	@Override
	public boolean deleteL140mm4as(String[] oids) {
		boolean flag = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L140MM4A> l140mm4as = new ArrayList<L140MM4A>();
		for (int i = 0, size = oids.length; i < size; i++) {
			L140MM4A l140mm4a = (L140MM4A) findModelByOid(L140MM4A.class,
					oids[i]);
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			l140mm4a.setDeletedTime(CapDate.getCurrentTimestamp());
			l140mm4a.setUpdater(user.getUserId());
			l140mm4as.add(l140mm4a);
			docLogService.record(l140mm4a.getOid(), DocLogEnum.DELETE);
		}
		if (!l140mm4as.isEmpty()) {
			l140mm4aDao.save(l140mm4as);
			flag = true;
		}
		return flag;
	}
	
	@Override
	public List<L140MM4C> findL140mm4csByMainId(String mainId) {
		return l140mm4cDao.findByMainId(mainId);
	}
	
	@Override
	public  Map<String, String> getData(L140MM4A l140mm4a) throws CapException {
		Map<String, String> returnMap = new LinkedHashMap<String, String>();
		String mainId = l140mm4a.getMainId();
		String cntrNo = l140mm4a.getCntrNo();
		returnMap.put("mainId", mainId);
		
		if (Util.equals(cntrNo, "")) {
			Properties pop = MessageBundleScriptCreator.getComponentResource(LMS7600M01Page.class);
			// 額度序號不得為空白
			throw new CapMessageException(pop.getProperty("cntrNoEmpty"), getClass());
		}
		
		returnMap.put("cntrNo", cntrNo);
		returnMap.put("queryDate", (Util.equals(Util.nullToSpace(l140mm4a.getQueryDate()) , "") ? "" :
			CapDate.formatDate(l140mm4a.getQueryDate(),UtilConstants.DateFormat.YYYY_MM_DD)));
		
		return returnMap;
	}
	
	public  Map<String, String> getElfData(L140MM4A l140mm4a)
		throws CapException {
		Map<String, String> returnMap = new LinkedHashMap<String, String>();
		String toDayStr = CapDate.formatDate(new Date(),UtilConstants.DateFormat.YYYY_MM_DD);
		String mainId = l140mm4a.getMainId();
		String cntrNo = l140mm4a.getCntrNo();
		returnMap.put("mainId", mainId);
		
		if (Util.equals(cntrNo, "")) {
			Properties pop = MessageBundleScriptCreator.getComponentResource(LMS7600M01Page.class);
			// 額度序號不得為空白
			throw new CapMessageException(pop.getProperty("cntrNoEmpty"), getClass());
		}
		
		ELF600 elf600 = misELF600Service.findByContract(cntrNo);
		if (elf600 != null && Util.isNotEmpty(elf600)) {
			this.newL140mm4c(mainId, elf600, "N");
			this.newL140mm4c(mainId, elf600, "Y");
		}
		
		l140mm4a.setCntrNo(cntrNo);
		l140mm4a.setQueryDate(CapDate.parseDate(toDayStr));
		l140mm4a.setChkYN("");
		l140mm4aDao.save(l140mm4a);
		
		returnMap.put("cntrNo", cntrNo); 
		returnMap.put("queryDate", toDayStr);
		
		return returnMap;
	}
	
	public  Map<String, String> getElf600Data(L140MM4A l140mm4a)
		throws CapException {
		Map<String, String> returnMap = new LinkedHashMap<String, String>();
		String toDayStr = CapDate.formatDate(new Date(),UtilConstants.DateFormat.YYYY_MM_DD);
		String mainId = l140mm4a.getMainId();
		String cntrNo = l140mm4a.getCntrNo();
		returnMap.put("mainId", mainId);
		
		if (Util.equals(cntrNo, "")) {
			Properties pop = MessageBundleScriptCreator.getComponentResource(LMS7600M01Page.class);
			// 額度序號不得為空白
			throw new CapMessageException(pop.getProperty("cntrNoEmpty"), getClass());
		}
		
		ELF600 elf600 = misELF600Service.findByContract(cntrNo);
		if (elf600 != null && Util.isNotEmpty(elf600)) {
			this.newL140mm4c(mainId, elf600, "N");
		}
		
		l140mm4a.setCntrNo(cntrNo);
		l140mm4a.setQueryDate(CapDate.parseDate(toDayStr));
		l140mm4a.setChkYN("");
		l140mm4aDao.save(l140mm4a);
		
		returnMap.put("cntrNo", cntrNo); 
		returnMap.put("queryDate", toDayStr);
		
		return returnMap;		
	}
	
	@Override
	public L140MM4C findCurrentL140mm4c(String mainId) {
		return l140mm4cDao.findCurrentByMainId(mainId);
	}
	
	@Override
	public L140MM4C findLastL140mm4c(String mainId) {
		return l140mm4cDao.findLastByMainId(mainId);
	}

	@Override
	public L140MM4A findL140mm4aByUniqueKey(String mainId) {
		return l140mm4aDao.findByUniqueKey(mainId);
	}
	
	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L140MM4A.class) {
			return l140mm4aDao.findByMainId(mainId);
		} else if (clazz == L140MM4B.class) {
			return l140mm4bDao.findByMainId(mainId);
		} else if (clazz == L140MM4C.class) {
			return l140mm4cDao.findByMainId(mainId);
		}
		return null;
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L140MM4A.class) {
			return (T) l140mm4aDao.findByOid(oid);
		} else if(clazz == L140MM4B.class){
			return (T) l140mm4bDao.findByOid(oid);
		} else if(clazz == L140MM4C.class){
			return (T) l140mm4cDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L140MM4A) {
					if (Util.isEmpty(((L140MM4A) model).getOid())) {
						((L140MM4A) model).setCreator(user.getUserId());
						((L140MM4A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
						l140mm4aDao.save((L140MM4A) model);

						flowService.start("LMS7600Flow",
								((L140MM4A) model).getOid(), user.getUserId(),
								user.getUnitNo());
					} else {
						// 當文件狀態為編製中時文件亂碼才變更

						((L140MM4A) model).setUpdater(user.getUserId());
						((L140MM4A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						l140mm4aDao.save((L140MM4A) model);
						if (!"Y".equals(SimpleContextHolder
								.get(EloanConstants.TEMPSAVE_RUN))) {
							tempDataService.deleteByMainId(((L140MM4A) model)
									.getMainId());
							docLogService.record(((L140MM4A) model).getOid(),
									DocLogEnum.SAVE);
						}
					}
				} else if (model instanceof L140MM4B) {
					((L140MM4B) model).setUpdater(user.getUserId());
					((L140MM4B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140mm4bDao.save((L140MM4B) model);
				} else if (model instanceof L140MM4C) {
					((L140MM4C) model).setUpdater(user.getUserId());
					((L140MM4C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140mm4cDao.save((L140MM4C) model);
				}
			}
		}
	}

	@Override
	public void deleteL140mm4bs(List<L140MM4B> l140mm4bs, boolean isAll) {
		if (isAll) {
			l140mm4bDao.delete(l140mm4bs);
		} else {
			List<L140MM4B> L140MM3BsOld = new ArrayList<L140MM4B>();
			for (L140MM4B l140mm3b : l140mm4bs) {
				String staffJob = l140mm3b.getStaffJob();
				if (!("L6".equals(staffJob) || "L7".equals(staffJob))) {
					L140MM3BsOld.add(l140mm3b);
				}
			}
			l140mm4bDao.delete(L140MM3BsOld);
		}
	}

	@Override
	public void saveL140mm4bList(List<L140MM4B> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L140MM4B l140mm4b : list) {
			l140mm4b.setUpdater(user.getUserId());
			l140mm4b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l140mm4bDao.save(list);
	}
	
	@Override
	public L140MM4B findL140mm4b(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
		return l140mm4bDao.findByUniqueKey(mainId, branchType, branchId,
				staffNo, staffJob);
	}
	
	@Override
	public void flowAction(String mainOid, L140MM4A model, boolean setResult,
			boolean resultType, boolean upMis) throws Throwable {
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS7600Flow",
						((L140MM4A) model).getOid(), user.getUserId(),
						user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				// resultType 控制前進還是後退
				// 當有先行動用的狀態 是到03O 非先行動用表示已完成 到05O
				inst.setAttribute("result", resultType ? "核准" : "退回");
				if (resultType) {
					//save((L140MM4A) model);
					if (upMis) {
						L140MM4A l140mm4a = (L140MM4A) findModelByOid(
								L140MM4A.class, mainOid);
						// 動審表簽章欄檔取得人員職稱
						List<L140MM4B> l140mm4blist = l140mm4bDao
								.findByMainId(l140mm4a.getMainId());
						String apprId = "";
						String reCheckId = "";

						for (L140MM4B l140mm4b : l140mm4blist) {
							String StaffJob = Util.trim(l140mm4b.getStaffJob());// 取得人員職稱
							String StaffNo = Util.trim(l140mm4b.getStaffNo());// 取得行員代碼
							if (Util.equals(StaffJob, "L1")) {// 分行經辦
								apprId = StaffNo;
							} else if (Util.equals(StaffJob, "L4")) {// 分行覆核主管
								reCheckId = StaffNo;
							}
						}
						// 若人員職稱為空值改取l140mm4a上的人員資料
						if (Util.isEmpty(apprId)) {
							apprId = l140mm4a.getUpdater();
						}
						if (Util.isEmpty(reCheckId)) {
							reCheckId = l140mm4a.getApprover();
						}

						String mainId = l140mm4a.getMainId();
						String cntrNo = l140mm4a.getCntrNo();
						String custId = l140mm4a.getCustId();
						String dupNo = l140mm4a.getDupNo();

						ELF600 elf600 = misELF600Service.findByContract(cntrNo);
						L140MM4C l140mm4c = l140mm4cDao.findCurrentByMainId(mainId);
						ELF600 newElf600 =  this.convertELF600(l140mm4c, custId, dupNo);
						if (elf600 != null && Util.isNotEmpty(elf600)) {
							// delete insert
							newElf600.setElf600_loan_bal(elf600.getElf600_loan_bal());
							misELF600Service.delete(cntrNo);
							misELF600Service.insert(newElf600);
						} else {
							misELF600Service.insert(newElf600);
						}						
					}
				}
			}
			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}
	
	private ELF600 convertELF600(L140MM4C l140mm4c, String custId, String dupNo){
		ELF600 newElf600 = new ELF600();
		
		newElf600.setElf600_cust_id(custId);
		newElf600.setElf600_cust_dup(dupNo);
		
		String newCntrNo = l140mm4c.getContract();
		newElf600.setElf600_contract(newCntrNo);
		
		newElf600.setElf600_use_date(new java.sql.Date(l140mm4c.getUse_Date().getTime()));
		newElf600.setElf600_usecd(l140mm4c.getUseCd());
		newElf600.setElf600_usetype(l140mm4c.getUseType());
		newElf600.setElf600_landtype(l140mm4c.getLandType());
		
		newElf600.setElf600_landkind(l140mm4c.getLandKind());
		
		newElf600.setElf600_idleland(l140mm4c.getIdleLand());
		newElf600.setElf600_ctltype(l140mm4c.getCtlType());
		if(l140mm4c.getFstDate() != null || Util.notEquals(Util.nullToSpace(l140mm4c.getFstDate()), "")){
			newElf600.setElf600_fstdate(new java.sql.Date(l140mm4c.getFstDate().getTime()));
		}
		if(l140mm4c.getLstDate() != null || Util.notEquals(Util.nullToSpace(l140mm4c.getLstDate()), "")){
			newElf600.setElf600_lstdate(new java.sql.Date(l140mm4c.getLstDate().getTime()));
		}
		
		if(l140mm4c.getRrmoveDate() != null || Util.notEquals(Util.nullToSpace(l140mm4c.getRrmoveDate()), "")){
			newElf600.setElf600_rrmovedt(new java.sql.Date(l140mm4c.getRrmoveDate().getTime()));
		}
		newElf600.setElf600_elflag(l140mm4c.getElFlag());
		if(l140mm4c.getEndDate() != null || Util.notEquals(Util.nullToSpace(l140mm4c.getEndDate()), "")){
			newElf600.setElf600_enddate(new java.sql.Date(l140mm4c.getEndDate().getTime()));
		}
		newElf600.setElf600_documentno(l140mm4c.getDocumentNo());
		if(l140mm4c.getCstDate() != null || Util.notEquals(Util.nullToSpace(l140mm4c.getCstDate()), "")){
			newElf600.setElf600_cstdate(new java.sql.Date(l140mm4c.getCstDate().getTime()));
		}
		newElf600.setElf600_adoptfg(l140mm4c.getAdoptFg());
		newElf600.setElf600_rateadd(l140mm4c.getRateAdd());
		newElf600.setElf600_custroa(l140mm4c.getCustRoa());
		newElf600.setElf600_relroa(l140mm4c.getRelRoa());
		newElf600.setElf600_cstreason(l140mm4c.getCstReason());
		
		newElf600.setElf600_islegal(l140mm4c.getIsLegal());
		
		newElf600.setElf600_loan_bal(Util.parseBigDecimal(0));
		
		newElf600.setElf600_updater(l140mm4c.getUpdater());
		newElf600.setElf600_tmestamp(CapDate.getCurrentTimestamp());
		newElf600.setElf600_universal_id(l140mm4c.getMainId());
		newElf600.setElf600_updfrom("FMS");

		return newElf600;
	}

	public String changeDateToString(Date datetime) {
		String date = "";
		date = (Util.equals(Util.nullToSpace(datetime) , "") ? "" :
			CapDate.formatDate(datetime,UtilConstants.DateFormat.YYYY_MM_DD));
		return date;
	}
	
	public void newL140mm4c(String mainId, ELF600 elf600, String flag) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L140MM4C l140mm4c = new L140MM4C();
		l140mm4c.setMainId(mainId);
		l140mm4c.setFlag(flag);
		l140mm4c.setCreateTime(CapDate.getCurrentTimestamp());
		l140mm4c.setCreator(user.getUserId());
		
		l140mm4c.setContract(Util.trim(elf600.getElf600_contract()));
		l140mm4c.setUse_Date(elf600.getElf600_use_date());
		l140mm4c.setUseCd(Util.trim(elf600.getElf600_usecd()));
		l140mm4c.setUseType(Util.trim(elf600.getElf600_usetype()));
		l140mm4c.setLandType(Util.trim(elf600.getElf600_landtype()));
		l140mm4c.setLandKind(Util.trim(elf600.getElf600_landkind()));
		l140mm4c.setIdleLand(Util.trim(elf600.getElf600_idleland()));
		l140mm4c.setCtlType(Util.trim(elf600.getElf600_ctltype()));
		l140mm4c.setFstDate(elf600.getElf600_fstdate());
		l140mm4c.setLstDate(elf600.getElf600_lstdate());
		l140mm4c.setRrmoveDate(elf600.getElf600_rrmovedt());
		l140mm4c.setElFlag(Util.trim(elf600.getElf600_elflag()));
		l140mm4c.setEndDate(elf600.getElf600_enddate());
		l140mm4c.setDocumentNo(Util.trim(elf600.getElf600_documentno()));
		l140mm4c.setCstDate(elf600.getElf600_cstdate());
		l140mm4c.setAdoptFg(Util.trim(elf600.getElf600_adoptfg()));
		l140mm4c.setRateAdd(elf600.getElf600_rateadd());
		l140mm4c.setCustRoa(elf600.getElf600_custroa());
		l140mm4c.setRelRoa(elf600.getElf600_relroa());
		l140mm4c.setCstReason(Util.trim(elf600.getElf600_cstreason()));
		l140mm4c.setIsLegal(Util.trim(elf600.getElf600_islegal()));
				
		L140MM4C data = new L140MM4C();
		if(Util.equals("N", flag)){
			data = l140mm4cDao.findLastByMainId(mainId);
		} else if(Util.equals("Y", flag)){
			data = l140mm4cDao.findCurrentByMainId(mainId);
		}
		l140mm4cDao.delete(data);
		l140mm4cDao.save(l140mm4c);
	}
	
	@Override
	public void delete(GenericBean... entity) {
		// TODO Auto-generated method stub
	}
}
