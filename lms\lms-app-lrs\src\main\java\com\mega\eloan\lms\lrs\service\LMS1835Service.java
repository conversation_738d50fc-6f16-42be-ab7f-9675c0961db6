/* 
 * LMS1835Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.service;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.lms.model.L184M01A;

public interface LMS1835Service extends ICapService {

	/**
	 * 查GRID資料
	 * 
	 * @param class1
	 * @param pageSetting
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	/**
	 * 查詢LMS.BDOC是否有此筆資料
	 * 
	 * @param mainId
	 * @param FieldId
	 * @return
	 */
	List<DocFile> findDocFile(String mainId, String FieldId);

	/**
	 * 刪除(刪除註記)
	 * 
	 * @param string
	 * @param listName
	 */
	void delete(String[] mainIds);

	/**
	 * 產生EXCEL
	 * 
	 * @param brNo
	 * @param dataDate
	 * @param listName
	 * @return
	 * @throws Exception 
	 */
	boolean transportExcel(boolean overSea, String brNo, Date dataDate, String listName) throws Exception;

	/**
	 * 找L184M01ByOid
	 * 
	 * @param oid
	 * @return
	 */
	L184M01A fL184m01aByoid(String oid);

	/**
	 * 儲存(篩選 全部的 model)
	 * 
	 * @param entity
	 */
	void save(GenericBean... entity);

	/**
	 * 找此筆Excel By dataDate, branchId
	 * 
	 * @param dataDate
	 * @param string
	 * @return
	 */

	L184M01A fL184m01aByDataDateAndBrNO(Date date, String branchId);

}
