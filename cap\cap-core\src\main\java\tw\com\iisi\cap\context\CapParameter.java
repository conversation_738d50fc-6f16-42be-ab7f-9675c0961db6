/* 
 * CapParameter.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package tw.com.iisi.cap.context;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import org.springframework.expression.ParserContext;
import org.springframework.expression.common.TemplateParserContext;

/**
 * <pre>
 * 取得從Request來的參數
 * </pre>
 * 
 * @since 2011/6/30
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011/6/30,iristu,new
 *          </ul>
 */
public class CapParameter {

    /**
     * 傳入的參數
     */
    final Map<String, Object> parameter;
    
    /**
     * 建構子
     * 
     * @param map
     *            傳入值
     */
    public CapParameter(Map<String, Object> map) {
        this.parameter = map;
    };

    /**
     * 建構子 將傳入的集合放進HasMap
     * 
     * @param collection
     *            傳入值
     */
    public CapParameter(Collection<Map<String, Object>> collection) {
        this.parameter = new HashMap<String, Object>();
        for (Map<String, Object> map : collection) {
            parameter.putAll(map);
        }
    }

    /**
     * 取得參數
     * 
     * @return
     */
    public Map<String, Object> getParameter() {
        Map<String, Object> tmp = new HashMap<String, Object>();
        tmp.putAll(parameter);
        return tmp;
    }// ;

    /**
     * 取得 Value <br>
     * 若無對應數值返回 defaultValue
     * 
     * @param <T>
     * @param key
     *            鍵值
     * @param defaultValue
     *            無對應建值返回之預設值
     * @return
     */
    @SuppressWarnings("unchecked")
    public <T> T getValue(String key, T defaultValue) {
        if (parameter.containsKey(key)) {
            Object obj = parameter.get(key);
            return (T) obj;
        }
        return defaultValue;
    }// ;

    /**
     * 取得 Value <br>
     * 若無對應數值返回 null
     * 
     * @param <T>
     * @param key
     * @return
     */
    @SuppressWarnings("unchecked")
    public <T> T getValue(String key) {
        if (parameter.containsKey(key)) {
            Object obj = parameter.get(key);
            return (T) obj;

        }
        return null;
    }// ;

    /**
     * 檢核是否含有該 Key 值
     * 
     * @param key
     * @return {@code parameter.containsKey(key);}
     */
    public boolean containsKey(String key) {
        return parameter.containsKey(key);
    }// ;
    
    public ParserContext getParserContext() {
    	return new TemplateParserContext("@(", ")");
    }// ;

}// ~
