/* 
 * L170M01DDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L170M01D;


/** 覆審項目檔 **/
public interface L170M01DDao extends IGenericDao<L170M01D> {

	L170M01D findByOid(String oid);

	List<L170M01D> findByMainId(String mainId);

	L170M01D findByUniqueKey(String mainId, String custId, String dupNo,
			String itemNo);

	List<L170M01D> findByIndex01(String mainId, String custId, String dupNo,
			String itemNo);

	L170M01D findByIndex02(String mainId, String custId, String dupNo,
			String itemNo);

	List<L170M01D> findByCustIdDupId(String custId,String DupNo);
}