/* 
 * C900M02K .java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 總授信業務授權額度檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C900M02K", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C900M02K extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/**
	 * 企/個金分類
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	@Size(max = 1)
	@Column(name = "DOCTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String docType;

	/**
	 * 授權層級
	 * <p/>
	 * 參考lms1205m01_caseLvl<br/>
	 * 1.董事會/常務董事會<br/>
	 * 6.總經理<br/>
	 * 7.副總經理<br/>
	 * 8.授信審查處處長<br/>
	 * B.區域授信管理分處處長<br/>
	 * 最大的董事會那筆應該不用建<br/>
	 * <br/>
	 * caseLvl、brNo、brClass擇一有值
	 */
	@Size(max = 2)
	@Column(name = "CASELVL", length = 2, columnDefinition = "VARCHAR(2)")
	private String caseLvl;

	/**
	 * 業務別
	 * <p/>
	 * 1: 授信<br/>
	 * 2: 應收帳款(無追)<br/>
	 * 3: 供應鏈融資<br/>
	 * 4: 出口押匯<br/>
	 * 5: 開發即期信用狀<br/>
	 * 6: 買入光票<br/>
	 * 7: D/A D/P<br/>
	 * <br/>
	 * 這個TABLE不會放授信相關的<br/>
	 * 授信放在C900M01K、C900M01K_KGD
	 */
	@Size(max = 2)
	@Column(name = "LOANKIND", length = 2, columnDefinition = "VARCHAR(2)")
	private String loanKind;

	/**
	 * 分行代號
	 * <p/>
	 * COM.BELSBRN.brNo<br/>
	 * 放特別指定的分行代號<br/>
	 * 007.國外部<br/>
	 * 201.金控總部分行<br/>
	 * 025.國際金融業務分行經理<br/>
	 * 149私銀處<br/>
	 * caseLvl、brNo、brClass擇一有值
	 */
	@Size(max = 3)
	@Column(name = "BRNO", length = 3, columnDefinition = "VARCHAR(3)")
	private String brNo;

	/**
	 * 分行等級
	 * <p/>
	 * COM.BELSBRN.brClass<br/>
	 * 若為Y則為簡易分行<br/>
	 * <br/>
	 * caseLvl、brNo、brClass擇一有值
	 */
	@Size(max = 1)
	@Column(name = "BRCLASS", length = 1, columnDefinition = "VARCHAR(1)")
	private String brClass;

	/**
	 * PD分組
	 * <p/>
	 * 可能有A~E五項，也有可能增減 <br/>
	 * 若此欄位為空，代表該筆不需要參照PD
	 */
	@Size(max = 1)
	@Column(name = "PDGROUP", length = 1, columnDefinition = "VARCHAR(1)")
	private String pdGroup;

	/**
	 * 細項1
	 * <p/>
	 * 1~12皆為存放細項金額用<br/>
	 * 看不同的各業務需要放什麼欄位各自表述<br/>
	 * 2: 應收帳款-買方無IF<br/>
	 * 3: 供應鏈融資-買方總額度<br/>
	 * 4: 出口押匯-符合出口<br/>
	 * 5: 開發即期信用狀-總額度<br/>
	 * 6: 買入光票-總額度<br/>
	 * 7: D/A D/P-總額度
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "DETAIL1", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal detail1;

	/**
	 * 細項2
	 * <p/>
	 * 2: 應收帳款-買方有IF<br/>
	 * 3: 供應鏈融資-賣方個別額度<br/>
	 * 4: 出口押匯-不符出口
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "DETAIL2", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal detail2;

	/**
	 * 細項3
	 * <p/>
	 * 2: 應收帳款-每戶授權總額<br/>
	 * 4: 出口押匯-瑕疵單據
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "DETAIL3", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal detail3;

	/** 細項4 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "DETAIL4", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal detail4;

	/** 細項5 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "DETAIL5", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal detail5;

	/** 細項6 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "DETAIL6", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal detail6;

	/** 細項7 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "DETAIL7", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal detail7;

	/** 細項8 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "DETAIL8", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal detail8;

	/** 細項9 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "DETAIL9", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal detail9;

	/** 細項10 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "DETAIL10", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal detail10;

	/** 細項11 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "DETAIL11", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal detail11;

	/** 細項12 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "DETAIL12", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal detail12;

	/** 版本 **/
	@Size(max = 13)
	@Column(name = "VERSION", length = 13, columnDefinition = "VARCHAR(13)")
	private String version;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}

	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 取得企/個金分類
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	public String getDocType() {
		return this.docType;
	}

	/**
	 * 設定企/個金分類
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 **/
	public void setDocType(String value) {
		this.docType = value;
	}

	/**
	 * 取得授權層級
	 * <p/>
	 * 參考lms1205m01_caseLvl<br/>
	 * 1.董事會/常務董事會<br/>
	 * 6.總經理<br/>
	 * 7.副總經理<br/>
	 * 8.授信審查處處長<br/>
	 * B.區域授信管理分處處長<br/>
	 * 最大的董事會那筆應該不用建<br/>
	 * <br/>
	 * caseLvl、brNo、brClass擇一有值
	 */
	public String getCaseLvl() {
		return this.caseLvl;
	}

	/**
	 * 設定授權層級
	 * <p/>
	 * 參考lms1205m01_caseLvl<br/>
	 * 1.董事會/常務董事會<br/>
	 * 6.總經理<br/>
	 * 7.副總經理<br/>
	 * 8.授信審查處處長<br/>
	 * B.區域授信管理分處處長<br/>
	 * 最大的董事會那筆應該不用建<br/>
	 * <br/>
	 * caseLvl、brNo、brClass擇一有值
	 **/
	public void setCaseLvl(String value) {
		this.caseLvl = value;
	}

	/**
	 * 取得業務別
	 * <p/>
	 * 1: 授信<br/>
	 * 2: 應收帳款(無追)<br/>
	 * 3: 供應鏈融資<br/>
	 * 4: 出口押匯<br/>
	 * 5: 開發即期信用狀<br/>
	 * 6: 買入光票<br/>
	 * 7: D/A D/P<br/>
	 * <br/>
	 * 這個TABLE不會放授信相關的<br/>
	 * 授信放在C900M01K、C900M01K_KGD
	 */
	public String getLoanKind() {
		return this.loanKind;
	}

	/**
	 * 設定業務別
	 * <p/>
	 * 1: 授信<br/>
	 * 2: 應收帳款(無追)<br/>
	 * 3: 供應鏈融資<br/>
	 * 4: 出口押匯<br/>
	 * 5: 開發即期信用狀<br/>
	 * 6: 買入光票<br/>
	 * 7: D/A D/P<br/>
	 * <br/>
	 * 這個TABLE不會放授信相關的<br/>
	 * 授信放在C900M01K、C900M01K_KGD
	 **/
	public void setLoanKind(String value) {
		this.loanKind = value;
	}

	/**
	 * 取得分行代號
	 * <p/>
	 * COM.BELSBRN.brNo<br/>
	 * 放特別指定的分行代號<br/>
	 * 007.國外部<br/>
	 * 201.金控總部分行<br/>
	 * 025.國際金融業務分行經理<br/>
	 * 149私銀處<br/>
	 * caseLvl、brNo、brClass擇一有值
	 */
	public String getBrNo() {
		return this.brNo;
	}

	/**
	 * 設定分行代號
	 * <p/>
	 * COM.BELSBRN.brNo<br/>
	 * 放特別指定的分行代號<br/>
	 * 007.國外部<br/>
	 * 201.金控總部分行<br/>
	 * 025.國際金融業務分行經理<br/>
	 * 149私銀處<br/>
	 * caseLvl、brNo、brClass擇一有值
	 **/
	public void setBrNo(String value) {
		this.brNo = value;
	}

	/**
	 * 取得分行等級
	 * <p/>
	 * COM.BELSBRN.brClass<br/>
	 * 若為Y則為簡易分行<br/>
	 * <br/>
	 * caseLvl、brNo、brClass擇一有值
	 */
	public String getBrClass() {
		return this.brClass;
	}

	/**
	 * 設定分行等級
	 * <p/>
	 * COM.BELSBRN.brClass<br/>
	 * 若為Y則為簡易分行<br/>
	 * <br/>
	 * caseLvl、brNo、brClass擇一有值
	 **/
	public void setBrClass(String value) {
		this.brClass = value;
	}

	/**
	 * 取得PD分組
	 * <p/>
	 * 可能有A~E五項，也有可能增減 <br/>
	 * 若此欄位為空，代表該筆不需要參照PD
	 */
	public String getPdGroup() {
		return this.pdGroup;
	}

	/**
	 * 設定PD分組
	 * <p/>
	 * 可能有A~E五項，也有可能增減 <br/>
	 * 若此欄位為空，代表該筆不需要參照PD
	 **/
	public void setPdGroup(String value) {
		this.pdGroup = value;
	}

	/**
	 * 取得細項1
	 * <p/>
	 * 1~12皆為存放細項金額用<br/>
	 * 看不同的各業務需要放什麼欄位各自表述<br/>
	 * 2: 應收帳款-買方無IF<br/>
	 * 3: 供應鏈融資-買方總額度<br/>
	 * 4: 出口押匯-符合出口<br/>
	 * 5: 開發即期信用狀-總額度<br/>
	 * 6: 買入光票-總額度<br/>
	 * 7: D/A D/P-總額度
	 */
	public BigDecimal getDetail1() {
		return this.detail1;
	}

	/**
	 * 設定細項1
	 * <p/>
	 * 1~12皆為存放細項金額用<br/>
	 * 看不同的各業務需要放什麼欄位各自表述<br/>
	 * 2: 應收帳款-買方無IF<br/>
	 * 3: 供應鏈融資-買方總額度<br/>
	 * 4: 出口押匯-符合出口<br/>
	 * 5: 開發即期信用狀-總額度<br/>
	 * 6: 買入光票-總額度<br/>
	 * 7: D/A D/P-總額度
	 **/
	public void setDetail1(BigDecimal value) {
		this.detail1 = value;
	}

	/**
	 * 取得細項2
	 * <p/>
	 * 2: 應收帳款-買方有IF<br/>
	 * 3: 供應鏈融資-賣方個別額度<br/>
	 * 4: 出口押匯-不符出口
	 */
	public BigDecimal getDetail2() {
		return this.detail2;
	}

	/**
	 * 設定細項2
	 * <p/>
	 * 2: 應收帳款-買方有IF<br/>
	 * 3: 供應鏈融資-賣方個別額度<br/>
	 * 4: 出口押匯-不符出口
	 **/
	public void setDetail2(BigDecimal value) {
		this.detail2 = value;
	}

	/**
	 * 取得細項3
	 * <p/>
	 * 2: 應收帳款-每戶授權總額<br/>
	 * 4: 出口押匯-瑕疵單據
	 */
	public BigDecimal getDetail3() {
		return this.detail3;
	}

	/**
	 * 設定細項3
	 * <p/>
	 * 2: 應收帳款-每戶授權總額<br/>
	 * 4: 出口押匯-瑕疵單據
	 **/
	public void setDetail3(BigDecimal value) {
		this.detail3 = value;
	}

	/** 取得細項4 **/
	public BigDecimal getDetail4() {
		return this.detail4;
	}

	/** 設定細項4 **/
	public void setDetail4(BigDecimal value) {
		this.detail4 = value;
	}

	/** 取得細項5 **/
	public BigDecimal getDetail5() {
		return this.detail5;
	}

	/** 設定細項5 **/
	public void setDetail5(BigDecimal value) {
		this.detail5 = value;
	}

	/** 取得細項6 **/
	public BigDecimal getDetail6() {
		return this.detail6;
	}

	/** 設定細項6 **/
	public void setDetail6(BigDecimal value) {
		this.detail6 = value;
	}

	/** 取得細項7 **/
	public BigDecimal getDetail7() {
		return this.detail7;
	}

	/** 設定細項7 **/
	public void setDetail7(BigDecimal value) {
		this.detail7 = value;
	}

	/** 取得細項8 **/
	public BigDecimal getDetail8() {
		return this.detail8;
	}

	/** 設定細項8 **/
	public void setDetail8(BigDecimal value) {
		this.detail8 = value;
	}

	/** 取得細項9 **/
	public BigDecimal getDetail9() {
		return this.detail9;
	}

	/** 設定細項9 **/
	public void setDetail9(BigDecimal value) {
		this.detail9 = value;
	}

	/** 取得細項10 **/
	public BigDecimal getDetail10() {
		return this.detail10;
	}

	/** 設定細項10 **/
	public void setDetail10(BigDecimal value) {
		this.detail10 = value;
	}

	/** 取得細項11 **/
	public BigDecimal getDetail11() {
		return this.detail11;
	}

	/** 設定細項11 **/
	public void setDetail11(BigDecimal value) {
		this.detail11 = value;
	}

	/** 取得細項12 **/
	public BigDecimal getDetail12() {
		return this.detail12;
	}

	/** 設定細項12 **/
	public void setDetail12(BigDecimal value) {
		this.detail12 = value;
	}

	/** 取得版本 **/
	public String getVersion() {
		return this.version;
	}

	/** 設定版本 **/
	public void setVersion(String value) {
		this.version = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
