/* 
 * C900M02KDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C900M02KDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900M02K;

/** 總授信業務授權額度檔 **/
@Repository
public class C900M02KDaoImpl extends LMSJpaDao<C900M02K, String> implements
		C900M02KDao {

	@Override
	public C900M02K findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C900M02K> findByDocTypeAndVersionAndBrNo(String docType,
			String loanKind, String version, String brNo) {
		ISearch search = createSearchTemplete();
		List<C900M02K> list = null;

		search.addSearchModeParameters(SearchMode.EQUALS, "docType", docType);
		search.addSearchModeParameters(SearchMode.EQUALS, "loanKind", loanKind);
		search.addSearchModeParameters(SearchMode.EQUALS, "version", version);
		search.addSearchModeParameters(SearchMode.EQUALS, "brNo", brNo);
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public C900M02K findByDocTypeAndVersionAndBrClassNoPd(String docType,
			String loanKind, String version, String brClass) {
		ISearch search = createSearchTemplete();

		search.addSearchModeParameters(SearchMode.EQUALS, "docType", docType);
		search.addSearchModeParameters(SearchMode.EQUALS, "loanKind", loanKind);
		search.addSearchModeParameters(SearchMode.EQUALS, "version", version);
		search.addSearchModeParameters(SearchMode.EQUALS, "brClass", brClass);
		search.addSearchModeParameters(SearchMode.EQUALS, "pdGroup", "");// 抓pdGroup為空白的資料
		search.setMaxResults(Integer.MAX_VALUE);

		return findUniqueOrNone(search);
	}

	@Override
	public List<C900M02K> findByDocTypeAndVersionAndBrClass(String docType,
			String loanKind, String version, String brClass) {
		ISearch search = createSearchTemplete();
		List<C900M02K> list = null;

		search.addSearchModeParameters(SearchMode.EQUALS, "docType", docType);
		search.addSearchModeParameters(SearchMode.EQUALS, "loanKind", loanKind);
		search.addSearchModeParameters(SearchMode.EQUALS, "version", version);
		search.addSearchModeParameters(SearchMode.EQUALS, "brClass", brClass);
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C900M02K> findByDocTypeAndVersionHaveCaseLvl(String docType,
			String loanKind, String version) {
		ISearch search = createSearchTemplete();
		List<C900M02K> list = null;

		search.addSearchModeParameters(SearchMode.EQUALS, "docType", docType);
		search.addSearchModeParameters(SearchMode.EQUALS, "loanKind", loanKind);
		search.addSearchModeParameters(SearchMode.EQUALS, "version", version);
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "caseLvl", "");// 只撈授權外有授權層級的資料
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}
}