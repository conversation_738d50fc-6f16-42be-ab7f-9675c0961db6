---------------------------------------------------------
-- LMS.C900M01D 會計科子目名稱檔
---------------------------------------------------------
---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.C900M01D;
CREATE TABLE LMS.C900M01D (
	OID           CHAR(32)     ,
	SUBJCODE      VARCHAR(8)    not null,
	SUBJCODE2     VARCHAR(4)   ,
	SUBJNM        VARCHAR(128) ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>    TIMESTAMP    ,

	constraint P_C900M01D PRIMARY KEY(SUBJCODE)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC900M01D01;
--CREATE UNIQUE INDEX LMS.XC900M01D01 ON LMS.C900M01D   (SUBJCODE);
--DROP INDEX LMS.XC900M01D02;
CREATE INDEX LMS.XC900M01D02 ON LMS.C900M01D   (SUBJCODE2);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C900M01D IS '會計科子目名稱檔';
COMMENT ON LMS.C900M01D (
	OID           IS 'oid', 
	SUBJCODE      IS '會計科子細目', 
	SUBJCODE2     IS '授信科目', 
	SUBJNM        IS '會計科子名稱', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
