package com.mega.eloan.lms.obsdb.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbELF006Service;

@Service
public class ObsdbELF006ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF006Service {

	@Override
	public Map<String, Object> getNetValue(String BRNID) {

		return this.getJdbc(BRNID).queryForMap("ELF006.selectElf006NetValue",
				new String[] {});

	}

	@Override
	public Map<String, Object> getByBrnTypeLimitType(String BRNID,
			String brnType, String limitType) {

		return this.getJdbc(BRNID).queryForMap(
				"ELF006.selectElf006ByBrnTypeLimitType",
				new String[] { brnType, limitType });

	}

}
