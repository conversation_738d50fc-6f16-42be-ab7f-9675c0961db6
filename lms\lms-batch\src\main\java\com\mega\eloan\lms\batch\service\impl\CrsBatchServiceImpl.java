package com.mega.eloan.lms.batch.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Scanner;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.gwclient.DWUCB1FTPClient;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.RPAService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsNoAgreeQueryEjVerOnlineCntrNo;
import com.mega.eloan.lms.base.common.ClsOnlineCntrNo;
import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.CrsVO;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.MISRows;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ContractDocService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.cls.report.CLS3401R07RptService;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.cls.service.CLS3401Service;
import com.mega.eloan.lms.crs.common.CrsRuleVO;
import com.mega.eloan.lms.crs.service.LMS2400Service;
import com.mega.eloan.lms.crs.service.LMS2401Service;
import com.mega.eloan.lms.crs.service.LMS2405Service;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eai.service.EAIService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.LmsCustdataService;
import com.mega.eloan.lms.enums.NcodeEnum;
import com.mega.eloan.lms.mfaloan.bean.ELF457;
import com.mega.eloan.lms.mfaloan.bean.ELF459;
import com.mega.eloan.lms.mfaloan.bean.ELF490B;
import com.mega.eloan.lms.mfaloan.bean.ELF491;
import com.mega.eloan.lms.mfaloan.bean.ELF491C;
import com.mega.eloan.lms.mfaloan.bean.ELF500;
import com.mega.eloan.lms.mfaloan.bean.ELF675;
import com.mega.eloan.lms.mfaloan.bean.MISLN30;
import com.mega.eloan.lms.mfaloan.bean.OTS_CRDLN031;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisDailyctlService;
import com.mega.eloan.lms.mfaloan.service.MisELF457Service;
import com.mega.eloan.lms.mfaloan.service.MisELF487Service;
import com.mega.eloan.lms.mfaloan.service.MisELF490BService;
import com.mega.eloan.lms.mfaloan.service.MisELF490Service;
import com.mega.eloan.lms.mfaloan.service.MisELF491CService;
import com.mega.eloan.lms.mfaloan.service.MisELF491Service;
import com.mega.eloan.lms.mfaloan.service.MisELF492Service;
import com.mega.eloan.lms.mfaloan.service.MisELF500Service;
import com.mega.eloan.lms.mfaloan.service.MisELF591Service;
import com.mega.eloan.lms.mfaloan.service.MisElf902Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF030Service;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01B;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01E;
import com.mega.eloan.lms.model.C160S01D;
import com.mega.eloan.lms.model.C241M01Z;
import com.mega.eloan.lms.model.C242M01A;
import com.mega.eloan.lms.model.C243M01A;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.C900M01G;
import com.mega.eloan.lms.model.C900M03A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01Y;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L180R19H;
import com.mega.eloan.lms.model.LMSBATCH;
import com.mega.eloan.lms.rpt.service.LMS9511Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;


/**
 * <pre>
 * 國內消金覆審批次
 * </pre>
 * 
 * url 可參考 SLMS-00002 文件刪除之批次整檔排程  的寫法
 * 在 PROD 的 LOG，輸出在   ELOANAP1目錄 : > lms > BRANCHES > BAT > crsBatchServiceImpl 
 */
@Service("crsBatchServiceImpl")
public class CrsBatchServiceImpl extends AbstractCapService implements
		WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource
	SysParameterService sysParameterService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	ContractDocService contractDocService;
	
	@Resource
	RPAService rpaService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	RetrialService retrialService;
	
	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	CLS3401Service cls3401Service;
	
	@Resource
	CLS3401R07RptService cls3401R07RptService;
	
	@Resource
	LMS2400Service lms2400Service;
	
	@Resource
	LMS2401Service lms2401Service;
	
	@Resource
	LMS2405Service lms2405Service;

	@Resource
	LMS9511Service lms9511Service;
	
	@Resource
	CodeTypeService codetypeService;
	
	@Resource
	CLS1220Service cls1220Service;
	
	@Resource
	MisStoredProcService misStoredProcService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	MisCustdataService misCustdataService;
	
	@Resource
	MisLNF030Service misLNF030Service;

	@Resource
	MisELF457Service misELF457Service;
	
	@Resource
	MisELF487Service misELF487Service;
	
	@Resource
	MisELF490Service misELF490Service;
	
	@Resource
	MisELF490BService misELF490BService;
	
	@Resource
	MisELF491Service misELF491Service;

	@Resource
	MisELF491CService misELF491CService;
	
	@Resource
	MisELF492Service misELF492Service;
	
	@Resource
	MisELF500Service misELF500Service;
	
	@Resource
	MisELF591Service misELF591Service;
	
	@Resource
	MisElf902Service misElf902Service;
	
	@Resource
	MisDailyctlService misDailyctlService;
	
	@Resource
	DwdbBASEService dwdbBASEService;
	
	@Resource
	ICustomerService iCustomerService; 
	
	@Resource
	LmsCustdataService lmsCustdataService;
	
	@Resource
	DocFileService docFileService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	DWUCB1FTPClient dwUCB1FTPClient;
		
	@Resource
	EAIService eaiService;
	
	@Override
	public JSONObject execute(JSONObject json) {
		boolean isSuccess = false;
		JSONObject result = null;
		JSONObject rq = json.getJSONObject("request");
			
		String act = rq.optString("act");
		String msg = "";
		
		try {
			if(Util.equals("nonOversea_produceC240M01A", act)){
				String userId = rq.optString("userId");
				String unitNo = rq.optString("unitNo");
				String unitType = rq.optString("unitType");
				String par_arr = rq.optString("par_arr");
				String[] dataSplit = Util.trim(par_arr).split("\\|");
				List<String> existBrSkipList = new ArrayList<String>();
				_act_nonOversea_produceC240M01A(userId, unitNo, unitType, dataSplit, existBrSkipList );
				if(CollectionUtils.isNotEmpty(existBrSkipList)){
					logger.trace("skipBr:["+StringUtils.join(existBrSkipList, "、")+"]");
				}
			}else if(Util.equals("elf490_to_elf491", act)){
				String limit_branch = Util.trim(rq.optString("limit_branch"));
				_act_elf490_to_elf491(limit_branch);
			}else if(Util.equals("elf490_to_elf491_force", act)){
				
				String limit_branch = Util.trim(rq.optString("limit_branch"));
				if(Util.isEmpty(limit_branch)){
					throw new CapException("limit_branch is empty", getClass());
				}
				//---
				//刪除 C240M01Z
				lms2400Service.deleteC240M01ZByPk(CrsUtil.get_sysMonth_1st(), limit_branch);
				//---
				_act_elf490_to_elf491(limit_branch);
			}else if(Util.equals("c241m01a_dbu_approveToEnd", act)){
				//在 JS 端指定 900 - BATCH
				String userId = rq.optString("userId");
				String unitNo = rq.optString("unitNo");
				_act_c241m01a_dbu_approveToEnd(userId, unitNo);
			}else if(Util.equals("procC242M01A", act)){
				boolean setExeTimeAndRun = false;
				C242M01A c242m01a = retrialService.findC242M01A_inProcess();
				
				int normalMin = Util.parseInt(rq.optString("normalMin"));
				if(normalMin>0){
					//ok
				}else{
					normalMin = 80;//default value
				}

				//已存在處理中
				if(c242m01a!=null){
					Timestamp nowTS = CapDate.getCurrentTimestamp();
					long difInMin=(nowTS.getTime()-c242m01a.getExeTime().getTime())/(1000*60);
					if(difInMin > normalMin){
						//restart
						//也要重設 exeTime, 避免 server 重啟後的第2個 batch 又start一次
						//produce() 略過同一 endDate 且仍在 編製中 的分行
						setExeTimeAndRun = true;
					}else{
						//仍在處理中
					}
				}else{
					c242m01a = retrialService.findC242M01A_notProcess();
					if(c242m01a != null){
						setExeTimeAndRun = true;
					}
				}
				
				if(setExeTimeAndRun){
					List<String> existBrSkipList = new ArrayList<String>();
					//在 procStart 裡，重設 exeTime					
					retrialService.saveC242M01A_procStart(c242m01a);
					//---
					boolean procResult = false;
					try{
						_act_nonOversea_produceC240M01A(c242m01a.getCreator(), c242m01a.getOwnBrId()
								, c242m01a.getUnitType(), Util.trim(c242m01a.getExeParam()).split("\\|")
								, existBrSkipList );
						//---
						procResult = true;	
					}catch(Exception e){
						logger.error(StrUtils.getStackTrace(e));
					}
					retrialService.saveC242M01A_procEnd(c242m01a, procResult);
					
					if(CollectionUtils.isNotEmpty(existBrSkipList)){
						logger.trace("skipBr:["+StringUtils.join(existBrSkipList, "、")+"]");
					}
				}
			}else if(Util.equals("update491_abnormal", act)){
				lms2401Service.update491_abnormal();
			}else if(Util.equals("update491_J_109_0372_1st", act)){
				lms2401Service.update491_J_109_0372_1st();
			}else if(Util.equals("update491_J_109_0372_byBatchNo", act)){				
				String steps = Util.trim(rq.optString("steps"));
				String batchNo = Util.trim(rq.optString("batchNo"));
				String postDocNoByBatchNo = Util.trim(rq.optString("postDocNoByBatchNo"));
				Date postDateByBatchNo = CapDate.parseDate(Util.trim(rq.optString("postDateByBatchNo")));
				Date firstCrDateByBatchNo = CapDate.parseDate(Util.trim(rq.optString("firstCrDateByBatchNo")));
				if(Util.isEmpty(steps) 
						|| Util.isEmpty(batchNo)
						|| Util.isEmpty(postDocNoByBatchNo)
						|| CrsUtil.isNull_or_ZeroDate(postDateByBatchNo)
						|| CrsUtil.isNull_or_ZeroDate(firstCrDateByBatchNo) ){
					throw new CapException("lost param", getClass());
				}
				lms2401Service.update491_J_109_0372_byBatchNo(steps, batchNo, postDocNoByBatchNo, postDateByBatchNo, firstCrDateByBatchNo);
			}else if(Util.equals("mail_processingOverDays", act)){
				int overDays = Util.parseInt(rq.optString("overDays"));
				if(overDays>0){
					//ok
				}else{
					overDays = 14;//default value
				}
				boolean skipBrT1 = "Y".equals(Util.trim(rq.optString("skipBrT1")));
				boolean rMail = lms2400Service.mail_processingOverDays(overDays, skipBrT1);
				if(rMail){
					
				}else{
					throw new CapException("mail_processingOverDays fail", getClass());
				}

				clsService.delOldC900S02B();
			}else if(Util.equals(CrsUtil.BATCH_FUNC_NAME_CHKR1R2R4_GEN, act)){ //key="chkR1R2R4_gen"
				/*
				  	+ select reason, count(*) from lms.c241m01z where reason in ('U', 'P') group by reason
				  	
				 	+ http://127.0.0.1:9081/lms-web/app/scheduler?input={'serviceId':'crsBatchServiceImpl','request':{act:'chkR1R2R4_gen', 'brNo':'229', 'custId':'', 'cmpLrDate':'2020-12-31'}}
				 					 	
				 	$.ajax({handler: "lms2411m01formhandler", action: "callBatch", timeout: 3000,
				 			data: {'act':'chkR1R2R4_gen','brNo':'229','custId':'', 'cmpLrDate':'2020-12-31', 'jq_timeout': 3},
				 			success: function(json_callBatch){}
                    });
				 */
				String brNo = rq.optString("brNo");
				String custId = rq.optString("custId");
				String cmpLrDate = rq.optString("cmpLrDate");
				Date sysDate = CapDate.parseDate(CapDate.getCurrentDate(CapDate.DEFAULT_DATE_FORMAT));
				String elf491_lrDate = TWNDate.toAD(CapDate.addMonth(sysDate, -1));
				if(Util.isNotEmpty(cmpLrDate)){
					if(CapDate.parseDate(cmpLrDate)!=null){
						elf491_lrDate = cmpLrDate;
					}
				}
				if(Util.isNotEmpty(custId) || Util.isNotEmpty(brNo)){
					// 用 where elf491_branch like ? and elf491_custid like ? 去查找
					// 輸入參數，應避免 brNo 與 custId 同時空白
					lms2401Service.chkR1R2R4(brNo, custId, elf491_lrDate);	
				}else{
					//一次處理太多筆,JPA丟出 java.lang.StackOverflowError
					//改成依分行
					for(String branchId : retrialService.getBranch("900").keySet()){
						lms2401Service.chkR1R2R4(branchId, custId, elf491_lrDate);	
					}	
				}				
				
			}else if(Util.equals(CrsUtil.BATCH_FUNC_NAME_CHKR1R2R4_PROCESS, act)){ //key="chkR1R2R4_process"
				/*
				 	SLMS-00021 消金覆審判斷R1之金額
				 	+ http://127.0.0.1:9081/lms-web/app/scheduler?input={'serviceId':'crsBatchServiceImpl','request':{act:'chkR1R2R4_process', 'cnt1':'4000', 'cnt2':'4000'}}
				 	+ 本機測試 http://127.0.0.1:9081/lms-web/app/scheduler?input={'serviceId':'crsBatchServiceImpl','request':{act:'chkR1R2R4_process', 'cnt1':'4000', 'cnt2':'4000', 'out_log':'true'}}
				 */
				Timestamp nowTS = CapDate.getCurrentTimestamp();
				String strNowTW = Util.trim(CapDate.parseToString(nowTS));
				int batch_cnt = 0;
				int atHour = Util.parseInt(StringUtils.substring(strNowTW, 11,13));				
				if(atHour<9 || atHour>18){
					//寬鬆時間					
					batch_cnt = Util.parseInt(rq.optString("cnt2"));
					if(batch_cnt>0){
						//ok
					}else{
						batch_cnt = 40;//default value
					}
				}else{
					//峰時				}
					batch_cnt = Util.parseInt(rq.optString("cnt1"));
					if(batch_cnt>0){
						//ok
					}else{
						batch_cnt = 10;//default value
					}
				}
				if(batch_cnt==0){
					batch_cnt = 10;
				}
				
				int allowMin = Util.parseInt(rq.optString("allowMin"));
				if(allowMin>0){
					//ok
				}else{
					allowMin = 60;//default value
				}

				boolean out_log = rq.optBoolean("out_log", false);
				List<C241M01Z> proc_list = lms2400Service.findProc(batch_cnt);
				if(CollectionUtils.isNotEmpty(proc_list)){
					//判斷是否 restart
					String strExeTime = Util.trim(proc_list.get(0).getReasonDesc());
					Timestamp existExeTime = CapDate.convertStringToTimestamp(strExeTime);
					long difInMin =(nowTS.getTime()- existExeTime.getTime())/(1000*60);
					if(difInMin > allowMin){
						//restart
						//也要重設 exeTime, 避免 server 重啟後的第2個 batch 又start一次
						for(C241M01Z c241m01z : proc_list){
							if(Util.notEquals(strExeTime, c241m01z.getReasonDesc())){
								continue;
							}
							c241m01z.setReason("U");
							c241m01z.setReasonDesc(strNowTW);
							//---
							retrialService.save(c241m01z);
						}
					}else{
						//仍在處理中(等待)
					}
				}else{
					List<C241M01Z> c241m01z_UnProc_list = lms2400Service.findUnProc(batch_cnt);
					if(CollectionUtils.isNotEmpty(c241m01z_UnProc_list)){
						for(C241M01Z c241m01z : c241m01z_UnProc_list){
							c241m01z.setReason("P");
							c241m01z.setReasonDesc(strNowTW);
							//---
							retrialService.save(c241m01z);
						}
						
						for(C241M01Z c241m01z : c241m01z_UnProc_list){
							CrsRuleVO crsRuleVO = new CrsRuleVO(nowTS,
									c241m01z.getBranch(), c241m01z.getCustId(),
									c241m01z.getDupNo());
							String _rule_no_new = "";
							String _rule_no = "";
							if (Util.equals(CrsUtil.ELF491_NEWFLAG_Y, c241m01z.getBf_newFlag())) {
								_rule_no_new = c241m01z.getBf_remomo();
							} else {
								_rule_no = c241m01z.getBf_remomo();
							}
							String brNo = crsRuleVO.getBrNo();
							String custId = crsRuleVO.getCustId();
							String dupNo = crsRuleVO.getDupNo();
							String idDup = LMSUtil.getCustKey_len10custId(custId, dupNo);							

							String out_log_basic = "chkR1R2R4_[brNo="+brNo+", idDup="+idDup+"]";
							
							lms2401Service.validate_R1R2R4(crsRuleVO, _rule_no_new, _rule_no);
								
							if (crsRuleVO.rule_decided()==false) {
								ELF491 elf491 = misELF491Service.findByPk(crsRuleVO.getBrNo(), crsRuleVO.getCustId(), crsRuleVO.getDupNo());
								/*
								 以額度序號 071110200062(產品02)與 額度序號071110800069(產品68)為例
								 (1)在 2019-07 建立額度序號071110800069(產品68), 但原本的額度序號 071110200062(產品02) ===> 不合理
								 (2)經過1個月，在 2019-08-26 才把額度序號 071110200062(產品02)【未銷戶】
								 (3)在 ELF490 裡，於 2019-08 針對此客戶，未寫入資料
								   	年月	      	舊案類別		新案類別
								 	010806   	2 ; 4    
  								 	010807      			1 
	
								 */
								
								boolean still_has_need_retrial_data = false;
								if(true){ 
									//~~~~~~~~~
									CrsVO crsVO = new CrsVO();
									retrialService.fetch_LNF020_LNF030040(crsVO, custId, dupNo);
									//判斷有哪些 LNF020 要排除
									List<Map<String, Object>> _filter_crsVO_getLNF020 = retrialService.filter_crsVO_getLNF020(
											crsVO.getLNF020(custId, dupNo), brNo);
									
									int lnap_1_cnt = 0;
									int lnap_2_cnt = 0;
									int lnap_3_cnt = 0;
									int lnap_4_cnt = 0;
									int lnap_5_cnt = 0;
									int lnap_6_cnt = 0;
									int lnap_tot_cnt = 0;
									
									if(CollectionUtils.isNotEmpty(_filter_crsVO_getLNF020)){
																				
										for(Map<String, Object> dataMap020 : _filter_crsVO_getLNF020){
											String cntrNo = Util.trim(dataMap020.get("LNF020_CONTRACT"));

											for (Map<String, Object> dataMap030_040 : crsVO.getLNF030_040_withLNF020_LN_BR_NO_NoCharcCode30(cntrNo, brNo)) {
												String lnf030_loan_no = Util.trim(dataMap030_040.get("LNF030_LOAN_NO"));
												
												lnap_tot_cnt++;
												String lnap = CrsUtil.getSubjCodeFromLNF030_LOAN_NO(lnf030_loan_no);
												
												if(lnap.startsWith("1")){
													++lnap_1_cnt;
												}else if(lnap.startsWith("2")){
													++lnap_2_cnt;
												}else if(lnap.startsWith("3")){
													++lnap_3_cnt;
												}else if(lnap.startsWith("4")){
													++lnap_4_cnt;
												}else if(lnap.startsWith("5")){
													++lnap_5_cnt;
												}else if(lnap.startsWith("6")){
													++lnap_6_cnt;
												}
											}
										}
										
										if(lnap_tot_cnt==0){
											//有額度，沒帳號的情況
											still_has_need_retrial_data = true;
										}					
										
										if(lnap_tot_cnt>0 && CrsUtil.isOnlyR2R4_noOthers(_rule_no) && lnap_2_cnt==0){
											still_has_need_retrial_data = true;
										}
									}
								}
								
								if(still_has_need_retrial_data){
									//仍符合. 不回寫 ELF491
									lms2400Service.deleteC241M01Z(c241m01z);
									if(out_log){
										logger.debug(out_log_basic+", rule_decided=false, [still_has_need_retrial_data="+still_has_need_retrial_data+"] => not update_elf491");
									}
								}else{
									if(elf491==null){
										lms2400Service.deleteC241M01Z(c241m01z);
										if(out_log){
											logger.debug(out_log_basic+", rule_decided=false, [elf491=null] => not update_elf491");
										}
									}else{
										boolean chg = false;
																		
										if(true){
											if(Util.notEquals(Util.trim(TWNDate.toAD(c241m01z.getBf_crDate())), Util.trim(TWNDate.toAD(elf491.getElf491_crdate())))){
												chg = true;
											}
											if(Util.notEquals(c241m01z.getBf_remomo(), elf491.getElf491_remomo())){
												chg = true;
											}
											if(Util.notEquals(c241m01z.getBf_reportKind(), elf491.getElf491_reportkind())){
												chg = true;
											}
											if(Util.notEquals(c241m01z.getBf_newFlag(), elf491.getElf491_newflag())){
												chg = true;
											}
										}	
										if(chg){
											//ELF491 有改過,退回狀態 待檢核R1
											lms2401Service.chkR1R2R4_copy_to_c241m01z(elf491, c241m01z);
											retrialService.save(c241m01z);
										}else{
											//ELF491 未改過,且不符合 R1, R2, R4
											lms2400Service.saveUnmatchR1R2R4(elf491, c241m01z, crsRuleVO);										
										}	
										if(out_log){
											logger.debug(out_log_basic+", rule_decided=false, [chg_ELF491="+chg+"]");
										}							
									}
								}
							}else{
								//仍符合. 不回寫 ELF491
								lms2400Service.deleteC241M01Z(c241m01z);
								if(out_log){
									logger.debug(out_log_basic+", rule_decided=true, reason="+crsRuleVO.getC241M01Z_reasonDesc());
								}
							}
						} //end for-loop	
					}		
				}
			}else if(Util.equals("updateDW_RKCREDIT", act)){
				String mowType = "M";
				if(true){
					int mower1=1;
					// 1.0 及 1.1
					for(int mower2=0;mower2<=1;mower2++){
						dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 1, new BigDecimal("0.00050"),new BigDecimal("0.00020"));
						dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 2, new BigDecimal("0.00120"),new BigDecimal("0.00040"));
						dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 3, new BigDecimal("0.00230"),new BigDecimal("0.00080"));
						dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 4, new BigDecimal("0.00390"),new BigDecimal("0.00130"));					
						dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 5, new BigDecimal("0.00750"),new BigDecimal("0.00250"));
						dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 6, new BigDecimal("0.01360"),new BigDecimal("0.00460"));
						dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 7, new BigDecimal("0.02010"),new BigDecimal("0.00670"));
						dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 8, new BigDecimal("0.02970"),new BigDecimal("0.01000"));
						dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 9, new BigDecimal("0.05590"),new BigDecimal("0.01900"));
						dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 10,new BigDecimal("0.23980"),new BigDecimal("0.08730"));	
					}	
				}
					
				if(true){
					int mower1=1;
					int mower2=2;
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 1, new BigDecimal("0.00050"),new BigDecimal("0.00020"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 2, new BigDecimal("0.00110"),new BigDecimal("0.00040"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 3, new BigDecimal("0.00220"),new BigDecimal("0.00070"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 4, new BigDecimal("0.00410"),new BigDecimal("0.00140"));					
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 5, new BigDecimal("0.00810"),new BigDecimal("0.00270"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 6, new BigDecimal("0.01780"),new BigDecimal("0.00600"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 7, new BigDecimal("0.03610"),new BigDecimal("0.01220"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 8, new BigDecimal("0.07620"),new BigDecimal("0.02610"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 9, new BigDecimal("0.13310"),new BigDecimal("0.04650"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 10,new BigDecimal("0.32340"),new BigDecimal("0.12210"));	
				}
				if(true){
					int mower1=1;
					int mower2=3;
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 1, new BigDecimal("0.00090"),new BigDecimal("0.00030"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 2, new BigDecimal("0.00150"),new BigDecimal("0.00050"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 3, new BigDecimal("0.00260"),new BigDecimal("0.00090"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 4, new BigDecimal("0.00460"),new BigDecimal("0.00150"));					
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 5, new BigDecimal("0.00870"),new BigDecimal("0.00290"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 6, new BigDecimal("0.01800"),new BigDecimal("0.00600"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 7, new BigDecimal("0.03660"),new BigDecimal("0.01240"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 8, new BigDecimal("0.07610"),new BigDecimal("0.02600"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 9, new BigDecimal("0.13630"),new BigDecimal("0.04770"));
					dwdbBASEService.updateDR__DR_1YR(mowType, mower1, mower2, 10,new BigDecimal("0.27290"),new BigDecimal("0.10080"));
				}
			}else if(Util.equals("updateDW_delL140M01A", act)){
				String BR_CD = rq.optString("BR_CD");
				String NOTEID = rq.optString("NOTEID");
				String CUSTID = rq.optString("CUSTID");
				String DUPNO = rq.optString("DUPNO");
				String MOWTYPE = rq.optString("MOWTYPE");
				String MOWVER1 = rq.optString("MOWVER1");
				String MOWVER2 = rq.optString("MOWVER2");
				String JCIC_DATE = rq.optString("JCIC_DATE");
				String ACCT_KEY = rq.optString("ACCT_KEY");
				String DELETE_REASON = rq.optString("DELETE_REASON");
				String REJECT_OTHEREASON_TEXT = rq.optString("REJECT_OTHEREASON_TEXT");
				dwdbBASEService.delL140M01A(BR_CD, NOTEID, CUSTID, DUPNO
						, MOWTYPE, MOWVER1, MOWVER2, JCIC_DATE, ACCT_KEY
						, DELETE_REASON, REJECT_OTHEREASON_TEXT);
			}else if(Util.equals("updateDW_delL140M01A_OVS", act)){
				String BR_CD = rq.optString("BR_CD");
				String NOTEID = rq.optString("NOTEID");
				String RATING_DATE = rq.optString("RATING_DATE");
				String RATING_ID = rq.optString("RATING_ID");				
				String CUSTID = rq.optString("CUSTID");
				String DUPNO = rq.optString("DUPNO");
				String CUST_KEY = rq.optString("CUST_KEY");
				String LOAN_CODE = rq.optString("LOAN_CODE");
				String MOWTYPE = rq.optString("MOWTYPE");
				String MOWVER1 = rq.optString("MOWVER1");
				String MOWVER2 = rq.optString("MOWVER2");				
				
				dwdbBASEService.delL140M01A_OVS(BR_CD, NOTEID, RATING_DATE, RATING_ID, 
						CUSTID, DUPNO, CUST_KEY, LOAN_CODE, MOWTYPE, MOWVER1, MOWVER2);
			}else if(Util.equals("update_risk_rating", act)){
				int[] type = {java.sql.Types.INTEGER, java.sql.Types.INTEGER};
			
				List<Object[]> lst = new ArrayList<Object[]>();
				lst.add(new Object[]{75, 100});
				lst.add(new Object[]{35, 45});
				
				/*
				 	若寫>=104, 會連107年(2018)也被納入
				 */
				misdbBASEService.update(
						new Object[] { "MIS.ELF501", "elf501_risk_rating=? "
								, "elf501_risk_rating=? and substr(elf501_cntrNo,5,3) in ('104','105','106') " }
						, type, lst);
			}else if(Util.equals("GEN_J_106_0266_RPT", act)){
				gen_J_106_0266_RPT();
			}else if(Util.equals("DEL_OLD_C900S02B", act)){
				clsService.delOldC900S02B();
			}else if(Util.equals("syncC900M01GwithLNF", act)){ // SLMS-00052
				String status = Util.trim(rq.optString("status"));
				String grpCntrNo = Util.trim(rq.optString("grpCntrNo"));
				syncC900M01GwithLNF(status, grpCntrNo);
				release_dc_C900M01G();
			}else if(Util.equals("uploadELF457", act)){
				int daysCnt = Util.parseInt(rq.optString("daysCnt"));
				String caseBrId = Util.trim(rq.optString("caseBrId"));
				//J-109-0304_10702_B1001 Web e-Loan 上傳elf457進件來源參數
				String startDate = Util.trim(rq.optString("startDate"));
				String endDate = Util.trim(rq.optString("endDate"));
				int commitCount = (rq.optString("commitCount")!="") ? Util.parseInt(rq.optString("commitCount")) : 1000;
				uploadELF457(daysCnt, caseBrId,startDate,endDate,commitCount);
			}else if(Util.equals("uploadOTS_CRDLN031", act)){ 	// SLMS-00054
				String period = Util.trim(rq.optString("period"));
				String[] companyId5_arr = StringUtils.split(Util.trim(rq.optString("companyId5", "11111|22222|55555|66666|77777|80001|80002|80003|80004|80005|80006")), "|");
				String s_beg = Util.trim(StringUtils.substring(period, 0, period.indexOf("~"))); 
				String s_end = Util.trim(StringUtils.substring(period, (period.indexOf("~")+1)));
				
				Date beg = CapDate.parseDate(s_beg);
				Date end = CapDate.parseDate(s_end);
				if(beg==null){
					beg = CapDate.shiftDays(CapDate.getCurrentTimestamp(), -1);
				}
				if(end==null){
					end = CapDate.shiftDays(CapDate.getCurrentTimestamp(), -1);
				}
				
				
				Date procDate = beg;
				int alreadCnt = 0;
				while(LMSUtil.cmpDate(procDate, "<=", end) && alreadCnt<=3650){
					for(String companyId5 : companyId5_arr){
						uploadOTS_CRDLN031(companyId5, procDate);	
					}					
					//=======
					procDate = CapDate.shiftDays(procDate, 1);
					alreadCnt++;
				}
			}else if(Util.equals("delOTS_CRDLN031", act)){
				String period = Util.trim(rq.optString("period"));
				String[] companyId5_arr = StringUtils.split(Util.trim(rq.optString("companyId5", "11111|22222|55555|66666|77777|80001|80002|80003|80004|80005|80006")), "|");
				String s_beg = Util.trim(StringUtils.substring(period, 0, period.indexOf("~"))); 
				String s_end = Util.trim(StringUtils.substring(period, (period.indexOf("~")+1)));
				
				Date beg = CapDate.parseDate(s_beg);
				Date end = CapDate.parseDate(s_end);
				if(beg==null){
					beg = CapDate.shiftDays(CapDate.getCurrentTimestamp(), -1);
				}
				if(end==null){
					end = CapDate.shiftDays(CapDate.getCurrentTimestamp(), -1);
				}
				
				
				Date procDate = beg;
				int alreadCnt = 0;
				while(LMSUtil.cmpDate(procDate, "<=", end) && alreadCnt<=3650){
					for(String companyId5 : companyId5_arr){
						delOTS_CRDLN031(companyId5, procDate);	
					}					
					//=======
					procDate = CapDate.shiftDays(procDate, 1);
					alreadCnt++;
				}
			}else if(Util.equals("upC160S01D_ELF675", act)){
				String period = Util.trim(rq.optString("lnFromDatePeriod"));
				String oid = Util.trim(rq.optString("oid"));
				String s_beg = Util.trim(StringUtils.substring(period, 0, period.indexOf("~"))); 
				String s_end = Util.trim(StringUtils.substring(period, (period.indexOf("~")+1)));
				
				Date beg = CapDate.parseDate(s_beg);
				Date end = CapDate.parseDate(s_end);
				if(beg==null){
					beg = CapDate.shiftDays(CapDate.getCurrentTimestamp(), -1);
				}
				if(end==null){
					end = CapDate.shiftDays(CapDate.getCurrentTimestamp(), -1);
				}
				
				upC160S01D_ELF675(beg, end, oid);				
			}else  if(Util.equals("upELF500_introduce", act)){ // J-107-0091
				String limit_branch = Util.trim(rq.optString("limit_branch"));
				//本機執行約 1分鐘
				//可能動審表覆核後，先寫入ELF500, ELF501 => 在途的案件 => 1個月後才至 a-loan 引入
				if(true){
					List<Map<String, Object>> rowData_list = misdbBASEService.findWithMax(null
							, new Object[] { "ELF501", "elf501_mega_code like '9000%' " }
							, null);
					if(rowData_list.size()>0){					
						int[] type = {java.sql.Types.CHAR, java.sql.Types.CHAR};
						
						List<Object[]> list = new ArrayList<Object[]>();						
						for(Map<String, Object> map : rowData_list){
							String cntrNo = Util.trim(MapUtils.getString(map, "ELF501_CNTRNO"));
							String megaCode = Util.trim(MapUtils.getString(map, "ELF501_MEGA_CODE"));
							list.add(new Object[]{megaCode, cntrNo});							
									
						}
												
						misdbBASEService.update(
								new Object[] { "MIS.ELF500", "elf500_mega_code=? "
										, "elf500_cntrNo=? " }
								, type, list);
					}					
				}
				if(true){
					String[] arr_agntNo = {"11111", "11112", "22222", "55555"};
					for(String brNo: get_br_list(limit_branch)){
						// elf501_importid 約4萬7千筆
						List<Map<String, Object>> rowData_list = misdbBASEService.findWithMax(null
								, new Object[] { "ELF501", "elf501_importid > ' ' and elf501_importid<>'00000' and elf501_cntrno like ? " }
								, new Object[]{ (brNo+"%") });
						if(rowData_list.size()>0){					
							int[] type = {java.sql.Types.CHAR, java.sql.Types.CHAR};
							
							List<Object[]> list_megaEmpNo = new ArrayList<Object[]>();
							List<Object[]> list_agntNo = new ArrayList<Object[]>();
							for(Map<String, Object> map : rowData_list){
								String cntrNo = Util.trim(MapUtils.getString(map, "ELF501_CNTRNO"));
								String importId = Util.trim(MapUtils.getString(map, "ELF501_IMPORTID"));
								
								if(CrsUtil.inCollection(importId, arr_agntNo)){
									list_agntNo.add(new Object[]{Util.equals(importId, "11112")?"11111":importId, cntrNo});
								}else{
									String newEmpNo = importId;
									if(importId.length()==5){
										newEmpNo = "0"+importId;
									}
									list_megaEmpNo.add(new Object[]{newEmpNo, cntrNo});
								}		
							}
							if(list_megaEmpNo.size()>0){								
								misdbBASEService.update(
										new Object[] { "MIS.ELF500", "elf500_mega_empno=? "
												, "elf500_cntrNo=? " }
										, type, list_megaEmpNo);
							}	
							if(list_agntNo.size()>0){
								misdbBASEService.update(
									new Object[] { "MIS.ELF500", "elf500_agnt_no=? "
											, "elf500_cntrNo=? " }
									, type, list_agntNo);
							}
						}
					}
				}
			}else  if(Util.equals("upL140M01A_introduce", act)){ // J-107-0091
				String limit_branch = Util.trim(rq.optString("limit_branch"));
				//把 l140s02a 的 importId, megaCode 填至 L140M01A, 本機執行約20分鐘
				if(true){
					ISearch search = clsService.getMetaSearch();
					search.addSearchModeParameters(SearchMode.LIKE, "megaCode", "9%");
					search.setMaxResults(Integer.MAX_VALUE);
					Page<? extends GenericBean> page = clsService.findPage(L140S02A.class, search);
					for (GenericBean bean : page.getContent()) {
						L140S02A l140s02a = (L140S02A) bean;
						L140M01A l140m01a = clsService.findL140M01A_mainId(l140s02a.getMainId());
						if(l140m01a!=null){
							l140m01a.setMegaCode(l140s02a.getMegaCode());
							clsService.daoSave(l140m01a);							
						}
					}					
				}
				if(true){
					String[] arr_agntNo = {"11111", "11112", "22222", "55555"};
					for(String brNo: get_br_list(limit_branch)){						
						ISearch search = clsService.getMetaSearch();
						search.addSearchModeParameters(SearchMode.GREATER_THAN, "importId", " ");
						search.addSearchModeParameters(SearchMode.NOT_EQUALS, "importId", "0");
						search.addSearchModeParameters(SearchMode.NOT_EQUALS, "importId", "0000");
						search.addSearchModeParameters(SearchMode.NOT_EQUALS, "importId", "00000");
						search.addSearchModeParameters(SearchMode.NOT_EQUALS, "importId", "000000");
						search.addSearchModeParameters(SearchMode.LIKE, "cntrNo", brNo+"%");
						search.setMaxResults(Integer.MAX_VALUE);
						Page<? extends GenericBean> page = clsService.findPage(L140S02A.class, search);
						for (GenericBean bean : page.getContent()) {
							L140S02A l140s02a = (L140S02A) bean;
							L140M01A l140m01a = clsService.findL140M01A_mainId(l140s02a.getMainId());
							if(l140m01a!=null){
								String importId = Util.trim(l140s02a.getImportId());
								if(CrsUtil.inCollection(importId, arr_agntNo)){									
									l140m01a.setAgntNo(Util.equals(importId, "11112")?"11111":importId);
								}else{
									l140m01a.setMegaEmpNo(importId); //放原始的 data, 不前補0
								}						
								clsService.daoSave(l140m01a);		
							}
						}	
					}										
				}				
			}else  if(Util.equals("procFtpFile", act)){ 
				String fn = Util.trim(rq.optString("fn"));
				String forceRedo = Util.trim(rq.optString("forceRedo"));
				int keepDays = -1;
				if(Util.equals("IMWM0017.D", fn)){ //月批
					procFtpFile_IMWM0017(forceRedo);
					keepDays = rq.optInt("keepDays", 130);
				}else if(Util.equals("IDWM0002.D", fn)){ //日批
					procFtpFile_IDWM0002(forceRedo);
					keepDays = rq.optInt("keepDays", 10);
				}
				deleteFtpRecord(fn, keepDays);
			}else  if(Util.equals("dw_fixData", act)){
				String brCd = Util.trim(rq.optString("brCd"));
				String noteId = Util.trim(rq.optString("noteId"));
				String cntrNo = Util.trim(rq.optString("cntrNo"));
				dw_fixData(brCd, noteId, cntrNo);
			}else  if(Util.equals("dw_fixData_DATA_SRC_DT__null", act)){
				String[] noteId_arr = Util.trim(rq.optString("noteId")).split("\\|");
				dw_fixData_DATA_SRC_DT__null(noteId_arr);
			}else  if(Util.equals("dw_fixData_OTS_RKCREDITOVS_CHKDATE_CHKEMPNO", act)){
				String brCd = Util.trim(rq.optString("brCd"));
				String noteId = Util.trim(rq.optString("noteId"));
				String cntrNo = Util.trim(rq.optString("cntrNo"));
				dw_fixData_OTS_RKCREDITOVS_CHKDATE_CHKEMPNO(brCd, noteId, cntrNo);
			}else if(Util.equals("uploadELF459", act)){
				//透過批次，補上傳 ELF459 的程式 => 在簽報書覆核時，已由 CLSService :: upMisByCls(L120M01A meta)
				String[] period_arr = Util.trim(rq.optString("period")).split("\\|");
				String cntrNo = Util.trim(rq.optString("cntrNo"));
				String s_type = "";
				String s_beg = ""; 
				String s_end = "";
				 
				if(period_arr!=null && period_arr.length==3){
					s_type = Util.trim(period_arr[0]);
					
					if(Util.equals("S", s_type)){
						s_beg = Util.trim(period_arr[1]);
						s_end = Util.trim(period_arr[2]);
						//==================
						uploadELF459(s_beg, s_end, cntrNo);
					}else{
						if(Util.equals("Y", s_type)){
							s_beg = Util.trim(period_arr[1])+"-01-01";
							s_end = Util.trim(period_arr[2])+"-12-31";
						}else if(Util.equals("M", s_type)){
							s_beg = Util.trim(period_arr[1])+"-01";
							s_end = TWNDate.toAD(CapDate.shiftDays(CapDate.addMonth(CapDate.parseDate(Util.trim(period_arr[2])+"-01"), 1), -1));
						}else {
							s_beg = Util.trim(period_arr[1]);
							s_end = Util.trim(period_arr[2]);	
						}	
						//==================
						//逐日
						Date beg = CapDate.parseDate(s_beg);
						Date end = CapDate.parseDate(s_end);
						if(beg==null){
							beg = CapDate.shiftDays(CapDate.getCurrentTimestamp(), -1);
						}
						if(end==null){
							end = CapDate.shiftDays(CapDate.getCurrentTimestamp(), -1);
						}
						
						Date procDate = beg;
						int alreadCnt = 0;
						while(LMSUtil.cmpDate(procDate, "<=", end) && alreadCnt<=3650){
							String str_procDate = TWNDate.toAD(procDate);
							uploadELF459(str_procDate, str_procDate, cntrNo);					
							//=======
							procDate = CapDate.shiftDays(procDate, 1);
							alreadCnt++;
						}
					}
				}else{
					String str_procDate = TWNDate.toAD(CapDate.getCurrentTimestamp());
					uploadELF459(str_procDate, str_procDate, cntrNo);
				}					
			}else if(Util.equals("upd_ELF459_SRCFLAG_prodKind69_2020", act)){ // {NoAgreeQueryEjVer}part1~part2合計8972筆 ,  {有AgreeQueryEjVer}part1~part2合計7581筆
				//select * from com.bcodetype where codetype='onlineApplyProd69NoEjAgr_2020'
				//select * from com.bcodetype where codetype='onlineApplyProdKind69_2020'
				List<String> data_list = new ArrayList<String>();
				data_list.addAll(ClsNoAgreeQueryEjVerOnlineCntrNo.upd_ELF459_SRCFLAG_prodKind69_noAgreeQueryEjVer_2020_part1());
				data_list.addAll(ClsNoAgreeQueryEjVerOnlineCntrNo.upd_ELF459_SRCFLAG_prodKind69_noAgreeQueryEjVer_2020_part2());
				data_list.addAll(ClsNoAgreeQueryEjVerOnlineCntrNo.upd_ELF459_SRCFLAG_prodKind69_noAgreeQueryEjVer_2020_part3());
				data_list.addAll(ClsOnlineCntrNo.upd_ELF459_SRCFLAG_prodKind69_hasAgreeQueryEjVer_2020_part1());
				data_list.addAll(ClsOnlineCntrNo.upd_ELF459_SRCFLAG_prodKind69_hasAgreeQueryEjVer_2020_part2());
				List<Object[]> batchValues = new ArrayList<Object[]>();	
				int idx = 0;
				int cnt_data_list = data_list.size();				
				String target_elf459_srcflag = "1";
				for(String cntrNo: data_list){
					++idx;
					//~~~
					Object[] o = new Object[2];
					//===
					o[0] = target_elf459_srcflag;
					o[1] = cntrNo;
					//===
					batchValues.add(o);
					
					//~~~
					if(idx%500==0 || idx==cnt_data_list){
						misdbBASEService.batch_update_elf459_srcflag(batchValues);
						batchValues = new ArrayList<Object[]>();	
					}
				}
			}else if(Util.equals("upd_ELF459_SRCFLAG_prodKind69_2021", act)){ // part1~part5合計21536筆
				//select * from com.bcodetype where codetype='onlineApplyProdKind69_2021'
				List<String> data_list = new ArrayList<String>();
				data_list.addAll(ClsOnlineCntrNo.upd_ELF459_SRCFLAG_prodKind69_2021_part1());
				data_list.addAll(ClsOnlineCntrNo.upd_ELF459_SRCFLAG_prodKind69_2021_part2());
				data_list.addAll(ClsOnlineCntrNo.upd_ELF459_SRCFLAG_prodKind69_2021_part3());
				data_list.addAll(ClsOnlineCntrNo.upd_ELF459_SRCFLAG_prodKind69_2021_part4());
				data_list.addAll(ClsOnlineCntrNo.upd_ELF459_SRCFLAG_prodKind69_2021_part5());
				List<Object[]> batchValues = new ArrayList<Object[]>();	
				int idx = 0;
				int cnt_data_list = data_list.size();				
				String target_elf459_srcflag = "1";
				for(String cntrNo: data_list){
					++idx;
					//~~~
					Object[] o = new Object[2];
					//===
					o[0] = target_elf459_srcflag;
					o[1] = cntrNo;
					//===
					batchValues.add(o);
					
					//~~~
					if(idx%500==0 || idx==cnt_data_list){
						misdbBASEService.batch_update_elf459_srcflag(batchValues);
						batchValues = new ArrayList<Object[]>();	
					}
				}
			}else if(Util.equals("run_ELF490B", act)){
				String yyyy_MM = Util.trim(rq.optString("yyyy_MM"));
				String[] brnGroup = Util.trim(rq.optString("brnGroup")).split("\\|");
				if(CapDate.parseDate(yyyy_MM+"-01")!=null){
					//使用傳入的參數
				}else{
					yyyy_MM = StringUtils.substring(TWNDate.toAD(CapDate.getCurrentTimestamp()), 0, 7);
				}
				
				String end_dataYM = CrsUtil.get_R96_base_ym(yyyy_MM);
				
				String[] period_arr = CrsUtil.get_CLS180R18_period_byEndYM(end_dataYM, 10);
				String beg_yyyyMMdd = period_arr[0];
				String end_yyyyMMdd = period_arr[1];
				
				if(brnGroup==null || brnGroup.length==0 || Util.equals("", StringUtils.join(brnGroup, ""))){
					brnGroup = CrsUtil.get_ELF490B_areaArr(); 
				}
				
				run_ELF490B(beg_yyyyMMdd, end_yyyyMMdd, brnGroup);				
			}else if(Util.equals("PROC_DW_LNCUSTREL_BR", act)){ //J-107-0129 , SLMS-00074 由DW接收同一通訊處註記_比對結果
				//TODO select * from DWADM.DW_LNCUSTREL where CUST_KEY like '###%' ORDER BY CYC_MN DESC fetch first 3 rows only
				/*
				  	若 DW_LNCUSTREL 裡 CUST_KEY like '###%' 的 CYC_MN 是 {上上個月}
				  	但是
				  	+ select * from DWADM.DW_LNCUSTREL  where CYC_MN= ?上個月
				  	+ select * from DWADM.DW_LNCUSTBR   where CYC_MN= ?上個月
				  	都有資料	=> 表示 DW 的批次有跑完，但是 因故未寫入 {完成註記}，要找 DW 協助處理
				 */
				
				String forceRedo = Util.trim(rq.optString("forceRedo"));
				String cyc_mn = Util.trim(rq.optString("cyc_mn"));
				String cust_key = Util.trim(rq.optString("cust_key"));
				String rel_flag = Util.trim(rq.optString("rel_flag"));
				if(Util.isEmpty(cyc_mn)){
					cyc_mn = TWNDate.toAD(CapDate.addMonth(CrsUtil.get_sysMonth_1st(), -1));
				}
				int cnt_cyc_mn = 0;
				if(true){
					String fn = CrsUtil.FN_KEY_C900S02E;
					String genDate = StringUtils.replace(cyc_mn, "-", "");
					String genTime="000000";
					C900M03A m03a_for_C900S02E = clsService.findC900M03A_fnGenDateGenTime(fn, genDate, genTime);
					if(m03a_for_C900S02E==null ){
						
					}else{
						cnt_cyc_mn = 1;
					}
				}				
				if(cnt_cyc_mn==0){
					//未寫入控制檔
					boolean save_LNCUSTREL = proc_GET_DW_LNCUSTREL(forceRedo, cyc_mn, cust_key, rel_flag);
					boolean save_LNCUSTBR = proc_GET_DW_LNCUSTBR(forceRedo, cyc_mn);
					if(save_LNCUSTREL || save_LNCUSTBR){
						proc_GET_MIS_ELF488(forceRedo, cyc_mn);
						ctl_C900S02E_C900S02F(cyc_mn);	
					}
				}else{
					if(Util.equals("Y", forceRedo)){
						boolean save_LNCUSTREL = proc_GET_DW_LNCUSTREL(forceRedo, cyc_mn, cust_key, rel_flag);
						boolean save_LNCUSTBR = proc_GET_DW_LNCUSTBR(forceRedo, cyc_mn);
						if(save_LNCUSTREL || save_LNCUSTBR){
							proc_GET_MIS_ELF488(forceRedo, cyc_mn);
						}
						
						int del_c900s02e_cnt = clsService.setDeletedtimeByCYC_MN_no_chk_result(cyc_mn);
						logger.info("del_c900s02e_cnt="+del_c900s02e_cnt);
						
						cnt_cyc_mn = clsService.count_C900S02E_cyc_mn(cyc_mn);
						if(cnt_cyc_mn==0){ // 把寫入成功的註記清掉 C900M03A
							String fn = CrsUtil.FN_KEY_C900S02E;
							String genDate = StringUtils.replace(cyc_mn, "-", "");
							String genTime="000000";
							C900M03A c900m03a = clsService.findC900M03A_fnGenDateGenTime(fn, genDate, genTime);
							if(c900m03a!=null){
								clsService.daoDelete(c900m03a);
							}
						}
						/*
						ctl_C900S02E_C900S02F(cyc_mn);
						*/
					}
				}		
			}else if(Util.equals("DEL_C900S02E_C900S02F", act)){
				String cyc_mn = Util.trim(rq.optString("cyc_mn"));
				if(Util.isEmpty(cyc_mn)){
					cyc_mn = TWNDate.toAD(CapDate.addMonth(CrsUtil.get_sysMonth_1st(), -1));
				}
				int del_c900s02e_cnt = clsService.setDeletedtimeByCYC_MN_no_chk_result(cyc_mn);
				logger.info("[DEL_C900S02E_C900S02F]del_c900s02e_cnt="+del_c900s02e_cnt);
				
				int cnt_cyc_mn = clsService.count_C900S02E_cyc_mn(cyc_mn);
				if(cnt_cyc_mn==0){ // 把寫入成功的註記清掉 C900M03A
					String fn = CrsUtil.FN_KEY_C900S02E;
					String genDate = StringUtils.replace(cyc_mn, "-", "");
					String genTime="000000";
					C900M03A c900m03a = clsService.findC900M03A_fnGenDateGenTime(fn, genDate, genTime);
					if(c900m03a!=null){
						clsService.daoDelete(c900m03a);
					}
				}
			}else if(Util.equals("CTL_C900S02E_C900S02F", act)){
				String forceRedo = Util.trim(rq.optString("forceRedo"));
				String cyc_mn = Util.trim(rq.optString("cyc_mn"));
				if(Util.isEmpty(cyc_mn)){
					cyc_mn = TWNDate.toAD(CapDate.addMonth(CrsUtil.get_sysMonth_1st(), -1));
				}
				int cnt_cyc_mn = clsService.count_C900S02E_cyc_mn(cyc_mn);
				int count_cyc_mn_no_chk_result = clsService.count_C900S02E_cyc_mn_no_chk_result(cyc_mn);
				logger.info("CTL_C900S02E_C900S02F[cnt_cyc_mn="+cnt_cyc_mn+"]["+count_cyc_mn_no_chk_result+"][cnt_approve="+(cnt_cyc_mn-count_cyc_mn_no_chk_result)+"]");
				if(cnt_cyc_mn==0){
					//尚未寫入C900S02E
					ctl_C900S02E_C900S02F(cyc_mn);
				}else{
					//cnt_cyc_mn>0, 表示已經寫入C900S02E
					if(cnt_cyc_mn==count_cyc_mn_no_chk_result){ //全部未註記，重產生						
						int del_c900s02e_cnt = clsService.setDeletedtimeByCYC_MN_no_chk_result(cyc_mn);
						logger.info("del_c900s02e_cnt="+del_c900s02e_cnt);
						
						ctl_C900S02E_C900S02F(cyc_mn);
					}else{
						//已有部份註記
						if(Util.equals("Y", forceRedo)){
							int del_c900s02e_cnt = clsService.setDeletedtimeByCYC_MN_no_chk_result(cyc_mn);
							logger.info("del_c900s02e_cnt="+del_c900s02e_cnt);
							
							ctl_C900S02E_C900S02F(cyc_mn);
						}
					}	
				}
			}else if(Util.equals("mail_crs_notify", act)){
				String version = Util.trim(rq.optString("version"));
				String data_type = Util.trim(rq.optString("data_type"));
				boolean run_batch = true;
				if(true){
					if(Util.equals("1", data_type)){
						Date run_date = CapDate.parseDate(Util.trim(rq.optString("run_date")));
						if(run_date==null){
							run_date = CapDate.getCurrentTimestamp();
						}
						//===========
						String[] special_date_arr = {TWNDate.toAD(CrsUtil.get_sysMonth_1st()), CrsUtil.getDataEndDate(StringUtils.substring(TWNDate.toAD(run_date), 0, 7)) };
						boolean is_bussDate = MapUtils.isNotEmpty(misdbBASEService.get_LNF320(TWNDate.toAD(run_date)));
						
						if(is_bussDate||CrsUtil.inCollection(TWNDate.toAD(run_date), special_date_arr)){
							// ...
						}else{
							run_batch = false;
						} 
						
						if(run_batch==false){
							logger.debug("mail_crs_notify[not run @"+TWNDate.toAD(run_date)+"]"
									+", is_bussDate="+is_bussDate
									+", special_date_arr={"+StringUtils.join(special_date_arr, " , ")+"}");
						}
					}else{
						//...
					}
				}
				//===========
				if(run_batch){
					String raw_data_ym = "";
					Date dt_data_ym = CapDate.parseDate(Util.trim(rq.optString("data_ym")));
					if(dt_data_ym==null){
						raw_data_ym = TWNDate.toAD(CrsUtil.get_sysMonth_1st());
					}else{
						raw_data_ym = TWNDate.toAD(dt_data_ym);
					}
					
					String data_ym = Util.trim(StringUtils.substring(raw_data_ym, 0, 7));
					boolean rMail = lms2400Service.mail_crs_notify(version, data_type, data_ym);
					if(rMail){
						
					}else{
						throw new CapException("mail_crs_notify fail", getClass());
					}	
				}				
			}else if(Util.equals("fix_LNF010_SALARY", act)){
				String period = Util.trim(rq.optString("lnFromDatePeriod"));
				String oid = Util.trim(rq.optString("oid"));
				String s_beg = Util.trim(StringUtils.substring(period, 0, period.indexOf("~"))); 
				String s_end = Util.trim(StringUtils.substring(period, (period.indexOf("~")+1)));
				String LNF010_UPDATE_YN = Util.trim(rq.optString("LNF010_UPDATE_YN"));
				Date beg = CapDate.parseDate(s_beg);
				Date end = CapDate.parseDate(s_end);
				if(beg==null){
					beg = CapDate.shiftDays(CapDate.getCurrentTimestamp(), -1);
				}
				if(end==null){
					end = CapDate.shiftDays(CapDate.getCurrentTimestamp(), -1);
				}
				if(Util.isEmpty(LNF010_UPDATE_YN)){
					LNF010_UPDATE_YN = "R";
				}
				fix_LNF010_SALARY(beg, end, oid, LNF010_UPDATE_YN);
			}else if(Util.equals("write_L180R19H", act)){
				Date givenDate = CapDate.parseDate(rq.optString("givenDate"));
				write_L180R19H(givenDate);
			}else if(Util.equals("job_J_109_0344", act)){
				job_J_109_0344();
			}else if(Util.equals("ploan_TRPAYSQ1", act)){ // SLMS-00121
				int daysTRPAYSQ1 = rq.optInt("daysTRPAYSQ1", -5 );
				ploan_TRPAYSQ1(daysTRPAYSQ1);
			}else if(Util.equals("ploan_getStakeholderData", act)){ // SLMS-00142
				//J-110-0373 中鋼消貸
				String ploanPlan = Util.trim(rq.optString("ploanPlan"));
				ploan_getCSCStakeholderData(ploanPlan);
			}else if(Util.equals("labor_bailout_genC340M01A", act)){ // SLMS-00129
				int days = rq.optInt("days", 0 );
				String mainId = rq.optString("mainId");
				labor_bailout_genC340M01A(days, mainId);
			}else if(Util.equals("trans_DW_RKtbl_cntrNo_loanNo", act)){ // SLMS-00123 , J-110-0131 , (110)第(0875)號 , 國外部、南京東_轉換
				Date exDate = CapDate.parseDate(rq.optString("exDate"));
				trans_DW_RKtbl_cntrNo_loanNo(exDate);
			}else if(Util.equals("trans_OTS_CRDLN031_old_to_new_AREA_CD", act)){ // J-110-0104 , 932 改為 931
				String old_areaNo = rq.optString("old_areaNo");
				String new_areaNo = rq.optString("new_areaNo");
				String limitBrNo = rq.optString("limitBrNo");
				Set<String> s = new HashSet<String>();
				if(Util.isEmpty(limitBrNo)){
					if(Util.equals("932", old_areaNo)){
						s.add("005"); // 忠孝分行                                                   
						s.add("019"); // 安和分行                                                   
						s.add("023"); // 花蓮分行                                                   
						s.add("031"); // 敦南分行                                                   
						s.add("036"); // 民生分行                                                   
						s.add("042"); // 松南分行                                                   
						s.add("046"); // 新店分行                                                   
						s.add("048"); // 信義分行                                                   
						s.add("049"); // 基隆分行                                                   
						s.add("051"); // 內湖分行                                                   
						s.add("055"); // 松山機場分行                                             
						s.add("062"); // 宜蘭分行                                                   
						s.add("067"); // 東內湖分行                                                
						s.add("074"); // 南港分行                                                   
						s.add("202"); // 台北分行                                                   
						s.add("210"); // 敦化分行                                                   
						s.add("216"); // 世貿分行                                                   
						s.add("226"); // 城東分行                                                   
						s.add("228"); // 羅東分行                                                   
						s.add("229"); // 大安分行                                                   
						s.add("237"); // 內湖科學園區分行	
					}				
				}else{
					s.add(limitBrNo);
				}   
				for(String brNo: s){
					trans_OTS_CRDLN031_old_to_new_AREA_CD(brNo, old_areaNo, new_areaNo);					
				}
			}else if(Util.equals("crs_baseCnt_R95_1", act)){ // SLMS-00127 , J-109-0372 每月6月底計算有效筆數
				String forceFlag = Util.trim(rq.optString("forceFlag"));
				crs_baseCnt_R95_1(forceFlag);
			}else if(Util.equals("fix_cls1131_labor", act)){
				
			}else if(Util.equals("fix_WBEL136_labor", act)){
				String serviceId = "NB";
				String txId = "WBEL136";
				String reqTimeBeg = rq.optString("reqTimeBeg");
				String reqTimeEnd = rq.optString("reqTimeEnd");
				String sno = Util.trim(rq.optString("sno"))+"%";
				String codeType_key = "fix_c122_labor";
				String codeType_key_cls1131 = "fix_cls1131_labor";

				logger.info("["+codeType_key+"]reqTimeBeg="+reqTimeBeg+", reqTimeEnd="+reqTimeEnd+", sno="+sno);
				if(Util.isEmpty(reqTimeBeg) || Util.isEmpty(reqTimeEnd)){
					
				}else{
					Map<String, String> flag_1 = new HashMap<String, String>();
					Map<String, String> flag_4 = new HashMap<String, String>();
					for(Map<String, Object> data_map : eloandbBASEService.find_com_bgwdata_serviceId_txId_between_reqtime(serviceId, txId, reqTimeBeg, reqTimeEnd, sno)){
						String LOGSNO = MapUtils.getString(data_map, "LOGSNO");
						String FLAG = MapUtils.getString(data_map, "FLAG");
						String DATA = MapUtils.getString(data_map, "DATA");
						if(Util.equals(FLAG, "1")){
							flag_1.put(LOGSNO, DATA);
						}else if(Util.equals(FLAG, "4")){
							flag_4.put(LOGSNO, DATA);
						}
					}	

					logger.info("["+codeType_key+"]flag_1.size()="+flag_1.size()+", flag_4.size()="+flag_4.size());
					for(String LOGSNO : flag_4.keySet()){
						String c122_mainId = Util.trim(flag_4.get(LOGSNO));
						if(!flag_1.containsKey(LOGSNO)){
							continue;
						}
						String inputStr = Util.trim(flag_1.get(LOGSNO));
						//~~~~~~~~~~~~~~~~~~~~~
						String comName = get_column_from_logStr(inputStr, "comName");
						String comTel = get_column_from_logStr(inputStr, "comTel");
						String jobTitle = get_column_from_logStr(inputStr, "jobTitle");
						String s_seniority = get_column_from_logStr(inputStr, "seniority");
						String s_payAmt = get_column_from_logStr(inputStr, "payAmt");
						String s_othAmt = get_column_from_logStr(inputStr, "othAmt");
						String othType = get_column_from_logStr(inputStr, "othType");
						
						Integer seniority = Util.isEmpty(s_seniority)?null:Util.parseInt(s_seniority);
						BigDecimal payAmt = Util.isEmpty(s_payAmt)?null:CrsUtil.parseBigDecimal(s_payAmt);
						BigDecimal othAmt = Util.isEmpty(s_payAmt)?null:CrsUtil.parseBigDecimal(s_othAmt);
						
						if(c122_mainId.length()!=32){
							continue;
						}
						C122M01A c122m01a = clsService.findC122M01A_mainId(c122_mainId);
						if(c122m01a==null){
							logger.info("["+codeType_key+"]c122_mainId="+c122_mainId+", c122m01a is null");
							continue;							
						}
						String applyKind = c122m01a.getApplyKind();
						if(!Util.equals(applyKind, UtilConstants.C122_ApplyKind.B)){
							logger.info("["+codeType_key+"]c122_mainId="+c122_mainId+", applyKind="+applyKind+"(not B)");
							continue;
						}
						CodeType codeTypeObj = codeTypeService.findByCodeTypeAndCodeValue(codeType_key, c122m01a.getMainId());
						if(codeTypeObj!=null){
							continue;
						}
						C120S01B c120s01b = clsService.findC120S01B(c122m01a.getMainId(), c122m01a.getCustId(), c122m01a.getDupNo());
						if(c120s01b==null){
							//insert
							c120s01b = new C120S01B();
							c120s01b.setMainId(c122m01a.getMainId());
							c120s01b.setCustId(c122m01a.getCustId());
							c120s01b.setDupNo(c122m01a.getDupNo());
							//================
							if (true) { //比照 WBEL136
								c120s01b.setComName(comName);
								if (true) {
									c120s01b.setComCity("");
									c120s01b.setComZip("");
									c120s01b.setComAddr("");

									c120s01b.setComTarget("");
								}
								c120s01b.setComTel(comTel);;
								c120s01b.setWorkDate(null);
								c120s01b.setJobType1("");
								c120s01b.setJobType2("");
								c120s01b.setJobTitle(jobTitle);
								c120s01b.setSeniority(new BigDecimal(seniority));
								c120s01b.setPayCurr("TWD");
								c120s01b.setPayAmt(payAmt);
								c120s01b.setOthType(othType);
								c120s01b.setOthCurr("TWD");
								c120s01b.setOthAmt(othAmt);
								c120s01b.setInDoc("");
							}							
							String insertSQL = fix_labor_s01b_ins("lms.c120s01b" , c122m01a.getMainId(), c122m01a.getCustId(), c122m01a.getDupNo()
									, comName, comTel, jobTitle, seniority==null?"null":String.valueOf(seniority)
									, payAmt==null?"null":LMSUtil.pretty_numStr(payAmt), othType
									, othAmt==null?"null":LMSUtil.pretty_numStr(othAmt));
							//================							
							clsService.daoSave(c120s01b);
							//>>>>>>>>>>>>>>>>>>>>>>>>
							CodeType codeType = new CodeType();
							codeType.setCodeType(codeType_key);
							codeType.setCodeValue(c122m01a.getMainId());
							codeType.setCodeDesc(insertSQL);
							codeType.setCodeDesc2("ins");
							codeType.setLastModifyBy("sysfix");
							codeType.setLastModifyTime(CapDate.getCurrentTimestamp());
							clsService.daoSave(codeType);
						}else{
							//update
							if(fix_labor_comName_comTel(c120s01b.getComName(), c120s01b.getComTel())) {
								String befStr = keep_chg_str(c120s01b);
								//================
								c120s01b.setComName(comName);
								c120s01b.setComTel(comTel);;
								c120s01b.setJobTitle(jobTitle);
								c120s01b.setSeniority(new BigDecimal(seniority));
								c120s01b.setPayAmt(payAmt);
								c120s01b.setOthAmt(othAmt);
								//================
								String aftstr = keep_chg_str(c120s01b);
								String upd_c122_sql = fix_labor_s01b_upd("lms.c120s01b", comName, comTel, jobTitle
										, seniority==null?"null":String.valueOf(seniority)
										, payAmt==null?"null":LMSUtil.pretty_numStr(payAmt)
										, othAmt==null?"null":LMSUtil.pretty_numStr(othAmt), c120s01b.getOid());
								clsService.daoSave(c120s01b);
								logger.info("["+codeType_key+"]c122_mainId="+c122_mainId+", update_c120s01b");
								//>>>>>>>>>>>>>>>>>>>>>>>>
								if(true){
									CodeType codeType = new CodeType();
									codeType.setCodeType(codeType_key);
									codeType.setCodeValue(c120s01b.getOid());
									codeType.setCodeDesc(upd_c122_sql);
									codeType.setCodeDesc2("upd");
									codeType.setLastModifyBy("sysfix");
									codeType.setLastModifyTime(CapDate.getCurrentTimestamp());
									clsService.daoSave(codeType);
									logger.info("["+codeType_key+"]c122_mainId="+c122_mainId+", update_codetype_1, 【upd_sql】"+upd_c122_sql);
									logger.info("["+codeType_key+"]c122_mainId="+c122_mainId+", update_codetype_2, 【befStr】"+befStr+"【aftstr】"+aftstr);									
								}
								//>>>>>>>>>>>>>>>>>>>>>>>>
								C101M01A c101m01a = clsService.findC101M01A_brIdDup(c122m01a.getOwnBrId(), c122m01a.getCustId(), c122m01a.getDupNo());
								if(c101m01a==null){
									logger.info("["+codeType_key+"]c122_mainId="+c122_mainId+", find_no_c101{"+c122m01a.getOwnBrId()+", "+c122m01a.getCustId()+", "+c122m01a.getDupNo()+"}");
								}else{
									C101S01B c101s01b = clsService.findC101S01B(c101m01a);
									if(c101s01b==null){
										
									}else{
										if(fix_labor_comName_comTel(c101s01b.getComName(), c101s01b.getComTel())) {
											c101s01b.setComName(comName);
											c101s01b.setComTel(comTel);;
											c101s01b.setJobTitle(jobTitle);
											c101s01b.setSeniority(new BigDecimal(seniority));
											c101s01b.setPayAmt(payAmt);
											c101s01b.setOthAmt(othAmt);
											//================
											clsService.daoSave(c101s01b);
											logger.info("["+codeType_key+"]c122_mainId="+c122_mainId+", update_c101s01b");			
											//>>>>>>>>>>>>>>>>>>>>>>>>
											if(true){
												String upd_cls1131_sql = fix_labor_s01b_upd("lms.c101s01b", comName, comTel, jobTitle
														, seniority==null?"null":String.valueOf(seniority)
														, payAmt==null?"null":LMSUtil.pretty_numStr(payAmt)
														, othAmt==null?"null":LMSUtil.pretty_numStr(othAmt), c101s01b.getOid());
												CodeType codeType = new CodeType();
												codeType.setCodeType(codeType_key_cls1131);
												codeType.setCodeValue(c101s01b.getOid());
												codeType.setCodeDesc(upd_cls1131_sql);
												codeType.setCodeDesc2("upd");
												codeType.setLastModifyBy("sysfix");
												codeType.setLastModifyTime(CapDate.getCurrentTimestamp());
												clsService.daoSave(codeType);
											}
										}	
									}
								}
							}else{
								//未被異動
							}
						}
					}
				}
			}else if(Util.equals("c160s01d_proc_ctrTypeC", act)){
				String act_step = Util.trim(rq.optString("act_step", "act01"));
				int batch_cnt = Util.parseInt(rq.optString("batch_cnt"));
				if(batch_cnt==0){
					batch_cnt = 20;
				}
				
				boolean run_act01 = false;
				boolean run_act02 = false;
				if(Util.equals(act_step, "act01")){
					run_act01 = true;
				}else if(Util.equals(act_step, "act02")){
					run_act02 = true;
				}else if(Util.equals(act_step, "act0102")){
					run_act01 = true;
					run_act02 = true;
				}
				//===========
				if(run_act01){
					c160s01d_proc_ctrTypeC_doAct01_genC340(batch_cnt);
				}
				if(run_act02){
					c160s01d_proc_ctrTypeC_doAct02_send(batch_cnt);
				}
			}else if(Util.equals("cls_call_center_amort_keep", act)){ //J-111-0455 電銷中心費用分攤：snapshot資料(最後1個營業日)
				Timestamp nowTS = CapDate.getCurrentTimestamp();
				String sysDate = TWNDate.toAD(nowTS);
				String begTs = Util.trim(rq.optString("begTs", "2022-08-31 16:00:00")); // 自2022-09起消金業務處電銷人員移至卡處，故調整信卡處客服中心之電銷人員費用分攤邏輯 => 8/31 是 8月的最後1個營業日
				String forceFlag = Util.trim(rq.optString("forceFlag", "N"));
				boolean is_sysDate_lastBusinessDay = is_givenDate_lastBusinessDay_in_givenMonth(sysDate);
				
				if(Util.equals("Y", forceFlag) || is_sysDate_lastBusinessDay){
					String amort_ym = StringUtils.substring(sysDate, 0, 7);
					int upd_cnt = eloandbBASEService.fill_call_center_amort_column(amort_ym, Timestamp.valueOf(begTs));
					logger.trace("CALL_CENTER_AMORT | amort_ym="+amort_ym+" | begTs="+begTs+" => {upd_cnt:"+upd_cnt+" }");
				}
			}else if(Util.equals("cls_call_center_amort_upDW", act)){ //J-111-0455 電銷中心費用分攤：上傳(DW說：在最後1個營業日 or 日歷日 都可 => 用日歷日)
				//上傳
				//(1)在 /elnfs/LMS/900/CALL_CENTER_AMORT/IMEL0111.H 及 IMEL0111.D
				//(2)把 .H 及.D 上傳  ftpucb1
				String act_step = Util.trim(rq.optString("act_step", "act01"));				
				String remoteDir = Util.trim(rq.optString("remoteDir", sysParameterService.getParamValue(SysParamConstants.DWUCB1FTP_DEF_DIR)));
				
				String forceFlag = Util.trim(rq.optString("forceFlag", "N"));
				String act01givenYYYYMM = Util.trim(rq.optString("act01givenYYYYMM", ""));
				boolean run_act01 = false;
				boolean run_act02 = false;
				Timestamp nowTS = CapDate.getCurrentTimestamp();
				
				String amort_ym = StringUtils.substring(Util.isNotEmpty(act01givenYYYYMM)?act01givenYYYYMM:TWNDate.toAD(nowTS), 0, 7);
				
				if(Util.equals("Y", forceFlag) || LMSUtil.cmpDate(nowTS, "==", CrsUtil.get_month_last_date(nowTS))){
					if(Util.equals(act_step, "act01")){
						run_act01 = true;
					}else if(Util.equals(act_step, "act02")){
						run_act02 = true;
					}else if(Util.equals(act_step, "act0102")){
						run_act01 = true;
						run_act02 = true;
					}	
				}
				logger.trace("CALL_CENTER_AMORT | amort_ym="+amort_ym+" | forceFlag="+forceFlag+" => {run_act01:"+run_act01+" , run_act02:"+run_act02+" }");
				//===========
				String _dw_fileName = "IMEL0111"; 
				String fileName_D = _dw_fileName+".D" ; //組合出     IMEL0111.D
				String fileName_H = _dw_fileName+".H" ; //組合出     IMEL0111.H
				
				String localFilePath = PropUtil.getProperty("docFile.dir")+ File.separator 
					+ PropUtil.getProperty("systemId") + File.separator 
					+ "900" + File.separator
					+ "CALL_CENTER_AMORT" + File.separator; //組合出      /elnfs/LMS/900/CALL_CENTER_AMORT/
				
				if(run_act01){					
					call_center_amort_genH_and_D(amort_ym , localFilePath , fileName_D , fileName_H , nowTS);
				}
				if(run_act02){
					call_center_amort_ftpH_and_DToDW(localFilePath , fileName_D , fileName_H, remoteDir);
				}
			}else if(Util.equals("J-111-0022_CLSA", act)){ //J-111-0022 金管會查核,更新 LN.LNACNTR 
				String cntrNo_param = Util.trim(rq.optString("cntrNo", ""));
				int batch_cnt = Util.parseInt(rq.optString("batch_cnt"));
				int times_cnt = Util.parseInt(rq.optString("times_cnt"));
				if(batch_cnt==0){
					batch_cnt = 50;
				}
				if(times_cnt==0){
					times_cnt = 100;
				}
				for(int i=0; i<times_cnt;i++){
					List<Map<String, Object>> rtn_list = eloandbBASEService.find_J_111_0022_CLSA(cntrNo_param, batch_cnt);
					if(rtn_list.size()==0){
						break;
					}

					List<Object[]> batchValues_MIS = new ArrayList<Object[]>();
					List<Object[]> batchValues_EL = new ArrayList<Object[]>();
					for(Map<String, Object> map : rtn_list ){
						String cntrNo = MapUtils.getString(map, "CNTRNO");					
						BigDecimal dbr22 = null;
						Object o_dbr22 = MapUtils.getObject(map, "DBR22");
						if(o_dbr22!=null){
							dbr22 = CrsUtil.parseBigDecimal(o_dbr22); 
						}					
						String companyName = MapUtils.getString(map, "COMPANYNAME");
						//=========
						batchValues_MIS.add(new Object[]{dbr22, companyName, cntrNo});
						//=========
						batchValues_EL.add(new String[]{cntrNo});					
					}
					misdbBASEService.batchUpdate_J_111_0022_LNACNTR_CLSA(batchValues_MIS);
					eloandbBASEService.batchUpdate_J_111_0022_CLSA(batchValues_EL);
				}
			}else if(Util.equals("J-111-0022_CLSB", act)){ //J-111-0022 金管會查核,更新 LN.LNA2021 
				String loanNo_param = Util.trim(rq.optString("loanNo", ""));
				int batch_cnt = Util.parseInt(rq.optString("batch_cnt"));
				int times_cnt = Util.parseInt(rq.optString("times_cnt"));
				if(batch_cnt==0){
					batch_cnt = 50;
				}
				if(times_cnt==0){
					times_cnt = 100;
				}
				for(int i=0; i<times_cnt;i++){
					List<Map<String, Object>> rtn_list = eloandbBASEService.find_J_111_0022_CLSB(loanNo_param, batch_cnt);
					if(rtn_list.size()==0){
						break;
					}

					List<Object[]> batchValues_MIS = new ArrayList<Object[]>();
					List<Object[]> batchValues_EL = new ArrayList<Object[]>();
					for(Map<String, Object> map : rtn_list ){
						String loanNo = MapUtils.getString(map, "LOANNO");					
						Integer grade1 = null;
						Object o_grade1 = MapUtils.getObject(map, "GRADE1");
						if(o_grade1!=null){
							if(o_grade1 instanceof BigDecimal){
								grade1 = CrsUtil.parseBigDecimal(o_grade1).intValue(); 
							}							
						}					
						Integer grade3 = null;
						Object o_grade3 = MapUtils.getObject(map, "GRADE3");
						if(o_grade3!=null){
							if(o_grade3 instanceof BigDecimal){
								grade3 = CrsUtil.parseBigDecimal(o_grade3).intValue(); 
							}
						}
						//=========
						batchValues_MIS.add(new Object[]{grade1, grade3, loanNo});
						//=========
						batchValues_EL.add(new String[]{loanNo});					
					}
					misdbBASEService.batchUpdate_J_111_0022_LNACNTR_CLSB(batchValues_MIS);
					eloandbBASEService.batchUpdate_J_111_0022_CLSB(batchValues_EL);
				}
			}else if(Util.equals("update491_newCrDate_J_112_0001_by_branch", act)){ //J-112-0001 因過年農曆春節連假調整覆審日期 
				String branch = Util.trim(rq.optString("branch", ""));	// 013,038,040,047,072,103,227
				String newCrDate = Util.trim(rq.optString("newCrDate", ""));	// 2023-02-01
				int addMonths = Util.parseInt(rq.optString("addMonths"));
				String sDate = Util.trim(rq.optString("sDate"));
				String eDate = Util.trim(rq.optString("eDate"));
				String chgReason = Util.trim(rq.optString("chgReason", "春節連續假期延後一個月內辦理覆審"));
				List<ELF491> elf491_list = new ArrayList<ELF491>();
				List<C241M01Z> c241m01z_list = new ArrayList<C241M01Z>();
				// 1.參數設定
				List<ELF491> elf491s = misELF491Service.selByBranch_activeCrDate(branch.split(";"), sDate, eDate);
				// 2.更新覆審日期
				for (ELF491 elf491 : elf491s) {
					// 借用覆審控制檔來做更新
					C243M01A meta = new C243M01A();
					Date chgCrDate = null;
					// 用指定的CRDATE來更新
					if (!newCrDate.isEmpty()) {
						chgCrDate = Util.parseDate(newCrDate);
					} else if (addMonths != 0) {
						// 用指定的增加月份來更新
						chgCrDate = CapDate.addMonth(elf491.getElf491_crdate(), addMonths);
					} else {
						continue;
					}
					meta.setChgCrDate(chgCrDate);
					meta.setChgReason(chgReason);
					retrialService.up491_at_c243m01aFinish(elf491_list, c241m01z_list, elf491, meta, "BATCH");
				}
				retrialService.upELF491_DelThenInsert(elf491_list);
				retrialService.saveC241M01Z(c241m01z_list);
			}else if(Util.equals("ploan_autoUpdateBranch", act)){ //J-111-0602 自動更新線上貸款平台分行
				String caseNo = Util.trim(rq.optString("caseNo", ""));	// PA20230306029246
				String newBranch = Util.trim(rq.optString("newBranch", ""));	// 229
				call_ploan_autoUpdateBranch(caseNo, newBranch);
			}else if(Util.equals("_act_elf491_R14", act)){
				String limit_branch = rq.optString("limit_branch");	// 229
				boolean force = rq.optBoolean("force");	// true,false
				boolean updateOldData = rq.optBoolean("updateOldData");	// true,false
				_act_elf491_R14(limit_branch, force, updateOldData);
			}else{
				throw new CapException("unknown_act["+act+"]", getClass());
			}
			isSuccess = true;
		} catch (CapException e) {
			msg = e.getMessage();
			isSuccess = false;
			
		} catch (Exception e) {
			msg = e.getMessage();
			logger.error(msg, e);
			isSuccess = false;
		}

		if (isSuccess) {
			result = WebBatchCode.RC_SUCCESS;//此json object 內已包含 SUCCESS
			result.element(WebBatchCode.P_RESPONSE, result.get(WebBatchCode.P_RC_MSG));
		} else {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, msg);
			result.element(WebBatchCode.P_RESPONSE, msg);
		}

		return result;
	}
	private boolean fix_labor_comName_comTel(String comName, String comTel){
		if(Util.equals(comName, "台灣佳能股份有限公司") && Util.equals(comTel, "0425322123#4321")){
			return true;
		}
		return false;
	}
	private String fix_labor_s01b_ins(String tbName, String mainId, String custId, String dupNo, String comName
			, String comTel, String jobTitle, String seniority
			, String payAmt, String othType, String othAmt){
		String ins_sql = "insert into "+tbName+" (oid,mainId,custId,dupNo,comName"
			+",comTel,jobType1,jobType2,jobTitle, seniority"
			+",PayCurr,payAmt,othType,OthCurr, othAmt)"
			+" values(get_oid(),'"+mainId+"','"+custId+"','"+dupNo+"','"+comName+"'"
			+",'"+comTel+"','', '','"+jobTitle+"',"+seniority
			+",'TWD',"+payAmt+",'"+othType+"','TWD',"+othAmt+");";
		return ins_sql;
	}
	private String fix_labor_s01b_upd(String tbName, String comName, String comTel, String jobTitle, String seniority, String payAmt, String othAmt, String oid){
		String upd_sql = "update "+tbName+" set comName='"+comName+"',comTel='"+comTel+"',jobTitle='"+jobTitle+"'"
			+",seniority="+seniority+",payAmt="+payAmt+",othAmt="+othAmt+"  where oid='"+oid+"';";
		return upd_sql;
	}
	private String keep_chg_str(C120S01B c120s01b){
		String chg_str = "{";
		chg_str += c120s01b.getComName();
		chg_str += " , ";
		chg_str += c120s01b.getComTel();
		chg_str += " , ";
		chg_str += c120s01b.getJobTitle();
		chg_str += " , ";
		chg_str += c120s01b.getSeniority();
		chg_str += " , ";
		chg_str += c120s01b.getPayAmt();
		chg_str += " , ";
		chg_str += c120s01b.getOthAmt();
		chg_str += "}";		
		return chg_str;
	}
	
	private String get_column_from_logStr(String inputStr, String column){
		String patternStr = "["+column+"=";
		int idx1 = inputStr.indexOf(patternStr);
		if(idx1 < 0){
			return "";
		}
		int idx2 = inputStr.indexOf("]", idx1);
		
		return inputStr.substring(idx1+patternStr.length(), idx2);
	}
	
	private void _act_nonOversea_produceC240M01A(String userId, String unitNo, String unitType
			, String[] dataSplit, List<String> existBrSkipList)
	throws CapException{
		if (Util.isEmpty(userId)){
			throw new CapException("userId is empty", getClass());
		}
		if (Util.isEmpty(unitNo)){
			throw new CapException("unitNo is empty", getClass());
		}
		if (Util.isEmpty(unitType)){
			throw new CapException("unitType is empty", getClass());
		}
		
		if(dataSplit==null || dataSplit.length==0){
			throw new CapException("未輸入分行、資料年月", getClass());
		}
		
		List<String> branchList = new ArrayList<String>();
		List<String> basedateList = new ArrayList<String>();
		for(String item_branch_basedata : dataSplit){
			String[] item = item_branch_basedata.split("\\^");
			if(item==null || item.length!=2){
				throw new CapException("分項資料 "+ item_branch_basedata +" 格式錯誤", getClass());	
			}
			String branch = item[0];
			String basedate = item[1];
			
			if(CapDate.parseDate(basedate+"-01")==null){
				throw new CapException(branch+"分行的資料年月:"+basedate+"錯誤", getClass());
			}
			
			if(Util.isNotEmpty(branch)){
				branchList.add(branch);	
			}
			if(Util.isNotEmpty(basedate)){
				basedateList.add(basedate);	
			}										
		}
		if(branchList.size() != basedateList.size()){
			throw new CapException("branchList.size() != basedateList.size()["+branchList.size() +"!="+ basedateList.size()+"]", getClass());
		}
		//======================
		//當該分行 無 ELF490 轉 ELF491 的記錄(C240M01Z),先轉
		lms2400Service.update490_491abnormal(branchList, CrsUtil.get_sysMonth_1st());
		
		for(String brNo : branchList){
			lms2401Service.proc_elf591_custid_not_in_ELF491(brNo, TWNDate.toAD(CapDate.addMonth(CrsUtil.get_sysMonth_1st(), 6) ));	
		}	
		
		//依選取的 分行, 產生覆審工作底稿
		int cnt = branchList.size();
		String latest__aprdcdate = misDailyctlService.getLatest_aprdcdate();
		for(int i=0; i<cnt; i++){
			String branch = branchList.get(i);
			String basedate = basedateList.get(i);
			
			lms2400Service.produce(branch, basedate, userId, unitNo, unitType, existBrSkipList, latest__aprdcdate);	
		}
	}
	
	private List<String> get_br_list(String limit_branch){
		List<IBranch> list = new ArrayList<IBranch>();		
		if(Util.isNotEmpty(limit_branch)){
			IBranch currentBr = branchService.getBranch(limit_branch);
			if(currentBr!=null){
				list.add(currentBr);	
			}			
		}else{
			list.addAll(branchService.getAllBranch());
		}
		
		List<String> branchList = new ArrayList<String>();
		for(IBranch o : list){
			branchList.add( o.getBrNo() );
		}
		return branchList;
	}
	
	private void _act_elf490_to_elf491(String limit_branch)
	throws CapException{ // SLMS-00017 消金覆審ELF490轉ELF491
		List<IBranch> list = new ArrayList<IBranch>();		
		if(Util.isNotEmpty(limit_branch)){
			IBranch currentBr = branchService.getBranch(limit_branch);
			if(currentBr==null){
				throw new CapException("分行代碼:"+limit_branch+"錯誤", getClass());
			}
			list.add(currentBr);
		}else{
			list.addAll(branchService.getAllBranch());
		}
		
		String version = "v2";
		if(Util.equals(version, "v1")){
			List<String> branchList = new ArrayList<String>();
			for(IBranch o : list){
				branchList.add( o.getBrNo() );
			}
			lms2400Service.update490_491abnormal(branchList, CrsUtil.get_sysMonth_1st());
		}else{			
			Date sysMonth_1st = CrsUtil.get_sysMonth_1st();
			String elf490_data_ym_max = misELF490Service.findMaxDataYM();
			String lastMonth = CapDate.addMonth(
					CapDate.formatDate(sysMonth_1st, CapDate.DEFAULT_DATE_FORMAT), -1);

			// elf490_data_ym 的格式: 009912表示0099年12月
			String elf490_data_ym_should = CrsUtil.elf490YM_from_adDate(lastMonth);
			
			if(Util.equals(elf490_data_ym_max, elf490_data_ym_should)){
				//ok
			}else{
				throw new CapException("目前批次執行日期{"+StringUtils.substring(TWNDate.toAD(sysMonth_1st), 0, 7)+"}對應之最新ELF490_DATA_YM應是{"+elf490_data_ym_should+"}"
						+"，但MIS資料卻是{"+elf490_data_ym_max+"}請確認中心批次作業是否「異常」？", getClass());
			}
			
			for(IBranch o : list){
				String branch = o.getBrNo();
				if(retrialService.overSeaProgram(branch)){
					continue;
				}
				
				try{
					lms2401Service.update490(branch, sysMonth_1st);	
				}catch(Exception e){
					logger.error("【"+branch+"】"+StrUtils.getStackTrace(e) );
				}
			}
			//比照 notes 的程式 gfnDB2Abnormal
			/*
			如果房貸戶, 在首次覆審後, 經過了5年, 開始出現逾期的狀況 → 要'強制'納入異常通報, 不應套用免覆審的條款
			異常通報 免覆審的狀況
			(1)都已銷戶		=> G.已結清或銷戶案件
			(2)已變成逾催戶	=> H.轉列報逾放、催收、呆帳案件
			*/
			lms2401Service.update491_abnormal();
			if(clsService.is_function_on_codetype("crs_proc_crossMonth_loanData")){
				lms2401Service.proc_crossMonth_loanData(sysMonth_1st);	
			}	
			
			for(IBranch o : list){
				String branch = o.getBrNo();
				lms2401Service.proc_elf591_custid_not_in_ELF491(branch, TWNDate.toAD(CapDate.addMonth(CrsUtil.get_sysMonth_1st(), 6) ));	
			}			
		}					
	
		if(true){
			misELF491Service.update_remom_from_8_2_to_8_1();
		}
		
		if(true){
			// R14抽樣
			_act_elf491_R14(limit_branch, false, true);
		}
		
	}
	
	private void _act_c241m01a_dbu_approveToEnd(String userId, String unitNo)
	throws CapException{
		if (Util.isEmpty(userId)){
			throw new CapException("userId is empty", getClass());
		}
		if (Util.isEmpty(unitNo)){
			throw new CapException("unitNo is empty", getClass());
		}
		lms2400Service.batch_toEnd(userId, unitNo);
	}


	/**
	 * 參考 cms 的 GenJ1060193ReportServiceImpl
	 */
	private void gen_J_106_0266_RPT(){
		List<String[]> xls_list = new ArrayList<String[]>();
		//依 loanNo 分組
		Map<String, List<Map<String, Object>> > groupByLoanNoMap_bf = new LinkedHashMap<String, List<Map<String, Object>> >();
		for(Map<String, Object> row : misdbBASEService.gen_J_106_0266_RPT()){
			String loanNo = Util.trim(MapUtils.getString(row, "LOANNO"));
			if(!groupByLoanNoMap_bf.containsKey(loanNo)){
				groupByLoanNoMap_bf.put(loanNo, new ArrayList<Map<String, Object>>());
			}
			groupByLoanNoMap_bf.get(loanNo).add(row);
		}
		
		Map<String, List<Map<String, Object>> > groupByLoanNoMap_af = _J_106_0266_fill(
				_J_106_0266_dayaYMMax(groupByLoanNoMap_bf));
			
		
		//填入 xls_list
		if(true){
			Map<String, String> idNameMap = new HashMap<String, String>();			
			for(String loanNo : groupByLoanNoMap_af.keySet()){
				for(Map<String, Object> row : groupByLoanNoMap_af.get(loanNo)){					
					String idDupNo = Util.trim(MapUtils.getString(row, "LNF030_CUST_ID"));
					String custName = _gen_J_106_0266_idName(idNameMap, idDupNo);
					String transDt = Util.trim(MapUtils.getString(row, "TRANSDT"));
					String loanDate = Util.trim(MapUtils.getString(row, "LNF030_LOAN_DATE"));
					String cancelDate = Util.trim(MapUtils.getString(row, "LNF030_CANCEL_DATE"));
					String trans_bef_loanDate = Util.trim(MapUtils.getString(row, "BEFLOANDT"));
					String trans_has_dtl = Util.trim(MapUtils.getString(row, "HASDTL"));
					String dataYM = Util.trim(MapUtils.getString(row, "DATAYM"));
					String loanBal = Util.trim(MapUtils.getString(row, "LNF090_LOAN_BAL"));
					String intRate = Util.trim(MapUtils.getString(row, "LNF090_INT_RATE_A6"));
					String pgAdd = Util.trim(MapUtils.getString(row, "PGADD"));
					//==========
					String[] object = {idDupNo, custName, loanNo, transDt, loanDate, cancelDate,
							trans_bef_loanDate, trans_has_dtl, dataYM, loanBal, intRate, pgAdd};
					xls_list.add(object);
				}
			}
		}		
		
		//輸出 xls
		if(true){
			Label label = null;
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			
			try{
				WritableWorkbook book = Workbook.createWorkbook(baos);
				WritableSheet sheet = book.createSheet("result", 0);
				WritableCellFormat cellFmt = new WritableCellFormat();
				cellFmt.setWrap(true);
				cellFmt.setAlignment(Alignment.LEFT);
				cellFmt.setVerticalAlignment(VerticalAlignment.TOP);
				cellFmt.setBackground(Colour.LIGHT_GREEN);
				cellFmt.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.GRAY_50);
				if(true){
					Map<String, Integer> header = new LinkedHashMap<String, Integer>();
					header.put("客戶統編", 13);
					header.put("客戶姓名", 30);
					header.put("授信帳號", 20);
					header.put("不動產移轉日期", 25);
					header.put("首撥日", 25);
					header.put("銷戶日", 25);
					header.put("移轉日期<首撥日", 25);
					header.put("移轉日期<銷戶日", 25);
					header.put("資料年月", 15);
					header.put("授信餘額", 15);
					header.put("補貼息利率", 15);
					header.put("註記", 15);

					int colIdx = 0;
					for(String colDesc : header.keySet()){
						int len = header.get(colDesc);
						//======
						sheet.setColumnView(colIdx, len);
						
						label = new Label(colIdx, 0, colDesc);
						label.setCellFormat(cellFmt);
						sheet.addCell(label);
						//======
						colIdx++;
					}
				}
				
				int y = 1;
				WritableCellFormat wcf2 = new WritableCellFormat();
				WritableFont wf2 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.NO_BOLD);
				wcf2.setFont(wf2);
				for(String[] row:xls_list){
					
					int size = row.length;
					for(int idx=0; idx<size;idx++){
						label = new Label(idx, y, row[idx]);
						label.setCellFormat(wcf2);
						sheet.addCell(label);	
					}
					y++;
				}
				book.write();
				book.close();
				
				DocFile docFile = new DocFile();
				docFile.setBranchId("900");
				docFile.setContentType("application/vnd.ms-excel");
				docFile.setMainId("J1060266");
				docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
				docFile.setFieldId("J1060266");
				docFile.setDeletedTime(null);
				SimpleDateFormat SDF = new SimpleDateFormat();
				SDF.applyLocalizedPattern("yyyyMMdd_HHmmss");
				String filename = SDF.format(new Date())+"_"+".xls"; 
				docFile.setSrcFileName(filename);
				docFile.setUploadTime(CapDate.getCurrentTimestamp());
				docFile.setSysId("LMS");
				docFile.setData(baos.toByteArray());
				docFileService.save(docFile);		
				
			}catch (Exception e) {
				logger.error(StrUtils.getStackTrace(e));
			}	
		}		
	}
	
	private Map<String, List<Map<String, Object>> > _J_106_0266_dayaYMMax(Map<String, List<Map<String, Object>> > org){
		Map<String, List<Map<String, Object>> > result = new LinkedHashMap<String, List<Map<String, Object>> >();
		
		for(String loanNo : org.keySet()){
			List<Map<String, Object>> raw_list = org.get(loanNo);			
			List<Map<String, Object>> new_list = new ArrayList<Map<String, Object>>();
			//========
			// 已按 LNF090_TXN_DATE, LNF090_TXN_TIME 排序
			// 取同一 dataYM 最後一筆 
			Map<String, Integer> dataYM_idx_map = new LinkedHashMap<String, Integer>();
			int raw_size = raw_list.size();
			for(int i=0; i<raw_size; i++){
				Map<String, Object> raw = raw_list.get(i);
				String dataYM = Util.trim(MapUtils.getString(raw, "DATAYM"));
				dataYM_idx_map.put(dataYM, i);
			}
			
			for(String dataYM: dataYM_idx_map.keySet()){
				int idx = dataYM_idx_map.get(dataYM);
				new_list.add(raw_list.get(idx));				
			}
			//========
			result.put(loanNo, new_list);			
		}
		return result;
	}
	private Map<String, List<Map<String, Object>> > _J_106_0266_fill(Map<String, List<Map<String, Object>> >  org){
		Map<String, List<Map<String, Object>> > result = new LinkedHashMap<String, List<Map<String, Object>> >();
		
		for(String loanNo : org.keySet()){
			List<Map<String, Object>> raw_list = org.get(loanNo);			
			List<Map<String, Object>> new_list = new ArrayList<Map<String, Object>>();
			//========
			
			Map<String, Object> previous_obj = new HashMap<String, Object>();
			int raw_size = raw_list.size();
			String pgAdd = "PGADD";
			for(int i=0; i<raw_size; i++){
				Map<String, Object> raw = raw_list.get(i);
				Date dataYM = CapDate.parseDate(Util.trim(MapUtils.getString(raw, "DATAYM"))+"-01");
				String previous_dataYM = Util.trim(MapUtils.getString(previous_obj, "DATAYM"));
				
				if(i>0){
					Date previousDt = CapDate.parseDate(previous_dataYM+"-01");
					int added = 1;
					for(;added<=12;added++){
						if(LMSUtil.cmp_yyyyMM(CapDate.addMonth(previousDt, added), "==", dataYM)){
							break;
						}
					}
					for(int proc=1; proc<=(added-1); proc++){
						Map<String, Object> copy_obj = new HashMap<String, Object>();
						copy_obj.putAll(previous_obj);
						copy_obj.put(pgAdd, "*");
						copy_obj.put("DATAYM", CapDate.formatDate(CapDate.addMonth(previousDt, proc),"yyyy-MM"));
						new_list.add(copy_obj);
					}
				}else{
					//第1筆, 仍要往下跑, 去 keep previous
				}				
				//========
				previous_obj = new HashMap<String, Object>();
				previous_obj.putAll(raw);
				//========
				new_list.add(raw);
			}
			//========
			result.put(loanNo, new_list);			
		}
		return result;
	}
	
	/**
	 * J-106-0266 產製補貼息之一次性/臨時性檔案
	 * @param idNameMap
	 * @param idDupNo
	 * @return
	 */
	private String _gen_J_106_0266_idName(Map<String, String> idNameMap, String idDupNo){
		
		if(!idNameMap.containsKey(idDupNo)){
			String custId = StringUtils.substring(idDupNo, 0, 10);
			String dupNo = StringUtils.substring(idDupNo, 10);
			Map<String, Object> latestData = iCustomerService.findByIdDupNo(custId, dupNo);
			String newCustName = Util.trim(MapUtils.getString(latestData, "CNAME"));
			idNameMap.put(idDupNo, newCustName);
		}
		return idNameMap.get(idDupNo); 
	}

	private void syncC900M01GwithLNF(String status, String grpCntrNo){
		ISearch search = clsService.getMetaSearch();
		search.addSearchModeParameters(SearchMode.LIKE, "status", status+"%");
		search.addSearchModeParameters(SearchMode.LIKE, "grpcntrno", grpCntrNo+"%");
		search.addSearchModeParameters(SearchMode.IS_NULL, "lnf_loan_date", "");
		search.setMaxResults(Integer.MAX_VALUE);
		
		for(C900M01G c900m01g : clsService.findC900M01G_search(search)){
			String cntrNo = c900m01g.getCntrNo();
			TreeSet<String> ts = new TreeSet<String>();
			List<MISLN30> lnf030_list = misLNF030Service.selByCntrNo(cntrNo);
			for(MISLN30 lnf030 : lnf030_list){
				String lnf030_loan_date = Util.trim(TWNDate.toAD(lnf030.getLnf030_loan_date()));
				if(Util.isEmpty(lnf030_loan_date)){
					continue;
				}
				ts.add(lnf030_loan_date);
			}
			if(ts.size()==0){
				// 尚未撥款
				continue;
			}else{
				String lnf030_loan_date = ts.first();
				//==============================
				if(!Util.equals("3", c900m01g.getStatus())){  //如已撥款, 變更 status=3
					c900m01g.setStatus("3");
				}
				//==============================	
				if(c900m01g.getLoanAmt()==null){
					ELF500 elf500 = misELF500Service.findByCntrNo(cntrNo);
					if(elf500!=null){
						c900m01g.setLoanAmt(elf500.getElf500_factamt());	
					}						
				}
				if(c900m01g.getLoanDate()==null){
					c900m01g.setLoanDate(CapDate.parseDate(lnf030_loan_date));	
				}
				//==============================				
				c900m01g.setLnf_loan_date(CapDate.parseDate(lnf030_loan_date));				
				clsService.daoSave(c900m01g);
			}			
		}
	}
	
	private void release_dc_C900M01G(){
		ISearch search = clsService.getMetaSearch();
		search.addSearchModeParameters(SearchMode.EQUALS, "status", "1");
		search.addSearchModeParameters(SearchMode.OR
				, new SearchModeParameter(SearchMode.IS_NULL, "UseFlag", "") 
				, new SearchModeParameter(SearchMode.NOT_EQUALS, "UseFlag", "D"));
		search.addSearchModeParameters(SearchMode.GREATER_THAN, "createTime", TWNDate.toAD(CapDate.shiftDays(CapDate.getCurrentTimestamp(), -180))+" 00:00:00");
		search.addSearchModeParameters(SearchMode.LESS_EQUALS, "createTime", TWNDate.toAD(CapDate.shiftDays(CapDate.getCurrentTimestamp(), -16))+" 00:00:00");// deleteTable.properties 預設10天
		search.addSearchModeParameters(SearchMode.IS_NULL, "lnf_loan_date", "");
		search.setMaxResults(3000);
		
		Page<? extends GenericBean> page = clsService.findPage(C900M01G.class, search);
		List<C900M01G> c900m01g_list = (List<C900M01G>) page.getContent();
		for(C900M01G c900m01g : c900m01g_list){
			if(c900m01g.getApplyAmt()==null || c900m01g.getApplyAmt().compareTo(BigDecimal.ZERO)==0){
				continue;
			}
			String cntrNo = Util.trim(c900m01g.getCntrNo());
			if(is_c900m01g_match_delL140m01aCntrNo(cntrNo)){				
				c900m01g.setUseFlag("D");
				//~~~~~~~
				clsService.daoSave(c900m01g);
			}
		}
	}
	
	private boolean is_c900m01g_match_delL140m01aCntrNo(String cntrNo){
		ISearch search = clsService.getMetaSearch();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "proPerty", UtilConstants.Cntrdoc.Property.不變);
		Page<? extends GenericBean> page = clsService.findPage(L140M01A.class, search);
		List<L140M01A> l140m01a_list = (List<L140M01A>) page.getContent();
		int cnt_del = 0;
		//int cnt_use = 0;
		for(L140M01A l140m01a : l140m01a_list){
			if(l140m01a.getDeletedTime()==null){
				return false;
			}else{
				++cnt_del;
			}
		}
		if(cnt_del>0 ){
			return true;
		}
		return false;
	}
	
	private void uploadELF457(int daysCnt, String caseBrId, String startDate, String endDate,int commitCount){
		List<ELF457> list = new ArrayList<ELF457>();
		List<Map<String, Object>> datas=new ArrayList<Map<String, Object>>();
		String approveDateBeg = "1911-01-01";
		if(daysCnt > 0){
			approveDateBeg = TWNDate.toAD(CapDate.shiftDays(CapDate.getCurrentTimestamp(), -1*daysCnt));
			datas = eloandbBASEService.findLaaData(approveDateBeg, caseBrId);
		}
		//J-109-0304_10702_B1001 上傳elf457進件來源參數
		else if(Util.isNotEmpty(startDate)&&Util.isNotEmpty(endDate)){
			datas = eloandbBASEService.findLaaData2(startDate, endDate);
		}
		
		for(Map<String, Object> rowData :datas){
			String cntrNo = Util.trim(MapUtils.getString(rowData, "CNTRNO"));
			String laaName = Util.trim(MapUtils.getString(rowData, "LAANAME"));
			String laaYear = Util.trim(MapUtils.getString(rowData, "LAAYEAR"));
			String laaWord = Util.trim(MapUtils.getString(rowData, "LAAWORD"));
			String laaNo = Util.trim(MapUtils.getString(rowData, "LAANO"));
			String laaOfficeId = Util.trim(MapUtils.getString(rowData, "LAAOFFICEID"));
			String laaOffice = Util.trim(MapUtils.getString(rowData, "LAAOFFICE"));
			String caseSrcFlag = Util.trim(MapUtils.getString(rowData, "CASESRCFLAG"));
			
			ELF457 mis_elf457 = misELF457Service.findByCntrNo(cntrNo);
			if(mis_elf457==null){
				mis_elf457 = new ELF457();
				mis_elf457.setElf457_cntrno(cntrNo);
			}
			mis_elf457.setElf457_layear(laaYear);
			mis_elf457.setElf457_laword(laaWord);
			mis_elf457.setElf457_lano(laaNo);
			mis_elf457.setElf457_laname(laaName);
			mis_elf457.setElf457_loid(laaOfficeId);
			mis_elf457.setElf457_loname(laaOffice);
			mis_elf457.setElf457_casesrcflag(caseSrcFlag);
			//---------------
			list.add(mis_elf457);
			//J-109-0304_10702_B1001 Web e-Loan 上傳elf457進件來源參數
			//當更新筆數大於commitCount筆數，先做commit，並清掉List，預設為1000筆
			if(list.size()>=commitCount){
				MISRows<ELF457> mis_elf457_temp = new MISRows<ELF457>(ELF457.class);
				mis_elf457_temp.setValues(list);
				upMisToServer(mis_elf457_temp, "MIS");
				list = new ArrayList<ELF457>();
			}
		}
		if(list.size()>0){
			MISRows<ELF457> mis_elf457 = new MISRows<ELF457>(ELF457.class);
			mis_elf457.setValues(list);
			upMisToServer(mis_elf457, "MIS");
		}
	}
	
	private void uploadELF459(String approveTime_begDt, String approveTime_endDt, String param_cntrNo){
		if(approveTime_begDt.length()!=10 || CapDate.parseDate(approveTime_begDt)==null){
			return;
		}
		if(approveTime_endDt.length()!=10 || CapDate.parseDate(approveTime_endDt)==null){
			return;
		}
		String approveTime_beg = approveTime_begDt+" 00:00:00";
		String approveTime_end = approveTime_endDt+" 23:59:59";
				
		List<Map<String, Object>> lms_list = eloandbBASEService.findClsApplyCntrDataForElf459(approveTime_beg, approveTime_end, param_cntrNo);
		
		BigDecimal val_75 = BigDecimal.valueOf(75);
		BigDecimal val_60 = BigDecimal.valueOf(60);
		BigDecimal val_1000 = BigDecimal.valueOf(1000);
		BigDecimal val_income_max = BigDecimal.valueOf(99999999);
		Date val_20131231 = CapDate.parseDate("2013-12-31");
		Date val_19110101 = CapDate.parseDate("1911-01-01");
		for(Map<String, Object> map : lms_list){
			String CASEMAINID = Util.trim(MapUtils.getString(map, "CASEMAINID"));
			String TABMAINID = Util.trim(MapUtils.getString(map, "TABMAINID"));
			String CNTRNO = Util.trim(MapUtils.getString(map, "CNTRNO"));
			String CUSTID = Util.trim(MapUtils.getString(map, "CUSTID"));
			String DUPNO = Util.trim(MapUtils.getString(map, "DUPNO"));
			Date CASEDATE = (Date)MapUtils.getObject(map, "CASEDATE");
			Date BIRTHDAY = (Date)MapUtils.getObject(map, "BIRTHDAY");
			String CHKITEM6 = Util.trim(MapUtils.getString(map, "CHKITEM6"));
			String CHKITEM7 = Util.trim(MapUtils.getString(map, "CHKITEM7"));
			String CHKITEM8 = Util.trim(MapUtils.getString(map, "CHKITEM8"));
			BigDecimal IDVANNUALINCOME = (BigDecimal)MapUtils.getObject(map, "IDVANNUALINCOME");
			BigDecimal DRATE = (BigDecimal)MapUtils.getObject(map, "DRATE");
			String S01A_OID = Util.trim(MapUtils.getString(map, "S01A_OID"));
			String S01B_OID = Util.trim(MapUtils.getString(map, "S01B_OID"));
			String S01C_OID = Util.trim(MapUtils.getString(map, "S01C_OID"));
			String S01G_OID = Util.trim(MapUtils.getString(map, "S01G_OID"));
			BigDecimal MAX_LNYM = (BigDecimal)MapUtils.getObject(map, "MAX_LNYM");
			String MIN_GRADE1 = Util.trim(MapUtils.getString(map, "MIN_GRADE1"));
			if(Util.isEmpty(CNTRNO)){
				continue;
			}
			/* logger.debug("_BEF"
					+"[CNTRNO="+CNTRNO+"]"
					+"[CASEMAINID="+CASEMAINID+"]"
					+"[TABMAINID="+TABMAINID+"]"
					+"[CUSTID="+CUSTID+"]"
					+"[CASEDATE="+TWNDate.toAD(CASEDATE)+"]"
					+"[BIRTHDAY="+TWNDate.toAD(BIRTHDAY)+"]"
					+"[CHKITEM6="+CHKITEM6+"]"
					+"[CHKITEM7="+CHKITEM7+"]"
					+"[CHKITEM8="+CHKITEM8+"]"
					+"[IDVANNUALINCOME="+IDVANNUALINCOME+"]"
					+"[DRATE="+DRATE+"]"
					+"[MAX_LNYM="+MAX_LNYM+"]"
					+"[MIN_GRADE1="+MIN_GRADE1+"]"
					); */
			//==============
			if(Util.isEmpty(S01A_OID) || Util.isEmpty(S01B_OID) || Util.isEmpty(S01C_OID)){
				List<Map<String, Object>> rk_applicant_list = dwdbBASEService.findDW_RKAPPLICANT_byNoteIdCustIdDupNo(CASEMAINID, TABMAINID, CUSTID, DUPNO);
				if(rk_applicant_list.size()>0){
					Map<String, Object> rk_applicant = rk_applicant_list.get(0);
					if(BIRTHDAY==null){
						BIRTHDAY = (Date)MapUtils.getObject(rk_applicant, "DOB");
					}
					if(IDVANNUALINCOME==null || BigDecimal.ZERO.compareTo(IDVANNUALINCOME)==0){
						BigDecimal YPAY = (BigDecimal)MapUtils.getObject(rk_applicant, "YPAY");
						BigDecimal OMONEY_AMT = (BigDecimal)MapUtils.getObject(rk_applicant, "OMONEY_AMT");
						if(YPAY!=null || OMONEY_AMT!=null){
							IDVANNUALINCOME = CrsUtil.parseBigDecimal(YPAY).add(CrsUtil.parseBigDecimal(OMONEY_AMT));
						}						
					}
					if(DRATE==null || BigDecimal.ZERO.compareTo(DRATE)==0){
						DRATE = (BigDecimal)MapUtils.getObject(rk_applicant, "DRATE");
					}
				}				
			}
			if(BIRTHDAY==null || (BIRTHDAY!=null && LMSUtil.cmpDate(BIRTHDAY, "<=", val_19110101))){
				//生日不合理, 改抓0024
				Map<String, Object> map0024 = misCustdataService.findByIdDupNo(CUSTID, DUPNO);
				BIRTHDAY = (Date)MapUtils.getObject(map0024, "BIRTHDT");
			}
			
			if(Util.isEmpty(S01G_OID)){
				List<Map<String, Object>> rk_jcic_list = dwdbBASEService.findDW_RKJCIC_byNoteIdCustIdDupNo(CASEMAINID, TABMAINID, CUSTID, DUPNO);
				if(rk_jcic_list.size()>0){
					Map<String, Object> rk_jcic = rk_jcic_list.get(0);
					CHKITEM6 = Util.trim(MapUtils.getString(rk_jcic, "CC12_TOTPAY_DELAY_TIMES"));
					CHKITEM7 = Util.trim(MapUtils.getString(rk_jcic, "CC12_CASH_ADV_TIMES"));
					CHKITEM8 = Util.trim(MapUtils.getString(rk_jcic, "LN12_CASH_TIMES"));
				}
			}
			if(Util.isEmpty(MIN_GRADE1) && CASEDATE!=null && LMSUtil.cmpDate(CASEDATE, "<=", val_20131231)){				
				List<Map<String, Object>> elf675_list = misdbBASEService.findWithMax(null
						, new Object[] { "ELF675", "elf675_noteId in (?, ?) and elf675_mowtype='M' and elf675_final_ratfg='Y' " }
						, new Object[] {CASEMAINID, TABMAINID});
				if(elf675_list.size()>0){		
					Map<String, Object> elf675 = elf675_list.get(0);
					MIN_GRADE1 = Util.trim(MapUtils.getString(elf675, "ELF675_FINAL_RATE"));
				}
			}
			/* logger.debug("_AFT"
					+"[CNTRNO="+CNTRNO+"]"
					+"[CASEMAINID="+CASEMAINID+"]"
					+"[TABMAINID="+TABMAINID+"]"
					+"[CUSTID="+CUSTID+"]"
					+"[CASEDATE="+TWNDate.toAD(CASEDATE)+"]"
					+"[BIRTHDAY="+TWNDate.toAD(BIRTHDAY)+"]"
					+"[CHKITEM6="+CHKITEM6+"]"
					+"[CHKITEM7="+CHKITEM7+"]"
					+"[CHKITEM8="+CHKITEM8+"]"
					+"[IDVANNUALINCOME="+IDVANNUALINCOME+"]"
					+"[DRATE="+DRATE+"]"
					+"[MAX_LNYM="+MAX_LNYM+"]"
					+"[MIN_GRADE1="+MIN_GRADE1+"]"
					); */
			//==============
			BigDecimal AGEWHENAPPLYE = null;
			if(BIRTHDAY!=null){
				Integer newage = OverSeaUtil.getAge(BIRTHDAY, CASEDATE);
				AGEWHENAPPLYE = (newage==null)?null:BigDecimal.valueOf(newage);
			}
			
			ELF459 elf459 = findELF459(CNTRNO);
			String elf459_srcflag = "";
			String elf459_ploan_no = "";
			String elf459_term_group = "";	
			if(elf459==null){
				elf459 = new ELF459();
				elf459.setElf459_cntrno(CNTRNO);
			}else{
				elf459_srcflag = Util.trim(elf459.getElf459_srcflag());
				//可能 notes e-loan 簽報書/批覆書
				//在 L120M01A 有2個 mainId
				
				if((AGEWHENAPPLYE==null && IDVANNUALINCOME==null && DRATE==null && Util.isEmpty(MIN_GRADE1))){
					continue;
				}
			}	
			
			if((AGEWHENAPPLYE!=null && AGEWHENAPPLYE.compareTo(val_1000)>=0) || 
				(MAX_LNYM!=null && MAX_LNYM.compareTo(val_1000)>=0) || 
				(DRATE!=null && DRATE.compareTo(val_1000)>=0) ){
				logger.error(""
					+"[CNTRNO="+CNTRNO+"]"
					+"[CASEMAINID="+CASEMAINID+"]"
					+"[TABMAINID="+TABMAINID+"]"
					+"[CUSTID="+CUSTID+"]"
					+"[CASEDATE="+TWNDate.toAD(CASEDATE)+"]"
					+"[BIRTHDAY="+TWNDate.toAD(BIRTHDAY)+"]"
					+"[CHKITEM6="+CHKITEM6+"]"
					+"[CHKITEM7="+CHKITEM7+"]"
					+"[CHKITEM8="+CHKITEM8+"]"
					+"[IDVANNUALINCOME="+IDVANNUALINCOME+"]"
					+"[DRATE="+DRATE+"]"
					+"[MAX_LNYM="+MAX_LNYM+"]"
					+"[MIN_GRADE1="+MIN_GRADE1+"]"
					);
				continue;
			}
			if(IDVANNUALINCOME!=null && IDVANNUALINCOME.compareTo(val_income_max)>0) {
				IDVANNUALINCOME = val_income_max;
			}
						
			elf459.setElf459_rptId(CASEMAINID);
			elf459.setElf459_tabMainId(TABMAINID); //可能核准後, 又退回, 另外產生批覆書
			elf459.setElf459_caseDate(CASEDATE);
			elf459.setElf459_applyAge(AGEWHENAPPLYE);
			elf459.setElf459_lnAge(MAX_LNYM);
			elf459.setElf459_negative_6(convert_negative_info(CHKITEM6));
			elf459.setElf459_negative_7(convert_negative_info(CHKITEM7));
			elf459.setElf459_negative_8(convert_negative_info(CHKITEM8));
			elf459.setElf459_income(IDVANNUALINCOME);
			elf459.setElf459_dRate(DRATE);			
			elf459.setElf459_grade(MIN_GRADE1);
			if(true){
				String flag1 = CrsUtil.parseBigDecimal(elf459.getElf459_applyAge()).add(elf459.getElf459_lnAge()).compareTo(val_75)>0?"Y":"";
				String flag2 = (Util.equals("Y", elf459.getElf459_negative_6())||Util.equals("Y", elf459.getElf459_negative_7())||Util.equals("Y", elf459.getElf459_negative_8()))?"Y":"";
				String flag3 = (CrsUtil.parseBigDecimal(elf459.getElf459_dRate()).compareTo(val_60)>0 && elf459.getElf459_income()!=null && CrsUtil.parseBigDecimal(elf459.getElf459_income()).compareTo(val_60)<0)?"Y":"";
				elf459.setElf459_flag1(flag1);
				elf459.setElf459_flag2(flag2);
				elf459.setElf459_flag3(flag3);
			}
			
			L140M01A l140m01a = clsService.findL140M01A_mainId(TABMAINID);
			if(l140m01a!=null){
				L140M01Y l140m01y_elf459Srcflag1 = clsService.findL140M01Y_refTypeELF459Srcflag1_1stItem(l140m01a.getMainId());
				if(l140m01y_elf459Srcflag1 != null){
					C122M01A c122m01a = clsService.findC122M01A_fromL140M01Y_refTypeDocCode1ELF459Srcflag1_refModelC122M01A(l140m01y_elf459Srcflag1);
					if(c122m01a!=null){
						String incomType = Util.trim(c122m01a.getIncomType());
						if(Util.notEquals(incomType, "1")){//不為1的都是線上進件
							elf459_srcflag = "1";
						}
						elf459_ploan_no = Util.trim(c122m01a.getPloanCaseId());
					}
				}
				List<L140S02A> l140s02a_list = clsService.findL140S02A(l140m01a);
				if(true){					
					elf459_term_group = ClsUtility.get_elf459_term_group( l140s02a_list );
				}	
			}	
			
			elf459.setElf459_srcflag(elf459_srcflag);					
			elf459.setElf459_ploan_no(elf459_ploan_no);
			elf459.setElf459_term_group(elf459_term_group);
			elf459.setElf459_proj_class(Util.trim(l140m01a==null?"":l140m01a.getProjClass()));
			//---------------
			List<ELF459> list = new ArrayList<ELF459>();
			list.add(elf459);
			if(list.size()>0){
				MISRows<ELF459> mis_elf459 = new MISRows<ELF459>(ELF459.class);
				mis_elf459.setValues(list);
				upMisToServer(mis_elf459, "MIS");
			}
		}
	}
	
	private String convert_negative_info(String input){
		if(Util.equals("Y", input) || Util.equals("1", input)){
			return "Y";
		}else if(Util.equals("N", input) || Util.equals("2", input)){
			return "N";
		}else if(Util.equals("3", input)){
			return "N";
		}
		return input;
	}
	private ELF459 findELF459(String cntrNo) {
		List<Map<String, Object>> rowData_list = misdbBASEService.findWithMax(null
				, new Object[] { "ELF459", "elf459_cntrno=?  " }
				, new Object[] {cntrNo});		
		List<ELF459> list = toELF459(rowData_list);
		
		if(list.size()==1){			
			return list.get(0);
		}else{
			return null;
		}
	}
	
	private List<ELF459> toELF459(List<Map<String, Object>> rowData){
		List<ELF459> list = new ArrayList<ELF459>();
		for (Map<String, Object> row : rowData) {
			ELF459 model = new ELF459();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
	
	private void uploadOTS_CRDLN031(String companyId5, Date date){		
		List<OTS_CRDLN031> list_OTS_CRDLN031 = new ArrayList<OTS_CRDLN031>();
		String str_date = TWNDate.toAD(date);
		LinkedHashMap<String, Map<String, Object>> list_dd = new LinkedHashMap<String, Map<String, Object>>();
		LinkedHashMap<String, Map<String, Object>> list_time = new LinkedHashMap<String, Map<String, Object>>();
		
		BigDecimal unit = BigDecimal.ONE; //new BigDecimal("1000");
		int scale = 0;
		
		for(Map<String, Object> row_mm : eloandbBASEService.findC900S02A_groupBy_brNo(
				str_date, str_date, companyId5)){
			String key = Util.trim(row_mm.get("BRSETNO"))+"_"
								+Util.trim(row_mm.get("BRNO"))+"_"
								+Util.trim(row_mm.get("COMPANYID5"));												
			list_dd.put(key, row_mm);
		}
		
		for(Map<String, Object> row_time : eloandbBASEService.findC900S02A_groupBy_brNo(
				"1911-01-01", str_date, companyId5)){
			String key = Util.trim(row_time.get("BRSETNO"))+"_"
							+Util.trim(row_time.get("BRNO"))+"_"
							+Util.trim(row_time.get("COMPANYID5"));
			list_time.put(key, row_time);
		}
		
		TreeSet<String> define_key_set = new TreeSet<String>(list_time.keySet());
		for(String area_companyId_brNo_key: define_key_set){
			Map<String, Object> map_mm = list_dd.get(area_companyId_brNo_key);
			Map<String, Object> map_time = list_time.get(area_companyId_brNo_key);
			
			String BRSETNO = Util.trim(MapUtils.getString(map_time, "BRSETNO")); 
//			String BRNAME = Util.trim(MapUtils.getString(map_time, "BRNAME"));                                                          
			String BRNO = Util.trim(MapUtils.getString(map_time, "BRNO"));
			String COMPANYID5 = Util.trim(MapUtils.getString(map_time, "COMPANYID5")); 
			BigDecimal time_CNT_MS = CrsUtil.parseBigDecimal( MapUtils.getString(map_time, "CNT_MS"));      
			BigDecimal time_AMT_MS = CrsUtil.parseBigDecimal((BigDecimal) MapUtils.getObject(map_time, "AMT_MS"));                             
			BigDecimal time_CNT_O = CrsUtil.parseBigDecimal( MapUtils.getString(map_time, "CNT_O"));       
			BigDecimal time_AMT_O = CrsUtil.parseBigDecimal((BigDecimal) MapUtils.getObject(map_time, "AMT_O"));
			BigDecimal time_CNT_MSO = time_CNT_MS.add(time_CNT_O);
			BigDecimal time_AMT_MSO = null;
			//月初, 當月可能尚無資料 => CrsUtil.parseBigDecimal(...)
			BigDecimal mm_CNT_MS = CrsUtil.parseBigDecimal( MapUtils.getString(map_mm, "CNT_MS"));       
			BigDecimal mm_AMT_MS = CrsUtil.parseBigDecimal((BigDecimal) MapUtils.getObject(map_mm, "AMT_MS"));
			BigDecimal mm_CNT_O = CrsUtil.parseBigDecimal( MapUtils.getString(map_mm, "CNT_O"));
			BigDecimal mm_AMT_O = CrsUtil.parseBigDecimal((BigDecimal) MapUtils.getObject(map_mm, "AMT_O"));
			BigDecimal mm_CNT_MSO = mm_CNT_MS.add(mm_CNT_O);
			BigDecimal mm_AMT_MSO = null; 
			if(true){
				//轉換為千元單位
				time_AMT_MS = Arithmetic.div(time_AMT_MS, unit, scale);
				time_AMT_O = Arithmetic.div(time_AMT_O, unit, scale);	
				
				mm_AMT_MS = Arithmetic.div(mm_AMT_MS, unit, scale);
				mm_AMT_O = Arithmetic.div(mm_AMT_O, unit, scale);
				
				time_AMT_MSO = time_AMT_MS.add(time_AMT_O);
				mm_AMT_MSO = mm_AMT_MS.add(mm_AMT_O); 
			}
			
			//===========
			OTS_CRDLN031 model = new OTS_CRDLN031();
			model.setCyc_dt(date);
			model.setArea_cd(BRSETNO);
			model.setBr_cd(BRNO);
			model.setProj_id(COMPANYID5);
			model.setTx_cnt(mm_CNT_MSO);
			model.setTx_amount(mm_AMT_MSO);
			model.setAccum_tx_cnt(time_CNT_MSO);
			model.setAccum_tx_amount(time_AMT_MSO);
			//~~~
			list_OTS_CRDLN031.add(model);
		}
		
		
		
		if(list_OTS_CRDLN031.size()>0){
			MISRows<OTS_CRDLN031> beans = new MISRows<OTS_CRDLN031>(OTS_CRDLN031.class);
			beans.setValues(list_OTS_CRDLN031);
			upDwToServer(beans, "DWADM");
		}
	}
	
	private void delOTS_CRDLN031(String companyId5, Date date){
		dwdbBASEService.delete(new Object[] { "DWADM.OTS_CRDLN031", "cyc_dt =? and proj_id=?" }
													, new String[] { Util.trim(TWNDate.toAD(date)), companyId5 });
	}
	
	@SuppressWarnings("unchecked")
	private void upC160S01D_ELF675(Date dateBeg, Date dateEnd, String oid){
		List<C160S01D> list = new ArrayList<C160S01D>();
		if(true){
			ISearch search = clsService.getMetaSearch();
			search.addSearchModeParameters(SearchMode.BETWEEN, "lnFromDate", new Object[]{dateBeg, dateEnd});
			search.addSearchModeParameters(SearchMode.EQUALS, "misflag", "Y"); //補上傳
			if(Util.isNotEmpty(oid)){
				search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
			}
			search.setMaxResults(Integer.MAX_VALUE);
			Page<? extends GenericBean> page = clsService.findPage(C160S01D.class, search);
			list = (List<C160S01D>) page.getContent();
		}
		
		if(true){
			List<ELF675> ELF675List = new ArrayList<ELF675>();
			String subjCode3 = "321"; //只在補上傳時, 正常應抓 prodservice.getSubject8to3(c160m01f.getSubjCode());
			for (C160S01D c160s01d : list) {
				ELF675List = upELF675Case3(ELF675List, c160s01d, subjCode3);
			}
			
			if(ELF675List.size() > 0){
				MISRows<ELF675> misRows675 = new MISRows<ELF675>(ELF675.class);
				misRows675.setValues(ELF675List);
				upMisToServer(misRows675, "MIS");
			}			
		}		
	}
	
	private List<ELF675> upELF675Case3(List<ELF675> data, C160S01D c160s01d, String subjCode3) {
		String mainId = c160s01d.getMainId();
		String cntrNo = Util.trim(c160s01d.getCntrNo());
		String custId = c160s01d.getCustId().substring(0, 10);
		String dupNo = c160s01d.getCustId().substring(10);
		C120S01Q c120s01q = clsService.findC120S01Q(mainId, custId, dupNo);
		if(c120s01q==null){
			return data;
		}
		//==========================
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		int l140s02a_seq = 1;
		String ACCT_KEY = "";
		ACCT_KEY = Util.isEmpty(ACCT_KEY) ? Util.addZeroWithValue(l140s02a_seq, 5) : ACCT_KEY;
		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		
		Date jcicDate = null;
		BigDecimal BASE_A = null;
		BigDecimal BASE_B = null;
		BigDecimal BASE_S = null;
		BigDecimal BASE_SCORE = null;
		BigDecimal TOTAL_SCORE = null;
		BigDecimal INITIAL_SCORE = null;
		BigDecimal PREDICT_BAD_RATE = null;
		Integer INITIAL_RATING = null;
		Integer FINAL_RATING = null;
		String JCIC_WARNING_FLAG = null;
		BigDecimal DR = null;
		BigDecimal DR_1YR = null;

		Timestamp nowTS = CapDate.getCurrentTimestamp();
		String c_flag = "";
		if (true) {
			if (!Util.isEmpty(c120s01q.getVarVer())) {
				String VarVer[] = c120s01q.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01q.getJcicQDate();
			c_flag = ClsScoreUtil.upDW_column_C_FLAG(c120s01q);
			
			BASE_A = c120s01q.getVarA();
			BASE_B = c120s01q.getVarB();
			BASE_S = c120s01q.getVarC();
			BASE_SCORE = c120s01q.getScrNum12();
			TOTAL_SCORE = c120s01q.getScrNum11();
			INITIAL_SCORE = c120s01q.getScrNum13();
			PREDICT_BAD_RATE = c120s01q.getPd();
			INITIAL_RATING = Util.parseInt(c120s01q.getGrade1());
			FINAL_RATING = Util.parseInt(c120s01q.getGrade3());
			JCIC_WARNING_FLAG = "N";
			if (Util.equals(Util.nullToSpace(c120s01q.getChkItem1()), "Y")
					|| Util.equals(Util.nullToSpace(c120s01q.getChkItem2()),
							"Y")
					|| Util.equals(Util.nullToSpace(c120s01q.getChkItem3()),
							"Y")
					|| Util.equals(Util.nullToSpace(c120s01q.getChkItem4()),
							"Y")
					|| Util.equals(Util.nullToSpace(c120s01q.getChkItem5()),
							"Y")
					|| Util.equals(Util.nullToSpace(c120s01q.getChkItem6()),
							"Y")
					|| Util.equals(Util.nullToSpace(c120s01q.getChkItem7()),
							"Y")
					|| Util.equals(Util.nullToSpace(c120s01q.getChkItem8()),
							"Y")) {
				JCIC_WARNING_FLAG = "Y";
			}

//			String subjCode3 = Util.trim(prodService.getSubject8to3(c160m01f.getSubjCode()));
			boolean isShortPeriodCase = subjCode3.startsWith("1")
					|| subjCode3.startsWith("2");

			DR = isShortPeriodCase ? c120s01q.getDr_2YR() : c120s01q
					.getDr_3YR();
			DR_1YR = isShortPeriodCase ? c120s01q.getDr_1YR_S() : c120s01q
					.getDr_1YR_L();
		}

		String CUST_KEY = c120s01q.getCustId();
		String FINAL_RATING_FLAG = "Y"; //在整批貸款時
		
		ELF675 elf675 = new ELF675();
		elf675.setElf675_contract(cntrNo);
		elf675.setElf675_brn(c120s01q.getOwnBrId());
		elf675.setElf675_noteid(Util.trim(c160s01d.getOid())); //ref upDW_RKSCORE(...)			
		elf675.setElf675_custid(c120s01q.getCustId());
		elf675.setElf675_dupno(c120s01q.getDupNo());
		elf675.setElf675_mowtype("N");
		elf675.setElf675_mowver1(MOWVER1);
		elf675.setElf675_mowver2(MOWVER2);
		elf675.setElf675_jcic_date(jcicDate);
		elf675.setElf675_loan_no(ACCT_KEY);
		elf675.setElf675_cust_key(CUST_KEY);
		elf675.setElf675_lngeflag("M");
		elf675.setElf675_base_a(BASE_A);
		elf675.setElf675_base_b(BASE_B);
		elf675.setElf675_base_s(BASE_S);
		elf675.setElf675_base_score(BASE_SCORE);
		elf675.setElf675_total_score(TOTAL_SCORE);
		elf675.setElf675_init_score(INITIAL_SCORE);
		elf675.setElf675_predict_rat(PREDICT_BAD_RATE);
		elf675.setElf675_init_rating(INITIAL_RATING);
		elf675.setElf675_adj_rating(INITIAL_RATING - FINAL_RATING);
		elf675.setElf675_final_rate(FINAL_RATING);
		elf675.setElf675_jcic_warnfg(JCIC_WARNING_FLAG);
		elf675.setElf675_final_ratfg(FINAL_RATING_FLAG);
		elf675.setElf675_del_reason("");
		elf675.setElf675_reject_txt("");
		elf675.setElf675_docstatus("3"); //參考 LMSServiceImpl :: DwDOCSTATUS(...)
		elf675.setElf675_dr(DR);
		elf675.setElf675_dr_1yr(DR_1YR);
		elf675.setElf675_data_src_dt(nowTS);
		elf675.setElf675_updater(user.getUserId());
		elf675.setElf675_tmestamp(nowTS);
		elf675.setElf675_c_flag(c_flag);
		elf675.setElf675_coll_house("N");
		
		data.add(elf675);
		//==========================
		return data;
	}

	private <T> void upMisToServer(MISRows<T> misRows, String schemaName) {
		if (Util.isNotEmpty(misRows.getKeyValues())) {
			int DelCount = misdbBASEService.delete(
					misRows.getKeyMsgFmtParam(schemaName),
					misRows.getKeyValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Delete:"
					+ DelCount);
			misdbBASEService.insert(misRows.getMsgFmtParam(schemaName),
					misRows.getTypes(), misRows.getValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Insert");
		}
	}
	
	private <T> void upDwToServer(MISRows<T> misRows, String TableType) {
		if (!Util.isEmpty(misRows.getKeyValues())) {
			int DelCount = dwdbBASEService.delete(
					misRows.getKeyMsgFmtParam(TableType),
					misRows.getKeyValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Delete:"
					+ DelCount);
			dwdbBASEService.insert(misRows.getMsgFmtParam(TableType),
					misRows.getTypes(), misRows.getValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Insert");
		}
	}
	
	/**
	 * @param raw_localPath 若傳入參數為 null, 則抓程式預設路徑
	 * @param fileLs
	 * @return
	 * @throws IOException
	 */
	private String getUCB1FTPFile_common(String raw_localPath, String[] fileLs)
	throws IOException{
		String localPath = raw_localPath;
		if(Util.isEmpty(localPath)){
			String file_separator = "/";			
			
			localPath = PropUtil.getProperty("docFile.dir") //docFile.dir=/elnfs
				+file_separator+PropUtil.getProperty("systemId") //systemId=LMS
				+file_separator+"DWNLOAD"
				+file_separator;
		}
		if(true){			
			File tempFilePath = new File(localPath);
			if (!tempFilePath.exists()) {
				FileUtils.forceMkdir(tempFilePath);
			}
		}
		
		//驗證 FTP 連線，若失敗，會拋出 GWException
		dwUCB1FTPClient.test();

		
		boolean isBinaryFile = true;
		String msgId = IDGenerator.getUUID();
		for(String fn: fileLs){
			String localFilePath = localPath+fn;
			dwUCB1FTPClient.download(msgId, fn, localFilePath, isBinaryFile, null);			
		}
		
		return localPath;
	}
	/*
	  	http://127.0.0.1:9081/lms-web/app/scheduler?input={'serviceId':'crsBatchServiceImpl','request':{act:'procFtpFile', 'fn':'IMWM0017.D'}}
	   	存放於 /elnfs/LMS/DWNLOAD/
	*/
	private void procFtpFile_IMWM0017(String forceRedo)
	throws Exception{
		String fn_D = "IMWM0017.D";
		String fn_H = "IMWM0017.H";
		String localPath = getUCB1FTPFile_common(null, new String[]{fn_H, fn_D});
		
		C900M03A fptH = parseFtpH(new File(localPath+fn_H), "Big5");	
		if(fptH==null){
			throw new Exception("ftp_H["+fn_H+"]==null");
		}
		if(!Util.equals(forceRedo, "Y")){
			C900M03A c900m03a = clsService.findC900M03A_fnGenDateGenTime(fptH.getFn(), fptH.getGenDate(), fptH.getGenTime());
			if(c900m03a!=null && c900m03a.getGenCnt()!=null && fptH.getGenCnt()!=null && c900m03a.getGenCnt().compareTo(fptH.getGenCnt())==0){
				logger.info("c900m03a["+fptH.getFn()+", "+fptH.getGenDate()+", "+fptH.getGenTime()+"],cnt="+fptH.getGenCnt()+", already EXIST");
				return;
			}
		}
		
		eloandbBASEService.delete_C900M03A_dtl(fptH.getFn(), fptH.getGenDate(), fptH.getGenTime());
		
		int idx = 0;
		int cnt_data_list = 0;
		if(true){
			List<String> data_list = getLines(new File(localPath+fn_D), "Big5");
			List<Object[]> batchValues = new ArrayList<Object[]>();	
			//===
			cnt_data_list = data_list.size();				
			for(String data_line: data_list){
				++idx;
				//~~~
				String idDup = Util.trim(StringUtils.substring(data_line, 4, 4+11));
				
				Object[] o = new Object[9];
				//===
				o[0] = fptH.getFn();
				o[1] = fptH.getGenDate();
				o[2] = fptH.getGenTime();
				o[3] = new BigDecimal(idx);
				o[4] = StringUtils.substring(data_line, 0, 0+3);
				o[5] = StringUtils.substring(idDup, 0, 10);
				o[6] = StringUtils.substring(idDup, 10, 11);
				o[7] = StringUtils.substring(data_line, 16, 16+2);
				o[8] = StringUtils.substring(data_line, 19, 19+2);
				//===
				batchValues.add(o);
				
				//~~~
				if(idx%500==0 || idx==cnt_data_list){
					eloandbBASEService.batchInsert_C900M03A_dtl(fptH.getFn(), batchValues);
					batchValues = new ArrayList<Object[]>();	
				}
			}
			//===
		}
		
		if(true){ //筆數勾稽
			int cnt_h = fptH.getGenCnt().intValue();
			int cnt_d = clsService.count_C900M03A_dtl(fn_D, fptH.getGenDate(), fptH.getGenTime());
			
			if(cnt_h!=cnt_d){
				throw new Exception("["+fn_D+","+fptH.getGenDate()+","+fptH.getGenTime()+"]cnt_h="+cnt_h+", cnt_d="+cnt_d);
			}
		}
		
		if(true){ // 寫入 C900M03A
			C900M03A c900m03a = clsService.findC900M03A_fnGenDateGenTime(fptH.getFn(), fptH.getGenDate(), fptH.getGenTime());
			if(c900m03a==null){
				c900m03a = new C900M03A();
				c900m03a.setFn(fptH.getFn());
				c900m03a.setGenDate(fptH.getGenDate());
				c900m03a.setGenTime(fptH.getGenTime());
			}
			c900m03a.setGenCnt(fptH.getGenCnt());
			clsService.save(c900m03a);
		}		
	}
	
	/*
	  	http://127.0.0.1:9081/lms-web/app/scheduler?input={'serviceId':'crsBatchServiceImpl','request':{act:'procFtpFile', 'fn':'IDWM0002.D'}}
	   	存放於 /elnfs/LMS/DWNLOAD/
	*/
	private void procFtpFile_IDWM0002(String forceRedo)
	throws Exception{
		String fn_D = "IDWM0002.D";
		String fn_H = "IDWM0002.H";
		String localPath = getUCB1FTPFile_common(null, new String[]{fn_H, fn_D});
		
		C900M03A fptH = parseFtpH(new File(localPath+fn_H), "Big5");	
		if(fptH==null){
			throw new Exception("ftp_H["+fn_H+"]==null");
		}
		if(!Util.equals(forceRedo, "Y")){
			C900M03A c900m03a = clsService.findC900M03A_fnGenDateGenTime(fptH.getFn(), fptH.getGenDate(), fptH.getGenTime());
			if(c900m03a!=null && c900m03a.getGenCnt()!=null && fptH.getGenCnt()!=null && c900m03a.getGenCnt().compareTo(fptH.getGenCnt())==0){
				logger.info("c900m03a["+fptH.getFn()+", "+fptH.getGenDate()+", "+fptH.getGenTime()+"],cnt="+fptH.getGenCnt()+", already EXIST");
				return;
			}
		}
		
		eloandbBASEService.delete_C900M03A_dtl(fptH.getFn(), fptH.getGenDate(), fptH.getGenTime());
		
		int idx = 0;
		int cnt_data_list = 0;
		if(true){
			List<String> data_list = getLines(new File(localPath+fn_D), "Big5");
			List<Object[]> batchValues = new ArrayList<Object[]>();	
			//===
			cnt_data_list = data_list.size();				
			for(String data_line: data_list){
				++idx;
				//~~~
				String idDup = Util.trim(StringUtils.substring(data_line, 0, 0+11));
				
				String part_line = Util.trim(StringUtils.substring(data_line, 16));
				String[] part = StringUtils.split(part_line, ",");
				String gra_cd = "";
				String gra_name = "";
				String ao_cd = "";
				String ao_name = "";
				if(part!=null && part.length==4){
					gra_cd = part[0];
					gra_name = part[1];
					ao_cd = part[2];
					ao_name = part[3];
				}else if(part!=null && part.length==2 && part_line.endsWith(",,")){
					/*
					資料可能是 "30,貴賓客戶,,"
					*/							
					gra_cd = part[0];
					gra_name = part[1];
				}else if(part!=null && part.length==3 && part_line.endsWith(",")){
					/*
					資料可能是 "30,貴賓客戶,091088,"
					*/							
					gra_cd = part[0];
					gra_name = part[1];
					ao_cd = part[2];
				}else{
					// invalidFmt
					if(Util.isEmpty(data_line)){
						logger.info("SKIP["+fn_D+"]"+idx+",data_line=empty");
						continue;
					}
					
					throw new Exception("["+fn_D+"]"+idx+"/"+cnt_data_list+"]part.length="+part.length+"\t\tpart=["+part_line+"],data_line=["+data_line+"]");
				}
				
				
				Object[] o = new Object[11];
				//===
				o[0] = fptH.getFn();
				o[1] = fptH.getGenDate();
				o[2] = fptH.getGenTime();
				o[3] = new BigDecimal(idx);
				o[4] = StringUtils.substring(idDup, 0, 10);
				o[5] = StringUtils.substring(idDup, 10, 11);
				o[6] = StringUtils.substring(data_line, 12, 12+3);
				o[7] = gra_cd;
				o[8] = gra_name;
				o[9] = ao_cd;
				o[10] = ao_name;
				//===
				batchValues.add(o);
				
				//~~~
				if(idx%500==0 || idx==cnt_data_list){
					eloandbBASEService.batchInsert_C900M03A_dtl(fptH.getFn(), batchValues);
					batchValues = new ArrayList<Object[]>();	
				}
			}
			//===
		}
		
		if(true){ //筆數勾稽
			int cnt_h = fptH.getGenCnt().intValue();
			int cnt_d = clsService.count_C900M03A_dtl(fn_D, fptH.getGenDate(), fptH.getGenTime());
			
			if(cnt_h!=cnt_d){
				throw new Exception("["+fn_D+","+fptH.getGenDate()+","+fptH.getGenTime()+"]cnt_h="+cnt_h+", cnt_d="+cnt_d);
			}
		}
		
		if(true){ // 寫入 C900M03A
			C900M03A c900m03a = clsService.findC900M03A_fnGenDateGenTime(fptH.getFn(), fptH.getGenDate(), fptH.getGenTime());
			if(c900m03a==null){
				c900m03a = new C900M03A();
				c900m03a.setFn(fptH.getFn());
				c900m03a.setGenDate(fptH.getGenDate());
				c900m03a.setGenTime(fptH.getGenTime());
			}
			c900m03a.setGenCnt(fptH.getGenCnt());
			clsService.save(c900m03a);
		}		
	}
	
	private C900M03A parseFtpH(File file, String encoding) 
	throws IOException{
		List<String> data_list = getLines(file, encoding);	
		if(data_list.size()==0){
			return null;
		}
		
		C900M03A c900m03a = new C900M03A();
		String raw_str = data_list.get(0);
		if(true){
			/*
			2018101620181016IDWM0002.D20181017012146000107674 --- 0002
			2018091720180917IMWM0017.D20180918010027000001832 --- 0017
			*/
			String fn = Util.trim(StringUtils.substring(raw_str, 16, 16+10));
			String genDate = Util.trim(StringUtils.substring(raw_str, 26, 26+8));
			String genTime = Util.trim(StringUtils.substring(raw_str, 34, 34+6));
			String str_cnt = Util.trim(StringUtils.substring(raw_str, 40, 40+9));
			BigDecimal genCnt = Util.parseBigDecimal(str_cnt);
			if(genCnt==null){
				genCnt = BigDecimal.ZERO;	
			}			
			//~~~~~~
			c900m03a.setFn(fn);
			c900m03a.setGenDate(genDate);
			c900m03a.setGenTime(genTime);
			c900m03a.setGenCnt(genCnt);
		}		
		return c900m03a;
	}
	
	private void deleteFtpRecord(String fn_D, int keepDays){
		C900M03A max_genDate = clsService.findC900M03A_fn_maxGenDate(fn_D);
		if(max_genDate==null){
			return;
		}
		
		String dval = CapDate.shiftDaysString(max_genDate.getGenDate(), "yyyyMMdd", -1*keepDays);	
		for(C900M03A c900m03a: clsService.findC900M03A_fn_bfGenDate(fn_D, dval)){			
			eloandbBASEService.delete_C900M03A_dtl(c900m03a.getFn(), c900m03a.getGenDate(), c900m03a.getGenTime());			
			clsService.daoDelete(c900m03a);
		}
	}
	
	private List<String> getLines(File file, String charSet) 
	throws IOException {
		List<String> r = new ArrayList<String>();
		try {
		    Scanner scanner = new Scanner(file, charSet);

		    //now read the file line by line...
		   // int lineNum = 0;
		    while (scanner.hasNextLine()) {
			String line = scanner.nextLine();
			//lineNum++;
			    r.add(line);
			
		    }
		} catch(FileNotFoundException e) { 
		    //handle this
		}
		return r;
	}
	
	private void dw_fixData(String param_brCd, String param_noteId, String param_cntrN){		
		for(Map<String, Object> rowmap: dwdbBASEService.find_fixData(param_brCd, param_noteId, param_cntrN)){
			String BR_CD = Util.trim(MapUtils.getString(rowmap, "BR_CD"));
			String NOTEID = Util.trim(MapUtils.getString(rowmap, "NOTEID"));
			String CUSTID = Util.trim(MapUtils.getString(rowmap, "CUSTID"));
			String DUPNO = Util.trim(MapUtils.getString(rowmap, "DUPNO"));
			String MOWTYPE = Util.trim(MapUtils.getString(rowmap, "MOWTYPE"));
			String MOWVER1 = Util.trim(MapUtils.getString(rowmap, "MOWVER1"));
			String MOWVER2 = Util.trim(MapUtils.getString(rowmap, "MOWVER2"));
			String JCIC_DATE = TWNDate.toAD((Date)MapUtils.getObject(rowmap, "JCIC_DATE"));
			String CNTRNO = Util.trim(MapUtils.getString(rowmap, "CNTRNO"));
			String ACCT_KEY = Util.trim(MapUtils.getString(rowmap, "ACCT_KEY"));
			//=========
			Integer LNMONTH = MapUtils.getInteger(rowmap, "LNMONTH");
			
			Integer NEW_LNMONTH = LNMONTH % 12;  
			
			dwdbBASEService.run_fixData(BR_CD, NOTEID, CUSTID, DUPNO, MOWTYPE, MOWVER1, MOWVER2, JCIC_DATE, CNTRNO, ACCT_KEY, NEW_LNMONTH);
			logger.info("dw_fixData["+BR_CD+", "+NOTEID+", "+CUSTID+", "+DUPNO+", "+MOWTYPE+", "+MOWVER1+", "+MOWVER2+", "+JCIC_DATE+", "+CNTRNO+", "+ACCT_KEY+"] from ["+LNMONTH+"] to ["+NEW_LNMONTH+"]");
		}			
	}
	
	private void dw_fixData_DATA_SRC_DT__null(String[] noteId_arr){
		String[] tofix_table_arr = {"DWADM.DW_RKCREDIT"};
		for(String noteId: noteId_arr){
			Date DATA_SRC_DT = null;
			if(true){
				for(Map<String, Object> map : dwdbBASEService.find_clsScoreData_by_noteId("DWADM.DW_RKSCORE", noteId)){
					Object raw = MapUtils.getObject(map, "DATA_SRC_DT");
					if(raw instanceof Date){
						DATA_SRC_DT = (Date)raw;
						break;
					}				
				}
			}
			if(DATA_SRC_DT==null){				
				for(Map<String, Object> map :dwdbBASEService.find_clsScoreData_by_noteId("DWADM.DW_RKCREDIT", noteId)){
					Object raw = MapUtils.getObject(map, "JCIC_DATE");
					if(raw instanceof Date){
						DATA_SRC_DT = (Date)raw;
						break;
					}				
				}
			}
			if(DATA_SRC_DT!=null){
				for(String targetTable: tofix_table_arr){
					String newDATA_SRC_DT = TWNDate.toAD(DATA_SRC_DT);
					dwdbBASEService.dw_fixData_DATA_SRC_DT__null(targetTable, noteId, newDATA_SRC_DT);
					logger.info("dw_fixData_DATA_SRC_DT__null[table="+targetTable+"][noteId="+noteId+"], DATA_SRC_DT from [null] to ["+newDATA_SRC_DT+"]");	
				}				
			}				
		}		
	}

	private void dw_fixData_OTS_RKCREDITOVS_CHKDATE_CHKEMPNO(String param_brCd, String param_noteId, String param_cntrNo){		
		for(Map<String, Object> rowmap: dwdbBASEService.find_fixData_OTS_RKCREDITOVS_CHKDATE_CHKEMPNO(param_brCd, param_noteId, param_cntrNo)){
			String BR_CD = Util.trim(MapUtils.getString(rowmap, "BR_CD"));
			String NOTEID = Util.trim(MapUtils.getString(rowmap, "NOTEID"));
			String RATING_DATE = TWNDate.toAD((Date)MapUtils.getObject(rowmap, "RATING_DATE"));
			String RATING_ID = Util.trim(MapUtils.getString(rowmap, "RATING_ID"));
			String CUSTID = Util.trim(MapUtils.getString(rowmap, "CUSTID"));
			String DUPNO = Util.trim(MapUtils.getString(rowmap, "DUPNO"));
			String CUST_KEY = Util.trim(MapUtils.getString(rowmap, "CUST_KEY"));
			String LOAN_CODE = Util.trim(MapUtils.getString(rowmap, "LOAN_CODE"));
			String MOWTYPE = Util.trim(MapUtils.getString(rowmap, "MOWTYPE"));
			String MOWVER1 = Util.trim(MapUtils.getString(rowmap, "MOWVER1"));
			String MOWVER2 = Util.trim(MapUtils.getString(rowmap, "MOWVER2"));
			//=========
			C121M01A c121m01a = clsService.findC121M01AByMainId(RATING_ID);
			if(c121m01a==null){
				continue;
			}
			String desc = "dw_fixData_OTS_RKCREDITOVS_CHKDATE_CHKEMPNO["+BR_CD+", "+NOTEID+", "+RATING_DATE+", "+RATING_ID+", "+CUSTID+", "+DUPNO+", "+CUST_KEY+", "+LOAN_CODE+", "+MOWTYPE+", "+MOWVER1+", "+MOWVER2+"]";
			String c121_RATING_DATE = TWNDate.toAD(c121m01a.getRatingDate());
			String c121_ApproveTime = Util.trim(TWNDate.toAD(c121m01a.getApproveTime()));
			String c121_approver = Util.trim(c121m01a.getApprover());
			if(Util.equals(CreditDocStatusEnum.海外_已核准.getCode(), c121m01a.getDocStatus()) && c121m01a.getApproveTime()!=null && Util.equals(c121_RATING_DATE, RATING_DATE)){
				
				dwdbBASEService.run_fixData_OTS_RKCREDITOVS_CHKDATE_CHKEMPNO(BR_CD, NOTEID, RATING_DATE, RATING_ID, CUSTID, DUPNO, CUST_KEY, LOAN_CODE, MOWTYPE, MOWVER1, MOWVER2
						, c121_ApproveTime, c121_approver);
				logger.info(desc+", fix_data_with[docStatus="+c121m01a.getDocStatus()+", approveTime="+c121_ApproveTime+", approver="+c121_approver+", c121_RATING_DATE="+c121_RATING_DATE+"]");
			}else{
				logger.info(desc+", c121m01a_error[docStatus="+c121m01a.getDocStatus()+", approveTime="+c121_ApproveTime+", approver="+c121_approver+", c121_RATING_DATE="+c121_RATING_DATE+"]");		
			}
		}			
	}

	
	private void run_ELF490B(String beg_yyyyMMdd, String end_yyyyMMdd, String[] brnGroupArr){
		String elf490b_data_ym = StringUtils.substring(end_yyyyMMdd, 0, 7);

		//======================
		//處理 elf490b_flag = "1";
		if(true){
			String elf490b_flag = "1";
			for(String brnGroup : brnGroupArr){
				String  brno_area = CrsUtil.convert_ELF339_BRNO_AREA(brnGroup); //把 931 轉成 1
				List<ELF490B> elf490b_list = new ArrayList<ELF490B>();	
				//==========================
				List<Map<String, Object>> result_Rule1 = misdbBASEService.get_ELF490B_Rule1(brno_area, beg_yyyyMMdd, end_yyyyMMdd);
				List<Map<String, Object>> result_Rule2 = misdbBASEService.get_ELF490B_Rule2(brno_area, beg_yyyyMMdd, end_yyyyMMdd);
				List<Map<String, Object>> result_Rule3 = misdbBASEService.get_ELF490B_Rule3(brno_area, TWNDate.toAD(CapDate.addMonth(CapDate.parseDate(StringUtils.substring(end_yyyyMMdd, 0, 7)+"-01"), -11)),  end_yyyyMMdd);
				List<Map<String, Object>> result_Rule4 = misdbBASEService.get_ELF490B_Rule4(brno_area, beg_yyyyMMdd, end_yyyyMMdd);
				List<Map<String, Object>> result_Rule5 = misdbBASEService.get_ELF490B_Rule5(brno_area, beg_yyyyMMdd, end_yyyyMMdd);
				List<Map<String, Object>> result_Rule6 = misdbBASEService.get_ELF490B_Rule6(brno_area, beg_yyyyMMdd, end_yyyyMMdd);
				//==========================
				
				rs_ELF490B(elf490b_list, brnGroup, result_Rule1, elf490b_data_ym, elf490b_flag);
				rs_ELF490B(elf490b_list, brnGroup, result_Rule2, elf490b_data_ym, elf490b_flag);			
				rs_ELF490B(elf490b_list, brnGroup, result_Rule3, elf490b_data_ym, elf490b_flag);			
				rs_ELF490B(elf490b_list, brnGroup, result_Rule4, elf490b_data_ym, elf490b_flag);
				rs_ELF490B(elf490b_list, brnGroup, result_Rule5, elf490b_data_ym, elf490b_flag);								
				rs_ELF490B(elf490b_list, brnGroup, result_Rule6, elf490b_data_ym, elf490b_flag);
				//==========================
				if(elf490b_list.size()>0){				
					MISRows<ELF490B> mis_elf490B = new MISRows<ELF490B>(ELF490B.class);
					mis_elf490B.setValues(elf490b_list);
					upMisToServer(mis_elf490B, "MIS");
				}
			}
		}

		//======================
		//處理 elf490b_flag = "2";
		if(true){
			List<Map<String, Object>> result_BrNoAndEmpNo = misdbBASEService.get_ELF490B_findBrNoAndEmpNo(beg_yyyyMMdd, end_yyyyMMdd, elf490b_data_ym);
			String elf490b_flag = "2";
			List<ELF490B> elf490b_list = new ArrayList<ELF490B>();
			String[] areaNoArr = CrsUtil.get_ELF490B_areaArr();
			for(Map<String, Object> data: result_BrNoAndEmpNo){
				String elf339_brNo = Util.trim(MapUtils.getString(data, "ELF339_BRNO"));
				String brnGroup = Util.trim(MapUtils.getString(data, "BRNGROUP"));
				String empNo = Util.trim(MapUtils.getString(data, "EMPNO"));
				//======
				if(CrsUtil.inCollection(brnGroup, areaNoArr)){
					
				}else{
					//007及201不做專案覆審(要的話, 會由另一獨立單位去查)
					continue;
				}
				
				ELF490B elf490b = new ELF490B();
				elf490b.setElf490b_data_ym(elf490b_data_ym);
				elf490b.setElf490b_brno(elf339_brNo);
				elf490b.setElf490b_flag(elf490b_flag);
				elf490b.setElf490b_rule_no("");
				elf490b.setElf490b_emp_no(empNo);
				elf490b.setElf490b_count(1);
				elf490b_list.add(elf490b);
			}
			if(elf490b_list.size()>0){				
				MISRows<ELF490B> mis_elf490B = new MISRows<ELF490B>(ELF490B.class);
				mis_elf490B.setValues(elf490b_list);
				upMisToServer(mis_elf490B, "MIS");
			}
		}
		//======================
		//處理 elf490b_flag = "3"; (區分R1、R2、R3的前三大「承辦行員」)
		if(true){
			Map<String, TreeSet<String>> elf490b_flag2_data = new HashMap<String, TreeSet<String>>();
			if (true) {
				String elf490b_flag = "2";
				for(ELF490B elf490b : misELF490BService.selBy_dataym_flag(elf490b_data_ym, elf490b_flag)){
					String brNo = Util.trim(elf490b.getElf490b_brno());
					String empNo = Util.trim(elf490b.getElf490b_emp_no());

					if (!elf490b_flag2_data.containsKey(brNo)) {
						elf490b_flag2_data.put(brNo, new TreeSet<String>());
					}
					elf490b_flag2_data.get(brNo).add(empNo);
				}
			}
			
			Map<String, Set<String>> map_elf490b_flag1_empNo_matchRule = new HashMap<String, Set<String>>();
			if (true) {
				String elf490b_flag = "1";
				for(ELF490B elf490b : misELF490BService.selBy_dataym_flag(elf490b_data_ym, elf490b_flag)){
					String ruleNo = Util.trim(elf490b.getElf490b_rule_no());
					String empNo = Util.trim(elf490b.getElf490b_emp_no());

					if (!map_elf490b_flag1_empNo_matchRule.containsKey(empNo)) {
						map_elf490b_flag1_empNo_matchRule.put(empNo, new TreeSet<String>());
					}
					map_elf490b_flag1_empNo_matchRule.get(empNo).add(ruleNo);
				}
			}
			

			String elf490b_flag = "3";
			int cnt_for_top3 = CrsUtil.get_ELF490B_cnt_for_top3();
			int cnt_default = 1;
			List<ELF490B> elf490b_list = new ArrayList<ELF490B>();
			for (String areaNo : CrsUtil.get_ELF490B_areaArr()) {				
				TreeMap<String, String> brMap = retrialService.getBranch(areaNo);	
				/*
				  每一個營運中心, R1、R2、R3的前三大「承辦行員」案件優先覆審	
				 當elf490b_flag=3時
				 ● 前3大行員 : ELF490B_COUNT 借用 999 來區分
				 ● 之外的行員: ELF490B_COUNT 先放 1		  		  
				 */
				Set<String> r1_top3 = proc_mark_match_top3(elf490b_data_ym, areaNo, CrsUtil.DOCKIND_S_R1, brMap.keySet(), elf490b_flag2_data, map_elf490b_flag1_empNo_matchRule);
				Set<String> r2_top3 = proc_mark_match_top3(elf490b_data_ym, areaNo, CrsUtil.DOCKIND_S_R2, brMap.keySet(), elf490b_flag2_data, map_elf490b_flag1_empNo_matchRule);
				Set<String> r3_top3 = proc_mark_match_top3(elf490b_data_ym, areaNo, CrsUtil.DOCKIND_S_R3, brMap.keySet(), elf490b_flag2_data, map_elf490b_flag1_empNo_matchRule);
				
				for (String brNo : brMap.keySet()) {					
					if (elf490b_flag2_data.containsKey(brNo)) {
						for (String empNo : elf490b_flag2_data.get(brNo)) {							
							for(String ruleNo : map_elf490b_flag1_empNo_matchRule.get(empNo)){
								ELF490B elf490b = new ELF490B();
								elf490b.setElf490b_data_ym(elf490b_data_ym);
								elf490b.setElf490b_brno(brNo);
								elf490b.setElf490b_flag(elf490b_flag);
								elf490b.setElf490b_rule_no(ruleNo);
								elf490b.setElf490b_emp_no(empNo);
								if(Util.equals(ruleNo, CrsUtil.DOCKIND_S_R1)){
									elf490b.setElf490b_count(r1_top3.contains(empNo)?cnt_for_top3:cnt_default);
								}else if(Util.equals(ruleNo, CrsUtil.DOCKIND_S_R2)){
									elf490b.setElf490b_count(r2_top3.contains(empNo)?cnt_for_top3:cnt_default);
								}else if(Util.equals(ruleNo, CrsUtil.DOCKIND_S_R3)){
									elf490b.setElf490b_count(r3_top3.contains(empNo)?cnt_for_top3:cnt_default);
								}else{
									elf490b.setElf490b_count(cnt_default);
								}
								elf490b_list.add(elf490b);
							}							
						}
					}
				}
				
			}
			if(elf490b_list.size()>0){				
				MISRows<ELF490B> mis_elf490B = new MISRows<ELF490B>(ELF490B.class);
				mis_elf490B.setValues(elf490b_list);
				upMisToServer(mis_elf490B, "MIS");
			}
		}
	}
	
	/**
	 * 因 R3 抓【近一年內轉催】未結清之逾催案件前10大, 但抓出的行員編號, 可能是10年前的授信人員。在近半年內, 已未承辦授信案件
	 * @param ELF490B_DATA_YM
	 * @param ELF490B_BRNO
	 * @param ELF490B_RULE_NO
	 * @return
	 */
	private LinkedHashSet<String> proc_mark_match_top3(String ELF490B_DATA_YM , String ELF490B_BRNO, String ELF490B_RULE_NO,
			Set<String> brNoUnderArea, Map<String, TreeSet<String>> elf490b_flag2_data, Map<String, Set<String>> map_elf490b_flag1_empNo_matchRule){
		//=================
		//每個營運中心底下有X個分行, 每個分行有Y個行員, 每個行員, 可能符合{R1, R1及R3}
		Set<String> empNoUnderArea = new HashSet<String>();
		Set<String> empNo_matchRule = new HashSet<String>();
		for(String brNo: brNoUnderArea){
			if(elf490b_flag2_data.containsKey(brNo)){
				empNoUnderArea.addAll(elf490b_flag2_data.get(brNo));	
			}				
		}
		for(String empNo: empNoUnderArea){
			Set<String> set_matchRule = map_elf490b_flag1_empNo_matchRule.get(empNo);
			if(set_matchRule.contains(ELF490B_RULE_NO)){
				empNo_matchRule.add(empNo);
			}
		}
		//=================
		LinkedHashSet<String> set = new LinkedHashSet<String>();
		
		for (ELF490B elf490b : misELF490BService.selBy_dataym_flag1_brNo_ruleNo_inOrder(ELF490B_DATA_YM, ELF490B_BRNO, ELF490B_RULE_NO)) {
			//list 已依照 count, empNo 去排序, 但抓出的行員編號, 可能是10年前的授信人員
			//所以還要再去判斷, 該 empNo 在近半年, 有無承辦授信案件
			if(set.size()>=3){
				break;
			}
			String empNo = Util.trim(elf490b.getElf490b_emp_no());
			if(empNo_matchRule.contains(empNo)){
				set.add(empNo);	
			}else{
				//該 empNo 在近半年內, 未承辦授信案件
			}			
		}		
		return set;
	}
	
	private void rs_ELF490B(List<ELF490B> elf490b_list , String brnGroup, List<Map<String, Object>> raw, String elf490b_data_ym, String elf490b_flag){
		for(Map<String, Object> data: raw){
			String rule_no = Util.trim(MapUtils.getString(data, "RULE_NO"));
			String empNo = Util.trim(MapUtils.getString(data, "EMPNO"));
			Integer cnt = MapUtils.getInteger(data, "CNT");
			//======
			ELF490B elf490b = new ELF490B();
			elf490b.setElf490b_data_ym(elf490b_data_ym);
			elf490b.setElf490b_brno(brnGroup);
			elf490b.setElf490b_flag(elf490b_flag);
			elf490b.setElf490b_rule_no(rule_no);
			elf490b.setElf490b_emp_no(empNo);
			elf490b.setElf490b_count(cnt);
			elf490b_list.add(elf490b);
		}
	}

	private boolean proc_GET_DW_LNCUSTREL(String forceRedo, String cyc_mn, String filter_cust_key, String filter_rel_flag)
	throws Exception{
		if(true){
			boolean isFinish = dwdbBASEService.isLnCustRel_done(cyc_mn, CrsUtil.FN_KEY_C900S03C_DONE_CUST_KEY);
			if(!isFinish){
				throw new Exception("[cyc_mn="+cyc_mn+"][DW_LNCUSTREL]DW_isFinish="+isFinish);
			}		
		}
		
		Date dt_cyc_mn = CapDate.parseDate(cyc_mn);
		String fn = CrsUtil.FN_KEY_C900S03C;
		String genDate = StringUtils.replace(cyc_mn, "-", "");
		String genTime="000000";
		
		BigDecimal cnt_dw = dwdbBASEService.countLnCustRel_done_cust_key(cyc_mn, CrsUtil.FN_KEY_C900S03C_DONE_CUST_KEY);
		//======================
		
		if(!Util.equals(forceRedo, "Y")){
			C900M03A c900m03a = clsService.findC900M03A_fnGenDateGenTime(fn, genDate, genTime);
			if(c900m03a!=null && c900m03a.getGenCnt()!=null && cnt_dw!=null && c900m03a.getGenCnt().compareTo(cnt_dw)==0){
				logger.info("c900m03a["+fn+", "+genDate+", "+genTime+"],cnt="+cnt_dw+", already EXIST");
				return false;
			}
		}
		
		eloandbBASEService.delete_C900S03C(cyc_mn);
		
		if(true){
			//rel_flag{地址:1, 電話:2, e-mail:3}
			String[] rel_flagArr = {"1", "2", "3"};
			for(String rel_flag: rel_flagArr){
				int idx = 0;
				int cnt_data_list = 0;
				if(true){
					List<Map<String, Object>> data_list = dwdbBASEService.findLnCustRel_by_rel_flag(cyc_mn,  rel_flag);
					List<Object[]> batchValues = new ArrayList<Object[]>();	
					//===
					cnt_data_list = data_list.size();				
					for(Map<String, Object> map :data_list){
						++idx;
						//~~~
						String cust_key = Util.trim(MapUtils.getString(map, "CUST_KEY"));
						if(Util.equals(cust_key, CrsUtil.FN_KEY_C900S03C_DONE_CUST_KEY)){
							continue;	
						}
						//~~~
						Object[] o = new Object[11];
						//===
						o[0] = dt_cyc_mn;
						o[1] = cust_key;		
						o[2] = Util.trim(MapUtils.getString(map, "CUST_ID"));		
						o[3] = Util.trim(MapUtils.getString(map, "CUST_DUP_NO"));		
						o[4] = Util.trim(MapUtils.getString(map, "REL_FLAG"));		
						o[5] = Util.trim(MapUtils.getString(map, "TEXT"));		
						o[6] = Util.trim(MapUtils.getString(map, "REL_UPD"));		
						o[7] = CrsUtil.parseBigDecimal(MapUtils.getInteger(map, "DUP_CNT", 0));
						o[8] = Util.trim(MapUtils.getString(map, "DW_LST_DATA_SRC"));		
						o[9] = CapDate.parseDate(MapUtils.getString(map, "DW_DATA_SRC_DT"));		
						o[10] = CapDate.parseDate(MapUtils.getString(map, "DW_LST_MNT_DT"));
						//===
						batchValues.add(o);
						
						//~~~
						if(idx%500==0 || idx==cnt_data_list){
							eloandbBASEService.batchInsert_C900S03C(batchValues);
							batchValues = new ArrayList<Object[]>();	
						}
					}
					//===
				}
			}
		}
		
		
		if(true){ //筆數勾稽			
			BigDecimal cnt_eloan = BigDecimal.valueOf(clsService.count_C900M03A_dtl(fn, cyc_mn, ""));
			
			if(cnt_dw==null || cnt_eloan==null || cnt_dw.compareTo(cnt_eloan)!=0){
				throw new Exception("[fn="+fn+", cyc_mn="+cyc_mn+"]cnt_dw="+cnt_dw+", cnt_eloan="+cnt_eloan);
			}
		}	
		
		if(true){ // 寫入 C900M03A
			C900M03A c900m03a = clsService.findC900M03A_fnGenDateGenTime(fn, genDate, genTime);
			if(c900m03a==null){
				c900m03a = new C900M03A();
				c900m03a.setFn(fn);
				c900m03a.setGenDate(genDate);
				c900m03a.setGenTime(genTime);
			}
			c900m03a.setGenCnt(cnt_dw);
			clsService.save(c900m03a);
		}
		
		return true;
	}
	
	private boolean proc_GET_DW_LNCUSTBR(String forceRedo, String cyc_mn)
	throws Exception{
		Date dt_cyc_mn = CapDate.parseDate(cyc_mn);
		String fn = CrsUtil.FN_KEY_C900S03D;

		BigDecimal cnt_dw = dwdbBASEService.countLnCustBr(cyc_mn);
		
		if(!Util.equals(forceRedo, "Y")){
			BigDecimal cnt_eloan = BigDecimal.valueOf(clsService.count_C900M03A_dtl(fn, cyc_mn, ""));
			
			if(cnt_dw!=null && cnt_eloan!=null && cnt_dw.compareTo(cnt_eloan)==0){
				return false;
			}
		}
		
		eloandbBASEService.delete_C900S03D(cyc_mn);

		List<Map<String, Object>> data_list = dwdbBASEService.findLnCustBr(cyc_mn);
		
		if(true){
			int idx = 0;
			int cnt_data_list = data_list.size();
			List<Object[]> batchValues = new ArrayList<Object[]>();	
			//===
			for(Map<String, Object> map :data_list){
				++idx;
				//~~~				
				Object[] o = new Object[3];
				//===
				o[0] = dt_cyc_mn;
				o[1] = Util.trim(MapUtils.getString(map, "CUST_KEY"));
				o[2] = Util.trim(MapUtils.getString(map, "BR_NO"));				
				//===
				batchValues.add(o);
				
				//~~~
				if(idx%500==0 || idx==cnt_data_list){
					eloandbBASEService.batchInsert_C900S03D(batchValues);
					batchValues = new ArrayList<Object[]>();	
				}
			}
		}
		
		if(true){ //筆數勾稽			
			BigDecimal cnt_eloan = BigDecimal.valueOf(clsService.count_C900M03A_dtl(fn, cyc_mn, ""));
			
			if(cnt_dw==null || cnt_eloan==null || cnt_dw.compareTo(cnt_eloan)!=0){
				throw new Exception("[fn="+fn+", cyc_mn="+cyc_mn+"]cnt_dw="+cnt_dw+", cnt_eloan="+cnt_eloan);
			}
		}		
		
		if(true){ // 寫入 C900M03A
		}
		
		return true;
	}
	
	private void proc_GET_MIS_ELF488(String forceRedo, String cyc_mn)
	throws Exception{
		Date dt_cyc_mn = CapDate.parseDate(cyc_mn);
		String fn = CrsUtil.FN_KEY_C900S03E;

		eloandbBASEService.delete_C900S03E(cyc_mn);

		List<Map<String, Object>> data_list = misdbBASEService.get_ELF488_newCust(cyc_mn);
		BigDecimal cnt_mis = BigDecimal.valueOf(data_list.size());
		
		if(true){
			int idx = 0;
			int cnt_data_list = data_list.size();
			List<Object[]> batchValues = new ArrayList<Object[]>();	
			//===
			for(Map<String, Object> map :data_list){
				++idx;				
				//~~~				
				Object[] o = new Object[3];
				String custId = Util.trim(MapUtils.getString(map, "ELF488_CUST_ID"));
				String dupNo = Util.trim(MapUtils.getString(map, "ELF488_DUP_NO"));
				String custKey = LMSUtil.getCustKey_len10custId(custId, Util.equals("0", dupNo)?"":dupNo);
				//===
				o[0] = dt_cyc_mn;
				o[1] = custKey;
				o[2] = Util.trim(MapUtils.getString(map, "BRNO"));
//				o[1] = Util.trim(MapUtils.getString(map, "ELF488_CUST_ID"));
//				o[2] = Util.trim(MapUtils.getString(map, "ELF488_DUP_NO"));				
				//===
				batchValues.add(o);
				
				//~~~
				if(idx%500==0 || idx==cnt_data_list){
					eloandbBASEService.batchInsert_C900S03E(batchValues);
					batchValues = new ArrayList<Object[]>();	
				}
			}
		}
		
		if(true){ //筆數勾稽			
			BigDecimal cnt_eloan = BigDecimal.valueOf(clsService.count_C900M03A_dtl(fn, cyc_mn, ""));
			
			if(cnt_mis==null || cnt_eloan==null || cnt_mis.compareTo(cnt_eloan)!=0){
				throw new Exception("[fn="+fn+", cyc_mn="+cyc_mn+"]cnt_mis="+cnt_mis+", cnt_eloan="+cnt_eloan);
			}
		}		
		
		if(true){ // 寫入 C900M03A
		}
	}
	
	/* ==================================================================================================================
			select e.mainid, brno, rel_flag, text, custId, dupNo, cname, gen_time, chk_result, rel_custId, rel_dupno, rel_cname 
			from lms.c900s02e e left outer join lms.c900s02f f on e.mainid=f.mainId
			where e.cyc_mn='2019-01-01' and e.deletedtime is null
			order by brno, rel_flag, text, e.mainid
	*/
	private void ctl_C900S02E_C900S02F(String cyc_mn)
	throws Exception{
		//===================================================
		//筆數DW資料是否產生
		if(true){  
			boolean isFinish = dwdbBASEService.isLnCustRel_done(cyc_mn, CrsUtil.FN_KEY_C900S03C_DONE_CUST_KEY);
			if(!isFinish){
				throw new Exception("[cyc_mn="+cyc_mn+"][DW_LNCUSTREL]DW_isFinish="+isFinish);
			}		
		}
		//===================================================
		//筆數勾稽
		if(true){
			if(true){ //筆數勾稽 DW_LNCUSTREL vs C900S03C	
				String fn = CrsUtil.FN_KEY_C900S03C;
				
				
				BigDecimal cnt_dw = dwdbBASEService.countLnCustRel_done_cust_key(cyc_mn, CrsUtil.FN_KEY_C900S03C_DONE_CUST_KEY);
				//~~~~~~
				BigDecimal cnt_eloan = BigDecimal.valueOf(clsService.count_C900M03A_dtl(fn, cyc_mn, ""));
				
				if(cnt_dw==null || cnt_eloan==null || cnt_dw.compareTo(cnt_eloan)!=0){
					throw new Exception("check_DW_LNCUSTREL_C900S03C[cyc_mn="+cyc_mn+"]cnt_dw="+cnt_dw+", cnt_eloan="+cnt_eloan);
				}			
			}
			if(true){ //筆數勾稽 DW_LNCUSTBR vs C900S03D	
				String fn = CrsUtil.FN_KEY_C900S03D;
				
				
				BigDecimal cnt_dw = dwdbBASEService.countLnCustBr(cyc_mn);
				//~~~~~~
				BigDecimal cnt_eloan = BigDecimal.valueOf(clsService.count_C900M03A_dtl(fn, cyc_mn, ""));
				
				if(cnt_dw==null || cnt_eloan==null || cnt_dw.compareTo(cnt_eloan)!=0){
					throw new Exception("check_DW_LNCUSTBR_C900S03D[cyc_mn="+cyc_mn+"]cnt_dw="+cnt_dw+", cnt_eloan="+cnt_eloan);
				}
			}
		}
		
		Date dt_cyc_mn = CapDate.parseDate(cyc_mn);
		
		Map<String, String> idDup_cName = new HashMap<String, String>();
		String[] rel_flag_arr = {"1", "2", "3"};
		boolean isDwCustRelFirstTotalRun = CrsUtil.isDwCustRelFirstTotalRun(cyc_mn);
		Map<String, Set<String>> c900s03e_idDup_brNos_map = new HashMap<String, Set<String>>();
		if(isDwCustRelFirstTotalRun==false){
			c900s03e_idDup_brNos_map = clsService.findC900S03E_idDup_brNos(cyc_mn);
		}
		
		for(String rel_flag : rel_flag_arr){
			List<Map<String, Object>> layer1_list = eloandbBASEService.prep_text_from_C900S03C(isDwCustRelFirstTotalRun, cyc_mn, rel_flag, "");
			
			if(true){
				Set<String> text_set = new HashSet<String>();
				for(Map<String, Object> map_1 : layer1_list){
					String text = Util.trim(MapUtils.getString(map_1, "TEXT"));
					text_set.add(text);
				}
				
				int jdbc_idx_text = 0;
				int jdbc_cnt_data_list = text_set.size();
				List<Object[]> jdbc_batchValues_c900s02e = new ArrayList<Object[]>();
				List<Object[]> jdbc_batchValues_c900s02f = new ArrayList<Object[]>();
				for(String text : text_set){
					++jdbc_idx_text;
					
//					List<GenericBean> save_list = new ArrayList<GenericBean> ();
					if(true){
						List<Map<String, Object>> layer2_list = eloandbBASEService.prep_custKey_brNo_from_C900S03C_text(cyc_mn, rel_flag, text);
						int layer2_size = layer2_list.size();
						for(int i=0;i<layer2_size;i++){
						
							String mainId = IDGenerator.getUUID();
							
							Map<String, Object> map_2 = layer2_list.get(i);
							String cust_key = Util.trim(MapUtils.getString(map_2, "CUST_KEY"));
							String brNo = Util.trim(MapUtils.getString(map_2, "BRNO"));
							
							/* 用JPA的寫法
							C900S02E c900s02e = new C900S02E();
							if(true){
								c900s02e.setMainId(mainId);
								c900s02e.setCyc_mn(dt_cyc_mn);
								c900s02e.setBrNo(brNo);
								c900s02e.setRel_flag(rel_flag);
								c900s02e.setText(text);
								c900s02e.setCustId(CrsUtil.get_custId_from_custKey(cust_key));
								c900s02e.setDupNo(CrsUtil.get_dupNo_from_custKey(cust_key));
								c900s02e.setCname(get_cname(idDup_cName, c900s02e.getCustId(), c900s02e.getDupNo()));
								c900s02e.setGen_time(nowTS);
								c900s02e.setChk_result("");
								c900s02e.setChk_memo("");
							}                  	                  
							save_list.add(c900s02e);
							*/
							String custId = CrsUtil.get_custId_from_custKey(cust_key);
							String dupNo = CrsUtil.get_dupNo_from_custKey(cust_key);
							String idDup = LMSUtil.getCustKey_len10custId(custId, dupNo);
							Object[] o_s02e = new Object[9];
							o_s02e[0] = mainId;
							o_s02e[1] = dt_cyc_mn;
							o_s02e[2] = brNo;
							o_s02e[3] = rel_flag;
							o_s02e[4] = text;
							o_s02e[5] = custId;
							o_s02e[6] = dupNo;
							o_s02e[7] = get_cname(idDup_cName, custId, dupNo);
							o_s02e[8] = isDwCustRelFirstTotalRun?"":(c900s03e_idDup_brNos_map.containsKey(idDup)?"N":"");
							//===
							jdbc_batchValues_c900s02e.add(o_s02e);

							if(true){
								Set<String> layer2_key_set = new HashSet<String>();
								for(int j=0; j<layer2_size;j++){
									if(i==j){
										continue;
									}
									
									Map<String, Object> map_2_rel = layer2_list.get(j);
									String rel_cust_key = Util.trim(MapUtils.getString(map_2_rel, "CUST_KEY"));
									
									if(Util.equals(cust_key, rel_cust_key)){
										continue;
									}else{
										// 同一人在 N個分行有授信額度 => c900s02f 的同一個 rel_cust_key, 可能有N個分行 
										// String rel_brNo = Util.trim(MapUtils.getString(map_2_rel, "BRNO"));
										layer2_key_set.add(rel_cust_key);	
									}									
								}
								for(String rel_cust_key : layer2_key_set ){
									/*  用JPA的寫法
									C900S02F c900s02f = new C900S02F();
									if(true){
										c900s02f.setMainId(mainId);
										c900s02f.setRel_custId(CrsUtil.get_custId_from_custKey(rel_cust_key));
										c900s02f.setRel_dupNo(CrsUtil.get_dupNo_from_custKey(rel_cust_key));
										c900s02f.setRel_cname(get_cname(idDup_cName, c900s02f.getRel_custId(), c900s02f.getRel_dupNo()));							
									}
									save_list.add(c900s02f);
									*/
									String rel_custId = CrsUtil.get_custId_from_custKey(rel_cust_key);
									String rel_dupNo = CrsUtil.get_dupNo_from_custKey(rel_cust_key);
									String rel_idDup = LMSUtil.getCustKey_len10custId(rel_custId, rel_dupNo);
									Object[] o_s02f = new Object[5];
									o_s02f[0] = mainId;
									o_s02f[1] = rel_custId;
									o_s02f[2] = rel_dupNo;
									o_s02f[3] = get_cname(idDup_cName, rel_custId, rel_dupNo);
									o_s02f[4] = isDwCustRelFirstTotalRun?"":(c900s03e_idDup_brNos_map.containsKey(rel_idDup)?"N":"");
									//===
									jdbc_batchValues_c900s02f.add(o_s02f);
								}	
							}											
						}
					}
//					clsService.save(save_list);
					//~~~
					if(jdbc_idx_text%50==0 || jdbc_idx_text==jdbc_cnt_data_list){
						eloandbBASEService.batchInsert_C900S02E_C900S02F(jdbc_batchValues_c900s02e, jdbc_batchValues_c900s02f);
						jdbc_batchValues_c900s02e = new ArrayList<Object[]>();
						jdbc_batchValues_c900s02f = new ArrayList<Object[]>();
					}
				} //end text_set
			}
			
			if(isDwCustRelFirstTotalRun){
				
			}else{
				
			}
		}
		
		if(true){ // 寫入 C900M03A
			String fn = CrsUtil.FN_KEY_C900S02E;
			String genDate = StringUtils.replace(cyc_mn, "-", "");
			String genTime="000000";
			C900M03A c900m03a = clsService.findC900M03A_fnGenDateGenTime(fn, genDate, genTime);
			if(c900m03a==null){
				c900m03a = new C900M03A();
				c900m03a.setFn(fn);
				c900m03a.setGenDate(genDate);
				c900m03a.setGenTime(genTime);
			}
			c900m03a.setGenCnt(BigDecimal.ZERO);
			clsService.save(c900m03a);
		}
	}
	
	private String get_cname(Map<String, String> idDup_cName , String custId, String dupNo){
		String idDup = LMSUtil.getCustKey_len10custId(custId, dupNo);
		if(!idDup_cName.containsKey(idDup)){
			String cName = "";
			if(Util.isEmpty(cName)){ //先從 ELOAN
				Map<String, Object> map = lmsCustdataService.findCustDataCname(custId, dupNo);
				cName = Util.trim(MapUtils.getString(map, "CNAME"));			
			}
			if(Util.isEmpty(cName)){ //再從 MIS
				Map<String, Object> latestData = iCustomerService.findByIdDupNo(custId, dupNo);
				cName = Util.trim(MapUtils.getString(latestData, "CNAME"));				
			}
			idDup_cName.put(idDup, cName);
		}
		return idDup_cName.get(idDup);
	}
	
	@SuppressWarnings("unchecked")
	private void fix_LNF010_SALARY(Date dateBeg, Date dateEnd, String oid, String LNF010_UPDATE_YN){
		List<C160S01D> list = new ArrayList<C160S01D>();
		if(true){
			ISearch search = clsService.getMetaSearch();
			search.addSearchModeParameters(SearchMode.BETWEEN, "lnFromDate", new Object[]{dateBeg, dateEnd});
			search.addSearchModeParameters(SearchMode.EQUALS, "misflag", "Y"); //補上傳
			search.addSearchModeParameters(SearchMode.IS_NULL, "annuity", "");
			if(Util.isNotEmpty(oid)){
				search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
			}
			search.setMaxResults(Integer.MAX_VALUE);
			Page<? extends GenericBean> page = clsService.findPage(C160S01D.class, search);
			list = (List<C160S01D>) page.getContent();
		}
		
		if(true){
			int[] type = {java.sql.Types.INTEGER, java.sql.Types.INTEGER, java.sql.Types.CHAR, java.sql.Types.CHAR};
			logger.info("fix_LNF010_SALARY[inputParam:"+TWNDate.toAD(dateBeg)+","+TWNDate.toAD(dateEnd)+","+oid+","+LNF010_UPDATE_YN+"]proc_size="+list.size());			
			for(C160S01D c160s01d : list){
				if(c160s01d.getAnnuity()==null || c160s01d.getAnnuity()==0){
					String noteId = c160s01d.getOid(); 
					String custId = StringUtils.substring(c160s01d.getCustId(), 0, 10);
					String dupNo = StringUtils.substring(c160s01d.getCustId(), 10, 11);
					List<Map<String, Object>> dw_list = dwdbBASEService.findDW_RKAPPLICANT_byNoteIdCustIdDupNo(noteId, noteId,  custId,  dupNo);
					if(dw_list.size()>0){
						Map<String, Object> dw_map = dw_list.get(0);
						BigDecimal ypay = CrsUtil.parseBigDecimal(MapUtils.getObject(dw_map, "YPAY"));
						BigDecimal omoney_amt = CrsUtil.parseBigDecimal(MapUtils.getObject(dw_map, "OMONEY_AMT"));
						logger.info("c160s01d.oid["+noteId+"]bef["+c160s01d.getAnnuity()+"]["+c160s01d.getOthincome()+"], af["+ypay+"]["+omoney_amt+"]");
						//==================
						if(ypay!=null && (c160s01d.getAnnuity()==null || c160s01d.getAnnuity()==0)){
							c160s01d.setAnnuity(ypay.intValue());	
						}
						if(omoney_amt!=null && (c160s01d.getOthincome()==null || c160s01d.getOthincome()==0)){
							c160s01d.setOthincome(omoney_amt.intValue());	
						}
						clsService.daoSave(c160s01d);
						//==================
						List<Object[]> lst = new ArrayList<Object[]>();
						lst.add(new Object[]{ypay, omoney_amt, LNF010_UPDATE_YN, c160s01d.getCustId()});
						misdbBASEService.update(
								new Object[] { "LN.LNF010", "LNF010_SALARY=? , LNF010_OTH_REVENUE=?, LNF010_UPDATE_YN=? "
										, "LNF010_CUST_ID=? " }
								, type, lst);					
					}else{
						logger.info("c160s01d.oid["+noteId+"]=dw_list.size()==0");
					}				
				}
			}
		}		
	}
	
	/**
	 * 比照 RetrialBatchServiceImpl :: generateL180r19Data_ctlType
	 */
	private void write_L180R19H(Date givenDate){
		List<IBranch> branchList = branchService.getAllBranch();
		List<IBranch> removeList = new ArrayList<IBranch>();
		// 組出不需要的銀行資料
		for (IBranch branch : branchList) {
			// 要排除的資料 "117","009","088","243","001","011","024","078" ,
			// Left(strtBrno,1) = "9"
			if ("009".equals(branch.getBrNo())
					|| "088".equals(branch.getBrNo())
					|| "243".equals(branch.getBrNo())
					|| "001".equals(branch.getBrNo())
					|| "011".equals(branch.getBrNo())
					|| "024".equals(branch.getBrNo())
					|| "109".equals(branch.getBrNo())) {
				removeList.add(branch);
			} else if ("Y".equals(branch.getBrekFlag())) {
				removeList.add(branch);
			} else if (branch.getBrNo().startsWith("9")) {
				removeList.add(branch);
			} else if (Util.notEquals(branch.getCountryType(), "TW")) {
				removeList.add(branch);
			} else if (UtilConstants.BrNoType.國外.equals(branch.getBrNoFlag())) {
				removeList.add(branch);
			}
		}
		branchList.removeAll(removeList);
		//==============
		String docType = UtilConstants.Casedoc.DocType.個金;
		String ctlType = "";
		Date runJobDate = givenDate!=null?givenDate:CapDate.getCurrentTimestamp();
		Date baseDate = CapDate.shiftDays(Util.parseDate(CapDate.formatDate(runJobDate, "yyyy-MM")+ "-01"), -1); // 上個月底
		String chkDateBgn = CapDate.formatDate(baseDate, "yyyy-MM") + "-01";
		String chkDateEnd = CapDate.formatDate(baseDate, "yyyy-MM-dd");
		String fetch_elf491_param_begDate = chkDateBgn;
		logger.info("SLMS-00078_write_L180R19H_param_1st[baseDate="+TWNDate.toAD(baseDate)+"][chkDateBgn/fetch_elf491_param_begDate="+chkDateBgn+"/"+fetch_elf491_param_begDate+"][chkDateEnd="+chkDateEnd+"]");
		if(LMSUtil.cmp_yyyyMM(CapDate.parseDate("2021-08-01"), "<=", runJobDate) && LMSUtil.cmp_yyyyMM(runJobDate, "<=", CapDate.parseDate("2022-08-31"))){
			fetch_elf491_param_begDate = TWNDate.toAD(CapDate.addMonth(CapDate.parseDate(chkDateBgn), -6));
		}
		logger.info("SLMS-00078_write_L180R19H_param_2nd[baseDate="+TWNDate.toAD(baseDate)+"][chkDateBgn/fetch_elf491_param_begDate="+chkDateBgn+"/"+fetch_elf491_param_begDate+"][chkDateEnd="+chkDateEnd+"]");
		Date dt_chkDateEnd = CapDate.parseDate(chkDateEnd);
		String l180r19hMainId = IDGenerator.getUUID();
		for (IBranch branch : branchList) {
			String brNo = branch.getBrNo();
			
			// 檢核該分行這個月是否已經產生
			// 已產生就PASS，沒產生才做

			Map<String, Object> l180r19hMap = eloandbBASEService.findL180r19hHasDone(docType, chkDateBgn, chkDateEnd,
							ctlType, brNo);

			if (l180r19hMap != null && !l180r19hMap.isEmpty()) {
				String tCount = Util.trim(MapUtils.getString(l180r19hMap,
						"TCOUNT", "0"));
				if (Util.parseBigDecimal(tCount).compareTo(BigDecimal.ZERO) > 0) {
					// 有999999999 代表該分行上個月底資料已經產生，就不用再執行了
					logger.info("SLMS-00078_write_L180R19H_skip_alreadyGen[findL180r19hHasDone("+docType+", "+chkDateBgn+", "+chkDateEnd+", "+ctlType+", "+brNo+")]");
					continue;
				}
			}

			List<Map<String, Object>> over_list = misELF491Service.sel_gfnGetWillOverReviewData(brNo, fetch_elf491_param_begDate, chkDateEnd);
			if(over_list.size()>0){
				Date _lnDataDate = CapDate.getCurrentTimestamp();
				for(Map<String, Object> map : over_list){
					if (Util.notEquals(brNo,
							MapUtils.getString(map, "LNF020_LN_BR_NO", ""))) {
						continue;
					}
					L180R19H l180r19h = new L180R19H();

					l180r19h.setMainId(l180r19hMainId);
					l180r19h.setDataDate(baseDate);
					l180r19h.setDocType(docType);
					l180r19h.setCtlType(ctlType);
					l180r19h.setBranch(Util.trim(MapUtils.getString(map, "ELF491_BRANCH")));
					l180r19h.setCustId(Util.trim(MapUtils.getString(map, "ELF491_CUSTID")));
					l180r19h.setDupNo(Util.trim(MapUtils.getString(map, "ELF491_DUPNO")));
					l180r19h.setCntrNo(Util.trim(MapUtils.getString(map, "LNF020_CONTRACT")));
					l180r19h.setFactCurr(Util.trim(MapUtils.getString(map, "LNF020_SWFT")));
					l180r19h.setFactAmt(CrsUtil.parseBigDecimal(MapUtils.getObject(map, "LNF020_FACT_AMT")));
					l180r19h.setBalCurr(Util.trim(MapUtils.getString(map, "LNF020_SWFT")));
					l180r19h.setBalAmt(CrsUtil.parseBigDecimal(MapUtils.getObject(map, "LNF030_LOAN_BAL")));
					l180r19h.setSubject(Util.trim(CrsUtil.getSubjCodeFromLNF030_LOAN_NO(MapUtils.getString(map, "LNF030_LOAN_NO"))));
					Date loanDuringBgn = null;
					Date loanDuringEnd = null;
					if(true){
							Date UseFDate = (CapDate.parseDate(Util.trim(MapUtils.getObject(map, "LNF020_BEG_DATE"))));
							Date UseEDate = (CapDate.parseDate(Util.trim(MapUtils.getObject(map, "LNF020_END_DATE"))));
							Date LoanFDate = (CapDate.parseDate(Util.trim(MapUtils.getObject(map, "LNF020_DURATION_BG"))));
							Date LoanEDate = (CapDate.parseDate(Util.trim(MapUtils.getObject(map, "LNF020_DURATION_ED"))));
							
							Date[] loanUseArr = new Date[2];
							loanUseArr[0] = null;
							loanUseArr[1] = null;

							boolean fetch_useDate = true;// default 抓動用起迄日
							if (CrsUtil.isNOT_null_and_NOTZeroDate(LoanFDate)
									&& LMSUtil.cmpDate(_lnDataDate, ">", UseEDate)) {
								fetch_useDate = false;// 改抓 中長期授信期間起迄日
							}

							if (fetch_useDate) {
								loanUseArr[0] = UseFDate;
								loanUseArr[1] = UseEDate;
							} else {
								loanUseArr[0] = LoanFDate;// 授信起日
								loanUseArr[1] = LoanEDate;// 授信迄日
							}
							
							loanDuringBgn = loanUseArr[0];
							loanDuringEnd = loanUseArr[1];
					}
					l180r19h.setBgnDate(loanDuringBgn);
					l180r19h.setEndDate(loanDuringEnd);
					Date lrDate = CapDate.parseDate(Util.trim(MapUtils.getObject(map, "ELF491_LRDATE")));					
					l180r19h.setLrDate(CrsUtil.isNOT_null_and_NOTZeroDate(lrDate) ?lrDate:null);// 前次覆審日
					Date crDate = CapDate.parseDate(Util.trim(MapUtils.getObject(map, "ELF491_CRDATE")));
					Date extendCrDate = CrsUtil.get_J_110_0309_extendCrDate(brNo, crDate);
					if(LMSUtil.cmp_yyyyMM(extendCrDate, "==", dt_chkDateEnd)){
						
					}else{
						logger.info("SLMS-00078_write_L180R19H_skip[brNo="+brNo+"][custId="+l180r19h.getCustId()+"][dupNo="+l180r19h.getDupNo()+"][ELF491_CRDATE="+TWNDate.toAD(crDate)+"][extendCrDate="+TWNDate.toAD(extendCrDate)+"]");
						continue;
					}
					l180r19h.setDueDate(CrsUtil.isNOT_null_and_NOTZeroDate(extendCrDate) ?extendCrDate:null);
					l180r19h.setRckdLine(Util.trim(MapUtils.getString(map, "ELF491_REMOMO")));

					// 異常通報的條件
					Date uckdDt = CapDate.parseDate(Util.trim(MapUtils.getObject(map, "ELF491_UCKDDT")));
					l180r19h.setMdDate(CrsUtil.isNOT_null_and_NOTZeroDate(uckdDt) ?uckdDt:null);
					String ELF491_UCKDLINE = Util.trim(MapUtils.getString(map, "ELF491_UCKDLINE"));
					l180r19h.setIsMdDue(CrsUtil.isNOT_null_and_NOTZeroDate(uckdDt)&& Util.equals("1", ELF491_UCKDLINE)?"Y":"");

					String memo = "";
					if(LMSUtil.cmp_yyyyMM(extendCrDate, "==", crDate)){
						//ok
					}else{
						memo = "因疫情順延，原為："+StringUtils.substring(TWNDate.toAD(crDate), 0, 7);
					}
					l180r19h.setMemo(memo);

					l180r19h.setCreator("S00078"); // SLMS-00078
					l180r19h.setCreateTime(CapDate.getCurrentTimestamp());
					l180r19h.setUpdater(null);
					l180r19h.setUpdateTime(null);
					clsService.daoSave(l180r19h);
				}
			}
			
			// 寫LMS.L180R19H
			// 要再另外的Service寫，要不然會受到@NonTransactional影響，最後一筆不會寫入
			if (true) {
				L180R19H l180r19h = new L180R19H();

				l180r19h.setMainId(l180r19hMainId);
				l180r19h.setDataDate(baseDate);
				l180r19h.setDocType(docType);
				l180r19h.setCtlType(ctlType);
				l180r19h.setBranch(brNo);
				l180r19h.setCustId("9999999999");
				l180r19h.setDupNo("9");
				l180r19h.setCntrNo("999999999999");
				l180r19h.setCreator("S00078"); // SLMS-00078
				l180r19h.setCreateTime(CapDate.getCurrentTimestamp());
				l180r19h.setUpdater(null);
				l180r19h.setUpdateTime(null);

				clsService.daoSave(l180r19h);
			}
		}
	}

	private void job_J_109_0344(){
		/*
		  	select * from ln.lnf07a where LNF07A_KEY_1='J-109-0344(109)2561'
		*/
		if(clsService.is_function_on_codetype("job_J_109_0344_step1")){
			int cnt = misdbBASEService.job_J_109_0344_step1();
			logger.info("job_J_109_0344_step1_procCnt="+cnt);
		}
		/*
			select * from mis.lnunid where refusecd=23 and refuseds like '109.8.25兆銀總授審字第1090045985號函%'
		*/
		if(clsService.is_function_on_codetype("job_J_109_0344_step2")){
			int cnt = misdbBASEService.job_J_109_0344_step2();
			logger.info("job_J_109_0344_step1_procCnt="+cnt);
		}
	}

	private void ploan_TRPAYSQ1(int daysTRPAYSQ1){
		for(Map<String, Object> itemMap : eloandbBASEService.find_ploan_no_ptaData(daysTRPAYSQ1)){
			String mainId = Util.trim(MapUtils.getString(itemMap, "MAINID")); 
			String custId = Util.trim(MapUtils.getString(itemMap, "CUSTID"));
			String dupNo = Util.trim(MapUtils.getString(itemMap, "DUPNO"));
			C120S01B c120s01b = clsService.findC120S01B(mainId, custId, dupNo);
			if(c120s01b==null){
				continue;
			}	
			
			String idDup = LMSUtil.getCustKey_len10custId(c120s01b.getCustId(), Util.isEmpty(Util.trim(c120s01b.getDupNo()))?"0":c120s01b.getDupNo());
			Map<String, Object> map = misStoredProcService.callTRPAYSQ1(idDup);
			if ("YES".equals(Util.trim(map.get("SP_RETURN")))) {
				String outResult = Util.trim(map.get("SP_OUTPUT_AREA"));

				// 第1碼是{
				Map<String, String> parseRtnMap = ClsUtility.parse_TRPAYSQ1_rtn(outResult);			
				String RETURN_CODE = Util.trim(MapUtils.getString(parseRtnMap, "RETURN_CODE"));
				String PAYLG = Util.trim(MapUtils.getString(parseRtnMap, "PAYLG"));
				String TAXNO = Util.trim(MapUtils.getString(parseRtnMap, "TAXNO"));
				String GRADE = Util.trim(MapUtils.getString(parseRtnMap, "GRADE"));
				String ACTNO = Util.trim(MapUtils.getString(parseRtnMap, "ACTNO"));
				if (Util.equals("0000", RETURN_CODE)) {
					c120s01b.setPtaDataDt(CapDate.getCurrentTimestamp());
					c120s01b.setPtaFlag(PAYLG);
					c120s01b.setPtaTaxNo(TAXNO);
					c120s01b.setPtaGrade(GRADE);
					c120s01b.setPtaBrNo(Util.trim(StringUtils.substring(ACTNO, 0, 3)));
					c120s01b.setPtaActNo(ACTNO);
					clsService.daoSave(c120s01b);
				} else {
					logger.error("setPtaData, idDup=[" + idDup + "],RETURN_CODE=["+ RETURN_CODE + "]");
				}
			} else {
				logger.error("setPtaData, idDup=[" + idDup + "],SP_RETURN=["+ Util.trim(map.get("SP_RETURN")) + "]");
			}
		}
	}

	private void ploan_getCSCStakeholderData(String ploanPlan) throws CapMessageException{
		StringBuilder sb = new StringBuilder();
		String[] planArr = null;
		if(Util.isEmpty(ploanPlan)){
			logger.info("SLMS-00142 ploanPlan is empty");
			return;
		}else{
			planArr = ploanPlan.split(",");
//			for(String plan:planArr){
//				if(Util.isNotEmpty(sb)){
//					sb.append(",");
//				}
//				sb.append("'").append(plan).append("'");
//			}
		}
		for(Map<String, Object> itemMap : eloandbBASEService.findPloanCSCNoStkhData(planArr)){
			String oid = Util.trim(MapUtils.getString(itemMap, "OID")); 
			String custId = Util.trim(MapUtils.getString(itemMap, "CUSTID"));
			String dupNo = Util.trim(MapUtils.getString(itemMap, "DUPNO"));
			String custName = Util.trim(MapUtils.getString(itemMap, "CUSTNAME"));
			
			C122M01A c122m01a = clsService.findC122M01A_oid(oid);
			if(c122m01a==null){
				continue;
			}	

			JSONArray stakeholderData = eaiService
					.findStakeholderInfoByFinancialHoldingAct("", custId, dupNo);// 客戶是否為金控利害關係人
			c122m01a.setStkhQueryEJTs(CapDate.getCurrentTimestamp());
			String name = custName;
			String remain = String.valueOf(stakeholderData.get(0));
			String other = String.valueOf(stakeholderData.get(3));
			String law44 = String.valueOf(stakeholderData.get(1));
			String law45 = String.valueOf(stakeholderData.get(2));

			// MEGAID用戶名查詢金控法
			if (custId.matches("^[A-Z]{2}Z[\\d]{7}$")) {

				String relNAME = this.getRelationshipNameByMegaId(custName, custId,
						dupNo);
				// 取代畫面上的戶名
				name = relNAME;

				// 若戶名為空，顯示錯誤訊息
				law44 = "無中文及英文戶名";
				law45 = "無中文及英文戶名";

				if (Util.isNotEmpty(relNAME)) {

					String RelId = "";
					String compare = "";
					String check = "0";
					String nCode = NcodeEnum.法人2.getCode(); // 法人

					// 戶名查詢金控法44條
					JSONArray law44Data = eaiService.doIPSCO01(RelId, relNAME,
							compare, check, "44", nCode);
					law44 = "N";// message.001=查無資料
					if (law44Data != null && !law44Data.isEmpty()) {
						law44 = "Y";
					}

					// 戶名查詢金控法45條
					JSONArray law45Data = eaiService.doIPSCO01(RelId, relNAME,
							compare, check, "45", nCode);
					law45 = "N";// message.001=查無資料
					if (law45Data != null && !law45Data.isEmpty()) {
						law45 = "Y";
					}
				}
			}

			Properties properties = MessageBundleScriptCreator
					.getComponentResource(AbstractEloanPage.class);

			// J-108-0178 新增公司法董事控制從屬關係公司查詢
			String director = properties.getProperty("no");
			String directorStatus = "N";

			if (!custId.isEmpty()) {

				Map<String, Object> directorMap = misElf902Service
						.findCnameByRcustIdAndDupNo1(custId, dupNo);
				String mapKey = !directorMap.keySet().isEmpty() ? new ArrayList<String>(
						directorMap.keySet()).get(0) : "";

				String directorValue = StringUtils.removeEnd(
						CapString.trimNull(directorMap.get(mapKey)), "、");
				if (directorValue.length() > 0) {
					director = properties.getProperty("yes") + "(" + directorValue
							+ ")";
					directorStatus = "Y";
				}
			}

			c122m01a.setStkhBank33(this.getDataStatusOfStakeholder(remain));//銀行法利害關係人
			c122m01a.setStkhFh44(this.getDataStatusOfStakeholder(law44));//金控法第44條利害關係人
			c122m01a.setStkhFh45(this.getDataStatusOfStakeholder(law45));//金控法第45條利害關係人
			c122m01a.setStkhRelFg(this.getDataStatusOfStakeholder(other));//實質關係人(授信以外交易)
			c122m01a.setStkhCoFg(this.getDataStatusOfStakeholder(directorStatus));//公司法與本行董事具有控制從屬關係公司
			
			clsService.save(c122m01a);
		}
	}
	
	private String getDataStatusOfStakeholder(String msg) {

		if ("N".equals(msg) || CapString.trimNull(msg).contains("EAI 1000")) {
			return "0";
		}

		return "1";
	}

	private String getRelationshipNameByMegaId(String custName, String custId,
			String dupNo) {

		// 戶名查詢金控法44條
		String relNAME = custName;
		// 戶名為空，查詢0024ENAME
		if (CapString.isEmpty(relNAME)) {

			List<Map<String, Object>> custNamelsit = iCustomerService
					.findByIdBy0024(custId);
			if (custNamelsit != null) {

				for (Map<String, Object> cust : custNamelsit) {
					if (dupNo.equals(cust.get("DUPNO"))) {
						relNAME = CapString.trimNull(Util.isNotEmpty(cust
								.get("ENAME")) ? cust.get("ENAME") : "");
					}
				}
			}
		}

		return relNAME;
	}
	private void labor_bailout_genC340M01A(int days, String mainId)throws CapException, Exception{
		if(Util.isNotEmpty(mainId)){
			L120M01A l120m01a = clsService.findL120M01A_mainId(mainId);
			if(l120m01a==null){
				throw new CapException("labor_bailout_genC340M01A : mainId["+mainId+"] not found", getClass());
			}
			
			if( ! Util.equals(l120m01a.getDocStatus(), CreditDocStatusEnum.海外_已核准.getCode())){
				throw new CapException("labor_bailout_genC340M01A : mainId["+mainId+"],docStatus=["+l120m01a.getDocStatus()+"]", getClass());
			}
			
			if(true) {
				//增加 retry 機制
				call_rpa_genC340M01A(l120m01a.getCaseBrId(), l120m01a.getCustId(), mainId, 1);
				/*
				  	select * from com.bschlog where schid='SLMS-00129' and startTime>='2021-06-12 17:10:00'
				  	----------------
				  	【測試環境 因 資料量少，可以這樣子下SQL】
				  	【正式環境 的 bgwdata 資料量太多，並不建議用 data like ? 去查詢】 找到 uniqueID 後，請 RPA 的同仁去查詢，是尚未執行？ 或已執行，但執行失敗？ 
				  	select clientip, serviceId, txid, msgid, b.* from (select sno,clientip,serviceId, txid,msgid from com.bgwlog where serviceId in ( 'RPA' ) 
				  		and sno in (select logsno from com.bgwdata where logts>='2021-06-12 17:10:00' and data like '%Queue_API_LaborLoan_EloanAutomation_Contract%')
				  	) a left outer join com.bgwdata b on a.sno=b.logsno 
				  	----------------
				  	update lms.L120M01A set orgapprovetime=null where mainid=?
				  	----------------
				  	select oid, custid, applykind, statflag, createtime from lms.c122m01a where deletedtime is null and applykind in ('B','D') and custId in (select custid from lms.l120m01a where mainid= ?)  
				*/
			}		
		}
		if(days != 0){
			// 若 RPA 失敗，是否撈某一個區間內的 簽報書，重送 RPA
		}
	}
	
	private String rpa_get_token() throws CapException{
		// Step 1 取得 token
		String errorMsg  = "";
		String token = null;
		JSONObject resultJson = null;
		Map<String, Object> objResult = new LinkedHashMap<String, Object>();
		try {
			resultJson = rpaService.getRPAAccessToken(objResult);
			token = resultJson != null ? resultJson.getString("result")
					: "";
			token = "Bearer " + token;
			
		} catch (Exception e) {
			errorMsg = "取得RPA Token失敗，請稍後再試。"+StrUtils.getStackTrace(e);
			throw new CapException(errorMsg, this.getClass());
		}
		return token;
	}
	
	//呼叫RPA產生 C340M01A
	private Boolean call_rpa_genC340M01A(String brNo, String custId, String mainId, int retry) 
	throws CapException{
		if (retry > 5) {
			//若 retry 超過5次，就 stop 
			return false;
		} else {
			Boolean isSuccess = false;			
			try {
				//Step 1 取得 token
				String token = rpa_get_token();

				//Step 發查RPA
				//STEP2 StartRPAJobForLMS
				Map<String, Object> objResult = new LinkedHashMap<String, Object>();
				try {
					logger.info("啟動JOB-RPA 產生 C340M01A 開始========================");
					// 設定要傳遞的參數 LinkedHashMap 才可以按順序取出
					objResult.put("responseURL", sysParameterService.getParamValue(SysParamConstants.RPA_GW_RESPONSE_URL_LMS));
					objResult.put("system", "eloan");
					objResult.put("uniqueID", custId+"_"+mainId+"_"+CapDate.convertTimestampToString(CapDate.getCurrentTimestamp(), "MMddHHmmss"));
					
					objResult.put("data_Id", custId);
					objResult.put("data_BranchNo", brNo); 
					logger.info("傳入參數==>[{}]", objResult.toString());
					rpaService.StartRPAJobForLMS(objResult, token, "", SysParamConstants.RPA_勞工紓困案契約書); // "Queue_API_LaborLoan_EloanAutomation_Contract"	
					logger.info("啟動JOB-RPA 產生 C340M01A 結束========================");
					//===============
					isSuccess = true;
				} catch (Exception e){
					logger.error("RPA Job建立失敗，請稍後再試。========================");
					throw new CapException("RPA Job建立失敗，請稍後再試。", this.getClass());
				}
			} catch (CapException e) {
				isSuccess = false;
			}
			
			if (isSuccess == false) {
				return call_rpa_genC340M01A(brNo, custId, mainId, retry + 1);
			} else {
				return true;
			}
		}
	}
	
	private void trans_DW_RKtbl_cntrNo_loanNo(Date exDate)
	throws Exception{
		if(exDate==null){
			return;
		}
		LinkedHashSet<String> cntrNo_set = new LinkedHashSet<String>();
		LinkedHashSet<String> loanNo_set = new LinkedHashSet<String>();
		
		List<Map<String, Object>> eLoan_COM_LNF078T_list = eloandbBASEService.LNF078T_selAllByExDate(TWNDate.toAD(exDate));
		if(eLoan_COM_LNF078T_list.size()==0){
			throw new Exception("exDate="+TWNDate.toAD(exDate)+" data_size==0");
		}else{
			String splict_char = "^";
			for(Map<String, Object> row_data : eLoan_COM_LNF078T_list){
				String CONTRACT_O = Util.trim(MapUtils.getString(row_data, "CONTRACT_O"));
				String LOAN_NO_O = Util.trim(MapUtils.getString(row_data, "LOAN_NO_O"));
				String CONTRACT = Util.trim(MapUtils.getString(row_data, "CONTRACT"));
				String LOAN_NO = Util.trim(MapUtils.getString(row_data, "LOAN_NO"));

				if(Util.isNotEmpty(CONTRACT_O) && Util.isNotEmpty(CONTRACT) ){
					cntrNo_set.add(CONTRACT_O + splict_char + CONTRACT);
				}
				if(Util.isNotEmpty(LOAN_NO_O) && Util.isNotEmpty(LOAN_NO) ){
					loanNo_set.add(LOAN_NO_O + splict_char + LOAN_NO);
				}
			}
		
			List<String> cntrNoSplit_list = new ArrayList<String>(cntrNo_set);
			List<String> loanNoSplit_list = new ArrayList<String>(loanNo_set);
			if(cntrNoSplit_list.size()==0 && loanNoSplit_list.size()==0){
				throw new Exception("exDate="+TWNDate.toAD(exDate)+" , data_size>0, but convert_data_size==0");	
			}else{				
				String[] cntrNoSplit = cntrNoSplit_list.toArray(new String[0]);
				String[] loanNoSplit = loanNoSplit_list.toArray(new String[0]);
				
				clsService.trans_DW_RKtbl_cntrNo_loanNo(cntrNoSplit, loanNoSplit);
			}
			
		}
	}

	private void trans_OTS_CRDLN031_old_to_new_AREA_CD(String brNo, String old_areaNo, String new_areaNo){
		logger.info("trans_OTS_CRDLN031_old_to_new_AREA_CD["+brNo+", "+old_areaNo+" to "+new_areaNo+"]");
		dwdbBASEService.trans_OTS_CRDLN031_old_to_new_AREA_CD(brNo, old_areaNo, new_areaNo);
	}
	
	private void crs_baseCnt_R95_1(String forceFlag)
	throws Exception{
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		String str_sysDate = TWNDate.toAD(nowTS); 
		Date writeDate = null;
		if(str_sysDate.endsWith("-07-01")){
			writeDate = CapDate.parseDate(CrsUtil.get_R95_1_baseDate(str_sysDate));
		}else{
			if(Util.equals("Y", forceFlag)){
				writeDate = CapDate.parseDate(CrsUtil.get_R95_1_baseDate(str_sysDate));
			}else{
				throw new Exception("sysDate="+str_sysDate);
			}
		}
		
		if(writeDate!=null){
			/*
			  select * from mis.elf491c where elf491c_lrdate='2020-06-30' and elf491c_rule_no in ('DN_95', 'LS_95') order by elf491c_rule_no
			*/
			List<ELF491C> elf491c_list = new ArrayList<ELF491C>();
			String elf491c_unid_prefix = new SimpleDateFormat("yyyyMMdd").format(nowTS)+"_"+"SLMS-00127"+"_";
			String updater = "eloan";
			for(Map<String, Object> data : misELF491CService.list_baseCnt_R95_1()){
				String brNo = MapUtils.getString(data, "LNF020_BR");
				String custId = MapUtils.getString(data, "CUSTID");
				String dupNo = MapUtils.getString(data, "DUPNO");
				//~~~~~~
				ELF491C elf491c = new ELF491C();
				if(true){ // 把明細留存，若要抓「年度基準日」的總筆數，用 select count(*) from mis.elf491c
					elf491c.setElf491c_unid(elf491c_unid_prefix+custId+"-"+dupNo);
					elf491c.setElf491c_rule_no(CrsUtil.LIST_BASE_95_1_RULE_NO);
					elf491c.setElf491c_branch(brNo);
					elf491c.setElf491c_custid(custId);
					elf491c.setElf491c_dupno(dupNo);
					elf491c.setElf491c_lrdate(writeDate);
					elf491c.setElf491c_updater(updater);
					elf491c.setElf491c_tmestamp(nowTS);
				}				
				elf491c_list.add(elf491c);
			}
			if(true){ //用 XXXXXXXXXX-X 來標註，已跑完 Rule 95-1 的批次
				ELF491C elf491c = new ELF491C();
				String custId = CrsUtil.DONE_95_1_CUSTID;
				String dupNo = CrsUtil.DONE_95_1_DUPNO;
				if(true){
					elf491c.setElf491c_unid(elf491c_unid_prefix+custId+"-"+dupNo);
					elf491c.setElf491c_rule_no(CrsUtil.DONE_95_1_RULE_NO);
					elf491c.setElf491c_branch(CrsUtil.DONE_95_1_BR);
					elf491c.setElf491c_custid(custId);
					elf491c.setElf491c_dupno(dupNo);
					elf491c.setElf491c_lrdate(writeDate);
					elf491c.setElf491c_updater(updater);
					elf491c.setElf491c_tmestamp(nowTS);
				}				
				elf491c_list.add(elf491c);
			}
			retrialService.upELF491C_DelThenInsert(elf491c_list);
		}		
	}

	
	private void c160s01d_proc_ctrTypeC_doAct01_genC340(int batch_cnt){
		ISearch search = clsService.getMetaSearch();
		search.addSearchModeParameters(SearchMode.GREATER_THAN, "cntrNo", "");
		search.addSearchModeParameters(SearchMode.GREATER_THAN, "c122m01a_mainId", "");
		search.addSearchModeParameters(SearchMode.EQUALS, "gen_ctrType_C", "P");
		if(true){ //產  pdf 應該會花一些 resource, 在 batch_cnt 不要設太大
			search.setMaxResults(batch_cnt);
		}
		Page<? extends GenericBean> page = clsService.findPage(C160S01D.class, search);

		// UPGRADE: 待確認，URL怎麼取得
		String ctrType_C_docUrl = "";// CLS3401M07Page.class.getAnnotation(MountPath.class).path();
		//~~~~~~~~~~~~~
		@SuppressWarnings("unchecked")
		List<C160S01D> c160s01d_list = (List<C160S01D>) page.getContent();;
		for(C160S01D c160s01d : c160s01d_list){
			try{
				_c160s01d_proc_ctrTypeC_act01(c160s01d, ctrType_C_docUrl);
			}catch(Exception e){
				logger.error("【_c160s01d_proc_ctrTypeC_act01】【c160s01d_oid='"+c160s01d.getOid()+"' , cntrNo="+Util.trim(c160s01d.getCntrNo())+"】"+StrUtils.getStackTrace(e));
			}
		}
	}
	
	private void _c160s01d_proc_ctrTypeC_act01(C160S01D c160s01d, String ctrType_C_docUrl){
		String c122m01a_mainId = c160s01d.getC122m01a_mainId();
		C122M01A c122m01a = clsService.findC122M01A_mainId(c122m01a_mainId);			
		if(c122m01a==null){
			return;
		}
		
		C122M01E c122m01e = clsService.findC122M01E_by_C122M01A_forChinaSteelCorpHQ(c122m01a);
		if(Util.equals(ClsUtility.get_ploanPlan_ChinaSteelCorp_HQ(), c122m01a.getPloanPlan()) && c122m01e==null){
			return;
		}
		
		//有保證人的，走「紙本對保」
		if(StringUtils.isNotBlank(c160s01d.getRId1()) || StringUtils.isNotBlank(c160s01d.getRName1()) 
				|| StringUtils.isNotBlank(c160s01d.getRId2()) || StringUtils.isNotBlank(c160s01d.getRName2())){
			return;
		}
		//=================
		C340M01A c340m01a = _c160s01d_proc_ctrTypeC_act01_genC340_with_pdf_content(c160s01d, c122m01a, c122m01e, ctrType_C_docUrl);
		if(c340m01a!=null){				
			c160s01d.setGen_ctrType_C("Y");					
			clsService.daoSave(c160s01d);				
		}		
	}
	
	private C340M01A _c160s01d_proc_ctrTypeC_act01_genC340_with_pdf_content(C160S01D c160s01d, C122M01A c122m01a, C122M01E c122m01e, String ctrType_C_docUrl){

		String c340m01a_mainId = IDGenerator.getUUID();
		C340M01A c340m01a_tmp = proc_repeat_ploanCtrNo(cls3401Service.init_deactive_C340_ctrType_C(c340m01a_mainId, c160s01d, c122m01a, c122m01e, ctrType_C_docUrl));
		if(c340m01a_tmp!=null){
			byte[] pdf_contract = null;
			byte[] pdf_deductWageAgrmt = null;
			if(true){
				try{
					pdf_contract = cls3401R07RptService.gen_ctrTypeC_pdf_contract(c340m01a_tmp, c160s01d, c122m01a, c122m01e);
				}catch(Exception e){
					logger.error("【gen_ctrTypeC_pdf_contract】"+StrUtils.getStackTrace(e));
				}
				if(pdf_contract==null){
					return null;
				}
				//~~~~~~~~~~~~
				try{
					pdf_deductWageAgrmt = cls3401R07RptService.gen_ctrTypeC_pdf_deductWageAgrmt(c340m01a_tmp, c160s01d, c122m01a, c122m01e);
				}catch(Exception e){
					logger.error("【gen_ctrTypeC_pdf_deductWageAgrmt】"+StrUtils.getStackTrace(e));
				}			
				if(pdf_deductWageAgrmt==null){
					return null;
				}	
			}
			return cls3401Service.save_C340_ctrType_C(c340m01a_tmp, pdf_contract, pdf_deductWageAgrmt);
		}
		return null;		
	}
	
	private C340M01A proc_repeat_ploanCtrNo(C340M01A meta){
		if(meta==null){
			return null;
		}
		String ploanCtrNo = Util.trim(meta.getPloanCtrNo());
		List<C340M01A> c340m01a_list = clsService.findC340M01A_ploanCtrNo_OrderByCreateTimeAsc(ploanCtrNo);
		int c340m01a_list_size = c340m01a_list.size(); 
		if(c340m01a_list_size > 1){
			//當idx=0 表示是 CreateTime 最早的
			C340M01A earliest_item = c340m01a_list.get(0);
			if(Util.equals(earliest_item.getOid(), meta.getOid())){
				return meta;
			}else{
				clsService.daoDelete(meta);
				return null;
			}			
		}else{
			return meta;
		}		
	}
	
	private void c160s01d_proc_ctrTypeC_doAct02_send(int batch_cnt){

		ISearch search = clsService.getMetaSearch();
		search.addSearchModeParameters(SearchMode.EQUALS, "ctrType", ContractDocConstants.C340M01A_CtrType.Type_C);
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", CreditDocStatusEnum.海外_已核准.getCode());
		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCtrStatus", "");
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime","");
		if(true){ //產  pdf 應該會花一些 resource, 在 batch_cnt 不要設太大
			search.setMaxResults(batch_cnt);
		}
		Page<? extends GenericBean> page = clsService.findPage(C340M01A.class, search);

		//~~~~~~~~~~~~~
		@SuppressWarnings("unchecked")
		List<C340M01A> c340m01a_list = (List<C340M01A>) page.getContent();;
		for(C340M01A c340m01a : c340m01a_list){
			try{
				_c160s01d_proc_ctrTypeC_doAct02(c340m01a);
			}catch(Exception e){
				logger.error("【_c160s01d_proc_ctrTypeC_doAct02】【c340m01a_oid='"+c340m01a.getOid()+"' , ctrNo="+Util.trim(c340m01a.getPloanCtrNo())+"】"+StrUtils.getStackTrace(e));
			}
		}
	}
	
	private void _c160s01d_proc_ctrTypeC_doAct02(C340M01A c340m01a)
	throws CapException{		
		boolean procResult = _c340m01a_ctrTypeC_sendToCust(c340m01a);
		if(procResult){				
			c340m01a.setPloanCtrStatus("9");			
			clsService.daoSave(c340m01a);				
		}		
	}
	
	private boolean _c340m01a_ctrTypeC_sendToCust(C340M01A c340m01a) 
	throws CapException{
		C160S01D c160s01d = clsService.findC160S01D_oid(c340m01a.getC160s01d_oid());
		C122M01A c122m01a = clsService.findC122M01A_mainId(c160s01d.getC122m01a_mainId());
		C122M01E c122m01e = clsService.findC122M01E_by_C122M01A_forChinaSteelCorpHQ(c122m01a);
		C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(c122m01a.getMainId(), c122m01a.getCustId(), c122m01a.getDupNo());
		C120S01A c120s01a = clsService.findC120S01A(c120m01a);
		if(c120s01a==null){
			throw new CapException("lost c120s01a", getClass());
		}
		if(Util.isEmpty(Util.trim(c120s01a.getEmail())) ){
			throw new CapException("lost c120s01a_email", getClass());
		}
		if(Util.isEmpty(Util.trim(c120s01a.getMTel())) ){
			throw new CapException("lost c120s01a_mTel", getClass());
		}else{
			if(!ClsUtility.isPureNumber(Util.trim(c120s01a.getMTel()))){
				throw new CapException("c120s01a_mTel formatError", getClass());	
			}	
		}
				
		//把2份PDF給合併成一份之後，寄送給客戶
		byte[] pdf_merged = cls3401Service.get_C340_ctrType_C_merge_Nto1_pdf(c340m01a);
		if(pdf_merged==null || pdf_merged.length==0){
			return false;
		}
		JSONObject rtnJSON = contractDocService.ploan_sendCtrTypeC(c340m01a, pdf_merged, c160s01d, c122m01a, c122m01e, c120m01a, c120s01a);
		if(!Util.equals(rtnJSON.optString("stat"), "ok")){
			throw new CapException("ploan_sendCtrTypeC Error", getClass());	
		}
		return true;
	}


	private boolean is_givenDate_lastBusinessDay_in_givenMonth(String lnf320_query_date){		
		Map<String, Object> map = misdbBASEService.get_LNF320(lnf320_query_date);
		if(MapUtils.isNotEmpty(map)){
			String nextDate = Util.trim(TWNDate.toAD((Date)map.get("LNF320_NEXT_DATE")));
			//在2022-09-30發現，次營業日是 2022-10-03 => 不能用下一個月的日期結尾是-01 來判斷
			//if(Util.isNotEmpty(nextDate) && nextDate.endsWith("-01") && Util.notEquals(StringUtils.substring(lnf320_query_date, 0, 7), StringUtils.substring(nextDate, 0, 7))){
			if(Util.isNotEmpty(nextDate) &&  Util.notEquals(StringUtils.substring(lnf320_query_date, 0, 7), StringUtils.substring(nextDate, 0, 7))){
				return true;
			}
		}
		return false;
	}
	
	private void call_center_amort_genH_and_D(String amort_ym, String localFilePath, String fileName_D, String fileName_H, Timestamp nowTS ) 
	throws IOException, Exception{
		boolean is_folder_exist = false;
		if(true){
			File _folder = new File(localFilePath);			
			if (_folder.exists()) {
				is_folder_exist = true;
			}else{
				try {
					FileUtils.forceMkdir(_folder);
					//FileUtils :: public static void forceMkdir(File directory) throws IOException {
					is_folder_exist = true;
				} catch (IOException ex) {
					logger.error(StrUtils.getStackTrace(ex));
				}
			}			
		}
		//=======================================
		if(is_folder_exist==false){
			logger.error("localFilePath="+localFilePath+", not exist");
			return;
		}
		//=======================================
		if(true){
			File pre_dataFile = new File(localFilePath, fileName_D);
			File pre_headerTxtFile = new File(localFilePath, fileName_H);
			
			//先刪除舊的.H , .D 檔案 ，再產生新的
			FileUtils.deleteQuietly(pre_dataFile);
			FileUtils.deleteQuietly(pre_headerTxtFile);
		}
		
		//=======================================	
		//準備寫入 .H 的{第1~8碼:起日}, {第9~16碼:迄日} 
		String dataPeriodBeg = amort_ym+"-01";
		String dataPeriodEnd = TWNDate.toAD(CrsUtil.get_month_last_date(CapDate.parseDate(amort_ym+"-01")));
		if(dataPeriodBeg.length()!=10 || dataPeriodEnd.length()!=10 || !dataPeriodEnd.startsWith(amort_ym) ){
			throw new Exception("dataPeriodBeg{"+dataPeriodBeg+"},dataPeriodEnd{"+dataPeriodEnd+"} lost data");
		}
		
		List<Map<String, Object>> el_rtn_list = eloandbBASEService.cls_call_center_amort_cnt(amort_ym);
		List<String> outList = new ArrayList<String>();
		
		int sum_all_brNoCnt = 0;
		for (Map<String, Object> map : el_rtn_list) {
			String brNo = MapUtils.getString(map, "BRNO");
			Integer cnt = MapUtils.getInteger(map, "CNT");
			//~~~~~~
			sum_all_brNoCnt += cnt;
			//~~~~~~
			StringBuffer newValBuffer = new StringBuffer("");
			
			newValBuffer.append("\"");
			newValBuffer.append(amort_ym+"-01"); // 資料月份
			newValBuffer.append("\"");
			newValBuffer.append(",");
			newValBuffer.append("\"");
			newValBuffer.append(brNo); // 分行代號
			newValBuffer.append("\"");
			newValBuffer.append(",");
			newValBuffer.append(String.valueOf(cnt)); // cnt
			//=============
			outList.add(newValBuffer.toString());
		}
		
		if(sum_all_brNoCnt==0 
			&& clsService.is_function_on_codetype("call_center_amort_disallow_0") ){ 
				/* 應不太可能出現這種情況 
					+ 若出現，應該99.9%是 {前1個batch：keep_data} 沒跑成功, 用 param 控制, 是否可繼續
					+ 只有 0.1% 才真的會這樣 。因此，若發生的 default 處理，是先擋住 
					=> 寧可先不上傳 , 也不要傳 errorData 出去（避免會計處的 BUMAS系統 抓到錯的資料）
				*/
				throw new Exception("sum_all_brNoCnt="+sum_all_brNoCnt);
		}
		
		File dataFile = new File(localFilePath, fileName_D);
		File headerTxtFile = new File(localFilePath, fileName_H);
		if (true) {
			// write header
			List<String> headerSB = new ArrayList<String>();
			if (true) {
				headerSB.add(StringUtils.replace(dataPeriodBeg, "-", "")); //起日
				headerSB.add(StringUtils.replace(dataPeriodEnd, "-", "")); //迄日
				headerSB.add(fileName_D); //.D的檔名
				headerSB.add(new SimpleDateFormat("yyyyMMddHHmmss").format(nowTS));
				headerSB.add(CapString.fillZeroHead(String.valueOf(outList.size()), 9)); //.D的總筆數
			}
			FileUtils.write(headerTxtFile, StringUtils.join(headerSB, ""), "UTF-8"); //把N個欄位，合併成1列，再寫到.H
			
			// write data			
			FileUtils.writeLines(dataFile, "UTF8", outList);
		}
	}
	
	private void call_center_amort_ftpH_and_DToDW(String localFilePath , String fileName_D , String fileName_H , String remoteDir)
	throws GWException , Exception{		
		
		//驗證 FTP 連線，若失敗，會拋出 GWException
		dwUCB1FTPClient.test();


		//檔案存在，才繼續執行
		File dataFile = new File(localFilePath, fileName_D);		
		File headerTxtFile = new File(localFilePath, fileName_H);
		boolean _dataFile_exists = dataFile.exists();
		boolean _headerTxtFile_exists = headerTxtFile.exists();
		if(_dataFile_exists && _headerTxtFile_exists){
			
		}else{
			throw new Exception("file not exists{"
					+ fileName_D +"="+ _dataFile_exists +", "
					+ fileName_H +"="+ _headerTxtFile_exists +"}");
		}
		
		
		boolean ftpParam_isBinaryFile = false;
		boolean ftpParam_delFtpFile = true;
		boolean ftpParam_isAddDate = false;
		
		dwUCB1FTPClient.send("", dataFile.getAbsolutePath(), 
				remoteDir, fileName_D, 
				ftpParam_isBinaryFile, ftpParam_delFtpFile, ftpParam_isAddDate);
		
		dwUCB1FTPClient.send("", headerTxtFile.getAbsolutePath(), 
				remoteDir, fileName_H, 
				ftpParam_isBinaryFile, ftpParam_delFtpFile, ftpParam_isAddDate);

		/*
		 * 上傳FTP的歷程，有寫到 com.bgwlog
		 select clientip, serviceId, txid, msgid, b.* 
		 from (select sno,clientip,serviceId, txid,msgid from com.bgwlog where serviceId ='DWUCB1FTP' and reqtime>='2022-09-15 16:10:00' 
		 ) a left outer join com.bgwdata b on a.sno=b.logsno  
		*/
	}

	private void call_ploan_autoUpdateBranch(String caseNo, String newBrNo)throws CapException{
		if(clsService.is_function_on_codetype("c340_useAssignBranchFlag")){
			if (Util.isNotEmpty(caseNo) && Util.isNotEmpty(newBrNo)) {
				JSONObject rtnJSON = cls1220Service.ploan_update_brNo(caseNo, newBrNo);
				if(!Util.equals(rtnJSON.optString("stat"), "ok")){
					throw new CapMessageException("送「線上貸款平台」失敗，請洽資訊處!", getClass());
				}
			}
		}
	}
	
	/**
	 * J-111-0622 修改為(不含個人授信額度以不動產十足擔保之授信案件)，所以不判斷R1 R2的不動產十足擔保，只判斷R3 R4非不動產的<BR>
	 * 配合「本行授信覆審作業須知」111.12.1修訂，請協助修改E-Loan系統企金與消金「授信覆審作業系統」如下修改內容：<BR>
	 * 授信覆審作業須知第十一條第九款不含個人授信額度以不動產十足擔保之授信案件<BR>
	 * 各分行抽樣母數資料=去年底的該類總件數<BR>
	 * SLMS-00172 消金覆審R14單一授信額度新臺幣一千萬元以下且為十足擔保授信或經信用保證基金保證成數七成以上
	 * @param limit_branch 特定分行
	 * @param force 強制執行: true,false
	 * @param updateOldData 更新舊資料: true,false
	 * @throws CapMessageException 
	 * 
	 */
	private void _act_elf491_R14(String limit_branch, boolean force, boolean updateOldData) throws CapMessageException { 
		Date sysMonth_1st = CrsUtil.get_sysMonth_1st();
		Calendar cal = Calendar.getInstance();
		int nowYYYY = cal.get(Calendar.YEAR);
		cal.setTime(sysMonth_1st);
		// 取得日期的月份
		int month = cal.get(Calendar.MONTH) + 1;
		// 判斷日期的月份是否為1月
		if (month == 1 || force) {
			if (updateOldData) {
				// 搬移YYYYY-1,YYYY-2資料及刪除YYYY-3資料
				misELF491Service.delete_R14("FFFFFFFFFF", "F");
				misELF491Service.update_R14("EEEEEEEEEE", "E", "FFFFFFFFFF", "F");
				misELF491Service.update_R14("DDDDDDDDDD", "D", "EEEEEEEEEE", "E");
			}
			// 取得現在的日期和時間
			// 將日期設定為去年12月1日
			cal.set(Calendar.YEAR, cal.get(Calendar.YEAR) - 1);
			cal.set(Calendar.MONTH, Calendar.DECEMBER);
			cal.set(Calendar.DAY_OF_MONTH, 1);
			// 取得該日期的中華民國年份和月份
			int year = cal.get(Calendar.YEAR) - 1911;
			month = cal.get(Calendar.MONTH) + 1;
			// 將年份和月份轉換成"0YYYMM"的格式
			String twYYYYMM = String.format("%04d%02d", year, month);
			List<Map<String, Object>> brnoCountlist = misELF491CService
					.selAllBrnoCount_R14(twYYYYMM);
			
			// J-112-0465 新增「○○區營運中心覆審類別「額度一千萬元以下十足擔保信保七成循環動用」抽樣之授信戶明細表
			// 統計各分行資料筆數
			Map<String, Integer> brnoCntMap = new HashMap<String, Integer>();
			int brnoCnt = 0;
			for (Map<String, Object> map : brnoCountlist) {

				String brno = Util.trim(MapUtils
						.getString(map, "ELF487A_BR_NO"));

				// 統計到另一家分行時，筆數重新歸0
				if (!brnoCntMap.containsKey(brno)) {
					brnoCnt = 0;
				}
				brnoCnt++;
				brnoCntMap.put(brno, Integer.valueOf(brnoCnt));
			}

			// 將分行總筆數資料，塞回LIST
			for (Map<String, Object> map : brnoCountlist) {
				String brno = Util.trim(MapUtils
						.getString(map, "ELF487A_BR_NO"));

				if (brnoCntMap.containsKey(brno)) {
					map.put("ELF487A_BR_NO_COUNT",
							Util.trim(MapUtils.getString(brnoCntMap, brno)));
				}
			}

			for (Map<String, Object> map : brnoCountlist) {
				String brno = Util.trim(MapUtils
						.getString(map, "ELF487A_BR_NO"));
				if (!limit_branch.isEmpty() && !limit_branch.equals(brno)) {
					continue;
				}
				int count = MapUtils.getIntValue(map, "ELF487A_BR_NO_COUNT");
				String elf491_remomo = String.format("%013d%013d", count, 0);
				ELF491 elf491_R14 = misELF491Service
						.selWithCustId_DDDDDDDDDD___dupNo_D(brno);
				if (elf491_R14 == null) {
					elf491_R14 = new ELF491();
					elf491_R14.setElf491_branch(brno);
					elf491_R14.setElf491_custid("DDDDDDDDDD");
					elf491_R14.setElf491_dupno("D");
					elf491_R14.setElf491_remomo(elf491_remomo);
					elf491_R14.setElf491_updater("SLMS172");
				}
				int exist_total = CrsUtil.getR8_1_total(elf491_R14
						.getElf491_remomo());
				int done_cnt = misELF491CService.countByBrNoRuleNoLrDateBegEnd(
						elf491_R14.getElf491_branch(), CrsUtil.R14, nowYYYY
								+ "-01-01", nowYYYY + "-12-31");
				elf491_R14.setElf491_remomo(CrsUtil
						.buildR8_1_total(exist_total)
						+ CrsUtil.buildR8_1_already(done_cnt));
				elf491_R14.setElf491_tmestamp(CapDate.getCurrentTimestamp());
				retrialService.upELF491_DelThenInsert(elf491_R14);
			}

			// J-112-0465 新增「○○區營運中心覆審類別「額度一千萬元以下十足擔保信保七成循環動用」抽樣之授信戶明細表
			// 產檔
			LMSBATCH lmsBatch = new LMSBATCH();

			Calendar calBngDate = Calendar.getInstance();
			calBngDate.set(Calendar.YEAR, calBngDate.get(Calendar.YEAR) - 1);
			calBngDate.set(Calendar.MONTH, Calendar.JANUARY);
			calBngDate.set(Calendar.DAY_OF_MONTH, 1);
			String bngDateStr = CapDate.formatDate(calBngDate.getTime(),
					"yyyy-MM-dd");
			Date bngDate = lms9511Service.getStartDateForLMSRPT(bngDateStr);

			Calendar calEndDate = Calendar.getInstance();
			calEndDate.set(Calendar.YEAR, calEndDate.get(Calendar.YEAR) - 1);
			calEndDate.set(Calendar.MONTH, Calendar.DECEMBER);
			calEndDate.set(Calendar.DAY_OF_MONTH, 31);
			String endDateStr = CapDate.formatDate(calEndDate.getTime(),
					"yyyy-MM-dd");
			Date endDate = lms9511Service.getStartDateForLMSRPT(endDateStr);

			Map<String, String> clsRptName = codetypeService
					.findByCodeType("lms9511v01_docType2");
			String remark = "";
			String userId = "SYS";
			String rptNo = UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表;

			List<String> uninNoList = new ArrayList<String>();
			uninNoList.add(UtilConstants.BankNo.北一區營運中心);
			uninNoList.add(UtilConstants.BankNo.桃竹苗區營運中心);
			uninNoList.add(UtilConstants.BankNo.中區營運中心);
			uninNoList.add(UtilConstants.BankNo.南區營運中心);
			for (String unitNo : uninNoList) {
				try {
					// 查詢單位轄下的所屬分行
					TreeMap<String, String> tm = retrialService
							.getBranch(unitNo);

					String tmpRemark = "";

					if (Util.isNotEmpty(tm) && tm.size() > 0) {

						// 將明細表資料組合，放入至tmpRemark
						JSONObject jsonObejct = new JSONObject();
						JSONArray jsonArray = new JSONArray();
						Map<String, String> brnoMap = new LinkedHashMap<String, String>();
						for (Map<String, Object> map : brnoCountlist) {

							if (tm.containsKey(map.get("ELF487A_BR_NO"))) {
								jsonObejct = new JSONObject();
								jsonObejct.put("ELF487A_BR_NO", Util
										.trim(MapUtils.getString(map,
												"ELF487A_BR_NO")));
								jsonObejct.put("ELF487A_CUST_ID", Util
										.trim(MapUtils.getString(map,
												"ELF487A_CUST_ID")));
								jsonObejct.put("ELF487A_DUP_NO", Util
										.trim(MapUtils.getString(map,
												"ELF487A_DUP_NO")));
								jsonObejct.put("CNAME", Util.trim(MapUtils
										.getString(map, "CNAME")));

								jsonArray.add(jsonObejct);

								brnoMap.put(Util.trim(MapUtils.getString(map,
										"ELF487A_BR_NO")), MapUtils.getString(
										map, "ELF487A_BR_NO"));
							}
						}
						tmpRemark = jsonArray.toString();

						// 取得該單位底下，符合這次抽樣的分行，組成REMARK內容，格式：brType=M;br=004|018|028或brType=S;br=004
						if (brnoMap.size() != 0) {
							if (brnoMap.size() == 1) {
								remark = CrsUtil.RPT_REMARK_BRTYPE + "=S;";
							} else if (brnoMap.size() > 1) {
								remark = CrsUtil.RPT_REMARK_BRTYPE + "=M;";
							}
							remark = remark + CrsUtil.RPT_REMARK_BR + "=";

							for (Map.Entry<String, String> entry : brnoMap
									.entrySet()) {
								remark = remark + entry.getKey() + "|";
							}
							remark = remark.substring(0, remark.length() - 1);
						}

					}

					// 確認是否有重複日期的報表
					String mainId = lms9511Service.checkAndReplaceBatchData(
							getClass().getSimpleName(), remark, unitNo, userId,
							rptNo, bngDate, endDate);

					// 新增報表
					lmsBatch = lms9511Service.addbatchData(remark, unitNo,
							rptNo, bngDate, endDate, mainId);

					if (lmsBatch != null) {
						boolean checkResult = false;
						if (Util.isNotEmpty(mainId)) {
							checkResult = true;
						}

						boolean execResult = lms9511Service.execBatchData(
								lmsBatch.getMainId(), checkResult);
						if (execResult) {
							// 產生檔案
							lms9511Service.createFileForAddBatch(lmsBatch,
									clsRptName, tmpRemark);
						}
					}

				} catch (Exception e) {
					logger.error(StrUtils.getStackTrace(e));
					throw new CapMessageException(
							"產生「○○區營運中心覆審類別「額度一千萬元以下十足擔保信保七成循環動用」抽樣之授信戶明細表失敗，請洽資訊處!",
							getClass());
				}
			}
		} else {
			logger.info("執行時間非1月或參數force不等於true，不執行R14抽樣母體計算");
		}
	}
}
