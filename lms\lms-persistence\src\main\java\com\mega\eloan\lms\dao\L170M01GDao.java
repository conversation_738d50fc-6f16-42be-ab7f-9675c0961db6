/* 
 * L170M01GDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L170M01G;

/** 覆審報告表簽章欄檔 **/
public interface L170M01GDao extends IGenericDao<L170M01G> {

	L170M01G findByOid(String oid);
	
	List<L170M01G> findByMainId(String mainId);
	
	L170M01G findByBranchTypeStaffJob(String mainId,String branchType,String staffJob);
	
	L170M01G findByUniqueKey(String mainId, String branchType, String branchId, String staffNo, String staffJob);

	List<L170M01G> findByIndex01(String mainId, String branchType, String branchId, String staffNo, String staffJob);
}