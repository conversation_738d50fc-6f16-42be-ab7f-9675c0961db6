/* 
 * DWCommonService.java
 * 
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2011 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.lms.eloandb.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;
import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.model.BELDFM02;

/**
 * <pre>
 * EloandbBASEService
 * </pre>
 * 
 * @since 2011/9/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/29,UFO,new
 *          </ul>
 */

public interface EloandbFsBASEService {

	public int update(String sql);

	public int update(String sql, Object... objects);

	/**
	 * 取得簽報書敘述說明檔
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findL120m01d(String bgnDate, String endDate);

	/**
	 * 取得額度明細表敘述說明檔
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findL140m01b(String bgnDate, String endDate);

	/**
	 * 取得借款人基本資料
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findL120s01ab(String bgnDate,
			String endDate);

	/**
	 * 取得取得額度明細表資料
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findL140m01a(String bgnDate, String endDate);

	/**
	 * 取得異常通報資料
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findL130m01a(String bgnDate, String endDate);

	/**
	 * 取得簽報書主檔資料
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findL120m01a(String bgnDate, String endDate);

	/**
	 * 取得會議決議檔資料
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findL120m01h(String bgnDate, String endDate);

	/**
	 * 依照SQLNAME 取得資料
	 * 
	 * @param sqlName
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findDataListBySqlName(String sqlName,
			String bgnDate, String endDate);
}
