/* 
 * L140M03ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M03A;

/** 個金額度明細表主檔補充資料檔 **/
public interface L140M03ADao extends IGenericDao<L140M03A> {

	L140M03A findByOid(String oid);

	L140M03A findByMainId(String mainId);

	L140M03A findByUniqueKey(String mainId);

	List<L140M03A> findByIndex01(String mainId);

	List<L140M03A> findByMainIds(List<String> mainIds);
}