package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * 財務比率 - The persistent class for the F101S01B database table.
 * </pre>
 * 
 * @since 2011/7/26
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/7/26,Sunkist Wang,new</li>
 *          </ul>
 */
@Generated(value = "Dali", date = "2011-07-26T10:49:31.500+0800")
@StaticMetamodel(F101S01B.class)
public class F101S01B_ extends RelativeMeta_ {
	public static volatile SingularAttribute<F101S01B, BigDecimal> ratio;
	public static volatile SingularAttribute<F101S01B, String> ratioNo;
	public static volatile SingularAttribute<F101S01B, F101M01A> f101m01a;
}
