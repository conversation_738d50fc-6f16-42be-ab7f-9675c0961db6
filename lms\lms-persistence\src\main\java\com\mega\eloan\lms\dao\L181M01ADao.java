/* 
 * L180M02ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L181M01A;

/** 覆審控制維護主檔 **/
public interface L181M01ADao extends IGenericDao<L181M01A> {

	L181M01A findByOid(String oid);

	L181M01A findByMainId(String mainId);

	List<L181M01A> findByDocStatus(String docStatus);

	L181M01A findByBranchCustId(String branch, String custId, String dupNo,
			String ctlType);

	L181M01A findInProcessData(String branch, String custId, String dupNo,
			String[] docStatusArr, String ctlType, String ownBrId);
}