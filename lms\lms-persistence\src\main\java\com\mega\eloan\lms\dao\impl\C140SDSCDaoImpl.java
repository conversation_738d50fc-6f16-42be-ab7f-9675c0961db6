/* 
 * C140SDSCDaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.model.GenericBean;

import com.mega.eloan.lms.dao.C140SDSCDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C140M04B;
import com.mega.eloan.lms.model.C140SDSC;
import com.mega.eloan.lms.model.C140SDSC_;

/**
 * <pre>
 * 徵信調查報告書副檔 註記說明
 * </pre>
 * 
 * @since 2011/9/21
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
@Repository
public class C140SDSCDaoImpl extends LMSJpaDao<C140SDSC, String> implements
		C140SDSCDao {

	public List<C140SDSC> findByMainPid(String pid, String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140SDSC_.mainId.getName(), mainId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140SDSC_.pid.getName(), pid);
		
		return find(search);
	}// ;
	
	public List<C140SDSC> findByMainPidTab(String pid, String mainId, String tab) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140SDSC_.mainId.getName(), mainId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140SDSC_.pid.getName(), pid);
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140SDSC_.tab.getName(), tab);
		
		return find(search);
	}// ;

	public C140SDSC findByMainPidTab(String uid, String mainId, String tab,
			String fieldId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140SDSC_.mainId.getName(), mainId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140SDSC_.pid.getName(), uid);
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140SDSC_.tab.getName(), tab);
		search.addSearchModeParameters(SearchMode.EQUALS,
				C140SDSC_.fieldId.getName(), fieldId);
		return findUniqueOrNone(search);
	}
	
	@Override
	public int deleteByMeta(GenericBean meta) {
		Query query = entityManager
				.createNamedQuery("ces140sdsc.deleteByMainIdAndPid");
		if(meta instanceof C140M04B){
			query.setParameter("mainId", ((C140M04B)meta).getMainId());
			query.setParameter("pid", ((C140M04B)meta).getUid());
		}
		
		return query.executeUpdate();
	}

}// ;
