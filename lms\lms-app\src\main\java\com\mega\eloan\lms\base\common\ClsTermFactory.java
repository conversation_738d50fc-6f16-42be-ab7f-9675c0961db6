package com.mega.eloan.lms.base.common;

import java.math.BigDecimal;

import tw.com.jcs.common.Util;

public class ClsTermFactory {
	public IClsTerm getCreditLoan(String termGroup, BigDecimal income, String s01r_grade, String reduct_fg, String pConBegEnd_fg){
		if(Util.equals(termGroup, "GA")){
			return new ClsTermGACreditLoan(income, s01r_grade, reduct_fg, pConBegEnd_fg);
		}else if(Util.equals(termGroup, "GB")){
			return new ClsTermGBCreditLoan(income, s01r_grade, reduct_fg, pConBegEnd_fg);
		}else if(Util.equals(termGroup, "N")){
			return new ClsTermNormCreditLoan(income, s01r_grade, reduct_fg, pConBegEnd_fg);
		}else if(Util.equals(termGroup, "S")){
			return new ClsTermSmallCreditLoan(income, s01r_grade, reduct_fg, pConBegEnd_fg);
		}
		return new ClsTermEmptyCreditLoan();
	}
}
