<?xml version="1.0" encoding="UTF-8"?>
<beans:beans xmlns="http://www.springframework.org/schema/security"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:beans="http://www.springframework.org/schema/beans"
	xsi:schemaLocation="
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/security https://www.springframework.org/schema/security/spring-security.xsd">

    <http pattern="/img/**" security="none" />
    <http pattern="/**/images/**" security="none" />
    <http pattern="/js/**" security="none" />
    <http pattern="/jquery/**" security="none" />
    <http pattern="/css/**" security="none" />
    <http pattern="/pagejs/**" security="none" />
    
	<http auto-config="false" access-decision-manager-ref="accessDecisionManager" entry-point-ref="loginEntryPoint" use-expressions="false">
		<headers>
            <frame-options policy="SAMEORIGIN" />
            <content-security-policy policy-directives="script-src 'self' 'unsafe-inline' 'unsafe-eval'" />
        </headers>
        <access-denied-handler error-page="/app/error/message" />
        <csrf request-matcher-ref="csrfMatcher" />
		<intercept-url pattern="/servlet/*" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/app/i18n/js*" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/app/error/message*" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/app/login*" access="IS_AUTHENTICATED_ANONYMOUSLY"/>
		<intercept-url pattern="/app/scheduler*" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/app/helpdesk*" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/app/remoteservice*" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/app/oracle" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/app/logout*" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/app/home/<USER>" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/app/sso/dfs*" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		
		<intercept-url pattern="/app/simple/FileProcessingPageForNotes" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/app/simple/FileProcessingPageForFullSearch" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/app/simple/FileProcessingPageForFullSearchLNS" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/app/simple/FileProcessingPageForFullSearchLMS" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<!--J-111-0397_05097_B1001 Web e-Loan國內企?授信新增權加權??性?產(RWA)相關計算與預約機制-->
		<intercept-url pattern="/app/simple/FileProcessingPageForLms1201R46" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<!--J-113-0490_07623_B1001 公司訪談紀錄表批次調閱  1.建議新增鍵入統編即可調閱分行完成之「公司訪談紀錄表」。2.建議增列批次列印功能，例如輸入期間及10筆統編，即可產生「公司訪談紀錄表」之PDF檔。-->
		<intercept-url pattern="/app/simple/FileProcessingPageForLmsBat0057" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/app/lmsdataconvertpage" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/app/clsdataconvertpage" access="IS_AUTHENTICATED_ANONYMOUSLY" />

		<intercept-url pattern="/**" access="EL" />
		<anonymous enabled="false" />

		<custom-filter position="CONCURRENT_SESSION_FILTER" ref="concurrencyFilter" />
        <custom-filter ref="anonymousAuthFilter" position="ANONYMOUS_FILTER" />
		<custom-filter ref="megaAuthenticationProcessingFilter" position="FORM_LOGIN_FILTER" />
		<session-management session-authentication-strategy-ref="sas" />
		<logout logout-url="/app/logout" success-handler-ref="megaLogoutHandler" />

	</http>
	
	<beans:bean id="csrfMatcher" class="org.springframework.security.web.util.matcher.AndRequestMatcher">
		<beans:constructor-arg>
			<beans:list value-type="org.springframework.security.web.util.matcher.RequestMatcher">
				<beans:value>#{T(org.springframework.security.web.csrf.CsrfFilter).DEFAULT_CSRF_MATCHER}</beans:value>
                <beans:bean class="org.springframework.security.web.util.matcher.NegatedRequestMatcher">
                    <beans:constructor-arg>
                        <beans:bean class="org.springframework.security.web.util.matcher.AntPathRequestMatcher">
                            <beans:constructor-arg name="pattern" value="/app/remoteservice*" />
                            <beans:constructor-arg name="httpMethod" value="POST" />
                        </beans:bean>
                    </beans:constructor-arg>
                </beans:bean>
                <beans:bean class="org.springframework.security.web.util.matcher.NegatedRequestMatcher">
                    <beans:constructor-arg>
                        <beans:bean class="org.springframework.security.web.util.matcher.AntPathRequestMatcher">
                            <beans:constructor-arg name="pattern" value="/app/helpdesk*" />
                        </beans:bean>
                    </beans:constructor-arg>
                </beans:bean>
                <beans:bean class="org.springframework.security.web.util.matcher.NegatedRequestMatcher">
                    <beans:constructor-arg>
                        <beans:bean class="org.springframework.security.web.util.matcher.AntPathRequestMatcher">
                            <beans:constructor-arg name="pattern" value="/app/ssoverify" />
                            <beans:constructor-arg name="httpMethod" value="POST" />
                        </beans:bean>
                    </beans:constructor-arg>
                </beans:bean>
                <beans:bean class="org.springframework.security.web.util.matcher.NegatedRequestMatcher">
                    <beans:constructor-arg>
                        <beans:bean class="org.springframework.security.web.util.matcher.AntPathRequestMatcher">
                            <beans:constructor-arg name="pattern" value="/app/scheduler*" />
                            <beans:constructor-arg name="httpMethod" value="POST" />
                        </beans:bean>
                    </beans:constructor-arg>
                </beans:bean>
                <beans:bean class="org.springframework.security.web.util.matcher.NegatedRequestMatcher">
                    <beans:constructor-arg>
                        <beans:bean class="org.springframework.security.web.util.matcher.AntPathRequestMatcher">
                            <beans:constructor-arg name="pattern" value="/app/api/*" />
                        </beans:bean>
                    </beans:constructor-arg>
                </beans:bean>
			</beans:list>
		</beans:constructor-arg>
	</beans:bean>

	<beans:bean id="concurrencyFilter" class="org.springframework.security.web.session.ConcurrentSessionFilter">
		<!-- <beans:property name="sessionRegistry" ref="sessionRegistry" />
		<beans:property name="expiredUrl" value="/app/error/message?PRE_VALIDATE_MESSAGE=loginOther" /> -->
		<beans:constructor-arg ref="sessionRegistry"/>
		<beans:constructor-arg value="/app/error/message?PRE_VALIDATE_MESSAGE=loginOther"/>
	</beans:bean>
	
    <beans:bean id="anonymousAuthFilter" class="com.mega.sso.webapp.EloanAnonymousAuthenticationFilter">
        <beans:constructor-arg value="cap" />
    </beans:bean>

	<beans:bean id="sas" class="org.springframework.security.web.authentication.session.ConcurrentSessionControlAuthenticationStrategy">
		<beans:constructor-arg name="sessionRegistry" ref="sessionRegistry" />
		<beans:property name="maximumSessions" value="10" />
	</beans:bean>

	<beans:bean id="sessionRegistry"
		class="org.springframework.security.core.session.SessionRegistryImpl" />

	<beans:bean id="megaAuthenticationProcessingFilter"
		class="com.mega.sso.webapp.MegaSSOAuthenticationProcessingFilter">
		<beans:property name="filterProcessesUrl" value="/app/ssoverify" />
		<beans:property name="authenticationManager" ref="authenticationManager" />
		<beans:property name="authenticationFailureHandler" ref="failureHandler" />
		<beans:property name="authenticationSuccessHandler" ref="successHandler" />
		<beans:property name="sessionAuthenticationStrategy" ref="sas" />
	</beans:bean>

	<authentication-manager alias="authenticationManager">
		<authentication-provider ref="megaUserAuthenicationProvider"/>
	</authentication-manager>

	<beans:bean id="megaUserAuthenicationProvider" 
		class="com.mega.sso.providers.MegaUserAuthenticationProvider">
		<beans:property name="forcePrincipalAsString" value="true" />
		<beans:property name="userDetailsService" ref="userDetailsService" />
		<beans:property name="megaSSO" ref="MegaSSOUtils" />
	</beans:bean>

	<beans:bean id="userDetailsService" class="com.mega.sso.service.MegaSSOUserService">
		<beans:property name="getRoleBySSO" value="false" />
		<beans:property name="megaSSO" ref="MegaSSOUtils" />
	</beans:bean>

	<beans:bean id="loginEntryPoint" class="com.mega.sso.webapp.MegaAuthenticationEntryPoint">
		<!-- <beans:property name="loginFormUrl" value="/app/login" />
		<beans:property name="forceHttps" value="false" /> -->
        <beans:constructor-arg value="/app/login" />
        <beans:property name="forceHttps" value="false" />
	</beans:bean>

	<beans:bean id="megaLogoutHandler" class="com.mega.eloan.common.web.MegaLogoutHandler">
		<beans:property name="defaultTargetUrl" value="/app/login" /><!-- logout-success-url -->
		<beans:property name="alwaysUseDefaultTargetUrl" value="true" />
		<beans:property name="megaSSO" ref="MegaSSOUtils" />
	</beans:bean>

	<beans:bean id="MegaSSOUtils" class="${sso.utilClass}">	
		<beans:property name="sysId" value="${sso.systemId}_${systemId}"/>
		<beans:property name="ssoURL" value="${sso.url}" />
		<beans:property name="ssoSystemKey" value="${sso.systemId}" />
		<beans:property name="ssoValidateInterval" value="${sso.validateInterval}" />
		<beans:property name="ssoSessionInterval" value="${sso.sessionInterval}" />
	</beans:bean>

	<beans:bean id="accessDecisionManager" class="org.springframework.security.access.vote.AffirmativeBased">
		<beans:constructor-arg name="decisionVoters">
			<beans:list>
				<beans:bean class="com.mega.sso.vote.MegaSSOLightVoter">
					<beans:property name="ssoLigntChecker" ref="megaSSOLightService" />
				</beans:bean>
				<beans:bean class="com.mega.sso.vote.MegaPermissionVoter">
					<beans:property name="rolePrefix" value="EL" />
					<beans:property name="ignoreHandlers">
						<beans:set>
                            <beans:value>i18nhandler</beans:value>
							<beans:value>commonformhandler</beans:value>
							<beans:value>codetypehandler</beans:value>
							<beans:value>Simplefileuploadhandler</beans:value>
							<beans:value>simplefiledwnhandler</beans:value>
							<beans:value>amlrelatefileuploadhandler</beans:value>
							<beans:value>lms1401fileuploadhandler</beans:value>
							<beans:value>lms2105v01fileuploadhandler</beans:value>
							<beans:value>lms1201fileuploadhandler</beans:value>
							<beans:value>lmscopycntrdocfileuploadhandler</beans:value>
							<beans:value>lmsfileuploadhandler</beans:value>
							<beans:value>lms1601fileuploadhandler</beans:value>
							<beans:value>lms8500m01fileuploadhandler</beans:value>
						</beans:set>
					</beans:property>
				</beans:bean>
                <beans:bean class="org.springframework.security.access.vote.AuthenticatedVoter" />
			</beans:list>
		</beans:constructor-arg>
        <beans:property name="allowIfAllAbstainDecisions" value="false" />
	</beans:bean>

	<beans:bean id="megaSSOLightService" class="com.mega.sso.service.MegaSSOLightService">
		<beans:property name="megaSSO" ref="MegaSSOUtils" />
	</beans:bean>

	<beans:bean id="successHandler"
		class="org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler">
		<beans:property name="defaultTargetUrl" value="/app/home/<USER>" />
		<beans:property name="alwaysUseDefaultTargetUrl" value="true" />
	</beans:bean>
	<beans:bean id="failureHandler"
		class="org.springframework.security.web.authentication.SimpleUrlAuthenticationFailureHandler">
		<beans:property name="defaultFailureUrl" value="/app/login?error" />
	</beans:bean>

	<beans:bean id="loggerListener"
		class="org.springframework.security.authentication.event.LoggerListener" />

</beans:beans>
