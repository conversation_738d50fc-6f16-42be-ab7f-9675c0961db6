$(document).ready(function(){
    var wrapFormId = "#wrapForm";
    // ie下 需要有tag結束符號，否則不能work
    var wrapForm = "<form id='wrapForm'></form>";
    
    $("#tAddr,#statementAddrFrom,#statementAddrTo").change(function(){
        var value = $(this).val().toFull();
        $(this).val(value);
    });
    
    $("#groupLoanTabs").tabs({ //需先檢核成功後才能切tab,否則儲存時,無法顯示其它被隱藏的tab的錯誤
        select: function(event, ui){
            return $(wrapFormId).valid();
        }
    });
    
    
    
    var grid = $("#groupLoanDetail").iGrid({
        height: 200,
        width: 700,
        autowidth: false,
        handler: "lms1915gridhandler",
        action: "queryL192M01B",
        postData: {
            custType: "1"
        },
        rownumbers: true,
        colModel: [{
            colHeader: i18n.lms1915m01['lms1915s02.007'],// "身份證"
            name: "custId",
            align: "center",
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.lms1915m01['lms1915s02.008'],// "借款人姓名",
            name: "custName",
            align: "center"
        }, {
            name: "oid",
            hidden: "true"
        }, {
            name: "mainId",
            hidden: "true"
        }]
    });
    
    function openDoc(cellvalue, options, rowObject){
        $("#groupLoanTabsShow").thickbox({
            title: i18n.lms1915m01['lms1915s03.009'],// 申請內容
            modal: true,
            width: 900,
            height: 600,
            align: 'left',
            valign: 'top',
            open: function(){
            
                $("#groupLoanTabs").wrap(wrapForm);
                $(wrapFormId).reset();
                $.ajax({
                    handler: "lms1915m01formhandler",
                    data: {
                        l192m01bOid: rowObject.oid,
                        formAction: "queryL192M01B"
                    },
                    success: function(responseData){
                        $('#groupLoanTabs').injectData(responseData);
                        
                        appResult.setGridParam({
                            postData: {
                                mainCustId: $("#mainCustId").val(),
                                mainDupNo: $("#mainDupNo").val()
                            }
                        });
                        grid2.setGridParam({
                            postData: {
                                mainCustId: $("#mainCustId").val(),
                                mainDupNo: $("#mainDupNo").val(),
                                custType: "2"
                            }
                        });
                        appResult.trigger("reloadGrid");
                        
                        grid2.trigger("reloadGrid");
                    }
                });
                $("#groupLoanTabs").tabs({
                    selected: 0
                });
            },
            close: function(){
            
            },
            buttons: API.createJSON([{
                key: i18n.def['sure'],
                value: function(){
                    if ($(wrapFormId).valid()) {
                        $.ajax({
                            handler: "lms1915m01formhandler",
                            action: "updateL192M01B",
                            data: $.extend($("#groupLoanTabs").serializeData(), {
                                l192m01bOid: $("#l192m01bOid").val()
                            }),
                            success: function(responseData){
                                grid.trigger("reloadGrid");
                            }
                        });
                        $("#groupLoanTabs").unwrap(wrapForm);
                        $.thickbox.close();
                    }
                    
                }
            }, {
                key: i18n.def['cancel'],
                value: function(){
                    $("#groupLoanTabs").unwrap(wrapForm);
                    $.thickbox.close();
                }
            }])
        });
    }
    
    var grid2 = $("#result2").iGrid({
        height: 50,
        width: "100%",
        autowidth: true,
        localFirst: true,
        handler: "lms1915gridhandler",
        action: "queryL192M01B2",
        postData: {
            custType: "2"
        },
        rownumbers: true,
        colModel: [{
            colHeader: i18n.lms1915m01['lms1915s02.007'],// "身份證"
            name: "custId",
            align: "center"
        }, {
            colHeader: i18n.lms1915m01['lms1915s02.008'],// "借款人姓名",
            name: "custName",
            align: "center"
        }, {
            colHeader: i18n.lms1915m01['lms1915s02.009'],// "(職業)行業別",
            name: "posi",
            align: "center"
        }, {
            colHeader: i18n.lms1915m01['lms1915s03.013'],// "幣別(收入)",
            name: "incomeCurr",
            align: "center"
        }, {
            colHeader: i18n.lms1915m01['lms1915s02.010'],// "收入",
            name: "incomeAmt",
            align: "center"
        }, {
            name: "oid",
            hidden: "true"
        }, {
            name: "mainId",
            hidden: "true"
        }]
    });
    var appResult;
    appResult = $("#appResult").iGrid({
        localFirst: true,
        height: 200,
        width: 800,
        autowidth: false,
        handler: "lms1915gridhandler",
        action: "queryL192S01A",
        rownumbers: true,
        colModel: [{
            colHeader: i18n.lms1915m01['lms1915s03.003'],// "帳號",
            name: "accNo",
            align: "center",
            formatter: 'click',
            onclick: openLoanDoc
        }, {
            colHeader: i18n.lms1915m01['lms1915s03.005'],// "額度",
            name: "quotaAmt",
            align: "center",
            formatter: 'currency',
            formatoptions: {
                decimalSeparator: ",",
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 2
            }
        }, {
            colHeader: i18n.lms1915m01['lms1915s03.006'],// "餘額",
            name: "balAmt",
            align: "center",
            formatter: 'currency',
            formatoptions: {
                decimalSeparator: ",",
                thousandsSeparator: ",",
				
                decimalPlaces: 0
            }
        }, {
            colHeader: i18n.lms1915m01['lms1915s03.014'],// "動用起日",
            name: "fromDate",
            align: "center"
        }, {
            colHeader: i18n.lms1915m01['lms1915s03.015'],// "動用迄日",
            name: "endDate",
            align: "center"
        }, {
            name: "balDate",
            hidden: "true"
        }, {
            name: "oid",
            hidden: "true"
        }, {
            name: "mainId",
            hidden: "true"
        }],
        gridComplete: function(){
            if (appResult) {
                var ret = appResult.getRowData(1);
                $("#bal_date").text(ret.balDate);
            }           
        }
    });
    
    function openLoanDoc(cellvalue, options, rowObject){
    
        $("#loanDatas").thickbox({
            title: i18n.lms1915m01['lms1915s03.009'],//申請內容
            width: 640,
            height: 500,
            align: 'left',
            valign: 'top',
            open: function(){
                $.ajax({
                    handler: "lms1915m01formhandler",
                    data: {
                        l192s01aoid: rowObject.oid,
                        formAction: "getL192S01A"
                    },
                    success: function(responseData){
                        $('#loanData').injectData(responseData);
                    }
                });
            },
            close: function(){
                $('#loanData .text-only').val("");
            },
            buttons: API.createJSON([{
                key: i18n.def['sure'],
                value: function(){
                    $.thickbox.close();
                }
            }])
        });
    }
    
});
