/* 
 * LMS1505Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service;

/* 

 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
import java.util.Map;

import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L150M01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

/**
 * <pre>
 * 小放會會議紀錄介面
 * </pre>
 * 
 * @since 2011/10/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/21,REX,new
 *          </ul>
 */
public interface LMS1505Service extends ICapService {

	/**
	 * 小放會議紀錄
	 * 
	 * @param oid
	 *            文件編號
	 * @return L150M01A
	 */
	L150M01A findL150m01aByOid(String oid);

	/**
	 * 取得小放會grid資料
	 * 
	 * @param search
	 *            條件
	 * @return Page
	 */
	Page<L150M01A> findPage(ISearch search);

	/**
	 * 取得使用者人員資料
	 * 
	 * @param brNo
	 *            分行代號
	 * @param search
	 *            搜尋條件
	 * @return Page<Map<String, Object>> 人員清單
	 */
	public Page<Map<String, Object>> findPageby(String brNo, ISearch search);

	/**
	 * 儲存小放會
	 * 
	 * @param model
	 *            model
	 * @param mainOid
	 */
	void saveL150m01a(L150M01A model);

	public void deleteL150m01aList(String[] oids);

	/**
	 * 取得簽報書
	 * 
	 * @param oid
	 *            文件編號
	 * @return 案件簽報書
	 */
	public L120M01A findL120M01AByOid(String oid);

}