package com.mega.eloan.lms.mfaloan.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF492;

/**
 * <pre>
 * 覆審明細檔
 * </pre>
 * 
 * @since 2013/3/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/7,EL08034,new
 *          </ul>
 */
public interface MisELF492Service {
	public ELF492 findByPk(String elf492_branch, String elf492_custid,
			String elf492_dupno, String elf492_cntrno, Date elf492_lrdate);

	public Date selMaxLrDateByBrNoCustIdDupNoCntrNo(String brNo, String custId,
			String dupNo, String cntrNo);

	public List<ELF492> selByBrNoCntrNo(String brNo, String cntrNo);

	public Map<String, Object> selStatsDataByBranch_lrDate(
			String elf492_branch, String elf492_lrdate_s, String elf492_lrdate_e);

	/**
	 * J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
	 * 
	 * @param elf492_branch
	 * @param elf492_lrdate_s
	 * @param elf492_lrdate_e
	 * @param apprId
	 * @return
	 */
	public Map<String, Object> selStatsDataByBranch_lrDate_By_ApprId(
			String elf492_branch, String elf492_lrdate_s,
			String elf492_lrdate_e, String elf492_apprId);

	public ELF492 selMaxUckdDt(String elf492_branch, String elf492_custid,
			String elf492_dupno);
}
