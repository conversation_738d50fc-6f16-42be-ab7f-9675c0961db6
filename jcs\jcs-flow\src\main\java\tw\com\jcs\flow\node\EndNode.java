package tw.com.jcs.flow.node;

import tw.com.jcs.flow.core.FlowInstanceImpl;

/**
 * <pre>
 * 流程節點(結束)
 * </pre>
 * 
 * @since 2023年1月9日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月9日
 *          </ul>
 */
public class EndNode extends FlowNode {

    /**
     * 結束目前的流程節點，若有父流程則判斷父流程是否sub-process出來的， 如果父流程是sub-process出來的，應該next
     * 
     * @param instance
     */
    @Override
    public void next(FlowInstanceImpl instance) {
        instance.handle();
        finishCurrentNode(instance);
        changeToThisNode(instance);
        finishCurrentNode(instance);

        if (instance.getParentInstanceId() != null) {
            // 流程結束，如果有父流程，則從父流程中移除子流程
            FlowInstanceImpl parent = (FlowInstanceImpl) instance.getEngine().getPersistence().getInstance(instance.getParentInstanceId());

            String state = parent.removeSubInstance(instance.getId());

            // 如果父流程是sub-process出來的，應該next
            if (parent.getDefinition().getNodes().get(state) instanceof SubProcessNode) {
                parent.next();
            }
        } else {
            instance.getEngine().getPersistence().moveToHistory(instance.getId());
        }
    }

}
