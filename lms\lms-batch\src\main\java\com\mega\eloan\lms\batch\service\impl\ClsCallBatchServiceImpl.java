package com.mega.eloan.lms.batch.service.impl;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.gwclient.EmailClient;
import com.mega.eloan.common.gwclient.PLOAN018;
import com.mega.eloan.common.gwclient.PLOAN018.PLOAN018_paymentInfo;
import com.mega.eloan.common.gwclient.PLOANGwClient;
import com.mega.eloan.common.gwclient.RPAFTPClient;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.MISRows;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.service.CLS1161Service;
import com.mega.eloan.lms.cls.service.CLS3401Service;
import com.mega.eloan.lms.dao.C160M01ADao;
import com.mega.eloan.lms.dao.C160M01BDao;
import com.mega.eloan.lms.dao.C160S01FDao;
import com.mega.eloan.lms.dao.C340M01ADao;
import com.mega.eloan.lms.dao.C340M01BDao;
import com.mega.eloan.lms.dao.C340M01CDao;
import com.mega.eloan.lms.dao.L290M01ADao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.bean.ELF460;
import com.mega.eloan.lms.mfaloan.bean.ELF606;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01B;
import com.mega.eloan.lms.model.C160M03A;
import com.mega.eloan.lms.model.C160S01F;
import com.mega.eloan.lms.model.C160S03A;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.C340M01B;
import com.mega.eloan.lms.model.C340M01C;
import com.mega.eloan.lms.model.L140M01R;
import com.mega.eloan.lms.model.L290M01A;
import com.mega.eloan.lms.rpt.service.LMS9511Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.Workbook;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

@Service("clsCallBatchServiceImpl")
public class ClsCallBatchServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger logger = LoggerFactory
			.getLogger(ClsCallBatchServiceImpl.class);

	@Resource
	CLS1161Service cls1161Service;

	@Resource
	DocFileService docFileService;

	@Resource
	RPAFTPClient rpaFTPClient;

	@Resource
	CLSService clsService;

	@Resource
	L290M01ADao l290m01aDao;

	@Resource
	C340M01ADao c340m01aDao;

	@Resource
	C340M01CDao c340m01cDao;

	@Resource
	C160M01ADao c160m01aDao;

    @Resource
    C160M01BDao c160m01bDao;

	@Resource
	C160S01FDao c160s01fDao;

	@Resource
	C340M01BDao c340m01bDao;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	BranchService branchService;

	@Resource
	LMS9511Service lms9511Service;

	@Resource
	CLS3401Service cls3401Service;

	@Resource
	private SysParameterService sysParameterService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	EmailClient emailClient;

	@Resource
	private PLOANGwClient pLoanGwClient;

	@Override
	public JSONObject execute(JSONObject json) {
		boolean isSuccess = false;
		JSONObject result = null;
		JSONObject rq = json.getJSONObject("request");

		String act = rq.optString("act");
		String msg = "";
		
		try {
			if (Util.equals("procC160M03A_xls", act)) {
				String excelId = Util.trim(rq.optString("excelId"));
				String mainOid = Util.trim(rq.optString("mainOid"));
				procC160M03A_xls(excelId, mainOid);
			}
			else if (Util.equals("procL290M01A_tdcc_xls", act)) {
				String downloadYYYYMM = Util.trim(rq.optString("downloadYYYYMM"));
				procL290M01A_tdcc_xls(downloadYYYYMM);
			}
			else if (Util.equals("procCLS722NonAppropriation", act)) {
				String begDate = Util.trim(rq.optString("begDate"));
				String endDate = Util.trim(rq.optString("endDate"));
				if(Util.isEmpty(begDate)){
					begDate = "2021-04-01";
				}
				if(Util.isEmpty(endDate)){
					SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
					Calendar end=Calendar.getInstance();
					end.add(Calendar.DATE, -1);
					endDate = format.format(end.getTime());
				}
//				procCLS722NonAppropriation(begDate,endDate);
			}
			else if (Util.equals("procC340M01A_to_ELF460", act)) {
				procC340M01A_to_ELF460();
			}
			else if (Util.equals("procC340M01A_to_inValidOnlineCtr", act)) {
				procC340M01A_to_inValidOnlineCtr();
			}
			else if (Util.equals("procC340M01A_to_notifypLoanSendMail", act)) {
				procC340M01A_to_notifypLoanSendMail();
			}
			isSuccess = true;
		} catch (Throwable e) {
			msg = e.getMessage();
			logger.error(msg, e);
			isSuccess = false;
		}

		if (isSuccess) {
			result = WebBatchCode.RC_SUCCESS;// 此json object 內已包含 SUCCESS
			result.element(WebBatchCode.P_RESPONSE,
					result.get(WebBatchCode.P_RC_MSG));
		} else {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, msg);
			result.element(WebBatchCode.P_RESPONSE, msg);
		}
		
		return result;
	}
	
	private void procC160M03A_xls(String excelId, String mainOid )
	throws Exception{
		
		
		C160M03A meta = cls1161Service.findModelByOid(C160M03A.class, mainOid);
		if(meta==null){
			throw new CapMessageException("mainOid=["+mainOid+"]", getClass());
		}
		DocFile docFile = docFileService.read(excelId);
		if (docFile == null) {
			throw new CapMessageException("請先上傳EXCEL", getClass());
		}
		
		WritableWorkbook workbook = null;
		String filePath = docFileService.getFilePath(docFile);
		String errMsg = "";
		try {

			workbook = Workbook.createWorkbook(new File(filePath),
					Workbook.getWorkbook(new File(filePath)));
			WritableSheet sheet = workbook.getSheet(0);

			
			if (meta != null) {
				meta.setExcelId(excelId);
				
				int maxSeq = cls1161Service.findC160S03AMaxSeqNoByCntrNo(meta.getCntrNo());
				
				List<C160S03A> old_List = cls1161Service.findC160S03AByMainIdOrderBySeqNo(meta.getMainId());
				cls1161Service.delete(old_List);
				// 取得EXCEL資料
				boolean rtnParse = false;
				
				try{
					rtnParse = cls1161Service.parseC160S03A(sheet, meta, null, maxSeq);	
				}catch(Exception e){
					errMsg = StrUtils.getStackTrace(e);
				}finally{
					if(rtnParse){
						
					}else{						
						if(Util.isEmpty(errMsg)){
							errMsg = "parse fail";
						}
					}
				}
			}
			
			workbook.write();
			workbook.close();
		} catch (Exception e) {
			logger.error("parseExcel EXCEPTION!!", e);
			throw new CapMessageException(e, getClass());
		} finally {
			try {
				if (workbook != null) {
					workbook.close();
					workbook = null;
				}
			} catch (Exception e) {
				logger.debug("Workbook close EXCEPTION!", getClass());
			}
		}
	
		if(Util.isNotEmpty(errMsg)){
			throw new CapMessageException(errMsg, getClass());
		}
		
	}

	private void procL290M01A_tdcc_xls(String downloadYYYYMM )
			throws Exception{
		final int dataStartColumn = 0;//使用者開始輸入欄位
		String msgId = IDGenerator.getUUID();
		Calendar today = Calendar.getInstance();
		int year = today.get(Calendar.YEAR);
		Date strDate1 = today.getTime();
		DateFormat df1 = new SimpleDateFormat("yyyyMM");
		String thisMonthFile= Util.trim(df1.format(strDate1))+"01";
		if(Util.isNotEmpty(downloadYYYYMM)){
			thisMonthFile = downloadYYYYMM+"01";
		}
		String ServerDir = "ESG_infoDownload";
		String esgFileName = "ESGDataDownload" + "_" + thisMonthFile + ".csv";
		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + year + File.separator + ServerDir + File.separator;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		//檢查foloder是否存在
		File localFileFolder = new File(localFilePath);
		if (!localFileFolder.exists()) {
			FileUtils.forceMkdir(localFileFolder);
		}

		if (clsService.is_function_on_codetype("ESG_RPA_DownLoad")) {
			//自RPA FTP下載ESG檔案
			//rpaFTPClient.setServerDir(ServerDir);
			String[] ftpFiles = rpaFTPClient.list(msgId, ServerDir);
			for (String ftpFile : ftpFiles) {
				if (ftpFile.toUpperCase().endsWith(".CSV")) {
					if (ftpFile.contains(thisMonthFile)) {
						rpaFTPClient.download(msgId, "/"+ServerDir+"/"+ftpFile, new File(localFilePath, ftpFile).toString(), true,
								"/"+ServerDir +"/"+ "MAIN");
						break;
					}
				}
			}
		}

		//將excel儲存至DB
		File esgFile = new File(localFilePath + esgFileName);
		List<Object[]> batchValues = new ArrayList<Object[]>();
		List<ELF606> elf606_list = new ArrayList<ELF606>();
		if (esgFile.exists()) {
			String mainId=IDGenerator.getUUID();
			int idx = 0;
			try {
				BufferedReader bufferedReader = new BufferedReader(new FileReader(esgFile));
				String input;
				int count = 0;

				//找出欄位名稱
				//找出證券公司塞進List
				//找到證券代碼1101開始的row就判斷為資料，把isTitle=false
				boolean isTitle = true;
				List<String[]> rows = new ArrayList<String[]>();
				String title = "";
				while((input = bufferedReader.readLine()) != null)
				{
					if (input.contains("1101")){
						isTitle = false;
					}

					if (isTitle)
					{
						if(input.startsWith("\uFEFF")) {
							input = input.replace("\uFEFF", "");
						}
						input = input.replace("\n", "");
						input = input.replace(", ", "_");
						input = input.trim().replace(" ","");
						title += input;
					}
					else{
						if(input.startsWith("\uFEFF")) {
							input = input.replace("\uFEFF", "");
						}

						int column = dataStartColumn;
						String item[] = input.split(",");
						rows.add(item);
						count++;
					}

				}
				//判斷欄位名稱不是空的，列出所有欄位
				String[] rowTitles = null;
				if (title.length()>0){
					title = title.trim().replace("\"","");
					rowTitles = title.split(",");
				}

				//檢查RPA撈回來的欄位是否一致
				//不一致發信通知ESG_mailSites
				List<CodeType> cs = codeTypeService.findByCodeTypeList("ESG_column");
				if (cs.size()>0) {
					CodeType c =cs.get(0);
					if (!c.getCodeDesc().equals(title)) {
						String diffColumn ="";
						for (int x=0;x<rowTitles.length;x++) {
							if (!c.getCodeDesc().contains(rowTitles[x])) {
								diffColumn += diffColumn.length()==0? rowTitles[x] : "," + rowTitles[x];
							}
						}
						if (diffColumn.length()>0) {
							Set<String> mail_cls_list = _mailList("ESG_mailSites");
							List<String> toAddr = new ArrayList<String>();
							toAddr.addAll(mail_cls_list);
							emailClient.send(false, null,toAddr.toArray(new String[toAddr.size()]),
									"ESG欄位發生異動請確認!!", "原:"+c.getCodeDesc() +"<br/><br/>新:"+title+"<br/><br/>差異欄位:"+diffColumn);
						}
					}
				}

				//判斷該月份是否已有資料，若有就刪除重新新增
				if ( count >0) {
					List<L290M01A> l290M01AS = l290m01aDao.findByReceiveDate(Util.parseDate(thisMonthFile));

					if (l290M01AS.size()>0) {
						eloandbBASEService
								.deleteByMainId("L290M01A", l290M01AS.get(0).getMainId());
					}
				}
				Map<String, String> stocklist = misdbBASEService.selStockCompanyId("");
				Map<String, Object> map = new HashMap<String, Object>();

				for (int i=0;i < rows.size();i++) {
					++idx;
					String stkName = "";
					String stkNo="";

					String esgScore  = "-";
					String msciLevel  = "-";
					String esgLevel  = "-";
					String issLevel1 = "-" ; //Util.trim(item[column++]);
					String issLevel2 = "-" ;//Util.trim(item[column++]);
					String issLevel = "-";
					String spScore = "-";
					String moody = "-";
					String companyGovernance = "-";
					String sinoPac = "-";
					String sustainability = "-";
					String[] row = rows.get(i);
					//根據Title順序塞入對應欄位
					for (int j=0;j<rowTitles.length;j++)
					{
						String rowTitle =rowTitles[j];
						if (rowTitle.toLowerCase().contains("證券代號")){
							String[] stkObject = Util.trim(row[j]).split(" ");
							if(stkObject.length==2){
								stkNo= Util.trim(Util.getLeftStr(stkObject[0],4));
								stkName = Util.trim(stkObject[1]);
							}
						}
						if (rowTitle.contains("SustainalyticsESG")){
							esgScore  = Util.trim(row[j]);
						}
						if (rowTitle.contains("MSCIESG")){
							msciLevel  = Util.trim(row[j]);
						}
						if (rowTitle.contains("FTSERussellESG")){
							esgLevel  = Util.trim(row[j]);
						}
						if (rowTitle.contains("ISSESG")){
							issLevel  = Util.trim(row[j]);
						}
						if (rowTitle.contains("S&PGlobalESG")){
							spScore  = Util.trim(row[j]);
						}
						if (rowTitle.contains("Moody’sESG")){
							moody  = Util.trim(row[j]);
						}
						if (rowTitle.contains("台灣公司治理評鑑")){
							companyGovernance  = Util.trim(row[j]);
						}
						if (rowTitle.contains("SinoPac+ESG")){
							sinoPac  = Util.trim(row[j]);
						}
						if (rowTitle.contains("台灣永續評鑑")){
							sustainability  = Util.trim(row[j]);
						}
					}

					String stkCmp="";
					if (stocklist.containsKey(stkNo)) {
						stkCmp = Util.trim(stocklist.get(stkNo));
					}
					//aloan不允許NULL需塞空白
					stkCmp = Util.isNotEmpty(stkCmp) ?stkCmp : "    ";
					String source="tdcc";

					//判斷是否要填null
					//BigDecimal的話，還需要在MISRows.java加欄位，不然null會被預設值0
					BigDecimal esgScore_BigDecimal = !Util.equals(esgScore, "-") ? Util.parseBigDecimal(esgScore) : null;
					BigDecimal esgLevel_BigDecimal = !Util.equals(esgLevel, "-") ? Util.parseBigDecimal(esgLevel) : null;
//					BigDecimal issLevel1_BigDecimal = !Util.equals(issLevel1, "-") ? Util.parseBigDecimal(issLevel1) : null;
//					BigDecimal issLevel2_BigDecimal = !Util.equals(issLevel2, "-") ? Util.parseBigDecimal(issLevel2) : null;
					String msciLevel_String = !Util.equals(msciLevel, "-") ? msciLevel : null;
					String issLevel_String = !Util.equals(issLevel, "-") ? issLevel : null;
					BigDecimal spScore_BigDecimal = !Util.equals(spScore, "-") ? Util.parseBigDecimal(spScore) : null;
					BigDecimal moody_BigDecimal = !Util.equals(moody, "-") ? Util.parseBigDecimal(moody) : null;
					String sinoPac_String = !Util.equals(sinoPac, "-") ? sinoPac : null;
					String sustainability_String = !Util.equals(sustainability, "-") ? sustainability : null;

					//如果又有新增ESG欄位，L290M01A.toObjectArray()、EloandbBASEServiceImpl、eloanSQL.xml也要新增欄位
					L290M01A l290m01a = new L290M01A();
					l290m01a.setMainId(mainId);
					l290m01a.setStkNo(stkNo);
					l290m01a.setReceiveDate(Util.parseDate(thisMonthFile));
					l290m01a.setSource(source);
					l290m01a.setStkCMP(stkCmp);
					l290m01a.setStkName(stkName);
					l290m01a.setEsgScore(esgScore_BigDecimal);
					l290m01a.setMsciLevel(msciLevel_String);
					l290m01a.setEsgLevel(esgLevel_BigDecimal);
					l290m01a.setIssLevel(issLevel_String);
					l290m01a.setCompanyGovernance(companyGovernance);
					l290m01a.setCreator(user.getUserId());
					l290m01a.setCreateTime(CapDate.getCurrentTimestamp());
					l290m01a.setSpScore(spScore_BigDecimal);
					l290m01a.setMoody(moody_BigDecimal);
					l290m01a.setSinoPac(sinoPac_String);
					l290m01a.setSustainability(sustainability_String);

					ELF606 elf606 = new ELF606();
					elf606.setElf606_StkNo(stkNo);
					elf606.setElf606_ReceiveDate(Util.parseDate(thisMonthFile));
					elf606.setElf606_Source(source);
					elf606.setElf606_StkCMP(stkCmp);
					elf606.setElf606_StkCMPNM(stkName);
					elf606.setElf606_EsgScore(esgScore_BigDecimal);
					elf606.setElf606_MsciLevel(msciLevel_String);
					elf606.setElf606_EsgLevel(esgLevel_BigDecimal);
					elf606.setElf606_IssLevel(issLevel_String);
					elf606.setElf606_Governance(companyGovernance);
					elf606.setElf606_DeletedTime(null);
					elf606.setElf606_Creator( user.getUserId());
					elf606.setElf606_CreateTime(CapDate.getCurrentTimestamp());
					elf606.setElf606_SpScore(spScore_BigDecimal);
					elf606.setElf606_Moody(moody_BigDecimal);
					elf606.setElf606_SinoPac(sinoPac_String);
					elf606.setElf606_Sustainability(sustainability_String);

					//檢查重複就不存
					if (!map.containsKey(stkNo)) {
						map.put(stkNo,l290m01a);
						elf606_list.add(elf606);
						batchValues.add(l290m01a.toObjectArray());
					}

					if(idx%500==0 || idx == count){

						eloandbBASEService.batchInsert_L290M01A(batchValues);
						batchValues = new ArrayList<Object[]>();

						//上傳mis
						MISRows<ELF606> mis_elf606_temp = new MISRows<ELF606>(ELF606.class);
						mis_elf606_temp.setValues(elf606_list);
						upMisToServer(mis_elf606_temp, "MIS");
						elf606_list = new ArrayList<ELF606>();
					}
				}

			} catch (Exception e) {
				logger.error("parseExcel EXCEPTION!!", e);
				throw new CapMessageException(e, getClass());
			}
		}
		else{
			logger.error("parseExcel EXCEPTION!!", "該月份檔案不存在");
			// Send E-Mail
			Set<String> mail_cls_list = _mailList("ESG_mailSites");
			List<String> toAddr = new ArrayList<String>();
			toAddr.addAll(mail_cls_list);
			emailClient.send(false, null,toAddr.toArray(new String[toAddr.size()]),
					"ESG該月份檔案不存在之通知!!", "");
			throw new CapMessageException("該月份檔案不存在", getClass());
		}
	}
	private <T> void upMisToServer(MISRows<T> misRows, String schemaName) {
		if (Util.isNotEmpty(misRows.getKeyValues())) {
			int DelCount = misdbBASEService.delete(
					misRows.getKeyMsgFmtParam(schemaName),
					misRows.getKeyValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Delete:"
					+ DelCount);
			misdbBASEService.insert(misRows.getMsgFmtParam(schemaName),
					misRows.getTypes(), misRows.getValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Insert");
		}
	}

	//J-110-0463_10702_B1001 Web e-Loan產出72-2放款餘額予消金處
//	private void procCLS722NonAppropriation(String begDate,String endDate) throws IOException, WriteException {
//		String mainId=IDGenerator.getUUID();
//		String filePath = LMSUtil.getUploadFilePath("943",
//				mainId, "cls943_722");
//
//		String fileName = begDate + "~" + endDate + "未撥款的72-2消金額度";
//
//		DocFile docFile = this.genCLS722XLS(mainId, filePath, begDate, endDate);
//		byte[] localFile = docFile.getData();
//		File file = new File(filePath + "/" + fileName + ".xls");
//
//		// Send E-Mail
//		Set<String> mail_cls_list = _mailList("CLS722NonAppropriation_mailSites");
//		List<String> toAddr = new ArrayList<String>();
//		toAddr.addAll(mail_cls_list);
//		String subject = fileName+ "之通知!!";
//		emailClient.send(false, null,toAddr.toArray(new String[toAddr.size()]),
//				subject, "",new File[]{file});
//
//	}
//	private DocFile genCLS722XLS(String mainId, String filePath, String begDate, String endDate)
//			throws WriteException, IOException {
//		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
//
//		File file = null;
//		Label label = null;
//
//		WritableWorkbook workbook = null;
//		WritableSheet sheet = null;
//		DocFile docFile = null;
//		String fileName = begDate + "~" + endDate + "未撥款的72-2消金額度";
//
//		try {
//			docFile = lms9511Service.saveDocFile("943",
//					mainId, "cls943_722", "xls");
//			// 設定下載的 xls 名稱
//			docFile.setSrcFileName("未撥款的72-2消金額度.xls");
//			docFileService.save(docFile, false);
//
//			file = new File(filePath);
//			file.mkdirs();
//			file = new File(filePath + "/" + fileName + ".xls");
//
//			WritableFont font_default = new WritableFont(
//					WritableFont.createFont("新細明體"), 9);
//			// 字型設定
//			WritableCellFormat cellFormatL_Border = null;
//			WritableCellFormat cellFormatR_Border = null;
//			WritableCellFormat cellFormatL = null;
//			WritableCellFormat cellFormat_Title = null;
//			cellFormatL_Border = LMSUtil.setCellFormat(cellFormatL_Border,
//					font_default, jxl.format.Alignment.LEFT, true);
//			cellFormatR_Border = LMSUtil.setCellFormat(cellFormatR_Border,
//					font_default, jxl.format.Alignment.RIGHT, true);
//			cellFormatL_Border = LMSUtil.setCellFormat(cellFormatL_Border,
//					font_default, jxl.format.Alignment.LEFT, true);
//			cellFormat_Title = LMSUtil.setCellFormat(cellFormatL_Border,
//					font_default, jxl.format.Alignment.LEFT, true);
//			// ~~~
//			WritableFont font_header = new WritableFont(
//					WritableFont.createFont("新細明體"), 12, WritableFont.BOLD);
//			WritableCellFormat font_headerCenter = null;
//			font_headerCenter = LMSUtil.setCellFormat(font_headerCenter,
//					font_header, jxl.format.Alignment.CENTRE, false);
//			// ~~~
//			WritableCellFormat amtTWDFormat = new WritableCellFormat(
//					font_default, NumberFormats.FORMAT1);
//			if (true) {
//				amtTWDFormat.setVerticalAlignment(jxl.format.VerticalAlignment.TOP);
//				amtTWDFormat.setBorder(jxl.format.Border.ALL, BorderLineStyle.THIN);
//			}
//
//			workbook = Workbook.createWorkbook(file);
//
//			if(true){
//				workbook.setColourRGB(Colour.GREEN,204, 255, 204);
//				cellFormat_Title.setBackground(jxl.format.Colour.GREEN);
//			}
//
//			if (true) {
//				sheet = workbook.createSheet("result", 0);
//				SheetSettings setting = sheet.getSettings();
//				setting.setPaperSize(PaperSize.A4);
//
//				setting.setFitWidth(1); // 設定預覽列印與列印成為一頁, 寬度
//				setting.setOrientation(PageOrientation.PORTRAIT);
//				Map<String, String> brNo_Name_Map = new HashMap<String, String>();
//
//				Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
//				//
//				headerMap.put("簽報書序號", 16);
//				headerMap.put("分行代碼", 10);
//				headerMap.put("簽報書狀態", 10);
//				headerMap.put("是否已核准", 12);
//				headerMap.put("是否已動審", 12);
//				headerMap.put("簽報書日期", 12);
//				headerMap.put("ENDDATE", 12);
//				headerMap.put("更新者員編", 10);
//				headerMap.put("userName", 12);
//				headerMap.put("額度明細表序號", 12);
//				headerMap.put("客戶統編", 10);
//				headerMap.put("重複碼", 8);
//				headerMap.put("客戶姓名", 12);
//				headerMap.put("額度序號", 14);
//				headerMap.put("幣別", 12);
//				headerMap.put("利率", 12);
//				headerMap.put("金額", 12);// idx==16(由0計算)
//				headerMap.put("REUSE", 10);
//				headerMap.put("房仲", 14);
//				headerMap.put("房仲敘述", 14);
//				headerMap.put("PARENTID", 14);
//				headerMap.put("PARENTDUPNO", 16);
//				headerMap.put("PROJECTNAME", 16);
//
//				int col_size = headerMap.size();
//
//				if (true) {
//					int rowIdx = 0;
//					int colIdx = 0;
//					for (String h : headerMap.keySet()) {
//						int colWidth = headerMap.get(h);
//
//						sheet.setColumnView(colIdx, colWidth);
//						sheet.addCell(new Label(colIdx, rowIdx, h,
//								cellFormat_Title));
//						// ---
//						colIdx++;
//					}
//				}
//
//				if (true) {
//					int rowIdx = 1;
//					List<Object[]> list = new ArrayList<Object[]>();
//					String brNo = user.getUnitNo();
//
//					List<Map<String, Object>> list_map = eloandbBASEService.findCLS722NonAppropriation(begDate, endDate);
//					StringBuffer cntrNos = new StringBuffer();
//					for (Map<String, Object> row_map :list_map) {
//						if (cntrNos.toString().indexOf(Util.trim(row_map.get("CNTRNO"))) == -1) {
//							if (cntrNos.length() > 0) {
//								cntrNos.append(",");
//							}
//							cntrNos.append("'");
//							cntrNos.append(Util.trim(row_map.get("CNTRNO")));
//							cntrNos.append("'");
//						}
//					}
//					List<Map<String, Object>> list_cntrNos = misdbBASEService.findCLS722NonAppropriationByCntrNo(cntrNos.toString());
//
//					for (Map<String, Object> row_map :list_map) {
//						Object[] arr = new Object[col_size];
//						for (int i = 0; i < col_size; i++) {
//							arr[i] = "";
//						}
//						String ownBrId = Util.trim(MapUtils.getString(row_map,
//								"OWNBRID"));
//						String brName = branchService.getBranchName(ownBrId);
//						String cntrNo = Util.trim(MapUtils.getString(row_map, "CNTRNO"));
//						String isUse = "";
//						for (Map<String, Object> map_cntrNos :list_cntrNos) {
//							if(Util.equals(cntrNo,Util.trim(MapUtils.getString(map_cntrNos, "ELF500_CNTRNO")))){
//								isUse = Util.trim(MapUtils.getString(map_cntrNos, "FLAG"));
//								break;
//							}
//						}
//
//						// ~~~
//						arr[0] = Util.trim(MapUtils.getString(row_map, "CASE_MAINID")); //CASE_MAINID
//						arr[1] = Util.trim(MapUtils.getString(row_map, "CASEBRID")); //CASEBRID
//						arr[2] = Util.trim(MapUtils.getString(row_map, "CASE_DOCSTATUS")); //CASE_DOCSTATUS
//						arr[3] = Util.trim(MapUtils.getString(row_map, "ISAPPROVE")); //是否已核准 ISAPPROVE
//						arr[4] = isUse; //是否已動審
//						arr[5] = Util.trim(MapUtils.getObject(row_map, "CASEDATE")); //CASEDATE
//						arr[6] = Util.trim(MapUtils.getObject(row_map, "ENDDATE")); //ENDDATE
//						arr[7] = Util.trim(MapUtils.getObject(row_map, "UPDATER")); //UPDATER
//						arr[8] = Util.trim(MapUtils.getString(row_map, "USERNAME")); //USERNAME
//						arr[9] = Util.trim(MapUtils.getString(row_map, "TABMAINID")); //TABMAINID
//						arr[10] = Util.trim(MapUtils.getString(row_map, "CUSTID")); //CUSTID
//						arr[11] = Util.trim(MapUtils.getString(row_map, "DUPNO")); //DUPNO
//						arr[12] = Util.trim(MapUtils.getString(row_map, "CUSTNAME")); //CUSTNAME
//						arr[13] = Util.trim(MapUtils.getString(row_map, "CNTRNO")); //CNTRNO
//						arr[14] = Util.trim(MapUtils.getString(row_map, "CURRENTAPPLYCURR")); //CURRENTAPPLYCURR
//						arr[15] = LMSUtil.pretty_numStr(CrsUtil.parseBigDecimal(Util.trim(MapUtils.getObject(row_map, "RATE")))); //RATE
//						arr[16] = LMSUtil.pretty_numStr(CrsUtil
//								.parseBigDecimal(MapUtils.getObject(row_map,
//										"CURRENTAPPLYAMT"))); //CURRENTAPPLYAMT
//						arr[17] = Util.trim(MapUtils.getString(row_map, "REUSE")); //REUSE
//						arr[18] = Util.trim(MapUtils.getString(row_map, "AGNTNO")); //AGNTNO
//						arr[19] = Util.trim(MapUtils.getString(row_map, "AGNT_DESC")); //AGNT_DESC
//						arr[20] = Util.trim(MapUtils.getString(row_map, "PARENTID")); //PARENTID
//						arr[21] = Util.trim(MapUtils.getString(row_map, "PARENTDUPNO")); //PARENTDUPNO
//						arr[22] = Util.trim(MapUtils.getString(row_map, "PROJECTNAME")); //PROJECTNAME
//
//						list.add(arr);
//					}
//
//					for (Object[] arr : list) {
//						int colLen = arr.length;
//						for (int i_col = 0; i_col < colLen; i_col++) {
//							if (i_col == 16 ) { // 數字欄位,自0起算
//								if (Util.isEmpty(Util.trim(arr[i_col]))) {
//									// 空白, 就不去轉數字
//								} else {
//									// 金額(至整數欄位)
//									sheet.addCell(new jxl.write.Number(i_col,
//											rowIdx, CrsUtil.parseBigDecimal(
//											arr[i_col]).doubleValue(),
//											amtTWDFormat));
//								}
//							} else {
//								sheet.addCell(new Label(i_col, rowIdx, Util
//										.trim(arr[i_col]), cellFormatL_Border));
//							}
//						}
//						// ---
//						rowIdx++;
//					}
//				}
//			}
//			workbook.write();
//			workbook.close();
//		} finally {
//
//		}
//		return docFile;
//	}
	private Set<String> _mailList(String codeType) {
		Set<String> r = new HashSet<String>();
		for (CodeType obj : codeTypeService.findByCodeTypeList(codeType)) {
			String target = Util.trim(obj.getCodeDesc());
			if (Util.isNotEmpty(target)) {
				r.add(target);
			}
		}
		return r;
	}

	//**上傳ACH手續費至mis.ELF460**///
	private void procC340M01A_to_ELF460()
			throws Exception{
		List<ELF460> elf460_list = new ArrayList<ELF460>();
		int idx = 0;

		try {
			List<C340M01A> c340M01As = c340m01aDao.findByCtrTypeA_isNeedACH_misFlag_orderBy_ploanCtrBegDateDesc();

			for (C340M01A c340m01a:c340M01As){
				++idx;
				Date rcADate = null;

				C340M01C c340m01c = c340m01cDao.findByMainIdItemType(c340m01a.getMainId() , "9");
				if(c340m01c!=null){
					JSONObject c340m01c_json =JSONObject.fromObject(c340m01c.getJsonData());
					rcADate =CapDate.getDate(c340m01c_json.getString("rcADate") , "yyyy-MM-dd") ;
				}
				if(rcADate!=null){
					ELF460 elf460 = new ELF460();
					elf460.setElf460_date(rcADate);
					elf460.setElf460_brn(c340m01a.getOwnBrId());
					elf460.setElf460_txId("824");
					elf460.setElf460_type("01");//01:EDDA申請授權、02:ACH扣帳
					elf460.setElf460_cntrNo_Seq(c340m01a.getPloanCtrNo());
					elf460.setElf460_rBank(c340m01a.getBankACHAcctCode());
					elf460.setElf460_rclNo(c340m01a.getBankACHAcctNo());
					elf460.setElf460_source("ELOAN");
					//elf460.setElf460_flag();
					elf460.setElf460_tmeStamp(new Timestamp(System.currentTimeMillis()));

					elf460_list.add(elf460);

					if(idx%500==0 || idx == c340M01As.size()){
						//上傳mis
						MISRows<ELF460> mis_elf460_temp = new MISRows<ELF460>(ELF460.class);
						mis_elf460_temp.setValues(elf460_list);
						upMisToServer(mis_elf460_temp, "MIS");
						elf460_list = new ArrayList<ELF460>();
					}
				}
			}

			//上傳至mis.ELF460後，壓回C340M01A.misFlag
			for (C340M01A c340m01a:c340M01As){
				c340m01a.setMisFlag(UtilConstants.DEFAULT.是);
				cls3401Service.saveTemporaryMeta(c340m01a);
			}
		} catch (Exception e) {
			logger.error("uploanELF460 EXCEPTION!!", e);
			throw new CapMessageException(e, getClass());
		}
	}
	//判斷已對保完成超過契約期限，但仍未撥款的對保契約書，自動作廢
	//增加判斷只作廢信貸逾期之契約書
	private void procC340M01A_to_inValidOnlineCtr() throws CapMessageException {
		if (clsService.is_function_on_codetype("J-111-0524_sendJCIC2ELF632")) {
			CodeType codeType = codeTypeService.findByCodeTypeAndCodeValue("LMS_FUNC_ON_FLAG", "J-111-0524_sendJCIC2ELF632", "zh_TW");
			if (Util.isNotEmpty(codeType)) {
				Date startDate = Util.parseDate(codeType.getCodeDesc());
				List<C340M01A> c340M01As = c340m01aDao.findC340M01ACompleteOverExprDate(startDate, new String[] {"A","B"});
				for (C340M01A c340m01a:c340M01As){
					//已有撥款紀錄
					if (misdbBASEService.chkFilterData1(c340m01a.getCustId(), c340m01a.getDupNo(), c340m01a.getContrNumber())) {
						c340m01a.setHaveLnf020(UtilConstants.DEFAULT.是);
					}
					else{
						//逾期還無撥款紀錄，發動契約書作廢、聯徵報送作廢
						c340m01a.setHaveLnf020(UtilConstants.DEFAULT.否);
						cls3401Service.inValidOnlineCtr(c340m01a);
					}
					cls3401Service.saveTemporaryMeta(c340m01a);
				}
			}
		}
	}
	//判斷已撥款通知pLoan發送對保契約書
	private void procC340M01A_to_notifypLoanSendMail() throws CapException {
		if (clsService.is_function_on_codetype("notifypLoanSendMail")) {
			HashSet promptMsg_list = new HashSet<String>();
			List<C340M01A> c340M01As = c340m01aDao.findNotifypLoanSendMail(new String[] {"A","B"});
			for (C340M01A c340m01a:c340M01As){
				//增加判斷有PLOAN回傳撥款通知就不再發送，避免多次發送造成客戶困擾
				try{
					boolean isSend = false;
					List<DocFile> listFile = docFileService.findByIDAndPid(
							c340m01a.getMainId(), null);
					for (DocFile file:listFile) {
						if (Util.equals(file.getSrcFileName(),"撥款合約.pdf")) {
							isSend = true;
							break;
						}
					}
					List<C160M01A> c160m01as = new ArrayList<C160M01A>();
					if (!isSend) {
						c160m01as = c160m01aDao.findLastBySrcMainId(c340m01a.getCaseMainId(), new String[] {CLSDocStatusEnum.已核准.getCode(),CLSDocStatusEnum.先行動用_已覆核.getCode()});
						//J-113-0216 新增檢核團貸案也要加入發送名單
						if (c160m01as.size() == 0) {
							c160m01as = new ArrayList<C160M01A>();
							List<C340M01B> c340m01bs = c340m01bDao.findByMainId(c340m01a.getMainId());
							C340M01B c340m01b = new C340M01B();
							if (c340m01bs != null && !c340m01bs.isEmpty()) {
								c340m01b = c340m01bs.get(0);
							}
							List<C160M01B> c160M01bs = c160m01bDao.findLastByRefMainId(c340m01b.getTabMainId());
							for(C160M01B c160M01b: c160M01bs) {
								C160M01A c160m01a = c160m01aDao.findByMainId(c160M01b.getMainId());
								if (Util.isEmpty(c160m01a.getDeletedTime())
										&& (c160m01a.getDocStatus().equals(CLSDocStatusEnum.已核准.getCode()) ||
										    c160m01a.getDocStatus().equals(CLSDocStatusEnum.先行動用_已覆核.getCode()))
										&& UtilConstants.Usedoc.caseType2.團貸.equals(c160m01a.getCaseType())
								   ) {
									c160m01as.add(c160m01a);
									break;
								}
							}
						}

						//已完成動審表
						if (c160m01as.size() > 0) {
							for (C160M01A c160m01a:c160m01as) {
								//新增判斷動審表額度序號與對保契約書一致
							    C160M01B c160M01B = c160m01bDao.findByMainidCntrno(c160m01a.getMainId(), c340m01a.getContrNumber());
								if (Util.isNotEmpty(c160M01B)) {
									List<Map<String, Object>> lnf030s = misdbBASEService.creditLoanCheckLoanDate(c340m01a.getCustId(), c340m01a.getDupNo(), c340m01a.getContrNumber());
									//已有撥款紀錄
									//J-113-0216 增加判斷房貸增貸契約書，入續約無lnf030改查lnf020
									if (lnf030s.size() >0
											|| ( Util.equals(c340m01a.getCtrType(),"B") && misdbBASEService.chkFilterData1(c340m01a.getCustId(), c340m01a.getDupNo(), c340m01a.getContrNumber()))) {
										PLOAN018 ploanObj = new PLOAN018();
										ploanObj.setContractNo(c340m01a.getPloanCtrNo());
										Map<String, Object> map = new HashMap<String, Object>();

										BigDecimal preliminaryFee = BigDecimal.ZERO;
										BigDecimal creditCheckFee = BigDecimal.ZERO;
										List<L140M01R> l140m01r_list = clsService.findL140M01R_exclude_feeSrc3(c160m01a.getMainId());
										if(l140m01r_list.size()>0){
											for(L140M01R l140m01r: l140m01r_list){
												String feeNo = l140m01r.getFeeNo();
												BigDecimal amt = l140m01r.getFeeAmt();

												if(Util.equals("01", feeNo)){
													preliminaryFee = preliminaryFee.add(amt);
												}else if(Util.equals("02", feeNo)){
													creditCheckFee = creditCheckFee.add(amt);
												}
												if (c340m01a.getCtrType().equals(ContractDocConstants.C340M01A_CtrType.Type_B)) {
													if ("03".equals(feeNo)) {
														// 續約作業費
														if (l140m01r.getFeeAmt() != null) {
															preliminaryFee = preliminaryFee.add(l140m01r.getFeeAmt());
														}
													}
													if ("04".equals(feeNo)) {
														// 變更條件手續費
														if (l140m01r.getFeeAmt() != null) {
															preliminaryFee = preliminaryFee.add(l140m01r.getFeeAmt());
														}
													}
												}
											}
										}

										ploanObj.setPreliminaryFee(preliminaryFee);
										ploanObj.setCreditCheckFee(creditCheckFee);

										List<PLOAN018_paymentInfo> paymentInfoList = ploanObj.getPaymentInfoList();

										List<C160M01B> c160s01bs = clsService.findC160M01B_mainId(c160m01a.getMainId());
										//代償欄位看是抓c340m01c還是c160s01f
										if (c160s01bs.size() > 0) {
											int seq = 1;
											for (C160M01B c160s01b : c160s01bs) {
												List<C160S01F> c160s01fs = c160s01fDao.findByMainIdSeqRefMainid(
														c160s01b.getMainId(), seq,
														c160s01b.getRefmainId());
												for (C160S01F c160s01f:c160s01fs) {
													String repaymentProduct= codeTypeService
															.findByCodeTypeAndCodeValue("repaymentProduct",c160s01f.getRepaymentProduct()).getCodeDesc();
													PLOAN018_paymentInfo paymentInfo = new PLOAN018_paymentInfo();
													paymentInfo.setAccountName(c160s01f.getCustName());//戶名
													paymentInfo.setBankAcctNo(c160s01f.getAccNo());//帳號
													paymentInfo.setBankCode(c160s01f.getBankNo());//銀行代碼
													paymentInfo.setBankName(c160s01f.getBranchName());//分行名稱
													paymentInfo.setRepaymentAmt(c160s01f.getSubAmt());//代償金額
													paymentInfo.setRepaymentProduct(repaymentProduct);//產品
													paymentInfo.setRepaymentProductType(repaymentProduct);//產品別
													ploanObj.getPaymentInfoList().add(paymentInfo);
												}
												seq++;
											}
										}

										JSONObject jsonObj = pLoanGwClient.send_paymentInfo(ploanObj);

										//pLoan會傳OK，才壓已通知pLoan
										if (Util.equals(jsonObj.optString("stat"), "ok")) {
											c340m01a.setNotifypLoanSendMail("Y");
											cls3401Service.saveTemporaryMeta(c340m01a);
										} else {
											promptMsg_list.add(jsonObj.optString("errorCode") + jsonObj.optString("errorMsg") + ":" + c340m01a.getPloanCtrNo());
										}
									}
								}
							}
						}
					}
					else{
						c340m01a.setNotifypLoanSendMail("Y");
						cls3401Service.saveTemporaryMeta(c340m01a);
					}
				} catch (Exception ex) {
					promptMsg_list.add(ex.toString() + ":" + c340m01a.getPloanCtrNo());
				}
			}
			if (promptMsg_list.size() > 0) {
				logger.error("通知pLoan發送對保契約信件排程失敗!!"+ StringUtils.join(promptMsg_list, ","));
				throw new CapMessageException("通知pLoan發送對保契約信件排程失敗，" + StringUtils.join(promptMsg_list, ","), getClass());
			}
		}
	}
}

