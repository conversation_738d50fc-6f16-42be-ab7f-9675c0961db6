var initDfd = initDfd || $.Deferred();

initDfd.done(function(json){
	var initControl_lockDoc = json['initControl_lockDoc'];
	
	initAtPage456(json);
	//ilog.debug("@S06: initDfd.done(...)");
	$('input[type=radio][name=noAdj]').change(function(){
		var val = this.value;
		/*
		 不要寫 
		if($("#sprtRating").val()=="DF" && val=="2"){
		
		若有 N 個人
		第 1 個人是 DF
		第 2 個人非 DF 且 有升降等
		
		由【第 1 個人 → 第 2 個人】 
		$("#sprtRating").val()=="DF" && val=="2" 前面可能抓到第1個人；後面可能抓到第2個人		 
		 */
		var sprtRating = $("#sprtRating").val();
		if(sprtRating=="DF" && val=="2"){
			$("#tabForm").injectData({'noAdj':'1'});
			API.showErrorMessage(i18n.lms1015m01['msg.005']);
			return;
		}		
		
		var debugMsg = "event change";
		if(val=="1"){						
			isS06_show(false, debugMsg);
		}else if(val=="2"){
			isS06_show(true, debugMsg);
		}else{
			isS06_show(true, debugMsg);
		}
		
		var elmArr = ["adjustStatus","adjRating"];
		$.each(elmArr, function(idx, elmId) {
			if( $("#"+elmId).is(":visible") ){
				$("#"+elmId).addClass('required');
			}else{
				$("#"+elmId).removeClass('required');
			}
		});
	});	
	
	
	$('input[type=radio][name=adjustStatus]').change(function(){
		var val = this.value;
		
		if(val=="1"){						
			//調昇
			$("#div_adjustStatus_1").show();
		}else if(val=="2"){
			//調降
			$("#div_adjustStatus_1").hide();
		}		
		
	});	
	
	$('input[type=radio][name=adjustFlag]').change(function(){
		var val = this.value;
		
		$("tr.tr_adjustFlag").hide();
		if(val=="1"){						
			//淨資產
			$("tr.tr_adjustFlag_1").show();
		}else if(val=="2"){
			//職業
			$("tr.tr_adjustFlag_2").show();
		}else if(val=="3"){
			//其它
			$("tr.tr_adjustFlag_3").show();
		}		
		
	});	
	
	if(initControl_lockDoc){
		
	}else{
		$('#adjustReason').click(function(){
			//初始
			$("#tmp_adjustReason").val( $('#adjustReason').val() );
			
			$("#divEnterAdjustReason").thickbox({
		        title: '', width: 720, height: 280, align: "center", valign: "bottom",
	            modal: true, i18n: i18n.def,
	            buttons: {
	                "cancel": function(){
	                    $.thickbox.close();
	                },
		            "sure": function(){
		            	if( $("#tmp_adjustReasonForm").valid()){
			            	var newval = $("#tmp_adjustReason").val();
			            	
			            	$.ajax({handler : "lms1015m01formhandler",action : 'validateAdjustReason',				
			    				data : {'keyStr':'', 'mowType': 'J', 'adjustReason': newval },
			    				}).done(function(json) {
			    					procCfmMsg(json.adjRsnFmt_cfmObj).done(function(){
			    		        		alwaysConfirmAdjReason(json.adjRsnFmt_cnt
			    		        				, json.adjRsnFmt_alwaysCfmObj).done(function(){
			    		        			
			    		        			$.thickbox.close();
			    		    	            $('#adjustReason').val( newval );					
			    		        		});
			    		        	});
			    			});	  
		            	}        	
		            }
	            }
		    });
		});	
	}
		
});
function _downloadFileByDocFileOid(docOid){
	$.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:docOid
        }
    });
}
function _logicalDeleteBDocFile(docOid){
	return $.ajax({type : "POST", handler : 'lms1015m01formhandler',
		action: "overSeaCLS_logicalDeleteBDocFile",
		data : {'docOid': docOid }
	});
}

function _set_c120m01a_attchFileOid2(){
	var $frm = $("#tabForm")
	
	var docOid = $frm.find("#attchFileOid2").val();
	var c120m01a_oid = $frm.find("#c120_id_list").val();
	
	return $.ajax({type : "POST", handler : 'lms1015m01formhandler',
		action: "set_c120m01a_attchFileOid2",
		data : {'c120m01a_oid': c120m01a_oid, 'docOid': docOid }
	});
}
function _JP_uploadOverrideReportDoc(){
	var $frm = $("#tabForm");
	
	var my_fieldId = ($frm.find("#c120_id_list option:selected").attr("data-idDup") + "_jpov");
	/*
	deleteDup會讓同一[mainId, fieldId]下只有1個檔案
	但之後在簽報書中 mainId 都是L120M01A.mainId
	所以把 fieldId 都加上 (custId+dupNo)
	以免甲的附加檔案，被乙的附加檔案 蓋掉
	*/			
	var limitFileSize=3145728;
	MegaApi.uploadDialog({
		fieldId:my_fieldId,
		fieldIdHtml:"size='30'",
		fileDescId:"fileDesc",
		fileDescHtml:"size='30' maxlength='30'",
		subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
		limitSize:limitFileSize,
		width:320,
		height:190,
		data:{
		mainId: responseJSON.mainId,
		preDelAll: true,
		deleteDup: true
	},
	}).done(function(obj) {
		$frm.find("#getOverrideFile").show();
		$frm.find("#attchFileOid2").val(obj.fileKey);
	
		_set_c120m01a_attchFileOid2();
	});
}
function _JP_deleteOverrideReportDoc(){
	var $frm = $("#tabForm");
	
	var docOid = $frm.find("#attchFileOid2").val();
	if(docOid){
		_logicalDeleteBDocFile(docOid).done(function(){
			$frm.find("#getOverrideFile").hide();
			$frm.find("#attchFileOid2").val("");

			_set_c120m01a_attchFileOid2();
		});
	}
}
function _JP_downloadOverrideReportDoc(){
	var $frm = $("#tabForm");
	
	var docOid = $frm.find("#attchFileOid2").val();
	if(docOid){
		_downloadFileByDocFileOid(docOid);
	}
}