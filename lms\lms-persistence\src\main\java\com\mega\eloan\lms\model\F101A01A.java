package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;

import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/**
 * <pre>
 * The persistent class for the F101A01A database table.
 * </pre>
 * 
 * @since 2011/7/26
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/7/26,<PERSON><PERSON><PERSON>,new</li>
 *          <li>2011/8/03,<PERSON><PERSON><PERSON>,add
 *          {@link F101A01A#setAuthType(DocAuthTypeEnum)}</li>
 *          <li>2011/8/11,Sunkis<PERSON> Wang,add column.</li>
 *          <li>2011/9/01,Sunkist Wang,delete column.
 *          </ul>
 */
@NamedEntityGraph(name = "F101A01A-entity-graph", attributeNodes = { @NamedAttributeNode("f101m01a") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "F101A01A")
public class F101A01A extends com.mega.eloan.common.model.RelativeMeta
		implements Serializable, tw.com.iisi.cap.model.IDataObject, IDocObject {
	private static final long serialVersionUID = 1L;

	private Timestamp authTime;

	@Column(length = 1)
	private String authType;

	@Column(length = 3)
	private String authUnit;

	@Column(length = 6)
	private String owner;

	@Column(length = 3)
	private String ownUnit;

	// bi-directional many-to-one association to F101M01A
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false)
			})
	private F101M01A f101m01a;

	public F101A01A() {
	}

	public Timestamp getAuthTime() {
		return this.authTime;
	}

	public void setAuthTime(Timestamp authTime) {
		this.authTime = authTime;
	}

	public String getAuthType() {
		return this.authType;
	}

	public void setAuthType(String authType) {
		this.authType = authType;
	}

	public void setAuthType(DocAuthTypeEnum docAuthTypeEnum) {
		this.authType = docAuthTypeEnum.getCode();
	}

	public String getAuthUnit() {
		return this.authUnit;
	}

	public void setAuthUnit(String authUnit) {
		this.authUnit = authUnit;
	}

	public String getOwner() {
		return this.owner;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}

	public String getOwnUnit() {
		return this.ownUnit;
	}

	public void setOwnUnit(String ownUnit) {
		this.ownUnit = ownUnit;
	}

	public F101M01A getF101m01a() {
		return this.f101m01a;
	}

	public void setF101m01a(F101M01A f101m01a) {
		this.f101m01a = f101m01a;
	}

}