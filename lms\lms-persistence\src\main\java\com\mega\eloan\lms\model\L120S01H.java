/* 
 * L120S01H.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotNull;

import org.apache.bval.constraints.NotEmpty;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.lms.validation.group.Check2;

/** 個金基本資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S01H", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class L120S01H extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 出生日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "BIRTHDAY", columnDefinition = "DATE")
	@NotNull(message = "{required.message}", groups = Check.class)
	@NotEmpty(message = "{required.message}", groups = Check.class)
	private Date birthday;

	/**
	 * 學歷
	 * <p/>
	 * 國中以下 | 1<br/>
	 * 國中 | 2<br/>
	 * 高中職 | 3<br/>
	 * 專科 | 4<br/>
	 * 大學 | 5<br/>
	 * 碩士 | 6<br/>
	 * 博士 | 7
	 */
	@Column(name = "EDU", length = 1, columnDefinition = "CHAR(1)")
	@NotNull(message = "{required.message}", groups = Check2.class)
	@NotEmpty(message = "{required.message}", groups = Check2.class)
	private String edu;

	/**
	 * 性別
	 * <p/>
	 * 100/12/06調整<br/>
	 * M男、F女
	 */
	@Column(name = "SEX", length = 1, columnDefinition = "CHAR(1)")
	@NotNull(message = "{required.message}", groups = Check2.class)
	@NotEmpty(message = "{required.message}", groups = Check2.class)
	private String sex;

	/**
	 * 婚姻狀況
	 * <p/>
	 * 1未婚、2已婚、4離婚、5歿
	 */
	@Column(name = "MARRY", length = 1, columnDefinition = "CHAR(1)")
	@NotNull(message = "{required.message}", groups = Check2.class)
	@NotEmpty(message = "{required.message}", groups = Check2.class)
	private String marry;

	/** 子女數 **/
	@Column(name = "CHILD", columnDefinition = "DECIMAL(2,0)")
	private Integer child;

	/** 國別 **/
	@Column(name = "NTCODE", length = 2, columnDefinition = "CHAR(2)")
	@NotNull(message = "{required.message}", groups = Check.class)
	@NotEmpty(message = "{required.message}", groups = Check.class)
	private String ntCode;

	/**
	 * 外國人無特定住所
	 * <p/>
	 * 100/12/08新增<br/>
	 * Y/N<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	@Column(name = "FOREGINAL", length = 1, columnDefinition = "CHAR(1)")
	private String foreginal;

	/** 通訊電話 **/
	@Column(name = "COTEL", length = 20, columnDefinition = "VARCHAR(20)")
	private String coTel;

	/** 戶籍電話 **/
	@Column(name = "FTEL", length = 20, columnDefinition = "VARCHAR(20)")
	private String fTel;

	/** 行動電話 **/
	@Column(name = "MTEL", length = 20, columnDefinition = "VARCHAR(20)")
	private String mTel;

	/** email **/
	@Column(name = "EMAIL", length = 48, columnDefinition = "VARCHAR(48)")
	private String email;

	/**
	 * 戶籍地址
	 * <p/>
	 * 64個全型字
	 */
	@Column(name = "FADDR", length = 192, columnDefinition = "VARCHAR(192)")
	@NotNull(message = "{required.message}", groups = Check2.class)
	@NotEmpty(message = "{required.message}", groups = Check2.class)
	private String fAddr;

	/**
	 * 通訊地址
	 * <p/>
	 * 64個全型字
	 */
	@Column(name = "COADDR", length = 192, columnDefinition = "VARCHAR(192)")
	@NotNull(message = "{required.message}", groups = Check2.class)
	@NotEmpty(message = "{required.message}", groups = Check2.class)
	private String coAddr;

	/**
	 * 現住房屋
	 * <p/>
	 * 1自有，無貸款<br/>
	 * 2自有，有貸款<br/>
	 * 3配偶所有<br/>
	 * 4父母親所有<br/>
	 * 5宿舍<br/>
	 * 6租賃
	 */
	@Column(name = "HOUSESTATUS", length = 1, columnDefinition = "CHAR(1)")
	private String houseStatus;

	/**
	 * 每月貸款
	 * <p/>
	 * houseStatus=2
	 */
	@Column(name = "HOUSELEND", columnDefinition = "DECIMAL(13,0)")
	private Long houseLend;

	/**
	 * 每月租金
	 * <p/>
	 * houseStatus=6
	 */
	@Column(name = "HOUSERENT", columnDefinition = "DECIMAL(13,0)")
	private Long houseRent;

	/**
	 * 不動產狀況
	 * <p/>
	 * 1本人或配偶不動產，未設定抵押權<br/>
	 * 2本人或配偶不動產，已設定抵押權<br/>
	 * 3本人或配偶無不動產
	 */
	@Column(name = "CMSSTATUS", length = 1, columnDefinition = "CHAR(1)")
	@NotNull(message = "{required.message}", groups = Check2.class)
	@NotEmpty(message = "{required.message}", groups = Check2.class)
	private String cmsStatus;

	/** 存款往來銀行 **/
	@Column(name = "DPBANK", length = 3, columnDefinition = "VARCHAR(3)")
	private String dpBank;

	/** 存款往來分行代碼 **/
	@Column(name = "DPBRNO", length = 3, columnDefinition = "VARCHAR(3)")
	private String dpBrno;

	/**
	 * 存款往來銀行(海外用)
	 * <p/>
	 * 101/01/18新增
	 */
	@Column(name = "DPBANKNAME", length = 30, columnDefinition = "VARCHAR(30)")
	private String dpBankName;

	/** 存款帳戶 **/
	@Column(name = "DPACCT", length = 16, columnDefinition = "VARCHAR(16)")
	private String dpAcct;

	/**
	 * 配偶欄
	 * <p/>
	 * 無配偶 |0<br/>
	 * 列於本欄|1<br/>
	 * 同本案借保人|2
	 */
	@Column(name = "DATANAME", length = 1, columnDefinition = "VARCHAR(1)")
	private String dataName;

	/**
	 * 配偶身分證統編
	 * <p/>
	 * 100/12/08新增<br/>
	 * (保留給dataName=2用)
	 */
	@Column(name = "MCUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String mCustId;

	/**
	 * 配偶身分證統編重複碼
	 * <p/>
	 * 100/12/08新增<br/>
	 * (保留給dataName=2用)
	 */
	@Column(name = "MDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String mDupNo;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 引入行業對象別
	 */
	@Column(name = "BUSCODE", columnDefinition = "CHAR(6)")
	private String busCode;

	/**
	 * 引入行業對象別名稱
	 */
	@Column(name = "ECONM", columnDefinition = "CHAR(78)")
	private String ecoNm;

	/**
	 * 引入次產業別
	 */
	@Column(name = "BUSSKIND", columnDefinition = "CHAR(6)")
	private String bussKind;

	/**
	 * 引入次產業別名稱
	 */
	@Column(name = "ECONM07A", columnDefinition = "CHAR(78)")
	private String ecoNm07A;

	/**
	 * 引入客戶類別
	 */
	@Column(name = "CUSTCLASS", columnDefinition = "CHAR(1)")
	private String custClass;

	@Column(name = "CRDTYPE", columnDefinition = "CHAR(2)")
	private String crdType;

	@Column(name = "GRADE", columnDefinition = "CHAR(120)")
	private String grade;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得出生日期 **/
	public Date getBirthday() {
		return this.birthday;
	}

	/** 設定出生日期 **/
	public void setBirthday(Date value) {
		this.birthday = value;
	}

	/**
	 * 取得學歷
	 * <p/>
	 * 國中以下 | 1<br/>
	 * 國中 | 2<br/>
	 * 高中職 | 3<br/>
	 * 專科 | 4<br/>
	 * 大學 | 5<br/>
	 * 碩士 | 6<br/>
	 * 博士 | 7
	 */
	public String getEdu() {
		return this.edu;
	}

	/**
	 * 設定學歷
	 * <p/>
	 * 國中以下 | 1<br/>
	 * 國中 | 2<br/>
	 * 高中職 | 3<br/>
	 * 專科 | 4<br/>
	 * 大學 | 5<br/>
	 * 碩士 | 6<br/>
	 * 博士 | 7
	 **/
	public void setEdu(String value) {
		this.edu = value;
	}

	/**
	 * 取得性別
	 * <p/>
	 * 100/12/06調整<br/>
	 * M男、F女
	 */
	public String getSex() {
		return this.sex;
	}

	/**
	 * 設定性別
	 * <p/>
	 * 100/12/06調整<br/>
	 * M男、F女
	 **/
	public void setSex(String value) {
		this.sex = value;
	}

	/**
	 * 取得婚姻狀況
	 * <p/>
	 * 1未婚、2已婚、4離婚、5歿
	 */
	public String getMarry() {
		return this.marry;
	}

	/**
	 * 設定婚姻狀況
	 * <p/>
	 * 1未婚、2已婚、4離婚、5歿
	 **/
	public void setMarry(String value) {
		this.marry = value;
	}

	/** 取得子女數 **/
	public Integer getChild() {
		return this.child;
	}

	/** 設定子女數 **/
	public void setChild(Integer value) {
		this.child = value;
	}

	/** 取得國別 **/
	public String getNtCode() {
		return this.ntCode;
	}

	/** 設定國別 **/
	public void setNtCode(String value) {
		this.ntCode = value;
	}

	/**
	 * 取得外國人無特定住所
	 * <p/>
	 * 100/12/08新增<br/>
	 * Y/N<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	public String getForeginal() {
		return this.foreginal;
	}

	/**
	 * 設定外國人無特定住所
	 * <p/>
	 * 100/12/08新增<br/>
	 * Y/N<br/>
	 * ※國內DBU/OBU才需填寫
	 **/
	public void setForeginal(String value) {
		this.foreginal = value;
	}

	/** 取得通訊電話 **/
	public String getCoTel() {
		return this.coTel;
	}

	/** 設定通訊電話 **/
	public void setCoTel(String value) {
		this.coTel = value;
	}

	/** 取得戶籍電話 **/
	public String getFTel() {
		return this.fTel;
	}

	/** 設定戶籍電話 **/
	public void setFTel(String value) {
		this.fTel = value;
	}

	/** 取得行動電話 **/
	public String getMTel() {
		return this.mTel;
	}

	/** 設定行動電話 **/
	public void setMTel(String value) {
		this.mTel = value;
	}

	/** 取得email **/
	public String getEmail() {
		return this.email;
	}

	/** 設定email **/
	public void setEmail(String value) {
		this.email = value;
	}

	/**
	 * 取得戶籍地址
	 * <p/>
	 * 64個全型字
	 */
	public String getFAddr() {
		return this.fAddr;
	}

	/**
	 * 設定戶籍地址
	 * <p/>
	 * 64個全型字
	 **/
	public void setFAddr(String value) {
		this.fAddr = value;
	}

	/**
	 * 取得通訊地址
	 * <p/>
	 * 64個全型字
	 */
	public String getCoAddr() {
		return this.coAddr;
	}

	/**
	 * 設定通訊地址
	 * <p/>
	 * 64個全型字
	 **/
	public void setCoAddr(String value) {
		this.coAddr = value;
	}

	/**
	 * 取得現住房屋
	 * <p/>
	 * 1自有，無貸款<br/>
	 * 2自有，有貸款<br/>
	 * 3配偶所有<br/>
	 * 4父母親所有<br/>
	 * 5宿舍<br/>
	 * 6租賃
	 */
	public String getHouseStatus() {
		return this.houseStatus;
	}

	/**
	 * 設定現住房屋
	 * <p/>
	 * 1自有，無貸款<br/>
	 * 2自有，有貸款<br/>
	 * 3配偶所有<br/>
	 * 4父母親所有<br/>
	 * 5宿舍<br/>
	 * 6租賃
	 **/
	public void setHouseStatus(String value) {
		this.houseStatus = value;
	}

	/**
	 * 取得每月貸款
	 * <p/>
	 * houseStatus=2
	 */
	public Long getHouseLend() {
		return this.houseLend;
	}

	/**
	 * 設定每月貸款
	 * <p/>
	 * houseStatus=2
	 **/
	public void setHouseLend(Long value) {
		this.houseLend = value;
	}

	/**
	 * 取得每月租金
	 * <p/>
	 * houseStatus=6
	 */
	public Long getHouseRent() {
		return this.houseRent;
	}

	/**
	 * 設定每月租金
	 * <p/>
	 * houseStatus=6
	 **/
	public void setHouseRent(Long value) {
		this.houseRent = value;
	}

	/**
	 * 取得不動產狀況
	 * <p/>
	 * 1本人或配偶不動產，未設定抵押權<br/>
	 * 2本人或配偶不動產，已設定抵押權<br/>
	 * 3本人或配偶無不動產
	 */
	public String getCmsStatus() {
		return this.cmsStatus;
	}

	/**
	 * 設定不動產狀況
	 * <p/>
	 * 1本人或配偶不動產，未設定抵押權<br/>
	 * 2本人或配偶不動產，已設定抵押權<br/>
	 * 3本人或配偶無不動產
	 **/
	public void setCmsStatus(String value) {
		this.cmsStatus = value;
	}

	/** 取得存款往來銀行 **/
	public String getDpBank() {
		return this.dpBank;
	}

	/** 設定存款往來銀行 **/
	public void setDpBank(String value) {
		this.dpBank = value;
	}

	/** 取得存款往來分行代碼 **/
	public String getDpBrno() {
		return this.dpBrno;
	}

	/** 設定存款往來分行代碼 **/
	public void setDpBrno(String value) {
		this.dpBrno = value;
	}

	/**
	 * 取得存款往來銀行(海外用)
	 * <p/>
	 * 101/01/18新增
	 */
	public String getDpBankName() {
		return this.dpBankName;
	}

	/**
	 * 設定存款往來銀行(海外用)
	 * <p/>
	 * 101/01/18新增
	 **/
	public void setDpBankName(String value) {
		this.dpBankName = value;
	}

	/** 取得存款帳戶 **/
	public String getDpAcct() {
		return this.dpAcct;
	}

	/** 設定存款帳戶 **/
	public void setDpAcct(String value) {
		this.dpAcct = value;
	}

	/**
	 * 取得配偶欄
	 * <p/>
	 * 無配偶 |0<br/>
	 * 列於本欄|1<br/>
	 * 同本案借保人|2
	 */
	public String getDataName() {
		return this.dataName;
	}

	/**
	 * 設定配偶欄
	 * <p/>
	 * 無配偶 |0<br/>
	 * 列於本欄|1<br/>
	 * 同本案借保人|2
	 **/
	public void setDataName(String value) {
		this.dataName = value;
	}

	/**
	 * 取得配偶身分證統編
	 * <p/>
	 * 100/12/08新增<br/>
	 * (保留給dataName=2用)
	 */
	public String getMCustId() {
		return this.mCustId;
	}

	/**
	 * 設定配偶身分證統編
	 * <p/>
	 * 100/12/08新增<br/>
	 * (保留給dataName=2用)
	 **/
	public void setMCustId(String value) {
		this.mCustId = value;
	}

	/**
	 * 取得配偶身分證統編重複碼
	 * <p/>
	 * 100/12/08新增<br/>
	 * (保留給dataName=2用)
	 */
	public String getMDupNo() {
		return this.mDupNo;
	}

	/**
	 * 設定配偶身分證統編重複碼
	 * <p/>
	 * 100/12/08新增<br/>
	 * (保留給dataName=2用)
	 **/
	public void setMDupNo(String value) {
		this.mDupNo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/**
	 * 設定行業對象別
	 * 
	 * @param busCode
	 */
	public void setBusCode(String busCode) {
		this.busCode = busCode;
	}

	/**
	 * 取得行業對象別
	 * 
	 * @return
	 */
	public String getBusCode() {
		return busCode;
	}

	/**
	 * 設定行業對象別名稱
	 * 
	 * @param ecoNm
	 */
	public void setEcoNm(String ecoNm) {
		this.ecoNm = ecoNm;
	}

	/**
	 * 取得行業對象別名稱
	 * 
	 * @return
	 */
	public String getEcoNm() {
		return ecoNm;
	}

	/**
	 * 設定次產業別
	 * 
	 * @param bussKind
	 */
	public void setBussKind(String bussKind) {
		this.bussKind = bussKind;
	}

	/**
	 * 取得次產業別
	 * 
	 * @return
	 */
	public String getBussKind() {
		return bussKind;
	}

	/**
	 * 設定次產業別名稱
	 * 
	 * @param ecoNm07A
	 */
	public void setEcoNm07A(String ecoNm07A) {
		this.ecoNm07A = ecoNm07A;
	}

	/**
	 * 取得次產業別名稱
	 * 
	 * @return
	 */
	public String getEcoNm07A() {
		return ecoNm07A;
	}

	/**
	 * 設定客戶類別
	 * 
	 * @param custClass
	 */
	public void setCustClass(String custClass) {
		this.custClass = custClass;
	}

	/**
	 * 取得客戶類別
	 * 
	 * @return
	 */
	public String getCustClass() {
		return custClass;
	}

	/**
	 * 設定個金海外評等表類型
	 * 
	 * @param crdType
	 */
	public void setCrdType(String crdType) {
		this.crdType = crdType;
	}

	/**
	 * 取得個金海外評等表類型
	 * 
	 * @return
	 */
	public String getCrdType() {
		return crdType;
	}

	/**
	 * 設定個金海外評等等級
	 * 
	 * @param grade
	 */
	public void setGrade(String grade) {
		this.grade = grade;
	}

	/**
	 * 取得個金海外評等等級
	 * 
	 * @return
	 */
	public String getGrade() {
		return grade;
	}
}
