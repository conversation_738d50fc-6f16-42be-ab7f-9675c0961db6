package com.mega.eloan.lms.obsdb.service.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.common.jdbc.EloanColumnMapRowMapper;
import com.mega.eloan.lms.mfaloan.bean.ELF601;
import com.mega.eloan.lms.mfaloan.bean.ELF602;
import com.mega.eloan.lms.obsdb.service.ObsdbELF601Service;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 海外貸後管理	ELF601-控制檔 ELF602-紀錄檔
 * </pre>
 *
 * @since 2021/8/
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/8/,009301,new
 *          </ul>
 */
@Service
public class ObsdbELF601ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF601Service {
	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Override
	public Map<String, Object> findByIdDupNo(String BRNID, String custId, String dupNo) {
		return this.getJdbc(BRNID).queryForMap("ELFCUST.queryByIdDupNo",
				new String[] { custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> queryElf602List(ISearch pageSetting,
			String BRNID) {

		// 主機欄位如果首字可能有英文、數字，請先使用ascii(欄位名稱)排序、再以該欄位排序，避免排序亂序問題
		Map<String, Boolean> oriOrderMap = pageSetting.getOrderBy();
		Map<String, Boolean> newOrderMap = new LinkedHashMap<String, Boolean>();
		Map<String, String> replaceKeyMap = new LinkedHashMap<String, String>();
		replaceKeyMap.put("custId", "ascii(custId)");
		replaceKeyMap.put("cntrNo", "ascii(cntrNo)");
		replaceKeyMap.put("loanNo", "ascii(loanNo)");
		for (String key : oriOrderMap.keySet()) {
			if (replaceKeyMap.containsKey(key)) {
				newOrderMap.put(replaceKeyMap.get(key), oriOrderMap.get(key));
			}
			newOrderMap.put(key, oriOrderMap.get(key));
		}
		pageSetting.setOrderBy(newOrderMap);

		String orderByStr = Util.getOrderData(pageSetting.getOrderBy());
		List<Object> params = new ArrayList<Object>();
		params.add(BRNID);

		return this.getJdbc(BRNID).queryForListByCustParam(
				"ELF602.queryElf602List", new Object[] { orderByStr },
				params.toArray(), 0, Integer.MAX_VALUE,
				new EloanColumnMapRowMapper());
	}

	@Override
	public List<ELF601> getElf601ByCntrNoLoanNo(String BRNID, String cntrNo, String loanNo) {
		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();
		sb.append(" ELF601_CNTRNO=? ");
		w1List.add(cntrNo);

		// 20200619 金襄理說 只選到額度層 => 只能撈 有額度沒帳號的
		// if(Util.isNotEmpty(loanNo)){
		sb.append(" AND ELF601_LOAN_NO=? ");
		w1List.add(loanNo);
		// }

		if (Util.isNotEmpty(BRNID)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF601_BR_NO=? ");
			w1List.add(BRNID);
		}

		if(sb.length() > 0) {
			sb.insert(0, " WHERE ");
		}

		List<Map<String, Object>> rowData = this.getJdbc(BRNID).queryForList("ELF601.getElf601",
				sb.toString(), w1List.toArray() , 0, Integer.MAX_VALUE);

		List<ELF601> list = new ArrayList<ELF601>();
		for (Map<String, Object> row : rowData) {
			ELF601 model = new ELF601();
//			DataParse.map2Bean(row, model);
			// 因為海外DATE欄位為數字 會失敗
			convertMapToELF601(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF602> getElf602ByCntrNoLoanNo(String BRNID,
				String cntrNo, String loanNo, boolean undone) {
		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();
		sb.append(" ELF602_CNTRNO=? ");
		w1List.add(cntrNo);

		// 20200619 金襄理說 只選到額度層 => 只能撈 有額度沒帳號的
		// if(Util.isNotEmpty(loanNo)){
		sb.append(" AND ELF602_LOAN_NO=? ");
		w1List.add(loanNo);
		// }

		if (Util.isNotEmpty(BRNID)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF602_BR_NO=? ");
			w1List.add(BRNID);
		}

		if (undone) {
			sb.append(" AND ELF602_STATUS IN ('1','2') ");
		}

		if(sb.length() > 0) {
			sb.insert(0, " WHERE ");
		}

		List<Map<String, Object>> rowData = this.getJdbc(BRNID).queryForList("ELF602.getElf602",
				sb.toString(), w1List.toArray() , 0, Integer.MAX_VALUE);
		
		List<ELF602> list = new ArrayList<ELF602>();
		for (Map<String, Object> row : rowData) {
			ELF602 model = new ELF602();
//			DataParse.map2Bean(row, model);
			convertMapToELF602(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF601> getElf601ByFilter(String custId, String dupNo,
	  		String BRNID, String cntrNo, String loanNo, String status) {
		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();

		if (Util.isNotEmpty(custId)) {
			sb.append(" ELF601_CUSTID=? ");
			w1List.add(custId);
		}
		if (Util.isNotEmpty(dupNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF601_DUPNO=? ");
			w1List.add(dupNo);
		}
		if (Util.isNotEmpty(BRNID)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF601_BR_NO=? ");
			w1List.add(BRNID);
		}
		if (Util.isNotEmpty(cntrNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF601_CNTRNO=? ");
			w1List.add(cntrNo);
		}
		if (Util.isNotEmpty(loanNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF601_LOAN_NO=? ");
			w1List.add(loanNo);
		}
		if (Util.isNotEmpty(status)) {
			if (Util.equals(status, "A")) {
				// 全部
			} else if (Util.equals(status, "N")) {
				// 未解除
				sb.append(sb.length() > 0 ? " AND " : "");
				sb.append(" ELF601_STATUS <> 'C' ");
			} else {
				// C-已解除
				sb.append(sb.length() > 0 ? " AND " : "");
				sb.append(" ELF601_STATUS=? ");
				w1List.add(status);
			}
		}

		if(sb.length() > 0) {
			sb.insert(0, " WHERE ");
		}

		List<Map<String, Object>> rowData = this.getJdbc(BRNID).queryForList("ELF601.getElf601",
				sb.toString(), w1List.toArray() , 0, Integer.MAX_VALUE);

		List<ELF601> list = new ArrayList<ELF601>();
		for (Map<String, Object> row : rowData) {
			ELF601 model = new ELF601();
//			DataParse.map2Bean(row, model);
			convertMapToELF601(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF602> getElf602ByFilter(String custId, String dupNo,
		  	String BRNID, String cntrNo, String loanNo, String startDate,
		  	String endDate, String[] handlingStatus) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();

		if (Util.isNotEmpty(custId)) {
			sb.append(" ELF602_CUSTID=? ");
			w1List.add(custId);
		}
		if (Util.isNotEmpty(dupNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF602_DUPNO=? ");
			w1List.add(dupNo);
		}
		if (Util.isNotEmpty(BRNID)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF602_BR_NO=? ");
			w1List.add(BRNID);
		}
		if (Util.isNotEmpty(cntrNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF602_CNTRNO=? ");
			w1List.add(cntrNo);
		}
		if (Util.isNotEmpty(loanNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF602_LOAN_NO=? ");
			w1List.add(loanNo);
		}
		if (Util.isNotEmpty(startDate) && Util.isNotEmpty(endDate)) {
			sb.append(" AND ELF602_FO_DATE BETWEEN ? AND ? ");
			w1List.add(Util.parseBigDecimal(sdf.format(CapDate.getDate(startDate, "yyyy-MM-dd")).replaceAll("\\D", "")).toString());
			w1List.add(Util.parseBigDecimal(sdf.format(CapDate.getDate(endDate, "yyyy-MM-dd")).replaceAll("\\D", "")).toString());
		}

		StringBuffer sb2 = new StringBuffer();
		for (String status : handlingStatus) {
			if (Util.isEmpty(status)) {
				continue;
			}
			sb2.append(sb2.length() > 0 ? ", " : "");
			sb2.append("?");
			w1List.add(status);
		}
		if (sb2.length() > 0) {
			sb.append(" AND ELF602_STATUS IN ( ").append(sb2).append(" )");
		}

		if(sb.length() > 0) {
			sb.insert(0, " WHERE ");
		}

		List<Map<String, Object>> rowData = this.getJdbc(BRNID).queryForList("ELF602.getElf602",
				sb.toString(), w1List.toArray() , 0, Integer.MAX_VALUE);

		List<ELF602> list = new ArrayList<ELF602>();
		for (Map<String, Object> row : rowData) {
			ELF602 model = new ELF602();
//			DataParse.map2Bean(row, model);
			convertMapToELF602(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF601> getElf601OnlyById(String custId, String dupNo,
										  String brNo) {
		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();
		sb.append(" ELF601_CUSTID=? ");
		w1List.add(custId);

		sb.append(" AND ELF601_DUPNO=? ");
		w1List.add(dupNo);

		sb.append(" AND ELF601_BR_NO=? ");
		w1List.add(brNo);

		sb.append(" AND ELF601_CNTRNO='' AND ELF601_LOAN_NO='' ");

		if(sb.length() > 0) {
			sb.insert(0, " WHERE ");
		}

		List<Map<String, Object>> rowData = this.getJdbc(brNo).queryForList("ELF601.getElf601",
				sb.toString(), w1List.toArray() , 0, Integer.MAX_VALUE);

		List<ELF601> list = new ArrayList<ELF601>();
		for (Map<String, Object> row : rowData) {
			ELF601 model = new ELF601();
//			DataParse.map2Bean(row, model);
			convertMapToELF601(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF601> getElf601OnlyIdByFilter(String custId, String dupNo,
												String status, String brNo) {
		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();

		if (Util.isNotEmpty(custId)) {
			sb.append(" ELF601_CUSTID=? ");
			w1List.add(custId);
		}
		if (Util.isNotEmpty(dupNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF601_DUPNO=? ");
			w1List.add(dupNo);
		}
		if (Util.isNotEmpty(brNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF601_BR_NO=? ");
			w1List.add(brNo);
		}
		sb.append(sb.length() > 0 ? " AND " : "");
		sb.append(" ELF601_CNTRNO='' AND ELF601_LOAN_NO='' ");

		if (Util.isNotEmpty(status)) {
			if (Util.equals(status, "A")) {
				// 全部
			} else if (Util.equals(status, "N")) {
				// 未解除
				sb.append(sb.length() > 0 ? " AND " : "");
				sb.append(" ELF601_STATUS <> 'C' ");
			} else {
				// C-已解除
				sb.append(sb.length() > 0 ? " AND " : "");
				sb.append(" ELF601_STATUS=? ");
				w1List.add(status);
			}
		}

		if(sb.length() > 0) {
			sb.insert(0, " WHERE ");
		}

		List<Map<String, Object>> rowData = this.getJdbc(brNo).queryForList("ELF601.getElf601",
				sb.toString(), w1List.toArray() , 0, Integer.MAX_VALUE);

		List<ELF601> list = new ArrayList<ELF601>();
		for (Map<String, Object> row : rowData) {
			ELF601 model = new ELF601();
//			DataParse.map2Bean(row, model);
			convertMapToELF601(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF602> getElf602OnlyById(String custId, String dupNo,
										  boolean undone, String brNo) {
		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();
		sb.append(" ELf602_CUSTID=? ");
		w1List.add(custId);

		sb.append(" AND ELf602_DUPNO=? ");
		w1List.add(dupNo);

		sb.append(" AND ELF602_BR_NO=? ");
		w1List.add(brNo);

		sb.append(" AND ELF602_CNTRNO='' AND ELF602_LOAN_NO='' ");

		if (undone) {
			sb.append(" AND ELF602_STATUS IN ('1','2') ");
		}

		if(sb.length() > 0) {
			sb.insert(0, " WHERE ");
		}

		List<Map<String, Object>> rowData = this.getJdbc(brNo).queryForList("ELF602.getElf602",
				sb.toString(), w1List.toArray() , 0, Integer.MAX_VALUE);

		List<ELF602> list = new ArrayList<ELF602>();
		for (Map<String, Object> row : rowData) {
			ELF602 model = new ELF602();
//			DataParse.map2Bean(row, model);
			convertMapToELF602(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF602> getElf602OnlyIdByFilter(String custId, String dupNo,
												String startDate, String endDate, String[] handlingStatus,
												String brNo) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

		StringBuffer sb = new StringBuffer();
		List<String> w1List = new ArrayList<String>();

		if (Util.isNotEmpty(custId)) {
			sb.append(" ELF602_CUSTID=? ");
			w1List.add(custId);
		}
		if (Util.isNotEmpty(dupNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF602_DUPNO=? ");
			w1List.add(dupNo);
		}
		if (Util.isNotEmpty(brNo)) {
			sb.append(sb.length() > 0 ? " AND " : "");
			sb.append(" ELF602_BR_NO=? ");
			w1List.add(brNo);
		}
		sb.append(sb.length() > 0 ? " AND " : "");
		sb.append(" ELF602_CNTRNO='' AND ELF602_LOAN_NO='' ");

		if (Util.isNotEmpty(startDate) && Util.isNotEmpty(endDate)) {
			sb.append(" AND ELF602_FO_DATE BETWEEN ? AND ? ");
			w1List.add(Util.parseBigDecimal(TWNDate.toAD(CapDate.getDate(
					startDate, "yyyy-MM-dd")).replaceAll("\\D", "")).toString());
			w1List.add(Util.parseBigDecimal(TWNDate.toAD(CapDate.getDate(
					endDate, "yyyy-MM-dd")).replaceAll("\\D", "")).toString());
//			w1List.add(sdf.format(CapDate.getDate(startDate, "yyyy-MM-dd")));
//			w1List.add(sdf.format(CapDate.getDate(endDate, "yyyy-MM-dd")));
		}

		StringBuffer sb2 = new StringBuffer();
		for (String status : handlingStatus) {
			if (Util.isEmpty(status)) {
				continue;
			}
			sb2.append(sb2.length() > 0 ? ", " : "");
			sb2.append("?");
			w1List.add(status);
		}
		if (sb2.length() > 0) {
			sb.append(" AND ELF602_STATUS IN ( ").append(sb2).append(" )");
		}

		if(sb.length() > 0) {
			sb.insert(0, " WHERE ");
		}

		List<Map<String, Object>> rowData = this.getJdbc(brNo).queryForList("ELF602.getElf602",
				sb.toString(), w1List.toArray() , 0, Integer.MAX_VALUE);

		List<ELF602> list = new ArrayList<ELF602>();
		for (Map<String, Object> row : rowData) {
			ELF602 model = new ELF602();
//			DataParse.map2Bean(row, model);
			convertMapToELF602(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public ELF601 getElf601ByUnid(String BRNID, String unid) {
		Map<String, Object> rowData = this.getJdbc(BRNID).queryForMap(
				"ELF601.getElf601ByUnid", new Object[] { unid });
		if (rowData == null) {
			return null;
		} else {
			ELF601 model = new ELF601();
//			DataParse.map2Bean(rowData, model);
			convertMapToELF601(rowData, model);
			return model;
		}
	}

	@Override
	public ELF602 getElf602ByUnid(String BRNID, String unid) {
		Map<String, Object> rowData = this.getJdbc(BRNID).queryForMap(
				"ELF602.getElf602ByUnid", new Object[] { unid });
		if (rowData == null) {
			return null;
		} else {
			ELF602 model = new ELF602();
//			DataParse.map2Bean(rowData, model);
			convertMapToELF602(rowData, model);
			return model;
		}
	}

	@Override
	public List<ELF601> getElf601ByUnidLike(String BRNID, String unid) {
		List<Map<String, Object>> rowData = this.getJdbc(BRNID).queryForList("ELF601.getElf601ByUnidLike",
				new String[] { unid });

		List<ELF601> list = new ArrayList<ELF601>();
		for (Map<String, Object> row : rowData) {
			ELF601 model = new ELF601();
			//DataParse.map2Bean(row, model);
			convertMapToELF601(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF602> getElf602ByDatasrcLike(String BRNID, String datasrc) {
		List<Map<String, Object>> rowData = this.getJdbc(BRNID).queryForList("ELF602.getElf602ByDatasrcLike",
				new String[] { datasrc });
		List<ELF602> list = new ArrayList<ELF602>();
		for (Map<String, Object> row : rowData) {
			ELF602 model = new ELF602();
			//DataParse.map2Bean(row, model);
			convertMapToELF602(row, model);
			list.add(model);
		}
		return list;
	}
	
	@Override
	public List<ELF602> getElf602ByDatasrc(String BRNID, String datasrc) {
		List<Map<String, Object>> rowData = this.getJdbc(BRNID).queryForList("ELF602.getElf602ByDatasrc",
				new String[] { datasrc });
		List<ELF602> list = new ArrayList<ELF602>();
		for (Map<String, Object> row : rowData) {
			ELF602 model = new ELF602();
			//DataParse.map2Bean(row, model);
			convertMapToELF602(row, model);
			list.add(model);
		}
		return list;
	}
	
	@Override
	public void deleteELF601(String BRNID, String unid) {
		// DELETE FROM MIS.ELF601 WHERE ELF601_UNID = ?
		this.getJdbc(BRNID).update("ELF601.delete", new String[] { unid });
	}

	@Override
	public void deleteELF602(String BRNID, String unid) {
		// DELETE FROM MIS.ELF602 WHERE ELF602_UNID = ?
		this.getJdbc(BRNID).update("ELF602.delete", new String[] { unid });
	}

    @Override
    public void deleteELF602ByFoDateLikeUnid(String BRNID, Date foDate,String likeUnid) {
        // DELETE FROM #SCHEMA#.ELF602 WHERE ELF602_FO_DATE = ? AND ELF602_UNID Like ?
        this.getJdbc(BRNID).update("ELF602.deleteByFoDateLikeUnid", new Object[] { (Util.isNotEmpty(foDate) ?
				Util.parseBigDecimal(TWNDate.toAD(foDate).replaceAll("\\D", "")) : BigDecimal.ZERO) , likeUnid + "%" });
    }

	@Override
	public void insertELF601(String BRNID, ELF601 elf601) {
		this.getJdbc(BRNID).update(
				"ELF601.insert",
				new Object[] { elf601.getElf601_unid(),
						elf601.getElf601_cntrno(), elf601.getElf601_loan_no(),
						elf601.getElf601_loan_kind(),
						elf601.getElf601_fo_kind(),
						elf601.getElf601_fo_content(),
						elf601.getElf601_fo_way(), elf601.getElf601_fo_cycle(),
						(Util.isNotEmpty(elf601.getElf601_fo_beg_date()) ?
								Util.parseBigDecimal(TWNDate.toAD(elf601.getElf601_fo_beg_date()).replaceAll("\\D", "")) : BigDecimal.ZERO),
						(Util.isNotEmpty(elf601.getElf601_fo_end_date()) ?
								Util.parseBigDecimal(TWNDate.toAD(elf601.getElf601_fo_end_date()).replaceAll("\\D", "")) : BigDecimal.ZERO),
						(Util.isNotEmpty(elf601.getElf601_fo_next_date()) ?
								Util.parseBigDecimal(TWNDate.toAD(elf601.getElf601_fo_next_date()).replaceAll("\\D", "")) : BigDecimal.ZERO),
						elf601.getElf601_staff(),
						elf601.getElf601_fo_staffNo(),
						elf601.getElf601_ao_staffNo(),
						elf601.getElf601_status(),
						(Util.isNotEmpty(elf601.getElf601_cre_date()) ?
								Util.parseBigDecimal(TWNDate.toAD(elf601.getElf601_cre_date()).replaceAll("\\D", "")) : BigDecimal.ZERO),
						elf601.getElf601_cre_teller(),
						elf601.getElf601_cre_supvno(),
						(Util.isNotEmpty(elf601.getElf601_upd_date()) ?
								Util.parseBigDecimal(TWNDate.toAD(elf601.getElf601_upd_date()).replaceAll("\\D", "")) : BigDecimal.ZERO),
						elf601.getElf601_upd_teller(),
						elf601.getElf601_upd_supvno(),
						elf601.getElf601_full_content(), elf601.getElf601_custid(), elf601.getElf601_dupno(), elf601.getElf601_br_no(),
                        elf601.getElf601_case_mark(), Util.trim(elf601.getElf601_suid()), 
                        (Util.isNotEmpty(elf601.getElf601_sapptime()) ?
								Util.parseBigDecimal(elf601.getElf601_sapptime().toString().replaceAll("\\D", "")) : BigDecimal.ZERO),
						(Util.isNotEmpty(elf601.getElf601_sseqno()) ?
								Util.parseBigDecimal(elf601.getElf601_sseqno().toString().replaceAll("\\D", "")) : BigDecimal.ZERO)});
	}

	@Override
	public void insertELF602(String BRNID, ELF602 elf602) {
		this.getJdbc(BRNID).update(
				"ELF602.insert",
				new Object[] { elf602.getElf602_unid(),
						elf602.getElf602_cntrno(), elf602.getElf602_loan_no(),
						elf602.getElf602_loan_kind(),
						elf602.getElf602_fo_kind(),
						elf602.getElf602_fo_content(),
						elf602.getElf602_staff(),
						(Util.isNotEmpty(elf602.getElf602_fo_date()) ?
								Util.parseBigDecimal(TWNDate.toAD(elf602.getElf602_fo_date()).replaceAll("\\D", "")) : BigDecimal.ZERO),
						(Util.isNotEmpty(elf602.getElf602_chkdate()) ?
								Util.parseBigDecimal(TWNDate.toAD(elf602.getElf602_chkdate()).replaceAll("\\D", "")) : BigDecimal.ZERO),
						elf602.getElf602_conform_fg(),
						elf602.getElf602_fo_memo(), elf602.getElf602_status(),
						elf602.getElf602_datasrc(),
						elf602.getElf602_unusual_fg(),
						elf602.getElf602_unusualdesc(),
						elf602.getElf602_isnotional(),
						elf602.getElf602_isaml(),
						(Util.isNotEmpty(elf602.getElf602_upd_date()) ?
								Util.parseBigDecimal(TWNDate.toAD(elf602.getElf602_upd_date()).replaceAll("\\D", "")) : BigDecimal.ZERO),
						elf602.getElf602_upd_teller(),
						elf602.getElf602_upd_supvno(),
						elf602.getElf602_full_content(),
						elf602.getElf602_fieldMainId(),
						// Util.trimSizeInOS390(elf602.getElf602_fileDesc(), 399)
						elf602.getElf602_fileDesc(), elf602.getElf602_custid(), elf602.getElf602_dupno(), elf602.getElf602_br_no(),
                        elf602.getElf602_case_mark(), elf602.getElf602_suid(), 
                        (Util.isNotEmpty(elf602.getElf602_sapptime()) ?
								Util.parseBigDecimal(elf602.getElf602_sapptime().toString().replaceAll("\\D", "")) : BigDecimal.ZERO),
                        elf602.getElf602_sseqno() });
	}
	
	@Override
	public void updateELF601Status(String brNo, String unid, String status){
		this.getJdbc(brNo).update(
				"ELF601.updateELF601Status",
				new Object[] { status, unid });
	}
	
	@Override
	public void updateELF602StatusAndMemo(String brNo, String unid, String status, String memo){
		this.getJdbc(brNo).update(
				"ELF602.updateELF602StatusAndMemo",
				new Object[] { status, memo, unid });
	}

	@Override
	public void insertElf602(List<ELF602> overseaElf602List) {
		for(ELF602 newelf602 : overseaElf602List) {
			String unid = newelf602.getElf602_unid();
			deleteELF602ByFoDateLikeUnid(newelf602.getElf602_br_no(),newelf602.getElf602_fo_date(), unid.substring(0, unid.indexOf("_0055BATCH")+ 18));
			logger.info("doLmsBatch0055 insertELF602，brNo: " + newelf602.getElf602_br_no() + ", unid: " + newelf602.getElf602_unid() + ", foDate:" + newelf602.getElf602_fo_date());
			insertELF602(newelf602.getElf602_br_no(), newelf602);
		}
	}

	public ELF601 convertMapToELF601(Map<String, Object> map, ELF601 newElf601) {

		newElf601.setElf601_unid((String) Util.trim(MapUtils.getObject(map,"ELF601_UNID")));
		newElf601.setElf601_cntrno((String) Util.trim(MapUtils.getObject(map,"ELF601_CNTRNO")));
		newElf601.setElf601_loan_no((String) Util.trim(MapUtils.getObject(map,"ELF601_LOAN_NO")));
		newElf601.setElf601_loan_kind((String) Util.trim(MapUtils.getObject(map,"ELF601_LOAN_KIND")));
		newElf601.setElf601_fo_kind((String) Util.trim(MapUtils.getObject(map,"ELF601_FO_KIND")));
		newElf601.setElf601_fo_content((String) Util.trim(MapUtils.getObject(map,"ELF601_FO_CONTENT")));
		newElf601.setElf601_fo_way((String) Util.trim(MapUtils.getObject(map,"ELF601_FO_WAY")));
		newElf601.setElf601_fo_cycle((BigDecimal) MapUtils.getObject(map,"ELF601_FO_CYCLE"));
		BigDecimal ELF601_FO_BEG_DATE = (BigDecimal) MapUtils.getObject(map,"ELF601_FO_BEG_DATE");
		Date FO_BEG_DATE = (ELF601_FO_BEG_DATE.compareTo(BigDecimal.ZERO) == 0 ? null : CapDate.getDate(CapDate.formatyyyyMMddToDateFormat(
				ELF601_FO_BEG_DATE.toString(), "yyyy-MM-dd"), "yyyy-MM-dd"));
		newElf601.setElf601_fo_beg_date(FO_BEG_DATE);
		BigDecimal ELF601_FO_END_DATE = (BigDecimal) MapUtils.getObject(map,"ELF601_FO_END_DATE");
		Date FO_END_DATE = (ELF601_FO_END_DATE.compareTo(BigDecimal.ZERO) == 0 ? null : CapDate.getDate(CapDate.formatyyyyMMddToDateFormat(
				ELF601_FO_END_DATE.toString(), "yyyy-MM-dd"), "yyyy-MM-dd"));
		newElf601.setElf601_fo_end_date(FO_END_DATE);
		BigDecimal ELF601_FO_NEXT_DATE = (BigDecimal) MapUtils.getObject(map,"ELF601_FO_NEXT_DATE");
		Date FO_NEXT_DATE = (ELF601_FO_NEXT_DATE.compareTo(BigDecimal.ZERO) == 0 ? null : CapDate.getDate(CapDate.formatyyyyMMddToDateFormat(
				ELF601_FO_NEXT_DATE.toString(), "yyyy-MM-dd"), "yyyy-MM-dd"));
		newElf601.setElf601_fo_next_date(FO_NEXT_DATE);
		newElf601.setElf601_staff((String) Util.trim(MapUtils.getObject(map,"ELF601_STAFF")));
		newElf601.setElf601_fo_staffNo((String) Util.trim(MapUtils.getObject(map,"ELF601_FO_STAFFNO")));
		newElf601.setElf601_ao_staffNo((String) Util.trim(MapUtils.getObject(map,"ELF601_AO_STAFFNO")));
		newElf601.setElf601_status((String) Util.trim(MapUtils.getObject(map,"ELF601_STATUS")));
		BigDecimal ELF601_CRE_DATE = (BigDecimal) MapUtils.getObject(map,"ELF601_CRE_DATE");
		Date CRE_DATE = (ELF601_CRE_DATE.compareTo(BigDecimal.ZERO) == 0 ? null : CapDate.getDate(CapDate.formatyyyyMMddToDateFormat(
				ELF601_CRE_DATE.toString(), "yyyy-MM-dd"), "yyyy-MM-dd"));
		newElf601.setElf601_cre_date(CRE_DATE);
		newElf601.setElf601_cre_teller((String) Util.trim(MapUtils.getObject(map,"ELF601_CRE_TELLER")));
		newElf601.setElf601_cre_supvno((String) Util.trim(MapUtils.getObject(map,"ELF601_CRE_SUPVNO")));
		BigDecimal ELF601_UPD_DATE = (BigDecimal) MapUtils.getObject(map,"ELF601_UPD_DATE");
		Date UPD_DATE = (ELF601_UPD_DATE.compareTo(BigDecimal.ZERO) == 0 ? null : CapDate.getDate(CapDate.formatyyyyMMddToDateFormat(
				ELF601_UPD_DATE.toString(), "yyyy-MM-dd"), "yyyy-MM-dd"));
		newElf601.setElf601_upd_date(UPD_DATE);
		newElf601.setElf601_upd_teller((String) Util.trim(MapUtils.getObject(map,"ELF601_UPD_TELLER")));
		newElf601.setElf601_upd_supvno((String) Util.trim(MapUtils.getObject(map,"ELF601_UPD_SUPVNO")));
		newElf601.setElf601_full_content((String) Util.trim(MapUtils.getObject(map,"ELF601_FULL_CONTENT")));
        newElf601.setElf601_case_mark((String) Util.trim(MapUtils.getObject(map,"ELF601_CASE_MARK")));

        newElf601.setElf601_suid((String) Util.trim(MapUtils.getObject(map,"ELF601_SUID")));        
        String ELF601_SAPPTIME =  Util.trim(MapUtils.getObject(map,"ELF601_SAPPTIME"));
		newElf601.setElf601_sapptime(this.convertDateTime(ELF601_SAPPTIME, "yyyyMMddHHmmssSSS"));
		newElf601.setElf601_sseqno(new BigDecimal(Util.trim(MapUtils.getObject(map,"ELF601_SSEQNO"))));
        
		return newElf601;
	}

	public ELF602 convertMapToELF602(Map<String, Object> map, ELF602 newElf602) {

		newElf602.setElf602_unid((String) Util.trim(MapUtils.getObject(map,"ELF602_UNID")));
		BigDecimal ELF602_FO_DATE = (BigDecimal) MapUtils.getObject(map,"ELF602_FO_DATE");
		Date FO_DATE = (ELF602_FO_DATE.compareTo(BigDecimal.ZERO) == 0 ? null : CapDate.getDate(CapDate.formatyyyyMMddToDateFormat(
				ELF602_FO_DATE.toString(), "yyyy-MM-dd"), "yyyy-MM-dd"));
		newElf602.setElf602_fo_date(FO_DATE);
		newElf602.setElf602_cntrno((String) Util.trim(MapUtils.getObject(map,"ELF602_CNTRNO")));
		newElf602.setElf602_loan_no((String) Util.trim(MapUtils.getObject(map,"ELF602_LOAN_NO")));
		newElf602.setElf602_loan_kind((String) Util.trim(MapUtils.getObject(map,"ELF602_LOAN_KIND")));
		newElf602.setElf602_fo_kind((String) Util.trim(MapUtils.getObject(map,"ELF602_FO_KIND")));
		newElf602.setElf602_fo_content((String) Util.trim(MapUtils.getObject(map,"ELF602_FO_CONTENT")));
		newElf602.setElf602_staff((String) Util.trim(MapUtils.getObject(map,"ELF602_STAFF")));
		BigDecimal ELF602_CHKDATE = (BigDecimal) MapUtils.getObject(map,"ELF602_CHKDATE");
		Date CHKDATE = (ELF602_CHKDATE.compareTo(BigDecimal.ZERO) == 0 ? null : CapDate.getDate(CapDate.formatyyyyMMddToDateFormat(
				ELF602_CHKDATE.toString(), "yyyy-MM-dd"), "yyyy-MM-dd"));
		newElf602.setElf602_chkdate(CHKDATE);
		newElf602.setElf602_conform_fg((String) Util.trim(MapUtils.getObject(map,"ELF602_CONFORM_FG")));
		newElf602.setElf602_fo_memo((String) Util.trim(MapUtils.getObject(map,"ELF602_FO_MEMO")));
		newElf602.setElf602_status((String) Util.trim(MapUtils.getObject(map,"ELF602_STATUS")));
		newElf602.setElf602_datasrc((String) Util.trim(MapUtils.getObject(map,"ELF602_DATASRC")));
		newElf602.setElf602_unusual_fg((String) Util.trim(MapUtils.getObject(map,"ELF602_UNUSUAL_FG")));
		newElf602.setElf602_unusualdesc((String) Util.trim(MapUtils.getObject(map,"ELF602_UNUSUALDESC")));
		newElf602.setElf602_isnotional((String) Util.trim(MapUtils.getObject(map,"ELF602_ISNOTIONAL")));
		newElf602.setElf602_isaml((String) Util.trim(MapUtils.getObject(map,"ELF602_ISAML")));
		BigDecimal ELF602_UPD_DATE = (BigDecimal) MapUtils.getObject(map,"ELF602_UPD_DATE");
		Date UPD_DATE = (ELF602_UPD_DATE.compareTo(BigDecimal.ZERO) == 0 ? null : CapDate.getDate(CapDate.formatyyyyMMddToDateFormat(
				ELF602_UPD_DATE.toString(), "yyyy-MM-dd"), "yyyy-MM-dd"));
		newElf602.setElf602_upd_date(UPD_DATE);
		newElf602.setElf602_upd_teller((String) Util.trim(MapUtils.getObject(map,"ELF602_UPD_TELLER")));
		newElf602.setElf602_upd_supvno((String) Util.trim(MapUtils.getObject(map,"ELF602_UPD_SUPVNO")));
		newElf602.setElf602_full_content((String) Util.trim(MapUtils.getObject(map,"ELF602_FULL_CONTENT")));
		newElf602.setElf602_fieldMainId((String) Util.trim(MapUtils.getObject(map,"ELF602_FIELDMAINID")));
		newElf602.setElf602_fileDesc((String) Util.trim(MapUtils.getObject(map,"ELF602_FILEDESC")));
        newElf602.setElf602_case_mark((String) Util.trim(MapUtils.getObject(map,"ELF602_CASE_MARK")));
           
        newElf602.setElf602_suid(Util.trim(MapUtils.getObject(map,"ELF602_SUID")));        
        String ELF602_SAPPTIME =  Util.trim(MapUtils.getObject(map,"ELF602_SAPPTIME"));
    	newElf602.setElf602_sapptime(this.convertDateTime(ELF602_SAPPTIME, "yyyyMMddHHmmssSSS"));
        newElf602.setElf602_sseqno(new BigDecimal(Util.trim(MapUtils.getObject(map,"ELF602_SSEQNO"))));
        newElf602.setElf602_sesgsunre((String) Util.trim(MapUtils.getObject(map,"ELF602_SESGSUNRE")));
        
        

		return newElf602;
	}
	
	public Date convertDateTime(String timestamp, String dateformat){
		SimpleDateFormat formatter = new SimpleDateFormat(dateformat);
		Date finaldate = null;
        try {
        	finaldate = formatter.parse(timestamp);
		} catch (ParseException e) {
			finaldate = null;
		}
		return finaldate;
	}
}
