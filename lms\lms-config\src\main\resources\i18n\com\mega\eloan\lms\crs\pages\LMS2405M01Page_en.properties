page.title=LMS2405M01 Personal Banking Credit Review
title=Personal Banking Credit Review Details Sheet:
#tit02=\u6848\u4ef6\u8cc7\u8a0a
tit02=Case Information
tit03=File Attachment
C241M01A.projectNo=Serial Number
C241M01A.retrialYN=Credit Review Required
C241M01A.custId=Customer's unified business number
C241M01A.custName=Customer Name
C241M01A.lastRetrialDate=Last Review Date
C241M01A.shouldReviewDate=Credit Review Due Date
C241M01A.retrialKind=Rules
C241M01A.docStatus=Credit Review Procedure Checkpoint
C241M01A.quotaAmt=Balance
docfile.filename=File Name
docfile.uploadTime=Upload Time
#listExcel=Personal Banking Credit Review Worksheet
listPre=Scheduled Credit Review Listing
listChk=Validate Credit Review Listing
newDefaultDate=Please input the new scheduled credit review date:
notAllow=Report already completed; you cannot change the credit review status
failList=Failed to generate personal banking customers listing
noRetrial=Please select cases that do not require review
retrial=Please select cases that require review
chooseNckdflag=Please specify a reason for waiving credit review
enterCustId=Please input customer's UBN (11 digits in total, including repeated serial numbers):
alreadyHave=Detail data exists
noCustdata=No Basic Profile
branchNO=Bank ID:
preCTL=Scheduled Credit Review:
realDoor=Number Of Accounts
new=New
old=Existing
door=Accounts
item=Case
lms2405m01.comment1=Note 1: The credit review database was generated since 2010/12.
lms2405m01.comment2=Note 2: New cases refer to accounts which have not been reviewed one month before the list is generated; existing cases refer to those which have been reviewed, with credit review due dates falling one month before (inclusive) the next credit review date.
listExcel=Personal Banking Credit Review Worksheet
listPreExcel=Personal Banking Credit Review Listing
listChkExcel=Personal Banking Credit Review Validation File
alreadyHaveList=The list already contains these details
lastCTLDate=The "Credit Review Due Date" is effective as at
date=Date
CTLDate=Credit Review Date
randomCode=Random Report Code
excelTitle1="Date of Data" New Cases:up till dataDate1 Handling/Existing Case:As within the Credit Review Due Date dataDate2 (Note)
producer=Generation Personnel
noDataDate=Year and month of data not specified
reportInfo=Report Data
title.modifyDate=Change Credit Review Date
err.defaultDate=Please input a date
err.date=Input Format Error
err.update491=Executed failed
err.chooseOne=Please Select One of the Actions
err.noDate=No scheduled credit review date
button.acceptBack=Verifier
button.Update491=Save and upload the Credit Review Control File once complete
close=Close
#==================================================
# \u500b\u91d1-\u5217\u5370\u8986\u5be9\u5831\u544a\u8868
#==================================================
print.custName=Borrower's Name
print.rptNo=Report Serial Number
print.rptName=Report Name
saveBeforeSend=The data is saved automatically after executing; are you sure you want to continue?

c240m01a.docStatus010=\u7de8\u88fd\u4e2d
c240m01a.docStatus020=\u5f85\u8986\u6838
c240m01a.docStatus030=\u5df2\u6838\u51c6
c240m01a.docStatus0A0=\u5df2\u7522\u751f\u8986\u5be9\u540d\u55ae\u5831\u544a\u6a94

#J-110-0304_05097_B1001 Web e-Loan\u6388\u4fe1\u8986\u5be9\u914d\u5408RPA\u4f5c\u696d\u4fee\u6539
rpa.status.A01=RPA\u57f7\u884c\u4e2d
rpa.status.A02=RPA\u5b8c\u6210
rpa.status.A03=RPA\u7570\u5e38

