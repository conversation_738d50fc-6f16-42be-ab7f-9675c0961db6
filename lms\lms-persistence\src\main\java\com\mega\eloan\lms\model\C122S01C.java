/* 
 * C122S01C.java
 */

package com.mega.eloan.lms.model;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 經濟部中小企業處-青創貸款線上申貸案
 */
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C122S01C", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class C122S01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 簡訊OTP 查詢uuid **/
	@Column(name = "OTP_UUID", length = 36, columnDefinition = "CHAR(36)")
	private String OTP_UUID;

	/** 簡訊OTP IP */
	private String OTP_IPAddr;
	
	/** 簡訊OTP 時間 */
	private Timestamp OTP_Time;

	/** 簡訊OTP 狀態碼 */
	private String OTP_STATUS;

	/** 簡訊OTP 訊息代碼 */
	private String OTP_ERRORCODE;

	/** 簡訊OTP 核驗結果 */
	private String OTP_DATARESULT;

	/** 簡訊OTP 帳號核驗結果 */
	private String OTP_ACCTRESULT;

	/** 簡訊OTP 帳戶類別 */
	private String OTP_TYPERESULT;

	/**
	 * 是否100萬以下案件 1是 2否
	 */
	private String apply_type;

	/**
	 * 流水號
	 */
	private Integer smeaId;
	/**
	 * 事業資料-統一編號
	 */
	private String n_cnumber;
	/**
	 * 事業體名稱
	 */
	private String cname;
	/**
	 * 事業資料-設立登記日期
	 */
	private Date n_cdate;
	/**
	 * 事業體類型
	 */
	private String ctype;
	/**
	 * 事業形態補充
	 */
	private String ctype_ctxt;
	/**
	 * over Y 首次申請 ,N 非首次申請
	 */
	private String a0;
	/**
	 * under A01.曾獲貸本項貸款/A02.曾獲貸其他政府辦理之創業貸款 over
	 * A01.首次申請/A02.曾經獲貸青年創業及啟動金貸款/A03.曾經獲貸其他政府辦理之創業貸款
	 */
	private String a1;
	/**
	 * 獲貸金融機構
	 */
	private String bank1;
	/**
	 * 獲貸金融機構分行
	 */
	private String bank2;
	/**
	 * 是否已全數清償
	 */
	private String a1_1_;
	/**
	 * 其他政府辦理之創業貸款名稱
	 */
	private String a1_ctxt2;
	/**
	 * under A01.具備專業證照或技術專利 A02.曾獲相關獎項 A03.經政府立案之育成機構輔導 over
	 * 「1.準備金及開辦費」或「2.週轉性支出」或「3.資本性支出」擇一填寫
	 */
	private String a2;
	/**
	 * 專業證照或技術專利名稱
	 */
	private String a2_1;
	/**
	 * 獲獎項名稱
	 */
	private String a2_2;
	/**
	 * 政府立案之育成機構輔導名稱
	 */
	private String a2_3;
	/**
	 * 「1.準備金及開辦費」或 「2.週轉性支出」或 「3.資本性支出」擇一填寫
	 */
	private String a3;
	/**
	 * 準備金及開辦費金額
	 */
	private BigDecimal a3_1;
	/**
	 * 準備金及開辦費年限
	 */
	private BigDecimal a3_2;
	/**
	 * 準備金及開辦費寬限期
	 */
	private BigDecimal a3_3;

	/**
	 * 週轉性支出金額
	 */
	private BigDecimal a3_4;
	/**
	 * 週轉性支出年限
	 */
	private BigDecimal a3_5;
	/**
	 * 週轉性支出寬限期
	 */
	private BigDecimal a3_6;
	/**
	 * 1.用途-水電燃料費 2.用途-薪資費用 3.用途-營業租金 4.用途-購料費用 5.其他
	 */
	private String a3_7;
	/**
	 * 用途-其他補充
	 */
	private String a3_8;
	/**
	 * 資本性支出金額
	 */
	private BigDecimal a3_9;
	/**
	 * 資本性支出年限
	 */
	private BigDecimal a3_10;
	/**
	 * 資本性支出寬限期
	 */
	private BigDecimal a3_11;
	/**
	 * 所購置機器設備
	 */
	private String a3_12;
	/**
	 * 身分證字號
	 */
	// private String idnumber;
	/**
	 * 出生年月日
	 */
	private Date bday;
	/**
	 * 連絡電話
	 */
	private String tel;
	/**
	 * 電子郵件
	 */
	private String email;
	/**
	 * 教育程度
	 */
	private String elevel;
	/**
	 * 戶籍地址:縣/市
	 */
	private String citya;
	/**
	 * 戶籍地址:區/市/鄉/鎮
	 */
	private String cityareaa;
	/**
	 * 戶籍地址:路/街
	 */
	private String address;
	/**
	 * 通訊地址:同戶籍
	 */
	private String cform;
	/**
	 * 通訊地址:縣/市
	 */
	private String cityb;
	/**
	 * 通訊地址:區/市/鄉/鎮
	 */
	private String cityareab;
	/**
	 * 通訊地址:路/街
	 */
	private String address2;
	/**
	 * 職稱
	 */
	private String enjob;
	/**
	 * 年資
	 */
	private String enyear;
	/**
	 * 現職年薪
	 */
	private String eninput;
	/**
	 * 婚姻狀態
	 */
	private String marital;
	/**
	 * 配偶姓名
	 */
	private String matename;
	/**
	 * 方便往來縣市
	 */
	private String enselect1;
	/**
	 * 方便往來鄉鎮市區
	 */
	private String enselect2;
	/**
	 * 創業事業報稅營收
	 */
	private String input2;
	/**
	 * 創業事業報稅年份
	 */
	private String input3;
	/**
	 * 登記出資額
	 */
	private String input4;
	/**
	 * 提供銀行其他資訊
	 */
	private String infsl;
	/**
	 * 往來行庫
	 */
	private String enbank1;
	/**
	 * 往來分行別
	 */
	private String enbank2;
	/**
	 * 往來帳號
	 */
	private String enaccount;
	/**
	 * 經歷-服務處所名稱-01
	 */
	private String sname1;
	/**
	 * 經歷-職稱-01
	 */
	private String sjob1;
	/**
	 * 經歷-年資-01
	 */
	private BigDecimal syear1;
	/**
	 * 經歷-服務處所名稱-02
	 */
	private String sname2;
	/**
	 * 經歷-職稱-02
	 */
	private String sjob2;
	/**
	 * 經歷-年資-02
	 */
	private BigDecimal syear2;
	/**
	 * 經歷-服務處所名稱-03
	 */
	private String sname3;
	/**
	 * 經歷-職稱-03
	 */
	private String sjob3;
	/**
	 * 經歷-年資-03
	 */
	private BigDecimal syear3;
	/**
	 * 經歷-服務處所名稱-04
	 */
	private String sname4;
	/**
	 * 經歷-職稱-04
	 */
	private String sjob4;
	/**
	 * 經歷-年資-04
	 */
	private BigDecimal syear4;
	/**
	 * 輔導課程-課程內容-01
	 */
	private String class_ctxt;
	/**
	 * 輔導課程-授課單位-01
	 */
	private String class_unit;
	/**
	 * 輔導課程-時數-01
	 */
	private BigDecimal class_hour;
	/**
	 * 輔導課程-課程內容-02
	 */
	private String class2_ctxt;
	/**
	 * 輔導課程-授課單位-02
	 */
	private String class2_unit;
	/**
	 * 輔導課程-時數-02
	 */
	private BigDecimal class2_hour;
	/**
	 * 輔導課程-課程內容-03
	 */
	private String class3_ctxt;
	/**
	 * 輔導課程-授課單位-03
	 */
	private String class3_unit;
	/**
	 * 輔導課程-時數-03
	 */
	private BigDecimal class3_hour;
	/**
	 * 輔導課程-課程內容-04
	 */
	private String class4_unit;
	/**
	 * 輔導課程-授課單位-04
	 */
	private String class4_ctxt;
	/**
	 * 輔導課程-時數-04
	 */
	private BigDecimal class4_hour;
	/**
	 * 輔導課程-課程內容-05
	 */
	private String class5_unit;
	/**
	 * 輔導課程-授課單位-05
	 */
	private String class5_ctxt;
	/**
	 * 輔導課程-時數-05
	 */
	private BigDecimal class5_hour;
	/**
	 * 輔導課程-課程內容-06
	 */
	private String class6_unit;
	/**
	 * 輔導課程-授課單位-06
	 */
	private String class6_ctxt;
	/**
	 * 輔導課程-時數-06
	 */
	private BigDecimal class6_hour;
	/**
	 * 營業地址:縣/市
	 */
	private String city;
	/**
	 * 營業地址:區/市/鄉/鎮
	 */
	private String cityarea;
	/**
	 * 營業地址:路/街
	 */
	private String n_address;
	/**
	 * 營業地址:聯絡電話
	 */
	private String n_tel;
	/**
	 * 工廠地址:縣/市
	 */
	private String city2;
	/**
	 * 工廠地址:區/市/鄉/鎮
	 */
	private String cityarea2;
	/**
	 * 工廠地址:路/街
	 */
	private String n_address2;
	/**
	 * 工廠地址:聯絡電話
	 */
	private String n_phone;
	/**
	 * 其他:縣/市
	 */
	private String city3;
	/**
	 * 其他:區/市/鄉/鎮
	 */
	private String cityarea3;
	/**
	 * 其他:路/街
	 */
	private String n_addressctxt;
	/**
	 * 其他:聯絡電話
	 */
	private String n_fax;
	/**
	 * 產品及服務項目
	 */
	private String n_ctxt;
	/**
	 * 四-(一)1對所營產業熟悉度
	 */
	private String q1;
	/**
	 * 四-(一)2銷售(服務)項目
	 */
	private String q2;
	/**
	 * 四-(一)3原料供應及採購來源
	 */
	private String q3;
	/**
	 * 四-(一)4生產及服務複雜度
	 */
	private String q4;
	/**
	 * 四-(一)5核心技術與專利(證照)
	 */
	private String q5;
	/**
	 * 四-(一)6人力培育與管理
	 */
	private String q6;
	/**
	 * 四-(一)7銷售(服務)通路多寡
	 */
	private String q7;
	/**
	 * 四-(一)8品質(客服)管理及改進
	 */
	private String q8;
	/**
	 * 四-(二)1所營產業競爭程度
	 */
	private String q9;
	/**
	 * 四-(二)2所營產業需求前景
	 */
	private String q10;
	/**
	 * 四-(二)3市場目標客群
	 */
	private String q11;
	/**
	 * 四-(二)4鄰近競爭對手
	 */
	private String q12;
	/**
	 * 四-(二)5定價策略獲利性
	 */
	private String q13;
	/**
	 * 四-(二)6行銷策略與管道
	 */
	private String q14;
	/**
	 * 預估獲貸後第一年營業(服務)收入
	 */
	private String q15;
	/**
	 * 預估獲貸後第二年起營業(服務)收入年成長率
	 */
	private String q16;
	/**
	 * 預估獲貸後第三年起營業(服務)收入年成長率
	 */
	private String q17;
	/**
	 * 預估獲貸後第一年息前稅前淨利率(扣除相關成本及費用)
	 */
	private String q18;
	/**
	 * 預估獲貸後第二年息前稅前淨利率(扣除相關成本及費用)
	 */
	private String q19;
	/**
	 * 預估獲貸後第三年起息前稅前淨利率(扣除相關成本及費用)
	 */
	private String q20;
	/**
	 * 本人已詳閱並同意上述事項
	 */
	private String agree1;
	/**
	 * 青年創業及啟動金貸款切結書
	 */
	private String agree2;

	// ------------------------Over 100
	/**
	 * 準備金及開辦費金額(申請背景)
	 */
	private BigDecimal a1_1;
	/**
	 * 週轉性支出金額(申請背景)
	 */
	private BigDecimal a1_2;
	/**
	 * 資本性支出金額(申請背景)
	 */
	private BigDecimal a1_3;
	/**
	 * 其他政府辦理之創業貸款名稱
	 */
	private String a1_5;

	/**
	 * 配偶身分證字號
	 */
	private String mateid;
	/**
	 * 現住房屋持有狀態
	 */
	private String house;

	/**
	 * 主業行業
	 **/
	private String c1;
	/**
	 * 主要產品(或業務)
	 **/
	private String c2;
	/**
	 * 現有員工人數(不含本人)
	 **/
	private BigDecimal c3;
	/**
	 * 生財設備-項目-01
	 **/
	private String d1_1;
	/**
	 * 生財設備-數量-01
	 **/
	private BigDecimal d1_2;
	/**
	 * 生財設備-單價-01
	 **/
	private BigDecimal d1_3;
	/**
	 * 生財設備-總價-01
	 **/
	private BigDecimal d1_4;
	/**
	 * 生財設備-項目-02
	 **/
	private String d1_5;
	/**
	 * 生財設備-數量-02
	 **/
	private BigDecimal d1_6;
	/**
	 * 生財設備-單價-02
	 **/
	private BigDecimal d1_7;
	/**
	 * 生財設備-總價-02
	 **/
	private BigDecimal d1_8;
	/**
	 * 生財設備-項目-03
	 **/
	private String d1_9;
	/**
	 * 生財設備-數量-03
	 **/
	private BigDecimal d1_10;
	/**
	 * 生財設備-單價-03
	 **/
	private BigDecimal d1_11;
	/**
	 * 生財設備-總價-03
	 **/
	private BigDecimal d1_12;
	/**
	 * 生財設備-項目-04
	 **/
	private String d1_13;
	/**
	 * 生財設備-數量-04
	 **/
	private BigDecimal d1_14;
	/**
	 * 生財設備-單價-04
	 **/
	private BigDecimal d1_15;
	/**
	 * 生財設備-總價-04
	 **/
	private BigDecimal d1_16;
	/**
	 * 生財設備-項目-05
	 **/
	private String d1_17;
	/**
	 * 生財設備-數量-05
	 **/
	private BigDecimal d1_18;
	/**
	 * 生財設備-單價-05
	 **/
	private BigDecimal d1_19;
	/**
	 * 生財設備-總價-05
	 **/
	private BigDecimal d1_20;
	/**
	 * 生財設備-小計
	 **/
	private BigDecimal d1_subtotal;
	/**
	 * 週轉金-水電費-數量
	 **/
	private BigDecimal d2_1;
	/**
	 * 週轉金-水電費-單價
	 **/
	private BigDecimal d2_2;
	/**
	 * 週轉金-水電費-總價
	 **/
	private BigDecimal d2_3;
	/**
	 * 週轉金-營業場所租金-數量
	 **/
	private BigDecimal d2_4;
	/**
	 * 週轉金-營業場所租金-單價
	 **/
	private BigDecimal d2_5;
	/**
	 * 週轉金-營業場所租金-總價
	 **/
	private BigDecimal d2_6;
	/**
	 * 週轉金-薪資-數量
	 **/
	private BigDecimal d2_7;
	/**
	 * 週轉金-薪資-單價
	 **/
	private BigDecimal d2_8;
	/**
	 * 週轉金-薪資-總價
	 **/
	private BigDecimal d2_9;
	/**
	 * 週轉金-購買原物料-數量
	 **/
	private BigDecimal d2_10;
	/**
	 * 週轉金-購買原物料-單價
	 **/
	private BigDecimal d2_11;
	/**
	 * 週轉金-購買原物料-總價
	 **/
	private BigDecimal d2_12;
	/**
	 * 週轉金-其他-項目-01
	 **/
	private String d2_13;
	/**
	 * 週轉金-其他-數量-01
	 **/
	private BigDecimal d2_14;
	/**
	 * 週轉金-其他-單價-01
	 **/
	private BigDecimal d2_15;
	/**
	 * 週轉金-其他-總價
	 **/
	private BigDecimal d2_16;
	/**
	 * 週轉金-其他-項目-02
	 **/
	private String d2_17;
	/**
	 * 週轉金-其他-數量-02
	 **/
	private BigDecimal d2_18;
	/**
	 * 週轉金-其他-單價-02
	 **/
	private BigDecimal d2_19;
	/**
	 * 週轉金-其他-總價-02
	 **/
	private BigDecimal d2_20;
	/**
	 * 週轉金-小計
	 **/
	private BigDecimal d2_subtotal;
	/**
	 * 生財器具備或生產設備及週轉金合計
	 **/
	private BigDecimal total;
	/**
	 * 經營現況-服務(產品)名稱
	 **/
	private String e1_1;
	/**
	 * 經營現況-主要用途
	 **/
	private String e1_2;
	/**
	 * 經營現況-特點
	 **/
	private String e1_3;
	/**
	 * 經營現況-其他
	 **/
	private String e1_4;
	/**
	 * 市場分析-主要服務(產品)市場
	 **/
	private String e2_1;
	/**
	 * 市場分析-潛在客源
	 **/
	private String e2_2;
	/**
	 * 市場分析-銷售方式
	 **/
	private String e2_3;
	/**
	 * 市場分析-市場潛力
	 **/
	private String e2_4;
	/**
	 * 市場分析-未來展望
	 **/
	private String e2_5;
	/**
	 * 市場分析-其他
	 **/
	private String e2_6;
	/**
	 * 預估獲貸後第一年營業收入
	 **/
	private BigDecimal e3_1;
	/**
	 * 營業收入
	 **/
	private BigDecimal e3_2;
	/**
	 * 營業成本
	 **/
	private BigDecimal e3_3;
	/**
	 * 營業毛利
	 **/
	private BigDecimal e3_4;
	/**
	 * 管銷費用
	 **/
	private BigDecimal e3_5;
	/**
	 * 營業淨利
	 **/
	private BigDecimal e3_6;
	/**
	 * 營業外收入
	 **/
	private BigDecimal e3_7;
	/**
	 * 營業外支出
	 **/
	private BigDecimal e3_8;
	/**
	 * 本期損益
	 **/
	private BigDecimal e3_9;
	/**
	 * 還款來源
	 **/
	private String e3_10;
	/**
	 * 其他
	 **/
	private String e3_11;

	@Override
	public String getOid() {
		return oid;
	}

	@Override
	public void setOid(String oid) {
		this.oid = oid;
	}

	@Override
	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public Integer getSmeaId() {
		return smeaId;
	}

	public void setSmeaId(Integer smeaId) {
		this.smeaId = smeaId;
	}

	public String getCname() {
		return cname;
	}

	public void setCname(String cname) {
		this.cname = cname;
	}

	public String getCtype() {
		return ctype;
	}

	public void setCtype(String ctype) {
		this.ctype = ctype;
	}

	public String getCtype_ctxt() {
		return ctype_ctxt;
	}

	public void setCtype_ctxt(String ctype_ctxt) {
		this.ctype_ctxt = ctype_ctxt;
	}

	public String getA0() {
		return a0;
	}

	public void setA0(String a0) {
		this.a0 = a0;
	}

	public String getA1() {
		return a1;
	}

	public void setA1(String a1) {
		this.a1 = a1;
	}

	public String getBank1() {
		return bank1;
	}

	public void setBank1(String bank1) {
		this.bank1 = bank1;
	}

	public String getBank2() {
		return bank2;
	}

	public void setBank2(String bank2) {
		this.bank2 = bank2;
	}

	public String getA1_ctxt2() {
		return a1_ctxt2;
	}

	public void setA1_ctxt2(String a1_ctxt2) {
		this.a1_ctxt2 = a1_ctxt2;
	}

	public String getA2() {
		return a2;
	}

	public void setA2(String a2) {
		this.a2 = a2;
	}

	public String getA2_1() {
		return a2_1;
	}

	public void setA2_1(String a2_1) {
		this.a2_1 = a2_1;
	}

	public String getA2_2() {
		return a2_2;
	}

	public void setA2_2(String a2_2) {
		this.a2_2 = a2_2;
	}

	public String getA2_3() {
		return a2_3;
	}

	public void setA2_3(String a2_3) {
		this.a2_3 = a2_3;
	}

	public String getA3() {
		return a3;
	}

	public void setA3(String a3) {
		this.a3 = a3;
	}

	public BigDecimal getA3_1() {
		return a3_1;
	}

	public void setA3_1(BigDecimal a3_1) {
		this.a3_1 = a3_1;
	}

	public BigDecimal getA3_2() {
		return a3_2;
	}

	public void setA3_2(BigDecimal a3_2) {
		this.a3_2 = a3_2;
	}

	public BigDecimal getA3_3() {
		return a3_3;
	}

	public void setA3_3(BigDecimal a3_3) {
		this.a3_3 = a3_3;
	}

	public BigDecimal getA3_4() {
		return a3_4;
	}

	public void setA3_4(BigDecimal a3_4) {
		this.a3_4 = a3_4;
	}

	public BigDecimal getA3_5() {
		return a3_5;
	}

	public void setA3_5(BigDecimal a3_5) {
		this.a3_5 = a3_5;
	}

	public BigDecimal getA3_6() {
		return a3_6;
	}

	public void setA3_6(BigDecimal a3_6) {
		this.a3_6 = a3_6;
	}

	public String getA3_7() {
		return a3_7;
	}

	public void setA3_7(String a3_7) {
		this.a3_7 = a3_7;
	}

	public String getA3_8() {
		return a3_8;
	}

	public void setA3_8(String a3_8) {
		this.a3_8 = a3_8;
	}

	public BigDecimal getA3_9() {
		return a3_9;
	}

	public void setA3_9(BigDecimal a3_9) {
		this.a3_9 = a3_9;
	}

	public BigDecimal getA3_10() {
		return a3_10;
	}

	public void setA3_10(BigDecimal a3_10) {
		this.a3_10 = a3_10;
	}

	public BigDecimal getA3_11() {
		return a3_11;
	}

	public void setA3_11(BigDecimal a3_11) {
		this.a3_11 = a3_11;
	}

	public String getA3_12() {
		return a3_12;
	}

	public void setA3_12(String a3_12) {
		this.a3_12 = a3_12;
	}

	public Date getBday() {
		return bday;
	}

	public void setBday(Date bday) {
		this.bday = bday;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getElevel() {
		return elevel;
	}

	public void setElevel(String elevel) {
		this.elevel = elevel;
	}

	public String getCitya() {
		return citya;
	}

	public void setCitya(String citya) {
		this.citya = citya;
	}

	public String getCityareaa() {
		return cityareaa;
	}

	public void setCityareaa(String cityareaa) {
		this.cityareaa = cityareaa;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getCform() {
		return cform;
	}

	public void setCform(String cform) {
		this.cform = cform;
	}

	public String getCityb() {
		return cityb;
	}

	public void setCityb(String cityb) {
		this.cityb = cityb;
	}

	public String getCityareab() {
		return cityareab;
	}

	public void setCityareab(String cityareab) {
		this.cityareab = cityareab;
	}

	public String getAddress2() {
		return address2;
	}

	public void setAddress2(String address2) {
		this.address2 = address2;
	}

	public String getEnjob() {
		return enjob;
	}

	public void setEnjob(String enjob) {
		this.enjob = enjob;
	}

	public String getEnyear() {
		return enyear;
	}

	public void setEnyear(String enyear) {
		this.enyear = enyear;
	}

	public String getEninput() {
		return eninput;
	}

	public void setEninput(String eninput) {
		this.eninput = eninput;
	}

	public String getMarital() {
		return marital;
	}

	public void setMarital(String marital) {
		this.marital = marital;
	}

	public String getMatename() {
		return matename;
	}

	public void setMatename(String matename) {
		this.matename = matename;
	}

	public String getEnselect1() {
		return enselect1;
	}

	public void setEnselect1(String enselect1) {
		this.enselect1 = enselect1;
	}

	public String getEnselect2() {
		return enselect2;
	}

	public void setEnselect2(String enselect2) {
		this.enselect2 = enselect2;
	}

	public String getInput2() {
		return input2;
	}

	public void setInput2(String input2) {
		this.input2 = input2;
	}

	public String getInput3() {
		return input3;
	}

	public void setInput3(String input3) {
		this.input3 = input3;
	}

	public String getInput4() {
		return input4;
	}

	public void setInput4(String input4) {
		this.input4 = input4;
	}

	public String getInfsl() {
		return infsl;
	}

	public void setInfsl(String infsl) {
		this.infsl = infsl;
	}

	public String getEnbank1() {
		return enbank1;
	}

	public void setEnbank1(String enbank1) {
		this.enbank1 = enbank1;
	}

	public String getEnbank2() {
		return enbank2;
	}

	public void setEnbank2(String enbank2) {
		this.enbank2 = enbank2;
	}

	public String getEnaccount() {
		return enaccount;
	}

	public void setEnaccount(String enaccount) {
		this.enaccount = enaccount;
	}

	public String getSname1() {
		return sname1;
	}

	public void setSname1(String sname1) {
		this.sname1 = sname1;
	}

	public String getSjob1() {
		return sjob1;
	}

	public void setSjob1(String sjob1) {
		this.sjob1 = sjob1;
	}

	public BigDecimal getSyear1() {
		return syear1;
	}

	public void setSyear1(BigDecimal syear1) {
		this.syear1 = syear1;
	}

	public String getSname2() {
		return sname2;
	}

	public void setSname2(String sname2) {
		this.sname2 = sname2;
	}

	public String getSjob2() {
		return sjob2;
	}

	public void setSjob2(String sjob2) {
		this.sjob2 = sjob2;
	}

	public BigDecimal getSyear2() {
		return syear2;
	}

	public void setSyear2(BigDecimal syear2) {
		this.syear2 = syear2;
	}

	public String getSname3() {
		return sname3;
	}

	public void setSname3(String sname3) {
		this.sname3 = sname3;
	}

	public String getSjob3() {
		return sjob3;
	}

	public void setSjob3(String sjob3) {
		this.sjob3 = sjob3;
	}

	public BigDecimal getSyear3() {
		return syear3;
	}

	public void setSyear3(BigDecimal syear3) {
		this.syear3 = syear3;
	}

	public String getSname4() {
		return sname4;
	}

	public void setSname4(String sname4) {
		this.sname4 = sname4;
	}

	public String getSjob4() {
		return sjob4;
	}

	public void setSjob4(String sjob4) {
		this.sjob4 = sjob4;
	}

	public BigDecimal getSyear4() {
		return syear4;
	}

	public void setSyear4(BigDecimal syear4) {
		this.syear4 = syear4;
	}

	public String getClass_ctxt() {
		return class_ctxt;
	}

	public void setClass_ctxt(String class_ctxt) {
		this.class_ctxt = class_ctxt;
	}

	public String getClass_unit() {
		return class_unit;
	}

	public void setClass_unit(String class_unit) {
		this.class_unit = class_unit;
	}

	public BigDecimal getClass_hour() {
		return class_hour;
	}

	public void setClass_hour(BigDecimal class_hour) {
		this.class_hour = class_hour;
	}

	public String getClass2_ctxt() {
		return class2_ctxt;
	}

	public void setClass2_ctxt(String class2_ctxt) {
		this.class2_ctxt = class2_ctxt;
	}

	public String getClass2_unit() {
		return class2_unit;
	}

	public void setClass2_unit(String class2_unit) {
		this.class2_unit = class2_unit;
	}

	public BigDecimal getClass2_hour() {
		return class2_hour;
	}

	public void setClass2_hour(BigDecimal class2_hour) {
		this.class2_hour = class2_hour;
	}

	public String getClass3_ctxt() {
		return class3_ctxt;
	}

	public void setClass3_ctxt(String class3_ctxt) {
		this.class3_ctxt = class3_ctxt;
	}

	public String getClass3_unit() {
		return class3_unit;
	}

	public void setClass3_unit(String class3_unit) {
		this.class3_unit = class3_unit;
	}

	public BigDecimal getClass3_hour() {
		return class3_hour;
	}

	public void setClass3_hour(BigDecimal class3_hour) {
		this.class3_hour = class3_hour;
	}

	public String getClass4_unit() {
		return class4_unit;
	}

	public void setClass4_unit(String class4_unit) {
		this.class4_unit = class4_unit;
	}

	public String getClass4_ctxt() {
		return class4_ctxt;
	}

	public void setClass4_ctxt(String class4_ctxt) {
		this.class4_ctxt = class4_ctxt;
	}

	public BigDecimal getClass4_hour() {
		return class4_hour;
	}

	public void setClass4_hour(BigDecimal class4_hour) {
		this.class4_hour = class4_hour;
	}

	public String getClass5_unit() {
		return class5_unit;
	}

	public void setClass5_unit(String class5_unit) {
		this.class5_unit = class5_unit;
	}

	public String getClass5_ctxt() {
		return class5_ctxt;
	}

	public void setClass5_ctxt(String class5_ctxt) {
		this.class5_ctxt = class5_ctxt;
	}

	public BigDecimal getClass5_hour() {
		return class5_hour;
	}

	public void setClass5_hour(BigDecimal class5_hour) {
		this.class5_hour = class5_hour;
	}

	public String getClass6_unit() {
		return class6_unit;
	}

	public void setClass6_unit(String class6_unit) {
		this.class6_unit = class6_unit;
	}

	public String getClass6_ctxt() {
		return class6_ctxt;
	}

	public void setClass6_ctxt(String class6_ctxt) {
		this.class6_ctxt = class6_ctxt;
	}

	public BigDecimal getClass6_hour() {
		return class6_hour;
	}

	public void setClass6_hour(BigDecimal class6_hour) {
		this.class6_hour = class6_hour;
	}

	public String getN_cnumber() {
		return n_cnumber;
	}

	public void setN_cnumber(String n_cnumber) {
		this.n_cnumber = n_cnumber;
	}

	public Date getN_cdate() {
		return n_cdate;
	}

	public void setN_cdate(Date n_cdate) {
		this.n_cdate = n_cdate;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCityarea() {
		return cityarea;
	}

	public void setCityarea(String cityarea) {
		this.cityarea = cityarea;
	}

	public String getN_address() {
		return n_address;
	}

	public void setN_address(String n_address) {
		this.n_address = n_address;
	}

	public String getN_tel() {
		return n_tel;
	}

	public void setN_tel(String n_tel) {
		this.n_tel = n_tel;
	}

	public String getCity2() {
		return city2;
	}

	public void setCity2(String city2) {
		this.city2 = city2;
	}

	public String getCityarea2() {
		return cityarea2;
	}

	public void setCityarea2(String cityarea2) {
		this.cityarea2 = cityarea2;
	}

	public String getN_address2() {
		return n_address2;
	}

	public void setN_address2(String n_address2) {
		this.n_address2 = n_address2;
	}

	public String getN_phone() {
		return n_phone;
	}

	public void setN_phone(String n_phone) {
		this.n_phone = n_phone;
	}

	public String getCity3() {
		return city3;
	}

	public void setCity3(String city3) {
		this.city3 = city3;
	}

	public String getCityarea3() {
		return cityarea3;
	}

	public void setCityarea3(String cityarea3) {
		this.cityarea3 = cityarea3;
	}

	public String getN_addressctxt() {
		return n_addressctxt;
	}

	public void setN_addressctxt(String n_addressctxt) {
		this.n_addressctxt = n_addressctxt;
	}

	public String getN_fax() {
		return n_fax;
	}

	public void setN_fax(String n_fax) {
		this.n_fax = n_fax;
	}

	public String getN_ctxt() {
		return n_ctxt;
	}

	public void setN_ctxt(String n_ctxt) {
		this.n_ctxt = n_ctxt;
	}

	public String getQ1() {
		return q1;
	}

	public void setQ1(String q1) {
		this.q1 = q1;
	}

	public String getQ2() {
		return q2;
	}

	public void setQ2(String q2) {
		this.q2 = q2;
	}

	public String getQ3() {
		return q3;
	}

	public void setQ3(String q3) {
		this.q3 = q3;
	}

	public String getQ4() {
		return q4;
	}

	public void setQ4(String q4) {
		this.q4 = q4;
	}

	public String getQ5() {
		return q5;
	}

	public void setQ5(String q5) {
		this.q5 = q5;
	}

	public String getQ6() {
		return q6;
	}

	public void setQ6(String q6) {
		this.q6 = q6;
	}

	public String getQ7() {
		return q7;
	}

	public void setQ7(String q7) {
		this.q7 = q7;
	}

	public String getQ8() {
		return q8;
	}

	public void setQ8(String q8) {
		this.q8 = q8;
	}

	public String getQ9() {
		return q9;
	}

	public void setQ9(String q9) {
		this.q9 = q9;
	}

	public String getQ10() {
		return q10;
	}

	public void setQ10(String q10) {
		this.q10 = q10;
	}

	public String getQ11() {
		return q11;
	}

	public void setQ11(String q11) {
		this.q11 = q11;
	}

	public String getQ12() {
		return q12;
	}

	public void setQ12(String q12) {
		this.q12 = q12;
	}

	public String getQ13() {
		return q13;
	}

	public void setQ13(String q13) {
		this.q13 = q13;
	}

	public String getQ14() {
		return q14;
	}

	public void setQ14(String q14) {
		this.q14 = q14;
	}

	public String getQ15() {
		return q15;
	}

	public void setQ15(String q15) {
		this.q15 = q15;
	}

	public String getQ16() {
		return q16;
	}

	public void setQ16(String q16) {
		this.q16 = q16;
	}

	public String getQ17() {
		return q17;
	}

	public void setQ17(String q17) {
		this.q17 = q17;
	}

	public String getQ18() {
		return q18;
	}

	public void setQ18(String q18) {
		this.q18 = q18;
	}

	public String getQ19() {
		return q19;
	}

	public void setQ19(String q19) {
		this.q19 = q19;
	}

	public String getQ20() {
		return q20;
	}

	public void setQ20(String q20) {
		this.q20 = q20;
	}

	public String getAgree1() {
		return agree1;
	}

	public void setAgree1(String agree1) {
		this.agree1 = agree1;
	}

	public String getAgree2() {
		return agree2;
	}

	public void setAgree2(String agree2) {
		this.agree2 = agree2;
	}

	public String getApply_type() {
		return apply_type;
	}

	public void setApply_type(String apply_type) {
		this.apply_type = apply_type;
	}

	public BigDecimal getA1_2() {
		return a1_2;
	}

	public void setA1_2(BigDecimal a1_2) {
		this.a1_2 = a1_2;
	}

	public BigDecimal getA1_3() {
		return a1_3;
	}

	public void setA1_3(BigDecimal a1_3) {
		this.a1_3 = a1_3;
	}

	public String getA1_5() {
		return a1_5;
	}

	public void setA1_5(String a1_5) {
		this.a1_5 = a1_5;
	}

	public String getMateid() {
		return mateid;
	}

	public void setMateid(String mateid) {
		this.mateid = mateid;
	}

	public String getHouse() {
		return house;
	}

	public void setHouse(String house) {
		this.house = house;
	}

	public String getA1_1_() {
		return a1_1_;
	}

	public void setA1_1_(String a1_1_) {
		this.a1_1_ = a1_1_;
	}

	public BigDecimal getA1_1() {
		return a1_1;
	}

	public void setA1_1(BigDecimal a1_1) {
		this.a1_1 = a1_1;
	}

	public String getC1() {
		return c1;
	}

	public void setC1(String c1) {
		this.c1 = c1;
	}

	public String getC2() {
		return c2;
	}

	public void setC2(String c2) {
		this.c2 = c2;
	}

	public BigDecimal getC3() {
		return c3;
	}

	public void setC3(BigDecimal c3) {
		this.c3 = c3;
	}

	public String getD1_1() {
		return d1_1;
	}

	public void setD1_1(String d1_1) {
		this.d1_1 = d1_1;
	}

	public BigDecimal getD1_2() {
		return d1_2;
	}

	public void setD1_2(BigDecimal d1_2) {
		this.d1_2 = d1_2;
	}

	public BigDecimal getD1_3() {
		return d1_3;
	}

	public void setD1_3(BigDecimal d1_3) {
		this.d1_3 = d1_3;
	}

	public BigDecimal getD1_4() {
		return d1_4;
	}

	public void setD1_4(BigDecimal d1_4) {
		this.d1_4 = d1_4;
	}

	public String getD1_5() {
		return d1_5;
	}

	public void setD1_5(String d1_5) {
		this.d1_5 = d1_5;
	}

	public BigDecimal getD1_6() {
		return d1_6;
	}

	public void setD1_6(BigDecimal d1_6) {
		this.d1_6 = d1_6;
	}

	public BigDecimal getD1_7() {
		return d1_7;
	}

	public void setD1_7(BigDecimal d1_7) {
		this.d1_7 = d1_7;
	}

	public BigDecimal getD1_8() {
		return d1_8;
	}

	public void setD1_8(BigDecimal d1_8) {
		this.d1_8 = d1_8;
	}

	public String getD1_9() {
		return d1_9;
	}

	public void setD1_9(String d1_9) {
		this.d1_9 = d1_9;
	}

	public BigDecimal getD1_10() {
		return d1_10;
	}

	public void setD1_10(BigDecimal d1_10) {
		this.d1_10 = d1_10;
	}

	public BigDecimal getD1_11() {
		return d1_11;
	}

	public void setD1_11(BigDecimal d1_11) {
		this.d1_11 = d1_11;
	}

	public BigDecimal getD1_12() {
		return d1_12;
	}

	public void setD1_12(BigDecimal d1_12) {
		this.d1_12 = d1_12;
	}

	public String getD1_13() {
		return d1_13;
	}

	public void setD1_13(String d1_13) {
		this.d1_13 = d1_13;
	}

	public BigDecimal getD1_14() {
		return d1_14;
	}

	public void setD1_14(BigDecimal d1_14) {
		this.d1_14 = d1_14;
	}

	public BigDecimal getD1_15() {
		return d1_15;
	}

	public void setD1_15(BigDecimal d1_15) {
		this.d1_15 = d1_15;
	}

	public BigDecimal getD1_16() {
		return d1_16;
	}

	public void setD1_16(BigDecimal d1_16) {
		this.d1_16 = d1_16;
	}

	public String getD1_17() {
		return d1_17;
	}

	public void setD1_17(String d1_17) {
		this.d1_17 = d1_17;
	}

	public BigDecimal getD1_18() {
		return d1_18;
	}

	public void setD1_18(BigDecimal d1_18) {
		this.d1_18 = d1_18;
	}

	public BigDecimal getD1_19() {
		return d1_19;
	}

	public void setD1_19(BigDecimal d1_19) {
		this.d1_19 = d1_19;
	}

	public BigDecimal getD1_20() {
		return d1_20;
	}

	public void setD1_20(BigDecimal d1_20) {
		this.d1_20 = d1_20;
	}

	public BigDecimal getD1_subtotal() {
		return d1_subtotal;
	}

	public void setD1_subtotal(BigDecimal d1_subtotal) {
		this.d1_subtotal = d1_subtotal;
	}

	public BigDecimal getD2_1() {
		return d2_1;
	}

	public void setD2_1(BigDecimal d2_1) {
		this.d2_1 = d2_1;
	}

	public BigDecimal getD2_2() {
		return d2_2;
	}

	public void setD2_2(BigDecimal d2_2) {
		this.d2_2 = d2_2;
	}

	public BigDecimal getD2_3() {
		return d2_3;
	}

	public void setD2_3(BigDecimal d2_3) {
		this.d2_3 = d2_3;
	}

	public BigDecimal getD2_4() {
		return d2_4;
	}

	public void setD2_4(BigDecimal d2_4) {
		this.d2_4 = d2_4;
	}

	public BigDecimal getD2_5() {
		return d2_5;
	}

	public void setD2_5(BigDecimal d2_5) {
		this.d2_5 = d2_5;
	}

	public BigDecimal getD2_6() {
		return d2_6;
	}

	public void setD2_6(BigDecimal d2_6) {
		this.d2_6 = d2_6;
	}

	public BigDecimal getD2_7() {
		return d2_7;
	}

	public void setD2_7(BigDecimal d2_7) {
		this.d2_7 = d2_7;
	}

	public BigDecimal getD2_8() {
		return d2_8;
	}

	public void setD2_8(BigDecimal d2_8) {
		this.d2_8 = d2_8;
	}

	public BigDecimal getD2_9() {
		return d2_9;
	}

	public void setD2_9(BigDecimal d2_9) {
		this.d2_9 = d2_9;
	}

	public BigDecimal getD2_10() {
		return d2_10;
	}

	public void setD2_10(BigDecimal d2_10) {
		this.d2_10 = d2_10;
	}

	public BigDecimal getD2_11() {
		return d2_11;
	}

	public void setD2_11(BigDecimal d2_11) {
		this.d2_11 = d2_11;
	}

	public BigDecimal getD2_12() {
		return d2_12;
	}

	public void setD2_12(BigDecimal d2_12) {
		this.d2_12 = d2_12;
	}

	public String getD2_13() {
		return d2_13;
	}

	public void setD2_13(String d2_13) {
		this.d2_13 = d2_13;
	}

	public BigDecimal getD2_14() {
		return d2_14;
	}

	public void setD2_14(BigDecimal d2_14) {
		this.d2_14 = d2_14;
	}

	public BigDecimal getD2_15() {
		return d2_15;
	}

	public void setD2_15(BigDecimal d2_15) {
		this.d2_15 = d2_15;
	}

	public BigDecimal getD2_16() {
		return d2_16;
	}

	public void setD2_16(BigDecimal d2_16) {
		this.d2_16 = d2_16;
	}

	public String getD2_17() {
		return d2_17;
	}

	public void setD2_17(String d2_17) {
		this.d2_17 = d2_17;
	}

	public BigDecimal getD2_18() {
		return d2_18;
	}

	public void setD2_18(BigDecimal d2_18) {
		this.d2_18 = d2_18;
	}

	public BigDecimal getD2_19() {
		return d2_19;
	}

	public void setD2_19(BigDecimal d2_19) {
		this.d2_19 = d2_19;
	}

	public BigDecimal getD2_20() {
		return d2_20;
	}

	public void setD2_20(BigDecimal d2_20) {
		this.d2_20 = d2_20;
	}

	public BigDecimal getD2_subtotal() {
		return d2_subtotal;
	}

	public void setD2_subtotal(BigDecimal d2_subtotal) {
		this.d2_subtotal = d2_subtotal;
	}

	public BigDecimal getTotal() {
		return total;
	}

	public void setTotal(BigDecimal total) {
		this.total = total;
	}

	public String getE1_1() {
		return e1_1;
	}

	public void setE1_1(String e1_1) {
		this.e1_1 = e1_1;
	}

	public String getE1_2() {
		return e1_2;
	}

	public void setE1_2(String e1_2) {
		this.e1_2 = e1_2;
	}

	public String getE1_3() {
		return e1_3;
	}

	public void setE1_3(String e1_3) {
		this.e1_3 = e1_3;
	}

	public String getE1_4() {
		return e1_4;
	}

	public void setE1_4(String e1_4) {
		this.e1_4 = e1_4;
	}

	public String getE2_1() {
		return e2_1;
	}

	public void setE2_1(String e2_1) {
		this.e2_1 = e2_1;
	}

	public String getE2_2() {
		return e2_2;
	}

	public void setE2_2(String e2_2) {
		this.e2_2 = e2_2;
	}

	public String getE2_3() {
		return e2_3;
	}

	public void setE2_3(String e2_3) {
		this.e2_3 = e2_3;
	}

	public String getE2_4() {
		return e2_4;
	}

	public void setE2_4(String e2_4) {
		this.e2_4 = e2_4;
	}

	public String getE2_5() {
		return e2_5;
	}

	public void setE2_5(String e2_5) {
		this.e2_5 = e2_5;
	}

	public String getE2_6() {
		return e2_6;
	}

	public void setE2_6(String e2_6) {
		this.e2_6 = e2_6;
	}

	public BigDecimal getE3_1() {
		return e3_1;
	}

	public void setE3_1(BigDecimal e3_1) {
		this.e3_1 = e3_1;
	}

	public BigDecimal getE3_2() {
		return e3_2;
	}

	public void setE3_2(BigDecimal e3_2) {
		this.e3_2 = e3_2;
	}

	public BigDecimal getE3_3() {
		return e3_3;
	}

	public void setE3_3(BigDecimal e3_3) {
		this.e3_3 = e3_3;
	}

	public BigDecimal getE3_4() {
		return e3_4;
	}

	public void setE3_4(BigDecimal e3_4) {
		this.e3_4 = e3_4;
	}

	public BigDecimal getE3_5() {
		return e3_5;
	}

	public void setE3_5(BigDecimal e3_5) {
		this.e3_5 = e3_5;
	}

	public BigDecimal getE3_6() {
		return e3_6;
	}

	public void setE3_6(BigDecimal e3_6) {
		this.e3_6 = e3_6;
	}

	public BigDecimal getE3_7() {
		return e3_7;
	}

	public void setE3_7(BigDecimal e3_7) {
		this.e3_7 = e3_7;
	}

	public BigDecimal getE3_8() {
		return e3_8;
	}

	public void setE3_8(BigDecimal e3_8) {
		this.e3_8 = e3_8;
	}

	public BigDecimal getE3_9() {
		return e3_9;
	}

	public void setE3_9(BigDecimal e3_9) {
		this.e3_9 = e3_9;
	}

	public String getE3_10() {
		return e3_10;
	}

	public void setE3_10(String e3_10) {
		this.e3_10 = e3_10;
	}

	public String getE3_11() {
		return e3_11;
	}

	public void setE3_11(String e3_11) {
		this.e3_11 = e3_11;
	}

	public String getOTP_UUID() {
		return OTP_UUID;
	}

	public void setOTP_UUID(String OTP_UUID) {
		this.OTP_UUID = OTP_UUID;
	}

	public String getOTP_IPAddr() {
		return OTP_IPAddr;
	}

	public void setOTP_IPAddr(String OTP_IPAddr) {
		this.OTP_IPAddr = OTP_IPAddr;
	}

	public Timestamp getOTP_Time() {
		return OTP_Time;
	}

	public void setOTP_Time(Timestamp OTP_Time) {
		this.OTP_Time = OTP_Time;
	}

	public String getOTP_STATUS() {
		return OTP_STATUS;
	}

	public void setOTP_STATUS(String OTP_STATUS) {
		this.OTP_STATUS = OTP_STATUS;
	}

	public String getOTP_ERRORCODE() {
		return OTP_ERRORCODE;
	}

	public void setOTP_ERRORCODE(String OTP_ERRORCODE) {
		this.OTP_ERRORCODE = OTP_ERRORCODE;
	}

	public String getOTP_DATARESULT() {
		return OTP_DATARESULT;
	}

	public void setOTP_DATARESULT(String OTP_DATARESULT) {
		this.OTP_DATARESULT = OTP_DATARESULT;
	}

	public String getOTP_ACCTRESULT() {
		return OTP_ACCTRESULT;
	}

	public void setOTP_ACCTRESULT(String OTP_ACCTRESULT) {
		this.OTP_ACCTRESULT = OTP_ACCTRESULT;
	}

	public String getOTP_TYPERESULT() {
		return OTP_TYPERESULT;
	}

	public void setOTP_TYPERESULT(String OTP_TYPERESULT) {
		this.OTP_TYPERESULT = OTP_TYPERESULT;
	}
}
