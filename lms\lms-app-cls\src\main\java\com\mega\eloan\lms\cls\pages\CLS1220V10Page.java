package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.IncomDocStatusEnum;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.auth.AuthService;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 房貸進件
 * </pre>
 * 
 * @since 2020/6/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/6/23,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1220v10")
public class CLS1220V10Page extends AbstractEloanInnerView {

	@Autowired
	AuthService au;
	
	@Autowired
	CLS1220Service cls1220Service;
	
	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(IncomDocStatusEnum.待派案);
		//========================
		Set<String> eloanRoles = MegaSSOSecurityContext.getEloanRoles();
		String pgmDept = MegaSSOSecurityContext.getPGMDept();
		int transactionCode = Util.parseInt(params
				.getString(EloanConstants.TRANSACTION_CODE));
		
//		boolean _Query = au.auth(pgmDept, eloanRoles, transactionCode,
//				AuthType.Query);
		boolean _Accept = au.auth(pgmDept, eloanRoles, transactionCode,
				AuthType.Accept);
		boolean _Modify = au.auth(pgmDept, eloanRoles, transactionCode,
				AuthType.Modify);
		
		List<EloanPageFragment> list = new ArrayList<EloanPageFragment>();
		list.add(LmsButtonEnum.Filter);
		list.add(LmsButtonEnum.Modify); // 借用此btn去做「查詢客戶申貸記錄」
		list.add(LmsButtonEnum.Print); // 借用此btn去做「引介案件進度查詢」
		list.add(LmsButtonEnum.View);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if(_Accept||_Modify){
			list.add(LmsButtonEnum.Add);
		}
		
		String ssoUnitNo = user.getSsoUnitNo();
		if(_Accept||_Modify){
			if(Util.equals("900", ssoUnitNo)||Util.equals("943", ssoUnitNo)){
				list.add(LmsButtonEnum.CaseToChange);
				if(user.getRoles().containsKey("EL02")){ //使用者單位為消金業務處且具主管權限，才可維護派案分行
					list.add(LmsButtonEnum.AutoAssignMaintenance);
				}
			}else{
				if(user.getRoles().containsKey("EL02")){ //一般分行需要主管權限才可移轉分行
					list.add(LmsButtonEnum.CaseToChange);
				}
			}
		}
		
		if (user.getRoles().containsKey("EL02")) { // 主管權限才能改簽案人員
			list.add(LmsButtonEnum.CaseToChangeSignEmp);
		}
		
		// J-111-0602 歡喜信貸eLoan流程精進修改-徵審分案流程變動
		// 主管權限才能改一鍵分案
		// 是943主管到各分行裡去操作此功能，所以只有943主管看得到這按鈕
		if(user.getRoles().containsKey("EL02")){
			if (Util.equals(user.getSsoUnitNo(), "900")
					|| Util.equals(user.getSsoUnitNo(), "943")) {
				
				// 僅於自動派案分行清單中列表分行有此權限，並限定甲級主管可使用
				boolean isShowOneButtonAssign = cls1220Service.showOneButtonAssignCase(user.getUnitNo());
				if(isShowOneButtonAssign){
					list.add(LmsButtonEnum.OneButtonAssignCase);
				}
			}
		}
		
		// 加上Button
		addToButtonPanel(model, list);
		// build i18n
		renderJsI18N(CLS1220V10Page.class);

		// UPGRADE: 待確認JavaScript有無正確讀取
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1220V10Page');");
	}

}
