package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L192S02ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L192S02A;

@Repository
public class L192S02ADaoImpl extends LMSJpaDao<L192S02A, String> implements
		L192S02ADao {

	@Override
	public List<L192S02A> findByL192M01A(L192M01A meta) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId",
				meta.getMainId());
		return find(search);
	}

	@Override
	public int deleteByMeata(L192M01A meta) {
		Query query = entityManager.createNamedQuery("l192s02a.deleteByMainId");
		query.setParameter("MAINID", meta.getMainId());
		return query.executeUpdate();
	}

}
