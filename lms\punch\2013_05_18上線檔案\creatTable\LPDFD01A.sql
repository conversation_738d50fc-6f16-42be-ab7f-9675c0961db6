---------------------------------------------------------
-- LMS.LPDFD01A 授信 PDF 舊案刪除記錄檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.LPDFD01A;
CREATE TABLE LMS.LPDFD01A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	RP<PERSON><PERSON><PERSON>       CHAR(32)     ,
	RPTSEQ        DECIMAL(3,0) ,
	RP<PERSON><PERSON><PERSON>       DECIMAL(3,0) ,
	RP<PERSON><PERSON><PERSON>       VARCHAR(20)  ,
	RP<PERSON>NAME       VARCHAR(120) ,
	C<PERSON>RNO        VARCHAR(12)  ,
	RP<PERSON>ILE       VARCHAR(128) ,
	RANDOMCODE    CHAR(32)     ,
	DELETETIME    TIMESTAMP    ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_LPDFD01A PRIMARY KEY(OID)
) IN EL_DATA_4KTS
  INDEX IN EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XLPDFD01A01;
CREATE INDEX LMS.XLPDFD01A01 ON LMS.LPDFD01A   (MAINID, RPTUNID, RPTSEQ, RPTTYPE, RPTFILE);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.LPDFD01A IS '授信 PDF 舊案刪除記錄檔';
COMMENT ON LMS.LPDFD01A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	RPTUNID       IS '報表文件ID', 
	RPTSEQ        IS '顯示順序', 
	RPTDISP       IS '顯示註記', 
	RPTTYPE       IS '報表編號', 
	RPTNAME       IS '報表名稱(描述)', 
	CNTRNO        IS '額度序號', 
	RPTFILE       IS '報表檔檔案位置', 
	RANDOMCODE    IS '報表亂碼', 
	DELETETIME    IS '刪除時間', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
