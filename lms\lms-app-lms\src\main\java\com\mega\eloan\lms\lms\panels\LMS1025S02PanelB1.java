package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 基本資料
 * </pre>
 * 
 * @since 2015/6/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/6/1,EL08034,new
 *          </ul>
 */
public class LMS1025S02PanelB1 extends Panel {
	private static final long serialVersionUID = 1L;

	private boolean isBasicData;

	public LMS1025S02PanelB1(String id) {
		super(id);
	}

	public LMS1025S02PanelB1(String id, boolean updatePanelName, boolean isBasicData) {
		super(id, updatePanelName);
		this.isBasicData = isBasicData;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		model.addAttribute("hs_baseData_Y", isBasicData);
		model.addAttribute("hs_baseData_N", !isBasicData);
	}
}
