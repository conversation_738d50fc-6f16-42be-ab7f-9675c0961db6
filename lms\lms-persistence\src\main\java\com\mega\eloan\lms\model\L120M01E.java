/* 
 * L120M01E.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 相關文件資料檔 **/
@NamedEntityGraph(name = "L120M01E-entity-graph", attributeNodes = { @NamedAttributeNode("l120m01a") , @NamedAttributeNode("c140m04a")})
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120M01E", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","docType","docURL","docOid"}))
public class L120M01E extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件 L120M01E．關聯檔
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumn(name = "DOCOID", referencedColumnName = "OID", nullable = false, insertable = false, updatable = false)
	private C140M04A c140m04a;

	public C140M04A getC140m04a() {
		return c140m04a;
	}

	public void setC140m04a(C140M04A c140m04a) {
		this.c140m04a = c140m04a;
	}

	/**
	 * JOIN條件 L120M01A 關聯
	 * 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false)
	private L120M01A l120m01a;

	public L120M01A getL120m01a() {
		return l120m01a;
	}

	public void setL120m01a(L120M01A l120m01a) {
		this.l120m01a = l120m01a;
	}	
	
	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 相關文件類別<p/>
	 * 1.徵信報告書<br/>
	 *  2.資信簡表<br/>
	 *  3.擔保品估價報告書<br/>
	 *  4.新增連保人<br/>
	 *  5.最新集團企業授信往來情形<br/>
	 *  6.個金簽報書<br/>
	 *  7.土建融案檢視清單<br/>	
	 *  8.同一關係人<br/>
	 *  9.小放會會議記錄<br/>
	 *  A.團貸母戶額度明細表
	 */
	@Column(name="DOCTYPE", length=1, columnDefinition="CHAR(01)")
	private String docType;

	/** 
	 * 關聯URL<p/>
	 * 1,2,3項提供調閱功能<br/>
	 *  此處為記錄交易名稱
	 */
	@Column(name="DOCURL", length=40, columnDefinition="VARCHAR(40)")
	private String docURL;

	/** 
	 * 關聯oid<p/>
	 * 100/08/24新增<br/>
	 *  1,2,3項提供調閱功能<br/>
	 *  此處為記錄文件oid開啟畫面
	 */
	@Column(name="DOCOID", length=32, columnDefinition="CHAR(32)")
	private String docOid;

	/** 
	 * 關聯custId<p/>
	 * 101/03/29新增
	 */
	@Column(name="DOCCUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String docCustId;

	/** 
	 * 關聯dupNo<p/>
	 * 101/03/29新增
	 */
	@Column(name="DOCDUPNO", length=1, columnDefinition="CHAR(1)")
	private String docDupNo;
	
	/** 
	 * 資料日期<p/>
	 * 額外記錄資訊<br/>
	 *  eg.徵信報告日期、擔保品鑑價日期、黑名單查詢資料日期…
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="DOCDATE", columnDefinition="DATE")
	private Date docDate;

	/** 連結內容說明 **/
	@Column(name="DOCDSCR", length=240, columnDefinition="VARCHAR(240)")
	private String docDscr;

	/** 
	 * 輸入資料檢誤完成(Y/N)<p/>
	 * 100/12/05新增<br/>
	 *  Y/N<br/>
	 *  預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Column(name="CHKYN", length=1, columnDefinition="CHAR(1)")
	private String chkYN;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得相關文件類別<p/>
	 * 徵信報告書<br/>
	 *  資信簡表<br/>
	 *  擔保品估價報告書<br/>
	 *  新增連保人？<br/>
	 *  黑名單查詢
	 */
	public String getDocType() {
		return this.docType;
	}
	/**
	 *  設定相關文件類別<p/>
	 *  1.徵信報告書<br/>
	 *  2.資信簡表<br/>
	 *  3.擔保品估價報告書<br/>
	 *  4.新增連保人<br/>
	 *  5.最新集團企業授信往來情形<br/>
	 *  6.個金簽報書<br/>
	 *  7.土建融案檢視清單<br/>	
	 *  8.同一關係人<br/>
	 *  9.小放會會議記錄<br/>
	 *  A.團貸母戶額度明細表
	 **/
	public void setDocType(String value) {
		this.docType = value;
	}

	/** 
	 * 取得關聯URL<p/>
	 * 1,2,3項提供調閱功能<br/>
	 *  此處為記錄交易名稱
	 */
	public String getDocURL() {
		return this.docURL;
	}
	/**
	 *  設定關聯URL<p/>
	 *  1,2,3項提供調閱功能<br/>
	 *  此處為記錄交易名稱
	 **/
	public void setDocURL(String value) {
		this.docURL = value;
	}

	/** 
	 * 取得關聯oid<p/>
	 * 100/08/24新增<br/>
	 *  1,2,3項提供調閱功能<br/>
	 *  此處為記錄文件oid開啟畫面
	 */
	public String getDocOid() {
		return this.docOid;
	}
	/**
	 *  設定關聯oid<p/>
	 *  100/08/24新增<br/>
	 *  1,2,3項提供調閱功能<br/>
	 *  此處為記錄文件oid開啟畫面
	 **/
	public void setDocOid(String value) {
		this.docOid = value;
	}

	/** 
	 * 取得關聯custId<p/>
	 * 101/03/29新增
	 */
	public String getDocCustId() {
		return this.docCustId;
	}
	/**
	 *  設定關聯custId<p/>
	 *  101/03/29新增
	 **/
	public void setDocCustId(String value) {
		this.docCustId = value;
	}

	/** 
	 * 取得關聯dupNo<p/>
	 * 101/03/29新增
	 */
	public String getDocDupNo() {
		return this.docDupNo;
	}
	/**
	 *  設定關聯dupNo<p/>
	 *  101/03/29新增
	 **/
	public void setDocDupNo(String value) {
		this.docDupNo = value;
	}
	
	/** 
	 * 取得資料日期<p/>
	 * 額外記錄資訊<br/>
	 *  eg.徵信報告日期、擔保品鑑價日期、黑名單查詢資料日期…
	 */
	public Date getDocDate() {
		return this.docDate;
	}
	/**
	 *  設定資料日期<p/>
	 *  額外記錄資訊<br/>
	 *  eg.徵信報告日期、擔保品鑑價日期、黑名單查詢資料日期…
	 **/
	public void setDocDate(Date value) {
		this.docDate = value;
	}

	/** 取得連結內容說明 **/
	public String getDocDscr() {
		return this.docDscr;
	}
	/** 設定連結內容說明 **/
	public void setDocDscr(String value) {
		this.docDscr = value;
	}

	/** 
	 * 取得輸入資料檢誤完成(Y/N)<p/>
	 * 100/12/05新增<br/>
	 *  Y/N<br/>
	 *  預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	public String getChkYN() {
		return this.chkYN;
	}
	/**
	 *  設定輸入資料檢誤完成(Y/N)<p/>
	 *  100/12/05新增<br/>
	 *  Y/N<br/>
	 *  預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
