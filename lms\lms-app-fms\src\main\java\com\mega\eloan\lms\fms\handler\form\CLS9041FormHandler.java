package com.mega.eloan.lms.fms.handler.form;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Scanner;

import javax.annotation.Resource;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.aspectj.util.FileUtil;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.fms.service.CLS9041M01Service;
import com.mega.eloan.lms.model.C004M01A;
import com.mega.eloan.lms.model.C004S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 政策性留學生貸款送保彙報
 * </pre>
 * 
 * @since 2012/11/01
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/05,Vector Lo,new
 *          </ul>
 */
@Scope("request")
@Controller("cls9041formhandler")
public class CLS9041FormHandler extends AbstractFormHandler {
	private static final Logger logger = LoggerFactory.getLogger(CLS9041FormHandler.class);
	
	final String DATE_SPLIT = "-";

	@Resource
	CLS9041M01Service service;

	@Resource
	UserInfoService userinfoservice;

	@Resource
	DocFileService docFileService;

	/**
	 * 新增報送資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 * @throws IOException
	 */
	public IResult addC004M01A(PageParameters params)
			throws CapException, IOException {
		// 依本日日期抓資料的日期範圍
		Calendar now = Calendar.getInstance();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		int year = now.get(Calendar.YEAR), month = now.get(Calendar.MONTH) + 1;// 起始為0
		String beginDate, endDate, rptName = null;
		if (month <= 4) {// 1~4月->找去年5/1到10/31
			year--;
			beginDate = year + "-05-01";
			endDate = year + "-10-31";
		} else if (month >= 11) {// 11~12月->找今年5/1到10/31
			beginDate = year + "-05-01";
			endDate = year + "-10-31";
		} else {// 5~10月->找去年11/1到今年4/30
			endDate = year + "-04-30";
			beginDate = (year - 1) + "-11-01";
		}

		// 開始加
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = IDGenerator.getUUID();
		String rptType = params.getString("rptType");
		
		if(Util.equals("S3", rptType)){
			//S3為按月報送，不應該像S1, S2抓半年
			String dataYearB = Util.trim(params.getString("dataYearB"));
			String dataMmB = Util.getRightStr("00"+Util.trim(params.getString("dataMmB")), 2);
			String dataYearE = Util.trim(params.getString("dataYearE"));
			String dataMmE = Util.getRightStr("00"+Util.trim(params.getString("dataMmE")), 2);
			
			String dataYearMmB = dataYearB+"-"+dataMmB;
			String dataYearMmE = dataYearE+"-"+dataMmE;
			
			check_date(dataYearMmB, UtilConstants.DateFormat.YYYY_MM);
			check_date(dataYearMmE, UtilConstants.DateFormat.YYYY_MM);			
			//====
			beginDate = dataYearMmB+"-01";
			endDate = CrsUtil.getDataEndDate(dataYearMmE);
			if(LMSUtil.cmp_yyyyMM(CapDate.parseDate(beginDate), ">", CapDate.parseDate(endDate))){
				throw new CapMessageException(beginDate+" > "+endDate, getClass());
			}
		}
		
		C004M01A c004m01a = new C004M01A();
		// List<Map<String, Object>> data = service.getMisData(beginDate,
		// endDate, rptType);
		List<Map<String, Object>> data = null;
		switch (rptType.charAt(1)) {
		case '1':
			rptName = "政策性留學生貸款S1報表";
			data = service.getMisDataNewS1(beginDate, endDate, rptType);
			break;
		case '2':
			rptName = "政策性留學生貸款S2報表";
			data = service.getMisData(beginDate, endDate, rptType);
			break;
		case '3':
			rptName = "代位清償申請彙報通知";
			data = service.getMisData(beginDate, endDate, rptType);
		}

		// 取得txt資料

		String fileName = TWNDate.toFullAD(new Date()).replace(':', '-')
				+ user.getDutyAgentNo() + rptType + ".txt";
		File tempFile = new File(fileName);
		BufferedWriter bufWriter = new BufferedWriter(new OutputStreamWriter(
				new FileOutputStream(tempFile, true), "Big5"));
		try{
			for (int i = 0; i < data.size(); i++) {
				switch (rptType.charAt(1)) {
				case '1':
					bufWriter.write(S1TxtSequencs(data.get(i)));
					break;
				case '2':
					bufWriter.write(S2TxtSequencs(data.get(i)));
					break;
				case '3':
					bufWriter.write(S3TxtSequencs(data.get(i)));
				}
				//此寫法在 AIX 上會新增 0A。但在 windows 開啟會不換行
				//bufWriter.newLine();
				bufWriter.write(CapConstants.LINE_BREAK);
			}
			bufWriter.close();
			// start upload

			DocFile file = new DocFile();
			file.setMainId(mainId);
			file.setData(FileUtil.readAsByteArray(tempFile));
			file.setCrYear("" + year);
			file.setFieldId("fms");
			file.setTotPages(1);
			file.setSrcFileName(tempFile.getName());
			file.setUploadTime(CapDate.getCurrentTimestamp());
			file.setBranchId(user.getOvUnitNo());
			file.setContentType("text/plain");
			file.setSysId("LMS");

			docFileService.save(file);
			
			c004m01a.setRptType(rptType);

			c004m01a.setMainId(mainId);
			c004m01a.setUnid(mainId);// 判斷q1從哪個s1產生的依據

			c004m01a.setRptName(rptName);
			c004m01a.setBgnDate(TWNDate.valueOf(beginDate).toDate());
			c004m01a.setEndDate(TWNDate.valueOf(endDate).toDate());
			service.save(c004m01a);
			//=======
			FileUtils.deleteQuietly(tempFile);
		}catch(Exception e){
			FileUtils.deleteQuietly(tempFile);
			//=======
			logger.error(StrUtils.getStackTrace(e));
			throw new CapMessageException(e, getClass());			
		}
		return result;
	}

	private void check_date(String yyyy_MM, String pattern)
	throws CapMessageException{
		if(!CapDate.validDate(yyyy_MM, pattern)){
			throw new CapMessageException(yyyy_MM+"日期格式錯誤", getClass());
		}
	}
	
	/**
	 * 依S1格式輸出字串至txt
	 */
	private String S1TxtSequencs(Map<String, Object> data) {
		String tString = "";
		String tBank = "017";
		String tBranch = "";
		String tBkCode = "";
		String tType = "";
		String tCname = "";
		String tAddr = "";
		String tSchlnm = "";
		String tLngenm1 = "";
		String tLngenm2 = "";
		String tAmt = "";

		StringBuffer result = new StringBuffer();
		if (!Util.isEmpty(data)) {
			tBranch = Util.trim(data.get("LNF192_BR_NO"));
			tBkCode = String.valueOf(this.branchCode(tBranch));

			if (!"".equals(Util.trim(data.get("CASE_1")))) {
				tType = Util.trim(data.get("CASE_1"));
			} else if (!"".equals(Util.trim(data.get("CASE_2")))) {
				tType = Util.trim(data.get("CASE_2"));
			} else if (!"".equals(Util.trim(data.get("CASE_3")))) {
				tType = Util.trim(data.get("CASE_3"));
			}

			tCname = Util.trim(data.get("CNAME"));
			tCname = this.fillString(tCname, 12);
			tAddr = Util.trim(data.get("ADDR1"));
			tAddr = this.fillString(tAddr, 80);
			tSchlnm = Util.trim(data.get("SCHLNM"));
			tSchlnm = this.fillString(tSchlnm, 80);
			tLngenm1 = Util.trim(data.get("LNGENM1"));
			tLngenm1 = this.fillString(tLngenm1, 12);
			tLngenm2 = Util.trim(data.get("LNGENM2"));
			tLngenm2 = this.fillString(tLngenm2, 12);
			// 去掉主機的[.00]
			tAmt = NumConverter.addComma(((BigDecimal) data
					.get("LNF192_LOAN_AMT")).stripTrailingZeros(), "0");

			tString = "";
			tString = Util.addSpaceWithValue(tBank, 3)
					+ Util.addSpaceWithValue(tBranch, 3)
					+ Util.addSpaceWithValue(tBkCode, 1)
					+ Util.addSpaceWithValue(tType, 1)  //通知事項{1:新送保, 9:更正資料}
					+ tCname
					+ Util.addSpaceWithValue(Util.trim(data.get("CUSTID")), 10)
					+ Util.addSpaceWithValue(
							TWNDate.valueOf(
									Util.trim(data.get("LNF192_BEG_DATE")))
									.toTW(), 7)
					+ Util.addSpaceWithValue(
							TWNDate.valueOf(
									Util.trim(data.get("LNF192_END_DATE")))
									.toTW(), 7)
					+ Util.addZeroWithValue(tAmt, 7)
					+ Util.addSpaceWithValue(Util.trim(data.get("ICMTYPE")), 1)
					+ Util.addSpaceWithValue(Util.trim(data.get("ZIP")), 3)
					+ tAddr
					+ Util.addSpaceWithValue(Util.trim(data.get("GRATSCHL")), 6)
					+ Util.addSpaceWithValue(Util.trim(data.get("GRATDEP"))
							.substring(0, 2), 2)
					+ Util.addZeroWithValue(Util.trim(data.get("SCHLLNO")), 2)
					+ tSchlnm
					+ Util.addSpaceWithValue(Util.trim(data.get("DEPTNO"))
							.substring(0, 2), 2)
					+ Util.addSpaceWithValue(Util.trim(data.get("EDUCLS")), 1)
					+ tLngenm1
					+ Util.addSpaceWithValue(Util.trim(data.get("LNGEID1")), 10)
					+ Util.addSpaceWithValue(Util.trim(data.get("STUREL2")), 1)
					+ tLngenm2
					+ Util.addSpaceWithValue(Util.trim(data.get("LNGEID2")), 10)
					+ Util.addSpaceWithValue(Util.trim(data.get("STUREL3")), 1);

			if (!"".equals(tString)) {
				result.append(this.fillString(Util.trim(tString), 297));
			}

			// result.append(Util.addSpaceWithValue(tBank, 3))
			// .append(Util.addSpaceWithValue(tBranch, 3))
			// .append(Util.addSpaceWithValue(tBkCode, 1))
			// .append(Util.addSpaceWithValue(tType, 1))
			// // .append(Util.addSpaceWithValue(Util.trim(data.get("RSFLD1")),
			// 10))
			// .append(tCname)
			// .append(Util.addSpaceWithValue(Util.trim(data.get("CUSTID")),
			// 10))
			// .append(Util.addSpaceWithValue(TWNDate.valueOf(Util.trim(data.get("LNF192_BEG_DATE"))).toTW(),
			// 7))
			// .append(Util.addSpaceWithValue(TWNDate.valueOf(Util.trim(data.get("LNF192_END_DATE"))).toTW(),
			// 7))
			//
			// .append(Util.addZeroWithValue(tAmt, 7))
			// .append(Util.addSpaceWithValue(Util.trim(data.get("ICMTYPE")),
			// 1))
			// .append(Util.addSpaceWithValue(Util.trim(data.get("ZIP")), 3))
			// .append(tAddr)
			// .append(Util.addSpaceWithValue(Util.trim(data.get("GRATSCHL")),
			// 6))
			// .append(Util.addSpaceWithValue(Util.trim(data.get("GRATDEP")).substring(0,
			// 2), 2))
			// .append(Util.addSpaceWithValue(Util.trim(data.get("SCHLLNO")),
			// 2))
			// .append(tSchlnm)
			// .append(Util.addSpaceWithValue(Util.trim(data.get("DEPTNO")).substring(0,
			// 2), 2))
			// .append(Util.addSpaceWithValue(Util.trim(data.get("EDUCLS")), 1))
			// .append(tLngenm1)
			// .append(Util.addSpaceWithValue(Util.trim(data.get("LNGEID1")),
			// 10))
			// .append(Util.addSpaceWithValue(Util.trim(data.get("STUREL2")),
			// 1))
			// .append(tLngenm2)
			// .append(Util.addSpaceWithValue(Util.trim(data.get("LNGEID2")),
			// 10));
		}
		return result.toString();
	}

	private int branchCode(String branch) {
		int value = 0;
		if (NumberUtils.isNumber(branch) && branch.length() == 3) {
			int first = Integer.parseInt(branch.substring(0, 1));
			int second = Integer.parseInt(branch.substring(1, 2));
			int third = Integer.parseInt(branch.substring(2, 3));
			value = first * 7 + second * 3 + third * 1;
			value = Integer
					.parseInt(StringUtils.right(String.valueOf(value), 1));
		}
		return value;
	}

	private String fillString(String txt, int len) {
		try {
			int length = txt.getBytes("BIG5").length;
			if (length > len) {
				txt = new String(txt.getBytes("BIG5"), 0, len, "BIG5");
			} else {
				txt = new String(
						(txt + StringUtils.repeat(" ", len)).getBytes("BIG5"),
						0, len, "BIG5");
			}
			return txt;
		} catch (UnsupportedEncodingException e) {
			logger.error(e.toString());
		}
		return StringUtils.repeat(" ", len);
	}

	private String getfillString(String txt, int sByte, int length) {

		try {
			txt = new String(txt.getBytes("BIG5"), sByte, length, "BIG5");
			return txt;
		} catch (UnsupportedEncodingException e) {

			logger.error(e.toString());

		}

		return "";

	}

	/**
	 * 依S2格式輸出字串至txt
	 * 學生身分證號(10)+貸款起日(7)+貸款餘額(7)+轉列逾放日(7)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 * @throws IOException
	 */
	private String S2TxtSequencs(Map<String, Object> data) {		
		StringBuffer result = new StringBuffer();
		
		String tAmt = "";
		
		if (!Util.isEmpty(data)) {
			tAmt = NumConverter.addComma(((BigDecimal) data.get("LOAN_BAL")).stripTrailingZeros(), "0");
			/*
			LNF192_OV_DATE		轉列逾放日（轉催收日
			LNF192_OV_DATE_F	轉列催收日－報送用
			LNF192_OV_DATE_S	轉列催收日－E-LOAN
			LNF192_LOANBAL_FIX	放款餘額－報送用	
			LNF192_UPDATE_FIX	更新日期－報送用
			LNF192_COP_DATE		應該是 L515 畫面上的「代位清償資料建檔日期」

			在紙上「手寫」的說明, 因半年報一次, 若這半年轉逾期又轉正常, 則以正常報出
			在紙上「印出」的說明, 正常戶免填, 原為逾期戶, 如符合規定轉為正常戶者, 則填 9999999


LOAN.TEST.SOURCE(LNBX001)
程式功能    : 產生留學生貸款申請補貼利息明細表     
   		   ( 每年 4 月底及 10 月底產生前半年資料 )
*------------------*                                       
 7400-UPDATE-LNF192.                                       
*------------------*                                       
     MOVE  '7400'                TO      WK-RTN.           
     MOVE  'LNF192'              TO      WK-TAB.           
     MOVE  'UPDATE'              TO      WK-ACT.           
                                                           
     EXEC  SQL                                             
           UPDATE  LN.LNF192                               
              SET  LNF192_OV_DATE_F   =  LNF192_OV_DATE,   
                   LNF192_LOANBAL_FIX =  LNF192_LOAN_BAL,  
                   LNF192_UPDATE_FIX  =  LNF192_UPDATE_DATE
     END-EXEC.                                             


			資料的變化
			(1)應該是 a-loan 會寫入 LNF192_OV_DATE
			(2)在4月底,10月底的批次, 將 LNF192_OV_DATE 寫到 LNF192_OV_F (非每個月)
            (3)依 LNF192_OV_S 及 LNF192_OV_F 來決定, 在 S2的「轉列逾放日」應如何呈現？
            (4)把S2上傳的TXT(可能 user 會更改內容), 寫到 LNF192_OV_S, 供下次比對使用
			*/
			String LNF192_OV_DATE_S = Util.trim(data.get("LNF192_OV_DATE_S"));
			String LNF192_OV_DATE_F = Util.trim(data.get("LNF192_OV_DATE_F"));
			String def_tOverDate = Util.addSpaceWithValue(TWNDate.valueOf(LNF192_OV_DATE_F).toTW(), 7);
			if (Util.isEmpty(Util.trim(def_tOverDate)) || "0010101".equals(def_tOverDate)){
				def_tOverDate = "0000000";
			}
		
			String tOverDate = def_tOverDate;
			if(true){
				if(Util.equals(LNF192_OV_DATE_S, "0001-01-01") && ( Util.notEquals(LNF192_OV_DATE_F, "0001-01-01") && Util.notEquals(LNF192_OV_DATE_F, "9999-12-31") )){
					//****** 上一次正常，本次不正常，報送 逾放日  ******
					tOverDate = def_tOverDate;
				}else if((Util.notEquals(LNF192_OV_DATE_S, "0001-01-01") && Util.notEquals(LNF192_OV_DATE_S, "9999-12-31")) &&  Util.equals(LNF192_OV_DATE_F, "0001-01-01") ){
					//****** 上一次有逾放日，本次正常，報送「轉正的代碼」  ******
					tOverDate = "9999999";
				}else if(Util.equals(LNF192_OV_DATE_S, "0001-01-01") &&  Util.equals(LNF192_OV_DATE_F, "0001-01-01") ){
					//****** 都正常 ******
					tOverDate = "0000000";
				}else if((Util.notEquals(LNF192_OV_DATE_S, "0001-01-01") && Util.notEquals(LNF192_OV_DATE_S, "9999-12-31")) &&  Util.equals(LNF192_OV_DATE_F, LNF192_OV_DATE_S) ){
					//****** 上一次有逾放日，本次也有逾放日，報送 逾放日 ******
					
					//If LNF192_LOAN_AMT =LNF192_LOANBAL_FIX Then
					//	'不報送   SQL 已經擋掉，所以本條件不會執行到   只是註記而已
					//	tOverDate = "0000000"		
					//Else
						tOverDate =  def_tOverDate; 
					//End If
				}else if(Util.equals(LNF192_OV_DATE_S, "9999-12-31") &&  Util.equals(LNF192_OV_DATE_F, "0001-01-01") ){
					tOverDate = "0000000";
				}else if(Util.equals(LNF192_OV_DATE_S, "9999-12-31") &&  Util.notEquals(LNF192_OV_DATE_F, "0001-01-01") ){
					//****** 上一次轉正，本次又不正常，報送 逾放日  ******
					tOverDate = def_tOverDate;
				}
			}
			
			String tString = Util.addSpaceWithValue(Util.trim(data.get("STU_ID")), 10) + 
				Util.addSpaceWithValue(TWNDate.valueOf(Util.trim(data.get("BEG_DATE"))).toTW(), 7) + //貸款起日
				Util.addZeroWithValue(tAmt, 7) + //貸款餘額
				tOverDate //轉列逾放日【如為正常戶免填。原為逾期戶，如轉為正常戶者，則填9999999】
				;
			
			if (true) {
				result.append(this.fillString(Util.trim(tString), 31));
			}

//			result.append(
//					Util.addSpaceWithValue(Util.trim(data.get("STU_ID")), 10))
//					.append(Util.addSpaceWithValue(
//							Util.trim(data.get("BEG_DATE")).replaceAll(
//									DATE_SPLIT, ""), 8))
//					.append(Util.addZeroWithValue(data.get("LOAN_BAL")
//							.toString().replace(".", ""), 7)).append("0000000");
		}
		return result.toString();
	}

	/**
	 * 依S3格式輸出字串至txt
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 * @throws IOException
	 */
	private String S3TxtSequencs(Map<String, Object> data) {
		StringBuffer result = new StringBuffer();
		//XXX 在做完L515 代位清償維護交易後，才有報 S3 的來源
		if (!Util.isEmpty(data)) {
			
			String cop_bal = NumConverter.addComma(((BigDecimal) data.get("LNF192_COP_BAL")).stripTrailingZeros(), "0");
			String cop_int = NumConverter.addComma(((BigDecimal) data.get("LNF192_COP_INT")).stripTrailingZeros(), "0");
			String cop_ovint = NumConverter.addComma(((BigDecimal) data.get("LNF192_COP_OVINT")).stripTrailingZeros(), "0");
			String cop_fee = NumConverter.addComma(((BigDecimal) data.get("LNF192_COP_FEE")).stripTrailingZeros(), "0");
			
			result.append(Util.addSpaceWithValue(Util.trim(data.get("LNF192_STUDENT_ID")), 10))
					.append(Util.addSpaceWithValue(TWNDate.valueOf(Util.trim(data.get("LNF192_BEG_DATE"))).toTW(), 7))							
					.append(Util.addZeroWithValue(cop_bal, 7))
					.append(Util.addZeroWithValue(cop_int, 6))
					.append(Util.addZeroWithValue(cop_ovint, 6))
					.append(Util.addZeroWithValue(cop_fee, 6))
					.append(Util.addSpaceWithValue(TWNDate.valueOf(Util.trim(data.get("LNF192_RTINT_DATE"))).toTW(), 7))
					.append(Util.addSpaceWithValue(TWNDate.valueOf(Util.trim(data.get("LNF192_STA_DATE"))).toTW(), 7))
					.append(Util.addSpaceWithValue(TWNDate.valueOf(Util.trim(data.get("LNF192_DUE_DATE"))).toTW(), 7))
					.append(Util.addSpaceWithValue(TWNDate.valueOf(Util.trim(data.get("LNF192_DP_DATE"))).toTW(), 7))
					//參考 LOAN.TEST.SQL(LNF192L)
					//法院收件日=LNF192_REC_DATE
					.append(Util.addSpaceWithValue(TWNDate.valueOf(Util.trim(data.get("LNF192_REC_DATE"))).toTW(), 7))
					.append(Util.addSpaceWithValue(Util.trim(data.get("LNF192_GET_CODE")), 1))
					.append(Util.addSpaceWithValue(Util.trim(data.get("LNF192_FINE_CODE")), 1))
					;
		}
		return result.toString();
	}

	/**
	 * 刪資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult deleteFile(PageParameters params) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.nullToSpace(params.getString("oid"));
		service.deleteFile(oid);
		return result;
	}

	/**
	 * 刪除資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult deleteC004M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params.getString("mainId"));
		if (params.getString("rptType").charAt(0) == 'Q') {
			service.deleteQ(mainId);
		}
		List<DocFile> oldFile = docFileService.findByIDAndPid(mainId, null);
		for (DocFile file : oldFile) {
			file.setDeletedTime(CapDate.getCurrentTimestamp());
		}
		service.delete(service.findModelByOid(C004M01A.class,
				params.getString("oid")));
		return result;
	}

	/**
	 * 產生Q1報表
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult createRpt(PageParameters params)
			throws CapException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String form = Util.trim(params.getString("detailForm"));
		JSONObject S_info = DataParse.toJSON(form);
		String rptType = S_info.getString("rptType");
		List<C004S01A> qList = null;

		// 讀取txt內容
		// 因為有可能改動檔案，所以不用頁面上資料
		DocFile file = docFileService.findByIDAndPid(
				S_info.getString("mainId"), null).get(0);
		File txt = docFileService.getRealFile(file);
		if (txt.exists()) {
			String qMainId = IDGenerator.getUUID();
			// 存至C004S01A
			/*
			TODO
			原本是 Scanner read = new Scanner(txt); 
			修改後 Scanner read = new Scanner(txt, "Big5");
			
			因為會去 parse S1 的 txt 檔, 來產生Q1. 而S1的 txt 檔內,有姓名、地址......等中文字
			在判斷 貸款金額（44~51） 時，用原本的寫法（不指定 file encoding）
			●有的 data row 會是對的，抓到 0500000
			●但有的 data row 會抓到第45~52 5000001
			 */
			Scanner read = new Scanner(txt, "Big5");
			read.useDelimiter("\n");
			try {
				switch (rptType.charAt(1)) {
				case '1':
					S_info.put("rptName", "留學生就學貸款送保彙報通知Q1總表");
					qList = readQ1(read, qMainId);
					break;
				case '2':
					S_info.put("rptName", "留學生就學貸款送保彙報通知Q2總表");
					qList = readQ2(read, qMainId, S_info.getString("bgnDate"),
							S_info.getString("endDate"));
					break;
				case '3':
					S_info.put("rptName", "代位清償申請彙報通知Q3 總表");
					qList = readQ3(read, qMainId, S_info.getString("bgnDate"),
							S_info.getString("endDate"));
				}
			} catch (Exception e) {
				logger.error(StrUtils.getStackTrace(e));
				throw new CapMessageException(RespMsgHelper.getMessage("Error",
						"", "讀取檔案失敗", "請確認檔案內容是否正確"), getClass());
			} finally {
				read.close();
			}
			// 將資料換為Q報表用
			S_info.put("unid", S_info.getString("mainId"));
			S_info.put("rptType", rptType.replace('S', 'Q'));
			S_info.put("mainId", qMainId);
			// 在C004M01A增加Q1記錄
			C004M01A c004m01a = new C004M01A();
			DataParse.toBean(S_info, c004m01a);
			service.save(c004m01a);
			// 儲存Q
			service.saveQ(qList);
			result.set("unid", c004m01a.getUnid());
			result.set("mainId", qMainId);
			result.set("rptType", rptType.replace('S', 'Q'));
			result.set("bgnDate", S_info.getString("bgnDate"));
			result.set("endDate", S_info.getString("endDate"));
			result.set("creator", S_info.getString("creator"));
			result.set("createTime", S_info.getString("createTime"));
			result.set("rptDate", "");
			// 顯示結果交給grid去處理

		} else {
			throw new CapMessageException(RespMsgHelper.getMessage("Error", "",
					"無法找到檔案", "請上傳檔案"), getClass());
		}
		return result;
	}

	private List<C004S01A> readQ1(Scanner reader, String mainId)
			throws CapMessageException {
		int line = 0;
		List<C004S01A> q1 = new ArrayList<C004S01A>();
		while (reader.hasNext()) {
			line++;
			String pivot = reader.next();
			String brno = pivot.substring(3, 6);
			String amtStr = this.getfillString(pivot, 44, 7);
			
			BigDecimal amt = Util.parseBigDecimal(amtStr);			
			amt = Arithmetic.ceil((amt.divide(new BigDecimal(1000))), 0); // 設定抵押金額>>無條件進位

			if (inQ1Format(pivot)) {// 第一筆直接加入
				if (line == 1) {
					C004S01A newNode = new C004S01A();
					newNode.setBrno(brno);
					newNode.setMainId(mainId);
					newNode.setNum(1);
					newNode.setAmt(amt);
					q1.add(newNode);
					continue;
				}
				for (int i = 0; i < q1.size(); i++) {
					C004S01A q1row = q1.get(i);
					if (q1row.getBrno().equals(brno)) {// 已有該分行
						q1row.setNum(q1row.getNum() + 1);// 件數+1
						q1row.setAmt(amt.add(q1row.getAmt()));
						break;// 這些內容都只能被執行一次!
					} else if (i == q1.size() - 1) {// 無該分行=>創點
						C004S01A newNode = new C004S01A();
						newNode.setBrno(brno);
						newNode.setMainId(mainId);
						newNode.setNum(1);
						newNode.setAmt(amt);
						q1.add(newNode);
						break;
					}
				}

			} else {
				throw new CapMessageException(RespMsgHelper.getMessage("Error",
						"", "格式錯誤", "第" + line + "行讀取失敗：" + pivot), getClass());
			}
		}
		return q1;
	}

	private List<C004S01A> readQ2(Scanner reader, String mainId,
			String bgnDate, String endDate) throws CapMessageException {
		Map<String, Object> brnoWithStuid = service.findBrnoAndStuidS2(bgnDate,
				endDate);
		int line = 0;
		List<C004S01A> q1 = new ArrayList<C004S01A>();
		while (reader.hasNext()) {
			line++;
			String pivot = reader.next();
			String stuId = pivot.substring(0, 10);
			String brno = Util.trim(brnoWithStuid.get(stuId));
			BigDecimal amt = Util.parseBigDecimal(this.getfillString(pivot, 17, 7));

			amt = Arithmetic.ceil(amt, 0); // 設定抵押金額>>無條件進位
			
			if (inQ2Format(pivot)) {// 第一筆直接加入
				if (line == 1) {
					C004S01A newNode = new C004S01A();
					newNode.setBrno(brno);
					newNode.setMainId(mainId);
					newNode.setNum(1);
					newNode.setAmt(amt);
					q1.add(newNode);
					continue;
				}
				for (int i = 0; i < q1.size(); i++) {
					C004S01A q1row = q1.get(i);
					if (q1row.getBrno().equals(brno)) {// 已有該分行
						q1row.setNum(q1row.getNum() + 1);// 件數+1
						q1row.setAmt(amt.add(q1row.getAmt()));// AMT相加
						break;// 這些內容都只能被執行一次!
					} else if (i == q1.size() - 1) {// 無該分行=>創點
						C004S01A newNode = new C004S01A();
						newNode.setBrno(brno);
						newNode.setMainId(mainId);
						newNode.setNum(1);
						newNode.setAmt(amt);
						q1.add(newNode);
						break;
					}
				}

			} else {
				throw new CapMessageException(RespMsgHelper.getMessage("Error",
						"", "格式錯誤", "第" + line + "行讀取失敗：" + pivot), getClass());
			}
		}
		return q1;
	}

	private List<C004S01A> readQ3(Scanner reader, String mainId,
			String bgnDate, String endDate)
			throws CapMessageException {
		Map<String, Object> brnoWithStuid = service.findBrnoAndStuidS3(bgnDate,
				endDate);
		int line = 0;
		List<C004S01A> q1 = new ArrayList<C004S01A>();
		while (reader.hasNext()) {
			line++;
			String pivot = reader.next();
			
			String stuId = pivot.substring(0, 10);
			String brno = Util.trim(brnoWithStuid.get(stuId));
			
			BigDecimal valA = Util.parseBigDecimal(this.getfillString(pivot, 17, 7));
			BigDecimal valB = Util.parseBigDecimal(this.getfillString(pivot, 24, 6));
			BigDecimal valC = Util.parseBigDecimal(this.getfillString(pivot, 30, 6));
			BigDecimal valD = Util.parseBigDecimal(this.getfillString(pivot, 36, 6));
			
						
			if (inQ3Format(pivot)) {// 第一筆直接加入
				if (line == 1) {
					C004S01A newNode = new C004S01A();
					newNode.setBrno(brno);
					newNode.setMainId(mainId);
					newNode.setNum(1);
					
					q3_amt_set(newNode, valA, valB, valC, valD);
					q1.add(newNode);
					continue;
				}
				for (int i = 0; i < q1.size(); i++) {
					C004S01A q1row = q1.get(i);
					if (q1row.getBrno().equals(brno)) {// 已有該分行
						q1row.setNum(q1row.getNum() + 1);// 件數+1
						
						q3_amt_add(q1row, valA, valB, valC, valD);
						break;// 這些內容都只能被執行一次!
					} else if (i == q1.size() - 1) {// 無該分行=>創點
						C004S01A newNode = new C004S01A();
						newNode.setBrno(brno);
						newNode.setMainId(mainId);
						newNode.setNum(1);
						
						q3_amt_set(newNode, valA, valB, valC, valD);
						q1.add(newNode);
						break;
					}
				}

			} else {
				throw new CapMessageException(RespMsgHelper.getMessage("Error",
						"", "格式錯誤", "第" + line + "行讀取失敗：" + pivot), getClass());
			}
		}
		return q1;
	}
	
	private void q3_amt_set(C004S01A newNode, BigDecimal valA, BigDecimal valB, BigDecimal valC, BigDecimal valD ){
		newNode.setAmt(valA);
		newNode.setOweInterest(valB);
		newNode.setDueInterest(valC);
		newNode.setLitigate(valD);
	}
	
	private void q3_amt_add(C004S01A newNode, BigDecimal valA, BigDecimal valB, BigDecimal valC, BigDecimal valD ){
		newNode.setAmt(newNode.getAmt().add(valA));
		newNode.setOweInterest(newNode.getOweInterest().add(valB));
		newNode.setDueInterest(newNode.getDueInterest().add(valC));
		newNode.setLitigate(newNode.getLitigate().add(valD));
	}

	private boolean inQ1Format(String pivot) {
		// if (Util.isNotEmpty(pivot) && pivot.length() > 56
		// && Util.isNotEmpty(pivot.substring(0, 3))
		// && Util.isNotEmpty(pivot.substring(39, 56))) {
		return true;
		// }
		// return false;
	}

	private boolean inQ2Format(String pivot) {
//		if (Util.isNotEmpty(pivot) && pivot.length() > 25
//				&& Util.isNotEmpty(pivot.substring(0, 10))
//				&& Util.isNotEmpty(pivot.substring(18, 25))) {
			return true;
//		} else
//			return false;
	}

	private boolean inQ3Format(String pivot) {
		// if (Util.isNotEmpty(pivot) && pivot.length() > 56
		// && Util.isNotEmpty(pivot.substring(0, 3))
		// && Util.isNotEmpty(pivot.substring(39, 56))) {
		return true;
		// }
		// return false;
	}

	/**
	 * 送出Q
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult sendQ(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String unid = Util.trim(params.getString("unid"));
		logger.trace("sendQ_params{unid:"+unid+"}");
		
		List<C004M01A> c004m01a_list = service.findC004M01AByUnid(unid);
		if(c004m01a_list.size()>0){
			String form = Util.trim(params.getString("detailForm"));
			JSONObject S_info = DataParse.toJSON(form);
			DocFile file = docFileService.findByIDAndPid(
					S_info.getString("mainId"), null).get(0);
			
			String rptType = "";
			if(true){
				C004M01A c004m01a = c004m01a_list.get(0);
				if(Util.equals(c004m01a.getRptType(), "S1") || Util.equals(c004m01a.getRptType(), "Q1")){
					rptType = "S1";
				}else if(Util.equals(c004m01a.getRptType(), "S2") || Util.equals(c004m01a.getRptType(), "Q2")){
					rptType = "S2";
				}else{
					throw new CapMessageException(Util.trim(c004m01a.getRptName())+" 無 報送 功能", getClass());
				}
			}
			File txt = docFileService.getRealFile(file);
			logger.trace("{rptType:"+rptType+"}");
			if (txt.exists()) {
				Scanner reader;
				String pivot = "";					
				try {
					reader = new Scanner(txt, "Big5");
					reader.useDelimiter("\n");
					int cnt = 0;
					logMsg("sendQ, prepare["+rptType+"]");
					if(Util.equals("S1", rptType)){
						while (reader.hasNext()) {
							pivot = reader.next();
							String brNo = pivot.substring(3, 6);
							String studentId = this.getfillString(pivot, 20, 10);
							String begDate = this.getfillString(pivot, 30, 7);

							TWNDate begDateTwn = new TWNDate();
							begDateTwn.setTime(begDate);

							String endDate = this.getfillString(pivot, 37, 7);
							
							TWNDate endDateTwn = new TWNDate();
							endDateTwn.setTime(endDate);
							logMsg("sendQ1["+(cnt++)+"]["+brNo+"]["+studentId+"]["+begDateTwn.toAD('-')+"]["+endDateTwn.toAD('-')+"]");
							service.updateLNF192S1(CapDate.getCurrentDate("yyyy-MM-dd"), brNo, studentId,
									begDateTwn.toAD('-'), endDateTwn.toAD('-'));
						}	
					}else if(Util.equals("S2", rptType)){
						while (reader.hasNext()) {
							pivot = reader.next();
							String _STUDID = pivot.substring(0, 10);
							String _CNTFROM = this.getfillString(pivot, 10, 7);
							//String _FACTAMT = this.getfillString(pivot, 17, 7);
							String _OV_DATE = this.getfillString(pivot, 24, 7);
							
							String tCNTFROM = "";
							if(true){
								TWNDate begDateTwn = new TWNDate();
								begDateTwn.setTime(_CNTFROM);
								tCNTFROM = begDateTwn.toAD('-');	
							}
							
							String tOV_DATE = "";
							if(true){
								if(Util.equals(_OV_DATE, "")){
									tOV_DATE = "0001-01-01";
								}else if(Util.equals(_OV_DATE, "0000000")){
									tOV_DATE = "0001-01-01";
								}else if(Util.equals(_OV_DATE, "9999999")){
									tOV_DATE = "9999-12-31";
								}else{
									TWNDate begDateTwn = new TWNDate();
									begDateTwn.setTime(_OV_DATE);
									tOV_DATE = begDateTwn.toAD('-');	
								}
							}
							logMsg("sendQ2["+(cnt++)+"]["+tOV_DATE+"]["+_STUDID+"]["+tCNTFROM+"]");
							service.updateLNF192S2(tOV_DATE, _STUDID, tCNTFROM );
						}	
					}else{
						throw new Exception("["+rptType+"]無 報送 功能");
					}
					logMsg("sendQ finish["+unid+"]["+cnt+"]");
					if(true){
						service.sendRpt(unid, new Date());
					}
				} catch (FileNotFoundException e) {
					logger.error(e.toString());
				} catch (Exception e) {
					logger.error("pivot="+pivot);
					logger.error(StrUtils.getStackTrace(e));
					throw new CapMessageException(e, getClass());
				}				
			}else{
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.報表無資料));
			}			
		}else{
			HashMap<String, String> msg = new HashMap<String, String>();
			msg.put("colName", "");
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.查無資料, msg), getClass());
		}	
		
		return result;
	}

	private void logMsg(String msg){
		logger.info(msg); 
	}
	
	//TODO S4代償後收回款
	/*
	 * 在 S3 代位清償申請後
	 * 若本行向客戶催收，有收到還款的話
	 * 還要再做 S4，把 錢 繳還給信保基金
	 */
}
