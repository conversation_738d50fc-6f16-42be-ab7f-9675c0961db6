package com.mega.eloan.lms.model;

import java.io.Serializable;
import javax.persistence.*;

import com.mega.eloan.common.model.RelativeMeta;

import java.math.BigDecimal;


/**
 * <pre>
 * C140S07B model-授信明細表.
 * </pre>
 * 
 * @since 2011/10/04
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/04,Tim<PERSON>hiang,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "C140S07B-entity-graph", attributeNodes = { 
		@NamedAttributeNode("c140m01a"),
		@NamedAttributeNode("c140m04b") })
@Entity
@Table(name="C140S07B", uniqueConstraints = @UniqueConstraint(columnNames ={"oid"}))
public class C140S07B extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 幣別 */
	@Column(length=3)
	private String curr;
	/** 單位名稱 */
	@Column(name="LND1_1", length=48)
	private String lnd1_1;
	/** 借款餘額 */
	@Column(name="LND1_2", precision=12)
	private BigDecimal lnd1_2;
	/** 借款比重 */
	@Column(name="LND1_3", precision=11, scale=2)
	private BigDecimal lnd1_3;
	/** 保證餘額 */
	@Column(name="LND1_5", precision=12)
	private BigDecimal lnd1_5;
	/** 保證比重 */
	@Column(name="LND1_6", precision=11, scale=2)
	private BigDecimal lnd1_6;
	/** 保證排名 */
	@Column(name="LND1_7", precision=3)
	private BigDecimal lnd1_7;
	/** 綜合額度 */
	@Column(name="LND1_8", precision=12)
	private BigDecimal lnd1_8;
	/** 額度比重 */
	@Column(name="LND1_9", precision=11, scale=2)
	private BigDecimal lnd1_9;
	/** 餘額排名 */
	@Column(name="LND1_10", precision=3)
	private BigDecimal lnd1_10;
	/** 單位代號 */
	@Column(name="LNDID_1", length=3)
	private String lndid_1;
	/** 科目 */
	@Column(length=30)
	private String subj;

	//bi-directional many-to-one association to C140M01A
    @ManyToOne(fetch=FetchType.LAZY)
	@JoinColumns({
		@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false)
		})
	private C140M01A c140m01a;
    
  //bi-directional many-to-one association to C140M04B
    @ManyToOne(fetch=FetchType.LAZY)
	@JoinColumns({
		@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false)
		})
	private C140M04B c140m04b;

	/**幣別
	 * @return the curr
	 */
	public String getCurr() {
		return curr;
	}

	/**幣別
	 * @param curr the curr to set
	 */
	public void setCurr(String curr) {
		this.curr = curr;
	}

	/**單位名稱
	 * @return the lnd1_1
	 */
	public String getLnd1_1() {
		return lnd1_1;
	}

	/**單位名稱
	 * @param lnd1_1 the lnd1_1 to set
	 */
	public void setLnd1_1(String lnd1_1) {
		this.lnd1_1 = lnd1_1;
	}

	/**借款餘額
	 * @return the lnd1_2
	 */
	public BigDecimal getLnd1_2() {
		return lnd1_2;
	}

	/**借款餘額
	 * @param lnd1_2 the lnd1_2 to set
	 */
	public void setLnd1_2(BigDecimal lnd1_2) {
		this.lnd1_2 = lnd1_2;
	}

	/**借款比重
	 * @return the lnd1_3
	 */
	public BigDecimal getLnd1_3() {
		return lnd1_3;
	}

	/**借款比重
	 * @param lnd1_3 the lnd1_3 to set
	 */
	public void setLnd1_3(BigDecimal lnd1_3) {
		this.lnd1_3 = lnd1_3;
	}

	/**保證餘額
	 * @return the lnd1_5
	 */
	public BigDecimal getLnd1_5() {
		return lnd1_5;
	}

	/**保證餘額
	 * @param lnd1_5 the lnd1_5 to set
	 */
	public void setLnd1_5(BigDecimal lnd1_5) {
		this.lnd1_5 = lnd1_5;
	}

	/**保證比重
	 * @return the lnd1_6
	 */
	public BigDecimal getLnd1_6() {
		return lnd1_6;
	}

	/**保證比重
	 * @param lnd1_6 the lnd1_6 to set
	 */
	public void setLnd1_6(BigDecimal lnd1_6) {
		this.lnd1_6 = lnd1_6;
	}

	/**保證排名 
	 * @return the lnd1_7
	 */
	public BigDecimal getLnd1_7() {
		return lnd1_7;
	}

	/**保證排名 
	 * @param lnd1_7 the lnd1_7 to set
	 */
	public void setLnd1_7(BigDecimal lnd1_7) {
		this.lnd1_7 = lnd1_7;
	}

	/**綜合額度
	 * @return the lnd1_8
	 */
	public BigDecimal getLnd1_8() {
		return lnd1_8;
	}

	/**綜合額度
	 * @param lnd1_8 the lnd1_8 to set
	 */
	public void setLnd1_8(BigDecimal lnd1_8) {
		this.lnd1_8 = lnd1_8;
	}

	/**額度比重
	 * @return the lnd1_9
	 */
	public BigDecimal getLnd1_9() {
		return lnd1_9;
	}

	/**額度比重
	 * @param lnd1_9 the lnd1_9 to set
	 */
	public void setLnd1_9(BigDecimal lnd1_9) {
		this.lnd1_9 = lnd1_9;
	}

	/**餘額排名
	 * @return the lnd1_10
	 */
	public BigDecimal getLnd1_10() {
		return lnd1_10;
	}

	/**餘額排名
	 * @param lnd1_10 the lnd1_10 to set
	 */
	public void setLnd1_10(BigDecimal lnd1_10) {
		this.lnd1_10 = lnd1_10;
	}

	/**單位代號
	 * @return the lndid_1
	 */
	public String getLndid_1() {
		return lndid_1;
	}

	/**單位代號
	 * @param lndid_1 the lndid_1 to set
	 */
	public void setLndid_1(String lndid_1) {
		this.lndid_1 = lndid_1;
	}

	/**科目
	 * @return the subj
	 */
	public String getSubj() {
		return subj;
	}

	/**科目
	 * @param subj the subj to set
	 */
	public void setSubj(String subj) {
		this.subj = subj;
	}

	public C140M01A getC140m01a() {
		return this.c140m01a;
	}

	public void setC140m01a(C140M01A c140m01a) {
		this.c140m01a = c140m01a;
	}
	
	public C140M04B getC140m04b() {
		return this.c140m04b;
	}

	public void setC140m04b(C140M04B c140m04b) {
		this.c140m04b = c140m04b;
	}
}