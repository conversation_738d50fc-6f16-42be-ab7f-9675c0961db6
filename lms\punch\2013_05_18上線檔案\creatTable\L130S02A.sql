---------------------------------------------------------
-- LMS.L130S02A 異常通報表額度控管設定
---------------------------------------------------------
-- DROP TABLE LMS.L130S02A;
CREATE TABLE LMS.L130S02A (
	OID           CHAR(32)     not null,
	MAINID        CHAR(32)     not null,
	ENDDATE       DATE         not null,
	CTLTYPE   CHAR(1)      not null,
	CTLITEM   VARCHAR(12)  not null,
	CTLNA<PERSON>       VARCHAR(120) ,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>   TIMESTAMP    ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L130S02A PRIMARY KEY(OID)
) in EL_DATA_4KTS index in EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
-- DROP INDEX LMS.XL130S02A01;
CREATE INDEX LMS.XL130S02A01 ON LMS.L130S02A   (MAINID);
CREATE UNIQUE INDEX LMS.XL130S02A02 ON LMS.L130S02A   (MAINID, CTLTYPE, CTLITEM);
---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L130S02A IS '異常通報表額度控管設定';
COMMENT ON LMS.L130S02A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	ENDDATE       IS '控管迄日', 
	CTLTYPE   IS '控管種類', 
	CTLITEM   IS '控管資料', 
	CTLNAME       IS '描述',
	DELETEDTIME   IS '刪除註記日期',
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
