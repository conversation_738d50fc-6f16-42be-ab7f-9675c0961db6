var button = $("#buttonPanel");
$(document).ready(function(){
	initData();
	button.before('<div class=" tit2 color-black" id="searchActionName" name="searchActionName"></div>');

})
//初始化欄位
function initData(){
//    brank("");
	$.ajax({
        type: "POST",
        handler: "lms9515m01formhandler",
        data: {
            formAction: "queryReportType"
        },
        success: function(obj){
			$("#searchAction").setItems({
                // i18n : i18n.samplehome,
                item: obj.item,
                format: "{key}" // ,
                // value :
            });
            showThickBox(true);
        }
    });
}

// ================================================================================================================

//搜尋分行
//function brank(val){
////	if(val != ''){
//		$.ajax({
//	        type: "POST",
//	        handler: "lms9515m01formhandler",
//	        data: {
//	            formAction: "queryBranch",
//	            reportType : val
//	        },
//	        success: function(obj){
//	            // alert(JSON.stringify(obj));
//	            $("#selectBranch").setItems({
//	                // i18n : i18n.samplehome,
//	                item: obj.item,
//	                format: "{value} {key}",
//	                space: false
//	                // value :
//	            });
//	            
//	        }
//	    });
////	}
//}

function choickReportShowDate(){
    $("#startYear").val(CommonAPI.getToday().split("-")[0]);
    $("#startDate").val(CommonAPI.getToday().split("-")[0] + "-01");
    $("#endDate").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(CommonAPI.getToday().split("-")[1],2));
	if($('select#searchAction option:selected').val() == '7'){
		$("#dataYear").show();
		$("#dataDate").hide();
	}else{
		$("#dataYear").hide();
		$("#dataDate").show();
	}
}
//顯示查詢條件
function showThickBox(showResult){
//    brank("");
    $("#startYear").val(CommonAPI.getToday().split("-")[0]);
    $("#startDate").val(CommonAPI.getToday().split("-")[0] + "-01");
    $("#endDate").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(CommonAPI.getToday().split("-")[1],2));
    var thickboxHeight;
    if(showResult){
    	$("#managerReport").show();
    	thickboxHeight = 185;
    }else{
    	$("#managerReport").hide();
    	thickboxHeight = 145;
    }
    $("#lms9525new1").thickbox({
        title: i18n.lms9525v01['newInfo'],
        width: 500,
        height: thickboxHeight,
        modal: false,
        align: 'center',
        valign: 'bottom',
        i18n: i18n.def,
        buttons: {
            "sure": function(){
            	checkAndSend(showResult);
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}
//左邊補0
function padLeft(str,lenght){
	str=""+str;//統一換成string
    if(str.length >= lenght)
        return str;
    else
        return padLeft("0" +str,lenght);
}

function checkAndSend(showResult){
	//YYYY-MM 格式驗證

    var str = "";
    
    //alert(startDate);
    //起始年度
    var startYear = $("#startYear").val();
    //起始日期
    var startDate = $("#startDate").val();
    //迄至日期
    var endDate = $("#endDate").val();
    if (startYear == "") {
        //L784M01a.startDate=起始日期
        str = i18n.lms9525v01["L784M01a.startYear"];
        //val.ineldate=請輸入年月
        return CommonAPI.showMessage(str + "" + i18n.def["val.ineldate"]);
    }
    if (startDate == "") {
        //L784M01a.startDate=起始日期
        str = i18n.lms9525v01["L784M01a.startDate"];
        //val.ineldate=請輸入年月
        return CommonAPI.showMessage(str + "" + i18n.def["val.ineldate"]);
    }
    if (endDate == "") {
        //L784M01a.endDate=迄至日期
        str = i18n.lms9525v01["L784M01a.endDate"];
        //val.ineldate=請輸入年月
        return CommonAPI.showMessage(str + i18n.def["val.ineldate"]);
    }
	if (!startYear.match(/\d{4}$/)) {
        //val.date2=日期格式錯誤(YYYY-MM)
        return CommonAPI.showMessage(i18n.lms9525v01["L784M01a.dataYearError"]);
    }
	if (!startDate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
        //val.date2=日期格式錯誤(YYYY-MM)
        return CommonAPI.showMessage(i18n.def["val.date2"]);
    }
    if (!endDate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
        //val.date2=日期格式錯誤(YYYY-MM)
        return CommonAPI.showMessage(i18n.def["val.date2"]);
    }
    if (startDate.replace(/\-/g,"") > endDate.replace(/\-/g,"")) {
        //L784M01a.startDate=L784M01a.startDate
        //L784M01a.error2=不能大於迄至日期
        return CommonAPI.showErrorMessage(i18n.lms9525v01["L784M01a.startMonth"] + i18n.lms9525v01["L784M01a.error"]);
    }
    var action = $("#searchAction").val();
    if (action == "") {
        return CommonAPI.confirmMessage(i18n.lms9525v01['L784M01a.error10']);
    }
    $("#searchActionName").val($('select#searchAction option:selected').text());
    
    if(showResult){
    	switch (action) {
        case '1':
            type1();
            break;
        case '2':
            type2();
            break;
        case '3':
            type3();
            break;
        case '4':
            type4();
            break;
        case '5':
            type5();
            break;
        case '6':
            type6();
            break;
        case '7':
            type7();
            break;
        case '8':
            type8();
            break;
        case '9':
            type9();
            break;
        case '10':
            type9();
            break;
        case '11':
            type9();
            break;
        case '12':
            type9();
            break;
    }
    }else{
    	$("#gridview").jqGrid("setGridParam", {//重新設定grid需要查到的資料
            postData: {
                searchAction: $("#searchAction").val(),
                startYear: $("#startYear").val(),
                startDate: $("#startDate").val(),
                endDate: $("#endDate").val()
            },
            search: true
        }).trigger("reloadGrid");
    }
    $.thickbox.close();
}


// ================================================================================================================
// 篩選
$(function(){
    button.find("#btnFilter").click(function(){
    	showThickBox(false);
    }).end().find("#btnPrint").click(function(){
    	var gridview = $("#gridview");
        var id = gridview.getGridParam('selrow');
        //var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_006=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_006"]);
        }
        //var data = $("#gridview").getRowData(id);
        var data = gridview.getRowData(id);
        if($("#searchAction").val() == '7'){
            var mYear = CommonAPI.getToday().split("-")[0];
            var mMonth = CommonAPI.getToday().split("-")[1];
            if(parseInt(mMonth,10) == 1){
            	mYear = parseInt(mYear,10) - 1;
            	mMonth = "12";
            }else{
            	mMonth = padLeft(parseInt(mMonth,10) - 1,2);
            }
            
            $("#yearM1").val(mYear + "-" + mMonth);
            
        	var test2 = $("#lms9515new7b").thickbox({
	            title: i18n.lms9525v01['L784M01a.ManageReport'],
	            width: 250,
	            height: 150,
	            align: 'center',
	            valign: 'bottom',
	            modal: false,
	            i18n: i18n.def,
	            buttons: {
	                "print": function(){
	                	
	                	var printType = $('input:radio:checked[name="printType"]').val();
	                	 var yearM = $("#yearM1").val();
	                	 
	                	 if (printType == "allByAppr"){
	                		 var str = "";
	                         if (yearM == "") {
	                             //L784M01a.startDate=起始日期
	                             str = i18n.lms9515v01["L784M01a.startDate"]
	                             //val.ineldate=請輸入年月
	                             return CommonAPI.showMessage(str + "" + i18n.def["val.ineldate"]);
	                         }
	                         if (!yearM.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
	                             //val.date2=日期格式錯誤(YYYY-MM)
	                             return CommonAPI.showMessage(i18n.def["val.date2"]);
	                         }
	                		 
	                	 }
	                	 
	                	$.form.submit({
	                        url: "../simple/FileProcessingService",
	                        target: "_blank",
	                        data: {
	                            mainId: data.mainId,
	                            brNo : data.oid,
	                            printType : printType,
	                            startDate : yearM,
	                            fileDownloadName: "pdf7.pdf",
	                            serviceName: "lms9515r02rptservice"
	                        }
	                    });
	                },
	                "close": function(){
	                    $.thickbox.close();
	                }// 關閉
	            }
	        });// thickbox
        }else{
            var result = $("#gridview").getRowData(id);
            var oid = result.reportFile;
            download2(oid);
        }
        //openDoc(null, null, result);
    });
})

// ======================================================================================================
function type1(){
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9525gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val(),
            startDate: $("#startDate").val(),
            endDate: $("#endDate").val()
        },
        colModel: [{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'oid',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms9525v01["L784M01a.ownBrId2"], // 分行名稱
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'ownBrId' // col.id
        }, {
            colHeader: i18n.lms9525v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = grid.getRowData(rowid);
			download(null, null, data);
		}
    
    });
}

// ======================================================================================================
function type2(){
	if(userInfo.unitType == '4'){
	    var grid = $("#gridview").iGrid({
	        rownumbers: true,
	        handler: 'lms9525gridhandler',
	        height: 350, // 設定高度
	        sortname: 'createTime', // 預設排序
	        // multiselect : true,
	        postData: {
	            formAction: "queryL951m01a",
	            searchAction: $("#searchAction").val(),
	            startDate: $("#startDate").val(),
	            endDate: $("#endDate").val()
	        },
	        colModel: [{
	            name: 'oid',
	            hidden: true
	            // 是否隱藏
	        },{
	            name: 'hqCheckDate',
	            hidden: true
	            // 是否隱藏
	        }, {
	            name: 'mainId', // col.id
	            hidden: true
	            // 是否隱藏
	        }, {
	            name: 'reportFile', // col.id
	            hidden: true
	            // 是否隱藏
	        }, {
	            name: 'rptType', // col.id
	            hidden: true
	            // 是否隱藏
	        }, {
	            colHeader: i18n.lms9525v01["L784M01a.ownBrId2"], // 分行名稱
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'ownBrId' // col.id
	        }, {
	            colHeader: i18n.lms9525v01["L784M01a.dataDate"], // 資料年月
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'dataDate', // col.id
	            formatter: 'date',
	            formatoptions: {
	                srcformat: 'Y-m-d',
	                newformat: 'Y-m'
	            }
	        }, {
	            colHeader: i18n.lms9525v01["L784M01a.createTime"], // 資料產生日期
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'createTime' // col.id,
	        }, {
	            colHeader: i18n.lms9525v01["L784M01a.sendFirstTime"], // 分行傳送時間
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'docStatus' // col.id
	        }],
	         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
	         		showType2Detail();
			}
	    
	    });
	}else{
	    var grid = $("#gridview").iGrid({
	        rownumbers: true,
	        handler: 'lms9525gridhandler',
	        height: 350, // 設定高度
	        sortname: 'createTime', // 預設排序
	        // multiselect : true,
	        postData: {
	            formAction: "queryL951m01a",
	            searchAction: $("#searchAction").val(),
	            startDate: $("#startDate").val(),
	            endDate: $("#endDate").val()
	        },
	        colModel: [{
	            name: 'oid',
	            hidden: true
	            // 是否隱藏
	        },{
	            name: 'hqCheckDate',
	            hidden: true
	            // 是否隱藏
	        }, {
	            name: 'mainId', // col.id
	            hidden: true
	            // 是否隱藏
	        }, {
	            name: 'reportFile', // col.id
	            hidden: true
	            // 是否隱藏
	        }, {
	            name: 'rptType', // col.id
	            hidden: true
	            // 是否隱藏
	        }, {
	            colHeader: i18n.lms9525v01["L784M01a.ownBrId2"], // 分行名稱
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'ownBrId' // col.id
	        }, {
	            colHeader: i18n.lms9525v01["L784M01a.dataDate"], // 資料年月
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'dataDate', // col.id
	            formatter: 'date',
	            formatoptions: {
	                srcformat: 'Y-m-d',
	                newformat: 'Y-m'
	            }
	        }, {
	            colHeader: i18n.lms9525v01["L784M01a.createTime"], // 資料產生日期
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'createTime' // col.id,
	        }, {
	            colHeader: i18n.lms9525v01["L784M01a.sendFirstTime"], // 分行傳送時間
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'docStatus' // col.id
	        }],
	         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
				var data = grid.getRowData(rowid);
				download(null, null, data);
			}
	    
	    });
	}
}

// ======================================================================================================
function type3(){
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9525gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val(),
            startDate: $("#startDate").val(),
            endDate: $("#endDate").val()
        },
        colModel: [{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'oid',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms9525v01["L784M01a.ownBrId2"], // 分行名稱
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'ownBrId' // col.id
        }, {
            colHeader: i18n.lms9525v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = grid.getRowData(rowid);
			download(null, null, data);
		}
    
    });
}

// ======================================================================================================
function type5(){
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9525gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val(),
            startDate: $("#startDate").val(),
            endDate: $("#endDate").val()
        },
        colModel: [{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'oid',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms9525v01["L784M01a.ownBrId2"], // 分行名稱
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            //formatter: 'click',
            // onclick : BOM,
            name: 'ownBrId' // col.id
        }, {
            colHeader: i18n.lms9525v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = grid.getRowData(rowid);
			download(null, null, data);
		}
    });
    
}

// ======================================================================================================
function type6(){
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9525gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val(),
            startDate: $("#startDate").val(),
            endDate: $("#endDate").val()
        },
        colModel: [{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'oid',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        },{
            colHeader: i18n.lms9525v01["L784M01a.ownBrId2"], // 分行名稱
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'branchId' // col.id
        },{
            colHeader: i18n.lms9525v01["L784M01a.createTime2"], // 資料年月
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'dataDate', // col.id
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            }
        }, {
            colHeader: i18n.lms9525v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }, {
            colHeader: i18n.lms9525v01["L784M01a.transportEnd"], // 傳送時間
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'docStatus' // col.id
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = grid.getRowData(rowid);
			download(null, null, data);
		}
    
    });
}

// ======================================================================================================
function type7(){
	var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9515gridhandler',
        height: 350, // 設定高度
        sortname: 'brNo', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL784s07a",
            searchAction: $("#searchAction").val(),
            startYear : $("#startYear").val()
        },
        colModel: [{
            name: 'oid', // col.id
            hidden: true
            // 是否隱藏
        },{
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms9525v01["L784M01a.yearDate"], // 資料年度
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'apprYY' // col.id
        }, {
            colHeader: i18n.lms9525v01["L784M01a.ownBrId2"], // 分行別
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            //formatter: 'click',
            // onclick : BOM,
            name: 'brNo' // col.id
        }, {
            colHeader: i18n.lms9525v01["L784M01a.rptType2"], // 資料性質
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'caseDept' // col.id
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
         	showType7Detail();
		}
    
    });
}

var L9515V01Grid01 = $("#L9515v01Grid2").iGrid({
    handler: 'lms9515gridhandler',
    height: 350, // 設定高度
    cellsubmit: 'clientArray',
     sortname : 'apprMM', //預設排序
    colModel: [{
        colHeader: "oid",
        name: 'oid',
        hidden: true
        // 是否隱藏
    },{
        colHeader: "mainId",
        name: 'mainId',
        hidden: true
        // 是否隱藏
    },{
        colHeader: "brNo",
        name: 'brNo',
        hidden: true
        // 是否隱藏
    }, {
        colHeader: i18n.lms9525v01["L784S07A.apprYY"], // 案件核准年度(民國年)
        align: "center",
        width: 50, // 設定寬度
        // formatter : 'click',
        // onclick : function,
        name: 'apprYY' // col.id
    }, {
        colHeader: i18n.lms9525v01["L784S07A.apprMM"], // 案件核准月份
        align: "center",
        width: 50, // 設定寬度
        // formatter : 'click',
        // onclick : function,
        name: 'apprMM' // col.id
    }, {
        colHeader: i18n.lms9525v01["L784S07A.cItem12Rec"], // 逾放展期、轉正常(筆數)
        align: "right",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        //formatter : GridFormatter.number['addComma'],
        name: 'cItem12Rec' // col.id
    }, {
        colHeader: i18n.lms9525v01["L784S07A.cItem12Amt"], // 逾放展期、轉正常(金額)
        align: "right",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        //formatter : GridFormatter.number['addComma'],
        name: 'cItem12Amt' // col.id
    }, {
        colHeader: i18n.lms9525v01["L784S07A.updater"], // 異動人員
        align: "left",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'updater' // col.id
    }]
});

function showType7Detail(){
	var row = $("#gridview").getGridParam('selrow');
    if (!row) {
        return CommonAPI.showMessage(i18n.lms9525v01["L784S01A.error1"]);
    }
    var data = $("#gridview").getRowData(row);
    L9515V01Grid01.jqGrid("setGridParam", {//重新設定grid需要查到的資料
        postData: {
        	formAction : "queryL784s07aForTotalMonth",
            mainId : data.mainId,
            brNo : data.oid
        },
        search: true
    }).trigger("reloadGrid");
    
    var test2 = $("#lms9515new7a").thickbox({
        title: i18n.lms9525v01['newInfo2a'] + "-" + data.brNo,
        width: 850,
        height: 510,
        align: 'left',
        // valign : 'bottom',
        modal: false,
        i18n: i18n.def,
        buttons: {
            "print": function(){
            	$.form.submit({
                    url: "../simple/FileProcessingService",
                    target: "_blank",
                    data: {
                        mainId: data.mainId,
                        brNo : data.oid,
                        fileDownloadName: "pdf7.pdf",
                        serviceName: "lms9515r02rptservice"
                    }
                });
            },//
            "close": function(){
                $.thickbox.close();
            }// 關閉
        }
        // bottons
    
    });// thickbox
}

// ======================================================================================================
function type8(){
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9525gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val(),
            startDate: $("#startDate").val(),
            endDate: $("#endDate").val()
        },
        colModel: [{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'oid',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        },{
            colHeader: i18n.lms9525v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'dataDate', // col.id
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            }
        }, {
            colHeader: i18n.lms9525v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
     		var data = grid.getRowData(rowid);
			download(null, null, data);
		}
    
    });
}

// ======================================================================================================
function type9(){
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9525gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val(),
            startDate: $("#startDate").val(),
            endDate: $("#endDate").val()
        },
        colModel: [{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'oid',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        },{
            colHeader: i18n.lms9525v01["L784M01a.dataDate"], // 資料年月
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'dataDate', // col.id
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            }
        }, {
            colHeader: i18n.lms9525v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = grid.getRowData(rowid);
			download(null, null, data);
		}
    
    });
}

//======================================================================================================
function type10(){
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9515gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val()
        },
        colModel: [{
            name: 'oid',
            hidden: true
            // 是否隱藏
        },{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms9515v01["L784M01a.ownBrId2"], // 分行名稱
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'ownBrId' // col.id
        }, {
            colHeader: i18n.lms9515v01["L784M01a.dataDate"], // 資料年月
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'dataDate', // col.id
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            }
        }, {
            colHeader: i18n.lms9515v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }, {
            colHeader: i18n.lms9515v01["L784M01a.sendFirstTime"], // 分行傳送時間
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'sendFirstTime' // col.id
        }, {
            colHeader: i18n.lms9515v01["L784M01a.approveTime"], // 核准日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'approveTime' // col.id
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
         		var data = grid.getRowData(rowid);
				showType10Detail(data);
		}
    });
}

//======================================================================================================
function type11(){
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9515gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val()
        },
        colModel: [{
            name: 'oid',
            hidden: true
            // 是否隱藏
        },{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms9515v01["L784M01a.ownBrId2"], // 分行名稱
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'ownBrId' // col.id
        }, {
            colHeader: i18n.lms9515v01["L784M01a.dataDate"], // 資料年月
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'dataDate', // col.id
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            }
        }, {
            colHeader: i18n.lms9515v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }, {
            colHeader: i18n.lms9515v01["L784M01a.sendFirstTime"], // 分行傳送時間
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'sendFirstTime' // col.id
        }, {
            colHeader: i18n.lms9515v01["L784M01a.approveTime"], // 核准日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'approveTime' // col.id
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發列印功能
         		var data = grid.getRowData(rowid);
         		download(null,null,data);
		}
    });
}
//======================================================================================================
function type12(){
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9515gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val()
        },
        colModel: [{
            name: 'oid',
            hidden: true
            // 是否隱藏
        },{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms9515v01["L784M01a.ownBrId2"], // 分行名稱
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'ownBrId' // col.id
        }, {
            colHeader: i18n.lms9515v01["L784M01a.dataDate"], // 資料年月
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'dataDate', // col.id
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            }
        }, {
            colHeader: i18n.lms9515v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }, {
            colHeader: i18n.lms9515v01["L784M01a.sendFirstTime"], // 分行傳送時間
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'sendFirstTime' // col.id
        }, {
            colHeader: i18n.lms9515v01["L784M01a.approveTime"], // 核准日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'approveTime' // col.id
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發列印功能
         		var data = grid.getRowData(rowid);
         		download(null,null,data);
		}
    });
}

function BOM(cellvalue, options, rowObject){
    ilog.debug(rowObject);
    $.form.submit({
    
        url: '../rpt/LMS9515M01Page/01',
        
        data: {
            formAction: "queryL741s07a",
            mainId: rowObject.mainId,
            // mainDocStatus : viewstatus,
            mainOid: rowObject.oid, // mainOid 驗證文件狀態 ,權限按鈕顯現
            custId: rowObject.custId,
            dupNo: rowObject.dupNo
        
        },
        target: rowObject.oid
    });
}

var L9515V01Grid02 = $("#L9515v01Grid").iGrid({
    handler: 'lms9515gridhandler',
    height: 350, // 設定高度
    // sortname : 'oid', //預設排序
    //multiselect : true, //是否開啟多選
    colModel: [{
        colHeader: "oid",
        name: 'oid',
        hidden: true
        // 是否隱藏
    }, {
        colHeader: i18n.lms9525v01["L784S01A.endDate"], // 核定日
        align: "center",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        // formatter : 'click',
        // onclick : function,
        name: 'endDate' // col.id
    }, {
        colHeader: i18n.lms9525v01["L784S01A.custId"], // 統一編號
        align: "left",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        // formatter : 'click',
        // onclick : function,
        name: 'custId' // col.id
    }, {
        colHeader: i18n.lms9525v01["L784S01A.custName"], // 戶名
        align: "left",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        // formatter : 'click',
        // onclick : function,
        name: 'custName' // col.id
    }, {
        colHeader: i18n.lms9525v01["L784S01A.cntrNo"], // 額度序號
        align: "left",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        // formatter : 'click',
        // onclick : function,
        name: 'cntrNo' // col.id
    }, {
        colHeader: i18n.lms9525v01["L784S01A.currentApplyAmt"], // 額度(金額)
        align: "right",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        //formatter : GridFormatter.number['addComma'],
        name: 'currentApplyAmt', // col.id
        formatter:'currency', 
		formatoptions:
		{
		    thousandsSeparator: ",",
			removeTrailingZero: true,
		    decimalPlaces: 2//小數點到第幾位
		}
    }, {
    	colHeader: i18n.lms9525v01["L784S01A.hqCheckDate"], // 總處核備日
        name: 'hqCheckDate',
        align: "center",
        width: 50,
        sortable: true
    }, {
        colHeader: i18n.lms9525v01["L784S01A.hqCheckMemo"], // 備註
        align: "left",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'hqCheckMemo'
    }]
});

function showType2Detail(){
	var row = $("#gridview").getGridParam('selrow');
    if (!row) {
        return CommonAPI.showMessage(i18n.lms9525v01["L784S01A.error1"]);
    }
    var data = $("#gridview").getRowData(row);
    L9515V01Grid02.jqGrid("setGridParam", {//重新設定grid需要查到的資料
        postData: {
        	formAction : "queryL784s01a",
            mainId : data.mainId
        },
        search: true
    }).trigger("reloadGrid");
    
    var test2 = $("#lms9515new2a").thickbox({
        title: i18n.lms9525v01['newInfo2a'],
        width: 850,
        height: 510,
        align: 'left',
        // valign : 'bottom',
        modal: false,
        i18n: i18n.def,
        buttons: {
            "print": function(){
            	$.form.submit({
                    url: "../simple/FileProcessingService",
                    target: "_blank",
                    data: {
                        mainId: data.mainId,
                        brNo : data.brNo,
                        fileDownloadName: "pdf201.pdf",
                        serviceName: "lms9515r03rptservice"
                    }
                });
            },//
            "close": function(){
                $.thickbox.close();
            }// 關閉
        }
        // bottons
    
    });// thickbox
}

//======================================================================================================
function download(cellvalue, options, data){
    // alert(data.reportFile);
    $.capFileDownload({
         handler: "simplefiledwnhandler",
        data: {
            fileOid: data.reportFile
        }
    });
    
}

//下載EXCEL
function download2(oid){

    $.capFileDownload({
          handler: "simplefiledwnhandler",
        data: {
            fileOid: oid
        }
    });
    
}

function download3(cellvalue, options, data){
    // alert(data.reportFile);
    $.capFileDownload({
         handler: "simplefiledwnhandler",
        data: {
            fileOid: data.oid
        }
    });
    
}

$("input[type='radio'][name='printType']").click(function(){
	var value = $(this).filter(":checked").val(); 
	
	if(value == "allByAppr"){
		$("#startYearMonth").show();
	}else{
		$("#startYearMonth").hide();
	}
});
