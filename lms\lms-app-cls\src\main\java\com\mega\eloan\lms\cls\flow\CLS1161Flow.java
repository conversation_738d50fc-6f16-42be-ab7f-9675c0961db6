/* 
 *  CLS1161Flow.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.flow;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.dao.C160M01ADao;
import com.mega.eloan.lms.dao.C160M01DDao;
import com.mega.eloan.lms.dao.C160M01EDao;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01D;
import com.mega.eloan.lms.model.C160M01E;
import com.mega.sso.context.MegaSSOSecurityContext;

/**
 * <pre>
 * 個金動審表 - 流程
 * </pre>
 * 
 * @since 2013/1/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/30,Fantasy,new
 *          </ul>
 */
@Component
public class CLS1161Flow extends AbstractFlowHandler {

	@Resource
	C160M01ADao c160m01aDao;

	@Resource
	C160M01EDao c160m01eDao;

	@Resource
	C160M01DDao c160m01dDao;

	@Transition(node = "確認", value = "先行動用")
	public void First(FlowInstance instance) {
		String instanceId = Util.trim(instance.getId());
		C160M01A c160m01a = c160m01aDao.findByOid(instanceId);
		if (c160m01a != null) {
			c160m01a.setApprover(MegaSSOSecurityContext.getUserId());
			c160m01a.setReCheckId(MegaSSOSecurityContext.getUserId());
			c160m01aDao.save(c160m01a);
		}
	}

	@Transition(node = "確認", value = "核准")
	public void Upload(FlowInstance instance) {
		String instanceId = Util.trim(instance.getId());
		C160M01A c160m01a = c160m01aDao.findByOid(instanceId);
		if (c160m01a != null) {
			c160m01a.setApprover(MegaSSOSecurityContext.getUserId());
			c160m01a.setReCheckId(MegaSSOSecurityContext.getUserId());
			c160m01aDao.save(c160m01a);
		}
		// upload MIS
		UploadMis(instance);
	}

	@Transition(node = "確認", value = "退回")
	public void back(FlowInstance instance) {
		String instanceId = Util.trim(instance.getId());
		C160M01A c160m01a = c160m01aDao.findByOid(instanceId);
		if (c160m01a != null) {
			List<C160M01E> list = c160m01eDao
					.findByMainId(c160m01a.getMainId());
			c160m01eDao.delete(list);

			c160m01a.setApprover(null);
			c160m01a.setReCheckId(null);
			c160m01a.setManagerId(null);
			c160m01a.setBossId(null);
			c160m01aDao.save(c160m01a);
		}
	}

	@Transition(node = "先行動用確認", value = "先行動用核准")
	public void Upload2(FlowInstance instance) {
		String instanceId = Util.trim(instance.getId());
		C160M01A c160m01a = c160m01aDao.findByOid(instanceId);
		if (c160m01a != null) {
			String mainId = c160m01a.getMainId();
			C160M01D c160m01d = c160m01dDao.findByUniqueKey(mainId);
			if (c160m01d != null) {
				c160m01d.setBossId(MegaSSOSecurityContext.getUserId());
				c160m01dDao.save(c160m01d);
			}
		}
		// upload MIS
		UploadMis(instance);
	}

	private void UploadMis(FlowInstance instance) {
		//
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C160M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CLSDocStatusEnum.class;
	}
}