var init120s08 = {
	fhandle : "lms1201formhandler",
	ghandle : "lms1201gridhandler",
	fhandle140 : "lms1401m01formhandler",
	ghandle140 : "lms1405gridhandler",
	versionDate : "2023-03-01",
	defButton : {
		"close" : function() {
			$.thickbox.close();
		}
	}
};

var initDfd;
pageJsInit(function() {
  $(function() {
initDfd = initDfd || $.Deferred();
initDfd.done(function(auth) {

			// ===================Grid Code=============================
			/** 主表grid */

			$("#gridviewPrice").iGrid({
				needPager : false,
				handler : init120s08.ghandle,
				height : 150,
				rownumbers : true,
				sortname : 'custId|printGroup|curr|printSeq',
				sortorder : 'asc|asc|asc|asc',
				multiselect : true,
				// rowNum: 10,
				postData : {
					formAction : "queryL120s08a",
					mainId : responseJSON.mainId
				},
				colModel : [ {
					colHeader : i18n.lms1401s03["L120S08A.custId"],// 統編
					width : 20,
					name : 'custId',
					sortable : true
				}, {
					colHeader : i18n.lms1401s03["L120S08A.curr"],// 幣別
					width : 10,
					name : 'curr',
					sortable : true,
					formatter : 'click',
					onclick : L120s08aAPI.opendocBox
				}, {
					colHeader : i18n.lms1401s03["L120S08A.rateDscr"],// "利率條件"
					name : 'rateDscr',
					width : 120,
					sortable : true,
					align : "left"
				}, {
					colHeader : i18n.lms1401s03["L120S08A.versionDate"],// "版本日期"
					name : 'versionDate',
					width : 20,
					sortable : true,
					align : "left"	
				}, {
					colHeader : i18n.lms1401s03["L120S08A.printGroup"],// "列印群組號碼"
					name : 'printGroup',
					width : 20,
					sortable : true,
					align : "left"				
				}, {
					name : 'oid',
					hidden : true
				} ],
				ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
					var data = $(L120s08aAPI.mainGridId).getRowData(rowid);
					L120s08aAPI.opendocBox(null, null, data,data.versionDate);
				}
			});

			L120s08aAPI.gridviewConnomOtherSelect();

			// button
			$("#addNew20170905").click(function() {

				L120s08aAPI.opendocBox('new', null, null,'2017-09-05');
			});
			// button
			$("#addNew20181001").click(function() {

				L120s08aAPI.opendocBox('new', null, null,'2018-10-01');
			});
			$("#addNew20210501").click(function() {
				L120s08aAPI.opendocBox('new', null, null,'2021-05-01');
			});
			$("#addNew20220222").click(function() {
				L120s08aAPI.opendocBox('new', null, null,'2022-02-22');
			});
			$("#addNew20230301").click(function() {
				L120s08aAPI.opendocBox('new', null, null,'2023-03-01');
			});
			
			$("#remove").click(function() {
				L120s08aAPI.remove();
			});
			 
			$("#btnCaculate_20181001")
					.click(
							function() {
								if (!L120s08aAPI.btnCaculate_20181001()) {
									//L120S08A.error14=申請減碼項目之每項減碼數字不符規定<br>借戶各項往來實績：(每項減碼≦0.25%)<br>集團或關係人(不含借戶)貢獻：(每項減碼≦0.20%)<br>列舉實績優異貢獻項目申請特別減碼：(合計減碼≦0.50%)
									CommonAPI
											.showErrorMessage(i18n.lms1401s03["L120S08A.error14"]);
								}
							});
			$("#btnClearS_20181001").click(function() {
				L120s08aAPI.btnClear_20181001('S');
			});
			$("#btnClearN_20181001").click(function() {
				L120s08aAPI.btnClear_20181001('N');
			});
			$("#btnApplyGroup_20181001").click(function() {
				L120s08aAPI.btnApplyGroup_20181001();
			});
			
			
			$.ajax({
				handler : init120s08.fhandle,
				data : {// 把資料轉成json
					formAction : "getInit1401S03PanelData",
					mainId : responseJSON.mainId
				}
			}).done(function(initObj) {

				if (!thickboxOptions.readOnly) {
					if(initObj.showOldVersion == "Y"){
						// button
						$("#addNew20170905").show();
						$("#itemDscr0N").readOnly(false);
					}else{
						$("#addNew20170905").hide();
						$("#itemDscr0N").readOnly(true);
					}
					if(initObj.showVer2018 == "Y"){
				        $("#addNew20181001").show();
				    }else{
				        $("#addNew20181001").hide();
				    }
					if(initObj.showVer2021 == "Y"){
				        $("#addNew20210501").show();
				    }else{
				        $("#addNew20210501").hide();
				    }
					if(initObj.showVer2022 == "Y"){
				        $("#addNew20220222").show();
				    }else{
				        $("#addNew20220222").hide();
				    }
					if(initObj.showVer2023 == "Y"){
				        $("#addNew20230301").show();
				    }else{
				        $("#addNew20230301").hide();
				    }
				}else{
					 $("#lms1405s03PanelBt").find("button").hide();
				}
			}); // close ajax
			
			
			 
			
			 

		});
	})
});

// 額度明細表內程式
var L120s08aAPI = {

	mainGridId : "#gridviewPrice",
	/**
	 * 觸發主檔Grid更新
	 */
	_triggerMainGrid : function() {
		$(L120s08aAPI.mainGridId).trigger('reloadGrid');
	},
	/** 主檔 */
	opendocBox : function(type, docOid, data,versionDate) {

		verDt = '2017-09-05';
		if(data){
			verDt = data.versionDate
			if(!verDt){
				verDt = '2017-09-05';
			}
		}else{
			verDt = versionDate;
		}
	
		if (verDt == '2023-03-01'){
			L120s08aAPI.opendocBox_20230301(type, docOid, data);
		}else if (verDt == '2022-02-22'){
			L120s08aAPI.opendocBox_20220222(type, docOid, data);
		}else if (verDt == '2021-05-01'){
			L120s08aAPI.opendocBox_20210501(type, docOid, data);
		}else if(verDt == '2018-10-01' ){
			L120s08aAPI.opendocBox_20181001(type, docOid, data);
		}else{
			L120s08aAPI.opendocBox_20170905(type, docOid, data);
		}

	},
	/** 主檔 */
	opendocBox_20170905 : function(type, docOid, data) {
		var versionDate = '2017-09-05';
		init120s08.versionDate = versionDate;
		
		$("#inputL120s08Box").empty();
        $("#inputL120s08Box").load("../../lms/lmsL120S08BBox01",function(){	
			var verNo = init120s08.versionDate.replace(/-/g, "");
            var L120S08FormName = "L120S08Form_"+verNo;
            var $L120S08Form = $("#"+L120S08FormName);
            var $choiceCurrBox = $("#choiceCurrBox_"+verNo);
            var $currBox = $("#currBox_"+verNo);
			$L120S08Form.reset();
            if (type == 'new') {
                $choiceCurrBox.thickbox({
                    title : i18n.lms1401s03['L120S08A.title1'],// L120S08A.title1, //選擇利率幣別
                    width : 200,
                    height : 100,
                    modal : true,
                    readOnly : false,
                    align : "center",
                    i18n : i18n.def,
                    valign : "bottom",
                    buttons : {
                        "sure" : function() {
                            var curr = $("#rateCurr :selected").val();//$choiceCurrBox.find('#rateCurr').val();
                            // 當為新增授信科目，要判斷是否有已登錄幣別。
                            $.ajax({
                                handler : init120s08.fhandle,
                                data : {// 把資料轉成json
                                    formAction : "chkIsCurrExistL120s08a",
                                    mainId : responseJSON.mainId,
                                    curr : curr,
                                    versionDate : init120s08.versionDate
                                }
							}).done(function(obj) {
								$L120S08Form.find('#curr').val(curr);
								// 新增時，合理利潤率 與 授權減碼帶預設值
								$L120S08Form.find('#reasonRate_dscr').val(i18n.lms1401s03['L120S08B.reasonRateDf']);
								$L120S08Form.find('#underweight_dscr').val(i18n.lms1401s03['L120S08B.underweightDf']);

								// J-107-0077-001 Web e-Loan 授信案件簽報書之「新臺幣、美元利率合理性分析表」修改
								var showRiskAdd = false;

								// J-107-01?? GGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGG
								// 待程式修改申請來要調成 false ****************************
								var showCompetition = true;

								$.thickbox.close();

								API_2017.openDetailBox(type, docOid, data, showRiskAdd,showCompetition);	
                            }); // close ajax
                        },
                        "cancel" : function() {
                            $.thickbox.close();
                            return;
                        }
                    }
                });
            } else {
                // 為修改授信科目資料，
                $.ajax({
                    handler : init120s08.fhandle,
                    data : {// 把資料轉成json
                        formAction : "queryL120s08DataByOid",
                        docOid : data.oid
                    }
				}).done(function(objL120s08) {
					$L120S08Form.injectData(objL120s08);

					// J-107-0077-001 Web e-Loan 授信案件簽報書之「新臺幣、美元利率合理性分析表」修改
					var showRiskAdd = false;
					if (objL120s08.hasLostDiffRate == "N") {
					    // 舊案沒有各等級擬制呆帳損失差異率加碼，所以只顯示風險加碼
					    showRiskAdd = true;
					}

					// J-107-01?? GGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGG
					var showCompetition = true;
					API_2017.openDetailBox(type, docOid, data, showRiskAdd,showCompetition);
                }); // close ajax
            }
        });
	},
	/** 主檔 */
	opendocBox_20181001 : function(type, docOid, data) {
        var versionDate = '2018-10-01';
        init120s08.versionDate = versionDate;
		
		$("#inputL120s08Box").empty();
		$("#inputL120s08Box").load("../../lms/lmsL120S08BBox02",function(){
			var verNo = init120s08.versionDate.replace(/-/g, "");
			var L120S08FormName = "L120S08Form_"+verNo;
			var $L120S08Form = $("#"+L120S08FormName);
			var $choiceCurrBox = $("#choiceCurrBox_"+verNo);
			var $currBox = $("#currBox_"+verNo);
			$L120S08Form.reset();
			if (type == 'new') {
				// 詢問幣別跟借款人
				$.ajax({
		            handler: inits.fhandle,
		            action: "queryL120s01a",
		            data: {}
				}).done(function(obj) {
					if ($.isEmptyObject(obj.item)) {
					    //L120S08A.error13=請先登錄借款人資料
					    return CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error13']);
					}
					$choiceCurrBox.find("#selectBorrower").setItems({//塞複製的select
					    item: obj.item,
					    space: false,
					    format: "{value} {key}"
					});

					//列印群組號碼BY CUSTID
					var tempHtml = "";
					for (var i = 1; i <= 20; i++) {
					    tempHtml += "<option value=" + i + ">" + i + "</option>";
					}
					$("#selectPrintGroup").html(tempHtml);

					$choiceCurrBox.thickbox({
						title : i18n.lms1401s03['L120S08A.title1'],// L120S08A.title1,// //選擇利率幣別
						width : 400,
						height : 260,
						modal : true,
						readOnly : false,
						align : "center",
						i18n : i18n.def,
						valign : "bottom",
						buttons: {
							"sure" : function() {
								var curr = $("#rateCurr :selected").val();//$choiceCurrBox.find('#rateCurr').val();
								var printGroup = $currBox.find('#selectPrintGroup').val();
								var fullCustId = $currBox.find('#selectBorrower').val();
								
								if(fullCustId == ""){
									//L120S08A.error12=請先輸入  L120S08A.custId=借款人統編
									return CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error12'] + i18n.lms1401s03['L120S08A.custId']);
								}
								
								if(printGroup == ""){
									//L120S08A.error12=請先輸入  L120S08A.printGroup=列印群組號碼
									return CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error12'] + i18n.lms1401s03['L120S08A.printGroup']);
								}
								
								$.thickbox.close();
								
								// 當為新增授信科目，要判斷是否有已登錄幣別。
					            $.ajax({
					                handler : init120s08.fhandle,
					                data : {// 把資料轉成json
					                    formAction : "chkIsCurrExistL120s08a",
					                    mainId : responseJSON.mainId,
					                    curr : curr,
					                    versionDate : init120s08.versionDate,
										printGroup:printGroup,
										fullCustId:fullCustId
					                }
								}).done(function(obj) {
									$L120S08Form.find('#curr').val(curr);
									$L120S08Form.find('#printGroup').val(printGroup);
									$L120S08Form.find('#custId').val(fullCustId.substr(0,fullCustId.length-1));
									$L120S08Form.find('#dupNo').val(fullCustId.substr(fullCustId.length-1,fullCustId.length));

									// 新增時，合理利潤率 與 授權減碼帶預設值
									$L120S08Form.find('#reasonRate_dscr').val(i18n.lms1401s03['L120S08B.reasonRateDf']);
									$L120S08Form.find('#underweight_dscr').val(i18n.lms1401s03['L120S08B.underweightDf']);

									API_2018.openDetailBox(type,docOid,data);
								}); // close ajax									
							},
							"cancel" : function() {
								$.thickbox.close();
								return;
							}
						}
					});
		        }); //close ajax
			} else {
				// 為修改授信科目資料，
				$.ajax({
					handler : init120s08.fhandle,
					data : {// 把資料轉成json
						formAction : "queryL120s08DataByOid_20181001",
						docOid : data.oid
					}
				}).done(function(objL120s08) {
					$L120S08Form.injectData(objL120s08);
					API_2018.openDetailBox(type, docOid, data);
					if (navigator.userAgent.search("MSIE") >= 0) {
					    // 若為IE瀏覽器
					    setTimeout(function(){
					        $L120S08Form.find("#memo").val(objL120s08.memo);
					    },100);
					} else if (navigator.userAgent.search("Edg") >= 0) {
					    // 若為Edge瀏覽器
					    L120s08aAPI.setCkeditor("memo", objL120s08.memo);
					} else {
					    // 採用IE way
					    $L120S08Form.find("#memo").val(objL120s08.memo);
					}	
				}); // close ajax
			}
        });
	},
	/** 主檔 */
	opendocBox_20210501 : function(type, docOid, data) {
        var versionDate = '2021-05-01';
        init120s08.versionDate = versionDate;
		
		$("#inputL120s08Box").empty();
		$("#inputL120s08Box").load("../../lms/lmsL120S08BBox03",function(){
			var verNo = init120s08.versionDate.replace(/-/g, "");
			var L120S08FormName = "L120S08Form_"+verNo;
			var $L120S08Form = $("#"+L120S08FormName);
			var $choiceCurrBox = $("#choiceCurrBox_"+verNo);
			var $currBox = $("#currBox_"+verNo);
			$L120S08Form.reset();
			if (type == 'new') {
				// 詢問幣別跟借款人
				$.ajax({
		            handler: inits.fhandle,
		            action: "queryL120s01a",
		            data: {}
				}).done(function(obj) {
					if ($.isEmptyObject(obj.item)) {
					    //L120S08A.error13=請先登錄借款人資料
					    return CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error13']);
					}
					$choiceCurrBox.find("#selectBorrower").setItems({//塞複製的select
					    item: obj.item,
					    space: false,
					    format: "{value} {key}"
					});

					//列印群組號碼BY CUSTID
					var tempHtml = "";
					for (var i = 1; i <= 20; i++) {
					    tempHtml += "<option value=" + i + ">" + i + "</option>";
					}
					$("#selectPrintGroup").html(tempHtml);

					$choiceCurrBox.thickbox({
						title : i18n.lms1401s03['L120S08A.title1'],// L120S08A.title1,// //選擇利率幣別
						width : 400,
						height : 260,
						modal : true,
						readOnly : false,
						align : "center",
						i18n : i18n.def,
						valign : "bottom",
						buttons: {
							"sure" : function() {
								var curr = $("#rateCurr :selected").val();//$choiceCurrBox.find('#rateCurr').val();
								var printGroup = $currBox.find('#selectPrintGroup').val();
								var fullCustId = $currBox.find('#selectBorrower').val();
								
								if(fullCustId == ""){
									//L120S08A.error12=請先輸入  L120S08A.custId=借款人統編
									return CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error12'] + i18n.lms1401s03['L120S08A.custId']);
								}
								
								if(printGroup == ""){
									//L120S08A.error12=請先輸入  L120S08A.printGroup=列印群組號碼
									return CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error12'] + i18n.lms1401s03['L120S08A.printGroup']);
								}
								
								$.thickbox.close();
								
								// 當為新增授信科目，要判斷是否有已登錄幣別。
					            $.ajax({
					                handler : init120s08.fhandle,
					                data : {// 把資料轉成json
					                    formAction : "chkIsCurrExistL120s08a",
					                    mainId : responseJSON.mainId,
					                    curr : curr,
					                    versionDate : init120s08.versionDate,
										printGroup:printGroup,
										fullCustId:fullCustId
					                }
								}).done(function(obj) {
									$L120S08Form.find('#curr').val(curr);
									$L120S08Form.find('#printGroup').val(printGroup);
									$L120S08Form.find('#custId').val(fullCustId.substr(0,fullCustId.length-1));
									$L120S08Form.find('#dupNo').val(fullCustId.substr(fullCustId.length-1,fullCustId.length));

									// 新增時，合理利潤率 與 授權減碼帶預設值
									$L120S08Form.find('#reasonRate_dscr').val(i18n.lms1401s03['L120S08B.reasonRateDf']);
									$L120S08Form.find('#underweight_dscr').val(i18n.lms1401s03['L120S08B.underweightDf']);

									API_2021.openDetailBox(type,docOid,data);
								}); // close ajax									
							},
							"cancel" : function() {
								$.thickbox.close();
								return;
							}
						}
					});
		        }); //close ajax
			} else {
				// 為修改授信科目資料，
				$.ajax({
					handler : init120s08.fhandle,
					data : {// 把資料轉成json
						formAction : "queryL120s08DataByOid_20210501",
						docOid : data.oid
					}
				}).done(function(objL120s08) {
					$L120S08Form.injectData(objL120s08);
					API_2021.openDetailBox(type, docOid, data);
					if (navigator.userAgent.search("MSIE") >= 0) {
					    // 若為IE瀏覽器
					    setTimeout(function(){
					        $L120S08Form.find("#memo").val(objL120s08.memo);
					    },100);
					} else if (navigator.userAgent.search("Edg") >= 0) {
					    // 若為Edge瀏覽器
					    L120s08aAPI.setCkeditor("memo", objL120s08.memo);
					} else {
					    // 採用IE way
					    $L120S08Form.find("#memo").val(objL120s08.memo);
					}
				}); // close ajax
			}
        });
	},
	/** 主檔 */
	opendocBox_20220222 : function(type, docOid, data) {
        var versionDate = '2022-02-22';
        init120s08.versionDate = versionDate;
		
		$("#inputL120s08Box").empty();
		$("#inputL120s08Box").load("../../lms/lmsL120S08BBox04",function(){
			var verNo = init120s08.versionDate.replace(/-/g, "");
			var L120S08FormName = "L120S08Form_"+verNo;
			var $L120S08Form = $("#"+L120S08FormName);
			var $choiceCurrBox = $("#choiceCurrBox_"+verNo);
			var $currBox = $("#currBox_"+verNo);
			$L120S08Form.reset();
			if (type == 'new') {
				// 詢問幣別跟借款人
				$.ajax({
		            handler: inits.fhandle,
		            action: "queryL120s01a",
		            data: {}
				}).done(function(obj) {
					if ($.isEmptyObject(obj.item)) {
					    //L120S08A.error13=請先登錄借款人資料
					    return CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error13']);
					}
					$choiceCurrBox.find("#selectBorrower").setItems({//塞複製的select
					    item: obj.item,
					    space: false,
					    format: "{value} {key}"
					});

					//列印群組號碼BY CUSTID
					var tempHtml = "";
					for (var i = 1; i <= 20; i++) {
					    tempHtml += "<option value=" + i + ">" + i + "</option>";
					}
					$("#selectPrintGroup").html(tempHtml);

					$choiceCurrBox.thickbox({
						title : i18n.lms1401s03['L120S08A.title1'],// L120S08A.title1,// //選擇利率幣別
						width : 400,
						height : 260,
						modal : true,
						readOnly : false,
						align : "center",
						i18n : i18n.def,
						valign : "bottom",
						buttons: {
							"sure" : function() {
								var curr = $("#rateCurr :selected").val();//$choiceCurrBox.find('#rateCurr').val();
								var printGroup = $currBox.find('#selectPrintGroup').val();
								var fullCustId = $currBox.find('#selectBorrower').val();
								
								if(fullCustId == ""){
									//L120S08A.error12=請先輸入  L120S08A.custId=借款人統編
									return CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error12'] + i18n.lms1401s03['L120S08A.custId']);
								}
								
								if(printGroup == ""){
									//L120S08A.error12=請先輸入  L120S08A.printGroup=列印群組號碼
									return CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error12'] + i18n.lms1401s03['L120S08A.printGroup']);
								}
								
								$.thickbox.close();
								
								// 當為新增授信科目，要判斷是否有已登錄幣別。
					            $.ajax({
					                handler : init120s08.fhandle,
					                data : {// 把資料轉成json
					                    formAction : "chkIsCurrExistL120s08a",
					                    mainId : responseJSON.mainId,
					                    curr : curr,
					                    versionDate : init120s08.versionDate,
										printGroup:printGroup,
										fullCustId:fullCustId
					                }
								}).done(function(obj) {
									$L120S08Form.find('#curr').val(curr);
									$L120S08Form.find('#printGroup').val(printGroup);
									$L120S08Form.find('#custId').val(fullCustId.substr(0,fullCustId.length-1));
									$L120S08Form.find('#dupNo').val(fullCustId.substr(fullCustId.length-1,fullCustId.length));

									// 新增時，合理利潤率 與 授權減碼帶預設值
									$L120S08Form.find('#reasonRate_dscr').val(i18n.lms1401s03['L120S08B.reasonRateDf']);
									$L120S08Form.find('#underweight_dscr').val(i18n.lms1401s03['L120S08B.underweightDf']);

									API_2022.openDetailBox(type,docOid,data);
								}); // close ajax									
							},
							"cancel" : function() {
								$.thickbox.close();
								return;
							}
						}
					});
		        }); //close ajax
			} else {
				// 為修改授信科目資料，
				$.ajax({
					handler : init120s08.fhandle,
					data : {// 把資料轉成json
						formAction : "queryL120s08DataByOid_20220222",
						docOid : data.oid
					}
				}).done(function(objL120s08) {
					$L120S08Form.injectData(objL120s08);
					API_2022.openDetailBox(type, docOid, data);
					if (navigator.userAgent.search("MSIE") >= 0) {
					    // 若為IE瀏覽器
					    setTimeout(function(){
					        $L120S08Form.find("#memo").val(objL120s08.memo);
					    },100);
					} else if (navigator.userAgent.search("Edg") >= 0) {
					    // 若為Edge瀏覽器
					    L120s08aAPI.setCkeditor("memo", objL120s08.memo);
					} else {
					    // 採用IE way
					    $L120S08Form.find("#memo").val(objL120s08.memo);
					}
				}); // close ajax
			}
        });
	},
	/** 主檔 */
	opendocBox_20230301 : function(type, docOid, data) {
        var versionDate = '2023-03-01';
        init120s08.versionDate = versionDate;
		
		$("#inputL120s08Box").empty();
		$("#inputL120s08Box").load("../../lms/lmsL120S08BBox05",function(){
			var verNo = init120s08.versionDate.replace(/-/g, "");
			var L120S08FormName = "L120S08Form_"+verNo;
			var $L120S08Form = $("#"+L120S08FormName);
			var $choiceCurrBox = $("#choiceCurrBox_"+verNo);
			var $currBox = $("#currBox_"+verNo);
			$L120S08Form.reset();
			if (type == 'new') {
				// 詢問幣別跟借款人
				$.ajax({
		            handler: inits.fhandle,
		            action: "queryL120s01a",
		            data: {}
				}).done(function(obj) {
					if ($.isEmptyObject(obj.item)) {
					    //L120S08A.error13=請先登錄借款人資料
					    return CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error13']);
					}
					$choiceCurrBox.find("#selectBorrower").setItems({//塞複製的select
					    item: obj.item,
					    space: false,
					    format: "{value} {key}"
					});

					//列印群組號碼BY CUSTID
					var tempHtml = "";
					for (var i = 1; i <= 20; i++) {
					    tempHtml += "<option value=" + i + ">" + i + "</option>";
					}
					$("#selectPrintGroup").html(tempHtml);

					$choiceCurrBox.thickbox({
						title : i18n.lms1401s03['L120S08A.title1'],// L120S08A.title1,// //選擇利率幣別
						width : 400,
						height : 260,
						modal : true,
						readOnly : false,
						align : "center",
						i18n : i18n.def,
						valign : "bottom",
						buttons: {
							"sure" : function() {
								var curr = $("#rateCurr :selected").val();//$choiceCurrBox.find('#rateCurr').val();
								var printGroup = $currBox.find('#selectPrintGroup').val();
								var fullCustId = $currBox.find('#selectBorrower').val();
								
								if(fullCustId == ""){
									//L120S08A.error12=請先輸入  L120S08A.custId=借款人統編
									return CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error12'] + i18n.lms1401s03['L120S08A.custId']);
								}
								
								if(printGroup == ""){
									//L120S08A.error12=請先輸入  L120S08A.printGroup=列印群組號碼
									return CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error12'] + i18n.lms1401s03['L120S08A.printGroup']);
								}
								
								$.thickbox.close();
								
								// 當為新增授信科目，要判斷是否有已登錄幣別。
					            $.ajax({
					                handler : init120s08.fhandle,
					                data : {// 把資料轉成json
					                    formAction : "chkIsCurrExistL120s08a",
					                    mainId : responseJSON.mainId,
					                    curr : curr,
					                    versionDate : init120s08.versionDate,
										printGroup:printGroup,
										fullCustId:fullCustId
					                }
								}).done(function(obj) {
									$L120S08Form.find('#curr').val(curr);
									$L120S08Form.find('#printGroup').val(printGroup);
									$L120S08Form.find('#custId').val(fullCustId.substr(0,fullCustId.length-1));
									$L120S08Form.find('#dupNo').val(fullCustId.substr(fullCustId.length-1,fullCustId.length));

									// 新增時，合理利潤率 與 授權減碼帶預設值
									$L120S08Form.find('#reasonRate_dscr').val(i18n.lms1401s03['L120S08B.reasonRateDf']);
									$L120S08Form.find('#underweight_dscr').val(i18n.lms1401s03['L120S08B.underweightDf']);

									// 是否為新戶
									if(obj.isNewCust && obj.isNewCust == 'Y'){
										$L120S08Form.find('#isNewCustSpan').show();
										$L120S08Form.find('#plusTotal_rate').val('0.45');// 動態塞入0.45給使用者看
									}
									API_2023.openDetailBox(type,docOid,data);
								}); // close ajax									
							},
							"cancel" : function() {
								$.thickbox.close();
								return;
							}
						}
					});
		        }); //close ajax
			} else {
				// 為修改授信科目資料，
				$.ajax({
					handler : init120s08.fhandle,
					data : {// 把資料轉成json
						formAction : "queryL120s08DataByOid_20230301",
						docOid : data.oid
					}
				}).done(function(objL120s08) {
					$L120S08Form.injectData(objL120s08);

					// 是否為新戶
					if(objL120s08.isNewCust == 'Y'){
						$L120S08Form.find('#isNewCustSpan').show();
						$L120S08Form.find('#plusTotal_rate').val('0.45');// 動態塞入0.45給使用者看
					}

					// 是否本案未符合經權內之加減碼，仍於經權內敘作
					if(objL120s08.isOverAuth_dscr == 'Y'){
						$L120S08Form.find('#isOverAuthSpan').show();
					}

					API_2023.openDetailBox(type, docOid, data);
					if (navigator.userAgent.search("MSIE") >= 0) {
					    // 若為IE瀏覽器
					    setTimeout(function(){
					        $L120S08Form.find("#memo").val(objL120s08.memo);
					    },100);
					} else if (navigator.userAgent.search("Edg") >= 0) {
					    // 若為Edge瀏覽器
					    L120s08aAPI.setCkeditor("memo", objL120s08.memo);
					} else {
					    // 採用IE way
					    $L120S08Form.find("#memo").val(objL120s08.memo);
					}
				}); // close ajax
			}
        });
	},
	/* 注意：如果CKeditor在Thickbox裡一定要先打開ThickBox再執行此方法 */
	setCkeditor : function(name, val){
        var oEditor = CKEDITOR.instances[name];
        if (oEditor) {
            oEditor.setData(val);
        }
    },
	/**
	 * 判斷是否已有額度明細表 return true ,false
	 */
	isCheckGrid : function() {
		var countGrid = $(L120s08aAPI.mainGridId).jqGrid('getGridParam',
				'records');
		if (countGrid == 0) {
			// L120S08A.error04=尚未登錄利率定價合理性分析表，無法執行此動作
			CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error04']);
			return false;
		}
		return true;
	},
	/** 當現請額度大於攤貸總金額時 會出現 grid選擇 將餘額加至哪筆 */
	remove : function() {
		if (!L120s08aAPI.isCheckGrid()) {
			return false;
		}
		;
		var $gridviewC_2 = $(L120s08aAPI.mainGridId);
		var ids = $gridviewC_2.getGridParam('selarrrow');
		var oids = [];

		if (ids == "") {
			// action_005=請先選取一筆以上之資料列
			return CommonAPI.showErrorMessage(i18n.def['action_005']);
		}
		for ( var i in ids) {
			oids.push($gridviewC_2.getRowData(ids[i]).oid);
		}
		CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b) {
			if (b) {
				$.ajax({
					handler : init120s08.fhandle,
					action : "removeL120s08Data",
					data : {
						mainId : responseJSON.mainId,
						oids : oids
					}
				}).done(function(obj) {
					L120s08aAPI._triggerMainGrid();
				});
			}
		});

	},
	/** 選取額度序號 */
	gridviewConnomOtherSelect : function() {
		if ($("#gridviewConnomOtherSelect").length) {
			$("#gridviewConnomOtherSelect").iGrid({
				handler : init120s08.ghandle140,
				sortname : 'cntrNo',
				sortorder : 'asc',
				colModel : [ {
					colHeader : i18n.lms1401s03["L140M01a.cntrNo"],// "額度序號",
					name : 'cntrNo',
					align : "center",
					width : 80,
					sortable : true
				}, {
					colHeader : "oid",
					name : 'oid',
					hidden : true
				}, {
					colHeader : "mainId",
					name : 'mainId',
					hidden : true
				} ]
			});
		}	
	},
	/**
	 * 引進關係戶往來彙總二維表集團評等與貢獻度合計 return true ,false
	 */
	btnApplyGroup_20181001 : function() {

		var verNo = init120s08.versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);
		
		var nowDate = new Date();
		var MM = nowDate.getMonth();
		var YY = nowDate.getFullYear();
		var SMM;
		var SYY;
		if (MM == 0) {
			MM = 12;
		}

		if (MM == 12) {
			SMM = MM - 5;
			YY = YY - 1;
			SYY = YY;
		} else if (MM > 5 && MM < 12) {
			SMM = MM - 5;
			SYY = YY;
		} else {
			SMM = MM + 12 - 5;
			SYY = YY - 1;
		}

		var $tL120S08Form1a = $("#tL120S08Form1a_"+verNo);
		$tL120S08Form1a.find("#queryDateE0").val(YY);
		$tL120S08Form1a.find("#queryDateE1").val(MM);

		$("#inputSearch_"+verNo)
				.thickbox(
						{ // 使用選取的內容進行彈窗
							title : i18n.lms1401s03["L120S08A.fieldTitle48"]+"/"+i18n.lms1401s03["L120S08A.fieldTitle49"], // L120S08A.title4=貢獻度查詢期間
							width : 600,
							height : 300,
							modal : true,
							align : 'center',
							valign : 'bottom',
							i18n : i18n.def,
							buttons : {
								"sure" : function() {
									 
									if ($tL120S08Form1a.valid()) {
										if ($tL120S08Form1a.find(
														"#queryDateE1").val() < 1
												|| $tL120S08Form1a.find(
														"#queryDateE1").val() > 12) {
											CommonAPI
													.showMessage(i18n.lms1401s03["L120S08A.error07"]);
											return;
										} else if ($tL120S08Form1a.find(
														"#queryDateE0").val() <= 0) {
											CommonAPI
													.showMessage(i18n.lms1401s03["L120S08A.error08"]);
											return;
										} else {
											
											var selectKind = $tL120S08Form1a.find("#selectKind").val();
											
											$.thickbox.close();
											
											var qCustId = $L120S08Form.find("#custId").val();
											var qDupNo = $L120S08Form.find("#dupNo").val();

											$.ajax({
												handler : init120s08.fhandle,
												type : "POST",
												dataType : "json",
												data : {
													formAction : "queryL120S08BDWData_20181001",
													mainId : responseJSON.mainid,
													queryDateE0 : $tL120S08Form1a.find("#queryDateE0").val(),
													queryDateE1 : $tL120S08Form1a.find("#queryDateE1").val(),
													qCustId:qCustId,
													qDupNo:qDupNo,
													kind : selectKind
												}
											}).done(function(obj120) {
												var kindStr = "";
												if(selectKind == "1"){
													kindStr = "P";
												}else{
													kindStr = "G";
												}
												$L120S08Form.find("#halfYearDeposit"+kindStr+"_dscr").val(obj120.halfYearDeposit);
												$L120S08Form.find("#oneYearForeign"+kindStr+"_dscr").val(obj120.oneYearForeign);
												$L120S08Form.find("#oneYearWealth"+kindStr+"_dscr").val(obj120.oneYearWealth);
												$L120S08Form.find("#oneYearIncome"+kindStr+"_dscr").val(obj120.oneYearIncome);
											});
										}
									}
								},
								"cancel" : function() {
									$.thickbox.close();
								}
							}
						});

	},
	/** 計算 */
	btnCaculate_20181001 : function() {
		
		var baseRate = 0;
		var verNo = init120s08.versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);
		
		
		if (!$L120S08Form.find("#baseRate").val()) {
			// L120S08A.error12=請先輸入
			// L120S08A.baseRate=基礎放款利利率
			CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error12']
					+ i18n.lms1401s03["L120S08A.baseRate"]);
			return false;
		}
		baseRate = parseFloat($L120S08Form.find("#baseRate").val(), 10);

		var disYearRateSTot = parseFloat(0);
		var disYearRateNTot = parseFloat(0);

		// 沒有區分有擔無擔
		var lostDiffRate_disYearRate = $L120S08Form.find("#lostDiffRate_disYearRate").val();
		if (lostDiffRate_disYearRate) {
			lostDiffRate_disYearRate = parseFloat(lostDiffRate_disYearRate, 10);
		} else {
			lostDiffRate_disYearRate = 0;
			$L120S08Form.find("#lostDiffRate_disYearRate").val(0);
		}
		
		

		// 檢查有擔無擔是否要計算
		var hasS = false;
		var hasN = false;
		// 擔保利率計算(美元為利率計算)
		$("form[id='"+L120S08FormName+"'] input[id*='_disYearRateS']").each(function() {
			// $(this).attr('id') = this.id
			var disYearRateSId = this.id;
			if ($(this).val()) {
				hasS = true;
			}
		});

		// 無擔保利率計算(只有台幣要計算)
		var curr = $L120S08Form.find("#curr").val();
		if (curr != "USD") {
			$("form[id='"+L120S08FormName+"'] input[id*='_disYearRateN']").each(
					function() {
						// $(this).attr('id') = this.id
						var disYearRateNId = this.id;
						if ($(this).val()) {
							hasN = true;
						}
					});

		}

		var hasError = false;

		// 擔保利率計算(美元為利率計算)
		if (hasS) {
			// 一開始為基礎放款利利率
			disYearRateSTot = disYearRateSTot + baseRate
					+ lostDiffRate_disYearRate;
			
			 
			$("form[id='"+L120S08FormName+"'] input[id*='_disYearRateS']").each(function() {
					// $(this).attr('id') = this.id

					var disYearRateSId = this.id;
					var disYearRateS = 0;

					if ($(this).val()) {
						disYearRateS = parseFloat($(this).val(), 10);
					} else {
						$(this).val(0);
						disYearRateS = 0;
					}

					if (disYearRateSId == "reasonRate_disYearRateS"
							|| disYearRateSId == "otherAdd_disYearRateS") {
						// 合理利潤率要相加
						disYearRateSTot = disYearRateSTot
								+ disYearRateS;

					} else if (disYearRateSId == "lowestRate_disYearRateS") {
						// 本案申請利率最低不處理

					} else {
						// 借戶各項往來實績：(每項減碼≦0.25%)
						if (disYearRateSId == "halfYearDepositP_disYearRateS"
								|| disYearRateSId == "oneYearForeignP_disYearRateS"
								|| disYearRateSId == "oneYearWealthP_disYearRateS"
								|| disYearRateSId == "oneYearIncomeP_disYearRateS") {
							if (disYearRateS > 0.25) {
								hasError = true;
								return false;
							}
						}
						
						// 個集團或關係人(不含借戶)貢獻：(每項減碼≦0.2%)
						if (disYearRateSId == "halfYearDepositG_disYearRateS"
							|| disYearRateSId == "oneYearForeignG_disYearRateS"
							|| disYearRateSId == "oneYearWealthG_disYearRateS"
							|| disYearRateSId == "oneYearIncomeG_disYearRateS") {
							if (disYearRateS > 0.2) {
								hasError = true;
								return false;
							}
						}
						
						// 列舉實績優異貢獻項目申請特別減碼 (每項≦0.5%)
						if (disYearRateSId == "specialMinus_disYearRateS") {
							if (disYearRateS > 0.5) {
								hasError = true;
								return false;
							}
						}
						
						// 其他要相減
						disYearRateSTot = disYearRateSTot
								- disYearRateS;
					}
								
								
			});
			 
			$L120S08Form.find("#lowestRate_disYearRateS").val(
					disYearRateSTot.toFixed(5));
		}

		// 無擔保利率計算(只有台幣要計算)
		// 一開始為基礎放款利利率
		if (hasN) {
			disYearRateNTot = disYearRateNTot + baseRate
					+ lostDiffRate_disYearRate;
			$("form[id='"+L120S08FormName+"'] input[id*='_disYearRateN']").each(function() {
				// $(this).attr('id') = this.id

				var disYearRateNId = this.id;
				var disYearRateN = 0;

				if ($(this).val()) {
					disYearRateN = parseFloat($(this).val(), 10);
				} else {
					$(this).val(0);
					disYearRateN = 0;
				}

				if (disYearRateNId == "reasonRate_disYearRateN"
					|| disYearRateNId == "otherAdd_disYearRateN") {
					// 合理利潤率要相加
					disYearRateNTot = disYearRateNTot
							+ disYearRateN;

				} else if (disYearRateNId == "lowestRate_disYearRateN") {
					// 本案申請利率最低不處理

				} else {
					// 借戶各項往來實績：(每項減碼≦0.25%)
					if (disYearRateNId == "halfYearDepositP_disYearRateN"
						|| disYearRateNId == "oneYearForeignP_disYearRateN"
							|| disYearRateNId == "oneYearWealthP_disYearRateN"
							|| disYearRateNId == "oneYearIncomeP_disYearRateN") {
						if (disYearRateN > 0.25) {
							hasError = true;
							return false;
						}
					}
					
					// 個集團或關係人(不含借戶)貢獻：(每項減碼≦0.2%)
					if (disYearRateNId == "halfYearDepositG_disYearRateN"
						|| disYearRateNId == "oneYearForeignG_disYearRateN"
							|| disYearRateNId == "oneYearWealthG_disYearRateN"
							|| disYearRateNId == "oneYearIncomeG_disYearRateN") {
						if (disYearRateN > 0.2) {
							hasError = true;
							return false;
						}
					}
					
					// 列舉實績優異貢獻項目申請特別減碼 (每項≦0.5%)
					if (disYearRateNId == "specialMinus_disYearRateN") {
						if (disYearRateN > 0.5) {
							hasError = true;
							return false;
						}
					}
					
					// 其他要相減
					disYearRateNTot = disYearRateNTot
							- disYearRateN;

				}
			});

			$L120S08Form.find("#lowestRate_disYearRateN").val(
					disYearRateNTot.toFixed(5));
		}

		if (hasError) {
			return false;
		} else {
			return true;
		}
	},
	/** 計算 */
	btnClear_20181001 : function(sType) {
		
		var verNo = init120s08.versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);
		
		
		$("form[id='"+L120S08FormName+"'] input[id*='_disYearRate" + sType + "']")
				.each(function() {
					$(this).val('');
				});

	}

};