package com.mega.eloan.lms.base.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSOverSeaUpDWService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.dao.L140M02ADao;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKADJUSTOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKAPPLICANTOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKCNTRNOOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKCOLLOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKCREDITOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKJCICOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKPROJECTOVS;
import com.mega.eloan.lms.mfaloan.bean.OTS_RKSCOREOVS;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01B;
import com.mega.eloan.lms.model.C121M01C;
import com.mega.eloan.lms.model.C121M01D;
import com.mega.eloan.lms.model.C121M01F;
import com.mega.eloan.lms.model.C121M01G;
import com.mega.eloan.lms.model.C121M01H;
import com.mega.eloan.lms.model.C121S01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M02A;
import com.mega.eloan.lms.obsdb.service.ObsdbELF026Service;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.Util;

@Service("CLSOverSeaUpDWService")
public class CLSOverSeaUpDWServiceImpl extends AbstractCapService implements CLSOverSeaUpDWService {
	
	@Resource
	CLSService clsService;
	
	@Resource
	L140M02ADao l140m02aDao;
	
	@Resource
	ObsdbELF026Service obsdbELF026Service;
	
	@Resource
	CodeTypeService codeTypeService;
	
	private static final BigDecimal 單位_仟 = new BigDecimal("1000");
	
	/*TODO upDW_OVS_RKSCORE*/
	public List<OTS_RKSCOREOVS> upDW_OVS_RKSCORE(List<OTS_RKSCOREOVS> data,
			L120M01A l120m01a, L140M01A l140m01a, L140M01C l140m01c,
			C121M01A c121m01a, C120M01A c120m01a, Date ratingDate,
			Timestamp nowTS, Map<String, String> loanTp_actCodeMap,
			String dDOCSTATUS, GenericBean c121m01_grade) {

		OTS_RKSCOREOVS dw_rkscoreOVS = new OTS_RKSCOREOVS();
		setDW_OVS_field(dw_rkscoreOVS, l120m01a, l140m01a, l140m01c, c121m01a,
				c120m01a, ratingDate, nowTS, loanTp_actCodeMap, dDOCSTATUS);
		// ------------
		BigDecimal amtunit = BigDecimal.ONE;
		Date pr_date = null;
		BigDecimal m1_age = null;
		BigDecimal m5_occupation = null;
		BigDecimal m7_seniority = null;
		BigDecimal d1_icr = null;
		String d1_icr_na = null;
		BigDecimal p2_pincome = null;
		BigDecimal p3_hincome = null;
		BigDecimal a5_loan_period = null;
		BigDecimal o1_vedascore = null;
		BigDecimal z1 = null;
		BigDecimal z2 = null;
		BigDecimal ypay_ex_rate = null;
		BigDecimal omoney_amt_ex_rate = null;
		BigDecimal hincome_ex_rate = null;
		BigDecimal m1_score = null;
		BigDecimal m5_score = null;
		BigDecimal m7_score = null;
		BigDecimal d1_score = null;
		BigDecimal p2_score = null;
		BigDecimal p3_score = null;
		BigDecimal a5_score = null;
		BigDecimal o1_score = null;
		BigDecimal z1_score = null;
		BigDecimal z2_score = null;
		BigDecimal m1_std_score = null;
		BigDecimal m5_std_score = null;
		BigDecimal m7_std_score = null;
		BigDecimal d1_std_score = null;
		BigDecimal p2_std_score = null;
		BigDecimal p3_std_score = null;
		BigDecimal a5_std_score = null;
		BigDecimal o1_std_score = null;
		BigDecimal z1_std_score = null;
		BigDecimal z2_std_score = null;
		BigDecimal core_stdscore = null;
		BigDecimal p3_th_totincome = null;
		BigDecimal p4_th_drate = null;
		BigDecimal z3_th_security_ratio = null;
		BigDecimal p3_th_score = null;
		BigDecimal p4_th_score = null;
		BigDecimal z3_th_score = null;
		BigDecimal p3_th_std_score = null;
		BigDecimal p4_std_score = null;
		BigDecimal z3_th_std_score = null;
		
		//日本模型2.0新增欄位
		String edu = null;
		BigDecimal edu_score = null;
		BigDecimal edu_weight_score = null;
		BigDecimal drate_weight_score = null;
		BigDecimal m1_weight_score = null;
		BigDecimal m7_weight_score = null;
		BigDecimal a5_weight_score = null;
		BigDecimal m5_weight_score = null;
		BigDecimal p2_weight_score = null;
		BigDecimal z1_weight_score = null;
		BigDecimal z2_weight_score = null;
		
		//澳洲模型3.0新增欄位，部分欄位與2.0新增欄位相同
		BigDecimal p3_weight_score = null;
		String p3_hincome_curr = null;
				
		if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_日本, c121m01a.getMowType())) {
			String varVer = Util.trim(c121m01a.getVarVer());
			boolean isC121M01B = true;
			boolean is2_0 = Util.equals(varVer, OverSeaUtil.V2_0_LOAN_JP) ? true : false;
			amtunit = 單位_仟;
			if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_JP)){
				String modelType = Util.trim(l140m01c.getModelType());
				if(Util.equals(modelType, OverSeaUtil.海外評等_非房貸)){
					isC121M01B = false;
				}
			}
			
			if(isC121M01B){ //抓C121M01B
				C121M01B c121m01b = (C121M01B) c121m01_grade;
				// ----------
				pr_date = c121m01b.getCreateTime();
				m1_age = int_to_bigdecimal(c121m01b.getItem_m1());
				m5_occupation = int_to_bigdecimal(clsService.parseIntColumn(c121m01b.getItem_m5()));
				m7_seniority = c121m01b.getItem_m7();
				p2_pincome = c121m01b.getItem_p2();
				a5_loan_period = c121m01b.getItem_a5();
				z1 = int_to_bigdecimal(c121m01b.getItem_z1());
				z2 = int_to_bigdecimal(c121m01b.getItem_z2());
				ypay_ex_rate = c121m01b.getExRate_pay();
				omoney_amt_ex_rate = c121m01b.getExRate_oth();
				hincome_ex_rate = c121m01b.getExRate_hincome();
				m1_score = c121m01b.getScr_m1();
				m5_score = c121m01b.getScr_m5();
				m7_score = c121m01b.getScr_m7();
				p2_score = c121m01b.getScr_p2();
				a5_score = c121m01b.getScr_a5();
				z1_score = c121m01b.getScr_z1();
				z2_score = c121m01b.getScr_z2();
				
				if(is2_0){ //2.0要多傳一些東西
					edu = c121m01b.getItem_edu();
					edu_score = c121m01b.getScr_edu();
					p4_th_drate = c121m01b.getItem_drate();
					p4_th_score = c121m01b.getScr_drate();
					
					edu_weight_score = c121m01b.getWeight_scr_edu();
					drate_weight_score = c121m01b.getWeight_scr_drate();
					m1_weight_score = c121m01b.getWeight_scr_m1();
					m7_weight_score = c121m01b.getWeight_scr_m7();
					a5_weight_score = c121m01b.getWeight_scr_a5();
					m5_weight_score = c121m01b.getWeight_scr_m5();
					p2_weight_score = c121m01b.getWeight_scr_p2();
					z1_weight_score = c121m01b.getWeight_scr_z1();
					z2_weight_score = c121m01b.getWeight_scr_z2();
					
				}else{
					//2.0版本沒有標準化
					m1_std_score = c121m01b.getStd_m1();
					m5_std_score = c121m01b.getStd_m5();
					m7_std_score = c121m01b.getStd_m7();
					p2_std_score = c121m01b.getStd_p2();
					a5_std_score = c121m01b.getStd_a5();
					z1_std_score = c121m01b.getStd_z1();
					z2_std_score = c121m01b.getStd_z2();
					core_stdscore = c121m01b.getStd_core();
				}
				
			}else{//抓C121M01F，只有2.0項目會進這邊
				C121M01F c121m01f = (C121M01F) c121m01_grade;
				pr_date = c121m01f.getCreateTime();
				m1_age = int_to_bigdecimal(c121m01f.getItem_m1());
				m5_occupation = int_to_bigdecimal(clsService.parseIntColumn(c121m01f.getItem_m5()));
				m7_seniority = c121m01f.getItem_m7();
				p2_pincome = c121m01f.getItem_p2();
				a5_loan_period = c121m01f.getItem_a5();
				z1 = int_to_bigdecimal(c121m01f.getItem_z1());
				z2 = int_to_bigdecimal(c121m01f.getItem_z2());
				ypay_ex_rate = c121m01f.getExRate_pay();
				omoney_amt_ex_rate = c121m01f.getExRate_oth();
				hincome_ex_rate = c121m01f.getExRate_hincome();
				m1_score = c121m01f.getScr_m1();
				m5_score = c121m01f.getScr_m5();
				m7_score = c121m01f.getScr_m7();
				p2_score = c121m01f.getScr_p2();
				a5_score = c121m01f.getScr_a5();
				z1_score = c121m01f.getScr_z1();
				z2_score = c121m01f.getScr_z2();
				
				//2.0參數~~只有2.0會有機會進到這
				edu = c121m01f.getItem_edu();
				edu_score = c121m01f.getScr_edu();
				p4_th_drate = c121m01f.getItem_drate();
				p4_th_score = c121m01f.getScr_drate();
				m1_weight_score = c121m01f.getWeight_scr_m1();
				m7_weight_score = c121m01f.getWeight_scr_m7();
				a5_weight_score = c121m01f.getWeight_scr_a5();
				m5_weight_score = c121m01f.getWeight_scr_m5();
				p2_weight_score = c121m01f.getWeight_scr_p2();
				z1_weight_score = c121m01f.getWeight_scr_z1();
				z2_weight_score = c121m01f.getWeight_scr_z2();	
				edu_weight_score = c121m01f.getWeight_scr_edu();
				drate_weight_score = c121m01f.getWeight_scr_drate();
			}
			
		} else if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_澳洲,
				c121m01a.getMowType())) {
			
			String varVer = Util.trim(c121m01a.getVarVer());
			boolean isC121M01C = true;
			boolean is3_0 = Util.equals(varVer, OverSeaUtil.V3_0_LOAN_AU) ? true : false;
			amtunit = 單位_仟;
			if(Util.equals(varVer, OverSeaUtil.V3_0_LOAN_AU)){
				String modelType = Util.trim(l140m01c.getModelType());
				if(Util.equals(modelType, OverSeaUtil.海外評等_非房貸)){
					isC121M01C = false;
				}
			}
			
			if(isC121M01C){ //房貸!!
				C121M01C c121m01c = (C121M01C) c121m01_grade;
				// ----------
				amtunit = BigDecimal.ONE;
				pr_date = c121m01c.getCreateTime();
				m1_age = int_to_bigdecimal(c121m01c.getItem_m1());
				m5_occupation = int_to_bigdecimal(clsService.parseIntColumn(c121m01c.getItem_m5()));
				m7_seniority = c121m01c.getItem_m7();
				p3_hincome = c121m01c.getItem_p3();
				a5_loan_period = c121m01c.getItem_a5();
				z1 = int_to_bigdecimal(c121m01c.getItem_z1());
				z2 = int_to_bigdecimal(c121m01c.getItem_z2());
				ypay_ex_rate = c121m01c.getExRate_pay();
				omoney_amt_ex_rate = c121m01c.getExRate_oth();
				hincome_ex_rate = c121m01c.getExRate_hincome();
				m1_score = c121m01c.getScr_m1();
				m5_score = c121m01c.getScr_m5();
				m7_score = c121m01c.getScr_m7();
				p3_score = c121m01c.getScr_p3();
				a5_score = c121m01c.getScr_a5();
				z1_score = c121m01c.getScr_z1();
				z2_score = c121m01c.getScr_z2();
				
				if(is3_0){
					edu = c121m01c.getItem_edu();
					edu_score = c121m01c.getScr_edu();
					p4_th_drate = c121m01c.getItem_drate();
					p4_th_score = c121m01c.getScr_drate();
					p3_hincome_curr = c121m01c.getItem_p3_curr();
					p3_weight_score = c121m01c.getWeight_scr_p3();
					m1_weight_score = c121m01c.getWeight_scr_m1();
					m7_weight_score = c121m01c.getWeight_scr_m7();
					a5_weight_score = c121m01c.getWeight_scr_a5();
					m5_weight_score = c121m01c.getWeight_scr_m5();
					z1_weight_score = c121m01c.getWeight_scr_z1();
					z2_weight_score = c121m01c.getWeight_scr_z2();	
					edu_weight_score = c121m01c.getWeight_scr_edu();
					drate_weight_score = c121m01c.getWeight_scr_drate();
				}else{
					//3.0版本沒有D1、O1因子
					d1_icr = c121m01c.getItem_d1_icr();
					d1_icr_na = c121m01c.getItem_d1_na();
					o1_vedascore = int_to_bigdecimal(c121m01c.getItem_o1());
					d1_score = c121m01c.getScr_d1();
					o1_score = c121m01c.getScr_o1();
					//3.0版本沒有標準化
					m1_std_score = c121m01c.getStd_m1();
					m5_std_score = c121m01c.getStd_m5();
					m7_std_score = c121m01c.getStd_m7();
					d1_std_score = c121m01c.getStd_d1();
					p3_std_score = c121m01c.getStd_p3();
					a5_std_score = c121m01c.getStd_a5();
					o1_std_score = c121m01c.getStd_o1();
					z1_std_score = c121m01c.getStd_z1();
					z2_std_score = c121m01c.getStd_z2();
					core_stdscore = c121m01c.getStd_core();
				}
				
			}else{ //非房貸
				C121M01G c121m01g = (C121M01G) c121m01_grade;
				
				amtunit = BigDecimal.ONE;
				pr_date = c121m01g.getCreateTime();
				m1_age = int_to_bigdecimal(c121m01g.getItem_m1());
				m5_occupation = int_to_bigdecimal(clsService.parseIntColumn(c121m01g.getItem_m5()));
				m7_seniority = c121m01g.getItem_m7();
				p3_hincome = c121m01g.getItem_p3();
				a5_loan_period = c121m01g.getItem_a5();
				z1 = int_to_bigdecimal(c121m01g.getItem_z1());
				z2 = int_to_bigdecimal(c121m01g.getItem_z2());
				ypay_ex_rate = c121m01g.getExRate_pay();
				omoney_amt_ex_rate = c121m01g.getExRate_oth();
				hincome_ex_rate = c121m01g.getExRate_hincome();
				m1_score = c121m01g.getScr_m1();
				m5_score = c121m01g.getScr_m5();
				m7_score = c121m01g.getScr_m7();
				d1_score = c121m01g.getScr_d1();
				p3_score = c121m01g.getScr_p3();
				a5_score = c121m01g.getScr_a5();
				o1_score = c121m01g.getScr_o1();
				z1_score = c121m01g.getScr_z1();
				z2_score = c121m01g.getScr_z2();

				//3.0參數~~只有3.0會有機會進到這
				edu = c121m01g.getItem_edu();
				edu_score = c121m01g.getScr_edu();
				p4_th_drate = c121m01g.getItem_drate();
				p4_th_score = c121m01g.getScr_drate();
				p3_hincome_curr = c121m01g.getItem_p3_curr();
				p3_weight_score = c121m01g.getWeight_scr_p3();
				m1_weight_score = c121m01g.getWeight_scr_m1();
				m7_weight_score = c121m01g.getWeight_scr_m7();
				a5_weight_score = c121m01g.getWeight_scr_a5();
				m5_weight_score = c121m01g.getWeight_scr_m5();
				z1_weight_score = c121m01g.getWeight_scr_z1();
				z2_weight_score = c121m01g.getWeight_scr_z2();	
				edu_weight_score = c121m01g.getWeight_scr_edu();
				drate_weight_score = c121m01g.getWeight_scr_drate();
			}
			
		} else if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_泰國,
				c121m01a.getMowType())) {
			
			String varVer = Util.trim(c121m01a.getVarVer());
			boolean isC121M01D = true;
			boolean is2_0 = Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH) ? true : false;
			amtunit = 單位_仟;
			if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){
				String modelType = Util.trim(l140m01c.getModelType());
				if(Util.equals(modelType, OverSeaUtil.海外評等_非房貸)){
					isC121M01D = false;
				}
			}
			
			if(isC121M01D){ //房貸
				C121M01D c121m01d = (C121M01D) c121m01_grade;
				// ----------
				amtunit = BigDecimal.ONE;
				pr_date = c121m01d.getCreateTime();
				
				//1.0、2.0因子相同部分
				p2_pincome = c121m01d.getRaw_p3_idv();
				m5_occupation = int_to_bigdecimal(clsService.parseIntColumn(c121m01d.getItem_m5()));
				m7_seniority = c121m01d.getItem_m7();
				a5_loan_period = c121m01d.getItem_a5();
				z1 = int_to_bigdecimal(c121m01d.getItem_z1());
				z2 = int_to_bigdecimal(c121m01d.getItem_z2());
				ypay_ex_rate = c121m01d.getExRate_pay();
				omoney_amt_ex_rate = c121m01d.getExRate_oth();
				m5_score = c121m01d.getScr_m5();
				m7_score = c121m01d.getScr_m7();
				a5_score = c121m01d.getScr_a5();
				z1_score = c121m01d.getScr_z1();
				z2_score = c121m01d.getScr_z2();
				p3_th_totincome = c121m01d.getItem_p3();
				p4_th_drate = c121m01d.getItem_p4();
				p3_th_score = c121m01d.getScr_p3();
				p4_th_score = c121m01d.getScr_p4();
				
				if(is2_0){
					p3_hincome = c121m01d.getItem_p3();
					p3_hincome_curr = c121m01d.getItem_p3_curr();
					p3_score = c121m01d.getScr_p3();
					edu = c121m01d.getItem_edu();
					edu_score = c121m01d.getScr_edu();
					m1_age = c121m01d.getItem_m1();
					m1_score = c121m01d.getScr_m1();
					m1_weight_score = c121m01d.getWeight_scr_m1();
					m7_weight_score = c121m01d.getWeight_scr_m7();
					a5_weight_score = c121m01d.getWeight_scr_a5();
					m5_weight_score = c121m01d.getWeight_scr_m5();
					z1_weight_score = c121m01d.getWeight_scr_z1();
					z2_weight_score = c121m01d.getWeight_scr_z2();	
					p3_weight_score = c121m01d.getWeight_scr_p3();
					edu_weight_score = c121m01d.getWeight_scr_edu();
					drate_weight_score = c121m01d.getWeight_scr_p4();
				}else{
					z3_th_security_ratio = c121m01d.getItem_z3();
					z3_th_score = c121m01d.getScr_z3();
					m5_std_score = c121m01d.getStd_m5();
					m7_std_score = c121m01d.getStd_m7();
					a5_std_score = c121m01d.getStd_a5();
					z1_std_score = c121m01d.getStd_z1();
					z2_std_score = c121m01d.getStd_z2();
					p4_std_score = c121m01d.getStd_p4();
					z3_th_std_score = c121m01d.getStd_z3();
					p3_th_std_score = c121m01d.getStd_p3();
					core_stdscore = c121m01d.getStd_core();
				}
			}else{ //非房貸
				C121M01H c121m01h = (C121M01H) c121m01_grade;
				// ----------
				amtunit = BigDecimal.ONE;
				pr_date = c121m01h.getCreateTime();
				p2_pincome = c121m01h.getRaw_p3_idv();
				m5_occupation = int_to_bigdecimal(clsService.parseIntColumn(c121m01h.getItem_m5()));
				m7_seniority = c121m01h.getItem_m7();
				a5_loan_period = c121m01h.getItem_a5();
				z1 = int_to_bigdecimal(c121m01h.getItem_z1());
				z2 = int_to_bigdecimal(c121m01h.getItem_z2());
				ypay_ex_rate = c121m01h.getExRate_pay();
				omoney_amt_ex_rate = c121m01h.getExRate_oth();
				m5_score = c121m01h.getScr_m5();
				m7_score = c121m01h.getScr_m7();
				a5_score = c121m01h.getScr_a5();
				z1_score = c121m01h.getScr_z1();
				z2_score = c121m01h.getScr_z2();
				p3_th_totincome = c121m01h.getItem_p3();
				p4_th_drate = c121m01h.getItem_p4();
				p3_th_score = c121m01h.getScr_p3();
				p3_score = c121m01h.getScr_p3();
				p4_th_score = c121m01h.getScr_p4();
				
				//非房貸只會有2.0
				p3_hincome = c121m01h.getItem_p3();
				p3_hincome_curr = c121m01h.getItem_p3_curr();
				edu = c121m01h.getItem_edu();
				edu_score = c121m01h.getScr_edu();
				m1_age = c121m01h.getItem_m1();
				m1_score = c121m01h.getScr_m1();
				m1_weight_score = c121m01h.getWeight_scr_m1();
				m7_weight_score = c121m01h.getWeight_scr_m7();
				a5_weight_score = c121m01h.getWeight_scr_a5();
				m5_weight_score = c121m01h.getWeight_scr_m5();
				z1_weight_score = c121m01h.getWeight_scr_z1();
				z2_weight_score = c121m01h.getWeight_scr_z2();	
				p3_weight_score = c121m01h.getWeight_scr_p3();
				edu_weight_score = c121m01h.getWeight_scr_edu();
				drate_weight_score = c121m01h.getWeight_scr_p4();
			}
		}
		dw_rkscoreOVS.setAmtunit(amtunit);
		dw_rkscoreOVS.setPr_date(pr_date);
		dw_rkscoreOVS.setM1_age(m1_age);
		dw_rkscoreOVS.setM5_occupation(m5_occupation);
		dw_rkscoreOVS.setM7_seniority(m7_seniority);
		dw_rkscoreOVS.setD1_icr(d1_icr);
		dw_rkscoreOVS.setD1_icr_na(d1_icr_na);
		dw_rkscoreOVS.setP2_pincome(p2_pincome);
		dw_rkscoreOVS.setP3_hincome(p3_hincome);
		dw_rkscoreOVS.setA5_loan_period(a5_loan_period);
		dw_rkscoreOVS.setO1_vedascore(o1_vedascore);
		dw_rkscoreOVS.setZ1(z1);
		dw_rkscoreOVS.setZ2(z2);
		dw_rkscoreOVS.setYpay_ex_rate(ypay_ex_rate);
		dw_rkscoreOVS.setOmoney_amt_ex_rate(omoney_amt_ex_rate);
		dw_rkscoreOVS.setHincome_ex_rate(hincome_ex_rate);
		dw_rkscoreOVS.setM1_score(m1_score);
		dw_rkscoreOVS.setM5_score(m5_score);
		dw_rkscoreOVS.setM7_score(m7_score);
		dw_rkscoreOVS.setD1_score(d1_score);
		dw_rkscoreOVS.setP2_score(p2_score);
		dw_rkscoreOVS.setP3_score(p3_score);
		dw_rkscoreOVS.setA5_score(a5_score);
		dw_rkscoreOVS.setO1_score(o1_score);
		dw_rkscoreOVS.setZ1_score(z1_score);
		dw_rkscoreOVS.setZ2_score(z2_score);
		dw_rkscoreOVS.setM1_std_score(m1_std_score);
		dw_rkscoreOVS.setM5_std_score(m5_std_score);
		dw_rkscoreOVS.setM7_std_score(m7_std_score);
		dw_rkscoreOVS.setD1_std_score(d1_std_score);
		dw_rkscoreOVS.setP2_std_score(p2_std_score);
		dw_rkscoreOVS.setP3_std_score(p3_std_score);
		dw_rkscoreOVS.setA5_std_score(a5_std_score);
		dw_rkscoreOVS.setO1_std_score(o1_std_score);
		dw_rkscoreOVS.setZ1_std_score(z1_std_score);
		dw_rkscoreOVS.setZ2_std_score(z2_std_score);
		dw_rkscoreOVS.setCore_stdscore(core_stdscore);
		dw_rkscoreOVS.setP3_th_totincome(p3_th_totincome);
		dw_rkscoreOVS.setP4_th_drate(p4_th_drate); //日本模型2.0-個人負債比因子，也用這欄位
		dw_rkscoreOVS.setZ3_th_security_ratio(z3_th_security_ratio);
		dw_rkscoreOVS.setP3_th_score(p3_th_score);
		dw_rkscoreOVS.setP4_th_score(p4_th_score); //日本模型2.0-個人負債比分數，也用這欄位
		dw_rkscoreOVS.setZ3_th_score(z3_th_score);
		dw_rkscoreOVS.setP3_th_std_score(p3_th_std_score);
		dw_rkscoreOVS.setP4_std_score(p4_std_score);
		dw_rkscoreOVS.setZ3_th_std_score(z3_th_std_score);
		
		//日本模型2.0新增項目
		dw_rkscoreOVS.setEdu_item(edu);
		dw_rkscoreOVS.setEdu_score(edu_score);
		dw_rkscoreOVS.setEdu_weight_score(edu_weight_score);
		dw_rkscoreOVS.setDrate_weight_score(drate_weight_score);
		dw_rkscoreOVS.setM1_weight_score(m1_weight_score);
		dw_rkscoreOVS.setM7_weight_score(m7_weight_score);
		dw_rkscoreOVS.setA5_weight_score(a5_weight_score);
		dw_rkscoreOVS.setM5_weight_score(m5_weight_score);
		dw_rkscoreOVS.setP2_weight_score(p2_weight_score);
		dw_rkscoreOVS.setZ1_weight_score(z1_weight_score);
		dw_rkscoreOVS.setZ2_weight_score(z2_weight_score);
		
		//澳洲模型3.0新增項目(部分新欄位與日本模型2.0相同)
		dw_rkscoreOVS.setP3_hincome_curr(p3_hincome_curr);
		dw_rkscoreOVS.setP3_weight_score(p3_weight_score);
		// ------------
		data.add(dw_rkscoreOVS);
		return data;
	}
	
	/*TODO upDW_OVS_RKCREDIT*/
	public List<OTS_RKCREDITOVS> upDW_OVS_RKCREDIT(List<OTS_RKCREDITOVS> data,
			L120M01A l120m01a, L140M01A l140m01a, L140M01C l140m01c,
			C121M01A c121m01a, C121M01A c121m01a_docStatus05O,
			C120M01A c120m01a, C120S01E c120s01e, Date ratingDate,
			Timestamp nowTS, Map<String, String> loanTp_actCodeMap,
			String dDOCSTATUS, GenericBean c121m01_grade) {

		OTS_RKCREDITOVS dw_rkcreditOVS = new OTS_RKCREDITOVS();
		setDW_OVS_field(dw_rkcreditOVS, l120m01a, l140m01a, l140m01c, c121m01a,
				c120m01a, ratingDate, nowTS, loanTp_actCodeMap, dDOCSTATUS);
		dw_rkcreditOVS.setLoan_period(OverSeaUtil.yearmonth_toYear(OverSeaUtil
				.get_total_month(l140m01c)));
		dw_rkcreditOVS.setCntrno(l140m01a.getCntrNo());

		Integer pr = null;
		Integer sr = null;
		String jcic_warning_flag = "N";
		String final_rating_flag = "N";
		if (Util.equals(LMSUtil.getCustKey_len10custId(
				l140m01c.getC121CustId(), l140m01c.getC121DupNo()), LMSUtil
				.getCustKey_len10custId(c120m01a.getCustId(),
						c120m01a.getDupNo()))) {
			final_rating_flag = "Y";
		}
		String j10_score_flag = null;
		Integer j10_score = null;
		Integer spr = null;
		Integer fr = null;
		String updater = null;
		Timestamp tmestamp = null;
		BigDecimal core_score = null;
		BigDecimal core_stdscore = null;
		Integer jspts = null;
		Integer gws_dg = null;
		Integer sws_dg = null;
		Integer oi_ug = null;
		Integer jr_autodg = null;
		Integer adj_rating = null;
		BigDecimal dr = null;
		BigDecimal dr_1yr = null;
		String ncb_report_flag = "";
		Integer sws_rating_cap = null;
		String borrower_no_ncb = "";
		Integer ui_dg = null;
		//2.0新增
		BigDecimal predict_bad_rate = null;
		BigDecimal slope = null;
		BigDecimal intercept = null;
		
		
		// ------------
		if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_日本, c121m01a.getMowType())) {
			String varVer = Util.trim(c121m01a.getVarVer());
			boolean isC121M01B = true;
			boolean is2_0 = Util.equals(varVer, OverSeaUtil.V2_0_LOAN_JP) ? true : false;
			if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_JP)){
				String modelType = Util.trim(l140m01c.getModelType());
				if(Util.equals(modelType, OverSeaUtil.海外評等_非房貸)){
					isC121M01B = false;
				}
			}
			if(isC121M01B){ //抓C121M01B
				C121M01B c121m01b = (C121M01B) c121m01_grade;
				// ----------
				pr = parseIntColumnWithDF(c121m01b.getPRating());
				sr = parseIntColumnWithDF(c121m01b.getSRating());
				if (true) {
					List<String> jcic_warning_flagColl = new ArrayList<String>();
					if (true) {
						jcic_warning_flagColl.add(c121m01b.getChkItem1());
						jcic_warning_flagColl.add(c121m01b.getChkItem2());
						jcic_warning_flagColl.add(c121m01b.getChkItem9());
						jcic_warning_flagColl.add(c121m01b.getChkItem11());
						jcic_warning_flagColl.add(c121m01b.getChkItem12());
						jcic_warning_flagColl.add(c121m01b.getChkItem4());
						jcic_warning_flagColl.add(c121m01b.getChkItem5());
						jcic_warning_flagColl.add(c121m01b.getChkItem10());
						jcic_warning_flagColl.add(c121m01b.getChkItem7());
						jcic_warning_flagColl.add(c121m01b.getChkItem13());
					}

					if (has_jcic_warning_flag(jcic_warning_flagColl)) {
						jcic_warning_flag = "Y";
					}
				}

				j10_score_flag = c121m01b.getJ10_score_flag();
				j10_score = c121m01b.getJ10_score();
				spr = parseIntColumnWithDF(c121m01b.getSprtRating());
				fr = parseIntColumnWithDF(c121m01b.getFRating());
				updater = c121m01b.getUpdater();
				tmestamp = c121m01b.getUpdateTime();
				core_score = c121m01b.getScr_core();
				jspts = c121m01b.getSumRiskPt();
				gws_dg = c121m01b.getAdj_pts();
				jr_autodg = c121m01b.getAdj_j10();
				if(is2_0){ //2.0版本
					predict_bad_rate = c121m01b.getPd();
					slope = c121m01b.getSlope();
					intercept = c121m01b.getInterCept();
					dr_1yr = c121m01b.getDr_1yr();
				}else{
					core_stdscore = c121m01b.getStd_core();
					adj_rating = ots_adj_rating(c121m01b);
					dr = c121m01b.getDr_3yr();
					dr_1yr = c121m01b.getDr_1yr();
				}
			}else{ //抓C121M01F(2.0 非房貸)
				C121M01F c121m01f = (C121M01F) c121m01_grade;
				
				pr = parseIntColumnWithDF(c121m01f.getPRating());
				sr = parseIntColumnWithDF(c121m01f.getSRating());
				if (true) {
					List<String> jcic_warning_flagColl = new ArrayList<String>();
					if (true) {
						jcic_warning_flagColl.add(c121m01f.getChkItem1());
						jcic_warning_flagColl.add(c121m01f.getChkItem2());
						jcic_warning_flagColl.add(c121m01f.getChkItem9());
						jcic_warning_flagColl.add(c121m01f.getChkItem11());
						jcic_warning_flagColl.add(c121m01f.getChkItem12());
						jcic_warning_flagColl.add(c121m01f.getChkItem4());
						jcic_warning_flagColl.add(c121m01f.getChkItem5());
						jcic_warning_flagColl.add(c121m01f.getChkItem10());
						jcic_warning_flagColl.add(c121m01f.getChkItem7());
						jcic_warning_flagColl.add(c121m01f.getChkItem13());
					}

					if (has_jcic_warning_flag(jcic_warning_flagColl)) {
						jcic_warning_flag = "Y";
					}
				}

				j10_score_flag = c121m01f.getJ10_score_flag();
				j10_score = c121m01f.getJ10_score();
				spr = parseIntColumnWithDF(c121m01f.getSprtRating());
				fr = parseIntColumnWithDF(c121m01f.getFRating());
				updater = c121m01f.getUpdater();
				tmestamp = c121m01f.getUpdateTime();
				core_score = c121m01f.getScr_core();
				predict_bad_rate = c121m01f.getPd();
				slope = c121m01f.getSlope();
				intercept = c121m01f.getInterCept();
				dr_1yr = c121m01f.getDr_1yr();
				jspts = c121m01f.getSumRiskPt();
				gws_dg = c121m01f.getAdj_pts();
				jr_autodg = c121m01f.getAdj_j10();

			}
		} else if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_澳洲,
				c121m01a.getMowType())) {
			String varVer = Util.trim(c121m01a.getVarVer());
			boolean isC121M01C = true;
			boolean is3_0 = Util.equals(varVer, OverSeaUtil.V3_0_LOAN_AU) ? true : false;
			if(Util.equals(varVer, OverSeaUtil.V3_0_LOAN_AU)){
				String modelType = Util.trim(l140m01c.getModelType());
				if(Util.equals(modelType, OverSeaUtil.海外評等_非房貸)){
					isC121M01C = false;
				}
			}
			
			if(isC121M01C){ //房貸!!
				C121M01C c121m01c = (C121M01C) c121m01_grade;
				// ----------
				pr = clsService.parseIntColumn(c121m01c.getPRating());
				sr = clsService.parseIntColumn(c121m01c.getSRating());
				if (true) {
					List<String> jcic_warning_flagColl = new ArrayList<String>();
					if (true) {
						jcic_warning_flagColl.add(c121m01c.getChkItemAUG1());
						jcic_warning_flagColl.add(c121m01c.getChkItemAUG2());
						jcic_warning_flagColl.add(c121m01c.getChkItemAUG3());
						jcic_warning_flagColl.add(c121m01c.getChkItemAUS1());
						jcic_warning_flagColl.add(c121m01c.getChkItemAUS2());
					}
					if (has_jcic_warning_flag(jcic_warning_flagColl)) {
						jcic_warning_flag = "Y";
					}
				}
				spr = parseIntColumnWithDF(c121m01c.getSprtRating());
				fr = parseIntColumnWithDF(c121m01c.getFRating());
				updater = c121m01c.getUpdater();
				tmestamp = c121m01c.getUpdateTime();
				core_score = c121m01c.getScr_core();
				
				jspts = c121m01c.getSumRiskPt();
				gws_dg = c121m01c.getAdj_pts();
				sws_dg = c121m01c.getAdj_sw();
				oi_ug = c121m01c.getAdj_oi();
				
				if(is3_0){ //3.0版本
					predict_bad_rate = c121m01c.getPd();
					slope = c121m01c.getSlope();
					intercept = c121m01c.getInterCept();
					dr_1yr = c121m01c.getDr_1yr();
				}else{
					core_stdscore = c121m01c.getStd_core();
					adj_rating = ots_adj_rating(c121m01c);
					dr = c121m01c.getDr_3yr();
					dr_1yr = c121m01c.getDr_1yr();
				}
			}else{ //非房貸!!
				C121M01G c121m01g = (C121M01G) c121m01_grade;
				// ---------
				pr = clsService.parseIntColumn(c121m01g.getPRating());
				sr = clsService.parseIntColumn(c121m01g.getSRating());
				if (true) {
					List<String> jcic_warning_flagColl = new ArrayList<String>();
					if (true) {
						jcic_warning_flagColl.add(c121m01g.getChkItemAUG1());
						jcic_warning_flagColl.add(c121m01g.getChkItemAUG2());
						jcic_warning_flagColl.add(c121m01g.getChkItemAUG3());
						jcic_warning_flagColl.add(c121m01g.getChkItemAUS1());
						jcic_warning_flagColl.add(c121m01g.getChkItemAUS2());
					}
					if (has_jcic_warning_flag(jcic_warning_flagColl)) {
						jcic_warning_flag = "Y";
					}
				}
				spr = parseIntColumnWithDF(c121m01g.getSprtRating());
				fr = parseIntColumnWithDF(c121m01g.getFRating());
				updater = c121m01g.getUpdater();
				tmestamp = c121m01g.getUpdateTime();
				core_score = c121m01g.getScr_core();
				jspts = c121m01g.getSumRiskPt();
				gws_dg = c121m01g.getAdj_pts();
				sws_dg = c121m01g.getAdj_sw();
				oi_ug = c121m01g.getAdj_oi();
				predict_bad_rate = c121m01g.getPd();
				slope = c121m01g.getSlope();
				intercept = c121m01g.getInterCept();
				dr_1yr = c121m01g.getDr_1yr();
			}

		} else if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_泰國,
				c121m01a.getMowType())) {
			
			String varVer = Util.trim(c121m01a.getVarVer());
			boolean isC121M01D = true;
			boolean is2_0 = Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH) ? true : false;
			if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){
				String modelType = Util.trim(l140m01c.getModelType());
				if(Util.equals(modelType, OverSeaUtil.海外評等_非房貸)){
					isC121M01D = false;
				}
			}
			
			if(isC121M01D){
				C121M01D c121m01d = (C121M01D) c121m01_grade;
				// ----------
				pr = clsService.parseIntColumn(c121m01d.getPRating());
				sr = clsService.parseIntColumn(c121m01d.getSRating());
				if (true) {
					List<String> jcic_warning_flagColl = new ArrayList<String>();
					if (true) {
						// 係處理(有NCB報告) & (一般警訊 OR 特殊警訊有觸動)者
						jcic_warning_flagColl.add(c121m01d.getChkItemTHG1());
						jcic_warning_flagColl.add(c121m01d.getChkItemTHG2());
						jcic_warning_flagColl.add(c121m01d.getChkItemTHS1());
					}
					if (has_jcic_warning_flag(jcic_warning_flagColl)) {
						jcic_warning_flag = "Y";
					}
				}

				spr = parseIntColumnWithDF(c121m01d.getSprtRating());
				fr = parseIntColumnWithDF(c121m01d.getFRating());
				updater = c121m01d.getUpdater();
				tmestamp = c121m01d.getUpdateTime();

				core_score = c121m01d.getScr_core();
				jspts = c121m01d.getSumRiskPt();
				gws_dg = c121m01d.getAdj_pts();
				sws_dg = this.sws_dg(c121m01d.getAdj_matchS(), pr);
				ncb_report_flag = this.ncb_report_flag(c120s01e.getNcbRecord());
				sws_rating_cap = this.sws_rating_cap(c121m01d.getAdj_matchS());
				borrower_no_ncb = Util.equals(c121m01d.getChkItemTHO3(),
						UtilConstants.haveNo.有) ? "Y" : "N";
				ui_dg = c121m01d.getAdj_ui();
				
				if(is2_0){ //2.0版本
					predict_bad_rate = c121m01d.getPd();
					slope = c121m01d.getSlope();
					intercept = c121m01d.getInterCept();
					dr_1yr = c121m01d.getDr_1yr();
				}else{
					core_stdscore = c121m01d.getStd_core();
					adj_rating = ots_adj_rating(c121m01d);
					dr = c121m01d.getDr_3yr();
					dr_1yr = c121m01d.getDr_1yr();
				}	
			}else{
				C121M01H c121m01h = (C121M01H) c121m01_grade;
				// ----------
				pr = clsService.parseIntColumn(c121m01h.getPRating());
				sr = clsService.parseIntColumn(c121m01h.getSRating());
				if (true) {
					List<String> jcic_warning_flagColl = new ArrayList<String>();
					if (true) {
						// 係處理(有NCB報告) & (一般警訊 OR 特殊警訊有觸動)者
						jcic_warning_flagColl.add(c121m01h.getChkItemTHG1());
						jcic_warning_flagColl.add(c121m01h.getChkItemTHG2());
						jcic_warning_flagColl.add(c121m01h.getChkItemTHS1());
					}
					if (has_jcic_warning_flag(jcic_warning_flagColl)) {
						jcic_warning_flag = "Y";
					}
				}

				spr = parseIntColumnWithDF(c121m01h.getSprtRating());
				fr = parseIntColumnWithDF(c121m01h.getFRating());
				updater = c121m01h.getUpdater();
				tmestamp = c121m01h.getUpdateTime();

				core_score = c121m01h.getScr_core();
				jspts = c121m01h.getSumRiskPt();
				gws_dg = c121m01h.getAdj_pts();
				sws_dg = this.sws_dg(c121m01h.getAdj_matchS(), pr);
				ncb_report_flag = this.ncb_report_flag(c120s01e.getNcbRecord());
				sws_rating_cap = this.sws_rating_cap(c121m01h.getAdj_matchS());
				borrower_no_ncb = Util.equals(c121m01h.getChkItemTHO3(),
						UtilConstants.haveNo.有) ? "Y" : "N";
				ui_dg = c121m01h.getAdj_ui();
				
				predict_bad_rate = c121m01h.getPd();
				slope = c121m01h.getSlope();
				intercept = c121m01h.getInterCept();
				dr_1yr = c121m01h.getDr_1yr();
			}	
			
		}
		// ------------
		dw_rkcreditOVS.setPr(pr);
		dw_rkcreditOVS.setSr(sr);
		dw_rkcreditOVS.setJcic_warning_flag(jcic_warning_flag);
		dw_rkcreditOVS.setFinal_rating_flag(final_rating_flag);
		dw_rkcreditOVS.setJ10_score_flag(j10_score_flag);
		dw_rkcreditOVS.setJ10_score(j10_score);
		dw_rkcreditOVS.setSpr(spr);
		dw_rkcreditOVS.setFr(fr);
		dw_rkcreditOVS.setUpdater(updater);
		dw_rkcreditOVS.setTmestamp(tmestamp);
		if (c121m01a_docStatus05O != null) {
			dw_rkcreditOVS.setChkdate(c121m01a_docStatus05O.getApproveTime());
			dw_rkcreditOVS.setChkempno(c121m01a_docStatus05O.getApprover());
			dw_rkcreditOVS.setCreatedt(c121m01a_docStatus05O.getCreateTime());
			dw_rkcreditOVS.setUpdatedt(c121m01a_docStatus05O.getUpdateTime());
		} else {
			// 當 docStatus=ZZZ，表示被引入至簽報書的copy版本
			dw_rkcreditOVS.setChkdate(c121m01a.getApproveTime());
			dw_rkcreditOVS.setChkempno(c121m01a.getApprover());
			dw_rkcreditOVS.setCreatedt(c121m01a.getCreateTime());
			dw_rkcreditOVS.setUpdatedt(c121m01a.getUpdateTime());
		}
		dw_rkcreditOVS.setCore_score(core_score);
		dw_rkcreditOVS.setCore_stdscore(core_stdscore);
		dw_rkcreditOVS.setJspts(jspts);
		dw_rkcreditOVS.setGws_dg(gws_dg);
		dw_rkcreditOVS.setSws_dg(sws_dg);
		dw_rkcreditOVS.setOi_ug(oi_ug);
		dw_rkcreditOVS.setJr_autodg(jr_autodg);
		dw_rkcreditOVS.setAdj_rating(adj_rating);
		dw_rkcreditOVS.setDr(dr);
		dw_rkcreditOVS.setDr_1yr(dr_1yr);
		dw_rkcreditOVS.setNcb_report_flag(ncb_report_flag);
		dw_rkcreditOVS.setSws_rating_cap(sws_rating_cap);
		dw_rkcreditOVS.setBorrower_no_ncb(borrower_no_ncb);
		dw_rkcreditOVS.setUi_dg(ui_dg);
		
		//日本模型2.0新增欄位
		dw_rkcreditOVS.setPredict_bad_rate(predict_bad_rate);
		dw_rkcreditOVS.setSlope(slope);
		dw_rkcreditOVS.setIntercept(intercept);

		data.add(dw_rkcreditOVS);
		return data;
	}
	
	/*TODO upDW_OVS_RKJCIC*/
	public List<OTS_RKJCICOVS> upDW_OVS_RKJCIC(List<OTS_RKJCICOVS> data,
			L120M01A l120m01a, L140M01A l140m01a, L140M01C l140m01c,
			C121M01A c121m01a, C120M01A c120m01a, C120S01E c120s01e,
			Date ratingDate, Timestamp nowTS,
			Map<String, String> loanTp_actCodeMap, String dDOCSTATUS,
			GenericBean c121m01_grade) {

		OTS_RKJCICOVS dw_rkjcicOVS = new OTS_RKJCICOVS();
		setDW_OVS_field(dw_rkjcicOVS, l120m01a, l140m01a, l140m01c, c121m01a,
				c120m01a, ratingDate, nowTS, loanTp_actCodeMap, dDOCSTATUS);
		// ------------
		if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_日本, c121m01a.getMowType())) {
			String modelTType = Util.trim(l140m01c.getModelType());
			if(Util.equals(modelTType, OverSeaUtil.海外評等_非房貸)){
				C121M01F c121m01f = (C121M01F) c121m01_grade;
				// 日本負面資訊第1項
				dw_rkjcicOVS.setEver_bad_check(c121m01f.getChkItem1a());
				dw_rkjcicOVS.setReject_yn(c121m01f.getChkItem1b());
				dw_rkjcicOVS.setInq_date(c121m01f.getJcicQDate());
				dw_rkjcicOVS.setCredit_force_stop(c121m01f.getChkItem1c());
				dw_rkjcicOVS.setBad_debt(c121m01f.getChkItem1d());
				// 日本負面資訊第2項
				dw_rkjcicOVS.setNego_lawbankoth(c121m01f.getChkItem2());
				// 日本負面資訊第3項
				dw_rkjcicOVS.setLn12_pay_delay_times(c121m01f.getChkItem9());
				String j10_score_flag = Util.trim(c121m01f.getJ10_score_flag());
				if (Util.equals("NA", j10_score_flag)) {
					j10_score_flag = "";
				}
				dw_rkjcicOVS.setJ10_score_flag(j10_score_flag);
				dw_rkjcicOVS.setJ10_score(c121m01f.getJ10_score());
			}else{
				C121M01B c121m01b = (C121M01B) c121m01_grade;
				// 日本負面資訊第1項
				dw_rkjcicOVS.setEver_bad_check(c121m01b.getChkItem1a());
				dw_rkjcicOVS.setReject_yn(c121m01b.getChkItem1b());
				dw_rkjcicOVS.setInq_date(c121m01b.getJcicQDate());
				dw_rkjcicOVS.setCredit_force_stop(c121m01b.getChkItem1c());
				dw_rkjcicOVS.setBad_debt(c121m01b.getChkItem1d());
				// 日本負面資訊第2項
				dw_rkjcicOVS.setNego_lawbankoth(c121m01b.getChkItem2());
				// 日本負面資訊第3項
				dw_rkjcicOVS.setLn12_pay_delay_times(c121m01b.getChkItem9());
				String j10_score_flag = Util.trim(c121m01b.getJ10_score_flag());
				if (Util.equals("NA", j10_score_flag)) {
					j10_score_flag = "";
				}
				dw_rkjcicOVS.setJ10_score_flag(j10_score_flag);
				dw_rkjcicOVS.setJ10_score(c121m01b.getJ10_score());
			}
			
			// ----------
			// 日本負面資訊第1項
			dw_rkjcicOVS.setCheck_qdate(c120s01e.getEChkQDate());
			dw_rkjcicOVS.setEnd_date(c120s01e.getEChkDDate());
			// 日本負面資訊第4項
			dw_rkjcicOVS.setCash_amt_flag(c120s01e.getIsQdata25());
			dw_rkjcicOVS.setCash_amt(c120s01e.getBalQdata25());
			// 日本負面資訊第5項
			dw_rkjcicOVS.setNos_amt_flag(c120s01e.getIsQdata26());
			dw_rkjcicOVS.setNos_amt(c120s01e.getBalQdata26());
			dw_rkjcicOVS.setSl_amt_flag(c120s01e.getIsQdata28());
			dw_rkjcicOVS.setSl_amt(c120s01e.getBalQdata28());
			// 日本負面資訊第6項
			dw_rkjcicOVS.setCc12_revol_pay_delay_times_flag(c120s01e
					.getIsQdata21());
			dw_rkjcicOVS
					.setCc12_revol_pay_delay_times(c120s01e.getCntQdata21());
			// 日本負面資訊第7項
			dw_rkjcicOVS.setCc12_minpay_delay_times_flag(c120s01e
					.getIsQdata22());
			dw_rkjcicOVS.setCc12_minpay_delay_times(c120s01e.getCntQdata22());
			// 日本負面資訊第8項
			dw_rkjcicOVS.setCc12_totpay_delay_times_flag(c120s01e
					.getIsQdata23());
			dw_rkjcicOVS.setCc12_totpay_delay_times(c120s01e.getCntQdata23());
			// 日本負面資訊第9項
			dw_rkjcicOVS.setCc12_cash_adv_times_flag(c120s01e.getIsQdata24());
			dw_rkjcicOVS.setCc12_cash_adv_times(c120s01e.getCntQdata24());
			// 日本負面資訊第10項
			dw_rkjcicOVS.setInq3_napp_bank_flag(c120s01e.getIsQdata27());
			dw_rkjcicOVS.setInq3_napp_bank(c120s01e.getCntQdata27());
		} else if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_澳洲,
				c121m01a.getMowType())) {
			// C121M01C c121m01c = (C121M01C)c121m01_grade;
			// ----------
			dw_rkjcicOVS.setEver_bad_check(Util.trim(c120s01e.getIsQdata9()));
			dw_rkjcicOVS.setReject_yn(Util.trim(c120s01e.getIsQdata10()));
			dw_rkjcicOVS.setInq_date(c120s01e.getVedaQDate());
			dw_rkjcicOVS.setCheck_qdate(c120s01e.getEChkQDate());
			dw_rkjcicOVS.setEnd_date(c120s01e.getEChkDDate());
			dw_rkjcicOVS.setCredit_force_stop(c120s01e.getIsQdata13());
			dw_rkjcicOVS.setBad_debt(c120s01e.getIsQdata11());

			dw_rkjcicOVS.setVeda_adverse_file(c120s01e.getVedaAdverseFile());
			dw_rkjcicOVS.setVeda_default_outstanding(c120s01e
					.getVedaDefaultAmt());
			dw_rkjcicOVS.setVeda_judgement(c120s01e.getVedaJudgement());
			dw_rkjcicOVS.setVeda_credit_enquiries_flag(c120s01e
					.getVedaEnquiriesFlag());
			dw_rkjcicOVS.setVeda_credit_enquiries(c120s01e
					.getVedaEnquiriesTimes());
			dw_rkjcicOVS.setVeda_credit_provider(c120s01e.getVedaProvider());
			dw_rkjcicOVS.setVeda_credit_file_age_flag(c120s01e
					.getVedaFileAgeFlag());
			dw_rkjcicOVS.setVeda_credit_file_age(c120s01e.getVedaFileAge());
		} else if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_泰國,
				c121m01a.getMowType())) {
			// C121M01D c121m01d = (C121M01D)c121m01_grade;
			// ----------
			dw_rkjcicOVS.setNcb_over_90_days_flag(c120s01e
					.getNcbOver90DaysFlag());
			dw_rkjcicOVS.setNcb_over_90_days(c120s01e.getNcbOver90Days());
			dw_rkjcicOVS.setNcb_maximum_dpd_flag(c120s01e
					.getNcbMaximumDpdFlag());
			dw_rkjcicOVS.setNcb_maximum_dpd(c120s01e.getNcbMaximumDpd());
			dw_rkjcicOVS.setNcb_enq_recent_6m_flag(c120s01e
					.getNcbEnqRecent6MFlag());
			dw_rkjcicOVS.setNcb_enq_recent_6m(c120s01e.getNcbEnqRecent6M());
		}
		// ------------
		data.add(dw_rkjcicOVS);
		return data;
	}
	
	
	/*TODO upDW_OVS_RKADJUST*/
	public List<OTS_RKADJUSTOVS> upDW_OVS_RKADJUST(List<OTS_RKADJUSTOVS> data,
			L120M01A l120m01a, L140M01A l140m01a, L140M01C l140m01c,
			C121M01A c121m01a, C120M01A c120m01a, Date ratingDate,
			Timestamp nowTS, Map<String, String> loanTp_actCodeMap,
			String dDOCSTATUS, GenericBean c121m01_grade) {

		OTS_RKADJUSTOVS dw_rkadjustOVS = new OTS_RKADJUSTOVS();
		setDW_OVS_field(dw_rkadjustOVS, l120m01a, l140m01a, l140m01c, c121m01a,
				c120m01a, ratingDate, nowTS, loanTp_actCodeMap, dDOCSTATUS);
		// ------------
		// 上傳DW_RKADJUST的理由，DW已放大到和 e-Loan 相同
		// 不要 call Util.trimSizeInOS390 ，會多截字

		String upgrade_reason_flag = "";
		String upgrade_reason_text = "";
		String downgrade_reason_text = "";
		Integer adj_rating = null;

		if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_日本, c121m01a.getMowType())) {
			String modelTType = Util.trim(l140m01c.getModelType());
			if(Util.equals(modelTType, OverSeaUtil.海外評等_非房貸)){
				C121M01F c121m01f = (C121M01F) c121m01_grade;
				// ----------
				String reason = Util.trim(c121m01f.getAdjustReason());
				if (Util.equals("1", c121m01f.getAdjustStatus())) {
					upgrade_reason_flag = Util.trim(c121m01f.getAdjustFlag());
					upgrade_reason_text = reason;
				} else if (Util.equals("2", c121m01f.getAdjustStatus())) {
					downgrade_reason_text = reason;
				}
				adj_rating = ots_adj_rating(c121m01f);
			}else{
				C121M01B c121m01b = (C121M01B) c121m01_grade;
				// ----------
				String reason = Util.trim(c121m01b.getAdjustReason());
				if (Util.equals("1", c121m01b.getAdjustStatus())) {
					upgrade_reason_flag = Util.trim(c121m01b.getAdjustFlag());
					upgrade_reason_text = reason;
				} else if (Util.equals("2", c121m01b.getAdjustStatus())) {
					downgrade_reason_text = reason;
				}
				adj_rating = ots_adj_rating(c121m01b);
			}
		} else if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_澳洲,
				c121m01a.getMowType())) {
			String modelTType = Util.trim(l140m01c.getModelType());
			if(Util.equals(modelTType, OverSeaUtil.海外評等_非房貸)){
				C121M01G c121m01g = (C121M01G) c121m01_grade;
				// ----------
				String reason = Util.trim(c121m01g.getAdjustReason());

				if (Util.equals("1", c121m01g.getAdjustStatus())) {
					upgrade_reason_flag = Util.trim(c121m01g.getAdjustFlag());
					upgrade_reason_text = reason;
				} else if (Util.equals("2", c121m01g.getAdjustStatus())) {
					downgrade_reason_text = reason;
				}
				adj_rating = ots_adj_rating(c121m01g);
			}else{
				C121M01C c121m01c = (C121M01C) c121m01_grade;
				// ----------
				String reason = Util.trim(c121m01c.getAdjustReason());

				if (Util.equals("1", c121m01c.getAdjustStatus())) {
					upgrade_reason_flag = Util.trim(c121m01c.getAdjustFlag());
					upgrade_reason_text = reason;
				} else if (Util.equals("2", c121m01c.getAdjustStatus())) {
					downgrade_reason_text = reason;
				}
				adj_rating = ots_adj_rating(c121m01c);
			}
		} else if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_泰國,
				c121m01a.getMowType())) {
			String modelTType = Util.trim(l140m01c.getModelType());
			if(Util.equals(modelTType, OverSeaUtil.海外評等_非房貸)){
				C121M01H c121m01h = (C121M01H) c121m01_grade;
				// ----------
				String reason = Util.trim(c121m01h.getAdjustReason());

				if (Util.equals("1", c121m01h.getAdjustStatus())) {
					upgrade_reason_flag = Util.trim(c121m01h.getAdjustFlag());
					upgrade_reason_text = reason;
				} else if (Util.equals("2", c121m01h.getAdjustStatus())) {
					downgrade_reason_text = reason;
				}
				adj_rating = ots_adj_rating(c121m01h);
			}else{
				C121M01D c121m01d = (C121M01D) c121m01_grade;
				// ----------
				String reason = Util.trim(c121m01d.getAdjustReason());

				if (Util.equals("1", c121m01d.getAdjustStatus())) {
					upgrade_reason_flag = Util.trim(c121m01d.getAdjustFlag());
					upgrade_reason_text = reason;
				} else if (Util.equals("2", c121m01d.getAdjustStatus())) {
					downgrade_reason_text = reason;
				}
				adj_rating = ots_adj_rating(c121m01d);
			}
		}
		dw_rkadjustOVS.setUpgrade_reason_flag(upgrade_reason_flag);
		dw_rkadjustOVS.setUpgrade_reason_text(upgrade_reason_text);
		dw_rkadjustOVS.setDowngrade_reason_text(downgrade_reason_text);
		dw_rkadjustOVS.setAdj_rating(adj_rating);
		// ------------
		data.add(dw_rkadjustOVS);
		return data;
	}
	
	
	/*TODO upDW_OVS_RKAPPLICANT*/
	public List<OTS_RKAPPLICANTOVS> upDW_OVS_RKAPPLICANT(
			List<OTS_RKAPPLICANTOVS> data, L120M01A l120m01a,
			L140M01A l140m01a, L140M01C l140m01c, C121M01A c121m01a,
			C120M01A c120m01a, C120S01A c120s01a, C120S01B c120s01b,
			C120S01C c120s01c, Date ratingDate, Timestamp nowTS,
			Map<String, String> loanTp_actCodeMap, String dDOCSTATUS,
			GenericBean c121m01_grade) {

		OTS_RKAPPLICANTOVS dw_rkapplicantOVS = new OTS_RKAPPLICANTOVS();
		setDW_OVS_field(dw_rkapplicantOVS, l120m01a, l140m01a, l140m01c,
				c121m01a, c120m01a, ratingDate, nowTS, loanTp_actCodeMap,
				dDOCSTATUS);
		// ------------
		BigDecimal amtunit = BigDecimal.ONE;

		BigDecimal YPAY_EX_RATE = null;
		BigDecimal OMONEY_AMT_EX_RATE = null;
		BigDecimal HINCOME_EX_RATE = null;
		BigDecimal RINCOME_EX_RATE = null;
		BigDecimal INVMBAL_EX_RATE = null;
		BigDecimal INVOBAL_EX_RATE = null;
		BigDecimal ODEP_EX_RATE = null;
		BigDecimal TOTMMIN_EX_RATE = null;
		BigDecimal TOTMMEXP_EX_RATE = null;
		if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_日本, c121m01a.getMowType())) {
			amtunit = 單位_仟;
			String modelTType = Util.trim(l140m01c.getModelType());
			if(Util.equals(modelTType, OverSeaUtil.海外評等_非房貸)){
				C121M01F c121m01f = (C121M01F) c121m01_grade;
				// --------
				YPAY_EX_RATE = c121m01f.getExRate_pay();
				OMONEY_AMT_EX_RATE = c121m01f.getExRate_oth();
				HINCOME_EX_RATE = c121m01f.getExRate_hincome();
				RINCOME_EX_RATE = c121m01f.getExRate_rincome();
				INVMBAL_EX_RATE = c121m01f.getExRate_invMBal();
				INVOBAL_EX_RATE = c121m01f.getExRate_invOBal();
				ODEP_EX_RATE = c121m01f.getExRate_branAmt();
			}else{
				C121M01B c121m01b = (C121M01B) c121m01_grade;
				// --------
				YPAY_EX_RATE = c121m01b.getExRate_pay();
				OMONEY_AMT_EX_RATE = c121m01b.getExRate_oth();
				HINCOME_EX_RATE = c121m01b.getExRate_hincome();
				RINCOME_EX_RATE = c121m01b.getExRate_rincome();
				INVMBAL_EX_RATE = c121m01b.getExRate_invMBal();
				INVOBAL_EX_RATE = c121m01b.getExRate_invOBal();
				ODEP_EX_RATE = c121m01b.getExRate_branAmt();
			}	
		} else if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_澳洲,
				c121m01a.getMowType())) {
			amtunit = BigDecimal.ONE;
			String modelTType = Util.trim(l140m01c.getModelType());
			if(Util.equals(modelTType, OverSeaUtil.海外評等_非房貸)){
				C121M01G c121m01g = (C121M01G) c121m01_grade;
				// --------
				YPAY_EX_RATE = c121m01g.getExRate_pay();
				OMONEY_AMT_EX_RATE = c121m01g.getExRate_oth();
				HINCOME_EX_RATE = c121m01g.getExRate_hincome();
				RINCOME_EX_RATE = c121m01g.getExRate_rincome();
				INVMBAL_EX_RATE = c121m01g.getExRate_invMBal();
				INVOBAL_EX_RATE = c121m01g.getExRate_invOBal();
				ODEP_EX_RATE = c121m01g.getExRate_branAmt();
			}else{
				C121M01C c121m01c = (C121M01C) c121m01_grade;
				// --------
				YPAY_EX_RATE = c121m01c.getExRate_pay();
				OMONEY_AMT_EX_RATE = c121m01c.getExRate_oth();
				HINCOME_EX_RATE = c121m01c.getExRate_hincome();
				RINCOME_EX_RATE = c121m01c.getExRate_rincome();
				INVMBAL_EX_RATE = c121m01c.getExRate_invMBal();
				INVOBAL_EX_RATE = c121m01c.getExRate_invOBal();
				ODEP_EX_RATE = c121m01c.getExRate_branAmt();
			}
			
		} else if (Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_泰國,
				c121m01a.getMowType())) {
			String modelTType = Util.trim(l140m01c.getModelType());
			if(Util.equals(modelTType, OverSeaUtil.海外評等_非房貸)){
				C121M01H c121m01h = (C121M01H) c121m01_grade;
				// --------
				amtunit = BigDecimal.ONE;
				YPAY_EX_RATE = c121m01h.getExRate_pay();
				OMONEY_AMT_EX_RATE = c121m01h.getExRate_oth();
				HINCOME_EX_RATE = c121m01h.getExRate_hincome();
				// RINCOME_EX_RATE
				INVMBAL_EX_RATE = c121m01h.getExRate_invMBal();
				INVOBAL_EX_RATE = c121m01h.getExRate_invOBal();
				ODEP_EX_RATE = c121m01h.getExRate_branAmt();
				TOTMMIN_EX_RATE = c121m01h.getExRate_totMmIn();
				TOTMMEXP_EX_RATE = c121m01h.getExRate_totMmExp();
			}else{
				C121M01D c121m01d = (C121M01D) c121m01_grade;
				// --------
				amtunit = BigDecimal.ONE;
				YPAY_EX_RATE = c121m01d.getExRate_pay();
				OMONEY_AMT_EX_RATE = c121m01d.getExRate_oth();
				HINCOME_EX_RATE = c121m01d.getExRate_hincome();
				// RINCOME_EX_RATE
				INVMBAL_EX_RATE = c121m01d.getExRate_invMBal();
				INVOBAL_EX_RATE = c121m01d.getExRate_invOBal();
				ODEP_EX_RATE = c121m01d.getExRate_branAmt();
				TOTMMIN_EX_RATE = c121m01d.getExRate_totMmIn();
				TOTMMEXP_EX_RATE = c121m01d.getExRate_totMmExp();
			}
		}

		dw_rkapplicantOVS.setDob(c120s01a.getBirthday());
		dw_rkapplicantOVS.setEducation(clsService.convert_dw_edu(c120s01a));
		dw_rkapplicantOVS.setMarriage(Util.parseInt(Util.trim(c120s01a
				.getMarry())));
		dw_rkapplicantOVS.setChildren(c120s01a.getChild());
		dw_rkapplicantOVS.setSeniority(c120s01b.getSeniority());
		String POS = Util.trim(c120s01b.getJobType1())
				+ Util.trim(c120s01b.getJobType2())
				+ Util.trim(c120s01b.getJobTitle());
		dw_rkapplicantOVS.setPos(POS);
		dw_rkapplicantOVS.setAmtunit(amtunit);

		dw_rkapplicantOVS
				.setYpay(ots_amt_byUnit(c120s01b.getPayAmt(), amtunit));
		dw_rkapplicantOVS.setYpay_swft(c120s01b.getPayCurr());
		dw_rkapplicantOVS.setYpay_ex_rate(YPAY_EX_RATE);

		dw_rkapplicantOVS.setOmoney(c120s01c.getOIncome());
		dw_rkapplicantOVS.setOmoney_amt(ots_amt_byUnit(c120s01c.getOMoneyAmt(),
				amtunit));
		dw_rkapplicantOVS
				.setOmoney_amt_swft(Util.trim(c120s01c.getOMoneyCurr()));
		dw_rkapplicantOVS.setOmoney_amt_ex_rate(OMONEY_AMT_EX_RATE);

		BigDecimal hincome = ots_amt_byUnit(c120s01c.getYFamAmt(), amtunit);
		if (hincome != null) {
			String maxHINCOME = "9999999999";
			if (CapMath.compare(hincome, maxHINCOME) > 0) {
				hincome = CapMath.getBigDecimal(maxHINCOME);
			}
		}
		dw_rkapplicantOVS.setHincome(hincome);
		dw_rkapplicantOVS.setHincome_swft(Util.trim(c120s01c.getYFamCurr()));
		dw_rkapplicantOVS.setHincome_ex_rate(HINCOME_EX_RATE);

		dw_rkapplicantOVS.setRincome(ots_amt_byUnit(
				c120s01c.getRealEstateRentIncomeAmt(), amtunit));
		dw_rkapplicantOVS.setRincome_swft(Util.trim(c120s01c
				.getRealEstateRentIncomeCurr()));
		dw_rkapplicantOVS.setRincome_ex_rate(RINCOME_EX_RATE);

		dw_rkapplicantOVS.setDrate(c120s01c.getDRate());
		dw_rkapplicantOVS.setYrate(c120s01c.getYRate());
		dw_rkapplicantOVS.setCertificate(Util.trim(c120s01b.getInDoc()));
		dw_rkapplicantOVS.setHcertificate(Util.trim(c120s01c.getYIncomeCert()));

		String DBCREDIT = Util.trim(c120s01c.getCredit());
		if (Util.equals(DBCREDIT, "A|B") || Util.equals(DBCREDIT, "AB")) {
			DBCREDIT = "C";
		}
		dw_rkapplicantOVS.setDbcredit(DBCREDIT);
		dw_rkapplicantOVS.setIspfund(Util.trim(c120s01c.getIsPeriodFund()));
		dw_rkapplicantOVS.setObusiness(clsService._convertBusi(c120s01c.getBusi()));

		dw_rkapplicantOVS.setInvmbal(ots_amt_byUnit(c120s01c.getInvMBalAmt(),
				amtunit));
		dw_rkapplicantOVS.setInvmbal_swft(Util.trim(c120s01c.getInvMBalCurr()));
		dw_rkapplicantOVS.setInvmbal_ex_rate(INVMBAL_EX_RATE);

		dw_rkapplicantOVS.setInvobal(ots_amt_byUnit(c120s01c.getInvOBalAmt(),
				amtunit));
		dw_rkapplicantOVS.setInvobal_swft(Util.trim(c120s01c.getInvOBalCurr()));
		dw_rkapplicantOVS.setInvobal_ex_rate(INVOBAL_EX_RATE);

		dw_rkapplicantOVS
				.setOdep(ots_amt_byUnit(c120s01c.getBranAmt(), amtunit));
		dw_rkapplicantOVS.setOdep_swft(Util.trim(c120s01c.getBranCurr()));
		dw_rkapplicantOVS.setOdep_ex_rate(ODEP_EX_RATE);

		dw_rkapplicantOVS.setCmsstatus(Util.trim(c120s01a.getCmsStatus()));

		dw_rkapplicantOVS
				.setLocal_risk_rating(Util.trim(c120s01a.getO_grade()));
		dw_rkapplicantOVS.setMonth_total_income(ots_amt_byUnit(
				c120s01c.getTotMmInAmt(), amtunit));
		dw_rkapplicantOVS.setMonth_tot_income_swft(Util.trim(c120s01c
				.getTotMmInCurr()));
		dw_rkapplicantOVS.setMonth_tot_income_ex_rate(TOTMMIN_EX_RATE);
		dw_rkapplicantOVS.setMonth_total_expense(ots_amt_byUnit(
				c120s01c.getTotMmExpAmt(), amtunit));
		dw_rkapplicantOVS.setMonth_tot_expense_swft(Util.trim(c120s01c
				.getTotMmExpCurr()));
		dw_rkapplicantOVS.setMonth_tot_expense_ex_rate(TOTMMEXP_EX_RATE);
		// ~~~~~~
		data.add(dw_rkapplicantOVS);
		return data;
	}
	
	
	
	/*TODO upDW_OVS_RKCOLL*/
	public List<OTS_RKCOLLOVS> upDW_OVS_RKCOLL(List<OTS_RKCOLLOVS> data,
			L120M01A l120m01a, L140M01A l140m01a, L140M01C l140m01c,
			C121M01A c121m01a, C120M01A c120m01a, Date ratingDate,
			Timestamp nowTS, Map<String, String> loanTp_actCodeMap,
			String dDOCSTATUS, C121S01A c121s01a) {

		OTS_RKCOLLOVS dw_rkcollOVS = new OTS_RKCOLLOVS();
		setDW_OVS_field(dw_rkcollOVS, l120m01a, l140m01a, l140m01c, c121m01a,
				c120m01a, ratingDate, nowTS, loanTp_actCodeMap, dDOCSTATUS);
		// ------------
		dw_rkcollOVS.setColl_flag(c121s01a.getCmsType());
		dw_rkcollOVS.setColl_addr(c121s01a.getLocation());
		dw_rkcollOVS.setColl_area(c121s01a.getRegion());
		dw_rkcollOVS.setHouse_age(c121s01a.getHouseAge());
		dw_rkcollOVS.setBuild_area(c121s01a.getHouseArea());
		dw_rkcollOVS.setSecurity_rate(c121s01a.getSecurityRate());
		dw_rkcollOVS.setColl_usage(c121s01a.getCollUsage());
		dw_rkcollOVS.setLocationType(c121s01a.getLocationType());
		// ------------
		data.add(dw_rkcollOVS);
		return data;
	}
	
	
	
	
	
	
	/*TODO upDW_OVS_RKPROJECT CLSService >> 14433 >> verify_C121M01A >> 沒有傳這個 */
	public List<OTS_RKPROJECTOVS> upDW_OVS_RKPROJECT(
			List<OTS_RKPROJECTOVS> data, L120M01A l120m01a, L140M01A l140m01a,
			L140M01C l140m01c, C121M01A c121m01a, C120M01A c120m01a,
			Date ratingDate, Timestamp nowTS,
			Map<String, String> loanTp_actCodeMap, String dDOCSTATUS) {

		OTS_RKPROJECTOVS dw_rkprojectOVS = new OTS_RKPROJECTOVS();
		setDW_OVS_field(dw_rkprojectOVS, l120m01a, l140m01a, l140m01c,
				c121m01a, c120m01a, ratingDate, nowTS, loanTp_actCodeMap,
				dDOCSTATUS);
		// ------------
		L140M02A l140m02a = l140m02aDao.findByUniqueKey(l140m01a.getMainId());
		if (l140m02a == null) {
			l140m02a = new L140M02A();
		}

		String REASON_FLAG = Util.trim(l120m01a.getCaseLvlReason());
		if (Util.equals(REASON_FLAG, "7")) {
			REASON_FLAG = "6";
		}

		String UPDATE_AMT_FLAG = "N";
		String UPDATE_RATE_FLAG = "N";
		String[] temps = Util.trim(l140m02a.getJsonData()).split(
				UtilConstants.Mark.SPILT_MARK);
		for (String name : temps) {
			if (UtilConstants.L140M02AName.現請額度.equals(name)) {
				UPDATE_AMT_FLAG = "Y";
			}
			if (UtilConstants.L140M02AName.利率條件.equals(name)) {
				UPDATE_RATE_FLAG = "Y";
			}
		}
		dw_rkprojectOVS.setCase_level(Util.trim(l120m01a.getCaseLvl()));
		dw_rkprojectOVS.setReason_flag(REASON_FLAG);
		dw_rkprojectOVS.setAuth_flag(Util.equals(
				Util.trim(l120m01a.getDocRslt()), "1") ? "Y" : "N");
		dw_rkprojectOVS.setReject_flag(Util.trim(l140m01a.getCesRjtCause()));
		dw_rkprojectOVS.setUpdate_amt_flag(UPDATE_AMT_FLAG);
		dw_rkprojectOVS.setUpdate_rate_flag(UPDATE_RATE_FLAG);
		dw_rkprojectOVS.setChkdate(l120m01a.getEndDate());
		// ------------
		data.add(dw_rkprojectOVS);
		return data;
	}
	
	/*TODO upDW_OVS_RKCNTRNOOVS CLSService >> 14433 >> verify_C121M01A >> 沒有傳這個 */
	public List<OTS_RKCNTRNOOVS> upDW_OVS_RKCNTRNO(List<OTS_RKCNTRNOOVS> data,
			L120M01A l120m01a, L140M01A l140m01a, L140M01C l140m01c,
			C121M01A c121m01a, C120M01A c120m01a, Date ratingDate,
			Timestamp nowTS, Map<String, String> loanTp_actCodeMap,
			String dDOCSTATUS) {

		OTS_RKCNTRNOOVS dw_rkcntrnoOVS = new OTS_RKCNTRNOOVS();
		setDW_OVS_field(dw_rkcntrnoOVS, l120m01a, l140m01a, l140m01c, c121m01a,
				c120m01a, ratingDate, nowTS, loanTp_actCodeMap, dDOCSTATUS);
		// ------------
		dw_rkcntrnoOVS.setCntrno(l140m01a.getCntrNo());
		dw_rkcntrnoOVS.setLoancurr(l140m01a.getCurrentApplyCurr());
		dw_rkcntrnoOVS.setLoanamt(l140m01a.getCurrentApplyAmt());
		dw_rkcntrnoOVS.setLoan_period(OverSeaUtil.yearmonth_toYear(OverSeaUtil
				.get_total_month(l140m01c)));
		dw_rkcntrnoOVS.setS_flag(l140m01a.getSbjProperty());
		// ------------
		data.add(dw_rkcntrnoOVS);
		return data;
	}
	
	
	//TODO 基礎數值INIT
	private void setDW_OVS_field(GenericBean bean, L120M01A l120m01a,
			L140M01A l140m01a, L140M01C l140m01c, C121M01A c121m01a,
			C120M01A c120m01a, Date ratingDate, Timestamp nowTS,
			Map<String, String> loanTp_actCodeMap, String dDOCSTATUS) {
		String BR_CD = Util.trim(l140m01a.getOwnBrId());
		String NOTEID = Util.trim(l140m01a.getMainId());
		Date RATING_DATE = ratingDate;
		if (RATING_DATE == null) {
			RATING_DATE = CapDate.parseDate(CapDate.ZERO_DATE);
		}
		String RATING_ID = Util.trim(c121m01a.getRatingId());
		String CUSTID = Util.trim(c120m01a.getCustId());
		String DUPNO = Util.trim(c120m01a.getDupNo());
		String CUST_KEY = Util.trim(l120m01a.getCustId());
		String LOAN_CODE = Util.trim(l140m01c.getLoanTP());
		String SUBJCODE = "";
		if (true) {
			SUBJCODE = LMSUtil.getDesc(loanTp_actCodeMap, LOAN_CODE);
		}
		String MOWTYPE = Util.trim(c121m01a.getMowType());
		String varVer = Util.trim(c121m01a.getVarVer());
		
		//配合新模型調整，優先抓c121m01a，若c121m01a無資料，改抓l120m01a
		String MOWTYPE_COUNTRY = Util.trim(c121m01a.getMowTypeCountry()); //國家別代碼
		if(Util.isEmpty(MOWTYPE_COUNTRY)){
			MOWTYPE_COUNTRY = Util.trim(l120m01a.getRatingFlag()); //國家別代碼
		}
		
		String MOWTYPE2 = null;
		if((Util.equals(MOWTYPE, OverSeaUtil.C121M01A_MOW_TYPE_日本) && Util.equals(varVer, OverSeaUtil.V2_0_LOAN_JP))
				|| ((Util.equals(MOWTYPE, OverSeaUtil.C121M01A_MOW_TYPE_澳洲) && Util.equals(varVer, OverSeaUtil.V3_0_LOAN_AU)))
				|| ((Util.equals(MOWTYPE, OverSeaUtil.C121M01A_MOW_TYPE_泰國) && Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)))){
			MOWTYPE2=l140m01c.getModelType(); //房貸/非房貸型別
		}
		int MOWVER1 = 0;
		int MOWVER2 = 0;
		if (true) {
			String[] varVerArr = Util.trim(c121m01a.getVarVer()).split("\\.");
			MOWVER1 = Util.parseInt(varVerArr[0]);
			MOWVER2 = Util.parseInt(varVerArr[1]);
		}
		String LNGEFLAG = Util.equals("Y", c120m01a.getKeyMan()) ? "M" : Util
				.trim(c120m01a.getCustPos());

		String DOCSTATUS = ""; // dDOCSTATUS 若傳入 66，代表註記為刪除案件
		if (Util.isNotEmpty(Util.trim(dDOCSTATUS))) {
			DOCSTATUS = dDOCSTATUS;
		} else {
			DOCSTATUS = clsService._dwDOCSTATUS(Util.trim(l120m01a.getDocStatus()));
		}
		// =====================
		if (bean instanceof OTS_RKADJUSTOVS) {
			OTS_RKADJUSTOVS o = (OTS_RKADJUSTOVS) bean;
			o.setBr_cd(BR_CD);
			o.setNoteid(NOTEID);
			o.setRating_date(RATING_DATE);
			o.setRating_id(RATING_ID);
			o.setCustid(CUSTID);
			o.setDupno(DUPNO);
			o.setCust_key(CUST_KEY);
			o.setLoan_code(LOAN_CODE);
			o.setMowtype(MOWTYPE);
			o.setMowtype2(MOWTYPE2);
			o.setMowtype_country(MOWTYPE_COUNTRY);
			o.setMowver1(MOWVER1);
			o.setMowver2(MOWVER2);
			o.setSubjcode(SUBJCODE);
			o.setLngeflag(LNGEFLAG);
			o.setDocstatus(DOCSTATUS);
			o.setData_src_dt(nowTS);
		} else if (bean instanceof OTS_RKAPPLICANTOVS) {
			OTS_RKAPPLICANTOVS o = (OTS_RKAPPLICANTOVS) bean;
			o.setBr_cd(BR_CD);
			o.setNoteid(NOTEID);
			o.setRating_date(RATING_DATE);
			o.setRating_id(RATING_ID);
			o.setCustid(CUSTID);
			o.setDupno(DUPNO);
			o.setCust_key(CUST_KEY);
			o.setLoan_code(LOAN_CODE);
			o.setMowtype(MOWTYPE);
			o.setMowtype2(MOWTYPE2);
			o.setMowtype_country(MOWTYPE_COUNTRY);
			o.setMowver1(MOWVER1);
			o.setMowver2(MOWVER2);
			o.setSubjcode(SUBJCODE);
			o.setLngeflag(LNGEFLAG);
			o.setDocstatus(DOCSTATUS);
			o.setData_src_dt(nowTS);
		} else if (bean instanceof OTS_RKCNTRNOOVS) {
			OTS_RKCNTRNOOVS o = (OTS_RKCNTRNOOVS) bean;
			o.setBr_cd(BR_CD);
			o.setNoteid(NOTEID);
			o.setRating_date(RATING_DATE);
			o.setRating_id(RATING_ID);
			o.setCustid(CUSTID);
			o.setDupno(DUPNO);
			o.setCust_key(CUST_KEY);
			o.setLoan_code(LOAN_CODE);
			o.setMowtype(MOWTYPE);
			o.setMowtype2(MOWTYPE2);
			o.setMowtype_country(MOWTYPE_COUNTRY);
			o.setMowver1(MOWVER1);
			o.setMowver2(MOWVER2);
			o.setSubjcode(SUBJCODE);
			o.setLngeflag(LNGEFLAG);
			o.setDocstatus(DOCSTATUS);
			o.setData_src_dt(nowTS);
		} else if (bean instanceof OTS_RKCOLLOVS) {
			OTS_RKCOLLOVS o = (OTS_RKCOLLOVS) bean;
			o.setBr_cd(BR_CD);
			o.setNoteid(NOTEID);
			o.setRating_date(RATING_DATE);
			o.setRating_id(RATING_ID);
			o.setCustid(CUSTID);
			o.setDupno(DUPNO);
			o.setCust_key(CUST_KEY);
			o.setLoan_code(LOAN_CODE);
			o.setMowtype(MOWTYPE);
			o.setMowtype2(MOWTYPE2);
			o.setMowtype_country(MOWTYPE_COUNTRY);
			o.setMowver1(MOWVER1);
			o.setMowver2(MOWVER2);
			o.setSubjcode(SUBJCODE);
			o.setLngeflag(LNGEFLAG);
			o.setDocstatus(DOCSTATUS);
			o.setData_src_dt(nowTS);
		} else if (bean instanceof OTS_RKCREDITOVS) {
			OTS_RKCREDITOVS o = (OTS_RKCREDITOVS) bean;
			o.setBr_cd(BR_CD);
			o.setNoteid(NOTEID);
			o.setRating_date(RATING_DATE);
			o.setRating_id(RATING_ID);
			o.setCustid(CUSTID);
			o.setDupno(DUPNO);
			o.setCust_key(CUST_KEY);
			o.setLoan_code(LOAN_CODE);
			o.setMowtype(MOWTYPE);
			o.setMowtype2(MOWTYPE2);
			o.setMowtype_country(MOWTYPE_COUNTRY);
			o.setMowver1(MOWVER1);
			o.setMowver2(MOWVER2);
			o.setSubjcode(SUBJCODE);
			o.setLngeflag(LNGEFLAG);
			o.setDocstatus(DOCSTATUS);
			o.setData_src_dt(nowTS);
		} else if (bean instanceof OTS_RKJCICOVS) {
			OTS_RKJCICOVS o = (OTS_RKJCICOVS) bean;
			o.setBr_cd(BR_CD);
			o.setNoteid(NOTEID);
			o.setRating_date(RATING_DATE);
			o.setRating_id(RATING_ID);
			o.setCustid(CUSTID);
			o.setDupno(DUPNO);
			o.setCust_key(CUST_KEY);
			o.setLoan_code(LOAN_CODE);
			o.setMowtype(MOWTYPE);
			o.setMowtype2(MOWTYPE2);
			o.setMowtype_country(MOWTYPE_COUNTRY);
			o.setMowver1(MOWVER1);
			o.setMowver2(MOWVER2);
			o.setSubjcode(SUBJCODE);
			o.setLngeflag(LNGEFLAG);
			o.setDocstatus(DOCSTATUS);
			o.setData_src_dt(nowTS);
		} else if (bean instanceof OTS_RKPROJECTOVS) {
			OTS_RKPROJECTOVS o = (OTS_RKPROJECTOVS) bean;
			o.setBr_cd(BR_CD);
			o.setNoteid(NOTEID);
			o.setRating_date(RATING_DATE);
			o.setRating_id(RATING_ID);
			o.setCustid(CUSTID);
			o.setDupno(DUPNO);
			o.setCust_key(CUST_KEY);
			o.setLoan_code(LOAN_CODE);
			o.setMowtype(MOWTYPE);
			o.setMowtype2(MOWTYPE2);
			o.setMowtype_country(MOWTYPE_COUNTRY);
			o.setMowver1(MOWVER1);
			o.setMowver2(MOWVER2);
			o.setSubjcode(SUBJCODE);
			o.setLngeflag(LNGEFLAG);
			o.setDocstatus(DOCSTATUS);
			o.setData_src_dt(nowTS);
		} else if (bean instanceof OTS_RKSCOREOVS) {
			OTS_RKSCOREOVS o = (OTS_RKSCOREOVS) bean;
			o.setBr_cd(BR_CD);
			o.setNoteid(NOTEID);
			o.setRating_date(RATING_DATE);
			o.setRating_id(RATING_ID);
			o.setCustid(CUSTID);
			o.setDupno(DUPNO);
			o.setCust_key(CUST_KEY);
			o.setLoan_code(LOAN_CODE);
			o.setMowtype(MOWTYPE);
			o.setMowtype2(MOWTYPE2);
			o.setMowtype_country(MOWTYPE_COUNTRY);
			o.setMowver1(MOWVER1);
			o.setMowver2(MOWVER2);
			o.setSubjcode(SUBJCODE);
			o.setLngeflag(LNGEFLAG);
			o.setDocstatus(DOCSTATUS);
			o.setData_src_dt(nowTS);
		}
	}
	
	
	//TODO updateELF026
	public boolean updateELF026Flag() {
		//是否上傳AS400 ELF026
		boolean updateELF026Flag = false;
		//取得上傳AS400起日(COM.BCODETYPE >> TH_UpLoadAS400)
		String startDate = "9999-01-01";
		CodeType updateELF026 = codeTypeService.findByCodeTypeAndCodeValue("TH_UpLoadAS400", "", "zh_TW");
		if(updateELF026 != null){
			startDate = updateELF026.getCodeDesc2();
		}
		if(LMSUtil.cmpDate(new Date(), ">=", CapDate.parseDate(startDate))){
			updateELF026Flag = true;
		}
		
		return updateELF026Flag;
	}
	
	/*TODO updateELF026*/
	public void updateELF026(
			L120M01A l120m01a, L140M01A l140m01a, L140M01C l140m01c, 
			C121M01A c121m01a, C121M01A c121m01a_docStatus05O,
			C120M01A c120m01a, Date ratingDate, Timestamp nowTS,
			Map<String, String> loanTp_actCodeMap, 
			GenericBean c121m01_grade) {

		String modelTType = Util.trim(l140m01c.getModelType());
		int fr = 0;
		BigDecimal loan_period = new BigDecimal("0");
		if(Util.equals(modelTType, OverSeaUtil.海外評等_非房貸)){ //新版模型才會出現非房貸註記
			C121M01H c121m01h = (C121M01H) c121m01_grade;
			fr = parseIntColumnWithDF(c121m01h.getFRating());
			loan_period = c121m01h.getItem_a5();
		}else{
			C121M01D c121m01d = (C121M01D) c121m01_grade;
			fr = parseIntColumnWithDF(c121m01d.getFRating());
			loan_period = c121m01d.getItem_a5();
		}
		
		String br_cd = Util.trim(l140m01a.getOwnBrId());
		String cust_key = Util.trim(l120m01a.getCustId());
		String loan_code = Util.trim(l140m01c.getLoanTP());
		String subjcode = "";
		if (true) {
			subjcode = LMSUtil.getDesc(loanTp_actCodeMap, loan_code);
		}
		String cntrno = Util.trim(l140m01a.getCntrNo());
		
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		Timestamp chkdate = null;
		if (c121m01a_docStatus05O != null) {
			chkdate = c121m01a_docStatus05O.getApproveTime();
		} else {
			// 當 docStatus=ZZZ，表示被引入至簽報書的copy版本
			chkdate = c121m01a.getApproveTime();
		}
		int chkdateInt = Integer.parseInt(sdf.format(chkdate));
		
		String custid = Util.trim(c120m01a.getCustId());
		String dupno = Util.trim(c120m01a.getDupNo());
		String noteid = Util.trim(l140m01a.getMainId());
		
		String final_rating_flag = "N";
		if (Util.equals(LMSUtil.getCustKey_len10custId(
				l140m01c.getC121CustId(), l140m01c.getC121DupNo()), LMSUtil
				.getCustKey_len10custId(c120m01a.getCustId(),
						c120m01a.getDupNo()))) {
			final_rating_flag = "Y";
		}
		
		int rating_date = Integer.parseInt(sdf.format(ratingDate));
		String rating_id = Util.trim(c121m01a.getRatingId());
		String mowtype = Util.trim(c121m01a.getMowType());
		
		int mowver1 = 0;
		int mowver2 = 0;
		if (true) {
			String[] varVerArr = Util.trim(c121m01a.getVarVer()).split("\\.");
			mowver1 = Util.parseInt(varVerArr[0]);
			mowver2 = Util.parseInt(varVerArr[1]);
		}
		
		
		//若同一筆資料於同一天上傳兩次，會產生KEY值重複的錯誤，因此先針對同KEY的資料進行刪除
		obsdbELF026Service.delByKey(br_cd, cust_key, subjcode, cntrno, chkdateInt, custid, dupno, noteid);
		
		//定義好資料 >> 上傳AS400
		obsdbELF026Service.insertForInside(br_cd, cust_key, subjcode, cntrno, chkdateInt, custid, 
				dupno, noteid, final_rating_flag, fr, rating_date, 
				rating_id, loan_code, mowtype, mowver1, mowver2, loan_period);
		
	}
	
	
	private BigDecimal int_to_bigdecimal(Integer v) {
		if (v == null) {
			return null;
		}
		return BigDecimal.valueOf(v);
	}
	
	private Integer parseIntColumnWithDF(String rating) {
		if (rating == null) {
			return null;
		}
		if (Util.equals(OverSeaUtil.DF, rating)) {
			return 99;
		}
		return Util.parseInt(rating);
	}
	
	private Integer bigdecimal_to_int(BigDecimal loan_period){
		if(loan_period == null){
			return 0;
		}
		String aa = loan_period.toString();
		int bb = Integer.parseInt(aa);
		return Integer.parseInt(aa);
	}
	
	private int ots_adj_rating(C121M01B c121m01_grade) {
		return inner_ots_adj_rating(c121m01_grade.getAdjustStatus(),
				c121m01_grade.getAdjRating());
	}
	private int ots_adj_rating(C121M01F c121m01_grade) {
		return inner_ots_adj_rating(c121m01_grade.getAdjustStatus(),
				c121m01_grade.getAdjRating());
	}

	private int ots_adj_rating(C121M01C c121m01_grade) {
		return inner_ots_adj_rating(c121m01_grade.getAdjustStatus(),
				c121m01_grade.getAdjRating());
	}
	private int ots_adj_rating(C121M01G c121m01_grade) {
		return inner_ots_adj_rating(c121m01_grade.getAdjustStatus(),
				c121m01_grade.getAdjRating());
	}

	private int ots_adj_rating(C121M01D c121m01_grade) {
		return inner_ots_adj_rating(c121m01_grade.getAdjustStatus(),
				c121m01_grade.getAdjRating());
	}
	
	private int ots_adj_rating(C121M01H c121m01_grade) {
		return inner_ots_adj_rating(c121m01_grade.getAdjustStatus(),
				c121m01_grade.getAdjRating());
	}
	
	private int inner_ots_adj_rating(String adjustStatus, String adjRating) {
		int val = Util.parseInt(adjRating);
		if (Util.equals("2", adjustStatus)) {// 調降
			return val * -1;
		} else {
			return val;
		}
	}
	
	private boolean has_jcic_warning_flag(
			Collection<String> jcic_warning_flagColl) {
		for (String chkItem : jcic_warning_flagColl) {
			if (Util.equals(chkItem, "Y") || Util.equals(chkItem, "1")) {
				return true;
			}
		}
		return false;
	}
	
	private BigDecimal ots_amt_byUnit(BigDecimal v, BigDecimal amtunit) {
		if (v == null) {
			return null;
		}
		int scale = 0;
		return Arithmetic.div(v, amtunit, scale);
	}

	private String ncb_report_flag(String NcbRecord){
		if (Util.equals("1", NcbRecord)) {
			return "Y";
		} else if (Util.equals("2", NcbRecord)) {
			return "N";
		} else {
			return Util.trim(NcbRecord);
		}
	}
	
	private Integer sws_rating_cap(String Adj_matchS){
		if (Util.equals("Y", Adj_matchS)) { // 特殊警訊評等上限
			return 9;
		} else {
			return 1;
		}
	}
	
	private Integer sws_dg(String Adj_matchS, Integer pr){
		if (Util.equals("Y", Adj_matchS)) { // 特殊警訊降等數
			/*
			 * c120s01e.ncbMaximumDpd, 最大逾期天數區間{0:未逾期,1:逾期30天內
			 * ,2:逾期31-90天,3:超過90天} 當值=1，觸動 chkItemTHG1(一般警訊1) 當值=2，觸動
			 * chkItemTHG2(一般警訊2) 當值=3，觸動 chkItemTHS1(特殊警訊) => 因互斥，當
			 * Adj_matchS=Y, 表示 c120s01e.ncbMaximumDpd=3, 不可能觸動 "一般警訊"
			 */
			int tmp_val = OverSeaUtil.rating_min9_max10(pr);
			return (pr - tmp_val);
		} else {
			return 0;
		}
	}
	
}
