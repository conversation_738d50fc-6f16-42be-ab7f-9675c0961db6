/* 
 * CLS1161S01Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import java.util.HashMap;

import javax.annotation.Resource;

import org.springframework.ui.ModelMap;

import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.jcs.common.Util;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.L120M01A;

/**
 * <pre>
 * 動用審核表 - 基本資訊
 * </pre>
 * 
 * @since 2012/12/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/25,Fantasy,new
 *          </ul>
 */
public class CLS1161S01Panel extends Panel {
	
	private String caseType;
	
	private String newCustFlag;

	public CLS1161S01Panel(String id) {
		super(id);
	}

	public CLS1161S01Panel(String id, boolean updatePanelName, String caseType, String newCustFlag) {
		super(id, updatePanelName);
		this.caseType = caseType;
		this.newCustFlag = newCustFlag;
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		new DocLogPanel("_docLog").processPanelData(model, params);

		// UPGRADE: 前端須配合改Thymeleaf的樣式
//		add(new Label("_btCaseType1")
//				.setVisible(UtilConstants.Usedoc.caseType2.一般.equals(caseType)));
//		add(new Label("_btCaseType2")
//				.setVisible(UtilConstants.Usedoc.caseType2.團貸.equals(caseType)));
//		add(new Label("_groupLoanInfo")
//				.setVisible(UtilConstants.Usedoc.caseType2.團貸.equals(caseType)));
//		add(new Label("_btCaseType3")
//				.setVisible(UtilConstants.Usedoc.caseType2.整批匯入
//						.equals(caseType)));
//		add(new Label("_excelInfo")
//				.setVisible(UtilConstants.Usedoc.caseType2.整批匯入
//						.equals(caseType)));
		model.addAttribute("_btCaseType1_visible", UtilConstants.Usedoc.caseType2.一般.equals(caseType));
		model.addAttribute("_btCaseType2_visible", UtilConstants.Usedoc.caseType2.團貸.equals(caseType));
		model.addAttribute("_groupLoanInfo_visible", UtilConstants.Usedoc.caseType2.團貸.equals(caseType));
		model.addAttribute("_btCaseType3_visible", UtilConstants.Usedoc.caseType2.整批匯入.equals(caseType));
		model.addAttribute("_excelInfo_visible", UtilConstants.Usedoc.caseType2.整批匯入.equals(caseType));

		// UPGRADE: 前端須配合改Thymeleaf的樣式
		if(Util.equals(newCustFlag, UtilConstants.DEFAULT.是)){
//			add(new Label("_note").setVisible(true));
			model.addAttribute("_note_visible", true);
		} else {
//			add(new Label("_note").setVisible(false));
			model.addAttribute("_note_visible", false);
		}
	}

	/**/
	private static final long serialVersionUID = 1L;
}
