/* 
 * L180R60ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L180R60A;

/** 振興券兌換客戶統計檔 **/
public interface L180R60ADao extends IGenericDao<L180R60A> {

	L180R60A findByOid(String oid);

	List<L180R60A> findByMainId(String mainId);

	List<L180R60A> findByIndex01(String caseBrId, String custId);

	List<L180R60A> findByIndex02(String custId);

	List<L180R60A> findAll();
}