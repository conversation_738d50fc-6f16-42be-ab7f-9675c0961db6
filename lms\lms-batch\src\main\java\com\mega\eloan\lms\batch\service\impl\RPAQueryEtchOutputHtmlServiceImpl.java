package com.mega.eloan.lms.batch.service.impl;

import java.util.Date;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.gwclient.ETCHGwClient;
import com.mega.eloan.common.gwclient.ETCHGwReqMessage;
import com.mega.eloan.common.gwclient.GWLogger;
import com.mega.eloan.common.gwclient.GWType;
import com.mega.eloan.common.service.GWLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * RPA 回傳勞工紓困貸款案件狀態
 * </pre>
 * <p>
 * LOCAL Test URL example ： http://localhost/ces-web/app/schedulerRPA
 * <p>
 * Post Request : {"serviceId":"getSmallBusStatus", "vaildIP":"N",
 * "request":{"responseCode":"1","custId":"13724746","brNo":"007",
 * "rpaUserId","078001"}}
 * <p>
 * SIT http://*************/ces-web/app/schedulerRPA
 * 
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/6/2,EL07623,new
 *          </ul>
 * @since 2021/6/2
 */
@Service("queryEtchOutputHtmlService")
public class RPAQueryEtchOutputHtmlServiceImpl extends AbstractCapService
		implements WebBatchService {

	private static Logger logger = LoggerFactory
			.getLogger(RPAQueryEtchOutputHtmlServiceImpl.class);

	@Resource
	LMS1201Service service1201;

	@Resource
	SysParameterService sysParameterService;

	@Resource
	GWLogService gwLogService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	ETCHGwClient etchClient;

	@Resource
	EjcicService ejcicService;

	@Value("${systemId}")
	private String sysId;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 * 
	 * REQUEST: http://localhost:9081/lms-web/app/schedulerRPA
	 * {"serviceId":"queryEjcicService"
	 * ,"vaildIP":"N","request":{"custId":"10101013"
	 * ,"dupNo":"0","brNo":"010","rpaUserId":"007623"}}
	 * 
	 * RESPONSE: {"rc":0,"rcmsg":"SUCCESS","message":"執行成功","rcUrl":
	 * "http://***************/ejcic/combination/JCIC0444.jsp?deptid=010&branchnm=%C4%F5%B6%AE%A4%C0%A6%E6&prodid=P1&queryid=10101013&empname=%B6%C0_007623&empid=007623&apid=EL&pur=B4A&purpose=1&cbdeptid=0103&key=ELKEY17925320881792"
	 * }
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		JSONObject mag;
		logger.info("queryEjcicOutputHtmlService 啟動========================");
		logger.info("傳入參數==>[{}]", json.toString());
		GWLogger gwlogger = new GWLogger(GWType.GWTYPE_RPA, gwLogService,
				sysParameterService);

		JSONObject req = json.getJSONObject("request");
		String errorMsg = "";
		String custId = req.optString("custId", "");
		String dupNo = req.optString("dupNo", "");
		String brNo = req.optString("brNo", "");
		String prodId = req.optString("prodId", "");
		String rpaUserId = req.optString("rpaUserId", "");
		String retrialDate = req.optString("retrialDate", "");
		String diffDay = req.optString("diffDay", "0");
		String purpose_for_PACK = req.optString("purpose", ""); // 查詢目的{1:企業授信,
																// 2:房屋貸款,3:消費性貸款,
																// 4:留學生貸款}

		gwlogger.logBegin(sysId, custId, "queryEtchOutputHtmlService",
				req.toString(), System.currentTimeMillis());

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String userId = rpaUserId;

		String empname = userInfoService.getUserName(userId);
		String deptid = brNo;
		String cbdeptid = "";
		String deptnm = "";
		IBranch iBranch = branchService.getBranch(brNo);
		if (iBranch != null) {
			cbdeptid = iBranch.getBrNo() + iBranch.getChkNo();
			deptnm = iBranch.getBrName();
		}

		String callAPI_URL_etch = "";
		String resp_url = "";

		ETCHGwReqMessage etchReq = new ETCHGwReqMessage();
		etchReq.setSysId("CES");
		etchReq.setMsgId(IDGenerator.getUUID());
		etchReq.setEmpid(userId);
		etchReq.setDeptid(deptid);
		etchReq.setProdid(this.isCorp(custId) ? "4112" : "4111"); // 個人第一類4111
																	// 公司第一類4112
		etchReq.setId(custId);
		etchReq.setCbdeptid(cbdeptid);
		etchReq.setReasonid("08"); // 08-其它作業前之客戶資料查詢
		etchReq.setEmpname(empname);
		etchReq.setDeptnm(deptnm);

		String respHtml = "";
		String respHtml64 = "";
		boolean hasOld = false;
		int tDiffDay = Util.parseInt(diffDay);
		Date toDate = Util.parseDate(CapDate.getCurrentDate("yyyy-MM-dd"));
		Date calcDate = CapDate.shiftDays(toDate, tDiffDay);
		String gStrQDate = "";
		try {

			/*
			 * // 先抓EJCIC DB
			 * 
			 * List<Map<String, Object>> datedate_list = ejcicService
			 * .get_mis_datadate_records(custId, prodId);
			 * 
			 * for (Map<String, Object> map : datedate_list) { String str_qDate
			 * = MapUtils.getString(map, "QDATE"); TWNDate qDate =
			 * TWNDate.valueOf(str_qDate); // 同一日(或?日內之資料可以使用) if (qDate !=
			 * null) { if (LMSUtil.cmpDate(qDate, ">=", calcDate)) { gStrQDate =
			 * str_qDate; // 最新一筆查詢日期 break; } }
			 * 
			 * }
			 * 
			 * for (Map<String, Object> map : datedate_list) { String str_qDate
			 * = MapUtils.getString(map, "QDATE"); TWNDate qDate =
			 * TWNDate.valueOf(str_qDate);
			 * 
			 * if (Util.equals(str_qDate, gStrQDate)) {
			 * 
			 * String tProdId = MapUtils.getString(map, "PRODID");
			 * 
			 * try { StringBuilder htmlData = new StringBuilder();
			 * List<Map<String, Object>> list = ejcicService
			 * .getCPXQueryLogHtml(custId, prodId, str_qDate); for (Map<String,
			 * Object> querylog : list) { String txId =
			 * MapUtils.getString(querylog, "TXID"); String html =
			 * MapUtils.getString(querylog, "HTMLDATA");
			 * logger.debug("TXID:{},HTML:{}", txId, html);
			 * htmlData.append(html); } if (Util.notEquals(htmlData.toString(),
			 * "")) { hasOld = true; respHtml64 =
			 * Base64.encodeBytes(htmlData.toString() .getBytes("BIG5")); }
			 * 
			 * } catch (Exception e) { errorMsg = "EJCIC OLD Exception:" +
			 * StrUtils.getStackTrace(e);
			 * logger.error(StrUtils.getStackTrace(e));
			 * 
			 * }
			 * 
			 * } }
			 */
			if (!hasOld) {

				// 抓EJCIC
				callAPI_URL_etch = etchClient.get_callAPI_URL(etchReq);
				resp_url = callAPI_URL_etch;

				/*
				try {

					int sec = 30;
					KeyStore trustStore = KeyStore.getInstance(KeyStore
							.getDefaultType());
					trustStore.load(null, null);

					SSLSocketFactory sf = new RPAHttpSSLSocketFactory(
							trustStore);
					final HttpParams params = new BasicHttpParams();
					HttpConnectionParams.setStaleCheckingEnabled(params, false);
					HttpConnectionParams.setConnectionTimeout(params,
							sec * 1000);
					HttpConnectionParams.setSoTimeout(params, sec * 1000);
					HttpConnectionParams.setSocketBufferSize(params, 8192 * 5);
					SchemeRegistry registry = new SchemeRegistry();
					registry.register(new Scheme("http", PlainSocketFactory
							.getSocketFactory(), 80));
					registry.register(new Scheme("https", sf, 443));
					ClientConnectionManager ccm = new ThreadSafeClientConnManager(
							params, registry);

					HttpClient httpclient = new DefaultHttpClient(ccm, params);

					HttpGet httpGet = new HttpGet(resp_url);
					HttpResponse response = null;
					response = httpclient.execute(httpGet);

					if (response.getStatusLine().getStatusCode() == 200) {
						// 回傳內容
						String content = EntityUtils.toString(
								response.getEntity(), "big5");// UTF-8 big5
						// 內容寫如檔案
						// FileUtils
						// .writeByteArrayToFile(
						// new File(
						// "/elnfs/LMS/931/2021/858e8062cdcd43879d812f6ae1d07136/lrs/cccccc.html"),
						// content.getBytes("BIG5"));

						respHtml64 = Base64.encodeBytes(content
								.getBytes("BIG5"));

					} else {
						errorMsg = "HTTP ERROR:"
								+ response.getStatusLine().getStatusCode();
					}

				} catch (IOException ioe) {
					errorMsg = "EJCIC NEW resp_url=" + resp_url + " Exception:"
							+ StrUtils.getStackTrace(ioe);
					logger.error("url={}", resp_url);
					logger.error(StrUtils.getStackTrace(ioe));
				} catch (Exception e) {
					errorMsg = "EJCIC NEW resp_url=" + resp_url + " Exception:"
							+ StrUtils.getStackTrace(e);
					logger.error("url={}", resp_url);
					logger.error(StrUtils.getStackTrace(e));
				} finally {

				}
				*/
				
			}

		} catch (Exception e) {
			errorMsg = e.toString();
		}

		logger.info("queryEjcicOutputHtmlService 結束========================");

		GWException gwException = null;
		if (!CapString.isEmpty(errorMsg)) {
			logger.info(errorMsg);
			gwException = new GWException(errorMsg, getClass(),
					GWException.GWTYPE_RPA);
		}

		if (!CapString.isEmpty(errorMsg)) {
			logger.info(errorMsg);
		} else {
			logger.info("執行成功");
		}

		if (!CapString.isEmpty(errorMsg)) {
			// mag = JSONObject
			// .fromObject("{\"rc\": 0, \"rcmsg\": \"FAIL\", \"message\":\" "
			// + errorMsg + "\"}");

			JSONObject jsonVal = new JSONObject();
			jsonVal.put("rc", "1");
			jsonVal.put("rcmsg", "FAIL");
			jsonVal.put("message", errorMsg);
			mag = jsonVal;
		} else {

			JSONObject jsonVal = new JSONObject();
			jsonVal.put("rc", "0");
			jsonVal.put("rcmsg", "SUCCESS");
			jsonVal.put("message", "執行成功");
			jsonVal.put("rcType", hasOld ? "O" : "N"); // O:OLD N:NEW
			jsonVal.put("rcHtml", respHtml64);
			jsonVal.put("resp_url", resp_url);
			

			mag = jsonVal;
		}

		gwlogger.logEnd(mag.toString(), gwException, "0");
		return mag;
	}

	public boolean isCorp(String custId) {
		return custId.matches("^[\\d]{8}$")
				|| custId.matches("^[A-Z]{2}Z[\\d]{7}$");
	}
}
