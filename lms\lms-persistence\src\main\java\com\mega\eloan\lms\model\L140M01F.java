/* 
 * L140M01F.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.model;

import java.util.Date;
import java.util.Set;

import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.IDocObject;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 額度利費率主檔 **/
@NamedEntityGraph(name = "L140M01F-entity-graph", attributeNodes = { 
		@NamedAttributeNode("l140m01h"),
		@NamedAttributeNode("l140m01a")
		})
@Entity
@Table(name = "L140M01F", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "rateSeq" }))
public class L140M01F extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件與 額度利率明細檔 L140M01G
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l140m01f", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L140M01G> l140m01g;

	public Set<L140M01G> getL140m01g() {
		return l140m01g;
	}

	public void setL140m01g(Set<L140M01G> l140m01g) {
		this.l140m01g = l140m01g;
	}

	/**
	 * JOIN條件與 額度費率明細檔L140M01H
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "RATESEQ", referencedColumnName = "RATESEQ", nullable = false, insertable = false, updatable = false) })
	private L140M01H l140m01h;

	public L140M01H getL140m01h() {
		return l140m01h;
	}

	public void setL140m01h(L140M01H l140m01h) {
		this.l140m01h = l140m01h;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 序號
	 * <p/>
	 * insert時max(rateSeq)+1
	 */
	@Column(name = "RATESEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer rateSeq;

	/**
	 * 授信科目代碼組合
	 * <p/>
	 * xxx|xxx|xxx…
	 */
	@Column(name = "LOANTPLIST", length = 300, columnDefinition = "VARCHAR(300)")
	private String loanTPList;

	/**
	 * 組成說明字串
	 * <p/>
	 * 利費率敘述
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "RATEDSCR", columnDefinition = "CLOB")
	private String rateDscr;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;
	
	/**
	 * JOIN條件 關聯檔
	 * 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false)
	private L140M01A l140m01a;

	public L140M01A getL140m01a() {
		return l140m01a;
	}

	public void setL140m01a(L140M01A l140m01a) {
		this.l140m01a = l140m01a;
	}

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得序號
	 * <p/>
	 * insert時max(rateSeq)+1
	 */
	public Integer getRateSeq() {
		return this.rateSeq;
	}

	/**
	 * 設定序號
	 * <p/>
	 * insert時max(rateSeq)+1
	 **/
	public void setRateSeq(Integer value) {
		this.rateSeq = value;
	}

	/**
	 * 取得授信科目代碼組合
	 * <p/>
	 * xxx|xxx|xxx…
	 */
	public String getLoanTPList() {
		return this.loanTPList;
	}

	/**
	 * 設定授信科目代碼組合
	 * <p/>
	 * xxx|xxx|xxx…
	 **/
	public void setLoanTPList(String value) {
		this.loanTPList = value;
	}

	/**
	 * 取得組成說明字串
	 * <p/>
	 * 利費率敘述
	 */
	public String getRateDscr() {
		return this.rateDscr;
	}

	/**
	 * 設定組成說明字串
	 * <p/>
	 * 利費率敘述
	 **/
	public void setRateDscr(String value) {
		this.rateDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
