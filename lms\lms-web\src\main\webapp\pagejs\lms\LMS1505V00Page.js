var newRowNum = 15;

$(function(){
    var grid = $("#gridview").iGrid({
        handler: 'lms1505gridhandler',
        height: 347,
        width: 785,
        autowidth: false,
        sortname: 'meetingDate|oid',
        sortorder: 'desc|desc',
        postData: {
            formAction: "query"
        },
        rowNum: newRowNum,
        colModel: [{
            colHeader: i18n.lms1505m01['L150M01a.meetingDate'],//"會議日期",
            name: 'meetingDate',
            align: "center",
            width: 70,
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.lms1505m01['L150M01a.gist'],//"案由",
            name: 'gist',
            width: 100,
            sortable: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        $.form.submit({
            url: '..' + rowObject.docURL + '/02',//../lms/lms1505m01/02
            data: {
                formAction: "query",
                mainOid: rowObject.oid,
                mainId: rowObject.mainId,
                mainDocStatus: viewstatus,
                txCode: txCode
            
            },
            target: rowObject.oid
        });
    };
    
    
    $("#buttonPanel").find("#btnDelete").click(function(){
        var $gridview = $("#gridview");
        var ids = $gridview.getGridParam('selrow');
        
        if (!ids) {//TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        var oids = [];
        for (var i in ids) {
            oids.push($gridview.getRowData(ids).oid);
        }
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "lms1505m01formhandler",
                    data: {
                        formAction: "deleteList",
                        oids: oids
                    },
                    success: function(obj){
                        $("#gridview").trigger("reloadGrid");
                    }
                });
            }
        });
        
    }).end().find("#btnAdd").click(function(){
        $.form.submit({
            url: '../lms/lms1505m01/02',
            data: {
                mainDocStatus: viewstatus
            },
            target: "_blank"
        });
    }).end().find("#btnModify").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {//TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        var result = $("#gridview").getRowData(id);
        openDoc(null, null, result);
        
    }).end().find("#btnFilter").click(function(){
        openFilterBox();
    })
});

function dateObjtoStr(tDate){
    return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" + (tDate.getDate() < 10 ? "0" : "") + tDate.getDate();
}

// 篩選
function openFilterBox(){

    var $filterForm = $("#filterForm");
    // 初始化
    $filterForm.reset();
    
    var sysdate = CommonAPI.getToday().split("-");
    var endDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
    var fromDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
    fromDate.setMonth(fromDate.getMonth() - 12);
    
    $("#meetingDateS").val(dateObjtoStr(fromDate));
    $("#meetingDateE").val(dateObjtoStr(endDate));
    
    var thickTitle;
    
    thickTitle = i18n.lms1205v01['l120v05.title02'];
    
    $("#filterBox").thickbox({
        // l120v05.title02=請輸入欲查詢項目
        title: thickTitle,
        width: 500,
        height: 400,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
            
                if (!$("#filterForm").valid()) {
                    return;
                }
                
                if ($("#meetingDateS").val() > $("#meetingDateE").val()) {
                    //起始日期不能大於結束日期
                    CommonAPI.showErrorMessage(i18n.lms1505v00['l150m01a.meetingDate'] + "：" + i18n.lms1505v00['l150m01a.error01']);
                    return false;
                }
                
                var mDateS = $("#meetingDateS").val().split("-");
                //var newMDateS = new Date(mDateS[0], mDateS[1] - 1, mDateS[2]);
                var mDateE = $("#meetingDateE").val().split("-");
                //var newMDateE = new Date(mDateE[0], mDateE[1] - 1, mDateE[2]);
                //var diffDay = ((newMDateE.getTime()/1000/60/60/24/31)-(newMDateS.getTime()/1000/60/60/24/31)) ;
                var diffDay = ((mDateE[0] - mDateS[0]) * 12) + (mDateE[1] - mDateS[1]);
                if (diffDay > 24) {
                    //查詢起迄日期區間不得相差兩年以上
                    CommonAPI.showErrorMessage(i18n.lms1505v00['l150m01a.meetingDate'] + "：" + i18n.lms1505v00['l150m01a.error02']);
                    return false;
                }
                
                grid("queryL150m01a1");
                
                $.thickbox.close();
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                    
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function grid(action){
    newRowNum = $("#rowNum").val();
    if (newRowNum <= 0) {
        newRowNum = 15;
    }
    $("#gridview").jqGrid("setGridParam", {
        postData: $.extend($("#filterForm").serializeData(), {
            handler: "lms1505gridhandler",
            formAction: action,
            docStatus: viewstatus,
            mainDocStatus: viewstatus,
            rowNum: newRowNum
        
        }),
        rowNum: newRowNum,
        page: 1
    
    }).trigger("reloadGrid");
    
}
