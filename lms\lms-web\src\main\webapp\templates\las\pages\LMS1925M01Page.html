<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
	<body>
		<wicket:extend>
			<script type="text/javascript" src="pagejs/las/LMS1925M01.js"></script>
			<div class="button-menu funcContainer" id="buttonPanel">
				<!--稽核 在分行操作產生文件-->
				<wicket:enclosure><span wicket:id="_btnDOC_EDITING_G_TO_B" />
					<button id="btnSave"> 
	        			<span class="ui-icon ui-icon-jcs-04" />
	        			<wicket:message key="button.save">儲存</wicket:message>
	        		</button>
	        		<button id="btnSendNextG" >
	        			<span class="ui-icon ui-icon-jcs-02" />
	        			<wicket:message key="button.SendNextG">傳送稽核室</wicket:message>
	        		</button>					
		        </wicket:enclosure>
				<!-- 分行自行查核-->
				<wicket:enclosure><span wicket:id="_btnDOC_EDITING" />
					<button id="btnSave"> 
	        			<span class="ui-icon ui-icon-jcs-04" />
	        			<wicket:message key="button.save">儲存</wicket:message>
	        		</button>
	        		<button id="btnSend" >
	        			<span class="ui-icon ui-icon-jcs-02" />
	        			<wicket:message key="button.send">呈主管覆核</wicket:message>
	        		</button>			
		        </wicket:enclosure>
				<!-- 分行自行查核-->
				<wicket:enclosure><span wicket:id="_btnWAIT_APPROVE" />
					<button id="btnAccept" >
		        		<span class="ui-icon ui-icon-jcs-214" />
		        		<wicket:message key="button.accept">核可</wicket:message>
		        	</button>
		        	<button id="btnReturn" >
		        		<span class="ui-icon ui-icon-jcs-210" />
		        		<wicket:message key="button.return">退回</wicket:message>
		        	</button>
		        </wicket:enclosure>
				<!--稽核 查核-->
				<wicket:enclosure><span wicket:id="_btnDOC_EDITING_G" />
					<button id="btnSave"> 
	        			<span class="ui-icon ui-icon-jcs-04" />
	        			<wicket:message key="button.save">儲存</wicket:message>
	        		</button>
	        		<button id="btnSendG" >
	        			<span class="ui-icon ui-icon-jcs-02" />
	        			<wicket:message key="button.send">呈主管覆核</wicket:message>
	        		</button>			
		        </wicket:enclosure>
				<!--稽核 查核-->
				<wicket:enclosure><span wicket:id="_btnWAIT_APPROVE_G" />
					<button id="btnAcceptG" >
		        		<span class="ui-icon ui-icon-jcs-214" />
		        		<wicket:message key="button.accept">核可</wicket:message>
		        	</button>				
		        	<button id="btnReturnG" >
		        		<span class="ui-icon ui-icon-jcs-210" />
		        		<wicket:message key="button.return">退回</wicket:message>
		        	</button>										
		        </wicket:enclosure>
				<!--
                <button id="btnPrint">
                	<span class="ui-icon ui-icon-jcs-03"></span>
					<wicket:message key="button.print">列印</wicket:message>
				</button>
				-->
                <button id="btnExit" class="forview">
                	<span class="ui-icon ui-icon-jcs-01"></span>
					<wicket:message key="button.exit">離開</wicket:message>
				</button>
				
				<button id="btnPrint2">
                	<span class="ui-icon ui-icon-jcs-03"></span>
					<wicket:message key="Lms_Auditsht.print_button_1">列印工作底稿</wicket:message>
				</button>				
				<button id="btnPrint3">
                	<span class="ui-icon ui-icon-jcs-03"></span>
					<wicket:message key="Lms_Auditsht.print_button_2">列印對帳單</wicket:message>
				</button>
				
            </div>
			<div class="tit2 color-black">
				<wicket:message key="Lms_Auditsht.info">查核授信業務工作底稿</wicket:message>
				<span class="color-red" id="innerAudit_info" style="display:none;"><wicket:message key="Lms_Auditsht.innerAuditInfo">(分行內部查核案件)</wicket:message></span>
				<span class="color-blue" id="titInfo"/>
			</div>
			<div class="tabs doc-tabs">
                <ul>
                	<li><a href="#tab-01" goto="01"><b><wicket:message key="doc.docinfo">文件資訊</wicket:message></b></a></li>
					<li><a href="#tab-02" goto="02"><b><wicket:message key="Lms_Auditsht.tit02">借款人及連保人基本資料</wicket:message></b></a></li>
					<li><a href="#tab-03" goto="03"><b><wicket:message key="Lms_Auditsht.tit03">申請內容</wicket:message></b></a></li>
					<li><a href="#tab-04" goto="04"><b><wicket:message key="Lms_Auditsht.tit04">擔保品內容</wicket:message></b></a></li>
					<li><a href="#tab-05" goto="05"><b><wicket:message key="Lms_Auditsht.tit05">查核事項</wicket:message></b></a></li>
					<li><a href="#tab-06" goto="06"><b><wicket:message key="Lms_Auditsht.tit06">檢查意見&處理意見</wicket:message></b></a></li>
                </ul>
                <div class="tabCtx-warp">
                	<form id="tabForm">
                		<input type="text" class="hide" id="mainDocStatus" name="mainDocStatus" />
                		<input type="text" class="hide" id="mainOid" name="mainOid" />
						<input type="text" class="hide" id="mainId" name="mainId" />
						<input type="text" class="hide" id="uid" name="uid" />                		
                		<div id="tabs-00" wicket:id="_tabCtx" />
					</form>
				</div>
			</div>
			<!--<div id="tabs-01" wicket:id="_address" />-->
		</wicket:extend>
    </body>
</html>
