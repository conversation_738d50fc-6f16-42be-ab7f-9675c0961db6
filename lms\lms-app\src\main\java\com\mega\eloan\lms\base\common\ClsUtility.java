package com.mega.eloan.lms.base.common;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InterruptedIOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.math.BigDecimal;
import java.nio.channels.FileLock;
import java.nio.channels.OverlappingFileLockException;
import java.sql.Timestamp;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.common.gwclient.Brmp001O;
import com.mega.eloan.common.gwclient.Brmp001O.Brmp001O_result_policyObj;
import com.mega.eloan.common.gwclient.Brmp002O;
import com.mega.eloan.common.gwclient.Brmp002O.Brmp002O_resultObj;
import com.mega.eloan.common.gwclient.Brmp002O.Brmp002O_result_policyObj;
import com.mega.eloan.common.gwclient.Brmp003O;
import com.mega.eloan.common.gwclient.Brmp005O;
import com.mega.eloan.common.gwclient.Brmp005O.Brmp005O_result_policyObj;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.ProdService.ProdKindEnum;
import com.mega.eloan.lms.model.C101S01B;
import com.mega.eloan.lms.model.C101S01W;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S03A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140MC1A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02D;
import com.mega.eloan.lms.model.L140S02N;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

public class ClsUtility {
	private static Logger logger = LoggerFactory.getLogger(ClsUtility.class);
	public static final String C101M01A_JCICFLG = "jcicFlg";
	public static final String C101M01A_PRIMARY_CARD = "primary_card";
	public static final String C101M01A_ADDITIONAL_CARD = "additional_card";
	public static final String C101M01A_BUSINESS_OR_P_CARD = "business_or_p_card";
	public static final String C101M01A_HOLD_MEGA_CARD_DT = "holdMegaCardDt";
	public static final String L120S18A_CUSTID_9999999999 = "9999999999";
	public static final String L120S18A_DUPNO_9 = "9";
	
	public static final String L140S02A_ESGGTYPE_Z_其他 = "Z";
	public static final String L140S02N_ITEMTYPE_1_綠色支出類型Z_說明 = "1";
	
	public static final String SNRDESC = "snrDesc"; //傳入 i-net 報表 的變數
	
	public static final String CSC_GRPCNTRNO_PERIOD_046 = "918111000325"; //中鋼消貸第46期
	public static final String CSC_GRPCNTRNO_PERIOD_048 = "918111200181"; //中鋼消貸第48期
	public static final String CSC_GRPCNTRNO_PERIOD_049 = "918111300276"; //中鋼消貸第49期
	
	private static final BigDecimal RATE_16 = new BigDecimal(16);
	
	public static boolean is_rate_gt_16percent(BigDecimal bd){
		if(bd==null){
			return false;
		}else{
			if(bd.compareTo(RATE_16)>0){
				return true;	
			}
		}
		return false;
	}
	
	public static L120S03A cls_initL120s03a(L120S03A l120s03a, L140M01A l140m01a,
			String crdFlag, NumberFormat nf) {
		if (UtilConstants.Casedoc.L120s03aCrdFlag.信保.equals(crdFlag)) {
			// 信保
			l120s03a.setCrdFlag(UtilConstants.Casedoc.L120s03aCrdFlag.信保);
			// 保證成數(B)
			if (l140m01a.getGutPercent() != null) {
				l120s03a.setCrdRatio(l140m01a.getGutPercent().doubleValue());
			}

			// J-104-0084-001 Web e-Loan授信系統修改風險權數計算明細表，就信保基金保證之授信案，風險權數改為得由經辦修改
			l120s03a.setCrdRskRatio(new Double("100"));

			// 本行自貸曝險風險權數
			l120s03a.setRskMega(Util.parseDouble(nf.format(0)));
			// 信保曝險風險權數
			l120s03a.setRskCrd(Util.parseBigDecimal(nf.format(0)));
			// 抵減後全部曝險額
			l120s03a.setRskAmt2(BigDecimal.ZERO);
			// 抵減後風險權數
			l120s03a.setRskr2((Double) Util.parseDouble(nf.format(0)));
			// 資本使用額（免填）
			l120s03a.setCamt2(BigDecimal.ZERO);
			// 占資本適足率
			l120s03a.setBisr2(new Double("0"));
			// 資金成本率
			l120s03a.setCostr2(new Double("0"));
			if (Util.isEmpty(Util.trim(l140m01a.getGutPercent()))) {
				// 保證成數(B)沒輸入->設為N
				l120s03a.setChkYN(UtilConstants.DEFAULT.否);
			} else {
				l120s03a.setChkYN(UtilConstants.DEFAULT.是);
			}
		} else {
			// 非信保
			l120s03a.setCrdFlag(UtilConstants.Casedoc.L120s03aCrdFlag.非信保);
			// J-104-0084-001 Web e-Loan授信系統修改風險權數計算明細表，就信保基金保證之授信案，風險權數改為得由經辦修改
			l120s03a.setCrdRskRatio(null);
			l120s03a.setRskAmt1(BigDecimal.ZERO);
			l120s03a.setRskr1((Double) Util.parseDouble(nf.format(0)));
			l120s03a.setCamt1(BigDecimal.ZERO);
			l120s03a.setBisr1(new Double("0"));
			l120s03a.setCostr1(new Double("0"));
			l120s03a.setChkYN(UtilConstants.DEFAULT.否);
		}
		return l120s03a;
	}
	
	public static String get_elf459_term_group(List<L140S02A> l140s02a_list){
		Set<String> property8_term = new HashSet<String>();
		Set<String> propertyNot8_term = new HashSet<String>();
		for(L140S02A l140s02a : l140s02a_list){
			String termGroup = Util.trim(l140s02a.getTermGroup());
			if(Util.isEmpty(termGroup)){
				continue;
			}else{
				if(Util.equals(l140s02a.getProperty(), UtilConstants.Cntrdoc.Property.取消)){
					property8_term.add(termGroup);
				}else{
					propertyNot8_term.add(termGroup);
				}
			}
		}
		//優先
		for(String termGroup : propertyNot8_term){
			return termGroup;
		}
		
		for(String termGroup : property8_term){
			return termGroup;
		}
		return "";
	}

	public static BigDecimal add_BigDecimal(BigDecimal a, BigDecimal b){
		if(a==null){
			return b;
		}else{
			if(b==null){
				return a;
			}else{
				return a.add(b);
			}
		}
	}
	
	public static String get_inject_snrY(BigDecimal s01b_seniority){
		String snrY = "";
		if(s01b_seniority!=null){
			snrY = String.valueOf(ClsUtility.seniorityYM_decode(s01b_seniority)[0]); 
		}
		return snrY;
	}
	
	public static BigDecimal floor_seniorityYM_to_yearVal_because_outer_system(BigDecimal val){ //J-110-0298 年資由「X 年」改變為「X 年 X 月」
		return new BigDecimal(seniorityYM_decode(val)[0]).stripTrailingZeros();
	}
	
	public static BigDecimal seniorityYM_encode(int year, int month){
		Map<Integer, BigDecimal> map = _seniority_month_round_year_map();
		return new BigDecimal(year).add(map.containsKey(month)?map.get(month):BigDecimal.ZERO);
	}
	public static Integer[] seniorityYM_decode(BigDecimal v){
		Integer[] result = new Integer[]{0, 0};
		if(v!=null){
			result[0] = Arithmetic.floor(v, 0).intValue();
			result[1] = _seniorityYM_map_month_to_year(v);
		}
		return result;
	}
	private static Map<Integer, BigDecimal> _seniority_month_round_year_map(){
		Map<Integer, BigDecimal> map = new HashMap<Integer, BigDecimal>();
		map.put(0, new BigDecimal("0"));
		map.put(1, new BigDecimal("0.08")); 
		map.put(2, new BigDecimal("0.17")); 
		map.put(3, new BigDecimal("0.25")); 
		map.put(4, new BigDecimal("0.33"));
		map.put(5, new BigDecimal("0.42"));
		map.put(6, new BigDecimal("0.50"));
		map.put(7, new BigDecimal("0.58"));
		map.put(8, new BigDecimal("0.67"));
		map.put(9, new BigDecimal("0.75"));
		map.put(10, new BigDecimal("0.83"));
		map.put(11, new BigDecimal("0.92"));
		map.put(12, new BigDecimal("1"));
		return map;
	}
	
	private static int _seniorityYM_map_month_to_year(BigDecimal v){
		BigDecimal remainder = v.remainder(BigDecimal.ONE);
		Map<Integer, BigDecimal> map = _seniority_month_round_year_map();
		for(Integer month : map.keySet()){
			if(map.get(month).compareTo(remainder)==0){
				return month;
			}
		}
		return 0;
	}
	
	//ref TRSF.TEST.MACLIB(TRPAYSQC)
	//input="{0000F1543619520Y03705903   00702036062            "
	public static Map<String, String> parse_TRPAYSQ1_rtn(String outResult){
		Map<String, String> result = new HashMap<String, String>();
		
		// 第1碼是{
		String RETURN_CODE = Util.trim(StringUtils.substring(outResult, 1, 5));
		String PV_ID = Util.trim(StringUtils.substring(outResult, 5, 16));
		String PAYLG = Util.trim(StringUtils.substring(outResult, 16, 17));//是否為薪轉戶
		String TAXNO = Util.trim(StringUtils.substring(outResult, 17, 27));
		String GRADE = Util.trim(StringUtils.substring(outResult, 27, 28));
		String ACTNO = Util.trim(StringUtils.substring(outResult, 28, 39));
		
		result.put("RETURN_CODE", RETURN_CODE);
		result.put("PV_ID", PV_ID);
		result.put("PAYLG", PAYLG);
		result.put("TAXNO", TAXNO);
		result.put("GRADE", GRADE);
		result.put("ACTNO", ACTNO);
		return result;
	}

	public static Map<String, String> ploan_attch_DocFileFlagDesc(){
		/*
		  參考 megagw 裡的【 PLoanServiceImpl.java 】procPLOAN003(...)
		 */
		Map<String, String> r = new HashMap<String, String>();
		r.put("0", "申請"); //若需修正申請書PDF的資料，在PLOAN003電文增加{serviceType=3}處理接收{新產生的PDF}
		r.put("3", "正面");
		r.put("4", "反面");
		r.put("7", "財力");
		r.put("9", "其他");
		r.put("1", "員工識別證");
		r.put("2", "薪資單");
		r.put("5", "第二證件");
		r.put("6", "扣繳憑單");
		r.put("M", "MyData");
		r.put("8", "ixmljws");
		r.put("A", "授權書");
		return r;
		
	}

	public static boolean is_only_M3_and_pmRate_0(List<L140S02D> l140s02d_list ){
		int size = l140s02d_list.size();
		if(size > 0){
			int match_cnt = 0 ;
			for(L140S02D l140s02d: l140s02d_list){
				if(Util.equals(l140s02d.getRateType(), CrsUtil.RATE_TYPE_M3) 
						&& (l140s02d.getPmRate()==null || BigDecimal.ZERO.compareTo(l140s02d.getPmRate())==0)){
					++match_cnt;
				}else{
					return false;
				}
			}
			
			if(match_cnt>0){
				return true;
			}
		}
		
		return false;
	}

	public static boolean is_only_MR_and_pmRate_0(List<L140S02D> l140s02d_list ){
		int size = l140s02d_list.size();
		if(size > 0){
			int match_cnt = 0 ;
			for(L140S02D l140s02d: l140s02d_list){
				if(Util.equals(l140s02d.getRateType(), CrsUtil.RATE_TYPE_MR)){
					++match_cnt;
				}else{
					return false;
				}
			}

			if(match_cnt>0){
				return true;
			}
		}

		return false;
	}

	public static String get_latest_rptId_C340M01A_CtrType(String ctrType) {
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_L)){
			return ContractDocConstants.C340M01A_RptId.CtrTypeL_V202106;
		}
		return "";		
	}
	
	public static String get_labor_bailout_2021_since_ts(){
		return  "2021-06-01 00:00:00";
	}
	
	public static String get_labor_bailout_2021_since_date(){
		return  Util.getLeftStr(get_labor_bailout_2021_since_ts(), 10);
	}
	
	public static boolean overwrite_labor_bailout_l120m01a_purposeOth(L120M01A l120m01a){
		String l120m01a_purpose = Util.trim(l120m01a.getPurpose());
		if(l120m01a_purpose.indexOf(UtilConstants.Casedoc.purpose.其他)<0){
			if(Util.isEmpty(l120m01a_purpose)){
				l120m01a.setPurpose(UtilConstants.Casedoc.purpose.其他);
			}else{
				TreeSet<String> ts = new TreeSet<String>();  
				for(String item : l120m01a_purpose.split("\\|")){
					ts.add(item);
				}
				ts.add(UtilConstants.Casedoc.purpose.其他);
				l120m01a.setPurpose(StringUtils.join(ts, "|"));
			}
			l120m01a.setPurposeOth("供生活支出所需或週轉之用");
			return true;
		}
		return false;
	}

	public static boolean is_l120m01a_simplifyFlag_labor_bailout(L120M01A l120m01a){
		// LMS . L120S13A 個金勞工紓困審核表
		// 參考  CLS1201S23Panel_zh_TW.properties
		if (Util.equals(UtilConstants.Casedoc.SimplifyFlag.勞工紓困, l120m01a.getSimplifyFlag())) {
			return true;
		}
		
		return false;
	}

	public static String get_check_stepValue___calc_L140M01A_totValue(){
		return "calc_L140M01A_totValue";
	}
	
	
	public static String convert_c101s01a_IdCardChgFlag_to_z21_applyReasonCode(String idCardChgFlag) {
		if (Util.equals("0", idCardChgFlag)) { // 初發
			return "1";
		} else if (Util.equals("1", idCardChgFlag)) { // 補發
			return "2";
		} else if (Util.equals("2", idCardChgFlag)) { // 換發
			return "3";
		} else if (Util.equals("", idCardChgFlag)) {
			return " ";
		}
		return idCardChgFlag;
	}
	
	//保證人財力明顯高於借款人且二者無親屬或共同生活關係註記 = 1	PH014	C	保證人財力明顯高於借款人且二者無親屬或共同生活關係
	public static boolean match_L140MC1A_itemCode_C(BigDecimal mainBorrower_s01b_payAmt, BigDecimal l140s01a_s01b_payAmt, String l140s01a_rKindM, String l140s01a_rKindD, String l140s01a_isLiveWithBorrower){
		boolean s = l140s01a_s01b_payAmt.compareTo(mainBorrower_s01b_payAmt) > 0;
		boolean r = !"2".equals(l140s01a_rKindM) 
						|| ("2".equals(l140s01a_rKindM) && "XL".equals(l140s01a_rKindD) && !"Y".equals(l140s01a_isLiveWithBorrower));
		if(s && r){ 
			return true;
		}
		return false; 
	}
	
	//TODO 推薦人異常註記= 1	PH029	E	非行員之同一介紹人轉介整批案件之申請案件(單月達三筆)
	
	//資料與聯徵中心或與本行不相符註記 = 1	PH015	F	借款人提供之資料與聯徵中心或與本行內部留存資料不相符
	public static boolean match_L140MC1A_itemCode_F(String isImportDataMatch){
		return "N".equals(isImportDataMatch);
	}
	
	//G-19  年資<=1 年時
	public static boolean match_L140MC1A_itemCode_G(BigDecimal annualSeniority){
		return annualSeniority.compareTo(BigDecimal.ONE) <= 0;
	}
	
	/* 借款人工作屬性是否相同註記= 1	PH006	H	借款人職業變動頻繁或目前任職之工作為期短暫前工作屬性是否與現工作相同
	 * &&
	 * G-19  年資<=1 年時
	 */
	public static boolean match_L140MC1A_itemCode_H(boolean isSeniorityLessThan1Year, String isSameWorkAttributes){
		return isSeniorityLessThan1Year && "N".equals(isSameWorkAttributes);
	}
	
	//難以照會註記= 1	PH018	I	照會過程難以直接聯繫借款人本人，需由第三人居中聯繫
	public static boolean match_L140MC1A_itemCode_I(String isNoteBorrower){
		return "N".equals(isNoteBorrower);
	}
	
	//提供經變造之申貸文件註記 = 1	PH019	J	借款人提供非正本之申貸文件
	public static boolean match_L140MC1A_itemCode_J(String isCheckOriginalDocument){
		return "N".equals(isCheckOriginalDocument);
	}
	
	//文件過於完整註記= 1	PH004	K	借款人或保證人提供申請資料及證明文件過於完整
	public static boolean match_L140MC1A_itemCode_K(String isFullApplyDocument){
		return "Y".equals(isFullApplyDocument);
	}
	
	//多筆非自住房貸註記 = 1	PH020	N	借款人名下有多筆非自住房貸貸款
	public static boolean match_L140MC1A_itemCode_N(int count){
		return count > 2 ;
	}	
	
	//他行新種業務查詢次數密集註記 = 1	PH011	P	借款人於聯徵中心被查詢次數密集
	public static int get_L140MC1A_itemCode_P_threshold(){
		return 2;
	}
	
	//升等註記= 1	PH010	R	行內訂條款A01-評等升等
	public static boolean match_L140MC1A_itemCode_R(String grade_adjustStatus){
		return "1".equals(grade_adjustStatus);
	}
	
	//簽名不一致註記= 1	PH026	V	買賣契約書(如有)與借款契約、借款申請書簽名不一致
	public static boolean match_L140MC1A_itemCode_V(String isDocSignatureNotMatch){
		return Util.equals("Y", isDocSignatureNotMatch);
	}
	
	//擔保品所有權與債權異常註記= 1	PH027	W	借款人、擔保品所有權人與房屋契約書之買方不同人(產品類別非房貸不適用)
	
	
	//非親屬第三人陪同註記 = 1	PH022	X	由非親屬之第三人陪同申辦貸款並回答詢問案件問題
	public static boolean match_L140MC1A_itemCode_X(String isWithNonRelatives){
		return Util.equals("Y", isWithNonRelatives);
	}
	
	//指定撥款日期註記= 1	PH023	Y	借款人指定撥款日期及時間且無法提出合理解釋
	public static boolean match_L140MC1A_itemCode_Y(String isPointAppropriationDate){
		return Util.equals("Y", isPointAppropriationDate);
	}
	
	//職業內容及貸款用途不明確註記= 1	PH025	Z	對自己從事之行業或職業性質與內容及貸款用途不瞭解或毫無概念
	public static boolean match_L140MC1A_itemCode_Z(String isDontKnowOwnAffairs){
		return Util.equals("Y", isDontKnowOwnAffairs);
	}
	
	//聯徵異常查詢註記= 1	PH028	1	於聯徵具被查詢紀錄，卻無法說明原因
	public static boolean match_L140MC1A_itemCode_1(String isDontExplainEjcicRecord){
		return "Y".equals(isDontExplainEjcicRecord);
	}
	
	//支付銀行開辦費以外費用註記= 1	PH024	2	支付銀行收取開辦手續費以外之費用
	public static boolean match_L140MC1A_itemCode_2(String isPayOtherFee){
		return "Y".equals(isPayOtherFee);
	}
	
	//TODO 收入入帳金額為整數註記 = 1	PH021	3	借款人或保證人之扣繳憑單給付總額或收入入帳金額為整數
	
	
	/** 例如：70.23％  ＝＝＝＞ input：70.23 , 應回傳 0.7023
	 */
	public static BigDecimal get_noPercentSignal_value(BigDecimal src){
		if(src != null){
			int scale = src.scale();
			return Arithmetic.div(src, new BigDecimal(100), scale+2);
		}
		return src;
	}
	
	/** 例如：0.0268  ＝＝＝＞ input：0.0268 , 應回傳 2.68％
	 */
	public static BigDecimal get_percentSignal_value(BigDecimal src){
		if(src != null){
			return Arithmetic.mul(src, new BigDecimal(100));
		}
		return src;
	}
	
	/** 例如：2萬  ＝＝＝＞ input：2 , 應回傳 20000
	*/
	public static BigDecimal get_value_multiplyWAN(BigDecimal src){
		if(src != null){			
			return src.multiply(new BigDecimal(10000));
		}
		return src;
	}
	
	/** 例如：5萬  ＝＝＝＞ input：50000 , 應回傳 5
	*/
	public static BigDecimal get_value_divideWAN(BigDecimal src){
		if(src != null){			
			return src.divide(new BigDecimal(10000));
		}
		return src;
	}
	
	/** 例如：46,702 , 應回傳 4
	*/
	public static BigDecimal get_floorValue_divideWAN(BigDecimal src){
		if(src != null){			
			return Arithmetic.floor(src.divide(new BigDecimal(10000)), 0);
		}
		return src;
	}
	
	public static BigDecimal brmp_loanAmt_WAN(BigDecimal src){
		return get_floorValue_divideWAN(src);
	}

	/**
	 * 頁籤-歡喜信貸審核表 的「平均月收入」，比照 頁籤-信貸決策審核表 的 「平均月收入」的 logic 去抓
	 */
	public static BigDecimal brmp_avgMonthIncome(String c120s01c_rateData){
		BigDecimal r = null;
		
		try{
			JSONObject rateDataJs = JSONObject.fromObject(c120s01c_rateData);
			
			r = CapMath.getBigDecimal(rateDataJs.optString("pAllAmt"))
					.divide(BigDecimal.valueOf(12), BigDecimal.ROUND_HALF_UP);	
		}catch(Exception e){
			
		}
		return r;
	}
	
	public static BigDecimal get_simplifyFlagD_l120s15a_pmtAmt(String c120s01c_rateData){
		BigDecimal r = null;
		
		try{
			JSONObject rateDataJs = JSONObject.fromObject(c120s01c_rateData);
			String mode_1 = Util.trim(rateDataJs.opt("mode_1"));
			String mPay_1 = Util.trim(rateDataJs.opt("mPay_1"));
			String mPayCh_1 = Util.trim(rateDataJs.opt("mPayCh_1"));
			if(Util.equals("1", mode_1)){
				if(Util.isNotEmpty(mPayCh_1)){
					return CrsUtil.parseBigDecimal(mPayCh_1);
				}else if(Util.isNotEmpty(mPay_1)){
					return CrsUtil.parseBigDecimal(mPay_1);
				}
			}	
		}catch(Exception e){
			
		}
		return r;
	}
	
	public static BigDecimal s01c_pAllAmt___yearIncome(C101S01B s01b, List<C101S01W> c101s01ws){
		if (s01b != null) {
			BigDecimal yearIncome = BigDecimal.ZERO;

			if (s01b.getIncomeDetailVer() == null || s01b.getIncomeDetailVer().intValue() == 0) {
				String mainIncomeType = s01b.getMainIncomeType();
				if ("A".equals(mainIncomeType)) {
					yearIncome = yearIncome.add(s01b.getItemAvalueYear());
				} else if ("B1".equals(mainIncomeType)) {
					yearIncome = yearIncome.add(s01b.getItemB1valueYear());
				} else if ("B2".equals(mainIncomeType)) {
					yearIncome = yearIncome.add(s01b.getItemB2valueYear());
				} else if ("B3".equals(mainIncomeType)) {
					yearIncome = yearIncome.add(s01b.getItemB3valueYear());
				} else if ("B4".equals(mainIncomeType)) {
					yearIncome = yearIncome.add(s01b.getItemB4valueYear());
				}

				String otherC1 = s01b.getOtherC1();
				if ("Y".equals(otherC1)) {
					yearIncome = yearIncome.add(s01b.getItemC1valueYear());
				}

				String otherC2 = s01b.getOtherC2();
				if ("Y".equals(otherC2)) {
					yearIncome = yearIncome.add(s01b.getItemC2valueYear());
				}

				String otherC3 = s01b.getOtherC3();
				if ("Y".equals(otherC3)) {
					yearIncome = yearIncome.add(s01b.getItemC3valueYear());
				}
			} else if (s01b.getIncomeDetailVer().intValue() == 1) {
				for (C101S01W c101s01w : c101s01ws) {
					if ("pYearIncome".equals(c101s01w.getKeyString())) {
						yearIncome = CapMath.getBigDecimal(c101s01w.getValueString());
						break;
					}
				}
			} else {
				for (C101S01W c101s01w : c101s01ws) {
					if ("A".equals(c101s01w.getIncomeType())) {
						yearIncome = yearIncome.add(CapMath.bigDecimalIsNullToZero(c101s01w.getValueYear()));
					}
				}
			}
			return yearIncome;
		}
		return BigDecimal.ZERO;
	}
	
	public static BigDecimal s01c_pAllAmt___otherIncome(C101S01B s01b, List<C101S01W> c101s01ws){
		if (s01b != null) {
			BigDecimal otherIncome = BigDecimal.ZERO;
			if (s01b.getIncomeDetailVer() == null || s01b.getIncomeDetailVer().intValue() == 0) {
				String[] loops = new String[] { "D4", "D5", "D6", "D7", "D8", "D9" };
				try{
					for (String loop : loops) {
						String other = (String) s01b.get("other" + loop);
						if ("Y".equals(other)) {
							otherIncome = otherIncome.add((BigDecimal) s01b.get("item" + loop + "valueYear"));
						}
					}
				}catch(CapException e){

				}
			} else if (s01b.getIncomeDetailVer().intValue() == 1) {
				for (C101S01W c101s01w : c101s01ws) {
					if ("pOtherIncome".equals(c101s01w.getKeyString())) {
						otherIncome = CapMath.getBigDecimal(c101s01w.getValueString());
						break;
					}
				}
			} else {
				for (C101S01W c101s01w : c101s01ws) {
					if ("B".equals(c101s01w.getIncomeType())) {
						otherIncome = otherIncome.add(CapMath.bigDecimalIsNullToZero(c101s01w.getValueYear()));
					}
				}
			}
			return otherIncome;
		}
		return BigDecimal.ZERO;
	}

	public static String brmp_l120m01d_6_itemDscr(L120M01A l120m01a){
		if(l120m01a!=null){
			if (UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(l120m01a.getAuthLvl()) 
					&& Util.isNotEmpty(Util.trim(l120m01a.getAreaBrId()))) {
				return "呈報區域營運中心";
			} 
			
			if (UtilConstants.Casedoc.DocKind.授權內.equals(l120m01a.getDocKind())
					&& UtilConstants.Casedoc.AuthLvl.分行授權內.equals(l120m01a.getAuthLvl())) {
				
				if(UtilConstants.Casedoc.SimplifyFlag.快速審核信貸.equals(l120m01a.getSimplifyFlag())){
					return "可";	
				}else{
					return "可";
				}
			} 			
		}		
		return "";		
	}
	
	public static String brmp_check_policyStatus_forEloanForm(String[] policyStatus_arr, String pass_status, String fail_status){
		int pass_cnt = 0;
		int size = policyStatus_arr==null?0:policyStatus_arr.length;
		if(size>0){
			for(String policyStatus :policyStatus_arr){
				if(Util.equals(policyStatus, pass_status)){
					++pass_cnt;
				}else if(Util.equals(policyStatus, fail_status)){
					return "N";
				}
			}
			if(pass_cnt==size){
				return "Y";
			}
		}
		return "";
	}
	
	public static BigDecimal get_pmRate_from_baseRate_stageRate(BigDecimal baseRate, BigDecimal stageRate){
		return stageRate.subtract(baseRate);
	}

	public static String get_Brmp001O_return_status(List<Brmp001O_result_policyObj> list, String policyCode){
		for(Brmp001O_result_policyObj obj : list){
			if(Util.equals(obj.getPolicyCode(), policyCode)){
				return Util.trim(obj.getStatus());
			}
		}
		return "";
	}

	public static List<String> get_Brmp001O_statusMissing(Brmp001O brmpObj){
		List<String> list = new ArrayList<String>();
		
		List<Brmp001O_result_policyObj> brmp_rtn_list = brmpObj.getResult().getPolicyResult();
					
		for(Brmp001O_result_policyObj policyObj : brmp_rtn_list ){
			if(Util.equals("M", policyObj.getStatus())){
				list.add(Util.trim(policyObj.getPolicyCode())+"："+Util.trim(policyObj.getPolicyDescription()));
			}			
		}			
		return list;
	}

	public static String get_Brmp002O_return_status(List<Brmp002O_result_policyObj> list, String policyCode){
		for(Brmp002O_result_policyObj obj : list){
			if(Util.equals(obj.getPolicyCode(), policyCode)){
				return Util.trim(obj.getStatus());
			}
		}
		return "";
	}

	public static List<String> get_Brmp002O_statusMissing(Brmp002O brmpObj){
		List<String> list = new ArrayList<String>();
		
		List<Brmp002O_result_policyObj> brmp_rtn_list = brmpObj.getResult().getPolicyResult();
					
		for(Brmp002O_result_policyObj policyObj : brmp_rtn_list ){
			if(Util.equals("M", policyObj.getStatus())){
				list.add(Util.trim(policyObj.getPolicyCode())+"："+Util.trim(policyObj.getPolicyDescription()));
			}			
		}			
		return list;
	}
	
	public static List<String> get_Brmp002O_rejectDesc(Brmp002O brmpObj){
		List<String> list = new ArrayList<String>();
		
		List<Brmp002O_result_policyObj> brmp_rtn_list = brmpObj.getResult().getPolicyResult();
					
		for(Brmp002O_result_policyObj policyObj : brmp_rtn_list ){
			if(Util.equals("R", policyObj.getActionType())){
				list.add(Util.trim(policyObj.getPolicyCode())+"："+Util.trim(policyObj.getPolicyDescription()));
			}			
		}			
		return list;
	}
	public static List<String> get_Brmp002O_rejectDesc(Brmp002O brmpObj, String limit_policyCode){
		List<String> list = new ArrayList<String>();
		
		List<Brmp002O_result_policyObj> brmp_rtn_list = brmpObj.getResult().getPolicyResult();
					
		for(Brmp002O_result_policyObj policyObj : brmp_rtn_list ){
			if(Util.equals("R", policyObj.getActionType()) 
					&& Util.equals(limit_policyCode, StringUtils.substring(policyObj.getPolicyCode(), 1, 2))){
				list.add(Util.trim(policyObj.getPolicyCode())+"："+Util.trim(policyObj.getPolicyDescription()));
			}			
		}			
		return list;
	}
	public static List<String> get_Brmp005O_rejectDesc(Brmp005O brmpObj){
		List<String> list = new ArrayList<String>();

		List<Brmp005O_result_policyObj> brmp_rtn_list = brmpObj.getResult().getPolicyResult();

		for(Brmp005O_result_policyObj policyObj : brmp_rtn_list ){
			if(Util.equals("R", policyObj.getActionType())){
				list.add(Util.trim(policyObj.getPolicyCode())+"："+Util.trim(policyObj.getPolicyDescription()));
			}
		}
		return list;
	}
	public static List<String> get_Brmp005O_rejectDesc(Brmp005O brmpObj, String limit_policyCode){
		List<String> list = new ArrayList<String>();

		List<Brmp005O_result_policyObj> brmp_rtn_list = brmpObj.getResult().getPolicyResult();

		for(Brmp005O_result_policyObj policyObj : brmp_rtn_list ){
			if(Util.equals("R", policyObj.getActionType())
					&& Util.equals(limit_policyCode, StringUtils.substring(policyObj.getPolicyCode(), 1, 2))){
				list.add(Util.trim(policyObj.getPolicyCode())+"："+Util.trim(policyObj.getPolicyDescription()));
			}
		}
		return list;
	}

	/** 人頭戶強指標 
	 */
	public static boolean headAccount_strongIndex_match(Brmp002O brmpOutput){
		int rejectDesc_size_policyCodeH = ClsUtility.get_Brmp002O_rejectDesc(brmpOutput, "H").size();
		return rejectDesc_size_policyCodeH > 0;
	}
	
	public static boolean headAccount_weakIndex_match(Brmp002O brmpOutput, int percent){
		Brmp002O_resultObj brmp_result = brmpOutput.getResult();
		if(brmp_result!=null){
			BigDecimal haWeakIndicator = brmp_result.getHaWeakIndicator();
			if(haWeakIndicator!=null && ClsUtility.get_percentSignal_value(haWeakIndicator).compareTo(new BigDecimal(percent))>=0){
				return true;
			}	
		}
		return false;
	}
	
	/** 一般性指標+強指標「行內訂條款A04-承辦案件的地政士有受警示紀錄」觸及40%，須改為授權外權限承做
	 */
	public static boolean headAccount_HaUnauthorizeIndicator_cross_AuthLvl_1_Threshold(Brmp002O brmpOutput, int percent){
		Brmp002O_resultObj brmp_result = brmpOutput.getResult();
		if(brmp_result!=null){
			BigDecimal haUnauthorizeIndicator = brmp_result.getHaUnauthorizeIndicator();
			if(haUnauthorizeIndicator!=null && ClsUtility.get_percentSignal_value(haUnauthorizeIndicator).compareTo(new BigDecimal(percent))>=0){
				return true;
			}	
		}
		return false;
	}
	
	public static boolean is_l140mc1a_resultVal_complete(List<L140MC1A> l140mc1a_list){
		int l140mc1a_list_size = l140mc1a_list.size();
		if(l140mc1a_list_size==0){
			return false;
		}else{
			int mc1a_normal_result = 0;
			for(L140MC1A l140mc1a: l140mc1a_list){
				if(Util.isNotEmpty(Util.trim(l140mc1a.getResult()))){
					++mc1a_normal_result;
				}else{
					//若 決策平台 執行失敗，回傳的 status=M:Missing => 在寫入 l140mc1a.result 會是空白
				}
			}
			
			if(l140mc1a_list_size!=mc1a_normal_result){
				return false;
			}else{
				return true;
			}
		}		
	}
	
	public static boolean is_Brmp001O_ok(Brmp001O brmpObj){
		if(!"ok".equals(brmpObj.getStat())){
			return false;	
		}
		if(brmpObj.getFailedSystem()!=null && brmpObj.getFailedSystem().size()>0){
			return false;	
		}
		if(Util.isNotEmpty(Util.trim(brmpObj.getErrorCode()))){
			return false;	
		}
		if(Util.isNotEmpty(Util.trim(brmpObj.getErrorMsg()))){
			return false;	
		}
		return true;
	}
	
	public static boolean is_Brmp002O_ok(Brmp002O brmpObj){
		if(!"ok".equals(brmpObj.getStat())){
			return false;	
		}
		if(brmpObj.getFailedSystem()!=null && brmpObj.getFailedSystem().size()>0){
			return false;	
		}
		if(Util.isNotEmpty(Util.trim(brmpObj.getErrorCode()))){
			return false;	
		}
		if(Util.isNotEmpty(Util.trim(brmpObj.getErrorMsg()))){
			return false;	
		}
		return true;
	}
	
	public static boolean is_Brmp003O_ok(Brmp003O brmpObj){
		if(!"ok".equals(brmpObj.getStat())){
			return false;	
		}
		if(Util.isNotEmpty(Util.trim(brmpObj.getErrorCode()))){
			return false;	
		}
		if(Util.isNotEmpty(Util.trim(brmpObj.getErrorMsg()))){
			return false;	
		}
		return true;
	}

	public static boolean is_Brmp005O_ok(Brmp005O brmpObj){
		if(!"ok".equals(brmpObj.getStat())){
			return false;
		}
		if(brmpObj.getFailedSystem()!=null && brmpObj.getFailedSystem().size()>0){
			return false;
		}
		if(Util.isNotEmpty(Util.trim(brmpObj.getErrorCode()))){
			return false;
		}
		if(Util.isNotEmpty(Util.trim(brmpObj.getErrorMsg()))){
			return false;
		}
		return true;
	}

	public static boolean brmp_BaseRateCode_L140S02D_rateFlag2(String baseRateCode){
		if(CrsUtil.inCollection(baseRateCode, new String[]{CrsUtil.RATE_TYPE_M3, CrsUtil.RATE_TYPE_N2, CrsUtil.RATE_TYPE_MR})){ //{N2-行員理財型貸款利率, M3-行員消貸利率, MR-新行員利率}
			return true;
		}
		return false;
	}

	public static Set<String> get_s01r_out_authority_grade_6_to_10(){
		HashSet<String> set = new HashSet<String>();
		for (int i = 6; i <= 10; i++) {
			set.add(String.valueOf(i));
		}
		return set;
	}
	public static Set<String> get_s01r_out_authority_grade_prodKind71(String brNo){
		HashSet<String> out_authority_prodKind71 = new HashSet<String>();
		if(Util.equals("229", brNo)){
			//程修(110)2899  自2021-10-06 起，大安分行 可做「歡喜信貸」1~10等
		}else{
			//其它分行仍照舊，只可做「歡喜信貸」1~8等
			//修程J-111-0010，自2022-01-21起，所有分行皆可做「歡喜信貸」1~10等
//			for (int i = 9; i <= 10; i++) {
//				out_authority_prodKind71.add(String.valueOf(i));
//			}
		}
		return out_authority_prodKind71;
	}
	
	
	/*可用 c122m01e.refMainId 去 left outer join c122m01a.mainId
	
	public static String get_match_C122M01E_grpCntrNo_by_C122M01A(C122M01A c122m01a){
		if(c122m01a!=null && Util.equals(get_ploanPlan_ChinaSteelCorp_HQ(), c122m01a.getPloanPlan())){
			Timestamp c122m01a_applyTs = c122m01a.getApplyTS();
			if(LMSUtil.cmpDate(c122m01a_applyTs, ">=", CapDate.parseDate("2021-11-01")) && LMSUtil.cmpDate(c122m01a_applyTs, "<=", CapDate.parseDate("2021-12-01"))){
				return CSC_GRPCNTRNO_PERIOD_046; //中鋼第46次消費性貸款
			}	
		}		
		return "";	
	}*/
	
	public static Map<String, String> get_ctrParam_from_C122M01A(C122M01A c122m01a){
		Map<String, String> result = new HashMap<String, String>();
		String grpCntrNo = "";
		String rptVer = "";
		String ctrCheckDate = "";
		String ctrYear = "";
		String ctrNumberOfConsumerLoan_numeric = "";
		String ctrNumberOfConsumerLoan_twNumber = "";
		String preliminaryFee = ""; //開辦費
		String creditCheckFee = "";
		String advancedBaseRateChineseName = "";
		String advancedBaseRate = "";
		String advancedPmRate = "";
		String advancedNowRate = "";
		String advancedAPR = "";
		String courtName = "";
		String rateChgRange = "";//借款利率調整區間
		//===============
		String ploanPlan = Util.trim(c122m01a.getPloanPlan());		
		String agreeQueryEJDate = c122m01a.getAgreeQueryEJTs() != null ? TWNDate.toAD(c122m01a.getAgreeQueryEJTs()) : "";//簽署日期
		
		if(c122m01a!=null && c122m01a.getApplyTS()!=null 
				&& Util.isNotEmpty(ploanPlan) &&
				(Util.equals(get_ploanPlan_ChinaSteelCorp_HQ(), ploanPlan) || get_ploanPlan_ChinaSteelCorp_subsidinary().contains(ploanPlan))){ //中鋼集團 C001~c020
			Timestamp c122m01a_applyTs = c122m01a.getApplyTS();
			if(LMSUtil.cmpDate(c122m01a_applyTs, ">=", CapDate.parseDate("2021-11-01")) && LMSUtil.cmpDate(c122m01a_applyTs, "<=", CapDate.parseDate("2021-12-01"))){
				//自從 第46次消費性貸款    918111000325 ，才開始線上申貸
				grpCntrNo = CSC_GRPCNTRNO_PERIOD_046;
				rptVer = "046"; //因不同次的中鋼集團消貸, 契約書的內容會變, 在 rpt 的檔名, 用3碼來編碼(CLS3401R07C046_zh_TW.rpt 或  CLS3401R07D046_zh_TW.rpt)
				//若第47次的契約內容，與第46次不同，可另建新的 rpt 檔 CLS3401R07C047_{locale}.rpt
				//~~~~~~~~
				ctrCheckDate = "2021-11-01";
				ctrYear = "110";//002分行，蘇科長回覆：契約攜回日 不是 進件日期，中鋼總公司公告是110.11.01 
				ctrNumberOfConsumerLoan_numeric = "46"; //印在 PDF 的
				ctrNumberOfConsumerLoan_twNumber = "四十六"; //印在 PDF 的
				//~~~~~~~~
				preliminaryFee = "0";
				creditCheckFee = "0";
				//~~~~~~~~
				advancedBaseRateChineseName = "月變動消費金融指標利率";
				advancedBaseRate = "0.84";
				advancedPmRate = "0.4"; //實際的加碼利率，不能單純按{簽報書}的批覆內容，要問 002分行。可能簽案時寫{加0.21%}，但去中鋼投標時是{加0.4%}
				advancedNowRate = "1.24";
				advancedAPR = "1.24";
				//~~~~~~~~
				courtName = "臺灣高雄地方法院";
			}
			// 第48次消費性貸款    918111200181 線上進件日期要設定在中鋼(總公司+子公司)可以線上進件的區間
			if(LMSUtil.cmpDate(c122m01a_applyTs, ">=", CapDate.parseDate("2023-11-01")) && LMSUtil.cmpDate(c122m01a_applyTs, "<=", CapDate.parseDate("2023-12-01"))){
				//自從 第46次消費性貸款，才開始開放線上申貸
				grpCntrNo = CSC_GRPCNTRNO_PERIOD_048;
				rptVer = "048"; //因不同次的中鋼集團消貸, 契約書的內容會變, 在 rpt 的檔名, 用3碼來編碼(CLS3401R07C048_zh_TW.rpt &  CLS3401R07D048_zh_TW.rpt)
				//~~~~~~~~
				ctrCheckDate = "2023-11-01";//契約攜回日(不是 進件日期，中鋼總公司公告 112.11.01)  由港都提供
				ctrYear = "112";//契約書頁尾的年份
				ctrNumberOfConsumerLoan_numeric = "48"; //印在 PDF 的
				ctrNumberOfConsumerLoan_twNumber = "四十八"; //印在 PDF 的
				//~~~~~~~~
				preliminaryFee = "0";//開辦手續費
				creditCheckFee = "0";//信用查詢費
				rateChgRange = "6";//借款利率調整區間
				//~~~~~~~~
				advancedBaseRateChineseName = "月變動消費金融指標利率";
				advancedBaseRate = "1.593";//訂約時乙方消費金融放款指標利率為年利率 ???% 由港都提供
				advancedPmRate = "0.44"; //實際的加碼利率，不能單純按{簽報書}的批覆內容，要問 002分行。可能簽案時寫{加0.21%}，但去中鋼投標時是{加0.4%} 由港都提供
				advancedNowRate = "2.033";//-->>rpt沒有用到，發信的時候才要(PLOAN016)
				advancedAPR = "2.033";//總費用年百分率(由港都提供)
				//~~~~~~~~
				courtName = "臺灣高雄地方法院";
			}// 第48次消費性貸款 end
			// 第49次消費性貸款    918111300276 線上進件日期要設定在中鋼該年度(總公司+子公司)可以線上進件的區間前後
			if(LMSUtil.cmpDate(c122m01a_applyTs, ">=", CapDate.parseDate("2024-10-01")) && LMSUtil.cmpDate(c122m01a_applyTs, "<=", CapDate.parseDate("2024-12-15"))){
				//自從 第46次消費性貸款，才開始開放線上申貸
				grpCntrNo = CSC_GRPCNTRNO_PERIOD_049;
				rptVer = "049"; //因不同次的中鋼集團消貸, 契約書的內容會變, 在 rpt 的檔名, 用3碼來編碼(CLS3401R07C049_zh_TW.rpt &  CLS3401R07D049_zh_TW.rpt)
				//~~~~~~~~
				ctrCheckDate = "2024-11-01";//契約攜回日(不是 進件日期，中鋼總公司公告 113.11.01)  由港都提供
				ctrYear = "113";//契約書頁尾的年份
				ctrNumberOfConsumerLoan_numeric = "49"; //印在 PDF 的
				ctrNumberOfConsumerLoan_twNumber = "四十九"; //印在 PDF 的
				//~~~~~~~~
				preliminaryFee = "0";//開辦手續費
				creditCheckFee = "0";//信用查詢費
				rateChgRange = "6";//借款利率調整區間
				//~~~~~~~~
				advancedBaseRateChineseName = "月變動消費金融指標利率";
				advancedBaseRate = "1.718";//訂約時乙方消費金融放款指標利率為年利率 ???% 由港都提供
				advancedPmRate = "1.282"; //實際的加碼利率，不能單純按{簽報書}的批覆內容，要問 002分行。可能簽案時寫{加0.21%}，但去中鋼投標時是{加0.4%} 由港都提供
				advancedNowRate = "3";//-->>rpt沒有用到，發信的時候才要(PLOAN016)
				advancedAPR = "3";//總費用年百分率(由港都提供)
				//~~~~~~~~
				courtName = "臺灣高雄地方法院";
			}// 第49次消費性貸款 end
			
			//在 第45次 918110900285 以前，都是紙本申貸
		}
		//===============
		result.put("grpCntrNo", grpCntrNo);
		result.put("rptVer", rptVer);
		result.put("ctrCheckDate", ctrCheckDate);
		result.put("ctrYear", ctrYear);
		result.put("ctrNumberOfConsumerLoan_numeric", ctrNumberOfConsumerLoan_numeric);
		result.put("ctrNumberOfConsumerLoan_twNumber", ctrNumberOfConsumerLoan_twNumber);
		result.put("preliminaryFee", preliminaryFee);
		result.put("creditCheckFee", creditCheckFee);
		result.put("rateChgRange", rateChgRange);
		result.put("advancedBaseRateChineseName", advancedBaseRateChineseName);
		result.put("advancedBaseRate", advancedBaseRate);
		result.put("advancedPmRate", advancedPmRate);
		result.put("advancedNowRate", advancedNowRate);
		result.put("advancedAPR", advancedAPR);
		result.put("courtName", courtName);
		result.put("agreeQueryEJDate", agreeQueryEJDate);
		return result;
	}
	
	public static String get_ploanPlan_C101(){
		return "C101";	//精銳
	}
	
	public static String get_ploanPlan_ChinaSteelCorp_HQ(){
		return "C001";	//中鋼總公司
	}
	
	public static Set<String> get_ploanPlan_ChinaSteelCorp_subsidinary(){
		Set<String> result = new HashSet<String>();
		//SELECT * FROM com.bcodetype WHERE codetype = 'ploan_plan'
		
		result.add("C002");	//中鋼消貸(中鋼工會)	76214988
		result.add("C003");	//中鋼消貸(中保)	16099725
		result.add("C004");	//中鋼消貸(鋼堡)	16801739
		result.add("C005");	//中鋼消貸(鋼構)	85800192
		result.add("C006");	//中鋼消貸(中運)	96973086
		result.add("C007");	//中鋼消貸(中貿)	96975583
		result.add("C008");	//中鋼消貸(中冠)	70767857
		result.add("C009");	//中鋼消貸(中機)	70818455
		result.add("C010");	//中鋼消貸(中鋁)	96971313
		result.add("C011");	//中鋼消貸(中龍)	84613756
		result.add("C012");	//中鋼消貸(網優)	70762419
		result.add("C013");	//中鋼消貸(中碳)	23221384
		result.add("C014");	//中鋼消貸(興達海基)	42816688
		result.add("C015");	//中鋼消貸(智揚)	89761328
		result.add("C016");	//中鋼消貸(中聯資)	86119908
		result.add("C017");	//中鋼消貸(中鴻)	07838854
		result.add("C018");	//中鋼消貸(中宇)	84252401
		result.add("C019");	//中鋼消貸(高捷)	70798839
		result.add("C020");	//中鋼消貸(聯鋼)	85975087
		// J-112-0390 新增中鋼子公司
		result.add("C021");	//中鋼消貸(台灣智能) ********
		result.add("C022");	//中鋼消貸(中鋼光能) ********
		result.add("C023");	//中鋼消貸(中鋼幼兒園) ********
		result.add("C024");	//中鋼消貸(鋼友旅行) ********
		result.add("C025");	//中鋼消貸(超揚投資) ********
		result.add("C026");	//中鋼消貸(運盈投資) ********
		result.add("C027");	//中鋼消貸(中盈投資) ********

		return result;		
	}
	
	/**
	 * AP-CODE 09 可能是公司戶、也可能是個人戶 <br/>
	 * 參考  ＜entry key="IDDP0001.getAccount"＞ 的SQL寫法
	 * <ul>
	 * <li>SELECT MR_PB_ID_NO, MR_PB_ACT_NO, MR_PB_TAX_NO	FROM DWADM.IDDP0001 <br/>
	 * WHERE MR_PB_ID_NO like 'F127%' AND MR_PB_CURR_CODE='00' and substr(MR_PB_ACT_NO, 4, 2)  ='09'
	 * </li>
	 * </ul> 
	 */
	public static boolean ctr_acctNo_APCODE_09_forPersoanlLoanContract(String acctNo, String taxNo){
		if(Util.equals("09", get_acctNo_APCODE(acctNo))){
			if(Util.isEmpty(Util.trim(taxNo))){
				return true;
			}
		}
		return false;
	}
	
	public static String get_acctNo_APCODE(String acctNo){
		return Util.trim(StringUtils.substring(acctNo, 3, 5));
	}
	
	public static boolean isPureNumber(String s){
		String[] valid_Char = new String[]{"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"};
		int length = s.length();
		for(int i=0;i<length;i++){
			if(ArrayUtils.indexOf(valid_Char, s.substring(i, i+1)) == -1){
				return false;
			}
		}
		return true;
	}	
	public static String convert_to_ploanDateStr(Date d){
		if(d==null){
			return "";
		}else{
			return TWNDate.toAD(d).replace("-", "/");
		}
	}


	public static L140S02N getL140S02N_by_itemType(List<L140S02N> l140s02n_list, String itemType){
		for(L140S02N l140s02n : l140s02n_list){
			if(Util.equals(l140s02n.getItemType(), itemType)){
				return l140s02n;
			}
		}
		return null;
	}

	public static boolean needRedoAction_paramDateNotNullAndBeforeNowDayCnt(Date paramDate, int beforeNowDaycnt){
		if(paramDate==null){
			return false;
		}else{
			return LMSUtil.cmpDate(paramDate, "<" , CapDate.shiftDays(CapDate.getCurrentTimestamp(), beforeNowDaycnt));
		}
	}

	public static BigDecimal calc_nowRate(String rateType, String rateUserType
			, String rateFlag, BigDecimal l140s02d_nowRate, BigDecimal latestMisBaseRate, String pmFlag, BigDecimal pmRate){
		boolean use_default = true;
		if (CrsUtil.RATE_TYPE_01.equals(rateType) && Util.equals("C01", rateUserType)) {
			//當 固定利率
		}else{
			if(Util.equals("1", rateFlag)){
				
			}else{
				use_default = false;
			}	
		}
		
		if(use_default){
			return l140s02d_nowRate;
		}else{
			if(latestMisBaseRate==null){
				return null;
			}else{
				if(pmRate!=null){
					if("P".equals(pmFlag)){
						return latestMisBaseRate.add(pmRate);
					}else if("M".equals(pmFlag)){
						return latestMisBaseRate.subtract(pmRate);
					}
				}
				return latestMisBaseRate;
			}	
		}
	}

	private static void _mkLogDir(String fileName)throws IOException {
		FileUtils.forceMkdir(new File(FilenameUtils.getFullPath(fileName)));		
	}

	public static void writeToLog(String fileName, String line, String encoding) throws IOException {
		_mkLogDir(fileName);
		
		boolean written = false;
		FileOutputStream fos = null;
		Writer osw = null;
		try {
			fos = new FileOutputStream(fileName, true);
			osw = new BufferedWriter(new OutputStreamWriter(fos, encoding));
			//---
			int retryCnt = 100;
			for(int i=0;i<retryCnt;i++){
				try {
					FileLock lock = fos.getChannel().lock();					
					try {	
						osw.write(line);
						osw.flush();
						
						written = true;
					} catch(Exception e){
						logger.error("writeToLog["+(i+1)+"/"+retryCnt+"]"+e.getMessage());
					} finally {
						lock.release();
					}
				} catch (OverlappingFileLockException ofle) {
					try {
						Thread.sleep(100);
					} catch (InterruptedException ex) {
						throw new InterruptedIOException("wait FileLock:"+fileName);
					}
				}
				if(written){
					break;
				}
			}
			//---
			osw.close();
			fos.close();
		}finally{
			IOUtils.closeQuietly(osw);
			IOUtils.closeQuietly(fos);
		}
		if(!written){
			throw new IOException("[" +line+"] not write to:"+fileName);
		}
	}
	
	public static String build_CLS1151R01___payway_l140s02asize_gt1(List<L140S02A> l140s02as, Map<Integer, String> seq_printStrMap){
		String payWay = "";
		
		if (l140s02as.size() > 1) {
			List<Map<String, String>> payWaylist = new ArrayList<Map<String, String>>();

			
			for (L140S02A l140s02a : l140s02as) {
				String seqStr = "";
				if (seq_printStrMap.containsKey(l140s02a.getSeq())) {
					seqStr = seq_printStrMap.get(l140s02a.getSeq());
				}
				
				String lnYear = Util.trim(l140s02a.getLnYear());
				String lnMonth = Util.trim(l140s02a.getLnMonth());
				String other = Util.trim(l140s02a.getLnOther());

				if ((Util.isNotEmpty(lnYear) && Util.isNotEmpty(lnMonth))
						|| Util.isNotEmpty(other)) {

					if (ProdKindEnum.企金科目.getCode().equals(
							l140s02a.getProdKind())) {
					} else {
						boolean paywayboo = true;
						for (Map<String, String> payWaymap : payWaylist) {
							String Paywaystring = "";
							Paywaystring = Util.trim(l140s02a.getPayoffWay());
							if (Util.equals(Paywaystring,
									payWaymap.get("pagway"))) {
								payWaymap.put("pagwayId",
										payWaymap.get("pagwayId") + "、"
												+ seqStr);
								paywayboo = false;
							}
						}
						if (paywayboo) {
							Map<String, String> payWaymap = new HashMap<String, String>();
							String seq = seqStr;
							String Paywaystring = "";
							Paywaystring = Util.trim(l140s02a.getPayoffWay());
							payWaymap.put("pagwayId", seq);
							payWaymap.put("pagway", Paywaystring);
							payWaylist.add(payWaymap);
						}
					}
				}
			}

			for (Map<String, String> payWaymap : payWaylist) {
				payWay = payWay + payWaymap.get("pagwayId") + ":"
						+ payWaymap.get("pagway") + " ";
			}
		}		
		return payWay;
	}
	
	public static String build_CLS1151R01___payway_l140s02asize_notgt1(List<L140S02A> l140s02as){
		String payWay = "";
		if (!l140s02as.isEmpty()) {
			L140S02A l140s02a = l140s02as.get(0);

			if (ProdKindEnum.企金科目.getCode().equals(l140s02a.getProdKind())) {
				payWay = "";
			} else {
				payWay = Util.trim(l140s02a.getPayoffWay());
			}
		}else{
			
		}
		return payWay;
	}

	/** https://github.com/apache/commons-lang/blob/master/src/main/java/org/apache/commons/lang3/StringUtils.java 
	 * @param str
	 * @return
	 */
	public static String commons_lang3_StringUtils_normalizeSpace(final String str) {        
        if (StringUtils.isEmpty(str)) {
            return str;
        }
        final int size = str.length();
        final char[] newChars = new char[size];
        int count = 0;
        int whitespacesCount = 0;
        boolean startWhitespaces = true;
        
        String SPACE = " ";
        for (int i = 0; i < size; i++) {
            final char actualChar = str.charAt(i);
            final boolean isWhitespace = Character.isWhitespace(actualChar);
            if (isWhitespace) {
                if (whitespacesCount == 0 && !startWhitespaces) {
                    newChars[count++] = SPACE.charAt(0);
                }
                whitespacesCount++;
            } else {
                startWhitespaces = false;
                newChars[count++] = (actualChar == 160 ? 32 : actualChar);
                whitespacesCount = 0;
            }
        }
        if (startWhitespaces) {
            return StringUtils.EMPTY;
        }
        return new String(newChars, 0, count - (whitespacesCount > 0 ? 1 : 0)).trim();
    }
}
