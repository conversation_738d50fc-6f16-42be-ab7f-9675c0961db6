package com.mega.eloan.lms.dw.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dw.service.DwBGMOPENXService;

/**
 * 全國營業(稅籍)已停業登記資料 DWADM.OTS_DW_BGMOPENX
 */
@Service
public class DwBGMOPENXServiceImpl extends AbstractDWJdbc implements
		DwBGMOPENXService {

	/**
	 * 取得全國營業(稅籍)登記資料
	 * 
	 * @param custId
	 * @return
	 */
	@Override
	public Map<String, Object> getDataFromTaxation(String custId) {
		return this.getJdbc().queryForMap("OTS_DW_BGMOPENXByCustId",
				new String[] { custId });
	}
}
