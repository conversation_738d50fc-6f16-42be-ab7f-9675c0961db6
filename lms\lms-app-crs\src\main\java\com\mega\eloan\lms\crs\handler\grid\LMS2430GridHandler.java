package com.mega.eloan.lms.crs.handler.grid;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;


import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.model.C243M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;


@Scope("request")
@Controller("lms2430gridhandler")
public class LMS2430GridHandler extends AbstractGridHandler {
		
	@Resource
	RetrialService retrialService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	BranchService branchService;
	
	private static final DateFormat S_FORMAT = new SimpleDateFormat(UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
	
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryMain(ISearch pageSetting,
			PageParameters params) throws CapException {
		
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());	
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);		
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		
		// 借款人統編篩選 - 前後模糊查詢
		String custId = Util.trim(params.getString("search_custId"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId", "%" + custId + "%");
		}	
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Page<? extends GenericBean> page =  retrialService.findPage(C243M01A.class, pageSetting);
		List<C243M01A> src_list = (List<C243M01A>)page.getContent();		
		for(C243M01A c243m01a : src_list){
			Map<String, Object> row = new HashMap<String, Object>();
			LMSUtil.meta_to_map(row, c243m01a, new String[] { "oid", "mainId",
					"custName",  "elfCrDate", "chgCrDate", "chgReason" });
			IBranch ibranch = branchService.getBranch(c243m01a.getElfBranch());
			if (ibranch != null) {
				row.put("elfBranch",
						c243m01a.getElfBranch() + " " + ibranch.getBrName());
			}

			row.put("custId",
					Util.trim(c243m01a.getCustId()) + "-"
							+ Util.trim(c243m01a.getDupNo()));
			row.put("updater",
					Util.trim(userInfoService.getUserName(c243m01a.getUpdater())));
			row.put("approver",
					Util.trim(userInfoService.getUserName(c243m01a.getApprover())));
			row.put("approveTime", c243m01a.getApproveTime()==null?"":S_FORMAT.format(c243m01a.getApproveTime()));
			// ---
			list.add(row);
		}
	
		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				list, page.getTotalRow(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());
	}
}
