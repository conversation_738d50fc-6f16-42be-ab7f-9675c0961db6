package com.mega.eloan.lms.model;

import javax.persistence.metamodel.ListAttribute;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * The persistent class for the C140M07A database table.
 * </pre>
 * @since  2011/10/04
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/10/04,TimChiang,new
 *          </ul>
 */
@StaticMetamodel(C140M07A.class)
public class C140M07A_  extends RelativeMeta_{
	public static volatile SingularAttribute<C140M07A, String> tab;
	public static volatile SingularAttribute<C140M07A, String> subtab;
	public static volatile SingularAttribute<C140M07A, String> flag;
	public static volatile SingularAttribute<C140M07A, String> uid;
	public static volatile ListAttribute<C140M07A, C140JSON> c140jsons;
	public static volatile SingularAttribute<C140M07A, C140M01A> c140m01a;
	public static volatile ListAttribute<C140M07A, C140S07A> c140s07as;
}
