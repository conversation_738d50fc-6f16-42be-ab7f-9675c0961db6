package com.mega.eloan.lms.lms.service;

/* 
 * LMS7830Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;

/**
 * <pre>
 * 簽報案件查詢
 * </pre>
 * 
 * @since 2011/12/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/12,REX,new
 *          </ul>
 */
public interface LMS7830Service {

	/**
	 * 查詢簽報書案件
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重複序號
	 * @param search
	 *            搜尋設定
	 * @return grid 資料
	 */
	Page<Map<String, Object>> queryData(String custId, String dupNo,
			ISearch search);

	/**
	 * 查詢客戶資料
	 * 
	 * @param custId
	 *            客戶統編
	 * @return 客戶資料map
	 */
	List<Map<String, Object>> queryCustData(String custId);

}