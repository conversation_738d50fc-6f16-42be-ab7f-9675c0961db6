/* 
 * C242M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C242M01A;

/** 覆審預約單檔 **/
public interface C242M01ADao extends IGenericDao<C242M01A> {

	C242M01A findByOid(String oid);
}