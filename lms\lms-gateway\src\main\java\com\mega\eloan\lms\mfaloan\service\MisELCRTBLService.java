/* 
 * MisELCRTBService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services,String  Inc. 
 * 9F,String  No.30,String  Sec.1,  Ming Sheng E. Rd.,  Taipei,  Taiwan,  R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services,  Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services,  Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.util.List;

/**
 * <pre>
 * 企金費率檔ELCRTBL(MIS.ELV47601)
 * </pre>
 * 
 * @since 2012/01/01
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/01/01, REX, new
 *          </ul>
 */
public interface MisELCRTBLService {
	/**
	 * 
	 * 新增
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @param subject
	 *            科目
	 * @param sDate
	 *            核准日期
	 * @param rType
	 *            保證/費率種類 1:商業本票 2.開發保證函 3.公司債保證 4.承兌費率
	 * 
	 * @param cp_Type
	 *            商業本票種類
	 * @param cp1_Rate
	 *            年費率1
	 * @param cp1_Fee
	 *            最低收費新台幣X元
	 * @param cp2_Rate1
	 *            年費率2
	 * @param cp2_Rate2
	 *            若由本行承銷則年費率X
	 * @param cp_Des
	 *            商業本票文字敘述
	 * @param cf_Type
	 *            開發保證函種類
	 * @param cf1_Rate
	 *            年費率1
	 * @param cf1_mth1
	 *            每X個月為一期按期計收
	 * @param cf1_md
	 *            計收方法
	 * @param cf1_mth2
	 *            X月
	 * @param cf2_Rate
	 *            年費率2
	 * @param cf2_md
	 *            計收方法
	 * @param cf2_mth
	 *            X月
	 * @param cf_des
	 *            開發保證函文字敘述
	 * @param cpy_type
	 *            公司債保證種類
	 * @param cpy1_rate
	 *            年費率1
	 * @param cpy1_mth1
	 *            每X個月為一期按期計收
	 * @param cpy1_md
	 *            計收方法
	 * @param cpy1_mth2
	 *            X月
	 * @param cpy2_rate
	 *            年費率2
	 * @param cpy2_md
	 *            計收方法
	 * @param cpy2_mth
	 *            X月
	 * @param cpy_des
	 *            公司債文字敘述
	 * @param pa_type
	 *            承兌費率
	 * @param pa1_rate
	 *            年費率1
	 * @param pa1_md
	 *            計收方法
	 * @param pa1_mth
	 *            X月
	 * @param pa2_rate
	 *            年費率2
	 * @param pa2_md
	 *            計收方法
	 * @param pa2_mth
	 *            X月
	 * @param pa_des
	 *            承兌費率文字敘述
	 * @param updater
	 *            資料修改人（行員代號）
	 */
	void insert(List<Object[]> dataList);

	/**
	 * 刪除
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @param subject
	 *            授信科目
	 * @param sDate
	 *            系統時間
	 * @param rtype
	 *            類別
	 */
	void delByKey(String cntrNo, String subject, String sDate, String rtype);
}
