var initDfd = $.Deferred(), inits = {
    fhandle: "lms8000m01formhandler",
    ghandle: "lms8000gridhandler"
};
//
//select source     CommonAPI.loadOrderCombosAsList
var result_s = CommonAPI.loadOrderCombosAsList(["postLoan_handlingStatus", "postLoan_followWay",
    "postLoan_followKind", "postLoan_staff", "postLoan_remitType", "c101m06_rasp_status", "c101m06_rasp_way",
    "l260s01fESGoption", "lms140_esgSustainLoanType"
    ]);

// J-112-0307 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
// 追蹤項目、追蹤紀錄中某一筆資料的ID階層
var oneRowByIdFlag;
//追蹤項目、追蹤紀錄中某一筆資料的資料來源
var oneRowDataFrom;

//J-113-0192 Web eloan擔保品系統配合授審處貸後管理規範，調整現行貸後管理系統中自動發查不動產謄本規則
var oneRowDataCaseMark;

var Action = {
	_isLoad: false,
	l260m01cGrid: null,
	l260m01dGrid: null,
	undoneElf602Grid: null,
	FormData: {},
	initUser : function(){
        $.ajax({
            handler: "codetypehandler",
            action: "userByBrnoTellerId1",//"userByBranch",
            async: false,
            success: function(json){
                $("#fo_staffNo").setItems({
                    item: json,
                    format: "{value} - {key}"
                });
                $("#ao_staffNo").setItems({
                    item: json,
                    format: "{value} - {key}"
                });
            }
        });
    },
    _initItem: function(dataOid){
        $("#handlingStatus").setItems({
            item: convertItems(result_s.postLoan_handlingStatus),
            format: "{key}"
        });
        $("select#handlingStatus option[value=4]").remove();
        
        $("#staff").setItems({
            item: convertItems(result_s.postLoan_staff),
            format: "{key}"
        });
        $("#followStaff").setItems({
            item: convertItems(result_s.postLoan_staff),
            format: "{key}"
        });
        $("#followKind").setItems({
            item: convertItems(result_s.postLoan_followKind),
            format: "{key}",
            size: 5,
            maxlength: 2
        });
        $("#followWay").setItems({
            item: convertItems(result_s.postLoan_followWay),
            format: "{key}"
        });
        $("#remitType").setItems({
            item: convertItems(result_s.postLoan_remitType),
            format: "{value}：{key}"
        });

        $("#raspStat").setItems({
            item: convertItems(result_s.c101m06_rasp_status),
            format: "{key}",
            space: true
        });
        $("#raspWay").setItems({
            item: convertItems(result_s.c101m06_rasp_way),
            format: "{value} - {key}",
            space: true
        });
        
        Action._initForm();
    },
    _initForm: function(){
        $.form.init({
            formHandler: inits.fhandle,
            formAction: 'queryL260m01a',
            loadSuccess: function(json){
                $('body').injectData(json);
                if ((responseJSON.newType && responseJSON.newType == "2") ||
                (json.loanNo && json.loanNo != "")) {
                    $("#showloanNo").show();
                }
                else {
                    $("#showloanNo").hide();
                }
                if (json.showImpL260M01C && json.showImpL260M01C == "2") {
                	$("#impL260M01C").show();
                } else {
                	$("#impL260M01C").hide();
                }

                if ( typeof json.rtnModifyReason !== "undefined" && json.rtnModifyReason !== ""  ) {
                     $("#showRtnModifyReason").show();
                } else {
                     $("#showRtnModifyReason").hide();
                }
                
                /*
                if(json.overSeaHide && json.overSeaHide == "Y"){
                    $("#overSeaHideVal").val("Y");
                } else {
                    $("#overSeaHideVal").val("N");
                }
                */
                Action.setOverSeaHideUI(userInfo.isOverSea);
                Action._initGrid();
                Action._initEvent();
            }
        });
    },
    _initEvent: function(){
        $("#addL260M01C").click(function(){
            $("#addById").thickbox({
                title: i18n.lms8000m01['addById'],
                width: 100,
                height: 20,
                align: "center",
                valign: "bottom",
                i18n: i18n.def,
                open: function(){
                    var isIdLevel = false;
                    var caseCntrNo = $.trim($("#cntrNo").val());
                    if (caseCntrNo == "" || caseCntrNo == null) {
                        isIdLevel = true;
                    }
                    $("input[name='byIdFlag']").prop('checked', false);
                    $("input[name='byIdFlag']").prop('disabled', false);
                    if(isIdLevel){
                        $("input[name='byIdFlag'][value='Y']").prop("checked", true);
                        $("input[name='byIdFlag'][value='N']").prop('disabled', true);
                    }
                },
                buttons: {
                    "sure": function(){
                        var byIdFlag = $("[name=byIdFlag]:checked").val();
                        if (byIdFlag == "") {
                            return CommonAPI.showErrorMessage(i18n.lms8000m01["checkSelect"] + i18n.lms8000m01["addById"]);
                        }
                        var caseCntrNo = $.trim($("#cntrNo").val());
                        if (caseCntrNo == "" || caseCntrNo == null) {
                            if (byIdFlag == "N") {
                                // 維護案無額度序號  不能選非ID階層
                                // checkMsg05=ID階層不得為否
                                return CommonAPI.showErrorMessage(i18n.lms8000m01["checkMsg05"]);
                            }
                        }
                        var oidL260M01Cadd = "";
                        $.ajax({
                            handler: inits.fhandle,
                            data: {
                                formAction: "addL260M01C",
                                mainId: $("#mainId").val(),
                                oid: responseJSON.oid,
                                byIdFlag: byIdFlag
                            },
                            success: function(obj){
                                $.thickbox.close();
                                $("#l260m01cGrid").trigger("reloadGrid");
                                oidL260M01Cadd = obj.oidL260M01C;

                                if(oidL260M01Cadd != undefined && oidL260M01Cadd != null && oidL260M01Cadd != ""){
                                    var rowObject = {};
                                    rowObject["oid"] = oidL260M01Cadd;
                                    rowObject["byIdFlag"] = byIdFlag;
                                    Action.openDetailBox("L260M01C", null, rowObject);
                                }
                            }
                        });

                        $.thickbox.close();
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        });
        
        $("#delL260M01C").click(function(){
            var row = $("#l260m01cGrid").getGridParam('selrow');
            
            if (!row) {
                return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            }
            else {
                CommonAPI.confirmMessage(i18n.lms8000m01["changeStatus"] + "　" + i18n.lms8000m01["confirmRun"], function(b){
                    if (b) {
                        var data = $("#l260m01cGrid").getRowData(row);
                        var oid = data.oid;
                        $.ajax({
                            handler: inits.fhandle,
                            data: {
                                formAction: "deleteL260M01C",
                                oid: oid
                            },
                            success: function(obj){
                                $("#l260m01cGrid").trigger("reloadGrid");
                            }
                        });
                    }
                });
            }
        });
        
        $("#impL260M01C").click(function(){
        	$.ajax({
				handler: inits.fhandle,
				data: {
				    formAction: "impL260M01C",
				    mainId: $("#mainId").val(),
				    oid: responseJSON.oid
				},
				success: function(obj){
				    $("#l260m01cGrid").trigger("reloadGrid");
			    }
			});
        });
        
        $("#addL260M01D").click(function(){
            $("#addFollowDate").thickbox({
                title: i18n.def['newData'],
                width: 300,
                height: 20,
                align: "center",
                valign: "bottom",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        var value = $.trim($("#addFoDate").val());

                        var regEx = /^\d{4}-\d{2}-\d{2}$/;

                        if (value == "" || value == null) {
                            return CommonAPI.showErrorMessage(i18n.lms8000m01['L260M01D.followDate'] + i18n.lms8000m01['notEmpty']);
                        }
                        if (value && !value.match(regEx)) {
                            return CommonAPI.showErrorMessage("日期格式錯誤(yyyy-MM-dd)");
                        }

                        var dateArr = value.split("-");
                        if (!Action.validateDate(dateArr[0], dateArr[1], dateArr[2])) {
                            return CommonAPI.showErrorMessage("日期錯誤");
                        }

                        $.thickbox.close();
                        $("#addById").thickbox({
                            title: i18n.lms8000m01['addById'],
                            width: 100,
                            height: 20,
                            align: "center",
                            valign: "bottom",
                            i18n: i18n.def,
                            open: function(){
                                var isIdLevel = false;
                                var caseCntrNo = $.trim($("#cntrNo").val());
                                if (caseCntrNo == "" || caseCntrNo == null) {
                                    isIdLevel = true;
                                }
                                $("input[name='byIdFlag']").prop('checked', false);
                                $("input[name='byIdFlag']").prop('disabled', false);
                                if(isIdLevel){
                                    $("input[name='byIdFlag'][value='Y']").prop("checked", true);
                                    $("input[name='byIdFlag'][value='N']").prop('disabled', true);
                                }
                            },
                            buttons: {
                                "sure": function(){
                                    var byIdFlag = $("[name=byIdFlag]:checked").val();
                                    if (byIdFlag == "") {
                                        return CommonAPI.showErrorMessage(i18n.lms8000m01["checkSelect"] + i18n.lms8000m01["addById"]);
                                    }
                                    var caseCntrNo = $.trim($("#cntrNo").val());
                                    if (caseCntrNo == "" || caseCntrNo == null) {
                                        if (byIdFlag == "N") {
                                            // 維護案無額度序號  不能選非ID階層
                                            // checkMsg05=ID階層不得為否
                                            return CommonAPI.showErrorMessage(i18n.lms8000m01["checkMsg05"]);
                                        }
                                    }
                                    var oidL260M01Dadd = "";
                                    $.ajax({
                                        handler: inits.fhandle,
                                        data: {
                                            formAction: "addL260M01D",
                                            mainId: $("#mainId").val(),
                                            oid: responseJSON.oid,
                                            foDate: value,
                                            byIdFlag: byIdFlag
                                        },
                                        success: function(obj){
                                            $.thickbox.close();
                                            $("#l260m01dGrid").trigger("reloadGrid");
                                            oidL260M01Dadd = obj.oidL260M01D;

                                            if(oidL260M01Dadd != undefined && oidL260M01Dadd != null && oidL260M01Dadd != ""){
                                                var rowObject = {};
                                                rowObject["oid"] = oidL260M01Dadd;
                                                rowObject["byIdFlag"] = byIdFlag;
                                                Action.openDetailBox("L260M01D", null, rowObject);
                                            }
                                        }
                                    });

                                    $.thickbox.close();
                                },
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        });
        
        $("#delL260M01D").click(function(){
            //            var rows = $("#l260m01dGrid").getGridParam('selarrrow');
            //            var data = [];
            //
            //            if (rows == "") {// action_005=請先選取一筆以上之資料列
            //                return CommonAPI.showMessage(i18n.def["action_005"]);
            //            }
            //            for (var i in rows) {
            //                data.push($("#l260m01dGrid").getRowData(rows[i]).oid);
            //            }
            var row = $("#l260m01dGrid").getGridParam('selrow');
            
            if (!row) {
                return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            }
            else {
                CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                    if (b) {
                        var data = $("#l260m01dGrid").getRowData(row);
                        var oid = data.oid;
                        $.ajax({
                            handler: inits.fhandle,
                            data: {
                                formAction: "deleteL260M01D",
                                oids: oid//oids: data
                            },
                            success: function(obj){
                                $("#l260m01dGrid").trigger("reloadGrid");
                            }
                        });
                    }
                });
            }
        });

        $("#impL260M01D").click(function(){
//            var row = $("#l260m01dGrid").getGridParam('selrow');
//
//            if(!row){
//                return CommonAPI.showMessage(i18n.def['grid.selrow']);
//            }else{
//                if (row.length > 1) {
//                    // checkMsg02=此功能只能選擇單筆
//                    return API.showMessage(i18n.lms8000m01["checkMsg02"]);
//                }
//
//                var rowData = $("#l260m01dGrid").getRowData(row);

                $("#undoneElf602Grid").jqGrid("setGridParam", {
                    postData: {
                        key: responseJSON.oid//rowData.oid
                    },
                    search: true
                }).trigger("reloadGrid");

                $("#undoneElf602View").thickbox({
                    title: "未完成追蹤紀錄",
                    width: 900,
                    height: 300,
                    modal: true,
                    buttons: {
                        "sure": function(){
                            var rows = $("#undoneElf602Grid").getGridParam('selarrrow');
                            var data = [];

                            if (rows == "") {   // action_005=請先選取一筆以上之資料列
                                return CommonAPI.showMessage(i18n.def["action_005"]);
                            }

                            for (var i in rows) {
                                data.push($("#undoneElf602Grid").getRowData(rows[i]).unid);
                            }

                            $.ajax({
                                handler: inits.fhandle,
                                data: {
                                    formAction: "importL260M01D",
                                    mainId: $("#mainId").val(),
                                    oids: data
                                },
                                success: function(obj){
                                    $("#l260m01dGrid").trigger("reloadGrid");
                                    $.thickbox.close();
                                    if (obj.raspMsg && obj.raspMsg != "") {
                                        CommonAPI.showMessage(i18n.def["runSuccess"] + "，" + obj.raspMsg);
                                    } else {
                                        CommonAPI.showMessage(i18n.def["runSuccess"]);
                                    }
                                }
                            });
                        },
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });
//            }
        });

        // J-109-0339 弱化惡化 檢核互斥
        $("input[name=followKind]").click(function(){
            var cols = ["W1", "W2", "W3", "D1", "D2", "D3"];
            var chkValue = DOMPurify.sanitize(String($(this).val()));
            // == -1 代表沒找到
            if ($.inArray(chkValue, cols) != -1) {
                var type = chkValue.substring(0, 1);
                var seq = chkValue.substring(1, 2);
                // 需要檢查
                if (this.checked) {
                    if (type == "W") {
                        $("[name=followKind][value='D" + seq + "']").removeAttr("checked").prop("disabled", "disabled");
                    }
                    else 
                        if (type == "D") {
                            $("[name=followKind][value='W" + seq + "']").removeAttr("checked").prop("disabled", "disabled");
                        }
                }
                else {
                    if (type == "W") {
                        $("[name=followKind][value='D" + seq + "']").removeAttr("disabled");
                    }
                    else 
                        if (type == "D") {
                            $("[name=followKind][value='W" + seq + "']").removeAttr("disabled");
                        }
                }
            }
        });
        
        // 檢視應注意/承諾/追蹤ESG連結條款明細內容
    	$('#btnViewESGContent').click(function(){      
    		
    		$.ajax({
			        handler : inits.fhandle,
			        type : "POST",
			        dataType : "json",
			        data : 
			        {
				       formAction : "viewESGContent",
				       oid: $("#oidL260M01D").val(),
				       brNo: userInfo.unitNo
			        },
			        success : function(json) {    		                   	  		                     					
					   $("#ESGContent").val(DOMPurify.sanitize(json.ESGContent));					 
			        }       				  			        
		     });
    		
    		
           	$("#viewESGContentThickbox").thickbox({
           		title : i18n.lms8000m01["L260S01F.thickbox1"],
           		width : 750,
           		height : 500,
           		modal : true,
           		align : 'center',
           		valign: 'bottom',
           		i18n:i18n.def,
           		buttons : {
           			"close" : function() {                    					               				
           				$.thickbox.close();		
           				$("#ESGContent").val('');		
           			}
           		}
           	});	
    		
        });
                                               
        // 列印公司訪問紀錄表
    	$('#btnprintVisitComp').click(function(){      
    		
    		$('#prtParam').val('R06');    
    		
    		if(checkReadonly()){
    			// 文件為唯讀時，直接印表    				
    			Action.printVisitComp();  	
    		}else{
                CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                    if (b) {                      	    
                    	    $('#saveData').click();                 
                    }
                });    			
    		}
    		
        });
    	
    	
    	// 引進單位主管、帳戶管理員
    	$('#btnimportMgrForm').click(function(){          				
			    
    		    var nowUnitMgr = $("#unitMgr_S01D").val();
    		    var nowAccountMgr = $("#accountMgr_S01D").val();
    		
	  		    $.ajax({
	  			        handler : inits.fhandle,
	  			        type : "POST",
	  			        dataType : "json",
	  			        data : 
	  			        {
	  				       formAction : "importMgr"               		
	  			        },
	  			        success : function(json) {    
	  			        	 
	  			               var unitMgr_S01DList = $("#_unitMgr");
	  		                   unitMgr_S01DList.setItems({
                                   item: json.unitMgr_S01DList,
	  		                           format: "{value} - {key}"
	  		                   });
	  		                       
	  		                   var accountMgr_S01DList = $("#_accountMgr");
	  		                   accountMgr_S01DList.setItems({
	  		                           item: json.accountMgr_S01DList,
	  		                           format: "{value} - {key}"
	  		                    });
	  		                   	  		                     					  
    						 $("#_unitMgr").val(nowUnitMgr);
    						 $("#_accountMgr").val(nowAccountMgr);
	  			         }       				  			        
	  		     });
    		
    		
               
               	$("#importMgrForm").thickbox({ // 使用選取的內容進行彈窗
               		width : 400,
               		height : 200,
               		modal : true,
               		align : 'center',
               		valign: 'bottom',
               		i18n:i18n.lms8000m01,
               		buttons : {
               			"L260S01E.thickbox1" : function() {                    					               				
               				  						     						
    						 if( nowUnitMgr !== $("#_unitMgr").val() || nowAccountMgr !== $("#_accountMgr").val()){
    							 
    		                        CommonAPI.confirmMessage(i18n.lms8000m01["L260S01D.msg"], function(b){
    		                            if (b) {
    		                            	$.thickbox.close();
    	    					  		    $.ajax({
    	    				  			        handler : inits.fhandle,
    	    				  			        type : "POST",
    	    				  			        dataType : "json",
    	    				  			        data : 
    	    				  			        {
    	    				  				       formAction : "deleteReleationCustomerSummaryFile",
    	    				  				       mainId : $("#oidL260M01D").val()
    	    				  			        },
    	    				  			        success : function(json) {    
    	    				  			        	
    	    			      						 $("#unitMgr_S01D").val($("#_unitMgr").val());
    	    			    						 $("#accountMgr_S01D").val($("#_accountMgr").val());
    	    			    						     						 
    	    			    						 var selectUnitMgrText=$("#_unitMgr").find("option:selected").text();
    	    			    						 var selectAccountMgrText=$("#_accountMgr").find("option:selected").text();  
    	    				  			        	    	    				  			        	
    	    			    						 if($("#unitMgr_S01D").val()!==""){
    	    			     						    $("#unitMgrName_S01D").val(selectUnitMgrText.substring(selectUnitMgrText.indexOf("-")+1, selectUnitMgrText.length));
    	    			     						 }else{
    	    			     							$("#unitMgrName_S01D").val(''); 
    	    			     						 }
    	    			     						 
    	    			     						 if($("#accountMgr_S01D").val()!==""){
    	    			     						    $("#accountMgrName_S01D").val(selectAccountMgrText.substring(selectAccountMgrText.indexOf("-")+1, selectAccountMgrText.length));       
    	    			                			 }else{
    	    			                			    $("#accountMgrName_S01D").val('');
    	    			                			 }
    	    				  			        	    	    				  			        	
    	    				  			        	  subAction._reloadFileGrid("Certified", $("#oidL260M01D").val());	            	    				  			        	
    	    				  			        }       				  			        
    	    				  		       });
    		                            }
    		                        });
    							       							     							 
    						 }else{    							     							 
    							 $.thickbox.close();
    						 }
    						 
    						 nowUnitMgr = $("#unitMgr_S01D").val();
    			    		 nowAccountMgr = $("#accountMgr_S01D").val();    						 
    						 
               			},       
               			"L260S01E.thickbox2" : function() {
               				$.thickbox.close();
               			}
               		}
               	});
               
        });
    	
        
    	// 產製關係戶往來彙總表
    	$('#btnautoProLMS9535V01').click(function(){          				
			
    		   if( $("#unitMgr_S01D").val()==="" || $("#accountMgr_S01D").val()==="" ){
				   CommonAPI.showMessage(  i18n.lms8000m01["checkSelect"]
				                         + i18n.lms8000m01["L260S01D.unitMgr"]
				                         + "、"
				                         + i18n.lms8000m01["L260S01D.accountMgr"]);
				   return;	
				}
    		
               	var nowDate = new Date();
               	var MM = nowDate.getMonth();
               	var YY = nowDate.getFullYear();
               	var SMM;
               	var SYY;
               	if(MM == 0){
               		MM = 12;
               	}
               	
               	if(MM ==12 ){
               		SMM = MM - 5;
               		YY = YY -1 ;
               		SYY = YY; 
               	}else if(MM > 5  && MM < 12  ){
               		SMM = MM - 5;
               		SYY = YY;
               	}else{
               		SMM = MM + 12 - 5;
               		SYY = YY-1; 
               	}
               	
               	var $relationRptForm = $("#relationRptForm");
               	$relationRptForm.find("#qryDtS0").val(SYY);
               	$relationRptForm.find("#qryDtS1").val(SMM);
               	$relationRptForm.find("#qryDtE0").val(YY);
               	$relationRptForm.find("#qryDtE1").val(MM);	
               	               	               	               	               	
               	$("#inputSearchRelationRptForm").thickbox({ // 使用選取的內容進行彈窗
               		title : i18n.lms8000m01["L260S01E.thickbox"],
               		width : 400,
               		height : 200,
               		modal : true,
               		align : 'center',
               		valign: 'bottom',
               		i18n:i18n.lms8000m01,
               		buttons : {
               			"L260S01E.thickbox1" : function() {
               				var $relationRptForm = $("#relationRptForm");
   
               				if($relationRptForm.valid()){
              					
               					if($relationRptForm.find("#qryDtS1").val()< 1
               					|| $relationRptForm.find("#qryDtS1").val()> 12
               					|| $relationRptForm.find("#qryDtE1").val()< 1
               					|| $relationRptForm.find("#qryDtE1").val()> 12){
               						CommonAPI.showMessage(i18n.lms8000m01["L260S01E.error1"]);
               						return;
               					}else if($relationRptForm.find("#qryDtS0").val()<=0
               						   ||$relationRptForm.find("#qryDtE0").val()<=0){
               						CommonAPI.showMessage(i18n.lms8000m01["L260S01E.error2"]);
               						return;
               					}else if($relationRptForm.find("#qryDtE0").val()-
               							$relationRptForm.find("#qryDtS0").val()<0){
               						CommonAPI.showMessage(i18n.lms8000m01["L260S01E.error3"]);
               						return;
               					}else if(($relationRptForm.find("#qryDtE0").val()-
               							$relationRptForm.find("#qryDtS0").val()==0) &&
               							 ($relationRptForm.find("#qryDtE1").val()-
               									$relationRptForm.find("#qryDtS1").val()<0)
               							 ){
               						CommonAPI.showMessage(i18n.lms8000m01["L260S01E.error3"]);
               						return;		
               					}else{               						      
               						
           				  		    $.ajax({
       				  			        handler : "lms9535formhandler",
       				  			        type : "POST",
       				  			        dataType : "json",
       				  			        data : 
       				  			        {
       				  				       formAction : "autoProLMS9535V01",               		
       								       custId : $("#custId").val(),
       								       dupNo : $("#dupNo").val(),
       				  				       queryDateS0 : $relationRptForm.find("#qryDtS0").val(),
       				  				       queryDateS1 : $relationRptForm.find("#qryDtS1").val(),
       				  				       queryDateE0 : $relationRptForm.find("#qryDtE0").val(),
       				  				       queryDateE1 : $relationRptForm.find("#qryDtE1").val(),       				  				       
       				  				       unitMgrName_S01D : $("#unitMgrName_S01D").val(),
       				  				       accountMgrName_S01D : $("#accountMgrName_S01D").val(),       				  				       
       								       mainId : $("#oidL260M01D").val()       								              								              								       
       				  			         },
       				  			         success : function(json) {    
       				  			           $.thickbox.close();
	                                       subAction._reloadFileGrid("Certified", $("#oidL260M01D").val());	      
	                           			   if(json.msg){
	                           			      CommonAPI.showMessage(i18n.def["runSuccess"] + "，" + json.msg);	                
	                           			   }else{
	                           				  CommonAPI.showMessage(i18n.def["runSuccess"]);
	                           			   }
                      					   $("#li-tab1").click();
                      					   
       				  			         }       				  			        
       				  		         });		

               					}
               				}			
               			},       
               			"L260S01E.thickbox2" : function() {
               							$.thickbox.close();
               			}
               		}
               	});
               
        });
    	    	
        $("#btnRtnCopy").click(function(){              	
            CommonAPI.confirmMessage(i18n.lms8000m01["msg.rtnCase"], function(b){            	
                if (b) {                                  	
                    $.ajax({
                        handler: "lms8000m01formhandler",
                        action: "chkL260M01ANotEnd",
                        data: {
                            single: true,
                            custId: $("#custId").val(),
                            dupNo: $("#dupNo").val(),
                            cntrNo: $("#cntrNo").val(),
                            loanNo: $("#loanNo").val()
                        },
                        success: function(obj){
                            if(obj.msg){
                                return CommonAPI.showErrorMessage(i18n.lms8000m01["msg.alreadyHave"]);
                            } else {
                                Action.rtnCopy();
                            }
                        }
                     });
                }
             });
        });    	                     
    	
    	// 修改公司訪問紀錄表格式為最新版本
    	$('#btnmodCompVisitVer').click(function(){
  		    $.ajax({
			        handler : inits.fhandle,
			        type : "POST",
			        dataType : "json",
			        data : 
			        {
				       formAction : "importComVisitData",   
				       mainId: $("#oidL260M01D").val(),
				       modCompVisitVerFlag : true
			         },
			         success : function(obj) {
			        	 $("#tab-1_7")
			        	 .find(':input,.field')
			        	 .not(':button, :submit, :reset, :hidden')
						 .removeAttr('checked')
						 .removeAttr('selected')
						 .not(':checkbox, :radio')
						 .val(''); 		          
			        	$("#table_L260S01E").empty();          
	                    Action.createTabVisitCom(obj);   
	                    $("#l260m01dGrid").trigger("reloadGrid");
			         }
		     });
    	});
    	
    	// J-113-0035 為利ESG案件之貸後管控, ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
    	$("input:checkbox[name=followKind][value='12'],input:checkbox[name=followKind][value='13']").change(function(){	
    		
			let followKind12Element=$("input:checkbox[name=followKind][value='12']");
    		let followKind12ElementChk=followKind12Element.prop("checked");
    		
    		let followKind13Element=$("input:checkbox[name=followKind][value='13']");    		
    		let followKind13ElementChk=followKind13Element.prop("checked");
    		  		
    		if($("#openFormKind").val()==='L260M01D'){
            	if(followKind12ElementChk || followKind13ElementChk){
            		$(".isShowTab8").show();		
                   
                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "showL260s01f",                        
                            mainId: $("#oidL260M01D").val(),
                            custId: $("#custId").val(),
                            dupNo: $("#dupNo").val(),
                            cntrNo: $("#cntrNo").val()
                        },
                        success: function(obj){                             
                        	Action.createTabESG(obj);                           
                        	// 產生完資料後，若文件為唯讀，disabled相關選項
                        	if(checkReadonly()){
                                var tmp_table_L260S01F = $("#formDetail").find("#table_L260S01F");
                                tmp_table_L260S01F.lockDoc();        
                        	}
                        }
                    });
     
            	}else{        		
                    $(".isShowTab8").hide();
                    $("#tab-1_8").hide();
                    $("#li-tab1").trigger("click");//跳回基本資訊頁
            	}
    		}

    	});
    	    	        
        // 將要初始化的function 放入_initEvent function裡
    	$("input:checkbox[name=followKind][value='10']").change(function(){	

    		var element=$("input:checkbox[name=followKind][value='10']");
    		var companyChk=element.prop("checked");
    		
    		// 為ID階層    		
    		if(oneRowByIdFlag==="Y"){
    			    			
        		if(companyChk){
        			// 類別勾選公司訪問紀錄表，其餘類別不能選擇
        			$('input:checkbox[name="followKind"]').prop('disabled', true);
        			$('input:checkbox[name="followKind"]').prop('checked',false);
        			
        			element.prop('checked' ,true);
        			
        			// 資料來源不為系統產生時，才可以勾選
        			if( oneRowDataFrom!=="B" ){
        				element.prop("disabled",false);        			    
        			}        			             			
        				        			
        		}else{
        			// 類別取消勾選公司訪問紀錄表，其餘類別可選擇，但esg相關類別不開放(因不讓其自行新增)
        			$('input:checkbox[name="followKind"]').prop('disabled', false);
        			//J-113-0349 應讓分行可以自行新增"永續績效連結"、"其他ESG項目"
        			//$('input:checkbox[name="followKind"][value="12"]').prop('disabled',true);
        			//$('input:checkbox[name="followKind"][value="13"]').prop('disabled',true);
        		}
        		
    		} else{
    			// 不為ID階層，開放其他類別，但esg相關類別不開放(因不讓其自行新增)
    			$('input:checkbox[name="followKind"]').prop('disabled',false);
    			//J-113-0349 應讓分行可以自行新增"永續績效連結"、"其他ESG項目"
    			//$('input:checkbox[name="followKind"][value="12"]').prop('disabled',true);
    			//$('input:checkbox[name="followKind"][value="13"]').prop('disabled',true);
    			
    			// 不為ID階層，類別不得勾選公司訪問紀錄表
    			element.prop("disabled",true);
    			element.prop("checked" ,false);
    		}
    		
    		
    		
    		
         	if($("#openFormKind").val()==='L260M01D'){
         		
        		$("#tab-1_7")
	        	 .find(':input,.field')
	        	 .not(':button, :submit, :reset, :hidden')
				 .removeAttr('checked')
				 .removeAttr('selected')
				 .not(':checkbox, :radio')
				 .val(''); 		                                        
                $("#table_L260S01E").empty(); 
         		
            	if(companyChk){
            		$(".isShowTab7").show();  		
                   
                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "importComVisitData",                        
                            mainId: $("#oidL260M01D").val(),
                            custId: $("#custId").val(),
                            dupNo: $("#dupNo").val()
                        },
                        success: function(obj){
                             
                        	Action.createTabVisitCom(obj);                           
                        	// 產生完公司訪問記錄表後，若文件為唯讀，disabled相關選項
                        	if(checkReadonly()){
                                var tmp_table_L260S01E = $("#formDetail").find("#table_L260S01E");
                                tmp_table_L260S01E.lockDoc();        
                        	}
                        }
                    });
            		                    		
            	}else{        		
                    $(".isShowTab7").hide();
                    $("#tab-1_7").hide();            
            	}
        	}
    		    
    	});
    	
    	// J-112-0418 配合企金處依據企金處112年一般業務查核意見，E-LOAN企金授信管理系統修改增加「綠色授信」註記之管控機制等。
    	var followKind11Element = $("input:checkbox[name=followKind][value='11']");
    	followKind11Element.change(function(){	
    	
    	   var greenLoanChk= followKind11Element.prop("checked");
    	   if( oneRowDataFrom=="B" && greenLoanChk ){
    		   followKind11Element.prop("disabled",true);    		   
    	   	   $("input:checkbox[name=followKind][value='10']").prop("disabled",true);           		   
    	   }
    	   
    	});

        $("input:checkbox[name='followKind']").filter("[value='11'], [value='12'], [value='13'], [value='14']").change(function(){
    	   // 當類別有變動時，調整顯示說明、序號
    	   let greenLoanChk = $("input:checkbox[name=followKind][value='11']").prop("checked");//綠色授信
    	   let ssksLoanChk = $("input:checkbox[name=followKind][value='12']").prop("checked");//永續績效
    	   let otherESGChk = $("input:checkbox[name=followKind][value='13']").prop("checked");//其他ESG
    	   let socialLoanChk = $("input:checkbox[name=followKind][value='14']").prop("checked");// 社會責任授信
    	   let memoSeq = 0;
    	   let memoStr = ""; 
    	   if(greenLoanChk){    		   
    		   memoStr = memoStr + (++memoSeq) +". "+ i18n.lms8000m01["L260M01D.greenLoanMemo1"]+"<br/>"; 
    		   //memoStr = memoStr + (++memoSeq) +". "+ i18n.lms8000m01["L260M01D.greenLoanMemo2"]+"<br/>";
    	   }
    	   if(ssksLoanChk){
    		   memoStr = memoStr + (++memoSeq) +". "+ i18n.lms8000m01["L260M01D.sslsMemo"]+"<br/>"; 
    	   }
    	   if(otherESGChk){
    		   memoStr = memoStr + (++memoSeq) +". "+ i18n.lms8000m01["L260M01D.otherESGMemo"]+"<br/>"; 
     	   }
           if(socialLoanChk){
               memoStr = memoStr + (++memoSeq) +". "+ i18n.lms8000m01["L260M01D.socialLoanMemo"]+"<br/>";
           }
    	   
    	   $("#memo").html(DOMPurify.sanitize(memoStr));    	   
    	});
    	    	    	    	
		$('#nextFollowDate').change(function(){	
    		// J-113-0192 Web eloan擔保品系統配合授審處貸後管理規範，調整現行貸後管理系統中自動發查不動產謄本規則    			    	
            if (oneRowDataCaseMark=="01" && $("input:checkbox[name=followKind][value='8']").prop("checked")) {
                return CommonAPI.showErrorMessage(i18n.lms8000m01["dateMsg7"] );
			}
    	});
        
    },
    // 產生ESG畫面
    createTabESG: function(obj){

        Action.FormData = obj;
        let $form = $("#formDetail");
        $form.injectData(obj);
          
        let table_L260S01F = $("#formDetail").find("#table_L260S01F");         
       	let array = obj.L260S01FArray; 
		let strHt="";
      	       	      
		strHt += "<thead>";
       	strHt += "<tr valign='top'  "+ " id='_S01F'>";               	   
       	strHt += "<td  align='left' valign='middle' class='hd2' >";   
       	strHt += i18n.lms8000m01["L260S01F.title1"];     	          	          	   
       	strHt += "</td>";
       	strHt += "<td  align='left' valign='middle' class='hd2' >";   
       	strHt += i18n.lms8000m01["L260S01F.title2"];      	          	          	   
       	strHt += "</td>";
       	strHt += "<td  align='left' valign='middle' class='hd2' >";   
       	strHt += i18n.lms8000m01["L260S01F.title3"];     	          	          	   
       	strHt += "</td>";
		strHt += " </tr>	";
		strHt += "</thead>";
   	    
       	if(array.length == 0){//新增
       		$.each(convertItems(result_s.lms140_esgSustainLoanType), function(key, value) {
       			let text="";
       			let itemNo = key;// E、S、G
    		    let itemContent = value;//'E.環境', 'S.社會責任', 'G.公司治理'
    		    let str = "";
				str += "<tbody>";
    		    str += "<tr valign='top'  "+ " id='" + itemNo + "_S01F'>";
         	     
         	    str += "<input type='hidden' id='itemNo_S01F' name='itemNo_S01F'  value='"+itemNo+"'>"
         	    str += "<td  align='left' valign='middle'  >";          	  
         	    str += itemContent.desc;
         	    str += "</td>";       
         	    str += "<td align='center' valign='middle' style='text-align:center' >";                  
         	    str += "<label><select id='chkResult" + itemNo + "_S01F' name='chkResult" + itemNo + "_S01F'  /><span class='style999'>"; 
         	    str += "<span class='style999'></label></td>";
         	    str += "<td align='center' valign='middle' style='text-align:center' >";                  
         	    str += "<label><textarea id='itemMemo" + itemNo + "_S01F' name='itemMemo" + itemNo + "_S01F' cols='85' rows='6' maxlengthC='200' class='trim'></textarea>";                         
         	    str += "</label></td>";
         	    str += "</tr>";
				str += "</tbody>";
         	    strHt+=str; 
       		});
       	}else{//既有資料
       		for (let i = 0; i < array.length; i++) {
     		     let text="";
     		     let itemNo = DOMPurify.sanitize(Action.FormData.L260S01FArray[i].itemNo);
     		     let itemContent = DOMPurify.sanitize(Action.FormData.L260S01FArray[i].itemContent);  		  
     		     let str = "";  	
				 str += "<tbody>";   		    		  
     		  
          	     str += "<tr valign='top'  "+ " id='" + itemNo + "_S01F'>";
          	     
          	     str += "<input type='hidden' id='itemNo_S01F' name='itemNo_S01F'  value='"+itemNo+"'>"
          	     str += "<td  align='left' valign='middle'  >";          	  
          	     str += itemContent;       	          	          	   
          	     str += "</td>";       
          	     
          	     str += "<td align='center' valign='middle' style='text-align:center' >";                  
          	     str += "<label><select id='chkResult" + itemNo + "_S01F' name='chkResult" + itemNo + "_S01F'  /><span class='style999'>"; 
                str += "<span class='style999'></label></td>";
                
                str += "<td align='center' valign='middle' style='text-align:center' >";                  
                str += "<label><textarea id='itemMemo" + itemNo + "_S01F' name='itemMemo" + itemNo + "_S01F' cols='85' rows='6' maxlengthC='200' class='trim'></textarea>";                         
                str += "</label></td>";
                
                str += "</tr>";
				str += "</tbody>";
                strHt+=str;  
          	}
       	}
  
    	if(strHt!==""){    		
     	   let tmpObj = {"strHt":strHt};
           let tmpJsonObj=JSON.parse(JSON.stringify(tmpObj));
		   $("#table_L260S01F").html(tmpJsonObj.strHt);
           $("select[id^='chkResult']select[id$='_S01F']").setItems({
               item: result_s.l260s01fESGoption,
               format: "{key}"
           });
     	}
            	    	                 
        for (let i = 0; i < array.length; i++) {        	
            let json = array[i];
            let itemNo = DOMPurify.sanitize(json.itemNo);
            let itemMemo = DOMPurify.sanitize(json.itemMemo);
            let uuu = DOMPurify.sanitize(json.chkResult); 
            $("select[id='chkResult" + itemNo + "_S01F']").val(uuu);
                        
            let s01FMemo = $("#itemMemo" + itemNo + "_S01F");
            s01FMemo.val(itemMemo);            
        }
     
		if (obj.msg && obj.msg != "") {
            CommonAPI.showMessage(obj.msg);
        }
    },
    // 產生公司訪問紀錄表畫面
    createTabVisitCom: function(obj){
    	
        Action.FormData = obj;
        var $form = $("#formDetail");
        $form.injectData(obj);
             
        var typeJson = obj.typeJson;
        var table_L260S01E = $("#formDetail").find("#table_L260S01E");
        var array = obj.L260S01EArray;
    	var rptId_S01D = obj.rptId_S01D;	//代入報表版本
    	var country = obj.Country;	//國別	CA英文與他國不同
    	var locale = userInfo.userLocale;	//語系
        
    	var randomCode_S01D = Action.FormData.randomCode_S01D;	
    	$("#randomCode_S01D").val(randomCode_S01D);
   	
    	var titleAry=[];
    	var chBoxAry=[];                    
    	var chBoxAry2=[];                    	
    	var chBoxAry3=[];   
    	// checkBox 單選
//    	var singleBoxAry  = ['A016','A017','A018'];
//    	var singleBoxAry2 = ['A019','A020','A021'];
    	    	    	
    	if(rptId_S01D<="Ver202308" || rptId_S01D===""){
    		titleAry.push('A001');
    		titleAry.push('A015');
    		
    		chBoxAry.push('A016');
    		chBoxAry.push('A017');
    		chBoxAry.push('A018');
    		chBoxAry.push('A021');
    		
    		chBoxAry2.push('A019');
    		chBoxAry2.push('A020');
    		
    		chBoxAry3.push('A022');    		
    	}
    	
            
       	var strHt="";
       	
       	// title
		strHt += "<thead>";
		strHt += "<tr>";
		strHt += "   <td   style='text-align:center ;background: #E2ECF7' width='15%'>";
		strHt += "	     <input type='hidden' name='prtParam'   id='prtParam' value=''/>";										
		// 結果
		strHt += i18n.lms8000m01["L260S01E.title1"];
		strHt += "     <br/>";
		// 無該項目以一表示
		strHt += "     <span class='text-red'><strong>(		";	
		strHt += i18n.lms8000m01["L260S01E.title2"];		                                                    
		strHt += "     )";
		strHt += "     </strong></span>";
		strHt += "   </td>";
		strHt += "	 <td  style='text-align:center;background: #E2ECF7' width='5%'>";
		// 項次
		strHt += i18n.lms8000m01["L260S01E.title3"];	                                                    
		strHt += "	 </td>	";
		strHt += "	 <td   style='text-align:center;background: #E2ECF7' colspan='2' width='50%'>";
		// 項目
		strHt += i18n.lms8000m01["L260S01E.title4"];	                                               
		strHt += "	 </td>					";									 
		strHt += "   <td   style='text-align:center;background: #E2ECF7' colspan='2' width='30%'>";
		// 變動之內容
		strHt += i18n.lms8000m01["L260S01E.title5"];			
		strHt += "    </td>	";												 
		strHt += " </tr>	";
		strHt += "</thead>";
       	
       	
        for (var type in typeJson) {
        	for (var i = 0; i < array.length; i++) {
        		  var text="";
        		  var itemNo = Action.FormData.L260S01EArray[i].itemNo;
        		  var itemContent = Action.FormData.L260S01EArray[i].itemContent;
        		  var itemSeqShow = Action.FormData.L260S01EArray[i].itemSeqShow;
        		  var str = "";   
				  
				  str += "<tbody>";
                  // 處理標題
                  if(jQuery.inArray(itemNo,titleAry) >= 0){
                	  
                	  str += "<tr valign='top'  "+ " id='" + itemNo + "_S01E'>";    
                	  str += "<input type='hidden' id='itemNo_S01E' name='itemNo_S01E'  value='"+itemNo+"'>"
                	  str += "<td>";   
                	  str += "</td>";   
                	  
                	  str += "<td  align='center' valign='middle' style='text-align:center'>";   
                	  str += itemSeqShow;    
                	  str += "</td>";   
                	  
                	  str += "<td colspan='4' >";   
                	  str += itemContent;    
                	  str += "</td>";   

                  }else if (jQuery.inArray(itemNo,chBoxAry) >= 0){
                	  str = "<tr valign='top' " + " id='" + itemNo + "_S01E'>";
                	  str += "<input type='hidden' id='itemNo_S01E'  name='itemNo_S01E'  value='"+itemNo+"'>"
                	  str += "<td>";        
                	  str += "</td>";     
                	  
                	  str += "<td align='center' valign='middle' style='text-align:left' >";
                	  str +="</td>";
                	  
                	  str += "<td align='center' valign='middle' style='text-align:left' colspan='2'>";
                	  
                      str += "<label>";
                      
                      str += Action.addSpaceForLevel(rptId_S01D,itemNo);

                      
//                      var chkboxName=itemNo;
//                      if (jQuery.inArray(itemNo,singleBoxAry) >= 0){
//                    	  chkboxName=singleBoxAry[0];
//                      }else if  (jQuery.inArray(itemNo,singleBoxAry2) >= 0){
//                    	  chkboxName=singleBoxAry2[0];
//                      }
                      str += "<input id='chkResult" + itemNo + "_S01E' name='chkResult" + itemNo + "_S01E' type='checkBox' value='Y' /><span class='style999'>";
                      str += itemContent;
                      str += "</label>";  
                	  str += "</td>";  
                	  
                	  str += "<td colspan='2'>";        
                	  str += "</td>";
                	  
                  }else if (jQuery.inArray(itemNo,chBoxAry2) >= 0){
                	  str = "<tr valign='top' " + " id='" + itemNo + "_S01E'>";
                	  str += "<input type='hidden' id='itemNo_S01E'  name='itemNo_S01E'  value='"+itemNo+"'>"
                	  str += "<td>";        
                	  str += "</td>";     
                	                                  	  
                	  str += "<td align='center' valign='middle' style='text-align:left' >";        
                	  str += "</td>";     
                	  
                	  str += "<td align='center' valign='middle' style='text-align:left' colspan='2'>";                                	  
                      
                	  str += Action.addSpaceForLevel(rptId_S01D,itemNo);  
                      
                      var chkboxName=itemNo;
//                      if (jQuery.inArray(itemNo,singleBoxAry2) >= 0){
//                    	  chkboxName=singleBoxAry2[0];
//                      }
                                                            
                      var  tmpAry = itemContent.split("@");
                      for (var j = 0; j < tmpAry.length; j++) {
                    	  str += "<label>";
                    	  if(j==0){ 
                             str += "<input id='chkResult" + itemNo +"_S01E' name='chkResult" + itemNo + "_S01E' type='checkBox' value='Y' /><span class='style999'>";
                          }else{
                        	 str += "<input id='chkResult" + itemNo +"_"+j+ "_S01E' name='chkResult" + itemNo + "_1_S01E' type='checkBox' value='"+j+"' /><span class='style999'>";
                          }
                    	  str += tmpAry[j];  
                    	  str += "</label>";  
                      }                                      
                      
                	  str += "</td>";  
                	  
                	  str += "<td >";        
                	  str += "</td>";
                	  
                  }                                                                    
                  else if (jQuery.inArray(itemNo,chBoxAry3) >= 0){
                	  str = "<tr valign='top' " + " id='" + itemNo + "_S01E'>";
                	  str += "<input type='hidden' id='itemNo_S01E' name='itemNo_S01E' value='"+itemNo+"'>"
                	  str += "<td>";        
                	  str += "</td>";
                	  
                	  str += "<td align='center' valign='middle' style='text-align:center' >";
                	  str +="</td>";
                	  
                	  str += "<td align='center' valign='middle' style='text-align:left' colspan='2'>";                                	  
                      str += "<label>";                                      
                   
                      str += Action.addSpaceForLevel(rptId_S01D,itemNo);
                      
                      str += "<input id='chkResult" + itemNo + "_S01E' name='chkResult" + itemNo + "_S01E' type='checkBox' value='Y' /><span class='style999'>";
                      str += itemContent;
                      str += "</label>";  
                	  str += "</td>";  
                                                            
                      str += "<td align='center' valign='middle'  style='text-align:center' colspan='2'>";
                      str += "<span class='style44'><input type='text' id='chkText" + itemNo + "_S01E' name='chkText" + itemNo + "_S01E' size='40' maxlengthC='64' /></span>";
                      str += "</td>";
                      
                  }
                  else{

                      str = "<tr valign='top' " + " id='" + itemNo + "_S01E'>";
                      str += "<input type='hidden' id='itemNo_S01E' name='itemNo_S01E' value='"+itemNo+"'>"
                      //  結果
                      str += "<td align='center' valign='middle' style='text-align:center' >";
                  
                      str += "<label><input id='chkResult" + itemNo + "_S01E' name='chkResult" + itemNo + "_S01E' type='radio' value='Y'  /><span class='style999'>";
                      str += i18n.lms8000m01["L260S01E.Y"];

                      str += "</label><label><input id='chkResult" + itemNo + "_S01E' name='chkResult" + itemNo + "_S01E' type='radio' value='N' /><span class='style999'>";
                      str += i18n.lms8000m01["L260S01E.N"];
                  
                      str += "</label><label><input id='chkResult" + itemNo + "_S01E' name='chkResult" + itemNo + "_S01E' type='radio' value='K'  /><span class='style999'>";
                      str += i18n.lms8000m01["L260S01E.K"];
                  
                      str += "</label></td>";
                  
                      //  項次
                      str += "<td align='center' valign='middle' style='text-align:center' >";
                      str += "</td>";
                      
                     
                     // 項目                                                                    
                     str += "<td align='center' valign='middle'  style='text-align:left' colspan='2' >";  
                     str += Action.addSpaceForLevel(rptId_S01D,itemNo);
                     str +=  itemContent;
                     str += "</td>";
                  
                     // 說明                                                                                                     
                     str += "<td align='center' valign='middle'  style='text-align:center' colspan='2' >";
                     str += "<span class='style44'><input type='text' id='chkText" + itemNo + "_S01E' name='chkText" + itemNo + "_S01E' size='40' maxlengthC='64' /></span>";
                     str += "</td>";
                  }
                  
                  
                  str+="</tr>"; 
				  str += "</tbody>";                 
                  strHt+=str;                                    		  
        	}
        	
        }
    	if(strHt!==""){    		
     	   var tmpObj = {"strHt":strHt};
           var tmpJsonObj=JSON.parse(JSON.stringify(tmpObj));
		   
		   $("#table_L260S01E").html(tmpJsonObj.strHt);
     	}
        
                  
        for (var i = 0; i < array.length; i++) {
            var json = array[i];
            var itemNo = json.itemNo;
            var uuu = json.chkResult;
            var chkText = json.chkText;

 
            $("input[type='radio'][name='chkResult" + itemNo + "_S01E'][value='" + uuu + "']").prop("checked", true);
            var s01eText = $("input[type='text'][name='chkText"   + encodeURI(itemNo) + "_S01E']");
            s01eText.val(chkText);
            $("input[type='checkbox'][name='chkResult" + itemNo + "_S01E'][value='" + uuu + "']").prop("checked", true);
               
            if(jQuery.inArray(itemNo,chBoxAry2) >= 0){
                    var  tempAry = [];
                    if (typeof uuu === "string"){
                    	tempAry = uuu.split("@");
                    }
                    for (var j = 0; j < tempAry.length; j++) { 
                      
                      $("input[type='checkbox'][name='chkResult" + itemNo + "_S01E'][value='" + tempAry[j] + "']").prop("checked", true);                     
                      $("input[type='checkbox'][name='chkResult" + itemNo + "_1_S01E'][value='" + tempAry[j] + "']").prop("checked", true); 
                       
                    }  
            }
                
                
//                單一checkbox，目前不需要                            	
//                if (jQuery.inArray(itemNo,chBoxAry2) >= 0){
//                	 var tmpAry=uuu.split('@');
//                	 $.each( obj, function( key, value ) {
//               		 $("input[type='checkbox'][name='chkResult" + key + "'][value=" + value + "]").prop("checked", true);
//                    	 $("input[type='checkbox'][name='chkResult" + key + "_1'][value=" + value + "]").prop("checked", true);
//                	 });
//                	 
//                }
            
            

        }
                
       
//        var chkResultAry = $("input[type='checkbox'][name^='chkResult']");                                                
//        for (var i = 0 ; i < chkResultAry.length; i++) {
        	  	
        	// 註冊change事件
//        	chkResultAry[i].addEventListener('change' ,function(){
            	          
        		// 處理checkBox單選，目前不需要
//        		var nowChkBoxId          = $(this).prop('id');
//        		var nowChkBoxName        = $(this).prop('name');
//        		var nowChkBoxStatus      = $(this).prop('checked');
//        		var chkBoxName           = "";                         	
//        		if (jQuery.inArray(nowChkBoxName.replace("chkResult",""),singleBoxAry) >= 0){
//        			chkBoxName=nowChkBoxName;
//        		}else if(jQuery.inArray(nowChkBoxName.replace("chkResult",""),singleBoxAry2) >= 0){
//        			chkBoxName=nowChkBoxName;
//        		}
//   
//                if(chkBoxName!==""){
//                   $("input[type='checkbox'][name='"+chkBoxName+"']").removeAttr("checked");         
//                   $(this).prop("checked",nowChkBoxStatus);
//                   
//               
//                   $.each( singleBoxAry2, function( key, value ) {
//                	   if(nowChkBoxId.replace("chkResult","")===value){
//                		   $("input[type='checkbox'][name='chkResult"+value+"_1']").prop("disabled",false);
//                		   return true;
//                	   }else{
//                		   $("input[type='checkbox'][name='chkResult"+value+"_1']").removeAttr("checked");
//                		   $("input[type='checkbox'][name='chkResult"+value+"_1']").prop("disabled",true);
//                	   }
//                   });
//                   
//        	    }
        		

//            });                         			
//        }                                  
        
        
        
		if (obj.msg && obj.msg != "") {
            CommonAPI.showMessage(obj.msg);
        }
    },    
    addSpaceForLevel: function(rptId_S01D,itemNo){
    	
    	var str="";   
    	
    	// 因為項目有階層，第一階前面就加一個空白
    	var tab1Ary  = [];
    	
    	if(rptId_S01D<="Ver202308" || rptId_S01D===""){
    		tab1Ary.push('A002');
    		tab1Ary.push('A003');
    		tab1Ary.push('A004');
    		tab1Ary.push('A005');
    		tab1Ary.push('A006');
    		tab1Ary.push('A007');
    		tab1Ary.push('A008');
    		tab1Ary.push('A009');
    		tab1Ary.push('A010');
    		tab1Ary.push('A011');
    		tab1Ary.push('A012');
    		tab1Ary.push('A013');   		
    		tab1Ary.push('A014');
    	}
    	
        if (jQuery.inArray(itemNo,tab1Ary) >= 0){
              	 str = "&nbsp;";
        }   
        
    	return str;

    },    
    printVisitComp: function(){
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {            	
            	oidL260M01D        : $("#oidL260M01D").val(),
            	randomCode_S01D    : $("#randomCode_S01D").val(),
                type: $('#prtParam').val(),                
                fileDownloadName: "lms8000R06.pdf",
                serviceName: "lms8000r01rptservice"
            }
        });
        $('#prtParam').val('');
    },   
    rtnCopy: function(){    	        
    	 // 退回修改
        $("#rtnCopyBox").thickbox({            
            title: i18n.lms8000m01["L260M01A.rtnModifyReason"],
            width: 450,
            height: 260,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#rtnCopyForm").valid()) {
                        return;
                    }
                    Action.sendRtn();
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
        
    	$("#rtnModifyReasonStr").removeAttr('readonly');
        $("#rtnModifyReasonStr").val('');
    },
    sendRtn: function(){
        CommonAPI.confirmMessage(i18n.def["confirmReturn"], function(b){
            if (b) {               
                $.ajax({
                    handler: "lms8000m01formhandler",
                    data: {
                        formAction: "rtnModifyL260m01a",
                        oids: responseJSON.oid,
                        mainIds: $("#mainId").val(),
                        rtnModifyReasonStr: $('#rtnModifyReasonStr').val()
                    },
                    success: function(obj){
                    	CommonAPI.triggerOpener("gridview", "reloadGrid");
                        window.close();
                    }
                });
                                
                $.thickbox.close();                
            }
        });    	
    },
    _initGrid: function(){
        this.l260m01cGrid = $("#l260m01cGrid").iGrid({
            height: 150,
            handler: inits.ghandle,
            sortname: "statusForShow|nextFollowDate|unid|oid",
            sortorder: 'asc|desc|asc|asc',
            action: "queryList",
            postData: {
                mainId: $("#mainId").val(),
                fieldId: "l260m01c",
                mainDocStatus: responseJSON.mainDocStatus
            },
            loadComplete: function(){
                $('#l260m01cGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });

            },
            colModel: [{
                colHeader: i18n.lms8000m01["addById"],  //ID階層
                name: 'byId',
                width: 30,
                sortable: false,
                align: "center"
            }, {
                colHeader: i18n.lms8000m01["L260M01C.followContent"], //追蹤事項通知內容
                align: "left",
                width: 500,
                name: 'followContent',
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                    Action.openDetailBox("L260M01C", null, rowObject);
                }
            }, {
                colHeader: "追蹤人員",
                name: 'staff',
                width: 38,
                align: "center",
                hidden: userInfo.isOverSea
            }, {
                colHeader: i18n.lms8000m01["cycle"] + "(月)",//循環追蹤週期
                name: 'followCycle',
                width: 38,
                align: "center"
            }, {
                colHeader: i18n.lms8000m01["L260M01C.nextFollowDate"],//下次追蹤日
                name: 'nextFollowDate',
                width: 50,
                sortable: false,
                align: "center",
                formatter: function(cellvalue, options, rowObject){
                    // rowObject ==> ,分隔 資料
                    // ex.  followContent,followCycle,nextFollowDate,statusForShow,checkYN,oid,unid
                    var sfs = rowObject + ''; //statusForShow
                    sfs = sfs.split(",")[3];
                    //                    if(sfs == "3"){
                    if (rowObject[4] == "已解除") { // 因為 追蹤內容可能輸入  ,  使用 split 會切錯
                        // 3-已解除 (下次追蹤日顯示 N.A.)
                        return "N.A.";
                    }
                    else {
                        if (cellvalue == null) {
                            return "";
                        }
                        else {
                            return cellvalue;
                        }
                    }
                }
            }, {
                colHeader: i18n.lms8000m01["L260M01C.status"],//狀態
                name: 'statusForShow',
                width: 30,
                align: "center"
            }, {
                colHeader: " ",
                name: 'checkYN',
                align: 'center',
                width: 8
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }, {
                colHeader: "unid",
                name: 'unid',
                hidden: true
            }, {
                colHeader: "byIdFlag",
                name: 'byIdFlag',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#l260m01cGrid").getRowData(rowid);
                Action.openDetailBox("L260M01C", null, data);
            }
        });
        
        this.l260m01dGrid = $("#l260m01dGrid").iGrid({
            height: 150,
            handler: inits.ghandle,
            sortname: "followDate|unid|oid",
            sortorder: 'asc|asc|asc',
            action: "queryList",
            //            multiselect: true,
            postData: {
                mainId: $("#mainId").val(),
                fieldId: "l260m01d",
                mainDocStatus: responseJSON.mainDocStatus
            },
            loadComplete: function(){
                $('#l260m01dGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
                colHeader: i18n.lms8000m01["addById"],  //ID階層
                name: 'byId',
                width: 30,
                sortable: false,
                align: "center"
            }, {
                colHeader: i18n.lms8000m01["L260M01C.followContent"], //追蹤事項通知內容
                align: "left",
                width: 500,
                name: 'followContent',
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                    Action.openDetailBox("L260M01D", null, rowObject);
                }
            }, {
                colHeader: "追蹤人員",
                name: 'followStaff',
                width: 38,
                align: "center",
                hidden: userInfo.isOverSea
            }, {
                colHeader: "應追蹤日",
                name: 'followDate',
                width: 50,
                align: "center"
            }, {
                colHeader: i18n.lms8000m01["L260M01D.handlingStatus"],//辦理狀況
                name: 'handlingStatus',
                width: 35,
                align: "center"
            }, {
                colHeader: " ",
                name: 'checkYN',
                align: 'center',
                width: 8
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }, {
                colHeader: "unid",
                name: 'unid',
                hidden: true
            }, { // P:人工新增
                colHeader: "dataFrom",
                name: 'dataFrom',
                hidden: true
            }, {
                colHeader: "byIdFlag",
                name: 'byIdFlag',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#l260m01dGrid").getRowData(rowid);
                Action.openDetailBox("L260M01D", null, data);
            }
        });

        this.undoneElf602Grid = $("#undoneElf602Grid").iGrid({
            height: 150,
            handler: inits.ghandle,
            sortname: "followDate|unid",
            sortorder: 'asc|asc',
            action: "queryUndoneElf602List",
            multiselect: true,
            postData: {
            },
            loadComplete : function() {
                $('#undoneElf602Grid a').click(function(e) {
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
               colHeader: i18n.lms8000m01["L260M01C.followContent"], //追蹤事項通知內容
               align: "left", width: 500, name: 'followContent'
            }, {
               colHeader: "追蹤人員",
               name: 'followStaff',
               width: 38,
               align: "center",
               hidden: userInfo.isOverSea
            }, {
               colHeader: "應追蹤日",
               name: 'followDate',
               width: 50,
               align: "center"
            }, {
               colHeader: i18n.lms8000m01["L260M01D.handlingStatus"],//辦理狀況
               name: 'handlingStatus',
               width: 35,
               align: "center"
            }, {
               colHeader: "unid",
               name: 'unid',
               hidden: true
               /* 解決 IE 吃空白，但前後就都會多 '<pre>'  '</pre>'
               formatter: function(cellvalue, options, rowObject){
                   return '<pre>' + cellvalue + '</pre>';
               }
               */
            }]
        });
    },
    openDetailBox: function(cellvalue, options, data){
   
        var $form = $("#formDetail");
        $form.reset();
        $form.find("input").each(function(){                    	        
        	var $item = $(this);
            var itemType = $item.prop("type");
            if (itemType == "hidden") {
                // 代表是 injectData 自動生成的隱藏欄位 也就是畫面上沒有的欄位
                // .reset() 不會清到這塊  要特別清除  避免欄位值沒更新
                $item.val('');
            }
        });
    	$("#openFormKind").val(cellvalue);

        // J-112-0307 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。  
        oneRowByIdFlag="";
        oneRowDataFrom=""; 
        
        
               
        var buttons = {
            "saveData": function(){
                if($("#cantEdit").val() == "Y") {
                    return CommonAPI.showErrorMessage($("#showCantEdit").val());
                }
                if ($form.valid()) {
                    var dataOid;                    
                    if (cellvalue == "L260M01C") {
                        CommonAPI.confirmMessage(i18n.lms8000m01["changeStatus"] + "　" + i18n.lms8000m01["confirmRun"], function(b){
                            if (b) {
                                dataOid = $("#oidL260M01C").val();
                                
                                if (Action.checkFollowKind()) {
                                    return CommonAPI.showErrorMessage(i18n.lms8000m01['checkSelect'] +
                                    i18n.lms8000m01['L260M01C.followKind']);
                                }
								var byIdFlag = data.byIdFlag;
                                if(!userInfo.isOverSea && byIdFlag != "Y"){    // ID階層不需輸入追蹤人員
                                    if ($("input[name='staff']:checked").val() == "") {
                                        return CommonAPI.showErrorMessage(i18n.lms8000m01["checkSelect"] + "應辦理追蹤人員");
                                    }
                                    if ($("#fo_staffNo :selected").val() == "" && $("input[name='staff']:checked").val() == "01") {
                                        return CommonAPI.showErrorMessage(i18n.lms8000m01["checkSelect"] + "授信人員");
                                    }
                                    if ($("#ao_staffNo :selected").val() == "" && $("input[name='staff']:checked").val() == "02") {
                                        return CommonAPI.showErrorMessage(i18n.lms8000m01["checkSelect"] + "AO人員");
                                    }
                                }
                                
                                var keyInDate = $("#nextFollowDate").val();
                                var followWay = $("input[name='followWay']:checked").val();
                                if (followWay == "2") { // 1-特定日期 2-循環週期
                                    subAction.calNextDt(false, function(){
                                        if (keyInDate == "") {
                                            $("#nextFollowDate").val(subAction.calDt);
                                            keyInDate = subAction.calDt;
                                        }
                                        if (keyInDate != subAction.calDt) {
                                            var str = "下次追蹤日計算為 " + subAction.calDt + " 與輸入之下次追蹤日 " + keyInDate + " 不符。<br/>" +
                                            "點選[確定]為 " +
                                            subAction.calDt +
                                            "，點選[取消]為 " +
                                            keyInDate;
                                            CommonAPI.confirmMessage(str, function(b){
                                                if (b) {
                                                    $("#nextFollowDate").val(subAction.calDt);
                                                    Action.saveL260M01C(dataOid);
                                                }
                                                else {
                                                    $("#nextFollowDate").val(keyInDate);
                                                    Action.saveL260M01C(dataOid);
                                                }
                                            });
                                        }
                                        else {
                                            Action.saveL260M01C(dataOid);
                                        }
                                    });
                                }
                                else {
                                    if (keyInDate == "") {
                                        return CommonAPI.showErrorMessage(i18n.lms8000m01["checkSelect"] + i18n.lms8000m01["L260M01C.nextFollowDate"]);
                                    }
                                    Action.saveL260M01C(dataOid);
                                }
                            }
                        });
                    }
                    else 
                        if (cellvalue == "L260M01D") {
                            dataOid = $("#oidL260M01D").val();
                            
                            if (Action.checkFollowKind()) {
                                return CommonAPI.showErrorMessage(i18n.lms8000m01['checkSelect'] +
                                i18n.lms8000m01['L260M01C.followKind']);
                            }
                            
                            $.ajax({
                                handler: inits.fhandle,
                                data: $.extend($("#formDetail").serializeData(), {
                                    formAction: "saveL260M01D",
                                    oid: dataOid,
                                    followKind: Action.getSelectItem(),
                                    prtParam: $('#prtParam').val()
                                }),
                                success: function(obj){
                                	
                                	// J-112-0307 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
                                	// 更新公司訪問紀錄表的文件亂碼
                                	var randomCode_S01D = obj.randomCode_S01D;
                                	if(    typeof randomCode_S01D  !== "undefined"  
                                		&& randomCode_S01D !== ""){
                                	    $("#randomCode_S01D").val(randomCode_S01D);
                                	}
                                                                   	
                                    if (obj.msg && obj.msg != "") {
                                        CommonAPI.showErrorMessage(obj.msg);
                                        $('#prtParam').val('');
                                    }else{
                                        // J-112-0307 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
                                        // 儲存成功才印表
                                    	if($('#prtParam').val()==="R06"){                                    	
                                           Action.printVisitComp();
                                        }
                                    }
									                                    
                                    $("#l260m01dGrid").trigger("reloadGrid");

                                }
                            });                                                        
                            	
                            
                        }
                        else {
                        
                        }
                }
            },
            "close": function(){            	
            	if (cellvalue == "L260M01D") {
            		// 關閉時清除附件欄位，避免開啟下一筆資料時，因載入太慢，看到前次瀏覽的資料
            		$("#certifiedFileGrid").jqGrid("clearGridData");            		
            	}
                $.thickbox.close();
            }
        }
        
        $.ajax({
            handler: inits.fhandle,
            action: "queryDetail",
            data: {
                tableOid: data.oid,
                table: cellvalue
            },
            success: function(obj){
                Action.FormData = obj;
                $form.injectData(obj);
                subAction._init(data.oid);
                if(obj.showCantEdit && responseJSON.mainDocStatus != "0CO"
                    && responseJSON.mainDocStatus != "05O" 
                    && responseJSON.mainDocStatus != "BKL"     	
                ){   
                	// 查詢、已覆核、退回不用顯示
                    $("#showCantEdit").show();
                    $("#cantEdit").val("Y");
                } else {
                    $("#showCantEdit").hide();
                    $("#showCantEdit").val("");
                    $("#cantEdit").val("");
                }
                var value = obj.followKind.split("|");
                var cols = ["W1", "W2", "W3", "D1", "D2", "D3"];
                for (var i = 0; i < value.length; i++) {
                    var followKind = value[i];
                    $("[name=followKind][value=" + followKind + "]").prop("checked", true);
                    // J-109-0339 弱化惡化 檢核互斥
                    if ($.inArray(followKind, cols) != -1) {
                        var type = followKind.substring(0, 1);
                        var seq = followKind.substring(1, 2);
                        if (type == "W") {
                            $("[name=followKind][value='D" + seq + "']").removeAttr("checked").prop("disabled", "disabled");
                        }
                        else 
                            if (type == "D") {
                                $("[name=followKind][value='W" + seq + "']").removeAttr("checked").prop("disabled", "disabled");
                            }
                    }
                }
                //                if(obj.followWay){
                //                    $("[name=followWay][value=" + obj.followWay + "]:radio").prop("checked", "checked");
                //                }
                //                if(obj.staff){
                //                    $("[name=staff][value=" + obj.staff + "]:radio").prop("checked", "checked");
                //                }
                
                
                if (cellvalue == "L260M01C") {
                    $(".divL260M01Csub").show();
                    $('#nextFollowDateMemo').show();
                    // divL260M01Csub 特別針對海外再做隱藏
                    if(userInfo.isOverSea){
                        $("tr[class*=ovsHide]").hide();
                    }
                    $("#divL260M01C").find("tr:not(.divL260M01Csub)").find("input,textarea").prop("disabled", false);
                    $("#divL260M01D").hide();
                    $(".isShowTab2").hide();
                    $(".isShowTab3").hide();
                    $(".isShowTab4").hide();
                    $(".isShowTab5").hide();
                    $(".isShowTab6").hide();
                    // J-112-0307 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
                    $(".isShowTab7").hide();
                    $("#tab-1_7").hide();
                    // J-113-0035 為利ESG案件之貸後管控, ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
                    $(".isShowTab8").hide();
                    $("#tab-1_8").hide();
                    
                    var followWay = obj.followWay; // 1-特定日期 2-循環週期
                    if (followWay == "2") {
                        $("#divL260M01C").find("tr[class*=followWayCycle]").show();
                        $("#calNextDt").show();
                    }
                    else {
                        $("#divL260M01C").find("tr[class*=followWayCycle]").hide().find(":input").val("");
                        $("#calNextDt").hide();
                    }
                    
                    // J-112-0307 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
                    oneRowByIdFlag=data.byIdFlag;
                    oneRowDataFrom="";
                    $("input[name=followKind]:checkbox").trigger('change');
					                    
                    // J-113-0192 Web eloan擔保品系統配合授審處貸後管理規範，調整現行貸後管理系統中自動發查不動產謄本規則
                    oneRowDataCaseMark =  obj.caseMark;                    
                     
                }
                else 
                    if (cellvalue == "L260M01D") {
                        $("#tabsL260M01D").tabs({
                            selected: 0
                        });
                        subAction._reloadGrid("finProdGrid", $("#oidL260M01D").val());
                        subAction._reloadGrid("raspGrid", $("#oidL260M01D").val());
                        
                        $(".divL260M01Csub").hide();
                        $('#nextFollowDateMemo').hide();
                        $("#divL260M01D").show();
                        
                        $("#divL260M01C").find("tr:not(.divL260M01Csub)").find("input,textarea");//.prop("disabled", true);
                        var repayUnusualFg = obj.repayUnusualFg; // 還款來源異常註記
                        if (repayUnusualFg == "Y") {
                            $(".isShowTab2").show();
                        }
                        else {
                            $(".isShowTab2").hide();
                        }
                        var finProdFg = obj.finProdFg; // 理財商品
                        if (finProdFg == "Y") {
                            $(".isShowTab3").show();
                        }
                        else {
                            $(".isShowTab3").hide();
                        }
                        var caseMark = obj.caseMark; // 案件註記
                        if (caseMark == "03") { // J-110-0497 餘屋貸款
                            $(".isShowTab4").show();
                            if (obj.checkBeg && obj.checkBeg == "N") {
                                $("#begForSell").prop("disabled", true);
                            } else {
                                $("#begForSell").prop("disabled", false);
                            }
                            $("#proStatus").trigger("change");
                        }
                        else {
                            $(".isShowTab4").hide();
                            $("#begForSell").prop("disabled", false);
                        }
                        
                        // J-112-0307 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。           
                        oneRowByIdFlag=data.byIdFlag;
                        oneRowDataFrom=data.dataFrom;
                        $("input[name=followKind]:checkbox").trigger('change');
                    }
                    else {
                        $(".divL260M01Csub").hide();
                        $('#nextFollowDateMemo').hide();
                        $("#divL260M01C").find("tr:not(.divL260M01Csub)").find("input,textarea").prop("disabled", true);
                        $("#divL260M01D").hide();
                        $(".isShowTab2").hide();
                        $(".isShowTab3").hide();
                        $(".isShowTab4").hide();
                        $(".isShowTab5").hide();
                        $(".isShowTab6").hide();
                        // J-112-0307 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
                        $(".isShowTab7").hide();
                        $("#tab-1_7").hide();
                        // J-113-0035 為利ESG案件之貸後管控, ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
                        $(".isShowTab8").hide();
                        $("#tab-1_8").hide();
                    }

                Action.setOverSeaHideUI(userInfo.isOverSea);

                if(!userInfo.isOverSea && obj.hasLoanNo && obj.hasLoanNo == "Y"){
                    $("button[class*=loanNoShow]").show();
                } else {
                    $("button[class*=loanNoShow]").hide();
                }

                if(!userInfo.isOverSea && obj.hasEland && obj.hasEland == "Y"){
                    $("button[class*=eLandShow]").show();
                } else {
                    $("button[class*=eLandShow]").hide();
                }

                if(!userInfo.isOverSea && obj.hasRasp && obj.hasRasp == "Y"){
                    $("button[class*=raspShow]").show();
                    $(".isShowTab5").show();
                } else {
                    $("button[class*=raspShow]").hide();
                    $(".isShowTab5").hide();
                }

                if(!userInfo.isOverSea && obj.hasConstr && obj.hasConstr == "Y"){
                    $(".isShowTab6").show();
                    if (obj.begConstr == 'Y') {
                        $('#constrHide').show();
                    } else {
                        $('#actStDate').val("");
                        $('#constrHide').hide();
                    }
                } else {
                    $(".isShowTab6").hide();
                    $('#actStDate').val("");
                    $('#constrHide').hide();
                }
           
                
                $("#detailThickbox").thickbox({
                    title: "",
                    width: 1000,
                    height: (cellvalue == "L260M01C" ? 600 : 800),
                    modal: true,
                    readOnly: _openerLockDoc == "1" || inits.toreadOnly,
                    i18n: i18n.def,
                    buttons: buttons,
                    open: function(){
                        if (cellvalue == "L260M01D") {
                            // 文件狀態為 編制中 && (CMS.C101M29 有資料 而且 都是A3) && 沒有實登資料列L260S01C
                            if(obj.raspStatus && obj.raspStatus == "3" && (!checkReadonly())){
                                // 擔保品已查詢過實價登錄，無須再查。
                                CommonAPI.showMessage(i18n.lms8000m01['L260M01D.WORDING06']);
                            }
                        }
                    }
                });
            }
        });
    },
    _reloadGrid: function(){
        this.l260m01cGrid.jqGrid("setGridParam", {
            postData: {
                mainId: $("#mainId").val(),
                fieldId: "l260m01c"
            },
            page: 1,
            search: true
        }).trigger("reloadGrid");
        
        this.l260m01dGrid.jqGrid("setGridParam", {
            postData: {
                mainId: $("#mainId").val(),
                fieldId: "l260m01d"
            },
            page: 1,
            search: true
        }).trigger("reloadGrid");
    },
    _init: function(){
        if (!this._isLoad) {
            this.initUser();
            this._initItem();
            this._isLoad = true;
        }
        else {
            this._reloadGrid();
        }
    },
    checkFollowKind: function(){
        var cnt = 0;
        $("#itemSpan_followKind").find("[name=followKind]:checked").each(function(v, k){
            cnt++;
        });
        if (cnt > 0) {
            return false;
        }
        else {
            return true;
        }
    },
    validateDate: function(orgYear, orgMonth, orgDay){
        var date = new Date(orgYear, orgMonth - 1, orgDay);
        var year = date.getFullYear();
        var month = date.getMonth()+1;
        var day = date.getDate();
        return year == orgYear && month == orgMonth && day == orgDay;
    },
    getSelectItem: function(){
        var data = [];
        $("#itemSpan_followKind").find("[name=followKind]:checked").each(function(v, k){
            data.push($(k).val());
        });
        return data.join("|");
    },
    setOverSeaHideUI: function(overSeaHideVal){
        var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
        if(overSeaHideVal){// && overSeaHideVal == "Y"){
            $("tr[class*=overSeaHide]").hide();
            $("button[class*=overSeaHide]").hide();
            $("span[class*=overSeaHide]").hide();
            $("tr[class*=overSeaShow]").show();
            $("button[class*=overSeaShow]").show();
            $("span[class*=overSeaShow]").show();
        } else {
//                    $(".overSeaHide").show();
            $("tr[class*=overSeaHide]").show();
            $("button[class*=overSeaHide]").show();
            $("span[class*=overSeaHide]").show();
            $("tr[class*=overSeaShow]").hide();
            $("button[class*=overSeaShow]").hide();
            $("span[class*=overSeaShow]").hide();
        }

        if (checkReadonly()) {  // 非可編輯狀態 功能button都要隱藏
            $("button[class*=overSeaHide]").hide();
            $("button[class*=overSeaShow]").hide();
        }
    },
    saveL260M01C: function(dataOid){
        $.ajax({
            handler: inits.fhandle,
            data: $.extend($("#formDetail").serializeData(), {
                formAction: "saveL260M01C",
                oid: dataOid,
                followKind: Action.getSelectItem()
            }),
            success: function(obj){
                if (obj.msg && obj.msg != "") {
                    CommonAPI.showErrorMessage(obj.msg);
                }
                $("#l260m01cGrid").trigger("reloadGrid");
            }
        });
    }              
        
}  //Action_End

var subAction = {
    _isLoad: false,
    certifiedFileGrid: null,
    repayFileGrid: null,
    finProdGrid: null,
    dwFinProdGrid: null,
    query0320Grid: null,
    queryElandGrid: null,
    elandDetailGrid: null,
    raspGrid: null,
    queryRaspGrid: null,
    qRaspFileGrid: null,
    _initEvent: function(dataOid){
        $("#uploadCertifiedFile").click(function(){
            subAction.uploadFile("Certified");
        });
        
        $("#deleteCertifiedFile").click(function(){
            subAction.chkIsRaspAttach().done(function(){
                subAction.deleteFile("Certified");
            });
        });
        
        $("#uploadRepayFile").click(function(){
            subAction.uploadFile("Repay");
        });
        
        $("#deleteRepayFile").click(function(){
            subAction.deleteFile("Repay");
        });
        
        $("#importFinProdData").click(function(){
            subAction.openDwFinProdGrid();
        });
        
        $("#deleteFinProdData").click(function(){
            subAction.deleteFinProd();
        });
        
        $("#getLstFinProdData").click(function(){
            subAction.getLstFinProd();
        });
        
        $("#calNextDt").click(function(){
            subAction.calNextDt(true);
        });

        $("#query0320").click(function(){
            subAction.query0320();
        });

        $("#query0060s").click(function(){
            subAction.query0060s();
        });

        $("#query8250").click(function(){
            subAction.query8250();
        });

        $("#query8410").click(function(){
            subAction.query8410();
        });

        $("#queryEland").click(function(){
            subAction.queryEland();
        });

        $("#downloadEland").click(function(){
            subAction.downloadElandData();
        });

        $("#queryRasp").click(function(){
            subAction.queryRasp();
        });

        $("#downloadRasp").click(function(){
            subAction.downloadRaspData();
        });

        $("#uploadRaspFile").click(function(){
            subAction.uploadRaspFile();
        });

        $("#aFileLink").click(function(){
            $.capFileDownload({
               handler:"simplefiledwnhandler",
               data : {
                   fileOid : $('#fileOid').val()
               }
            });
        });
        
        var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
        if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
            $("#nextFollowDate").datepicker('destroy');
            $("#actStDate").datepicker('destroy');
        }
        else {
            $("#nextFollowDate").datepicker({
                minDate: 1
            });
            $("#actStDate").datepicker({
                maxDate: 0
            });
        }
        
        // 要寫在 setItems 後  才會生效
        $("input[name='followWay']").change(function(k, v){
            var value = $(this).val();
            $("#nextFollowDate").val(""); // 每次變更都清空
            // 1-特定日期 2-循環週期
            if (value == "2") {
                $("#divL260M01C").find("tr[class*=followWayCycle]").show();
                $("#calNextDt").show();
            }
            else {
                $("#divL260M01C").find("tr[class*=followWayCycle]").hide().find(":input").val("");
                $("#calNextDt").hide();
            }
        });
        
        $("input[name='repayUnusualFg']").change(function(k, v){
            var value = $(this).val();
            if (value == "Y") {
                $(".isShowTab2").show();
            }
            else {
                $(".isShowTab2").hide();
                $(".isShowTab2").find(":radio").removeAttr("checked");
                $(".isShowTab2").find("textarea").val("");
            }
        });
        
        
        // 理財商品
        //		$("input[name='finProdFg']").change(function(k, v){
        //			var value = $(this).val();
        //			if(value == "Y") {
        //                $(".isShowTab3").show();
        //            } else {
        //                $(".isShowTab3").hide();
        //            }
        $("input[name='finProdFg']").click(function(){
            if (this.checked) {
                $(".isShowTab3").show();
            }
            else {
                $(".isShowTab3").hide();
            }
        });

        // J-110-0497 餘屋貸款
        $("#proStatus").change(function(k, v){
            var value = $(this).val();
            if (value == "B") { // 落後
                $("#proStatusB").show();
            }
            else {
                $("#behindDesc").val("");
                $("#proStatusB").hide();
            }
        });

        $("#raspStat").change(function(){
            var value = $(this).val();
            if (value == 'Y') {
                $('#trRaspWay').hide();
                $('#trRWayDt').hide();
                $('#trRaspAmt').show();
                $('#trRaspDscr').hide();

                $('#raspWay').val("");
                $('#rWayDt').val("");
                $('#raspDscr').val("");
            } else if (value == 'N') {
                $('#trRaspWay').show();
                $('#trRWayDt').show();
                $('#trRaspAmt').show();
                $('#trRaspDscr').show();

            } else if (value == 'A' || value == 'B') {
                $('#trRaspWay').hide();
                $('#trRWayDt').hide();
                $('#trRaspAmt').show();
                $('#trRaspDscr').show();

                $('#raspWay').val("");
                $('#rWayDt').val("");
            } else {
                $('#trRaspWay').hide();
                $('#trRWayDt').hide();
                $('#trRaspAmt').hide();
                $('#trRaspDscr').hide();

                $('#raspWay').val("");
                $('#rWayDt').val("");
                $('#raspAmt').val("");
                $('#raspDscr').val("");
            }
        });
        $("#raspWay").change(function(){
            var value = $(this).val();
            if (value == '1' || value == '2') {
                $('#trRWayDt').show();
                $('#trRaspAmt').show();
                $('#trRaspDscr').hide();

                $('#raspDscr').val("");
            } else if (value == '3') {
                $('#trRWayDt').show();
                $('#trRaspAmt').show();
                $('#trRaspDscr').show();
            } else if (value == '4') {
                $('#trRWayDt').hide();
                $('#trRaspAmt').show();
                $('#trRaspDscr').show();

                $('#rWayDt').val("");
            } else {

            }

        });

        $("input[name='begConstr']").change(function(k, v){
            var value = $(this).val();
            if (value == 'Y') {
                $('#constrHide').show();
            } else if (value == 'N') {
                $('#actStDate').val("");
                $('#constrHide').hide();
            } else {
                $('#actStDate').val("");
                $('#constrHide').hide();
            }
        });
        
        subAction._initFileGrid(dataOid);
    },
    _initFileGrid: function(dataOid){
        this.certifiedFileGrid = $("#certifiedFileGrid").iGrid({
            height: 80,
            handler: inits.ghandle,
            sortorder: 'asc',
            action: "queryFile",
            localFirst: true,
            postData: {
                oid: dataOid,
                fieldId: "postLoanCertified"
            },
            loadComplete: function(){
                $('#certifiedFileGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
                colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
                width: 80,
                name: 'srcFileName',
                sortable: true,
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                    $.capFileDownload({
                        handler: "simplefiledwnhandler",
                        data: {
                            fileOid: rowObject.oid
                        }
                    });
                }
            }, {
                colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
                name: 'fileDesc',
                width: 100,
                align: "center",
                sortable: true
            }, {
                colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
                name: 'uploadTime',
                width: 100,
                align: "center",
                sortable: true
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }]
        });
        
        this.repayFileGrid = $("#repayFileGrid").iGrid({
            height: 100,
            handler: inits.ghandle,
            sortorder: 'asc',
            action: "queryFile",
            localFirst: true,
            postData: {
                oid: dataOid,
                fieldId: "postLoanRepay"
            },
            loadComplete: function(){
                $('#repayFileGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
                colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
                width: 80,
                name: 'srcFileName',
                sortable: true,
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                    $.capFileDownload({
                        handler: "simplefiledwnhandler",
                        data: {
                            fileOid: rowObject.oid
                        }
                    });
                }
            }, {
                colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
                name: 'fileDesc',
                width: 100,
                align: "center",
                sortable: true
            }, {
                colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
                name: 'uploadTime',
                width: 100,
                align: "center",
                sortable: true
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }]
        });
        
        subAction._reloadFileGrid("Certified", dataOid);
        subAction._reloadFileGrid("Repay", dataOid);

        this.finProdGrid = $("#finProdGrid").iGrid({
            height: 100,
            handler: inits.ghandle,
            action: "queryL260s01aList",
            localFirst: true,
            multiselect: true,
            postData: {
                oid: dataOid
            },
            loadComplete: function(){
                $('#finProdGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
                colHeader: i18n.lms8000m01['L260S01A.proType'],//商品類別,
                width: 20,
                name: 'proTypeStr',
                align: "center"
            }, {
                colHeader: i18n.lms8000m01['L260S01A.accNo'],//編號,
                width: 70,
                name: 'accNo',
                align: "center"
            }, {
                colHeader: i18n.lms8000m01['L260S01A.bankPro'],//商品
                width: 120,
                name: 'bankPro',
                align: "left",
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                    subAction.openFinProdDetail(null, null, rowObject);
                }
            }, {
                colHeader: i18n.lms8000m01['L260S01A.lstBuy'],//近一次申購
                width: 30,
                name: 'lstBuy',
                formatter: function(cellvalue, options, rowObject){
                    return cellvalue == null || cellvalue == "0001-01-01" ? "" : cellvalue;
                },
                align: "center"
            }, {
                colHeader: i18n.lms8000m01['L260S01A.lstSell'],//近一次贖回/到期
                width: 30,
                name: 'lstSell',
                formatter: function(cellvalue, options, rowObject){
                    return cellvalue == null || cellvalue == "0001-01-01" ? "" : cellvalue;
                },
                align: "center"
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }, {
                colHeader: "proType",
                name: 'proType',
                hidden: true
            }, {
                colHeader: "bankProCode",
                name: 'bankProCode',
                hidden: true
            }]
        });
        subAction._reloadGrid("finProdGrid", dataOid);
        
        this.dwFinProdGrid = $("#dwFinProdGrid").iGrid({
            height: 100,
            handler: inits.ghandle,
            action: "queryDwFinProdList",
            needPager: false,
            multiselect: true,
            postData: {
                custId: $("#custId").val(),
                dupNo: $("#dupNo").val(),
                bgnDate: $("#followDate").val(),
                mainId: $("#oidL260M01D").val()
            },
            //            loadComplete : function() {
            //                $('#dwFinProdGrid a').click(function(e) {
            //                    // 避免<a href="#"> go to top
            //                    e.preventDefault();
            //                });
            //            },
            loadComplete: function(data){
                var allRows = $('#dwFinProdGrid').jqGrid('getDataIDs');
                for (var i = 0; i < allRows.length; i++) {
                    var rowid = allRows[i];
                    var checked = $('#dwFinProdGrid').jqGrid('getCell', rowid, 'checked');
                    if (checked == "true") {
                        $("#dwFinProdGrid").setSelection(rowid);
                    }
                }
                $('#dwFinProdGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
                colHeader: i18n.lms8000m01['L260S01A.proType'],//商品類別,
                width: 25,
                name: 'proTypeStr',
                align: "center"
            }, {
                colHeader: i18n.lms8000m01['L260S01A.accNo'],//編號,
                width: 70,
                name: 'accNo',
                align: "center"
            }, {
                colHeader: i18n.lms8000m01['L260S01A.bankPro'],//商品
                width: 150,
                name: 'bankPro',
                align: "left"
            }, {
                colHeader: i18n.lms8000m01['L260S01A.lstBuy'],//近一次申購
                width: 30,
                name: 'lstBuy',
                formatter: function(cellvalue, options, rowObject){
                    return cellvalue == null || cellvalue == "0001-01-01" ? "" : cellvalue;
                },
                align: "center"
            }, {
                colHeader: i18n.lms8000m01['L260S01A.lstSell'],//近一次贖回/到期
                width: 30,
                name: 'lstSell',
                formatter: function(cellvalue, options, rowObject){
                    return cellvalue == null || cellvalue == "0001-01-01" ? "" : cellvalue;
                },
                align: "center"
            }, {
                colHeader: "proType",
                name: 'proType',
                hidden: true
            }, {
                colHeader: "bankProCode",
                name: 'bankProCode',
                hidden: true
            }, {
                colHeader: "custId",
                name: 'custId',
                hidden: true
            }, {
                colHeader: "custDupNo",
                name: 'custDupNo',
                hidden: true
            }, {
                colHeader: "key",
                name: 'key',
                hidden: true
            }, {
                colHeader: "checked",
                name: 'checked',
                hidden: true
            }]
        });

        this.query0320Grid = $("#query0320Grid").iGrid({
            height: 270,
            handler: inits.ghandle,
            action: "query0320List",
            needPager: false,
            multiselect: false,
            postData: {
                init: true
            },
            loadComplete: function(data){

            },
            colModel: [{
                colHeader: "帳戶",
                sortable : false,
                width: 70,
                name: 'fakeActNo',
                align: "center"
            }, {
                colHeader: "幣別",
                sortable : false,
                width: 25,
                name: 'curr',
                align: "center"
            }, {
                colHeader: "currCode",
                name: 'currCode',
                hidden: true
            }, {
                colHeader: "realActNo",
                name: 'realActNo',
                hidden: true
            }, {
                colHeader: "q0060Flag",
                name: 'q0060Flag',
                hidden: true
            }, {
                colHeader: "0060交易",
                sortable : false,
                width: 25,
                name: 'q0060Btn',
                align: "center",
                formatter: function(cellvalue, options, rowObject){
                    if(cellvalue == 'N'){
                        return "";
                    } else {
                        return "<input type='button' value='查詢' class='btn0060' name='" + rowObject + "'>";
                    }
                }
            }]
        });

        $('#query0320Grid').delegate('input.btn0060', 'click', function(){
            var rowObj = $(this).prop('name');
            subAction.datePeriodFormReset('btt0060').done(function(){
                $("#datePeriod").thickbox({
                    title: i18n.lms8000m01['datePeriod'],
                    width: 600,
                    height: 160,
                    align: "center",
                    valign: "bottom",
                    i18n: i18n.def,
                    buttons: {
                        "sure": function(){
                            subAction.chkDatePeriod('btt0060', $("#dateBgn").val(), $("#dateEnd").val()).done(function(){
                                $.thickbox.close();

                                $.form.submit({
                                    url: "../../simple/FileProcessingService",
                                    target: "_blank",
                                    data: {
                                        mainId: $("#oidL260M01D").val(),
                                        rowObj : rowObj,
                                        bgnDate: $("#dateBgn").val(),
                                        endDate: $("#dateEnd").val(),
                                        actType: $("[name=actType]:checked").val(),
                                        type: "R03",
                                        fileDownloadName: "lms8000r03.pdf",
                                        serviceName: "lms8000r01rptservice"
                                    }
                                });

                                // 設定5秒reoload
                                setTimeout(function(){
                                    subAction._reloadFileGrid("Certified", $("#oidL260M01D").val());
                                }, 5000);
                            });
                        },
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });
            });
        });

        this.queryElandGrid = $("#queryElandGrid").iGrid({
            height: 250,
            handler: inits.ghandle,
            action: "queryElandList",
            needPager: false,
            multiselect: false,
            postData: {
                init: true
            },
            loadComplete: function(data){

            },
            colModel: [{
                name : 'c101m01MainId',
                hidden : true
            }, {
                colHeader: "申請編號",
                sortable : false,
                width: 100,
                name: 'applyNo',
                align: "center"
            }, {
                colHeader: "標的物名稱",
                sortable : false,
                width: 180,
                name: 'target',
                align: "left"
            }, {
                colHeader: "申請日期",
                sortable : false,
                width: 80,
                name: 'applyDt',
                align: "center"
            }, {
                colHeader: "謄本申請日期",
                sortable : false,
                width: 80,
                name: 'elandDt',
                align: "center"
            }, {
                colHeader: "明細",
                sortable : false,
                width: 25,
                name: 'qElandBtn',
                align: "center",
                formatter: function(cellvalue, options, rowObject){
                    if(cellvalue == 'N'){
                        return "";
                    } else if(cellvalue == 'Y'){
                        return "<input type='button' value='開啟' class='btnEland' name='" + rowObject + "'>";
                    } else {
                        return "";
                    }
                }
            }]
        });

        $('#queryElandGrid').delegate('input.btnEland', 'click', function(){
            var rowObj = $(this).prop('name');
            var sfs = rowObj + '';
            var c101m01MainId = sfs.split(",")[0];

            subAction.openElandDetail(c101m01MainId);
        });

        this.elandDetailGrid = $("#elandDetailGrid").iGrid({
            height: 250,
            handler: inits.ghandle,
            action: "queryElandDetail",
            needPager: false,
            multiselect: true,
            postData: {
                init: true
            },
            loadComplete: function(data){

            },
            colModel: [{
                name : 'c100s02aOid',
                hidden : true
            }, {
                name : 'pathFlag',
                hidden : true
            },
            /*
            {
                name : 'filePath',
                hidden : true
            },
            */
            {
                colHeader: "文件說明",
                sortable : false,
                width: 180,
                name: 'fileDesc',
                align: "left"
            }, {
                colHeader: "執行",
                sortable : false,
                width: 25,
                name: 'oElandBtn',
                align: "center",
                formatter: function(cellvalue, options, rowObject){
                    if(cellvalue == 'N'){
                        return "";
                    } else if(cellvalue == 'Y'){
                        return "<input type='button' value='開啟' class='btnOpenEland' name='" + rowObject + "'>";
                    } else {
                        return "";
                    }
                }
            }]
        });

        $('#elandDetailGrid').delegate('input.btnOpenEland', 'click', function(){
            var rowObj = $(this).prop('name');
            var sfs = rowObj + '';
            var c100s02aOid = sfs.split(",")[0];
//            var path = sfs.split(",")[1];

            subAction.openElandData(c100s02aOid);
//            window.open(path,'eland',"scrollbars=yes,resizable=yes,status=no");
        });

        this.raspGrid = $("#raspGrid").iGrid({
            height: 100,
            handler: inits.ghandle,
            action: "queryL260s01cList",
            localFirst: true,
            multiselect: false,
            postData: {
                oid: dataOid
            },
            loadComplete: function(){
                $('#raspGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }, {
                colHeader: "分行",
                sortable : false,
                width: 15,
                name: 'branch',
                align: "center"
            }, {
                colHeader: "統一編號",
                sortable : false,
                width: 30,
                name: 'custId',
                align: "center"
            }, {
                colHeader: "姓名",
                sortable : false,
                width: 100,
                name: 'custName',
                align: "center"
            }, {
                colHeader: "擔保品編號",
                sortable : false,
                width: 180,
                name: 'collNo',
                align: "center",
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                    subAction.openRaspBox(null, null, rowObject);
                }
            }, {
                colHeader: " ",
                name: 'checkYN',
                align: 'center',
                width: 8,
                formatter: function(cellvalue, options, rowObject){
                    if (cellvalue == "Y") {
                        return "O";
                    }else {
                        return "X";
                    }
                }
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#raspGrid").getRowData(rowid);
                subAction.openRaspBox(null, null, data);
            }
        });

        this.queryRaspGrid = $("#queryRaspGrid").iGrid({
            height: 150,
            handler: inits.ghandle,
            action: "queryRaspList",
            needPager: false,
            multiselect: true,
            postData: {
                init: true
            },
            loadComplete: function(data){

            },
            colModel: [{
                name : 'c101m29Oid',
                hidden : true
            }, {
                name : 'c101m29MainId',
                hidden : true
            }, {
                name : 'contractPriceStr',
                hidden : true
            }, {
                colHeader: "分行",
                sortable : false,
                width: 15,
                name: 'branch',
                align: "center"
            }, {
                colHeader: "統一編號",
                sortable : false,
                width: 30,
                name: 'custId',
                align: "center"
            }, {
                colHeader: "擔保品編號",
                sortable : false,
                width: 100,
                name: 'collNo',
                align: "center"
            }, {
                colHeader: "查詢實價登錄日期",
                sortable : false,
                width: 80,
                name: 'queryDtStr',
                align: "center"
            }, {
                name : 'custName',
                hidden : true
            }, {
                colHeader: "執行",
                sortable : false,
                width: 25,
                name: 'qRaspBtn',
                align: "center",
                formatter: function(cellvalue, options, rowObject){
                    if(cellvalue == 'N'){
                        return "";
                    } else if(cellvalue == 'Y'){
                        return "<input type='button' value='開啟' class='btnRasp' name='" + rowObject + "'>";
                    } else {
                        return "";
                    }
                }
            }]
        });

        $('#queryRaspGrid').delegate('input.btnRasp', 'click', function(){
            var rowObj = $(this).prop('name');
            var sfs = rowObj + '';
            var c101m29Oid = sfs.split(",")[0];

            subAction.openRaspData(c101m29Oid);
        });

        this.qRaspFileGrid = $("#qRaspFileGrid").iGrid({
            height: 40,
            handler: inits.ghandle,
            action: "queryRaspFileByFileOid",
            postData: {
                init: true
            },
            loadComplete: function(){
            },
            colModel: [{
                colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
                width: 80,
                name: 'srcFileName',
                sortable: true,
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                    $.capFileDownload({
                        handler: "simplefiledwnhandler",
                        data: {
                            fileOid: rowObject.oid
                        }
                    });
                }
            }, {
                colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
                name: 'fileDesc',
                width: 100,
                align: "center",
                sortable: true
            }, {
                colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
                name: 'uploadTime',
                width: 100,
                align: "center",
                sortable: true
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }]
        });
    },
    _reloadGrid: function(src, dataOid){
        var srcGrid = null;
        if (src == "finProdGrid") {
            srcGrid = this.finProdGrid;
        } else if (src == "raspGrid") {
            srcGrid = this.raspGrid;
        }
        srcGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料
            postData: {
                oid: dataOid
            }
        }).trigger("reloadGrid");
    },
    _reloadFileGrid: function(src, dataOid){
        var fileGrid = null;
        if (src == "Certified") {
            fileGrid = this.certifiedFileGrid;
        }
        else 
            if (src == "Repay") {
                fileGrid = this.repayFileGrid;
            }
        
        fileGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料
            postData: {
                oid: dataOid,
                fieldId: "postLoan" + src
            }
        }).trigger("reloadGrid");
    },
    openDwFinProdGrid: function(){
        this.dwFinProdGrid.jqGrid("setGridParam", {
            postData: {
                custId: $("#custId").val(),
                dupNo: $("#dupNo").val(),
                bgnDate: $("#followDate").val(),
                mainId: $("#oidL260M01D").val()
            },
            search: true
        }).trigger("reloadGrid");
        
        $("#dwFinProdView").thickbox({
            title: i18n.lms8000m01['L260M01D.tab3'],
            width: 800,
            height: 250,
            modal: true,
            buttons: {
                "sure": function(){
                    subAction.importFinProd();
                    /*
                     var rows = $("#dwFinProdGrid").getGridParam('selarrrow');
                     var data = [];
                     if (rows == "") {// action_005=請先選取一筆以上之資料列
                     return CommonAPI.showErrorMessage(i18n.def["action_005"]);
                     }
                     
                     var count = $("#finProdGrid").jqGrid('getGridParam','records');
                     if(count > 0){
                     // confirmBeforeDeleteAll=執行時會刪除已存在之資料，是否確定執行？
                     CommonAPI.confirmMessage(i18n.def["confirmBeforeDeleteAll"], function(b){
                     if (b) {
                     subAction.importFinProd();
                     }
                     });
                     }else{
                     subAction.importFinProd();
                     }
                     */
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    importFinProd: function(){
        var rows = $("#dwFinProdGrid").getGridParam('selarrrow');
        var data = [];
//        if (rows == "") {// action_005=請先選取一筆以上之資料列
//            return CommonAPI.showErrorMessage(i18n.def["action_005"]);
//        }
        for (var i in rows) {
            var rowData = $("#dwFinProdGrid").getRowData(rows[i]);
            data.push(rowData.custId + "^" + rowData.custDupNo + "^" + rowData.proType + "^" + rowData.bankProCode + "^" + rowData.accNo);
        }
//        var str = i18n.def["confirmRun"];
//        CommonAPI.confirmMessage(i18n.def["confirmBeforeDeleteAll"], function(b){
//            if (b) {
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "importFinProdData",
                        keys: data,
                        mainId: $("#oidL260M01D").val()
                    },
                    success: function(obj){
                        subAction._reloadGrid("finProdGrid", $("#oidL260M01D").val());
                        // 要強制按儲存才會更新MIS
                        subAction.changeL260m01dYN($("#oidL260M01D").val());
                        $.thickbox.close();
						if (obj.msg && obj.msg != "") {
                            CommonAPI.showMessage(obj.msg);
                        }
                    }
                });
//            }
//        });
    },
    deleteFinProd: function(){
        var rows = $("#finProdGrid").getGridParam('selarrrow');
        var data = [];
        if (rows == "") {// action_005=請先選取一筆以上之資料列
            return CommonAPI.showErrorMessage(i18n.def["action_005"]);
        }
        for (var i in rows) {
            var rowData = $("#finProdGrid").getRowData(rows[i]);
            data.push(rowData.oid);
        }
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "deleteL260s01a",
                oids: data
            },
            success: function(obj){
                subAction._reloadGrid("finProdGrid", $("#oidL260M01D").val());
                // 要強制按儲存才會更新MIS
                subAction.changeL260m01dYN($("#oidL260M01D").val());
                $.thickbox.close();
            }
        });
    },
    query0320: function(){
        this.query0320Grid.jqGrid("setGridParam", {
            postData: {
                init: false,
                custId: $("#custId").val(),
                dupNo: $("#dupNo").val()
            },
            search: true
        }).trigger("reloadGrid");

        $("#query0320View").thickbox({
            title: "0320",
            width: 400,
            height: 450,
            modal: true,
            buttons: {
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    query0060s: function(){
        var rows = $('#query0320Grid').getGridParam('selarrrow');

        if (rows == 'undefined' || rows == null || rows == "") {
            CommonAPI.showErrorMessage(i18n.def["action_005"]);
            return false;
        }

        var dataArr = [];
        var errArr = [];
        var hasErr = false;
        $.each(rows, function(i, data){
            var ret = $('#query0320Grid').getRowData(data);
            if(ret.q0060Flag == 'N'){
                errArr.push(ret.fakeActNo);
                hasErr = true;
            } else {
                dataArr.push(ret.fakeActNo+","+ret.curr+","+ret.currCode+","+ret.realActNo+","+ret.q0060Flag);
            }
        });

        if(hasErr){
            var errNoArr = errArr.filter(function(element, index, arr){
                return arr.indexOf(element) === index;
            });

            CommonAPI.showErrorMessage("以下帳號不可查詢0060：<br>" + errNoArr.join("、"));
            return false;
        }

        if (dataArr.length == 0) {
            CommonAPI.showMessage(i18n.def['grid.selrow']);
            return false;
        }

        subAction.datePeriodFormReset('btt0060').done(function(){
            $("#datePeriod").thickbox({
                title: i18n.lms8000m01['datePeriod'],
                width: 600,
                height: 150,
                align: "center",
                valign: "bottom",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        subAction.chkDatePeriod('btt0060', $("#dateBgn").val(), $("#dateEnd").val()).done(function(){
                            $.thickbox.close();
                            $.thickbox.close();

                            $.form.submit({
                                url: "../../simple/FileProcessingService",
                                target: "_blank",
                                data: {
                                    mainId: $("#oidL260M01D").val(),
                                    rowObj : dataArr.join("^"),
                                    muti: true,
                                    bgnDate: $("#dateBgn").val(),
                                    endDate: $("#dateEnd").val(),
                                    actType: $("[name=actType]:checked").val(),
                                    type: "R03",
                                    fileDownloadName: "lms8000r03.pdf",
                                    serviceName: "lms8000r01rptservice"
                                }
                            });

                            // 設定5秒reoload
                            setTimeout(function(){
                                subAction._reloadFileGrid("Certified", $("#oidL260M01D").val());
                            }, 5000);
                        });
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        });
    },
    query8250: function(){
        subAction.datePeriodFormReset('btt8250').done(function(){
            $("#datePeriod").thickbox({
                title: i18n.lms8000m01['datePeriod'],
                width: 600,
                height: 380,
                align: "center",
                valign: "bottom",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        var ioFlag = $("[name=ioFlag]:checked").val();
                        var remitType = $("#remitType :selected").val();
                        var begAmt = $.trim($("#begAmt").val());
                        var endAmt = $.trim($("#endAmt").val());
                        var bankId = $.trim($("#bankId").val());
                        var ractNo = $.trim($("#ractNo").val());
                        var actType = $("[name=actType]:checked").val();

                        // 匯出入別
                        if (ioFlag == "") {
                            return CommonAPI.showErrorMessage(i18n.lms8000m01["checkSelect"] + i18n.lms8000m01["btt.ioFlag"]);
                        }

                        // 匯款種類
                        if (remitType == "") {
                            return CommonAPI.showErrorMessage(i18n.lms8000m01["checkSelect"] + i18n.lms8000m01["btt.remitType"]);
                        }

                        // 起始金額     不會是負數；金額區間不可同時為零；起始金額不可大於結束金額
                        // 整數11位 小數2位
                        if ($.trim(begAmt) != "" || $.trim(endAmt) != "") {
                            if(begAmt != "" && endAmt != "") {  // 兩個都有輸入
                                if(!subAction.chkAmtFormat(begAmt) || !subAction.chkAmtFormat(endAmt)){
                                    return;
                                }
                                if(parseFloat(begAmt) == 0 && parseFloat(endAmt) == 0){
                                    // msg.chkAmt1=金額不可同時為零
                                    return CommonAPI.showErrorMessage(i18n.lms8000m01["msg.chkAmt1"]);
                                }
                                if(parseFloat(begAmt) > parseFloat(endAmt)){
                                    // msg.chkAmt2=起始金額不可大於結束金額
                                    return CommonAPI.showErrorMessage(i18n.lms8000m01["msg.chkAmt2"]);
                                }
                            } else {    // 只輸入其一
                                var chkAmt = (begAmt != "" ? begAmt : endAmt);
                                if(!subAction.chkAmtFormat(chkAmt)){
                                    return;
                                }
                                if(endAmt != "" && parseFloat(endAmt) == 0){
                                    // 沒輸入起始金額  結束金額輸入0   等同於 同時為0
                                    // msg.chkAmt1=金額不可同時為零
                                    return CommonAPI.showErrorMessage(i18n.lms8000m01["msg.chkAmt1"]);
                                }
                            }
                        }

                        // 對方行代號
                        if ($.trim(bankId) != "") {
                            if(bankId.length != 7){
                                // msg.chkBankIdLength=對方行代號請輸入7碼
                                return CommonAPI.showErrorMessage(i18n.lms8000m01["msg.chkBankIdLength"]);
                            } else {
                                var bankId_3 = bankId.substring(0,3);
                                if(/[^\d]/g.test(bankId_3)){    // 前三碼一定是數字
                                    return CommonAPI.showErrorMessage(i18n.def['val.numText']);
                                } else {
                                    var bankId_4 = bankId.substring(3, 7);
                                    if(/[^\d]/g.test(bankId_4) && bankId_4 != "XXXX"){
                                        // 後四碼需為 純數字 或 XXXX(代表依總行查詢)
                                        return CommonAPI.showErrorMessage(i18n.def['val.numText']);
                                    }
                                }
                            }
                        }

                        // 收款人帳號
                        if ($.trim(ractNo) != "") {
                            if(ractNo.length != 14){
                                // msg.chkRactNoLength=收款人帳號請輸入14碼
                                return CommonAPI.showErrorMessage(i18n.lms8000m01["msg.chkRactNoLength"]);
                            } else {
                                if(/[^\d]/g.test(ractNo)){
                                    return CommonAPI.showErrorMessage(i18n.def['val.numText']);
                                }
                            }
                        }

                        subAction.chkDatePeriod('btt8250', $("#dateBgn").val(), $("#dateEnd").val()).done(function(){
                            $.thickbox.close();

                            $.form.submit({
                                url: "../../simple/FileProcessingService",
                                target: "_blank",
                                data: {
                                    mainId: $("#oidL260M01D").val(),
                                    bgnDate: $("#dateBgn").val(),
                                    endDate: $("#dateEnd").val(),
                                    actType: $("[name=actType]:checked").val(),
                                    custId: $("#custId").val(),
                                    dupNo: $("#dupNo").val(),
                                    ioFlag: ioFlag,
                                    remitType: remitType,
                                    begAmt: begAmt,
                                    endAmt: endAmt,
                                    bankId: bankId,
                                    ractNo: ractNo,
                                    type: "R04",
                                    fileDownloadName: "lms8000r04.pdf",
                                    serviceName: "lms8000r01rptservice"
                                }
                            });

                            // 設定5秒reoload
                            setTimeout(function(){
                                subAction._reloadFileGrid("Certified", $("#oidL260M01D").val());
                            }, 5000);
                        });
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        });
    },
    query8410: function(){
        subAction.datePeriodFormReset('btt8410').done(function(){
            $("#datePeriod").thickbox({
                title: i18n.lms8000m01['datePeriod'],
                width: 600,
                height: 80,
                align: "center",
                valign: "bottom",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        $.form.submit({
                            url: "../../simple/FileProcessingService",
                            target: "_blank",
                            data: {
                                mainId: $("#oidL260M01D").val(),
                                actType: $("[name=actType]:checked").val(),
                                type: "R05",
                                fileDownloadName: "lms8000r05.pdf",
                                serviceName: "lms8000r01rptservice"
                            }
                        });

                        // 設定5秒reoload
                        setTimeout(function(){
                            subAction._reloadFileGrid("Certified", $("#oidL260M01D").val());
                        }, 5000);
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        });
    },
    queryEland: function(){
        this.queryElandGrid.jqGrid("setGridParam", {
            postData: {
                init: false,
                dataOid: $("#oidL260M01D").val()
            },
            search: true
        }).trigger("reloadGrid");

        $("#queryElandView").thickbox({
            title: "謄本",
            width: 800,
            height: 400,
            modal: true,
            buttons: {
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    openElandDetail: function(c101m01MainId){
        this.elandDetailGrid.jqGrid("setGridParam", {
            postData: {
                init: false,
                c101m01MainId: c101m01MainId
            },
            search: true
        }).trigger("reloadGrid");

        $("#elandDetailView").thickbox({
            title: "調閱附件",
            width: 400,
            height: 400,
            modal: true,
            buttons: {
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    openElandData: function(c100s02aOid){
        $.ajax({
            handler: inits.fhandle,
            data: {// 把資料轉成json
                formAction: "getC100s02aUrl",
                c100s02aOid: c100s02aOid
            },
            success: function(json){
                if (json.filePath && json.filePath != "") {
                    window.open(json.filePath,'eland',"scrollbars=yes,resizable=yes,status=no");
                } else {
                    CommonAPI.showErrorMessage(i18n.def['noData']);
                    return false;
                }
            }
        });
    },
    downloadElandData: function(){
        var rows = $('#elandDetailGrid').getGridParam('selarrrow');

        if (rows == 'undefined' || rows == null || rows == "") {
            CommonAPI.showErrorMessage(i18n.def["action_005"]);
            return false;
        }

        var dataArr = [];
        var hasErr = false;
        $.each(rows, function(i, data){
            var ret = $('#elandDetailGrid').getRowData(data);
            if(ret.pathFlag != 'Y'){
                hasErr = true;
            } else {
                dataArr.push(ret.c100s02aOid + "," + ret.fileDesc);
            }
        });

        if (dataArr.length == 0) {
            CommonAPI.showMessage(i18n.def['grid.selrow']);
            return false;
        }

        $.ajax({
            handler: inits.fhandle,
            data: {// 把資料轉成json
                formAction: "getC100s02aUrlToAttach",
                oidL260M01D: $("#oidL260M01D").val(),
                rowObj : dataArr.join("^")
            },
            success: function(json){
                subAction._reloadFileGrid("Certified", $("#oidL260M01D").val());
                // 變更附件要強制按儲存才會更新MIS
                subAction.changeL260m01dYN($("#oidL260M01D").val());
            }
        });
    },
    queryRasp: function(){
        this.queryRaspGrid.jqGrid("setGridParam", {
            postData: {
                init: false,
                dataOid: $("#oidL260M01D").val()
            },
            search: true
        }).trigger("reloadGrid");

        $("#queryRaspView").thickbox({
            title: "實價登錄",
            width: 800,
            height: 400,
            modal: true,
            buttons: {
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    openRaspData: function(c101m29Oid){
        $.ajax({
            handler: inits.fhandle,
            data: {// 把資料轉成json
                formAction: "getC101m29Url",
                c101m29Oid: c101m29Oid
            },
            success: function(json){
                if (json.filePath && json.filePath != "") {
                    window.open(json.filePath, "emap", '_blank');
//                    window.open(json.filePath,'eland',"scrollbars=yes,resizable=yes,status=no");
                } else {
                    CommonAPI.showErrorMessage(i18n.def['noData']);
                    return false;
                }
            }
        });
    },
    downloadRaspData: function(){
        var rows = $('#queryRaspGrid').getGridParam('selarrrow');

        if (rows == 'undefined' || rows == null || rows == "") {
            CommonAPI.showErrorMessage(i18n.def["action_005"]);
            return false;
        }

        var dataArr = [];
        var hasErr = false;
        $.each(rows, function(i, data){
            var ret = $('#queryRaspGrid').getRowData(data);
            dataArr.push(ret.c101m29Oid + "," + ret.c101m29MainId + "," + ret.contractPriceStr +
                            "," + ret.branch + "," + ret.custId + "," + ret.collNo +
                            "," + ret.queryDtStr + "," + ret.custName);
        });

        if (dataArr.length == 0) {
            CommonAPI.showMessage(i18n.def['grid.selrow']);
            return false;
        }

        $.ajax({
            handler: inits.fhandle,
            data: {// 把資料轉成json
                formAction: "getC101m29UrlToAttach",
                oidL260M01D: $("#oidL260M01D").val(),
                rowObj : dataArr.join("^")
            },
            success: function(json){
                subAction._reloadFileGrid("Certified", $("#oidL260M01D").val());
                subAction._reloadGrid("raspGrid", $("#oidL260M01D").val());
                // 變更附件要強制按儲存才會更新MIS
                subAction.changeL260m01dYN($("#oidL260M01D").val());
            }
        });
    },
    openRaspBox: function(cellvalue, options, rowObject){
        $("#raspForm").reset();

        $.ajax({
            handler: inits.fhandle,
            action: "queryL260S01CDetail",
            data: {
                oid: rowObject.oid
            },
            success: function(obj){
                $("#raspForm").injectData(obj);

                $("#qRaspFileGrid").jqGrid("setGridParam", {
                    postData: {
                        init: false,
                        dataOid: rowObject.oid
                    },
                    search: true
                }).trigger("reloadGrid");

                $("#raspStat").triggerHandler("change");
                $("#raspWay").triggerHandler("change");

                $("#raspBox").thickbox({
                    title: i18n.lms8000m01['btn.queryRasp'],
                    width: 600,
                    height: 450,
                    align: "center",
                    valign: "bottom",
                    i18n: i18n.def,
                    buttons: {
                        "saveData": function(){
                            if ($("#raspForm").valid()) {
                                //檢誤實價登錄價格，(查無價格請填0)
                                if ($("#raspStat").val() == 'B' && $("#raspAmt").val() != '0') {
                                    CommonAPI.showErrorMessage("查無價格請填0");
                                    return false;
                                }
                                if ($("#raspStat").val() == 'A' && $("#fileOid").val() == '') {
                                    CommonAPI.showErrorMessage("請上傳佐證資料");
                                    return false;
                                }

                                $.ajax({
                                    handler: inits.fhandle,
                                    data: $.extend($("#raspForm").serializeData(), {
                                        formAction: "saveL260S01C",
                                        oid: rowObject.oid
                                    }),
                                    success: function(obj){
                                        subAction._reloadGrid("raspGrid", $("#oidL260M01D").val());
                                    }
                                });
                            }

                            /*
                            // 檢查輸入欄位
                            var chkFlag = true;
                            var data = [];
                            $("#raspForm").find("input,select").each(function(){
                                var thisId = $(this).prop("id");
                                var thisVal = $.trim($(this).val());
                                if($(this).is(":visible") && (thisVal == "" || thisVal == null)){
                                    data.push(i18n.lms8000m01['L260S01C.' + thisId ]);
                                    chkFlag = false;
                                }
                            });
                            if(!chkFlag){
                                CommonAPI.showErrorMessage(data.join("、") + i18n.lms8000m01['notEmpty']);
                                return false;
                            }
                            */
                        },
                        "close": function(){
                            $.thickbox.close();
                        }
                    }
                });
            }
        });
    },
    chkDatePeriod: function(txnCode, bgnDate, endDate){
        var my_dfd = $.Deferred();
        $.ajax({
            handler: inits.fhandle,
            data: {// 把資料轉成json
                formAction: "chkDatePeriod",
                txnCode: txnCode,
                bgnDate: bgnDate,
                endDate: endDate
            },
            success: function(json){
                if (json.msg && json.msg != "") {
                    CommonAPI.showErrorMessage(json.msg);
                    return false;
                } else {
                    my_dfd.resolve();
                }
            }
        });
        return my_dfd.promise();
    },
    chkAmtFormat: function(chkAmt){
        var chkPos = chkAmt.indexOf(".");
        if(chkPos != -1){   // 有小數點
            if(chkPos > 11){
                CommonAPI.showErrorMessage(i18n.lms8000m01["btt.amt"] + i18n.def('val.numeric', [11]));
                return false;
            }
        } else {   // 找不到小數點
            if(chkAmt.length > 11){
                CommonAPI.showErrorMessage(i18n.lms8000m01["btt.amt"] + i18n.def('val.numeric', [11]));
                return false;
            }
        }

        if(!/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/.test(chkAmt)){
            CommonAPI.showErrorMessage(i18n.lms8000m01["btt.amt"] + i18n.def('val.numeric', [11]) + i18n.def('val.numericFraction', [2]));
            return false;
        }

        return true;
    },
    datePeriodFormReset: function(txnCode){
        var my_dfd = $.Deferred();
        $(".show8250").hide();
        $(".showDatePeriod").hide();
        $("#msg8250").hide();
        $("#msg0060").hide();
        $("#msg8410").hide();
        $("#datePeriodForm").reset();
        var followDate = $("#followDate").val();    // 追蹤日
        var followContent = $("#followContent").val();    // 追蹤事項通知內容
        var tDate = new Date();

        var defaultType = "";
        if(followContent.search("還款") != -1 && followContent.search("撥款") != -1){
        } else if(followContent.search("還款") != -1){
            defaultType = "1";
        } else if(followContent.search("撥款") != -1){
            defaultType = "2";
        } else {
        }

        if (txnCode == "btt8250") {
            $(".show8250").show();
            $("#msg8250").show();
            $("#msg0060").hide();
            $("#msg8410").hide();
            $("#remitType").val(0);
            $("input[name='ioFlag']").prop('checked', false);
            $("input[name='ioFlag'][value='" + defaultType + "']").prop("checked", true);
            $(".showDatePeriod").show();
        } else if (txnCode == "btt0060") {
            $("#msg8250").hide();
            $("#msg0060").show();
            $("#msg8410").hide();
            $(".showDatePeriod").show();
        } else if (txnCode == "btt8410") {
            $(".showDatePeriod").hide();
            $("#msg8410").show();
        }

        if(defaultType == "1"){
            var sysdate = followDate.split("-");
            var tDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
            tDate.setMonth(tDate.getMonth() - 1);
            $("#dateBgn").val(tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" + (tDate.getDate() < 10 ? "0" : "") + tDate.getDate());
            $("#dateEnd").val(followDate);
        } else if(defaultType == "2"){
            $("#dateBgn").val(followDate);
            $("#dateEnd").val(API.getToday());
        }

        my_dfd.resolve();
        return my_dfd.promise();
    },
    getLstFinProd: function(){
        var rows = $("#finProdGrid").getGridParam('selarrrow');
        var data = [];
        if (rows == "") {// action_005=請先選取一筆以上之資料列
            return CommonAPI.showErrorMessage(i18n.def["action_005"]);
        }
        for (var i in rows) {
            var rowData = $("#finProdGrid").getRowData(rows[i]);
            data.push(rowData.oid);
        }
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "getLstFinProd",
                oids: data
            },
            success: function(obj){
                subAction._reloadGrid("finProdGrid", $("#oidL260M01D").val());
                // 要強制按儲存才會更新MIS
                subAction.changeL260m01dYN($("#oidL260M01D").val());
                $.thickbox.close();
            }
        });
    },
    openFinProdDetail: function(cellvalue, options, data){
        var $form = $("#formFinProdDetail");
        $form.reset();
        
        $.ajax({
            handler: inits.fhandle,
            action: "queryL260S01ADetail",
            data: {
                oid: data.oid
            },
            success: function(obj){
                $form.injectData(obj);
                if (obj.lstSellDt == "") {
                    $("#hideSell").hide();
                }
                else {
                    $("#hideSell").show();
                }
                
                if (obj.proType == "FUND" || obj.proType == "BD" || obj.proType == "ETF") {
                    $("#hideInvAmt").show();
                }
                else {
                    $("#hideInvAmt").hide();
                }
                
                $("#finProdDetail").thickbox({
                    title: "",
                    width: 450,
                    height: 200,
                    readOnly: true,
                    modal: false
                    //                    buttons: {
                    //		                "cancel": function(){
                    //		                    $.thickbox.close();
                    //		                }
                    //		            }
                });
            }
        });
    },
    uploadFile: function(src){
        var limitFileSize = 10485760;   // 放大至10M, 10*1024*1024
        MegaApi.uploadDialog({
            fieldId: "postLoan" + src,
            fieldIdHtml: "size='30'",
            fileDescId: "fileDesc",
            fileDescHtml: "size='30' maxlength='30'",
            subTitle: i18n.def('insertfileSize', {
                'fileSize': (limitFileSize / 1048576).toFixed(2)
            }),
            limitSize: limitFileSize,
            width: 320,
            height: 190,
            data: {
            	// J-111-0025_05097_B1001 為增進eloan擔保品及貸後管理查詢時價登入作業效率,增加相關作業需求
                mainId: responseJSON.mainId,
                crYear:  CommonAPI.getToday().substring(0,7),
                uid : $("#oidL260M01D").val()
            },
            success: function(obj){
                subAction._reloadFileGrid(src, $("#oidL260M01D").val());
                // 變更附件要強制按儲存才會更新MIS
                subAction.changeL260m01dYN($("#oidL260M01D").val());
            }
        });
        
    },
    deleteFile: function(src){
        var fileGrid = null;
        if (src == "Certified") {
            fileGrid = this.certifiedFileGrid;
        }
        else 
            if (src == "Repay") {
                fileGrid = this.repayFileGrid;
            }
        var select = fileGrid.getGridParam('selrow');
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var data = fileGrid.getRowData(select);
                if (data.oid == "" || data.oid == undefined || data.oid == null) {
                    // TMMDeleteError=請先選擇需修改(刪除)之資料列
                    CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
                    return;
                }
                $.ajax({
                    handler: inits.fhandle,
                    type: "POST",
                    dataType: "json",
                    data: {
                        formAction: "deleteUploadFile",
                        fileOid: data.oid
                    },
                    success: function(obj){
                        subAction._reloadFileGrid(src, $("#oidL260M01D").val());
                        // 變更附件要強制按儲存才會更新MIS
                        subAction.changeL260m01dYN($("#oidL260M01D").val());
                        subAction._reloadGrid("raspGrid", $("#oidL260M01D").val());
                    }
                });
            }
            else {
                return;
            }
        });
    },
    uploadRaspFile: function(){
        var limitFileSize = 5242880;    // 5MB
        MegaApi.uploadDialog({
            fieldId: "postLoanRasp",
            fileDescHtml: "size='30' maxlength='30'",
            subTitle: i18n.def('insertfileSize', {
                'fileSize': (limitFileSize / 1048576).toFixed(2)
            }),
            limitSize: limitFileSize,
            data: {
                mainId: responseJSON.mainId,
                crYear: CommonAPI.getToday().substring(0,7),
                uid : $("#oidL260S01C").val()
            },
            success: function(obj){
                $.ajax({
                    type: "POST",
                    handler: inits.fhandle,
                    action: "afterUploadRaspFile",
                    data: {
                        oidL260S01C: $("#oidL260S01C").val(),
                        newFileOid: obj.fileKey
                    },
                    success: function(responseData){
                        $('#fileOid').val(responseData.fileOid);
                        $('#fileName').val(responseData.fileName);
                    }
                });
            }
        });
    },
    chkIsRaspAttach: function(){
        var my_dfd = $.Deferred();

        var select = $("#certifiedFileGrid").getGridParam('selrow');
        var data = $("#certifiedFileGrid").getRowData(select);
        if (data.oid == "" || data.oid == undefined || data.oid == null) {
            // TMMDeleteError=請先選擇需修改(刪除)之資料列
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }

        $.ajax({
            handler: inits.fhandle,
            type: "POST",
            dataType: "json",
            data: {
                formAction: "chkIsRaspAttach",
                fileOid: data.oid
            },
            success: function(obj){
                if(obj.hasRaspData && obj.hasRaspData == "Y"){
                    CommonAPI.confirmMessage("此為實價登錄附件，將同時清除實價登錄資訊，是否確定刪除？", function(b){
                        if (b) {
                            my_dfd.resolve();
                        } else {
                            my_dfd.reject();
                        }
                    });
                } else {
                    my_dfd.resolve();
                }
            }
        });
        return my_dfd.promise();
    },
    changeL260m01dYN: function(dataOid){
        $.ajax({
            handler: inits.fhandle,
            type: "POST",
            dataType: "json",
            data: {
                formAction: "changeL260m01dYN",
                oid: dataOid
            },
            success: function(obj){
                $("#l260m01dGrid").trigger("reloadGrid");
            }
        });
    },
    checkDt: function(){
        var followCycle = $("#followCycle").val(); // 週期
        var followBgnDate = $("#followBgnDate").val(); // 起日
        var followEndDate = $("#followEndDate").val(); // 止日
        var followWay = $("input[name='followWay']:checked").val();
        if (followWay == "2") {
            if ($.trim(followCycle) == "" || $.trim(followBgnDate) == "") {
                // dataMiss=尚有資料未輸入，請補齊
                CommonAPI.showMessage(i18n.lms8000m01["dataMiss"]);
                return false;
            }
            
            var sysdate = CommonAPI.getToday();
            var tempDt = subAction.getNextDt(followCycle, followBgnDate);
            if (sysdate >= tempDt) {
                // checkDate=循環追蹤起日加計循環追蹤週期為過去日，請確認
                CommonAPI.showErrorMessage(i18n.lms8000m01['checkDate']);
                return false;
            }
            if (followEndDate) {
                if (tempDt > followEndDate) {
                    // checkDate2=下次追蹤日不可超過迄日
                    CommonAPI.showErrorMessage(i18n.lms8000m01['checkDate2']);
                    return false;
                }
            }
            
            if (followBgnDate && followEndDate) {
                if (followBgnDate > followEndDate) {
                    // checkDate3=起日不可超過迄日
                    CommonAPI.showErrorMessage(i18n.lms8000m01['checkDate3']);
                    return false;
                }
            }
            
            var inputStr = followCycle;
            for (var k = 0; k < inputStr.length; k++) {
                var oneChar = inputStr.substring(k, k + 1)
                if (!parseFloat(oneChar)) {
                    if (oneChar != "0") {
                        // checkCycle=週期須為正整數
                        CommonAPI.showErrorMessage(i18n.lms8000m01['checkCycle']);
                        return false;
                    }
                }
            }
            
            return true;
        }
        else {
            if ($("#nextFollowDate").val() == "") {
                // checkNextDate=請選擇或計算下次追蹤日
                CommonAPI.showErrorMessage(i18n.lms8000m01['checkNextDate']);
                return false;
            }
            return true;
        }
    },
    getNextDt: function(cycle, bgnDt){
        var BgnDate = bgnDt.replace(/-/g, "/");
        BgnDate = new Date(BgnDate);
        var bngMonth = BgnDate.getMonth() + 1;
        var year = 0;
        var month = 0;
        var plus = Number(bngMonth) + Number(cycle);
        if (plus > 12) {
            year = parseInt(plus / 12);
            month = plus % 12;
            BgnDate.setYear(BgnDate.getFullYear() + year);
            BgnDate.setMonth(month - 1);
        }
        else {
            BgnDate.setMonth(plus - 1);
        }
        return subAction.dateObjtoStr(BgnDate);
    },
    calDt: "",
    calNextDt: function(showMsg, callback){
        var followCycle = $("#followCycle").val(); // 週期
        var followBgnDate = $("#followBgnDate").val(); // 起日
        var followEndDate = $("#followEndDate").val(); // 止日
        var followWay = $("input[name='followWay']:checked").val();
        if (followWay == "2") {
            var a = subAction.checkDt();
            if (a == true) {
                $.ajax({
                    handler: inits.fhandle,
                    async: false,
                    data: {
                        formAction: "calNextFollowDate",
                        oid: $("#oidL260M01C").val(),
                        followCycle: followCycle,
                        followBgnDate: followBgnDate,
                        followEndDate: followEndDate,
                        showMsg: showMsg
                    },
                    success: function(obj){
                        if(obj.errMsg){
                            CommonAPI.showErrorMessage(obj.errMsg);
                        } else {
                            if (showMsg) {
                                $("#nextFollowDate").val(obj.nextFollowDate);
                            }
                            else {
                                subAction.calDt = obj.nextFollowDate;
                                callback();
                            }
                        }
                        $("#l260m01cGrid").trigger("reloadGrid");
                    }
                });
            }
        }
        else {
            // checkNextDate=請選擇或計算下次追蹤日
            CommonAPI.showErrorMessage("追蹤方式不為循環週期" + i18n.lms8000m01['checkNextDate']);
            return false;
        }
    },
    dateObjtoStr: function(tDate){
        return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" + (tDate.getDate() < 10 ? "0" : "") + tDate.getDate();
    },
    _init: function(dataOid){
        if (!this._isLoad) {
            this._initEvent(dataOid);
            this._isLoad = true;
        }
        else {
            this._reloadFileGrid("Certified", dataOid);
            this._reloadFileGrid("Repay", dataOid);
        }
    }
}

// 驗證readOnly狀態
function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    if (auth.readOnly || responseJSON.mainDocStatus != "01O" || _openerLockDoc == "1") {
        return true;
    }
    return false;
}

pageJsInit(function() {
	var initDfd = initDfd || $.Deferred();
	var initAll = initAll || $.Deferred();
	
	$(function(){
	    var tabForm = $("#mainPanel");
	    Action._init();
	    $("#check1").show();
	    
	    if (checkReadonly()) {
	        $(".readOnlyhide").hide();
	        $("form").lockDoc();
	        _openerLockDoc = "1";
	    }
	    
	    // 查詢 沒有列印 只有列印檢核表
	    if (responseJSON.mainDocStatus == "0CO") {
	        $("#btnPrint").hide();
	        $("#docPanel").hide();
	    }
	    
	    // 呈主管覆核 選授信主管人數
	    $("#numPerson").change(function(){
	        $('#bossItem').empty();
	        var value = $(this).val();
	        if (value) {
	            var html = '';
	            for (var i = 1; i <= value; i++) {
	                var name = 'boss' + i;
	                html += i + '. '
	                // || '授信主管'
	                html += '<select id="' + name + '" name="boss"' +
	                '" class="required" CommonManager="kind:2;type:2" />';
	                html += '<br/>';
	            }
	            $('#bossItem').append(html).find('select').each(function(){
	                $(this).setItems({
	                    item: item,
	                    format: "{value} {key}"
	                });
	            });
	        }
	    });
	    
	    var btn = $("#buttonPanel");
	    btn.find("#btnSave").click(function(showMsg){
	        saveData(true);
	    }).end().find("#btnSend").click(function(){
	        //saveData(false, sendBoss);
	        chkNextDt(false, "Send").done(function(){
	            saveData(false, sendNoBoss);
	        });
	    }).end().find("#btnCheck").click(function(){
	        openCheck();
	    }).end().find("#btnPrint").click(function(){
	        if (checkReadonly()) {
	            printAction();
	        }
	        else {
	            // saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作?
	            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
	                if (b) {
	                    chkNextDt(true, "Print").done(function(){
	                        saveData(false, printAction);
	                    });
	                }
	            });
	        }
	    }).end().find("#btn_printLatestL140M01A").click(function(){
	        printLatestL140M01A($("#mainOid").val());
	    }).end().find("#btn_openLMS9535V01").click(function(){
	        $("#loadLMS9535V01").load("../../rpt/lms9535v01/01", function(){
	            $("#source").val("fms");
	        });
	    }).end().find("#btnPrintR02").click(function(){
	        printActionR02();
	    }).end().find("#btnToEdit").click(function(){
	        // J-109-0336 覆審
	        var unitNo = userInfo ? userInfo.unitNo : "";
	        var unitType = userInfo ? userInfo.unitType : "";
	        if (unitType == "2") {
	            // A: 區域營運中心
	            return CommonAPI.showErrorMessage("使用者無權限");//i18n.def["noAuth"]);
	        }
	        // confirmRun=是否確定執行此功能?
	        CommonAPI.confirmMessage(i18n.def["confirmRun"], function(b){
	            if (b) {
	                $.ajax({
	                    handler: inits.fhandle,
	                    data: {
	                        formAction: "chkL260M01ANotEnd",
	                        single: false,
	                        oid: responseJSON.oid
	                    },
	                    success: function(chkObj){
	                        if (chkObj.msg) {
	                            return CommonAPI.showErrorMessage(i18n.lms8000m01["msg.alreadyHave"]);
	                        }
	                        else {
	                            $.ajax({
	                                handler: inits.fhandle,
	                                data: {// 把資料轉成json
	                                    formAction: "sendToEdit",
	                                    mainId: $("#mainId").val(),
	                                    custId: $("#custId").val(),
	                                    dupNo: $("#dupNo").val(),
	                                    custName: $("#custName").val(),
	                                    cntrNo: $("#cntrNo").val(),
	                                    loanNo: $("#loanNo").val(),
	                                    oid: responseJSON.oid
	                                },
	                                success: function(obj){
	                                    // 先新增主檔 L260M01A Then 清除 M01C.M01D deletetime = null
	                                    $.ajax({
	                                        handler: inits.fhandle,
	                                        action: 'cleanDeletedTime',
	                                        data: {
	                                            mainId: obj.mainId
	                                        },
	                                        success: function(obj){
	                                            CommonAPI.triggerOpener("gridview", "reloadGrid");
	                                            window.close();
	                                        }
	                                    });
	                                }
	                            });
	                        }
	                    }
	                });
	            }
	        });
	    });
	
		function chkNextDt(showMsg, action){
		    var my_dfd = $.Deferred();
		    // 為檢查UI的值是否皆無異常
	        if ($("#mainPanel").valid() == false) {
	            return;
	        }
	
	        $.ajax({
	            handler: inits.fhandle,
	            data: {// 把資料轉成json
	                formAction: "chkNextDt",
	                oid: responseJSON.oid
	            },
	            success: function(json){
	                if (json.msg && json.msg != "") {
	                    if(json.overdue && json.overdue == "Y") {
	                        var confirm = json.msg + "<br/><br/>";
	                        $.each(json.msgMap, function(key, value){ // key: L260M01C.oid
	                            confirm = confirm + value + "<br/>";
	                        });
	                        var actionStr = "";
	                        if(action == "Send") {
	                            actionStr = i18n.abstracteloan['button.send'];
	                        } else if(action == "Print") {
	                            actionStr = i18n.abstracteloan['button.print'];
	                        } else if(action == "Approved") {
	                            actionStr = i18n.abstracteloan['button.check'];
	                        }
	                        confirm = confirm + "<br/>點選[確定]自動更改並" + actionStr + "，點選[取消]請自行修改";
	                        CommonAPI.confirmMessage(confirm, function(b){
	                            if (b) {
	                                var content = "";
	                                $.each(json.nextDateList, function(key, value){ // key: L260M01C.oid
	                                    content = content + key + "^" + value + "|";
	                                });
	                                if (content.length != 0) {
	                                    content = content.substring(0, content.length - 1);
	                                }
	                                $.ajax({
	                                    handler: inits.fhandle,
	                                    data: {
	                                        formAction: "changeNextDt",
	                                        oid: responseJSON.oid,
	                                        nextDateList: content,
	                                        showMsg: showMsg
	                                    },
	                                    success: function(obj){
	                                        $("#l260m01cGrid").trigger("reloadGrid");
	                                        my_dfd.resolve();
	                                    }
	                                });
	                            }
	                        });
	                    } else {
	                        return API.showErrorMessage(json.msg);
	                    }
	                } else {
	                    my_dfd.resolve();
	                }
	            }
	        });
	        return my_dfd.promise();
		}
		// 儲存的動作
	    function saveData(showMsg, tofn){
	    
	        // 為檢查UI的值是否皆無異常
	        if ($("#mainPanel").valid() == false) {
	            return;
	        }
	        
	        $.ajax({
	            handler: inits.fhandle,
	            data: {// 把資料轉成json
	                formAction: "saveL260M01A",
	                oid: responseJSON.oid,
	                showMsg: showMsg
	            },
	            success: function(obj){
	                $('body').injectData(obj);
	                
	                // 執行列印
	                if (!showMsg && tofn) {
	                    tofn();
	                }
	            }
	        });
	    }
	    
	    var item;
	    // 呈主管 - 編製中
	    function sendBoss(){
	        $.ajax({
	            handler: inits.fhandle,
	            action: "checkData",
	            data: {},
	            success: function(json){
	                $('#managerItem').empty();
	                $('#bossItem').empty();
	                item = json.bossList;
	                var bhtml = '1. <select id="boss1" name="boss" class="required" CommonManager="kind:2;type:2"/>';
	                $('#bossItem').append(bhtml).find('select').each(function(){
	                    $(this).setItems({
	                        item: item,
	                        format: "{value} {key}"
	                    });
	                });
	                var html = '<select id="manager" name="manager" class="required" CommonManager="kind:2;type:2" />';
	                $('#managerItem').append(html).find('select').each(function(){
	                    $(this).setItems({
	                        item: item,
	                        format: "{value} {key}"
	                    });
	                });
	                
	                // 是否呈主管覆核？
	                CommonAPI.confirmMessage(i18n.lms8000m01["L260M01B.message01"], function(b){
	                    if (b) {
	                    	$("#selectBossBox").thickbox({
	                            // 覆核
	                            title: i18n.lms8000m01['approve'],
	                            width: 500,
	                            height: 300,
	                            modal: true,
	                            readOnly: false,
	                            valign: "bottom",
	                            align: "center",
	                            i18n: i18n.def,
	                            buttons: {
	                                "sure": function(){
	                                
	                                    var selectBoss = $("select[name^=boss]").map(function(){
	                                        return $(this).val();
	                                    }).toArray();
	                                    
	                                    for (var i in selectBoss) {
	                                        if (selectBoss[i] == "") {
	                                            // 請選擇授信主管
	                                            return CommonAPI.showErrorMessage(i18n.lms8000m01['checkSelect'] +
	                                            i18n.lms8000m01['L260M01B.bossId']);
	                                        }
	                                    }
	                                    if ($("#manager").val() == "") {
	                                        // 請選擇經副襄理
	                                        return CommonAPI.showErrorMessage(i18n.lms8000m01['checkSelect'] +
	                                        i18n.lms8000m01['L260M01B.managerId']);
	                                    }
	                                    // 驗證是否有重複的主管
	                                    if (checkArrayRepeat(selectBoss)) {
	                                        // 主管人員名單重複請重新選擇
	                                        return CommonAPI.showErrorMessage(i18n.lms8000m01['L260M01B.message02']);
	                                    }
	                                    
	                                    flowAction({
	                                        page: responseJSON.page,
	                                        saveData: true,
	                                        selectBoss: selectBoss,
	                                        manager: $("#manager").val()
	                                    });
	                                    $.thickbox.close();
	                                    
	                                },
	                                
	                                "cancel": function(){
	                                    $.thickbox.close();
	                                }
	                            }
	                        });      
	                    }
	                });
	            }
	        });
	    }    
	    
	    // 呈主管(不選簽章欄主管) - 編製中
	    function sendNoBoss(){
	        // 是否呈主管覆核？
	        CommonAPI.confirmMessage(i18n.lms8000m01["L260M01B.message01"], function(b){
	            if (b) {
	            	
	               	$.ajax({
	        	        handler: inits.fhandle,
	        	        action: "checkComVisisVer",
	        	        data: {
	        	            mainId: responseJSON.mainId                				
	        	        },
	        	        success: function(warnObj){
	        				
	        	        	if(warnObj.showMessage !=  ""){
	        					CommonAPI.confirmMessage(warnObj.showMessage, function(b){
	        						if (b) {
	        							flowAction({
	        			                    page: responseJSON.page,
	        			                    saveData: true,
	        			                    sendBoss: true
	        			                    //                    selectBoss: selectBoss,
	        			                    //                    manager: $("#manager").val()
	        			                });
	        						}
	        					});
	        				}else{
	        					flowAction({
	        	                    page: responseJSON.page,
	        	                    saveData: true,
	        	                    sendBoss: true
	        	                    //                    selectBoss: selectBoss,
	        	                    //                    manager: $("#manager").val()
	        	                });
	        				}        	
	            	
	        	        }
	       		 	});	            	
	                
	            }
	        });
	    }
	    
	    // 待覆核 - 覆核
	    function openCheck(){
	        $("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
	            title: i18n.lms8000m01['approve'],
	            width: 100,
	            height: 100,
	            modal: true,
	            readOnly: false,
	            valign: "bottom",
	            align: "center",
	            i18n: i18n.def,
	            buttons: {
	                "sure": function(){
	                    var val = $("[name=checkRadio]:checked").val();
	                    if (!val) {
	                        return CommonAPI.showMessage(i18n.lms8000m01['checkSelect']);
	                    }
	                    $.thickbox.close();
	                    switch (val) {
	                        case "1":
	                            // 一般退回到編製中01O
	                            // 該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
	                            CommonAPI.confirmMessage(i18n.lms8000m01['L260M01B.message03'], function(b){
	                                if (b) {
	                                    flowAction({
	                                        flowAction: false
	                                    });
	                                }
	                            });
	                            break;
	                        case "3":
	                            // 該案件是否確定執行核定作業
	                            CommonAPI.confirmMessage(i18n.lms8000m01['L260M01B.message04'], function(b){
	                                if (b) {
	                                    $.ajax({
	                                        handler: inits.fhandle,
	                                        data: {// 把資料轉成json
	                                            formAction: "getConfirmMsg",
	                                            oid: responseJSON.oid
	                                        },
	                                        success: function(json){
	                                            if (json.msg && json.msg != undefined && json.msg != null && json.msg != "") {
	                                                CommonAPI.confirmMessage(json.msg, function(result){
	                                                    if (result) {
	                                                        chkNextDt(false, "Approved").done(function(){
	                                                            flowAction({
	                                                                flowAction: true,
	                                                                checkDate: CommonAPI.getToday(),//forCheckDate
	                                                                M01cUnids: json.M01cUnids,
	                                                                M01dUnids: json.M01dUnids
	                                                            });
	                                                        });
	                                                    }
	                                                });
	                                            } else {
	                                                chkNextDt(false, "Approved").done(function(){
	                                                    flowAction({
	                                                        flowAction: true,
	                                                        checkDate: CommonAPI.getToday()//forCheckDate
	                                                    });
	                                                });
	                                            }
	                                        }
	                                    });
	                                }
	                            });
	                            break;
	                    }
	                },
	                "cancel": function(){
	                    $.thickbox.close();
	                }
	            }
	        });
	    }
	    
	    function flowAction(sendData){
	        $.ajax({
	            handler: inits.fhandle,
	            data: $.extend({
	                formAction: "flowAction",
	                mainOid: responseJSON.mainOid//$("#mainOid").val()
	            }, (sendData || {})),
	            success: function(){
	                CommonAPI.triggerOpener("gridview", "reloadGrid");
	                window.close();
	            }
	        });
	    }
	    
	    // 列印動作
	    function printAction(){
	        $.form.submit({
	            url: "../../simple/FileProcessingService",
	            target: "_blank",
	            data: {
	                mainId: $("#mainId").val(),//responseJSON.mainId,
	                mainOid: responseJSON.oid,
	                type: "R01",
	                fileDownloadName: "lms8000r01.pdf",
	                serviceName: "lms8000r01rptservice"
	            }
	        });
	    }
	    
	    function printActionR02(){
	        gridPrint.trigger("reloadGrid");
	        
	        $("#printView").thickbox({ // 使用選取的內容進行彈窗
	            title: i18n.def['print'],
	            width: 700,
	            height: 400,
	            readOnly: false,
	            modal: true,
	            buttons: (function(){
	                var btn = {};
	                btn[i18n.def['print']] = function(){
	                    var count = 0;
	                    var content = "";
	                    var id = gridPrint.getGridParam('selarrrow');
	                    for (var i = 0; i < id.length; i++) {
	                        if (id[i] != "") {
	                            var datas = gridPrint.getRowData(id[i]);
	                            content = content + datas.cntrNo + "^" + datas.loanNo + "|";
	                            count++;
	                        }
	                    }
	                    
	                    if (content.length != 0) {
	                        content = content.substring(0, content.length - 1);
	                    }
	                    if (count == 0) {
	                        CommonAPI.showMessage(i18n.def['grid.selrow']);
	                    }
	                    else {
	                        $.form.submit({
	                            url: "../../simple/FileProcessingService",
	                            target: "_blank",
	                            data: {
	                                mainId: $("#mainId").val(),
	                                mainOid: responseJSON.oid,
	                                rptOid: content,
	                                type: "R02",
	                                fileDownloadName: "lms8000r02.pdf",
	                                serviceName: "lms8000r01rptservice"
	                            }
	                        });
	                    }
	                };
	                btn[i18n.def['close']] = function(){
	                    $.thickbox.close();
	                };
	                return btn;
	            })()
	        });
	    }
	    
	    // 檢查陣列內容是否重複
	    function checkArrayRepeat(arrVal){
	        var newArray = [];
	        for (var i = arrVal.length; i--;) {
	            var val = arrVal[i];
	            if ($.inArray(val, newArray) == -1) {
	                newArray.push(val);
	            }
	            else {
	                return true;
	            }
	        }
	        return false;
	    }
	    
	    function printLatestL140M01A(oid){
	        $.ajax({
	            type: "POST",
	            handler: inits.fhandle,
	            data: {
	                formAction: "getPrintL140M01AParam",
	                'oid': oid
	            },
	            success: function(json){
	                var my_dfd = $.Deferred();
	                if (json.notProc) {
	                    API.showPopMessage("", json.notProc, function(){
	                        my_dfd.resolve();
	                    });
	                }
	                else {
	                    my_dfd.resolve();
	                }
	                
	                my_dfd.done(function(){
	                    if (json.parStr) {
	                        $.form.submit({
	                            url: "../../simple/FileProcessingService",
	                            target: "_blank",
	                            data: {
	                                'rptOid': json.parStr,
	                                'fileDownloadName': "cntrNo.pdf",
	                                serviceName: "lms1201r01rptservice"
	                            }
	                        });
	                    }
	                });
	            }
	        });
	    }
	    
	    var gridPrint = $("#printGrid").iGrid({
	        needPager: false,
	        localFirst: true,
	        handler: "lms8000gridhandler",
	        height: 270,
	        rownumbers: true,
	        multiselect: true,
	        hideMultiselect: false,
	        caption: "&nbsp;",
	        hiddengrid: false,
	        sortname: "cntrNo|loanNo",
	        sortorder: 'asc|desc',
	        postData: {
	            formAction: "queryPrintList",
	            mainId: $("#mainId").val(),
	            oid: responseJSON.oid
	        },
	        colModel: [{
	            colHeader: i18n.lms8000m01['L260S01A.cust'],
	            name: 'custId',
	            align: "center",
	            width: 50,
	            sortable: false
	        }, {
	            colHeader: i18n.lms8000m01['cntrNo'],// "額度序號",
	            name: 'cntrNo',
	            align: "center",
	            width: 50,
	            sortable: false
	        }, {
	            colHeader: (userInfo.isOverSea ? i18n.lms8000m01['loanNoOvs'] : i18n.lms8000m01['loanNo']),// "放款帳號",
	            name: 'loanNo',
	            align: "center",
	            width: 50,
	            sortable: false
	        }, {
	            colHeader: "oid",
	            name: 'oid',
	            hidden: true
	        }, {
	            colHeader: "unid",
	            name: 'unid',
	            hidden: true
	        }]
	    });
		
		$("#actStDate").change(function(){
			
			if($(this).val() != ''){
				// 購地貸款如已動工，須另至擔保品管理系統將該額度項下所有地號修改「土地性質」為「房地建地」或其他非空地選項
				CommonAPI.showMessage(i18n.lms8000m01['L260M01D.msg.tips.updateLandPropertiesToHousingOrConstructionLand']);
			}
		});
	})

});
