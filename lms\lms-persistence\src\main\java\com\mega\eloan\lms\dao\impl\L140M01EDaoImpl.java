/* 
 * L140M01EDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L140M01EDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140M01E;



/** 額度聯行攤貸比例檔 **/
@Repository
public class L140M01EDaoImpl extends LMSJpaDao<L140M01E, String> implements
		L140M01EDao {

	@Override
	public L140M01E findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140M01E> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("createTime", false);
		List<L140M01E> list = createQuery(L140M01E.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L140M01E findByUniqueKey(String mainId, String shareBrId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "shareBrId",
				shareBrId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140M01E> findByMainIdAndFlag(String mainId, String[] Flag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IN, "flag", Flag);
		return find(search);
	}

	@Override
	public List<L140M01E> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<L140M01E> list = createQuery(L140M01E.class, search)
				.getResultList();
		return list;
	}
	@Override
	public List<L140M01E> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "shareNo", CntrNo);
		search.addOrderBy("shareNo");
		List<L140M01E> list = createQuery(L140M01E.class,search).getResultList();
		
		return list;
	}
	@Override
	public List<L140M01E> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L140M01E> list = createQuery(L140M01E.class,search).getResultList();
		return list;
	}
}