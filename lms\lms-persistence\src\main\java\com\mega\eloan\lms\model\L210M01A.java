/* 
 * L210M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 異動聯貸案參貸比率檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L210M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L210M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	/**
	 * JOIN條件 L210A01A．關聯檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l210m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L210A01A> l210a01a;

	public Set<L210A01A> getL210a01a() {
		return l210a01a;
	}

	public void setL210a01a(Set<L210A01A> l210a01a) {
		this.l210a01a = l210a01a;
	}

	/**
	 * 動審表文件編號
	 * <p/>
	 * 101/04/18新增<br/>
	 * 來源：L160M01A.mainId
	 */
	@Size(max = 32)
	@Column(name = "REFMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String refMainId;

	/**
	 * 額度明細表來源文件編號
	 * <p/>
	 * 101/04/18新增<br/>
	 * 來源簽報書/聯行額度明細表的文件編號<br/>
	 * 來源：L120M01A.mainId<br/>
	 * 來源：L141M01A.mainId<br/>
	 * 101/07/11 修改 原來的額度明細表mainId
	 */
	@Size(max = 32)
	@Column(name = "SRCMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String srcMainId;

	/** 案件號碼 **/
	@Size(max = 62)
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/** 簽案日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	/** 資料異動日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "MODIFYDATE", columnDefinition = "DATE")
	private Date modifyDate;

	/** 申請概要(幣別) **/
	@Size(max = 3)
	@Column(name = "LTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String ltCurr;

	/**
	 * 申請概要(總額度)
	 * <p/>
	 * 101/03/01調整<br/>
	 * DECIMAL(13,0)(DECIMAL(15,0)<br/>
	 * 單位：元
	 */
	@Digits(integer = 15, fraction = 0, groups = Check.class)
	@Column(name = "LTAMT", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal ltAmt;

	/** 額度序號 **/
	@Size(max = 12)
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/** 本案是否有同業聯貸案額度 **/
	@Size(max = 1)
	@Column(name = "UNITLOANCASE", length = 1, columnDefinition = "CHAR(1)")
	private String unitLoanCase;

	/**
	 * 本案聯貸種類
	 * <p/>
	 * 2012/01/17 新增：<br/>
	 * 1同行聯貸<br/>
	 * 2同業聯貸<br/>
	 * 3同業聯貸&同行聯貸
	 */
	@Size(max = 1)
	@Column(name = "CASETYPE", length = 1, columnDefinition = "CHAR(1)")
	private String caseType;

	/** 經辦 **/
	@Size(max = 6)
	@Column(name = "APPRID", length = 6, columnDefinition = "CHAR(6)")
	private String apprId;

	/** 覆核主管 **/
	@Size(max = 6)
	@Column(name = "RECHECKID", length = 6, columnDefinition = "CHAR(6)")
	private String reCheckId;

	/** 帳戶管理員 **/
	@Size(max = 6)
	@Column(name = "BOSSID", length = 6, columnDefinition = "CHAR(6)")
	private String bossId;

	/** 單位/授權主管 **/
	@Size(max = 6)
	@Column(name = "MANAGERID", length = 6, columnDefinition = "CHAR(6)")
	private String managerId;

	/**
	 * RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	@Size(max = 32)
	@Column(name = "RPTID", length = 32, columnDefinition = "VARCHAR(32)")
	private String rptId;

	/**
	 * 取得動審表文件編號
	 * <p/>
	 * 101/04/18新增<br/>
	 * 來源：L160M01A.mainId
	 */
	public String getRefMainId() {
		return this.refMainId;
	}

	/**
	 * 設定動審表文件編號
	 * <p/>
	 * 101/04/18新增<br/>
	 * 來源：L160M01A.mainId
	 **/
	public void setRefMainId(String value) {
		this.refMainId = value;
	}

	/**
	 * 取得額度明細表來源文件編號
	 * <p/>
	 * 101/04/18新增<br/>
	 * 來源簽報書/聯行額度明細表的文件編號<br/>
	 * 來源：L120M01A.mainId<br/>
	 * 來源：L141M01A.mainId<br/>
	 * 101/07/11 修改 原來的額度明細表mainId
	 */
	public String getSrcMainId() {
		return this.srcMainId;
	}

	/**
	 * 設定額度明細表來源文件編號
	 * <p/>
	 * 101/04/18新增<br/>
	 * 來源簽報書/聯行額度明細表的文件編號<br/>
	 * 來源：L120M01A.mainId<br/>
	 * 來源：L141M01A.mainId<br/>
	 * 101/07/11 修改 原來的額度明細表mainId
	 **/
	public void setSrcMainId(String value) {
		this.srcMainId = value;
	}

	/** 取得案件號碼 **/
	public String getCaseNo() {
		return this.caseNo;
	}

	/** 設定案件號碼 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/** 取得簽案日期 **/
	public Date getCaseDate() {
		return this.caseDate;
	}

	/** 設定簽案日期 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/** 取得資料異動日期 **/
	public Date getModifyDate() {
		return this.modifyDate;
	}

	/** 設定資料異動日期 **/
	public void setModifyDate(Date value) {
		this.modifyDate = value;
	}

	/** 取得申請概要(幣別) **/
	public String getLtCurr() {
		return this.ltCurr;
	}

	/** 設定申請概要(幣別) **/
	public void setLtCurr(String value) {
		this.ltCurr = value;
	}

	/**
	 * 取得申請概要(總額度)
	 * <p/>
	 * 101/03/01調整<br/>
	 * DECIMAL(13,0)(DECIMAL(15,0)<br/>
	 * 單位：元
	 */
	public BigDecimal getLtAmt() {
		return this.ltAmt;
	}

	/**
	 * 設定申請概要(總額度)
	 * <p/>
	 * 101/03/01調整<br/>
	 * DECIMAL(13,0)(DECIMAL(15,0)<br/>
	 * 單位：元
	 **/
	public void setLtAmt(BigDecimal value) {
		this.ltAmt = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}

	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得本案是否有同業聯貸案額度 **/
	public String getUnitLoanCase() {
		return this.unitLoanCase;
	}

	/** 設定本案是否有同業聯貸案額度 **/
	public void setUnitLoanCase(String value) {
		this.unitLoanCase = value;
	}

	/**
	 * 取得本案聯貸種類
	 * <p/>
	 * 2012/01/17 新增：<br/>
	 * 1同行聯貸<br/>
	 * 2同業聯貸<br/>
	 * 3同業聯貸&同行聯貸
	 */
	public String getCaseType() {
		return this.caseType;
	}

	/**
	 * 設定本案聯貸種類
	 * <p/>
	 * 2012/01/17 新增：<br/>
	 * 1同行聯貸<br/>
	 * 2同業聯貸<br/>
	 * 3同業聯貸&同行聯貸
	 **/
	public void setCaseType(String value) {
		this.caseType = value;
	}

	/** 取得經辦 **/
	public String getApprId() {
		return this.apprId;
	}

	/** 設定經辦 **/
	public void setApprId(String value) {
		this.apprId = value;
	}

	/** 取得覆核主管 **/
	public String getReCheckId() {
		return this.reCheckId;
	}

	/** 設定覆核主管 **/
	public void setReCheckId(String value) {
		this.reCheckId = value;
	}

	/** 取得帳戶管理員 **/
	public String getBossId() {
		return this.bossId;
	}

	/** 設定帳戶管理員 **/
	public void setBossId(String value) {
		this.bossId = value;
	}

	/** 取得單位/授權主管 **/
	public String getManagerId() {
		return this.managerId;
	}

	/** 設定單位/授權主管 **/
	public void setManagerId(String value) {
		this.managerId = value;
	}

	/**
	 * 取得RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	public String getRptId() {
		return this.rptId;
	}

	/**
	 * 設定RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 **/
	public void setRptId(String value) {
		this.rptId = value;
	}
}
