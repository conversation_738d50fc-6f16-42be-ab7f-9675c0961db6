$(function(){
	$("#LMS1705S03Grid").iGrid({
		handler : 'lms1705gridhandler',
		height : 350, // 設定高度
		postData : { // 送至server的資料
			mainId : responseJSON.mainId
		},
		multiselect : true, // 是否開啟多選
		colModel : [ {
			name : 'mainId',
			hidden : true
		}, {
			colHeader :i18n.lms1705s03["lms1705.tit01"],// "報表年度",
			align : "center",
			width : 60, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'year' // col.id
		}, {
			colHeader :i18n.lms1705s03["lms1705.tit02"],// "期間(起)",
			align : "right",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'sDate' // col.id
		}, {
			colHeader : i18n.lms1705s03["lms1705.tit03"],//"期間(迄)",
			align : "right",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'eDate'
		}, {
			colHeader : i18n.lms1705s03["lms1705.tit04"],//"確認日期",// 
			align : "right",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'approveTime'
		}, {
			colHeader :i18n.lms1705s03["lms1705.tit07"],// "幣別",
			align : "center",
			width : 40, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'curr' // col.id
		}, {
			colHeader :"財報種類",// "財報種類",
			align : "center",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'gaapFlag' // col.id
		}, {
			colHeader :"行業別",// "行業別",
			align : "center",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'tradeType' // col.id		
		}, {
			colHeader :i18n.lms1705s03["lms1705.tit06"],// "合併財報",
			align : "center",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'conso' // col.id		
		}, {
			colHeader :i18n.lms1705s03["lms1705.tit05"],// "類型",
			align : "center",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'periodType' // col.id
         }, {
			colHeader :i18n.lms1705s03["lms1705.tit08"],// "單位",
			align : "right",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'amtUnit' // col.id
         }
		]
      });
	
	$("#_lms1705S03Button").click(function(){
		$("#LMS1705S03Grid").jqGrid("setGridParam", {
	        postData: {
	    		formAction : "queryCESF101"
	        },
			page : 1,
			//gridPage : 1,
	        search: true
	    }).trigger("reloadGrid");
//		$("#L170M01cForm").reset();

		$("#lms1703s03View").thickbox({
			title : i18n.lms1705s03['L170M01c.insertData2'],// 引進最近三次財務及業務資料
			width : 800,
			height :400,
			align : 'center',
			valign : 'bottom',
		    modal : false,
			i18n : i18n.def,
			buttons : {
				"sure" : function(showMsg,rowObject, cellvalue,options) {
					var nowCurr = "";
					var nowUnit = "";
					var list = [];
					var rows = $("#LMS1705S03Grid").getGridParam('selarrrow');
					if (rows == "") {
						return CommonAPI
								.showMessage(i18n.lms1705s03["L170M01c.error1"]);
					} else {
						var count = 0;
						for (var i = 0; i < rows.length; i++) {
				            if (rows[i] != "") {
				            	var data = $("#LMS1705S03Grid").getRowData(rows[i]);
				            	if(nowCurr == ''){
				            		nowCurr = data.curr;
				            	}else{
				            		if(nowCurr != data.curr){
				            			return CommonAPI
										.showMessage(i18n.lms1705s03["L170M01c.error3"]);
				            		}
				            	}
				            	if(nowUnit == ''){
				            		nowUnit = data.amtunit;
				            	}else{
				            		if(nowUnit != data.amtunit){
				            			return CommonAPI
										.showMessage(i18n.lms1705s03["L170M01c.error3"]);
				            		}
				            	}
				            	list[count] = data.mainId + "-" + data.year;
				            	count = count + 1;
				            }
						}
						if(count > 3){
							return CommonAPI
							.showMessage(i18n.lms1705s03["L170M01c.error2"]);
						}
					}// else End
//					$("#LMS1705S03Grid").trigger("reloadGrid");
					$.thickbox.close();
					$("#lms1703s03Check").find("input[type='checkbox']").removeAttr("checked");

					// J-111-0326 海外覆審作業系統改良第一階段： 1. 加預設選項
					// 國內 LMS1700S03Panel.js
					var defRatioArr = ["20", "11", "12", "22"];
					$.each(defRatioArr, function(idx, val) {
                        $("#lms1703s03Check").find("[name=ratioNo][value="+val+"]").prop("checked", "checked");
                    });
//					<input type="checkbox" id="ratioNo" name="ratioNo"/>
//					var obj = CommonAPI.loadCombos(["lms1205s01_finItem"]);
//			        var data = {
//			            width: "50%",
//			            value: '',
//			            size: 1,
//			            item: obj.lms1205s01_finItem
//			        };
//			        $("#ratioNo").setItems(data);

					$("#lms1703s03Check").thickbox({
					     title : i18n.lms1705s03['L170M01c.insertData2'],// '引進',
					     width : 250,
					     height : 350,
					     align : 'center',
					     valign : 'bottom',
					     modal : false,
					     i18n: i18n.def,
					     buttons : {
					     "sure" : function() {
					    	 if($("[name=ratioNo]:checked").length > 4 ){
					    		 return CommonAPI
									.showMessage(i18n.lms1705s03["err.cantmore4"]);
					    	 }else if($("[name=ratioNo]:checked").length == 0){
					    		 return CommonAPI
									.showMessage(i18n.lms1705s03["err.less1"]);
					    	 }
					     	var ratioNo = $("[name=ratioNo]:checked").map(function() {
	     						return $(this).val();
	     					}).toArray();
							$("#L170M01cForm").reset();
					     	$.ajax({
								handler : "lms1705m01formhandler",
								type : "POST",
								dataType : "json",
								action: "queryFinance",
								data : {
									//formAction : "queryFinance",
									list : list,
									mainId : responseJSON.mainId,
					             	mainOid : responseJSON.oid,
					             	showMsg : true,
					             	txCode : responseJSON.txCode,
					             	ratioNo : ratioNo
					             },
							 }).done(function(responseData){
								var ratioNo1 = responseData.No1;
								var ratioNo2 = responseData.No2;
								var ratioNo3 = responseData.No3;
								var ratioNo4 = responseData.No4;

								$("#L170M01CForm").injectData(responseData);

								$("#ratioNo1").html("");
								$("#ratioNo2").html("");
								$("#ratioNo3").html("");
								$("#ratioNo4").html("");

								$("#ratioNo1").html(i18n.lms1705s03["L170M01c.ratioNo" + ratioNo1]);
								$("#ratioNo2").html(i18n.lms1705s03["L170M01c.ratioNo" + ratioNo2]);
								$("#ratioNo3").html(i18n.lms1705s03["L170M01c.ratioNo" + ratioNo3]);
								$("#ratioNo4").html(i18n.lms1705s03["L170M01c.ratioNo" + ratioNo4]);
							 });
					      	$.thickbox.close();
					      },
					      "cancel" : function() {
					      	$.thickbox.close();
					      }
					     }
					});
				},
				"cancel" : function() {
					$.thickbox.close();
			    }
			}
		})// thickbox括弧
	});// 最外一層括弧
});
