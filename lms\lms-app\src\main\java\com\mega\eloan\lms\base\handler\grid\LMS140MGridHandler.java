/* 
 *  LMS140MGridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.handler.grid;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMS140MM01Page;
import com.mega.eloan.lms.base.service.LMS140MService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.mfaloan.bean.ELF500;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF500Service;
import com.mega.eloan.lms.mfaloan.service.MisELF517Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF030Service;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140MM1A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 央行註記異動作業
 * </pre>
 * 
 * @since 2014/08/28
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms140mgridhandler")
public class LMS140MGridHandler extends AbstractGridHandler {

	@Resource
	LMS140MService lms140mService;
	@Resource
	UserInfoService userInfoService;
	@Resource
	BranchService branchService;
	@Resource
	DocFileService docFileService;
	@Resource
	CodeTypeService codeTypeService;
	@Resource
	LMSService lmsService;
	@Resource
	MisCustdataService miscustdataservice;
	@Resource
	MisLNF030Service mislnf030service;
	@Resource
	MisMISLN20Service misMISln20Service;
	@Resource
	MisdbBASEService misDbService;
	@Resource
	MisELF500Service misELF500Service;
	@Resource
	MisELF517Service misElf517Service;

	/**
	 * 央行註記異動作業grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140mm1a(ISearch pageSetting,
			PageParameters params) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));

		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l140am1a.authUnit", user.getUnitNo());
		
		//
		if(CreditDocStatusEnum.海外_編製中.getCode().equals(docStatus)){
			
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"editType", params.getString("editType"));
		}

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = lms140mService.findPage(
				L140MM1A.class, pageSetting);

		List<L140MM1A> l140mm1alist = (List<L140MM1A>) page.getContent();
		StringBuilder cntrNo = new StringBuilder("");
		for (L140MM1A l140mm1a : l140mm1alist) {
			l140mm1a.setCreator(userInfoService.getUserName(l140mm1a
					.getCreator()));
			// 設定顯示名稱 使用者id+重複序號
			l140mm1a.setCustId(l140mm1a.getCustId() + " " + l140mm1a.getDupNo());
			cntrNo.setLength(0);
		}
		return new CapGridResult(l140mm1alist, page.getTotalRow());

	}

	/**
	 * 查詢央行註記異動作業grid(已覆核)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140mm1a3(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String[] docStatusArray = new String[] { CreditDocStatusEnum.海外_已核准
				.getCode() };
		String type = Util.nullToSpace(params.getString("type"));

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l140am1a.authUnit", user.getUnitNo());
		// 狀態1 是用客戶ID去查
		switch (Util.parseInt(type)) {
		case 1:
			String custId = Util.nullToSpace(params.getString("custId"));
			String dupNo = Util.nullToSpace(params.getString("dupNo"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
					dupNo);
			break;
		case 2:
			String cntrNo = Util.nullToSpace(params.getString("cntrNo"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "cntrNo",
					cntrNo);
			break;
		case 3:
			String approveTime = Util.nullToSpace(params
					.getString("approveTime"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"approveTime", CapDate.parseDate(approveTime));
			break;

		default:
			break;
		}

		Page<? extends GenericBean> page = lms140mService.findPage(
				L140M01M.class, pageSetting);
		List<L140M01M> l140m01ms = (List<L140M01M>) page.getContent();
		StringBuilder cntrNo = new StringBuilder("");
		for (L140M01M l140m01m : l140m01ms) {
			// 設定顯示名稱 使用者id+重複序號
			// l140m01m.setCustId(l140m01m.getCustId() + " " +
			// l140m01m.getDupNo());
			cntrNo.setLength(0);
			// 確認全部動用是否有選
		}
		return new CapGridResult(l140m01ms, page.getTotalRow());
	}

	/**
	 * 從MIS.ELF500查詢額度序號
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */

	public CapMapGridResult queryGetCntrno(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties prop = null;
		prop = MessageBundleScriptCreator
				.getComponentResource(LMS140MM01Page.class);
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String branch = Util.trim(user.getUnitNo());

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		List<ELF500> elf500Data = null;
		boolean queryELF500 = false;
		boolean queryELF383 = false;
		Set<String> elf500CntrNoSet = new HashSet<String>();
		if(custId.length()==10){
			queryELF500 = true;
			queryELF383 = true;
		}else{
			queryELF383 = true;
		}
		
		boolean isNot918Office = !this.lmsService.isOpenUnsoldHouseLoanInfoFunction() || !UtilConstants.BankNo.授管處.equals(branch);
		
		if (queryELF500){
			elf500Data = misELF500Service.findByCustIdALL(custId, dupNo);
			for (ELF500 elf500 : elf500Data) {
				Map<String, Object> row = new HashMap<String, Object>();

				String tBrn = Util.trim(elf500.getElf500_cntrno()).substring(0, 3);
				if (isNot918Office && !tBrn.equals(branch)) {
					// 避免重覆加入一筆空值
					continue;
				}
				String cntrNo = elf500.getElf500_cntrno();
				elf500CntrNoSet.add(cntrNo);
				row.put("cntrNo", cntrNo);
				row.put("sDate", "");
				String version = elf500.getElf500_version();
				row.put("version", StringUtils.isBlank(version) ? UtilConstants.L140m01mVersion.VERSION_OLD : version);
				list.add(row);
			}
		}
		if (queryELF383){
			List<Map<String, Object>> contrnoList = misDbService.selL140M01MForQUOTAPPR(custId);
			for (Map<String, Object> rows : contrnoList){
				Map<String, Object> row = new HashMap<String, Object>();
				String tBrn = Util.trim(rows.get("cntrNo")).substring(0, 3);
				if (isNot918Office && !tBrn.equals(branch)) {
					// 避免重覆加入一筆空值
					continue;
				}
				String cntrNo = Util.trim(rows.get("cntrNo"));
				if(elf500CntrNoSet.contains(cntrNo)){
					continue;
				}
				row.put("cntrNo", cntrNo);
				row.put("sDate", Util.trim(TWNDate.toAD((Date)rows.get("sDate"))));
				String version = rows.get("VERSION") == null ? "" : (String)rows.get("VERSION") ;
				row.put("version", StringUtils.isBlank(version) ? UtilConstants.L140m01mVersion.VERSION_OLD : version);
				list.add(row);
			}
		}
		return new CapMapGridResult(list, list.size());
	}
	
	 @DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public CapGridResult filterL140mm1aByEditCompletedCase(ISearch pageSetting, PageParameters params) throws CapException {
		
    	MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String cntrNo = Util.trim(params.getString("cntrNo"));
		String custId = Util.trim(params.getString("custId"));
		
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));

		String[] docStatusArray = docStatus.split(UtilConstants.Mark.SPILT_MARK);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l140am1a.authUnit", user.getUnitNo());

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		
		if(StringUtils.isNotBlank(cntrNo)){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		}
		
		if(StringUtils.isNotBlank(custId)){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, UtilConstants.Field.CUSTID, custId);
		}
		
		Page<? extends GenericBean> page = lms140mService.findPage(
				L140MM1A.class, pageSetting);

		@SuppressWarnings("unchecked")
		List<L140MM1A> l140mm1alist = (List<L140MM1A>) page.getContent();

		for (L140MM1A l140mm1a : l140mm1alist) {
			l140mm1a.setCreator(userInfoService.getUserName(l140mm1a
					.getCreator()));
			// 設定顯示名稱 使用者id+重複序號
			l140mm1a.setCustId(l140mm1a.getCustId() + " " + l140mm1a.getDupNo());
		}
		return new CapGridResult(l140mm1alist, page.getTotalRow());
	}
}
