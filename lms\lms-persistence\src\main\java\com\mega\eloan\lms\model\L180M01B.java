/* 
 * L180M01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

import javax.persistence.*;
import javax.validation.constraints.Digits;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;

/** 覆審名單明細檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L180M01B", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "ctlType" }))
public class L180M01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 借款人ID
	 * <p/>
	 * ELF412_CUSTID
	 */
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/**
	 * 借款人重覆序號
	 * <p/>
	 * ELF412_DUPNO
	 */
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 區部別
	 * <p/>
	 * 1.DBU、4.OBU、5.海外<br/>
	 * 固定：5.海外
	 */
	@Column(name = "TYPCD", length = 1, columnDefinition = "CHAR(1)")
	private String typCd;

	/**
	 * 文件產生方式
	 * <p/>
	 * 系統產生 | SYS<br/>
	 * 人工產生 | PEO
	 */
	@Column(name = "CREATEBY", length = 3, columnDefinition = "CHAR(3)")
	private String createBY;

	/**
	 * 分行覆審報告表新增註記
	 * <p/>
	 * Y/N<br/>
	 * gfnWrite170M01ToChkList()<br/>
	 * FLMS170M01執行【編製完後儲存並上傳覆審控制檔】時寫回
	 */
	@Column(name = "NEWBY170M01", length = 1, columnDefinition = "CHAR(1)")
	private String newBy170M01;

	/**
	 * 文件狀態
	 * <p/>
	 * 要覆審 | 1<br/>
	 * 不覆審 | 2
	 */
	@Column(name = "DOCSTATUS1", length = 1, columnDefinition = "CHAR(1)")
	private String docStatus1;

	/**
	 * 覆審案號_序號
	 * <p/>
	 * ※預設值「null」，於執行「重新產生批號」時與「主檔_覆審批號」一起給號，依序編號。<br/>
	 * ※依序：<br/>
	 * 1.文件狀態：<br/>
	 * 「1.要覆審」案件依序全數編號完畢後，才開始續編「2.不覆審」案件。<br/>
	 * 2.DBU/OBU共管ID：<br/>
	 * 海外分行帳務獨立，無共管ID。<br/>
	 * 3.借款人ID：<br/>
	 * 依「借款人ID」、「借款人重覆序號」排序。<br/>
	 * 4.調整後上次覆審日/上次覆審日：<br/>
	 * 有「調整後上次覆審日」的依日期昇冪先排序，之後再依「上次覆審日」排序。<br/>
	 * 顯示格式：001
	 */
	@Column(name = "PROJECTSEQ", columnDefinition = "DECIMAL(3,0)")
	private Integer projectSeq;

	/**
	 * 覆審案號
	 * <p/>
	 * 格式為：年度(YYYY) +分行簡稱(3碼)+(兆)+覆審字第+ 批號+ - + 序號 + 號，例：2011蘭雅(兆)覆審字第001-003號
	 */
	@Column(name = "PROJECTNO", length = 64, columnDefinition = "VARCHAR(64)")
	private String projectNo;

	/**
	 * 借款人姓名
	 * <p/>
	 * ELF411_CUSTNAME
	 */
	@Column(name = "ELFCNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String elfCName;

	/**
	 * 行業別<br/>
	 * select * from MIS.BSTBL where ecoCd =(busCd)
	 **/
	@Column(name = "ECONM", length = 60, columnDefinition = "VARCHAR(60)")
	private String eCoNm;

	/**
	 * 負責人
	 * <p/>
	 * 21個全型字
	 */
	@Column(name = "SUP3CNM", length = 60, columnDefinition = "VARCHAR(60)")
	private String sup3CNm;

	/**
	 * 連保人
	 * <p/>
	 * 100個全型字
	 */
	@Column(name = "RLTGUARANTOR", length = 300, columnDefinition = "VARCHAR(300)")
	private String rltGuarantor;

	/**
	 * 共同借款人
	 * <p/>
	 * 100個全型字
	 */
	@Column(name = "RLTBORROWER", length = 300, columnDefinition = "VARCHAR(300)")
	private String rltBorrower;

	/**
	 * 是否為聯貸案
	 * <p/>
	 * Y/N
	 */
	@Column(name = "ULOAN", length = 1, columnDefinition = "CHAR(1)")
	private String uLoan;

	/**
	 * 不覆審代碼 (人工調整)
	 * <p/>
	 * ※不覆審人工調整資訊<br/>
	 * NEW_NCKDFLAG<br/>
	 * 1.本行或同業主辦之聯貸案件，非擔任管理行。<br/>
	 * 2.十成定存。<br/>
	 * 3.純進出押戶。<br/>
	 * 4.對政府或政府所屬機關、學校之授信案件。<br/>
	 * 5.拆放同業或對同業之融通。<br/>
	 * 6.已列報為逾期放款或轉列催收款項之案件。<br/>
	 * 7.銷戶<br/>
	 * 8.本次暫不覆審<br/>
	 * 9.已專案核准免辦理覆審之房屋仲介價金履約保證案件。<br/>
	 * (名單確認後此欄位會更新中心覆審控制檔)
	 */
	@Column(name = "NEWNCKDFLAG", length = 2, columnDefinition = "CHAR(2)")
	private String newNCkdFlag;

	/**
	 * 不覆審備註 (人工調整)
	 * <p/>
	 * ※不覆審人工調整資訊<br/>
	 * NEW_NCKDMEMO<br/>
	 * 100個全型字<br/>
	 * (名單確認後此欄位會更新中心覆審控制檔)
	 */
	@Column(name = "NEWNCKDMEMO", length = 300, columnDefinition = "VARCHAR(300)")
	private String newNCkdMemo;

	/**
	 * 下次恢復覆審日期 (人工調整)
	 * <p/>
	 * ※不覆審人工調整資訊<br/>
	 * NEW_NEXTNWDT<br/>
	 * (名單確認後此欄位會更新中心覆審控制檔)
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "NEWNEXTNWDT", columnDefinition = "DATE")
	private Date newNextNwDt;

	/**
	 * 調整後上次覆審日(人工調整)
	 * <p/>
	 * ※不覆審改要覆審人工調整資訊 NEW_ELF412_LRDATE (名單確認後此欄位會更新中心覆審控制檔) <br/>
	 * 補充：當不覆審 改 要覆審，若不能更改 lrDate，可能該資料會逾期。
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "NEWLRDATE", columnDefinition = "DATE")
	private Date newLRDate;

	/**
	 * 資料日期
	 * <p/>
	 * ※每月月初前置作業更新??<br/>
	 * ELF412_DATADT
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFDATADT", columnDefinition = "DATE")
	private Date elfDataDt;

	/**
	 * 主要授信戶
	 * <p/>
	 * ELF412_MAINCUST<br/>
	 * 是 | Y<br/>
	 * 否 | N<br/>
	 * 1.新戶增額由ELF411<br/>
	 * 2.舊案由STORE PROCEDURE
	 */
	@Column(name = "ELFMAINCUST", length = 1, columnDefinition = "CHAR(1)")
	private String elfMainCust;

	/**
	 * 資信評等類別
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_CRDTYPE<br/>
	 * B=DBU、大型企業、L=DBU中小型企業、O=OBU
	 */
	@Column(name = "ELFCRDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elfCrdType;

	/**
	 * 資信評等
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_CRDTTBL
	 */
	@Column(name = "ELFCRDTTBL", length = 2, columnDefinition = "CHAR(2)")
	private String elfCrdTTbl;

	/**
	 * 信用模型評等類別
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_MOWTYPE<br/>
	 * DBU大型企業 | 1<br/>
	 * DBU中型企業 | 2<br/>
	 * DBU中小型企業|3<br/>
	 * DBU不動產有建案規劃 | 4<br/>
	 * DBU專案融資 | 5<br/>
	 * DBU本國證券公司 | 6<br/>
	 * DBU投資公司一般情況 | 8<br/>
	 * DBU租賃公司 | 9<br/>
	 * DBU一案建商 | A<br/>
	 * DBU非一案建商(擔保/土融) | B<br/>
	 * DBU非一案建商(無擔) | C<br/>
	 * 投資公司情況一 | D<br/>
	 * 投資公司情況二 | E<br/>
	 * OBU境外船舶/航空器 | F
	 */
	@Column(name = "ELFMOWTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elfMowType;

	/**
	 * 信用模型評等
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_MOWTBL1
	 */
	@Column(name = "ELFMOWTBL1", length = 2, columnDefinition = "CHAR(2)")
	private String elfMowTbl1;

	/**
	 * 上上次覆審日
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_LLRDATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFLLRDATE", columnDefinition = "DATE")
	private Date elfLLRDate;

	/**
	 * 上次覆審日
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_LRDATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFLRDATE", columnDefinition = "DATE")
	private Date elfLRDate;

	/**
	 * 覆審週期
	 * <p/>
	 * ELF412_RCKDLINE<br/>
	 * 覆審週期說明：<br/>
	 * A：一年覆審一次。<br/>
	 * B：半年覆審一次(主要授信戶並符合信評條件)。<br/>
	 * C：新戶/增額戶。<br/>
	 * D：異常戶已三個月覆審過- 爾後半年覆審一次。<br/>
	 * E：首次通報之異常戶。（必需在首次通報日後3月內覆審）。<br/>
	 * F：會計師出具保留意見已三個月覆審過- 爾後半年覆審一次。<br/>
	 * G：首次通報有會計師出具保留意見之異常戶。（必需在首次通報日後3月內覆審）。<br/>
	 * H：主管機關指定覆審案件。
	 */
	@Column(name = "ELFRCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elfRCkdLine;

	/**
	 * 上次電腦計算週期 (原始週期)
	 * <p/>
	 * ELF412_OCKDLINE<br/>
	 * ※項目同「覆審週期」<br/>
	 * ※如果沒有異常通報、增額、主管機關指定時之週期，只會為A或B
	 */
	@Column(name = "ELFOCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elfOCkdLine;

	/**
	 * 主管機關指定覆審案件
	 * <p/>
	 * ELF412_UCKDLINE<br/>
	 * 是 | Y<br/>
	 * 否 | N
	 */
	@Column(name = "ELFUCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elfUCkdLINE;

	/**
	 * 主管機關通知日期
	 * <p/>
	 * ELF412_UCKDDT
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFUCKDDT", columnDefinition = "DATE")
	private Date elfUCkdDt;

	/**
	 * 戶況
	 * <p/>
	 * ELF412_CSTATE<br/>
	 * 無餘額 | 0<br/>
	 * 正常 | 1<br/>
	 * 逾期 | 2<br/>
	 * 催收 | 3<br/>
	 * 呆帳 | 4<br/>
	 * (該戶項下最嚴重的代碼)
	 */
	@Column(name = "ELFCSTATE", length = 1, columnDefinition = "CHAR(1)")
	private String elfCState;

	/**
	 * 銷戶日
	 * <p/>
	 * ELF412_CANCELDT<br/>
	 * 全部都沒有額度的日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFCANCELDT", columnDefinition = "DATE")
	private Date elfCancelDt;

	/**
	 * 異常通報代碼
	 * <p/>
	 * ELF412_MDFLAG<br/>
	 * (LN.LNFE0851)<br/>
	 * 停止營業/工廠停工<br/>
	 * 公司、負責人及保證人退票<br/>
	 * 公司聲請重整<br/>
	 * 申請紓困、協議清償、展延還款<br/>
	 * 本金、利息逾期或財務週轉困難<br/>
	 * 負責人、保證人等涉嫌違反法令<br/>
	 * 客戶大筆應收帳款未收現或商糾<br/>
	 * 發生火災、技術授權金發生爭議等重大事件<br/>
	 * 其他足以影響正常營運之事件<br/>
	 * 會計師簽發保留意見（足以影響借戶正常營運、財務狀況者）、否定意見或無法表示意見者
	 */
	@Column(name = "ELFMDFLAG", length = 2, columnDefinition = "CHAR(2)")
	private String elfMDFlag;

	/**
	 * 異常通報日期
	 * <p/>
	 * ELF412_MDDT<br/>
	 * (LN.LNFE0851)
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFMDDT", columnDefinition = "DATE")
	private Date elfMDDt;

	/**
	 * 異常通報情形
	 * <p/>
	 * ELF412_PROCESS<br/>
	 * (LN.LNFE0851)<br/>
	 * 100個全型字
	 */
	@Column(name = "ELFPROCESS", length = 300, columnDefinition = "VARCHAR(300)")
	private String elfProcess;

	/**
	 * 新作/增額/逾放轉正
	 * <p/>
	 * ELF412_NEWADD<br/>
	 * 新作 | N<br/>
	 * 增額 | C<br/>
	 * 逾放轉正 | R
	 */
	@Column(name = "ELFNEWADD", length = 1, columnDefinition = "CHAR(1)")
	private String elfNewAdd;

	/**
	 * 新作/增額/逾放轉正 年月
	 * <p/>
	 * ELF412_NEWDATE<br/>
	 * ※ELF411_DATAYM之資料
	 */
	@Column(name = "ELFNEWDATE", length = 6, columnDefinition = "CHAR(6)")
	private String elfNewDate;

	/**
	 * 不覆審代碼
	 * <p/>
	 * ELF412_NCKDFLAG<br/>
	 * ※項目同「不覆審代碼(人工調整)」
	 */
	@Column(name = "ELFNCKDFLAG", length = 2, columnDefinition = "CHAR(2)")
	private String elfNCkdFlag;

	/**
	 * 不覆審日期
	 * <p/>
	 * ELF412_NCKDDATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFNCKDDATE", columnDefinition = "DATE")
	private Date elfNCkdDate;

	/**
	 * 不覆審備註
	 * <p/>
	 * ELF412_NCKDMEMO<br/>
	 * 100個全型字
	 */
	@Column(name = "ELFNCKDMEMO", length = 300, columnDefinition = "VARCHAR(300)")
	private String elfNCkdMemo;

	/**
	 * 下次恢復覆審日期
	 * <p/>
	 * ELF412_NEXTNWDT
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFNEXTNWDT", columnDefinition = "DATE")
	private Date elfNextNwDt;

	/**
	 * DBUOBU是否有共管
	 * <p/>
	 * ELF412_DBUOBU<br/>
	 * 是 | Y<br/>
	 * 否 | N<br/>
	 * 海外目前無使用<br/>
	 * 固定：否 | N
	 */
	@Column(name = "ELFDBUOBU", length = 1, columnDefinition = "CHAR(1)")
	private String elfDBUOBU;

	/**
	 * 人工維護日
	 * <p/>
	 * ELF412_UPDDATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFUPDDATE", columnDefinition = "DATE")
	private Date elfUpdDate;

	/**
	 * 人工調整ID
	 * <p/>
	 * ELF412_UPDATER
	 */
	@Column(name = "ELFUPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String elfUpdater;

	/**
	 * 其他備註
	 * <p/>
	 * ELF412_MEMO<br/>
	 * 100個全型字
	 */
	@Column(name = "ELFMEMO", length = 300, columnDefinition = "VARCHAR(300)")
	private String elfMemo;

	/**
	 * 資料更新日
	 * <p/>
	 * ELF412_TMESTAMP
	 */
	@Column(name = "ELFTMESTAMP", columnDefinition = "TIMESTAMP")
	private Date elfTmeStamp;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/** 共用戶ID代碼，(用於排序) **/
	@Column(name = "COMAINID", columnDefinition = "CHAR(32)")
	private String coMainId;

	/**
	 * 外部評等類別 <br/>
	 * 標準普爾 | 1 <br/>
	 * 穆迪信評 | 2 <br/>
	 * 惠譽信評 | 3 <br/>
	 * 中華信評 | 4
	 */
	@Column(name = "ELFFCRDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elfFcrdType;

	/**
	 * 外部評等地區別 <br/>
	 * 國際 | 1 <br/>
	 * 本國 | 2
	 */
	@Column(name = "ELFFCRDAREA", length = 1, columnDefinition = "CHAR(1)")
	private String elfFcrdArea;

	/**
	 * 外部評等期間別 <br/>
	 * 長期 | 1 <br/>
	 * 短期 | 2
	 */
	@Column(name = "ELFFCRDPRED", length = 1, columnDefinition = "CHAR(1)")
	private String elfFcrdPred;

	/** 外部評等等級 **/
	@Column(name = "ELFFCRDGRAD", length = 30, columnDefinition = "VARCHAR(30)")
	private String elfFcrdGrad;

	@ToStringExclude
	@OneToMany(mappedBy = "l180m01b", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L180M01D> l180m01ds;

	@Column(name = "BUSCD", length = 6, columnDefinition = "CHAR(6)")
	private String busCd;

	@Column(name = "BUSSKIND", length = 2, columnDefinition = "CHAR(2)")
	private String bussKind;

	/**
	 * 行業別細分類 <br/>
	 * SELECT * FROM ln.lnf07a where LNF07A_KEY_1= 'BUSINESS-SUB-CODE' AND
	 * LNF07A_KEY_2=(RIGHT(BUSCD,4)||BUSSKIND)
	 **/
	@Column(name = "ECONM07A", length = 250, columnDefinition = "VARCHAR(250)")
	private String eCoNm07A;

	/**
	 * 是否為實地覆審 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 */
	@Column(name = "ELFREALCKFG", length = 1, columnDefinition = "CHAR(1)")
	private String elfRealCkFg;

	/**
	 * 最近一次實地覆審時間 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFREALDT", columnDefinition = "DATE")
	private Date elfRealDt;

	/**
	 * 覆審名單類別 J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 */
	@Column(name = "CTLTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String ctlType;

	/**
	 * 舊簽報書MAINID
	 */
	@Column(name = "OLDRPTID", length = 32, columnDefinition = "CHAR(32)")
	private String oldRptId;

	/**
	 * 舊簽報書核准日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "OLDRPTDT", columnDefinition = "DATE")
	private Date oldRptDt;

	/**
	 * 新簽報書MAINID
	 */
	@Column(name = "NEWRPTID", length = 32, columnDefinition = "CHAR(32)")
	private String newRptId;

	/**
	 * 新簽報書核准日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "NEWRPTDT", columnDefinition = "DATE")
	private Date newRptDt;

	/**
	 * J-108-0078_05097_B1001
	 * 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
	 * 
	 * 
	 * 首次往來之新貸戶
	 */
	@Column(name = "ELFISALLNEW", length = 1, columnDefinition = "CHAR(1)")
	private String elfIsAllNew;

    /**
     * 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
     * 純紓困戶
     */
    @Column(name = "ELFISRESCUE", length = 1, columnDefinition = "CHAR(1)")
    private String elfIsRescue;

	/** 信保擔保註記
	 * 2020/04 設定為8成 **/
	@Column(name = "ELFGUARFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String elfGuarFlag;

	/** 新作紓困註記 **/
	@Column(name = "ELFNEWRESCUE", length = 1, columnDefinition = "CHAR(1)")
	private String elfNewRescue;

    /** 新作紓困資料年月 **/
    @Column(name="ELFNEWRESCUEYM", length=6, columnDefinition="CHAR(6)")
    private String elfNewRescueYM;

    /**
     * J-109-0313 小規模覆審
     * 純小規模營業人(央行C方案) **/
    @Column(name = "ISSMALLBUSS", length = 1, columnDefinition = "CHAR(1)")
    private String isSmallBuss;

    /**
     * J-109-0313 小規模覆審
     * 銀行簡易評分表總分(該ID最低分)
     */
    @Digits(integer = 5, fraction = 0, groups = Check.class)
    @Column(name = "SBSCORE", columnDefinition = "DECIMAL(5,0)")
    private BigDecimal sbScore;

    /** 抽樣類別 **/
    @Column(name = "ELFRANDOMTYPE", length = 1, columnDefinition = "VARCHAR(1)")
    private String elfRandomType;

	/**
	 * J-110-0396
	 * 不覆審代碼
	 * <p/>
	 * ELF415_NREVIEW<br/>
	 * NewNCkdFlag = elfNCkdFlag有值(1,2,3) 或 elfNReview 有值
	 */
	@Column(name = "ELFNREVIEW", length = 2, columnDefinition = "CHAR(2)")
	private String elfNReview;

	/**
	 * J-110-0396
	 * 不覆審代碼年月
	 * ELF415_DATAYM
	 **/
	@Column(name="ELFNREVIEWYM", length=6, columnDefinition="CHAR(6)")
	private String elfNReviewYM;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得借款人ID
	 * <p/>
	 * ELF412_CUSTID
	 */
	public String getCustId() {
		return this.custId;
	}

	/**
	 * 設定借款人ID
	 * <p/>
	 * ELF412_CUSTID
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/**
	 * 取得借款人重覆序號
	 * <p/>
	 * ELF412_DUPNO
	 */
	public String getDupNo() {
		return this.dupNo;
	}

	/**
	 * 設定借款人重覆序號
	 * <p/>
	 * ELF412_DUPNO
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得區部別
	 * <p/>
	 * 1.DBU、4.OBU、5.海外<br/>
	 * 固定：5.海外
	 */
	public String getTypCd() {
		return this.typCd;
	}

	/**
	 * 設定區部別
	 * <p/>
	 * 1.DBU、4.OBU、5.海外<br/>
	 * 固定：5.海外
	 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/**
	 * 取得文件產生方式
	 * <p/>
	 * 系統產生 | SYS<br/>
	 * 人工產生 | PEO
	 */
	public String getCreateBY() {
		return this.createBY;
	}

	/**
	 * 設定文件產生方式
	 * <p/>
	 * 系統產生 | SYS<br/>
	 * 人工產生 | PEO
	 **/
	public void setCreateBY(String value) {
		this.createBY = value;
	}

	/**
	 * 取得分行覆審報告表新增註記
	 * <p/>
	 * Y/N<br/>
	 * gfnWrite170M01ToChkList()<br/>
	 * FLMS170M01執行【編製完後儲存並上傳覆審控制檔】時寫回
	 */
	public String getNewBy170M01() {
		return this.newBy170M01;
	}

	/**
	 * 設定分行覆審報告表新增註記
	 * <p/>
	 * Y/N<br/>
	 * gfnWrite170M01ToChkList()<br/>
	 * FLMS170M01執行【編製完後儲存並上傳覆審控制檔】時寫回
	 **/
	public void setNewBy170M01(String value) {
		this.newBy170M01 = value;
	}

	/**
	 * 取得文件狀態
	 * <p/>
	 * 要覆審 | 1<br/>
	 * 不覆審 | 2
	 */
	public String getDocStatus1() {
		return this.docStatus1;
	}

	/**
	 * 設定文件狀態
	 * <p/>
	 * 要覆審 | 1<br/>
	 * 不覆審 | 2
	 **/
	public void setDocStatus1(String value) {
		this.docStatus1 = value;
	}

	/**
	 * 取得覆審案號_序號
	 * <p/>
	 * ※預設值「null」，於執行「重新產生批號」時與「主檔_覆審批號」一起給號，依序編號。<br/>
	 * ※依序：<br/>
	 * 1.文件狀態：<br/>
	 * 「1.要覆審」案件依序全數編號完畢後，才開始續編「2.不覆審」案件。<br/>
	 * 2.DBU/OBU共管ID：<br/>
	 * 海外分行帳務獨立，無共管ID。<br/>
	 * 3.借款人ID：<br/>
	 * 依「借款人ID」、「借款人重覆序號」排序。<br/>
	 * 4.調整後上次覆審日/上次覆審日：<br/>
	 * 有「調整後上次覆審日」的依日期昇冪先排序，之後再依「上次覆審日」排序。<br/>
	 * 顯示格式：001
	 */
	public Integer getProjectSeq() {
		return this.projectSeq;
	}

	/**
	 * 設定覆審案號_序號
	 * <p/>
	 * ※預設值「null」，於執行「重新產生批號」時與「主檔_覆審批號」一起給號，依序編號。<br/>
	 * ※依序：<br/>
	 * 1.文件狀態：<br/>
	 * 「1.要覆審」案件依序全數編號完畢後，才開始續編「2.不覆審」案件。<br/>
	 * 2.DBU/OBU共管ID：<br/>
	 * 海外分行帳務獨立，無共管ID。<br/>
	 * 3.借款人ID：<br/>
	 * 依「借款人ID」、「借款人重覆序號」排序。<br/>
	 * 4.調整後上次覆審日/上次覆審日：<br/>
	 * 有「調整後上次覆審日」的依日期昇冪先排序，之後再依「上次覆審日」排序。<br/>
	 * 顯示格式：001
	 **/
	public void setProjectSeq(Integer value) {
		this.projectSeq = value;
	}

	/**
	 * 取得覆審案號
	 * <p/>
	 * 格式為：年度(YYYY) +分行簡稱(3碼)+(兆)+覆審字第+ 批號+ - + 序號 + 號，例：2011蘭雅(兆)覆審字第001-003號
	 */
	public String getProjectNo() {
		return this.projectNo;
	}

	/**
	 * 設定覆審案號
	 * <p/>
	 * 格式為：年度(YYYY) +分行簡稱(3碼)+(兆)+覆審字第+ 批號+ - + 序號 + 號，例：2011蘭雅(兆)覆審字第001-003號
	 **/
	public void setProjectNo(String value) {
		this.projectNo = value;
	}

	/**
	 * 取得借款人姓名
	 * <p/>
	 * ELF411_CUSTNAME
	 */
	public String getElfCName() {
		return this.elfCName;
	}

	/**
	 * 設定借款人姓名
	 * <p/>
	 * ELF411_CUSTNAME
	 **/
	public void setElfCName(String value) {
		this.elfCName = value;
	}

	/** 取得行業別 **/
	public String getECoNm() {
		return this.eCoNm;
	}

	/** 設定行業別 **/
	public void setECoNm(String value) {
		this.eCoNm = value;
	}

	/**
	 * 取得負責人
	 * <p/>
	 * 21個全型字
	 */
	public String getSup3CNm() {
		return this.sup3CNm;
	}

	/**
	 * 設定負責人
	 * <p/>
	 * 21個全型字
	 **/
	public void setSup3CNm(String value) {
		this.sup3CNm = value;
	}

	/**
	 * 取得連保人
	 * <p/>
	 * 100個全型字
	 */
	public String getRltGuarantor() {
		return this.rltGuarantor;
	}

	/**
	 * 設定連保人
	 * <p/>
	 * 100個全型字
	 **/
	public void setRltGuarantor(String value) {
		this.rltGuarantor = value;
	}

	/**
	 * 取得共同借款人
	 * <p/>
	 * 100個全型字
	 */
	public String getRltBorrower() {
		return this.rltBorrower;
	}

	/**
	 * 設定共同借款人
	 * <p/>
	 * 100個全型字
	 **/
	public void setRltBorrower(String value) {
		this.rltBorrower = value;
	}

	/**
	 * 取得是否為聯貸案
	 * <p/>
	 * Y/N
	 */
	public String getULoan() {
		return this.uLoan;
	}

	/**
	 * 設定是否為聯貸案
	 * <p/>
	 * Y/N
	 **/
	public void setULoan(String value) {
		this.uLoan = value;
	}

	/**
	 * 取得不覆審代碼 (人工調整)
	 * <p/>
	 * ※不覆審人工調整資訊<br/>
	 * NEW_NCKDFLAG<br/>
	 * 1.本行或同業主辦之聯貸案件，非擔任管理行。<br/>
	 * 2.十成定存。<br/>
	 * 3.純進出押戶。<br/>
	 * 4.對政府或政府所屬機關、學校之授信案件。<br/>
	 * 5.拆放同業或對同業之融通。<br/>
	 * 6.已列報為逾期放款或轉列催收款項之案件。<br/>
	 * 7.銷戶<br/>
	 * 8.本次暫不覆審<br/>
	 * 9.已專案核准免辦理覆審之房屋仲介價金履約保證案件。<br/>
	 * (名單確認後此欄位會更新中心覆審控制檔)
	 */
	public String getNewNCkdFlag() {
		return this.newNCkdFlag;
	}

	/**
	 * 設定不覆審代碼 (人工調整)
	 * <p/>
	 * ※不覆審人工調整資訊<br/>
	 * NEW_NCKDFLAG<br/>
	 * 1.本行或同業主辦之聯貸案件，非擔任管理行。<br/>
	 * 2.十成定存。<br/>
	 * 3.純進出押戶。<br/>
	 * 4.對政府或政府所屬機關、學校之授信案件。<br/>
	 * 5.拆放同業或對同業之融通。<br/>
	 * 6.已列報為逾期放款或轉列催收款項之案件。<br/>
	 * 7.銷戶<br/>
	 * 8.本次暫不覆審<br/>
	 * 9.已專案核准免辦理覆審之房屋仲介價金履約保證案件。<br/>
	 * (名單確認後此欄位會更新中心覆審控制檔)
	 **/
	public void setNewNCkdFlag(String value) {
		this.newNCkdFlag = value;
	}

	/**
	 * 取得不覆審備註 (人工調整)
	 * <p/>
	 * ※不覆審人工調整資訊<br/>
	 * NEW_NCKDMEMO<br/>
	 * 100個全型字<br/>
	 * (名單確認後此欄位會更新中心覆審控制檔)
	 */
	public String getNewNCkdMemo() {
		return this.newNCkdMemo;
	}

	/**
	 * 設定不覆審備註 (人工調整)
	 * <p/>
	 * ※不覆審人工調整資訊<br/>
	 * NEW_NCKDMEMO<br/>
	 * 100個全型字<br/>
	 * (名單確認後此欄位會更新中心覆審控制檔)
	 **/
	public void setNewNCkdMemo(String value) {
		this.newNCkdMemo = value;
	}

	/**
	 * 取得下次恢復覆審日期 (人工調整)
	 * <p/>
	 * ※不覆審人工調整資訊<br/>
	 * NEW_NEXTNWDT<br/>
	 * (名單確認後此欄位會更新中心覆審控制檔)
	 */
	public Date getNewNextNwDt() {
		return this.newNextNwDt;
	}

	/**
	 * 設定下次恢復覆審日期 (人工調整)
	 * <p/>
	 * ※不覆審人工調整資訊<br/>
	 * NEW_NEXTNWDT<br/>
	 * (名單確認後此欄位會更新中心覆審控制檔)
	 **/
	public void setNewNextNwDt(Date value) {
		this.newNextNwDt = value;
	}

	/**
	 * 取得調整後上次覆審日(人工調整)
	 * <p/>
	 * ※不覆審改要覆審人工調整資訊<br/>
	 * NEW_ELF412_LRDATE<br/>
	 * (名單確認後此欄位會更新中心覆審控制檔)
	 */
	public Date getNewLRDate() {
		return this.newLRDate;
	}

	/**
	 * 設定調整後上次覆審日(人工調整)
	 * <p/>
	 * ※不覆審改要覆審人工調整資訊<br/>
	 * NEW_ELF412_LRDATE<br/>
	 * (名單確認後此欄位會更新中心覆審控制檔)
	 **/
	public void setNewLRDate(Date value) {
		this.newLRDate = value;
	}

	/**
	 * 取得資料日期
	 * <p/>
	 * ※每月月初前置作業更新??<br/>
	 * ELF412_DATADT
	 */
	public Date getElfDataDt() {
		return this.elfDataDt;
	}

	/**
	 * 設定資料日期
	 * <p/>
	 * ※每月月初前置作業更新??<br/>
	 * ELF412_DATADT
	 **/
	public void setElfDataDt(Date value) {
		this.elfDataDt = value;
	}

	/**
	 * 取得主要授信戶
	 * <p/>
	 * ELF412_MAINCUST<br/>
	 * 是 | Y<br/>
	 * 否 | N<br/>
	 * 1.新戶增額由ELF411<br/>
	 * 2.舊案由STORE PROCEDURE
	 */
	public String getElfMainCust() {
		return this.elfMainCust;
	}

	/**
	 * 設定主要授信戶
	 * <p/>
	 * ELF412_MAINCUST<br/>
	 * 是 | Y<br/>
	 * 否 | N<br/>
	 * 1.新戶增額由ELF411<br/>
	 * 2.舊案由STORE PROCEDURE
	 **/
	public void setElfMainCust(String value) {
		this.elfMainCust = value;
	}

	/**
	 * 取得資信評等類別
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_CRDTYPE<br/>
	 * B=DBU、大型企業、L=DBU中小型企業、O=OBU
	 */
	public String getElfCrdType() {
		return this.elfCrdType;
	}

	/**
	 * 設定資信評等類別
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_CRDTYPE<br/>
	 * B=DBU、大型企業、L=DBU中小型企業、O=OBU
	 **/
	public void setElfCrdType(String value) {
		this.elfCrdType = value;
	}

	/**
	 * 取得資信評等
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_CRDTTBL
	 */
	public String getElfCrdTTbl() {
		return this.elfCrdTTbl;
	}

	/**
	 * 設定資信評等
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_CRDTTBL
	 **/
	public void setElfCrdTTbl(String value) {
		this.elfCrdTTbl = value;
	}

	/**
	 * 取得信用模型評等類別
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_MOWTYPE<br/>
	 * DBU大型企業 | 1<br/>
	 * DBU中型企業 | 2<br/>
	 * DBU中小型企業|3<br/>
	 * DBU不動產有建案規劃 | 4<br/>
	 * DBU專案融資 | 5<br/>
	 * DBU本國證券公司 | 6<br/>
	 * DBU投資公司一般情況 | 8<br/>
	 * DBU租賃公司 | 9<br/>
	 * DBU一案建商 | A<br/>
	 * DBU非一案建商(擔保/土融) | B<br/>
	 * DBU非一案建商(無擔) | C<br/>
	 * 投資公司情況一 | D<br/>
	 * 投資公司情況二 | E<br/>
	 * OBU境外船舶/航空器 | F
	 */
	public String getElfMowType() {
		return this.elfMowType;
	}

	/**
	 * 設定信用模型評等類別
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_MOWTYPE<br/>
	 * DBU大型企業 | 1<br/>
	 * DBU中型企業 | 2<br/>
	 * DBU中小型企業|3<br/>
	 * DBU不動產有建案規劃 | 4<br/>
	 * DBU專案融資 | 5<br/>
	 * DBU本國證券公司 | 6<br/>
	 * DBU投資公司一般情況 | 8<br/>
	 * DBU租賃公司 | 9<br/>
	 * DBU一案建商 | A<br/>
	 * DBU非一案建商(擔保/土融) | B<br/>
	 * DBU非一案建商(無擔) | C<br/>
	 * 投資公司情況一 | D<br/>
	 * 投資公司情況二 | E<br/>
	 * OBU境外船舶/航空器 | F
	 **/
	public void setElfMowType(String value) {
		this.elfMowType = value;
	}

	/**
	 * 取得信用模型評等
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_MOWTBL1
	 */
	public String getElfMowTbl1() {
		return this.elfMowTbl1;
	}

	/**
	 * 設定信用模型評等
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_MOWTBL1
	 **/
	public void setElfMowTbl1(String value) {
		this.elfMowTbl1 = value;
	}

	/**
	 * 取得上上次覆審日
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_LLRDATE
	 */
	public Date getElfLLRDate() {
		return this.elfLLRDate;
	}

	/**
	 * 設定上上次覆審日
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_LLRDATE
	 **/
	public void setElfLLRDate(Date value) {
		this.elfLLRDate = value;
	}

	/**
	 * 取得上次覆審日
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_LRDATE
	 */
	public Date getElfLRDate() {
		return this.elfLRDate;
	}

	/**
	 * 設定上次覆審日
	 * <p/>
	 * ※每月月初前置作業更新<br/>
	 * ELF412_LRDATE
	 **/
	public void setElfLRDate(Date value) {
		this.elfLRDate = value;
	}

	/**
	 * 取得覆審週期
	 * <p/>
	 * ELF412_RCKDLINE<br/>
	 * 覆審週期說明：<br/>
	 * A：一年覆審一次。<br/>
	 * B：半年覆審一次(主要授信戶並符合信評條件)。<br/>
	 * C：新戶/增額戶。<br/>
	 * D：異常戶已三個月覆審過- 爾後半年覆審一次。<br/>
	 * E：首次通報之異常戶。（必需在首次通報日後3月內覆審）。<br/>
	 * F：會計師出具保留意見已三個月覆審過- 爾後半年覆審一次。<br/>
	 * G：首次通報有會計師出具保留意見之異常戶。（必需在首次通報日後3月內覆審）。<br/>
	 * H：主管機關指定覆審案件。
	 */
	public String getElfRCkdLine() {
		return this.elfRCkdLine;
	}

	/**
	 * 設定覆審週期
	 * <p/>
	 * ELF412_RCKDLINE<br/>
	 * 覆審週期說明：<br/>
	 * A：一年覆審一次。<br/>
	 * B：半年覆審一次(主要授信戶並符合信評條件)。<br/>
	 * C：新戶/增額戶。<br/>
	 * D：異常戶已三個月覆審過- 爾後半年覆審一次。<br/>
	 * E：首次通報之異常戶。（必需在首次通報日後3月內覆審）。<br/>
	 * F：會計師出具保留意見已三個月覆審過- 爾後半年覆審一次。<br/>
	 * G：首次通報有會計師出具保留意見之異常戶。（必需在首次通報日後3月內覆審）。<br/>
	 * H：主管機關指定覆審案件。
	 **/
	public void setElfRCkdLine(String value) {
		this.elfRCkdLine = value;
	}

	/**
	 * 取得上次電腦計算週期 (原始週期)
	 * <p/>
	 * ELF412_OCKDLINE<br/>
	 * ※項目同「覆審週期」<br/>
	 * ※如果沒有異常通報、增額、主管機關指定時之週期，只會為A或B
	 */
	public String getElfOCkdLine() {
		return this.elfOCkdLine;
	}

	/**
	 * 設定上次電腦計算週期 (原始週期)
	 * <p/>
	 * ELF412_OCKDLINE<br/>
	 * ※項目同「覆審週期」<br/>
	 * ※如果沒有異常通報、增額、主管機關指定時之週期，只會為A或B
	 **/
	public void setElfOCkdLine(String value) {
		this.elfOCkdLine = value;
	}

	/**
	 * 取得主管機關指定覆審案件
	 * <p/>
	 * ELF412_UCKDLINE<br/>
	 * 是 | Y<br/>
	 * 否 | N
	 */
	public String getElfUCkdLINE() {
		return this.elfUCkdLINE;
	}

	/**
	 * 設定主管機關指定覆審案件
	 * <p/>
	 * ELF412_UCKDLINE<br/>
	 * 是 | Y<br/>
	 * 否 | N
	 **/
	public void setElfUCkdLINE(String value) {
		this.elfUCkdLINE = value;
	}

	/**
	 * 取得主管機關通知日期
	 * <p/>
	 * ELF412_UCKDDT
	 */
	public Date getElfUCkdDt() {
		return this.elfUCkdDt;
	}

	/**
	 * 設定主管機關通知日期
	 * <p/>
	 * ELF412_UCKDDT
	 **/
	public void setElfUCkdDt(Date value) {
		this.elfUCkdDt = value;
	}

	/**
	 * 取得戶況
	 * <p/>
	 * ELF412_CSTATE<br/>
	 * 無餘額 | 0<br/>
	 * 正常 | 1<br/>
	 * 逾期 | 2<br/>
	 * 催收 | 3<br/>
	 * 呆帳 | 4<br/>
	 * (該戶項下最嚴重的代碼)
	 */
	public String getElfCState() {
		return this.elfCState;
	}

	/**
	 * 設定戶況
	 * <p/>
	 * ELF412_CSTATE<br/>
	 * 無餘額 | 0<br/>
	 * 正常 | 1<br/>
	 * 逾期 | 2<br/>
	 * 催收 | 3<br/>
	 * 呆帳 | 4<br/>
	 * (該戶項下最嚴重的代碼)
	 **/
	public void setElfCState(String value) {
		this.elfCState = value;
	}

	/**
	 * 取得銷戶日
	 * <p/>
	 * ELF412_CANCELDT<br/>
	 * 全部都沒有額度的日期
	 */
	public Date getElfCancelDt() {
		return this.elfCancelDt;
	}

	/**
	 * 設定銷戶日
	 * <p/>
	 * ELF412_CANCELDT<br/>
	 * 全部都沒有額度的日期
	 **/
	public void setElfCancelDt(Date value) {
		this.elfCancelDt = value;
	}

	/**
	 * 取得異常通報代碼
	 * <p/>
	 * ELF412_MDFLAG<br/>
	 * (LN.LNFE0851)<br/>
	 * 停止營業/工廠停工<br/>
	 * 公司、負責人及保證人退票<br/>
	 * 公司聲請重整<br/>
	 * 申請紓困、協議清償、展延還款<br/>
	 * 本金、利息逾期或財務週轉困難<br/>
	 * 負責人、保證人等涉嫌違反法令<br/>
	 * 客戶大筆應收帳款未收現或商糾<br/>
	 * 發生火災、技術授權金發生爭議等重大事件<br/>
	 * 其他足以影響正常營運之事件<br/>
	 * 會計師簽發保留意見（足以影響借戶正常營運、財務狀況者）、否定意見或無法表示意見者
	 */
	public String getElfMDFlag() {
		return this.elfMDFlag;
	}

	/**
	 * 設定異常通報代碼
	 * <p/>
	 * ELF412_MDFLAG<br/>
	 * (LN.LNFE0851)<br/>
	 * 停止營業/工廠停工<br/>
	 * 公司、負責人及保證人退票<br/>
	 * 公司聲請重整<br/>
	 * 申請紓困、協議清償、展延還款<br/>
	 * 本金、利息逾期或財務週轉困難<br/>
	 * 負責人、保證人等涉嫌違反法令<br/>
	 * 客戶大筆應收帳款未收現或商糾<br/>
	 * 發生火災、技術授權金發生爭議等重大事件<br/>
	 * 其他足以影響正常營運之事件<br/>
	 * 會計師簽發保留意見（足以影響借戶正常營運、財務狀況者）、否定意見或無法表示意見者
	 **/
	public void setElfMDFlag(String value) {
		this.elfMDFlag = value;
	}

	/**
	 * 取得異常通報日期
	 * <p/>
	 * ELF412_MDDT<br/>
	 * (LN.LNFE0851)
	 */
	public Date getElfMDDt() {
		return this.elfMDDt;
	}

	/**
	 * 設定異常通報日期
	 * <p/>
	 * ELF412_MDDT<br/>
	 * (LN.LNFE0851)
	 **/
	public void setElfMDDt(Date value) {
		this.elfMDDt = value;
	}

	/**
	 * 取得異常通報情形
	 * <p/>
	 * ELF412_PROCESS<br/>
	 * (LN.LNFE0851)<br/>
	 * 100個全型字
	 */
	public String getElfProcess() {
		return this.elfProcess;
	}

	/**
	 * 設定異常通報情形
	 * <p/>
	 * ELF412_PROCESS<br/>
	 * (LN.LNFE0851)<br/>
	 * 100個全型字
	 **/
	public void setElfProcess(String value) {
		this.elfProcess = value;
	}

	/**
	 * 取得新作/增額/逾放轉正
	 * <p/>
	 * ELF412_NEWADD<br/>
	 * 新作 | N<br/>
	 * 增額 | C<br/>
	 * 逾放轉正 | R
	 */
	public String getElfNewAdd() {
		return this.elfNewAdd;
	}

	/**
	 * 設定新作/增額/逾放轉正
	 * <p/>
	 * ELF412_NEWADD<br/>
	 * 新作 | N<br/>
	 * 增額 | C<br/>
	 * 逾放轉正 | R
	 **/
	public void setElfNewAdd(String value) {
		this.elfNewAdd = value;
	}

	/**
	 * 取得新作/增額/逾放轉正年月
	 * <p/>
	 * ELF412_NEWDATE<br/>
	 * ※ELF411_DATAYM之資料
	 */
	public String getElfNewDate() {
		return this.elfNewDate;
	}

	/**
	 * 設定新作/增額/逾放轉正年月
	 * <p/>
	 * ELF412_NEWDATE<br/>
	 * ※ELF411_DATAYM之資料
	 **/
	public void setElfNewDate(String value) {
		this.elfNewDate = value;
	}

	/**
	 * 取得不覆審代碼
	 * <p/>
	 * ELF412_NCKDFLAG<br/>
	 * ※項目同「不覆審代碼(人工調整)」
	 */
	public String getElfNCkdFlag() {
		return this.elfNCkdFlag;
	}

	/**
	 * 設定不覆審代碼
	 * <p/>
	 * ELF412_NCKDFLAG<br/>
	 * ※項目同「不覆審代碼(人工調整)」
	 **/
	public void setElfNCkdFlag(String value) {
		this.elfNCkdFlag = value;
	}

	/**
	 * 取得不覆審日期
	 * <p/>
	 * ELF412_NCKDDATE
	 */
	public Date getElfNCkdDate() {
		return this.elfNCkdDate;
	}

	/**
	 * 設定不覆審日期
	 * <p/>
	 * ELF412_NCKDDATE
	 **/
	public void setElfNCkdDate(Date value) {
		this.elfNCkdDate = value;
	}

	/**
	 * 取得不覆審備註
	 * <p/>
	 * ELF412_NCKDMEMO<br/>
	 * 100個全型字
	 */
	public String getElfNCkdMemo() {
		return this.elfNCkdMemo;
	}

	/**
	 * 設定不覆審備註
	 * <p/>
	 * ELF412_NCKDMEMO<br/>
	 * 100個全型字
	 **/
	public void setElfNCkdMemo(String value) {
		this.elfNCkdMemo = value;
	}

	/**
	 * 取得下次恢復覆審日期
	 * <p/>
	 * ELF412_NEXTNWDT
	 */
	public Date getElfNextNwDt() {
		return this.elfNextNwDt;
	}

	/**
	 * 設定下次恢復覆審日期
	 * <p/>
	 * ELF412_NEXTNWDT
	 **/
	public void setElfNextNwDt(Date value) {
		this.elfNextNwDt = value;
	}

	/**
	 * 取得DBUOBU是否有共管
	 * <p/>
	 * ELF412_DBUOBU<br/>
	 * 是 | Y<br/>
	 * 否 | N<br/>
	 * 海外目前無使用<br/>
	 * 固定：否 | N
	 */
	public String getElfDBUOBU() {
		return this.elfDBUOBU;
	}

	/**
	 * 設定DBUOBU是否有共管
	 * <p/>
	 * ELF412_DBUOBU<br/>
	 * 是 | Y<br/>
	 * 否 | N<br/>
	 * 海外目前無使用<br/>
	 * 固定：否 | N
	 **/
	public void setElfDBUOBU(String value) {
		this.elfDBUOBU = value;
	}

	/**
	 * 取得人工維護日
	 * <p/>
	 * ELF412_UPDDATE
	 */
	public Date getElfUpdDate() {
		return this.elfUpdDate;
	}

	/**
	 * 設定人工維護日
	 * <p/>
	 * ELF412_UPDDATE
	 **/
	public void setElfUpdDate(Date value) {
		this.elfUpdDate = value;
	}

	/**
	 * 取得人工調整ID
	 * <p/>
	 * ELF412_UPDATER
	 */
	public String getElfUpdater() {
		return this.elfUpdater;
	}

	/**
	 * 設定人工調整ID
	 * <p/>
	 * ELF412_UPDATER
	 **/
	public void setElfUpdater(String value) {
		this.elfUpdater = value;
	}

	/**
	 * 取得其他備註
	 * <p/>
	 * ELF412_MEMO<br/>
	 * 100個全型字
	 */
	public String getElfMemo() {
		return this.elfMemo;
	}

	/**
	 * 設定其他備註
	 * <p/>
	 * ELF412_MEMO<br/>
	 * 100個全型字
	 **/
	public void setElfMemo(String value) {
		this.elfMemo = value;
	}

	/**
	 * 取得資料更新日
	 * <p/>
	 * ELF412_TMESTAMP
	 */
	public Date getElfTmeStamp() {
		return this.elfTmeStamp;
	}

	/**
	 * 設定資料更新日
	 * <p/>
	 * ELF412_TMESTAMP
	 **/
	public void setElfTmeStamp(Date value) {
		this.elfTmeStamp = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	public Set<L180M01D> getL180m01ds() {
		return l180m01ds;
	}

	public void setL180m01ds(Set<L180M01D> l180m01ds) {
		this.l180m01ds = l180m01ds;
	}

	/** 取得共用戶ID代碼，(用於排序) **/
	public String getCoMainId() {
		return coMainId;
	}

	/** 設定共用戶ID代碼，(用於排序) **/
	public void setCoMainId(String coMainId) {
		this.coMainId = coMainId;
	}

	/** 取得外部評等類別 **/
	public String getElfFcrdType() {
		return elfFcrdType;
	}

	/** 設定外部評等類別 **/
	public void setElfFcrdType(String elfFcrdType) {
		this.elfFcrdType = elfFcrdType;
	}

	/** 取得外部評等地區別 **/
	public String getElfFcrdArea() {
		return elfFcrdArea;
	}

	/** 設定外部評等地區別 **/
	public void setElfFcrdArea(String elfFcrdArea) {
		this.elfFcrdArea = elfFcrdArea;
	}

	/** 取得外部評等期間別 **/
	public String getElfFcrdPred() {
		return elfFcrdPred;
	}

	/** 設定外部評等期間別 **/
	public void setElfFcrdPred(String elfFcrdPred) {
		this.elfFcrdPred = elfFcrdPred;
	}

	/** 取得外部評等等級 **/
	public String getElfFcrdGrad() {
		return elfFcrdGrad;
	}

	/** 設定外部評等等級 **/
	public void setElfFcrdGrad(String elfFcrdGrad) {
		this.elfFcrdGrad = elfFcrdGrad;
	}

	public String getBusCd() {
		return busCd;
	}

	public void setBusCd(String s) {
		this.busCd = s;
	}

	public String getBussKind() {
		return bussKind;
	}

	public void setBussKind(String s) {
		this.bussKind = s;
	}

	public String getECoNm07A() {
		return eCoNm07A;
	}

	public void setECoNm07A(String s) {
		this.eCoNm07A = s;
	}

	/** 設定是否為實地覆審 **/
	public void setElfRealCkFg(String elfRealCkFg) {
		this.elfRealCkFg = elfRealCkFg;
	}

	/** 取得是否為實地覆審 **/
	public String getElfRealCkFg() {
		return elfRealCkFg;
	}

	/** 設定最近一次實地覆審時間 **/
	public void setElfRealDt(Date elfRealDt) {
		this.elfRealDt = elfRealDt;
	}

	/** 取得最近一次實地覆審時間 **/
	public Date getElfRealDt() {
		return elfRealDt;
	}

	/** 設定覆審名單類別 **/
	public void setCtlType(String ctlType) {
		this.ctlType = ctlType;
	}

	/** 取得覆審名單類別 **/
	public String getCtlType() {
		return ctlType;
	}

	/** 設定舊簽報書MAINID **/
	public void setOldRptId(String oldRptId) {
		this.oldRptId = oldRptId;
	}

	/** 取得舊簽報書MAINID **/
	public String getOldRptId() {
		return oldRptId;
	}

	/** 設定舊簽報書核准日期 **/
	public void setOldRptDt(Date oldRptDt) {
		this.oldRptDt = oldRptDt;
	}

	/** 取得舊簽報書核准日期 **/
	public Date getOldRptDt() {
		return oldRptDt;
	}

	/** 設定新簽報書MAINID **/
	public void setNewRptId(String newRptId) {
		this.newRptId = newRptId;
	}

	/** 取得新簽報書MAINID **/
	public String getNewRptId() {
		return newRptId;
	}

	/** 設定新簽報書核准日期 **/
	public void setNewRptDt(Date newRptDt) {
		this.newRptDt = newRptDt;
	}

	/** 取得新簽報書核准日期 **/
	public Date getNewRptDt() {
		return newRptDt;
	}

	/** 設定首次往來之新授信戶(下稱純新貸戶) **/
	public void setElfIsAllNew(String elfIsAllNew) {
		this.elfIsAllNew = elfIsAllNew;
	}

	/** 取得首次往來之新授信戶(下稱純新貸戶) **/
	public String getElfIsAllNew() {
		return elfIsAllNew;
	}

    /** 設定純紓困戶 **/
    public void setElfIsRescue(String elfIsRescue) {
        this.elfIsRescue = elfIsRescue;
    }

    /** 取得純紓困戶 **/
    public String getElfIsRescue() {
        return elfIsRescue;
    }

	/** 設定信保擔保註記(2020/04 設定為8成) **/
	public void setElfGuarFlag(String elfGuarFlag) {
		this.elfGuarFlag = elfGuarFlag;
	}

	/** 取得信保擔保註記(2020/04 設定為8成) **/
	public String getElfGuarFlag() {
		return elfGuarFlag;
	}

	/** 設定新作紓困註記 **/
	public void setElfNewRescue(String elfNewRescue) {
		this.elfNewRescue = elfNewRescue;
	}

	/** 取得新作紓困註記 **/
	public String getElfNewRescue() {
		return elfNewRescue;
	}

    /**
     * 設定新作紓困資料年月
     */
    public void setElfNewRescueYM(String elfNewRescueYM) {
        this.elfNewRescueYM = elfNewRescueYM;
    }

    /**
     * 取得新作紓困資料年月
     */
    public String getElfNewRescueYM() {
        return elfNewRescueYM;
    }

    /**
     * J-109-0313 小規模覆審
     * 設定純小規模營業人(央行C方案)
     */
    public void setIsSmallBuss(String isSmallBuss) {
        this.isSmallBuss = isSmallBuss;
    }

    /**
     * J-109-0313 小規模覆審
     * 取得純小規模營業人(央行C方案)
     */
    public String getIsSmallBuss() {
        return isSmallBuss;
    }

	/**
	 * J-109-0313 小規模覆審
	 * 設定銀行簡易評分表總分(該ID最低分)
	 */
	public void setSbScore(BigDecimal sbScore) {
		this.sbScore = sbScore;
	}

	/**
	 * J-109-0313 小規模覆審
	 * 取得銀行簡易評分表總分(該ID最低分)
	 */
	public BigDecimal getSbScore() {
		return sbScore;
	}

    /**
     * J-110-0272
     * 設定抽樣類別
     */
    public void setElfRandomType(String elfRandomType) {
        this.elfRandomType = elfRandomType;
    }

    /**
     * J-110-0272
     * 取得抽樣類別
     */
    public String getElfRandomType() {
        return elfRandomType;
    }

	/**
	 * J-110-0396
	 * 取得不覆審代碼
	 * <p/>
	 * ELF415_NREVIEW<br/>
	 * NewNCkdFlag = elfNCkdFlag有值(1,2,3) 或 elfNReview 有值
	 */
	public String getElfNReview() {
		return this.elfNReview;
	}

	/**
	 * J-110-0396
	 * 設定不覆審代碼
	 * <p/>
	 * ELF415_NREVIEW<br/>
	 * NewNCkdFlag = elfNCkdFlag有值(1,2,3) 或 elfNReview 有值
	 **/
	public void setElfNReview(String value) {
		this.elfNReview = value;
	}

	/**
	 * J-110-0396
	 * 設定不覆審代碼年月
	 * ELF415_DATAYM
	 */
	public void setElfNReviewYM(String elfNReviewYM) {
		this.elfNReviewYM = elfNReviewYM;
	}

	/**
	 * J-110-0396
	 * 取得不覆審代碼年月
	 * ELF415_DATAYM
	 */
	public String getElfNReviewYM() {
		return elfNReviewYM;
	}
}
