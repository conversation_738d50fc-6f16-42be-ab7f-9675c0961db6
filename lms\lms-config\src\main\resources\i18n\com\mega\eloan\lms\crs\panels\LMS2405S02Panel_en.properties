comment1=A total of
comment2=reviews are required
comment3=Credit Review Type 8-1 [
comment4=]; the total number of accounts is:
comment5=; random samples made on (
comment6=records) or
comment7=\uff05. (The number of valid accounts is:
comment8=)
comment10=Generate Data
comment11=(The size of this sample is:
comment12=Intended Sample Size

C240M01A.excelFile=\uff20Credit Review Worksheet - EXCEL File:
C240M01A.branchFile=\uff20Scheduled Credit Review Listing - EXCEL File:
C240M01A.chkExcelFile=\uff20Credit Review Validation File:

detailTitle=Credit Review Customer List
buttonNew=Add Review Customer
noCTL=Delete (Remark) Non-review Customers
reCTL=Reverse Non-review Customer
print=Tick Data To Print
number=Rearrange Credit Review Serial Number
chooseNckdflag=Please specify a reason code for waiving credit review

detail.tabs02=Credit Underwriting/Review Information
C241M01a.ownBrId=Branch name
C241M01a.staff=Branch Staff
C241M01a.custId=Borrower

detail.tabs03=File Attachment