<?xml version="1.0" encoding="UTF-8"?>

<schema xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="/jcs-process"
	xmlns:tns="/jcs-process" elementFormDefault="qualified">
	<annotation>
		<documentation>
			A xml schema for jcs-process.(referenced jPDL 4)
		</documentation>
	</annotation>

	<element name="process">
		<complexType>
			<sequence>
				<element name="start" type="tns:node" minOccurs="1" maxOccurs="1" />
				<element name="end" type="tns:node" minOccurs="1" maxOccurs="1" />
				<element name="state" type="tns:node" />
				<element name="task" type="tns:node" />
				<element name="fork" type="tns:node" />
				<element name="join" type="tns:node" />
				<element name="script" type="tns:node" />
				<element name="decision" type="tns:decision" />
				<element name="sub-process" type="tns:sub-process">
				</element>
			</sequence>
			<attribute name="name" type="string" use="required" />
			<attribute name="handler" type="string" />
		</complexType>
	</element>

	<complexType name="node" mixed="true">
		<sequence minOccurs="1">
			<element name="transition" type="tns:transition" />
		</sequence>
		<attribute name="name" use="required" type="string" />
		<attribute name="g" type="string" />
	</complexType>

	<complexType name="transition">
		<attribute name="name" type="string" />
		<attribute name="to" type="string" use="required" />
		<attribute name="g" type="string" />
		<attribute name="default" type="string" />
	</complexType>

	<complexType name="sub-process">
		<complexContent>
			<extension base="tns:node">
				<attribute name="sub-process-id" type="string" use="required" />
			</extension>
		</complexContent>
	</complexType>

	<complexType name="decision">
		<complexContent>
			<extension base="tns:node">
				<attribute name="expr" type="string" use="required" />
			</extension>
		</complexContent>
	</complexType>

</schema>
