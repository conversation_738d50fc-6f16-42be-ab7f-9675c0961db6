package com.mega.eloan.lms.cls.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.cls.panels.CLS1161S41Panel;
import com.mega.eloan.lms.cls.panels.CLS1161S42Panel;
import com.mega.eloan.lms.cls.panels.CLS1161S43Panel;
import com.mega.eloan.lms.cls.service.CLS1161Service;
import com.mega.eloan.lms.model.L250M01A;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * 消金模擬動審
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/cls/cls1161m04/{page}")
public class CLS1161M04Page extends AbstractEloanForm {

	@Autowired
	CLS1161Service cls1161Service;
	
	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params,
				getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));

		addAclLabel(model,
				new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
						AuthType.Accept, false, CreditDocStatusEnum.海外_待覆核));
		renderJsI18N(CLS1161M04Page.class);
		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page,params);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
		model.addAttribute("show_ivr_panel_visible",
				cls1161Service.getProjClassFromL250M01A(
						params.getString(EloanConstants.MAIN_OID)));
	}// ;

	// 頁籤
	@SuppressWarnings("unused")
	public Panel getPanel(int index, PageParameters params) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new CLS1161S41Panel(TAB_CTX, true);
			break;
		case 2:
			panel = new CLS1161S42Panel(TAB_CTX, true);
			break;
		case 3:
			panel = new CLS1161S43Panel(TAB_CTX, true);
			break;
		default:
			panel = new CLS1161S41Panel(TAB_CTX, true);
			break;
		}
		if (panel == null) {
			panel = new Panel(TAB_CTX);
		}

		return panel;
	}

	public Class<? extends Meta> getDomainClass() {
		return L250M01A.class;
	}

}
