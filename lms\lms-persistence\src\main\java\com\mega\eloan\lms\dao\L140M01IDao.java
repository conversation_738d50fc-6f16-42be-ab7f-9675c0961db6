/* 
 * L140M01IDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01I;

/** 連保人資料檔 **/
public interface L140M01IDao extends IGenericDao<L140M01I> {

	L140M01I findByOid(String oid);

	/**
	 * 用oid 陣列 找出所有連保人
	 * 
	 * @param oids
	 *            文件編號
	 * @return 連保人list
	 */
	List<L140M01I> findByOids(String[] oids);

//	List<L140M01I> findByMainId(String mainId);

//	L140M01I findByUniqueKey(String mainId, String type, String rId,
//			String rDupNo);

//	List<L140M01I> findByIndex01(String mainId, String type, String rId,
//			String rDupNo);

	/**
	 * 找出這個簽報書底下的額度明細表的連保人
	 * 
	 * @param caseMainId
	 *            簽報書　mainId
	 * @param itemType
	 * @return 連保人
	 */
	// List<L140M01I> findL140m01iListByL120m01cMainId(String caseMainId,
	// String itemType);

	/**
	 * J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	 */
	public List<L140M01I> findByMainIdWithRType(String mainId, String rType);

	/**
	 * J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	 * 
	 * @param mainId
	 * @param type
	 * @param rId
	 * @param rDupNo
	 * @param rType
	 * @return
	 */
	public L140M01I findByUniqueKeyWithRType(String mainId, String type,
			String rId, String rDupNo, String rType);

	/**
	 * J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	 * 
	 * @param mainId
	 * @param type
	 * @param rId
	 * @param rDupNo
	 * @param rType
	 * @return
	 */
	public List<L140M01I> findByIndex01WithRType(String mainId, String type,
			String rId, String rDupNo, String rType);

	/**
	 * J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	 * 
	 * @param caseMainId
	 * @param itemType
	 * @param rType
	 * @return
	 */
	public List<L140M01I> findL140m01iListByL120m01cMainIdWithRType(
			String caseMainId, String itemType, String rType);

	List<L140M01I> findByMainIdWithAllRType(String mainId);

}