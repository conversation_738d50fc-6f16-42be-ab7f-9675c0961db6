/* 
 * L120S05EDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S05EDao;
import com.mega.eloan.lms.model.L120S05E;

/** 借款人集團相關資料檔 **/
@Repository
public class L120S05EDaoImpl extends LMSJpaDao<L120S05E, String> implements
		L120S05EDao {

	@Override
	public L120S05E findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S05E> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S05E> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L120S05E findByUniqueKey(String mainId, String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L120S05E> findByIndex01(String mainId, String custId,
			String dupNo) {
		ISearch search = createSearchTemplete();
		List<L120S05E> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findMaxCaseSeq(String mainId, String custId,
			String dupNo) {
		Query query = getEntityManager().createNamedQuery(
				"L120S05F.getGpcomamt");
		query.setParameter("MAINID", mainId); // 設置參數
		query.setParameter("CUSTID", custId); // 設置參數
		query.setParameter("DUPNO", dupNo); // 設置參數
		return query.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findTotAmtBSeq(String mainId, String custId,
			String dupNo) {
		Query query = getEntityManager()
				.createNamedQuery("L120S05F.getTotAmtB");
		query.setParameter("MAINID", mainId); // 設置參數
		query.setParameter("CUSTID", custId); // 設置參數
		query.setParameter("DUPNO", dupNo); // 設置參數
		return query.getResultList();
	}

	@Override
	public int delModel(String mainId, String custId, String dupNo) {
		Query query = getEntityManager().createNamedQuery("L120S05E.delModel");
		query.setParameter("MAINID", mainId); // 設置參數
		query.setParameter("CUSTID", custId); // 設置參數
		query.setParameter("DUPNO", dupNo); // 設置參數
		return query.executeUpdate();
	}

}