package com.mega.eloan.lms.lms.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 消金信用評等模型
 * </pre>
 * 
 * @since 2017/2/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/2/1,EL08034,new
 *          </ul>
 */
public class LMS1035S02Panel extends Panel {
	private static final long serialVersionUID = 1L;

	public LMS1035S02Panel(String id) {
		super(id);		
	}

	public LMS1035S02Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}
	
	@Override
	protected String getViewName() {
		return getEloanPagePathByClass(getClass());
	}
}
