/* 
 * LMS2105S04Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 修改資料特殊流程 - 變更條件後
 * </pre>
 * 
 * @since 2012/01/10
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/01/10,REX,new
 *          </ul>
 */
public class LMS2105S04Panel extends Panel {

	public LMS2105S04Panel(String id) {
		super(id);

	}

	public LMS2105S04Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	private static final long serialVersionUID = 1L;

}
