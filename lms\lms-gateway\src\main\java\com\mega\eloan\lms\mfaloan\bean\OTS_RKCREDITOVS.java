package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 信用評分評等紀錄 **/
public class OTS_RKCREDITOVS extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Column(name="BR_CD", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String br_cd;

	/** NOTES文件編號 **/
	@Column(name="NOTEID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String noteid;

	/** 
	 * 評等日期<p/>
	 * 最終評等日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="RATING_DATE", columnDefinition="DATE", nullable=false,unique = true)
	private Date rating_date;

	/** 評等文件編號 **/
	@Column(name="RATING_ID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String rating_id;

	/** 客戶統一編號 **/
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String custid;

	/** 重複序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String dupno;

	/** 主借款人統一編號 **/
	@Column(name="CUST_KEY", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String cust_key;

	/** 
	 * 授信科目<p/>
	 * 3-4碼授信科目
	 */
	@Column(name="LOAN_CODE", length=4, columnDefinition="VARCHAR(4)", nullable=false,unique = true)
	private String loan_code;

	/** 
	 * 評等模型類別(c121m01a.mowType)
	 */
	@Column(name="MOWTYPE", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String mowtype;

	/** 
	 * 房貸/非房貸註記(l141m01c.modelType)
	 * N=非房貸、M=房貸
	 */
	@Column(name="MOWTYPE2", length=1, columnDefinition="CHAR(1)")
	private String mowtype2;
	
	/** 
	 * 採用模型註記
	 * 國別碼(l120m01a.ratingFlag)
	 */
	@Column(name="MOWTYPE_COUNTRY", length=2, columnDefinition="CHAR(2)", nullable=false,unique = true)
	private String mowtype_country;
	
	/** 模型版本-大版 **/
	@Column(name="MOWVER1", columnDefinition="DEC(5,0)", nullable=false,unique = true)
	private Integer mowver1;

	/** 模型版本-小版 **/
	@Column(name="MOWVER2", columnDefinition="DEC(5,0)", nullable=false,unique = true)
	private Integer mowver2;

	/** 
	 * 科目<p/>
	 * 8碼會計科目
	 */
	@Column(name="SUBJCODE", length=8, columnDefinition="VARCHAR(8)")
	private String subjcode;

	/** 授信期間(年) **/
	@Column(name="LOAN_PERIOD", columnDefinition="DEC(15,5)")
	private BigDecimal loan_period;

	/** 相關身分 **/
	@Column(name="LNGEFLAG", length=1, columnDefinition="CHAR(1)")
	private String lngeflag;

	/** 文件狀態 **/
	@Column(name="DOCSTATUS", length=2, columnDefinition="CHAR(2)")
	private String docstatus;

	/** 本件額度序號 **/
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrno;

	/** 初始評等 **/
	@Column(name="PR", columnDefinition="DEC(2,0)")
	private Integer pr;

	/** 獨立評等 **/
	@Column(name="SR", columnDefinition="DEC(2,0)")
	private Integer sr;

	/** 出現聯徵特殊負面資訊 **/
	@Column(name="JCIC_WARNING_FLAG", length=1, columnDefinition="CHAR(1)")
	private String jcic_warning_flag;

	/** 本案為最終採用之關係人評等 **/
	@Column(name="FINAL_RATING_FLAG", length=1, columnDefinition="CHAR(1)")
	private String final_rating_flag;
	
	/** 
	 * J10信用評分種類<p/>
	 * A：實際評分(J10_SCORE應有值請檢核)<br/>
	 *  B：無法評分(代碼為001~015)<br/>
	 *  C：無法評分，且揭露理由為有信用不良紀錄，且目前無正常授信帳戶或有效信用卡正卡(代碼為016~022)<br/>
	 *  D：固定評分(代碼為023~043)<br/>
	 *  N：無<br/>
	 *  空白：N.A(未徵提或無聯徵信用報告)<br/>
	 *  *代碼內容詳個人信用評分各類理由對照
	 */
	@Column(name="J10_SCORE_FLAG", length=2, columnDefinition="CHAR(2)")
	private String j10_score_flag;

	/** J10信用評分 **/
	@Column(name="J10_SCORE", columnDefinition="DEC(4,0)")
	private Integer j10_score;

	/** 支援評等 **/
	@Column(name="SPR", columnDefinition="DEC(2,0)")
	private Integer spr;

	/** 最終評等 **/
	@Column(name="FR", columnDefinition="DEC(2,0)")
	private Integer fr;

	/** 資料修改人(行員代號) **/
	@Column(name="UPDATER", length=6, columnDefinition="VARCHAR(6)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name="TMESTAMP", columnDefinition="TIMESTAMP")
	private Timestamp tmestamp;

	/** 覆核日期(C121M01A) **/
	@Temporal(TemporalType.DATE)
	@Column(name="CHKDATE", columnDefinition="DATE")
	private Date chkdate;

	/** 覆核人員(行員代號)(C121M01A) **/
	@Column(name="CHKEMPNO", length=6, columnDefinition="CHAR(6)")
	private String chkempno;

	/** 文件建立日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CREATEDT", columnDefinition="DATE")
	private Date createdt;

	/** 最後異動日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="UPDATEDT", columnDefinition="DATE")
	private Date updatedt;

	/** 核心模型分數 **/
	@Column(name="CORE_SCORE", columnDefinition="DEC(15,5)")
	private BigDecimal core_score;

	/** 核心模型標準化分數 **/
	@Column(name="CORE_STDSCORE", columnDefinition="DEC(15,5)")
	private BigDecimal core_stdscore;

	/** 聯徵特殊負面資訊/VEDA累加風險點數 **/
	@Column(name="JSPTS", columnDefinition="DEC(2,0)")
	private Integer jspts;

	/** 聯徵特殊負面資訊降等數/一般警訊降等數 **/
	@Column(name="GWS_DG", columnDefinition="DEC(2,0)")
	private Integer gws_dg;

	/** 特殊警訊降等數 **/
	@Column(name="SWS_DG", columnDefinition="DEC(2,0)")
	private Integer sws_dg;

	/** 其他資訊升等數 **/
	@Column(name="OI_UG", columnDefinition="DEC(2,0)")
	private Integer oi_ug;

	/** 
	 * 外部J10評等升降等數<p/>
	 * 升降等數(+/-) 不調整(0)  DF: 99
	 */
	@Column(name="JR_AUTODG", columnDefinition="DEC(2,0)")
	private Integer jr_autodg;

	/** 
	 * 主觀評等更新升降等數<p/>
	 * 升降等數(+/-) 不調整(0)
	 */
	@Column(name="ADJ_RATING", columnDefinition="DEC(2,0)")
	private Integer adj_rating;

	/** 違約機率 **/
	@Column(name="DR", columnDefinition="DEC(8,5)")
	private BigDecimal dr;

	/** 違約機率(預估1年期) **/
	@Column(name="DR_1YR", columnDefinition="DEC(8,5)")
	private BigDecimal dr_1yr;

	/** 上傳資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DATA_SRC_DT", columnDefinition="DATE")
	private Date data_src_dt;

	/** NCB信用報告有無Y/N  **/
	@Column(name="NCB_REPORT_FLAG", length=1, columnDefinition="CHAR(1)")
	private String ncb_report_flag;
	
	/** 特殊警訊評等上限 **/
	@Column(name="SWS_RATING_CAP", columnDefinition="DEC(2,0)")
	private Integer sws_rating_cap;
	
	/** 為主借款人且無NCB報告之旗標  **/
	@Column(name="BORROWER_NO_NCB", length=1, columnDefinition="CHAR(1)")
	private String borrower_no_ncb;
	
	/** 其他資訊降等數 **/
	@Column(name="UI_DG", columnDefinition="DEC(2,0)")
	private Integer ui_dg;
	
	/**SLOPE斜率
	 * 0.08 <p/>
	 **/
	@Column(name="SLOPE", columnDefinition="DECIMAL(6,4)")
	private BigDecimal slope;
	
	/**INTERCEPT 截距 
	 * 1.8 <p/>
	 **/
	@Column(name="INTERCEPT", columnDefinition="DECIMAL(6,4)")
	private BigDecimal intercept;
	
	/** 
	 * 預測壞率<p/>
	 * "=1/1+EXP((初始評分-A)/B)"
	 */
	@Column(name="PREDICT_BAD_RATE", columnDefinition="DECIMAL(14,8)")
	private BigDecimal predict_bad_rate;

	
	/** 取得分行別 **/
	public String getBr_cd() {
		return this.br_cd;
	}
	/** 設定分行別 **/
	public void setBr_cd(String value) {
		this.br_cd = value;
	}

	/** 取得NOTES文件編號 **/
	public String getNoteid() {
		return this.noteid;
	}
	/** 設定NOTES文件編號 **/
	public void setNoteid(String value) {
		this.noteid = value;
	}

	/** 
	 * 取得評等日期<p/>
	 * 最終評等日
	 */
	public Date getRating_date() {
		return this.rating_date;
	}
	/**
	 *  設定評等日期<p/>
	 *  最終評等日
	 **/
	public void setRating_date(Date value) {
		this.rating_date = value;
	}

	/** 取得評等文件編號 **/
	public String getRating_id() {
		return this.rating_id;
	}
	/** 設定評等文件編號 **/
	public void setRating_id(String value) {
		this.rating_id = value;
	}

	/** 取得客戶統一編號 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定客戶統一編號 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得主借款人統一編號 **/
	public String getCust_key() {
		return this.cust_key;
	}
	/** 設定主借款人統一編號 **/
	public void setCust_key(String value) {
		this.cust_key = value;
	}

	/** 
	 * 取得授信科目<p/>
	 * 3-4碼授信科目
	 */
	public String getLoan_code() {
		return this.loan_code;
	}
	/**
	 *  設定授信科目<p/>
	 *  3-4碼授信科目
	 **/
	public void setLoan_code(String value) {
		this.loan_code = value;
	}

	/** 
	 * 取得評等模型類別(c121m01a.mowType)
	 */
	public String getMowtype() {
		return this.mowtype;
	}
	/**
	 *  設定評等模型類別(c121m01a.mowType)
	 **/
	public void setMowtype(String value) {
		this.mowtype = value;
	}
	
	/** 
	 * 取得房貸/非房貸註記(l141m01c.modelType)
	 * N=非房貸、M=房貸
	 */
	public String getMowtype2() {
		return this.mowtype2;
	}
	/**
	 *  設定房貸/非房貸註記(l141m01c.modelType)
	 *  N=非房貸、M=房貸
	 **/
	public void setMowtype2(String value) {
		this.mowtype2 = value;
	}
	
	/** 
	 * 採用模型註記
	 * 國別碼(l120m01a.ratingFlag)
	 */
	public String getMowtype_country() {
		return this.mowtype_country;
	}
	/** 
	 * 採用模型註記
	 * 國別碼(l120m01a.ratingFlag)
	 */
	public void setMowtype_country(String value) {
		this.mowtype_country = value;
	}

	/** 取得模型版本-大版 **/
	public Integer getMowver1() {
		return this.mowver1;
	}
	/** 設定模型版本-大版 **/
	public void setMowver1(Integer value) {
		this.mowver1 = value;
	}

	/** 取得模型版本-小版 **/
	public Integer getMowver2() {
		return this.mowver2;
	}
	/** 設定模型版本-小版 **/
	public void setMowver2(Integer value) {
		this.mowver2 = value;
	}

	/** 
	 * 取得科目<p/>
	 * 8碼會計科目
	 */
	public String getSubjcode() {
		return this.subjcode;
	}
	/**
	 *  設定科目<p/>
	 *  8碼會計科目
	 **/
	public void setSubjcode(String value) {
		this.subjcode = value;
	}

	/** 取得授信期間(年) **/
	public BigDecimal getLoan_period() {
		return this.loan_period;
	}
	/** 設定授信期間(年) **/
	public void setLoan_period(BigDecimal value) {
		this.loan_period = value;
	}

	/** 取得相關身分 **/
	public String getLngeflag() {
		return this.lngeflag;
	}
	/** 設定相關身分 **/
	public void setLngeflag(String value) {
		this.lngeflag = value;
	}

	/** 取得文件狀態 **/
	public String getDocstatus() {
		return this.docstatus;
	}
	/** 設定文件狀態 **/
	public void setDocstatus(String value) {
		this.docstatus = value;
	}

	/** 取得本件額度序號 **/
	public String getCntrno() {
		return this.cntrno;
	}
	/** 設定本件額度序號 **/
	public void setCntrno(String value) {
		this.cntrno = value;
	}

	/** 取得初始評等 **/
	public Integer getPr() {
		return this.pr;
	}
	/** 設定初始評等 **/
	public void setPr(Integer value) {
		this.pr = value;
	}

	/** 取得獨立評等 **/
	public Integer getSr() {
		return this.sr;
	}
	/** 設定獨立評等 **/
	public void setSr(Integer value) {
		this.sr = value;
	}

	/** 取得出現聯徵特殊負面資訊 **/
	public String getJcic_warning_flag() {
		return this.jcic_warning_flag;
	}
	/** 設定出現聯徵特殊負面資訊 **/
	public void setJcic_warning_flag(String value) {
		this.jcic_warning_flag = value;
	}

	/** 取得本案為最終採用之關係人評等 **/
	public String getFinal_rating_flag() {
		return this.final_rating_flag;
	}
	/** 設定本案為最終採用之關係人評等 **/
	public void setFinal_rating_flag(String value) {
		this.final_rating_flag = value;
	}
	
	/** 
	 * 取得J10信用評分種類<p/>
	 * A：實際評分(J10_SCORE應有值請檢核)<br/>
	 *  B：無法評分(代碼為001~015)<br/>
	 *  C：無法評分，且揭露理由為有信用不良紀錄，且目前無正常授信帳戶或有效信用卡正卡(代碼為016~022)<br/>
	 *  D：固定評分(代碼為023~043)<br/>
	 *  N：無<br/>
	 *  空白：N.A(未徵提或無聯徵信用報告)<br/>
	 *  *代碼內容詳個人信用評分各類理由對照
	 */
	public String getJ10_score_flag() {
		return this.j10_score_flag;
	}
	/**
	 *  設定J10信用評分種類<p/>
	 *  A：實際評分(J10_SCORE應有值請檢核)<br/>
	 *  B：無法評分(代碼為001~015)<br/>
	 *  C：無法評分，且揭露理由為有信用不良紀錄，且目前無正常授信帳戶或有效信用卡正卡(代碼為016~022)<br/>
	 *  D：固定評分(代碼為023~043)<br/>
	 *  N：無<br/>
	 *  空白：N.A(未徵提或無聯徵信用報告)<br/>
	 *  *代碼內容詳個人信用評分各類理由對照
	 **/
	public void setJ10_score_flag(String value) {
		this.j10_score_flag = value;
	}

	/** 取得J10信用評分 **/
	public Integer getJ10_score() {
		return this.j10_score;
	}
	/** 設定J10信用評分 **/
	public void setJ10_score(Integer value) {
		this.j10_score = value;
	}

	/** 取得支援評等 **/
	public Integer getSpr() {
		return this.spr;
	}
	/** 設定支援評等 **/
	public void setSpr(Integer value) {
		this.spr = value;
	}

	/** 取得最終評等 **/
	public Integer getFr() {
		return this.fr;
	}
	/** 設定最終評等 **/
	public void setFr(Integer value) {
		this.fr = value;
	}

	/** 取得資料修改人(行員代號) **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定資料修改人(行員代號) **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Timestamp getTmestamp() {
		return this.tmestamp;
	}
	/** 設定資料修改日期 **/
	public void setTmestamp(Timestamp value) {
		this.tmestamp = value;
	}

	/** 取得覆核日期(C121M01A) **/
	public Date getChkdate() {
		return this.chkdate;
	}
	/** 設定覆核日期(C121M01A) **/
	public void setChkdate(Date value) {
		this.chkdate = value;
	}

	/** 取得覆核人員(行員代號)(C121M01A) **/
	public String getChkempno() {
		return this.chkempno;
	}
	/** 設定覆核人員(行員代號)(C121M01A) **/
	public void setChkempno(String value) {
		this.chkempno = value;
	}

	/** 取得文件建立日期 **/
	public Date getCreatedt() {
		return this.createdt;
	}
	/** 設定文件建立日期 **/
	public void setCreatedt(Date value) {
		this.createdt = value;
	}

	/** 取得最後異動日期 **/
	public Date getUpdatedt() {
		return this.updatedt;
	}
	/** 設定最後異動日期 **/
	public void setUpdatedt(Date value) {
		this.updatedt = value;
	}

	/** 取得核心模型分數 **/
	public BigDecimal getCore_score() {
		return this.core_score;
	}
	/** 設定核心模型分數 **/
	public void setCore_score(BigDecimal value) {
		this.core_score = value;
	}

	/** 取得核心模型標準化分數 **/
	public BigDecimal getCore_stdscore() {
		return this.core_stdscore;
	}
	/** 設定核心模型標準化分數 **/
	public void setCore_stdscore(BigDecimal value) {
		this.core_stdscore = value;
	}

	/** 取得聯徵特殊負面資訊/VEDA累加風險點數 **/
	public Integer getJspts() {
		return this.jspts;
	}
	/** 設定聯徵特殊負面資訊/VEDA累加風險點數 **/
	public void setJspts(Integer value) {
		this.jspts = value;
	}

	/** 取得聯徵特殊負面資訊降等數/一般警訊降等數 **/
	public Integer getGws_dg() {
		return this.gws_dg;
	}
	/** 設定聯徵特殊負面資訊降等數/一般警訊降等數 **/
	public void setGws_dg(Integer value) {
		this.gws_dg = value;
	}

	/** 取得特殊警訊降等數 **/
	public Integer getSws_dg() {
		return this.sws_dg;
	}
	/** 設定特殊警訊降等數 **/
	public void setSws_dg(Integer value) {
		this.sws_dg = value;
	}

	/** 取得其他資訊升等數 **/
	public Integer getOi_ug() {
		return this.oi_ug;
	}
	/** 設定其他資訊升等數 **/
	public void setOi_ug(Integer value) {
		this.oi_ug = value;
	}

	/** 
	 * 取得外部J10評等升降等數<p/>
	 * 升降等數(+/-) 不調整(0)  DF: 99
	 */
	public Integer getJr_autodg() {
		return this.jr_autodg;
	}
	/**
	 *  設定外部J10評等升降等數<p/>
	 *  升降等數(+/-) 不調整(0)  DF: 99
	 **/
	public void setJr_autodg(Integer value) {
		this.jr_autodg = value;
	}

	/** 
	 * 取得主觀評等更新升降等數<p/>
	 * 升降等數(+/-) 不調整(0)
	 */
	public Integer getAdj_rating() {
		return this.adj_rating;
	}
	/**
	 *  設定主觀評等更新升降等數<p/>
	 *  升降等數(+/-) 不調整(0)
	 **/
	public void setAdj_rating(Integer value) {
		this.adj_rating = value;
	}

	/** 取得違約機率 **/
	public BigDecimal getDr() {
		return this.dr;
	}
	/** 設定違約機率 **/
	public void setDr(BigDecimal value) {
		this.dr = value;
	}

	/** 取得違約機率(預估1年期) **/
	public BigDecimal getDr_1yr() {
		return this.dr_1yr;
	}
	/** 設定違約機率(預估1年期) **/
	public void setDr_1yr(BigDecimal value) {
		this.dr_1yr = value;
	}

	/** 取得上傳資料日期 **/
	public Date getData_src_dt() {
		return this.data_src_dt;
	}
	/** 設定上傳資料日期 **/
	public void setData_src_dt(Date value) {
		this.data_src_dt = value;
	}

	/** 取得NCB信用報告有無Y/N **/
	public String getNcb_report_flag() {
		return ncb_report_flag;
	}
	/** 設定NCB信用報告有無Y/N **/
	public void setNcb_report_flag(String ncb_report_flag) {
		this.ncb_report_flag = ncb_report_flag;
	}
	
	/** 取得特殊警訊評等上限 **/
	public Integer getSws_rating_cap() {
		return sws_rating_cap;
	}
	/** 設定特殊警訊評等上限 **/
	public void setSws_rating_cap(Integer sws_rating_cap) {
		this.sws_rating_cap = sws_rating_cap;
	}
	
	/** 取得為主借款人且無NCB報告之旗標 **/
	public String getBorrower_no_ncb() {
		return borrower_no_ncb;
	}
	/** 設定為主借款人且無NCB報告之旗標 **/
	public void setBorrower_no_ncb(String borrower_no_ncb) {
		this.borrower_no_ncb = borrower_no_ncb;
	}
	
	/** 取得其他資訊降等數 **/
	public Integer getUi_dg() {
		return ui_dg;
	}
	/** 設定其他資訊降等數 **/
	public void setUi_dg(Integer ui_dg) {
		this.ui_dg = ui_dg;
	}	
	
	/** 取得斜率<p/> */
	public BigDecimal getSlope() {
		return this.slope;
	}
	/** 設定斜率<p/> **/
	public void setSlope(BigDecimal value) {
		this.slope = value;
	}
	
	/** 取得截距<p/> */
	public BigDecimal getIntercept() {
		return this.intercept;
	}
	/** 設定截距<p/> **/
	public void setIntercept(BigDecimal value) {
		this.intercept = value;
	}
	
	/** 
	 * 取得預測壞率<p/>
	 * "=1/1+EXP((初始評分-A)/B)"
	 */
	public BigDecimal getPredict_bad_rate() {
		return this.predict_bad_rate;
	}
	/**
	 *  設定預測壞率<p/>
	 *  "=1/1+EXP((初始評分-A)/B)"
	 **/
	public void setPredict_bad_rate(BigDecimal value) {
		this.predict_bad_rate = value;
	}
}
