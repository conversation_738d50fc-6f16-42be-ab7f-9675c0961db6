/* 
 * LMS1601S04Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 動用審核表 - RPA資料查詢
 * </pre>
 * 
 * @since 2011/10/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/5,REX,new
 *          </ul>
 */
public class LMS1201S28Panel extends Panel {

	public LMS1201S28Panel(String id) {
		super(id);
	}
	
	public LMS1201S28Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);

	}

	/**/
	private static final long serialVersionUID = 1L;

}
