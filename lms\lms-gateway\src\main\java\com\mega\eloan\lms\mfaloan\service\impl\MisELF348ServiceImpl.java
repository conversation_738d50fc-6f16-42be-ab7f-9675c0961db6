package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisELF348Service;

@Service
public class MisELF348ServiceImpl extends AbstractMF<PERSON>loanJdbc implements
		MisELF348Service {

	@Override
	public List<Map<String, Object>> getELF348DPChk(String SITE1, String SITE2,
			String SITE3, String SITE4, String LNNO1, String LNNO2) {

		return getJdbc().queryForList("elf348.selbyCityZip",
				new String[] { SITE1, SITE2, SITE3, SITE4, LNNO1, LNNO2 });
	}

	@Override
	public List<Map<String, Object>> getELF348DPChk(String SITE1, String SITE2,
			String SITE3, String LNNO1, String LNNO2) {

		return getJdbc().queryForList("elf348.selbyNoSITE4",
				new String[] { SITE1, SITE2, SITE3, LNNO1, LNNO2 });
	}

	@Override
	public List<Map<String, Object>> getELF348SITE3(String SITE1, String SITE2) {
		return getJdbc().queryForList("elf348.selSITE3byCityZip",
				new String[] { SITE1, SITE2 });
	}

	@Override
	public List<Map<String, Object>> getELF348SITE4(String SITE1, String SITE2,
			String SITE3) {
		return getJdbc().queryForList("elf348.selSITE4byCityZip",
				new String[] { SITE1, SITE2, SITE3 });
	}

	@Override
	public List<Map<String, Object>> getCOLL0101findByKey(String SITE1,
			String SITE2, String SITE3, String SITE4, String LNNO1, String LNNO2) {
		return getJdbc().queryForList("MIS.COLL0101findByKey",
				new String[] { SITE1, SITE2, SITE3, SITE4, LNNO1, LNNO2 });
	}

}
