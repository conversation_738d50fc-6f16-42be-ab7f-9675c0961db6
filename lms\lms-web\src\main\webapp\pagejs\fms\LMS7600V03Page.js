$(document).ready(function(){
    // 判斷查詢為何種形式
    $("[name=queryData]").click(function(){
        $(".select").hide()
        //J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        $("#queryDataTr" + DOMPurify.sanitize($(this).val())).show();
    });
    $("#managerId").change(function(){
        if ($(this).val() == "0") {
            $("#managerNm").show();
        }
        else {
            $("#managerNm").hide();
        }
    });
	
    var grid = $("#gridview").iGrid({
        handler: 'lms7600gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        action: "queryL140mm4a",
        postData: {
            docStatus: viewstatus
        },
        rowNum: 15,
        sortname: "custId",
        sortorder: "desc|desc",
        multiselect: true,
        colModel: [{
	        colHeader: i18n.lms7600v01["L140MM4A.custId"], //借款戶統一編號
	        align: "left", width: 100, sortable: true, name: 'custId',
	        formatter: 'click', onclick: openDoc
	    }, {
	        colHeader: i18n.lms7600v01["L140MM4A.custName"], //借款戶名稱
	        align: "left", width: 100, sortable: true, name: 'custName'
	    },{
            colHeader: i18n.lms7600v01['L140MM4A.cntrNo'],//"額度序號",
            name: 'cntrNo',
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.lms7600v01['L140MM4A.creator'],//"分行經辦",
            name: 'updater',
            width: 80,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms7600v01['L140MM4A.approver'],//"覆核",
            name: 'approver',
            width: 80,
            sortable: true,
            align: "center"
        }, {
                colHeader: i18n.lms7600v01["L140MM4A.approveTime"], // 核准日期
                align: "left",
                width: 80, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'approveTime',
                formatter: 'date',
                formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d H:i'
                }
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        $.form.submit({
            url: '..' + rowObject.docURL + '/01',
            data: {
                formAction: "queryL140mm4a",
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    }
    
    $("#buttonPanel").find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
            // L140M01M.error1=此功能不能多選
            CommonAPI.showMessage(i18n.lms7600m01["L140MM4A.error1"]);
        }
        else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").click(function(){
		openFilterBox();
    });
	
	// 篩選
    function openFilterBox(){
        $("#filterBox").thickbox({
			title: '',
            width: 400,
            height: 150,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
					
					if($.trim($("#cntrnoFiltered").val()) == "" && $.trim($("#custIdFiltered").val()) == ""){
						return API.showErrorMessage(i18n.lms7600v01['L140MM4A.msg.error.filter']);
					}

					$("#gridview").jqGrid("setGridParam", {
				        postData: $.extend($("#filterForm").serializeData(), {
				            handler: 'lms7600gridhandler',
				            formAction: 'filterL140mm4aByEditCompletedCase',
				            docStatus: viewstatus,
				            mainDocStatus: viewstatus,
							custId: $("#custIdFiltered").val(),
							cntrNo: $("#cntrnoFiltered").val(),
				            rowNum: 15
				        })
				    }).trigger("reloadGrid");

                    $.thickbox.close();
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }
			
		});
    }
});
