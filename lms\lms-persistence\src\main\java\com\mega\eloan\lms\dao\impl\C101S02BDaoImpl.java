package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C101S02BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C101S02B;

/** 大數據風險報告查詢結果 **/
@Repository
public class C101S02BDaoImpl extends LMSJpaDao<C101S02B, String> implements
		C101S02BDao {

	@Override
	public C101S02B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C101S02B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		if (mainId != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		}
		search.setMaxResults(Integer.MAX_VALUE);
		List<C101S02B> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C101S02B findByUniqueKey(String mainId, String custId, String dupNo){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C101S02B.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}

	@Override
	public List<C101S02B> findByList(String mainId, String custId, String dupNo) {
		ISearch search = createSearchTemplete();
        if (mainId != null) {
            search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
        }
        if (custId != null) {
            search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
        }
        if (dupNo != null) {
            search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
        }
        search.setMaxResults(Integer.MAX_VALUE);
        List<C101S02B> list = createQuery(search).getResultList();
        return list;
	}
}