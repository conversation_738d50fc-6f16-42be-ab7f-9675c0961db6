package com.mega.eloan.lms.dao.impl;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C900S03ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900S03A;

/** FTP_IMWM0017檔  **/
@Repository
public class C900S03ADaoImpl extends LMSJpaDao<C900S03A, String>
	implements C900S03ADao {

	@Override
	public C900S03A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public int countByFnGenDateGenTime(String fn, String genDate, String genTime){
		Query query = getEntityManager().createNamedQuery("C900S03A.countByFnGenDateGenTime");
		// 設置參數
		query.setParameter(1, fn);
		query.setParameter(2, genDate);
		query.setParameter(3, genTime);
		Integer count = (Integer) query.getSingleResult();
		return count.intValue();
	}
	
	@Override
	public C900S03A findByData(String fn, String genDate, String genTime, String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "fn", fn);
		search.addSearchModeParameters(SearchMode.EQUALS, "genDate", genDate);
		search.addSearchModeParameters(SearchMode.EQUALS, "genTime", genTime);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		return findUniqueOrNone(search);
	}
}