var _handler = "cls3801m01formhandler";
$(function(){
    var grid = $("#gridview").iGrid({
        handler: 'cls3801gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        action: "queryView",
        postData: {
            docStatus : viewstatus
        },
        rowNum: 15,
        sortname: "createTime|custId",
        sortorder: "desc|asc|desc",//desc
        multiselect: true,
        colModel: [{
            colHeader: i18n.cls3801m01["C103M01A.gridCustId"],//客戶統編
            align: "left", 
            width: 90, 
            sortable: true, 
            name: 'custId',
            formatter: 'click', 
            onclick: openDoc
        }, {
            colHeader: i18n.cls3801m01["C103M01A.dupNo"],
            align: "left", 
            width: 10, 
            //sortable: true, 
            name: 'dupNo'
        }, {
            colHeader: i18n.cls3801m01["C103M01A.gridCustName"],
            align: "left", 
            width: 120, 
            //sortable: true, 
            name: 'custName'
        }, {
            colHeader: i18n.cls3801m01['C103M01A.updater'],//異動人員
            name: 'updater',
            width: 80,
            align: "center"
        }, {
            colHeader: i18n.cls3801m01["C103M01A.createTime"], //建立日期
            align: "center",
            width: 80, // 設定寬
            name: 'createTime',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d H:i:s',
                newformat: 'Y-m-d'
            }
        }, {
            colHeader: i18n.cls3801m01['C103M01A.updateTime'],//異動日期
            name: 'updateTime',
            width: 80,
            align: "center",
            formatter: 'date',
            formatoptions: {
            	srcformat: 'Y-m-d H:i:s',
                newformat: 'Y-m-d'
            }
        },{
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docStatus',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        },
        codetypeItem: {}
    });
	
	function openDoc(cellvalue, options, rowObject) {
		$.form.submit({
			url : '../cls/cls3801m01/01',
			data : {
				'oid' : rowObject.oid,
				'mainOid' : rowObject.oid,
				'mainId' : rowObject.mainId,
				'mainDocStatus' : viewstatus
			},
			target : rowObject.oid
		});					
	};
	
	
    $("#buttonPanel").find("#btnView").click(function(){
    	var id = $("#gridview").getGridParam('selarrrow');
        if (id.length==0) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
        	// includeId.selData=請選擇一筆資料!!
        	return CommonAPI.showMessage(i18n.def["includeId.selData"]);
        } else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").click(function(){
    	var _id = "_div_cls3801v01_filter";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table class='tb2' >");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3801m01["C103M01A.gridCustId"]+"</td><td>"
					+"<input type='text' id='search_custId' name='search_custId'  maxlength='10'>"
					+"</td></tr>");
			dyna.push(" </table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		    $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	       title: '篩選',
	       width: 550,
           height: 190,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
                  $.thickbox.close();
                  //=============
                  $("#gridview").setGridParam({
  	                postData: $.extend(
  	                	{}
  	                	,$("#"+_form).serializeData()
  	                ),
  	                search: true
                  }).trigger("reloadGrid");
               },
               "cancel": function(){
            	   $.thickbox.close();
               }
           }
		});
    });

});
