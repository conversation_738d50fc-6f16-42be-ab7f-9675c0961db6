<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
		<div id="C160M01CDiv" class="content">
			<fieldset>
				<legend><b><th:block th:text="#{'title.legend'}">檢附資訊</th:block></b></legend>
				<b><th:block th:text="#{'C160M01C.msg1'}">檢附文件（已收到(已完成)者請勾選「V」，未收到(未完成)者請勾選「X」，免附者請勾「免」。）</th:block></b><br/>
				<button type="button" id="allFinish" >
					<span class="text-only"><th:block th:text="#{'button.allFinish'}">全部收到/完成</th:block></span>
				</button>
				<button type="button" id="allWithout" >
					<span class="text-only"><th:block th:text="#{'button.allWithout'}">全部免附</th:block></span>
				</button>
				<button type="button" id="pullinAgain" >
					<span class="text-only"><th:block th:text="#{'button.pullinAgain'}">重新引進</th:block></span>
				</button>
				<button type="button" id="allPrint" class="forview">
					<span class="text-only"><th:block th:text="#{'button.allPrint'}">列印保管袋附件</th:block></span>
				</button>
				<button type="button" id="allDetails" >
					<span class="text-only"><th:block th:text="#{'button.allDetails'}">產生資料清冊</th:block></span>
				</button>
				<div id="detials" ></div>
				<th:block th:if="${show_allocateFundList_Msg_visible}">
					<p class="text-red" style="margin:5px 10px;">
						<th:block th:text="#{'AllocateFundsCheckList.text.precautions'}">
		                	「央行管制」購屋(或購地)案件撥款當日檢核表：請注意應於首次動撥日當日查詢聯徵中心B29及B33項目，
							並當日將動審表送呈覆核完成，Aloan當日方能撥款，上述作業皆需於同一日完成。
						</th:block>
						<a href="/lms-web/img/lms/AllocateFundsCheckList.xlsx" target="_blank" style="font-weight: bold"><th:block th:text="#{'AllocateFundsCheckList.file.download.excel'}">購屋購地央管案件撥款當日檢核表</th:block></a>
	                </p>
				</th:block>
			</fieldset>
			
			<div id="openC801M01A" style="display:none;">
            <td width="100%">
					<button type="button" id="pullinC801M01ABt">
                         <span class="text-only"><th:block th:text="#{'button.pullinAgain'}">重新引進</th:block></span>
                    </button>
                    <button type="button" id="delC801M01ABt">
                         <span class="text-only"><th:block th:text="#{'button.delete'}">刪除</th:block></span>
                    </button>
                    <div id="C801M01AGrid">             				        
					</div>
			</td>
			</div>							
			
			<fieldset>
				<legend><b><th:block th:text="#{'C160M01A.sign'}">批示</th:block>&<th:block th:text="#{'C160M01A.comm'}">審核意見</th:block></b></legend>
				<form id="C160M01AForm" name="C160M01AForm" >
					<table class="tb2" width="100%">
						<tr>
							<td width="50%" class="hd2" align="center"><th:block th:text="#{'C160M01A.sign'}">批示</th:block></td>
							<td width="50%" class="hd2" align="center">
								<th:block th:text="#{'C160M01A.comm'}">審核意見</th:block>
								<button type="button" id="btnPhrase" >
									<span class="text-only"><th:block th:text="#{'button.Phrase'}">片語</th:block></span>
								</button>
							</td>
						</tr>
						<tr>
							<td align="center" ><textarea id="sign" name="sign" cols="50" rows="7" maxlength="3072" maxlengthC="1024"></textarea></td>
							<td align="center" ><textarea id="comm" name="comm" cols="50" rows="7" maxlength="3072" maxlengthC="1024"></textarea></td>
						</tr>
					</table>
				</form>
			</fieldset>
			
			<fieldset>
				<legend><b><th:block th:text="#{'title.files'}">附加檔案</th:block></b></legend>
				<button type="button" id="addFiles" class="forview" >
					<span class="text-only"><th:block th:text="#{'title.files'}">附加檔案</th:block></span>
				</button>
			</fieldset>
		</div>
		
		<script type="text/javascript">loadScript('pagejs/cls/CLS1161S03Panel');</script>
	</th:block>
</body>
</html>
