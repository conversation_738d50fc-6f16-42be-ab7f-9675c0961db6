/* 
 *RPS4035R01RptServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.report.impl;

import java.text.ParseException;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lms.pages.RPS4035V00Page;
import com.mega.eloan.lms.lms.service.RPS4035Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * 婉卻記錄查詢紀錄
 * </pre>
 * 
 * @since 2012/2/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/9,REX,new
 *          </ul>
 */
@Service("rps4035r01rptservice")
public class RPS4035R01RptServiceImpl extends AbstractReportService {

	@Resource
	RPS4035Service rps4035Service;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	LMSService lmsService;
	//
	// private static Logger logger = LoggerFactory
	// .getLogger(RPS4035R01RptServiceImpl.class);

	@Override
	public String getReportTemplateFileName() {
		Locale locale = null;
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		return "report/lms/RPS4035R01_" + locale.toString() + ".rpt";
		// 　local用
		// return "D:/RPS4035R01_zh_TW.rpt";
	}

	/*
	 * (non-Javadoc) 設定需要傳入RPT參數
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.AbstractReportService#setReportData(com
	 * .mega.eloan.lms.base.report.ReportGenerator,
	 * org.apache.wicket.PageParameters)
	 */
	@Override
	public void setReportData(ReportGenerator reportTools, PageParameters params) throws ParseException {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(RPS4035V00Page.class);
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));
		String custName = Util.nullToSpace(params.getString("custName"));
		Locale locale = null;
		try {

			locale = LocaleContextHolder.getLocale();
			if (locale == null) {
				locale = Locale.getDefault();
			}

			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			rptVariableMap.put("queryDate",
					Util.getDate(CapDate.getCurrentTimestamp()));
			rptVariableMap.put("queryPeople", user.getUserName());
			rptVariableMap.put("custId", custId);
			rptVariableMap.put("dupNo", dupNo);
			rptVariableMap.put("custName", custName);
			rptVariableMap.put("lnunidMemo", Util.trim(prop.getProperty("lnunid.memo")));

			String logoPath = lmsService.getLogoShowPath(UtilConstants.RPTPicType.兆豐LOGO,"00",user.getUnitNo());
			rptVariableMap.put("LOGOSHOW", logoPath);
			
			List<Map<String, Object>> lnunid = rps4035Service
					.getLnunIdByCustId(custId, dupNo);
			if (lnunid != null) {
				titleRows = this.setLNUNIDDataList(titleRows, locale, lnunid,
						prop);
			}
			List<Map<String, Object>> lnunid02 = rps4035Service
					.getLnunId02ByCustId(custId, dupNo);
			if (lnunid02 != null) {
				titleRows = this.setLNUNID02DataList(titleRows, locale,
						lnunid02, prop);
			}

			reportTools.setLang(locale);
			reportTools.setVariableData(rptVariableMap);
			reportTools.setRowsData(titleRows);
			// local用
			// reportTools.setTestMethod(true);
		}finally{
			
		}
	}

	/**
	 * 設定LNUNID資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            LNUNID List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setLNUNIDDataList(
			List<Map<String, String>> titleRows, Locale locale,
			List<Map<String, Object>> list, Properties prop) {
		Map<String, String> mapInTitleRows = null;
		StringBuffer temp = new StringBuffer();
		mapInTitleRows = Util.setColumnMap();
		mapInTitleRows.put("ReportBean.column01", "FL");
		titleRows.add(mapInTitleRows);
		String[] codeType = { "RejtCode" };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);
		String def = "";
		for (Map<String, Object> lnunid : list) {
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column01", "FY");
			mapInTitleRows.put("ReportBean.column02",
					Util.getDate((Date) lnunid.get("regdt")));
			mapInTitleRows.put("ReportBean.column03", LMSUtil.concat(temp,
					(String) lnunid.get("regbr"), " ",
					branchService.getBranchName((String) lnunid.get("regbr"))));
			mapInTitleRows.put(
					"ReportBean.column04",
					prop.getProperty(StrUtils.concat("lnunid.clscase",
							lnunid.get("clscase"))));
			def = Util.trim(lnunid.get("refusecd"));
			mapInTitleRows.put("ReportBean.column05", StrUtils.concat(codeMap
					.get("RejtCode").containsKey(def) ? codeMap.get("RejtCode")
					.get(def) : "", "\r", lnunid.get("refuseds")));
			
			mapInTitleRows.put("ReportBean.column06", Util.trim(lnunid.get("statuscd")));
			mapInTitleRows.put("ReportBean.column07", Util.trim(lnunid.get("regteller")));
			titleRows.add(mapInTitleRows);
		}

		if (list.isEmpty()) {
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column01", "FY");
			mapInTitleRows.put("ReportBean.column02",
					prop.getProperty("lnunid.noCase"));
			titleRows.add(mapInTitleRows);
		}
		return titleRows;
	}

	/**
	 * 設定LNUNID02資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            LNUNID02 List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setLNUNID02DataList(
			List<Map<String, String>> titleRows, Locale locale,
			List<Map<String, Object>> list, Properties prop) {
		Map<String, String> mapInTitleRows = null;

		mapInTitleRows = Util.setColumnMap();
		mapInTitleRows.put("ReportBean.column01", "DL");
		titleRows.add(mapInTitleRows);

		for (Map<String, Object> lnunid : list) {
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column01", "DY");
			mapInTitleRows.put("ReportBean.column02",
					Util.getDate((Date) lnunid.get("regdt")));
			mapInTitleRows.put(
					"ReportBean.column03",
					prop.getProperty(StrUtils.concat("lnunid.clscase",
							lnunid.get("clscase"))));
			mapInTitleRows.put("ReportBean.column04",
					(String) lnunid.get("refuseds"));
			titleRows.add(mapInTitleRows);
		}

		if (list.isEmpty()) {
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column01", "DY");
			mapInTitleRows.put("ReportBean.column02",
					prop.getProperty("lnunid.noCase"));
			titleRows.add(mapInTitleRows);
		}
		return titleRows;
	}

}
