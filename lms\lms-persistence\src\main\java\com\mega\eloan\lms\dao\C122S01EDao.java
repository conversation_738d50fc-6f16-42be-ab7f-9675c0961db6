/* 
 * C122S01EDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.sql.Timestamp;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C122S01E;

/** 線上貸款申貸資料查詢紀錄 **/
public interface C122S01EDao extends IGenericDao<C122S01E> {

	C122S01E findByOid(String oid);
	
	List<C122S01E> findByMainId(String mainId);

	List<C122S01E> findByMainIdUserIdIp(String mainId, String userId,
			String userIp, Timestamp startTime, Timestamp endTime);
	
}