function paUI(value){
    if (value == "Y") {
        $("#paFieldset").show();
    } else {
        paInit();
        $("#paFieldset").hide();
    }
}

function paInit(){
//    $("#paTable").find(':input').not(':radio').val('0');
    $("#paTable").find("input[id*='_itemYn']").attr("checked", false);
    $("#paTable").find("input[id*='_itemCnt']").val('0');
    $("#paTable").find("textarea[id*='_dscr']").val('');
}

var initDfd = initDfd || new $.Deferred();
var initAll = initAll || new $.Deferred();
initDfd.done(function(json){
	
	if(json.lock){
	}

	$("input[name='needPa']").change(function(k, v){
	    // 是否有須扣分情事
        var value = $(this).val();
        paUI(value);
        if (value == "N") { // 不須扣分 => 帶入預設值
            paInit();
        }
    });

    $("input[name='needPa']").attr('checked', false).filter("[value='" + json.needPa + "']").attr("checked", true).trigger('change');
});
