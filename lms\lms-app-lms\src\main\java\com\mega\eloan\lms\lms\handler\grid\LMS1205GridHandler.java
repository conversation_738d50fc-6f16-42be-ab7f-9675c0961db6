/* 
 * LMS1205GridHandler.java 
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.grid;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;


import org.springframework.context.annotation.Scope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.CustIdFormatter;
import com.mega.eloan.common.formatter.I18NFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.NGFlagHelper;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.CESConstant;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CMSDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSLgdService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.enums.GaapFlagEnum;
import com.mega.eloan.lms.enums.PeriodTypeEnum;
import com.mega.eloan.lms.lms.pages.LMS1015M01Page;
import com.mega.eloan.lms.lms.pages.LMS1205M01Page;
import com.mega.eloan.lms.lms.pages.LMS1205S05Page06b;
import com.mega.eloan.lms.lms.panels.LMS1405S06Panel;
import com.mega.eloan.lms.lms.panels.LMSS07Panel;
import com.mega.eloan.lms.lms.service.FSSGridService;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.model.C100M01;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121S01A;
import com.mega.eloan.lms.model.C140M01A;
import com.mega.eloan.lms.model.C140M04A;
import com.mega.eloan.lms.model.C140S04A;
import com.mega.eloan.lms.model.C140S04B;
import com.mega.eloan.lms.model.C140S04C;
import com.mega.eloan.lms.model.C140S09A;
import com.mega.eloan.lms.model.C140S09B;
import com.mega.eloan.lms.model.C140S09C;
import com.mega.eloan.lms.model.C140S09D;
import com.mega.eloan.lms.model.C140S09E;
import com.mega.eloan.lms.model.C140S09F;
import com.mega.eloan.lms.model.F101M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01D;
import com.mega.eloan.lms.model.L120M01E;
import com.mega.eloan.lms.model.L120M01J;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01M;
import com.mega.eloan.lms.model.L120S03A;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S04B;
import com.mega.eloan.lms.model.L120S05B;
import com.mega.eloan.lms.model.L120S05D;
import com.mega.eloan.lms.model.L120S05E;
import com.mega.eloan.lms.model.L120S05F;
import com.mega.eloan.lms.model.L120S06A;
import com.mega.eloan.lms.model.L120S11A;
import com.mega.eloan.lms.model.L120S11A_LOC;
import com.mega.eloan.lms.model.L120S21A;
import com.mega.eloan.lms.model.L120S21B;
import com.mega.eloan.lms.model.L120S21C;
import com.mega.eloan.lms.model.L120S24A;
import com.mega.eloan.lms.model.L120S24B;
import com.mega.eloan.lms.model.L120S25A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L720M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.dao.utils.SearchParameterUtil;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.formatter.NumericFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 授信簽報書Grid
 * </pre>
 * 
 * @since 2011/8/1
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/8/1,Miller Lin,new
 *          <li>2013/04/22,UFO,當登入單位=918時，需增加判斷NGFLAG
 *          <li>2013/07/17,Rex,修改設定授管處和營運中心 在已核准的顯示規則
 *          </ul>
 */
@Scope("request")
@Controller("lms1205gridhandler")
public class LMS1205GridHandler extends AbstractGridHandler {

	@Resource
	LMS1205Service service1205;

	// @Resource
	// LMS1215Service service1215

	@Resource
	LMS1405Service service1405;

	@Resource
	CodeTypeService codeservice;

	@Resource
	UserInfoService userservice;

	@Resource
	BranchService branchService;

	@Resource
	DocFileService docfileservice;

	@Resource
	FSSGridService gridService; // 查詢待列印的一般財務報表

	@Resource
	LMSService lmsService;

	@Resource
	CLSService clsService;

	@Resource
	DocLogService docLogService;

	@Resource
	LMSLgdService lmsLgdService;

	private final String DATEYYYYMMDD = "yyyy-MM-dd";

	/**
	 * 查詢L120M01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitType = user.getUnitType();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205M01Page.class);
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		// 取得文件狀態代碼(交易代碼)
		String docStatus = Util.trim(params.getString("mainDocStatus"));
		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.海外_編製中;
		}

		// 2012-09-06 黃建霖 begin
		String custId = Util.trim(params.getString("custId"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		// 2012-09-06 黃建霖 end

		Date fromDate = null;
		Date endDate = null;
		if (!Util.isEmpty(Util.nullToSpace(params.getString("fromDate")))) {
			fromDate = Util.parseDate(Util.nullToSpace(params
					.getString("fromDate")));
		}
		if (!Util.isEmpty(Util.nullToSpace(params.getString("endDate")))) {
			endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate") + " 23:59:59"));
		}

		if (fromDate != null && endDate != null) {
			Object[] reason = { fromDate, endDate };
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "caseDate",
					reason);
		}

		switch (docStatusEnum) {
		// Rex 註解 當遇到有 | 的V page 文件狀態會有問題 像營運中心等會帶到caseBrid的條件
		// case 海外_編製中:
		// case 海外_待覆核:
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
		// user.getUnitNo());
		// break;
		case 海外_呈總行:

			// 當登錄分行為澳洲 ，分行呈總行文件狀態 後為提會待登錄 或提會帶覆核
			// IBranch branchtype = branch.getBranch(user.getUnitNo());
			// if (UtilConstants.Country.澳洲.equals(branchtype.getCountryType())
			// || UtilConstants.Country.加拿大.equals(branchtype
			// .getCountryType())
			// || UtilConstants.Country.泰國.equals(branchtype
			// .getCountryType())) {
			String[] showDoc = new String[] {
					CreditDocStatusEnum.海外_總行待覆核.getCode(),
					CreditDocStatusEnum.海外_總行提會待登錄.getCode(),
					CreditDocStatusEnum.海外_總行提會待覆核.getCode(),
					CreditDocStatusEnum.泰國_提會待登錄.getCode(),
					CreditDocStatusEnum.泰國_提會待覆核.getCode() };
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					showDoc);
			// } else {
			// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
			// "docStatus", CreditDocStatusEnum.海外_總行待覆核.getCode());
			// }
			break;
		case 海外_呈授管處:
			// 當狀態為海外_呈授管處時要變更搜尋條件要文件狀態為C或H結尾都要顯示，並且join授權檔
			pageSetting
					.addSearchModeParameters(SearchMode.OR,
							new SearchModeParameter(SearchMode.LIKE,
									"docStatus", "%H"),
							new SearchModeParameter(SearchMode.LIKE,
									"docStatus", "%C"));
			// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
			// "docStatus", CreditDocStatusEnum.授管處_待陳復.getCode());
			break;
		case 營運中心_呈總處:
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "docStatus",
					"%H");

			break;

		case 營運中心_待更正:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"backUnit", "S"), new SearchModeParameter(
									SearchMode.EQUALS, "backUnit", "A")),
					new SearchModeParameter(SearchMode.EQUALS, "returnFromBH",
							"1"));

			break;

		case 授管處_待收案件:
			pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
					"hqReceiveDate", "");
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					UtilConstants.Field.目前編製行, UtilConstants.BankNo.授管處);
			break;
		case 授管處_已收案件:
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"hqReceiveDate", "");
			break;
		case 授管處_審查中:
			// 審查中在提授審會時不能不能出現此案件
			pageSetting
					.addSearchModeParameters(
							SearchMode.OR,
							new SearchModeParameter(
									SearchMode.OR,
									new SearchModeParameter(
											SearchMode.OR,
											new SearchModeParameter(
													SearchMode.OR,
													new SearchModeParameter(
															SearchMode.OR,
															new SearchModeParameter(
																	SearchMode.EQUALS,
																	"hqMeetFlag",
																	null),
															new SearchModeParameter(
																	SearchMode.EQUALS,
																	"hqMeetFlag",
																	"A")),
													new SearchModeParameter(
															SearchMode.EQUALS,
															"hqMeetFlag", "B")),
											new SearchModeParameter(
													SearchMode.EQUALS,
													"hqMeetFlag", "C")),
									new SearchModeParameter(SearchMode.EQUALS,
											"hqMeetFlag", "")),
							new SearchModeParameter(SearchMode.EQUALS,
									"hqMeetFlag", "0"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
			break;
		case 授管處_待更正:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"backUnit", "C"), new SearchModeParameter(
									SearchMode.EQUALS, "backUnit", "S")),
					new SearchModeParameter(SearchMode.EQUALS, "returnFromBH",
							"1"));
			break;
		case 海外_已核准:
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"docCode", UtilConstants.Casedoc.DocCode.陳復陳述案);
			// 如果登錄的是總行要多看到 泰國的提會待登錄、和泰國提會待覆核的案件
			if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())
					|| BranchTypeEnum.營運中心.getCode().equals(unitType)) {
				// 已核准案件要排除陳覆陳述案，
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						new String[] { CreditDocStatusEnum.海外_已核准.getCode(),
								CreditDocStatusEnum.泰國_提會待登錄.getCode(),
								CreditDocStatusEnum.泰國_提會待覆核.getCode() });
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", docStatus);
			}
			// 2013/07/17,Rex,修改設定授管處和營運中心 在已核准的顯示規則
			this.setBy050Grid(pageSetting, unitType);
			break;
		case 海外_陳復案_陳述案:
			// 已核准 且 docCode = 3
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.海外_已核准.getCode());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					UtilConstants.Casedoc.DocCode.陳復陳述案);
			break;

		case 授管處_免批覆案件:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.海外_已核准.getCode());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					UtilConstants.Casedoc.DocCode.陳復陳述案);
			break;
		case 營運中心_所有提會案件:
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"rptTitleArea1", "");
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"rptTitleArea1", "");
			break;
		default:
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);
			break;
		}

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);

		if (docStatus.indexOf(CreditDocStatusEnum.總處營業單位已會簽.getCode()) != -1) {
			// 不顯示特殊分行提會
			// pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
			// "hqMeetFlag",
			// "");
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"hqMeetFlag", "A"),
							new SearchModeParameter(SearchMode.EQUALS,
									"hqMeetFlag", "B")),
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"hqMeetFlag", "C"),
							new SearchModeParameter(SearchMode.IS_NULL,
									"hqMeetFlag", "")));
		}

		if (!"S".equals(unitType) && !"A".equals(unitType)) {
			// 當非授管處或營運中心時
			// 限定只顯示海外授信案件
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
					UtilConstants.Casedoc.typCd.海外);
		}

		// // 排除掉異常通報案件
		// if (!UtilConstants.BankNo.授管處.equals(user.getUnitNo())
		// && !UtilConstants.BankNo.中區營運中心.equals(user.getUnitNo())
		// && !UtilConstants.BankNo.中部區域授信中心.equals(user.getUnitNo())
		// && !UtilConstants.BankNo.北一區營運中心.equals(user.getUnitNo())
		// && !UtilConstants.BankNo.北二區營運中心.equals(user.getUnitNo())
		// && !UtilConstants.BankNo.南區營運中心.equals(user.getUnitNo())
		// && !UtilConstants.BankNo.南部區域授信中心.equals(user.getUnitNo())
		// && !UtilConstants.BankNo.桃竹苗區營運中心.equals(user.getUnitNo())) {
		// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
		// "docCode", UtilConstants.Casedoc.DocCode.異常通報);
		// }else{
		// if(CreditDocStatusEnum.授管處_審查中.getCode().equals(docStatus) ||
		// CreditDocStatusEnum.營運中心_審查中.getCode().equals(docStatus)){
		// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
		// "docCode", UtilConstants.Casedoc.DocCode.異常通報);
		// }
		// }

		// UFO@********:協議案查詢條件
		NGFlagHelper.addSearchParamsAT918(pageSetting, user);

		// 當user使用經辦名稱等來排序時，因為不是唯一性，所以誇頁的排序可能會告成重覆資料，所以再加個oid來排
		pageSetting.addOrderBy("oid");

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120M01A.class,
				pageSetting);
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			StringBuffer strB = new StringBuffer();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			// other.msg144=舊
			if (Util.trim(model.getCaseNo()).indexOf(
					pop2.getProperty("other.msg144")) != -1) {
				// other.msg142=婉卻
				model.setGist(pop2.getProperty("other.msg142"));
			} else {
				if ("2".equals(Util.trim(model.getReturnFromBH()))) {
					// other.msg143=待陳復
					model.setGist(pop.getProperty("other.msg143"));
				} else {
					model.setGist(UtilConstants.Mark.SPACE);
				}
			}
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			// l120m01a.edit=修改中
			model.setReEstFlag("Y".equals(model.getReEstFlag()) ? pop
					.getProperty("l120m01a.edit") : "");
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));
			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode()));
			} else {
				model.setDocStatus("");
			}
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getAreaAppraiser())) ? userservice
						.getUserName(model.getAreaAppraiser()) : Util
						.trim(model.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(getPerName(Util.trim(model
						.getAreaAppraiser())));
			}
			// 「授管處負責經辦(hqAppraiser)」對應出員工姓名(不要出現null)
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getHqAppraiser())) ? userservice
						.getUserName(model.getHqAppraiser()) : Util.trim(model
						.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			// 當「營運中心放行時間(areaSendInfo)」有值時，顯示「營運中心放行時間」；其他則顯示「核准日期(approveTime)」
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setAreaSendInfo(model.getAreaSendInfo());
			} else {
				model.setAreaSendInfo(model.getApproveTime());
			}
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢L120M01AGrid 資料(異常通報用)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01a1(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205M01Page.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		// 取得文件狀態代碼(交易代碼)
		String docStatus = params.getString("mainDocStatus");
		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}
		// 2012-09-06 黃建霖 begin
		String custId = Util.trim(params.getString("custId"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		// 2012-09-06 黃建霖 end

		Date fromDate = null;
		Date endDate = null;
		if (!Util.isEmpty(Util.nullToSpace(params.getString("fromDate")))) {
			fromDate = Util.parseDate(Util.nullToSpace(params
					.getString("fromDate")));
		}
		if (!Util.isEmpty(Util.nullToSpace(params.getString("endDate")))) {
			endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate") + " 23:59:59"));
		}

		if (fromDate != null && endDate != null) {
			Object[] reason = { fromDate, endDate };
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "caseDate",
					reason);
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
		// "ownBrId", user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (Util.notEquals(user.getUnitType(), "S")
				&& Util.notEquals(user.getUnitType(), "A")) {
			// 當非授管處或營運中心時
			// 排除掉海外授信案件
			// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
			// "typCd",
			// UtilConstants.Casedoc.typCd.海外);
		}

		// 限定只顯示異常通報案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
				UtilConstants.Casedoc.DocCode.異常通報);

		// UFO@********:協議案查詢條件
		NGFlagHelper.addSearchParamsAT918(pageSetting, user);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120M01A.class,
				pageSetting);
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			StringBuffer strB = new StringBuffer();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			// l120m01a.edit=修改中
			model.setReEstFlag("Y".equals(model.getReEstFlag()) ? pop
					.getProperty("l120m01a.edit") : "");
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));
			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				// 用UID暫存文件狀態
				model.setUid(Util.trim(model.getDocStatus()));
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode()));
			} else {
				model.setDocStatus("");
			}
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getAreaAppraiser())) ? userservice
						.getUserName(model.getAreaAppraiser()) : Util
						.trim(model.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(getPerName(Util.trim(model
						.getAreaAppraiser())));
			}
			// 「授管處負責經辦(hqAppraiser)」對應出員工姓名(不要出現null)
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getHqAppraiser())) ? userservice
						.getUserName(model.getHqAppraiser()) : Util.trim(model
						.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			// 當「營運中心放行時間(areaSendInfo)」有值時，顯示「營運中心放行時間」；其他則顯示「核准日期(approveTime)」
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setAreaSendInfo(model.getAreaSendInfo());
			} else {
				model.setAreaSendInfo(model.getApproveTime());
			}
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢L120M01AGrid 資料(授審、催收、常董)用
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120m01a3(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205M01Page.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		String docStatus = params.getString("mainDocStatus");
		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}
		String kind = params.getString("kind");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);
		// pageSetting.addSearchModeParameters(SearchMode.OR,
		// new SearchModeParameter(SearchMode.EQUALS, "docStatus",
		// docStatus), new SearchModeParameter(SearchMode.EQUALS,
		// "docStatus", CreditDocStatusEnum.會簽後修改編製中.getCode()));
		//1=授審會、2=逾審會、3=常董會、4=審計委員會、A=特殊分行授審會、B=特殊分行逾審會、C=特殊分行常董會、D=特殊分行審計委員會;
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "hqMeetFlag",
				kind);
		// pageSetting.addSearchModeParameters(
		// SearchMode.OR,
		// new SearchModeParameter(SearchMode.EQUALS, "hqMeetFlag", kind),
		// new SearchModeParameter(SearchMode.EQUALS, "hqMeetFlag",
		// UtilConstants.Casedoc.HqMeetFlag.授審會.equals(kind) ? "A"
		// : UtilConstants.Casedoc.HqMeetFlag.逾審會
		// .equals(kind) ? "B"
		// : UtilConstants.Casedoc.HqMeetFlag.常董會
		// .equals(kind) ? "C" : kind));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (!"S".equals(user.getUnitType()) && !"A".equals(user.getUnitType())) {
			// 限定只顯示海外授信案件
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
					UtilConstants.Casedoc.typCd.海外);
		}

		// // 排除掉異常通報案件
		// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "docCode",
		// UtilConstants.Casedoc.DocCode.異常通報);

		// UFO@********:協議案查詢條件
		NGFlagHelper.addSearchParamsAT918(pageSetting, user);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120M01A.class,
				pageSetting);

		for (int w = 0; w < page.getContent().size(); w++) {
			L120M01A model = (L120M01A) page.getContent().get(w);
			StringBuilder strB = new StringBuilder();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());
			strB.append(
					("1".equals(model.getDocKind()) && "1".equals(model
							.getDocType())) ? pop.getProperty("L1205G.grid1")
							: ("2".equals(model.getDocKind()) && "1"
									.equals(model.getDocType())) ? pop
									.getProperty("L1205G.grid2") : ("1"
									.equals(model.getDocKind()) && "2"
									.equals(model.getDocType())) ? pop
									.getProperty("L1205G.grid12") : pop
									.getProperty("L1205G.grid13")).append("(")
					.append(docCodeName(model.getDocCode())).append(")");
			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			model.setDocRslt(model.getDocKind());
			model.setDocKind(strB.toString());
			model.setDocStatus(getMessage("docStatus."
					+ CreditDocStatusEnum.getEnum(model.getDocStatus())
							.getCode()));
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			model.setAreaAppraiser(getPerName(Util.trim(model
					.getAreaAppraiser())));
			model.setHqAppraiser(getPerName(Util.trim(model.getHqAppraiser())));
			// TODO 由於目前登錄的userId是自己定義，資料庫裡沒有相對應的userId，所以回傳一定是空白...
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢L121M01AGrid 資料(海外聯貸案Grid)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL121m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205M01Page.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		// 取得文件狀態代碼(交易代碼)
		String docStatus = params.getString("mainDocStatus");
		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		Date fromDate = null;
		Date endDate = null;

		// 2012-09-06 黃建霖 begin
		String custId = Util.trim(params.getString("custId"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		// 2012-09-06 黃建霖 end

		if (!Util.isEmpty(Util.nullToSpace(params.getString("fromDate")))) {
			fromDate = Util.parseDate(Util.nullToSpace(params
					.getString("fromDate")));
		}
		if (!Util.isEmpty(Util.nullToSpace(params.getString("endDate")))) {
			endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate") + " 23:59:59"));
		}

		if (fromDate != null && endDate != null) {
			Object[] reason = { fromDate, endDate };
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "caseDate",
					reason);
		}

		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}
		switch (docStatusEnum) {
		case 營運中心_海外聯貸案_已會簽:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"areaDocstatus",
					CreditDocStatusEnum.營運中心_海外聯貸案_已會簽.getCode());
			break;
		case 營運中心_海外聯貸案_待放行:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"areaDocstatus",
					CreditDocStatusEnum.營運中心_海外聯貸案_待放行.getCode());
			break;
		case 營運中心_海外聯貸案_會簽中:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"areaDocstatus",
					CreditDocStatusEnum.營運中心_海外聯貸案_會簽中.getCode());
			break;
		default:
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);
			break;
		}

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (!"S".equals(user.getUnitType()) && !"A".equals(user.getUnitType())) {
			// 限定只顯示海外授信案件
			// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
			// UtilConstants.Casedoc.typCd.海外);
		}

		// 排除掉異常通報案件
		// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "docCode",
		// UtilConstants.Casedoc.DocCode.異常通報);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120M01A.class,
				pageSetting);

		for (int w = 0; w < page.getContent().size(); w++) {
			L120M01A model = (L120M01A) page.getContent().get(w);
			StringBuilder strB = new StringBuilder();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());
			strB.append(
					"1".equals(model.getDocKind()) ? pop
							.getProperty("L1205G.grid1") : pop
							.getProperty("L1205G.grid2")).append("(")
					.append(docCodeName(model.getDocCode())).append(")");
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			model.setDocRslt(model.getDocKind());
			model.setDocKind(strB.toString());
			model.setDocStatus(getMessage("docStatus."
					+ CreditDocStatusEnum.getEnum(model.getAreaDocstatus())
							.getCode()));
			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			// TODO 由於目前登錄的userId是自己定義，資料庫裡沒有相對應的userId，所以回傳一定是空白...
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 篩選L120M01AGrid 外部的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult old_queryL120m01a2_20131104(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205M01Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		String unitType = user.getUnitType();
		String typCd = Util.trim(params.getString("typCd"));
		String docType = Util.trim(params.getString("docType"));
		String docKind = Util.trim(params.getString("docKind"));
		String docCode = Util.trim(params.getString("docCode"));
		String custName = Util.trim(params.getString("custName"));
		String updater = Util.trim(params.getString("updater"));
		String approveDateS = Util.nullToSpace(Util.trim(params
				.getString("approveDateS")));
		String approveDateE = Util.nullToSpace(Util.trim(params
				.getString("approveDateE")));
		String caseBrId = Util.nullToSpace(params.getString("caseBrId"));

		// 2012-09-06 黃建霖 begin
		String custId = Util.trim(params.getString("custId"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		// 2012-09-06 黃建霖 end

		Date fromDate = null;
		Date endDate = null;
		if (!Util.isEmpty(Util.nullToSpace(params.getString("fromDate")))) {
			fromDate = Util.parseDate(Util.nullToSpace(params
					.getString("fromDate")));
		}
		if (!Util.isEmpty(Util.nullToSpace(params.getString("endDate")))) {
			endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate") + " 23:59:59"));
		}

		// Miller added at 2012/12/17
		boolean isReject = params.getBoolean("isReject");

		if (fromDate != null && endDate != null) {
			Object[] reason = { fromDate, endDate };
			if (docStatus.equals("03K|01K|02K|04K")) {
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"approveTime", reason);
			} else {
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"caseDate", reason);
			}
			// if (docStatus.equals(CreditDocStatusEnum.海外_已核准.getCode())) {
			// pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
			// "approveTime", reason);
			// } else {
			// pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
			// "caseDate", reason);
			// }
		}
		if (!Util.isEmpty(caseBrId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
					caseBrId);
		}

		// 篩選婉卻/變更格式選項 Miller added at 2012/12/17
		if (isReject) {
			// 3婉卻變更格式
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docRslt",
					"3");
		}
		if (Util.isNotEmpty(typCd)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
					typCd);
		}
		if (Util.isNotEmpty(docType)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
					docType);
		}
		if (Util.isNotEmpty(docKind)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docKind",
					docKind);
		}
		if (Util.isNotEmpty(docCode)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					docCode);
		}
		if (Util.isNotEmpty(custName)) {
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custName",
					custName + "%");
		}
		if (Util.isNotEmpty(updater)) {

			if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"hqAppraiser", updater);
			} else if (UtilConstants.BankNo.中區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.中部區域授信中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.北一區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.北二區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.南區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.南部區域授信中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(user.getUnitNo())) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"areaAppraiser", updater);
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"updater", updater);
			}

		}
		if (!Util.isEmpty(approveDateS) && !Util.isEmpty(approveDateE)) {
			pageSetting.addSearchModeParameters(
					SearchMode.BETWEEN,
					"endDate",
					new Object[] { Util.parseDate(approveDateS),
							Util.parseDate(approveDateE) });
		}

		// UFO@********:協議案查詢條件
		NGFlagHelper.addSearchParamsAT918(pageSetting, user);

		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}

		switch (docStatusEnum) {
		case 海外_呈總行:

			// 當登錄分行為澳洲 ，分行呈總行文件狀態 後為提會待登錄 或提會帶覆核
			IBranch branchtype = branchService.getBranch(user.getUnitNo());
			if (UtilConstants.Country.澳洲.equals(branchtype.getCountryType())
					|| UtilConstants.Country.加拿大.equals(branchtype
							.getCountryType())) {
				String[] showDoc = new String[] {
						CreditDocStatusEnum.海外_總行待覆核.getCode(),
						CreditDocStatusEnum.海外_總行提會待登錄.getCode(),
						CreditDocStatusEnum.海外_總行提會待覆核.getCode() };
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						showDoc);
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", CreditDocStatusEnum.海外_總行待覆核.getCode());
			}
			break;
		case 海外_呈授管處:
			// 當狀態為海外_呈授管處時要變更搜尋條件要文件狀態為C或H結尾都要顯示，並且join授權檔
			pageSetting
					.addSearchModeParameters(SearchMode.OR,
							new SearchModeParameter(SearchMode.LIKE,
									"docStatus", "%H"),
							new SearchModeParameter(SearchMode.LIKE,
									"docStatus", "%C"));
			// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
			// "docStatus", CreditDocStatusEnum.授管處_待陳復.getCode());
			break;
		case 營運中心_呈總處:
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "docStatus",
					"%H");

			break;

		case 營運中心_待更正:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"backUnit", "S"), new SearchModeParameter(
									SearchMode.EQUALS, "backUnit", "A")),
					new SearchModeParameter(SearchMode.EQUALS, "returnFromBH",
							"1"));

			break;

		case 授管處_待收案件:
			pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
					"hqReceiveDate", "");
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					UtilConstants.Field.目前編製行, UtilConstants.BankNo.授管處);
			break;
		case 授管處_已收案件:
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"hqReceiveDate", "");
			break;
		case 授管處_審查中:
			// 審查中在提授審會時不能不能出現此案件
			// pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
			// "hqMeetFlag", null);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
			break;
		case 授管處_待更正:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"backUnit", "C"), new SearchModeParameter(
									SearchMode.EQUALS, "backUnit", "S")),
					new SearchModeParameter(SearchMode.EQUALS, "returnFromBH",
							"1"));
			break;
		case 海外_已核准:
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"docCode", UtilConstants.Casedoc.DocCode.陳復陳述案);
			// 如果登錄的是總行要多看到 泰國的提會待登錄、和泰國提會待覆核的案件
			if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
				// 已核准案件要排除陳覆陳述案，
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						new String[] { CreditDocStatusEnum.海外_已核准.getCode(),
								CreditDocStatusEnum.泰國_提會待登錄.getCode(),
								CreditDocStatusEnum.泰國_提會待覆核.getCode() });
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", docStatus);

			}
			// 2013/07/17,Rex,修改設定授管處和營運中心 在已核准的顯示規則
			this.setBy050Grid(pageSetting, unitType);
			break;
		case 海外_陳復案_陳述案:
			// 已核准 且 docCode = 3
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.海外_已核准.getCode());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					UtilConstants.Casedoc.DocCode.陳復陳述案);
			break;

		case 授管處_免批覆案件:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.海外_已核准.getCode());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					UtilConstants.Casedoc.DocCode.陳復陳述案);
			break;
		case 營運中心_所有提會案件:
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"rptTitleArea1", "");
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"rptTitleArea1", "");
			break;
		default:
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);
			break;
		}

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		if (!"S".equals(user.getUnitType()) && !"A".equals(user.getUnitType())) {
			// 限定只顯示海外授信案件
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
					UtilConstants.Casedoc.typCd.海外);
		}

		// 排除掉異常通報案件
		if (!UtilConstants.BankNo.授管處.equals(user.getUnitNo())
				&& !UtilConstants.BankNo.中區營運中心.equals(user.getUnitNo())
				&& !UtilConstants.BankNo.中部區域授信中心.equals(user.getUnitNo())
				&& !UtilConstants.BankNo.北一區營運中心.equals(user.getUnitNo())
				&& !UtilConstants.BankNo.北二區營運中心.equals(user.getUnitNo())
				&& !UtilConstants.BankNo.南區營運中心.equals(user.getUnitNo())
				&& !UtilConstants.BankNo.南部區域授信中心.equals(user.getUnitNo())
				&& !UtilConstants.BankNo.桃竹苗區營運中心.equals(user.getUnitNo())) {
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"docCode", UtilConstants.Casedoc.DocCode.異常通報);
		} else {
			if (CreditDocStatusEnum.授管處_審查中.getCode().equals(docStatus)
					|| CreditDocStatusEnum.營運中心_審查中.getCode().equals(docStatus)) {
				pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
						"docCode", UtilConstants.Casedoc.DocCode.異常通報);
			}
		}
		Page<? extends GenericBean> page = service1205.findPage(L120M01A.class,
				pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name)); // 分行名稱格式化
		dataReformatter.put("userid", new UserNameFormatter(userservice)); // 使用者名稱格式化

		for (int w = 0; w < page.getContent().size(); w++) {
			L120M01A model = (L120M01A) page.getContent().get(w);
			StringBuffer strB = new StringBuffer();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());
			strB.append(
					"1".equals(model.getDocKind()) ? pop
							.getProperty("L1205G.grid1") : pop
							.getProperty("L1205G.grid2")).append("(")
					.append(docCodeName(model.getDocCode())).append(")");
			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			if (Util.trim(model.getCaseNo()).indexOf("舊") != -1) {
				model.setGist("V");
			} else {
				model.setGist(UtilConstants.Mark.SPACE);
			}
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getAreaAppraiser())) ? userservice
						.getUserName(model.getAreaAppraiser()) : Util
						.trim(model.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(getPerName(Util.trim(model
						.getAreaAppraiser())));
			}
			// 和 method : queryL120m01a 內的邏輯相同
			// 「授管處負責經辦(hqAppraiser)」對應出員工姓名(不要出現null)
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getHqAppraiser())) ? userservice
						.getUserName(model.getHqAppraiser()) : Util.trim(model
						.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			if ("Y".equals(model.getReEstFlag())) {
				model.setReEstFlag(pop.getProperty("l120m01a.edit"));
			} else if ("A".equals(model.getReEstFlag())) {
				model.setReEstFlag(pop.getProperty("l120m01a.editA"));
			} else {
				model.setReEstFlag("");
			}
			// 針對授管處特殊分行已會簽的顯示
			// if (docStatus.equals("03K|01K|02K|04K")) {
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
			// }
		}
		// model.setDocStatus(CreditDocStatusEnum.getEnum(model.getDocStatus()));
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢L120S01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("keyMan", true);
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);

		// pageSetting.addOrderBy("oid");
		pageSetting.addOrderBy("keyMan", true);
		pageSetting.addOrderBy("custId", false);
		pageSetting.addOrderBy("dupNo", false);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120S01A.class,
				pageSetting);
		// Page<L120S01A> page = service.getPage(pageSetting);
		List<L120S01A> list = (List<L120S01A>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S01A model = list.get(i);
			model.setCustId(model.getCustId() + " " + model.getDupNo());
			model.setCustPos(findCustPos(model));
			CodeType code1 = new CodeType();
			CodeType code2 = new CodeType();
			String custRlt = Util.trim(model.getCustRlt());
			if (UtilConstants.DEFAULT.是.equals(model.getChkYN())) {
				model.setChkYN("V");
			} else {
				model.setChkYN("X");
			}
			if ("Y".equals(model.getKeyMan())) {
				model.setKeyMan("*");
			} else {
				model.setKeyMan("");
			}
			if (!Util.isEmpty(custRlt)) {
				if (!(custRlt.contains("X"))) {
					// 其他綜合關係
					code1 = codeservice.findByCodeTypeAndCodeValue(
							"Relation_type31", custRlt.substring(0, 1), LocaleContextHolder.getLocale().toString());
					code2 = codeservice.findByCodeTypeAndCodeValue(
							"Relation_type32", custRlt.substring(1, 2), LocaleContextHolder.getLocale().toString());
				} else {
					if (custRlt.endsWith("X")) {
						// 企業關係人
						code1 = codeservice.findByCodeTypeAndCodeValue(
								"Relation_type1", custRlt, LocaleContextHolder.getLocale()
										.toString());
					} else {
						// 親屬關係
						code1 = codeservice.findByCodeTypeAndCodeValue(
								"Relation_type2", custRlt, LocaleContextHolder.getLocale()
										.toString());
					}
				}
			}
			if (code1 != null && code2 != null) {
				if (!Util.isEmpty(code2.getCodeDesc())) {
					StringBuilder strB = new StringBuilder();
					strB.append(code1.getCodeDesc()).append("-")
							.append(code2.getCodeDesc());
					// code2有資料則設定code1+code2
					model.setCustRlt(strB.toString());
				} else {
					if (!Util.isEmpty(code1.getCodeDesc())) {
						// code1有資料則設定code1
						model.setCustRlt(code1.getCodeDesc());
					} else {
						// 無資料則設為空
						model.setCustRlt("");
					}
				}
			}
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	public CapGridResult queryC120M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("keyMan", true);
		pageSetting.addOrderBy("custId", false);
		pageSetting.addOrderBy("dupNo", false);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(C120M01A.class,
				pageSetting);

		List<C120M01A> list = (List<C120M01A>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			C120M01A model = list.get(i);
			model.setCustId(model.getCustId() + " " + model.getDupNo());
			model.setCustPos(findCustPos(model));
			CodeType code1 = new CodeType();
			CodeType code2 = new CodeType();
			String o_custRlt = Util.trim(model.getO_custRlt());
			if (UtilConstants.DEFAULT.是.equals(model.getO_chkYN())) {
				model.setO_chkYN("V");
			} else {
				model.setO_chkYN("X");
			}
			if ("Y".equals(model.getKeyMan())) {
				model.setKeyMan("*");
			} else {
				model.setKeyMan("");
			}
			if (Util.isNotEmpty(o_custRlt)) {
				if (!(o_custRlt.contains("X"))) {
					// 其他綜合關係
					code1 = codeservice.findByCodeTypeAndCodeValue(
							"Relation_type31", o_custRlt.substring(0, 1),
							LocaleContextHolder.getLocale().toString());
					code2 = codeservice.findByCodeTypeAndCodeValue(
							"Relation_type32", o_custRlt.substring(1, 2),
							LocaleContextHolder.getLocale().toString());
				} else {
					if (o_custRlt.endsWith("X")) {
						// 企業關係人
						code1 = codeservice.findByCodeTypeAndCodeValue(
								"Relation_type1", o_custRlt, LocaleContextHolder.getLocale()
										.toString());
					} else {
						// 親屬關係
						code1 = codeservice.findByCodeTypeAndCodeValue(
								"Relation_type2", o_custRlt, LocaleContextHolder.getLocale()
										.toString());
					}
				}
			}
			if (code1 != null && code2 != null) {
				if (!Util.isEmpty(code2.getCodeDesc())) {
					StringBuilder strB = new StringBuilder();
					strB.append(code1.getCodeDesc()).append("-")
							.append(code2.getCodeDesc());
					// code2有資料則設定code1+code2
					model.setO_custRlt(strB.toString());
				} else {
					if (!Util.isEmpty(code1.getCodeDesc())) {
						// code1有資料則設定code1
						model.setO_custRlt(code1.getCodeDesc());
					} else {
						// 無資料則設為空
						model.setO_custRlt("");
					}
				}
			}
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 目前在 2 個地方用 (1)簽報書的借款人基本資料頁籤 (2)於額度中，選擇評等 多傳1個參數 tabFormId
	 */
	public CapGridResult queryC120Rating(ISearch pageSetting,
			PageParameters params) throws CapException {
		String caseId = Util.trim(params.getString("caseId"));
		String l140m01a_oid = Util.trim(params.getString("tabFormId"));

		if (Util.isNotEmpty(l140m01a_oid)) {
			String keymanCustId = "";
			String keymanDupNo = "";
			L140M01A l140m01a = clsService.findL140M01A_oid(l140m01a_oid);
			if (l140m01a != null) {
				keymanCustId = Util.trim(l140m01a.getCustId());
				keymanDupNo = Util.trim(l140m01a.getDupNo());
			}

			if (Util.isNotEmpty(keymanCustId) && Util.isNotEmpty(keymanDupNo)) {
				Set<String> set = new HashSet<String>();
				List<C120M01A> candidata_list = clsService
						.findC120M01A_caseId_keyManIdDup_ratingKind1(caseId,
								keymanCustId, keymanDupNo);
				if (candidata_list.size() > 0) {
					for (C120M01A c120m01a : candidata_list) {
						set.add(c120m01a.getMainId());
					}
				}
				if (set.size() > 0) {
					List<String> c120m01a_mainId_list = new ArrayList<String>(
							set);
					String[] c120m01a_mainId_arr = c120m01a_mainId_list
							.toArray(new String[0]);
					pageSetting.addSearchModeParameters(SearchMode.IN,
							"mainId", c120m01a_mainId_arr);
				} else {
					// 若只有手動insert的從債務人，不應出現在 grid 供經辦挑選
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"mainId", "");
				}
			}
		}

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "caseId", caseId);
		pageSetting.setMaxResults(Integer.MAX_VALUE);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = clsService.findPage(C120M01A.class,
				pageSetting);

		Properties prop_lms1015m01 = MessageBundleScriptCreator
				.getComponentResource(LMS1015M01Page.class);
		String prop_lnYearMonth = prop_lms1015m01
				.getProperty("tab01.lnYearMonth");

		@SuppressWarnings("unchecked")
		List<C120M01A> list = (List<C120M01A>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			C120M01A model = list.get(i);

			/*
			 * XXX 若把該 model 透過 Service 再串到其它的 model EX:
			 * xxxService.findXXX(model);
			 * 
			 * 又在這裡寫 model.setKeyMan 會 overwrite DB 中的值 ===＞ 改用 IFormatter
			 */
			String lnPeriod = "";
			String cmsLocation = "";
			if (true) {
				C121M01A c121m01a = clsService.findC121M01A(model);
				if (c121m01a != null) {
					if (c121m01a.getLnYear() != null
							|| c121m01a.getLnMonth() != null) {
						lnPeriod = MessageFormat.format(prop_lnYearMonth,
								Util.trim(c121m01a.getLnYear()),
								Util.trim(c121m01a.getLnMonth()));
					}

					C121S01A c121s01a = clsService.findC121S01A(c121m01a);
					if (c121s01a != null) {
						cmsLocation = Util.trim(c121s01a.getLocation());
					}
				}
			}

			model.setLnPeriod(lnPeriod);
			model.setCmsLocation(cmsLocation);
		}
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("keyMan", new LMS1205Grid_KeyManFormatter());
		formatter.put("o_chkYN", new LMS1205Grid_ChkYNFormatter());
		return new CapGridResult(list, page.getTotalRow(), formatter);
	}

	class LMS1205Grid_KeyManFormatter implements IFormatter {
		private static final long serialVersionUID = 5386269374814033466L;

		@SuppressWarnings("unchecked")
		@Override
		public String reformat(Object in) throws CapFormatException {
			return Util.equals("Y", in) ? "*" : "";
		}
	}

	class LMS1205Grid_ChkYNFormatter implements IFormatter {

		private static final long serialVersionUID = -8311213843883425077L;

		@SuppressWarnings("unchecked")
		@Override
		public String reformat(Object in) throws CapFormatException {
			return Util.equals("Y", in) ? "V" : "X";
		}
	}

	/**
	 * 依客戶統編查詢L120S01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120s01aById(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String custId = Util.nullToSpace(params.getString("custId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120S01A.class,
				pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S01AGrid 資料(借款人引入)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s01aToGetData(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120S01A.class,
				pageSetting);
		List<L120S01A> list = (List<L120S01A>) page.getContent();
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢資信簡表(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s01e1(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		String thisOid = Util.nullToSpace(params.getString("thisOid"));
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainid);
		L120S01A l120s01a = service1205.findL120s01aByOid(thisOid);
		String caseBrId = "";
		String custId = "";
		String dupNo = "";
		if (l120s01a != null) {
			caseBrId = Util.trim(l120m01a.getCaseBrId());
			custId = Util.trim(l120s01a.getCustId());
			dupNo = Util.trim(l120s01a.getDupNo());
		}
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1205.getCesMainId1(caseBrId,
				custId, dupNo, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢資信簡表(徵信)-- 集團
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s01e1Grp(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainid);
		String authBrId = user.getUnitNo();
		String custId = Util.trim(l120m01a.getCustId());
		String dupNo = Util.trim(l120m01a.getDupNo());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1205.getCesMainId1(authBrId,
				custId, dupNo, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢徵信報告(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s01e2(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		String thisOid = Util.nullToSpace(params.getString("thisOid"));
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainid);
		L120S01A l120s01a = service1205.findL120s01aByOid(thisOid);
		String caseBrId = "";
		String custId = "";
		String dupNo = "";
		if (l120s01a != null) {
			caseBrId = Util.trim(l120m01a.getCaseBrId());
			custId = Util.trim(l120s01a.getCustId());
			dupNo = Util.trim(l120s01a.getDupNo());
		}
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1205.getCesMainId2(caseBrId,
				custId, dupNo, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢徵信報告2(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s01e3(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String oid = Util.nullToSpace(params.getString("oid"));
		String mainId = Util.nullToSpace(params.getString("mainId"));
		L120S01A l120s01a = service1205.findL120s01aByOid(oid);
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainId);
		String caseBrId = "";
		String custId = "";
		String dupNo = "";
		if (l120m01a != null) {
			caseBrId = Util.trim(l120m01a.getCaseBrId());
			custId = Util.trim(l120s01a.getCustId());
			dupNo = Util.trim(l120s01a.getDupNo());
		}
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1205.getCesMainId2(caseBrId,
				custId, dupNo, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢徵信報告--依照使用者輸入之統編(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIds(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		String custId = Util.nullToSpace(params.getString("custId"));
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1205.getCesMainId(caseBrId,
				custId, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢徵信報告 MainId(範圍)(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIds2(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1205.getCesMainId2s(caseBrId,
				mainid, mainid, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢徵信報告 MainId(範圍)(徵信報告用，不限制文件狀態)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIdss2(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1205.getCesMainId2ss(caseBrId,
				pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
			map.put("creator",
					Util.isEmpty(map.get("creator")) ? "" : withIdName(Util
							.trim(Util.nullToSpace(map.get("creator")))));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢資信簡表 MainId(範圍)(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIda(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1205.getCesMainIda(caseBrId,
				mainid, mainid, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢資信簡表 MainId(範圍)(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIdb(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		String custId = Util.nullToSpace(params.getString("custId"));
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1205.getCesMainIdb(caseBrId,
				custId, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢資信簡表 MainId(範圍)(徵信報告用，不限制文件狀態)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIdc(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1205.getCesMainIdc(caseBrId,
				pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢簽報書敘述說明檔
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120m01d(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "itemType", "C");
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120M01D.class,
				pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S03AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s03a(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07Panel.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("cntrNo");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120S03A.class,
				pageSetting);

		List<L120S03A> list = (List<L120S03A>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S03A model = list.get(i);
			if (UtilConstants.DEFAULT.是.equals(model.getChkYN())) {
				model.setChkYN("V");
			} else {
				model.setChkYN("X");
			}
			// 非信保
			if ("2".equals(model.getCrdFlag())) {
				model.setCrdFlag(pop.getProperty("L1205S07.index2"));
				model.setCrdRatio(null);
				model.setRskAmt2(model.getRskAmt1());
				model.setRskr2(model.getRskr1());
				model.setCamt2(model.getCamt1());
				model.setBisr2(model.getBisr1());
				model.setCostr2(model.getCostr1());
			} else {
				model.setCrdFlag(pop.getProperty("L1205S07.index1"));
			}
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢L120S04AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s04a(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07Panel.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		// J-109-0370 相關評估改版
		boolean needCustId = params.getBoolean("needCustId");
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		if (needCustId) {
			String keyCustId = Util.trim(params.getString("keyCustId"));
			String keyDupNo = Util.trim(params.getString("keyDupNo"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "keyCustId",
					keyCustId);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo",
					keyDupNo);
		}
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120S04A.class,
				pageSetting);
		List<L120S04A> list = (List<L120S04A>) page.getContent();

		if (list != null && !list.isEmpty()) {

			Collections.sort(list, new Comparator<L120S04A>() {

				@Override
				public int compare(L120S04A object1, L120S04A object2) {
					int cr = 0;
					String[] resStr1 = Util.trim(object1.getCustRelation())
							.split(",");
					Arrays.sort(resStr1);
					String[] resStr2 = Util.trim(object2.getCustRelation())
							.split(",");
					Arrays.sort(resStr2);

					int a = resStr2[0].compareTo(resStr1[0]);

					String prtFlag1 = object1.getPrtFlag();
					String prtFlag2 = object2.getPrtFlag();
					int prtFlag = prtFlag2.compareTo(prtFlag1);

					if (prtFlag != 0) {
						cr = (prtFlag > 0) ? -1 : 5;
					} else if (a != 0) {
						cr = (a > 0) ? -2 : 4;
					} else {
						long b = (object2.getProfit() == null ? 0 : object2
								.getProfit())
								- (object1.getProfit() == null ? 0 : object1
										.getProfit());
						if (b != 0) {
							cr = (b > 0) ? 3 : -3;
						} else {
							int c = object2.getCustId().compareTo(
									object1.getCustId());
							if (c != 0) {
								cr = (c > 0) ? -4 : 2;
							} else {
								// String oid1 = object1.getOid();
								// String oid2 = object2.getOid();
								// int oidFlag = oid2.compareTo(oid2);
								// if(oidFlag != 0){
								// cr = (oidFlag > 0)? -5:1;
								// }
							}
						}
					}

					return cr;
				}
			});
		}

		for (int i = 0; i < list.size(); i++) {
			L120S04A model = list.get(i);
			if (Util.isEmpty(model.getCustId())) {
				model.setCustId("");
			} else {
				model.setCustId(model.getCustId() + " " + model.getDupNo());
			}
			if (UtilConstants.DEFAULT.是.equals(model.getChkYN())) {
				model.setChkYN("V");
			} else {
				model.setChkYN("X");
			}
			if ("1".equals(model.getPrtFlag())) {
				model.setPrtFlag("V");
			} else {
				model.setPrtFlag("X");
			}

			StringBuilder sb = new StringBuilder();
			String[] strs = Util.trim(model.getCustRelation()).split(",");
			// 對陣列進行排序
			Arrays.sort(strs);
			String custRelationIndex = "";
			for (String s : strs) {
				if (Util.equals(Util.trim(custRelationIndex), "")) {
					custRelationIndex = s;
				}
				if (sb.length() > 0)
					sb.append("/");
				sb.append(Util.trim(pop.getProperty("L1205S07.checkbox" + s)));
			}
			model.setCustRelationIndex(sb.toString());
			model.setCustRelation(custRelationIndex);

		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S04AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120s04b(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07Panel.class);

		String mainid = Util.trim(params.getString("mainId"));
		// J-109-0370 相關評估改版
		boolean needCustId = params.getBoolean("needCustId");
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		if (needCustId) {
			String keyCustId = Util.trim(params.getString("keyCustId"));
			String keyDupNo = Util.trim(params.getString("keyDupNo"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "keyCustId",
					keyCustId);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo",
					keyDupNo);
		}
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120S04B.class,
				pageSetting);
		List<L120S04B> list = (List<L120S04B>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S04B model = list.get(i);
			if (Util.equals(model.getDocKind(), "A")) {
				// L1205S07.grid48=共借戶與本行往來實績彙總表
				model.setRptName(pop.getProperty("L1205S07.grid48"));
			} else {
				// L1205S07.grid43=借戶暨關係戶與本行往來實績彙總表
				model.setRptName(pop.getProperty("L1205S07.grid43"));
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S05BGrid 資料(集團)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL120s05b(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120S05B.class,
				pageSetting);
		List<L120S05B> list = (List<L120S05B>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S05B model = list.get(i);
			StringBuilder strBuf = new StringBuilder();
			strBuf.append(model.getCustId()).append(model.getDupNo())
					.append(" ").append(model.getCustName());
			model.setCustName(strBuf.toString());
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢L120S05DGrid 資料(關係企業)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL120s05d(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120S05D.class,
				pageSetting);
		List<L120S05D> list = (List<L120S05D>) page.getContent();
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢L120S06AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL120s06a(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07Panel.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120S06A.class,
				pageSetting);
		List<L120S06A> list = (List<L120S06A>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S06A model = list.get(i);
			if (UtilConstants.DEFAULT.是.equals(model.getChkYN())) {
				model.setChkYN("V");
			} else {
				model.setChkYN("X");
			}
			StringBuilder str1 = new StringBuilder();
			StringBuilder str2 = new StringBuilder();
			str1.append(model.getCustId()).append(" ").append(model.getDupNo())
					.append(" ").append(model.getCustName());
			str2.append(model.getCustId2()).append(" ")
					.append(model.getDupNo2()).append(" ")
					.append(model.getCustName2());
			model.setCustId(str1.toString());
			model.setCustId2(str2.toString());
			if ("1".equals(model.getPrintMode())) {
				model.setPrintMode(pop.getProperty("L1205S07.grid1"));
			} else {
				model.setPrintMode(pop.getProperty("L1205S07.grid2"));
			}
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢L140SM01AGrid 資料(對照)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryL140m01a2(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String custId = Util.trim(params.getString("custId"));
		String caseBrid = Util.trim(params.getString("textBrid"));
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1205.getLihai(custId, caseBrid,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 根據後端相關身份值找出相對應的名稱
	 * 
	 * @param model
	 *            L120S01A
	 * @return String
	 */
	public String findCustPos(L120S01A model) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205M01Page.class);
		if (Util.trim(model.getCustPos()).length() != 0) {
			switch (model.getCustPos().toCharArray()[0]) {
			case 'C':
				return pop.getProperty("L1205G.grid4");
			case 'D':
				return pop.getProperty("L1205G.grid5");
			case 'G':
				return pop.getProperty("L1205G.grid6");
			case 'N':
				return pop.getProperty("L1205G.grid7");
			case 'S':
				return pop.getProperty("L1205G.grid8");
			default:
				return "";
			}
		}
		return "";
	}

	private String findCustPos(C120M01A model) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205M01Page.class);
		if (Util.trim(model.getCustPos()).length() != 0) {
			switch (model.getCustPos().toCharArray()[0]) {
			case 'C':
				return pop.getProperty("L1205G.grid4");
			case 'D':
				return pop.getProperty("L1205G.grid5");
			case 'G':
				return pop.getProperty("L1205G.grid6");
			case 'N':
				return pop.getProperty("L1205G.grid7");
			case 'S':
				return pop.getProperty("L1205G.grid8");
			default:
				return "";
			}
		}
		return "";
	}

	/**
	 * 查詢L120S01AGrid 資料(原始)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s01aOrigin(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainid);
		if (LMSUtil.isOverSea_CLS(l120m01a)) {
			if (OverSeaUtil.isCaseDoc_CLS_rawBorrowerPanel(l120m01a)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"mainId", mainid);
			} else {
				// ref clsService.findC120M01A_caseId_keyMan(mainId)
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"caseId", mainid);
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"keyMan", "Y");
			}

			// 同一個簽報書，可能含N個評等文件(主借人相同)
			Page<? extends GenericBean> page = service1205.findPage(
					C120M01A.class, pageSetting);
			List<C120M01A> list = (List<C120M01A>) page.getContent();
			Set<String> existIdDup = new HashSet<String>();
			List<C120M01A> newlist = new ArrayList<C120M01A>();
			for (C120M01A c120m01a : list) {
				String idDup = LMSUtil.getCustKey_len10custId(
						c120m01a.getCustId(), c120m01a.getDupNo());
				if (existIdDup.contains(idDup)) {
					continue;
				}
				existIdDup.add(idDup);
				newlist.add(c120m01a);
			}
			return new CapGridResult(newlist, newlist.size());
		} else {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
					mainid);

			Page<? extends GenericBean> page = service1205.findPage(
					L120S01A.class, pageSetting);
			List<L120S01A> list = (List<L120S01A>) page.getContent();
			return new CapGridResult(list, page.getTotalRow());
		}
	}

	/**
	 * 依照案件別代碼取得相對應案件別名稱
	 * 
	 * @param doccode
	 *            String
	 * @return Properties
	 */
	public String docCodeName(String doccode) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205M01Page.class);
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		// 參考此 java 檔裡的 private String getCaseType(L120M01A model, Properties
		// pop, StringBuffer temp) {
		if ("1".equals(doccode)) {
			return pop.getProperty("L1205G.grid9");
		} else if ("2".equals(doccode)) {
			return pop.getProperty("L1205G.grid10");
		} else if ("4".equals(doccode)) {
			return pop2.getProperty("other.msg59");
		} else if (UtilConstants.Casedoc.DocCode.團貸案件.equals(doccode)) {
			// other.msg134=團貸
			return pop2.getProperty("other.msg134");
		} else {
			return pop.getProperty("L1205G.grid11");
		}
	}

	/**
	 * 查詢檔案上傳的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult oldqueryfileBK(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String fieldId = params.getString("fieldId");
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		Page<DocFile> page = docfileservice.readToGrid(pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢檔案上傳的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryfile(ISearch pageSetting, PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String fieldId = params.getString("fieldId");
		boolean needCngName = params.getBoolean("needCngName");
		boolean needBranch = params.getBoolean("needBranch");
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		Page<DocFile> page = docfileservice.readToGrid(pageSetting);
		List<DocFile> list = (List<DocFile>) page.getContent();
		if (needCngName) {
			for (DocFile file : list) {
				// other.msg61=借戶暨關係戶與本行授信往來比較表
				// J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
				if (Util.equals(Util.trim(file.getSrcFileName()),
						"LMS1205R24A_B.xls")) {
					file.setSrcFileName(pop.getProperty("other.msg61_B")
							+ ".xls");
				} else {
					file.setSrcFileName(pop.getProperty("other.msg61") + ".xls");
				}
			}
		}
		if (needBranch) {
			// 需要分行名稱(透過CRYEAR欄位顯示)
			for (DocFile file : list) {
				file.setCrYear(Util.trim(file.getBranchId())
						+ " "
						+ branchService.getBranchName(Util.trim(file
								.getBranchId())));
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢需列印的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryPrint(ISearch pageSetting,
			PageParameters params) throws CapException {
		String printCondition = Util.nullToSpace(params
				.getString("printCondition"));
		String mainId = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.setDistinct(true);
		Page<Map<String, Object>> page = service1205.getBorrows(mainId,
				printCondition, pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢 登錄主要負責人連保人資信狀況資料 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryViewA(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = CapString.trimNull(params.getString("gridMainId"));
		String uid = CapString.trimNull(params.getString("gridUid"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01e.docType", "4");
		Page page = service1205.getC140M04APage(pageSetting);
		Map formatter = new HashMap();
		formatter.put("pcTitle", new CodeTypeFormatter(codeservice, "Title2"));
		formatter.put("pcType", new I18NFormatter("pcType."));
		formatter.put("pcSex", new I18NFormatter("pcSex."));
		List<C140M04A> c140m04as = (List<C140M04A>) page.getContent();
		List<L120M01E> listL120m01e = service1205.findL120m01eByMainId(mainId);
		if (!listL120m01e.isEmpty()) {
			for (L120M01E l120m01e : listL120m01e) {
				for (C140M04A model : c140m04as) {
					if (model.equals(l120m01e.getC140m04a())) {
						if (l120m01e != null) {
							String custId = model.getL120m01e().getDocCustId();
							String dupNo = model.getL120m01e().getDocDupNo();
							model.getL120m01e().setDocCustId(
									custId + " " + dupNo);
						}
						break;
					}
				}
			}
		}
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return result;
	}

	/**
	 * 查詢 第四章 經營事業 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryView41SA(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = CapString.trimNull(params.getString("gridMainId"));
		String uid = CapString.trimNull(params.getString("gridUid"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);
		Page page = service1205.getC140S04APage(pageSetting);
		Map formatter = new HashMap();
		for (C140S04A model : (List<C140S04A>) page.getContent()) {
			if (!Util.isEmpty(model.getInvCap21())) {
				model.setInvCap21(model.getInvCap21().setScale(0));
			}
			if (!Util.isEmpty(model.getAmtUnitST())) {
				model.setAmtUnitST(model.getAmtUnitST().setScale(0));
			}
		}
		formatter.put("invCap11", new CodeTypeFormatter(codeservice,
				"Common_Currcy"));
		formatter.put("invCap21",
				new CodeTypeFormatter(codeservice, "CurrUnit"));
		formatter.put("amtUnitST", new CodeTypeFormatter(codeservice,
				"CurrUnit"));
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return result;
	}

	/**
	 * 查詢 第四章 本人之土地 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryView41SB(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = CapString.trimNull(params.getString("gridMainId"));
		String uid = CapString.trimNull(params.getString("gridUid"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);
		Page page = service1205.getC140S04BPage(pageSetting);
		Map formatter = new HashMap();
		formatter.put("landUse", new landUse1());
		formatter.put("landLevel", new CodeTypeFormatter(codeservice,
				"LandLevel"));
		formatter.put("landRate", new landRate());
		formatter.put("landMp", new landMp());
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return result;
	}

	/**
	 * 查詢 第四章 本人之建物 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryView41SC(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = CapString.trimNull(params.getString("gridMainId"));
		String uid = CapString.trimNull(params.getString("gridUid"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);
		Page page = service1205.getC140S04CPage(pageSetting);
		Map formatter = new HashMap();
		formatter.put("buUse", new CodeTypeFormatter(codeservice, "BuUse"));
		formatter.put("buStru", new CodeTypeFormatter(codeservice, "BuStru"));
		formatter.put("buMp", new buMp());
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return result;
	}

	/**
	 * 查詢 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryView(ISearch pageSetting, PageParameters params) throws CapException {

		String mainId = CapString.trimNull(params.getString("gridMainId"));
		String uid = CapString.trimNull(params.getString("gridUid"));
		String type = CapString.trimNull(params.getString("gridType"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);

		CapGridResult result = new CapGridResult();

		// 取得資料
		if ("A".equals(type)) {
			Page<C140S09A> page = service1205.getC140S09APage(pageSetting);
			Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
			formatter.put("gBal", new gBal());
			result = new CapGridResult(page.getContent(), page.getTotalRow(),
					formatter);
		} else if ("B".equals(type)) {
			Page<C140S09B> page = service1205.getC140S09BPage(pageSetting);
			result = new CapGridResult(page.getContent(), page.getTotalRow());
		} else if ("C".equals(type)) {
			Page<C140S09C> page = service1205.getC140S09CPage(pageSetting);
			result = new CapGridResult(page.getContent(), page.getTotalRow());
		} else if ("D".equals(type)) {
			Page<C140S09D> page = service1205.getC140S09DPage(pageSetting);
			result = new CapGridResult(page.getContent(), page.getTotalRow());
		} else if ("E".equals(type)) {
			Page<C140S09E> page = service1205.getC140S09EPage(pageSetting);
			result = new CapGridResult(page.getContent(), page.getTotalRow());
		} else if ("F".equals(type)) {
			Page<C140S09F> page = service1205.getC140S09FPage(pageSetting);

			Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
			formatter.put("linv14", new CodeTypeFormatter(codeservice,
					"Common_Currcy", CodeTypeFormatter.ShowTypeEnum.Val_Desc));
			result = new CapGridResult(page.getContent(), page.getTotalRow(),
					formatter);
		}

		return result;
	}

	/**
	 * gBal formatter
	 */
	class gBal implements IBeanFormatter {
		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			C140S09A c140s09a = (C140S09A) in;
			String GMbkBal = c140s09a.getGMbkBal() == null ? "" : c140s09a
					.getGMbkBal().toPlainString();
			String GObuBal = c140s09a.getGObuBal() == null ? "" : c140s09a
					.getGObuBal().toPlainString();
			String GOvsBal = c140s09a.getGOvsBal() == null ? "" : c140s09a
					.getGOvsBal().toPlainString();
			return CapMath.add(new String[] { GMbkBal, GObuBal, GOvsBal });
		}
	}

	/**
	 * buMp formatter (buMp + "/" + buMm)
	 */
	class buMp implements IBeanFormatter {

		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			C140S04C meta = (C140S04C) in;

			return new StringBuffer()
					.append(CapString.trimNull(meta.getBuMp())).append(" / ")
					.append(new NumericFormatter().reformat(meta.getBuMm()))
					.toString();
		}
	}

	/**
	 * landUse1 formatter
	 */
	class landUse1 implements IBeanFormatter {

		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) {
			C140S04B meta = (C140S04B) in;

			if (!CapString.isEmpty(meta.getLandUse1())) {
				CodeType landUse1 = codeservice.findByCodeTypeAndCodeValue(
						"LandUse1", meta.getLandUse1());
				CodeType landUse2 = codeservice.findByCodeTypeAndCodeValue(
						"LandUse2" + meta.getLandUse1(), meta.getLandUse2());
				return new StringBuffer(landUse1.getCodeDesc()).append("/")
						.append(landUse2.getCodeDesc()).toString();
			}

			return EloanConstants.EMPTY_STRING;
		}
	}

	/**
	 * landRate formatter
	 */
	class landRate implements IBeanFormatter {

		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			C140S04B meta = (C140S04B) in;
			StringBuffer sb = new StringBuffer();

			if (meta.getLandRateC() != null) {
				sb.append(meta.getLandRateC());
			}

			sb.append('/');

			if (meta.getLandRateD() != null) {
				sb.append(meta.getLandRateD());
			}

			return sb.toString();
		}
	}

	/**
	 * landMp formatter (landMp + "/" + landMm)
	 */
	class landMp implements IBeanFormatter {

		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			C140S04B meta = (C140S04B) in;

			return new StringBuffer()
					.append(CapString.trimNull(meta.getLandMp())).append(" / ")
					.append(new NumericFormatter().reformat(meta.getLandMm()))
					.toString();
		}
	}

	/**
	 * 查詢待列印的一般財務報表。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryIncludeFSS(ISearch pageSetting,
			PageParameters params) throws CapException {
		String gaapFlag = params.getString("gaapFlag",
				GaapFlagEnum.GAAP.getCode()); // 2013.03.07 Mike Add 整合IFRS財報引進
		String getBranch = CapString.trimNull(params.getString("qryBranch"));
		if (CapString.isEmpty(getBranch)) {
			getBranch = MegaSSOSecurityContext.getUnitNo();
		}
		if (!CapString.isEmpty(params.getString("fssCustId", null))) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					params.getString("fssCustId"));
		}
		if (!CapString.isEmpty(params.getString("fssDupNo", null))) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
					params.getString("fssDupNo"));
		}
		String type = CapString.trimNull(params.getString("type"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "type", type);

		// add ifrs 合併報表別
		// J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
		if (!CapString.isEmpty(params.getString(
				GaapFlagEnum.IFRS.isEquals(gaapFlag)
						|| (GaapFlagEnum.EAS.isEquals(gaapFlag)) ? "conso"
						: "fssConso", null))) {
			// J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
			pageSetting
					.addSearchModeParameters(
							SearchMode.EQUALS,
							"conso",
							params.getString((GaapFlagEnum.IFRS
									.isEquals(gaapFlag) || GaapFlagEnum.EAS
									.isEquals(gaapFlag)) ? "conso" : "fssConso"));
		}

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS,
						CESConstant.DOC_STATUS,
						params.getString("fssDocStatus", "230"));
		if (params.getAsBoolean("fssPeriodType", false)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"periodType", PeriodTypeEnum.YEAR.getCode());
		}

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"f101a01as.authUnit", getBranch);
		pageSetting.addSearchModeParameters(SearchMode.IN,
				"f101a01as.authType",
				new String[] { DocAuthTypeEnum.MODIFY.getCode(),
						DocAuthTypeEnum.VIEW_TRANSFER.getCode() });

		if (!CapString.isEmpty(params.getString("fssSource", null))) {
			String fssSource = params.getString("fssSource");
			if (fssSource.indexOf("|") > 0) {
				String[] split = fssSource.split("\\" + "|");
				pageSetting.addSearchModeParameters(SearchMode.IN, "source",
						split);
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"source", fssSource);
			}
		}
		// 增加GAAP會計準則
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "gaapFlag",
				gaapFlag);
		Page<F101M01A> page = gridService.getF1010V01(pageSetting);
		// Page<Map<String, Object>> page = service1201.getFss(getBranch,
		// fssCustId, fssDupNo, pageSetting);

		// formatter
		Map<String, IFormatter> map = new HashMap<String, IFormatter>();
		map.put("custId", new CustIdFormatter());
		map.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name));
		map.put("approver", new UserNameFormatter(userservice));
		IFormatter myFmt = new IFormatter() {
			private static final long serialVersionUID = 1L;

			@SuppressWarnings("unchecked")
			@Override
			public String reformat(Object in) throws CapFormatException {
				return "1".equals(in) ? "V" : "";
			}
		};

		// 2013.03.07 Mike Add 整合IFRS財報引進 Start
		if (GaapFlagEnum.IFRS.isEquals(gaapFlag)) {
			map.put("conso", new CodeTypeFormatter(codeservice, "IFRSConso"));
		} else if (GaapFlagEnum.IFRS.isEquals(gaapFlag)) {
			// J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
			map.put("conso", new CodeTypeFormatter(codeservice, "IFRSConso"));
		} else {
			map.put("conso", myFmt);
		}
		// 2013.03.07 Mike Add 整合IFRS財報引進 End

		// map.put("conso", myFmt);
		map.put("inFlag", myFmt);
		map.put("publicFlag", myFmt);
		map.put("source", new CodeTypeFormatter(codeservice, "FssSource"));
		return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	}

	/**
	 * Mow模型評等Grid
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryMowTrust(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String thisOid = Util.nullToSpace(params.getString("thisOid"));
		L120S01A l120s01a = service1205.findL120s01aByOid(thisOid);
		String custId = "";
		String dupNo = "";
		if (l120s01a != null) {
			custId = Util.trim(l120s01a.getCustId());
			dupNo = Util.trim(l120s01a.getDupNo());
		}
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1205.getMowTrust(custId, dupNo,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢列印營運中心意見
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryPrintArea(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				"hqReceiveDate", "");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				UtilConstants.Field.目前編製行, UtilConstants.BankNo.授管處);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "areaChk", "3");
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120M01A.class,
				pageSetting);
		StringBuilder allCust = new StringBuilder();
		StringBuffer docName = new StringBuffer();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205M01Page.class);
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			allCust.setLength(0);
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo()).append(" ")
					.append(model.getCustName());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());
			model.setDocKind(this.getCaseType(model, pop, docName));
			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode()));
			} else {
				model.setDocStatus("");
			}
			model.setUpdater(this.getUserName(model.getUpdater()));
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(this.getUserName(model
						.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(Util.trim(model.getAreaAppraiser()));
			}
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(this.getUserName(model.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setApproveTime(model.getAreaSendInfo());
			}

		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得 案件類別名稱
	 * 
	 * @param model
	 *            簽報書主檔
	 * @param pop
	 *            語系檔
	 * @param temp
	 *            暫存的stringBuffer
	 * 
	 * @return
	 */
	private String getCaseType(L120M01A model, Properties pop, StringBuffer temp) {
		temp.setLength(0);
		String areaTitle = null;
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		// J-112-0JJJ_05097_B1001 Web e-Loan日本地區分行簽報書新增管理行授權內案件權限及相關修改
		String brNoType = branchService.getBranch(model.getCaseBrId())
				.getCountryType();

		if (UtilConstants.Casedoc.DocType.企金.equals(model.getDocType())) {
			if (UtilConstants.Casedoc.DocKind.授權內.equals(model.getDocKind())) {
				// L1205G.grid1=企金營運中心授權內
				// L1205G.grid1a=企金分行授權內
				if (UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(Util
						.trim(model.getAuthLvl()))) {
					temp.append(pop.getProperty("L1205G.grid1"));
				} else {
					if (UtilConstants.Casedoc.AuthLvl.總行授權內.equals(Util
							.trim(model.getAuthLvl()))) {
						// 企金總行授權內
						// J-112-0JJJ_05097_B1001 Web
						// e-Loan日本地區分行簽報書新增管理行授權內案件權限及相關修改
						if (UtilConstants.Country.日本.equals(brNoType)) {
							// L1205G.grid16=企金管理行授權內
							temp.append(pop.getProperty("L1205G.grid16"));
						} else {
							// L1205G.grid14=企金總行授權內
							temp.append(pop.getProperty("L1205G.grid14"));
						}
					} else {

						temp.append(pop.getProperty("L1205G.grid1a"));
					}

				}
			} else {
				areaTitle = queryAreaTitle(model);
				if (Util.isNotEmpty(areaTitle)) {
					// l120m01a.title0a=企金
					temp.append(pop.getProperty("l120m01a.title0a")).append(
							areaTitle);
				} else {
					temp.append(pop.getProperty("L1205G.grid2"));
				}
			}
		} else {
			if (UtilConstants.Casedoc.DocKind.授權內.equals(model.getDocKind())) {
				// L1205G.grid12=個金營運中心授權內
				// L1205G.grid12a=個金分行授權內
				if (UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(Util
						.trim(model.getAuthLvl()))) {
					temp.append(pop.getProperty("L1205G.grid12"));
				} else {

					if (UtilConstants.Casedoc.AuthLvl.總行授權內.equals(Util
							.trim(model.getAuthLvl()))) {
						// 個金總行授權內

						// J-112-0JJJ_05097_B1001 Web
						// e-Loan日本地區分行簽報書新增管理行授權內案件權限及相關修改
						if (UtilConstants.Country.日本.equals(brNoType)) {
							// L1205G.grid17=個金管理行授權內
							temp.append(pop.getProperty("L1205G.grid17"));
						} else {
							// L1205G.grid15=個金總行授權內
							temp.append(pop.getProperty("L1205G.grid15"));
						}
					} else {
						temp.append(pop.getProperty("L1205G.grid12a"));
					}

				}
			} else {
				areaTitle = queryAreaTitle(model);
				if (Util.isNotEmpty(areaTitle)) {
					// l120m01a.title0b=個金
					temp.append(pop.getProperty("l120m01a.title0b")).append(
							areaTitle);
				} else {
					temp.append(pop.getProperty("L1205G.grid13"));
				}
			}
		}
		// L1205G.grid9=一般
		// L1205G.grid10=其他
		// L1205G.grid11=陳復/陳述案
		temp.append("(");
		if (UtilConstants.Casedoc.DocCode.一般.equals(model.getDocCode())) {
			temp.append(pop.getProperty("L1205G.grid9"));
		} else if (UtilConstants.Casedoc.DocCode.其他.equals(model.getDocCode())) {
			temp.append(pop.getProperty("L1205G.grid10"));
		} else if (UtilConstants.Casedoc.DocCode.異常通報.equals(Util.trim(model
				.getDocCode()))) {
			// other.msg59=異常通報案件
			temp.append(pop2.getProperty("other.msg59"));
		} else if (UtilConstants.Casedoc.DocCode.團貸案件.equals(Util.trim(model
				.getDocCode()))) {
			// other.msg134=團貸
			temp.append(pop2.getProperty("other.msg134"));
		} else {
			temp.append(pop.getProperty("L1205G.grid11"));
		}
		temp.append(")");
		return temp.toString();
	}

	/**
	 * 取得國內屬營運中心制分行的標題名稱
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @return
	 * @throws CapException
	 */
	private String queryAreaTitle(L120M01A l120m01a) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch tBranch = branchService.getBranch((l120m01a != null) ? Util
				.trim(l120m01a.getCaseBrId()) : user.getUnitNo());
		String docKind = Util.trim(l120m01a.getDocKind());
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		if (tBranch != null) {
			String brnGroup = Util.trim(tBranch.getBrnGroup());
			if (UtilConstants.BankNo.中部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北一區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北二區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.中區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南區營運中心.equals(brnGroup)) {
				if (UtilConstants.Casedoc.DocKind.授權外.equals(docKind)) {
					/*
					 * 因為海外分行不屬於營運中心制，所以提醒第四階段，國內屬營運中心制分行時TITLE顯示會有差異
					 * 國內營運中心制分行，分行授權外案件會顯示營運中心授權外案件簽報書
					 */
					// other.msg131=營運中心授權外
					return pop.getProperty("other.msg131");
				}
			}
		}
		return null;
	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (Util.isEmpty(userId)) {
			return "";
		}
		String result = userservice.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL720m01a(ISearch pageSetting,
			PageParameters params) throws CapException {

		// 建立主要Search 條件
		// pageSetting.addOrderBy("patternNM");
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L720M01A.class,
				pageSetting);

		List<L720M01A> list = (List<L720M01A>) page.getContent();
		if (!list.isEmpty()) {
			for (int i = 0; i < list.size(); i++) {
				L720M01A model = list.get(i);
				StringBuilder fullUp = new StringBuilder();
				fullUp.append(getPerName(model.getUpdater())).append(" (")
						.append(TWNDate.toFullTW(model.getUpdateTime()))
						.append(")");
				model.setUpdater(fullUp.toString());
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * 
	 * @param id
	 *            使用者id
	 * @return 空值: 使用者id 非空值: 使用者名稱
	 */
	private String getPerName(String id) {
		return (!Util.isEmpty(userservice.getUserName(id)) ? userservice
				.getUserName(id) : id);
	}

	/**
	 * 將指定字串轉成 (字串 + " " + 字串對應名稱)格式
	 * 
	 * @param str
	 *            指定字串
	 * @return 轉換後格式
	 */
	public String withIdName(String str) {
		StringBuilder sb = new StringBuilder();
		sb.append(Util.addZeroWithValue(Util.trim(str), 6)).append(" ")
				.append(getPerName(Util.trim(str)));
		return sb.toString();
	}

	/**
	 * 查詢擔保品 MainId(by 分行、by 簽報書主要借款人、by 擔保品大類)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCmsMainId(ISearch pageSetting,
			PageParameters params) throws CapException {

		Page<Map<String, Object>> page = null;

		// Grid種類
		String type = Util.trim(params.getString("type"));

		if (type.equals("1")) {
			// 建立主要Search 條件
			String branchId = Util.trim(params.getString("branchId"));
			// 第三個參數為formatting
			page = service1205.getCmsMainId(branchId, pageSetting);
		} else if (type.equals("2")) {
			// 建立主要Search 條件
			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			// 第三個參數為formatting
			page = service1205.getCmsMainId2(mainId, pageSetting);
		} else if (type.equals("3")) {
			// 建立主要Search 條件
			String collTyp1 = Util.trim(params.getString("collTyp1"));
			// 第三個參數為formatting
			page = service1205.getCmsMainId3(collTyp1, pageSetting);
		}

		if (page == null) {
			// 查無資料
			return new CapMapGridResult(new ArrayList<Map<String, Object>>(), 0);
		}

		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			sb.append(Util.trim(map.get("custId"))).append(" ")
					.append(Util.trim(map.get("dupNo"))).append(" ")
					.append(Util.trim(map.get("custName")));
			map.put("collKind",
					codeservice.getDescOfCodeType("cms1090_collTyp1",
							Util.trim(map.get("collTyp1"))));
			map.put("custName", sb.toString());
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
			map.put("megaAmt", Util.isEmpty(map.get("megaAmt")) ? ""
					: NumConverter.delComma(Util.trim(map.get("megaAmt"))));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 清除擔保品Grid
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapGridResult beforeClearCMS(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID, ""));
		// 建立主要Search 條件
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.L120m01eDocType.擔保品估價報告書);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120M01E.class,
				pageSetting);

		// List<L120M01E> list = (List<L120M01E>) page.getContent();
		// if (!list.isEmpty()) {
		// for (int i = 0; i < list.size(); i++) {
		// L120M01E model = list.get(i);
		// StringBuilder fullUp = new StringBuilder();
		// fullUp.append(getPerName(model.getUpdater())).append(" (")
		// .append(TWNDate.toFullTW(model.getUpdateTime()))
		// .append(")");
		// model.setUpdater(fullUp.toString());
		// }
		// }
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢擔保品
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            <pre>
	 *            colltyp1 擔保品大類 
	 *            custId 客戶統編 
	 *            dupNo 重覆序號 
	 *            branch 分行代號
	 * </pre>
	 * @param parent
	 *            Component
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryCMS(ISearch pageSetting, PageParameters params) throws CapException {
		String colltyp1 = Util.trim(params.getString("collTyp1", ""));
		String custId = Util.trim(params.getString("cmsCustId", ""));
		String dupNo = Util.trim(params.getString("cmsDupNo", ""));
		String branch = Util.trim(params.getString("branchId", "005"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "collTyp1",
				colltyp1);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);
		pageSetting.addSearchModeParameters(
				SearchMode.IN,
				EloanConstants.DOC_STATUS,
				new String[] { CMSDocStatusEnum.分行_編製中.getCode(),
						CMSDocStatusEnum.分行_待覆核.getCode(),
						CMSDocStatusEnum.分行_已覆核.getCode(),
						CMSDocStatusEnum.分行_待設質.getCode(),
						CMSDocStatusEnum.分行_已設質.getCode(),
						CMSDocStatusEnum.分行_待塗銷.getCode(),
						CMSDocStatusEnum.聯行傳回.getCode(),
						CMSDocStatusEnum.代鑑價編製中.getCode(),
						CMSDocStatusEnum.代鑑價待覆核.getCode(),
						CMSDocStatusEnum.代鑑價已完成.getCode(),
						CMSDocStatusEnum.營運中心_編製中.getCode(),
						CMSDocStatusEnum.營運中心_待覆核.getCode(),
						CMSDocStatusEnum.營運中心_已覆核.getCode(),
						CMSDocStatusEnum.營運中心_已傳回.getCode(),
						CMSDocStatusEnum.營運中心_待收件.getCode(),
						CMSDocStatusEnum.營運中心_覆核待收件.getCode(),
						CMSDocStatusEnum.營運中心_覆核編制中.getCode(),
						CMSDocStatusEnum.營運中心_覆核待覆核.getCode(),
						CMSDocStatusEnum.營運中心_覆核已覆核.getCode(),
						CMSDocStatusEnum.營運中心_覆核已傳回.getCode(),
						CMSDocStatusEnum.待斷頭.getCode(),
						CMSDocStatusEnum.已斷頭.getCode(),
						CMSDocStatusEnum.補提.getCode(),
						CMSDocStatusEnum.擔保率不足.getCode(),
						CMSDocStatusEnum.授管處_編製中.getCode(),
						CMSDocStatusEnum.授管處_待覆核.getCode(),
						CMSDocStatusEnum.授管處_已覆核.getCode() });
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		// 限定只顯示海外授信案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
				UtilConstants.Casedoc.typCd.海外);
		Page<? extends GenericBean> page = lmsService.findPage(C100M01.class,
				pageSetting);
		List<C100M01> c100m01s = (List<C100M01>) page.getContent();
		for (C100M01 model : c100m01s) {
			model.setAppraiserName(userservice.getUserName(model.getAppraiser()));
		}
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("branch", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name));
		dataReformatter.put("collTyp1", new CodeTypeFormatter(codeservice,
				"lmsUseCms_collTyp1"));
		dataReformatter.put("docStatus", new I18NFormatter("status."));
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 設定授管處和營運中心 在已核准的顯示規則
	 * 
	 * @param pageSetting
	 * @param unitType
	 */
	private void setBy050Grid(ISearch pageSetting, String unitType) {
		if (BranchTypeEnum.營運中心.getCode().equals(unitType)) {
			// 已核准的營運中心只能看到授權外的或營運中心授權內

			// pageSetting.addSearchModeParameters(SearchMode.OR,
			// new SearchModeParameter(SearchMode.EQUALS, "authLvl",
			// UtilConstants.Casedoc.AuthLvl.營運中心授權內),
			// new SearchModeParameter(SearchMode.EQUALS, "docKind",
			// UtilConstants.Casedoc.DocKind.授權外));

			// J-111-0488_05097_B1001 Web e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
			// J-111-0443_05097_B1006 Web e-Loan企金授信開發授信BIS評估表
			pageSetting
					.addSearchModeParameters(
							SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"authLvl",
									UtilConstants.Casedoc.AuthLvl.營運中心授權內),

							SearchParameterUtil.getMultiOrParameters(

									SearchParameterUtil
											.getMultiAndParameters(
													new SearchModeParameter(
															SearchMode.AND,
															new SearchModeParameter(
																	SearchMode.EQUALS,
																	"authLvl",
																	UtilConstants.Casedoc.AuthLvl.分行授權內),
															new SearchModeParameter(
																	SearchMode.NOT_EQUALS,
																	"signNo",
																	"")),
													new SearchModeParameter(
															SearchMode.IS_NOT_NULL,
															"signNo", ""))

									, new SearchModeParameter(
											SearchMode.EQUALS, "docKind",
											UtilConstants.Casedoc.DocKind.授權外)));

		} else if (BranchTypeEnum.授管處.getCode().equals(unitType)) {
			// 已核准的授管處只能看到授權外的

			// J-104-0066-001 Web e-Loan授信系統授管處可以看到全行授權內、外簽報書
			// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docKind",
			// UtilConstants.Casedoc.DocKind.授權外);
		}
	}

	/**
	 * 篩選L120M01AGrid 外部的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120m01a2(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205M01Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		String unitType = user.getUnitType();
		String typCd = Util.trim(params.getString("typCd"));
		String docType = Util.trim(params.getString("docType"));
		String docKind = Util.trim(params.getString("docKind"));
		String docCode = Util.trim(params.getString("docCode"));
		String custName = Util.trim(params.getString("custName"));
		String updater = Util.trim(params.getString("updater"));
		String approveDateS = Util.nullToSpace(Util.trim(params
				.getString("approveDateS")));
		String approveDateE = Util.nullToSpace(Util.trim(params
				.getString("approveDateE")));
		String caseBrId = Util.nullToSpace(params.getString("caseBrId"));

		// 2012-09-06 黃建霖 begin
		String custId = Util.trim(params.getString("custId"));
		String cntrNo = Util.trim(params.getString("cntrNo"));
		String l140m01a_mainId = Util.trim(params.getString("l140m01a_mainId"));

		// J-109-0431_05097_B1001 Web e-Loan授信系統已核准受理案件篩選條件新增案件審核層級
		String caseLvl = Util.trim(params.getString("caseLvl"));

		// 全文檢索專用欄位**************************************************************
		String fxUserId = user.getUserId();
		String fxGroupId = user.getUnitNo();
		String fxCaseDateName = ""; // approveTime or caseDate
		String fxCaseDateS = "";
		String fxCaseDateE = "";
		String fxEndDateS = "";
		String fxEndDateE = "";
		String fxTypCd = "";
		String fxDocType = "";
		String fxDocKind = "";
		String fxDocCode = "";
		String fxUpdaterName = ""; // hqAppraiser areaAppraiser updater
		String fxUpdater = "";
		String fxCaseBrId = "";
		String fxCustId = "";
		String fxDocRslt = "";
		StringBuffer fxDocStatus = new StringBuffer("");
		String fxLnSubject = "";
		String fxRateText = "";
		String fxOtherCondition = "";
		String fxReportOther = "";
		String fxReportReason1 = "";
		String fxReportReason2 = "";
		String fxAreaOption = "";
		String fxCollateral = "";
		String fxCustName = "";
		String fxBusCode = "";
		// J-109-0431_05097_B1001 Web e-Loan授信系統已核准受理案件篩選條件新增案件審核層級
		String fxCaseLvl = "";
		// ************************************************************************

		// J-104-0066-001 Web e-Loan授信系統授管處可以看到全行授權內、外簽報書。
		// 去篩選簽報書統編、額度、MAINID 符合者
		List<String> l120m01a_maindId_list = lmsService
				.getL120M01AMainIdByFilterForm(params); // 額度明細表符合 統編、額度、MAINID

		if (l120m01a_maindId_list.size() > 20000) {
			if (Util.isNotEmpty(custName)) {
				// 筆數太多會掛掉
				// 戶名符合條件筆數太多l120m01a_maindId_list.size()，請增加篩選條件後再執行本作業。
				throw new CapMessageException("簽報書基本查詢條件符合筆數太多" + "("
						+ Util.trim(l120m01a_maindId_list.size()) + "筆)"
						+ "，請增加簽報書篩選條件後再執行本作業", getClass());

			}

		}

		// 之簽報書MAINID
		if (Util.equals(custId, "") && Util.equals(custName, "")) {
			if (l120m01a_maindId_list.size() > 0) {
				pageSetting.addSearchModeParameters(SearchMode.IN, "mainId",
						l120m01a_maindId_list);
			}
		} else {
			// 篩選條件有統編、戶名時
			if (l120m01a_maindId_list.size() > 0) {

				if (Util.isNotEmpty(custId) && Util.isNotEmpty(custName)) {
					pageSetting.addSearchModeParameters(SearchMode.OR,
							new SearchModeParameter(SearchMode.IN, "mainId",
									l120m01a_maindId_list),
							new SearchModeParameter(SearchMode.AND,
									new SearchModeParameter(SearchMode.EQUALS,
											"custId", custId),
									new SearchModeParameter(SearchMode.LIKE,
											"custName", custName + "%")));
				} else {
					if (Util.isNotEmpty(custId)) {
						pageSetting.addSearchModeParameters(SearchMode.OR,
								new SearchModeParameter(SearchMode.IN,
										"mainId", l120m01a_maindId_list),
								new SearchModeParameter(SearchMode.EQUALS,
										"custId", custId));
					}
					if (Util.isNotEmpty(custName)) {
						pageSetting.addSearchModeParameters(SearchMode.OR,
								new SearchModeParameter(SearchMode.IN,
										"mainId", l120m01a_maindId_list),
								new SearchModeParameter(SearchMode.LIKE,
										"custName", custName + "%"));
					}
				}

			} else {
				if (Util.isNotEmpty(custId)) {
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"custId", custId);
				}
				if (Util.isNotEmpty(custName)) {
					pageSetting.addSearchModeParameters(SearchMode.LIKE,
							"custName", custName + "%");
				}

			}
		}

		// 2012-09-06 黃建霖 end

		Date fromDate = null;
		Date endDate = null;

		if (!Util.isEmpty(Util.nullToSpace(params.getString("fromDate")))) {
			fromDate = Util.parseDate(Util.nullToSpace(params
					.getString("fromDate")));
		}
		if (!Util.isEmpty(Util.nullToSpace(params.getString("endDate")))) {
			endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate") + " 23:59:59"));
		}

		// Miller added at 2012/12/17
		boolean isReject = params.getBoolean("isReject");

		if (fromDate != null && endDate != null) {
			Object[] reason = { fromDate, endDate };

			if (docStatus.equals("03K|01K|02K|04K")) {
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"approveTime", reason);
				fxCaseDateName = "approveTime";

			} else {
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"caseDate", reason);

				// pageSetting.addSearchModeParameters(SearchMode.GREATER_THAN,
				// "caseDate", fromDateN);
				// pageSetting.addSearchModeParameters(SearchMode.LESS_THAN,
				// "caseDate", endDateN);

				fxCaseDateName = "caseDate";
			}
			fxCaseDateS = params.getString("fromDate");
			fxCaseDateE = params.getString("endDate");
			// if (docStatus.equals(CreditDocStatusEnum.海外_已核准.getCode())) {
			// pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
			// "approveTime", reason);
			// } else {
			// pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
			// "caseDate", reason);
			// }
		}
		if (!Util.isEmpty(caseBrId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
					caseBrId);
			fxCaseBrId = caseBrId;
		}

		// 篩選婉卻/變更格式選項 Miller added at 2012/12/17
		if (isReject) {
			// 3婉卻變更格式
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docRslt",
					"3");
			fxDocRslt = "3";
		}
		if (Util.isNotEmpty(typCd)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
					typCd);
			fxTypCd = typCd;
		}
		if (Util.isNotEmpty(docType)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
					docType);
			fxDocType = docType;
		}
		if (Util.isNotEmpty(docKind)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docKind",
					docKind);
			fxDocKind = docKind;
		}
		if (Util.isNotEmpty(docCode)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					docCode);
			fxDocCode = docCode;
		}

		// J-109-0431_05097_B1001 Web e-Loan授信系統已核准受理案件篩選條件新增案件審核層級
		if (Util.isNotEmpty(caseLvl)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseLvl",
					caseLvl);
			fxCaseLvl = caseLvl;
		}

		if (Util.isNotEmpty(updater)) {

			if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"hqAppraiser", updater);
				fxUpdaterName = "hqAppraiser";
			} else if (UtilConstants.BankNo.中區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.中部區域授信中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.北一區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.北二區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.南區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.南部區域授信中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(user.getUnitNo())) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"areaAppraiser", updater);
				fxUpdaterName = "areaAppraiser";
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"updater", updater);
				fxUpdaterName = "updater";
			}
			fxUpdater = updater;

		}
		if (!Util.isEmpty(approveDateS) && !Util.isEmpty(approveDateE)) {
			pageSetting.addSearchModeParameters(
					SearchMode.BETWEEN,
					"endDate",
					new Object[] { Util.parseDate(approveDateS),
							Util.parseDate(approveDateE) });

			fxEndDateS = approveDateS;
			fxEndDateE = approveDateE;
		}

		// UFO@********:協議案查詢條件
		if ((BranchTypeEnum.授管處.getCode().equals(unitType) || BranchTypeEnum.國金部
				.getCode().equals(unitType))
				&& Util.equals(docStatus, CreditDocStatusEnum.海外_已核准)) {
			// J-104-0066-001 Web e-Loan授信系統授管處可以看到全行授權內、外簽報書

		} else {
			NGFlagHelper.addSearchParamsAT918(pageSetting, user);
		}

		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}

		StringBuffer txDocStatus = null;

		switch (docStatusEnum) {
		case 海外_呈總行:

			// 當登錄分行為澳洲 ，分行呈總行文件狀態 後為提會待登錄 或提會帶覆核
			IBranch branchtype = branchService.getBranch(user.getUnitNo());
			if (UtilConstants.Country.澳洲.equals(branchtype.getCountryType())
					|| UtilConstants.Country.加拿大.equals(branchtype
							.getCountryType())) {
				String[] showDoc = new String[] {
						CreditDocStatusEnum.海外_總行待覆核.getCode(),
						CreditDocStatusEnum.海外_總行提會待登錄.getCode(),
						CreditDocStatusEnum.海外_總行提會待覆核.getCode() };
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						showDoc);

				/*
				 * SQL效能考量，不用IN， 改用=
				 * 
				 * txDocStatus = new StringBuffer(" (DOCSTATUS IN (");
				 * for(String tShowDoc : showDoc){
				 * txDocStatus.append("'"+tShowDoc+"',"); }
				 * txDocStatus.replace(txDocStatus.length()-1,
				 * txDocStatus.length(), "");//最後一個,去掉
				 * txDocStatus.append(") )");
				 * 
				 * if(Util.notEquals(fxDocStatus.toString(),"")){
				 * fxDocStatus.append(" AND "); fxDocStatus.append(txDocStatus);
				 * }else{ fxDocStatus.append(txDocStatus); }
				 */

				txDocStatus = new StringBuffer(" ( ");
				txDocStatus.append("   DOCSTATUS = '"
						+ CreditDocStatusEnum.海外_總行待覆核.getCode() + "' ");
				txDocStatus.append("OR DOCSTATUS = '"
						+ CreditDocStatusEnum.海外_總行提會待登錄.getCode() + "' ");
				txDocStatus.append("OR DOCSTATUS = '"
						+ CreditDocStatusEnum.海外_總行提會待覆核.getCode() + "' ");
				txDocStatus.append(" ) ");

				if (Util.notEquals(fxDocStatus.toString(), "")) {
					fxDocStatus.append(" AND ");
					fxDocStatus.append(txDocStatus);
				} else {
					fxDocStatus.append(txDocStatus);
				}

			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", CreditDocStatusEnum.海外_總行待覆核.getCode());

				txDocStatus = new StringBuffer(" (DOCSTATUS = '"
						+ CreditDocStatusEnum.海外_總行待覆核.getCode() + "')");

				if (Util.notEquals(fxDocStatus.toString(), "")) {
					fxDocStatus.append(" AND ");
					fxDocStatus.append(txDocStatus);
				} else {
					fxDocStatus.append(txDocStatus);
				}

			}
			break;
		case 海外_呈授管處:
			// 當狀態為海外_呈授管處時要變更搜尋條件要文件狀態為C或H結尾都要顯示，並且join授權檔
			pageSetting
					.addSearchModeParameters(SearchMode.OR,
							new SearchModeParameter(SearchMode.LIKE,
									"docStatus", "%H"),
							new SearchModeParameter(SearchMode.LIKE,
									"docStatus", "%C"));

			txDocStatus = new StringBuffer(
					" (DOCSTATUS LIKE '%H' OR DOCSTATUS LIKE '%C')");

			if (Util.notEquals(fxDocStatus.toString(), "")) {
				fxDocStatus.append(" AND ");
				fxDocStatus.append(txDocStatus);
			} else {
				fxDocStatus.append(txDocStatus);
			}
			// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
			// "docStatus", CreditDocStatusEnum.授管處_待陳復.getCode());

			break;
		case 營運中心_呈總處:
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "docStatus",
					"%H");

			txDocStatus = new StringBuffer(" (DOCSTATUS LIKE '%H' )");

			if (Util.notEquals(fxDocStatus.toString(), "")) {
				fxDocStatus.append(" AND ");
				fxDocStatus.append(txDocStatus);
			} else {
				fxDocStatus.append(txDocStatus);
			}

			break;

		case 營運中心_待更正:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"backUnit", "S"), new SearchModeParameter(
									SearchMode.EQUALS, "backUnit", "A")),
					new SearchModeParameter(SearchMode.EQUALS, "returnFromBH",
							"1"));

			break;

		case 授管處_待收案件:
			pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
					"hqReceiveDate", "");
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					UtilConstants.Field.目前編製行, UtilConstants.BankNo.授管處);
			break;
		case 授管處_已收案件:
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"hqReceiveDate", "");
			break;
		case 授管處_審查中:
			// 審查中在提授審會時不能不能出現此案件
			// pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
			// "hqMeetFlag", null);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);

			txDocStatus = new StringBuffer(" (DOCSTATUS = '" + docStatus + "')");

			if (Util.notEquals(fxDocStatus.toString(), "")) {
				fxDocStatus.append(" AND ");
				fxDocStatus.append(txDocStatus);
			} else {
				fxDocStatus.append(txDocStatus);
			}

			break;
		case 授管處_待更正:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"backUnit", "C"), new SearchModeParameter(
									SearchMode.EQUALS, "backUnit", "S")),
					new SearchModeParameter(SearchMode.EQUALS, "returnFromBH",
							"1"));
			break;
		case 海外_已核准:
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"docCode", UtilConstants.Casedoc.DocCode.陳復陳述案);
			// 如果登錄的是總行要多看到 泰國的提會待登錄、和泰國提會待覆核的案件
			if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
				// 已核准案件要排除陳覆陳述案，
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						new String[] { CreditDocStatusEnum.海外_已核准.getCode(),
								CreditDocStatusEnum.泰國_提會待登錄.getCode(),
								CreditDocStatusEnum.泰國_提會待覆核.getCode() });

				txDocStatus = new StringBuffer(" ( ");

				txDocStatus.append("   DOCSTATUS = '"
						+ CreditDocStatusEnum.海外_已核准.getCode() + "' ");
				txDocStatus.append("OR DOCSTATUS = '"
						+ CreditDocStatusEnum.泰國_提會待登錄.getCode() + "' ");
				txDocStatus.append("OR DOCSTATUS = '"
						+ CreditDocStatusEnum.泰國_提會待覆核.getCode() + "' ");

				txDocStatus.append(") ");

				if (Util.notEquals(fxDocStatus.toString(), "")) {
					fxDocStatus.append(" AND ");
					fxDocStatus.append(txDocStatus);
				} else {
					fxDocStatus.append(txDocStatus);
				}

			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", docStatus);

				txDocStatus = new StringBuffer(" (DOCSTATUS = '" + docStatus
						+ "')");

				if (Util.notEquals(fxDocStatus.toString(), "")) {
					fxDocStatus.append(" AND ");
					fxDocStatus.append(txDocStatus);
				} else {
					fxDocStatus.append(txDocStatus);
				}

			}
			// 2013/07/17,Rex,修改設定授管處和營運中心 在已核准的顯示規則
			this.setBy050Grid(pageSetting, unitType);
			break;
		case 海外_陳復案_陳述案:
			// 已核准 且 docCode = 3
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.海外_已核准.getCode());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					UtilConstants.Casedoc.DocCode.陳復陳述案);

			txDocStatus = new StringBuffer(" (DOCSTATUS = '"
					+ CreditDocStatusEnum.海外_已核准.getCode() + "')");

			if (Util.notEquals(fxDocStatus.toString(), "")) {
				fxDocStatus.append(" AND ");
				fxDocStatus.append(txDocStatus);
			} else {
				fxDocStatus.append(txDocStatus);
			}

			break;

		case 授管處_免批覆案件:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.海外_已核准.getCode());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					UtilConstants.Casedoc.DocCode.陳復陳述案);

			txDocStatus = new StringBuffer(" (DOCSTATUS = '"
					+ CreditDocStatusEnum.海外_已核准.getCode() + "')");

			break;
		case 營運中心_所有提會案件:
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"rptTitleArea1", "");
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"rptTitleArea1", "");
			break;
		default:
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);

			/*
			 * 
			 * txDocStatus = new StringBuffer(" (DOCSTATUS IN ("); for(String
			 * tShowDoc : _docStatus){ txDocStatus.append("'"+tShowDoc+"',"); }
			 * txDocStatus.replace(txDocStatus.length()-1, txDocStatus.length(),
			 * "");//最後一個,去掉 txDocStatus.append(") )");
			 */

			txDocStatus = new StringBuffer(" (");
			String fxDocStr = "";
			for (String tShowDoc : _docStatus) {
				txDocStatus.append(fxDocStr + " DOCSTATUS = '" + tShowDoc
						+ "' ");

				if (Util.equals(fxDocStr, "")) {
					fxDocStr = "OR";
				}
			}
			txDocStatus.append(")");

			if (Util.notEquals(fxDocStatus.toString(), "")) {
				fxDocStatus.append(" AND ");
				fxDocStatus.append(txDocStatus);
			} else {
				fxDocStatus.append(txDocStatus);
			}

			break;
		}

		if ((BranchTypeEnum.授管處.getCode().equals(unitType) || BranchTypeEnum.國金部
				.getCode().equals(unitType))
				&& Util.equals(docStatus, CreditDocStatusEnum.海外_已核准)) {

			// J-104-0066-001 Web e-Loan授信系統授管處可以看到全行授權內、外簽報書
		} else {

			// J-111-0058_05097_B1002 Web e-Loan授信額度批覆表總處核定批示顯示內容調整
			// 因為北區分行授權外案件直送授審處，導致北區營運中心看不到分行直送的案件，所以針對北區營運中心加上 (t0.areaChk =
			// '3' AND areaBrId = '931')之條件

			if (Util.equals(user.getUnitNo(), UtilConstants.BankNo.北一區營運中心)
					&& Util.equals(CreditDocStatusEnum.海外_已核准.getCode(),
							docStatus)) {

				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"l120a01a.authUnit", user.getUnitNo());

				// pageSetting
				// .addSearchModeParameters(
				// SearchMode.AND,
				// new SearchModeParameter(
				// SearchMode.NOT_EQUALS,
				// "l120a01a.authUnit",
				// user.getUnitNo() ),
				// new SearchModeParameter(
				// SearchMode.AND,
				// new SearchModeParameter(
				// SearchMode.EQUALS,
				// "areaChk",
				// UtilConstants.Casedoc.AreaChk.送審查),
				// new SearchModeParameter(
				// SearchMode.EQUALS,
				// "areaBrId", user
				// .getUnitNo())) );

				// pageSetting
				// .addSearchModeParameters(
				// SearchMode.OR,
				// new SearchModeParameter(SearchMode.EQUALS,
				// "l120a01a.authUnit", user.getUnitNo()),
				// new SearchModeParameter(
				// SearchMode.AND,
				// new SearchModeParameter(SearchMode.AND,
				// new SearchModeParameter(
				// SearchMode.EQUALS,
				// "l120a01a.authUnit",
				// "918"),
				// new SearchModeParameter(
				// SearchMode.NOT_EQUALS,
				// "l120a01a.authUnit",
				// user.getUnitNo())),
				// new SearchModeParameter(
				// SearchMode.AND,
				// new SearchModeParameter(
				// SearchMode.EQUALS,
				// "areaChk",
				// UtilConstants.Casedoc.AreaChk.送審查),
				// new SearchModeParameter(
				// SearchMode.EQUALS,
				// "areaBrId", user
				// .getUnitNo()))));

				// pageSetting.addSearchModeParameters(
				// SearchMode.OR,
				// new SearchModeParameter(SearchMode.AND,
				// new SearchModeParameter(SearchMode.EQUALS,
				// "l120a01a.authUnit", user.getUnitNo()),
				// new SearchModeParameter(SearchMode.AND,
				// new SearchModeParameter(
				// SearchMode.NOT_EQUALS,
				// "l120a01a.authUnit", user
				// .getUnitNo()),
				// new SearchModeParameter(
				// SearchMode.EQUALS, "areaBrId",
				// user.getUnitNo()))),
				// new SearchModeParameter(SearchMode.AND,
				// new SearchModeParameter(SearchMode.NOT_EQUALS,
				// "l120a01a.authUnit", user.getUnitNo()),
				// new SearchModeParameter(SearchMode.AND,
				// new SearchModeParameter(
				// SearchMode.NOT_EQUALS,
				// "l120a01a.authUnit", user
				// .getUnitNo()),
				// new SearchModeParameter(
				// SearchMode.EQUALS, "areaBrId",
				// user.getUnitNo()))));

			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"l120a01a.authUnit", user.getUnitNo());
			}

		}

		// pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
		// UtilConstants.Field.刪除時間, "");
		if (!"S".equals(user.getUnitType()) && !"A".equals(user.getUnitType())
				&& !"K".equals(user.getUnitType())) {
			// 限定只顯示海外授信案件
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
					UtilConstants.Casedoc.typCd.海外);
		}

		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);

		// 排除掉異常通報案件
		if (!UtilConstants.BankNo.授管處.equals(user.getUnitNo())
				&& !UtilConstants.BankNo.中區營運中心.equals(user.getUnitNo())
				&& !UtilConstants.BankNo.中部區域授信中心.equals(user.getUnitNo())
				&& !UtilConstants.BankNo.北一區營運中心.equals(user.getUnitNo())
				&& !UtilConstants.BankNo.北二區營運中心.equals(user.getUnitNo())
				&& !UtilConstants.BankNo.南區營運中心.equals(user.getUnitNo())
				&& !UtilConstants.BankNo.南部區域授信中心.equals(user.getUnitNo())
				&& !UtilConstants.BankNo.桃竹苗區營運中心.equals(user.getUnitNo())) {
			// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
			// "docCode", UtilConstants.Casedoc.DocCode.異常通報);
		} else {
			if (CreditDocStatusEnum.授管處_審查中.getCode().equals(docStatus)
					|| CreditDocStatusEnum.營運中心_審查中.getCode().equals(docStatus)) {
				// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
				// "docCode", UtilConstants.Casedoc.DocCode.異常通報);
			}
		}

		// ********************************************************************

		/*
		 * for(SearchModeParameter tSearch :
		 * pageSetting.getSearchModeParameters()){
		 * System.out.println((String)tSearch.getKey()); Object aa =
		 * tSearch.getValue(); if( aa instanceof String){
		 * System.out.println((String)aa); }else if(aa instanceof String[] ){
		 * 
		 * for(String s : (String[] )aa){ System.out.println((String)s); } } }
		 */

		String reQuery = params.getString("newQuery") == null ? "N" : params
				.getString("newQuery");

		Page<? extends GenericBean> page = null;

		String fxFlag = Util.trim(params.getString("fxFlag")); // 進階查詢為Y

		if (Util.equals(fxFlag, "Y")) {
			// 全文檢索
			if (reQuery.equals("Y")) {
				// 若是第一次Query(reQuery = "Y" ，由LMS1205V01Page.js來) 就要先CALL

				if (Util.isNotEmpty(custId)) {
					fxCustId = custId;
				}

				if (Util.isNotEmpty(custName)) {
					fxCustName = "%" + Util.trim(custName) + "%";
				}

				// 暫時不將docCode當篩選條件(有點複雜)，反正撈L120M01A時會再篩選一次

				fxDocCode = "";
				fxTypCd = "";

				// 全文檢索專用欄位
				fxLnSubject = Util.trim(params.getString("fxLnSubject"));
				fxRateText = Util.trim(params.getString("fxRateText"));
				fxOtherCondition = Util.trim(params
						.getString("fxOtherCondition"));
				fxReportOther = Util.trim(params.getString("fxReportOther"));
				fxReportReason1 = Util
						.trim(params.getString("fxReportReason1"));
				fxReportReason2 = Util
						.trim(params.getString("fxReportReason1"));
				fxAreaOption = Util.trim(params.getString("fxAreaOption"));
				fxCollateral = Util.trim(params.getString("fxCollateral"));
				fxBusCode = Util.trim(params.getString("fxBusCode"));

				fxLnSubject = Util.notEquals(fxLnSubject, "") ? "%"
						+ fxLnSubject + "%" : "";
				fxRateText = Util.notEquals(fxRateText, "") ? "%" + fxRateText
						+ "%" : "";
				fxOtherCondition = Util.notEquals(fxOtherCondition, "") ? "%"
						+ fxOtherCondition + "%" : "";
				fxReportOther = Util.notEquals(fxReportOther, "") ? "%"
						+ fxReportOther + "%" : "";
				fxReportReason1 = Util.notEquals(fxReportReason1, "") ? "%"
						+ fxReportReason1 + "%" : "";
				fxReportReason2 = Util.notEquals(fxReportReason1, "") ? "%"
						+ fxReportReason1 + "%" : "";
				fxAreaOption = Util.notEquals(fxAreaOption, "") ? "%"
						+ fxAreaOption + "%" : "";
				fxCollateral = Util.notEquals(fxCollateral, "") ? "%"
						+ fxCollateral + "%" : "";
				fxBusCode = Util.notEquals(fxBusCode, "") ? fxBusCode : "";

				// FOR TEST
				// fxCustName = "%統%";

				int delCount = service1205.delPreSearchResult(fxUserId);

				int tCount = service1205.execFullTextSearch(fxUserId,
						fxGroupId, fxCaseDateName, fxCaseDateS, fxCaseDateE,
						fxEndDateS, fxEndDateE, fxTypCd, fxDocType, fxDocKind,
						fxDocCode, fxUpdaterName, fxUpdater, fxCaseBrId,
						fxCustId, fxDocRslt, fxDocStatus.toString(),
						fxLnSubject, fxRateText, fxOtherCondition,
						fxReportOther, fxReportReason1, fxReportReason2,
						fxAreaOption, fxCollateral, fxCustName, fxBusCode,
						fxCaseLvl);
			}

			// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "notesUp",
			// userId);

			page = service1205.findL120m01aByL120m01atmp1UserId(pageSetting,
					fxUserId);

		} else {
			// 一般搜尋

			if (Util.isNotEmpty(custId)) {
				// J-104-0066-001 Web e-Loan授信系統授管處可以看到全行授權內、外簽報書。 移到
				// LMSServiceImpl.java getL120M01AMainIdByFilterForm 去做
				// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				// "custId", custId);
				fxCustId = custId;
			}

			if (Util.isNotEmpty(custName)) {
				// J-104-0066-001 Web e-Loan授信系統授管處可以看到全行授權內、外簽報書。 移到
				// LMSServiceImpl.java getL120M01AMainIdByFilterForm 去做
				// pageSetting.addSearchModeParameters(SearchMode.LIKE,
				// "custName", "%" + Util.trim(custName) + "%");
				fxCustName = "%" + Util.trim(custName) + "%";
			}

			page = service1205.findPage(L120M01A.class, pageSetting);
		}

		// ********************************************************************

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name)); // 分行名稱格式化
		dataReformatter.put("userid", new UserNameFormatter(userservice)); // 使用者名稱格式化

		for (int w = 0; w < page.getContent().size(); w++) {
			L120M01A model = (L120M01A) page.getContent().get(w);
			StringBuffer strB = new StringBuffer();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());
			strB.append(
					"1".equals(model.getDocKind()) ? pop
							.getProperty("L1205G.grid1") : pop
							.getProperty("L1205G.grid2")).append("(")
					.append(docCodeName(model.getDocCode())).append(")");
			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			if (Util.trim(model.getCaseNo()).indexOf("舊") != -1) {
				model.setGist("V");
			} else {
				model.setGist(UtilConstants.Mark.SPACE);
			}
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getAreaAppraiser())) ? userservice
						.getUserName(model.getAreaAppraiser()) : Util
						.trim(model.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(getPerName(Util.trim(model
						.getAreaAppraiser())));
			}
			// 和 method : queryL120m01a 內的邏輯相同
			// 「授管處負責經辦(hqAppraiser)」對應出員工姓名(不要出現null)
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getHqAppraiser())) ? userservice
						.getUserName(model.getHqAppraiser()) : Util.trim(model
						.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			if ("Y".equals(model.getReEstFlag())) {
				model.setReEstFlag(pop.getProperty("l120m01a.edit"));
			} else if ("A".equals(model.getReEstFlag())) {
				model.setReEstFlag(pop.getProperty("l120m01a.editA"));
			} else {
				model.setReEstFlag("");
			}
			// 針對授管處特殊分行已會簽的顯示
			// if (docStatus.equals("03K|01K|02K|04K")) {
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
			// }
		}

		// model.setDocStatus(CreditDocStatusEnum.getEnum(model.getDocStatus()));
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢文件 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryCreditRisk(ISearch pageSetting,
			PageParameters params) throws CapException {

		String mainId = CapString.trimNull(params.getString("mainId"));
		String custId = CapString.trimNull(params.getString("custId"));
		String dupNo = CapString.trimNull(params.getString("dupNo"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 取得資料
		Page<? extends GenericBean> page = service1205.findPage(L120S01M.class,
				pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		return result;
	}

	/**
	 * 查詢文件 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryDocView(ISearch pageSetting,
			PageParameters params) throws CapException {

		String mainId = CapString.trimNull(params.getString("mainId"));
		mainId = checkMainId(params, mainId);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "sysId", "LMS");
		// #1578 #330 附件分行問題
		// String branch = CapString.trimNull(params.getString("qryBranch"));
		// if(CapString.isEmpty(branch)){
		// //調整附件查詢，應該以C140M01A所屬分行為條件
		// // branch = MegaSSOSecurityContext.getUnitNo();
		// C140M01A meta = service.getC140M01AByMainId(mainId);
		// if(meta!=null)
		// branch = meta.getOwnBrId();
		// }
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branchId",
		// branch);
		String fieldId = CapString.trimNull(params.getString("fieldId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		// 取得資料
		Page<DocFile> page = docfileservice.readToGrid(pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		return result;
	}

	private String checkMainId(PageParameters params, String mainId) {
		if (CapString.isEmpty(mainId)
				&& !CapString.isEmpty(params.getString(CESConstant.MAIN_OID))) {
			C140M01A meta = service1205.getC140M01A(params
					.getString(CESConstant.MAIN_OID));
			if (meta != null) {
				mainId = meta.getMainId();
			}
		}
		return mainId;
	}

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01jCustTotal(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String MainId = Util.nullToSpace(params.getString("mainId"));
		String type = Util.nullToSpace(params.getString("type"));
		String custId2 = "9999999999";
		String dupNo2 = "9";

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "type", type);

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId2",
				custId2);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "dupNo2", dupNo2);

		pageSetting.addOrderBy("printSeq", false);

		Page<? extends GenericBean> page = service1205.findPage(L120M01J.class,
				pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		return result;
	}

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01jByCustId(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String MainId = Util.nullToSpace(params.getString("mainId"));
		String type = Util.nullToSpace(params.getString("type"));
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "type", type);

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		pageSetting.addOrderBy("itemSeq", false);
		Page<? extends GenericBean> page = service1205.findPage(L120M01J.class,
				pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		return result;
	}

	/**
	 * 查詢資信簡表 MainId(範圍)(徵信) J-107-0178_05097_B1001 Web
	 * e-loan案件簽報書相關文件之資信簡表增加借保人之資信簡表之勾選(能勾選跨頁之資料)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIdd(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = service1205.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1205.getCesMainIdd(caseBrId,
				mainid, mainid, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s11aMainCust(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String MainId = Util.nullToSpace(params.getString("mainId"));
		String custId2 = "9999999999";
		String dupNo2 = "9";

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId2",
				custId2);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "dupNo2", dupNo2);

		pageSetting.addOrderBy("printSeq", false);

		Page<? extends GenericBean> page = service1205.findPage(L120S11A.class,
				pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		return result;
	}

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s11aByCustId(ISearch pageSetting,
			PageParameters params) throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S06Panel.class);

		// 查這份文件的MinId
		String MainId = Util.nullToSpace(params.getString("mainId"));
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		pageSetting.addOrderBy("itemSeq", false);
		Page<? extends GenericBean> page = service1205.findPage(L120S11A.class,
				pageSetting);
		// CapGridResult result = new CapGridResult(page.getContent(),
		// page.getTotalRow());
		// return result;

		List<L120S11A> list = (List<L120S11A>) page.getContent();

		for (int i = 0; i < list.size(); i++) {
			L120S11A model = list.get(i);

			String relTypeStr = "";
			relTypeStr = Util.trim(pop.getProperty("L120S11A.relType_"
					+ Util.trim(model.getRelType())));

			model.setRelTypeStr(relTypeStr);

		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * J-110-0325_11557_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * 增加合併關係企業與當地有往來資料(L120S11A_LOC)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s11aLocMainCust(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String MainId = Util.nullToSpace(params.getString("mainId"));
		String custId2 = "9999999999";
		String dupNo2 = "9";

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId2",
				custId2);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "dupNo2", dupNo2);

		pageSetting.addOrderBy("printSeq", false);

		Page<? extends GenericBean> page = service1205.findPage(
				L120S11A_LOC.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		return result;
	}

	/**
	 * J-110-0325_11557_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * 增加合併關係企業與當地有往來資料(L120S11A_LOC)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s11aLocByCustId(ISearch pageSetting,
			PageParameters params) throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S06Panel.class);

		// 查這份文件的MinId
		String MainId = Util.nullToSpace(params.getString("mainId"));
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		pageSetting.addOrderBy("itemSeq", false);
		Page<? extends GenericBean> page = service1205.findPage(
				L120S11A_LOC.class, pageSetting);
		// CapGridResult result = new CapGridResult(page.getContent(),
		// page.getTotalRow());
		// return result;

		List<L120S11A_LOC> list = (List<L120S11A_LOC>) page.getContent();

		for (int i = 0; i < list.size(); i++) {
			L120S11A_LOC model = list.get(i);

			String relTypeStr = "";
			relTypeStr = Util.trim(pop.getProperty("L120S11A.relType_"
					+ Util.trim(model.getRelType())));

			model.setRelTypeStr(relTypeStr);

		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢L120S05BGrid 資料(集團)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL120s05f(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		String custId = Util.nullToSpace(params.getString("qCustId"));
		String dupNo = Util.nullToSpace(params.getString("qDupNo"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120S05F.class,
				pageSetting);
		List<L120S05F> list = (List<L120S05F>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S05F model = list.get(i);
			StringBuilder strBuf = new StringBuilder();
			strBuf.append(model.getCustId()).append(model.getDupNo())
					.append(" ").append(model.getCustName());
			model.setCustName(strBuf.toString());
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢L120S05BGrid 資料(集團)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL120s05e(ISearch pageSetting,
			PageParameters params) throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205S05Page06b.class);

		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		String custId = Util.nullToSpace(params.getString("qCustId"));
		String dupNo = Util.nullToSpace(params.getString("qDupNo"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1205.findPage(L120S05E.class,
				pageSetting);
		List<L120S05E> list = (List<L120S05E>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S05E model = list.get(i);
			StringBuilder strBuf = new StringBuilder();

			String grpFlag = Util.trim(model.getGrpFlag());

			if (Util.equals(grpFlag, "Y")) {
				strBuf.append(pop.getProperty("l120s05.grpKind1"));
			} else if (Util.equals(grpFlag, "A")) {
				strBuf.append(pop.getProperty("l120s05.grpKind2"));
			} else {
				strBuf.append(pop.getProperty("l120s05.grpKind3"));
			}

			model.setGrpFlag(strBuf.toString());
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryL120s01qList(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainid = Util.nullToSpace(params.getString("mainId"));
		Page<Map<String, Object>> page = lmsService.findListbyL120S01Aseq(
				"L120S01Q", mainid);
		List<Map<String, Object>> list = page.getContent();
		return new CapMapGridResult(list, page.getTotalRow());
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryL120s04dList(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainid = Util.nullToSpace(params.getString("mainId"));
		Page<Map<String, Object>> page = lmsService.findListbyL120S01Aseq(
				"L120S04D", mainid);
		List<Map<String, Object>> list = page.getContent();
		return new CapMapGridResult(list, page.getTotalRow());
	}

	/**
	 * L120S24A
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s24aList(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addOrderBy("cntrNo_s24a");// 用額度序號排序

		Page<? extends GenericBean> page = lmsService.findPage(L120S24A.class,
				pageSetting);

		String L120S24A_20250101_ON = lmsService.isL120s24a2025On();
		// 如果已經啟用，最新的版本就是20250101
		final String newestVersionDate = "Y".equals(L120S24A_20250101_ON) ? UtilConstants.L120s24aVersion.Ver_20250101
				: UtilConstants.L120s24aVersion.Ver_20220812;
		
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		// 幣別轉換
		// formatter.put("currentApplyCurr_s24a", new
		// CodeTypeFormatter(codeservice,
		// "Common_Currcy", CodeTypeFormatter.ShowTypeEnum.ValSpaceDesc));

		// 現請額度
		formatter.put("currentApplyAmt_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				res = LMSUtil.processL120s24aBigDecimal(
						meta.getCurrentApplyAmt_s24a(), true, false);
				return res;
			}
		});
		// CCF
		formatter.put("ccf_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				String onlyGuar_s24a = Util.trim(meta.getOnlyGuar_s24a());
				if ("N".equals(onlyGuar_s24a)) {
					res = "NA";
				} else {
					res = LMSUtil.processL120s24aBigDecimal(meta.getCcf_s24a(),
							false, true);
				}
				return res;
			}
		});
		// 純表外之信用相當額
		formatter.put("ccfAmt_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				String onlyGuar_s24a = Util.trim(meta.getOnlyGuar_s24a());
				if ("N".equals(onlyGuar_s24a)) {
					res = "NA";
				} else {
					res = LMSUtil.processL120s24aBigDecimal(
							meta.getCcfAmt_s24a(), true, false);
				}
				return res;
			}
		});
		// 特殊融資
		formatter.put("specialFinRiskType_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				String specialFinRiskType_s24a = Util.trim(meta.getSpecialFinRiskType_s24a());
				if ("0".equals(specialFinRiskType_s24a) || "".equals(specialFinRiskType_s24a)) {
					res = "N";
				} else {
					res = "Y";
				}
				return res;
			}
		});
		// 抵減前風險權數
		formatter.put("beforeDeductRW_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				res = LMSUtil.processL120s24aBigDecimal(
						meta.getBeforeDeductRW_s24a(), false, true);
				return res;
			}
		});
		// 合格擔保品抵減金額
		formatter.put("totalDisCollAmt_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				String hasQuaColl_s24a = Util.trim(meta.getHasQuaColl_s24a());
				if ("N".equals(hasQuaColl_s24a)) {
					res = "NA";
				} else if (Util.isEmpty(hasQuaColl_s24a)) {
					res = "";
				} else {
					res = LMSUtil.processL120s24aBigDecimal(
							meta.getTotalDisCollAmt_s24a(), true, false);
				}
				return res;
			}
		});
		// 合格擔保品抵減後暴險額
		formatter.put("calDisCollExposureAmt_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				res = LMSUtil.processL120s24aBigDecimal(
						meta.getCalDisCollExposureAmt_s24a(), true, false);
				return res;
			}
		});
		// 信保部位風險性資產
		formatter.put("calHasGutDeptRWA_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				String hasGutClass_s24a = Util.trim(meta.getHasGutClass_s24a());
				if ("N".equals(hasGutClass_s24a)) {
					res = "NA";
				} else {
					res = LMSUtil.processL120s24aBigDecimal(
							meta.getCalHasGutDeptRWA_s24a(), true, false);
				}
				return res;
			}
		});
		// 非信保部位風險性資產
		formatter.put("calNoGutRWA_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				res = LMSUtil.processL120s24aBigDecimal(
						meta.getCalNoGutRWA_s24a(), true, false);
				return res;
			}
		});
		// 抵減後風險性資產
		formatter.put("calDeductRWA_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				res = LMSUtil.processL120s24aBigDecimal(
						meta.getCalDeductRWA_s24a(), true, false);
				return res;
			}
		});
		// 抵減後風險權數
		formatter.put("calDeductRW_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				res = LMSUtil.processL120s24aBigDecimal(
						meta.getCalDeductRW_s24a(), false, true);
				return res;
			}
		});
		// 當前最新的版本，為了讓前端可以判斷要不要出現提示的字眼
		formatter.put("newestVersionDate", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				return newestVersionDate;
			}
		});
		
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}

	/**
	 * L120S24B
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s24bList(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));
		String refOid_s24b = Util.nullToSpace(params.getString("refOid_s24b"));

		Page<L120S24B> page = lmsService.findL120s24bByRefOid_s24b(mainId,
				refOid_s24b, pageSetting);
		Map<String, IFormatter> fmtMap = new HashMap<String, IFormatter>();
		fmtMap.put("quaColl_s24b", new CodeTypeFormatter(codeservice,
				"quaColl_s24b"));
		fmtMap.put("currSym_s24b", new CodeTypeFormatter(codeservice,
				"Common_YesNo"));
		// 擔保品價值
		fmtMap.put("collAmt_s24b", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24B meta = (L120S24B) in;
				res = LMSUtil.processL120s24aBigDecimal(meta.getCollAmt_s24b(),
						false, false);
				return res;
			}
		});
		// 折扣後擔保品價值
		fmtMap.put("disCollAmt_s24b", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24B meta = (L120S24B) in;
				res = LMSUtil.processL120s24aBigDecimal(
						meta.getDisCollAmt_s24b(), false, false);
				return res;
			}
		});
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), fmtMap);

		return result;
	}

	/**
	 * 查詢借款人說明->1.中央政府/央行名單
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s24aCentralGov(ISearch pageSetting,
			PageParameters params) throws CapException {
		Page<Map<String, Object>> page = lmsService
				.queryL120s24aCentralGov(pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢借款人說明->2.非營利國營事業名單
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s24aNonPro(ISearch pageSetting,
			PageParameters params) throws CapException {
		Page<Map<String, Object>> page = lmsService
				.queryL120s24aNonPro(pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * J-110-0382 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s21aDistinct(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.setDistinct(true);
		pageSetting.setDistinctColumn(new String[] { EloanConstants.MAIN_ID,
				"cntrNoCo_s21a", "currCo_s21a", "factAmtCo_s21a" });
		pageSetting.addOrderBy("cntrNoCo_s21a", false);
		pageSetting.addOrderBy("currCo_s21a", false);
		pageSetting.addOrderBy("factAmtCo_s21a", false);
		Page<? extends GenericBean> page = lmsLgdService.findPage(
				L120S21A.class, pageSetting);
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("co_s21a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,##0.##");
				String co_s21a = "";
				L120S21A meta = (L120S21A) in;
				co_s21a = Util.nullToSpace(meta.getCurrCo_s21a())
						+ (meta.getFactAmtCo_s21a() == null ? "" : df
								.format(meta.getFactAmtCo_s21a()));

				return co_s21a;
			}
		});

		List<L120S21A> l120s21as = (List<L120S21A>) page.getContent();

		return new CapGridResult(page.getContent(), page.getTotalRow(),
				formatter);
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s21a(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String cntrNoCo_s21a = Util.nullToSpace(params
				.getString("cntrNoCo_s21a"));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "cntrNoCo_s21a",
				cntrNoCo_s21a);
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("cntrNoCo_s21a", false);
		pageSetting.addOrderBy("cntrNo_s21a", false);
		Page<? extends GenericBean> page = lmsLgdService.findPage(
				L120S21A.class, pageSetting);
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("currentApply_s21a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,##0.##");
				String currentApply_s21a = "";
				L120S21A meta = (L120S21A) in;
				currentApply_s21a = Util.nullToSpace(meta
						.getCurrentApplyCurr_s21a())
						+ (meta.getCurrentApplyAmt_s21a() == null ? "" : df
								.format(meta.getCurrentApplyAmt_s21a()));
				return currentApply_s21a;
			}
		});
		formatter.put("bl_s21a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,##0.##");
				String bl_s21a = "";
				L120S21A meta = (L120S21A) in;
				bl_s21a = Util.nullToSpace(meta.getBlCurr_s21a())
						+ (meta.getBlAmt_s21a() == null ? "" : df.format(meta
								.getBlAmt_s21a()));
				return bl_s21a;
			}
		});
		formatter.put("rcv_s21a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,##0.##");
				String rcv_s21a = "";
				L120S21A meta = (L120S21A) in;
				rcv_s21a = Util.nullToSpace(meta.getRcvCurr_s21a())
						+ (meta.getRcvInt_s21a() == null ? "" : df.format(meta
								.getRcvInt_s21a()));
				return rcv_s21a;
			}
		});

		formatter.put("reUse_s21a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,##0.##");
				String reUse_s21a = "";
				L120S21A meta = (L120S21A) in;
				reUse_s21a = Util.equals(Util.trim(meta.getReUse_s21a()), "1") ? "N"
						: (Util.equals(Util.trim(meta.getReUse_s21a()), "2") ? "Y"
								: "");
				return reUse_s21a;
			}
		});

		List<L120S21A> l120s21as = (List<L120S21A>) page.getContent();

		return new CapGridResult(page.getContent(), page.getTotalRow(),
				formatter);
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s21b(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"hasCntrDoc_s21b", "Y");
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("custId_s21b", false);
		pageSetting.addOrderBy("dupNo_s21b", false);
		pageSetting.addOrderBy("cntrNo_s21b", false);
		Page<? extends GenericBean> page = lmsLgdService.findPage(
				L120S21B.class, pageSetting);
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("custId_s21b", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21B meta = (L120S21B) in;
				return meta.getCustId_s21b() + meta.getDupNo_s21b();
			}
		});

		// J-110-0485_05097_B1004 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		formatter.put("bussType_s21b", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21B meta = (L120S21B) in;
				return codeservice.getDescOfCodeType("LGD_BussType",
						Util.trim(meta.getBussType_s21b()));

			}
		});

		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		formatter.put("collateralRecoveryCms", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21B l120s21b = (L120S21B) in;
				// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
				return (l120s21b.getCollateralRecoveryCms() == null ? "-"
						: NumConverter.addComma(l120s21b
								.getCollateralRecoveryCms().toPlainString()));
			}
		});

		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		formatter.put("collateralRecoveryOth", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21B l120s21b = (L120S21B) in;
				// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
				return (l120s21b.getCollateralRecoveryOth() == null ? "-"
						: NumConverter.addComma(l120s21b
								.getCollateralRecoveryOth().toPlainString()));
			}
		});

		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		formatter.put("creditRecovery", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21B l120s21b = (L120S21B) in;
				// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
				return (l120s21b.getCreditRecovery() == null ? "-"
						: NumConverter.addComma(l120s21b.getCreditRecovery()
								.toPlainString()));
			}
		});

		// J-111-0083_05097_B1002 Web
		// e-Loan企金授信額度明細表新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
		formatter.put("isStandAloneAuth_s21b", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21B meta = (L120S21B) in;
				return Util.isEmpty(meta.getIsStandAloneAuth_s21b()) ? ""
						: Util.trim(meta.getIsStandAloneAuth_s21b())
								+ "."
								+ codeservice.getDescOfCodeType(
										"lms140_isStandAloneAuth",
										Util.trim(meta
												.getIsStandAloneAuth_s21b()));

			}
		});

		List<L120S21B> l120s21bs = (List<L120S21B>) page.getContent();

		return new CapGridResult(page.getContent(), page.getTotalRow(),
				formatter);
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryL120s21b_1(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"hasCntrDoc_s21b", "Y");
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("custId_s21b", false);
		pageSetting.addOrderBy("dupNo_s21b", false);

		Page<? extends GenericBean> page = lmsLgdService.findPage(
				L120S21B.class, pageSetting);
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("custId_s21b", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21B meta = (L120S21B) in;
				return meta.getCustId_s21b() + meta.getDupNo_s21b();
			}
		});

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		List<L120S21B> l120s21bs = (List<L120S21B>) page.getContent();
		String gCustId = "";
		for (L120S21B l120s21b : l120s21bs) {
			Map<String, Object> row = new HashMap<String, Object>();
			String custId_s21b = Util.trim(l120s21b.getCustId_s21b());
			if (Util.notEquals(gCustId, custId_s21b)) {
				gCustId = custId_s21b;
				row.put("custId_s21b", custId_s21b);
				row.put("custLgd",
						l120s21b.getCustLgd() == null ? null
								: new NumericFormatter("##.##")
										.reformat(l120s21b.getCustLgd()));
				list.add(row);
			}

		}
		// return new CapGridResult(page.getContent(), page.getTotalRow(),
		// formatter);
		Page<Map<String, Object>> pages = LMSUtil.setPageMap(list, pageSetting);
		return new CapMapGridResult(pages.getContent(), list.size());

	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s21c(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String cntrNo_s21c = Util.nullToSpace(params.getString("cntrNo_s21c"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "cntrNo_s21c",
				cntrNo_s21c);

		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("collType_s21c", false);
		pageSetting.addOrderBy("colKind_s21c", false);
		Page<? extends GenericBean> page = lmsLgdService.findPage(
				L120S21C.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("collType_s21c", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21C l120s21c = (L120S21C) in;
				return codeservice.getDescOfCodeType("LGD_CollType",
						Util.trim(l120s21c.getCollType_s21c()));
			}
		});

		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		formatter.put("colCurr_s21c", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21C l120s21c = (L120S21C) in;
				// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
				return (Util.equals(l120s21c.getColKind_s21c(), "999901") ? "-"
						: l120s21c.getColCurr_s21c());
			}
		});

		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		formatter.put("colTimeValue_s21c", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21C l120s21c = (L120S21C) in;
				// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
				return (Util.equals(l120s21c.getColKind_s21c(), "999901") ? "-"
						: l120s21c.getColTimeValue_s21c() == null ? ""
								: NumConverter
										.addComma(l120s21c
												.getColTimeValue_s21c()
												.toPlainString()));
			}
		});

		// J-110-0485_05097_B1008 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		formatter.put("colRecoveryTwd_s21c", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21C l120s21c = (L120S21C) in;
				// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則

				String returnVal = "";
				if (Util.equals(l120s21c.getColKind_s21c(), "050300")) {
					returnVal = "N.A.";
				} else if (Util.equals(l120s21c.getColKind_s21c(), "999901")) {
					returnVal = "-";
				} else {
					returnVal = l120s21c.getColRecoveryTwd_s21c() == null ? "0"
							: NumConverter.addComma(l120s21c
									.getColRecoveryTwd_s21c().toPlainString());
				}

				return returnVal;
			}
		});

		// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
		// 前順位設定金額
		formatter.put("colPreRgstAmt_s21c", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21C l120s21c = (L120S21C) in;
				String colKind_s21c = Util.trim(l120s21c.getColKind_s21c());
				return (lmsLgdService.hideRgstInfoForL120s21c(colKind_s21c) ? ""
						: l120s21c.getColPreRgstAmt_s21c() == null ? "0"
								: NumConverter.addComma(l120s21c
										.getColPreRgstAmt_s21c()
										.toPlainString()));
			}
		});

		// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
		// 擔保品設定金額
		formatter.put("colRgstAmt_s21c", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21C l120s21c = (L120S21C) in;
				String colKind_s21c = Util.trim(l120s21c.getColKind_s21c());
				return (lmsLgdService.hideRgstInfoForL120s21c(colKind_s21c) ? ""
						: l120s21c.getColRgstAmt_s21c() == null ? "0"
								: NumConverter.addComma(l120s21c
										.getColRgstAmt_s21c().toPlainString()));
			}
		});

		List<L120S21C> l120s21cs = (List<L120S21C>) page.getContent();

		// J-110-0485_05097_B1009 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		for (L120S21C l120s21c : l120s21cs) {
			// J-110-0485_05097_B1009 Web
			// e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
			BigDecimal colShareRate_s21c = l120s21c.getColShareRate_s21c();
			if (colShareRate_s21c == null) {
				if (Util.equals(Util.trim(l120s21c.getColCoUseFlag_s21c()), "N")) {
					if (l120s21c.getColPreRgstAmt_s21c() == null
							|| BigDecimal.ZERO.compareTo(l120s21c
									.getColPreRgstAmt_s21c()) == 0) {
						colShareRate_s21c = new BigDecimal(100);
					}
				}
			}

			l120s21c.setColShareRate_s21c(colShareRate_s21c);

		}

		return new CapGridResult(page.getContent(), page.getTotalRow(),
				formatter);
	}

	/**
	 * BIS
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s25aList(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("bisCustId_s25a", false);
		pageSetting.addOrderBy("bisDupNo_s25a", false);
		pageSetting.addOrderBy("bisCntrNo_s25a", false);
		pageSetting.addOrderBy("oid", false);

		Page<? extends GenericBean> page = service1205.findPage(L120S25A.class,
				pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

		formatter.put("bisSheetItem", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				Map<String, String> proPertyMap = codeservice
						.findByCodeType("bisSheetItem");
				L120S25A l120s25a = (L120S25A) in;
				String bisSheetItem = Util.nullToSpace(l120s25a
						.getBisSheetItem());
				return Util.trim(proPertyMap.get(bisSheetItem));
			}
		});

		formatter.put("bisCcf", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S25A l120s25a = (L120S25A) in;
				String bisSheetItem = Util.nullToSpace(l120s25a
						.getBisSheetItem());
				BigDecimal bisCcf = l120s25a.getBisCcf();
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,###.#####");
				return Util.equals(bisSheetItem, "Y") ? df.format(bisCcf) : "";
			}
		});

		formatter.put("bisRiskAdjReturn", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S25A l120s25a = (L120S25A) in;

				BigDecimal bisRiskAdjReturn = l120s25a.getBisRiskAdjReturn();
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,###.#####");
				return bisRiskAdjReturn == null ? "N.A." : df
						.format(bisRiskAdjReturn);
			}
		});

		formatter.put("bisRiskAdjReturn_1", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S25A l120s25a = (L120S25A) in;

				BigDecimal bisRiskAdjReturn_1 = l120s25a
						.getBisRiskAdjReturn_1();
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,###.#####");
				return bisRiskAdjReturn_1 == null ? "N.A." : df
						.format(bisRiskAdjReturn_1);
			}
		});

		// J-111-0443_05097_B1005 Web e-Loan企金授信開發授信BIS評估表
		formatter.put("bisRorwa", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S25A l120s25a = (L120S25A) in;

				BigDecimal bisRorwa = l120s25a.getBisRorwa();
				BigDecimal rItemD = (l120s25a.getBisRItemD() == null ? BigDecimal.ZERO
						: l120s25a.getBisRItemD()); // %
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,###.#####");
				return rItemD.compareTo(BigDecimal.ZERO) != 0 ? (bisRorwa == null ? ""
						: df.format(bisRorwa))
						: "N.A.";
			}
		});

		formatter.put("bisRorwa_1", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S25A l120s25a = (L120S25A) in;

				BigDecimal bisRorwa_1 = l120s25a.getBisRorwa_1();
				BigDecimal rItemD = (l120s25a.getBisRItemD() == null ? BigDecimal.ZERO
						: l120s25a.getBisRItemD()); // %
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,###.#####");
				return rItemD.compareTo(BigDecimal.ZERO) != 0 ? (bisRorwa_1 == null ? ""
						: df.format(bisRorwa_1))
						: "N.A.";
			}
		});

		// J-111-0443_05097_B1005 Web e-Loan企金授信開發授信BIS評估表
		formatter.put("bisImpactNum", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S25A l120s25a = (L120S25A) in;

				BigDecimal bisImpactNum = l120s25a.getBisImpactNum();
				BigDecimal rItemD = (l120s25a.getBisRItemD() == null ? BigDecimal.ZERO
						: l120s25a.getBisRItemD()); // %
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,###.#####");
				return rItemD.compareTo(BigDecimal.ZERO) != 0 ? (bisImpactNum == null ? ""
						: df.format(bisImpactNum))
						: "N.A.";
			}
		});

		List<L120S25A> list = (List<L120S25A>) page.getContent();

		return new CapGridResult(list, page.getTotalRow(), formatter);
	}
}