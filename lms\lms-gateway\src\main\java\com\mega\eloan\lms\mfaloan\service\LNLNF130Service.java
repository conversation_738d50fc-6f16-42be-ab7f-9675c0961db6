/* 
 *LNLNF130Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import java.util.List;

import com.mega.eloan.lms.mfaloan.bean.LNF130;

/**
 * <pre>
 * LN.LNF130
 * </pre>
 * 
 * @since 2012/11/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/7,REX,new
 *          </ul>
 */
public interface LNLNF130Service {

	/**
	 * 查詢
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @param loanNo
	 *            放款帳號
	 * @return
	 */
	public List<LNF130> findBy<PERSON><PERSON>(String custId, String dupNo, String cntrNo,
			String loanNo);
	
	public List<LNF130> findByCntrNo(String cntrNo);

}
