package com.mega.eloan.lms.rpt.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;

import javax.annotation.Resource;

import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.NumberFormats;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisRatetblService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.rpt.service.LMS9570Service;
import com.mega.sso.service.BranchService;

/**
 * <pre>
 * 共同行銷維護作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class LMS9570ServiceImpl extends AbstractCapService implements LMS9570Service {

	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	SysParameterService sysparamService;

	@Resource
	MisRatetblService rateService;
	
	@Override
	public void setHeaderContent(WritableSheet sheet, Map<String, Integer> headerMap, Properties prop) throws WriteException{
		
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat header = null;
		WritableCellFormat cellFormat = LMSUtil.setCellFormat(header, font,
				Alignment.CENTRE, true, false);

		WritableCellFormat cellFormat_aloan = LMSUtil.setCellFormat(header, font,
				Alignment.CENTRE, true, false);
		if(true){
			cellFormat_aloan.setBackground(jxl.format.Colour.YELLOW);
		}

		int colIndex = 0;
		int rowIndex = 0;
		for(String map : headerMap.keySet()){
			if(!map.contains("ALOAN")){
				this.setCellsFormat(sheet, headerMap.get(map), prop.getProperty(map), colIndex++, rowIndex, cellFormat);
			}
			else{
				this.setCellsFormat(sheet, headerMap.get(map), prop.getProperty(map), colIndex++, rowIndex, cellFormat_aloan);
			}
		}
	}
	
	@Override
	public void setHeaderTotal(WritableSheet sheet, Map<String, Integer> headerMap, Properties prop, int rowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}

		int colIndex = 0;
		for(String header : headerMap.keySet()){
			this.setCellsFormat(sheet, headerMap.get(header), prop.getProperty(header), colIndex++, rowIndex, cellFormat);
		}
	}
	
	@Override
	public List<Map<String, Object>> getStatisticsData(){
		
		String applyTs = this.sysparamService.getParamValue("APPLY_DATE_LABOUR_BAILOUT4");
		
		List<Map<String, Object>> eloanList = this.eloandbBASEService.getEloanDataForLaborReliefLoanSummaryReport(applyTs);
		List<Map<String, Object>> aloanList = this.misdbBASEService.getAppropriationDataForLaborReliefLoanSummaryReport();
		Map<String, Map<String, Object>> aloanMap = new HashMap<String, Map<String, Object>>();
		
		for(Map<String, Object> m : aloanList){
			String branchNo = CapString.trimNull(m.get("OWNBRID"));
			aloanMap.put(branchNo, m);
		}
		
		for(Map<String, Object> m : eloanList){
			String branchNo = CapString.trimNull(m.get("BRNO"));
			Map<String, Object> aMap = aloanMap.get(branchNo);
			if(aloanMap.get(branchNo) != null){
				m.put("LNF_CNT", aMap.get("LNF_CNT"));
				m.put("LNF_AMT", aMap.get("LNF_AMT"));
			}
		}
		
		return eloanList;
	}
	
	@Override
	public int setBodyContent(WritableSheet sheet, List<Map<String, Object>> dataList) throws RowsExceededException, WriteException{
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		int colIndex = 0;
		int rowIndex = 1;
		for(Map<String, Object> map : dataList){
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("BRNGROUPDESC")), cellFormat));//分區
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("BRNO")), cellFormat));//分行代碼
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("BRNAME")), cellFormat));//分行名稱
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("BR_CATE")), cellFormat));//組別
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "NUMBER_OF_COLLATERAL_TO_CREDIT_INSURANCE"), cellFormat));//擔保品送信保件數
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "AMOUNT_COLLATERAL_TO_CREDIT_INSURANCE"), cellFormat));//擔保品送信保金額
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "NUMBER_OF_ACCEPTANCE"), cellFormat));//受理件數
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "AMOUNT_ACCEPTED"), cellFormat));//受理金額
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "NUMBER_OF_APPROVALS"), cellFormat));//核准件數
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "APPROVED_AMOUNT"), cellFormat));//核准金額
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "LNF_CNT"), cellFormat));//撥款件數
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "LNF_AMT"), cellFormat));//撥款金額
			colIndex = 0;
			rowIndex++;
		}
		
		return rowIndex;
	}
	
	@Override
	public int setBodyContentOfDetailReport(WritableSheet sheet, List<Map<String, Object>> dataList) throws RowsExceededException, WriteException{
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		int colIndex = 0;
		int rowIndex = 1;
		for(Map<String, Object> map : dataList){
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CASEBRID")), cellFormat));//分行代碼
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("BRNAME")), cellFormat));//分行名稱
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CUSTID")), cellFormat));//客戶統編
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("DUPNO")), cellFormat));//客戶重複序號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CUSTNAME")), cellFormat));//姓名
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "CURRENTAPPLYAMT"), cellFormat));//申請額度
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CASEDATE")), cellFormat));//簽案日期
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ENDDATE")), cellFormat));//核定日期
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("GRTNO")), cellFormat));//信保基金保證案號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CNTRNO")), cellFormat));//額度序號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("LOAN_DATE")), cellFormat));//撥款日期
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("APPLY_DATE")), cellFormat));//線上申請日
			colIndex = 0;
			rowIndex++;
		}
		
		return rowIndex;
	}
	
	private void setCellsFormat(WritableSheet sheet, int width, String content, int colIndex, int rowIndex, WritableCellFormat cellFormat) throws RowsExceededException, WriteException{
		sheet.setColumnView(colIndex, width);//設定欄寬
		sheet.addCell(new Label(colIndex, rowIndex, content, cellFormat));
	}
	
	@Override
	public List<Map<String, Object>> getDetailReportData(){
		
		String applyTs = this.sysparamService.getParamValue("APPLY_DATE_LABOUR_BAILOUT4");
		
		List<Map<String, Object>> sendCashList = new ArrayList<Map<String, Object>>();
		int size = -1;
		String custId = "";
		while(size == -1 || size == 10000){
			List<Map<String, Object>> list = this.misdbBASEService.getAppropriationDateForLaborReliefLoanDetailReport(applyTs, custId);
			sendCashList.addAll(list);
			size = list.size();
			custId = !list.isEmpty() ? String.valueOf(list.get(size-1).get("CUST_ID")) : custId;
		}
		
		Map<String, Map<String, Object>> sendCashMap = new HashMap<String, Map<String, Object>>();
		for(Map<String, Object> m : sendCashList){
			custId = String.valueOf(m.get("CUST_ID"));
			sendCashMap.put(custId, m);
		}
		
		List<Map<String, Object>> appDataList = new ArrayList<Map<String, Object>>();
		size = -1;
		custId = "";
		while(size == -1 || size == 10000){
			List<Map<String, Object>> list = this.eloandbBASEService.getOnlineApplicationDateForLaborReliefLoanDetailReportData(applyTs, custId);
			appDataList.addAll(list);
			size = list.size();
			custId = !list.isEmpty() ? String.valueOf(list.get(size-1).get("CUSTID")) : custId ;
		}
		
		Map<String, Map<String, Object>> appDateMap = new HashMap<String, Map<String, Object>>();
		for(Map<String, Object> m : appDataList){
			custId = String.valueOf(m.get("CUSTID"));
			appDateMap.put(custId, m);
		}
		
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		size = -1;
		custId = "";
		String dupNo = "";
		while(size == -1 || size == 10000){
			List<Map<String, Object>> list = this.eloandbBASEService.getEloanDataForLaborReliefLoanDetailReport(applyTs, custId + dupNo);
			dataList.addAll(list);
			size = list.size();
			if(!list.isEmpty()){
				custId = String.valueOf(list.get(size-1).get("CUSTID"));
				dupNo = String.valueOf(list.get(size-1).get("DUPNO"));
			}
		}
		
		for(Map<String, Object> dataMap : dataList){
			
			custId = String.valueOf(dataMap.get("CUSTID"));
			dupNo = String.valueOf(dataMap.get("DUPNO"));
			
			Map<String, Object> sMap = sendCashMap.get(custId + dupNo);
			if(null != sMap){
				dataMap.put("LOAN_DATE", sMap.get("LOAN_DATE"));
			}
			
			Map<String, Object> aMap = appDateMap.get(custId);
			if(null != aMap){
				//線上申請日
				dataMap.put("APPLY_DATE", CapDate.formatDateFromF1ToF2(CapString.trimNull(aMap.get("APPLYTS")), "yyyy-MM-dd", "yyyy-MM-dd"));
				appDateMap.remove(custId);
			}
		}
		
		//將剩餘線上申請紓困貸款資料, 加至報表最後面
		for(String id : appDateMap.keySet()){
			Map<String, Object> m = appDateMap.get(id);
			m.put("APPLY_DATE", CapDate.formatDateFromF1ToF2(CapString.trimNull(m.get("APPLYTS")), "yyyy-MM-dd", "yyyy-MM-dd"));
			BigDecimal applyQuota = LMSUtil.nullToZeroBigDecimal(m.get("CURRENTAPPLYAMT")).multiply(new BigDecimal(10000));
			m.put("CURRENTAPPLYAMT", applyQuota);
			dataList.add(appDateMap.get(id));
		}
		
		return dataList;
	}

	// J-110-0142 特定金錢信託案件量統計報表 
	@Override
	public int setCLS180R42ContentOfDetail(WritableSheet sheet, List<Map<String, Object>> dataList, List<Map<String, Object>> dataList2) throws RowsExceededException, WriteException{
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat header = null;
		WritableCellFormat cellFormat = LMSUtil.setCellFormat(header, font,
				Alignment.CENTRE, true, false);
		WritableCellFormat amtTWDFormat = new WritableCellFormat(font, NumberFormats.FORMAT1);
		amtTWDFormat.setVerticalAlignment(VerticalAlignment.TOP);
		amtTWDFormat.setBorder(Border.ALL, BorderLineStyle.THIN);

//		String cntrno, docType, currentApplyCur, docstatus, loanNo,  intRate, lnAmt1st;
		String originApplyCurr, currentApplyCurr;
		BigDecimal lnAmt_1st, currentApplyAmt, originApplyAmt, loanBal, rate, rate6;
		// 取得 上個月最後一個營業日各幣別收盤匯率
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.MONTH, -1);
		String[] ROCDateStrAry = TWNDate.toTW(calendar.getTime()).split("/");
		String ROCDateYMStr = ROCDateStrAry[0] + ROCDateStrAry[1];
		Map<String, Object> exRateMap = rateService.findExRateOfLastWorkingDayOfPreviousMonth(ROCDateYMStr);
		
		int colIndex = 0;
		int rowIndex = 1;
		for(Map<String, Object> map : dataList){
			originApplyCurr = CapString.trimNull(map.get("ORIGINAPPLYCURR"));
			currentApplyCurr = CapString.trimNull(map.get("CURRENTAPPLYCURR"));
			originApplyAmt = (BigDecimal) map.get("ORIGINAPPLYAMT");
			currentApplyAmt = (BigDecimal) map.get("CURRENTAPPLYAMT");
			loanBal = (BigDecimal) map.get("LOANBAL");
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CUSTID")), cellFormat));// 借款人統編
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CUSTNAME")), cellFormat));// 借款人姓名
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CASEDATE")), cellFormat));// 簽案日期
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ENDDATE")), cellFormat));// 核准日期
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("DOCTYPE")), cellFormat));// 企/個金案件
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CASENO")), cellFormat));// 簽報書案號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CNTRNO")), cellFormat));// 額度序號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("OWNBRID")), cellFormat));// 額度所屬分行別
			sheet.addCell(new Label(colIndex++, rowIndex, originApplyCurr, cellFormat));// 最初核貸額度幣別
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, CrsUtil.parseBigDecimal(NumConverter.addComma(
					originApplyAmt.setScale(0,BigDecimal.ROUND_HALF_UP))).doubleValue(), amtTWDFormat));// 最初核貸額度
			BigDecimal originApplyAmtTWD = (Util.isNotEmpty(originApplyCurr)) ? 
					originApplyAmt.multiply(Util.parseBigDecimal(exRateMap.get(originApplyCurr))) : originApplyAmt;
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, CrsUtil.parseBigDecimal(NumConverter.addComma(
					originApplyAmtTWD.setScale(0,BigDecimal.ROUND_HALF_UP))).doubleValue(), amtTWDFormat));// 等值台幣額度
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(currentApplyCurr), cellFormat));// 現請額度幣別
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, CrsUtil.parseBigDecimal(NumConverter.addComma(
					currentApplyAmt.setScale(0,BigDecimal.ROUND_HALF_UP))).doubleValue(), amtTWDFormat));// 現請額度
			BigDecimal currentApplyAmtTWD = (Util.isNotEmpty(currentApplyAmt)) ? 
					currentApplyAmt.multiply(Util.parseBigDecimal(exRateMap.get(currentApplyCurr))) : currentApplyAmt;
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, CrsUtil.parseBigDecimal(NumConverter.addComma(
					currentApplyAmtTWD.setScale(0,BigDecimal.ROUND_HALF_UP))).doubleValue(), amtTWDFormat));// 等值台幣額度
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("PROPERTYDESC")), cellFormat));// 性質

			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("DOCSTATUS")), cellFormat));// 動審表
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, CrsUtil.parseBigDecimal(NumConverter.addComma(
					loanBal)).doubleValue(), amtTWDFormat));// 授信餘額-折台幣(有被折成台幣嗎？)
			BigDecimal loanBalTWD = (Util.isNotEmpty(currentApplyAmt)) ? 
					loanBal.multiply(Util.parseBigDecimal(exRateMap.get(currentApplyCurr))) : loanBal;
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, CrsUtil.parseBigDecimal(NumConverter.addComma(
					loanBalTWD.setScale(0,BigDecimal.ROUND_HALF_UP))).doubleValue(), amtTWDFormat));// 授信餘額(台幣)
			// 取ALOAN放款帳號、利率、首次動撥金額
			dataList2 = misdbBASEService.queryCLS180R42Data2(Util.trim(MapUtils.getObject(map, "CNTRNO")));
			if (dataList2 != null && !dataList2.isEmpty()) {
				for(int k=1;k<=dataList2.size();k++){
			        Map<String,Object> map2 = dataList2.get(k-1);
			        if (k!=1){
			        	sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
			        	sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
			        	sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
			        	sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
			        	sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
			        	sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
			        	sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
			        	sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
			        	sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
						sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
						sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
						sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
						sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
						sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
					}
					else{
						sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map2.get("LNF020_SWFT")), cellFormat));// aloan現請額度幣別
						currentApplyAmt=(BigDecimal) map2.get("LNF020_FACT_AMT");
						sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, Util.isNotEmpty(currentApplyAmt) ? 
								CrsUtil.parseBigDecimal(NumConverter.addComma(currentApplyAmt.setScale(0,BigDecimal.ROUND_HALF_UP))).doubleValue() :  
									BigDecimal.ZERO.setScale(0,BigDecimal.ROUND_HALF_UP).doubleValue(), amtTWDFormat));// aloan現請額度
					}
					sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map2.get("LNF030_LOAN_NO")), cellFormat));// 放款帳號
					rate = (BigDecimal) map2.get("LNF154_RATE");
					rate6 = (BigDecimal) map2.get("LNF030_INT_RATE_6");			
					if ((Util.parseToBigDecimal(map2.get("LNF154_RATE"))).compareTo(BigDecimal.ZERO)!=0) {
						sheet.addCell(new Label(colIndex++, rowIndex, Util.isNotEmpty(rate) ? CapString.trimNull(rate.stripTrailingZeros().toPlainString()) : null, cellFormat));// 放款利率(匯集檔)
					} else {
						sheet.addCell(new Label(colIndex++, rowIndex, Util.isNotEmpty(rate6) ? CapString.trimNull(rate6.stripTrailingZeros().toPlainString()) : null, cellFormat));// 簽約利率
					} 
					sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map2.get("LNF030_SWFT")), cellFormat));// 撥款幣別
					lnAmt_1st=(BigDecimal) map2.get("LNF030_1ST_LN_AMT");
					sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, Util.isNotEmpty(lnAmt_1st) ? 
							CrsUtil.parseBigDecimal(NumConverter.addComma(lnAmt_1st.setScale(0,BigDecimal.ROUND_HALF_UP))).doubleValue() : 
								BigDecimal.ZERO.setScale(0,BigDecimal.ROUND_HALF_UP).doubleValue(), amtTWDFormat));// 首撥金額
					sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map2.get("LNF020_BEG_DATE")), cellFormat));// 動用起日
					sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map2.get("LNF020_END_DATE")), cellFormat));// 動用迄日

					String cancelDate = Util.isNotEmpty(CapString.trimNull(map2.get("LNF020_CANCEL_DATE"))) ? CapString.trimNull(map2.get("LNF020_CANCEL_DATE")) : CapString.trimNull(map2.get("LNF030_CANCEL_DATE"));
					sheet.addCell(new Label(colIndex++, rowIndex, cancelDate, cellFormat));// 銷戶日

			        if (k!=dataList2.size()){
			        	colIndex = 0;
						rowIndex++;
					}
				}
			} else {
				sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
				sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
				sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
				sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
				sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
				sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
				sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
				sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
				sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));
			}
			colIndex = 0;
			rowIndex++;
		}
		//J-111-0395增加資料來源註解
		WritableCellFormat cellFormat_aloan = LMSUtil.setCellFormat(header, font,
				Alignment.CENTRE, true, false);
		if(true){
			cellFormat_aloan.setBackground(jxl.format.Colour.YELLOW);
		}
		sheet.mergeCells(0, rowIndex, 1, rowIndex);
		sheet.addCell(new Label(0, rowIndex, CapString.trimNull("註解：黃底資料來源為aLoan"), cellFormat_aloan));
		rowIndex++;
		
		return rowIndex;
	}
	
	@Override
	public Map<String, Object> getCLS180R42DetailData(List<Map<String, Object>> dataList2){
		
		String cntrno, docType, currentApplyCur, docstatus;
		BigDecimal loanBal, loanBal_old, currentAoolyAmt, currentAoolyAmt_old;
		int docstatusCnt;
		Map<String, BigDecimal> loanBalMap = new HashMap();
		Map<String, BigDecimal> clamtMap = new HashMap();

		if (dataList2 != null && !dataList2.isEmpty()) {
			for (Map<String, Object> map : dataList2) {
				
				// 依額度序號取授信餘額(折台幣)
				cntrno = Util.trim(MapUtils.getObject(map, "CNTRNO"));	
				loanBal = misdbBASEService.queryLNF022getLOANBAL(cntrno);
				map.put("LOANBAL",loanBal.setScale(0,BigDecimal.ROUND_HALF_UP));
				
				// 取動審表狀態
				docstatusCnt = misdbBASEService.queryCLS180R42Data1(cntrno);
				if (docstatusCnt > 0){
					docstatus = "V";
				} else {
					docstatus = "查無資料";
				}
				map.put("DOCSTATUS",docstatus);
				
				// 企金/消金
				docType = Util.trim(MapUtils.getObject(map, "DOCTYPE"));	
				// 企金
				if (("1").equals(docType)){
					map.put("DOCTYPE","企金");
				} else if (("2").equals(docType)) {// 消金
					map.put("DOCTYPE","消金");
				}
				
				// 各幣別累計加總額度
				currentApplyCur = Util.trim(MapUtils.getObject(map, "CURRENTAPPLYCURR"));
				currentAoolyAmt = Util.parseToBigDecimal(map.get("CURRENTAPPLYAMT")); 
				if (clamtMap.containsKey(currentApplyCur)) {
					currentAoolyAmt_old = Util.parseToBigDecimal(clamtMap.get(currentApplyCur)); 
					currentAoolyAmt = currentAoolyAmt_old.add(currentAoolyAmt);
					clamtMap.put(currentApplyCur, currentAoolyAmt.setScale(0,BigDecimal.ROUND_HALF_UP));
				} else {
					clamtMap.put(currentApplyCur, currentAoolyAmt.setScale(0,BigDecimal.ROUND_HALF_UP));
				}
				// 各幣別累計加總授信餘額			
				currentAoolyAmt = Util.parseToBigDecimal(map.get("CURRENTAPPLYAMT"));
				if (loanBalMap.containsKey(currentApplyCur)) {
					loanBal_old = Util.parseToBigDecimal(loanBalMap.get(currentApplyCur));
					loanBal = loanBal_old.add(loanBal);
					loanBalMap.put(currentApplyCur, loanBal.setScale(0,BigDecimal.ROUND_HALF_UP));
				} else {
					loanBalMap.put(currentApplyCur, loanBal.setScale(0,BigDecimal.ROUND_HALF_UP));
				}

			}
		}
			
		Map<String, Object> listMap = new HashMap();
		listMap.put("dataList", dataList2);
		listMap.put("loanBalMap", loanBalMap);
		listMap.put("clamtMap", clamtMap);
		
		return listMap;
	}
	
	@Override
	public int setCLS180R42TotalOfDetail(WritableSheet sheet, Map<String, BigDecimal> loanBalMap, Map<String, BigDecimal> clamtMap, int rowIndex) throws RowsExceededException, WriteException{
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		WritableCellFormat amtTWDFormat = new WritableCellFormat(font, NumberFormats.FORMAT1);
		amtTWDFormat.setVerticalAlignment(VerticalAlignment.TOP);
		amtTWDFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		int colIndex = 0;
		for (Entry<String, BigDecimal> entry : clamtMap.entrySet()) {
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(""), cellFormat));// 空白
            sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(entry.getKey()), cellFormat));// 幣別
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, CrsUtil.parseBigDecimal(NumConverter.addComma(
					entry.getValue())).doubleValue(), amtTWDFormat));// 累計加總額度
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, CrsUtil.parseBigDecimal(NumConverter.addComma(
					loanBalMap.get(entry.getKey()))).doubleValue(), amtTWDFormat));// 累計加總授信餘額
			colIndex = 0;
			rowIndex++;
		}
		
		return rowIndex;
	}
}
