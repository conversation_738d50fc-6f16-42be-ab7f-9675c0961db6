/* 
 * L120S16BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S16BDao;
import com.mega.eloan.lms.model.L120S06B;
import com.mega.eloan.lms.model.L120S16B;

/** 主要申請敘作內容明細檔 **/
@Repository
public class L120S16BDaoImpl extends LMSJpaDao<L120S16B, String> implements
		L120S16BDao {

	@Override
	public L120S16B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S16B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S16B> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L120S16B findByUniqueKey(String mainId, String type, String custId, String dupNo,
			String cntrNo, String itemType) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		if (itemType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemType",
					itemType);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L120S16B> findByIndex01(String mainId, String custId,
			String dupNo, String cntrNo, String type, String itemType) {
		ISearch search = createSearchTemplete();
		List<L120S16B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		if (itemType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemType",
					itemType);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S16B> findByIndex02(String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		List<L120S16B> list = null;
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S16B> findByIndex03(String cntrNo) {
		ISearch search = createSearchTemplete();
		List<L120S16B> list = null;
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S16B> findByCustIdDupId(String custId, String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L120S16B> list = createQuery(L120S16B.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L120S16B> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", CntrNo);
		search.addOrderBy("cntrNo");
		List<L120S16B> list = createQuery(L120S16B.class, search)
				.getResultList();
		return list;
	}

	@Override
	public int delModel(String mainId) {
		Query query = getEntityManager().createNamedQuery("L120S16B.delModel");
		query.setParameter("MAINID", mainId); // 設置參數
		return query.executeUpdate();
	}

	@Override
	public List<L120S16B> findByMainIdCustIdCntrNo(String mainId,
			String custId, String dupNo, String cntrNo) {
		ISearch search = createSearchTemplete();
		List<L120S16B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

}