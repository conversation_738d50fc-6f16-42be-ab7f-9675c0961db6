var initDfd = initDfd || $.Deferred();
initDfd.done(function(json){  
    showdataUseItemData();
    doChecked();
    
    function doChecked(){
        $("input[type=radio][name=dataUseFlag][value='" + json.dataUseFlag + "']").prop("checked", true);
    }
    
    
    function showdataUseItemData(){
        var obj = CommonAPI.loadCombos(["megacompany"]);
        var data = {
            width: "50%",
            border: "none",
            value: json.dataUseItem,
            size: 1,
            fn: function(){
                checkAll($(this));
            },
            item: obj.megacompany
        };
        $("#dataUseItem").setItems(data);
        checkAll($("#dataUseItem"));
    }
    
    function checkAll(obj){
       var $checkBox = $("input[type=checkbox][name=dataUseItem][value!=0]");
        if (obj.val() == 0) {
            var chk0 = $("input[type=checkbox][name=dataUseItem][value='" + DOMPurify.sanitize(obj.val()) + "']").is(":checked");
            if (chk0) {
                $checkBox.prop("checked", false);
                if ($(obj).prop("checked")) {
                    $checkBox.prop("disabled", true);
                }
                else {
                    $checkBox.prop("disabled", false);
                }
                
            }else{
				$checkBox.prop("disabled", false);
			}
            
        }
    }
});
