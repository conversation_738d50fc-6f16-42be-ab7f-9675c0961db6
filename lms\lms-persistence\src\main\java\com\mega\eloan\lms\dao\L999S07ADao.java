/* 
 * L999S07ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L999S02A;
import com.mega.eloan.lms.model.L999S07A;

/** 授信約定書檔 **/
public interface L999S07ADao extends IGenericDao<L999S07A> {

	L999S07A findByOid(String oid);
	
	L999S07A findByMainId(String mainId);
}