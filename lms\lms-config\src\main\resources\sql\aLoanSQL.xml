<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="
http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-2.0.xsd">

	<util:map id="aLoanSql" map-class="java.util.HashMap" key-type="java.lang.String" >
		 <!-- ########## -->
    <!-- 授信共用 -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 徵信相關(from CES.xxxx) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 擔保品相關(from CMS.xxxx) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 企金授信(簽報書) -->
    <!-- 個金授信(簽報書) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 沿用徵信交易 -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 企金授信(額度明細表) -->
    <!-- 個金授信(額度明細表) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 企金授信(動審表) -->
    <!-- 個金授信(動審表) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 企金授信(其他) -->
    <!-- 個金授信(其他) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 企金覆審(覆審名單) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 企金覆審(覆審報告表) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 個金覆審(覆審名單) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 個金覆審(覆審報告表) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 稽核工作底稿 -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 管理報表 -->
    <!-- ########## -->
		<entry key="ELF339.selectByBRNO">
			<value>SELECT * FROM MIS.ELF339 WHERE ELF339_BRNO=?</value>
		</entry>
		<entry key="RATETBL.latestRate">
			<value>SELECT A.CURR AS CURR,A.ENDRATE AS ENDRATE FROM MIS.RATETBL A WHERE DATAYMD = '0000000'</value>
		</entry>
		<entry key="MISSTAFF.selectByDateAndCurr">
			<value><![CDATA[SELECT A.CURR AS CURR, A.ENDRATE AS ENDRATE FROM MIS.RATETBL A WHERE A.DATAYMD = ? and A.CURR = ?]]></value>
		</entry>

	</util:map>

</beans>
