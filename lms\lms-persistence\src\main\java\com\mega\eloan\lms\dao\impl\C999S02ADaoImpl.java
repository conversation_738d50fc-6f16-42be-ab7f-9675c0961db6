/* 
 * C999S02ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C999S02ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C999S02A;

/** 個金約據書增補條款內容檔 **/
@Repository
public class C999S02ADaoImpl extends LMSJpaDao<C999S02A, String>
	implements C999S02ADao {

	@Override
	public C999S02A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C999S02A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C999S02A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C999S02A findByUniqueKey(String mainId, Integer seq, String type){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C999S02A> findByIndex01(String mainId, Integer seq, String type){
		ISearch search = createSearchTemplete();
		List<C999S02A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}