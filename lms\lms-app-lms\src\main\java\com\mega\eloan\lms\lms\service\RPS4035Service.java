/* 
 * RPS4035Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

/**
 * <pre>
 * 婉卻資料查詢
 * </pre>
 * 
 * @since 2012/5/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/5/22,REX,new
 *          </ul>
 */
public interface RPS4035Service extends ICapService {

	/**
	 * 本行婉卻記錄
	 * 
	 * @param custId
	 *            客戶統編
	 * 
	 * @return 本行婉卻紀錄同一客戶統編不同重覆序號筆數
	 * @throws ParseException
	 */
	List<Map<String, Object>> getLnunIdByCustIdCount(String custId);

	/**
	 * 本行婉卻記錄
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 本行婉卻記錄
	 * @throws ParseException
	 */
	List<Map<String, Object>> getLnunIdByCustId(String custId, String dupNo)
			throws ParseException;

	/**
	 * 金控婉卻記錄
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 金控婉卻記錄
	 */
	List<Map<String, Object>> getLnunId02ByCustId(String custId, String dupNo);

	/**
	 * 本行婉卻記錄
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param search
	 *            搜尋設定
	 * @return 本行婉卻記錄
	 * @throws ParseException
	 */
	Page<Map<String, Object>> getLnunIdByCustId(String custId, String dupNo,
			ISearch search) throws ParseException;

	/**
	 * 金控婉卻記錄
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param search
	 *            搜尋設定
	 * @return 金控婉卻記錄
	 */
	Page<Map<String, Object>> getLnunId02ByCustId(String custId, String dupNo,
			ISearch search);
}