package com.mega.eloan.lms.dc.bean;

import java.io.Serializable;

import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * 借款收付彙計數明細檔
 * 
 * <AUTHOR>
 */
public class L140M01KBean extends BaseBean implements Serializable {

	private static final long serialVersionUID = 1L;

	/** 借款收付彙計數（付）－幣別 */
	private String cpCurr = null;
	/** 借款收付彙計數（付）－金額 */
	private String collectPay = null;
	/** 借款收付彙計數（收）－幣別 */
	private String caCurr = null;
	/** 借款收付彙計數（收）－金額 */
	private String collectAccept = null;

	/**
	 * @return 借款收付彙計數（付）－幣別
	 */
	public String getCpCurr() {
		return cpCurr;
	}

	/**
	 * @param cpCurr
	 *            借款收付彙計數（付）－幣別
	 */
	public void setCpCurr(String cpCurr) {
		this.cpCurr = cpCurr;
	}

	/**
	 * @return 借款收付彙計數（付）－金額
	 */
	public String getCollectPay() {
		return collectPay;
	}

	/**
	 * @param collectPay
	 *            借款收付彙計數（付）－金額
	 */
	public void setCollectPay(String collectPay) {
		this.collectPay = collectPay;
	}

	/**
	 * @return 借款收付彙計數（收）－幣別
	 */
	public String getCaCurr() {
		return caCurr;
	}

	/**
	 * @param caCurr
	 *            借款收付彙計數（收）－幣別
	 */
	public void setCaCurr(String caCurr) {
		this.caCurr = caCurr;
	}

	/**
	 * @return 借款收付彙計數（收）－金額
	 */
	public String getCollectAccept() {
		return collectAccept;
	}

	/**
	 * @param collectAccept
	 *            借款收付彙計數（收）－金額
	 */
	public void setCollectAccept(String collectAccept) {
		this.collectAccept = collectAccept;
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer();

		// Table 標準 開頭.......................................
		sb.append(Util.nullToSpace(this.getOid()))
				.append(TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getMainId())).append(
				TextDefine.FILE_DELIM);

		sb.append(Util.nullToSpace(this.getCpCurr())).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getCollectPay())).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getCaCurr())).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getCollectAccept())).append(
				TextDefine.FILE_DELIM);

		// Table 標準 節尾.......................................
		sb.append(Util.nullToSpace(this.getCreator())).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getCreateTime())).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getUpdater())).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getUpdateTime()));

		return sb.toString();
	}
}
