package com.mega.eloan.lms.lrs.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.lms.base.common.RO412;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.eloan.lms.model.L180M01C;
import com.mega.eloan.lms.model.L180M01D;
import com.mega.eloan.lms.model.L181M01A;
import com.mega.eloan.lms.model.L181M01B;

public interface LMS1810Service extends ICapService {

	public L181M01A findInProcessData(String branch, String[] docStatusArr,
			String ctlType, String ownBrId);

	public L181M01A findInProcessData(String branch, String custId,
			String dupNo, String[] docStatusArr, String ctlType, String ownBrId);

	/**
	 * mode 同 gfnCTL_Caculate_DueDate的mode{mode=1 產生本月覆審名單, mode = 2 產生未覆審名單,
	 * mode = 3 維護覆審名單}
	 * <ul>
	 * <li>回傳第1個參數 mainCoFlag{空白 =>沒有共用, DBU =>minChkDate 為採用 DBU最小覆審日, OBU
	 * =>minChkDate 為採用 OBU最小覆審日}</li>
	 * <li>回傳第2個參數 minChkDate</li>
	 * <li>回傳第3個參數 mainChkID</li>
	 * </ul>
	 */
	public String[] gfnGetNEW_DBUOBU_MINCHKDATE(String mode, String brNo,
			RO412 ro412, Map<String, String> elf412_DBUCOID,
			Map<String, String> elf412_OBUCOID);

	public List<String> gfnCTL_Get_ELF411_CONTRACT(String branch,
			String custId, String dupNo, Date elf412_newDate);

	/**
	 * @param exMode
	 * <br/>
	 *            exMode = "1" ' 手動程式 <br/>
	 *            exMode = "2" ' 背景代理程式執行 <br/>
	 *            exMode = "A" ' 產生報表 'J-100-0241 e-Loan授信管理之覆審名單檔
	 *            '產生覆審名單'中，增列針對特定或全部中心轄下分行截至某月份止依規『必定要』辦理覆審之客戶名單及戶數之功能。 <br/>
	 *            當為 A 覆審筆數為 l180m01b_list.size(), 不覆審註記筆數為NckdFlag in
	 *            {"1","2","3","4","5","9"}
	 * @param instIdList
	 * @param branch
	 * @param baseDate
	 * @param userId
	 * @param unitNo
	 * @param unitType
	 * @param createBy
	 * @param l180m01b_list
	 * @return
	 */
	public boolean gfnGenCTLList_ByBrno(boolean is_exMode_A,
			List<String> instIdList, String branch, Date baseDate,
			String userId, String unitNo, String unitType, String createBy,
			List<L180M01B> l180m01b_list);

	public L180M01B gfnDB2CTLInsertNewList(L180M01A meta, String custId,
			String dupNo, String cName, String ctlType) throws CapException;

	public void l180m01b_reCtl(L180M01B model);

	public void gfnGenCTLList_ByBrno_GenDoc(Map<String, L180M01B> m_L180M01B,
			Map<String, List<L180M01C>> m_L180M01C,
			Map<String, List<L180M01D>> m_L180M01D, L180M01B l180m01b,
			List<String> cntrNo_list, Map<String, String> elf412_DBUCOID_map,
			Map<String, String> elf412_OBUCOID_map, String coMainId);

	public void gfnGenCTLList_GenDBUOBUList(Map<String, L180M01B> m_L180M01B,
			Map<String, List<L180M01C>> m_L180M01C,
			Map<String, List<L180M01D>> m_L180M01D, boolean is_exMode_A,
			String branch, L180M01A meta, Map<String, String> needGenId_coMainId);

	/**
	 * @param exMode
	 * <br/>
	 *            exMode = "1" ' 手動程式 <br/>
	 *            exMode = "2" ' 背景代理程式執行
	 * @return
	 */
	public void gfnStartNorthBranch_BuildData(String exMode, String branch,
			Date baseDate);

	public void setterL181Model(L181M01A meta, L181M01B bfObj, L181M01B afObj,
			String ctlType) throws CapException;
}
