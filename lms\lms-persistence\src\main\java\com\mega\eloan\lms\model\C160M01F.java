/* 
 * C160M01F.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 個金動審表匯入主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C160M01F", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C160M01F extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 負責事業體統編 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 負責事業體重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 負責事業體名稱 **/
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 
	 * 資金來源<p/>
	 * 單選：<br/>
	 *  0本行自有資金<br/>
	 *  1中美基金<br/>
	 *  4郵匯局基金<br/>
	 *  5信保基金
	 */
	@Size(max=1)
	@Column(name="FFUND", length=1, columnDefinition="CHAR(1)")
	private String fFund;

	/** 
	 * 用途別<p/>
	 * 單選：<br/>
	 *  1購置不動產<br/>
	 *  2購置動產<br/>
	 *  3企業投資<br/>
	 *  4週轉金
	 */
	@Size(max=1)
	@Column(name="LNPURS", length=1, columnDefinition="CHAR(1)")
	private String lnPurs;

	/** 產品種類 **/
	@Size(max=2)
	@Column(name="PRODKIND", length=2, columnDefinition="CHAR(02)")
	private String prodKind;

	/** 
	 * 科目<p/>
	 * ※會計科目代碼
	 */
	@Size(max=8)
	@Column(name="SUBJCODE", length=8, columnDefinition="VARCHAR(8)")
	private String subjCode;

	/** 
	 * 計息方式<p/>
	 * 單選：<br/>
	 *  1按月計息<br/>
	 *  2期付金
	 */
	@Size(max=1)
	@Column(name="INTWAY", length=1, columnDefinition="CHAR(1)")
	private String intWay;

	/** 
	 * 收息方式<p/>
	 * 單選：<br/>
	 *  1按月收息<br/>
	 *  2三個月收息一次<br/>
	 *  3半年收息一次<br/>
	 *  4按年收息<br/>
	 *  6期付金
	 */
	@Size(max=1)
	@Column(name="RINTWAY", length=1, columnDefinition="CHAR(1)")
	private String rIntWay;

	/** 
	 * 是否循環使用<p/>
	 * 1.不循環使用<br/>
	 *  2.循環使用
	 */
	@Size(max=1)
	@Column(name="REUSE", length=1, columnDefinition="CHAR(1)")
	private String reUse;

	/** 
	 * 償還方式<p/>
	 * 1.本息平均攤還(按月繳款)<br/>
	 *  2.本息平均攤還(雙週繳款)<br/>
	 *  3.本金平均攤還(按月繳款，每期攤還本金：元)<br/>
	 *  4.本金平均攤還(雙週繳款，每期攤還本金：元)<br/>
	 *  5.貸款本金及貸款期間全部利息自第４年起平均攤還。(921貸款專案適用)<br/>
	 *  6.其他（請自行輸入）<br/>
	 *  7.按月付息，契約到期清償本金(房貸還本週轉適用)
	 */
	@Size(max=1)
	@Column(name="PAYWAY", length=1, columnDefinition="CHAR(1)")
	private String payWay;

	/** 計收延遲利息加碼(%) **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="DRATEADD", columnDefinition="DECIMAL(5,2)")
	private BigDecimal dRateAdd;

	/** 違約金計算條件(%) **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="DRATE1", columnDefinition="DECIMAL(2,0)")
	private Integer dRate1;

	/** 有無共同借款人 **/
	@Size(max=1)
	@Column(name="COMMSCUST", length=1, columnDefinition="CHAR(1)")
	private String commsCust;

	/** 授信經辦行員代號 **/
	@Size(max=30)
	@Column(name="CNAME", length=30, columnDefinition="VARCHAR(30)")
	private String cName;

	/** 初放主管行員代號 **/
	@Size(max=6)
	@Column(name="OMGRNO", length=6, columnDefinition="CHAR(6)")
	private String omgrNo;

	/** 初放主管姓名 **/
	@Size(max=30)
	@Column(name="OMGRNAME", length=30, columnDefinition="VARCHAR(30)")
	private String omgrName;

	/** 敘作主管姓名 **/
	@Size(max=30)
	@Column(name="FRGRNAME", length=30, columnDefinition="VARCHAR(30)")
	private String frgrName;

	/** 申請日 **/
	@Column(name="FIRSTDATE", columnDefinition="TIMESTAMP")
	private Timestamp firstDate;

	/** 核准日期 **/
	@Column(name="APPROVETIME", columnDefinition="TIMESTAMP")
	private Timestamp approveTime;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得負責事業體統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定負責事業體統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得負責事業體重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定負責事業體重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得負責事業體名稱 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定負責事業體名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 
	 * 取得資金來源<p/>
	 * 單選：<br/>
	 *  0本行自有資金<br/>
	 *  1中美基金<br/>
	 *  4郵匯局基金<br/>
	 *  5信保基金
	 */
	public String getFFund() {
		return this.fFund;
	}
	/**
	 *  設定資金來源<p/>
	 *  單選：<br/>
	 *  0本行自有資金<br/>
	 *  1中美基金<br/>
	 *  4郵匯局基金<br/>
	 *  5信保基金
	 **/
	public void setFFund(String value) {
		this.fFund = value;
	}

	/** 
	 * 取得用途別<p/>
	 * 單選：<br/>
	 *  1購置不動產<br/>
	 *  2購置動產<br/>
	 *  3企業投資<br/>
	 *  4週轉金
	 */
	public String getLnPurs() {
		return this.lnPurs;
	}
	/**
	 *  設定用途別<p/>
	 *  單選：<br/>
	 *  1購置不動產<br/>
	 *  2購置動產<br/>
	 *  3企業投資<br/>
	 *  4週轉金
	 **/
	public void setLnPurs(String value) {
		this.lnPurs = value;
	}

	/** 取得產品種類 **/
	public String getProdKind() {
		return this.prodKind;
	}
	/** 設定產品種類 **/
	public void setProdKind(String value) {
		this.prodKind = value;
	}

	/** 
	 * 取得科目<p/>
	 * ※會計科目代碼
	 */
	public String getSubjCode() {
		return this.subjCode;
	}
	/**
	 *  設定科目<p/>
	 *  ※會計科目代碼
	 **/
	public void setSubjCode(String value) {
		this.subjCode = value;
	}

	/** 
	 * 取得計息方式<p/>
	 * 單選：<br/>
	 *  1按月計息<br/>
	 *  2期付金
	 */
	public String getIntWay() {
		return this.intWay;
	}
	/**
	 *  設定計息方式<p/>
	 *  單選：<br/>
	 *  1按月計息<br/>
	 *  2期付金
	 **/
	public void setIntWay(String value) {
		this.intWay = value;
	}

	/** 
	 * 取得收息方式<p/>
	 * 單選：<br/>
	 *  1按月收息<br/>
	 *  2三個月收息一次<br/>
	 *  3半年收息一次<br/>
	 *  4按年收息<br/>
	 *  6期付金
	 */
	public String getRIntWay() {
		return this.rIntWay;
	}
	/**
	 *  設定收息方式<p/>
	 *  單選：<br/>
	 *  1按月收息<br/>
	 *  2三個月收息一次<br/>
	 *  3半年收息一次<br/>
	 *  4按年收息<br/>
	 *  6期付金
	 **/
	public void setRIntWay(String value) {
		this.rIntWay = value;
	}

	/** 
	 * 取得是否循環使用<p/>
	 * 1.不循環使用<br/>
	 *  2.循環使用
	 */
	public String getReUse() {
		return this.reUse;
	}
	/**
	 *  設定是否循環使用<p/>
	 *  1.不循環使用<br/>
	 *  2.循環使用
	 **/
	public void setReUse(String value) {
		this.reUse = value;
	}

	/** 
	 * 取得償還方式<p/>
	 * 1.本息平均攤還(按月繳款)<br/>
	 *  2.本息平均攤還(雙週繳款)<br/>
	 *  3.本金平均攤還(按月繳款，每期攤還本金：元)<br/>
	 *  4.本金平均攤還(雙週繳款，每期攤還本金：元)<br/>
	 *  5.貸款本金及貸款期間全部利息自第４年起平均攤還。(921貸款專案適用)<br/>
	 *  6.其他（請自行輸入）<br/>
	 *  7.按月付息，契約到期清償本金(房貸還本週轉適用)
	 */
	public String getPayWay() {
		return this.payWay;
	}
	/**
	 *  設定償還方式<p/>
	 *  1.本息平均攤還(按月繳款)<br/>
	 *  2.本息平均攤還(雙週繳款)<br/>
	 *  3.本金平均攤還(按月繳款，每期攤還本金：元)<br/>
	 *  4.本金平均攤還(雙週繳款，每期攤還本金：元)<br/>
	 *  5.貸款本金及貸款期間全部利息自第４年起平均攤還。(921貸款專案適用)<br/>
	 *  6.其他（請自行輸入）<br/>
	 *  7.按月付息，契約到期清償本金(房貸還本週轉適用)
	 **/
	public void setPayWay(String value) {
		this.payWay = value;
	}

	/** 取得計收延遲利息加碼(%) **/
	public BigDecimal getDRateAdd() {
		return this.dRateAdd;
	}
	/** 設定計收延遲利息加碼(%) **/
	public void setDRateAdd(BigDecimal value) {
		this.dRateAdd = value;
	}

	/** 取得違約金計算條件(%) **/
	public Integer getDRate1() {
		return this.dRate1;
	}
	/** 設定違約金計算條件(%) **/
	public void setDRate1(Integer value) {
		this.dRate1 = value;
	}

	/** 取得有無共同借款人 **/
	public String getCommsCust() {
		return this.commsCust;
	}
	/** 設定有無共同借款人 **/
	public void setCommsCust(String value) {
		this.commsCust = value;
	}

	/** 取得授信經辦行員代號 **/
	public String getCName() {
		return this.cName;
	}
	/** 設定授信經辦行員代號 **/
	public void setCName(String value) {
		this.cName = value;
	}

	/** 取得初放主管行員代號 **/
	public String getOmgrNo() {
		return this.omgrNo;
	}
	/** 設定初放主管行員代號 **/
	public void setOmgrNo(String value) {
		this.omgrNo = value;
	}

	/** 取得初放主管姓名 **/
	public String getOmgrName() {
		return this.omgrName;
	}
	/** 設定初放主管姓名 **/
	public void setOmgrName(String value) {
		this.omgrName = value;
	}

	/** 取得敘作主管姓名 **/
	public String getFrgrName() {
		return this.frgrName;
	}
	/** 設定敘作主管姓名 **/
	public void setFrgrName(String value) {
		this.frgrName = value;
	}

	/** 取得申請日 **/
	public Timestamp getFirstDate() {
		return this.firstDate;
	}
	/** 設定申請日 **/
	public void setFirstDate(Timestamp value) {
		this.firstDate = value;
	}

	/** 取得核准日期 **/
	public Timestamp getApproveTime() {
		return this.approveTime;
	}
	/** 設定核准日期 **/
	public void setApproveTime(Timestamp value) {
		this.approveTime = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
