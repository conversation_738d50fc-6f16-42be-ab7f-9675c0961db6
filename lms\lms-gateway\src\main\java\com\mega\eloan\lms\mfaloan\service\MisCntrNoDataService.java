package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

public interface MisCntrNoDataService {
	/**
	 * 取得額度序號相同對象不同明細表
	 * 
	 * @param
	 * @param
	 * @return
	 */

	public void MisUpdatedata(String table, String OldCntrNo, String NewCntrNo);
	public void MisUpdatedata(String table,String OldCustId,String OldDupNo,
			String NewCustId,String NewDupNo);
	List<Map<String, Object>> getCntrNoData(String[] table, String CntrNo);

	public List<Map<String, Object>> getcustIdData(String table[],String custId,
			String dupNo);

}
