package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF490B;

/**
 * <pre>
 * 防杜代辦消金覆審資料檔
 * </pre>
 * 
 * @since 2019/4/10
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/4/10,EL08034,new
 *          </ul>
 */
public interface MisELF490BService { 
	public List<Map<String, Object>> chooseCust_by_brNo_specificEmpNo(String beg_yyyyMMdd, String end_yyyyMMdd, String brNo, String empNo);
	public List<Map<String, Object>> chooseCust_by_brNo_empNo_R1toR5(String beg_yyyyMMdd, String end_yyyyMMdd, String brNo, String empNo);
	public List<Map<String, Object>> chooseCust_by_brNo_empNo_R6(String beg_yyyyMMdd, String end_yyyyMMdd, String brNo, String empNo);
	
	public List<ELF490B> selBy_dataym_flag(String elf490b_data_ym, String elf490b_flag);
	public List<ELF490B> selBy_dataym_flag_brNo(String elf490b_data_ym, String elf490b_flag, String elf490b_brNo);
	public List<ELF490B> selBy_dataym_flag1_inOrder(String elf490b_data_ym, String elf490b_brNo);
	public List<ELF490B> selBy_dataym_flag1_brNo_ruleNo_inOrder(String elf490b_data_ym, String elf490b_brNo, String elf490b_ruleNo);
	public List<ELF490B> selBy_dataym_flag3_ruleNo_areaNo(String brGroup, String elf490b_data_ym, String elf490b_ruleNo);
}
