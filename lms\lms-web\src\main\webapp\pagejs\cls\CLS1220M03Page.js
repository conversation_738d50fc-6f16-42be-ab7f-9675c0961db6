//做為畫面init完成後使用
var initDfd = window.initDfd || $.Deferred();
var _handler="cls1220m03formhandler";
$(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	
	
    $.form.init({
        formId: "tabForm",
        formHandler: _handler,
        formAction: "query",
        loadSuccess: function(json){    	
        	if(!$("#buttonPanel").find("#btnSave").is("button") || json.lock) {
				tabForm.lockDoc();
			}
        	        	
        	if(true){
        		//當 html 中的 element 中有 codeType
            	tabForm.buildItem();		
        	}

        	tabForm.injectData(json);
        	if(json.resource){
        		$("[name=resourceTemp]").val(json.resource.split("|"));
        	}
        	if(json.othType){
        		$("[name=othTypeTemp]").val(json.othType.split("|"));
        	}
        	initDfd.resolve(json);

        	var createTime = $("#createTime").val();
        	createTime = createTime.substring(0, 10);
            if(createTime > "2021-06-01"){
                $("#voidTheApply").show();
            } else {
                $("#voidTheApply").hide();
            }

        }
    });
   
    var attchGrid = $("#attchGrid").iGrid({
        handler: 'cls1221gridhandler',
        height: 100,
        width: 400,
        autowidth: false,
        action: "queryAttch",
        postData: {
            mainId: responseJSON.mainId
        },
        needPager:false,
        colModel : [ {
			colHeader : '檔案名稱',
			name : 'srcFileName',
			width : 120,
			align: "left",
			sortable : false,
			formatter : 'click',
			onclick : openFile
		}, {
			colHeader : '上傳時間',
			name : 'uploadTime',
			width : 100,
			sortable : false
		}, {
			colHeader : i18n.cls1220m03['label.fileSrc'],
			name : 'fileSrc',
			width : 35,
			sortable : false
		}, {
			name : 'fieldId',
			hidden : true
		}, {
			name : 'oid',
			hidden : true
		}]				
	});
    
    var saveAction = function(opts){
        if (tabForm.valid()) {
        	return $.ajax({
                type: "POST",
                handler: _handler,
                data:$.extend( {
                	formAction: "saveMain",
                    page: responseJSON.page,
                    mainOid: responseJSON.mainOid
                    }, 
                    tabForm.serializeData(),
                    ( opts||{} )
                )
            }).done(function(json){
				tabForm.injectData(json);
				//更新 opener 的 Grid
				API.triggerOpener();
			});
        	
        } else {
            return $.Deferred();
        }
    }
   
    
    btnPanel.find("#btnSave").click(function(){
        saveAction({'allowIncomplete':'Y'}).done(function(json){
			if(json.saveOkFlag){
				var dyna = [];
				if(true){
					dyna.push(i18n.def.saveSuccess);
				}	
				
				if(json.IncompleteMsg){
					dyna.push(json.IncompleteMsg);
				}
					
				API.showMessage(dyna.join("<br/>-------------------<br/>"));
			}
        });
    }).end().find("#btnPrint").click(function(){
    	print();
    }).end();

    $("#voidTheApply").click(function(){
		var msg = "是否將本案執行作廢?" + "<br/><br/>" + "如為年收大於50萬，請選擇「不承作-年收大於50萬」"
        API.confirmMessage(msg, function(result){
             if (result) {
                $.ajax({
                    type: "POST",
                    handler: _handler,
                    data: {
                        formAction: "voidTheApply",
                        mainOid: responseJSON.mainOid
                    }
                }).done(function(responseData){
					tabForm.injectData(responseData);
				});
             }
        });
    });
	
	$("#changeCaseStatus").click(function(){
		
		$("#changeCaseStatusDiv").thickbox({
	        title: '變更案件狀態',
	        width: 50,
	        height: 220,
	        modal: true,
	        align: "center",
	        valign: "bottom",
	        readOnly: false,
	        i18n: i18n.def,
	        buttons: {
				
	            "sure": function(){
					
					var msg = "確定更改案件狀態 ?";
        			API.confirmMessage(msg, function(result){
             			if (result) {
							var status = $("input[name=statFlagChanged]:checked").val();
							if(status == undefined || status == ''){
								API.showMessage("請選擇一筆");
								return;
							}
							
							$.thickbox.close();
							$.ajax({
			                    type: "POST",
			                    handler: _handler,
			                    data: {
			                        formAction: "changeCaseStatus",
			                        mainOid: responseJSON.mainOid,
									statFlagChanged: status
			                    }
			                }).done(function(responseData){
								tabForm.injectData(responseData);
								API.showMessage("變更成功");
							});
						}
					});
	            },
	            "cancel": function(){
	                $.thickbox.close();
	            }
	        }
		 });
    });
	
	// 調閱資料須加註查詢理由，查詢是否當日已有紀錄
	// J-110-0395調閱資料須加註查詢理由
	$.ajax({
        handler: 'cls1220m01formhandler',
        action: 'findTodayRecord',
        data : {
            mainId: responseJSON.mainId
        }
    }).done(function(r){
		if (r.queryReasonIsRecorded) {
			$("#queryReasonIsRecorded").val(r.queryReasonIsRecorded);
		}
	});
	
	//上傳檔案按鈕
	$("#uploadFile").click(function(){
		var limitFileSize=9437103;
		MegaApi.uploadDialog({
			fieldId:"userUpload" + userInfo.userId,
            fieldIdHtml:"size='30'",
            fileDescId:"fileDesc",
            fileDescHtml:"size='30' maxlength='30'",
			subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
			limitSize:limitFileSize,
            width:320,
            height:190,			
			data:{
				mainId: responseJSON.mainId,
				flag:"9"
			},
			success : function(obj) {
				attchGrid.trigger("reloadGrid");
			}
	   });
	});
	
	//刪除檔案按鈕
	$("#deleteFile").click(function(){
		var select  = attchGrid.getGridParam('selrow');		
		// confirmDelete=是否確定刪除?
		CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
			if(b){				
				var data = attchGrid.getRowData(select);
				if(data.oid == "" || data.oid == undefined || data.oid == null){		
					// TMMDeleteError=請先選擇需修改(刪除)之資料列
					CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
					return;
				}
				if(!data.fieldId.match(/^userUpload/)) {
					CommonAPI.showMessage("客戶自行上傳檔案不能刪除");
					return;
				}
				$.ajax({
					handler : "lmscommonformhandler",
					type : "POST",
					dataType : "json",
					data : {
						formAction : "deleteUploadFile",
						oids:data.oid
					},
					success : function(obj) {
						attchGrid.trigger("reloadGrid");
					}
				});
			}else{
				return ;
			}
		});
	});
});


/**
 * 同時印多份: 
 * oid_arr.push(data.oid+"^"+data.mainId);
 * rptOid: oid_arr.join("|")
 */
function print(){
	   $.form.submit({
           url: "../../simple/FileProcessingService",
           target: "_blank",
           data: {
               'rptOid': responseJSON.mainOid+"^"+responseJSON.mainId,
               'fileDownloadName': "CLS1220R03.pdf",
               serviceName: "cls1220r03rptservice"
           }
       });
}

function openFile(cellvalue, options, rowObject){
    recordQueryReason(function(){
		$.capFileDownload({
	        handler:"simplefiledwnhandler",
	        data : {
	            fileOid:rowObject.oid
	        }
	    });
	});
}

function recordQueryReason(action) {
	if ("Y" == $("#queryReasonIsRecorded").val()) {
		if (action) {
			action();
		}
	} else {
		var setting = new Object();
		setting.id = "queryReasonDialog";
		setting.reasonCodeType = "C122S01E_queryReason";
		var queryReasonDailog = CommonAPI.queryReasonDialog(setting, function() {
			var queryReason = queryReasonDailog.find("#sseidQueryReason").val();
	        if (!queryReason) {
	            //id_reasonMsg=請先輸入查詢理由!
	            CommonAPI.showMessage(i18n.def['id_reasonMsg']);
				return;
	        }
			// J-110-0395調閱資料須加註查詢理由
			$.ajax({
	            handler: 'cls1220m01formhandler',
	            action: 'recordQueryReason',
	            data : {
	                mainId: responseJSON.mainId,
					queryReason: queryReason
		        }
	        }).done(function(r){
				$("#queryReasonIsRecorded").val("Y");
			});
			if (action) {
				action();
			}
		});
	}
}

$.extend(window.tempSave,{
	handler: _handler, // handler 名稱
	action: "tempSave", // action Method
	beforeCheck:function(){ // return false or true		
		return $("#tabForm").valid();
	},sendData:function(){ // 需上送之資料集合(Map<String,String>)
		return $("#tabForm").serializeData();
	}
});