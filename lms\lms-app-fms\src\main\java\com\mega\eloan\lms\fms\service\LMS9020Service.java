/* 
 * LMS7110Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service;

import java.util.List;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L902M01A;
import com.mega.eloan.lms.model.L902S01A;

/**
 * <pre>
 * 停權解除維護Service
 * </pre>
 * 
 * @since 2013/1/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/21,Miller,new
 *          </ul>
 */
public interface LMS9020Service extends AbstractService {

	// 停權解除維護主檔

	/**
	 * 利用oid找尋停權解除維護主檔
	 * 
	 * @param oid
	 * @return
	 */
	L902M01A findL902m01aByOid(String oid);

	/**
	 * 利用mainId找尋停權解除維護主檔
	 * 
	 * @param mainId
	 * @return
	 */
	L902M01A findL902m01aByMainId(String mainId);

	/**
	 * 利用peNo找尋私募基金主檔
	 * 
	 * @param peNo
	 * @return
	 */
	L902M01A findL902m01aByPeNo(String peNo);

	/**
	 * 取得私募基金最大號peNo
	 * 
	 * @return
	 */
	L902M01A findL902m01aMaxPeNo();

	/**
	 * 利用oid刪除停權解除維護主檔(邏輯刪除)
	 * 
	 * @param oid
	 */
	void deleteL902m01a(String oid);

	// 停權解除維護明細檔
	/**
	 * 利用mainId找尋停權解除維護明細檔群組
	 * 
	 * @param mainId
	 * @return
	 */
	List<L902S01A> findL902s01aByMainId(String mainId);

	/**
	 * 利用oid找尋停權解除維護明細檔
	 * 
	 * @param oid
	 * @return
	 */
	L902S01A findL902s01aByOid(String oid);

	/**
	 * 利用oid刪除停權解除維護明細檔(邏輯刪除)
	 * 
	 * @param oid
	 */
	void deleteL902s01a(String oid);

	/**
	 * 儲存停權明細檔群組
	 * 
	 * @param list
	 *            停權明細檔
	 */
	public void saveList902s01a(List<L902S01A> list);

	/**
	 * 取得未解除之私募基金轄下事業
	 * 
	 * @param mainId
	 * @return
	 */
	List<L902S01A> findL902s01aByMainIdWithoutDelete(String mainId);

	/**
	 * 取得所有私募基金轄下事業(含已刪除)
	 * 
	 * @param mainId
	 * @return
	 */
	List<L902S01A> findL902s01aByMainIdContainDelete(String mainId);

	List<L902S01A> findL902s01aByCustIdAndPeNo(String custId, String dupNo,
			String peNo);

	List<L902S01A> findL902s01aByCustIdWithoutDelete(String custId, String dupNo);
}
