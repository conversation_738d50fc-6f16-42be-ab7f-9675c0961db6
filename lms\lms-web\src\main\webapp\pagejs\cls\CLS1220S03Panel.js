initDfd.done(function(json){	
	
	if(true){ //依 seq 呈現選項
		var item = API.loadOrderCombosAsList("cls1131m01_othType")["cls1131m01_othType"];
	
    	$("#othType").setItems({ size: "1", item: convertItems(item), clear: true, itemType: 'checkbox' })
		
    	if(true){
    		$("[name=othType]").val(json.othType.split("|")).change();		
    	}
		
		$("input[type=text]").readOnly();
		$("input[type=checkbox]").readOnly();
		$("select").readOnly();
		
    }
	if(true){
		$("input[name=othType]").closest('label').css('color','lightgray');
	    $("input[name=othType]:checked").closest('label').css('color','black');
	}	
	$('#jobType1').change(function(){
        var code = $(this).val();
        if (code) {
            var item = CommonAPI.loadCombos('jobType' + code);
            $('#jobType2').setItems({
                item: item['jobType' + code],
                format: '{key}'
            });
            //填入 jobType2
            $("#jobType2").val(json.jobType2);
        }
    });
	$('#jobType1').trigger('change');
});
