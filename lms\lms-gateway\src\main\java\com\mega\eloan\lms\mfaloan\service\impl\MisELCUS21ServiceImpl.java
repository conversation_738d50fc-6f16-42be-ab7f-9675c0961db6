package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import tw.com.iisi.cap.util.CapString;

import com.mega.eloan.lms.mfaloan.service.MisELCUS21Service;

@Service
public class MisELCUS21ServiceImpl extends AbstractMFAloanJdbc implements
		MisELCUS21Service {

	@Override
	public List<Map<String, Object>> getCustAddressForLas(String custId, String dupNo) {
		StringBuffer id = new StringBuffer(custId);
		while (id.length() < 10) {
			id.append(" ");
		}
		id.append(dupNo);

		return getJdbc().queryForList("ELCUS21.getCustAddrByLAS",
				new String[] { custId, dupNo, id.toString() });
	}

	/**
	 * 取得客戶通訊地址資料
	 * 
	 * @param custId
	 *            統編
	 * @param dupNo
	 *            重覆序號
	 * @return Map<String, Object>
	 */
	@Override
	public Map<String, Object> findByIdDupNo(String custId, String dupNo) {
		return getJdbc().queryForMap("ELCUS21.findById",
				new String[] { custId , dupNo});
	}//;

	/* (non-Javadoc)
	 * @see com.mega.eloan.ces.mfaloan.service.MisElcus21Service#getAddr(java.util.Map)
	 */
	@Override
	public String getAddr(Map<String, Object> data){
		if(!CollectionUtils.isEmpty(data)){
			String[] addrUnits = new String[] { "CITYR", "TOWNR", "LEER",
					"LINR", "ADDRR" };
			StringBuffer addr = new StringBuffer();
			for (String addrUnit : addrUnits) {
				addr.append(CapString.trimNull(data.get(addrUnit)));
			}

			return addr.toString();
		}
		return "";
	}
}
