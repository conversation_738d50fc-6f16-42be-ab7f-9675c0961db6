/* 
 * C900M01M.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** MEGAIMAGE文件數位化檔案上傳紀錄檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C900M01M", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C900M01M extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(unique = true, nullable = false, name = "OID", length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	/**
	 * 案件mainId
	 */
	@Column(length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 上傳批號
	 */
	@Column(length = 32, columnDefinition = "CHAR(32)")
	private String batId;

	/**
	 * docFileOid
	 */
	@Column(length = 32, columnDefinition = "CHAR(32)")
	private String docFileOid;

	/**
	 * 表單代碼=業務別+列印通道+內/外+借保人類別+借保人序號+文件種類流水號
	 */
	@Column(length = 9, columnDefinition = "VARCHAR(9)")
	private String formId;

	/**
	 * 案件編號
	 */
	@Column(length = 90, columnDefinition = "VARCHAR(90)")
	private String caseNo;

	/**
	 * 案件分行代碼
	 */
	@Column(length = 3, columnDefinition = "VARCHAR(3)")
	private String branch;

	/**
	 * 主借款人ID
	 */
	@Column(length = 10, columnDefinition = "VARCHAR(10)")
	private String borrower;

	/**
	 * 案件申請日期
	 */
	@Column(length = 8, columnDefinition = "VARCHAR(8)")
	private String applicationDate;

	/**
	 * 關係人ID
	 */
	@Column(length = 30, columnDefinition = "VARCHAR(30)")
	private String stakeholderID;

	/** 上傳人員號碼 **/
	@Size(max = 6)
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String userCode;

	/**
	 * 上傳時間
	 */
	@Column(columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;
	
	/**
	 * 刪除時間
	 */
	@Column(columnDefinition = "TIMESTAMP")
	private Timestamp deletedTime;


	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getBatId() {
		return batId;
	}

	public void setBatId(String batId) {
		this.batId = batId;
	}

	public String getDocFileOid() {
		return docFileOid;
	}

	public void setDocFileOid(String docFileOid) {
		this.docFileOid = docFileOid;
	}

	public String getFormId() {
		return formId;
	}

	public void setFormId(String formId) {
		this.formId = formId;
	}

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	public String getBranch() {
		return branch;
	}

	public void setBranch(String branch) {
		this.branch = branch;
	}

	public String getBorrower() {
		return borrower;
	}

	public void setBorrower(String borrower) {
		this.borrower = borrower;
	}

	public String getApplicationDate() {
		return applicationDate;
	}

	public void setApplicationDate(String applicationDate) {
		this.applicationDate = applicationDate;
	}

	public String getStakeholderID() {
		return stakeholderID;
	}

	public void setStakeholderID(String stakeholderID) {
		this.stakeholderID = stakeholderID;
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}

	public Timestamp getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Timestamp updateTime) {
		this.updateTime = updateTime;
	}

	public Timestamp getDeletedTime() {
		return deletedTime;
	}

	public void setDeletedTime(Timestamp deletedTime) {
		this.deletedTime = deletedTime;
	}
}
