package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 系統初評 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C101S02C", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C101S02C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/**
	 * 現請額度
	 * <p/>
	 * 單位:元
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "LOANAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal loanAmt;

	/** 授信期間-年數 **/
	@Digits(integer=2, fraction=0)
	@Column(name="LNYEAR", columnDefinition="DECIMAL(2,0)")
	private Integer lnYear;

	/** 授信期間-月數 **/
	@Digits(integer=2, fraction=0)
	@Column(name="LNMONTH", columnDefinition="DECIMAL(2,0)")
	private Integer lnMonth;

	/** 期付金 **/
	@Digits(integer=13, fraction=0)
	@Column(name="INSTALLMENTPAY", columnDefinition="DECIMAL(13,0)")
	private Integer installmentPay;

	/** 進件編號 **/
	@Size(max=32)
	@Column(name="ONLINECASENO", columnDefinition="CHAR(32)")
	private String onlineCaseNo;

	/** 產品種類     select * from lms.c900m01a ||  select * from ln.lnf110 **/
	@Size(max = 2)
	@Column(name = "PRODKIND", length = 2, columnDefinition = "CHAR(02)")
	private String prodKind;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;
	
	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 本次系統初評結果 */
	@Column(length = 3, columnDefinition = "CHAR(3)")
	private String docStatus;

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}
    public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public String getDupNo() {
		return dupNo;
	}

	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}

	public BigDecimal getLoanAmt() {
		return loanAmt;
	}

	public void setLoanAmt(BigDecimal loanAmt) {
		this.loanAmt = loanAmt;
	}

	public Integer getLnYear() {
		return lnYear;
	}

	public void setLnYear(Integer lnYear) {
		this.lnYear = lnYear;
	}

	public Integer getLnMonth() {
		return lnMonth;
	}

	public void setLnMonth(Integer lnMonth) {
		this.lnMonth = lnMonth;
	}

	public Integer getInstallmentPay() {
		return installmentPay;
	}

	public void setInstallmentPay(Integer installmentPay) {
		this.installmentPay = installmentPay;
	}

	public String getOnlineCaseNo() {
		return onlineCaseNo;
	}

	public void setOnlineCaseNo(String onlineCaseNo) {
		this.onlineCaseNo = onlineCaseNo;
	}

	/** 取得產品種類 **/
	public String getProdKind() {
		return this.prodKind;
	}

	/** 設定產品種類 **/
	public void setProdKind(String value) {
		this.prodKind = value;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public String getUpdater() {
		return updater;
	}

	public void setUpdater(String updater) {
		this.updater = updater;
	}

	public Timestamp getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Timestamp updateTime) {
		this.updateTime = updateTime;
	}

	public String getDocStatus() {
		return docStatus;
	}

	public void setDocStatus(String docStatus) {
		this.docStatus = docStatus;
	}

	@SuppressWarnings("rawtypes")
	public void setDocStatus(Enum docStatusEnum) {
		this.docStatus = docStatusEnum.toString();
	}
}
