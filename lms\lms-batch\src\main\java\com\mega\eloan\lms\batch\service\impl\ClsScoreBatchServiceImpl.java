package com.mega.eloan.lms.batch.service.impl;


import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.lms.lms.service.LMS1025Service;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

@Service("clsScoreBatchServiceImpl")
public class ClsScoreBatchServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger logger = LoggerFactory
			.getLogger(ClsScoreBatchServiceImpl.class);
		
	@Autowired
	LMS1025Service lms1025Service;
	
	@Override
	public JSONObject execute(JSONObject json) {
		boolean isSuccess = false;
		JSONObject result = null;
		JSONObject rq = json.getJSONObject("request");
			
		String act = rq.optString("act");
		String msg = "";
		try {
			if(Util.equals("runModelCompare", act)){
				String docOid = Util.trim(rq.optString("docOid"));
				//============
				lms1025Service.runModelCompare(docOid);
			}
			isSuccess = true;
		} catch (Throwable e) {	
			msg = e.getMessage();
			logger.error(msg, e);
			isSuccess = false;
		}

		if (isSuccess) {
			result = WebBatchCode.RC_SUCCESS;//此json object 內已包含 SUCCESS
			result.element(WebBatchCode.P_RESPONSE, result.get(WebBatchCode.P_RC_MSG));
		} else {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, msg);
			result.element(WebBatchCode.P_RESPONSE, msg);
		}
		return result;
	}
}
      