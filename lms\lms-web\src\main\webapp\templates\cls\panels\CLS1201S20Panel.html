<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <script type="text/javascript">loadScript('pagejs/cls/CLS1201S20Panel');</script>
            <form id="CLS1201S20Form">
			<div class="content">
					<fieldset>
						<legend>
							<b><th:block th:text="#{L120S09a.blacklistQuery}">洗錢防制查詢</th:block></b>
						</legend>
						<div class="funcContainer">
							<!-- **** -->
							<th:block th:if="${HS_importBlackList_s_lock}">
								<button type="button" id="cls1201s20_btn_importBlackList" onClick="importBlackList()" disabled>
									<span class="text-only"><th:block th:text="#{L120S09a.reApplyBlacklist}">重新引進查詢名單</th:block></span>
								</button>
							</th:block>
							<th:block th:if="${HS_importBlackList_s_enb}">
								<button type="button" id="cls1201s20_btn_importBlackList" onClick="importBlackList()">
									<span class="text-only"><th:block th:text="#{L120S09a.reApplyBlacklist}">重新引進查詢名單</th:block></span>
								</button>
							</th:block>
							<!-- **** -->
							
							<!-- ** 原功能黑名單查詢(起) ** -->
							<th:block th:if="${HS_queryBlackList_s}">
								<button type="button" id="cls1201s20_btn_queryBlackList" onClick="queryBlackList()">
									<span class="text-only"><th:block th:text="#{L120S09a.queryBlacklist}">黑名單查詢</th:block></span>
								</button>	
							</th:block>
							<!-- ** 原功能黑名單查詢(迄) ** -->
							
							<!-- **** -->
							<th:block th:if="${HS_sendAmlList_s_lock}">
								<button type="button" id="cls1201s20_btn_sendAmlList" onClick="sendAmlList()" disabled>
									<span class="text-only"><th:block th:text="#{L120S09a.sendAmlList}">傳送名單掃描</th:block></span>
								</button>	
							</th:block>
							<th:block th:if="${HS_sendAmlList_s_enb}">
								<button type="button" id="cls1201s20_btn_sendAmlList" onClick="sendAmlList()">
									<span class="text-only"><th:block th:text="#{L120S09a.sendAmlList}">傳送名單掃描</th:block></span>
								</button>	
							</th:block>
							<!-- **** -->
							
							
							<!-- **** -->
							<th:block th:if="${HS_checkAmlResult_s_lock}">
								<button type="button" id="cls1201s20_btn_checkAmlResult" onClick="checkAmlResult()" disabled >
									<span class="text-only"><th:block th:text="#{L120S09a.checkAmlResult}">取得黑名單查詢結果</th:block></span>
								</button>	
							</th:block>
							<th:block th:if="${HS_checkAmlResult_s_enb}">
								<button type="button" id="cls1201s20_btn_checkAmlResult" onClick="checkAmlResult()">
									<span class="text-only"><th:block th:text="#{L120S09a.checkAmlResult}">取得黑名單查詢結果</th:block></span>
								</button>	
							</th:block>
							<!-- **** -->
							&nbsp;&nbsp;&nbsp;&nbsp;
							<button type="button" id="cls1201s20_btn_applyCm1AmlStatus" onClick="applyCm1AmlStatus()">
								<span class="text-only"><th:block th:text="#{L120S09a.apply}">引進</th:block><th:block th:text="#{L120S09a.cm1AmlStatus}">0024拒絕交易註記</th:block></span>
							</button>
							<button type="button" id="cls1201s20_btn_applyLuvRiskLevel" onClick="applyLuvRiskLevel()">
								<span class="text-only"><th:block th:text="#{L120S09a.applyLuvRiskLevel}">引進申貸戶風險等級</th:block></span>
							</button>	
							<!-- **** -->
							&nbsp;&nbsp;&nbsp;&nbsp;
							<button type="button" id="cls1201s20_btn_applyAmlStatusDone" onClick="ApplyAmlStatusDoneAction.applyAmlStatusDone()">
								<span class="text-only">引進當日已完成掃描/調查結果</span>
							</button>
							<!--J-113-0082 配合法務部新規，新增引入「受告誡處分」資訊-->
							<button type="button" id="cls1201s20_btn_importCmfwarnp" onClick="importCmfwarnpResult()">
								<span class="text-only"><th:block th:text="#{L120S09a.importCmfwarnpResult}">告誡戶掃描</th:block></span>
							</button>
						</div>
						
						<table width="50%" style="display: inline-block;vertical-align:top;">
							<tr>
								<td><span class="color-blue"><th:block th:text="#{L120S09a.queryDateS}">資料查詢日期</th:block>：</span>
									<span class="color-blue field" id="blackListQDate" name="blackListQDate"></span> 
								</td>
							</tr>
							<!-- **** -->
							<th:block th:if="${HS_sas_column01}">
								<tr>
									<td><span class="color-blue"><th:block th:text="#{L120S09a.ncResult}">案件調查結果</th:block>：</span>
										    <select id="ncResult" name="ncResult" space="true" combokey="SAS_NC_Result"  disabled="disabled" />
									</td>
								</tr>
								<tr>
									<td><span class="color-blue"><th:block th:text="#{L120S09a.refNo}">掃描對象編號</th:block>：</span><span class="color-blue field" id="refNo" name="refNo"></span>
									</td>
								</tr>
								<tr>
									<td><span class="color-blue"><th:block th:text="#{L120S09a.uniqueKey}">掃描批號</th:block>：</span><span class="color-blue field" id="uniqueKey" name="uniqueKey"></span>
									</td>
								</tr>
								<tr>
									<td><span class="color-blue"><th:block th:text="#{L120S09a.ncCaseId}">案例ID</th:block>：</span><span class="color-blue field" id="ncCaseId" name="ncCaseId"></span>
									</td>
								</tr>
							</th:block>
							<!-- **** -->
						</table>
						
						<!--  J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位 -->
						<table width="48%" style="display: inline-block;">
							<th:block th:if="${HS_sas_column03}">
								<tr>
									<td>
	                                	<div id="ncResultRemarkDiv" class="hide">
											<span class="color-blue" style="vertical-align:top">
	                                            <th:block th:text="#{L120S09a.ncResultRemark}">制裁/管制名單掃描調查結果說明</th:block>：
												<br/>
	                                            <th:block th:text="#{L120S09a.ncResultRemarkTip}">(請簡要說明調查結果，可參照A2表或C表內容)</th:block>
	                                        </span>
											<br/>
											<textarea name="ncResultRemark" id="ncResultRemark" style="width:300px;height:60px;" maxlength="300" maxlengthC="100"></textarea>
	                                	</div>
										<div id="highRiskRemarkDiv" class="hide">
											<span class="color-blue" style="vertical-align:top">
	                                            <th:block th:text="#{L120S09a.highRiskRemark}">高風險調查結果說明</th:block>：
												<br/>
	                                            <th:block th:text="#{L120S09a.highRiskRemarkTip}">(請簡要說明調查結果，可參照F1表或B3表內容)</th:block>
	                                        </span>
											<br/>
											<textarea name="highRiskRemark" id="highRiskRemark" style="width:300px;height:60px;" maxlength="300" maxlengthC="100"></textarea>
	                                	</div>
									</td>
								</tr>
							</th:block>
						</table>
						
						<table width="100%">
							<tr style='vertical-align:top;'>
							<td nowrap>
								<div class="funcContainer">
									&nbsp;&nbsp;
									<!-- **** -->
									<th:block th:if="${HS_add_rm_l120s09a_s_lock}">
										<button type="button" id="cls1201s20_btn_addData" onClick="addData()" disabled>
											<span class="text-only"><th:block th:text="#{L120S09a.addNew}">新增查詢名單</th:block></span>
										</button>
										<button type="button" id="cls1201s20_btn_deleteList" onClick="deleteList()" disabled>
											<span class="text-only"><th:block th:text="#{L120S09a.delete}">刪除名單</th:block></span>
										</button>
									</th:block>
									<th:block th:if="${HS_add_rm_l120s09a_s_enb}">
										<button type="button" id="cls1201s20_btn_addData" onClick="addData()">
											<span class="text-only"><th:block th:text="#{L120S09a.addNew}">新增查詢名單</th:block></span>
										</button>
										<button type="button" id="cls1201s20_btn_deleteList" onClick="deleteList()">
											<span class="text-only"><th:block th:text="#{L120S09a.delete}">刪除名單</th:block></span>
										</button>
									</th:block>
									<!-- **** -->
									&nbsp;&nbsp;	
									<!-- **** -->
									<th:block th:if="${HS_sas_column02}">
										<span>
										<a th:href="@{/img/lms/AML_Route_Rule_EL.htm}" target="_blank"><th:block th:text="#{L120S09a.openRouteRule}">開啟命中代碼說明</th:block></a>	
										</span>
									</th:block>
									<!-- **** -->	
								</div>
							</td>
							<td>
								<th:block th:utext="#{L120S09a.showMemo}">查詢結果：&nbsp;&nbsp;◎:未列於黑名單&nbsp;&nbsp; ★:是黑名單&nbsp;&nbsp;△:可能是黑名單&nbsp;&nbsp;╳:拒絕交易</th:block>&nbsp;&nbsp;<br/>
								<th:block th:utext="#{L120S09a.showMemo1}">風險等級說明：L:低&nbsp;&nbsp; M:中&nbsp;&nbsp;H:高</th:block>
							</td>
							</tr>
						</table>
						<th:block th:if="${grid_column_sas_show}">
	                  		<div id="gridview_blackList" data-show="Y" style="margin-left: 10px; margin-right: 10px"></div>
						</th:block>
						<th:block th:if="${grid_column_sas_hide}">
	                  		<div id="gridview_blackList" data-show="N" style="margin-left: 10px; margin-right: 10px"></div>
						</th:block>
					</fieldset>
									
				</div>
            </form>
			
			<!-- pop up screen -->
			<div id="blackListDetail" style="display: none;">
			<form id="tL120S09AForm">
				
				<fieldset>
					<legend>
						<b><th:block th:text="#{L120S09a.inputBlacklist}">登錄洗錢防制名單</th:block></b>
					</legend>
					<p />
					<b><th:block th:text="#{L120S09a.createBY}">文件產生方式</th:block>：</b><span class="field" id="createBY2" name="createBY2" ></span>
					<span class="field color-blue max" id="createBY" name="createBY" maxlength="3" style="display:none;"></span>
					<p />
					<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
						<tbody>					
							<!--
							參考 LMSS20APage.js 裡面的 BlackListRelation
							另外, 在 CLS1141R01RptServiceImpl 裡也用到 custRelationMap = codetypeservice.findByCodeType("BlackListRelation", locale.toString());
							-->			
							<tr>
								<td width="20%" class="hd1"><th:block th:text="#{L120S09a.custRelation}">與本案關係</th:block>&nbsp;&nbsp;</td>
								<td width="15%">
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="1" />
									<th:block th:text="#{L120S09a.checkbox1}">借戶</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="3" />
									<th:block th:text="#{L120S09a.checkbox3}">借戶負責人</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="5" />
									<th:block th:text="#{L120S09a.checkbox5}">擔保品提供人</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="7" />
									<th:block th:text="#{L120S09a.checkbox7}">實質受益人</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="9" />
									<th:block th:text="#{L120S09a.checkbox9}">應收帳款買方無追索</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="11" />
									<th:block th:text="#{L120S09a.checkbox11}">具控制權人</th:block></label><br />
								</td>
								<td width="15%">
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="2" />
									<th:block th:text="#{L120S09a.checkbox2}">共同借款人</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="4" />
									<th:block th:text="#{L120S09a.checkbox4}">連保人</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="6" />
									<th:block th:text="#{L120S09a.checkbox6}">關係企業</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="8" />
									<th:block th:text="#{L120S09a.checkbox8}">一般保證人</th:block></label><br />
									<label><input type="checkbox" name="custRelation" class="max" maxlength="12" value="10" />
									<th:block th:text="#{L120S09a.checkbox10}">高階管理人員</th:block></label><br />
								</td>
							</tr>
						</tbody>
					</table>
					<br />
					<div class="funcContainer"></div>
					<table class="tb2" width="100%" border="0" cellspacing="0"
						cellpadding="0">
						<tbody>
							<tr>
								<td class="hd2" colspan="4" ><th:block th:text="#{L120S09a.blacklistContent}">名單內容</th:block></td>	
							</tr>
							<tr>
								<td class="hd1" width="20%" ><th:block th:text="#{L120S09a.custId}">本案關係人統編</th:block>&nbsp;&nbsp;</td>
								<td width="30%">
									<input type="text" id="custId" name="custId" class="upText"  maxlength="10"/>
								</td>
								<td class="hd1" width="20%" ><th:block th:text="#{L120S09a.dupNo}">重覆序號</th:block>&nbsp;&nbsp;</td>
								<td width="30%">
									<input type="text" id="dupNo" name="dupNo" maxlength="1" size="5"/>
								</td> 
							</tr>
							<tr>
								<td class="hd1">
									<th:block th:text="#{L120S09a.custName}">戶名</th:block>&nbsp;&nbsp;
								    <br>
									<button type="button" onClick="applyCustName()">
										<span class="text-only"><th:block th:text="#{L120S09a.apply}">引進</th:block></span>
									</button>
								</td>
								<td colspan="3">
									<!--halfword-->
									<input type="text" id="custName" name="custName"  class="required halfText" size="100" maxlength="120" maxlengthC="40" />
								</td>								
							</tr>	
							<tr>
								<td class="hd1"><th:block th:text="#{L120S09a.custEName}">英文戶名</th:block>&nbsp;&nbsp;</td>
								<td colspan="3">
									<input type="text" id="custEName" name="custEName" size="100" maxlength="120" maxlengthC="40" class="halfText halfword"/>
								</td> 
							</tr>	
							<tr> 
								<td class="hd1"><th:block th:text="#{L120S09a.country}">國別</th:block>&nbsp;</td>
								<td colspan="3"><select id="country" name="country" class="" space="true" combokey="CountryCode" combotype="4" />
								</td>
							</tr> 	
							<tr>
								<td class="hd1"><th:block th:text="#{L120S09a.queryDateS}">資料查詢日</th:block>&nbsp;&nbsp;</td>
								<td>
									<input type="text" id="queryDateS" name="queryDateS" class="caseReadOnly" readonly/>
								</td>
								<td class="hd1"><th:block th:text="#{L120S09a.blackListCode}">查詢結果</th:block>&nbsp;&nbsp;</td>
								<td>
									<select id="blackListCode" name="blackListCode" space="true" combokey="BlackListCode" combotype="2" readonly="readonly" disabled />									
								</td> 
							</tr>	
							<tr>
								<td class="hd1"><th:block th:text="#{L120S09a.memo}">命中代碼</th:block>&nbsp;&nbsp;</td>
								<td>
									<span id="memo" class="field"></span>
								</td>
								<td class="hd1"><th:block th:text="#{L120S09a.cm1AmlStatus}">0024註記</th:block>&nbsp;</td>
								<td><span id="cm1AmlStatus" class="field"></span>
								</td> 
							</tr>		
							<tr>
								<td class="hd1"><th:block th:text="#{L120S09a.luvRiskLevel}">風險等級</th:block>&nbsp;&nbsp;</td>
								<td>
									<span id="luvRiskLevel" class="field"></span>
								</td>
								<td class="hd1"><th:block th:text="#{L120S09a.cmfwarnpResult}">受告誡處分</th:block>&nbsp;&nbsp;</td>
								<td>
									<span id="cmfwarnpResultDesc" class="field"></span>
								</td>
							</tr>					
						</tbody>
					</table>
				</fieldset>
				<input type="hidden" id="seqNum" name="seqNum" />
			</form>
			</div>
			
			
			<div id="selectAmlStatusDoneBox" style="display:none">
                <div id="selectAmlStatusDoneView" />
            </div><!--顯示當日已完成掃描/調查結果 thickbox-->
			
        </th:block>
    </body>
</html>