/* 
 * L184M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L184M01A;


/** 企金戶新增/增額/逾放轉正名單檔 **/
public interface L184M01ADao extends IGenericDao<L184M01A> {

	L184M01A findByOid(String oid);

	L184M01A findByMainId(String mainId);

	L184M01A findByUniqueKey(Date dataDate, String branchId);

	List<L184M01A> findByIndex01(Date dataDate, String branchId);

	L184M01A findByIndex02(String brNo);
	
	List<L184M01A> findByCustIdDupId(String custId,String DupNo);
}