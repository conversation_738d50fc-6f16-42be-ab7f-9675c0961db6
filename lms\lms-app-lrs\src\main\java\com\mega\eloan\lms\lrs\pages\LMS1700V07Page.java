package com.mega.eloan.lms.lrs.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.lrs.panels.LMS1700FilterPanel;

/**
 * 覆審單位-已覆核
 */
@Controller
@RequestMapping("/lrs/lms1700v07")
public class LMS1700V07Page extends AbstractEloanInnerView {

	public LMS1700V07Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {

		setGridViewStatus(RetrialDocStatusEnum.編製中, RetrialDocStatusEnum.待覆核);
		addToButtonPanel(model, LmsButtonEnum.Filter);
		renderJsI18N(LMS1700V01Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "require(['pagejs/lrs/LMS1700FilterPanel'],function(){loadScript('pagejs/lrs/LMS1700V01Page');});");
		setupIPanel(new LMS1700FilterPanel(PANEL_ID, true), model, params);
	}
	
	protected void addPanel(ModelMap model, PageParameters params, String panelId) {
		new LMS1700FilterPanel(panelId, true).processPanelData(model, params);
	}
}
