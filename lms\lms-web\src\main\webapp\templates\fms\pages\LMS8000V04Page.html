<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
			<script type="text/javascript">
				loadScript('pagejs/fms/LMS8000V04Page');
			</script>
			<div id="filterBox" style="display:none">
                <input type="hidden" id="openFrom" name="openFrom" value=""/>
				<form id="filterForm"><!--Z-->
					<table class="tb2" width="100%">
						<tbody>
						<tr class="lrsHide">
							<td class="hd1" style="width:25%;">
								<span class="text-red">＊</span>
								<th:block th:text="#{'doc.id'}"></th:block>：&nbsp;&nbsp;
							</td>
							<td>
								<input id="custId" name="custId" type="text" class="upText required" size="10" maxlength="10" /> -
								<input id="dupNo" name="dupNo" type="text" size="1" maxlength="1">
							</td>
						</tr>
						<tr class="lrsHide">
							<td class="hd1">
								<span class="text-red">＊</span>
								<th:block th:text="#{'cntrNo'}">額度序號</th:block>：&nbsp;&nbsp;
							</td>
							<td>
								<input id="cntrNo" name="cntrNo" type="text" size="12" maxlength="12" class="trim alphanum required"/>
							</td>
						</tr>
						<tr class="lrsHide">
							<td class="hd1">
								<span class="overSeaHide" style="display:none">
                                    <th:block th:text="#{'loanNo'}"></th:block>
                                </span>
                                <span class="overSeaShow" style="display:none">
                                    <th:block th:text="#{'loanNoOvs'}"></th:block>
                                </span>：&nbsp;&nbsp;
							</td>
							<td>
								<input type="text" id="loanNo" name="loanNo" size="20" maxlength="20"/>
							</td>
						</tr>
						<tr>
							<td class="hd1" style="width:30%;">
								<span class="text-red">＊</span>
								<th:block th:text="#{'L260M01C_status'}">追蹤檢核項目狀態</th:block>：&nbsp;&nbsp;
							</td>
							<td>
								<label><input name="status" type="radio" value="N" class="required" checked><th:block th:text="#{'status_N'}"></th:block></label>&nbsp;&nbsp;
								<label><input name="status" type="radio" value="A"><th:block th:text="#{'status_A'}"></th:block></label>&nbsp;&nbsp;
								<label><input name="status" type="radio" value="C"><th:block th:text="#{'status_C'}"></th:block></label>
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<span class="text-red">＊</span>
								<th:block th:text="#{'L260M01D_status'}">追蹤事項通知狀態</th:block>：&nbsp;&nbsp;
							</td>
							<td>
								<input type="checkbox" id="handlingStatus" name="handlingStatus" />
							</td>
						</tr>
						<tr>
							<td class="hd1">
								<span class="text-red">＊</span>
								<th:block th:text="#{'L260M01D_followDate'}">追蹤事項通知日期區間</th:block>：&nbsp;&nbsp;
							</td>
							<td>
								<input id="startDate" name="startDate" type="text" class="date required" maxlength="10" _requiredLength="10"/>~
								<input id="endDate" name="endDate" type="text" class="date required" maxlength="10" _requiredLength="10"/>
								<span class="text-red">ex:YYYY-MM-DD</span>
							</td>
						</tr>
						<tr class="lrsShow" style="display: none;">
							<td colspan="2">
								<select id='cntrnoList' name='cntrnoList'></select>
							</td>
						</tr>
						</tbody>
					</table>
				</form>
			</div>
		</th:block>
	</body>
</html>