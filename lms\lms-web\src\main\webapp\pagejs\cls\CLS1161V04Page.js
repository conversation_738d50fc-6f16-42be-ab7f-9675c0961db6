var pageAction = {
    handler: 'cls1161m02formhandler',
    grid: null,
    oid: null,
    build: function(){
        pageAction.grid = $("#gridview").iGrid({
            // localFirst: true,
            handler: 'cls1161gridhandler',
            height: 400,
            action: 'queryViewC160M02A',
            rowNum: 17,
            rownumbers: true,
            sortname: 'createTime',
            sortorder: 'desc',
            postData: {
				docStatus: viewstatus
			},
            colModel: [{
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }, {
                name: 'dupNo',
                hidden: true
            }, {
                name: 'docURL',
                hidden: true
            }, {
                colHeader: i18n.cls1161v04["C160M02A.custId"], // 身分證統編重複碼
                align: "left",
                width: 60, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'custId',
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                    pageAction.openDoc(rowObject);
                }
            }, {
                colHeader: i18n.cls1161v04["C160M02A.custName"], // 借款人姓名
                align: "left",
                width: 60, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'custName'
            }, {
                colHeader: i18n.cls1161v04["C160M02A.caseNo"],
                align: "center",
                width: 120, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'caseNo'
            }, {
                colHeader: i18n.cls1161v04["C160M02A.apprId"], // 經辦
                align: "center",
                width: 60, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'apprId'
            }, {
                colHeader: i18n.cls1161v04["C160M02A.createTime"], // 建立日期
                align: "center",
                width: 60, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'createTime'
            }],
            ondblClickRow: function(rowid){
                var data = pageAction.grid.getRowData(rowid);
                pageAction.openDoc(data);
            }
        });
        
        // build button
        // 新增
        $("#buttonPanel").find("#btnAdd").click(function(){     
        	AddCustAction.open({
				handler : pageAction.handler,
				action : 'addCase',
				callback : function(response){
					//~~~~~~
					$.thickbox.close();
                    pageAction.openDoc(response.data);
                }
            });
        }) // 編輯 & 調閱
.end().find("#btnModify,#btnView").click(function(){
            var data = pageAction.grid.getSingleData();
            if (data) {
                pageAction.openDoc(data);
            }
        }) // 刪除
.end().find("#btnDelete").click(function(){
            var data = pageAction.grid.getSingleData();
            if (data) {
                MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
                    if (action) {
                        $.ajax({
                            handler: pageAction.handler,
                            action: 'deleteCase',
                            data: data,
                            success: function(response){
                                pageAction.grid.reload();
                                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["confirmDeleteSuccess"]);
                            }
                        });
                    }
                });
            }
        }) // 篩選
.end().find('#btnFilter').click(function(){
            pageAction.openFilter();
        });
        
    },
    /**
     * 開啟文件
     */
    openDoc: function(data){
        $.form.submit({
            url: data.docURL,
            data: $.extend(data, {
                mainOid: data.oid || '',
                mainDocStatus: viewstatus
            }),
            target: data.oid
        });
    },
    load: function(data){
        $.ajax({
            handler: pageAction.handler,
            action: 'loadDetial',
            data: $.extend(data, {
                formName: 'C160M02AForm',
                noOpenDoc: true
            }),
            success: function(response){
                $('#C160M01DForm').setValue(response.C160M02AForm);
                $('#C160M01DForm').setValue(response.C160M01DForm, false);
            }
        });
    },
    /**
     * 篩選
     */
    openFilter: function(){
    	var _id = "c160m02a_tb";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>請輸入欲查詢之ID：");
			dyna.push("	<table class='tb2'><tr><td class='hd2'>身分證統編&nbsp;&nbsp;&nbsp;&nbsp;</td>");
			dyna.push(" <td>");
			dyna.push(" <input type='text' id='search_custId' name='search_custId' maxlength=10>&nbsp;&nbsp;&nbsp;&nbsp;");
			dyna.push(" </td></tr></table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
    	
    	$("#"+_id).thickbox({
			title : i18n.def['query'] || '篩選',
			width : 400, height : 150, modal : false,
			align : 'center', valign: 'bottom', i18n: i18n.def,
			buttons : {
				'sure' : function(){
					var $form = $('#'+_form);
					if ($form.valid()){	
						$.thickbox.close();
						pageAction.reloadGrid($form.serializeData());
					}
				}
			}
		});
    },
    bfReCheckDateFormatter: function(cellvalue, otions, rowObject){
        var value = "";
        if ($.trim(cellvalue)) {
            value = "Ｖ"
        }
        return value
    },
	/**
	 * 重整資料表
	 */
	reloadGrid : function(data){
		if (data){
			pageAction.grid.jqGrid("setGridParam", {
				postData : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}else{
			pageAction.grid.trigger('reloadGrid');
		}
	}
}

$(function(){
    pageAction.build();
});
