/* 
 * LMS1815V03Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;

/**
 * <pre>
 * 覆審控制檔 - 已覆核
 * </pre>
 * 
 * @since 2011/9/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/20,irene,new
 *          </ul>
 */
@Controller
@RequestMapping("/lrs/lms1815v03")
public class LMS1815V03Page extends AbstractEloanInnerView {

	public LMS1815V03Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(RetrialDocStatusEnum.已核准);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.Filter);
		renderJsI18N(LMS1815V01Page.class);
		renderJsI18N(LMS1815V03Page.class);
		model.addAttribute("loadScript",
				"loadScript('pagejs/lrs/LMS1815V01Page');");
	}

//	public String[] getJavascriptPath() {
//		return new String[] { "pagejs/lrs/LMS1815V01Page.js" };
////		return null;
//	}
}
