var initDfd = $.Deferred();
var _handler = "cls3701m01formhandler";

var result_s = CommonAPI.loadCombos(["HaveNo", "YesNo", "counties"]);

var Action = {
	_isLoad: false,
	FormData: {},
	_initForm: function(){
        $.form.init({
            formHandler:_handler,
            formAction:'queryC126M01A',
            loadSuccess:function(json){
                $('body').injectData(json);
                Action.FormData = json;
                Action._initData();
                Action.checkLockDoc();
            }
        });
    },
    _initEvent: function(){
        Action._initForm();
        $("#city").setItems({
            item: result_s.counties,
            format: "{key}",
            fn: function(k, v){
                Action.changCityValue($("#city"), $("#dist"));
            }
        });
        $("#dist").change(function(k, v){
            if ($("#dist").val() != "") {
                $("#zip").val($("#dist").val());
            }
        });
    },
    _initData: function(){
        var obj = Action.FormData;
        Action.changCityValue($("#city"), $("#dist"));        
    },
	_init: function(){
        if (!this._isLoad) {
			this.initItem();
            this._initEvent();
            this._isLoad = true;
        } else {
            this._reloadGrid();
        }
		
		ImportAgentLicenseCertificate.init();
    },
    codetypeItem: {},
    /**
     * 初始化下拉選單
     *
     */
    initItem: function(){
        //第一次開啟box
        //產生下拉選單
        var $div = $("#mainPanel").find("[itemType]");
        var allKey = [];
        $div.each(function(){
            allKey.push($(this).attr("itemType"));
        });
        Action.codetypeItem = API.loadCombos(allKey);
        $div.each(function(){
            var $obj = $(this);
            var itemType = $obj.attr("itemType");
            if (itemType) {
                var format = $obj.attr("itemFormat") || "{value} - {key}";
                $obj.setItems({
                    space: $obj.attr("space") || true,
                    item: Action.codetypeItem[itemType],
                    format: format,
                    sort: $obj.attr("itemSort") || "asc",
                    size: $obj.attr("itemSize")
                });
            }
        });
    },
    changCityValue: function(tCity, tArea){
        var value = tCity.val();
        var combos = CommonAPI.loadCombos('counties' + value);

        tArea.setItems({
            item: combos['counties' + value],
            format: '{key}',
            value: Action.FormData['dist'] || ""
        });
    },
    checkLockDoc: function(){
    	//alert("checkReadonly:"checkReadonly() +"-Unit是否相同:"+userInfo.unitNo!=Action.FormData.ownBrId);
        //alert("Auth.readOnly:"+responseJSON.Auth.readOnly+"-responseJSON.mainDocStatus:"+responseJSON.mainDocStatus);
        //alert(auth.readOnly && (responseJSON.mainDocStatus != "01O" & responseJSON.mainDocStatus != "02O"));
//    	var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
//      if ((auth.readOnly && (responseJSON.mainDocStatus != "01O" & responseJSON.mainDocStatus != "03O")) || userInfo.unitNo!=Action.FormData.ownBrId) {
//    		$("#btnSave").hide();
//        	$("#btnCheck").hide();
//            $("form").lockDoc();
//    		_openerLockDoc="1";
//        }
    	if(Action.FormData.docStatusVal != "01O"){
    		$("form").lockDoc();
    		_openerLockDoc="1";
    	}
    	else{
    		if(Action.FormData.isNew){
        		
        	}
        	else{
        		$(".isNew").attr('disabled', 'disabled');
				$("#openButton").hide();
				$("#licenseExp").datepicker("destroy")
        	}
    	}
    	if(Action.FormData.docStatusVal == "02O" || Action.FormData.docStatusVal == "03O"){
    		$("#btnSave").hide();
    	}
    	if(Action.FormData.docStatusVal == "01O" || Action.FormData.docStatusVal == "03O"){
        	$("#btnCheck").hide();
    	}
    }
}

$(document).ready(function(){
	var tabForm = $("#mainPanel");
	Action._init();

	var btnPanel = $("#buttonPanel");
	var initControl_lockDoc = false;

	$("#lnAmt").change(function(){
		calculatRebate();
    });
	
	$("#rebateRatio").change(function(){
		calculatRebate();
    });
	
	
	btnPanel.find("#btnSave").click(function(){
		if(Action.FormData.docStatus == "03O"){
			CommonAPI.confirmMessage(i18n.cls3701m01["haveApprover"], function(b){
                if (b) {
                    saveData(true);
                }
            });
		}
		else{
			saveData(true);
		}
	}).end().find("#btnCheck").click(function(){
	    openCheck();
	});

});

//驗證readOnly狀態
function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    if (auth.readOnly && (responseJSON.mainDocStatus != "01O" & responseJSON.mainDocStatus != "03O")) {
        return true;
    }
    return false;
}

// 儲存的動作
function saveData(showMsg, tofn){

    // 為檢查UI的值是否皆無異常
    if ($("#mainPanel").valid() == false) {
        return;
    }
    
    $.ajax({
        handler: _handler,
        data: $.extend($("#mainPanel").serializeData(), {// 把資料轉成json
                formAction: "saveMain",
                oid: responseJSON.oid,
                showMsg: showMsg
            }),
        success: function(obj){
            $('body').injectData(obj);
            Action.FormData=obj;
            if(obj.msg!=null && obj.msg!=""){
            	return CommonAPI.showErrorMessage(obj.msg);
            }
            else if(obj.tip!=null && obj.tip!=""){
            	return CommonAPI.showMessage(obj.tip);
            }
        }
    });
}

//待覆核 - 覆核
function openCheck(){
	CommonAPI.confirmMessage(i18n.cls3701m01["doApprove"], function(b){
        if (b) {
        	//saveData(false);
        	$.ajax({
                handler: _handler,
                data: $.extend($("#mainPanel").serializeData(), {// 把資料轉成json
                        formAction: "appvoeCase",
                        oids: responseJSON.oid
                    }),
                success: function(obj){
                	CommonAPI.triggerOpener("gridview", "reloadGrid");
                    window.close();
                }
            });
            $.thickbox.close();
        }
    });
}

function calculatRebate(){
	var lnAmt = parseFloat($('#lnAmt').val().replace(/[^\d\.\-]/g, ""));
    var ratio = parseFloat($('#rebateRatio').val().replace(/[^\d\.\-]/g, ""));
    var rebate=ratio*lnAmt*0.01;
    if (ratio!=null && lnAmt!=null) {
    	if(isNaN(rebate)) {
    		$("#rebate").val(0);
    	}
    	else{
    		$("#rebate").val(rebate);
    	}	
    }
	else{
    	$("#rebate").val(0);
    }
}

//引介房仲證書(明)字號
ImportAgentLicenseCertificate = {
	
	init: function(){
		
		ImportAgentLicenseCertificate.loadLicenseInfoGrid();
		
		$("#queryLicenseNoButton").click(function(){
			ImportAgentLicenseCertificate.queryLicenseNoInfo();
		});
		
		$("#updateInquiryDateButton").click(function(){
			ImportAgentLicenseCertificate.updateAgentLicenseInfoStatus();
			$("#agentLicenseInfoGrid").trigger("reloadGrid");
		});
		
		$("#openButton").click(function(){
			$("#agentNameThickBox").val($("#agntName").val());
			ImportAgentLicenseCertificate.openBox();
		});
	},
	
	loadLicenseInfoGrid: function(){
		$("#agentLicenseInfoGrid").iGrid({
			handler: 'cls3701gridhandler',
			action:'queryC126S01A',
			height: 80,
			width: 200,
			postData: {
				'mainId': responseJSON.mainId,
				'agentName': $("#agentName").val()
			},
			colModel: [{
				colHeader: i18n.cls3701m01["C126S01A.realtorName"],//房仲姓名
				width: 20,
				name: 'realtorName',
				align: 'center',
				sortable: true
			}, {
				colHeader: i18n.cls3701m01['C126S01A.license'],//證書(明)字號
				width: 45,
				name: 'licenseFullName',
				sortable: true,
				formatter: 'click',
				onclick : function(cellvalue, options, rowObject){
					$("#licenseYear").val(rowObject.licenseYear);
					$("#licenseNumber").val(rowObject.licenseNumber);
					$("#licenseExp").val(rowObject.licensePeriod);
					$("#agntCompanyName").val(rowObject.estateAgent);
					$("#agntBranchName").val(rowObject.estateAgentOffice);
					$("#licenseWord").val(rowObject.licenseword);
					$.thickbox.close();
				}
			},{
				colHeader: i18n.cls3701m01['C126S01A.licenseExpiration'],//有效期限
				width: 25,
				name: 'licensePeriod',
				align: 'center',
				sortable: true
			},{
				colHeader: i18n.cls3701m01['C126M01A.agentCompanyName'],//任職經紀業名稱
				width: 50,
				name: 'estateAgent',
				sortable: true
			},{
				colHeader: i18n.cls3701m01['C126M01A.agentBranchName'],//任職營業處所名稱
				width: 30,
				name: 'estateAgentOffice',
				sortable: true
			},
			{  name:'licenseYear', hidden: true},
			{  name:'licenseNumber', hidden: true},
			{  name:'licenseword', hidden: true}
			]
		})
	},
	
	queryLicenseNoInfo: function(){
		
		if($("#agntName").val() == undefined || $("#agntName").val() == ''){
			return CommonAPI.showErrorMessage("引介房仲姓名未輸入");
		}
		
		$.ajax({
	        handler: _handler,
			action: "queryAgentLicenseInfo",
	        data: $.extend($("#mainPanel").serializeData(), {// 把資料轉成json
                agentName: $("#agentName").val()
            }),
	        success: function(obj){
				$("#status").val(obj.statusName);
	        }
    	});
	},
	
	updateAgentLicenseInfoStatus: function(){
		
		$.ajax({
	        handler: _handler,
			action: "updateAgentLicenseInfoStatus",
	        data: $.extend($("#mainPanel").serializeData(), {// 把資料轉成json
            }),
	        success: function(obj){
				$("#status").val(obj.statusName);
				$("#remark").val(obj.remark);
	        }
    	});
	},
	
	openBox: function(){

		$("#agentLicenseInfoGrid").trigger("reloadGrid");
		
		var buttons = {};
		buttons[i18n.def.close] = function(){
			$.thickbox.close();
        };

       	$("#openBox_agentLicenseInfo").thickbox({
            title: i18n.cls3701m01['C126S01A.title.agentLicenseCertificateInfo'],
            width: 550,
            height: 250,
            modal: true,
			align: "center",
			valign: 'bottom',
            i18n: i18n.def,
            buttons: buttons
        });
	}
}
