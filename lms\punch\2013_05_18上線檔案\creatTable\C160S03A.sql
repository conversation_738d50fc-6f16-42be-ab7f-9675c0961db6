---------------------------------------------------------
-- LMS.C160S03A 整批自動開戶分戶檔


---------------------------------------------------------
-- TABLE
---------------------------------------------------------
CREATE TABLE LMS.C160S03A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>        CHAR(12)      not null,
	SEQNO         DECIMAL(5, 0) not null,
	INT_CODE      CHAR(02)     ,
	INT_SPRD      DECIMAL(08,6),
	INT_TYPE      CHAR(01)     ,
	INTCHG_TYPE   CHAR(01)     ,
	INTCHG_CYCL   CHAR(01)     ,
	<PERSON><PERSON><PERSON><PERSON>         CHAR(1)      ,
	FUND_TYPE2    CHAR(03)     ,
	<PERSON><PERSON><PERSON><PERSON>        CHAR(1)      ,
	<PERSON><PERSON>_<PERSON>URPOSE    CHAR(1)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>      DECIMAL(3, 0),
	DUE_DT        DATE         ,
	RT_DD         DECIMAL(2, 0),
	NT_RT_DT      DATE         ,
	INT_RT_DD     DECIMAL(2, 0),
	INT_INTRT_DT    DATE         ,
	AVGPAY        CHAR(1)      ,
	EHPAYCPT      DECIMAL(15, 2),
	INTRT_CYCL    CHAR(1)      ,
	AUTORCT       CHAR(1)      ,
	RCTDATE       DATE         ,
	ACCNO         CHAR(14)     ,
	SWFT          CHAR(03)     ,
	RCTAMT        DECIMAL(15, 2) ,
	AUTOPAY       CHAR(1)      ,
	ATPAYNO       CHAR(14)     ,
	CUSTID_S      CHAR(10)     ,
	DUPNO_S       CHAR(1)      ,
	INT_CODE_S    CHAR(02)     ,
	INT_SPRD_S    DECIMAL(08,6),
	INT_TYPE_S    CHAR(01)     ,
	INTCHG_TYPE_S CHAR(01)     ,
	INTCHG_CYCL_S CHAR(01)     ,
	MONTHCNT_S    DECIMAL(3, 0) ,
	DUE_DT_S      DATE         ,
	RT_DD_S       DECIMAL(2, 0) ,
	NT_RT_DT_S    DATE         ,
	INT_RT_DD_S   DECIMAL(2, 0) ,
	INT_INTRT_DT_S  DATE         ,
	AVGPAY_S      CHAR(1)       ,
	EHPAYCPT_S    DECIMAL(15, 2),
	INTRT_CYCL_S  CHAR(1)        ,
	AUTOPAY_S     CHAR(1)        ,
	ATPAYNO_S     CHAR(14)       ,
	ADR_PTR_S     CHAR(2)       ,
	OPH_PTR_S     CHAR(2)       ,
	HPH_PTR_S     CHAR(2)       ,
	MPH_PTR_S     CHAR(2)       ,
	EML_PTR_S     CHAR(2)       ,
	MEMO_S        VARCHAR(60)   ,
	UNPAY_FG_S    CHAR(1)       ,
	CHKYN         CHAR(1)       ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_C160S03A PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
CREATE UNIQUE INDEX LMS.XC160S03A01 ON LMS.C160S03A   (CNTRNO, SEQNO);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C160S03A IS '整批自動開戶分戶檔';
COMMENT ON LMS.C160S03A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	CNTRNO        IS '額度序號', 
	SEQNO         IS '額度案件序號', 
	INT_CODE      IS '利率代碼', 
	INT_SPRD      IS '利率/加減碼', 
	INT_TYPE      IS '利率方式', 
	INTCHG_TYPE   IS '利率變動方式', 
	INTCHG_CYCL   IS '利率變動週期', 
	FNDSRE         IS '資金來源', 
	FUND_TYPE2    IS '資金來源小類', 
	LNPURS        IS '用途別', 
	LN_PURPOSE    IS '融資業務分類', 
	MONTHCNT      IS '貸款總期數', 
	DUE_DT        IS '到期日', 
	RT_DD         IS '還款基準日', 
	NT_RT_DT      IS '下次還款日', 
	INT_RT_DD     IS '扣帳基準日', 
	INT_INTRT_DT    IS '下次扣帳日', 
	AVGPAY        IS '償還方式', 
	EHPAYCPT      IS '每期攤還本金',
	INTRT_CYCL    IS '期付金繳款週期',
	AUTORCT       IS '自動進帳', 
	RCTDATE       IS '進帳日期', 
	ACCNO         IS '存款帳號 (進帳帳號)', 
	SWFT          IS '放款幣別', 
	RCTAMT        IS '進帳金額', 
	AUTOPAY       IS '自動扣帳', 
	ATPAYNO       IS '扣帳帳號', 
	CUSTID_S      IS '客戶編號–分戶', 
	DUPNO_S       IS '重複序號–分戶', 
	INT_CODE_S    IS '利率代碼–分戶', 
	INT_SPRD_S    IS '利率/加減碼–分戶', 
	INT_TYPE_S    IS '利率方式–分戶', 
	INTCHG_TYPE_S IS '利率變動方式–分戶', 
	INTCHG_CYCL_S IS '利率變動週期–分戶', 
	MONTHCNT_S    IS '貸款總期數–分戶', 
	DUE_DT_S      IS '到期日–分戶', 
	RT_DD_S       IS '還款基準日–分戶', 
	NT_RT_DT_S    IS '下次還款日–分戶', 
	INT_RT_DD_S   IS '扣帳基準日-分戶', 
	INT_INTRT_DT_S  IS '下次扣帳日–分戶', 
	AVGPAY_S      IS '償還方式–分戶', 
	EHPAYCPT_S    IS '每期攤還本金–分戶', 
	INTRT_CYCL_S  IS '期付金繳款週期–分戶',
	AUTOPAY_S     IS '自動扣帳–分戶', 
	ATPAYNO_S     IS '扣帳帳號–分戶', 
	ADR_PTR_S     IS '通訊地址指標–分戶', 
	OPH_PTR_S     IS '公司電話指標–分戶', 
	HPH_PTR_S     IS '住家電話指標–分戶', 
	MPH_PTR_S     IS '行動電話指標–分戶',
	EML_PTR_S     IS 'EMAIL指標–分戶',
	MEMO_S        IS '備註–分戶',
	UNPAY_FG_S    IS '扣帳失敗通知方式–分戶',
	CHKYN         IS '資料檢核', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
