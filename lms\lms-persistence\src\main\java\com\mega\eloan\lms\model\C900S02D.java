package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 總處特殊控管額度資料 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C900S02D", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C900S02D extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	
	/** 類別(A:永慶專案40年) **/
	@Size(max=1)
	@Column(name="CATEGORY", length=1, columnDefinition="VARCHAR(1)")
	private String category;
	
	/** 分行別 **/
	@Size(max=3)
	@Column(name="CASEBRID", length=3, columnDefinition="VARCHAR(3)")
	private String caseBrId;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 簽報書案號 **/
	@Size(max=62)
	@Column(name="DOCUMENT_NO", length=62, columnDefinition="VARCHAR(62)")
	private String document_no;
	
	/** 幣別 **/
	@Size(max=3)
	@Column(name="CURR", length=3, columnDefinition="VARCHAR(3)")
	private String curr;

	/** 申請額度金額 **/
	@Column(name = "APPLYAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal applyAmt;
	
	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getCaseBrId() {
		return caseBrId;
	}

	public void setCaseBrId(String caseBrId) {
		this.caseBrId = caseBrId;
	}
	
	public String getCntrNo() {
		return cntrNo;
	}

	public void setCntrNo(String cntrNo) {
		this.cntrNo = cntrNo;
	}

	public String getDocument_no() {
		return document_no;
	}

	public void setDocument_no(String document_no) {
		this.document_no = document_no;
	}

	public String getCurr() {
		return curr;
	}

	public void setCurr(String curr) {
		this.curr = curr;
	}

	public BigDecimal getApplyAmt() {
		return applyAmt;
	}

	public void setApplyAmt(BigDecimal applyAmt) {
		this.applyAmt = applyAmt;
	}

	
}
