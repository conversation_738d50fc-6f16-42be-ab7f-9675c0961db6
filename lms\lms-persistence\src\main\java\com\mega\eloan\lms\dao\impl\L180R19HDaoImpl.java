/* 
 * L180R19HDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L180R19HDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L180R19H;

/** 逾期未覆審歷史檔 **/
@Repository
public class L180R19HDaoImpl extends LMSJpaDao<L180R19H, String> implements
		L180R19HDao {

	@Override
	public L180R19H findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L180R19H> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L180R19H> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L180R19H> findByDocTypeAndDataDate(String docType, Date dataDate) {
		ISearch search = createSearchTemplete();
		List<L180R19H> list = null;
		if (docType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "docType",
					docType);
		if (dataDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataDate",
					dataDate);
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R19H> findByDocTypeDataDateAndBranch(String docType,
			Date dataDate, String branch) {
		ISearch search = createSearchTemplete();
		List<L180R19H> list = null;
		if (docType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "docType",
					docType);
		if (dataDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataDate",
					dataDate);
		if (branch != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);

		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R19H> findByDocTypeDataDateBranchAndCustId(String docType,
			Date dataDate, String branch, String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		List<L180R19H> list = null;
		if (docType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "docType",
					docType);
		if (dataDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataDate",
					dataDate);
		if (branch != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R19H> findByDocTypeDataDateBranchAndCtlType(String docType,
			Date dataDate, String branch, String ctlType) {
		ISearch search = createSearchTemplete();
		List<L180R19H> list = null;
		if (docType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "docType",
					docType);
		if (dataDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataDate",
					dataDate);
		if (branch != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);
		if (ctlType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);

		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R19H> findByDocTypeDataDateAndBranchOrderByDataDate(String docType, String cntrNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docType", docType);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addOrderBy("dataDate");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L180R19H> list = createQuery(search).getResultList();
		return list;		
	}
}