package com.mega.eloan.lms.mfaloan.service.impl;

import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.ArrayUtils;
import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisElf901Service;


@Service
public class MisElf901ServiceImpl extends AbstractMFAloanJdbc implements MisElf901Service {
	
	public static final String[] COLS = { "BRN", "CUSTID", "CUSTNAME", "BANK", "LAW44", "LAW45", "QUERYTX",
											"LAST_TELLER", "LAST_TIME" };
	
	public final int[] SqlTypes = { Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
										Types.CHAR, Types.TIMESTAMP };
	
	@Override
	public int[] addElf901(List<Map<String, Object>> addBeanList) {
		List<int[]> outList = new ArrayList<int[]>();
		for (int i = 0; i < addBeanList.size(); i++) {
			Map<String, Object> map = addBeanList.get(i);
			Object[] array = new Object[COLS.length];
			for (int j = 0; j < COLS.length; j++) {
				array[j] = map.get(COLS[j]) != null ? map.get(COLS[j]) : null;
			}
			List<Object[]> batchValues = new ArrayList<Object[]>();
			batchValues.add(array);
			int[] out1 = getJdbc().batchUpdate("MIS.ELF901.addElf901", SqlTypes, batchValues);
			outList.add(out1);
		}
		if (!outList.isEmpty()) {
			int[] rtn = {};
			for (int[] is : outList) {
				rtn = ArrayUtils.addAll(rtn, is);
			}
			return rtn;
		} else {
			return new int[] { 0 };
		}
	}
}
