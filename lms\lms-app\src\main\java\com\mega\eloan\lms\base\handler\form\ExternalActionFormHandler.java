/* 
 * ExternalActionFormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.handler.form;

import javax.annotation.Resource;


import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.gwclient.EJCICGwClient;
import com.mega.eloan.common.gwclient.EJCICGwReqMessage;
import com.mega.eloan.common.gwclient.ETCHGwClient;
import com.mega.eloan.common.gwclient.ETCHGwReqMessage;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 外部連結
 * </pre>
 * 
 * @since 2012/11/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/1,Fantasy,new
 *          </ul>
 */
@Scope("request")
@Controller("externalactionformhandler")
public class ExternalActionFormHandler extends AbstractFormHandler {

	@Resource
	EJCICGwClient ejcicClient;

	@Resource
	ETCHGwClient etchClient;

	/**
	 * 聯徵URL
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult ejcicUrl(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String sysId = Util.trim(params.getString(UtilConstants.External.系統別));
		String queryId = Util.trim(params
				.getString(UtilConstants.External.查詢ID));
		String prodId = Util.trim(params.getString(UtilConstants.External.產品別));

		EJCICGwReqMessage ejcicReq = new EJCICGwReqMessage();
		ejcicReq.setSysId(Util.isEmpty(sysId) ? UtilConstants.CaseSchema.個金
				: sysId);
		ejcicReq.setMsgId(IDGenerator.getUUID());
		ejcicReq.setEmpid(user.getUserId());
		ejcicReq.setDeptid(user.getUnitNo());
		ejcicReq.setProdid(Util.isEmpty(prodId) ? "P7" : prodId); // P7
		ejcicReq.setQueryid(queryId);

		String url = Util.trim(ejcicClient.getURL(ejcicReq));
		result.set("url", url);

		return result;
	}

	/**
	 * 票信URL
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult etchUrl(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String sysId = Util.trim(params.getString(UtilConstants.External.系統別));
		String queryId = Util.trim(params
				.getString(UtilConstants.External.查詢ID));
		String prodId = Util.trim(params.getString(UtilConstants.External.產品別));

		ETCHGwReqMessage etchReq = new ETCHGwReqMessage();
		etchReq.setSysId(Util.isEmpty(sysId) ? UtilConstants.CaseSchema.個金
				: sysId);
		etchReq.setMsgId(IDGenerator.getUUID());
		etchReq.setEmpid(user.getUserId());
		etchReq.setDeptid(user.getUnitNo());
		etchReq.setProdid(Util.isEmpty(prodId) ? "4111" : prodId); // 4111,4114
		etchReq.setId(queryId);

		String url = Util.trim(etchClient.getURL(etchReq));
		result.set("url", url);

		return result;
	}
}
