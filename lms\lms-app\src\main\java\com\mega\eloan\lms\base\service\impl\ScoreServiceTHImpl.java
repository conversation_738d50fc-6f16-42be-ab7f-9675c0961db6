/* 
 * ScoreServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.Date;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.ScoreTH;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.base.service.ScoreServiceTH;
import com.mega.eloan.lms.eloandb.service.impl.AbstractEloandbJdbc;

import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.Util;



/**
 * <pre>
 * 評分 ServiceImpl
 * </pre>
 * 
 * @since 2012/10/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/30,Fantasy,new
 *          </ul>
 */
@Service("ScoreServiceTH")
public class ScoreServiceTHImpl extends AbstractEloandbJdbc implements
		ScoreServiceTH {
	private static final Logger logger = LoggerFactory.getLogger(ScoreServiceJPImpl.class);
	private static final int OVERSEA_SCALE = 5;
	
	@Resource
	ScoreService scoreService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	//TODO scoreTH
	@Override
	public JSONObject scoreTH(String type, JSONObject data, String varVer, String mowType2, String brNo) {
		JSONObject result = new JSONObject();
		String fileLoc = ScoreTH.設定檔_House_TH_V1_0;	
		
		if(Util.equals(mowType2, OverSeaUtil.海外評等_房貸)){
			if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){
				fileLoc = ScoreTH.設定檔_House_TH_V2_0;	
			}
		}else{//非房貸
			if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){
				fileLoc = ScoreTH.設定檔_notHouse_TH_V2_0;	
			}
		}
				
		JSONObject items = null;
		/*
		 * 邏輯應該是
		 * <1>先有 base基本 → 再算出「初始」評等
		 * <2>依評等 → 對應 DR (若有人工調整評等，要以調整後的評等，去對應)
		 */
		if(Util.equals(type, ScoreTH.type.泰國消金模型基本)){
			if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){
				items = scoreService.parse(ScoreTH.thBase_V2_0.class, fileLoc);	
				if(Util.equals(data.get("item_p3_curr"), "THB")){ //泰國
					JSONObject items_p3_au = scoreService.parse(ScoreTH.thBase_V2_0_P3_TH.class, fileLoc);
					items.putAll(items_p3_au);
				}else if(Util.equals(data.get("item_p3_curr"), "VND")){ //越南
					JSONObject items_p3_au = scoreService.parse(ScoreTH.thBase_V2_0_P3_VN.class, fileLoc);
					items.putAll(items_p3_au);
				}
			}else{
				items = scoreService.parse(ScoreTH.thBase.class, fileLoc);			
			}
		}else if(Util.equals(type, ScoreTH.type.泰國消金模型評等)){
			items = scoreService.parse(ScoreTH.thGrade.class, fileLoc);
		}else if(Util.equals(type, ScoreTH.type.泰國消金模型違約機率)){
			items = scoreService.parse(ScoreTH.thDR.class, fileLoc);
		}
		Properties prop = getProperty(fileLoc);
		
		if (items != null) {		
			_debug("[scoreTH]input_param:"+data);
			if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){
				get_Score_V2_0(items, result, data, prop);
			}else{
				get_Score(items, result, data, prop);
			}
		}

		if(Util.equals(type, ScoreTH.type.泰國消金模型基本)){
			process_TH(data, result, prop, varVer, mowType2, brNo);//裡面 call 計算[評等、違約機率]			
		}

		return result;
	}
	
	
	private void get_Score(JSONObject items, JSONObject result, JSONObject data, Properties prop){
		@SuppressWarnings("unchecked")
		Set<String> set = (Set<String>) items.keySet();
		BigDecimal scr_core = BigDecimal.ZERO;
		BigDecimal val_100 = new BigDecimal("100");
		for (String key : set) {
			JSONObject json = (JSONObject) items.get(key);
			Object determine_val = scoreService.determine(key, json, data);
			_debug("[scoreTH]---af["+key+"]"+determine_val);
			result.put(key, determine_val);
							
			String weightKeyName = "";
			String stdKeyName = "";
			if(Util.equals(key, ScoreTH.thBase.職業_M5)){
				weightKeyName = ScoreTH.column.加權M5;
				stdKeyName = ScoreTH.column.標準化M5;
			}else if(Util.equals(key, ScoreTH.thBase.年資_M7)){
				weightKeyName = ScoreTH.column.加權M7;
				stdKeyName = ScoreTH.column.標準化M7;
			}else if(Util.equals(key, ScoreTH.thBase.借款人及連保人之年收入合計_P3)){
				weightKeyName = ScoreTH.column.加權P3;
				stdKeyName = ScoreTH.column.標準化P3;
			}else if(Util.equals(key, ScoreTH.thBase.個人負債比率_P4)){
				weightKeyName = ScoreTH.column.加權P4;
				stdKeyName = ScoreTH.column.標準化P4;
			}else if(Util.equals(key, ScoreTH.thBase.契約年限_A5)){
				weightKeyName = ScoreTH.column.加權A5;
				stdKeyName = ScoreTH.column.標準化A5;
			}else if(Util.equals(key, ScoreTH.thBase.擔保品地點及種類_Z1)){
				weightKeyName = ScoreTH.column.加權Z1;
				stdKeyName = ScoreTH.column.標準化Z1;
			}else if(Util.equals(key, ScoreTH.thBase.市場環境及變現性_Z2)){
				weightKeyName = ScoreTH.column.加權Z2;
				stdKeyName = ScoreTH.column.標準化Z2;
			}else if(Util.equals(key, ScoreTH.thBase.擔保率_Z3)){
				weightKeyName = ScoreTH.column.加權Z3;
				stdKeyName = ScoreTH.column.標準化Z3;
			}else{
									
			}
			BigDecimal stdItemVal = thItemStdVal(prop, key, CrsUtil.parseBigDecimal(determine_val));
			BigDecimal weightItemVal = CrsUtil.parseBigDecimal(prop.getProperty(weightKeyName));
			result.put(stdKeyName, stdItemVal);
			result.put(weightKeyName, weightItemVal);
			
			scr_core = scr_core.add(stdItemVal.multiply(weightItemVal).divide(val_100));
		}
		scr_core = Arithmetic.round(scr_core, OVERSEA_SCALE);
		
		result.put(ScoreTH.column.合計WeightedScore, scr_core);
		result.put(ScoreTH.column.核心模型分數, thItemStdVal(prop, ScoreTH.column.合計WeightedScore, scr_core));
		
	}
	
	private void get_Score_V2_0(JSONObject items, JSONObject result, JSONObject data, Properties prop){
		@SuppressWarnings("unchecked")
		Set<String> set = (Set<String>) items.keySet();
		BigDecimal scr_core = BigDecimal.ZERO;
		BigDecimal val_100 = new BigDecimal("100");
		for (String key : set) {
			JSONObject json = (JSONObject) items.get(key);
			Object determine_val = scoreService.determine(key, json, data);
			_debug("[scoreTH]---af["+key+"]"+determine_val);
			result.put(key, determine_val);
			
			String weightKeyName = "";
			String weightScrName = "";
			if(Util.equals(key, ScoreTH.thBase_V2_0.職業_M5)){
				weightKeyName = ScoreTH.column.加權M5;
				weightScrName = ScoreTH.column.權重分數M5;
			}else if(Util.equals(key, ScoreTH.thBase_V2_0.年資_M7)){
				weightKeyName = ScoreTH.column.加權M7;
				weightScrName = ScoreTH.column.權重分數M7;
			}else if(Util.equals(key, ScoreTH.thBase_V2_0_P3_TH.借款人及連保人之年收入合計_P3_TH)){
				weightKeyName = ScoreTH.column.加權P3;
				weightScrName = ScoreTH.column.權重分數P3;
			}else if(Util.equals(key, ScoreTH.thBase_V2_0.個人負債比率_P4)){
				weightKeyName = ScoreTH.column.加權P4;
				weightScrName = ScoreTH.column.權重分數P4;
			}else if(Util.equals(key, ScoreTH.thBase_V2_0.契約年限_A5)){
				weightKeyName = ScoreTH.column.加權A5;
				weightScrName = ScoreTH.column.權重分數A5;
			}else if(Util.equals(key, ScoreTH.thBase_V2_0.擔保品地點及種類_Z1)){
				weightKeyName = ScoreTH.column.加權Z1;
				weightScrName = ScoreTH.column.權重分數Z1;
			}else if(Util.equals(key, ScoreTH.thBase_V2_0.市場環境及變現性_Z2)){
				weightKeyName = ScoreTH.column.加權Z2;
				weightScrName = ScoreTH.column.權重分數Z2;
			}else if(Util.equals(key, ScoreTH.thBase_V2_0.年齡_M1)){
				weightKeyName = ScoreTH.column.加權M1;
				weightScrName = ScoreTH.column.權重分數M1;
			}else if(Util.equals(key, ScoreTH.thBase_V2_0.學歷_EDU)){
				weightKeyName = ScoreTH.column.加權edu;
				weightScrName = ScoreTH.column.權重分數edu;
			}else{
									
			}
			//2.0版本不用算標準化(STD)
			//weightItemVal = 權重
			BigDecimal weightItemVal = CrsUtil.parseBigDecimal(prop.getProperty(weightKeyName));
			result.put(weightKeyName, weightItemVal);
			
			BigDecimal scr_decimal = CrsUtil.parseBigDecimal(determine_val);
			//scr_weight = 分數*權重
			BigDecimal scr_weight = scr_decimal.multiply(weightItemVal).divide(val_100);
			result.put(weightScrName, scr_weight);
			scr_core = scr_core.add(scr_weight);
		}
		
		scr_core = Arithmetic.round(scr_core, OVERSEA_SCALE);
		
		result.put(ScoreTH.column.合計WeightedScore, scr_core); // Σ分數*權重
		result.put(ScoreTH.column.核心模型分數, null);
		
		BigDecimal slope = Util.parseBigDecimal(prop.getProperty(ScoreTH.column.斜率));
		BigDecimal interCept = Util.parseBigDecimal(prop.getProperty(ScoreTH.column.截距));
		result.put(ScoreTH.column.截距, interCept);
		result.put(ScoreTH.column.斜率, slope);

		
		double pd = (1 / (1 + Math.exp( interCept.add(slope.multiply(scr_core)).doubleValue())));			
		result.put(ScoreTH.column.預測壞率, Util.Overflow(pd, 4));
	}
	
	
	@Override
	public String get_Version_TH(){
		CodeType activeDate_2_0 = codeTypeService.findByCodeTypeAndCodeValue("ActiveDate_TH", OverSeaUtil.V2_0_LOAN_TH, "zh_TW");
		//UPGRADE
//		String V2_0_TH_ACTIVEDATE = activeDate_2_0.getCodeDesc2();
//		
//		if(LMSUtil.cmpDate(new Date(), ">=", CapDate.parseDate(V2_0_TH_ACTIVEDATE))){
//			//自2023-MM-DD後，改用模型 2.0 
//			return OverSeaUtil.V2_0_LOAN_TH;
//		}else {
//			return OverSeaUtil.V1_0_LOAN_TH; 
//		}
//
////		return "1.0";
		 if (activeDate_2_0 == null) {
             logger.warn("[get_Version_TH] 找不到 ActiveDate_TH/2.0，改用 1.0 模型");
             return OverSeaUtil.V1_0_LOAN_TH;
         }
 
         String actDateStr = Util.trim(activeDate_2_0.getCodeDesc2());
         if (Util.isEmpty(actDateStr)) {
             logger.warn("[get_Version_TH] ActiveDate_TH/2.0.CodeDesc2 為空字串，改用 1.0 模型");
             return OverSeaUtil.V1_0_LOAN_TH;
         }
 
         Date actDate = CapDate.parseDate(actDateStr);
         if (actDate == null) {
             logger.warn("[get_Version_TH] 無法解析啟用日 ({})，改用 1.0 模型", actDateStr);
             return OverSeaUtil.V1_0_LOAN_TH;
         }
 
         return LMSUtil.cmpDate(new Date(), ">=", actDate)
                ? OverSeaUtil.V2_0_LOAN_TH
                : OverSeaUtil.V1_0_LOAN_TH;
	}
	
	private void process_TH(JSONObject data, JSONObject result, Properties prop, String varVer, String mowType2, String brNo) {
		if (prop != null) {
			
			result.put(ScoreTH.column.評等建立日期, CapDate.getCurrentDate(DataParse.DateFormat));
			result.put(ScoreTH.column.模型版本, varVer);
						
			if(true){//評等
				JSONObject level = scoreTH(ScoreTH.type.泰國消金模型評等, result, varVer, mowType2, brNo);
				String pRating = Util.trim(level.get(ScoreTH.thGrade.等級));
				result.put(ScoreTH.column.初始評等, pRating);
				
				//======根據	NCB Report結果進行評等調整====== 此部分僅適用於[泰國]，[越南]跳過
				//欄位初始值設定
				
				Integer sumRiskPt = 0;
				Integer adj_pts = 0;
				String adj_matchS = "N";
				Integer adj_ui = 0;
				Integer sRating = Integer.valueOf(pRating);	
				
				if(LMSUtil.get_TH_BRNO_SET().contains(brNo)){
					sumRiskPt = process_TH_sumRiskPt(data, varVer);
					adj_pts = get_TH_adj_pts_g(sumRiskPt);
					
					if(Util.isNotEmpty(pRating) && Util.isInteger(pRating)){
						//=============
						//依一般警訊的「累加風險點數」降等
						int raw_sRating = Util.parseInt(pRating) - adj_pts;
						if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){
							raw_sRating = OverSeaUtil.rating_min1_max15(raw_sRating);
						}else{
							raw_sRating = OverSeaUtil.rating_min1_max10(raw_sRating);
						}
						//=============
						//依 特殊警訊 判斷上限
						/*
						c120s01e.ncbMaximumDpd, 最大逾期天數區間{0:未逾期,1:逾期30天內 ,2:逾期31-90天,3:超過90天}
						當值=1，觸動 chkItemTHG1(一般警訊1)
						當值=2，觸動 chkItemTHG2(一般警訊2)
						當值=3，觸動 chkItemTHS1(特殊警訊)
						=> 因互斥，當 Adj_matchS=Y, 表示 c120s01e.ncbMaximumDpd=3, 不可能觸動 "一般警訊"
						*/
						if(match_TH_adj_pts_s1(data, varVer)){
							adj_matchS = "Y";
							if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){
								raw_sRating = OverSeaUtil.rating_min14_max15(raw_sRating);
							}else{
								raw_sRating = OverSeaUtil.rating_min9_max10(raw_sRating);
							}
						}
						
						//=============
						//依 其他資訊 升/降等
						/*
						DW_RKCREDITOVS.UI_DG			其他資訊降等數	DEC(2,0)
							泰子行主借款人:無NCB報告時,降2等與第9等之孰差之降等數; 
							泰子行其他關係人不適用,設為0;
							其他國家: NULL
						*/
						if(match_TH_adj_pts_o(data, varVer)){
							/*
							 	有 NCB 會走[一般警訊,特殊警訊]
							 	無 NCB 才走[其他資訊] ⇒ 此時的 adj_pts=0, raw_sRating==pRating
							*/
							//【主借人+無NCB】要取【pRating降2等  v.s. 第9等】孰差
							if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){
								raw_sRating = OverSeaUtil.rating_min14_max15(Util.parseInt(pRating)- (-2));
							}else{
								raw_sRating = OverSeaUtil.rating_min9_max10(Util.parseInt(pRating)- (-2));
							}
							
							//~~~~~~
							adj_ui = Util.parseInt(pRating) - raw_sRating;
						}
						//=============
						sRating = raw_sRating;
					}
				}
				
				
				//================
				if(true){
					result.put(ScoreTH.column.累加風險點數, Util.trim(sumRiskPt));
					result.put(ScoreTH.column.升降等_依風險點數, Util.trim(adj_pts));
					result.put(ScoreTH.column.升降等_觸動特殊警訊, adj_matchS);
					result.put(ScoreTH.column.升降等_依其他資訊, adj_ui);
				}				
				result.put(ScoreTH.column.獨立評等, Util.trim(sRating));
				
				result.put(ScoreTH.column.支援評等, "");
				result.put(ScoreTH.column.調整評等, "");
				result.put(ScoreTH.column.原始最終評等, Util.trim(sRating));
				result.put(ScoreTH.column.最終評等, Util.trim(sRating));
	
				result.put(ScoreTH.column.評等調整日期, "");
				result.put(ScoreTH.column.完成最終評等日期, "");
				result.put(ScoreTH.column.註記不調整, "");
				result.put(ScoreTH.column.調整狀態, "");
				result.put(ScoreTH.column.調整註記, "");
				result.put(ScoreTH.column.調整理由, "");
			
			}
			if(true){//違約機率
				JSONObject thDR = scoreTH(ScoreTH.type.泰國消金模型違約機率, result, varVer, mowType2, brNo);
				setTHDR(thDR, result);
			}
			// clear
			prop.clear();
			prop = null;
		}
	}
	
	@Override
	public void setTHDR(JSONObject thDR, JSONObject target){
		String[] keyArr = {ScoreTH.thDR.違約機率_預估3年期, ScoreTH.thDR.違約機率_預估1年期};
		for(String key: keyArr){
			target.put(key, thDR.get(key));	
		}
	}
	
	private BigDecimal thItemStdVal(Properties prop, String key, BigDecimal factorVal){
		BigDecimal devSampleMean = BigDecimal.ZERO;
		BigDecimal devSampleSTD = BigDecimal.ONE;
		BigDecimal A = BigDecimal.ZERO;
		BigDecimal B = BigDecimal.ZERO;
		if(Util.equals(key, ScoreTH.column.合計WeightedScore)){
			devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdCoreScoreDevSampleMean"));
			devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdCoreScoreDevSampleSTD"));
			A = CrsUtil.parseBigDecimal(prop.getProperty("stdCoreScoreA"));
			B = CrsUtil.parseBigDecimal(prop.getProperty("stdCoreScoreB"));
		}else{
			A = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreA"));
			B = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreB"));
			
			if(Util.equals(key, ScoreTH.thBase.職業_M5)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_m5"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_m5"));				
			}else if(Util.equals(key, ScoreTH.thBase.年資_M7)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_m7"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_m7"));				
			}else if(Util.equals(key, ScoreTH.thBase.借款人及連保人之年收入合計_P3)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_p3"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_p3"));				
			}else if(Util.equals(key, ScoreTH.thBase.個人負債比率_P4)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_p4"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_p4"));				
			}else if(Util.equals(key, ScoreTH.thBase.契約年限_A5)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_a5"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_a5"));				
			}else if(Util.equals(key, ScoreTH.thBase.擔保品地點及種類_Z1)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_z1"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_z1"));				
			}else if(Util.equals(key, ScoreTH.thBase.市場環境及變現性_Z2)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_z2"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_z2"));					
			}else if(Util.equals(key, ScoreTH.thBase.擔保率_Z3)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_z3"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_z3"));					
			}	
		}
		BigDecimal val = CapMath.normalization(factorVal, devSampleMean, devSampleSTD, B, A, MathContext.DECIMAL64);
		return Arithmetic.round(val, OVERSEA_SCALE);
	}
	
	private Integer process_TH_sumRiskPt(JSONObject data, String varVer){
		String have = UtilConstants.haveNo.有;
		
		int sumRiskPt = 0;
		//和 UI 的順序相同，以方便比對 chkItem 對應的點數
		if(Util.equals(Util.trim(data.get(ScoreTH.column.TH一般警訊1)), have)){
			sumRiskPt += 2;
		}
		if(Util.equals(Util.trim(data.get(ScoreTH.column.TH一般警訊2)), have)){
			sumRiskPt += 3;
		}
		return sumRiskPt;
	}
	
	/**	
	不降等	0<=points<2
	降1等	2<=points<3
	降2等	points>=3
	*/
	private int get_TH_adj_pts_g(Integer sumRiskPt){
		int adj = 0;
		if(sumRiskPt==null){
			
		}else if(sumRiskPt>=0 && sumRiskPt<2){
			adj = 0;
		}else if(sumRiskPt>=2 && sumRiskPt<3){
			adj = -1;
		}else if(sumRiskPt>=3){
			adj = -2;
		}
		return adj;
	}
	
	private boolean match_TH_adj_pts_s1(JSONObject data, String varVer){		
		String have = UtilConstants.haveNo.有;
		if(Util.equals(Util.trim(data.get(ScoreTH.column.TH特殊警訊1)), have)){
			return true;
		}
		return false;
	}
	
	private boolean match_TH_adj_pts_o(JSONObject data, String varVer){		
		String have = UtilConstants.haveNo.有;

		//判斷是否[主借人]且[無NCB]
		if(Util.equals(Util.trim(data.get(ScoreTH.column.TH其他資訊3)), have)){
			return true;
		}
		return false;
	}
	
	private static Properties getProperty(String name) {
		Properties prop = new Properties();
		InputStream stream = Thread.currentThread().getContextClassLoader()
				.getResourceAsStream(name);
		try {
			prop.load(stream);
		} catch (IOException e) {
			logger.error("[Properties.load]",e);
		} finally {
			try {
				if (stream != null)
					stream.close();
			} catch (IOException e) {
				logger.error("[InputStream.close]",e);
			}
		}
		return prop;
	}
	
	private void _debug(String s){
		
	}
	
	public boolean getModelType_2_0(String loanTP) {
		/*Step1.判斷要引入的模型為房貸 or 非房貸
		 * 若會計科目為[1260-5000_273短期房屋購置擔保放款、1350-6200_473中期房屋購置擔保放款、1350-6300_474中期房屋修繕擔保放款、
		 * 1450-1500_673長期房屋購置擔保放款、1450-2000_674長期房屋修繕擔保放款、1260-6000_???房屋修繕住宅貸款(這個科目在系統及科目表都找不到，先不管)]
		 * 其餘引用[非房貸模型]
		*/
		//loanTP=會計科目
		Map<String, String> modelTypeMap = codeTypeService.findByCodeType("modelType_TH_M");
		if(modelTypeMap.get(loanTP) != null ){
			return true;
		}
			return false;
	}
	
}