package com.mega.eloan.lms.rpt.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapString;

import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.rpt.service.CLS180R50Service;
import com.mega.sso.service.BranchService;

/**
 * <pre>
 * 共同行銷維護作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class CLS180R50ServiceImpl extends AbstractCapService implements CLS180R50Service {

	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	BranchService branchService;
	
	@Override
	public Map<String, Map<String, Object>> getStatisticsDataForMonthlyReport(String branchNo, Properties prop){
		
		Map<String, Map<String, Object>> returnMap = new LinkedHashMap<String, Map<String, Object>>();
		
		List<Map<String, Object>> aloanList = this.misdbBASEService.getLandOrBuildLoanCntrNoDataForMonthlyReport(branchNo);
		
//		String groupLoanMasterNoString = "";
		List<String> groupLoanMasterNoList = new ArrayList<String>();
		for(Map<String, Object> map : aloanList){
			
			String landOrBuildCntrNo = CapString.trimNull(map.get("ELF447N_CONTRACT"));//土建融額度序號
			String groupLoanMasterNo = CapString.trimNull(map.get("GRPCNTRNO"));
			
			returnMap.put(landOrBuildCntrNo, map);
			
			if(!CapString.isEmpty(groupLoanMasterNo)){
//				groupLoanMasterNoString += "'" + groupLoanMasterNo + "',";
				groupLoanMasterNoList.add(groupLoanMasterNo);
			}
		}
		
//		groupLoanMasterNoString = !"".equals(groupLoanMasterNoString)
//								? groupLoanMasterNoString.substring(0, groupLoanMasterNoString.length()-1)
//								: groupLoanMasterNoString;
		
		Map<String, Object> accSendMoneyAmountMap = this.getAccumulatedMortgageAppropriationAmount(groupLoanMasterNoList.toArray(new String[0]));
		
		for(String landOrBuildCntrNo : returnMap.keySet()){
			
			Map<String, Object> aMap = returnMap.get(landOrBuildCntrNo);
			String groupLoanMasterNo = CapString.trimNull(aMap.get("GRPCNTRNO"));
			
			Object accMortgageAmount = accSendMoneyAmountMap.get(groupLoanMasterNo);
			if(null != accMortgageAmount){
				aMap.put("ACC_MORTGAGE_AMOUNT", accMortgageAmount);//房貸累積撥款金額
			}
		}

		for(String landOrBuildCntrNo : returnMap.keySet()){
			
			Map<String, Object> map = returnMap.get(landOrBuildCntrNo);
			
			String productTypeName = "33".equals(map.get("ELF447N_PROD_CLASS")) ? prop.getProperty("CLS180R50.landFinancing") : prop.getProperty("CLS180R50.buildFinancing");
			String branchName = branchService.getBranchName(CapString.trimNull(map.get("ISSUEBRNO")));
			BigDecimal accAppropriationAmount = LMSUtil.nullToZeroBigDecimal(map.get("ACC_MORTGAGE_AMOUNT"));//房貸累積撥款金額
			BigDecimal buildCaseApprovedQuota = LMSUtil.nullToZeroBigDecimal(map.get("TOTAMT"));//建案核准額度
			
			BigDecimal mortgageUsedRatio = buildCaseApprovedQuota.compareTo(BigDecimal.ZERO) == 0 
										? BigDecimal.ZERO
										: accAppropriationAmount.divide(buildCaseApprovedQuota, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2);

			map.put("PRODUCT_TYPE", productTypeName);//產品類別
			map.put("BRANCH_NAME", branchName);
			map.put("MORTGAGE_USED_RATIO", mortgageUsedRatio + "%");//房貸動用率
			map.put("LAND_CONS_CNTRNO", map.get("ELF447N_CONTRACT"));//土/建融額度序號
		}
		
		return returnMap;
	}
	
	private Map<String, Object> getAccumulatedMortgageAppropriationAmount(String[] groupLoanMasterNoArr){
		
		List<Map<String, Object>> accSendMoneyAmountlist = new ArrayList<Map<String, Object>>();
		
		if(groupLoanMasterNoArr != null && groupLoanMasterNoArr.length > 0){
			
			int size = -1;
			while(size == -1){
				List<Map<String, Object>> list = this.eloandbBASEService.getAccumulatedMortgageAppropriationAmount(groupLoanMasterNoArr);
				size = list.size();
				accSendMoneyAmountlist.addAll(list);
			}
		}
		
		Map<String, Object> accSendMoneyAmountMap = new HashMap<String, Object>();
		for(Map<String, Object> aMap : accSendMoneyAmountlist){
			String groupLoanMasterNo = CapString.trimNull(aMap.get("GRPCNTRNO"));
			accSendMoneyAmountMap.put(groupLoanMasterNo, aMap.get("ACC_MORTGAGE_AMOUNT"));
		}
		
		return accSendMoneyAmountMap;
	}
	
	@Override
	public int setTitleContent(WritableSheet sheet, Map<String, Integer> titleMap, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}

		for(String title : titleMap.keySet()){
			this.setCellsMergeFormat(sheet, fromColIndex, toColIndex, fromRowIndex, toRowIndex, prop.getProperty(title), cellFormat);
		}
		
		return ++toRowIndex;
	}
	
	@Override
	public int setHeaderContent(WritableSheet sheet, Map<String, Integer> headerMap, Properties prop, int colIndex, int rowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}

		for(String header : headerMap.keySet()){
			this.setCellsFormat(sheet, headerMap.get(header), prop.getProperty(header), colIndex++, rowIndex, cellFormat);
		}
		
		return ++rowIndex;
	}
	
	
	
	@Override
	public int setBodyContent(WritableSheet sheet, Map<String, Map<String, Object>> dataMap, int colIndex, int rowIndex) throws RowsExceededException, WriteException{
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		for(String landOrBuildCntrNo : dataMap.keySet()){
			Map<String, Object> map = dataMap.get(landOrBuildCntrNo);
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CNAME")), cellFormat));//建設公司名稱
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CUSTID")), cellFormat));//統一編號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("PRODUCT_TYPE")), cellFormat));//產品種類
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("LAND_CONS_CNTRNO")), cellFormat));//土/建融額度序號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("LNF022_END_DATE")), cellFormat));//土/建融動用到期日
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("PROJECTNM")), cellFormat));//建案名稱
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("GRPCNTRNO")), cellFormat));//整批房貸額度序號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("MORTGAGE_USED_RATIO")), cellFormat));//房貸動用率
			colIndex = 0;
			rowIndex++;
		}
		
		return rowIndex;
	}
	
	@Override
	public int setBodyContentForHeadOffice(WritableSheet sheet, Map<String, Map<String, Object>> dataMap, int colIndex, int rowIndex) throws RowsExceededException, WriteException{
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		for(String landOrBuildCntrNo : dataMap.keySet()){
			Map<String, Object> map = dataMap.get(landOrBuildCntrNo);
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ISSUEBRNO")), cellFormat));//分行代號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("BRANCH_NAME")), cellFormat));//分行名稱
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CNAME")), cellFormat));//建設公司名稱
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CUSTID")), cellFormat));//統一編號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("PRODUCT_TYPE")), cellFormat));//產品種類
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("LAND_CONS_CNTRNO")), cellFormat));//土/建融額度序號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("LNF022_END_DATE")), cellFormat));//土/建融動用到期日
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("PROJECTNM")), cellFormat));//建案名稱
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("GRPCNTRNO")), cellFormat));//整批房貸額度序號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("MORTGAGE_USED_RATIO")), cellFormat));//房貸動用率
			colIndex = 0;
			rowIndex++;
		}
		
		return rowIndex;
	}
	
	@Override
	public void setFooterContent(WritableSheet sheet, Map<String, Integer> footerMap, Properties prop, int rowIndex) throws WriteException{
		
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.LEFT);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
//			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		for(String footer : footerMap.keySet()){
			this.setCellsMergeFormat(sheet, 0, 6, rowIndex, rowIndex, prop.getProperty(footer), cellFormat);
		}
	}
	
	private void setCellsFormat(WritableSheet sheet, int width, String content, int colIndex, int rowIndex, WritableCellFormat cellFormat) throws RowsExceededException, WriteException{
		sheet.setColumnView(colIndex, width);//設定欄寬
		sheet.addCell(new Label(colIndex, rowIndex, content, cellFormat));
	}
	
	private void setCellsMergeFormat(WritableSheet sheet, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, 
										String content, WritableCellFormat cellFormat) throws RowsExceededException, WriteException{
		
		sheet.mergeCells(fromColIndex, fromRowIndex, toColIndex, toRowIndex);
		sheet.addCell(new Label(fromColIndex, fromRowIndex, content, cellFormat));
	}
}
