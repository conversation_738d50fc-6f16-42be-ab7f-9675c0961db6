# Log4J configuring file for application logging
#
# Define the default log level, and the default appenders
# LEVEL : OFF\u3001FATAL\u3001ERROR\u3001WARN\u3001INFO\u3001DEBUG\u3001ALL
#log4j.rootLogger=INFO,Stdout

log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.conversionPattern=%d [%t] [%X{sessionId}] | %X{login} | %X{reqURI} | %-28.28c{1} [%-5p] %m%n
#log4j.appender.console.layout.conversionPattern=%d[%-5p]%m%n
logs.dir=/elnfs/logs/${hostName}/lms

log4j.appender.FILE.encoding=UTF-8
log4j.appender.FILE=org.apache.log4j.RollingFileAppender
log4j.appender.FILE.File=${logs.dir}/lms-web.log
log4j.appender.FILE.MaxFileSize=10240KB
log4j.appender.FILE.MaxBackupIndex=200
log4j.appender.FILE.layout=org.apache.log4j.PatternLayout
log4j.appender.FILE.layout.ConversionPattern=%d [%t] [%X{sessionId}] | %X{login} | %X{reqURI} | %-28.28c{1} [%-5p] %m%n

# ----- EL\u81ea\u8a02\u7684FileAppender(\u5b50\u7cfb\u7d71/\u55ae\u4f4d\u4ee3\u78bc/\u4f7f\u7528\u8005\u4ee3\u78bc/\u65e5\u671f/log\u6a94\u540d.log)
log4j.appender.ELLOG=com.mega.eloan.common.log.EloanFileAppender
log4j.appender.ELLOG.file=${logs.dir}/lms.log
log4j.appender.ELLOG.root=${logs.dir}
log4j.appender.ELLOG.userId=login
log4j.appender.ELLOG.branchNo=unitno
log4j.appender.ELLOG.maxFileSize=10240KB
log4j.appender.ELLOG.maxBackupIndex=100
log4j.appender.ELLOG.encoding=UTF-8
log4j.appender.ELLOG.layout=org.apache.log4j.PatternLayout
log4j.appender.ELLOG.layout.ConversionPattern=%d [%t] [%X{sessionId}] | %X{login} | %X{reqURI} | %-28.28c{1} [%-5p] %m%n

# ----- APPLICATION
#log4j.rootLogger=TRACE,console
log4j.rootLogger=DEBUG,FILE,ELLOG


log4j.logger.org=ERROR
log4j.logger.net=ERROR
log4j.logger.tw.com.iisi=DEBUG
log4j.logger.tw.com.iisi.cap.ajax.CapAjaxBehavior=TRACE
log4j.logger.tw.com.iisi.cap.base.pages.AbstractCapPage=TRACE
log4j.logger.tw.com.iisi.cap.jawr.CapJawrRequestHandler=ERROR
log4j.logger.tw.com.iisi.cap.utils.CapBeanUtil=WARN
log4j.logger.com.mega.eloan.common.gwclient.MonitorClient=ERROR


## -- jdbc gateway
log4j.logger.com.mega.eloan.lms.dw.service.impl=TRACE
log4j.logger.com.mega.eloan.lms.eai.service.impl=TRACE
log4j.logger.com.mega.eloan.lms.ejcic.service.impl=TRACE
log4j.logger.com.mega.eloan.lms.eloandb.service.impl=TRACE
log4j.logger.com.mega.eloan.lms.etch.service.impl=TRACE
log4j.logger.com.mega.eloan.lms.mfaloan.service.impl=TRACE
log4j.logger.com.mega.eloan.lms.obsdb.service.impl=TRACE
log4j.logger.com.mega.eloan.common.service.impl=TRACE
## -- Batch
log4j.logger.com.mega.eloan.common.batch=TRACE
# ----- OPENJPA
log4j.logger.openjpa.Tool=WARN
log4j.logger.openjpa.Runtime=WARN
log4j.logger.openjpa.Remote=WARN
log4j.logger.openjpa.DataCache=WARN
log4j.logger.openjpa.MetaData=WARN
log4j.logger.openjpa.Enhance=WARN
log4j.logger.openjpa.Query=WARN
log4j.logger.openjpa.jdbc.SQL=TRACE
log4j.logger.openjpa.jdbc.JDBC=WARN
log4j.logger.openjpa.jdbc.Schema=WARN

# ----- OTHER OPEN SOURCE PACKAGES
# avoid misleading log "No service named XXX is available"
# More on this topic: http://wiki.apache.org/ws/FrontPage/Axis/DealingWithCommonExceptions

log4j.logger.org.springframework.security=WARN
log4j.logger.org.apache.commons=WARN
log4j.logger.org.apache.velocity=WARN
log4j.logger.org.springframework=WARN

log4j.logger.org.springframework.beans.factory=WARN
log4j.logger.org.springframework.beans.factory.support=WARN
log4j.logger.org.springframework.transaction=WARN
log4j.logger.org.springframework.orm.hibernate3=WARN

# ----- JAWR
log4j.logger.net.jawr=ERROR

# ----- WICKET
log4j.logger.org.apache.wicket=WARN
org.apache.wicket.util.watch.ModificationWatcher=ERROR
#log4j.logger.org.apache.wicket.protocol.http.HttpSessionStore=INFO
#log4j.logger.org.apache.wicket.version=WARN
#log4j.logger.org.apache.wicket.RequestCycle=WARN

# ----- CXF
log4j.logger.org.apache.cxf.interceptor.LoggingOutInterceptor=INFO

# ----- SUBSTITUTE SYMBOL
# %c Logger, %c{2 } last 2 partial names
# %C Class name (full agony), %C{2 } last 2 partial names
# %d{dd MMM yyyy HH:MM:ss } Date, format see java.text.SimpleDateFormat, If no date format specifier is given then ISO8601 format is assumed.
# %F File name
# %l Location (caution: compiler-option-dependently)
# %L Line number
# %m user-defined message
# %M Method name
# %p Level
# %r Milliseconds since program start
# %t Threadname
# %x, %X see Doku
# %% individual percentage sign
# Caution: %C, %F, %l, %L, %M slow down program run!

