package com.mega.eloan.lms.cls.service;

/* 
 * CLS1021Service.java
 * 
 * Copyright (c) 2011-2013 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.C120M01A;



/**
 * <pre>
 *  線上申貸原始資料
 * </pre>
 * 
 * @since 2013/01/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/07,Gary<PERSON><PERSON>,new
 *          </ul>
 */
public interface CLS1200Service extends AbstractService {

	/**
	 * 
	 * @param search
	 *            提供給grid handler的搜尋條件
	 * @return Page<C122M01A>
	 */
	
	Page<C120M01A> getC120V01(ISearch search);
}