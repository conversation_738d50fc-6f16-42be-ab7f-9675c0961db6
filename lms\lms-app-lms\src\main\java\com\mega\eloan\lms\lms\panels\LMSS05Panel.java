/* 
 * CLS1205S05Panel.java
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.panels;

import com.mega.eloan.common.panels.Panel;

/**<pre>
 * 說明(個金&授權內企金共用)
 * </pre>
 * @since  2012/1/20
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/1/20,<PERSON>,new
 *          </ul>
 */
public class LMSS05Panel extends Panel {

	public LMSS05Panel(String id) {
		super(id);
	}

	public LMSS05Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

}
