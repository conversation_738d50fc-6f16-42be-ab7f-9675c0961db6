var panelAction = {
    handler: 'cls1161m02formhandler',
    gridhandler: 'cls1161gridhandler',
    grid: null,
    init: function(){
    },
    build: function(){
        //匯入EXCEL
        $('body').find('#btCaseType3').click(function(){
            panelAction.caseType = '3';
            panelAction.openCaseType3();
        });
                
        //build button
        $('#CaseType3Form').find('#downloadExcel').click(function(){
        	$.ajax({
        		type : "POST",
        		handler: panelAction.handler,
        		action: 'checkImportStatus',
        		data: {
        			oid: responseJSON.oid
        		},
        		success: function(responseData){
        			if (responseData.importExcelStatus !== "04") {
        				CommonAPI.triggerOpener("gridview", "reloadGrid");
        				API.showPopMessage(responseData.msg);
        				return;
        			}else{
        				//下載EXCEL
        				$.capFileDownload({
        					data: {
        						fileOid: $('#excelId').val()
        					}
        				});
        			}
        		}
        	});
        }).end().find('#btUploadExcel').click(function(){            
            $.ajax({
        		type : "POST",
        		handler: panelAction.handler,
        		action: 'checkImportStatus',
        		data: {
        			mainId: responseJSON.mainId
        		},
        		success: function(responseData){
        			if (responseData.importExcelStatus == "01" || responseData.importExcelStatus == "02") {
        				CommonAPI.triggerOpener("gridview", "reloadGrid");
        				API.showPopMessage(responseData.msg);
        				return;
        			}else{
        				//上傳EXCEL
        	            MegaApi.uploadDialog({
        	                fieldId: "fileName",
        	                width: 320,
        	                height: 100,
        	                fileCheck: ['.xls'],
        	                data: {
        	                    deleteDup: true,
        	                    uid: responseJSON.oid, //避免此檔案被列為此文件之附加檔案
        	                    mainId: responseJSON.mainId
        	                },
        	                success: function(response){
        	                    var $form = $('#CaseType3Form');
        	                    $form.find('#downloadExcel').show();
        	                    $form.find('#excelId').val(response.fileKey);
        	                    //$form.find('#progress').html('初始化中，請稍後...'); //finish 100%
        	                    //$form.find('#progressTr').show();
        	                    panelAction.importExcelStep();
        	                }
        	            });
        			}
        		}
        	});
            
        }).end().find('#selectCaseNo').click(function(){
            var sendData = $.extend($('#CaseType3Form').serializeData(), {
                caseType: panelAction.caseType
            });
            panelAction.openPTEAMAPP(sendData);
        });
    },
    /**
     * 匯入EXCEL
     */
    openCaseType3: function(){
        //下載EXCEL
        $('#progressTr').hide();
        $('#downloadExcel').hide();
        if ($('#excelId').val()){ 
            $('#downloadExcel').show();
        }
        //=================
        var btnObj = {};
        //在開啟 tb 前,都是隱藏的. 不能用:visible 來判斷
        if( $("#btUploadExcel").length>0 ){
			btnObj['saveData'] = function() {
				if ($('#CaseType3Form').valid()) {
                    MegaApi.confirmMessage(i18n.def["actoin_001"], function(action){
                        if (action) {
                            panelAction.importExcel({});
                        }
                    });
                }	
            };
		}
		btnObj['close'] = function() {
		   	$.thickbox.close();
		};
		//=================
        $('#CaseType3ThickBox').thickbox({
            title: i18n.cls1161s21['button.btCaseType3'],
            width: 600,
            height: 450,
            align: 'center',
            valign: 'bottom',
            buttons: btnObj
        });
    },
    /**
     * 匯入EXCEL step
     */
    importExcelStep: function(data){
        var $form = $('#CaseType3Form');
        var progress = 10;
        //delayProgress(progress);
        var my_timeout = 1000 * 60 * 120;//120分鐘
        if(true){//先延長 timer，不然在處理過程中，會 timeout
			timer_long_ajax_beg(my_timeout);	
		}
        
        $.ajax({
            handler: panelAction.handler,
            action: 'importExcelStep1',
            formId: 'CaseType3Form',
            data: data || {},
            timeout: my_timeout, 
            success: function(response){
            	if(true){//恢復 timer
            		timer_long_ajax_end();
				}
            	
            	var $form = $('#CaseType3Form');
                if (response.CaseType3Form) {
                    $form.setValue(response.CaseType3Form);
                }
                progress = 100;
                //finish progress
                //$form.find('#progress').html(progress);
                //MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['runSuccess']);
                $.thickbox.close();
                MegaApi.showPopMessage(i18n.def["confirmTitle"], "匯入EXCEL作業處理中，請稍後檢視");
                $form.find('#progressTr').hide().end().find("#btUploadExcel").hide();
                //更新opener
                CommonAPI.triggerOpener("gridview", "reloadGrid");
            },
            error: function(){
                MegaApi.showPopMessage(i18n.def["confirmTitle"], "上傳錯誤請聯絡資訊處!");
                $.thickbox.close();
            }
        });
    },
    
    /**
     * 匯入EXCEL (確定)
     */
    importExcel: function(data){
        $.thickbox.close();
        MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['runSuccess']);
        pageAction.init();
    }
};
function delayProgress(progress){
    setTimeout(function(){
        if (progress < 100) {
            $('#CaseType3Form').find('#progress').html(progress += 5);
            delayProgress(progress);
        }
    }, 800);
}

$(function(){
    panelAction.build();
    panelAction.init();
});
