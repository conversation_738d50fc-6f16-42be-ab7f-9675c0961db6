/* 
 * AbstractETchJdbc.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.etch.service.impl;

import javax.annotation.Resource;
import javax.sql.DataSource;

import tw.com.iisi.cap.context.CapParameter;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.jdbc.EloanJdbcTemplate;

/**
 * <pre>
 * G/W擷取票交所資料資料
 * </pre>
 * 
 * @since 2011/9/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/30,tammyChen,new
 *          </ul>
 */
public class AbstractETchJdbc {

	public final String DEFAULT_CHARSET = "MS950";

	EloanJdbcTemplate jdbc;

	@Resource(name = "etchSqlStatement")
	CapParameter sqlp;

	@Resource(name = "etch-db")
	public void setDataSource(DataSource dataSource) {
		jdbc = new EloanJdbcTemplate(dataSource, GWException.GWTYPE_ETCH);
		jdbc.setSqlProvider(sqlp);
		jdbc.setCauseClass(this.getClass());
	}

	/**
	 * getJdbc
	 * 
	 * @return EloanJdbcTemplate
	 */
	public EloanJdbcTemplate getJdbc() {
		return jdbc;
	}

	/**
	 * 取得Sql
	 * @param sqlId sqlId
	 * @return sqlString
	 */
	public String getSqlBySqlId(String sqlId) {
		return sqlp.getValue(sqlId);
	}

}
