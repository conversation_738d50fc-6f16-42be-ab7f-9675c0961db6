var initDfd = initDfd || new $.Deferred();
var initAll = initAll || new $.Deferred();
FormAction.open = false;
//J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
var g_lmsLgdCountTotal = 5;
var g_editCountSize = 1;
var dfd1, dfd2, dfd3, dfd4, beforeCheckGridDef, l140m01eAmtGridDef;
var l140m01eAmtGridDef = new $.Deferred();
var inits = {
    fhandle: "lms1401m01formhandler",
    ghandle: "lms1401gridhandler",
    action: "queryL140m01a",
    itemType: "1",//案件簽報書的種類 1額度明細表、2額度批覆表、3母行法人提案意見
    toreadOnly: false,//用來控制是否要readOnly
    defButton: {
        "close": function(){
            $.thickbox.close();
        }
    }
};

function changedistanceWord(number, mainWidth, otherWidth){
    if ($('#pageNum' + number).val() == '0') {
        $('#itemDscr' + number).attr('distanceWord', mainWidth);
    }
    else {
        $('#itemDscr' + number).attr('distanceWord', otherWidth);
    }
}

// J-112-0148 疫後振興
function isResueItemCaseF(){
    var rescueItem = $("#isRescueSpan").find("#rescueItem").val();
    var isFVal = false;
    // LMS_RESCUEITEM_CASE_F 參數調整這裡也要改
    var fVals = ["F01", "F02", "F03", "F04", "F05", "F06", "F07", "F08", "F09", "F10"];
    // == -1 代表沒找到
    if ($.inArray(rescueItem, fVals) != -1) {
        isFVal = true;
    }
    return isFVal;

    /*
    $.ajax({
        handler: inits.fhandle,
        action: "isResueItemCaseF",
        data: {
             rescueItem : rescueItem
        },
        success: function(responseData){
            var isFVal = responseData.isResueItemCaseF;
            return isFVal;
        }
    });
    */
}

// J-112-0226 0403花蓮地震融資保證專案貸款
function isResueItemCaseJ(){
    var rescueItem = $("#isRescueSpan").find("#rescueItem").val();
    var isFVal = false;
    // LMS_RESCUEITEM_CASE_J 參數調整這裡也要改
    var fVals = ["J01","J02","J03","J04","J05","J06","J07","J08","J09"];
    // == -1 代表沒找到
    if ($.inArray(rescueItem, fVals) != -1) {
        isFVal = true;
    }
    return isFVal;
}

function isResueItemCaseL(){
    var rescueItem = $("#isRescueSpan").find("#rescueItem").val();
    var isFVal = false;
    // LMS_RESCUEITEM_CASE_J 參數調整這裡也要改
    var fVals = ["L01","L02","L03"];
    // == -1 代表沒找到
    if ($.inArray(rescueItem, fVals) != -1) {
        isFVal = true;
    }
    return isFVal;
}

/** 聯行攤貸比例grid  */
function l140m01eAmtGrid(){
    $("#l140m01eAmtGrid").iGrid({
        handler: inits.ghandle,
        rowNum: 10,
        postData: {
            formAction: "queryL140m01e",
            tabFormMainId: $("#tabFormMainId").val()
        },
        rowNum: 10,
        autowidth: true,
        colModel: [{
            name: 'shareRate2',
            hidden: true
        }, {
            colHeader: i18n.lms1401s02["L140M01e.shareBrId"],//"攤貸分行",
            name: 'shareBrId',
            align: "left",
            width: 110,
            sortable: true
        }, {
            colHeader: i18n.lms1401s02["L140M01e.shareAmt"],//"攤貸金額",
            name: 'shareAmt',
            width: 160,
            sortable: true,
            align: "right",
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 2
            }
        }, {
            colHeader: i18n.lms1401s02["L140M01e.shareRate1"],//"攤貸比例",
            width: 140,
            name: 'showRate',
            align: "right",
            sortable: true
        }, {
            colHeader: i18n.lms1401s02["L140M01a.cntrNo"],//"額度序號",
            width: 140,
            name: 'shareNo',
            sortable: true
        }, {
            name: 'oid',
            hidden: true
        }]
    });
}//close l140m01eAmtGrid fn(x)

/** J-109-0152 保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構 **/
function gridviewCrdGrade(){
    $("#crdGradeGrid").iGrid({
        handler: 'lms1810gridhandler',
        height: 270,
        postData: {
            queryType: "L140",
            fcrdType: "1",
            fcrdArea: "1",
            fcrdPred: "1",
            formAction: "query_elfFcrdGrad"
        },
        needPager: false,
        shrinkToFit: true,
        colModel: [{ colHeader: '評等等級', name: 'ratingGrad', sortable: false, align: "left" }]
    });

	// 取得信評相關欄位 下拉選單
	$.ajax({
	    handler: inits.fhandle,
	    action: "queryCrdSelect"
	}).done(function(responseData) {
		if(responseData.selItem != "" && responseData.selItem != undefined
		    && responseData.selItem != null){
		    $.each(responseData.selItem, function(itemName, kvMap){
		        var chooseItem = $("#cga_" + itemName);
		        $("#cga_" + itemName + " option").remove();   // 移除全部的項目
		        var _addSpace = false;
		        if (chooseItem.attr("space") == "true") {
		            _addSpace = true;
		        }
		        var _fmt = "{key}";
		        if (chooseItem.attr("myShowKey") === "Y") {
		            _fmt = "{value}" + sep + "{key}";
		        }
		        chooseItem.setItems({
		            item: convertItems(kvMap),
		            format: _fmt,
		            clear: false,
		            space: _addSpace
		        });
		    });
		    // 限定 國際&長期
		    $("#cga_crdArea").val("1").prop('disabled', true);
		    $("#cga_crdPred").val("1").prop('disabled', true);
		}
	});
}

/**  登錄性質的格式化  */
function proPertyFormatter(cellvalue, otions, rowObject){
    var itemName = '';
    
    if (cellvalue) {
        var list = cellvalue.split("|");
        itemName = i18n.lms1401s02["L140M01a.type" + list[0]];
        if (cellvalue.length > 1) {
            for (var i = 1; i < list.length; i++) {
                var itemone = i18n.lms1401s02["L140M01a.type" + list[i]];
                itemName = itemName + "、" + itemone;
            }//close for
        }//close if
    }
    return itemName;
}

/** J-112-0417 e-Loan簽報書新增高品質專案融資判斷欄位 取得高品質最終結果 */
function getHighQualityResult(){
	if($("input[name^='isHighQualityProjOpt_']:checked").length < 5){
		//沒填妥 放空值 
		$("#isHighQualityProjResult").val("");
		$("#div_hqProjResHighQualityProjectFinace").hide();
		$("#div_hqProjResProjectFinace").hide();
	}else if($("input[name^='isHighQualityProjOpt_'][value='Y']:checked").length == 5){
		//高品質融資選項5個都勾 >> 顯示「高品質專案融資」，適用80%風險權數
		$("#isHighQualityProjResult").val("Y");
		$("#div_hqProjResHighQualityProjectFinace").show();
		$("#div_hqProjResProjectFinace").hide();
	}else{//不屬於高品質專案融資，顯示「專案融資」，適用100%風險權數
		$("#isHighQualityProjResult").val("N");
		$("#div_hqProjResHighQualityProjectFinace").hide();
		$("#div_hqProjResProjectFinace").show();		
	}
}

var btObjectDefault = API.createJSON([{
    key: i18n.lms1401s02['btn.special'],
    value: function(){
        CntrNoAPI.special();
    }
}, {
    key: i18n.def['print'],
    value: function(){
        CntrNoAPI.pre_Print_CntrDoc(false, false);
    }
}, {
    key: i18n.def['close'],
    value: function(){
        //當為readOnly狀態不詢問是否關閉
        if (!inits.toreadOnly) {
            API.confirmMessage(i18n.def['flow.exit'], function(res){
                if (res) {
                    //$.thickbox.close();
                    window.close();
                }
            });
        }
        else {
            //$.thickbox.close();
            window.close();
        }
    }
}, {
    key: i18n.lms1401s02['btn.printW03'],
    value: function(){
        CntrNoAPI.print_Doc("W03", true, false);
    }
}, {
    key: i18n.lms1401s02['btn.printW04'],
    value: function(){
        CntrNoAPI.print_Doc("W04", true, false);
    }
}, {
    key: i18n.lms1401s02['btn.printW01'],
    value: function(){
        CntrNoAPI.print_Doc("W01", true, false);
    }
}]);
var btObject03 = API.createJSON([{
    key: i18n.lms1401s02['btn.toMoneyData'],
    value: function(){
        CntrNoAPI.getMoneyData();
    }
}, {
    key: i18n.lms1401s02['btn.special'],
    value: function(){
        CntrNoAPI.special();
    }
}, {
    key: i18n.def['saveData'],
    value: function(){
        CntrNoAPI.save_CntrDoc(true);
    }
}, {
    key: i18n.def['del'],
    value: function(){
        CntrNoAPI.delete_CntrDoc();
    }
}, {
    key: i18n.def['print'],
    value: function(){
        CntrNoAPI.pre_Print_CntrDoc(true, true);
    }
}, {
    key: i18n.def['close'],
    value: function(){
        //當為readOnly狀態不詢問是否關閉
        if (!inits.toreadOnly) {
            API.confirmMessage(i18n.def['flow.exit'], function(res){
                if (res) {
                    CntrNoAPI._triggerMainGrid();
                    //$.thickbox.close();
                    window.close();
                }
            });
        }
        else {
            //$.thickbox.close();
            window.close();
        }
    }
}, {
    key: i18n.lms1401s02['btn.printW03'],
    value: function(){
        CntrNoAPI.print_Doc("W03", true, false);
    }
}, {
    key: i18n.lms1401s02['btn.printW04'],
    value: function(){
        CntrNoAPI.print_Doc("W04", true, false);
    }
}, {
    key: i18n.lms1401s02['btn.printW01'],
    value: function(){
        CntrNoAPI.print_Doc("W01", true, false);
    }
}]);

var btObject11 = API.createJSON([{
    key: i18n.lms1401s02['btn.special'],
    value: function(){
        CntrNoAPI.special();
    }
}, {
    key: i18n.def['saveData'],
    value: function(){
        CntrNoAPI.save_CntrDoc(true);
    }
}, {
    key: i18n.def['del'],
    value: function(){
        CntrNoAPI.delete_CntrDoc();
    }
}, {
    key: i18n.def['print'],
    value: function(){
        CntrNoAPI.pre_Print_CntrDoc(true, true);
    }
}, {
    key: i18n.def['close'],
    value: function(){
        //當為readOnly狀態不詢問是否關閉
        if (!inits.toreadOnly) {
            API.confirmMessage(i18n.def['flow.exit'], function(res){
                if (res) {
                    CntrNoAPI._triggerMainGrid();
                    window.close();
                }
            });
        }
        else {
            window.close();
        }
    }
}, {
    key: i18n.lms1401s02['btn.printW03'],
    value: function(){
        CntrNoAPI.print_Doc("W03", true, false);
    }
}, {
    key: i18n.lms1401s02['btn.printW04'],
    value: function(){
        CntrNoAPI.print_Doc("W04", true, false);
    }
}, {
    key: i18n.lms1401s02['btn.printW01'],
    value: function(){
        CntrNoAPI.print_Doc("W01", true, false);
    }
}]);

//額度明細表內程式
var CntrNoAPI = {
    //授信戶為銀行法或金控法利害關係人
	//J-104-0219-001 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
    needUnsecureFlag: "",
    setNeedUnsecureFlag: function(data){
		if (data && data.needUnsecureFlag) {			
            this.needUnsecureFlag = data.needUnsecureFlag;
        }
    },
    /**
     * J-104-0219-001 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
     * 授信戶是否為銀行法或金控法利害關係人
     */
    isNeedUnsecureFlag: function(){
        if (this.needUnsecureFlag == "Y") {
            return true;
        }
        return false;
    },
    /**
     * 清空欄位值並隱藏
     * @param {Object} $obj jquery 物件
     */
    cleanTrHideInput: function($obj){
        $obj.hide();
        $obj.find("input,select").each(function(){
            var item = $(this).attr("type");
            if (item) {
                switch (item.toLowerCase()) {
                    case "text":
                    case "hidden":
                    case "password":
                        $(this).val("");
                        break;
                    case "radio":
                    case "checkbox":
                        $(this).removeAttr("checked");
                        break;
                    default:
                        $(this).val("");
                        break;
                }
            }
            else {
                $(this).val("")
            }
        });
    },
    mainGridId: "#gridviewC_2",
    /**  
     * 判斷是否已有額度明細表
     * return  true ,false
     */
    isCheckGrid: function(){
        var countGrid = $(CntrNoAPI.mainGridId).jqGrid('getGridParam', 'records');
        if (countGrid == 0) {
            //L140M01a.error38=尚未登錄額度明細表，無法執行此動作
            CommonAPI.showMessage(i18n.lms1401s02['L140M01a.error38']);
            return false;
        }
        return true;
    },
    // J-108-0316_05097_B1001 Web e-Loan修改總處營 業單位授權外簽報流程
    reloadRandomCodeSbr : function (){
		
         $("#showRandomCodeSbr").hide();
         $.ajax({
             handler: inits.fhandle,
             action: "getRandomCodeSbr",
             data: {
                 mainId: responseJSON.mainId
             }
		 }).done(function(obj) {
			if(obj.showRandomCodeSbr =="Y"){
				$("#showRandomCodeSbr").show();
				$("#randomCodeSbr").val(obj.randomCodeSbr);
			}else{
				$("#showRandomCodeSbr").hide();
			}
         });
         
	},
    /**
     * 觸發主檔Grid更新
     * @param {Object} isOutSide 是裡層還是外層
     */
    _triggerMainGrid: function(isOutSide){
        if (isOutSide) {
            $(CntrNoAPI.mainGridId).trigger('reloadGrid');
        }
        else {
            API.triggerOpener(CntrNoAPI.mainGridId.substring(1));
        }
        // J-108-0316_05097_B1001 Web e-Loan修改總處營 業單位授權外簽報流程
        CntrNoAPI.reloadRandomCodeSbr();
        
    },
    
	
    /**  額度序號給號 
     *
     * @param {String} ownBrId 分行號碼
     * @param {String} unitCode 區部別 DBU=1 OBU=4 海外=5
     * return 額度序號
     */
    newCntrNo: function(ownBrId, unitCode){
        var number;
        $.ajax({
            async: false,
            handler: inits.fhandle,
            action: "queryNewCntrNo",
            data: {//把資料轉成json
                tabFormId: $("#tabFormId").val(),
                ownBrId: ownBrId,
                unitCode: unitCode,
                classCD: "0"
            }
		}).done(function(obj) {
			CntrNoAPI._triggerMainGrid();
			number = obj.cntrNo;
			var $form02 = $("#L140M01AForm2")
			$form02.find("#showCntrNoName").html(obj.branchName);
        }); //close ajax
        return number;
    },
    /**
     *	查詢原案額度序號
     * @param {String } originalText 原案額度序號
     * @param {String } justSave 1.為查詢舊額度序號2.儲存預約額度序號 3,聯行攤貸比例
     * return {Object}cntrNo 額度序號",ownBrName 額度序號前三碼分行名稱
     */
    queryOriginalCntrNo: function(originalText, justSave){
        //驗證舊有額度序號規則
        if (!originalText.match(/\w{12}/)) {
            //L140M01a.message68=額度序號長度應為12碼，編碼原則:XXX(分行代號)+X(1:DBU,4:OBU,5:海外)+YYY(年度)+99999(流水號)
            return CommonAPI.showMessage(i18n.lms1401s02["L140M01a.message68"]);
        }
        var queryObj = {};
        $.ajax({
            handler: inits.fhandle,
            async: false,
            action: "checkCntrno",
            data: {
                cntrNo: originalText,
                justSave: justSave || "1",
                snoKind: $("#L140M01AForm2").find("#snoKind").val(),
                oid: $("#tabFormId").val()
            }
		}).done(function(obj) {
			queryObj.cntrNo = originalText.toUpperCase();
			queryObj.ownBrName = obj.ownBrName;
			//當錯誤碼有值則不帶額度序號資料
			if (justSave == "1" && obj.error) {
			    queryObj.cntrNo = "";
			    queryObj.ownBrName = "";
			    queryObj.error = obj.error;
			    //原始額度控管種類
			    queryObj.snoKindOld = obj.snoKindOld;
			    queryObj.snoKindOldShow = obj.snoKindOldShow;
			    //原始額度序號 
			    queryObj.snoOld = obj.snoOld;
			    
			}
			if (justSave != "3") {
			    CntrNoAPI._triggerMainGrid();
			}
        });
        return queryObj;
    },
    /**
     *	複製額度明細表	篩選條件
     * @param {String } caseType 1.額度明細表 ,2.聯行額度明細表,3.引進簽報書
     */
    copyFilterType: function(caseType){
        //欄位初始化
        $("[name=copyValueRadio][value=1]").click();
        $("#otherTextid,#selectFilterBrno").val("");
        //帶入預設分行
        $("#selectFilterBrno").val(userInfo ? userInfo.unitNo : "");
        var title = "";
        switch (caseType) {
            case 1:
                title = i18n.lms1401s02["title.04"];
                break;
            case 2:
                title = i18n.lms1401s02["btn.openNewList3"];
                break;
            case 3:
                //L140M01a.message107=引進簽報書
                title = i18n.lms1401s02["L140M01a.message107"];
                break;
        }
        
        
        $("#copyvalueBox").thickbox({
            //title.04 =複製額度明細表,btn.openNewList3=轉入聯行額度明細表
            title: title,
            width: 430,
            height: 250,
            modal: true,
            readOnly: false,
            align: "center",
            i18n: i18n.def,
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var brNo = $("#selectFilterBrno").val();
                    var id = "";
                    var targetData = $("#dataFrom");
                    //1.列出本案借戶項下所有額度明細表 2.列出特定借戶項下所有額度明細表 3.列出所有額度明細表
                    var get = $("[name=copyValueRadio]:checked").val();
                    switch (get) {
                        case "1":
                            id = $("#nowSelectPerson").val();
                            if (id == "") {
                                //請選擇本案借款人
                                return CommonAPI.showMessage(i18n.lms1401s02["L140M01a.message10"]);
                            }
                            targetData.html($("#nowSelectPerson :selected").text());
                            break;
                        case "2":
                            //TODO 判斷ID輸入身分證格式是否正確(是否要加上判斷是否有這個id)
                            id = $("#otherTextid").val();
                            if (!$.trim(id)) {
                                //L140M01a.message11=請輸入複製來源之借款人統一編號(加上重覆序號)
                                return CommonAPI.showMessage(i18n.lms1401s02["L140M01a.message11"]);
                            }
                            targetData.html(id);
                            break;
                        case "3":
                            id = "";
                            break;
                        default:
                            //action_004=請先選擇需「調閱」之資料列
                            return CommonAPI.showMessage(i18n.def["action_004"]);
                            break;
                    }
                    $.thickbox.close();
                    switch (caseType) {
                        case 1:
                        case 2:
                            $("#gridviewcopyvalueNow").jqGrid("setGridParam", {//重新設定grid需要查到的資料
                                postData: {
                                    formAction: "queryCopyFitlePeole",
                                    type: get,
                                    brNo: brNo,
                                    borrowId: id,
                                    copyType: caseType
                                },
                                search: true
                            }).trigger("reloadGrid");
                            CntrNoAPI.copyvalueNow(caseType);
                            break;
                            
                        //                            $("#gridviewtrans").jqGrid("setGridParam", {//重新設定grid需要查到的資料
                        //                                postData: {
                        //                                    formAction: "queryL141m01a",
                        //                                    type: get,
                        //                                    brNo: brNo,
                        //                                    borrowId: id
                        //                                },
                        //                                search: true
                        //                            }).trigger("reloadGrid");
                        //                            CntrNoAPI.transvalue();
                        //                            break;
                        case 3:
                            CntrNoAPI.copySrcByL120M01A(brNo, id);
                            break;
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    	
         
    },
    /**
     * 產生額度明細表
     * @param {String } action
     *
     */
    creatCntrDoc: function(){
    	
    	//J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
    	$.ajax({
            handler: inits.fhandle,
            action: "queryCreateCntrDocInitData",
            data: {}
		}).done(function(obj) {
			 
			if(obj.showPrintButtonList_8 == "Y"){
				$("#printButtonList").find("#showPrintButtonList_8").show();
			}else{
				$("#printButtonList").find("#showPrintButtonList_8").hide();
			}

			//J-110-0429_05097_B1001 配合內政部紓困方案，增列內政部紓困方案代號及相關條件
			if(obj.showPrintButtonList_10 == "Y"){
				$("#printButtonList").find("#showPrintButtonList_10").show();
			}else{
				$("#printButtonList").find("#showPrintButtonList_10").hide();
			}

			$("#printButtonList").thickbox({
			    //title.05 =產生額度明細表
			    title: i18n.lms1401s02["title.05"],
			    width: 600,
			    height: 400,
			    modal: true,
			    align: "center",
			    i18n: i18n.def,
			    readOnly: false,
			    valign: "bottom",
			    buttons: {
			        "sure": function(){
			            //get取得目前使用者產生明細表所用的方法
			            var get = $("[name=printButtonType]:checked").val();
			            switch (get) {
			                case "1":
			                    $.ajax({
			                        handler: inits.fhandle,
			                        action: "queryL120s01a",
			                        data: {}
								}).done(function(obj) {
									$.thickbox.close();
									//如果當登陸的借款人只有一位直接開啟畫面
									if (obj.size == 1) {
									    CntrNoAPI.opendocBox("", "", obj.oid);
									}
									else {
									    //選擇借款人
									    CntrNoAPI.borrowvalue();
									}
			                    }); //close ajax
			                    break;
			                case "2":
			                    $.thickbox.close();
			                    //複製額度明細表grid
			                    dfd3.resolve();
			                    CntrNoAPI.copyFilterType(1);
			                    break;
			                case "3":
			                    dfd3.resolve();
			                    $.thickbox.close();
			                    CntrNoAPI.copyFilterType(2);
			                    break;
			                case "4":
			                    $.thickbox.close();
			                    CntrNoAPI.copyFilterType(3);
			                    break;
			                case "5":
			                    $.thickbox.close();
			                    //選擇借款人
			                    CntrNoAPI.borrowvalue(true,"");
			                    break;
			                case "6":
			                	//J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			                    $.thickbox.close();
			                    //選擇借款人
			                    CntrNoAPI.borrowvalue(true,"001");
			                    break;  
			                case "7":
			                	//J-109-0241_05097_B1001 Web e-loan國內企金授信新增經濟部B方案(營運週轉金)搭配央行A、央行B專案融通專用明細表
			                    $.thickbox.close();
			                    //選擇借款人
			                    $("#choiceCntrDocA02Type").thickbox({
			                        title: "",
			                        width: 200,
			                        height: 200,
			                        modal: true,
			                        align: "center",
			                        valign: "bottom",
			                        i18n: i18n.def,
			                        buttons: {
			                            "sure": function(){
			                            	var caseSubType = $("input[name=printButtonType_A02]:checked").val();
			                            	$.thickbox.close();
			                            	CntrNoAPI.borrowvalue(true,"A02",caseSubType);
			                            	
			                            },
			                            "cancel": function(){
			                                $.thickbox.close();
			                            }
			                        }
			                    });
			                    
			                    break; 
			                case "8":
			                	//J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
			                    $.thickbox.close();
			                    //選擇借款人
			                    CntrNoAPI.borrowvalue(true,"002");
			                    break;     
			                case "9":
			                	//J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
			                    $.thickbox.close();
			                    //選擇借款人
			                    $("#choiceCntrDocA07Type").thickbox({
			                        title: "",
			                        width: 200,
			                        height: 200,
			                        modal: true,
			                        align: "center",
			                        valign: "bottom",
			                        i18n: i18n.def,
			                        buttons: {
			                            "sure": function(){
			                            	var caseSubType = $("input[name=printButtonType_A07]:checked").val();
			                            	$.thickbox.close();
			                            	CntrNoAPI.borrowvalue(true,"A07",caseSubType);
			                            	
			                            },
			                            "cancel": function(){
			                                $.thickbox.close();
			                            }
			                        }
			                    });
			                    
			                    break;  
			                case "10":
			                	//J-110-0429_05097_B1001 配合內政部紓困方案，增列內政部紓困方案代號及相關條件
			                    $.thickbox.close();
			                    //選擇借款人
			                    CntrNoAPI.borrowvalue(true,"I01");   
			                    break;
			                case "11":
			                    // J-112-0148 疫後振興
			                    $.thickbox.close();
			                    //選擇借款人
			                    CntrNoAPI.borrowvalue(true,"F02");
			                    break;
			                case "12":
			                    // J-112-0148 疫後振興
			                    $.thickbox.close();
			                    //選擇借款人
			                    CntrNoAPI.borrowvalue(true,"F07");
			                    break;    
			            }//close switch
			        },
			        "cancel": function(){
			            $.thickbox.close();
			        }
			    }
			});



        }); //close ajax
    	
    	
    	
        
    },
    /**
     * 選擇借款人
     * @param {Object} isCreatOld 是否為產生舊案
     */
    borrowvalue: function(isCreatOld,caseType,caseSubType){
    	
    	$("#borrowvalue").thickbox({
            //title.06= 借款人選擇
            title: i18n.lms1401s02['title.06'],
            width: 480,
            height: 400,
            modal: true,
            align: "center",
            i18n: i18n.def,
            readOnly: false,
            valign: "bottom",
            buttons: {
                "sure": function(){
                	//J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
                	var rowId = $("#gridviewborrow").getGridParam('selrow');
                    if (rowId) {
                        $.thickbox.close();
                        // 帶借款人資料到下個方法
                        var data = $("#gridviewborrow").getRowData(rowId);
                        if (isCreatOld) {
                            $.ajax({
                                handler: inits.fhandle,
                                action: "createOldL140M01A",
                                data: {
                                    custOid: data.oid,
                                    caseType : caseType,
                                    caseSubType: caseSubType
                                }
							}).done(function(obj) {
								CntrNoAPI._triggerMainGrid(true);


								//J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
								//若有引進小規模銀行簡易評分表，要把評分表的資信簡表連結寫到相關文件
								if(obj.CESMAINID != undefined && obj.CESMAINID != ""){
									$.ajax({ // 查詢主要借款人資料
										handler : "lms1101formhandler",
										type : "POST",
										dataType : "json",
										data : {
											formAction : "findRelate2",
											formL120m01e : "{}",
											mainId : responseJSON.mainId,
											cesMainId : obj.CESMAINID,
											notShowMsg : 'Y'
										}
									}).done(function(json) {
									});
								}
                            });
                        }
                        else {
                            CntrNoAPI.opendocBox("", "", data.oid);
                        }
                    }
                    else {
                        //L140M01a.error02=請選擇借款人
                        CommonAPI.showMessage(i18n.lms1401s02['L140M01a.error02']);
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    
    
    /**
     * 額度明細表複製來源選擇
     * @param {Object} copyType  1-複製額度明細表 2-轉入聯行額度明細表
     */
    copyvalueNow: function(copyType){
        var isCopy1 = (copyType == "1");
        if (isCopy1) {
            $("#theCopyPersonSpan").show();
        }
        else {
            $("#theCopyPersonSpan").hide();
        }
        $("#copyvalueNowBox").thickbox({
            //title.03 =額度明細表複製來源選擇
            //title.02 =聯行額度明細表選擇
            title: isCopy1 ? i18n.lms1401s02['title.03'] : i18n.lms1401s02["title.02"],
            width: 800,
            height: 380,
            modal: true,
            align: "center",
            i18n: i18n.def,
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var allSelect = $("#gridviewcopyvalueNow").getGridParam('selarrrow');
                    var allSelectOid = new Array(allSelect);
                    var copySelect = [];
                    
                    
                    
                    if (allSelect != "") {
                    	
                    	//J-110-0195_05097_B1001 Web e-Loan 企金授信額度明細表新增以整批匯入方式填列
                        for (var i in allSelect) {
                            allSelectOid[i] = $("#gridviewcopyvalueNow").getRowData(allSelect[i]).oid;
                        }
                        copySelect = $("#theCopyPerson :selected").text().split(" ");
                        
                        var copyCntrDocTo = $("[name=copyCntrDocTo]:checked").val();
                        
                        if( copyCntrDocTo == 'B' ){
                        	
                        	//透過EXCEL 上傳
                        	//lmscopycntrdocfileuploadhandler
                        	if (isCopy1) {
                                if ($("#uploadCopyCntrDocByXls").val() == "") {
                                    //L140M01a.message12 =此份額度明細表的「借款人」欄位要設定為何人
                                    return CommonAPI.showMessage("請先選擇額度明細表的「借款人」匯入EXCEL清單。");
                                }
                            }
                        	
                        	 var fileSize = 10 * 1024 * 1024;
                             var s = $.extend({
                                 handler: 'lmscopycntrdocfileuploadhandler',
                                 fieldId: "uploadCopyCntrDocByXls",
                                 title: i18n && i18n.def.insertfile || "請選擇附加檔案",
                                 fileCheck: ['xls'],
                                 successMsg: false,
                                 success: function(){
                                 },
                                 data: {
                                     fileSize: fileSize,
                         			 mainId : responseJSON.mainId,
                         			 deleteDup : true,
                                     changeUploadName: "uploadCopyCntrDocByXls.xls"
                                 }
                             }, s);
                             
                             
                             $.capFileUpload({
                                 handler: s.handler,
                                 fileCheck: s.fileCheck,
                                 fileElementId: s.fieldId,
                                 successMsg: s.successMsg,
                                 data: $.extend({
                                 	fieldId: "uploadCopyCntrDocByXls",
                                 	copyType: copyType,
                                    list: allSelectOid,
                                    copyCntrDocTo : copyCntrDocTo,
                                    CaseType: inits.itemType//文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
             	                }, s.data || {}),
                                 success: function(json){
                                     $.thickbox.close();
                                     API.showPopMessage("上傳成功"); 
                                     CntrNoAPI._triggerMainGrid(true);
                                 }
                             });
                        	
                        }else{
                        	
                        	if (isCopy1) {
                                if ($("#theCopyPerson :selected").val() == "") {
                                    //L140M01a.message12 =此份額度明細表的「借款人」欄位要設定為何人
                                    return CommonAPI.showMessage(i18n.lms1401s02["L140M01a.message12"]);
                                }
                            }
                        	
                        	for (var i in allSelect) {
                                allSelectOid[i] = $("#gridviewcopyvalueNow").getRowData(allSelect[i]).oid;
                            }
                            copySelect = $("#theCopyPerson :selected").text().split(" ");
                            
                        	$.ajax({
                                handler: inits.fhandle,
                                action: "copyL140m01a",
                                data: {
                                    copyType: copyType,
                                    list: allSelectOid,
                                    main: copySelect, //要變更後的借款人名稱
                                    CaseType: inits.itemType//文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
                                }
							}).done(function(obj) {
								//J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
								//若有引進小規模銀行簡易評分表，要把評分表的資信簡表連結寫到相關文件
								if(obj.CESMAINID != undefined && obj.CESMAINID != ""){
									$.ajax({ // 查詢主要借款人資料
										handler : "lms1101formhandler",
										type : "POST",
										dataType : "json",
										data : {
											formAction : "findRelate2",
											formL120m01e : "{}",
											mainId : responseJSON.mainId,
											cesMainId : obj.CESMAINID,
											notShowMsg : 'Y'
										}
									}).done(function(json) {
									});
								}
                            });
                        }
                        
                        CntrNoAPI._triggerMainGrid(true);
                        $.thickbox.close();
                    }
                    else {
                        //action_004=請先選擇需「調閱」之資料列
                        return CommonAPI.showMessage(i18n.def["action_004"]);
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    copySrcByL120M01AGrid: null,
    
    /**
     *引進簽報書
     * @param {Object} brNo 分行代號
     * @param {Object} id 客戶統編
     */
    copySrcByL120M01A: function(brNo, id){
        if (!this.copySrcByL120M01AGrid) {
            this.copySrcByL120M01AGrid = $("#copyL120M0AGrid").iGrid({
                handler: inits.ghandle,
                height: 230,
                rownumbers: true,
                multiselect: false,
                hideMultiselect: true,
                sortname: 'caseDate|caseNo',
                sortorder: 'desc|desc',
                rowNum: 10,
                postData: {
                    formAction: "querySrcByL120M01A",
                    custId: id,
                    brNo: brNo
                },
                colModel: [{
                    colHeader: i18n.lms1401s02["L140M01a.caseDate"],//簽案日期,
                    name: 'caseDate',
                    align: "center",
                    width: 80,
                    sortable: false
                }, {
                    colHeader: i18n.lms1401s02["L140M01a.mainPerson"],//"主要借款人",
                    name: 'custName',
                    width: 120,
                    sortable: false
                }, {
                    colHeader: i18n.lms1401s02["L140M01a.caseNum"],//"案號",
                    name: 'caseNo',
                    width: 160,
                    sortable: false
                }, {
                    colHeader: i18n.lms1401s02["L140M01a.manger"],//"經辦",
                    name: 'updater',
                    width: 80,
                    sortable: false,
                    align: "center"
                }, {
                    colHeader: "oid",
                    name: 'oid',
                    hidden: true
                }, {
                    colHeader: "mainId",
                    name: 'mainId',
                    hidden: true
                }]
            });
        }
        else {
            this.copySrcByL120M01AGrid.reload({
                custId: id,
                brNo: brNo
            });
        }
        $("#copyL120M0ABox").thickbox({
            //L140M01a.message107=引進簽報書
            title: i18n.lms1401s02["L140M01a.message107"],
            width: 680,
            height: 440,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $grid = CntrNoAPI.copySrcByL120M01AGrid;
                    //單筆
                    var rowData = $grid.getSingleData();
                    if (rowData) {
                        $.thickbox.close();
                        CntrNoAPI.copySrcByL140M01ABox(rowData.mainId);
                    }
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
        
    },
    copySrcByL140M01AGrid: null,
    /**
     * 選擇額度明細表
     * @param {Object} L120M01A_mainId 簽報書mainId
     */
    copySrcByL140M01ABox: function(L120M01A_mainId){
        if (!this.copySrcByL140M01AGrid) {
            this.copySrcByL140M01AGrid = $("#copyL140M0AGrid").iGrid({
                handler: inits.ghandle,
                height: 230,
                rownumbers: true,
                sortname: 'printSeq|custId|cntrNo',
                sortorder: 'asc|asc|asc',
                multiselect: true,
                rowNum: 10,
                postData: {
                    formAction: "queryL140m01aByL120M01ASrc",
                    itemType: inits.itemType,
                    mainId: L120M01A_mainId
                },
                colModel: [{
                    colHeader: i18n.lms1401s02["L140M01a.custName"],//借款人名稱
                    width: 140,
                    name: 'custName',
                    sortable: true
                }, {
                    colHeader: i18n.lms1401s02["L140M01a.cntrNo"],//"額度序號",
                    name: 'cntrNo',
                    width: 80,
                    sortable: true
                }, {
                    colHeader: i18n.lms1401s02["L140M01a.cntrNoCom"],//"共用額度序號",
                    name: 'commSno',
                    width: 80,
                    sortable: true
                }, {
                    colHeader: "&nbsp;",
                    name: 'currentApplyCurr',
                    width: 25,
                    sortable: true,
                    align: "center"
                }, {
                    colHeader: i18n.lms1401s02["L140M01a.moneyAmt"],//現請額度,
                    name: 'currentApplyAmt',
                    width: 100,
                    sortable: true,
                    align: "right",
                    formatter: 'currency',
                    formatoptions: {
                        thousandsSeparator: ",",
						removeTrailingZero: true,
                        decimalPlaces: 2//小數點到第幾位
                    }
                }, {
                    colHeader: i18n.lms1401s02["L140M01a.type"],//"性質"
                    name: 'proPerty',
                    width: 70,
                    sortable: true,
                    align: "center",
                    formatter: proPertyFormatter
                }, {
                    colHeader: i18n.lms1401s02["L140M01a.docStatus"], //"文件狀態",
                    name: 'docStatus',
                    width: 60,
                    sortable: true,
                    align: "center"
                }, {
                    colHeader: i18n.lms1401s02["L140M01a.branchId"],//"分行別",
                    name: 'ownBrId',
                    width: 80,
                    sortable: true,
                    align: "center"
                }, {
                    colHeader: i18n.lms1401s02["L140M01a.dataDrc"],//L140M01a.dataDrc=來源
                    name: 'dataSrc',
                    width: 50,
                    sortable: true,
                    align: "center"
                }, {
                    colHeader: "&nbsp",//"檢核欄位",
                    name: 'chkYN',
                    width: 20,
                    sortable: true,
                    align: "center"
                }, {
                    name: 'oid',
                    hidden: true
                }]
            });
        }
        else {
            this.copySrcByL140M01AGrid.reload({
                mainId: L120M01A_mainId
            });
        }
        
        $("#copyL140M0ABox").thickbox({
            title: "",
            width: 900,
            height: 380,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $grid = CntrNoAPI.copySrcByL140M01AGrid;
                    //多筆
                    var rowData = $grid.getSelectData("oid");
                    if (rowData) {
                        $.ajax({
                            handler: inits.fhandle,
                            action: "copySrcByL140M01A",
                            data: {
                                oldCaseMainId: L120M01A_mainId,
                                //案件簽報書mainId
                                caseMainId: $("#mainId").val(),
                                oids: rowData
                            },
                            success: function(obj){
                                CntrNoAPI._triggerMainGrid(true);
                            }
                        });
                    }
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**  轉入聯行額度明細表 */
    transvalue: function(){
        $("#transvalue").thickbox({
            //title.02 =聯行額度明細表選擇
            title: i18n.lms1401s02["title.02"],
            width: 680,
            height: 400,
            modal: true,
            readOnly: false,
            i18n: i18n.def,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var SelectId = $("#gridviewtrans").getGridParam('selrow');
                    var selectMainid = $("#gridviewtrans").getRowData(SelectId).srcMainId;
                    if (!SelectId) {
                        //action_004=請先選擇需「調閱」之資料列
                        return CommonAPI.showMessage(i18n.def["action_004"]);
                    }
                    $.ajax({
                        handler: inits.fhandle,
                        action: "copyL140m01a",
                        data: {
                            //原案件簽報書mainId
                            selectMainid: selectMainid
                        },
                        success: function(obj){
                            CntrNoAPI._triggerMainGrid(true);
                        }
                    });
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**  額度明細表主檔  */
    opendocBox: function(bb, oid, data){
    
        if (!data.oid) {
            var custId = data;
            data = {};
            data.custId = custId;
        }
        data.page = responseJSON.page;
        data.mainId = responseJSON.mainId;
        data.srcMainId = $("#srcMainId").val();
        data._openerLockDoc = _openerLockDoc;
        data.mainDocStatus = responseJSON.mainDocStatus;
        //(108)第 3230 號
        data.areaChk = responseJSON.areaChk;  
        $.form.submit({
            url: "../lms1401m01page",
            data: data,
            target: data.oid ? data.oid : "new"
        });
        
        return;
    },
    /**  額度明細表主檔另開新頁後，載入資料  */
    opendocBoxInit: function(){
		dfd2 = dfd2 || $.Deferred();
    	gSbjProperty = false;
        $("#mainId").val(responseJSON.mainId);
        $("#srcMainId").val(responseJSON.srcMainId);
        //把文件已開啟的狀態傳進來
        _openerLockDoc = responseJSON._openerLockDoc;
        

        //載入頁面
        if ($("#loadPanel").attr("openFlag") != "Y") {
            //額度明細表grid 
            dfd2.resolve();
            l140m01eAmtGridDef.done(l140m01eAmtGrid);//選擇將餘額攤貸金額grid
            $("#loadPanel").load("../lms/lms1401m01", function() {
                CntrNoAPI.opentionFn();
                //聯貸說明
                $("input[name='unitCase2']").change(function(){
                    if ($("input[name=unitCase2]:checked").val() == "Y") {
                        $("#countSayTr").show();
                    }
                    else {
                        $("#countSayTr").hide();
                    }
                });
				
                
                $("#mRateType").change(function(){
                    var value = $(this).val();
                    if (value == "1") {
                        $("#mRate,#mRateSpan").show();
                    }
                    else 
                        if (value == "0") {
                            $("#mRate").hide().val("140");
                            $("#mRateSpan").hide();
                        }
                        else {
                            $("#mRate").hide().val("");
                            $("#mRateSpan").hide();
                        }
                    
                });
                
                
                //J-109-0239_05097_B1001 Web e-Loane-Loan授信管理系統案件簽報書之額度明細表新增「特殊融資或不動產ADC融資暴險註記」
                $("input[name='isSpecialFinRisk']").change(function(){
                    var value = $("input[name=isSpecialFinRisk]:checked").val();
                    if (value == "Y") {
                        $("#isSpecialFinRiskSpan").show();
                         
                    } else {
                        $("div#isSpecialFinRiskSpan").find("select").val("");
                        $("#isSpecialFinRiskSpan").hide();
                    }
                });
                
                
                // J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
                $("#specialFinRiskType").change(function(){
                    var value = $("#specialFinRiskType").val();
                    if (value == "1") {
                    	//專案融資
                        $("#isProjectFinOperateStagSpan").show();                        
                    } else {
                    	$("input[name='isProjectFinOperateStag']" ).removeAttr('checked');   //塞值               	
                        $("#isProjectFinOperateStagSpan").hide();
                    }
                    $("input[name='isProjectFinOperateStag']" ).triggerHandler("change");
                });
                
                //J-112-0417 e-Loan簽報書新增高品質專案融資判斷欄位
                $("input[name='isProjectFinOperateStag']").change(function(){
                	//專案融資是否屬營運階段>選擇是時才顯示"高品質融資"相關選項
                	if( $("input[name='isProjectFinOperateStag']:checked").val() == "Y"){
                		$("#isHqProjSpan").show();	
                	}else{
                		$("input[name='isHighQualityProjOpt_1']").removeAttr('checked');//勾選拿掉
                		$("input[name='isHighQualityProjOpt_2']").removeAttr('checked');//勾選拿掉
                		$("input[name='isHighQualityProjOpt_3']").removeAttr('checked');//勾選拿掉
                		$("input[name='isHighQualityProjOpt_4']").removeAttr('checked');//勾選拿掉
                		$("input[name='isHighQualityProjOpt_5']").removeAttr('checked');//勾選拿掉
                		$("#isHighQualityProjResult").val("");//最終結果清空
                        $("#isHqProjSpan").hide();       
                	}
                	//取得高品質最終結果
                	getHighQualityResult();
                });
                
                $("input[name^='isHighQualityProjOpt_']").change(function(){
                	//取得高品質最終結果
                	getHighQualityResult();
                });
                               
                //J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
                $("input[name='isRescue']").change(function(){
                    var value = $("input[name=isRescue]:checked").val();
                    if (value == "Y") {
                    	$("#isRescueSpan").show();
                    }else {
                    	$("#isRescueSpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#isRescueSpan").hide();
                    	$("#rescueRateSpan").hide();
                    	$("#rescueItemSubSpan").hide();
                    	$("#cbRefinSpan").hide();
                    	$("#smallBussSpan").hide();
                    	$("#showSimpleScoreCardData").hide();
                    	$("#rescueItemC02Span").hide();
                    	$("#rescueItemA06Span").hide();
                    }
                    
                });

                $("input[name='headItem1']").change(function () {
                    $("input[name='isRescue']").triggerHandler("change");
                    $("#rescueItem").triggerHandler("change");
                });

                // 經濟部
                $("input[name='isRescueOn']").change(function(){
                    var value = $("input[name=isRescueOn]:checked").val();
                    if (value == "Y") {
                        $("#isRescueOnSpan").show();
                    }else {
                        $("#isRescueOnSpan").find("#onMainId,#caseNoOn,#cntrNoOn,#rescueItemOn").val('');
                        $("#isRescueOnSpan").hide();
                    }
                });
                
                //J-109-0077_05097_B1005 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
                //J-109-0309_05097_B1001 Web e-Loan企金授信觀光局利息補貼額度控管及新增營業所在地欄位
                $("#rescueItem").change(function(e){
                    var value = $(this).val();

                    // 預設
                    $("#isRescueSpan").find('#rescueItemTitleStr').val(i18n.lms1401s02["L140M01a.rescueItem"]);
                    $("#smallBussSpan").find("#isRescueIntroduceTitleStr").val(i18n.lms1401s02["L140M01a.isRescueIntroduce"]);
                    $("#smallBussSpan").find("#sbBussStatusStr_1").val(i18n.lms1401s02["L140M01a.sbBussStatus_1"]);
                    $("#smallBussSpan").find("#sbBussStatusStr_2").val(i18n.lms1401s02["L140M01a.sbBussStatus_2"]);
                    $("#smallBussSpan").find("#sbBussStatusStr_3").val(i18n.lms1401s02["L140M01a.sbBussStatus_3"]);
                    $("#smallBussSpan").find("#showMegaSuperProfitProject").show();
                    
                    //J-111-0112_05097_B1002 Web e-Loan企金授信管理系統新增111年經濟部紓困方案
                    if (value == "A01" || value =="E01" || value == "A04"  || value == "A08") {
                    	$("#rescueRateSpan").show();
                        $("#rescueItemSubSpan").hide();
                        $("#rescueNoRateSpan").hide();
                        $("#rescueItemC02Span").hide();
                        $("#rescueItemA06Span").hide();
                        $("#isRescueSpan").find('#rescueItemSub').val('');
                        if(value == "A04"){
                            $("#rescueItemA01StrSpan").hide();
                            $("#rescueItemA04StrSpan").show();
                            $("#rescueItemA08StrSpan").hide();
                        } else if(value == "A08"){
                            $("#rescueItemA01StrSpan").hide();
                            $("#rescueItemA04StrSpan").hide();   
                            $("#rescueItemA08StrSpan").show();
                        } else {
                            $("#rescueItemA01StrSpan").show();
                            $("#rescueItemA04StrSpan").hide();
                            $("#rescueItemA08StrSpan").hide();
                        }
                        $("#isRescueSpan").find('.rescueItemFhide').show();
                        $("#isRescueSpan").find('.rescueItemFshow').hide();
                        $("#rescueItemFSpan").hide();
                        $("#rescueItemLSpan").hide();
                    }else if (value.substring(0, 1) == "Z") { 
                    	//要顯示合併申請紓困方案
                    	$("#isRescueSpan").find(':input').not('#rescueItem,#rescueDate,#isCbRefin,#rescueItemSub,#isSmallBuss,#sbRegistPeriod,#sbPrincipalPeriod,#sbCreditRating,#sbColStatus,#sbBussStatus,#sbScore,#sbHasCreditRating,#isRescueIntroduce,#rescueIntroduceBrNo,#rescueIntroduceUserId,#rescueIntroduceBrNoForShow,#rescueIntroduceUserIdForShow,#isMegaSuperProfitProject,#rescueIndustry,#rescueCity,:button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#rescueRateSpan").hide();
                    	$("#rescueItemSubSpan").show();
                    	$("#rescueNoRateSpan").show();
                    	$("#rescueItemC02Span").hide();
                    	$("#rescueItemA06Span").hide();
                        $("#isRescueSpan").find('.rescueItemFhide').show();
                        $("#isRescueSpan").find('.rescueItemFshow').hide();
                    	$("#rescueItemFSpan").hide();
                        $("#rescueItemLSpan").hide();
                    }else if (value == "C02" || value =="C03") { 
                    	//要顯示合併申請紓困方案
                    	$("#isRescueSpan").find(':input').not('#rescueItem,#rescueDate,#isCbRefin,#rescueItemSub,#isSmallBuss,#sbRegistPeriod,#sbPrincipalPeriod,#sbCreditRating,#sbColStatus,#sbBussStatus,#sbScore,#sbHasCreditRating,#isRescueIntroduce,#rescueIntroduceBrNo,#rescueIntroduceUserId,#rescueIntroduceBrNoForShow,#rescueIntroduceUserIdForShow,#isMegaSuperProfitProject,#rescueIndustry,#rescueCity,:button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#rescueRateSpan").hide();
                    	$("#rescueItemSubSpan").show();
                    	$("#rescueNoRateSpan").show();	
                    	$("#rescueItemC02Span").show();
                    	$("#rescueItemA06Span").hide();
                        $("#isRescueSpan").find('.rescueItemFhide').show();
                        $("#isRescueSpan").find('.rescueItemFshow').hide();
                    	$("#rescueItemFSpan").hide();
                        $("#rescueItemLSpan").hide();
                    }else if (value == "A03") { 
                    	//要顯示合併申請紓困方案
                    	$("#isRescueSpan").find(':input').not('#rescueItem,#rescueDate,#isCbRefin,#rescueItemSub,#isSmallBuss,#sbRegistPeriod,#sbPrincipalPeriod,#sbCreditRating,#sbColStatus,#sbBussStatus,#sbScore,#sbHasCreditRating,#isRescueIntroduce,#rescueIntroduceBrNo,#rescueIntroduceUserId,#rescueIntroduceBrNoForShow,#rescueIntroduceUserIdForShow,#isMegaSuperProfitProject,#rescueIndustry,#rescueCity,:button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#rescueRateSpan").hide();
                    	$("#rescueItemSubSpan").show();
                    	$("#rescueNoRateSpan").show();	
                    	$("#rescueItemC02Span").hide();	
                    	$("#rescueItemA06Span").hide();
                        $("#isRescueSpan").find('.rescueItemFhide').show();
                        $("#isRescueSpan").find('.rescueItemFshow').hide();
                    	$("#rescueItemFSpan").hide();
                        $("#rescueItemLSpan").hide();
                    }else if (value == "A07" || value == "H01" || value == "A11" || value == "H02") { 
                    	//J-111-0303_05097_B1001 Web e-Loan企金授信管理系統新增111年經濟部紓困方案
                    	//J-110-0258_05097_B1002 Web e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
                    	//要顯示國發基金加碼保證成數
                    	$("#isRescueSpan").find(':input').not('#rescueItem,#rescueDate,#isCbRefin,#isSmallBuss,#sbRegistPeriod,#sbPrincipalPeriod,#sbCreditRating,#sbColStatus,#sbBussStatus,#sbScore,#sbHasCreditRating,#isRescueIntroduce,#rescueIntroduceBrNo,#rescueIntroduceUserId,#rescueIntroduceBrNoForShow,#rescueIntroduceUserIdForShow,#isMegaSuperProfitProject,#rescueIndustry,#rescueCity,#rescueNdfGutPercent,:button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#rescueRateSpan").hide();
                    	$("#rescueItemSubSpan").hide();
                    	$("#rescueNoRateSpan").show();	
                    	$("#rescueItemC02Span").hide();	
                    	$("#rescueItemA06Span").show();
                        $("#isRescueSpan").find('.rescueItemFhide').show();
                        $("#isRescueSpan").find('.rescueItemFshow').hide();
                    	$("#rescueItemFSpan").hide();
                        $("#rescueItemLSpan").hide();
                    } else if (isResueItemCaseF() || isResueItemCaseJ() || value == "K01" || isResueItemCaseL()){
                        // J-112-0148 疫後振興
                        $("#isRescueSpan").find(':input')
                            .not('input[name="rescueItemLKind"],#rescueItem,#rescueDate,#sbRegistPeriod,#sbPrincipalPeriod,#sbCreditRating,#sbColStatus,#sbBussStatus,#sbScore,#sbHasCreditRating,#sbReasonCode,#isRescueIntroduce,#rescueIntroduceBrNo,#rescueIntroduceUserId,#rescueIntroduceBrNoForShow,#rescueIntroduceUserIdForShow,#isMegaSuperProfitProject,#attachDoc,#attachDoc2,#attachDoc3,#attachDocMemo,:button, :submit, :reset, :hidden')
                            .removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                        $("#rescueRateSpan").hide();
                        if (isResueItemCaseF()) {
                            $("#rescueNoRateSpan").show();
                        } else {
                            $("#rescueNoRateSpan").hide();
                        }
                        $("#rescueItemSubSpan").hide();
                        $("#rescueItemC02Span").hide();
                        $("#rescueItemA06Span").hide();
                        $("#rescueItemFSpan").hide();
                        $("#isRescueSpan").find('.rescueItemFhide').hide();
                        if (isResueItemCaseF()) {
                            $("#isRescueSpan").find('.rescueItemFshow').show();
                        } else {
                            $("#isRescueSpan").find('.rescueItemFshow').hide();
                        }
                        // 為了引介欄位
                        $("#cbRefinSpan").show();
                        $("#showSmallBussCase").show();
                        $("#smallBussSpan").show();
                        // 只有F02可以show
                        $("#showSimpleScoreCardData").hide();
                        // 文字顯示
                        $("#isRescueSpan").find('#rescueItemTitleStr').val(isResueItemCaseF()?i18n.lms1401s02["L140M01a.rescueItem2"]:isResueItemCaseJ()?i18n.lms1401s02["L140M01a.rescueItem3"]:isResueItemCaseL()?i18n.lms1401s02["L140M01a.rescueItem5"]:i18n.lms1401s02["L140M01a.rescueItem4"]);
                        $("#smallBussSpan").find("#isRescueIntroduceTitleStr").val(i18n.lms1401s02["L140M01a.isRescueIntroduce2"]);
                        if (isResueItemCaseF()) {
                            $("#smallBussSpan").find("#sbBussStatusStr_1").val(i18n.lms1401s02["L140M01a.sbBussStatusF_1"]);
                            $("#smallBussSpan").find("#sbBussStatusStr_2").val(i18n.lms1401s02["L140M01a.sbBussStatusF_2"]);
                            $("#smallBussSpan").find("#sbBussStatusStr_3").val(i18n.lms1401s02["L140M01a.sbBussStatusF_3"]);
                        }
                        if(value == "F01" || value == "F02" || value == "F03" || value == "F06" || value == "F07" || value == "F08"){
                            $("#rescueItemFSpan").show();
                            if(value == "F02" || value == "F07"){ // 呈現簡易評分表
                                $("#showSimpleScoreCardData").show();
                            }
                        }else if (value == "J03"){
                            var headItem1 = $("input[name='headItem1']:checked").val();
                            var projClass = $("#projClass").val();
                            // 呈現簡易評分表
                            if (headItem1 == "Y" && "23" == projClass) {
                                $("#showSimpleScoreCardData").show();
                            } else {
                                $("#showSimpleScoreCardData").hide();
                            }
                        }else if (value == "J09"){
                            var headItem1 = $("input[name='headItem1']:checked").val();
                            // 呈現簡易評分表
                            if (headItem1 == "Y") {
                                $("#showSimpleScoreCardData").show();
                            } else {
                                $("#showSimpleScoreCardData").hide();
                            }
                        } else if (value == "L01") {
                            $("#showSimpleScoreCardData").show();
                        } else{
                            $("#rescueItemFSpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                            $("#rescueItemFSpan").hide();
                        }
                        if (isResueItemCaseJ() || value == "K01" || isResueItemCaseL()) {
                            $("#smallBussSpan").find("#showMegaSuperProfitProject").hide();
                            $("input[name='isMegaSuperProfitProject']").prop("checked", false);
                        }

                        if (isResueItemCaseL()) {
                            $(".rescueItemLSpan").show();
                        } else {
                            $('input[name="rescueItemLKind"]:checked').prop("checked", false);
                            $(".rescueItemLSpan").hide();
                        }
                    }else {
                    	$("#isRescueSpan").find(':input').not('#rescueItem,#rescueDate,#isCbRefin,#isSmallBuss,#sbRegistPeriod,#sbPrincipalPeriod,#sbCreditRating,#sbColStatus,#sbBussStatus,#sbScore,#sbHasCreditRating,#isRescueIntroduce,#rescueIntroduceBrNo,#rescueIntroduceUserId,#rescueIntroduceBrNoForShow,#rescueIntroduceUserIdForShow,#isMegaSuperProfitProject,#rescueIndustry,#rescueCity,:button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#rescueRateSpan").hide();
                    	$("#rescueItemSubSpan").hide();
                    	$("#rescueNoRateSpan").show();
                    	$("#rescueItemC02Span").hide();
                    	$("#rescueItemA06Span").hide();
                        $("#isRescueSpan").find('.rescueItemFhide').show();
                        $("#isRescueSpan").find('.rescueItemFshow').hide();
                    	$("#rescueItemFSpan").hide();
                        $(".rescueItemLSpan").hide();
                    }
                    
                    
                    //J-110-0253_05097_B1001 Web e-Loan企金授信配合經濟部紓困4.0，新增A04、A05、A06專案
                    if(e.originalEvent){
                    	//USER 人手觸發才會發動
                    	
                    	if(value == "A04" || value == "A01" || value == "A08"){
                    		
                    		var isRescueOn = $("input[name=isRescueOn]:checked").val();
                    		//if (isRescueOn == undefined || isRescueOn == null || isRescueOn == "") {
                    			
                    			$.ajax({
                                    handler: inits.fhandle,
                                    action: "queryLastRescueItemOn",
                                    data: {//把資料轉成json
                                    	rptMainId:responseJSON.mainId,
                                    	tabFormMainId: $("#tabFormMainId").val(),
                                        queryCustId: $("#L140M01AForm1").find("#custId").val(),
                                        queryDupNo: $("#L140M01AForm1").find("#dupNo").val(),
                                        queryCntrNo: $("#cntrNo").val()
                                    }
								}).done(function(responseData) {
									if(responseData.isRescueOn == "Y"){
										$("input[name='isRescueOn'][value='Y']:radio" ).prop( "checked" , true );   //塞值
										$("#onMainId").val(responseData.onMainId);
									    $("#caseNoOn").val(responseData.caseNoOn);
									    $("#cntrNoOn").val(responseData.cntrNoOn);
									    $("#rescueItemOn").val(responseData.rescueItemOn);
									}else if(responseData.isRescueOn == "N"){
										$("input[name='isRescueOn'][value='N']:radio" ).prop( "checked" , true );   //塞值
									}
									$("input[name='isRescueOn']" ).trigger("change");
                                });
                    			
                    			
                    		//}
                    	}else{
                    		
                    		var isRescueOn = $("input[name=isRescueOn]:checked").val();
                    		if (isRescueOn == undefined || isRescueOn == null || isRescueOn == "") {
                    			$("input[name='isRescueOn'][value='N']:radio" ).prop( "checked" , true );   //塞值
                    			$("input[name='isRescueOn']" ).trigger("change");
                    		}
                    	}
                    }
                    
                    
                    
                });
                
                
                
                //J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
                $("input[name='isCbRefin']").change(function(){
                	var value = $("input[name=isCbRefin]:checked").val();
                	if (value == "Y") {
                    	$("#cbRefinSpan").show();
                    }else {
                        // J-112-0148 疫後振興
                        if (isResueItemCaseF() || isResueItemCaseJ() || isResueItemCaseL()){
                            $("#cbRefinSpan").show();
                            $("#cbRefinSpan").find('input[name=isCbRefin]').removeAttr('checked');
                        } else {
                            $("#cbRefinSpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                            $("#cbRefinSpan").hide();
                    	}
                    }
                });
                
                //J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
                $("#isSmallBuss").change(function(){
                	var value = $(this).val();
                	$("#smallBussSpan").find("#isRescueIntroduceTitleStr").val(i18n.lms1401s02["L140M01a.isRescueIntroduce"]);
                	$("#smallBussSpan").find("#sbBussStatusStr_1").val(i18n.lms1401s02["L140M01a.sbBussStatus_1"]);
                	$("#smallBussSpan").find("#sbBussStatusStr_2").val(i18n.lms1401s02["L140M01a.sbBussStatus_2"]);
                	$("#smallBussSpan").find("#sbBussStatusStr_3").val(i18n.lms1401s02["L140M01a.sbBussStatus_3"]);
                	if (value == "C") {
                    	$("#smallBussSpan").show();
                    	$("#showSimpleScoreCardData").show();
                    }else {
                        // J-112-0148 疫後振興
                        if (isResueItemCaseF() || isResueItemCaseJ() || isResueItemCaseL()){
                            $("#smallBussSpan").show();
                            var rescueItem = $("#isRescueSpan").find("#rescueItem").val();
                            var headItem1 = $("input[name='headItem1']:checked").val();
                            var projClass = $("#projClass").val();
                            if (rescueItem == "F02" || rescueItem == "F07" || (rescueItem == "J03" && "Y" == headItem1 && "23" == projClass) || (rescueItem == "J09" && "Y" == headItem1)) {
                                $("#showSimpleScoreCardData").show();
                            } else {
                                $("#showSimpleScoreCardData").hide();
                            }
                            $("#smallBussSpan").find("#isRescueIntroduceTitleStr").val(i18n.lms1401s02["L140M01a.isRescueIntroduce2"]);
                            if (isResueItemCaseF()) {
                                $("#smallBussSpan").find("#sbBussStatusStr_1").val(i18n.lms1401s02["L140M01a.sbBussStatusF_1"]);
                                $("#smallBussSpan").find("#sbBussStatusStr_2").val(i18n.lms1401s02["L140M01a.sbBussStatusF_2"]);
                                $("#smallBussSpan").find("#sbBussStatusStr_3").val(i18n.lms1401s02["L140M01a.sbBussStatusF_3"]);
                            }
                            if (isResueItemCaseJ() || isResueItemCaseL()) {
                                $("#rescueItemFSpan").hide();
                            }
                        } else {
                            $("#smallBussSpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                            $("#smallBussSpan").find("[id^=rescueIntroduce]").val('');
                            $("#smallBussSpan").hide();
                            $("#showSimpleScoreCardData").hide();
                    	}
                    }
                });
                
                //J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
                $("input[name='sbHasCreditRating']").change(function(){
                	var value = $("input[name=sbHasCreditRating]:checked").val();
                	if (value == "Y") {
                    	$("#hasCreditRatingSpan").show();
                    	$("#hasReasonCodeSpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#hasReasonCodeSpan").hide();	
                    	
                	}else if (value == "N") {
                		$("#hasCreditRatingSpan").hide();
                		var sbReasonCode = $('#sbReasonCode').val();
                		if( sbReasonCode  ){
                			$("#hasReasonCodeSpan").show();	
                		}else{
                			$("#hasReasonCodeSpan").hide();	
                			$("#hasReasonCodeSpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                		}
                        
                    }else {
                    	$("#hasCreditRatingSpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#hasReasonCodeSpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#hasCreditRatingSpan").hide(); 
                    	$("#hasReasonCodeSpan").hide();	
                    	
                    }
                });
                
                
                
                //J-109-0352_05097_B1001 (109) 第 2684 號 國內企金授信額度明細表新增「109年9至12月提升中小企業放款方案」
                //J-110-0038_05097_B1001 Web e-Loan企金額度明細表新增「本案是否屬110年行銷名單來源客戶」並產生統計報表
                //J-112-0183_08035_B1001 新創重點產業及110年特定工廠欄位隱藏
                $("input[name='headItem2']").change(function(){

                    var value = $("input[name=headItem2]:checked").val();
                    if (value == "Y") {
                    	$("#showIsEnhanceSmeLoan").hide();  //企金處詹景安襄理說不要再顯示了
                    	$("#showIsMarketingList110").show();
                    } else {
                    	$("#showIsEnhanceSmeLoan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#showIsEnhanceSmeLoan").hide(); 
                    	$("#showIsMarketingList110").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#showIsMarketingList110").hide();
                    }
                    
                    $.ajax({
                        handler: inits.fhandle,
                        action: "checkJ1120183Flag",
                        data: {//把資料轉成json
                        	tabFormMainId:$("#tabFormMainId").val()
                        }
					}).done(function(responseData) {
						if (responseData.isMarketingList110_hide == "N"){
							$("#showIsMarketingList110").show();
						} else {
							$("#showIsMarketingList110").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
							$("#showIsMarketingList110").hide();
						}

						if (responseData.isStartUp_hide == "N"){
							$("#isShowStartUp").show();
						} else {
							$("#isShowStartUp").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
							$("#isShowStartUp").hide();
						}
                    }); 
                });
                
                //J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
                $("input[name='isRevive']").change(function(){
                    var value = $("input[name=isRevive]:checked").val();
                    if (value == "Y") {
                    	$("#showReviveTable").show();  
                    	$("input[name='reviveTarget']").trigger('change');
                    }else {
                    	$("#showReviveTable").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#showReviveTable").hide(); 
                    }              
                });
                
                //J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
                $("input[name='isRevive']").click(function(){
                    var value = $("input[name=isRevive]:checked").val();
                    if (value == "Y") {
                    	var reviveTarget = $("input[name=reviveTarget]:checked").val();
                    	if (reviveTarget == undefined || reviveTarget == null || reviveTarget == "") {
                    		$.ajax({
                                handler: inits.fhandle,
                                action: "queryL140s08a",
                                data: {//把資料轉成json
                                	rptMainId:responseJSON.mainId,
                                    tabFormMainId: $("#tabFormMainId").val()
                                }
							}).done(function(responseData) {
								if(responseData.mainItem != undefined && responseData.mainItem != ""){
									
									if(responseData.mainItem == "XX"){
										$("input[name='isRevive'][value='N']:radio" ).prop( "checked" , true );   //塞值
										$("input[name='isRevive']").trigger('change');
									}else{
										$("input[name='reviveTarget'][value='01']:radio" ).prop( "checked" , true );   //塞值
								    	$("input[name='reviveTarget']").trigger('change');
								    	$("input[name='reviveCoreIndustry'][value='"+responseData.mainItem+"']:radio" ).prop( "checked" , true );   //塞值
									}
									
								}
                            });
                    	}

                    }         
                });
                
                $("input[name='reviveTarget']").change(function(){
                	var value = $("input[name=reviveTarget]:checked").val();

                    if (value == "01") {
                    	$("#showReviveCoreIndustry").show();
                    	$("#showReviveChain").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#showReviveChain").hide(); 
                    }else if (value == "02") {
                    	$("#showReviveChain").show();
                    	$("#showReviveCoreIndustry").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#showReviveCoreIndustry").hide(); 
                    }else if (value == "03") {
                    	
                    	$("#showReviveCoreIndustry").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#showReviveCoreIndustry").hide(); 
                    	$("#showReviveChain").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#showReviveChain").hide(); 
                    }          
                });
                
                
                //J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
                $("input[name='reviveTarget']").click(function(){
                	var value = $("input[name=reviveTarget]:checked").val();
                	if (value == "01") {
                    	 
                		$.ajax({
                            handler: inits.fhandle,
                            action: "queryL140s08a",
                            data: {//把資料轉成json
                            	rptMainId:responseJSON.mainId,
                                tabFormMainId: $("#tabFormMainId").val()
                            }
						}).done(function(responseData) {
							if(responseData.mainItem != undefined && responseData.mainItem != ""){
								if(responseData.mainItem == "XX"){
									$("input[name='isRevive'][value='N']:radio" ).prop( "checked" , true );   //塞值
									$("input[name='isRevive']").trigger('change');
								}else{
									$("input[name='reviveCoreIndustry'][value='"+responseData.mainItem+"']:radio" ).prop( "checked" , true );   //塞值
								}
							}
                        });

                    }         
                });
                
                 
                //J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
                $("input[name='isRescueIntroduce']").click(function(){
                	var value = $(this).val();
                	if (value == "Y") {
                    	$("#showRescueIntroduceData").show();  
                    	
                    }else {
                    	$("#showRescueIntroduceData").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#smallBussSpan").find("[id^=rescueIntroduce]").val('');
                    	$("#showRescueIntroduceData").hide(); 
                    }              
                });
  
                $("#applyIntroduceData").click(function(){
                	$("#roleForm").reset();
                	$("#_rescueIntroduceUserId").empty();
                	//Web e-Loan於簽報央行C方案件中增列總處單位引介資料
            		CntrNoAPI.initIntroduceAllBranch();
                	$("#applyRescueIntroduceThickbox").thickbox({ // 使用選取的內容進行彈窗
        				title :  "",
        				width : 520,
        				height : 200,
        				align : 'center',
        				valign : 'bottom',
        				modal : false,
        				i18n:i18n.def,
        				buttons : {
        					"sure" : function() {
        						 $("#rescueIntroduceBrNo").val($("#_rescueIntroduceBrNo").val());
        						 $("#rescueIntroduceUserId").val($("#_rescueIntroduceUserId").val());
        						 
        						 
        						 var selectIntroduceBrnoText=$("#_rescueIntroduceBrNo").find("option:selected").text();
        						 var selectIntroduceUserIdText=$("#_rescueIntroduceUserId").find("option:selected").text();
        						 
        						 $("#rescueIntroduceBrNoForShow").val( selectIntroduceBrnoText.substring(selectIntroduceBrnoText.indexOf("-")+1, selectIntroduceBrnoText.length)      );
        						 $("#rescueIntroduceUserIdForShow").val(selectIntroduceUserIdText.substring(selectIntroduceUserIdText.indexOf("-")+1, selectIntroduceUserIdText.length));
        						 
        						 $.thickbox.close();
        					},
        					"cancel" : function() {
        						$.thickbox.close();
        					}
        				}
        			});
                                  
                });
                
                
                $("#_rescueIntroduceBrNo").change(function(){
                	var value = $(this).val();
                	CntrNoAPI.changeRescueIntroduceUserList(value);
                });

                $("input[name='attachDoc']").change(function(){
                    var value = $("input[name='attachDoc']:checked").val();
                    if (value == "A") {
                        $("#rescueItemFdocBSpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                        $("#rescueItemFdocASpan").show();
                        $("#rescueItemFdocBSpan").hide();
                    } else if (value == "B") {
                        $("#rescueItemFdocASpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                        $("#rescueItemFdocASpan").hide();
                        $("#rescueItemFdocBSpan").show();
                    } else if (value == "C") {
                        $("#rescueItemFdocASpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                        $("#rescueItemFdocBSpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                        $("#rescueItemFdocASpan").hide();
                        $("#rescueItemFdocBSpan").hide();
                    } else {
                        $("#rescueItemFdocASpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                        $("#rescueItemFdocBSpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                        $("#rescueItemFdocASpan").hide();
                        $("#rescueItemFdocBSpan").hide();
                    }
                });
                $("input[name='attachDoc2']").change(function(){
                    var value = $("input[name='attachDoc2']:checked").val();
                    if (value == "B2") {
                        $("#rescueItemFdocB3Span").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                        $("#rescueItemFdocB2Span").show();
                        $("#rescueItemFdocB3Span").hide();
                    } else if (value == "B3") {
                        $("#rescueItemFdocB2Span").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                        $("#rescueItemFdocB2Span").hide();
                        $("#rescueItemFdocB3Span").show();
                    } else {
                        $("#rescueItemFdocB2Span").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                        $("#rescueItemFdocB3Span").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                        $("#rescueItemFdocB2Span").hide();
                        $("#rescueItemFdocB3Span").hide();
                    }
                });

                // J-112-0200 中小企業千億振興融資方案
                $("input[name='isRevital']").change(function(){
                    var value = $("input[name=isRevital]:checked").val();
                    if (value == "Y") {
                        $("#showRevitalTable").show();
                        $("input[name='revitalTarget']").trigger('change');
                    }else {
                        $("#showRevitalTable").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                        $("#showRevitalTable").hide();
                    }
                });

                //J-109-0152 保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構
                $("input[name='isOfficialCga']").change(function(){
                    var value = $("input[name=isOfficialCga]:checked").val();
                    if (value == "Y") {
                        $("#isOfficialCgaSpan").show();
                        // 限定 國際&長期
                        $("#cga_crdArea").val("1").prop('disabled', true);
                        $("#cga_crdPred").val("1").prop('disabled', true);
                    } else {
//                        $("#isOfficialCgaSpan").removeAttr('selected').val('');
                        $("span#isOfficialCgaSpan").find("select").val("");
                        $("#cga_crdGrade").val("");
                        $("#cga_rskRatio").val("");
                        $("#isOfficialCgaSpan").hide();
                    }
                });
                $("#applyCountryCrd").click(function(){
                    var value = $("select#cga_country option:selected").val();
                    if(value != undefined && value != ""){
                        // 引進國家信評
                        $.ajax({
                            handler: inits.fhandle,
                            action: "queryCountryCrd",
                            data: {
                                country: value
                            }
						}).done(function(responseData) {
                            if (responseData.msg && responseData.msg != "") {
                                return API.showErrorMessage(responseData.msg);
                            } else {
							// $("#cga_crdType option[value=" + responseData.cga_crdType + "]").attr("selected", true);
                            $("#cga_crdType").val(responseData.cga_crdType);
                            $("#cga_crdGrade").val(responseData.cga_crdGrade);
                            if(responseData.cga_rskRatio != undefined && responseData.cga_rskRatio != ""){
                                $("#cga_rskRatio").val(responseData.cga_rskRatio);
                            }
                        }
                        });
                    } else {
                        return API.showErrorMessage("請選擇主權國家");
                    }
                });
                $("#btn_fcrdGrad").click(function(){
                    var crdType = $("#cga_crdType :selected").val();
                    var crdArea = $("#cga_crdArea :selected").val();
                    var crdPred = $("#cga_crdPred :selected").val();
                    if(crdType != undefined && crdType != "" && crdArea != undefined
                        && crdArea != "" && crdPred != undefined && crdPred != ""){
                        // 取得風險等級
                        $("#crdGradeGrid").jqGrid("setGridParam", {
                            postData : {
                                handler: "lms1810gridhandler",
                                queryType: "L140",
                                fcrdType: crdType,
                                fcrdArea: crdArea,
                                fcrdPred: crdPred,
                                formAction: "query_elfFcrdGrad"
                            },
                            search: true
                        }).trigger("reloadGrid");

                        $("#crdGradeThickbox").thickbox({
                            title: "",
                            width: 400,
                            height: 400,
                            align: "center",
                            valign: "bottom",
                            modal: false,
                            i18n: i18n.def,
                            buttons: {
                                "sure": function(){
                                     var selrow = $("#crdGradeGrid").getGridParam('selrow');
                                     if (selrow) {
                                        var data =  $("#crdGradeGrid").getRowData(selrow);
                                        $("#cga_crdGrade").val( data.ratingGrad );
                                        $("#cga_rskRatio").val("");
                                        $.thickbox.close();
                                     }
                                },
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    } else {
                        return API.showErrorMessage("信評相關欄位尚未填妥");
                    }
                });
                $("#applyRskRatio").click(function(){
                    var crdType = $("#cga_crdType :selected").val();
                    var value = $("#cga_crdGrade").val();
                    if(value != undefined && value != ""){
                        // 計算風險權數
                        $.ajax({
                            handler: inits.fhandle,
                            action: "queryCrdGradeToRskRatio",
                            data: {
                                crdType: crdType,
                                crdGrade: value
                            }
						}).done(function(responseData) {
							if (responseData.msg && responseData.msg != "") {
							    return API.showErrorMessage(responseData.msg);
							} else {
							    $("#cga_rskRatio").val(responseData.cga_rskRatio);
							}
                        });
                    } else {
                        return API.showErrorMessage("請先取得風險等級");
                    }
                });
                $("#cga_country").change(function(){
                    $("#cga_crdType").val("");
                    $("#cga_crdGrade").val("");
                    $("#cga_rskRatio").val("");
                });
                $("#cga_crdType").change(function(){
                    $("#cga_crdGrade").val("");
                    $("#cga_rskRatio").val("");
                });

                //J-111-000A_05097_B1001 Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
                $("input[name='isEsgGreenLoan']").change(function(){
                    var value = $("input[name=isEsgGreenLoan]:checked").val();
                    if (value == "Y") {
                        $("#isEsgGreenLoanSpan").show();
                    }else {
                    	$("#isEsgGreenLoanSpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#esgGreenSpendType").val('');
                    	$("#isEsgGreenLoanSpan").hide();
                    	$("#showEsgGreenSpendTypeA").hide();
                    	$("#showEsgGreenSpendTypeZ").hide();
                    }
                    
                });
                
                //J-111-000A_05097_B1001 Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
                $("input[name='esgSustainLoan']").change(function(){
                    var value = $("input[name=esgSustainLoan]:checked").val();
                    if (value == "Y") {
                        $(".showEsgSustainLoan").show();
                    }else {
                    	$(".showEsgSustainLoan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#esgSustainLoanType").val('');
                    	$("#showEsgSustainLoanUnReach").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#showEsgSustainLoanUnReach").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$(".showEsgSustainLoan").hide();
                    }
                    
                });
                
                //J-111-000A_05097_B1001 Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
                $("input[name='esgSustainLoanUnReach']").change(function(){
                    var value = $("input[name=esgSustainLoanUnReach]:checked").val();
                    if (value == "Y") {
                        $("#showEsgSustainLoanUnReach").show();
                    }else {
                    	$("#showEsgSustainLoanUnReach").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#showEsgSustainLoanUnReach").hide();
                    }
                    
                });
                
                
                //J-111-000A_05097_B1001 Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
                $("#esgGreenSpendType").change(function(){
                	
                	var value = $(this).val();
                	var allCheacked = value.split("|");

                	if(jQuery.inArray("A", allCheacked) !== -1){ 
                    	$("#showEsgGreenSpendTypeA").show();
                    }else{
                    	$("#showEsgGreenSpendTypeA").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#showEsgGreenSpendTypeA").hide();
                    }
                    
                    if(jQuery.inArray("Z", allCheacked) !== -1){ 
                    	$("#showEsgGreenSpendTypeZ").show();
                    }else{
                    	$("#showEsgGreenSpendTypeZ").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
                    	$("#showEsgGreenSpendTypeZ").hide();
                    }
                });
                
                //J-113-0329 企金授信新增社會責任授信
                $("input[name='socialLoanFlag']").change(function(){
                    var value = $("input[name=socialLoanFlag]:checked").val();
                    if (value == "Y") {
                        $(".showSocialLoan").show();
                    }else {
                    	$(".showSocialLoan").find(':input').val('');
                    	$("#socialKindShowText").val('');
                    	$("#socialTaShowText").val('');
                    	$("#socialRespShowText").val('');
                    	$(".showSocialLoan").hide();
                    }
                });
  
                //**********************************************************************************
                
                
                $("#pageNumA").change(function(){
                    if ($(this).val() != "") {
                    
                        if ($(this).val() != "2") {
                            $("#toALoanA").val($("#pageNumA :selected").text());
                        }
                        else {
                            //應注意事項：
                            $("#toALoanA").val($("#pageNumA :selected").text() + "\n" + i18n.lms1401s02['L140M01a.Headapproved2']);
                        }

                    }
                });

                $("#pageNumB").change(function(){
                    if ($(this).val() != "") {
                        $("#toALoanB").val($("#pageNumB :selected").text());
                    }
                });
                // 專案種類為22-辦理企業戶購置廠辦整批分戶貸款 and 額度控管種類為41-合作子 時，出現[是否已向管理行(母戶簽報行)通報占用貸款額度]radio
                $("#projClass").change(function() {
            		if ($('#projClass').val() == "22" && $("#snoKind").val() == "41") {
            			$("#showIsInformMbrchUsedAmt").show();
            		} else {
            			$("#showIsInformMbrchUsedAmt").hide();
            		}

                    $("input[name='isRescue']").triggerHandler("change");
                    $("#rescueItem").triggerHandler("change");
                });

                util.init($('#L140M01AForm2'));
                CntrNoAPI.queryL140M01A(responseJSON);
                IntroductionSource.initEvent(); //J-112-0438_12473_B1001 引介人員相關function
            });
            $("#loadPanel").attr("openFlag", "Y")
        }
        else {
            CntrNoAPI.queryL140M01A(responseJSON);
        }
    },
    /**
     * 查詢額度明細表內容
     */
    queryL140M01A: function(data){
    
    	//J-110-0281_05097_B1001 Web e-Loan授信配合「公股攜手，兆元振興」融資方案已結案，謹申請取消e-LOAN簽報書相關欄位之顯示
    	$.ajax({
            handler: inits.fhandle,
            action: "queryInitData",
            data: {
            	tabFormMainId: $("#tabFormMainId").val(),
            	rptMainId: responseJSON.mainId
            }
		}).done(function(initObj) {
			if(initObj.showSmallBussCase == 'Y'){
				$("#showSmallBussCase").show();
			}else{
				$("#showSmallBussCase").hide();
			}

			//J-110-0281_05097_B1001 Web e-Loan授信配合「公股攜手，兆元振興」融資方案已結案，謹申請取消e-LOAN簽報書相關欄位之顯示
			if(initObj.showIsRevive == 'Y'){
				$("#showIsRevive").show();
			}else{
				$("#showIsRevive").hide();
			}

			// J-112-0200 中小企業千億振興融資方案
			if(initObj.showIsRevital == 'Y'){
			    $("#showIsRevital").show();
			}else{
			    $("#showIsRevital").hide();
			}

			var $selctObj = $("#useDeadline,#noInsuReason,#sbjProperty");
			if (data.oid) {// 如果OID有值才執行ajax
			    $.ajax({
			        handler: inits.fhandle,
			        action: "queryL140m01a",
			        data: {
			            //當為聯行額度明細表mainId來源為srcMainId
			            caseMaiId: (responseJSON.page + "") == "02" ? $("#srcMainId").val() : $("#mainId").val(),
			            oid: data.oid,
			            noOpenDoc: true
			        }
				}).done(function(obj) {
					inits.itemType = obj.caseType;
					$("#showCustId").html(DOMPurify.sanitize(obj.showCustId));
					CntrNoAPI.openMainBox(obj);
					//當觸發條件為有值時，清空輸入值
					$selctObj.trigger("change", "T");
					//是否有批覆勾選
					if (obj.changeCheck) {
					    var checked = obj.changeCheck.split("|");
					    for (var i in checked) {
					        $("#" + checked[i]).prop("checked", true);
					    }
					    //當文件狀態編製中
					    if (obj.L140M01AForm1.docStatus == "010") {
					        //觸發隱藏顯示條件
					        $(".caseBox").trigger("change");
					    }
					}
			    });
			}
			else {
			    //借款人檔的oid
			    CntrNoAPI.newCase(data.custId);
			    CntrNoAPI.openMainBox(data);
			    //當觸發條件為有值時，清空輸入值
			    $selctObj.trigger("change", "T");
			}			
        });
    	
        
    },
    /**   
     *  新增額度明細表
     *@param L120S01A (借款人主檔)的 oid
     *
     * */
    newCase: function(custOid){
        $.ajax({
            handler: inits.fhandle,
            action: "addL140m01a",
            data: {//把資料轉成json
                caseType: inits.itemType,
                noOpenDoc: true,
                mainId: $("#mainId").val(),
                custOid: custOid
            }
		}).done(function(obj) {
			//CntrNoAPI.setNeedUnsecureFlag(responseData); 
            CntrNoAPI._triggerMainGrid();
            $('#L140M01AForm1').injectData(responseData);
			
			//J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等
			if(responseData.showMarkModel=="Y"){
				$('.showMarkModel').show();	
				 
			}else{
				$('.showMarkModel').hide();	
				 
			}
			
			//J-109-0239_05097_B1001 Web e-Loane-Loan授信管理系統案件簽報書之額度明細表新增「特殊融資或不動產ADC融資暴險註記」
			if(responseData.showSpecialFinRisk=="Y"){
				$('#isSpecialFinRiskShow').show();	
			}else{
				$('#isSpecialFinRiskShow').hide();	
			}
			 
			
			//J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
			 var xformid = $("#tabFormMainId").val();
			 if(xformid != undefined && xformid != null && xformid != ""){
				 var isRevive = $("input[name=isRevive]:checked").val();
		         if(isRevive == undefined || isRevive == null || isRevive == ""){
		         	//預設值為是 OOXX
		         	$("input[name='isRevive'][value='Y']:radio" ).prop( "checked" , true ).trigger("click").trigger("change");   //塞值       
		         }
			 }
	         
			//J-105-0250-001  Web e-Loan 新增利害關係人檢核
			//改成由 LMS1401S02Panel02.js   的 $("#sbjProperty").change 觸發
			// CntrNoAPI.chkIsNeedUnsecureFlag();
			 //J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
			 if(responseData.showUnitCase2 == "Y"){
				 $('.unitCase2Div').show();
			 }
			
			
			//J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
	        CntrNoAPI.chkIsNoneHedge();
	        
	        //J-109-0365_05097_B1001 Web e-Loan國內企金授信額度明細表科目為遠期外匯、換匯交易時，新增是否徵提保證金等相關欄位
	        CntrNoAPI.chkIsNeedMarginFlag();
	        
	        CntrNoAPI.chkIsNeedDerivEval();
	
	        //J-110-0485_05097_B1001 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
	        CntrNoAPI.initGlobalVar();
	        
	        //J-112-0082 約定融資額度註記部分，原以表列式勾選方式，改以問答方式
	        CntrNoAPI.initExceptFlagQA();
	        CntrNoAPI.initExceptFlagQAEvent();
        });
    },
    /**
     * 打開額度明細表thickbox
     * @param {Object} data L140M01A
     */
    openMainBox: function(data){
        //設定本案借款人是否為金控法
		//J-104-0219-001 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
        //CntrNoAPI.setNeedUnsecureFlag(data);
        /**初始化**/
        var $unitCase = $("input[name=unitCase]");
        //是否顯示是否為聯貸案
        if ($unitCase && $unitCase.length > 0 && (responseJSON.page + "") == "03") {
            if ($('input[name=unitCase]:checked').val() == "Y") {
            	//J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
                $('.unitCase2Div').show();
            }
            else {
            	//J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
                //$('.unitCase2Div').hide().find(":radio").removeAttr("checked");//移除隱藏選項的radio checked
                $('.unitCase2Div').find(":radio").removeAttr("checked");// 取消隱藏
            }
        }
        else {
            if (data.showUnitCase2 == "Y") {
            	//J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
                $('.unitCase2Div').show();
            }
        }

        $('#pageNumX,#pageNumY').each(function(){
            this.rejectDisabled = function(){
                if (this.options[this.selectedIndex].disabled) {
                    if (this.lastSelectedIndex) {
                        this.selectedIndex = this.lastSelectedIndex;
                    }
                    else {
                        var first_enabled = $(this).children('option:not(:disabled)').get(0);
                        this.selectedIndex = first_enabled ? first_enabled.index : 0;
                    }
                }
                else {
                    this.lastSelectedIndex = this.selectedIndex;
                }
            };
            this.rejectDisabled();
            this.lastSelectedIndex = this.selectedIndex;
            $(this).children('option[disabled]').each(function(){
                $(this).css('color', '#CCC');
            });
            $(this).change(function(){
                this.rejectDisabled();
            });
        });

		
		
        var $formA1 = $("#L140M01AForm1");
        var $formA2 = $("#L140M01AForm2");
		var $formA5 = $("#L140M01AForm5");
        var $formB = $("#L140M01BForm");
        var $openBox = $("#opendocBox");
        
        //讓每次開起box都是第一頁
        $(".tabs", $openBox).tabs({
            selected: 0
        });
        //另開新頁，不需reset
        //        $formA1.reset();
        //        $formA2.reset();
        //        $formB.reset();
        var $itemDscr1 = $("#itemDscr1", $openBox);
        var $itemDscr2 = $("#itemDscr2", $openBox);
        var $itemDscr3 = $("#itemDscr3", $openBox);
        //J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
        var $itemDscrH = $("#itemDscrH", $openBox);
        $itemDscr1.val("");
        $itemDscr2.val("");
        $itemDscr3.text("");
        //J-107-0137_05097_B1001 Web e-Loan企金授信額度明細表，新增「信保基金保證書發文日期」與「信保基金核准之保證手續費率」
        //J-111-0268_05097_B1001 Web e-Loan修改額度明細表信保案件相關功能
        //J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
        $openBox.find("#multiAmtTr,#editMark,#editMarkLgd,#countSayTr,#CATable,#CPTable,#headItem1Tr,#rejectTr,#mRate,#mRateSpan,#headItem5Span,#unsecureFlagSpan","#headItem1Tr_1","#headItem1Tr_1_1","#headItem1Tr_1_1_1","#isRescueSpan","#isRescueOnSpan","#derivEvalSpan").hide();
        
        $("#tabFormId").val("");
        $("#tabFormMainId").val("");
        $("#pageNum1").val("");
        $("#pageNum2").val("");
        $("#pageNum3").val("");
        /** set資料*/
        var page01 = data.L140M01AForm1;
        var page02 = data.L140M01AForm2;
		var page05 = data.L140M01AForm5;
        //J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
        //$('#L140M01AForm2').find("#lgdTotAmt").html(data.lgdTotAmt);
        var lgdTotAmt = $('#L140M01AForm2').find("#lgdTotAmt");
        //lgdTotAmt.html(data.lgdTotAmt);
        //lgdTotAmt.injectData(data.lgdTotAmt);
        lgdTotAmt.injectData({'lgdTotAmt':data.lgdTotAmt},false);
		
		$("#clsLgdInfo").injectData({'clsLgdInfo':data.clsLgdInfo},false);
		$("#expectLgdDesc").text(data.expectLgdDesc);
		if(data.isShowClsLgdInfo == "Y"){
			$("#clsLgdInfoTr").show();
		}
		
        $('#L140M01AForm2').find("#label_lgdTotAmt_T").val(data.label_lgdTotAmt_T);
        
        if (page01) {
            $formA1.injectData(page01);
            $formA2.injectData(page02);
			$formA5.injectData(page05);
            $formB.injectData(data.L140M01BForm);
            $itemDscr1.val(data.L140M01BForm.itemDscr1);
            $itemDscr2.val(data.L140M01BForm.itemDscr2);
            $itemDscr3.val(data.L140M01BForm.itemDscr3);
            //J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
            $itemDscrH.val(data.L140M01BForm.itemDscrH);
            $("#pageNum1").val(data.L140M01BForm.pageNum1);
            $("#pageNum2").val(data.L140M01BForm.pageNum2);
            $("#pageNum3").val(data.L140M01BForm.pageNum3);
            $("#tabFormId").val(page02.tabFormId);
            $("#tabFormMainId").val(page02.tabFormMainId);
          
            //預設選取衍生性商品期數
			var derivativeShow = "";
            if (page02.derivatives) {				
                var dervChecked = page02.derivatives.split("|");
                for (var i in dervChecked) {
                    //$('#derivatives option[value=' + dervChecked[i] + ']').attr('selected', 'selected');
					derivativeShow = derivativeShow + (derivativeShow != "" ? "," : "" )+  i18n.dervPeriodCodeType[dervChecked[i]] ;	//"L140M01a.DERVPERIOD" + 	
                }				
            }
            $('#L140M01AForm2').find("#derivativeShow").val(derivativeShow);
			
			// J-108-0116 共同行銷
			var csTypes = "";
  			if (page02.csTypes) {
                var csTypesArr = page02.csTypes.split("|");
				if(page02.checkYN_A == "Y" && page02.result_A != ""){
					$("#result_A").show();
					if(page02.result_A == "A1"){
						$("#select_A").show();
						$("#memo_A").show();
						$("#mtitle_A").text(i18n.lms1401s02['person']);
					}
				}
					
                for (var i in csTypesArr) {
					var csType = csTypesArr[i];
					if(csType != "A") {
						// A 一定有 so 不用判斷
						$(".CS_"+csType).show();
					}
					if(csType == "B"){
						if (page02.checkYN_B == "Y") {
							$("#result_B").show();
							if (page02.result_B == "B1") {
								$("#select_B").show();
								$("#memo_B").show();
								$("#mtitle_B").text(i18n.lms1401s02['person']);
							}
						} else if(page02.checkYN_B == "N"){
							$("#memo_B").show();
							$("#mtitle_B").text(i18n.lms1401s02['reason']);
						}
					} else if(csType == "C"){
						if (page02.checkYN_C == "Y") {
							$("#result_C").show();
							if (page02.result_C == "C1") {
								$("#select_C").show();
								$("#memo_C").show();
								$("#mtitle_C").text(i18n.lms1401s02['person']);
							}
						} else if(page02.checkYN_C == "N"){
							$("#memo_C").show();
							$("#mtitle_C").text(i18n.lms1401s02['reason']);
						}
					}
                }				
            }

            if (page01.headItem5 == "Y") {
                $("#mRateType").trigger("change");
            }
            
            //J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
            $("input[name='isRescue']" ).trigger("change");
            $("input[name='isRescueOn']" ).trigger("change");
            $("#rescueItem").trigger("change");
            $("input[name='isCbRefin']" ).trigger("change");
            $("#isSmallBuss").trigger("change");
            $("#sbHasCreditRating").trigger("change");
            
            //J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
            $("input[name='isRevive']" ).trigger("change");

            // J-112-0200 中小企業千億振興融資方案
            $("input[name='isRevital']").trigger("change");

            // J-112-0148 疫後振興
            $("input[name='attachDoc']").trigger("change");
            $("input[name='attachDoc2']").trigger("change");
            
            //eb e-Loan於簽報央行C方案件中增列總處單位引介資料
            //$("input[name='isRescueIntroduce']" ).trigger("click");


            //J-109-0152 保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構
            $("input[name='isOfficialCga']" ).trigger("change");
            
            //J-109-0239_05097_B1001 Web e-Loane-Loan授信管理系統案件簽報書之額度明細表新增「特殊融資或不動產ADC融資暴險註記」
            $("input[name='isSpecialFinRisk']" ).trigger("change");
            
            //J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
            $("#specialFinRiskType" ).trigger("change");
            
            //J-109-0352_05097_B1001 (109) 第 2684 號 國內企金授信額度明細表新增「109年9至12月提升中小企業放款方案」
            $("input[name='headItem2']" ).trigger("change");
//            if (page01.headItem2 == "Y") {
//            	var caseDate = new Date(page01.caseDate);
//            	var onlineDate = new Date('2020-05-12');
//            	if (caseDate>=onlineDate){
//            		$("#showIsMarketingList110").hide();
//            	} else {
//            		$("#showIsMarketingList110").show();
//            	}
//            }
            
            //J-111-000A_05097_B1001 Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
            $("input[name='isEsgGreenLoan']" ).trigger("change");
            $("input[name='esgSustainLoan']" ).trigger("change");
            $("input[name='esgSustainLoanUnReach']" ).trigger("change");
            $("#esgGreenSpendType").trigger("change");
            
            //J-113-0329 企金授信新增社會責任授信
            $("input[name='socialLoanFlag']" ).trigger("change");
            $("input[name='socialKind']" ).trigger("change");
            $("input[name='socialTa']" ).trigger("change");
            $("input[name='socialResp']" ).trigger("change");
            

            // 2013/1/8,Rex,新增核准文號顯示
            if (inits.itemType != 1 && page01.signNo && page01.signNo != "") {
                $("#signNoTr").show();
            }
            
            //呈現婉卻說明
            if (page01.cesRjtCause && page01.cesRjtCause != "") {
                $("#rejectTr").show();
            }
            //呈現聯貸說明
            if ("Y" == page01.unitCase2) {
                $("#countSayTr").show();
            }
            //呈現授信合計多幣別
            if (page02.multiAmt && page02.multiAmt != "") {
                $("#multiAmtTr").show();
            }
            
            //當調整註記等於Y 要顯示調整註記在 授信額度合計 
            if ("Y" == page02.valueTune) {
                $("#editMark").show();
            }
            
            //J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
            if ("Y" == page02.valueTuneLgd) {
            	$("#editMarkLgd").show();
            }
            
            //收付彙計數顯示
            if (page02.CACURR && page02.CACURR != "") {
                $("#CATable").show();
                $("#CACURR").html(DOMPurify.sanitize(page02.CACURR));
                $("#CAAMT").html(DOMPurify.sanitize(page02.CAAMT));
            }
            if (page02.CPCURR && page02.CPCURR != "") {
                $("#CPTable").show();
                $("#CPCURR").html(DOMPurify.sanitize(page02.CPCURR));
                $("#CPAMT").html(DOMPurify.sanitize(page02.CPAMT));
            }

			//J-105-0250-001  Web e-Loan 新增利害關係人檢核
			//改成由 LMS1401S02Panel02.js   的 $("#sbjProperty").change 觸發
		    //CntrNoAPI.chkIsNeedUnsecureFlag();
			
            //J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
            CntrNoAPI.chkIsNoneHedge();
            
            //J-109-0365_05097_B1001 Web e-Loan國內企金授信額度明細表科目為遠期外匯、換匯交易時，新增是否徵提保證金等相關欄位
            CntrNoAPI.chkIsNeedMarginFlag();

            CntrNoAPI.chkIsNeedDerivEval();

            //J-110-0485_05097_B1001 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
            CntrNoAPI.initGlobalVar();
			
            //J-112-0082 約定融資額度註記部分，原以表列式勾選方式，改以問答方式
            CntrNoAPI.initExceptFlagQA();
            CntrNoAPI.initExceptFlagQAEvent();
            
            //J-112-0438_12473_B1001
            IntroductionSource.show(page01.introduceSrc);
            IntroductionSource.setSubUnitNo(page01.subUnitNo);
        }
        
        //判斷文件是否執行lock
        var isLock = false;
        //用來判斷要出現甚麼樣的button
        var opendocboxBts = {};
        var ownbrId = userInfo ? userInfo.unitNo : "";
        var unitType = userInfo ? userInfo.unitType : "";
        inits.toreadOnly = false;
        //不加空白會變成數字
        
        switch (responseJSON.page + "") {
            case "03"://額度明細表
                if (responseJSON["readOnly"] == "true" || "4" == unitType || "2" == unitType) {
                    isLock = true;
                }
                else {
                    opendocboxBts = btObject03;
					if(data.authLvl != "1" || data.showContract != "Y"){
					    // 不是分行授權內的額度明細表不能有列印契約書
					    // 純小規 不顯示額度明細表內的合約書
						if (opendocboxBts[i18n.lms1401s02['btn.printW03']]) {
			                delete opendocboxBts[i18n.lms1401s02['btn.printW03']];
			            }
						if (opendocboxBts[i18n.lms1401s02['btn.printW04']]) {
			                delete opendocboxBts[i18n.lms1401s02['btn.printW04']];
			            }
						if (opendocboxBts[i18n.lms1401s02['btn.printW01']]) {
			                delete opendocboxBts[i18n.lms1401s02['btn.printW01']];
			            }
					}
                    thickboxOptions.customButton = [i18n.lms1401s02['btn.toMoneyData'], i18n.def['newData'], 'newData', 'del', i18n.def['del'], i18n.def['close'], i18n.lms1401s02['btn.special'], i18n.def['saveData'], 'saveData', i18n.def['del'], i18n.lms1401s02['btn.contentPerson']];
                }
                break;
            case "11"://額度批覆表，只有出現在授管處 -審核中 和泰國總行 -提會待登錄 
                var docstatus = responseJSON.mainDocStatus;
                var auth = (responseJSON ? responseJSON.Auth : {}); //權限  
                
                if (responseJSON.page == "16") {
                    if (lmsM01Json.isArea()) {
                        if (responseJSON.caseBrId != undefined && responseJSON.caseBrId != null && responseJSON.caseBrId != "") {
                            var unitNo = responseJSON.caseBrId.toString().substring(0, 3);
                            if (unitNo == "007" || unitNo == "009" || unitNo == "025" ||
                            unitNo == "011" ||
                            unitNo == "201" ||
                            unitNo == "940" ||
                            unitNo == "943" ||
                            unitNo == "149") {
                                if (("L1C" == docstatus) && auth.Modify && _openerLockDoc != "1") {
                                    if (lmsM01Json.docKind == "2") {
                                        // 鎖住
                                        isLock = true;
                                    }
                                    else {
                                        if (lmsM01Json.docCode == "4" || lmsM01Json.docCode == "3") {
                                            // 鎖住
                                            isLock = true;
                                        }
                                    }
                                }
                                else {
                                    // 鎖住
                                    isLock = true;
                                }
                            }
                        }
                    }
                }
                else {
                    $("#lms140Tab11").show();
                    //當文件在授管處-審查中或泰國總行提會待登錄 ，且為經辦並無人開啟文件 顯示批覆按鈕
                    var unitNo = userInfo.unitNo;
                    if (("L1H" == docstatus || "L1C" == docstatus || "L3G" == docstatus) && auth.Modify && _openerLockDoc != "1" && (unitType == "4" || unitType == "2")) {
                        //控制thickbox要顯示的按鈕
                        thickboxOptions.customButton = [i18n.def['newData'], 'newData', 'del', i18n.def['del'], i18n.def['close'], i18n.lms1401s02['btn.special'], i18n.def['saveData'], 'saveData', i18n.def['del'], i18n.def['close'], i18n.lms1401s02['btn.contentPerson']];
                        opendocboxBts = btObject11;
                    }else if (("01K" == docstatus || "02K" == docstatus || "03K" == docstatus || "05K" == docstatus || "06K" == docstatus || "07K" == docstatus) && auth.Modify && _openerLockDoc != "1" && (unitType == "5") &&
                    		(unitNo == "007" || unitNo == "009" || unitNo == "025" ||
                            unitNo == "011" ||
                            unitNo == "201" ||
                            unitNo == "940" ||
                            unitNo == "943" ||
                            unitNo == "149") && (responseJSON.areaChk =="5" || responseJSON.areaChk =="6")  ) {
                    	//(108)第 3230 號
                        //控制thickbox要顯示的按鈕
                    	thickboxOptions.customButton = [i18n.def['newData'], 'newData', 'del', i18n.def['del'], i18n.def['close'], i18n.lms1401s02['btn.special'], i18n.def['saveData'], 'saveData', i18n.def['del'], i18n.def['close'], i18n.lms1401s02['btn.contentPerson']];
                        opendocboxBts = btObject11;    	 
                        
                    }
                    else {
                    	isLock = true;
                    }
                }
                break;
            default: //聯行額度明細表  母行法人代表意見
                isLock = true;
                opendocboxBts = btObjectDefault;
                break;
        }

        //當額度明細表非編製中不能編輯 除了 新增的案件
        
        //國外部等新會簽(審查流程)
        if(CntrNoAPI.isCheckShowSpecialBrid() && responseJSON.page == "03"){
        	//額度明細表，新會簽審查流程，與已會簽(審查)後，因為額度明細表DOCSTATUS仍然是編製中，所以要加上下面檢核，要不然已會簽之後的狀態還是可以修改額度明細表(應該只能修改額度批覆書)
        	//增加判斷除編制中01O、待補件07O、撤件0EO、會簽後修改01K外，不能修改
        	if ((page01 && page01.docStatus && page01.docStatus != "010") || _openerLockDoc == "1" || (responseJSON.mainDocStatus != "01O" && responseJSON.mainDocStatus != "07O" && responseJSON.mainDocStatus != "0EO" && responseJSON.mainDocStatus != "01K" )) {
                opendocboxBts = btObjectDefault;
                isLock = true;
            }
        }else{
        	if ((page01 && page01.docStatus && page01.docStatus != "010") || _openerLockDoc == "1") {
                opendocboxBts = btObjectDefault;
                isLock = true;
            }
        }
        
        //當不是額度批覆表才執行lock 
        if (responseJSON.page != "11") {
            CntrNoAPI.todoLock(isLock, page01);
        }
        else {
            CntrNoAPI.todoLockforPage11(isLock, page01);
        }
		
		// 控制合約書列印button 
		switch (responseJSON.page + "") {
			case "03"://額度明細表
				if(data.authLvl != "1"  || data.showContract != "Y"){
				    // 不是分行授權內的額度明細表不能有列印契約書
				    // 純小規 不顯示額度明細表內的合約書
					if (opendocboxBts[i18n.lms1401s02['btn.printW03']]) {
		                delete opendocboxBts[i18n.lms1401s02['btn.printW03']];
		            }
					if (opendocboxBts[i18n.lms1401s02['btn.printW04']]) {
		                delete opendocboxBts[i18n.lms1401s02['btn.printW04']];
		            }
					if (opendocboxBts[i18n.lms1401s02['btn.printW01']]) {
		                delete opendocboxBts[i18n.lms1401s02['btn.printW01']];
		            }
				}
				break;
			case "11"://額度批覆表
				if(data.showContract != "Y"){
                    // 純小規 不顯示額度明細表內的合約書
                    if (opendocboxBts[i18n.lms1401s02['btn.printW03']]) {
                        delete opendocboxBts[i18n.lms1401s02['btn.printW03']];
                    }
                    if (opendocboxBts[i18n.lms1401s02['btn.printW04']]) {
                        delete opendocboxBts[i18n.lms1401s02['btn.printW04']];
                    }
                    if (opendocboxBts[i18n.lms1401s02['btn.printW01']]) {
                        delete opendocboxBts[i18n.lms1401s02['btn.printW01']];
                    }
                }
                break;
		}

        // J-108-0283 變更條件Condition Change
        if (page02 && page02.LMS140S05Ashow) {
            $("#queryL140S05A").show();
        } else {
            $("#queryL140S05A").hide();
        }

        //當如果是空值
        if ($.isEmptyObject(opendocboxBts)) {
            opendocboxBts = API.createJSON([{
                key: i18n.def['close'],
                value: function(){
                    window.close();
                }
            }]);
        }
        
        //判斷如果是007 025 201 918 931~935 才出現特殊登錄按鍵按鈕
        //不是上述銀行登錄銀行則刪除特殊登錄案件
        if (!CntrNoAPI.isCheckShowSpecialBrid()) {
            if (opendocboxBts[i18n.lms1401s02['btn.special']]) {
                delete opendocboxBts[i18n.lms1401s02['btn.special']];
            }
        }
        if (initAll.state() != "resolved") {
            initAll.resolve(inits);
            //用來驗證畫面上的值是否合法
            $("#mainTabs").tabs({
                select: function(event, ui){
                    //目前頁面的inex
                    var pageIndex = $(this).find("li.ui-tabs-selected").index();
                    switch (pageIndex + 1) {
                        case 1:
                            if (_openerLockDoc != "1" && !$("#L140M01AForm1").valid()) {
                                API.showMessage(i18n.def["common.001"]);
                                event.preventDefault();
                            }
                            break;
                        case 2:
                            if (_openerLockDoc != "1" && !$("#L140M01AForm2").valid()) {
                                API.showMessage(i18n.def["common.001"]);
                                event.preventDefault();
                            }
                            break;
                        case 6:
                        case 11:
                        case 12:
                            if (_openerLockDoc != "1" && !$("#L140M01BForm").valid()) {
                                API.showMessage(i18n.def["common.001"]);
                                event.preventDefault();
                            }
                            break;
                    }
                }
            });
        }
    
	    //J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等
		if(data.showMarkModel){
			if(data.showMarkModel=="Y"){
				$('.showMarkModel').show();	
				$('#markModelGradeForShow').val(data.markModelGradeForShow);	
			}else{
				$('.showMarkModel').hide();	
				$('#markModelGradeForShow').val('');	
			}
		}
		
//		$('#expectModelKindTr').hide();
//		if (data.isShowExpectModelKind == 'Y') {
//			$('#expectModelKindTr').show();
//		}
		
		//J-109-0239_05097_B1001 Web e-Loane-Loan授信管理系統案件簽報書之額度明細表新增「特殊融資或不動產ADC融資暴險註記」
        //新增額度明細表時，tabFormMainId 為NULL
        //新增額度明細表時，data為NULL，所以data.showSpecialFinRisk 無法判斷
        //新增額度明細表時，newCase時已經處裡過data.showSpecialFinRisk，所以新增額度明細表時，不用再判斷要不要顯示特殊融資註記，要不然特殊融資註記會因為NULL而一律隱藏
        var xformid = $("#tabFormMainId").val();
		if(xformid != undefined && xformid != null && xformid != ""){
			var showSpecialFinRisk = data.showSpecialFinRisk;
			if(showSpecialFinRisk != undefined && showSpecialFinRisk != null && showSpecialFinRisk != ""){
				if(showSpecialFinRisk=="Y"){
	     			$('#isSpecialFinRiskShow').show();	
	     		}else{
	     			$('#isSpecialFinRiskShow').hide();	
	     		}
			} 
			
		}
		
		
		
//		$("input[name='isBuy']:checked").triggerHandler("click");
//		$("input[name='isBuyOn']:checked").triggerHandler("change");
		
		var isBuyFlag = $("input[name='isBuy']:checked").val();
		if(isBuyFlag == "Y"){
			$("#isInstalmentView").show();
		} else {
			$("#isInstalmentView").hide();
		}
		var isInstalment = $("input[name='isInstalment']:checked").val();		
		$("#realEstateAfterGridView")[isBuyFlag == "Y" ? "show":"hide"]();
		
		
        $.each(opendocboxBts, function(key, value){
            $("<span class='fg-buttonset'><span class='fg-child'><button type='button' class='ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only fg-button'><span class='ui-button-text'><span class='text-only'>" + key + "</span></span></button></span></span>").bind("click", value).appendTo("#buttonPanel");
        });
        $(".caseBox").on('change', function(){
            var $thisId = $(this).attr("id");
            var $thisInput = $(this).parents("td").next("td").find("input:not(.caseReadOnly),textarea:not(.caseReadOnly),select");
            if (this.checked) {
				switch ($thisId) {
                    case "caseBox12":
                        //央行房貸註記
                        $("#modifyL140M01M").show();
						$("input[name='cbRuleVersion']").removeAttr("disabled").prop("readonly", false);
                        $("#pageNumX").removeAttr("disabled").prop("readonly", false);
						$("#cbRuleButton").show();
                        break;
                    case "tab06":
                        $("#itemDscr4").prop("readonly", false);
                        break;
                    case "tab07":
                        $("#itemDscr5").prop("readonly", false);
                        break;
					case "caseBox2_22":
                        $("#modifyL140M01Q").show();
                        $("#pageNumY").removeAttr("disabled").prop("readonly", false);
                        break;		
					case "caseBox29":	// J-108-0293
                        $("#addIntReg").show();
						$("#deleteIntReg").show();
                        break;

                    default:
                        $thisInput.removeAttr("disabled").prop("readonly", false);
                        $thisInput.each(function(){
                            if ($(this).hasClass("date")) {
                                $(this).datepicker();
                            }
                        });
                        
                        switch ($thisId) {
                            case "caseBox2_19":
                                //借款收付彙計數
                                $("#usePar,#useParDate").removeAttr("disabled").prop("readonly", false);
                                $("#useParDate").datepicker();
                                break;
                            case "tab05":
                                $("#itemDscr3").prop("readonly", false);
                                break;
                            case "caseBox2_12":
                                $("#guarantorType").removeAttr("disabled");
                                break;	
							case "caseBox19":
							    //J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
                                $("#applyIsStartUp").show();
                                //J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
                                $("#itwCodeCoreBuss").removeAttr("disabled").prop("readonly", false);
                                break;		
							case "caseBox14":   
		                    	//J-109-0470_05097_B1001 Web e-Loan授信簽案配合本行110年施行LTV法，土建融案件新增案件編號
		                    	$("#newAdcCaseNoBt").show();
		                    	$("#queryAdcCaseNoBt").show();
		                    	break;    
							case "caseBox16":
								$("#cancelExItem").show();
								break;
							case "caseBox31":
		                        //本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困
		                        $("#applySbScore").show();
		                        $("#applyIntroduceData").show();
		                        break;
		                    case "caseBox33":
		                        //J-109-0152 保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構
		                        $("#applyCountryCrd").show();
		                        $("#btn_fcrdGrad").show();
		                        $("#applyRskRatio").show();
		                        $("input[name='isOfficialCga']" ).trigger("change");
		                        break;
                            case "caseBox35":	// 經濟部
                                $("#queryOnL140M01A").show();
                                break;
                            case "caseBox38":
                            	$("input[name='syndIsCmsSpecial_1']" ).removeAttr("disabled");
                            case "caseBox39":
                            	//J-111-0073_05097_B1001 Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
                            	//綠色授信暨永續績效連結授信
                            	$("#esgGreenSpendTypeBT").show();
                            	$("#esgSustainLoanTypeBT").show();
                            	$("#socialKindBT").show();
                                break;
                            
                                break;	    
                        }
                        
                        
                        
                        //當不是在readonly的狀態才可以顯示按鈕
                        if (!inits.toreadOnly) {
                            $(this).parents("td").find("button").show();
                        }
                        if ($(this).hasClass("tabBox")) {
                            $("li[id^=" + $thisId + "]").show();
                        }
                        break;
                }
            }
            else {
                switch ($thisId) {
                    case "caseBox12":
                        //央行房貸註記
                        $("#modifyL140M01M").hide();
						$("#cbRuleButton").hide();
                        $("#pageNumX").prop("disabled", true);
                        break;            
                    case "tab06":
                        $("#itemDscr4").prop("readonly", true);
                        break;
                    case "tab07":
                        $("#itemDscr5").prop("readonly", true);
                        break;
                    case "caseBox2_22":
                        $("#modifyL140M01Q").hide();
                        $("#pageNumY").prop("disabled", true);
                        break;
					case "caseBox29":	// J-108-0293
                        $("#addIntReg").hide();
						$("#deleteIntReg").hide();
                        break;
                    default:
                        if ($thisInput.hasClass("date")) {
                            $thisInput.datepicker('destroy');
                        }
                        switch ($thisId) {
                            case "caseBox2_19":
                                //借款收付彙計數
                                $("#usePar,#useParDate").prop("readonly", true);
                                $("#useParDate").datepicker('destroy');
                                break;
                            case "tab05":
                                $("#itemDscr3").prop("readonly", true);
                                break;
                            case "caseBox2_12":
                                $("#guarantorType").prop("disabled", true);
                                break;
							case "caseBox19":
							    //J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
                                $("#applyIsStartUp").hide();
                                //J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
                                $("#itwCodeCoreBuss").prop("disabled", true).prop("readonly", true);
                                break;		
							case "caseBox14":   
		                    	//J-109-0470_05097_B1001 Web e-Loan授信簽案配合本行110年施行LTV法，土建融案件新增案件編號
		                    	$("#newAdcCaseNoBt").hide();
		                    	$("#queryAdcCaseNoBt").hide();   
		                    	break;    
							case "caseBox16":
								$("#cancelExItem").hide();
								break;
							case "caseBox31":
		                        //本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困
		                        $("#applySbScore").hide();
		                        $("#applyIntroduceData").hide();
		                        break;
                            case "caseBox33":
                                //J-109-0152 保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構
                                $("#applyCountryCrd").hide();
                                $("#btn_fcrdGrad").hide();
                                $("#applyRskRatio").hide();
                                $("input[name='isOfficialCga']" ).trigger("change");
                                break;
                            case "caseBox35":	// 經濟部
                                $("#queryOnL140M01A").hide();
                                break;
                            case "caseBox38":
                                $("input[name='syndIsCmsSpecial_1']" ).prop("disabled", true);
                            case "caseBox39":
                            	//J-111-0073_05097_B1001 Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
                            	//綠色授信暨永續績效連結授信
                            	$("#esgGreenSpendTypeBT").hide();
                            	$("#esgSustainLoanTypeBT").hide();
                            	$("#socialKindBT").hide();
                                break;    
                        }
                        //展開tabs頁面
                        if ($(this).hasClass("tabBox")) {
                            $("li[id^=" + $thisId + "]").hide();
                        }
                        $("#tab03_4,#tab04_2,#tab05_1,#tab05_2").show();   //#tab05_2    J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
                        $thisInput.prop("disabled", true);
                        $(this).parents("td").find("button:not(.forview)").hide();
                        break;
                }
            }
        });

		// J-108-0302 是否符合出口實績規範
        if(page02 && page02.showExperf) {
            $("#show_Experf").show();
            if(page02.flaw_fg == "2") {
                $("#show_flaw_amt").show();
            } else {
                $("#show_flaw_amt").hide();
            }

            if($("#flaw_fg").prop("disabled")){
                $("#flaw_amt").prop("readonly", true);
            } else {
                $("#flaw_amt").prop("readonly", false);
            }
        } else {
            $("#show_Experf").hide();
            $("#show_flaw_amt").hide();
        }

        $('#headItem1Tr_9').hide();     // J-108-0303 連鎖店Chain store
        $('#headItem1Tr_TW').hide();    // J-108-0304 投資台灣三大方案
        if(page01) {
            if(page01.projClass == "09") {
                $('#headItem1Tr_9').show();
                if(page01.chainStore == "1"){
                    $("#mainBiz").find("input").prop("disabled", true);
                    $('#showJoinNum').show();
                } else if(page01.chainStore == "2"){
                    $("#mainBiz").find("input").prop("disabled", false);
                    $('#showJoinNum').hide();
                } else {
                    $("#mainBiz").find("input").prop("disabled", true);
                    $('#showJoinNum').hide();
                }
            } else if(page01.projClass == "10" || page01.projClass == "11" || page01.projClass == "12") {
                $('#headItem1Tr_TW').show();
            }
        }

		// J-108-0303 連鎖店Chain store
		$("#itemSpan_chainStore").change(function(){
            if($("[name='chainStore'][value='1']").is(":checked")){
                var cntrNo = $("#cntrNo").val();
                if(cntrNo ==""){
                    $("[name='chainStore']").prop("checked", false);
                    return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.err015"]);
                }
                $("#mainBiz").find("input").prop("disabled", true);
                $("#mainBizId").val( $("#custId").val() );
                $("#mainBizDupNo").val( $("#dupNo").val() );
                $("#mainBizName").val( $("#custName").val() );
                $("#mainBizCntrNo").val(cntrNo);
                $("#showJoinNum").show();
            } else {
                $("#mainBiz").find("input").prop("disabled", false).val('');
                $("#mainBizCntrNo").val('');
                $("#joinNum").val('');
                $("#showJoinNum").hide();
            }
        });

        //自動帶入姓名
		 $("#mainBizId,#mainBizDupNo").blur(function(){
            var custId = $.trim($("#mainBizId").val());
            var dupNo = $.trim($("#mainBizDupNo").val());
            if ($.trim(custId).length > 0 && $.trim(dupNo).length > 0) {
                var chainStore = $("[name='chainStore']").val();
                if(chainStore == "2") {
                    if(custId==$("#custId").val() && dupNo==$("#dupNo").val()){
                        API.showMessage(i18n.lms1401s02["L140M01a.message226"]);
                    }
                }
                $.ajax({
                    handler: "lms1405m01formhandler",//inits.fhandle,
                    action: "getMisCustData",
                    data: {
                        custId: custId,
                        dupNo: dupNo
                    }
				}).done(function(obj) {
					if (!$.isEmptyObject(obj)) {
					    $("#mainBizName").val(obj.custName);
					    $("#mainBizCntrNo").val('');
					    $.ajax({
					        handler: inits.fhandle,
					        action : 'queryL140M01V',
					        data : {
					            custId: custId,
					            dupNo: dupNo,
					            type: "1"
					        }
						}).done(function(data) {
							$("#mainBizCntrNo").val(data.mainBizCntrNo);
					    });
					} else {
					    $("#mainBizName").val('');
					    $("#mainBizCntrNo").val('');
					}
                });
            }
        });
				
				
        //        $openBox.thickbox({
        //            //title.01 LMS1405 授信管理系統
        //            title: i18n.lms1401s02["title.01"],
        //            width: 980,
        //            height: 555,
        //            readOnly: false,
        //            modal: true,
        //            open: $.noop,
        //            buttons: opendocboxBts
        //        });
		
		 
		//J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
		//營運中心、授審處批覆書不要自動變成是，要不然儲存時會檢核有欄位未輸入
		// if(responseJSON.page != "11" && isLock != true){
		 if(isLock != true){
			 var xformid = $("#tabFormMainId").val();
			 if(xformid != undefined && xformid != null && xformid != ""){
				 var isRevive = $("input[name=isRevive]:checked").val();
		         if(isRevive == undefined || isRevive == null || isRevive == ""){
		        	 	$.ajax({
		                    handler: inits.fhandle,
		                    action: "isReviveDefaultY",
		                    data: {
		                         cntrMainId : xformid
		                    }
						}).done(function(dfObj) {
							if(dfObj.defaultY == "Y"){
								 var isRevive = $("input[name=isRevive]:checked").val();
							        if(isRevive == undefined || isRevive == null || isRevive == ""){
							        	//預設值為是 OOXX
							       	//alert("isLock="+isLock + "，responseJSON.page="+responseJSON.page);
							        	$("input[name='isRevive'][value='Y']:radio" ).prop( "checked" , true ).trigger("click").trigger("change");   //塞值       
							        } 
							}
		                });
		         }
			 }
		 }	 
		 //}

        //J-109-0152 保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構
        //再觸發一次去鎖定地區別跟期間別
        $("input[name='isOfficialCga']").trigger("change");
		 
    	//讀取 J-108-0097 購置高價住宅貸款檢核表
		CntrNoAPI.loadHighPricedHousingLoanCheckList(isLock);
    },
    
    //判斷是否為007 025 201 918 931~935 是回傳true
    isCheckShowSpecialBrid: function(){
        //目前登錄分行id
        var ownbrId = userInfo ? userInfo.unitNo : "", val = false;
        switch (ownbrId) {
            case "007":
            case "025":
            case "201":
            case "918":
            case "931":
            case "932":
            case "933":
            case "934":
            case "935":
			case "940":
			case "943":
			case "149":	
                val = true;
                break;
        }
        return val;
    },
    //針對額度批覆表lock
    todoLockforPage11: function(readOnly, data){
        //限額條件 附表下拉選單 和利費率 附表下拉選單
        var $pageNum = $("select[id^=pageNum]");
        var $formA = $("#L140M01AForm1,#L140M01AForm2");
        var $formB = $("#L140M01BForm");
        var $openBox = $("#opendocBox");
        //隱藏部分頁面
        $("#tab03_1,#tab03_2,#tab03_3").hide();    //#tab05_1 J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
        //將頁面的以這幾頁開起
        $("#tab03_4,#tab04_2,#tab05_1,#tab05_2").find("a").click();   //#tab05_1 J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
        //隱藏按鈕限額組字串, 利費率組字串
        $openBox.find("#limitWordAll,#tabRateDrcLink,#reloadCMS").hide();
        if (readOnly) {
            $(".caseSpan").show().find(".caseBox").prop("disabled", true).removeAttr("checked");
            $pageNum.prop("disabled", readOnly);
            $formA.lockDoc();
            $formB.lockDoc();
            $("button:not(.forview)", $openBox).hide();
            thickboxOptions.customButton = [i18n.def['close'], 'close'];
            $("#caseBox2_1").show();
            inits.toreadOnly = readOnly;
            thickboxOptions.readOnly = readOnly;
        }
        else {
            $formA.lockDoc();
            //顯示批附表需要的勾選框並初始化勾選框
            $(".caseSpan").show().find(".caseBox").removeAttr("disabled").removeAttr("checked");
            $(".nodisabled").removeAttr("disabled").prop("readonly", false);
            $formB.find("#itemDscr6,#itemDscr7,#itemDscr8,#itemDscrA").each(function(){
                $(this).readOnly(false);
            });
            $(".noHideBt").show();
            $("#caseBox2_1").show();
            //當來源等於聯行要隱藏性質按鈕
            //2013-04-09,Rex, 註解掉 beg 
            //            if (data && data.dataSrc == "3") {
            //                $("#caseBox2_1").hide();
            //            }
            //2013-04-09,Rex, 註解掉 end
            inits.toreadOnly = readOnly;
            thickboxOptions.readOnly = readOnly;
        }
        //來源為條件續約變更 要顯示 變更前變更後按鈕
        $("#showBeforeBt,#showFirstBossSpan").hide();
        if (data && data.dataSrc == "4") {
            $("#showBeforeBt,#showFirstBossSpan").show();
        }
    },
    /**
     * 針對額度明細表lock
     * @param {Boolean} readOnly 是否執行 readOnly
     * @param {Object} data L140M01A 的資料
     */
    todoLock: function(readOnly, data){
        //限額條件 附表下拉選單 和利費率 附表下拉選單
        var $pageNum = $("select[id^=pageNum]");
        var $formA = $("#L140M01AForm1,#L140M01AForm2");
        var $formB = $("#L140M01BForm");
        var $openBox = $("#opendocBox");

        $pageNum.prop("disabled", readOnly);
        if (readOnly) {
            $formA.lockDoc();
            $formB.lockDoc();
            $("button:not(.forview)", $openBox).hide();
            thickboxOptions.customButton = [i18n.def['close'], 'close'];
            inits.toreadOnly = readOnly;
            thickboxOptions.readOnly = readOnly;
            $("#itemDscr3").prop("readOnly", true);
        }
        else {
            $("#toALoan1,#toALoan2").prop("readonly", false).removeAttr("disabled");
            $("#gutCutDate").datepicker();
            $("input:not(.caseReadOnly),textarea:not(.caseReadOnly),select", $formA).prop("readonly", false).removeAttr("disabled");
            $formB.find("#itemDscr3,#itemDscr4,#itemDscr5,#itemDscr6,#itemDscr7,#itemDscr8").prop("readonly", false).removeAttr("disabled");
            $("#itemDscr3").prop("readonly", false).removeAttr("disabled");
            ;
            $("button,.fg-buttonset", $formA).show();
            $(".noHideBt").show().parents(".fg-buttonset").show();
            $("#limitWordAll,#tabRateDrcLink,#reloadCMS").show().parents(".fg-buttonset").show();
            //當來源等於聯行要隱藏性質按鈕
            $("#loginTypeBT").show();
			//J-103-0299-001  Web e-Loan企金額度明細表保證人新增保證比例
            $("#L140M01AForm2").find("input[name='guaPercentFg']").prop("readOnly", true).prop("disabled", true);
            //J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
            $("#L140M01AForm2").find("input[name='guaNaExposure']").prop("readOnly", true).prop("disabled", true);
            //2013-04-09,Rex, 註解掉 beg
            // if (data && data.dataSrc == "3") {
            //  $("#loginTypeBT").hide();
            //}
            //2013-04-09,Rex, 註解掉 end
            inits.toreadOnly = readOnly;
            thickboxOptions.readOnly = readOnly;
        }
        //來源為條件續約變更 要顯示 變更前變更後按鈕
        $("#showBeforeBt,#showFirstBossSpan").hide();
        if (data && data.dataSrc == "4") {
            $("#showBeforeBt,#showFirstBossSpan").show();
        }
    },
    /** 引進帳務*/
    getMoneyData: function(){
        var cntrNo = $("#cntrNo").val() || false, lastValue = $("#LVCurr").val() || false;
        if (!cntrNo) {
            //請輸入額度序號
            return CommonAPI.showMessage(i18n.lms1401s02["L140M01a.error18"] + i18n.lms1401s02["L140M01a.cntrNo"]);
        }
        
        //請輸入前准額度
        //L782M01A.applyCurr=幣別
        if (!lastValue) {
            return CommonAPI.showMessage(i18n.lms1401s02["L140M01a.error18"] + i18n.lms1401s02["L140M01a.lastValue"] + i18n.lms1401s02["L782M01A.applyCurr"]);
        }
        $.ajax({
            handler: inits.fhandle,
            action: "queryMoneyData",
            data: {
                oid: $("#tabFormId").val(),
                cntrNo: cntrNo,
                lastValue: lastValue
            }
		}).done(function(obj) {
			$('#L140M01AForm2').injectData(obj);
        });//close ajax
    },
    /**  檢查是否有登錄授信科目  */
    isCheckL140M01C: function(){
        var temp = true;
        $.ajax({
            async: false,
            handler: inits.fhandle,
            action: "queryL140m01c",
            data: {
                tabFormMainId: $("#tabFormMainId").val(),
                noOpenDoc: true
            }
		}).done(function(obj) {
			if (obj.count == '0') {
			    temp = false
			}
        });//close ajax
        return temp;
    },
    /**  特殊登錄案件*/
    special: function(){
        if (!CntrNoAPI.isCheckL140M01C()) {
            //L782M01A.error 請先登錄授信科目
            return CommonAPI.showMessage(i18n.lms1401s02["L782M01A.error"]);
        }
        $('#special').find(":disabled,:input").removeAttr("disabled").prop("readonly", false).end().find("#dispatchDate").datepicker();
        $('#L782M01AForm').reset();
        $.ajax({
            handler: inits.fhandle,
            action: "querySpecial",
            data: {
                tabFormId: $("#tabFormId").val(),
                tabFormMainId: $("#tabFormMainId").val()
            }
		}).done(function(obj) {
			$('#L782M01AForm').injectData(obj);
			$("#L782M01AForm #SplieloanTP").setItems({
			    item: obj.item,
			    format: "{value} - {key}"
			});
			$("#special").thickbox({
			    //title.07=特殊案件登記表
			    title: i18n.lms1401s02['title.07'],
			    width: 900,
			    height: 370,
			    modal: true,
			    readOnly: false || _openerLockDoc == "1",
			    i18n: i18n.def,
			    buttons: {
			        "saveData": function(){
			            if ($("#L782M01AForm").valid()) {
			                FormAction.open = true;
			                $.ajax({
			                    handler: inits.fhandle,
			                    action: "saveL782m01a",
			                    data: {
			                        tabFormMainId: $("#tabFormMainId").val()
			                    }
							}).done(function(responseData) {
								FormAction.open = false;
								$('#L782M01AForm').injectData(responseData);
			                });
			            }
			        },
			        //	      "print" : function(){
			        //	      		$.thickbox.close();
			        //	      },
			        "close": function(){
			            $.thickbox.close();
			        }
			    }
			});
        });//close ajax
    },
    /**thickBox 儲存 */
    save_CntrDoc: function(type, doPrint){
        var npldate = $("#npldate").val();
        if (npldate != "" && !npldate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
            //val.date2=日期格式錯誤(YYYY-MM)
            //L140M01a.nPL=分行逾放比
            return CommonAPI.showMessage(i18n.lms1401s02['L140M01a.nPL'] + i18n.def["val.date2"]);
        }
        //在save的時候把moveDurOtEnd、moveDurOtFrom 組起來放在moveDurOt
        if ($("#useDeadline").val() == 1) {
            var end = $("#moveDurOtEnd").val().split("-");
            var from = $("#moveDurOtFrom").val().split("-");
            var endData = new Date(end[0], end[1], end[2]);
            var fromData = new Date(from[0], from[1], from[2]);
            if (fromData > endData) {
                //L140M01a.useDeadline=動用期限
                //L140M01a.error37=起始日期不能大於結束日期
                return CommonAPI.showErrorMessage(i18n.lms1401s02["L140M01a.useDeadline"] + i18n.lms1401s02["L140M01a.error37"]);
            }
            $("#desp1").val($("#moveDurOtFrom").val() + ' ~ ' + $("#moveDurOtEnd").val());
        }
        
        if (inits.itemType != "1") {
            var checkData = $(".caseBox:checked").map(function(){
                return $(this).attr("id");
            }).toArray().join("|");
        }
		
		
		if (userInfo.unitType != "2" && userInfo.unitType != "4") {
			//分行報案時儲存才清，已經到授管處、營運中心者不清，免得額度明細表與額度批覆書內容不一致
		    var isBuy= $("input[name='isBuy']:radio:checked" ).val(); 
			if(!isBuy){  
			    $("input[name='is722Flag']:radio:checked").each(function(){
				     $(this).removeAttr("checked");
				});
			}
		}
        
        //提示使用者尚未填妥項目
        if (!$("#L140M01AForm1").valid() || !$("#L140M01AForm2").valid() || !$("#L140M01BForm").valid()) {
            //common.001=欄位檢核未完成，請填妥後再送出
            API.showMessage(i18n.def["common.001"]);
            return false;
        }
        $.ajax({
            handler: inits.fhandle,
            action: "saveL140m01a",
            data: {
                caseType: inits.itemType,
                pageNum1: $("#pageNum1").val(),
                pageNum2: $("#pageNum2").val(),
                pageNum3: $("#pageNum3").val(),
                itemDscr3: $("#itemDscr3").val(),
                itemDscrH: $("#itemDscrH").val(),
                noOpenDoc: true,
                tabFormId: $("#tabFormId").val(),
                changeCode: checkData || "",
                L140M01AForm: JSON.stringify($("#L140M01AForm1,#L140M01AForm2,#L140M01AForm5").serializeData()),
                L140M01BForm: JSON.stringify($("#L140M01BForm").serializeData()),
                showMsg: type
            }
		}).done(function(obj) {
            if (obj.msg && obj.msg != "") {
                CommonAPI.showErrorMessage("", obj.msg, function(){
                    //當攤貸時還有餘額，要跳出餘額修改視窗
                    if (obj.l140m01eAmt != 0) {
                        CntrNoAPI.l140m01eAmtBox(obj.l140m01eAmt);
                    }
                });
            }
            //showTips 提醒 額度種類
            if (obj.showTips && obj.showTips != "") {
                CommonAPI.showErrorMessage(obj.showTips);
            }
            
            CntrNoAPI._triggerMainGrid();
            $("#gridviewitemChildren3").trigger("reloadGrid");
            $('#L140M01AForm1').injectData(obj.L140M01AForm1);
            $('#L140M01AForm2').injectData(obj.L140M01AForm2);
            
            
            //預設選取衍生性商品期數
			var derivativeShow = "";
            if (obj.L140M01AForm2.derivatives) {					
                var dervChecked = obj.L140M01AForm2.derivatives.split("|");
                for (var i in dervChecked) {
					//$('#derivatives option[value=' + dervChecked[i] + ']').attr('selected', 'selected');
					derivativeShow = derivativeShow + (derivativeShow != "" ? "," : "" )+  i18n.dervPeriodCodeType[dervChecked[i]] ;   //"L140M01a.DERVPERIOD" + 
                }
            }
            $('#L140M01AForm2').find("#derivativeShow").val(derivativeShow);
            //adjust722UI();
			//$("input[name='isBuy']:checked").triggerHandler("click");
			//$("input[name='isBuyOn']:checked").triggerHandler("change");
			$("#isNonSMEProjLoan").trigger("change");
			
			//J-105-0135-001 Web e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
			$("#cIsNonSMEProjLoan").trigger("change");
 
			//J-106-0085-002  Web e-Loan企金授信新增主要還款來源國等相關欄位
			$("input[name='isFreezeFactCountry']" ).triggerHandler("change");
			$("input[name='isNoFactCountry']" ).triggerHandler("change");
			$("input[name='inSmeFg']" ).triggerHandler("change");
				
			//J-109-0077_05097_B1005 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			$("input[name='isRescue']" ).triggerHandler("change");
			$("input[name='isRescueOn']" ).triggerHandler("change");
			
			//J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			$("#rescueItem" ).triggerHandler("change");
			
			$("input[name='isCbRefin']" ).triggerHandler("change");
			$("input[name='isSmallBuss']" ).triggerHandler("change");
			$("input[name='sbHasCreditRating']" ).triggerHandler("change");
			//J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
            $("input[name='isRevive']" ).triggerHandler("change");
            // J-112-0200 中小企業千億振興融資方案
            $("input[name='isRevital']").triggerHandler("change");
            // J-112-0148 疫後振興
            $("input[name='attachDoc']").triggerHandler("change");
            $("input[name='attachDoc2']").triggerHandler("change");
            //Web e-Loan於簽報央行C方案件中增列總處單位引介資料
            //$("input[name='isRescueIntroduce']" ).triggerHandler("click");
            
			//J-109-0152 保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構
            $("input[name='isOfficialCga']" ).triggerHandler("change");
            
            //J-109-0239_05097_B1001 Web e-Loane-Loan授信管理系統案件簽報書之額度明細表新增「特殊融資或不動產ADC融資暴險註記」
            $("input[name='isSpecialFinRisk']" ).triggerHandler("change");
            
            //J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
            $("#specialFinRiskType" ).trigger("change");
            
            //J-109-0352_05097_B1001 (109) 第 2684 號 國內企金授信額度明細表新增「109年9至12月提升中小企業放款方案」
            $("input[name='headItem2']" ).triggerHandler("change");
                
            //J-110-0340_05097_B1001 Web e-Loan額度明細表新增授信作業手續費相關資訊
            $("input[name='isOperationFee']" ).triggerHandler("change");
            $("input[name='operationFeeWay']").triggerHandler("change");
            
            //J-110-0485_05097_B1001 於簽報書新增LGD欄位
            $("input[name='unitCase2']" ).triggerHandler("change");
            
            //J-110-0485_05097_B1001 於簽報書新增LGD欄位
            $("input[name='isEfin']" ).triggerHandler("change");
                
            //J-111-000A_05097_B1001 Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
			// $("input[name='isBuy']:checked").triggerHandler("click");
			// $("input[name='isBuyOn']:checked").triggerHandler("change");
			                
            $("input[name='isEsgGreenLoan']" ).triggerHandler("change");
            $("input[name='esgSustainLoan']" ).triggerHandler("change");
            $("input[name='esgSustainLoanUnReach']" ).triggerHandler("change");
            $("#esgGreenSpendType").triggerHandler("change");
            
            //J-113-0329 企金授信新增社會責任授信
            $("input[name='socialLoanFlag']" ).trigger("change");
            $("input[name='socialKind']" ).trigger("change");
            $("input[name='socialTa']" ).trigger("change");
            $("input[name='socialResp']" ).trigger("change");
			                
			// J-108-0116 共同行銷
			var page02 = obj.L140M01AForm2;
			var csTypes = "";
  			if (page02.csTypes) {
                var csTypesArr = page02.csTypes.split("|");
				if(page02.checkYN_A == "Y" && page02.result_A != ""){
					$("#result_A").show();
					if(page02.result_A == "A1"){
						$("#select_A").show();
						$("#memo_A").show();
						$("#mtitle_A").text(i18n.lms1401s02['person']);
					}
				}
				ilog.debug(page02.csTypes);
                for (var i in csTypesArr) {
					var csType = csTypesArr[i];
					if(csType != "A") {
						// A 一定有 so 不用判斷
						$(".CS_"+csType).show();
					}
					if(csType == "B"){
						if (page02.checkYN_B == "Y") {
							$("#result_B").show();
							if (page02.result_B == "B1") {
								$("#select_B").show();
								$("#memo_B").show();
								$("#mtitle_B").text(i18n.lms1401s02['person']);
							}
						} else if(page02.checkYN_B == "N"){
							$("#memo_B").show();
							$("#mtitle_B").text(i18n.lms1401s02['reason']);
						}
					} else if(csType == "C"){
						if (page02.checkYN_C == "Y") {
							$("#result_C").show();
							if (page02.result_C == "C1") {
								$("#select_C").show();
								$("#memo_C").show();
								$("#mtitle_C").text(i18n.lms1401s02['person']);
							}
						} else if(page02.checkYN_C == "N"){
							$("#memo_C").show();
							$("#mtitle_C").text(i18n.lms1401s02['reason']);
						}
					}
                }				
            }

            // J-108-0283 變更條件Condition Change
            if (page02 && page02.LMS140S05Ashow) {
                $("#queryL140S05A").show();
            } else {
                $("#queryL140S05A").hide();
            }

            // J-108-0302-001 是否符合出口實績規範
            if(page02 && page02.showExperf) {
                $("#show_Experf").show();
                if(page02.flaw_fg == "2") {
                    $("#show_flaw_amt").show();
                } else {
                    $("#show_flaw_amt").hide();
                }

                if($("#flaw_fg").prop("disabled")){
                    $("#flaw_amt").prop("readonly", true);
                } else {
                    $("#flaw_amt").prop("readonly", false);
                }
            } else {
                $("#show_Experf").hide();
                $("#show_flaw_amt").hide();
            }

            var page01 = obj.L140M01AForm1;
            $('#headItem1Tr_9').hide();     // J-108-0303 連鎖店Chain store
            $('#headItem1Tr_TW').hide();    // J-108-0304 投資台灣三大方案
            if(page01) {
                if(page01.projClass == "09") {
                    $('#headItem1Tr_9').show();
                    if(page01.chainStore == "1"){
                        $("#mainBiz").find("input").prop("disabled", true);
                        $('#showJoinNum').show();
                    } else if(page01.chainStore == "2"){
                        $("#mainBiz").find("input").prop("disabled", false);
                        $('#showJoinNum').hide();
                    } else {
                        $("#mainBiz").find("input").prop("disabled", true);
                        $('#showJoinNum').hide();
                    }
                } else if(page01.projClass == "10" || page01.projClass == "11" || page01.projClass == "12") {
                    $('#headItem1Tr_TW').show();
                }
            }

            if (doPrint) {
                CntrNoAPI.print_CntrDoc();
            }
            
            //J-108-0122 配合檢查科目 719,819預付款時跳出提醒視窗
            if (obj.showTips2 && obj.showTips2 != "") {
            	CommonAPI.showMessage(obj.showTips2);
            }

			$("#gridviewCollateral").trigger('reloadGrid');
        });
    },
    /**  thickBox 刪除  */
    delete_CntrDoc: function(){
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.thickbox.close();
                $.ajax({
                    handler: inits.fhandle,
                    action: "deleteL140m01a",
                    data: {
                        caseType: inits.itemType,
                        mainId: $("#mainId").val(),
                        tabFormMainId: $("#tabFormMainId").val(),
                        showMsg: true
                    }
				}).done(function(responseData) {
					CntrNoAPI._triggerMainGrid();
					//$.thickbox.close();
					window.close();
                });
            }
        });
    },
    /**  thickBox 列印  */
    pre_Print_CntrDoc: function(type, preSave){
        //儲存後列印
        if (preSave == true) {
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    CntrNoAPI.save_CntrDoc(type, true);
                    
                }
            })
        }
        else {
            CntrNoAPI.print_CntrDoc();
        }
        
    },
    print_CntrDoc: function(){
    
        var pdfName = "LMS1401R01.pdf";
        var rType = "R12"
        if (inits.itemType == "1") {
            pdfName = "LMS1401R01.pdf";
            rType = "R12"
        }
        else 
            if (inits.itemType == "2") {
                pdfName = "LMS1401R02.pdf";
                rType = "R13"
            }
            else {
                pdfName = "LMS1401R01.pdf";
                rType = "R12"
            }
        
        var content = ""; //R12^10C4E73B266C11E3B4D7126EC0A83B82^73251209^0^006109290001^
        content = rType + "^" + $("#tabFormId").val() + "^" + $("#formTabTable1").find("#custId").val() + "^" + $("#formTabTable1").find("#dupNo").val() + "^" + $("#L140M01AForm2").find("#cntrNo").val() + "^" + $("#tabFormMainId").val() + "^";
        pdfName = pdfName;
        
        //$.thickbox.close();
        $.form.submit({
            url: "../../app/simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                rptOid: content,
                fileDownloadName: pdfName,
                serviceName: "lms1201r01rptservice"
            }
        });
        
        
    },
	print_Doc: function(doc, type, preSave){
		var fileDownloadName = doc + ".doc";
		$.capFileDownload({
            handler: "lmsdownloadformhandler",
            data: {
				tabFormMainId: $("#tabFormMainId").val(),
                contractType: doc,
                fileDownloadName: fileDownloadName,
                serviceName: "LmsContractDocWService"//"lms9990doc01service"
            }
        });
    },
    print_Lgd: function(){
        //J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
        var pdfName = "LMS1201R44.pdf";
        var rType = "R44"
        var content = ""; //R12^10C4E73B266C11E3B4D7126EC0A83B82^73251209^0^006109290001^
        content = rType + "^" + "" + "^" + "" + "^" + "" + "^" + "" + "^" + "" + "^";
        pdfName = pdfName;
        
        //$.thickbox.close();
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                rptOid: content,
                fileDownloadName: pdfName,
                caseType : inits.itemType, //文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
                serviceName: "lms1201r01rptservice"
            }
        });
        
        
    },
    changeSuggestMode: function(suggestMode){
        // J-111-0461 建議審核層級
    	// 2023.09.28 在計算額度合計時，可以選擇by借款人還是合併借款人
    	// 純粹塞值給後續ajax to 後端時使用
    	responseJSON.suggestMode = suggestMode;
    },
    /** 當現請額度大於攤貸總金額時 會出現 grid選擇  將餘額加至哪筆 */
    l140m01eAmtBox: function(amt){
        //L140M01a.message70=聯行攤貸金額尚餘 {0}元，請選擇要將餘額加進何筆資料!
        $("#l140m01eAmtMsg").html(i18n.lms1401s02['L140M01a.message70'].replace("{0}", amt))
        
        if (l140m01eAmtGridDef.state() == "resolved") {
            $("#l140m01eAmtGrid").setGridParam({//重新設定grid需要查到的資料
                postData: {
                    tabFormMainId: $("#tabFormMainId").val()
                },
                search: true
            }).trigger("reloadGrid");
        }
        else {
            l140m01eAmtGridDef.resolve();
        }
        
        $("#l140m01eAmtBox").thickbox({
            title: "",
            width: 600,
            height: 350,
            align: "center",
            valign: "bottom",
            modal: true,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var id = $("#l140m01eAmtGrid").getGridParam('selrow');
                    if (id == null || !id) {
                        //action_005=請先選取一筆以上之資料列
                        return CommonAPI.showErrorMessage(i18n.def['action_005']);
                    }
                    var oid = $("#l140m01eAmtGrid").getRowData(id).oid;
                    $.thickbox.close();
                    $.ajax({
                        handler: inits.fhandle,
                        action: "saveL140m01eAmt",
                        data: {
                            oid: oid,
                            amt: util.delComma(amt),
                            tabFormMainId: $("#tabFormMainId").val()
                        }
					}).done(function(responseData) {
						if (dfd33 && dfd33.state() == "resolved") {
						    if (responseData && responseData.drc) {
						        $("#itemDscr1").val(responseData.drc);
						    }
						    $("#gridviewitemChildren3").trigger("reloadGrid");
						}
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**   
     * 下拉選單
     * */
    opentionFn: function(){
		
		//J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
    	//J-107-0357_05097_B1001 Web e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
    	//J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
    	//J-112-0438_12473_B1001  引介來源及相關細項選單
        var obj = CommonAPI.loadCombos(["CountryCode", "Common_Currcy", "lms1405m01_SpecialCaseType", "lms1405m01_snoKind",
		"lms1405s02_derivatives", "lms1405s02_useDeadline", "lms1405s02_noInsuReason", 
		"lms1405s02_checkNote", "lms1205s01_RelClass", "Relation_type1", "Relation_type2", 
		"Relation_type31", "Relation_type32", "lms1405m01_SubItem", 
		"lms1405s0202_sbjProperty", "lms1405s0202_reUse", "lms1405s0202_otherCurr", 
		"lms140_noLoan", "lms140_unsecureFlag", "lms140_residential", "lms1605s03_rType", 
		"lms140_lnType","lms140_itwCode", "lms140_projClass",
		"lms7800_resultA","lms7800_resultB","lms7800_resultC",
		"lms140_flawFg","chainStore","projTW_use","lms140_itwCodeCoreBuss",
		"L140M01A_introductionSource_lms", "L140S02A_megaCode", "lms1401s02_derivEval"]);

        //國別
        $(".country").setItems({
            item: obj.CountryCode,
            format: "{value} - {key}"
        });
        //幣別
        $(".money").setItems({
            item: obj.Common_Currcy,
            format: "{value} - {key}"
        });
        //特殊登錄案件-歸類
        $("#caseType").setItems({
            item: obj.lms1405m01_SpecialCaseType,
            format: "{value} - {key}"
        });
        //額度控管種類
        $("#snoKind").setItems({
            item: obj.lms1405m01_snoKind,
            format: "{value} - {key}"
        });
        
        //衍生性金融商品
        $("#derivatives").setItems({
            item: obj.lms1405s02_derivatives,
            format: "{value} - {key}"
        });
        //動用期限
        $("#useDeadline").setItems({
            item: obj.lms1405s02_useDeadline,
            format: "{value} - {key}"
        });
        
        //本案未送保原因
        $("#noInsuReason").setItems({
            item: obj.lms1405s02_noInsuReason,
            format: "{value} - {key}"
        });
        
        //請選擇本票詞庫
        $("#localPageSelect").setItems({
            item: obj.lms1405s02_checkNote,
            format: "{value} - {key}"
        });
        if (!$.isEmptyObject(obj) && obj.lms1205s01_RelClass) {
            delete obj.lms1205s01_RelClass[""];
        }
        //連保人關係
        $("#relationshipSelect").setItems({
            item: obj.lms1205s01_RelClass,
            format: "{value} - {key}"
        });
        //連保人關係
        $("#rationSelect1").setItems({
            item: obj.Relation_type1,
            format: "{value} - {key}"
        
        });
        //連保人關係
        $("#rationSelect2").setItems({
            item: obj.Relation_type2,
            format: "{value} - {key}"
        });
        //連保人關係
        $("#rationSelect31").setItems({
            item: obj.Relation_type31,
            format: "{value} - {key}"
        });
        //連保人關係
        $("#rationSelect32").setItems({
            item: obj.Relation_type32,
            format: "{value} - {key}"
        });
        
        //額度性質
        $("#sbjProperty").setItems({
            item: obj.lms1405s0202_sbjProperty,
            format: "{key}"
        });
        //是否循環使用
        $("#reUse").setItems({
            item: obj.lms1405s0202_reUse,
            format: "{key}"
        });
        //或等值其他外幣
        $("#otherCurr").setItems({
            item: obj.lms1405s0202_otherCurr,
            format: "{key}"
        });
        
        //同一人/同一關係企業受限額註計項目
        $("#noLoan").setItems({
            item: obj.lms140_noLoan,
            format: "{value}.{key}"
        });
		//利害關係人無擔保註記
        $("#unsecureFlag").setItems({
            item: obj.lms140_unsecureFlag,
            format: "{value}.{key}"
        });
        /**
         *是否興建房屋
         */
        $("#residential").setItems({
            item: obj.lms140_residential,
            format: "{key}"
        });
        /**
         *是否興建房屋
         */
        $("#lnType").setItems({
            item: obj.lms140_lnType,
            format: "{key}"
        });
		
		//J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
		//新創產業細目
        $("#itwCode").setItems({
            item: obj.lms140_itwCode,
            format: "{value}.{key}"
        });
        
        //J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
		//六大核心戰略產業細目
        $("#itwCodeCoreBuss").setItems({
            item: obj.lms140_itwCodeCoreBuss,
            format: "{value}.{key}"
        });
        
        //J-107-0357_05097_B1001 Web e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
        $("#projClass").setItems({
            item: obj.lms140_projClass,
            format: "{key}"
        });
		
		//J-108-0116 共同行銷
        $("#result_A").setItems({
            item: obj.lms7800_resultA,
            format: "{key}"
        });
		$("select#result_A option[value=A3]").remove();
		
    	$("#result_B").setItems({
            item: obj.lms7800_resultB,
            format: "{key}"
        });
		$("select#result_B option[value=B3]").remove();

        $("#result_C").setItems({
            item: obj.lms7800_resultC,
            format: "{key}"
        });
        $("select#result_C option[value=C3]").remove();
		this._getItem();
		//============共同行銷	End

		//J-108-0302-001 是否符合出口實績規範
        $("#flaw_fg").setItems({
            item: obj.lms140_flawFg,
            format: "{value}.{key}"
        });

        //J-108-0303-001 連鎖店
        $("#chainStore").setItems({
            item: obj.chainStore,
            format: "{key}"
        });

        //J-108-0304-001 投資台灣三大方案
        $("#projTW_use").setItems({
            item: obj.projTW_use,
            format: "{key}"
        });

        //J-109-0309_05097_B1001 Web e-Loan企金授信觀光局利息補貼額度控管及新增營業所在地欄位
        $("#rescueCity").setItems({
            item: QueryCityCode.getCode("1", ""),
            format: "{key}"
        });
        
        //J-112-0438_12473_B1001 引介來源及相關細項選單
        $("#introduceSrc").setItems({
            item: obj.L140M01A_introductionSource_lms,
            format: "{value}.{key}"
        });
        $("#megaCode").setItems({
            item: obj.L140S02A_megaCode,
            format: "{value}.{key}"
        });

        $("#derivEval").setItems({
            item: obj.lms1401s02_derivEval,
            format: "{key}"
        });
        
        if (responseJSON["readOnly"] == "true") {
            $("#opendocBox").find("select").prop("disabled", true);
        }

        //J-109-0152 保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構
        gridviewCrdGrade();
        
        
    },
	_getItem: function(){
		$.ajax({
			handler: inits.fhandle,//handler:"lms7800m01formhandler", 
			action:'querySynBankOfCsTypes',//'querySynBank',
			async: false
		}).done(function(json) {
			$("[name='select_A']").setItems({
			    item: json.select_AList,
			    format: "{key}"
			});
			$("[name='select_B']").setItems({
			    item: json.select_BList,
			    format: "{key}"
			});
			$("[name='select_B']").append($('<option>', {
			    value: 'X11',
			    text: '財務處'
			}));
			$("[name='select_C']").setItems({
			    item: json.select_CList,
			    format: "{key}"
			}); 
		});
	},
    /**   
     *依照畫面上的值檢查是否需要利害關係人欄位
     *J-105-0250-001  Web e-Loan 新增利害關係人檢核
     *
     * */
    chkIsNeedUnsecureFlag: function(){
		
		var selectSbjProperty = $("#sbjProperty").val();
		
        $.ajax({
            handler: inits.fhandle,
            action: "chkIsNeedUnsecureFlag",
            data: {//把資料轉成json
                tabFormMainId: $("#tabFormMainId").val(),
                selectSbjProperty:selectSbjProperty
            }
		}).done(function(obj) {
			CntrNoAPI.setNeedUnsecureFlag(obj); 
			var $unsecureFlagSpan = $("#unsecureFlagSpan");
			if(obj.needUnsecureFlag == "Y"){
				$unsecureFlagSpan.show();
			}else{
				$unsecureFlagSpan.hide();
				CntrNoAPI.cleanTrHideInput($unsecureFlagSpan);
			}

			//J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
			$("input[name='isEfin']").trigger("change");
        });
    },
    /**   
     *J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
     *
     * */
    chkIsNoneHedge: function(){

        $.ajax({
            handler: inits.fhandle,
            action: "chkIsNoneHedge",
            data: {//把資料轉成json
                tabFormMainId: $("#tabFormMainId").val()
            }
		}).done(function(obj) {
			if(obj.isNoneHedge == "Y"){
				$(".showNoneHedge").show();
			}else{
				$(".showNoneHedge").hide();
			}			
        });
    },
    /**
     * 是否徵提保證金
     * J-109-0365_05097_B1001 Web e-Loan國內企金授信額度明細表科目為遠期外匯、換匯交易時，新增是否徵提保證金等相關欄位
     */
    chkIsNeedMarginFlag: function(){
		 
    	 $.ajax({
             handler: inits.fhandle,
             action: "chkIsNeedMarginFlag",
             data: {//把資料轉成json
                 tabFormMainId: $("#tabFormMainId").val()
             }
		 }).done(function(obj) {
			if(obj.isMarginFlag == "Y"){
				$(".showMarginFlag").show();
			}else{
				$(".showMarginFlag").hide();
			}
         });
    },
    chkIsNeedDerivEval: function(){
         $.ajax({
             handler: inits.fhandle,
             action: "chkIsNeedDerivEval",
             data: {//把資料轉成json
                 tabFormMainId: $("#tabFormMainId").val()
             }
		 }).done(function(obj) {
			if(obj.isDerivEval == "Y"){
			    $("#derivEvalSpan").show();

			    if(obj.needEvalDate == "Y"){
			        $(".evalDateDiv").show();
			    } else {
			        $(".evalDateDiv").hide();
			        $("#evalDate").val('');
			    }
			}else{
			    $("#derivEvalSpan").hide();
			    CntrNoAPI.cleanTrHideInput($("#derivEvalSpan"));
			}
         });
    },
    /**   
     *J-110-0485_05097_B1001 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
     *
     * */
    initGlobalVar: function(){

        $.ajax({
            handler: inits.fhandle,
            action: "initGlobalVar",
            data: {//把資料轉成json
                tabFormMainId: $("#tabFormMainId").val(),
                rptMainId: responseJSON.mainId
            }
		}).done(function(obj) {
			if(obj.showLgdEffect == "Y"){
				$(".showLgdEffect").show();
			}else{
				$(".showLgdEffect").hide();
			}

			//J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
			//J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
			if(obj.showLgdTotAmt == "Y"){
				
				//J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
				g_lmsLgdCountTotal = obj.lmsLgdCountTotal;
				
				//J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
				for (var i = 1; i <= obj.lmsLgdCountTotal; i++) {
					if(obj["showLgdTotAmt_"+i] == "Y"){
			    		$(".showLgdTotAmt_"+i).show();
					}else{
						$(".showLgdTotAmt_"+i).hide();
					}
				}		
			}else{
				//J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
				for (var i = 1; i <= obj.lmsLgdCountTotal; i++) {
					$(".showLgdTotAmt_"+i).hide();
				}	
			}

			//J-111-000A_05097_B1001 Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
			//簽報書編制中，且額度明細表可編輯時，若isEsgGreenLoan綠色授信註記為空，且屬於六大綠色產業時，則塞預設值
			if ( obj.setIsEsgGreenLoanY == "Y"){
				 $("input[name='isEsgGreenLoan'][value='Y']:radio" ).prop( "checked" , true );   //塞值
				 $("input[name='isEsgGreenLoan']").trigger('change');
			}
        });    
    },
	//J-108-0097 購置高價住宅貸款檢核表
	loadHighPricedHousingLoanCheckList: function (isLock){
		
        var l140m01aMainId = $("#tabFormMainId").val();
	    $('#L140M01AForm1').find("#grid1").iGrid({
			handler: 'lmscommongridhandler',
			action: "queryCheckListOfHighPricedHousingLoan",
			height: 50,
			width: 80,
			autowidth: true,
			shrinkToFit: false,
			async:false,
			needPager: false,
			postData: {
	             l140m01aMainId: l140m01aMainId,
				 custId: $("#custId").val(),
				 dupNo:  $("#dupNo").val()
	        },
			colModel: [{
			    colHeader: '類別',
			    name: 'type',
				align: 'left',
				width: 150,
			    sortable: true,
				formatter: 'click',
				onclick : function(cellValue, options, rowObject){
					
					l140m01aMainId = rowObject.mainId;
					var isReadonly = $('#L140M01AForm1').find("#caseBox12").is(':visible') 
								? !$('#L140M01AForm1').find("#caseBox12").is(':checked') 
								: false;
					isReadonly = isLock || isReadonly;
					HighPricedHousingLoanCheckList.open($('#L140M01AForm1'), isReadonly, l140m01aMainId); 
				}
			},
			{
			    colHeader: '檢核',
			    name: 'check',
				align: 'center',
				width: 50
			},
			{
				name: 'oid',
                hidden: true
			},
			{
				name: 'mainId',
                hidden: true
			}]
		});
	},
	//Web e-Loan於簽報央行C方案件中增列總處單位引介資料
	initIntroduceAllBranch: function (isLock){
	    // J-112-0148 疫後振興不限單位
	    var noNeedSysParam = isResueItemCaseF();
		$.ajax({
	        type: "POST",
	        handler: inits.fhandle,
	        async: false ,
	        dataType : "json",
	        data: {
	            formAction: "allIntroduceBranch",
	            noNeedSysParam: noNeedSysParam
	        }
		}).done(function(responseData) {
			var json = {
			    format: "{value}-{key}",
			    item: responseData.brData
			};
			$("#_rescueIntroduceBrNo").setItems(json);
	    });
         
	},
	//Web e-Loan於簽報央行C方案件中增列總處單位引介資料
	changeRescueIntroduceUserList: function (objValue){
		 if(objValue){
    		$.ajax({
                type: "POST",
                handler: inits.fhandle,
                dataType : "json",
                data: {
                    formAction: "allIntroduceUserByBranch",
                    qryBranch: objValue
                }
			}).done(function(responseData) {
				var json = {
				    format: "{value}-{key}",
				    item: responseData 
				};

				$("#_rescueIntroduceUserId").setItems(json);
            });
    	}
	},
	showExceptFlagQ2Q7Plus: true,
	initExceptFlagQA: function(){
	        $.ajax({
	            handler: inits.fhandle,
	            action: "initExceptFlag",
	            data: {//把資料轉成json
	            	tabFormMainId: $("#tabFormMainId").val(),
	            	mainId: responseJSON.mainId
	            }
			}).done(function(obj) {
				var $boxContex = $("#L140M01AForm1");
				var exceptFlagQAisY = $boxContex.find("#exceptFlagQAisY").val();
				var exceptFlagQAPlus = $boxContex.find("#exceptFlagQAPlus").val();
				var exceptFlag = $boxContex.find("[name=exceptFlag]:radio:checked").val();
				CntrNoAPI.showExceptFlagQ2Q7Plus = true;
				if(obj.showExceptFlagQ2Q7Plus == "N"){
					CntrNoAPI.showExceptFlagQ2Q7Plus = false;
				}
				if(obj.showExceptFlagQA == "Y"){
					if( CntrNoAPI.showExceptFlagQ2Q7Plus ){// 法人寫額度明細
						$boxContex.find("#exceptFlagQ2Plus_note").show();
						$boxContex.find("#exceptFlagQ7Plus_note").show();
					}else{
						$boxContex.find("#exceptFlagQ2Plus_note").hide();
						$boxContex.find("#exceptFlagQ7Plus_note").hide();
					}
					if(exceptFlagQAisY != ""){
						for(var i = 1 ; i <= exceptFlagQAisY ; i++){
							var inputName = "exceptFlagQ"+i;
							$boxContex.find("#"+inputName+"Tr").show();
							$("input[name='"+ inputName +"YN'][value='N']").prop('checked',true);
							if(i == exceptFlagQAisY){
								$("input[name='"+ inputName +"YN'][value='Y']").prop('checked',true);
								//J-112-0566 企金簽報書「額度明細表」中約定融資註記部分針對Q2及Q7新增勾選項目
								if( CntrNoAPI.showExceptFlagQ2Q7Plus ){// 法人寫額度明細
									if( exceptFlagQAisY == "2" || exceptFlagQAisY == "7"){
										//第二或第七題勾是
										$boxContex.find("#"+inputName+"PlusTr").show();
										if( exceptFlagQAPlus == "Y"){
											$("input[name='"+ inputName +"PlusYN'][value='Y']").prop('checked',true);
										}
										if( exceptFlagQAPlus == "N"){
											$("input[name='"+ inputName +"PlusYN'][value='N']").prop('checked',true);
										}
									}else{
										$boxContex.find("#"+inputName+"PlusTr").hide();
									}
								}
							}
						}
					}
					CntrNoAPI.getExceptFlagStr(exceptFlag);
					$boxContex.find("#exceptFlagTable1").hide();
					$boxContex.find("#exceptFlagTable2").show();
				}else{
					$boxContex.find("#exceptFlagTable1").show();
					$boxContex.find("#exceptFlagTable2").hide();	
				}
	        });
	},
	initExceptFlagQAEvent : function(){
		var $boxContex = $("#L140M01AForm1");
		$boxContex.find("[name=exceptFlagQ1YN]:radio").click(function(){
			CntrNoAPI.resetExceptFlagQAPlusItem(["2","7"]);
			if ($boxContex.find("[name=exceptFlagQ1YN]:radio:checked").val() == "Y") {
				$("input[name='exceptFlag'][value='_']").prop('checked',true);//_:N.A.
				$("#exceptFlagQAisY").val("1");
				CntrNoAPI.getExceptFlagStr("_");
				CntrNoAPI.resetExceptFlagQAItem(["2","3","4","5","6","7","8"]);
			}else{
				$boxContex.find("#exceptFlagQ2Tr").show();
				CntrNoAPI.cleanExceptFlagDes();
			}
		});
		$boxContex.find("[name=exceptFlagQ2YN]:radio").click(function(){
			CntrNoAPI.resetExceptFlagQAPlusItem(["2","7"]);
			if ($boxContex.find("[name=exceptFlagQ2YN]:radio:checked").val() == "Y") {
				CntrNoAPI.resetExceptFlagQAItem(["3","4","5","6","7","8"]);
				// J-112-0566 企金簽報書「額度明細表」中約定融資註記部分針對Q2新增勾選項目
    			if(CntrNoAPI.showExceptFlagQ2Q7Plus){// 法人寫額度明細
    				CntrNoAPI.cleanExceptFlagDes();
    				$boxContex.find("#exceptFlagQ2PlusTr").show();
    			}else{
    				$("input[name='exceptFlag'][value='Y']").prop('checked',true);//Y:無條件可取消融資額度
    				CntrNoAPI.getExceptFlagStr("Y");
    				$boxContex.find("#exceptFlagQ2PlusTr").hide();
    			}
    			$("#exceptFlagQAisY").val("2");
			}else{
				$boxContex.find("#exceptFlagQ3Tr").show();
				CntrNoAPI.cleanExceptFlagDes();
				// J-112-0566 企金簽報書「額度明細表」中約定融資註記部分針對Q2新增勾選項目
				$boxContex.find("#exceptFlagQ2PlusTr").hide();
			}
		});
		$boxContex.find("[name=exceptFlagQ3YN]:radio").click(function(){
			CntrNoAPI.resetExceptFlagQAPlusItem(["2","7"]);
			if ($boxContex.find("[name=exceptFlagQ3YN]:radio:checked").val() == "Y") {
				CntrNoAPI.resetExceptFlagQAItem(["4","5","6","7","8"]);
				$("input[name='exceptFlag'][value='N']").prop('checked',true);//N:不可取消融資額度
				$("#exceptFlagQAisY").val("3");
				CntrNoAPI.getExceptFlagStr("N");
			}else{
				$boxContex.find("#exceptFlagQ4Tr").show();
				CntrNoAPI.cleanExceptFlagDes();
			}
		});
		$boxContex.find("[name=exceptFlagQ4YN]:radio").click(function(){
			CntrNoAPI.resetExceptFlagQAPlusItem(["2","7"]);
			if ($boxContex.find("[name=exceptFlagQ4YN]:radio:checked").val() == "Y") {
				CntrNoAPI.resetExceptFlagQAItem(["5","6","7","8"]);
				$("input[name='exceptFlag'][value='N']").prop('checked',true);//N:不可取消融資額度
				$("#exceptFlagQAisY").val("4");
				CntrNoAPI.getExceptFlagStr("N");
			}else{
				$boxContex.find("#exceptFlagQ5Tr").show();
				CntrNoAPI.cleanExceptFlagDes();
			}
		});
		$boxContex.find("[name=exceptFlagQ5YN]:radio").click(function(){
			CntrNoAPI.resetExceptFlagQAPlusItem(["2","7"]);
			if ($boxContex.find("[name=exceptFlagQ5YN]:radio:checked").val() == "Y") {
				CntrNoAPI.resetExceptFlagQAItem(["6","7","8"]);
				$("input[name='exceptFlag'][value='N']").prop('checked',true);//N:不可取消融資額度
				$("#exceptFlagQAisY").val("5");
				CntrNoAPI.getExceptFlagStr("N");
			}else{
				$boxContex.find("#exceptFlagQ6Tr").show();
				CntrNoAPI.cleanExceptFlagDes();
			}
		});
		$boxContex.find("[name=exceptFlagQ6YN]:radio").click(function(){
			CntrNoAPI.resetExceptFlagQAPlusItem(["2","7"]);
			if ($boxContex.find("[name=exceptFlagQ6YN]:radio:checked").val() == "Y") {
				CntrNoAPI.resetExceptFlagQAItem(["7","8"]);
				$("input[name='exceptFlag'][value='N']").prop('checked',true);//N:不可取消融資額度
				$("#exceptFlagQAisY").val("6");
				CntrNoAPI.getExceptFlagStr("N");
			}else{
				$boxContex.find("#exceptFlagQ7Tr").show();
				CntrNoAPI.cleanExceptFlagDes();
			}
		});
		$boxContex.find("[name=exceptFlagQ7YN]:radio").click(function(){
			CntrNoAPI.resetExceptFlagQAPlusItem(["2","7"]);
			if ($boxContex.find("[name=exceptFlagQ7YN]:radio:checked").val() == "Y") {
				CntrNoAPI.resetExceptFlagQAItem(["8"]);
				// J-112-0566 企金簽報書「額度明細表」中約定融資註記部分針對Q7新增勾選項目
				if(CntrNoAPI.showExceptFlagQ2Q7Plus){// 法人寫額度明細
    				CntrNoAPI.cleanExceptFlagDes();
    				$boxContex.find("#exceptFlagQ7PlusTr").show();
    			}else{
    				$("input[name='exceptFlag'][value='C']").prop('checked',true);//C:有條件可取消融資額度
    				CntrNoAPI.getExceptFlagStr("C");
    				$boxContex.find("#exceptFlagQ7PlusTr").hide();
    			}
    			$("#exceptFlagQAisY").val("7");
			}else{
				$boxContex.find("#exceptFlagQ8Tr").show();
				CntrNoAPI.cleanExceptFlagDes();
				// J-112-0566 企金簽報書「額度明細表」中約定融資註記部分針對Q7新增勾選項目
				$boxContex.find("#exceptFlagQ7PlusTr").hide();
			}
		});
		$boxContex.find("[name=exceptFlagQ8YN]:radio").click(function(){
			CntrNoAPI.resetExceptFlagQAPlusItem(["2","7"]);
			if ($boxContex.find("[name=exceptFlagQ8YN]:radio:checked").val() == "Y") {
				$("input[name='exceptFlag'][value='N']").prop('checked',true);//N:不可取消融資額度
				$("#exceptFlagQAisY").val("8");
				CntrNoAPI.getExceptFlagStr("N");
			}else{
				CntrNoAPI.cleanExceptFlagDes();
			}
		});
		// J-112-0566 企金簽報書「額度明細表」中約定融資註記部分針對Q2新增勾選項目
		$boxContex.find("[name=exceptFlagQ2PlusYN]:radio").click(function(){
			if ($boxContex.find("[name=exceptFlagQ2PlusYN]:radio:checked").val() == "Y") {
				//勾選無條件可取消融資額度且符合(a)(b)(c)要改下Z
				$("input[name='exceptFlag'][value='Z']").prop('checked',true);//Z:無條件可取消融資額度
				$("#exceptFlagQAPlus").val("Y");
				CntrNoAPI.getExceptFlagStr("Z");
			}else if($boxContex.find("[name=exceptFlagQ2PlusYN]:radio:checked").val() == "N"){
				$("input[name='exceptFlag'][value='Y']").prop('checked',true);//Y:無條件可取消融資額度
				$("#exceptFlagQAPlus").val("N");
				CntrNoAPI.getExceptFlagStr("Y");
			}
		});
		// J-112-0566 企金簽報書「額度明細表」中約定融資註記部分針對Q7新增勾選項目
		$boxContex.find("[name=exceptFlagQ7PlusYN]:radio").click(function(){
			if ($boxContex.find("[name=exceptFlagQ7PlusYN]:radio:checked").val() == "Y") {
				//勾選有條件可取消融資額度且符合(a)(b)(c)要改下D
				$("input[name='exceptFlag'][value='D']").prop('checked',true);//D:有條件可取消融資額度
				$("#exceptFlagQAPlus").val("Y");
				CntrNoAPI.getExceptFlagStr("D");
			}else if($boxContex.find("[name=exceptFlagQ7PlusYN]:radio:checked").val() == "N"){
				$("input[name='exceptFlag'][value='C']").prop('checked',true);//C:有條件可取消融資額度
				$("#exceptFlagQAPlus").val("N");
				CntrNoAPI.getExceptFlagStr("C");
			}
		});
	},
	
	resetExceptFlagQAItem : function(items){
		var $boxContex = $("#L140M01AForm1");
		for(var i = 0 ; i < items.length ; i++ ){
			var inputName = "exceptFlagQ"+items[i];
			$boxContex.find("#"+inputName+"Tr").hide();
			$("input[name='"+ inputName +"YN'][value='Y']").prop('checked',false);
			$("input[name='"+ inputName +"YN'][value='N']").prop('checked',false);
		}
	},
	
	resetExceptFlagQAPlusItem : function(items){//
		var $boxContex = $("#L140M01AForm1");
		for(var i = 0 ; i < items.length ; i++ ){
			var inputName = "exceptFlagQ"+items[i];
			$boxContex.find("#"+inputName+"PlusTr").hide();
			$("input[name='"+ inputName +"PlusYN'][value='Y']").prop('checked',false);
			$("input[name='"+ inputName +"PlusYN'][value='N']").prop('checked',false);
		}
		$("#exceptFlagQAPlus").val("");
	},
	
	cleanExceptFlagDes : function(){
		$("input[name='exceptFlag'][value='Y']").prop('checked',false);//Y:無條件可取消融資額度
		$("input[name='exceptFlag'][value='Z']").prop('checked',false);//Z:無條件可取消融資額度
		$("input[name='exceptFlag'][value='C']").prop('checked',false);//C:有條件可取消融資額度
		$("input[name='exceptFlag'][value='D']").prop('checked',false);//D:有條件可取消融資額度
		$("input[name='exceptFlag'][value='N']").prop('checked',false);//N:不可取消融資額度
		$("input[name='exceptFlag'][value='_']").prop('checked',false);//_:N.A.
		$("#exceptFlagQAisY").val("");
		var exceptFlagDes = $("#exceptFlagDes");
		exceptFlagDes.injectData({'exceptFlagDes':''},false);
	},
	
	getExceptFlagStr : function(exceptFlag){
		var exceptFlagDes = $("#exceptFlagDes");
		var exceptFlagStr = "";
		switch (exceptFlag) {
        case "Y":
            // L140M01a.exceptFlag_Y=無條件可取消融資額度
        	// L140M01a.exceptFlag_CCF_memo2=(CCF=10%)
        	exceptFlagStr = i18n.lms1401s02["L140M01a.exceptFlag_Y"]+i18n.lms1401s02["L140M01a.exceptFlag_Y_memo1"];
        	if(CntrNoAPI.showExceptFlagQ2Q7Plus && $("#exceptFlagQAPlus").val()!=""){
        		exceptFlagStr = exceptFlagStr +i18n.lms1401s02["L140M01a.exceptFlag_CCF_memo2"];
        	}
            break;
        case "Z":// J-112-0566 配合金管會修訂「銀行自有資本與風險性資產計算方法說明及表格修正重點」，新增代碼Z
            // L140M01a.exceptFlag_Z=無條件可取消融資額度
        	// L140M01a.exceptFlag_CCF_memo1=(CCF=0%)
        	exceptFlagStr = i18n.lms1401s02["L140M01a.exceptFlag_Z"]+i18n.lms1401s02["L140M01a.exceptFlag_Z_memo1"];
        	if(CntrNoAPI.showExceptFlagQ2Q7Plus && $("#exceptFlagQAPlus").val()!=""){
        		exceptFlagStr = exceptFlagStr +i18n.lms1401s02["L140M01a.exceptFlag_CCF_memo1"];
        	}
            break;
        case "C":
        	// L140M01a.exceptFlag_C=有條件可取消融資額度
        	// L140M01a.exceptFlag_CCF_memo2=(CCF=10%)
        	exceptFlagStr = i18n.lms1401s02["L140M01a.exceptFlag_C"]+i18n.lms1401s02["L140M01a.exceptFlag_C_memo1"];
        	if(CntrNoAPI.showExceptFlagQ2Q7Plus && $("#exceptFlagQAPlus").val()!=""){
        		exceptFlagStr = exceptFlagStr +i18n.lms1401s02["L140M01a.exceptFlag_CCF_memo2"];
        	}
        	break;
        case "D":// J-112-0566 配合金管會修訂「銀行自有資本與風險性資產計算方法說明及表格修正重點」，新增代碼D
        	// L140M01a.exceptFlag_D=有條件可取消融資額度
        	// L140M01a.exceptFlag_CCF_memo1=(CCF=0%)
        	exceptFlagStr = i18n.lms1401s02["L140M01a.exceptFlag_D"]+i18n.lms1401s02["L140M01a.exceptFlag_D_memo1"];
        	if(CntrNoAPI.showExceptFlagQ2Q7Plus && $("#exceptFlagQAPlus").val()!=""){
        		exceptFlagStr = exceptFlagStr +i18n.lms1401s02["L140M01a.exceptFlag_CCF_memo1"];
        	}
        	break;
        case "N":
            // L140M01a.exceptFlag_N=不可取消融資額度
        	exceptFlagStr = i18n.lms1401s02["L140M01a.exceptFlag_N"]+i18n.lms1401s02["L140M01a.exceptFlag_N_memo1"];
            break;
        case "_":
        	exceptFlagStr = "N.A.";
            break;
        default:
            break;
		}
		exceptFlagDes.injectData({'exceptFlagDes':exceptFlagStr},false);
	}
};

//J-112-0438_12473_B1001 引介來源相關
var IntroductionSource = {
		
		initEvent: function(){
			$("#introduceSrc").change(function(){
				IntroductionSource.change();
			});
			
			$("#importCustOrComButton").click(function(){
				IntroductionSource.importCustomerOrCompanyInfo();
			})
			
			$("#selectBranchLink").click(function(){
				IntroductionSource.selectBranch();
			});
			
			$("#L140M01AForm1").find("select#megaCode").change(function(){
				IntroductionSource.setSubUnitNo();
			});
			
		},
		
		show: function(introduceSrc){

			switch (introduceSrc) {
			    case '1':
			        $("#employeeDiv").show();
			        break;
			    case '3':
			        $("#megaSubCompanyDiv").show();
			        break;
			    case '5':
			    case '6':
			    case '9':
					$("#customerOrCompanyDiv").show();
			        break;
			}
		},
		
		change: function(){
			
			$("#employeeDiv").hide();
			$("#megaSubCompanyDiv").hide();
			$("#customerOrCompanyDiv").hide();
			
			//行員引介
			$("#employeeDiv").find("input:text").val("");
			//金控子公司員工引介
			$("#megaSubCompanyDiv").find("select").val("");
			$("#megaSubCompanyDiv").find("input:text").val("");
			//往來企金戶所屬員工, 本行客戶引介
			$("#customerOrCompanyDiv").find("input:text").val("");
			//引介人姓名
			$("#introducerNameDiv").find("input:text").val("");
			
			IntroductionSource.show($("#introduceSrc").val());
		},
		
		importCustomerOrCompanyInfo: function(){

			AddCustAction.open({
	    		handler: 'cls1151m01formhandler',
				action : 'importCustomerOrCompanyInfo',
				data : {
	            },
				callback : function(json){					
	            	// 關掉 AddCustAction 的 
	            	$.thickbox.close();					
					$("#introCustId").val(json.introCustId);
					$("#introDupNo").val(json.introDupNo);
					$("#introCustName").val(json.introCustName);
					var introduceSrc = $("#introduceSrc").val();
					if(introduceSrc == '5' || introduceSrc == 'A'){
						$("#introducerName").val(json.introCustName)
					}
				}
			});
		},
		
		selectBranch: function(){
			
			var form = $("#L140M01AForm1");
			$.ajax({
	            handler: "cls1220m10formhandler",
	            action: "getAllOrgBrId"
	        }).done(function(responseData){
				if(responseData.Success){ //成功
					form.find("#selectBranch").setItems({
				        item: responseData.childMap,
				        format: "{value} - {key}"
				    });
				}
				
				$("#selectBranchDiv").thickbox({
	  	       	 	title: i18n.lms1401s02['L140M01a.selectBranch'], width: 350, height: 100, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
	  	            buttons: {
	  	                "sure": function(){
	  	                	var branchStr = $("#selectBranch option:selected").text();
							var branchArray = branchStr.split("-");
							$("#megaEmpBrNo").val($.trim(branchArray[0]));
							$("#megaEmpBrNoName").val($.trim(branchArray[1]));
							$.thickbox.close();
	  	                },
	  	                "cancel": function(){
	  	               		$.thickbox.close();
	  	                }
	  	            }
	  	        });
			});
		},
		
		setSubUnitNo: function(subUnitNo){
			var val_mega_code = $("#L140M01AForm1").find("select#megaCode").val();
			if(val_mega_code==''){
				$("#L140M01AForm1").find("select#subUnitNo").setItems({});
				$("#L140M01AForm1").find("#subEmpNo").val("");
				$("#L140M01AForm1").find("#subEmpNm").val("");
			}else{
	    		var key_sub_unitNo = ('LNF13E_SUB_UNITNO_' + val_mega_code);
	    		var item = CommonAPI.loadCombos(key_sub_unitNo);
	    		$("#L140M01AForm1").find("select#subUnitNo").setItems({ item: item[key_sub_unitNo] , format: "{value} - {key}" });
	    		if(subUnitNo != ''){
	    			$("#L140M01AForm1").find("select#subUnitNo").val(subUnitNo);
	    		}
			}
		}
	};

	
pageJsInit(function() {
	$(function() {
initDfd.done(function(auth){
	dfd1 = dfd1 || $.Deferred(); 
	dfd2 = dfd2 || $.Deferred();
	dfd3 = dfd3 || $.Deferred();
	dfd4 = dfd4 || $.Deferred();
	beforeCheckGridDef = beforeCheckGridDef || $.Deferred();
	
    dfd1.done(gridviewborrow, gridviewtrans);//新增額度明細表
    dfd2.done(gridviewC);//主表額度明細表
    dfd3.done(gridviewcopyvalueNow);//複製額度明細表grid
    dfd4.done(gridviewprint);//印表grid
    beforeCheckGridDef.done(beforeCheckGridview)//擬核准顯示的grid
    //====額度明細表和批覆表異動====
    //不加空白會變成數字
    switch (responseJSON.page + "") {
        case "02": //聯行額度明細表  
            inits.action = "queryL140m01aByL141m01b";
            dfd2.resolve();
            break;
        case "03"://額度明細表
            var ownbrId = userInfo ? userInfo.unitNo : "";
            var unitType = userInfo ? userInfo.unitType : "";
            if ("4" == unitType || "2" == unitType) {
                $("#lms1405s02PanelBt").hide();
            }
            if (responseJSON.docKind == "1") {
                switch (responseJSON.authLvl + "") {
                    case "1":
                        //授權內要顯示擬核准按鈕
                        $("#beforeCheckSpan").show();
                        break;
                    case "2":
                        //TODO 總行授權內還要檢查目前登錄的是不是總行
                        //授權內要顯示擬核准按鈕
                        $("#beforeCheckSpan").show();
                        break;
                    case "3":
                        if (lmsM01Json.isSpectialBank()) {
                            $("#beforeCheckSpan").show();
                        }
                        else {
                            $("#beforeCheckSpan").hide();
                        }
                        break;
                    default:
                        //授權內要顯示擬核准按鈕
                        $("#beforeCheckSpan").hide();
                        break;
                }
            }
            else {
                // 授權外 特殊分行(國外部、財富管理處、國金部、財務部、金控總部分行)
                // 無條件顯示擬核准按鈕
                var unitNo = userInfo.unitNo;
                if (unitNo == "007" || unitNo == "009" || unitNo == "025" ||
                unitNo == "011" ||
                unitNo == "201" ||
                unitNo == "940" ||
                unitNo == "943" ||
                unitNo == "149") {
                	//(108)第 3230 號
                	//初審與初審審核，國外部都沒有擬核定的按鈕
                	if(responseJSON.areaChk =="5" || responseJSON.areaChk =="6"){
                		$("#beforeCheckSpan").hide();
                	}else{
                		$("#beforeCheckSpan").show();
                	}
                }
                else {
                    $("#beforeCheckSpan").hide();
                }
            }
            break;
        case "11"://額度批覆表，只有出現在授管處 -審核中 和泰國總行 -提會待登錄 
            var docstatus = responseJSON.mainDocStatus;
            var auth = (responseJSON ? responseJSON.Auth : {}); //權限
            var unitType = userInfo ? userInfo.unitType : "";
            var unitNo = userInfo.unitNo;
            
            //J-108-0316_05097_B1001 Web e-Loan修改總處營	業單位授權外簽報流程
            CntrNoAPI.reloadRandomCodeSbr();
             
            
            //(108)第 3230 號
            //當文件在授管處-審查中或泰國總行提會待登錄 ，且為經辦並無人開啟文件 顯示批覆按鈕
            if (("L1H" == docstatus || "L1C" == docstatus || "L3G" == docstatus) && auth.Modify && _openerLockDoc != "1" && (unitType == "4" || unitType == "2")) {
                $("#lms1405s02PanelBt").show();
            }else if (("01K" == docstatus || "02K" == docstatus || "03K" == docstatus || "05K" == docstatus || "06K" == docstatus || "07K" == docstatus) && auth.Modify && _openerLockDoc != "1" && (unitType == "5") && 
            		(unitNo == "007" || unitNo == "009" || unitNo == "025" ||
                    unitNo == "011" ||
                    unitNo == "201" ||
                    unitNo == "940" ||
                    unitNo == "943" ||
                    unitNo == "149")  && (responseJSON.areaChk =="5" || responseJSON.areaChk =="6")) {
            	//(108)第 3230 號
                $("#lms1405s02PanelBt").show();
                //國外部沒有批覆書批覆功能
                $("#lms1405s02PanelBt").find("#checkBt").hide();
                //$("#beforeCheckSpan").hide();
            }
        
            //(108)第 3230 號 
            if (lmsM01Json.isArea()) {
            	
            	//登入單位是營運中心
                if (responseJSON.caseBrId != undefined && responseJSON.caseBrId != null && responseJSON.caseBrId != "") {
                    var unitNo = responseJSON.caseBrId.toString().substring(0, 3);
                    if (unitNo == "007" || unitNo == "009" || unitNo == "025" ||
                    unitNo == "011" ||
                    unitNo == "201" ||
                    unitNo == "940" ||
                    unitNo == "943" ||
                    unitNo == "149") {
                        var docstatus = responseJSON.mainDocStatus;
                        var auth = (responseJSON ? responseJSON.Auth : {}); //權限
                        if (lmsM01Json.docCode == "4" || lmsM01Json.docCode == "3") {
                            $("#lms1405s02PanelBt").hide();
                        }
                        else 
                            if (("L1C" == docstatus) && auth.Modify && _openerLockDoc != "1") {
                            }
                            else {
                                $("#lms1405s02PanelBt").hide();
                            }
                    }
                }
            }

            inits.itemType = "2";
            
            
            //額度批覆表  
            dfd2.resolve();
            break;
    }
    //===================change event Code======================
    
    
    $("#lms140s02").click(function(){
        dfd2.resolve();
    });
    
    //當案件在審查中預設到額度明細表  
    if (Constants.docStatus['L1H'] == responseJSON.mainDocStatus && responseJSON.page == "03" && responseJSON.docCode != "3") {
        $("#lms140s02").trigger('click');
    }
    
    /**
     登錄分行代號
     */
    $("#selectCopyBranchBt").click(function(){
        CommonAPI.showAllBranch({
            btnAction: function(a, b){
                $("#selectFilterBrno").val(b.brNo);
                $.thickbox.close();
            }
        });
    });

    //===================change event End======================
    
    //===================button event ======================
    $("#openNewList").click(function(){
        $("[name=printButtonType][value=1]").prop("checked", true);
        $.ajax({
            handler: inits.fhandle,
            action: "queryL120s01a",
            data: {}
		}).done(function(obj){
			if ($.isEmptyObject(obj.item)) {
			    //L140M01a.error01 = 請先登錄借款人資料
			    return CommonAPI.showMessage(i18n.lms1401s02['L140M01a.error01']);
			}
			$("#nowSelectPerson,#theCopyPerson").setItems({//塞複製的select
			    item: obj.item,
			    format: "{value} {key}"
			});

			//新增額度明細表grid
			dfd1.resolve();
			/**  新增額度明細表  */
			CntrNoAPI.creatCntrDoc();
        }); //close ajax
    });
    
    /** 設定列印順序  */
    $("#printviewBt").click(function(){
    	
        if (!CntrNoAPI.isCheckGrid()) {
            return false;
        };
        dfd4.resolve();
        $("#gridviewprint").trigger("reloadGrid");
        $("#printview").thickbox({
            //title.08=額度明細表列印順序設定
            title: i18n.lms1401s02['title.08'],
            width: 900,
            height: 500,
            modal: true,
            i18n: i18n.lms1401s02,
            buttons: API.createJSON([{
                key: i18n.lms1401s02['btn.writeCase'],
                value: function(){
                    var $gridviewprint = $("#gridviewprint");
                    //寫回額度明細表
                    $gridviewprint.jqGrid('saveRow', lastSel, false, 'clientArray');
                    var ids = $gridviewprint.jqGrid('getDataIDs');
                    //用來放列印順序跟oid
                    var json = {};
                    var checkArray = $gridviewprint.getCol("printSeq");
                    
                    //檢查列印順序值是否重複
                    if (checkArrayRepeat(checkArray)) {
                        //L140M01a.error24=列印順序不可重複
                        return CommonAPI.showMessage(i18n.lms1401s02['L140M01a.error24']);
                    }
                    
                    for (var id in ids) {
                        var data = $gridviewprint.jqGrid('getRowData', ids[id]);
                        json[data.oid] = data.printSeq;
                    }
                    FormAction.open = true;
                    $.ajax({
                        handler: inits.fhandle,
                        action: "savePrintSeq",
                        data: {
                            mainId: $("#mainId").val(),
                            noOpenDoc: true,
                            caseType: inits.itemType,
                            data: JSON.stringify(json)
                        }
					}).done(function(obj){
						FormAction.open = false;
						$.thickbox.close();
						CntrNoAPI._triggerMainGrid(true);
                    });
                }
            }, {
                key: i18n.def['close'],
                value: function(){
                    $.thickbox.close();
                }
            }])
        });
    });
    
    /**  
     * J-106-0113-001 Web e-Loan國內企金針對銀行法第72-2條之授信戶(含新、舊案)，於E-LOAN授信管理系統額度明細表中，加設警語，以達控管之效。
     *計算合計  
     */
    $("#countValue").click(function(){
		$.ajax({
	        handler: inits.fhandle,
	        action: "showCntrDocWarnMessage",
	        data: {
	            itemType: inits.itemType,
				tabFormMainId: $("#tabFormMainId").val(),
	            mainId: responseJSON.mainId,
				noOpenDoc: true
	        }
		 }).done(function(warnObj){
			// J-111-0461 建議核定層級，可以合併全案計算or不計算
			if(warnObj.showSuggestModeItem){
				responseJSON.showSuggestModeItem = encodeURI(warnObj.showSuggestModeItem);
			}else{
				responseJSON.showSuggestModeItem = '';
			}

			if(warnObj.showMessage !=  ""){
				//銀行法第72-2條授信戶，不得適用本行新臺幣競爭性利率及新臺幣七百億優惠利率。
				//本案借款人加計關係戶往來彙總表集團(ID如下)有授信往來已達三戶以上，集團代號不得為9或A開頭(非徵信處控管之集團)，請洽徵信處申請新集團代號。<br> {0}
				//提醒！額度明細表之借款人最近一次簽報書尚有下列其他分行已核准額度序號未列於本案，請再確認是否需補齊。
				CommonAPI.confirmMessage(warnObj.showMessage, function(b){
					if (b) {
						countValue();
					}
				});
			}else{
				countValue();
			}        	

			//J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			//如果為微型企業簽報書，則自動將微型企業頁籤之引進J10
			if(responseJSON.miniFlag == "Y"){
				$.ajax({
					async: false,
					handler : "microentformhandler",
					type : "POST",
					data : {
						formAction : "importList",
						mainId : responseJSON.mainid
					}
				}).done(function(json){
					//J-109-0459_05097_B1001 Web e-Loan簡化微型企業簽報書資僅為動用新台幣案件時得免執行制裁/管制名單掃描。
					if( json.isLmsCaseReportCanPassAml=="Y"){
						$("#book20").hide();
					}else{
						$("#book20").show();
					}
				});		
			}

			// J-111-0397 RWA 計算授信額度時，自動引進RWA
			if (responseJSON.page == "03") {
			    $.ajax({
			        async: false,
			        handler : "lms1401s09formhandler",
			        type : "POST",
			        data : {
			            formAction : "importRwaList",
			            mainId : responseJSON.mainid
			        }
				}).done(function(json){
					if(json.needImportRwa && json.needImportRwa == "Y"){
					    $("#gridviewRWA").trigger("reloadGrid");
					    $("#gridviewRORWA").trigger("reloadGrid");
					    showRorwaForm();
					    showTot();
					}
			    });
			}
		 });		
    });
	
	
    
    //重新引進 客戶名稱
    $("#getCustNewName").click(function(){
        if (!CntrNoAPI.isCheckGrid()) {
            return false;
        };
        $.ajax({
            handler: inits.fhandle,
            action: "getNewCustName",
            data: {
                noOpenDoc: true,
                mainId: $("#mainId").val()
            }
		}).done(function(obj){
			CntrNoAPI._triggerMainGrid(true);
        });
    });
	
	
	//整批引進最新資料
    $("#btnEntireApply").click(function(){
        if (!CntrNoAPI.isCheckGrid()) {
            return false;
        };

        //海外:lms1405s02_EntireApplyNew
		//國內:lms1401s02_EntireApplyNew
        //var item = API.loadOrderCombosAsList("lms1401s02_EntireApplyNew")["lms1401s02_EntireApplyNew"];
		var item = API.loadCombos("lms1401s02_EntireApplyNew")["lms1401s02_EntireApplyNew"];
		$("#entireApply").setItems({
			size: "1",
            item: item,
			clear : true,
			itemType: 'checkbox' 
        })
 
        $("[name=entireApply]").removeAttr("disabled").removeAttr("checked");
 
        var $gridviewC_2 = $(CntrNoAPI.mainGridId);
        var ids = $gridviewC_2.getGridParam('selarrrow');
        var oids = [];
        if (ids == "") {
            //action_005=請先選取一筆以上之資料列
            return CommonAPI.showErrorMessage(i18n.def['action_005']);
        }
		
		$("#choiceEntireApply").thickbox({
		        //L140M01a.select=選取關鍵字
		        title: i18n.lms1401s02["L140M01a.select"],
		        width: 600,
		        height: 200,
		        modal: true,
		        align: "center",
		        valign: "bottom",
		        readOnly: false,
		        i18n: i18n.def,
		        buttons: {
		            "sure": function(){
		                $.thickbox.close();
		                 var allCheackedVal = [];
		                $.each($("#choiceEntireApply :checkbox[name=entireApply]:checked"), function(i, n){
		                    allCheackedVal[i] = $(n).val();
		                });
		                 
				        for (var i in ids) {
				            oids.push($gridviewC_2.getRowData(ids[i]).oid);
				        } 
						 
						$.ajax({
				            async: false,
				            handler: "lms1401m01formhandler",
				            data: {
				                formAction: "entireApplyNew",
				                allCheackedVal: allCheackedVal.join("|"),
								oids: oids
				            }
						}).done(function(obj){
							//runSuccess=執行成功
							$gridviewC_2.trigger("reloadGrid");
							CommonAPI.showMessage(i18n.def["runSuccess"]);
				        });
		            },
		            "cancel": function(){
		                $.thickbox.close();
		            }
		        }
		 });
    });
 
	
	
    //刪除全部
    $("#removeAll").click(function(){
        if (!CntrNoAPI.isCheckGrid()) {
            return false;
        };
        var $gridviewC_2 = $(CntrNoAPI.mainGridId);
        var ids = $gridviewC_2.getGridParam('selarrrow');
        var oids = [];
        if (ids == "") {
            //action_005=請先選取一筆以上之資料列
            return CommonAPI.showErrorMessage(i18n.def['action_005']);
        }
        for (var i in ids) {
            oids.push($gridviewC_2.getRowData(ids[i]).oid);
        }
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: inits.fhandle,
                    action: "removeAll",
                    data: {
                        noOpenDoc: true,
                        mainId: $("#mainId").val(),
                        caseType: inits.itemType,
                        oids: oids
                    }
				}).done(function(obj){
					CntrNoAPI.reloadRandomCodeSbr();
					$gridviewC_2.trigger("reloadGrid");
                });
            }
        });
        
    });
    
    
    /**  產生批覆表  */
    $("#newCheckPageBt").click(function(){
        $.ajax({
            handler: inits.fhandle,
            action: "produceCase",
            data: {}
		}).done(function(obj){
			CntrNoAPI._triggerMainGrid(true);
        });
    });
    
    /** 批覆和擬批覆  */
    $("#checkBt").click(function(){
        toDoCheck();
    });
    
    
    //J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
    /** 查詢LGD明細  */
    $("#printLgdDetail").click(function(){
    	CntrNoAPI.print_Lgd();
    });
    
    //J-112-0426_05097_B1001 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。
    $("input[name='checkBtRadio']").change(function(){
    	 var value = $("input[name=checkBtRadio]:checked").val();
    	 if(value=="1" || value=="2" || value=="4" ){
    		 $('#showCheckBtEsg').show();
			 var obj = CommonAPI.loadCombos(["lms_checkBtEsg_"+value]);
             //涉及ESG風險授信案件之審查註記
             $("#checkBtEsg").setItems({
            	 space: true,
                 item: obj["lms_checkBtEsg_"+value],
                 format: "{value} - {key}"
             });
    	 }else{
    		 $('#showCheckBtEsg').hide();
    	 }
    });
    
    //===================button event End======================
    
    //===================Grid Code=============================
    /**  主表grid  */
    function gridviewC(){
        $(CntrNoAPI.mainGridId).iGrid({
			needPager: false,
            handler: inits.ghandle,
            height: 300,
            rownumbers: true,
            sortname: 'printSeq|custId|cntrNo',
            sortorder: 'asc|asc|asc',
            multiselect: true,
            //rowNum: 10,
            postData: {
                formAction: inits.action,
                itemType: inits.itemType
            },
            colModel: [{
                colHeader: i18n.lms1401s02["L140M01a.custName"],//借款人名稱
                width: 140,
                name: 'custName',
                sortable: true,
                formatter: 'click',
                onclick: CntrNoAPI.opendocBox
            }, {
                colHeader: i18n.lms1401s02["L140M01a.cntrNo"],//"額度序號",
                name: 'cntrNo',
                width: 80,
                sortable: true
            }, {
                colHeader: i18n.lms1401s02["L140M01a.cntrNoCom"],//"共用額度序號",
                name: 'commSno',
                width: 80,
                sortable: true
            }, {
                colHeader: "&nbsp;",
                name: 'currentApplyCurr',
                width: 25,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.lms1401s02["L140M01a.moneyAmt"],//現請額度,
                name: 'currentApplyAmt',
                width: 100,
                sortable: true,
                align: "right",
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
					removeTrailingZero: true,
                    decimalPlaces: 2//小數點到第幾位
                }
            }, {
                colHeader: i18n.lms1401s02["L140M01a.type"],//"性質"
                name: 'proPerty',
                width: 70,
                sortable: true,
                align: "center",
                formatter: proPertyFormatter
            }, {
            	//J-112-0426_05097_B1001 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。
                colHeader: i18n.lms1401s02["L140M01a.docStatus"]+"<br>(ESG)", //"文件狀態",
                name: 'docStatus',
                width: 60,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.lms1401s02["L140M01a.branchId"],//"分行別",
                name: 'ownBrId',
                width: 80,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.lms1401s02["L140M01a.dataDrc"],//L140M01a.dataDrc=來源
                name: 'dataSrc',
                width: 50,
                sortable: true,
                align: "center"
            }, {
                colHeader: "&nbsp",//"檢核欄位",
                name: 'chkYN',
                width: 20,
                sortable: true,
                align: "center"
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'printSeq',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $(CntrNoAPI.mainGridId).getRowData(rowid);
                CntrNoAPI.opendocBox(null, null, data);
            }
        });
    }
    
    /**  擬核准用gird */
    function beforeCheckGridview(){
        $("#beforeCheckGridview").iGrid({
			needPager: false,
            handler: inits.ghandle,
            height: 200,
            rownumbers: true,
            sortname: 'printSeq|custId|cntrNo',
            sortorder: 'asc|asc|asc',
            multiselect: true,
            postData: {
                formAction: inits.action,
                itemType: inits.itemType
            },
            colModel: [{
                colHeader: i18n.lms1401s02["L140M01a.custName"],//借款人名稱
                width: 140,
                name: 'custName',
                sortable: true
            
            }, {
                colHeader: i18n.lms1401s02["L140M01a.cntrNo"],//"額度序號",
                name: 'cntrNo',
                width: 80,
                sortable: true
            }, {
                colHeader: i18n.lms1401s02["L140M01a.cntrNoCom"],//"共用額度序號",
                name: 'commSno',
                width: 80,
                sortable: true
            }, {
                colHeader: "&nbsp;",
                name: 'currentApplyCurr',
                width: 25,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.lms1401s02["L140M01a.moneyAmt"],//現請額度,
                name: 'currentApplyAmt',
                width: 100,
                sortable: true,
                align: "right",
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
					removeTrailingZero: true,
                    decimalPlaces: 2//小數點到第幾位
                }
            }, {
                colHeader: i18n.lms1401s02["L140M01a.type"],//"性質"
                name: 'proPerty',
                width: 70,
                sortable: true,
                align: "center",
                formatter: proPertyFormatter
            }, {
                colHeader: i18n.lms1401s02["L140M01a.docStatus"], //"文件狀態",
                name: 'docStatus',
                width: 60,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.lms1401s02["L140M01a.branchId"],//"分行別",
                name: 'ownBrId',
                width: 80,
                sortable: true,
                align: "center"
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'printSeq',
                hidden: true
            }, {
                name: 'dataSrc',
                hidden: true
            }]
        });
    }
    
    
    
    /**  設定列印順序grid  */
    var lastSel;
    function gridviewprint(){
        $("#gridviewprint").iGrid({
			needPager: false,
            handler: 'lms1405gridhandler',
            postData: {
                formAction: "queryL140m01a",
                mainId: $("#mainId").val(),
                itemType: inits.itemType
            },
            height: 230,
            cellsubmit: 'clientArray',
            autowidth: true,
            sortname: 'printSeq|custId|cntrNo',
            sortorder: 'asc|asc|asc',
            colModel: [{
                colHeader: i18n.lms1401s02["L140M01a.custName"],//"借款人名稱",
                name: 'custName',
                align: "left",
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.lms1401s02["L140M01a.cntrNo"],//"額度序號",
                name: 'cntrNo',
                align: "left",
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.lms1401s02["L782M01A.loanTP"],//"科目",
                name: 'lnSubject',
                align: "left",
                width: 200,
                sortable: true
            }, {
                colHeader: i18n.lms1401s02["L140M01a.type"],//"性質",
                name: 'proPerty',
                align: "center",
                width: 60,
                sortable: true,
                formatter: proPertyFormatter
            }, {
                colHeader: i18n.lms1401s02["L140M01a.printSeq"],//"列印順序",
                name: 'printSeq',
                align: "center",
                editable: true,
                width: 60,
                sortable: true,
                editrules: {
                    number: true
                },
                formatter: printSeqformatter
            }, {
                name: 'oid',
                hidden: true
            }],
            onSelectRow: function(id){
                if (id && id != lastSel) {
                    $("#gridviewprint").saveRow(lastSel, false, 'clientArray');
                    $('#gridviewprint').restoreRow(lastSel);
                    lastSel = id;
                }
                $('#gridviewprint').editRow(id, false);
            }
        });
    }
    
    /**  列印格式化  */
    function printSeqformatter(cellvalue, otions, rowObject){
        var seq = 0;
        if (!cellvalue) {
            cellvalue = ++seq;
        }
        seq = 0;
        return cellvalue;
    }
    
    /**  複製額度明細表grid  */
    function gridviewcopyvalueNow(){
        $("#gridviewcopyvalueNow").iGrid({
            handler: 'lms1405gridhandler',
            autowidth: true,
            postData: {
                formAction: "copyNow"
            },
            multiselect: true,
            sortname: 'caseDate|caseNo|cntrNo',
            sortorder: 'desc|desc|desc',
            hideMultiselect: false,
            rowNum: 10,
            colModel: [{
                colHeader: i18n.lms1401s02["L140M01a.caseDate"],//"簽案日期",
                name: 'caseDate',
                align: "center",
                width: 80,
                sortable: true
            }, {
                colHeader: i18n.lms1401s02["L140M01a.caseNum"],//"案號",
                name: 'caseNo',
                align: "content",
                width: 200,
                sortable: true
            }, {
                colHeader: i18n.lms1401s02["L140M01a.cntrNo"],//"額度序號",
                name: 'cntrNo',
                align: "center",
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.lms1401s02["L140M01a.type"],//"性質",
                name: 'proPerty',
                align: "center",
                width: 50,
                sortable: true,
                formatter: proPertyFormatter
            }, {
                colHeader: i18n.lms1401s02["L140M01a.docStatus"],// "文件狀態",
                name: 'docStatus',
                align: "center",
                width: 70,
                sortable: true
            }, {
                colHeader: i18n.lms1401s02["L140M01a.branchId"],//"分行別",
                name: 'ownBrId',
                align: "center",
                width: 100,
                sortable: true
            }, {
                name: 'oid',
                hidden: true
            }]
        });
    }
    
    /**  借款人資料Grid */
    function gridviewborrow(){
        $("#gridviewborrow").iGrid({
            handler: 'lms1201gridhandler',
            height: 230,
            postData: {
                formAction: "queryL120s01aOrigin",
                mainId: $("#mainId").val()
            },
            sortname: 'custId|dupNo',
            sortorder: 'desc|desc',
            rowNum: 10,
            rownumbers: true,
            colModel: [{
                colHeader: i18n.lms1401s02["L140M01a.custId"],//"統一編號",
                name: "custId",
                align: "center",
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.lms1401s02["L140M01a.custName"],//"借款人名稱",
                name: "custName",
                align: "center",
                width: 200,
                sortable: true
            }, {
                name: 'dupNo',
                hidden: true
            }, {
                name: 'typCd',
                hidden: true
            }, {
                name: 'oid',
                hidden: true
            }]
        });
    }
    
    //查詢聯行Grid
    function gridviewtrans(){
        $("#gridviewtrans").iGrid({
            handler: inits.ghandle,
            rownumbers: true,
            height: 235,
            rowNum: 10,
            sortname: 'caseBrId|custName|caseNo',
            sortorder: 'desc|desc|desc',
            postData: {
                formAction: "queryL141m01a"
            },
            colModel: [{
                colHeader: i18n.lms1401s02["L140M01a.together"],//"聯行",
                name: 'caseBrId',
                align: "center",
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.lms1401s02["L140M01a.mainPerson"],//"主要借款人",
                name: 'custName',
                align: "center",
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.lms1401s02["L140M01a.caseNum"],//"案號",
                name: 'caseNo',
                align: "center",
                width: 200,
                sortable: true
            }, {
                colHeader: i18n.lms1401s02["L140M01a.togetherDate"],// "聯行簽案日期",
                name: 'caseDate',
                align: "center",
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.lms1401s02["L140M01a.togetherManger"],//"聯行經辦",
                name: 'coAppraiser',
                align: "center",
                width: 100,
                sortable: true
            }, {
                name: 'srcMainId',
                hidden: true
            }]
        });
    }
    
    
    //=======================Grid Code end============================	
    
    //=======================thickbox Code============================
    /**
     *開啟額度明細表授信合計調整
     * @param {Object} size 筆數
     * @param {Object} curr 主要計價幣別
     * @param {Object} showCurr 是否顯示多幣別
     */
    function countEditBox(size, curr, showCurr){
    	$("#countEditBox").thickbox({
            //title.18=額度明細表授信合計調整
            title: i18n.lms1401s02['title.18'],
            width: 1000,
            height: 600,
            modal: true,
            i18n: i18n.def,
            align: "center",
            readOnly: false,
            valign: "bottom",
            buttons: {
                "sure": function(){
                    if ($("#countEditForm").valid()) {
                        FormAction.open = true;
                        $.ajax({
                            handler: inits.fhandle,
                            action: "saveCountEditForm",
                            data: {
                                noOpenDoc: true,
                                mainId: $("#mainId").val(),
                                caseType: inits.itemType,
                                countEditForm: JSON.stringify($("#countEditForm").serializeData()),
                                //調整筆數
                                size: size
                            }
						}).done(function(obj){
							//alert("countEditBox");
							//G-104-0097-001 Web e-Loan 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。
							//重新計算信用風險遵循
							$.ajax({
							     handler: inits.fhandle,
							     action:  "reCaculateL120S01M",
							     data: {
							         noOpenDoc: true,
							         mainId: $("#mainId").val(),
							         CaseType: inits.itemType
							     }
							}).done(function(obj){
								if(obj.errMsg != ""){
									FormAction.open = false;
								     CntrNoAPI._triggerMainGrid(true);
								    $.thickbox.close(); 
									CommonAPI.showMessage(obj.errMsg);
								     return false;
								}
								FormAction.open = false;
								 CntrNoAPI._triggerMainGrid(true);
								$.thickbox.close(); 
								CommonAPI.showMessage(i18n.def['runSuccess']);
							});	

							if(obj.highestCaseLvlSpan){
								// J-111-0461 簽報書上呈現最終最高審核層級
								$(".highestCaseLvlSpan").html(DOMPurify.sanitize(obj.highestCaseLvlSpan));
							}else{
								$(".highestCaseLvlSpan").html("");
							}
							// 疑似逾越授權紅字+公式
							if("Y" == obj.overAuthVersion2024){
								// 是啟用新版逾越授權的情況下這邊才要做事，不然都不會在這裡做事
								$("#overAuthMessageOnTitle").html(DOMPurify.sanitize(obj.overAuthMessageOnTitle));
								$("#overAuthDetailSpan").html(DOMPurify.sanitize(obj.overAuthDetailSpan));
							}	
                        });
                    }
                },
                "cancel": function(){
                    //直接計算不做修改
                    justOnlyCount(curr, showCurr);
                    $.thickbox.close();
                }
            }
        });
    }
    /**
     * 選擇計算幣別
     
     */
    function selecCountMoney(){
		
		$("#nowCurrRate").html("");
		$("#showSaveRateFg").hide();
		$("[name=saveRateFg]" ).removeAttr ("checked");
			 
		$("#countEditBoxCurrSelect").change(function(){
             var countEditBoxCurrSelect = $("#countEditBoxCurrSelect").val();
			 $("#nowCurrRate").html("");
			 $("#showSaveRateFg").hide();
			 $("[name=saveRateFg]" ).removeAttr ("checked");
			 
			 if(countEditBoxCurrSelect != "" && countEditBoxCurrSelect != undefined){
			 	$.ajax({
                    handler: inits.fhandle,
					async: false ,
                    action: "getCurrNowMoneyRate",
                    data: {
                        mainId: $("#mainId").val(),
                        itemType: inits.itemType,
						mainCurr: countEditBoxCurrSelect  
                    }
				}).done(function(obj){
					$("#nowCurrRate").html(obj.nowCurrRate);
					if(obj.nowCurrRate != "" && obj.nowCurrRate != undefined ){
						$("#showSaveRateFg").show();
						$("[name=saveRateFg]" ).removeAttr ("checked");
					}else{
						$("#showSaveRateFg").hide();
						$("[name=saveRateFg]" ).removeAttr ("checked");
					}
                });
			 }
        });
		
		$("#countEditBoxCurrSelect").change();
		 
        $("#countEditBoxCurr").thickbox({
            //title.19=請選擇計算幣別
            title: i18n.lms1401s02['title.19'],
            width: 500,
            height: 270,
            modal: true,
            i18n: i18n.def,
            readOnly: false,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var curr = $("#countEditBoxCurrSelect").val();
                    if (curr == "") {
                        //title.19=請選擇計算幣別
                        return CommonAPI.confirmMessage(i18n.lms1401s02['title.19']);
                    }
                    $.thickbox.close();
                    //L140M01a.error22=同一份額度明細表有二種以上幣別者, 是否要增列按各幣別合計顯示？
                    CommonAPI.confirmMessage(i18n.lms1401s02['L140M01a.error22'], function(b){
                        if (b) {
                            editCount(curr, true);
                        }
                        else {
                            editCount(curr, false);
                        }
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    /** 登錄婉卻原因
     *
     * @param {Array} oids 所選擇的額度明細表oid
     * @param {String } checked 執行動作
     */
    function rejectCauseBox(oids, checked){
        //初始化
        $("#cesRjtReasonEnt").val("");
        
        $("#rejectCauseBox").thickbox({
            //title.21=登錄婉卻原因
            title: i18n.lms1401s02['title.21'],
            width: 600,
            height: 340,
            align: "center",
            valign: "bottom",
            modal: true,
            readOnly: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#rejectCauseForm").valid()) {
                        return false;
                    }
                    
                    //J-107-0283_05097_B1001 修改web e-loan授信管理系統登錄點選婉卻原因為第23項:「其他」欄位時強制要求分行須登錄婉卻註記原因。
                    if($('#cesRjtCauseEnt').val()=="23"){
                    	if($.trim($('#cesRjtReasonEnt').val())==""){
                    		return CommonAPI.showErrorMessage(i18n.lms1401s02['L140M01a.cesRjtReason']+i18n.def['val.required']);
                    	}
                    }
                    
                    //J-112-0426_05097_B1001 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。
                    var checkBtEsg = $('#checkBtEsg').val();
                    
                    $.thickbox.close();
                    $.ajax({
                        handler: inits.fhandle,
                        action: "checkAction",
                        data: {
                            oids: oids,
                            doAction: checked,
                            itemType: inits.itemType,
                            Cause: $("#cesRjtCauseEnt").val(),
                            Reason: $("#cesRjtReasonEnt").val(),
                            //J-112-0426_05097_B1001 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。
                            checkBtEsg : checkBtEsg    
                        }
					}).done(function(obj){
						CntrNoAPI._triggerMainGrid(true);
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    //==========================thickbox Code end============================
    


    /** 
     * 打開修改視窗
     * @param {String} curr 多幣別計算時計算結果的幣別
     * @param {String} showCurr 是否顯示多幣別描述
     */
    function editCount(curr, showCurr){
		
    	// 先預設成mode 1
    	CntrNoAPI.changeSuggestMode(1);
    	var suggestModeText = '<BR/><BR/>';
    	if(responseJSON.showSuggestModeItem){
    		suggestModeText += '授信業務建議授權層級計算方式:<br/>';
    		var modeItemArr = responseJSON.showSuggestModeItem.split(",");

    		for (var i in modeItemArr) {
    			    //J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
            	 	if(modeItemArr[i] == 1){
            	 		suggestModeText += '<label class="suggestModeLabel" id="modeLabe1">' + 
    					'<input name="suggestMode" type="radio" value="1" onclick="CntrNoAPI.changeSuggestMode(1);"/> 1.個別借款人歸戶授權</label><br/>';
					}else if( modeItemArr[i] == 2 ){
						suggestModeText += '<label class="suggestModeLabel" id="modeLabe2">' + 
						'<input name="suggestMode" type="radio" value="2" onclick="CntrNoAPI.changeSuggestMode(2);"/> 2.全案所有借款人授權</label><br/>';
					}else if( modeItemArr[i] == 3 ){
						suggestModeText += '<label class="suggestModeLabel" id="modeLabe3">' +
						'<input name="suggestMode" type="radio" value="3" onclick="CntrNoAPI.changeSuggestMode(3);"/> 3.部分借款人或額度合併授權(不提供建議授權層級)</label>';
					}
             }
    	}
    	
		var saveRateFg= $('input:checkbox:checked[name="saveRateFg"]').val()== "Y" ? true : false;
        //L140M01a.error21 = 是否需調整計算後之合計值？ \r若需調整請按【確定】，系統會開啟調整畫面；\r若不調整請按【取消】，則以系統算出的金額填入額度
        CommonAPI.confirmMessage(i18n.lms1401s02['L140M01a.error21'] + suggestModeText, function(b){
            if (b) {
                var td = "";
                $.ajax({
                    handler: inits.fhandle,
                    async: false,
                    action: "queryL140m01aCountToEdit",
                    data: {
                        noOpenDoc: true,
                        mainId: $("#mainId").val(),
                        CaseType: inits.itemType,
                        curr: curr || "",
                        showCurr: showCurr || false,
						saveRateFg : saveRateFg
                    }
				}).done(function(obj){
					//J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
					//J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
					if(obj.showLgdTotAmt == "Y"){
						//J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
				    	for (var i = 1; i <= obj.lmsLgdCountTotal; i++) {
				    		if(obj["showLgdTotAmt_"+i] == "Y"){
				        		$(".showLgdTotAmt_"+i).show();
							}else{
								$(".showLgdTotAmt_"+i).hide();
							}
				    	}		
					}else{
						//J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
				    	for (var i = 1; i <= obj.lmsLgdCountTotal; i++) {
				    		$(".showLgdTotAmt_"+i).hide();
				    	}	
					}

					//J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					//*****************************************************************
					genCountEdit_1(obj);
					
					//J-111-0461_05097_B1006-2 授信額度合計新增單獨另計授權及各組LGD合計檢核
					// 合併改成一筆所有借款人合計，所以改到baseCount後才合計**********************
					g_editCountSize = obj.size1;
					 
					if(obj.size2 > 0 || obj.size3 > 0){
						$("#tab2").show();
						if(obj.size2 > 0){
				    		genCountEdit_2(obj);
				    	}
						if(obj.size3 > 0){
				    		genCountEdit_3(obj); 
				    	}
					}else{
						$("#tab2").hide();
					}
					
					//J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
					if(obj.size5 > 0 || obj.size6 > 0){
						$("#tab5").show();
						if(obj.size5 > 0){
				    		genCountEdit_5(obj);
				    	}
						if(obj.size6 > 0){
				    		genCountEdit_6(obj); 
				    	}
					}else{
						$("#tab5").hide();
					}
					 
					if(obj.size4 > 0){                    		
						var suggestModeVal = responseJSON.suggestMode;
				    	genCountEdit_4(obj,suggestModeText,suggestModeVal);
				    	var value = $("input[name=suggestMode]:checked").val();
				    	if (value == "2") {
				    		$("#tab3").show(); 
				        }else {
				        	$("#tab3").hide(); 
				        }
					}else{
						$("#tab3").hide();
					}
					 
					//*****************************************************************
					
					//J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
					//obj.size1
					countEditBox(g_editCountSize, curr, showCurr);
				   
				    // J-111-0536 授信額度合計顯示提示訊息
				    var loanTotAmtWriteSuggestSel = $("#loanTotAmtWriteSuggest");
				    // 此js會被額度明細&額度批覆call到，但目前不在額度批覆顯示suggest
				    if(loanTotAmtWriteSuggestSel.length > 0){
				    	loanTotAmtWriteSuggestSel.empty();
				    	if(obj.loanTotAmtSuggest){
				    		loanTotAmtWriteSuggestSel.injectData({'loanTotAmtWriteSuggest':obj.loanTotAmtSuggest},false);
				    	}else{
				    		loanTotAmtWriteSuggestSel.injectData({'loanTotAmtWriteSuggest':''},false);
				    	}
				    }
				    
				    //J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
				    var countEditMsg_3 = $("#countEditMsg_3");
				    countEditMsg_3.html(DOMPurify.sanitize(obj.countEditMsg_3));
				    var countEditMsg_4 = $("#countEditMsg_4");
				    countEditMsg_4.html(DOMPurify.sanitize(obj.countEditMsg_4));
                });
            }
            else {
                //直接做計算
                justOnlyCount(curr, showCurr);
            }
        });
    }
    
    //J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
    function registEvent_editCount(){
    	
    	//1.個別借款人歸戶授權
        //2.全案所有借款人授權
        //3.部分借款人或額度合併授權(不提供建議授權層級) =>已被棄用
    	$("input[name='suggestMode']").on("change", function() {
        	var value = $("input[name='suggestMode']:radio:checked").val();
        	if (value == "2") {
        		$("#tab3").show(); 
            }else {
            	$("#tab3").hide(); 
            }
        });
    	 
    	//第一頁籤********************************************************************************************
    	//授信額度合計(元)
    	$("#countEditForm").find("input[id^='loanTotAmt']").on("change", function() {
    		var $this = $(this);
    		//alert("this.id="+this.id +"，this.title="+this.title +"，this.val()="+$this.val());
    		$("#countEditForm").find("input[name='show_"+this.id+"']").val(util.addComma($this.val())).trigger("change");   //第二籤授信額度合計變更
    	});
    	
    	//第二頁籤 *********************************************************************************************
    	//單獨另計授權額度合計(元)	
//    	$("#countEditForm").find("input[id^='standAloneAuthTotal']").live("change", function() {
//    		var title = this.title;
//    		$("#countEditForm").find("input[name='show_loanTotAmt"+title+"']").trigger("change") ;   //授信額度合計變更
//    	});
    	
    	
    	//第二頁籤 LGD 1-5
    	$("#countEditForm").find("input[name^='lgdTotAmt_']").on("change", function() {
  
    		//授信額度合計變更觸發->授信授權額度
    		var $this = $(this);
    		var title = this.title;
    		var id = this.id;
    
    		var lgdTotAmtTotal = 0;
    		for (var i = 1; i <= g_lmsLgdCountTotal; i++) {
    			var newId = "lgdTotAmt_" + i + title ;
    			var lgdAmt = strToNum($("#countEditForm").find("input[id='" + newId + "']").val());
    			//alert(newId+"="+lgdAmt);
    			lgdTotAmtTotal =  lgdTotAmtTotal + lgdAmt;
        	}		
//    		alert("lgdTotAmtTotal="+lgdTotAmtTotal);
//    		alert("lgdTotAmtTotal toFix="+parseFloat(lgdTotAmtTotal.toFixed(2)));
    		//更新-授信授權額度***************************************************************
    		//更新第2頁籤 
    		$("#countEditForm").find("input[name='show_lgdTotAmt" + title+"']").val(util.addComma(numToFix(lgdTotAmtTotal,2))).trigger("change") ;  //(顯示)授信授權額度合計(元)
    		
    		 
    		//J-111-0461_05097_B1006-2 授信額度合計新增單獨另計授權及各組LGD合計檢核
    		// 合併改成一筆所有借款人合計，所以改到baseCount後才合計**********************
    		//更新第3頁籤
//    		var allCase_lgdTotMgAmt = 0;
//    		//更新-第3頁籤-LGD 1-5***************************************************************
//    		for (var i = 1; i <= g_lmsLgdCountTotal; i++) {
//    			var allCase_lgdTotAmt = 0;
//    			for (var s = 0; s < g_editCountSize; s++) {
//    				var tmpLgdAmt = strToNum($("#countEditForm").find("input[name='lgdTotAmt_" + i + s + "']").val());
//    				allCase_lgdTotAmt = allCase_lgdTotAmt + tmpLgdAmt ;
//    				allCase_lgdTotMgAmt = allCase_lgdTotMgAmt + tmpLgdAmt;
//    			}
//    			$("#countEditForm").find("input[name='lgdTotMgAmt_" + i + "0" +"']").val(util.addComma(allCase_lgdTotAmt)).trigger("change") ;  //LGD 1-5
//    			$("#countEditForm").find("input[name='show_lgdTotMgAmt_" + i + "0" +"']").val(util.addComma(allCase_lgdTotAmt)) ;  //(異動前)LGD 1-5
//        	}    		
//    		//更新第3頁籤
//    		$("#countEditForm").find("input[name='tmp_lgdTotMgAmt0']").val(util.addComma(allCase_lgdTotMgAmt)).trigger("change") ;  //授信授權額度合計(元)
//    		$("#countEditForm").find("input[name='show_tmp_lgdTotMgAmt0']").val(util.addComma(allCase_lgdTotMgAmt));  //(異動前)授信授權額度合計(元)
    	});
    	
    	 
//    	//授信額度合計變更
//    	$("#countEditForm").find("input[name^='show_loanTotAmt']").live("change", function() {
//    		//授信額度合計變更觸發->授信授權額度
//    		var $this = $(this);
//    		var title = this.title;
//    		var show_loanTotAmt = strToNum($this.val());
//    		//var standAloneAuthTotal = strToNum($("#countEditForm").find("input[name='standAloneAuthTotal"+this.title+"']").val());
//    		var standAloneAuthTotal = 0;  //單獨另計授權額度合計(元) 已取消
//    		var show_lgdTotAmt = (show_loanTotAmt - standAloneAuthTotal) < 0 ? 0 : (show_loanTotAmt - standAloneAuthTotal) ;
//    		 
//    		$("#countEditForm").find("input[name='show_lgdTotAmt"+title+"']").val(util.addComma(show_lgdTotAmt)).trigger("change") ;  //第二頁籤-授信授權額度
//    		$("#countEditForm").find("input[name='show_lgdTotMgAmt"+title+"']").val(util.addComma(show_lgdTotAmt)).trigger("change") ;  //第三頁籤-授信授權額度
//    	});
    	
    	//瑕疵押匯額度合計(元)
    	$("#countEditForm").find("input[id^='flawAmtTotal']").on("change", function() {
    		var $this = $(this);
    		var title = this.title;
    		var id = this.id;
    		$("#countEditForm").find("input[name='show_lgdTotAmt"+title+"']").trigger("change") ;    //授信授權額度變更
    		
    		//更新第3頁籤
//    		var allCase_flawAmtMgTotal = 0;
//    		for (var s = 0; s < g_editCountSize; s++) {
//    			allCase_flawAmtMgTotal = allCase_flawAmtMgTotal  +  strToNum($("#countEditForm").find("input[name='flawAmtTotal" + s + "']").val());
//    		}
//    		$("#countEditForm").find("input[name='flawAmtMgTotal0']").val(allCase_flawAmtMgTotal).trigger("change") ;  //瑕疵押匯額度合計(元)
//    		$("#countEditForm").find("input[name='show_flawAmtMgTotal0']").val(allCase_flawAmtMgTotal);  //(異動前)瑕疵押匯額度合計(元)
    		
    	});
    	
    	//授信授權額度變更
    	$("#countEditForm").find("input[name^='show_lgdTotAmt']").on("change", function() {
    		//授信額度合計變更觸發->授信授權額度
    		var $this = $(this);
    		var title = this.title;
    		var show_lgdTotAmt = strToNum($this.val());
     
    		var flawAmtTotal = strToNum($("#countEditForm").find("input[name='flawAmtTotal"+this.title+"']").val());
    		var generalLoanTotAmt = (show_lgdTotAmt +  flawAmtTotal) < 0 ? 0 : (show_lgdTotAmt +  flawAmtTotal) ;
    		$("#countEditForm").find("input[name='generalLoanTotAmt"+title+"']").val(util.addComma(numToFix(generalLoanTotAmt,2))) ;  //總授信授權額度合計(元)
    		
    		//更新第3頁籤
//    		var allCase_generalLoanMgTotAmt = 0;
//    		for (var s = 0; s < g_editCountSize; s++) {
//    			var show_lgdTotAmt = strToNum($("#countEditForm").find("input[name='show_lgdTotAmt"+ s +"']").val());
//    			var flawAmtTotal = strToNum($("#countEditForm").find("input[name='flawAmtTotal"+ s +"']").val());
//        		var generalLoanTotAmt = (show_lgdTotAmt +  flawAmtTotal) < 0 ? 0 : (show_lgdTotAmt +  flawAmtTotal) ;
//    			allCase_generalLoanMgTotAmt = allCase_generalLoanMgTotAmt  +  generalLoanTotAmt  ;
//    		}
//    		$("#countEditForm").find("input[name='generalLoanMgTotAmt0']").val(util.addComma(allCase_generalLoanMgTotAmt)) ;  //總授信授權額度合計(元)
//    		$("#countEditForm").find("input[name='show_generalLoanMgTotAmt0']").val(util.addComma(allCase_generalLoanMgTotAmt)) ;  //總授信授權額度合計(元)
    	});
    	
    	//J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
    	//第三頁籤合併關係企業********************************************************************************************
    	// LGD 1-5
    	$("#countEditForm").find("input[name^='lgdTotRcAmt_']").on("change", function() {
  
    		//授信額度合計變更觸發->授信授權額度
    		var $this = $(this);
    		var title = this.title;
    		var id = this.id;
    
    		var lgdTotRcAmtTotal = 0;
    		for (var i = 1; i <= g_lmsLgdCountTotal; i++) {
    			var newId = "lgdTotRcAmt_" + i + title ;
    			var lgdRcAmt = strToNum($("#countEditForm").find("input[id='" + newId + "']").val());
    			//alert(newId+"="+lgdAmt);
    			lgdTotRcAmtTotal =  lgdTotRcAmtTotal + lgdRcAmt;
        	}		
//    		alert("lgdTotAmtTotal="+lgdTotAmtTotal);
//    		alert("lgdTotAmtTotal toFix="+parseFloat(lgdTotAmtTotal.toFixed(2)));
    		//更新-授信授權額度***************************************************************
    		//更新第2頁籤 
    		$("#countEditForm").find("input[name='show_lgdTotRcAmt" + title+"']").val(util.addComma(numToFix(lgdTotRcAmtTotal,2))).trigger("change") ;  //(顯示)授信授權額度合計(元)
    		
    	});	 
    		 
    	
    	//瑕疵押匯額度合計(元)
    	$("#countEditForm").find("input[id^='flawAmtRcTotal']").on("change", function() {
    		var $this = $(this);
    		var title = this.title;
    		var id = this.id;
    		$("#countEditForm").find("input[name='show_lgdTotRcAmt"+title+"']").trigger("change") ;    //授信授權額度變更
    	 
    	});
    	
    	//授信授權額度變更
    	$("#countEditForm").find("input[name^='show_lgdTotRcAmt']").on("change", function() {
    		//授信額度合計變更觸發->授信授權額度
    		var $this = $(this);
    		var title = this.title;
    		var show_lgdTotRcAmt = strToNum($this.val());
     
    		var flawAmtRcTotal = strToNum($("#countEditForm").find("input[name='flawAmtRcTotal"+this.title+"']").val());
    		var generalLoanRcTotAmt = (show_lgdTotRcAmt +  flawAmtRcTotal) < 0 ? 0 : (show_lgdTotRcAmt +  flawAmtRcTotal) ;
    		$("#countEditForm").find("input[name='generalLoanRcTotAmt"+title+"']").val(util.addComma(numToFix(generalLoanRcTotAmt,2))) ;  //總授信授權額度合計(元)
    		
    	});
    	
    	
    	
    	//第四頁籤********************************************************************************************
    	//全案
    	//LGD 1-5
    	$("#countEditForm").find("input[name^='lgdTotMgAmt_']").on("change", function() {
    		var $this = $(this);
    		var title = this.title;
    		var id = this.id;
    		caculate_generalLoanMgTotAmt(title,id);
    	});
    	
    	//瑕疵押匯額度合計(元)
    	$("#countEditForm").find("input[id^='flawAmtMgTotal']").on("change", function() {
    		var $this = $(this);
    		var title = this.title;
    		var id = this.id;
    		caculate_generalLoanMgTotAmt(title,id);
    	});
    	 
    	
    	//授信授權額度
//    	$("#countEditForm").find("input[name^='tmp_lgdTotMgAmt']").live("change", function() {
//    		//授信額度合計變更觸發->授信授權額度
//    		var $this = $(this);
//    		var title = this.title;
//    		var lgdTotMgAmt = strToNum($this.val());
//    		 
//    		var flawAmtMgTotal = strToNum($("#countEditForm").find("input[name='flawAmtMgTotal"+this.title+"']").val());
//    		var generalLoanMgTotAmt = (lgdTotMgAmt +  flawAmtMgTotal) < 0 ? 0 : (lgdTotMgAmt +  flawAmtMgTotal) ;
//    		$("#countEditForm").find("input[name='generalLoanMgTotAmt"+title+"']").val(util.addComma(generalLoanMgTotAmt)) ; 
//    	});

    }
    
    function caculate_generalLoanMgTotAmt(title,id){
    	//授信額度合計變更觸發->授信授權額度
		var lgdTotMgAmt = 0;
		for (var i = 1; i <= g_lmsLgdCountTotal; i++) {
			var newId = "lgdTotMgAmt_" + i + title ;
			lgdTotMgAmt =  lgdTotMgAmt + strToNum($("#countEditForm").find("input[id='" + newId + "']").val());
    	}		
	 
		var flawAmtMgTotal = strToNum($("#countEditForm").find("input[name='flawAmtMgTotal"+title+"']").val());
		var generalLoanMgTotAmt = (lgdTotMgAmt +  flawAmtMgTotal) < 0 ? 0 : (lgdTotMgAmt +  flawAmtMgTotal) ;
		$("#countEditForm").find("input[name='generalLoanMgTotAmt"+title+"']").val(util.addComma(generalLoanMgTotAmt)) ; 
    }
    
     
    //文字轉數字，Nan回傳0
    function strToNum(inStr){
    	var value = parseFloat(util.delComma(inStr));
    	return (isNaN(value) ? 0 : value) ;
    }
    
    //去除數字相加後JS會有很多小數位，例如: 1234.9999999998
    function numToFix(inVal,fixNum){
    	var value = parseFloat(inVal.toFixed(fixNum))
    	return (isNaN(value) ? 0 : value) ;
    }
    
    //J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
    function genCountEdit_1(obj){

    	//J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
    	$("#countEditTitle1").empty();
    	var td = "";
//    	alert("obj.item1="+obj.item1);
//    	alert("obj.size1="+obj.size1);
    	var item = JSON.parse(JSON.stringify(obj.item1));
    	var size = JSON.parse(JSON.stringify(obj.size1));
    	var title = item[0].title;
    	for (var colName in title) {
    		var lgdPrintBtn = "";
//    		if(colName == "lgdTotAmt_1"){
//    			lgdPrintBtn += "<br><button type='button' id='printLgdDetail' class='ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only fg-button' onclick= 'CntrNoAPI.print_Lgd();' ><span class='ui-button-text'><span class='text-only'>查詢</span></span></button>";
//    		}
    		var titleName = DOMPurify.sanitize(title[DOMPurify.sanitize(colName)]);
    		td += "<td class='hd2 ct' ><span>" + titleName+"</span>"+lgdPrintBtn+"</td>"; 
        }
    	//$("#countEditTitle").html("<tr>" + td + "</tr>");
    	var countEditTitle = $("#countEditTitle1");
    	//countEditTitle.html("<tr>" + td + "</tr>");
    	var titleStr = "<tr>" + td + "</tr>";
    	//countEditTitle.injectData({'countEditTitle':titleStr},false);
    	countEditTitle.html(titleStr);
    	td = "";
         
    	
        $("#countEditBody1").empty();
        for (var i = 0; i < size; i++) {
        	var nameMemo = "<br><div style='text-align: left;'><span class='text-red' >"+ DOMPurify.sanitize(item[i].nameMemo) +"</span></div>";
            td += "<td ><input value='" + DOMPurify.sanitize(item[i].name) + "' disabled/><input name='custId" + i + "' value='" + DOMPurify.sanitize(item[i].id) + "' style='display:none' />"+nameMemo+"</td>";
            //J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
            //L140M01a.saveCount_9=異動前
            
            td += "<td><select id='curr" + i + "'name='countCurr" + i + "'class='curr reuqired'/><br><div style='text-align: center;'><span class='text-red' >("+i18n.lms1401s02['L140M01a.saveCount_9']+")"+ DOMPurify.sanitize(item[i].curr) +"</span></div></td>";
            var counts = item[i].count;
            
            //J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
            for (var colName in counts) {
            	//J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
            	var colNameD = DOMPurify.sanitize(colName);
            	if(DOMPurify.sanitize(counts[colNameD]) == "readOnly"){
            		td += "<td><input id='" + colNameD + i + "' name='" + colNameD + i + "' title='" + i + "' value='' size='19' maxlength='22'  readonly=readonly  /></td>";
            	}else{
            		//J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
            		td += "<td><input id='" + colNameD + i + "' name='" + colNameD + i + "' title='" + i + "' value='" + DOMPurify.sanitize(counts[colNameD]) + "' size='19' positiveonly='true' integer='15' fraction='2' class='numeric  required' maxlength='22' /><br><div style='text-align: right;'><span class='text-red' >"+ DOMPurify.sanitize(counts[colNameD])+"</span></div></td>";
            	}
            }
            var drcs = item[i].drc;
            if (!$.isEmptyObject(drcs)) {
                for (var colName in drcs) {
                	var colNameD = DOMPurify.sanitize(colName);
                	if(DOMPurify.sanitize(drcs[colNameD]) == "readOnly"){
                		td += "<td><textarea id='" + colNameD + i + "' name='" + colNameD + i + "'rows='5' maxlengthC='100' readonly=readonly  ></textarea></td>";
                	}else{
                		td += "<td><textarea id='" + colNameD + i + "' name='" + colNameD + i + "'rows='5' maxlengthC='100' >" + DOMPurify.sanitize(drcs[colNameD]) + "</textarea></td>";
                	}                                  
                }
            }
            else {
                //無多幣別敘述開空欄位
                //td += "<td><textarea id='MultiAmt" + i + "' name='MultiAmt" + i + "' rows='5' maxlengthC='100' readonly=readonly disabled=disabled ></textarea></td>";
                //td += "<td><textarea id='MultiAssureAmt" + i + "' name='MultiAssureAmt" + i + "' rows='5' maxlengthC='100' readonly=readonly disabled=disabled ></textarea></td>";
            }
            $("#countEditBody1").append("<tr>" + td + "</tr>");
            td = "";
        }
       
        //修改視窗
        var objs = CommonAPI.loadCombos(["Common_Currcy"]);
        
        $("select.curr").setItems({
            space: false,
            item: objs.Common_Currcy,
            value: "TWD",
            format: "{value} - {key}"
        });
        
        //把相對應的幣別放入
        for (var i = 0; i < size; i++) {
            $("[name=countCurr" + i + "]").val(DOMPurify.sanitize(item[i].curr));
        }
        
    }
    
    //J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
    function genCountEdit_2(obj){
    	$("#countEditTitle2").empty();
    	var td = "";
//    	alert("obj.item2="+obj.item2);
//    	alert("obj.size2="+obj.size2);
    	var item = JSON.parse(JSON.stringify(obj.item2));
    	var size = JSON.parse(JSON.stringify(obj.size2));
    	var title = item[0].title;
    	
    	for (var colName in title) {
    		var lgdPrintBtn = "";
//    		if(colName == "lgdTotAmt_1"){
//    			lgdPrintBtn += "<br><button type='button' id='printLgdDetail' class='ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only fg-button' onclick= 'CntrNoAPI.print_Lgd();' ><span class='ui-button-text'><span class='text-only'>查詢</span></span></button>";
//    		}
    		td += "<td class='hd2 ct' ><span>"+DOMPurify.sanitize(title[DOMPurify.sanitize(colName)]) +"</span>"+lgdPrintBtn+"</td>"; 
        }
    	//$("#countEditTitle").html("<tr>" + td + "</tr>");
    	var countEditTitle = $("#countEditTitle2");
    	//countEditTitle.html("<tr>" + td + "</tr>");
    	var titleStr = "<tr>" + td + "</tr>";
    	//countEditTitle.injectData({'countEditTitle':titleStr},false);
    	countEditTitle.html(titleStr);
    	
        td = "";
         
    	
        $("#countEditBody2").empty();
        for (var i = 0; i < size; i++) {
        	//L140M01a.saveCount_9=異動前
            td += "<td ><input value='" + DOMPurify.sanitize(item[i].name) + "' disabled/><input value='" + DOMPurify.sanitize(item[i].id) + "' style='display:none' /><br><div style='text-align: center;'><span class='text-red' >("+i18n.lms1401s02['L140M01a.saveCount_9']+")</span></div></td>";
            var counts = item[i].count;
            var readOnlys = item[i].readOnly;
            
            //J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
            for (var colName in counts) {
            	var colNameD = DOMPurify.sanitize(colName);
            	//J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
            	if(DOMPurify.sanitize(readOnlys[colNameD]) == "Y"){
            		td += "<td><input name='" + colNameD + i + "' value='" + DOMPurify.sanitize(counts[colNameD]) + "' title='" + i + "'  readonly=readonly   /></td>";
            	}else{
            		//J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
            		td += "<td><input id='" + colNameD + i + "' name='" + colNameD + i + "' title='" + i + "' value='" + DOMPurify.sanitize(counts[colNameD]) + "' size='19' positiveonly='true' integer='15' fraction='2' class='numeric  required' maxlength='22' /><br><div style='text-align: right;'><span class='text-red' >"+ DOMPurify.sanitize(counts[colNameD])+"</span></div></td>";
            	}
            }
            
            
            $("#countEditBody2").append("<tr>" + td + "</tr>");
            td = "";
        }
        
        
    }
    
    //J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
    function genCountEdit_3(obj){
    	$("#countEditTitle3").empty();
    	var td = "";
//    	alert("obj.item3="+obj.item3);
//    	alert("obj.size3="+obj.size3);
    	var item = JSON.parse(JSON.stringify(obj.item3));
    	var size = JSON.parse(JSON.stringify(obj.size3));
    	var title = item[0].title;
    	
    	for (var colName in title) {
    		var lgdPrintBtn = "";
    		td += "<td class='hd2 ct' ><span>"+DOMPurify.sanitize(title[DOMPurify.sanitize(colName)]) +"</span>"+lgdPrintBtn+"</td>"; 
        }
    	//$("#countEditTitle").html("<tr>" + td + "</tr>");
    	var countEditTitle = $("#countEditTitle3");
    	//countEditTitle.html("<tr>" + td + "</tr>");
    	var titleStr = "<tr>" + td + "</tr>";
    	//countEditTitle.injectData({'countEditTitle':titleStr},false);
    	countEditTitle.html(titleStr);
    	
        td = "";
         
    	
        $("#countEditBody3").empty();
        for (var i = 0; i < size; i++) {
        	//L140M01a.saveCount_9=異動前
            td += "<td ><input value='" + DOMPurify.sanitize(item[i].name) + "' disabled/><input value='" + DOMPurify.sanitize(item[i].id) + "' style='display:none' /><br><div style='text-align: center;'><span class='text-red' >("+i18n.lms1401s02['L140M01a.saveCount_9']+")</span></div></td>";
            var counts = item[i].count;
            var readOnlys = item[i].readOnly;
            
            //J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
            for (var colName in counts) {
            	var colNameD = DOMPurify.sanitize(colName);
            	//J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
            	if(DOMPurify.sanitize(readOnlys[colNameD]) == "Y"){
            		td += "<td><input name='" + colNameD + i + "' value='" + DOMPurify.sanitize(counts[colNameD]) + "' title='" + i + "' readonly=readonly /></td>";
            	}else{
            		//J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
            		td += "<td><input id='" + colNameD + i + "' name='" + colNameD + i + "' title='" + i + "' value='" + DOMPurify.sanitize(counts[colNameD]) + "' size='19' positiveonly='true' integer='15' fraction='2' class='numeric  required' maxlength='22' /><br><div style='text-align: right;'><span class='text-red' >"+ DOMPurify.sanitize(counts[colNameD])+"</span></div></td>";
            	}
            }
            
            
            $("#countEditBody3").append("<tr>" + td + "</tr>");
            td = "";
        }
        
        
    }
    
    //J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
    function genCountEdit_4(obj,suggestModeText,suggestModeVal){
        
    	$("#suggestModeTextDiv").empty();
    	var suggestModeTextDiv = $("#suggestModeTextDiv");
    	suggestModeTextDiv.html(suggestModeText);
    	$("input[name='suggestMode'][value='"+suggestModeVal+"']:radio" ).prop( "checked" , "checked" );
    	
    	$("#countEditTitle4").empty();
    	var td = "";
//    	alert("obj.item4="+obj.item4);
//    	alert("obj.size4="+obj.size4);
    	var item = JSON.parse(JSON.stringify(obj.item4));
    	var size = JSON.parse(JSON.stringify(obj.size4));
    	var title = item[0].title;
    	
    	for (var colName in title) {
    		var lgdPrintBtn = "";
    		td += "<td class='hd2 ct' ><span>"+DOMPurify.sanitize(title[DOMPurify.sanitize(colName)]) +"</span>"+lgdPrintBtn+"</td>"; 
        }
    	//$("#countEditTitle").html("<tr>" + td + "</tr>");
    	var countEditTitle = $("#countEditTitle4");
    	//countEditTitle.html("<tr>" + td + "</tr>");
    	var titleStr = "<tr>" + td + "</tr>";
    	//countEditTitle.injectData({'countEditTitle':titleStr},false);
    	countEditTitle.html( titleStr);
    	
        td = "";
         
        $("#countEditBody4").empty();
        for (var i = 0; i < size; i++) {
        	//L140M01a.saveCount_9=異動前
            //td += "<td ><input value='" + item[i].name + "' disabled/><input value='" + item[i].id + "' style='display:none' /><br><div style='text-align: center;'><span class='text-red' >("+i18n.lms1401s02['L140M01a.saveCount_9']+")</span></div></td>";
        	//var showLgdTotMgCurr = "<div><span class='text-red' >("+i18n.lms1401s02['L140M01a.saveCount_9']+")</span>&nbsp;&nbsp;<span class='text-red'><input name='show_lgdTotMgCurr" + i + "' value='" + item[i].lgdTotMgCurr + "' title='" + i + "' style='color:red' /></span></div>";
            td += "<td><select id='lgdTotMgCurr" + i + "'name='lgdTotMgCurr" + i + "'class='lgdTotMgCurr reuqired'/><br><div><span class='text-red' style='text-align: left;'>("+i18n.lms1401s02['L140M01a.saveCount_9']+")</span>&nbsp;&nbsp;<span class='text-red' style='text-align: right;'>"+ DOMPurify.sanitize(item[i].lgdTotMgCurr) +"</span></div></td>";
            
            var counts = item[i].count;
            var readOnlys = item[i].readOnly;
             
            //J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
            //var showField="";
            for (var colName in counts) {
            	var colNameD = DOMPurify.sanitize(colName);
            	//J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
            	var showField="";
            	if(DOMPurify.sanitize(readOnlys[colNameD]) == "Y"){
            		td += "<td><input name='" + colNameD + i + "' value='" + DOMPurify.sanitize(counts[colNameD]) + "' title='" + i + "' style='color:red' readonly=readonly /></td>";
            	}else if(DOMPurify.sanitize(readOnlys[colNameD]) == "X"){
            		//J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
            		showField = "<div><span class='text-red'><input name='show_" + colNameD + i + "' value='" + DOMPurify.sanitize(counts[colNameD]) + "' title='" + i + "' style='color:red' readonly=readonly /></span></div>";
            		td += "<td><input id='" + colNameD + i + "' name='" + colNameD + i + "' title='" + i + "' value='" + DOMPurify.sanitize(counts[colNameD]) + "' readonly=readonly /><br>"+showField+"</td>";
            	}else{
            		//J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
            		showField = "<div><span class='text-red'><input name='show_" + colNameD + i + "' value='" + DOMPurify.sanitize(counts[colNameD]) + "' title='" + i + "' style='color:red'  readonly=readonly /></span></div>";
            		td += "<td><input id='" + colNameD + i + "' name='" + colNameD + i + "' title='" + i + "' value='" + DOMPurify.sanitize(counts[colNameD]) + "' size='19' positiveonly='true' integer='15' fraction='2' class='numeric  required' maxlength='22' /><br>"+showField+"</td>";
            	}
            }
             
            $("#countEditBody4").append("<tr>" + td + "</tr>");
            td = "";
        }
        
        var objs = CommonAPI.loadCombos(["Common_Currcy"]);
        $("select.lgdTotMgCurr").setItems({
            space: false,
            item: objs.Common_Currcy,
            value: "TWD",
            format: "{value} - {key}"
        });
        
        //把相對應的幣別放入
        for (var i = 0; i < size; i++) {
            $("[name=lgdTotMgCurr" + i + "]").val(DOMPurify.sanitize(item[i].lgdTotMgCurr));
        }
        
    }
    
    //J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
    function genCountEdit_5(obj){
    	$("#countEditTitle5").empty();
    	var td = "";
//    	alert("obj.item2="+obj.item2);
//    	alert("obj.size2="+obj.size2);
    	var item = JSON.parse(JSON.stringify(obj.item5));
    	var size = JSON.parse(JSON.stringify(obj.size5));
    	var title = item[0].title;
    	
    	for (var colName in title) {
    		var lgdPrintBtn = "";
//    		if(colName == "lgdTotAmt_1"){
//    			lgdPrintBtn += "<br><button type='button' id='printLgdDetail' class='ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only fg-button' onclick= 'CntrNoAPI.print_Lgd();' ><span class='ui-button-text'><span class='text-only'>查詢</span></span></button>";
//    		}
    		td += "<td class='hd2 ct' ><span>"+DOMPurify.sanitize(title[DOMPurify.sanitize(colName)]) +"</span>"+lgdPrintBtn+"</td>"; 
        }
    	//$("#countEditTitle").html("<tr>" + td + "</tr>");
    	var countEditTitle = $("#countEditTitle5");
    	//countEditTitle.html("<tr>" + td + "</tr>");
    	var titleStr = "<tr>" + td + "</tr>";
    	//countEditTitle.injectData({'countEditTitle':titleStr},false);
    	countEditTitle.html(titleStr);
    	
        td = "";
         
    	
        $("#countEditBody5").empty();
        for (var i = 0; i < size; i++) {
        	//L140M01a.saveCount_9=異動前
            td += "<td ><input value='" + DOMPurify.sanitize(item[i].name) + "' disabled/><input value='" + DOMPurify.sanitize(item[i].id) + "' style='display:none' /><br><div style='text-align: center;'><span class='text-red' >("+i18n.lms1401s02['L140M01a.saveCount_9']+")</span></div></td>";
            var counts = item[i].count;
            var readOnlys = item[i].readOnly;
            
            //J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
            for (var colName in counts) {
            	var colNameD = DOMPurify.sanitize(colName);
            	//J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
            	if(DOMPurify.sanitize(readOnlys[colNameD]) == "Y"){
            		td += "<td><input name='" + colNameD + i + "' value='" + DOMPurify.sanitize(counts[colNameD]) + "' title='" + i + "'  readonly=readonly   /></td>";
            	}else{
            		//J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
            		td += "<td><input id='" + colNameD + i + "' name='" + colNameD + i + "' title='" + i + "' value='" + DOMPurify.sanitize(counts[colNameD]) + "' size='19' positiveonly='true' integer='15' fraction='2' class='numeric  required' maxlength='22' /><br><div style='text-align: right;'><span class='text-red' >"+ DOMPurify.sanitize(counts[colNameD])+"</span></div></td>";
            	}
            }
            
            
            $("#countEditBody5").append("<tr>" + td + "</tr>");
            td = "";
        }
        
        
    }
    
    //J-111-0461_05097_B1009 授信額度合計新增單獨另計授權及各組LGD合計檢核
    function genCountEdit_6(obj){
    	$("#countEditTitle6").empty();
    	var td = "";
//    	alert("obj.item3="+obj.item3);
//    	alert("obj.size3="+obj.size3);
    	var item = JSON.parse(JSON.stringify(obj.item6));
    	var size = JSON.parse(JSON.stringify(obj.size6));
    	var title = item[0].title;
    	
    	for (var colName in title) {
    		var lgdPrintBtn = "";
    		td += "<td class='hd2 ct' ><span>"+DOMPurify.sanitize(title[DOMPurify.sanitize(colName)]) +"</span>"+lgdPrintBtn+"</td>"; 
        }
    	//$("#countEditTitle").html("<tr>" + td + "</tr>");
    	var countEditTitle = $("#countEditTitle6");
    	//countEditTitle.html("<tr>" + td + "</tr>");
    	var titleStr = "<tr>" + td + "</tr>";
    	//countEditTitle.injectData({'countEditTitle':titleStr},false);
    	countEditTitle.html(titleStr);
    	
        td = "";
         
    	
        $("#countEditBody6").empty();
        for (var i = 0; i < size; i++) {
        	//L140M01a.saveCount_9=異動前
            td += "<td ><input value='" + DOMPurify.sanitize(item[i].name) + "' disabled/><input value='" + DOMPurify.sanitize(item[i].id) + "' style='display:none' /><br><div style='text-align: center;'><span class='text-red' >("+i18n.lms1401s02['L140M01a.saveCount_9']+")</span></div></td>";
            var counts = item[i].count;
            var readOnlys = item[i].readOnly;
            
            //J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
            for (var colName in counts) {
            	var colNameD = DOMPurify.sanitize(colName);
            	//J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
            	if(DOMPurify.sanitize(readOnlys[colNameD]) == "Y"){
            		td += "<td><input name='" + colNameD + i + "' value='" + DOMPurify.sanitize(counts[colNameD]) + "' title='" + i + "' readonly=readonly /></td>";
            	}else{
            		//J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核
            		td += "<td><input id='" + colNameD + i + "' name='" + colNameD + i + "' title='" + i + "' value='" + DOMPurify.sanitize(counts[colNameD]) + "' size='19' positiveonly='true' integer='15' fraction='2' class='numeric  required' maxlength='22' /><br><div style='text-align: right;'><span class='text-red' >"+ DOMPurify.sanitize(counts[colNameD])+"</span></div></td>";
            	}
            }
            
            
            $("#countEditBody6").append("<tr>" + td + "</tr>");
            td = "";
        }
        
        
    }
    
    
    /**
     * 直接做計算
     * @param {Object} curr 多幣別時的計算幣別
     * @param {Object} showCurr 是否多幣別顯示
     */
    function justOnlyCount(curr, showCurr){
		var saveRateFg= $('input:checkbox:checked[name="saveRateFg"]').val()== "Y" ? true : false;
        $.ajax({
            handler: inits.fhandle,
            action: (curr) ? "queryL140m01aCountToTwoCurr" : "queryL140m01aCount",
            data: {
                noOpenDoc: true,
                mainId: $("#mainId").val(),
                CaseType: inits.itemType,
                showCurr: showCurr || false,
                curr: curr,
				saveRateFg:saveRateFg
            },
            success: function(obj){
            }
		}).done(function(obj){
			//CntrNoAPI._triggerMainGrid(true);

			//G-104-0097-001 Web e-Loan 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。
			//重新計算信用風險遵循
			//alert("justOnlyCount");
			$.ajax({
			    handler: inits.fhandle,
			    action:  "reCaculateL120S01M",
			    data: {
			        noOpenDoc: true,
			        mainId: $("#mainId").val(),
			        CaseType: inits.itemType
			    }
			}).done(function(obj){
				if(obj.errMsg != ""){
					CommonAPI.showMessage(obj.errMsg);
					CntrNoAPI._triggerMainGrid(true);
				    return false;
				}
				CommonAPI.showMessage(i18n.def['runSuccess']);
				CntrNoAPI._triggerMainGrid(true);
			});	

			if(obj.highestCaseLvlSpan){
				// J-111-0461 簽報書上呈現最終最高審核層級
				$(".highestCaseLvlSpan").html(DOMPurify.sanitize(obj.highestCaseLvlSpan));
			}else{
				$(".highestCaseLvlSpan").html("");
			}
			// 疑似逾越授權紅字+公式
			if("Y" == obj.overAuthVersion2024){
				// 是啟用新版逾越授權的情況下這邊才要做事，不然都不會在這裡做事
				$("#overAuthMessageOnTitle").html(DOMPurify.sanitize(obj.overAuthMessageOnTitle));
				$("#overAuthDetailSpan").html(DOMPurify.sanitize(obj.overAuthDetailSpan));
			}
        });
    }
    
    /** 檢查陣列內容是否重複 */
    function checkArrayRepeat(arrVal){
        var newArray = [];
        for (var i = arrVal.length; i--;) {
            var val = arrVal[i];
            if ($.inArray(val, newArray) == -1) {
                newArray.push(val);
            }
            else {
                return true;
            }
        }
        return false;
    }
    
    
    //批覆
    function toDoCheck(){
        if (!CntrNoAPI.isCheckGrid()) {
            return false;
        }
        if (beforeCheckGridDef.state() == "resolved") {
            $("#beforeCheckGridview").trigger("reloadGrid");
        }
        else {
            beforeCheckGridDef.resolve();
        }
   
        $("[name=checkBtRadio]").removeAttr("disabled");
        $("[name=checkBtRadio][value=1]").prop("checked", true);
        
        //J-112-0426_05097_B1001 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。
        $("input[name='checkBtRadio']" ).trigger("change"); 
        
		// J-108-0166  社會與環境風險評估
		$('#cesRjtCauseEnt option[value=24]').prop("disabled", true);
		
        $("#checkBtBox").thickbox({
            //title.20=整批批覆作業
            title: i18n.lms1401s02['title.20'],
            width: 830,
            height: 400,
            align: "center",
            valign: "bottom",
            modal: true,
            readOnly: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var checked = $("[name=checkBtRadio]:checked").val();
                    var ids = $("#beforeCheckGridview").getGridParam('selarrrow');
                    
                    var oids = [];
                    for (var i in ids) {
                        oids.push($("#beforeCheckGridview").getRowData(ids[i]).oid);
                    }
                    if (ids == "") {
                        //action_005=請先選取一筆以上之資料列
                        return CommonAPI.showErrorMessage(i18n.def['action_005']);
                    }
             
                    //J-112-0426_05097_B1001 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。
                    var checkBtEsg = $('#checkBtEsg').val();
                    switch (checked) {
                    case "1":
                    case "2":
					case "4":
                    	if($('#checkBtEsg').val() == '' ){
                    		//title.checkBtEsg=涉及ESG風險授信案件之審查註記
                    		return CommonAPI.showErrorMessage(i18n.lms1401s02['title.checkBtEsg']+i18n.def['val.required']);
                    	}
						break;
                    }
                    
                    switch (checked) {
                        case "1":
						case "4":
                        case "3":
                            $.thickbox.close();
                            $.ajax({
                                handler: inits.fhandle,
                                data: {
                                    formAction: "checkAction",
                                    oids: oids,
                                    doAction: checked,
                                    itemType: inits.itemType,
                                    //J-112-0426_05097_B1001 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。
                                    checkBtEsg : checkBtEsg    
                                }
							}).done(function(obj){
								CntrNoAPI._triggerMainGrid(true);
                            });
                            break;
                        case "2":
                            $.thickbox.close();
                            //打開婉卻輸入欄 
                            rejectCauseBox(oids, checked);
                            break;
                        default:
                            //l120m01a.error1=請選擇
                            return CommonAPI.showErrorMessage(i18n.lms1205m01['l120m01a.error1']);
                            break;
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }

	/**
	 * J-106-0113-001 Web e-Loan國內企金針對銀行法第72-2條之授信戶(含新、舊案)，於E-LOAN授信管理系統額度明細表中，加設警語，以達控管之效。
	 */
	function countValue(){
		
		var $L120M01BForm = $("#L120M01BForm");
		var unitCase = $L120M01BForm.find("input[name='unitCase']:radio:checked" ).val();  
		var unitMega = $L120M01BForm.find("input[name='unitMega']:radio:checked" ).val(); 
		
        if (!CntrNoAPI.isCheckGrid()) {
            return false;
        };
		
		$.ajax({
			handler: inits.fhandle,
			action: "queryCountCurr",
			data: {
				itemType: inits.itemType,
				mainId: $("#mainId").val(),
				unitCase: unitCase,
				unitMega: unitMega,
				noOpenDoc: true
			}
		}).done(function(obj){
			//判斷簽報書項下額度明細表/批覆書是否 cntrChgOid 都等於 簽報書的oid
			$.ajax({
				handler: inits.fhandle,
				action: "chkTotalNeedChange",
				data: {
					itemType: inits.itemType,
					mainId: $("#mainId").val()
				}
			}).done(function(objChg){
				if (objChg.totalNeedChange == "N") {
					//合計可以不用重新計算
					var orgReadOnlyStatus = thickboxOptions.readOnly;
					thickboxOptions.readOnly = false;
					
					CommonAPI.iConfirmDialog({
						message: i18n.lms1401s02["L140M01a.chkReCaculateTot"], //"是否需重新計算?(選則否沿用上次計算結果)
						buttons: API.createJSON([{
							key: i18n.def.yes,
							value: function(){
								thickboxOptions.readOnly = orgReadOnlyStatus;
								$.thickbox.close();
								if (!obj.printSeqMsg || obj.printSeqMsg == "") {
									if (obj.theCase) {
										//幣別
										$("#countEditBoxCurrSelect").setItems({
											item: obj.curr,
											value: "TWD",
											format: "{value} - {key}"
										});
										//請選擇計算幣別
										selecCountMoney();
									}
									else {
										//調整視窗
										editCount(false, false);
									}
								}
								else {
									CommonAPI.confirmMessage(obj.printSeqMsg, function(b){
										if (b) {
											//當同一借款人的額度明細表有兩種以上幣別彈出詢問視窗
											if (obj.theCase) {
												//幣別
												$("#countEditBoxCurrSelect").setItems({
													item: obj.curr,
													format: "{value} - {key}"
												});
												//請選擇計算幣別
												selecCountMoney();
											}
											else {
												//調整視窗
												editCount(false, true);
											}
										}
									});
								}
							}
						}, {
							key: i18n.def.no,
							value: function(){
								//否--不用重新計算
								//更新額度明細表/批覆書 check Flag為V	
								thickboxOptions.readOnly = orgReadOnlyStatus;
								$.thickbox.close();
								$.ajax({
									handler: inits.fhandle,
									action: "setCntrCompleteCaculateTotal",
									data: {
										itemType: inits.itemType,
										mainId: $("#mainId").val()
									}
								}).done(function(obj){
									//runSuccess=執行成功
									CntrNoAPI._triggerMainGrid(true);
									CommonAPI.showMessage(i18n.def["runSuccess"]);
								});
								
							}
						}, {
							key: i18n.def.cancel,
							value: function(){
								thickboxOptions.readOnly = orgReadOnlyStatus;
								$.thickbox.close();
							}
						}])
					});
					
					
				}
				else {
					//--必須重新計算
					if (!obj.printSeqMsg || obj.printSeqMsg == "") {
						if (obj.theCase) {
							//幣別
							$("#countEditBoxCurrSelect").setItems({
								item: obj.curr,
								value: "TWD",
								format: "{value} - {key}"
							});
							//請選擇計算幣別
							selecCountMoney();
						}
						else {
							//調整視窗
							editCount(false, false);
						}
					}
					else {
						CommonAPI.confirmMessage(obj.printSeqMsg, function(b){
							if (b) {
								//當同一借款人的額度明細表有兩種以上幣別彈出詢問視窗
								if (obj.theCase) {
									//幣別
									$("#countEditBoxCurrSelect").setItems({
										item: obj.curr,
										format: "{value} - {key}"
									});
									//請選擇計算幣別
									selecCountMoney();
								}
								else {
									//調整視窗
									editCount(false, true);
								}
							}
						});
					}
				}		
			});
		});  
	}
	
	//J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
	registEvent_editCount();
	
});

  })
});