/* 
 * C241M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C241M01A;

/** 覆審報告表主檔 **/
public interface C241M01ADao extends IGenericDao<C241M01A> {

	C241M01A findByOid(String oid);
	
	C241M01A findByMainId(String mainId);
	
	List<C241M01A> findByDocStatus(String docStatus);

	/**
	 * 用C240M01A mainId 刪除
	 * @param mainId
	 */
	void deleteByC240M01AMainid(String mainId);
}