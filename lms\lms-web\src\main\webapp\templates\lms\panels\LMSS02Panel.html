<html xmlns="http://www.w3.org/1999/xhtml"  xmlns:th="http://www.thymeleaf.org">
		<!--<script type="text/javascript">
			if(responseJSON.docType == "1"){
				alert(1);
				var Fname = "pagejs/lms/LMS1205S02Page.js";
				<!--document.write('<script type="text/javascript" src="pagejs/lms/LMS1205S02Page.js"><\/script>');
				document.write('<script type="text\/javascript" src="pagejs\/lms\/LMS1205S02Page.js"><\/script>');
			}else{
				alert("test");
			}
		</script>-->
<body>
	<th:block th:fragment="panelFragmentBody">
		<script type="text/javascript">
			loadScript('pagejs/lms/LMSS02Page01');
		</script>
		<fieldset>
			<legend>
				<b><th:block th:text="#{'l120s01a.subtitle1'}">借款人明細</th:block></b>
			</legend>
			<div id="bowbtn" class="funcContainer">
				&nbsp;&nbsp;
				<button type="button"
					onclick="openList('thickboxaddborrow','新增借款人',500,240);">
					<span class="text-only"><th:block th:text="#{'l120s01a.btn1'}">新增借款人</th:block></span>
				</button>
			</div>
			<div id="l120s01agrid" width="100%"
				style="margin-left: 10px; margin-right: 10px">
			</div>
		</fieldset>
		<div id="thickboxaddborrow" style="display: none;">
			<form id="addborrow">
				<!--<table class="A-creditChecking-tbl01 tbl09">-->
				<table class="tb2" width="100%" border="0" cellspacing="0"
					cellpadding="0">
					<tr style="display:none;">
						<td class="hd1"><th:block th:text="#{'l120s01a.doctype'}">企/個金</th:block>&nbsp;&nbsp;</td>
						<td><label><input name="docType" class="max" maxlength="1"
							type="radio" value="1" /><th:block th:text="#{'l120s01a.radio1'}">企業戶</th:block></label><label><input name="docType" class="max"
							maxlength="1" type="radio" value="2" /><th:block th:text="#{'l120s01a.radio2'}">個人戶</th:block></label>
						</td>
					</tr>
					<tr><input type="hidden" id="buscd" name="buscd"/></tr>
					<tr style="display:none;">
						<td width="20%" class="hd1"><th:block th:text="#{'l120s01a.typcd'}">客戶型態(區部別)</th:block>&nbsp;&nbsp;
						</td>
						<td width="80%"><label><input name="typCd" class="max" maxlength="1" type="radio"
							value="1" /><th:block th:text="#{'l120s01a.radio3'}">DBU 客戶</th:block></label><label><input name="typCd" class="max" maxlength="1"
							type="radio" value="4" /><th:block th:text="#{'l120s01a.radio4'}">OBU 客戶</th:block></label><label><input name="typCd" class="max"
							maxlength="1" type="radio" value="5" /><th:block th:text="#{'l120s01a.radio5'}">海外同業</th:block></label><label><input name="typCd"
							class="max" maxlength="1" type="radio" value="5" checked="checked" /><th:block th:text="#{'l120s01a.radio6'}">海外客戶</th:block></label><label><input
							name="typCd" class="max" maxlength="1" type="radio" value="0"
							checked style="display: none;" /></label>
						</td>
					</tr>
					<tr>
						<td width="20%" class="hd1">
							<input type="radio" name="rborrow" checked="checked" value="1" onclick="$('._rborrow1').prop('disabled',false);$('._rborrow2').val('').prop('disabled',true);"/>
							<th:block th:text="#{'l120s01a.custid'}">身分證統編</th:block>&nbsp;&nbsp;						
						</td>
						<td width="80%">
							<input id="custId" name="custId"
							class="max upText _rborrow1" size="10" maxlength="10" />
							&nbsp;&nbsp; 
							<input id="dupNo" name="dupNo" class="max" size="1" maxlength="1" disabled="true" />
							&nbsp;&nbsp;
							<button type="button" id="getCustData" name="getCustData">
							<span class="text-only"><th:block th:text="#{'l120s01a.btn2'}">引進</th:block></span>
							</button>
						</td>
					</tr>
					<tr>
						<td width="20%" class="hd1">
							<input type="radio" name="rborrow" value="2" onclick="$('._rborrow1').val('').prop('disabled',true);$('._rborrow2').prop('disabled',false);"/>
							<th:block th:text="#{'l120s01a.custname'}">借款人姓名</th:block>&nbsp;&nbsp;
						</td>
						<td width="80%">
							<input type="text" class="max _rborrow2" id="custName" name="custName"
								maxlength="120" disabled="true" /> &nbsp;&nbsp;
							<!--
							<button type="button">
								<span class="text-only"><th:block th:text="#{'l120s01a.btn3'}">取號</th:block></span>
							</button>
							-->
						</td>
					</tr>
					<tr>
						<td colspan="2" class="text-red">
							<th:block th:utext="#{'l120s01a.other16'}">說明...</th:block>							
						</td>
					</tr>
				</table>
			</form>
		</div>
		<div id="openDocaddborrow" style="display: none;">
			<div id="lmss02_panel" openFlag="true" th:insert="~{ lms/panels/LMSS02_Panel :: panelFragmentBody }"></div>
		</div>
	</th:block>
</body>
</html>
