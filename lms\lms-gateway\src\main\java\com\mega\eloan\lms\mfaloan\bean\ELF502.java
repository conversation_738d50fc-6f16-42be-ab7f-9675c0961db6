/* 
 * ELF502.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON>., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.wicket.markup.html.form.Check;

import tw.com.iisi.cap.model.GenericBean;

/** 消金轉貸資料介面檔 **/

public class ELF502 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/**
	 * 借款人身分證統一編號
	 * <p/>
	 * LNF135
	 */
	@Size(max = 10)
	@Column(name = "ELF502_CUSTID", length = 10, columnDefinition = "CHAR(10)")
	private String elf502_custid;

	/**
	 * 重複序號
	 * <p/>
	 * LNF135
	 */
	@Size(max = 1)
	@Column(name = "ELF502_DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String elf502_dupno;

	/**
	 * 本件額度序號
	 * <p/>
	 * LNF135
	 */
	@Size(max = 12)
	@Column(name = "ELF502_CNTRNO", length = 12, columnDefinition = "CHAR(12)",unique = true)
	private String elf502_cntrno;

	/**
	 * 案件序號
	 * <p/>
	 * LNF135
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF502_SEQ_NO", columnDefinition = "DECIMAL(03,0)",unique = true)
	private Integer elf502_seq_no;

	/**
	 * 轉貸日期
	 * <p/>
	 * LNF135
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF502_CHGDATE", columnDefinition = "DATE",unique = true)
	private Date elf502_chgdate;

	/**
	 * 轉入之原金融機構代碼
	 * <p/>
	 * LNF135
	 */
	@Size(max = 7)
	@Column(name = "ELF502_ORIBANCD", length = 7, columnDefinition = "CHAR(7)",unique = true)
	private String elf502_oribancd;

	/**
	 * 轉貸剩餘貸款本金
	 * <p/>
	 * LNF135
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF502_CHGLNBAL", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf502_chglnbal;

	/**
	 * 轉貸原貸款貸放日期
	 * <p/>
	 * LNF135
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF502_ORI_B_DATE", columnDefinition = "DATE")
	private Date elf502_ori_b_date;

	/**
	 * 原貸款到期日
	 * <p/>
	 * LNF135
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF502_ORIPDATE", columnDefinition = "DATE")
	private Date elf502_oripdate;

	/**
	 * 轉貸前後是否相同政策貸款
	 * <p/>
	 * LNF135
	 */
	@Size(max = 1)
	@Column(name = "ELF502_SAME_POLICY", length = 1, columnDefinition = "CHAR(01)")
	private String elf502_same_policy;

	/**
	 * 代償他行房貸原因
	 * <p/>
	 * LNF135
	 */
	@Size(max = 1)
	@Column(name = "ELF502_CHGREASON", length = 1, columnDefinition = "CHAR(01)")
	private String elf502_chgreason;

	/**
	 * 代償原因－其他
	 * <p/>
	 * LNF135
	 */
	@Size(max = 10)
	@Column(name = "ELF502_CHGMEMO", length = 10, columnDefinition = "GRAPHIC(10)")
	private String elf502_chgmemo;

	/**
	 * 取得借款人身分證統一編號
	 * <p/>
	 * LNF135
	 */
	public String getElf502_custid() {
		return this.elf502_custid;
	}

	/**
	 * 設定借款人身分證統一編號
	 * <p/>
	 * LNF135
	 **/
	public void setElf502_custid(String value) {
		this.elf502_custid = value;
	}

	/**
	 * 取得重複序號
	 * <p/>
	 * LNF135
	 */
	public String getElf502_dupno() {
		return this.elf502_dupno;
	}

	/**
	 * 設定重複序號
	 * <p/>
	 * LNF135
	 **/
	public void setElf502_dupno(String value) {
		this.elf502_dupno = value;
	}

	/**
	 * 取得本件額度序號
	 * <p/>
	 * LNF135
	 */
	public String getElf502_cntrno() {
		return this.elf502_cntrno;
	}

	/**
	 * 設定本件額度序號
	 * <p/>
	 * LNF135
	 **/
	public void setElf502_cntrno(String value) {
		this.elf502_cntrno = value;
	}

	/**
	 * 取得案件序號
	 * <p/>
	 * LNF135
	 */
	public Integer getElf502_seq_no() {
		return this.elf502_seq_no;
	}

	/**
	 * 設定案件序號
	 * <p/>
	 * LNF135
	 **/
	public void setElf502_seq_no(Integer value) {
		this.elf502_seq_no = value;
	}

	/**
	 * 取得轉貸日期
	 * <p/>
	 * LNF135
	 */
	public Date getElf502_chgdate() {
		return this.elf502_chgdate;
	}

	/**
	 * 設定轉貸日期
	 * <p/>
	 * LNF135
	 **/
	public void setElf502_chgdate(Date value) {
		this.elf502_chgdate = value;
	}

	/**
	 * 取得轉入之原金融機構代碼
	 * <p/>
	 * LNF135
	 */
	public String getElf502_oribancd() {
		return this.elf502_oribancd;
	}

	/**
	 * 設定轉入之原金融機構代碼
	 * <p/>
	 * LNF135
	 **/
	public void setElf502_oribancd(String value) {
		this.elf502_oribancd = value;
	}

	/**
	 * 取得轉貸剩餘貸款本金
	 * <p/>
	 * LNF135
	 */
	public BigDecimal getElf502_chglnbal() {
		return this.elf502_chglnbal;
	}

	/**
	 * 設定轉貸剩餘貸款本金
	 * <p/>
	 * LNF135
	 **/
	public void setElf502_chglnbal(BigDecimal value) {
		this.elf502_chglnbal = value;
	}

	/**
	 * 取得轉貸原貸款貸放日期
	 * <p/>
	 * LNF135
	 */
	public Date getElf502_ori_b_date() {
		return this.elf502_ori_b_date;
	}

	/**
	 * 設定轉貸原貸款貸放日期
	 * <p/>
	 * LNF135
	 **/
	public void setElf502_ori_b_date(Date value) {
		this.elf502_ori_b_date = value;
	}

	/**
	 * 取得原貸款到期日
	 * <p/>
	 * LNF135
	 */
	public Date getElf502_oripdate() {
		return this.elf502_oripdate;
	}

	/**
	 * 設定原貸款到期日
	 * <p/>
	 * LNF135
	 **/
	public void setElf502_oripdate(Date value) {
		this.elf502_oripdate = value;
	}

	/**
	 * 取得轉貸前後是否相同政策貸款
	 * <p/>
	 * LNF135
	 */
	public String getElf502_same_policy() {
		return this.elf502_same_policy;
	}

	/**
	 * 設定轉貸前後是否相同政策貸款
	 * <p/>
	 * LNF135
	 **/
	public void setElf502_same_policy(String value) {
		this.elf502_same_policy = value;
	}

	/**
	 * 取得代償他行房貸原因
	 * <p/>
	 * LNF135
	 */
	public String getElf502_chgreason() {
		return this.elf502_chgreason;
	}

	/**
	 * 設定代償他行房貸原因
	 * <p/>
	 * LNF135
	 **/
	public void setElf502_chgreason(String value) {
		this.elf502_chgreason = value;
	}

	/**
	 * 取得代償原因－其他
	 * <p/>
	 * LNF135
	 */
	public String getElf502_chgmemo() {
		return this.elf502_chgmemo;
	}

	/**
	 * 設定代償原因－其他
	 * <p/>
	 * LNF135
	 **/
	public void setElf502_chgmemo(String value) {
		this.elf502_chgmemo = value;
	}
}
