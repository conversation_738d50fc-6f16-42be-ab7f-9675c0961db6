/*
 * AjaxHandlerPlugin.java
 *  
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System, Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.plugin;

import java.text.MessageFormat;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.text.StrSubstitutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.IResult;

/**
 * <pre>
 * abstract class AjaxHandlerPlugin implements ICapPlugin. 
 * 前端回傳資料處理
 * </pre>
 *
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/16,iristu,modify
 *          <li>2010/10/13,iristu,add i18n method
 *          <li>2010/3/16,RodesChen,add logger
 *          </ul>
 */
public abstract class AjaxHandlerPlugin implements ICapPlugin {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private MessageSource messageSource;

    /**
     * 執行
     * 
     * @param params
     *            Client 參數
     * @param parent
     *            Component
     * @return
     */
    public abstract IResult execute(PageParameters params) throws CapException;

    /**
     * Request 參數
     */
    PageParameters params;

    /**
     * 取得參數
     * 
     * @return {@code params}
     */
    public PageParameters getParameters() {
        return params;
    }

    /**
     * 設置參數
     * 
     * @param params
     *            Request 參數
     */
    protected void setParameters(PageParameters params) {
        this.params = params;
    }

    /**
     * 取得i18n的訊息字串
     * 
     * @param key
     *            the i18n key
     * @return String
     */
    public String getMessage(String key) {
        return getMessage(key, null, LocaleContextHolder.getLocale());
    }// ;

    /**
     * 取得i18n的訊息字串
     * 
     * @param key
     *            the i18n key
     * @param params
     *            the parameters
     * @return String
     */
    public String getMessage(String key, Object[] params, Locale locale) {
        return messageSource.getMessage(key, params, locale);
    }// ;

    /**
     * 取得i18n的字串 loginUser=Are you $\{userName\}?
     * 
     * key:loginUser params:{userName=John}
     * 
     * return-->Are you John?
     * 
     * @param key
     *            the key
     * @param params
     *            the parameters
     * @return String
     */
    public String getMessage(String key, Object[] params) {
        return getMessage(key, params, LocaleContextHolder.getLocale());
    }// ;

    /**
     * 取得i18n的字串 loginUser=Are you $\{userName\}?
     * 
     * @param key
     *            the key
     * @param map
     *            the map
     * @return
     */
    public String getMessage(String key, Map<String, String> map) {
        return getMessage(key, null, map);
    }

    /**
     * 取得i18n的字串 loginUser=Are you $\{userName\}?
     * 
     * @param key
     *            the key
     * @param params
     *            the parameters
     * @param map
     *            the map
     * @return
     */
    public String getMessage(String key, Object[] params, Map<String, String> map) {
        return getMessage(key, params, map, LocaleContextHolder.getLocale());
    }

    /**
     * 取得i18n的字串 loginUser=Are you $\{userName\}?
     * 
     * @param key
     *            the key
     * @param params
     *            the parameters
     * @param map
     *            the map
     * @param locale
     *            the locale
     * @return
     */
    public String getMessage(String key, Object[] params, Map<String, String> map, Locale locale) {
        String msg = getMessage(key, null, locale);
        if (StringUtils.isNotEmpty(msg) && !key.equals(msg)) {
            if (map != null) {
                msg = msg.replace("$\\{", "${").replace("\\}", "}");
                msg = StrSubstitutor.replace(msg, map, "${", "}");
            }
            if (ArrayUtils.isNotEmpty(params)) {
                msg = new MessageFormat(msg).format(params);
            }
        }
        return msg;
    }

    /**
     * 根據傳入的key從Properties取得i18n的字串
     * 
     * @param key
     *            the i18n key
     * @param props
     *            the i18n Properties
     * @return
     */
    public String getMessageByProperties(String key, Properties props) {
        return getMessageByProperties(key, props, null, null);
    }

    /**
     * 根據傳入的key從Properties取得i18n的字串
     * 
     * @param key
     *            the i18n key
     * @param props
     *            the i18n Properties
     * @param params
     *            the parameters
     * @return
     */
    public String getMessageByProperties(String key, Properties props, Object[] params) {
        return getMessageByProperties(key, props, params, null);
    }

    /**
     * 根據傳入的key從Properties取得i18n的字串
     * 
     * @param key
     *            the i18n key
     * @param props
     *            the i18n Properties
     * @param map
     *            the map
     * @return
     */
    public String getMessageByProperties(String key, Properties props, Map<String, String> map) {
        return getMessageByProperties(key, props, null, map);
    }

    /**
     * 根據傳入的key從Properties取得i18n的字串
     * 
     * @param key
     *            the i18n key
     * @param props
     *            the i18n Properties
     * @param params
     *            the parameters
     * @param map
     *            the map
     * @return
     */
    public String getMessageByProperties(String key, Properties props, Object[] params, Map<String, String> map) {
        String msg = key;
        if (props != null && !props.isEmpty()) {
            msg = props.getProperty(key);
            if (map != null) {
                msg = msg.replace("$\\{", "${").replace("\\}", "}");
                msg = StrSubstitutor.replace(msg, map, "${", "}");
            }
            if (ArrayUtils.isNotEmpty(params)) {
                msg = new MessageFormat(msg).format(params);
            }
        }
        return msg;
    }

    /*
     * 取得插件名稱
     * 
     * @see tw.com.iisi.cap.plugin.ICapPlugin#getPluginName()
     */
    @Override
    public String getPluginName() {
        return this.getClass().getSimpleName();
    }

}
