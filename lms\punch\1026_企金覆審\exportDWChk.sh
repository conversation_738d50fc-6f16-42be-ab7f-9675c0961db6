#!/bin/ksh
# Joe Lin 100/09/14
################################################################

# The following three lines have been added by UDB DB2.
if [ -f /db2inst1/.profile ]; then
   . /db2inst1/.profile
fi


PROG="exportDWChk.sh"

DB=$1
DBID=$2
DBPSW=$3
DBSCHEME=$4
FH=$5
LOG=$6
CHKFILE=$7
CHK=0
EXPDW=expDWChk.sql
echo  "** " $PROG "  開始執行時間 : " `date`  >>  $LOG
db2 connect to $DB user $DBID using $DBPSW  >> $LOG
if [[ -e $FH ]]
then

  while read CHKLINE
  do
    if [[ $CHKLINE != "" ]]
    then
      cat expDWCheck.sql | sed -e 's/TABLENAME/'$CHKLINE'/g' | sed -e 's/DBNAME/'$DBSCHEME'/g' > $EXPDW
      db2 -vtf $EXPDW  >> $LOG
      NUM=`db2 -vtf $EXPDW`
      NUM=`echo $NUM | awk '{print $27}'`
      echo $CHKLINE  " " $NUM  >> $CHKFILE
      echo $CHKLINE  " record number is " $NUM  >> $LOG  
      if [[ $NUM == "0" ]]
      then
        CHK=10
      fi
     # rm $EXPDW
    fi
  done < $FH
fi
db2 disconnect current  >> $LOG

echo  "** " $PROG " 執行完成時間 : " `date`  >>  $LOG  
echo "     "  >>  $LOG
echo "     "  >>  $LOG

exit $CHK
