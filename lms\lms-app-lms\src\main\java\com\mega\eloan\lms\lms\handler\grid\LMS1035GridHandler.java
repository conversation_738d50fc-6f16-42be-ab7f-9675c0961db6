package com.mega.eloan.lms.lms.handler.grid;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;
import javax.persistence.criteria.JoinType;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.lms.pages.LMS1035M01Page;
import com.mega.eloan.lms.lms.pages.LMS1205M01Page;
import com.mega.eloan.lms.lms.service.LMS1035Service;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 消金信用評等模型
 * </pre>
 * 
 * @since 2017/2/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/2/1,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1035gridhandler")
public class LMS1035GridHandler extends AbstractGridHandler {

	@Resource
	CLSService clsService;

	@Resource
	RetrialService retrialService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	DocFileService docFileService;

	@Resource
	LMS1035Service lms1035Service;
	
	public CapGridResult queryC121M01A(ISearch pageSetting, PageParameters params) 
	throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String search_custId = Util.trim(params.getString("custId"));
		if (Util.isNotEmpty(search_custId)){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					search_custId);
		}
		Properties prop_lms1035m01 = MessageBundleScriptCreator.getComponentResource(LMS1035M01Page.class);
		String prop_lnYearMonth = prop_lms1035m01.getProperty("tab01.lnYearMonth");
		
		String docStatus = Util.trim(params.getString(EloanConstants.DOC_STATUS));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);
		if(true){
			pageSetting.addSearchModeParameters(SearchMode.OR
					, new SearchModeParameter(SearchMode.EQUALS, "ownBrId", user.getUnitNo())
					, new SearchModeParameter(SearchMode.EQUALS, "c121a01a.authUnit", user.getUnitNo(), JoinType.LEFT)
			);				
		}
		
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "docStatus", OverSeaUtil.C121M01A_IMPDOCSTATUS);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		
		Page<? extends GenericBean> page = clsService.findPage(C121M01A.class, pageSetting);		
		
		@SuppressWarnings("unchecked")
		List<C121M01A> list = (List<C121M01A>) page.getContent();
		for (C121M01A model : list) {
			set_ln_period(model, prop_lnYearMonth);
			
			if(true){
				C121S01A c121s01a = clsService.findC121S01A(model);
				if(c121s01a!=null){
					model.setCmsLocation(c121s01a.getLocation());	
				}					
			}			
		}
		
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("updater", new UserNameFormatter(userInfoService));
		return new CapGridResult(page.getContent(), page.getTotalRow(), formatter);		
	}
	
	private void set_ln_period(C121M01A model, String prop_lnYearMonth ){
		if(true){
			String lnPeriod = "";
			if(model.getLnYear()!=null || model.getLnMonth()!=null){
				lnPeriod = MessageFormat.format(prop_lnYearMonth
						, Util.trim(model.getLnYear())
						, Util.trim(model.getLnMonth()) );	
			}
			model.setLnPeriod(lnPeriod);	
		}
	}
	
	public CapGridResult queryC120M01A(ISearch pageSetting, PageParameters params) 
	throws CapException {
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.MAIN_ID, Util.trim(params.getString(EloanConstants.MAIN_ID)));		
		Page<? extends GenericBean> page = clsService.findPage(C120M01A.class,pageSetting);	
		
		@SuppressWarnings("unchecked")
		List<C120M01A> list = (List<C120M01A>) page.getContent();
		Map<String, String> custPosMap = retrialService.get_codeTypeWithOrder("lms1015_custPos");
		
		String[] codeType = { UtilConstants.CodeTypeItem.企業關係,
				UtilConstants.CodeTypeItem.親屬關係,
				UtilConstants.CodeTypeItem.綜合關係_企業,
				UtilConstants.CodeTypeItem.綜合關係_親屬 };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService.findByCodeType(codeType);
		
		for (C120M01A model : list) {
			
			boolean isKeyMan = Util.equals("Y", model.getKeyMan());
			if (UtilConstants.DEFAULT.是.equals(model.getO_chkYN())) {
				model.setO_chkYN("V");
			} else {
				model.setO_chkYN("X");
			}
			
			if (isKeyMan) {
				model.setKeyMan("*");
			} else {
				model.setKeyMan("");
			}
			String custPos = isKeyMan?OverSeaUtil.M:Util.trim(model.getCustPos());			
			if(Util.isNotEmpty(custPos)){
				String custPosDesc = LMSUtil.getDesc(custPosMap, custPos);
				if(Util.notEquals(custPos, custPosDesc)){
					model.setCustPos(custPos+"."+custPosDesc);	
				}	
			}
			
			String o_custRlt = Util.trim(model.getO_custRlt());			
			if(Util.isNotEmpty(o_custRlt)){				
				model.setO_custRlt(LMSUtil.changeCustRlt(o_custRlt, codeMap));
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());		
	}
	
	public CapGridResult queryMateInCase(ISearch pageSetting, PageParameters params) 
	throws CapException {
		String c120m01a_oid = Util.trim(params.getString("oid"));
		C120M01A c120m01a = clsService.findC120M01A_oid(c120m01a_oid);
		String mainId = "";
		if(c120m01a!=null){
			mainId = c120m01a.getMainId();
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//排除自己
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "oid", c120m01a_oid);

		pageSetting.addOrderBy("keyMan", true);
		pageSetting.addOrderBy("custId", false);
		pageSetting.addOrderBy("dupNo", false);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = clsService.findPage(C120M01A.class,
				pageSetting);

		String[] codeType = { UtilConstants.CodeTypeItem.企業關係,
				UtilConstants.CodeTypeItem.親屬關係,
				UtilConstants.CodeTypeItem.綜合關係_企業,
				UtilConstants.CodeTypeItem.綜合關係_親屬 };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService.findByCodeType(codeType);
		
		@SuppressWarnings("unchecked")
		List<C120M01A> list = (List<C120M01A>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			C120M01A model = list.get(i);
			model.setCustPos(findCustPos(model));
			
			String o_custRlt = Util.trim(model.getO_custRlt());
			if (UtilConstants.DEFAULT.是.equals(model.getO_chkYN())) {
				model.setO_chkYN("V");
			} else {
				model.setO_chkYN("X");
			}
			if ("Y".equals(model.getKeyMan())) {
				model.setKeyMan("*");
			} else {
				model.setKeyMan("");
			}
			if (Util.isNotEmpty(o_custRlt)) {
				model.setO_custRlt(LMSUtil.changeCustRlt(o_custRlt, codeMap));			
			}
		}
		return new CapGridResult(list, page.getTotalRow());
	}
	
	private String findCustPos(C120M01A model) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205M01Page.class);
		if (Util.trim(model.getCustPos()).length() != 0) {
			switch (model.getCustPos().toCharArray()[0]) {
			case 'C':
				return pop.getProperty("L1205G.grid4");
			case 'D':
				return pop.getProperty("L1205G.grid5");
			case 'G':
				return pop.getProperty("L1205G.grid6");
			case 'N':
				return pop.getProperty("L1205G.grid7");
			case 'S':
				return pop.getProperty("L1205G.grid8");
			default:
				return "";
			}
		}
		return "";
	}
	
	/**
	 * 查 評等級數總表
	 */
	public CapGridResult queryRatingTbl(ISearch pageSetting,
			PageParameters params) throws CapException {

		String mainId = CapString.trimNull(params.getString("mainId"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "sysId", "LMS");
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		pageSetting.addOrderBy("fieldId");
		// 取得資料
		Page<DocFile> page = docFileService.readToGrid(pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		return result;
	}
	
	public CapGridResult queryBaseData(ISearch pageSetting,
			PageParameters params) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String custId = Util.trim(params.getString("search_custId"));
		if (Util.isNotEmpty(custId)){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		String custName = Util.trim(params.getString("search_custName"));
		if(Util.isNotEmpty(custName)){
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custName",
					"%"+custName+"%");
		}		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "o_single", "Y");

		Page<? extends GenericBean> page = clsService.findPage(C120M01A.class,
				pageSetting);
		
		@SuppressWarnings("unchecked")
		List<C120M01A> list = (List<C120M01A>) page.getContent();
		
		List<String> mainId_list = new ArrayList<String>();
		for (C120M01A model : list) {
			mainId_list.add(model.getMainId());
		}
		List<C120S01E> c120s01e_list = clsService.findC120S01E_mainId_list(mainId_list);
		Map<String, C120S01E> mainId_C120S01E_map = new HashMap<String, C120S01E>();
		for(C120S01E c120s01e: c120s01e_list){
			mainId_C120S01E_map.put(c120s01e.getMainId() , c120s01e);
		}
		
		for (C120M01A model : list) {
			
			C120S01E c120s01e = mainId_C120S01E_map.get(model.getMainId());
			model.setNcbQDate(c120s01e==null?null:c120s01e.getNcbQDate());
			model.setEJcicQDate(c120s01e==null?null:c120s01e.getEJcicQDate());
			model.setEChkQDate(c120s01e==null?null:c120s01e.getEChkQDate());
			
			/* XXX
			若把該 model 透過 Service 再串到其它的 model
			EX: xxxService.findXXX(model);
			
			又在這裡寫 model.setO_chkYN 
			會 overwrite DB 中的值 ─────→ 改用 IFormatter
			*/
		}
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("updater", new UserNameFormatter(userInfoService));		
		formatter.put("o_chkYN", new LMS1035Grid_ChkYNFormatter());
		return new CapGridResult(page.getContent(), page.getTotalRow(), formatter);
	}
	
	public CapMapGridResult queryFromBaseData_adv(ISearch pageSetting,
			PageParameters params) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		if(true){
			ISearch mySearch = clsService.getMetaSearch();
			String custId = Util.trim(params.getString("search_custId"));
			if (Util.isNotEmpty(custId)){
				mySearch.addSearchModeParameters(SearchMode.LIKE, "custId",
						custId+"%");
			}
			String custName = Util.trim(params.getString("search_custName"));
			if(Util.isNotEmpty(custName)){
				mySearch.addSearchModeParameters(SearchMode.LIKE, "custName",
						custName+"%");
			}		
			mySearch.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					user.getUnitNo());
			mySearch.addSearchModeParameters(SearchMode.EQUALS, "o_single", "Y");
			mySearch.addSearchModeParameters(SearchMode.EQUALS, "o_chkYN", "Y");
			if(true){
				//套 orderBy
				mySearch.setOrderBy(pageSetting.getOrderBy());
			}
			mySearch.setMaxResults(Integer.MAX_VALUE);
							
			Page<? extends GenericBean> my_page = clsService.findPage(C120M01A.class,
					mySearch);
			
			@SuppressWarnings("unchecked")
			List<C120M01A> raw_list = (List<C120M01A>) my_page.getContent();
			List<String> mainId_list = new ArrayList<String>();
			for (C120M01A model : raw_list) {
				mainId_list.add(model.getMainId());
			}
			List<C120S01E> c120s01e_list = clsService.findC120S01E_mainId_list(mainId_list);
			Map<String, C120S01E> mainId_C120S01E_map = new HashMap<String, C120S01E>();
			for(C120S01E c120s01e: c120s01e_list){
				mainId_C120S01E_map.put(c120s01e.getMainId() , c120s01e);
			}
			
			for (C120M01A model : raw_list) {
				
				C120S01E c120s01e = mainId_C120S01E_map.get(model.getMainId());
				model.setNcbQDate(c120s01e==null?null:c120s01e.getNcbQDate());
				model.setEJcicQDate(c120s01e==null?null:c120s01e.getEJcicQDate());
				model.setEChkQDate(c120s01e==null?null:c120s01e.getEChkQDate());
			}
			
			if(true){
				//泰國檢核 ncbQDate 在3個月內		
				Date cmpDate = CapDate.addMonth(new Date(), -3);
				
				for (C120M01A model : raw_list) {
					Map<String, Object> row = new HashMap<String, Object>();
					if(model.getNcbQDate()!=null && LMSUtil.cmpDate(model.getNcbQDate(), "<", cmpDate)){
						continue;
					}
					LMSUtil.meta_to_map(row, model,
							new String[] { "oid", "mainId",
								"custId", "dupNo", "custName",
								"ncbQDate", "eJcicQDate", "eChkQDate", 
								"updater", "updateTime","o_chkYN" });
				
					list.add(row);
				}				
			}
		}
		
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("updater", new UserNameFormatter(userInfoService));		
		formatter.put("o_chkYN", new LMS1035Grid_ChkYNFormatter());
		
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);		
		return new CapMapGridResult(page.getContent(), page.getTotalRow(), formatter);		
	}
	
	public CapGridResult queryFromRatingDoc(ISearch pageSetting,
			PageParameters params) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus"
				, new String[]{CreditDocStatusEnum.海外_待覆核.getCode()
							, CreditDocStatusEnum.海外_已核准.getCode()
				});
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());		
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		String custId = Util.trim(params.getString("search_custId"));
		if (Util.isNotEmpty(custId)){
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId",
					custId+"%");
		}		
		Properties prop_lms1035m01 = MessageBundleScriptCreator.getComponentResource(LMS1035M01Page.class);
		String prop_lnYearMonth = prop_lms1035m01.getProperty("tab01.lnYearMonth");
		
		Page<? extends GenericBean> page = clsService.findPage(C121M01A.class, pageSetting);		
		
		@SuppressWarnings("unchecked")
		List<C121M01A> list = (List<C121M01A>) page.getContent();
		for (C121M01A model : list) {
			set_ln_period(model, prop_lnYearMonth);

			if(true){
				C121S01A c121s01a = clsService.findC121S01A(model);
				if(c121s01a!=null){
					model.setCmsLocation(c121s01a.getLocation());	
				}					
			}
		}
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("updater", new UserNameFormatter(userInfoService));
		return new CapGridResult(page.getContent(), page.getTotalRow(), formatter);
	}
	
	public CapMapGridResult queryPrint(ISearch pageSetting,
			PageParameters params) throws CapException {
		
		String mainId = Util.nullToSpace(params.getString("mainId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.setDistinct(true);
		Page<Map<String, Object>> page = lms1035Service.queryPrint(mainId, pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
	
	/**
	 * 查詢
	 */
	public CapGridResult queryModelCmp(ISearch pageSetting,
			PageParameters params) throws CapException {

		String mainId = CapString.trimNull(params.getString("mainId"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "sysId", "LMS");
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		
		// 取得資料
		Page<DocFile> page = docFileService.readToGrid(pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		return result;
	}
	
	class LMS1035Grid_ChkYNFormatter implements IFormatter {
		
		private static final long serialVersionUID = -8311213843883425077L;

		@SuppressWarnings("unchecked")
		@Override
		public String reformat(Object in) throws CapFormatException {
			return Util.equals("Y", in)?"V":"X";
		}
	}
	
}