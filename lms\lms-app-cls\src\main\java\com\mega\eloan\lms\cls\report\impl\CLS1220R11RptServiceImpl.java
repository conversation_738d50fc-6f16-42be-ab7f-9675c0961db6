package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LmsExcelUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.pages.CLS1220V10Page;
import com.mega.eloan.lms.cls.report.CLS1220R11RptService;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.dao.C101S01YDao;
import com.mega.eloan.lms.dao.C120S01YDao;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.eloan.lms.dao.C122S01HDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01YDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C101S01Y;
import com.mega.eloan.lms.model.C120S01Y;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01F;
import com.mega.eloan.lms.model.C122S01G;
import com.mega.eloan.lms.model.C122S01H;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01Y;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * 中鋼團體消貸控制檔XLS
 */
@Service("cls1220r11rptservice")
public class CLS1220R11RptServiceImpl implements FileDownloadService,
		CLS1220R11RptService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1220R11RptServiceImpl.class);

	@Resource
	BranchService branchService;
	@Resource
	EloandbBASEService eloandbBASEService;
	@Resource
	UserInfoService userInfoService;
	@Resource
	CLS1220Service service;
	@Resource
	CodeTypeService codeTypeService;
	@Resource
	C122M01ADao c122m01adao;
	@Resource
	C122S01HDao c122s01hdao;
	@Resource
	C101S01YDao c101s01ydao;
	@Resource
	L140M01YDao l140m01ydao;
	@Resource
	C120S01YDao c120s01ydao;
	@Resource
	L140M01ADao l140m01adao;

	@Override
	public byte[] getContent(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateXls(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	private ByteArrayOutputStream generateXls(PageParameters params)
			throws IOException, Exception {

		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		genXls(outputStream, params);

		if (outputStream != null) {
			outputStream.flush();
		}
		return outputStream;
	}

	private void genXls(ByteArrayOutputStream outputStream,
			PageParameters params) throws IOException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		HSSFWorkbook workbook = null;
		HSSFSheet sheet1 = null;

		Properties prop_cls1220v10 = MessageBundleScriptCreator
				.getComponentResource(CLS1220V10Page.class);
		String custId = Util.trim(params.getString("custId"));
		String ploanCaseId = Util.trim(params.getString("ploanCaseId"));
		String applyKind = Util.trim(params.getString("applyKind"));
		String incomType = Util.trim(params.getString("incomType"));
		String applyTS_beg_date = Util.trim(params.getString("applyTS_beg"));
		String applyTS_end_date = Util.trim(params.getString("applyTS_end"));
		String SdocStatus = Util.trim(params.getString("SdocStatus"));
		String SignMegaEmp = Util.trim(params.getString("SignMegaEmp"));
		String ploanPlan = Util.trim(params.getString("ploanPlan"));
		String purchaseHouse = Util.trim(params.getString("purchaseHouse"));
		
		if (Util.isEmpty(SdocStatus)) { // 未選擇，抓出所有
		} else {
			SdocStatus = SdocStatus + "%";
		}
		ISearch search = c122m01adao.createSearchTemplete();
		search.setMaxResults(Integer.MAX_VALUE);
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		//943、900可抓取所有分行資料
		if(Util.notEquals(ownBrId, "943") && Util.notEquals(ownBrId, "900")){
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		}
		
		if (Util.notEquals(custId, "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		}
		if (Util.notEquals(ploanCaseId, "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ploanCaseId",
					ploanCaseId);
		}
		if (Util.notEquals(applyKind, "")) {
			if (Util.equals(applyKind, "B")) {
				search.addSearchModeParameters(SearchMode.OR,
						new SearchModeParameter(SearchMode.EQUALS, "applyKind","B"), 
						new SearchModeParameter(SearchMode.EQUALS, "applyKind","D"));
			} else if (Util.equals(applyKind, "E")) {
//				search.addSearchModeParameters(SearchMode.OR,
//						new SearchModeParameter(SearchMode.EQUALS, "applyKind","E"), 
//						new SearchModeParameter(SearchMode.EQUALS, "applyKind","F"));
				search.addSearchModeParameters(SearchMode.EQUALS, "applyKind",UtilConstants.C122_ApplyKind.E);
			} else if (Util.equals(applyKind, "P")) {
//				search.addSearchModeParameters(SearchMode.OR,
//						new SearchModeParameter(SearchMode.EQUALS, "applyKind","P"), 
//						new SearchModeParameter(SearchMode.EQUALS, "applyKind","Q"));
				search.addSearchModeParameters(SearchMode.EQUALS, "applyKind",UtilConstants.C122_ApplyKind.P);
			} else if (Util.equals(applyKind, "C")
					|| Util.equals(applyKind, "H")) {
				search.addSearchModeParameters(SearchMode.OR,
						new SearchModeParameter(SearchMode.EQUALS, "applyKind","C"), 
						new SearchModeParameter(SearchMode.EQUALS, "applyKind","H"));
//				search.addSearchModeParameters(SearchMode.EQUALS, "applyKind",applyKind);
			} else if(Util.equals(applyKind, "I")){
				search.addSearchModeParameters(SearchMode.EQUALS, "applyKind",UtilConstants.C122_ApplyKind.I);
			} else if(Util.equals(applyKind, "J")){
				search.addSearchModeParameters(SearchMode.EQUALS, "applyKind",UtilConstants.C122_ApplyKind.J);
			} else if(Util.equals(applyKind, "O")){
				search.addSearchModeParameters(SearchMode.EQUALS, "applyKind",UtilConstants.C122_ApplyKind.O);
			}
		}else{
			String[] applyKindArray = new String[] { "B", "D", "E", "P", "C","H" ,
					UtilConstants.C122_ApplyKind.I , UtilConstants.C122_ApplyKind.J,
					UtilConstants.C122_ApplyKind.O };
			search.addSearchModeParameters(SearchMode.IN, "applyKind",
					applyKindArray);
		}
		if (Util.notEquals(incomType, "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "IncomType",
					incomType);
		}

		if (Util.notEquals(applyTS_beg_date, "")) {
			String applyTS_beg = applyTS_beg_date + " 00:00:00";
			search.addSearchModeParameters(SearchMode.GREATER_EQUALS,
					"createTime", Util.parseDate(applyTS_beg));
		}

		if (Util.notEquals(applyTS_end_date, "")) {
			String applyTS_end = applyTS_end_date + " 24:00:00";
			search.addSearchModeParameters(SearchMode.LESS_EQUALS,
					"createTime", Util.parseDate(applyTS_end));
		}

		if (Util.notEquals(SdocStatus, "")) {
			search.addSearchModeParameters(SearchMode.LIKE, "docStatus",
					SdocStatus);
		}
		if (Util.notEquals(SignMegaEmp, "")) {
			search.addSearchModeParameters(SearchMode.EQUALS,
					"c122m01f.signMegaEmpNo", SignMegaEmp);
		}
		if(Util.notEquals(ploanPlan, "")){
			search.addSearchModeParameters(SearchMode.EQUALS, "ploanPlan", ploanPlan);		
		}
		
		if(Util.notEquals(purchaseHouse, "")){
			search.addSearchModeParameters(SearchMode.EQUALS, "purchaseHouse", purchaseHouse);		
		}

		search.addOrderBy("createTime");
		
		if (true) {
			// ---
			workbook = new HSSFWorkbook();
			sheet1 = workbook.createSheet("清單");

			// ===內容樣式===	
            HSSFFont headFont12 = workbook.createFont();
            {
            	headFont12.setFontName("標楷體");
                headFont12.setFontHeightInPoints((short)12);
            }
			
			HSSFCellStyle cellFormatL = workbook.createCellStyle();
			{
				cellFormatL.setFont(headFont12);
				cellFormatL.setAlignment(HorizontalAlignment.LEFT);
				cellFormatL.setWrapText(true);
			}
			
			HSSFCellStyle cellFormatL_Border = workbook.createCellStyle();
	        {
		        cellFormatL_Border.cloneStyleFrom(cellFormatL);
		        cellFormatL_Border.setBorderTop(BorderStyle.THIN);
		        cellFormatL_Border.setBorderBottom(BorderStyle.THIN);
		        cellFormatL_Border.setBorderLeft(BorderStyle.THIN);
		        cellFormatL_Border.setBorderRight(BorderStyle.THIN);
	        }
			// ===標題樣式===

	        HSSFFont headFontTitle = workbook.createFont();
			{
				headFontTitle.setFontName("標楷體");
				headFontTitle.setFontHeightInPoints((short)20);
				headFontTitle.setBold(true);
				headFontTitle.setItalic(false); 
				headFontTitle.setUnderline(HSSFFont.U_NONE); 
			}
			
			HSSFCellStyle cellFormatT = workbook.createCellStyle();
			{
				cellFormatT.setFont(headFontTitle);
				cellFormatT.setAlignment(HorizontalAlignment.CENTER);
				cellFormatT.setWrapText(true);
			}
			
			HSSFCellStyle cellFormatT_Border = workbook.createCellStyle();
	        {
	        	cellFormatT_Border.cloneStyleFrom(cellFormatL);
	        	cellFormatT_Border.setBorderTop(BorderStyle.THIN);
	        	cellFormatT_Border.setBorderBottom(BorderStyle.THIN);
	        	cellFormatT_Border.setBorderLeft(BorderStyle.THIN);
	        	cellFormatT_Border.setBorderRight(BorderStyle.THIN);
	        }
			
			// ======

			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();

			headerMap.put("案件編號", 25);
			headerMap.put("進件日期", 25);
			headerMap.put("進件類型", 15);
			headerMap.put("貸款種類", 15);
			headerMap.put("客戶統一編號", 20);
			headerMap.put("重覆序號", 5);
			headerMap.put("客戶名稱", 25);
			headerMap.put("申貸金額(萬元)", 25);
			headerMap.put("派案主管編號", 15);
			headerMap.put("派案主管姓名", 15);
			headerMap.put("派案時間", 25);
			headerMap.put("簽案行員編號", 15);
			headerMap.put("簽案行員姓名", 15);
			headerMap.put("估價人員行編", 15);
			headerMap.put("估價人員姓名", 15);
			headerMap.put("地政士姓名", 15);
			headerMap.put("地政士證書字號", 15);
			headerMap.put("分行代號", 15);
			headerMap.put("分行名稱", 15);
			headerMap.put("案件狀態", 15);
			headerMap.put("取消/不承作原因", 30);
			headerMap.put("行銷代碼", 30);
			headerMap.put("行銷方案", 30);
			headerMap.put("需重新指派案件", 20);

			int totalColSize = headerMap.size();
			List<String[]> rows = new ArrayList<String[]>();
			String brNo = user.getUnitNo();
			Map<String, String> cacheMap = new HashMap<String, String>();

			List<C122M01A> c122m01aList = service.getC122M01AList(search);
			
			for (int i = 0; i < c122m01aList.size(); i++) {

				C122M01A c122m01a = c122m01aList.get(i);
				C122M01F c122m01f = c122m01a.getC122m01f();
				if (c122m01f == null) {
					c122m01f = new C122M01F();
				}
				// ---
				String[] arr = new String[totalColSize];
				for (int i_col = 0; i_col < totalColSize; i_col++) {
					arr[i_col] = "";
				}
				if (Util.notEquals(Util.trim(c122m01a.getPloanCaseId()), "")) {
					arr[0] = Util.trim(c122m01a.getPloanCaseId()); // 案件編號
					arr[1] = Util.trim(TWNDate.toFullAD(c122m01a
							.getCreateTime())); // 進件日期
					String incomtype = Util.trim(c122m01a.getIncomType());
					arr[2] = "1".equals(incomtype) ? "人工進件" : "線上進件"; // 進件類型

					// B,D=勞工紓困、C=卡友信貸、H=房貸增貸、E,F=線上房貸、P,Q=線上信貸
					// I=青創一百萬以下、J=青創一百萬以上
					// 貸款種類
					String applykind = Util.trim(c122m01a.getApplyKind());
					if (Util.equals(applykind, "B")
							|| Util.equals(applykind, "D")
							|| Util.equals(applykind, "C")
							|| Util.equals(applykind, "H")
							|| Util.equals(applykind, UtilConstants.C122_ApplyKind.I)
							|| Util.equals(applykind, UtilConstants.C122_ApplyKind.J)) {
						arr[3] = prop_cls1220v10
								.getProperty("C122M01A.applyKind." + applykind);
					} else if (Util.equals(applykind, "E")
							|| Util.equals(applykind, "F")) {
						arr[3] = prop_cls1220v10
								.getProperty("C122M01A.applyKind.E2");
					} else if (Util.equals(applykind, "P")
							|| Util.equals(applykind, "Q")) {
						arr[3] = prop_cls1220v10
								.getProperty("C122M01A.applyKind.P2");
					} else if (Util.equals(applykind, "O")
							|| Util.equals(applykind, "R")) {
						arr[3] = prop_cls1220v10
								.getProperty("C122M01A.applyKind.O");
					} else {
						arr[3] = "";
					}

					arr[4] = Util.trim(c122m01a.getCustId()); // 客戶統一編號
					arr[5] = Util.trim(c122m01a.getDupNo()); // 重複序號
					arr[6] = Util.trim(c122m01a.getCustName()); // 客戶名稱

					arr[7] = Util.trim(c122m01a.getApplyAmt());// 申貸金額(萬元)

					String appover = Util.trim(c122m01a.getApprover());
					arr[8] = appover; // 派案主管編號
					arr[9] = Util.trim(userInfoService.getUserName(appover)); // 派案主管姓名
					arr[10] = Util.trim(TWNDate.toFullAD(c122m01a
							.getApproveTime()));// 派案時間
					arr[11] = Util.trim(c122m01f.getSignMegaEmpNo()); // 簽案行員編號
					arr[12] = Util.trim(c122m01f.getSignMegaEmpName()); // 簽案行員姓名

					arr[13] = Util.trim(c122m01f.getEvaMegaEmpNo()); // 估價人員行編
					arr[14] = Util.trim(c122m01f.getEvaMegaEmpName()); // 估價人員姓名

					String laaName = "";
					String laaNo = "";

					//地政士姓名，先用mainid、oid反查C122M01Y，若於簽報書有引入，帶出該比C120S01Y資料
					List<L140M01Y> l140m01yList = l140m01ydao.findByRef(UtilConstants.L140M01Y_refType_docCode1.ELF459_SRCFLAG_1, c122m01a.getMainId(), c122m01a.getOid());
					boolean haveLaa = false;
					if (l140m01yList != null && l140m01yList.size() > 0) {
						Map<String, String> laaMap = new HashMap<String, String>();
						for(int u=0;u<l140m01yList.size();u++){
							L140M01Y l140m01y = l140m01yList.get(u);
							String l140m01a_mainid = Util.trim(l140m01y.getMainId());
							//找回簽報書
							L140M01A l140m01a = l140m01adao.findByMainId(l140m01a_mainid);
							if(l140m01a != null && l140m01a.getL120m01c() != null){
								String l120m01a_mainId = l140m01a.getL120m01c().getMainId();
								List<C120S01Y> c120s01yList = c120s01ydao.findByMainId(l120m01a_mainId);    
								if (c120s01yList != null && c120s01yList.size() > 0) {
									for (int y = 0; y < c120s01yList.size(); y++) {
										C120S01Y c120s01y = c120s01yList.get(y);
										if(laaMap.get(c120s01y.getLaaNo()) == null){
											laaMap.put(c120s01y.getLaaNo(), c120s01y.getLaaName());
										}
									}
								}
							}
						}
						if(laaMap != null && laaMap.size()>0){
							haveLaa = true;
							int y = 0;
							for (String key : laaMap.keySet()) {
								laaNo = laaNo + key;
								laaName = laaName + laaMap.get(key);
								if (y != laaMap.size() - 1) {
									laaName = laaName + "\r\n";
									laaNo = laaNo + "\r\n";
								}
								y++;
					        }
						}
					}
					//簽報書無對應資料，找對應的徵信照會
					if(haveLaa == false){
						C122S01H c122s01h = c122s01hdao.findByUniqueKey(
								c122m01a.getMainId(),
								UtilConstants.C122s01h_flowId.借保人資料);
						if (c122s01h != null) {
							// 通常只會有一筆
							String remainId = Util.trim(c122s01h.getRemainId());
							List<C101S01Y> c101s01yList = c101s01ydao
									.findByMainId(remainId);
							if (c101s01yList != null && c101s01yList.size() > 0) {
								for (int y = 0; y < c101s01yList.size(); y++) {
									C101S01Y c101s01y = c101s01yList.get(y);
									laaName = laaName + c101s01y.getLaaName();
									laaNo = laaNo + c101s01y.getLaaNo();
									if (y != c101s01yList.size() - 1) {
										laaName = laaName + "\r\n";
										laaNo = laaNo + "\r\n";
									}
								}
							}
						}
					}
					
					arr[15] = laaName; // 地政士姓名
					arr[16] = laaNo; // 地政士證書字號

					String ownBrId2 = Util.trim(c122m01a.getOwnBrId());
					arr[17] = ownBrId2; // 分行代號
					arr[18] = getBrName(cacheMap, ownBrId2); // 分行名稱

					final Map<String, String> _DocStatusNewDescMap = service
							.get_DocStatusNewDescMap();
					arr[19] = Util.trim(_DocStatusNewDescMap.get(c122m01a
							.getDocStatus())); // 案件狀態

					String reason = "";
					List<C122S01G> c122s01gList = service.findC122S01G(
							c122m01a.getMainId(), c122m01a.getDocStatus());
					if (c122s01gList != null && c122s01gList.size() > 0) {
						for (int re = 0; re < c122s01gList.size(); re++) {
							C122S01G c122s01g = c122s01gList.get(re);
							reason = reason + c122s01g.getCodeValue() + "-"
									+ c122s01g.getCodeDesc();
							if (re != c122s01gList.size() - 1) {
								reason = reason + "\r\n";
							}
						}
					}
					arr[20] = reason; // 取消/不承作原因

					//J-111-0393 增加行銷方案欄位
					CodeType ploanPlanCodeType = codeTypeService.findByCodeTypeAndCodeValue("ploan_plan", Util.trim(c122m01a.getPloanPlan()), "zh_TW");
					arr[21] = Util.isNotEmpty(ploanPlanCodeType) ? ploanPlanCodeType.getCodeValue() : null; //行銷方案
					arr[22] = Util.isNotEmpty(ploanPlanCodeType) ? ploanPlanCodeType.getCodeDesc() : null; //行銷方案
					
					//J-112-0006 地政士案件重複派案註記
					arr[23] = Util.equals("Y", c122m01f.getIsSameMegaEmpLaaCase()) ? "Y" : "";//需重新指派案件 

					// ---
					rows.add(arr);
				}
			}

			int rowIdx = 0;
			// ==============================
		    HSSFRow titleRow = sheet1.createRow(rowIdx);  

			if (true) {
				String tableName = "派案登記表";
				if(Util.equals(applyKind, UtilConstants.C122_ApplyKind.E) && Util.equals(purchaseHouse, "Y")){ 
					//[房貸]且為[購屋]表單TITLE變更為[購屋新做房貸案件派案登記表]
					tableName = "購屋新做房貸案件派案登記表";
				}
				String title = ownBrId+getBrName(cacheMap, ownBrId)+" "+tableName + " 期間："+applyTS_beg_date + "~"+applyTS_end_date;
				sheet1.addMergedRegion(new CellRangeAddress(0, 0, 0, totalColSize-1));
				LmsExcelUtil.addCell(titleRow, 0, title, cellFormatT_Border);

			}
			
			// ==============================
			if (true) {
				rowIdx = 1;
				HSSFRow headerRow = sheet1.createRow(rowIdx);
				int colIdx = 0;
				for (String h : headerMap.keySet()) {
					int colWidth = headerMap.get(h);
					sheet1.setColumnWidth(colIdx, colWidth * 256);
					LmsExcelUtil.addCell(headerRow, colIdx, h, cellFormatL_Border);
					// ---
					colIdx++;
				}
			}
			// ==============================
			if (true) {
				rowIdx = 2;
				int i_row = 0;
				for (String[] arr : rows) {
					HSSFRow dataRow = sheet1.createRow(rowIdx + i_row);
					for (int i_col = 0; i_col < totalColSize; i_col++) {
						LmsExcelUtil.addCell(dataRow, i_col, arr[i_col], cellFormatL_Border);
					}
					// ---
					i_row++;
				}
			}

			// ---
			workbook.write(outputStream);
			workbook.close();
		}
	}

	private String getBrName(Map<String, String> cacheMap, String brNo) {
		if (!cacheMap.containsKey(brNo)) {
			IBranch obj = branchService.getBranch(brNo);
			if (obj != null) {
				cacheMap.put(brNo, Util.trim(obj.getBrName()));
			}
		}
		return Util.trim(cacheMap.get(brNo));
	}

}
