/**
 * TODO ELF501_SEQ_NO 的對應
 
l140s02a				c160s01c							elf501
新增3個產品，刪前2個              不可只動用部分產品，產品要一次全動用
============            ============ 						============ 
seq    secNo			seq, caseSeq(==l140s02a.secNo)		ELF501_SEQ_NO(c160s01c.caseSeq, maybe+50)
1
2
3        1				3	1
 
*/

package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.wicket.markup.html.form.Check;

import tw.com.iisi.cap.model.GenericBean;

/** 消金額度案件介面檔 **/

public class ELF501 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/**
	 * 客戶編號
	 * <p/>
	 * LNF030_CUST_ID(1:10)
	 */
	@Size(max = 10)
	@Column(name = "ELF501_CUSTID", length = 10, columnDefinition = "CHAR(10)")
	private String elf501_custid;

	/**
	 * 重複序號
	 * <p/>
	 * LNF030_CUST_ID(11:1)
	 */
	@Size(max = 1)
	@Column(name = "ELF501_DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_dupno;

	/**
	 * 額度序號
	 * <p/>
	 * LNF030_CONTRACT
	 */
	@Size(max = 12)
	@Column(name = "ELF501_CNTRNO", length = 12, columnDefinition = "CHAR(12)",unique = true)
	private String elf501_cntrno;

	/** 額度案件序號 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_SEQ_NO", columnDefinition = "DECIMAL(3,0)",unique = true)
	private Integer elf501_seq_no;

	/**
	 * 放款幣別
	 * <p/>
	 * LNF030_SWFT
	 */
	@Size(max = 3)
	@Column(name = "ELF501_SWFT", length = 3, columnDefinition = "CHAR(03)")
	private String elf501_swft;

	/**
	 * 對應放款帳號
	 * <p/>
	 * LNF030_LOAN_NO
	 */
	@Size(max = 14)
	@Column(name = "ELF501_LOAN_NO", length = 14, columnDefinition = "CHAR(14)")
	private String elf501_loan_no;

	/**
	 * 產品種類
	 * <p/>
	 * LNF030_LOAN_CLASS
	 */
	@Size(max = 2)
	@Column(name = "ELF501_LNTYPE", length = 2, columnDefinition = "CHAR(2)")
	private String elf501_lntype;

	/**
	 * 科目代碼
	 * <p/>
	 * LNF030_LOAN_NO(5,3)
	 */
	@Size(max = 8)
	@Column(name = "ELF501_LOANTP", length = 8, columnDefinition = "CHAR(8)")
	private String elf501_loantp;

	/**
	 * 資金來源
	 * <p/>
	 * LNF030_FUND_TYPE1
	 */
	@Size(max = 1)
	@Column(name = "ELF501_FNDSRE", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_fndsre;

	/**
	 * 資金來源小類
	 * <p/>
	 * LNF030_FUND_TYPE2
	 */
	@Size(max = 3)
	@Column(name = "ELF501_FUND_TYPE2", length = 3, columnDefinition = "CHAR(03)")
	private String elf501_fund_type2;

	/**
	 * 用途別
	 * <p/>
	 * LNF030_USE_TYPE
	 */
	@Size(max = 1)
	@Column(name = "ELF501_LNPURS", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_lnpurs;

	/**
	 * 融資業務分類
	 * <p/>
	 * LNF030_LN_PURPOSE
	 */
	@Size(max = 1)
	@Column(name = "ELF501_LN_PURPOSE", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_ln_purpose;

	/**
	 * 是否屬興建房屋
	 * <p/>
	 * LNF030_RESIDENTIAL
	 */
	@Size(max = 1)
	@Column(name = "ELF501_RESIDENTIAL", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_residential;

	/**
	 * 是否自用住宅Y/N
	 * <p/>
	 * LNF033_OWN_HOUSE
	 */
	@Size(max = 1)
	@Column(name = "ELF501_OWN_HOUSE", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_own_house;

	/**
	 * 風險權數 ( ref LNF033_RISK_RATING ) 
	 * <ul>
	 * <li>2022-02 詢問8組 林育龍，之前判斷住宅用不動產時 首撥日 >=100-04-21 要去看 LNF033_OWN_HOUSE,LN033_RISK_RATING這邊的權數, 不過採用 LTV 法就不再適用了
	 * </li>
	 * <li>J-111-0096 自 2021-06-30起，因應本行「不動產暴險」改以貸放比率(LTV)決定適用之風險權數，個人戶授信案件免填「風險權數」，新做案件上傳到 RISK_RATING 的值改為 "-1"
	 * </li>
	 * <li>trigger 由 ELF501 改 LNF033 , 可用 SELECT * FROM SYSIBM.SYSTRIGGERS where name in ('LNTR0030','LNTR0031') 
	 * </li>
	 * </ul>
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_RISK_RATING", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_risk_rating;

	/**
	 * 貸款總月數
	 * <p/>
	 * LNF033-TOT-TERM
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_MONTHCNT", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_monthcnt;

	/**
	 * 計息方式
	 * <p/>
	 * LNF030_INTCAL_TYPE
	 */
	@Size(max = 1)
	@Column(name = "ELF501_GINTTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_ginttype;

	/**
	 * 收息方式
	 * <p/>
	 * LNF030_INTRT_TYPE
	 */
	@Size(max = 1)
	@Column(name = "ELF501_HINTTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_hinttype;

	/**
	 * 償還方式
	 * <p/>
	 * LNF033_RT_TYPE
	 */
	@Size(max = 1)
	@Column(name = "ELF501_AVGPAY", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_avgpay;

	/**
	 * 繳款週期
	 * <p/>
	 * LNF033-INTRT-CYCL
	 */
	@Size(max = 1)
	@Column(name = "ELF501_PAYCLE", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_paycle;

	/**
	 * 每期攤還本金
	 * <p/>
	 * LNF033-RT-PRINCPL
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF501_EHPAYCPT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf501_ehpaycpt;

	/**
	 * 稅籍編號
	 * <p/>
	 * LNF033_HOUSE_NO
	 */
	@Size(max = 12)
	@Column(name = "ELF501_TAXNO", length = 12, columnDefinition = "CHAR(12)")
	private String elf501_taxno;

	/**
	 * 稅籍地址
	 * <p/>
	 * LNF033_HOUSE_ADDR
	 */
	@Size(max = 50)
	@Column(name = "ELF501_TAXADDR", length = 50, columnDefinition = "GRAPHIC(50)")
	private String elf501_taxaddr;

	/**
	 * 引介房仲代號(原 LNF033_COMPANY_ID 改成LNF13E_AGNT_NO)
	 */
	@Size(max = 5)
	@Column(name = "ELF501_IMPORTID", length = 5, columnDefinition = "CHAR(5)")
	private String elf501_importid;

	/**
	 * 行銷分行
	 * <p/>
	 * LNF033_COM_BR
	 */
	@Size(max = 3)
	@Column(name = "ELF501_EFCTBH", length = 3, columnDefinition = "CHAR(3)")
	private String elf501_efctbh;

	/**
	 * 行銷代號
	 * <p/>
	 * LNF033-INSC-NO
	 */
	@Size(max = 3)
	@Column(name = "ELF501_INSC_NO", length = 3, columnDefinition = "CHAR(03)")
	private String elf501_insc_no;

	/**
	 * 自動進帳
	 * <p/>
	 * LNF033_ADP_FLAG
	 */
	@Size(max = 1)
	@Column(name = "ELF501_AUTORCT", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_autorct;

	/**
	 * 進帳日期
	 * <p/>
	 * LNF033_ADP_DATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF501_RCTDATE", columnDefinition = "DATE")
	private Date elf501_rctdate;

	/**
	 * 存款帳號(進帳帳號)
	 * <p/>
	 * LNF030_DP_ACT_1
	 */
	@Size(max = 14)
	@Column(name = "ELF501_ACCNO", length = 14, columnDefinition = "CHAR(14)")
	private String elf501_accno;

	/**
	 * 進帳金額
	 * <p/>
	 * LNF030_1ST_LN_AMT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF501_RCTAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf501_rctamt;

	/**
	 * 自動扣帳
	 * <p/>
	 * LNF033_ART_FLAG
	 */
	@Size(max = 1)
	@Column(name = "ELF501_AUTOPAY", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_autopay;

	/**
	 * 扣帳帳號
	 * <p/>
	 * LNF030_DP_ACT_3
	 */
	@Size(max = 14)
	@Column(name = "ELF501_ATPAYNO", length = 14, columnDefinition = "CHAR(14)")
	private String elf501_atpayno;

	/**
	 * 計息天數基礎
	 * <p/>
	 * LNF030_INTDAY_BASE<br/>
	 * (本欄位不給使用者輸入直接放2)<br/>
	 * 1:實際天數 / 360 <br/>
	 * 2:實際天數 / 365 <br/>
	 * 3:每月 30/ 360
	 */
	@Size(max = 1)
	@Column(name = "ELF501_INTDAY_BASE", length = 1, columnDefinition = "CHAR(01)")
	private String elf501_intday_base;

	/**
	 * 利息出帳幣別
	 * <p/>
	 * LNF030_INTCUR_BASE<br/>
	 * 1: 台幣計息<br/>
	 * 2: 原幣計息
	 */
	@Size(max = 1)
	@Column(name = "ELF501_INTCUR_BASE", length = 1, columnDefinition = "CHAR(01)")
	private String elf501_intcur_base;

	/**
	 * 寬限期(起)
	 * <p/>
	 * LNF033_ALLOW_BEG
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_EXTFROM", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_extfrom;

	/**
	 * 寬限期(迄)
	 * <p/>
	 * LNF033_ALLOW_END
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_EXTEND", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_extend;

	/**
	 * 展延種類
	 * <p/>
	 * LNF033_DEF_TYPE
	 */
	@Size(max = 1)
	@Column(name = "ELF501_EXTTP", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_exttp;

	/**
	 * 展延項目
	 * <p/>
	 * LNF033_DEF_ITEM
	 */
	@Size(max = 1)
	@Column(name = "ELF501_EXTITEM", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_extitem;

	/**
	 * 本金展延起始期
	 * <p/>
	 * LNF033_PRI_DEF_BEG
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_CPEXTFM", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_cpextfm;

	/**
	 * 本金展延截止期
	 * <p/>
	 * LNF033_PRI_DEF_DUE
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_CPEXTED", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_cpexted;

	/**
	 * 利息展期起始期
	 * <p/>
	 * LNF033_INT_DEF_BEG
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_INEXTFM", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_inextfm;

	/**
	 * 利息展延截止期
	 * <p/>
	 * LNF033_INT_DEF_DUE
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_INEXTED", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_inexted;

	/**
	 * 應收利息攤還截止期
	 * <p/>
	 * LNF033_LST_INT_DUE
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_INDLINE", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_indline;

	/**
	 * 計收遲延利息加碼
	 * <p/>
	 * LNF033_OVDUE_PNT
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "ELF501_OVERCHG", columnDefinition = "DECIMAL(6,4)")
	private BigDecimal elf501_overchg;

	/** 逾期在個月以內部份 **/
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_OVERMM1", columnDefinition = "DECIMAL(2,0)")
	private Integer elf501_overmm1;

	/**
	 * 違約金計算條件１－百分比
	 * <p/>
	 * LNF033_OV_PNT
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "ELF501_PERCT1", columnDefinition = "DECIMAL(6,4)")
	private BigDecimal elf501_perct1;

	/** 逾期超過個月部份 **/
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_OVERMM2", columnDefinition = "DECIMAL(2,0)")
	private Integer elf501_overmm2;

	/** 違約金計算條件２－百分比 **/
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "ELF501_PERCT2", columnDefinition = "DECIMAL(6,4)")
	private BigDecimal elf501_perct2;

	/**
	 * 連動式房貸註記
	 * <p/>
	 * LNF033-OFFSET-FLAG
	 */
	@Size(max = 1)
	@Column(name = "ELF501_ISCREDIT", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_iscredit;

	/**
	 * 提前還本管制起期
	 * <p/>
	 * LNF033_RTCAP_BEG
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_PCONBEG", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_pconbeg;

	/**
	 * 提前還本管制迄期
	 * <p/>
	 * LNF033_RTCAP_END
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_PCONEND", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_pconend;

	/**
	 * 提前還本違約金計算條件'
	 * <p/>
	 * LNF033_RTCAP_PNT
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "ELF501_CALCONDI", columnDefinition = "DECIMAL(6,4)")
	private BigDecimal elf501_calcondi;

	/**
	 * 提前還本管制迄期2
	 * <p/>
	 * LNF033_RTCAP_BEG2
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_PCONEND2", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_pconend2;

	/**
	 * 提前還本管制起期2
	 * <p/>
	 * LNF033_RTCAP_END2
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_PCONBEG2", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_pconbeg2;

	/**
	 * 提前還本違約金計算條件2
	 * <p/>
	 * LNF033_RTCAP_PNT2
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "ELF501_CALCONDI2", columnDefinition = "DECIMAL(6,4)")
	private BigDecimal elf501_calcondi2;

	/**
	 * 是否代付費用
	 * <p/>
	 * LNF033_AGENCY
	 */
	@Size(max = 1)
	@Column(name = "ELF501_ISTAKFEE", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_istakfee;

	/**
	 * 代付費用管制迄期
	 * <p/>
	 * LNF033_AGENCY_END
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_TCONEND", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_tconend;

	/**
	 * 是否轉貸戶
	 * <p/>
	 * LNF135_CHG_CASE
	 */
	@Size(max = 1)
	@Column(name = "ELF501_CHGCASE", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_chgcase;

	/**
	 * 轉貸剩餘貸款本金
	 * <p/>
	 * LNF135_CHG_BAL
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF501_CHGLNBAL", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf501_chglnbal;

	/**
	 * 原貸款到期日
	 * <p/>
	 * LNF135_ORI_P_DATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF501_ORIPDATE", columnDefinition = "DATE")
	private Date elf501_oripdate;

	/**
	 * 轉貸日期
	 * <p/>
	 * LNF135_CHG_DATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF501_CHGDATE", columnDefinition = "DATE")
	private Date elf501_chgdate;

	/**
	 * 轉入之原金融機構代碼
	 * <p/>
	 * LNF135_ORI_BK_CD
	 */
	@Size(max = 7)
	@Column(name = "ELF501_ORIBANCD", length = 7, columnDefinition = "CHAR(7)")
	private String elf501_oribancd;

	/**
	 * 轉貸前後為相同性質之政策性貸款
	 * <p/>
	 * LNF135_SAME_POLICY
	 */
	@Size(max = 1)
	@Column(name = "ELF501_SAME_POLICY", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_same_policy;

	/**
	 * 轉貸原貸款貸放日期
	 * <p/>
	 * LNF135_ORI_B_DATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF501_ORI_B_DATE", columnDefinition = "DATE")
	private Date elf501_ori_b_date;

	/**
	 * 代償他行房貸原因
	 * <p/>
	 * LNF135_CHG_REASON1<br/>
	 * 代償原因（買賣）<br/>
	 * LNF135_CHG_REASON2<br/>
	 * 代償原因（利率）<br/>
	 * LNF135_CHG_REASON3<br/>
	 * 代償原因（額度）
	 */
	@Size(max = 1)
	@Column(name = "ELF501_CHGREASON", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_chgreason;

	/**
	 * 代償原因－其他
	 * <p/>
	 * LNF135_CHG_REASON4
	 */
	@Size(max = 10)
	@Column(name = "ELF501_CHGMEMO", length = 10, columnDefinition = "GRAPHIC(10)")
	private String elf501_chgmemo;

	/**
	 * 扣帳方式
	 * <p/>
	 * LNF033_CREDIT_INS<br/>
	 * 00:一般<br/>
	 * 01:ACH 扣帳
	 */
	@Size(max = 2)
	@Column(name = "ELF501_PAYTYPE", length = 2, columnDefinition = "CHAR(2)")
	private String elf501_paytype;

	/**
	 * 選擇權項目
	 * <p/>
	 * LNF033_MATCH_ITEM
	 */
	@Size(max = 1)
	@Column(name = "ELF501_UPTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_uptype;

	/**
	 * 選擇權起期
	 * <p/>
	 * LNF033_UP_BEG
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_UPBEG", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_upbeg;

	/**
	 * 選擇權迄期
	 * <p/>
	 * LNF033_UP_END
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_UPEND", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_upend;

	/**
	 * 選擇權利率
	 * <p/>
	 * LNF033_UP_RATE
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "ELF501_UPRATE", columnDefinition = "DECIMAL(6,4)")
	private BigDecimal elf501_uprate;

	/**
	 * 省息遞減註記
	 * <p/>
	 * LNF033_DEC_FLAG
	 */
	@Size(max = 1)
	@Column(name = "ELF501_DECFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_decflag;

	/**
	 * 利率方案
	 * <p/>
	 * LNF033_RATE_PLAN
	 */
	@Size(max = 2)
	@Column(name = "ELF501_RATE_PLAN", length = 2, columnDefinition = "CHAR(2)")
	private String elf501_rate_plan;

	/**
	 * 產品方案
	 * <p/>
	 * LNF033_PROD_PLAN
	 */
	@Size(max = 2)
	@Column(name = "ELF501_PROD_PLAN", length = 2, columnDefinition = "CHAR(2)")
	private String elf501_prod_plan;

	/**
	 * 借款繳保費之金額
	 * <p/>
	 * LNF033_INSC_AMT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF501_INSC_AMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf501_insc_amt;

	/**
	 * 是否搭配房貸壽險(Y/N)
	 * <p/>
	 * LNF033_RMBINS_FLAG
	 */
	@Size(max = 1)
	@Column(name = "ELF501_RMBINS_FLAG", length = 1, columnDefinition = "CHAR(01)")
	private String elf501_rmbins_flag;

	/**
	 * 是否搭配房貸壽險利率優惠方案(Y/N)
	 * <p/>
	 * LNF033_RMBINT_FLAG
	 */
	@Size(max = 1)
	@Column(name = "ELF501_RMBINT_FLAG", length = 1, columnDefinition = "CHAR(01)")
	private String elf501_rmbint_flag;

	/**
	 * 搭配房貸壽險利率優惠方案之期數(999)
	 * <p/>
	 * LNF033_RMBINT_TERM
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF501_RMBINT_TERM", columnDefinition = "DECIMAL(3,0)")
	private Integer elf501_rmbint_term;

	/**
	 * 是否保費融資(Y/N)
	 * <p/>
	 * LNF033_INS_FLAG
	 */
	@Size(max = 1)
	@Column(name = "ELF501_INS_FLAG", length = 1, columnDefinition = "CHAR(01)")
	private String elf501_ins_flag;

	/**
	 * 保費融資金額
	 * <p/>
	 * LNF033_INS_LOANBAL
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF501_INS_LOANBAL", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf501_ins_loanbal;

	/**
	 * 勞貸中籤編號
	 * <p/>
	 * LNF033_LNUM
	 */
	@Size(max = 7)
	@Column(name = "ELF501_ASGNNO", length = 7, columnDefinition = "CHAR(7)")
	private String elf501_asgnno;

	/**
	 * 勞貸中籤年份
	 * <p/>
	 * LNF033_PLAN_YEAR
	 */
	@Size(max = 3)
	@Column(name = "ELF501_ASGNYY", length = 3, columnDefinition = "CHAR(3)")
	private String elf501_asgnyy;

	/**
	 * 勞貸收件編號
	 * <p/>
	 * LNF033_RECV_NO
	 */
	@Size(max = 8)
	@Column(name = "ELF501_REVNO", length = 8, columnDefinition = "CHAR(8)")
	private String elf501_revno;

	/**
	 * 輔購核准年度
	 * <p/>
	 * LNF033_CITY_YYY
	 */
	@Size(max = 3)
	@Column(name = "ELF501_APRVYY", length = 3, columnDefinition = "CHAR(3)")
	private String elf501_aprvyy;

	/**
	 * 輔購名冊編號
	 * <p/>
	 * LNF033_CITY_NO
	 */
	@Size(max = 9)
	@Column(name = "ELF501_LISTNO", length = 9, columnDefinition = "CHAR(9)")
	private String elf501_listno;

	/**
	 * 內政部整合住宅方案'
	 * <p/>
	 * LNF033_CPAMI_NO
	 */
	@Size(max = 10)
	@Column(name = "ELF501_ALLOWNO", length = 10, columnDefinition = "CHAR(10)")
	private String elf501_allowno;

	/**
	 * 安心成家貸款申請編號'
	 * <p/>
	 * LNF033_APPLY_NO
	 */
	@Size(max = 30)
	@Column(name = "ELF501_APPLY_NO", length = 30, columnDefinition = "CHAR(30)")
	private String elf501_apply_no;
	
	/**
	 * 縣(市)政府首購貸款(縣市)
	 * <p/>
	 * LNF033_KG_AREA
	 */
	@Size(max = 9)
	@Column(name = "ELF501_KG_AREA", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_kg_area;

	/**
	 * 縣(市)政府首購貸款核准編號
	 * <p/>
	 * LNF033_KG_AGREE_NO
	 */
	@Size(max = 9)
	@Column(name = "ELF501_KG_AGREE_NO", length = 9, columnDefinition = "CHAR(9)")
	private String elf501_kg_agree_no;

	/**
	 * 縣(市)政府首購貸款核准日期
	 * <p/>
	 * LNF033_KG_AGREE_DT
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF501_KG_AGREE_DT", columnDefinition = "DATE")
	private Date elf501_kg_agree_dt;

	/**
	 * 縣(市)政府首購貸款終止補貼日
	 * <p/>
	 * LNF033_KG_END_DATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF501_KG_END_DATE", columnDefinition = "DATE")
	private Date elf501_kg_end_date;

	/**
	 * 縣(市)政府首購貸款終止補貼原因
	 * <p/>
	 * LNF033_KG_END_CODE
	 */
	@Size(max = 1)
	@Column(name = "ELF501_KG_END_CODE", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_kg_end_code;

	/**
	 * 縣(市)政府首購貸款首購補貼起日
	 * <p/>
	 * LNF033_KG_INT_DATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF501_KG_INT_DATE", columnDefinition = "DATE")
	private Date elf501_kg_int_date;

	/**
	 * 外勞仲介公司統編
	 * <p/>
	 * LNF033_AGENCY_ID
	 */
	@Size(max = 11)
	@Column(name = "ELF501_INTRID", length = 11, columnDefinition = "CHAR(11)")
	private String elf501_intrid;

	/**
	 * 外勞仲介獎金比例
	 * <p/>
	 * LNF033_AGEN_RATIO
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "ELF501_INTRRATE", columnDefinition = "DECIMAL(6,4)")
	private BigDecimal elf501_intrrate;

	/**
	 * 計算擔保維持率註記
	 * <p/>
	 * LNF030_STOCK_RATE
	 */
	@Size(max = 1)
	@Column(name = "ELF501_STOCK_RATE", length = 1, columnDefinition = "CHAR(01)")
	private String elf501_stock_rate;

	/**
	 * 開戶原因
	 * <p/>
	 * LNF030-TRANS-CODE<br/>
	 * 0:一舨案<br/>
	 * 1: 催收轉正<br/>
	 * 2: 更正前次撥款<br/>
	 * 3: 繼承案件<br/>
	 * 4: 受贈土地之土地增值稅融資<br/>
	 * 5: 遺產稅融資<br/>
	 * 6: 納稅義務人為受贈人之贈與稅融資<br/>
	 * 7: 借款人擔保品被政府徵收而改列無擔保之放款
	 */
	@Size(max = 1)
	@Column(name = "ELF501_TRANS_CODE", length = 1, columnDefinition = "CHAR(01)")
	private String elf501_trans_code;

	/**
	 * 承接之前放款帳號'
	 * <p/>
	 * LNF030-ORG-LOAN-NO<br/>
	 * (當ELF501_TRANS_CODE = 123時才需要輸入此欄位)
	 */
	@Size(max = 14)
	@Column(name = "ELF501_ORG_LOAN_NO", length = 14, columnDefinition = "CHAR(14)")
	private String elf501_org_loan_no;

//	/** 資料修改人 **/
//	@Size(max = 5)
//	@Column(name = "ELF501_UPDATER", length = 5, columnDefinition = "CHAR(5)")
//	private String elf501_updater;

	/** 主管代號 **/
	@Size(max = 6)
	@Column(name = "ELF501_SUPVNO", length = 6, columnDefinition = "CHAR(06)")
	private String elf501_supvno;
	
	/** 資料修改日期 **/
	@Column(name = "ELF501_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf501_tmestamp;

	/** ELOAN寫入時間 **/
	@Column(name = "ELF501_ELOANTIMES", columnDefinition = "TIMESTAMP")
	private Timestamp elf501_eloantimes;

	/**
	 * 櫃員代號	 */
	@Size(max = 6)
	@Column(name = "ELF501_TELLER", length = 14, columnDefinition = "CHAR(6)")
	private String elf501_teller;
	
	/** 扣稅負擔值 **/
	
	@Digits(integer = 7, fraction = 5, groups = Check.class)
	@Column(name="ELF501_TAXRATE", length=7, columnDefinition="DECIMAL(7,5)")
	private BigDecimal elf501_taxrate;

	/** 開辦費 **/
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="ELF501_AGENCY_AMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf501_agency_amt;
	
	/**
	 * 評等
	 * <p/>
	 */
	@Size(max = 2)
	@Column(name = "ELF501_GRADE", length = 2, columnDefinition = "CHAR(2)")
	private String elf501_grade;
	
	
	/**
	 * 帳號(科目)限額
	 * <p/>
	 * 
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF501_LNAP_AMT", columnDefinition = "DECLICL(15,2)")
	private BigDecimal elf501_lnap_amt;
	
	/** A-Loan 執行時間 on Line **/
	@Column(name = "ELF501_ONLNTIME", columnDefinition = "TIMESTAMP")
	private Timestamp elf501_onlntime;

	/** A-Loan 執行時間 Batch **/
	@Column(name = "ELF501_BTHTIME", columnDefinition = "TIMESTAMP")
	private Timestamp elf501_bthtime;
	
	/** 透支計息方式：0-透支End, 1-透支Top **/
	@Size(max = 1)
	@Column(name = "ELF501_OVBAL_TYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_ovbal_type;
	
	/** 契約種類：1-購屋契約, 2-純信用無擔保(DBR22倍), 3-其他類契約, 4-歡喜樂活貸款契約書(以房養老)<br/>
	 * 按月計息: ?<br/>
	 * 期付金: LNF033_CNTRNO_TYPE
	 */
	@Size(max = 1)
	@Column(name = "ELF501_CNTRNO_TYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_cntrno_type;
	
	/** 天然及重大災害種類  **/
	@Size(max = 2)
	@Column(name = "ELF501_DISAS_TYPE", length = 2, columnDefinition = "CHAR(2)")
	private String elf501_disas_type;
	
	/** 房貸引介子公司代號{90001:兆豐金控, 90002:	兆豐產險, 90003:	兆豐證券, 90004:	兆豐投信
		, 90005:兆豐資產管理, 90006:兆豐人身保代, 90007:兆豐創投, 90008:兆豐票券} **/
	@Size(max = 5)
	@Column(name = "ELF501_MEGA_CODE", length = 5, columnDefinition = "CHAR(5)")
	private String elf501_mega_code;
	
	/** 引介房仲收取回饋金  **/
	@Size(max = 1)
	@Column(name = "ELF501_AGNT_FBFG", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_agnt_fbfg;
	
	/**
	 * 週轉金用途細項
	 * <p/>
	 */
	@Size(max = 1)
	@Column(name = "ELF501_LNPURS_SUB", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_lnpurs_sub;
	
	/** 首次還款日 ( LNF033_1ST_RT_DT )
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF501_1ST_RT_DT", columnDefinition = "DATE")
	private Date elf501_1st_rt_dt; 
	
	/** 綠色支出類型 */ 
	@Size(max = 1)
	@Column(name = "ELF501_ESGGTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_esggtype;
	
	/** 社會責任專案類別 */ 
	@Size(max = 1)
	@Column(name = "ELF501_SOCIALKIND", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_socialKind;
	
	/** 社會責任專案目標族群 */ 
	@Size(max = 1)
	@Column(name = "ELF501_SOCIALTA", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_socialTa;
	
	/** 承作社會責任專案者 */ 
	@Size(max = 1)
	@Column(name = "ELF501_SOCIALRESP", length = 1, columnDefinition = "CHAR(1)")
	private String elf501_socialResp;
	
	
	/**
	 * 取得客戶編號
	 * <p/>
	 * LNF030_CUST_ID(1:10)
	 */
	public String getElf501_custid() {
		return this.elf501_custid;
	}

	/**
	 * 設定客戶編號
	 * <p/>
	 * LNF030_CUST_ID(1:10)
	 **/
	public void setElf501_custid(String value) {
		this.elf501_custid = value;
	}

	/**
	 * 取得重複序號
	 * <p/>
	 * LNF030_CUST_ID(11:1)
	 */
	public String getElf501_dupno() {
		return this.elf501_dupno;
	}

	/**
	 * 設定重複序號
	 * <p/>
	 * LNF030_CUST_ID(11:1)
	 **/
	public void setElf501_dupno(String value) {
		this.elf501_dupno = value;
	}

	/**
	 * 取得額度序號
	 * <p/>
	 * LNF030_CONTRACT
	 */
	public String getElf501_cntrno() {
		return this.elf501_cntrno;
	}

	/**
	 * 設定額度序號
	 * <p/>
	 * LNF030_CONTRACT
	 **/
	public void setElf501_cntrno(String value) {
		this.elf501_cntrno = value;
	}

	/** 取得額度案件序號 **/
	public Integer getElf501_seq_no() {
		return this.elf501_seq_no;
	}

	/** 設定額度案件序號 **/
	public void setElf501_seq_no(Integer value) {
		this.elf501_seq_no = value;
	}

	/**
	 * 取得放款幣別
	 * <p/>
	 * LNF030_SWFT
	 */
	public String getElf501_swft() {
		return this.elf501_swft;
	}

	/**
	 * 設定放款幣別
	 * <p/>
	 * LNF030_SWFT
	 **/
	public void setElf501_swft(String value) {
		this.elf501_swft = value;
	}

	/**
	 * 取得對應放款帳號
	 * <p/>
	 * LNF030_LOAN_NO
	 */
	public String getElf501_loan_no() {
		return this.elf501_loan_no;
	}

	/**
	 * 設定對應放款帳號
	 * <p/>
	 * LNF030_LOAN_NO
	 **/
	public void setElf501_loan_no(String value) {
		this.elf501_loan_no = value;
	}

	/**
	 * 取得產品種類
	 * <p/>
	 * LNF030_LOAN_CLASS
	 */
	public String getElf501_lntype() {
		return this.elf501_lntype;
	}

	/**
	 * 設定產品種類
	 * <p/>
	 * LNF030_LOAN_CLASS
	 **/
	public void setElf501_lntype(String value) {
		this.elf501_lntype = value;
	}

	/**
	 * 取得科目代碼
	 * <p/>
	 * LNF030_LOAN_NO(5,3)
	 */
	public String getElf501_loantp() {
		return this.elf501_loantp;
	}

	/**
	 * 設定科目代碼
	 * <p/>
	 * LNF030_LOAN_NO(5,3)
	 **/
	public void setElf501_loantp(String value) {
		this.elf501_loantp = value;
	}

	/**
	 * 取得資金來源
	 * <p/>
	 * LNF030_FUND_TYPE1
	 */
	public String getElf501_fndsre() {
		return this.elf501_fndsre;
	}

	/**
	 * 設定資金來源
	 * <p/>
	 * LNF030_FUND_TYPE1
	 **/
	public void setElf501_fndsre(String value) {
		this.elf501_fndsre = value;
	}

	/**
	 * 取得資金來源小類
	 * <p/>
	 * LNF030_FUND_TYPE2
	 */
	public String getElf501_fund_type2() {
		return this.elf501_fund_type2;
	}

	/**
	 * 設定資金來源小類
	 * <p/>
	 * LNF030_FUND_TYPE2
	 **/
	public void setElf501_fund_type2(String value) {
		this.elf501_fund_type2 = value;
	}

	/**
	 * 取得用途別
	 * <p/>
	 * LNF030_USE_TYPE
	 */
	public String getElf501_lnpurs() {
		return this.elf501_lnpurs;
	}

	/**
	 * 設定用途別
	 * <p/>
	 * LNF030_USE_TYPE
	 **/
	public void setElf501_lnpurs(String value) {
		this.elf501_lnpurs = value;
	}

	/**
	 * 取得融資業務分類
	 * <p/>
	 * LNF030_LN_PURPOSE
	 */
	public String getElf501_ln_purpose() {
		return this.elf501_ln_purpose;
	}

	/**
	 * 設定融資業務分類
	 * <p/>
	 * LNF030_LN_PURPOSE
	 **/
	public void setElf501_ln_purpose(String value) {
		this.elf501_ln_purpose = value;
	}

	/**
	 * 取得是否屬興建房屋
	 * <p/>
	 * LNF030_RESIDENTIAL
	 */
	public String getElf501_residential() {
		return this.elf501_residential;
	}

	/**
	 * 設定是否屬興建房屋
	 * <p/>
	 * LNF030_RESIDENTIAL
	 **/
	public void setElf501_residential(String value) {
		this.elf501_residential = value;
	}

	/**
	 * 取得是否自用住宅Y/N
	 * <p/>
	 * LNF033_OWN_HOUSE
	 */
	public String getElf501_own_house() {
		return this.elf501_own_house;
	}

	/**
	 * 設定是否自用住宅Y/N
	 * <p/>
	 * LNF033_OWN_HOUSE
	 **/
	public void setElf501_own_house(String value) {
		this.elf501_own_house = value;
	}

	/**
	 * 取得風險權數
	 * <p/>
	 * LNF033_RISK_RATING
	 */
	public Integer getElf501_risk_rating() {
		return this.elf501_risk_rating;
	}

	/**
	 * 設定風險權數
	 * <p/>
	 * LNF033_RISK_RATING
	 **/
	public void setElf501_risk_rating(Integer value) {
		this.elf501_risk_rating = value;
	}

	/**
	 * 取得貸款總月數
	 * <p/>
	 * LNF033-TOT-TERM
	 */
	public Integer getElf501_monthcnt() {
		return this.elf501_monthcnt;
	}

	/**
	 * 設定貸款總月數
	 * <p/>
	 * LNF033-TOT-TERM
	 **/
	public void setElf501_monthcnt(Integer value) {
		this.elf501_monthcnt = value;
	}

	/**
	 * 取得計息方式
	 * <p/>
	 * LNF030_INTCAL_TYPE
	 */
	public String getElf501_ginttype() {
		return this.elf501_ginttype;
	}

	/**
	 * 設定計息方式
	 * <p/>
	 * LNF030_INTCAL_TYPE
	 **/
	public void setElf501_ginttype(String value) {
		this.elf501_ginttype = value;
	}

	/**
	 * 取得收息方式
	 * <p/>
	 * LNF030_INTRT_TYPE
	 */
	public String getElf501_hinttype() {
		return this.elf501_hinttype;
	}

	/**
	 * 設定收息方式
	 * <p/>
	 * LNF030_INTRT_TYPE
	 **/
	public void setElf501_hinttype(String value) {
		this.elf501_hinttype = value;
	}

	/**
	 * 取得償還方式
	 * <p/>
	 * LNF033_RT_TYPE
	 */
	public String getElf501_avgpay() {
		return this.elf501_avgpay;
	}

	/**
	 * 設定償還方式
	 * <p/>
	 * LNF033_RT_TYPE
	 **/
	public void setElf501_avgpay(String value) {
		this.elf501_avgpay = value;
	}

	/**
	 * 取得繳款週期
	 * <p/>
	 * LNF033-INTRT-CYCL
	 */
	public String getElf501_paycle() {
		return this.elf501_paycle;
	}

	/**
	 * 設定繳款週期
	 * <p/>
	 * LNF033-INTRT-CYCL
	 **/
	public void setElf501_paycle(String value) {
		this.elf501_paycle = value;
	}

	/**
	 * 取得每期攤還本金
	 * <p/>
	 * LNF033-RT-PRINCPL
	 */
	public BigDecimal getElf501_ehpaycpt() {
		return this.elf501_ehpaycpt;
	}

	/**
	 * 設定每期攤還本金
	 * <p/>
	 * LNF033-RT-PRINCPL
	 **/
	public void setElf501_ehpaycpt(BigDecimal value) {
		this.elf501_ehpaycpt = value;
	}

	/**
	 * 取得稅籍編號
	 * <p/>
	 * LNF033_HOUSE_NO
	 */
	public String getElf501_taxno() {
		return this.elf501_taxno;
	}

	/**
	 * 設定稅籍編號
	 * <p/>
	 * LNF033_HOUSE_NO
	 **/
	public void setElf501_taxno(String value) {
		this.elf501_taxno = value;
	}

	/**
	 * 取得稅籍地址
	 * <p/>
	 * LNF033_HOUSE_ADDR
	 */
	public String getElf501_taxaddr() {
		return this.elf501_taxaddr;
	}

	/**
	 * 設定稅籍地址
	 * <p/>
	 * LNF033_HOUSE_ADDR
	 **/
	public void setElf501_taxaddr(String value) {
		this.elf501_taxaddr = value;
	}

	/**
	 * 取得引介房仲代號(原 LNF033_COMPANY_ID 改成LNF13E_AGNT_NO)
	 */
	public String getElf501_importid() {
		return this.elf501_importid;
	}

	/**
	 * 設定引介房仲代號(原 LNF033_COMPANY_ID 改成LNF13E_AGNT_NO)
	 **/
	public void setElf501_importid(String value) {
		this.elf501_importid = value;
	}

	/**
	 * 取得行銷分行
	 * <p/>
	 * LNF033_COM_BR
	 */
	public String getElf501_efctbh() {
		return this.elf501_efctbh;
	}

	/**
	 * 設定行銷分行
	 * <p/>
	 * LNF033_COM_BR
	 **/
	public void setElf501_efctbh(String value) {
		this.elf501_efctbh = value;
	}

	/**
	 * 取得行銷代號
	 * <p/>
	 * LNF033-INSC-NO
	 */
	public String getElf501_insc_no() {
		return this.elf501_insc_no;
	}

	/**
	 * 設定行銷代號
	 * <p/>
	 * LNF033-INSC-NO
	 **/
	public void setElf501_insc_no(String value) {
		this.elf501_insc_no = value;
	}

	/**
	 * 取得自動進帳
	 * <p/>
	 * LNF033_ADP_FLAG
	 */
	public String getElf501_autorct() {
		return this.elf501_autorct;
	}

	/**
	 * 設定自動進帳
	 * <p/>
	 * LNF033_ADP_FLAG
	 **/
	public void setElf501_autorct(String value) {
		this.elf501_autorct = value;
	}

	/**
	 * 取得進帳日期
	 * <p/>
	 * LNF033_ADP_DATE
	 */
	public Date getElf501_rctdate() {
		return this.elf501_rctdate;
	}

	/**
	 * 設定進帳日期
	 * <p/>
	 * LNF033_ADP_DATE
	 **/
	public void setElf501_rctdate(Date value) {
		this.elf501_rctdate = value;
	}

	/**
	 * 取得存款帳號(進帳帳號)
	 * <p/>
	 * LNF030_DP_ACT_1
	 */
	public String getElf501_accno() {
		return this.elf501_accno;
	}

	/**
	 * 設定存款帳號(進帳帳號)
	 * <p/>
	 * LNF030_DP_ACT_1
	 **/
	public void setElf501_accno(String value) {
		this.elf501_accno = value;
	}

	/**
	 * 取得進帳金額
	 * <p/>
	 * LNF030_1ST_LN_AMT
	 */
	public BigDecimal getElf501_rctamt() {
		return this.elf501_rctamt;
	}

	/**
	 * 設定進帳金額
	 * <p/>
	 * LNF030_1ST_LN_AMT
	 **/
	public void setElf501_rctamt(BigDecimal value) {
		this.elf501_rctamt = value;
	}

	/**
	 * 取得自動扣帳
	 * <p/>
	 * LNF033_ART_FLAG
	 */
	public String getElf501_autopay() {
		return this.elf501_autopay;
	}

	/**
	 * 設定自動扣帳
	 * <p/>
	 * LNF033_ART_FLAG
	 **/
	public void setElf501_autopay(String value) {
		this.elf501_autopay = value;
	}

	/**
	 * 取得扣帳帳號
	 * <p/>
	 * LNF030_DP_ACT_3
	 */
	public String getElf501_atpayno() {
		return this.elf501_atpayno;
	}

	/**
	 * 設定扣帳帳號
	 * <p/>
	 * LNF030_DP_ACT_3
	 **/
	public void setElf501_atpayno(String value) {
		this.elf501_atpayno = value;
	}

	/**
	 * 取得計息天數基礎
	 * <p/>
	 * LNF030_INTDAY_BASE<br/>
	 * (本欄位不給使用者輸入直接放2)<br/>
	 * 1:實際天數 / 360 <br/>
	 * 2:實際天數 / 365 <br/>
	 * 3:每月 30/ 360
	 */
	public String getElf501_intday_base() {
		return this.elf501_intday_base;
	}

	/**
	 * 設定計息天數基礎
	 * <p/>
	 * LNF030_INTDAY_BASE<br/>
	 * (本欄位不給使用者輸入直接放2)<br/>
	 * 1:實際天數 / 360 <br/>
	 * 2:實際天數 / 365 <br/>
	 * 3:每月 30/ 360
	 **/
	public void setElf501_intday_base(String value) {
		this.elf501_intday_base = value;
	}

	/**
	 * 取得利息出帳幣別
	 * <p/>
	 * LNF030_INTCUR_BASE<br/>
	 * 1: 台幣計息<br/>
	 * 2: 原幣計息
	 */
	public String getElf501_intcur_base() {
		return this.elf501_intcur_base;
	}

	/**
	 * 設定利息出帳幣別
	 * <p/>
	 * LNF030_INTCUR_BASE<br/>
	 * 1: 台幣計息<br/>
	 * 2: 原幣計息
	 **/
	public void setElf501_intcur_base(String value) {
		this.elf501_intcur_base = value;
	}

	/**
	 * 取得寬限期(起)
	 * <p/>
	 * LNF033_ALLOW_BEG
	 */
	public Integer getElf501_extfrom() {
		return this.elf501_extfrom;
	}

	/**
	 * 設定寬限期(起)
	 * <p/>
	 * LNF033_ALLOW_BEG
	 **/
	public void setElf501_extfrom(Integer value) {
		this.elf501_extfrom = value;
	}

	/**
	 * 取得寬限期(迄)
	 * <p/>
	 * LNF033_ALLOW_END
	 */
	public Integer getElf501_extend() {
		return this.elf501_extend;
	}

	/**
	 * 設定寬限期(迄)
	 * <p/>
	 * LNF033_ALLOW_END
	 **/
	public void setElf501_extend(Integer value) {
		this.elf501_extend = value;
	}

	/**
	 * 取得展延種類
	 * <p/>
	 * LNF033_DEF_TYPE
	 */
	public String getElf501_exttp() {
		return this.elf501_exttp;
	}

	/**
	 * 設定展延種類
	 * <p/>
	 * LNF033_DEF_TYPE
	 **/
	public void setElf501_exttp(String value) {
		this.elf501_exttp = value;
	}

	/**
	 * 取得展延項目
	 * <p/>
	 * LNF033_DEF_ITEM
	 */
	public String getElf501_extitem() {
		return this.elf501_extitem;
	}

	/**
	 * 設定展延項目
	 * <p/>
	 * LNF033_DEF_ITEM
	 **/
	public void setElf501_extitem(String value) {
		this.elf501_extitem = value;
	}

	/**
	 * 取得本金展延起始期
	 * <p/>
	 * LNF033_PRI_DEF_BEG
	 */
	public Integer getElf501_cpextfm() {
		return this.elf501_cpextfm;
	}

	/**
	 * 設定本金展延起始期
	 * <p/>
	 * LNF033_PRI_DEF_BEG
	 **/
	public void setElf501_cpextfm(Integer value) {
		this.elf501_cpextfm = value;
	}

	/**
	 * 取得本金展延截止期
	 * <p/>
	 * LNF033_PRI_DEF_DUE
	 */
	public Integer getElf501_cpexted() {
		return this.elf501_cpexted;
	}

	/**
	 * 設定本金展延截止期
	 * <p/>
	 * LNF033_PRI_DEF_DUE
	 **/
	public void setElf501_cpexted(Integer value) {
		this.elf501_cpexted = value;
	}

	/**
	 * 取得利息展期起始期
	 * <p/>
	 * LNF033_INT_DEF_BEG
	 */
	public Integer getElf501_inextfm() {
		return this.elf501_inextfm;
	}

	/**
	 * 設定利息展期起始期
	 * <p/>
	 * LNF033_INT_DEF_BEG
	 **/
	public void setElf501_inextfm(Integer value) {
		this.elf501_inextfm = value;
	}

	/**
	 * 取得利息展延截止期
	 * <p/>
	 * LNF033_INT_DEF_DUE
	 */
	public Integer getElf501_inexted() {
		return this.elf501_inexted;
	}

	/**
	 * 設定利息展延截止期
	 * <p/>
	 * LNF033_INT_DEF_DUE
	 **/
	public void setElf501_inexted(Integer value) {
		this.elf501_inexted = value;
	}

	/**
	 * 取得應收利息攤還截止期
	 * <p/>
	 * LNF033_LST_INT_DUE
	 */
	public Integer getElf501_indline() {
		return this.elf501_indline;
	}

	/**
	 * 設定應收利息攤還截止期
	 * <p/>
	 * LNF033_LST_INT_DUE
	 **/
	public void setElf501_indline(Integer value) {
		this.elf501_indline = value;
	}

	/**
	 * 取得計收遲延利息加碼
	 * <p/>
	 * LNF033_OVDUE_PNT
	 */
	public BigDecimal getElf501_overchg() {
		return this.elf501_overchg;
	}

	/**
	 * 設定計收遲延利息加碼
	 * <p/>
	 * LNF033_OVDUE_PNT
	 **/
	public void setElf501_overchg(BigDecimal value) {
		this.elf501_overchg = value;
	}

	/** 取得逾期在個月以內部份 **/
	public Integer getElf501_overmm1() {
		return this.elf501_overmm1;
	}

	/** 設定逾期在個月以內部份 **/
	public void setElf501_overmm1(Integer value) {
		this.elf501_overmm1 = value;
	}

	/**
	 * 取得違約金計算條件１－百分比
	 * <p/>
	 * LNF033_OV_PNT
	 */
	public BigDecimal getElf501_perct1() {
		return this.elf501_perct1;
	}

	/**
	 * 設定違約金計算條件１－百分比
	 * <p/>
	 * LNF033_OV_PNT
	 **/
	public void setElf501_perct1(BigDecimal value) {
		this.elf501_perct1 = value;
	}

	/** 取得逾期超過個月部份 **/
	public Integer getElf501_overmm2() {
		return this.elf501_overmm2;
	}

	/** 設定逾期超過個月部份 **/
	public void setElf501_overmm2(Integer value) {
		this.elf501_overmm2 = value;
	}

	/** 取得違約金計算條件２－百分比 **/
	public BigDecimal getElf501_perct2() {
		return this.elf501_perct2;
	}

	/** 設定違約金計算條件２－百分比 **/
	public void setElf501_perct2(BigDecimal value) {
		this.elf501_perct2 = value;
	}

	/**
	 * 取得連動式房貸註記
	 * <p/>
	 * LNF033-OFFSET-FLAG
	 */
	public String getElf501_iscredit() {
		return this.elf501_iscredit;
	}

	/**
	 * 設定連動式房貸註記
	 * <p/>
	 * LNF033-OFFSET-FLAG
	 **/
	public void setElf501_iscredit(String value) {
		this.elf501_iscredit = value;
	}

	/**
	 * 取得提前還本管制起期
	 * <p/>
	 * LNF033_RTCAP_BEG
	 */
	public Integer getElf501_pconbeg() {
		return this.elf501_pconbeg;
	}

	/**
	 * 設定提前還本管制起期
	 * <p/>
	 * LNF033_RTCAP_BEG
	 **/
	public void setElf501_pconbeg(Integer value) {
		this.elf501_pconbeg = value;
	}

	/**
	 * 取得提前還本管制迄期
	 * <p/>
	 * LNF033_RTCAP_END
	 */
	public Integer getElf501_pconend() {
		return this.elf501_pconend;
	}

	/**
	 * 設定提前還本管制迄期
	 * <p/>
	 * LNF033_RTCAP_END
	 **/
	public void setElf501_pconend(Integer value) {
		this.elf501_pconend = value;
	}

	/**
	 * 取得提前還本違約金計算條件'
	 * <p/>
	 * LNF033_RTCAP_PNT
	 */
	public BigDecimal getElf501_calcondi() {
		return this.elf501_calcondi;
	}

	/**
	 * 設定提前還本違約金計算條件'
	 * <p/>
	 * LNF033_RTCAP_PNT
	 **/
	public void setElf501_calcondi(BigDecimal value) {
		this.elf501_calcondi = value;
	}

	/**
	 * 取得提前還本管制迄期2
	 * <p/>
	 * LNF033_RTCAP_BEG2
	 */
	public Integer getElf501_pconend2() {
		return this.elf501_pconend2;
	}

	/**
	 * 設定提前還本管制迄期2
	 * <p/>
	 * LNF033_RTCAP_BEG2
	 **/
	public void setElf501_pconend2(Integer value) {
		this.elf501_pconend2 = value;
	}

	/**
	 * 取得提前還本管制起期2
	 * <p/>
	 * LNF033_RTCAP_END2
	 */
	public Integer getElf501_pconbeg2() {
		return this.elf501_pconbeg2;
	}

	/**
	 * 設定提前還本管制起期2
	 * <p/>
	 * LNF033_RTCAP_END2
	 **/
	public void setElf501_pconbeg2(Integer value) {
		this.elf501_pconbeg2 = value;
	}

	/**
	 * 取得提前還本違約金計算條件2
	 * <p/>
	 * LNF033_RTCAP_PNT2
	 */
	public BigDecimal getElf501_calcondi2() {
		return this.elf501_calcondi2;
	}

	/**
	 * 設定提前還本違約金計算條件2
	 * <p/>
	 * LNF033_RTCAP_PNT2
	 **/
	public void setElf501_calcondi2(BigDecimal value) {
		this.elf501_calcondi2 = value;
	}

	/**
	 * 取得是否代付費用
	 * <p/>
	 * LNF033_AGENCY
	 */
	public String getElf501_istakfee() {
		return this.elf501_istakfee;
	}

	/**
	 * 設定是否代付費用
	 * <p/>
	 * LNF033_AGENCY
	 **/
	public void setElf501_istakfee(String value) {
		this.elf501_istakfee = value;
	}

	/**
	 * 取得代付費用管制迄期
	 * <p/>
	 * LNF033_AGENCY_END
	 */
	public Integer getElf501_tconend() {
		return this.elf501_tconend;
	}

	/**
	 * 設定代付費用管制迄期
	 * <p/>
	 * LNF033_AGENCY_END
	 **/
	public void setElf501_tconend(Integer value) {
		this.elf501_tconend = value;
	}

	/**
	 * 取得是否轉貸戶
	 * <p/>
	 * LNF135_CHG_CASE
	 */
	public String getElf501_chgcase() {
		return this.elf501_chgcase;
	}

	/**
	 * 設定是否轉貸戶
	 * <p/>
	 * LNF135_CHG_CASE
	 **/
	public void setElf501_chgcase(String value) {
		this.elf501_chgcase = value;
	}

	/**
	 * 取得轉貸剩餘貸款本金
	 * <p/>
	 * LNF135_CHG_BAL
	 */
	public BigDecimal getElf501_chglnbal() {
		return this.elf501_chglnbal;
	}

	/**
	 * 設定轉貸剩餘貸款本金
	 * <p/>
	 * LNF135_CHG_BAL
	 **/
	public void setElf501_chglnbal(BigDecimal value) {
		this.elf501_chglnbal = value;
	}

	/**
	 * 取得原貸款到期日
	 * <p/>
	 * LNF135_ORI_P_DATE
	 */
	public Date getElf501_oripdate() {
		return this.elf501_oripdate;
	}

	/**
	 * 設定原貸款到期日
	 * <p/>
	 * LNF135_ORI_P_DATE
	 **/
	public void setElf501_oripdate(Date value) {
		this.elf501_oripdate = value;
	}

	/**
	 * 取得轉貸日期
	 * <p/>
	 * LNF135_CHG_DATE
	 */
	public Date getElf501_chgdate() {
		return this.elf501_chgdate;
	}

	/**
	 * 設定轉貸日期
	 * <p/>
	 * LNF135_CHG_DATE
	 **/
	public void setElf501_chgdate(Date value) {
		this.elf501_chgdate = value;
	}

	/**
	 * 取得轉入之原金融機構代碼
	 * <p/>
	 * LNF135_ORI_BK_CD
	 */
	public String getElf501_oribancd() {
		return this.elf501_oribancd;
	}

	/**
	 * 設定轉入之原金融機構代碼
	 * <p/>
	 * LNF135_ORI_BK_CD
	 **/
	public void setElf501_oribancd(String value) {
		this.elf501_oribancd = value;
	}

	/**
	 * 取得轉貸前後為相同性質之政策性貸款
	 * <p/>
	 * LNF135_SAME_POLICY
	 */
	public String getElf501_same_policy() {
		return this.elf501_same_policy;
	}

	/**
	 * 設定轉貸前後為相同性質之政策性貸款
	 * <p/>
	 * LNF135_SAME_POLICY
	 **/
	public void setElf501_same_policy(String value) {
		this.elf501_same_policy = value;
	}

	/**
	 * 取得轉貸原貸款貸放日期
	 * <p/>
	 * LNF135_ORI_B_DATE
	 */
	public Date getElf501_ori_b_date() {
		return this.elf501_ori_b_date;
	}

	/**
	 * 設定轉貸原貸款貸放日期
	 * <p/>
	 * LNF135_ORI_B_DATE
	 **/
	public void setElf501_ori_b_date(Date value) {
		this.elf501_ori_b_date = value;
	}

	/**
	 * 取得代償他行房貸原因
	 * <p/>
	 * LNF135_CHG_REASON1<br/>
	 * 代償原因（買賣）<br/>
	 * LNF135_CHG_REASON2<br/>
	 * 代償原因（利率）<br/>
	 * LNF135_CHG_REASON3<br/>
	 * 代償原因（額度）
	 */
	public String getElf501_chgreason() {
		return this.elf501_chgreason;
	}

	/**
	 * 設定代償他行房貸原因
	 * <p/>
	 * LNF135_CHG_REASON1<br/>
	 * 代償原因（買賣）<br/>
	 * LNF135_CHG_REASON2<br/>
	 * 代償原因（利率）<br/>
	 * LNF135_CHG_REASON3<br/>
	 * 代償原因（額度）
	 **/
	public void setElf501_chgreason(String value) {
		this.elf501_chgreason = value;
	}

	/**
	 * 取得代償原因－其他
	 * <p/>
	 * LNF135_CHG_REASON4
	 */
	public String getElf501_chgmemo() {
		return this.elf501_chgmemo;
	}

	/**
	 * 設定代償原因－其他
	 * <p/>
	 * LNF135_CHG_REASON4
	 **/
	public void setElf501_chgmemo(String value) {
		this.elf501_chgmemo = value;
	}

	/**
	 * 取得扣帳方式
	 * <p/>
	 * LNF033_CREDIT_INS<br/>
	 * 00:一般<br/>
	 * 01:ACH 扣帳
	 */
	public String getElf501_paytype() {
		return this.elf501_paytype;
	}

	/**
	 * 設定扣帳方式
	 * <p/>
	 * LNF033_CREDIT_INS<br/>
	 * 00:一般<br/>
	 * 01:ACH 扣帳
	 **/
	public void setElf501_paytype(String value) {
		this.elf501_paytype = value;
	}

	/**
	 * 取得選擇權項目
	 * <p/>
	 * LNF033_MATCH_ITEM
	 */
	public String getElf501_uptype() {
		return this.elf501_uptype;
	}

	/**
	 * 設定選擇權項目
	 * <p/>
	 * LNF033_MATCH_ITEM
	 **/
	public void setElf501_uptype(String value) {
		this.elf501_uptype = value;
	}

	/**
	 * 取得選擇權起期
	 * <p/>
	 * LNF033_UP_BEG
	 */
	public Integer getElf501_upbeg() {
		return this.elf501_upbeg;
	}

	/**
	 * 設定選擇權起期
	 * <p/>
	 * LNF033_UP_BEG
	 **/
	public void setElf501_upbeg(Integer value) {
		this.elf501_upbeg = value;
	}

	/**
	 * 取得選擇權迄期
	 * <p/>
	 * LNF033_UP_END
	 */
	public Integer getElf501_upend() {
		return this.elf501_upend;
	}

	/**
	 * 設定選擇權迄期
	 * <p/>
	 * LNF033_UP_END
	 **/
	public void setElf501_upend(Integer value) {
		this.elf501_upend = value;
	}

	/**
	 * 取得選擇權利率
	 * <p/>
	 * LNF033_UP_RATE
	 */
	public BigDecimal getElf501_uprate() {
		return this.elf501_uprate;
	}

	/**
	 * 設定選擇權利率
	 * <p/>
	 * LNF033_UP_RATE
	 **/
	public void setElf501_uprate(BigDecimal value) {
		this.elf501_uprate = value;
	}

	/**
	 * 取得省息遞減註記
	 * <p/>
	 * LNF033_DEC_FLAG
	 */
	public String getElf501_decflag() {
		return this.elf501_decflag;
	}

	/**
	 * 設定省息遞減註記
	 * <p/>
	 * LNF033_DEC_FLAG
	 **/
	public void setElf501_decflag(String value) {
		this.elf501_decflag = value;
	}

	/**
	 * 取得利率方案
	 * <p/>
	 * LNF033_RATE_PLAN
	 */
	public String getElf501_rate_plan() {
		return this.elf501_rate_plan;
	}

	/**
	 * 設定利率方案
	 * <p/>
	 * LNF033_RATE_PLAN
	 **/
	public void setElf501_rate_plan(String value) {
		this.elf501_rate_plan = value;
	}

	/**
	 * 取得產品方案
	 * <p/>
	 * LNF033_PROD_PLAN
	 */
	public String getElf501_prod_plan() {
		return this.elf501_prod_plan;
	}

	/**
	 * 設定產品方案
	 * <p/>
	 * LNF033_PROD_PLAN
	 **/
	public void setElf501_prod_plan(String value) {
		this.elf501_prod_plan = value;
	}

	/**
	 * 取得借款繳保費之金額
	 * <p/>
	 * LNF033_INSC_AMT
	 */
	public BigDecimal getElf501_insc_amt() {
		return this.elf501_insc_amt;
	}

	/**
	 * 設定借款繳保費之金額
	 * <p/>
	 * LNF033_INSC_AMT
	 **/
	public void setElf501_insc_amt(BigDecimal value) {
		this.elf501_insc_amt = value;
	}

	/**
	 * 取得是否搭配房貸壽險(Y/N)
	 * <p/>
	 * LNF033_RMBINS_FLAG
	 */
	public String getElf501_rmbins_flag() {
		return this.elf501_rmbins_flag;
	}

	/**
	 * 設定是否搭配房貸壽險(Y/N)
	 * <p/>
	 * LNF033_RMBINS_FLAG
	 **/
	public void setElf501_rmbins_flag(String value) {
		this.elf501_rmbins_flag = value;
	}

	/**
	 * 取得是否搭配房貸壽險利率優惠方案(Y/N)
	 * <p/>
	 * LNF033_RMBINT_FLAG
	 */
	public String getElf501_rmbint_flag() {
		return this.elf501_rmbint_flag;
	}

	/**
	 * 設定是否搭配房貸壽險利率優惠方案(Y/N)
	 * <p/>
	 * LNF033_RMBINT_FLAG
	 **/
	public void setElf501_rmbint_flag(String value) {
		this.elf501_rmbint_flag = value;
	}

	/**
	 * 取得搭配房貸壽險利率優惠方案之期數(999)
	 * <p/>
	 * LNF033_RMBINT_TERM
	 */
	public Integer getElf501_rmbint_term() {
		return this.elf501_rmbint_term;
	}

	/**
	 * 設定搭配房貸壽險利率優惠方案之期數(999)
	 * <p/>
	 * LNF033_RMBINT_TERM
	 **/
	public void setElf501_rmbint_term(Integer value) {
		this.elf501_rmbint_term = value;
	}

	/**
	 * 取得是否保費融資(Y/N)
	 * <p/>
	 * LNF033_INS_FLAG
	 */
	public String getElf501_ins_flag() {
		return this.elf501_ins_flag;
	}

	/**
	 * 設定是否保費融資(Y/N)
	 * <p/>
	 * LNF033_INS_FLAG
	 **/
	public void setElf501_ins_flag(String value) {
		this.elf501_ins_flag = value;
	}

	/**
	 * 取得保費融資金額
	 * <p/>
	 * LNF033_INS_LOANBAL
	 */
	public BigDecimal getElf501_ins_loanbal() {
		return this.elf501_ins_loanbal;
	}

	/**
	 * 設定保費融資金額
	 * <p/>
	 * LNF033_INS_LOANBAL
	 **/
	public void setElf501_ins_loanbal(BigDecimal value) {
		this.elf501_ins_loanbal = value;
	}

	/**
	 * 取得勞貸中籤編號
	 * <p/>
	 * LNF033_LNUM
	 */
	public String getElf501_asgnno() {
		return this.elf501_asgnno;
	}

	/**
	 * 設定勞貸中籤編號
	 * <p/>
	 * LNF033_LNUM
	 **/
	public void setElf501_asgnno(String value) {
		this.elf501_asgnno = value;
	}

	/**
	 * 取得勞貸中籤年份
	 * <p/>
	 * LNF033_PLAN_YEAR
	 */
	public String getElf501_asgnyy() {
		return this.elf501_asgnyy;
	}

	/**
	 * 設定勞貸中籤年份
	 * <p/>
	 * LNF033_PLAN_YEAR
	 **/
	public void setElf501_asgnyy(String value) {
		this.elf501_asgnyy = value;
	}

	/**
	 * 取得勞貸收件編號
	 * <p/>
	 * LNF033_RECV_NO
	 */
	public String getElf501_revno() {
		return this.elf501_revno;
	}

	/**
	 * 設定勞貸收件編號
	 * <p/>
	 * LNF033_RECV_NO
	 **/
	public void setElf501_revno(String value) {
		this.elf501_revno = value;
	}

	/**
	 * 取得輔購核准年度
	 * <p/>
	 * LNF033_CITY_YYY
	 */
	public String getElf501_aprvyy() {
		return this.elf501_aprvyy;
	}

	/**
	 * 設定輔購核准年度
	 * <p/>
	 * LNF033_CITY_YYY
	 **/
	public void setElf501_aprvyy(String value) {
		this.elf501_aprvyy = value;
	}

	/**
	 * 取得輔購名冊編號
	 * <p/>
	 * LNF033_CITY_NO
	 */
	public String getElf501_listno() {
		return this.elf501_listno;
	}

	/**
	 * 設定輔購名冊編號
	 * <p/>
	 * LNF033_CITY_NO
	 **/
	public void setElf501_listno(String value) {
		this.elf501_listno = value;
	}

	/**
	 * 取得內政部整合住宅方案'
	 * <p/>
	 * LNF033_CPAMI_NO
	 */
	public String getElf501_allowno() {
		return this.elf501_allowno;
	}

	/**
	 * 設定內政部整合住宅方案'
	 * <p/>
	 * LNF033_CPAMI_NO
	 **/
	public void setElf501_allowno(String value) {
		this.elf501_allowno = value;
	}

	/**
	 * 取得安心成家貸款申請編號'
	 * <p/>
	 * LNF033_APPLY_NO
	 */
	public String getElf501_apply_no() {
		return this.elf501_apply_no;
	}

	/**
	 * 設定安心成家貸款申請編號'
	 * <p/>
	 * LNF033_APPLY_NO
	 **/
	public void setElf501_apply_no(String value) {
		this.elf501_apply_no = value;
	}

	/**
	 * 取得高市首購核准編號
	 * <p/>
	 * LNF033_KG_AGREE_NO
	 */
	public String getElf501_kg_agree_no() {
		return this.elf501_kg_agree_no;
	}

	/**
	 * 設定高市首購核准編號
	 * <p/>
	 * LNF033_KG_AGREE_NO
	 **/
	public void setElf501_kg_agree_no(String value) {
		this.elf501_kg_agree_no = value;
	}

	/**
	 * 取得高市首購核准日期
	 * <p/>
	 * LNF033_KG_AGREE_DT
	 */
	public Date getElf501_kg_agree_dt() {
		return this.elf501_kg_agree_dt;
	}

	/**
	 * 設定高市首購核准日期
	 * <p/>
	 * LNF033_KG_AGREE_DT
	 **/
	public void setElf501_kg_agree_dt(Date value) {
		this.elf501_kg_agree_dt = value;
	}

	/**
	 * 取得高市終止補貼日
	 * <p/>
	 * LNF033_KG_END_DATE
	 */
	public Date getElf501_kg_end_date() {
		return this.elf501_kg_end_date;
	}

	/**
	 * 設定高市終止補貼日
	 * <p/>
	 * LNF033_KG_END_DATE
	 **/
	public void setElf501_kg_end_date(Date value) {
		this.elf501_kg_end_date = value;
	}

	/**
	 * 取得高市終止補貼原因
	 * <p/>
	 * LNF033_KG_END_CODE
	 */
	public String getElf501_kg_end_code() {
		return this.elf501_kg_end_code;
	}

	/**
	 * 設定高市終止補貼原因
	 * <p/>
	 * LNF033_KG_END_CODE
	 **/
	public void setElf501_kg_end_code(String value) {
		this.elf501_kg_end_code = value;
	}

	/**
	 * 取得高市首購補貼起日
	 * <p/>
	 * LNF033_KG_INT_DATE
	 */
	public Date getElf501_kg_int_date() {
		return this.elf501_kg_int_date;
	}

	/**
	 * 設定高市首購補貼起日
	 * <p/>
	 * LNF033_KG_INT_DATE
	 **/
	public void setElf501_kg_int_date(Date value) {
		this.elf501_kg_int_date = value;
	}

	/**
	 * 取得外勞仲介公司統編
	 * <p/>
	 * LNF033_AGENCY_ID
	 */
	public String getElf501_intrid() {
		return this.elf501_intrid;
	}

	/**
	 * 設定外勞仲介公司統編
	 * <p/>
	 * LNF033_AGENCY_ID
	 **/
	public void setElf501_intrid(String value) {
		this.elf501_intrid = value;
	}

	/**
	 * 取得外勞仲介獎金比例
	 * <p/>
	 * LNF033_AGEN_RATIO
	 */
	public BigDecimal getElf501_intrrate() {
		return this.elf501_intrrate;
	}

	/**
	 * 設定外勞仲介獎金比例
	 * <p/>
	 * LNF033_AGEN_RATIO
	 **/
	public void setElf501_intrrate(BigDecimal value) {
		this.elf501_intrrate = value;
	}

	/**
	 * 取得計算擔保維持率註記
	 * <p/>
	 * LNF030_STOCK_RATE
	 */
	public String getElf501_stock_rate() {
		return this.elf501_stock_rate;
	}

	/**
	 * 設定計算擔保維持率註記
	 * <p/>
	 * LNF030_STOCK_RATE
	 **/
	public void setElf501_stock_rate(String value) {
		this.elf501_stock_rate = value;
	}

	/**
	 * 取得開戶原因
	 * <p/>
	 * LNF030-TRANS-CODE<br/>
	 * 0:一舨案<br/>
	 * 1: 催收轉正<br/>
	 * 2: 更正前次撥款<br/>
	 * 3: 繼承案件<br/>
	 * 4: 受贈土地之土地增值稅融資<br/>
	 * 5: 遺產稅融資<br/>
	 * 6: 納稅義務人為受贈人之贈與稅融資<br/>
	 * 7: 借款人擔保品被政府徵收而改列無擔保之放款
	 */
	public String getElf501_trans_code() {
		return this.elf501_trans_code;
	}

	/**
	 * 設定開戶原因
	 * <p/>
	 * LNF030-TRANS-CODE<br/>
	 * 0:一舨案<br/>
	 * 1: 催收轉正<br/>
	 * 2: 更正前次撥款<br/>
	 * 3: 繼承案件<br/>
	 * 4: 受贈土地之土地增值稅融資<br/>
	 * 5: 遺產稅融資<br/>
	 * 6: 納稅義務人為受贈人之贈與稅融資<br/>
	 * 7: 借款人擔保品被政府徵收而改列無擔保之放款
	 **/
	public void setElf501_trans_code(String value) {
		this.elf501_trans_code = value;
	}

	/**
	 * 取得承接之前放款帳號'
	 * <p/>
	 * LNF030-ORG-LOAN-NO<br/>
	 * (當ELF501_TRANS_CODE = 123時才需要輸入此欄位)
	 */
	public String getElf501_org_loan_no() {
		return this.elf501_org_loan_no;
	}

	/**
	 * 設定承接之前放款帳號'
	 * <p/>
	 * LNF030-ORG-LOAN-NO<br/>
	 * (當ELF501_TRANS_CODE = 123時才需要輸入此欄位)
	 **/
	public void setElf501_org_loan_no(String value) {
		this.elf501_org_loan_no = value;
	}

	/**


//
//	/** 取得資料修改人 **/
//	public String getElf501_updater() {
//		return this.elf501_updater;
//	}
//
//	/** 設定資料修改人 **/
//	public void setElf501_updater(String value) {
//		this.elf501_updater = value;
//	}

	/** 取得資料修改日期 **/
	public Timestamp getElf501_tmestamp() {
		return this.elf501_tmestamp;
	}

	/** 設定資料修改日期 **/
	public void setElf501_tmestamp(Timestamp value) {
		this.elf501_tmestamp = value;
	}
	/** 取得eloantimes **/
	public Timestamp getElf501_eloantimes() {
		return this.elf501_eloantimes;
	}

	/** 設定eloantimes **/
	public void setElf501_eloantimes(Timestamp value) {
		this.elf501_eloantimes = value;
	}
	
	
	/** 取得櫃員代號 **/
	public String getElf501_teller() {
		return this.elf501_teller;
	}

	/** 設定櫃員代號  **/
	public void setElf501_teller(String value) {
		this.elf501_teller = value;
	}

	/**
	 * 取得帳號(科目)限額
	 * <p/>
	 */
	public BigDecimal getElf501_lnap_amt() {
		return this.elf501_lnap_amt;
	}
	/**
	 * 設定帳號(科目)限額
	 * <p/>
	 */
	public void setElf501_lnap_amt(BigDecimal value) {
		this.elf501_lnap_amt=value;
	}
	
	/** 取得主管代號 **/
	public String getElf501_supvno() {
		return this.elf501_supvno;
	}

	/** 設定主管代號 **/
	public void setElf501_supvno(String value) {
		this.elf501_supvno = value;
	}
			
	/** 取得扣稅負擔值 **/
	public BigDecimal getElf501_taxrate() {
		return this.elf501_taxrate;
	}
	/** 設定扣稅負擔值 **/
	public void setElf501_taxrate(BigDecimal value) {
		this.elf501_taxrate = value;
	}

	/** 取得開辦費 **/
	public BigDecimal getElf501_agency_amt() {
		return this.elf501_agency_amt;
	}
	/** 設定開辦費 **/
	public void setElf501_agency_amt(BigDecimal value) {
		this.elf501_agency_amt= value;
	}
	
	/** 取得A-Loan 執行時間 OnLntime **/
	public Timestamp getElf501_onlntime() {
		return this.elf501_onlntime;
	}

	/** 設定A-Loan 執行時間 OnLntime **/
	public void setElf501_onlntime(Timestamp value) {
		this.elf501_onlntime = value;
	}
	
	/** 取得A-Loan 執行時間 Batch **/
	public Timestamp getElf501_bthtime() {
		return this.elf501_bthtime;
	}

	/** 設定A-Loan 執行時間 Batch **/
	public void setElf501_bthtime(Timestamp value) {
		this.elf501_bthtime = value;
	}
	
	/** 取得評等**/
	public String getElf501_grade() {
		return this.elf501_grade;
	}

	/** 設定評等  **/
	public void setElf501_grade(String value) {
		this.elf501_grade = value;
	}

	/** 設定縣(市)政府首購貸款 **/
	public void setElf501_kg_area(String value) {
		this.elf501_kg_area = value;
	}

	/** 取得縣(市)政府首購貸款 **/
	public String getElf501_kg_area() {
		return elf501_kg_area;
	}
	
	/** 設定透支計息方式：0-透支End, 1-透支Top **/
	public void setElf501_ovbal_type(String value) {
		this.elf501_ovbal_type = value;
	}
	/** 取得透支計息方式：0-透支End, 1-透支Top **/
	public String getElf501_ovbal_type() {
		return elf501_ovbal_type;
	}

	/** 設定契約種類：1-購屋契約, 2-純信用無擔保(DBR22倍), 3-其他類契約, 4-歡喜樂活貸款契約書(以房養老)<br/>
	 * 按月計息: ?<br/>
	 * 期付金: LNF033_CNTRNO_TYPE
	 */
	public void setElf501_cntrno_type(String value) {
		this.elf501_cntrno_type = value;
	}
	/** 取得契約種類：1-購屋契約, 2-純信用無擔保(DBR22倍), 3-其他類契約, 4-歡喜樂活貸款契約書(以房養老)<br/>
	 * 按月計息: ?<br/>
	 * 期付金: LNF033_CNTRNO_TYPE
	 */
	public String getElf501_cntrno_type() {
		return elf501_cntrno_type;
	}

	/** 設定天然及重大災害種類 **/
	public void setElf501_disas_type(String value) {
		this.elf501_disas_type = value;
	}
	/** 取得天然及重大災害種類 **/
	public String getElf501_disas_type() {
		return elf501_disas_type;
	}

	/** 取得房貸引介子公司代號{90001:兆豐金控, 90002:	兆豐產險, 90003:	兆豐證券, 90004:	兆豐投信
	, 90005:兆豐資產管理, 90006:兆豐人身保代, 90007:兆豐創投, 90008:兆豐票券} **/
	public String getElf501_mega_code() {
		return elf501_mega_code;
	}
	/** 設定房貸引介子公司代號{90001:兆豐金控, 90002:	兆豐產險, 90003:	兆豐證券, 90004:	兆豐投信
	, 90005:兆豐資產管理, 90006:兆豐人身保代, 90007:兆豐創投, 90008:兆豐票券} **/
	public void setElf501_mega_code(String elf501_mega_code) {
		this.elf501_mega_code = elf501_mega_code;
	}

	/** 取得引介房仲收取回饋金  **/
	public String getElf501_agnt_fbfg() {
		return elf501_agnt_fbfg;
	}
	/** 設定引介房仲收取回饋金  **/
	public void setElf501_agnt_fbfg(String elf501_agnt_fbfg) {
		this.elf501_agnt_fbfg = elf501_agnt_fbfg;
	}

	/** 取得週轉金用途細項 */
	public String getElf501_lnpurs_sub() {
		return elf501_lnpurs_sub;
	}
	/** 設定週轉金用途細項 */
	public void setElf501_lnpurs_sub(String elf501_lnpurs_sub) {
		this.elf501_lnpurs_sub = elf501_lnpurs_sub;
	}

	/** 取得首次還款日 */
	public Date getElf501_1st_rt_dt() {
		return elf501_1st_rt_dt;
	}
	/** 設定首次還款日 */
	public void setElf501_1st_rt_dt(Date elf501_1st_rt_dt) {
		this.elf501_1st_rt_dt = elf501_1st_rt_dt;
	}

	/** 取得綠色支出類型 */ 
	public String getElf501_esggtype() {
		return elf501_esggtype;
	}
	/** 設定綠色支出類型 */ 
	public void setElf501_esggtype(String elf501_esggtype) {
		this.elf501_esggtype = elf501_esggtype;
	}

	public String getElf501_socialKind() {
		return elf501_socialKind;
	}

	public void setElf501_socialKind(String elf501_socialKind) {
		this.elf501_socialKind = elf501_socialKind;
	}

	public String getElf501_socialTa() {
		return elf501_socialTa;
	}

	public void setElf501_socialTa(String elf501_socialTa) {
		this.elf501_socialTa = elf501_socialTa;
	}

	public String getElf501_socialResp() {
		return elf501_socialResp;
	}

	public void setElf501_socialResp(String elf501_socialResp) {
		this.elf501_socialResp = elf501_socialResp;
	}

}
