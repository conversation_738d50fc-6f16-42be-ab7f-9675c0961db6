package tw.com.iisi.cap.base.attr;

/**
 * <pre>
 * 處理Java動態產生的JavaScript
 * </pre>
 */
public class JavascriptAttr {

    /**
     * JavaScript內容
     */
    private String content;

    /**
     * 該段Script識別
     */
    private String id;

    /**
     * 建構子
     * 
     * @param content
     *            JavaScript內容
     * @param id
     *            該段Script識別
     */
    public JavascriptAttr(String content, String id) {
        super();
        this.content = content;
        this.id = id;
    }

    /**
     * 建構子
     */
    public JavascriptAttr() {
        super();
    }

    /**
     * 取得Script內容
     * 
     * @return
     */
    public String getContent() {
        return content;
    }

    /**
     * 設置Script內容
     * 
     * @param content
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * 取得該段Script識別
     * 
     * @return
     */
    public String getId() {
        return id;
    }

    /**
     * 設置該段Script識別
     * 
     * @param id
     */
    public void setId(String id) {
        this.id = id;
    }

}
