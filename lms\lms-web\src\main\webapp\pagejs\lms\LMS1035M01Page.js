var initDfd = initDfd || $.Deferred();	
var _handler = "lms1035m01formhandler";
var __bef_c120m01a_oid = "bef_c120m01a_oid";

var printGrid_height = 200;

$(document).ready(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	var initControl_lockDoc = false;
	$.form.init({
		formHandler:_handler, 
		formAction:'query', 
		loadSuccess:function(json){			
			
			// 控制頁面 Read/Write
			tabForm.buildItem();		
			
			if(json.page=="02"){
				$("#chooseC120M01ACustPosForm").buildItem();
			}else if(json.page=="04"){
				
				$.ajax({
					type : "POST", handler : "lms1015m01formhandler", async: false,//用「同步」的方式
					data : {
						formAction : "codeTypeWithOrder" ,
						key : "c121s01a_cmsType_TH"
					}
				}).done(function(obj){
						var chooseItem = $("#cmsType");
						var _addSpace = false;
						if(chooseItem.attr("space")=="true"){
							_addSpace = true;	
						}
						
						$.each(obj.itemOrder, function(idx, brNo) {
							var currobj = {};
							var brName = obj.item[brNo];
							currobj[brNo] = brName;
					
							//select
							chooseItem.setItems({ item: currobj, format: "{key}", clear:false, space: (_addSpace?(idx==0):false) });
						});
					});
				
			}
			
			tabForm.injectData(json);
			
			if(!$("#buttonPanel").find("#btnSave").is("button") || json.lock) {
				tabForm.lockDoc();
				initControl_lockDoc = true;
				json['initControl_lockDoc'] = initControl_lockDoc;
			}
			
			initDfd.resolve(json);	
	}});
	
	btnPanel.find("#btnSave").click(function(){		
		saveAction({'allowIncomplete':'Y'}).done(function(json){
			if(json.saveOkFlag){
				if(json.IncompleteMsg){
					API.showMessage(i18n.def.saveSuccess+"<br/>-------------------<br/>"+json.IncompleteMsg);
				}else{
					API.showMessage(i18n.def.saveSuccess);	
				}	
			}
        });
	}).end().find("#btnSend").click(function(){	
		saveAction().done(function(json_saveAction){
    		if(json_saveAction.saveOkFlag){
    			API.confirmMessage(i18n.def.confirmApply, function(result){
    	            if (result) {
    	            	flowAction({'decisionExpr':'呈主管'});	
    	        	}
    	    	});
    		}
    	});
	}).end().find("#btnAccept").click(function(){
		var _id = "_div_btnAccept";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");

			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.def['accept']+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.def['return']+"</label></p>");

			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380,
            height: 180,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    if(val=="1"){
                    	flowAction({'decisionExpr':'核定'});
                    }else if(val=="2"){
                    	flowAction({'decisionExpr':'退回'});
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	}).end().find("#btnReturnToCompiling").click(function(){
		API.confirmMessage(i18n.def.confirmReturn, function(result){
            if (result) {
            	flowAction({'decisionExpr':'退回'});
        	}
    	});
	}).end().find("#btnPrint").click(function(){
		if(initControl_lockDoc) {
			do_print();
		}else{
			saveAction({'allowIncomplete':'Y'}).done(function(json){
				if(json.saveOkFlag){					
					do_print();
				}
	        });
		}
	});	
		
	$("#printGrid").iGrid({
		handler : 'lms1035gridhandler',
		height : printGrid_height,
		action :  'queryPrint',
		postData: {
			formAction: "queryPrint",
            mainId: responseJSON.mainId
        },
        needPager: false,        
        shrinkToFit: false,
        multiselect: true,     
		colModel : [{ name : 'oid', hidden : true}
		,{name : 'mainId',hidden : true}
		,{name : 'type',hidden : true}
		,{colHeader : i18n.lms1035m01['print.rptName'], width : 150, name : 'desc1', sortable : false}
		,{colHeader : '&nbsp;', width : 300, name : 'desc2', sortable : false}
		]
	});
	var flowAction = function(opts){
		return $.ajax({
            type: "POST",
            handler: _handler, action: "flowAction",
            data:$.extend( {
            	mainOid: $("#mainOid").val(), 
            	mainDocStatus: $("#mainDocStatus").val() 
                }
                , ( opts||{} )
            )
        }).done(function(json){            	
            	API.triggerOpener();//gridview.reloadGrid 
            	window.close();            	
            });
	}
	
});
/*
$.extend(window.tempSave,{
	handler: _handler, // handler 名稱
	action: "tempSave", // action Method
	beforeCheck:function(){ // return false or true
		if(responseJSON.page == "02"){
			var count=$("#c120m01agrid").jqGrid('getGridParam','records');
			if(count == 0){
				CommonAPI.showErrorMessage(i18n.lms1035m01('l120m01a.error24', {'colName': ''}));				
				return false;
			}
    	}
		return $("#tabForm").valid();
	},sendData:function(){ // 需上送之資料集合(Map<String,String>)
		var opts = {}
		if(responseJSON.page == "05"||responseJSON.page == "06"){
			opts[__bef_c120m01a_oid] = $('#c120_id_list').val();
		}
		return $.extend($("#tabForm").serializeData(), opts);
	}
});
*/
//UPGRADE
$.extend(window.tempSave, {
    handler: _handler,            // 後端 Handler 名稱，跟你現場保持一致
    action: "tempSave",           // 呼叫後端的方法
    beforeCheck: function() {
        // 先檢核當前是哪一個分頁(page)
        if (responseJSON.page === "02") {
            // 如果是第 02 頁，就要檢查 c120m01agrid 裡至少要有一筆資料
            var count = $("#c120m01agrid").jqGrid('getGridParam', 'records');
            if (count == 0) {
                CommonAPI.showErrorMessage(
                  i18n.lms1035m01('l120m01a.error24', { 'colName': '' })
                );
                return false;
            }
        }
        // 其他頁面就用 #tabForm 做標準 jQuery validate
        return $("#tabForm").valid();
    },
    sendData: function() {
        // 先把當前哪幾個分頁需要額外帶參數放到 opts 裡
        var opts = {};
        if (responseJSON.page === "05" || responseJSON.page === "06") {
            // 如果當前是第 05 或 06 頁，就要送 __bef_c120m01a_oid 這個 key
            opts[__bef_c120m01a_oid] = $('#c120_id_list').val();
        }

        // 最後把必須帶給後端的參數全寫進來
        // 1) formAction: "tempSave"
        // 2) txCode, page 都從 responseJSON 拿
        // 3) mainOid (假設你在 html 裡有一個 <input id="mainOid">)
        // 4) docUrl (若後端需要，目前示範使用 /lms/lms1035v00c，你可依實際改)
        // 5) 把整張 #tabForm 序列化後的資料 JSON 化，放到 tabForm 這個 key
        // 6) 再把 $("#tabForm").serializeData() 解開，讓後端能直接取到 individual 欄位值
        return $.extend(
            {
                formAction   : "tempSave",
                txCode       : responseJSON.txCode,
                page         : responseJSON.page,
                mainOid      : $("#mainOid").val(),
                docUrl       : "/lms/lms1035v00c",
                tabForm      : JSON.stringify($("#tabForm").serializeData())
            },
            $("#tabForm").serializeData(),
            opts
        );
    }
});

function do_print(){	
	$("#printGrid").jqGrid("setGridParam", {
        postData: {
            formAction: "queryPrint",
            mainId: responseJSON.mainId
        },
        search: true
    }).trigger("reloadGrid");
	//==============
	var _id = "divPrint";
	$("#"+_id).thickbox({
        title: '', width: 520, height: (printGrid_height+150), align: "center", valign: "bottom",
        modal: false, i18n: i18n.def,
     buttons: {
         "print": function(){
        	 var printGrid = $("#printGrid");
        	 var rowId_arr = printGrid.getGridParam('selarrrow');
        	 var oid_arr = [];
        	 /**
         	 * 同時印多份: 
         	 * oid_arr.push(data.oid+"^"+data.mainId);
         	 * rptOid: oid_arr.join("|")
         	 */    
        	 for (var i = 0; i < rowId_arr.length; i++) {
     			var data = printGrid.getRowData(rowId_arr[i]);
     			
     			oid_arr.push(data.oid+"^"+data.type);
             }
        	 if (oid_arr.length == 0) {
                 CommonAPI.showMessage(i18n.def['grid.selrow']);
                 return
             }
        	 $.form.submit({
      	        url: "../../simple/FileProcessingService",
      	        target: "_blank",
      	        data: {
      	            'rptOid': oid_arr.join("|"),
      	            'fileDownloadName': "lms1035.pdf",
      	            serviceName: "lms1035rptservice"            
      	        }
      	     });	                    
         },
         "cancel": function(){
             $.thickbox.close();
         }
     }
    });
}

function saveAction(opts){
	var tabForm = $("#tabForm");
	if(tabForm.valid()){
		var optsPage = {};
		if(responseJSON.page == "05"||responseJSON.page == "06"){
			optsPage[__bef_c120m01a_oid] = $('#c120_id_list').val();
		}
		
		return $.ajax({
            type: "POST",
            handler: _handler,
            data:$.extend( {
            	formAction: "saveMain",
                page: responseJSON.page,
                mainOid: responseJSON.mainOid
                }, 
                tabForm.serializeData(),
                optsPage,
                ( opts||{} )
            )
        }).done(function(json){
            	tabForm.injectData(json);
            	
            	//更新 opener 的 Grid
                CommonAPI.triggerOpener("gridview", "reloadGrid");
            });
	}else{
		return $.Deferred();
	}
}

function enable_c120_id_list(){
	$("#c120_id_list").removeAttr("disabled");
}

function build_borrower_list(json){
	if(json.c120m01a_list){
		var dyna = []
		var size = json.c120m01a_list.key.length;
		if(size>1){
			dyna.push("<table border='0'>");
			dyna.push("<tr><td class='text-red rt'>"+i18n.lms1035m01['msg.006']+"</td></tr>");
			dyna.push("<tr><td>");	
		}
		dyna.push("<select id='c120_id_list' style='background-color:#C9E4CA'>");
		$.each(json.c120m01a_list.key, function(idx, jsonItem) {
			dyna.push("<option value='"+jsonItem.oid+"'>"+jsonItem.custId+"-"+jsonItem.dupNo+" "+jsonItem.custName+"</option>");
		});	
		dyna.push("</select>");		
		if(size>1){
			dyna.push("</td></tr>");
			dyna.push("</table>");
		}
		$("#borrower_list").html(dyna.join(""));
	}
}

function initAtPage56(json){
	build_borrower_list(json);
	//來自 build_borrower_list(...)
	if(true){
		if(json.c120m01a_oid_m){
			//default 顯示主借人的資料
			$("#c120_id_list").val( json.c120m01a_oid_m );//.trigger('change');
		}
		
		$("#c120_id_list").data(__bef_c120m01a_oid, $("#c120_id_list").val());
		
		//不用 trigger('change') 而用 loadC121M... 是為了配合 切換 ID時
		//要先save原資料，再load新資料
		loadC121M_items($("#c120_id_list").val());	
	}
		
	$("#c120_id_list").change(function(){
		var pre_oid = $(this).data(__bef_c120m01a_oid);
		var c120m01a_oid = $(this).val();
		$(this).data(__bef_c120m01a_oid, c120m01a_oid);
		
		saveC121M_items(pre_oid)
		.done(function(){
			if(true){
				loadC121M_items(c120m01a_oid);
		    }	
		})
		.fail(function(){
			//故意在 A 輸入升3等，於selectBox 選擇B
			$("#c120_id_list").val(pre_oid);
			$("#c120_id_list").data(__bef_c120m01a_oid, $("#c120_id_list").val());			
		});	    
	});
}
function saveC121M_items(pre_oid){
	if(!$("#buttonPanel").find("#btnSave").is("button")){
		ilog.debug("noNeedToSave[pre_oid]"+pre_oid);
		var my_dfd = $.Deferred();    	
    	my_dfd.resolve();  		
    	return my_dfd.promise();
		
	}else{
		ilog.debug("save[pre_oid]"+pre_oid);
		var opts = {};
		opts[__bef_c120m01a_oid] = pre_oid;
		
		return $.ajax({
	        type: "POST",
	        handler: _handler, action: "tempSave",
	        data:$.extend($("#tabForm").serializeData(), opts)
	    }).done(function(json){});	
	}
}
function loadC121M_items(c120m01a_oid){
	ilog.debug("loadC121M_items: "+c120m01a_oid);
	if(responseJSON.page=='05' || responseJSON.page=='06'){
		//若用 setValue 時，會把 selectBox 的目前 CustId 也一併清空
		//$("#tabForm").setValue();
		var cleanJSON = {};
		$.each($("#tabForm").serializeData(), function(colName, colVal){
			cleanJSON[colName] = '';
		});
		$("#tabForm").setValue(cleanJSON, false);
	}
	$.ajax({
        type: "POST",
        handler: _handler, action: "loadC121M_items",
        data:{
        	'c120m01a_oid': c120m01a_oid, 
        	'mainOid': responseJSON.mainOid,
        	page: responseJSON.page 
        }
    }).done(function(json){            	
        	$("#tabForm").injectData(json);        	
        	if(responseJSON.page=='06'){
        		var debugMsg = "ajax:loadC121M_items:success";
        		var val = json.noAdj;
    			if(val=="1"){						
    				isS06_show(false, debugMsg);
    			}else if(val=="2"){
    				isS06_show(true, debugMsg);
    			}else{
    				isS06_show(true, debugMsg);
    			}
        	}
        	//即使在 readonly 狀態，也要可檢視不同身分的 custId
        	enable_c120_id_list();
        });		
}

function calc_C121_score(){
	$.ajax({
        type: "POST",
        handler: _handler, action: "calc_C121_score",
        data:{
        	'mainOid': responseJSON.mainOid,        	
        	page: responseJSON.page 
        }
    }).done(function(json){});		
}

function isS06_show(isShow, debugMsg){
	if(isShow){
		$("tr.tr_adjustStatus12").show();
		
		if(true){
			/*
			 * 之前沒有加以下的程式
			 * [編製中]有正常運作
			 * 但[待覆核、已核准]有升等的case,未呈現[ ]淨資產[ ] 職業 [ ]其它 
			 */
			$('input[type=radio][name=adjustStatus]:checked').trigger('change');
			$('input[type=radio][name=adjustFlag]:checked').trigger('change');	
		}
	}else{
		$("tr.tr_adjustStatus12").hide();
	}
}


function alwaysConfirmAdjReason(cnt, obj){
	var my_dfd = $.Deferred();
	if(cnt=="0"){
		my_dfd.resolve();
	}else{	
		if(true){
			$("#adjustReasonAlwaysCfmMsg").html(obj.alwaysCfmStr);
		}
		$("#divAdjustReasonAlwaysCfmMsg").thickbox({
	        title: "", width: 550, height: 180,
	        align: "center", valign: "bottom", modal: false,
	        i18n: (obj || i18n.def),
	        buttons: {
	        	"alwaysCfmN": function(){
	                $.thickbox.close();
	                my_dfd.reject();
	            },
	            "alwaysCfmY": function(){
	            	//=============
	                $.thickbox.close();
	            	my_dfd.resolve();
	            }
	        }
	    });	
	}		
	return my_dfd.promise(); 
}
function procCfmMsg(obj){
	var my_dfd = $.Deferred();
	
	if((obj.cfmStr||"")==""){
		my_dfd.resolve();
	}else{		
		if(true){
			$("#adjustReasonCfmMsg").html(obj.cfmStr);
		}
		$("#divAdjustReasonCfmMsg").thickbox({
	        title: "", width: 600, height: 200,
            align: "center", valign: "bottom", modal: false,
            i18n: (obj || i18n.def),
            buttons: {
            	"cfmN": function(){
                    $.thickbox.close();
                    my_dfd.reject();
                },
                "cfmY": function(){
                	//=============
                    $.thickbox.close();
                	my_dfd.resolve();
                }
            }
	    });	
		
	}
	return my_dfd.promise(); 
}
