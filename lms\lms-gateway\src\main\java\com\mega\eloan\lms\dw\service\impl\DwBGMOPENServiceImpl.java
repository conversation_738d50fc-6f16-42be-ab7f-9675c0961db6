package com.mega.eloan.lms.dw.service.impl;

import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dw.service.DwBGMOPEN1YService;
import com.mega.eloan.lms.dw.service.DwBGMOPENService;
import com.mega.eloan.lms.dw.service.DwBGMOPENXService;

/**
 * 全國營業(稅籍)登記資料 DWADM.OTS_DW_BGMOPEN
 */
@Service
public class DwBGMOPENServiceImpl extends AbstractDWJdbc implements
		DwBGMOPENService {

	@Resource
	DwBGMOPENXService dwBGMOPENXService;
	@Resource
	DwBGMOPEN1YService dwBGMOPEN1YService;

	/**
	 * 取得全國營業(稅籍)登記資料
	 * 
	 * @param custId
	 * @return
	 */
	@Override
	public Map<String, Object> getDataFromTaxation(String custId) {
		return this.getJdbc().queryForMap("OTS_DW_BGMOPENByCustId",
				new String[] { custId });
	}

	/**
	 * 綜合營業、停業、停業以外之非營業中資料
	 */
	@Override
	public Map<String, Object> getDataFromTaxationAllOpenStatus(String custId) {
		// 營業中
		Map<String, Object> dataMap = this.getJdbc().queryForMap(
				"OTS_DW_BGMOPENByCustId", new String[] { custId });
		// 停業
		if (dataMap == null) {
			dataMap = dwBGMOPENXService.getDataFromTaxation(custId);
		}
		// 停業以外之非營業中
		if (dataMap == null) {
			dataMap = dwBGMOPEN1YService.getDataFromTaxation(custId);
		}
		return dataMap;
	}
}
