/* 
 * C801M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C801M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C801M01A;

/** 個金個人資料清冊作業主檔 **/
@Repository
public class C801M01ADaoImpl extends LMSJpaDao<C801M01A, String> implements
		C801M01ADao {

	@Override
	public C801M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C801M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}
	
	@Override
	public C801M01A findByCntrNo(String cntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<C801M01A> findByOwnBrId(String ownBrId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}
	
	@Override
	public C801M01A findByCntrNoOwnBrId(String cntrNo,String ownBrId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.setMaxResults(Integer.MAX_VALUE);
		return findUniqueOrNone(search);
	}
}