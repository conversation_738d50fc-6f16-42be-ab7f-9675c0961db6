/* 
 * CLS8011ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.base.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.base.service.CLS8011Service;
import com.mega.eloan.lms.dao.C801M01ADao;
import com.mega.eloan.lms.dao.C801M01BDao;
import com.mega.eloan.lms.dao.C801M01CDao;
import com.mega.eloan.lms.eloandb.service.impl.AbstractEloandbJdbc;
import com.mega.eloan.lms.model.C801M01A;
import com.mega.eloan.lms.model.C801M01B;
import com.mega.eloan.lms.model.C801M01C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 個金個人資料清冊作業ServiceImpl
 * </pre>
 * 
 * @since 2014/04/01
 * <AUTHOR> @version <ul>
 * 
 *          </ul>
 */
@Service
@Qualifier("CLS8011Service")
public class CLS8011ServiceImpl extends AbstractEloandbJdbc implements
		CLS8011Service {
	private static final int SELF_VAL = 900;
	private static final Logger logger = LoggerFactory
			.getLogger(CLS8011ServiceImpl.class);

	@Resource
	C801M01ADao c801m01aDao;
	
	@Resource
	C801M01BDao c801m01bDao;

	@Resource
	C801M01CDao c801m01cDao;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	DocLogService docLogService;
	
	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				// set updater and updateTime
				try {
					if (Util.isEmpty(model.get(EloanConstants.OID))) {
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}
					model.set("updater", user.getUserId());
					model.set("updateTime", CapDate.getCurrentTimestamp());
				} catch (CapException e) {
					logger.error("[CLS8011ServiceImpl.save]", e);
				}

				if (model instanceof C801M01A) {
					C801M01A c801m01a = (C801M01A) model;
					//---
					if (Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
						c801m01a.setRandomCode(IDGenerator.getRandomCode());
					}
					
					if (Util.isNotEmpty(c801m01a.getOid()) && 
							Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
//						docLogService.record(c801m01a.getOid(), DocLogEnum.SAVE);
					}
					//---
					c801m01aDao.save(c801m01a);
					
					if (Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(c801m01a.getMainId());						
					}
				}else if (model instanceof C801M01B) {
					C801M01B c801m01b = (C801M01B) model;
					c801m01bDao.save(c801m01b);
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model instanceof C801M01A) {
				c801m01aDao.delete(((C801M01A) model));
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == C801M01A.class) {
			return c801m01aDao.findPage(search);
		}
		return null;
	}
	
	@SuppressWarnings("rawtypes")
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {		 
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {		
		return null;
	}
	
	@Override
	public C801M01A findC801M01A_oid(String oid) {
		return c801m01aDao.findByOid(oid);
	}

	@Override
	public C801M01A findC801M01A_mainId(String mainId) {
		return c801m01aDao.findByMainId(mainId);
	}

	@Override
	public C801M01A findC801M01A_cntrNo(String cntrNo) {
		return c801m01aDao.findByCntrNo(cntrNo);
	}

	@Override
	public List<C801M01A> findC801M01A_ownBrId(String ownBrId){
		return c801m01aDao.findByOwnBrId(ownBrId);
	}
	
	@Override
	public C801M01A findC801M01A_cntrNo_ownBrId(String cntrNo,String ownBrId) {
		return c801m01aDao.findByCntrNoOwnBrId(cntrNo,ownBrId);
	}
	
	@Override
	public List<C801M01B> findC801M01B_mainId(String mainId) {
		return c801m01bDao.findByMainId(mainId);
	}
	
	@Override
	public List<C801M01B> findC801M01B_mainId_itemType(String mainId, String itemType){
		return c801m01bDao.findByMainId_itemType(mainId, itemType);
	}

	@Override
	public C801M01B findC801M01B_oid(String oid){
		return c801m01bDao.findByOid(oid);
	}
	
	@Override
	public void saveData(C801M01A meta, List<C801M01B> addList){
		for(C801M01B c801m01b : addList ){
			save(c801m01b);
		}
		save(meta);
	}
	
	@Override
	public List<C801M01B> genLatest(String mainId){
		List<C801M01B> r = new ArrayList<C801M01B>();
		
		for(C801M01C c801m01c : c801m01cDao.getAll()){
			C801M01B c801m01b = new C801M01B();
			//---
			c801m01b.setMainId(mainId);
			c801m01b.setItemCode(Util.trim(c801m01c.getItemCode()));
			c801m01b.setItemSeq(c801m01c.getItemSeq());
			c801m01b.setItemType(Util.trim(c801m01c.getItemType()));
			c801m01b.setItemContent(Util.trim(c801m01c.getItemContent()));
			c801m01b.setItemCheck(Util.trim(c801m01c.getItemCheck()));
			c801m01b.setRiskLevel(Util.trim(c801m01c.getRiskLevel()));
			//---
			r.add(c801m01b);
		}
		return r;
	}
	
	@Override
	public boolean deleteC801m01as(String[] oids) {
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<C801M01A> list = new ArrayList<C801M01A>();
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		for (int i = 0, size = oids.length; i < size; i++) {
			C801M01A c801m01a = findC801M01A_oid(oids[i]);
			//---
			c801m01a.setDeletedTime(nowTS);
			c801m01a.setUpdater(user.getUserId());
			c801m01a.setUpdateTime(nowTS);
			//---
			list.add(c801m01a);
		}
		if (CollectionUtils.isNotEmpty(list)) {
			for(C801M01A model:list){
				//不用 save(model); 會更改 randomCode
				c801m01aDao.save(model);
			}
		}
		return true;
	}
	
	@Override
	public void deleteC801M01B(String mainId){
		c801m01bDao.deleteByMainId(mainId);
	}
	
	@Override
	public C801M01A initModel(String custId, String dupNo, String custName, String cntrNo){
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		//---
		C801M01A c801m01a = new C801M01A();
		
		c801m01a.setMainId(IDGenerator.getUUID());
		c801m01a.setTypCd("1");
		c801m01a.setCustId(custId);
		c801m01a.setDupNo(dupNo);
		c801m01a.setCustName(custName);
		c801m01a.setCntrNo(cntrNo);
		c801m01a.setOwnBrId(user.getUnitNo());		
		c801m01a.setDocURL(UtilConstants.CaseSchema.個金);
		c801m01a.setCaseStatus("1");
		c801m01a.setCreator(user.getUserId());
		c801m01a.setCreateTime(CapDate.getCurrentTimestamp());
		c801m01a.setUpdater(user.getUserId());
		c801m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		//---
		return c801m01a;
	}
	
	@Override
	public void setStatusEdit(C801M01A meta) {
		/*
		 * 會在 CLS8011V0_Page 中用到
		 * 會在 CLS8011M01Page 中用到，設定 btn 出現
		 */
		meta.setDocStatus(CLSDocStatusEnum.編製中);
		meta.setDeletedTime(null);
		meta.setApprover("");
		meta.setApproveTime(null);
	}

	@Override
	public void setStatusFinish(C801M01A meta) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		//---
		meta.setDocStatus(CLSDocStatusEnum.已核准);
		meta.setApprover(user.getUserId());
		meta.setApproveTime(CapDate.getCurrentTimestamp());
	}
	
	@Override
	public boolean isSelfAddItem(C801M01B c801m01b){
		return Util.parseInt(c801m01b.getItemCode())>SELF_VAL;
	}
	
	@Override
	public C801M01B selfAddItem(C801M01A meta, String itemType, String itemContent){
		List<C801M01B> list = findC801M01B_mainId_itemType(meta.getMainId(), itemType);
		int maxSeq = 0;
		int maxItemCode = SELF_VAL;
		for(C801M01B model : list){
			maxSeq = Math.max(maxSeq, model.getItemSeq());
			maxItemCode = Math.max(maxItemCode,  Util.parseInt(model.getItemCode()));
		}
		//--
		C801M01B c801m01b = new C801M01B();
		c801m01b.setMainId(meta.getMainId());
		c801m01b.setItemCode(String.valueOf(maxItemCode+1));
		c801m01b.setItemSeq(maxSeq+1);
		c801m01b.setItemType(itemType);
		c801m01b.setItemContent(Util.trim(itemContent));
		c801m01b.setItemCheck("");
		c801m01b.setRiskLevel("L");
		//---
		save(c801m01b);
		//---
		return c801m01b;
	}
	
	@Override
	public void saveAndAdjustSeq(C801M01A meta, C801M01B c801m01b){
		c801m01bDao.delete(c801m01b);
		List<C801M01B> c801m01b_list = findC801M01B_mainId_itemType(meta.getMainId(), c801m01b.getItemType());
		//預設項目最大 seq
		int maxSeq = 0;
		for(C801M01B model : c801m01b_list){
			if(isSelfAddItem(model)==false){
				maxSeq = Math.max(maxSeq, model.getItemSeq());
			}
		}
		for(C801M01B model : c801m01b_list){
			if(isSelfAddItem(model)){
				model.setItemSeq(++maxSeq);
				save(model);
			}
		}
		//---
		save(meta);
		//---
	}

	@Override
	public void exportExcel(ByteArrayOutputStream outputStream, List<C801M01A> list ) throws IOException, WriteException{
		
		WritableWorkbook workbook = null;
		WritableSheet sheet1 = null;
		if(true) {			
			//---
			workbook = Workbook.createWorkbook(outputStream);
			sheet1 = workbook.createSheet("Sheet1", 0);
			//讓第2頁也能印出 header
			sheet1.getSettings().setPrintTitlesRow(0, 0);
			//======
			WritableFont font12 = new WritableFont(WritableFont.createFont("標楷體"), 12);
			WritableFont font12_B = new WritableFont(WritableFont.createFont("標楷體"), 12, WritableFont.BOLD);
			WritableCellFormat cellFormatL = new WritableCellFormat(font12);
			{
				cellFormatL.setAlignment(Alignment.LEFT);
				cellFormatL.setWrap(true);
			}
			WritableCellFormat cellFormatL_B = new WritableCellFormat(font12_B);
			{
				cellFormatL_B.setAlignment(Alignment.LEFT);
				cellFormatL_B.setWrap(true);
			}
			
			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
			headerMap.put("檔案編號", 12);
			headerMap.put("借款戶名稱/姓名", 20);
			headerMap.put("營利事業統一編號", 22);	
			headerMap.put("額度序號", 16);
			headerMap.put("放款帳號", 18);
			headerMap.put("配偶", 12);
			headerMap.put("保證人", 18);
			headerMap.put("經辦", 12);
			headerMap.put("負責人", 12);

			int totalColSize = headerMap.size();
			
			List<String[]> rows = new ArrayList<String[]>();
			for(C801M01A model :list){
				String[] arr = new String[totalColSize];
				for(int i_col = 0; i_col <totalColSize ; i_col++){
					arr[i_col] = "";
				}
				arr[0] = Util.trim(model.getCaseNo());
				arr[1] = Util.trim(model.getCustName());
				arr[2] = Util.trim(model.getCustId());				
				arr[3] = Util.trim(model.getCntrNo());
				arr[4] = Util.trim(model.getLoanNo());
				arr[5] = Util.trim(model.getMName());
				arr[6] = Util.trim(model.getGuarantor());
				arr[7] = Util.trim(model.getCaseAppr());
				arr[8] = Util.trim(model.getChairmanName());
				//---
				rows.add(arr);
			}
						
			//==============================
			int rowIdx = 0;		
			//==============================
			rowIdx = 0;
			int colIdx = 0;
			for(String h: headerMap.keySet()){
				int colWidth = headerMap.get(h);
				sheet1.setColumnView(colIdx, colWidth);
				sheet1.addCell(new Label( colIdx, rowIdx, h, cellFormatL_B));
				//---
				colIdx++;
			}
			//==============================			
			rowIdx = 1;
			int i = 0;
			for(String[] arr: rows){	
				int colLen = arr.length;
				for(int i_col = 0; i_col <colLen ; i_col++){
					sheet1.addCell(new Label( i_col, rowIdx + i, arr[i_col], cellFormatL));
				}
				//---	
				i++;
			}
			
			workbook.write();
			workbook.close();
		}
		
	}
}