var initDfd = initDfd || $.Deferred(), inits = {
    fhandle: "lms1601m01formhandler",
    ghaddle: "lms1601gridhandler"
};
//驗證readOnly狀態
function checkReadonly(){
	var auth = (responseJSON ? responseJSON.Auth : {}); //權限
    if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
		return true ;
	}
	return false ;
}
  //檢查陣列內容是否重複
    function checkArrayRepeat(arrVal){
        var newArray = [];
        for (var i = arrVal.length; i--;) {
            var val = arrVal[i];
            if ($.inArray(val, newArray) == -1) {
                newArray.push(val);
            }
            else {
                return true;
            }
        }
        return false;
    }
	/**
	 * 產生稽核項目畫面
	 * @param {Object} json
	 */
	function creatL160M01C(json){
		if ($.isArray(json.allItem)) {
                if (json.allItem.length > 0) {
                    //當項目不是3的倍數，補成3的倍數
                    json.allItem = checkArray(json.allItem);
                    creatItemTable(json.allItem, "all");
                }
				
                //置入自訂項目描述
                for (var b in json.elfItem) {
                    $("#itemContent" + json.elfItem[b].id).text(json.elfItem[b].drc)
                }
                var item = {
                    "0": i18n.lms1601m01["L160M01A.noNeed"],
                    "1": "Ｖ",
                    "2": "Ｘ"
                };
                
                //產生select項目
                var $s1 = $(".s1");
                if ($s1.length > 0) {
                    $s1.setItems({
                        space: false,
                        item: item,
                        value: "2"
                    });
                }
                
                for (var b in json.value) {
                    var oid = json.value[b].id;
                    
                    //當oid的長度為1時是自訂項目
                    if (oid.length == 1) {
                        $("#itemCheck" + oid).val(json.value[b].val);
                    }
                    else {
                        $("[name$=" + oid + "]").val(json.value[b].val);
                    }
                }
            }   
	}
	//當項目不是3的倍數，補成3的倍數的陣列
    function checkArray(Item){
        var size = Item.length;
        if (size % 3 != 0) {
            var max = 3 - (size % 3);
            for (var i = 0; i < max; i++) {
                Item.push({
                    "id": "0",
                    "drc": ""
                });
            }
        }
        return Item;
    }
    
    //畫稽核項目的table
    function creatItemTable(itemObject, itemName){
        var tr = "";
        var td = "";
        for (var b in itemObject) {
            td += "<td><select name='" + itemName + itemObject[b].id + "' class='s1'/></td><td>" + itemObject[b].drc + "</td>";
            
            //當已經到三欄就換行
            if ((b + 1) % 3 == 0) {
                tr += ("<tr class='creatTr'>" + td + "</tr>");
                td = "";
            }
        }
        $("#" + itemName + "Branch").after(tr);
    }
    
$(function(){
    $.form.init({
        formHandler: inits.fhandle,
        formPostData: {//把form上貼上資料
            formAction: "queryL160m01a",
            oid: responseJSON.oid
        },
        loadSuccess: function(json){
			responseJSON.mainDocStatus = json.docStatusVal;
            responseJSON.unitLoanCase = json.unitLoanCase;
            var auth = (responseJSON ? responseJSON.Auth : {}); //權限
            initDfd.resolve(json, auth);
            responseJSON.mainId = json.mainId;
            //判斷是否顯示先行動用表
            (json.useType == "Y") ? $("#tabs_4").show() : $("#tabs_4").hide();
			//判斷是否顯示RPA查詢頁 only 小規模
            
            //J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
            //json.isSmallBuss == "Y"  ? $("#tabs_6").show() : $("#tabs_6").hide();
            
            //J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
            json.show_tabs_6 == "Y"  ? $("#tabs_6").show() : $("#tabs_6").hide();
            

            
            
            //依據文件狀態顯示不同覆核
            (json.docStatusVal == "02O") ? $("#check1").show().siblings("#check2").hide() : $("#check2").show().siblings("#check1").hide();
            
            if(responseJSON.page == "01"){
				$("#bossId").html(json.bossId);
			}
            
            if (responseJSON.page == "02") {
                //動用期限切換
                $("#the" + json.useSelect).show().siblings(".the").hide().find(":input").val("");
                
                //授信期限切換
                $("#two" + json.lnSelect).show().siblings(".two").hide().find(":input").val("");
                
                //J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
                //授信契約書 中 短期 切換
                //(json.tType == "2") ? $("#long").show() : $("#long").hide();
                if(json.tType == "1"){
                	$("#long").hide() ;
                	$("#show_not_tType3").show();
                }else if(json.tType == "2"){
                	$("#long").show();
                	$("#show_not_tType3").show();
                }else{
                	$("#long").hide();       
                	$("#show_not_tType3").find(":input").val("");
                	$("#show_not_tType3").hide();
                	
                }
                
                creatL160M01C(json);
                //J-111-0207 信保案件負責人客戶基本資料檔未建配偶資料檔要調整CSS
                if(json.smeMateInfo){
            		$("#smeMateInfo").val(json.smeMateInfo);
            		if(json.smeMateInfo == "2"){
            			$("#smeMateInfoText,#smeMateInfo").css("color", "red");
            			$("#smeMateInfoText,#smeMateInfo").css("font-weight", "bold");
            			$("#NoSmeMateInfoText").injectData({'NoSmeMateInfoText':json.noMateBorrower},false);
            		}
            	}else{console.log(json.docStatusVal);
            		if(json.docStatusVal != "01O"){
            			$("#smeMateInfoText,#smeMateInfo").hide();
            		}
            	}
            }//close if page 02
            if (responseJSON.page == "04") {
                $("#L163M01AForm").injectData(json.L163M01AForm);
            }
			if (responseJSON.page == "05") {
				//J-106-0029-003  洗錢防制-新增實質受益人
                var $LMS1205S20Form01 = $("#LMS1205S20Form01"); 
					if (responseJSON.readOnly != undefined &&
					responseJSON.readOnly != null &&
					responseJSON.readOnly != '') {
						if (responseJSON.readOnly.toString() == "true") {
							setReadOnly2();
						}					
					}	
				$LMS1205S20Form01.setData(json.LMS1205S20Form01,false);
            }
            if(checkReadonly()){
				$(".readOnlyhide").hide();
                $("form").lockDoc();
			}
				
            
        }
    });//close form init
    //呈主管覆核 選授信主管人數
    $("#numPerson").change(function(){
        var $newBossSpan = $("#newBossSpan");
        
        //清空原本的
        $newBossSpan.empty();
        
        var newdiv = "";
        var val = parseInt($(this).val(), 10);
        if (val > 1) {
            for (var i = 2, count = val + 1; i < count; i++) {
                newdiv += "<div>" + i18n.lms1601m01['L163M01A.no'] + i +
                i18n.lms1601m01['L163M01A.site'] +
                "&nbsp;" +
                i18n.lms1601m01['L160M01A.bossId'] +
                "&nbsp;&nbsp;&nbsp;<select name=boss" +
                i +
                " class='boss'/></div>";
            }
            $newBossSpan.append(newdiv);
            var copyOption = $("#mainBoss").html();
            $("[name^=boss]").html(copyOption);
        }
    });
    
    
    
    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
        saveData(true);
    }).end().find("#btnDelete").click(function(){
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "delete",
                        mainOid: $("#oid").val()
                    }
                });
            }
        });
    }).end().find("#btnTest").click(function(){   
	     $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "TETSMIS",
                mainOid: $("#mainOid").val()
            }, 
            success: function(){
            }
        });
    }).end().find("#btnSend").click(function(){
    	saveData(false,sendBoss);
    }).end().find("#btnAccept").click(function(){
        flowAction({
            flowAction: true
        });
    }).end().find("#btnCheck").click(function(){
        openCheck();
    }).end().find("#btnReUpMIS").click(function(){
        reUpMis();
    }).end().find("#btnPrint").click(function(){
		if (checkReadonly()) {
            printAction();
        }
        else {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                 	saveData(false,printAction);
                }
            });
        }
	
    });
	
	//列印動作
	function printAction(){
		$.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                type:"R01",
                mainId: responseJSON.mainId,
                mainOid: $("#mainOid").val(),
                fileDownloadName: "lms1601r01.pdf",
                serviceName: "lms1601r01rptservice"
            }
        });
	}
    //儲存的動作
    function saveData(showMsg,tofn){
        var allresult = {};
        var selfresult = {};
        if (responseJSON.page == "01") {
            if (!$("#L160M01AForm").valid() || $.trim($("#cntrNo").html()) == "") {
                //L160M01A.error3=請先引進額度明細表
                return CommonAPI.showErrorMessage(i18n.lms1601m01["L160M01A.error3"]);
            }
        }
        if (responseJSON.page == "02") {
            if (!$("#L160M01AForm").valid()) {
                return;
            }
			
			
			if($("#useSelect").val()=="1" &&($("#useFromDate").val() > $("#useEndDate").val())){
				//EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
				 CommonAPI.showErrorMessage(i18n.msg('EFD3026').replace(/\$\\{colName\\}/,i18n.lms1601m01['L160M01A.useDate']));
				return false;
			}
			
			if($("#tType").val() == "2" && $("#lnSelect").val()=="1" && ($("#lnFromDate").val() > $("#lnEndDate").val())){
				//EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
			 	 CommonAPI.showErrorMessage(i18n.msg('EFD3026').replace(/\$\\{colName\\}/,i18n.lms1601m01['L160M01A.lnDate']));
				return false;
			}
			if(($("#guFromDate").val()!="" && $("#guEndDate").val()!="")&& ($("#guFromDate").val() > $("#guEndDate").val())){
				//EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
			 	 CommonAPI.showErrorMessage(i18n.msg('EFD3026').replace(/\$\\{colName\\}/,i18n.lms1601m01['L160M01A.guCurr']+ i18n.lms1601m01['L160M01A.guFromDate']));
				return false;
			}
            
            //全行共用項目
            $("select[name^=all]").each(function(k, v){
                var id = $(v).attr("name");
                var value = $(v).val();
                id = id.replace("all", "");
                if (id != "0") {
                    allresult[id] = value;
                }
            });
            
            
            //自訂項目
            for (var i = 1; i < 7; i++) {
                var id = $("#itemCheck" + i).val();
                var drc = $.trim($("#itemContent" + i).val());
                    selfresult[i] = {
                        "id": id,
                        "drc": drc
                    };
            }
        }
        
        if (responseJSON.page == "03") {
            if (!$("#L161M01AForm").valid()) {
                return;
            }
        }
        if (responseJSON.page == "04") {
            if (!$("#L163M01AForm").valid()) {
                return;
            }
        }
        FormAction.open = true;
        $.ajax({
            handler: inits.fhandle,
            data: {//把資料轉成json
                formAction: "saveL160m01a",
                page: responseJSON.page,
                txCode: responseJSON.txCode,
                showMsg: showMsg,
                selfresult: JSON.stringify(selfresult),
                allresult: JSON.stringify(allresult),
				L160M01AForm: JSON.stringify($("#L160M01AForm").serializeData()),
				L163M01AForm: JSON.stringify($("#L163M01AForm").serializeData())
            },
            success: function(obj){
				if(responseJSON.page == "01"){
					 $('body').injectData(obj);
				}
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                if ($("#mainOid").val()) {
                    setRequiredSave(false);
                }else {
                    setRequiredSave(true);
                }
				 responseJSON.oid = obj.oid;
				 responseJSON.mainOid = obj.mainOid;
                 responseJSON.mainId = obj.mainId;
				 
                //用來判斷是否出現第四頁籤
                if (responseJSON.page == "02") {
                    if (obj.mark) {
                        $("#tabs_4").show();
                    }
                    else {
                        $("#tabs_4").hide();
                    }
                }
				
                // J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
				//用來判斷是否出現RPA查詢
                if (responseJSON.page == "01") {
//                    if (obj.isSmallBuss == "Y") {
//                        $("#tabs_6").show();
//                    }
//                    else {
//                        $("#tabs_6").hide();
//                    }
                	
                	obj.show_tabs_6 == "Y"  ? $("#tabs_6").show() : $("#tabs_6").hide();
                }
				
				if(obj.showPage4msg ){
					// L160M01A.message24 = 尚有待辦事項，在『先行動用呈核及控制表』頁籤中之「預定補全日期」，請至『先行動用呈核及控制表』登錄
					 return CommonAPI.showErrorMessage(i18n.lms1601m01['L160M01A.message24']);
				}
				
                //執行列印
                if (!showMsg && tofn) {
                     tofn();
                }
            }
        });
    }
    function flowAction(sendData){
        $.ajax({
            handler: inits.fhandle,
            data: $.extend({
                formAction: "flowAction",
                mainOid: $("#mainOid").val()
            }, (sendData || {})),
            success: function(obj){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
               	API.showPopMessage(i18n.def["runSuccess"], window.close);
				if(obj.otherMsg){
					CommonAPI.showMessage(obj.otherMsg);
				}
            }
        });
    }
    
    //呈主管 -  編製中
    function sendBoss(){
    	var selectBossForm = $("#selectBossForm");
        $.ajax({
            handler: inits.fhandle,
			action: "checkData",
            data: {
            },
            success: function(json){
            	/*掃到高風險
                $(".boss").setItems({
                    item: json.bossList
                });
                */
            	selectBossForm.find("select[name^=boss]").setItems({item: json.bossList});
            	selectBossForm.find("select[name=manager]").setItems({item: json.bossList});
				
                //J-108-0145_05097_B1001 Web e-Loan 國內外企金授信私募基金案件調整實質受益人控管
                $.ajax({
		            handler: inits.fhandle,
					action: "getWarnMsg",
		            data: {
		            },
		            success: function(warnJson){
		            	
		            	if(warnJson.warnMsg != ""){
							//下列額度於動審表-相關報表-額度動用資訊-動撥提醒資訊有承諾事項，但未設定是否需追蹤檢視，是否繼續?
							CommonAPI.confirmMessage(warnJson.warnMsg, function(b){  //i18n.lms1601m01["L160M01A.message27"]
								if (b) {
									$.ajax({
							            handler: inits.fhandle,
										action: "checkToAloan2",
							            data: {
							            },
							            success: function(chkJson){
											//J-103-0317-001 Web e-Loan企金簽報書上傳承諾事項與追蹤檢視日期
											if(chkJson.checkToAloan2 != ""){
												//下列額度於動審表-相關報表-額度動用資訊-動撥提醒資訊有承諾事項，但未設定是否需追蹤檢視，是否繼續?
												CommonAPI.confirmMessage(chkJson.checkToAloan2, function(b){  //i18n.lms1601m01["L160M01A.message27"]
													if (b) {
													 	sendToBoss(json);
													}else{
														return;
													}
												});
											}else{
												sendToBoss(json);
											}
							
							            }
							        });
								}else{
									return;
								}
							});
						}else{
							$.ajax({
					            handler: inits.fhandle,
								action: "checkToAloan2",
					            data: {
					            },
					            success: function(chkJson){
									//J-103-0317-001 Web e-Loan企金簽報書上傳承諾事項與追蹤檢視日期
									if(chkJson.checkToAloan2 != ""){
										//下列額度於動審表-相關報表-額度動用資訊-動撥提醒資訊有承諾事項，但未設定是否需追蹤檢視，是否繼續?
										CommonAPI.confirmMessage(chkJson.checkToAloan2, function(b){  //i18n.lms1601m01["L160M01A.message27"]
											if (b) {
											 	sendToBoss(json);
											}else{
												return;
											}
										});
									}else{
										sendToBoss(json);
									}
					
					            }
					        });
						}
		            	
		            	
		
		            }
		        });
                
				 
	
            }
        });
    }
    
    //待覆核  - 覆核
    function openCheck(){
		//bossApprove
    	
    	//J-108-0145_05097_B1001 Web e-Loan 國內外企金授信私募基金案件調整實質受益人控管
    	$.ajax({
            handler: inits.fhandle,
			action: "getWarnMsg",
            data: {
            },
            success: function(warnJson){
				//J-103-0317-001 Web e-Loan企金簽報書上傳承諾事項與追蹤檢視日期
				if(warnJson.warnMsg != ""){
					//下列額度於動審表-相關報表-額度動用資訊-動撥提醒資訊有承諾事項，但未設定是否需追蹤檢視，是否繼續?
					CommonAPI.confirmMessage(warnJson.warnMsg, function(b){  //i18n.lms1601m01["L160M01A.message27"]
						if (b) {
							$.ajax({
					            handler: inits.fhandle,
								action: "checkToAloan2",
					            data: {
					            },
					            success: function(chkJson){
									//J-103-0317-001 Web e-Loan企金簽報書上傳承諾事項與追蹤檢視日期
									if(chkJson.checkToAloan2 != ""){
										//下列額度於動審表-相關報表-額度動用資訊-動撥提醒資訊有承諾事項，但未設定是否需追蹤檢視，是否繼續?
										CommonAPI.confirmMessage(chkJson.checkToAloan2, function(b){  //i18n.lms1601m01["L160M01A.message27"]
											if (b) {
											 	bossApprove();
											}else{
												return;
											}
										});
									}else{
										bossApprove();
									}

					            }
					        });
						}else{
							return;
						}
					});
				}else{
					$.ajax({
			            handler: inits.fhandle,
						action: "checkToAloan2",
			            data: {
			            },
			            success: function(chkJson){
							//J-103-0317-001 Web e-Loan企金簽報書上傳承諾事項與追蹤檢視日期
							if(chkJson.checkToAloan2 != ""){
								//下列額度於動審表-相關報表-額度動用資訊-動撥提醒資訊有承諾事項，但未設定是否需追蹤檢視，是否繼續?
								CommonAPI.confirmMessage(chkJson.checkToAloan2, function(b){  //i18n.lms1601m01["L160M01A.message27"]
									if (b) {
									 	bossApprove();
									}else{
										return;
									}
								});
							}else{
								bossApprove();
							}

			            }
			        });
				}

            }
        });
    	
		
    }
    
    function reUpMis(){
    	CommonAPI.confirmMessage(i18n.def["confirmRun"], function(b){
            if (b) {
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "reUpMIS",
                        mainOid: responseJSON.oid
                    },
                    success: function(obj){
                        CommonAPI.triggerOpener("gridview", "reloadGrid");
                       	API.showPopMessage(i18n.def["runSuccess"], window.close);
        				if(obj.otherMsg){
        					CommonAPI.showMessage(obj.otherMsg);
        				}
                    }
                });
            }
        });
    }
    
    //輸入核定日期視窗
    function checkDate(){
        //帶入今天日期
        $("#forCheckDate").val(CommonAPI.getToday());
        $("#openChecDatekBox").thickbox({ // 使用選取的內容進行彈窗
            //L160M01A.message38 = 請輸入核定日
            title: i18n.lms1601m01['L160M01A.message38'],
            width: 100,
            height: 100,
            modal: true,
            valign: "bottom",
            align: "center",
			 readOnly: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var forCheckDate = $("#forCheckDate").val();
                    if ($.trim(forCheckDate) == "") {
                        //L160M01A.message38 = 請輸入核定日
                        return CommonAPI.showErrorMessage(i18n.lms1601m01['L160M01A.message38']);
                    }
                    flowAction({
                        flowAction: true,
                        checkDate: forCheckDate
                    });
                    $.thickbox.close();
                },
                
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
	
	//動審表呈主管覆核
	function sendToBoss(json){
		//L160M01A.message27=是否呈主管覆核？
        CommonAPI.confirmMessage(i18n.lms1601m01["L160M01A.message27"], function(b){
            if (b) {
                $("#selectBossBox").thickbox({ 
                    //L160M01A.bt14=覆核
                    title: i18n.lms1601m01['L160M01A.bt14'],
                    width: 500,
                    height: 300,
                    modal: true,
					 readOnly: false,
                    valign: "bottom",
                    align: "center",
                    i18n: i18n.def,
                    buttons: {
                        "sure": function(){
							
                            var selectBoss = $("select[name^=boss]").map(function(){
                                return $(this).val();
                            }).toArray();
                            
                            for (var i in selectBoss) {
                                if (selectBoss[i] == "") {
                                    //L160M01A.error2=請選擇+ L160M01A.bossId=授信主管
                                    return CommonAPI.showErrorMessage(i18n.lms1601m01['L160M01A.error2'] + i18n.lms1601m01['L160M01A.bossId']);
                                }
                            }
                            if ($("#manager").val() == "") {
                                //L160M01A.error2=請選擇+ L160M01A.managerId=經副襄理
                                return CommonAPI.showErrorMessage(i18n.lms1601m01['L160M01A.error2'] + i18n.lms1601m01['L160M01A.managerId']);
                            }
                            //驗證是否有重複的主管
                            if (checkArrayRepeat(selectBoss)) {
                                //主管人員名單重複請重新選擇
                                return CommonAPI.showErrorMessage(i18n.lms1601m01['L160M01A.message31']);
                            }
                            
                            flowAction({
                                page: responseJSON.page,
                                saveData: true,
                                selectBoss: selectBoss,
                                manager: $("#manager").val()
                            });
                            $.thickbox.close();
                            
                        },
                        
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });
            }
        });
	}
	
	//動審表主管覆核
	function bossApprove(){
	   // thickboxOptions.readOnly= false; 
        $("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
            //L160M01A.bt14=覆核
            title: i18n.lms1601m01['L160M01A.bt14'],
            width: 100,
            height: 100,
            modal: true,
			readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                
                    var val = $("[name=checkRadio]:checked").val();
                    if (!val) {
                        //L160M01A.error2=請選擇
                        return CommonAPI.showMessage(i18n.lms1601m01['L160M01A.error2']);
                    }
                    $.thickbox.close();
                    switch (val) {
                        case "1":
                            //一般退回到編製中01O
                            //L160M01A.message32=該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                            CommonAPI.confirmMessage(i18n.lms1601m01['L160M01A.message32'], function(b){
                                if (b) {
                                    flowAction({
                                        flowAction: false
                                    });
                                }
                            });
                            
                            break;
                        case "2":
                            //狀態是040退回到03O 
                            //L160M01A.message36=該案件是否退回經辦修改「辦妥日期及追蹤辦理情形」？要退回請按【確定】，不退回請按【取消】
                            CommonAPI.confirmMessage(i18n.lms1601m01["L160M01A.message36"], function(b){
                                if (b) {
                                    flowAction({
                                        flowAction: false
                                    });
                                }
                            });
                            break;
                        case "3":
                            //核定動撥   有兩種情形    先行動用_已覆核03O 或  無先行動用 到  已覆核05O
                            //L160M01A.message34=該案件是否確定執行核定及動撥作業
                            CommonAPI.confirmMessage(i18n.lms1601m01["L160M01A.message34"], function(b){
                                if (b) {
                                    checkDate();
                                }
                            });
                            break;
                        case "4":
                            //海外_已覆核05O
                            //L160M01A.message35=「先行動用呈核及控制表」內容是否確認？
                            CommonAPI.confirmMessage(i18n.lms1601m01["L160M01A.message35"], function(b){
                                if (b) {
                                    flowAction({
                                        flowAction: true
                                    });
                                }
                            });
                            
                            break;
                    }
                    
                    
                },
                
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
	}	
	
	
	
	
	
});


$.extend(window.tempSave, {
    handler: inits.fhandle,
    action: "tempSave",
    beforeCheck: function(){
        switch (responseJSON.page+"") {
            case "01":
               if ($.trim($("#cntrNo").html()) == "") {
                //L160M01A.error3=請先引進額度明細表
                 CommonAPI.showErrorMessage(i18n.lms1601m01["L160M01A.error3"]);
                }

                return $.trim($("#cntrNo").html()) != "";
                break;
            case "02":
				if($("#useSelect").val()=="1" &&($("#useFromDate").val() > $("#useEndDate").val())){
					//EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
					 CommonAPI.showErrorMessage(i18n.msg('EFD3026').replace(/\$\\{colName\\}/,i18n.lms1601m01['L160M01A.useDate']));
					return false;
				}
				
				if($("#tType").val() == "2" && $("#lnSelect").val()=="1" && ($("#lnFromDate").val() > $("#lnEndDate").val())){
					//EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
				 	 CommonAPI.showErrorMessage(i18n.msg('EFD3026').replace(/\$\\{colName\\}/,i18n.lms1601m01['L160M01A.lnDate']));
					return false;
				}
				if(($("#guFromDate").val()!="" && $("#guEndDate").val()!="")&& ($("#guFromDate").val() > $("#guEndDate").val())){
					//EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
				 	 CommonAPI.showErrorMessage(i18n.msg('EFD3026').replace(/\$\\{colName\\}/,i18n.lms1601m01['L160M01A.guCurr']+ i18n.lms1601m01['L160M01A.guFromDate']));
					return false;
				}
                return $("#L160M01AForm").valid();
                break;
            case "03":
                // return true;
              	return $("#L161M01AForm").valid();
                break;
			case "04":
                return true;
                break;
			case "05":
			    //J-106-0029-004  洗錢防制-動審表新增洗錢防制頁籤
                return true;
                break;	
			case "06":
                return true;
                break;	
            case "07":
                return true;
                break;	
        }
        
    },
    sendData: function(){
        var allresult = {};
        var selfresult = {};
        if (responseJSON.page == "01") {
            return {
                L160M01AForm: JSON.stringify($("#L160M01AForm").serializeData())
            };
        }
        if (responseJSON.page == "02") {
			
            //全行共用項目
            $("select[name^=all]").each(function(k, v){
                var id = $(v).attr("name");
                var value = $(v).val();
                id = id.replace("all", "");
                if (id != "0") {
                    allresult[id] = value;
                }
            });
            
            
            //自訂項目
            for (var i = 1; i < 7; i++) {
                var id = $("#itemCheck" + i).val();
                var drc = $.trim($("#itemContent" + i).val());
                    selfresult[i] = {
                        "id": id,
                        "drc": drc
                    };
            }
            return {
                L160M01AForm: JSON.stringify($("#L160M01AForm").serializeData()),
                selfresult: JSON.stringify(selfresult),
                allresult: JSON.stringify(allresult)
            };
            
        }
        
        if (responseJSON.page == "03") {
            return {
                L161M01AForm: JSON.stringify($("#L161M01AForm").serializeData())
            };
        }
        if (responseJSON.page == "04") {
            return {
                L163M01AForm: JSON.stringify($("#L163M01AForm").serializeData())
            };
        }
    }
});

