#==================================================
# \u4e00\u822c\u6388\u4fe1\u8cc7\u6599\u6a94
#==================================================
L170M01b.newCase=New/Existing
L170M01b.newCase1=New Loan
L170M01b.newCase2=Existing Case
L170M01b.newCase3=Please Select
L170M01b.cntrNo=Credit Limit Serial Number
L170M01b.loanNo=Loan Account number
L170M01b.loanTP=Credit Category
L170M01b.subject=Credit Category
L170M01b.lnDataDateResult=Manually add
Curr=Currency
L170M01b.quotaCurr=Limit Currency
L170M01b.balCurr=Balance currency
L170M01b.quotaAmt=Balance
L170M01b.balAmt=Outstanding Balance As At The Previous Day
L170M01b.Date=Drawdown period or credit tenor
L170M01b.guaranteeName=Name Of Collateral
L170M01b.curr=Currency
L170M01b.amt=Amount
L170M01b.estAmt=Valuation
L170M01b.loanAmt=Sum Secured
L170M01b.insMemo=Insurance-related Data
L170M01b.majorMemo=Key Terms & Conditions
L170M01b.quotaAmtSum=Total Credit Limit
L170M01b.balCurrSum=Outstanding Balance As At The Previous Day
L170M01b.bunCreatAll=Generate All Credit Data
L170M01b.bunDeleteAll=Delete All Credit Data
L170M01b.retrialDate=Credit Review Date
L170M01b.custName=Borrower
L170M01b.docStatus=Main Document Status
L170M01b.unit1=Unit: thousand dollars
L170M01b.unit2=Unit
L170M01b.unit3=thousand dollars
L170M01b.creditData=General Credit Data
L170M01b.warmMsg01=Please Select Outstanding Balance Currency As At The Previous Day
L170M01b.warmMsg02=Please Select Survey Valuation Currency
L170M01b.warmMsg03=Please Select Sum Secured Currency
L170M01b.warmMsg04=Please input Credit Limit Serial No.
L170M01b.warmMsg05=Please input Loan Account
L170M01b.errMsg01=Drawdown period or Ccredit tenor Start period less than End period
L170M01b.lnDataDate=The date of the credit data

ImportCMSData.001=Land(Address)\uff1a
ImportCMSData.002=Building(Address)\uff1a
ImportCMSData.003=Land(Lot No.)\uff1a
ImportCMSData.004=Without Collateral
ImportCMSData.005=Movable Property(Machinery Equipment)\uff1a
ImportCMSData.006=Movable Property(Tools)\uff1a
ImportCMSData.007=Movable Property(Raw Material)\uff1a
ImportCMSData.008=Movable Property(Work-in-progress)\uff1a
ImportCMSData.009=Movable Property(Finished Goods)\uff1a
ImportCMSData.010=Movable Property(Vehicles)\uff1a
ImportCMSData.011=Movable Property(Vessel)\uff1a
ImportCMSData.012=Movable Property(Aircraft)\uff1a
ImportCMSData.013=Movable Property(Others)\uff1a
ImportCMSData.014=Lien Over Rights\uff1a
ImportCMSData.015=Fixed Deposit
ImportCMSData.016=Fixed storage single
ImportCMSData.017=Transferable time deposit
ImportCMSData.018=Fixed Deposit In Other Bank
ImportCMSData.019=\u7532 Type Treasury Bill
ImportCMSData.020=\u4e59 Type Treasury Bill
ImportCMSData.021=Bonds
ImportCMSData.022=Financial bond
ImportCMSData.023=Government Bond
ImportCMSData.024=Corporate bonds
ImportCMSData.025=Shares
ImportCMSData.026=Beneficiary Certificate
ImportCMSData.027=Chattel Pledge :
ImportCMSData.028=Guarantee:
ImportCMSData.029=Banker's Guarantee
ImportCMSData.030=Guarantee from government treasury
ImportCMSData.031=Guarantee from credit guarantee institutes
ImportCMSData.032=Corporate Guarantee
ImportCMSData.033=Promissory Notes\uff1a
ImportCMSData.034=Amortization Notes\uff1a
ImportCMSData.035=Promissory Note
ImportCMSData.036=Bill
ImportCMSData.037=Check
ImportCMSData.038=Ticket numbers\uff1a
ImportCMSData.039=Percentage
ImportCMSData.040=Discounted bills\uff1a
ImportCMSData.041=Check\uff1a
ImportCMSData.042=Invoice Name\uff1a
ImportCMSData.043=Trust Receipt(Machinery Equipment)\uff1a
ImportCMSData.044=Trust Receipt(Tools)\uff1a
ImportCMSData.045=Trust Receipt(Raw Material)\uff1a
ImportCMSData.046=Trust Receipt(Work-in-progress)\uff1a
ImportCMSData.047=Trust Receipt(Finished Goods)\uff1a
ImportCMSData.048=Trust Receipt(Vehicles)\uff1a
ImportCMSData.049=Trust Receipt(Vessel)\uff1a
ImportCMSData.050=Trust Receipt(Aircraft)\uff1a
ImportCMSData.051=Trust Receipt(Others)\uff1a
ImportCMSData.052=Other Participating Lenders\uff1a
ImportCMSData.053=Othe Collateral\uff1a
ImportCMSData.054=Negative Pledge\uff1a
ImportCMSData.055=Floating Security\uff1a
ImportCMSData.056=1.The lending value accounting Kam valuation into several
ImportCMSData.057=2.The collateral set the value of first priority mortgagee
ImportCMSData.058= to the Bank\u3002
ImportCMSData.059=3.Collateral insured
ImportCMSData.060=\uff0cIn favor of the Bank\u3002  
