/* 
 * CLS1161V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;

/**
 * <pre>
 * 動用審核表(個金)
 * </pre>
 * 
 * @since 2012/12/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/14,Fantasy,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1161v01")
public class CLS1161V01Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CLSDocStatusEnum.編製中);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.Add, LmsButtonEnum.Modify,
				LmsButtonEnum.Delete);
		// build i18n
		renderJsI18N(CLS1161V01Page.class);
		// add panel
		// add(new CLS1131S01Panel("CLS1131S01"));

		// UPGRADE: 待確認JavaScript有無正確讀取
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1161V01Page');");
	}// ;

}
