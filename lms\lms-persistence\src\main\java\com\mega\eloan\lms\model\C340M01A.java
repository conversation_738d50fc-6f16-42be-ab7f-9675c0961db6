package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.IDataObject;

/** 消金契約書主檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C340M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C340M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	// bi-directional many-to-one association to M100S01B
	@ToStringExclude
	@OneToMany(mappedBy = "c340m01a", fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
	private List<C340M01B> c340m01bs;

	@ToStringExclude
	@OneToMany(mappedBy = "c340m01a", fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
	private List<C340M01C> c340m01cs;

	/** 契約書種類：1-購屋契約, 2-純信用無擔保(DBR22倍), 3-其他類契約, 4-以房養老契約 <br/>  
	 * 【PLOAN專屬】A-線上對保契約書, S-線上對保擔保品提供人契約} <br/>
	 * 【三竹資訊】L-線上對保契約書勞工紓困 <br/>
	 * C-中鋼集團消貸線上契約
	 */	
	@Column(name = "CTRTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String ctrType; // 參考 ContractDocConstants.C340M01A_CtrType 或 C160S01C.ctrType 
	
	@Column(name = "RPTID", length = 32, columnDefinition = "VARCHAR(32)")	
	private String rptId;
	
	/** 簽報書MainId */	
	@Column(name = "CASEMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String caseMainId;
	
	/** 簽報書案號 */
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/** 契約書編號 */
	@Column(name = "CONTRNUMBER", length = 60, columnDefinition = "CHAR(60)")
	private String contrNumber;

	/** 契約書客戶名稱 */
	@Column(name = "CONTRPARTYNM", length = 150, columnDefinition = "CHAR(150)")
	private String contrPartyNm;
	
	//在此 table 不放 L120M01A.endDate => 因簽報書可能核准後, 又退回修改
//	@Temporal(TemporalType.DATE)
//	@Column(name = "ENDDATE", columnDefinition = "DATE")
//	private Date endDate;
	
	/** PLOAN契約編號 */
	@Column(name = "PLOANCTRNO", length = 20, columnDefinition = "CHAR(20)")
	private String ploanCtrNo;
	
	/** PLOAN契約約態{1:已送至PLOAN啟動對保, 2:已作廢, 9:已對保完成} */
	@Column(name = "PLOANCTRSTATUS", length = 1, columnDefinition = "CHAR(1)")
	private String ploanCtrStatus;
	
	/** PLOAN契約對保期限 **/
	@Temporal(TemporalType.DATE)
	@Column(name="PLOANCTREXPRDATE", columnDefinition="DATE")
	private Date ploanCtrExprDate;

	/** PLOAN契約對保完成_主借人 **/
	@Column(name="PLOANCTRSIGNTIMEM", columnDefinition="TIMESTAMP")
	private Timestamp ploanCtrSignTimeM;
	
	/** PLOAN契約對保完成_保證人 **/
	@Column(name="PLOANCTRSIGNTIME1", columnDefinition="TIMESTAMP")
	private Timestamp ploanCtrSignTime1;
	
	/** PLOAN契約作廢時間 **/
	@Column(name="PLOANCTRDCTIME", columnDefinition="TIMESTAMP")
	private Timestamp ploanCtrDcTime;
	
	/** PLOAN契約作廢人員 */
	@Column(name = "PLOANCTRDCUSER", length = 6, columnDefinition = "CHAR(6)")
	private String ploanCtrDcUser;
	
	/** PLOAN契約授信起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="PLOANCTRBEGDATE", columnDefinition="DATE")
	private Date ploanCtrBegDate;
	
	/** PLOAN契約授信迄日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="PLOANCTRENDDATE", columnDefinition="DATE")
	private Date ploanCtrEndDate;
	
	/** 通知T1時點 **/
	@Column(name="PLOANNOTIFYT1TS", columnDefinition="TIMESTAMP")
	private Timestamp ploanNotifyT1TS;
	
	/** PLOAN提供品提供人ID */
	//ploan004.setRelatedPersonId(l140s01a.getCustId())
	@Column(name = "PLOANPOSSID", length = 10, columnDefinition = "CHAR(10)")
	private String ploanPosSId;
	
	/** PLOAN契約{0:原購屋貸款全數清償 , 1:原購屋貸款未全數清償 } */
	@Column(name = "PLOANPOSSFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String ploanPosSFlag;
	
	/** PLOAN契約對保完成_posS **/
	@Column(name="PLOANCTRSIGNTIMES", columnDefinition="TIMESTAMP")
	private Timestamp ploanCtrSignTimeS;
	
	/** PLOAN契約對保完成_主借人IP */
	@Column(name = "PLOANBORROWERIPADDR", length = 100, columnDefinition = "VARCHAR(100)")
	private String ploanBorrowerIPAddr;
	
	/** PLOAN契約對保完成_保證人IP */
	@Column(name = "PLOANSTAKEHOLDERIPADDR", length = 100, columnDefinition = "VARCHAR(100)")
	private String ploanStakeholderIPAddr;
	
	/** PLOAN契約首次還款日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="PLOANCTR1STRTDT", columnDefinition="DATE")
	private Date ploanCtr1stRtDt;
	
	/** 契約攜回日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CTRCHECKDATE", columnDefinition="DATE")
	private Date ctrCheckDate;
	
	/** c160s01d_oid動審整批匯入 */
	@Column(name = "C160S01D_OID", length = 32, columnDefinition = "VARCHAR(32)")
	private String c160s01d_oid;

	/** 是否同意ACH扣款
	 * Y/N/空白
	 * */
	@Column(name = "ISNEEDACH", length = 1, columnDefinition = "VARCHAR(1)")
	private String isNeedACH;

	/** 撥款銀行代碼 */
	@Column(name = "BANKACCTCODE", length = 3, columnDefinition = "VARCHAR(3)")
	private String bankAcctCode;

	/** 撥款銀行帳號 */
	@Column(name = "BANKACCTNO", length = 16, columnDefinition = "VARCHAR(16)")
	private String bankAcctNo;

	/** 撥款分行代碼 */
	@Column(name = "BANKACCTBRANCHCODE", length = 7, columnDefinition = "VARCHAR(7)")
	private String bankAcctBranchCode;

	/** 還款銀行代碼 */
	@Column(name = "BANKACHACCTCODE", length = 3, columnDefinition = "VARCHAR(3)")
	private String bankACHAcctCode;

	/** 還款銀行帳號 */
	@Column(name = "BANKACHACCTNO", length = 16, columnDefinition = "VARCHAR(16)")
	private String bankACHAcctNo;

	/** 還款分行代碼 */
	@Column(name = "BANKACHACCTBRANCHCODE", length = 7, columnDefinition = "VARCHAR(7)")
	private String bankACHAcctBranchCode;

	/** 是否已上傳ELF460
	 * Y/N/空白
	 * */
	@Column(name = "MISFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String misFlag;

	/**
	* 是否已產生額度檔
	 * Y/N/Null
	 * 用來判斷對保完成，超過契約期限未撥款，是否需要取消報送聯徵
	* */
	@Column(name = "HAVELNF020", length = 1, columnDefinition = "VARCHAR(1)")
	private String haveLnf020;

	/** 通知PLOAN發對保契約書通知信給客戶
	 * Y/N/空白
	 * */
	@Column(name = "NOTIFYPLOANSENDMAIL", length = 1, columnDefinition = "VARCHAR(1)")
	private String notifypLoanSendMail;
	
	/** RPA查詢家事時間 **/
	@Column(name="FARPAJOBTS", columnDefinition="timestamp")
	private Timestamp faRpaJobTs;

	public String getCtrType() {
		return ctrType;
	}

	public void setCtrType(String ctrType) {
		this.ctrType = ctrType;
	}

	public String getRptId() {
		return rptId;
	}

	public void setRptId(String rptId) {
		this.rptId = rptId;
	}

	public String getCaseMainId() {
		return caseMainId;
	}

	public void setCaseMainId(String caseMainId) {
		this.caseMainId = caseMainId;
	}

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	public String getContrNumber() {
		return contrNumber;
	}

	public void setContrNumber(String contrNumber) {
		this.contrNumber = contrNumber;
	}

	public String getContrPartyNm() {
		return contrPartyNm;
	}

	public void setContrPartyNm(String contrPartyNm) {
		this.contrPartyNm = contrPartyNm;
	}

	public String getPloanCtrNo() {
		return ploanCtrNo;
	}

	public void setPloanCtrNo(String ploanCtrNo) {
		this.ploanCtrNo = ploanCtrNo;
	}

	public String getPloanCtrStatus() {
		return ploanCtrStatus;
	}

	public void setPloanCtrStatus(String ploanCtrStatus) {
		this.ploanCtrStatus = ploanCtrStatus;
	}

	public Date getPloanCtrExprDate() {
		return ploanCtrExprDate;
	}

	public void setPloanCtrExprDate(Date ploanCtrExprDate) {
		this.ploanCtrExprDate = ploanCtrExprDate;
	}

	public Timestamp getPloanCtrSignTimeM() {
		return ploanCtrSignTimeM;
	}

	public void setPloanCtrSignTimeM(Timestamp ploanCtrSignTimeM) {
		this.ploanCtrSignTimeM = ploanCtrSignTimeM;
	}

	public Timestamp getPloanCtrSignTime1() {
		return ploanCtrSignTime1;
	}

	public void setPloanCtrSignTime1(Timestamp ploanCtrSignTime1) {
		this.ploanCtrSignTime1 = ploanCtrSignTime1;
	}

	public Timestamp getPloanCtrDcTime() {
		return ploanCtrDcTime;
	}

	public void setPloanCtrDcTime(Timestamp ploanCtrDcTime) {
		this.ploanCtrDcTime = ploanCtrDcTime;
	}
	
	public String getPloanCtrDcUser() {
		return ploanCtrDcUser;
	}

	public void setPloanCtrDcUser(String ploanCtrDcUser) {
		this.ploanCtrDcUser = ploanCtrDcUser;
	}

	public Date getPloanCtrBegDate() {
		return ploanCtrBegDate;
	}

	public void setPloanCtrBegDate(Date ploanCtrBegDate) {
		this.ploanCtrBegDate = ploanCtrBegDate;
	}

	public Date getPloanCtrEndDate() {
		return ploanCtrEndDate;
	}

	public void setPloanCtrEndDate(Date ploanCtrEndDate) {
		this.ploanCtrEndDate = ploanCtrEndDate;
	}

	public Timestamp getPloanNotifyT1TS() {
		return ploanNotifyT1TS;
	}

	public void setPloanNotifyT1TS(Timestamp ploanNotifyT1TS) {
		this.ploanNotifyT1TS = ploanNotifyT1TS;
	}
	
	public String getPloanPosSId() {
		return ploanPosSId;
	}

	public void setPloanPosSId(String ploanPosSId) {
		this.ploanPosSId = ploanPosSId;
	}

	public String getPloanPosSFlag() {
		return ploanPosSFlag;
	}

	public void setPloanPosSFlag(String ploanPosSFlag) {
		this.ploanPosSFlag = ploanPosSFlag;
	}

	public Timestamp getPloanCtrSignTimeS() {
		return ploanCtrSignTimeS;
	}
	public void setPloanCtrSignTimeS(Timestamp ploanCtrSignTimeS) {
		this.ploanCtrSignTimeS = ploanCtrSignTimeS;
	}

	public String getPloanBorrowerIPAddr() {
		return ploanBorrowerIPAddr;
	}

	public void setPloanBorrowerIPAddr(String ploanBorrowerIPAddr) {
		this.ploanBorrowerIPAddr = ploanBorrowerIPAddr;
	}

	public String getPloanStakeholderIPAddr() {
		return ploanStakeholderIPAddr;
	}

	public void setPloanStakeholderIPAddr(String ploanStakeholderIPAddr) {
		this.ploanStakeholderIPAddr = ploanStakeholderIPAddr;
	}	
	
	public Date getPloanCtr1stRtDt() {
		return ploanCtr1stRtDt;
	}

	public void setPloanCtr1stRtDt(Date ploanCtr1stRtDt) {
		this.ploanCtr1stRtDt = ploanCtr1stRtDt;
	}

	public Date getCtrCheckDate() {
		return ctrCheckDate;
	}

	public void setCtrCheckDate(Date ctrCheckDate) {
		this.ctrCheckDate = ctrCheckDate;
	}

	public String getC160s01d_oid() {
		return c160s01d_oid;
	}

	public void setC160s01d_oid(String c160s01d_oid) {
		this.c160s01d_oid = c160s01d_oid;
	}

	//~~~~~~~~~~~~~~~
	public List<C340M01B> getC340m01bs() {
		return c340m01bs;
	}

	public void setC340m01bs(List<C340M01B> c340m01bs) {
		this.c340m01bs = c340m01bs;
	}

	public List<C340M01C> getC340m01cs() {
		return c340m01cs;
	}

	public void setC340m01cs(List<C340M01C> c340m01cs) {
		this.c340m01cs = c340m01cs;
	}

	public String getIsNeedACH(){
		return isNeedACH;
	}
	public void setIsNeedACH( String isNeedACH )
	{
		this.isNeedACH = isNeedACH;
	}

	public String getBankAcctCode()
	{
		return bankAcctCode;
	}
	public void setBankAcctCode( String bankAcctCode )
	{
		this.bankAcctCode = bankAcctCode;
	}

	public String getBankAcctNo()
	{
		return bankAcctNo;
	}
	public void setBankAcctNo( String bankAcctNo )
	{
		this.bankAcctNo = bankAcctNo;
	}

	public String getBankAcctBranchCode()
	{
		return bankAcctBranchCode;
	}
	public void setBankAcctBranchCode( String bankAcctBranchCode )
	{
		this.bankAcctBranchCode = bankAcctBranchCode;
	}

	public String getBankACHAcctBranchCode()
	{
		return bankACHAcctBranchCode;
	}
	public void setBankACHAcctBranchCode( String bankACHAcctBranchCode )
	{
		this.bankACHAcctBranchCode = bankACHAcctBranchCode;
	}

	public String getBankACHAcctCode()
	{
		return bankACHAcctCode;
	}
	public void setBankACHAcctCode( String bankACHAcctCode )
	{
		this.bankACHAcctCode = bankACHAcctCode;
	}

	public String getBankACHAcctNo()
	{
		return bankACHAcctNo;
	}
	public void setBankACHAcctNo( String bankACHAcctNo )
	{
		this.bankACHAcctNo = bankACHAcctNo;
	}

	public String getMisFlag() {
		return misFlag;
	}
	public void setMisFlag(String misFlag) {
		this.misFlag = misFlag;
	}

	public String getHaveLnf020() {
		return haveLnf020;
	}

	public void setHaveLnf020(String haveLnf020) {
		this.haveLnf020 = haveLnf020;
	}

	public String getNotifypLoanSendMail() {
		return notifypLoanSendMail;
	}

	public void setNotifypLoanSendMail(String notifypLoanSendMail) {
		this.notifypLoanSendMail = notifypLoanSendMail;
	}
	
	public Timestamp getFaRpaJobTs() {
		return faRpaJobTs;
	}
	
	public void setFaRpaJobTs(Timestamp faRpaJobTs) {
		this.faRpaJobTs = faRpaJobTs;
	}
}
