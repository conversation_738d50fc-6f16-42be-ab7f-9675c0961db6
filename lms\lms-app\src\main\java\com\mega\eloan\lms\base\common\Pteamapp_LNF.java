package com.mega.eloan.lms.base.common;

import java.math.BigDecimal;
import java.util.Date;

import tw.com.jcs.common.Util;

public class Pteamapp_LNF {
	private String lnf020_cust_id;
	private String brno;
	private String lnf020_contract;
	private String lnf020_grp_cntrno;
	private String raw_lnf020_grp_cntrno;
	private BigDecimal fact_amt;
	private String lnf030_loan_no;
	private String lnap;
	private String lnf030_charc_code;
	private Date lnf030_open_date;
	private Date lnf030_loan_date;
	private String loan_date_ym;
	private BigDecimal lnf030_1st_ln_amt;
	private Date lnf030_cancel_date;
	private BigDecimal loan_bal;
	private BigDecimal loan_bal_m;
	private BigDecimal loan_rate;
	private BigDecimal loan_rate_m;
	private String elf500_lotno;
	private String elf500_document_no;
	private String elf447n_unid;
	private String elf447n_project_no;
	private String elf447n_curr;
	private BigDecimal first_fact_amt;
	private String cName;
	private String elf506_722_flag;
	
	public Pteamapp_LNF(){
		this.lnf020_cust_id = "";
		this.brno = "";
		this.lnf020_contract = "";
		this.lnf020_grp_cntrno = "";
		this.raw_lnf020_grp_cntrno = "";
		this.fact_amt = null;
		this.lnf030_loan_no = "";
		this.lnap = "";
		this.lnf030_charc_code = "";
		this.lnf030_open_date = null;
		this.lnf030_loan_date = null;
		this.loan_date_ym = "";
		this.lnf030_1st_ln_amt = null;
		this.lnf030_cancel_date = null;
		this.loan_bal = null;
		this.loan_bal_m = null;
		this.loan_rate = null;
		this.loan_rate_m = null;
		this.elf500_lotno = "";
		this.elf500_document_no = "";
		this.elf447n_unid = "";
		this.elf447n_project_no = "";
		this.elf447n_curr = "";
		this.first_fact_amt = null;
		this.cName = "";
		this.elf506_722_flag = "";
	}
	//================================================
	public BigDecimal fetch_bal(String dataPeriodFlag){
		if(Util.equals("D", dataPeriodFlag)){
			return loan_bal;
		}else{
			return loan_bal_m;
		}
	}
	public BigDecimal fetch_rate(String dataPeriodFlag){
		if(Util.equals("D", dataPeriodFlag)){
			return loan_rate;
		}else{
			return loan_rate_m;
		}
	}
	//================================================
	public String getLnf020_cust_id() {
		return lnf020_cust_id;
	}

	public void setLnf020_cust_id(String lnf020_cust_id) {
		this.lnf020_cust_id = lnf020_cust_id;
	}

	public String getBrno() {
		return brno;
	}

	public void setBrno(String brno) {
		this.brno = brno;
	}

	public String getLnf020_contract() {
		return lnf020_contract;
	}

	public void setLnf020_contract(String lnf020_contract) {
		this.lnf020_contract = lnf020_contract;
	}

	public String getLnf020_grp_cntrno() {
		return lnf020_grp_cntrno;
	}

	public void setLnf020_grp_cntrno(String lnf020_grp_cntrno) {
		this.lnf020_grp_cntrno = lnf020_grp_cntrno;
	}

	public String getRaw_lnf020_grp_cntrno() {
		return raw_lnf020_grp_cntrno;
	}

	public void setRaw_lnf020_grp_cntrno(String raw_lnf020_grp_cntrno) {
		this.raw_lnf020_grp_cntrno = raw_lnf020_grp_cntrno;
	}

	public BigDecimal getFact_amt() {
		return fact_amt;
	}

	public void setFact_amt(BigDecimal fact_amt) {
		this.fact_amt = fact_amt;
	}

	public String getLnf030_loan_no() {
		return lnf030_loan_no;
	}

	public void setLnf030_loan_no(String lnf030_loan_no) {
		this.lnf030_loan_no = lnf030_loan_no;
	}

	public String getLnap() {
		return lnap;
	}

	public void setLnap(String lnap) {
		this.lnap = lnap;
	}

	public String getLnf030_charc_code() {
		return lnf030_charc_code;
	}

	public void setLnf030_charc_code(String lnf030_charc_code) {
		this.lnf030_charc_code = lnf030_charc_code;
	}

	public Date getLnf030_open_date() {
		return lnf030_open_date;
	}

	public void setLnf030_open_date(Date lnf030_open_date) {
		this.lnf030_open_date = lnf030_open_date;
	}

	public Date getLnf030_loan_date() {
		return lnf030_loan_date;
	}

	public void setLnf030_loan_date(Date lnf030_loan_date) {
		this.lnf030_loan_date = lnf030_loan_date;
	}

	public String getLoan_date_ym() {
		return loan_date_ym;
	}

	public void setLoan_date_ym(String loan_date_ym) {
		this.loan_date_ym = loan_date_ym;
	}

	public BigDecimal getLnf030_1st_ln_amt() {
		return lnf030_1st_ln_amt;
	}

	public void setLnf030_1st_ln_amt(BigDecimal lnf030_1st_ln_amt) {
		this.lnf030_1st_ln_amt = lnf030_1st_ln_amt;
	}

	public Date getLnf030_cancel_date() {
		return lnf030_cancel_date;
	}

	public void setLnf030_cancel_date(Date lnf030_cancel_date) {
		this.lnf030_cancel_date = lnf030_cancel_date;
	}

	public BigDecimal getLoan_bal() {
		return loan_bal;
	}

	public void setLoan_bal(BigDecimal loan_bal) {
		this.loan_bal = loan_bal;
	}

	public BigDecimal getLoan_bal_m() {
		return loan_bal_m;
	}

	public void setLoan_bal_m(BigDecimal loan_bal_m) {
		this.loan_bal_m = loan_bal_m;
	}

	public BigDecimal getLoan_rate() {
		return loan_rate;
	}

	public void setLoan_rate(BigDecimal loan_rate) {
		this.loan_rate = loan_rate;
	}

	public BigDecimal getLoan_rate_m() {
		return loan_rate_m;
	}

	public void setLoan_rate_m(BigDecimal loan_rate_m) {
		this.loan_rate_m = loan_rate_m;
	}
	
	public String getElf500_lotno() {
		return elf500_lotno;
	}

	public void setElf500_lotno(String elf500_lotno) {
		this.elf500_lotno = elf500_lotno;
	}

	public String getElf500_document_no() {
		return elf500_document_no;
	}

	public void setElf500_document_no(String elf500_document_no) {
		this.elf500_document_no = elf500_document_no;
	}

	public String getElf447n_unid() {
		return elf447n_unid;
	}

	public void setElf447n_unid(String elf447n_unid) {
		this.elf447n_unid = elf447n_unid;
	}

	public String getElf447n_project_no() {
		return elf447n_project_no;
	}

	public void setElf447n_project_no(String elf447n_project_no) {
		this.elf447n_project_no = elf447n_project_no;
	}

	public String getElf447n_curr() {
		return elf447n_curr;
	}

	public void setElf447n_curr(String elf447n_curr) {
		this.elf447n_curr = elf447n_curr;
	}

	public BigDecimal getFirst_fact_amt() {
		return first_fact_amt;
	}

	public void setFirst_fact_amt(BigDecimal first_fact_amt) {
		this.first_fact_amt = first_fact_amt;
	}

	public String getCName() {
		return cName;
	}

	public void setCName(String val) {
		this.cName = val;
	}
	
	public String getElf506_722_flag() {
		return elf506_722_flag;
	}
	
	public void setElf506_722_flag(String elf506_722_flag) {
		this.elf506_722_flag = elf506_722_flag;
	}
	
}
