package com.mega.eloan.lms.dao.impl;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C900S02CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900S02C;

/** 消金新做案件(當月、當年度累計)統計表 **/
@Repository
public class C900S02CDaoImpl extends LMSJpaDao<C900S02C, String>
	implements C900S02CDao {

	@Override
	public C900S02C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public void deleteByDataYM(String dataYM){
		Query query = entityManager.createNamedQuery("C900S02C.deleteByDataYM");
		query.setParameter(1, dataYM);
		query.executeUpdate();
	}
}