var oldOid = "";
initDfd.done(function() {
	setCloseConfirm(true);
	
	var pageUrl = "";
	
	//登錄事件
	$("#lms4a").click(function(){
		pageUrl = "../../lms/lmss07A";
		loadHtml(pageUrl, "7a");
		//J-104-0138-001 隱藏引進社會與環境風險(赤道原則)評估
		if(responseJSON.docType == "2"){
		    $("#btnApplyEquatorPrinciples").hide();
		}else{
			$("#btnApplyEquatorPrinciples").show();
		}
	});
	$("#lms4b").click(function(){
		pageUrl = "../../lms/lmss07C";
		loadHtml(pageUrl, "7c");
	});
	$("#lms4c").click(function(){
		pageUrl = "../../lms/lmss07D";
		loadHtml(pageUrl, "7d");
	});
	$("#lms4d").click(function(){
		pageUrl = "../../lms/lmss07B";
		loadHtml(pageUrl, "7b");
	});
	//J-104-0183	
	$("#lmss07f").click(function(){
		pageUrl = "../../lms/lmss07F";
		loadHtml(pageUrl, "7f");
	});		
	$("#lmss07g").click(function() {
		pageUrl = "../../lms/lmss07aG";
		loadHtml(pageUrl, "7g");
	});
	
	//預設LOAD HTML
	if (responseJSON.docType == '1') {
	    // 企金
		pageUrl = "../../lms/lmss07A";	
	    loadHtml(pageUrl, "7a");
		
		$("#lmss07f").show();
		pageUrl = "../../lms/lmss07F";
	    loadHtml(pageUrl, "7f");
	}else{
		//J-104-0138-001個金隱藏 企金授信新增赤道原則 "../../lms/lmss07F 
		$("#lmss07f").hide();
		pageUrl = "../../lms/lmss07A";	
	    loadHtml(pageUrl, "7a");
		$("#lms4a").trigger('click');
	} 

	if(responseJSON.docCode == "3"){
		$("#lms4c").hide();
		$("#lms4d").hide();
	}else{
		$("#lms4c").show();
		$("#lms4d").show();
	}	
	
	
	
	
	A_1_8_1();
	A_1_8_2();
	gridviewCust();
	gridViewShow();
	gridSelectMainId();
	A_1_10_4();
	A_1_10_3();	
/*
	$("#buttonSearch").click(function(){
		if($("#formSearch").valid()){
			$("#gridviewAA").jqGrid("setGridParam", {//重新設定grid需要查到的資料
				postData : {
					formAction:"queryL140m01a2",
					custId : $("#formSearch").find("#searchId").val()
					//textBrid : $("#textBrid").val()
				},
				search: true
			}).trigger("reloadGrid");  
		}
	});
*/
/*
	$("#custSearch").click(function(){
		if($("#formAdd").valid()){
			$("#gridviewCust").jqGrid("setGridParam", {//重新設定grid需要查到的資料
				postData : {
					formAction:"queryL120s01aById",
					custId : $("#formAdd").find("#searchId").val()
				},
				search: true
			}).trigger("reloadGrid");  
		}
	});
*/
	$("#open1").click(function(){
		$("#gridSelectMainId").jqGrid("setGridParam", {//重新設定grid需要查到的資料
			postData : {
				formAction:"queryCesMainIdss2",
				rowNum:10
			},
			search: true
		}).trigger("reloadGrid");  
	});
	
	//上傳檔案按鈕
	$("#uploadFile6").click(function(){
		var limitFileSize=3145728;
		MegaApi.uploadDialog({
			fieldId:"upFile1305",
            fieldIdHtml:"size='30'",
            fileDescId:"fileDesc",
            fileDescHtml:"size='30' maxlength='30'",
			subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
			limitSize:limitFileSize,			
            width:320,
            height:190,
			data:{
				mainId:$("#mainId").val()
			},
			success : function(obj) {
				$("#gridfile6").trigger("reloadGrid");
			}
	   });
	});
	
	//刪除檔案按鈕
	$("#deleteFile6").click(function(){
		var select  = $("#gridfile6").getGridParam('selrow');		
		if(select == "" || select == null || select == undefined){		
			// TMMDeleteError=請先選擇需修改(刪除)之資料列
			CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
			return;
		}		
		// confirmDelete=是否確定刪除?
		CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
			if(b){				
				var data = $("#gridfile6").getRowData(select);
				$.ajax({
					handler : "lms1205formhandler",
					type : "POST",
					dataType : "json",
					data : {
						formAction : "deleteUploadFile",
						fileOid:data.oid
					},
					success : function(obj) {
						$("#gridfile6").trigger("reloadGrid");
					}
				});
			}else{
				return ;
			}
		});
	});
	
	//檔案上傳grid
	$("#gridfile6").iGrid({
		handler : 'lms1205gridhandler',
		height : 150,
		sortname : 'srcFileName',
		postData : {
			formAction : "queryfile",
			fieldId:"upFile1305",
			mainId:responseJSON.mainId
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rowNum : 15,
		//multiselect : true,
		colModel : [ {
			colHeader :i18n.lms1305s05['L120M01D.grid1'],//原始檔案名稱,
			name : 'srcFileName',
			width : 120,
			align: "left",
			sortable : false,
			formatter : 'click',
			onclick : openDocFile
		}, {
			colHeader : i18n.lms1305s05['L120M01D.grid2'],//檔案說明
			name : 'fileDesc',
			width : 140,
			sortable : false
		}, {
			colHeader : i18n.lms1305s05['L120M01D.grid3'],//上傳時間
			name : 'uploadTime',
			width : 140,
			sortable : false
		}, {
			name : 'oid',
			hidden : true
		}]
	});
	
	// J-112-0013 (112) 隱藏企金簽報書之綜合評估/往來彙總下之風險權數之頁籤 
	hidePanelByCallAjax();
});

function hidePanelByCallAjax() {
	$.ajax({
		handler : "lms1205formhandler",
		type : "POST",
		dataType : "json",
		action : "hidePanelLmsS07",
		data : {
			mainId : responseJSON.mainId
		},
		success : function(json) {
			// 舊的風險權數頁籤隱藏
			if(json.hide_lmss07b){
				$("#lms4d").hide();// 隱藏這個id
			}
		}
	});
}

function loadHtml(pageUrl, page){
	responseJSON["handler"] = "lms1305formhandler";
	responseJSON["s07page"]=page;
	if($("#lmss07_panel").find("#tab-"+page).attr("open") == "true"){	
		$("#lmss07_panel").find("#tab-"+page).load(pageUrl, function(){
			//查詢分頁資料
			$.ajax({		
				handler : "lms1305formhandler",
				type : "POST",
				dataType : "json",
				data : 
				{
					formAction : "queryL120S07",
					mainId : responseJSON.mainId,
					page: page
				},
				success : function(jsonInit) {
					if(page == "7c"){
//						$("#LMS1205S07Form03").setData(jsonInit.LMS1205S07Form03,false);
					}else if(page == "7f"){
//						$("#LMS1205S07Form05").setData(jsonInit.LMS1205S07Form05,false);
//						$("#LMS1205S07Form05").find(".hasDeeds").trigger("change");
//						//J-106-0213-001 Web e-Loan 授信管理系統新增企業誠信經營評估
//						$("#LMS1205S07Form05").find(".hasBadFaith").trigger("change");
//						// J-108-0166 社會與環境風險評估改版
//						if(jsonInit.LMS1205S07Form05.ver == "01"){
//							$("#LMS1205S07Form05").find("#ver1").show();
//							$("#LMS1205S07Form05").find("#ver2").hide();
//						} else {
//							var item1_D1 = jsonInit.LMS1205S07Form05.item1_D1.split("|");
//							for (var i = 0; i < item1_D1.length; i++) {
//								var val = item1_D1[i];
//								$("[name='item1_D1'][value=" + val + "]").attr("checked", true);
//							}
//							var item1_D2 = jsonInit.LMS1205S07Form05.item1_D2.split("|");
//							for (var i = 0; i < item1_D2.length; i++) {
//								var val = item1_D2[i];
//								$("[name='item1_D2'][value=" + val + "]").attr("checked", true);
//							}
//							var item1_D3 = jsonInit.LMS1205S07Form05.item1_D3.split("|");
//							for (var i = 0; i < item1_D3.length; i++) {
//								var val = item1_D3[i];
//								$("[name='item1_D3'][value=" + val + "]").attr("checked", true);
//							}
//							var item2_D1 = jsonInit.LMS1205S07Form05.item2_D1.split("|");
//							for (var i = 0; i < item2_D1.length; i++) {
//								var val = item2_D1[i];
//								$("[name='item2_D1'][value=" + val + "]").attr("checked", true);
//							}
//							var item2_D2 = jsonInit.LMS1205S07Form05.item2_D2.split("|");
//							for (var i = 0; i < item2_D2.length; i++) {
//								var val = item2_D2[i];
//								$("[name='item2_D2'][value=" + val + "]").attr("checked", true);
//							}
//							var item2_D3 = jsonInit.LMS1205S07Form05.item2_D3.split("|");
//							for (var i = 0; i < item2_D3.length; i++) {
//								var val = item2_D3[i];
//								$("[name='item2_D3'][value=" + val + "]").attr("checked", true);
//							}
//
//							$("#LMS1205S07Form05").find("#itemSpan_item1_D1,#itemSpan_item1_D2,#itemSpan_item1_D3").trigger("change");
//							$("#LMS1205S07Form05").find("#itemSpan_item2_D1,#itemSpan_item2_D2,#itemSpan_item2_D3").trigger("change");
//
//							if(jsonInit.LMS1205S07Form05.hasD1 == "Y"){
//								$("#LMS1205S07Form05").find("#jsonD1").show();
//							} else {
//								$("#LMS1205S07Form05").find("#jsonD1").hide();
//							}
//							if(jsonInit.LMS1205S07Form05.hasD2 == "Y"){
//								$("#LMS1205S07Form05").find("#jsonD2").show();
//							} else {
//								$("#LMS1205S07Form05").find("#jsonD2").hide();
//							}
//							if(jsonInit.LMS1205S07Form05.hasD3 == "Y"){
//								$("#LMS1205S07Form05").find("#jsonD3").show();
//							} else {
//								$("#LMS1205S07Form05").find("#jsonD3").hide();
//							}
//
////						if(jsonInit.LMS1205S07Form05.ver == "01"){
////							$("#LMS1205S07Form05").find("#ver1").show();
////							$("#LMS1205S07Form05").find("#ver2").hide();
////						} else {
//							$("#LMS1205S07Form05").find("#ver2").show();
//							$("#LMS1205S07Form05").find("#ver1").hide();
//						}
					}
					$("#showBorrowData").reset();
					$("#showBorrowData").setData(jsonInit.showBorrowData,false);
					$("#lmss07_panel").find("#tab-"+page).attr("open",false);
				}
			});
			if(responseJSON.readOnly == "true"){
				if(page == "7b"){
					var $LMS1205S07Form02 = $("#LMS1205S07Form02");
					var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
					$LMS1205S07Form02.readOnlyChilds(true);
					$LMS1205S07Form02.find("button").hide();
					$tLMS1205S07Form02.readOnlyChilds(true);
					$tLMS1205S07Form02.find("button").hide();
				}else if(page == "7c"){
					var $LMS1205S07Form03 = $("form#LMS1205S07Form03");
					var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");
					var $tLMS1205S07Form03_btn = $("#tLMS1205S07Form03_btn");
					$LMS1205S07Form03.readOnlyChilds(true);
					$LMS1205S07Form03.find("button").hide();
					$tLMS1205S07Form03.readOnlyChilds(true);
					$tLMS1205S07Form03.find("button").hide();
					$tLMS1205S07Form03_btn.readOnlyChilds(true);
                    $tLMS1205S07Form03_btn.find("button").hide();
				}else if(page == "7d"){
					var $LMS1205S07Form04 = $("#LMS1205S07Form04");
					var $tLMS1205S07Form04 = $("#tLMS1205S07Form04");
					$LMS1205S07Form04.readOnlyChilds(true);
					$LMS1205S07Form04.find("button").hide();
					$tLMS1205S07Form04.readOnlyChilds(true);
					$tLMS1205S07Form04.find("button").hide();
				}				
			}
		});				
	}
}

function openDocFile(cellvalue, options, rowObject){
    $.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:rowObject.oid
        }
    });
};

function gridSelectThick(list) {
	$.ajax({
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		data : 
		{
			formAction : "findFfbody",
		mainId : responseJSON.mainId,
		cesMainId : list
		},
		success : function(json) {
		setCkeditor2("ffbody",json.ffbody);
		}
	});		
/*
	$("#gridSelectThick").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss07["L1205S07.thickbox16"],
		width : 640,
		height : 350,
		modal : true,
		align : 'center',
		valign: 'bottom',
		i18n:i18n.lmss07,
		buttons : {
			"L1205S07.thickbox1" : function() {
					var row = $("#gridSelectMainId").getGridParam('selrow'); 
					var list = "";
					var data = $("#gridSelectMainId").getRowData(row);
					list = data.mainId;
					list = (list == undefined ? "" : list);
					if(list != ""){
				  		$.ajax({
				  			handler : "lms1305formhandler",
				  			type : "POST",
				  			dataType : "json",
				  			data : 
				  			{
				  				formAction : "findFfbody",
								mainId : responseJSON.mainId,
								cesMainId : list
				  			},
				  			success : function(json) {
				  				setCkeditor2("ffbody",json.ffbody);
				  			}
				  		});				
						$.thickbox.close();	
					}
			},
			"L1205S07.thickbox2" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
*/
}
function gridSelectMainId(){
    var gridSelectMainId = $("#gridSelectMainId").iGrid({
		handler : 'lms1205gridhandler',
		height : 175,
		sortname : 'custName',
		postData : {
			formAction : "queryCesMainIdss2",
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rownumbers:true,
		rowNum:10,
		//multiselect : true,
		colModel : [ {
			colHeader : i18n.lms1305s05["L120M01D.grid7"], //主要借款人
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName' //col.id
		},{
			colHeader : i18n.lms1305s05["L120M01D.grid4"], //核准日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'approveTime' //col.id
		},{
			colHeader : i18n.lms1305s05["L120M01D.grid9"], //文件狀態
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'docStatus' //col.id
		},{
			colHeader : i18n.lms1305s05["L1205S07.grid12"], //徵信報告編號
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'cesId' //col.id
		},{
			colHeader : i18n.lms1305s05["L120M01D.grid5"], //文件建立者
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'creator' //col.id
		},{
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		}],
		ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
    });
}

function getLocalData(){
	var oid = $("#tLMS1205S07Form04").find("#oldOid").val();
	var $tLMS1205S07Form04 = $("#tLMS1205S07Form04");
	$.ajax({
		handler : "lms1305formhandler",
		type : "POST",
		dataType : "json",
		data : 
		{
			formAction : "queryL120s06bl",
			oid : oid,
			typCd2 : $tLMS1205S07Form04.find("#typCd2").val(),
			custId2 : $tLMS1205S07Form04.find("#custId2").val(),
			dupNo2 : $tLMS1205S07Form04.find("#dupNo2").val(),
			custName2 : $tLMS1205S07Form04.find("#custName2").val(),
			requery: true,
			payDeadline2: $tLMS1205S07Form04.find("#payDeadline2").val(),
			guarantor2: $tLMS1205S07Form04.find("#guarantor2").val(),
			purpose2: $tLMS1205S07Form04.find("#purpose2").val(),
			lnSubject2: $tLMS1205S07Form04.find("#lnSubject2").val(),
			cntrNo2: $tLMS1205S07Form04.find("#cntrNo2").val(),
			property2: $tLMS1205S07Form04.find("#property2").val(),
			currentApplyCurr2: $tLMS1205S07Form04.find("#currentApplyCurr2").val(),
			currentApplyAmt2: $tLMS1205S07Form04.find("#currentApplyAmt2").val(),
			gutPercent2: $tLMS1205S07Form04.find("#gutPercent2").val(),
			guarantorMemo2: $tLMS1205S07Form04.find("#guarantorMemo2").val()
		},
		success : function(obj) {
			$("#tLMS1205S07Form04").setData(obj.tLMS1205S07Form04,false);
		}
	});	
}
function inputSearch() {
	var nowDate = new Date();
	var MM = nowDate.getMonth();
	var YY = nowDate.getFullYear();
	var SMM;
	var SYY;
	if(MM == 0){
		MM = 12;
	}
	if(MM > 5){
		SMM = MM - 5;
		SYY = YY;
	}else{
		SMM = MM + 12 - 5;
		SYY = YY-1; 
	}
	var $tLMS1205S07Form03b = $("#tLMS1205S07Form03b");
	$tLMS1205S07Form03b.find("#queryDateS0").val(SYY);
	$tLMS1205S07Form03b.find("#queryDateS1").val(SMM);
	$tLMS1205S07Form03b.find("#queryDateE0").val(YY);
	$tLMS1205S07Form03b.find("#queryDateE1").val(MM);	
	$("#inputSearch").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss07["L1205S07.thickbox8"],
		width : 450,
		height : 210,
		modal : true,
		align : 'center',
		valign: 'bottom',
		i18n:i18n.lmss07,
		buttons : {
			"L1205S07.thickbox1" : function() {
				var $tLMS1205S07Form03b = $("#tLMS1205S07Form03b");
				var $LMS1205S07Form03 = $("form#LMS1205S07Form03");
				if($tLMS1205S07Form03b.valid()){
					if($tLMS1205S07Form03b.find("#queryDateS1").val()< 1
					|| $tLMS1205S07Form03b.find("#queryDateS1").val()> 12
					|| $tLMS1205S07Form03b.find("#queryDateE1").val()< 1
					|| $tLMS1205S07Form03b.find("#queryDateE1").val()> 12){
						CommonAPI.showMessage(i18n.lmss07["l120v01.error3"]);
						return;
					}else if($tLMS1205S07Form03b.find("#queryDateS0").val()<=0
						   ||$tLMS1205S07Form03b.find("#queryDateE0").val()<=0){
						CommonAPI.showMessage(i18n.lmss07["l120v01.error8"]);
						return;
					}else if($tLMS1205S07Form03b.find("#queryDateE0").val()-
						     $tLMS1205S07Form03b.find("#queryDateS0").val()<0){
						CommonAPI.showMessage(i18n.lmss07["l120v01.error9"]);
						return;
					}else if(($tLMS1205S07Form03b.find("#queryDateE0").val()-
						      $tLMS1205S07Form03b.find("#queryDateS0").val()==0) &&
							 ($tLMS1205S07Form03b.find("#queryDateE1").val()-
						      $tLMS1205S07Form03b.find("#queryDateS1").val()<0)
							 ){
						CommonAPI.showMessage(i18n.lmss07a["l120v01.error9"]);
						return;		
					}else{
						$.thickbox.close();
				  		$.ajax({
				  			handler : "lms1305formhandler",
				  			type : "POST",
				  			dataType : "json",
				  			data : 
				  			{
				  				formAction : "saveL120s04a2",								
				  				mainId : responseJSON.mainid,
								LMS1205S07Form03 : JSON.stringify($LMS1205S07Form03.serializeData()),
				  				queryDateS0 : $tLMS1205S07Form03b.find("#queryDateS0").val(),
				  				queryDateS1 : $tLMS1205S07Form03b.find("#queryDateS1").val(),
				  				queryDateE0 : $tLMS1205S07Form03b.find("#queryDateE0").val(),
				  				queryDateE1 : $tLMS1205S07Form03b.find("#queryDateE1").val(),
                                oidL120s04d : $("#oidL120s04d").val()
				  			},
				  			success : function(json) {
								var $LMS1205S07Form03 = $("form#LMS1205S07Form03");
				  				$LMS1205S07Form03.setData(json.LMS1205S07Form03);
				  				$LMS1205S07Form03.find("#gridview_A-1-8-1").trigger("reloadGrid");
				  			}
				  		});										
					}
				}			
			},
			// "刪除本頁": function() {alert("刪除本頁");},
			"L1205S07.thickbox2" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}
function setTotal(){
	var count=$("#gridview_A-1-8-1").jqGrid('getGridParam','records');
	if(count == 0){
		CommonAPI.showMessage(i18n.lmss07["L1205S07.error14"]);
		return;
	}
	var $LMS1205S07Form03 = $("form#LMS1205S07Form03");
	$.ajax({
		handler : "lms1305formhandler",
		type : "POST",
		dataType : "json",
		data : {
			formAction : "saveTotal",
			LMS1205S07Form03 : JSON.stringify($LMS1205S07Form03.serializeData()),
			mainId : responseJSON.mainid,
			queryDateS : $LMS1205S07Form03.find("#queryDateS").html(),
			queryDateE : $LMS1205S07Form03.find("#queryDateE").html(),
            oidL120s04d: $("#oidL120s04d").val()
		},
		success : function(json) {
			var $LMS1205S07Form03 = $("form#LMS1205S07Form03");
			$LMS1205S07Form03.setData(json.LMS1205S07Form03);
			$LMS1205S07Form03.find("#gridview_A-1-8-1").trigger("reloadGrid");
		}
	});	
}

/**
 * 查詢並開啟往來實績彙總表ThickBox
 */
function tL120s04b(cellvalue, options, rowObject){
    var docKind = rowObject.docKind;
	if(docKind=="A"){
		tL120s04b_A(cellvalue, options, rowObject);
		
	}else{
		tL120s04b_0(cellvalue, options, rowObject);
	}
}	
function tL120s04b_0(cellvalue, options, rowObject){
	var oid = rowObject.oid;
	// 進行查詢 
	$.ajax({		//查詢主要借款人資料
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "queryL120s04b",
		data : {
			oid : oid,
			mainId : responseJSON.mainId,
            oidL120s04d: $("#oidL120s04d").val()
		},
		success : function(json) {
			var $formL120s04b = $("#formL120s04b");

			/*
			var grpGrrd = $formL120s04b.find("#grpGrrd option:selected").val();
			// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat 2012/11/27
			if(grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3" || grpGrrd == "4" || grpGrrd == "5"){
				// 顯示屬主要集團企業...
				$formL120s04b.find(".spectialHide").show();
			}else{
				// 隱藏屬主要集團企業...
				$formL120s04b.find(".spectialHide").hide();
			}
			*/
			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			var grpYear = json.formL120s04b.grpYear;
			var grpGrrd = json.formL120s04b.grpGrrd;
			if (grpYear) {
				// 判斷2017以後為新版，之前為舊版
				if (parseInt(grpYear, 10) >= 2017) {

					var obj = CommonAPI
							.loadCombos([ "GroupGrade2017" ]);

					// 評等等級
					$("#grpGrrd").setItems({
						item : obj.GroupGrade2017,
						format : "{key}"
					});

					// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller
					// eddedat 2012/11/27
					if (grpGrrd == "1" || grpGrrd == "2"
							|| grpGrrd == "3" || grpGrrd == "4"
							|| grpGrrd == "5" || grpGrrd == "6"
							|| grpGrrd == "7") {
						// 顯示屬主要集團企業...
						$formL120s04b.find(".spectialHide").show();
					} else {
						// 隱藏屬主要集團企業...
						$formL120s04b.find(".spectialHide").hide();
					}
				} else {

					var obj = CommonAPI.loadCombos([ "GroupGrade" ]);

					// 評等等級
					$("#grpGrrd").setItems({
						item : obj.GroupGrade,
						format : "{key}"
					});

					// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller
					// eddedat 2012/11/27
					if (grpGrrd == "1" || grpGrrd == "2"
							|| grpGrrd == "3" || grpGrrd == "4"
							|| grpGrrd == "5") {
						// 顯示屬主要集團企業...
						$formL120s04b.find(".spectialHide").show();
					} else {
						// 隱藏屬主要集團企業...
						$formL120s04b.find(".spectialHide").hide();
					}
				}

			} else {

				var obj = CommonAPI.loadCombos([ "GroupGrade" ]);

				// 評等等級
				$("#grpGrrd").setItems({
					item : obj.GroupGrade,
					format : "{key}"
				});

				// 如果沒有評等年度，以舊版執行
				// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat
				// 2012/11/27
				if (grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3"
						|| grpGrrd == "4" || grpGrrd == "5") {
					// 顯示屬主要集團企業...
					$formL120s04b.find(".spectialHide").show();
				} else {
					// 隱藏屬主要集團企業...
					$formL120s04b.find(".spectialHide").hide();
				}
			}
			
            $formL120s04b.setData(json.formL120s04b);
            // J-111-0052 修改借戶暨關係戶與本行往來實績彙總表
            var showHins = json.formL120s04b.showHins;
            if(showHins == "Y"){
                $formL120s04b.find("#hinsDiv").show();
            } else {
                $formL120s04b.find("#hinsDiv").hide();
            }
			var grpNo = $formL120s04b.find("#grpNo").html();
			
			
				$("#tL120s04b").thickbox({ // 使用選取的內容進行彈窗
					title : i18n.lmss07["L1205S07.grid43"],
					width : 965,
					height : 480,
					modal : true,
					i18n:i18n.def,
					buttons : {
						"saveData" : function() {
							$("#tL120s04bDate").thickbox({
								title : "",
								width : 800,
								height : 200,
								modal : false,
								i18n:i18n.def,
								buttons : {
									"sure" : function(){
										if(!$("#tL120s04bDateTmpForm").find("#_docDateYear,#_docDateMonth").valid()){
											return false;
										}
										var beginMonth = 1;
										var endMonth = parseInt($("#_docDateMonth").val(), 10);
										var year = parseInt($("#_docDateYear").val(), 10);
										$("#docDate3,#docDate6").val(year+"/"+beginMonth+"~"+endMonth+"月");										
										$.thickbox.close();
										if($formL120s04b.valid()){
											$formL120s04b.find(".numeric").each(function(i){
												$(this).val(RemoveStringComma($(this).val()));
											});
											// 進行報酬率計算
											for(var i=1; i<=6; i++){
												// D利潤貢獻(TWD)
												
												//var profitAmt = ($formL120s04b.find("#profitAmt" + i).val() == undefined || 
												//$formL120s04b.find("#profitAmt" + i).val() == null || 
												//$formL120s04b.find("#profitAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#profitAmt" + i).val(),10);
												var profitAmt = 0;									
												if($formL120s04b.find("#profitAmt" + i).val() == undefined || 
												$formL120s04b.find("#profitAmt" + i).val() == null || 
												$formL120s04b.find("#profitAmt" + i).val() == ""){
													profitAmt = 0;
													$formL120s04b.find("#profitAmt" + i).val(profitAmt);
												}else {
													profitAmt = parseInt($formL120s04b.find("#profitAmt" + i).val(),10);
												}								
																								
												// A平均授信(TWD)
												//var avgLoanAmt = ($formL120s04b.find("#avgLoanAmt" + i).val() == undefined || 
												//$formL120s04b.find("#avgLoanAmt" + i).val() == null || 
												//$formL120s04b.find("#avgLoanAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#avgLoanAmt" + i).val(),10);
												
												var avgLoanAmt = 0;
												if($formL120s04b.find("#avgLoanAmt" + i).val() == undefined || 
												$formL120s04b.find("#avgLoanAmt" + i).val() == null || 
												$formL120s04b.find("#avgLoanAmt" + i).val() == ""){
													avgLoanAmt = 0;
													$formL120s04b.find("#avgLoanAmt" + i).val(avgLoanAmt);
												} else {
													avgLoanAmt = parseInt($formL120s04b.find("#avgLoanAmt" + i).val(),10);
												}
												
												
												// B應收帳款無追索買方承購平均餘額(TWD)
												//var rcvBuyAvgAmt = ($formL120s04b.find("#rcvBuyAvgAmt" + i).val() == undefined || 
												//$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == null || 
												//$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#rcvBuyAvgAmt" + i).val(),10);
												
												var rcvBuyAvgAmt = 0;
												if($formL120s04b.find("#rcvBuyAvgAmt" + i).val() == undefined || 
												$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == null || 
												$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == ""){
													rcvBuyAvgAmt = 0;
													$formL120s04b.find("#rcvBuyAvgAmt" + i).val(rcvBuyAvgAmt);
												}else {
													rcvBuyAvgAmt = parseInt($formL120s04b.find("#rcvBuyAvgAmt" + i).val(),10);
												}						
												
												// C應收帳款無追索權賣方融資平均餘額(TWD)
												//var rcvSellAvgAmt = ($formL120s04b.find("#rcvSellAvgAmt" + i).val() == undefined || 
												//$formL120s04b.find("#rcvSellAvgAmt" + i).val() == null || 
												//$formL120s04b.find("#rcvSellAvgAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#rcvSellAvgAmt" + i).val(),10);
												
												var rcvSellAvgAmt = 0;
												if($formL120s04b.find("#rcvSellAvgAmt" + i).val() == undefined || 
												$formL120s04b.find("#rcvSellAvgAmt" + i).val() == null || 
												$formL120s04b.find("#rcvSellAvgAmt" + i).val() == ""){
													rcvSellAvgAmt = 0;
													$formL120s04b.find("#rcvSellAvgAmt" + i).val(rcvSellAvgAmt);
												} else {
													rcvSellAvgAmt = parseInt($formL120s04b.find("#rcvSellAvgAmt" + i).val(),10);
												}
												
												// 報酬率% = D/(A-B+C)
												if((avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt) != 0){
													// 四捨五入取到小數點兩位																										
													if(i==3 || i == 6){
														//資料需要年化
														var num = new Number(profitAmt / (avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt)/(endMonth - beginMonth + 1)* 12 * 100);
														$formL120s04b.find("#profitRate" + i).html(parseFloat(num.toFixed(2)));
													}else{
														var num = new Number(profitAmt / (avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt) * 100);
														$formL120s04b.find("#profitRate" + i).html(parseFloat(num.toFixed(2)));
													}
																																	
												}
											}							
											// 進行儲存
											$.ajax({
												handler : responseJSON["handler"],
												type : "POST",
												dataType : "json",
												action : "saveL120s04b",
												data : {
													oid : oid,
													formL120s04b : JSON.stringify($formL120s04b.serializeData())
												},
												success : function(json) {
													//$.thickbox.close();
													$.thickbox.close();
													CommonAPI.showMessage(json.NOTIFY_MESSAGE);
												}
											});
										}
									}
								}
							});
						},
/*
						"print" : function(){
							if($("#gridview_A-1-8-2").jqGrid('getGridParam','records') <= 0){
								// 報表無資料
								CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
							}else{
								var count = 0;
								$.form.submit({
							        url: "../../simple/FileProcessingService",
							        target: "_blank",
							        data: {
							        	mainId : responseJSON.mainId,
							        	rptOid : "R24" + "^" + "",
										fileDownloadName : "l120r01.pdf",
										serviceName : "lms1205r01rptservice"
							        }
							    });		
							}							
						},
*/
						"close" : function() {
							 API.confirmMessage(i18n.def['flow.exit'], function(res){
									if(res){
										$.thickbox.close();
									}
						        });
						}
					}
				});			
		}
	});		
}

//A.共借戶與本行往來實績彙總表
function tL120s04b_A(cellvalue, options, rowObject){
	var oid = rowObject.oid;
	
	// 進行查詢 
	$.ajax({		//查詢主要借款人資料
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "queryL120s04b_a",
		data : {
			oid : oid,
			mainId : responseJSON.mainId
		},
		success : function(json) {
			var $formL120s04b_a = $("#formL120s04b_a");
			$formL120s04b_a.find("#showDetailHtml").html(DOMPurify.sanitize(json.showDetailResult));
			$formL120s04b_a.find(".numeric").each(function(i){
				$(this).val(util.addComma($(this).val()));
			});
				$("#tL120s04b_a").thickbox({ // 使用選取的內容進行彈窗
					title : i18n.lmss07["L1205S07.grid43"],
					width : 1000,
					height : 500,
					modal : true,
					i18n:i18n.def,
					buttons : {
						"saveData" : function() {
							var docDate6 = $("#docDate_3_1").val();
							$("#_docDateYear").val(docDate6.substring(0, 4));
							$("#_docDateMonth").val(docDate6.substring(docDate6.indexOf("~")+1, docDate6.length-1));
							
							$("#tL120s04bDate").thickbox({
								title : "",
								width : 800,
								height : 200,
								modal : false,
								i18n:i18n.def,
								buttons: {
									"sure": function(){
										if(!$("#tL120s04bDateTmpForm").find("#_docDateYear,#_docDateMonth").valid()){
											return false;
										}
										var beginMonth = 1;
										var endMonth = parseInt($("#_docDateMonth").val(), 10);
										var year = parseInt($("#_docDateYear").val(), 10);
										//$("#docDate3,#docDate6").val(year+"/"+beginMonth+"~"+endMonth+"月");		
										
										$("input[name^='docDate_3_']").val(year+"/"+beginMonth+"~"+endMonth+"月");		
																		
										$.thickbox.close();
										
										
										if($formL120s04b_a.valid()){
											$formL120s04b_a.find(".numeric").each(function(i){
												$(this).val(RemoveStringComma($(this).val()));
											});
											
											
											var custCount = $formL120s04b_a.find("#custCount").val() ;
											
											//進行合計計算***************
											//初始化合計欄位
											for (var i = 1; i <= 3; i++) {
												$formL120s04b_a.find("#avgDepositAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#avgLoanAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#rcvBuyAvgAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#rcvSellAvgAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#exportAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#importAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#profitAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#profitSalaryAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#profitTrustFdtaAmt" + "_" + i + "_"+ custCount).val(0);
												$formL120s04b_a.find("#profitRate" + "_" + i + "_"+ custCount).val(0);
											}
											//合計欄位
											for (var k = 1; k <= custCount-1; k++) {
											    for (var i = 1; i <= 3; i++) {
//													alert("#profitAmt" + "_" + i + "_"+ custCount+"="+
//													   $formL120s04b_a.find("#profitAmt" + "_" + i + "_"+ custCount).val()+"+"+$formL120s04b_a.find("#profitAmt" + "_" + i + "_"+ k).val()													
//													);

												   	$formL120s04b_a.find("#avgDepositAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#avgDepositAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#avgDepositAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													$formL120s04b_a.find("#avgLoanAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#avgLoanAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#avgLoanAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													$formL120s04b_a.find("#rcvBuyAvgAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#rcvBuyAvgAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#rcvBuyAvgAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													$formL120s04b_a.find("#rcvSellAvgAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#rcvSellAvgAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#rcvSellAvgAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													$formL120s04b_a.find("#exportAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#exportAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#exportAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													$formL120s04b_a.find("#importAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#importAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#importAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													 
													$formL120s04b_a.find("#profitAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#profitAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#profitAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													$formL120s04b_a.find("#profitSalaryAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#profitSalaryAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#profitSalaryAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
													$formL120s04b_a.find("#profitTrustFdtaAmt" + "_" + i + "_"+ custCount).val(
													     parseInt($formL120s04b_a.find("#profitTrustFdtaAmt" + "_" + i + "_"+ custCount).val(),10)+
														 parseInt($formL120s04b_a.find("#profitTrustFdtaAmt" + "_" + i + "_"+ k).val(),10)
												    );
													
												}
											}
												
												
											// 進行報酬率計算
											for (var k = 1; k <= custCount; k++) {
												for(var i=1; i<=3; i++){
													// D利潤貢獻(TWD)
													var profitAmt = 0;									
													if($formL120s04b_a.find("#profitAmt" + "_"+i + "_"+ k).val() == undefined || 
													$formL120s04b_a.find("#profitAmt" + "_"+i + "_"+ k).val() == null || 
													$formL120s04b_a.find("#profitAmt" + "_"+i + "_"+ k).val() == ""){
														profitAmt = 0;
														$formL120s04b_a.find("#profitAmt" + "_"+i + "_"+ k).val(profitAmt);
													}else {
														profitAmt = parseInt($formL120s04b_a.find("#profitAmt" + "_"+i + "_"+ k).val(),10);
													}								
																									
													// A平均授信(TWD)
													var avgLoanAmt = 0;
													if($formL120s04b_a.find("#avgLoanAmt" + "_"+i + "_"+ k).val() == undefined || 
													$formL120s04b_a.find("#avgLoanAmt" + "_"+i + "_"+ k).val() == null || 
													$formL120s04b_a.find("#avgLoanAmt" + "_"+i + "_"+ k).val() == ""){
														avgLoanAmt = 0;
														$formL120s04b_a.find("#avgLoanAmt" + "_"+i + "_"+ k).val(avgLoanAmt);
													} else {
														avgLoanAmt = parseInt($formL120s04b_a.find("#avgLoanAmt" + "_"+i + "_"+ k).val(),10);
													}
													
													
													// B應收帳款無追索買方承購平均餘額(TWD)
													var rcvBuyAvgAmt = 0;
													if($formL120s04b_a.find("#rcvBuyAvgAmt" + "_"+i + "_"+ k).val() == undefined || 
													$formL120s04b_a.find("#rcvBuyAvgAmt" + "_"+i + "_"+ k).val() == null || 
													$formL120s04b_a.find("#rcvBuyAvgAmt" + "_"+i + "_"+ k).val() == ""){
														rcvBuyAvgAmt = 0;
														$formL120s04b_a.find("#rcvBuyAvgAmt" + "_"+i + "_"+ k).val(rcvBuyAvgAmt);
													}else {
														rcvBuyAvgAmt = parseInt($formL120s04b_a.find("#rcvBuyAvgAmt" + "_"+i + "_"+ k).val(),10);
													}						
													
													// C應收帳款無追索權賣方融資平均餘額(TWD)
													var rcvSellAvgAmt = 0;
													if($formL120s04b_a.find("#rcvSellAvgAmt" + "_"+i + "_"+ k).val() == undefined || 
													$formL120s04b_a.find("#rcvSellAvgAmt" + "_"+i + "_"+ k).val() == null || 
													$formL120s04b_a.find("#rcvSellAvgAmt" + "_"+i + "_"+ k).val() == ""){
														rcvSellAvgAmt = 0;
														$formL120s04b_a.find("#rcvSellAvgAmt" + "_"+i + "_"+ k).val(rcvSellAvgAmt);
													} else {
														rcvSellAvgAmt = parseInt($formL120s04b_a.find("#rcvSellAvgAmt" + "_"+i + "_"+ k).val(),10);
													}
													
													// 報酬率% = D/(A-B+C)
													if((avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt) != 0){
														// 四捨五入取到小數點兩位																										
														if(i==3 || i == 6){
															//資料需要年化
															var num = new Number(profitAmt / (avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt)/(endMonth - beginMonth + 1)* 12 * 100);
															$formL120s04b_a.find("#profitRate" + "_"+i + "_"+ k).html(parseFloat(num.toFixed(2)));
														}else{
															var num = new Number(profitAmt / (avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt) * 100);
															$formL120s04b_a.find("#profitRate" + "_"+i + "_"+ k).html(parseFloat(num.toFixed(2)));
														}
																																		
													}
												}
											}
											
											// 進行儲存
											$.ajax({
												handler : responseJSON["handler"],
												type : "POST",
												dataType : "json",
												action : "saveL120s04b_a",
												data : {
													oid : oid,
													formL120s04b : JSON.stringify($formL120s04b_a.serializeData())
												},
												success : function(json) {
													//$.thickbox.close();
													$formL120s04b_a.find(".numeric").each(function(i){
														$(this).val(util.addComma($(this).val()));
													});
													$.thickbox.close();
													CommonAPI.showMessage(json.NOTIFY_MESSAGE);
												}
											});
											
										}
									}
								}
							});
							
							
						},
						"close" : function() {
							 API.confirmMessage(i18n.def['flow.exit'], function(res){
									if(res){
										$.thickbox.close();
									}
						        });
						}
					}
				});			
		}
	});		
}

/**
 * 產生往來實績彙總表
 */
function createReport(){
	var count=$("#gridview_A-1-8-1").jqGrid('getGridParam','records');
	if(count > 0){
		// L1205S07.confirm4=執行引進後會刪除已存在之借戶暨關係戶與本行往來實績彙總表，是否確定執行？
		CommonAPI.confirmMessage(i18n.lmss07["L1205S07.confirm4"], function(b){
			if (b) {					
				//是的function
				$.ajax({
					handler : responseJSON["handler"],
					type : "POST",
					dataType : "json",
					data : {
						formAction : "deleteL120s04b",
						mainId : responseJSON.mainid,
                        oidL120s04d: $("#oidL120s04d").val()
					},
					success : function(json) {
						$.thickbox.close();
						// 開始產生實績彙總表
						importReport();
					}
				});				
			}				
		});		
	}
}

/**
 * 刪除往來實績彙總表
 */
function deleteL120s04a(){
	var id = $("#gridview_A-1-8-2").getGridParam('selrow'); 
	var data = $("#gridview_A-1-8-2").getRowData(id);
//	if (data.oid == "" || data.oid == undefined || data.oid == null) {
//			CommonAPI.showMessage(i18n.lmss07["L1205S07.alert1"]);
//			return;
//	}
	CommonAPI.confirmMessage(i18n.def["action_003"], function(b){
		if (b) {					
			//是的function
			$.ajax({
				handler : responseJSON["handler"],
				type : "POST",
				dataType : "json",
				data : {
					formAction : "deleteL120s04b",
					mainId : responseJSON.mainid,
                    oidL120s04d: $("#oidL120s04d").val()
				},
				success : function(json) {
					// 更新實績彙總表Grid
					$("form#LMS1205S07Form03").find("#gridview_A-1-8-2").trigger("reloadGrid");
				}
			});				
		}				
	});
}

/**
 * 執行產生往來實績彙總表
 */
function importReport(){
	var $LMS1205S07Form03 = $("form#LMS1205S07Form03");
	$.ajax({
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		data : {
			formAction : "importL120s04b",
			mainId : responseJSON.mainid,
		  	queryDateS : $LMS1205S07Form03.find("#queryDateS").html(),
		  	queryDateE : $LMS1205S07Form03.find("#queryDateE").html(),
            oidL120s04d: $("#oidL120s04d").val()
			
		},
		success : function(json) {
			// 更新實績彙總表Grid
			$("form#LMS1205S07Form03").find("#gridview_A-1-8-2").trigger("reloadGrid");
		}
	});	
}

function cancelPrint(){
	var rows = $("#gridview_A-1-8-1").getGridParam('selarrrow');
	var list = "";
	var sign = ",";
	for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
			var data = $("#gridview_A-1-8-1").getRowData(rows[i]);
			list += ((list == "") ? "" : sign ) + data.oid;
		}
	}
	if (list == "") {
		CommonAPI.showMessage(i18n.lmss07["L1205S07.alert1"]);
		return;
	}	

	API.confirmMessage(i18n.lmss07["L1205S07.confirm1"],function(b){
		if(b){
			//是的function
			$.ajax({
				handler : "lms1305formhandler",
				type : "POST",
				dataType : "json",
				action : "cancelPrint",
				data : {
					listOid : list,
					mainId : responseJSON.mainid
				},
				success : function(json) {
					$("form#LMS1205S07Form03").find("#gridview_A-1-8-1").trigger("reloadGrid");
				}
			});
		}else{
			//否的function
			//CommonAPI.showMessage(i18n.lmss07["L1205S07.alert3"]);
		}
	})	
}
function undoPrint(){
	var rows = $("#gridview_A-1-8-1").getGridParam('selarrrow');
	var list = "";
	var sign = ",";
	for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
			var data = $("#gridview_A-1-8-1").getRowData(rows[i]);
			list += ((list == "") ? "" : sign ) + data.oid;
		}
	}
	if (list == "") {
		CommonAPI.showMessage(i18n.lmss07["L1205S07.alert1"]);
		return;
	}
	API.confirmMessage(i18n.lmss07["L1205S07.confirm2"],function(b){
		if(b){
			//是的function
			$.ajax({
				handler : responseJSON["handler"],
				type : "POST",
				dataType : "json",
				action : "undoPrint",
				data : {
					listOid : list,
					mainId : responseJSON.mainid
				},
				success : function(json) {
					$("form#LMS1205S07Form03").find("#gridview_A-1-8-1").trigger("reloadGrid");
				}
			});
		}else{
			//否的function
			//CommonAPI.showMessage(i18n.lmss07["L1205S07.alert4"]);
		}
	})	
}
function deleteAllCust(){
	API.confirmMessage(i18n.lmss07["L1205S07.confirm3"],function(b){
		if(b){
			//是的function
			$.ajax({
				handler : "lms1305formhandler",
				type : "POST",
				dataType : "json",
				data : 
				{
					formAction : "deleteL120s04a",
					mainId : responseJSON.mainid,
                    oidL120s04d: $("#oidL120s04d").val()
				},
				success : function(json) {
					$("form#LMS1205S07Form03").setData(json.LMS1205S07Form03,false);
					$("form#LMS1205S07Form03").find("#gridview_A-1-8-1").trigger("reloadGrid");
				}
			});
		}else{
			//否的function
			//CommonAPI.showMessage(i18n.lmss07["L1205S07.alert5"]);
		}
	})
}
function gridviewCust(){
	var gridView = $("#gridviewCust").iGrid({
		handler: 'lms1205gridhandler',
		//height: 345, //for 15 筆
		height: "230px", //for 10 筆
		//autoHeight: true,
		width: "100%",
		sortname : 'custName',
		postData : {
			formAction : "queryL120s01aById",
			custId : $("#custSearch").find("#searchId").val(),
			rowNum:10
		},
		// autofit: false,
		autowidth:true,
		caption: "&nbsp;",
		hiddengrid : false,
		colModel: [{
			  colHeader: i18n.lmss07["L1205S07.grida"],//"借款人名稱"
			  name: 'custName',
			  width: 100,
			  sortable: true
		},{
		  name: 'oid',
		  hidden: true
		}],
			ondblClickRow: function(rowid){
		}
	});
}
function gridViewShow(){
	var gridView = $("#gridviewShow").iGrid({
		handler: 'lms1205gridhandler',
		//height: 345, //for 15 筆
		height: "230px", //for 10 筆
		//autoHeight: true,
		width: "100%",
		sortname : 'custId',
		postData : {
			formAction : "queryL120s06a",
			mainId : responseJSON.mainid,
			rowNum:10
		},
		multiselect: true,hideMultiselect:false,
		// autofit: false,
		autowidth:true,
		caption: "&nbsp;",
		hiddengrid : false,
		colModel: [{
		  colHeader: i18n.lmss07["L1205S07.gridb"],//"本次授信對像"
		  name: 'custId',
		  align:"center",
		  width: 110,
		  sortable: true,
		  formatter : 'click',
		  onclick : openDoc2
		}, {
		  colHeader: i18n.lmss07["L1205S07.grid3"],//"本次科目"
		  name: 'lnSubject',
		  width: 80,
		  sortable: true
		}, {
		  colHeader: i18n.lmss07["L1205S07.grid4"],//"本次額度序號"
		  width: 90,
		  name: 'cntrNo',
		  sortable: true
		}, {
		  colHeader: i18n.lmss07["L1205S07.grid5"],//"對照授信對象"
		  name: 'custId2',
		  width: 110,
		  sortable: true,
		  align:"center"
		}, {
		  colHeader: i18n.lmss07["L1205S07.grid6"],//"對照科目"
		  name: 'lnSubject2',
		  width: 80,
		  sortable: true,
		  align:"right"
		}, {
		  colHeader: i18n.lmss07["L1205S07.grid7"],//"對照額度序號"
		  name: 'cntrNo2',
		  width: 90,
		  sortable: true,
		  align:"left"
		}, {
		  colHeader: "<span class='text-red'>"+i18n.lmss07['L1205S07.grid8']+"</span>",//列印模式
		  name: 'printMode',
		  width:50,
		  sortable: true,
		  align:"center"
		},{
		  name: 'oid',
		  hidden: true
		}],
			ondblClickRow: function(rowid){
				var data = gridView.getRowData(rowid);
				openDoc2(null, null, data);
		}
	});
}
function A_1_10_4(){
	$("#gridviewCC").iGrid({
   	 handler: "lms1405gridhandler",
        height: 200,
        rownumbers:true,
		multiselect : true,
		hideMultiselect : false,
        sortname: 'printSeq',
        sortorder: 'asc',
		caption: "&nbsp;",
		hiddengrid : false,
        postData : {
				formAction:"queryL140m01a",
				itemType:"1"
			},
        colModel: [{
            colHeader: i18n.lms1405s02["L140M01a.custName"],//借款人名稱
            width: 140,
            name: 'custName',
            sortable: true			 
        }, {
            colHeader: i18n.lms1405s02["L140M01a.cntrNo"],//"額度序號",
            name: 'cntrNo',
            width: 80,
            sortable: true
        }, {
            colHeader: i18n.lms1405s02["L140M01a.cntrNoCom"],//"共用額度序號",
            name: 'commSno',
            width: 80,
            sortable: true
        }, {
            colHeader: "&nbsp;",
            name: 'currentApplyCurr',
            width: 25,
            sortable: true,
			 align:"center"
        }, {
            colHeader: i18n.lms1405s02["L140M01a.moneyAmt"],//現請額度,
            name: 'currentApplyAmt',
            width: 100,
            sortable: true,
			 align:"right",
			 formatter:'currency', 
			 formatoptions:
			   {
			    thousandsSeparator: ",",
			    decimalPlaces: 0//小數點到第幾位
			    }
        }, {
            colHeader: i18n.lms1405s02["L140M01a.type"],//"性質"
            name: 'proPerty',
            width: 70,
            sortable: true,
            align:"center",
            formatter:proPertyFormatter
        }, {
            colHeader:i18n.lms1405s02["L140M01a.docStatus"], //"文件狀態",
            name: 'docStatus',
            width: 60,
            sortable: true,
			 align:"center"
        }, {
            colHeader: i18n.lms1405s02["L140M01a.branchId"],//"分行別",
            name: 'ownBrId',
            width: 80,
            sortable: true,
			 align:"center"
        },	{
            name: 'oid',
            hidden: true
        },{
            name: 'printSeq',
            hidden: true
        }],
        	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
    });
}

function proPertyFormatter(cellvalue,otions,rowObject){
	 //登錄授信科目的格式化
	var itemName='';	
	if(cellvalue == null || 	
	   cellvalue == undefined || 
	   cellvalue == ""){
		//授信科目為空!!
	}else{
		var list = cellvalue.split("|");
		if(cellvalue){
		itemName = i18n.lms1405s02["L140M01a.type"+list[0]];
			if(cellvalue.length > 1){
	  			for(var i =1; i< list.length ;i++ ){
	  				var itemone = i18n.lms1405s02["L140M01a.type"+list[i]];
	  				itemName = itemName +"、"+itemone; 
	  			}
			}
		}
	} 		
 	return itemName;
}

function test999(oid) {
	$("#borrower-data999").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss07["L1205S07.thickbox9"],
		width : 960,
		height : 500,
		modal : true,
		i18n:i18n.def,
		buttons : {
			"reQuery" : function() {
				var $LMS1205S07Form03 = $("form#LMS1205S07Form03");
				$.ajax({
					handler : "lms1305formhandler",
					type : "POST",
					dataType : "json",
					action : "rQueryL120s04a",
					data : {
						oid : oid,
						mainId : responseJSON.mainid,
		  				custId : $("#tLMS1205S07Form03").find("#custId").html(),
		  				dupNo : $("#tLMS1205S07Form03").find("#dupNo").html(),
		  				custName : $("#tLMS1205S07Form03").find("#custName").html(),
		  				queryDateS : $LMS1205S07Form03.find("#queryDateS").html(),
		  				queryDateE : $LMS1205S07Form03.find("#queryDateE").html(),
                        oidL120s04d: $("#oidL120s04d").val()
					},
					success : function(obj) {
						var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");	
						$tLMS1205S07Form03.setData(obj);
						for(o in obj.tLMS1205S07Form03.custRelation){
							$tLMS1205S07Form03.find("[name=custRelation]").each(function(i){
								if($(this).val() == obj.tLMS1205S07Form03.custRelation[o]){
									$(this).attr("checked",true);
								}
							});
						}
						$("LMS1205S07Form03").setData(obj.LMS1205S07Form03,false);						
						$("#gridview_A-1-8-1").trigger("reloadGrid");
					}
				});
			},
			"saveData" : function() {
				var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");
				var $LMS1205S07Form03 = $("form#LMS1205S07Form03");
				var count=$("#gridview_A-1-8-1").jqGrid('getGridParam','records');
				if(count == 0){
					CommonAPI.showMessage(i18n.lmss07["L1205S07.error14"]);
					return;
				}
				var list = "";
				var sign = ",";
				if ($tLMS1205S07Form03.valid()) {
					var checkSub = false;
					var kind = 0;
					var checkCou = 0;
					$tLMS1205S07Form03.find("[name=custRelation]:checkbox:checked").each(function(i){
						if ($(this).val() == 3) {
							checkSub = true;
							kind = 1;
						}
						else 
							if ($(this).val() == 4) {
								checkSub = true;
								kind = 2;
							}
						list += ((list == "") ? "" : sign) + $(this).val();
						checkCou++;
					});
					if (checkCou == 0) {
						CommonAPI.showMessage(i18n.lmss07["L1205S07.error15"]);
						return;
					}
					if (checkSub && list.length > 1) {
						if (kind == 1) {
							// 集團企業合計
							CommonAPI.showMessage(i18n.lmss07["L1205S07.error13"]);
						}
						else 
							if (kind == 2) {
								// 關係企業合計
								CommonAPI.showMessage(i18n.lmss07["L1205S07.error12"]);
							}
						return;
					}
					
					$.ajax({ //查詢主要借款人資料
						handler: "lms1305formhandler",
						type: "POST",
						dataType: "json",
						data: {
							formAction: "saveL120s04a",
							tLMS1205S07Form03 : JSON.stringify($tLMS1205S07Form03.serializeData()),
							mainId: responseJSON.mainid,
			  				custId : $("#tLMS1205S07Form03").find("#custId").html(),
			  				dupNo : $("#tLMS1205S07Form03").find("#dupNo").html(),							
							queryDateS: $LMS1205S07Form03.find("#queryDateS").html(),
							queryDateE: $LMS1205S07Form03.find("#queryDateE").html(),
							oid: oid,
							list: list,
                            oidL120s04d: $("#oidL120s04d").val()
						},
						success: function(json){
							var $LMS1205S07Form03 = $("form#LMS1205S07Form03");
							$LMS1205S07Form03.find("#gridview_A-1-8-1").trigger("reloadGrid");
							oid = json.newOid;
						}
					});
					//$.thickbox.close();
				}
			},
			// "刪除本頁": function() {alert("刪除本頁");},
			"close" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

function addData() {
	$("#formAdd").reset();
	var count=$("#gridview_A-1-8-1").jqGrid('getGridParam','records');
	if(count == 0){
		CommonAPI.showMessage(i18n.lmss07["L1205S07.error14"]);
		return;
	}	
	$("#addData").find("#showSel03").hide();
		$("#addData").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss07["L1205S07.thickbox11"],
		width : 600,
		height : 200,
		modal : true,
		align : 'center',
		valign: 'bottom',
		i18n:i18n.lmss07,
		buttons : {
			"L1205S07.thickbox1" : function() {				
				var gridCust = $("#gridview_A-1-8-1").getCol("custId");
				var gridName = $("#gridview_A-1-8-1").getCol("custName");
				if(!$("#selCus03").children().is("option")){
		  			CommonAPI.showMessage(i18n.lmss07["L1205S07.alert7"]);
		  			return;					
				}else if($("#selCus03 option:eq(0)").attr("selected")){
		  			CommonAPI.showMessage(i18n.lmss07["L1205S07.alert6"]);
		  			return;
				}else{
			  		var	custIdAll = $("#selCus03 option:selected").val();
					var custIdOnly = custIdAll.substring(0,custIdAll.length-1) + " " + custIdAll.substring(custIdAll.length-1);
					var	custName = $("#selCus03 option:selected").html();
					var custNameOnly = custName.substring(custIdAll.length+3);					
			  		$.ajax({
			  			handler : "lms1305formhandler",
			  			type : "POST",
			  			dataType : "json",
			  			data : 
			  			{
			  				formAction : "addL120s04a",
			  				custIdAll : custIdAll,
							custName : custName,
							mainId : responseJSON.mainId,
			  				queryDateS : $("form#LMS1205S07Form03").find("#queryDateS").html(),
			  				queryDateE : $("form#LMS1205S07Form03").find("#queryDateE").html(),
                            oidL120s04d: $("#oidL120s04d").val()
			  			},
			  			success : function(json) {		  				
			  				var tLMS1205S07Form03 = $("#tLMS1205S07Form03");
			  				tLMS1205S07Form03.setData(json);
			  				tLMS1205S07Form03.find("#createBY2").text(DOMPurify.sanitize(i18n.lmss07["L1205S07.createBY2"]));
			  				$("form#LMS1205S07Form03").find("#gridview_A-1-8-1").trigger("reloadGrid");
			  				test999("");
			  			}
			  		});	  		
					$.thickbox.close();			  			
		  		}				
			},
			// "刪除本頁": function() {alert("刪除本頁");},
			"L1205S07.thickbox2" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

// $(function(){
function A_1_8_1() {
	var gridA181 = $("#gridview_A-1-8-1")
			.iGrid({
				needPager: false,
				handler : 'lms1205gridhandler',
				// height: 345, //for 15 筆
				height : "300px", // for 10 筆
				// autoHeight: true,
				width : "100%",
				postData : {
					formAction : "queryL120s04a",
					mainId : responseJSON.mainid
					//rowNum:10
				},
				caption: "&nbsp;",
				hiddengrid : false,
				sortname : 'custRelation|profit|custId',
				sortorder:'asc|desc|asc',
				multiselect: true,
				//rownumbers : true,
				hideMultiselect:false,
				autowidth : true,
				colModel : [ {
					colHeader : i18n.lmss07["L1205S07.grid15"],//"需列印"
					name : 'prtFlag',
					align : "center",
					width : 40,
					sortable : false
				}, {
					colHeader : i18n.lmss07["L1205S07.grid16"],//"關係戶統編"
					name : 'custId',
					width : 80,
					sortable : false,
					formatter : 'click',
					onclick : openDoc3
				}, {
					colHeader : i18n.lmss07["L1205S07.grid17"],//"關係戶戶名"
					width : 160,
					name : 'custName',
					sortable : false
				}, {
					colHeader : i18n.lmss07["L1205S07.grid18"],//"與借戶關係"
					name : 'custRelationIndex',
					width : 80,
					sortable : false,
					align : "center"
				}, {
					colHeader : i18n.lmss07["L1205S07.grid19"],//"貢獻度"
					name : 'profit',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data == null){
							return "";
						}else{
							// 加入撇節符號
							return util.addComma(data);	
						}
					}					
				}, {
					colHeader : i18n.lmss07["L1205S07.grid20"],//"放款額度"
					name : 'loanQuota',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data == null){
							return "";
						}else{
							// 加入撇節符號
							return util.addComma(data);	
						}
					}					
				}, {
					colHeader : i18n.lmss07["L1205S07.grid21"],//"放款餘額"
					name : 'loanAvgBal',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data == null){
							return "";
						}else{
							// 加入撇節符號
							return util.addComma(data);	
						}
					}					
				}, {
					colHeader : i18n.lmss07["L1205S07.grid22"],//"活期存款"
					name : 'depTime',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data == null){
							return "";
						}else{
							// 加入撇節符號
							return util.addComma(data);	
						}
					}					
				}, {
					name : 'oid',
					hidden : true
				} ],
				ondblClickRow : function(rowid) {
					var data = gridA181.getRowData(rowid);
					openDoc3(null, null, data);
				}
			});
}

/**
 * 往來彙總實績表
 */
//function A_1_8_2() {
//	var gridA182 = $("#gridview_A-1-8-2")
//			.iGrid({
//				handler : 'lms1205gridhandler',
//				// height: 345, //for 15 筆
//				height : "240px", // for 10 筆
//				// autoHeight: true,
//				width : "100%",
//				postData : {
//					formAction : "queryL120s04a",
//					mainId : responseJSON.mainid,
//					rowNum:30
//				},
//				rowNum:30,
//				caption: "&nbsp;",
//				hiddengrid : false,
//				sortname : 'custRelation|custId',
//				//sortname : 'custRelation',
//				multiselect: true,
//				//rownumbers : true,
//				hideMultiselect:false,
//				// autofit: false,
//				autowidth : true,
//				colModel : [ {
//					colHeader : i18n.lmss07["L1205S07.grid42"],//"報表名稱"
//					width : 200,
//					name : 'rptName',
//					sortable : false
//				}, {
//					colHeader : i18n.lmss07["L1205S07.grid38"],//"建立日期"
//					width : 200,
//					name : 'createTime',
//					sortable : false
//				}, {
//					name : 'docKind',
//					hidden : true				
//				}, {
//					name : 'oid',
//					hidden : true
//				} ],
//				ondblClickRow : function(rowid) {
//					var data = gridA182.getRowData(rowid);
//					//openDoc3(null, null, data);
//				}
//			});
//}

function A_1_8_2() {
	var gridA182 = $("#gridview_A-1-8-2")
			.iGrid({
				handler : 'lms1205gridhandler',
				// height: 345, //for 15 筆
				height : "50", // for 10 筆
				// autoHeight: true,
				width : "100%",
				postData : {
					formAction : "queryL120s04b",
					mainId : responseJSON.mainid,
					rowNum:30
				},
				rowNum:30,
				caption: "&nbsp;",
				hiddengrid : false,
				//sortname : 'custRelation',
				//multiselect: true,
				//rownumbers : true,
				//hideMultiselect:false,
				// autofit: false,
				autowidth : true,
				colModel : [ {
					colHeader : i18n.lmss07["L1205S07.grid42"],//"報表名稱"
					width : 200,
					name : 'rptName',
					sortable : false
//					formatter : function(data) {
//						// L1205S07.grid43=借戶暨關係戶與本行往來實績彙總表
//						return i18n.lmss07["L1205S07.grid43"];
//					}								
				}, {
					colHeader : i18n.lmss07["L1205S07.grid38"],//"建立日期"
					width : 200,
					name : 'createTime',
					sortable : false,
					formatter : 'click',
					onclick : tL120s04b	
				}, {
					name : 'docKind',
					hidden : true								
				}, {
					name : 'oid',
					hidden : true
				} ],
				ondblClickRow : function(rowid) {
					var data = gridA182.getRowData(rowid);
					tL120s04b(null, null, data);
				}
			});
}

/**
 * 讀取往來彙總以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc3(cellvalue, options, rowObject) {
	ilog.debug(rowObject);
	$("#tLMS1205S07Form03").reset();
	$.ajax({
		handler : "lms1305formhandler",
		type : "POST",
		dataType : "json",
		data : 
		{
			formAction : "queryL120s04a",
			oid : rowObject.oid
		},
		success : function(obj) {
			var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");
			$tLMS1205S07Form03.setData(obj);
			for(o in obj.tLMS1205S07Form03.custRelation){
				$tLMS1205S07Form03.find("[name=custRelation]").each(function(i){
					var $this = $(this);
					if($this.val() == obj.tLMS1205S07Form03.custRelation[o]){
						$this.attr("checked",true);
					}
				});
			}
			$tLMS1205S07Form03.setData(obj.LMS1205S07Form03,false);
			test999(obj.tLMS1205S07Form03.oid);
		}
	});	
}

/**
 * 讀取利害關係人以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc2(cellvalue, options, rowObject) {
	ilog.debug(rowObject);
	$("#LMS1205S07Form04").reset();
	$.ajax({
		handler : "lms1305formhandler",
		type : "POST",
		dataType : "json",
		data : 
		{
			formAction : "queryL120s06b",
			oid : rowObject.oid,
			requery : false
		},
		success : function(obj) {
			var $tLMS1205S07Form04 = $("#tLMS1205S07Form04");
			bbbb999(obj.tLMS1205S07Form04.oid);
			$tLMS1205S07Form04.setData(obj.tLMS1205S07Form04);
			$tLMS1205S07Form04.find("#oldOid").val(obj.tLMS1205S07Form04.oid);
			if(obj.tLMS1205S07Form04.printMode == '2'){
				$tLMS1205S07Form04.find("[name='printMode']:eq(1)").attr("checked",true);
			}else{
				$tLMS1205S07Form04.find("[name='printMode']:eq(0)").attr("checked",true);
			}
		}
	});	
}

//利害關係人授信條件對照表-thickBox
function bbbb999(oid){
	  $("#borrower-data921").thickbox({	// 使用選取的內容進行彈窗
	    title : i18n.lmss07["L1205S07.thickbox14"],//'利害關係人授信條件對照表'
	    width : 960,
	    height : 500,
	    modal : true,
	    i18n:i18n.def,
	    buttons: {
			  	"saveData": function() {
			  		var $tLMS1205S07Form04 = $("#tLMS1205S07Form04");
					if($tLMS1205S07Form04.valid()){			  	
						$.ajax({		//查詢主要借款人資料
							handler : "lms1305formhandler",
							type : "POST",
							dataType : "json",
							data : 
							{
								formAction : "saveL120s06b",
								oid : oid,
								tLMS1205S07Form04 : JSON.stringify($tLMS1205S07Form04.serializeData())
							},
							success : function(json) {
								$("#LMS1205S07Form04").find("#gridviewShow").trigger("reloadGrid");
							}
						});
						$.thickbox.close();
				  	}
				},	   
				"close": function() {
					 API.confirmMessage(i18n.def['flow.exit'], function(res){
							if(res){
								$.thickbox.close();
							}
				        });
				}
		     }
		});
}	
function openbox(){
	$("#formSearch").find("#showSel04").hide();
	$("#formSearch").find("#showBrid").hide();
	$("#formSearch").reset();
	  $("#showGrid").hide();
	  $("#gridviewAA").setGridParam({'selrow' : null}).trigger("reloadGrid");
	  $("#LMS1205S07Form04").find("#gridviewAA").trigger("reloadGrid");	
	  $("#openbox").thickbox({	// 使用選取的內容進行彈窗
	    title : i18n.lmss07["L1205S07.thickbox14"],//'利害關係人授信條件對照表',
	    width : 700,
	    height : 500,
	    modal : true,
		align:"center",
		valign:"bottom",
		i18n:i18n.lmss07,
	    buttons: {
		  	"L1205S07.thickbox1": function() {
		  		var id = $("#gridviewAA").getGridParam('selrow'); 
		  		var data = $("#gridviewAA").getRowData(id);
				if(!$("#selCus04").children().is("option")){
		  			CommonAPI.showMessage(i18n.lmss07["L1205S07.alert7"]);
		  			return;					
				}else if($("#selCus04 option:eq(0)").attr("selected")){
		  			CommonAPI.showMessage(i18n.lmss07["L1205S07.alert6"]);
		  			return;
				}else if (data.mainId == "" || data.mainId == undefined || data.mainId == null) {
		  			CommonAPI.showMessage(i18n.lmss07["L1205S07.alert1"]);
		  			return;
		  			}	
		  		$.ajax({
		  			handler : "lms1305formhandler",
		  			type : "POST",
		  			dataType : "json",
		  			data : {
		  				formAction : "queryL120s06c",
		  				mainId : data.mainId,
		  				oldOid : $("#tLMS1205S07Form04").find("#oldOid").val()
		  			},
		  			success : function(json) {
		  				$("#showGrid").hide();
		  				$("#tLMS1205S07Form04").setData(json.tLMS1205S07Form04,false);
		  				$("#LMS1205S07Form04").find("#gridviewShow").trigger("reloadGrid");
		  			}
		  		});
		  		//gridviewAA		  		
				$.thickbox.close();
				},	   
			"L1205S07.thickbox2": function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$("#showGrid").hide();
							$.thickbox.close();
						}
			        });
				}
		     }
		});
}	

function deleteL120s06a(){
	var rows = $("#gridviewShow").getGridParam('selarrrow');
	var list = "";
	var sign = ",";
	for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
			var data = $("#gridviewShow").getRowData(rows[i]);
			list += ((list == "") ? "" : sign ) + data.oid;
		}
	}
	if (list == "") {
		CommonAPI.showMessage(i18n.lmss07["L1205S07.alert1"]);
		return;
	}
	$.ajax({
		handler : "lms1305formhandler",
		type : "POST",
		dataType : "json",
		data : {
			formAction : "deleteL120s06b",
			listOid : list,
			sign : sign
		},
		success : function(json) {
			$("#LMS1205S07Form04").find("#gridviewShow").trigger("reloadGrid");
		}
	});
	$.thickbox.close();
}
	
function openbox222(){
	  $("#openbox222").thickbox({	// 使用選取的內容進行彈窗
	    title : i18n.lmss07["L1205S07.thickbox15"],//'額度明細表選擇'
	    width : 800,
	    height :400,
	    modal : true,
		align:"center",
		valign:"bottom",
		i18n:i18n.lmss07,
	    buttons: {
		  	"L1205S07.thickbox1": function() {
					var rows = $("#gridviewCC").getGridParam('selarrrow');
					var list = "";
					var sign = ",";
					for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
						if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
							var data = $("#gridviewCC").getRowData(rows[i]);
							list += ((list == "") ? "" : sign ) + data.oid;
						}
					}
					if (list == "") {
						CommonAPI.showMessage(i18n.lmss07["L1205S07.alert1"]);
						return;
					}
					$.ajax({
						handler : "lms1305formhandler",
						type : "POST",
						dataType : "json",
						data : {
							formAction : "addL120s06a",
							listOid : list,
							mainId : responseJSON.mainid,
							sign : sign
						},
						success : function(json) {
							$("#LMS1205S07Form04").find("#gridviewShow").trigger("reloadGrid");
						}
					});
					$.thickbox.close();
				},	   
			"L1205S07.thickbox2": function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
				}
		     }
		});
}

function A_1_10_3(){
var grid = $("#gridviewAA").iGrid({
  handler: 'lms1205gridhandler',
  //height: 345, //for 15 筆
  height: "230px", //for 10 筆
  //autoHeight: true,
  width: 500,
	postData : {
		formAction : "queryL140m01a2",
		custId : $("#formSearch").find("#searchId").val(),
		textBrid : $("#textBrid").val(),
		rowNum:10
	},
	page : 1,
	caption: "&nbsp;",
	hiddengrid : false,
  sortname: 'custId',
//  multiselect: true,hideMultiselect:false,
 // autofit: false,
  autowidth:true,
  colModel: [{
      colHeader: i18n.lmss07["L1205S07.grid31"],//"簽案日期"
      name: 'caseDate',
		align:"center",
		width: 80,
      sortable: true
  }, {
      colHeader: i18n.lmss07["L1205S07.grid32"],//"案號",
      name: 'caseNo',
      width: 80,
      sortable: true
  }, {
      colHeader: i18n.lmss07["L1205S07.grid33"],//"額度序號",
      name: 'cntrNo',
      width: 80,
      sortable: true,
		align:"center"
  }, {
      colHeader: i18n.lmss07["L1205S07.grid34"],//"文件狀態",
      name: 'docStatus',
      width: 80,
      sortable: true,
						align:"right"
  }, {
      colHeader: i18n.lmss07["L1205S07.grid35"],//"現請額度",
      name: 'currentApplyAmt',
      width: 80,
      sortable: true,
						align:"right"
  }, {
      colHeader: i18n.lmss07["L1205S07.grid36"],//"科目",
      name: 'lnSubject',
      width:50,
      sortable: true,
		align:"center"
  },{
      name: 'oid',
      hidden: true
  }],
  	ondblClickRow: function(rowid){  
  }
});
}

/**
 * 引進額度明細表以讀取資本適足率
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function getBisFromCn() {
	$.ajax({		//查詢主要借款人資料
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "getBis",
		data : {
			mainId : responseJSON.mainid
		},
		success : function(obj) {		
			$("#LMS1205S07Form02").find("#gridview_A-1-9-1").trigger("reloadGrid");	//更新Grid內容
		}
	});	
}

/**
 * 寫回額度明細表
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function saveToL140m01a() {
	$.ajax({		//查詢主要借款人資料
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "saveToL140m01a",
		data : {
			mainId : responseJSON.mainid
		},
		success : function(obj) {
		}
	});	
}

/**
 * 讀取資本適足率以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc(cellvalue, options, rowObject) {
	ilog.debug(rowObject);
	$("#tLMS1205S07Form02").reset();
	$(function(){
		var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
		if(rowObject.crdFlag == i18n.lmss07["L1205S07.index2"]){
			$tLMS1205S07Form02.find("#crdRatio").attr({id: "collAmt", name: "collAmt"});
			$tLMS1205S07Form02.find("#rskMega").attr({id: "rskRatio", name: "rskRatio"});
			$tLMS1205S07Form02.find("#rskCrd").val("");
			$tLMS1205S07Form02.find("#rskAmt2").attr({id: "rskAmt1", name: "rskAmt1"});
			$tLMS1205S07Form02.find("#rskr2").attr({id: "rskr1", name: "rskr1"});
			$tLMS1205S07Form02.find("#camt2").attr({id: "camt1", name: "camt1"});
			$tLMS1205S07Form02.find("#bisr2").attr({id: "bisr1", name: "bisr1"});
			$tLMS1205S07Form02.find("#costr2").attr({id: "costr1", name: "costr1"});
		}else{
			$tLMS1205S07Form02.find("#collAmt").attr({id: "crdRatio", name: "crdRatio"});
			$tLMS1205S07Form02.find("#rskRatio").attr({id: "rskMega", name: "rskMega"});
			$tLMS1205S07Form02.find("#rskCrd").val("");
			$tLMS1205S07Form02.find("#rskAmt1").attr({id: "rskAmt2", name: "rskAmt2"});
			$tLMS1205S07Form02.find("#rskr1").attr({id: "rskr2", name: "rskr2"});
			$tLMS1205S07Form02.find("#camt1").attr({id: "camt2", name: "camt2"});
			$tLMS1205S07Form02.find("#bisr1").attr({id: "bisr2", name: "bisr2"});
			$tLMS1205S07Form02.find("#costr1").attr({id: "costr2", name: "costr2"});
		}
	});
	$.ajax({		//查詢主要借款人資料
		handler : responseJSON["handler"],
		type : "POST",
		dataType : "json",
		action : "queryBis",
		data : {
			oid : rowObject.oid
		},
		success : function(obj) {	
			$("#tLMS1205S07Form02").setData(obj);
			addBis(obj.tLMS1205S07Form02.oid);
		}
	});	
}

/**
 * 修改資本適足率
 */
function addBis(oid) {
	$("#thickboxAdd").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss07["L1205S07.thickbox12"],
		width : 965,
		height : 330,
		modal : true,
		i18n:i18n.def,
		buttons : {
			"reQuery" : function() {
				$.ajax({		//查詢主要借款人資料
					handler : responseJSON["handler"],
					type : "POST",
					dataType : "json",
					action : "queryBis",
					data : {
						oid : oid,
						crdFlag : $("#tLMS1205S07Form02").find("#crdFlag").html()
					},
					success : function(obj) {	
						$("#tLMS1205S07Form02").setData(obj);
					}
				});
			},
			"calculate" : function() {
				var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
				$.ajax({		//查詢主要借款人資料
					handler : responseJSON["handler"],
					type : "POST",
					dataType : "json",
					action : "calculateBis",
					data : {
						oid : oid,
						tLMS1205S07Form02 : JSON.stringify($tLMS1205S07Form02.serializeData())
					},
					success : function(json) {
						var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
						$tLMS1205S07Form02.setData(json.tLMS1205S07Form02);					
					}
				});	
			},
			"saveData" : function() {
				var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");								
				$.ajax({		//查詢主要借款人資料
					handler : responseJSON["handler"],
					type : "POST",
					dataType : "json",
					action : "calculateBis",
					data : {
						oid : oid,
						tLMS1205S07Form02 : JSON.stringify($tLMS1205S07Form02.serializeData())
					},
					success : function(json) {
						var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
						$tLMS1205S07Form02.setData(json.tLMS1205S07Form02);
						$.ajax({		//查詢主要借款人資料
							handler : responseJSON["handler"],
							type : "POST",
							dataType : "json",
							action : "saveBis",
							data : {
								oid : oid,
								tLMS1205S07Form02 : JSON.stringify($tLMS1205S07Form02.serializeData())
							},
							success : function(json) {
								$("#LMS1205S07Form02").find("#gridview_A-1-9-1").trigger("reloadGrid");
							}
						});											
					}
				});				
				//$.thickbox.close();
			},
			"close" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

//往來彙總(產報表)
function printR14(){
	if($("#gridview_A-1-8-1").jqGrid('getGridParam','records') <= 0){
		// 報表無資料
		CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
	}else{
		var pdfName = "l120r01.pdf";
		var count = 0;
		var content = "";
		content = "R14" + "^" + "^" + "^" + "^" + "^" + $("#oidL120s04d").val(),
//		content = "R14" + "^" + "";
		$.form.submit({
	        url: "../../simple/FileProcessingService",
	        target: "_blank",
	        data: {
	        	mainId : responseJSON.mainId,
	        	rptOid : content,
				fileDownloadName : pdfName,
				serviceName : "lms1205r01rptservice"
	        }
	    });	
	}
}

//資本適足率列印(產報表)
function printBIS(){
	if($("#gridview_A-1-9-1").jqGrid('getGridParam','records') <= 0){
		// 報表無資料
		CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
	}else{
		var count = 0;
		$.form.submit({
	        url: "../../simple/FileProcessingService",
	        target: "_blank",
	        data: {
	        	mainId : responseJSON.mainId,
	        	rptOid : "R04" + "^" + "",
				fileDownloadName : "l120r01.pdf",
				serviceName : "lms1205r01rptservice"
	        }
	    });		
	}
}

function applyEquatorPrinciples(){

    var $LMS1205S07Form05 = $("#LMS1205S07Form05");
	
    $.ajax({ // 查詢主要借款人資料
		handler : "lms1205formhandler",
		type : "POST",
		dataType : "json",
		data : {
			formAction : "applyEquatorPrinciples",
			mainId : responseJSON.mainId,
			LMS1205S07Form05: JSON.stringify($LMS1205S07Form05.serializeData())
		},
		success : function(json) {
			var newstr = json.equatorPrinciples;
			setCkeditor2("itemDscr03",newstr+$("#itemDscr03").val());
		}
	});
}


/**
 * 產生主要關係戶與本行授信往來比較表(Excel)
 */
function creExcel(){
	API.confirmMessage(i18n.def["confirmRun"],function(b){
		if(b){
			//是的function
			$.ajax({
				handler : responseJSON["handler"],
				type : "POST",
				dataType : "json",
				action : "creExcel",
				data : {
					mainId : $("#oidL120s04d").val(),//responseJSON.mainId,
					type : "1"
				},
				success : function(json) {
					$("#gridviewPare").trigger("reloadGrid");
				}
			});	
/*
	        $.form.submit({
	            url: "../../simple/FileProcessingService",
	            target: "_blank",
	            data: {
	                mainId: responseJSON.mainId,
	                fileDownloadName: "LMS1201M01A.xls",
	                serviceName: "lms1201xlsservice"
	            }
	        });
*/
		}else{
			//否的function
			//CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert5"]);
		}
	})
}

/**
 * J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
 * 產生集團／關係企業與本行授信往來條件比較表(Excel)
 */
function creExcel2() {
	API.confirmMessage(i18n.def["confirmRun"], function(b) {
		if (b) {
			// 是的function
			$.ajax({
				handler : responseJSON["handler"],
				type : "POST",
				dataType : "json",
				action : "creExcel2",
				data : {
					mainId : $("#oidL120s04d").val(),//responseJSON.mainId,
					type : "2"
				},
				success : function(json) {
					$("#gridviewPare").trigger("reloadGrid");
				}
			});
			/*
			 * $.form.submit({ url: "../../simple/FileProcessingService",
			 * target: "_blank", data: { mainId: responseJSON.mainId,
			 * fileDownloadName: "LMS1201M01A.xls", serviceName:
			 * "lms1201xlsservice" } });
			 */
		} else {
			// 否的function
			// CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert5"]);
		}
	})
}

