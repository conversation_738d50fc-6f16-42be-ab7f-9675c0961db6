/*_
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.rpt.handler.file;

import java.io.IOException;
import java.io.InputStream;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.rpt.service.LMS9541V04Service;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.handler.FileUploadHandler;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;

/**
 * <pre>
 * 查詢優惠額度資訊
 * </pre>
 * 
 * @since 2012/12/06
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/06,Vector,new
 *          </ul>
 */

@Scope("request")
@Controller("lms9541fileuploadhandler")
public class LMS9541FileUploadHandler extends FileUploadHandler {

	@Resource
	LMS9541V04Service service;

	@Override
	public IResult afterUploaded(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		MultipartFile uFile = params.getFile(params.getString("fieldId"));

		InputStream is = null;
		Workbook book = null;
		try {
			is = uFile.getInputStream();
			book = Workbook.getWorkbook(is);
			Sheet sheet = book.getSheet(0);
			String[] record = new String[18];
			String wrongLine = "";
			boolean error = false, rowError = false;// 判斷檔案內容是否錯誤
			for (int j = 1; j < sheet.getRows(); j++) {
				rowError = false;
				Cell[] row = sheet.getRow(j);
				if (row == null || row.length != 18) {
					error = rowError = true;
					wrongLine += "," + (j + 1);
				} else {
					record = new String[18];
					for (int i = 0; i < record.length; i++) {
						String cell = row[i].getContents();
						record[i] = cell;
					}
				}
				if (!rowError) {// 無錯誤則新增
					rowError = !service.save(record, record[1], record[14]);
					if (rowError) {
						error = true;
						wrongLine += "," + (j + 1);
					}
				}
			}
			book.close();
			if (error) {
				result.set("errorMsg", "someError");
				result.set("line", wrongLine.substring(1));
			} else {
				result.set("errorMsg", "nonError");
			}
		} catch (BiffException e) {
			result.set("errorMsg", "typeError");
		} catch (ArrayIndexOutOfBoundsException e2) {
			result.set("errorMsg", "contentError");
		} catch (Exception ex) {
			result.set("errorMsg", "unknow");
			logger.error("[getContent] Exception!!", ex.getMessage());
		} finally {
			if (book != null) {
				try {
					book.close();
				} catch (Exception e) {
					logger.debug("inputStream close Error", getClass());
				}
			}
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					logger.debug("inputStream close Error", getClass());
				}
			}
		}

		return result;
	}

	@Override
	public String getOperationName() {
		return "fileUploadOperation";
	}

}
