/* 
 * LMS1855Formhandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.handler.form;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

import jxl.write.WriteException;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.lrs.service.LMS1855Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 覆審 - 管理報表
 * </pre>
 * 
 * @since 2011/11/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/25,jessica,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1855m01formhandler")
public class LMS1855M01Formhandler extends AbstractFormHandler {

	@Resource
	BranchService branch;

	@Resource
	LMS1855Service service1855;

	@Resource
	DocCheckService docCheckService;

	@Resource
	CodeTypeService codetypeService;

	static final String SPACE = "";

	/**
	 * 搜尋分行
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	public IResult queryBranch(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, String> m = new TreeMap<String, String>();
		List<IBranch> bank = branch.getBranchOfGroup(user.getUnitNo());
		bank.add(branch.getBranch(user.getUnitNo()));
		for (IBranch b : bank) {
			String brName = Util.trim(b.getBrName());
			String brCode = b.getBrNo();
			m.put(brCode, brName);
		}

		CapAjaxFormResult bankList = new CapAjaxFormResult(m);
		result.set("item", bankList);
		return result;
	}

	/**
	 * <pre>
	 * 產生 EXCEL
	 * "1"= 逾期未覆審名單
	 * "2"= 企金戶未出現於覆審名單
	 * "3"= 撥貸後半年內辦理覆審檢核表
	 * "4"= 授信覆審明細檢核表
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 * @throws IOException 
	 * @throws WriteException 
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult transportExcel(PageParameters params)
			throws CapException, WriteException, IOException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		// EXCEL資料夾名
		String listName = "";
		String brNos = params.getString("brNos","");
		// 單一搜尋日期
		String dataDate = params.getString("dataDate","");
		if("".equals(dataDate)){
			dataDate = TWNDate.toAD(LMSUtil.getExMonthFirstDay(-1)).substring(0, 7);
		}
		// 管理報表類型
		String searchAction = params.getString("searchAction");
		int i = Util.parseInt(searchAction);

		HashMap<String, String> param = new HashMap<String, String>();
		String msg = null;
		String[] brNosTemp = brNos.split("\\^");
		// 產生Excelt成功與否
		switch (i) {
		case 1:
			// "1"= 逾期未覆審名單
			listName = "CTLNoList1";
			// 重產則會刪除該分行所有資料 (沒有起迄日期 預設值為"1911-01")
			//service1855.delete(brNo, listName, dataDate, "1911-01", 1);
			for(String brNo : brNosTemp){
				msg = service1855.findType1ByBrNoAndDate(brNo, dataDate,listName);
				if(!"".equals(msg)){
					param.put("msg", msg);
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", param), getClass());
				}
			}
			if("".equals(msg)){
				// EFD0018=INFO|執行成功|
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage( "EFD0018"));
			}
			break;
		case 2:
			// "2"= 企金戶未出現於覆審名單
			listName = "NoInCLTExcel";
			// 重產則會刪除該分行所有資料(沒有起迄日期 預設值為"1911-01")
			//service1855.delete(brNo, listName, dataDate, "1911-01", 2);
			dataDate = dataDate + "-01";
			//dataDate = TWNDate.toAD(CapDate.getCurrentTimestamp());
			for(String brNo : brNosTemp){
				msg = service1855.findType2ByBrNoAndDate(brNo, dataDate,listName);
				if(!"".equals(msg)){
					param.put("msg", msg);
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", param), getClass());
				}
			}
			if("".equals(msg)){
				// EFD0018=INFO|執行成功|
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage("EFD0018"));
			}
			break;
		case 3:
			// "3"= 撥貸後半年內辦理覆審檢核表
			listName = "LMSCTLExcel111";
			// 起始日期
			String starDate = params.getString("starDate");
			// 迄至日期
			String endDate = params.getString("endDate");

			// 重產則會刪除該分行所有資料(沒有起迄日期 預設值為"1911-01")
			//service1855.delete(brNo, listName, starDate, endDate, 3);
			for(String brNo : brNosTemp){
				msg = service1855.findType3ByBrNoAndDate(brNo, starDate, endDate,listName);
				if(!"".equals(msg)){
					param.put("msg", msg);
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", param), getClass());
				}
			}
			if("".equals(msg)){
				// EFD0018=INFO|執行成功|
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage("EFD0018"));
			}
			break;
		case 4:
			// ""4"= 授信覆審明細檢核表
			listName = "LMSCTLExcel112";
			// 重產則會刪除該分行所有資料 (沒有起迄日期 預設值為"1911-01")
			//service1855.delete(brNo, listName, "1911-01", "1911-01", 4);

//			msg = service1855.findType4ByBrNoAndDate(brNo, "0000-01",listName);
			dataDate = dataDate + "-01";
			for(String brNo : brNosTemp){
				msg = service1855.findType4ByBrNoAndDate(brNo, dataDate,listName);
				if(!"".equals(msg)){
					param.put("msg", msg);
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", param), getClass());
				}
			}
			if("".equals(msg)){
				// EFD0018=INFvO|執行成功|
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage("EFD0018"));
			}
			break;
		}
		return result;
	}

	/**
	 * <pre>
	 * 刪除
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteListL185m01a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] mainIdList = params.getStringArray("mainId");
		String listName = null;
		// 管理報表類型
		String searchAction = params.getString("searchAction");
		int i = Util.parseInt(searchAction);
		switch (i) {
		case 1:
			listName = "CTLNoList1";
			break;
		case 2:
			listName = "NoInCLTExcel";
			break;
		case 3:
			listName = "LMSCTLExcel111";
			break;
		case 4:
			listName = "LMSCTLExcel112";
			break;
		default:
			listName = "";
			break;
		}
		service1855.deleteL185m01a(mainIdList,listName);
		// EFD0019=INFO|刪除成功|
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage("EFD0019"));
		return result;
	};
}
