/* 
 * L000M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 近期已收案件檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L000M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L000M01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/**
	 * 文件種類
	 * <p/>
	 * L120M01A=案件簽報書<br/>
	 * L141M01A=聯行額度明細表<br/>
	 * L160M01A=動用審核表
	 */
	@Column(name = "CASETYPE", length = 10, columnDefinition = "VARCHAR(10)")
	private String caseType;

	/** 文件狀態 **/
	@Column(name = "DOCSTATUS", length = 3, columnDefinition = "CHAR(3)")
	private String docStatus;

	/**
	 * 刪除註記
	 * <p/>
	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 */
	@Column(name = "DELETEDTIME", columnDefinition = "TIMESTAMP")
	private Date deletedTime;

	/**
	 * 文件URL
	 * <p/>
	 * 文件URL
	 */
	@Column(name = "DOCURL", length = 40, columnDefinition = "VARCHAR(40)")
	private String docURL;

	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	/**
	 * 授權別
	 * <p/>
	 * 1授權內<br/>
	 * 2授權外
	 */
	@Column(name = "DOCKIND", length = 1, columnDefinition = "CHAR(1)")
	private String docKind;

	public String getDocKind() {
		return docKind;
	}

	public void setDocKind(String docKind) {
		this.docKind = docKind;
	}

	public String getDocCode() {
		return docCode;
	}

	public void setDocCode(String docCode) {
		this.docCode = docCode;
	}

	/**
	 * 企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	@Column(name = "DOCTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String docType;

	public String getDocType() {
		return docType;
	}

	public void setDocType(String docType) {
		this.docType = docType;
	}

	/**
	 * 案件別
	 * <p/>
	 * 1一般<br/>
	 * 2其他<br/>
	 * 3陳復/陳述案<br/>
	 * 4異常通報
	 */
	@Column(name = "DOCCODE", length = 1, columnDefinition = "CHAR(1)")
	private String docCode;

	/** 收件單位 **/
	@Column(name = "OWNBRID", length = 3, columnDefinition = "CHAR(3)")
	private String ownBrId;

	/** 統一編號 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 客戶名稱 **/
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/** 傳送單位 **/
	@Column(name = "SENDBRID", length = 3, columnDefinition = "CHAR(3)")
	private String sendBrid;

	/** 傳送時間 **/
	@Column(name = "SENDINFO", columnDefinition = "TIMESTAMP")
	private Date sendInfo;

	/** 原始文件oid **/
	@Column(name = "SRCOID", length = 32, columnDefinition = "CHAR(32)")
	private String srcOid;

	/** 原始文件mainId **/
	@Column(name = "SRCMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String srcMainId;

	/** 原始文件交易代碼 **/
	@Column(name = "SRCTXCODE", length = 6, columnDefinition = "CHAR(6)")
	private String srcTxCode;

	/**
	 * 三個月以上協議案註記(0:無,1:授管處三個月以內,2:債管處三個月以上)
	 * **/
	@Column(name = "NGFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String ngFlag;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 取得文件種類
	 * <p/>
	 * L120M01A=案件簽報書<br/>
	 * L141M01A=聯行額度明細表<br/>
	 * L160M01A=動用審核表
	 */
	public String getCaseType() {
		return this.caseType;
	}

	/**
	 * 設定文件種類
	 * <p/>
	 * L120M01A=案件簽報書<br/>
	 * L141M01A=聯行額度明細表<br/>
	 * L160M01A=動用審核表
	 **/
	public void setCaseType(String value) {
		this.caseType = value;
	}

	/** 取得文件狀態 **/
	public String getDocStatus() {
		return this.docStatus;
	}

	/** 設定文件狀態 **/
	public void setDocStatus(String value) {
		this.docStatus = value;
	}

	/**
	 * 取得刪除註記
	 * <p/>
	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 */
	public Date getDeletedTime() {
		return this.deletedTime;
	}

	/**
	 * 設定刪除註記
	 * <p/>
	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Date value) {
		this.deletedTime = value;
	}

	/**
	 * 取得文件URL
	 * <p/>
	 * 文件URL
	 */
	public String getDocURL() {
		return this.docURL;
	}

	/**
	 * 設定文件URL
	 * <p/>
	 * 文件URL
	 **/
	public void setDocURL(String value) {
		this.docURL = value;
	}

	/** 取得收件單位 **/
	public String getOwnBrId() {
		return this.ownBrId;
	}

	/** 設定收件單位 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得傳送單位 **/
	public String getSendBrid() {
		return this.sendBrid;
	}

	/** 設定傳送單位 **/
	public void setSendBrid(String value) {
		this.sendBrid = value;
	}

	/** 取得傳送時間 **/
	public Date getSendInfo() {
		return this.sendInfo;
	}

	/** 設定傳送時間 **/
	public void setSendInfo(Date value) {
		this.sendInfo = value;
	}

	/** 取得原始文件oid **/
	public String getSrcOid() {
		return this.srcOid;
	}

	/** 設定原始文件oid **/
	public void setSrcOid(String value) {
		this.srcOid = value;
	}

	/** 取得原始文件mainId **/
	public String getSrcMainId() {
		return this.srcMainId;
	}

	/** 設定原始文件mainId **/
	public void setSrcMainId(String value) {
		this.srcMainId = value;
	}

	/** 取得原始文件交易代碼 **/
	public String getSrcTxCode() {
		return this.srcTxCode;
	}

	/** 設定原始文件交易代碼 **/
	public void setSrcTxCode(String value) {
		this.srcTxCode = value;
	}

	/**
	 * get the ngFlag
	 * 
	 * @return the ngFlag
	 */
	public String getNgFlag() {
		return ngFlag;
	}

	/**
	 * set the ngFlag
	 * 
	 * @param ngFlag
	 *            the ngFlag to set
	 */
	public void setNgFlag(String ngFlag) {
		this.ngFlag = ngFlag;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.mega.eloan.common.model.IDocObject#getMainId()
	 */
	@Override
	public String getMainId() {
		// TODO Auto-generated method stub
		return null;
	}
}
