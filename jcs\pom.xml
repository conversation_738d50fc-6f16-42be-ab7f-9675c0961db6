<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>tw.com.jcs</groupId>
    <artifactId>jcs</artifactId>
    <version>2.0.0</version>
    <packaging>pom</packaging>
    <modules>
        <module>jcs-auth</module>
        <module>jcs-common</module>
        <module>jcs-flow</module>
    </modules>
    <properties>
        <spring.version>5.3.39</spring.version>
        <slf4j.version>1.7.25</slf4j.version>
        <log4j.version>2.17.2</log4j.version>
        <inet-cr.version>20.10.553</inet-cr.version>
        <thymeleaf.version>3.1.2.RELEASE</thymeleaf.version>
        <localRepositoryPath>src/localRepos</localRepositoryPath>
    </properties>
    <!-- <distributionManagement>
        <repository>
            <id>eloan-internal</id>
            <url>http://192.168.51.222:1135/archiva/repository/internal</url>
        </repository>
        <snapshotRepository>
            <id>eloan-snapshots</id>
            <url>http://192.168.51.222:1135/archiva/repository/snapshots</url>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>eloan-local</id>
            <url>file://${project.basedir}/../${localRepositoryPath}</url>
        </repository>
        <repository>
            <id>eloan-internal</id>
            <name>Mega Archiva Managed Internal Repository</name>
            <url>http://192.168.51.222:1135/archiva/repository/internal/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>eloan-snapshots</id>
            <url>http://192.168.51.222:1135/archiva/repository/snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>eloan-internal</id>
            <name>Mega Archiva Managed Internal Repository</name>
            <url>http://192.168.51.222:1135/archiva/repository/internal/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories> -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>tw.com.jcs</groupId>
                <artifactId>jcs-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- spring -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thymeleaf</groupId>
                <artifactId>thymeleaf</artifactId>
                <version>${thymeleaf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thymeleaf</groupId>
                <artifactId>thymeleaf-spring5</artifactId>
                <version>${thymeleaf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- jpa -->
            <dependency>
                <groupId>org.hibernate.javax.persistence</groupId>
                <artifactId>hibernate-jpa-2.1-api</artifactId>
                <version>1.0.2.Final</version>
            </dependency>
            <!-- ClearReports -->
            <dependency>
                <groupId>ClearReports</groupId>
                <artifactId>reporting</artifactId>
                <version>${inet-cr.version}</version>
            </dependency>
            <dependency>
                <groupId>ClearReports</groupId>
                <artifactId>inetcore</artifactId>
                <version>${inet-cr.version}</version>
            </dependency>
            <dependency>
                <groupId>ClearReports</groupId>
                <artifactId>JWebEngine</artifactId>
                <version>${inet-cr.version}</version>
            </dependency>
            <!-- pdf -->
            <dependency>
                <groupId>com.github.librepdf</groupId>
                <artifactId>openpdf</artifactId>
                <version>1.3.26</version>
            </dependency>
            <dependency>
                <groupId>com.twelvemonkeys.imageio</groupId>
                <artifactId>imageio-tiff</artifactId>
                <version>3.7.1</version>
            </dependency>
            <!-- logging -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-web</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <!-- apache commons -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.15.0</version>
            </dependency>
            <!-- util -->
            <dependency>
                <groupId>ognl</groupId>
                <artifactId>ognl</artifactId>
                <version>3.2.21</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.15.0</version>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib-nodep</artifactId>
                <version>3.3.0</version>
            </dependency>
            <dependency>
                <groupId>org.owasp.esapi</groupId>
                <artifactId>esapi</artifactId>
                <version>2.5.4.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>xerces</groupId>
                        <artifactId>xercesImpl</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-io</groupId>
                        <artifactId>commons-io</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <encoding>utf-8</encoding>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <encoding>utf-8</encoding>
                </configuration>
            </plugin>
        </plugins>
        <extensions>
            <!-- begin - needed for deploying to repository using webdav -->
            <extension>
                <groupId>org.apache.maven.wagon</groupId>
                <artifactId>wagon-webdav</artifactId>
                <version>1.0-beta-2</version>
            </extension>
            <!-- end - needed for deploying to repository using webdav -->
        </extensions>
    </build>
</project>