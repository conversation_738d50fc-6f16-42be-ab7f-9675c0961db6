var initDfd = initDfd || $.Deferred();
pageJsInit(function() {
	$(function() {
		var chgFlag = "";

		/**
		 *驗證是否為數字
		 * @param {String} 要驗證的字
		 */
		function isNumber(val) {
			var reg = /\d/;
			return reg.test(val);
		}

		if ("03" == responseJSON.page) {
			chgFlag = "1";
			thickboxOptions.readOnly = true;
		} else {
			chgFlag = "2";
		}

		//登錄聯行攤貸比例
		var BranchAciton = {
			$form: $("#L210S01AForm"),
			gridId: "#gridviewitemChildren3",
			//呈現聯行攤貸比例組字
			setWord: function(word) {
				$("#showWord").html(DOMPurify.sanitize(word));
			},
			/**
			 * 查詢
			 * @param {Object} cellvalue 欄位顯示值
			 * @param {Object} type  欄位選項
			 * @param {Object} data  欄位資料
			 */
			query: function(cellvalue, type, data) {
				if (!data) {
					data = {
						oid: ""
					};
				}
				var $form = $("#L210S01AForm");
				util.init($form);
				$form.reset();

				$.ajax({
					handler: inits.fhandle,
					action: "queryL210s01a",
					data: {//把資料轉成json
						mainId: $("#mainId").val(),
						oid: data.oid,
						chgFlag: chgFlag
					},
					success: function(obj) {
						$("#totalAmt").val(obj.totalAmt);
						$("#shareBrId").setItems({
							item: obj.item,
							format: "{value} {key}",
							space: false
						});
						$form.injectData(obj.formData);
						if (data.oid != "") {
							$("#shareBrId").attr("disabled", true);
						} else {
							$("#shareBrId").attr("disabled", false);
						}
						$("#shareAmt,#shareRate1,#shareRate2").attr("readonly", true);
						$("#shareMoneyCount1,#shareMoneyCount2").hide().parents(".fg-buttonset").hide();
						if (obj.role != "0") {
							$("#shareRate2").val(obj.role);
							$("#shareRate2").attr("readonly", true);
							$("[name=shareFlag]").attr("disabled", true);
							$("[name=shareFlag][value=" + obj.shareFlag + "]").attr("checked", true);
						} else {
							$("[name=shareFlag]").removeAttr("disabled");
						}//close if
						BranchAciton.controlShow();
						BranchAciton.openBox();
					}//close success
				}); //close ajax  
			},
			openBox: function() {
				$("#newItemChildrenBox3").thickbox({
					//title.15=登錄聯行攤貸比例
					title: i18n.lms1405s02["title.15"],
					width: 600,
					height: 300,
					modal: true,
					readOnly: _openerLockDoc == "1" || inits.toreadOnly || thickboxOptions.readOnly,
					i18n: i18n.def,
					open: function() {
						if (_openerLockDoc == "1" || inits.toreadOnly || thickboxOptions.readOnly) {
							//鎖定box
							$("#L210S01AForm").readOnlyChilds(_openerLockDoc == "1" || inits.toreadOnly || thickboxOptions.readOnly);
						}
					},
					buttons: {
						"saveData": function() {
							var $form = $("#L210S01AForm");
							if ($("[name=shareFlag]:checked").val() == "1") {
								$form.find("#shareRate1,#shareRate2").removeClass("required");
							} else {
								BranchAciton.countByAMT();
								$form.find("#shareRate1,#shareRate2").addClass("required");
							}
							if (!$form.valid()) {
								return false;
							}
							var shareRate1 = parseInt($("#shareRate1").val(), 10), shareRate2 = parseInt($("#shareRate2").val(), 10);
							if (shareRate1 > shareRate2) {
								//L140M01e.lmterror=分子總和大於分母無法儲存
								return CommonAPI.showMessage(i18n.lms1405s02["L140M01e.lmterror"]);
							}
							var shareBrId = $("#shareBrId").val();
							$.ajax({
								handler: inits.fhandle,
								data: {
									formAction: "queryShareBrIdType",//查詢目前分行為海外還是國內，若為海外第四碼為5，若為國內判斷
									shareBrId: shareBrId,
									mainId: $("#mainId").val(),
									chgFlag: chgFlag
								},
								success: function(obj) {
									//先檢查該間分行是否存在，若存在只做save的動作
									if (obj.have) {
										//若該分行已存在只執行儲存
										BranchAciton.save("");
									} else {
										//新增案件開起給號視窗
										if (obj.type == "5") {
											BranchAciton.getCntrNo("5", shareBrId);
										} else {
											BranchAciton.save();
										}
									}
								}//close success
							});
						},
						"close": function() {
							$.thickbox.close();
						}
					}
				});
			},
			/**儲存
			 *
			 * @param {Object} type 1.DBU,4.OBU,5.海外
			 */
			save: function(type) {
				$.ajax({
					handler: inits.fhandle,
					data: {
						formAction: "saveL210s01a",
						tabFormMainId: $("#tabFormMainId").val(),
						L210S01AForm: JSON.stringify(BranchAciton.$form.serializeData()),
						type: type
					},
					success: function(obj) {
						BranchAciton.setWord(obj.desc);
						$.thickbox.close();
						$(BranchAciton.gridId).trigger("reloadGrid");

					}
				});
			},
			/**
			 * 給號畫面
			 * @param {String} type 5.海外
			 * @param {String} brId 分行代號
			 */
			getCntrNo: function(type, brId) {
				var $cntrNoForm = $("#cntrNoBoxforItem3Form");
				$("#cntrNoBoxforItem3").thickbox({
					//btn.number=給號
					title: i18n.lms1405s02["btn.number"],
					width: 640,
					height: 320,
					modal: true,
					align: "center",
					readOnly: _openerLockDoc == "1" || inits.toreadOnly,
					valign: "bottom",
					i18n: i18n.def,
					open: function() {
						//初始化
						$cntrNoForm.reset();
						//帶入分行預設值
						$cntrNoForm.find("#branchNoItem3").val(brId);
						$cntrNoForm.find("#cntrNoType").val(type);
						$cntrNoForm.find(".ForOriginal,.ForInSide").hide();
					},
					buttons: {
						"sure": function() {
							if (!$cntrNoForm.valid()) {
								return false;
							}
							var numberType = $cntrNoForm.find("input[name=numberType]:checked").val();
							var originalCntrNo = $cntrNoForm.find("#originalCntrNo").val();
							brId = $cntrNoForm.find("#branchNoItem3").val();
							//給新號
							if (numberType == "1") {
								if (type != "5") {
									//國內的可以自己選分行和DBU or OBU
									type = $cntrNoForm.find("input[name=typeCd]:checked").val();
								}
							} else {
								//舊號
								var cntrno = BranchAciton.queryOriginalCntrNo(originalCntrNo);
								if ($.isEmptyObject(cntrno) || !cntrno.cntrNo) {
									return false;
								}
							}
							$.ajax({
								handler: inits.fhandle,
								action: "saveL210s01a",
								data: {
									L210S01AForm: JSON.stringify(BranchAciton.$form.serializeData()),
									chgFlag: chgFlag,
									mainId: $("#mainId").val(),
									numberType: numberType,
									originalCntrNo: originalCntrNo,
									type: type,
									classCD: "0",
									selectBrNo: brId
								},
								success: function(obj) {
									BranchAciton.setWord(obj.desc);
									$.thickbox.close();
									$.thickbox.close();
									$(BranchAciton.gridId).trigger("reloadGrid");
								}
							});
						},
						"cancel": function() {
							$.thickbox.close();
						}
					}
				});
			},
			/**
			 * 刪除
			 */
			remove: function() {
				var gridData = $(BranchAciton.gridId);
				var gridID = gridData.getGridParam('selarrrow');
				if (gridID == "") {
					//TMMDeleteError=請先選擇需修改(刪除)之資料列
					return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
				}
				//confirmDelete=是否確定刪除?
				CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b) {
					if (b) {
						var gridIDList = [];
						for (var i = 0; i < gridID.length; i++) {
							gridIDList[i] = gridData.getRowData(gridID[i]).oid;
						}
						$.ajax({
							handler: inits.fhandle,
							action: "deleteL210s01a",
							data: {
								oids: gridIDList
							},
							success: function(obj) {
								BranchAciton.setWord(obj.desc);
								gridData.trigger("reloadGrid");
							}
						});
					}
				});
			},
			/**
			 * 控制計算方式顯示
			 */
			controlShow: function() {
				var value = $("[name=shareFlag]:checked").val();
				$("#shareAmt,#shareRate1,#shareRate2").attr("readonly", true);
				$("#shareMoneyCount1,#shareMoneyCount2").hide().parents(".fg-buttonset").hide();
				//顯示隱藏
				switch (value) {
					case "1":
						// 1.依攤貸金額
						$("#shareMoneyCount2").show().parents(".fg-buttonset").show();
						$("#shareAmt").removeAttr("readonly");
						$("#shareRate1,#shareRate2").val("");
						break;
					case "2":
						//2.依攤貸比例          
						$("#shareMoneyCount1").show().parents(".fg-buttonset").show();
						if (!$("[name=shareFlag]:checked").attr("disabled")) {
							$("#shareAmt").val("");
						}
						$("#shareRate1").removeAttr("readonly");
						var countGrid = $(BranchAciton.gridId).jqGrid('getGridParam', 'records');
						if (countGrid == 0) {
							$("#shareRate2").removeAttr("readonly");
						}
						break;
					default:
						break;
				}
			},
			/**
			 *以比例計算金額
			 */
			countByAMT: function() {
				var totel = $("#totalAmt").val().replace(/,/g, ""), value1 = $("#shareRate1").val(), value2 = $("#shareRate2").val(), shareRate1 = parseInt(value1, 10), shareRate2 = parseInt(value2, 10), end = (totel * shareRate1) / shareRate2;
				if (!isNumber(end)) {
					end = "";
				} else {
					end = util.addComma(parseInt(end, 10));
				}
				$("#shareAmt").val(end);
			},
			/**
			 *	查詢原案額度序號
			 * @param {String } originalText 原案額度序號
			 * @param {String } justSave 1.為查詢舊額度序號2.儲存預約額度序號 3,聯行攤貸比例
			 * return {Object}cntrNo 額度序號",ownBrName 額度序號前三碼分行名稱
			 */
			queryOriginalCntrNo: function(originalText) {
				//驗證舊有額度序號規則
				if (!originalText.match(/\w{12}/)) {
					//L140M01a.message68=額度序號長度應為12碼，編碼原則:XXX(分行代號)+X(1:DBU,4:OBU,5:海外)+YYY(年度)+99999(流水號)
					return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.message68"]);
				}
				var queryObj = {};
				$.ajax({
					handler: inits.fhandle,
					async: false,
					action: "checkCntrno",
					data: {
						cntrNo: originalText,
						mainOid: $("#mainOid").val()
					},
					success: function(obj) {
						queryObj.cntrNo = originalText.toUpperCase();
						queryObj.ownBrName = obj.ownBrName;
					}
				});
				return queryObj;
			},
			/**
			 *變更分母
			 */
			changesShareRate: function() {
				$("#newSharteNew").val("");
				$.ajax({
					handler: inits.fhandle,
					action: "queryChangesShareRate",
					data: {

						mainId: $("#mainId").val()
					},
					success: function(obj) {
						$("#newSharteNewBox").thickbox({
							title: i18n.lms1405s02["btn.changesShareRate2"],//變更分母
							width: 200,
							height: 100,
							readOnly: _openerLockDoc == "1" || inits.toreadOnly,
							i18n: i18n.def,
							modal: true,
							align: "center",
							valign: "bottom",
							open: function() {
							},
							buttons: {
								"sure": function() {
									if ($("#newSharteNew").val() == 0) {
										//L140M01a.message81=分母不可為0
										return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.message81"]);
									}
									if (!$("#newSharteNewForm").valid()) {
										return false;
									}
									$.ajax({
										handler: inits.fhandle,
										action: "saveChangesShareRate",
										data: {
											mainId: $("#mainId").val(),
											shareRate: $("#newSharteNew").val()
										},
										success: function(obj) {
											BranchAciton.setWord(obj.desc);
											$(BranchAciton.gridId).trigger("reloadGrid");
											$.thickbox.close();
										}
									});
								},
								"cancel": function() {
									$.thickbox.close();
								}
							}
						});
					}
				});
			}
		};

		initDfd.done(function(json, auth) {

			var gridDef1 = $.Deferred();
			var gridDef2 = $.Deferred();
			gridDef1.done(gridviewitemChildren3);
			gridDef2.done(gridviewBranch);
			//    var chgFlag = "";
			//    var gridDef1 = $.Deferred();
			//    var gridDef2 = $.Deferred();
			//    gridDef1.done(gridviewitemChildren3);
			//    gridDef2.done(gridviewBranch);
			//    
			//    if ("03" == responseJSON.page) {
			//        chgFlag = "1";
			//        thickboxOptions.readOnly = true;
			//    } else {
			//        chgFlag = "2";
			//    }
			switch (json.caseType) {
				case "1":
					$("#fieldset2").show();
					gridDef1.resolve();
					break
				case "2":
					$("#fieldset1").show()
					gridDef2.resolve();
					break
				case "3":
					$("#fieldset1,#fieldset2").show()
					gridDef1.resolve();
					gridDef2.resolve();
					break

			}



			/**取得聯貸案已編碼國外銀行的清單
			 *
			 * @param {String } value 分行種類 12-國外分行 99 -其他(由國外部徵信系統金融機構資料維護)
			 */
			function getBranch(value) {
				$.ajax({
					handler: "lms1605m01formhandler",
					data: {
						formAction: "queryforeignBranch",
						type: value
					},
					success: function(obj) {
						if (!$.isEmptyObject(obj.foreignBranch)) {
							//新增空白選項
							$("#selBank" + value).setItems({
								item: obj.foreignBranch,
								format: "{value} - {key}",
								space: false
							});
						}
					}
				});
			}


			//變更分母
			$("#changesShareRate2").click(function() {
				BranchAciton.changesShareRate();
			});
			//銀行種類切換
			$('#selBank').change(function() {
				var value = DOMPurify.sanitize(String($(this).val()));
				if ($.trim(value) != "") {
					$("#selBank" + value).show().siblings("[id^=selBank]").hide();
				} else {
					$("#tdSelBank").find("[id^=selBank]").hide();
				}
				//當沒有下拉選單時才去抓		
				if (value == "12" && $("#selBank12 option").length == 0) {
					getBranch(value);
				} else if (value == "99" && $("#selBank99 option").length == 0) {
					getBranch(value);
				}
			});

			// 新號，舊號的隱藏條件
			$("input[name=numberType]").change(function() {
				var $cntrNoForm = $("#cntrNoBoxforItem3Form");
				var type = $cntrNoForm.find("#cntrNoType").val(type);
				$cntrNoForm.find(".ForOriginal,.ForInSide").hide();
				//海外不行選輸入分行 和 是否遠匯 
				var value = $(this).val();
				switch (value) {
					case "1":
						if (type != "5") {
							$cntrNoForm.find(".ForInSide").show();
						}
						$("#showBrNoTr").show();
						break;
					case "2":
						$cntrNoForm.find(".ForOriginal").show();
						break;
				}
			});


			//動態切換載入銀行
			$('#selBank01,#selBank02').change(function() {
				var value = $(this).val();
				var id = $(this).attr("id").slice(7);
				id = DOMPurify.sanitize(String(id));
				$("#selBankOther" + id).show();
				if ($.trim(value) == "") {
					$("#selBankOther" + id).hide();
					return false;
				}

				$.ajax({
					handler: "lms1605m01formhandler",
					data: {
						formAction: "queryBranch",
						mainBranch: value
					},
					success: function(obj) {
						if (!$.isEmptyObject(obj.brankList)) {
							$("#selBankOther" + id).setItems({
								item: obj.brankList,
								format: "{value} {key}",
								space: false
							});
						} else {
							$("#selBankOther" + id).hide().html("");
						}
					}
				});
			});


			//====================button event ================================

			//新增聯貸案參貸比率一覽表
			$("#openBranchBox").click(function() {
				branchBox();
			});

			//刪除聯貸案參貸比率一覽表
			$("#deleteBranch").click(function() {
				var select = $("#gridviewBranch").getGridParam('selarrrow');
				if (select == "") {
					// TMMDeleteError=請先選擇需修改(刪除)之資料列
					return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
				}

				// confirmDelete=是否確定刪除?
				CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b) {
					if (b) {
						var data = [];
						for (var i in select) {
							data.push($("#gridviewBranch").getRowData(select[i]).oid);
						}

						$.ajax({
							handler: inits.fhandle,
							data: {
								formAction: "deleteL210s01b",
								oids: data
							},
							success: function(obj) {
								setTotMoney(obj.showTotalMoney);
								$("#gridviewBranch").trigger("reloadGrid");
							}
						});
					} else {
						return;
					}
				});
			});

			//新增攤待比率
			$("#newItemChildren3Bt").click(function() {
				BranchAciton.query();
			});


			//刪除攤待比率
			$("#removeGridviewitemChildren3").click(function() {
				BranchAciton.remove();
			});

			//變更攤貸行計算方式
			$("[name=shareFlag]").click(function() {
				BranchAciton.controlShow();
			});

			//====================button event END================================
			//====================thickbox Code======================================
			//登錄銀行
			$("#includeBranch").click(function() {
				//初始化選項
				$("#includeBranchBox [id^=selBank]").hide();
				$("#selBank").show().val("");
				$("#includeBranchForm").reset();
				$("#includeBranchBox").thickbox({
					//L210M01A.s03title03=參貸行庫/分行
					title: i18n.lms2105m01['L210M01A.s03title03'],
					width: 650,
					height: 200,
					modal: true,
					align: "center",
					i18n: i18n.def,
					valign: "bottom",
					buttons: {
						"sure": function() {
							//初始化畫面欄位
							$("#slBankType,#slBranchCN,#slBranch,#slBank,#slBankCN").val("");
							$("#showBranch,#showBranchCn").html("");

							//參貸行總類
							var value = DOMPurify.sanitize(String($("#selBank").val()));
							//參貸銀行
							var number = $("#selBank" + value).val();

							var name = $.trim($("#selBank" + value + " :selected").text().slice(5));

							if ($.trim(number) == "" || $.trim(value) == "") {
								//grid.selrow=請先選擇一筆資料。
								return CommonAPI.showMessage(i18n.def["grid.selrow"]);
							}

							$("#slBank").val(number);
							$("#slBankCN").val(name);
							$("#showBranch").html(DOMPurify.sanitize(String(number)) + " " + DOMPurify.sanitize(String(name)));
							$("#slBankType").val(value);
							switch (value) {
								case "01":
								case "02":
									var numberCn = $("#selBankOther" + value).val();
									if ($.trim(numberCn) == "" && !$("#selBankOther" + value).is(":hidden")) {
										//grid.selrow=請先選擇一筆資料。
										return CommonAPI.showMessage(i18n.def["grid.selrow"]);
									}
									var numberCnName = $("#selBankOther" + value + " :selected").text();
									$("#slBranch").val(numberCn);
									if ("017" == number) {
										$("#slBranchCN").val(numberCnName.slice(4));
										$("#showBranchCn").html(DOMPurify.sanitize(numberCnName));
									} else {
										if ($("#selBankOther" + value).is(":hidden")) {
											$("#showBranchCn").html("");
										} else {
											$("#showBranchCn").html(DOMPurify.sanitize(numberCn) + " " + DOMPurify.sanitize(numberCnName.slice(8)));
											$("#slBranchCN").val(numberCnName.slice(8));
										}
									}
									break;
								case "03":
								case "04":
								case "05":
								case "06":
								case "07":
								case "08":
								case "09":
								case "10":
								case "11":
									break;
								case "12":
								case "99":
									$("#slBankCN").val($("#selBank" + value + " :selected").text().split(" - ")[1]);
									$("#showBranch").html(DOMPurify.sanitize($("#selBank" + value + " :selected").text()));
									break;
								default:
									//grid.selrow=請先選擇一筆資料。
									return CommonAPI.showMessage(i18n.def["grid.selrow"]);
									break;
							}
							$.thickbox.close();
						},
						"cancel": function() {
							$.thickbox.close();
						}
					}
				});
			});
			/**
			 * 設定同業聯貸總金額
			 * @param {Object} value
			 */
			function setTotMoney(value) {
				$("#showTotalMoney").html(DOMPurify.sanitize(value));
			}
			function openBranchBox() {
				$("#branchBox").thickbox({

					//L210M01A.s03title08=聯貸案參貸比率一覽表
					title: i18n.lms2105m01['L210M01A.s03title08'],
					width: 620,
					height: 280,
					modal: true,
					i18n: i18n.def,
					buttons: {
						"saveData": function() {
							var $mainForm = $("#L210S01BForm");
							//當在變動前頁面不得做儲存動作
							if (!$mainForm.valid()) {
								return;
							}
							var amt = $.trim($("#slAmt").val());

							//檢驗規則
							//                    if (amt == 0) {
							//                        //L160M01A.error6=參貸金額不得為
							//                        return CommonAPI.showErrorMessage(i18n.lms2105m01["L160M01A.error6"] + "0");
							//                    }
							if ($.trim($("#slBankType").val()) == "") {

								//L210M01A.s03title08=參貸行庫/分行 L210M01A.s03title15=請登錄
								return CommonAPI.showErrorMessage(i18n.lms2105m01["L210M01A.s03title15"] + i18n.lms2105m01["L210M01A.s03title08"]);
							}
							$.ajax({
								handler: "lms2105m01formhandler",
								data: {
									formAction: "saveL210s01b",
									oid: $mainForm.attr("formOid"),
									slCurr: $("#quotaCurr").val(),
									totalAmt: $("#slAmt").val().replace(/,/g, ""),
									L210S01BForm: JSON.stringify($mainForm.serializeData)
								},
								success: function(obj) {
									setTotMoney(obj.showTotalMoney);
									$.thickbox.close();
									$("#gridviewBranch").trigger('reloadGrid');
								}
							});
						},
						"close": function() {
							$.thickbox.close();
						}
					}
				});
			}


			function branchBox(cellvalue, options, rowData) {
				var $mainForm = $("#L210S01BForm");
				//初始化表單物件
				$mainForm.reset().attr("formOid", "");
				$mainForm.find("#showBranch,#showBranchCn").html("");
				if (rowData) {
					$.ajax({
						handler: "lms2105m01formhandler",
						data: {
							formAction: "queryL210s01b",
							oid: rowData.oid
						},
						success: function(obj) {
							openBranchBox()
							$mainForm.setData(obj);
							$("#showBranch").html(DOMPurify.sanitize(obj.slBank) + " " + DOMPurify.sanitize(obj.slBankCN));
							$("#showBranchCn").html(DOMPurify.sanitize(obj.slBranch) + " " + DOMPurify.sanitize(obj.slBranchCN));
							if ("1" == chgFlag) {
								$mainForm.lockDoc();
							}
						}
					});
				} else {
					openBranchBox()
				}
			}
			//====================thickbox Code==END====================================
			//====================Grid Code ============================================

			/** 聯行攤貸比例grid  */
			function gridviewitemChildren3() {
				$("#gridviewitemChildren3").iGrid({//
					handler: "lms2105gridhandler",
					rowNum: 10,
					rownumbers: true,
					multiselect: true,
					hideMultiselect: false,
					sortname: 'createTime',
					sortorder: 'asc',
					postData: {
						formAction: "queryL210s01a",
						mainId: json.mainId || "",
						chgFlag: chgFlag
					},
					rowNum: 10,
					autowidth: true,
					colModel: [{
						name: 'shareRate2',
						hidden: true
					}, {
						colHeader: i18n.lms2105m01["L210M01A.s03title09"],//"攤貸分行",
						name: 'shareBrId',
						align: "left",
						width: 110,
						sortable: true,
						formatter: 'click',
						onclick: BranchAciton.query
					}, {
						colHeader: i18n.lms2105m01["L210M01A.s03title10"],//"攤貸金額",
						name: 'shareAmt',
						width: 160,
						sortable: true,
						align: "right",
						formatter: 'currency',
						formatoptions: {
							thousandsSeparator: ",",
							removeTrailingZero: true,
							decimalPlaces: 2//小數點到第幾位
						}
					}, {
						colHeader: i18n.lms2105m01["L210M01A.s03title11"],//"攤貸比例",
						width: 140,
						name: 'showRate',
						align: "right",
						sortable: true
					}, {
						colHeader: i18n.lms2105m01["L210M01A.s03title12"],//"額度序號",
						width: 140,
						name: 'shareNo',
						sortable: true
					}, {
						name: 'oid',
						hidden: true
					}]
				});
			}//close gridviewitemChildren3 fn(x)
			/** 同業聯貸比率grid  */
			function gridviewBranch() {
				$("#gridviewBranch").iGrid({
					handler: "lms2105gridhandler",
					height: "190px",
					width: "100%",
					multiselect: true,
					postData: {
						formAction: "queryL210s01b",
						mainId: json.mainId || "",
						chgFlag: chgFlag
					},
					colModel: [{
						colHeader: i18n.lms2105m01['L210M01A.s03title03'],// "參貸行庫/分行",
						name: 'slBankCN',
						width: '300px',
						sortable: true,
						align: "left",
						formatter: 'click',
						onclick: branchBox
					}, {
						colHeader: i18n.lms2105m01['L210M01A.s03title04'],//"共同主辦行",
						name: 'slMaster',
						width: '100px',
						sortable: true,
						align: "center"
					}, {
						colHeader: i18n.lms2105m01['L210M01A.s03title05'],// "同業帳號",
						name: 'slAccNo',
						width: '200px',
						sortable: true,
						align: "right"
					}, {
						colHeader: i18n.lms2105m01['L210M01A.s03title06'],// "參貸金額",
						name: 'slAmt',
						width: '200px',
						sortable: true,
						align: "right",
						formatter: 'currency',
						formatoptions: {
							thousandsSeparator: ",",
							removeTrailingZero: true,
							decimalPlaces: 2//小數點到第幾位
						}
					}, {
						name: 'oid',
						hidden: true
					}],
					ondblClickRow: function(rowid) {
						var data = $("#gridviewBranch").getRowData(rowid);
					}
				});
			}
			//====================Grid Code End======================================			
		});

	});
});
