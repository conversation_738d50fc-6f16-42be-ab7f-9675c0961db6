var _handler = "cls3501m01formhandler";
$(function(){	
	var hidden05O = true;
    if(viewstatus == "05O"){
        hidden05O = false;
    }
	var grid = $("#gridview").iGrid({
        handler: 'cls3501gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        postData: {
            formAction: "queryView",
            docStatus : viewstatus
        },
        rowNum: 15,
        sortname: "createTime|applyLoanDay|custId",
        sortorder: "desc|asc|desc",//desc
        multiselect: true,
        colModel: [{
            colHeader: i18n.cls3501v01["custId"],
            align: "left", width: 90, sortable: true, name: 'custId',
            formatter: 'click', onclick: openDoc
        }, {
            colHeader: i18n.cls3501v01["dupNo"],
            align: "left", width: 10, sortable: true, name: 'dupNo'
        }, {
            colHeader: i18n.cls3501v01["custName"],
            align: "left", width: 120, sortable: true, name: 'custName'
        }, {
            colHeader: i18n.cls3501v01["applyLoanDay"],
            align: "center", width: 60, sortable: true, name: 'applyLoanDay'
        }, {
            colHeader: i18n.cls3501v01['updater'],//經辦
            name: 'updater',
            width: 80,
            align: "center",
            hidden: !hidden05O
        }, {
            colHeader: i18n.cls3501v01["createTime"], //建立日期
            align: "center",
            width: 80, // 設定寬
            name: 'createTime',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d H:i:s',
                newformat: 'Y-m-d H:i'
            },
            hidden: !hidden05O
        }, {
            colHeader: i18n.cls3501v01["approveTime"], //核准日期
            align: "center",
            width: 60, // 設定寬
            name: 'approveTime',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d H:i:s',
                newformat: 'Y-m-d'
            },
            hidden: hidden05O
        }, {
            colHeader: i18n.cls3501v01["grntPaper"], //保證案號
            align: "left",
            width: 80, // 設定寬
            name: 'grntPaper',
            hidden: hidden05O
        }, {
            colHeader: i18n.cls3501v01["dataStatus"], //回覆結果
            align: "left",
            width: 60, // 設定寬
            name: 'dataStatus',
            hidden: hidden05O
        }, {
            colHeader: i18n.cls3501v01["description"], //處理說明
            align: "left",
            width: 100, // 設定寬
            name: 'description',
            hidden: hidden05O
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docStatus',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
	
	function openDoc(cellvalue, options, rowObject) {
		$.form.submit({
			url : '../cls/cls3501m01/01',
			data : {
				'oid' : rowObject.oid,
				'mainOid' : rowObject.oid,
				'mainId' : rowObject.mainId,
				'mainDocStatus' : viewstatus
			},
			target : rowObject.oid
		});					
	};
	
	
    $("#buttonPanel").find("#btnView").click(function(){
    	var id = $("#gridview").getGridParam('selarrrow');
        if (!id || id.length == 0) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
        	// includeId.selData=請選擇一筆資料!!
        	return CommonAPI.showMessage(i18n.def["includeId.selData"]);
        } else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").click(function(){
    	openFilterBox();
    }).end().find("#btnAdd").click(function(){
    	chose_custId().done(function(resultFrom_chose_custId){
            $.ajax({
                handler: _handler,
                action: 'checkCust',
                data: {
                    custId: resultFrom_chose_custId.custId,
                    dupNo: resultFrom_chose_custId.dupNo,
                    custName: resultFrom_chose_custId.custName
                },
			}).done(function(obj){
				if(obj.isEixst){
                    return CommonAPI.showErrorMessage(obj.isEixst);
                } else {
                    $.ajax({
                        handler: _handler,
                        action : 'newC124M01A',
                        data : {
                            custId: resultFrom_chose_custId.custId,
                            dupNo: resultFrom_chose_custId.dupNo,
                            custName: resultFrom_chose_custId.custName
                        },
					}).done(function(obj){
						$.form.submit({
	                        url: '../cls/cls3501m01/01',
	                        data: {
	                            oid: obj.oid,
	                            mainOid: obj.oid,
	                            mainDocStatus: viewstatus,
	                            txCode: txCode,
	                            custId: resultFrom_chose_custId.custId,
	                            dupNo: resultFrom_chose_custId.dupNo,
	                            custName: resultFrom_chose_custId.custName,
	                            mainId: obj.mainId
	                        },
	                        target: obj.oid
	                    });
	                    $("#gridview").trigger("reloadGrid");
					})
                }
                $.thickbox.close();
			})
	    });
    }).end().find("#btnDelete").click(function(){ //編製中-刪除
    	var rows = $("#gridview").getGridParam('selarrrow');
		var data = [];
		if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
		    // action_005=請先選取一筆以上之資料列
            return CommonAPI.showMessage(i18n.def["action_005"]);
        }

		if(rows){
			CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
				if(b){
                    for (var i in rows) {
                        data.push($("#gridview").getRowData(rows[i]).oid);
                    }
					$.ajax({
						handler : _handler,
						type : "POST",
						dataType : "json",
						data :{
							'formAction' : 'deleteC124M01A',
							oids: data
						},
					}).done(function(obj){
						$("#gridview").trigger("reloadGrid");
					})
				}
			});
		}
    }).end().find("#btnFCheck").click(function(){ // 整批核准 整批覆核 btnAllSend
        var rows = $("#gridview").getGridParam('selarrrow');
        var data = [];
        if (rows == "") { // action_005=請先選取一筆以上之資料列
            return CommonAPI.showMessage(i18n.def["action_005"]);
        }

        if(rows){
            for (var i in rows) {
                data.push($("#gridview").getRowData(rows[i]).oid);
            }

            var _id = "_div_btnFCheck";
            var _form = _id+"_form";
            if ($("#"+_id).length == 0){
                var dyna = [];
                dyna.push("<div id='"+_id+"' style='display:none;' >");
                dyna.push("<form id='"+_form+"'>");

                var submenu = {'1':"核准", '2':"退回經辦修改"};
                build_submenu(dyna, 'decision_btnFCheck', submenu);

                dyna.push("</form>");
                dyna.push("</div>");

                 $('body').append(dyna.join(""));
            }
            //clear data
            $("#"+_form).reset();

            $("#"+_id).thickbox({ // 使用選取的內容進行彈窗
            title: "覆核", width: 200, height: 150, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decision_btnFCheck']:checked").val();

                    if (!val) {
                        return CommonAPI.showMessage(i18n.cls3501m01['checkSelect']);
                    }

                    if(val=="1" || val=="2"){
                        var sendData = {};
                        if(val=="1"){   // 核定作業
                            sendData = {
                                flowAction: true,
                                checkDate: CommonAPI.getToday()
                            }
                        } else if(val=="2"){    // 退回到編製中01O
                            sendData = {flowAction: false};
                        }
                        var errorMsg = false;
                        $.each(data, function(idx, oid){
                            if(errorMsg){
                                return false;
                            }
                            $.ajax({
                                type: "POST",
                                async: false,
                                handler: _handler,
                                data: $.extend({
                                    formAction: "flowAction",
                                    mainOid: oid,
                                    mainDocStatus: viewstatus,
                                    batch: true
                                }, (sendData || {})),
							}).done(function(json){
								 if(json.msg){
                                    errorMsg = true;
                                    $.thickbox.close();
                                    return CommonAPI.showErrorMessage(json.msg);
	                              }
	                              if(idx==data.length-1){
                                    //grid.trigger("reloadGrid");
                                    $.thickbox.close();
	                              }
							})
                        });
                        grid.trigger("reloadGrid");
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
        }
    });

    // 篩選
    function openFilterBox(){
        var $filterForm = $("#filterForm");
        $filterForm.reset();            // 初始化

        $("#filterBox").thickbox({
            // filter=請輸入欲查詢項目：
            title: i18n.cls3501v01["filter"],
            width: 450,
            height: 210,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#filterForm").valid()) {
                        return;
                    }
//                    grid();

                    grid.jqGrid("setGridParam", {
                        postData: $.extend({ docStatus: viewstatus}, $filterForm.serializeData() ),
                        search: true
                    }).trigger("reloadGrid");

                    $.thickbox.close();
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
    }

    function chose_custId(){
        var my_dfd = $.Deferred();
        AddCustAction.open({
                handler: _handler,
                action : 'echo_custId',
                data : {
                },
                callback : function(json){
                    // 關掉 AddCustAction 的
                    $.thickbox.close();
                    my_dfd.resolve( json );
                }
            });
        return my_dfd.promise();
	}

	function build_submenu(dyna, rdoName, submenu){
    	$.each(submenu, function(k, v) {
    		dyna.push("   <p ><label id='_itemMenu_"+rdoName+"_"+k+"'><input type='radio' name='"+rdoName+"' value='"+k+"' class='required' />"+v+"</label></p>");
        });
    }
});
