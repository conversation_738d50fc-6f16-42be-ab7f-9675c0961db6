package com.mega.eloan.lms.fms.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;

import tw.com.jcs.auth.AuthType;

@Controller
@RequestMapping(path = "/fms/lms7005v00")
public class LMS7005V00Page extends AbstractEloanInnerView {

	public LMS7005V00Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(FlowDocStatusEnum.DOC_EDITING);
		// if (this.getAuth(AuthType.Accept)) {
		// // 主管權限時要顯示的按鈕...
		// addToButtonPanel(model,new
		// CreditButtonEnum[]{}));
		// } else {
		// // 否則需要顯示的按鈕
		// // 加上Button
		// addToButtonPanel(model,
		// CreditButtonEnum.Add, CreditButtonEnum.Modify,
		// CreditButtonEnum.Delete));
		// }

		// 加上Button
		List<EloanPageFragment> btns = new ArrayList<>();
		// 主管跟經辦都會出現的按鈕

		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
			//btns.add(CreditButtonEnum.View);
		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			btns.add(LmsButtonEnum.Add);
			btns.add(LmsButtonEnum.Modify);
			btns.add(LmsButtonEnum.Delete);
		}
		addToButtonPanel(model, btns);
		renderJsI18N(LMS7005V00Page.class);
	}// ;

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS7005V00Page.js" };
	}
}
