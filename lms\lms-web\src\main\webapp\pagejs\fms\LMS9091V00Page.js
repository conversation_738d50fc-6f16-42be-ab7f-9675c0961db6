$(document).ready(function(){
	$("#btn_calc").click(function(){
		Cal_result();
	});
	$("#btn_clear").click(function(){
		Clear();
	});
});

function setFocus(){
	$("form#CalForm").find("#Money").focus();
}

function Clear(){
	$("form#CalForm").reset();
}

function Cal_result(){
	var $frm = $("form#CalForm");
	if (! $frm.valid()) {
        return;
    }
	
    if ($frm.find("#Money").val().length == 0) {
        alert("請輸入貸款總金額！");
        return false;
    }
    else {
        var money = $frm.find("#Money").val();
        inputStr = money;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    alert("貸款總金額資料須為正整數的數值資料！");
                    return false;
                }
            }
        }
    }
    
    if ($frm.find("#Rate").val().length == 0) {
        alert("請輸入產品平均利率！");
        return false;
    }
    else {
        var rate = $frm.find("#Rate").val();
        inputStr = rate;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if ((oneChar != "0") && (oneChar != ".")) {
                    alert("產品平均利率資料須為數值資料！");
                    return false;
                }
            }
        }
    }
    
    if ($frm.find("#Month").val().length == 0) {
        alert("請輸入貸款期數！");
        return false;
    }
    else {
    	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        var month = DOMPurify.sanitize($frm.find("#Month").val());
        inputStr = month;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    alert("貸款期數資料須為正整數的數值資料！");
                    return false;
                }
            }
        }
    }
    
    if ($frm.find("#Grace").val().length == 0) {
    	$frm.find("#Grace").val("0");
        var grace = 0;
    }
    else {
    	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        var grace = DOMPurify.sanitize($frm.find("#Grace").val());
        inputStr = grace;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    alert("寬限期資料須為正整數的數值資料！");
                    return false;
                }
            }
        }
        if (grace > 36) {
            alert("寬限期範圍為0～36個月");
            return false;
        }
    }
    
    if ($frm.find("#Mmoney").val().length == 0) {
    	$frm.find("#Mmoney").val("0");
        var mmoney = 0;
    }
    else {
        var mmoney = $frm.find("#Mmoney").val();
        inputStr = mmoney;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    mmoney = 0;
                    $frm.find("#Mmoney").val("0");
                }
            }
        }
    }
    
    if ($frm.find("#Tmoney").val().length == 0) {
    	$frm.find("#Tmoney").val("0");
        var tmoney = 0;
    }
    else {
        var tmoney = $frm.find("#Tmoney").val();
        inputStr = tmoney;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if (oneChar != "0") {
                    tmoney = 0;
                    $frm.find("#Tmoney").val("0");
                }
            }
        }
    }
    
    if ($frm.find("#Trate").val().length == 0) {
    	$frm.find("#Trate").val("0");
        var trate = 0;
    }
    else {
        var trate = $frm.find("#Trate").val();
        inputStr = trate;
        for (var k = 0; k < inputStr.length; k++) {
            var oneChar = inputStr.substring(k, k + 1)
            if (!parseFloat(oneChar)) {
                if ((oneChar != "0") && (oneChar != ".")) {
                    trate = 0;
                    $frm.find("#Trate").val("0");
                }
            }
        }
    }
    
    var pmt = 0, pmt_1 = 0, pmt_2 = 0;
    var result = 20;
    var total = Math.round(money - mmoney - tmoney - (money * trate / 100));
    var flag = -1;
    var sub_total;
    var i;
    var j;
    var rate_tmp;
    var sub;
    var result_true;
    
    if (parseInt(grace) > parseInt(month)) {
        alert("寬限期須小於貸款期數！");
        return false;
    }
    
    if (grace != 0) {
        pmt_1 = Math.round(money * (rate / 1200));
        pmt_2 = Math.round(money * (rate / 1200) /
        (1 - (1 / Math.pow(1 + rate / 1200, month - grace))));
    }
    else {
        pmt = Math.round(money * (rate / 1200) /
        (1 - (1 / Math.pow(1 + rate / 1200, month))));
    }
    
    while (flag <= 0) {
        sub_total = 0;
        i = 1;
        result_true = result;
        for (j = 1; j <= month; j++) {
            rate_tmp = Math.pow(1 + result / 1200, -i);
            
            if (grace != 0) {
                if (j <= grace) {
                    pmt = pmt_1;
                }
                else {
                    pmt = pmt_2;
                }
            }
            
            sub = pmt * rate_tmp;
            sub_total = sub_total + sub;
            i++;
        }
        flag = eval(sub_total) - eval(total);
        result = eval(result) - 0.001;
    }
    
    result = result + 0.001;
    result_true_string = result_true.toString();
    var pos = result_true_string.indexOf(".");
    $frm.find("#Result").val(result_true_string.substring(0, pos + 4));
    $frm.find("#Pmt").val(pmt);
}
 