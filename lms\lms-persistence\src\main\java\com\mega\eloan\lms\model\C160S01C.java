/* 
 * C160S01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.apache.bval.constraints.NotEmpty;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.lms.validation.group.Check2;
import com.mega.eloan.lms.validation.group.Check3;
import com.mega.eloan.lms.validation.group.SaveCheck;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 個金產品種類檔 **/
@NamedEntityGraph(name = "C160S01C-entity-graph", attributeNodes = { @NamedAttributeNode("c160s01e") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C160S01C", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "seq", "refmainId" }))
public class C160S01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 額度明細表來源mainId
	 */
	@Size(max = 32)
	@Column(name = "REFMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String refmainId;

	/** 序號 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "SEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer seq;

	/** 流水號 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "CASESEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer caseSeq;

	/** 額度序號 **/
	@Size(max = 12)
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/** 產品種類 **/
	@Size(max = 2)
	@Column(name = "PRODKIND", length = 2, columnDefinition = "CHAR(02)")
	private String prodKind;

	/**
	 * 科目
	 * <p/>
	 * ※會計科目代碼
	 */
	@Size(max = 8)
	@Column(name = "SUBJCODE", length = 8, columnDefinition = "VARCHAR(8)")
	private String subjCode;

	/**
	 * 承做性質
	 * <p/>
	 * 單選：<br/>
	 * 報價|13 ( 個金無此項目<br/>
	 * 新做|1<br/>
	 * 增額|5<br/>
	 * 紓困|10<br/>
	 * 協議清償|12<br/>
	 * 減額|6<br/>
	 * 變更條件|3<br/>
	 * 續約|2<br/>
	 * 提前續約|11<br/>
	 * 展期(不良授信案)|9<br/>
	 * 流用|4 ( 個金無此項目<br/>
	 * 取消|8<br/>
	 * 不變|7
	 */
	@Size(max = 2)
	@Column(name = "PROPERTY", length = 2, columnDefinition = "VARCHAR(2)")
	private String property;

	/**
	 * 動撥幣別
	 * <p/>
	 * 預設：新台幣TWD
	 */
	@Size(max = 3)
	@Column(name = "LOANCURR", length = 3, columnDefinition = "CHAR(3)")
	private String loanCurr;

	/**
	 * 動撥金額
	 * <p/>
	 * 單位：元
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "LOANAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal loanAmt;

	/**
	 * 核准額度
	 * <p/>
	 * 單位：元
	 */
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "APPROVEDAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal approvedAmt;

	/**
	 * 利率
	 * <p/>
	 * 256個全型字
	 */
	@Size(max = 1024)
	@Column(name = "RATEDESC", length = 1024, columnDefinition = "VARCHAR(1024)")
	private String rateDesc;

	/**
	 * 費率
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 256個全型字產品種類為ZZ時顯示
	 */
	@Size(max = 768)
	@Column(name = "FREERATEDESC", length = 768, columnDefinition = "VARCHAR(768)")
	private String freeRateDesc;

	/** 期限-年 **/
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "YEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer year;

	/** 期限-月 **/
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "MONTH", columnDefinition = "DECIMAL(2,0)")
	private Integer Month;

	/**
	 * 授信期間選項
	 * <p/>
	 * 單選：<br/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月
	 */
	@Size(max = 1)
	@Column(name = "LNSELECT", length = 1, columnDefinition = "CHAR(1)")
	private String lnSelect;

	/**
	 * 授信期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LNSTARTDATE", columnDefinition = "DATE")
	private Date lnStartDate;

	/**
	 * 授信期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LNENDDATE", columnDefinition = "DATE")
	private Date lnEndDate;

	/**
	 * 授信期間-年數
	 * <p/>
	 * lnSelect<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "LNYEAR", columnDefinition = "DECIMAL(2,0)")
	private Integer lnYear;

	/**
	 * 授信期間-月數
	 * <p/>
	 * lnSelect<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "LNMONTH", columnDefinition = "DECIMAL(2,0)")
	private Integer lnMonth;

	/**
	 * 授信期間-其他
	 * <p/>
	 * 5.其他
	 */
	@Size(max = 60)
	@Column(name = "LNOTHER", length = 60, columnDefinition = "VARCHAR(60)")
	private String lnOther;

	/**
	 * 動用期間選項
	 * <p/>
	 * 0.不再動用<br/>
	 * 1.YYYY-MM-DD~YYYY-MM-DD<br/>
	 * 2.自核准日起MM個月<br/>
	 * 3.自簽約日起MM個月<br/>
	 * 4.自首次動用日起MM個月<br/>
	 * 5.其他
	 */
	@Size(max = 1)
	@Column(name = "USELINE", length = 1, columnDefinition = "CHAR(01)")
	private String useLine;

	/**
	 * 動用期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Temporal(TemporalType.DATE)
	@Column(name = "USESTARTDATE", columnDefinition = "DATE")
	private Date useStartDate;

	/**
	 * 動用期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Temporal(TemporalType.DATE)
	@Column(name = "USEENDDATE", columnDefinition = "DATE")
	private Date useEndDate;

	/**
	 * 動用期間-月數
	 * <p/>
	 * lnSelect<br/>
	 * 2.自核准日起YY年MM月<br/>
	 * 3.自簽約日起YY年MM月
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "USEMONTH", columnDefinition = "DECIMAL(2,0)")
	private Integer useMonth;

	/**
	 * 動用期間-其他
	 * <p/>
	 * 5.其他
	 */
	@Size(max = 60)
	@Column(name = "USEOTHER", length = 60, columnDefinition = "VARCHAR(60)")
	private String useOther;

	/**
	 * 自動扣帳
	 * <p/>
	 * Y|是<br/>
	 * N|否
	 */
	@Size(max = 1)
	@Column(name = "AUTOPAY", length = 1, columnDefinition = "CHAR(01)")
	private String autoPay;

	/** 扣款帳號 **/
	@NotNull(message = "{required.message}", groups = Check2.class)
	@NotEmpty(message = "{required.message}", groups = Check2.class)
	@Size(max = 16)
	@Column(name = "ATPAYNO", length = 16, columnDefinition = "VARCHAR(16)")
	private String atpayNo;

	/**
	 * 自動進帳
	 * <p/>
	 * Y|是<br/>
	 * N|否
	 */
	@Size(max = 1)
	@Column(name = "AUTORCT", length = 1, columnDefinition = "CHAR(01)")
	private String autoRct;

	/** 進帳金額 **/
	@NotNull(message = "{required.message}", groups = Check3.class)
	@NotEmpty(message = "{required.message}", groups = Check3.class)
	@Digits(integer = 15, fraction = 0, groups = Check.class)
	@Column(name = "RCTAMT", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal rctAMT;

	/** 存款帳號/進帳帳號 **/
	@NotNull(message = "{required.message}", groups = Check3.class)
	@NotEmpty(message = "{required.message}", groups = Check3.class)
	@Size(max = 16)
	@Column(name = "ACCNO", length = 16, columnDefinition = "VARCHAR(16)")
	private String accNo;

	/** 進帳日期 **/
	@NotNull(message = "{required.message}", groups = Check3.class)
	@NotEmpty(message = "{required.message}", groups = Check3.class)
	@Temporal(TemporalType.DATE)
	@Column(name = "RCTDATE", columnDefinition = "DATE")
	private Date rctDate;

	/**
	 * 計收延遲利息加碼
	 * <p/>
	 * 案件若有轉入催收系統時，可依此計算延遲利息，預設為1.00%，可修改。<br/>
	 * ※此欄不列印於報表
	 */
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "DRATEADD", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal dRateAdd;

	/**
	 * 違約金計算條件-逾期
	 * <p/>
	 * ※案件若轉入催收系統時，可依此計算違約金。欄位值一定要輸入但不印於報表。()為預設值，可修改。<br/>
	 * 借戶如延遲還本或付息時，本金自到期日起，利息自繳息日起，逾期在(6)個月以內部份，按約定利率百分之(10)，逾期超過(6)個月部份，
	 * 按約定利率百分之(20)計付違約金。<br/>
	 * <br/>
	 * 逾期在XX個月以內部份
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "DMONTH1", columnDefinition = "DECIMAL(2,0)")
	private Integer dMonth1;

	/**
	 * 違約金計算條件-利率百分比
	 * <p/>
	 * 按約定利率百分之XX
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "DRATE1", columnDefinition = "DECIMAL(2,0)")
	private Integer dRate1;

	/**
	 * 違約金計算條件-逾期超過
	 * <p/>
	 * 逾期超過XX個月部份
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "DMONTH2", columnDefinition = "DECIMAL(2,0)")
	private Integer dMonth2;

	/**
	 * 違約金計算條件-利率百分比
	 * <p/>
	 * 按約定利率百分之XX計違約金
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "DRATE2", columnDefinition = "DECIMAL(2,0)")
	private Integer dRate2;

	/**
	 * 所有權取得日
	 * <p/>
	 * 只限產品57才有
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "GETDATE", columnDefinition = "DATE")
	private Date getDate;

	/**
	 * 行銷分行
	 * <p/>
	 * 行銷分行
	 */
	@Size(max = 3)
	@Column(name = "EFCTBH", length = 3, columnDefinition = "CHAR(3)")
	private String efctBH;

	/**
	 * 扣帳方式
	 * <p/>
	 * ※團貸案時才顯示<br/>
	 * 00-一般<br/>
	 * 01-ACH扣帳	 <br/>
	 * 		Q：客戶想期付金自動扣別家銀行存款,可以設定L504-80或74嗎?	 <br/>
	 * 		A：	L504-80和74功能是於民國93年配合專案貸款增加的功能,目前都沒有ACH和郵局扣款了。	 <br/>
	 * 			ACH扣款須和授管處和票交所提出申請,才可以設定。	 <br/>
	 * 			● L504-80、非本行存款扣帳方式	 <br/>
	 * 			● L504-74、ACH授權扣帳帳號	 <br/> 		
	 * 			交易代號L650期付金郵局代扣作業
	 */
	@Size(max = 2)
	@Column(name = "PAYTYPE", length = 2, columnDefinition = "CHAR(2)")
	private String payType;

	/**
	 * 銀行代碼
	 * <p/>
	 * ※當payType = 01時才需填入
	 */
	@Size(max = 3)
	@Column(name = "ACHBANKNO", length = 3, columnDefinition = "CHAR(3)")
	private String achBankNo;

	/**
	 * 銀行名稱
	 * <p/>
	 * ※當payType = 01時才需填入
	 */
	@Size(max = 60)
	@Column(name = "ACHBANKNM", length = 60, columnDefinition = "VARCHAR(60)")
	private String achBankNm;

	/**
	 * 分行代號
	 * <p/>
	 * ※當payType = 01時才需填入
	 */
	@Size(max = 7)
	@Column(name = "ACHBRANCHNO", length = 7, columnDefinition = "VARCHAR(7)")
	private String achBranchNo;

	/**
	 * 分行名稱
	 * <p/>
	 * ※當payType = 01時才需填入
	 */
	@Size(max = 60)
	@Column(name = "ACHBRANCHNM", length = 60, columnDefinition = "VARCHAR(60)")
	private String achBranchNm;

	/**
	 * 帳號
	 * <p/>
	 * ※當payType = 01時才需填入
	 */
	@Size(max = 16)
	@Column(name = "ACHACCOUNT", length = 16, columnDefinition = "VARCHAR(16)")
	private String achAccount;

	/**
	 * 服務單位統編
	 * <p/>
	 * ※當payType = 01時才需填入
	 */
	@Size(max = 10)
	@Column(name = "ACHSERVID", length = 10, columnDefinition = "VARCHAR(10)")
	private String achServId;

	/** 本次寬限期(起) **/
	@Digits(integer = 3, fraction = 0)
	@Column(name = "NOWFROM", columnDefinition = "DECIMAL(3,0)")
	private Integer nowFrom;

	/** 本次寬限期(迄) **/
	@Digits(integer = 3, fraction = 0)
	@Column(name = "NOWEND", columnDefinition = "DECIMAL(3,0)")
	private Integer nowEnd;

	/**
	 * 開辦費
	 * <p/>
	 * 單位：元
	 */
	@Digits(integer = 13, fraction = 2)
	@Column(name = "AGENCYAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal agencyAMT;
	
	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 排序條件 **/
	@Digits(integer = 5, fraction = 0)
	@Column(name = "UISEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer uiSeq;

	/** 每次違約狀態最高連續收取期數 **/
	@Digits(integer = 2, fraction = 0)
	@Column(name = "PENALTYMAXCONTTM", columnDefinition = "DECIMAL(2,0)")
	private Integer penaltyMaxContTm;

	/** 契約書種類：1-購屋契約, 2-純信用無擔保(DBR22倍), 3-其他類契約, 4-以房養老契約 **/	
	@Column(name = "CTRTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String ctrType;
	
	/**
	 * 以房養老每期利息上限金額(單位：元)
	 */
	@Digits(integer = 13, fraction = 2)
	@Column(name = "RMINTMAX", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rmIntMax;
	
	/** 首次還款日(期付金) */
	@Temporal(TemporalType.DATE)
	@Column(name = "PMT_1ST_RT_DT", columnDefinition = "DATE")
	private Date pmt_1st_rt_dt;
	
	/**
	 * 撥款他行銀行代碼
	 * <p/>
	 * ※當為PLOAN線上簽約且為撥款他行時才需填入
	 */
	@Size(max = 3)
	@Column(name = "APPOTHERBANKNO", length = 3, columnDefinition = "CHAR(3)")
	private String appOtherBankNo;
	
	/**
	 * 撥款他行分行代碼
	 * <p/>
	 * ※當為PLOAN線上簽約且為撥款他行時才需填入
	 */
	@Size(max = 7)
	@Column(name = "APPOTHERBRANCHNO", length = 7, columnDefinition = "VARCHAR(7)")
	private String appOtherBranchNo;
	
	/**
	 * 撥款他行銀行名稱
	 * <p/>
	 * ※當為PLOAN線上簽約且為撥款他行時才需填入
	 */
	@Size(max = 60)
	@Column(name = "APPOTHERBANKNM", length = 60, columnDefinition = "VARCHAR(60)")
	private String appOtherBankNm;
	
	/**
	 * 撥款他行分行名稱
	 * <p/>
	 * ※當為PLOAN線上簽約且為撥款他行時才需填入
	 */
	@Size(max = 60)
	@Column(name = "APPOTHERBRANCHNM", length = 60, columnDefinition = "VARCHAR(60)")
	private String appOtherBranchNm;
	
	/**
	 * 撥款他行帳號
	 * <p/>
	 * ※當為PLOAN線上簽約且為撥款他行時才需填入
	 */
	@Size(max = 16)
	@Column(name = "APPOTHERACCOUNT", length = 16, columnDefinition = "VARCHAR(16)")
	private String appOtherAccount;
	
	/**
	 * PLOAN契約同意ACH
	 * <p/>
	 * ※當為PLOAN線上簽約且同意ACH
	 * Y-同意<br/>
	 * N-未同意<br/>
	 */
	@Size(max = 1)
	@Column(name = "PLOANISNEEDACH", length = 1, columnDefinition = "VARCHAR(1)")
	private String ploanIsNeedACH;

	/**
	 * 預約動用逾期原因
	 * <p/>
	 * ※當為房貸且動用期間超過「預計動撥月」才需填入
	 */
	@Size(max = 150)
	@Column(name = "OVERDUEREASON", length = 1, columnDefinition = "VARCHAR(150)")
	private String overdueReason;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得額度明細表來源mainId
	 * <p/>
	 * 額度明細表來源mainId
	 */
	public String getRefmainId() {
		return this.refmainId;
	}
	
	/**
	 * 設定額度明細表來源mainId
	 * <p/>
	 * 額度明細表來源mainId
	 **/
	public void setRefmainId(String value) {
		this.refmainId = value;
	}

	/** 取得序號 **/
	public Integer getSeq() {
		return this.seq;
	}

	/** 設定序號 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/** 取得流水號 **/
	public Integer getCaseSeq() {
		return this.caseSeq;
	}

	/** 設定流水號 **/
	public void setCaseSeq(Integer value) {
		this.caseSeq = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}

	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得產品種類 **/
	public String getProdKind() {
		return this.prodKind;
	}

	/** 設定產品種類 **/
	public void setProdKind(String value) {
		this.prodKind = value;
	}

	/**
	 * 取得科目
	 * <p/>
	 * ※會計科目代碼
	 */
	public String getSubjCode() {
		return this.subjCode;
	}

	/**
	 * 設定科目
	 * <p/>
	 * ※會計科目代碼
	 **/
	public void setSubjCode(String value) {
		this.subjCode = value;
	}

	/**
	 * 取得承做性質
	 * <p/>
	 * 單選：<br/>
	 * 報價|13 ( 個金無此項目<br/>
	 * 新做|1<br/>
	 * 增額|5<br/>
	 * 紓困|10<br/>
	 * 協議清償|12<br/>
	 * 減額|6<br/>
	 * 變更條件|3<br/>
	 * 續約|2<br/>
	 * 提前續約|11<br/>
	 * 展期(不良授信案)|9<br/>
	 * 流用|4 ( 個金無此項目<br/>
	 * 取消|8<br/>
	 * 不變|7
	 */
	public String getProperty() {
		return this.property;
	}

	/**
	 * 設定承做性質
	 * <p/>
	 * 單選：<br/>
	 * 報價|13 ( 個金無此項目<br/>
	 * 新做|1<br/>
	 * 增額|5<br/>
	 * 紓困|10<br/>
	 * 協議清償|12<br/>
	 * 減額|6<br/>
	 * 變更條件|3<br/>
	 * 續約|2<br/>
	 * 提前續約|11<br/>
	 * 展期(不良授信案)|9<br/>
	 * 流用|4 ( 個金無此項目<br/>
	 * 取消|8<br/>
	 * 不變|7
	 **/
	public void setProperty(String value) {
		this.property = value;
	}

	/**
	 * 取得動撥幣別
	 * <p/>
	 * 預設：新台幣TWD
	 */
	public String getLoanCurr() {
		return this.loanCurr;
	}

	/**
	 * 設定動撥幣別
	 * <p/>
	 * 預設：新台幣TWD
	 **/
	public void setLoanCurr(String value) {
		this.loanCurr = value;
	}

	/**
	 * 取得動撥金額
	 * <p/>
	 * 單位：元
	 */
	public BigDecimal getLoanAmt() {
		return this.loanAmt;
	}

	/**
	 * 設定動撥金額
	 * <p/>
	 * 單位：元
	 **/
	public void setLoanAmt(BigDecimal value) {
		this.loanAmt = value;
	}

	/**
	 * 取得核准額度
	 * <p/>
	 * 單位：元
	 */
	public BigDecimal getApprovedAmt() {
		return this.approvedAmt;
	}

	/**
	 * 設定核准額度
	 * <p/>
	 * 單位：元
	 **/
	public void setApprovedAmt(BigDecimal value) {
		this.approvedAmt = value;
	}

	/**
	 * 取得利率
	 * <p/>
	 * 256個全型字
	 */
	public String getRateDesc() {
		return this.rateDesc;
	}

	/**
	 * 設定利率
	 * <p/>
	 * 256個全型字
	 **/
	public void setRateDesc(String value) {
		this.rateDesc = value;
	}

	/**
	 * 取得費率
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 256個全型字產品種類為ZZ時顯示
	 */
	public String getFreeRateDesc() {
		return this.freeRateDesc;
	}

	/**
	 * 設定費率
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 256個全型字產品種類為ZZ時顯示
	 **/
	public void setFreeRateDesc(String value) {
		this.freeRateDesc = value;
	}

	/** 取得期限-年 **/
	public Integer getYear() {
		return this.year;
	}

	/** 設定期限-年 **/
	public void setYear(Integer value) {
		this.year = value;
	}

	/** 取得期限-月 **/
	public Integer getMonth() {
		return this.Month;
	}

	/** 設定期限-月 **/
	public void setMonth(Integer value) {
		this.Month = value;
	}

	/**
	 * 取得授信期間選項
	 * <p/>
	 * 單選：<br/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月
	 */
	public String getLnSelect() {
		return this.lnSelect;
	}

	/**
	 * 設定授信期間選項
	 * <p/>
	 * 單選：<br/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月
	 **/
	public void setLnSelect(String value) {
		this.lnSelect = value;
	}

	/**
	 * 取得授信期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	public Date getLnStartDate() {
		return this.lnStartDate;
	}

	/**
	 * 設定授信期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 **/
	public void setLnStartDate(Date value) {
		this.lnStartDate = value;
	}

	/**
	 * 取得授信期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	public Date getLnEndDate() {
		return this.lnEndDate;
	}

	/**
	 * 設定授信期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 **/
	public void setLnEndDate(Date value) {
		this.lnEndDate = value;
	}

	/**
	 * 取得授信期間-年數
	 * <p/>
	 * lnSelect<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月
	 */
	public Integer getLnYear() {
		return this.lnYear;
	}

	/**
	 * 設定授信期間-年數
	 * <p/>
	 * lnSelect<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月
	 **/
	public void setLnYear(Integer value) {
		this.lnYear = value;
	}

	/**
	 * 取得授信期間-月數
	 * <p/>
	 * lnSelect<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月
	 */
	public Integer getLnMonth() {
		return this.lnMonth;
	}

	/**
	 * 設定授信期間-月數
	 * <p/>
	 * lnSelect<br/>
	 * 2.YY年MM月<br/>
	 * 3.自核准日起YY年MM月<br/>
	 * 4.自簽約日起YY年MM月
	 **/
	public void setLnMonth(Integer value) {
		this.lnMonth = value;
	}

	/**
	 * 取得授信期間-其他
	 * <p/>
	 * 5.其他
	 */
	public String getLnOther() {
		return this.lnOther;
	}

	/**
	 * 設定授信期間-其他
	 * <p/>
	 * 5.其他
	 **/
	public void setLnOther(String value) {
		this.lnOther = value;
	}

	/**
	 * 取得動用期間選項
	 * <p/>
	 * 0.不再動用<br/>
	 * 1.YYYY-MM-DD~YYYY-MM-DD<br/>
	 * 2.自核准日起MM個月<br/>
	 * 3.自簽約日起MM個月<br/>
	 * 4.自首次動用日起MM個月<br/>
	 * 5.其他
	 */
	public String getUseLine() {
		return this.useLine;
	}

	/**
	 * 設定動用期間選項
	 * <p/>
	 * 0.不再動用<br/>
	 * 1.YYYY-MM-DD~YYYY-MM-DD<br/>
	 * 2.自核准日起MM個月<br/>
	 * 3.自簽約日起MM個月<br/>
	 * 4.自首次動用日起MM個月<br/>
	 * 5.其他
	 **/
	public void setUseLine(String value) {
		this.useLine = value;
	}

	/**
	 * 取得動用期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	public Date getUseStartDate() {
		return this.useStartDate;
	}

	/**
	 * 設定動用期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 **/
	public void setUseStartDate(Date value) {
		this.useStartDate = value;
	}

	/**
	 * 取得動用期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	public Date getUseEndDate() {
		return this.useEndDate;
	}

	/**
	 * 設定動用期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 **/
	public void setUseEndDate(Date value) {
		this.useEndDate = value;
	}

	/**
	 * 取得動用期間-月數
	 * <p/>
	 * lnSelect<br/>
	 * 2.自核准日起YY年MM月<br/>
	 * 3.自簽約日起YY年MM月
	 */
	public Integer getUseMonth() {
		return this.useMonth;
	}

	/**
	 * 設定動用期間-月數
	 * <p/>
	 * lnSelect<br/>
	 * 2.自核准日起YY年MM月<br/>
	 * 3.自簽約日起YY年MM月
	 **/
	public void setUseMonth(Integer value) {
		this.useMonth = value;
	}

	/**
	 * 取得動用期間-其他
	 * <p/>
	 * 5.其他
	 */
	public String getUseOther() {
		return this.useOther;
	}

	/**
	 * 設定動用期間-其他
	 * <p/>
	 * 5.其他
	 **/
	public void setUseOther(String value) {
		this.useOther = value;
	}

	/**
	 * 取得自動扣帳
	 * <p/>
	 * Y|是<br/>
	 * N|否
	 */
	public String getAutoPay() {
		return this.autoPay;
	}

	/**
	 * 設定自動扣帳
	 * <p/>
	 * Y|是<br/>
	 * N|否
	 **/
	public void setAutoPay(String value) {
		this.autoPay = value;
	}

	/** 取得扣款帳號 **/
	public String getAtpayNo() {
		return this.atpayNo;
	}

	/** 設定扣款帳號 **/
	public void setAtpayNo(String value) {
		this.atpayNo = value;
	}

	/**
	 * 取得自動進帳
	 * <p/>
	 * Y|是<br/>
	 * N|否
	 */
	public String getAutoRct() {
		return this.autoRct;
	}

	/**
	 * 設定自動進帳
	 * <p/>
	 * Y|是<br/>
	 * N|否
	 **/
	public void setAutoRct(String value) {
		this.autoRct = value;
	}

	/** 取得進帳金額 **/
	public BigDecimal getRctAMT() {
		return this.rctAMT;
	}

	/** 設定進帳金額 **/
	public void setRctAMT(BigDecimal value) {
		this.rctAMT = value;
	}

	/** 取得存款帳號/進帳帳號 **/
	public String getAccNo() {
		return this.accNo;
	}

	/** 設定存款帳號/進帳帳號 **/
	public void setAccNo(String value) {
		this.accNo = value;
	}

	/** 取得進帳日期 **/
	public Date getRctDate() {
		return this.rctDate;
	}

	/** 設定進帳日期 **/
	public void setRctDate(Date value) {
		this.rctDate = value;
	}

	/**
	 * 取得計收延遲利息加碼
	 * <p/>
	 * 案件若有轉入催收系統時，可依此計算延遲利息，預設為1.00%，可修改。<br/>
	 * ※此欄不列印於報表
	 */
	public BigDecimal getDRateAdd() {
		return this.dRateAdd;
	}

	/**
	 * 設定計收延遲利息加碼
	 * <p/>
	 * 案件若有轉入催收系統時，可依此計算延遲利息，預設為1.00%，可修改。<br/>
	 * ※此欄不列印於報表
	 **/
	public void setDRateAdd(BigDecimal value) {
		this.dRateAdd = value;
	}

	/**
	 * 取得違約金計算條件-逾期
	 * <p/>
	 * ※案件若轉入催收系統時，可依此計算違約金。欄位值一定要輸入但不印於報表。()為預設值，可修改。<br/>
	 * 借戶如延遲還本或付息時，本金自到期日起，利息自繳息日起，逾期在(6)個月以內部份，按約定利率百分之(10)，逾期超過(6)個月部份，
	 * 按約定利率百分之(20)計付違約金。<br/>
	 * <br/>
	 * 逾期在XX個月以內部份
	 */
	public Integer getDMonth1() {
		return this.dMonth1;
	}

	/**
	 * 設定違約金計算條件-逾期
	 * <p/>
	 * ※案件若轉入催收系統時，可依此計算違約金。欄位值一定要輸入但不印於報表。()為預設值，可修改。<br/>
	 * 借戶如延遲還本或付息時，本金自到期日起，利息自繳息日起，逾期在(6)個月以內部份，按約定利率百分之(10)，逾期超過(6)個月部份，
	 * 按約定利率百分之(20)計付違約金。<br/>
	 * <br/>
	 * 逾期在XX個月以內部份
	 **/
	public void setDMonth1(Integer value) {
		this.dMonth1 = value;
	}

	/**
	 * 取得違約金計算條件-利率百分比
	 * <p/>
	 * 按約定利率百分之XX
	 */
	public Integer getDRate1() {
		return this.dRate1;
	}

	/**
	 * 設定違約金計算條件-利率百分比
	 * <p/>
	 * 按約定利率百分之XX
	 **/
	public void setDRate1(Integer value) {
		this.dRate1 = value;
	}

	/**
	 * 取得違約金計算條件-逾期超過
	 * <p/>
	 * 逾期超過XX個月部份
	 */
	public Integer getDMonth2() {
		return this.dMonth2;
	}

	/**
	 * 設定違約金計算條件-逾期超過
	 * <p/>
	 * 逾期超過XX個月部份
	 **/
	public void setDMonth2(Integer value) {
		this.dMonth2 = value;
	}

	/**
	 * 取得違約金計算條件-利率百分比
	 * <p/>
	 * 按約定利率百分之XX計違約金
	 */
	public Integer getDRate2() {
		return this.dRate2;
	}

	/**
	 * 設定違約金計算條件-利率百分比
	 * <p/>
	 * 按約定利率百分之XX計違約金
	 **/
	public void setDRate2(Integer value) {
		this.dRate2 = value;
	}

	/**
	 * 取得所有權取得日
	 * <p/>
	 * 只限產品57才有
	 */
	public Date getGetDate() {
		return this.getDate;
	}

	/**
	 * 設定所有權取得日
	 * <p/>
	 * 只限產品57才有
	 **/
	public void setGetDate(Date value) {
		this.getDate = value;
	}

	/**
	 * 取得行銷分行
	 * <p/>
	 * 行銷分行
	 */
	public String getEfctBH() {
		return this.efctBH;
	}

	/**
	 * 設定行銷分行
	 * <p/>
	 * 行銷分行
	 **/
	public void setEfctBH(String value) {
		this.efctBH = value;
	}

	/**
	 * 取得扣帳方式
	 * <p/>
	 * ※團貸案時才顯示<br/>
	 * 00-一般<br/>
	 * 01-ACH扣帳
	 */
	public String getPayType() {
		return this.payType;
	}

	/**
	 * 取得扣帳方式
	 * <p/>
	 * ※團貸案時才顯示<br/>
	 * 00-一般<br/>
	 * 01-ACH扣帳
	 */
	public void setPayType(String value) {
		this.payType = value;
	}

	/**
	 * 取得銀行代碼
	 * <p/>
	 * ※當payType = 01時才需填入
	 */
	public String getAchBankNo() {
		return this.achBankNo;
	}

	/**
	 * 設定銀行代碼
	 * <p/>
	 * ※當payType = 01時才需填入
	 **/
	public void setAchBankNo(String value) {
		this.achBankNo = value;
	}

	/**
	 * 取得銀行名稱
	 * <p/>
	 * ※當payType = 01時才需填入
	 */
	public String getAchBankNm() {
		return this.achBankNm;
	}

	/**
	 * 設定銀行名稱
	 * <p/>
	 * ※當payType = 01時才需填入
	 **/
	public void setAchBankNm(String value) {
		this.achBankNm = value;
	}

	/**
	 * 取得分行代號
	 * <p/>
	 * ※當payType = 01時才需填入
	 */
	public String getAchBranchNo() {
		return this.achBranchNo;
	}

	/**
	 * 設定分行代號
	 * <p/>
	 * ※當payType = 01時才需填入
	 **/
	public void setAchBranchNo(String value) {
		this.achBranchNo = value;
	}

	/**
	 * 取得分行名稱
	 * <p/>
	 * ※當payType = 01時才需填入
	 */
	public String getAchBranchNm() {
		return this.achBranchNm;
	}

	/**
	 * 設定分行名稱
	 * <p/>
	 * ※當payType = 01時才需填入
	 **/
	public void setAchBranchNm(String value) {
		this.achBranchNm = value;
	}

	/**
	 * 取得帳號
	 * <p/>
	 * ※當payType = 01時才需填入
	 */
	public String getAchAccount() {
		return this.achAccount;
	}

	/**
	 * 設定帳號
	 * <p/>
	 * ※當payType = 01時才需填入
	 **/
	public void setAchAccount(String value) {
		this.achAccount = value;
	}

	/**
	 * 取得服務單位統編
	 * <p/>
	 * ※當payType = 01時才需填入
	 */
	public String getAchServId() {
		return this.achServId;
	}

	/**
	 * 設定服務單位統編
	 * <p/>
	 * ※當payType = 01時才需填入
	 **/
	public void setAchServId(String value) {
		this.achServId = value;
	}

	/** 本次寬限期(起) **/
	public Integer getNowFrom() {
		return nowFrom;
	}

	/** 本次寬限期(起) **/
	public void setNowFrom(Integer nowFrom) {
		this.nowFrom = nowFrom;
	}

	/** 本次寬限期(迄) **/
	public Integer getNowEnd() {
		return nowEnd;
	}

	/** 本次寬限期(迄) **/
	public void setNowEnd(Integer nowEnd) {
		this.nowEnd = nowEnd;
	}
	
	/**
	 * 取得開辦費
	 * <p/>
	 * 單位：元
	 */
	public BigDecimal getAgencyAMT() {
		return agencyAMT;
	}

	/**
	 * 設定開辦費
	 * <p/>
	 * 單位：元
	 */
	public void setAgencyAMT(BigDecimal agencyAMT) {
		this.agencyAMT = agencyAMT;
	}
	
	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得序號 **/
	public Integer getUiSeq() {
		return this.uiSeq;
	}

	/** 設定序號 **/
	public void setUiSeq(Integer value) {
		this.uiSeq = value;
	}

	/** 取得每次違約狀態最高連續收取期數 **/
	public Integer getPenaltyMaxContTm() {
		return this.penaltyMaxContTm;
	}

	/** 設定每次違約狀態最高連續收取期數 **/
	public void setPenaltyMaxContTm(Integer value) {
		this.penaltyMaxContTm = value;
	}
	
	/** 取得契約書種類：1-購屋契約, 2-純信用無擔保(DBR22倍), 3-其他類契約, 4-以房養老契約 **/
	public String getCtrType() {
		return this.ctrType;
	}

	/** 設定契約書種類：1-購屋契約, 2-純信用無擔保(DBR22倍), 3-其他類契約, 4-以房養老契約 **/
	public void setCtrType(String value) {
		this.ctrType = value;
	}

	/** 取得以房養老每期利息上限金額 **/
	public BigDecimal getRmIntMax() {
		return this.rmIntMax;
	}
	/** 設定以房養老每期利息上限金額 **/
	public void setRmIntMax(BigDecimal value) {
		this.rmIntMax = value;
	}
	
	/** 取得首次還款日 */
	public Date getPmt_1st_rt_dt() {
		return pmt_1st_rt_dt;
	}
	/** 設定首次還款日 */
	public void setPmt_1st_rt_dt(Date pmt_1st_rt_dt) {
		this.pmt_1st_rt_dt = pmt_1st_rt_dt;
	}

	/** 取得撥款他行銀行代碼 */
	public String getAppOtherBankNo() {
		return appOtherBankNo;
	}

	/** 設定撥款他行銀行代碼 */
	public void setAppOtherBankNo(String appOtherBankNo) {
		this.appOtherBankNo = appOtherBankNo;
	}

	/** 取得撥款他行分行代碼 */
	public String getAppOtherBranchNo() {
		return appOtherBranchNo;
	}

	/** 設定撥款他行分行代碼 */
	public void setAppOtherBranchNo(String appOtherBranchNo) {
		this.appOtherBranchNo = appOtherBranchNo;
	}

	/** 取得撥款他行帳號 */
	public String getAppOtherAccount() {
		return appOtherAccount;
	}

	/** 設定撥款他行帳號 */
	public void setAppOtherAccount(String appOtherAccount) {
		this.appOtherAccount = appOtherAccount;
	}

	/** 取得撥款他行銀行名稱 */
	public String getAppOtherBankNm() {
		return appOtherBankNm;
	}

	/** 設定撥款他行銀行名稱 */
	public void setAppOtherBankNm(String appOtherBankNm) {
		this.appOtherBankNm = appOtherBankNm;
	}

	/** 取得撥款他行分行名稱 */
	public String getAppOtherBranchNm() {
		return appOtherBranchNm;
	}

	/** 設定撥款他行分行名稱 */
	public void setAppOtherBranchNm(String appOtherBranchNm) {
		this.appOtherBranchNm = appOtherBranchNm;
	}

	/** 取得PLOAN契約同意ACH */
	public String getPloanIsNeedACH() {
		return ploanIsNeedACH;
	}

	/** 設定PLOAN契約同意ACH */
	public void setPloanIsNeedACH(String ploanIsNeedACH) {
		this.ploanIsNeedACH = ploanIsNeedACH;
	}

	public String getOverdueReason() {
		return overdueReason;
	}

	public void setOverdueReason(String OverdueReason) {
		overdueReason = OverdueReason;
	}

	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "refmainId", referencedColumnName = "refmainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "seq", referencedColumnName = "seq", nullable = false, insertable = false, updatable = false) })
	private C160S01E c160s01e;

	public void setC160s01e(C160S01E c160s01e) {
		this.c160s01e = c160s01e;
	}

	public C160S01E getC160s01e() {
		return c160s01e;
	}
}
