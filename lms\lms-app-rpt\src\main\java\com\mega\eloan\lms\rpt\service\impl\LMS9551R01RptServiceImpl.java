package com.mega.eloan.lms.rpt.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.I18NFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.gwclient.UCCFTPClient;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.DebConfig;
import com.mega.eloan.lms.dao.L201S99CDao;
import com.mega.eloan.lms.dao.L201S99DDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.L201S99C;
import com.mega.eloan.lms.model.L201S99D;
import com.mega.eloan.lms.rpt.pages.LMS9550R01Page;
import com.mega.eloan.lms.rpt.service.LMS9551R01RptService;
import com.mega.sso.context.MegaSSOSecurityContext;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.ADDateTimeFormatter;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapEntityUtil;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 報送卡務 Service
 * </pre>
 * 
 * @since 2012/7/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/16,TammyChen,new
 *          </ul>
 */

@Service
public class LMS9551R01RptServiceImpl extends AbstractCapService implements
		LMS9551R01RptService {

	@Resource
	L201S99CDao l201s99cDao;
	@Resource
	L201S99DDao l201s99dDao;
	@Resource
	DocFileService docFileSrv;
	@Resource
	UserInfoService userInfoSrv;
	@Resource
	UCCFTPClient ftpClient;
	@Resource
	DebConfig debConfig;
	@Resource
	EloandbBASEService eloandbBASEService;

	@Override
	public L201S99C getL201S99CByOid(String oid) {
		if (CapString.isEmpty(oid)) {
			return null;
		}
		return l201s99cDao.find(oid);
	}

	@Override
	public void saveS99C(L201S99C meta) {
		l201s99cDao.save(meta);
	}

	/**
	 * 查詢
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	@Override
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		L201S99C meta = null;
		if (!CapString.isEmpty(mainOid)) {
			meta = this.getL201S99CByOid(mainOid);
			if (meta != null) {

				// 產檔資料
				Map<String, IFormatter> format = new HashMap<String, IFormatter>();
				format.put("exeMsg", new exeMsg());
				format.put("creDate", new ADDateTimeFormatter(
						"yyyy-MM-dd hh:mm:ss a"));

				result.putAll(new CapAjaxFormResult(meta.toJSONObject(
						CapEntityUtil.getColumnName(meta), format)));
				// 各檔資料
				if (!CollectionUtils.isEmpty(meta.getL201s99ds())) {
					for (L201S99D s99d : meta.getL201s99ds()) {
						result.set("count" + s99d.getDataType(), s99d
								.getDataCnt() != null ? s99d.getDataCnt()
								: BigDecimal.ZERO);

					}
				}
				// 檔案資料
				List<DocFile> docFiles = docFileSrv.findByIDAndPid(
						meta.getMainId(), null);
				CapAjaxFormResult fileList = new CapAjaxFormResult();
				if (!CollectionUtils.isEmpty(docFiles)) {
					for (DocFile docFile : docFiles) {
						result.set(docFile.getFieldId(),
								docFile.getSrcFileName());
						fileList.set(docFile.getFieldId(), docFile.getOid());
					}
				}
				result.set("fileList", fileList);
			}
		}

		return defaultResult(params, meta, result);
	}// ;

	/**
	 * exeResult formatter
	 */
	class exeMsg implements IBeanFormatter {
		private static final long serialVersionUID = 1L;
		UserNameFormatter unFormatter = new UserNameFormatter(userInfoSrv);
		Properties properties = MessageBundleScriptCreator
				.getComponentResource(LMS9550R01Page.class);

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			L201S99C meta = (L201S99C) in;
			if (meta != null
					&& (meta.getSendTime() != null || !CapString.isEmpty(meta
							.getSender()))) {
				return StrUtils.concat(CapString.trimNull(CapDate
						.convertTimestampToString(meta.getSendTime(),
								"yyyy-MM-dd hh:mm:ss a")), CapConstants.SPACE,
						CapString.trimNull(unFormatter.reformat(meta
								.getSender())), ":", CapString
								.trimNull(properties.get("exeResult."
										+ meta.getExeResult())));
			}
			return EloanConstants.EMPTY_STRING;
		}
	}

	/**
	 * 統一處理回傳資訊
	 * 
	 * @param params
	 *            PageParameters
	 * @param meta
	 *            D104M01A
	 * @param result
	 *            CapAjaxFormResult
	 * @return IResult
	 * @throws CapException
	 */
	public IResult defaultResult(PageParameters params, L201S99C meta,
			CapAjaxFormResult result) throws CapException {
		if (result == null) {
			result = new CapAjaxFormResult();
		}

		if (meta != null) {
			result.set(EloanConstants.MAIN_ID, meta.getMainId());
			result.set(EloanConstants.MAIN_UID, meta.getUid());
			result.set(EloanConstants.MAIN_OID, meta.getOid());
		}

		return result;
	}

	/**
	 * 傳送檔案至卡務中心
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	@Override
	public IResult sendToFTP(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L201S99C meta = this.getL201S99CByOid(mainOid);
		meta.setExeResult("Y");
		if (meta != null) {
			List<DocFile> files = docFileSrv.findByIDAndPid(meta.getMainId(),
					null);
			ArrayList<String> filePath = new ArrayList<String>();
			ArrayList<String> fileName = new ArrayList<String>();
			if (!CollectionUtils.isEmpty(files)) {
				for (DocFile file : files) {
					if (!"Distributionlist".equals(file.getFieldId())) {
						filePath.add(docFileSrv.getFilePath(file));
						fileName.add(file.getSrcFileName());
					}
				}
				try {

					// 後台管理->系統設定維護->LMS_ArCtrlCanShow
					Map<String, Object> onlineDateMap = eloandbBASEService
							.getSysParamData("UCCFTP_DEF_DIR");

					String serverDir = Util.trim(onlineDateMap
							.get("PARAMVALUE"));

					ftpClient.send(
							meta.getMainId(),
							filePath.toArray(new String[] {}),
							serverDir, // dw/ftpdata/ftpucc1/ftpout/
							// debConfig.getUCCFTPUploadPath(), //
							// 切換路徑至：/ftpout/
							fileName.toArray(new String[] {}), true, true,
							false);
				} catch (Exception e) {
					meta.setExeResult("N");
					e.printStackTrace();
				}
			}
		}
		meta.setSender(MegaSSOSecurityContext.getUserId());
		meta.setSendTime(CapDate.getCurrentTimestamp());
		this.saveS99C(meta);

		result.set("exeMsg", new exeMsg().reformat(meta));
		result.set(EloanConstants.AJAX_NOTIFY_MESSAGE, new I18NFormatter("exeResult.").reformat(meta.getExeResult()));
		return result;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.handler.form.AbstractFormHandler#getOperationName()
	 */
	public String getOperationName() {
		return EloanConstants.OPERATION_FORM;
	}

}
