<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="innerPageBody">
        	<script type="text/javascript" th:utext="${loadScript}">loadScript('pagejs/cls/CLS1161M01Page');</script>
			
            <div class="button-menu funcContainer" id="buttonPanel">
                <!-- "_btnDOC_EDITING -->
                <th:block th:if="${_btnDOC_EDITING_visible}"><button id="btnSave">
                        <span class="ui-icon ui-icon-jcs-04" ></span><th:block th:text="#{'button.save'}">儲存</th:block>
                    </button>
					
                    <button id="btnSend">
                        <span class="ui-icon ui-icon-jcs-02" ></span><th:block th:text="#{'button.send'}">呈主管覆核</th:block>
                    </button>
                </th:block>
				<th:block th:if="${show_queryBeforeMoneyAllocating_button}">
						<button id="btnQueryBeforeSendMoney">
	                        <span class="ui-icon ui-icon-jcs-02" ></span><th:block th:text="#{'button.btnQueryBeforeSendMoney'}">信貸撥款前查詢</th:block>
	                    </button>
					</th:block>
                <!-- _btnWAIT_APPROVE -->
                <th:block th:if="${_btnWAIT_APPROVE_visible}"><button id="btnAccept" class="forview">
                        <span class="ui-icon ui-icon-jcs-106" ></span><th:block th:text="#{'button.accept'}">核准</th:block>
                    </button>
					<button id="btnReturn" class="forview">
                        <span class="ui-icon ui-icon-mail-closed" ></span><th:block th:text="#{'button.return'}">退回</th:block>
                    </button>
                </th:block>
				
				 <th:block th:if="${_btnDOC_END_visible}">
					<button id="btnReUpDate" class="forview">
                        <span class="ui-icon ui-icon-jcs-04" ></span><th:block th:text="#{'button.reUpDate'}">重新上傳</th:block>
                	</button>
                </th:block>
                <button id="btnPrint" class="forview">
                    <span class="ui-icon ui-icon-jcs-03" ></span>
                    <th:block th:text="#{'button.print'}">
                   		     列印
                    </th:block>
                </button>
                <button id="btnExit" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <th:block th:text="#{'button.exit'}">離開</th:block>
                </button>
            </div>
			<div class="tit2 color-black">
				<form id="baseForm" >
					<th:block th:text="#{'title.tab03'}">動用審核表</th:block>(<span id="caseTypeDesc" class="field" ></span>)：
					(<span id="typCd" class="text-red" ></span>)
					<span id="baseCustId" class="field color-blue" ></span>&nbsp;<span id="baseDupNo" class="field color-blue" ></span>&nbsp;<span id="custInfo" class="field color-blue" ></span>
					<input type="hidden" id="caseType" name="caseType" />
					<input type="hidden" id="mainId" name="mainId" th:value="${mainId}" />
				</form>
			</div>
            <div id="mainTab" class="tabs doc-tabs">
                <ul>
                   <li id="mainTab01" ><a href="#tab-01" goto="01"><b><th:block th:text="#{'title.tab01'}">文件資訊</th:block></b></a></li>
                <th:block th:if="${_caseType3_visible}"><li id="mainTab02" ><a href="#tab-02" goto="02"><b><th:block th:text="#{'title.tab02'}">額度明細</th:block></b></a></li>
                   <li id="mainTab03" ><a href="#tab-03" goto="03"><b><th:block th:text="#{'title.tab031'}">檢附資訊/審核意見</th:block></b></a></li>
				   <li id="mainTab04" style="display:none" ><a href="#tab-04" goto="04"><b><th:block th:text="#{'title.tab04'}">先行動用呈核及控制表</th:block></b></a></li>
                </th:block>
                	<li id="mainTab05" ><a href="#tab-05" goto="05"><b>AML/CFT</b></a></li>
					<th:block th:if="${show_ivr_panel_visible}">
						<li>
	                        <a href="#tab-06" goto="06"><b><th:block th:text="#{'title.tab05'}">IVR電話語音檔</th:block></b></a>
	                    </li>
					</th:block>
				</ul>
                <div class="tabCtx-warp">
                    <div th:id="${tabIdx}" th:insert="${panelName} :: ${panelFragmentName}"></div>
                </div>
            </div>
			
			<!-- 簽章欄 -->
			<div id="C160M01EThickBox" style="display:none;" > 
				<form id="C160M01EForm" >
					<table class="tb2" width="100%">
						<tr>
							<td width="30%" class="hd2" align="right"><th:block th:text="#{'manager.L3'}">授信主管</th:block><th:block th:text="#{'manager.numPerson'}">人數</th:block>&nbsp;&nbsp;</td>
							<td width="70%" ><select id="numPerson" name="numPerson" codeType="month" class="required" ></select></td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'manager.L3'}">授信主管</th:block>&nbsp;&nbsp;</td>
							<td ><div id="bossItem" ></div></td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'manager.L5'}">經副襄理</th:block>&nbsp;&nbsp;</td>
							<td ><select id="manager" name="manager" class="required" CommonManager="kind:2;type:3" ></select></td>
						</tr>
					</table>
				</form>
			</div>
			
			<div id="approveThickBox" style="display:none;" > 
				<form id="approveForm" >
					<table class="tb2" width="100%">
						<tr>
							<td width="30%" class="hd2" align="right"><th:block th:text="#{'title.approveDate'}">核定日</th:block>&nbsp;&nbsp;</td>
							<td width="70%" ><input id="approveDate" name="approveDate" class="required date" /></td>
						</tr>
					</table>
					<input style="display:none;" /><!-- Prevent click enter to do submit -->
				</form>
			</div>
        </th:block>
    </body>
</html>
