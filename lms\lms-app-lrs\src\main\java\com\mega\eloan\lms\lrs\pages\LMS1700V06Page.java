package com.mega.eloan.lms.lrs.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.panels.RetrialPtMgrIdPanel;
import com.mega.eloan.lms.lrs.panels.LMS1700FilterPanel;

/**
 * 覆審單位-待覆核
 */
@Controller
@RequestMapping("/lrs/lms1700v06")
public class LMS1700V06Page extends AbstractEloanInnerView {
	private static final String[] I18N_ARR = new String[]{"ui_lms1700.msg10", "ui_lms1700.msg11"};
	
	public LMS1700V06Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {

		setGridViewStatus(RetrialDocStatusEnum.區中心_待覆核);
		addToButtonPanel(model, LmsButtonEnum.Filter,
				LmsButtonEnum.SendToExamUnit);
		renderJsI18N(LMS1700V01Page.class);
		renderJsI18N(RetrialPtMgrIdPanel.class);
		renderJsI18N(LMS1700M01Page.class, I18N_ARR);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "require(['pagejs/lrs/LMS1700FilterPanel'],function(){loadScript('pagejs/lrs/LMS1700V01Page');});");
		setupIPanel(new LMS1700FilterPanel(PANEL_ID, true), model, params);
	}

	protected void addPanel(ModelMap model, PageParameters params, String panelId) {
		new LMS1700FilterPanel(panelId, true).processPanelData(model, params);
	}
}
