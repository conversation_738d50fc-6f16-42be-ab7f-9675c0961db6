/* 
 * C900M01DDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M01D;

/** 會計科子目名稱檔 **/
public interface C900M01DDao extends IGenericDao<C900M01D> {

	C900M01D findByOid(String oid);

	List<C900M01D> getAll();

	List<C900M01D> findByMainId(String mainId);

	/** 會計科子細目(8碼) */
	C900M01D findByUniqueKey(String subjCode);

	/** 授信細目(3~4碼) */
	C900M01D findByUniqueKey02(String subjCode2);

	List<C900M01D> findByIndex01(String subjCode);

	List<C900M01D> findByIndex02(String subjCode2);

	String getSubjCode2(String subjCode);

	String getUpMisSubjCode2(String subjCode);
}