package com.mega.eloan.lms.dc.action;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOCase;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.filefilter.FileFilterUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * DeleteSQL : 產生SQL指令
 * </pre>
 * 
 * @since 2013/2/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/2/25,Bang,new
 *          </ul>
 */
public class CreateDeleteSQL extends BaseAction {
	private Logger logger = LoggerFactory.getLogger(CreateDeleteSQL.class);
	private String schema = "";
	private String logsDirPath = "";
	private String loadDB2DataPath = "";// load_db2 下data目錄所在位置路徑
	private String dbType = "";
	private PrintWriter logsCs = null;// 輸出log

	/**
	 * 初始化必要資訊及執行產生SQL指令動作
	 * 
	 * @param dbType
	 *            String:連結的DB
	 * @param schema
	 *            String:連結的 schema
	 */
	public List<String> genSql(String schema) {
		if (StringUtils.isBlank(schema)) {
			String errmsg = "讀取系統名稱錯誤,未指定要執行的系統名稱,請重新確認!";
			this.logger.error(errmsg);
			throw new DCException(errmsg);
		}
		this.schema = schema; // Ex:LMS

		this.logger.info("正在初始化 DeleteSQL 必要資訊...");

		if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(schema)) {
			// LMS輸出log
			this.logsDirPath = this.configData.getLmsLogsDirPath();// User當前工作目錄\log\logs\執行日期\LMS
			// User當前工作目錄\load_db2\執行日期\LMS\data
			this.loadDB2DataPath = this.configData.getLmsloadDB2DirPath()
					+ this.configData.getDataPath();
		} else {
			// CLS輸出log
			this.logsDirPath = this.configData.getClsLogsDirPath();// User當前工作目錄\log\logs\執行日期\CLS
			// User當前工作目錄\load_db2\執行日期\CLS\data
			this.loadDB2DataPath = this.configData.getClsloadDB2DirPath()
					+ this.configData.getDataPath();
		}
		List<String> sqlList = null;
		try {
			String createDelSQLLogPath = this.logsDirPath + File.separator
					+ "createDeleteSQL.sql";
			this.logsCs = new PrintWriter(new BufferedWriter(
					new OutputStreamWriter(new FileOutputStream(new File(
							createDelSQLLogPath)))), true);

			sqlList = this.run();
			this.logger.info(" DeleteSQL 執行結束...\n");
		} catch (Exception e) {
			this.logger.error("DeleteSQL 時產生錯誤...", e);
			throw new DCException("DeleteSQL 時產生錯誤...", e);
		}
		return sqlList;
	}

	/**
	 * 主要執行程式
	 */
	public List<String> run() {
		long tt1 = System.currentTimeMillis();
		List<String> dataList = new ArrayList<String>();
		try {
			Collection<File> files = getLoadDb2DirList(this.loadDB2DataPath);

			for (File file : files) {
				System.out.println("file.getName=" + file.getName());
				String table = file.getName();
				int firstIdx = table.indexOf(".");
				int lastIdx = table.lastIndexOf(".");
				String tableName = table.substring(firstIdx + 1, lastIdx);
				if (tableName.equals("L120M01A")
						|| tableName.equals("L140M01A")
						|| tableName.equals("L230S01A")) {
					dataList = this.sqlGenerate(file, dataList, tableName);
				}
			}
			BigDecimal bd = new BigDecimal(
					(System.currentTimeMillis() - tt1) / 1000 / 60);
			bd.setScale(0, BigDecimal.ROUND_HALF_UP);
		} catch (Exception e) {
			String errmsg = "執行DeleteSQL 之run步驟時產生錯誤!";
			this.logger.error(errmsg, e);
			this.logsCs.println(errmsg);
			e.printStackTrace(this.logsCs);
			throw new DCException(errmsg, e);
		} finally {
			IOUtils.closeQuietly(this.logsCs);
		}
		return dataList;
	}

	/**
	 * 取得load_db2資料夾下準備寫入DB,開頭為"系統名稱.TableName"的文字檔
	 * 
	 * @param loadDb2Path
	 * @return
	 */
	private Collection<File> getLoadDb2DirList(String loadDb2Path) {

		Collection<File> files = FileUtils.listFiles(new File(loadDb2Path),
				FileFilterUtils.prefixFileFilter(this.schema,
						IOCase.INSENSITIVE), FileFilterUtils.suffixFileFilter(
						TextDefine.ATTACH_TXT, IOCase.INSENSITIVE));
		return files;
	}

	protected List<String> sqlGenerate(File file, List<String> dataList,
			String tablename) {
		try {
			List<String> tmpList = FileUtils.readLines(file);

			for (String tmp : tmpList) {
				String[] data = Util.split(tmp, TextDefine.SYMBOL_SEMICOLON);
				StringBuffer sb = new StringBuffer();
				sb.append("delete from  LMS.").append(tablename)
						.append(" where oid =").append("'").append(data[0])
						.append("';");
				this.logsCs.println(sb.toString());
				dataList.add(sb.toString());
			}
			return dataList;
		} catch (Exception e) {
			String errmsg = "組成SQL指令時產生錯誤...dbType:" + this.dbType
					+ " ,schema :" + this.schema + " ,table :" + tablename
					+ " ,file :" + file.getName();
			throw new DCException(errmsg, e);
		}
	}
}
