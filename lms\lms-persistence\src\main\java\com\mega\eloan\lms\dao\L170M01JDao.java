/* 
 * L170M01JDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import com.mega.eloan.lms.model.L170M01J;
import tw.com.iisi.cap.dao.IGenericDao;

import java.util.List;

/** 覆審考評表明細檔 **/
public interface L170M01JDao extends IGenericDao<L170M01J> {

	L170M01J findByOid(String oid);
	
	List<L170M01J> findByMainId(String mainId);

	List<L170M01J> findByIndex02(String mainId, String itemType, String itemName);
}