var thisOid = "";
initDfd.done(function(){
	var CLS1205S02aForm = $("#CLS1205S02aForm");
	//J-102-0198修改海外個人可自行輸入評等
    CLS1205S02aForm.find("input[name=crdType]").click(function(){
		//var value = $("[name=crdType]:checked").val();
    	var value = $(this).val();    	
		var $grade = CLS1205S02aForm.find("#grade");		
		if (value == "CK" ){
			$grade.show();
		} else {
			$grade.hide();
			$grade.val("");
		}
	});
    // 配偶引進客戶資料
    $("#formMCustId").find("#getCouple").click(function(i){
        //綁入MegaID
        CommonAPI.openQueryBox({
            defaultValue: $("#formMCustId").find("#mCustId2").val(), //預設值 
            divId: "divMdata", //在哪個div 底下 
            autoResponse: { // 是否自動回填資訊 
                id: "mCustId2", // 統一編號欄位ID 
                dupno: "mDupNo2" // 重覆編號欄位ID 
            }
        });
    });
    
    $("#keyMan").click(function(){
        if ($(this).attr("checked") == true) {
            $(this).val("Y");
            $("#custRltShow").val("");
            $("#custRlt").html("");
            $("#custPos option:eq(0)").attr("selected", true);
            $("#chk_radio1").hide();
        }
        else {
            $(this).val("N");
            if ($("#custRlt").html() != "" && $("#custPos option:selected").val() != "") {
                $("[name='custRlt_content'] option").each(function(i){
                    if ($(this).val() == $("#custRlt").html()) {
                        $("#custRltShow").val($(this).text());
                    }
                });
                $("#chk_radio1").show();
            }
            else {
                $("#custRlt").html("");
                $("#custPos option:eq(0)").attr("selected", true);
                $("#chk_radio1").show();
            }
        }
    });
    $("#custRlt_main").change(function(){
        if ($("#custRlt_main option:selected").val() == "1") {
            //企業關係人
            $("#relation").empty();
            $("#trRel").show();
            $("#relation").append($("#custRlt_main option:selected").text());
            $(".company").show();
            $(".family").hide();
            $(".other").hide();
        }
        else 
            if ($("#custRlt_main option:selected").val() == "2") {
                //親屬關係
                $("#relation").empty();
                $("#trRel").show();
                $("#relation").append($("#custRlt_main option:selected").text());
                $(".company").hide();
                $(".family").show();
                $(".other").hide();
            }
            else 
                if ($("#custRlt_main option:selected").val() == "3") {
                    //其他綜合關係
                    $("#relation").empty();
                    $("#trRel").show();
                    $("#relation").append($("#custRlt_main option:selected").text());
                    $(".company").hide();
                    $(".family").hide();
                    $(".other").show();
                }
                else {
                    //請選擇
                    $("#relation").empty();
                    $("#trRel").hide();
                    $(".company").hide();
                    $(".family").hide();
                    $(".other").hide();
                }
    });
    var coupleGrid = $("#coupleGrid").iGrid({ //借款人基本資料GridView
        handler: 'lms1205gridhandler',
        height: 350,
        sortname: 'keyMan',
        postData: {
            formAction: "queryC120M01A",
            rowNum: 10
        },
        rownumbers: true,
        rowNum: 10,
        //multiselect : true,
        colModel: [{
            colHeader: "&nbsp;", // 主要借款人Flag
            align: "center",
            width: 10, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'keyMan' // col.id
        }, {
            colHeader: i18n.lmss02a["l120s01a.custid"], //身分證統編
            align: "left",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'custId' //col.id
        }, {
            colHeader: i18n.lmss02a["l120s01a.custname2"], //借款人名稱
            align: "left",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            //formatter : 'click',
            //onclick : function,
            name: 'custName' //col.id
        }, {
            colHeader: i18n.lmss02a["l120s01a.custrlt"], //與主要借款人關係
            align: "left",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            //formatter : 'click',
            //onclick : function,
            name: 'custRlt' //col.id
        }, {
            colHeader: i18n.lmss02a["l120s01a.custpos"], //相關身份
            align: "left",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            //formatter : 'click',
            //onclick : function,
            name: 'custPos' //col.id
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
        }
    });
    
    var $C101S01EForm = $("#CLS1205S02eForm");
    $C101S01EForm.find('span.class_l120s01m_queryDate').click(function(){
    	var val = $( this ).val();
    	//alert(">LMSS02APage02.js (5 matches) [90701]  (5 matches) [90701] CC");
    	if(val && val.length==10){
    		$.ajax({
                handler: _handler,
                type: "POST",
                dataType: "json",
                action: "queryL120s01m",
                data: {
                	oid: thisOid,
                    custId : $("#CLS1205S02aForm").find("#custId").val() ,
                	dupNo : $("#CLS1205S02aForm").find("#dupNo").val() ,
                    mainId: responseJSON.mainId
                },
                success: function(json){
                    var $formL120s01m = $("#formL120s01m");
                    
                    //J-107-0087-001 Web e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。			
        			var grpYear = json.formL120s01m.grpYear;
        			var grpGrrd = json.formL120s01m.grpGrade;
        			if(grpYear){
        				//判斷2017以後為新版，之前為舊版
        				if(parseInt(grpYear, 10) >= 2017){
        					var obj = CommonAPI.loadCombos(["GroupGrade2017"]); 
        			        //評等等級
        			        $("#grpGrade").setItems({
        			            item: obj.GroupGrade2017,
        			            format: "{key}"
        			        });
        	
        				}else{
        					var obj = CommonAPI.loadCombos(["GroupGrade"]);
        			        //評等等級
        			        $("#grpGrade").setItems({
        			            item: obj.GroupGrade,
        			            format: "{key}"
        			        });
        				}
        	
        			}else{
        				var obj = CommonAPI.loadCombos(["GroupGrade"]);
        		        
        		        //評等等級
        		        $("#grpGrade").setItems({
        		            item: obj.GroupGrade,
        		            format: "{key}"
        		        });

        			}
        			
                    $formL120s01m.setData(json.formL120s01m);
                    $formL120s01m.readOnlyChilds(true);
                    
                    if (json.formL120s01m.dataNotShow_020 == "Y") {
                        $formL120s01m.find("#data_020").hide();
						$formL120s01m.find("#data_022").hide();
//		              J-108-0100_05097_B1001 Web e-Loan企金授信系統「授信信用風險管理」遵循檢核表增列海外當地限額辦法之遵循
		                $formL120s01m.find("#data_023").hide();
                    }
                    else {
                        $formL120s01m.find("#data_020").show();
						//J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
						if($formL120s01m.find("#localGroup").val() == "" ){
							//舊案
					        $formL120s01m.find("#data_022").show();	
						}else{
							$formL120s01m.find("#data_022").hide();
						}
						//J-108-0100_05097_B1001 Web e-Loan企金授信系統「授信信用風險管理」遵循檢核表增列海外當地限額辦法之遵循
		                $formL120s01m.find("#data_023").show();
                    }
                    
                    if (json.formL120s01m.dataNotShow_080 == "Y") {
                        $formL120s01m.find("#data_080").hide();
						$formL120s01m.find("#data_082").hide();
						$formL120s01m.find("#data_083").hide();
                    }
                    else {
                        $formL120s01m.find("#data_080").show();
						//J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
						if($formL120s01m.find("#localGroup").val() == "" ){
							//舊案
					        $formL120s01m.find("#data_082").show();
						}else{
							$formL120s01m.find("#data_082").hide();
						}
						$formL120s01m.find("#data_083").show();
                    }
                    
                    if (json.formL120s01m.dataNotShow_030 == "Y") {
                        $formL120s01m.find("#data_030").hide();
						$formL120s01m.find("#data_032").hide();
						$formL120s01m.find("#data_033").hide();
                    }
                    else {
                        $formL120s01m.find("#data_030").show();
						//J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
						if($formL120s01m.find("#localGroup").val() == "" ){
							//舊案
					        $formL120s01m.find("#data_032").show();
						}else{
							$formL120s01m.find("#data_032").hide();
						}
						$formL120s01m.find("#data_033").show();
                    }
                    
                    if (json.formL120s01m.grpNo == "") {
                        $formL120s01m.find("#grpYear").hide();
                        $formL120s01m.find("#grpGrade").hide();
                    }else{
        				$formL120s01m.find("#grpYear").show();
                        $formL120s01m.find("#grpGrade").show();
        			}
                    
                    if (json.formL120s01m.mbRlt == "1") {
        				$formL120s01m.find("#dataDate_060").show();
                        $formL120s01m.find("#dataDate_070").hide();
                    }
                    else {
                        $formL120s01m.find("#dataDate_060").hide();
        				$formL120s01m.find("#dataDate_070").show();
                    }
                    
                  //G-104-0097-001 Web e-Loan 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。
        			if (json.formL120s01m.hasLocalAmt == "Y") {
        				//J-108-0100_05097_B1001 Web e-Loan企金授信系統「授信信用風險管理」遵循檢核表增列海外當地限額辦法之遵循
                    	if (json.formL120s01m.countryType == "TH") {
                    		 $formL120s01m.find(".showLocal").show();
                    		 $formL120s01m.find(".showLocal_KH").hide();
                             $formL120s01m.find("#localNetValCurr1").val(json.formL120s01m.localNetValCurr);
                    	}else if (json.formL120s01m.countryType == "KH") {
                    		 $formL120s01m.find(".showLocal").hide();
                   		     $formL120s01m.find(".showLocal_KH").show();
                             $formL120s01m.find("#localNetValCurr1_KH").val(json.formL120s01m.localNetValCurr_KH);
                    	}else{
                    		 $formL120s01m.find(".showLocal").hide();
                   		     $formL120s01m.find(".showLocal_KH").hide();
                             $formL120s01m.find("#localNetValCurr1").val('');
                             $formL120s01m.find("#localNetValCurr1_KH").val('');
                    	}
                    	
        				
        			}else{
        				//J-108-0100_05097_B1001 Web e-Loan企金授信系統「授信信用風險管理」遵循檢核表增列海外當地限額辦法之遵循
                        $formL120s01m.find(".showLocal").hide();
                        $formL120s01m.find(".showLocal_KH").hide();
                        $formL120s01m.find("#localNetValCurr1").val("");
                        $formL120s01m.find("#localNetValCurr1_KH").val("");
        			}	
					
					if (json.formL120s01m.dataNotShow_090 == "Y") {
		                $formL120s01m.find("#data_090").hide();
						$formL120s01m.find("#data_092").hide();
						$formL120s01m.find("#showLocalGroup").val("");
		            }
		            else {
		                //J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
						if($formL120s01m.find("#localGroup").val() == "" ){
							//舊案
					        $formL120s01m.find("#data_090").hide();
						    $formL120s01m.find("#data_092").hide();
							$formL120s01m.find("#showLocalGroup").val("");
						}else{
							$formL120s01m.find("#data_090").show();
						    $formL120s01m.find("#data_092").show();
							//無集團不顯示集團代號
							if($formL120s01m.find("#localGroup").val()=="000000000"){
								$formL120s01m.find("#showLocalGroup").val("N.A.");
							}else{
								$formL120s01m.find("#showLocalGroup").val(parseInt($formL120s01m.find("#localGroup").val(),10));
							}	
						}
		            }
						
                    $("#tL120s01m").thickbox({ // 使用選取的內容進行彈窗
                        title: i18n.lmss02a["l120s01m.item26"], // 「授信信用風險管理」遵循檢核
                        width: 965,
                        height: 480,
                        modal: true,
                        i18n: i18n.def,
                        buttons: {
                        
                            "close": function(){
                                $.thickbox.close();                                
                            }
                        }
                    });
                }
            });
    	}
    });
    $C101S01EForm.find("input[name='mbRlt33']").click(function(){
    	var $desc = $("#mbRltDscr33");
    	if ($(this).val() == "2" || $(this).val() == "3") {
    		$desc.hide();
        	$desc.val("");
        }else {
        	$desc.show();
        }
    });
});

function coupleGrid(){
    $("#coupleGrid").jqGrid("setGridParam", {
        postData: {
            formAction: "queryC120M01A",
            rowNum: 10
        },
        search: true
    }).trigger("reloadGrid");
}

function getMCustData(){
    var rDataName = $("input[name='rDataName']:radio:checked").val();
    if (rDataName != "1") {
        //配偶資料非列於本欄，故不需引進配偶資料！
    }
    else {
        var mCustId = $("#mCustId").val();
        if (mCustId == undefined || mCustId == "") {
        
        }
        else {
            $.ajax({
                type: "POST",
                handler: _handler,
                data: {
                    formAction: "setCustData3m",
                    mCustId: mCustId
                },
                success: function(responseData){
                    //alert(JSON.stringify(responseData));
                    $("#CLS1205S02dForm").setData(responseData.CLS1205S02bForm, false);
                }
            });
        }
    }
}

function same(){
    var addr = $("#CLS1205S02aForm").find("#fAddr").val();
    if (addr == undefined || addr == "") {
        //戶籍地址為空!
        CommonAPI.showMessage(i18n.lmss02a["l120s02.alert11"]);
    }
    else {
        $("#CLS1205S02aForm").find("#coAddr").val(addr);
    }
}

function thickBoxCustId(){
    $("#formMCustId").reset();
    $("#thickBoxCustId").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.lmss02a["l120s02.thickbox12"],
        width: 640,
        height: 180,
        align: 'center',
        valign: 'bottom',
        modal: true,
        i18n: i18n.lmss02a,
        buttons: {
            "l120s02.thickbox1": function(showMsg){
                var custId = $("#CLS1205S02aForm").find("#custId").val();
                var dupNo = $("#CLS1205S02aForm").find("#dupNo").val();
                var mCustId = $("#formMCustId").find("#mCustId2").val();
                var mDupNo = $("#formMCustId").find("#mDupNo2").val();
                if (custId == mCustId && dupNo == mDupNo) {
                    CommonAPI.showMessage(i18n.lmss02a["l120s02.alert18"]);
                }
                else {
                    if ($("#formMCustId").valid()) {
                        $.thickbox.close();
                        $.ajax({
                            type: "POST",
                            handler: _handler,
                            data: {
                                formAction: "getCustData3m",
                                mCustId: mCustId,
                                mDupNo: mDupNo
                            },
                            success: function(responseData){
                                //alert(JSON.stringify(responseData));
                                $("#CLS1205S02dForm").setData(responseData.CLS1205S02dForm, false);
                            }
                        });
                    }
                }
            },
            "l120s02.thickbox2": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function thickBoxSel(){
    $("#thickBoxSel").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.lmss02a["l120s02.thickbox6"],
        width: 640,
        height: 280,
        align: 'center',
        valign: 'bottom',
        modal: true,
        i18n: i18n.lmss02a,
        buttons: {
            "l120s02.thickbox1": function(showMsg){
                //sCoAddr
                $.ajax({
                    type: "POST",
                    handler: _handler,
                    data: {
                        formAction: "setCustData3",
                        sCoAddr: $("#sCoAddr option:selected").val(),
                        sMComTel: $("#sMComTel option:selected").val(),
                        sMTel: $("#sMTel option:selected").val(),
                        sEmail: $("#sEmail option:selected").val()
                    },
                    success: function(responseData){
                        //alert(JSON.stringify(responseData));
                        $("#CLS1205S02aForm").setData(responseData.CLS1205S02aForm, false);
                        $("#CLS1205S02bForm").setData(responseData.CLS1205S02bForm, false);
                    }
                });
                $.thickbox.close();
            },
            "l120s02.thickbox2": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function infor(k){
    if (k == 2) {
        $("#infor").show();
        $("#rDataName3").hide();
        $("#CLS1205S02dForm").find("#sMCustId").html("");
        $("#CLS1205S02dForm").find("#sMName").html("");
    }
    else 
        if (k == 3) {
            $("#infor").hide();
            selCoupleGrid();
        //$("#rDataName3").show();
        }
        else 
            if (k == 1) {
                $("#infor").hide();
                $("#rDataName3").hide();
                $("#CLS1205S02dForm").find("#sMCustId").html("");
                $("#CLS1205S02dForm").find("#sMName").html("");
            }
            else {
                $("#infor").hide();
                $("#rDataName3").hide();
                $("#CLS1205S02dForm").find("#sMCustId").html("");
                $("#CLS1205S02dForm").find("#sMName").html("");
            }
}

function selCoupleGrid(){
    coupleGrid();
    $("#thickBoxCouple").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.lmss02a["l120s02.thickbox10"],
        width: 640,
        height: 480,
        modal: true,
        align: 'center',
        valign: 'bottom',
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var row = $("#coupleGrid").getGridParam('selrow');
                var list = "";
                var data = $("#coupleGrid").getRowData(row);
                list = data.oid;
                list = (list == undefined ? "" : list);
                if (list != "") {
                    $.ajax({
                        type: "POST",
                        handler: _handler,
                        data: {
                            formAction: "saveCouple",
                            mainId: responseJSON.mainId,
                            oid: list,
                            thisOid: thisOid
                        },
                        success: function(responseData){
                            $("#CLS1205S02dForm").find("#sMCustId").html(DOMPurify.sanitize(responseData.sMCustId));
                            $("#CLS1205S02dForm").find("#sMName").html(DOMPurify.sanitize(responseData.sMName));
                            $("#CLS1205S02dForm").find("#rDataName3").show();
                            $.thickbox.close();
                            $.thickbox.close();
                            CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
                        }
                    });
                }
                else {
                    CommonAPI.showMessage(i18n.lmss02a["l120s02.alert1"]);
                    return;
                }
            },
            "close": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        /*
                         $("#CLS1205S02dForm").find("#sMCustId").html("");
                         $("#CLS1205S02dForm").find("#sMName").html("");
                         $("#CLS1205S02dForm").find("#rDataName3").hide();
                         */
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function getRlt2(){
    CommonAPI.confirmMessage(i18n.lmss02a["l120s02.confirm3"], function(b){
        if (b) {
            $.ajax({
                type: "POST",
                handler: _handler,
                data: {
                    formAction: "getRlt2",
                    oid: thisOid
                },
                success: function(responseData){
                    //alert(JSON.stringify(responseData.CLS1205S02eForm));
                    $("#CLS1205S02eForm").setData(responseData.CLS1205S02eForm, false);
                    if (responseData.noData != null &&
                    responseData.noData != undefined &&
                    responseData.noData != "") {
                        CommonAPI.showErrorMessage(i18n.lmss02a('l120s02.alert13', {
                            'colName': responseData.noData
                        }));
                    }
                }
            });
        }
        else {
            CommonAPI.showMessage(i18n.lmss02a["l120s02.alert10"]);
        }
    });
}

function getSel(){
    CommonAPI.confirmMessage(i18n.lmss02a["l120s02.confirm3"], function(b){
        if (b) {
            $.ajax({
                type: "POST",
                handler: _handler,
                data: {
                    formAction: "getSel",
                    oid: thisOid
                },
                success: function(responseData){
                    //alert(JSON.stringify(responseData.CLS1205S02eForm));
                    $("#CLS1205S02eForm").setData(responseData.CLS1205S02eForm, false);
                }
            });
        }
        else {
            CommonAPI.showMessage(i18n.lmss02a["l120s02.alert10"]);
        }
    });
}

function applyCreditRisk(needAsk){	
	 /*
     * 對於 19110101AB 這類的 id,可能查不到 or 不準
     * 以 user 在畫面上輸入的為主
     */
    var mbRlt = $("input[name='isQdata2']:radio:checked").val();
    var mhRlt44 = $("input[name='isQdata3']:radio:checked").val();
    var mhRlt45 = $("input[name='isQdata16']:radio:checked").val();
    //---
    var custId = $("#CLS1205S02aForm").find("#custId").val();
    var dupNo = $("#CLS1205S02aForm").find("#dupNo").val();
    var busCode = $("#CLS1205S02aForm").find("#busCode").val();
    var custName = $("#CLS1205S02aForm").find("#custName").val();
    
    $.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "prepareImportL120S01m",
            'custId' : custId ,
        	'dupNo' : dupNo,
        	'mbRlt' : mbRlt,
        	'mhRlt44' : mhRlt44,
        	'mhRlt45' : mhRlt45,
        	'busCode' : busCode,
        	'custName' : custName
        },
        success: function(json_prepare){
        	proc_needAsk(needAsk).done(function(){
        		//===============
        		$.ajax({
                    handler: _handler,
                    type: "POST",
                    dataType: "json",
                    data: {
                        formAction: "deleteL120s01m",
                        custId : custId ,
                    	dupNo : dupNo ,
                        mainId: responseJSON.mainid
                    },
                    success: function(){
                    	//===============
                    	importL120S01m(needAsk, json_prepare);    	
                    }
                });
            		
        	});        	
        }
    })
}

function proc_needAsk(needAsk){
	var my_dfd = $.Deferred();
	if(needAsk){
		var $C101S01EForm = $("#CLS1205S02eForm");
	    var val = $C101S01EForm.find('span.class_l120s01m_queryDate').val();
		if(val && val.length==10){
			CommonAPI.confirmMessage(i18n.lmss02a["l120s01m.confirm1"], function(b){
	            if (b) {
	            	my_dfd.resolve();
	            }else{
	            	my_dfd.reject();
	            }
	        });	
		}else{
			my_dfd.resolve();	
		}		
	}else{
		my_dfd.resolve();
	}
	return my_dfd.promise();
}

/**
 * 執行產生往來實績彙總表
 */
function importL120S01m(needAsk, json_prepare){    
   
    $.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data:$.extend({
        	formAction: "importL120s01m",
            mainId: responseJSON.mainid
            }, 
            json_prepare
        ),	
        success: function(json_importL120s01m){
        	//=========
        	$.ajax({
                handler: _handler,
                type: "POST",
                dataType: "json",
                data:$.extend({
                	formAction: "queryL120S01m_date",
                    mainId: responseJSON.mainid
                    }, 
                    json_prepare
                ),	
                success: function(json_queryL120S01m_date){
                	$("#l120s01m_queryDate").html(DOMPurify.sanitize(json_queryL120S01m_date.l120s01m_queryDate));
                	
                    if (needAsk == true) {
                        //執行成功
                        CommonAPI.showMessage(i18n.lmss02a["l120s01m.message1"]);
                    }            
                }
            });           
        }
    });
}

function showDetail(relType,hasLocal){

	var custId = $("#CLS1205S02aForm").find("#custId").val();
    var dupNo = $("#CLS1205S02aForm").find("#dupNo").val();
    
    // 進行查詢 
    $.ajax({ //查詢主要借款人資料
        handler: _handler,
        type: "POST",
        dataType: "json",
        action: "queryL120s01o",
        data: {
            custId: custId,
            dupNo: dupNo,
            relType: relType,
            mainId: responseJSON.mainId,
			hasLocal:hasLocal
        },
        success: function(json){
            var $formL120s01o = $("#formL120s01o");
            $formL120s01o.find("#showDetailHtml").html(DOMPurify.sanitize(json.showDetailResult));
            
			//G-104-0097-001 Web e-Loan 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。						
			//海外_編製中("01O"),	
			//海外_待補件("07O"), 	
			//會簽後修改編製中("01K"),	
            var buttons = {};
			if(responseJSON.mainDocStatus =="01O" || responseJSON.mainDocStatus =="07O"  || responseJSON.mainDocStatus =="01K"  ){
				//$formL120s01o.readOnlyChilds(false);   =>不可以設為false 因為有其他不需要開放編輯的input欄位
				
				if (hasLocal == "Y") {
				     buttons["sure"] = function(){
					    $.ajax({  	    
		                    type: "POST",
							dataType: "json",
		                    handler: _handler,
		                    data: {
		                        formAction: "resetLocalL120S01OAndReCaculate",
		                        custId: custId,
					            dupNo: dupNo,
					            relType: relType,
					            mainId: responseJSON.mainId,
								FORML120S01O: JSON.stringify($formL120s01o.serializeData())
		                    },
		                    success: function(json){
		                        $.thickbox.close();
		                        $.thickbox.close(); 
		                    }
		                });
					};
				}
				
			}else{
				$formL120s01o.readOnlyChilds(true);
			}
			
			buttons ["cancel" ] = function(){
                 $. thickbox.close ();
            };
			
            $("#tL120s01o").thickbox({ // 使用選取的內容進行彈窗
                title: i18n.lmss02a["l120s01m.item26"], // 「授信信用風險管理」遵循檢核
                width: 965,
                height: 480,
                modal: true,
                i18n: i18n.def,
                buttons: buttons
            });
        }
    });    
}

function printCreditRisk(){
    //列印信用風險管理遵循
	var custId = $("#CLS1205S02aForm").find("#custId").val();
    var dupNo = $("#CLS1205S02aForm").find("#dupNo").val();
    
    var $C101S01EForm = $("#CLS1205S02eForm");
    var val = $C101S01EForm.find('span.class_l120s01m_queryDate').val();
	if(val && val.length==10){
		 $.form.submit({
		        url: "../../simple/FileProcessingService",
		        target: "_blank",
		        data: {
		            rptOid: "R30" + "^" + "",
		            mainId: responseJSON.mainId,
		            mainOid: $("#mainOid").val(),
		            fileDownloadName: "lms1205r30.pdf",
		            serviceName: "lms1205r01rptservice",
		            custId: custId,
		            dupNo: dupNo,
		            mode: "ONE"
		        }
		    });	
	}else{
        //EFD0002=INFO|報表無資料|
        return CommonAPI.showErrorMessage(i18n.msg["EFD0002"]);
    }
}

/**
 * 開啟借款人資料ThickBox內容(個金)
 * @param oid
 */
function openDocAddBorrow(oid){
    var $CLS1205S02eForm = $("#CLS1205S02eForm");
    //此方法為點選新增或修改借款人資料時所開啟的ThickBox內容
    thisOid = oid;
    var couple = $("#CLS1205S02dForm").find("input[name='rDataName']:checked").val();
    var cjob = $("#CLS1205S02dForm").find("#mJobKind option:selected").val();
    /*
     if(couple == "0"){
     infor(1);
     }else if(couple == "1"){
     infor(2);
     }else{
     infor(3);
     }
     */
    if (cjob == "99") {
        $("#mJobOther").show();
    }
    else {
        $("#mJobOther").hide();
        $("#mJobOther").val("");
    }
    //每次開啟都是第一頁
    $("#tabs-x").tabs({
        selected: 0
    });
    $("#openDocaddborrow").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.lmss02a["l120s02.thickbox6"],
        width: 960,
        height: 500,
        modal: true,
        i18n: i18n.def,
        buttons: {
            "saveData": function(showMsg){
                saveBorrow(thisOid, showMsg);
            },
            "del": function(){
                CommonAPI.confirmMessage(i18n.lmss02a["l120s02.confirm1"], function(b){
                    if (b) {
                        $.thickbox.close();
                        //是的function
                        $.ajax({
                            type: "POST",
                            handler: _handler,
                            data: {
                                formAction: "deleteBorrowMain2",
                                mainId: responseJSON.mainId,
                                oid: oid
                            },
                            success: function(responseData){
                                $("#showBorrowData").setData(responseData.showBorrowData, false);
                                //更新借款人基本資料Grid內容
                                $("#l120s01agrid").trigger("reloadGrid");
                                //更新授信簽報書Grid內容
                                CommonAPI.triggerOpener("gridview", "reloadGrid");
                            }
                        });
                        $.thickbox.close();
                    }
                    else {
                        //否的function
                        CommonAPI.showMessage(i18n.lmss02a["l120s02.alert5"]);
                    }
                })
            },
            "close": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * 檢查是否為正常的月份
 * @param String
 */
function checkMonth(mm){
    if (mm != "") {
        if (mm > 12 || mm <= 00 || mm <= 0) {
            CommonAPI.showMessage(i18n.lmss02a["l120s02.alert6"]);
            return false;
        }
        else {
            return true;
        }
    }
    else {
        return true;
    }
}

/**
 * 儲存主要借款人(含修改)
 */
function saveBorrow(oid, showMsg){
    var $CLS1205S02aForm = $("#CLS1205S02aForm");
    var $CLS1205S02bForm = $("#CLS1205S02bForm");
    var $CLS1205S02cForm = $("#CLS1205S02cForm");
    var $CLS1205S02dForm = $("#CLS1205S02dForm");
    var $CLS1205S02eForm = $("#CLS1205S02eForm");
    
    $CLS1205S02eForm.valid();
    $CLS1205S02aForm.find(".number").each(function(i){
        var $this = $(this);
        $this.val(RemoveStringComma($this.val()));
    });
    $CLS1205S02bForm.find(".number").each(function(j){
        var $this = $(this);
        $this.val(RemoveStringComma($this.val()));
    });
    $CLS1205S02cForm.find(".number").each(function(k){
        var $this = $(this);
        $this.val(RemoveStringComma($this.val()));
    });
    $CLS1205S02dForm.find(".number").each(function(l){
        var $this = $(this);
        $this.val(RemoveStringComma($this.val()));
    });
    FormAction.open = true;
    
    var rDataName = $("input[name='rDataName']:radio:checked").val();
    if (rDataName != "1") {
        $CLS1205S02dForm.reset();
        $CLS1205S02dForm.find("input[name='rDataName']:radio").each(function(i){
            var $this = $(this);
            if (rDataName == $this.val()) {
                $this.attr("checked", true);
            }
        });
    }
    
    if (rDataName == "2" && $CLS1205S02aForm.find("#_renCd").val() != "C") {
        // 同本案借保人且非公司戶(法人)
        var sMCustId = $("#CLS1205S02dForm").find("#sMCustId").html();
        if (Trim(sMCustId) != null && Trim(sMCustId) != undefined && Trim(sMCustId) != "") {
        }
        else {
            // 跳出錯誤訊息 l120s02.alert28 配偶資料同本案借保人不得為空
            CommonAPI.showErrorMessage(i18n.lmss02a('l120s02.alert28'));
            return;
        }
    }
    
    //提示使用者尚未填妥項目
    if (!$CLS1205S02aForm.valid()) {
        return false;
    }
    if (!$CLS1205S02bForm.valid()) {
        return false;
    }
    if (!$CLS1205S02cForm.valid()) {
        return false;
    }
    if (!$CLS1205S02dForm.valid()) {
        return false;
    }
    if (!$CLS1205S02eForm.valid()) {
        return false;
    }
    
    var errorMsg = "";
    var errorMsgA = "";
    var errorMsgB = "";
    var errorMsgH = "";
    var eChkFlag = $CLS1205S02eForm.find("input[name='eChkFlag']:radio:checked").val();
    var isQdata9 = $CLS1205S02eForm.find("input[name='isQdata9']:radio:checked").val();
    var isQdata10 = $CLS1205S02eForm.find("input[name='isQdata10']:radio:checked").val();
    var eJcicFlag = $CLS1205S02eForm.find("input[name='eJcicFlag']:radio:checked").val();
    var isQdata4 = $CLS1205S02eForm.find("input[name='isQdata4']:radio:checked").val();
    var isQdata14 = $CLS1205S02eForm.find("input[name='isQdata14']:radio:checked").val();
    var isQdata11 = $CLS1205S02eForm.find("input[name='isQdata11']:radio:checked").val();
    var isQdata13 = $CLS1205S02eForm.find("input[name='isQdata13']:radio:checked").val();
    
    var eChkDDate = $CLS1205S02eForm.find("#eChkDDate").val();
    var eChkQDate = $CLS1205S02eForm.find("#eChkQDate").val();
    var eJcicDDate = $CLS1205S02eForm.find("#eJcicDDate").val();
    var eJcicQDate = $CLS1205S02eForm.find("#eJcicQDate").val();
    
    if (eChkFlag == "1" || eChkFlag == "2") {
        if (eChkFlag == "1") {
            //票信退補紀錄勾選"有"
            if (isQdata9 != "1" && isQdata10 != "1") {
                //「退票紀錄」或「拒絕往來紀錄」其一必須為「有」。
                errorMsgA += i18n.lmss02a["l120s02.alert22"];
            }
        }
        else 
            if (eChkFlag == "2") {
                //票信退補紀錄勾選"無"
                if (isQdata9 == "2" && isQdata10 == "2") {
                // 正常情況
                }
                else {
                    //「退票紀錄」及「拒絕往來紀錄」必須皆為「無」。
                    errorMsgA += i18n.lmss02a["l120s02.alert23"];
                }
            }
        if (isQdata9 == "3") {
            //退票紀錄不能為N.A
            errorMsg += i18n.lmss02a["L120S01L.isQdata9"] + ",";
        }
        if (isQdata10 == "3") {
            //拒絕往來紀錄不能為N.A
            errorMsg += i18n.lmss02a["L120S01L.isQdata10"] + ",";
        }
    }
    
    if (isQdata9 != "3" || isQdata10 != "3") {
        if (eChkFlag == "3") {
            // ◎票信退補紀錄不能為N.A
            errorMsg += i18n.lmss02a["l120s02.index59"] + ",";
        }
    }
    
    if (eJcicFlag == "1" || eJcicFlag == "2") {
        if (eJcicFlag == "1") {
            //聯徵逾催呆紀錄勾選"有"
            if (isQdata4 == "2" && isQdata14 == "2" && isQdata11 == "2" && isQdata13 == "2") {
                //「歸戶(本行餘額為a-Loan資料、他行餘額為聯徵資料)」、「近一年內不含查詢當日非Z費被聯行查詢紀錄明細」、
                // 「主債務逾期、催收、呆帳紀錄」或「信用卡強停紀錄」其一必須為「有」。
                errorMsgB += i18n.lmss02a["l120s02.alert24"];
            }
        }
        else 
            if (eJcicFlag == "2") {
                //聯徵逾催呆紀錄勾選"無"
                if (isQdata4 == "2" && isQdata14 == "2" && isQdata11 == "2" && isQdata13 == "2") {
                // 正常情況
                }
                else {
                    //「歸戶(本行餘額為a-Loan資料、他行餘額為聯徵資料)」、「近一年內不含查詢當日非Z費被聯行查詢紀錄明細」、
                    // 「主債務逾期、催收、呆帳紀錄」或「信用卡強停紀錄」必須皆為「無」。
                    errorMsgB += i18n.lmss02a["l120s02.alert25"];
                }
            }
        if (isQdata4 == "3") {
            //歸戶(本行餘額為a-Loan資料、他行餘額為聯徵資料不能為N.A
            errorMsg += i18n.lmss02a["L120S01L.isQdata4"] + ",";
        }
        if (isQdata14 == "3") {
            //近一年內不含查詢當日非Z類被聯行查詢紀錄明細不能為N.A
            errorMsg += i18n.lmss02a["L120S01L.isQdata14"] + ",";
        }
        if (isQdata11 == "3") {
            //主債務逾期、催收、呆帳紀錄不能為N.A
            errorMsg += i18n.lmss02a["L120S01L.isQdata11"] + ",";
        }
        if (isQdata13 == "3") {
            //信用卡強停紀錄不能為N.A
            errorMsg += i18n.lmss02a["L120S01L.isQdata13"] + ",";
        }
    }
    
    if (isQdata4 != "3" || isQdata14 != "3" || isQdata11 != "3" || isQdata13 != "3") {
        if (eJcicFlag == "3") {
            // ◎聯徵逾催呆紀錄不能為N.A
            errorMsg += i18n.lmss02a["l120s02.index60"] + ",";
        }
    }
    
    /*
     if(errorMsg != null && errorMsg != undefined && errorMsg != ""){
     CommonAPI.showErrorMessage(i18n.lmss02a('l120s02.alert19', {
     'colName': errorMsg
     }));
     return;
     }
     */
    if ((eChkDDate != null && eChkDDate != undefined && eChkDDate != "") &&
    (eChkQDate != null && eChkQDate != undefined && eChkQDate != "")) {
        if (eChkQDate < eChkDDate) {
            // 票信查詢日期不能小於截止日期! l120s02.index61
            CommonAPI.showErrorMessage(i18n.lmss02a('l120s02.alert20', {
                'colName': i18n.lmss02a('l120s02.index61')
            }));
            return;
        }
    }
    if ((eJcicDDate != null && eJcicDDate != undefined && eJcicDDate != "") &&
    (eJcicQDate != null && eJcicQDate != undefined && eJcicQDate != "")) {
        if (eJcicQDate < eJcicDDate) {
            // 聯徵查詢日期不能小於截止日期! l120s02.index62
            CommonAPI.showErrorMessage(i18n.lmss02a('l120s02.alert20', {
                'colName': i18n.lmss02a('l120s02.index62')
            }));
            return;
        }
    }
    
    var busCode = $CLS1205S02aForm.find("#busCode").val();
    var custClass = $CLS1205S02aForm.find("#custClass").val();
    if ((busCode == undefined || busCode == null || busCode == "") || (custClass == undefined || custClass == null || custClass == "")) {
        // l120s02.alert29=尚未引進行業對象別/客戶類別！
        
        errorMsgH += i18n.lmss02a["l120s02.alert29"];
        /*     
         return CommonAPI.showMessage(i18n.lmss02b["l120s02.alert29"]);
         */     
    }
    
    //儲存主要借款人(含修改)
    $.ajax({
        type: "POST",
        handler: _handler,
        data: {
            formAction: "saveBorrow2",
            oid: oid,
            CLS1205S02aForm: JSON.stringify($CLS1205S02aForm.serializeData()),
            CLS1205S02bForm: JSON.stringify($CLS1205S02bForm.serializeData()),
            CLS1205S02cForm: JSON.stringify($CLS1205S02cForm.serializeData()),
            CLS1205S02dForm: JSON.stringify($CLS1205S02dForm.serializeData()),
            CLS1205S02eForm: JSON.stringify($CLS1205S02eForm.serializeData()),
            custName: $CLS1205S02aForm.find("#custName").html(),
            keyMan: $CLS1205S02aForm.find("#keyMan").val(),
            errorMsg2: errorMsg,
            errorMsgA: errorMsgA,
            errorMsgB: errorMsgB,
			errorMsgH: errorMsgH,
            eChkDDate: eChkDDate,
            eChkQDate: eChkQDate,
            eJcicDDate: eJcicDDate,
            eJcicQDate: eJcicQDate,
            eChkFlag: eChkFlag,
            eJcicFlag: eJcicFlag,
            showMsg: showMsg
        },
        success: function(responseData){
            FormAction.open = false;
            //alert(JSON.stringify(responseData));
            if (responseData.keyMan == "Y") {
                //為主要借款人
                $("#showBorrowData").setData(responseData.showBorrowData, false);
                $CLS1205S02aForm.find("#keyMan").attr("checked", true);
                $("#chk_radio1").hide();
                $CLS1205S02aForm.find("#custRlt").html("");
                $CLS1205S02aForm.find("#custPos option:eq(0)").attr("selected", true);
            }
            else {
                //為非主要借款人
                $("#showBorrowData").setData(responseData.showBorrowData, false);
                $CLS1205S02aForm.find("#keyMan").attr("checked", false);
                $("#chk_radio1").show();
            }
            //更新Grid內容
            $("#l120s01agrid").trigger("reloadGrid");
            if (!responseData.ExistKeyMan) {
                if (responseData.errorMsg != null && responseData.errorMsg != undefined && responseData.errorMsg != "") {
                    CommonAPI.showErrorMessage(responseData.errorMsg);
                }
                //更新授信簽報書Grid內容
                CommonAPI.triggerOpener("gridview", "reloadGrid");
            }
            else {
                var check;
                CommonAPI.confirmMessage(i18n.lmss02a('l120s02.confirm2', {
                    'borrower': responseData.oldBorrower
                }), function(b){
                    if (b) {
                        //是的function
                        check = true;
                    }
                    else {
                        //否的function
                        //User 決定不覆蓋主要借款人
                        check = false;
                    }
                    $.ajax({
                        //依照使用者選擇決定執行覆蓋主要借款人動作
                        type: "POST",
                        handler: _handler,
                        data: {
                            formAction: "modifyBorrow2",
                            mainOid: responseData.haveKeyManDocNo,
                            docOid: responseData.haveKeyManOid,
                            oldOid: responseData.needtoAddOid,
                            errorMsg: responseData.errorMsg,
                            errorMsg2: responseData.errorMsg2,
                            errorMsg3: responseData.errorMsg3,
                            errorMsgA: responseData.errorMsgA,
                            errorMsgB: responseData.errorMsgB,
                            check: check,
                            showMsg: showMsg
                        },
                        success: function(responseData){
                            //alert(check);
                            //更新Grid內容
                            $("#l120s01agrid").trigger("reloadGrid");
                            if (responseData.check) {
                                //要覆蓋主要借款人
                                $("#showBorrowData").setData(responseData.showBorrowData, false);
                                $CLS1205S02aForm.find("#keyMan").attr("checked", true);
                                $("#chk_radio1").hide();
                                $CLS1205S02aForm.find("#custRlt").html("");
                                $CLS1205S02aForm.find("#custPos option:eq(0)").attr("selected", true);
                                if (responseData.errorMsg != null && responseData.errorMsg != undefined && responseData.errorMsg != "") {
                                    CommonAPI.showErrorMessage(responseData.errorMsg);
                                }
                                else {
                                    //Show 出儲存成功訊息
                                    responseData.SaveSuccess;
                                }
                                //更新授信簽報書Grid內容
                                CommonAPI.triggerOpener("gridview", "reloadGrid");
                                //更新Grid內容
                                $("#l120s01agrid").trigger("reloadGrid");
                            }
                            else 
                                if (!responseData.check) {
                                    //不覆蓋主要借款人
                                    $CLS1205S02aForm.find("#keyMan").attr("checked", false);
                                    //為非主要借款人
                                    $CLS1205S02aForm.find("#keyMan").val("N");
                                    $("#custRltShow").val("");
                                    $("#custRlt").html("");
                                    $("#custPos option:eq(0)").attr("selected", true);
                                    $("#chk_radio1").show();
                                    if (responseData.errorMsg != null && responseData.errorMsg != undefined && responseData.errorMsg != "") {
                                        CommonAPI.showErrorMessage(responseData.errorMsg);
                                    }
                                    else {
                                        //Show 出儲存成功訊息
                                        responseData.SaveSuccess;
                                    }
                                    //更新授信簽報書Grid內容
                                    CommonAPI.triggerOpener("gridview", "reloadGrid");
                                    //更新Grid內容
                                    $("#l120s01agrid").trigger("reloadGrid");
                                }
                        }
                    });
                });
            }
        }
    });
}

/**
 * 主要借款人關係選擇ThickBox
 */
function openCustRlt(){
    //alert(JSON.stringify(responseJSON));
    var $custRltTd = $("#custRltTd");
    //初始化借款人標題
    $("#relation").empty();
    $("#trRel").hide();
    $("#custRlt_main option:eq(0)").attr("selected", true);
    $custRltTd.find("#custRlt_content1 option:eq(0)").attr("selected", true);
    $custRltTd.find("#custRlt_content2 option:eq(0)").attr("selected", true);
    $custRltTd.find("#custRlt_content3 option:eq(0)").attr("selected", true);
    $custRltTd.find("#custRlt_content4 option:eq(0)").attr("selected", true);
    $custRltTd.find("#custRlt_content1").hide();
    $custRltTd.find("#custRlt_content2").hide();
    $custRltTd.find("#custRlt_content3").hide();
    $custRltTd.find("#custRlt_content4").hide();
    //查詢與主要借款人關係
    $.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "queryCustRlt2",
            thisOid: thisOid,
            isOther: false
        },
        success: function(json){
            if (json.isOther) {
                if (json.isNull) {
                    //如果為空則初始化選項
                    $("#custRlt_main option:eq(0)").attr("selected", true);
                    $custRltTd.find("#custRlt_content3").hide();
                    $custRltTd.find("#custRlt_content4").hide();
                    $("#trRel").hide();
                }
                else {
                    //其他綜合關係
                    $("#custRlt_main option:eq(3)").attr("selected", true);
                    $("#trRel").show();
                    $("#relation").append($("#custRlt_main option:selected").text());
                    $custRltTd.find("#custRlt_content3 option").each(function(j){
                        var $this = $(this);
                        if ($this.attr("value") == json.custRlt_content1) {
                            $this.attr("selected", true);
                            $this.parents().show();
                        }
                    });
                    $custRltTd.find("#custRlt_content4 option").each(function(k){
                        var $this = $(this);
                        if ($this.attr("value") == json.custRlt_content2) {
                            $this.attr("selected", true);
                            $this.parents().show();
                        }
                    });
                }
            }
            else {
                $("[name='custRlt_content'] option").each(function(i){
                    var $this = $(this);
                    //非其他綜合關係
                    if ($this.attr("value") == json.custRlt_content) {
                        switch (json.custRlt_main) {
                            case "1":
                                $("#custRlt_main option:eq(1)").attr("selected", true);
                                $("#trRel").show();
                                $("#relation").append($("#custRlt_main option:selected").text());
                                break;
                            case "2":
                                $("#custRlt_main option:eq(2)").attr("selected", true);
                                $("#trRel").show();
                                $("#relation").append($("#custRlt_main option:selected").text());
                                break;
                            default:
                                $("#trRel").hide();
                                break;
                        }
                        $this.attr("selected", true);
                        $this.parents().show();
                    }
                });
            }
        }
    });
    
    var openCustRlt = $("#thickboxCustRlt").thickbox({
        // 使用選取的內容進行彈窗
        title: i18n.lmss02a["l120s02.thickbox7"],
        width: 640,
        height: 200,
        align: 'center',
        valign: 'bottom',
        modal: false,
        i18n: i18n.lmss02a,
        buttons: {
            "l120s02.thickbox1": function(showMsg){
                if ($("#custRlt_main option:selected").val() == "" ||
                $("#custRltTd :nth-child(" + $('#custRlt_main option:selected').val() +
                ") option:selected").val() ==
                "") {
                    CommonAPI.showMessage(i18n.lmss02a["l120s02.alert7"]);
                }
                else {
                    //初始化關係類別
                    $("#custRltShow").val("");
                    $("#custRlt").html("");
                    if ($("#custRlt_main option:selected").val() == "3") {
                        //設定存取到後端的值
                        $("#custRlt").html($("#custRltTd").find("#custRlt_content3 option:selected").val() +
                        $("#custRltTd").find("#custRlt_content4 option:selected").val());
                        //設定顯示給user看的內容
                        $("#custRltShow").val($("#custRltTd").find("#custRlt_content3 option:selected").val() +
                        $("#custRltTd").find("#custRlt_content4 option:selected").val() +
                        " " +
                        $("#custRltTd").find("#custRlt_content3 option:selected").text() +
                        "-" +
                        $("#custRltTd").find("#custRlt_content4 option:selected").text());
                    }
                    else {
                        //設定存取到後端的值
                        $("#custRlt").html($("#custRltTd :nth-child(" + $('#custRlt_main option:selected').val() +
                        ") option:selected").val());
                        //設定顯示給user看的內容
                        $("#custRltShow").val($("#custRltTd :nth-child(" + $('#custRlt_main option:selected').val() +
                        ") option:selected").val() +
                        " " +
                        $("#custRltTd :nth-child(" + $('#custRlt_main option:selected').val() +
                        ") option:selected").text());
                    }
                    $.thickbox.close();
                }
            },
            "l120s02.thickbox2": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * 取得行業對象別與客戶類別
 */
function getBusCdAndCustClass(){
    var $L120S01aForm = $("#CLS1205S02aForm");
    $.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getBusCdAndCustClass",
            mainId: responseJSON.mainId,
            custId: $L120S01aForm.find("#custId").html(),
            dupNo: $L120S01aForm.find("#dupNo").html()
        },
        success: function(json){
        
            $L120S01aForm.setData(json, false);
        }
    });
}
