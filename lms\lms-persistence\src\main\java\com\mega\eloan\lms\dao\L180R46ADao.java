/* 
 * L180R46ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L180R46A;

/** 共同行銷未結案資訊檔 **/
public interface L180R46ADao extends IGenericDao<L180R46A> {

	L180R46A findByOid(String oid);
	
	List<L180R46A> findByMainId(String mainId);

	List<L180R46A> findByIndex01(String mainId);

	List<L180R46A> findByIndex02(String cntrNo);

	List<L180R46A> findByIndex03(String result, Integer cnt);
	
	List<L180R46A> findByIndex04(String type, String result, Integer cnt);
	
	L180R46A findByCntrNoType(String cntrNo, String type);
}