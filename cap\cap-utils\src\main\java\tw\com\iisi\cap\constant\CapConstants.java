/*
 * CapConstants.java
 *
 * Copyright (c) 2009 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System, Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.constant;

import java.math.BigDecimal;

/**
 * <p>
 * This interface provide common use constants..
 * </p>
 *
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/6/30,iristu,modify
 *          <li>2012/10/11,<PERSON><PERSON><PERSON>,modify LINE_BREAK
 *          </ul>
 */
public interface CapConstants {

    /**
     * Line break.
     */
    public final static String LINE_BREAK = "\r\n"; //$NON-NLS-1$

    /**
     * empty string.
     */
    public final static String EMPTY_STRING = ""; //$NON-NLS-1$

    /**
     * a space string.
     */
    public final static String SPACE = " "; //$NON-NLS-1$

    /**
     * a "0" string.
     */
    public final static String S0 = "0"; //$NON-NLS-1$

    /**
     * a "1" string.
     */
    public final static String S1 = "1"; //$NON-NLS-1$

    /**
     * a "-1" string.
     */
    public final static String S1N = "-1"; //$NON-NLS-1$

    /**
     * a BigDecimal, value is 0.
     */
    public final static BigDecimal B0 = new BigDecimal(0d);

    /**
     * a BigDecimal, value is 1.
     */
    public final static BigDecimal B1 = new BigDecimal(1d);

    /**
     * a BigDecimal, value is -1.
     */
    public final static BigDecimal B1N = new BigDecimal(-1d);

    /**
     * a empty string array.
     */
    public final static String[] EMPTY_ARRAY = new String[0];

    public final static String VALUES_SEPARATOR = "|";

    /**
     * Empty Json
     */
    public final static String EMPTY_JSON = "{}";

    /** The Constant AJAX_HANDLER_TIMEOUT. */
    public final static String AJAX_HANDLER_TIMEOUT = "AJAX_HANDLER_TIMEOUT";

    /** UI端顯示訊息 */
    public final static String AJAX_NOTIFY_MESSAGE = "NOTIFY_MESSAGE";

    /** The Default Charset encoding **/
   	public final static String DEFAULT_CHARSET = "MS950";
   	
   	/** The Default Date format **/
   	public final static String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
}
