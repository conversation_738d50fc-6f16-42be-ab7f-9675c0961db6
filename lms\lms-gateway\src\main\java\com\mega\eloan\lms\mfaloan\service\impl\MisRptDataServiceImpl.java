package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisRptDataService;

@Service
public class MisRptDataServiceImpl extends AbstractMFAloanJdbc implements
		MisRptDataService {
	private static final Logger logger = LoggerFactory
			.getLogger(MisRptDataServiceImpl.class);

	@Override
	public List<Map<String, Object>> getCLS180R01Data(String brno,
			String bgnDate, String endDate) {
		try {
			return getJdbc().queryForListWithMax("MIS.GETCASELIST",
					new String[] { brno, bgnDate, endDate, brno });
		} catch (Exception e) {
			logger.error("[getContent] Exception!!", e.getMessage());
		}
		return null;
	}

	@Override
	public List<Map<String, Object>> getCLS180R11Data(String formId) {
		try {
			return getJdbc().queryForListWithMax("MIS.LNFE0600.QUERY",
					new String[] { formId });
		} catch (Exception e) {
			logger.error("[getContent] Exception!!", e.getMessage());
		}
		return null;
	}

	@Override
	public List<Map<String, Object>> getCLS180R10Data(String brno,
			String bgnDate, String endDate) {
		try {
			return getJdbc().queryForListWithMax("MIS.MISLN20.QUERY",
					new String[] { brno, bgnDate, endDate });
		} catch (Exception e) {
			logger.error("[getContent] Exception!!", e.getMessage());
		}
		return null;
	}

	@Override
	public Map<String, Object> getCustData(String custId, String dupNo) {
		try {
			return getJdbc().queryForMap("MIS.CUSTDATA.FINDCUSTOM",
					new String[] { custId, dupNo });
		} catch (Exception e) {
			logger.error("[getContent] Exception!!", e.getMessage());
		}
		return null;
	}

	@Override
	public List<Map<String, Object>> findforNewReportType1ByBrNo(String brNo,
			String dateStartDate, String dateEndDate) {
		return this.getJdbc().queryForList("MIS.selForNewReportType1ByBrNo",
				new Object[] { brNo, dateStartDate, dateEndDate });
	}

	@Override
	public List<Map<String, Object>> findTotAmt(String custId) {
		return this.getJdbc().queryForList("LN.LNF150.findTotAmt",
				new Object[] { custId });
	}

	@Override
	public List<Map<String, Object>> caleLNF020AMT(HashSet<String> custIds) {
		List<Map<String, Object>> rowData = new ArrayList<Map<String, Object>>();
		if (custIds.size() > 0) {
			String custIdParams = Util.genSqlParam(custIds.toArray(new String[0]));
//			StringBuffer temp = new StringBuffer();
//			for (String cntrNo : custIds) {
//				if (Util.isNotEmpty(Util.trim(cntrNo))) {
//					temp.append(temp.length() > 0 ? "," : "");
//					temp.append("'");
//					temp.append(cntrNo);
//					temp.append("'");
//				}
//			}
//			if(temp.length() > 0 ){
			rowData = this.getJdbc().queryForListByCustParam("LNF020.caleAMT",
					new Object[] { custIdParams }, custIds.toArray(new String[0]));
//			}
		}
		return rowData;
	}
	
	/**
	 * Rpt[可疑代辦案件註記] 由MIS.ELF516中取得可疑代辦案件註記未編輯
	 */
	@Override
	public List<Map<String, Object>> getCLS250R01Data_ELF516_ForRptDataNoEdit() {
		try {
			return getJdbc().queryForList("MIS.ELF516_ForRptDataNoEdit",
					new String[] { null });
		} catch (Exception e) {
			logger.error("[getContent] Exception!!", e.getMessage());
		}
		return null;
	}
	
	/**
	 * Rpt[可疑代辦案件註記] 由MIS.ELF516中取得可疑代辦案件註記已編輯
	 */
	@Override
	public List<Map<String, Object>> getCLS250R01Data_ELF516_ForRptDataEdit(String begDate, String edDate) {
		try {
			return getJdbc().queryForList("MIS.ELF516_ForRptDataEdit",
					new String[] { begDate, edDate });
		} catch (Exception e) {
			logger.error("[getContent] Exception!!", e.getMessage());
		}
		return null;
	}
	
	/**
	 * Rpt[可疑代辦案件註記] 由MIS.ELF516中取得可疑代辦案件註記ALL
	 */
	@Override
	public List<Map<String, Object>> getCLS250R01Data_ELF516_ForRptDataAll(String begDate, String edDate) {
		try {
			return getJdbc().queryForList("MIS.ELF516_ForRptDataAll",
					new String[] { begDate, edDate });
		} catch (Exception e) {
			logger.error("[getContent] Exception!!", e.getMessage());
		}
		return null;
	}
}
