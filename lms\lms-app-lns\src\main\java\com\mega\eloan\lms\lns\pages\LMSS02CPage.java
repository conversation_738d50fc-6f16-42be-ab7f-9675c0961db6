package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.lns.panels.LMSS02CPanel01;
import com.mega.eloan.lms.lns.panels.LMSS02CPanel02;
import com.mega.eloan.lms.lns.panels.LMSS02CPanel03;
import com.mega.eloan.lms.lns.panels.LMSS02CPanel04;
import com.mega.eloan.lms.lns.panels.LMSS02CPanel05;

/**
 * <pre>
 * 借款人個金-分頁
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lmss02c/{page}")
public class LMSS02CPage extends AbstractEloanForm {

	@Override
	public void afterExecute(ModelMap model, PageParameters parameters) {
		super.afterExecute(model, parameters);
		// UPGRADE: 前端須配合改Thymeleaf的樣式
		// remove("_headerPanel");
		//model.addAttribute("showHeader", false);  // 不顯示 _headerPanel
	}

	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
	
	@Override
	public void execute(ModelMap model, PageParameters params) {
		new LMSS02CPanel01("lmss02apanel01").processPanelData(model, params);
		new LMSS02CPanel02("lmss02apanel02").processPanelData(model, params);
		new LMSS02CPanel03("lmss02apanel03").processPanelData(model, params);
		new LMSS02CPanel04("lmss02apanel04").processPanelData(model, params);
		new LMSS02CPanel05("lmss02apanel05").processPanelData(model, params);

	}
	
    /*
     * (non-Javadoc)
     * 
     * @see com.mega.eloan.common.pages.AbstractEloanForm#getViewName()
     */
    @Override
    public String getViewName() {
        // 不要headerarea
        return "common/pages/None";
    }
}
