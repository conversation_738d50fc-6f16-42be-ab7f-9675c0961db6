package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.model.C101S01H;
import com.mega.eloan.lms.model.C101S01I;
import com.mega.eloan.lms.model.C101S01U;
import com.mega.eloan.lms.model.C120S01H;
import com.mega.eloan.lms.model.C120S01I;
import com.mega.eloan.lms.model.C120S01U;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 聯徵(自C120S01H/C101S01H) 票信(自C120S01I/C101S01I)
 * </pre>
 * 
 * @since 2012/12/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/07,Fantasy,new
 *          <li>2013/07/01,Fantasy,add isC120M01A discriminate C101 or C120
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1131p01")
public class CLS1131P01Page extends AbstractOutputPage {

	@Autowired
	CLS1131Service cms1131Service;
	
	@Autowired
	CLSService clsService;
	
	@Autowired
	EjcicService ejcicService;

	// UPGRADETODO: 不確定Header是否還需要
	// @Override
	// protected void setHeaders(WebResponse response){
	// if(true){
	// Map<String, String> map =
	// clsService.get_codeTypeWithOrder("LMS_FUNC_ON_FLAG");
	// String key = "cls1131p01_X-UA-Compatible";
	// if(map.containsKey(key)){
	// String configStr = Util.trim(map.get(key));
	// if(Util.isNotEmpty(configStr) && configStr.indexOf("IE")>=0){
	// response.setHeader("X-UA-Compatible", configStr);
	// }
	// }
	// }
	// }
	
	@Override
	public String getOutputString(ModelMap model, PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String queryType = Util.trim(
				params.getString(UtilConstants.External.查詢類別)).toLowerCase();
		StringBuilder sb = new StringBuilder();
		// if (UtilConstants.External.聯徵.equals(queryType)) {
		// List<C101S01H> list = (List<C101S01H>) cms1131Service
		// .findListByRelationKey(C101S01H.class, mainId, custId,
		// dupNo);
		// for (C101S01H model : list) {
		// sb.append(model.getHtmlData());
		// }
		// } else if (UtilConstants.External.票信.equals(queryType)) {
		// List<C101S01I> list = (List<C101S01I>) cms1131Service
		// .findListByRelationKey(C101S01I.class, mainId, custId,
		// dupNo);
		// for (C101S01I model : list) {
		// sb.append(model.getHtmlResult());
		// }
		// }
		model.addAttribute("cls1131p01_allow_print",
				clsService.is_function_on_codetype("cls1131p01_allow_print"));

		boolean isC120M01A = params.getBoolean("isC120M01A");
		Class<? extends GenericBean> clazz = null;
		String tag = "";
		if (UtilConstants.External.聯徵.equals(queryType)) {
			tag = "htmlData";
			List<GenericBean> gericList = new ArrayList<GenericBean>();
			
			clazz = isC120M01A ? C120S01H.class : C101S01H.class;
			List<? extends GenericBean> list = cms1131Service.findListByRelationKey(clazz, mainId, custId, dupNo);
			
			gericList.addAll(list);
			
			clazz = isC120M01A ? C120S01U.class : C101S01U.class;
			List<? extends GenericBean> listS01U = cms1131Service.findListByRelationKey(clazz, mainId, custId, dupNo);
			gericList.addAll(listS01U);
			
			boolean s01h_contains_Z13 = false;	
			try{
				int size = gericList.size();				
				LinkedHashMap<String, List<Integer>> ord_map = new LinkedHashMap<String, List<Integer>>();
				List<Integer> other_list = new ArrayList<Integer>(); //放其它的TXID
				//按順序 H116:逾期催收及呆帳資料, H135:授信資料, H128:信用卡資料, HB29, HB68, HJ10, HZ13, HZ21
				String[] txid_arr = new String[]{"H116", "H135", "H128", "HB29", "HB68", "HJ10", CrsUtil.EJ_TXID_HZ13, CrsUtil.EJ_TXID_HZ21, CrsUtil.EJ_TXID_HD10, CrsUtil.EJ_TXID_HR20, CrsUtil.EJ_TXID_S11};
				for(String txId : txid_arr){
					ord_map.put(txId, new ArrayList<Integer>());
				}
				for (int i=0; i<size;i++) {
					GenericBean bean = gericList.get(i);
					String txId = "";
					try {
						txId =  (String) bean.get("txid");
					} catch (CapException e) {
					}
					if(ord_map.containsKey(txId)){
						ord_map.get(txId).add(i);
					}else{
						other_list.add(i);
					}
					if(Util.equals(txId, CrsUtil.EJ_TXID_HZ13)){
						s01h_contains_Z13 = true;
					}
				}
				//~~~~~~~~~~~~~~~~~~~~~~
				List<Integer> new_seq_list = new ArrayList<Integer>();
				for(String txId: ord_map.keySet()){ //依指定的 txId 順序呈現
					List<Integer> match_list = ord_map.get(txId);
					if(match_list.size()>0){
						new_seq_list.addAll(match_list);
					}
				}
				new_seq_list.addAll(other_list);
				//~~~~~~~~~~~~~~~~~~~~~~
				if(new_seq_list.size()==size){ //先 check 重排序後的筆數相否相同
					for(Integer idx: new_seq_list){
						GenericBean bean = gericList.get(idx);
						
						String txId = "";
						try {
							txId =  (String) bean.get("txid");
						} catch (CapException e) {
						}
						if (s01h_contains_Z13 && Util.equals(txId, CrsUtil.EJ_TXID_HZ13)) {
							//P9查詢結果已包含Z13，但另外在 標準查詢 的UI去呈現Z13
							continue;
						}
						
						
						try {
							sb.append((String) bean.get(tag));
						} catch (CapException e) {
						}
					}
				}else{
					throw new CapException("diff size", getClass());
				}				
			}catch(Exception excep){
				//之前版本：無特定順序
				for (GenericBean bean : gericList) {
					try {
						sb.append((String) bean.get(tag));
					} catch (CapException e) {
					}
				}
			}
		} else if (UtilConstants.External.票信.equals(queryType)) {
			tag = "htmlResult";
			clazz = isC120M01A ? C120S01I.class : C101S01I.class;
			
			List<? extends GenericBean> list = cms1131Service.findListByRelationKey(clazz, mainId, custId, dupNo);
			for (GenericBean bean : list) {
				try {
					sb.append((String) bean.get(tag));
				} catch (CapException e) {
				}
			}
		}
		
		if (sb.length() > 0) {
			setNeedHtml(true);
			// 執行浮水印
			// O-112-0233 調整浮水印，UnitNo若在EJF369有對應的VDEPTID就改用VDEPTID
			String wm_msg = UtilConstants.兆豐銀行代碼 + ejcicService.findEJF369VDEPTID(user.getUnitNo()) 
					+" "+user.getUserId()+" "+user.getLoginIP();
			sb.append("<script>window.onload = function(){");
			sb.append("watermark('").append(wm_msg).append("');");
			sb.append("};</script>");
		}

		return sb.toString();
	}

	// UPGRADE: 待確認是否需要ViewName
	@Override
	protected String getViewName() {
		return null;
	}

}
