/* 
 * CLS1151S05Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import java.util.Date;
import java.util.Set;

import org.springframework.ui.ModelMap;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.LMSUtil;

/**
 * <pre>
 * 個金額度明細表 - 產品種類
 * </pre>
 * 
 * @since 2012/12/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/7,REX,new
 *          </ul>
 */
public class CLS1151S05Panel extends Panel {

	private static final long serialVersionUID = 1L;
	
	private Date l120m01a_endDate;
	
	private String subTab2Desc;
	
	private Set<String> c102m01a_rptId;

	/**
	 * @param id
	 */
	public CLS1151S05Panel(String id, Date l120m01a_endDate, String subTab2Desc, Set<String> c102m01a_rptId) {
		super(id);
		this.l120m01a_endDate = l120m01a_endDate;
		this.subTab2Desc = subTab2Desc;
		this.c102m01a_rptId = c102m01a_rptId;
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
	
		boolean c102m01aRskFlagPanel_V202208 = false;
		boolean c102m01aRskFlagPanel_V20171231 = false;
		boolean c102m01aRskFlagPanel_old = false;
		if(l120m01a_endDate!=null 
			&& LMSUtil.cmpDate(l120m01a_endDate, "<", CapDate.parseDate("2017-12-31"))){
			c102m01aRskFlagPanel_old = true;
		}else{
			if(c102m01a_rptId.contains(LMSUtil.C102M01A_RPTID_V202208)){
				c102m01aRskFlagPanel_V202208 = true;
			}else if(c102m01a_rptId.contains(LMSUtil.C102M01A_RPTID_V20171231)){
				c102m01aRskFlagPanel_V20171231 = true;
			}						
		}
		//若皆 false , 則顯示 latest_version
		if(c102m01aRskFlagPanel_V202208==false 
			&& c102m01aRskFlagPanel_V20171231==false
			&& c102m01aRskFlagPanel_old==false){
			
			if(Util.equals(LMSUtil.get_C102M01A_RPTID_latestVersion(), LMSUtil.C102M01A_RPTID_V202208) ){
				c102m01aRskFlagPanel_V202208 = true;
			}else if(Util.equals(LMSUtil.get_C102M01A_RPTID_latestVersion(), LMSUtil.C102M01A_RPTID_V20171231) ){
				c102m01aRskFlagPanel_V20171231 = true;
			}else{
				c102m01aRskFlagPanel_old = true;	
			}			
		}
		
		/*
		 無法在1個頁面的情況下
		 同時 呈現【 35%, 75%, 100%, 45%】【45%, 100%】
		只能儘可能讓 "列印的順序" 與 "呈現的順序" 相同
		
		[1]在異動生效之前舊案, 若其endDate<2017-12-31, showRptPanel_old		
		[2]如果是 2017-12-31之前的舊案, 但未覆核, 留在編製中, 只能【45%, 100%】順序不一致 → 更新 rptId 版本來解決
		*/
		model.addAttribute("c102m01aRskFlagPanel_V202208", c102m01aRskFlagPanel_V202208);
		model.addAttribute("c102m01aRskFlagPanel_V20171231", c102m01aRskFlagPanel_V20171231);
		model.addAttribute("c102m01aRskFlagPanel_old", c102m01aRskFlagPanel_old); // 順序【 35%, 75%, 45%, 100%】
		
		model.addAttribute("c102m01aTitle_V202208", c102m01aRskFlagPanel_V202208);
		model.addAttribute("c102m01aTitle_old", !c102m01aRskFlagPanel_V202208);
		
		model.addAttribute("L140S02ATabs_2", subTab2Desc);
	}
}
