//var unitType = userInfo ? userInfo.unitType : "";
var unitNo = userInfo ? userInfo.unitNo : "";

var pageAction = {
	handler : 'lms7010formhandler',
	grid : null,
	build : function(){
		
		//建立下拉單
		$.ajax({
			handler : pageAction.handler,
			action : 'queryBrnoData',
			data : {
				brno:unitNo
			},
			success : function(response) {
				var brno = response.itemBranch;
				var zgName = {
					format : "{key}",
			        item : response.zgNo
				};
				$("select#ZGName").change(function(item){
					var sel = $("select#ZGName").find("option:selected");
					$("input#zhuGuan").val(sel.val()+" "+sel.text());
				}).setItems(zgName);
				$("input#brno").val(brno);//基本上只看的到本身分行
				$("input#unitType").val(response.unitType);
				//依分行更改主管類別
		
				var unitType = $("input#unitType").val();
				//alert(unitType);
				if(unitType!='B' && unitType!='K'){//非一般分行&K type
					pageAction.grid.jqGrid("hideCol","isType1");
					pageAction.grid.jqGrid("hideCol","isType2");
					$("label#type1").hide();
					$("label#type2").hide();
				}else{
					pageAction.grid.jqGrid("hideCol","isType4");
					pageAction.grid.jqGrid("hideCol","isType5");
					$("label#type4").hide();
					$("label#type5").hide();
				}
			}
		});
		
		pageAction.grid = $("#gridview").iGrid({
			//localFirst: true,
			handler : 'lms7010gridhandler',
			height : 400,
			action :  "queryL800m01a",
			rowNum:15,
			rownumbers:false,
			//multiselect : true,
			colModel : [{
				colHeader : "oid",
				name : 'oid',
				hidden : true //是否隱藏
			},{
				colHeader : "brno", //單位代號
				hidden : true, //是否隱藏
				name : 'brno' //col.id
				
			},{
				colHeader :"dataType", //資料類型
				hidden : true, //是否隱藏
				name : 'dataType' //col.id
			},{
				colHeader : i18n.lms7010v01["L800M01A.dataType"], //資料類型(顯示)
				align:"center",
				width : 50, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'dataType_show' //col.id
			},{
				colHeader : i18n.lms7010v01["L800M01A.ZGtype"], //主管分類
				hidden : true, //是否隱藏
				name : 'ZGtype' //col.id
			},{
				colHeader : i18n.lms7010v01["L800M01A.zhuGuan"], //主管員工號碼
				align:"center",
				width : 50, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'zhuGuan' //col.id
			},{
				colHeader : i18n.lms7010v01["L800M01A.ZGName"], //主管姓名
				align:"center",
				width : 50, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'ZGName' //col.id
			},{
				colHeader : i18n.lms7010v01["type1"], //帳戶管理員
				align:"center",
				width : 50, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'isType1' //col.id
			},{
				colHeader : i18n.lms7010v01["type2"], //授信主管
				align:"center",
				width : 50, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'isType2' //col.id
			},{
				colHeader : i18n.lms7010v01["type3"], //授權主管
				align:"center",
				width : 50, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'isType3' //col.id
			},{
				colHeader : i18n.lms7010v01["type4"], //單位副主管
				align:"center",
				width : 50, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'isType4' //col.id
			},{
				colHeader : i18n.lms7010v01["type5"], //單位主管
				align:"center",
				width : 50, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'isType5' //col.id
			},{
				colHeader : "creator", //建立人員號碼
				hidden : true, //是否隱藏
				name : 'creator' //col.id
			},{
				colHeader : "creatorNM", //建立人員號碼
				hidden : true, //是否隱藏
				name : 'creatorNM' //col.id
			},{
				colHeader : "createTime", //建立日期
				hidden : true, //是否隱藏
				name : 'createTime' //col.id
			},{
				colHeader : "updater", //異動人員號碼
				hidden : true,
				name : 'updater' //col.id
			},{
				colHeader : "updaterNM", //異動人員
				hidden : true,
				name : 'updaterNM' //col.id
			},{
				colHeader :"updateTime", //異動日期
				hidden : true,
				name : 'updateTime' //col.id
			}
		],
			ondblClickRow: function(rowid){//同修改
				var data = pageAction.grid.getRowData(rowid);
				pageAction.openBox(data);
				$("input[name=dataType]").trigger("change");
			}			
		});
		
		
		
		//build button 
		//新增
		$("#buttonPanel").find("#btnAdd").click(function() {
			pageAction.openBox({});
			$("input[name=dataType]").trigger("change");
		})
		
		//編輯
		.end().find("#btnModify").click(function() {
			var data = pageAction.getRowData();
			if (data){
				pageAction.openBox(data);
				$("input[name=dataType]").trigger("change");
			}
		})
		//刪除
		.end().find("#btnDelete").click(function() {
			var data = pageAction.getRowData();
			if (data){
				MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
					if (action){
						$.ajax({
							handler : pageAction.handler,
							action : 'deleteL800m01a',
							data : {
								oid : data.oid || ''
							},
							success:function(responseData){
								pageAction.reloadGrid();
								MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["confirmDeleteSuccess"]);
							}
						});
					}
				});
			}
		});
		
		$("input[name=dataType]").change(function(){
			pageAction.switchAuthCharger();
		});
		
	},
	/**
	 * 開啟視窗
	 */
	openBox : function(data){
		
		var table = $("table#adtb");
		//有傳值(on 編輯)則將data代入畫面
		table.find("input:checked").attr("checked",false);
		if(data.oid!=null){
			table.find("input#dataType:eq(0)").attr("checked",(data.dataType==1 || data.dataType==3));
			table.find("input#dataType:eq(1)").attr("checked",(data.dataType>=2));
			table.find("input#oid").val(data.oid);
			table.find("select#ZGName").val(data.zhuGuan);
			table.find("input#updater").val(data.updater+" "+data.updaterNM);
			table.find("input#zhuGuan").val(data.zhuGuan+" "+data.ZGName);
			table.find("input#type1").attr("checked",(data.isType1));
			table.find("input#type2").attr("checked",(data.isType2));
			table.find("input#type3").attr("checked",(data.isType3));
			table.find("input#type4").attr("checked",(data.isType4));
			table.find("input#type5").attr("checked",(data.isType5));
		}
		else{//reset
			table.find("input#oid").val("");
			table.find("input#updater").val("");
			table.find("input#zhuGuan").val("");
			table.find("select#ZGName").val("");
		}
		//開啟畫面
		$("#addThickBox").thickbox({
			title : i18n.lms7010v01["addPanelTitle"],//'常用主管表',
			width : 700,
			height : 300,
			modal : true,
			align : 'center',
			valign: 'bottom',
			i18n: i18n.def,
			buttons : {
				'sure' : function(){
					if ($("input#zhuGuan").val()!=""){	//因為zgName需要select的option內容，無法用form傳.....
						
						var dataType=0;
						$("input[id='dataType']:checked").each(function(){
							dataType= dataType+parseInt($(this).val());
						});
//						alert($(":checked[name='type1']").val());
						$.ajax({
							handler : pageAction.handler,
							action : 'saveL800m01a',
							data : {
								oid:$("input#oid").val(),
								brno:$("input#brno").val(),
								updater:$("input#updater").val(),
								zhuGuan:$("input#zhuGuan").val(),
								isType1:$(":checked[name='type1']").val(),
								isType2:$(":checked[name='type2']").val(),
								isType3:$(":checked[name='type3']").val(),
								isType4:$(":checked[name='type4']").val(),
								isType5:$(":checked[name='type5']").val(),
								zgName:$("select#ZGName").find(":selected").text(),
								creator:data.creator,
								createTime:data.createTime,
								dataType:dataType
							},
							success:function(response){
								if(response["error"])
									MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.lms7010v01["dup"]);
								else{
									$.thickbox.close();
									MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["saveSuccess"]);
									pageAction.reloadGrid();
								}
							}
						});
					}else{
						MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.lms7010v01["plsSelZGNM"]);
					}
				},
				'close' : function(){
					$.thickbox.close();
				}
			}
		});
	},
	/**
	 * 取得資料表之選擇列
	 */
	getRowData : function(){
		var row = pageAction.grid.getGridParam('selrow');
		var data;
		if (row) {
			data = pageAction.grid.getRowData(row);
		}else{
			MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["grid.selrow"]);
		}
		return data;
	},
	/**
	 * 重整資料表
	 */
	reloadGrid : function(data){
		if (data){
			pageAction.grid.jqGrid("setGridParam", {
				posinputata : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}else{
			pageAction.grid.trigger('reloadGrid');
		}
	},
	
	switchAuthCharger: function(){

		$("#type3").show();

		var isBranchManager = $("#isBranchManager").val();
		$("input[name=dataType]:checked").each(function(){
			if ($(this).val() == '2' && isBranchManager != 'Y') {
				$("#type3").hide();
				$("input[id=type3]").attr('checked', false);
			}
		});
	}
}

$(function() {
	pageAction.build();
});