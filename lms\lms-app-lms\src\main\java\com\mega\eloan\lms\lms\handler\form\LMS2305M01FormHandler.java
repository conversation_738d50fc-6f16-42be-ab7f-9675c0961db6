/* 
 * LMS2305M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.lms.handler.form;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.lms.pages.LMS2305M01Page;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lms.service.LMS2305Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF022Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L230M01A;
import com.mega.eloan.lms.model.L230S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 簽約未動用授信案件送作業
 * </pre>
 * 
 * @since 2012/1/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/12,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lms2305m01formhandler")
@DomainClass(L230M01A.class)
public class LMS2305M01FormHandler extends AbstractFormHandler {

	@Resource
	LMS2305Service lms2305Service;

	@Resource
	LMS1405Service lms1405Service;

	@Resource
	DocLogService docLogService;

	@Resource
	NumberService numberService;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMSService lmsService;

	@Resource
	MisLNF022Service misLNF022Service;

	/**
	 * 查詢 queryL230m01a
	 * 
	 * @param params
	 *            前端資料
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult queryL230m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String caseMainId = params.getString("caseMainId");
		int page = Util.parseInt(params.getString("page"));
		if (Util.isEmpty(mainOid)) {
			L120M01A l120m01a = lms2305Service.findL120M01AByMainId(caseMainId);
			if (l120m01a != null) {
				switch (page) {
				case 1:
					result.set("caseDate", l120m01a.getCaseDate());
					result.set("caseNo", l120m01a.getCaseNo());
					result.set("custId", l120m01a.getCustId());
					result.set("custName", l120m01a.getCustName());
					result.set("dupNo", l120m01a.getDupNo());
					result.set("typCd",
							getMessage("typCd." + l120m01a.getTypCd()));
					result.set("ownBrId", StrUtils.concat(user.getUnitNo(),
							" ", branchService.getBranchName(user.getUnitNo())));
					result.set("docStatus", getMessage("docStatus."
							+ CreditDocStatusEnum.海外_編製中.toString()));
					result.set("caseMainId", caseMainId);

					break;
				case 2:
					List<L140M01A> l140m01as = lms1405Service
							.findL140m01aListByL120m01cMainId(
									l120m01a.getMainId(),
									new String[] {
											UtilConstants.Cntrdoc.ItemType.額度明細表,
											UtilConstants.Cntrdoc.ItemType.額度批覆表 },
									FlowDocStatusEnum.已核准.getCode());
					if (!l140m01as.isEmpty()) {
						result.set("ltCurr", l140m01as.get(0).getLoanTotCurr());
						result.set("ltAmt", l140m01as.get(0).getLoanTotAmt());
					}
					result.set("gist", l120m01a.getGist());
					break;
				case 3:
					break;
				}

				result.set("showTypCd",
						getMessage("typCd." + l120m01a.getTypCd()));
				result.set(
						"showCustId",
						StrUtils.concat(l120m01a.getCustId(), " ",
								l120m01a.getDupNo(), " ",
								l120m01a.getCustName()));
			}

			return result;
		}

		L230M01A l230m01a = lms2305Service.findModelByOid(L230M01A.class,
				mainOid);

		if (l230m01a != null) {

			result = DataParse.toResult(l230m01a);
			formatResultShow(result, l230m01a, page);

		} else {
			result.set("docStatus", getMessage("docStatus."
					+ CreditDocStatusEnum.海外_編製中));
			result.set(
					"ownBrId",
					StrUtils.concat(user.getUnitNo(), " ",
							branchService.getBranchName(user.getUnitNo())));
		}

		return result;
	}

	/**
	 * 查詢 getNewL120M0a
	 * 
	 * @param params
	 *            前端資料
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult getNewL120M0a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String caseMainId = params.getString("caseMainId");
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		L120M01A l120m01a = lms2305Service.findL120M01AByMainId(caseMainId);

		if (l120m01a != null) {
			L230M01A l230m01a = lms2305Service.findModelByOid(L230M01A.class,
					mainOid);
			l230m01a = this.copyL120M01AtoL230M01A(l230m01a, l120m01a);
			List<L230S01A> l230s01as = this.copyL140M01AtoL230S01A(l230m01a,
					l120m01a);
			lms2305Service.saveMain(l230m01a, l230s01as);
			result.set("caseDate", l120m01a.getCaseDate());
			result.set("caseNo", l120m01a.getCaseNo());
			result.set("custId", l120m01a.getCustId());
			result.set("custName", l120m01a.getCustName());
			result.set("dupNo", l120m01a.getDupNo());
			result.set("typCd", getMessage("typCd." + l120m01a.getTypCd()));
			result.set(
					"ownBrId",
					StrUtils.concat(user.getUnitNo(), " ",
							branchService.getBranchName(user.getUnitNo())));
			result.set("docStatus", getMessage("docStatus."
					+ CreditDocStatusEnum.海外_編製中.toString()));
			result.set("caseMainId", caseMainId);

		}
		return result;

	}

	/**
	 * queryCntrDoc 查詢 額度資訊修改
	 * 
	 * @param params
	 *            oid : 額度資訊檔 oid
	 * @param parent
	 *            Component
	 * @return <pre>
	 * signDate:簽約註記日,
	 *  nuseMemo :簽約註記
	 * </pre>
	 * 
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult queryCntrDoc(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		L230S01A l230s01a = lms2305Service.findModelByOid(L230S01A.class, oid);
		if (l230s01a != null) {
			if (LMSUtil.isContainValue(l230s01a.getProPerty(),
					UtilConstants.Cntrdoc.Property.不變)
					|| LMSUtil.isContainValue(l230s01a.getProPerty(),
							UtilConstants.Cntrdoc.Property.取消)) {
				Properties pop = this.setProp(MessageBundleScriptCreator
						.getComponentResource(LMS2305M01Page.class));
				// l230m01a.errorMessage05=性質為【不變、取消】不用維護簽約註記
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤,
						pop.getProperty("l230m01a.errorMessage05")), getClass());
			}
			// J-111-0551_05097_B1003 Web
			// e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
			result = DataParse.toResult(l230s01a, DataParse.Need, new String[] {
					"signDate", "nuseMemo", "reason", "reasonDrc",
					"currentApplyCurr", "currentApplyAmt",
					"isSignAmtLowerApplyAmt", "signAmt_S", "signAmt_N" });
		}
		return result;

	}

	/**
	 * saveCntrDoc 儲存 額度資訊修改
	 * 
	 * @param params
	 *            <pre>
	 *             oid : 額度資訊檔 oid,
	 *             CntrDocForm:畫面輸入資料
	 * </pre>
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult saveCntrDoc(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		L230S01A l230s01a = lms2305Service.findModelByOid(L230S01A.class, oid);
		if (l230s01a != null) {
			String cntrDocForm = params.getString("CntrDocForm");
			DataParse.toBean(cntrDocForm, l230s01a);

			// J-111-0551_05097_B1003 Web
			// e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
			if (Util.equals(Util.trim(l230s01a.getIsSignAmtLowerApplyAmt()),
					"Y")) {
				if (l230s01a.getSignAmt_N() != null
						&& l230s01a.getSignAmt_S() != null
						&& l230s01a.getCurrentApplyAmt() != null) {
					if (l230s01a.getCurrentApplyAmt().compareTo(
							l230s01a.getSignAmt_S()
									.add(l230s01a.getSignAmt_N())) < 0) {
						throw new CapMessageException(
								"本案實際簽約額度低於核准額度，實際簽約額度有擔保與無擔保之合計金額不得大於現請額度",
								getClass());
					}
				}
			}

			// 清空不該有值得欄位
			String nuseMemo = l230s01a.getNuseMemo();
			if (UtilConstants.NoUseCase.NuseMemo.已動用.equals(nuseMemo)
					|| UtilConstants.NoUseCase.NuseMemo.已簽約.equals(nuseMemo)) {
				l230s01a.setReason("");
				l230s01a.setReasonDrc("");
			}
			if (UtilConstants.NoUseCase.NuseMemo.不簽約註銷額度.equals(nuseMemo)) {
				String reason = Util.trim(params.getString("reason"));
				l230s01a.setReason(reason);
				if (reason.indexOf("99") == -1) {
					l230s01a.setReasonDrc("");
				}
				l230s01a.setSignDate(null);
			}
			if (UtilConstants.NoUseCase.NuseMemo.未簽約.equals(nuseMemo)) {
				l230s01a.setReason("");
				l230s01a.setReasonDrc("");
				l230s01a.setSignDate(null);
			}
			lms2305Service.save(l230s01a);
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		return result;

	}

	/**
	 * 格式化顯示訊息
	 * 
	 * @param result
	 *            格式化訊息
	 * @param meta
	 *            主檔table
	 * @param page
	 *            頁碼
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	private CapAjaxFormResult formatResultShow(CapAjaxFormResult result,
			L230M01A meta, Integer page) throws CapException {
		switch (page) {
		case 1:
			result.set("typCd", getMessage("typCd." + meta.getTypCd()));
			result.set("docStatus",
					getMessage("docStatus." + meta.getDocStatus()));
			result.set(
					"ownBrId",
					StrUtils.concat(meta.getOwnBrId(), " ",
							branchService.getBranchName(meta.getOwnBrId())));

			result.set("creator", lmsService.getUserName(meta.getCreator()));
			result.set("updater", lmsService.getUserName(meta.getUpdater()));
			result.set("managerId", Util.trim(meta.getManagerId()) + " "
					+ lmsService.getUserName(meta.getManagerId()));
			result.set(
					"bossId",
					Util.trim(meta.getBossId()) + " "
							+ lmsService.getUserName(meta.getBossId()));
			result.set("reCheckId", Util.trim(meta.getReCheckId()) + " "
					+ lmsService.getUserName(meta.getReCheckId()));
			result.set(
					"apprId",
					Util.trim(meta.getApprId()) + " "
							+ lmsService.getUserName(meta.getApprId()));
			break;
		case 2:

			break;
		case 3:

			break;

		}
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));
		result.set("showTypCd", getMessage("typCd." + meta.getTypCd()));
		result.set("showCustId", StrUtils.concat(meta.getCustId(), " ",
				meta.getDupNo(), " ", meta.getCustName()));
		return result;

	}

	/**
	 * 刪除(多筆)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return Component
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteFile(PageParameters params) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oidList = params.getStringArray("list");

		lms2305Service.deleteL230M01AByOids(oidList);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));

		return result;
	}

	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveL230m01a(PageParameters params)
			throws CapException {

		L230M01A l230m01a = saveBase(params);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		l230m01a.setRandomCode(IDGenerator.getRandomCode());
		if (CreditDocStatusEnum.海外_編製中.getCode()
				.equals(l230m01a.getDocStatus())) {
			// 在編製中的狀態按儲存的是經辦
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			l230m01a.setApprId(user.getUserId());
		}

		lms2305Service.save(l230m01a);
		CapAjaxFormResult result = new CapAjaxFormResult();

		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		result = DataParse.toResult(l230m01a);

		formatResultShow(result, l230m01a, page);
		if (params.getAsBoolean("showMsg", false)) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		return result;
	}

	/**
	 * 儲存基本處理
	 * 
	 * <pre>
	 * @param params
	 *            caseMainId : 簽報書mainId
	 * @param parent
	 *            Component
	 * @return 簽約未動用主檔
	 * @throws CapException
	 * </pre>
	 */
	private L230M01A saveBase(PageParameters params)
			throws CapException {
		String oid = params.getString(EloanConstants.MAIN_OID);
		String formLData = params.getString("L230M01AForm"); // 抓前端資料
		JSONObject jsonData = JSONObject.fromObject(formLData);
		L230M01A l230m01a = lms2305Service.findModelByOid(L230M01A.class, oid);
		List<L230S01A> l230s01as = new ArrayList<L230S01A>();
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		if (l230m01a == null) {
			l230m01a = new L230M01A();
			l230m01a.setCreateTime(CapDate.getCurrentTimestamp());
			String caseMainId = params.getString("caseMainId");
			L120M01A l120m01a = lms2305Service.findL120M01AByMainId(caseMainId);
			CapBeanUtil.copyBean(l120m01a, l230m01a, new String[] { "custId",
					"dupNo", "custName", "typCd", "caseDate", "gist",
					"caseYear", "caseNo" });
			l230m01a.setOwnBrId(l120m01a.getCaseBrId());
			l230m01a.setMainId(IDGenerator.getUUID());
		} else {
			switch (page) {
			case 1:
			case 2:
				DataParse.toBean(jsonData, l230m01a);

				break;
			case 3:

				break;
			}
		}

		Properties pop = this.setProp(MessageBundleScriptCreator
				.getComponentResource(LMS2305M01Page.class));
		String validate = Util.validateColumnSize(l230m01a, pop, "L230M01A");
		if (validate != null) {
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", validate);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		if (CreditDocStatusEnum.海外_編製中.getCode()
				.equals(l230m01a.getDocStatus())) {
			// 在編製中的狀態按儲存的是經辦
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			l230m01a.setApprId(user.getUserId());
		}
		lms2305Service.saveMain(l230m01a, l230s01as);
		return l230m01a;
	}

	/**
	 * 設定Properties檔驗證時要show的名稱
	 * 
	 * @param prop
	 *            語系檔
	 * @return 處理完的prop
	 */
	private Properties setProp(Properties prop) {
		// l230m01a.title05=案號
		prop.put("L230M01A.caseNo", prop.getProperty("l230m01a.title05"));
		// l230m01a.title13=原申請案由
		prop.put("L230M01A.gist", prop.getProperty("l230m01a.title13"));
		// l230m01a.title16=未簽約、動用原因
		prop.put("L230M01A.reasion", prop.getProperty("l230m01a.title16"));
		return prop;
	}

	/**
	 * 新增未動用案件
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult addCreditUnused(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String txCode = params.getString(EloanConstants.TRANSACTION_CODE);
		String custId = params.getString("custId", "").toUpperCase();
		String dupNo = params.getString("dupNo", "0").toUpperCase();
		String name = params.getString("name", "");

		L230M01A l230m01a = new L230M01A();
		l230m01a.setCreateTime(CapDate.getCurrentTimestamp());
		l230m01a.setCreator(user.getUserId());
		l230m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		l230m01a.setOwnBrId(user.getUnitNo());
		CapAjaxFormResult result = new CapAjaxFormResult();
		l230m01a.setMainId(IDGenerator.getUUID());
		l230m01a.setCustId(custId);
		l230m01a.setDupNo(dupNo);
		l230m01a.setCustName(name);
		l230m01a.setTxCode(txCode);
		l230m01a.setTypCd(TypCdEnum.海外.getCode());
		l230m01a.setDocURL(params.getString("docUrl"));

		// 案件簽報書MainId
		String caseMainId = params.getString("caseMainId");
		L120M01A l120m01a = lms2305Service.findL120M01AByMainId(caseMainId);
		if (l120m01a != null) {
			// Uid 用來存 原案簽報書的mainId
			l230m01a.setUid(caseMainId);
			l230m01a.setCaseNo(l120m01a.getCaseNo());
			l230m01a.setCaseDate(l120m01a.getCaseDate());
			l230m01a.setCustId(l120m01a.getCustId());
			l230m01a.setCustName(l120m01a.getCustName());
			l230m01a.setDupNo(l120m01a.getDupNo());
			l230m01a.setTypCd(l120m01a.getTypCd());
			l230m01a.setGist(l120m01a.getGist());
		}
		List<L230S01A> l230s01as = this.copyL140M01AtoL230S01A(l230m01a,
				l120m01a);
		lms2305Service.saveMain(l230m01a, l230s01as);
		result.set(EloanConstants.MAIN_OID, l230m01a.getOid());
		return result;
	}

	/**
	 * 呈主管覆核(呈主管 主管覆核 拆2個method)
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify + AuthType.Accept, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {

		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L230M01A mainModel = lms2305Service.findModelByOid(L230M01A.class, oid);
		List<L230S01A> l230s01as = (List<L230S01A>) lms2305Service
				.findListByMainId(L230S01A.class, mainModel.getMainId());
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String account = Util.trim(params.getString("account"));
		if (!Util.isEmpty(account)) {
			String manager = Util.trim(params.getString("manager"));
			mainModel.setBossId(account);
			mainModel.setManagerId(manager);
		}

		// 如果有這個key值表示是輸入chekDate核准日期
		if (params.containsKey("checkDate")) {
			mainModel.setApproveTime(CapDate.convertStringToTimestamp(params
					.getString("checkDate") + " 00:00:00"));
			mainModel.setApprover(user.getUserId());
			mainModel.setReCheckId(user.getUserId());
			// 設定資料日期
			for (L230S01A l230s01a : l230s01as) {
				l230s01a.setDataDate(CapDate.parseDate(CapDate
						.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD)));
			}
		}

		if (!Util.isEmpty(mainModel)) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
				if (params.containsKey("flowAction")) {

					if (!"back".equals(params.getString("flowAction"))) {
						if (user.getUserId().equals(mainModel.getApprId())) {
							// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						}
					}
				} else {
					// 當為呈主管要變更文件亂碼
					mainModel.setRandomCode(IDGenerator.getRandomCode());
				}
				lms2305Service.flowAction(mainModel.getOid(), mainModel,
						l230s01as, params.containsKey("flowAction"),
						params.getString("flowAction", ""));
			} catch (FlowMessageException t1) {
				logger.error(
						"[flowAction] lms2305Service.flowAction FlowException!!",
						t1);
				if (t1.getExtraMessage() == null
						|| t1.getExtraMessage().isEmpty()) {
					throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
				} else {
					throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage(), t1.getExtraMessage()),
							getClass());
				}
			} catch (Throwable t1) {
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}
		return new CapAjaxFormResult();
	}

	/**
	 * 暫存
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	// @DomainAuth(value = AuthType., CheckDocStatus = false)
	public IResult tempSave(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "Y"));
		this.saveBase(params);
		return result;
	}

	/**
	 * 檢核資料是否已經有正確的登錄
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params)
			throws CapException {
		Map<String, String> messageSet = new HashMap<String, String>();
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L230M01A l230m01a = saveBase(params);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS2305M01Page.class);
		if (l230m01a == null) {
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}
		String mainId = l230m01a.getMainId();
		if (Util.isEmpty(Util.trim(l230m01a.getCaseNo()))) {
			// l230m01a.title05=案號
			messageSet.put("colName", pop.getProperty("l230m01a.title05"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", messageSet), getClass());
		}

		if (Util.isEmpty(l230m01a.getCaseDate())) {
			// l230m01a.title06=簽案日期
			messageSet.put("colName", pop.getProperty("l230m01a.title06"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", messageSet), getClass());
		}

		if (Util.isEmpty(Util.trim(l230m01a.getGist()))) {
			// l230m01a.title13=原申請案由
			messageSet.put("colName", pop.getProperty("l230m01a.title13"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", messageSet), getClass());
		}

		if (Util.isEmpty(Util.trim(l230m01a.getReasion()))) {
			// l230m01a.title16=未簽約、動用原因
			messageSet.put("colName", pop.getProperty("l230m01a.title16"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", messageSet), getClass());
		}

		if (Util.isEmpty(l230m01a.getLtAmt())
				|| BigDecimal.ZERO.compareTo(l230m01a.getLtAmt()) == 0) {
			// l230m01a.title14=授信額度合計
			messageSet.put("colName", pop.getProperty("l230m01a.title14"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", messageSet), getClass());
		}

		if (Util.isEmpty(l230m01a.getLtCurr())) {
			// l230m01a.title26=幣別
			messageSet.put("colName", pop.getProperty("l230m01a.title26"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", messageSet), getClass());
		}
		List<L230S01A> l230s01as = (List<L230S01A>) lms2305Service
				.findListByMainId(L230S01A.class, mainId);

		// 檢查狀態註記是否都有登錄
		String errorMessage = "";
		// 檢查是否有未維護狀態
		HashMap<String, String> noNuseMemoCntrno1 = new HashMap<String, String>();
		// 未維護已簽約日
		HashMap<String, String> noNuseMemoCntrno2 = new HashMap<String, String>();
		// 已簽約日不得小於
		HashMap<String, String> noNuseMemoCntrno3 = new HashMap<String, String>();
		String nuseMemo = "";
		String cntrNo = "";
		Date singDate = null;
		// 簽約日最小年份
		int minYear = 2011;
		for (L230S01A l230s01a : l230s01as) {
			// 如果性質為(不變、取消)不檢查
			if (LMSUtil.isContainValue(l230s01a.getProPerty(),
					UtilConstants.Cntrdoc.Property.不變)
					|| LMSUtil.isContainValue(l230s01a.getProPerty(),
							UtilConstants.Cntrdoc.Property.取消)) {
				continue;
			}
			nuseMemo = l230s01a.getNuseMemo();
			cntrNo = l230s01a.getCntrNo();
			singDate = l230s01a.getSignDate();

			if (Util.isEmpty(nuseMemo)) {
				noNuseMemoCntrno1.put(cntrNo, "");
			} else {
				if (UtilConstants.NoUseCase.NuseMemo.不簽約註銷額度.equals(nuseMemo)) {
					// 檢查額度是否已經動用
					String result2 = misLNF022Service.findDB2ChkALoanUse(
							l230s01a.getCustId(), l230s01a.getDupNo(), cntrNo);
					if (UtilConstants.DEFAULT.是.equals(result2)) {
						// l230m01a.errorMessage04=額度序號 {0}
						// 帳務已建立，請先刪除a-Loan帳務後，再報送註銷該筆額度
						errorMessage = MessageFormat.format(
								pop.getProperty("l230m01a.errorMessage04"),
								cntrNo);
						throw new CapMessageException(RespMsgHelper.getMessage(
								UtilConstants.AJAX_RSP_MSG.執行有誤, errorMessage), getClass());
					}
				}
				if (UtilConstants.NoUseCase.NuseMemo.已簽約.equals(nuseMemo)) {
					if (Util.isEmpty(singDate)) {
						noNuseMemoCntrno2.put(cntrNo, "");
					} else {
						String singYear = CapDate.formatDate(singDate, "yyyy");
						if (Integer.valueOf(singYear) < minYear) {
							noNuseMemoCntrno3.put(cntrNo, "");
						}
					}
				}
			}
		}

		String[] errors1 = Util.getMapKey(noNuseMemoCntrno1);
		if (errors1.length > 0) {
			StringBuffer temp = new StringBuffer();
			for (String errorCntrno : errors1) {
				temp.append(temp.length() > 0 ? UtilConstants.Mark.MARKDAN : "");
				temp.append(errorCntrno);
			}
			// l230m01a.errorMessage01=額度序號{0}未維護狀態註記
			errorMessage = MessageFormat
					.format(pop.getProperty("l230m01a.errorMessage01"),
							temp.toString());
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, errorMessage), getClass());
		}

		String[] errors2 = Util.getMapKey(noNuseMemoCntrno2);
		if (errors2.length > 0) {
			StringBuffer temp = new StringBuffer();
			for (String errorCntrno : errors2) {
				temp.append(temp.length() > 0 ? UtilConstants.Mark.MARKDAN : "");
				temp.append(errorCntrno);
			}
			// l230m01a.errorMessage02=額度序號{0}未維護已簽約日
			errorMessage = MessageFormat
					.format(pop.getProperty("l230m01a.errorMessage02"),
							temp.toString());
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, errorMessage), getClass());
		}

		String[] errors3 = Util.getMapKey(noNuseMemoCntrno3);
		if (errors3.length > 0) {
			StringBuffer temp = new StringBuffer();
			for (String errorCntrno : errors3) {
				temp.append(temp.length() > 0 ? UtilConstants.Mark.MARKDAN : "");
				temp.append(errorCntrno);
			}
			// l230m01a.errorMessage03=額度序號{0}已簽約日不得小於{1}年
			errorMessage = MessageFormat.format(
					pop.getProperty("l230m01a.errorMessage03"),
					temp.toString(), String.valueOf(minYear));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, errorMessage), getClass());
		}

		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;
	}

	/**
	 * 複製簽報書內容
	 * 
	 * @param l230m01a
	 *            簽約未動用
	 * @param l120m01a
	 *            簽報書
	 * @return 簽約未動用
	 */
	private L230M01A copyL120M01AtoL230M01A(L230M01A l230m01a, L120M01A l120m01a) {
		l230m01a.setCaseNo(l120m01a.getCaseNo());
		l230m01a.setCaseDate(l120m01a.getCaseDate());
		l230m01a.setCustId(l120m01a.getCustId());
		l230m01a.setCustName(l120m01a.getCustName());
		l230m01a.setDupNo(l120m01a.getDupNo());
		l230m01a.setTypCd(l120m01a.getTypCd());
		l230m01a.setGist(l120m01a.getGist());
		// Uid 用來存 原案簽報書的mainId
		l230m01a.setUid(l120m01a.getMainId());
		return l230m01a;
	}

	/**
	 * 重新引進額度資訊檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return Component
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult pullinAgain(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L230M01A l230m01a = lms2305Service.findModelByOid(L230M01A.class,
				mainOid);

		Boolean suesses = lms2305Service.reloadL230S01A(l230m01a);
		if (!suesses) {
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	/**
	 * 複製額度明細表
	 * 
	 * @param l230m01a
	 *            簽約未動用
	 * @param l120m01a
	 *            簽報書
	 * @return 簽約未動用額度資訊檔 List
	 */
	private List<L230S01A> copyL140M01AtoL230S01A(L230M01A l230m01a,
			L120M01A l120m01a) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 先刪除已存在的
		lms2305Service.deleteL230S01A(l230m01a);
		ArrayList<L230S01A> l230s01as = new ArrayList<L230S01A>();
		if (l120m01a != null) {
			String itemtype = lmsService.checkL140M01AItemType(l120m01a);
			List<L140M01A> l140m01as = lms1405Service
					.findL140m01aListByL120m01cMainId(l120m01a.getMainId(),
							itemtype);
			for (L140M01A l140m01a : l140m01as) {
				L230S01A l230s01a = new L230S01A();
				try {
					CapBeanUtil.copyBean(l140m01a, l230s01a, new String[] {
							"custId", "dupNo", "custName", "ownBrId",
							"docStatus", "caseNo", "caseDate", "cntrNo",
							"lnSubject", "proPerty", "currentApplyCurr",
							"currentApplyAmt" });
					l230s01a.setOid(null);
					l230s01a.setMainId(l230m01a.getMainId());
					l230s01a.setSrcMainId(l140m01a.getMainId());
					l230s01a.setCreator(user.getUnitNo());
					l230s01a.setCreateTime(CapDate.getCurrentTimestamp());
					l230s01a.setUpdater(user.getUnitNo());
					l230s01a.setUpdateTime(CapDate.getCurrentTimestamp());
					l230s01as.add(l230s01a);

				} catch (CapException e) {
					logger.error(e.getMessage());
					logger.info("[copyL140M01AtoL230S01A] EXCEPTION!", e);

				}
			}

		}

		return l230s01as;
	}

}
