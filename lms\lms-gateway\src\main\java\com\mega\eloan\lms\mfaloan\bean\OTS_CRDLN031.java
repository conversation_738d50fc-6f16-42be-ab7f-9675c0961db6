package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 分行敘做永慶信義住商仲介房貸引介統計表 **/
public class OTS_CRDLN031 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CYC_DT", columnDefinition="DATE", nullable=false,unique = true)
	private Date cyc_dt;
	
	/** 歸屬單位  **/
	@Column(name="AREA_CD", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String area_cd;
	
	/** 分行別 **/
	@Column(name="BR_CD", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String br_cd;
	
	/** 仲介公司  **/
	@Column(name="PROJ_ID", length=5, columnDefinition="CHAR(5)", nullable=false,unique = true)
	private String proj_id;

	/** 筆數 **/
	@Column(name="TX_CNT", columnDefinition="DEC(8,0)")
	private BigDecimal tx_cnt;

	/** 金額 **/
	@Column(name="TX_AMOUNT", columnDefinition="DEC(13,0)")
	private BigDecimal tx_amount;

	/** 累計筆數 **/
	@Column(name="ACCUM_TX_CNT", columnDefinition="DEC(8,0)")
	private BigDecimal accum_tx_cnt;

	/** 累計金額 **/
	@Column(name="ACCUM_TX_AMOUNT", columnDefinition="DEC(13,0)")
	private BigDecimal accum_tx_amount;
	
	
	/** 取得資料日期 **/
	public Date getCyc_dt() {
		return cyc_dt;
	}
	/** 設定資料日期 **/
	public void setCyc_dt(Date cyc_dt) {
		this.cyc_dt = cyc_dt;
	}
	
	/** 取得歸屬單位  **/
	public String getArea_cd() {
		return area_cd;
	}
	/** 設定歸屬單位  **/
	public void setArea_cd(String area_cd) {
		this.area_cd = area_cd;
	}
	
	/** 取得分行別 **/
	public String getBr_cd() {
		return this.br_cd;
	}
	/** 設定分行別 **/
	public void setBr_cd(String value) {
		this.br_cd = value;
	}
	
	/** 取得仲介公司  **/
	public String getProj_id() {
		return proj_id;
	}
	/** 設定仲介公司  **/
	public void setProj_id(String proj_id) {
		this.proj_id = proj_id;
	}
	
	/** 取得筆數 **/
	public BigDecimal getTx_cnt() {
		return tx_cnt;
	}
	/** 設定筆數 **/
	public void setTx_cnt(BigDecimal tx_cnt) {
		this.tx_cnt = tx_cnt;
	}
	
	/** 取得金額 **/
	public BigDecimal getTx_amount() {
		return tx_amount;
	}
	/** 設定金額 **/
	public void setTx_amount(BigDecimal tx_amount) {
		this.tx_amount = tx_amount;
	}
	
	/** 取得累計筆數 **/
	public BigDecimal getAccum_tx_cnt() {
		return accum_tx_cnt;
	}
	/** 設定累計筆數 **/
	public void setAccum_tx_cnt(BigDecimal accum_tx_cnt) {
		this.accum_tx_cnt = accum_tx_cnt;
	}
	
	/** 取得累計金額 **/
	public BigDecimal getAccum_tx_amount() {
		return accum_tx_amount;
	}
	/** 設定累計金額 **/
	public void setAccum_tx_amount(BigDecimal accum_tx_amount) {
		this.accum_tx_amount = accum_tx_amount;
	}	
}
