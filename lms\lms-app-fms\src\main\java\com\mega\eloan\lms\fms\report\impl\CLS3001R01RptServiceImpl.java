package com.mega.eloan.lms.fms.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.fms.report.CLS3001R01RptService;
import com.mega.eloan.lms.fms.service.CLS3001Service;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.Util;

@Service("cls3001r01rptservice")
public class CLS3001R01RptServiceImpl implements
	FileDownloadService, CLS3001R01RptService {

	@Resource
	CLS3001Service cls3001Service;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			String category = Util.trim(params.getString("category"));
			String caseBrId = Util.trim(params.getString("caseBrId"));
			if(true){
				baos = (ByteArrayOutputStream) this.generateXls(category, caseBrId);	
			}
			if(baos==null){
				return null;
			}else{
				return baos.toByteArray();	
			}			
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}
	
	private ByteArrayOutputStream generateXls(String category, String caseBrId) throws IOException, Exception {	
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		if(true){
			cls3001Service.genExcel(outputStream, category, caseBrId);	
		}		
		if(outputStream!=null){
			outputStream.flush();	
		}		
		return outputStream;
	}
	
}
