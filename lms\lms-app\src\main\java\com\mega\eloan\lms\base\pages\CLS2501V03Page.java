/* 
 * LMS140MV03Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.pages;

import java.util.ArrayList;
import java.util.List;
import com.mega.eloan.common.html.EloanPageFragment;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

import tw.com.jcs.auth.AuthType;



/**
 * <pre>
 * 可疑代辦案件註記作業 - 已核准*/

@Controller
@RequestMapping("/cls/cls2501v03")
public class CLS2501V03Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_已核准);
		
		// 加上Button
		List<EloanPageFragment> list = new ArrayList<>();
		// 主管跟經辦都會出現的按鈕
		 list.add(LmsButtonEnum.Filter);
		 list.add(LmsButtonEnum.View);
		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {

		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			
		}
		addToButtonPanel(model, list);
		
		// 加上Button
		renderJsI18N(CLS2501V01Page.class);
		renderJsI18N(CLS2501M01Page.class);
		renderJsI18N(CLS2501V03Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/base/CLS2501V03Page');");
	}
	
}
