package tw.com.jcs.flow.node;

import tw.com.jcs.flow.core.FlowInstanceImpl;

/**
 * <pre>
 * 流程節點(狀態)
 * </pre>
 * 
 * @since 2023年1月10日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月10日
 *          </ul>
 */
public class StateNode extends FlowNode {

    public static final String NEXT_USER_KEY = "next_user";
    public static final String NEXT_DEPT_KEY = "next_dept";

    String role;

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.node.FlowNode#next(tw.com.jcs.flow.core.FlowInstanceImpl)
     */
    @Override
    public void next(FlowInstanceImpl instance) {
        instance.handle();
        finishCurrentNode(instance);
        instance.setRoleId(role);
        Object userId = instance.getAttribute(NEXT_USER_KEY);
        Object deptId = instance.getAttribute(NEXT_DEPT_KEY);

        instance.setUserId(userId != null ? userId.toString() : null);
        if (deptId != null) {
            instance.setDeptId(deptId.toString());
        }
        instance.removeAttribute(NEXT_USER_KEY);
        instance.removeAttribute(NEXT_DEPT_KEY);
        changeToThisNode(instance);
    }

}
