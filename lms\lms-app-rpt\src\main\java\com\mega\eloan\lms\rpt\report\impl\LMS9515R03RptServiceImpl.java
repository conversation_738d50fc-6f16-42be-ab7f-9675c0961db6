package com.mega.eloan.lms.rpt.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.model.L784M01A;
import com.mega.eloan.lms.model.L784S01A;
import com.mega.eloan.lms.rpt.pages.LMS9515V01Page;
import com.mega.eloan.lms.rpt.service.LMS9515Service;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 產生授信 有 無 不適用 銀行法,44,45所稱與本行有利害關係人PDF
 * 
 * <AUTHOR>
 * 
 */
@Service("lms9515r03rptservice")
public class LMS9515R03RptServiceImpl extends AbstractReportService {

	@Resource
	BranchService branch;

	@Resource
	LMS9515Service service9515;

	@Resource
	LMSService lmsService;
	
	@Override
	public String getReportTemplateFileName() {
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		return "report/rpt/LMS9515R0201_" + LMSUtil.getLocale().toString() + ".rpt";
	}

	/*
	 * (non-Javadoc) 設定需要傳入RPT參數
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.AbstractReportService#setReportData(com
	 * .mega.eloan.lms.base.report.ReportGenerator,
	 */
	@Override
	public void setReportData(ReportGenerator reportTools, PageParameters params) throws CapException {
		Properties prop = null;
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = null;
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		String mainId = params.getString("mainId","");
		L784M01A l784m01a = null;
		List<L784S01A> l784s01aList = null;
		try {
			locale = LMSUtil.getLocale();
			l784m01a = service9515.findL784m01aByMainId(mainId);
			if(l784m01a == null)
				l784m01a = new L784M01A();
			l784s01aList = service9515.findL784S01AByMainId(mainId);
			prop = MessageBundleScriptCreator.getComponentResource(LMS9515V01Page.class);

			titleRows = this.setL784S01AListData(titleRows, l784s01aList, prop);
			rptVariableMap = this.setL784M01AData(rptVariableMap, l784m01a, prop);

			String logoPath = lmsService.getLogoShowPath(
					UtilConstants.RPTPicType.兆豐LOGO, "00", "");
			rptVariableMap.put("LOGOSHOW", logoPath);
			
			// this.generator.setLang(java.util.Locale.TAIWAN);
			reportTools.setLang(locale);
			reportTools.setVariableData(rptVariableMap);
			reportTools.setRowsData(titleRows);
			// reportTools.checkVariableExist("C:/test.txt", rptVariableMap);
			// this.reportTools.setTestMethod(true);
		}finally{
			
		}
	}
	
	/**
	 * 塞值 TitleRows
	 * 
	 * @param titleRows
	 * @param dataCollection
	 * @return
	 * @throws CapException 
	 */
	private List<Map<String, String>> setL784S01AListData(
			List<Map<String, String>> titleRows,
			List<L784S01A> l784s01aList,Properties propV01) throws CapException {
		try{
			
			Map<String, String> mapInTitleRows = null;
			for(L784S01A l784s01a : l784s01aList){
				mapInTitleRows = Util.setColumnMap();
				mapInTitleRows.put("ReportBean.column01", TWNDate.toAD(l784s01a.getEndDate()));
				mapInTitleRows.put("ReportBean.column02", Util.trim(l784s01a.getCustId()) + Util.trim(l784s01a.getDupNo()));
				mapInTitleRows.put("ReportBean.column03", Util.trim(l784s01a.getCustName()));
				mapInTitleRows.put("ReportBean.column04", Util.trim(l784s01a.getCntrNo()));
				mapInTitleRows.put("ReportBean.column05", Util.trim(l784s01a.getCurrentApplyCurr()) + " " + NumConverter.addComma(l784s01a.getCurrentApplyAmt()));
				mapInTitleRows.put("ReportBean.column06", TWNDate.toAD(l784s01a.getHqCheckDate()));
				mapInTitleRows.put("ReportBean.column07", Util.trim(l784s01a.getHqCheckMemo()));
				titleRows.add(mapInTitleRows);
			}
			if(l784s01aList.isEmpty()){
				mapInTitleRows = Util.setColumnMap();
				titleRows.add(mapInTitleRows);
			}
		}catch(Exception e){
			throw new CapException();
		}
		return titleRows;
	}
	
	/**
	 * 塞值 RptVariableMap
	 * 
	 * @param rptVariableMap
	 * @param dataCollection2
	 * @return
	 * @throws CapException 
	 */
	private Map<String, String> setL784M01AData(
			Map<String, String> rptVariableMap,
			L784M01A l784m01a,Properties propV01) throws CapException {
		try{
			rptVariableMap.put("L784M01A.YEAR", TWNDate.toAD(l784m01a.getDataEDate()).split("-")[0]);
			rptVariableMap.put("L784M01A.MONTH", TWNDate.toAD(l784m01a.getDataEDate()).split("-")[1]);
		}catch(Exception e){
			throw new CapException();
		}
		return rptVariableMap;
	}
	
	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.ReportService#generateReport(
	 */
	@Override
	public OutputStream generateReport(PageParameters params)
			throws CapException {

		ReportGenerator rptGenerator = new ReportGenerator(
				this.getReportTemplateFileName());
		setReportData(rptGenerator, params);
		OutputStream outputStream = null;
		Map<InputStream,Integer> pdfNameMap = new LinkedHashMap<InputStream,Integer>();
		int subLine = 7;
		Properties propEloanPage = null;
		try {
			propEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
			outputStream = rptGenerator.generateReport();
			pdfNameMap.put(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()),subLine);
			if(pdfNameMap != null && pdfNameMap.size() > 0){
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,propEloanPage.getProperty("PaginationText"), true,LMSUtil.getLocale(),subLine,true);
			}
		} catch (Exception ex) {
			throw new CapException(ex.getCause(), ex.getClass());
		} finally {
			if (outputStream != null) {
				try {
					outputStream.close();
				} catch (IOException ex) {
					LOGGER.error("[generateReport]close() Exception!!", ex);
				}
			}

		}
		return outputStream;
	}
}
