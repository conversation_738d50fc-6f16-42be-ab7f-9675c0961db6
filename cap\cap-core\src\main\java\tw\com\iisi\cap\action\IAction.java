/**
 * IAction.java
 *
 * Copyright (c) 2009 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.action;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.IResult;

/**
 * <p>
 * 動作.
 * </p>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/20,iristu,new
 *          </ul>
 */
public interface IAction {

    /**
     * 從Request帶有的資訊調用對應方法
     * 
     * @param params
     *            從Request取得的參數
     * @return
     * @throws CapException
     */
    IResult doWork(PageParameters params) throws CapException;

}
