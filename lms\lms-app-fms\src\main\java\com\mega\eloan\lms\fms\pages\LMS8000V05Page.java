package com.mega.eloan.lms.fms.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 貸後管理作業 - 待辦案件
 *
 * </pre>
 * 
 * @since 2020
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller@RequestMapping(path = "/fms/lms8000v05")
public class LMS8000V05Page extends AbstractEloanInnerView {

	public LMS8000V05Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
//		setGridViewStatus(CreditDocStatusEnum.海外_呈總行);

		// 加上Button
		List<EloanPageFragment> btns = new ArrayList<>();
		// 只有經辦出現的按鈕
		if (!this.isOverSea() && this.getAuth(AuthType.Modify)) {
			btns.add(LmsButtonEnum.Filter);
		}

		// 加上Button
		addToButtonPanel(model, btns);
		
		renderJsI18N(LMS8000V01Page.class);
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS8000V05Page.js" };
	}
}