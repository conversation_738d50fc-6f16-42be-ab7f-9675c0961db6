<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="LMS1405S02Panel07">
		<table width="100%" class="tb2" border="0" cellpadding="0" cellspacing="0">
			<tr class="hd1" style="text-align:left">
				<td>
					<span style="display:none" class="caseSpan">
						<label>
							<input id="tab07" type="checkbox" class="caseBox"></input>
							<th:block th:text="#{'button.modify'}"><!--修改--></th:block>
						</label>
					</span>
					<select id="pageNum5" name="pageNum5" class="nodisabled">
						<option value="0" selected="selected">
							<th:block th:text="#{'L140M01b.printMain'}"><!--印於主表--></th:block>
						</option>
						<option value="1">
							<th:block th:text="#{'L140M01b.print01'}"><!--印於附表(一)--></th:block>
						</option>
						<option value="2">
							<th:block th:text="#{'L140M01b.print02'}"><!--印於附表(二)--></th:block>
						</option>
						<option value="3">
							<th:block th:text="#{'L140M01b.print03'}"><!--印於附表(三)--></th:block>
						</option>
					</select>
				</td>
			</tr>
		</table>
		<!-- J-112-0357 新增敘做條件異動比較表 -->
		<span>
			<font color="red" style="font-size:18px">
				<b>
					1.對於常董會提報案件或擬採表格方式陳述者，請選用分項表格編輯功能，方可於簽報書之相關文件頁籤產製敘做案件異動比較表(word)。<br>
					2.分項表格編輯與自由格式編輯方式建議請擇一辦理。若同時編輯，先列印自由格式編輯內容再列印分項表格編輯內容。
				</b>
			</font>
		</span><br>
		<button type="button" id="btnOpenL140s11a" class="forview">
			<th:block th:text="#{'btn.openL140s11a'}">分項功能</th:block>
		</button>
		<button type="button" id="btnPreviewL140s11a">
			<th:block th:text="#{'btn.previewL140s11a'}">預覽分項表格編輯之敘做案件異動比較表</th:block>
		</button><br>
		<textarea cols="100" rows="10%" id="itemDscr5" name="itemDscr5" class="tckeditor" showType="b" wicket:message="displayMessage:L140S02Tab.7" preview="width:800;heigth:300"></textarea>
		<div id="previewL140s11aBox" style="display:none;">
			<!--弱掃高風險 <span id="previewL140s11aSpan"/>-->
			<!--J-113-0241 ELOAN-額度明細表-敘做條件異動情形-開啟分項表格編輯功能調整-->
			<p id="previewL140s11aSpan"></p>
		</div>
		<div id="l140s11aThickbox" style="display:none;">
			<button type="button" id="addL140s11a">
				<th:block th:text="#{'button.add'}">新增</th:block>
			</button>
			<button type="button" id="deleteL140s11a">
				<th:block th:text="#{'button.delete'}">刪除</th:block>
			</button>
			<button type="button" id="importL140s11a">
				<th:block th:text="#{'btn.importOtherL140s11a'}">引進</th:block>
			</button>
			<button type="button" id="upL140s11aSeq">
				<span class="text-only">
					<th:block th:text="#{'btn.upSeqno'}">向上移動</th:block>
				</span>
			</button>
			<button type="button" id="downL140s11aSeq">
				<span class="text-only">
					<th:block th:text="#{'btn.downSeqno'}">向下移動</th:block>
				</span>
			</button>
			<div id="l140s11aGrid"></div>
		</div>
		<div id="importL140s11aThickbox" style="display:none;">
			<div id="importL140s11aGrid"></div>
		</div>
		<div id="l140s11aDetailThickbox" style="display:none;">
			<table id="l140s11aFormDetail" class="tb2" border="0" cellspacing="0" cellpadding="0" style="width:400px;height:250px;">
				<tbody>
					<span id="l140s11aOid" style="display:none"></span>
					<span id="l140s11aSeqNum" style="display:none"></span>
					<tr class="hd2">
						<td align="center" style="width:10%">
							<th:block th:text="#{'L140S11A.applyItem'}">項目</th:block>
						</td>
						<td align="center" style="width:35%">
							<th:block th:text="#{'L140S11A.befApply'}"></th:block>
						</td>
						<td align="center" style="width:35%">
							<th:block th:text="#{'L140S11A.aftApply'}"></th:block>
						</td>
						<td align="center" style="width:20%">
							<th:block th:text="#{'L140S11A.applyRemark'}"></th:block>
						</td>
					</tr>
					<tr>
						<td>
							<textarea id="applyItem" name="applyItem" cols="10" rows="2" maxlength="120" maxlengthC="100"></textarea>
						</td>
						<td>
							<textarea id="befApply" name="befApply" cols="40" rows="10" maxlength="1500" maxlengthC="600" class="ickeditor"></textarea>
						</td>
						<td>
							<textarea id="aftApply" name="aftApply" cols="40" rows="10" maxlength="1500" maxlengthC="600" class="ickeditor"></textarea>
						</td>
						<td>
							<textarea id="applyRemark" name="applyRemark" cols="40" rows="10" maxlength="1500" maxlengthC="600" class="ickeditor"></textarea>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</th:block>
</body>
</html>
