<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="innerPageBody">
		<script type="text/javascript">
			loadScript('pagejs/lrs/LMS1800M01Page');
		</script>
		<div class="button-menu funcContainer" id="buttonPanel">
			<th:block th:if="${_btnDOC_EDITING_visible}">
				<button id="btnSave" type="button">
					<span class="ui-icon ui-icon-jcs-04" ></span>
					<th:block th:text="#{'button.save'}">儲存</th:block>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02" ></span>
					<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
				</button>
			</th:block>
			<th:block th:if="${_btnWAIT_APPROVE_visible}">
				<button type="button" id="btnAccept">
					<span class="ui-icon ui-icon-check" ></span>
					<th:block th:text="#{'button.accept'}">核可</th:block>
				</button>
				<!-- 退回  -->
				<button type="button" id="btnReturn">
					<span class="ui-icon ui-icon-closethick" ></span>
					<th:block th:text="#{'button.return'}">退回</th:block>
				</button>
			</th:block>
			
			<th:block th:if="${_btnGEN_BATCHNO}">
				<!-- 在傳送分行後，已把 projectNo 上傳至 ELF493且寫入L170M01A，若再重產，會亂掉 -->
				<button type="button" id="btn_genBatchNo">
					<th:block th:text="#{'button.genBatchNo'}">重新產生批號</th:block>
				</button>			
			</th:block>
			
			<button type="button" id="btn_printLatestL140M01A" class="forview">
				<th:block th:text="#{'button.printLatestL140M01A'}">列印分行最新授權外額度批覆書</th:block>
			</button> 
			
			<button type="button" id="btnExit" class="forview">
				<span class="ui-icon ui-icon-jcs-01"></span>
				<th:block th:text="#{'button.exit'}">離開</th:block>
			</button>
		</div>
		<div class="tit2 color-black">
            <table width="100%">
                <tr>
                    <td width="100%">
                        <th:block th:text="#{'doc.title'}">分行覆審名單明細表</th:block>
						<span class="color-blue" id="titleInfo" ></span>
                    </td>
                </tr>
            </table>
        </div>
		
		<div class="tabs doc-tabs">
			<ul>
				<li><a href="#tab-01" goto="01"><b><th:block th:text="#{'doc.tit01'}">文件資訊</th:block></b></a></li>
				<li><a href="#tab-02" goto="02"><b><th:block th:text="#{'doc.tit02'}">覆審名單明細資料</th:block></b></a></li>
				<th:block th:if="${HS_L170M01A}">
					<li><a href="#tab-03" goto="03"><b><th:block th:text="#{'doc.tit03'}">覆審報告表</th:block></b></a></li>						
				</th:block>
				
			</ul>
			<div class="tabCtx-warp">
				<form id="tabForm">
				<div th:id="${tabID}" th:insert="~{${panelName} :: ${panelFragmentName}}"></div>
				</form>
			</div>
		</div>

		<div id="div_printL140M01A" style="display:none">
			<div id="grid_printL140M01A"></div>
		</div>
		<!--===================================-->
		<!--在 S02Panel.html 中不應包含 <form> 的 tag -->
		<div id="div_NEW_NEXTNWDT" style="display:none">
			<form id='div_NEW_NEXTNWDT_form'>
				<table class="tb2" width="100%">
					<tr>
					<td class="hd1" nowrap>
						<th:block th:text="#{'L180M01B.newNextNwDt'}">下次恢復覆審日期</th:block>
					</td>
					<td>
						<input type="text" id="newNextNwDt" name="newNextNwDt" size="8" maxlength="10" class="date required" _requiredLength="10" />
					</td>
					</tr>
				 </table>
			</form>
		</div>
		
		<div id="div_newLRDate" style="display:none">
			<form id='div_newLRDate_form'>
				<span id="div_newLRDate_desc"></span>
				<div>本案變更為要覆審，請輸入上次覆審日</div>				
				<table class="tb2" width="100%">
					<tr>
					<td class="hd1" nowrap>
						<th:block th:text="#{'L180M01B.newLRDate'}">上次覆審日</th:block>
					</td>
					<td>
						<input type="text" id="newLRDate" name="newLRDate" size="8" maxlength="10" class="date" _requiredLength="10" />
						<br/><span id="div_newLRDate_newAddC" class='color-red'>※新案本欄位可空白</span>
					</td>
					</tr>
				 </table>
			</form>
		</div>		
	</th:block>
</body>
</html>
