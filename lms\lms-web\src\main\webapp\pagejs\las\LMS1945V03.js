$(document).ready(function(){

    var grid = $("#gridview").iGrid({
        sortname: 'checkDate',
        sortorder: 'desc',
        height: 380,
        width: "100%",
        multiselect: true,
        autowidth: true,
        handler: "lms1945gridhandler",
        action: "queryViewDocApproved",
        postData: {
            docStatus: viewstatus
        },
        colModel: [{
            colHeader: i18n.lms1945v01['lms1945.001'],// "受檢單位",
            name: 'ownBrId',
            align: "center",
            width: 20,
            sortable: true
        }, {
            colHeader: i18n.lms1945v01['lms1945.002'],// "檢查日期",
            name: 'checkDate',
            align: "center",
            width: 15,
            sortable: true
        }, {
            colHeader: i18n.lms1945v01['lms1945.003'],// "主要借款人統編",
            name: 'custId',
            align: "center",
            width: 20,
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.lms1945v01['lms1945.004'],// "主要借款人名稱",
            name: 'custName',
            align: "center",
            width: 20,
            sortable: true
        }, {
            colHeader: i18n.lms1945v01['lms1945.006'],// "種類",
            name: 'shtType',
            align: "center",
            width: 30,
            sortable: true,
            formatter: function shtTypeFormatter(cellvalue, options, rowObject){
                return i18n.lms1945v01['shttype.name' + cellvalue];
            }
        }, {
            colHeader: i18n.lms1945v01['lms1945.007'],// "對帳單",
            name: 'createBill',
            align: "center",
            width: 10,
            sortable: true
        }, {
            colHeader: i18n.lms1945v01['lms1945.005'],// "檢查人",
            name: 'checkMan',
            align: "center",
            width: 20,
            sortable: true
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'uid',
            hidden: true
        }, {
            name: 'docStatus',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = grid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    // resourceGrid.trigger("reloadGrid")
    
    function openDoc(cellvalue, options, rowObject){
        //    	alert(rowObject.shtType);
        var shtType = rowObject.shtType;
        var url = "";
        
        switch (shtType) {
            case i18n.lms1945v01['shttype.name1']:
                url = "../las/lms1925m01/02";
                break;
            case i18n.lms1945v01['shttype.name2']:
                url = "../las/lms1905m01/02";
                break;
            case i18n.lms1945v01['shttype.name3']:
                url = "../las/lms1915m01/02";
                break;
        }
        
        //ilog.debug(rowObject);
        
        $.form.submit({
            url: url,
            data: {
                mainDocStatus: viewstatus,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid
            },
            target: rowObject.oid
        });
        
    }
    
    $("#buttonPanel").find("#btnReviewAll").click(function(){
    
        var selectRows = grid.getGridParam('selarrrow');
        if (selectRows == "") {
            CommonAPI.showErrorMessage(i18n.def["action_005"]);
            return false;
        }
        API.confirmMessage(i18n.def["confirmSend"], function(result){
            if (result) {
                $.each(selectRows, function(i, data){
                    var ret = grid.getRowData(data);
                    $.ajaxQueue({
                        handler: ret.shtType == i18n.lms1945v01['shttype.name1'] ? "lms1925m01formhandler" : ret.shtType == i18n.lms1945v01['shttype.name2'] ? "lms1905m01formhandler" : "lms1915m01formhandler",
                        action: "accept",
                        data: {
                            mainOid: ret.oid,
                            mainDocStatus: viewstatus
                        },
                        success: function(responseData){
                            if ((i + 1) == selectRows.length) {
                                grid.trigger("reloadGrid");
                            }
                        }
                    });
                });
            }
        });
        
    }).end().find("#btnAdd").click(function(){
    
    }).end().find("#btnPrintAllR01").click(function(){
        var selectRows = grid.getGridParam('selarrrow');
        
        if (selectRows == "") {
            CommonAPI.showErrorMessage(i18n.def["action_005"]);
            return false;
        }
        
        API.confirmMessage(i18n.def["actoin_001"], function(result){
            if (result) {
                var tmp = "";
                $.each(selectRows, function(i, data){
                    var ret = grid.getRowData(data);
                    tmp = tmp + ret.oid + "|";
                });
                
                $.form.submit({
                    url: "../las/lms1945r01",
                    target: "_blank",
                    data: {
                        mainOids: tmp
                    }
                });
            }
        });
    }).end().find("#btnPrintAllR02").click(function(){

        var selectRows = grid.getGridParam('selarrrow');
        
        if (selectRows == "") {
            CommonAPI.showErrorMessage(i18n.def["action_005"]);
            return false;
        }
        var tmp = "";
        var showMessage = "";
        
        $.each(selectRows, function(i, data){
            var ret = grid.getRowData(data);
            var handler = "";
            switch (ret.shtType) {
                case i18n.lms1945v01['shttype.name1']:
                    handler = "lms1925m01formhandler";
                    break;
                case i18n.lms1945v01['shttype.name2']:
                    handler = "lms1905m01formhandler";
                    break;
                case i18n.lms1945v01['shttype.name3']:
                    handler = "lms1915m01formhandler";
                    break;
            }
            
            $.ajaxQueue({
                handler: handler,
                action: "checkPrint",
                data: {
                    mainOid: ret.oid,
                    mainDocStatus: viewstatus
                },
                success: function(responseData){
                	showMessage = showMessage + i18n.lms1945v01['lms1945.002'] + ":" + ret.checkDate + "，" + i18n.lms1945v01['lms1945.003'] + ":" + ret.custId + '：';
                    if (responseData.L192S01A_PRINT_MARK == "Y" || responseData.L192S01A_PRINT_MARK == "P") {
                        tmp = tmp + ret.oid + "|";
                        showMessage = showMessage + "<br>";
                        showMessage = showMessage + responseData.NO_PRINT_DETAIL;
                        showMessage = showMessage + "<br>";
                    } else if (responseData.L192S01A_PRINT_MARK == "N"){
                        showMessage = showMessage + i18n.lms1945v01['lms1945.010'];
                        showMessage = showMessage + "<br>";
                        showMessage = showMessage + responseData.NO_PRINT_DETAIL;
                        showMessage = showMessage + "<br>";
                    }
                    if ((i + 1) == selectRows.length && tmp.length > 0) {
                        API.confirmMessage(showMessage.length == 0 ? i18n.def["actoin_001"] : showMessage, function(result){
                            if (result) {
                                $.form.submit({
                                    url: "../las/lms1945r02",
                                    target: "_blank",
                                    data: {
                                        mainOids: tmp
                                    }
                                });
                                //設定5秒reoload，但如果report印得太慢的話，那麼寫回createBill的速度會比較慢，那可能沒效果
                                setTimeout(function(){
                                    grid.trigger("reloadGrid");
                                }, 5000);
                            }
                        });
                    }
                    else 
                        if ((i + 1) == selectRows.length && showMessage.length > 0) {
                            API.showMessage(showMessage);
							grid.trigger("reloadGrid");
                        }
                }
            });
        });
    });
    
    
});


