package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 疑似人頭戶檢核明細紀錄檔 **/
@Entity
@Table(name="L140MC1A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","itemCode"}))
public class L140MC1A implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	
	public L140MC1A(){
	}
	
	public L140MC1A(String itemCode){
		this.itemCode = itemCode;
	}
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;
	
	/** 文件編號 */
	@Column(length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;
	
	/**
	 * 檢核項目代碼 , select * from com.BCODETYPE where CODETYPE = 'cls1141_headAccountCheckItem'
	 */
	@Column(name = "ITEMCODE", length = 1, columnDefinition = "CHAR(1)")
	private String itemCode;
	
	/**
	 * 檢核項目結果代碼(1-是, 0-否, NA不適用用)
	 */
	@Size(max=2)
	@Column(name = "RESULT", length = 2, columnDefinition = "VARCHAR(2)")
	private String result;

	/** 備註 **/
	@Size(max=300)
	@Column(name="REMARKS", length=300, columnDefinition="VARCHAR(300)")
	private String remarks;
	
	/**
	 * 版本
	 */
	@Size(max=20)
	@Column(name = "VERSION", length = 20, columnDefinition = "VARCHAR(20)")
	private String version;
	
	/**
	 * 異動時間
	 */
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	

	public String getCntrNo() {
		return cntrNo;
	}

	public void setCntrNo(String cntrNo) {
		this.cntrNo = cntrNo;
	}

	public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String itemCode) {
		this.itemCode = itemCode;
	}

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public Timestamp getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Timestamp updateTime) {
		this.updateTime = updateTime;
	}

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

}
