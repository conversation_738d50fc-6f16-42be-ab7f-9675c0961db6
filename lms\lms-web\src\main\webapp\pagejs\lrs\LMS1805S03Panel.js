$(document).ready(function(){
	$("#lms1805s03").iGrid({
		handler : 'lms1805gridhandler',
		height : 350,
		sortname : 'uploadTime',
		postData : {
			mainId : responseJSON.mainId,
			formAction : "queryDocFile"
		},
		colModel : [ {
			colHeader : i18n.lms1805m01['docfile.filename'],//"檔名"
			name : 'srcFileName',
			width : 100,
			formatter : 'click',
			onclick : openDoc
		}, {
			colHeader : i18n.lms1805m01['docfile.uploadTime'],//"上傳時間"
			name : 'uploadTime',
			width : 165,
			sortable : true
		}, {
			colHeader : "oid",
			name : 'oid',
			hidden : true
		}],
		ondblClickRow : function(rowid){
			openDoc(null, null, $("#lms1805s03").getRowData(rowid));
		}
	});
	
	function openDoc(cellvalue, options, rowObject) {
		ilog.debug(rowObject);
		$.capFileDownload({
	        handler : "simplefiledwnhandler",
	        data : {
	            fileOid : rowObject.oid
	        }
		});
	};
});