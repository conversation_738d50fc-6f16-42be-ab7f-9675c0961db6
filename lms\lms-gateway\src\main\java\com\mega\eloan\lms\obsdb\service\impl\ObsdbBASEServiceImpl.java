package com.mega.eloan.lms.obsdb.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbBASEService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class ObsdbBASEServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbBASEService {

	/**
	 * J-105-0202-001 Web e-Loan企金授信修改客戶統編。
	 * 
	 * @param BRNID
	 * @param orgCustId
	 * @param orgDupNo
	 * @param newCustId
	 * @param newDupNo
	 * @param documentNo
	 * @param cntrNo
	 * @param rptMainId
	 */
	@Override
	public void updateJ1050202_by_custIdAndDupNo_byCaseBrId(String BRNID,
			String orgCustId, String orgDupNo, String newCustId,
			String newDupNo, String documentNo, String cntrNo, String rptMainId) {

		String fullCustKeyOrg = Util.getLeftStr(orgCustId + "          ", 10)
				+ orgDupNo;
		String fullCustKeyNew = Util.getLeftStr(newCustId + "          ", 10)
				+ newDupNo;

		// 依照簽案行

		// ELF404(ELCSECNT)(授信案件統計檔)

		// ELF461(ELLNSEEK)-企消金狀態報送檔
		this.getJdbc(BRNID)
				.update("J1050202.update_ELF461_01",
						new String[] { newCustId, newDupNo, orgCustId,
								orgDupNo, cntrNo });

	}

	/**
	 * J-105-0202-001 Web e-Loan企金授信修改客戶統編。
	 * 
	 * @param BRNID
	 * @param orgCustId
	 * @param orgDupNo
	 * @param newCustId
	 * @param newDupNo
	 * @param documentNo
	 * @param cntrNo
	 * @param rptMainId
	 */
	@Override
	public void updateJ1050202_by_custIdAndDupNo_byCntrBranch(
			Map<String, Object> BRNIDs, String orgCustId, String orgDupNo,
			String newCustId, String newDupNo, String documentNo,
			String cntrNo, String rptMainId) {

		String fullCustKeyOrg = Util.getLeftStr(orgCustId + "          ", 10)
				+ orgDupNo;
		String fullCustKeyNew = Util.getLeftStr(newCustId + "          ", 10)
				+ newDupNo;

		for (String BRNID : BRNIDs.keySet()) {
			this.getJdbc(BRNID).update("J1050202.update_ELF506_01",
					new String[] { fullCustKeyNew, cntrNo });
		}

	}

	@Override
	public void updateJ1050202_by_custIdAndDupNo_byDrawdownBranch(
			Map<String, Object> BRNIDs, String orgCustId, String orgDupNo,
			String newCustId, String newDupNo, String documentNo,
			String cntrNo, String rptMainId) {

		String fullCustKeyOrg = Util.getLeftStr(orgCustId + "          ", 10)
				+ orgDupNo;
		String fullCustKeyNew = Util.getLeftStr(newCustId + "          ", 10)
				+ newDupNo;

		for (String BRNID : BRNIDs.keySet()) {
			try {
				// ELF383 授信額度檔
				this.getJdbc(BRNID).update(
						"J1050202.update_ELF383_01",
						new String[] { newCustId, newDupNo, orgCustId,
								orgDupNo, cntrNo });
			} catch (Exception e) {

			}

			try {
				// ELF384 科(子)目及其限額檔
				this.getJdbc(BRNID).update(
						"J1050202.update_ELF384_01",
						new String[] { newCustId, newDupNo, orgCustId,
								orgDupNo, cntrNo });
			} catch (Exception e) {

			}

			// ELF385 聯貸案參貸比率檔--無ID不用改

			try {
				// ELF388 核准額度資料檔
				this.getJdbc(BRNID).update(
						"J1050202.update_ELF388_01",
						new String[] { newCustId, newDupNo, orgCustId,
								orgDupNo, cntrNo });
			} catch (Exception e) {

			}

			try {
				// ELF164 授信利率檔
				this.getJdbc(BRNID)
						.update("J1050202.update_ELF164_01",
								new String[] { fullCustKeyNew, fullCustKeyOrg,
										cntrNo });
			} catch (Exception e) {

			}

			// ELF476 企金簽案費率檔--無ID不用改

			try {
				// ELF422(QUOTAINF)
				this.getJdbc(BRNID).update(
						"J1050202.update_ELF422_01",
						new String[] { newCustId, newDupNo, orgCustId,
								orgDupNo, cntrNo });
			} catch (Exception e) {

			}

		}

	}

	/**
	 * G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
	 * 
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0012(String BRNID) {
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF164 SET ELF164_BR_NO = (CASE ELF164_BR_NO WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE ELF164_BR_NO END) WHERE ELF164_BR_NO IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF164 SET ELF164_CONTRACT = (CASE LEFT(ELF164_CONTRACT,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF164_CONTRACT,3) END) || RIGHT(ELF164_CONTRACT,9) WHERE LEFT(ELF164_CONTRACT,3) IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF383 SET ELF383_CNTRNO = (CASE LEFT(ELF383_CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF383_CNTRNO,3) END) || RIGHT(ELF383_CNTRNO,9) WHERE LEFT(ELF383_CNTRNO,3) IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF383 SET ELF383_CRDTBR = (CASE ELF383_CRDTBR WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE ELF383_CRDTBR END) WHERE ELF383_CRDTBR IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF383 SET ELF383_MOWBR  = (CASE ELF383_MOWBR WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE ELF383_MOWBR END) WHERE ELF383_MOWBR IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF384 SET ELF384_CNTRNO = (CASE LEFT(ELF384_CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF384_CNTRNO,3) END) || RIGHT(ELF384_CNTRNO,9) WHERE LEFT(ELF384_CNTRNO,3) IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF385 SET ELF385_CNTRNO = (CASE LEFT(ELF385_CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF385_CNTRNO,3) END) || RIGHT(ELF385_CNTRNO,9) WHERE LEFT(ELF385_CNTRNO,3) IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF388 SET ELF388_QUOTANO = (CASE LEFT(ELF388_QUOTANO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF388_QUOTANO,3) END) || RIGHT(ELF388_QUOTANO,9) WHERE LEFT(ELF388_QUOTANO,3) IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF404 SET ELF404_CNTRNO = (CASE LEFT(ELF404_CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF404_CNTRNO,3) END) || RIGHT(ELF404_CNTRNO,9) WHERE LEFT(ELF404_CNTRNO,3) IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF404 SET ELF404_BRNO = (CASE ELF404_BRNO WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE ELF404_BRNO END) WHERE ELF404_BRNO IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF422 SET ELF422_CNTRNO = (CASE LEFT(ELF422_CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF422_CNTRNO,3) END) || RIGHT(ELF422_CNTRNO,9) WHERE LEFT(ELF422_CNTRNO,3) IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF422 SET ELF422_BRANCH = (CASE ELF422_BRANCH WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE ELF422_BRANCH END) WHERE ELF422_BRANCH IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF461 SET ELF461_CNTRNO = (CASE LEFT(ELF461_CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF461_CNTRNO,3) END) || RIGHT(ELF461_CNTRNO,9) WHERE LEFT(ELF461_CNTRNO,3) IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF461 SET ELF461_BRNO = (CASE ELF461_BRNO WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE ELF461_BRNO END) WHERE ELF461_BRNO IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF476 SET ELF476_CNTRNO = (CASE LEFT(ELF476_CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF476_CNTRNO,3) END) || RIGHT(ELF476_CNTRNO,9) WHERE LEFT(ELF476_CNTRNO,3) IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF386 SET ELF386_CNTRNO = (CASE LEFT(ELF386_CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF386_CNTRNO,3) END) || RIGHT(ELF386_CNTRNO,9) WHERE LEFT(ELF386_CNTRNO,3) IN ('Z01','Z03') ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF506 SET ELF506_CNTRNO = (CASE LEFT(ELF506_CNTRNO,3)  WHEN 'Z01' THEN '0D7' WHEN 'Z03' THEN '0D8' ELSE LEFT(ELF506_CNTRNO,3) END) || RIGHT(ELF506_CNTRNO,9) WHERE LEFT(ELF506_CNTRNO,3) IN ('Z01','Z03') ",
						new Object[] {});

	}

	/**
	 * G-107-0115-001 Web e-Loan 授信系統配合巴箇行整併調整簽報書相關資料
	 * 
	 */
	@NonTransactional
	@Override
	public void doLmsBatch0013(String BRNID) {

		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF164 SET ELF164_BR_NO = '0A5' WHERE ELF164_BR_NO IN ('0A6')",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF164 SET ELF164_CONTRACT = '0A5'||SUBSTR(ELF164_CONTRACT,4,4)||( CASE SUBSTR(ELF164_CONTRACT,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF164_CONTRACT,8,1) END)||SUBSTR(ELF164_CONTRACT,9,4) WHERE LEFT(ELF164_CONTRACT,3) IN ('0A6') AND SUBSTR(ELF164_CONTRACT,8,1) IN ('0','5') AND ELF164_CONTRACT IS NOT NULL  ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF383 SET ELF383_CNTRNO = '0A5'||SUBSTR(ELF383_CNTRNO,4,4)||( CASE SUBSTR(ELF383_CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF383_CNTRNO,8,1) END)||SUBSTR(ELF383_CNTRNO,9,4) WHERE LEFT(ELF383_CNTRNO,3) IN ('0A6') AND SUBSTR(ELF383_CNTRNO,8,1) IN ('0','5') AND ELF383_CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF383 SET ELF383_CRDTBR = '0A5' WHERE ELF383_CRDTBR IN ('0A6')    ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF383 SET ELF383_MOWBR  = '0A5' WHERE ELF383_MOWBR IN ('0A6')    ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF384 SET ELF384_CNTRNO = '0A5'||SUBSTR(ELF384_CNTRNO,4,4)||( CASE SUBSTR(ELF384_CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF384_CNTRNO,8,1) END)||SUBSTR(ELF384_CNTRNO,9,4) WHERE LEFT(ELF384_CNTRNO,3) IN ('0A6') AND SUBSTR(ELF384_CNTRNO,8,1) IN ('0','5') AND ELF384_CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF385 SET ELF385_CNTRNO = '0A5'||SUBSTR(ELF385_CNTRNO,4,4)||( CASE SUBSTR(ELF385_CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF385_CNTRNO,8,1) END)||SUBSTR(ELF385_CNTRNO,9,4) WHERE LEFT(ELF385_CNTRNO,3) IN ('0A6') AND SUBSTR(ELF385_CNTRNO,8,1) IN ('0','5') AND ELF385_CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF388 SET ELF388_QUOTANO = '0A5'||SUBSTR(ELF388_QUOTANO,4,4)||( CASE SUBSTR(ELF388_QUOTANO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF388_QUOTANO,8,1) END)||SUBSTR(ELF388_QUOTANO,9,4) WHERE LEFT(ELF388_QUOTANO,3) IN ('0A6') AND SUBSTR(ELF388_QUOTANO,8,1) IN ('0','5') AND ELF388_QUOTANO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF404 SET ELF404_CNTRNO = '0A5'||SUBSTR(ELF404_CNTRNO,4,4)||( CASE SUBSTR(ELF404_CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF404_CNTRNO,8,1) END)||SUBSTR(ELF404_CNTRNO,9,4) WHERE LEFT(ELF404_CNTRNO,3) IN ('0A6') AND SUBSTR(ELF404_CNTRNO,8,1) IN ('0','5') AND ELF404_CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF404 SET ELF404_BRNO = '0A5' WHERE ELF404_BRNO IN ('0A6')    ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF422 SET ELF422_CNTRNO = '0A5'||SUBSTR(ELF422_CNTRNO,4,4)||( CASE SUBSTR(ELF422_CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF422_CNTRNO,8,1) END)||SUBSTR(ELF422_CNTRNO,9,4) WHERE LEFT(ELF422_CNTRNO,3) IN ('0A6') AND SUBSTR(ELF422_CNTRNO,8,1) IN ('0','5') AND ELF422_CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF422 SET ELF422_BRANCH = '0A5' WHERE ELF422_BRANCH IN ('0A6')    ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF461 SET ELF461_CNTRNO = '0A5'||SUBSTR(ELF461_CNTRNO,4,4)||( CASE SUBSTR(ELF461_CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF461_CNTRNO,8,1) END)||SUBSTR(ELF461_CNTRNO,9,4) WHERE LEFT(ELF461_CNTRNO,3) IN ('0A6') AND SUBSTR(ELF461_CNTRNO,8,1) IN ('0','5') AND ELF461_CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF461 SET ELF461_BRNO = '0A5' WHERE ELF461_BRNO IN ('0A6')    ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF476 SET ELF476_CNTRNO = '0A5'||SUBSTR(ELF476_CNTRNO,4,4)||( CASE SUBSTR(ELF476_CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF476_CNTRNO,8,1) END)||SUBSTR(ELF476_CNTRNO,9,4) WHERE LEFT(ELF476_CNTRNO,3) IN ('0A6') AND SUBSTR(ELF476_CNTRNO,8,1) IN ('0','5') AND ELF476_CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF386 SET ELF386_CNTRNO = '0A5'||SUBSTR(ELF386_CNTRNO,4,4)||( CASE SUBSTR(ELF386_CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF386_CNTRNO,8,1) END)||SUBSTR(ELF386_CNTRNO,9,4) WHERE LEFT(ELF386_CNTRNO,3) IN ('0A6') AND SUBSTR(ELF386_CNTRNO,8,1) IN ('0','5') AND ELF386_CNTRNO IS NOT NULL  ",
						new Object[] {});
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF506 SET ELF506_CNTRNO = '0A5'||SUBSTR(ELF506_CNTRNO,4,4)||( CASE SUBSTR(ELF506_CNTRNO,8,1) WHEN '0' THEN '3' WHEN '5' THEN '8' ELSE SUBSTR(ELF506_CNTRNO,8,1) END)||SUBSTR(ELF506_CNTRNO,9,4) WHERE LEFT(ELF506_CNTRNO,3) IN ('0A6') AND SUBSTR(ELF506_CNTRNO,8,1) IN ('0','5') AND ELF506_CNTRNO IS NOT NULL  ",
						new Object[] {});

	}

	// G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	@Override
	public Map<String, Object> findElf515ByCntrNoForBt(String BRNID,
			String cntrNo) {

		return this.getJdbc(BRNID).queryForMap("ELF515.findByCntrNoForBt",
				new String[] { cntrNo });
	}

	// G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	@Override
	public void updateElf515ByCntrNoByCntrNoForBt(String BRNID,
			String newCntrNo, String oldCntrNo, String modifyUnit) {
		this.getJdbc(BRNID).update(
				"ELF515.updateCntrNoByCntrNoForBt",
				new Object[] { oldCntrNo, newCntrNo, oldCntrNo, newCntrNo,
						modifyUnit, oldCntrNo, oldCntrNo });
	}

	/**
	 * J-109-0341_05097_B1001 Web e-Loan異常通報增加授信戶信評大幅貶落事項並傳送國內海外帳務系統
	 */
	public void updateUnNormal4(String BRNID, String mainId, String custId,
			String dupNo, String caseBrid, String caseNo,
			boolean canCloseAllBranch) {

		// J-110-0398_05097_B1001 Web e-Loan配合異常通報須知修訂, 修改異常通報之解除方式
		String updateMdBrno = "";
		if (canCloseAllBranch) {
			updateMdBrno = " "; // 全行未結案都一起結案
		} else {
			//updateMdBrno = " AND LNFE0851_MDBRNO= '" + caseBrid + "' "; // 只結案該分行
			updateMdBrno = " AND LNFE0851_MDBRNO= ? "; // 只結案該分行
		}

		this.getJdbc(BRNID).updateByCustParam(
				"LNFE0851.updateUnNormal4",
				new Object[] { updateMdBrno },
				canCloseAllBranch?
						new Object[] {
								"Y",
								new BigDecimal(CapDate.formatDate(
										CapDate.parseSQLDate(new Date()), "yyyyMMdd")),
								caseNo, mainId, custId, dupNo }:
						new Object[] {
								"Y",
								new BigDecimal(CapDate.formatDate(
										CapDate.parseSQLDate(new Date()), "yyyyMMdd")),
								caseNo, mainId, custId, dupNo, caseBrid });

	}

	/**
	 * J-109-0341_05097_B1001 Web e-Loan異常通報增加授信戶信評大幅貶落事項並傳送國內海外帳務系統
	 */
	@Override
	public void update0851UnNormal5(String BRNID, String isClosed,
			String closeDate, String closeCaseNo, String closeMainId,
			String mainId, String custId, String dupNo, String caseBrid) {
		this.getJdbc(BRNID)
				.update("LNFE0851.uploadUnNormal5",
						new Object[] {
								isClosed,
								Util.equals(Util.trim(closeDate), "") ? 0
										: new BigDecimal(CapDate.formatDate(
												Util.parseDate(closeDate),
												"yyyyMMdd")), closeCaseNo,
								closeMainId, custId, dupNo, caseBrid, mainId });

	}

	/**
	 * J-109-0341_05097_B1001 Web e-Loan異常通報增加授信戶信評大幅貶落事項並傳送國內海外帳務系統
	 */
	@Override
	public boolean selUnNormal4(String BRNID, String custId, String dupNo,
			String brno) {
		List<Map<String, Object>> mapList = this.getJdbc(BRNID).queryForList(
				"LNFE0851.selUnNormal4", new String[] { custId, dupNo, brno });
		if (mapList.isEmpty()) {
			return false;
		} else {
			return true;
		}
	}

	/**
	 * J-109-0341_05097_B1001 Web e-Loan異常通報增加授信戶信評大幅貶落事項並傳送國內海外帳務系統
	 */
	@Override
	public void insertUnNormal4(String BRNID, String custId, String dupNo,
			String custName, String brno, long lostAmt, String collStat,
			String process, String sameIdea, Date createTime, String mdClass,
			String ndCode) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		this.getJdbc(BRNID)
				.update("LNFE0851.uploadUnNormal4",
						new Object[] {
								custId,
								dupNo,
								custName,
								brno,
								lostAmt,
								// 因異常通報擴大欄位長度造成上傳LNFE0851資料過長，
								// 建霖說上傳 LNFE0851時全部截掉。 Miller added at
								// 2013/02/26
								Util.trimSizeInOS390(collStat, 102),
								Util.trimSizeInOS390(process, 202),
								Util.trimSizeInOS390(sameIdea, 202),
								new BigDecimal(CapDate.formatDate(
										CapDate.parseSQLDate(new Date()),
										"yyyyMMdd")),
								user.getUserId().substring(1),
								"",
								"",
								"",
								"",
								createTime == null ? 0 : new BigDecimal(CapDate
										.formatDate(createTime, "yyyyMMdd")),
								(mdClass.length() > 0 && mdClass.charAt(0) == '0') ? mdClass
										.substring(1) : mdClass, "", 0, "", "",
								ndCode });

	}

	/**
	 * J-109-0341_05097_B1001 Web e-Loan異常通報增加授信戶信評大幅貶落事項並傳送國內海外帳務系統
	 * 
	 * @param custId
	 * @param dupNo
	 * @param caseBrid
	 * @param ndCode
	 */
	@Override
	public void update0851UnNormaData(String BRNID, String custId,
			String dupNo, String caseBrid, String ndCode) {
		this.getJdbc(BRNID).update("LNFE0851.updateUnNormaData",
				new Object[] { ndCode, custId, dupNo, caseBrid });

	}

	/**
	 * J-111-0423_05097_B1001 Web
	 * e-Loan企金授信就海外分行承做永續績效連結授信案(如附件)，於E-Loan「永續績效連結授信」相關註記
	 * 
	 */
	@Override
	public void updateEsgDataFromLms2105v01ServiceImpl(String BRNID,
			String cntrNo, String esgSustainLoan, String esgSustainLoanType,
			String esgSustainLoanUnReach) {
		this.getJdbc(BRNID)
				.update("UPDATE #SCHEMA#.ELF383 SET ELF383_ESGSFG = ?,ELF383_ESGSTYPE = ?,ELF383_ESGSUNRE=? WHERE ELF383_CNTRNO =? ",
						new Object[] { esgSustainLoan, esgSustainLoanType,
								esgSustainLoanUnReach, cntrNo });

	}

	/**
	 * J-111-0633_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
	 * 
	 * @param cntrNo
	 * @param prodKind
	 * @param adcCaseNo
	 */
	@Override
	public void updateAdcInfo(String BRNID, String cntrNo,
			String prodKind, String adcCaseNo) {
		this.getJdbc(BRNID).update("ELF506.update_PROD_KIND_ByCntrNo",
				new Object[] { prodKind, adcCaseNo, cntrNo });

	}

}
