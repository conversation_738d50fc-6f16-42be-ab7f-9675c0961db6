var col = [ "brno", "custId", "dupNo", "unDocId", "areaNo",
			"cName", "appDate", "appMoney", "favloan", "noHouse","apprDate", "favcntno",
			"orcntno1", "orcntno2", "updater", "tmestamp", "kindNo",
			"loanDate", "cntrno", "sellerId" ]
var pageAction = {
	handler : 'lms9541v04formhandler',
	/**
	 * 起始畫面
	 */
	initBox : function(){
		//開啟畫面
		$("div#initThickBox").thickbox({
			title : i18n.lms9541v04["initTitle"],
			width : 550,
			height : 260,
			modal : true,
			align : 'center',
			valign: 'bottom',
			i18n: i18n.lms9541v04,
			buttons : {
				'importXls': function(){//匯入excel資料 
					MegaApi.uploadDialog({
						handler:"lms9541fileuploadhandler",
						fieldId: "importData",
				        fieldIdHtml: "size='30'",
				        width: 320,
				        height: 190,
				        data: {
				           
				        },
				        success: function(response){
				        	if(response["errorMsg"]){
				        		if(response["errorMsg"]!="someError"){
				        			MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.lms9541v04[response["errorMsg"]]);
				        		}
				        		else{
				        			MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.lms9541v04["row"]+response["line"]+i18n.lms9541v04["someError"]);
				        		}
				        	}
				        		
				        }
					});
				},
				'sure' : function(){//
					if($("#initForm").valid()){
						$.ajax({
							handler : pageAction.handler,
							action : 'search',
							form : 'initForm',
							data : {},
							success:function(response){
								if(response.isEmpty){
									MegaApi.showErrorMessage(i18n.def["confirmTitle"],i18n.def["noData"]);
								}
								else{
									$.thickbox.close();
									pageAction.openDetail(response);
								}
							}
						});
					}
				},
				'close' : function(){//關閉
					$.thickbox.close();
				}
			}
		});
	},
	/**
	 * 查詢結果
	 */
	openDetail : function(data){
		//將值帶入
		var form = $("#detailForm");
		for(var i=0 ; i<col.length ; i++){
			form.find("#"+col[i]).val(data[col[i]]);
		}
		//radio額外處理(上面CODE對radio無功能...)
		$("input[name='noHouse'][value='"+data["noHouse"]+"']").attr("checked",true);
		$("input[name='orcntno2'][value='"+data["orcntno2"]+"']").attr("checked",true);
		//開啟畫面
		$("div#detailThickBox").thickbox({
			title : i18n.lms9541v04["detailTitle"],
			width : 630,
			height : 430,
			modal : true,
			align : 'left',
			valign: 'top',
			i18n: i18n.lms9541v04,
			buttons : {
				
				'save' : function(){//儲存
					if(form.valid()){
						$.ajax({
							handler : pageAction.handler,
							action : 'save',
							form : form,
							data : {},
							success:function(response){
								if(response.success){
									MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["saveSuccess"]);
								}else{
									MegaApi.showErrorMessage(i18n.def["confirmTitle"],i18n.lms9541v04["saveError"]);
								}
							}
						});
					}
				},
				'close' : function(){//關閉
					$.thickbox.close();
				}
			}
		});
	}
}

$(function() {
	pageAction.initBox();
});