<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:util="http://www.springframework.org/schema/util" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
    	   http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

<!-- 	<bean id="UnlockDocRService" class="com.mega.eloan.common.remote.impl.UnlockDocRServiceImpl" />
	<bean id="LockDocRService" class="com.mega.eloan.common.remote.impl.LockDocRServiceImpl" />
	<bean id="CacheReloadRService" class="com.mega.eloan.common.remote.impl.CacheReloadRServiceImpl" /> -->

	<context:component-scan base-package="com.mega.eloan.common.remote.impl.**" />

</beans>