package com.mega.eloan.lms.crs.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;

/**
 * <pre>
 * 個金覆審控制檔 - 待覆核
 * </pre>
 * 
 * @since 2024/12/20
 * <AUTHOR> Assistant
 * @version <ul>
 *          <li>2024/12/20,AI Assistant,new - 簡化版本，只保留篩選功能
 *          </ul>
 */
@Controller
@RequestMapping("/crs/lms2430v02")
public class LMS2430V02Page extends AbstractEloanInnerView {

	public LMS2430V02Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(RetrialDocStatusEnum.待覆核);
		
		// 簡化版本：只保留篩選功能，移除整批覆核和調閱功能
		addToButtonPanel(model, LmsButtonEnum.Filter);
		
		renderJsI18N(LMS2430V01Page.class);
		renderJsI18N(LMS2430V02Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/crs/LMS2430V01Page');");
	}

}
