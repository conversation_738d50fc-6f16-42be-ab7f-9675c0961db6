package com.mega.eloan.lms.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** <pre>同一通訊處註記主檔(供分行輸入 查證結果 的主檔)
 * </pre>
 * 
 * @since 2019/02
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/02, J-107-0129 , 經由批次 SLMS-00074 產出控制檔（C900S02E、C900S02F）
 *          </li>　
 *          <li>分行在檢視報表「消金借款人留存同一通訊處未註記清單」之後，會跑  LMS.C310M01A 的簽核流程，把 查證結果 回寫到控制檔（LMS.C900S02E）
 *          </li>
 *          </ul>
 */
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C310M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C310M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	@Column(name = "REFMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String refMainId;
	
	/** 查證結果{Y:正常, N異常} **/
	@Column(name="CHK_RESULT", length=1, columnDefinition="CHAR(1)")
	private String chk_result;
	
	@Column(name="CHK_MEMO", length=300, columnDefinition="CHAR(300)")
	private String chk_memo;

	public String getRefMainId() {
		return refMainId;
	}

	public void setRefMainId(String refMainId) {
		this.refMainId = refMainId;
	}

	public String getChk_result() {
		return chk_result;
	}

	public void setChk_result(String chk_result) {
		this.chk_result = chk_result;
	}

	public String getChk_memo() {
		return chk_memo;
	}

	public void setChk_memo(String chk_memo) {
		this.chk_memo = chk_memo;
	}
	
}
