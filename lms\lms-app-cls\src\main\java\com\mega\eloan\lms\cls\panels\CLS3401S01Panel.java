
package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 消金契約書
 * </pre>
 * 
 * @since 2020/02/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/02/14,EL08034,new
 *          </ul>
 */
public class CLS3401S01Panel extends Panel {

	public CLS3401S01Panel(String id) {
		super(id);
	}

	public CLS3401S01Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		new DocLogPanel("_docLog").processPanelData(model, params);
	}

	/**/
	private static final long serialVersionUID = 1L;
}
