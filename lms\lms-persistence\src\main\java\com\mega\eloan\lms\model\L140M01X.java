/* 
 * L140M01X.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 高價住宅貸款檢核檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140M01X", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo"}))
public class L140M01X extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custid;

	/** 身分證統編重複碼 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 本案是否無寬限期 **/
	@Size(max=1)
	@Column(name="ISGRACEPERIOD", length=1, columnDefinition="CHAR(1)")
	private String isGracePeriod;

	/** 本案利率未低於年息1.72% **/
	@Size(max=1)
	@Column(name="ISGTSPECIFICINTEREST", length=1, columnDefinition="CHAR(1)")
	private String isGtSpecificInterest;

	/** 貸款額度是否以高價住宅鑑價金額或買賣金額較低者之6成核計 **/
	@Size(max=1)
	@Column(name="ISCALCULATEDATLOWERAMOUNT", length=1, columnDefinition="CHAR(1)")
	private String isCalculatedAtLowerAmount;

	/** 買賣金額 **/
	@Column(name="DEALAMOUNT", columnDefinition="DECIMAL(14,0)" )
	private BigDecimal dealAmount;

	/** 鑑估價格 **/
	@Column(name="APPRAISEPRICE", columnDefinition="DECIMAL(14,0)" )
	private BigDecimal appraisePrice;

	/** 本案核貸額度 **/
	@Column(name="APPROVEDLOANQUOTA", columnDefinition="DECIMAL(14,0)" )
	private BigDecimal approvedLoanQuota;

	/** 申貸約當期間是否有另案增貸或新貸案件額度 **/
	@Size(max=1)
	@Column(name="ISNEWORADDLOAN", length=1, columnDefinition="CHAR(1)")
	private String isNewOrAddLoan;

	/** 增貸或新貸案件額度 **/
	@Column(name="NEWORADDLOANQUOTA", columnDefinition="DECIMAL(14,0)" )
	private BigDecimal newOrAddLoanQuota;

	/** 是否有其他既有貸款 **/
	@Size(max=1)
	@Column(name="ISEXISTINGLOAN", length=1, columnDefinition="CHAR(1)")
	private String isExistingLoan;

	/** 既有貸款額度 **/
	@Column(name="EXISTINGLOANQUOTA", columnDefinition="DECIMAL(14,0)" )
	private BigDecimal existingLoanQuota;

	/** 是否有其他金融機構貸款 **/
	@Size(max=1)
	@Column(name="ISOTHERAGENCYLOAN", length=1, columnDefinition="CHAR(1)")
	private String isOtherAgencyLoan;

	/** 其他金融機構貸款額度 **/
	@Column(name="OTHERAGENCYLOANQUOTA", columnDefinition="DECIMAL(14,0)" )
	private BigDecimal otherAgencyLoanQuota;

	/** 是否提供十成存款設質之貸款 **/
	@Size(max=1)
	@Column(name="ISDEPOSITPLEDGEDLOANS", length=1, columnDefinition="CHAR(1)")
	private String isDepositPledgedLoans;

	/** 是否小於原貸額度 **/
	@Size(max=1)
	@Column(name="ISLEORIGINALLOANQUOTA", length=1, columnDefinition="CHAR(1)")
	private String isLeOriginalLoanQuota;

	/** 是否非行家理財-短期擔保案件或循環額度承做 **/
	@Size(max=1)
	@Column(name="ISNOTSPECIFICPROJECTCOMMIT", length=1, columnDefinition="CHAR(1)")
	private String isNotSpecificProjectCommit;

	/** 是否有向借戶說明貸款須知 **/
	@Size(max=1)
	@Column(name="ISEXPLAINTOLENDER", length=1, columnDefinition="CHAR(1)")
	private String isExplainToLender;

	/** 其他事項說明 **/
	@Size(max=3000)
	@Column(name="DESCRIPTION", length=3000, columnDefinition="VARCHAR(3000)")
	private String description;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;
	
	/** 本案資金用途是否屬股權收購 **/
	@Size(max=1)
	@Column(name="ISSHAREACQUISITION", length=1, columnDefinition="CHAR(1)")
	private String isShareAcquisition;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定身分證統編 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}
	
	/** 取得本案是否無寬限期 **/
	public String getIsGracePeriod() {
		return this.isGracePeriod;
	}
	/** 設定本案是否無寬限期 **/
	public void setIsGracePeriod(String value) {
		this.isGracePeriod = value;
	}

	/** 取得本案利率未低於年息1.72% **/
	public String getIsGtSpecificInterest() {
		return this.isGtSpecificInterest;
	}
	/** 設定本案利率未低於年息1.72% **/
	public void setIsGtSpecificInterest(String value) {
		this.isGtSpecificInterest = value;
	}

	/** 取得貸款額度是否以高價住宅鑑價金額或買賣金額較低者之6成核計 **/
	public String getIsCalculatedAtLowerAmount() {
		return this.isCalculatedAtLowerAmount;
	}
	/** 設定貸款額度是否以高價住宅鑑價金額或買賣金額較低者之6成核計 **/
	public void setIsCalculatedAtLowerAmount(String value) {
		this.isCalculatedAtLowerAmount = value;
	}

	/** 取得買賣金額 **/
	public BigDecimal getDealAmount() {
		return dealAmount;
	}
	
	/** 設定買賣金額 **/
	public void setDealAmount(BigDecimal dealAmount) {
		this.dealAmount = dealAmount;
	}

	/** 取得鑑估價格 **/
	public BigDecimal getAppraisePrice() {
		return this.appraisePrice;
	}
	/** 設定鑑估價格 **/
	public void setAppraisePrice(BigDecimal value) {
		this.appraisePrice = value;
	}

	/** 取得本案核貸額度 **/
	public BigDecimal getApprovedLoanQuota() {
		return this.approvedLoanQuota;
	}
	/** 設定本案核貸額度 **/
	public void setApprovedLoanQuota(BigDecimal value) {
		this.approvedLoanQuota = value;
	}

	/** 取得申貸約當期間是否有另案增貸或新貸案件額度 **/
	public String getIsNewOrAddLoan() {
		return this.isNewOrAddLoan;
	}
	/** 設定申貸約當期間是否有另案增貸或新貸案件額度 **/
	public void setIsNewOrAddLoan(String value) {
		this.isNewOrAddLoan = value;
	}

	/** 取得增貸或新貸案件額度 **/
	public BigDecimal getNewOrAddLoanQuota() {
		return this.newOrAddLoanQuota;
	}
	/** 設定增貸或新貸案件額度 **/
	public void setNewOrAddLoanQuota(BigDecimal value) {
		this.newOrAddLoanQuota = value;
	}

	/** 取得是否有其他既有貸款 **/
	public String getIsExistingLoan() {
		return this.isExistingLoan;
	}
	/** 設定是否有其他既有貸款 **/
	public void setIsExistingLoan(String value) {
		this.isExistingLoan = value;
	}

	/** 取得既有貸款額度 **/
	public BigDecimal getExistingLoanQuota() {
		return this.existingLoanQuota;
	}
	/** 設定既有貸款額度 **/
	public void setExistingLoanQuota(BigDecimal value) {
		this.existingLoanQuota = value;
	}

	/** 取得是否有其他金融機構貸款 **/
	public String getIsOtherAgencyLoan() {
		return this.isOtherAgencyLoan;
	}
	/** 設定是否有其他金融機構貸款 **/
	public void setIsOtherAgencyLoan(String value) {
		this.isOtherAgencyLoan = value;
	}

	/** 取得其他金融機構貸款額度 **/
	public BigDecimal getOtherAgencyLoanQuota() {
		return this.otherAgencyLoanQuota;
	}
	/** 設定其他金融機構貸款額度 **/
	public void setOtherAgencyLoanQuota(BigDecimal value) {
		this.otherAgencyLoanQuota = value;
	}

	/** 取得是否提供十成存款設質之貸款 **/
	public String getIsDepositPledgedLoans() {
		return this.isDepositPledgedLoans;
	}
	/** 設定是否提供十成存款設質之貸款 **/
	public void setIsDepositPledgedLoans(String value) {
		this.isDepositPledgedLoans = value;
	}

	/** 取得是否小於原貸額度 **/
	public String getIsLeOriginalLoanQuota() {
		return this.isLeOriginalLoanQuota;
	}
	/** 設定是否小於原貸額度 **/
	public void setIsLeOriginalLoanQuota(String value) {
		this.isLeOriginalLoanQuota = value;
	}

	/** 取得是否非行家理財-短期擔保案件或循環額度承做 **/
	public String getIsNotSpecificProjectCommit() {
		return this.isNotSpecificProjectCommit;
	}
	/** 設定是否非行家理財-短期擔保案件或循環額度承做 **/
	public void setIsNotSpecificProjectCommit(String value) {
		this.isNotSpecificProjectCommit = value;
	}

	/** 取得是否有向借戶說明貸款須知 **/
	public String getIsExplainToLender() {
		return this.isExplainToLender;
	}
	/** 設定是否有向借戶說明貸款須知 **/
	public void setIsExplainToLender(String value) {
		this.isExplainToLender = value;
	}

	/** 取得其他事項說明 **/
	public String getDescription() {
		return this.description;
	}
	/** 設定其他事項說明 **/
	public void setDescription(String value) {
		this.description = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}
	
	public Timestamp getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}
	
	public Timestamp getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Timestamp updateTime) {
		this.updateTime = updateTime;
	}
	public String getIsShareAcquisition() {
		return isShareAcquisition;
	}
	public void setIsShareAcquisition(String isShareAcquisition) {
		this.isShareAcquisition = isShareAcquisition;
	}
}
