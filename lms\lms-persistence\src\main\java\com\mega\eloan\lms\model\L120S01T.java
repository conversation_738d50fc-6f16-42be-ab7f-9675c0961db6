/* 
 * L120S01T.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 借款人私募基金檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S01T", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S01T extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 
	 * 資料註記<p/>
	 * 本次(Y)，原始查詢(N)
	 */
	@Size(max=1)
	@Column(name="FLAG", length=1, columnDefinition="CHAR(1)")
	private String flag;

	/** 
	 * 私募基金代碼<p/>
	 * 舊資料：<br/>
	 *  L120S01B.privateEquityNo<br/>
	 *  L120S01B.bfPrivateEquityNo
	 */
	@Size(max=4)
	@Column(name="PRIVATEFUNDNO", length=4, columnDefinition="VARCHAR(4)")
	private String privateFundNo;

	/** 
	 * 私募基金名稱<p/>
	 * 舊資料：<br/>
	 *  L120S01B.privateEquityName<br/>
	 *  L120S01B.bfPrivateEquityName
	 */
	@Size(max=120)
	@Column(name="PRIVATEFUNDNAME", length=120, columnDefinition="VARCHAR(120)")
	private String privateFundName;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得資料註記<p/>
	 * 本次(Y)，原始查詢(N)
	 */
	public String getFlag() {
		return this.flag;
	}
	/**
	 *  設定資料註記<p/>
	 *  本次(Y)，原始查詢(N)
	 **/
	public void setFlag(String value) {
		this.flag = value;
	}

	/** 
	 * 取得私募基金代碼<p/>
	 * 舊資料：<br/>
	 *  L120S01B.privateEquityNo<br/>
	 *  L120S01B.bfPrivateEquityNo
	 */
	public String getPrivateFundNo() {
		return this.privateFundNo;
	}
	/**
	 *  設定私募基金代碼<p/>
	 *  舊資料：<br/>
	 *  L120S01B.privateEquityNo<br/>
	 *  L120S01B.bfPrivateEquityNo
	 **/
	public void setPrivateFundNo(String value) {
		this.privateFundNo = value;
	}

	/** 
	 * 取得私募基金名稱<p/>
	 * 舊資料：<br/>
	 *  L120S01B.privateEquityName<br/>
	 *  L120S01B.bfPrivateEquityName
	 */
	public String getPrivateFundName() {
		return this.privateFundName;
	}
	/**
	 *  設定私募基金名稱<p/>
	 *  舊資料：<br/>
	 *  L120S01B.privateEquityName<br/>
	 *  L120S01B.bfPrivateEquityName
	 **/
	public void setPrivateFundName(String value) {
		this.privateFundName = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
