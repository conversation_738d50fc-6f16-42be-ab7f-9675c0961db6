package com.mega.eloan.lms.base.report.impl;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.pages.CLS2501M01Page;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.impl.C250M01ADaoImpl;
import com.mega.eloan.lms.dao.impl.C250M01EDaoImpl;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C250M01A;
import com.mega.eloan.lms.model.C250M01E;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 可疑代辦案件註記PDF
 * */
@Service("cls2501r01rptservice")
public class CLS2501R01RptServiceImpl extends AbstractReportService {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS2501R01RptServiceImpl.class);
	
	@Resource
	EloandbBASEService eloanDbService;
	@Resource
	UserInfoService userInfoService;
	@Resource
	C250M01ADaoImpl c250m01adao;
	@Resource
	C250M01EDaoImpl c250m01edao;
	@Resource
	BranchService branch;
	@Resource
	CodeTypeService codeTypeService;
	@Resource
	LMSService lmsService;
	
	@Override
	public String getReportTemplateFileName() {
		LOGGER.info("into getReportTemplateFileName");
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		// 測試用
		// return
		// "D:/work/src.mega/WebELoan45/lms/lms-config/src/main/resources/report/cls/CLS1021R01_zh_TW.rpt";
		return "report/cls/CLS2501R01_" + locale.toString() + ".rpt";
	}

	/*
	 * (non-Javadoc) 設定需要傳入RPT參數
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.AbstractReportService#setReportData(com
	 * .mega.eloan.lms.base.report.ReportGenerator,
	 * org.apache.wicket.PageParameters)
	 */
	@Override
	public void setReportData(ReportGenerator reportTools, PageParameters params) {
		// 測試用
		// reportTools.setTestMethod(true);
		LOGGER.info("into setReportData");
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS2501M01Page.class);
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		// List<Map<String, String>> titleRows = new LinkedList<Map<String,
		// String>>();
		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		// C250M01A．可疑代辦案件註記主檔
		C250M01A c250m01a = null;
		// C250M01E．可疑代辦案件註記簽章欄檔
		List<C250M01E> c250m01elist = null;
		
		Map<String, CapAjaxFormResult> C250M01ACodeMap = codeTypeService.findByCodeType(new String[] { "C250M01A_lnFlag"});
		if (C250M01ACodeMap == null) {
			C250M01ACodeMap = new HashMap<String, CapAjaxFormResult>();
		}

		String branchName = null;
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = null;
		try {
			locale = LocaleContextHolder.getLocale();
			if (locale == null)
				locale = Locale.getDefault();
			c250m01a = c250m01adao.findByOid(mainOid);
			if (c250m01a == null) {
				c250m01a = new C250M01A();
			}
			
			String apprid = "";
			String recheckid = "";
			String bossid = "";
			String managerid = "";
			c250m01elist = c250m01edao.findByMainId(mainId);

				for (C250M01E c250m01e : c250m01elist) {
					// 要加上人員代碼
					String type = Util.trim(c250m01e.getStaffJob());
					String userId = Util.trim(c250m01e.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {
						apprid = userId + " " + value;
					} else if ("L3".equals(type)) {
						bossid = bossid + userId + " " + value + "<br/>";
					} else if ("L4".equals(type)) {
						recheckid = userId + " " + value;
					} else if ("L5".equals(type)) {
						managerid = userId + " " + value;
					}
				}
				
			branchName = Util.nullToSpace(branch.getBranchName(Util.nullToSpace(c250m01a.getOwnBrId())));
			String custId = Util.nullToSpace(c250m01a.getCustId()) + " " + Util.nullToSpace(c250m01a.getDupNo());
			String custname = Util.nullToSpace(c250m01a.getCustName());
			String cntrno = Util.nullToSpace(c250m01a.getCntrNo());
			String tDate = "";
			
			if (!"".equals(Util.trim(c250m01a.getApproveTime()))){
				tDate = Util.trim(c250m01a.getUpdateTime()) + "(" + Util.trim(c250m01a.getApproveTime()) + ")";
			} else {
				tDate = Util.trim(c250m01a.getUpdateTime());
			}
			
			String C250M01A_MEMO = "";
			C250M01A_MEMO = prop.getProperty("C250M01A.MEMO01") 
			+ "</br>"
			+ prop.getProperty("C250M01A.MEMO02")
			+ "</br>"
			+ prop.getProperty("C250M01A.MEMO03");
			
			rptVariableMap.put("C250M01A.MEMO",	C250M01A_MEMO);
			
			rptVariableMap.put("C250M01A.BRANCHCOMM",	Util.trim(c250m01a.getBranchComm()));
			
			rptVariableMap.put("C250M01A.DESC",	CLS2501R01RptServiceImpl.C250M01AStr(c250m01a, C250M01ACodeMap));

			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap.put("C250M01A.Mainid", Util.trim(c250m01a.getMainId()));
			rptVariableMap.put("C250M01A.CUSTID", custId);			
			rptVariableMap.put("C250M01A.CUSTNAME", custname);			
			rptVariableMap.put("C250M01A.CNTRNO", cntrno);
			
			rptVariableMap.put("C250M01A.YYYYMM", Util.trim(c250m01a.getYyyymm()));
			
			String STATUS = Util.trim(c250m01a.getStatus());
			if ("A".equals(STATUS)) {
				STATUS = prop.getProperty("C250M01A.statusA"); // C250M01A.statusA=增額
			} else if ("N".equals(STATUS)){
				STATUS = prop.getProperty("C250M01A.statusN"); // C250M01A.statusN=新做
			} else {
				STATUS = "";
			}
			
			rptVariableMap.put("C250M01A.STATUS", STATUS);
			rptVariableMap.put("C250M01A.LOANNO", Util.trim(c250m01a.getLoanNo()));
			
			rptVariableMap.put("C250M01E.APPRID", apprid);
			rptVariableMap.put("C250M01E.RECHECKID", recheckid);
			rptVariableMap.put("C250M01E.BOSSID", bossid);
			rptVariableMap.put("C250M01E.MANAGERID", managerid);
			rptVariableMap.put("C250M01E.TITLEMARK", "");
			rptVariableMap.put("C250M01A.DATE", tDate);
			
			// this.generator.setLang(java.util.Locale.TAIWAN);
			reportTools.setLang(locale);
			reportTools.setVariableData(rptVariableMap);
			// reportTools.setRowsData(titleRows);
			// new ReportGenerator().checkVariableExist("C:/test.txt",
			// rptVariableMap);
		} finally {

		}
	}
	
	private static String C250M01AStr(C250M01A c250m01a, Map<String, CapAjaxFormResult> codeMap) {
		
//		a.      撥款後回查有其他金融機構短期內接續撥款情形 (指3個月內)
//		b.      貸款後不久即延滯情形 (指3個月內)
//		c.      跨區承作之情形 (對於借款人居住、工作地與案件來源無地源關係之申貸案件)
//		d.      其他可疑情形：                            (請自行填寫)
//		e.    無以上情形   (此項設為預設值)

		
		StringBuffer temp = new StringBuffer();
		String br = "<br/>";
//		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS2501M01Page.class);
		String lnFlag = Util.trim(c250m01a.getLnflag());
		String lnFlagStr = Util.trim(codeMap.get("C250M01A_lnFlag").get(lnFlag));
		String otherMemo = Util.trim(c250m01a.getOthermemo());

		temp.append(lnFlagStr);
		
		if ("D".equals(lnFlag)) {
			temp.append("-");
			temp.append(otherMemo);
		} else {
			temp.append("。");
		}
		return temp.toString();
	}

}
