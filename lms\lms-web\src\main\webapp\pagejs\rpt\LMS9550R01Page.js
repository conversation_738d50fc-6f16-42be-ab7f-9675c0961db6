//做為畫面init完成後使用
//var initDfd = $.Deferred();
var _fHandler = "lms9550r01formhandler";
var _gHandler = "lms9550v01gridhandler";
$(document).ready(function(){
	var tabForm = $("#tabForm");
	$.form.init({
	    formHandler: _fHandler,
	    formAction: "query",
	    formId:"tabForm",
        loadSuccess: function(json){
        	setIgnoreTempSave(true);
        	tabForm.find("#fileList").setOptions(json.fileList, false);
        	
        	tabForm.find("a:.linkFile").each(function(i,v){
        		$(this).click(function(){downloadFile(this.id);});
        	});
        	$("#buttonPanel").find("#btnFTP").click(function(){
        		API.confirmMessage(i18n.lms9550r01['msg.sendFTP1'],function(result){
    				if(result){
    					$.ajax({
    						handler: _fHand<PERSON>,
       			            action: "sendToFTP",
       			            data: {mainOid:$("#mainOid").val()},
	       			        success:function(json){
	       			        	tabForm.injectData(json);
	       			        	API.triggerOpener();
	       			        }
    		            });
    				}
            	});
        	});
        }
	});
	
	function downloadFile(fileId){
		var fileOid = tabForm.find("#fileList").find("[value="+fileId+"]").attr("showvalue");
	    $.capFileDownload({
	        handler:"simplefiledwnhandler",
	        data : {
	            fileOid:fileOid
	        }
	    });
	}
});

