package com.mega.eloan.lms.las.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L192M01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

/**
 * 稽核室 稽核工作底稿 介面
 * 
 * <AUTHOR>
 * 
 */
public interface LMS1935Service extends ICapService {

	/**
	 * 取得工作底稿grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<L192M01A>
	 */
	public Page<L192M01A> get1935V01(ISearch search);

	/**
	 * J-111-0011 新增於eloan授信管理系統(906稽核處)稽核工作底稿，增列「已核准額度明細表及案件簽報書」功能鍵
	 * 撈出額度明細的資料用
	 * 
	 * @param oid
	 * @return
	 */
	public List<Map<String, Object>> findPrintL140M01AByOidForLMS1935(String oid);
	
	public List<L120S01B> findL120s01bByMainId(String mainId);
}
