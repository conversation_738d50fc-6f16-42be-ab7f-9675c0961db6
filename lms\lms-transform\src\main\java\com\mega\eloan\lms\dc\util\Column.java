package com.mega.eloan.lms.dc.util;

/**
 * <pre>
 * 欄位屬性定義
 * </pre>
 * 
 * <AUTHOR>
 */
public class Column {
	private String name = null;
	private Type type = null;
	private int length = 0;
	private int scale = 0;
	private String dscr = null;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Type getType() {
		return type;
	}

	public void setType(Type type) {
		this.type = type;
	}

	public int getLength() {
		return length;
	}

	public void setLength(int length) {
		this.length = length;
	}

	public int getScale() {
		return scale;
	}

	public void setScale(int scale) {
		this.scale = scale;
	}

	public String getDscr() {
		return dscr;
	}

	public void setDscr(String dscr) {
		this.dscr = dscr;
	}

	public String toString() {
		StringBuffer sb = new StringBuffer();
		sb.append("NAME[").append(this.getName()).append("]");
		sb.append("DSCR[").append(this.getDscr()).append("]");
		sb.append("TYPE[").append(this.getType().name()).append("]");
		sb.append("LENGTH[").append(this.getLength()).append("]");
		sb.append("SCALE[").append(this.getScale()).append("]");
		return sb.toString();
	}
}