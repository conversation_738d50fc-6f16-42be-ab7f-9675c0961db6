package com.mega.eloan.lms.batch.service.impl;

import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonGenerationException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.gwclient.EJCICGwClient;
import com.mega.eloan.common.gwclient.EJCICGwReqMessage;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLS2501Service;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.dao.C250M01ADao;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.mfaloan.service.MisLNF030Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101S01H;
import com.mega.eloan.lms.model.C101S01U;
import com.mega.eloan.lms.model.C250M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 *  J-111-0175 修改疑似代辦案件-依聯徵資訊及滯延付款自動化判別結果
 * </pre>
 * 
 * @since 2022/06/09
 * <AUTHOR>
 * @version <ul>
 *          <li>2022/06/010173,new
 *          </ul>
 */
@Service("clsBatchSuspectedAgentAppCaseServiceImpl")
public class ClsBatchSuspectedAgentAppCaseServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory.getLogger(ClsBatchSuspectedAgentAppCaseServiceImpl.class);
	
	private static final String Virtual_Employee_id = "00ZCB1";
	private static final String Virtual_Employee_Name = "消金處貸後";
	protected static final int timeout = 60;

	@Resource
	BranchService branchService;
	
	@Resource
	EJCICGwClient ejcicClient;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	EjcicService ejcicService;
	
	@Resource
	CLS2501Service cls2501Service;
	
	@Resource
	C250M01ADao c250m01aDao;
	
	@Resource
	MisLNF030Service mislnf030service;
	
	@Resource
	CLSService clsService;
 
	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		
		JSONObject result = null;
		String cntrNo = null;
		String custId = null;
		String dupNo = null;
		String status = null;
		String yyyymm = null;
		String lnflag = null;
		Date checkTime = null;
		try {
			result = new JSONObject();
			
			JSONObject request = json.getJSONObject("request");
			String specificDataDate = request.get("SPECIFIC_DATADATE") == null ? "" : String.valueOf(request.get("SPECIFIC_DATADATE"));
			
			Calendar calNowDate = Calendar.getInstance();
			calNowDate.setTime(new Date());
			
			String nowDate_yyyyMM = String.valueOf(calNowDate.get(Calendar.YEAR)) + String.valueOf(calNowDate.get(Calendar.MONTH)+1);
			
			List<Map<String, Object>> elf516List = StringUtils.isBlank(specificDataDate) 
													? this.misdbBASEService.getElf516UneditedDataAndCheckTimesLessThan3times(nowDate_yyyyMM)
													: this.misdbBASEService.getELF516UneditedDataAndCheckTimesLessThan3timesByYYYYMM(specificDataDate, nowDate_yyyyMM);
													
			for(Map<String, Object> elf516Map : elf516List){
				
				cntrNo = String.valueOf(elf516Map.get("ELF516_CNTRNO"));
				custId = String.valueOf(elf516Map.get("ELF516_CUSTID"));
				dupNo = String.valueOf(elf516Map.get("ELF516_DUPNO"));
				Integer checkTimes = Integer.valueOf(String.valueOf(elf516Map.get("ELF516_CHECKTIMES") == null ? 0 : elf516Map.get("ELF516_CHECKTIMES")));
				status = String.valueOf(elf516Map.get("ELF516_STATUS"));
				yyyymm = String.valueOf(elf516Map.get("ELF516_YYYYMM"));
				checkTime = elf516Map.get("ELF516_CHECKTIME") != null ? (Timestamp)elf516Map.get("ELF516_CHECKTIME") : null;
				
				String branchNo = cntrNo.substring(0, 3);
				//確認是否已再 C250M01A 已有資料, 如有 ---> 不檢核

				//當月已檢查過 ---> 不需檢查
				boolean isCheckedThisMonth = false;
				if(checkTime != null){
					Calendar calCheckTime = Calendar.getInstance();
					calCheckTime.setTime(checkTime);
					isCheckedThisMonth = (String.valueOf(calCheckTime.get(Calendar.YEAR)) + calCheckTime.get(Calendar.MONTH)).equals(String.valueOf(calNowDate.get(Calendar.YEAR)) + calNowDate.get(Calendar.MONTH))
									   ? true
									   : false;
				}
				
				if(isCheckedThisMonth){
					LOGGER.debug("當月已檢查過-不需檢查 cntrNo: {}, custId: {}, dupNo: {}, yyyymm: {}, status: {}, isCheckedThisMonth: {}", new String[]{cntrNo, custId, dupNo, yyyymm, status, isCheckedThisMonth ? "Y" : "N"});
					continue;
				}
				
				String c250m01aMainid = IDGenerator.getUUID();
				
				Map<String, Object> b29LogMap = null;
				
				if(this.isRequeryEjcicB29Data(cntrNo, custId, dupNo, status, yyyymm, calNowDate)){
					b29LogMap = this.queryEjcicB29Data(branchNo, custId, dupNo, c250m01aMainid);
				}
				else{
					//get ejcic B29 log data
					b29LogMap = this.ejcicService.getLatestLogFileByTxId(custId, "HB29");
					b29LogMap.put("IS_EJCIC_SUCCESS", true);
				}
				
				String prodId = String.valueOf(b29LogMap.get("PRODID"));
				String qDate = String.valueOf(b29LogMap.get("QDATE"));
				String txId = String.valueOf(b29LogMap.get("TXID")); 
				String qBranch = String.valueOf(b29LogMap.get("QBRANCH")); 
				String qEmpCode = String.valueOf(b29LogMap.get("REQID"));
				boolean isEjcicSuccess = (Boolean)b29LogMap.get("IS_EJCIC_SUCCESS");
				
				String b29SrcOid = null;
				String b29SrcFileName = null;
				if("P9".equals(prodId)){
					b29SrcOid = this.clsService.findC101S01HByEjcicInquiryRecord(custId, dupNo, txId, prodId, qBranch, qDate, qEmpCode).getOid();
					b29SrcFileName = "C101S01H";
				}
				
				if("ST".equals(prodId)){
					b29SrcOid = this.clsService.findLatestC101S01UByCustIdDupNoTxid(custId, dupNo, "B29").getOid();
					b29SrcFileName = "C101S01U";
				}
				
				//聯徵查尋成功並且未銷戶案件
				List<Map<String, Object>> bam029List = null;
				if(isEjcicSuccess){
					bam029List = this.ejcicService.getBam029NewApprovedQuotaByOtherBank(custId, prodId, qDate);
				}
				
				//start check
				List<C250M01A> c250m01aList = this.c250m01aDao.findByCntrNo_custId_dupNo_yyyymm_status(cntrNo, custId, dupNo, yyyymm, status);
				Map<String, String> checkMap = this.checkLnFlagAndGetOverdueJson(custId, dupNo, cntrNo, bam029List, checkTimes, c250m01aList, elf516Map.get("LNF020_CANCEL_DATE"), isEjcicSuccess);
				lnflag = checkMap.get("LN_FLAG");
				String overdueJson = checkMap.get("OVERDUE_JSON");
				String otherDesc = checkMap.get("OTHER_DESC");
				
				if(!"".equals(lnflag) && !"Z".equals(lnflag)){
					this.createC250M01A(c250m01aMainid, cntrNo, branchNo, custId, dupNo, lnflag, status, yyyymm, elf516Map, overdueJson, b29SrcOid, b29SrcFileName, otherDesc);
				}
				
				//更新elf516 checktime 和時間
				
				if("Z".equals(lnflag)){
					this.misdbBASEService.updateElf516UnfundedAndCanceledCase(lnflag, checkTimes, cntrNo, custId, dupNo, yyyymm, status);
				}
				else{
					this.misdbBASEService.updateElf516CheckTimes(checkTimes+1, cntrNo, custId, dupNo, yyyymm, status);
				}
				
				LOGGER.debug("疑似代辦案件檢核執行成功 ===> cntrNo: {}, custId: {}, dupNo: {}, yyyymm: {}, status: {}, lnflag: {}", new String[]{cntrNo, custId, dupNo, yyyymm, status, lnflag});
			}
			
			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE, "clsBatchSuspectedAgentAppCaseServiceImpl 執行成功！");

		} catch (Exception ex) {
			LOGGER.error(StrUtils.getStackTrace(ex));
			result = WebBatchCode.RC_ERROR;
			String msg = ex.getMessage() + "===> cntrNo:" + cntrNo + ", custId:" + custId + ", dupNo:" + dupNo + ", yyyymm:" + yyyymm + ", status:" + status + ", lnflag" + lnflag;
			result.element(
					WebBatchCode.P_RESPONSE, "clsBatchSuspectedAgentAppCaseServiceImpl 執行失敗！==>" + msg + " - " + ex.getLocalizedMessage());
		}

		return result;
	}
	
	private Map<String, String> checkLnFlagAndGetOverdueJson(String custId, String dupNo, String cntrNo, List<Map<String, Object>> bam029List, Integer checkTimes, 
																List<C250M01A> c250m01aList, Object cancelDateObj, boolean isEjcicSuccess) 
																throws JsonGenerationException, JsonMappingException, IOException{
		

		Map<String, String> rtnMap = new HashMap<String, String>();
		
		//抓首撥日
		String tmpfirstLoanDate = this.misdbBASEService.getLatestFirstLoanDateByLnf030Cntrno(cntrNo);
		Date firstLoanDate = tmpfirstLoanDate == null ? null : CapDate.parseDate(tmpfirstLoanDate);
		
		String otherDesc = null;
		String lnflag = "";
		String overdueJson = null;
		boolean isAccountCanceled = cancelDateObj != null ? true : false;
		
		if(isAccountCanceled && firstLoanDate == null){
			lnflag = "Z";//為未撥款已銷戶案件 or 3個月後一直未撥款案件
		}
		
		//代表已銷戶
		if(isAccountCanceled && "".equals(lnflag)){
			Date cancelDate = (Date)cancelDateObj;
			Date dateAfter3Month = CapDate.addMonth(firstLoanDate, 3);
			//首撥後3個月內銷戶
			if(dateAfter3Month.compareTo(cancelDate) > 0){
				lnflag = "E"; //經查並無仲介代辦情況出現
				otherDesc = "貸後三個月內銷戶";
			}
		}
		
		//聯徵查詢成功
		if(isEjcicSuccess && "".equals(lnflag)){
			
			if(firstLoanDate == null && checkTimes == 2 && c250m01aList.size() == 0){
				lnflag = "Z";//為未撥款已銷戶案件 or 3個月後一直未撥款案件
			}
			
			if(firstLoanDate != null){
				
				if(bam029List != null && !bam029List.isEmpty()){
					lnflag += this.isNewApprovedQuotaAfterFirstLoanDateWithin3Month(firstLoanDate, bam029List) ? "A" : "" ;
				}
				
				List<Map<String, Object>> overdueData = this.misdbBASEService.getELFDELYDEarliestOverdueDataByCustId_dupNo_Contract(custId, dupNo, cntrNo);
				List<Map<String, Object>> overdueList = getOverdueDateListWithin3Month(firstLoanDate, overdueData);
				lnflag += !overdueList.isEmpty() ? "B" : "" ;
				
				lnflag = lnflag.equals("AB") ? "F" : lnflag;
				
				if(checkTimes == 2 && "".equals(lnflag) && c250m01aList.size() == 0 ){
					lnflag = "E";
				}

				overdueJson = !overdueList.isEmpty() ? new ObjectMapper().writeValueAsString(overdueList) : null;
			}
		}
		
		//聯徵查詢失敗 且 非3個月內銷戶案件
		if(!isEjcicSuccess && "".equals(lnflag)){
			lnflag = "X";
			otherDesc = "無法自動化判斷之原因";
		}
		
		rtnMap.put("LN_FLAG", lnflag);
		rtnMap.put("OVERDUE_JSON", overdueJson);
		rtnMap.put("OTHER_DESC", otherDesc);
		
		return rtnMap;
	}
	
	private void createC250M01A(String c250m01aMainid, String cntrNo, String branchNo, String custId, String dupNo, 
								String lnflag, String status, String yyyymm, Map<String, Object> elf516Map, String overdueData,
								String b29SrcOid, String b29SrcFileName, String otherDesc){
		
		//抓取帳號 參考: CLS2501GridHandler.queryGetCntrno()
		String loanNo = "";
		List<Map<String, Object>> lnf030s = mislnf030service.selaLoanNoByCntrno(cntrNo);

		if (!lnf030s.isEmpty()) {
			for (Map<String, Object> lnf030 : lnf030s) {
				if ("".equals(loanNo)) {
					loanNo = Util.trim(lnf030.get("LNF030_LOAN_NO"));
				} else {
					loanNo = loanNo + "、" + Util.trim(lnf030.get("LNF030_LOAN_NO"));
				}						
			}
		}
		
		String custName = String.valueOf(elf516Map.get("ELF516_CUSTNAME"));
		String otherMemo = String.valueOf(elf516Map.get("ELF516_OTHERMEMO"));
		String branchComm = String.valueOf(elf516Map.get("ELF516_BRANCHCOMM"));
		
		C250M01A c250m01a = new C250M01A();
		
		c250m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		c250m01a.setOwnBrId(branchNo);

		c250m01a.setMainId(c250m01aMainid);
		c250m01a.setTxCode("batch1");
		// UPGRADE: 待確認，URL怎麼取得
		// c250m01a.setDocURL(CLS2501M01Page.class.getAnnotation(MountPath.class).path());
		
		c250m01a.setTypCd(UtilConstants.Casedoc.typCd.DBU);
		c250m01a.setCustId(custId);
		c250m01a.setDupNo(dupNo);
		c250m01a.setCustName(custName);
		c250m01a.setCntrNo(cntrNo);
		c250m01a.setStatus(status);
		c250m01a.setLnflag(lnflag);	
		c250m01a.setLoanNo(loanNo);
		c250m01a.setYyyymm(yyyymm);
		c250m01a.setOthermemo(otherMemo);
		c250m01a.setBranchComm(branchComm);
		c250m01a.setRandomCode(IDGenerator.getRandomCode());
		c250m01a.setOverDueJson(overdueData);
		c250m01a.setB29SrcOid(b29SrcOid);
		c250m01a.setB29SrcFileName(b29SrcFileName);
		c250m01a.setOtherDesc(otherDesc);
		this.cls2501Service.saveC250M01AForBatchCheck(c250m01a, "system", branchNo);
	}
	
	private boolean isNewApprovedQuotaAfterFirstLoanDateWithin3Month(Date firstLoanDate, List<Map<String, Object>> bam029List){
		
		Date dateAfter3Month = CapDate.addMonth(firstLoanDate, 3);
		
		for(Map<String, Object> map : bam029List){
			
			String tmpDate = String.valueOf(map.get("PAY_LOAN_DATE"));
			Date approvedDate = CapDate.parseDate(CapDate.formatDateFromF1ToF2(tmpDate, "YYYMMDD", "yyyyMMdd"));
			
			if(approvedDate.compareTo(dateAfter3Month) <= 0){
				return true;
			}
		}
		
		return false;
	}
	
	private List<Map<String, Object>> getOverdueDateListWithin3Month(Date firstLoanDate, List<Map<String, Object>> overdueData){
		
		List<Map<String, Object>> rtnList = new ArrayList<Map<String, Object>>();
		
		if(overdueData.isEmpty()){
			return rtnList;
		}
		
		Date dateAfter3Month = CapDate.addMonth(firstLoanDate, 3);
		
		Map<String, Map<String, Object>> filterRepeatMap = new HashMap<String, Map<String, Object>>();
		
		for(Map<String, Object> overdueMap : overdueData){
			
			if(overdueMap.get("ELFDELYD_OV_HAPEN") != null){
				
				Date overdueDate = CapDate.parseDate(String.valueOf(overdueMap.get("ELFDELYD_OV_HAPEN")));
				if(overdueDate.compareTo(dateAfter3Month) <= 0){
					String key = String.valueOf(overdueMap.get("ELFDELYD_BR_NO")) + overdueMap.get("ELFDELYD_CONTRACT") + overdueMap.get("ELFDELYD_LOAN_NO");
					filterRepeatMap.put(key, overdueMap);
				}
			}
		}
		
		for(String key : filterRepeatMap.keySet()){
			Map<String, Object> map = filterRepeatMap.get(key);
			Map<String, Object> m = new HashMap<String, Object>();
			m.put("BR_NO", map.get("ELFDELYD_BR_NO"));
			m.put("CONTRACT", map.get("ELFDELYD_CONTRACT"));
			m.put("LOAN_NO", map.get("ELFDELYD_LOAN_NO"));
			rtnList.add(m);
		}
		
		return rtnList;
	}
	
	private Map<String, Object> queryEjcicB29Data(String branchNo, String custId, String dupNo, String c250m01aMainid) throws ClientProtocolException, IOException, CapException{
		//query B29 data
		String ejcicB29Url = this.getEjcicB29Url(branchNo, custId);
		this.keep_url_htmloutput_to_c101s01u(c250m01aMainid, custId, dupNo, "B29", ejcicB29Url);
		String httpCode = this.queryEjcicByHttpClient(ejcicB29Url);
		
		Map<String, Object> b29LogMap = this.ejcicService.getLatestLogFileByQKey1_txId_toJcic(custId, "HB29");
		
		boolean isEjcicSuccess = true;
		String retCode = null;
		if(!"200".equals(httpCode) || b29LogMap == null || !"0000".equals(b29LogMap.get("RETCODE"))){
			retCode = b29LogMap != null ? String.valueOf(b29LogMap.get("RETCODE")) : retCode;
			LOGGER.debug("聯徵B29查詢失敗 branchNo:{}, custId:{}, dupNo:{}, c250m01aMainid:{},  httpCode:{}, retCode:{}", new String[]{branchNo, custId, dupNo, c250m01aMainid, httpCode, retCode});
			isEjcicSuccess = false;
		}
		
		if(b29LogMap == null){
			b29LogMap = new HashMap<String, Object>();
		}
		
		b29LogMap.put("IS_EJCIC_SUCCESS", isEjcicSuccess);
		
		return b29LogMap;
	}
	
	private String getEjcicB29Url(String branchNo, String custId) throws ClientProtocolException, IOException{
		
		String cbdeptid = "";
		String deptnm = "";
		IBranch iBranch = branchService.getBranch(branchNo);
		if (iBranch != null) {
			cbdeptid = iBranch.getBrNo() + iBranch.getChkNo();
			deptnm = iBranch.getBrName();
		}
		
		EJCICGwReqMessage ejcicReq_ST = new EJCICGwReqMessage();
		ejcicReq_ST.setSysId("LMS");
		ejcicReq_ST.setMsgId(IDGenerator.getUUID());
		ejcicReq_ST.setQueryid(custId);
		ejcicReq_ST.setEmpid(ClsBatchSuspectedAgentAppCaseServiceImpl.Virtual_Employee_id);
		ejcicReq_ST.setEmpname(ClsBatchSuspectedAgentAppCaseServiceImpl.Virtual_Employee_Name);
		ejcicReq_ST.setDeptid(branchNo);
		ejcicReq_ST.setCbdeptid(cbdeptid);
		ejcicReq_ST.setBranchnm(deptnm);
		ejcicReq_ST.setPur("B4A");  /*  查詢理由:B4A => 
									 *	第一層 B.原業務往來
									 *  第二層 4放款業務(c) 
									 *	第三層 A.取得當事人書面同意
									 */
		ejcicReq_ST.setProdid("");
		ejcicReq_ST.setTxid(CrsUtil.EJ_TXID_B29);
		ejcicReq_ST.setQkey1(custId);//custId
		ejcicReq_ST.setQkey2("");
		
		return Util.trim(ejcicClient.get_callAPI_URL(ejcicReq_ST));
	}
	
	private void keep_url_htmloutput_to_c101s01u(String mainId, String custId, String dupNo, String txId, String url) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		InputStream in = null;
		String resp_html = "";
		try {
			in = new URL(url).openStream();
			StringWriter writer = new StringWriter();
			IOUtils.copy(in, writer, "Big5_HKSCS");
			resp_html = writer.toString();

			List<C101S01U> c101s01u_list = clsService.findC101S01U_txid(mainId, custId, dupNo, txId);
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			C101S01U c101s01u = null;
			if (c101s01u_list.size() > 0) {
				c101s01u = c101s01u_list.get(0);
			} else {
				c101s01u = new C101S01U();
				c101s01u.setMainId(mainId);
				c101s01u.setCustId(custId);
				c101s01u.setDupNo(dupNo);
				c101s01u.setTxid(txId);
				c101s01u.setCreator(user.getUserId());
				c101s01u.setCreateTime(nowTS);
			}
			c101s01u.setHtmlData(resp_html);
			c101s01u.setSendTime(nowTS);
			c101s01u.setUpdater(user.getUserId());
			c101s01u.setUpdateTime(nowTS);
			clsService.daoSave(c101s01u);
		} catch (IOException ioe) {
			LOGGER.error("聯徵B29 Keep Html失敗: mainId={}, custId={}, dupNo={}, url={}, resp_html={}", new String[]{mainId, custId, dupNo, url, resp_html});
			LOGGER.error(StrUtils.getStackTrace(ioe));
		} catch (Exception e) {
			LOGGER.error("聯徵B29 Keep Html失敗: mainId={}, custId={}, dupNo={}, url={}, resp_html={}", new String[]{mainId, custId, dupNo, url, resp_html});
			LOGGER.error(StrUtils.getStackTrace(e));
		} finally {
			IOUtils.closeQuietly(in);
		}
	}
	
	private boolean isRequeryEjcicB29Data(String cntrNo, String custId, String dupNo, String status, String yyyymm, Calendar calNowDate){

		//get ejcic B29 log data
		Map<String, Object> b29LogMap = this.ejcicService.getLatestLogFileByTxId(custId, "HB29");
		
		//無 B29 log data 需重查
		if(b29LogMap == null){
			return true;
		}
		
		String prodId = String.valueOf(b29LogMap.get("PRODID"));
		if(!"P9".equals(prodId) && !"ST".equals(prodId)){
			return true;
		}
		
		//查詢日期不為當月  需重查
		String qDate = String.valueOf(b29LogMap.get("QDATE"));
		String qDate_yyyyMMdd = CapDate.formatDateFormatToyyyyMMdd(qDate, "YYY/MM/DD");
		Calendar calQdate = Calendar.getInstance();
		calQdate.setTime(CapDate.parseDate(qDate_yyyyMMdd));
		if(calQdate.get(Calendar.MONTH) != calNowDate.get(Calendar.MONTH)){
			return true;
		}
		
		String txId = String.valueOf(b29LogMap.get("TXID")); 
		String qBranch = String.valueOf(b29LogMap.get("QBRANCH")); 
		String qEmpCode = String.valueOf(b29LogMap.get("REQID")); 
		
		//查詢天數 - 組合查詢超過30天 or 標準查詢超過15天  需重查
		int daysApart = CapDate.calculateDays(CapDate.getCurrentDate("yyyyMMdd"), qDate_yyyyMMdd);
		
		if("P9".equals(prodId)){
			
			if(daysApart >= 30){
				return true;
			}
			
			//無 html B29資料 需重查
			C101S01H c101s01h = this.clsService.findC101S01HByEjcicInquiryRecord(custId, dupNo, txId, prodId, qBranch, qDate, qEmpCode);
			if(c101s01h == null){
				return true;
			}
		}
		
		if("ST".equals(prodId)){
			
			if(daysApart >= 15){
				return true;
			}
			
			//無 html B29資料 需重查
			C101S01U c101s01u = this.clsService.findLatestC101S01UByCustIdDupNoTxid(custId, dupNo, "B29");
			if(c101s01u == null){
				return true;
			}
		}
			
		return false;	
	}
	
	private String queryEjcicByHttpClient(String url){
		
		final String TITLE = StrUtils.concat("queryEjcicByUrl [send][", System.nanoTime(), "] ");
		
		HttpResponse response;
		String rcCode = null;
		String httpMessage = null;
		
		try {
			
			LOGGER.info(TITLE + "[start] url={}", url);
			
			HttpClient httpclient = this.getDefaultHttpClient(url.toLowerCase().startsWith("https"));
			HttpGet httpGet = new HttpGet(url);
			response = httpclient.execute(httpGet);
			rcCode = String.valueOf(response.getStatusLine().getStatusCode());
			httpMessage = response.getStatusLine().getReasonPhrase();
			
		} catch (IOException ioe) {
			
			LOGGER.error(StrUtils.getStackTrace(ioe));
			
		} catch (Exception e) {
			
			LOGGER.error(StrUtils.getStackTrace(e));
			
		} finally{
			
			LOGGER.info(TITLE + "[end] httpCode=" + rcCode + ", httpMessage=" + httpMessage);
		}
		
		return rcCode;
	}
	
	private DefaultHttpClient getDefaultHttpClient(boolean urlStartsWithHttps) {
		boolean useHttps = urlStartsWithHttps;
		
		final HttpParams params = new BasicHttpParams();
		HttpConnectionParams.setStaleCheckingEnabled(params, false);
		HttpConnectionParams.setConnectionTimeout(params, timeout * 1000);
		HttpConnectionParams.setSoTimeout(params, timeout * 1000);
		HttpConnectionParams.setSocketBufferSize(params, 8192 * 5);
		DefaultHttpClient httpClient = new DefaultHttpClient(params);
		
		if(useHttps){
			X509TrustManager tm = new X509TrustManager() {
				public void checkClientTrusted(
						java.security.cert.X509Certificate[] x509Certificates,
						String s)
						throws java.security.cert.CertificateException {
				}

				public void checkServerTrusted(
						java.security.cert.X509Certificate[] x509Certificates,
						String s)
						throws java.security.cert.CertificateException {
				}

				public java.security.cert.X509Certificate[] getAcceptedIssuers() {
					return new java.security.cert.X509Certificate[0];
				}
            };
            
            try { 
				//java 6 可能沒有到 1.2, 會出現 NoSuchAlgorithmException
				//若把 JRE 切換至 JAVA 8 可以執行
				SSLContext ctx = SSLContext.getInstance("TLSv1.2"); // ("TLSv1.2"); 
				
				//傳入的參數 TLS 會丟出 peer not authenticated
				//SSLContext ctx = SSLContext.getInstance("TLS"); 
	            ctx.init(null, new TrustManager[]{tm}, null);	            
	            SSLSocketFactory sf = new SSLSocketFactory(ctx, SSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
				Scheme sch = new Scheme("https", 443, sf);
				httpClient.getConnectionManager().getSchemeRegistry().register(sch);
			} catch (NoSuchAlgorithmException e) {
				LOGGER.error("[getDefaultHttpClient]NoSuchAlgorithmException!!");
			} catch (KeyManagementException e) {
				LOGGER.error("[getDefaultHttpClient]KeyManagementException!!");
			}
		}
		
		return httpClient;
	}
	
}
