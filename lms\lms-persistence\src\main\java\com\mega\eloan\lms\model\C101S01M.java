/* 
 * C101S01M.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金相關查詢聯徵非Z類檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101S01M", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "qDate" }))
public class C101S01M extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 查詢日期
	 * <p/>
	 * (MIS.STM022.QUERY_DATE)
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "QDATE", columnDefinition = "DATE")
	private Date qDate;

	/**
	 * 查詢分行代碼
	 * <p/>
	 * (MIS.STM022.BANK_COD)<br/>
	 * 查詢單位代號CHAR(7)
	 */
	@Size(max = 7)
	@Column(name = "BANKNO", length = 7, columnDefinition = "VARCHAR(7)")
	private String bankNo;

	/**
	 * 查詢分行名稱
	 * <p/>
	 * (MIS.STM022.BANK_NAME)<br/>
	 * 查詢單位名稱CHAR(40)
	 */
	@Size(max = 60)
	@Column(name = "BANKNAME", length = 60, columnDefinition = "VARCHAR(60)")
	private String bankName;

	/**
	 * 查詢項目摘要串列
	 * <p/>
	 * (MIS.STM022.ITEM_LIST)<br/>
	 * 查詢項目串列 B:授信 D:票信 K:信用卡等<br/>
	 * CHAR(10)
	 */
	@Size(max = 10)
	@Column(name = "MEMO", length = 10, columnDefinition = "VARCHAR(10)")
	private String memo;

	/**
	 * 查詢理由代碼
	 * <p/>
	 * (MIS.STM022.INQ_PURPOSE_1)
	 */
	@Size(max = 1)
	@Column(name = "INQCD", length = 1, columnDefinition = "VARCHAR(1)")
	private String inqCd;

	/**
	 * 查詢理由
	 * <p/>
	 * (MIS.STM022.INQ_PURPOSE)<br/>
	 * 查詢理由碼(中文註解) 新、原業務、新原存款開戶及公開資訊
	 */
	@Size(max = 60)
	@Column(name = "INQ", length = 60, columnDefinition = "VARCHAR(60)")
	private String inq;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得查詢日期
	 * <p/>
	 * (MIS.STM022.QUERY_DATE)
	 */
	public Date getQDate() {
		return this.qDate;
	}

	/**
	 * 設定查詢日期
	 * <p/>
	 * (MIS.STM022.QUERY_DATE)
	 **/
	public void setQDate(Date value) {
		this.qDate = value;
	}

	/**
	 * 取得查詢分行代碼
	 * <p/>
	 * (MIS.STM022.BANK_COD)<br/>
	 * 查詢單位代號CHAR(7)
	 */
	public String getBankNo() {
		return this.bankNo;
	}

	/**
	 * 設定查詢分行代碼
	 * <p/>
	 * (MIS.STM022.BANK_COD)<br/>
	 * 查詢單位代號CHAR(7)
	 **/
	public void setBankNo(String value) {
		this.bankNo = value;
	}

	/**
	 * 取得查詢分行名稱
	 * <p/>
	 * (MIS.STM022.BANK_NAME)<br/>
	 * 查詢單位名稱CHAR(40)
	 */
	public String getBankName() {
		return this.bankName;
	}

	/**
	 * 設定查詢分行名稱
	 * <p/>
	 * (MIS.STM022.BANK_NAME)<br/>
	 * 查詢單位名稱CHAR(40)
	 **/
	public void setBankName(String value) {
		this.bankName = value;
	}

	/**
	 * 取得查詢項目摘要串列
	 * <p/>
	 * (MIS.STM022.ITEM_LIST)<br/>
	 * 查詢項目串列 B:授信 D:票信 K:信用卡等<br/>
	 * CHAR(10)
	 */
	public String getMemo() {
		return this.memo;
	}

	/**
	 * 設定查詢項目摘要串列
	 * <p/>
	 * (MIS.STM022.ITEM_LIST)<br/>
	 * 查詢項目串列 B:授信 D:票信 K:信用卡等<br/>
	 * CHAR(10)
	 **/
	public void setMemo(String value) {
		this.memo = value;
	}

	/**
	 * 取得查詢理由代碼
	 * <p/>
	 * (MIS.STM022.INQ_PURPOSE_1)
	 */
	public String getInqCd() {
		return this.inqCd;
	}

	/**
	 * 設定查詢理由代碼
	 * <p/>
	 * (MIS.STM022.INQ_PURPOSE_1)
	 **/
	public void setInqCd(String value) {
		this.inqCd = value;
	}

	/**
	 * 取得查詢理由
	 * <p/>
	 * (MIS.STM022.INQ_PURPOSE)<br/>
	 * 查詢理由碼(中文註解) 新、原業務、新原存款開戶及公開資訊
	 */
	public String getInq() {
		return this.inq;
	}

	/**
	 * 設定查詢理由
	 * <p/>
	 * (MIS.STM022.INQ_PURPOSE)<br/>
	 * 查詢理由碼(中文註解) 新、原業務、新原存款開戶及公開資訊
	 **/
	public void setInq(String value) {
		this.inq = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
