var _handler = "";
$(document).ready(function(){
    //2012_07_20_rex add 取得sso 連線資訊 
    BrowserAction.init();
    setCloseConfirm(true);
    _handler = CLSAction.fhandle;
    
    /**
     * 小放會會議記錄
     */
    $("#L120S08Gbt").click(function(){
        L120S08GAction.openBox()
    });
    
    /**
     * 小放會會議記錄清除
     */
    $("#cleanL120S08GBt").click(function(){
        L120S08GAction.clean()
    });
    
    if($("#btnPrint_docDscrA").length>0){
    	$("#grid_btnPrint_docDscrA").iGrid({
	        handler: 'cls1141gridhandler',        
	        height: 270,
			rownumbers:true,
			multiselect: true,
			hideMultiselect:false,
			caption: "&nbsp;",
			hiddengrid : false,
	        postData: {
	        	mainId: responseJSON.mainId,
	            formAction: "queryPrint_docDscrA"
	        },       
	        colModel: [
	          {colHeader: i18n.cls1141m01['print.custName'],// "借款人名稱",
	                name: 'custName',
	                width: 120,
	                sortable: false
	            }, {
	                colHeader: i18n.cls1141m01['print.rptNo'],// "報表編號",
	                name: 'rptNo',
	                align: "center",
	                width: 40,
	                sortable: false
	            }, {
	                colHeader: i18n.cls1141m01['print.rptName'],// "報表名稱",
	                name: 'rptName',
	                width: 70,
	                sortable: false
	            }, {
	                colHeader: i18n.cls1141m01['print.cntrNo'],// "額度序號",
	                name: 'cntrNo',
	                align: "center",
	                width: 50,
	                sortable: false
	          }        
	        , { name: 'oid', hidden: true }
	        , { name: 'rpt', hidden: true }
	        , { name: 'custId', hidden: true }
	        , { name: 'dupNo', hidden: true }
	        , { name: 'refMainId', hidden: true }
	        , { name: 'keyCustId', hidden: true }
	        , { name: 'keyDupNo', hidden: true }
	        ],        
	        ondblClickRow: function(rowid){
	        	
	        }        
	    });		
    	//===========
    	$("#btnPrint_docDscrA").click(function(){        	
        	
        	$("#thickbox_btnPrint_docDscrA").thickbox({
                title: i18n.def.print,
                width: 700,
                height: 450,                
                modal: true,
                i18n: i18n.def,
                buttons: {
                    "print": function(){
                    	var id = $("#grid_btnPrint_docDscrA").getGridParam('selarrrow');
                    	id.sort(function(a, b){
                            return a - b
                        });
                    	
                    	var pdfName = "";
                    	var count = 0;
                        var content = "";
                        
                        for (var i = 0; i < id.length; i++) {
                            if (id[i] != "") {
                                var datas = $("#grid_btnPrint_docDscrA").getRowData(id[i]);
                                content = content + datas.rpt + "^" + datas.oid 
                                    + "^" + datas.custId + "^" + datas.dupNo + "^" + datas.cntrNo 
                                    + "^" + datas.refMainId
                                	+ "^" + datas.keyCustId + "^" + datas.keyDupNo+ "|";
                                pdfName = datas.rptNo + ".pdf";
                                count++;
                            }
                        }
                        
                        if (content.length != 0) {
                            content = content.substring(0, content.length - 1);
                        }
                   	 	if(count==0){   	 		
                   	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
                   	 		return;
                   	 	}
                   	 	
                   	 	if (count != 1) {
                   	 		pdfName = "CLS1141R01.pdf";
                   	 	}
	                   	$.form.submit({
	                         url: "../../simple/FileProcessingService",
	                         target: "_blank",
	                         data: {
	                             mainId: responseJSON.mainId,
	                             rptOid: content,
	                             fileDownloadName: pdfName,
	                             serviceName: "cls1141r01rptservice"
	                         }
	                    });
                    },
                    "close": function(){
                        $.thickbox.close();
                    }
                }
            });
        	
        });
    }
    
    //J-112-0586_05097_B1001 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
	$("#docDscr9 a").live( "click", function() {
		//消金--小放會
		L120S08GAction.openLink($(this));
	});
  
	$("#docDscrA a").live( "click", function() {
		//消金--團貸母戶額度明細表
		//docDscrA 個金區域營運中心授權內案件簽報書(團貸)：(DBU)12305479 0 興12305479
		getL140M01A(DOMPurify.sanitize(this.title));
	});
    
});




/*function getMain(ces){
    $.ajax({ // 查詢主要借款人資料
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getRelate1",
            mainId: responseJSON.mainId,
            cesMainId: ces
        },
        success: function(json){
            // 徵信報告
            BrowserAction.submit({
                system: "ces",
                url: json.url,
                mainId: json.mainId,
                mainOid: json.mainOid,
                txCode: json.txCode,
                data: { //其它參數
                    fromView: true,
                    uid: json.uid,
                    mainDocStatus: json.mainDocStatus,
                    oid: json.mainOid,
                    mainOid: json.mainOid
                }
            });
        }
    });
}

function getCes(ces){
    $.ajax({ // 查詢主要借款人資料
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getRelate2",
            mainId: responseJSON.mainId,
            cesMainId: ces
        },
        success: function(json){
            // 資信簡表
            BrowserAction.submit({
                system: "ces",
                url: json.url,
                mainId: json.mainId,
                mainOid: json.mainOid,
                txCode: json.txCode,
                data: { //其它參數
                    uid: json.uid,
                    mainDocStatus: json.mainDocStatus,
                    oid: json.mainOid,
                    mainOid: json.mainOid
                }
            });
        }
    });
}*/

function seachKind3(){
    if ($("#lmss08a_panel").attr("open") == "true") {
        $("#lmss08a_panel").load("../../lms/baselmss08a", function(){
            $("#thickboxPeo").thickbox({ // 使用選取的內容進行彈窗
                title: i18n.clss08a["L120S08.thickbox11"],
                width: 960,
                height: 480,
                modal: true,
                i18n: i18n.def,
                buttons: {
                    "print": function(){
                        printA41();
                    },
                    "close": function(){
                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                            if (res) {
                                $.thickbox.close();
                            }
                        });
                    }
                }
            });
            // 控制分頁頁籤內容唯讀(不包括下拉式選單)			
            if (responseJSON.readOnly == "true") {
                $("#tabForm_1").readOnlyChilds(true);
                $("#s41Form").find("button").hide();
                $("#tabForm_1").find("button").hide();
            }
        });
        $("#lmss08a_panel").attr("open", false);
    } else {
        $("#thickboxPeo").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.clss08a["L120S08.thickbox11"],
            width: 960,
            height: 480,
            modal: true,
            i18n: i18n.def,
            buttons: {
                "print": function(){
                    printA41();
                },
                "close": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
    }
}

function seachKind4(){
    if ($("#lmss08b_panel").attr("open") == "true") {
        $("#lmss08b_panel").load("../../lms/baselmss08b", function(){
            $.ajax({
                handler: _handler,
                type: "POST",
                dataType: "json",
                data: {
                    formAction: "queryGrp",
                    page: "91",
                    mainId: responseJSON.mainId
                },
                success: function(json){
                    var $tabForm08 = $("#tabForm08");
                    $tabForm08.reset();
                    $tabForm08.setData(json, false);
                    if ($tabForm08.find("input[name=isGroupCompany1]:radio:checked").val() == "1") {
                        $tabForm08.find("#isGroupCompany1-1").show().siblings("[id^=isGroupCompany1]").hide();
                    }
                    $tabForm08.find("#show_curr5").val("TWD");
                    $tabForm08.find("#show_curr5").val("1000");
                    $tabForm08.find("#curr5").val("TWD");
                    $tabForm08.find("#unit").val("1000");
                    $tabForm08.find("#curr6").val("TWD");
                    $tabForm08.find("#unit1").val("1000");
                    $tabForm08.find("#curr7").val("TWD");
                    $tabForm08.find("#unit2").val("1000");
                    
                    $("#thickboxGrp").thickbox({ // 使用選取的內容進行彈窗
                        title: i18n.clss08a["L120S08.thickbox10"],
                        width: 960,
                        height: 480,
                        modal: true,
                        i18n: i18n.def,
                        buttons: {
                            "saveData": function(){
                                //								$.thickbox.close();
                                if ($("#tabForm08").valid()) {
                                    $.ajax({
                                        handler: _handler,
                                        type: "POST",
                                        dataType: "json",
                                        data: {
                                            formAction: "save",
                                            page: "91",
                                            mainId: responseJSON.mainId,
                                            toM4: $("#tabForm08").find("[name='toM4']:radio:checked").val(),
                                            typem4: $("#tabForm08").find("#typem4").val(),
                                            GroupCompanyID1: $("#tabForm08").find("#GroupCompanyID1").val(),
                                            GroupCompanyName1: $("#tabForm08").find("#GroupCompanyName1").val(),
                                            curr5: $("#tabForm08").find("#curr5").val(),
                                            unit: $("#tabForm08").find("#unit").val(),
                                            grt_IsNgRec: $("#tabForm08").find("#grt_IsNgRec").val(),
                                            grt_data_src: $("#tabForm08").find("#grt_data_src").val(),
                                            grt_data_date: $("#tabForm08").find("#grt_data_date").val(),
                                            curr6: $("#tabForm08").find("#curr6").val(),
                                            unit1: $("#tabForm08").find("#unit1").val(),
                                            grp_credit_note: $("#tabForm08").find("#grp_credit_note").val(),
                                            curr7: $("#tabForm08").find("#curr7").val(),
                                            unit2: $("#tabForm08").find("#unit2").val(),
                                            grp_ov_note: $("#tabForm08").find("#grp_ov_note").val(),
                                            gcom_SrcDate: $("#tabForm08").find("#gcom_SrcDate").val(),
                                            gcom_note1: $("#tabForm08").find("#gcom_note1").val(),
                                            setGrp: true
                                        },
                                        success: function(json){
                                            $("#formL120m01e").setData(json.formL120m01e, false);
                                        }
                                    });
                                }
                            },
                            "print": function(){
                                printA91();
                            },
                            "close": function(){
                                API.confirmMessage(i18n.def['flow.exit'], function(res){
                                    if (res) {
                                        $.thickbox.close();
                                    }
                                });
                            }
                        }
                    });
                }
            });
            // 控制分頁頁籤內容唯讀(不包括下拉式選單)
            if (responseJSON.readOnly == "true") {
                $("#tabForm08").readOnlyChilds(true);
                $("#tabForm08").find("button").hide();
            }
        });
        $("#lmss08b_panel").attr("open", false);
    } else {
        $.ajax({
            handler: _handler,
            type: "POST",
            dataType: "json",
            data: {
                formAction: "queryGrp",
                page: "91",
                mainId: responseJSON.mainId
            },
            success: function(json){
                var $tabForm08 = $("#tabForm08");
                $tabForm08.reset();
                $tabForm08.setData(json, false);
                if ($tabForm08.find("input[name=isGroupCompany1]:radio:checked").val() == "1") {
                    $tabForm08.find("#isGroupCompany1-1").show().siblings("[id^=isGroupCompany1]").hide();
                }
                $tabForm08.find("#curr6").val("TWD");
                $tabForm08.find("#unit1").val("1000");
                $tabForm08.find("#curr7").val("TWD");
                $tabForm08.find("#unit2").val("1000");
                
                $("#thickboxGrp").thickbox({ // 使用選取的內容進行彈窗
                    title: i18n.clss08a["L120S08.thickbox10"],
                    width: 960,
                    height: 480,
                    modal: true,
                    i18n: i18n.def,
                    buttons: {
                        "saveData": function(){
                            $.thickbox.close();
                            $.ajax({
                                handler: _handler,
                                type: "POST",
                                dataType: "json",
                                data: {
                                    formAction: "save",
                                    page: "91",
                                    mainId: responseJSON.mainId,
                                    toM4: $("#tabForm08").find("[name='toM4']:radio:checked").val(),
                                    typem4: $("#tabForm08").find("#typem4").val(),
                                    GroupCompanyID1: $("#tabForm08").find("#GroupCompanyID1").val(),
                                    GroupCompanyName1: $("#tabForm08").find("#GroupCompanyName1").val(),
                                    curr5: $("#tabForm08").find("#curr5").val(),
                                    unit: $("#tabForm08").find("#unit").val(),
                                    grt_IsNgRec: $("#tabForm08").find("#grt_IsNgRec").val(),
                                    grt_data_src: $("#tabForm08").find("#grt_data_src").val(),
                                    grt_data_date: $("#tabForm08").find("#grt_data_date").val(),
                                    curr6: $("#tabForm08").find("#curr6").val(),
                                    unit1: $("#tabForm08").find("#unit1").val(),
                                    grp_credit_note: $("#tabForm08").find("#grp_credit_note").val(),
                                    curr7: $("#tabForm08").find("#curr7").val(),
                                    unit2: $("#tabForm08").find("#unit2").val(),
                                    grp_ov_note: $("#tabForm08").find("#grp_ov_note").val(),
                                    gcom_SrcDate: $("#tabForm08").find("#gcom_SrcDate").val(),
                                    gcom_note1: $("#tabForm08").find("#gcom_note1").val(),
                                    setGrp: true
                                },
                                success: function(json){
                                    $("#formL120m01e").setData(json.formL120m01e, false);
                                }
                            });
                        },
                        "print": function(){
                            printA91();
                        },
                        "close": function(){
                            API.confirmMessage(i18n.def['flow.exit'], function(res){
                                if (res) {
                                    $.thickbox.close();
                                }
                            });
                        }
                    }
                });
            }
        });
    }
}

//連保人(產報表)
function printA41(){
    if ($("#s41grid").jqGrid('getGridParam', 'records') <= 0) {
        // 報表無資料
        CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
    } else {
        var pdfName = "l120r01.pdf";
        var count = 0;
        var content = "";
        content = "R41" + "^" + "";
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                rptOid: content,
                fileDownloadName: pdfName,
                serviceName: "lms1201r01rptservice"
            }
        });
    }
}

//最新集團企業(產報表)
function printA91(){
    if ($("#s91t1f1grid").jqGrid('getGridParam', 'records') <= 0) {
        // 報表無資料
        CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
    } else {
        var pdfName = "l120r01.pdf";
        var count = 0;
        var content = "";
        content = "R91" + "^" + "";
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                rptOid: content,
                fileDownloadName: pdfName,
                serviceName: "lms1201r01rptservice"
            }
        });
    }
}

//UFO@20130114 案件報告表 BEGIN
function seachKindCaseRpt(){
    //	if ($("#lmss08f_page").attr("open") == "true") {
    $("#lmss08f_page").load("../../lms/lmss08f", function(){
        $("#thickboxCaseRpt").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.clss08a["L120S08.thickbox16"],
            width: 960,
            height: 480,
            modal: true,
            i18n: i18n.def,
            buttons: {
                "close": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
        
    });
    //		$("#lmss08f_page").attr("open", false);
    //	} else {
    //		$("#thickboxCaseRpt").thickbox({ // 使用選取的內容進行彈窗
    //			title : i18n.clss08a["L120S08.thickbox16"],
    //			width : 960,
    //			height : 480,
    //			modal : true,
    //			i18n : i18n.def,
    //			buttons : {
    //				"close" : function() {
    //					API.confirmMessage(i18n.def['flow.exit'], function(res) {
    //						if (res) {
    //							$.thickbox.close();
    //						}
    //					});
    //				}
    //			}
    //		});
    //	}
}

function getL140M01A(l140m01a_oid){
	$.ajax({
        handler: 'cls1141m01formhandler',
        data: {
            formAction: "getRelateA",
            'l140m01a_oid': l140m01a_oid
        },
        success: function(json_getRelateA){
        	$.form.submit({
                url: "../cls1151s01",
                data: json_getRelateA,
                target: l140m01a_oid
            });
        }
    });
}

/**
 * 小放會會議記錄
 */
var L120S08GAction = {
    docUrl: "/lms/lms1505m01/02",
    setDesc: function(value){
    	//J-112-0586_05097_B1001 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
    	$("#docDscr9").html(DOMPurify.sanitize(value));
    },
    isInit: false,
    grid: null,
    init: function(){
        if (!L120S08GAction.isInit) {
            L120S08GAction.grid = $("#L120S08GGrid").iGrid({
                handler: 'lms1505gridhandler',
                height: 200,
                sortname: 'meetingDate',
                sortorder: 'desc',
                multiselect: true,
                hideMultiselect: false,
                postData: {
                    formAction: "query",
                    createType: "3"//1.海外 2.企金 3.個金
                },
                rowNum: 15,
                colModel: [{
                    colHeader: i18n.clss08a['L150M01a.meetingDate'],//"會議日期",
                    name: 'meetingDate',
                    align: "center",
                    width: 70,
                    sortable: true
                
                }, {
                    colHeader: i18n.clss08a['L150M01a.gist'],//"案由",
                    name: 'gist',
                    width: 100,
                    sortable: true
                }, {
                    colHeader: "oid",
                    name: 'oid',
                    hidden: true
                }, {
                    colHeader: "mainId",
                    name: 'mainId',
                    hidden: true
                }, {
                    colHeader: "docURL",
                    name: 'docURL',
                    hidden: true
                }]
            });
            L120S08GAction.isInit = true;
        }
    },
    openBox: function(){
        L120S08GAction.init();
        $("#L120S08GBox").thickbox({
            title: "",
            width: 800,
            height: 400,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $grid = L120S08GAction.grid;
                    //多筆
                    var rowData = $grid.getSelectData("oid");
                    if (rowData) {
                        $.ajax({
                            handler: _handler,
                            formId: 'empty',
                            action: "saveL120M01EBy9",
                            data: {
                                docUrl: L120S08GAction.docUrl,
                                oids: rowData
                            },
                            success: function(obj){
                                $.thickbox.close();
                                L120S08GAction.setDesc(obj.desc);
                            }
                        });
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    clean: function(){
        $.ajax({
            handler: _handler,
            formId: 'empty',
            action: "deleteL120M01EBy9",
            data: {},
            success: function(obj){
                L120S08GAction.setDesc("");
            }
        });
    },
    openLink: function($this){
    	//J-112-0586_05097_B1001 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        //var oid = $this.attr("oid");
    	var oid = $this.attr("title"); //oid會被DOMPurify.sanitize清除
        $.ajax({ // 查詢主要借款人資料
            handler: _handler,
            type: "POST",
            dataType: "json",
            data: {
                formAction: "getL150M01A",
                L150M01AOid: oid
            },
            success: function(json){
                BrowserAction.submit({
                    system: "lms",
                    url: json.url,
                    mainId: json.mainId,
                    mainOid: json.mainOid,
                    txCode: "328006",
                    mainDocStatus: "01O",
                    data: { //其它參數
                        fromView: true,
                        uid: json.uid,
                        oid: json.mainOid,
                        mainOid: json.mainOid
                    }
                });
            }
        });
        
        
    }
};

