#panel
doc.title01=Personal Application Credit Rating Model
doc.title02=Canada
label.title=RISK RATING SCORING SYSTEM - FOR PERSONAL BORROWER
label.row01=Item
label.row02=Maximum Points
label.row03=Scoring Criteria
label.row04=Score
label.row05=Previous Score
label.row06=Remarks

label.column01=BEACON Score
label.column02=Total Debt Service Ratio (TDSR) or Debt Service Coverage Ratio (DSCR)
label.column03=Loan-to-Value (LTV) Ratio 
label.column04=Years with Current Employer/ Self-Employed
label.column05=Stability of Source of Income
label.column06=Property Usage Types
label.column07=Comprehensive Assessment  (e.g.:Personal financial net worth / Loans with/without guarantor/ Potential profit contribution/ Has the borrower ever gone through bankruptcy? / Is the borrower the endorser or guarantor of anyone else's debt? )
label.column08=Total Score
label.column09=Risk Rating (*)
label.column10=Evaluation Results
label.column11=Frequency of Credit Review

label.RemarksBEACON=A score of 8 is assigned if a bureau credit score is not available.
label.RemarksTypes=If 100% term deposit is pledged as collateral, a highest score of 5 is assigned.
label.RemarksAssessment01=1. Please ener your comments. TKS. ^_^
label.RemarksAssessment02=2. The basic score is 10.
label.RemarksAssessment03=3. The highest score that the borrower could receive is 13.. 

label.MaxPoint5=5
label.MaxPoint10=10
label.MaxPoint15=15
label.MaxPoint20=20

label.CriteriaBEACON01=20 (>= 780)
label.CriteriaBEACON02=17 (779~680)
label.CriteriaBEACON03=14 (679~580)
label.CriteriaBEACON04=11 (579~500)
label.CriteriaBEACON05=8 (< 500)
label.CriteriaTDSR=** TDSR:
label.CriteriaTDSR01=20 (<= 20 %)
label.CriteriaTDSR02=17((20%, 40%])
label.CriteriaTDSR03=14 ((40%, 50%])
label.CriteriaTDSR04=11 ( > 50%)
label.CriteriaDSCR=** DSCR:
label.CriteriaDSCR01=20 (>=130%)     
label.CriteriaDSCR02=17([110%, 130%))
label.CriteriaDSCR03=14([100%, 110%))
label.CriteriaDSCR04=11 (< 100%)
label.CriteriaLTV01=20 (<=50%)
label.CriteriaLTV02=18 ((50%, 60%])
label.CriteriaLTV03=16 ((60%, 70%])
label.CriteriaLTV04=14 ((70%, 75%])
label.CriteriaLTV05=12 ((75%, 100%])
label.CriteriaLTV06=10 (no property to provide as collateral)
label.CriteriaYears01=10 ( >= 5 years)
label.CriteriaYears02=8  ([2.5 years, 5 years))
label.CriteriaYears03=6  ([1.5 years, 2.5 years))
label.CriteriaYears04=4  ((6 months, 1.5 years))
label.CriteriaYears05=2  (<= 6 months)
label.CriteriaStability01=10 (Excellent)
label.CriteriaStability02=8  (Good)
label.CriteriaStability03=6  (Fair)
label.CriteriaStability04=4  (Poor)
label.CriteriaTypes01=5 (Primary Residence)
label.CriteriaTypes02=4 (Second Home)
label.CriteriaTypes03=3 (Investment)
label.CriteriaTypes04=2 (No collateral)

label.Comments=Comments:

text.score0=0
text.score1=1
text.score2=2
text.score3=3
text.score4=4
text.score5=5
text.score6=6
text.score7=7
text.score8=8
text.score9=9
text.score10=10
text.score11=11
text.score12=12
text.score13=13
text.score14=14
text.score15=15
text.score16=16
text.score17=17
text.score18=18
text.score19=19
text.score20=20

#standerd table
label.ST_row01=(*) RATING SCALE:
label.ST_row02=Grade
label.ST_row03=EVALUATION RESULTS
label.ST_row04=Credit Review Requirement
label.ST_column01=(Scores >= 90 points)
label.ST_column02=(Scores: 85~89 points)
label.ST_column03=(Scores: 80~84 points)
label.ST_column04=(Scores: 75~79 Points)
label.ST_column05=(Scores: 70~74 Points)
label.ST_column06=(Scores: 65~69 Points)
label.ST_column07=(Scores: 60~64 Points)
label.ST_column08=(Scores: 55~59 Points)
label.ST_column09=(Scores: 50~54 Points)
label.ST_column10=(Scores: < 50 Points)
label.ER01=Satisfactory
label.ER02=Especially Mentioned
label.ER03=Substandard
label.ER04=Doubtful
label.ER05=Loss
label.CR01=on an annual basis
label.CR02=on a semi-annual basis
label.CR03=on a quarterly basis

#
ErrorMessage=Score can not be 0