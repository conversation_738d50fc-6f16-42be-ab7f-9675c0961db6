package com.mega.eloan.lms.batch.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.Engine;
import com.inet.report.ReportException;
import com.mega.eloan.lms.base.report.AbstractIISIReportService;
import com.mega.eloan.lms.batch.report.LMSSEL001RptService;
import com.mega.eloan.lms.mfaloan.service.MisLNF155Service;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.NumericFormatter;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;

@Service("lmssel006rptservice")
public class LMSSEL001RptServiceImpl extends AbstractIISIReportService
		implements LMSSEL001RptService {

	@Resource
	MisLNF155Service misLNF155Service;

	@Override
	public ReportData getReportParameter(PageParameters params, ReportData reportData,
			Engine engine) {

		NumericFormatter nf = new NumericFormatter("#,###.##");
		DecimalFormat df = new DecimalFormat("##.##%");

		try {
			// 沒帶年月參數，使用系統年月
			String reqMonth = params.get("yyyyMM").toString();
			String currentDate = CapDate
					.getCurrentDate(CapDate.DEFAULT_DATE_FORMAT);
			String currentYear = reqMonth.isEmpty() ? currentDate.substring(0,
					4) : reqMonth.substring(0, 4);
			String currentMonth = reqMonth.isEmpty() ? currentDate.substring(4,
					6) : reqMonth.substring(4, 6);
			String lastSeason = ""; // 上季
			String thisSeason = ""; // 當季
			if ("01".equals(currentMonth)) {
				lastSeason = String.valueOf(Integer.valueOf(currentYear) - 1)
						+ "-09";
				thisSeason = String.valueOf(Integer.valueOf(currentYear) - 1)
						+ "-12";
			} else if ("04".equals(currentMonth)) {
				lastSeason = String.valueOf(Integer.valueOf(currentYear) - 1)
						+ "-12";
				thisSeason = currentYear + "-03";
			} else if ("07".equals(currentMonth)) {
				lastSeason = currentYear + "-03";
				thisSeason = currentYear + "-06";
			} else if ("10".equals(currentMonth)) {
				lastSeason = currentYear + "-06";
				thisSeason = currentYear + "-09";
			} else {
				// 批次日期錯誤
				return reportData;
			}
			reportData.setField("thisMonth", thisSeason.substring(5, 7));
			reportData.setField("lastMonth", lastSeason.substring(5, 7));

			List<Map<String, Object>> list = misLNF155Service.findSeasonData(
					thisSeason, lastSeason);

			List<List<String>> details = new ArrayList<List<String>>();
			BigDecimal sum1 = new BigDecimal(0);
			BigDecimal sum2 = new BigDecimal(0);
			BigDecimal sum3 = new BigDecimal(0);
			BigDecimal sum4 = new BigDecimal(0);
			for (Map<String, Object> map : list) {
				List<String> detail = new ArrayList<String>();
				// BRNO ELF339_BRNM SUM_BAL_TA_UN SUM_BAL_TA SUM_BAL_TB_UN
				// SUM_BAL_TB
				String brno = CapString.trimNull(map.get("BRNO"));
				String brnm = CapString.trimNull(map.get("ELF339_BRNM"));
				String sumBalTAUn = CapString
						.trimNull(map.get("SUM_BAL_TA_UN"));
				String sumBalTA = CapString.trimNull(map.get("SUM_BAL_TA"));
				String colA = "0";
				if (CapMath.compare("0", sumBalTA) != 0) {
					colA = CapMath.divide(sumBalTAUn, sumBalTA, 4);
				}
				String sumBalTBUn = CapString
						.trimNull(map.get("SUM_BAL_TB_UN"));
				String sumBalTB = CapString.trimNull(map.get("SUM_BAL_TB"));
				String colB = "0";
				if (CapMath.compare("0", sumBalTB) != 0) {
					colB = CapMath.divide(sumBalTBUn, sumBalTB, 4);
				}
				sum1 = sum1.add(CapMath.getBigDecimal(sumBalTAUn));
				sum2 = sum2.add(CapMath.getBigDecimal(sumBalTA));
				sum3 = sum3.add(CapMath.getBigDecimal(sumBalTBUn));
				sum4 = sum4.add(CapMath.getBigDecimal(sumBalTB));
				String colC = CapMath.subtract(colA, colB);
				detail.add(brno);
				detail.add(brnm);
				detail.add(nf.reformat(sumBalTAUn));
				detail.add(nf.reformat(sumBalTA));
				detail.add(df.format(Double.valueOf(colA)));
				detail.add(nf.reformat(sumBalTBUn));
				detail.add(nf.reformat(sumBalTB));
				detail.add(df.format(Double.valueOf(colB)));
				detail.add(df.format(Double.valueOf(colC)));
				details.add(detail);
			}
			List<String> detail = new ArrayList<String>();
			String rateA = "0";
			if (sum2.compareTo(BigDecimal.ZERO) != 0) {
				rateA = CapMath.divide(CapMath.bigDecimalToString(sum1),
						CapMath.bigDecimalToString(sum2), 4);
			}
			String rateB = "0";
			if (sum4.compareTo(BigDecimal.ZERO) != 0) {
				rateB = CapMath.divide(CapMath.bigDecimalToString(sum3),
						CapMath.bigDecimalToString(sum4), 4);
			}
			String rateC = CapMath.subtract(rateA, rateB);
			detail.add("總計");
			detail.add("");
			detail.add(nf.reformat(CapMath.bigDecimalToString(sum1)));
			detail.add(nf.reformat(CapMath.bigDecimalToString(sum2)));
			detail.add(df.format(Double.valueOf(rateA)));
			detail.add(nf.reformat(CapMath.bigDecimalToString(sum3)));
			detail.add(nf.reformat(CapMath.bigDecimalToString(sum4)));
			detail.add(df.format(Double.valueOf(rateB)));
			detail.add(df.format(Double.valueOf(rateC)));
			details.add(detail);
			reportData.addDetail(details);
			reportData.setField("printDate",
					CapDate.getCurrentDate("yyyy-MM-dd"));
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		return reportData;

	}

	@Override
	public String getReportDefinition() {
		return "report/lms/LLSEL001";
	}

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}

}
