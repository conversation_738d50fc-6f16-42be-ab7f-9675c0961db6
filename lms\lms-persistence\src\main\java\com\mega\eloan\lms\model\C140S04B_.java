package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

/**
 * <pre>
 * The persistent class for the C140S04B database table.
 * </pre>
 * @since  2011/9/20
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/9/20,<PERSON>,new
 *          </ul>
 */
@StaticMetamodel(C140S04B.class)
public class C140S04B_ {
	public static volatile SingularAttribute<C140S04B, BigDecimal> landAmtUnit;
	public static volatile SingularAttribute<C140S04B, String> landCurr;
	public static volatile SingularAttribute<C140S04B, String> landAddr;
	public static volatile SingularAttribute<C140S04B, BigDecimal> landAm;
	public static volatile SingularAttribute<C140S04B, BigDecimal> landAp;
	public static volatile SingularAttribute<C140S04B, BigDecimal> landBm;
	public static volatile SingularAttribute<C140S04B, BigDecimal> landBp;
	public static volatile SingularAttribute<C140S04B, String> landLevel;
	public static volatile SingularAttribute<C140S04B, String> landCust;
	public static volatile SingularAttribute<C140S04B, BigDecimal> landMm;
	public static volatile SingularAttribute<C140S04B, String> landMp;
	public static volatile SingularAttribute<C140S04B, String> landNum;
	public static volatile SingularAttribute<C140S04B, BigDecimal> landRateC;
	public static volatile SingularAttribute<C140S04B, BigDecimal> landRateD;
	public static volatile SingularAttribute<C140S04B, String> landUnit1;
	public static volatile SingularAttribute<C140S04B, String> landUnit2;
	public static volatile SingularAttribute<C140S04B, String> landUse1;
	public static volatile SingularAttribute<C140S04B, String> landUse2;
	public static volatile SingularAttribute<C140S04B, C140M04A> c140m04a;
}
