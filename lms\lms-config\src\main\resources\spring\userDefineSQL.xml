<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="
http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-2.0.xsd">

	<!--Data access using JDBC (Spring) http://static.springsource.org/spring/docs/2.0.x/reference/jdbc.html -->
	
	<import resource="classpath:sql/aLoanSQL.xml"/>
	
	<import resource="classpath:sql/eLoanSQL.xml"/>
	<import resource="classpath:sql/misSQL.xml"/>
	<import resource="classpath:sql/dwSQL.xml"/>
	<import resource="classpath:sql/odsSQL.xml"/>
	
	<import resource="classpath:sql/obsdbSQL.xml"/>
	<import resource="classpath:sql/etchSQL.xml"/>
	<import resource="classpath:sql/ejcicSQL.xml"/>
	<import resource="classpath:sql/eLoanCMSSQL.xml"/>
	<import resource="classpath:sql/tejSQL.xml"/>
	<import resource="classpath:sql/eLoanFSSQL.xml"/>
	<import resource="classpath:sql/megaimageSQL.xml"/>
	
	<bean id="nativeSqlStatement" class="tw.com.iisi.cap.context.CapParameter">
		<constructor-arg>
			<util:set value-type="java.util.HashMap">
				<ref bean="aLoanSql"/>
			</util:set>
		</constructor-arg>
	</bean>
	<!-- eLoanSqlStatement -->
	<bean id="eLoanSqlStatement" class="tw.com.iisi.cap.context.CapParameter">
		<constructor-arg>
			<util:set value-type="java.util.HashMap">
				<ref bean="eLoanSql"/>
			</util:set>
		</constructor-arg>
	</bean>
	<!-- eLoanCMSSqlStatement -->
	<bean id="eLoanCMSSqlStatement" class="tw.com.iisi.cap.context.CapParameter">
		<constructor-arg>
			<util:set value-type="java.util.HashMap">
				<ref bean="eLoanCMSSql"/>
			</util:set>
		</constructor-arg>
	</bean>
	<!-- misSqlStatement -->
	<bean id="misSqlStatement" class="tw.com.iisi.cap.context.CapParameter">
		<constructor-arg>
			<util:set value-type="java.util.HashMap">
				<ref bean="misSql"/>
			</util:set>
		</constructor-arg>
	</bean>
	<!-- dwSqlStatement -->
	<bean id="dwSqlStatement" class="tw.com.iisi.cap.context.CapParameter">
		<constructor-arg>
			<util:set value-type="java.util.HashMap">
				<ref bean="dwSql"/>
			</util:set>
		</constructor-arg>
	</bean>
    <!-- odsSqlStatement -->
    <bean id="odsSqlStatement" class="tw.com.iisi.cap.context.CapParameter">
        <constructor-arg>
            <util:set value-type="java.util.HashMap">
                <ref bean="odsSql"/>
            </util:set>
        </constructor-arg>
    </bean>
	
	<!-- as400Statement -->
	<bean id="osbdbSqlStatement" class="tw.com.iisi.cap.context.CapParameter">
		<constructor-arg>
			<util:set value-type="java.util.HashMap">
				<ref bean="obsdbSql"/>
			</util:set>
		</constructor-arg>
	</bean>
	
	<!-- etchStatement -->
	<bean id="etchSqlStatement" class="tw.com.iisi.cap.context.CapParameter">
		<constructor-arg>
			<util:set value-type="java.util.HashMap">
				<ref bean="eTchSql"/>
			</util:set>
		</constructor-arg>
	</bean>
	
	<!-- ejcicStatement -->
	<bean id="ejcicSqlStatement" class="tw.com.iisi.cap.context.CapParameter">
		<constructor-arg>
			<util:set value-type="java.util.HashMap">
				<ref bean="ejcicSql"/>
			</util:set>
		</constructor-arg>
	</bean>
	
	<!--tejSqlStatement-->
    <bean id="tejSqlStatement" class="tw.com.iisi.cap.context.CapParameter">
        <constructor-arg>
            <util:set value-type="java.util.HashMap">
                <ref bean="tejSql"/>
            </util:set>
        </constructor-arg>
    </bean>
	
	<!-- eLoanFsSqlStatement -->
	<bean id="eLoanFsSqlStatement" class="tw.com.iisi.cap.context.CapParameter">
		<constructor-arg>
			<util:set value-type="java.util.HashMap">
				<ref bean="eLoanFsSql"/>
			</util:set>
		</constructor-arg>
	</bean>
	
	<!--megaimageSqlStatement-->
    <bean id="megaimageSqlStatement" class="tw.com.iisi.cap.context.CapParameter">
        <constructor-arg>
            <util:set value-type="java.util.HashMap">
                <ref bean="megaimageSql"/>
            </util:set>
        </constructor-arg>
    </bean>
	
</beans>
