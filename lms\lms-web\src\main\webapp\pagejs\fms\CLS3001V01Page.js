$(function(){	
	var my_colModel = [{
        colHeader: "",name: 'oid', hidden: true
    }, {
    	colHeader: "",name: 'mainId', hidden: true
    }, {
        colHeader: i18n.cls3001v01["C900S02D.caseBrId"], //分行別
        align: "left", width: 70, sortable: true, name: 'caseBrId'
    }, {
        colHeader: i18n.cls3001v01["C900S02D.custId"], //統一編號
        align: "left", width: 80, sortable: true, name: 'custId',
        formatter: 'click', onclick: openDoc
    }, {
        colHeader: i18n.cls3001v01["C900S02D.custName"], //姓名/名稱
        align: "left", width: 110, sortable: true, name: 'custName'
    }, {
        colHeader: i18n.cls3001v01["C900S02D.category"], //類別
        align: "left", width: 90, sortable: true, name: 'category'
    }, {
        colHeader: i18n.cls3001v01["C900S02D.cntrNo"], //額度序號
        align: "left", width: 100, sortable: true, name: 'cntrNo'
    }, {
        colHeader: i18n.cls3001v01["C900S02D.document_no"], //簽報書案號
        align: "left", width: 150, sortable: true, name: 'document_no'
    }, {
    	colHeader: i18n.cls3001v01["C900S02D.updater"], //異動人員
        align: "left", width: 80, sortable: true, name: 'updater'
    }, {
    	colHeader: i18n.cls3001v01["C900S02D.updateTime"], //異動日期
        align: "left", width: 100, sortable: true, name: 'updateTime'
    }];
	
	if(viewstatus == "030"){
		//
		my_colModel.push({
	    	colHeader: i18n.cls3001v01["C900S02D.approver"], //核准人員
	        align: "left", width: 80, sortable: true, name: 'approver'
	    });
		my_colModel.push({
	    	colHeader: i18n.cls3001v01["C900S02D.approveTime"], //核准日期
	        align: "left", width: 100, sortable: true, name: 'approveTime'
	    });
	}
	
	
	var grid = $("#gridview").iGrid({
        handler: "cls3001gridhandler",
        height: 350,
        rowNum: 15,
        shrinkToFit: false,
        postData: {
            formAction: "queryMain",
            docStatus: viewstatus,
            ownBrId: userInfo.unitNo
        },
        colModel: my_colModel,
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
    	var postData = {
    			'mainOid': rowObject.oid, 
    			'mainId': rowObject.mainId,
    			'mainDocStatus': viewstatus
    		}
    	
    	if (typeof noOpenDoc != 'undefined' && noOpenDoc == 'Y'){
    		postData['noOpenDoc'] = true;
    	};
    	
		$.form.submit({
			url : '../fms/cls3001m01/01',
			data : postData,
			target : rowObject.oid || '_blank'
		});
    }
    
    $("#buttonPanel").find("#btnFilter").click(function(){
    	var _id = "_div_cls3001v01_filter";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).size() == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table class='tb2' >");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3001v01["C900S02D.caseBrId"]+"</td><td>"
					+"<input type='text' id='search_caseBrId' name='search_caseBrId'  maxlength='3' size='6'>"
					+"</td></tr>");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3001v01["C900S02D.custId"]+"</td><td>"
					+"<input type='text' id='search_custId' name='search_custId'  maxlength='10' size='12'>"
					+"</td></tr>");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3001v01["C900S02D.cntrNo"]+"</td><td>"
					+"<input type='text' id='search_cntrNo' name='search_cntrNo'  maxlength='12' size='14'>"
					+"</td></tr>");
			dyna.push(" </table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		    $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
			//ui_lms2401.msg02=請選擇不覆審原因
	       title: '篩選',
	       width: 550,
           height: 190,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
                  $.thickbox.close();
                  //=============
                  grid.setGridParam({
  	                postData: $.extend(
  	                	{}
  	                	,$("#"+_form).serializeData()
  	                ),
  	                search: true
                  }).trigger("reloadGrid");
               },
               "cancel": function(){
            	   $.thickbox.close();
               }
           }
		});
    }).end().find("#btnAdd").click(function(){
		var rowObject = {};
		openDoc(null, null, rowObject);
    }).end().find("#btnDelete").click(function(){
    	var rows = grid.getGridParam('selrow');
        var list = "";
        if (rows != 'undefined' && rows != null && rows != 0) {
            var data = grid.getRowData(rows);
            list = data.oid;
        }
        if (list == "") {
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "cls3001formhandler",
                    type: "POST",
                    dataType: "json",
                    data: {
                        formAction: "deleteMark",
                        list: list,
                        docStatus: viewstatus
                    },
                    success: function(obj){
                    	if(obj.saveOkFlag){
                    		API.showMessage(i18n.def.runSuccess);//執行成功
                    	}
                    	grid.trigger("reloadGrid");
                    }
                });
            }
        });	
    }).end().find("#btnCreateExl").click(function(){
    	$("#div_produceXls").find("#form_produceXls_category").val(i18n.cls3001v01["C900S02D.category"]);
    	$("#div_produceXls").find("#form_produceXls_caseBrId").val(i18n.cls3001v01["C900S02D.caseBrId"]);
    	//===========
    	$("#div_produceXls")
    	//===========
    	$("#div_produceXls").thickbox({
            title: "",
            width: 500, height: 200, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
            buttons: {
                "sure": function(){
               	 var $frm = $("#form_produceXls"); 
                    if (true) {                   	                        
                    	//不能直接用 $.capFileDownload(...)，會強制 encode 把  | 轉成 %7C
                        $.form.submit({
                       	url: __ajaxHandler,
                    		target : "_blank",
                    		data : $.extend({
                    			_pa : 'lmsdownloadformhandler',
                    			'fileDownloadName' : 'data.xls',
                    			'serviceName' : "cls3001r01rptservice"
                    		}, $frm.serializeData() )
                    	 });
                    	 $.thickbox.close();
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
});
