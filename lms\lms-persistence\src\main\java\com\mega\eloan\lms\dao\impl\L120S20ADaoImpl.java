/* 
 * L120S20ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Query;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S20ADao;
import com.mega.eloan.lms.model.L120S20A;

/** LGD額度共用檔 **/
@Repository
public class L120S20ADaoImpl extends LMSJpaDao<L120S20A, String> implements
		L120S20ADao {

	@Override
	public L120S20A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S20A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S20A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S20A> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L120S20A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);

		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S20A> findByIndex02(String mainId, String cntrNoCo) {
		ISearch search = createSearchTemplete();
		List<L120S20A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (cntrNoCo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNoCo",
					cntrNoCo);

		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S20A> findByIndex03(String mainId, String cntrNoCo,
			String cntrNo) {
		ISearch search = createSearchTemplete();
		List<L120S20A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (cntrNoCo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNoCo",
					cntrNoCo);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);

		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<Object[]> findMinAllocate(String mainId) {

		// String sql =
		// "SELECT cntrno,min(ALLOCATE2) FROM lms.l120s20a WHERE mainid = ?1 GROUP BY cntrno HAVING count(*) > 1 ";
		String sql = "SELECT CNTRNO,MIN_ALLOCATE2 FROM (SELECT cntrno,min(ALLOCATE2) AS MIN_ALLOCATE2 FROM lms.l120s20a WHERE mainid = ?1 AND cntrno NOT IN (SELECT cntrno FROM lms.l120s20b WHERE MAINID = ?2 ) GROUP BY cntrno HAVING count(*) > 1) AS T1 ORDER BY MIN_ALLOCATE2 DESC ";
		Query query = entityManager.createNativeQuery(sql); // 排除掉最後面的AND

		if (!StringUtils.isBlank(mainId)) {
			query.setParameter(1, mainId);
			query.setParameter(2, mainId);
		}

		List<Object[]> resultList = query.getResultList();
		return resultList;

	}

	@Override
	public List<L120S20A> findByCntrNo(String mainId, String cntrNo) {
		ISearch search = createSearchTemplete();
		List<L120S20A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);

		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數

		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

}