/* 
 * LMS1205ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dao.L700M01ADao;
import com.mega.eloan.lms.fms.service.LMS7005Service;
import com.mega.eloan.lms.model.L700M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;

/**
 * <pre>
 * 案件分案對照表Service Impl
 * <AUTHOR> Lin
 * </pre>
 */
@Service
public class LMS7005ServiceImpl extends AbstractCapService implements
		LMS7005Service {
	@Resource
	L700M01ADao l700m01adao;

	@Override
	public L700M01A findL700m01aByOid(String oid) {
		// 透過Oid取得資料
		return l700m01adao.findByOid(oid);
	}

	@Override
	public L700M01A findL700m01aByUniqueKey(String branchId, String subject) {
		// 透過獨特Key取得資料
		return l700m01adao.findByUniqueKey(branchId, subject);
	}

	@Override
	public List<L700M01A> findL700m01aList(ISearch search) {
		// 取得List資料
		return l700m01adao.find(search);
	}

	@Override
	public List<L700M01A> findL700m01aList() {
		//20130416 Vector 加入只抓當前分行條件
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		ISearch search = l700m01adao.createSearchTemplete();		
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());
		return l700m01adao.find(search);
	}	
	
	@Override
	public void deleteListL700m01a(String[] oidArray) {
		// 刪除多筆資料
		for (int i = 0; i < oidArray.length; i++) {
			L700M01A model = findL700m01aByOid(oidArray[i]);
			l700m01adao.delete(model);
		}
	}

	/**
	 * <pre>
	 * 進行無限多筆儲存
	 * (non-Javadoc)
	 * @see com.mega.eloan.lms.service.LMS1205Service#save(tw.com.iisi.cap.model.GenericBean[])
	 * </pre>
	 */
	@Override
	public void save(GenericBean... entity) {
		// 進行無限多筆儲存
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L700M01A) {
					((L700M01A) model).setUpdater(user.getUserId());
					((L700M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l700m01adao.save((L700M01A) model);
					// 記錄文件異動記錄
					// docLogService.record(model.getOid(),DocLogEnum.SAVE);
				}
			}
		}
	}

	/**
	 * <pre>
	 * 進行無限多筆刪除
	 * (non-Javadoc)
	 * @see com.mega.eloan.lms.service.LMS1205Service#delete(tw.com.iisi.cap.model.GenericBean[])
	 * </pre>
	 */
	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L700M01A) {
					l700m01adao.delete((L700M01A) model);
				}
			}
		}
	}

	/**
	 * <pre>
	 * 取得 DataPage
	 * (non-Javadoc)
	 * @see com.mega.eloan.lms.service.LMS1205Service#findPage(java.lang.Class, tw.com.iisi.cap.dao.utils.ISearch)
	 * </pre>
	 */
	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L700M01A.class) {
			return l700m01adao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		return null;
	}
}
