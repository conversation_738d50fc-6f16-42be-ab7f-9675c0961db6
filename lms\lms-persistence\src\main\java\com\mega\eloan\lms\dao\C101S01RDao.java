package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C101S01R;

/** 個金卡友貸信用評等表 **/
public interface C101S01RDao extends IGenericDao<C101S01R> {

	C101S01R findByOid(String oid);
	
	List<C101S01R> findByMainId(String mainId);
	
	C101S01R findByUniqueKey(String mainId, String ownBrId, String custId, String dupNo);

	List<C101S01R> findByIndex01(String mainId, String ownBrId, String custId, String dupNo);

	List<C101S01R> findByCustIdDupId(String custId,String dupNo);
	
	int deleteByOid(String oid);
}