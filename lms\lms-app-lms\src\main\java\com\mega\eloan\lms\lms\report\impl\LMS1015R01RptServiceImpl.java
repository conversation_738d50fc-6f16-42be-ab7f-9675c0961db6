package com.mega.eloan.lms.lms.report.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.Engine;
import com.inet.report.ReportException;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.report.AbstractIISIReportService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.dao.C121M01EDao;
import com.mega.eloan.lms.lms.pages.LMS1015M01Page;
import com.mega.eloan.lms.lms.report.LMS1015R01RptService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01B;
import com.mega.eloan.lms.model.C121M01E;
import com.mega.eloan.lms.model.C121M01F;
import com.mega.eloan.lms.model.C121S01A;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.Util;

@Service("lms1015r01rptservice")
public class LMS1015R01RptServiceImpl extends AbstractIISIReportService
		implements LMS1015R01RptService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1015R01RptServiceImpl.class);
	@Resource
	CLSService clsService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	UserInfoService userInfoService;
	
	@Resource
	C121M01EDao c121m01eDao;

	@Override
	public ReportData getReportParameter(PageParameters params, ReportData reportData,
			Engine engine) {

		String oid = Util.trim(params.getString("oid"));
		String cntrNo = Util.trim(params.getString("cntrNo"));
		String loanTP = Util.trim(params.getString("loanTP"));
		C121M01A c121m01a = clsService.findC121M01A_oid(oid);
		C121S01A c121s01a = null;
		if (c121m01a != null) {
			try {
				// 若用 JSONObject.fromObject(...)，遇到有關連的 entity 時，會出錯
				reportData.setAll(DataParse.toJSON(c121m01a));

				reportData.setField("cntrNo", cntrNo);
				reportData.setField("loanTP", loanTP);

				c121s01a = clsService.findC121S01A(c121m01a);
				if (c121s01a != null) {
					reportData.setAll(DataParse.toJSON(c121s01a));

					CodeTypeFormatter cmsType = new CodeTypeFormatter(
							codeTypeService, "c121s01a_cmsType");
					CodeTypeFormatter region = new CodeTypeFormatter(
							codeTypeService, "c121s01a_region");
					CodeTypeFormatter houseAge = new CodeTypeFormatter(
							codeTypeService, "c121s01a_houseAge");
					CodeTypeFormatter collUsage = new CodeTypeFormatter(
							codeTypeService, "c121s01a_collUsage");
					CodeTypeFormatter locationType = new CodeTypeFormatter(
							codeTypeService, "c121s01a_locationType");
					
					reportData.setField("cmsType",
							cmsType.reformat(c121s01a.getCmsType()));
					reportData.setField("region",
							region.reformat(c121s01a.getRegion()));
					reportData.setField("houseAge",
							houseAge.reformat(c121s01a.getHouseAge()));
					reportData.setField("collUsage",
							collUsage.reformat(c121s01a.getCollUsage()));
					reportData.setField("locationType",
							locationType.reformat(c121s01a.getLocationType()));
					
				}
				
				String appr = "";	//經辦
			    String boss = "";	//主管
			    String manager = "";	//經副襄理
			    reportData.setField("varVer", Util.trim(c121m01a.getVarVer()));
			    if(c121m01a.getDocStatus().equals(CreditDocStatusEnum.海外_編製中.getCode())){ 
			    	appr = Util.nullToSpace(userInfoService.getUserName(c121m01a.getUpdater()));
			    	reportData.setField("appr", appr);
			    }else{
			    	List<C121M01E> c121m01eList = null;
				    c121m01eList = c121m01eDao.findByMainId(c121m01a.getMainId());
				    if(c121m01eList!=null){
					    for (C121M01E c121m01e : c121m01eList) {
					    	if ("L5".equals(c121m01e.getStaffJob())){	//經副襄理
					    		manager = Util.nullToSpace(userInfoService.getUserName(c121m01e.getStaffNo()));
					    		reportData.setField("manager", manager);
					    	} else if ("L3".equals(Util.trim(c121m01e.getStaffJob()))) {	//主管
					    		if("".equals(boss)){boss = Util.nullToSpace(userInfoService.getUserName(c121m01e.getStaffNo()));}
					    		else{boss = boss+ "、" +Util.nullToSpace(userInfoService.getUserName(c121m01e.getStaffNo()));}
					    		reportData.setField("boss", boss);
							} else if ("L1".equals(Util.trim(c121m01e.getStaffJob()))) {	//經辦
								appr = Util.nullToSpace(userInfoService.getUserName(c121m01e.getStaffNo()));
								reportData.setField("appr", appr);
							}
					    }
				    }
			    }			    
			} catch (Exception e) {
				LOGGER.error(StrUtils.getStackTrace(e));
			}

		}
		return reportData;
	}

	@Override
	public Engine getSubReportData(PageParameters params, Engine engine) {
		String oid = params.getString("oid");
		C121M01A c121m01a = clsService.findC121M01A_oid(oid);
		if (c121m01a != null) {
			try {
				Engine srp = engine.getSubReport(0);
				Engine srpF = engine.getSubReport(1);
				if (true) {
					List<C120M01A> list = clsService
							.filter_shouldRating(clsService
									.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(c121m01a));
					Properties prop_lms1015m01Page = MessageBundleScriptCreator
							.getComponentResource(LMS1015M01Page.class);

					Properties prop_abstractOverSeaCLSPage = MessageBundleScriptCreator
						.getComponentResource(AbstractOverSeaCLSPage.class);
					
					Map<String, String> custPosMap = codeTypeService
							.findByCodeType("lms1015_custPos");
					List<String[]> detailList1 = new ArrayList<String[]>();
					for (C120M01A c120m01a : list) {
						C121M01B c121m01b = clsService.findC121M01B_byC120M01A(c120m01a);
						// ====================
						String col_01 = LMSUtil
								.getDesc(custPosMap, Util.equals("Y",
										c120m01a.getKeyMan()) ? OverSeaUtil.M
										: Util.trim(c120m01a.getCustPos()));
						String col_02 = c120m01a.getCustId();
						String col_03 = Util.trim(c120m01a.getCustName());
						
						String col_04 = OverSeaUtil.get_jp_negaInfoStr(prop_lms1015m01Page
								, OverSeaUtil.TYPE_RPT, c121m01b);						
						col_04 = OverSeaUtil.jp_negaInfoEmpty(col_04);
						
						String col_05 = OverSeaUtil.get_jp_j10Desc(prop_abstractOverSeaCLSPage
								, prop_lms1015m01Page, c121m01b);
						
						String col_06 = Util.trim(c121m01b.getAdjustReason());
						String col_07 = Util.trim(c121m01b.getPRating());
						String col_08 = Util.trim(c121m01b.getSRating());
						String col_09 = Util.trim(c121m01b.getSprtRating());
						String col_10 = Util.trim(c121m01b.getFRating());

						String varVer = Util.trim(c121m01a.getVarVer());
						
						String col_11 = "";
						String col_12 = "";
						String col_13 = "";
						String col_14 = "";
						
						C121M01F c121m01f = clsService.findC121M01F_byC120M01A(c120m01a);
						if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_JP)){
							if(c121m01f != null){
								col_11 = Util.trim(c121m01f.getPRating());
								col_12 = Util.trim(c121m01f.getSRating());
								col_13 = Util.trim(c121m01f.getSprtRating());
								col_14 = Util.trim(c121m01f.getFRating());
							}
						}
						
						detailList1.add(new String[] { col_01, col_02, col_03,
								col_04, col_05, col_06, col_07, col_08, col_09,
								col_10, col_11, col_12, col_13, col_14 });
					}

					String[] columns1 = { "CommonBean1.field01",
							"CommonBean1.field02", "CommonBean1.field03",
							"CommonBean1.field04", "CommonBean1.field05",
							"CommonBean1.field06", "CommonBean1.field07",
							"CommonBean1.field08", "CommonBean1.field09",
							"CommonBean1.field10", "CommonBean1.field11",
							"CommonBean1.field12", "CommonBean1.field13",
							"CommonBean1.field14"};
					int col_cnt = columns1.length;

					// 建立所有欄位的資料，不足筆數放空白
					String[][] data1 = new String[detailList1.size()][col_cnt];
					for (int i = 0; i < detailList1.size(); i++) {
						for (int j = 0; j < col_cnt; j++) {
							data1[i][j] = "";
						}
					}
					for (int i = 0; i < detailList1.size(); i++) {
						String[] cb1List = detailList1.get(i);
						for (int j = 0; j < col_cnt; j++) {
							data1[i][j] = cb1List[j];
						}
					}
					srp.setData(columns1, data1);
					srpF.setData(columns1, data1);
				}

			} catch (ReportException e) {
				LOGGER.error(StrUtils.getStackTrace(e));
			}
		}
		return engine;
	}

	@Override
	public String getReportDefinition() {
		return "report/lms/LMS1015R01";
	}

}
