/* 
 *  CLS8011M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.handler.form;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLS8011Service;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.mfaloan.service.MISLN30Service;
import com.mega.eloan.lms.model.C801M01A;
import com.mega.eloan.lms.model.C801M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金個人資料清冊作業
 * </pre>
 * 
 * @since 2014/04/01
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("cls8011m01formhandler")
@DomainClass(C801M01A.class)
public class CLS8011M01FormHandler extends AbstractFormHandler {

	private static final int MAXLEN_C801M01A_GUARANTOR = StrUtils.getEntityFileldLegth(C801M01A.class, "guarantor", 1800);
	
	@Resource
	BranchService branchService;

	@Resource
	TempDataService tempDataService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	CLSService clsService;
	
	@Resource
	CLS8011Service cls8011Service;

	@Resource
	MISLN30Service misLN30Service;

	/**
	 * 新增C801M01A 個金個人資料清冊
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newc801m01a(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// 企/個金案件->空白: 全部、2:個金、1:企金
		String docType = Util.trim(params.getString("docType"));

		CapAjaxFormResult result = new CapAjaxFormResult();

		if (Util.equals(docType, "")) {
			// 全部
			result = genNewc801m01a(params, "1");
			result = genNewc801m01a(params, "2");
		} else {
			result = genNewc801m01a(params, docType);
		}

		return result;

	}

	public CapAjaxFormResult genNewc801m01a(PageParameters params,
			String docType) throws CapException, CapMessageException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// docType 企/個金案件->空白: 全部、2:個金、1:企金

		// 含 deleted 及非 deleted
		List<C801M01A> c801m01a_list = cls8011Service.findC801M01A_ownBrId(user
				.getUnitNo());
		Map<String, C801M01A> dcMap = new HashMap<String, C801M01A>();
		Set<String> activeC801M01A = new HashSet<String>();
		for (C801M01A model : c801m01a_list) {
			if (Util.isEmpty(model.getDeletedTime())) {
				activeC801M01A.add(model.getCntrNo());
			} else {
				dcMap.put(model.getCntrNo(), model);
			}
		}
		// ---
		Map<String, List<Map<String, Object>>> cntrNo_dataList = new HashMap<String, List<Map<String, Object>>>();
		List<Map<String, Object>> rowMap = null;

		if (Util.equals(docType, "2")) {
			// 2:個金
			rowMap = misLN30Service.findByLNF030_PersonalData(user.getUnitNo());
		} else if (Util.equals(docType, "1")) {
			// 1:企金
			rowMap = misLN30Service.findByLNF030_CoporateData(user.getUnitNo());
		}

		for (Map<String, Object> row : rowMap) {

			String cntrNo = Util.trim(MapUtils
					.getString(row, "LNF030_CONTRACT"));
			// ----------
			// 已有 有效的個人資料檔案，不再重複新增
			if (activeC801M01A.contains(cntrNo)) {
				continue;
			}
			// ----------
			if (!cntrNo_dataList.containsKey(cntrNo)) {
				List<Map<String, Object>> _list = new ArrayList<Map<String, Object>>();
				cntrNo_dataList.put(cntrNo, _list);
			}
			cntrNo_dataList.get(cntrNo).add(row);
		}

		// 先刪掉 deletedC801M01A 的 c801m01b
		for (String cntrNo : cntrNo_dataList.keySet()) {
			if (dcMap.containsKey(cntrNo)) {
				C801M01A model = dcMap.get(cntrNo);
				cls8011Service.deleteC801M01B(model.getMainId());
			}
		}

		for (String cntrNo : cntrNo_dataList.keySet()) {
			String[] arr = _mergeData(cntrNo_dataList.get(cntrNo));
			String custId = arr[0];
			String dupNo = arr[1];
			String custName = arr[2];
			String loanNo = arr[3];
			String mName = arr[4];
			String caseStatus = arr[5];
			String coCustName = arr[6];
			String guarantor = arr[7];
			String chairmanName = arr[8];

			C801M01A c801m01a = null;
			if (dcMap.containsKey(cntrNo)) {
				c801m01a = dcMap.get(cntrNo);
			} else {
				c801m01a = cls8011Service.initModel(custId, dupNo, custName,
						cntrNo);
			}
			c801m01a.setDocType(docType);
			c801m01a.setCustName(custName);
			c801m01a.setLoanNo(loanNo);
			c801m01a.setMName(mName);
			c801m01a.setCaseStatus(caseStatus);
			c801m01a.setCoCustName(coCustName);
			//例如：當額度序號 025410600149 有82個從債務人
			if (Util.notEquals(
					Util.truncateString(guarantor, MAXLEN_C801M01A_GUARANTOR),
					guarantor)) {
				
				if(clsService.is_function_on_codetype("c801m01a_guarantor_semi_char")){
					//把英文全型轉半型
					guarantor = Util.toSemiCharString(guarantor);	
				}
				
				if (Util.notEquals(
						Util.truncateString(guarantor, MAXLEN_C801M01A_GUARANTOR),
						guarantor)) {
					//若仍不足夠
					logger.error("C801M01A.GUARANTOR exceed columnLength["+guarantor+"]");
					throw new CapMessageException(cntrNo+"之保證人姓名過長", getClass());
				}
			}else{
				//未超出DB長度
			}			
			c801m01a.setGuarantor(guarantor);
			c801m01a.setChairmanName(chairmanName);
			cls8011Service.setStatusEdit(c801m01a);
			// ---
			cls8011Service.saveData(c801m01a,
					cls8011Service.genLatest(c801m01a.getMainId()));
		}

		// ---
		CapAjaxFormResult result = new CapAjaxFormResult();
		return result;
	}

	private String[] _mergeData(List<Map<String, Object>> list) {
		String[] arr = new String[9];
		String custId = "";
		String dupNo = "";
		String custName = "";
		String loanNo = "";
		String mName = "";
		String caseStatus = "";
		String coCustName = "";
		String guarantor = "";
		String chairmanName = "";
		if (CollectionUtils.isNotEmpty(list)) {
			// 處理同一 cntrNo
			if (true) {
				Map<String, Object> firstRow = list.get(0);
				String _LNF030_CUST_ID = Util.trim(MapUtils.getString(firstRow,
						"LNF030_CUST_ID"));
				// ---
				custId = StringUtils.substring(_LNF030_CUST_ID, 0, 10);
				dupNo = StringUtils.substring(_LNF030_CUST_ID, 10);
				custName = Util.trim(MapUtils.getString(firstRow, "CNAME"));
				mName = Util.trim(MapUtils.getString(firstRow, "MATENM"));
				chairmanName = Util.trim(MapUtils.getString(firstRow,
						"CHAIRMANNAME"));
			}

			// 處理 loanNo
			if (true) {
				TreeSet<String> tm = new TreeSet<String>();
				for (Map<String, Object> map : list) {
					tm.add(Util.trim(MapUtils.getString(map, "LNF030_LOAN_NO")));
				}
				loanNo = StringUtils.join(tm, "、");
			}

			// 處理 caseStatus
			if (true) {
				TreeSet<String> tm = new TreeSet<String>();
				for (Map<String, Object> map : list) {
					tm.add(Util.trim(MapUtils.getString(map, "LNF030_STATUS")));
				}
				// 抓最大的 status
				for (String s : tm) {
					caseStatus = s;
				}
			}
			// 處理 coCustName, guarantor
			if (true) {
				Map<String, String> mis_LngeFlagMap = new HashMap<String, String>();
				mis_LngeFlagMap.put("G", "連保");
				mis_LngeFlagMap.put("N", "一般");
				mis_LngeFlagMap.put("L", "連帶債務");
				mis_LngeFlagMap.put("E", "票據債務");
				mis_LngeFlagMap.put("S", "擔保物提供");

				Set<String> _CoBorrower = new HashSet<String>();
				Set<String> _Guarantor = new HashSet<String>();
				for (Map<String, Object> map : list) {
					String lngeFlag = Util.trim(MapUtils.getString(map,
							"LNGEFLAG"));
					String lngeNm = Util
							.trim(MapUtils.getString(map, "LNGENM"));
					if (Util.isEmpty(lngeFlag)) {
						// left outer join 可能無 從債務人
						continue;
					}

					if (Util.equals(UtilConstants.lngeFlag.共同借款人, lngeFlag)) {
						_CoBorrower.add(lngeNm);
					} else {

						String val = lngeNm;
						if (mis_LngeFlagMap.containsKey(lngeFlag)) {
							val += ("(" + mis_LngeFlagMap.get(lngeFlag) + ")");
						} else {
							val += ("(" + lngeFlag + ")");
						}
						_Guarantor.add(val);
					}
				}
				coCustName = StringUtils.join(_CoBorrower, "、");
				guarantor = StringUtils.join(_Guarantor, "、");
			}

		}
		arr[0] = custId;
		arr[1] = dupNo;
		arr[2] = custName;
		arr[3] = loanNo;
		arr[4] = mName;
		arr[5] = caseStatus;
		arr[6] = coCustName;
		arr[7] = guarantor;
		arr[8] = chairmanName;
		return arr;
	}

	private CapAjaxFormResult defaultResult(PageParameters params,
			C801M01A meta, CapAjaxFormResult result) throws CapException {
		result.set(EloanConstants.PAGE,
				Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));
		result.set("custInfo",
				Util.trim(meta.getCustId()) + " " + Util.trim(meta.getDupNo())
						+ " " + Util.trim(meta.getCustName()));
		return result;
	}

	/**
	 * 查詢C801M01A 個金個人資料清冊
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C801M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = cls8011Service.findC801M01A_oid(mainOid);

			String page = params.getString(EloanConstants.PAGE);
			if ("01".equals(page)) {
				LMSUtil.addMetaToResult(result, meta, new String[] {
						"custName", "custId", "dupNo", "mName", "chairmanName",
						"caseStatus", "coCustName", "guarantor", "cntrNo",
						"loanNo", "caseNo", "storageLocation", "caseDate",
						"caseApprId", "caseRecheckId", "finalDate",
						"caseBossId", "custodianId", "destroyDate",
						"caseApproveTime", "caseCancelDate","docType" });

				Map<String, String> boss_list = userInfoService
						.findByBrnoAndSignId(meta.getOwnBrId(), new SignEnum[] {
								SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管 });

				Map<String, String> staff_list = userInfoService
						.findByBrnoAndSignId(meta.getOwnBrId(), new SignEnum[] {
								SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
								SignEnum.乙級主管, SignEnum.經辦人員 });

				result.set("boss_list", new CapAjaxFormResult(boss_list));
				result.set("staff_list", new CapAjaxFormResult(staff_list));

				if (true) {
					// 處理轉進來的資料，有名字，但沒有 ID
					// 用 StringUtils.isBlank 讓 ' ' 也判斷成 true
					if (StringUtils.isNotBlank(meta.getCaseBoss())
							&& StringUtils.isBlank(meta.getCaseBossId())) {
						String id = _get_id_by_name(staff_list,
								meta.getCaseBoss());
						meta.setCaseBossId(Util.isNotEmpty(id) ? id : "_");
						result.set("caseBossId", meta.getCaseBossId());
					}
					if (StringUtils.isNotBlank(meta.getCaseAppr())
							&& StringUtils.isBlank(meta.getCaseApprId())) {
						String id = _get_id_by_name(staff_list,
								meta.getCaseAppr());
						meta.setCaseApprId(Util.isNotEmpty(id) ? id : "_");
						result.set("caseApprId", meta.getCaseApprId());
					}
					if (StringUtils.isNotBlank(meta.getCaseRecheck())
							&& StringUtils.isBlank(meta.getCaseRecheckId())) {
						String id = _get_id_by_name(staff_list,
								meta.getCaseRecheck());
						meta.setCaseRecheckId(Util.isNotEmpty(id) ? id : "_");
						result.set("caseRecheckId", meta.getCaseRecheckId());
					}
					if (StringUtils.isNotBlank(meta.getCustodian())
							&& StringUtils.isBlank(meta.getCustodianId())) {
						String id = _get_id_by_name(staff_list,
								meta.getCustodian());
						meta.setCustodianId(Util.isNotEmpty(id) ? id : "_");
						result.set("custodianId", meta.getCustodianId());
					}
				}

				if (true) {
					if (Util.isNotEmpty(meta.getCaseBossId())
							&& !boss_list.containsKey(meta.getCaseBossId())) {
						Map<String, String> m = new HashMap<String, String>();
						m.put(meta.getCaseBossId(),
								Util.isNotEmpty(meta.getCaseBoss()) ? meta
										.getCaseBoss() : userInfoService
										.getUserName(meta.getCaseBossId()));
						result.set("added_caseBossId", new CapAjaxFormResult(m));
					}

					if (Util.isNotEmpty(meta.getCaseApprId())
							&& !staff_list.containsKey(meta.getCaseApprId())) {
						Map<String, String> m = new HashMap<String, String>();
						m.put(meta.getCaseApprId(),
								Util.isNotEmpty(meta.getCaseAppr()) ? meta
										.getCaseAppr() : userInfoService
										.getUserName(meta.getCaseApprId()));
						result.set("added_caseApprId", new CapAjaxFormResult(m));
					}

					if (Util.isNotEmpty(meta.getCaseRecheckId())
							&& !staff_list.containsKey(meta.getCaseRecheckId())) {
						Map<String, String> m = new HashMap<String, String>();
						m.put(meta.getCaseRecheckId(),
								Util.isNotEmpty(meta.getCaseRecheck()) ? meta
										.getCaseRecheck() : userInfoService
										.getUserName(meta.getCaseRecheckId()));
						result.set("added_caseRecheckId",
								new CapAjaxFormResult(m));
					}

					if (Util.isNotEmpty(meta.getCustodianId())
							&& !staff_list.containsKey(meta.getCustodianId())) {
						Map<String, String> m = new HashMap<String, String>();
						m.put(meta.getCustodianId(),
								Util.isNotEmpty(meta.getCustodian()) ? meta
										.getCustodian() : userInfoService
										.getUserName(meta.getCustodianId()));
						result.set("added_CustodianId",
								new CapAjaxFormResult(m));
					}
				}

			} else if ("02".equals(page) || "03".equals(page)
					|| "04".equals(page)) {
				String pageItemType = Util.trim(params
						.getString("pageItemType"));
				List<C801M01B> list = cls8011Service
						.findC801M01B_mainId_itemType(meta.getMainId(),
								pageItemType);
				HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
				_setC801M01B(map, pageItemType, list);
				result.set("c801m01b_list", new CapAjaxFormResult(map));
			}
		}

		return defaultResult(params, meta, result);
	}

	private void _setC801M01B(HashMap<String, JSONArray> map, String itemType,
			List<C801M01B> list) throws CapException {
		JSONArray jsonAraay = new JSONArray();

		// ---
		for (C801M01B c801m01b : list) {
			JSONObject o = new JSONObject();
			o.putAll(DataParse.toJSON(c801m01b, true));
			o.put("isSelfAdd", cls8011Service.isSelfAddItem(c801m01b) ? "Y"
					: "N");
			jsonAraay.add(o);
		}
		map.put(itemType, jsonAraay);
	}

	private String _get_id_by_name(Map<String, String> map, String name) {
		for (String k : map.keySet()) {
			String v = map.get(k);
			if (Util.equals(v, name)) {
				return k;
			}
		}
		return "";
	}

	/**
	 * 儲存
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params) throws CapException {
		return _saveAction(params, "N");
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params) throws CapException {
		return _saveAction(params, "Y");
	}

	private void _setUserName(PageParameters params, String[] ids,
			String[] names) {
		int cnt = ids.length;
		Map<String, String> r = new HashMap<String, String>();
		for (int i = 0; i < cnt; i++) {
			r.put(names[i], Util.trim(userInfoService.getUserName(Util
					.trim(params.getString(ids[i])))));
		}
		for (String k : r.keySet()) {
			params.put(k, r.get(k));
		}
	}

	private CapAjaxFormResult _saveAction(PageParameters params,
			String tempSave) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);

		// ===
		String KEY = "saveOkFlag";

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C801M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try {
				meta = cls8011Service.findC801M01A_oid(mainOid);
				String page = params.getString(EloanConstants.PAGE);
				if ("01".equals(page)) {
					_setUserName(params, new String[] { "caseApprId",
							"caseRecheckId", "caseBossId", "custodianId" },
							new String[] { "caseAppr", "caseRecheck",
									"caseBoss", "custodian" });

					CapBeanUtil.map2Bean(params, meta, new String[] {
							"custName", "mName", "chairmanName", "coCustName",
							"guarantor", "loanNo", "caseNo", "storageLocation",
							"caseDate", "caseApprId", "caseRecheckId",
							"finalDate", "caseBossId", "custodianId",
							"destroyDate", "caseApproveTime",
							"caseCancelDate",
							"caseStatus","docType"
							// 由 _setUserName 填入的 userName
							, "caseAppr", "caseRecheck", "caseBoss",
							"custodian" });

				} else if ("02".equals(page) || "03".equals(page)
						|| "04".equals(page)) {
					String pageItemType = Util.trim(params
							.getString("pageItemType"));
					if (Util.isNotEmpty(pageItemType)) {
						List<C801M01B> list = cls8011Service
								.findC801M01B_mainId_itemType(meta.getMainId(),
										pageItemType);
						for (C801M01B c801m01b : list) {
							{// part 1
								String key = "_itemCheck_"
										+ c801m01b.getItemType() + "_"
										+ Util.trim(c801m01b.getItemCode());
								if (!params.containsKey(key)) {
									continue;
								}
								c801m01b.setItemCheck(Util.trim(params
										.getString(key)));
							}
							{// part 2
								String key = "_riskLevel_"
										+ c801m01b.getItemType() + "_"
										+ Util.trim(c801m01b.getItemCode());
								if (!params.containsKey(key)) {
									continue;
								}
								c801m01b.setRiskLevel(Util.trim(params
										.getString(key)));
							}

							if (cls8011Service.isSelfAddItem(c801m01b)) {
								String key = "_itemContent_"
										+ c801m01b.getItemType() + "_"
										+ Util.trim(c801m01b.getItemCode());
								if (!params.containsKey(key)) {
									continue;
								}
								c801m01b.setItemContent(Util.trim(params
										.getString(key)));
							}
							// ---
							cls8011Service.save(c801m01b);
						}
					}
				}

				cls8011Service.save(meta);
				// ===
				result.set(KEY, true);
			} catch (Exception e) {
				logger.error(StrUtils.getStackTrace(e));
				throw new CapMessageException(e, getClass());
			}
		}

		result.add(query(params));
		return result;
	}

	/**
	 * 刪除C801M01A 個金個人資料清冊作業
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteC801m01a(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			if (cls8011Service.deleteC801m01as(oids)) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult to_finish(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			for (String oid : oids) {
				C801M01A model = cls8011Service.findC801M01A_oid(oid);
				cls8011Service.setStatusFinish(model);
				cls8011Service.save(model);
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult to_edit(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			for (String oid : oids) {
				C801M01A model = cls8011Service.findC801M01A_oid(oid);
				cls8011Service.setStatusEdit(model);
				cls8011Service.save(model);
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult impLatestItem(PageParameters params) throws CapException {
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			for (String oid : oids) {
				C801M01A model = cls8011Service.findC801M01A_oid(oid);
				cls8011Service.deleteC801M01B(model.getMainId());
				cls8011Service.saveData(model,
						cls8011Service.genLatest(model.getMainId()));
			}
		}
		return query(params);
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addC801M01B(PageParameters params) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String pageItemType = Util.trim(params.getString("pageItemType"));
		String itemContent = Util.trim(params.getString("itemContent"));
		List<C801M01B> list = new ArrayList<C801M01B>();
		C801M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try {
				meta = cls8011Service.findC801M01A_oid(mainOid);

				C801M01B c801m01b = cls8011Service.selfAddItem(meta,
						pageItemType, itemContent);
				// ---
				list.add(c801m01b);
			} catch (Exception e) {
				logger.error(StrUtils.getStackTrace(e));
				throw new CapMessageException(e, getClass());
			}
		}
		HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
		_setC801M01B(map, pageItemType, list);
		result.set("c801m01b_list", new CapAjaxFormResult(map));

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult delC801M01B(PageParameters params) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		String KEY = "delFlag";
		String prefix = "c801m01b_";

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String c801m01b_oid = Util.trim(params.getString("c801m01b_oid"));
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		C801M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = cls8011Service.findC801M01A_oid(mainOid);

			if (Util.isNotEmpty(c801m01b_oid)
					&& StringUtils.startsWith(c801m01b_oid, prefix)) {
				String oid = StringUtils.substring(c801m01b_oid,
						prefix.length());
				if (Util.isNotEmpty(oid)) {
					C801M01B c801m01b = cls8011Service.findC801M01B_oid(oid);
					if (c801m01b != null) {
						cls8011Service.saveAndAdjustSeq(meta, c801m01b);
						result.set(KEY, true);
					}
				}
			}
		}

		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryEmp(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		Map<String, String> staff_list = userInfoService.findByBrnoAndSignId(user.getUnitNo(), new SignEnum[] {
				SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管, SignEnum.經辦人員 });

		result.set("staff_list", new CapAjaxFormResult(staff_list));
		return result;
	}
}
