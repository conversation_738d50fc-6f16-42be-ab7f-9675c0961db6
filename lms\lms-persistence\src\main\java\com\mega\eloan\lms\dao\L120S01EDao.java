package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S01E;


/** 企金營收獲利財務狀況檔 **/
public interface L120S01EDao extends IGenericDao<L120S01E> {

	L120S01E findByOid(String oid);
	
	List<L120S01E> findByMainId(String mainId);
	
	List<L120S01E> findByKindMainId(String mainId,String finKind);
	
	List<L120S01E> findByKey(String finkind,String mainid,String custid,String dupno);
	
	List<L120S01E> findByKey2(String finkind,String mainid,String custid,String dupno);
	
	L120S01E findByUniqueKey(String mainId,String custId,String dupNo,String finKind,Date finYear,String finItem);
	
	List<L120S01E> findByCustIdDupId(String custId,String DupNo);
	List<Object[]> findMainId(String custId, String dupNo);
	List<Object[]> findRate(String mainId);
	List<Object[]> findL120s01e(String mainId, String custId, String dupNo, String finKind);
	List<Date> findFinYear(String mainId, String custId, String dupNo, String finKind);
	

	int delModel(String mainId);
}