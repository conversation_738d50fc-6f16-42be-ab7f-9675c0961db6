/* 
 * C999S01BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C999S01BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C999S01B;

/** 個金約據書契約內容檔 **/
@Repository
public class C999S01BDaoImpl extends LMSJpaDao<C999S01B, String>
	implements C999S01BDao {

	@Override
	public C999S01B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C999S01B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C999S01B> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C999S01B findByUniqueKey(String mainId, String pid, String type){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "pid", pid);
		search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C999S01B> findByIndex01(String mainId, String pid, String type){
		ISearch search = createSearchTemplete();
		List<C999S01B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (pid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "pid", pid);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}