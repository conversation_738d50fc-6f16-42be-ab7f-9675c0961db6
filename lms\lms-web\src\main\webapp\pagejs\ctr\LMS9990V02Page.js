$(function(){

    /***執行輸入查詢ID視窗***/
    openQuery();
    
    
    $("#buttonPanel").find("#btnSearch").click(function(){
        openQuery();
    });
    
    /***輸入查詢ID視窗***/
    function openQuery(){
        var $filterForm = $("#filterForm");
        //初始化
        $filterForm.reset();
        $("#filterBox").thickbox({ // 使用選取的內容進行彈窗
            //lnunid.title01=請輸入欲查詢婉卻紀錄的客戶統一編號
            title: i18n.lms9990v01['ThickBox.showTitle'],
            width: 500,
            height: 260,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                sure: function(){
                    conFirm();
                },
                cancel: function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    var gridviewCase = $("#gridviewCase").iGrid({
        handler: 'lms9990gridhandler',
        height: 225,
        postData: {
            formAction: ""
        },
        multiselect: false,
        rowNum: 15,
        colModel: [{
            colHeader: i18n.lms9990v01['L120M01A.caseDate'],// "簽報日期",
            name: 'caseDate',
            width: 120,
            align: "center",
            sortable: true,
            formatter: 'click',
            onclick: openDoc			
        }, {
            colHeader: i18n.lms9990v01['L120M01A.caseNo'],// "簽報書案號",
            name: 'caseNo',
            width: 200,
            align: "left",
            sortable: true
        }, {
            colHeader: i18n.lms9990v01['L120M01A.approveTime'],// "核准日期",
            name: 'approveTime',
            width: 140,
            align: "center",
            sortable: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'custId',
            hidden: true
        }, {
            name: 'dupNo',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = gridviewCase.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    //顯示Grid資料(主要借款人簽報書列表)
    function openDoc(cellvalue, options, rowObject){
        var uuid;
        $.ajax({ //載入Form資料(客戶名稱)
            handler: "lms9990m06formhandler",
            data: {
                formAction: "getUUID",
                txCode: txCode
            },
        }).done(function(json){
            uuid = json.uuid;
            ilog.debug(rowObject);
            $.form.submit({
                url: '../ctr/lms9990m06/01',//'../lms/lms1605m01/01',
                data: {
                    formAction: "queryL999m01a",
                    srcMainId: rowObject.mainId,
                    mainId: uuid,
                    custId: rowObject.custId,
                    dupNo: rowObject.dupNo,
                    txCode: txCode
                },
                target: rowObject.mainId
            });
        }).fail(function(responseData){
            API.showErrorMessage(i18n.lms9990v01['Alert.message05']);
        });
    }
    
    var gridViewDupNo = $("#gridviewCaseListDupNo").iGrid({
        handler: 'lms9990gridhandler',
        height: 270,
        rownumbers: true,
        multiselect: false,
        hideMultiselect: false,
        rowNum: 15,
        postData: {
            formAction: "listByNoDupNo"
        },
        colModel: [{
            colHeader: i18n.lms9990v01['L120M01A.custName'],// "姓名",
            name: 'custName',
            width: 40,
            align: "center",
            sortable: true
        }, {
            colHeader: i18n.lms9990v01['L120M01A.custId'],// "身分證字號",
            name: 'custId',
            width: 30,
            align: "center",
            sortable: true
        }, {
            colHeader: i18n.lms9990v01['L120M01A.dupNo'],// "重複序號",
            name: 'dupNo',
            width: 20,
            align: "left",
            sortable: true
        }, {
            colHeader: "typCd",
            name: 'typCd',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = gridViewDupNo.getRowData(rowid);
            showList(null, null, data);
        }
    });
    
    //顯示Grid資料(主要借款人簽報書列表)
    function showList(cellvalue, options, rowObject){
        if ("1" == $("input[name=typCd]:checked").val()) {
            $("#typCdName").val(i18n.lms9990v01['L999M01A.obu']);
        } else if ("2" == $("input[name=typCd]:checked").val()) {
            $("#typCdName").val(i18n.lms9990v01['L999M01A.dbu']);
        }
        $("#custData").val(rowObject.custId + " " + rowObject.dupNo + " " + rowObject.custName);
        filterGrid({
            custId: $("#custId").val(),
            dupNo: rowObject.dupNo,
            typCd: $("input[name=typCd]:checked").val(),
            txCode: txCode
        });
        $("#dupNo").val(rowObject.dupNo);
        $.thickbox.close();
        $.thickbox.close();
    }
    
    //驗證若是重複序號未輸入則跳出grid 
    function conFirm(){
        var checkResult = true;
        if ($("#custId").val().length == 0) {
            //custId= 【注意】請輸入客戶統一編號
            API.showErrorMessage(i18n.lms9990v01['Alert.message01']);
            $("#custId").val('');
            checkResult = false;
        }
        if ($("#custId").val().length != 0 && !($("#custId").val().length == 10 || $("#custId").val().length == 8)) {
            //custId= 【注意】客戶統一編號長度有誤
            API.showErrorMessage(i18n.lms9990v01['Alert.message02']);
            $("#custId").val('');
            checkResult = false;
        }
        
        if (checkResult) {
            if ($("#dupNo").val().length == 0) {
                gridViewDupNo.jqGrid("setGridParam", {
                    postData: {
                        custId: $("#custId").val()
                    },
                    search: true
                }).trigger("reloadGrid");
                
                $("#cntrCaseBoxListDupNo").thickbox({ // 使用選取的內容進行彈窗
                    title: i18n.lms9990v01['ThickBox.showDupNoTitle'],
                    width: 500,
                    height: 400,
                    modal: true,
                    buttons: (function(){
                        var btn = {};
                        btn[i18n.def['close']] = function(){
                            $.thickbox.close();
                        }
                        return btn;
                    })()
                });
            } else {
                $.ajax({ //載入Form資料(客戶名稱)
                    handler: "lms9990m06formhandler",
                    data: {
                        formAction: "getCustData",
                        custId: $("#custId").val(),
                        dupNo: $("#dupNo").val(),
                        typCd: $("input[name=typCd]:checked").val(),
                        txCode: txCode
                    },
                }).done(function(json){
                     $("#typCdName").val(json.CustDataForm.typCdName);
                        $("#custData").val(json.CustDataForm.custId + " " + json.CustDataForm.dupNo + " " + json.CustDataForm.custName);
                        if (json.CustDataForm.custName != "") {
                            filterGrid({
                                custId: $("#custId").val(),
                                dupNo: $("#dupNo").val(),
                                typCd: $("input[name=typCd]:checked").val(),
                                txCode: txCode
                            });
                            $.thickbox.close();
                        } else {
                            API.showErrorMessage(i18n.lms9990v01['Alert.message04']);
                        }
                }).fail(function(responseData){

                });
            }
        }
    }
    
    //grid資料篩選
    function filterGrid(sendData){
        $("#gridviewCase").jqGrid("setGridParam", {
            postData: $.extend({
                formAction: "queryReportList",
                custId: $("#custId").val(),
                dupNo: $("#dupNo").val(),
				docType : "2",
                typCd: $("input[name=typCd]:checked").val(),
                txCode: txCode
            }, sendData || {}),
            search: true
        }).trigger("reloadGrid");
        //		 //或是以下方法
        //		 $("#gridviewCase").jqGrid("setGridParam", {
        //			 postData : $.extend({
        //				formAction:"queryReportList"
        //			},sendData || {}), 
        //			search: true
        //		}).trigger("reloadGrid");
        //		 //或是以下方法
        //		 $("#gridviewCase").jqGrid("setGridParam", {
        //			 postData {
        //				formAction:"queryReportList",
        //				custId : $("#custId").val(),
        //				dupNo : $("#dupNo").val(),
        //				typCd : $("input[name=typCd]:checked").val()
        //			}, 
        //			search: true
        //		}).trigger("reloadGrid");
    }
    
});
