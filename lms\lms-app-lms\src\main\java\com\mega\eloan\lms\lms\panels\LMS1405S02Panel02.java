/* 
 * LMS1405S02Panel02.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.panels;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.model.L120M01A;

/**
 * <pre>
 * 額度明細表 - 申請內容
 * </pre>
 * 
 * @since 2011/10/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/5,REX,new
 *          </ul>
 */
public class LMS1405S02Panel02 extends Panel {

	@Autowired
	LMSService lmsService;

	private L120M01A l120m01a;

	public LMS1405S02Panel02(String id, L120M01A l120m01a) {
		super(id);
		this.l120m01a = l120m01a;

		// J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
		// Map<String, String> lgdMap = lmsService.getLgdTotAmtParam(null,
		// null);
		//
		// // J-111-0343_05097_B1003 Web e-Loan修改企金額度明細表合計之功能
		// // T=授信授權額度合計
		// String label_lgdTotAmt_T = MapUtils.getString(lgdMap, "label_T");
		// add(new Label("label_lgdTotAmt_T", label_lgdTotAmt_T));

		// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		// int lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
		// "lmsLgdCount", "0"));
		// int lmsLgdCountTotal = Util.parseInt(MapUtils.getString(lgdMap,
		// "lmsLgdCountTotal", "0"));
		//
		// for (int i = 1; i <= lmsLgdCountTotal; i++) {
		// String label_lgdTotAmt = MapUtils.getString(lgdMap, "label_" + i,
		// "");
		// add(new Label("label_lgdTotAmt_" + i, label_lgdTotAmt));
		// String label_lgdTotAmt_U_1 = MapUtils.getString(lgdMap, "label_1_"
		// + i, "");
		// add(new Label("label_lgdTotAmt_" + i + "_1", label_lgdTotAmt_U_1));
		// }

		// U=LGD其中擬制無擔保合計
		// P=LGD其中擬制部分擔保合計
		// S=LGD其中擬制十足擔保合計
		// T=授信授權額度合計
		// String label_lgdTotAmt_U = MapUtils.getString(lgdMap, "label_U");
		// String label_lgdTotAmt_P = MapUtils.getString(lgdMap, "label_P");
		// String label_lgdTotAmt_S = MapUtils.getString(lgdMap, "label_S");
		//
		// add(new Label("label_lgdTotAmt_U", label_lgdTotAmt_U));
		// add(new Label("label_lgdTotAmt_P", label_lgdTotAmt_P));
		// add(new Label("label_lgdTotAmt_S", label_lgdTotAmt_S));
		// add(new Label("label_lgdTotAmt_T", label_lgdTotAmt_T));
		//
		// //
		// 「其中擬制無擔保(LGD>=50%)合計」、「其中擬制部分擔保(10%<LGD<50%)合計」、「其中擬制十足擔保(LGD<=10%)合計」
		// String label_lgdTotAmt_U_1 = MapUtils.getString(lgdMap, "label_1_U");
		// String label_lgdTotAmt_P_1 = MapUtils.getString(lgdMap, "label_1_P");
		// String label_lgdTotAmt_S_1 = MapUtils.getString(lgdMap, "label_1_S");
		//
		// add(new Label("label_lgdTotAmt_U_1", label_lgdTotAmt_U_1));
		// add(new Label("label_lgdTotAmt_P_1", label_lgdTotAmt_P_1));
		// add(new Label("label_lgdTotAmt_S_1", label_lgdTotAmt_S_1));

	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		boolean fieldRating_new = false;
		if (OverSeaUtil.isCaseDoc_CLS_RatingFlag_ON(l120m01a)) {
			fieldRating_new = true;
		}
		model.addAttribute("_panel_fieldRating_old1", !fieldRating_new);
		model.addAttribute("_panel_fieldRating_old2", !fieldRating_new);
		model.addAttribute("_panel_fieldRating_old3", !fieldRating_new);

		model.addAttribute("_panel_fieldRating_new1", fieldRating_new);
		model.addAttribute("_panel_fieldRating_new2", fieldRating_new);
		model.addAttribute("_panel_fieldRating_new3", fieldRating_new);
	}

	/**/
	private static final long serialVersionUID = 1L;

}
