package com.mega.eloan.lms.base.service;

import java.util.List;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L180R60A;

import tw.com.iisi.cap.exception.CapException;

/**
 * <pre>
 * 模擬動審
 * </pre>
 * 
 * @since 2017/07/01
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/07/01,007625,new
 *          </ul>
 */
public interface LMS2105V01Service extends AbstractService {

	/**
	 * J-109-0519_05097_B1001 Web e-Loan產生央行C方案借款人，且兌付振興三倍券達888張之名單
	 * 
	 * @return
	 */
	public List<L180R60A> findL180r60aAll();

	/**
	 * J-109-0519_05097_B1001 Web e-Loan產生央行C方案借款人，且兌付振興三倍券達888張之名單
	 * 
	 * @param list
	 */
	public void deleteListL180r60a(List<L180R60A> list);

	/**
	 * J-109-0519_05097_B1001 Web e-Loan產生央行C方案借款人，且兌付振興三倍券達888張之名單
	 * 
	 * @param list
	 */
	public void saveListL180r60a(List<L180R60A> list);

	/**
	 * J-110-0304_05097_B1003 Web e-Loan授信覆審配合RPA作業修改
	 * 
	 * @param params
	 * @param uFile
	 * @return
	 */
	String rpaUpdateLmsReCheckReport(PageParameters params, MultipartFile uFile);

	/**
	 * J-111-0423_05097_B1001 Web
	 * e-Loan企金授信就海外分行承做永續績效連結授信案(如附件)，於E-Loan「永續績效連結授信」相關註記
	 * 
	 * @param listData
	 * @return
	 */
	public String processEsgFile(List<Map<String, String>> dataList);
	
	/**
	 * J-112-0366_12473_B1001 Web e-Loan 因應本行申請辦理中小信保基金批次保證業務，修改本行E-loan管理系統相關欄位
	 * @return 
	 * 
	 */
	public List<DocFile> processBatGutFile(List<Map<String, Object>> dataList, String mainId, String ssoUnitNo);
	
	/**
	 * J-112-0366_12473_B1001 Web e-Loan 因應本行申請辦理中小信保基金批次保證業務，修改本行E-loan管理系統相關欄位
	 * @throws CapException 
	 * 
	 */
	public void sendToFTP(List<DocFile> files, String mainId);
}
