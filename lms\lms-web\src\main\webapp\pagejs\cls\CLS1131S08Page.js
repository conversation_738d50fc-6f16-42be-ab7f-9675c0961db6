/*
先在 CLS1131S08Page.java 中指定 js , 該 Java 掛在 path = "/cls/cls1131s08"

然後在 
	●CLS1131S01Page.js 個金徵信
	●
	
	程式碼區塊 
		$('#adjustCardLoanSheet').load(webroot + '/app/cls/cls1131s08', function(){
	去載入頁面
*/
var AdjustCardLoanAction = {
	//有可能 handler 為 cls1161m02formhandler
	handler : 'cls1131formhandler',
	data : {},
	ready : false,
	adjust : false,
	manual : false,
	/**
	 * 初始化
	 */
	init : function() {
		$('#adjustCardLoanForm').setValue(); // reset
		AdjustCardLoanAction.adjust = false;
		AdjustCardLoanAction.manual = false;
	},
	/**
	 * 建置
	 */
	build : function() {
		var $form = $('#adjustCardLoanForm');
		$form.find('input[name=adjustStatus]').click(function() {
			var $fm = $('#adjustCardLoanForm');
			$fm.find('#adjustCardLoanFlagDiv').hide();
			$fm.find('#adjustCardLoanFlagSpan').html('理由為');
			$fm.find('#grade2').val('');
			$fm.find('#grade2').addClass('required').attr('disabled', false);

			//手動時清除資料
			if (AdjustCardLoanAction.manual){
				//$form.serializeData();
				$fm.find('input[name=adjustFlag]').attr('checked', false);
				$fm.find('#adjustReason').val('');
			}

			//addClass required add by fantasy 2013/07/09
			if (!$fm.find('#adjustReason').hasClass('required'))
				$fm.find('#adjustReason').addClass('required');
			
			switch ($(this).val()+''){
				case '1': //升等
					$fm.find('#adjustCardLoanFlagDiv').show();
					$fm.find('#grade2').show();
					$fm.find('#adjustCardLoanFlagSpan').html('勾選升等主要理由，並詳敘升等理由(如相關佐證等)');
					break;
				case '2': //降等
					break;
				case '3': //回復
					$fm.find('#grade2').removeClass('required').attr('disabled', true).val('');
					$fm.find('#adjustReason').removeClass('required');
					break;
			}

			AdjustCardLoanAction.compute();
		});
		$form.find('input[name=adjustFlag]').click(function() {
				switch ($(this).val() + '') {
				case '1': // 淨資產
					$('#adjustCardLoanFlagSpan1').html('淨資產');
					$('#adjustCardLoanFlagSpan2').html(
							'(請以具體數據文字敘明，如年(月)所得金額、存款金額、租金收入金額、其他資產多寡或負債百分比等)');
					break;
				case '2': // 職業
					$('#adjustCardLoanFlagSpan1').html('職業');
					$('#adjustCardLoanFlagSpan2')
							.html('(請詳述，如任職公司職稱、規模、職業類別、年資、擔任公司負責人或工作前景等)');
					break;
				case '3': // 其它
					$('#adjustCardLoanFlagSpan1').html('其它');
					$('#adjustCardLoanFlagSpan2').html('(如與銀行的往來關係、對客戶之綜合評述或非屬前兩類之理由等)');
					break;
				}
				
			  //手動時清除資料
				if (AdjustCardLoanAction.manual){
					$('#adjustCardLoanForm').find('#adjustReason').val('');
				}
		});
		$form.find('#grade2').change(function() {
			AdjustCardLoanAction.compute();
		});
		return true;
	},
	/**
	 * 開啟
	 */
	open : function(data) {
		if (!AdjustCardLoanAction.ready)
			AdjustCardLoanAction.ready = AdjustCardLoanAction.build();
		// 初始化
		AdjustCardLoanAction.init();
		// set data
		AdjustCardLoanAction.data = $.extend(data || {}, {
			noOpenDoc : true,
			'markModel':'3'
		});
		// load data
		AdjustCardLoanAction.load();
	},
	openThinkBox : function() {
		$('#adjustCardLoanThickBox').thickbox({
			title : '調整非房貸申請信用評等:專案信貸(非團體)',
			width : 800,
			height : 540,
			modal : true,
			align : 'center',
			valign : 'bottom',
			i18n : i18n.def,
			buttons : {
				'sure' : function() {
					if ($('#adjustCardLoanForm').valid()) {
						AdjustCardLoanAction.save();
					}
				},
				'cancel' : function() {
					$.thickbox.close();
				}
			}
		});
	},
	openReadOnlyThinkBox : function() {
		$('#adjustCardLoanForm').readOnlyChilds();
		
		$('#adjustCardLoanThickBox').thickbox({
			title : '調整非房貸申請信用評等:專案信貸(非團體)',
			width : 800,
			height : 540,
			modal : true,
			align : 'center',
			valign : 'bottom',
			i18n : i18n.def,
			buttons : {
				'cancel' : function() {
					$.thickbox.close();
				}
			}
		});
	},
	/**
	 * 讀取資料
	 */
	load : function() {
		if (!$.isEmptyObject(AdjustCardLoanAction.data)) {
			$.ajax({
				handler : AdjustCardLoanAction.handler,
				action : 'loadAdjust',
				formId : 'empty', //
				data : AdjustCardLoanAction.data,
				success : function(response) {
					if (response.adjustMsg) {
						// showErrorMessage showPopMessage
						MegaApi.showErrorMessage(i18n.def["confirmTitle"],
								response.adjustMsg);
					} else {
						AdjustCardLoanAction.adjust = (response.adjust ? true : false);
						$('#adjustCardLoanForm').setValue(response.adjustCardLoanForm);

						if(DOMPurify.sanitize(response.fromC120M01A)=="Y"){
							$(".area_cls1131s08page_c101").hide();
							$(".area_cls1131s08page_c120").show();
							AdjustCardLoanAction.openReadOnlyThinkBox();
						}else{
							$(".area_cls1131s08page_c101").show();
							$(".area_cls1131s08page_c120").hide();
							if(response.lockAdjust=="Y"){
								AdjustCardLoanAction.openReadOnlyThinkBox();	
							}else{
								AdjustCardLoanAction.openThinkBox();	
							}							
						}
					}
					AdjustCardLoanAction.manual = true;
				}
			});
		}
	},
	/**
	 * 儲存
	 */
	save : function() {
		if (!$.isEmptyObject(AdjustCardLoanAction.data) && AdjustCardLoanAction.checkGrade()) {
			$.ajax({handler : "lms1015m01formhandler",action : 'validateAdjustReason',				
				data : {'keyStr':'', 'mowType': 'N', 'adjustReason': $("#adjustCardLoanForm").find("#adjustReason").val()},
				success : function(json) {
					procCfmMsg(json.adjRsnFmt_cfmObj).done(function(){
		        		alwaysConfirmAdjReason(json.adjRsnFmt_cnt
		        				, json.adjRsnFmt_alwaysCfmObj).done(function(){
		        			
	        					$.ajax({
	        						handler : AdjustCardLoanAction.handler,
	        						action : 'saveAdjust',
	        						formId : 'adjustCardLoanForm', //
	        						data : AdjustCardLoanAction.data,
	        						success : function(response) {
	        							$('#gradeDiv_markModel_3').setValue(response.gradeDiv_markModel_3);
	        							$.thickbox.close();
	        							MegaApi.showPopMessage(i18n.def["confirmTitle"],
	        									i18n.def["runSuccess"]);
	        						
	        							if(true){
	        								//若在 中鋼整批 去升降等，也要 reloadGrid
	        								if(AdjustCardLoanAction.data.useC120Batch=='Y'){
	        									ilog.debug("refresh grid");
	        									$("#gridview").trigger("reloadGrid");	
	        								}							
	        							}	
	        						}
	        					});
		        		});
		        	});
				}
			});
		}
	},
	/**
	 * 計算最終評等
	 */
	compute : function() {
		var $form = $('#adjustCardLoanForm');
		var value = parseInt($form.find('#sprtRating').html() || '0'); //專案信貸(非團體)有「支援評等」
		$form.find('input[name=adjustStatus]:checked').each(function() {
			switch ($(this).val() + '') {
			case '1': // 調升
				value -= parseInt($form.find('#grade2').val() || '0');
				break;
			case '2': // 調降
				value += parseInt($form.find('#grade2').val() || '0');
				break;
			}
		});
		$form.find('#grade3').html(value);
		//AdjustCardLoanAction.checkGrade();
	},
	/**
	 * 最終評等檢核
	 */
	checkGrade : function(){
		var $form = $('#adjustCardLoanForm');
		var grade_base = parseInt($form.find('#sprtRating').html() || '0'); //專案信貸(非團體) 有「支援評等」
		var grade3 = parseInt($form.find('#grade3').html() || '0');
		if (grade3 >= 11 || grade3 <= 0) {
			MegaApi.showErrorMessage(i18n.def['confirmTitle'],'最終評等應介於1~10等');
			return false;
		}
		var adjustStatus = $form.find('input[name=adjustStatus]:checked').val()+'';
		if (adjustStatus === '1'){
			var grade = grade_base - grade3;
			var v = AdjustCardLoanAction.adjust ? 3 : 2;
			if (grade > v){
				MegaApi.showErrorMessage(i18n.def['confirmTitle'],'調升不可超過'+v+'等');
				return false;
			}
		}
		return true;
	}
}