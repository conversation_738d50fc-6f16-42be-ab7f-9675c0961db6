/* 
 * L120S21CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S21CDao;
import com.mega.eloan.lms.model.L120S21C;

/** LGD額度擔保品檔 **/
@Repository
public class L120S21CDaoImpl extends LMSJpaDao<L120S21C, String> implements
		L120S21CDao {

	@Override
	public L120S21C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S21C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		search.addOrderBy("colKind_s21c",false);
		search.addOrderBy("cmsGrtrt_s21c",true);
		search.addOrderBy("updateTime",false);
		
		List<L120S21C> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S21C> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L120S21C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		search.addOrderBy("colKind_s21c",false);
		search.addOrderBy("cmsGrtrt_s21c",true);
		search.addOrderBy("updateTime",false);
		
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S21C> findByIndex02(String mainId, String cntrNo_s21c) {
		ISearch search = createSearchTemplete();
		List<L120S21C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (cntrNo_s21c != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo_s21c",
					cntrNo_s21c);
		
		search.setMaxResults(Integer.MAX_VALUE);
		search.addOrderBy("colKind_s21c",false);
		search.addOrderBy("cmsGrtrt_s21c",true);
		search.addOrderBy("updateTime",false);
		
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		
		return list;
	}

	@Override
	public List<L120S21C> findByIndex03(String mainId, String cntrNo_s21c,
			String collType_s21c) {
		ISearch search = createSearchTemplete();
		List<L120S21C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (cntrNo_s21c != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo_s21c",
					cntrNo_s21c);
		if (collType_s21c != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "collType_s21c",
					collType_s21c);
		
		search.setMaxResults(Integer.MAX_VALUE);
		search.addOrderBy("colKind_s21c",false);
		search.addOrderBy("cmsGrtrt_s21c",true);
		search.addOrderBy("updateTime",false);
		
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S21C> findByColKind(String mainId, String cntrNo_s21c,
			String colKind_s21c) {
		ISearch search = createSearchTemplete();
		List<L120S21C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (cntrNo_s21c != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo_s21c",
					cntrNo_s21c);
		if (colKind_s21c != null)
			search.addSearchModeParameters(SearchMode.LIKE, "colKind_s21c",
					colKind_s21c + "%");

		
		search.addOrderBy("colKind_s21c",false);
		search.addOrderBy("cmsGrtrt_s21c",true);
		search.addOrderBy("updateTime",false);
		
		
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

}