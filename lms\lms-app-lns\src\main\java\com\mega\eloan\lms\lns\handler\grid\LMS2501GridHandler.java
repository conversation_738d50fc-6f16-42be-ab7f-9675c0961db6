package com.mega.eloan.lms.lns.handler.grid;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMS2501Service;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.model.L250M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;


@Scope("request")
@Controller("lms2501gridhandler")
public class LMS2501GridHandler extends AbstractGridHandler {

	@Resource
	LMS2501Service lms2501Service;

	@Resource
	LMSService lmsService;
	@Resource
	UserInfoService userInfoService;

	@Resource
	CodeTypeService codeTypeService;

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	public CapGridResult queryL250M01A(ISearch pageSetting,	PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l250a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"rptId", "LMS");
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		// 建立主要Search 條件

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);
		
		if(params.containsKey("custId")){
			String custId = Util.nullToSpace(params.getString("custId"));
			String dupNo = Util.nullToSpace(params.getString("dupNo"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
					dupNo);
		}
		
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("apprId", new UserNameFormatter(userInfoService));

		// 第三個參數為formatting
		Page<? extends GenericBean> page = lms2501Service.findPage(
				L250M01A.class, pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow(), formatter);
	}
	
	//J-108-0217_10702_B1001 模擬動審新增IVR語音系統選取視窗
	public CapMapGridResult queryIVRFiltergird(ISearch pageSetting, PageParameters params) throws CapException {
		
		String oid=params.getString(EloanConstants.MAIN_OID);
		List<Map<String, Object>> list = lms2501Service.getIVRFiltergrid(oid);
		
		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				list, list.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());
	}
	
	//J-108-0217_10702_B1001 模擬動審IVR語音系統查詢
	public CapMapGridResult queryIVRgrid(ISearch pageSetting, PageParameters params) throws CapException {

		String oid=params.getString(EloanConstants.MAIN_OID);
		List<Map<String, Object>> list = lms2501Service.getIVRgrid(oid);
		
		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				list, list.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());
	}
}
