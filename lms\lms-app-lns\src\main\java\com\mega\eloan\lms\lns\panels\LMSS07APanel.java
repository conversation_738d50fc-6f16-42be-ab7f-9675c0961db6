package com.mega.eloan.lms.lns.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**<pre>
 * 綜合評估/往來彙總(企金授權外)
 * </pre>
 * @since  2012/1/19
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class LMSS07APanel extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;
	
	public LMSS07APanel(String id) {
		super(id);
	}
	
	public LMSS07APanel(String id, boolean updatePanelName) {
		super(id, updatePanelName);

	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		new LMSS07A_Panel("lmss07_panel").processPanelData(model, params);
	}
}
