package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisDailyctlService;

@Service
public class MisDailyctlServiceImpl extends AbstractMFAloanJdbc implements
		MisDailyctlService {

	@Override
	public String getLatest_aprdcdate(){
		Map<String, Object>  dateRow = this.getJdbc().queryForMap(
				"MISDAILYCTL.selFirstByAprdcdateDescAdatadateDesc", null);
		
		//此欄位是民國年的格式. EX: 102/03/18
		return Util.trim(MapUtils.getString(dateRow, "APRDCDATE"));
		
	}
	
	@Override
	public Map<String, Object> selByBrnoLoanNoPrdcdate(String brNo, String loanNo, String aprdcdate){
		return this.getJdbc().queryForMap(
					"MISDAILYCTL.selByBrnoLoanNoPrdcdate"
				,new String[] {brNo, loanNo, aprdcdate});
		
	}	
}
