/* 
 *  LMS7820GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.grid;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.lms.service.LMS7840Service;

/**
 * <pre>
 * 簽報案件查詢
 * </pre>
 * 
 * @since 2011/12/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/12,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7840gridhandler")
public class LMS7840GridHandler extends AbstractGridHandler {

	@Resource
	LMS7840Service lms7840Service;

}
