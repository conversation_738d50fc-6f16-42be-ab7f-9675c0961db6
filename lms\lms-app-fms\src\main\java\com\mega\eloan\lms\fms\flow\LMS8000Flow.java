
package com.mega.eloan.lms.fms.flow;

import org.springframework.stereotype.Component;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.L260M01A;

import tw.com.jcs.flow.FlowInstance;


/**
 * <pre>
 * 貸後管理作業流程
 *
 * </pre>
 * 
 * @since 2020
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Component
public class LMS8000Flow extends AbstractFlowHandler {

	@Transition(node = "待覆核")
	public void test3(FlowInstance instance) {
	
	}
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return L260M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}
}