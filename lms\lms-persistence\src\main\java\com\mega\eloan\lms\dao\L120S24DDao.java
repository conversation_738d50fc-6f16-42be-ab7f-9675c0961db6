/* 
 * L120S24DDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S24D;

/** LTV風險權數計算對照表 **/
public interface L120S24DDao extends IGenericDao<L120S24D> {

	L120S24D findByOid(String oid);
	
	/**
	 * 多欄位查詢
	 * @param isCBControl
	 * @param LTVClass
	 * @param LTVType
	 * @return
	 */
	List<L120S24D> findByCBControlAndLTVClassAndLTVType(String isCBControl, String LTVClass, String LTVType);
	
	/**
	 * 撈央行管制的資料，只需要給分類即可得出RW
	 * @param LTVClass
	 * @return
	 */
	L120S24D findIsCBControlYByAndLTVClass(String LTVClass);
}