package com.mega.eloan.lms.batch.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.gwclient.AUDITFTPClient;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.service.CLS9071Service;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.StringChecker;
import tw.com.jcs.common.Util;

/**
 * J-111-0365 新增按月提供近一年(上年度1月至今年度最近月份)分行敘做「消金整批貸款清單」月報予稽核處
 * TEST 送稽核處AUDITFTP : 
 * http://localhost:9081/lms-web/app/scheduler?input={"serviceId":"ClsRptFtpAuditServiceImpl","request":{"PARM_MONTH":""}}
 * http://localhost:9081/lms-web/app/scheduler?input={"serviceId":"ClsRptFtpAuditServiceImpl","request":{"PARM_MONTH":"2024-03"}}
 * http://localhost:9081/lms-web/app/scheduler?input={"serviceId":"ClsRptFtpAuditServiceImpl","request":{"PARM_MONTH":"2024-03","SEND_AUDITFTP":"N"}}
 */
@Service("ClsRptFtpAuditServiceImpl")
public class ClsRptFtpAuditServiceImpl extends AbstractCapService implements
		WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource
    AUDITFTPClient auditFtpClient;
	
	@Resource
    SysParameterService sysParameterService;
	
	@Resource
	EloandbBASEService eloandbService;

	@Resource
	CLS9071Service cls9071Service;

	@Resource
	BranchService branchService;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		JSONObject result = new JSONObject();
		JSONObject request = json.getJSONObject("request");
		String dateYM = null;
		String SEND_AUDITFTP = null;
		if (!request.isNullObject()) {
            dateYM = request.optString("PARM_MONTH");
            SEND_AUDITFTP = request.optString("SEND_AUDITFTP");
		}
		if (StringUtils.isBlank(dateYM)) {
            dateYM = CapDate.formatDate(CapDate.addMonth(new Date(), -1), "yyyy-MM");
        }
		
		if (StringUtils.isBlank(SEND_AUDITFTP)) {
			SEND_AUDITFTP = "Y";
		}

		//上一年度1月
		String dateYMBegin = CapDate.formatDate(CapDate.addYears(new Date(), -1), "yyyy") + "-01";
		
		String errMsg = this.doBatch(dateYMBegin, dateYM, SEND_AUDITFTP);
		
		if (Util.notEquals(errMsg, "")) {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RESPONSE,
					"ClsRptFtpAuditServiceImpl執行失敗！==>" + errMsg);
			return result;
		} else {
			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE,
					"ClsRptFtpAuditServiceImpl執行成功！");
		}

		return result;
	}

	@NonTransactional
	public String doBatch(String dateYMBegin, String dateYM, String SEND_AUDITFTP) {
		String errMsg = "";
		try {
			String docFileBaseDir = sysParameterService.getParamValue(SysParamConstants.SYS_DOCFILE_ROOT_DIR);
            if (!docFileBaseDir.endsWith(File.separator)) {
                docFileBaseDir = docFileBaseDir + File.separator;
            }
            // 預設LMS/TEMP/
            docFileBaseDir = docFileBaseDir + "LMS" + File.separator 
            					+ "TEMP" + File.separator 
            					+ "RPTSEVER" + File.separator;

            File folder = new File(StringChecker.checkPathString(docFileBaseDir, true));
            if (!folder.exists()) {
                folder.setReadable(true, true);
                folder.setWritable(true, true);
                folder.setExecutable(true, true);

                folder.mkdirs();
            }
            
            //產生XLS
			ByteArrayOutputStream outputStream = generateXls(false  //includeOnTheWay false 只抓簽案已核准
					,"", "", "" 
					,"4", dateYM , dateYMBegin , dateYM);
			
			OutputStream fos = new FileOutputStream(new File(StringChecker.checkPathString(
					docFileBaseDir + dateYM + "_clsGroup_906P.xls", true)));
			try {
				outputStream.writeTo(fos);
			} catch (IOException ioe) {
                // Handle exception here
                ExceptionUtils.getStackTrace(ioe);
            } finally {
                fos.close();
            }
            
            if ("Y".equals(SEND_AUDITFTP)) {
            	// 上傳AUDIT
                this.rptftpToAUDIT("clsGroup_906P", dateYM + "_近一年分行敘做消金整批貸款清單月報.xls"
                		, "906", outputStream.toByteArray());
            }
			

            //CSV
            List<Map<String, Object>> lstData = cls9071Service.get_J_107_0046ForAudit(false  //includeOnTheWay false 只抓簽案已核准
            		,"", "", "" 
					,"4", dateYM , dateYMBegin , dateYM
					, true);
            List<String> outList = new ArrayList<String>();
            outList.add(this.wrapDoubleQuote("團貸額度統編") + "," 
            		+ this.wrapDoubleQuote("團貸報案分行") + "," 
            		+ this.wrapDoubleQuote("團貸額度序號") + "," 
            		+ this.wrapDoubleQuote("(原始)團貸額度序號") + ","
                    + this.wrapDoubleQuote("團貸案名") + "," 
                    + this.wrapDoubleQuote("流用子公司") + "," 
                    + this.wrapDoubleQuote("子戶分行") + ","
                    + this.wrapDoubleQuote("客戶統編") + "," 
                    + this.wrapDoubleQuote("客戶姓名") + "," 
                    + this.wrapDoubleQuote("子戶額度序號") + ","
                    + this.wrapDoubleQuote("放款帳號") + "," 
                    + this.wrapDoubleQuote("科目") + "," 
                    + this.wrapDoubleQuote("首撥日") + ","
                    + this.wrapDoubleQuote("首次核准額度") + ","
                    + this.wrapDoubleQuote("首撥金額") + "," 
                    + this.wrapDoubleQuote("額度") + "," 
                    + this.wrapDoubleQuote("餘額") + "," 
                    + this.wrapDoubleQuote("利率") + "," 
                    + this.wrapDoubleQuote("銷戶日"));

            if (lstData.size() > 0) {
                for (Map<String, Object> data : lstData) {
                    // 逐一寫明細
                    // 數字，日期格式不加雙引號，字串如果內含雙引號，以單引號取代
                    outList.add(this.wrapDoubleQuote(this.replaceDoubleQuote(Util.trim(MapUtils.getString(data, "field1", "")))) + ","
                            + this.wrapDoubleQuote(this.replaceDoubleQuote(Util.trim(MapUtils.getString(data, "field2", "")))) + ","
                            + this.wrapDoubleQuote(this.replaceDoubleQuote(Util.trim(MapUtils.getString(data, "field3", "")))) + ","
                            + this.wrapDoubleQuote(this.replaceDoubleQuote(Util.trim(MapUtils.getString(data, "field4", "")))) + "," 
                            + this.wrapDoubleQuote(this.replaceDoubleQuote(Util.trim(MapUtils.getString(data, "field5", "")))) + "," 
                            + this.wrapDoubleQuote(this.replaceDoubleQuote(Util.trim(MapUtils.getString(data, "field6", "")))) + "," 
                            + this.wrapDoubleQuote(this.replaceDoubleQuote(Util.trim(MapUtils.getString(data, "field7", "")))) + ","
                            + this.wrapDoubleQuote(this.replaceDoubleQuote(Util.trim(MapUtils.getString(data, "field8", "")))) + ","
                            + this.wrapDoubleQuote(this.replaceDoubleQuote(Util.trim(MapUtils.getString(data, "field9", "")))) + ","
                            + this.wrapDoubleQuote(this.replaceDoubleQuote(Util.trim(MapUtils.getString(data, "field10", "")))) + ","
                            + this.wrapDoubleQuote(this.replaceDoubleQuote(Util.trim(MapUtils.getString(data, "field11", "")))) + ","
                            + this.wrapDoubleQuote(this.replaceDoubleQuote(Util.trim(MapUtils.getString(data, "field12", "")))) + ","
                            + Util.trim(MapUtils.getString(data, "field13", "")) + ","
                            + Util.trim(MapUtils.getString(data, "field14", "")) + ","
                            + Util.trim(MapUtils.getString(data, "field15", "")) + ","
                            + Util.trim(MapUtils.getString(data, "field16", "")) + ","
                            + Util.trim(MapUtils.getString(data, "field17", "")) + ","
                            + Util.trim(MapUtils.getString(data, "field18", "")) + ","
                            + Util.trim(MapUtils.getString(data, "field19", ""))
                        );
                }
            } else {
                outList.add(""); // 沒有明細也要
            }

            if (outList.size() > 0) {
                try {
                    logger.info("FTP clsGroup_906P.csv START");
                    String rptfileName = dateYM + "_clsGroup_906P.csv";
                    File txtFile = new File(FilenameUtils.normalize(StringChecker.checkPathString(docFileBaseDir, true))
                    		, StringChecker.checkPathString(rptfileName, true));
                    txtFile.setReadable(true, true);
                    txtFile.setWritable(true, true);
                    txtFile.setExecutable(false, true);

                    FileUtils.writeLines(txtFile, "BIG5", outList, "\r\n");

                    logger.info("[execute] FTP Client : {}", auditFtpClient.toString());
                    logger.info("開始執行 " + rptfileName + " 取得報表檔傳至稽核處");
                    
                    try {
                    	if ("Y".equals(SEND_AUDITFTP)) {
                    		auditFtpClient.send("clsGroup_906P", txtFile.toString(), auditFtpClient.getServerDir()
                            		, dateYM + "_近一年分行敘做消金整批貸款清單月報.csv", true, true, false);
                    	}
                    } catch (Exception e) {
                        logger.error("執行 " + rptfileName + " 取得報表檔傳至稽核處失敗");
                        logger.error(e.getMessage());
                    }
                    logger.info("執行 " + rptfileName + " 取得報表檔傳至稽核處成功");
                } catch (Exception ex) {
                	logger.error(ex.getMessage(), ex);
                	errMsg = ex.getMessage();
                }
            } else {
                logger.info("FTP 近一年分行敘做消金整批貸款清單月報.csv is empty");
            }
			
			
		} catch (Exception e) {
			errMsg = e.getMessage();
	        logger.error(errMsg, e);
	    }
		return errMsg;
	}

	private ByteArrayOutputStream generateXls(boolean includeOnTheWay, String brNo, String custId, String grpCntrNo, 
			String rptNo, String p_dataYM1 , String c_dataYM1 , String c_dataYM2) throws IOException, Exception {	
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		if(true){
			cls9071Service.genExcel_J_107_0046(outputStream, includeOnTheWay
					, brNo, custId, grpCntrNo, rptNo,  p_dataYM1, c_dataYM1, c_dataYM2
					, true);	  //isAUDIT
		}		
		if(outputStream!=null){
			outputStream.flush();	
		}		
		return outputStream;
	}
	
	public boolean rptftpToAUDIT(String mainId, String rptfileName, String brno, byte[] byteArray) throws Exception {
        // throws CapException {
        boolean isSuccess = false;
        logger.info("開始執行 " + rptfileName + " 取得報表檔傳至稽核處");

        try {
            auditFtpClient.send(mainId, byteArray, auditFtpClient.getServerDir(), rptfileName, true, true, false, brno);
            
            logger.info("執行 " + rptfileName + " 取得報表檔傳至稽核處成功");
            isSuccess = true;
        } catch (Exception e) {
            logger.error("執行 " + rptfileName + " 取得報表檔傳至稽核處失敗");
            logger.error(e.getMessage());
            throw e;
        }
        return isSuccess;
    }
	
	private String replaceDoubleQuote(String txt) {
        return txt.replaceAll("\"", "'");
    }

    private String wrapDoubleQuote(String txt) {
        return new StringBuffer("\"").append(txt).append("\"").toString();

    }
	
}
