var pageAction = {
    readOnly: false,
    handler: 'cls1161m02formhandler',
    keepDtlRow:{}, 
    init: function(){
        $.ajax({
            handler: pageAction.handler,
            action: 'load',
            data: {},
            success: function(response){
                $('form').each(function(){
                	var $form = $(this);
                    var formId = $form.attr('id') || '';
                    var formData = response[formId];
                    if (formData) 
                        $form.setValue(formData);
                });
                if(response.s01q_has_srcMainId=="Y"){
                	$("#btUploadExcel").remove();	
                }
            }
        });
    },
    build: function(){
        $('body').buildItem();
        // 儲存
        $('#buttonPanel').find('#btnSave').click(function(){
            if (pageAction.check()) {
                pageAction.save();
            }
        }).end().find('#btnSend').click(function(){
            // 呈主管
            $.ajax({
                handler: pageAction.handler,
                action: 'sendCheck', //sendBeforeCheck
                formId: 'C160M02AForm',
                data: pageAction.getSaveData(),
                success: function(response){
                    if (response.message) {
                        CommonAPI.confirmMessage(i18n.def["confirmApprove2"] + '<br/>' + response.message, function(b){
                            if (b) 
                                pageAction.openManager();
                        });
                    }
                    else {
                        pageAction.openManager();
                    }
                }
            });
        }).end().find('#btnReturn').click(function(){
            // 退回
            pageAction.flow({
                activity: 'back'
            });
        }).end().find('#btnReUpDate').click(function(){
            $.ajax({
                handler: pageAction.handler,
                action: 'ReUpDate',
                data: {
                    oid: $('#oid').val()
                },
                success: function(response){
                
                }
            });
        }).end().find('#btnPrint').click(function(){
            // 列印動作
            if (auth.readOnly) {
                pageAction.Printpdf();
            }
            else {
                // saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作?
                CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                    if (b) {
                        $.ajax({
                            handler: pageAction.handler,
                            action: 'save',
                            formId: 'C160M02AForm',
                            data: pageAction.getSaveData(),
                            success: function(response){
                                pageAction.Printpdf();
                            }
                        });
                    }
                });
            }
        });
                
        // set readOnly
        var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
        if (auth.readOnly) {
            $('body').lockDoc(true);
            thickboxOptions.lockDoc = true;
            $('#displayCntrNo').append($('#CaseType3ThickBox').children());
        }
    },
    check: function(){
        return true;
    },
    save: function(){
        $.ajax({
            handler: pageAction.handler,
            action: 'save',
            formId: 'C160M02AForm',
            data: pageAction.getSaveData(),
            success: function(response){
                if (response.useType) {
                    pageAction.showTab4(response.useType);
                }
                pageAction.init();
                MegaApi.showPopMessage(i18n.def['confirmTitle'], i18n.def['saveSuccess'] +
                (response.message ? '<br/><br/>' + response.message : ''));
            }
        });
    },
    /**
     * Excel上傳MIS DB
     */
    xlsToMis: function(data){
        $.ajax({
            handler: pageAction.handler,
            action: 'xlsToMis',
            formId: 'CaseType3Form',
            data: data,
            success: function(response){
                if (response.finish) {
                    $('#progress').html(100); // finish progress
                    // lert('accept');
                    // return;
                    pageAction.flowAction({
                        activity: 'accept'
                    });
                }
                else {
                    if (/\d/.test(response.end) && /\d/.test(response.tot)) {
                        var end = parseInt(response.end);
                        var tot = parseInt(response.tot);
                        if (end <= tot && end > 0 && tot > 0) {
                            var progress = ((end / tot) * 100).toFixed(2);
                            $('#progress').html(progress); // finish progress
                            pageAction.xlsToMis({
                                start: end
                            });
                        }
                    }
                    else {
                        // alert('this is error!');
                    }
                }
            }
        });
    },
    Printpdf: function(){
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                fileDownloadName: "cls1161r01.pdf",
                serviceName: "cls1161r01rptservice"
            }
        });
    },
    doAdjRating: function(data){
    	if(true){
        	$("tr.hs_jcicFlg").hide();
        }
    	$.ajax({
            handler: pageAction.handler,
            action: 'prepareAdjRating',
            data: data,
            success: function(json_prepareAdjRating){
            	$("#form_markModel_2").injectData(json_prepareAdjRating);            	
            	pageAction.keepDtlRow = $.extend(data, {'markModel':'2', 'useC120Batch':'Y'});            	
            	hs_jcicFlg(json_prepareAdjRating)
            	$('#div_form_markModel_2').thickbox({
                    title: (data.custId+"："+data.custName), 
                    width: 800, height: 420, align: 'center', valign: 'bottom', modal:true ,
                    buttons: {
                        'close': function(){
                            $.thickbox.close();
                        }
                    }
                });
            }
        });
    	
    },
    /**
     * 開啟文件
     */
    openQDoc: function(data){
        var scoreNotHouseLoanSheet = $('#scoreNotHouseLoanSheet');
        if (data.rawMarkModel == "2") {
            //載入 非房貸評分表
            //因為有不同的版本，改成每次
            scoreNotHouseLoanSheet.empty();
        }
        var param = {
            'mainId': data.mainId,
            'custId': data.custId,
            custName: data.custName,
            'dupNo': data.dupNo,
            'use_handler': 'cls1161m02formhandler',
            'isC120M01A': 'Y',
            'noOpenDoc': true
        };
        scoreNotHouseLoanSheet.load('../cls1131s05', param, function(){
            var mainId = param.mainId;
            var custId = param.custId;
            var dupNo = param.dupNo;
            var handler = param.use_handler;
            
            $('#C101S01QDiv').buildItem();
            $('#C101S01QForm').readOnlyChilds();
            $.ajax({
                handler: handler,
                action: 'loadScore',
                formId: 'C101S01QForm',
                data: {
                    'mainId': mainId,
                    'custId': custId,
                    'dupNo': dupNo,
                    custName: param.custName,
                    'noOpenDoc': true,
                    'markModel': '2'
                },
                success: function(response){
					var C101S01QForm = $('#C101S01QForm');
                    C101S01QForm.setValue(response.C101S01QForm);
                    
					var C101S01QThickBox = $('#C101S01QThickBox');
                    C101S01QThickBox.thickbox({
                        title: ((i18n.cls1131s01q['C101S01Q.title'] || '') + response.C101S01QForm.varVer),
                        width: 800,
                        height: 450,
                        align: 'center',
                        valign: 'bottom',
                        buttons: {
                            'close': function(){
                                $.thickbox.close();
                            }
                        }
                    });
                    // default tab
                    $('#C101S01QTab').tabs({
                        selected: 0
                    });
                }
            });
        });
        
    }
};

$(function(){
    pageAction.build();
    pageAction.init();
});

function hs_jcicFlg(json){	
	var s_jcicFlg = json.jcicFlg+"      ";
	var jcicFlg_first2chars = s_jcicFlg.substring(0, 2);
	var jcicFlg_last2chars = s_jcicFlg.substring(1, 3);
	//若是 NNN，應該優先適用判斷後2碼		
	if(jcicFlg_last2chars=="NN"){
		$("span.date_jcicFlg_V_NN").val(json.date_jcicFlg_V_NN||'');
		$("tr.hs_jcicFlg_V_NN").show();
	}else if(jcicFlg_first2chars=="NN"){
		$("tr.hs_jcicFlg_VNN_").show();
	}			
}


function alwaysConfirmAdjReason(cnt, obj){
	var my_dfd = $.Deferred();
	if(cnt=="0"){
		my_dfd.resolve();
	}else{	
		if(true){
			$("#adjustReasonAlwaysCfmMsg").html(obj.alwaysCfmStr);
		}
		$("#divAdjustReasonAlwaysCfmMsg").thickbox({
	        title: "", width: 550, height: 180,
	        align: "center", valign: "bottom", modal: false,
	        i18n: (obj || i18n.def),
	        buttons: {
	        	"alwaysCfmN": function(){
	                $.thickbox.close();
	                my_dfd.reject();
	            },
	            "alwaysCfmY": function(){
	            	//=============
	                $.thickbox.close();
	            	my_dfd.resolve();
	            }
	        }
	    });	
	}		
	return my_dfd.promise(); 
}
function procCfmMsg(obj){
	var my_dfd = $.Deferred();
	
	if((obj.cfmStr||"")==""){
		my_dfd.resolve();
	}else{		
		if(true){
			$("#adjustReasonCfmMsg").html(obj.cfmStr);
		}
		$("#divAdjustReasonCfmMsg").thickbox({
	        title: "", width: 600, height: 200,
            align: "center", valign: "bottom", modal: false,
            i18n: (obj || i18n.def),
            buttons: {
            	"cfmN": function(){
                    $.thickbox.close();
                    my_dfd.reject();
                },
                "cfmY": function(){
                	//=============
                    $.thickbox.close();
                	my_dfd.resolve();
                }
            }
	    });	
		
	}
	return my_dfd.promise(); 
}


function openAdjust(){	
	if ($('#adjustNotHouseLoanThickBox').size() == 0) {		
        $('#adjustNotHouseLoanSheet').load(webroot + '/app/cls/cls1131s06', function(){        	
            AdjustNotHouseLoanAction.handler = 'cls1161m02formhandler';            
            AdjustNotHouseLoanAction.open(pageAction.keepDtlRow||{});
        });
    }
    else {
        AdjustNotHouseLoanAction.open(pageAction.keepDtlRow||{});
    }
}

// 畫面切換table 所需設定之資料 如無設定 則直接切換
$.extend(window.tempSave, {
    handler: pageAction.handler,
    action: 'tempSave',
    beforeCheck: function(){
        if ($.trim($('#custInfo').html()) === '') {
            MegaApi.showErrorMessage(i18n.def['confirmTitle'], i18n.def['includeId.subTitle'] + ' ' +
            i18n.def['val.required']);
            return false;
        }
        return true;
    },
    sendData: function(){
    }
});
