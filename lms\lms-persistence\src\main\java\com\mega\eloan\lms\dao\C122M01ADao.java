package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;
import java.util.TreeSet;

import tw.com.iisi.cap.dao.IGenericDao;
import tw.com.iisi.cap.dao.utils.ISearch;

import com.mega.eloan.lms.model.C122M01A;

/** 網銀線上增貸 **/
public interface C122M01ADao extends IGenericDao<C122M01A> {

	C122M01A findByOid(String oid);
	
	C122M01A findByMainId(String mainId);

	List<C122M01A> find_0A0_C122M01A(String[] applyKind_arr); //通知T1，有線上申貸進件案件
	
	List<C122M01A> findInProgressC122M01A(String ownBrId, String[] applyKind_arr); // SLMS-00026 , SLMS-00028 未結案報表[分行, 總處]
	
	List<C122M01A> findInProgressC122M01A(String ownBrId, String[] applyKind_arr, String custId, String dupNo);
	
	List<C122M01A> findBy_brNo_custId_applyKind_orderByApplyTSDesc(String ownBrId, String custId, String applyKind);
	List<C122M01A> findBy_brNo_custId_applyKind_orderByApplyTSDesc(String ownBrId, String custId, String[] applyKind_arr);
	
	List<C122M01A> findBy_brNo_custId_applyKind_orderByApplyTSDesc(String ownBrId, String custId, String applyKind, String createTimeSince);
	List<C122M01A> findBy_brNo_custId_applyKind_orderByApplyTSDesc(String ownBrId, String custId, String[] applyKind_arr, String createTimeSince);
	
	List<C122M01A> findBy_brNo_custId_applyKind_for_C160S01D_orderByApplyTSDesc(String ownBrId, String custId, String[] applyKind_arr, String createTimeSince, String exclude_statFlag);
	
	List<C122M01A> findBy_custId_applyKind_orderByApplyTSDesc(String custId, String applyKind, String createTimeSince);
	
	List<C122M01A> queryUnMatchReason(String custId, String dupNo);
	
	List<C122M01A> findBy_ploanCaseId(String ploanCaseId);
	
	TreeSet<String> queryOwnBranchListC122M01A(String applyKind); 
	
	/**
	 * @param status
	 * @return 裡面用到 setDistinctColumn(...), 雖然回傳 obj, 但只有 ownBrId 有值, 其它欄位(例如: custId)是null
	 */
	TreeSet<String> queryOwnBranchListC122M01AByApplyStatus(String applyKind, String[] applyStatus);
	
	List<Object[]> getOnLineLoanByBranch(String applyKind, String ownBrId); 

	List<Object[]> getAllOnLineLoan(String applyKind);

	List<Object[]> getCloseCaseByBranchAndMonth(String applyKind, String ownBrId, String yyyy_mm); 

	List<C122M01A> queryCreditLineByBranch(String applyKind, String ownBrId); 

	/** ploanCaseNo:主借人、保證人各有各自的 caseNo，但可用 ploanCaseId 去彙總(主借人的 ploanCaseId=ploanCaseNo 且 ploanCasePos=空白)
	 * 	ApplyKind	PloanCaseNo			PloanCaseId			PloanCasePos
	 *	E-房貸主借人	HA20200724000046	HA20200724000046	 
	 *	F-從債務人	HA20200724000047	HA20200724000046	N
	 */
	C122M01A findPloanParentByApplyKind_ploanCaseId(String applyKind, String ploanCaseId);
	C122M01A findValidContractBy_brNo_custId_applyKind_orderByCreateTime_ApplyTSDesc(String ownBrId, String custId, String[] applyKind_arr, String createTimeSince);
	List<C122M01A> findInProgressLastC122M01A(String ownBrId, String[] applyKind_arr, String custId,
													 String dupNo);
	List<C122M01A> findC122M01AList(ISearch search);
	
	//J-111-0226 配合青創貸款線上申請作業，額度明細表增加引進青創線上申請之欄位
	public List<C122M01A> findyoungLoanListBy_brNo_custId_applyKind(String ownBrId, String custId);

	public List<C122M01A> findOnlineCasesRequiringInitialCheck();

	/**
	 * 查找自動派案初審婉拒，且尚未發送簡訊之案件
	 * @return List<C122M01A>
	 */
	List<C122M01A> findSmsSendingCaseForOnlineInitialCheckCase();

	/**
	 * 查找自動派案初審婉拒，且已發送簡訊而尚未成功與尚未確定失敗之案件
	 * @return List<C122M01A>
	 */
	List<C122M01A> findSmsSentCaseForOnlineInitialCheckCase();

	C122M01A findByPloanCaseNo(String ploanCaseNo);

	List<Object[]> findSMEAList(Date sDate,Date eDate);
}