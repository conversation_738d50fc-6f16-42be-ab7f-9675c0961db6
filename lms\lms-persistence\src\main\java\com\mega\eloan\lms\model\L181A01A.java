/* 
 * L181A01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.IDocObject;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 覆審控制檔授權檔 **/
@NamedEntityGraph(name = "L181A01A-entity-graph", attributeNodes = { @NamedAttributeNode("l181m01a") })
@Entity
@Table(name="L181A01A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","ownUnit","authType","authUnit"}))
public class L181A01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件
	 * 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false)})
    private L181M01A l181m01a;

	public L181M01A getL181m01a() {
		return l181m01a;
	}
	public void setL181m01a(L181M01A l181m01a) {
		this.l181m01a = l181m01a;
	}
	
	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** pid **/
	@Column(name="PID", length=32, columnDefinition="CHAR(32)")
	private String pid;

	/** 授權單位 **/
	@Column(name="OWNUNIT", length=3, columnDefinition="CHAR(3)")
	private String ownUnit;

	/** 授權人員 **/
	@Column(name="OWNER", length=6, columnDefinition="CHAR(6)")
	private String owner;

	/** 授權日期 **/
	@Column(name="AUTHTIME", columnDefinition="TIMESTAMP")
	private Date authTime;

	/** 
	 * 授權類別<p/>
	 * 1.編製/移送<br/>
	 *  2.代編<br/>
	 *  3.傳簽<br/>
	 *  4.傳送(授權)
	 */
	@Column(name="AUTHTYPE", length=1, columnDefinition="CHAR(1)")
	private String authType;

	/** 被授權單位 **/
	@Column(name="AUTHUNIT", length=3, columnDefinition="CHAR(3)")
	private String authUnit;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得pid **/
	public String getPid() {
		return this.pid;
	}
	/** 設定pid **/
	public void setPid(String value) {
		this.pid = value;
	}

	/** 取得授權單位 **/
	public String getOwnUnit() {
		return this.ownUnit;
	}
	/** 設定授權單位 **/
	public void setOwnUnit(String value) {
		this.ownUnit = value;
	}

	/** 取得授權人員 **/
	public String getOwner() {
		return this.owner;
	}
	/** 設定授權人員 **/
	public void setOwner(String value) {
		this.owner = value;
	}

	/** 取得授權日期 **/
	public Date getAuthTime() {
		return this.authTime;
	}
	/** 設定授權日期 **/
	public void setAuthTime(Date value) {
		this.authTime = value;
	}

	/** 
	 * 取得授權類別<p/>
	 * 1.編製/移送<br/>
	 *  2.代編<br/>
	 *  3.傳簽<br/>
	 *  4.傳送(授權)
	 */
	public String getAuthType() {
		return this.authType;
	}
	/**
	 *  設定授權類別<p/>
	 *  1.編製/移送<br/>
	 *  2.代編<br/>
	 *  3.傳簽<br/>
	 *  4.傳送(授權)
	 **/
	public void setAuthType(String value) {
		this.authType = value;
	}

	/** 取得被授權單位 **/
	public String getAuthUnit() {
		return this.authUnit;
	}
	/** 設定被授權單位 **/
	public void setAuthUnit(String value) {
		this.authUnit = value;
	}
}
