/* 
 * F101A01ADao.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.F101A01A;
import com.mega.eloan.lms.model.F101M01A;

/**
 * <pre>
 * F101A01A dao interface.
 * </pre>
 * 
 * @since 2011/8/1
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/8/1,Sunkist Wang,new</li>
 *          <li>2011/8/13,Sunkist Wang,add
 *          {@link F101A01ADao#deleteByMeta(F101M01A)}</li>
 *          </ul>
 */
public interface F101A01ADao extends IGenericDao<F101A01A> {
	/**
	 * 刪除所有資料
	 * 
	 * @param meta
	 *            F101M01A
	 * @return int
	 */
	int deleteByMeta(F101M01A meta);

	/**
	 * 依meta取得授權資料，提供傳送動作使用。
	 * 
	 * @param mainId
	 *            meta's mainId
	 * @param pid
	 *            meta's uid
	 * @param branch
	 *            branch
	 * @return F101A01A
	 */
	F101A01A findF101A01AByBranch(String mainId, String pid, String branch);
}
