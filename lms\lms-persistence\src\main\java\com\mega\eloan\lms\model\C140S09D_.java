package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * The persistent class for the C140S09D database table.
 * </pre>
 * @since  2011/10/27
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/10/27,<PERSON>,new
 *          </ul>
 */
@StaticMetamodel(C140S09D.class)
public class C140S09D_ extends RelativeMeta_{
	public static volatile SingularAttribute<C140S09D, BigDecimal> gbaAgm1;
	public static volatile SingularAttribute<C140S09D, String> gbaNa1;
	public static volatile SingularAttribute<C140S09D, BigDecimal> gbaOdm1;
	public static volatile SingularAttribute<C140S09D, BigDecimal> gbaRamt;
	public static volatile SingularAttribute<C140S09D, BigDecimal> gbaXdm1;
	public static volatile SingularAttribute<C140S09D, C140M01A> c140m01a;
}
