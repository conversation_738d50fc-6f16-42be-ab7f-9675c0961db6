/* 
 * LMS1601V03Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.pages;

import java.util.ArrayList;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import tw.com.jcs.auth.AuthType;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * <pre>
 * 動用審核表 - 已覆核
 * </pre>
 * 
 * @since 2011/10/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/5,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1601v03")
public class LMS1601V03Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_已核准);

		// 加上Button
		ArrayList<LmsButtonEnum> btns = new ArrayList<LmsButtonEnum>();
		// 主管跟經辦都會出現的按鈕
		btns.add(LmsButtonEnum.Filter);
		btns.add(LmsButtonEnum.View);
		btns.add(LmsButtonEnum.LogeIN);
		btns.add(LmsButtonEnum.UseFirstTable);
		//btns.add(CreditButtonEnum.DataFix); //103.01.17 取消特殊資料修改流程，改以動審表額度動用資訊修改
		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {

		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {

		}
		addToButtonPanel(model, btns.toArray(new LmsButtonEnum[] {}));
		// 加上Button
		renderJsI18N(LMS1601V01Page.class);
		renderJsI18N(LMS1601M01Page.class);
	}
	
	@Override
	protected String getContentPageName() {
		return "lns/pages/LMS1601V03Page";
	}

}
