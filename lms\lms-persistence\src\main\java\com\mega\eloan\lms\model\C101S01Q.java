/* 
 * C101S01Q.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金非房貸信用評等表 **/
@NamedEntityGraph(name = "C101S01Q-entity-graph", attributeNodes = { @NamedAttributeNode("c101m01a") })
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C101S01Q", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","ownBrId","custId","dupNo"}))
public class C101S01Q extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 編製單位代號<p/>
	 * 單位代碼
	 */
	@Size(max=3)
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;

	/** 身分證統編 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 借款人姓名 **/
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 文件狀態 **/
	@Size(max=3)
	@Column(name="DOCSTATUS", length=3, columnDefinition="CHAR(3)")
	private String docStatus;

	/** 評等建立日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="GRDCDATE", columnDefinition="DATE")
	private Date grdCDate;

	/** 評等調整日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="GRDTDATE", columnDefinition="DATE")
	private Date grdTDate;

	/** 聯徵查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="JCICQDATE", columnDefinition="DATE")
	private Date jcicQDate;

	/** 票信查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ETCHQDATE", columnDefinition="DATE")
	private Date etchQDate;

	/** 個人年收入（萬元） **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="YPAY", columnDefinition="DECIMAL(13,0)")
	private BigDecimal yPay;

	/** 個人年收入分數 **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="SCRYPAY", columnDefinition="DEC(7,4)")
	private BigDecimal scrypay;

	/** 年資 **/
	@Digits(integer=2, fraction=2, groups = Check.class)
	@Column(name="SENIORITY", columnDefinition="DECIMAL(4,2)")
	private BigDecimal seniority;

	/** 年資分數 **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="SCRSENIORITY", columnDefinition="DEC(7,4)")
	private BigDecimal scrseniority;

	/** 學歷 **/
	@Size(max=2)
	@Column(name="EDUCATION", length=2, columnDefinition="CHAR(2)")
	private String education;

	/** 學歷分數 **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="SCREDUCATION", columnDefinition="DEC(7,4)")
	private BigDecimal screducation;

	/** 
	 * 聯徵查詢月份當時無擔保授信往來家數(排除學生助學貸款(Z)、本行貸款)<p/>
	 * d63_ln_nos_bank
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="NOCHKITEM01", columnDefinition="DEC(5,2)")
	private BigDecimal nochkItem01;

	/** 聯徵查詢月份當時無擔保授信往來家數(排除學生助學貸款(Z)、本行貸款)分數 **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="NOSCRITEM01", columnDefinition="DEC(7,4)")
	private BigDecimal noscrItem01;

	/** 
	 * 近6個月信用卡使用循環信用的月份數<p/>
	 * a21_cc6_rc_use_month
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="NOCHKITEM02", columnDefinition="DEC(5,2)")
	private BigDecimal nochkItem02;

	/** 近6個月信用卡使用循環信用的月份數分數 **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="NOSCRITEM02", columnDefinition="DEC(7,4)")
	private BigDecimal noscrItem02;

	/** 
	 * 近6個月信用卡使用循環信用的家數<p/>
	 * a11_cc6_rc_use_bank
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="NOCHKITEM03", columnDefinition="DEC(5,2)")
	private BigDecimal nochkItem03;

	/** 近6個月信用卡使用循環信用的家數分數 **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="NOSCRITEM03", columnDefinition="DEC(7,4)")
	private BigDecimal noscrItem03;

	/** 
	 * 聯徵查詢月份當時授信繳款記錄小於等於6次旗標<p/>
	 * d53_ln_6_times_flag
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="NOCHKITEM04", columnDefinition="DEC(5,2)")
	private BigDecimal nochkItem04;

	/** 聯徵查詢月份當時授信繳款記錄小於等於6次旗標分數 **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="NOSCRITEM04", columnDefinition="DEC(7,4)")
	private BigDecimal noscrItem04;

	/** 組別(房貸模型用到此欄位、為免重複，非房貸不用) **/
	@Size(max=20)
	@Column(name="GRPNUM10", length=20, columnDefinition="VARCHAR(20)")
	private String grpNum10;

	/** 合計變量得分 **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="SCRNUM11", columnDefinition="DEC(7,4)")
	private BigDecimal scrNum11;

	/** 
	 * 基準底分<p/>
	 * A + B ＊ 常數項
	 */
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="SCRNUM12", columnDefinition="DEC(7,4)")
	private BigDecimal scrNum12;

	/** 
	 * 初始評分<p/>
	 * scrNum11 + scrNum12
	 */
	@Digits(integer=8, fraction=4, groups = Check.class)
	@Column(name="SCRNUM13", columnDefinition="DEC(8,4)")
	private BigDecimal scrNum13;

	/** 
	 * 初始評等<p/>
	 * 初始評分對照的等級
	 */
	@Size(max=2)
	@Column(name="GRADE1", length=2, columnDefinition="VARCHAR(2)")
	private String grade1;

	/** 
	 * 調整評等<p/>
	 * 人工調整
	 */
	@Size(max=2)
	@Column(name="GRADE2", length=2, columnDefinition="VARCHAR(2)")
	private String grade2;

	/** 最終評等 **/
	@Size(max=2)
	@Column(name="GRADE3", length=2, columnDefinition="VARCHAR(2)")
	private String grade3;

	/** 
	 * 預測壞率<p/>
	 * 計算：小數點4 位，四捨五入<br/>
	 *  (1/(1+Exp(scrNum13 – A/B)))
	 */
	@Digits(integer=9, fraction=4, groups = Check.class)
	@Column(name="PD", columnDefinition="DEC(9,4)")
	private BigDecimal pd;

	/** 
	 * 有退票、拒往、信用卡強停或催收呆帳紀錄<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max=1)
	@Column(name="CHKITEM1", length=1, columnDefinition="CHAR(1)")
	private String chkItem1;

	/** 退票 **/
	@Size(max=1)
	@Column(name="CHKITEM1A", length=1, columnDefinition="CHAR(1)")
	private String chkItem1a;

	/** 拒往 **/
	@Size(max=1)
	@Column(name="CHKITEM1B", length=1, columnDefinition="CHAR(1)")
	private String chkItem1b;

	/** 信用卡強停 **/
	@Size(max=1)
	@Column(name="CHKITEM1C", length=1, columnDefinition="CHAR(1)")
	private String chkItem1c;

	/** 催收呆帳 **/
	@Size(max=1)
	@Column(name="CHKITEM1D", length=1, columnDefinition="CHAR(1)")
	private String chkItem1d;
	
	/** 逾期放款 **/
	@Size(max = 1)
	@Column(name = "CHKITEM1E", length = 1, columnDefinition = "CHAR(1)")
	private String chkItem1e;

	/** 
	 * 有消債條例信用註記、銀行公會債務協商註記或其他補充註記<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max=1)
	@Column(name="CHKITEM2", length=1, columnDefinition="CHAR(1)")
	private String chkItem2;

	/** 消債條例信用註記 **/
	@Size(max=1)
	@Column(name="CHKITEM2A", length=1, columnDefinition="CHAR(1)")
	private String chkItem2a;

	/** 銀行公會債務協商註記 **/
	@Size(max=1)
	@Column(name="CHKITEM2B", length=1, columnDefinition="CHAR(1)")
	private String chkItem2b;

	/** 其他補充註記 **/
	@Size(max=1)
	@Column(name="CHKITEM2C", length=1, columnDefinition="CHAR(1)")
	private String chkItem2c;

	/** 
	 * 近12個月授信帳戶出現延遲二次（含）以上<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max=1)
	@Column(name="CHKITEM3", length=1, columnDefinition="CHAR(1)")
	private String chkItem3;

	/** 
	 * 近12個月信用卡繳款狀況出現（循環信用有延遲）二次（含）以上<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max=1)
	@Column(name="CHKITEM4", length=1, columnDefinition="CHAR(1)")
	private String chkItem4;

	/** 
	 * 近12個月信用卡繳款狀況出現（未繳足最低金額）二次（含）以上<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max=1)
	@Column(name="CHKITEM5", length=1, columnDefinition="CHAR(1)")
	private String chkItem5;

	/** 
	 * 近12個月信用卡繳款狀況出現（全額逾期連續未繳）二次（含）以上<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max=1)
	@Column(name="CHKITEM6", length=1, columnDefinition="CHAR(1)")
	private String chkItem6;

	/** 
	 * 近12個月信用卡有預借現金餘額二次（含）以上<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max=1)
	@Column(name="CHKITEM7", length=1, columnDefinition="CHAR(1)")
	private String chkItem7;

	/** 
	 * 近12個月現金卡有動用紀錄<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	@Size(max=1)
	@Column(name="CHKITEM8", length=1, columnDefinition="CHAR(1)")
	private String chkItem8;

	/** 基準底分A **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="VARA", columnDefinition="DEC(5,0)")
	private BigDecimal varA;

	/** 基準底分B **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="VARB", columnDefinition="DEC(7,4)")
	private BigDecimal varB;

	/** 基準底分常數項 **/
	@Digits(integer=9, fraction=7, groups = Check.class)
	@Column(name="VARC", columnDefinition="DEC(9,7)")
	private BigDecimal varC;

	/** 基準底分版本 **/
	@Size(max=10)
	@Column(name="VARVER", length=10, columnDefinition="VARCHAR(10)")
	private String varVer;

	/** 報表亂碼 **/
	@Size(max=32)
	@Column(name="RANDOMCODE", length=32, columnDefinition="CHAR(32)")
	private String randomCode;

	/** 
	 * 調整狀態<p/>
	 * 1.調升 2.調降 3.回復
	 */
	@Size(max=1)
	@Column(name="ADJUSTSTATUS", length=1, columnDefinition="CHAR(1)")
	private String adjustStatus;

	/** 
	 * 調整註記(升等)<p/>
	 * 1.淨資產2.職業3.其它
	 */
	@Size(max=1)
	@Column(name="ADJUSTFLAG", length=1, columnDefinition="CHAR(1)")
	private String adjustFlag;

	/** 調整理由 **/
	@Size(max=300)
	@Column(name="ADJUSTREASON", length=300, columnDefinition="VARCHAR(300)")
	private String adjustReason;

	/** 
	 * 未持有信用卡<p/>
	 * Y.是
	 */
	@Size(max=1)
	@Column(name="CARDFLAG", length=1, columnDefinition="CHAR(1)")
	private String cardFlag;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 是否引用<p/>
	 * Y.是
	 */
	@Size(max=1)
	@Column(name="QUOTE", length=1, columnDefinition="CHAR(1)")
	private String quote;
	
	/** 違約機率(預估2年期)-短 **/
	@Digits(integer = 8, fraction = 5, groups = Check.class)
	@Column(name = "DR_2YR", columnDefinition = "DEC(8,5)")
	private BigDecimal dr_2YR;
	
	/** 違約機率(預估3年期)-中長 **/
	@Digits(integer = 8, fraction = 5, groups = Check.class)
	@Column(name = "DR_3YR", columnDefinition = "DEC(8,5)")
	private BigDecimal dr_3YR;
	
	/** 違約機率(預估1年期)-短 **/
	@Digits(integer = 8, fraction = 5, groups = Check.class)
	@Column(name = "DR_1YR_S", columnDefinition = "DEC(8,5)")
	private BigDecimal dr_1YR_S;
	
	/** 違約機率(預估1年期)-中長 **/
	@Digits(integer = 8, fraction = 5, groups = Check.class)
	@Column(name = "DR_1YR_L", columnDefinition = "DEC(8,5)")
	private BigDecimal dr_1YR_L;
	
	/** 個人年所得(年薪+其他收入)(萬元) **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="PINCOME", columnDefinition="DECIMAL(13,0)")
	private BigDecimal pIncome;

	/** 個人年所得(年薪+其他收入)分數 **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="SCRPINCOME", columnDefinition="DEC(7,4)")
	private BigDecimal scrPIncome;

	/** 聯徵查詢月份當月無擔保授信餘額(仟元)排除學生助學貸款(z) **/
	@Digits(integer=9, fraction=0, groups = Check.class)
	@Column(name="NOCHKITEMD07", columnDefinition="DEC(9,0)")
	private BigDecimal nochkItemD07;

	/** D07分數  **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="NOSCRITEMD07", columnDefinition="DEC(7,4)")
	private BigDecimal noscrItemD07;

	/** 近12個月新業務申請查詢總家數(三個月本行查詢不列入計算) **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="NOCHKITEMN06", columnDefinition="DEC(3,0)")
	private BigDecimal nochkItemN06;

	/** N06分數  **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="NOSCRITEMN06", columnDefinition="DEC(7,4)")
	private BigDecimal noscrItemN06;	

	/** 近6個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用之次數) **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="NOCHKITEMP68", columnDefinition="DEC(3,0)")
	private BigDecimal nochkItemP68;
	

	/** 近12個月信用卡繳款狀況出現全額繳清無延遲次數(不含無須繳款) **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="NOCHKITEMP19", columnDefinition="DEC(3,0)")
	private BigDecimal nochkItemP19;

	/** P68_P19分數  **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="NOSCRITEMP68P19", columnDefinition="DEC(7,4)")
	private BigDecimal noscrItemP68P19;
	
	/** P68_P19組別 **/
	@Size(max=12)
	@Column(name="GRPP68P19", length=12, columnDefinition="VARCHAR(12)")
	private String grpP68P19;
	
	/** 個人負債比率 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="NOCHKITEMDRATE", columnDefinition="DEC(5,2)")
	private BigDecimal nochkItemDrate;
	
	/** 個人負債比率_分數  **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="NOSCRITEMDRATE", columnDefinition="DEC(7,4)")
	private BigDecimal noscrItemDrate;
	
	/** P69因子 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="NOCHKITEMP69", columnDefinition="DEC(3,0)")
	private BigDecimal nochkItemP69;
	
	/** P69_P19分數  **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="NOSCRITEMP69P19", columnDefinition="DEC(7,4)")
	private BigDecimal noscrItemP69P19;
	
	/** P69_P19組別 **/
	@Size(max=12)
	@Column(name="GRPP69P19", length=12, columnDefinition="VARCHAR(12)")
	private String grpP69P19;
	
	/** R01因子  **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="NOCHKITEMR01", columnDefinition="DEC(7,4)")
	private BigDecimal nochkItemR01;
		
	/** R01分數  **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="NOSCRITEMR01", columnDefinition="DEC(7,4)")
	private BigDecimal noscrItemR01;
	
	/** P25因子 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="NOCHKITEMP25", columnDefinition="DEC(3,0)")
	private BigDecimal nochkItemP25;
	
	/** P25分數  **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="NOSCRITEMP25", columnDefinition="DEC(7,4)")
	private BigDecimal noscrItemP25;
	
	/** 職稱  **/
	@Column(name = "JOBTITLE", length = 1, columnDefinition = "CHAR(1)")
	private String jobTitle;

	/** 擔保品類別
	 */
	@Size(max = 2)
	@Column(name = "ISKIND", length = 2, columnDefinition = "CHAR(2)")
	private String isKind;
	
	/** ----------消金非房貸4.0 Start---------- **/
	/** 近 12 個月授信帳戶繳款狀況出現 0 的總次數，不含本行-因子 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="ITEMP01", columnDefinition="DECIMAL(5,2)")
	private BigDecimal itemP01;

	/** 近 12 個月授信帳戶繳款狀況出現 0 的總次數，不含本行-分數 **/
	@Digits(integer=10, fraction=4, groups = Check.class)
	@Column(name="SCRP01", columnDefinition="DECIMAL(10,4)")
	private BigDecimal scrP01;

	/** 目前有擔保餘額 ID 歸戶-因子 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="LOANBALSBYID", columnDefinition="DECIMAL(17,2)")
	private BigDecimal loanBalSByid;

	/** 目前有擔保餘額 ID 歸戶-分數 **/
	@Digits(integer=10, fraction=4, groups = Check.class)
	@Column(name="SCRLOANBALSBYID", columnDefinition="DECIMAL(10,4)")
	private BigDecimal scrLoanBalSByid;

	/** 近12個月信用卡(每筆)循環信用平均使用率-因子 **/
	@Column(name="ITEMMAXR01", columnDefinition="DECIMAL(10,4)")
	private BigDecimal itemMaxR01;

	/** 近12個月信用卡(每筆)循環信用平均使用率-分數 **/
	@Digits(integer=10, fraction=4, groups = Check.class)
	@Column(name="SCRMAXR01", columnDefinition="DECIMAL(10,4)")
	private BigDecimal scrMaxR01;
	
	/** 截距 **/
	@Digits(integer=6, fraction=4, groups = Check.class)
	@Column(name="INTERCEPT", columnDefinition="DECIMAL(6,4)")
	private BigDecimal interCept;

	/** 斜率 **/
	@Digits(integer=6, fraction=4, groups = Check.class)
	@Column(name="SLOPE", columnDefinition="DECIMAL(6,4)")
	private BigDecimal slope;
	/** ----------消金非房貸4.0 End---------- **/
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得編製單位代號<p/>
	 * 單位代碼
	 */
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/**
	 *  設定編製單位代號<p/>
	 *  單位代碼
	 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得借款人姓名 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定借款人姓名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得文件狀態 **/
	public String getDocStatus() {
		return this.docStatus;
	}
	/** 設定文件狀態 **/
	public void setDocStatus(String value) {
		this.docStatus = value;
	}

	/** 取得評等建立日期 **/
	public Date getGrdCDate() {
		return this.grdCDate;
	}
	/** 設定評等建立日期 **/
	public void setGrdCDate(Date value) {
		this.grdCDate = value;
	}

	/** 取得評等調整日期 **/
	public Date getGrdTDate() {
		return this.grdTDate;
	}
	/** 設定評等調整日期 **/
	public void setGrdTDate(Date value) {
		this.grdTDate = value;
	}

	/** 取得聯徵查詢日期 **/
	public Date getJcicQDate() {
		return this.jcicQDate;
	}
	/** 設定聯徵查詢日期 **/
	public void setJcicQDate(Date value) {
		this.jcicQDate = value;
	}

	/** 取得票信查詢日期 **/
	public Date getEtchQDate() {
		return this.etchQDate;
	}
	/** 設定票信查詢日期 **/
	public void setEtchQDate(Date value) {
		this.etchQDate = value;
	}

	/** 取得個人年收入（萬元） **/
	public BigDecimal getYPay() {
		return this.yPay;
	}
	/** 設定個人年收入（萬元） **/
	public void setYPay(BigDecimal value) {
		this.yPay = value;
	}

	/** 取得個人年收入分數 **/
	public BigDecimal getScrypay() {
		return this.scrypay;
	}
	/** 設定個人年收入分數 **/
	public void setScrypay(BigDecimal value) {
		this.scrypay = value;
	}

	/** 取得年資 **/
	public BigDecimal getSeniority() {
		return this.seniority;
	}
	/** 設定年資 **/
	public void setSeniority(BigDecimal value) {
		this.seniority = value;
	}

	/** 取得年資分數 **/
	public BigDecimal getScrseniority() {
		return this.scrseniority;
	}
	/** 設定年資分數 **/
	public void setScrseniority(BigDecimal value) {
		this.scrseniority = value;
	}

	/** 取得學歷 **/
	public String getEducation() {
		return this.education;
	}
	/** 設定學歷 **/
	public void setEducation(String value) {
		this.education = value;
	}

	/** 取得學歷分數 **/
	public BigDecimal getScreducation() {
		return this.screducation;
	}
	/** 設定學歷分數 **/
	public void setScreducation(BigDecimal value) {
		this.screducation = value;
	}

	/** 
	 * 取得聯徵查詢月份當時無擔保授信往來家數(排除學生助學貸款(Z)、本行貸款)<p/>
	 * d63_ln_nos_bank
	 */
	public BigDecimal getNochkItem01() {
		return this.nochkItem01;
	}
	/**
	 *  設定聯徵查詢月份當時無擔保授信往來家數(排除學生助學貸款(Z)、本行貸款)<p/>
	 *  d63_ln_nos_bank
	 **/
	public void setNochkItem01(BigDecimal value) {
		this.nochkItem01 = value;
	}

	/** 取得聯徵查詢月份當時無擔保授信往來家數(排除學生助學貸款(Z)、本行貸款)分數 **/
	public BigDecimal getNoscrItem01() {
		return this.noscrItem01;
	}
	/** 設定聯徵查詢月份當時無擔保授信往來家數(排除學生助學貸款(Z)、本行貸款)分數 **/
	public void setNoscrItem01(BigDecimal value) {
		this.noscrItem01 = value;
	}

	/** 
	 * 取得近6個月信用卡使用循環信用的月份數<p/>
	 * a21_cc6_rc_use_month
	 */
	public BigDecimal getNochkItem02() {
		return this.nochkItem02;
	}
	/**
	 *  設定近6個月信用卡使用循環信用的月份數<p/>
	 *  a21_cc6_rc_use_month
	 **/
	public void setNochkItem02(BigDecimal value) {
		this.nochkItem02 = value;
	}

	/** 取得近6個月信用卡使用循環信用的月份數分數 **/
	public BigDecimal getNoscrItem02() {
		return this.noscrItem02;
	}
	/** 設定近6個月信用卡使用循環信用的月份數分數 **/
	public void setNoscrItem02(BigDecimal value) {
		this.noscrItem02 = value;
	}

	/** 
	 * 取得近6個月信用卡使用循環信用的家數<p/>
	 * a11_cc6_rc_use_bank
	 */
	public BigDecimal getNochkItem03() {
		return this.nochkItem03;
	}
	/**
	 *  設定近6個月信用卡使用循環信用的家數<p/>
	 *  a11_cc6_rc_use_bank
	 **/
	public void setNochkItem03(BigDecimal value) {
		this.nochkItem03 = value;
	}

	/** 取得近6個月信用卡使用循環信用的家數分數 **/
	public BigDecimal getNoscrItem03() {
		return this.noscrItem03;
	}
	/** 設定近6個月信用卡使用循環信用的家數分數 **/
	public void setNoscrItem03(BigDecimal value) {
		this.noscrItem03 = value;
	}

	/** 
	 * 取得聯徵查詢月份當時授信繳款記錄小於等於6次旗標<p/>
	 * d53_ln_6_times_flag
	 */
	public BigDecimal getNochkItem04() {
		return this.nochkItem04;
	}
	/**
	 *  設定聯徵查詢月份當時授信繳款記錄小於等於6次旗標<p/>
	 *  d53_ln_6_times_flag
	 **/
	public void setNochkItem04(BigDecimal value) {
		this.nochkItem04 = value;
	}

	/** 取得聯徵查詢月份當時授信繳款記錄小於等於6次旗標分數 **/
	public BigDecimal getNoscrItem04() {
		return this.noscrItem04;
	}
	/** 設定聯徵查詢月份當時授信繳款記錄小於等於6次旗標分數 **/
	public void setNoscrItem04(BigDecimal value) {
		this.noscrItem04 = value;
	}

	/** 取得組別 **/
	public String getGrpNum10() {
		return this.grpNum10;
	}
	/** 設定組別 **/
	public void setGrpNum10(String value) {
		this.grpNum10 = value;
	}

	/** 取得合計變量得分 **/
	public BigDecimal getScrNum11() {
		return this.scrNum11;
	}
	/** 設定合計變量得分 **/
	public void setScrNum11(BigDecimal value) {
		this.scrNum11 = value;
	}

	/** 
	 * 取得基準底分<p/>
	 * A + B ＊ 常數項
	 */
	public BigDecimal getScrNum12() {
		return this.scrNum12;
	}
	/**
	 *  設定基準底分<p/>
	 *  A + B ＊ 常數項
	 **/
	public void setScrNum12(BigDecimal value) {
		this.scrNum12 = value;
	}

	/** 
	 * 取得初始評分<p/>
	 * scrNum11 + scrNum12
	 */
	public BigDecimal getScrNum13() {
		return this.scrNum13;
	}
	/**
	 *  設定初始評分<p/>
	 *  scrNum11 + scrNum12
	 **/
	public void setScrNum13(BigDecimal value) {
		this.scrNum13 = value;
	}

	/** 
	 * 取得初始評等<p/>
	 * 初始評分對照的等級
	 */
	public String getGrade1() {
		return this.grade1;
	}
	/**
	 *  設定初始評等<p/>
	 *  初始評分對照的等級
	 **/
	public void setGrade1(String value) {
		this.grade1 = value;
	}

	/** 
	 * 取得調整評等<p/>
	 * 人工調整
	 */
	public String getGrade2() {
		return this.grade2;
	}
	/**
	 *  設定調整評等<p/>
	 *  人工調整
	 **/
	public void setGrade2(String value) {
		this.grade2 = value;
	}

	/** 取得最終評等 **/
	public String getGrade3() {
		return this.grade3;
	}
	/** 設定最終評等 **/
	public void setGrade3(String value) {
		this.grade3 = value;
	}

	/** 
	 * 取得預測壞率<p/>
	 * 計算：小數點4 位，四捨五入<br/>
	 *  (1/(1+Exp(scrNum13 – A/B)))
	 */
	public BigDecimal getPd() {
		return this.pd;
	}
	/**
	 *  設定預測壞率<p/>
	 *  計算：小數點4 位，四捨五入<br/>
	 *  (1/(1+Exp(scrNum13 – A/B)))
	 **/
	public void setPd(BigDecimal value) {
		this.pd = value;
	}

	/** 
	 * 取得有退票、拒往、信用卡強停或催收呆帳紀錄<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem1() {
		return this.chkItem1;
	}
	/**
	 *  設定有退票、拒往、信用卡強停或催收呆帳紀錄<p/>
	 *  票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem1(String value) {
		this.chkItem1 = value;
	}

	/** 取得退票 **/
	public String getChkItem1a() {
		return this.chkItem1a;
	}
	/** 設定退票 **/
	public void setChkItem1a(String value) {
		this.chkItem1a = value;
	}

	/** 取得拒往 **/
	public String getChkItem1b() {
		return this.chkItem1b;
	}
	/** 設定拒往 **/
	public void setChkItem1b(String value) {
		this.chkItem1b = value;
	}

	/** 取得信用卡強停 **/
	public String getChkItem1c() {
		return this.chkItem1c;
	}
	/** 設定信用卡強停 **/
	public void setChkItem1c(String value) {
		this.chkItem1c = value;
	}

	/** 取得催收呆帳 **/
	public String getChkItem1d() {
		return this.chkItem1d;
	}
	/** 設定催收呆帳 **/
	public void setChkItem1d(String value) {
		this.chkItem1d = value;
	}
	
	/** 取得逾期放款 **/
	public String getChkItem1e() {
		return this.chkItem1e;
	}

	/** 設定逾期放款 **/
	public void setChkItem1e(String value) {
		this.chkItem1e = value;
	}

	/** 
	 * 取得有消債條例信用註記、銀行公會債務協商註記或其他補充註記<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem2() {
		return this.chkItem2;
	}
	/**
	 *  設定有消債條例信用註記、銀行公會債務協商註記或其他補充註記<p/>
	 *  票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem2(String value) {
		this.chkItem2 = value;
	}

	/** 取得消債條例信用註記 **/
	public String getChkItem2a() {
		return this.chkItem2a;
	}
	/** 設定消債條例信用註記 **/
	public void setChkItem2a(String value) {
		this.chkItem2a = value;
	}

	/** 取得銀行公會債務協商註記 **/
	public String getChkItem2b() {
		return this.chkItem2b;
	}
	/** 設定銀行公會債務協商註記 **/
	public void setChkItem2b(String value) {
		this.chkItem2b = value;
	}

	/** 取得其他補充註記 **/
	public String getChkItem2c() {
		return this.chkItem2c;
	}
	/** 設定其他補充註記 **/
	public void setChkItem2c(String value) {
		this.chkItem2c = value;
	}

	/** 
	 * 取得近12個月授信帳戶出現延遲二次（含）以上<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem3() {
		return this.chkItem3;
	}
	/**
	 *  設定近12個月授信帳戶出現延遲二次（含）以上<p/>
	 *  票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem3(String value) {
		this.chkItem3 = value;
	}

	/** 
	 * 取得近12個月信用卡繳款狀況出現（循環信用有延遲）二次（含）以上<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem4() {
		return this.chkItem4;
	}
	/**
	 *  設定近12個月信用卡繳款狀況出現（循環信用有延遲）二次（含）以上<p/>
	 *  票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem4(String value) {
		this.chkItem4 = value;
	}

	/** 
	 * 取得近12個月信用卡繳款狀況出現（未繳足最低金額）二次（含）以上<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem5() {
		return this.chkItem5;
	}
	/**
	 *  設定近12個月信用卡繳款狀況出現（未繳足最低金額）二次（含）以上<p/>
	 *  票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem5(String value) {
		this.chkItem5 = value;
	}

	/** 
	 * 取得近12個月信用卡繳款狀況出現（全額逾期連續未繳）二次（含）以上<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem6() {
		return this.chkItem6;
	}
	/**
	 *  設定近12個月信用卡繳款狀況出現（全額逾期連續未繳）二次（含）以上<p/>
	 *  票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem6(String value) {
		this.chkItem6 = value;
	}

	/** 
	 * 取得近12個月信用卡有預借現金餘額二次（含）以上<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem7() {
		return this.chkItem7;
	}
	/**
	 *  設定近12個月信用卡有預借現金餘額二次（含）以上<p/>
	 *  票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem7(String value) {
		this.chkItem7 = value;
	}

	/** 
	 * 取得近12個月現金卡有動用紀錄<p/>
	 * 票交所與聯徵特殊負面資訊
	 */
	public String getChkItem8() {
		return this.chkItem8;
	}
	/**
	 *  設定近12個月現金卡有動用紀錄<p/>
	 *  票交所與聯徵特殊負面資訊
	 **/
	public void setChkItem8(String value) {
		this.chkItem8 = value;
	}

	/** 取得基準底分A **/
	public BigDecimal getVarA() {
		return this.varA;
	}
	/** 設定基準底分A **/
	public void setVarA(BigDecimal value) {
		this.varA = value;
	}

	/** 取得基準底分B **/
	public BigDecimal getVarB() {
		return this.varB;
	}
	/** 設定基準底分B **/
	public void setVarB(BigDecimal value) {
		this.varB = value;
	}

	/** 取得基準底分常數項 **/
	public BigDecimal getVarC() {
		return this.varC;
	}
	/** 設定基準底分常數項 **/
	public void setVarC(BigDecimal value) {
		this.varC = value;
	}

	/** 取得基準底分版本 **/
	public String getVarVer() {
		return this.varVer;
	}
	/** 設定基準底分版本 **/
	public void setVarVer(String value) {
		this.varVer = value;
	}

	/** 取得報表亂碼 **/
	public String getRandomCode() {
		return this.randomCode;
	}
	/** 設定報表亂碼 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/** 
	 * 取得調整狀態<p/>
	 * 1.調升 2.調降 3.回復
	 */
	public String getAdjustStatus() {
		return this.adjustStatus;
	}
	/**
	 *  設定調整狀態<p/>
	 *  1.調升 2.調降 3.回復
	 **/
	public void setAdjustStatus(String value) {
		this.adjustStatus = value;
	}

	/** 
	 * 取得調整註記(升等)<p/>
	 * 1.淨資產2.職業3.其它
	 */
	public String getAdjustFlag() {
		return this.adjustFlag;
	}
	/**
	 *  設定調整註記(升等)<p/>
	 *  1.淨資產2.職業3.其它
	 **/
	public void setAdjustFlag(String value) {
		this.adjustFlag = value;
	}

	/** 取得調整理由 **/
	public String getAdjustReason() {
		return this.adjustReason;
	}
	/** 設定調整理由 **/
	public void setAdjustReason(String value) {
		this.adjustReason = value;
	}

	/** 
	 * 取得未持有信用卡<p/>
	 * Y.是
	 */
	public String getCardFlag() {
		return this.cardFlag;
	}
	/**
	 *  設定未持有信用卡<p/>
	 *  Y.是
	 **/
	public void setCardFlag(String value) {
		this.cardFlag = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得是否引用<p/>
	 * Y.是
	 */
	public String getQuote() {
		return this.quote;
	}
	/**
	 *  設定是否引用<p/>
	 *  Y.是
	 **/
	public void setQuote(String value) {
		this.quote = value;
	}
	
	/**
	 * 取得違約機率(預估2年期)-短
	 */
	public BigDecimal getDr_2YR() {
		return this.dr_2YR;
	}

	/**
	 * 設定違約機率(預估2年期)-短
	 **/
	public void setDr_2YR(BigDecimal value) {
		this.dr_2YR = value;
	}
	
	/**
	 * 取得違約機率(預估3年期)-中長
	 */
	public BigDecimal getDr_3YR() {
		return this.dr_3YR;
	}

	/**
	 * 設定違約機率(預估3年期)-中長
	 **/
	public void setDr_3YR(BigDecimal value) {
		this.dr_3YR = value;
	}
	
	/**
	 * 取得違約機率(預估1年期)-短
	 */
	public BigDecimal getDr_1YR_S() {
		return this.dr_1YR_S;
	}

	/**
	 * 設定違約機率(預估1年期)-短
	 **/
	public void setDr_1YR_S(BigDecimal value) {
		this.dr_1YR_S = value;
	}
	
	/**
	 * 取得違約機率(預估1年期)-中長
	 */
	public BigDecimal getDr_1YR_L() {
		return this.dr_1YR_L;
	}

	/**
	 * 設定違約機率(預估1年期)-中長
	 **/
	public void setDr_1YR_L(BigDecimal value) {
		this.dr_1YR_L = value;
	}

	public BigDecimal getPIncome() {
		return pIncome;
	}
	public void setPIncome(BigDecimal pIncome) {
		this.pIncome = pIncome;
	}
	public BigDecimal getScrPIncome() {
		return scrPIncome;
	}
	public void setScrPIncome(BigDecimal scrPIncome) {
		this.scrPIncome = scrPIncome;
	}
	public BigDecimal getNochkItemD07() {
		return nochkItemD07;
	}
	public void setNochkItemD07(BigDecimal nochkItemD07) {
		this.nochkItemD07 = nochkItemD07;
	}
	public BigDecimal getNoscrItemD07() {
		return noscrItemD07;
	}
	public void setNoscrItemD07(BigDecimal noscrItemD07) {
		this.noscrItemD07 = noscrItemD07;
	}
	public BigDecimal getNochkItemN06() {
		return nochkItemN06;
	}
	public void setNochkItemN06(BigDecimal nochkItemN06) {
		this.nochkItemN06 = nochkItemN06;
	}
	public BigDecimal getNoscrItemN06() {
		return noscrItemN06;
	}
	public void setNoscrItemN06(BigDecimal noscrItemN06) {
		this.noscrItemN06 = noscrItemN06;
	}
	public BigDecimal getNochkItemP68() {
		return nochkItemP68;
	}
	public void setNochkItemP68(BigDecimal nochkItemP68) {
		this.nochkItemP68 = nochkItemP68;
	}
	public BigDecimal getNochkItemP19() {
		return nochkItemP19;
	}
	public void setNochkItemP19(BigDecimal nochkItemP19) {
		this.nochkItemP19 = nochkItemP19;
	}
	public BigDecimal getNoscrItemP68P19() {
		return noscrItemP68P19;
	}
	public void setNoscrItemP68P19(BigDecimal noscrItemP68P19) {
		this.noscrItemP68P19 = noscrItemP68P19;
	}
	public String getGrpP68P19() {
		return grpP68P19;
	}
	public void setGrpP68P19(String grpP68P19) {
		this.grpP68P19 = grpP68P19;
	}
	
	public BigDecimal getNochkItemDrate() {
		return nochkItemDrate;
	}
	public void setNochkItemDrate(BigDecimal nochkItemDrate) {
		this.nochkItemDrate = nochkItemDrate;
	}
	public BigDecimal getNoscrItemDrate() {
		return noscrItemDrate;
	}
	public void setNoscrItemDrate(BigDecimal noscrItemDrate) {
		this.noscrItemDrate = noscrItemDrate;
	}
	public BigDecimal getNochkItemP69() {
		return nochkItemP69;
	}
	public void setNochkItemP69(BigDecimal nochkItemP69) {
		this.nochkItemP69 = nochkItemP69;
	}
	public BigDecimal getNoscrItemP69P19() {
		return noscrItemP69P19;
	}
	public void setNoscrItemP69P19(BigDecimal noscrItemP69P19) {
		this.noscrItemP69P19 = noscrItemP69P19;
	}
	public String getGrpP69P19() {
		return grpP69P19;
	}
	public void setGrpP69P19(String grpP69P19) {
		this.grpP69P19 = grpP69P19;
	}
	public BigDecimal getNochkItemR01() {
		return nochkItemR01;
	}
	public void setNochkItemR01(BigDecimal nochkItemR01) {
		this.nochkItemR01 = nochkItemR01;
	}
	public BigDecimal getNoscrItemR01() {
		return noscrItemR01;
	}
	public void setNoscrItemR01(BigDecimal noscrItemR01) {
		this.noscrItemR01 = noscrItemR01;
	}
	public BigDecimal getNochkItemP25() {
		return nochkItemP25;
	}
	public void setNochkItemP25(BigDecimal nochkItemP25) {
		this.nochkItemP25 = nochkItemP25;
	}
	public BigDecimal getNoscrItemP25() {
		return noscrItemP25;
	}
	public void setNoscrItemP25(BigDecimal noscrItemP25) {
		this.noscrItemP25 = noscrItemP25;
	}

	public String getJobTitle() {
		return jobTitle;
	}
	public void setJobTitle(String jobTitle) {
		this.jobTitle = jobTitle;
	}

	/** 取得擔保品類別 **/
	public String getIsKind() {
		return isKind;
	}
	/** 設定擔保品類別 **/
	public void setIsKind(String isKind) {
		this.isKind = isKind;
	}
	
	/** ----------消金非房貸4.0 Start---------- **/
	/** 取得近 12 個月授信帳戶繳款狀況出現 0 的總次數，不含本行-因子 **/
	public BigDecimal getItemP01() {
		return this.itemP01;
	}
	/** 設定近 12 個月授信帳戶繳款狀況出現 0 的總次數，不含本行-因子 **/
	public void setItemP01(BigDecimal value) {
		this.itemP01 = value;
	}

	/** 取得近 12 個月授信帳戶繳款狀況出現 0 的總次數，不含本行-分數 **/
	public BigDecimal getScrP01() {
		return this.scrP01;
	}
	/** 設定近 12 個月授信帳戶繳款狀況出現 0 的總次數，不含本行-分數 **/
	public void setScrP01(BigDecimal value) {
		this.scrP01 = value;
	}

	/** 取得目前有擔保餘額 ID 歸戶-因子 **/
	public BigDecimal getLoanBalSByid() {
		return this.loanBalSByid;
	}
	/** 設定目前有擔保餘額 ID 歸戶-因子 **/
	public void setLoanBalSByid(BigDecimal value) {
		this.loanBalSByid = value;
	}

	/** 取得目前有擔保餘額 ID 歸戶-分數 **/
	public BigDecimal getScrLoanBalSByid() {
		return this.scrLoanBalSByid;
	}
	/** 設定目前有擔保餘額 ID 歸戶-分數 **/
	public void setScrLoanBalSByid(BigDecimal value) {
		this.scrLoanBalSByid = value;
	}

	/** 取得近12個月信用卡(每筆)循環信用平均使用率-因子 **/
	public BigDecimal getItemMaxR01() {
		return this.itemMaxR01;
	}
	/** 設定近12個月信用卡(每筆)循環信用平均使用率-因子 **/
	public void setItemMaxR01(BigDecimal value) {
		this.itemMaxR01 = value;
	}

	/** 取得近12個月信用卡(每筆)循環信用平均使用率-分數 **/
	public BigDecimal getScrMaxR01() {
		return this.scrMaxR01;
	}
	/** 設定近12個月信用卡(每筆)循環信用平均使用率-分數 **/
	public void setScrMaxR01(BigDecimal value) {
		this.scrMaxR01 = value;
	}
	
	/** 取得截距 **/
	public BigDecimal getInterCept() {
		return this.interCept;
	}
	/** 設定截距 **/
	public void setInterCept(BigDecimal value) {
		this.interCept = value;
	}

	/** 取得斜率 **/
	public BigDecimal getSlope() {
		return this.slope;
	}
	/** 設定斜率 **/
	public void setSlope(BigDecimal value) {
		this.slope = value;
	}
	/** ----------消金非房貸4.0 End---------- **/

	/**
	 * join
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C101M01A c101m01a;

	public void setC101m01a(C101M01A c101m01a) {
		this.c101m01a = c101m01a;
	}

	public C101M01A getC101m01a() {
		return c101m01a;
	}

	/**
	 * 顯示用欄位
	 */
	/** 票交所與聯徵特殊負面資訊 **/
	@Transient
	private String alertMsg;

	public String getAlertMsg() {
		return this.alertMsg;
	}

	public void setAlertMsg(String value) {
		this.alertMsg = value;
	}
}
