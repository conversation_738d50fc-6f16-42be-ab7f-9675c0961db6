package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.pages.AbstractEloanInnerView;

@Controller@RequestMapping(path = "/fms/lms9031v00")
public class LMS9031V00Page extends AbstractEloanInnerView {

	public LMS9031V00Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(FlowDocStatusEnum.DOC_EDITING);

		// UPGRADE: 後續沒有用到就可以刪掉
		// add(new Label("_buttonPanel"));
		renderJsI18N(LMS9031V00Page.class);
	}// ;

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS9031V00Page.js" };
	}
}
