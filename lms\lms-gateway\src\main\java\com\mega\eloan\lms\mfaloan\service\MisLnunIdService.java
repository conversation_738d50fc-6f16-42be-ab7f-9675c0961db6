package com.mega.eloan.lms.mfaloan.service;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

public interface MisLnunIdService {

	/**
	 * 借款人明細婉卻紀錄查詢
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 借款人明細婉卻紀錄
	 */
	public List<Map<String, Object>> findReject(String custId, String dupNo);

	/**
	 * 本行婉卻紀錄查詢 查詢
	 * 
	 * @param custId
	 *            客戶統編
	 * 
	 * @return 本行婉卻紀錄同一客戶統編不同重覆序號筆數
	 */
	public List<Map<String, Object>> findLnunIdByCustIdCount(String custId);

	/**
	 * 本行婉卻紀錄查詢
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 本行婉卻紀錄
	 */
	public List<Map<String, Object>> findLnunIdByCustId(String custId,
			String dupNo);

	/**
	 * 金控婉卻記錄
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 金控婉卻記錄
	 */
	public List<Map<String, Object>> findLnunId02ByCustId(String custId,
			String dupNo);

	public static final String[] LnunidCols = { "CUSTID", "DUPNO", "REGDT",
			"REGBR", "REGTELLER", "REFUSECD", "REFUSEDS", "RFSAUTH", "UPDATER",
			"TMESTAMP", "CLSCASE", "CARDREJ", "CUSTNM" };

	public static final SimpleDateFormat df = new SimpleDateFormat(
			"yyyy-MM-dd HH:mm");

	public abstract Map<String, Object> findByIdDup(String paramString1,
			String paramString2);

	/**
	 * 新增本行婉卻紀錄查詢
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param REGDT
	 *            系統時間
	 * @param REGBR
	 *            簽案行
	 * @param REGTELLER
	 *            執行人員
	 * @param REFUSECD
	 *            婉卻原因代號
	 * @param REFUSEDS
	 *            婉卻原因描述
	 * @param RFSAUTH
	 * @param UPDATER
	 *            更新人員
	 * @param CLSCASE
	 *            案件類型
	 * @param CARDREJ
	 * @param CUSTNM
	 *            客戶姓名
	 * @param STATUSCD
	 * @param OID
	 *            案號
	 */
	public void insertLnunId(String custId, String dupNo, Timestamp REGDT,
			String REGBR, String REGTELLER, int REFUSECD, String REFUSEDS,
			String RFSAUTH, String UPDATER, Timestamp TMESTAMP, String CLSCASE,
			String CARDREJ, String CUSTNM, String STATUSCD, String OID);

	/**
	 * 更新本行婉卻紀錄查詢
	 * 
	 * @param custId
	 *            統編
	 * @param dupNo
	 *            重覆序號
	 * @param rejtCase
	 *            婉卻種類代碼
	 * @param caseNo
	 *            案號
	 */
	public void updateLunId(String custId, String dupNo, String rejtCase,
			String caseNo);

	/**
	 * 借款人明細婉卻紀錄查詢
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 借款人明細婉卻紀錄
	 */
	public Map<String, Object> queryReject(String custId, String dupNo);
	
	public List<Map<String, Object>> findRefusedRecordOfFinancialHolding(String custId, String dupNo);

}
