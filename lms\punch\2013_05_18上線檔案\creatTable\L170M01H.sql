---------------------------------------------------------
-- LMS.L170M01H 覆審借款人及連保人基本資料檔
---------------------------------------------------------

--DROP TABLE LMS.L170M01H;

CREATE TABLE LMS.L170M01H (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	CUSTID        VARCHAR(10)   not null,
	<PERSON><PERSON><PERSON><PERSON>         CHAR(1)       not null,
	DEBTYPE       CHAR(1)       not null,
	DEBID         VARCHAR(10)   not null,
	<PERSON>B<PERSON><PERSON><PERSON><PERSON>      CHAR(1)       not null,
	CUSTNAME      VARCHAR(120) ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATE<PERSON><PERSON>    TIMESTAMP    ,

  constraint P_L170M01H  PRIMARY KEY  (OID)
) in EL_DATA_4KTS index in EL_INDEX_4KTS ;

--DROP INDEX LMS.XL170M01H01;
CREATE UNIQUE INDEX LMS.XL170M01H01 ON LMS.L170M01H   (MAINID, CUSTID, DUPNO, DEBTYPE, DEBID,DEBDUPNO);

COMMENT ON TABLE LMS.L170M01H IS '覆審借款人及連保人基本資料檔';
COMMENT ON LMS.L170M01H (
	OID           IS 'oid', 
	MAINID        IS '文件編號',
	CUSTID        IS '身分證統編', 
	DUPNO         IS '身分證統編重複碼',
	DEBTYPE       IS '債務人種類 1.借款人, 2.連保人',
	DEBID         IS '債務人ID',
	DEBDUPNO      IS '債務人重複碼',
	CUSTNAME      IS '借款人姓名', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
