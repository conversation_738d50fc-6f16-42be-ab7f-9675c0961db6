<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="innerPageBody">
        	 <script type="text/javascript">loadScript('pagejs/ctr/LMS9990Common');</script>
        	  <script type="text/javascript">loadScript('pagejs/ctr/LMS9990M04Page');</script>
            <div class="button-menu funcContainer" id="buttonPanel">
                <button type="button" id="btnSave">
                    <span class="ui-icon ui-icon-jcs-04" ></span><th:block th:text="#{'button.save'}"><!--儲存--></th:block>
                </button>
                <button type="button" id="btnPrint">
                    <span class="ui-icon ui-icon-jcs-03"></span>
                    <th:block th:text="#{'button.print'}">列印</th:block>
                </button>
				<button id="btnQuery"  class="forview">
                	<span class="ui-icon ui-icon-jcs-102"></span>
					<th:block th:text="#{'button.queryByRate'}"><!--查詢利率--></th:block>
				</button>
                <button id="btnExit" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <th:block th:text="#{'button.exit'}"><!--離開--></th:block>
                </button>
            </div>
            <form id="ActionMForm">
                <div id="showTitle">
                    <div class=" tit2 color-black">
                        <th:block th:text="#{'L999M01AM04.showTitle01'}"><!--種類--></th:block>：<span name="contractType" id="contractType" class="color-blue"></span>
                        <br/>
                        <th:block th:text="#{'L999M01AM04.showTitle02'}"><!--客戶名稱--></th:block>：<span name="custData" id="custData" class="color-blue"></span>
                        <br/>
                        <th:block th:text="#{'L999M01AM04.showTitle03'}"><!--編      號--></th:block>：
                        <input name="contractNo" id="contractNo" type="text" size="20" class="alphanum" maxlength="20"/>
                    </div>
                </div>
                <div class="tabCtx-warp">
                    <fieldset>
                        <legend>
                            <b><th:block th:text="#{'L999M01AM04.title01'}"><!--基本資料--></th:block></b>
                        </legend>
                        <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td width="35%" class="hd1">
                                        <th:block th:text="#{'L999M01AM04.showTitle04'}"><!--  立約人--></th:block>&nbsp;&nbsp;
                                    </td>
                                    <td width="65%">
                                        <span name="custData2" id="custData2"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="35%" class="hd1">
                                        <th:block th:text="#{'L999M01AM04.showTitle05'}"><!--  資料保密--></th:block>&nbsp;&nbsp;
                                    </td>
                                    <td width="65%">
                                        <th:block th:text="#{'L999M01AM04.content01'}"><!--  立約人--></th:block>
                                        <label>
                                            <input type="radio" id="dataUseFlagY" name="dataUseFlag" value="Y"/>
                                            <th:block th:text="#{'L999M01AM04.content02'}"><!--  同意--></th:block>
                                        </label>&nbsp;<label>
                                            <input type="radio" id="dataUseFlagN" name="dataUseFlag" value="N"/>
                                            <th:block th:text="#{'L999M01AM04.content03'}"><!--  不同意--></th:block>
                                        </label>&nbsp;<th:block th:text="#{'L999M01AM04.content04'}"><!--  銀行得將連保人帳務、信用、投資及保險等資料，在合於營業登記項目或基於業務需要並遵守銀行所屬之金融控股公司及其各子公司(同意者請勾選)：--></th:block>：
                                        <br/>
                                        <input type="checkbox" id="dataUseItem" name="dataUseItem"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="35%" class="hd1">
                                        <th:block th:text="#{'L999M01AM04.showTitle06'}"><!-- 管轄法院--></th:block>&nbsp;&nbsp;
                                    </td>
                                    <td width="65%">
                                        <select id="courtCode" name="courtCode" comboKey="taiwancourt" combotype="2" space="true"></select>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="35%" class="hd1">
                                        <th:block th:text="#{'L999M01AM04.showTitle07'}"><!-- 特別條款--></th:block>&nbsp;&nbsp;
                                    </td>
                                    <td width="65%">
                                        <textarea id="itemContent" name="itemContent" maxlengthC="256" class="txt_mult" cols="102" rows="5" style="line-height: 20px;"></textarea>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </fieldset>
                </div>
            </form>
		</th:block>
    </body>
</html>
