package com.mega.eloan.lms.dao;

import com.mega.eloan.lms.model.C120S01W;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

/** 收入明細表 **/
public interface C120S01WDao extends IGenericDao<C120S01W> {

	C120S01W findByOid(String oid);

	List<C120S01W> findByMainId(String mainId);

	List<C120S01W> findByList(String mainId, String custId, String dupNo);

	List<C120S01W> findByList(String mainId, String custId, String dupNo, String[] incomeItemArray);
}