package com.mega.eloan.lms.mfaloan.bean;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 覆審控制檔 **/
public class ELF412B extends GenericBean {
	/**
	 * 分行代號
	 */
	@Column(name = "ELF412B_BRANCH", length = 3, columnDefinition = "CHAR(3)", nullable = false, unique = true)
	private String elf412b_branch;

	/**
	 * 借款人統一編號
	 */
	@Column(name = "ELF412B_CUSTID", length = 10, columnDefinition = "CHAR(10)", nullable = false, unique = true)
	private String elf412b_custId;

	/**
	 * 重複序號
	 */
	@Column(name = "ELF412B_DUPNO", length = 1, columnDefinition = "CHAR(1)", nullable = false, unique = true)
	private String elf412b_dupNo;

	/**
	 * 主要授信戶 <br/>
	 * 1.新戶增額由ELF411 <br/>
	 * 2.舊案由STORE PROCEDURE
	 */
	@Column(name = "ELF412B_MAINCUST", length = 1, columnDefinition = "CHAR(1)")
	private String elf412b_mainCust;

	/**
	 * 資信評等類別 <br/>
	 * B=DBU、大型企業、L=DBU中小型企業、O=OBU
	 */
	@Column(name = "ELF412B_CRDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf412b_crdType;

	/**
	 * 資信評等 <br/>
	 * MIS.CRDTTBL
	 */
	@Column(name = "ELF412B_CRDTTBL", length = 2, columnDefinition = "CHAR(2)")
	private String elf412b_crdtTbl;

	/**
	 * 信用模型評等類別 <br/>
	 * 1:DBU大型企業 2:DBU中型企業 3:DBU中小型企業 4:DBU不動產有建案規劃 5:DBU專案融資 6:DBU本國證券公司
	 * 8:DBU投資公司一般情況 9:DBU租賃公司 A:DBU一案建商 B:DBU非一案建商(擔保/土融) C:DBU非一案建商(無擔)
	 * D:投資公司情況一 E:投資公司情況二 F:境外船舶與航空器 G:境外貿易型控股型 H:國金部實體營運企業 I:國金部租賃業
	 * J:信託、基金及其他金融工具 K:澳洲不動產租售業
	 * 
	 * <br/>
	 * 參考 select substr(CODEVALUE, 2, 3) as mowType,CODEDESC from com.bcodetype
	 * where codetype='CRDType' and CODEVALUE like 'M%' and LOCALE='zh_TW' <br/>
	 * 參考 UtilConstants.mowType
	 */
	@Column(name = "ELF412B_MOWTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf412b_mowType;

	/**
	 * 信用模型評等 <br/>
	 * MIS.MOWTBL1
	 */
	@Column(name = "ELF412B_MOWTBL1", length = 2, columnDefinition = "CHAR(2)")
	private String elf412b_mowTbl1;

	/**
	 * 上上次覆審日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412B_LLRDATE", columnDefinition = "DATE")
	private Date elf412b_llrDate;

	/**
	 * 上次覆審日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412B_LRDATE", columnDefinition = "DATE")
	private Date elf412b_lrDate;

	/**
	 * 異常週期 <br/>
	 * A.一年覆審一次。 <br/>
	 * B.半年覆審一次(主要授信戶並符合信評條件)。 <br/>
	 * C.新戶/增額戶。 <br/>
	 * D.異常戶已三個月覆審過- 爾後半年覆審一次。 <br/>
	 * E.首次通報之異常戶。（必需在首次通報日後3月內覆審） <br/>
	 * F.會計師出具保留意見已三個月覆審過- 爾後半年覆審一次。 <br/>
	 * G.首次通報有會計師出具保留意見之異常戶。（必需在首次通報日後3月內覆審） <br/>
	 * H.主管機關指定覆審案件。
	 */
	@Column(name = "ELF412B_RCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elf412b_rckdLine;

	/**
	 * 原始週期 <br/>
	 * 如果沒有異常通報、增額、主管機關指定時之週期，只會為A或B
	 */
	@Column(name = "ELF412B_OCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elf412b_ockdLine;

	/**
	 * 戶況 <br/>
	 * 0.無餘額 <br/>
	 * 1.正常 <br/>
	 * 2.逾期 <br/>
	 * 3.催收 <br/>
	 * 4.呆帳(該戶項下最嚴重的代碼)
	 */
	@Column(name = "ELF412B_CSTATE", length = 1, columnDefinition = "CHAR(1)")
	private String elf412b_cState;

	/**
	 * 新作/增額 <br/>
	 * N.新做/ C.增貸 <br/>
	 * 來自 ELF411_NEWADD {N:新作 , C:增額, R:逾放轉正} <br/>
	 * 參考 UtilConstants.NEWADD
	 */
	@Column(name = "ELF412B_NEWADD", length = 1, columnDefinition = "CHAR(1)")
	private String elf412b_newAdd;

	/**
	 * 新作增額資料日期 <br/>
	 * ELF411_DATAYM 之資料
	 */
	@Column(name = "ELF412B_NEWDATE", length = 6, columnDefinition = "CHAR(6)")
	private String elf412b_newDate;

	/**
	 * 不覆審代碼 <br/>
	 * 1.本行或同業主辦之聯貸案件，非擔任管理行。 <br/>
	 * 2.十成定存。 <br/>
	 * 3.純進出押戶。 <br/>
	 * 4.對政府或政府所屬機關、學校之授信案件。 <br/>
	 * 5.拆放同業或對同業之融通。 <br/>
	 * 6.已列報為逾期放款或轉列催收款項之案件。 <br/>
	 * 7.銷戶 <br/>
	 * 8.本次暫不覆審 <br/>
	 * 9.已專案核准免辦理覆審之房屋仲介價金履約保證案件。
	 * 10.企業戶之外勞保證中長期授信案件，除於新作後辦理一次覆審外，免再辦理覆審，但嗣後如有增額、
	 * 減額、變更條件或續約時，仍應依本要點第五條規定辦理一次覆審
	 */
	@Column(name = "ELF412B_NCKDFLAG", length = 2, columnDefinition = "CHAR(2)")
	private String elf412b_nckdFlag;

	/**
	 * 不覆審日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412B_NCKDDATE", columnDefinition = "DATE")
	private Date elf412b_nckdDate;

	/**
	 * 不覆審備註
	 */
	@Column(name = "ELF412B_NCKDMEMO", length = 202, columnDefinition = "CHAR(202)")
	private String elf412b_nckdMemo;

	/**
	 * 銷戶日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412B_CANCELDT", columnDefinition = "DATE")
	private Date elf412b_cancelDt;

	/**
	 * 人工維護日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412B_UPDDATE", columnDefinition = "DATE")
	private Date elf412b_upddate;

	/**
	 * 人工調整ID
	 */
	@Column(name = "ELF412B_UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String elf412b_updater;

	/**
	 * 其他備註
	 */
	@Column(name = "ELF412B_MEMO", length = 202, columnDefinition = "CHAR(202)")
	private String elf412b_memo;

	/**
	 * 資料更新日
	 */
	@Column(name = "ELF412B_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf412b_tmestamp;

	/**
	 * 人工調整週期 <br/>
	 * 目前資料['Y', 'N', '']
	 */
	@Column(name = "ELF412B_UCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elf412b_uckdLine;

	/**
	 * 人工調整週期有效日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412B_UCKDDT", columnDefinition = "DATE")
	private Date elf412b_uckdDt;

	/**
	 * 資料日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412B_DATADT", columnDefinition = "DATE")
	private Date elf412b_dataDt;

	/**
	 * 最新一次下次恢復覆審日 <br/>
	 * 不覆審代碼註記為不覆審時，可設定下次恢復覆審日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412B_NEXTNWDT", columnDefinition = "DATE")
	private Date elf412b_nextNwDt;

	/**
	 * 上次設定之下次恢復覆審日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412B_NEXTLTDT", columnDefinition = "DATE")
	private Date elf412b_nextLtDt;

	/**
	 * 外部評等類別 <br/>
	 * 標準普爾 | 1 <br/>
	 * 穆迪信評 | 2 <br/>
	 * 惠譽信評 | 3 <br/>
	 * 中華信評 | 4
	 */
	@Column(name = "ELF412B_FCRDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf412b_fcrdType;

	/**
	 * 外部評等地區別 <br/>
	 * 國際 | 1 <br/>
	 * 本國 | 2
	 */
	@Column(name = "ELF412B_FCRDAREA", length = 1, columnDefinition = "CHAR(1)")
	private String elf412b_fcrdArea;

	/**
	 * 外部評等期間別 <br/>
	 * 長期 | 1 <br/>
	 * 短期 | 2
	 */
	@Column(name = "ELF412B_FCRDPRED", length = 1, columnDefinition = "CHAR(1)")
	private String elf412b_fcrdPred;

	/**
	 * 外部評等等級
	 */
	@Column(name = "ELF412B_FCRDGRAD", length = 30, columnDefinition = "CHAR(30)")
	private String elf412b_fcrdGrad;

	/**
	 * 舊簽報書MAINID
	 */
	@Column(name = "ELF412B_OLDRPTID", length = 32, columnDefinition = "CHAR(32)")
	private String elf412b_oldRptId;

	/**
	 * 舊簽報書核准日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412B_OLDRPTDT", columnDefinition = "DATE")
	private Date elf412b_oldRptDt;

	/**
	 * 舊動審表MAINID
	 */
	@Column(name = "ELF412B_OLDDRAID", length = 32, columnDefinition = "CHAR(32)")
	private String elf412b_oldDraId;

	/**
	 * 舊動審表核准日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412B_OLDDRADT", columnDefinition = "DATE")
	private Date elf412b_oldDraDt;

	/**
	 * 新簽報書MAINID
	 */
	@Column(name = "ELF412B_NEWRPTID", length = 32, columnDefinition = "CHAR(32)")
	private String elf412b_newRptId;

	/**
	 * 新簽報書核准日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412B_NEWRPTDT", columnDefinition = "DATE")
	private Date elf412b_newRptDt;

	/**
	 * 新動審表MAINID
	 */
	@Column(name = "ELF412B_NEWDRAID", length = 32, columnDefinition = "CHAR(32)")
	private String elf412b_newDraId;

	/**
	 * 新動審表核准日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412B_NEWDRADT", columnDefinition = "DATE")
	private Date elf412b_newDraDt;

	/**
	 * J-108-0078_05097_B1001
	 * 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
	 * 
	 * 
	 * 首次往來之新貸戶
	 */
	@Column(name = "ELF412B_ISALLNEW", length = 1, columnDefinition = "CHAR(1)")
	private String elf412b_isAllNew;

	public String getElf412b_branch() {
		return elf412b_branch;
	}

	public void setElf412b_branch(String elf412b_branch) {
		this.elf412b_branch = elf412b_branch;
	}

	public String getElf412b_custId() {
		return elf412b_custId;
	}

	public void setElf412b_custId(String elf412b_custId) {
		this.elf412b_custId = elf412b_custId;
	}

	public String getElf412b_dupNo() {
		return elf412b_dupNo;
	}

	public void setElf412b_dupNo(String elf412b_dupNo) {
		this.elf412b_dupNo = elf412b_dupNo;
	}

	public String getElf412b_mainCust() {
		return elf412b_mainCust;
	}

	public void setElf412b_mainCust(String elf412b_mainCust) {
		this.elf412b_mainCust = elf412b_mainCust;
	}

	public String getElf412b_crdType() {
		return elf412b_crdType;
	}

	public void setElf412b_crdType(String elf412b_crdType) {
		this.elf412b_crdType = elf412b_crdType;
	}

	public String getElf412b_crdtTbl() {
		return elf412b_crdtTbl;
	}

	public void setElf412b_crdtTbl(String elf412b_crdtTbl) {
		this.elf412b_crdtTbl = elf412b_crdtTbl;
	}

	public String getElf412b_mowType() {
		return elf412b_mowType;
	}

	public void setElf412b_mowType(String elf412b_mowType) {
		this.elf412b_mowType = elf412b_mowType;
	}

	public String getElf412b_mowTbl1() {
		return elf412b_mowTbl1;
	}

	public void setElf412b_mowTbl1(String elf412b_mowTbl1) {
		this.elf412b_mowTbl1 = elf412b_mowTbl1;
	}

	public Date getElf412b_llrDate() {
		return elf412b_llrDate;
	}

	public void setElf412b_llrDate(Date elf412b_llrDate) {
		this.elf412b_llrDate = elf412b_llrDate;
	}

	public Date getElf412b_lrDate() {
		return elf412b_lrDate;
	}

	public void setElf412b_lrDate(Date elf412b_lrDate) {
		this.elf412b_lrDate = elf412b_lrDate;
	}

	public String getElf412b_rckdLine() {
		return elf412b_rckdLine;
	}

	public void setElf412b_rckdLine(String elf412b_rckdLine) {
		this.elf412b_rckdLine = elf412b_rckdLine;
	}

	public String getElf412b_ockdLine() {
		return elf412b_ockdLine;
	}

	public void setElf412b_ockdLine(String elf412b_ockdLine) {
		this.elf412b_ockdLine = elf412b_ockdLine;
	}

	public String getElf412b_cState() {
		return elf412b_cState;
	}

	public void setElf412b_cState(String elf412b_cState) {
		this.elf412b_cState = elf412b_cState;
	}

	public String getElf412b_newAdd() {
		return elf412b_newAdd;
	}

	public void setElf412b_newAdd(String elf412b_newAdd) {
		this.elf412b_newAdd = elf412b_newAdd;
	}

	public String getElf412b_newDate() {
		return elf412b_newDate;
	}

	public void setElf412b_newDate(String elf412b_newDate) {
		this.elf412b_newDate = elf412b_newDate;
	}

	public String getElf412b_nckdFlag() {
		return elf412b_nckdFlag;
	}

	public void setElf412b_nckdFlag(String elf412b_nckdFlag) {
		this.elf412b_nckdFlag = elf412b_nckdFlag;
	}

	public Date getElf412b_nckdDate() {
		return elf412b_nckdDate;
	}

	public void setElf412b_nckdDate(Date elf412b_nckdDate) {
		this.elf412b_nckdDate = elf412b_nckdDate;
	}

	public String getElf412b_nckdMemo() {
		return elf412b_nckdMemo;
	}

	public void setElf412b_nckdMemo(String elf412b_nckdMemo) {
		this.elf412b_nckdMemo = elf412b_nckdMemo;
	}

	public Date getElf412b_cancelDt() {
		return elf412b_cancelDt;
	}

	public void setElf412b_cancelDt(Date elf412b_cancelDt) {
		this.elf412b_cancelDt = elf412b_cancelDt;
	}

	public Date getElf412b_upddate() {
		return elf412b_upddate;
	}

	public void setElf412b_upddate(Date elf412b_upddate) {
		this.elf412b_upddate = elf412b_upddate;
	}

	public String getElf412b_updater() {
		return elf412b_updater;
	}

	public void setElf412b_updater(String elf412b_updater) {
		this.elf412b_updater = elf412b_updater;
	}

	public String getElf412b_memo() {
		return elf412b_memo;
	}

	public void setElf412b_memo(String elf412b_memo) {
		this.elf412b_memo = elf412b_memo;
	}

	public Timestamp getElf412b_tmestamp() {
		return elf412b_tmestamp;
	}

	public void setElf412b_tmestamp(Timestamp elf412b_tmestamp) {
		this.elf412b_tmestamp = elf412b_tmestamp;
	}

	public String getElf412b_uckdLine() {
		return elf412b_uckdLine;
	}

	public void setElf412b_uckdLine(String elf412b_uckdLine) {
		this.elf412b_uckdLine = elf412b_uckdLine;
	}

	public Date getElf412b_uckdDt() {
		return elf412b_uckdDt;
	}

	public void setElf412b_uckdDt(Date elf412b_uckdDt) {
		this.elf412b_uckdDt = elf412b_uckdDt;
	}

	public Date getElf412b_dataDt() {
		return elf412b_dataDt;
	}

	public void setElf412b_dataDt(Date elf412b_dataDt) {
		this.elf412b_dataDt = elf412b_dataDt;
	}

	public Date getElf412b_nextNwDt() {
		return elf412b_nextNwDt;
	}

	public void setElf412b_nextNwDt(Date elf412b_nextNwDt) {
		this.elf412b_nextNwDt = elf412b_nextNwDt;
	}

	public Date getElf412b_nextLtDt() {
		return elf412b_nextLtDt;
	}

	public void setElf412b_nextLtDt(Date elf412b_nextLtDt) {
		this.elf412b_nextLtDt = elf412b_nextLtDt;
	}

	public String getElf412b_fcrdType() {
		return elf412b_fcrdType;
	}

	public void setElf412b_fcrdType(String elf412b_fcrdType) {
		this.elf412b_fcrdType = elf412b_fcrdType;
	}

	public String getElf412b_fcrdArea() {
		return elf412b_fcrdArea;
	}

	public void setElf412b_fcrdArea(String elf412b_fcrdArea) {
		this.elf412b_fcrdArea = elf412b_fcrdArea;
	}

	public String getElf412b_fcrdPred() {
		return elf412b_fcrdPred;
	}

	public void setElf412b_fcrdPred(String elf412b_fcrdPred) {
		this.elf412b_fcrdPred = elf412b_fcrdPred;
	}

	public String getElf412b_fcrdGrad() {
		return elf412b_fcrdGrad;
	}

	public void setElf412b_fcrdGrad(String elf412b_fcrdGrad) {
		this.elf412b_fcrdGrad = elf412b_fcrdGrad;
	}

	public void setElf412b_oldRptId(String elf412b_oldRptId) {
		this.elf412b_oldRptId = elf412b_oldRptId;
	}

	public String getElf412b_oldRptId() {
		return elf412b_oldRptId;
	}

	public void setElf412b_oldRptDt(Date elf412b_oldRptDt) {
		this.elf412b_oldRptDt = elf412b_oldRptDt;
	}

	public Date getElf412b_oldRptDt() {
		return elf412b_oldRptDt;
	}

	public void setElf412b_oldDraId(String elf412b_oldDraId) {
		this.elf412b_oldDraId = elf412b_oldDraId;
	}

	public String getElf412b_oldDraId() {
		return elf412b_oldDraId;
	}

	public void setElf412b_oldDraDt(Date elf412b_oldDraDt) {
		this.elf412b_oldDraDt = elf412b_oldDraDt;
	}

	public Date getElf412b_oldDraDt() {
		return elf412b_oldDraDt;
	}

	public void setElf412b_newRptId(String elf412b_newRptId) {
		this.elf412b_newRptId = elf412b_newRptId;
	}

	public String getElf412b_newRptId() {
		return elf412b_newRptId;
	}

	public void setElf412b_newRptDt(Date elf412b_newRptDt) {
		this.elf412b_newRptDt = elf412b_newRptDt;
	}

	public Date getElf412b_newRptDt() {
		return elf412b_newRptDt;
	}

	public void setElf412b_newDraId(String elf412b_newDraId) {
		this.elf412b_newDraId = elf412b_newDraId;
	}

	public String getElf412b_newDraId() {
		return elf412b_newDraId;
	}

	public void setElf412b_newDraDt(Date elf412b_newDraDt) {
		this.elf412b_newDraDt = elf412b_newDraDt;
	}

	public Date getElf412b_newDraDt() {
		return elf412b_newDraDt;
	}

	/**
	 * 設定首次往來之新貸戶
	 * 
	 * @param elf412_isAllNew
	 */
	public void setElf412b_isAllNew(String elf412b_isAllNew) {
		this.elf412b_isAllNew = elf412b_isAllNew;
	}

	/**
	 * 取得首次往來之新貸戶
	 * 
	 * @return
	 */
	public String getElf412b_isAllNew() {
		return elf412b_isAllNew;
	}

}
