var init120s08 = {
	fhandle : "lms1201formhandler",
	ghandle : "lms1201gridhandler",
	fhandle140 : "lms1401m01formhandler",
	ghandle140 : "lms1405gridhandler",
	versionDate : "2017-09-05",
	defButton : {
		"close" : function() {
			$.thickbox.close();
		}
	}
};

var API_2017 = {
    /** 明細檔 */
    // J-107-0077-001 Web e-Loan 授信案件簽報書之「新臺幣、美元利率合理性分析表」修改
    openDetailBox : function(type, docOid, data, showRiskAdd,showCompetition) {
        var verNo = init120s08.versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);
        var curr = $L120S08Form.find('#curr').val();
        if (curr == "TWD") {
            $(".showN").show();
            $("#showTitleSY").show();
            $("#showTitleSN").hide();
        } else {
            $(".showN").hide();
            $("#showTitleSY").hide();
            $("#showTitleSN").show();
        }

        var docOid = "";
        if (data) {
            docOid = data.oid;
        }

        // J-107-0077-001 Web e-Loan 授信案件簽報書之「新臺幣、美元利率合理性分析表」修改
        if (showRiskAdd) {
            $("#showRiskAdd").show();
            $("#showLostDiffRate").hide();
        } else {
            $("#showRiskAdd").hide();
            $("#showLostDiffRate").show();
        }

        // J-107-01?? GGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGG
        if(showCompetition){
            $('#showCompetition').show();
        }else{
            $('#showCompetition').hide();
        }

        var buttons = {};
        if (!thickboxOptions.readOnly) {

            buttons["sure"] = function() {
                var $form = $L120S08Form;

                // 先計算可以先將數字欄位塞0
                if (!API_2017.btnCaculate(init120s08.versionDate)) {
                    CommonAPI
                            .showErrorMessage(i18n.lms1401s03["L120S08A.fieldTitle12"]);
                    return false;
                }

                if (!$form.valid()) {
                    return false;
                }

                // J-107-0077-001 Web e-Loan 授信案件簽報書之「新臺幣、美元利率合理性分析表」修改
                var showRiskAddStyleVal = $('#showRiskAdd').css('display');
                var saveLostDiffRate = "Y" ;
                if(!(showRiskAddStyleVal==$.trim('none'))){
                    //非不顯示風險加碼，則刪除各等級擬制呆帳損失差異率加碼
                    saveLostDiffRate = "N";
                }

                // J-107-01?? GGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGG
                var showCompetitionVal = $('#showCompetition').css('display');
                var saveCompetition = "Y" ;
                if(showCompetitionVal==$.trim('none')){
                    //非不顯示風險加碼，則刪除各等級擬制呆帳損失差異率加碼
                    saveCompetition = "N";
                }

                $.ajax({
                    handler : init120s08.fhandle,
                    data : {// 把資料轉成json
                        formAction : "saveL120s08Data",
                        docOid : docOid,
                        mainId : responseJSON.mainId,
                        curr : curr,
                        L120S08Form : JSON.stringify($L120S08Form
                                .serializeData()),
                        saveLostDiffRate:saveLostDiffRate,
                        // J-107-01?? GGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGG
                        saveCompetition : saveCompetition

                    }
				}).done(function(data) {
					$.thickbox.close();
					L120s08BoxAPI._triggerMainGrid();
                }); // close ajax
            };

        } else {
            $L120S08Form.readOnlyChilds(true);
            $L120S08Form.find("button").hide();
        }

        buttons["cancel"] = function() {
            $.thickbox.close();
        };

        $("#inputL120s08Box_20170905").thickbox({
            // L120S08A.title2=合理性分析表
            title : i18n.lms1401s03['L120S08A.title2'],
            width : 850,
            height : 500,
            modal : true,
            readOnly : thickboxOptions.readOnly,
            align : "center",
            i18n : i18n.def,
            valign : "bottom",
            buttons : buttons
        });
    },
	/**
     * 引進關係戶往來彙總二維表集團評等與貢獻度合計 return true ,false
     */
    btnApplyGroup : function(versionDate) {

        var verNo = versionDate.replace(/-/g, "");
        var L120S08FormName = "L120S08Form_"+verNo;
        var $L120S08Form = $("#"+L120S08FormName);

        var nowDate = new Date();
        var MM = nowDate.getMonth();
        var YY = nowDate.getFullYear();
        var SMM;
        var SYY;
        if (MM == 0) {
            MM = 12;
        }

        if (MM == 12) {
            SMM = MM - 5;
            YY = YY - 1;
            SYY = YY;
        } else if (MM > 5 && MM < 12) {
            SMM = MM - 5;
            SYY = YY;
        } else {
            SMM = MM + 12 - 5;
            SYY = YY - 1;
        }

        var $tL120S08Form1a = $("#tL120S08Form1a");
        $tL120S08Form1a.find("#queryDateS0").val(SYY);
        $tL120S08Form1a.find("#queryDateS1").val(SMM);
        $tL120S08Form1a.find("#queryDateE0").val(YY);
        $tL120S08Form1a.find("#queryDateE1").val(MM);

        $("#inputSearch").thickbox({ // 使用選取的內容進行彈窗
            title : i18n.lms1401s03["L120S08A.title4"], // L120S08A.title4=貢獻度查詢期間
            width : 600,
            height : 300,
            modal : true,
            align : 'center',
            valign : 'bottom',
            i18n : i18n.def,
            buttons : {
                "sure" : function() {
                    var $tL120S08Form1a = $("#tL120S08Form1a");

                    if ($tL120S08Form1a.valid()) {
                        if ($tL120S08Form1a.find("#queryDateS1").val() < 1
                                || $tL120S08Form1a.find("#queryDateS1").val() > 12
                                || $tL120S08Form1a.find("#queryDateE1").val() < 1
                                || $tL120S08Form1a.find("#queryDateE1").val() > 12) {
                            CommonAPI.showMessage(i18n.lms1401s03["L120S08A.error07"]);
                            return;
                        } else if ($tL120S08Form1a.find("#queryDateS0").val() <= 0
                                || $tL120S08Form1a.find("#queryDateE0").val() <= 0) {
                            CommonAPI.showMessage(i18n.lms1401s03["L120S08A.error08"]);
                            return;
                        } else if ($tL120S08Form1a.find("#queryDateE0").val()
                                - $tL120S08Form1a.find("#queryDateS0").val() < 0) {
                            CommonAPI.showMessage(i18n.lms1401s03["L120S08A.error09"]);
                            return;
                        } else if (($tL120S08Form1a.find("#queryDateE0").val()
                                        - $tL120S08Form1a.find("#queryDateS0").val() == 0)
                                && ($tL120S08Form1a.find("#queryDateE1").val()
                                        - $tL120S08Form1a.find("#queryDateS1").val() < 0)) {
                            CommonAPI.showMessage(i18n.lms1401s03["L120S08A.error09"]);
                            return;
                        } else {
                            $.thickbox.close();
                            $.ajax({
                                handler : init120s08.fhandle,
                                type : "POST",
                                dataType : "json",
                                data : {
                                    formAction : "queryMainGroupDataFromL120S04B",
                                    mainId : responseJSON.mainid,
                                    queryDateS0 : $tL120S08Form1a.find("#queryDateS0").val(),
                                    queryDateS1 : $tL120S08Form1a.find("#queryDateS1").val(),
                                    queryDateE0 : $tL120S08Form1a.find("#queryDateE0").val(),
                                    queryDateE1 : $tL120S08Form1a.find("#queryDateE1").val()
                                }
							}).done(function(obj120) {
								$L120S08Form.find("#group_dscr").val(obj120.groupDscr);
                            });
                        }
                    }
                },
                "cancel" : function() {
                    $.thickbox.close();
                }
            }
        });
    },
	/** 計算 */
    btnCaculate : function(versionDate) {

        var baseRate = 0;
        var verNo = versionDate.replace(/-/g, "");
        var L120S08FormName = "L120S08Form_"+verNo;
        var $L120S08Form = $("#"+L120S08FormName);

        if (!$L120S08Form.find("#baseRate").val()) {
            // L120S08A.error12=請先輸入
            // L120S08A.baseRate=基礎放款利利率
            CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error12']
                    + i18n.lms1401s03["L120S08A.baseRate"]);
            return false;
        }
        baseRate = parseFloat($L120S08Form.find("#baseRate").val(), 10);

        var disYearRateSTot = parseFloat(0);
        var disYearRateNTot = parseFloat(0);

        // J-107-0077-001 Web e-Loan 授信案件簽報書之「新臺幣、美元利率合理性分析表」修改
        // 沒有區分有擔無擔
        var lostDiffRate_disYearRate = $L120S08Form.find("#lostDiffRate_disYearRate").val();
        if (lostDiffRate_disYearRate) {
            lostDiffRate_disYearRate = parseFloat(lostDiffRate_disYearRate, 10);
        } else {
            lostDiffRate_disYearRate = 0;
            $L120S08Form.find("#lostDiffRate_disYearRate").val(0);
        }

        var showLostDiffRateVal = $L120S08Form.find('#showLostDiffRate').css('display');
        if(showLostDiffRateVal==$.trim('none')){
            lostDiffRate_disYearRate = 0;
            $L120S08Form.find("#lostDiffRate_disYearRate").val(0);
        }

        // 檢查有擔無擔是否要計算
        var hasS = false;
        var hasN = false;
        // 擔保利率計算(美元為利率計算)
        $("form[id='"+L120S08FormName+"'] input[id*='_disYearRateS']").each(function() {
            // $(this).attr('id') = this.id
            var disYearRateSId = this.id;
            if ($(this).val()) {
                hasS = true;
            }
        });

        // 無擔保利率計算(只有台幣要計算)
        var curr = $L120S08Form.find("#curr").val();
        if (curr != "USD") {
            $("form[id='"+L120S08FormName+"'] input[id*='_disYearRateN']").each(function() {
                // $(this).attr('id') = this.id
                var disYearRateNId = this.id;
                if ($(this).val()) {
                    hasN = true;
                }
            });
        }

        var hasError = false;

        // 擔保利率計算(美元為利率計算)
        if (hasS) {
            // 一開始為基礎放款利利率
            disYearRateSTot = disYearRateSTot + baseRate + lostDiffRate_disYearRate;

            $("form[id='"+L120S08FormName+"'] input[id*='_disYearRateS']").each(function() {
                // $(this).attr('id') = this.id

                var disYearRateSId = this.id;
                var disYearRateS = 0;

                if ($(this).val()) {
                    disYearRateS = parseFloat($(this).val(), 10);
                } else {
                    $(this).val(0);
                    disYearRateS = 0;
                }

                if (disYearRateSId == "reasonRate_disYearRateS"
                        || disYearRateSId == "otherAdd_disYearRateS"
                        || disYearRateSId == "riskAdd_disYearRateS") {
                    // 合理利潤率要相加
                    disYearRateSTot = disYearRateSTot + disYearRateS;
                } else if (disYearRateSId == "lowestRate_disYearRateS") {
                    // 本案申請利率最低不處理
                } else {
                    // 個案申請減碼理由 (每項≦0.20%)
                    if (disYearRateSId == "collateral_disYearRateS"
                            || disYearRateSId == "dealing_disYearRateS"
                            || disYearRateSId == "contribution_disYearRateS"
                            || disYearRateSId == "guarantor_disYearRateS"
                            || disYearRateSId == "group_disYearRateS"
                            || disYearRateSId == "competition_disYearRateS"
                            || disYearRateSId == "otherDscr_disYearRateS") {
                        if (disYearRateS > 0.2) {
                            hasError = true;
                            return false;
                        }
                    }
                    // 其他要相減
                    disYearRateSTot = disYearRateSTot - disYearRateS;
                }
            });
            $L120S08Form.find("#lowestRate_disYearRateS").val(disYearRateSTot.toFixed(5));
        }

        // 無擔保利率計算(只有台幣要計算)
        // 一開始為基礎放款利利率
        if (hasN) {
            disYearRateNTot = disYearRateNTot + baseRate + lostDiffRate_disYearRate;
            $("form[id='"+L120S08FormName+"'] input[id*='_disYearRateN']").each(function() {
                // $(this).attr('id') = this.id

                var disYearRateNId = this.id;
                var disYearRateN = 0;

                if ($(this).val()) {
                    disYearRateN = parseFloat($(this).val(), 10);
                } else {
                    $(this).val(0);
                    disYearRateN = 0;
                }

                if (disYearRateNId == "reasonRate_disYearRateN"
                        || disYearRateNId == "otherAdd_disYearRateN"
                        || disYearRateNId == "riskAdd_disYearRateN") {
                    // 合理利潤率要相加
                    disYearRateNTot = disYearRateNTot + disYearRateN;
                } else if (disYearRateNId == "lowestRate_disYearRateN") {
                    // 本案申請利率最低不處理
                } else {
                    // 個案申請減碼理由 (每項≦0.20%)
                    if (disYearRateNId == "collateral_disYearRateN"
                            || disYearRateNId == "dealing_disYearRateN"
                            || disYearRateNId == "contribution_disYearRateN"
                            || disYearRateNId == "guarantor_disYearRateN"
                            || disYearRateNId == "group_disYearRateN"
                            || disYearRateNId == "competition_disYearRateN"
                            || disYearRateNId == "otherDscr_disYearRateN") {
                        if (disYearRateN > 0.2) {
                            hasError = true;
                            return false;
                        }
                    }
                    // 其他要相減
                    disYearRateNTot = disYearRateNTot - disYearRateN;
                }
            });

            $L120S08Form.find("#lowestRate_disYearRateN").val(disYearRateNTot.toFixed(5));
        }

        if (hasError) {
            return false;
        } else {
            return true;
        }
    }
};

initDfd.done(function(json) {
    $("#btnApplyRateDscr").click(function() {
        L120s08BoxAPI.btnApplyRateDscr(init120s08.versionDate);
    });
    $("#btnApplyGroup").click(function() {
        API_2017.btnApplyGroup(init120s08.versionDate);
    });
    $("#btnCaculate").click(function() {
        if (!API_2017.btnCaculate(init120s08.versionDate)) {
            CommonAPI.showErrorMessage(i18n.lms1401s03["L120S08A.fieldTitle12"]);
        }
    });
    $("#btnClearS").click(function() {
        L120s08BoxAPI.btnClear('S', init120s08.versionDate);
    });
    $("#btnClearN").click(function() {
        L120s08BoxAPI.btnClear('N', init120s08.versionDate);
    });
    $("#btnShowLostDiffRate").click(function() {
        L120s08BoxAPI.btnShowLostDiffRate(init120s08.versionDate);
    });

    L120s08BoxAPI.gridviewConnomOtherSelect();
});