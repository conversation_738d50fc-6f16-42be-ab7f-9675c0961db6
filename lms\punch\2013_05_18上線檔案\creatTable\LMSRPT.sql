---------------------------------------------------------
-- LMS.LMSRPT 授信批次報表歷史檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.LMSRPT;
CREATE TABLE LMS.LMSRPT (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	<PERSON>ANCH        CHAR(3)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>      DATE         ,
	B<PERSON><PERSON><PERSON><PERSON>       DATE         ,
	<PERSON><PERSON><PERSON>TE       DATE         ,
	RP<PERSON><PERSON><PERSON>         VARCHAR(10)  ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>       VARCHAR(120) ,
	NOWRPT        CHAR(01)     ,
	<PERSON><PERSON><PERSON><PERSON>       TIMESTAMP    ,
	REM<PERSON>KS       varchar(200) ,
	SENDER        VARCHAR(6)   ,
	SENDTIME      TIMESTAMP    ,
	JINGBAN       VARCHAR(6)   ,
	CFRMTIME      TIMESTAMP    ,
	CFRMFLAG      CHAR(1)      ,
	REPORTOIDFILE CHAR(32)     ,
	RANDOMCODE    VARCHAR(32)  ,
	UPDATER       VARCHAR(6)   ,
	UPDATETIME    TIMESTAMP    ,
	constraint P_LMSRPT PRIMARY KEY(OID)
) IN EL_DATA_4KTS INDEX IN EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XLMSRPT01;
CREATE INDEX LMS.XLMSRPT01  ON LMS.LMSRPT     (BRANCH, ENDDATE, RPTNO, NOWRPT);
--DROP INDEX LMS.XLMSRPT02;
CREATE INDEX LMS.XLMSRPT02  ON LMS.LMSRPT     (BRANCH, BGNDATE, ENDDATE, RPTNO, NOWRPT);
--DROP INDEX LMS.XLMSRPT03;
CREATE INDEX LMS.XLMSRPT03  ON LMS.LMSRPT     (MAINID);
--DROP INDEX LMS.XLMSRPT04;
CREATE INDEX LMS.XLMSRPT04  ON LMS.LMSRPT     (BRANCH, RPTNO, DATADATE, NOWRPT);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.LMSRPT IS '授信批次報表歷史檔';
COMMENT ON LMS.LMSRPT (
	OID           IS 'oid', 
	MAINID        IS 'mainId', 
	BRANCH        IS '分行代碼', 
	DATADATE      IS '資料日期', 
	BGNDATE       IS '起日', 
	ENDDATE       IS '迄日', 
	RPTNO         IS '報表代號', 
	RPTNAME       IS '報表名稱', 
	NOWRPT        IS '是否為最新報表', 
	BTHDATE       IS '批次時間', 
	REMARKS       IS '備註或其他需使用的值', 
	SENDER        IS '分行傳送授管處人員', 
	SENDTIME      IS '分行傳送授管處時間', 
	JINGBAN       IS '授管處核備經辦', 
	CFRMTIME      IS '授管處核備時間', 
	CFRMFLAG      IS '授管處核備註記', 
	REPORTOIDFILE IS '報表OID', 
	RANDOMCODE    IS '報表亂碼', 
	UPDATER       IS '資料修改人(行編)', 
	UPDATETIME    IS '資料修改日期'
);
