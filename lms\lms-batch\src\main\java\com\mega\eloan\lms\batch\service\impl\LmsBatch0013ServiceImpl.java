package com.mega.eloan.lms.batch.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.FlowNameService.FlowReturnEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01IDao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01MDao;
import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.dao.L140S02CDao;
import com.mega.eloan.lms.dao.L140S05ADao;
import com.mega.eloan.lms.dao.L140S06ADao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisELF447nService;
import com.mega.eloan.lms.mfaloan.service.MisELF447xService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01I;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02C;
import com.mega.eloan.lms.model.L140S05A;
import com.mega.eloan.lms.model.L140S06A;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * 不變案件補上傳到ELF447X
 */
@Service("lmsbatch0013serviceimpl")
public class LmsBatch0013ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	SysParameterService sysParamService;

	@Resource
	MisELF447nService misELF447nService;

	@Resource
	MisELF447xService misELF447xService;

	@Resource
	EloandbBASEService eloandbService;

	@Resource
	LMSService lmsService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L120S01BDao l120s01bDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	DwdbBASEService dwdbBASEService;

	@Resource
	L140S02ADao l140s02aDao;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	L140M01MDao l140m01mDao;

	@Resource
	L140S05ADao l140s05aDao;

	@Resource
	L140S06ADao l140s06aDao;

	@Resource
	L140S02CDao l140s02cDao;

	@Resource
	L120M01IDao l120m01iDao;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		// @NonTransactional
		JSONObject result = new JSONObject();
		JSONObject request = json.getJSONObject("request");

		String errMsg = this.doLmsBatch0001(request);
		if (Util.notEquals(errMsg, "")) {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RESPONSE,
					"LmsBatch0013ServiceImpl-doLmsBatch0001執行失敗！==>" + errMsg);
			return result;
		} else {
			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE,
					"LmsBatch00013ServiceImpl執行成功！");
		}

		return result;
	}

	/**
	 * J-112-0456_05097_B1001 Web e-Loan授信系統增加簽報中屬不變的額度序號之簽報明細上送DW
	 */
	@NonTransactional
	public String doLmsBatch0001(JSONObject request) {

		StringBuffer errMsg = new StringBuffer("");

		String dataStartDate = Util.trim(request.getString("dataStartDate"));
		String dataEndDate = Util.trim(request.getString("dataEndDate"));

		if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
			errMsg.append("無法取得完整資料期間 " + dataStartDate + "~" + dataEndDate
					+ " ");
			System.out.println(errMsg.toString());
			return errMsg.toString();

		}

		List<Map<String, Object>> listL120m01a = eloandbService
				.findL120m01aByEndDateOnlyMainId(dataStartDate, dataEndDate);

		if (listL120m01a != null && !listL120m01a.isEmpty()) {

			for (Map<String, Object> l120Map : listL120m01a) {

				String mainId = Util
						.trim(MapUtils.getObject(l120Map, "MAINID"));

				L120M01A l120m01a = l120m01aDao.findByMainId(mainId);
				if (l120m01a == null) {
					continue;
				}

				misELF447xService.deleteByunId(l120m01a.getMainId());

				// 根據來源不同出現的表不一樣 ，當授權外一定是批覆表，而當授權內為營運中心授權內還是以批覆表為準
				String itemType = lmsService.checkL140M01AItemType(l120m01a);
				List<L140M01A> l140m01as = l140m01aDao
						.findL140m01aListByL120m01cMainId(mainId, itemType,
								FlowDocStatusEnum.已核准.getCode());

				for (L140M01A l140m01a : l140m01as) {

					// String custId = l140m01a.getCustId();
					// String dupNo = l140m01a.getDupNo();
					// String cntrNo = l140m01a.getCntrNo();
					String ownBrid = l140m01a.getOwnBrId();

					// 不變的才要上傳ELF447X
					if (!LMSUtil.isContainValue(l140m01a.getProPerty(),
							UtilConstants.Cntrdoc.Property.不變)) {
						continue;
					}

					// String countryno = l140m01a.getRiskArea();
					// if (Util.isEmpty(countryno)) {
					// countryno = "TW";
					// }

					String areaNo = "";
					// 區域代碼
					// List<Map<String, Object>> areaMap = dwdbBASEService
					// .findDW_CNTRYAREAByCountryCode(countryno);
					// if (!areaMap.isEmpty()) {
					// for (Map<String, Object> data : areaMap) {
					// areaNo = Util.trim((String) data.get("AREA_NO"));
					// }
					// }

					// [上午 09:27] 黃建霖(資訊處,高級專員)
					//
					// 以下四個欄位不變的舊案不補:
					//
					// ELF447N_RISK_AREA 這個風險區域別
					//
					// ELF447N_GRPNO 集團代碼
					//
					// ELF447N_BUS_CD 行業別代碼
					//
					// ELF447N_BUS_SUB_CD 行業別代碼
					//
					//
					//
					// [上午 09:29] 金至忠(授信審查處,襄理)
					//
					// 可以不補

					boolean upElf447n = false;
					boolean upElf447x = true;

					String nextBranchID = Util.trim(ownBrid);

					if (upElf447n || upElf447x) {
						this.gfnDB2ELF447N_TMP(l120m01a, l140m01a, areaNo,
								l120m01a.getDocStatus(), nextBranchID,
								upElf447n, upElf447x);

					}

					logger.info("[LmsBatch0013ServiceImpl]   ====>"
							+ "l120m01a" + mainId + "，l140m01a ="
							+ l140m01a.getMainId() + "，cntrNo ="
							+ l140m01a.getCntrNo() + "，areaNo =" + areaNo
							+ "，DOCSTATUS =" + l120m01a.getDocStatus()
							+ "，nextBranchID =" + nextBranchID);

				}

			}

		}

		return errMsg.toString();
	}

	private void gfnDB2ELF447N_TMP(L120M01A meta, L140M01A l140m01a,
			String areaNo, String nextDocstatus, String nextBrId,
			boolean upElf447n, boolean upElf447x) {
		// Properties prop = MessageBundleScriptCreator
		// .getComponentResource(LMSCommomPage.class);
		// PTEAMAPP.msg=整批房貸承購戶
		// String pteamapMsg = prop.getProperty("PTEAMAPP.msg");
		String custId = l140m01a.getCustId();
		String dupNo = l140m01a.getDupNo();

		String ELF447N_UNID = meta.getMainId(); // 簽報書文件編號mainId
		String ELF447N_PROJECT_NO = Util.trimSizeInOS390(
				LMSUtil.getUploadCaseNo(meta), 40); // 案件編號

		String ELF447N_CLASS = "1"; // CHAR(1) NOT NULL,報案類別 1. 授信 ( 預設 ) 2.
		// 應收帳款無追索 3. 衍生性金融商品
		String ELF447N_CUSTID = custId; // CHAR(10) NOT
		// NULL,借款人統編
		String ELF447N_DUPNO = dupNo; // CHAR(1) NOT NULL,重複序號
		String ELF447N_STATUS = ""; // CHAR(01) NOT NULL,文件狀態 0- 預約 ( 預約到期日內有效
		// ) 1- 報核 ( 報核日起三個月有效 ) 2- 已核定 (
		// 核定日起六個月有效 )A- 已撤銷B- 已婉卻C- 已退件
		String ELF447N_STATUS_DT = ""; // DATE NOT NULL,狀態日0- 預約到期日 1- 報核日 2-
		// 核定日A- 撤銷日 B- 婉卻日
		String ELF447N_CONTRACT = l140m01a.getCntrNo(); // CHAR(12) NOT
		// NULL,額度序號
		String ELF447N_BRANCH = meta.getCaseBrId(); // CHAR(3) NOT NULL,主辦分行代號
		// CHAR(3) NOT
		// NULL,目前案件所在分行代號
		String ELF447N_PROCESS_BR = nextBrId;
		String ELF447N_FACT_TYPE = ""; // 額度控管種類
		String ELF447N_SYSTYPE = meta.getDocType(); // CHAR(3) NOT NULL,系統類別
		// 1- 企金 2- 個金
		String ELF447N_CASELEVEL = Util.trimSizeInOS390(meta.getCaseLvl(), 1); // CHAR(1)
		// NOT
		// NULL,授權等級
		Timestamp ELF447N_TMESTAMP = CapDate.getCurrentTimestamp();
		// NOT
		// NULL,資料修改日期
		String ELF447N_PROPERTY = ""; // 授信性質別
		BigDecimal ELF447N_CURAMT = l140m01a.getCurrentApplyAmt(); // DECIMAL(15,
		// 2)
		// NOT
		// NULL,現請額度金額
		String ELF447N_CURR = l140m01a.getCurrentApplyCurr(); // CHAR(3) NOT
		// NULL,現請額度幣別
		BigDecimal ELF447N_OLDAMT = l140m01a.getLV2Amt(); // DECIMAL(15, 2) NOT
		// NULL,前請額度金額
		String ELF447N_OLDCURR = l140m01a.getLV2Curr(); // CHAR(3) NOT
		// NULL,前請額度幣別
		/**
		 * 有前准批覆額度 優先當沒有值 再來抓前准額度
		 */

		// J-112-0456_05097_B1001 Web e-Loan授信系統增加簽報中屬不變的額度序號之簽報明細上送DW
		// 因為一併要上傳ELF447X，所以要多加判斷不變的才要上到ELF447X，才不會被誤用
		// 保險起見的檢核
		if (upElf447x
				&& !LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
						UtilConstants.Cntrdoc.Property.不變)) {
			// 要上傳ELF447X，但是額度明細表額度性質沒有不變
			// 要改成不要上傳ELF447X(可能參數誤用)
			upElf447x = false;
		}

		if (ELF447N_OLDAMT == null) {
			if (l140m01a.getLVAmt() != null) {
				ELF447N_OLDAMT = l140m01a.getLVAmt();
				ELF447N_OLDCURR = l140m01a.getLVCurr();
			} else {
				ELF447N_OLDAMT = BigDecimal.ZERO;
				ELF447N_OLDCURR = "";
			}
		} else {
			/*
			 * 當性質=取消時，檢核的條件並不嚴格 若經辦有輸入 ELF447N_OLDAMT，但未輸入 ELF447N_OLDCURR
			 * 在上傳ELF447N時，因DB中該欄位不可為null，會造成主管無法覆核
			 */
			if (ELF447N_OLDCURR == null) {
				ELF447N_OLDCURR = "";
			}
		}

		String ELF447N_GRPNO = ""; // CHAR(4) NOT NULL,集團代碼
		String ELF447N_RISK_CNTRY = ""; // CHAR(2) NOT NULL,風險國別
		String ELF447N_RISK_AREA = Util.trimSizeInOS390(areaNo, 2); // CHAR(2)
		// NOT
		// NULL,風險區域別
		String ELF447N_BUS_CD = ""; // CHAR(6) NOT NULL,行業對象別
		String ELF447N_BUS_SUB_CD = ""; // CHAR(2) NOT NULL,行業對象別細分類
		String ELF447N_BUILD_NAME = ""; // 團貸案名稱
		String ELF447N_SITE1 = ""; // 土地座落 (縣市)
		String ELF447N_SITE2 = ""; // 土地座落 (鄉鎮)
		String ELF447N_SITE3 = ""; // 土地座落 (段)
		String ELF447N_SITE4 = ""; // 土地座落 (小段)
		String ELF447N_NUSEDATE = "0001-01-01"; // DATE 未動用原因維護日
		String ELF447N_NUSEMEMO = ""; // VCHAR(804) 由簽約未動用報送來 FLMS230M01

		if (LMSUtil.isParentCase(meta)) {
			List<L140S02A> l140s02as = l140s02aDao.findByMainId(l140m01a
					.getMainId());
			L140S02A l140s02a = new L140S02A();
			if (!l140s02as.isEmpty()) {
				l140s02a = l140s02as.get(0);
			}
			String caseName = rm_linebreak(Util.trim(l140s02a.getCaseName()));
			String buildName = rm_linebreak(Util.trim(l140s02a.getBuildName()));
			// 當有團貸名稱就放團貸名稱 ，有建案名稱就放建案的
			if (Util.isEmpty(buildName)) {
				buildName = caseName;
				ELF447N_BUILD_NAME = Util.trimSizeInOS390(buildName, 62);
			} else {
				ELF447N_BUILD_NAME = Util.trimSizeInOS390(buildName, 62);
			}
			String landCity = Util.trim(l140s02a.getLandCity());
			CodeType counties = codeTypeService.findByCodeTypeAndCodeValue(
					"counties", landCity);
			CodeType counties2 = codeTypeService.findByCodeTypeAndCodeValue(
					"counties" + landCity, l140s02a.getLandArea());
			ELF447N_SITE1 = Util.trimSizeInOS390(counties == null ? ""
					: counties.getCodeDesc(), 22); // 土地座落
			// (縣市)
			ELF447N_SITE2 = Util.trimSizeInOS390(counties2 == null ? ""
					: counties2.getCodeDesc(), 22); // 土地座落
			// (鄉鎮)
			ELF447N_SITE3 = Util.trimSizeInOS390(l140s02a.getLandpart1(), 22); // 土地座落
			// (段)
			ELF447N_SITE4 = Util.trimSizeInOS390(l140s02a.getLandpart2(), 22);
		}

		// J-112-0456_05097_B1001 Web e-Loan授信系統增加簽報中屬不變的額度序號之簽報明細上送DW
		// 集團代碼
		// List<Map<String, Object>> grpcmp = misGrpcmpService
		// .findGrpcmpSelGrpdtl(custId, dupNo);
		//
		// for (Map<String, Object> data : grpcmp) {
		// ELF447N_GRPNO = Util.trimSizeInOS390(
		// Util.trim((String) data.get("GRPID")), 4);
		// }
		// logger.info("查詢集團代碼misGrpcmp=======>" + ELF447N_GRPNO);

		// 風險國別
		if (Util.isEmpty(l140m01a.getRiskArea())) {
			ELF447N_RISK_CNTRY = "TW";
		} else {
			ELF447N_RISK_CNTRY = l140m01a.getRiskArea();
		}

		// J-112-0456_05097_B1001 Web e-Loan授信系統增加簽報中屬不變的額度序號之簽報明細上送DW

		// Map<String, Object> busCDMap = misCustdataService
		// .findBUSCDByCustIdANdDupNo(custId, dupNo);
		//
		// if (busCDMap != null) {
		// // 行業別代碼
		// ELF447N_BUS_CD = Util.trim(busCDMap.get("BUSCD"));
		// ELF447N_BUS_SUB_CD = Util.trim(busCDMap.get("BUSSKIND"));
		// }

		// logger.info(
		// "引進借款人行業別及次產業別資訊==[ELF447N_BUS_CD===>{0}]==[ELF447N_BUS_SUB_CD={1}=>]",
		// new Object[] { ELF447N_BUS_CD, ELF447N_BUS_SUB_CD });

		if (CreditDocStatusEnum.海外_婉卻.getCode().equals(nextDocstatus)
				|| CreditDocStatusEnum.海外_已核准.getCode().equals(nextDocstatus)) {
			if (FlowDocStatusEnum.婉卻.getCode().equals(l140m01a.getDocStatus())) {
				ELF447N_STATUS = "B";
			} else if (FlowDocStatusEnum.已核准.getCode().equals(
					l140m01a.getDocStatus())) {
				ELF447N_STATUS = "2";
			} else {
				ELF447N_STATUS = "2";
			}

			// 舊案無ELF447N時，為了LLDLN503補上傳簽報書檢核用
			// 正常不變不會上傳ELF447N
			if (LMSUtil.isContainValue(l140m01a.getProPerty(),
					UtilConstants.Cntrdoc.Property.不變)) {
				if (Util.equals(ELF447N_STATUS, "2")) {
					ELF447N_STATUS = "E";
				}
			}

		} else if (CreditDocStatusEnum.海外_呈授管處.getCode().equals(nextDocstatus)
				|| CreditDocStatusEnum.海外_呈營運中心.getCode().equals(nextDocstatus)) {
			ELF447N_STATUS = "1";
		} else if (CreditDocStatusEnum.海外_待撤件.getCode().equals(nextDocstatus)
				|| CreditDocStatusEnum.海外_待補件.getCode().equals(nextDocstatus)
				|| CreditDocStatusEnum.授管處_待陳復.getCode().equals(nextDocstatus)) {
			if (FlowReturnEnum.撤件.getCode().equals(meta.getReturnFromBH())) {
				ELF447N_STATUS = "A";
			} else if (FlowReturnEnum.陳復.getCode().equals(
					meta.getReturnFromBH())) {
				ELF447N_STATUS = "C";
			}
		} else {
			ELF447N_STATUS = "1";
		}

		ELF447N_STATUS_DT = CapDate.formatDate(meta.getEndDate(),
				UtilConstants.DateFormat.YYYY_MM_DD);

		if (Util.isEmpty(ELF447N_STATUS_DT)) {
			ELF447N_STATUS_DT = CapDate.formatDate(
					CapDate.getCurrentTimestamp(),
					UtilConstants.DateFormat.YYYY_MM_DD);
		}
		String tproperty = "999";
		for (String x : l140m01a.getProPerty().split(
				UtilConstants.Mark.SPILT_MARK)) {
			if (UtilConstants.Cntrdoc.Property.新做.equals(x)
					|| UtilConstants.Cntrdoc.Property.續約.equals(x)
					|| UtilConstants.Cntrdoc.Property.增額.equals(x)) {

				if (Integer.valueOf(x) < Integer.valueOf(tproperty)) {
					tproperty = x;
				}
			}
		}

		if ("999".equals(tproperty)) {
			for (String x : l140m01a.getProPerty().split(
					UtilConstants.Mark.SPILT_MARK)) {
				if (Integer.valueOf(x) < Integer.valueOf(tproperty)) {
					tproperty = x;
				}
			}

		}

		// 加強檢核--報價
		if (LMSUtil.isContainValue(l140m01a.getProPerty(),
				UtilConstants.Cntrdoc.Property.報價)) {
			if (!UtilConstants.Cntrdoc.Property.報價.equals(tproperty)) {
				tproperty = UtilConstants.Cntrdoc.Property.報價;
			}
		}

		// 加強檢核--國外部性質同時選 取消、變更條件
		if (LMSUtil.isContainValue(l140m01a.getProPerty(),
				UtilConstants.Cntrdoc.Property.取消)) {
			if (!UtilConstants.Cntrdoc.Property.取消.equals(tproperty)) {
				if (!Util.isEmpty(l140m01a.getCurrentApplyAmt())) {
					if (BigDecimal.ZERO
							.compareTo(l140m01a.getCurrentApplyAmt()) == 0) {
						tproperty = UtilConstants.Cntrdoc.Property.取消;
					}
				} else {
					tproperty = UtilConstants.Cntrdoc.Property.取消;
				}
			}
		}

		ELF447N_PROPERTY = tproperty;
		ELF447N_FACT_TYPE = Util.trim(l140m01a.getSnoKind());

		// 利率說明
		String ELF447N_INT_MEMO = "";
		// 是否興建住宅
		String ELF447N_RESIDENTIAL = "";

		// 利率
		BigDecimal ELF447N_INT_Rate = BigDecimal.ZERO;
		// 產品
		String ELF447N_PROD_CLASS = "";
		// 科目八碼
		String ELF447N_ACT_CODE = "";
		// 用途別
		String ELF447N_PURPOSE = "";

		BigDecimal ELF447N_LAND_AREA = BigDecimal.ZERO; // ' 土地面積 ',
		Date ELF447N_BUILD_DATE = CapDate.getDate("0001/01/01", "yyyy/MM/dd");
		BigDecimal ELF447N_WAIT_MONTH = BigDecimal.ZERO; // ' 預計撥款至動工期間月數 ',
		String ELF447N_LOCATE_CD = ""; // ' 擔保品座落區 ',
		BigDecimal ELF447N_SITE3NO = BigDecimal.ZERO; // ' 座落區段 ',
		String ELF447N_SITE4NO = ""; // ' 座落區村里 ',
		String ELF447N_LAND_TYPE = ""; // ' 土地使用分區 ');

		L140M01M l140m01m = l140m01mDao.findByMainId(Util.trim(l140m01a
				.getMainId()));

		if (l140m01m != null) {
			ELF447N_LAND_AREA = (Util.isNotEmpty(l140m01m.getAreaLand()) ? l140m01m
					.getAreaLand() : BigDecimal.ZERO); // ' 土地面積 ',

			ELF447N_BUILD_DATE = (Util.isEmpty(l140m01m.getBuildDate()) ? CapDate
					.getDate("0001/01/01", "yyyy/MM/dd") : l140m01m
					.getBuildDate());// 預計取得建照日期(為空值時設定為0001/01/01)

			ELF447N_WAIT_MONTH = (Util.isNotEmpty(l140m01m.getWaitMonth()) ? l140m01m
					.getWaitMonth() : BigDecimal.ZERO); // ' 預計撥款至動工期間月數 ',
			ELF447N_LOCATE_CD = (Util.isNotEmpty(l140m01m.getLocationCd()) ? Util
					.trim(l140m01m.getLocationCd()) : ""); // ' 擔保品座落區 ',
			ELF447N_SITE3NO = (Util.isNotEmpty(l140m01m.getSite3No()) ? l140m01m
					.getSite3No() : BigDecimal.ZERO); // ' 座落區段 ',
			ELF447N_SITE4NO = (Util.isNotEmpty(l140m01m.getSite4No()) ? Util
					.trim(l140m01m.getSite4No()) : ""); // ' 座落區村里 ',
			ELF447N_LAND_TYPE = (Util.isNotEmpty(l140m01m.getLandType()) ? Util
					.trim(l140m01m.getLandType()) : ""); // ' 土地使用分區 ');
		}

		String ELF447N_PROPERTIES = Util.trim(l140m01a.getProPerty());

		// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
		// 實地覆審分行
		String ELF447N_REVIEWBR = "";
		L120S01B l120s01b = l120s01bDao.findByUniqueKey(meta.getMainId(),
				l140m01a.getCustId(), l140m01a.getDupNo());
		if (l120s01b != null) {
			ELF447N_REVIEWBR = Util.trim(l120s01b.getReviewBrNo());
		}

		// J-108-0210_05097_B1001 Web e-Loan企金授信新增上傳借款人基本資料的實收資本額
		String ELF447N_ENDDATE = meta.getEndDate() == null ? null : CapDate
				.formatDate(meta.getEndDate(), "yyyy-MM-dd");
		String ELF447N_RGTCURR = "";
		BigDecimal ELF447N_RGTAMT = BigDecimal.ZERO;
		BigDecimal ELF447N_RGTUNIT = BigDecimal.ONE; // 沒有值DEFAULT 也要是1

		// J-108-0283 變更條件Condition Change
		String ELF447N_COND_CHG = "";
		L140S05A l140s05a = l140s05aDao.findByUniqueKey(l140m01a.getMainId());
		if (l140s05a != null) {
			ELF447N_COND_CHG = Util.nullToSpace(l140s05a.getCond_chg())
					.replace("\n", "").replace("\"", "");
		}

		// J-108-0293 Web e-Loan 未依銀行內部規定 internal regulations
		String ELF447N_INTREG = "";
		List<L140S06A> l140s06as = l140s06aDao.findByMainId(l140m01a
				.getMainId());
		if (l140s06as == null || l140s06as.size() <= 0) {
			ELF447N_INTREG = "";
		} else {
			ELF447N_INTREG = this.getElf447nIntReg(l140s06as);
		}

		// J-111-0551_05097_B1003 Web
		// e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
		String ELF447N_LNNOFLAG = "";// 不計入授信項目代號
		BigDecimal ELF447N_CURAMT_S = null;
		BigDecimal ELF447N_CURAMT_N = null;

		boolean twClsUseAssure = true; // 已經沒用，改讀系統設定LMS_ELF447N_AMT_USE_CLS_ASSURE

		ELF447N_LNNOFLAG = Util.trim(l140m01a.getNoLoan());

		Map<String, BigDecimal> currAmtMap = lmsService.getCurrentApplyAmt_S_N(
				meta, l140m01a, true);
		if (currAmtMap != null && !currAmtMap.isEmpty()) {
			ELF447N_CURAMT_S = currAmtMap.get("ELF447N_CURAMT_S") == null ? null
					: Util.parseBigDecimal(currAmtMap.get("ELF447N_CURAMT_S"));
			ELF447N_CURAMT_N = currAmtMap.get("ELF447N_CURAMT_N") == null ? null
					: Util.parseBigDecimal(currAmtMap.get("ELF447N_CURAMT_N"));
		}

		if (LMSUtil.isClsCase(meta)) {

			if ("10".equals(ELF447N_FACT_TYPE)) {
				ELF447N_FACT_TYPE = "51";
			} else if ("20".equals(ELF447N_FACT_TYPE)) {
				ELF447N_FACT_TYPE = "50";
			}
			// 國內個金
			ELF447N_CLASS = LMSUtil.isParentCase(meta) ? "5" : "1";
			Set<String> prodKindSet = new HashSet<String>();
			Set<String> subjCodeSet = new HashSet<String>();
			Set<String> lnPursSet = new HashSet<String>();
			List<L140S02A> l140s02as = l140s02aDao.findByMainId(l140m01a
					.getMainId());
			int count = 0;
			for (L140S02A l140s02a : l140s02as) {
				count++;
				if (count == 1) {
					L140S02C l140s02c = l140s02cDao.findByUniqueKey(
							l140s02a.getMainId(), l140s02a.getSeq());
					if (l140s02c != null) {
						ELF447N_INT_Rate = l140s02c.getSubmitRate();
					}
					ELF447N_INT_MEMO = Util.trimSizeInOS390(
							l140s02a.getRateDesc(), 560);
					ELF447N_RESIDENTIAL = l140s02a.getResidential();

				}
				prodKindSet.add(l140s02a.getProdKind());
				subjCodeSet.add(l140s02a.getSubjCode());
				lnPursSet.add(l140s02a.getLnPurs());
			}
			StringBuffer prodKindStr = new StringBuffer();
			for (String prod : prodKindSet) {
				prodKindStr
						.append(prodKindStr.length() > 0 ? UtilConstants.Mark.MARK
								: "");
				prodKindStr.append(prod);
			}

			StringBuffer subjCodeStr = new StringBuffer();
			for (String subjCode : subjCodeSet) {
				subjCodeStr
						.append(subjCodeStr.length() > 0 ? UtilConstants.Mark.MARK
								: "");
				subjCodeStr.append(subjCode);
			}

			StringBuffer lnPursStr = new StringBuffer();
			for (String lnPurs : lnPursSet) {
				lnPursStr
						.append(lnPursStr.length() > 0 ? UtilConstants.Mark.MARK
								: "");
				lnPursStr.append(lnPurs);
			}

			ELF447N_PROD_CLASS = Util.trimSizeInOS390(prodKindStr.toString(),
					255);
			ELF447N_ACT_CODE = Util
					.trimSizeInOS390(subjCodeStr.toString(), 255);
			ELF447N_PURPOSE = Util.trimSizeInOS390(lnPursStr.toString(), 255);

			// J-108-0210_05097_B1001 Web e-Loan企金授信新增上傳借款人基本資料的實收資本額
			// J-111-0551_05097_B1003 Web
			// e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
			if (upElf447n) {
				misELF447nService.insertByCls(ELF447N_UNID, ELF447N_PROJECT_NO,
						ELF447N_CLASS, ELF447N_CUSTID, ELF447N_DUPNO,
						ELF447N_STATUS, ELF447N_STATUS_DT, ELF447N_CONTRACT,
						ELF447N_BRANCH, ELF447N_PROCESS_BR, ELF447N_FACT_TYPE,
						ELF447N_SYSTYPE, ELF447N_CASELEVEL, ELF447N_PROPERTY,
						ELF447N_CURAMT, ELF447N_CURR, ELF447N_OLDAMT,
						ELF447N_OLDCURR, ELF447N_GRPNO, ELF447N_RISK_CNTRY,
						ELF447N_RISK_AREA, ELF447N_BUS_CD, ELF447N_BUS_SUB_CD,
						ELF447N_BUILD_NAME, ELF447N_SITE1, ELF447N_SITE2,
						ELF447N_SITE3, ELF447N_SITE4, ELF447N_NUSEDATE,
						ELF447N_NUSEMEMO, ELF447N_TMESTAMP, ELF447N_INT_MEMO,
						ELF447N_RESIDENTIAL, ELF447N_INT_Rate,
						ELF447N_PROD_CLASS, ELF447N_ACT_CODE, ELF447N_PURPOSE,
						ELF447N_LAND_AREA, ELF447N_BUILD_DATE,
						ELF447N_WAIT_MONTH, ELF447N_LOCATE_CD, ELF447N_SITE3NO,
						ELF447N_SITE4NO, ELF447N_LAND_TYPE, ELF447N_PROPERTIES,
						ELF447N_ENDDATE, ELF447N_RGTCURR, ELF447N_RGTAMT,
						ELF447N_RGTUNIT, ELF447N_COND_CHG, ELF447N_INTREG,
						ELF447N_LNNOFLAG, ELF447N_CURAMT_S, ELF447N_CURAMT_N);
			}

			// J-112-0456_05097_B1001 Web e-Loan授信系統增加簽報中屬不變的額度序號之簽報明細上送DW
			if (upElf447x) {
				misELF447xService.insertByCls(ELF447N_UNID, ELF447N_PROJECT_NO,
						ELF447N_CLASS, ELF447N_CUSTID, ELF447N_DUPNO,
						ELF447N_STATUS, ELF447N_STATUS_DT, ELF447N_CONTRACT,
						ELF447N_BRANCH, ELF447N_PROCESS_BR, ELF447N_FACT_TYPE,
						ELF447N_SYSTYPE, ELF447N_CASELEVEL, ELF447N_PROPERTY,
						ELF447N_CURAMT, ELF447N_CURR, ELF447N_OLDAMT,
						ELF447N_OLDCURR, ELF447N_GRPNO, ELF447N_RISK_CNTRY,
						ELF447N_RISK_AREA, ELF447N_BUS_CD, ELF447N_BUS_SUB_CD,
						ELF447N_BUILD_NAME, ELF447N_SITE1, ELF447N_SITE2,
						ELF447N_SITE3, ELF447N_SITE4, ELF447N_NUSEDATE,
						ELF447N_NUSEMEMO, ELF447N_TMESTAMP, ELF447N_INT_MEMO,
						ELF447N_RESIDENTIAL, ELF447N_INT_Rate,
						ELF447N_PROD_CLASS, ELF447N_ACT_CODE, ELF447N_PURPOSE,
						ELF447N_LAND_AREA, ELF447N_BUILD_DATE,
						ELF447N_WAIT_MONTH, ELF447N_LOCATE_CD, ELF447N_SITE3NO,
						ELF447N_SITE4NO, ELF447N_LAND_TYPE, ELF447N_PROPERTIES,
						ELF447N_ENDDATE, ELF447N_RGTCURR, ELF447N_RGTAMT,
						ELF447N_RGTUNIT, ELF447N_COND_CHG, ELF447N_INTREG,
						ELF447N_LNNOFLAG, ELF447N_CURAMT_S, ELF447N_CURAMT_N);
			}

		} else {
			// J-111-0551_05097_B1003 Web
			// e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
			String LMS_ELF447N_CLASS_VER = Util.trim(lmsService
					.getSysParamDataValue("LMS_ELF447N_CLASS_VER"));
			ELF447N_CLASS = lmsService.checkELF447NCLASS(l140m01a,
					LMS_ELF447N_CLASS_VER);

			ELF447N_RESIDENTIAL = Util.trim(l140m01a.getResidential());
			// 企金
			Set<L140M01B> l140m01bs = l140m01a.getL140m01b();
			if (l140m01bs != null) {
				for (L140M01B l140m01b : l140m01bs) {
					if (UtilConstants.Cntrdoc.l140m01bItemType.利費率
							.equals(l140m01b.getItemType())) {
						ELF447N_INT_MEMO = Util.trimSizeInOS390(
								l140m01b.getItemDscr(), 560);
						break;
					}
				}
			}

			ELF447N_PROD_CLASS = Util.trim(l140m01a.getLnType());
			if (Util.equals(ELF447N_PROD_CLASS, "00")) {
				ELF447N_PROD_CLASS = "";
			}

			// J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
			String ELF447N_ISHEDGE = "";
			BigDecimal ELF447N_ENHANCEAMT = BigDecimal.ZERO;
			ELF447N_ISHEDGE = Util.trim(lmsService
					.getDerivateSubjectHedgeKinde(l140m01a));
			if (Util.equals(ELF447N_ISHEDGE, "2")) {
				ELF447N_ENHANCEAMT = l140m01a.getEnhanceAmt() == null ? BigDecimal.ZERO
						: l140m01a.getEnhanceAmt();
			}

			// J-107-0357_05097_B1001 Web
			// e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
			String ELF447N_PROJ_CLASS = Util.trim(l140m01a.getProjClass());
			if (Util.notEquals(Util.trim(l140m01a.getIsStartUp()), "N")) {
				if (Util.equals(Util.trim(ELF447N_PROJ_CLASS), "05")) {
					ELF447N_PROJ_CLASS = "";
				}
			}

			if (Util.equals(ELF447N_PROJ_CLASS, "00")) {
				ELF447N_PROJ_CLASS = "";
			}

			// J-108-0210_05097_B1001 Web e-Loan企金授信新增上傳借款人基本資料的實收資本額
			if (l120s01b != null) {
				ELF447N_RGTCURR = Util.trim(l120s01b.getCptlCurr());
				ELF447N_RGTAMT = Util
						.parseBigDecimal(l120s01b.getCptlAmt() == null ? 0
								: l120s01b.getCptlAmt());
				ELF447N_RGTUNIT = Util
						.parseBigDecimal(l120s01b.getCptlUnit() == null ? 1
								: l120s01b.getCptlUnit());

				if (ELF447N_RGTUNIT == null
						|| BigDecimal.ZERO.compareTo(Util
								.parseBigDecimal(l120s01b.getCptlUnit())) == 0) {
					ELF447N_RGTUNIT = BigDecimal.ONE;
				}
			}

			// J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			String ELF447N_ISRESCUE = "";
			String ELF447N_RESCUEITEM = "";
			BigDecimal ELF447N_RESCUERATE = BigDecimal.ZERO;
			if (!LMSUtil.isClsCase(meta)) {
				// 企金才有
				ELF447N_ISRESCUE = Util.trim(l140m01a.getIsRescue());
				ELF447N_RESCUEITEM = Util.trim(l140m01a.getRescueItem());
				ELF447N_RESCUERATE = l140m01a.getRescueRate() == null ? BigDecimal.ZERO
						: l140m01a.getRescueRate();
			}

			// M-109-0210_05097_B1001 Web
			// e-Loan企金授信配合DW產生額度未引入帳務系統之報表，上傳授信科目到ELF447N
			ELF447N_ACT_CODE = Util.trimSizeInOS390(
					Util.trim(l140m01a.getLnSubject()), 255);

			// J-111-0283 E-LOAN簽報，關係企業額度已逾分行權限，需覆核主管覆核，並產生檢核報表
			String ELF447N_OVAUTH_LN = "";
			String ELF447N_OVAUTH_EX = "";
			String ELF447N_OVAUTH_AL = "";
			L120M01I l120m01i = l120m01iDao.findByUniqueKey(meta.getMainId());
			if (l120m01i != null) {
				ELF447N_OVAUTH_LN = Util.trim(l120m01i.getOverAuthLoan());
				ELF447N_OVAUTH_EX = Util.trim(l120m01i.getOverAuthExperf());
				ELF447N_OVAUTH_AL = Util.trim(l120m01i.getOverAuthAloneLoan());
			}

			// misSQL.xml加欄位之外，在LMS.ELF447N也應同時增加欄位。避免delete/insert後，原本有值的欄位被清成null
			// J-108-0210_05097_B1001 Web e-Loan企金授信新增上傳借款人基本資料的實收資本額
			// J-111-0551_05097_B1003 Web
			// e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
			if (upElf447n) {
				misELF447nService.insert(ELF447N_UNID, ELF447N_PROJECT_NO,
						ELF447N_CLASS, ELF447N_CUSTID, ELF447N_DUPNO,
						ELF447N_STATUS, ELF447N_STATUS_DT, ELF447N_CONTRACT,
						ELF447N_BRANCH, ELF447N_PROCESS_BR, ELF447N_FACT_TYPE,
						ELF447N_SYSTYPE, ELF447N_CASELEVEL, ELF447N_PROPERTY,
						ELF447N_CURAMT, ELF447N_CURR, ELF447N_OLDAMT,
						ELF447N_OLDCURR, ELF447N_GRPNO, ELF447N_RISK_CNTRY,
						ELF447N_RISK_AREA, ELF447N_BUS_CD, ELF447N_BUS_SUB_CD,
						ELF447N_BUILD_NAME, ELF447N_SITE1, ELF447N_SITE2,
						ELF447N_SITE3, ELF447N_SITE4, ELF447N_NUSEDATE,
						ELF447N_NUSEMEMO, ELF447N_TMESTAMP, ELF447N_INT_MEMO,
						ELF447N_RESIDENTIAL, ELF447N_PROD_CLASS,
						ELF447N_LAND_AREA, ELF447N_BUILD_DATE,
						ELF447N_WAIT_MONTH, ELF447N_LOCATE_CD, ELF447N_SITE3NO,
						ELF447N_SITE4NO, ELF447N_LAND_TYPE, ELF447N_PROPERTIES,
						ELF447N_REVIEWBR, ELF447N_ISHEDGE, ELF447N_ENHANCEAMT,
						ELF447N_PROJ_CLASS, ELF447N_ENDDATE, ELF447N_RGTCURR,
						ELF447N_RGTAMT, ELF447N_RGTUNIT, ELF447N_COND_CHG,
						ELF447N_INTREG, ELF447N_ISRESCUE, ELF447N_RESCUEITEM,
						ELF447N_RESCUERATE, ELF447N_ACT_CODE,
						ELF447N_OVAUTH_LN, ELF447N_OVAUTH_EX,
						ELF447N_OVAUTH_AL, ELF447N_LNNOFLAG, ELF447N_CURAMT_S,
						ELF447N_CURAMT_N);
			}

			// J-112-0456_05097_B1001 Web e-Loan授信系統增加簽報中屬不變的額度序號之簽報明細上送DW
			if (upElf447x) {
				misELF447xService.insert(ELF447N_UNID, ELF447N_PROJECT_NO,
						ELF447N_CLASS, ELF447N_CUSTID, ELF447N_DUPNO,
						ELF447N_STATUS, ELF447N_STATUS_DT, ELF447N_CONTRACT,
						ELF447N_BRANCH, ELF447N_PROCESS_BR, ELF447N_FACT_TYPE,
						ELF447N_SYSTYPE, ELF447N_CASELEVEL, ELF447N_PROPERTY,
						ELF447N_CURAMT, ELF447N_CURR, ELF447N_OLDAMT,
						ELF447N_OLDCURR, ELF447N_GRPNO, ELF447N_RISK_CNTRY,
						ELF447N_RISK_AREA, ELF447N_BUS_CD, ELF447N_BUS_SUB_CD,
						ELF447N_BUILD_NAME, ELF447N_SITE1, ELF447N_SITE2,
						ELF447N_SITE3, ELF447N_SITE4, ELF447N_NUSEDATE,
						ELF447N_NUSEMEMO, ELF447N_TMESTAMP, ELF447N_INT_MEMO,
						ELF447N_RESIDENTIAL, ELF447N_PROD_CLASS,
						ELF447N_LAND_AREA, ELF447N_BUILD_DATE,
						ELF447N_WAIT_MONTH, ELF447N_LOCATE_CD, ELF447N_SITE3NO,
						ELF447N_SITE4NO, ELF447N_LAND_TYPE, ELF447N_PROPERTIES,
						ELF447N_REVIEWBR, ELF447N_ISHEDGE, ELF447N_ENHANCEAMT,
						ELF447N_PROJ_CLASS, ELF447N_ENDDATE, ELF447N_RGTCURR,
						ELF447N_RGTAMT, ELF447N_RGTUNIT, ELF447N_COND_CHG,
						ELF447N_INTREG, ELF447N_ISRESCUE, ELF447N_RESCUEITEM,
						ELF447N_RESCUERATE, ELF447N_ACT_CODE,
						ELF447N_OVAUTH_LN, ELF447N_OVAUTH_EX,
						ELF447N_OVAUTH_AL, ELF447N_LNNOFLAG, ELF447N_CURAMT_S,
						ELF447N_CURAMT_N);
			}
		}

	}

	private String rm_linebreak(String raw) {
		return StringUtils.join(StringUtils.split(raw), " ");
	}

	private String getElf447nIntReg(List<L140S06A> l140s06as) {
		String intReg = "";

		StringBuilder sb = new StringBuilder();
		if (l140s06as == null || l140s06as.size() <= 0) {

		} else {
			sb.append("〔");
			int i = 0;
			for (L140S06A l140s06a : l140s06as) {
				sb.append((i > 0 ? "|" : ""));
				sb.append(Util.nullToSpace(l140s06a.getIntReg()));
				i++;
			}
			sb.append("〕");

			intReg = sb.toString();
		}
		return intReg;
	}

}
