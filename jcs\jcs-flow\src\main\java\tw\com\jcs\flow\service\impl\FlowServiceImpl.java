package tw.com.jcs.flow.service.impl;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tw.com.jcs.flow.FlowDefinition;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowDefinitionImpl;
import tw.com.jcs.flow.core.FlowEngineImpl;
import tw.com.jcs.flow.core.FlowEngineUnit;
import tw.com.jcs.flow.core.FlowInstanceImpl;
import tw.com.jcs.flow.model.Pair;
import tw.com.jcs.flow.node.CancelNode;
import tw.com.jcs.flow.node.DecisionNode;
import tw.com.jcs.flow.node.EndNode;
import tw.com.jcs.flow.node.FlowNode;
import tw.com.jcs.flow.node.ForkNode;
import tw.com.jcs.flow.node.StateNode;
import tw.com.jcs.flow.node.SubProcessNode;
import tw.com.jcs.flow.query.Query;
import tw.com.jcs.flow.query.impl.QueryImpl;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 流程執行服務
 * </pre>
 * 
 * @since 2023年1月10日
 * <AUTHOR> Software Inc.
 * @version
 *          <ul>
 *          <li>2023年1月10日,JC Software Inc.
 *          </ul>
 */
public class FlowServiceImpl extends FlowEngineUnit implements FlowService {

    private static final Logger logger = LoggerFactory.getLogger(FlowServiceImpl.class);

    /**
     * constructor
     * 
     * @param engine
     */
    public FlowServiceImpl(FlowEngineImpl engine) {
        super(engine);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.service.FlowService#createQuery()
     */
    @Override
    public Query createQuery() {
        return new QueryImpl(getEngine());
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.service.FlowService#start(java.lang.String, java.lang.Object, java.lang.String, java.lang.String)
     */
    @Override
    public FlowInstance start(String defName, Object id, String userId, String deptId) {
        // 取得流程定義檔
        FlowEngineImpl engine = getEngine();
        FlowDefinitionImpl definition = engine.getDefinition(defName);
        if (definition == null) {
            throw new RuntimeException("Can't find flow definition named '" + defName + "'!");
        }

        // 建立新的流程實體
        logger.info("[{}] Starting flow ...", definition.getName());
        FlowInstanceImpl instance = new FlowInstanceImpl(engine);
        instance.setDefinition(definition);
        instance.setState(definition.getStartNode().getName());
        instance.setId(id);
        instance.setSeq(0);
        instance.setBeginTime(new Date());
        instance.setUserId(userId);
        instance.setDeptId(deptId);

        engine.getPersistence().saveFlowInstance(instance);
        engine.addInstanceToPool(instance);
        logger.info("[{}] Flow started in state '{}'.", instance.getId(), instance.getState());
        instance.setNextUser(userId);
        instance.setNextDept(deptId);
        instance.handle(FlowInstance.CREATE);
        instance.next();
        return instance;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.service.FlowService#start(java.lang.String, java.lang.Object, java.lang.String, java.lang.String, java.lang.String, java.lang.Object)
     */
    @Override
    public FlowInstance start(String defName, Object id, String userId, String deptId, String key, Object value) {
        // 取得流程定義檔
        FlowEngineImpl engine = getEngine();
        FlowDefinitionImpl definition = engine.getDefinition(defName);
        if (definition == null) {
            throw new RuntimeException("Can't find flow definition named '" + defName + "'!");
        }

        // 建立新的流程實體
        logger.info("[{}] Starting flow ...", definition.getName());
        FlowInstanceImpl instance = new FlowInstanceImpl(engine);
        instance.setDefinition(definition);
        instance.setState(definition.getStartNode().getName());
        instance.setId(id);
        instance.setSeq(0);
        instance.setBeginTime(new Date());
        instance.setUserId(userId);
        instance.setDeptId(deptId);
        instance.setAttribute(key, value);

        engine.getPersistence().saveFlowInstance(instance);
        engine.addInstanceToPool(instance);
        logger.info("[{}] Flow started in state '{}'.", instance.getId(), instance.getState());
        instance.setNextUser(userId);
        instance.setNextDept(deptId);
        instance.handle(FlowInstance.CREATE);
        instance.next();
        return instance;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.service.FlowService#start(java.lang.String, java.lang.Object, java.lang.String, java.lang.String, java.util.Map)
     */
    @Override
    public FlowInstance start(String defName, Object id, String userId, String deptId, Map<String, Object> attributes) {
        // 取得流程定義檔
        FlowEngineImpl engine = getEngine();
        FlowDefinitionImpl definition = engine.getDefinition(defName);
        if (definition == null) {
            throw new RuntimeException("Can't find flow definition named '" + defName + "'!");
        }

        // 建立新的流程實體
        logger.info("[{}] Starting flow ...", definition.getName());
        FlowInstanceImpl instance = new FlowInstanceImpl(engine);
        instance.setDefinition(definition);
        instance.setState(definition.getStartNode().getName());
        instance.setId(id);
        instance.setSeq(0);
        instance.setBeginTime(new Date());
        instance.setUserId(userId);
        instance.setDeptId(deptId);
        instance.setData(attributes);

        engine.getPersistence().saveFlowInstance(instance);
        engine.addInstanceToPool(instance);
        logger.info("[{}] Flow started in state '{}'.", instance.getId(), instance.getState());
        instance.setNextUser(userId);
        instance.setNextDept(deptId);
        instance.handle(FlowInstance.CREATE);
        instance.next();
        return instance;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.service.FlowService#start(java.lang.String, java.lang.String, java.lang.String)
     */
    @Override
    public FlowInstance start(String defName, String userId, String deptId) {
        return start(defName, engine.getNextId(), userId, deptId);
    }

    /**
     * 建立新的子流程
     * 
     * @param def
     *            使用的流程定義
     * @param state
     *            子流程的初始狀態(節點)
     * @param pState
     *            對應的父流程節點
     * @return 子流程實體
     */
    public FlowInstanceImpl startSubFlow(FlowDefinitionImpl definition, FlowInstanceImpl parent, String state, String pState) {
        FlowInstanceImpl subInst = new FlowInstanceImpl(engine);
        subInst.setDefinition(definition);
        subInst.setState(state);
        subInst.setId(engine.getNextId(parent));
        subInst.setSeq(0);
        subInst.setBeginTime(new Date());
        subInst.setUserId(parent.getUserId());
        subInst.setDeptId(parent.getDeptId());
        subInst.setParentInstanceId(parent.getId());
        subInst.setParentInstanceState(pState);

        logger.debug("[{}#{}] Sub flow created. (id:{})", new Object[] { parent.getId(), parent.getSeq(), subInst.getId() });
        subInst.handle(FlowInstance.CREATE);

        subInst.save();
        engine.addInstanceToPool(subInst);

        List<Object> subList = parent.getSubInstanceList().get(pState);
        if (subList == null) {
            subList = new LinkedList<Object>();
            parent.getSubInstanceList().put(pState, subList);
        }
        subList.add(subInst.getId());
        return subInst;
    }

    /**
     * 建立新的Fork子流程
     * 
     * @param def
     *            使用的流程定義
     * @param forkName
     *            Fork節點名
     * @param transition
     *            Fork路徑
     * @return 子流程實體
     */
    public FlowInstanceImpl forkSubFlow(FlowDefinitionImpl definition, FlowInstanceImpl parent, String forkName, String transition) {
        FlowInstanceImpl subInst = new FlowInstanceImpl(engine);
        subInst.setDefinition(definition);
        subInst.setState(forkName);
        subInst.setId(engine.getNextId(parent));
        subInst.setSeq(0);
        subInst.setBeginTime(new Date());
        subInst.setUserId(parent.getUserId());
        subInst.setDeptId(parent.getDeptId());
        subInst.setParentInstanceId(parent.getId());
        subInst.setParentInstanceState(forkName);

        logger.debug("[{}#{}] Sub flow created. (id:{})", new Object[] { parent.getId(), parent.getSeq(), subInst.getId() });

        subInst.handle(transition);
        subInst.setState(definition.getNodes().get(forkName).getTransitions().get(transition));

        subInst.save();
        engine.addInstanceToPool(subInst);

        List<Object> subList = parent.getSubInstanceList().get(forkName);
        if (subList == null) {
            subList = new LinkedList<Object>();
            parent.getSubInstanceList().put(forkName, subList);
        }
        subList.add(subInst.getId());
        return subInst;
    }

    /**
     * 取得下一個State節點<br/>
     * 如回傳null，則代表找不到State，或下一節點無法判斷
     */
    @Override
    public StateNode getNextState(FlowInstance instance) {
        // 用Stack來暫存父流程的狀態
        Stack<Pair<FlowDefinitionImpl, FlowNode>> stack = new Stack<Pair<FlowDefinitionImpl, FlowNode>>();
        Map<String, FlowNode> nodes = instance.getDefinition().getNodes();

        FlowNode node = nodes.get(instance.getState());
        node = nodes.get(node.getTransitions().values().iterator().next());
        while (!(node instanceof StateNode)) {
            if (node instanceof DecisionNode) {
                // 如果是DECISION節點，則以目前的資料執行表達式，取得結果
                String value = ((DecisionNode) node).execute((FlowInstanceImpl) instance);
                node = nodes.get(node.getTransitions().get(value));
            } else if (node instanceof ForkNode) {
                // 如果是Fork節點，則下個節點不可知，不可指定執行成員
                node = null;
                break;
            } else if (node instanceof SubProcessNode) {
                // 如果是節點是子流程，則進入子流程的定義，然後往下走
                FlowDefinitionImpl def = getEngine().getDefinition(((SubProcessNode) node).getDefinition());
                stack.push(new Pair<FlowDefinitionImpl, FlowNode>(def, node));
                nodes = def.getNodes();
                node = def.getStartNode();
            } else if (node instanceof EndNode) {
                // 如果是子流程的End，則回到父流程往下走
                if (!stack.empty()) {
                    Pair<FlowDefinitionImpl, FlowNode> pair = stack.pop();
                    nodes = pair.getFirst().getNodes();
                    node = nodes.get(pair.getSecond().getTransitions().values().iterator().next());
                } else {
                    // 如果沒有父流程，則不指定執行成員
                    node = null;
                    break;
                }
            } else {
                // 如為其他節點，則直接取第一個路徑的節點
                node = nodes.get(node.getTransitions().values().iterator().next());
            }
        }

        if (node != null) {
            return (StateNode) node;
        }
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.service.FlowService#cancel(java.lang.Object)
     */
    @Override
    public void cancel(Object id) {
        FlowInstanceImpl inst = (FlowInstanceImpl) engine.getPersistence().getInstance(id);
        if (inst == null)
            return;
        // 先儲存要移除的列表(避免ConcurrentModification)
        List<Object> removeIdList = new LinkedList<Object>();
        for (List<Object> subIdList : inst.getSubInstanceList().values()) {
            if (subIdList != null) {
                removeIdList.addAll(subIdList);
            }
        }
        // 再一次性移除
        for (Object subId : removeIdList) {
            if (subId != null) {
                cancel(subId);
            }
        }
        CancelNode.cancel(inst);
        logger.debug("[{}] Flow has canceled.", id);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.service.FlowService#isSubProcessFinished(tw.com.jcs.flow.FlowInstance)
     */
    @Override
    public boolean isSubProcessFinished(FlowInstance instance) {
        List<Object> list = new LinkedList<Object>();

        String forkName = ForkNode.peekFromForkStack((FlowInstanceImpl) instance);
        if (forkName == null) {
            for (List<Object> subList : instance.getSubInstanceList().values()) {
                list.addAll(subList);
            }
        } else {
            List<Object> subList = instance.getSubInstanceList().get(forkName);
            if (subList != null) {
                list.addAll(subList);
            }
        }
        if (list.size() > 0) {
            List<FlowInstance> instList = getEngine().getPersistence().queryForInstance(list.toArray(new Object[list.size()]));

            return instList.size() == 0;
        }
        return true;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.service.FlowService#existsFlow(java.lang.Object)
     */
    @Override
    public boolean existsFlow(Object id) {
        List<FlowInstance> list = getEngine().getPersistence().queryForInstance(id);
        return list.size() > 0;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.service.FlowService#getDefinition(java.lang.String)
     */
    @Override
    public FlowDefinition getDefinition(String defName) {
        FlowEngineImpl engine = getEngine();
        FlowDefinitionImpl definition = engine.getDefinition(defName);
        return definition;
    }

}
