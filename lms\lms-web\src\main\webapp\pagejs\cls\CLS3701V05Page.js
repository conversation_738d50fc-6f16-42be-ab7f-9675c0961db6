var _handler = "cls3701m01formhandler";
$(function(){
	var isChange = false;
    var thisYear=new Date().getFullYear();
	var thisMonth=new Date().getMonth()+1-1; //getLastMonth
	thisMonth = thisMonth < 10 ? '0'+ thisMonth : thisMonth;
	var lastDay=new Date(thisYear,thisMonth,0).getDate();
	$("#filterForm").find("[name=applyTS_beg]").val(thisYear + "-" + thisMonth + "-01");
	$("#filterForm").find("[name=applyTS_end]").val(thisYear + "-" + thisMonth + "-" + lastDay);
//	$("#filterForm").find("[name=applyTS_end]").val(CommonAPI.getToday());
	var hidden05O = true;
    if(viewstatus == "05O"){
        hidden05O = false;
    }
    var lastsel2="";
    var updateData = [];
	var grid = $("#gridview").iGrid({
        handler: 'cls3701gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        postData: {
            formAction: "queryELF604",
            docStatus : viewstatus
        },
        colModel: [{
        	colHeader: i18n.cls3701m01["C126M01A.custId"],
            align: "left",
            width: 100, // 設定寬
            name: 'LNF030_CUST_ID'
        }, {
        	colHeader: i18n.cls3701m01["C126M01A.custName"],
            align: "left",
            width: 80, // 設定寬
            name: 'CName'
        }, {
        	colHeader: i18n.cls3701m01["C126M01A.ownBrId"],
            align: "left",
            width: 80, // 設定寬
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: i18n.cls3701m01["cls3701v05.agnt_NO"], //房仲代號
            align: "left",
            width: 80, // 設定寬
            name: 'agntNo'
        }, {
            colHeader: i18n.cls3701m01["cls3701v05.LNF033_LOAN_NO"],//"放款帳號",
            align: "left",
            width: 120, // 設定寬
            name: 'LNF033_LOAN_NO'
        }, {
            colHeader: i18n.cls3701m01["cls3701v05.LNF030_CONTRACT"],//"額度序號",
            name: 'LNF030_CONTRACT',
            width: 100,
            align: "center"
        }, {
            colHeader: i18n.cls3701m01["cls3701v05.LNF090_TXN_DATE"],//"放款日期",
            name: 'LNF090_TXN_DATE',
            width: 80,
            align: "center"
        }, {
            colHeader: i18n.cls3701m01["cls3701v05.LNF090_TXN_AMt"],//"首次放款金額",
            name: 'LNF090_TXN_AMt',
            formatter:'number', 
            formatoptions:{decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 0, defaultValue: ''},
            width: 80,
            align: "center"
        }, {
            colHeader: i18n.cls3701m01["cls3701v05.LNF030_loan_bal"],//"放款餘額",
            name: 'LNF030_loan_bal',
            formatter:'number', 
            formatoptions:{decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 0, defaultValue: ''},
            width: 80,
            align: "center",
            hidden: true
        }, {
            colHeader: i18n.cls3701m01["cls3701v05.ELF604_UPD_AMT"],//"引介獎金",
            name: 'ELF604_UPD_AMT',
            width: 80,
            align: "center",
            formatter:'number', 
            formatoptions:{decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 0, defaultValue: ''},
            editable:true	//開放編輯
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docStatus',
            hidden: true
        }],
        rowNum: 1000,
        viewrecords: true,
        onSelectRow: function(id){
    		if(id && id!==lastsel2){
    			//判斷必填欄位
    			if(lastsel2!=""){
    				if($("#"+lastsel2+"_ELF604_UPD_AMT").val()==""){
    					$("#gridview").jqGrid('restoreRow',lastsel2);//還原那筆資料
    					return CommonAPI.showErrorMessage(i18n.cls3701m01["cls3701v05.error05"]);
    				}
    				//Save回grid
    		    	$("#gridview").jqGrid("saveRow", lastsel2, false, "clientArray");
    		    	//把前一筆record存起來
    		    	//把有Update的record index記起來
    		    	if($('#gridview').jqGrid ('getRowData')[lastsel2-1].ELF604_UPD_AMT!=""){
    					updateData.push(lastsel2);
    					isChange = true;
    					//alert("update:"+updateData);
    				}
    			}
    			//$("#gridview").jqGrid('restoreRow',lastsel2);//還原那筆資料
    			$("#gridview").jqGrid('editRow',id,true);
    			lastsel2=id;
    		}
    	},
    	codetypeItem:[],
    	caption:i18n.cls3701m01["cls3701v05.editing"]
    });
	openFilterBox();
    $("#buttonPanel").find("#btnSaveRebate").click(function(){
    	saveAll();
    }).end().find("#btnFilter").click(function(){
    	openFilterBox();
    });
    
    // 篩選
    function openFilterBox(){
        var $filterForm = $("#filterForm");
        if(true){
        	//第一次開啟box
            //產生下拉選單
            var $div = $("#filterForm").find("[itemType]");
            var allKey = [];
            $div.each(function(){
                allKey.push($(this).attr("itemType"));
            });
            grid.codetypeItem = API.loadCombos(allKey);
            $div.each(function(){
                var $obj = $(this);
                var itemType = $obj.attr("itemType");
                if (itemType) {
                    var format = $obj.attr("itemFormat") || "{value} - {key}";
                    $obj.setItems({
                        space: $obj.attr("space") || true,
                        item: grid.codetypeItem[itemType],
                        format: format,
                        sort: $obj.attr("itemSort") || "asc",
                        size: $obj.attr("itemSize")
                    });
                }
            });
            
            //J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
//            $("#agntNo").append(
//                $('<option></option>').val("A1234").html("A1234 - 地政/代書")
//            );
            $("#agntNo").append(DOMPurify.sanitize("<option value='A1234'>A1234 - 地政/代書</option>"));
        }
        
        $("#filterBox").thickbox({
            // filter=請輸入欲查詢項目：
            title: i18n.cls3701m01["filter"],
            width: 450,
            height: 210,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                	var start = $("[name=applyTS_beg]").val().split("-");
                    var end = $("[name=applyTS_end]").val().split("-");
                    var startDate = new Date(start[0], start[1], start[2]);
                    var endData = new Date(end[0], end[1], end[2]);
                    if (!$("#filterForm").valid()) {
                        return;
                    }
                    if (startDate > endData) {
                        //cls3301v00.error03=起始日期不能大於結束日期
                        return CommonAPI.showErrorMessage(i18n.cls3701m01["cls3701v05.error03"]);
                    }
                    
                    if ($.trim($("[name=applyTS_end]").val()) != "" && $.trim($("[name=applyTS_beg]").val()) != "") {
                        //cls3301v00.error04=日期區間不能大於一個月
                    	if(parseInt(Math.abs(startDate - endData) / 1000 / 60 / 60 / 24) > 30){
                    		return CommonAPI.showErrorMessage(i18n.cls3701m01["cls3701v05.error04"]);
                    	}
                    }
//                    grid();

                    grid.jqGrid("setGridParam", {
                        postData: $.extend({ docStatus: viewstatus}, $filterForm.serializeData() ),
                        search: true
                    }).trigger("reloadGrid");

                    $.thickbox.close();
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }
        });
    }
        
    function saveAll(){
    	//判斷必填欄位
    	if(lastsel2!=""){
			if($("#"+lastsel2+"_ELF604_UPD_AMT").val()==""){
				$("#gridview").jqGrid('restoreRow',lastsel2);//還原那筆資料
				return CommonAPI.showErrorMessage(i18n.cls3701m01["cls3701v05.error05"]);
			}
			//Save回grid
	    	$("#gridview").jqGrid("saveRow", lastsel2, false, "clientArray");
	    	//把前一筆record存起來
	    	//把有Update的record index記起來
	    	if($('#gridview').jqGrid ('getRowData')[lastsel2-1].ELF604_UPD_AMT!=""){
				updateData.push(lastsel2);
				isChange = true;
				//alert("SAVE:"+updateData);
			}
		}
		var data = [];
		var rows = $('#gridview').jqGrid ('getRowData');
		if(updateData!=""){
			for (var i in updateData) {
				var index=updateData[i]-1;
                data.push(rows[index].LNF033_LOAN_NO+";"+rows[index].LNF030_CONTRACT+";"+rows[index].ELF604_UPD_AMT);
            }
			$.ajax({
				handler : _handler,
				type : "POST",
				dataType : "json",
				data :{
					'formAction' : 'batchEditRebate',
					datas: data
				}
				}).done(function(obj) {
					updateData=[];
					lastsel2="";
		        	$("#gridview").trigger("reloadGrid");
			});
		}
	}
    
    function chose_custId(){
        var my_dfd = $.Deferred();
        AddCustAction.open({
                handler: _handler,
                action : 'echo_custId',
                data : {
                },
                callback : function(json){
                    // 關掉 AddCustAction 的
                    $.thickbox.close();
                    my_dfd.resolve( json );
                }
            });
        return my_dfd.promise();
	}

	function build_submenu(dyna, rdoName, submenu){
    	$.each(submenu, function(k, v) {
    		dyna.push("   <p ><label id='_itemMenu_"+rdoName+"_"+k+"'><input type='radio' name='"+rdoName+"' value='"+k+"' class='required' />"+v+"</label></p>");
        });
    }
	
	$(".ui-jqgrid-title").css({"color":"red"});
});
