/*
 *JC Software.
 * copyright @jcs.com.tw 2003~2006. all right reserved.
 */
package tw.com.jcs.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <pre>
 * 這個程式是用來做一般檢誤用.
 * </pre>
 * 
 * @since 2004年10月05日
 * <AUTHOR>
 * @version 1,1
 *          <ul>
 *          <li>2004年10月05日
 *          </ul>
 */
public class CustidValidator {
    private static Logger logger = LoggerFactory.getLogger(CustidValidator.class);

    /**
     * 檢誤外國人統編.
     * 
     * @param id
     *            外國人統編 (機器可判讀護照編碼).
     * @return boolean
     */
    public static boolean verifyForeignerID(String id) {
        if (id == null || id.length() != 10)
            return false;
        try {
            int iCheckCode = 0;
            int iCheckSexCode = 0;
            String firstchar = id.toUpperCase().substring(0, 1);
            String secondchar = id.toUpperCase().substring(1, 2);

            // get 身分証號第一碼 [地區碼]
            // .......................................................................
            String areaID = "ABCDEFGHJKLMNPQRSTUVWXYZIO";
            for (int i = 0; i < areaID.length(); i++) {
                if (areaID.substring(i, i + 1).equals(firstchar)) {
                    iCheckCode = i + 10;
                    break;
                }
            }

            // 第一碼非地區碼不可
            if (iCheckCode == 0) {
                return false;
            }

            // 第二碼姓別碼
            if ("A".equals(secondchar)) {
                iCheckSexCode = 0;
            } else if ("B".equals(secondchar)) {
                iCheckSexCode = 1;
            } else if ("C".equals(secondchar)) {
                iCheckSexCode = 2;
            } else if ("D".equals(secondchar)) {
                iCheckSexCode = 3;
            } else {
                return false;
            }

            // 第3~10碼非數字不可
            if (!NumConverter.isNumeric(id.substring(2, 10))) {
                return false;
            }

            // 計算檢查碼
            iCheckCode = Integer.parseInt(String.valueOf(iCheckCode).substring(0, 1)) * 1 + (Integer.parseInt(String.valueOf(iCheckCode).substring(1, 2)) * 9) % 10;
            iCheckCode += (iCheckSexCode * 8) % 10;

            for (int i = 2; i < id.length() - 1; i++) {
                iCheckCode += (Integer.parseInt(id.substring(i, i + 1)) * (9 - i)) % 10;
            }

            iCheckCode = iCheckCode % 10;
            // 驗證結果
            return (iCheckCode == (10 - Integer.parseInt(id.substring(9, 10)))) ? true : false;

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return false;
        }
    }

    /**
     * 檢誤身分証號是否正確.
     * 
     * @param id
     *            String 身分証號
     * @return boolean
     */
    public static boolean verifyID(String id) {
        if (id == null || id.length() != 10)
            return false;
        try {
            int iCheckCode = 0;
            String firstchar = id.toUpperCase().substring(0, 1);

            // get 身分証號第一碼 [地區碼]
            // .......................................................................
            // String areaID = "ABCDEFGHJKLMNPQRSTUVWXYZIO";
            String areaID = "ABCDEFGHJKLMNPQRSTUVXYWZIO";

            for (int i = 0; i < areaID.length(); i++) {
                if (areaID.substring(i, i + 1).equals(firstchar)) {
                    iCheckCode = i + 1 + 9;
                    break;
                }
            }

            // 第一碼非地區碼
            if (iCheckCode == 0)
                return false;
            // 第2~10碼非數字
            if (!NumConverter.isNumeric(id.substring(1, 10)))
                return false;

            // 計算檢查碼
            iCheckCode = Integer.parseInt(String.valueOf(iCheckCode).substring(0, 1)) * 1 + Integer.parseInt(String.valueOf(iCheckCode).substring(1, 2)) * 9;

            iCheckCode += Integer.parseInt(id.substring(id.length() - 1, id.length()));

            for (int i = 1; i < id.length() - 1; i++) {
                iCheckCode += Integer.parseInt(id.substring(i, i + 1)) * (9 - i);
            }
            // 驗證結果
            return (iCheckCode % 10 == 0) ? true : false;

        } catch (Exception e) {

            return false;
        }
    }
}
