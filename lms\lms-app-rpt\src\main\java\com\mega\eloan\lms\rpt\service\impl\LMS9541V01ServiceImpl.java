/* 
 *  LMS9515ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service.impl;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.dao.L810M01ADao;
import com.mega.eloan.lms.mfaloan.service.impl.AbstractMFAloanJdbc;
import com.mega.eloan.lms.model.L810M01A;
import com.mega.eloan.lms.rpt.service.LMS9541V01Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class LMS9541V01ServiceImpl extends AbstractMFAloanJdbc implements
		LMS9541V01Service {
	@Resource
	L810M01ADao l810m01aDao;

	private static final Logger logger = LoggerFactory
			.getLogger(LMS9541V01ServiceImpl.class);

	@Override
	public boolean isRepeat(L810M01A data) {
		ISearch search = l810m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "brno",
				data.getBrno());
		search.addSearchModeParameters(SearchMode.EQUALS, "rptType",
				data.getRptType());
		search.addSearchModeParameters(SearchMode.EQUALS, "useType",
				data.getUseType());
		if (!l810m01aDao.find(search).isEmpty())
			return true;
		else
			return false;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				// set updater and updateTime
				try {
					if (Util.isEmpty(model.get(EloanConstants.OID))) {
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}
					model.set("updater", user.getUserId());
					model.set("updateTime", CapDate.getCurrentTimestamp());
				} catch (CapException e) {
					logger.error("CapException!!", e);
				}

				if (model instanceof L810M01A) {
					l810m01aDao.save(((L810M01A) model));
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model instanceof L810M01A) {
				l810m01aDao.delete(((L810M01A) model));
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L810M01A.class) {
			return l810m01aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L810M01A.class) {
			L810M01A model = Util.isEmpty(oid) ? null : l810m01aDao
					.findByOid(oid);
			return (T) (model == null ? null : model);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L810M01A.class) {
			List<L810M01A> list = Util.isEmpty(mainId) ? null : l810m01aDao
					.findByMainId(mainId);
			return (list == null ? null : list);
		}
		return null;
	}

	@Override
	public List<Map<String, Object>> findMisData(String kindNo,
			boolean countData, boolean tooMuch, String brno) {
		List<Map<String, Object>> result = new LinkedList<Map<String, Object>>();
		//MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (tooMuch) {
			if ("5".equals(kindNo)) {
				for (int i = 1; i < 5; i++) {
					List<Map<String, Object>> data = getJdbc()
							.queryForListWithMax(
									"MIS.MORTGAGE.RECORD",
									new String[] { String.valueOf(BASE),
											String.valueOf(BASE), brno,
											String.valueOf(i) });
					result.addAll(data);
				}
			} else {
				result = getJdbc().queryForListWithMax(
						"MIS.MORTGAGE.RECORD",
						new String[] { String.valueOf(BASE),
								String.valueOf(BASE), brno, kindNo });
			}
		} else {
			//brno = user.getUnitNo();
			String query = "MIS.MORTGAGE";

			if (UtilConstants.BankNo.授管處.equals(brno)
					|| UtilConstants.BankNo.資訊處.equals(brno)) {
				// 900資訊處 或 918授管處=>全分行
				brno = "%";
			}
			// query="MIS.MORTGAGE.(是否加總)"
			query = countData ? query + ".COUNT" : query + ".RECORD";

			if ("5".equals(kindNo)) {// 全選=>把1~4串起來
				for (int i = 1; i < 5; i++) {
					List<Map<String, Object>> data = getJdbc()
							.queryForListWithMax(
									query,
									new String[] { String.valueOf(BASE),
											String.valueOf(BASE), brno,
											String.valueOf(i) });
					if (countData && i != 1) {

						Map<String, Object> oneType = data.get(0), record = result
								.get(0);
						BigDecimal count = Util.parseBigDecimal(oneType
								.get("TOT_CASE"));
						count = count.add(Util.parseBigDecimal(record
								.get("TOT_CASE")));
						record.put("TOT_CASE", count.toString());

						count = Util.parseBigDecimal(oneType.get("APP_MONEY"));
						count = count.add(Util.parseBigDecimal(record
								.get("APP_MONEY")));
						record.put("APP_MONEY", count.toString());

						count = Util.parseBigDecimal(oneType.get("APP_MONEY"));
						count = count.add(Util.parseBigDecimal(record
								.get("APP_MONEY")));
						record.put("APP_MONEY", count.toString());

						count = Util.parseBigDecimal(oneType.get("FAV_LOAN"));
						count = count.add(Util.parseBigDecimal(record
								.get("FAV_LOAN")));
						record.put("FAV_LOAN", count.toString());
					} else {
						result.addAll(data);
					}
				}
			} else {
				result = getJdbc().queryForListWithMax(
						query,
						new String[] { String.valueOf(BASE),
								String.valueOf(BASE), brno, kindNo });
			}
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> getDataBrno(String kindNo) {
		List<Map<String, Object>> result = null;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brno = user.getUnitNo(), query = "MIS.MORTGAGE.LISTBRNO";

		if (UtilConstants.BankNo.授管處.equals(brno)
				|| UtilConstants.BankNo.資訊處.equals(brno)) {
			// 900資訊處 或 918授管處=>全分行
			brno = "%";
		}
		// query="MIS.MORTGAGE.(是否加總)"

		if ("5".equals(kindNo)) {// 全選=>把1~4串起來
			result = new LinkedList<Map<String, Object>>();
			for (int i = 1; i < 5; i++) {
				List<Map<String, Object>> data = getJdbc().queryForListWithMax(
						query, new String[] { brno, String.valueOf(i) });
				result.addAll(data);
			}
		} else {
			result = getJdbc().queryForListWithMax(query,
					new String[] { brno, kindNo });
		}
		return result;
	}

}
