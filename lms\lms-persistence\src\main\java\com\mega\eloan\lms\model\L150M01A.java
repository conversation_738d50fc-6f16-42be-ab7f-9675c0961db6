/* 
 * L150M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;
import java.util.Set;

import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 小放會會議紀錄檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L150M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L150M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	
	/**
	 * JOIN條件 L150A01A．關聯檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l150m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L150A01A> l150a01a;

	public Set<L150A01A> getL150a01a() {
		return l150a01a;
	}

	public void setL150a01a(Set<L150A01A> l150a01a) {
		this.l150a01a = l150a01a;
	}

	/**
	 * 建檔來源
	 * <p/>
	 * 100/08/24新增<br/>
	 * 1.海外 2.企金 3.個金
	 */
	@Column(name = "CREATETYPE", length = 1, columnDefinition = "CHAR(1)")
	private String createType;

	/** 日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "MEETINGDATE", columnDefinition = "Date")
	private Date meetingDate;

	/**
	 * 時間
	 * <p/>
	 * 100/09/14調整<br/>
	 * TIMESTAMP ( CHAR(5)<br/>
	 * HH:MM
	 */
	@Column(name = "MEETINGTIME", length = 5, columnDefinition = "CHAR(5)")
	private String meetingTime;

	/**
	 * 地點
	 * <p/>
	 * 64個全型字
	 */
	@Column(name = "MEETINGPLACE", length = 192, columnDefinition = "VARCHAR(192)")
	private String meetingPlace;

	/** 主席 **/
	@Column(name = "CHAIRMAN", length = 6, columnDefinition = "CHAR(6)")
	private String chairMan;

	/**
	 * 主席其他
	 * <p/>
	 * 100/09/29調整<br/>
	 * 將其他欄位值存入 <br/>
	 * 102.02.18 欄位擴大 30 -> 120
	 */
	@Column(name = "CHAIRMANCN", length = 120, columnDefinition = "VARCHAR(120)")
	private String chairManCN;

	/** 遵守法令主管 **/
	@Column(name = "LAWSBOSS", length = 6, columnDefinition = "CHAR(6)")
	private String lawsBoss;

	/**
	 * 遵守法令主管其他
	 * <p/>
	 * 100/09/29調整<br/>
	 * 將其他欄位值存入 <br/>
	 * 102.02.18 欄位擴大 30 -> 120
	 */
	@Column(name = "LAWSBOSSCN", length = 120, columnDefinition = "VARCHAR(120)")
	private String lawsBossCN;

	/** 帳戶管理員 **/
	@Column(name = "ACCOUNTING", length = 6, columnDefinition = "CHAR(6)")
	private String accounting;

	/**
	 * 帳戶管理員其他
	 * <p/>
	 * 100/09/29調整<br/>
	 * 將其他欄位值存入 <br/>
	 * 102.02.18 欄位擴大 30 -> 120
	 */
	@Column(name = "ACCOUNTINGCN", length = 120, columnDefinition = "VARCHAR(120)")
	private String accountingCN;

	/** 紀錄 **/
	@Column(name = "RECORDER", length = 6, columnDefinition = "CHAR(6)")
	private String recorder;

	/**
	 * 紀錄其他
	 * <p/>
	 * 100/09/29調整<br/>
	 * 將其他欄位值存入 <br/>
	 * 102.02.18 欄位擴大 30 -> 120
	 */
	@Column(name = "RECORDERCN", length = 120, columnDefinition = "VARCHAR(120)")
	private String recorderCN;

	/**
	 * 出席人員
	 * <p/>
	 * XXXXXX,XXXXXX,XXXXXX…<br/>
	 * 128個全型字
	 */
	@Column(name = "PRESENT", length = 384, columnDefinition = "VARCHAR(384)")
	private String present;

	/**
	 * 案由
	 * <p/>
	 * 101/06/25調整<br/>
	 * VARCHAR(1536)(VARCHAR(4096)
	 */
	@Column(name = "GIST", length = 4096, columnDefinition = "VARCHAR(4096)")
	private String gist;

	/**
	 * 說明
	 * <p/>
	 * 101/03/19調整<br/>
	 * 1024個全型字
	 * 
	 * 102/02/19 由 VARCHAR(3072)	改 CLOB
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "DESCRIPTION", length = 1048576, columnDefinition="CLOB")
	private String description;

	/**
	 * 決議
	 * <p/>
	 * 101/03/19調整<br/>
	 * 512個全型字
	 * 
	 * 102/02/19 由 VARCHAR(1536)	改 CLOB
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "RESOLUTION", length = 1048576, columnDefinition="CLOB")
	private String resolution;

	/**
	 * RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	@Column(name = "RPTID", length = 32, columnDefinition = "VARCHAR(32)")
	private String rptId;

	/**
	 * 取得建檔來源
	 * <p/>
	 * 100/08/24新增<br/>
	 * 1.海外 2.企金 3.個金
	 */
	public String getCreateType() {
		return this.createType;
	}

	/**
	 * 設定建檔來源
	 * <p/>
	 * 100/08/24新增<br/>
	 * 1.海外 2.企金 3.個金
	 **/
	public void setCreateType(String value) {
		this.createType = value;
	}

	/** 取得日期 **/
	public Date getMeetingDate() {
		return this.meetingDate;
	}

	/** 設定日期 **/
	public void setMeetingDate(Date value) {
		this.meetingDate = value;
	}

	/**
	 * 取得時間
	 * <p/>
	 * 100/09/14調整<br/>
	 * TIMESTAMP ( CHAR(5)<br/>
	 * HH:MM
	 */
	public String getMeetingTime() {
		return this.meetingTime;
	}

	/**
	 * 設定時間
	 * <p/>
	 * 100/09/14調整<br/>
	 * TIMESTAMP ( CHAR(5)<br/>
	 * HH:MM
	 **/
	public void setMeetingTime(String value) {
		this.meetingTime = value;
	}

	/**
	 * 取得地點
	 * <p/>
	 * 64個全型字
	 */
	public String getMeetingPlace() {
		return this.meetingPlace;
	}

	/**
	 * 設定地點
	 * <p/>
	 * 64個全型字
	 **/
	public void setMeetingPlace(String value) {
		this.meetingPlace = value;
	}

	/** 取得主席 **/
	public String getChairMan() {
		return this.chairMan;
	}

	/** 設定主席 **/
	public void setChairMan(String value) {
		this.chairMan = value;
	}

	/**
	 * 取得主席其他
	 * <p/>
	 * 100/09/29調整<br/>
	 * 將其他欄位值存入
	 */
	public String getChairManCN() {
		return this.chairManCN;
	}

	/**
	 * 設定主席其他
	 * <p/>
	 * 100/09/29調整<br/>
	 * 將其他欄位值存入
	 **/
	public void setChairManCN(String value) {
		this.chairManCN = value;
	}

	/** 取得遵守法令主管 **/
	public String getLawsBoss() {
		return this.lawsBoss;
	}

	/** 設定遵守法令主管 **/
	public void setLawsBoss(String value) {
		this.lawsBoss = value;
	}

	/**
	 * 取得遵守法令主管其他
	 * <p/>
	 * 100/09/29調整<br/>
	 * 將其他欄位值存入
	 */
	public String getLawsBossCN() {
		return this.lawsBossCN;
	}

	/**
	 * 設定遵守法令主管其他
	 * <p/>
	 * 100/09/29調整<br/>
	 * 將其他欄位值存入
	 **/
	public void setLawsBossCN(String value) {
		this.lawsBossCN = value;
	}

	/** 取得帳戶管理員 **/
	public String getAccounting() {
		return this.accounting;
	}

	/** 設定帳戶管理員 **/
	public void setAccounting(String value) {
		this.accounting = value;
	}

	/**
	 * 取得帳戶管理員其他
	 * <p/>
	 * 100/09/29調整<br/>
	 * 將其他欄位值存入
	 */
	public String getAccountingCN() {
		return this.accountingCN;
	}

	/**
	 * 設定帳戶管理員其他
	 * <p/>
	 * 100/09/29調整<br/>
	 * 將其他欄位值存入
	 **/
	public void setAccountingCN(String value) {
		this.accountingCN = value;
	}

	/** 取得紀錄 **/
	public String getRecorder() {
		return this.recorder;
	}

	/** 設定紀錄 **/
	public void setRecorder(String value) {
		this.recorder = value;
	}

	/**
	 * 取得紀錄其他
	 * <p/>
	 * 100/09/29調整<br/>
	 * 將其他欄位值存入
	 */
	public String getRecorderCN() {
		return this.recorderCN;
	}

	/**
	 * 設定紀錄其他
	 * <p/>
	 * 100/09/29調整<br/>
	 * 將其他欄位值存入
	 **/
	public void setRecorderCN(String value) {
		this.recorderCN = value;
	}

	/**
	 * 取得出席人員
	 * <p/>
	 * XXXXXX,XXXXXX,XXXXXX…<br/>
	 * 128個全型字
	 */
	public String getPresent() {
		return this.present;
	}

	/**
	 * 設定出席人員
	 * <p/>
	 * XXXXXX,XXXXXX,XXXXXX…<br/>
	 * 128個全型字
	 **/
	public void setPresent(String value) {
		this.present = value;
	}

	/**
	 * 取得案由
	 * <p/>
	 * 101/06/25調整<br/>
	 * VARCHAR(1536)(VARCHAR(4096)
	 */
	public String getGist() {
		return this.gist;
	}

	/**
	 * 設定案由
	 * <p/>
	 * 101/06/25調整<br/>
	 * VARCHAR(1536)(VARCHAR(4096)
	 **/
	public void setGist(String value) {
		this.gist = value;
	}

	/**
	 * 取得說明
	 * <p/>
	 * 101/03/19調整<br/>
	 * 1024個全型字
	 */
	public String getDescription() {
		return this.description;
	}

	/**
	 * 設定說明
	 * <p/>
	 * 101/03/19調整<br/>
	 * 1024個全型字
	 **/
	public void setDescription(String value) {
		this.description = value;
	}

	/**
	 * 取得決議
	 * <p/>
	 * 101/03/19調整<br/>
	 * 512個全型字
	 */
	public String getResolution() {
		return this.resolution;
	}

	/**
	 * 設定決議
	 * <p/>
	 * 101/03/19調整<br/>
	 * 512個全型字
	 **/
	public void setResolution(String value) {
		this.resolution = value;
	}

	/**
	 * 取得RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	public String getRptId() {
		return this.rptId;
	}

	/**
	 * 設定RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 **/
	public void setRptId(String value) {
		this.rptId = value;
	}
}
