package com.mega.eloan.lms.lns.panels;

import com.mega.eloan.common.panels.Panel;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;

/**
 * <pre>
 * 基本資料(企金授權外)
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class LMSS01APanel extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public LMSS01APanel(String id) {
		super(id);
	}
	
	public LMSS01APanel(String id, boolean updatePanelName) {
		super(id, updatePanelName);

	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		new DocLogPanel("_docLog").processPanelData(model, params);
	}
}
