/* 
 * L120S25C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** BIS評估表參數檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S25C", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L120S25C extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 參數名稱 **/
	@Size(max = 32)
	@Column(name = "PARAMTYPE", length = 32, columnDefinition = "VARCHAR(32)")
	private String paramType;

	/** 參數日期 **/
	@Size(max = 10)
	@Column(name = "PARAMDATE", length = 10, columnDefinition = "CHAR(10)")
	private String paramDate;

	/** 參數KEY **/
	@Size(max = 32)
	@Column(name = "PARAMKEY", length = 32, columnDefinition = "VARCHAR(32)")
	private String paramKey;

	/** 參數值 **/
	@Size(max = 600)
	@Column(name = "PARAMVAL", length = 600, columnDefinition = "VARCHAR(600)")
	private String paramVal;

	/** 參數說明 **/
	@Size(max = 600)
	@Column(name = "PARAMDSCR", length = 600, columnDefinition = "VARCHAR(600)")
	private String paramDscr;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得參數名稱 **/
	public String getParamType() {
		return this.paramType;
	}

	/** 設定參數名稱 **/
	public void setParamType(String value) {
		this.paramType = value;
	}

	/** 取得參數日期 **/
	public String getParamDate() {
		return this.paramDate;
	}

	/** 設定參數日期 **/
	public void setParamDate(String value) {
		this.paramDate = value;
	}

	/** 取得參數KEY **/
	public String getParamKey() {
		return this.paramKey;
	}

	/** 設定參數KEY **/
	public void setParamKey(String value) {
		this.paramKey = value;
	}

	/** 取得參數值 **/
	public String getParamVal() {
		return this.paramVal;
	}

	/** 設定參數值 **/
	public void setParamVal(String value) {
		this.paramVal = value;
	}

	/** 取得參數說明 **/
	public String getParamDscr() {
		return this.paramDscr;
	}

	/** 設定參數說明 **/
	public void setParamDscr(String value) {
		this.paramDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
