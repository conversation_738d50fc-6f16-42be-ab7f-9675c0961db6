
package com.mega.eloan.lms.mfaloan.service.impl;

import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.LNF07A;
import com.mega.eloan.lms.mfaloan.service.LNLNF07AService;


@Service
public class LNLNF07AServiceImpl extends AbstractMFAloanJdbc implements
		LNLNF07AService {

	@Override
	public void insert(List<LNF07A> list) {
		List<Object[]> batchValues = new ArrayList<Object[]>();
		for(LNF07A bean : list){
			Object[] o = new Object[11];
			//===
			o[0] = Util.trim(bean.getLnf07a_key_1());
			o[1] = Util.trim(bean.getLnf07a_key_2());
			o[2] = Util.trim(bean.getLnf07a_key_3());
			o[3] = Util.trim(bean.getLnf07a_key_4());
			o[4] = Util.trim(bean.getLnf07a_key_5());
			o[5] = Util.trim(bean.getLnf07a_explain());
			o[6] = Util.trim(bean.getLnf07a_content_1());
			o[7] = Util.trim(bean.getLnf07a_content_2());
			o[8] = Util.trim(bean.getLnf07a_content_3());
			o[9] = Util.trim(bean.getLnf07a_content_4());
			o[10] = Util.trim(bean.getLnf07a_content_5());
			//===
			batchValues.add(o);
		}
		this.getJdbc().batchUpdate(
				"LNF07A.insert",
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR
						, Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR
						, Types.CHAR }, batchValues);
	}
	
	@Override
	public void delete_by_key1_key2(List<LNF07A> list){
		List<Object[]> batchValues = new ArrayList<Object[]>();
		for(LNF07A bean : list){
			Object[] o = new Object[2];
			//===
			o[0] = Util.trim(bean.getLnf07a_key_1());
			o[1] = Util.trim(bean.getLnf07a_key_2());
			//===
			batchValues.add(o);
		}
		this.getJdbc().batchUpdate("LNF07A.delete_by_key1_key2",
				new int[] { Types.CHAR, Types.CHAR }, batchValues);
	}	
	
	@Override
	public List<LNF07A> sel_by_key1(String key1){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("LNF07A.sel_by_key1", new String[]{key1});
		return toLNF07A(rowData);
	}
	
	@Override
	public List<LNF07A> sel_by_key1_orderBykey3Desc(String key1){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("LNF07A.sel_by_key1_orderBykey3Desc", new String[]{key1});
		return toLNF07A(rowData);
	}
	
	@Override
	public List<LNF07A> sel_by_key1_key2(String key1, String key2){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("LNF07A.sel_by_key1_key2", new String[]{key1, key2});
		return toLNF07A(rowData);
	}
	
	@Override
	public List<LNF07A> sel_by_key1_key2_key3(String key1, String key2, String key3){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("LNF07A.sel_by_key1_key2_key3", new String[]{key1, key2, key3});
		return toLNF07A(rowData);
	}
	
	
	private List<LNF07A> toLNF07A(List<Map<String, Object>> rowData){
		List<LNF07A> list = new ArrayList<LNF07A>();
		for (Map<String, Object> row : rowData) {
			LNF07A model = new LNF07A();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
	
	@Override
	public void delete_by_key1_key2_key3(List<LNF07A> list){
		List<Object[]> batchValues = new ArrayList<Object[]>();
		for(LNF07A bean : list){
			Object[] o = new Object[3];
			//===
			o[0] = Util.trim(bean.getLnf07a_key_1());
			o[1] = Util.trim(bean.getLnf07a_key_2());
			o[2] = Util.trim(bean.getLnf07a_key_3());
			//===
			batchValues.add(o);
		}
		this.getJdbc().batchUpdate("LNF07A.delete_by_key1_key2_key3",
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR }, batchValues);
	}	
	
	@Override
	public List<Map<String, Object>> findByCntrNoControl(){
		return this.getJdbc().queryForListWithMax("LNF07A.selCntrNoControl", null);
	}

	@Override
	public int upd_A32(String lnf07a_key_1, String lnf07a_key_2,
			String lnf07a_key_4, String lnf07a_content_4) {
		String lnf07a_content_2 = CapDate.convertTimestampToString(CapDate.getCurrentTimestamp(), "yyyy-MM-dd HH:mm:ss");
		return this.getJdbc().update("LNF07A.updCntrNoControl_A32", new Object[]{
				lnf07a_key_4, lnf07a_content_4, 
				lnf07a_content_2, 
				lnf07a_key_1, lnf07a_key_2
		});
		
	}			
}
