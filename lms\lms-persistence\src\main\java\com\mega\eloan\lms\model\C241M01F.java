package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 防杜代辦覆審_承辦行員 **/
@Entity
@Table(name = "C241M01F", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "caseMainId", "rel_type", "staffNo" }))
public class C241M01F extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** mainId **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** caseMainId **/
	@Column(name = "CASEMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String caseMainId;
	
	/** 承辦行員類型{1引介人, 2經辦人員, 3覆核主管} */
	@Column(name = "REL_TYPE", length = 1, columnDefinition = "CHAR(1)")
	private String rel_type;

	/** 行員代碼 **/
	@Column(name = "STAFFNO", length = 6, columnDefinition = "CHAR(6)")
	private String staffNo;

	/**
	 * L1. 分行經辦(Appraiser)<br>
	 * 營運中心經辦(Area_Appraiser) 授管處經辦(Head_Appraiser)<br>
	 * L2. 帳戶管理員(AO)<br>
	 * L3. 分行授信/覆核主管(Boss) 母行覆核 營運中心襄理(Area_Manager) 授管處覆核(Head_Manager)
	 * (母行/授管處/營運中心)<br>
	 * L4. 分行覆核主管(ReCheck) 母行覆核主管 營運中心覆核主管 授管處覆核主管 (母行/授管處/營運中心)<br>
	 * L5. 單位/授權主管(Manager) 營運中心副營運長(Area_Sub_Leader) 授管處副處長(Head_Sub_Leader)
	 * (母行/授管處/營運中心) <br>
	 * L6. 分行單位主管() 母行單位主管 營運中心營運長(Area_Leader) 授管處協理(Head_Leader) (母行/授管處/營運中心)
	 * 101/04/27新增( for 加、澳、泰) <br>
	 * L7. 提會登錄經辦 <br>
	 * L8. 提會放行主管
	 */
	@Column(name = "STAFFJOB", length = 2, columnDefinition = "CHAR(2)")
	private String staffJob;

	/** 是否承辦行員{Y/N} **/
	@Column(name = "REL_MATCH", length = 1, columnDefinition = "CHAR(1)")
	private String rel_match;
	
	/** 案件號碼-年度 */
	@Column(name="CASEYEAR", columnDefinition="DECIMAL(4,0)")
	private BigDecimal caseYear;
	
	/** 案件號碼-流水號 */
	@Column(name="CASESEQ", columnDefinition="DECIMAL(5,0)")
	private BigDecimal caseSeq;
	
	/** 依rel_type顯示順序 */
	@Column(name="SEQ", columnDefinition="DECIMAL(2,0)")
	private BigDecimal seq;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;
	

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	
	public String getCaseMainId() {
		return caseMainId;
	}
	public void setCaseMainId(String caseMainId) {
		this.caseMainId = caseMainId;
	}

	/** 承辦行員類型{1引介人, 2經辦人員, 3覆核主管} */
	public String getRel_type() {
		return rel_type;
	}
	/** 承辦行員類型{1引介人, 2經辦人員, 3覆核主管} */
	public void setRel_type(String rel_type) {
		this.rel_type = rel_type;
	}
	
	/** 取得行員代碼 **/
	public String getStaffNo() {
		return this.staffNo;
	}

	/** 設定行員代碼 **/
	public void setStaffNo(String value) {
		this.staffNo = value;
	}

	/**
	 * 取得人員職稱
	 * <p/>
	 * L1. 分行經辦<br/>
	 * (授管處/營運中心)<br/>
	 * L2. 帳戶管理員<br/>
	 * L3. 分行授信/覆核主管<br/>
	 * (母行)<br/>
	 * L4. 分行覆核主管<br/>
	 * (母行/授管處/營運中心)<br/>
	 * L5. 單位/授權主管<br/>
	 * (母行)<br/>
	 * L6. 分行單位主管<br/>
	 * (母行/授管處/營運中心)<br/>
	 * 徵信報告或資信簡表簽核層級<br/>
	 * C1. 徵信經辦<br/>
	 * C2. 徵信覆核主管<br/>
	 * C3. 經理/副理<br/>
	 * C4. 處長/協理/主任/副主任<br/>
	 * C5. 徵信單位主管
	 */
	public String getStaffJob() {
		return this.staffJob;
	}

	/**
	 * 設定人員職稱
	 * <p/>
	 * L1. 分行經辦<br/>
	 * (授管處/營運中心)<br/>
	 * L2. 帳戶管理員<br/>
	 * L3. 分行授信/覆核主管<br/>
	 * (母行)<br/>
	 * L4. 分行覆核主管<br/>
	 * (母行/授管處/營運中心)<br/>
	 * L5. 單位/授權主管<br/>
	 * (母行)<br/>
	 * L6. 分行單位主管<br/>
	 * (母行/授管處/營運中心)<br/>
	 * 徵信報告或資信簡表簽核層級<br/>
	 * C1. 徵信經辦<br/>
	 * C2. 徵信覆核主管<br/>
	 * C3. 經理/副理<br/>
	 * C4. 處長/協理/主任/副主任<br/>
	 * C5. 徵信單位主管
	 **/
	public void setStaffJob(String value) {
		this.staffJob = value;
	}

	/** 是否承辦行員{Y/N} **/
	public String getRel_match() {
		return rel_match;
	}
	/** 是否承辦行員{Y/N} **/
	public void setRel_match(String rel_match) {
		this.rel_match = rel_match;
	}
	
	/** 案件號碼-年度 */
	public BigDecimal getCaseYear() {
		return caseYear;
	}
	/** 案件號碼-年度 */
	public void setCaseYear(BigDecimal caseYear) {
		this.caseYear = caseYear;
	}
	
	/** 案件號碼-流水號 */
	public BigDecimal getCaseSeq() {
		return caseSeq;
	}
	/** 案件號碼-流水號 */
	public void setCaseSeq(BigDecimal caseSeq) {
		this.caseSeq = caseSeq;
	}

	/** 依rel_type顯示順序 */
	public void setSeq(BigDecimal seq) {
		this.seq = seq;
	}
	/** 依rel_type顯示順序 */
	public BigDecimal getSeq() {
		return seq;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
