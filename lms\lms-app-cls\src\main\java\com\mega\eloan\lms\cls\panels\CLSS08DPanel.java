/**
 *  CLSS08DPanel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 相關文件(個金) - 擔保品元件
 * </pre>
 * 
 * @since 2013/4/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/4/8,REX,new
 *          </ul>
 */
public class CLSS08DPanel extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public CLSS08DPanel(String id) {
		super(id);
	}
}
