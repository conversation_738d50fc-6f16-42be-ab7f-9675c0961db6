/* 
 * L140S08ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L140S08ADao;
import com.mega.eloan.lms.model.L140S08A;

/** 代碼轉換檔 **/
@Repository
public class L140S08ADaoImpl extends LMSJpaDao<L140S08A, String>
	implements L140S08ADao {

	@Override
	public L140S08A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S08A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L140S08A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L140S08A> findByIndex01(String itemName, String mainItem){
		ISearch search = createSearchTemplete();
		List<L140S08A> list = null;
		if (itemName != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemName", itemName);
		if (mainItem != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainItem", mainItem);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140S08A> findByIndex02(String itemName, String subItem){
		ISearch search = createSearchTemplete();
		List<L140S08A> list = null;
		if (itemName != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemName", itemName);
		if (subItem != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "subItem", subItem);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}