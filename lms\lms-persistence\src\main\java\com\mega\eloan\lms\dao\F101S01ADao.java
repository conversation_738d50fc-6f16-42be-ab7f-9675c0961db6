/* 
 * F101S01ADao.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.F101M01A;
import com.mega.eloan.lms.model.F101S01A;

/**
 * <pre>
 * F101S01A dao interface.
 * </pre>
 * 
 * @since 2011/7/20
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/7/20,Sunkist Wang,new
 *          <li>2011/8/13,Sunkist Wang,add
 *          {@link F101S01ADao#deleteByMeta(F101M01A)}</li>
 *          </ul>
 */
public interface F101S01ADao extends IGenericDao<F101S01A> {

    /**
     * 刪除所有資料
     * 
     * @param meta
     *            F101M01A
     * @return int
     */
    int deleteByMeta(F101M01A meta);

    /**
     * 依meta 的 mainId 、報表別以及科目代號取得財務3表。
     * 
     * @param mainId
     *            the F101S01A's mainId
     * @param tab
     *            報表別
     * @param subNos
     *            subNos array
     * @return F101S01A 3 table of FSS
     */
    List<F101S01A> findF101S01AByMetaAndSubNo(String mainId, String tab,
            String[] subNos);
}
