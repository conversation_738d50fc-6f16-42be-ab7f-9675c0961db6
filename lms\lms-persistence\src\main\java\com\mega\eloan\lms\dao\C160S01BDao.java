/* 
 * C160S01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C160S01B;

/** 主從債務人資料表檔 **/
public interface C160S01BDao extends IGenericDao<C160S01B> {

	C160S01B findByOid(String oid);
	
	List<C160S01B> findByMainId(String mainId);
	
	C160S01B findByUniqueKey(String mainId, String refmainId, String custId, String dupNo
			, String cntrNo, String rId, String rDupNo, String rType);

	List<C160S01B> findByMainIdRefMainId(String mainId, String refmainId);
	
	List<C160S01B> findByMainIdCntrno(String mainId, String Cntrno);
}