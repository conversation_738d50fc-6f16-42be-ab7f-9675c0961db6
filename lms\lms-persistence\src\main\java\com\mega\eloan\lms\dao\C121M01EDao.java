/* 
 * C121M01EDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C121M01E;

/** 海外消金評等簽章欄檔 **/
public interface C121M01EDao extends IGenericDao<C121M01E> {

	C121M01E findByOid(String oid);
	
	List<C121M01E> findByMainId(String mainId); //order by seq,oid
	
	C121M01E findByUniqueKey(String mainId, String branchType, String branchId, String staffNo, String staffJob);

	List<C121M01E> findByIndex01(String mainId, String branchType, String branchId, String staffNo, String staffJob);
}