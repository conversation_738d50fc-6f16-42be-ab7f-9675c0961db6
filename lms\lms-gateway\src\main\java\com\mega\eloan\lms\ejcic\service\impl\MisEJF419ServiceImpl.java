/* 
 * MisEJF419ServiceImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service.impl;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import tw.com.iisi.cap.util.CapString;

import com.mega.eloan.lms.ejcic.service.MisEJF419Service;
import com.mega.eloan.lms.etch.service.MisMsg001Service;
import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;

/**
 * <pre>
 * MIS.BAM087 ->MIS.EJV41901
 * </pre>
 * 
 * @since 2011/11/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/11,CP,new
 *          </ul>
 */
@Service
public class MisEJF419ServiceImpl extends AbstractEjcicJdbc implements
		MisEJF419Service {

	@Resource
	MisGrpcmpService misGrpcmpSrv;
//	@Resource
//	MisEJF334Service misEJF334Srv;
	@Resource
	MisMsg001Service misMsg001Srv;
//	@Resource
//	MisElcus25Service cmcus25Srv;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.ejcic.service.MisEJF419Service#getELLNF(java.lang.
	 * String, java.lang.String, java.lang.String)
	 */
	@Override
	public List<Map<String, Object>> getELLNF(String custId, String prodid,
			String yyyMMdd) {
		return getJdbc().queryForList("BAM087.findByIdQDateEllnf",
				new String[] { custId, prodid, yyyMMdd });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.ejcic.service.MisEJF419Service#countAccountCodeYAmtPos
	 * (java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public int countAccountCodeYAmtPos(String custId, String prodid,
			String yyyMMdd) {
		return getJdbc().queryForInt("BAM087.countByIdQDateProdidAccCode",
				new String[] { custId, yyyMMdd, prodid });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.ejcic.service.MisBamService#getELLNFYYYYMM(java.lang
	 * .String, java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public List<Map<String, Object>> getELLNFYYYYMM(String custId,
			String prodid, String yyyMMdd) {
		return getJdbc().queryForList("BAM087.findEllnfYYYYMM",
				new String[] { custId, prodid, yyyMMdd });
	}

	@Override
	public List<Map<String, Object>> findLoanInfo(String custId, String prodid,
			String yyyMMdd) {
		return getJdbc().queryForList(
				"BAM087.findLoanInfo",
				new String[] { custId, prodid, yyyMMdd, custId, prodid,
						yyyMMdd, custId });
	}

	@Override
	public List<Map<String, Object>> getPromiseBussLoanPromiseValP1(
			String custId, String bc, String prodid) {
		return getJdbc().queryForList("BAM087.getPromiseBussLoanPromiseValP1",
				new String[] { custId, prodid, bc });
	}

	@Override
	public List<Map<String, Object>> getPromiseBussLoanPromiseValNotP1(
			String custId, String bc, String prodid) {
		return getJdbc().queryForList(
				"BAM087.getPromiseBussLoanPromiseValNotP1",
				new String[] { custId, prodid, bc });
	}

	@Override
	public List<Map<String, Object>> findLoanAmountAndBalance(String qDate,
			List<String> compIds, List<String> prodIds, List<String> bcs) {
		String sql = getSqlBySqlId("BAM087.findLoanAmountAndBalance");
		String compIdSql = StringUtils.repeat("?,", compIds.size());
		String prodIdSql = StringUtils.repeat("?,", prodIds.size());
		String bcSql = StringUtils.repeat("?,", bcs.size());
		sql = MessageFormat.format(
				sql,
				new Object[] {
						prodIdSql.substring(0, prodIdSql.lastIndexOf(',')),
						compIdSql.substring(0, compIdSql.lastIndexOf(',')),
						bcSql.substring(0, bcSql.lastIndexOf(',')) });
		List<String> args = new ArrayList<String>();
		for (int i = 0; i < 3; i++) {
			args.addAll(prodIds);
			args.addAll(compIds);
			//#1565引金融機構往來
//			args.add(qDate);
		}
		for (int i = 0; i < 8; i++) {
			args.addAll(bcs);
		}
		return getJdbc().queryForList(sql, args.toArray());
	}

	public List<Map<String, Object>> findLoanAmountAndBalance(
			List<String> compIds, List<String> prodIds, List<String> bcs) {
		String sql = getSqlBySqlId("BAM087.findLoanAmountAndBalance2");
		String compIdSql = StringUtils.repeat("?,", compIds.size());
		String prodIdSql = StringUtils.repeat("?,", prodIds.size());
		String bcSql = StringUtils.repeat("?,", bcs.size());
		sql = MessageFormat.format(
				sql,
				new Object[] {
						prodIdSql.substring(0, prodIdSql.lastIndexOf(',')),
						compIdSql.substring(0, compIdSql.lastIndexOf(',')),
						bcSql.substring(0, bcSql.lastIndexOf(',')) });
		List<String> args = new ArrayList<String>();
		for (int i = 0; i < 3; i++) {
			args.addAll(prodIds);
			args.addAll(compIds);
		}
		for (int i = 0; i < 8; i++) {
			args.addAll(bcs);
		}
		return getJdbc().queryForList(sql, args.toArray());
	}

	@Override
	public Map<String, Object> getQryDate(String compId) {
		List<Map<String, Object>> resList = getJdbc().queryForList(
				"BAM087.getBam087QryDate", new String[] { compId });
		return CollectionUtils.isEmpty(resList) ? null : resList.get(0);
	}

	@Override
	public List<Map<String, Object>> getBam087_1(String compId, String yyy,
			String mm) {
		return getJdbc().queryForList("BAM087.getBam087_1",
				new String[] { compId, yyy, mm, compId, yyy, mm, compId });
	}

	@Override
	public Map<String, Object> getLoanAmtById(String compId) {
		return getJdbc().queryForMap("BAM087.getLoanAmtById",
				new String[] { compId });
	}

	@Override
	public Map<String, Object> getLoanAmtByIdAndDate(String compId, String yyy,
			String mm) {
		return getJdbc().queryForMap("BAM087.getLoanAmtByIdAndDate",
				new String[] { compId, yyy, mm });
	}

	@Override
	public Map<String, Object> getLoanAmtAndDueAmtById(String compId) {
		Map<String, Object> map = getJdbc().queryForMap(
				"BAM087.getLoanAmtAndDueAmtById", new String[] { compId });
		Map<String, Object> map2 = getJdbc().queryForMap(
				"BAM087.getLoanAmtAndDueAmtById2", new String[] { compId });
		Map<String, Object> map3 = getJdbc().queryForMap(
				"BAM087.getLoanAmtAndDueAmtById21", new String[] { compId });
		if (!CollectionUtils.isEmpty(map2)) {
			map.putAll(map2);
		}
		if (!CollectionUtils.isEmpty(map3)) {
			map.putAll(map3);
		}
		return map;
	}

	@Override
	public Map<String, Object> getConAmtByIdStep1(String compId) {
		return getJdbc().queryForMap("BAM087.getConAmtByIdStep1",
				new String[] { compId });
	}

	@Override
	public Map<String, Object> getConAmtByIdStep2(String compId) {
		return getJdbc().queryForMap("BAM087.getConAmtByIdStep2",
				new String[] { compId });
	}

	@Override
	public List<Map<String, Object>> findBorrowerInfo(String id, String prodId) {
		return getJdbc().queryForList("BAM087.findBorrowerInfo",
				new String[] { id, prodId });
	}

	@Override
	public Map<String, Object> findUseCashCardCycleAmtCount(String id,
			String qDate, String prodId) {
		return getJdbc().queryForMap("BAM087.findUseCashCardCycleAmtCount",
				new String[] { id, qDate, prodId });
	}

	@Override
	public List<Map<String, Object>> findGuaranteeCorpLoanInfo(String id,
			String[] prodId, boolean isQueryJPQ) {
		List<Map<String, Object>> rtnList = null;
		Map<String, Object> loanAmtMap = null;
		boolean hasP1 = false;
		for (String s : prodId) {
			if ("P1".equals(s)) {
				hasP1 = true;
				break;
			}
		}
		StringBuffer s1 = new StringBuffer();
		for (int i = 0; i < prodId.length; i++) {
			s1.append("?,");
		}
		if (prodId.length > 0)
			s1.deleteCharAt(s1.length() - 1);

		List<String> pp = new ArrayList<String>();
		for (int i = 1; i <= 3; i++) {
			pp.add(id);
			for (String s : prodId)
				pp.add(s);
		}
		pp.add(id);
		String[] params = pp.toArray(new String[] {});
		if (isQueryJPQ) {
			String sql = MessageFormat.format(
					getSqlBySqlId( hasP1 ?"BAM087.findGuaranteeCorpLoanInfo31" : "BAM087.findGuaranteeCorpLoanInfo32"),
					new Object[] { s1.toString(), s1.toString() });
			rtnList = getJdbc().queryForList(sql, params);
		} else {
			String sql = MessageFormat.format(
					getSqlBySqlId( hasP1 ? "BAM087.findGuaranteeCorpLoanInfo" : "BAM087.findGuaranteeCorpLoanInfo2"),
					new Object[] { s1.toString(), s1.toString() });
			rtnList = getJdbc().queryForList(sql, params);
		}
//		if (!CollectionUtils.isEmpty(rtnList)) {
//			pp = new ArrayList<String>();
//			pp.add(id);
//			for (String s : prodId) {
//				pp.add(s);
//			}
//			for (Map<String, Object> map : rtnList) {
//				// 2012/08/31,增加sql3查詢NEWBAM.BC, SUM(NEWBAM.LOAN) AS LOAN_AMT
//				if (hasP1) {
//					String sql = MessageFormat
//							.format(getSqlBySqlId("BAM087.findGuaranteeCorpLoanInfo31"),
//									new Object[] { s1.toString(), s1.toString() });
//					String bankCode = MapUtils.getString(map, "BC");
//					pp.add(bankCode);
//					params = pp.toArray(new String[] {});
//					loanAmtMap = getJdbc().queryForMap(sql, params);
//					if (!CollectionUtils.isEmpty(loanAmtMap))
//						processAddDatas(loanAmtMap, map);
//					pp.remove(pp.size() - 1);
//				} else {
//					String sql = MessageFormat
//							.format(getSqlBySqlId("BAM087.findGuaranteeCorpLoanInfo32"),
//									new Object[] { s1.toString(), s1.toString() });
//					String bankCode = MapUtils.getString(map, "BC");
//					pp.add(bankCode);
//					params = pp.toArray(new String[] {});
//					loanAmtMap = getJdbc().queryForMap(sql, params);
//					if (!CollectionUtils.isEmpty(loanAmtMap))
//						processAddDatas(loanAmtMap, map);
//					pp.remove(pp.size() - 1);
//				}
//			}
//		}
		return rtnList;
	}

	@Override
	public List<Map<String, Object>> findCorpLoanInfo(String id,
			String[] bankCode) {
		List<Map<String, Object>> rtnList = null;
		StringBuffer s1 = new StringBuffer();
		List<String> pp = new ArrayList<String>();
		for (int i = 1; i <= 2; i++) {
			pp.add(id);
			for (String s : bankCode) {
				pp.add(s);
			}
		}
		for (int i = 0; i < bankCode.length; i++) {
			s1.append("?,");
		}
		if (bankCode.length > 0)
			s1.deleteCharAt(s1.length() - 1);
		String sql = MessageFormat.format(
				getSqlBySqlId("BAM087.findGuaranteeCorpLoanInfo4"),
				new Object[] { s1.toString() });
		String[] params = pp.toArray(new String[] {});
		rtnList = getJdbc().queryForList(sql, params);
		return rtnList;
	}

	@Override
	public List<Map<String, Object>> findLoanBalOfBAK(String id) {
		return getJdbc().queryForList("BAM087.findLoanBalOfBAK",
				new String[] { id });
	}

	@Override
	public Map<String, Object> findLoanUserCreditData(String id, int type) {
		Map<String, Object> rtnMap = new HashMap<String, Object>();
		switch (type) {
		case 0:
			rtnMap.put("11", getLoanUserCreditDataType11(id));
			rtnMap.put("12", getLoanUserCreditDataType12(id));
			rtnMap.put("2", getLoanUserCreditDataType2(id));
			// test id=********;
			rtnMap.put("3", getLoanUserCreditDataType3(id));
			break;
		case 1:
			rtnMap.put("11", getLoanUserCreditDataType11(id));
			rtnMap.put("12", getLoanUserCreditDataType12(id));
			break;
		case 2:
			rtnMap.put("2", getLoanUserCreditDataType2(id));
			break;
		case 3:
			rtnMap.put("3", getLoanUserCreditDataType3(id));
			break;
		default:
			break;
		}
		return rtnMap;
	}

	private Map<String, Object> getLoanUserCreditDataType11(String id) {
		Map<String, Object> map = getJdbc().queryForMap(
				"BAM087.findLoanUserCreditStatus11", new String[] { id });
		return map;
	}

	private List<Map<String, Object>> getLoanUserCreditDataType12(String id) {
		return getJdbc().queryForList("BAM087.findLoanUserCreditStatus12",
				new String[] { id });
	}

	private List<Map<String, Object>> getLoanUserCreditDataType2(String id) {
		return getJdbc().queryForList("BAM087.findLoanUserCreditStatus2",
				new String[] { id });
	}

	private List<Map<String, Object>> getLoanUserCreditDataType3(String id) {
		return getJdbc().queryForList("BAM087.findLoanUserCreditStatus3",
				new String[] { id, id, id });
	}

	@Override
	public List<Map<String, Object>> findJCICDetailsFor92(String groupId,
			String compId) {
		// SQL1讀取集團轄下公司明細及其聯徵虛擬統編
		List<Map<String, Object>> grpRcds = misGrpcmpSrv
				.findByGrpIDOnly100Record(groupId);
		List<Map<String, Object>> rtnList = new ArrayList<Map<String, Object>>();
		if(!CollectionUtils.isEmpty(grpRcds)){
			List<String> idList = new ArrayList<String>();
			List<String> selfIdList = new ArrayList<String>();
			//取得集團轄下公司ID
			for(Map<String, Object> map : grpRcds){
				String realId = MapUtils.getString(map, "BAN");
				String jcicId = MapUtils.getString(map, "JCIC_TAXNO");
				if(!CapString.isEmpty(jcicId))
					realId = jcicId;
				if(!compId.equals(realId))
					idList.add(realId);
				else
					selfIdList.add(realId);
				Map<String, Object> detail = new HashMap<String, Object>();
				processAddDatas(map, detail);
				// SQL2 以其集團公司名單, 引進該集團轄下公司的負責人資料
//				Map<String, Object> comp = misEJF334Srv.findPNameByBan(realId);
//				if(comp != null){
//					processAddDatas(comp, detail);
//				}
				rtnList.add(detail);
			}
			//以該集團轄下公司ID查詢聯徵明細 start
			StringBuffer s1 = new StringBuffer();
			for(int i = 0; i < idList.size(); i++){
				s1.append("?,");
			}
			if(idList.size() > 0)
				s1.deleteCharAt(s1.length() - 1);
			String sql = MessageFormat.format(getSqlBySqlId("BAM087.findCreditData2"), new Object[]{ s1.toString() });
			String sql2 = MessageFormat.format(getSqlBySqlId("BAM087.findCreditData4"), new Object[]{ s1.toString() });
			List<String> toParams = new ArrayList<String>();
			for(int i = 1; i <= 3; i++){
				toParams.addAll(idList);
			}
			String[] params = toParams.toArray(new String[]{});
			List<Map<String, Object>> rtnList2 = new ArrayList<Map<String, Object>>();
			Map<String, Object> detail = new HashMap<String, Object>();
			rtnList2 = getJdbc().queryForList(sql, params);
			detail.put("LOAN", rtnList2);
			rtnList2 = getJdbc().queryForList(sql2, params);
			detail.put("JPQ", rtnList2);
			rtnList.add(detail);

			s1 = new StringBuffer();
			for(int i = 0; i < selfIdList.size(); i++){
				s1.append("?,");
			}
			if(selfIdList.size() > 0)
				s1.deleteCharAt(s1.length() - 1);
			sql = MessageFormat.format(getSqlBySqlId("BAM087.findCreditData1"), new Object[]{ s1.toString() });
			sql2 = MessageFormat.format(getSqlBySqlId("BAM087.findCreditData3"), new Object[]{ s1.toString() });
			toParams = new ArrayList<String>();
			for(int i = 1; i <= 3; i++){
				toParams.addAll(selfIdList);
			}
			params = toParams.toArray(new String[]{});
			rtnList2 = new ArrayList<Map<String, Object>>();
			detail = new HashMap<String, Object>();
			rtnList2 = getJdbc().queryForList(sql, params);
			detail.put("LOAN", rtnList2);
			rtnList2 = getJdbc().queryForList(sql2, params);
			detail.put("JPQ", rtnList2);
			rtnList.add(detail);
			//以該集團轄下公司ID查詢聯徵明細 end
		}
		return rtnList;
	}// ;

	private void processAddDatas(Map<String, Object> sourceMap,
			Map<String, Object> toMap) {
		Set<String> keys = sourceMap.keySet();
		for (String key : keys) {
			toMap.put(key, sourceMap.get(key));
		}
	}

	@Override
	public Map<String, Object> findRefundsExchangeBadDebidBygId(String gId) {
		Map<String, Object> rtnMap = misMsg001Srv.findDetailAndBanBygId(gId);
		if (rtnMap != null) {
			Map<String, Object> map = getJdbc().queryForMap(
					"BAM087.findHeaderDatasBygId",
					new String[] { gId, gId, gId });
			if (map != null)
				rtnMap.put("S09DData", map);// processAddDatas(map, rtnMap);
		}
		return rtnMap;
	}

	@Override
	public Map<String, Object> getCompSecondLoanDataInfo(String qDate,
			String compId) {
		return getJdbc().queryForMap("BAM087.getCompSecLoanDataInfo",
				new String[] { compId, qDate });
	}

	@Override
	public Map<String, Object> getCompSecondLoanDataInfoJPQ(String qDate,
			String compId) {
		return getJdbc().queryForMap("BAM087.getCompSecLoanDataInfoJPQ",
				new String[] { compId, qDate, compId, qDate });
	}

	@Override
	public Map<String, Object> getBam087ByP3P9(String compId) {
		Map<String, Object> bam087Bals = getJdbc().queryForMap(
				"BAM087.getBam087ByP3P9",
				new String[] { compId, compId, compId });
		if (bam087Bals.get("C") == null && bam087Bals.get("L") == null
				&& bam087Bals.get("P") == null) {
			return new HashMap<String, Object>();
		}
		return bam087Bals;
	}

	@Override
	public Map<String, Object> getBam087JPQByP3P9(String compId) {
		return getJdbc().queryForMap("BAM087.getBam087JPQByP3P9",
				new String[] { compId, compId, compId });
	}

	@Override
	public Map<String, Object> getCreditInfo(String compId, String realId) {
		Map<String, Object> detail = new HashMap<String, Object>();
		// SQL3 以其集團公司名單, 逐一於主機資料庫中查詢該集團轄下公司的授信資料
		if (compId.equals(realId)) {	//PRODID = 'P1' OR PRODID = 'P8'
			// 若為申貸戶-授信資料
			List<Map<String, Object>> mapI = getJdbc().queryForList(
					"BAM087.findCreditInfoI", new String[] { compId });
			if (!CollectionUtils.isEmpty(mapI))
				detail.put("LOAN", mapI);
			// 若為申貸戶-授信資料JPQ only (存在隱藏欄位)
			List<Map<String, Object>> mapIII = getJdbc().queryForList(
					"BAM087.findCreditInfoIII", new String[] { compId });
			if (!CollectionUtils.isEmpty(mapIII))
				detail.put("JPQ", mapIII);
		} else {		//PRODID = 'P5'
			// 非申貸戶-授信資料
			List<Map<String, Object>> mapII = getJdbc().queryForList(
					"BAM087.findCreditInfoII", new String[] { compId });
			if (!CollectionUtils.isEmpty(mapII))
				detail.put("LOAN", mapII);
			// 非申貸戶-授信資料JPQ only (存在隱藏欄位)
			List<Map<String, Object>> mapIV = getJdbc().queryForList(
					"BAM087.findCreditInfoIV", new String[] { compId });
			if (!CollectionUtils.isEmpty(mapIV))
				detail.put("JPQ", mapIV);
		}
		return detail;
	}// ;
}
