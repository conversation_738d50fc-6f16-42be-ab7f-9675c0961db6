/* 
 *  AMLRelateFormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.handler.form;

import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.aml.AmlStrategy;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.gwclient.EaiGwClient;
import com.mega.eloan.common.gwclient.OBSMqGwClient;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.ElAml;
import com.mega.eloan.common.model.ElAmlItem;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.AmlService;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.panels.LMSS20APanel;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.dw.service.DWAslndavgovsService;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eai.service.EAIService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.ejcic.service.MisEJF307Service;
import com.mega.eloan.lms.ejcic.service.MisEJF315Service;
import com.mega.eloan.lms.ejcic.service.MisEJF323Service;
import com.mega.eloan.lms.ejcic.service.MisEJF327Service;
import com.mega.eloan.lms.ejcic.service.MisEJF334Service;
import com.mega.eloan.lms.ejcic.service.MisEJF356Service;
import com.mega.eloan.lms.ejcic.service.MisEJF357Service;
import com.mega.eloan.lms.ejcic.service.MisEJF366Service;
import com.mega.eloan.lms.ejcic.service.MisEJF419Service;
import com.mega.eloan.lms.ejcic.service.MisEJF502Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.LmsCustdataService;
import com.mega.eloan.lms.etch.service.MisMsg001Service;
import com.mega.eloan.lms.etch.service.MisSuccKeyMapService;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELCUS21Service;
import com.mega.eloan.lms.mfaloan.service.MisELF447Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisElCUS25Service;
import com.mega.eloan.lms.mfaloan.service.MisElcrcoService;
import com.mega.eloan.lms.mfaloan.service.MisEldpfService;
import com.mega.eloan.lms.mfaloan.service.MisFintblService;
import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;
import com.mega.eloan.lms.mfaloan.service.MisGrpdtlService;
import com.mega.eloan.lms.mfaloan.service.MisGrpfinService;
import com.mega.eloan.lms.mfaloan.service.MisLNF022Service;
import com.mega.eloan.lms.mfaloan.service.MisLnunIdService;
import com.mega.eloan.lms.mfaloan.service.MisRatetblService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01P;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L120S09C;
import com.mega.eloan.lms.model.L120S26A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01I;
import com.mega.eloan.lms.model.L140M01J;
import com.mega.eloan.lms.model.L140M01S;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L162S01A;
import com.mega.eloan.lms.model.L164S01A;
import com.mega.eloan.lms.obsdb.service.MisELF001Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.iisi.cap.utils.CapEntityUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 共用的formHandle
 * </pre>
 * 
 * @since 2012/10/3
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/3,REX,new
 *          <li>2013/06/24,Fantasy,queryBlackName add ci0024 check flag
 *          </ul>
 */
@Scope("request")
@Controller("amlrelateformhandler")
public class AMLRelateFormHandler extends AbstractFormHandler {

	@Resource
	MisCustdataService misCustdataService;
	// @Resource
	// LMS1215Service service1215;
	@Resource
	BranchService branchSrv;
	@Resource
	NumberService number;
	@Resource
	CodeTypeService codeService;
	@Resource
	DocFileService docFileService;
	@Resource
	LmsCustdataService LmsCustdataService;
	@Resource
	EloandbBASEService eloanDbBaseService;
	@Resource
	UserInfoService userSrv;
	@Resource
	DocCheckService docCheckService;
	@Resource
	MisLNF022Service lnLnf022Service;
	@Resource
	DocLogService docLogService;
	@Resource
	LMSService lmsService;

	@Resource
	CLSService clsService;

	@Resource
	MisCustdataService custSrv; // 客戶資訊
	@Resource
	MisELCUS21Service elcus21Srv; // 介面_取得客戶通訊地址資料

	@Resource
	MisEldpfService eldpfSrv; // MIS存款檔
	@Resource
	MisRatetblService ratetblSrv; // 查詢客戶本行授信餘額之授信往來資料
	@Resource
	MisGrpdtlService grpdtlSrv;
	@Resource
	MisMsg001Service msg001Srv; // 介面_申貸戶退票、拒往紀錄查詢
	@Resource
	MisLnunIdService lnunSrv; // 婉卻資訊
	@Resource
	MisGrpcmpService grpcmpSrv;
	@Resource
	MisELLNGTEEService ellngteeSrv;
	@Resource
	EAIService eaiSrv;
	@Resource
	EaiGwClient eaiGwSrv;
	@Resource
	MisELF001Service elf001Srv; // 海外存款
	@Resource
	MisSuccKeyMapService succKeyMapSrv;
	@Resource
	ICustomerService icustSrv;
	@Resource
	DwdbBASEService dwdbService;
	@Resource
	MisdbBASEService misDbService;
	@Resource
	MisEJF419Service bam087Srv; // 查詢聯徵資料
	@Resource
	MisEJF315Service ejf315Srv; // 取得聯徵資料日期（是否有執行過聯徵組合查詢）
	@Resource
	MisEJF366Service bam206Srv; // 保證公司之票信與債信資料
	@Resource
	MisEJF334Service misEJF334Srv;
	@Resource
	MisRatetblService misRateService;
	@Resource
	DWAslndavgovsService dwAslnavgovsSsService;
	@Resource
	MisElCUS25Service elcus25Srv;
	@Resource
	MisGrpfinService grpfinSrv;
	@Resource
	MisFintblService fintblSrv;
	@Resource
	SysParameterService sysParameterService;
	@Resource
	MisEJF323Service krs001Srv; // 信用卡停用紀錄
	@Resource
	MisEJF327Service profileSrv; // 查詢信用卡停用種類中文名稱
	@Resource
	MisEJF502Service krm040Srv; // 引進過去一年信用卡繳款延遲紀錄 - 延遲次數
	@Resource
	MisEJF307Service acm009Srv;
	@Resource
	MisEJF357Service bam303Srv;
	@Resource
	MisEJF356Service bam302Srv; // 查詢聯徵BAM302資料
	@Resource
	MisELF447Service misELF447Service;
	@Resource
	ICustomerService customerSrv;
	@Resource
	MisElCUS25Service misElcus25Service;
	@Resource
	MisElcrcoService misElcrcoService;
	@Resource
	OBSMqGwClient obsMqGwClient;
	@Resource
	AMLRelateService amlRelateService;
	@Resource
	CodeTypeService codetypeservice;
	@Resource
	BranchService branchService;
	@Resource
	AmlService amlService;
	@Resource
	EjcicService ejcicService;

	// J-106-0029-002/J-106-0029-002 洗錢防制-新增洗錢防制頁籤 & 實質受益人
	/**
	 * 儲存洗錢防制 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120s09a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String formL120s09a = params.getString("tLMS1205S20Form01");
		JSONObject json = JSONObject.fromObject(formL120s09a);
		String custId = Util.trim(json.optString("custId", ""));
		String dupNo = Util.trim(json.optString("dupNo", ""));
		String custName = Util.trim(json.optString("custName", ""));
		String custEName = Util.trim(json.optString("custEName", ""));

		// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
		String country = Util.trim(json.optString("country", ""));

		String custRelation = Util.trim(params.getString("list"));

		// J-111-0278_05097_B1001 Web
		// e-Loan紐約分行企金限制eloan簽報書中，AMLCFT頁籤中，Name欄位僅能儲存英文、數字、標點符號，且會拒絕非英文數字之字型
		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());

		if (Util.equals(custRelation, "")) {
			// pop.getProperty("L120S09a.createBY1")
			// 「與本案關係」
			// L120S09a.message07=「{0}」欄位不得為空白
			throw new CapMessageException(MessageFormat.format(
					pop.getProperty("L120S09a.message07"),
					pop.getProperty("L120S09a.custRelation")), getClass());

		}

		String[] strs = amlRelateService.getSortCustRelation(custRelation
				.split(","));
		// 對陣列進行排序
		if (strs.length > 0) {
			// Arrays.sort(strs);
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			for (String str : strs) {
				sb.append((sb.length() > 0) ? "," : UtilConstants.Mark.SPACE)
						.append(str);
			}
			custRelation = sb.toString();
		}

		// 檢核是否輸入統編
		boolean needCustId = false;
		boolean hasSUP1 = false; // 有負責人
		boolean hasRelateCorp = false; // 有關係企業
		boolean hasRealMan = false; // 有實質受益人

		// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
		boolean needCountry = false;

		for (int i = 0; i < strs.length; i++) {

			if (Util.notEquals(strs[i], "")
					&& amlRelateService.isCustRelationNeedCustId(strs[i])) {
				// 負責人實質受益人可以不用輸入ID
				needCustId = true;
			}

			// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
			if (Util.notEquals(strs[i], "")
					&& amlRelateService.isCustRelationNeedCountry(strs[i])) {
				// 借款人(含共同借款人)及保證人(含一般保證、連帶保證、擔保品提供人)國別
				needCountry = true;
			}

			if (Util.equals(strs[i],
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人)) {
				// 負責人
				hasSUP1 = true;
			}

			if (Util.equals(strs[i],
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.關係企業)) {
				// 關係企業
				hasRelateCorp = true;
			}

			if (Util.equals(strs[i],
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)) {
				// 實質受益人
				hasRealMan = true;
			}

		}

		if (needCustId) {
			if (Util.equals(custId, "")) {
				// 「本案關係人統編」欄位不得為空白
				throw new CapMessageException(MessageFormat.format(
						pop.getProperty("L120S09a.message07"),
						pop.getProperty("L120S09a.custId")), getClass());
			}
			if (Util.equals(dupNo, "")) {
				// 「重覆序號」欄位不得為空白
				throw new CapMessageException(MessageFormat.format(
						pop.getProperty("L120S09a.message07"),
						pop.getProperty("L120S09a.dupNo")), getClass());
			}
		} else {
			if (Util.equals(custId, "") && Util.equals(dupNo, "")
					&& Util.equals(custName, "")) {
				// 「戶名」欄位不得為空白
				throw new CapMessageException(MessageFormat.format(
						pop.getProperty("L120S09a.message07"),
						pop.getProperty("L120S09a.custName")), getClass());

			}
		}

		// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
		if (needCountry) {
			if (Util.equals(country, "")) {
				// 「國別」欄位不得為空白
				throw new CapMessageException(MessageFormat.format(
						pop.getProperty("L120S09a.message07"),
						pop.getProperty("L120S09a.country")), getClass());
			}
		}

		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		boolean needEngName = false; // 需要英文戶名 (借款人或共同借款人)
		needEngName = amlRelateService.isL120s09ANeedEngName(custRelation, "");

		if (needEngName) {
			StringBuffer noEngNameCust = new StringBuffer("");

			if (Util.equals(Util.trim(custEName), "")) {
				if (Util.notEquals(Util.trim(noEngNameCust.toString()), "")) {
					noEngNameCust.append("、").append(Util.trim(custId))
							.append("  ").append(Util.trim(custName));
				} else {
					noEngNameCust.append(Util.trim(custId)).append("  ")
							.append(Util.trim(custName));
				}

				if (Util.notEquals(Util.trim(noEngNameCust.toString()), "")) {
					// AML.error018=欲掃描之名單「{0}」必須要有英文戶名。
					throw new CapMessageException(MessageFormat.format(
							pop.getProperty("AML.error018"),
							Util.trim(noEngNameCust.toString())), getClass());
				}
			}
		}

		// 檢查有沒有重複
		List<L120S09A> tl120s09as = null;
		if (Util.notEquals(custId, "") && Util.notEquals(dupNo, "")) {
			// BY ID
			tl120s09as = amlRelateService.findListL120s09aByCustId(mainId,
					custId, dupNo);
		} else {
			// BY CUSTNAME
			if (Util.notEquals(custName, "")) {
				tl120s09as = amlRelateService.findListL120s09aByCustName(
						mainId, custName);
			} else {
				tl120s09as = null;
			}

		}

		L120S09A l120s09a = null;
		if (Util.notEquals(oid, "")) {
			l120s09a = amlRelateService.findL120s09aByOid(oid);
		}

		if (l120s09a != null) {
			// 有資料(更新儲存)
			DataParse.toBean(formL120s09a, l120s09a);
		} else {
			// 如無資料則新建立
			l120s09a = new L120S09A();
			DataParse.toBean(formL120s09a, l120s09a);
			// 設定一些初始化內容
			l120s09a.setMainId(mainId);
			l120s09a.setCreateTime(CapDate.getCurrentTimestamp());
			l120s09a.setCreator(user.getUserId());
			l120s09a.setCreateBY(UtilConstants.Casedoc.L120s04aCreateBY.人工產生);
		}

		if (tl120s09as != null && !tl120s09as.isEmpty()) {
			for (L120S09A tl120s09a : tl120s09as) {
				if (Util.notEquals(tl120s09a.getOid(), l120s09a.getOid())) {

					// L120S09a.message08=「{0}」統編或戶名已存在
					throw new CapMessageException(MessageFormat.format(
							pop.getProperty("L120S09a.message08"), custId
									+ dupNo + " " + custName), getClass());

				}
			}

		}

		if (custRelation != null) {
			l120s09a.setCustRelation(custRelation);
		} else {
			l120s09a.setCustRelation(UtilConstants.Mark.SPACE);
		}

		// 戶名轉半形
		l120s09a.setCustName(Util.toSemiCharString(Util.trim(l120s09a
				.getCustName())));
		l120s09a.setCustEName(Util.toSemiCharString(Util.trim(l120s09a
				.getCustEName())));

		// J-111-0278_05097_B1001 Web
		// e-Loan紐約分行企金限制eloan簽報書中，AMLCFT頁籤中，Name欄位僅能儲存英文、數字、標點符號，且會拒絕非英文數字之字型
		boolean isCustNameOnlyEng = amlRelateService
				.isAmlCustNameOnlyEng(queryBrId);
		StringBuffer custNameOnlyEng = new StringBuffer("");
		if (isCustNameOnlyEng) {

			String semiCustName = Util.toSemiCharString(String.valueOf(Util
					.trim(l120s09a.getCustName())));

			if (Util.notEquals(semiCustName, "")) {

				if (amlRelateService.hasFullCharInStringText(semiCustName)) {
					if (Util.notEquals(Util.trim(custNameOnlyEng.toString()),
							"")) {
						custNameOnlyEng.append("、")
								.append(Util.trim(l120s09a.getCustId()))
								.append("  ")
								.append(Util.trim(l120s09a.getCustName()));
					} else {
						custNameOnlyEng.append(Util.trim(l120s09a.getCustId()))
								.append("  ")
								.append(Util.trim(l120s09a.getCustName()));
					}
				}

			}

			if (Util.notEquals(Util.trim(custNameOnlyEng.toString()), "")) {
				// AML.error030=欲掃描之名單「{0}」本案戶名必須為英文、數字或標點符號。
				throw new CapMessageException(MessageFormat.format(
						pop.getProperty("AML.error030"),
						Util.trim(custNameOnlyEng.toString())), getClass());
			}

		}

		// 進行檢核的判斷寫在此
		l120s09a.setChkYN(UtilConstants.DEFAULT.否);
		l120s09a.setBlackListCode("");
		l120s09a.setMemo("");

		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		l120s09a.setCheckSeq("");
		l120s09a.setCm1AmlStatus("");
		l120s09a.setCheckResult("");
		l120s09a.setHitList("");

		// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
		l120s09a.setLuvRiskLevel("");

		// 儲存
		L120M01A model = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);
		try {
			lmsService.save(model, l120s09a);
		} catch (Exception e) {
			logger.error("[saveL120s09a] service1201.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", UtilConstants.Mark.HTMLSPACE);
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.輸入位數超過, param), getClass());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}

		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		// 如果是新模式切回舊模式，要把L120S09B(如果有的話，NCRESULT 清掉)，才不會判斷錯誤(但L120S09B不要刪掉)
		L120S09B l120s09b = amlRelateService.findL120s09bByMainId(mainId);
		if (l120s09b != null) {
			l120s09b.setNcResult("");
			l120s09b.setUniqueKey("");
			l120s09b.setNcCaseId("");
			l120s09b.setQueryDateS(null);
			l120s09b.setQueryBrId("");
			l120s09b.setQueryUser("");
			l120s09b.setWayMode("");
			lmsService.save(l120s09b);
		}

		result.set("newOid", l120s09a.getOid());
		return result;
	}// ;

	/**
	 * 刪除洗錢防制 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL120s09a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String[] oids = params.getString("listOid").split(",");

		List<L120S09A> list = amlRelateService.findL120s09asByOids(oids);
		if (list != null) {
			// 有資料就刪除
			amlRelateService.deleteListL120s09a(list);
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;

	}// ;

	/**
	 * 開起洗錢防制時查詢 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s09a(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		L120S09A l120s09a = amlRelateService.findL120s09aByOid(oid);
		if (l120s09a == null) {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		CapAjaxFormResult myForm2Result = DataParse.toResult(l120s09a);
		String[] strs = l120s09a.getCustRelation().split(",");
		List<String> list = new ArrayList<String>();
		for (int i = 0; i < strs.length; i++) {
			list.add(strs[i]);
		}

		// J-112-0534 因應兆豐金控自113.1.1下架證券違約交割/上市櫃觀察名單，故調整E-Loan授信管理系統相關欄位資訊
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);
		if (l120m01a != null) {
			// 海外不需要
			if (Util.equals("5", l120m01a.getTypCd())) {
				myForm2Result.set("showT70", "N");
			} else {
				myForm2Result.set("showT70", "Y");
				// 撈取T70結果
				L120S26A l120s26a = amlRelateService
						.findL120s26aByMainIdCustId(l120s09a.getMainId(),
								l120s09a.getCustId());
				if (l120s26a != null) {
					myForm2Result.set("T70Date",
							Util.trim(l120s26a.getT70Date()));
					myForm2Result.set("T70Status",
							Util.trim(l120s26a.getT70Status()));
					myForm2Result.set("T70NegFlag",
							Util.trim(l120s26a.getT70NegFlag()));
					myForm2Result
							.set("T70Amt", Util.trim(l120s26a.getT70Amt()));
					myForm2Result.set("T70Docfileoid",
							Util.trim(l120s26a.getDocfileoid()));
				}
			}
		} else {
			// 非簽報不需要
			myForm2Result.set("showT70", "N");
		}

		// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊
		if (Util.isNotEmpty(Util.trim(l120s09a.getCmfwarnpResult()))) {
			String cmfwarnpResult = Util.trim(l120s09a.getCmfwarnpResult());
			String cmfwarnpResultDesc = pop
					.getProperty("L120S09a.cmfwarnpResult." + cmfwarnpResult);
			if (UtilConstants.Casedoc.L120s09aCmfwarnpResultCode.有
					.equals(cmfwarnpResult)) {
				// L120S09a.cmfwarnpResult1.Desc=借款人ID XXX 於OOOO/OO/OO
				// 被列為「洗錢防制法......
				cmfwarnpResultDesc = cmfwarnpResultDesc
						+ "-"
						+ MessageFormat
								.format(pop.getProperty("L120S09a.cmfwarnpResult1.Desc"
										+ (amlRelateService
												.cmfwarnpOverSea(mainId) ? "OverSea"
												: "")), Util.trim(l120s09a
										.getCustId()), Util.trim(l120s09a
										.getCustName()), Util.trim(l120s09a
										.getCmfwarnpQueryResultInfo()));

			}
			if (UtilConstants.Casedoc.L120s09aCmfwarnpResultCode.不適用
					.equals(cmfwarnpResult)) {
				// L120S09a.cmfwarnpResult3.Desc=受告誡處分資料庫暫停服務，請洽資訊處。
				cmfwarnpResultDesc = pop
						.getProperty("L120S09a.cmfwarnpResult3.Desc");
			}
			myForm2Result.set("cmfwarnpResultDesc", cmfwarnpResultDesc);
		}

		myForm2Result.set("createBY2",
				UtilConstants.Casedoc.L120s04aCreateBY.系統產生.equals(l120s09a
						.getCreateBY()) ? pop.getProperty("L120S09a.createBY1")
						: pop.getProperty("L120S09a.createBY2"));

		myForm2Result.set("custRelation", list);

		result.set("tLMS1205S20Form01", myForm2Result);
		return result;
	}

	/**
	 * 引進戶名 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s09aCustName(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		String formL120s09a = params.getString("tLMS1205S20Form01");

		JSONObject json = JSONObject.fromObject(formL120s09a);
		String custId = Util.trim(json.optString("custId", ""));
		String dupNo = Util.trim(json.optString("dupNo", ""));
		String custName = Util.trim(json.optString("custName", ""));
		String custEName = Util.trim(json.optString("custEName", ""));
		String custRelation = Util.trim(params.getString("list"));
		// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
		String country = Util.trim(json.optString("country", ""));

		String newCustName = "";
		String newCustEName = "";

		if (Util.equals(custRelation, "")) {
			// 「與本案關係」
			// L120S09a.message07=「{0}」欄位不得為空白
			throw new CapMessageException(MessageFormat.format(
					pop.getProperty("L120S09a.message07"),
					pop.getProperty("L120S09a.custRelation")), getClass());
		}

		String[] strs = custRelation.split(",");

		// 檢核是否輸入統編
		boolean needCustId = false;
		boolean hasSUP1 = false; // 有負責人
		boolean hasRelateCorp = false; // 有關係企業
		boolean hasRealMan = false; // 有實質受益人
		// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
		boolean needCountry = false;

		List<String> list = new ArrayList<String>();
		for (int i = 0; i < strs.length; i++) {
			if (Util.notEquals(strs[i], "")
					&& amlRelateService.isCustRelationNeedCustId(strs[i])) {
				// 負責人實質受益人可以不用輸入ID
				needCustId = true;
			}

			// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
			if (Util.notEquals(strs[i], "")
					&& amlRelateService.isCustRelationNeedCountry(strs[i])) {
				// 借款人(含共同借款人)及保證人(含一般保證、連帶保證、擔保品提供人)國別
				needCountry = true;
			}

			if (Util.equals(strs[i],
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人)) {
				// 負責人
				hasSUP1 = true;
			}

			if (Util.equals(strs[i],
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.關係企業)) {
				// 關係企業
				hasRelateCorp = true;
			}

			if (Util.equals(strs[i],
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)) {
				// 實質受益人
				hasRealMan = true;
			}
		}

		if (needCustId) {
			if (Util.equals(custId, "")) {
				// 「本案關係人統編」欄位不得為空白
				throw new CapMessageException(MessageFormat.format(
						pop.getProperty("L120S09a.message07"),
						pop.getProperty("L120S09a.custId")), getClass());
			}
			if (Util.equals(dupNo, "")) {
				// 「重覆序號」欄位不得為空白
				throw new CapMessageException(MessageFormat.format(
						pop.getProperty("L120S09a.message07"),
						pop.getProperty("L120S09a.dupNo")), getClass());
			}
		} else {
			if (Util.equals(custId, "") && Util.equals(dupNo, "")
					&& Util.equals(custName, "")) {
				// 「戶名」欄位不得為空白
				throw new CapMessageException(MessageFormat.format(
						pop.getProperty("L120S09a.message07"),
						pop.getProperty("L120S09a.custName")), getClass());

			}
		}

		// // J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
		// if (needCountry) {
		// if (Util.equals(country, "")) {
		// // 「國別」欄位不得為空白
		// throw new CapMessageException(MessageFormat.format(
		// pop.getProperty("L120S09a.message07"),
		// pop.getProperty("L120S09a.country")), getClass());
		// }
		// }

		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);
		if (l120m01a == null) {
			// 動審表
			L160M01A l160m01a = amlRelateService.findModelByMainId(
					L160M01A.class, mainId);
			if (l160m01a != null) {
				// 簽報書
				l120m01a = amlRelateService.findL120m01aByL160m01a(l160m01a);
			}
		}

		// 取得簽報書徵信報告AML資料
		Map<String, String> lmsAndCesAmlListIdCNameMap = new HashMap<String, String>();
		Map<String, String> lmsAndCesAmlListIdENameMap = new HashMap<String, String>();
		Map<String, String> lmsAndCesAmlListNameMap = new HashMap<String, String>();
		amlRelateService.getLMSAndCesDocAMLCustNameMap(l120m01a,
				lmsAndCesAmlListIdCNameMap, lmsAndCesAmlListIdENameMap,
				lmsAndCesAmlListNameMap);

		// 1.抓最新的MIS英文名稱
		Map<String, String> nameMap = amlRelateService
				.queryL120s09aNewCustNameForAML("1", mainId, l120m01a,
						custRelation, custId, dupNo, custName, custEName);

		newCustName = nameMap.get("newCustName");
		newCustEName = nameMap.get("newCustEName");

		String ntCode = "";
		// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
		// 抓國別

		// J-108-0169_05097_B1001 Web e-Loan企金授信系統AML/CFT查詢頁籤無自動引入國別
		// if (needCountry) {
		// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
		if (Util.notEquals(custId, "") && Util.notEquals(dupNo, "")) {
			Map<String, String> cust = customerSrv.findRegPlaceHouseholdReg(
					custId, dupNo);
			if (cust != null && !cust.isEmpty()) {
				ntCode = MapUtils.getString(cust, "regPlace", "");
			}
		}

		// }

		// 2.抓簽報書或徵信報告*******************************************************
		// 2.1 BY CUSTID
		if (Util.equals(newCustName, "") || Util.equals(newCustEName, "")) {
			if (Util.notEquals(custId + dupNo, "")) {

				if (Util.equals(newCustName, "")) {
					if (lmsAndCesAmlListIdCNameMap.containsKey(custId + dupNo)) {
						newCustName = Util.trim(lmsAndCesAmlListIdCNameMap
								.get(custId + dupNo));

						if (Util.notEquals(newCustName, "")) {
							// 有找到新的中文戶名
							newCustName = Util.toSemiCharString(Util
									.trim(newCustName));
						}
					}
				}

				if (Util.equals(newCustEName, "")) {
					if (lmsAndCesAmlListIdENameMap.containsKey(custId + dupNo)) {
						newCustEName = Util.trim(lmsAndCesAmlListIdENameMap
								.get(custId + dupNo));

						if (Util.notEquals(newCustEName, "")) {
							// 有找到新的英文戶名
							newCustEName = Util.toSemiCharString(Util
									.trim(newCustEName));
						}
					}
				}

			}
		}

		// 2.2 BY CUSTNAME
		if (Util.equals(newCustEName, "")) {
			if (Util.notEquals(custName, "")) {
				if (lmsAndCesAmlListNameMap.containsKey(custName)) {
					newCustEName = Util.trim(lmsAndCesAmlListNameMap
							.get(custName));
					if (Util.notEquals(newCustEName, "")) {
						// 有找到新的英文戶名
						newCustEName = Util.toSemiCharString(Util
								.trim(newCustEName));

					}
				}
			}
		}

		if (Util.equals(newCustName, "") && Util.notEquals(newCustEName, "")) {
			// 有英文名稱，沒有中文名稱(例如實質受益人)
			if (Util.equals(custName, "")) {
				newCustName = newCustEName;
			}
		}

		if (Util.equals(newCustName, "") && Util.equals(newCustEName, "")) {
			// L120S09a.message09=無戶名資料可引進
			throw new CapMessageException(
					pop.getProperty("L120S09a.message09"), getClass());
		}

		result.set("newCustName", Util.toSemiCharString(Util.trim(newCustName)));
		result.set("newCustEName",
				Util.toSemiCharString(Util.trim(newCustEName)));
		result.set("newCountry", ntCode);
		return result;
	}

	/**
	 * 重新引進查詢名單 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult importBlackList(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		// 留存舊的資料(英文戶名)
		Map<String, String> oldAmlListIdMap = new HashMap<String, String>();
		Map<String, String> oldAmlListNameMap = new HashMap<String, String>();
		List<L120S09A> l120s09as = amlRelateService
				.findL120s09aByMainId(mainId);
		if (l120s09as != null && !l120s09as.isEmpty()) {
			for (L120S09A l120s09a : l120s09as) {
				String tCustId = Util.trim(l120s09a.getCustId());
				String tDupNo = Util.trim(l120s09a.getDupNo());
				String tCustName = Util.toSemiCharString(String.valueOf(Util
						.trim(l120s09a.getCustName())));
				String tCustEName = Util.toSemiCharString(Util.trim(l120s09a
						.getCustEName()));
				String fullKey = tCustId + tDupNo;
				if (Util.notEquals(fullKey, "")) {
					if (!oldAmlListIdMap.containsKey(fullKey)) {
						oldAmlListIdMap.put(fullKey, tCustEName);
					}
				}

				if (Util.notEquals(tCustName, "")) {
					if (!oldAmlListNameMap.containsKey(tCustName)) {
						oldAmlListNameMap.put(tCustName, tCustEName);
					}
				}

			}
			amlRelateService.deleteListL120s09a(l120s09as);
		}

		// 開始引進名單開始********************************************************************************************************************************

		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);
		if (l120m01a != null) {
			this.importBlackListInnerForL120m01a(mainId);
		} else {
			// 動審表
			L160M01A l160m01a = amlRelateService.findModelByMainId(
					L160M01A.class, mainId);
			if (l160m01a != null) {
				this.importBlackListInnerForL160m01a(mainId);
			}

		}

		// 引進名單結束************************************************************************************************************************************

		// 引進名單後，撈英文戶名*********************************

		if (l120m01a == null) {
			// 動審表
			L160M01A l160m01a = amlRelateService.findModelByMainId(
					L160M01A.class, mainId);
			if (l160m01a != null) {
				// 簽報書
				l120m01a = amlRelateService.findL120m01aByL160m01a(l160m01a);
			}
		}

		// 取得簽報書AND徵信報告AML資料
		Map<String, String> lmsAndCesAmlListIdCNameMap = new HashMap<String, String>();
		Map<String, String> lmsAndCesAmlListIdENameMap = new HashMap<String, String>();
		Map<String, String> lmsAndCesAmlListNameMap = new HashMap<String, String>();
		amlRelateService.getLMSAndCesDocAMLCustNameMap(l120m01a,
				lmsAndCesAmlListIdCNameMap, lmsAndCesAmlListIdENameMap,
				lmsAndCesAmlListNameMap);

		List<L120S09A> newl120s09as = amlRelateService
				.findL120s09aByMainId(mainId);
		if (newl120s09as != null && !newl120s09as.isEmpty()) {
			for (L120S09A l120s09a : newl120s09as) {
				String xCustRelation = l120s09a.getCustRelation();
				String xCustId = l120s09a.getCustId();
				String xDupNo = l120s09a.getDupNo();
				String xCustName = l120s09a.getCustName();
				String xCustEName = l120s09a.getCustEName();
				// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
				String xCountry = Util.trim(l120s09a.getCountry());

				String[] strs = amlRelateService.getSortCustRelation(Util.trim(
						xCustRelation).split(","));

				// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
				boolean needCountry = false;

				for (int i = 0; i < strs.length; i++) {

					// J-107-0248_05097_B1001 Web
					// e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
					if (Util.notEquals(strs[i], "")
							&& amlRelateService
									.isCustRelationNeedCountry(strs[i])) {
						// 借款人(含共同借款人)及保證人(含一般保證、連帶保證、擔保品提供人)國別
						needCountry = true;
					}

				}

				// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
				// J-108-0169_05097_B1001 Web e-Loan企金授信系統AML/CFT查詢頁籤無自動引入國別
				// if (needCountry) {
				if (Util.equals(xCountry, "")) {
					if (Util.notEquals(xCustId, "")
							&& Util.notEquals(xDupNo, "")) {
						Map<String, String> cust = customerSrv
								.findRegPlaceHouseholdReg(xCustId, xDupNo);
						if (cust != null && !cust.isEmpty()) {
							String ntCode = MapUtils.getString(cust,
									"regPlace", "");
							if (Util.notEquals(ntCode, "")) {
								l120s09a.setCountry(ntCode);
								lmsService.save(l120s09a);
							}

						}
					}
				}
				// }

				if (Util.equals(xCustEName, "")) {

					// 1.抓最新的MIS英文名稱
					Map<String, String> nameMap = amlRelateService
							.queryL120s09aNewCustNameForAML("2", mainId,
									l120m01a, xCustRelation, xCustId, xDupNo,
									xCustName, xCustEName);

					String newCustName = Util.toSemiCharString(Util
							.trim(nameMap.get("newCustName")));
					String newCustEName = Util.toSemiCharString(Util
							.trim(nameMap.get("newCustEName")));

					if (Util.notEquals(newCustEName, "")) {
						// 有找到新的英文戶名
						l120s09a.setCustEName(newCustEName);
						lmsService.save(l120s09a);
						continue;
					}

					// 2.抓執行本功能之前以存在於簽報書AML頁籤的資料
					// 2.1 BY ID
					if (Util.equals(newCustEName, "")) {
						if (Util.notEquals(xCustId + xDupNo, "")) {
							if (oldAmlListIdMap.containsKey(xCustId + xDupNo)) {
								newCustEName = Util.trim(oldAmlListIdMap
										.get(xCustId + xDupNo));

								if (Util.notEquals(newCustEName, "")) {
									// 有找到新的英文戶名
									l120s09a.setCustEName(Util
											.toSemiCharString(Util
													.trim(newCustEName)));
									lmsService.save(l120s09a);
									continue;
								}
							}
						}
					}

					// 2.2 BY CUSTNAME
					if (Util.equals(newCustEName, "")) {
						if (Util.notEquals(xCustName, "")) {
							if (oldAmlListNameMap.containsKey(xCustName)) {
								newCustEName = Util.trim(oldAmlListNameMap
										.get(xCustName));
								if (Util.notEquals(newCustEName, "")) {
									// 有找到新的英文戶名
									l120s09a.setCustEName(Util
											.toSemiCharString(Util
													.trim(newCustEName)));
									lmsService.save(l120s09a);
									continue;
								}
							}
						}
					}

					// 3.抓簽報書或徵信報告*******************************************************
					// 3.1 BY ID
					if (Util.equals(newCustEName, "")) {
						if (Util.notEquals(xCustId + xDupNo, "")) {
							if (lmsAndCesAmlListIdENameMap.containsKey(xCustId
									+ xDupNo)) {
								newCustEName = Util
										.trim(lmsAndCesAmlListIdENameMap
												.get(xCustId + xDupNo));

								if (Util.notEquals(newCustEName, "")) {
									// 有找到新的英文戶名
									l120s09a.setCustEName(Util
											.toSemiCharString(Util
													.trim(newCustEName)));
									lmsService.save(l120s09a);
									continue;
								}
							}
						}
					}

					// 3.2 BY CUSTNAME
					if (Util.equals(newCustEName, "")) {
						if (Util.notEquals(xCustName, "")) {
							if (lmsAndCesAmlListNameMap.containsKey(xCustName)) {
								newCustEName = Util
										.trim(lmsAndCesAmlListNameMap
												.get(xCustName));
								if (Util.notEquals(newCustEName, "")) {
									// 有找到新的英文戶名
									l120s09a.setCustEName(Util
											.toSemiCharString(Util
													.trim(newCustEName)));
									lmsService.save(l120s09a);
									continue;
								}
							}
						}
					}

				}
			}

		}

		return result;
	}

	/**
	 * J-106-0029-002 洗錢防制-新增洗錢防制頁籤--------- CALL FOR 簽報書
	 * 
	 * @param mainId
	 *            簽報書mainId
	 */
	public void importBlackListInnerForL120m01a(String mainId) {
		// 有ID*9***********************************

		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class, mainId);

		// 借款人+共借人(要掃其負責人與實質受益人)
		Map<String, String> cntrAllCustIdMap = new HashMap<String, String>();
		List<L140M01A> l140m01as = amlRelateService
				.findL140m01aListByL120m01cMainId(mainId,
						UtilConstants.Cntrdoc.ItemType.額度明細表);

		Set<String> distinctL140id = new HashSet<String>();
		for (L140M01A l140m01a : l140m01as) {

			// 額度明細表借款人
			String bId = l140m01a.getCustId();
			String bNo = l140m01a.getDupNo();
			String bName = Util.toSemiCharString(Util.trim(l140m01a
					.getCustName()));

			distinctL140id.add(bId + "|" + bNo);

			if (!cntrAllCustIdMap.containsKey(bId + bNo)) {
				cntrAllCustIdMap.put(bId + bNo, bName);
			}

			String bCountry = "";
			L120S01B bL120s01b = amlRelateService.findL120s01bByUniqueKey(
					mainId, bId, bNo);
			if (bL120s01b != null) {
				bCountry = bL120s01b.getNtCode();
			}
			amlRelateService.reSetL120S09A(mainId, bId, bNo, bName,
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶, "",
					bCountry);

			// 額度明細表共同借款人
			Set<L140M01J> l140m01js = l140m01a.getL140m01j();
			for (L140M01J l140m01j : l140m01js) {
				String cId = l140m01j.getCustId();
				String cNo = l140m01j.getDupNo();
				String cName = Util.toSemiCharString(Util.trim(l140m01j
						.getCustName()));
				String cCountry = "";

				L120S01B cL120s01b = amlRelateService.findL120s01bByUniqueKey(
						mainId, cId, cNo);
				if (cL120s01b != null) {
					cCountry = cL120s01b.getNtCode();
				}

				if (!cntrAllCustIdMap.containsKey(cId + cNo)) {
					cntrAllCustIdMap.put(cId + cNo, cName);
				}

				amlRelateService.reSetL120S09A(mainId, cId, cNo, cName,
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.共同借款人,
						"", cCountry);
			}

			// 連帶保證人&物上保證人
			Set<L140M01I> l140m01is = l140m01a.getL140m01i();
			for (L140M01I l140m01i : l140m01is) {
				String gId = l140m01i.getRId();
				String gNo = l140m01i.getRDupNo();
				String gName = Util.toSemiCharString(Util.trim(l140m01i
						.getRName()));
				String gCountry = Util.trim(l140m01i.getRCountry());

				if (Util.equals(UtilConstants.lngeFlag.連帶保證人,
						l140m01i.getRType())) {
					String getGuarantorType = UtilConstants.Casedoc.L120s09aBlackListCtlTarget.連保人;

					String GuarantorType_l140m01a = Util.trim(l140m01a
							.getGuarantorType());
					if (Util.equals(GuarantorType_l140m01a, "3")) { // 連帶保證人/一般保證人
						String GuarantorTypeItem = Util.trim(l140m01i
								.getGuarantorTypeItem());
						if (Util.equals(GuarantorTypeItem, "2")) {
							getGuarantorType = UtilConstants.Casedoc.L120s09aBlackListCtlTarget.一般保證人;
						}
					} else if (Util.equals(GuarantorType_l140m01a, "2")) {
						getGuarantorType = UtilConstants.Casedoc.L120s09aBlackListCtlTarget.一般保證人;
					}

					amlRelateService.reSetL120S09A(mainId, gId, gNo, gName,
							getGuarantorType, "", gCountry);

				} else if (Util.equals(UtilConstants.lngeFlag.擔保品提供人,
						l140m01i.getRType())) {
					amlRelateService
							.reSetL120S09A(
									mainId,
									gId,
									gNo,
									gName,
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.擔保品提供人,
									"", gCountry);
				}

			}

			// J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
			// 應收帳款無追索權買方
			List<L140M01S> listL140m01s = amlRelateService
					.findL140m01sByMainIdType(l140m01a.getMainId(),
							UtilConstants.L140m01sType.本案應收帳款買方額度資訊);
			if (listL140m01s != null && !listL140m01s.isEmpty()) {
				for (L140M01S l140m01s : listL140m01s) {
					if (l140m01s.getItemSeq() != 99999) {
						String sId = l140m01s.getCustId();
						String sNo = l140m01s.getDupNo();
						String sName = Util.toSemiCharString(Util.trim(l140m01s
								.getCustName()));
						String sCountry = Util.trim(l140m01s.getCountry());

						if (!cntrAllCustIdMap.containsKey(sId + sNo)) {
							cntrAllCustIdMap.put(sId + sNo, sName);
						}

						amlRelateService
								.reSetL120S09A(
										mainId,
										sId,
										sNo,
										sName,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.應收帳款買方無追索,
										"", sCountry);
					}

				}
			}
		}

		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		boolean isObs = branchService.isOBSBranch(caseBrId);
		for (String idNo : distinctL140id) {

			String custId = idNo.split("\\|")[0];
			String dupNo = idNo.split("\\|")[1];
			// 再查關係企業
			List<?> rows5 = misElcrcoService.findElcrecomByIdDupnoForBlackList(isObs, custId, dupNo);
			if (rows5 != null && !rows5.isEmpty()) {
				Iterator<?> it5 = rows5.iterator();
				while (it5.hasNext()) {
					Map<?, ?> dataMap5 = (Map<?, ?>) it5.next();
					String s04aId = Util.trim(String.valueOf(dataMap5.get("BAN")));
					String s04aNo = Util.trim(String.valueOf(dataMap5.get("DUPNO")));
					String s04aName = Util.toSemiCharString(Util.trim(String.valueOf(dataMap5.get("CNAME"))));
					String s04aEName = Util.toSemiCharString(Util.trim(String.valueOf(dataMap5.get("ENAME"))));
					String sCountry = "";
					if (Util.notEquals(s04aId, "")) {
						amlRelateService
								.reSetL120S09A(
										mainId,
										s04aId,
										s04aNo,
										s04aName,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.關係企業,
										s04aEName, sCountry);
					}

				}
			}
		}

		// 關係企業
//		List<L120S04A> listL120s04a = (List<L120S04A>) amlRelateService
//				.findListByMainId(L120S04A.class, mainId);
//		if (listL120s04a != null && !listL120s04a.isEmpty()) {
//			for (L120S04A l120s04a : listL120s04a) {
//				String custRelation = l120s04a.getCustRelation();
//
//				String[] item = custRelation.split(",");
//				List<String> asList = Arrays.asList(item);
//				if (asList.contains("6")) {
//					String s04aId = Util.trim(l120s04a.getCustId());
//					String s04aNo = Util.trim(l120s04a.getDupNo());
//					String s04aName = Util.toSemiCharString(Util.trim(l120s04a
//							.getCustName()));
//					String sCountry = "";
//					if (Util.notEquals(s04aId, "")) {
//						amlRelateService
//								.reSetL120S09A(
//										mainId,
//										s04aId,
//										s04aNo,
//										s04aName,
//										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.關係企業,
//										"", sCountry);
//					}
//
//				}
//
//			}
//
//		}

		// 可能沒有ID*****************************************************************
		// 負責人+實質受益人
		List<L120S01B> l120s01bs = (List<L120S01B>) amlRelateService
				.findListByMainId(L120S01B.class, mainId);
		if (l120s01bs != null && !l120s01bs.isEmpty()) {
			for (L120S01B l120s01b : l120s01bs) {
				String s01bCustId = Util.trim(l120s01b.getCustId());
				String s01bDupNo = Util.trim(l120s01b.getDupNo());
				if (cntrAllCustIdMap.containsKey(s01bCustId + s01bDupNo)) {
					// 借款人或共借人才要

					// 負責人
					String chairmanId = Util.trim(l120s01b.getChairmanId());
					String chairmanDupNo = Util.trim(l120s01b
							.getChairmanDupNo());
					String chairman = Util.toSemiCharString(Util.trim(l120s01b
							.getChairman()));
					String chairmanCountry = "";

					if (Util.notEquals(chairmanId, "")
							|| Util.notEquals(chairman, "")) {
						amlRelateService
								.reSetL120S09A(
										mainId,
										chairmanId,
										chairmanDupNo,
										chairman,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人,
										"", chairmanCountry);
					}

					// 實質受益人
					List<L120S01P> listL120s01p = amlRelateService
							.findL120s01pByMainIdAndCustIdWithRType(
									mainId,
									s01bCustId,
									s01bDupNo,
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);
					if (listL120s01p != null && !listL120s01p.isEmpty()) {
						for (L120S01P l120s01p : listL120s01p) {
							String rId = Util.trim(l120s01p.getRId());
							String rDupNo = Util.trim(l120s01p.getRDupNo());
							String rName = Util.toSemiCharString(Util
									.trim(l120s01p.getRName()));
							String rEName = Util.toSemiCharString(Util
									.trim(l120s01p.getREName()));
							String rCountry = Util.equals(
									Util.trim(l120s01p.getNation()), "") ? ""
									: Util.trim(l120s01p.getNation());
							// String rCountry = "";

							if (Util.notEquals(rId, "")
									|| Util.notEquals(rName, "")) {
								amlRelateService
										.reSetL120S09A(
												mainId,
												rId,
												rDupNo,
												rName,
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人,
												"", rCountry);
							}

						}

					}

					// J-107-0070-001 Web e-Loan
					// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
					// 高階管理人員
					List<L120S01P> listL120s01p_smgr = amlRelateService
							.findL120s01pByMainIdAndCustIdWithRType(
									mainId,
									s01bCustId,
									s01bDupNo,
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員);
					if (listL120s01p_smgr != null
							&& !listL120s01p_smgr.isEmpty()) {
						for (L120S01P l120s01p : listL120s01p_smgr) {
							String rId = Util.trim(l120s01p.getRId());
							String rDupNo = Util.trim(l120s01p.getRDupNo());
							String rName = Util.toSemiCharString(Util
									.trim(l120s01p.getRName()));
							String rEName = Util.toSemiCharString(Util
									.trim(l120s01p.getREName()));
							String rCountry = Util.equals(
									Util.trim(l120s01p.getNation()), "") ? ""
									: Util.trim(l120s01p.getNation());
							// String rCountry = "";
							if (Util.notEquals(rId, "")
									|| Util.notEquals(rName, "")) {
								amlRelateService
										.reSetL120S09A(
												mainId,
												rId,
												rDupNo,
												rName,
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員,
												"", rCountry);
							}

						}

					}

					// J-108-0039_05097_B1001 Web e-Loan
					// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
					// 具控制權人
					List<L120S01P> listL120s01p_ctrlPeo = amlRelateService
							.findL120s01pByMainIdAndCustIdWithRType(
									mainId,
									s01bCustId,
									s01bDupNo,
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人);
					if (listL120s01p_ctrlPeo != null
							&& !listL120s01p_ctrlPeo.isEmpty()) {
						for (L120S01P l120s01p : listL120s01p_ctrlPeo) {
							String rId = Util.trim(l120s01p.getRId());
							String rDupNo = Util.trim(l120s01p.getRDupNo());
							String rName = Util.toSemiCharString(Util
									.trim(l120s01p.getRName()));
							String rEName = Util.toSemiCharString(Util
									.trim(l120s01p.getREName()));
							String rCountry = Util.equals(
									Util.trim(l120s01p.getNation()), "") ? ""
									: Util.trim(l120s01p.getNation());
							// String rCountry = "";
							if (Util.notEquals(rId, "")
									|| Util.notEquals(rName, "")) {
								amlRelateService
										.reSetL120S09A(
												mainId,
												rId,
												rDupNo,
												rName,
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人,
												"", rCountry);
							}

						}

					}

				}

			}
		}

	}

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤--------- CALL FOR 動審表
	 * 
	 * @param mainId
	 *            動審表mainId
	 */
	public void importBlackListInnerForL160m01a(String mainId) {

		// 動審表主要資料來源為MIS TABLE
		L160M01A l160m01a = amlRelateService.findModelByMainId(L160M01A.class,
				mainId);

		String ownBrId = Util.trim(l160m01a.getOwnBrId());
		boolean isObs = branchService.isOBSBranch(ownBrId);

		// 有ID*9***********************************
		Map<String, String> cntrCustIdMap = new HashMap<String, String>();
		Map<String, String> cntrAllCustIdMap = new HashMap<String, String>();
		List<L162S01A> l162s01as = (List<L162S01A>) amlRelateService
				.findListByMainId(L162S01A.class, l160m01a.getMainId());
		L120M01A l120M01A = amlRelateService.findL120m01aByL160m01a(l160m01a);
		String rptMainId = l120M01A.getMainId();

		if (l162s01as != null && !l162s01as.isEmpty()) {
			for (L162S01A l162s01a : l162s01as) {

				if (Util.equals(l162s01a.getRType(),
						UtilConstants.lngeFlag.共同借款人)) {
					// 額度明細表借款人
					String bId = l162s01a.getRId();
					String bNo = l162s01a.getRDupNo();
					String bName = Util.toSemiCharString(Util.trim(l162s01a
							.getRName()));
					String bCountry = "";
					L120S01B bL120s01b = amlRelateService
							.findL120s01bByUniqueKey(rptMainId, bId, bNo);
					if (bL120s01b != null) {
						bCountry = bL120s01b.getNtCode();
					}

					String borrKind = "";
					if (Util.equals(bId, l162s01a.getCustId())
							&& Util.equals(bNo, l162s01a.getDupNo())) {
						borrKind = UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶;
					} else {
						borrKind = UtilConstants.Casedoc.L120s09aBlackListCtlTarget.共同借款人;
					}

					// 借戶/共同借款人
					// 同一ID如果為借戶與共同借款人，則與本案關係都要勾起來，所以都要丟到reSetL120S09A去判斷
					amlRelateService.reSetL120S09A(mainId, bId, bNo, bName,
							borrKind, "", bCountry);

					if (!cntrCustIdMap.containsKey(bId + bNo)) {

						cntrCustIdMap.put(bId + bNo, bName);

						// 相同ID的借戶/共同借款人，負責人、實質受益人、關係企業應該都相同
						// 負責人
						L164S01A l164s01a = amlRelateService
								.findL164s01aByUniqueKey(mainId, bId, bNo);
						if (l164s01a != null) {
							String chairmanId = Util.trim(l164s01a
									.getChairmanId());
							String chairmanDupNo = Util.trim(l164s01a
									.getChairmanDupNo());
							String chairman = Util.toSemiCharString(Util
									.trim(l164s01a.getChairman()));
							String chairmanCountry = "";

							if (Util.notEquals(chairmanId, "")
									|| Util.notEquals(chairman, "")) {
								amlRelateService
										.reSetL120S09A(
												mainId,
												chairmanId,
												chairmanDupNo,
												chairman,
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人,
												"", chairmanCountry);
							}

						}

						// 實質受益人
						List<L120S01P> listL120s01p = amlRelateService
								.findL120s01pByMainIdAndCustIdWithRType(
										mainId,
										bId,
										bNo,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);
						if (listL120s01p != null && !listL120s01p.isEmpty()) {
							for (L120S01P l120s01p : listL120s01p) {
								String rId = Util.trim(l120s01p.getRId());
								String rDupNo = Util.trim(l120s01p.getRDupNo());
								String rName = Util.toSemiCharString(Util
										.trim(l120s01p.getRName()));
								String rCountry = Util.equals(
										Util.trim(l120s01p.getNation()), "") ? ""
										: Util.trim(l120s01p.getNation());
								// String rCountry = "";

								if (Util.notEquals(rId, "")
										|| Util.notEquals(rName, "")) {
									amlRelateService
											.reSetL120S09A(
													mainId,
													rId,
													rDupNo,
													rName,
													UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人,
													"", rCountry);
								}

							}

						}

						// J-107-0070-001 Web e-Loan
						// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
						// 高階管理人員
						List<L120S01P> listL120s01p_smgr = amlRelateService
								.findL120s01pByMainIdAndCustIdWithRType(
										mainId,
										bId,
										bNo,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員);
						if (listL120s01p_smgr != null
								&& !listL120s01p_smgr.isEmpty()) {
							for (L120S01P l120s01p : listL120s01p_smgr) {
								String rId = Util.trim(l120s01p.getRId());
								String rDupNo = Util.trim(l120s01p.getRDupNo());
								String rName = Util.toSemiCharString(Util
										.trim(l120s01p.getRName()));
								String rCountry = Util.equals(
										Util.trim(l120s01p.getNation()), "") ? ""
										: Util.trim(l120s01p.getNation());
								// String rCountry = "";
								if (Util.notEquals(rId, "")
										|| Util.notEquals(rName, "")) {
									amlRelateService
											.reSetL120S09A(
													mainId,
													rId,
													rDupNo,
													rName,
													UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員,
													"", rCountry);
								}

							}

						}

						// J-108-0039_05097_B1001 Web e-Loan
						// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
						// 具控制權人
						List<L120S01P> listL120s01p_ctrlPeo = amlRelateService
								.findL120s01pByMainIdAndCustIdWithRType(
										mainId,
										bId,
										bNo,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人);
						if (listL120s01p_ctrlPeo != null
								&& !listL120s01p_ctrlPeo.isEmpty()) {
							for (L120S01P l120s01p : listL120s01p_ctrlPeo) {
								String rId = Util.trim(l120s01p.getRId());
								String rDupNo = Util.trim(l120s01p.getRDupNo());
								String rName = Util.toSemiCharString(Util
										.trim(l120s01p.getRName()));
								String rCountry = Util.equals(
										Util.trim(l120s01p.getNation()), "") ? ""
										: Util.trim(l120s01p.getNation());
								// String rCountry = "";
								if (Util.notEquals(rId, "")
										|| Util.notEquals(rName, "")) {
									amlRelateService
											.reSetL120S09A(
													mainId,
													rId,
													rDupNo,
													rName,
													UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人,
													"", rCountry);
								}

							}

						}

						if (Util.equals(
								borrKind,
								UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶)) {

							// 再查關係企業
							List<?> rows5 = misElcrcoService.findElcrecomByIdDupnoForBlackList(isObs, bId, bNo);
							if (rows5 != null && !rows5.isEmpty()) {
								Iterator<?> it5 = rows5.iterator();
								while (it5.hasNext()) {
									Map<?, ?> dataMap5 = (Map<?, ?>) it5.next();

									String s04aId = Util.trim(String
											.valueOf(dataMap5.get("BAN")));
									String s04aNo = Util.trim(String
											.valueOf(dataMap5.get("DUPNO")));
									String s04aName = Util
											.toSemiCharString(Util.trim(String
													.valueOf(dataMap5
															.get("CNAME"))));
									String s04aEName = Util
											.toSemiCharString(Util.trim(String
													.valueOf(dataMap5
															.get("ENAME"))));
									String sCountry = "";
									if (Util.notEquals(s04aId, "")) {
										amlRelateService
												.reSetL120S09A(
														mainId,
														s04aId,
														s04aNo,
														s04aName,
														UtilConstants.Casedoc.L120s09aBlackListCtlTarget.關係企業,
														s04aEName, sCountry);
									}

								}
							}

						}
					}

				}

				if (Util.equals(l162s01a.getRType(),
						UtilConstants.lngeFlag.連帶保證人)) {
					String gId = l162s01a.getRId();
					String gNo = l162s01a.getRDupNo();
					String gName = Util.toSemiCharString(Util.trim(l162s01a
							.getRName()));
					String gCountry = Util.trim(l162s01a.getRCountry());

					amlRelateService
							.reSetL120S09A(
									mainId,
									gId,
									gNo,
									gName,
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.連保人,
									"", gCountry);
				}

				if (Util.equals(l162s01a.getRType(),
						UtilConstants.lngeFlag.ㄧ般保證人)) {
					String gId = l162s01a.getRId();
					String gNo = l162s01a.getRDupNo();
					String gName = Util.toSemiCharString(Util.trim(l162s01a
							.getRName()));
					String gCountry = Util.trim(l162s01a.getRCountry());

					amlRelateService
							.reSetL120S09A(
									mainId,
									gId,
									gNo,
									gName,
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.一般保證人,
									"", gCountry);
				}

				if (Util.equals(l162s01a.getRType(),
						UtilConstants.lngeFlag.擔保品提供人)
						|| Util.equals(l162s01a.getRType(),
								UtilConstants.lngeFlag.連帶借款人)) {
					String gId = l162s01a.getRId();
					String gNo = l162s01a.getRDupNo();
					String gName = Util.toSemiCharString(Util.trim(l162s01a
							.getRName()));
					String gCountry = Util.trim(l162s01a.getRCountry());

					amlRelateService
							.reSetL120S09A(
									mainId,
									gId,
									gNo,
									gName,
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.擔保品提供人,
									"", gCountry);
				}

			}
		}

		// 補強檢核************************************************************************************

		if (l160m01a != null) {
			Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
			if (l160m01bs != null && !l160m01bs.isEmpty()) {
				for (L160M01B l160m01b : l160m01bs) {
					L140M01A l140m01a = amlRelateService.findModelByMainId(
							L140M01A.class, l160m01b.getReMainId());
					if (l140m01a != null) {
						String mId = l140m01a.getCustId();
						String mNo = l140m01a.getDupNo();
						String mName = Util.toSemiCharString(Util.trim(l140m01a
								.getCustName()));

						String mCountry = "";
						L120S01B mL120s01b = amlRelateService
								.findL120s01bByUniqueKey(rptMainId, mId, mNo);
						if (mL120s01b != null) {
							mCountry = mL120s01b.getNtCode();
						}

						amlRelateService
								.reSetL120S09A(
										mainId,
										mId,
										mNo,
										mName,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶,
										"", mCountry);

						// J-107-0164_05097_B1001 Web
						// e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
						// 應收帳款無追索權買方
						List<L140M01S> listL140m01s = amlRelateService
								.findL140m01sByMainIdType(l140m01a.getMainId(),
										UtilConstants.L140m01sType.本案應收帳款買方額度資訊);
						if (listL140m01s != null && !listL140m01s.isEmpty()) {
							for (L140M01S l140m01s : listL140m01s) {
								if (l140m01s.getItemSeq() != 99999) {
									String sId = l140m01s.getCustId();
									String sNo = l140m01s.getDupNo();
									String sName = Util.toSemiCharString(Util
											.trim(l140m01s.getCustName()));
									String sCountry = Util.trim(l140m01s
											.getCountry());
									amlRelateService
											.reSetL120S09A(
													mainId,
													sId,
													sNo,
													sName,
													UtilConstants.Casedoc.L120s09aBlackListCtlTarget.應收帳款買方無追索,
													"", sCountry);
								}

							}
						}

					}
				}
			}
		}

	}

	/**
	 * 黑名單查詢 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBlackList(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String mainCustId = "";
		String mainDupNo = "";
		// String caseBrId = l120m01a.getCaseBrId();
		// boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
		// .getBranch(l120m01a.getCaseBrId()).getBrNoFlag());

		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);
		if (l120m01a != null) {
			// 簽報書
			mainCustId = l120m01a.getCustId();
			mainDupNo = l120m01a.getDupNo();
		} else {
			// 動審表
			L160M01A l160m01a = amlRelateService.findModelByMainId(
					L160M01A.class, mainId);
			if (l160m01a != null) {
				// 簽報書
				mainCustId = l160m01a.getCustId();
				mainDupNo = l160m01a.getDupNo();
			}
		}

		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		// 如果是新模式切回舊模式，要把L120S09B(如果有的話，NCRESULT 清掉)，才不會判斷錯誤(但L120S09B不要刪掉)
		L120S09B l120s09b = amlRelateService.findL120s09bByMainId(mainId);
		if (l120s09b != null) {
			lmsService.delete(l120s09b);
		}

		// 欄位檢核*******************************************************************
		// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
		amlRelateService.chkBlackListDataOk(mainId);

		// ********************************************************************************

		result = this.queryBlackListInner(mainId, mainCustId, mainDupNo);

		// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊
		amlRelateService.importL120s09aWarnData(mainId);

		Date blackListQDate = CapDate.getCurrentTimestamp();
		result.set("blackListQDate", CapDate.formatDate(blackListQDate,
				UtilConstants.DateFormat.YYYY_MM_DD));

		return result;
	}

	/**
	 * 黑名單查詢 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param mainId
	 * @param mainCustId
	 * @param mainDupNo
	 * @return
	 * @throws CapException
	 */
	public CapAjaxFormResult queryBlackListInner(String mainId,
			String mainCustId, String mainDupNo) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();

		Map<String, String> countryMap = codetypeservice.findByCodeType(
				"CountryCode", "en");

		// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
		NumberFormat n = new DecimalFormat("000");

		List<L120S09A> l120s09as = amlRelateService
				.findL120s09aByMainIdWithShowOrder(mainId);
		int checkSeq = 0;
		if (l120s09as != null && !l120s09as.isEmpty()) {
			for (L120S09A l120s09a : l120s09as) {
				String tCustId = Util.trim(l120s09a.getCustId());
				String tDupNo = Util.trim(l120s09a.getDupNo());
				String tCustName = Util.toSemiCharString(String.valueOf(Util
						.trim(l120s09a.getCustName())));
				String tCustEName = Util.trim(l120s09a.getCustEName());
				String qName = Util.notEquals(tCustEName, "") ? tCustEName
						: tCustName;
				// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
				String tCountry = Util.trim(l120s09a.getCountry());

				if (Util.equals(qName, "")) {
					continue;
				}

				// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
				// 針對海外分行，未使用PRIME/SAS系統，仍走原AS400 MQ查詢

				// J-111-0577_05097_B1001 Web
				// e-Loan系統修改以客戶名稱與國別各別經ELOAN系統分開掃描之規則。
				// if (Util.notEquals(tCountry, "")) {
				//
				// if (countryMap.containsKey(tCountry)) {
				// qName = qName + " "
				// + countryMap.get(tCountry).toUpperCase();
				// }
				//
				// }

				List<String> blackList = customerSrv.findBlackListWithCountry(
						unitNo, qName, mainId, tCountry);
				String blackListCode = blackList
						.get(ICustomerService.BlackList_ReturnCode);
				String memo = blackList
						.get(ICustomerService.BlackList_OFACName);
				int SeqNum = 0;
				if (Util.equals(blackListCode,
						UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單)) {
					SeqNum = 11;
				} else if (Util.equals(blackListCode,
						UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單)) {
					SeqNum = 21;
				} else if (Util.equals(blackListCode,
						UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單)) {
					SeqNum = 31;
				} else {

					blackListCode = "";
				}
				if (Util.equals(tCustId, mainCustId)
						&& Util.equals(tDupNo, mainDupNo)) {
					SeqNum = SeqNum - 1;
				}

				// J-106-0029-003 Web e-Loan授信簽報書借款人基本資料與動審表黑名單查詢調整使用共用模組
				// 國內分行如果實質受益人查不到需加查PEPS是否為國外政治敏感人物
				boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
						.getBranch(unitNo).getBrNoFlag());
				if (!isOverSea) {

					// J-107-0070-001 Web e-Loan
					// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
					// J-108-0039_05097_B1001 Web e-Loan
					// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
					if ((SeqNum == 31 || SeqNum == 21)
							&& (l120s09a
									.getCustRelation()
									.indexOf(
											UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人) > -1
									|| l120s09a
											.getCustRelation()
											.indexOf(
													UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員) > -1 || l120s09a
									.getCustRelation()
									.indexOf(
											UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人) > -1)) {
						String otherPEPS = "";

						List<L120S01P> l120s01ps = amlRelateService
								.findL120s01pByMainIdWithoutRType(mainId);

						if (l120s01ps != null && !l120s01ps.isEmpty()) {
							for (L120S01P l120s01p : l120s01ps) {
								if (Util.equals(
										Util.trim(l120s01p.getRType()),
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)
										|| Util.equals(
												Util.trim(l120s01p.getRType()),
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員)
										|| Util.equals(
												Util.trim(l120s01p.getRType()),
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人)) {
									String s01pId = Util
											.trim(l120s01p.getRId());
									String s01pNo = Util.trim(l120s01p
											.getRDupNo());
									String s01pName = Util.trim(l120s01p
											.getRName());
									String s01pEName = Util.trim(l120s01p
											.getREName());
									String s01pPEPS = Util.trim(l120s01p
											.getPeps());

									if (Util.notEquals(tCustId, "")
											&& Util.notEquals(tDupNo, "")) {
										if (Util.notEquals(s01pId, "")
												&& Util.notEquals(s01pNo, "")) {
											if (Util.equals(s01pId, tCustId)
													&& Util.equals(s01pNo,
															tDupNo)) {
												if (Util.equals(s01pPEPS, "Y")) {
													otherPEPS = "Y";
													break;
												}
											}
										}
									}

									if (Util.notEquals(tCustName, "")) {
										if (Util.notEquals(s01pName, "")) {
											if (Util.equals(s01pName, tCustName)) {
												if (Util.equals(s01pPEPS, "Y")) {
													otherPEPS = "Y";
													break;
												}
											}
										}
									}

									if (Util.notEquals(tCustEName, "")) {
										if (Util.notEquals(s01pEName, "")) {
											if (Util.equals(s01pEName,
													tCustEName)) {
												if (Util.equals(s01pPEPS, "Y")) {
													otherPEPS = "Y";
													break;
												}
											}
										}
									}
								}

							}
						}

						if (Util.equals(otherPEPS, "Y")) {
							blackListCode = UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單;
							memo = "PEPS*";
							SeqNum = 11;
						}

					}
				}

				l120s09a.setBlackListCode(blackListCode);
				l120s09a.setMemo(memo);
				l120s09a.setQueryDateS(CapDate.getCurrentTimestamp());
				l120s09a.setSeqNum(SeqNum);

				// J-106-0238-001
				// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
				// 舊模式要清
				l120s09a.setCheckSeq(n.format(++checkSeq));
				l120s09a.setCm1AmlStatus("");
				l120s09a.setCheckResult("");
				l120s09a.setHitList("");

				// J-106-0238-001
				// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
				if (Util.notEquals(tCustId, "") && Util.notEquals(tDupNo, "")) {
					// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
					if (LMSUtil
							.isContainValue(
									Util.trim(l120s09a.getCustRelation()),
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶,
									",")) {
						String luvRiskLevel = amlRelateService
								.getCustLuvRiskLevel(unitNo, tCustId, tDupNo);
						l120s09a.setLuvRiskLevel(Util.trim(luvRiskLevel));
					} else {
						l120s09a.setLuvRiskLevel("");
					}

				}

				lmsService.save(l120s09a);
			}

		}

		return result;

	}

	/**
	 * 查詢相關人 J-106-0029-003 洗錢防制-新增實質受益人
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s01p(PageParameters params)
			throws CapException {
		String oid = Util.trim(params.getString(EloanConstants.OID));

		if (Util.isEmpty(oid)) {
			return new CapAjaxFormResult();
		}
		L120S01P l120s01p = amlRelateService.findL120s01pByOid(oid);

		CapAjaxFormResult result = DataParse.toResult(l120s01p,
				DataParse.Delete, new String[] { EloanConstants.MAIN_ID,
						EloanConstants.OID });

		result.set("toglePersonType", l120s01p.getType());
		result.set("l120s01pFormOid", l120s01p.getOid());
		return result;
	}

	/**
	 * 儲存相關人 J-106-0029-003 洗錢防制-新增實質受益人
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120s01p(PageParameters params)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		String mainId = params.getString("mainId", "");
		String formL120s01p = params.getString("L120S01PForm");
		JSONObject jsonL120s01p = JSONObject.fromObject(formL120s01p);
		String type = jsonL120s01p.getString("toglePersonType");
		String rName = Util.toSemiCharString(Util.trim(jsonL120s01p
				.getString("rName")));

		String rType = Util.trim(params.getString("rType"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		L120S01P l120s01pold = null;

		// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
		// if (Util.equals(rType,
		// UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人) ||
		// Util.equals(rType,
		// UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員)) {
		// l120s01pold = amlRelateService.findL120s01pByRNameWithRType(mainId,
		// custId, dupNo, rType, jsonL120s01p.getString("rName"));
		// }
		l120s01pold = amlRelateService.findL120s01pByRNameWithRType(mainId,
				custId, dupNo, rType, jsonL120s01p.getString("rName"));

		L120S01P l120s01p = amlRelateService.findL120s01pByOid(oid);
		if (l120s01p == null) {
			l120s01p = new L120S01P();
			l120s01p.setMainId(mainId);
			l120s01p.setCreateTime(CapDate.getCurrentTimestamp());
			l120s01p.setCreator(user.getUserId());
			DataParse.toBean(jsonL120s01p, l120s01p);

			if (l120s01pold != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("custId", l120s01p.getRName());
				// EFD3011=ERROR|$\{custId\}此ID已經存在 ！！|
				throw new CapMessageException(RespMsgHelper.getMessage("EFD3011", param), getClass());
			}
		} else {
			DataParse.toBean(jsonL120s01p, l120s01p);
			if (l120s01pold != null
					&& !l120s01pold.getOid().equals(l120s01pold.getOid())) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("custId", l120s01p.getRName());
				// EFD3011=ERROR|$\{custId\}此ID已經存在 ！！|
				throw new CapMessageException(RespMsgHelper.getMessage("EFD3011", param), getClass());
			}
		}

		l120s01p.setCustId(custId);
		l120s01p.setDupNo(dupNo);

		// 戶名轉半形
		l120s01p.setRName(Util.toSemiCharString(Util.trim(l120s01p.getRName())));
		l120s01p.setREName(Util.toSemiCharString(Util.trim(l120s01p.getREName())));
		l120s01p.setType(type);
		l120s01p.setRType(rType);

		Integer seqNum = l120s01p.getSeqNum() == null ? 0 : l120s01p
				.getSeqNum();
		if (seqNum == 0) {

			L120S01P l120s01pMax = amlRelateService
					.findL120s01pMaxSeqNumByMainIdAndCustIdWithRType(mainId,
							custId, dupNo, rType);
			if (l120s01pMax == null) {
				l120s01p.setSeqNum(1);
			} else {
				l120s01p.setSeqNum(l120s01pMax.getSeqNum() == null ? 1
						: l120s01pMax.getSeqNum() + 1);
			}

		}

		// 當客戶統編不為空時要把他轉大寫
		l120s01p.setRId(l120s01p.getRId() == null ? "" : l120s01p.getRId()
				.toUpperCase());
		l120s01p.setRDupNo(l120s01p.getRDupNo() == null ? "" : l120s01p
				.getRDupNo().toUpperCase());

		lmsService.save(l120s01p);
		return result;// 傳回執行這個動作的AjAX
	}// ;

	/**
	 * 刪除(多筆)相關人 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL120s01p(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		String mainId = params.getString("mainId");
		if (oids.length > 0) {
			// 變更主檔檢核
			if (amlRelateService.deleteListL120s01p(oids)) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
			}
		}
		return result;
	}// ;

	/**
	 * 儲存相關人文字串 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120s01pBeneficiaryStr(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString("mainId");
		String custId = params.getString("custId");
		String dupNo = params.getString("dupNo");
		String rType = Util.trim(params.getString("rType"));
		String callFrom = Util.trim(params.getString("callFrom"));
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		String beneficiary = Util.trim(params.getString("beneficiary"));

		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());

		// 空白預設顯示無
		if (Util.equals(beneficiary, "")) {
			if (Util.notEquals(rType,
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)) {

				beneficiary = pop.getProperty("nohave");
			}

		}

		if (Util.equals(callFrom, "1")) {
			// 簽報書
			L120S01B l120s01b = amlRelateService.findL120s01bByUniqueKey(
					mainId, custId, dupNo);
			if (l120s01b != null) {

				// J-107-0070-001 Web e-Loan
				// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
				if (Util.equals(rType,
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)) {
					// 實質受益人
					if (Util.equals(beneficiary, "")) {
						// 實質受益人空白時，新加坡要顯示不適用
						if (amlRelateService
								.needShowNotApplicableWhenNoEffective(unitNo)) {
							beneficiary = pop.getProperty("notApplicable");
						} else {
							beneficiary = pop.getProperty("nohave");
						}
					}
					l120s01b.setBeneficiary(beneficiary);

				} else if (Util
						.equals(rType,
								UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員)) {
					// J-107-0070-001 Web e-Loan
					// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
					// 高階管理人員]
					l120s01b.setSeniorMgr(beneficiary);
				} else if (Util.equals(rType,
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人)) {
					// J-108-0039_05097_B1001 Web e-Loan
					// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
					// 具控制權人
					l120s01b.setCtrlPeo(beneficiary);
				} else {
					// 高階管理人員
					l120s01b.setSeniorMgr(beneficiary);
				}

				lmsService.save(l120s01b);
			}
		} else if (Util.equals(callFrom, "2")) {
			// 動審表
			L164S01A l164s01a = amlRelateService.findL164s01aByUniqueKey(
					mainId, custId, dupNo);
			if (l164s01a != null) {

				// J-107-0070-001 Web e-Loan
				// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
				if (Util.equals(rType,
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)) {
					// 實質受益人
					if (Util.equals(beneficiary, "")) {
						// 實質受益人空白時，新加坡要顯示不適用
						if (amlRelateService
								.needShowNotApplicableWhenNoEffective(unitNo)) {
							beneficiary = pop.getProperty("notApplicable");
						} else {
							beneficiary = pop.getProperty("nohave");
						}
					}
					l164s01a.setBeneficiary(beneficiary);
					result.set("beneficiary", beneficiary);
				} else if (Util
						.equals(rType,
								UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員)) {
					// J-107-0070-001 Web e-Loan
					// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
					// 高階管理人員
					l164s01a.setSeniorMgr(beneficiary);
				} else if (Util.equals(rType,
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人)) {
					// J-108-0039_05097_B1001 Web e-Loan
					// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
					// 具控制權人
					l164s01a.setCtrlPeo(beneficiary);
				} else {
					// 高階管理人員
					l164s01a.setSeniorMgr(beneficiary);
				}

				lmsService.save(l164s01a);
			} else {

				beneficiary = pop.getProperty("nohave");

			}

		}
		result.set("beneficiary", beneficiary);
		return result;
	}

	/**
	 * 引進實質受益人 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult applyBeneficiaryData(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		StringBuffer errMsg = new StringBuffer("");
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String mainId = Util.trim(params.getString("mainId"));

		List<L120S01P> l120s01ps = amlRelateService
				.findL120s01pByMainIdAndCustIdWithRType(mainId, custId, dupNo,
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);

		if (l120s01ps != null && !l120s01ps.isEmpty()) {
			for (L120S01P l120s01p : l120s01ps) {
				lmsService.delete(l120s01p);
			}
		}

		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());

		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(unitNo).getBrNoFlag());

		Map<String, String> beneficiaryMap = new HashMap<String, String>();

		if (!isOverSea) {
			// 國內
			Map<String, Object> cmfData = misDbService.selCMFLUNBNByCustId(
					custId, dupNo);

			if (cmfData == null || cmfData.isEmpty()) {
				// L120S09a.message10=借款人於0024-23洗錢防制維護作業尚未完成法人戶實際受益人身份確認。
				errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
				errMsg.append(pop.getProperty("L120S09a.message10", ""));
			} else {

				String BUSCD = Util.trim(MapUtils.getString(cmfData, "BUSCD"));
				String LUV_BEN_CONFIRM = Util.trim(MapUtils.getString(cmfData,
						"LUV_BEN_CONFIRM"));

				if (Util.notEquals(BUSCD, "")
						&& !LMSUtil.isBusCode_060000_130300(BUSCD)) {
					// 企業戶
					if (Util.notEquals(LUV_BEN_CONFIRM, "Y")
							&& Util.notEquals(LUV_BEN_CONFIRM, "N")) {
						// L120S09a.message10=借款人於0024-23洗錢防制維護作業尚未完成法人戶實際受益人身份確認。
						errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
						errMsg.append(pop.getProperty("L120S09a.message10", ""));
					}

					if (Util.equals(LUV_BEN_CONFIRM, "N")) {
						// L120S09a.message12=借款人於0024-23洗錢防制維護作業法人戶實際受益人身份為無須確認。
						errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
						errMsg.append(pop.getProperty("L120S09a.message12", ""));
					}
				} else {
					// 個人戶無須實際受益人
					// L120S09a.message13=個人戶無須確認實際受益人身份。
					errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
					errMsg.append(pop.getProperty("L120S09a.message13", ""));
				}

				if (Util.equals(LUV_BEN_CONFIRM, "Y")) {

					int count = 0;
					for (int i = 1; i < 5; i++) {
						String LUB_BEN_ID = Util.trim(
								MapUtils.getString(cmfData, "LUB_BEN" + i
										+ "_ID")).toUpperCase();
						String LUB_BEN_CNAME = Util.toSemiCharString(Util
								.trim(MapUtils.getString(cmfData, "LUB_BEN" + i
										+ "_CNAME")));
						String LUB_BEN_ENAME = Util.trim(MapUtils.getString(
								cmfData, "LUB_BEN" + i + "_ENAME"));
						String LUB_BEN_BIRTH_DATE = Util.trim(MapUtils
								.getString(cmfData, "LUB_BEN" + i
										+ "_BIRTH_DATE"));
						String LUB_BEN_NATION = Util.trim(MapUtils.getString(
								cmfData, "LUB_BEN" + i + "_NATION"));
						String LUB_BEN_PEPS = Util.trim(MapUtils.getString(
								cmfData, "LUB_BEN" + i + "_PEPS"));

						String rName = LUB_BEN_CNAME;
						if (Util.equals(LUB_BEN_CNAME, "")
								&& Util.notEquals(LUB_BEN_ENAME, "")) {
							rName = LUB_BEN_ENAME;
						}

						if (Util.notEquals(LUB_BEN_ID, "")
								|| Util.notEquals(LUB_BEN_CNAME, "")
								|| Util.notEquals(LUB_BEN_ENAME, "")) {

							// 判斷實際受益人ID + RNAME有沒有重覆
							if (beneficiaryMap.containsKey(LUB_BEN_ID + "-"
									+ rName)) {
								continue;
							} else {

								beneficiaryMap.put(LUB_BEN_ID + "-" + rName,
										rName);

								L120S01P l120s01p = new L120S01P();
								count = count + 1;
								l120s01p = new L120S01P();
								l120s01p.setMainId(mainId);
								l120s01p.setCreateTime(CapDate
										.getCurrentTimestamp());
								l120s01p.setCreator(user.getUserId());
								l120s01p.setCustId(custId);
								l120s01p.setDupNo(dupNo);
								l120s01p.setType("1");
								l120s01p.setRId(LUB_BEN_ID);
								l120s01p.setRDupNo("0");

								l120s01p.setRName(rName);

								l120s01p.setREName(LUB_BEN_ENAME);
								if (Util.equals(LUB_BEN_ENAME, "")
										&& Util.notEquals(LUB_BEN_CNAME, "")) {
									if (LUB_BEN_CNAME.length() == LUB_BEN_CNAME
											.getBytes().length) {
										l120s01p.setREName(LUB_BEN_CNAME);
									}
								}

								l120s01p.setRType(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);

								L120S01P l120s01pMax = amlRelateService
										.findL120s01pMaxSeqNumByMainIdAndCustIdWithRType(
												mainId,
												custId,
												dupNo,
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);
								if (l120s01pMax == null) {
									l120s01p.setSeqNum(1);
								} else {
									l120s01p.setSeqNum(l120s01pMax.getSeqNum() == null ? 1
											: l120s01pMax.getSeqNum() + 1);
								}

								l120s01p.setBirthDate(CapDate
										.parseDate(LUB_BEN_BIRTH_DATE));
								l120s01p.setNation(LUB_BEN_NATION);
								l120s01p.setPeps(LUB_BEN_PEPS);

								lmsService.save(l120s01p);
							}
						}
					}

					if (count == 0) {
						// L120S09a.message11={0}客戶檔無實質受益人資料可以引進
						errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
						errMsg.append(MessageFormat.format(
								pop.getProperty("L120S09a.message11"), "0024"));
					}
				}
			}

		} else {

			// 撈0024 行業對象別
			Map<String, Object> m0024 = icustSrv.findByIdDupNo(custId, dupNo);
			String busCode = (m0024 != null && !m0024.isEmpty()) ? Util
					.trim(MapUtils.getString(m0024, "BUSCD")) : "";
			if (Util.equals(busCode, "")) {
				// AML.error011=借款人於0024無行業對象別資料。
				errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
				errMsg.append(pop.getProperty("AML.error011"));
			}

			if (LMSUtil.isBusCode_060000_130300(busCode)) {
				// 個人戶無須實際受益人
				// L120S09a.message13=個人戶無須確認實際受益人身份。
				errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
				errMsg.append(pop.getProperty("L120S09a.message13", ""));
			} else {

				// 海外
				List<Map<String, Object>> cmfDataList = dwdbService
						.findDW_OTS_EFFECTIVE_OVS_By_CustId_And_BrNo(unitNo,
								custId, dupNo);

				if (cmfDataList == null || cmfDataList.isEmpty()) {
					// L120S09a.message11={0}客戶檔無實質受益人資料可以引進
					errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
					errMsg.append(MessageFormat.format(
							pop.getProperty("L120S09a.message11"), "AS400"));
				}

				for (Map<String, Object> cmfData : cmfDataList) {

					String CYC_DT = Util.trim(MapUtils.getString(cmfData,
							"CYC_DT"));
					String F1310_NAME = Util.toSemiCharString(Util
							.trim(MapUtils.getString(cmfData, "F1310_NAME")));
					String BENE_MEGAID = Util.trim(MapUtils.getString(cmfData,
							"BENE_MEGAID"));
					String F1310_TAIWAN_ID = Util.trim(MapUtils.getString(
							cmfData, "F1310_TAIWAN_ID"));
					String BENE_EFFECTIVE = Util.trim(MapUtils.getString(
							cmfData, "BENE_EFFECTIVE"));
					String BENE_ULITMATE = Util.trim(MapUtils.getString(
							cmfData, "BENE_ULITMATE"));

					if (Util.equals(BENE_EFFECTIVE, "Y")) {

						L120S01P l120s01p = new L120S01P();

						l120s01p = new L120S01P();
						l120s01p.setMainId(mainId);
						l120s01p.setCreateTime(CapDate.getCurrentTimestamp());
						l120s01p.setCreator(user.getUserId());
						l120s01p.setCustId(custId);
						l120s01p.setDupNo(dupNo);
						l120s01p.setType("1");

						if (Util.notEquals(F1310_TAIWAN_ID, "")) {
							l120s01p.setRId(F1310_TAIWAN_ID);
							l120s01p.setRDupNo("0");
						} else if (Util.notEquals(BENE_MEGAID, "")) {
							l120s01p.setRId(Util.getLeftStr(BENE_MEGAID, 10));
							l120s01p.setRDupNo(Util.equals(
									Util.getRightStr(BENE_MEGAID, 1), "") ? "0"
									: Util.getRightStr(BENE_MEGAID, 1));
						} else {
							l120s01p.setRId("");
							l120s01p.setRDupNo("");
						}

						l120s01p.setRName(F1310_NAME);
						l120s01p.setREName(F1310_NAME);

						l120s01p.setRType(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);

						L120S01P l120s01pMax = amlRelateService
								.findL120s01pMaxSeqNumByMainIdAndCustIdWithRType(
										mainId,
										custId,
										dupNo,
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);
						if (l120s01pMax == null) {
							l120s01p.setSeqNum(1);
						} else {
							l120s01p.setSeqNum(l120s01pMax.getSeqNum() == null ? 1
									: l120s01pMax.getSeqNum() + 1);
						}

						l120s01p.setQueryDateS(CYC_DT == null ? null : CapDate
								.parseDate(CYC_DT));
						l120s01p.setBirthDate(null);
						l120s01p.setNation("");
						l120s01p.setPeps("");

						lmsService.save(l120s01p);
					}
				}
			}

		}

		result.set("errMsg", errMsg.toString());
		return result;

	}

	/**
	 * 取得姓名資料 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getMisCustData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId", "")).toUpperCase();
		String dupNo = Util.trim(params.getString("dupNo", "")).toUpperCase();
		if (custId.length() == 0 || dupNo.length() == 0) {
			return result;
		}
		Map<String, Object> custData = (Map<String, Object>) misCustdataService
				.findBussTypeByIdDupNo(custId, dupNo);
		if (custData != null && !custData.isEmpty()) {
			result.set("custName", Util.toSemiCharString(Util
					.trim((String) custData.get("CNAME"))));
			result.set("custEName", (String) custData.get("ENAME"));
			// if ("zh_TW".equals(parent.getLocale().toString())) {
			// result.set("custName", (String) custData.get("CNAME"));
			// } else {
			// result.set("custName", (String) custData.get("ENAME"));
			// }
		}
		return result;
	}

	/**
	 * 重新引進查詢名單 J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業
	 * ，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult importBlackListNew(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		// 留存舊的資料(英文戶名)
		Map<String, String> oldAmlListIdMap = new HashMap<String, String>();
		Map<String, String> oldAmlListNameMap = new HashMap<String, String>();
		List<L120S09A> l120s09as = amlRelateService
				.findL120s09aByMainId(mainId);
		if (l120s09as != null && !l120s09as.isEmpty()) {
			for (L120S09A l120s09a : l120s09as) {
				String tCustId = Util.trim(l120s09a.getCustId());
				String tDupNo = Util.trim(l120s09a.getDupNo());
				String tCustName = Util.toSemiCharString(String.valueOf(Util
						.trim(l120s09a.getCustName())));
				String tCustEName = Util.toSemiCharString(Util.trim(l120s09a
						.getCustEName()));
				String fullKey = tCustId + tDupNo;
				if (Util.notEquals(fullKey, "")) {
					if (!oldAmlListIdMap.containsKey(fullKey)) {
						oldAmlListIdMap.put(fullKey, tCustEName);
					}
				}

				if (Util.notEquals(tCustName, "")) {
					if (!oldAmlListNameMap.containsKey(tCustName)) {
						oldAmlListNameMap.put(tCustName, tCustEName);
					}
				}

			}
			amlRelateService.deleteListL120s09a(l120s09as);
		}

		// 開始引進名單開始********************************************************************************************************************************

		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);
		if (l120m01a != null) {
			this.importBlackListInnerForL120m01a(mainId);
		} else {
			// 動審表
			L160M01A l160m01a = amlRelateService.findModelByMainId(
					L160M01A.class, mainId);
			if (l160m01a != null) {
				this.importBlackListInnerForL160m01a(mainId);
			}

		}

		// 引進名單結束************************************************************************************************************************************

		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		// 如果是新模式切回舊模式，要把L120S09B(如果有的話，NCRESULT 清掉)，才不會判斷錯誤(但L120S09B不要刪掉)
		L120S09B l120s09b = amlRelateService.findL120s09bByMainId(mainId);
		if (l120s09b != null) {
			l120s09b.setNcResult("");
			l120s09b.setUniqueKey("");
			l120s09b.setNcCaseId("");
			l120s09b.setQueryDateS(null);
			l120s09b.setQueryBrId("");
			l120s09b.setQueryUser("");
			l120s09b.setWayMode("");
			lmsService.save(l120s09b);
		}

		// 引進名單後，撈英文戶名*********************************

		if (l120m01a == null) {
			// 動審表
			L160M01A l160m01a = amlRelateService.findModelByMainId(
					L160M01A.class, mainId);
			if (l160m01a != null) {
				// 簽報書
				l120m01a = amlRelateService.findL120m01aByL160m01a(l160m01a);
			}
		}

		// 取得簽報書AND徵信報告AML資料
		Map<String, String> lmsAndCesAmlListIdCNameMap = new HashMap<String, String>();
		Map<String, String> lmsAndCesAmlListIdENameMap = new HashMap<String, String>();
		Map<String, String> lmsAndCesAmlListNameMap = new HashMap<String, String>();
		amlRelateService.getLMSAndCesDocAMLCustNameMap(l120m01a,
				lmsAndCesAmlListIdCNameMap, lmsAndCesAmlListIdENameMap,
				lmsAndCesAmlListNameMap);

		List<L120S09A> newl120s09as = amlRelateService
				.findL120s09aByMainId(mainId);
		if (newl120s09as != null && !newl120s09as.isEmpty()) {
			for (L120S09A l120s09a : newl120s09as) {
				String xCustRelation = l120s09a.getCustRelation();
				String xCustId = l120s09a.getCustId();
				String xDupNo = l120s09a.getDupNo();
				String xCustName = l120s09a.getCustName();
				String xCustEName = l120s09a.getCustEName();
				String xCountry = Util.trim(l120s09a.getCountry());

				String[] strs = amlRelateService.getSortCustRelation(Util.trim(
						xCustRelation).split(","));

				// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
				boolean needCountry = false;

				for (int i = 0; i < strs.length; i++) {

					// J-107-0248_05097_B1001 Web
					// e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
					if (Util.notEquals(strs[i], "")
							&& amlRelateService
									.isCustRelationNeedCountry(strs[i])) {
						// 借款人(含共同借款人)及保證人(含一般保證、連帶保證、擔保品提供人)國別
						needCountry = true;
					}

				}

				// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
				// J-108-0169_05097_B1001 Web e-Loan企金授信系統AML/CFT查詢頁籤無自動引入國別
				// if (needCountry) {
				if (Util.equals(xCountry, "")) {
					if (Util.notEquals(xCustId, "")
							&& Util.notEquals(xDupNo, "")) {
						Map<String, String> cust = customerSrv
								.findRegPlaceHouseholdReg(xCustId, xDupNo);
						if (cust != null && !cust.isEmpty()) {
							String ntCode = MapUtils.getString(cust,
									"regPlace", "");
							if (Util.notEquals(ntCode, "")) {
								l120s09a.setCountry(ntCode);
								lmsService.save(l120s09a);
							}

						}
					}
				}
				// }

				if (Util.equals(xCustEName, "")) {

					// 1.抓最新的MIS英文名稱
					Map<String, String> nameMap = amlRelateService
							.queryL120s09aNewCustNameForAML("2", mainId,
									l120m01a, xCustRelation, xCustId, xDupNo,
									xCustName, xCustEName);

					String newCustName = Util.toSemiCharString(Util
							.trim(nameMap.get("newCustName")));
					String newCustEName = Util.toSemiCharString(Util
							.trim(nameMap.get("newCustEName")));

					if (Util.notEquals(newCustEName, "")) {
						// 有找到新的英文戶名
						l120s09a.setCustEName(newCustEName);
						lmsService.save(l120s09a);
						continue;
					}

					// 2.抓執行本功能之前以存在於簽報書AML頁籤的資料
					// 2.1 BY ID
					if (Util.equals(newCustEName, "")) {
						if (Util.notEquals(xCustId + xDupNo, "")) {
							if (oldAmlListIdMap.containsKey(xCustId + xDupNo)) {
								newCustEName = Util.trim(oldAmlListIdMap
										.get(xCustId + xDupNo));

								if (Util.notEquals(newCustEName, "")) {
									// 有找到新的英文戶名
									l120s09a.setCustEName(Util
											.toSemiCharString(Util
													.trim(newCustEName)));
									lmsService.save(l120s09a);
									continue;
								}
							}
						}
					}

					// 2.2 BY CUSTNAME
					if (Util.equals(newCustEName, "")) {
						if (Util.notEquals(xCustName, "")) {
							if (oldAmlListNameMap.containsKey(xCustName)) {
								newCustEName = Util.trim(oldAmlListNameMap
										.get(xCustName));
								if (Util.notEquals(newCustEName, "")) {
									// 有找到新的英文戶名
									l120s09a.setCustEName(Util
											.toSemiCharString(Util
													.trim(newCustEName)));
									lmsService.save(l120s09a);
									continue;
								}
							}
						}
					}

					// 3.抓簽報書或徵信報告*******************************************************
					// 3.1 BY ID
					if (Util.equals(newCustEName, "")) {
						if (Util.notEquals(xCustId + xDupNo, "")) {
							if (lmsAndCesAmlListIdENameMap.containsKey(xCustId
									+ xDupNo)) {
								newCustEName = Util
										.trim(lmsAndCesAmlListIdENameMap
												.get(xCustId + xDupNo));

								if (Util.notEquals(newCustEName, "")) {
									// 有找到新的英文戶名
									l120s09a.setCustEName(Util
											.toSemiCharString(Util
													.trim(newCustEName)));
									lmsService.save(l120s09a);
									continue;
								}
							}
						}
					}

					// 3.2 BY CUSTNAME
					if (Util.equals(newCustEName, "")) {
						if (Util.notEquals(xCustName, "")) {
							if (lmsAndCesAmlListNameMap.containsKey(xCustName)) {
								newCustEName = Util
										.trim(lmsAndCesAmlListNameMap
												.get(xCustName));
								if (Util.notEquals(newCustEName, "")) {
									// 有找到新的英文戶名
									l120s09a.setCustEName(Util
											.toSemiCharString(Util
													.trim(newCustEName)));
									lmsService.save(l120s09a);
									continue;
								}
							}
						}
					}

				}
			}

		}

		return result;
	}

	// J-106-0238-001
	// **************************************************************************************************************************************************************************************
	/**
	 * J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 傳送名單掃描
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult sendAmlList(PageParameters params)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		unitNo = (instead ? queryBrId : user.getUnitNo());

		String mainCustId = "";
		String mainDupNo = "";
		L160M01A l160m01a = null;
		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);

		String className = "";

		if (l120m01a != null) {
			// 簽報書
			mainCustId = l120m01a.getCustId();
			mainDupNo = l120m01a.getDupNo();
			className = "L120M01A";
		} else {
			// 動審表
			l160m01a = amlRelateService.findModelByMainId(L160M01A.class,
					mainId);
			if (l160m01a != null) {
				// 動審表
				mainCustId = l160m01a.getCustId();
				mainDupNo = l160m01a.getDupNo();
				className = "L160M01A";
			}
		}

		// 檢核名單完成才能按-BEGIN************************************************************

		if (Util.equals(className, "L120M01A")) {
			l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
					mainId);
			// 檢查實質受益人欄未有無輸入 + 有沒有完成身分確認
			// 主要借款人+共同借款人
			LinkedHashMap<String, String> allBorrowerIdMap = new LinkedHashMap<String, String>();
			List<L140M01A> listL140m01a = amlRelateService
					.findL140m01aListByL120m01cMainId(mainId,
							UtilConstants.Cntrdoc.ItemType.額度明細表);
			for (L140M01A l140m01a : listL140m01a) {
				boolean isProPerty8 = LMSUtil.isContainValue(
						Util.trim(l140m01a.getProPerty()),
						UtilConstants.Cntrdoc.Property.取消);
				// 除不變外取消，其他借款人要檢查0024-23有沒有完成實質受益人身分確認
				if (!isProPerty8) {
					String custId = Util.trim(l140m01a.getCustId());
					String dupNo = Util.trim(l140m01a.getDupNo());
					String custName = Util.trim(l140m01a.getCustName());
					String fullCustId = custId + "-" + dupNo;

					// J-108-0145_05097_B1001 Web e-Loan
					// 國內外企金授信私募基金案件調整實質受益人控管
					if (Util.notEquals(amlRelateService
							.canPassAmlRelativeAndRiskLvlChk(l140m01a, custId,
									dupNo), "Y")) {
						if (!allBorrowerIdMap.containsKey(fullCustId)) {
							allBorrowerIdMap.put(fullCustId, custName);
						}
					}

					// 額度明細表共同借款人
					Set<L140M01J> l140m01js = l140m01a.getL140m01j();
					for (L140M01J l140m01j : l140m01js) {
						String cCustId = Util.trim(l140m01j.getCustId());
						String cDupNo = Util.trim(l140m01j.getDupNo());
						String cCustName = Util.trim(l140m01j.getCustName());
						String cFullCustId = cCustId + "-" + cDupNo;

						// J-108-0145_05097_B1001 Web e-Loan
						// 國內外企金授信私募基金案件調整實質受益人控管
						if (Util.notEquals(amlRelateService
								.canPassAmlRelativeAndRiskLvlChk(l140m01a,
										cCustId, cDupNo), "Y")) {
							if (!allBorrowerIdMap.containsKey(cFullCustId)) {
								allBorrowerIdMap.put(cFullCustId, cCustName);
							}
						}

					}
				}
			}

			if (allBorrowerIdMap != null && !allBorrowerIdMap.isEmpty()) {
				if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a
						.getDocType())) {

					// 檢查實質受益人
					String errMsg = amlRelateService
							.chkBeneficiaryIsOkForRptDoc(mainId,
									allBorrowerIdMap);
					if (Util.notEquals(errMsg, "")) {
						// AML.error006=借款人「{0}」於{1}尚未完成法人戶實際受益人身份確認
						throw new CapMessageException(errMsg, getClass());
					}

					// J-107-0070-001 Web e-Loan
					// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
					// 檢查高階管理人員
					String errMsg_smgr = amlRelateService
							.chkSeniorMgrIsOkForRptDoc(mainId, allBorrowerIdMap);
					if (Util.notEquals(errMsg_smgr, "")) {
						// AML.error022=「{0}」借款人基本資料高階管理人員欄位不得空白
						// AML.error023=「{0}」借款人0024-23無「高階管理人員」資料
						// AML.error024=「{0}」借款人0024-23有「高階管理人員」資料，e-Loan借款人基本資料「高階管理人員」不得為「無」
						throw new CapMessageException(errMsg_smgr, getClass());
					}

					// J-108-0039_05097_B1001 Web e-Loan
					// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
					// 檢查具控制權人
					String errMsg_ctrlPeo = amlRelateService
							.chkCtrlPeoIsOkForRptDoc(mainId, allBorrowerIdMap);
					if (Util.notEquals(errMsg_ctrlPeo, "")) {
						// AML.error027=「{0}」借款人基本資料「具控制權人」欄位不得空白
						// AML.error028=「{0}」借款人0024-23無「具控制權人」資料
						// AML.error029=「{0}」借款人0024-23有「具控制權人」資料，e-Loan借款人基本資料「具控制權人」不得為「無」
						throw new CapMessageException(errMsg_ctrlPeo,
								getClass());
					}

				}
			}

			// 檢查黑名單
			amlRelateService.chkBlackListFullExitForRptDoc(mainId, false);

		} else if (Util.equals(className, "L160M01A")) {

			l160m01a = amlRelateService.findModelByMainId(L160M01A.class,
					mainId);

			// BGN J-106-0029-004
			// 洗錢防制-動審表新增洗錢防制頁籤************************************************
			// 檢查實質受益人與負責人

			// 主要借款人
			LinkedHashMap<String, String> cntrNoMainBorrowerMap = new LinkedHashMap<String, String>();

			// 主要借款人+共同借款人
			LinkedHashMap<String, String> allBorrowerIdMap = new LinkedHashMap<String, String>();
			Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
			if (l160m01bs != null && !l160m01bs.isEmpty()) {
				for (L160M01B l160m01b : l160m01bs) {
					L140M01A l140m01a = amlRelateService.findModelByMainId(
							L140M01A.class, l160m01b.getReMainId());
					if (l140m01a != null) {
						String chkId = Util.trim(l140m01a.getCustId());
						String chkDupNo = Util.trim(l140m01a.getDupNo());
						if (Util.notEquals(chkId, "")
								&& Util.notEquals(chkDupNo, "")) {
							if (!cntrNoMainBorrowerMap.containsKey(l160m01b
									.getCntrNo())) {
								cntrNoMainBorrowerMap.put(l160m01b.getCntrNo(),
										chkId + "-" + chkDupNo);
							}

							if (!allBorrowerIdMap.containsKey(chkId + "-"
									+ chkDupNo)) {
								allBorrowerIdMap.put(chkId + "-" + chkDupNo,
										l140m01a.getCustName());
							}

						}
					}
				}
			}

			List<L162S01A> l162m01as = (List<L162S01A>) amlRelateService
					.findListByMainId(L162S01A.class, mainId);

			for (L162S01A l162s01a : l162m01as) {
				if (Util.equals(l162s01a.getRType(),
						UtilConstants.lngeFlag.共同借款人)) {
					String chkId = Util.trim(l162s01a.getRId());
					String chkDupNo = Util.trim(l162s01a.getRDupNo());
					if (Util.notEquals(chkId, "")
							&& Util.notEquals(chkDupNo, "")) {
						if (!allBorrowerIdMap.containsKey(chkId + "-"
								+ chkDupNo)) {
							allBorrowerIdMap.put(chkId + "-" + chkDupNo,
									l162s01a.getRName());
						}
					}
				}
			}

			if (cntrNoMainBorrowerMap != null
					&& !cntrNoMainBorrowerMap.isEmpty()) {
				// 檢查本次動用之額度與主借款人是否未列於相關報表->主從債務人資料表中。
				String errMsg = amlRelateService.chkMainBorrowerInL162S01A(
						mainId, cntrNoMainBorrowerMap);
				if (Util.notEquals(errMsg, "")) {
					// AML.error009=本次動用之額度與主借款人{0}未列於相關報表->主從債務人資料表中。
					throw new CapMessageException(errMsg, getClass());
				}

			}

			if (allBorrowerIdMap != null && !allBorrowerIdMap.isEmpty()) {
				// 檢查實質受益人欄未有無輸入 + 有沒有完成身分確認
				String errMsg = amlRelateService.chkBeneficiaryIsOkForDrawDown(
						mainId, allBorrowerIdMap);
				if (Util.notEquals(errMsg, "")) {
					// AML.error008=借款人/共同借款人{0}於相關報表->主從債務人資料表中尚有洗錢防制所需欄位未完成輸入。
					throw new CapMessageException(errMsg, getClass());
				}

				// J-107-0070-001 Web e-Loan
				// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
				// 檢查高階管理人員
				String errMsg_smgr = amlRelateService
						.chkSeniorMgrIsOkForDrawDown(mainId, allBorrowerIdMap);
				if (Util.notEquals(errMsg_smgr, "")) {
					// AML.error022=「{0}」借款人基本資料高階管理人員欄位不得空白
					// AML.error023=「{0}」借款人0024-23無「高階管理人員」資料
					// AML.error024=「{0}」借款人0024-23有「高階管理人員」資料，e-Loan借款人基本資料「高階管理人員」不得為「無」
					throw new CapMessageException(errMsg_smgr, getClass());
				}

				// J-108-0039_05097_B1001 Web e-Loan
				// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
				// 檢查具控制權人
				String errMsg_ctrlPeo = amlRelateService
						.chkCtrlPeoIsOkForDrawDown(mainId, allBorrowerIdMap);
				if (Util.notEquals(errMsg_ctrlPeo, "")) {
					// AML.error027=「{0}」借款人基本資料「具控制權人」欄位不得空白
					// AML.error028=「{0}」借款人0024-23無「具控制權人」資料
					// AML.error029=「{0}」借款人0024-23有「具控制權人」資料，e-Loan借款人基本資料「具控制權人」不得為「無」
					throw new CapMessageException(errMsg_ctrlPeo, getClass());
				}

			}

			// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
			// 檢查黑名單
			amlRelateService.chkBlackListFullExitForDrawDown(mainId, false);

			// END J-106-0029-004
			// 洗錢防制-動審表新增洗錢防制頁籤************************************************

		}

		// 欄位檢核*******************************************************************
		// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
		amlRelateService.chkBlackListDataOk(mainId);

		// 檢核名單完成才能按-END************************************************************

		// J-112-0534 簽報書才需要發查T70
		String importT70Result = "";
		if (l120m01a != null) {
			// 海外不用發查T70
			if (Util.notEquals("5", l120m01a.getTypCd())) {
				if (clsService.is_function_on_codetype("LMS_AMLAutoImportT70")) {
					// 先檢查
					amlRelateService.checkL120m01eForT70(l120m01a);
					// 查T70
					StringBuilder over2MonthSb = amlRelateService
							.importL120s09aT70Data(l120m01a);
					if (Util.isNotEmpty(over2MonthSb)) {
						importT70Result = over2MonthSb.toString()
								+ "之T70資料來源已逾2個月";
					}
				}
			}
		}

		// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊
		amlRelateService.importL120s09aWarnData(mainId);

		// 開始傳送名單到SAS**********************************************************************************
		L120S09B l120s09b = null;

		// I-111-0089 調整授信審查處子系統e-loan，除徵信系統外，其餘排除掃瞄PEPs名單。
		Map<String, String[]> pepsMap = new HashMap<String, String[]>();
		// P-108-0046_05097_B1001 Web e-Loan配合Oracle系統建置，修改AML/CFT相關功能。
		if (Util.equals(className, "L120M01A")) {
			l120s09b = amlRelateService.initL120s09b(l120m01a, unitNo);
			pepsMap = amlRelateService.getAmlListPepsMap(l120m01a);
		} else if (Util.equals(className, "L160M01A")) {
			l120s09b = amlRelateService.initL120s09b(l160m01a, unitNo);
			pepsMap = amlRelateService.getAmlListPepsMap(l160m01a);
		}

		if (l120s09b == null) {
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}

		l120s09b.setQueryDateS(CapDate.getCurrentTimestamp());
		result = this.sendAmlListInner(mainId, mainCustId, mainDupNo, l120s09b,
				pepsMap);

		l120s09b.setNcResult(UtilConstants.SasNcResult.掃描中);
		lmsService.save(l120s09b);

		Date blackListQDate = CapDate.getCurrentTimestamp();

		result.set("blackListQDate", CapDate.formatDate(blackListQDate,
				UtilConstants.DateFormat.YYYY_MM_DD));

		result.set("ncResult", l120s09b.getNcResult());
		result.set("refNo", l120s09b.getRefNo());
		result.set("uniqueKey", l120s09b.getUniqueKey());
		result.set("ncCaseId", l120s09b.getNcCaseId());
		result.set("sCaseBrId", (instead ? queryBrId : user.getUnitNo()));
		if (Util.isNotEmpty(Util.nullToSpace(l120s09b.getQueryUser()))) {
			result.set("sQueryUser", l120s09b.getQueryUser() + " - "
					+ lmsService.getUserName(l120s09b.getQueryUser()));
		} else {
			result.set("sQueryUser",
					user.getUserId() + " - " + user.getUserName());
		}
		result.set("importT70Result", importT70Result);

		return result;
	}

	/**
	 * J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 傳送名單掃描
	 * 
	 * @param mainId
	 * @param mainCustId
	 * @param mainDupNo
	 * @return
	 * @throws CapException
	 */
	public CapAjaxFormResult sendAmlListInner(String mainId, String mainCustId,
			String mainDupNo, L120S09B l120s09b, Map<String, String[]> pepsMap)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());

		IBranch branch = branchSrv.getBranch(unitNo);
		String tCallSas = Util.trim(branch.getCallSas());

		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(unitNo).getBrNoFlag());
		String countryType = Util.trim(branchSrv.getBranch(unitNo)
				.getCountryType());

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);

		Map<String, String> countryMap = codetypeservice.findByCodeType(
				"CountryCode", "en");

		NumberFormat n = new DecimalFormat("000");

		// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
		List<L120S09A> l120s09as = amlRelateService
				.findL120s09aByMainIdWithShowOrder(mainId);

		ArrayList<L120S09A> newL120s09as = new ArrayList<L120S09A>();
		ArrayList<ElAmlItem> elamlitems = new ArrayList<ElAmlItem>();
		int checkSeq = 0;

		// 設定AML HEADER
		ElAml elaml = this.generateElAml(l120s09b);

		// I-111-0089 調整授信審查處子系統e-loan，除徵信系統外，其餘排除掃瞄PEPs名單。
		// 引進調查結果說明
		Map<String, String> ncRrMap = new HashMap<String, String>();
		if (l120s09as != null && !l120s09as.isEmpty()) {

			for (L120S09A l120s09a : l120s09as) {
				String tCustId = Util.trim(l120s09a.getCustId());
				String tDupNo = Util.trim(l120s09a.getDupNo());
				String tCustName = Util.toSemiCharString(String.valueOf(Util
						.trim(l120s09a.getCustName())));
				String tCustEName = Util.trim(l120s09a.getCustEName());
				String qName = Util.notEquals(tCustEName, "") ? tCustEName
						: tCustName;

				if (Util.equals(qName, "")) {
					throw new CapMessageException(MessageFormat.format(
							pop.getProperty("L120S09a.message07"),
							pop.getProperty("L120S09a.custName")), getClass());
				}

				l120s09a.setBlackListCode("");
				l120s09a.setMemo("");
				l120s09a.setQueryDateS(l120s09b.getQueryDateS());
				l120s09a.setSeqNum(0);
				l120s09a.setCheckSeq(n.format(++checkSeq));
				l120s09a.setCheckResult("");
				l120s09a.setHitList("");
				// I-111-0089 調整授信審查處子系統e-loan，除徵信系統外，其餘排除掃瞄PEPs名單。
				// init
				l120s09a.setPassPeps("");
				l120s09a.setCesSn("");
				l120s09a.setCesNcResult("");
				// J-106-0238-001
				// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
				if (Util.notEquals(tCustId, "") && Util.notEquals(tDupNo, "")) {
					String CM1_AML_STATUS = amlRelateService
							.getCustIn0024AmlStatus(tCustId, tDupNo);
					l120s09a.setCm1AmlStatus(CM1_AML_STATUS);

					// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
					if (LMSUtil
							.isContainValue(
									Util.trim(l120s09a.getCustRelation()),
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶,
									",")) {
						String luvRiskLevel = amlRelateService
								.getCustLuvRiskLevel(unitNo, tCustId, tDupNo);
						l120s09a.setLuvRiskLevel(Util.trim(luvRiskLevel));
					} else {
						l120s09a.setLuvRiskLevel("");
					}

				}

				// I-111-0089 調整授信審查處子系統e-loan，除徵信系統外，其餘排除掃瞄PEPs名單。
				// 國內 & 企金(在pepsMap的時候就先判斷了，消金的話pepsMap會是null)
				if (!isOverSea) {
					String xFullKey = Util.trim(tCustId) + Util.trim(tDupNo);
					if (Util.isNotEmpty(xFullKey)) {
						if (pepsMap != null && !pepsMap.isEmpty()) {
							if (pepsMap.containsKey(xFullKey)) {
								l120s09a.setPassPeps("Y");
								String[] cesData = pepsMap.get(xFullKey);
								String formSys = null; // LMS or CES
								String tNcResult = null;
								String tNcResultRemark = null;
								String tSn = null; // 從徵信來才有
								formSys = ((cesData.length < 1) ? ""
										: cesData[0]);
								tNcResult = ((cesData.length < 2) ? ""
										: cesData[1]);
								tNcResultRemark = ((cesData.length < 3) ? ""
										: cesData[2]);
								tSn = ((cesData.length < 4) ? "" : cesData[3]);
								if (Util.equals(formSys, "CES")) {
									l120s09a.setCesSn(Util.trim(tSn));
									l120s09a.setCesNcResult(Util
											.trim(tNcResult));
									if (Util.isNotEmpty(tSn)
											&& !ncRrMap.containsKey(tSn)) {
										if (Util.isNotEmpty(tNcResultRemark)) {
											ncRrMap.put(tSn, tNcResultRemark);
										}
									}
								}
								/*
								 * 動審表沒有調查結果說明欄位 所以不用塞 else
								 * if(Util.equals(formSys, "LMS")){ // 同一份簽報書
								 * 說明都會一樣 取一個就好 if
								 * (!ncRrMap.containsKey(formSys)) {
								 * ncRrMap.put(formSys, tNcResultRemark); } }
								 */
							}
						}
					}
				}

				// 設定AML DETAIL-*************************
				ElAmlItem elamlitem = new ElAmlItem();

				String checkseq = l120s09a.getCheckSeq();
				String entityType = "01"; // 名稱 (無法區分名單種類)
				String entityRel = ""; // Entity_Relationship

				String[] strs = l120s09a.getCustRelation().split(",");

				String[] newItem = Util.trim(l120s09a.getCustRelation()).split(
						",");
				// 對陣列進行排序

				int i, j;
				String tmp;
				for (i = newItem.length - 1; i >= 0; i = i - 1) {
					for (j = 0; j < i; j = j + 1) {
						// if (newItem[j] > newItem[i])// 換（"小於"是由大到小）
						if (Util.parseInt((String) newItem[j]) > Util
								.parseInt((String) newItem[i]))// 換（"小於"是由大到小）
						{
							tmp = newItem[j];
							newItem[j] = newItem[i];
							newItem[i] = tmp;
						}
					}
				}

				// List<String> list = new ArrayList<String>();
				for (String strItem : newItem) {
					// list.add(strs[i]);
					// /** 洗錢防制控管對象 J-106-0029-002 洗錢防制-新增洗錢防制頁籤 */
					// interface L120s09aBlackListCtlTarget {
					// static final String 借戶 = "1";
					// static final String 共同借款人 = "2";
					// static final String 負責人 = "3";
					// static final String 連保人 = "4";
					// static final String 擔保品提供人 = "5";
					// static final String 關係企業 = "6";
					// static final String 實質受益人 = "7";
					// static final String 一般保證人 = "8";
					// static final String 應收帳款買方無追索 = "9";
					// }

					// Entity_Relationship
					// 01 客戶本人
					// 02 關係人(同客戶本人如為制裁名單不可敘做)
					// 03 其他關係人(可敘做)
					// 04 保證人 (信用卡)
					// 05 交易對手
					// 06 交易人國籍
					// 07 受匯款國別
					// 08 銀行
					// 09 進出口相關
					// 99 備註欄

					// J-107-0070-001 Web e-Loan
					// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
					String custRelation = strItem;
					if (Util.equals(custRelation,
							UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶)) {
						entityRel = "01"; // 01 客戶本
						break;
					} else if (Util
							.equals(custRelation,
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.共同借款人)
							|| Util.equals(
									custRelation,
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人)
							|| Util.equals(
									custRelation,
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)
							|| Util.equals(
									custRelation,
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員)
							|| Util.equals(
									custRelation,
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人)) {

						if (Util.notEquals(entityRel, "01")) {
							entityRel = "02"; // 02 關係人(同客戶本人如為制裁名單不可敘做)
						}
					} else {
						if (Util.notEquals(entityRel, "01")
								&& Util.notEquals(entityRel, "02")) {
							entityRel = "03"; // 03 其他關係人(可敘做)
						}

					}
				}

				String enitityRelDes = "";
				StringBuilder sb = new StringBuilder("");

				// J-110-00A2_05097_B1001 Web e-Loan配合紐行Oracle系統建置，修改AML相關功能。
				Map<String, String> custRelationMap = null;
				if (Util.equals(countryType, "US")) {
					custRelationMap = codetypeservice.findByCodeType(
							"BlackListRelation", "en");
				} else {
					Locale locale = LMSUtil.getLocale();
					custRelationMap = codetypeservice.findByCodeType(
							"BlackListRelation", locale.toString());
				}

				int enitityRelDesCount = 0;
				String hasOther = "";
				if (newItem.length > 2) {
					hasOther = "...";
				}
				// 只顯示2個關係說明，其他用...代表
				for (String strItem : newItem) {
					if (enitityRelDesCount >= 2) {
						break;
					}

					// P-108-0046_05097_B1001 Web
					// e-Loan配合Oracle系統建置，修改AML/CFT相關功能。
					// ORACLE 不能顯示中文"、"
					if (sb.length() > 0) {
						if (Util.equals(countryType, "US")) {
							sb.append(",");
						} else {
							sb.append("、");
						}

					}

					sb.append(Util.trim(Util.trim(custRelationMap.get(strItem))));

					enitityRelDesCount = enitityRelDesCount + 1;
				}

				enitityRelDes = Util.nullToSpace(sb.toString() + hasOther);// 相關身分

				String eName = Util.toSemiCharString(Util.trim(l120s09a
						.getCustEName()));

				// P-108-0046_05097_B1001 Web e-Loan配合Oracle系統建置，修改AML/CFT相關功能。
				String nonEname = "";
				nonEname = Util.trim(l120s09a.getCustName());

				// P-108-0046_05097_B1001 Web e-Loan配合Oracle系統建置，修改AML/CFT相關功能。
				/*
				 * if (Util.equals(tCallSas, "S")) { // ORACLE nonEname 不能放純英文
				 * nonEname = Util.trim(l120s09a.getCustName());
				 * 
				 * // 轉成半形 String semiName = Util
				 * .toSemiCharString(Util.trim(nonEname)); if
				 * (Util.equals(semiName, nonEname)) { // nonEname 裡面都是半形
				 * nonEname = ""; } } else { nonEname =
				 * Util.trim(l120s09a.getCustName()); }
				 */

				// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
				String country = Util.trim(l120s09a.getCountry());

				// e-Loan(企金、消金)徵信及授信管理系統，請增加可輸入借款人(含共同借款人)及保證人(含一般保證、連帶保證、擔保品提供人)國別之欄位；另徵信、簽報及動審AML/CFT頁籤，請將"國別"資料一併納入執行掃描制裁、管制名單(黑名單)。
				String idNo = "";
				if (!isOverSea) {
					// 國內要帶MEGA ID 給BTT-0015
					idNo = StringUtils.rightPad(tCustId, 10)
							+ (StringUtils.isEmpty(tDupNo) ? "0" : tDupNo);
				}

				String bsCode = "";
				String birth = "";
				String gender = "";
				String checkResult = ""; // AML_NameCheck (WS回傳或主動寫回結果) 回傳值不用塞
				String routeRule = ""; // AML_NameCheck (WS回傳或主動寫回結果) 回傳值不用塞
				String hitList = ""; // AML_NameCheck (WS回傳或主動寫回結果) 回傳值不用塞

				elamlitem.setUniKey(Util.trim(l120s09b.getUniqueKey()));
				elamlitem.setCheckseq(checkseq);
				elamlitem.setEntityType(entityType);
				elamlitem.setEntityRel(entityRel);
				elamlitem.setEnitityRelDes(enitityRelDes);

				if (Util.equals(countryType, "US")) {
					String tCountryName = "";
					if (Util.notEquals(country, "")) {
						if (countryMap.containsKey(country)) {
							tCountryName = " " + country;
						}
					}
					elamlitem.seteName(eName); // + tCountryName
				} else {
					elamlitem.seteName(eName);
				}

				boolean isOnlyEngName = amlRelateService
						.sendAmlOnlyEngName(unitNo);

				if (isOnlyEngName) {
					elamlitem.setNonEname("");
				} else {
					elamlitem.setNonEname(nonEname);
				}

				// if (Util.notEquals(countryType, "US")) {
				// elamlitem.setNonEname(nonEname);
				// } else {
				// if (Util.equals(tCallSas, "C")) {
				// // (新)紐約prime透過mq ->P
				// // (新)紐約ORACLE透過mq ->C
				// elamlitem.setNonEname(nonEname);
				// } else {
				// elamlitem.setNonEname("");
				// }
				// }

				elamlitem.setCountry(country);
				elamlitem.setIdNo(idNo);
				elamlitem.setBsCode(bsCode);
				elamlitem.setBirth(birth);
				elamlitem.setGender(gender);
				elamlitem.setCheckResult(checkResult);
				elamlitem.setRouteRule(routeRule);
				elamlitem.setHitList(hitList);
				// I-111-0089 調整授信審查處子系統e-loan，除徵信系統外，其餘排除掃瞄PEPs名單。
				elamlitem.setExcludePep(Util.trim(l120s09a.getPassPeps()));

				// ******************************************
				elamlitems.add(elamlitem);
				newL120s09as.add(l120s09a);
			}

		}

		// I-111-0089 調整授信審查處子系統e-loan，除徵信系統外，其餘排除掃瞄PEPs名單。
		// 更新調查結果說明
		if (l120s09b != null) {
			if (ncRrMap != null && !ncRrMap.isEmpty()) {
				StringBuilder ncRrStr = new StringBuilder();

				String orgStr = Util.trim(l120s09b.getNcResultRemark());
				ncRrStr.append(orgStr);

				for (Map.Entry<String, String> entry : ncRrMap.entrySet()) {
					String ncRrKey = entry.getKey(); // fromSys or tSn
					String ncRr = entry.getValue();
					ncRrStr.append(ncRrStr.length() > 0 ? "；" : "");
					ncRrStr.append(ncRr);
				}

				if (Util.isNotEmpty(ncRrStr.toString())) {
					l120s09b.setNcResultRemark(Util.truncateString(
							ncRrStr.toString(), 1000));
					lmsService.save(l120s09b);
				}
			}
		}

		if (newL120s09as != null && !newL120s09as.isEmpty()) {

			AmlStrategy as = (instead ? amlRelateService
					.getAmlStrategy(queryBrId) : amlRelateService
					.getAmlStrategy(""));

			as.sendAmlList(elaml, elamlitems);

			amlRelateService.saveL120s09aList(newL120s09as);
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;

	}

	/**
	 * J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 取得黑名單查詢結果
	 * 
	 * @param mainId
	 * @param mainCustId
	 * @param mainDupNo
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult checkAmlResult(PageParameters params)
			throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());

		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(unitNo).getBrNoFlag());

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("resultMsg", "");
		// String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		// Map<String, String> chkMap = amlRelateService.checkAmlNewFuncMode();
		Map<String, String> chkMap = new HashMap<String, String>();
		if (instead) {
			chkMap = amlRelateService.checkAmlNewFuncMode(queryBrId);
		} else {
			chkMap = amlRelateService.checkAmlNewFuncMode("");
		}

		String callNewFunc = MapUtils.getString(chkMap, "callNewFunc", "N");
		String callSasTW = MapUtils.getString(chkMap, "callSasTW", "N");

		L120S09B l120s09b = amlRelateService.findL120s09bByMainId(mainId);

		if (l120s09b == null) {
			// AML.error019=本案相關掃描對象編號與掃描批號尚未送防制洗錢及打擊資恐系統系統掃描，請先執行【傳送名單掃描】按鈕後再試。
			result.set("resultMsg", pop.getProperty("AML.error019"));
			return result;
		}

		AmlStrategy as = (instead ? amlRelateService.getAmlStrategy(queryBrId)
				: amlRelateService.getAmlStrategy(""));
		String uniqueKey = Util.trim(l120s09b.getUniqueKey());
		boolean isSend = as.isSent(uniqueKey);

		if (!isSend) {
			// AML.error019=本案相關掃描對象編號與掃描批號尚未送防制洗錢及打擊資恐系統系統掃描，請先執行【傳送名單掃描】按鈕後再試。
			result.set("resultMsg", pop.getProperty("AML.error019"));
			return result;
		}

		ElAml elaml = as.checkResult(uniqueKey);

		if (elaml != null) {
			String ERRORCODE = Util.trim(elaml.getErrorCode());
			String ERRPRMSG = Util.trim(elaml.getErrprMsg());
			if (Util.notEquals(ERRORCODE, "") && Util.notEquals(ERRORCODE, "0")) {
				result.set("resultMsg", "ERROR!! " + ERRORCODE + "：" + ERRPRMSG);
				return result;
			}
		} else {
			// AML.error019=本案相關掃描對象編號與掃描批號尚未送防制洗錢及打擊資恐系統系統掃描，請先執行【傳送名單掃描】按鈕後再試。
			result.set("resultMsg", pop.getProperty("AML.error019"));
			return result;
		}

		String caseNcResult = Util.trim(elaml.getNcresult());

		if (Util.equals(caseNcResult, "") && isSend) {
			caseNcResult = UtilConstants.SasNcResult.掃描中;
		}

		l120s09b.setNcResult(caseNcResult);

		String ncCaseId = Util.trim(elaml.getNcCaseId());
		l120s09b.setNcCaseId(ncCaseId);

		lmsService.save(l120s09b);

		String mainCustId = "";
		String mainDupNo = "";
		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);
		if (l120m01a != null) {
			// 簽報書
			mainCustId = l120m01a.getCustId();
			mainDupNo = l120m01a.getDupNo();
		} else {
			// 動審表
			L160M01A l160m01a = amlRelateService.findModelByMainId(
					L160M01A.class, mainId);
			if (l160m01a != null) {
				// 動審表
				mainCustId = l160m01a.getCustId();
				mainDupNo = l160m01a.getDupNo();
			}
		}

		// 回前端值
		result.set("blackListQDate", CapDate.formatDate(
				l120s09b.getQueryDateS(), UtilConstants.DateFormat.YYYY_MM_DD));
		result.set("ncResult", l120s09b.getNcResult());
		result.set("refNo", l120s09b.getRefNo());
		result.set("uniqueKey", l120s09b.getUniqueKey());
		result.set("ncCaseId", l120s09b.getNcCaseId());
		result.set("sCaseBrId", (instead ? queryBrId : user.getUnitNo()));
		if (Util.isNotEmpty(Util.nullToSpace(l120s09b.getQueryUser()))) {
			result.set("sQueryUser", l120s09b.getQueryUser() + " - "
					+ lmsService.getUserName(l120s09b.getQueryUser()));
		} else {
			result.set("sQueryUser",
					user.getUserId() + " - " + user.getUserName());
		}

		if (Util.equals(caseNcResult, "")) {
			// 等下再執行
			// AML.error013=黑名單尚未開始掃描，稍後再執行本作業。
			result.set("resultMsg", pop.getProperty("AML.error013"));
			return result;
		} else if (Util.equals(caseNcResult,
				UtilConstants.SasNcResult.SAS錯誤LEVEL_1)) {
			// AML.error014=黑名單掃描發生錯誤請通知資訊處，錯誤代碼:SAS錯誤LEVEL_1。
			result.set("resultMsg", pop.getProperty("AML.error014"));
			return result;
		} else if (Util.equals(caseNcResult,
				UtilConstants.SasNcResult.SAS錯誤LEVEL_2)) {
			// AML.error015=黑名單掃描發生錯誤請通知資訊處，錯誤代碼:SAS錯誤LEVEL_2。
			result.set("resultMsg", pop.getProperty("AML.error015"));
			return result;
		}

		Map<String, String> queryMap = new HashMap<String, String>();
		queryMap.put("caseFinish", "");

		Map<String, String> statusMap = amlRelateService
				.getCaseReportAmlStatus(caseNcResult, queryMap);

		String caseFinish = MapUtils.getString(statusMap, "caseFinish", "N");

		// static final String 未命中疑似名單 = "000";
		// static final String 命中疑似名單 = "001";
		// static final String 經調查覆核後可交易 = "002";
		// static final String 經調查覆核後不可交易 = "003";
		// static final String 台伊清算NonBlock = "007";
		// static final String 未掃描 = "008"; XXXX
		// static final String 經調查覆核後取消交易 = "009";
		// static final String 案件調查中 = "012";
		// static final String 經調查確認命中 = "101";
		// static final String 經調查確認誤中 = "102";
		// static final String SAS錯誤LEVEL_1 = "A81"; XXXX
		// static final String SAS錯誤LEVEL_2 = "A82"; XXXX

		// 更新L120S09A
		List<L120S09A> l120s09as = amlRelateService
				.findL120s09aByMainIdWithOrder1(mainId);
		List<ElAmlItem> elAmlItems = elaml.getElAmlItems();
		if (elAmlItems == null || elAmlItems.isEmpty()) {
			// AML.error016=黑名單掃描系統未回傳明細，請通知資訊處。
			result.set("resultMsg", pop.getProperty("AML.error016"));
			return result;
		}

		if (l120s09as != null && !l120s09as.isEmpty()) {

			for (L120S09A l120s09a : l120s09as) {

				String tCustId = Util.trim(l120s09a.getCustId());
				String tDupNo = Util.trim(l120s09a.getDupNo());
				String tCustName = Util.toSemiCharString(String.valueOf(Util
						.trim(l120s09a.getCustName())));
				String tCustEName = Util.trim(l120s09a.getCustEName());
				String qName = Util.notEquals(tCustEName, "") ? tCustEName
						: tCustName;

				String uniKey = l120s09b.getUniqueKey();
				String checkSeq = l120s09a.getCheckSeq();

				ElAmlItem elAmlItem = amlService.getElAmlItem(uniKey, checkSeq);

				if (elAmlItem == null) {
					// AML.error017=黑名單掃描系統{0}無對應之掃描結果，請通知資訊處。
					result.set(
							"resultMsg",
							MessageFormat.format(
									pop.getProperty("AML.error017"), tCustId
											+ tDupNo + " " + qName));
					return result;
				}

				String checkResult = Util.trim(elAmlItem.getCheckResult());
				l120s09a.setCheckResult(checkResult);

				String hitList = !isOverSea ? Util.trim(elAmlItem.getHitList())
						: Util.trim(elAmlItem.getRouteRule());
				l120s09a.setHitList(hitList);

				String memo = "";
				StringBuffer memoBuff = new StringBuffer("");

				String blackListCode = ""; // 最嚴重的狀態
				int worstBlackCode = 0; // 最嚴重的狀態 (排用放最嚴重狀態用)
				int tWorstBlackCode = 0; // 最嚴重的狀態 (排序暫存用)

				// 是黑名單
				// >
				// 可能是黑名單
				// >
				// 未列於黑名單

				// 注意!!
				// TW checkResultArr 與 hitListArr 內容會有多筆，用分號隔開
				// HK checkResultArr 與 hitListArr 內容只會有一筆，內容為最嚴重者
				if (isOverSea) {
					// 海外分行CALL 資通SWALLOW
					// checkResult = "001";
					// RouteRule =
					// "CL2-09-0-03;SL0-02-0-01;SL0-02-0-07;SL0-03-0-02";
					queryMap = new HashMap<String, String>();
					queryMap.put("hasHitFlag", "");
					queryMap.put("blackListCode", "");
					Map<String, String> checkResultMap = amlRelateService
							.getCaseReportAmlStatus(checkResult, queryMap);

					blackListCode = MapUtils.getString(checkResultMap,
							"blackListCode", "");

					memo = Util.trim(hitList);
				} else if (Util.equals(callSasTW, "Y")) {
					// 台灣分行直接CALL SAS
					// checkResult = "001";
					// RouteRule =
					// "CL2-09-0-03;SL0-02-0-01;SL0-02-0-07;SL0-03-0-02";
					queryMap = new HashMap<String, String>();
					queryMap.put("hasHitFlag", "");
					queryMap.put("blackListCode", "");
					Map<String, String> checkResultMap = amlRelateService
							.getCaseReportAmlStatus(checkResult, queryMap);

					blackListCode = MapUtils.getString(checkResultMap,
							"blackListCode", "");

					memo = Util.trim(hitList);
				} else {
					// 台灣分行CALL國內主機
					// checkResult = "000;101;102;001";
					// hitList = "AAAAA;BBBBB;CCCCC;DDDDD";
					String[] checkResultArr = Util.trim(checkResult).split(";");
					String[] hitListArr = Util.trim(hitList).split(";");
					// 真的有中的名單才加到MEMO
					for (int i = 0; i < checkResultArr.length; i++) {

						// 判斷案件狀態是否為初次掃描或是調查完後的狀態
						if (Util.equals(caseFinish, "Y")) {
							// 調查完後的狀態=>初次掃描都沒中或是有調查後的結果
							// 只抓沒中、調查後有中或調查後誤中
							if (Util.notEquals(checkResultArr[i],
									UtilConstants.SasNcResult.經調查確認命中)
									&& Util.notEquals(checkResultArr[i],
											UtilConstants.SasNcResult.經調查確認誤中)
									&& Util.notEquals(checkResultArr[i],
											UtilConstants.SasNcResult.未命中疑似名單)) {

								// 調查完後在BTT沒有勾的，會回傳001，這種的不要抓，只抓確定的調查結果

								// checkResultArr 裡面的資料PASS掉不抓

								// 因為
								// 如果案件為調查後，則只接收 經調查確認命中 或 經調查確認誤中
								// 張慶霆的程式與SAS不同，調查後的結果，如果BTT上有些命中名單有勾，有些沒有如下:
								// CHECKRESULT=101;001; 101為有勾的TRUE
								// HIT，但沒勾的會是001(疑似命中....怪怪的)
								// HITLIST=11;12;
								// 所以調查後的結果，針對國內BTT只抓 000、101、102

							} else {
								queryMap = new HashMap<String, String>();
								queryMap.put("hasHitFlag", "");
								queryMap.put("blackListCode", "");

								Map<String, String> checkResultMap = amlRelateService
										.getCaseReportAmlStatus(
												checkResultArr[i], queryMap);

								String tBlackListCode = "";
								tBlackListCode = MapUtils.getString(
										checkResultMap, "blackListCode", "");

								if (Util.equals(
										tBlackListCode,
										UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單)) {
									tWorstBlackCode = 2;
								} else if (Util
										.equals(tBlackListCode,
												UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單)) {
									tWorstBlackCode = 1;
								} else if (Util
										.equals(tBlackListCode,
												UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單)) {
									tWorstBlackCode = 0;
								}

								// 抓最嚴重的
								if (tWorstBlackCode >= worstBlackCode) {
									worstBlackCode = tWorstBlackCode;
									blackListCode = tBlackListCode;
								}

								String hasHitFlag = MapUtils.getString(
										checkResultMap, "hasHitFlag", "N");

								if (Util.equals(hasHitFlag, "Y")) {
									if (tWorstBlackCode == 2) {
										// 真正是命中黑名單才放進來
										if (Util.equals(memoBuff.toString(), "")) {
											memoBuff.append(hitListArr[i]);
										} else {
											memoBuff.append(";").append(
													hitListArr[i]);
										}
									}

								}
							}
						} else {
							// 第一次掃描
							queryMap = new HashMap<String, String>();
							queryMap.put("hasHitFlag", "");
							queryMap.put("blackListCode", "");

							Map<String, String> checkResultMap = amlRelateService
									.getCaseReportAmlStatus(checkResultArr[i],
											queryMap);
							blackListCode = MapUtils.getString(checkResultMap,
									"blackListCode", "");
							String hasHitFlag = MapUtils.getString(
									checkResultMap, "hasHitFlag", "N");

							if (Util.equals(hasHitFlag, "Y")) {

								if (Util.equals(memoBuff.toString(), "")) {
									memoBuff.append(hitListArr[i]);
								} else {
									memoBuff.append(";").append(hitListArr[i]);
								}

							}
						}

					}

					memo = Util.trim(memoBuff.toString());
				}

				int SeqNum = 0;
				if (Util.equals(blackListCode,
						UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單)) {
					SeqNum = 11;
				} else if (Util.equals(blackListCode,
						UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單)) {
					SeqNum = 21;
				} else if (Util.equals(blackListCode,
						UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單)) {
					SeqNum = 31;

					// 經調查後，若為誤中，SAS 會回傳 102，但是名單還是會傳
					// CL2-09-0-03;SL0-02-0-01;SL0-02-0-07;SL0-03-0-02
					// 這樣畫面看起來會有點怪，所以沒中的時後把MEMO清掉
					memo = "";

				} else {
					SeqNum = 0;
				}

				if (Util.notEquals(blackListCode, "")) {
					if (Util.equals(tCustId, mainCustId)
							&& Util.equals(tDupNo, mainDupNo)) {
						SeqNum = SeqNum - 1;
					}
				}

				// J-106-0029-003 Web e-Loan授信簽報書借款人基本資料與動審表黑名單查詢調整使用共用模組
				// 國內分行如果實質受益人查不到需加查PEPS是否為國外政治敏感人物
				if (!isOverSea) {

					// J-107-0070-001 Web e-Loan
					// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
					// J-108-0039_05097_B1001 Web e-Loan
					// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
					if ((SeqNum == 31 || SeqNum == 21)
							&& (l120s09a
									.getCustRelation()
									.indexOf(
											UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人) > -1
									|| l120s09a
											.getCustRelation()
											.indexOf(
													UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員) > -1 || l120s09a
									.getCustRelation()
									.indexOf(
											UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人) > -1)) {
						String otherPEPS = "";

						// J-107-0070-001 Web e-Loan
						// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
						List<L120S01P> l120s01ps = amlRelateService
								.findL120s01pByMainIdWithoutRType(mainId);

						if (l120s01ps != null && !l120s01ps.isEmpty()) {
							for (L120S01P l120s01p : l120s01ps) {

								// J-107-0070-001 Web e-Loan
								// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
								// J-108-0039_05097_B1001 Web e-Loan
								// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
								if (Util.equals(
										Util.trim(l120s01p.getRType()),
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)
										|| Util.equals(
												Util.trim(l120s01p.getRType()),
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員)
										|| Util.equals(
												Util.trim(l120s01p.getRType()),
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人)) {

									String s01pId = Util
											.trim(l120s01p.getRId());
									String s01pNo = Util.trim(l120s01p
											.getRDupNo());
									String s01pName = Util.trim(l120s01p
											.getRName());
									String s01pEName = Util.trim(l120s01p
											.getREName());
									String s01pPEPS = Util.trim(l120s01p
											.getPeps());

									if (Util.notEquals(tCustId, "")
											&& Util.notEquals(tDupNo, "")) {
										if (Util.notEquals(s01pId, "")
												&& Util.notEquals(s01pNo, "")) {
											if (Util.equals(s01pId, tCustId)
													&& Util.equals(s01pNo,
															tDupNo)) {
												if (Util.equals(s01pPEPS, "Y")) {
													otherPEPS = "Y";
													break;
												}
											}
										}
									}

									if (Util.notEquals(tCustName, "")) {
										if (Util.notEquals(s01pName, "")) {
											if (Util.equals(s01pName, tCustName)) {
												if (Util.equals(s01pPEPS, "Y")) {
													otherPEPS = "Y";
													break;
												}
											}
										}
									}

									if (Util.notEquals(tCustEName, "")) {
										if (Util.notEquals(s01pEName, "")) {
											if (Util.equals(s01pEName,
													tCustEName)) {
												if (Util.equals(s01pPEPS, "Y")) {
													otherPEPS = "Y";
													break;
												}
											}
										}
									}
								}
							}
						}

						if (Util.equals(otherPEPS, "Y")) {
							blackListCode = UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單;
							memo = "PEPS*";
							SeqNum = 11;
						}

					}
				}

				// J-106-0238-001
				// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
				if (Util.notEquals(tCustId, "") && Util.notEquals(tDupNo, "")) {
					String CM1_AML_STATUS = amlRelateService
							.getCustIn0024AmlStatus(tCustId, tDupNo);
					l120s09a.setCm1AmlStatus(CM1_AML_STATUS);

					// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
					if (LMSUtil
							.isContainValue(
									Util.trim(l120s09a.getCustRelation()),
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶,
									",")) {
						String luvRiskLevel = amlRelateService
								.getCustLuvRiskLevel(unitNo, tCustId, tDupNo);
						l120s09a.setLuvRiskLevel(Util.trim(luvRiskLevel));
					} else {
						l120s09a.setLuvRiskLevel("");
					}
				}

				l120s09a.setBlackListCode(blackListCode);
				l120s09a.setMemo(memo);
				l120s09a.setQueryDateS(l120s09b.getQueryDateS());
				l120s09a.setSeqNum(SeqNum);
				lmsService.save(l120s09a);
			}

		}

		// P-108-0046_05097_B1001 Web e-Loan配合Oracle系統建置，修改AML相關功能。
		String queryOracle = amlRelateService.isOracle(unitNo) ? "<br><br><font color='blue'>※配合Oracle階段上線作業，各筆明細命中與調查後之結果，請至行員網站「Oracle 名單掃描系統 CS/TF」查詢，本階段e-Loan僅能引進案件調查結果。</font>"
				: "";

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
				RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功) + queryOracle);

		return result;

	}

	/**
	 * J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 取得黑名單查詢結果
	 * 
	 * @param mainId
	 * @param mainCustId
	 * @param mainDupNo
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult initForShow(PageParameters params)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(unitNo).getBrNoFlag());

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		String callNewFunc = "N";
		String callSasTW = "N"; // 國內專用，用來切換呼叫主機還是直接CALL SAS

		Map<String, String> chkMap = amlRelateService.checkAmlNewFuncMode("");

		callNewFunc = MapUtils.getString(chkMap, "callNewFunc", "N");
		callSasTW = MapUtils.getString(chkMap, "callSasTW", "N");

		result.set("callNewFunc", callNewFunc);
		result.set("show0024AmlStatus", "N");
		result.set("showQueryInfo", "N");
		// J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位
		result.set("showNcResultRemark", "N");
		result.set("showHighRiskRemark", "N");

		// J-112-0534 引進聯徵T70查詢結果
		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);
		if (l120m01a != null) {
			// 海外不需要
			if (Util.equals("5", l120m01a.getTypCd())) {
				result.set("showImportT70", "N");
			} else {
				result.set("showImportT70", "Y");
			}
		} else {
			// 非簽報不需要
			result.set("showImportT70", "N");
		}

		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");

		if (Util.equals(callNewFunc, "Y")) {
			// 新按鈕要顯示前端畫面 class 為 canSendSas 還是 notSendSas
			L120S09B l120s09b = amlRelateService.findL120s09bByMainId(mainId);
			if (l120s09b != null) {

				String ncResult = Util.trim(l120s09b.getNcResult());

				Map<String, String> queryMap = new HashMap<String, String>();
				queryMap.put("lockEdit", "");

				Map<String, String> statusMap = amlRelateService
						.getCaseReportAmlStatus(ncResult, queryMap);

				String lockEdit = MapUtils
						.getString(statusMap, "lockEdit", "N");
				result.set("lockEdit", lockEdit);

				result.set("blackListQDate", CapDate.formatDate(
						l120s09b.getQueryDateS(),
						UtilConstants.DateFormat.YYYY_MM_DD));

				result.set("ncResult", l120s09b.getNcResult());
				result.set("refNo", l120s09b.getRefNo());
				result.set("uniqueKey", l120s09b.getUniqueKey());
				result.set("ncCaseId", l120s09b.getNcCaseId());
				if (instead) {
					result.set("showQueryInfo", "Y");
				}

				// P-108-0046_05097_B1001 Web e-Loan配合Oracle系統建置，修改AML相關功能。
				String sCaseBrId = (instead ? queryBrId : user.getUnitNo());
				result.set("sCaseBrId", sCaseBrId);
				if (Util.isNotEmpty(Util.nullToSpace(l120s09b.getQueryUser()))) {
					result.set("sQueryUser", l120s09b.getQueryUser() + " - "
							+ lmsService.getUserName(l120s09b.getQueryUser()));
				} else {
					result.set("sQueryUser",
							user.getUserId() + " - " + user.getUserName());
				}

				// J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位
				// 要有很大段的判斷!!callNewFunc Y了才有機會往後判斷
				result.set("showNcResultRemark", amlRelateService
						.isNcResultRemarkShow(mainId, false) ? "Y" : "N");
				result.set("showHighRiskRemark", amlRelateService
						.isHighRiskRemarkShow(mainId, false) ? "Y" : "N");
				result.set("ncResultRemark", l120s09b.getNcResultRemark());
				result.set("highRiskRemark", l120s09b.getHighRiskRemark());

			} else {
				// 舊案就不檔了
				result.set("lockEdit", "N");
			}

			// 判斷簽報書AML/CFT頁籤是否要顯是0024 AML STATUS 與提示訊息
			String show0024AmlStatus = amlRelateService
					.getIsShow0024AmlStatus((instead ? queryBrId : user
							.getUnitNo()));

			result.set("show0024AmlStatus", show0024AmlStatus);

			if (instead && Util.equals("N", l120s09b.getWayMode())) { // 是代發且代發分行為舊模式
				L120S09A l120s09a = amlRelateService
						.findL120s09aMaxQDateByMainId(mainId);
				if (l120s09a != null) {
					Date blackListQDate = l120s09a.getQueryDateS();
					if (blackListQDate != null) {
						result.set("blackListQDate", CapDate.formatDate(
								blackListQDate,
								UtilConstants.DateFormat.YYYY_MM_DD));

					}
				}
			}

		} else {
			// 舊模式就不檔了
			result.set("lockEdit", "N");
		}

		return result;

	}

	/**
	 * 取得0024 AML 註記 J-106-0238-001
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 取得黑名單查詢結果
	 * 
	 * @param mainId
	 * @param mainCustId
	 * @param mainDupNo
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult applyCm1AmlStatus(PageParameters params)
			throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(unitNo).getBrNoFlag());

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("resultMsg", "");
		// String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		String mainCustId = "";
		String mainDupNo = "";
		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);
		if (l120m01a != null) {
			// 簽報書
			mainCustId = l120m01a.getCustId();
			mainDupNo = l120m01a.getDupNo();
		} else {
			// 動審表
			L160M01A l160m01a = amlRelateService.findModelByMainId(
					L160M01A.class, mainId);
			if (l160m01a != null) {
				// 動審表
				mainCustId = l160m01a.getCustId();
				mainDupNo = l160m01a.getDupNo();
			}
		}

		// 更新L120S09A
		List<L120S09A> l120s09as = amlRelateService
				.findL120s09aByMainIdWithOrder1(mainId);

		if (l120s09as != null && !l120s09as.isEmpty()) {

			for (L120S09A l120s09a : l120s09as) {

				String tCustId = Util.trim(l120s09a.getCustId());
				String tDupNo = Util.trim(l120s09a.getDupNo());

				// J-106-0238-001
				// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
				if (Util.notEquals(tCustId, "") && Util.notEquals(tDupNo, "")) {
					String CM1_AML_STATUS = amlRelateService
							.getCustIn0024AmlStatus(tCustId, tDupNo);
					l120s09a.setCm1AmlStatus(CM1_AML_STATUS);
					lmsService.save(l120s09a);
				}

			}

		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;

	}

	// J-106-0238-001
	// **************************************************************************************************************************************************************************************
	/**
	 * J-106-0238-003
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 傳送名單掃描
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult askAmlList(PageParameters params)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		// 開始傳送名單到SAS**********************************************************************************
		L120S09B l120s09b = amlRelateService.findL120s09bByMainId(mainId);

		if (l120s09b == null) {
			// AML.error019=本案相關掃描對象編號與掃描批號尚未送防制洗錢及打擊資恐系統系統掃描，請先執行【傳送名單掃描】按鈕後再試。
			throw new CapMessageException(pop.getProperty("AML.error019"),
					getClass());
		}

		ElAml elaml = this.generateElAml(l120s09b);
		String uniqueKey = Util.trim(l120s09b.getUniqueKey());
		if (Util.notEquals(uniqueKey, "")) {
			AmlStrategy as = (instead ? amlRelateService
					.getAmlStrategy(queryBrId) : amlRelateService
					.getAmlStrategy(""));
			as.askResult(uniqueKey);
		} else {
			// AML.error019=本案相關掃描對象編號與掃描批號尚未送防制洗錢及打擊資恐系統系統掃描，請先執行【傳送名單掃描】按鈕後再試。
			throw new CapMessageException(pop.getProperty("AML.error019"),
					getClass());
		}

		result.set("blackListQDate", CapDate.formatDate(
				l120s09b.getQueryDateS(), UtilConstants.DateFormat.YYYY_MM_DD));
		result.set("ncResult", l120s09b.getNcResult());
		result.set("refNo", l120s09b.getRefNo());
		result.set("uniqueKey", l120s09b.getUniqueKey());
		result.set("ncCaseId", l120s09b.getNcCaseId());
		result.set("sCaseBrId", (instead ? queryBrId : user.getUnitNo()));
		if (Util.isNotEmpty(Util.nullToSpace(l120s09b.getQueryUser()))) {
			result.set("sQueryUser", l120s09b.getQueryUser() + " - "
					+ lmsService.getUserName(l120s09b.getQueryUser()));
		} else {
			result.set("sQueryUser",
					user.getUserId() + " - " + user.getUserName());
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	public ElAml generateElAml(L120S09B l120s09b) {
		ElAml elaml = new ElAml();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String unitNo = user.getUnitNo();
		if (l120s09b != null
				&& Util.isNotEmpty(Util.nullToSpace(l120s09b.getQueryBrId()))) {
			unitNo = l120s09b.getQueryBrId();
		}
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(unitNo).getBrNoFlag());

		// 設定AML HEADER

		String callingSys = ""; // 授信 EL
		String tranId = "LMS"; // 國內才有
		String screenPro = "02"; // Customer Event
		String resendFg = "N"; // 國內才有
		String callUser = Util.isNotEmpty(Util.nullToSpace(l120s09b
				.getQueryUser())) ? l120s09b.getQueryUser() : user.getUserId();

		// 部門別
		String busUnitId = Util.isNotEmpty(Util.nullToSpace(l120s09b
				.getQueryBrId())) ? l120s09b.getQueryBrId() : unitNo;

		// if(Util.notEquals(busUnitId, "")){
		// String countryType = Util.trim(branchSrv.getBranch(busUnitId)
		// .getCountryType());
		//
		// if (Util.equals(countryType, "US")) {
		// //美國PRIME 部門別要塞LOAN，PRIME上面有依部門別區分權限
		// busUnitId = "LOAN";
		// }
		// }

		String branchNo = Util.isNotEmpty(Util.nullToSpace(l120s09b
				.getQueryBrId())) ? l120s09b.getQueryBrId() : unitNo;
		String genAlertFg = "Y";
		Date tranDate = l120s09b.getQueryDateS();
		String uniKey = l120s09b.getUniqueKey();
		String refNo = l120s09b.getRefNo();
		String partyNo = ""; // 國內才有
		String ncrefId = "";
		String errorCode = ""; // checkResult 時才有
		String errprMsg = ""; // checkResult 時才有
		String ncresult = "";
		String routerule = "";
		String hitListType = "";
		String hitseq = "";
		String createUser = user.getUserId(); // FOR 徵信用，徵信會有 908 幫 020分行徵信，所以
												// createUuser為908經辦，CallUser
												// 是020的人
		if (isOverSea) {
			callingSys = "08"; // 授信 EL
		} else {
			callingSys = "06"; // 授信 EL
		}

		// TODO FOR TEST
		// callUser = "007716";
		// busUnitId = "0C3";
		// branchNo = "0C3";

		// TODO FOR TEST
		// callUser = "943BBB";
		// busUnitId = "943";
		// branchNo = "943";

		// TODO FOR TEST
		// callUser = "0229IL";
		// busUnitId = "229";
		// branchNo = "229";

		elaml.setCallingSys(callingSys);
		elaml.setTranId(tranId);
		elaml.setScreenPro(screenPro);
		elaml.setResendFg(resendFg);
		elaml.setCallUser(callUser);
		elaml.setBusUnitId(busUnitId);
		elaml.setBranchNo(branchNo);
		elaml.setGenAlertFg(genAlertFg);
		elaml.setTranDate(tranDate);
		elaml.setUniKey(uniKey);
		elaml.setRefNo(refNo);
		elaml.setPartyNo(partyNo);
		elaml.setNcrefId(ncrefId);
		elaml.setErrorCode(errorCode);
		elaml.setErrprMsg(errprMsg);
		elaml.setNcresult(ncresult);
		elaml.setRouterule(routerule);
		elaml.setHitListType(hitListType);
		elaml.setHitseq(hitseq);
		elaml.setCreateUser(createUser);

		return elaml;

	}

	/**
	 * J-106-0238-003
	 * 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單
	 * /黑國家/政治敏感人物交易具體檢核機制」 傳送名單掃描
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult clearNcResult(PageParameters params)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		L120S09B l120s09b = amlRelateService.findL120s09bByMainId(mainId);

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);

		if (l120s09b == null) {
			// AML.error019=本案相關掃描對象編號與掃描批號尚未送防制洗錢及打擊資恐系統系統掃描，請先執行【傳送名單掃描】按鈕後再試。
			throw new CapMessageException(pop.getProperty("AML.error019"),
					getClass());
		}

		l120s09b.setNcResult("");
		lmsService.save(l120s09b);

		result.set("blackListQDate", CapDate.formatDate(
				l120s09b.getQueryDateS(), UtilConstants.DateFormat.YYYY_MM_DD));
		result.set("ncResult", l120s09b.getNcResult());
		result.set("refNo", l120s09b.getRefNo());
		result.set("uniqueKey", l120s09b.getUniqueKey());
		result.set("ncCaseId", l120s09b.getNcCaseId());
		result.set("sCaseBrId", (instead ? queryBrId : user.getUnitNo()));
		if (Util.isNotEmpty(Util.nullToSpace(l120s09b.getQueryUser()))) {
			result.set("sQueryUser", l120s09b.getQueryUser() + " - "
					+ lmsService.getUserName(l120s09b.getQueryUser()));
		} else {
			result.set("sQueryUser",
					user.getUserId() + " - " + user.getUserName());
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	/**
	 * J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
	 * 
	 * @param mainId
	 * @param mainCustId
	 * @param mainDupNo
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult applyLuvRiskLevel(PageParameters params)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("resultMsg", "");
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		unitNo = (instead ? queryBrId : user.getUnitNo());

		String mainCustId = "";
		String mainDupNo = "";
		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);
		if (l120m01a != null) {
			// 簽報書
			mainCustId = l120m01a.getCustId();
			mainDupNo = l120m01a.getDupNo();
		} else {
			// 動審表
			L160M01A l160m01a = amlRelateService.findModelByMainId(
					L160M01A.class, mainId);
			if (l160m01a != null) {
				// 動審表
				mainCustId = l160m01a.getCustId();
				mainDupNo = l160m01a.getDupNo();
			}
		}

		// 更新L120S09A
		List<L120S09A> l120s09as = amlRelateService
				.findL120s09aByMainIdWithOrder1(mainId);

		if (l120s09as != null && !l120s09as.isEmpty()) {

			for (L120S09A l120s09a : l120s09as) {

				String tCustId = Util.trim(l120s09a.getCustId());
				String tDupNo = Util.trim(l120s09a.getDupNo());

				// J-106-0238-001
				// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
				if (Util.notEquals(tCustId, "") && Util.notEquals(tDupNo, "")) {
					// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
					if (LMSUtil
							.isContainValue(
									Util.trim(l120s09a.getCustRelation()),
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶,
									",")) {
						String luvRiskLevel = amlRelateService
								.getCustLuvRiskLevel(unitNo, tCustId, tDupNo);
						l120s09a.setLuvRiskLevel(Util.trim(luvRiskLevel));
					} else {
						l120s09a.setLuvRiskLevel("");
					}
					lmsService.save(l120s09a);
				}

			}

		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;

	}

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult applySeniorMgrData(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		StringBuffer errMsg = new StringBuffer("");
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String mainId = Util.trim(params.getString("mainId"));

		List<L120S01P> l120s01ps = amlRelateService
				.findL120s01pByMainIdAndCustIdWithRType(mainId, custId, dupNo,
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員);

		if (l120s01ps != null && !l120s01ps.isEmpty()) {
			for (L120S01P l120s01p : l120s01ps) {
				lmsService.delete(l120s01p);
			}
		}

		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());

		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(unitNo).getBrNoFlag());

		// 0024-23 高階管理員 獨資要求建兩個高階管理人員，但分行取巧把同一個高階管理人員建兩次，會造成L120S01P KEY DUP
		Map<String, String> seniorMgrMap = new HashMap<String, String>();

		if (!isOverSea) {
			// 國內
			List<Map<String, Object>> cmfDataList = misDbService
					.findCMFLUNSRByCustId(custId, dupNo);

			if (cmfDataList == null || cmfDataList.isEmpty()) {
				// L120S09a.message18={0}無借款人基本資料可以引進
				errMsg.append(MessageFormat.format(
						pop.getProperty("L120S09a.message18"), "0024"));
				result.set("errMsg", errMsg.toString());
				return result;
			}

			int count = 0;
			for (Map<String, Object> cmfData : cmfDataList) {
				if (cmfData == null || cmfData.isEmpty()) {
					// L120S09a.message18={0}無借款人基本資料可以引進
					errMsg.append(MessageFormat.format(
							pop.getProperty("L120S09a.message18"), "0024"));
					result.set("errMsg", errMsg.toString());
					return result;
				} else {

					String BUSCD = Util.trim(MapUtils.getString(cmfData,
							"BUSCD"));
					String CMSR_SR_PV_ID = Util.trim(MapUtils.getString(
							cmfData, "CMSR_SR_PV_ID"));
					String CMSR_SR_PV_CNAME = Util.trim(MapUtils.getString(
							cmfData, "CMSR_SR_PV_CNAME"));
					String CMSR_SR_PV_ENAME = Util.trim(MapUtils.getString(
							cmfData, "CMSR_SR_PV_ENAME"));
					String CMSR_SR_PV_BD = Util.trim(MapUtils.getString(
							cmfData, "CMSR_SR_PV_BD"));
					String CMSR_SR_PV_NC = Util.trim(MapUtils.getString(
							cmfData, "CMSR_SR_PV_NC"));

					// BTT 0024-23 高階管理人員畫面也有PEP
					String CMSR_SR_PEP = Util.trim(MapUtils.getString(cmfData,
							"CMSR_SR_PEP"));

					String rName = CMSR_SR_PV_CNAME;
					if (Util.equals(CMSR_SR_PV_CNAME, "")
							&& Util.notEquals(CMSR_SR_PV_ENAME, "")) {
						rName = CMSR_SR_PV_ENAME;
					}

					if (Util.notEquals(BUSCD, "")
							&& !LMSUtil.isBusCode_060000_130300(BUSCD)) {
						// 企業戶

						if (Util.notEquals(CMSR_SR_PV_ID, "")
								|| Util.notEquals(CMSR_SR_PV_CNAME, "")
								|| Util.notEquals(CMSR_SR_PV_ENAME, "")) {

							// 判斷高階管理人員ID + RNAME有沒有重覆
							if (seniorMgrMap.containsKey(CMSR_SR_PV_ID + "-"
									+ rName)) {
								continue;
							} else {

								seniorMgrMap.put(CMSR_SR_PV_ID + "-" + rName,
										rName);

								L120S01P l120s01p = new L120S01P();
								count = count + 1;
								l120s01p = new L120S01P();
								l120s01p.setMainId(mainId);
								l120s01p.setCreateTime(CapDate
										.getCurrentTimestamp());
								l120s01p.setCreator(user.getUserId());
								l120s01p.setCustId(custId);
								l120s01p.setDupNo(dupNo);
								l120s01p.setType("1");
								l120s01p.setRId(CMSR_SR_PV_ID);
								l120s01p.setRDupNo("0");

								l120s01p.setRName(rName);

								l120s01p.setREName(CMSR_SR_PV_ENAME);
								if (Util.equals(CMSR_SR_PV_ENAME, "")
										&& Util.notEquals(CMSR_SR_PV_CNAME, "")) {
									if (CMSR_SR_PV_CNAME.length() == CMSR_SR_PV_CNAME
											.getBytes().length) {
										l120s01p.setREName(CMSR_SR_PV_CNAME);
									}
								}

								l120s01p.setRType(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員);

								L120S01P l120s01pMax = amlRelateService
										.findL120s01pMaxSeqNumByMainIdAndCustIdWithRType(
												mainId,
												custId,
												dupNo,
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員);
								if (l120s01pMax == null) {
									l120s01p.setSeqNum(1);
								} else {
									l120s01p.setSeqNum(l120s01pMax.getSeqNum() == null ? 1
											: l120s01pMax.getSeqNum() + 1);
								}

								l120s01p.setBirthDate(CapDate
										.parseDate(CMSR_SR_PV_BD));
								l120s01p.setNation(CMSR_SR_PV_NC);
								l120s01p.setPeps(CMSR_SR_PEP);

								lmsService.save(l120s01p);
							}
						}

					} else {
						// 個人戶無高階管理人員
						// LL120S09a.message19={0}無高階管理人員資料可以引進
						errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
						errMsg.append(MessageFormat.format(
								pop.getProperty("L120S09a.message19"),
								"0024-23"));
						result.set("errMsg", errMsg.toString());
						return result;
					}

				}
			}

			if (count == 0) {
				// 個人戶無高階管理人員
				// LL120S09a.message19={0}無高階管理人員資料可以引進
				errMsg.append(MessageFormat.format(
						pop.getProperty("L120S09a.message19"), "0024-23"));
				result.set("errMsg", errMsg.toString());
				return result;
			}

		} else {

			// 海外，J-107-0070-001 本案只針對國內引進
			// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。

			List<Map<String, Object>> cmfDataList = misDbService
					.findCMFLUNSRByCustId(custId, dupNo);

			if (cmfDataList == null || cmfDataList.isEmpty()) {
				// L120S09a.message18={0}無借款人基本資料可以引進
				errMsg.append(MessageFormat.format(
						pop.getProperty("L120S09a.message18"), "0024"));
				result.set("errMsg", errMsg.toString());
				return result;
			}

			int count = 0;
			for (Map<String, Object> cmfData : cmfDataList) {
				if (cmfData == null || cmfData.isEmpty()) {
					// L120S09a.message18={0}無高階管理人員資料可以引進
					errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
					errMsg.append(MessageFormat.format(
							pop.getProperty("L120S09a.message18"), "0024"));
					result.set("errMsg", errMsg.toString());
					return result;
				} else {

					String BUSCD = Util.trim(MapUtils.getString(cmfData,
							"BUSCD"));
					String CMSR_SR_PV_ID = Util.trim(MapUtils.getString(
							cmfData, "CMSR_SR_PV_ID"));
					String CMSR_SR_PV_CNAME = Util.trim(MapUtils.getString(
							cmfData, "CMSR_SR_PV_CNAME"));
					String CMSR_SR_PV_ENAME = Util.trim(MapUtils.getString(
							cmfData, "CMSR_SR_PV_ENAME"));
					String CMSR_SR_PV_BD = Util.trim(MapUtils.getString(
							cmfData, "CMSR_SR_PV_BD"));
					String CMSR_SR_PV_NC = Util.trim(MapUtils.getString(
							cmfData, "CMSR_SR_PV_NC"));
					String rName = CMSR_SR_PV_CNAME;
					if (Util.equals(CMSR_SR_PV_CNAME, "")
							&& Util.notEquals(CMSR_SR_PV_ENAME, "")) {
						rName = CMSR_SR_PV_ENAME;
					}

					if (Util.notEquals(BUSCD, "")
							&& !LMSUtil.isBusCode_060000_130300(BUSCD)) {
						// 企業戶

						if (Util.notEquals(CMSR_SR_PV_ID, "")
								|| Util.notEquals(CMSR_SR_PV_CNAME, "")
								|| Util.notEquals(CMSR_SR_PV_ENAME, "")) {

							// 判斷高階管理人員ID + RNAME有沒有重覆
							if (seniorMgrMap.containsKey(CMSR_SR_PV_ID + "-"
									+ rName)) {
								continue;
							} else {

								seniorMgrMap.put(CMSR_SR_PV_ID + "-" + rName,
										rName);

								L120S01P l120s01p = new L120S01P();
								count = count + 1;
								l120s01p = new L120S01P();
								l120s01p.setMainId(mainId);
								l120s01p.setCreateTime(CapDate
										.getCurrentTimestamp());
								l120s01p.setCreator(user.getUserId());
								l120s01p.setCustId(custId);
								l120s01p.setDupNo(dupNo);
								l120s01p.setType("1");
								l120s01p.setRId(CMSR_SR_PV_ID);
								l120s01p.setRDupNo("0");

								l120s01p.setRName(rName);

								l120s01p.setREName(CMSR_SR_PV_ENAME);
								if (Util.equals(CMSR_SR_PV_ENAME, "")
										&& Util.notEquals(CMSR_SR_PV_CNAME, "")) {
									if (CMSR_SR_PV_CNAME.length() == CMSR_SR_PV_CNAME
											.getBytes().length) {
										l120s01p.setREName(CMSR_SR_PV_CNAME);
									}
								}

								l120s01p.setRType(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員);

								L120S01P l120s01pMax = amlRelateService
										.findL120s01pMaxSeqNumByMainIdAndCustIdWithRType(
												mainId,
												custId,
												dupNo,
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員);
								if (l120s01pMax == null) {
									l120s01p.setSeqNum(1);
								} else {
									l120s01p.setSeqNum(l120s01pMax.getSeqNum() == null ? 1
											: l120s01pMax.getSeqNum() + 1);
								}

								l120s01p.setBirthDate(CapDate
										.parseDate(CMSR_SR_PV_BD));
								l120s01p.setNation(CMSR_SR_PV_NC);
								l120s01p.setPeps("");

								lmsService.save(l120s01p);
							}
						}

					} else {
						// 個人戶無高階管理人員
						// LL120S09a.message19={0}無高階管理人員資料可以引進
						errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
						errMsg.append(MessageFormat.format(
								pop.getProperty("L120S09a.message19"),
								"0024-23"));
						result.set("errMsg", errMsg.toString());
						return result;
					}

				}
			}

			if (count == 0) {
				// 個人戶無高階管理人員
				// LL120S09a.message19={0}無高階管理人員資料可以引進
				errMsg.append(MessageFormat.format(
						pop.getProperty("L120S09a.message19"), "0024-23"));
				result.set("errMsg", errMsg.toString());
				return result;
			}

		}

		result.set("errMsg", errMsg.toString());
		return result;

	}

	/**
	 * <pre>
	 * 取得所有分行s
	 * </pre>
	 * 
	 * @param params
	 * @param parent
	 *            .4
	 * @return
	 * @throws CapException
	 */
	public IResult allBranch(PageParameters params) {
		CapAjaxFormResult r = new CapAjaxFormResult();
		for (IBranch b : branchService.getAllBranch()) {
			r.set(b.getBrNo(), b.getBrName());
		}
		return r;
	}

	/**
	 * <pre>
	 * 取得所有分行s
	 * </pre>
	 * 
	 * @param params
	 * @param parent
	 *            .4
	 * @return
	 * @throws CapException
	 */
	public IResult allUserByBranch(PageParameters params) {
		CapAjaxFormResult r = new CapAjaxFormResult();
		String branch = CapString.trimNull(params.getString("qryBranch"));
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管, SignEnum.經辦人員 };
		if (CapString.isEmpty(branch)) {
			branch = MegaSSOSecurityContext.getUnitNo();
		}
		r.putAll(userSrv.findByBrnoAndSignId(branch, signs));
		return r;
	}

	/**
	 * J-107-0176 儲存洗錢防制發查分行/人員
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120s09bQuery(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String caseBrId = Util.trim(params.getString("caseBrId",
				user.getUnitNo()));
		String queryUser = Util.trim(params.getString("queryUser",
				user.getUserId()));
		result.set("caseBrId", caseBrId);
		result.set("queryUser", queryUser);

		// 代查分行走新or舊
		Map<String, String> chkMap = amlRelateService
				.checkAmlNewFuncMode(caseBrId);
		String callNewFunc = MapUtils.getString(chkMap, "callNewFunc", "N");
		String wayMode = callNewFunc;
		result.set("callNewFunc", callNewFunc);

		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(caseBrId).getBrNoFlag());
		result.set("isOverSea", isOverSea);

		if (Util.notEquals(user.getUnitNo(), caseBrId)) {

		} else { // 若代查自己分行則不算代查
			caseBrId = "";
			queryUser = "";
			wayMode = "";
		}

		L120S09B l120s09b = amlRelateService.findL120s09bByMainId(mainId);
		if (l120s09b != null) {
			l120s09b.setQueryBrId(caseBrId);
			l120s09b.setQueryUser(queryUser);
			l120s09b.setWayMode(wayMode);
			lmsService.save(l120s09b);
		} else {
			L160M01A l160m01a = null;
			L120M01A l120m01a = amlRelateService.findModelByMainId(
					L120M01A.class, mainId);

			String className = "";

			if (l120m01a != null) {
				className = "L120M01A"; // 簽報書
			} else {
				l160m01a = amlRelateService.findModelByMainId(L160M01A.class,
						mainId);
				if (l160m01a != null) {
					className = "L160M01A"; // 動審表
				}
			}

			// P-108-0046_05097_B1001 Web e-Loan配合Oracle系統建置，修改AML/CFT相關功能。
			if (Util.equals(className, "L120M01A")) {
				l120s09b = amlRelateService.initL120s09b(l120m01a, caseBrId);
			} else if (Util.equals(className, "L160M01A")) {
				l120s09b = amlRelateService.initL120s09b(l160m01a, caseBrId);
			}
			l120s09b.setQueryBrId(caseBrId);
			l120s09b.setQueryUser(queryUser);
			l120s09b.setWayMode(wayMode);
			lmsService.save(l120s09b);
		}

		return result;
	}

	/**
	 * 黑名單查詢 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult insteadQueryBlackList(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String mainCustId = "";
		String mainDupNo = "";

		String caseBrId = Util.trim(params.getString("caseBrId",
				user.getUnitNo()));
		String queryUser = Util.trim(params.getString("queryUser",
				user.getUserId()));
		String callNewFunc = Util.trim(params.getString("callNewFunc"));
		result.set("sCaseBrId", caseBrId);
		result.set("sQueryUser", queryUser);

		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);
		if (l120m01a != null) {
			// 簽報書
			mainCustId = l120m01a.getCustId();
			mainDupNo = l120m01a.getDupNo();
		} else {
			// 動審表
			L160M01A l160m01a = amlRelateService.findModelByMainId(
					L160M01A.class, mainId);
			if (l160m01a != null) {
				// 簽報書
				mainCustId = l160m01a.getCustId();
				mainDupNo = l160m01a.getDupNo();
			}
		}

		// 如果是新模式切回舊模式，要把L120S09B(如果有的話，NCRESULT 清掉)，才不會判斷錯誤(但L120S09B不要刪掉)
		// J-107-0176 代發機制 - 不用清空QueryBrId QueryUser WayMode
		L120S09B l120s09b = amlRelateService.findL120s09bByMainId(mainId);
		if (l120s09b != null && Util.equals("N", callNewFunc)) { // 若代發不為舊方式也需留存資訊
			l120s09b.setNcResult("");
			l120s09b.setUniqueKey("");
			l120s09b.setRefNo("");
			l120s09b.setNcCaseId("");
			l120s09b.setQueryDateS(null);
			lmsService.save(l120s09b);
		}

		result = this.insteadQueryBlackListInner(mainId, mainCustId, mainDupNo,
				caseBrId);

		Date blackListQDate = CapDate.getCurrentTimestamp();
		result.set("blackListQDate", CapDate.formatDate(blackListQDate,
				UtilConstants.DateFormat.YYYY_MM_DD));

		return result;
	}

	/**
	 * 黑名單查詢 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param mainId
	 * @param mainCustId
	 * @param mainDupNo
	 * @return
	 * @throws CapException
	 */
	public CapAjaxFormResult insteadQueryBlackListInner(String mainId,
			String mainCustId, String mainDupNo, String QueryBrId)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
		NumberFormat n = new DecimalFormat("000");

		List<L120S09A> l120s09as = amlRelateService
				.findL120s09aByMainIdWithShowOrder(mainId);
		int checkSeq = 0;
		if (l120s09as != null && !l120s09as.isEmpty()) {
			for (L120S09A l120s09a : l120s09as) {
				String tCustId = Util.trim(l120s09a.getCustId());
				String tDupNo = Util.trim(l120s09a.getDupNo());
				String tCustName = Util.toSemiCharString(String.valueOf(Util
						.trim(l120s09a.getCustName())));
				String tCustEName = Util.trim(l120s09a.getCustEName());
				String qName = Util.notEquals(tCustEName, "") ? tCustEName
						: tCustName;
				// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
				String tCountry = Util.trim(l120s09a.getCountry());

				if (Util.equals(qName, "")) {
					continue;
				}

				// J-111-0577_05097_B1001 Web
				// e-Loan系統修改以客戶名稱與國別各別經ELOAN系統分開掃描之規則。
				// List<String> blackList = customerSrv.findBlackList(QueryBrId,
				// qName, mainId);
				List<String> blackList = customerSrv.findBlackListWithCountry(
						QueryBrId, qName, mainId, tCountry);

				String blackListCode = blackList
						.get(ICustomerService.BlackList_ReturnCode);
				String memo = blackList
						.get(ICustomerService.BlackList_OFACName);
				int SeqNum = 0;
				if (Util.equals(blackListCode,
						UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單)) {
					SeqNum = 11;
				} else if (Util.equals(blackListCode,
						UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單)) {
					SeqNum = 21;
				} else if (Util.equals(blackListCode,
						UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單)) {
					SeqNum = 31;
				} else {
					blackListCode = "";
				}
				if (Util.equals(tCustId, mainCustId)
						&& Util.equals(tDupNo, mainDupNo)) {
					SeqNum = SeqNum - 1;
				}

				// J-106-0029-003 Web e-Loan授信簽報書借款人基本資料與動審表黑名單查詢調整使用共用模組
				// 國內分行如果實質受益人查不到需加查PEPS是否為國外政治敏感人物
				boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
						.getBranch(QueryBrId).getBrNoFlag());
				if (!isOverSea) {

					// J-107-0070-001 Web e-Loan
					// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
					// J-108-0039_05097_B1001 Web e-Loan
					// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
					if ((SeqNum == 31 || SeqNum == 21)
							&& (l120s09a
									.getCustRelation()
									.indexOf(
											UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人) > -1
									|| l120s09a
											.getCustRelation()
											.indexOf(
													UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員) > -1 || l120s09a
									.getCustRelation()
									.indexOf(
											UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人) > -1)) {
						String otherPEPS = "";

						List<L120S01P> l120s01ps = amlRelateService
								.findL120s01pByMainIdWithoutRType(mainId);

						if (l120s01ps != null && !l120s01ps.isEmpty()) {
							for (L120S01P l120s01p : l120s01ps) {
								if (Util.equals(
										Util.trim(l120s01p.getRType()),
										UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)
										|| Util.equals(
												Util.trim(l120s01p.getRType()),
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員)
										|| Util.equals(
												Util.trim(l120s01p.getRType()),
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人)) {
									String s01pId = Util
											.trim(l120s01p.getRId());
									String s01pNo = Util.trim(l120s01p
											.getRDupNo());
									String s01pName = Util.trim(l120s01p
											.getRName());
									String s01pEName = Util.trim(l120s01p
											.getREName());
									String s01pPEPS = Util.trim(l120s01p
											.getPeps());

									if (Util.notEquals(tCustId, "")
											&& Util.notEquals(tDupNo, "")) {
										if (Util.notEquals(s01pId, "")
												&& Util.notEquals(s01pNo, "")) {
											if (Util.equals(s01pId, tCustId)
													&& Util.equals(s01pNo,
															tDupNo)) {
												if (Util.equals(s01pPEPS, "Y")) {
													otherPEPS = "Y";
													break;
												}
											}
										}
									}

									if (Util.notEquals(tCustName, "")) {
										if (Util.notEquals(s01pName, "")) {
											if (Util.equals(s01pName, tCustName)) {
												if (Util.equals(s01pPEPS, "Y")) {
													otherPEPS = "Y";
													break;
												}
											}
										}
									}

									if (Util.notEquals(tCustEName, "")) {
										if (Util.notEquals(s01pEName, "")) {
											if (Util.equals(s01pEName,
													tCustEName)) {
												if (Util.equals(s01pPEPS, "Y")) {
													otherPEPS = "Y";
													break;
												}
											}
										}
									}
								}
							}
						}

						if (Util.equals(otherPEPS, "Y")) {
							blackListCode = UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單;
							memo = "PEPS*";
							SeqNum = 11;
						}
					}
				}

				l120s09a.setBlackListCode(blackListCode);
				l120s09a.setMemo(memo);
				l120s09a.setQueryDateS(CapDate.getCurrentTimestamp());
				l120s09a.setSeqNum(SeqNum);

				// J-106-0238-001
				// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
				// 舊模式要清
				l120s09a.setCheckSeq(n.format(++checkSeq));
				l120s09a.setCm1AmlStatus("");
				l120s09a.setCheckResult("");
				l120s09a.setHitList("");

				// J-106-0238-001
				// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
				if (Util.notEquals(tCustId, "") && Util.notEquals(tDupNo, "")) {
					// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
					if (LMSUtil
							.isContainValue(
									Util.trim(l120s09a.getCustRelation()),
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶,
									",")) {
						String luvRiskLevel = amlRelateService
								.getCustLuvRiskLevel(QueryBrId, tCustId, tDupNo);
						l120s09a.setLuvRiskLevel(Util.trim(luvRiskLevel));
					} else {
						l120s09a.setLuvRiskLevel("");
					}
				}
				lmsService.save(l120s09a);
			}
		}

		return result;
	}

	/**
	 * J-108-0039_05097_B1001 Web e-Loan
	 * 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult applyCtrlPeoData(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		StringBuffer errMsg = new StringBuffer("");
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String mainId = Util.trim(params.getString("mainId"));

		List<L120S01P> l120s01ps = amlRelateService
				.findL120s01pByMainIdAndCustIdWithRType(mainId, custId, dupNo,
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人);

		if (l120s01ps != null && !l120s01ps.isEmpty()) {
			for (L120S01P l120s01p : l120s01ps) {
				lmsService.delete(l120s01p);
			}
		}

		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());

		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchSrv
				.getBranch(unitNo).getBrNoFlag());

		// 0024-23 高階管理員 獨資要求建兩個具控制權人，但分行取巧把同一個具控制權人建兩次，會造成L120S01P KEY DUP
		Map<String, String> ctrlPeoMap = new HashMap<String, String>();

		// J-108-0039_05097_B1001 TODO XXXXXXXXXXXXXXXXX
		if (!isOverSea) {
			// 國內
			List<Map<String, Object>> cmfDataList = misDbService
					.findCMFLUNB1ByCustId(custId, dupNo);

			if (cmfDataList == null || cmfDataList.isEmpty()) {
				// L120S09a.message18={0}無借款人基本資料可以引進
				errMsg.append(MessageFormat.format(
						pop.getProperty("L120S09a.message18"), "0024"));
				result.set("errMsg", errMsg.toString());
				return result;
			}

			int count = 0;
			for (Map<String, Object> cmfData : cmfDataList) {
				if (cmfData == null || cmfData.isEmpty()) {
					// L120S09a.message18={0}無借款人基本資料可以引進
					errMsg.append(MessageFormat.format(
							pop.getProperty("L120S09a.message18"), "0024"));
					result.set("errMsg", errMsg.toString());
					return result;
				} else {

					String BUSCD = Util.trim(MapUtils.getString(cmfData,
							"BUSCD"));
					String CMSR_SR_PV_ID = Util.trim(MapUtils.getString(
							cmfData, "CMB1_CNTRL_PV_ID"));
					String CMSR_SR_PV_CNAME = Util.trim(MapUtils.getString(
							cmfData, "CMB1_CNTRL_PV_CNAME"));
					String CMSR_SR_PV_ENAME = Util.trim(MapUtils.getString(
							cmfData, "CMB1_CNTRL_PV_ENAME"));
					String CMSR_SR_PV_BD = Util.trim(MapUtils.getString(
							cmfData, "CMB1_CNTRL_PV_BD"));
					String CMSR_SR_PV_NC = Util.trim(MapUtils.getString(
							cmfData, "CMB1_CNTRL_PV_NC"));

					// BTT 0024-23 具控制權人畫面也有PEP
					String CMSR_SR_PEP = Util.trim(MapUtils.getString(cmfData,
							"CMB1_CNTRL_PEP"));

					String rName = CMSR_SR_PV_CNAME;
					if (Util.equals(CMSR_SR_PV_CNAME, "")
							&& Util.notEquals(CMSR_SR_PV_ENAME, "")) {
						rName = CMSR_SR_PV_ENAME;
					}

					if (Util.notEquals(BUSCD, "")
							&& !LMSUtil.isBusCode_060000_130300(BUSCD)) {
						// 企業戶

						if (Util.notEquals(CMSR_SR_PV_ID, "")
								|| Util.notEquals(CMSR_SR_PV_CNAME, "")
								|| Util.notEquals(CMSR_SR_PV_ENAME, "")) {

							// 判斷具控制權人ID + RNAME有沒有重覆
							if (ctrlPeoMap.containsKey(CMSR_SR_PV_ID + "-"
									+ rName)) {
								continue;
							} else {

								ctrlPeoMap.put(CMSR_SR_PV_ID + "-" + rName,
										rName);

								L120S01P l120s01p = new L120S01P();
								count = count + 1;
								l120s01p = new L120S01P();
								l120s01p.setMainId(mainId);
								l120s01p.setCreateTime(CapDate
										.getCurrentTimestamp());
								l120s01p.setCreator(user.getUserId());
								l120s01p.setCustId(custId);
								l120s01p.setDupNo(dupNo);
								l120s01p.setType("1");
								l120s01p.setRId(CMSR_SR_PV_ID);
								l120s01p.setRDupNo("0");

								l120s01p.setRName(rName);

								l120s01p.setREName(CMSR_SR_PV_ENAME);
								if (Util.equals(CMSR_SR_PV_ENAME, "")
										&& Util.notEquals(CMSR_SR_PV_CNAME, "")) {
									if (CMSR_SR_PV_CNAME.length() == CMSR_SR_PV_CNAME
											.getBytes().length) {
										l120s01p.setREName(CMSR_SR_PV_CNAME);
									}
								}

								l120s01p.setRType(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人);

								L120S01P l120s01pMax = amlRelateService
										.findL120s01pMaxSeqNumByMainIdAndCustIdWithRType(
												mainId,
												custId,
												dupNo,
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人);
								if (l120s01pMax == null) {
									l120s01p.setSeqNum(1);
								} else {
									l120s01p.setSeqNum(l120s01pMax.getSeqNum() == null ? 1
											: l120s01pMax.getSeqNum() + 1);
								}

								l120s01p.setBirthDate(CapDate
										.parseDate(CMSR_SR_PV_BD));
								l120s01p.setNation(CMSR_SR_PV_NC);
								l120s01p.setPeps(CMSR_SR_PEP);

								lmsService.save(l120s01p);
							}
						}

					} else {
						// 個人戶無具控制權人
						// L120S09a.message26={0}無具控制權人資料可以引進
						errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
						errMsg.append(MessageFormat.format(
								pop.getProperty("L120S09a.message26"),
								"0024-23"));
						result.set("errMsg", errMsg.toString());
						return result;
					}

				}
			}

			if (count == 0) {
				// 個人戶無具控制權人
				// L120S09a.message26={0}無具控制權人資料可以引進
				errMsg.append(MessageFormat.format(
						pop.getProperty("L120S09a.message26"), "0024-23"));
				result.set("errMsg", errMsg.toString());
				return result;
			}

		} else {

			// 海外，J-108-0039 本案只針對國內引進
			// J-108-0039_05097_B1001 Web e-Loan
			// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。

			List<Map<String, Object>> cmfDataList = misDbService
					.findCMFLUNB1ByCustId(custId, dupNo);

			if (cmfDataList == null || cmfDataList.isEmpty()) {
				// L120S09a.message18={0}無借款人基本資料可以引進
				errMsg.append(MessageFormat.format(
						pop.getProperty("L120S09a.message18"), "0024"));
				result.set("errMsg", errMsg.toString());
				return result;
			}

			int count = 0;
			for (Map<String, Object> cmfData : cmfDataList) {
				if (cmfData == null || cmfData.isEmpty()) {
					// L120S09a.message26={0}無具控制權人資料可以引進
					errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
					errMsg.append(MessageFormat.format(
							pop.getProperty("L120S09a.message26"), "0024"));
					result.set("errMsg", errMsg.toString());
					return result;
				} else {

					String BUSCD = Util.trim(MapUtils.getString(cmfData,
							"BUSCD"));
					String CMSR_SR_PV_ID = Util.trim(MapUtils.getString(
							cmfData, "CMB1_CNTRL_PV_ID"));
					String CMSR_SR_PV_CNAME = Util.trim(MapUtils.getString(
							cmfData, "CMB1_CNTRL_PV_CNAME"));
					String CMSR_SR_PV_ENAME = Util.trim(MapUtils.getString(
							cmfData, "CMB1_CNTRL_PV_ENAME"));
					String CMSR_SR_PV_BD = Util.trim(MapUtils.getString(
							cmfData, "CMB1_CNTRL_PV_BD"));
					String CMSR_SR_PV_NC = Util.trim(MapUtils.getString(
							cmfData, "CMB1_CNTRL_PV_NC"));
					String rName = CMSR_SR_PV_CNAME;
					if (Util.equals(CMSR_SR_PV_CNAME, "")
							&& Util.notEquals(CMSR_SR_PV_ENAME, "")) {
						rName = CMSR_SR_PV_ENAME;
					}

					if (Util.notEquals(BUSCD, "")
							&& !LMSUtil.isBusCode_060000_130300(BUSCD)) {
						// 企業戶

						if (Util.notEquals(CMSR_SR_PV_ID, "")
								|| Util.notEquals(CMSR_SR_PV_CNAME, "")
								|| Util.notEquals(CMSR_SR_PV_ENAME, "")) {

							// 判斷具控制權人ID + RNAME有沒有重覆
							if (ctrlPeoMap.containsKey(CMSR_SR_PV_ID + "-"
									+ rName)) {
								continue;
							} else {

								ctrlPeoMap.put(CMSR_SR_PV_ID + "-" + rName,
										rName);

								L120S01P l120s01p = new L120S01P();
								count = count + 1;
								l120s01p = new L120S01P();
								l120s01p.setMainId(mainId);
								l120s01p.setCreateTime(CapDate
										.getCurrentTimestamp());
								l120s01p.setCreator(user.getUserId());
								l120s01p.setCustId(custId);
								l120s01p.setDupNo(dupNo);
								l120s01p.setType("1");
								l120s01p.setRId(CMSR_SR_PV_ID);
								l120s01p.setRDupNo("0");

								l120s01p.setRName(rName);

								l120s01p.setREName(CMSR_SR_PV_ENAME);
								if (Util.equals(CMSR_SR_PV_ENAME, "")
										&& Util.notEquals(CMSR_SR_PV_CNAME, "")) {
									if (CMSR_SR_PV_CNAME.length() == CMSR_SR_PV_CNAME
											.getBytes().length) {
										l120s01p.setREName(CMSR_SR_PV_CNAME);
									}
								}

								l120s01p.setRType(UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人);

								L120S01P l120s01pMax = amlRelateService
										.findL120s01pMaxSeqNumByMainIdAndCustIdWithRType(
												mainId,
												custId,
												dupNo,
												UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人);
								if (l120s01pMax == null) {
									l120s01p.setSeqNum(1);
								} else {
									l120s01p.setSeqNum(l120s01pMax.getSeqNum() == null ? 1
											: l120s01pMax.getSeqNum() + 1);
								}

								l120s01p.setBirthDate(CapDate
										.parseDate(CMSR_SR_PV_BD));
								l120s01p.setNation(CMSR_SR_PV_NC);
								l120s01p.setPeps("");

								lmsService.save(l120s01p);
							}
						}

					} else {
						// 個人戶無具控制權人
						// L120S09a.message26={0}無具控制權人資料可以引進
						errMsg.append(Util.equals(errMsg, "") ? "" : "<BR>");
						errMsg.append(MessageFormat.format(
								pop.getProperty("L120S09a.message26"),
								"0024-23"));
						result.set("errMsg", errMsg.toString());
						return result;
					}

				}
			}

			if (count == 0) {
				// 個人戶無具控制權人
				// L120S09a.message26={0}無具控制權人資料可以引進
				errMsg.append(MessageFormat.format(
						pop.getProperty("L120S09a.message26"), "0024-23"));
				result.set("errMsg", errMsg.toString());
				return result;
			}

		}

		result.set("errMsg", errMsg.toString());
		return result;

	}

	/**
	 * I-107-0260_05097_B1001 Web e-Loan企金授信系統增加提示訊息【該客戶屬於巴拿馬文件名單】
	 * 
	 * @param mainId
	 * @param mainCustId
	 * @param mainDupNo
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult applyCustPanaInfo(PageParameters params)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("resultMsg", "");
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		unitNo = (instead ? queryBrId : user.getUnitNo());

		String mainCustId = "";
		String mainDupNo = "";
		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);
		if (l120m01a != null) {
			// 簽報書
			mainCustId = l120m01a.getCustId();
			mainDupNo = l120m01a.getDupNo();
		} else {
			// 動審表
			L160M01A l160m01a = amlRelateService.findModelByMainId(
					L160M01A.class, mainId);
			if (l160m01a != null) {
				// 動審表
				mainCustId = l160m01a.getCustId();
				mainDupNo = l160m01a.getDupNo();
			}
		}

		LinkedHashMap<String, String> allBorrowerIdMap = new LinkedHashMap<String, String>();

		List<L120S09A> l120s09as = amlRelateService
				.findL120s09aByMainIdWithOrder1(mainId);

		if (l120s09as != null && !l120s09as.isEmpty()) {

			for (L120S09A l120s09a : l120s09as) {

				String tCustId = Util.trim(l120s09a.getCustId());
				String tDupNo = Util.trim(l120s09a.getDupNo());
				String tCustName = Util.trim(l120s09a.getCustName());

				if (Util.notEquals(tCustId, "") && Util.notEquals(tDupNo, "")) {

					// 借戶才要，因為目前只有檢核借戶才要引風險等級，代表只有借戶才有完整的0024-23
					if (LMSUtil
							.isContainValue(
									Util.trim(l120s09a.getCustRelation()),
									UtilConstants.Casedoc.L120s09aBlackListCtlTarget.借戶,
									",")) {

						String tFullCustId = tCustId + "-" + tDupNo;
						if (!allBorrowerIdMap.containsKey(tFullCustId)) {
							allBorrowerIdMap.put(tFullCustId, tCustName);
						}

					}

				}

			}

		}

		if (allBorrowerIdMap != null && !allBorrowerIdMap.isEmpty()) {
			String panaCust = amlRelateService
					.chkCustListInPana(allBorrowerIdMap);

			if (Util.notEquals(panaCust.toString(), "")) {
				// L120S09a.lnsp0130_3=下列借款人屬於巴拿馬文件名單
				result.set("resultMsg", pop.getProperty("L120S09a.lnsp0130_3")
						+ ":<BR>" + panaCust.toString());
			} else {
				// L120S09a.lnsp0130_1=借款人皆無列屬於巴拿馬文件名單
				result.set("resultMsg", pop.getProperty("L120S09a.lnsp0130_4"));
			}
		} else {
			// L120S09a.lnsp0130_5=無借款人資訊可查詢巴拿馬文件名單
			result.set("resultMsg", pop.getProperty("L120S09a.lnsp0130_5"));
		}

		return result;

	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120s01pStr(PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString("mainId");
		String custId = params.getString("custId");
		String dupNo = params.getString("dupNo");

		Map<String, Object> chkInsteadMap = amlRelateService
				.checkInstead(mainId);
		boolean instead = MapUtils.getBooleanValue(chkInsteadMap, "instead",
				false);
		String queryBrId = MapUtils.getString(chkInsteadMap, "queryBrId");
		String unitNo = (instead ? queryBrId : user.getUnitNo());

		boolean needCount = false;
		int num = 0;

		L120S01B l120s01b = amlRelateService.findL120s01bByUniqueKey(mainId,
				custId, dupNo);
		if (l120s01b != null) {
			List<L120S01P> l120s01p_7 = amlRelateService
					.findL120s01pByMainIdAndCustIdWithRTypeOrderForBuildStr(
							mainId,
							custId,
							dupNo,
							UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);
			needCount = false;
			num = 0;
			if (l120s01p_7.size() > 0) {
				needCount = true;
			}
			StringBuffer sb_7 = new StringBuffer();
			if (l120s01p_7 != null && !l120s01p_7.isEmpty()) {
				for (L120S01P l120s01p : l120s01p_7) {
					num++;
					sb_7.append((sb_7.length() > 0 ? "、" : ""));
					sb_7.append((needCount ? (num + ".") : ""));// (Integer.toString(num)
																// + ".") :
																// ""));
					sb_7.append(l120s01p.getRName());
				}
			} else {
				// 實質受益人空白時，新加坡要顯示不適用
				if (amlRelateService
						.needShowNotApplicableWhenNoEffective(unitNo)) {
					sb_7.append(pop.getProperty("notApplicable"));
				} else {
					sb_7.append(pop.getProperty("nohave"));
				}
			}
			l120s01b.setBeneficiary(sb_7.toString());

			List<L120S01P> l120s01p_10 = amlRelateService
					.findL120s01pByMainIdAndCustIdWithRTypeOrderForBuildStr(
							mainId,
							custId,
							dupNo,
							UtilConstants.Casedoc.L120s09aBlackListCtlTarget.高階管理人員);
			needCount = false;
			num = 0;
			if (l120s01p_10.size() > 0) {
				needCount = true;
			}
			StringBuffer sb_10 = new StringBuffer();
			if (l120s01p_10 != null && !l120s01p_10.isEmpty()) {
				for (L120S01P l120s01p : l120s01p_10) {
					num++;
					sb_10.append((sb_10.length() > 0 ? "、" : ""));
					sb_10.append((needCount ? (num + ".") : ""));// (Integer.toString(num)
																	// + ".") :
																	// ""));
					sb_10.append(l120s01p.getRName());
				}
			} else {
				sb_10.append(pop.getProperty("nohave"));
			}
			l120s01b.setSeniorMgr(sb_10.toString());

			List<L120S01P> l120s01p_11 = amlRelateService
					.findL120s01pByMainIdAndCustIdWithRTypeOrderForBuildStr(
							mainId,
							custId,
							dupNo,
							UtilConstants.Casedoc.L120s09aBlackListCtlTarget.具控制權人);
			needCount = false;
			num = 0;
			if (l120s01p_11.size() > 0) {
				needCount = true;
			}
			StringBuffer sb_11 = new StringBuffer();
			if (l120s01p_11 != null && !l120s01p_11.isEmpty()) {
				for (L120S01P l120s01p : l120s01p_11) {
					num++;
					sb_11.append((sb_11.length() > 0 ? "、" : ""));
					sb_11.append((needCount ? (num + ".") : ""));// (Integer.toString(num)
																	// + ".") :
																	// ""));
					sb_11.append(l120s01p.getRName());
				}
			} else {
				sb_11.append(pop.getProperty("nohave"));
			}
			l120s01b.setCtrlPeo(sb_11.toString());

			lmsService.save(l120s01b);
		}
		result.set("beneficiary", l120s01b.getBeneficiary());
		result.set("seniorMgr", l120s01b.getSeniorMgr());
		result.set("ctrlPeo", l120s01b.getCtrlPeo());

		return result;
	}

	/**
	 * J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120s09c(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// 2022.07.14出現第二版本，所以新增版本控管的機制
		String versionDate = Util.trim(params.getString("versionDate"));

		L120S09C l120s09c = null;
		if (Util.notEquals(oid, "")) {
			l120s09c = amlRelateService.findL120s09cByOid(oid);
		}

		if (l120s09c != null) {
			// 有資料(更新儲存)
			l120s09c = CapBeanUtil.map2Bean("state_", params, l120s09c);
		} else {
			// 如無資料則新建立
			l120s09c = new L120S09C();
			l120s09c = CapBeanUtil.map2Bean("state_", params, l120s09c);
			// 設定一些初始化內容
			l120s09c.setMainId(mainId);
			l120s09c.setVersionDate(versionDate);
			l120s09c.setCreateTime(CapDate.getCurrentTimestamp());
			l120s09c.setCreator(user.getUserId());
		}

		// 戶名轉半形，目前不對user輸入的資料做處理
		// l120s09c.setCustName(Util.toSemiCharString(Util.trim(l120s09c
		// .getCustName())));

		// 儲存
		try {
			lmsService.save(l120s09c);
		} catch (Exception e) {
			logger.error("[saveL120s09a] service1201.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", UtilConstants.Mark.HTMLSPACE);
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.輸入位數超過, param), getClass());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}

		result.set("newOid", l120s09c.getOid());
		return result;
	}// ;

	/**
	 * J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信 開起態樣檢核表時查詢
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s09c(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		L120S09C l120s09c = amlRelateService.findL120s09cByOid(oid);
		if (l120s09c == null) {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}

		String[] colMap = CapEntityUtil.getColumnName(l120s09c);
		List<String> colList = new ArrayList<String>(Arrays.asList(colMap));
		colList.remove("oid");
		colList.remove("creator");
		colList.remove("createTime");
		colList.remove("updater");
		colList.remove("updateTime");
		Map<String, Object> map = CapBeanUtil.bean2Map(l120s09c,
				colList.toArray(new String[colList.size()]), "state_");
		result.putAll(map);

		// 2022.07.14新增版本機制
		result.set("oid", l120s09c.getOid());
		result.set("versionDate", l120s09c.getVersionDate());
		return result;
	}

	/**
	 * J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信 新增態樣檢核表時查詢
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s09cInit(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String[] chooseCustArr = params.getStringArray("chooseCustArr");
		// J-113-0505 依113.11.25兆銀洗防字第1130050016號函已修訂「兆豐商銀疑似洗錢、資恐及異常交易態樣檢核表-授信」113.11版，e-loan系統請更新格式版本。
		String versionDate = params.getString("versionDate", "11311");// 如果沒勾當他選最新版

		Map<String, String> resMap = amlRelateService.queryL120s09cInit(mainId,
				chooseCustArr, versionDate);
		result.putAll(resMap);
		return result;
	}

	/**
	 * 刪除態樣檢核表 J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL120s09c(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getString("listOid").split(",");

		amlRelateService.deleteL120s09cByOid(oids);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;

	}

	/**
	 * 檢查是否有態樣檢核表資料，有才可以列印
	 * 
	 * J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkL120s09cCount(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);

		List<L120S09C> l120s09cList = amlRelateService
				.findL120s09cByMainId(mainId);
		result.set("l120s09cCount", l120s09cList.size());
		return result;

	}

	/**
	 * J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信 這個區塊只有國內才有這功能，若為海外時需隱藏區塊
	 * 按鈕+grid區塊
	 * 
	 * @param mainId
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult showAmlStateCheck(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String typCd = "";
		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);
		if (l120m01a != null) {
			// 簽報書
			typCd = l120m01a.getTypCd();
		} else {
			// 動審表
			L160M01A l160m01a = amlRelateService.findModelByMainId(
					L160M01A.class, mainId);
			if (l160m01a != null) {
				// 簽報書
				typCd = l160m01a.getTypCd();
			}
		}

		/** 類別 0.無,1.DBU,4.OBU,5.海外 */
		if ("5".equals(typCd)) {
			// 若為海外時需隱藏區塊
			result.set("showAmlState", false);
		} else {
			// 國內時需顯示此區塊
			result.set("showAmlState", true);
		}
		
		// 不生效，避免突然要關閉113版
		boolean removeL120s09c11311 = clsService
				.is_function_on_codetype("removeL120s09c11311");
		if (removeL120s09c11311) {
			// 避免突然要11311版下架
			result.set("remove11311", true);
		}

		// 只要這個參數是生效，就是直接關閉新增按鈕
		boolean removeL120s09cAdd = clsService
				.is_function_on_codetype("removeL120s09cAdd");
		if (removeL120s09cAdd) {
			result.set("removeAddButton", true);
		}
		
		return result;

	}

	/**
	 * J-112-0534 引入T70資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult importT70(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);

		amlRelateService.checkL120m01eForT70(l120m01a);

		StringBuilder over2MonthSb = amlRelateService
				.importL120s09aT70Data(l120m01a);

		if (Util.isNotEmpty(over2MonthSb)) {
			result.set("resultMsg", over2MonthSb.toString() + "資料來源已逾2個月");
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;

	}

	/**
	 * J-113-0082 引入「受告誡處分」資訊
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult importCmfwarnp(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		amlRelateService.importL120s09aWarnData(mainId);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;

	}
}
