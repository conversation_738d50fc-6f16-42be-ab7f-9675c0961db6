/* 
 * 原債清的DEB2010R03FormHandler.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.form;

import java.net.URISyntaxException;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.gwclient.EloanBatchClient;
import com.mega.eloan.common.gwclient.EloanServerBatReqMessage;
import com.mega.eloan.common.gwclient.UCCFTPClient;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.DebConfig;
import com.mega.eloan.lms.rpt.service.LMS9550R01RptService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.read.biff.BiffException;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;
import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 主債協商-報送卡務檔案之form handler
 * </pre>
 * 
 * @since 2012/7/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/16,TammyChen,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9550r01formhandler")
public class LMS9550R01FormHandler extends AbstractFormHandler {

	@Resource
	LMS9550R01RptService service;
	@Resource
	DocFileService docFileSrv;
	@Resource
	UserInfoService userInfoSrv;
	@Resource
	UCCFTPClient ftpClient;
	@Resource
	DebConfig debConfig;
	@Resource
	EloanBatchClient eloanBatClient;
	@Resource
	LMS9550R01RptService lms9550r01rptservice;
	@Resource
	CodeTypeService codetypeService;// com.bcodetype

	@Resource
	BranchService branchService;

	/**
	 * <pre>
	 * 啟動報表批次
	 * 
	 * @param params PageParameters
	 * @return CapAjaxFormResult
	 * @throws Exception 
	 * @throws URISyntaxException 
	 * @throws WriteException 
	 * @throws BiffException
	 * @throws RowsExceededException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult generate(PageParameters params)
			throws Exception {
		CapAjaxFormResult result = new CapAjaxFormResult();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, String> LMSBatchMap = codetypeService
				.findByCodeType("LMSBatch");
		if (LMSBatchMap == null) {
			LMSBatchMap = new LinkedHashMap<String, String>();
		}
		/*
		 * //直接線上呼叫BATCH執行 EloanSubsysBatReqMessage reqs = new
		 * EloanSubsysBatReqMessage(); reqs.setUrl("SYS_URL_LMS");
		 * //"http://127.0.0.1:8080/lms-web"
		 * 
		 * reqs.setLocalUrl(true);
		 * reqs.setReqFormat(EloanSubsysBatReqMessage.REQ_FMT_JSON);
		 * reqs.setServiceId("RptLMSBatch2ServiceImpl"); reqs.setTimeout(1200);
		 * 
		 * JSONObject requestJSON = new JSONObject();
		 * requestJSON.element("rptNo", rptNo); requestJSON.element("docType",
		 * "1"); requestJSON.element("unitNo", user.getUnitNo());
		 * requestJSON.element("userId", user.getUserId());
		 * requestJSON.element("dataStartDate", dataStartDate);
		 * requestJSON.element("dataEndDate", dataEndDate);
		 * reqs.setRequestJSON(requestJSON); eloanBatClient.send(reqs);
		 */

		// 線上啟動排程執行
		EloanServerBatReqMessage req = new EloanServerBatReqMessage();
		req.setUserId(user.getUserId());
		req.setRunType(EloanServerBatReqMessage.RUN_TYPE_QUEUE);
		logger.info(LMSBatchMap.get("16")); // 每日傳送卡務檔案作業
		req.setSchId(LMSBatchMap.get("16"));

		JSONObject paraJson = new JSONObject();

		paraJson.put("docType", "1");
		paraJson.put("unitNo", user.getUnitNo());
		paraJson.put("userId", user.getUserId());

		StringBuffer batchParams = new StringBuffer();
		batchParams.append("REQUEST=").append(paraJson.toString());

		// req.setParams("rptNo=" + rptNo + ",docType=1,unitNo=" +
		// user.getUnitNo() + ",userId=" + user.getUserId() + ",dataStartDate="
		// + dataStartDate + ",dataEndDate=" + dataEndDate);

		// req.setParams(reqJson.toString());

		req.setParams(batchParams.toString());

		req.setDupeId("119");
		// eloanBatClient.setRserviceClient(rserviceClient);
		eloanBatClient.send(req);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, "已加入排程");
		return result;
	}

	/**
	 * 開啟文件 LMS9550V01Page.js openDoc
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		return lms9550r01rptservice.query(params);

	}// ;

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult sendToFTP(PageParameters params)
			throws CapException {

		return lms9550r01rptservice.sendToFTP(params);
	}

}
