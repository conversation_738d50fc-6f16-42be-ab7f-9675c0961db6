var initS09aJson = {
	handlerName : null,
	// 設定handler名稱
	setHandler : function(){
		this.handlerName = "cls1141m01formhandler";		
	}
};

$(document).ready(function() {
	initS09aJson.setHandler();
	$.form.init({
        formHandler: initS09aJson.handlerName,
        formPostData:{
        	formAction : "queryL120M01A",
        	oid : responseJSON.oid,
        	page : responseJSON.page,
        	mainId : responseJSON.mainId,
        	docType : responseJSON.docType,
        	docCode : responseJSON.docCode,
        	docKind : responseJSON.docKind,
			docStatus : responseJSON.mainDocStatus,
			areaDocstatus : responseJSON.areaDocstatus,
			txCode : responseJSON.txCode,
			itemDscr03 : "",
			itemDscr05 : "",
			ffbody : ""			
        },
		loadSuccess:function(jsonInit){
			setCkeditor2("itemDscr05",jsonInit.L120M01DForm05.itemDscr05);
			$("#CLSForm").find("#susHeadAccountDescription").val(jsonInit.L120M01DForm05.susHeadAccountDescription);
			$("#CLSForm").find("#hitSameBorrowerInfoExplanation").val(jsonInit.L120M01DForm05.hitSameBorrowerInfoExplanation);
			$("#CLSForm").find("#hitSameBorrowerInfoDescription").val(jsonInit.L120M01DForm05.hitSameBorrowerInfoDescription);
			if(responseJSON.page != "01"){
				var $showBorrowData = $("#showBorrowData");
				$showBorrowData.reset();
				$showBorrowData.setData(jsonInit.showBorrowData,false);	
			}								
		}
    });
	
	$("#hitSameBorrowerInfoButton").click(function(){
		CommonAPI.triggerOpener($("#hitSameBorrowerInfoDetailGrid"), "reloadGrid");
		$("#hitSameBorrowerInfoDetailThickbox").thickbox({
            title: i18n.clss09a['L120M01D.hitSameBorrowerInfo'],//命中同一通訊資訊
            width: 700,
            height: 450,
            align: "center",
            valign: "bottom",
            i18n: i18n.def,
            buttons: {
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
		
    });
	
	hitSameBorrowerInfoDetailGrid();
	
});

function hitSameBorrowerInfoDetailGrid(){

	$("#hitSameBorrowerInfoDetailGrid").iGrid({
        handler: 'cls1141gridhandler',
        height: 400,
		width:700,
        rownumbers: true,
        needPager: false,  //rowNum: 26,  不同的版本，對應的人頭戶態樣的筆數是變動
        action: "queryL120m01dForHittingSameBorrowerInfoDetail",
        postData: {
            tabFormMainId: responseJSON.mainId
        },
        colModel: [{
            colHeader: i18n.clss09a["L120M01D.name"],//姓名
            name: 'name',
            align: "left",
            width: 20,
            sortable: true
        }, {
            colHeader: i18n.clss09a["L120M01D.id"],//身分證字號
            name: 'id',
            align: "left",
            width: 20,
            sortable: true
        }, {
            colHeader: i18n.clss09a["L120M01D.address"],//通訊地址
            name: 'address',
            align: "left",
            width: 50,
            sortable: true
        }, {
            colHeader: i18n.clss09a["L120M01D.cellphone"],//手機
            name: 'cellphone',
            align: "left",
            width: 20,
            sortable: true
        }, {
            colHeader: i18n.clss09a["L120M01D.telphone"],//通訊電話
            name: 'telphone',
            align: "left",
            width: 20,
            sortable: true
        },{
            colHeader: i18n.clss09a["L120M01D.email"],//email
            name: 'email',
            align: "left",
            width: 20,
            sortable: true
        }]
    });
}

