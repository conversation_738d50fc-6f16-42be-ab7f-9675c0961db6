package tw.com.jcs.common;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <pre>
 * 寫檔
 * </pre>
 * 
 * @since 2012/5/4
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2012/5/4,Fantasy,new
 *          </ul>
 */
public class WriteFile {

    public final static String ISO88591 = "iso-8859-1";
    public final static String BIG5 = "big5";
    public final static String UTF8 = "utf8";

    private static final Logger logger = LoggerFactory.getLogger(Util.class);

    /**
     * @param args
     */
    public static void main(String[] args) {
    }

    public WriteFile() {
        initWriteFile();
    }

    public void initWriteFile() {

    }

    /**
     * @param fileName
     *            可用相對路徑或絕對路徑
     * @param text
     *            將整個String寫入指定的檔案
     * @return
     */
    public static boolean write(String fileName, String text) {
        return write(fileName, text, false);
    }

    /**
     * @param fileName
     *            可用相對路徑或絕對路徑
     * @param text
     *            將整個String寫入指定的檔案
     * @param append
     *            true 將此次寫檔串在原本檔案最後面 | false 將此次寫檔蓋掉原本的文字檔內容
     * @return
     */
    public static boolean write(String fileName, String text, boolean append) {
        return write(fileName, text, UTF8, append);
    }

    /**
     * @param fileName
     *            可用相對路徑或絕對路徑
     * @param text
     *            將整個String寫入指定的檔案
     * @param format
     *            寫入檔案的編碼格式
     * @return
     */
    public static boolean write(String fileName, String text, String format) {
        return write(fileName, text, UTF8, false);
    }

    /**
     * 寫入文字檔(使用FileWriter 寫檔編碼為預設的iso-8859-1)， 因此此method使用OutputStreamWriter寫檔，可自行指定格式
     * 
     * @param fileName
     *            可用相對路徑或絕對路徑
     * @param text
     *            將整個String寫入指定的檔案
     * @param format
     *            寫入檔案的編碼格式
     * @param append
     *            true 將此次寫檔串在原本檔案最後面 | false 將此次寫檔蓋掉原本的文字檔內容
     * @return true 寫檔成功 | false 寫檔失敗
     */
    public static boolean write(String fileName, String text, String format, boolean append) {
        // 檢查路徑檔案是否存在
        if (!exists(fileName)) {
            mkDir(fileName);
        }

        try {
            BufferedWriter bufWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(fileName, append), format));
            bufWriter.write(trim(text));
            bufWriter.close();
        } catch (IOException e) {
            logger.error(e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 
     * 刪檔案
     * 
     * @param fileName
     *            可用相對路徑或絕對路徑
     */
    public static void deleteFile(String fileName) {
        if (exists(fileName)) {
            new File(fileName).delete();
        }
    }

    /**
     * 建立資料夾(可建多層資料夾)
     * 
     * @param path
     *            可用檔案的相對路徑或絕對路徑
     * @param 最後一層的資料夾
     */
    public static String mkDir(String path) {

        String parent = newFileInstance(path).getParent();

        newFileInstance(parent).mkdirs();
        return parent;
    }

    /**
     * 去除前後的空白字元
     * 
     * @param str
     *            字串
     */
    public static String trim(String str) {
        if (str == null)
            return "";
        return str.trim();
    }

    /**
     * 等同於 new File(...) <br>
     * 為了弱掃而使用
     * 
     * @param path
     *            可用檔案的相對路徑或絕對路徑
     * @return {@link File} instance if success.
     */
    public static File newFileInstance(String path) {
        try {
            Constructor<File> c = File.class.getConstructor(String.class);
            return c.newInstance(path);
        } catch (Exception e) {
            logger.warn("new File instance failed, path=" + path + ", error=" + e);
            return null;
        }
    }

    private static final String[] EMPTY_STRING_ARRAY = new String[] {};
    public static final String DEFAULT_DIRECTORY_PERMISSION = "rwxrwx---";
    private static final String OS_NAME = System.getProperty("os.name").toLowerCase();

    /**
     * 同new File(...)
     * 
     * @param path
     *            可用檔案的相對路徑或絕對路徑
     * @param more
     * @return
     */
    public static Path newPathInstance(String path, String... more) {

        try {
            Method method = Paths.class.getDeclaredMethod("get", String.class, EMPTY_STRING_ARRAY.getClass());
            return (Path) method.invoke(null, path, more);
        } catch (Exception e) {
            logger.warn("new Path instance failed, path=" + path + ", error=" + e);
            return null;
        }
    }

    /**
     * 判斷目錄是否存在
     * 
     * @param path
     *            可用相對路徑或絕對路徑
     * @return
     */
    public static boolean existsDirectory(String path) {

        return exists(path) && Files.isDirectory(Paths.get(path));
    }

    /**
     * 判斷目錄是否存在
     * 
     * @param path
     *            可用相對路徑或絕對路徑
     * @return
     */
    public static boolean exists(String path) {

        return Files.exists(newPathInstance(path));
    }

    /**
     * 刪除一個空目錄或檔案.
     * <p>
     * 為方便使用, Exception 不往外丟, 呼叫的程式應自行檢查是否為空目錄
     * 
     * @param path
     *            可用相對路徑或絕對路徑
     * @return true if success, false otherwise
     */
    public static boolean delete(String path) {

        try {
            return Files.deleteIfExists(newPathInstance(path));
        } catch (IOException e) {
            logger.warn("delete file/directory failed, path=" + path + ", error=" + e);
            return false;
        }
    }

    /**
     * 判斷作業系統
     * 
     * @return
     */
    public static boolean isWindows() {
        return (OS_NAME.indexOf("win") >= 0);
    }
}