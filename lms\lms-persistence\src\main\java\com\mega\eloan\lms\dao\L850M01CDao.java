/* 
 * L850M01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L850M01C;

/** 資料上傳作業主檔 **/
public interface L850M01CDao extends IGenericDao<L850M01C> {

	L850M01C findByOid(String oid);
	
	List<L850M01C> findByMainId(String mainId);

	List<L850M01C> findByIndex01(String mainId);
}