package com.mega.eloan.lms.dc.action;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.xpath.XPathAPI;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.traversal.NodeIterator;
import org.xml.sax.InputSource;
import org.xml.sax.helpers.DefaultHandler;

import com.mega.eloan.lms.dc.base.XMLHandler;
import com.mega.eloan.lms.dc.util.Base64;
import com.mega.eloan.lms.dc.util.CodeUtils;
import com.mega.eloan.lms.dc.util.DXLTransformer;
import com.mega.eloan.lms.dc.util.DXLUtil;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;
import com.mega.eloan.lms.dc.util.XMLTool;

/**
 * 主要處理.dxl相關: 轉為String,DOM Document,附件內容,RichText欄位值轉為 XML,轉出圖檔 & .doc & .htm
 * 
 * @move author :bang
 */

public class ParserAction extends BaseAction {
	protected static final int DXL_PROC_DEBUG_CNT = 200;
	protected String userPath = "";
	protected String htmlPath = ""; // 存放html檔之路徑
	protected String imagesPath = "";// 存放images檔之路徑
	protected String filesPath = "";// 存放附加檔案"FILES"之路徑
	protected String textPath = "";// 存放合併dxl,db2Xml後檔案的"TEXT"之路徑
	protected String richTextColumn = "";// RichText 之欄位名稱
	protected String loadDB2ClobPath = "";// load_db2 下clob 目錄所在位置路徑

	protected XMLHandler xmlHandler = new XMLHandler();

	protected final String PREFIX_RICHTEXT_XML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"
			+ "<note class='document' xmlns='http://www.lotus.com/dxl' version='6.5' replicaid='4825772400553012'>";

	// 先將小寫轉大寫再來比對資料
	protected final String ITEM_PATH = "//item[translate(@name,'abcdefghijklmnopqrstuvwxyz','ABCDEFGHIJKLMNOPQRSTUVWXYZ')='";

	private static final boolean isEnEnv = StringUtils.trimToEmpty(
			System.getProperty("user.language")).indexOf("en") != -1;

	/**
	 * 讀取.dxl並將之轉換為String型態
	 * 
	 * @param dxlFile
	 *            String :要被讀取的dxl檔(path/dxlName)
	 * @return sb StringBuffer:
	 * @throws IOException
	 */
	protected String readFile(String dxlFile) throws IOException {
		return FileUtils.readFileToString(new File(dxlFile), this
				.getConfigData().isOnlineMode() ? TextDefine.ENCODING_UTF8
				: TextDefine.ENCODING_MS950);
	}

	/**
	 * dxl檔轉換為DOM Document
	 * 
	 * @param dxl
	 *            String:已轉為String型態之.dxl檔
	 * @return A new DOM Document object.
	 * @throws Exception
	 */
	protected Document getDomDoc(String dxl) throws Exception {
		DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();

		factory.setValidating(false);
		factory.setNamespaceAware(false);
		DocumentBuilder builder = factory.newDocumentBuilder();
		builder.setErrorHandler(new DefaultHandler());

		StringReader reader = new StringReader(dxl);
		InputSource source = new InputSource(reader);
		return builder.parse(source);
	}

	/**
	 * 處理附件內容轉為.doc檔
	 * 
	 * @param domDoc
	 *            DOM Document:已轉為DOM Document的.dxl檔
	 * @param dxlName
	 *            String :.dxl的檔名
	 */
	protected void saveAttachments(Document domDoc, String dxlName,
			String schema) throws Exception {

		Node[] nodes = XMLTool.getNodesArray("//item[@name='$FILE']", domDoc);

		for (int i = 0; i < nodes.length; i++) {
			Node node = nodes[i];
			String name = XMLTool.selectSingleNodeValue(node, ".//file/@name");
			String base64Stream = XMLTool.selectSingleNodeValue(node,
					".//filedata");

			String prNo = "";
			String formName = dxlName.split("_")[0];// dxlNameEx:FCLS106M01_FFFCFFFFFC02BF5F48257A5600352D8B

			if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(schema)) {
				prNo = this.getItemValue(domDoc, "Project_No");
			} else {
				if (formName
						.equalsIgnoreCase(TextDefine.CLS_L140M01B_FORM_11501)
						|| formName
								.equalsIgnoreCase(TextDefine.CLS_L140M01B_FORM_71501)
						|| formName.equalsIgnoreCase("FCLS114M01")
						|| formName.equalsIgnoreCase("FCLS114M02")) {
					prNo = this.getItemValue(domDoc, "Project_No");
				} else {
					prNo = this.getItemValue(domDoc, "Private_No");
				}
			}
			String year = DXLUtil.getProjectNo(prNo, 0);
			if (StringUtils.isBlank(year)) {
				year = "2013";// default 2013 同DocFile.java
			}
			String unid = dxlName.split("_")[1];
			String tmp = "";
			String mainid = "";
			if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(schema)) {
			if (formName.equalsIgnoreCase("FLMS110M01")
					|| formName.equalsIgnoreCase("FLMS120M01")
					|| formName.equalsIgnoreCase("FLMS130M01")) {

				mainid = this.getItemValue(domDoc, "UnidDocID");

			} else if (formName.equalsIgnoreCase("FLMS140M01")
					|| formName.equalsIgnoreCase("FLMS740M01")) {
				String cntrDocId = this.getItemValue(domDoc, "WEBELOANMAINID");
				mainid = cntrDocId.isEmpty() ? unid : cntrDocId;
			}
			tmp = dxlName.replaceAll(unid, mainid);
			}else{
				tmp = dxlName;
			}

			String filename = this.filesPath + File.separator + tmp + "_"
					+ year + "_" + (i + 1) + "_" + name;

			if (isEnEnv && this.configData.isOnlineMode()) {
				filename = CodeUtils.getISO8859String(filename);
			}
			Base64.decodeToFile(base64Stream, filename);
		}
	}

	/**
	 * 取得RichText欄位之值並組成XML型態之字串
	 * 
	 * @param dxlXml
	 *            String : 已轉為String型態之.dxl檔
	 * @param rtColName
	 *            :RichText的欄位名稱
	 * @return rtstr String
	 */
	protected String getRichText(String dxlXml, String rtColName)
			throws Exception {
		int idx1 = dxlXml.indexOf("<item name='" + rtColName + "'>");
		String rtstr = null;
		if (idx1 != -1) {
			// 早期的RichText欄位後面有可能跟的不是<richtext>而是<text>,
			// 另不可用idx1去找第一個<richtext>或<text>若有其它<richtext>會出錯
			int richIdx = dxlXml.indexOf("<item name='" + rtColName
					+ "'><richtext>");
			int textIdx = dxlXml.indexOf("<item name='" + rtColName
					+ "'><text>");

			if (richIdx != -1) {
				// 取richIdx後第一次出現的</item>否則可能會誤取別人家的</text>或</richtext>
				int endItemIdx = dxlXml.indexOf("</item>", richIdx + 1);

				rtstr = dxlXml.substring(idx1, endItemIdx) + "</item>";
				rtstr = this.PREFIX_RICHTEXT_XML
						+ getXmlTag(dxlXml, "noteinfo") + rtstr + "</note>";
			} else if (textIdx != -1) {
				// 取textIdx後第一次出現的</item>否則可能會誤取別人家的</text>或</richtext>
				int endItemIdx = dxlXml.indexOf("</item>", textIdx + 1);

				rtstr = dxlXml.substring(idx1, endItemIdx) + "</item>";
				rtstr = this.PREFIX_RICHTEXT_XML
						+ getXmlTag(dxlXml, "noteinfo") + rtstr + "</note>";
			}
		}
		return rtstr;
	}

	/**
	 * 取得.dxl檔最上方之<noteinfo>開頭至</noteinfo>結尾字串
	 * 
	 * @param xml
	 *            String : 已轉為String型態之.dxl檔
	 * @param tag
	 *            String :tagName
	 * @return xmlStr String
	 */
	protected String getXmlTag(String xml, String tag) throws Exception {
		// <noteinfo noteid='922' unid='72C422BECF54980D48257814002BFADF'
		// sequence='1'> ...</noteinfo>
		int idx1 = xml.indexOf("<" + tag);
		String xmlStr = null;
		if (idx1 != -1) {
			String endTag = "</" + tag + ">";
			int idx2 = xml.indexOf(endTag, idx1 + 1);
			xmlStr = xml.substring(idx1, idx2 + endTag.length());
		}
		return xmlStr;
	}

	/*
	 * 需處理的 :: 因Notes轉檔時如有Richtext欄位，會轉入下列HTML TAG，且font-family:
	 * sans-serif及font-family: serif此兩種字型在R6產PDF時會出現疊字錯誤，擔保品轉 檔上線後發現此問題，
	 * 因授信也會轉入Richtext欄位，煩請將font-family: sans-serif 及 font-family: serif
	 * replace成細明體即可解決疊字錯誤。
	 */
	/**
	 * 處理Rich Text中的圖檔並將Rich Text輸出成Html格式
	 * 
	 * @param richTextXml
	 *            String : 已組成XML型態之RichText欄位之值
	 * @param dxlName
	 *            String :.dxl的檔名
	 * @param rtColName
	 *            String: Rich Text欄位名稱
	 * @param strBrn
	 *            :分行名稱
	 * @return html String
	 */
	// @SuppressWarnings("unused")
	// protected String processRTF(String richTextXml, String dxlName,
	// String rtColName, String strBrn) throws Exception {
	// String html = null;
	// if (richTextXml != null) {
	// DXLTransformer transformer = new DXLTransformer();
	// ByteArrayInputStream is = null;
	// // 產生.gif檔
	// richTextXml = genFileByBase64(dxlName, rtColName, richTextXml,
	// TextDefine.ATTACH_GIF);
	//
	// is = new ByteArrayInputStream(
	// richTextXml.getBytes(TextDefine.ENCODING_UTF8));
	// // is = new ByteArrayInputStream(xml.getBytes("US-ASCII"));
	// transformer.setXmlInputStream(is);
	// File dxl2html = new File(this.configData.getDC_ROOT()
	// + File.separator + "conf" + File.separator + "dxl2html.xsl");
	//
	// if (null == dxl2html) {
	// this.logger
	// .error("ParserAction在處理 processRTF時產生錯誤 -->【dxl2html.xsl】 is NotFound");
	// return null;
	// }
	// transformer.setXslin(dxl2html.getPath());
	// ByteArrayOutputStream baos = new ByteArrayOutputStream(
	// richTextXml.length());
	//
	// transformer.setOutput(baos);
	// transformer.doTransform();
	// html = baos.toString().replaceAll("vertical-align: bottom;", "");
	//
	// // String htmlName = this.htmlPath + File.separator + dxlName + "_"
	// // + rtColName + ".htm";
	// // 2013-03-26 Modify by Bang:Clob檔案改為直接寫至loadDB2下
	// String htmlName = this.loadDB2ClobPath + File.separator + strBrn
	// + File.separator + dxlName + "_" + rtColName + ".htm";
	//
	// PrintWriter out = new PrintWriter(new BufferedWriter(
	// new FileWriter(htmlName)));
	// out.write(html);
	// out.flush();
	// IOUtils.closeQuietly(out);
	// }
	// return html;
	// }

	/**
	 * 處理Rich Text中的圖檔並將Rich Text輸出成Html格式
	 * 
	 * @param richTextXml
	 *            String : 已組成XML型態之RichText欄位之值
	 * @return html String
	 */
	@SuppressWarnings("unused")
	protected String processRTF(String dxlName, Document domDoc, String schema,
			String richTextXml, String colName) throws Exception {
		String html = "";
		dxlName = dxlName.replaceAll("\\.dxl$", "");

		String formName = dxlName.split("_")[0];// dxlNameEx:FCLS106M01_FFFCFFFFFC02BF5F48257A5600352D8B
		String prNo = "";

		if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(schema)) {
			prNo = this.getItemValue(domDoc, "Project_No");
		} else {

			if (formName.equalsIgnoreCase(TextDefine.CLS_L140M01B_FORM_11501)
					|| formName
							.equalsIgnoreCase(TextDefine.CLS_L140M01B_FORM_71501)
					|| formName.equalsIgnoreCase("FCLS114M01")
					|| formName.equalsIgnoreCase("FCLS114M02")) {
				prNo = this.getItemValue(domDoc, "Project_No");
			} else {
				prNo = this.getItemValue(domDoc, "Private_No");
			}
		}
		String year = DXLUtil.getProjectNo(prNo, 0);

		if (StringUtils.isBlank(year)) {
			year = "2013";// default 2013 同DocFile.java
		}

		String unid = dxlName.split("_")[1];
		String tmp = "";
		String mainid = "";
		if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(schema)) {
		if (formName.equalsIgnoreCase("FLMS110M01")
				|| formName.equalsIgnoreCase("FLMS120M01")
				|| formName.equalsIgnoreCase("FLMS130M01")) {

			mainid = this.getItemValue(domDoc, "UnidDocID");

		} else if (formName.equalsIgnoreCase("FLMS140M01")
				|| formName.equalsIgnoreCase("FLMS740M01")) {
			String cntrDocId = this.getItemValue(domDoc, "WEBELOANMAINID");
			mainid = cntrDocId.isEmpty() ? unid : cntrDocId;
		}
		tmp = dxlName.replaceAll(unid, mainid);
		}else{
			tmp = dxlName;
		}
		

		String richTextContent = this.getRichTextContent(richTextXml, colName);
		if (StringUtils.isNotBlank(richTextContent)) {
			DXLTransformer transformer = new DXLTransformer();
			ByteArrayInputStream is = null;

			// 產生.gif檔
			richTextXml = genFileByBase64(tmp, year, richTextXml,
					TextDefine.ATTACH_GIF);

			is = new ByteArrayInputStream(
					richTextXml.getBytes(TextDefine.ENCODING_UTF8));
			transformer.setXmlInputStream(is);
			File dxl2html = new File(this.configData.getDC_ROOT()
					+ File.separator + "conf" + File.separator + "dxl2html.xsl");

			if (null == dxl2html) {
				this.logger
						.error("ParserAction在處理 processRTF時產生錯誤 -->【dxl2html.xsl】 is NotFound");
				return null;
			}
			transformer.setXslin(dxl2html.getPath());
			ByteArrayOutputStream baos = new ByteArrayOutputStream(
					richTextXml.length());

			transformer.setOutput(baos);
			transformer.doTransform();
			html = baos.toString().replaceAll("vertical-align: bottom;", "");
		}
		return html;
	}

	/**
	 * 取得 richText中的文字內容，為了判斷是否為空白的內容
	 * 
	 * @param richTextXml
	 * @param colName
	 * @return
	 * @throws Exception
	 */
	protected String getRichTextContent(String richTextXml, String colName)
			throws Exception {
		if (StringUtils.isBlank(richTextXml)) {
			return "";
		}
		StringBuffer sb = new StringBuffer();
		Document doc = this.getDomDoc(richTextXml);
		String strItem = ITEM_PATH + colName.toUpperCase() + "']//richtext";
		NodeIterator nl = XPathAPI.selectNodeIterator(doc, strItem);
		Node node;
		while ((node = nl.nextNode()) != null) {
			sb.append(Util.nullToSpace(node.getTextContent()));
		}
		return sb.toString();
	}

	/**
	 * 從已組成XML型態之RichText中判斷是否有圖檔,若有則產生gif圖檔 // * @param imagesPath String
	 * :存放images檔之路徑 Ex: dxlPath\\IMAGES
	 * 
	 * @param dxlName
	 *            String :.dxl的檔名
	 * @param rtColName
	 *            String: Rich Text欄位名稱
	 * @param richTextXml
	 *            String : 已組成XML型態之RichText欄位之值
	 * @param type
	 *            String : 圖檔類型
	 * @return richTextXml String
	 * @throws Exception
	 */
	// protected String genFileByBase64(String dxlName, String rtColName,
	// String richTextXml, String type) throws Exception {
	// int cnt = 1;
	// int idx1 = richTextXml.indexOf("<" + type);
	//
	// while (idx1 != -1) {
	// int idx2 = richTextXml.indexOf("</" + type + ">", idx1);
	// if (idx2 != -1) {
	// int idx3 = richTextXml.indexOf(">", idx1 + 1);
	// String base64String = richTextXml.substring(idx3 + 1, idx2);
	// String filename = dxlName + "_" + rtColName + "_"
	// + type.toUpperCase() + "_" + (cnt++) + "." + type;
	// String filepath = this.imagesPath + File.separator + filename;
	// Base64.decodeToFile(base64String, filepath);
	// richTextXml = richTextXml.substring(0, idx3 + 1) + filename
	// + richTextXml.substring(idx2);
	//
	// idx1 = richTextXml.indexOf("<" + type,
	// idx3 + filename.getBytes().length + 1);
	// }
	// }
	// return richTextXml;
	// }

	/**
	 * 從已組成XML型態之RichText中判斷是否有圖檔,若有則產生gif圖檔 // * @param imagesPath String
	 * :存放images檔之路徑 Ex: dxlPath\\IMAGES
	 * 
	 * @param richTextXml
	 *            String : 已組成XML型態之RichText欄位之值
	 * @param type
	 *            String : 圖檔類型
	 * @return richTextXml String
	 * @throws Exception
	 */
	protected String genFileByBase64(String dxlName, String year,
			String richTextXml, String type) throws Exception {
		int cnt = 1;
		int idx1 = richTextXml.indexOf("<" + type);

		while (idx1 != -1) {
			int idx2 = richTextXml.indexOf("</" + type + ">", idx1);
			if (idx2 != -1) {
				int idx3 = richTextXml.indexOf(">", idx1 + 1);
				String base64String = richTextXml.substring(idx3 + 1, idx2);

				String oid = Util.getOID();
				String filename = dxlName + "_" + year + "_" + (cnt++) + "_"
						+ oid + "." + type;

				String filepath = this.imagesPath + File.separator + filename;

				Base64.decodeToFile(base64String, filepath);
				richTextXml = richTextXml.substring(0, idx3 + 1) + oid
						+ richTextXml.substring(idx2);

				idx1 = richTextXml.indexOf("<" + type,
						idx3 + filename.getBytes().length + 1);
			}
		}
		return richTextXml;
	}

	/**
	 * 避免因"<break/>"此Tag,造成取回的Value被截斷
	 * 
	 * @param domDoc
	 * @param itemName
	 *            String:欲取得Value的Notes欄位名稱
	 * @return sb
	 */
	protected String getItemValueByMup(Document domDoc, String itemName)
			throws Exception {
		StringBuffer sb = new StringBuffer();
		for (String value : getItemValueByMupArray(domDoc, itemName)) {
			sb.append(value.replaceAll(TextDefine.SYMBOL_SEMICOLON, "；"));
			sb.append(TextDefine.SYMBOL_SEMICOLON);
		}
		return sb.toString().replaceAll(";$", "");
	}

	/**
	 * 將多重值取成陣列
	 * 
	 * @param domDoc
	 * @param itemName
	 * @return
	 * @throws Exception
	 */
	protected String[] getItemValueByMupArray(Document domDoc, String itemName)
			throws Exception {
		if (StringUtils.isBlank(itemName)) {
			return new String[] {};
		}
		ArrayList<String> lst = new ArrayList<String>();
		String strItem = this.ITEM_PATH + itemName.toUpperCase() + "']//text";
		NodeIterator nl = XPathAPI.selectNodeIterator(domDoc, strItem);
		Node node;
		while ((node = nl.nextNode()) != null) {
			lst.add(Util.nullToSpace(node.getTextContent()));
		}
		return lst.toArray(new String[] {});
	}

	/**
	 * 以"|"分隔其欄位值
	 * 
	 * @param domDoc
	 *            Document : 已轉換為DOM Document的dxl檔
	 * @param rtColName
	 *            :取值後需分隔的欄位名稱
	 * @return rtstr String
	 * @TODO 
	 *       自DXL取回的值，會有類似<textlist><text>111</text><text>7031</text></textlist>的值
	 *       ， 須改成111|7031，也就是個別取出後用|分隔
	 */
	protected String getTextList(Document domDoc, String rtColName)
			throws Exception {
		if (StringUtils.isBlank(rtColName)) {
			return "";
		}
		String strItem = this.ITEM_PATH + rtColName.toUpperCase() + "']//text";
		StringBuffer sb = new StringBuffer();
		NodeIterator nl = XPathAPI.selectNodeIterator(domDoc, strItem);
		for (Node child = nl.nextNode(); child != null; child = nl.nextNode()) {
			if (StringUtils.isNotBlank(child.getTextContent())) {
				sb.append(child.getTextContent()).append("|");
			}
		}
		return sb.toString().replaceAll("\\|$", "");
	}

	/**
	 * 
	 * @param domDoc
	 *            DOM Document:已轉為DOM Document的.dxl檔
	 * @param itemName
	 *            欲取得Value的Notes欄位名稱
	 * @return String
	 */
	protected String getItemValue(Document domDoc, String itemName)
			throws Exception {
		if (StringUtils.isBlank(itemName)) {
			return "";
		}
		String strItem = ITEM_PATH + itemName.toUpperCase() + "']";
		return readNodeValue(domDoc, strItem, itemName).replaceAll(
				TextDefine.SYMBOL_SEMICOLON, "；");// 後面的"；"是全形
	}

	private String readNodeValue(Document domDoc, String xpath, String strKey)
			throws Exception {
		String str = "";
		try {
			NodeIterator nl = XPathAPI.selectNodeIterator(domDoc, xpath);
			Node node;
			while ((node = nl.nextNode()) != null) {
				str = printNode(node, strKey);
			}
		} catch (Exception err) {
		}
		return Util.nullToSpace(str);
	}

	@SuppressWarnings("unused")
	private String printNode(Node node, String indent) throws Exception {
		switch (node.getNodeType()) {
		case Node.DOCUMENT_NODE:
			NodeList nodes = node.getChildNodes();
			if (nodes != null) {
				for (int i = 0; i < nodes.getLength(); i++) {
					return printNode(nodes.item(i), "");
				}
			}
			break;

		case Node.ELEMENT_NODE:
			// recurse on each child
			NodeList children = node.getChildNodes();
			if (children != null) {
				for (int i = 0; i < children.getLength(); i++) {
					if (children.item(i).toString().trim().length() > 0) {
						return printNode(children.item(i), indent);
					}
				}
			}
			break;

		case Node.TEXT_NODE:
			return node.getNodeValue();
		}
		return "";
	}
}
