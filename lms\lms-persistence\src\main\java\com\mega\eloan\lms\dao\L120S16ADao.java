/* 
 * L120S16ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S06A;
import com.mega.eloan.lms.model.L120S16A;

/** 主要申請敘作內容主檔 **/
public interface L120S16ADao extends IGenericDao<L120S16A> {

	L120S16A findByOid(String oid);

	List<L120S16A> findByMainId(String mainId);

	L120S16A findByUniqueKey(String mainId, String custId, String dupNo,
			String cntrNo);

	List<L120S16A> findByIndex01(String mainId, String custId, String dupNo,
			String cntrNo);

	List<L120S16A> findByIndex02(String custId, String dupNo);

	List<L120S16A> findByIndex03(String cntrNo);

	List<L120S16A> findByCntrNo(String CntrNo);

	List<L120S16A> findByCustIdDupId(String custId, String DupNo);

	int delModel(String mainId);

	L120S16A findByMainIdCustIdCnrNo(String mainId, String custId,
			String dupNo, String cntrNo);
}