package com.mega.eloan.lms.mfaloan.service;

import java.util.List;

import com.mega.eloan.lms.mfaloan.bean.ELF490;

/**
 * <pre>
 * 覆審資料檔
 * </pre>
 * 
 * @since 2013/3/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/7,EL08034,new
 *          </ul>
 */
public interface MisELF490Service {
	public List<ELF490> findByDataymBrno(String elf490_data_ym_beg,String elf490_data_ym_end,String elf490_br_no);
	public List<ELF490> findCustIdRecord(String elf490_data_ym_S, String elf490_data_ym_E, String custId, String dupNo);	
	public List<ELF490> findDataymBrnoCustIdDupNo(String elf490_data_ym, String elf490_br_no, String custId, String dupNo);
	public String findMaxDataYM();
}
