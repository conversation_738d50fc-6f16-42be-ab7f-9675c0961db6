package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.wicket.markup.html.form.Check;

import tw.com.iisi.cap.model.GenericBean;

/** 地上權分戶自動開戶 **/

public class ELF533 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 額度序號
	 *  <ul>
	 *  <li>007110500316 景美財訓所開發案-「華固新天地」住宅大樓建物使用權銷售融資</li>
	 *  <li>007110500319 景美財訓所開發案-「華固新天地」住商大樓建物使用權銷售融資</li>
	 *  </ul>
	 */
	@Size(max=12)
	@Column(name="ELF533_CNTRNO", length=12, columnDefinition="CHAR(12)", nullable=false,unique = true)
	private String elf533_cntrno;

	/** 額度案件序號 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="ELF533_SEQ_NO", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer elf533_seq_no;

	/** 對應放款帳號 **/
	@Size(max=14)
	@Column(name="ELF533_LOAN_NO", length=14, columnDefinition="CHAR(14)")
	private String elf533_loan_no;

	/** 科目代碼 **/
	@Size(max=3)
	@Column(name="ELF533_LNAP", length=3, columnDefinition="CHAR(3)")
	private String elf533_lnap;

	/** 利率代碼 **/
	@Size(max=2)
	@Column(name="ELF533_INT_CODE", length=2, columnDefinition="CHAR(02)")
	private String elf533_int_code;

	/** 利率/加減碼 **/
	@Digits(integer=2, fraction=6, groups = Check.class)
	@Column(name="ELF533_INT_SPRD", columnDefinition="DECIMAL(8,6)")
	private BigDecimal elf533_int_sprd;

	/** 利率方式 **/
	@Size(max=1)
	@Column(name="ELF533_INT_TYPE", length=1, columnDefinition="CHAR(01)")
	private String elf533_int_type;

	/** 利率變動方式 **/
	@Size(max=1)
	@Column(name="ELF533_INTCHG_TYPE", length=1, columnDefinition="CHAR(01)")
	private String elf533_intchg_type;

	/** 利率變動週期 **/
	@Size(max=1)
	@Column(name="ELF533_INTCHG_CYCL", length=1, columnDefinition="CHAR(01)")
	private String elf533_intchg_cycl;

	/** 資金來源 **/
	@Size(max=1)
	@Column(name="ELF533_FNDSRE", length=1, columnDefinition="CHAR(1)")
	private String elf533_fndsre;

	/** 資金來源小類 **/
	@Size(max=3)
	@Column(name="ELF533_FUND_TYPE2", length=3, columnDefinition="CHAR(03)")
	private String elf533_fund_type2;

	/** 用途別 **/
	@Size(max=1)
	@Column(name="ELF533_LNPURS", length=1, columnDefinition="CHAR(1)")
	private String elf533_lnpurs;

	/** 融資業務分類 **/
	@Size(max=1)
	@Column(name="ELF533_LN_PURPOSE", length=1, columnDefinition="CHAR(1)")
	private String elf533_ln_purpose;

	/** 貸款總期數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="ELF533_MONTHCNT", columnDefinition="DECIMAL(3,0)")
	private Integer elf533_monthcnt;

	/** 到期日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF533_DUE_DT", columnDefinition="DATE")
	private Date elf533_due_dt;

	/** 還款基準日 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="ELF533_RT_DD", columnDefinition="DECIMAL(2,0)")
	private Integer elf533_rt_dd;

	/** 下次還款日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF533_NT_RT_DT", columnDefinition="DATE")
	private Date elf533_nt_rt_dt;

	/** 扣帳基準日 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="ELF533_INT_RT_DD", columnDefinition="DECIMAL(2,0)")
	private Integer elf533_int_rt_dd;

	/** 下次扣帳日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF533_INT_INTRT_DT", columnDefinition="DATE")
	private Date elf533_int_intrt_dt;

	/** 償還方式{2:本息平均,3:本金平均} **/
	@Size(max=1)
	@Column(name="ELF533_AVGPAY", length=1, columnDefinition="CHAR(1)")
	private String elf533_avgpay;

	/** 每期攤還本金 **/
	@Digits(integer=13, fraction=2, groups = Check.class)
	@Column(name="ELF533_EHPAYCPT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf533_ehpaycpt;

	/** 期付金繳款週期{1:月,2:雙週} **/
	@Size(max=1)
	@Column(name="ELF533_INTRT_CYCL", length=1, columnDefinition="CHAR(1)")
	private String elf533_intrt_cycl;
	
	/** 自動進帳 **/
	@Size(max=1)
	@Column(name="ELF533_AUTORCT", length=1, columnDefinition="CHAR(1)")
	private String elf533_autorct;

	/** 進帳日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF533_RCTDATE", columnDefinition="DATE")
	private Date elf533_rctdate;

	/** 存款帳號(進帳帳號) **/
	@Size(max=14)
	@Column(name="ELF533_ACCNO", length=14, columnDefinition="CHAR(14)")
	private String elf533_accno;

	/** 放款幣別 **/
	@Size(max=3)
	@Column(name="ELF533_SWFT", length=3, columnDefinition="CHAR(03)")
	private String elf533_swft;

	/** 進帳金額 **/
	@Digits(integer=13, fraction=2, groups = Check.class)
	@Column(name="ELF533_RCTAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf533_rctamt;

	/** 自動扣帳 **/
	@Size(max=1)
	@Column(name="ELF533_AUTOPAY", length=1, columnDefinition="CHAR(1)")
	private String elf533_autopay;

	/** 扣帳帳號 **/
	@Size(max=14)
	@Column(name="ELF533_ATPAYNO", length=14, columnDefinition="CHAR(14)")
	private String elf533_atpayno;

	/** 客戶編號–分戶 **/
	@Size(max=10)
	@Column(name="ELF533_CUSTID_S", length=10, columnDefinition="CHAR(10)")
	private String elf533_custid_s;

	/** 重複序號–分戶 **/
	@Size(max=1)
	@Column(name="ELF533_DUPNO_S", length=1, columnDefinition="CHAR(1)")
	private String elf533_dupno_s;

	/** 利率代碼–分戶 **/
	@Size(max=2)
	@Column(name="ELF533_INT_CODE_S", length=2, columnDefinition="CHAR(02)")
	private String elf533_int_code_s;

	/** 利率/加減碼–分戶 **/
	@Digits(integer=2, fraction=6, groups = Check.class)
	@Column(name="ELF533_INT_SPRD_S", columnDefinition="DECIMAL(08,6)")
	private BigDecimal elf533_int_sprd_s;

	/** 利率方式–分戶 **/
	@Size(max=1)
	@Column(name="ELF533_INT_TYPE_S", length=1, columnDefinition="CHAR(01)")
	private String elf533_int_type_s;

	/** 利率變動方式–分戶 **/
	@Size(max=1)
	@Column(name="ELF533_INTCHG_TYPE_S", length=1, columnDefinition="CHAR(01)")
	private String elf533_intchg_type_s;

	/** 利率變動週期–分戶 **/
	@Size(max=1)
	@Column(name="ELF533_INTCHG_CYCL_S", length=1, columnDefinition="CHAR(01)")
	private String elf533_intchg_cycl_s;

	/** 貸款總期數–分戶 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="ELF533_MONTHCNT_S", columnDefinition="DECIMAL(3,0)")
	private Integer elf533_monthcnt_s;

	/** 到期日–分戶 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF533_DUE_DT_S", columnDefinition="DATE")
	private Date elf533_due_dt_s;

	/** 還款基準日–分戶 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="ELF533_RT_DD_S", columnDefinition="DECIMAL(2,0)")
	private Integer elf533_rt_dd_s;

	/** 下次還款日–分戶 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF533_NT_RT_DT_S", columnDefinition="DATE")
	private Date elf533_nt_rt_dt_s;

	/** 扣帳基準日-分戶 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="ELF533_INT_RT_DD_S", columnDefinition="DECIMAL(2,0)")
	private Integer elf533_int_rt_dd_s;

	/** 下次扣帳日–分戶 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF533_INT_INTRT_DT_S", columnDefinition="DATE")
	private Date elf533_int_intrt_dt_s;

	/** 償還方式–分戶 **/
	@Size(max=1)
	@Column(name="ELF533_AVGPAY_S", length=1, columnDefinition="CHAR(1)")
	private String elf533_avgpay_s;

	/** 每期攤還本金–分戶 **/
	@Digits(integer=13, fraction=2, groups = Check.class)
	@Column(name="ELF533_EHPAYCPT_S", columnDefinition="DECIMAL(15,2)")
	private BigDecimal elf533_ehpaycpt_s;

	/** 期付金繳款週期–分戶{1:月,2:雙週} **/
	@Size(max=1)
	@Column(name="ELF533_INTRT_CYCL_S", length=1, columnDefinition="CHAR(1)")
	private String elf533_intrt_cycl_s;
	
	/** 自動扣帳–分戶 **/
	@Size(max=1)
	@Column(name="ELF533_AUTOPAY_S", length=1, columnDefinition="CHAR(1)")
	private String elf533_autopay_s;

	/** 扣帳帳號–分戶 **/
	@Size(max=14)
	@Column(name="ELF533_ATPAYNO_S", length=14, columnDefinition="CHAR(14)")
	private String elf533_atpayno_s;

	/** 服務單位統編–分戶 **/
	@Size(max=11)
	@Column(name="ELF533_CMPID_S", length=11, columnDefinition="CHAR(11)")
	private String elf533_cmpid_s;

	/** 批號–分戶 **/
	@Size(max=4)
	@Column(name="ELF533_LOTNO_S", length=4, columnDefinition="CHAR(4)")
	private String elf533_lotno_s;

	/** 通訊地址指標–分戶 **/
	@Size(max=2)
	@Column(name="ELF533_ADR_PTR_S", length=2, columnDefinition="CHAR(2)")
	private String elf533_adr_ptr_s;

	/** 公司電話指標–分戶 **/
	@Size(max=2)
	@Column(name="ELF533_OPH_PTR_S", length=2, columnDefinition="CHAR(2)")
	private String elf533_oph_ptr_s;

	/** 住家電話指標–分戶 **/
	@Size(max=2)
	@Column(name="ELF533_HPH_PTR_S", length=2, columnDefinition="CHAR(2)")
	private String elf533_hph_ptr_s;

	/** 行動電話指標–分戶 **/
	@Size(max=2)
	@Column(name="ELF533_MPH_PTR_S", length=2, columnDefinition="CHAR(2)")
	private String elf533_mph_ptr_s;

	/** EMAIL指標–分戶 **/
	@Size(max=2)
	@Column(name="ELF533_EML_PTR_S", length=2, columnDefinition="CHAR(2)")
	private String elf533_eml_ptr_s;
	
	/** 備註–分戶 **/
	@Size(max=60)
	@Column(name="ELF533_MEMO_S", length=60, columnDefinition="CHAR(60)")
	private String elf533_memo_s;

	/** 扣帳失敗通知方式–分戶{' ':不通知 ,'1':簡訊通知, '2':EMAIL 通知} **/
	@Size(max=1)
	@Column(name="ELF533_UNPAY_FG_S", length=1, columnDefinition="CHAR(1)")
	private String elf533_unpay_fg_s;
	
	/** 櫃員代號 **/
	@Size(max=6)
	@Column(name="ELF533_TELLER", length=6, columnDefinition="CHAR(6)")
	private String elf533_teller;

	/** 主管代號 **/
	@Size(max=6)
	@Column(name="ELF533_SUPVNO", length=6, columnDefinition="CHAR(6)")
	private String elf533_supvno;

	/** E-Loan新增執行時間onLine **/
	@Column(name="ELF533_ELOANTIMES", columnDefinition="TIMESTAMP")
	private Timestamp elf533_eloantimes;
	
	/** 資料修改日期 **/
	@Column(name="ELF533_TMESTAMP", columnDefinition="TIMESTAMP")
	private Timestamp elf533_tmestamp;	

	/** A-Loan執行時間Batch **/
	@Column(name="ELF533_ALOANTIMES", columnDefinition="TIMESTAMP")
	private Timestamp elf533_aloantimes;

	/** 扣稅負擔值 **/
	@Digits(integer=4, fraction=3)
	@Column(name="ELF533_TAX_RATE", columnDefinition="DECIMAL(4,3)")
	private BigDecimal elf533_tax_rate;
	
	/** 寬限期_起 **/
	@Digits(integer=3, fraction=0)
	@Column(name="ELF533_ALLOW_BEG", columnDefinition="DECIMAL(3,0)")
	private Integer elf533_allow_beg;
	
	/** 寬限期_迄 **/
	@Digits(integer=3, fraction=0)
	@Column(name="ELF533_ALLOW_END", columnDefinition="DECIMAL(3,0)")
	private Integer elf533_allow_end;
	
	/** 取得額度序號 **/
	public String getElf533_cntrno() {
		return this.elf533_cntrno;
	}
	/** 設定額度序號 **/
	public void setElf533_cntrno(String value) {
		this.elf533_cntrno = value;
	}

	/** 取得額度案件序號 **/
	public Integer getElf533_seq_no() {
		return this.elf533_seq_no;
	}
	/** 設定額度案件序號 **/
	public void setElf533_seq_no(Integer value) {
		this.elf533_seq_no = value;
	}

	/** 取得對應放款帳號 **/
	public String getElf533_loan_no() {
		return this.elf533_loan_no;
	}
	/** 設定對應放款帳號 **/
	public void setElf533_loan_no(String value) {
		this.elf533_loan_no = value;
	}

	/** 取得科目代碼 **/
	public String getElf533_lnap() {
		return this.elf533_lnap;
	}
	/** 設定科目代碼 **/
	public void setElf533_lnap(String value) {
		this.elf533_lnap = value;
	}

	/** 取得利率代碼 **/
	public String getElf533_int_code() {
		return this.elf533_int_code;
	}
	/** 設定利率代碼 **/
	public void setElf533_int_code(String value) {
		this.elf533_int_code = value;
	}

	/** 取得利率/加減碼 **/
	public BigDecimal getElf533_int_sprd() {
		return this.elf533_int_sprd;
	}
	/** 設定利率/加減碼 **/
	public void setElf533_int_sprd(BigDecimal value) {
		this.elf533_int_sprd = value;
	}

	/** 取得利率方式 **/
	public String getElf533_int_type() {
		return this.elf533_int_type;
	}
	/** 設定利率方式 **/
	public void setElf533_int_type(String value) {
		this.elf533_int_type = value;
	}

	/** 取得利率變動方式 **/
	public String getElf533_intchg_type() {
		return this.elf533_intchg_type;
	}
	/** 設定利率變動方式 **/
	public void setElf533_intchg_type(String value) {
		this.elf533_intchg_type = value;
	}

	/** 取得利率變動週期 **/
	public String getElf533_intchg_cycl() {
		return this.elf533_intchg_cycl;
	}
	/** 設定利率變動週期 **/
	public void setElf533_intchg_cycl(String value) {
		this.elf533_intchg_cycl = value;
	}

	/** 取得資金來源 **/
	public String getElf533_fndsre() {
		return this.elf533_fndsre;
	}
	/** 設定資金來源 **/
	public void setElf533_fndsre(String value) {
		this.elf533_fndsre = value;
	}

	/** 取得資金來源小類 **/
	public String getElf533_fund_type2() {
		return this.elf533_fund_type2;
	}
	/** 設定資金來源小類 **/
	public void setElf533_fund_type2(String value) {
		this.elf533_fund_type2 = value;
	}

	/** 取得用途別 **/
	public String getElf533_lnpurs() {
		return this.elf533_lnpurs;
	}
	/** 設定用途別 **/
	public void setElf533_lnpurs(String value) {
		this.elf533_lnpurs = value;
	}

	/** 取得融資業務分類 **/
	public String getElf533_ln_purpose() {
		return this.elf533_ln_purpose;
	}
	/** 設定融資業務分類 **/
	public void setElf533_ln_purpose(String value) {
		this.elf533_ln_purpose = value;
	}

	/** 取得貸款總期數 **/
	public Integer getElf533_monthcnt() {
		return this.elf533_monthcnt;
	}
	/** 設定貸款總期數 **/
	public void setElf533_monthcnt(Integer value) {
		this.elf533_monthcnt = value;
	}

	/** 取得到期日 **/
	public Date getElf533_due_dt() {
		return this.elf533_due_dt;
	}
	/** 設定到期日 **/
	public void setElf533_due_dt(Date value) {
		this.elf533_due_dt = value;
	}

	/** 取得還款基準日 **/
	public Integer getElf533_rt_dd() {
		return this.elf533_rt_dd;
	}
	/** 設定還款基準日 **/
	public void setElf533_rt_dd(Integer value) {
		this.elf533_rt_dd = value;
	}

	/** 取得下次還款日 **/
	public Date getElf533_nt_rt_dt() {
		return this.elf533_nt_rt_dt;
	}
	/** 設定下次還款日 **/
	public void setElf533_nt_rt_dt(Date value) {
		this.elf533_nt_rt_dt = value;
	}

	/** 取得扣帳基準日 **/
	public Integer getElf533_int_rt_dd() {
		return this.elf533_int_rt_dd;
	}
	/** 設定扣帳基準日 **/
	public void setElf533_int_rt_dd(Integer value) {
		this.elf533_int_rt_dd = value;
	}

	/** 取得下次扣帳日 **/
	public Date getElf533_int_intrt_dt() {
		return this.elf533_int_intrt_dt;
	}
	/** 設定下次扣帳日 **/
	public void setElf533_int_intrt_dt(Date value) {
		this.elf533_int_intrt_dt = value;
	}

	/** 取得償還方式{2:本息平均,3:本金平均} **/
	public String getElf533_avgpay() {
		return this.elf533_avgpay;
	}
	/** 設定償還方式{2:本息平均,3:本金平均} **/
	public void setElf533_avgpay(String value) {
		this.elf533_avgpay = value;
	}

	/** 取得每期攤還本金 **/
	public BigDecimal getElf533_ehpaycpt() {
		return this.elf533_ehpaycpt;
	}
	/** 設定每期攤還本金 **/
	public void setElf533_ehpaycpt(BigDecimal value) {
		this.elf533_ehpaycpt = value;
	}

	/** 取得期付金繳款週期{1:月,2:雙週} **/
	public String getElf533_intrt_cycl() {
		return this.elf533_intrt_cycl;
	}
	/** 設定期付金繳款週期{1:月,2:雙週} **/
	public void setElf533_intrt_cycl(String value) {
		this.elf533_intrt_cycl = value;
	}
	
	/** 取得自動進帳 **/
	public String getElf533_autorct() {
		return this.elf533_autorct;
	}
	/** 設定自動進帳 **/
	public void setElf533_autorct(String value) {
		this.elf533_autorct = value;
	}

	/** 取得進帳日期 **/
	public Date getElf533_rctdate() {
		return this.elf533_rctdate;
	}
	/** 設定進帳日期 **/
	public void setElf533_rctdate(Date value) {
		this.elf533_rctdate = value;
	}

	/** 取得存款帳號(進帳帳號) **/
	public String getElf533_accno() {
		return this.elf533_accno;
	}
	/** 設定存款帳號(進帳帳號) **/
	public void setElf533_accno(String value) {
		this.elf533_accno = value;
	}

	/** 取得放款幣別 **/
	public String getElf533_swft() {
		return this.elf533_swft;
	}
	/** 設定放款幣別 **/
	public void setElf533_swft(String value) {
		this.elf533_swft = value;
	}

	/** 取得進帳金額 **/
	public BigDecimal getElf533_rctamt() {
		return this.elf533_rctamt;
	}
	/** 設定進帳金額 **/
	public void setElf533_rctamt(BigDecimal value) {
		this.elf533_rctamt = value;
	}

	/** 取得自動扣帳 **/
	public String getElf533_autopay() {
		return this.elf533_autopay;
	}
	/** 設定自動扣帳 **/
	public void setElf533_autopay(String value) {
		this.elf533_autopay = value;
	}

	/** 取得扣帳帳號 **/
	public String getElf533_atpayno() {
		return this.elf533_atpayno;
	}
	/** 設定扣帳帳號 **/
	public void setElf533_atpayno(String value) {
		this.elf533_atpayno = value;
	}

	/** 取得客戶編號–分戶 **/
	public String getElf533_custid_s() {
		return this.elf533_custid_s;
	}
	/** 設定客戶編號–分戶 **/
	public void setElf533_custid_s(String value) {
		this.elf533_custid_s = value;
	}

	/** 取得重複序號–分戶 **/
	public String getElf533_dupno_s() {
		return this.elf533_dupno_s;
	}
	/** 設定重複序號–分戶 **/
	public void setElf533_dupno_s(String value) {
		this.elf533_dupno_s = value;
	}

	/** 取得利率代碼–分戶 **/
	public String getElf533_int_code_s() {
		return this.elf533_int_code_s;
	}
	/** 設定利率代碼–分戶 **/
	public void setElf533_int_code_s(String value) {
		this.elf533_int_code_s = value;
	}

	/** 取得利率/加減碼–分戶 **/
	public BigDecimal getElf533_int_sprd_s() {
		return this.elf533_int_sprd_s;
	}
	/** 設定利率/加減碼–分戶 **/
	public void setElf533_int_sprd_s(BigDecimal value) {
		this.elf533_int_sprd_s = value;
	}

	/** 取得利率方式–分戶 **/
	public String getElf533_int_type_s() {
		return this.elf533_int_type_s;
	}
	/** 設定利率方式–分戶 **/
	public void setElf533_int_type_s(String value) {
		this.elf533_int_type_s = value;
	}

	/** 取得利率變動方式–分戶 **/
	public String getElf533_intchg_type_s() {
		return this.elf533_intchg_type_s;
	}
	/** 設定利率變動方式–分戶 **/
	public void setElf533_intchg_type_s(String value) {
		this.elf533_intchg_type_s = value;
	}

	/** 取得利率變動週期–分戶 **/
	public String getElf533_intchg_cycl_s() {
		return this.elf533_intchg_cycl_s;
	}
	/** 設定利率變動週期–分戶 **/
	public void setElf533_intchg_cycl_s(String value) {
		this.elf533_intchg_cycl_s = value;
	}

	/** 取得貸款總期數–分戶 **/
	public Integer getElf533_monthcnt_s() {
		return this.elf533_monthcnt_s;
	}
	/** 設定貸款總期數–分戶 **/
	public void setElf533_monthcnt_s(Integer value) {
		this.elf533_monthcnt_s = value;
	}

	/** 取得到期日–分戶 **/
	public Date getElf533_due_dt_s() {
		return this.elf533_due_dt_s;
	}
	/** 設定到期日–分戶 **/
	public void setElf533_due_dt_s(Date value) {
		this.elf533_due_dt_s = value;
	}

	/** 取得還款基準日–分戶 **/
	public Integer getElf533_rt_dd_s() {
		return this.elf533_rt_dd_s;
	}
	/** 設定還款基準日–分戶 **/
	public void setElf533_rt_dd_s(Integer value) {
		this.elf533_rt_dd_s = value;
	}

	/** 取得下次還款日–分戶 **/
	public Date getElf533_nt_rt_dt_s() {
		return this.elf533_nt_rt_dt_s;
	}
	/** 設定下次還款日–分戶 **/
	public void setElf533_nt_rt_dt_s(Date value) {
		this.elf533_nt_rt_dt_s = value;
	}

	/** 取得扣帳基準日-分戶 **/
	public Integer getElf533_int_rt_dd_s() {
		return this.elf533_int_rt_dd_s;
	}
	/** 設定扣帳基準日-分戶 **/
	public void setElf533_int_rt_dd_s(Integer value) {
		this.elf533_int_rt_dd_s = value;
	}

	/** 取得下次扣帳日–分戶 **/
	public Date getElf533_int_intrt_dt_s() {
		return this.elf533_int_intrt_dt_s;
	}
	/** 設定下次扣帳日–分戶 **/
	public void setElf533_int_intrt_dt_s(Date value) {
		this.elf533_int_intrt_dt_s = value;
	}

	/** 取得償還方式–分戶 **/
	public String getElf533_avgpay_s() {
		return this.elf533_avgpay_s;
	}
	/** 設定償還方式–分戶 **/
	public void setElf533_avgpay_s(String value) {
		this.elf533_avgpay_s = value;
	}

	/** 取得每期攤還本金–分戶 **/
	public BigDecimal getElf533_ehpaycpt_s() {
		return this.elf533_ehpaycpt_s;
	}
	/** 設定每期攤還本金–分戶 **/
	public void setElf533_ehpaycpt_s(BigDecimal value) {
		this.elf533_ehpaycpt_s = value;
	}

	/** 取得期付金繳款週期–分戶{1:月,2:雙週} **/
	public String getElf533_intrt_cycl_s() {
		return this.elf533_intrt_cycl_s;
	}
	/** 設定期付金繳款週期–分戶{1:月,2:雙週} **/
	public void setElf533_intrt_cycl_s(String value) {
		this.elf533_intrt_cycl_s = value;
	}
	
	/** 取得自動扣帳–分戶 **/
	public String getElf533_autopay_s() {
		return this.elf533_autopay_s;
	}
	/** 設定自動扣帳–分戶 **/
	public void setElf533_autopay_s(String value) {
		this.elf533_autopay_s = value;
	}

	/** 取得扣帳帳號–分戶 **/
	public String getElf533_atpayno_s() {
		return this.elf533_atpayno_s;
	}
	/** 設定扣帳帳號–分戶 **/
	public void setElf533_atpayno_s(String value) {
		this.elf533_atpayno_s = value;
	}

	/** 取得服務單位統編–分戶 **/
	public String getElf533_cmpid_s() {
		return this.elf533_cmpid_s;
	}
	/** 設定服務單位統編–分戶 **/
	public void setElf533_cmpid_s(String value) {
		this.elf533_cmpid_s = value;
	}

	/** 取得批號–分戶 **/
	public String getElf533_lotno_s() {
		return this.elf533_lotno_s;
	}
	/** 設定批號–分戶 **/
	public void setElf533_lotno_s(String value) {
		this.elf533_lotno_s = value;
	}

	/** 取得通訊地址指標–分戶 **/
	public String getElf533_adr_ptr_s() {
		return this.elf533_adr_ptr_s;
	}
	/** 設定通訊地址指標–分戶 **/
	public void setElf533_adr_ptr_s(String value) {
		this.elf533_adr_ptr_s = value;
	}

	/** 取得公司電話指標–分戶 **/
	public String getElf533_oph_ptr_s() {
		return this.elf533_oph_ptr_s;
	}
	/** 設定公司電話指標–分戶 **/
	public void setElf533_oph_ptr_s(String value) {
		this.elf533_oph_ptr_s = value;
	}

	/** 取得住家電話指標–分戶 **/
	public String getElf533_hph_ptr_s() {
		return this.elf533_hph_ptr_s;
	}
	/** 設定住家電話指標–分戶 **/
	public void setElf533_hph_ptr_s(String value) {
		this.elf533_hph_ptr_s = value;
	}

	/** 取得行動電話指標–分戶 **/
	public String getElf533_mph_ptr_s() {
		return this.elf533_mph_ptr_s;
	}
	/** 設定行動電話指標–分戶 **/
	public void setElf533_mph_ptr_s(String value) {
		this.elf533_mph_ptr_s = value;
	}

	/** 取得EMAIL指標–分戶 **/
	public String getElf533_eml_ptr_s() {
		return this.elf533_eml_ptr_s;
	}
	/** 設定EMAIL指標–分戶 **/
	public void setElf533_eml_ptr_s(String value) {
		this.elf533_eml_ptr_s = value;
	}
	
	/** 取得備註–分戶 **/
	public String getElf533_memo_s() {
		return this.elf533_memo_s;
	}
	/** 設定備註–分戶 **/
	public void setElf533_memo_s(String value) {
		this.elf533_memo_s = value;
	}

	/** 取得扣帳失敗通知方式–分戶{' ':不通知 ,'1':簡訊通知, '2':EMAIL 通知} **/
	public String getElf533_unpay_fg_s() {
		return this.elf533_unpay_fg_s;
	}
	/** 設定扣帳失敗通知方式–分戶{' ':不通知 ,'1':簡訊通知, '2':EMAIL 通知} **/
	public void setElf533_unpay_fg_s(String value) {
		this.elf533_unpay_fg_s = value;
	}

	/** 取得櫃員代號 **/
	public String getElf533_teller() {
		return this.elf533_teller;
	}
	/** 設定櫃員代號 **/
	public void setElf533_teller(String value) {
		this.elf533_teller = value;
	}

	/** 取得主管代號 **/
	public String getElf533_supvno() {
		return this.elf533_supvno;
	}
	/** 設定主管代號 **/
	public void setElf533_supvno(String value) {
		this.elf533_supvno = value;
	}

	/** 取得E-Loan新增執行時間onLine **/
	public Timestamp getElf533_eloantimes() {
		return this.elf533_eloantimes;
	}
	/** 設定E-Loan新增執行時間onLine **/
	public void setElf533_eloantimes(Timestamp value) {
		this.elf533_eloantimes = value;
	}
	
	/** 取得資料修改日期 **/
	public Timestamp getElf533_tmestamp() {
		return this.elf533_tmestamp;
	}
	/** 設定資料修改日期 **/
	public void setElf533_tmestamp(Timestamp value) {
		this.elf533_tmestamp = value;
	}

	/** 取得A-Loan執行時間Batch **/
	public Timestamp getElf533_aloantimes() {
		return this.elf533_aloantimes;
	}
	/** 設定A-Loan執行時間Batch **/
	public void setElf533_aloantimes(Timestamp value) {
		this.elf533_aloantimes = value;
	}
	
	/** 取得扣稅負擔值 **/
	public BigDecimal getElf533_tax_rate() {
		return elf533_tax_rate;
	}
	/** 設定扣稅負擔值 **/
	public void setElf533_tax_rate(BigDecimal elf533_tax_rate) {
		this.elf533_tax_rate = elf533_tax_rate;
	}
	
	/** 取得寬限期_起 **/
	public Integer getElf533_allow_beg() {
		return elf533_allow_beg;
	}
	/** 設定寬限期_起 **/
	public void setElf533_allow_beg(Integer elf533_allow_beg) {
		this.elf533_allow_beg = elf533_allow_beg;
	}
	
	/** 取得寬限期_迄 **/
	public Integer getElf533_allow_end() {
		return elf533_allow_end;
	}
	/** 設定寬限期_迄 **/
	public void setElf533_allow_end(Integer elf533_allow_end) {
		this.elf533_allow_end = elf533_allow_end;
	}
}
