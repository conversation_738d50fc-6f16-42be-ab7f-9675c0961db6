/* 
 * MISLN20.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.wicket.markup.html.form.Check;

import tw.com.iisi.cap.model.GenericBean;

/** MIS.MISLN20 **/
public class MISLN20 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/**
	 * 客戶編號
	 * <p/>
	 * NOT NULL
	 */
	@Size(max = 11)
	@Column(name = "LNF020_CUST_ID", length = 11, columnDefinition = "CHAR(11)")
	private String lnf020_cust_id;

	/**
	 * 額度序號
	 * <p/>
	 * NOT NULL
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 12)
	@Column(name = "LNF020_CONTRACT", length = 12, columnDefinition = "CHAR(12)", nullable = false)
	private String lnf020_contract;

	/**
	 * 出資總行代碼
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 3)
	@Column(name = "LNF020_BANK_CODE", length = 3, columnDefinition = "CHAR(03)", nullable = false)
	private String lnf020_bank_code;

	/**
	 * 出資分行代號
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 3)
	@Column(name = "LNF020_LN_BR_NO", length = 3, columnDefinition = "CHAR(03)", nullable = false)
	private String lnf020_ln_br_no;

	/**
	 * 額度控管種類
	 * <p/>
	 * NOT NULL
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 2)
	@Column(name = "LNF020_FACT_TYPE", length = 2, columnDefinition = "CHAR(02)", nullable = false)
	private String lnf020_fact_type;

	/**
	 * 合作外匯總額度序號
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 12)
	@Column(name = "LNF020_CONTRACT_M", length = 12, columnDefinition = "CHAR(12)")
	private String lnf020_contract_m;

	/**
	 * 幣別
	 * <p/>
	 * NOT NULL
	 */
	@Size(max = 3)
	@Column(name = "LNF020_SWFT", length = 3, columnDefinition = "CHAR(03)")
	private String lnf020_swft;

	/**
	 * 核准額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF020_FACT_AMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf020_fact_amt;

	/**
	 * 已用額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF020_USED_AMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf020_used_amt;

	/**
	 * OPEN ACCOUNT 已用額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF020_OA_USED_AMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf020_oa_used_amt;

	/**
	 * 可超用比率
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "LNF020_OVER_RATE", columnDefinition = "DECIMAL(02,0)")
	private Integer lnf020_over_rate;

	/**
	 * 最高可動用比率 (DEFAULT 100)
	 * <p/>
	 * NOT NULL WITH DEFAULT 100
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF020_UP_RATE", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf020_up_rate;

	/**
	 * 動用起日
	 * <p/>
	 * NOT NULL
	 */
	@Column(name = "LNF020_BEG_DATE", columnDefinition = "DATE   ")
	private Date lnf020_beg_date;

	/**
	 * 動用止日
	 * <p/>
	 * NOT NULL
	 */
	@Column(name = "LNF020_END_DATE", columnDefinition = "DATE   ")
	private Date lnf020_end_date;

	/**
	 * 承諾費註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_COMMIT", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_commit;

	/**
	 * 循環使用註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_REVOLVE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_revolve;

	/**
	 * 排除條款註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_EXCEPT", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_except;

	/**
	 * 銷戶日
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Column(name = "LNF020_CANCEL_DATE", columnDefinition = "DATE   ")
	private Date lnf020_cancel_date;

	/**
	 * 授權等級
	 * <p/>
	 * NOT NULL WITH DEFAULT '0'
	 */
	@Size(max = 1)
	@Column(name = "LNF020_AUTHO_LEVEL", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_autho_level;

	/**
	 * 共同借款人註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_COLN_FLAG", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_coln_flag;

	/**
	 * 免增提擔保品限額 ( 已不用 )
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF020_PLEDGE_LMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf020_pledge_lmt;

	/**
	 * 信保基金核准成數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "LNF020_IPFD_RATE", columnDefinition = "DECIMAL(05,2)")
	private BigDecimal lnf020_ipfd_rate;

	/**
	 * 額度共管註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_UNION_FLAG", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_union_flag;

	/**
	 * 額度組別編號計數器
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "LNF020_GROUP_CNT", columnDefinition = "DECIMAL(02,0)")
	private Integer lnf020_group_cnt;

	/**
	 * 額度共管序號計數器
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "LNF020_COCNTRL_CNT", columnDefinition = "DECIMAL(02,0)")
	private Integer lnf020_cocntrl_cnt;

	/** 中長期動用期限 **/
	@Column(name = "LNF020_DRAW_DUE_DT", columnDefinition = "DATE   ")
	private Date lnf020_draw_due_dt;

	/**
	 * 借方彙計數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF020_DR_TOT", columnDefinition = "DEC(15,2)")
	private BigDecimal lnf020_dr_tot;

	/**
	 * 貸方彙計數(折算台幣)
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF020_CR_TOT_TWD", columnDefinition = "DEC(15,2)")
	private BigDecimal lnf020_cr_tot_twd;

	/**
	 * 貸方彙計數(折算台幣)
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf020_cr_tot_twd() {
		return lnf020_cr_tot_twd;
	}

	/**
	 * 貸方彙計數(折算台幣)
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public void setLnf020_cr_tot_twd(BigDecimal lnf020_cr_tot_twd) {
		this.lnf020_cr_tot_twd = lnf020_cr_tot_twd;
	}

	/**
	 * 借方彙計數(折算台幣)
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf020_dr_tot_twd() {
		return lnf020_dr_tot_twd;
	}

	/**
	 * 借方彙計數(折算台幣)
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public void setLnf020_dr_tot_twd(BigDecimal lnf020_dr_tot_twd) {
		this.lnf020_dr_tot_twd = lnf020_dr_tot_twd;
	}

	/**
	 * 借方彙計數(折算台幣)
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF020_DR_TOT_TWD", columnDefinition = "DEC(15,2)")
	private BigDecimal lnf020_dr_tot_twd;

	/**
	 * 貸方彙計數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF020_CR_TOT", columnDefinition = "DEC(15,2)")
	private BigDecimal lnf020_cr_tot;

	/**
	 * 上年度借方彙計數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF020_DR_TOT_O", columnDefinition = "DEC(15,2)")
	private BigDecimal lnf020_dr_tot_o;

	/**
	 * 上年度貸方彙計數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF020_CR_TOT_O", columnDefinition = "DEC(15,2)")
	private BigDecimal lnf020_cr_tot_o;

	/**
	 * 單據寄發方式
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_NOTICE_TYPE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_notice_type;

	/**
	 * 通訊地址指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 */
	@Size(max = 2)
	@Column(name = "LNF020_ADR_PTR", length = 2, columnDefinition = "CHAR(02)")
	private String lnf020_adr_ptr;

	/**
	 * 公司電話指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 */
	@Size(max = 2)
	@Column(name = "LNF020_OPH_PTR", length = 2, columnDefinition = "CHAR(02)")
	private String lnf020_oph_ptr;

	/**
	 * 住家電話指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 */
	@Size(max = 2)
	@Column(name = "LNF020_HPH_PTR", length = 2, columnDefinition = "CHAR(02)")
	private String lnf020_hph_ptr;

	/**
	 * 行動電話指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 */
	@Size(max = 2)
	@Column(name = "LNF020_MPH_PTR", length = 2, columnDefinition = "CHAR(02)")
	private String lnf020_mph_ptr;

	/**
	 * 傳真電話指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 */
	@Size(max = 2)
	@Column(name = "LNF020_FAX_PTR", length = 2, columnDefinition = "CHAR(02)")
	private String lnf020_fax_ptr;

	/**
	 * 電子信箱指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 */
	@Size(max = 2)
	@Column(name = "LNF020_EML_PTR", length = 2, columnDefinition = "CHAR(02)")
	private String lnf020_eml_ptr;

	/**
	 * 檔案維護狀態
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_STATUS", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_status;

	/**
	 * 聯貸型態
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_SYND_TYPE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_synd_type;

	/**
	 * 國金部客戶編號
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 6)
	@Column(name = "LNF020_OBU_CUST_NO", length = 6, columnDefinition = "CHAR(06)")
	private String lnf020_obu_cust_no;

	/** 上次審核日 FOR E-LOAN **/
	@Column(name = "LNF020_LST_APV_DT", columnDefinition = "DATE   ")
	private Date lnf020_lst_apv_dt;

	/**
	 * 合作業務種類
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 2)
	@Column(name = "LNF020_LOAN_CLASS", length = 2, columnDefinition = "CHAR(02)")
	private String lnf020_loan_class;

	/**
	 * 動用期限代碼
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_DUE_CODE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_due_code;

	/**
	 * 動用期間值
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF020_DUE_MM", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf020_due_mm;

	/**
	 * 中長期授信期間代碼
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_DURATION_CD", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_duration_cd;

	/**
	 * 中長期授信期間
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF020_DURATION_MM", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf020_duration_mm;

	/** 中長期授信期間起日 **/
	@Column(name = "LNF020_DURATION_BG", columnDefinition = "DATE")
	private Date lnf020_duration_bg;

	/** 中長期授信期間迄日 **/
	@Column(name = "LNF020_DURATION_ED", columnDefinition = "DATE")
	private Date lnf020_duration_ed;

	/**
	 * 備註
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 60)
	@Column(name = "LNF020_MEMO", length = 60, columnDefinition = "CHAR(60)")
	private String lnf020_memo;

	/**
	 * 不計入同一關係人企業
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_IDENTITY_NO", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_identity_no;

	/**
	 * CONSIGNEE 非本行核准成數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF020_CONSIGNE_RT", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf020_consigne_rt;

	/**
	 * CONSIGNEE 非本行已用額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF020_CN_USED_AMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf020_cn_used_amt;

	/** 預約動用起日 **/
	@Column(name = "LNF020_BEG_DATE_N", columnDefinition = "DATE   ")
	private Date lnf020_beg_date_n;

	/** 預約動用止日 **/
	@Column(name = "LNF020_END_DATE_N", columnDefinition = "DATE   ")
	private Date lnf020_end_date_n;

	/**
	 * 資料寫入日期
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Column(name = "LNF020_CREATE_DT", columnDefinition = "DATE   ")
	private Date lnf020_create_dt;

	/**
	 * 關係人註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_RELATE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_relate;

	/**
	 * OA 動用購料成數
	 * <p/>
	 * NOT NULL WITH DEFAULT 70
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF020_OA_RATE", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf020_oa_rate;

	/**
	 * 線上融資註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_ONLOAN_FLAG", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_onloan_flag;

	/**
	 * 聯貸總額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 18, fraction = 0, groups = Check.class)
	@Column(name = "LNF020_UNIONAMT", columnDefinition = "DECIMAL(18)")
	private BigDecimal lnf020_unionamt;

	/**
	 * 本行參貸總額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 18, fraction = 0, groups = Check.class)
	@Column(name = "LNF020_SHAREAMT", columnDefinition = "DECIMAL(18)")
	private BigDecimal lnf020_shareamt;

	/**
	 * 報核方式
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_PERMITTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String lnf020_permittype;

	/**
	 * 是否隱名參貸
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_HIDEUNION", length = 1, columnDefinition = "CHAR(1)")
	private String lnf020_hideunion;

	/** 聯貸合約訂定日 **/
	@Column(name = "LNF020_SETDATE", columnDefinition = "DATE   ")
	private Date lnf020_setdate;

	/**
	 * 國內聯貸或國際聯貸
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_UNIONAREA", length = 1, columnDefinition = "CHAR(1)")
	private String lnf020_unionarea;

	/**
	 * 聯貸主辦（管理）行註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 3)
	@Column(name = "LNF020_UNIONROLE", length = 3, columnDefinition = "CHAR(3)")
	private String lnf020_unionrole;

	/**
	 * 契約名稱代碼
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_NAME_CODE", length = 1, columnDefinition = "CHAR(1)")
	private String lnf020_name_code;

	/**
	 * 風險歸屬國別
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 2)
	@Column(name = "LNF020_RISK_AREA", length = 2, columnDefinition = "CHAR(2)")
	private String lnf020_risk_area;

	/**
	 * 包含 OBU 額度計算維持率
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 14)
	@Column(name = "LNF020_CONTRACT_O", length = 14, columnDefinition = "CHAR(14)")
	private String lnf020_contract_o;

	/**
	 * 原核准額度幣別
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 3)
	@Column(name = "LNF020_SWFT_O", length = 3, columnDefinition = "CHAR(03)")
	private String lnf020_swft_o;

	/**
	 * 原核准額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF020_FACT_AMT_O", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf020_fact_amt_o;

	/**
	 * 在台無住所外國人註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_FRGN_NO_ADR", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_frgn_no_adr;

	/**
	 * 財部部特殊透支註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_OVERDRAW_FG", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_overdraw_fg;

	/**
	 * 聯貸信保註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF020_SYND_IPFD", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_synd_ipfd;

	/**
	 * 簽報書核准案號
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Size(max = 20)
	@Column(name = "LNF020_DOCUMENT_NO", length = 20, columnDefinition = "CHAR(20)")
	private String lnf020_document_no;

	/**
	 * 團貸戶額度序號 <br/>
	 * p.s 若誤建成「一般」，消金覆審要轉8-1 (1)做 L505-30 更改「團貸總戶額度序號」 (2)L504-81 更改「整批貸款設定」 
	 */
	@Column(name = "LNF020_GRP_CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String lnf020_grp_cntrno;
	
	/**
	 * 專案種類  <br/>
	 *  -01 愛臺建設   <br/>
	 *  -02 企業小頭家   <br/>
	 *  -03 外銷貸款優惠信用保證方案   <br/>
	 *  -05 新創產業   <br/>
	 *  -A6 地上權銷售建物使用權案件     <br/>
	 */
	@Size(max = 2)
	@Column(name = "LNF020_PROJ_CLASS", length = 2, columnDefinition = "CHAR(02)")
	private String lnf020_proj_class;
	
	/**
	 * 專案種類細項 <br/>
	 * 當 lnf020_proj_class=05新創產業 時， AA 綠能科技   <br/>
	 * 當 lnf020_proj_class=05新創產業 時， BB 亞洲矽谷  <br/>
	 * 當 lnf020_proj_class=05新創產業 時， CC 生技醫療  <br/>
	 * 當 lnf020_proj_class=05新創產業 時， DD 國防產業  <br/>
	 * 當 lnf020_proj_class=05新創產業 時， EE 智慧機械  <br/>
	 * 當 lnf020_proj_class=05新創產業 時， FF 新農業  <br/>
	 * 當 lnf020_proj_class=05新創產業 時， GG 循環經濟  <br/>
	 */
	@Size(max = 2)
	@Column(name = "LNF020_ITW_CODE", length = 2, columnDefinition = "CHAR(02)")
	private String lnf020_itw_code;
	
	/**
	 * 以基金贖回款為還款來源之授信
	 */
	@Size(max = 1)
	@Column(name = "LNF020_REPAYFUND", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_repayfund;
	
	/**
	 * 是否為供應鏈融資
	 */
	@Size(max = 1)
	@Column(name = "LNF020_IS_EFIN", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_is_efin;

	/**
	 * 利害關係人可做無擔保註記
	 */
	@Size(max = 1)
	@Column(name = "LNF020_UNSECUREFLA", length = 1, columnDefinition = "CHAR(01)")
	private String lnf020_unsecurefla;
	
	
	
	/**
	 * 取得客戶編號
	 * <p/>
	 * NOT NULL
	 */
	public String getLnf020_cust_id() {
		return this.lnf020_cust_id;
	}

	/**
	 * 設定客戶編號
	 * <p/>
	 * NOT NULL
	 **/
	public void setLnf020_cust_id(String value) {
		this.lnf020_cust_id = value;
	}

	/**
	 * 取得額度序號
	 * <p/>
	 * NOT NULL
	 */
	public String getLnf020_contract() {
		return this.lnf020_contract;
	}

	/**
	 * 設定額度序號
	 * <p/>
	 * NOT NULL
	 **/
	public void setLnf020_contract(String value) {
		this.lnf020_contract = value;
	}

	/**
	 * 取得出資總行代碼
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_bank_code() {
		return this.lnf020_bank_code;
	}

	/**
	 * 設定出資總行代碼
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_bank_code(String value) {
		this.lnf020_bank_code = value;
	}

	/**
	 * 取得出資分行代號
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_ln_br_no() {
		return this.lnf020_ln_br_no;
	}

	/**
	 * 設定出資分行代號
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_ln_br_no(String value) {
		this.lnf020_ln_br_no = value;
	}

	/**
	 * 取得額度控管種類
	 * <p/>
	 * NOT NULL
	 */
	public String getLnf020_fact_type() {
		return this.lnf020_fact_type;
	}

	/**
	 * 設定額度控管種類
	 * <p/>
	 * NOT NULL
	 **/
	public void setLnf020_fact_type(String value) {
		this.lnf020_fact_type = value;
	}

	/**
	 * 取得合作外匯總額度序號
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_contract_m() {
		return this.lnf020_contract_m;
	}

	/**
	 * 設定合作外匯總額度序號
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_contract_m(String value) {
		this.lnf020_contract_m = value;
	}

	/**
	 * 取得幣別
	 * <p/>
	 * NOT NULL
	 */
	public String getLnf020_swft() {
		return this.lnf020_swft;
	}

	/**
	 * 設定幣別
	 * <p/>
	 * NOT NULL
	 **/
	public void setLnf020_swft(String value) {
		this.lnf020_swft = value;
	}

	/**
	 * 取得核准額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf020_fact_amt() {
		return this.lnf020_fact_amt;
	}

	/**
	 * 設定核准額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_fact_amt(BigDecimal value) {
		this.lnf020_fact_amt = value;
	}

	/**
	 * 取得已用額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf020_used_amt() {
		return this.lnf020_used_amt;
	}

	/**
	 * 設定已用額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_used_amt(BigDecimal value) {
		this.lnf020_used_amt = value;
	}

	/**
	 * 取得OPEN ACCOUNT 已用額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf020_oa_used_amt() {
		return this.lnf020_oa_used_amt;
	}

	/**
	 * 設定OPEN ACCOUNT 已用額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_oa_used_amt(BigDecimal value) {
		this.lnf020_oa_used_amt = value;
	}

	/**
	 * 取得可超用比率
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public Integer getLnf020_over_rate() {
		return this.lnf020_over_rate;
	}

	/**
	 * 設定可超用比率
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_over_rate(Integer value) {
		this.lnf020_over_rate = value;
	}

	/**
	 * 取得最高可動用比率 (DEFAULT 100)
	 * <p/>
	 * NOT NULL WITH DEFAULT 100
	 */
	public Integer getLnf020_up_rate() {
		return this.lnf020_up_rate;
	}

	/**
	 * 設定最高可動用比率 (DEFAULT 100)
	 * <p/>
	 * NOT NULL WITH DEFAULT 100
	 **/
	public void setLnf020_up_rate(Integer value) {
		this.lnf020_up_rate = value;
	}

	/**
	 * 取得動用起日
	 * <p/>
	 * NOT NULL
	 */
	public Date getLnf020_beg_date() {
		return this.lnf020_beg_date;
	}

	/**
	 * 設定動用起日
	 * <p/>
	 * NOT NULL
	 **/
	public void setLnf020_beg_date(Date value) {
		this.lnf020_beg_date = value;
	}

	/**
	 * 取得動用止日
	 * <p/>
	 * NOT NULL
	 */
	public Date getLnf020_end_date() {
		return this.lnf020_end_date;
	}

	/**
	 * 設定動用止日
	 * <p/>
	 * NOT NULL
	 **/
	public void setLnf020_end_date(Date value) {
		this.lnf020_end_date = value;
	}

	/**
	 * 取得承諾費註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_commit() {
		return this.lnf020_commit;
	}

	/**
	 * 設定承諾費註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_commit(String value) {
		this.lnf020_commit = value;
	}

	/**
	 * 取得循環使用註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_revolve() {
		return this.lnf020_revolve;
	}

	/**
	 * 設定循環使用註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_revolve(String value) {
		this.lnf020_revolve = value;
	}

	/**
	 * 取得排除條款註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_except() {
		return this.lnf020_except;
	}

	/**
	 * 設定排除條款註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_except(String value) {
		this.lnf020_except = value;
	}

	/**
	 * 取得銷戶日
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public Date getLnf020_cancel_date() {
		return this.lnf020_cancel_date;
	}

	/**
	 * 設定銷戶日
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_cancel_date(Date value) {
		this.lnf020_cancel_date = value;
	}

	/**
	 * 取得授權等級
	 * <p/>
	 * NOT NULL WITH DEFAULT '0'
	 */
	public String getLnf020_autho_level() {
		return this.lnf020_autho_level;
	}

	/**
	 * 設定授權等級
	 * <p/>
	 * NOT NULL WITH DEFAULT '0'
	 **/
	public void setLnf020_autho_level(String value) {
		this.lnf020_autho_level = value;
	}

	/**
	 * 取得共同借款人註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_coln_flag() {
		return this.lnf020_coln_flag;
	}

	/**
	 * 設定共同借款人註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_coln_flag(String value) {
		this.lnf020_coln_flag = value;
	}

	/**
	 * 取得免增提擔保品限額 ( 已不用 )
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf020_pledge_lmt() {
		return this.lnf020_pledge_lmt;
	}

	/**
	 * 設定免增提擔保品限額 ( 已不用 )
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_pledge_lmt(BigDecimal value) {
		this.lnf020_pledge_lmt = value;
	}

	/**
	 * 取得信保基金核准成數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf020_ipfd_rate() {
		return this.lnf020_ipfd_rate;
	}

	/**
	 * 設定信保基金核准成數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_ipfd_rate(BigDecimal value) {
		this.lnf020_ipfd_rate = value;
	}

	/**
	 * 取得額度共管註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_union_flag() {
		return this.lnf020_union_flag;
	}

	/**
	 * 設定額度共管註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_union_flag(String value) {
		this.lnf020_union_flag = value;
	}

	/**
	 * 取得額度組別編號計數器
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public Integer getLnf020_group_cnt() {
		return this.lnf020_group_cnt;
	}

	/**
	 * 設定額度組別編號計數器
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_group_cnt(Integer value) {
		this.lnf020_group_cnt = value;
	}

	/**
	 * 取得額度共管序號計數器
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public Integer getLnf020_cocntrl_cnt() {
		return this.lnf020_cocntrl_cnt;
	}

	/**
	 * 設定額度共管序號計數器
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_cocntrl_cnt(Integer value) {
		this.lnf020_cocntrl_cnt = value;
	}

	/** 取得中長期動用期限 **/
	public Date getLnf020_draw_due_dt() {
		return this.lnf020_draw_due_dt;
	}

	/** 設定中長期動用期限 **/
	public void setLnf020_draw_due_dt(Date value) {
		this.lnf020_draw_due_dt = value;
	}

	/**
	 * 取得借方彙計數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf020_dr_tot() {
		return this.lnf020_dr_tot;
	}

	/**
	 * 設定借方彙計數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_dr_tot(BigDecimal value) {
		this.lnf020_dr_tot = value;
	}

	/**
	 * 取得貸方彙計數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf020_cr_tot() {
		return this.lnf020_cr_tot;
	}

	/**
	 * 設定貸方彙計數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_cr_tot(BigDecimal value) {
		this.lnf020_cr_tot = value;
	}

	/**
	 * 取得上年度借方彙計數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf020_dr_tot_o() {
		return this.lnf020_dr_tot_o;
	}

	/**
	 * 設定上年度借方彙計數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_dr_tot_o(BigDecimal value) {
		this.lnf020_dr_tot_o = value;
	}

	/**
	 * 取得上年度貸方彙計數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf020_cr_tot_o() {
		return this.lnf020_cr_tot_o;
	}

	/**
	 * 設定上年度貸方彙計數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_cr_tot_o(BigDecimal value) {
		this.lnf020_cr_tot_o = value;
	}

	/**
	 * 取得單據寄發方式
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_notice_type() {
		return this.lnf020_notice_type;
	}

	/**
	 * 設定單據寄發方式
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_notice_type(String value) {
		this.lnf020_notice_type = value;
	}

	/**
	 * 取得通訊地址指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 */
	public String getLnf020_adr_ptr() {
		return this.lnf020_adr_ptr;
	}

	/**
	 * 設定通訊地址指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 **/
	public void setLnf020_adr_ptr(String value) {
		this.lnf020_adr_ptr = value;
	}

	/**
	 * 取得公司電話指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 */
	public String getLnf020_oph_ptr() {
		return this.lnf020_oph_ptr;
	}

	/**
	 * 設定公司電話指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 **/
	public void setLnf020_oph_ptr(String value) {
		this.lnf020_oph_ptr = value;
	}

	/**
	 * 取得住家電話指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 */
	public String getLnf020_hph_ptr() {
		return this.lnf020_hph_ptr;
	}

	/**
	 * 設定住家電話指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 **/
	public void setLnf020_hph_ptr(String value) {
		this.lnf020_hph_ptr = value;
	}

	/**
	 * 取得行動電話指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 */
	public String getLnf020_mph_ptr() {
		return this.lnf020_mph_ptr;
	}

	/**
	 * 設定行動電話指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 **/
	public void setLnf020_mph_ptr(String value) {
		this.lnf020_mph_ptr = value;
	}

	/**
	 * 取得傳真電話指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 */
	public String getLnf020_fax_ptr() {
		return this.lnf020_fax_ptr;
	}

	/**
	 * 設定傳真電話指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 **/
	public void setLnf020_fax_ptr(String value) {
		this.lnf020_fax_ptr = value;
	}

	/**
	 * 取得電子信箱指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 */
	public String getLnf020_eml_ptr() {
		return this.lnf020_eml_ptr;
	}

	/**
	 * 設定電子信箱指標
	 * <p/>
	 * NOT NULL WITH DEFAULT '00'
	 **/
	public void setLnf020_eml_ptr(String value) {
		this.lnf020_eml_ptr = value;
	}

	/**
	 * 取得檔案維護狀態
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_status() {
		return this.lnf020_status;
	}

	/**
	 * 設定檔案維護狀態
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_status(String value) {
		this.lnf020_status = value;
	}

	/**
	 * 取得聯貸型態
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_synd_type() {
		return this.lnf020_synd_type;
	}

	/**
	 * 設定聯貸型態
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_synd_type(String value) {
		this.lnf020_synd_type = value;
	}

	/**
	 * 取得國金部客戶編號
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_obu_cust_no() {
		return this.lnf020_obu_cust_no;
	}

	/**
	 * 設定國金部客戶編號
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_obu_cust_no(String value) {
		this.lnf020_obu_cust_no = value;
	}

	/** 取得上次審核日 FOR E-LOAN **/
	public Date getLnf020_lst_apv_dt() {
		return this.lnf020_lst_apv_dt;
	}

	/** 設定上次審核日 FOR E-LOAN **/
	public void setLnf020_lst_apv_dt(Date value) {
		this.lnf020_lst_apv_dt = value;
	}

	/**
	 * 取得合作業務種類
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_loan_class() {
		return this.lnf020_loan_class;
	}

	/**
	 * 設定合作業務種類
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_loan_class(String value) {
		this.lnf020_loan_class = value;
	}

	/**
	 * 取得動用期限代碼
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_due_code() {
		return this.lnf020_due_code;
	}

	/**
	 * 設定動用期限代碼
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_due_code(String value) {
		this.lnf020_due_code = value;
	}

	/**
	 * 取得動用期間值
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public Integer getLnf020_due_mm() {
		return this.lnf020_due_mm;
	}

	/**
	 * 設定動用期間值
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_due_mm(Integer value) {
		this.lnf020_due_mm = value;
	}

	/**
	 * 取得中長期授信期間代碼
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_duration_cd() {
		return this.lnf020_duration_cd;
	}

	/**
	 * 設定中長期授信期間代碼
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_duration_cd(String value) {
		this.lnf020_duration_cd = value;
	}

	/**
	 * 取得中長期授信期間
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public Integer getLnf020_duration_mm() {
		return this.lnf020_duration_mm;
	}

	/**
	 * 設定中長期授信期間
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_duration_mm(Integer value) {
		this.lnf020_duration_mm = value;
	}

	/** 取得中長期授信期間起日 **/
	public Date getLnf020_duration_bg() {
		return this.lnf020_duration_bg;
	}

	/** 設定中長期授信期間起日 **/
	public void setLnf020_duration_bg(Date value) {
		this.lnf020_duration_bg = value;
	}

	/** 取得中長期授信期間迄日 **/
	public Date getLnf020_duration_ed() {
		return this.lnf020_duration_ed;
	}

	/** 設定中長期授信期間迄日 **/
	public void setLnf020_duration_ed(Date value) {
		this.lnf020_duration_ed = value;
	}

	/**
	 * 取得備註
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_memo() {
		return this.lnf020_memo;
	}

	/**
	 * 設定備註
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_memo(String value) {
		this.lnf020_memo = value;
	}

	/**
	 * 取得不計入同一關係人企業
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_identity_no() {
		return this.lnf020_identity_no;
	}

	/**
	 * 設定不計入同一關係人企業
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_identity_no(String value) {
		this.lnf020_identity_no = value;
	}

	/**
	 * 取得CONSIGNEE 非本行核准成數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public Integer getLnf020_consigne_rt() {
		return this.lnf020_consigne_rt;
	}

	/**
	 * 設定CONSIGNEE 非本行核准成數
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_consigne_rt(Integer value) {
		this.lnf020_consigne_rt = value;
	}

	/**
	 * 取得CONSIGNEE 非本行已用額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf020_cn_used_amt() {
		return this.lnf020_cn_used_amt;
	}

	/**
	 * 設定CONSIGNEE 非本行已用額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_cn_used_amt(BigDecimal value) {
		this.lnf020_cn_used_amt = value;
	}

	/** 取得預約動用起日 **/
	public Date getLnf020_beg_date_n() {
		return this.lnf020_beg_date_n;
	}

	/** 設定預約動用起日 **/
	public void setLnf020_beg_date_n(Date value) {
		this.lnf020_beg_date_n = value;
	}

	/** 取得預約動用止日 **/
	public Date getLnf020_end_date_n() {
		return this.lnf020_end_date_n;
	}

	/** 設定預約動用止日 **/
	public void setLnf020_end_date_n(Date value) {
		this.lnf020_end_date_n = value;
	}

	/**
	 * 取得資料寫入日期
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public Date getLnf020_create_dt() {
		return this.lnf020_create_dt;
	}

	/**
	 * 設定資料寫入日期
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_create_dt(Date value) {
		this.lnf020_create_dt = value;
	}

	/**
	 * 取得關係人註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_relate() {
		return this.lnf020_relate;
	}

	/**
	 * 設定關係人註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_relate(String value) {
		this.lnf020_relate = value;
	}

	/**
	 * 取得OA 動用購料成數
	 * <p/>
	 * NOT NULL WITH DEFAULT 70
	 */
	public Integer getLnf020_oa_rate() {
		return this.lnf020_oa_rate;
	}

	/**
	 * 設定OA 動用購料成數
	 * <p/>
	 * NOT NULL WITH DEFAULT 70
	 **/
	public void setLnf020_oa_rate(Integer value) {
		this.lnf020_oa_rate = value;
	}

	/**
	 * 取得線上融資註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_onloan_flag() {
		return this.lnf020_onloan_flag;
	}

	/**
	 * 設定線上融資註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_onloan_flag(String value) {
		this.lnf020_onloan_flag = value;
	}

	/**
	 * 取得聯貸總額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf020_unionamt() {
		return this.lnf020_unionamt;
	}

	/**
	 * 設定聯貸總額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_unionamt(BigDecimal value) {
		this.lnf020_unionamt = value;
	}

	/**
	 * 取得本行參貸總額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf020_shareamt() {
		return this.lnf020_shareamt;
	}

	/**
	 * 設定本行參貸總額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_shareamt(BigDecimal value) {
		this.lnf020_shareamt = value;
	}

	/**
	 * 取得報核方式
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_permittype() {
		return this.lnf020_permittype;
	}

	/**
	 * 設定報核方式
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_permittype(String value) {
		this.lnf020_permittype = value;
	}

	/**
	 * 取得是否隱名參貸
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_hideunion() {
		return this.lnf020_hideunion;
	}

	/**
	 * 設定是否隱名參貸
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_hideunion(String value) {
		this.lnf020_hideunion = value;
	}

	/** 取得聯貸合約訂定日 **/
	public Date getLnf020_setdate() {
		return this.lnf020_setdate;
	}

	/** 設定聯貸合約訂定日 **/
	public void setLnf020_setdate(Date value) {
		this.lnf020_setdate = value;
	}

	/**
	 * 取得國內聯貸或國際聯貸
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_unionarea() {
		return this.lnf020_unionarea;
	}

	/**
	 * 設定國內聯貸或國際聯貸
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_unionarea(String value) {
		this.lnf020_unionarea = value;
	}

	/**
	 * 取得聯貸主辦（管理）行註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_unionrole() {
		return this.lnf020_unionrole;
	}

	/**
	 * 設定聯貸主辦（管理）行註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_unionrole(String value) {
		this.lnf020_unionrole = value;
	}

	/**
	 * 取得契約名稱代碼
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_name_code() {
		return this.lnf020_name_code;
	}

	/**
	 * 設定契約名稱代碼
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_name_code(String value) {
		this.lnf020_name_code = value;
	}

	/**
	 * 取得風險歸屬國別
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_risk_area() {
		return this.lnf020_risk_area;
	}

	/**
	 * 設定風險歸屬國別
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_risk_area(String value) {
		this.lnf020_risk_area = value;
	}

	/**
	 * 取得包含 OBU 額度計算維持率
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_contract_o() {
		return this.lnf020_contract_o;
	}

	/**
	 * 設定包含 OBU 額度計算維持率
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_contract_o(String value) {
		this.lnf020_contract_o = value;
	}

	/**
	 * 取得原核准額度幣別
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_swft_o() {
		return this.lnf020_swft_o;
	}

	/**
	 * 設定原核准額度幣別
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_swft_o(String value) {
		this.lnf020_swft_o = value;
	}

	/**
	 * 取得原核准額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf020_fact_amt_o() {
		return this.lnf020_fact_amt_o;
	}

	/**
	 * 設定原核准額度
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_fact_amt_o(BigDecimal value) {
		this.lnf020_fact_amt_o = value;
	}

	/**
	 * 取得在台無住所外國人註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_frgn_no_adr() {
		return this.lnf020_frgn_no_adr;
	}

	/**
	 * 設定在台無住所外國人註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_frgn_no_adr(String value) {
		this.lnf020_frgn_no_adr = value;
	}

	/**
	 * 取得財部部特殊透支註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_overdraw_fg() {
		return this.lnf020_overdraw_fg;
	}

	/**
	 * 設定財部部特殊透支註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_overdraw_fg(String value) {
		this.lnf020_overdraw_fg = value;
	}

	/**
	 * 取得聯貸信保註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_synd_ipfd() {
		return this.lnf020_synd_ipfd;
	}

	/**
	 * 設定聯貸信保註記
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_synd_ipfd(String value) {
		this.lnf020_synd_ipfd = value;
	}

	/**
	 * 取得簽報書核准案號
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf020_document_no() {
		return this.lnf020_document_no;
	}

	/**
	 * 設定簽報書核准案號
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf020_document_no(String value) {
		this.lnf020_document_no = value;
	}

	/** 取得團貸戶額度序號 **/
	public String getLnf020_grp_cntrno() {
		return lnf020_grp_cntrno;
	}

	/** 設定團貸戶額度序號 **/
	public void setLnf020_grp_cntrno(String lnf020_grp_cntrno) {
		this.lnf020_grp_cntrno = lnf020_grp_cntrno;
	}

	/** 取得專案種類 **/
	public String getLnf020_proj_class() {
		return this.lnf020_proj_class;
	}

	/** 設定專案種類 **/
	public void setLnf020_proj_class(String value) {
		this.lnf020_proj_class = value;
	}
	
	/** 取得專案種類細項 **/
	public String getLnf020_itw_code() {
		return this.lnf020_itw_code;
	}

	/** 設定專案種類細項 **/
	public void setLnf020_itw_code(String value) {
		this.lnf020_itw_code = value;
	}	
	
	/** 取得以基金贖回款為還款來源之授信 **/
	public String getLnf020_repayfund() {
		return this.lnf020_repayfund;
	}

	/** 設定以基金贖回款為還款來源之授信 **/
	public void setLnf020_repayfund(String value) {
		this.lnf020_repayfund = value;
	}

	/** 取得是否為供應鏈融資 **/
	public String getLnf020_is_efin() {
		return this.lnf020_is_efin;
	}

	/** 設定是否為供應鏈融資 **/
	public void setLnf020_is_efin(String value) {
		this.lnf020_is_efin = value;
	}
	
	/** 取得利害關係人可做無擔保註記 **/
	public String getLnf020_unsecurefla() {
		return this.lnf020_unsecurefla;
	}

	/** 設定利害關係人可做無擔保註記 **/
	public void setLnf020_unsecurefla(String value) {
		this.lnf020_unsecurefla = value;
	}
}
