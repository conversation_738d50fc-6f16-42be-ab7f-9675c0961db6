/* 
 * C102M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C102M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C102M01A;

/** 購置房屋擔保放款風險權數檢核表 **/
@Repository
public class C102M01ADaoImpl extends LMSJpaDao<C102M01A, String>
	implements C102M01ADao {

	@Override
	public C102M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C102M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C102M01A> list = createQuery(search).getResultList();
		return list;
	}
	@Override
	public C102M01A findByMainId2(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}
	@Override
	public List<C102M01A> findByIndex01(String mainId, String custId, String dupNo, String cntrNo, String aLoanAC, Date aLoanDate, String aLoanPurpose, String rskFlag){
		ISearch search = createSearchTemplete();
		List<C102M01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		if (aLoanAC != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "aLoanAC", aLoanAC);
		if (aLoanDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "aLoanDate", aLoanDate);
		if (aLoanPurpose != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "aLoanPurpose", aLoanPurpose);
		if (rskFlag != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rskFlag", rskFlag);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C102M01A> findByMainIds(String[] mainIds) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "mainId", mainIds);
		List<C102M01A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C102M01A findByIndex02(String custId, String dupNo, String cntrNo, String aLoanAC){
		ISearch search = createSearchTemplete();
				
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
//		if (aLoanAC != null)
//			search.addSearchModeParameters(SearchMode.EQUALS, "aLoanAC", aLoanAC);
		
		search.addSearchModeParameters(SearchMode.IS_NOT_NULL, "updateTime", null);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		
		search.addOrderBy("updateTime");
		
			return findUniqueOrNone(search);
	}
}