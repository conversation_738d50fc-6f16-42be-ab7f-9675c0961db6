
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C101S01UDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C101S01U;

/** 個金EJ標準查詢檔 **/
@Repository
public class C101S01UDaoImpl extends LMSJpaDao<C101S01U, String>
	implements C101S01UDao {

	@Override
	public C101S01U findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C101S01U> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C101S01U> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C101S01U> findByMainIdCustIdDupNo(String mainId, String custId, String dupNo){
		
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		List<C101S01U> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C101S01U> findByMainIdCustIdDupNoTxid(String mainId, String custId, String dupNo, String txid){
		
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "txid", txid);
		search.addOrderBy("createTime", true);
		List<C101S01U> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C101S01U> findByMainIdTxidSendTimeBefore(String mainId, String[] txid_arr, String sendTimeCmp){		
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if(txid_arr!=null&&txid_arr.length>0){
			search.addSearchModeParameters(SearchMode.IN, "txid", txid_arr);
		}
		search.addSearchModeParameters(SearchMode.IS_NOT_NULL, "sendTime", "");
		if (sendTimeCmp != null)
		search.addSearchModeParameters(SearchMode.LESS_THAN, "sendTime", sendTimeCmp);
		List<C101S01U> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C101S01U.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}
	
	@Override
	public C101S01U findLatestByCustIdDupNoTxid(String custId, String dupNo, String txid){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "txid", txid);
		search.addOrderBy("createTime", true);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<C101S01U> findByMainIdTxid(String mainId, String txid){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "txid", txid);
		List<C101S01U> list = createQuery(search).getResultList();
		return list;
	}
}