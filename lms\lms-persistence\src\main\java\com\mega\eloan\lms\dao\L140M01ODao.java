/* 
 * L140M01ODao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01O;

/** 擔保品資料明細檔 **/
public interface L140M01ODao extends IGenericDao<L140M01O> {

	L140M01O findByOid(String oid);

	List<L140M01O> findByOids(String[] oids);

	List<L140M01O> findByMainId(String mainId);

	List<L140M01O> findByMainIdOrderByCcollTyp1(String mainId);

	L140M01O findByUniqueKey(String mainId, Integer seqNo);

	List<L140M01O> findByIndex01(String mainId, Integer seqNo);

	List<L140M01O> findByIndex02(String mainId, String collNo, String custId);

	List<L140M01O> findByCustIdDupId(String custId, String DupNo);

	List<L140M01O> findByMainIdOrderForLgd(String mainId);
}