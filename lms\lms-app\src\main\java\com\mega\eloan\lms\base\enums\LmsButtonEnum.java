/* 
 * CreditButtonEnum.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.enums;

import com.mega.eloan.common.html.EloanPageFragment;

public enum LmsButtonEnum implements EloanPageFragment {
	/** 篩選 */
	Filter("F"),
	/** 新增 */
	Add("A"),
	/** 修改 */
	Modify("M"),
	/** 修改審核層級 */
	ModifyCaseLvl("MCL"),
	/** 刪除 */
	Delete("D"),
	/** 預覽列印 */
	Print("P"),
	/** 複製 */
	Copy("C"),
	/** 調閱 */
	View("V"),
	/** 傳送 */
	Send("S"),
	/** 引進 */
	Pullin("PI"),
	/** 先行動用待辦控制表 */
	UseFirstTable("UF"),
	/** 登錄 */
	LogeIN("L"),
	/** 資料修正 */
	DataFix("DF"),
	/** 產生 */
	Create("CR"),
	/** 產生 */
	CreateExcel("CREXL"),	
	/** 查詢 */
	Search("SE"),
	/** 條件變更/續約 */
	Change("CH"),
	/** 額度明細表傳送聯行 */
	TableSend("TS"),
	/** 列印核貸通知書 */
	PrintBook("PB"),
	/** 貸放前照會借款人作業檢核表 */
	PrintNote("PN"),
	/** 案件複製 */
	CaseCopy("CC"),
	/** 登錄授審會會期 */
	Login1("L1"),
	/** 登錄催收會會期 */
	Login2("L2"),
	/** 登錄常董會會期 */
	Login3("L3"),
	/** 登錄審計委員會會期 */
	Login4("L4"),
	/** 常董稿 */
	CreDoc1("CD1"),
	/** 案件改分派 */
	CaseToChange("CTC"),
	/** 提會 */
	SendCase("SC"),
	/** 撤件/陳復 */
	BackCase("BC"),
	/** 退回分行更正 */
	BackUnit("BU"),
	/** 退回區域中心更正 */
	BackArea("BA"),
	/** 開啟授信報案考核表 */
	OpenLms("OL"),
	/** 重新傳回分行 */
	ReBackUnit("RBU"),
	/** 呈主管放行 */
	Send3("S3"),
	/** 案件格式變更(授權內) **/
	ChangeCaseFormat1("CCF1"),
	/** 案件格式變更 **/
	ChangeCaseFormat("CCF"),
	/** 收件 **/
	GetCase("GC"),
	/** 取消覆核 **/
	BackDoc("BD"),
	/** 列印營運中心意見 **/
	PrintArea("PA"),
	/** 覆核 **/
	FCheck("FC"),
	/** 上傳房貸評分卡 **/
	UPCls("UP"),
	/** 查詢額度控管設定 **/
	CntrNoCtl("CNTRCTL"),
	/** 修改統編 **/
	UpdCustId("UPDID"),
	/**上傳海外分行移轉名單 **/
	UpTransferId("UPTRANSFERID"),
	
	/**J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表 **/
	UpBisParam("UPBISPARAM"),
	
	/**J-112-0366_12473_B1001 Web e-Loan企金授信批次信用保證統計表總表 **/
	UpBatGutFile("UPBATGUTFILE"),
	
	/**
	 * J-107-0390_05097_B1001 分行權限之授信案件若於覆核後欲修改,得授權主管得退回至編製中
	 * 已覆核案件 **/
	BackApprove("BACKAPPROVE"),
	/** 產生Excel */
	ProduceExcel("PE"),
	/** 勞工紓困案，整批 */
	BatchAprvProd69("BATCHAPRVPROD69"),
    ChangeVer("ChangeVer"),
	/**J-109-0304_10702_B1005 Web e-Loan房仲回饋金整批輸入*/
	SaveRebate("SaveRebate"),
	SmallBussCRpa("SmallBussCRpa"), 
	StartUpReliefRpa("StartUpReliefRpa"),
	/**消金分組授權金額控管表上傳*/
	ImportClsAreaPriceExcel("ImportClsAreaPriceExcel"),
	/**
	 * J-110-0373 中鋼消貸
	 */
	CreateCSCExcel("CRCSCEXL"),	
	/** 改派簽案人員 */
	CaseToChangeSignEmp("CaseToChangeSignEmp"),
	/** 狀態回復 */
	CaseReturn("CaseReturn"),
	
	/** 在途授信額度EXCEL */
	ApproveUnestablshExl("ApproveUnestablshExl"),
	
	/** 派案分行設定(消金業務處維護功能) */
	AutoAssignMaintenance("AutoAssignMaintenance"),
	
	/**歡喜信貸eLoan流程精進修改-徵審分案流程變動 -> 一鍵分案 */
	OneButtonAssignCase("OneButtonAssignCase"),
	SingleMaintain("SingleMaintain"),
	BatchMaintain("BatchMaintain"),
	
	/** J-113-0306 案件簽報書送呈區域中心審核後，若被退件，在「待補件/撤件」中之被撤件之案件，能否設計可以再撈到編製中重新簽報，以增進作業效率 */
	/** 撤件案件退回編製中 */
	ReBackApproveUnit("ReBackApproveUnit"),
    /** J-113-0490_07623_B1001 公司訪談紀錄表批次調閱**/
	ImportExcel940CustId("ImportExcel940CustId"),
	/** 電銷中心傳送信貸案件 */
	CallCenterSendCreditCase("CallCenterSendCreditCase"),

	// 原RetrialButtonEnum的按鈕
	/** 產生名單 */
	ProduceList("PL"),
	/** 覆審控制檔維護 */
	Maintain("MT"),
	/** 整批覆核 **/
	AllSend("AS"),
	/** 修改預計覆審日 */
	ExceptRetrialDate("ERD"),
	/** 傳送分行覆審報告表 */
	SendRetrialReport("SRR"),
	/** 覆審工作底稿 */
	ProducePaper("PP"),
	/** 退回編製中 */
	ReturnToCompiling("RTCOMPILING"),
	/** 重新上傳名單到BTT */
	SendBtt("SDBTT"),
	/** 產生覆審考核表 */
	ProduceEvaluateTbl("PET"),
	/** 產生覆審考核排名表 */
	ProduceRankingBoard("PRB"),
	/** 傳送授管處 Credit Control Department */
	SendToCtrlDept("STCD"),
	/** 退回 */
	Return("R"),
	/** 移受檢單位登錄 */
	SendToExamUnit("SDEXMUT"),
	/** 整批核准 */
	BatchApproved("BTAPRV"),
	/** 整批作業 */
	BatchTask("BTTSK"),
	/** 產生企金戶新增/增額名單 */
	InsertExcelData("IED"),
	/** 產生企金戶未列於覆審名單 */
	InsertInExcelData("IIE"),
	/** 產生未於規定期限辦理覆審之企金戶名單 */
	InsertUnExcelData("IUE"),
	/** 產生新作增額逾期檢核表 */
	InsertLateExcelData("ILE"),
	/** 產生辦理最近授信檢核表 */
	InsertRecExcelData("IRE"),
	/** J-113-0519 一次性修正主機資料用 */
	UpdateRetrialDataJ1130519("URD0519"),
	/**整批呈主管覆核*/
	ToReviewAll("TRA"),
	/**整批列印工作底稿*/
	PrintAllAudit("PAA"),
	/**整批列印對帳單*/
	PrintAllBill("PAB"),
	/**整批列印對帳單*/
	ApprCreditAndCase("ACAC"),
	/**整批覆核*/
	ReviewAll("RA"),
	/** 產生報表 */
	CreateReport("CR2"),
	/** 引進當期資料 */
	PullinReport("PIR"),
	/** 上傳 */
	Upload("U"),
	/** 傳送法金處 */
	SendDocTypeReport("SDR"),
	/** 取消核備 */
	LongError("LE"),
	/** 登錄/調閱核備註記 */
	LongViewMemo("LVM"),
	/** 返回 */
	ReturnPage("RP"),
	/** 產生報表 */
	SummaryReport2("SR2"),
	/** 產生報表 */
	SummaryReport("SR"),
	/** 傳送總處 */
	SendHqTypeReport("SHR"),
	/**查詢客戶申貸紀錄 */
	QueryCustLoanRecord("QueryCustLoanRecord");
	

	/** 按鈕類型代碼 */
	private String code;

	/**
	 * constructor
	 * 
	 * @param code
	 *            按鈕代碼
	 */
	LmsButtonEnum(String code) {
		this.code = code;
	}

	/**
	 * 取得列舉型別代碼
	 * 
	 * @return
	 */
	public String getCode() {
		return code;
	}

	public boolean isEquals(Object other) {
		if (other instanceof String) {
			return code.equals(other);
		} else {
			return super.equals(other);
		}
	}

	public static LmsButtonEnum getEnum(String code) {
		for (LmsButtonEnum enums : LmsButtonEnum.values()) {
			if (enums.isEquals(code)) {
				return enums;
			}
		}
		return null;
	}

	/**
	 * 配合網頁對應 fragment 的內容
	 * 
	 * @see CMSButtonPanel.html
	 */
	@Override
	public String getFragmentCode() {

		return "fg" + getCode();
	}
}
