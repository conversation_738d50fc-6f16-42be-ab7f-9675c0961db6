/* 
 * SequenceDaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.BRelatedDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.BRelated;
import com.mega.eloan.lms.model.BRelated.DocTypeEnum;
import com.mega.eloan.lms.model.BRelated_;

/**
 * <pre>
 * 引用資料關聯記錄
 * </pre>
 * 
 * @since 2011/9/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/30,billWang,new
 *          </ul>
 */
@Repository
public class BRelatedDaoImpl extends LMSJpaDao<BRelated, String> implements
		BRelatedDao {

//	@Override
//	public int deleteByBRelated(String mainId1, String docType2) {
//		Query query = entityManager
//				.createNamedQuery("BRelated.deleteByMainId1AndDocType2");
//
//		query.setParameter("mainId1", mainId1);
//		query.setParameter("docType2", docType2);
//		return query.executeUpdate();
//	}

	@Override
	public BRelated findBymainId1AnddocType2(String mainId1, String docType2) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				BRelated_.mainId1.getName(), mainId1);
		search.addSearchModeParameters(SearchMode.EQUALS,
				BRelated_.docType2.getName(), docType2);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<BRelated> findByAll(String mainId1, String docType1, String docType2, String tab, String subtab, String relatedflag, boolean icludeFlag){
		ISearch search = createSearchTemplete();
		if(mainId1 != null){
			search.addSearchModeParameters(SearchMode.EQUALS,
					BRelated_.mainId1.getName(), mainId1);
		}
		if(docType1 != null){
			search.addSearchModeParameters(SearchMode.EQUALS,
					BRelated_.docType1.getName(), docType1);
		}
		if(docType2 != null){
			search.addSearchModeParameters(SearchMode.EQUALS,
					BRelated_.docType2.getName(), docType2);
		}
		if(tab != null){
			search.addSearchModeParameters(SearchMode.EQUALS,
					BRelated_.tab.getName(), tab);
		}
		if(subtab != null){
			search.addSearchModeParameters(SearchMode.EQUALS,
					BRelated_.subtab.getName(), subtab);
		}
		if(relatedflag != null){
			search.addSearchModeParameters(SearchMode.EQUALS,
					BRelated_.relatedflag.getName(), relatedflag);
		}
		if(!icludeFlag){
			search.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.IS_NULL, BRelated_.flag.getName(), ""),
					new SearchModeParameter(SearchMode.NOT_EQUALS, BRelated_.flag.getName(), "D"));
		}		
		
		return find(search);
	}

	@Override
	public List<BRelated> findByMainIdAndDocType2(String mainId1,
			DocTypeEnum docType2) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				BRelated_.mainId1.getName(), mainId1);
		search.addSearchModeParameters(SearchMode.EQUALS,
				BRelated_.docType2.getName(), docType2.toString());
		return find(search);
	}

	@Override
	public List<BRelated> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,BRelated_.mainId1.getName(), mainId);
		return find(search);
	}	
	
//	@Override
//	public int deleteByTab(String mainId1, String tab, String subtab) {
//		// delete from BRelated t where t.mainId1 = :mainId1 and t.tab=:tab and
//		// t.subtab=:subtab
//		Query query = entityManager
//				.createNamedQuery("BRelated.deleteByMainId1AndTab");
//		query.setParameter("mainId1", mainId1);
//		query.setParameter("tab", tab);
//		query.setParameter("subtab", subtab);
//		return query.executeUpdate();
//	}

//	@Override
//	public int deleteByTab2(String mainId1, String tab, String subtab,
//			String pid) {
//		// delete from BRelated t where t.mainId1 = :mainId1 and t.tab=:tab and t.subtab=:subtab and t.pid1=:pid1
//		Query query = entityManager
//				.createNamedQuery("BRelated.deleteByMainId1AndTabPid1");
//		query.setParameter("mainId1", mainId1);
//		query.setParameter("tab", tab);
//		query.setParameter("subtab", subtab);
//		query.setParameter("pid1", pid);
//		return query.executeUpdate();
//	}
}
