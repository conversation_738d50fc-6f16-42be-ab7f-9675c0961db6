/* 
 * CLS1161M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import java.io.OutputStream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.pages.AbstractSimplePdfPage;
import com.mega.eloan.lms.cls.service.CLS1220Service;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;

@Controller
@RequestMapping("/cls/cls1220r00")
public class CLS1220R00Report extends AbstractSimplePdfPage {

	@Autowired
	CLS1220Service cls1220Service;

	@Override
	protected OutputStream generateReport(PageParameters params) throws CapException {
		params.put("pageText",
				MessageBundleScriptCreator.getComponentResource(CLS1220R00Report.class).getProperty("PaginationText"));
		return super.generateReport(params);
	}

	@Override
	protected String getReportCreatorName() {
		return "cls1220r00rptservice";
	}

	@Override
	protected String getFileName() {
		return "cls1220r00.pdf";
	}

	// UPGRADE: 待確認ServiceName是否正確
	@Override
	protected String getFileDownloadServiceName() {
		return this.serviceName;
	}

}