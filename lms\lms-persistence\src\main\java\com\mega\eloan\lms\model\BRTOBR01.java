/* 
 * BRTOBR01.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import com.mega.eloan.lms.validation.group.Check;

import tw.com.iisi.cap.model.GenericBean;

/** ELOAN客戶資料移轉他行BY客戶ID檔 **/
@Entity
@Table(name="BRTOBR01")
public class BRTOBR01 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** Table Row Id */
	@Id
	@Column(name = "ROWID", columnDefinition = "VARCHAR(32) FOR BIT DATA", updatable = false, insertable = false)
	private byte[] rowid;

	/** 客戶MEGA ID **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 客戶重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "VARCHAR(1)")
	private String dupNo;

	/** 生效日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "EXDATE", columnDefinition = "DATE")
	private Date exDate;

	/** 來源分行 **/
	@Size(max = 3)
	@Column(name = "FBRANCH", length = 3, columnDefinition = "VARCHAR(03)")
	private String fbranch;

	/** 來源海外客戶ID **/
	@Digits(integer = 9, fraction = 0, groups = Check.class)
	@Column(name = "FCUSTID", columnDefinition = "DECIMAL(9,0)")
	private BigDecimal fcustId;

	/** 來源海外客戶編號 **/
	@Digits(integer = 10, fraction = 0, groups = Check.class)
	@Column(name = "FCUSTNO", columnDefinition = "DECIMAL(10,0)")
	private BigDecimal fcustNo;

	/** 目的地分行 **/
	@Size(max = 3)
	@Column(name = "TBRANCH", length = 3, columnDefinition = "VARCHAR(03)")
	private String tbranch;

	/** 目的地海外客戶ID **/
	@Digits(integer = 9, fraction = 0, groups = Check.class)
	@Column(name = "TCUSTID", columnDefinition = "DECIMAL(9,0)")
	private BigDecimal tcustId;

	/** 目的地海外客戶編號 **/
	@Digits(integer = 10, fraction = 0, groups = Check.class)
	@Column(name = "TCUSTNO", columnDefinition = "DECIMAL(10,0)")
	private BigDecimal tcustNo;

	/** 客戶名稱 **/
	@Size(max = 120)
	@Column(name = "CNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String cname;

	/** 轉換MEGA ID **/
	@Size(max = 10)
	@Column(name = "CHGCUSTID", length = 120, columnDefinition = "VARCHAR(10)")
	private String chgCustId;

	/** 轉換重複序號 **/
	@Size(max = 1)
	@Column(name = "CHGDUPNO", length = 120, columnDefinition = "VARCHAR(1)")
	private String chgDupNo;

	/** Table Row Id */
	public byte[] getRowid() {
		return rowid;
	}

	/** Table Row Id */
	public void setRowid(byte[] rowid) {
		this.rowid = rowid;
	}

	/** 取得客戶MEGA ID **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定客戶MEGA ID **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得客戶重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定客戶重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得生效日期 **/
	public Date getExDate() {
		return this.exDate;
	}

	/** 設定生效日期 **/
	public void setExDate(Date value) {
		this.exDate = value;
	}

	/** 取得來源分行 **/
	public String getFbranch() {
		return this.fbranch;
	}

	/** 設定來源分行 **/
	public void setFbranch(String value) {
		this.fbranch = value;
	}

	/** 取得來源海外客戶ID **/
	public BigDecimal getFcustId() {
		return this.fcustId;
	}

	/** 設定來源海外客戶ID **/
	public void setFcustId(BigDecimal value) {
		this.fcustId = value;
	}

	/** 取得來源海外客戶編號 **/
	public BigDecimal getFcustNo() {
		return this.fcustNo;
	}

	/** 設定來源海外客戶編號 **/
	public void setFcustNo(BigDecimal value) {
		this.fcustNo = value;
	}

	/** 取得目的地分行 **/
	public String getTbranch() {
		return this.tbranch;
	}

	/** 設定目的地分行 **/
	public void setTbranch(String value) {
		this.tbranch = value;
	}

	/** 取得目的地海外客戶ID **/
	public BigDecimal getTcustId() {
		return this.tcustId;
	}

	/** 設定目的地海外客戶ID **/
	public void setTcustId(BigDecimal value) {
		this.tcustId = value;
	}

	/** 取得目的地海外客戶編號 **/
	public BigDecimal getTcustNo() {
		return this.tcustNo;
	}

	/** 設定目的地海外客戶編號 **/
	public void setTcustNo(BigDecimal value) {
		this.tcustNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCname() {
		return this.cname;
	}

	/** 設定客戶名稱 **/
	public void setCname(String value) {
		this.cname = value;
	}

	/** 設定轉換MEGA ID **/
	public void setChgCustId(String chgCustId) {
		this.chgCustId = chgCustId;
	}

	/** 取得轉換MEGA ID **/
	public String getChgCustId() {
		return chgCustId;
	}

	/** 設定轉換重複序號 **/
	public void setChgDupNo(String chgDupNo) {
		this.chgDupNo = chgDupNo;
	}

	/** 取得轉換重複序號 **/
	public String getChgDupNo() {
		return chgDupNo;
	}
}
