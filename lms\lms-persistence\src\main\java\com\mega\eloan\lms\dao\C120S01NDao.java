/* 
 * C120S01NDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S01N;


/** 個金相關查詢授信歸戶檔 **/
public interface C120S01NDao extends IGenericDao<C120S01N> {

	C120S01N findByOid(String oid);

	List<C120S01N> findByMainId(String mainId);

	C120S01N findByUniqueKey(String mainId, String custId, String dupNo,
			String loanNo);

	List<C120S01N> findByIndex01(String mainId, String custId, String dupNo,
			String loanNo);
	List<C120S01N> findByCustIdDupId(String custId,String DupNo);
	
	int deleteByOid(String oid);
}