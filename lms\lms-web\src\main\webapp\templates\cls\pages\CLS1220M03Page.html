<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
			<!-- <script type="text/javascript" src="pagejs/cls/CLS1220M03Page.js?r=20220407"></script> > -->
			<script>loadScript('pagejs/cls/CLS1220M03Page')</script>
			<div class="button-menu funcContainer" id="buttonPanel">
				<th:block th:if="${_btnDOC_EDITING_visible}">
					<button id="btnSave"> 
	        			<span class="ui-icon ui-icon-jcs-04"></span>
	        			<th:block th:text="#{'button.save'}">儲存</th:block>
	        		</button>

				</th:block>
				
				<button id="btnPrint" class="forview">
					<span class="ui-icon ui-icon-jcs-03"></span>
					<th:block th:text="#{'button.print'}">列印</th:block>
				</button>
				<button id="btnExit" class="forview">
					<span class="ui-icon ui-icon-jcs-01"></span>
					<th:block th:text="#{'button.exit'}">離開</th:block>
				</button>
			</div>
			<!--=====================-->
			<div class="tit2 color-black">
				<th:block th:text="#{'doc.tit01'}">線上勞工紓困</th:block>:
				<span class="color-blue" id="titInfo"></span>
			</div>
			
            <form id="tabForm">
				<table class='tb2' width='95%'>
					<tr>
						<td colspan='6' class='hd2'><th:block th:text="#{'label.applyUserContent'}">申貸人</th:block>
						</td>
					</tr>
					<tr>
						<td class='hd1' width='10%' nowrap><th:block th:text="#{'C122M01A.custName'}">借款人姓名</th:block></td>
						<td width='36%'><span id='custName'></span>&nbsp;
						</td>
						<td class='hd1' width='4%' nowrap><th:block th:text="#{'labe.ntCode'}">國籍</th:block></td>
						<td width='20%'>[V]<th:block th:text="#{'label.ntCode.TW'}">中華民國</th:block>&nbsp;
						</td>
						<td class='hd1' width='10%' nowrap><th:block th:text="#{'C101S01A.coTel'}">住家電話</th:block></td>
						<td width='30%'><span id='coTel'></span>&nbsp;
						</td>
					</tr>
					<tr>
						<td class='hd1'><th:block th:text="#{'label.custEName'}">英文姓名</th:block> <br/>
							(<th:block th:text="#{'label.custEName.memo'}">與護照相同</th:block>) 
						</td>
						<td colspan='3'><span id='custENameLast'></span>&nbsp;(<th:block th:text="#{'C122M01A.custENameLast'}">姓</th:block>)&nbsp;&nbsp;&nbsp;&nbsp;
							<span id='custENameFirst'></span>&nbsp;(<th:block th:text="#{'C122M01A.custENameFirst'}">名</th:block>)
						</td>
						<td class='hd1' nowrap><th:block th:text="#{'C101S01A.mTel'}">手機</th:block></td>
						<td><span id='mTel'></span>&nbsp;
						</td>
					</tr>
					<tr>
						<td class='hd1' nowrap><th:block th:text="#{'C101S01A.marry'}">婚姻</th:block>
						</td>
						<td ><input type="radio" id="marry" name="marry" class="" codeType="marry" itemStyle="size:3" disabled />
						</td>		
						<td class='hd1' nowrap><th:block th:text="#{'C101S01A.child'}">子女</th:block></td>
						<td ><span id='child'></span>&nbsp;</td>		
						<td rowspan='2' class='hd1' nowrap><th:block th:text="#{'C101S01A.email'}">email</th:block></td>
						<td rowspan='2' ><span id='email'></span>&nbsp;</td>						
					</tr>
					<tr>
						<td class='hd1' nowrap><th:block th:text="#{'C122M01A.custId'}">身分證統編</th:block></td>
						<td colspan='3'><span id='custId'></span>&nbsp;</td>						
					</tr>
					<tr style='vertical-align:top;'>
						<td class='hd1' nowrap><th:block th:text="#{'C101S01A.birthday'}">出生日期</th:block></td>
						<td colspan='3'><span id='birthday'></span>&nbsp;</td>
						<td rowspan='5' class='hd1' nowrap><th:block th:text="#{'C101S01B.othAmt'}">其他收入</th:block></td>
						<td rowspan='5' >
								<input type="checkbox" id="othTypeTemp" name="othTypeTemp" value="07" class="" disabled><th:block th:text="#{'C101S01B.othType.07'}">執業所得</th:block> <br/>
								<input type="checkbox" id="othTypeTemp" name="othTypeTemp" value="04" class="" disabled><th:block th:text="#{'C101S01B.othType.04'}">租賃所得</th:block> <br/>
								<input type="checkbox" id="othTypeTemp" name="othTypeTemp" value="02" class="" disabled><th:block th:text="#{'C101S01B.othType.02'}">利息所得</th:block> <br/>
								<input type="checkbox" id="othTypeTemp" name="othTypeTemp" value="12" class="" disabled><th:block th:text="#{'C101S01B.othType.12'}">其他所得</th:block> <br/>
 								<span id='othAmt'></span>&nbsp;<th:block th:text="#{'label.twd10000'}">萬元</th:block>&nbsp;
						</td>						
					</tr>
					<tr style='vertical-align:top;'>
						<td class='hd1' nowrap><th:block th:text="#{'C101S01A.edu'}">學歷</th:block></td>
						<td colspan='3'><select id="edu" name="edu" class="" codeType="cls1131m01_edu" disabled ></select>&nbsp;</td>											
					</tr>
					<tr style='vertical-align:top;'>
						<td class='hd1' nowrap><th:block th:text="#{'C101S01B.comName'}">服務單位名稱</th:block></td>
						<td><span id='comName'></span>&nbsp;</td>		
						<td class='hd1' nowrap><th:block th:text="#{'C101S01B.seniority'}">年資</th:block></td>
						<td><span id='seniority'></span>&nbsp;
							<div><th:block th:text="#{'C101S01B.seniority.memo'}">(同質性工作連續年資)</th:block>
							</div>
						</td>									
					</tr>
					<tr>
						<td class='hd1' nowrap><th:block th:text="#{'C101S01B.jobTitle'}">職稱</th:block></td>
						<td><select id="jobTitle" name="jobTitle" class="" codeType="lms1205s01_jobTitle"  disabled ></select></td>		
						<td class='hd1' nowrap><th:block th:text="#{'C101S01B.payAmt'}">年薪</th:block></td>
						<td><span id='payAmt'></span>&nbsp;<th:block th:text="#{'label.twd10000'}">萬元</th:block>&nbsp;</td>									
					</tr>
					<tr>
						<td class='hd1' nowrap><th:block th:text="#{'C101S01B.comTel'}">服務單位電話</th:block></td>
						<td colspan='3'><span id='comTel'></span>&nbsp;</td>								
					</tr>
					<tr>
						<td class='hd1' nowrap><th:block th:text="#{'C101S01A.fTarget'}">戶籍地</th:block></td>
						<td colspan='5'><span id='fTarget'></span>&nbsp;</td>						
					</tr>
					<tr>
						<td class='hd1' nowrap><th:block th:text="#{'C101S01A.houseStatus'}">現住房屋</th:block></td>
						<td colspan='5'>
						<div>
							<span id='residenceTargetDesc'></span>
						</div>
						<input type="radio" id="houseStatus" name="houseStatus" class="" codeType="lms1205s01_houseStatus" itemStyle="size:6" disabled />
						</td>						
					</tr>
					<tr>
						<td class='hd1' nowrap><th:block th:text="#{'C101S01A.coTarget'}">通訊處</th:block></td>
						<td colspan='5'><span id='coTarget'></span>&nbsp;</td>						
					</tr>

		
					<!-- *************** -->
					<tr>
						<td colspan='6' class='hd2'><th:block th:text="#{'label.applyContent'}">申貸內容</th:block>
						</td>
					</tr>
					<tr>
						<td class='hd1' nowrap><th:block th:text="#{'C122M01A.applyAmt'}">申請金額</th:block></td>
						<td colspan='5'><th:block th:text="#{'label.prodKind69'}">勞工紓困貸款</th:block> <span id='applyAmt'></span>&nbsp;<th:block th:text="#{'label.twd10000'}">萬元(新台幣)</th:block>
						</td>
					</tr>
					<tr>
						<td class='hd1' nowrap><th:block th:text="#{'label.NoLabInsrMonth'}">無投保之收入所得</th:block></td>
						<td colspan='5'>&nbsp;<span id="noLabInsrMonth" ></span>&nbsp;<th:block th:text="#{'label.NoLabInsrMonth.unit'}">月</th:block>&nbsp;</td>
					</tr>
					<tr>
						<td class='hd1' nowrap><th:block th:text="#{'C122M01A.resource'}">還款財源</th:block></td>
						<td colspan='5'>
								<input type="checkbox" id="resourceTemp" name="resourceTemp" value="A" class="" disabled><th:block th:text="#{'C122M01A.resource.A'}">薪資</th:block>
								<input type="checkbox" id="resourceTemp" name="resourceTemp" value="B" class="" disabled><th:block th:text="#{'C122M01A.resource.B'}">營利</th:block>
								<input type="checkbox" id="resourceTemp" name="resourceTemp" value="C" class="" disabled><th:block th:text="#{'C122M01A.resource.C'}">投資</th:block>
								<input type="checkbox" id="resourceTemp" name="resourceTemp" value="D" class="" disabled><th:block th:text="#{'C122M01A.resource.D'}">租金</th:block>
								<input type="checkbox" id="resourceTemp" name="resourceTemp" value="E" class="" disabled><th:block th:text="#{'C122M01A.resource.E'}">利息</th:block>
								<input type="checkbox" id="resourceTemp" name="resourceTemp" value="3" class="" disabled><th:block th:text="#{'C122M01A.resource.3'}">其他</th:block>
						</td>
					</tr>
					<tr>
						<td class='hd1' nowrap><th:block th:text="#{'C122M01A.purpose'}">借款用途</th:block></td>
						<td colspan='5'>[V]<th:block th:text="#{'label.purpose_prodKind69'}">供生活支出所需或週轉之用</th:block>&nbsp;</td>
					</tr>
					<tr>
						<td class='hd1' nowrap><th:block th:text="#{'C122M01A.notifyWay'}">"貸款利息通知方式</th:block></td>
						<td colspan='5'>
                                	<label><input type="radio" id="notifyWay" name="notifyWay" value="1" disabled /><th:block th:text="#{'C122M01A.notifyWay.1'}"></th:block></label>&nbsp;&nbsp;
									<label><input type="radio" id="notifyWay" name="notifyWay" value="2" disabled /><th:block th:text="#{'C122M01A.notifyWay.2'}"></th:block></label>&nbsp;&nbsp;	
									<label><input type="radio" id="notifyWay" name="notifyWay" value="3" disabled /><th:block th:text="#{'C122M01A.notifyWay.3'}"></th:block></label>&nbsp;&nbsp;
						</td>
					</tr>
					<tr style='vertical-align:top;'>
						<td class='hd1' nowrap><th:block th:text="#{'C122M01A.idCardIssueArea'}">身分證發證地</th:block></td>
						<td>
							 <span id="idCardIssueArea" name="idCardIssueArea"></span>&nbsp;
						</td>
						<td rowspan='4' class='hd1' nowrap>
							<th:block th:text="#{'doc.attchGrid'}">上傳檔案</th:block>
							<br/>
							<button type="button" id="uploadFile">
	                            <span class="text-only"><th:block th:text="#{'button.uploadFile'}"><!-- 選擇附加檔案--></th:block></span>
	                        </button>
							<br/>
	                        <button type="button" id="deleteFile">
	                            <span class="text-only"><th:block th:text="#{'button.deleteFile'}"><!-- 刪除--></th:block></span>
	                        </button>
						</td>
						<td rowspan='4'  colspan='3'>          
			   						<div id='attchGrid' style='margin-left:7px;'>
			   						</div>			
						</td>
					</tr>
					<tr style='vertical-align:top;'>
						<td class='hd1' nowrap><th:block th:text="#{'C122M01A.idCardIssueDate'}">身分證發證日期</th:block></td>
						<td>
							 <span id="idCardIssueDate" name="idCardIssueDate"></span>&nbsp;
						</td>
					</tr>
					<tr style='vertical-align:top;'>
						<td class='hd1' nowrap><th:block th:text="#{'C122M01A.idCardChgFlag'}">身分證換補資料</th:block></td>
						<td>
                                	<label style="letter-spacing:0px;cursor:pointer;">
                                		<input type="radio" id="idCardChgFlag" name="idCardChgFlag" value='0' disabled><th:block th:text="#{'C122M01A.idCardChgFlag.0'}">初發</th:block>
									</label>
									&nbsp;&nbsp;
									<label style="letter-spacing:0px;cursor:pointer;">
										<input type="radio" id="idCardChgFlag" name="idCardChgFlag" value='1' disabled><th:block th:text="#{'C122M01A.idCardChgFlag.1'}">補發</th:block>
									</label>
									&nbsp;&nbsp;
									<label style="letter-spacing:0px;cursor:pointer;">
										<input type="radio" id="idCardChgFlag" name="idCardChgFlag" value='2' disabled><th:block th:text="#{'C122M01A.idCardChgFlag.2'}">換發</th:block>
									</label>
									&nbsp;&nbsp;
							
						</td>
					</tr>
					<tr style='vertical-align:top;'>
						<td class='hd1' nowrap><th:block th:text="#{'C122M01A.idCardPhoto'}">身分證有無照片</th:block></td>
						<td>
							  <input type="radio" id="idCardPhoto" name="idCardPhoto" codeType="Common_YesNo" itemStyle="sort:desc" disabled />
						</td>
					</tr>
					<tr style='vertical-align:top;'>
						<td class='hd2' nowrap><th:block th:text="#{'C122M01A.agreeQueryEJ'}">線上同意聯徵查詢</th:block></td>
						<td colspan='5'><span id='agreeQueryEJ'></span>&nbsp;
							<th:block th:if="${agreeQueryEJ_Y_detail_visible}">
								<table class='tb2' style='margin-left:36px;'>
									<tr style='vertical-align:top;' >
										<td class='' >
											<th:block th:text="#{'C122M01A.agreeQueryEJTs'}">線上同意查詢聯徵時間</th:block>
										</td>		
										<td class='' style='width:200px; '>
											<span id='agreeQueryEJTs' ></span>&nbsp;
										</td>		
									</tr>
									<tr style='vertical-align:top;' >
										<td class='' >
											<th:block th:text="#{'C122M01A.agreeQueryEJMtel'}">線上同意查詢聯徵手機</th:block>
										</td>		
										<td class='' >
											<span id='agreeQueryEJMtel' ></span>&nbsp;
										</td>		
									</tr>
									<tr style='vertical-align:top;' >
										<td class='' >
											<th:block th:text="#{'C122M01A.agreeQueryEJIp'}">線上同意查詢聯徵來源IP</th:block>
										</td>		
										<td class='' >
											<span id='agreeQueryEJIp' ></span>&nbsp;
										</td>		
									</tr>
									<tr style='vertical-align:top;' >
										<td class='' >
											<th:block th:text="#{'C122M01A.agreeQueryEJVer'}">同意書版本</th:block>
										</td>		
										<td class='' >
											<span id='agreeQueryEJVer' ></span>&nbsp;
										</td>		
									</tr>
								</table>
							</th:block>
						</td>
					</tr>
					<tr style='vertical-align:top;'>
						<td class='hd2' nowrap><th:block th:text="#{'C122M01A.statFlag'}">申貸案件狀態</th:block></td>
						<td colspan='5'>
								<table class='tb2'>
									<tr style='vertical-align:top;' >
									<td class='noborder' >
										<label style="letter-spacing:0px;cursor:pointer;">
	                                		<input type="radio" id="statFlag" name="statFlag" value='0' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.0'}">待受理</th:block>
										</label>
										&nbsp;&nbsp;
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlag" name="statFlag" value='1' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.1'}">審核中</th:block>
										</label>
										&nbsp;&nbsp;
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlag" name="statFlag" value='2' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.2'}">已承做</th:block>
										</label>
										&nbsp;&nbsp;
										<br>
										<button type="button" id="voidTheApply">申請作廢</button>
										<br>
										<span>執行作廢經辦：<input type="text" name="voider" id="voider" readonly="readonly"/></span>
										<br>
										<span>作廢時間：<input type="text" name="voidTime" id="voidTime" readonly="readonly"/></span>
									</td>
									<td class='noborder' >
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlag" name="statFlag" value='3' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.3'}">待對保</th:block>
										</label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlag" name="statFlag" value='4' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.4'}">已對保</th:block>
										</label>
										&nbsp;&nbsp;
									</td>
									<td class='noborder'>
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlag" name="statFlag" value='X' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.X'}">已作廢</th:block>
										</label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlag" name="statFlag" value='A' ><th:block th:text="#{'C122M01A.statFlag.applyKindB.A'}">不承做-票債信不良</th:block>
										</label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlag" name="statFlag" value='B' ><th:block th:text="#{'C122M01A.statFlag.applyKindB.B'}">不承做-評分未達60分</th:block>
										</label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlag" name="statFlag" value='C' ><th:block th:text="#{'C122M01A.statFlag.applyKindB.C'}">不承做-信保未承作</th:block>
										</label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlag" name="statFlag" value='D' ><th:block th:text="#{'C122M01A.statFlag.applyKindB.D'}">不承做-客戶撤件</th:block>
										</label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlag" name="statFlag" value='Q' ><th:block th:text="#{'C122M01A.statFlag.applyKindB.Q'}">不承作-客戶連繫不到</th:block>
										</label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlag" name="statFlag" value='R' ><th:block th:text="#{'C122M01A.statFlag.applyKindB.R'}">不承作-客戶重覆申請</th:block>
										</label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlag" name="statFlag" value='T' ><th:block th:text="#{'C122M01A.statFlag.applyKindB.T'}">不承作-年收大於50萬</th:block>
										</label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlag" name="statFlag" value='U' ><th:block th:text="#{'C122M01A.statFlag.applyKindB.U'}">不承作-其它</th:block>
										</label>
										<br/>
										<br/>
										<button type="button" id="changeCaseStatus">變更案件狀態</button>
									</td>
                                    <td class='noborder'>
                                        <label style="letter-spacing:0px;cursor:pointer;">
                                            <input type="radio" id="statFlag" name="statFlag" value='E' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.E'}">RPA評分未達60分</th:block>
                                        </label>
                                        <br/>
										<label style="letter-spacing:0px;cursor:pointer;">
                                            <input type="radio" id="statFlag" name="statFlag" value='N' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.N'}">RPA待人工評分</th:block>
                                        </label>
										<br/>
                                        <label style="letter-spacing:0px;cursor:pointer;">
                                            <input type="radio" id="statFlag" name="statFlag" value='F' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.F'}">RPA送信保中</th:block>
                                        </label>
                                        <br/>
                                        <label style="letter-spacing:0px;cursor:pointer;">
                                            <input type="radio" id="statFlag" name="statFlag" value='G' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.G'}">RPA簽報書待主管放行</th:block>
                                        </label>
                                        <br/>
                                        <label style="letter-spacing:0px;cursor:pointer;">
                                            <input type="radio" id="statFlag" name="statFlag" value='H' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.H'}">RPA簽報書需分行介入</th:block>
                                        </label>
                                        <br/>
										<label style="letter-spacing:0px;cursor:pointer;">
                                            <input type="radio" id="statFlag" name="statFlag" value='L' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.L'}">RPA契約書待主管放行</th:block>
                                        </label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
                                            <input type="radio" id="statFlag" name="statFlag" value='O' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.O'}">RPA契約書需分行介入</th:block>
                                        </label>
										<br/>
                                        <label style="letter-spacing:0px;cursor:pointer;">
                                            <input type="radio" id="statFlag" name="statFlag" value='I' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.I'}">RPA動審表待主管放行</th:block>
                                        </label>
                                        <br/>
                                        <label style="letter-spacing:0px;cursor:pointer;">
                                            <input type="radio" id="statFlag" name="statFlag" value='J' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.J'}">RPA動審表需分行介入</th:block>
                                        </label>
                                        <br/>
                                        <label style="letter-spacing:0px;cursor:pointer;">
                                            <input type="radio" id="statFlag" name="statFlag" value='K' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.K'}">RPA執行異常</th:block>
                                        </label>
										<br/>
										 <label style="letter-spacing:0px;cursor:pointer;">
                                            <input type="radio" id="statFlag" name="statFlag" value='P' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.P'}">RPA洗防高風險客群負面新聞需分行介入</th:block>
                                        </label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
                                            <input type="radio" id="statFlag" name="statFlag" value='M' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.M'}">擔保品待主管放行</th:block>
                                        </label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
                                            <input type="radio" id="statFlag" name="statFlag" value='S' disabled><th:block th:text="#{'C122M01A.statFlag.applyKindB.S'}">擔保品已覆核</th:block>
                                        </label>
                                    </td>
									</tr>
									<div id="changeCaseStatusDiv" style="display:none; margin-top:5px;">
					                    <label style="letter-spacing:0px;cursor:pointer;">
		                            		<input type="radio" id="statFlagChanged" name="statFlagChanged" value='0' class="required"><th:block th:text="#{'C122M01A.statFlag.applyKindB.0'}">待受理</th:block>
										</label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlagChanged" name="statFlagChanged" value='1' class="required"><th:block th:text="#{'C122M01A.statFlag.applyKindB.1'}">審核中</th:block>
										</label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlagChanged" name="statFlagChanged" value='2' class="required"><th:block th:text="#{'C122M01A.statFlag.applyKindB.2'}">已承做</th:block>
										</label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlagChanged" name="statFlagChanged" value='3' class="required"><th:block th:text="#{'C122M01A.statFlag.applyKindB.3'}">待對保</th:block>
										</label>
										<br/>
										<label style="letter-spacing:0px;cursor:pointer;">
											<input type="radio" id="statFlagChanged" name="statFlagChanged" value='4' class="required"><th:block th:text="#{'C122M01A.statFlag.applyKindB.4'}">已對保</th:block>
										</label>
									</div>
								</table>	
						</td>
					</tr>
					<tr style='vertical-align:top;'>
						<td class='hd2' nowrap><th:block th:text="#{'label.notifyMemo'}">備註</th:block></td>
						<td colspan='5'><textarea id='notifyMemo' name='notifyMemo' cols="72" rows="3" maxlengthC='90' class="txt_mult" style="width:700px;height:60px;" ></textarea>&nbsp;
						</td>
					</tr>
				</table>
				<input type="hidden" class="hidden" id="queryReasonIsRecorded" name="queryReasonIsRecorded"/>
            </form>
		</th:block>
    </body>
</html>
