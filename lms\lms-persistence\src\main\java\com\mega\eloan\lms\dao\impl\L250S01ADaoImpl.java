package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;

import com.mega.eloan.lms.dao.L250S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L250S01A;

@Repository
public class L250S01ADaoImpl extends LMSJpaDao<L250S01A, String> implements
		L250S01ADao {

	@Override
	public List<L250S01A> getAll() {

		ISearch search = createSearchTemplete();
		search.addOrderBy("groupOrder");
		return find(search);

	}

}