/* 
 * LMS9515GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.grid;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.I18NFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.LMSRPTDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.L180R02A;
import com.mega.eloan.lms.model.L784S01A;
import com.mega.eloan.lms.model.L784S07A;
import com.mega.eloan.lms.model.LMSRPT;
import com.mega.eloan.lms.rpt.service.LMS9511Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 歷史管理報表
 * </pre>
 * 
 * @since 2013/1/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/7,Ice,new
 *          <li>2013/01/10,Vector,加入個金報表
 *          </ul>
 */
@Scope("request")
@Controller("lms9521gridhandler")
public class LMS9521GridHandler extends AbstractGridHandler {
	
	@Resource
	EloandbBASEService eloandbBaseService;

	@Resource
	LMSService lmsService;

	@Resource
	LMS9511Service lms9511service;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMSRPTDao lmsRptDao;

	@Resource
	DocFileDao fileDao;

	/**
	 * 取得最新報表資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult queryLMSRPT(ISearch pageSetting,
			PageParameters params) throws CapException {
		String rptNo = Util.trim(params.getString("rptNo"));// 報表
		String nowRpt = Util.trim(params.getString("nowRpt"));// 報表
		String dataStartDate = Util.trim(params.getString("dataStartDate"));// 報表
		String dataEndDate = Util.trim(params.getString("dataEndDate"));// 報表
		Date bngDate = null;
		Date endDate = null;
		bngDate = lms9511service.getStartDateForLMSRPT(dataStartDate);
		endDate = lms9511service.getEndDateForLMSRPT(dataEndDate);
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "nowRpt", nowRpt);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rptNo", rptNo);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branch",
				user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
				"dataDate", bngDate==null?CapDate.ZERO_DATE:TWNDate.toAD(bngDate));
		pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS, "dataDate",
				endDate==null?CapDate.ZERO_DATE:TWNDate.toAD(endDate));
		Page<? extends GenericBean> page = lms9511service.findPage(
				LMSRPT.class, pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得最新報表資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult querySpeReport(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Date dataDate = null;
		String mainId = Util.trim(params.getString("mainId"));// 報表
		String rptNo = Util.trim(params.getString("rptNo"));// 報表
		String nowRpt = Util.trim(params.getString("nowRpt"));// 報表
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "nowRpt", nowRpt);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rptNo", rptNo);
		String brnachUnitType = UnitTypeEnum.convertToUnitType(
				user.getUnitType()).getCode();
		if (brnachUnitType.equals(UnitTypeEnum.分行.getCode()) || brnachUnitType.equals(UnitTypeEnum.國金部.getCode())) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branch",
					user.getUnitNo());
		} else if (brnachUnitType.equals(UnitTypeEnum.營運中心.getCode())) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branch",
					user.getUnitNo());
		} else if (brnachUnitType.equals(UnitTypeEnum.授管處.getCode())) {
			if ("N".equals(nowRpt)) {
				LMSRPT lmsRpt = lms9511service.findByLMSRPT(mainId);
				if (lmsRpt == null) {
					lmsRpt = new LMSRPT();
				}
				dataDate = lmsRpt.getDataDate();
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"dataDate", TWNDate.toAD(dataDate));
			}
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"sendTime", null);
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"branch", UtilConstants.BankNo.授管處);
		}

		// pageSetting.addOrderBy("createTime");
		Page<? extends GenericBean> page = lms9511service.findPage(
				LMSRPT.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("branch", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name)); // 分行名稱格式化
		result.setDataReformatter(dataReformatter);

		return result;
	}

	/**
	 * 取得918報表所有資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult querySpeLMS180R02AReport(ISearch pageSetting,
			PageParameters params) throws CapException {
		// String rptNo = Util.trim(params.getString("rptNo"));// 報表
		String nowRpt = Util.trim(params.getString("nowRpt"));// 報表
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "nowRpt", nowRpt);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rptNo",
				UtilConstants.RPTREPORT.DOCTYPE1.營運中心已敘做授信案件清單);
		pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL, "sendTime",
				null);

		// pageSetting.addOrderBy("createTime");
		Page<? extends GenericBean> page = lms9511service.findPage(
				LMSRPT.class, pageSetting);
		List<? extends GenericBean> list = page.getContent();
		for (GenericBean bean : list) {
			bean.set("jingBan", bean.get("branch"));
		}
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("branch", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name)); // 分行名稱格式化
		result.setDataReformatter(dataReformatter);

		return result;
	}

	/**
	 * 取得營運中心報表所有資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult querySpeLMS180R02AReport2(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString("mainId"));// 報表
		String approver = Util.trim(params.getString("approver"));// 報表
		String[] listBranch = params.getStringArray("listBranch");// 報表
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (Util.isNotEmpty(approver)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "approver",
					approver);
		}
		if (listBranch != null && listBranch.length > 0) {
			pageSetting.addSearchModeParameters(SearchMode.IN, "brno",
					listBranch);
		}

		// pageSetting.addOrderBy("createTime");
		Page<? extends GenericBean> page = lms9511service.findPage(
				L180R02A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("brno", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name)); // 分行名稱格式化
		dataReformatter.put("docType", new CodeTypeFormatter(codetypeService,
				"L120M01A_docType"));
		dataReformatter.put("docKind", new CodeTypeFormatter(codetypeService,
				"L120M01A_docKind"));
		result.setDataReformatter(dataReformatter);

		return result;
	}

	/**
	 * 查詢Grid 資料(L784s01a)
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	public CapGridResult queryL784s01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 判定是否已註記被刪除
		// pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
		// null);
		Page<? extends GenericBean> page = lms9511service.findPage(
				L784S01A.class, pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 取得常董會資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL784s07a(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString("mainId"));// 報表代碼-企金
		String year = null;
		List<IBranch> branchList = lmsService.getBranchList();
		List<Map<String, Object>> branchMapList = new LinkedList<Map<String, Object>>();
		LMSRPT lmsRpt = lmsRptDao.findByIndex03(mainId);
		if (lmsRpt == null) {
			lmsRpt = new LMSRPT();
		}
		year = lmsRpt.getDataDate() == null ? "" : TWNDate.toAD(
				lmsRpt.getDataDate()).substring(0, 4);
		for (IBranch ibranch : branchList) {
			Map<String, Object> map = new LinkedHashMap<String, Object>();
			map.put("mainId", mainId);
			map.put("apprYY", year);
			map.put("brNo", ibranch.getBrNo());
			map.put("brName", ibranch.getBrNo() + " " + ibranch.getBrName());
			map.put("caseDept", "3");
			branchMapList.add(map);
		}
		Page<Map<String, Object>> pages = LMSUtil.setPageMap(branchMapList,
				pageSetting);

		// 加入格式化
		CapMapGridResult result = new CapMapGridResult(pages.getContent(),
				branchMapList.size());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("caseDept", new I18NFormatter("caseDept."));
		result.setDataReformatter(dataReformatter);

		return result;
	}

	/**
	 * 查詢Grid 資料(L784s07a)
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	public CapGridResult queryL784s07aForTotalMonth(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));
		String brNo = Util.nullToSpace(params.getString("brNo"));

		// 判定是否已註記被刪除
		// pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
		// null);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "brNo", brNo);
		// pageSetting.setDistinct(true);
		Page<? extends GenericBean> page = lms9511service.findPage(
				L784S07A.class, pageSetting);
		// 加入格式化
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();

		dataReformatter.put("caseDept", new I18NFormatter("caseDept."));
		dataReformatter.put("updater", new UserNameFormatter(userInfoService)); // codeType格式化
		result.setDataReformatter(dataReformatter);

		return result;
	}

	/**
	 * 查詢 DocFile 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	public CapMapGridResult queryFile(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		String mainId = Util.nullToSpace(params.getString("mainId"));
		String rptNo = params.getString("rptNo");
		boolean useMainId = params.getAsBoolean("useMainId");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rptNo", rptNo);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "nowRpt", "N");
		if (UtilConstants.RPTREPORT.DOCTYPE2.審件統計表.equals(rptNo)) {
			pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
					"bgnDate", params.getString("bgnDate"));
			pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS, "endDate",
					params.getString("endDate"));
		} else {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
					mainId);
		}

		List<? extends GenericBean> list = lms9511service.findPage(
				LMSRPT.class, pageSetting).getContent();
		List<DocFile> fileList;
		if (list != null) {
			for (int i = 0; i < list.size(); i++) {
				LMSRPT pivot = (LMSRPT) list.get(i);
				// 查DOCFILE
				if (useMainId) {
					fileList = fileDao.getDocFilesByIdAndPid(pivot.getMainId(),
							null);
				} else {
					ISearch search = fileDao.createSearchTemplete();
					search.addSearchModeParameters(SearchMode.EQUALS, "oid",
							pivot.getReportOidFile());
					search.addSearchModeParameters(SearchMode.IS_NULL,
							"deletedTime", null);
					fileList = fileDao.find(search);
				}
				// 通通丟進MAP
				for (int x = 0; x < fileList.size(); x++) {
					DocFile file = fileList.get(x);
					Map<String, Object> record = new LinkedHashMap<String, Object>();
					record.put("oid", file.getOid());
					record.put("mainId", pivot.getMainId());
					record.put("reportOidFile", pivot.getReportOidFile());
					record.put("rptMainId", pivot.getMainId());
					record.put("branch",
							branchService.getBranchName(pivot.getBranch()));
					record.put("rptName", pivot.getRptName());
					record.put("bgnDate", pivot.getBgnDate());
					record.put("endDate", pivot.getEndDate());
					record.put("sendTime", pivot.getSendTime());
					record.put("cfmTime", pivot.getCfrmTime());
					record.put("updateTime", pivot.getUpdateTime());
					record.put("fileDesc", file.getFileDesc());
					result.add(record);
				}
			}
		}
		return new CapMapGridResult(result, result.size());
	}
}
