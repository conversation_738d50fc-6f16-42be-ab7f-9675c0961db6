$(document).ready(function(){
	initData();
	$("#buttonPanel").before('<div class=" tit2 color-black" id="searchActionName" name="searchActionName"></div>');
})


var button = $("#buttonPanel");
//隱藏不該顯示的欄位
function hideBtn(){
    $("#btnFilter").hide();
    $("#btnView").hide();
    $("#btnPrint").hide();
    $("#btnCreateReport").hide();
    $("#btnPullinReport").hide();
    $("#btnSummaryReport2").hide();
    $("#btnSummaryReport").hide();
    $("#btnSendDocTypeReport").hide();
    $("#btnSendHqTypeReport").hide();
    $("#btnLongViewMemo").hide();
    $("#btnDelete").hide();
}
//初始化欄位
function initData(){
	hideBtn();
    //brank("");
	$.ajax({
        type: "POST",
        handler: "lms9515m01formhandler",
        data: {
            formAction: "queryReportType"
        },
        success: function(obj){
        	
        	var searchAction = $("#searchAction");
        	searchAction.setItems({
                // i18n : i18n.samplehome,
                item: obj.item,
                format: "{key}" // ,
                // value :
            });
            showThickBox();
        }
    });
}

// ================================================================================================================

//搜尋分行
//function brank(val){
////	if(val != ''){
//		$.ajax({
//	        type: "POST",
//	        handler: "lms9515m01formhandler",
//	        data: {
//	            formAction: "queryBranch",
//	            reportType : val
//	        },
//	        success: function(obj){
//	            // alert(JSON.stringify(obj));
//	            $("#selectBranch").setItems({
//	                // i18n : i18n.samplehome,
//	                item: obj.item,
//	                format: "{value} {key}",
//	                space: false
//	                // value :
//	            });
//	            
//	        }
//	    });
////	}
//}
//顯示查詢條件
function showThickBox(){
    $("#lms9515new1").thickbox({
        title: i18n.lms9515v01['newInfo'],
        width: 500,
        height: 135,
        modal: false,
        align: 'center',
        valign: 'bottom',
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var action = $("#searchAction").val();
                if (action == "") {
                    return CommonAPI.confirmMessage(i18n.lms9515v01['L784M01a.error10']);
                }
                $("#searchActionName").val($('select#searchAction option:selected').text());
                
                // refactorGrid();
                switch (action) {
                    case '1':
                        type1();
                        break;
                    case '2':
                        type2();
                        break;
                    case '3':
                        type3();
                        break;
                    case '4':
                        //type4();
                        break;
                    case '5':
                        type5();
                        break;
                    case '6':
                        type6();
                        break;
                    case '7':
                        type7();
                        break;
                    case '8':
                        type8();
                        break;
                    case '9':
                        type9();
                        break;
					case '10':
                        type10();
                        break;
                }
                $.thickbox.close();
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });//Ajax
}
// ================================================================================================================
// 引進當期資料
var L9515V01Grid02 = $("#L9515v01Grid").iGrid({
    handler: 'lms9515gridhandler',
    height: 350, // 設定高度
	rownumbers:true,
	multiselect: true,
    // sortname : 'oid', //預設排序
    //multiselect : true, //是否開啟多選
    colModel: [{
        colHeader: "oid",
        name: 'oid',
        hidden: true
        // 是否隱藏
    }, {
        colHeader: i18n.lms9515v01["L784S01A.endDate"], // 核定日
        align: "center",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        // formatter : 'click',
        // onclick : function,
        name: 'endDate' // col.id
    }, {
        colHeader: i18n.lms9515v01["L784S01A.custId"], // 統一編號
        align: "left",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        // formatter : 'click',
        // onclick : function,
        name: 'custId' // col.id
    }, {
        colHeader: i18n.lms9515v01["L784S01A.custName"], // 戶名
        align: "left",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        // formatter : 'click',
        // onclick : function,
        name: 'custName' // col.id
    }, {
        colHeader: i18n.lms9515v01["L784S01A.cntrNo"], // 額度序號
        align: "left",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        // formatter : 'click',
        // onclick : function,
        name: 'cntrNo' // col.id
    }, {
        colHeader: i18n.lms9515v01["L784S01A.currentApplyAmt"], // 額度(金額)
        align: "right",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        //formatter : GridFormatter.number['addComma'],
        name: 'currentApplyAmt', // col.id
        formatter:'currency', 
		formatoptions:
		{
		    thousandsSeparator: ",",
			removeTrailingZero: true,
		    decimalPlaces: 2//小數點到第幾位
		}
    }, {
    	colHeader: i18n.lms9515v01["L784S01A.hqCheckDate"], // 總處核備日
        name: 'hqCheckDate',
        align: "center",
        width: 50,
        sortable: true
    }, {
        colHeader: i18n.lms9515v01["L784S01A.hqCheckMemo"], // 備註
        align: "left",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'hqCheckMemo'
    }]
});
button.find("#btnPullinReport").click(function(){
    var action = $("#searchAction").val();
    
    
    // 7. 常董會及申報案件統計表
    if (action == 7) {
        // 跳出[執行此功能會至中心主機將「所有分行」當月分資料引進，耗時較久，請問是否確定執行？]彈窗
    	$("#lms9515new7").thickbox({
            title: i18n.lms9515v01['newInfo2'],
            width: 350,
            height: 150,
            modal: false,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    $.thickbox.close();
                    var mYear = CommonAPI.getToday().split("-")[0];
                    var mMonth = CommonAPI.getToday().split("-")[1];
                    if(parseInt(mMonth,10) == 1){
                    	mYear = parseInt(mYear,10) - 1;
                    	mMonth = "12";
                    }else{
                    	mMonth = padLeft(parseInt(mMonth,10) - 1,2);
                    }
                    $("#yearM").val(mYear + "-" + mMonth);
                	var test2 = $("#lms9515new9").thickbox({
                        title: i18n.lms9515v01['newInfo1'],
                        width: 400,
                        height: 250,
                        align: 'center',
                        valign: 'bottom',
                        modal: false,
                        i18n: i18n.def,
                        buttons: {
                            "sure": function(){
                            	//起始日期
                                var yearM = $("#yearM").val();
                                var str = "";
                                if (yearM == "") {
                                    //L784M01a.startDate=起始日期
                                    str = i18n.lms9515v01["L784M01a.startDate"]
                                    //val.ineldate=請輸入年月
                                    return CommonAPI.showMessage(str + "" + i18n.def["val.ineldate"]);
                                }
                                if (!yearM.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                                    //val.date2=日期格式錯誤(YYYY-MM)
                                    return CommonAPI.showMessage(i18n.def["val.date2"]);
                                }
                                $.ajax({
                                    type: "POST",
                                    handler: "lms9515m01formhandler",
                                    data: {
                                        formAction: "transportRpt",
                                        searchAction: action,
                                        startDate : yearM
                                    },
                                    success: function(responseData){
                                        $("#gridview").trigger("reloadGrid");
                                        // 更新Grid內容
                                        
                                    }
                                });//Ajax
                                $.thickbox.close();
                            },
                            "close": function(){
                                $.thickbox.close();
                            }// 關閉
                        }
                    });// thickbox
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });//thickbox
    }
    else 
        if (action == 8) {
            // (8. 各級授權範圍內承做授信案件統計表)
            // 跳出[執行此功能會至中心主機將「所有分行」當月分資料引進，耗時較久，請問是否確定執行？]彈窗
            $("#lms9515new8").thickbox({
                title: i18n.lms9515v01['newInfo2'],
                width: 350,
                height: 150,
                modal: false,
                align: 'center',
                valign: 'bottom',
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        $.ajax({
                            type: "POST",
                            handler: "lms9515m01formhandler",
                            data: {
                                formAction: "transportRpt",
                                searchAction: action
                            },
                            success: function(responseData){
                                $("#gridview").trigger("reloadGrid");
                            }
                        });//Ajax
                        $.thickbox.close();
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        }
    // 彙總報表
}).end().find("#btnSummaryReport").click(function(){
    var action = $("#searchAction").val();
    if (action == 1) {
        $.ajax({
            type: "POST",
            handler: "lms9515m01formhandler",
            data: {
                formAction: "transportRpt",
                searchAction: action
            
            },
            success: function(responseData){
                $("#gridview").trigger("reloadGrid");
            }
        });
    }else if (action == 2) {
    	var mYear = CommonAPI.getToday().split("-")[0];
        var mMonth = CommonAPI.getToday().split("-")[1];
		
		if(parseInt(mMonth,10) == 1){
			//假設現在是2014-01  應該要變成 2013-12
        	mYear = parseInt(mYear,10) - 1;
        	mMonth = "12";
        }else{
        	mMonth = padLeft(parseInt(mMonth,10) - 1,2);
        }
		
		var isAccGroup = "";
		var countryType = "";
		$.ajax({
	        type: "POST",
	        handler: "lms9515m01formhandler",
			async: false,
	        data: {
	            formAction: "getAccGroup",
				unitNo : userInfo.unitNo
	        },
	        success: function(json){
				isAccGroup = json.accGroup; 
				//J-112-0JJJ_05097_B1001 Web e-Loan日本地區分行簽報書新增管理行授權內案件權限及相關修改
				countryType = json.countryType; 
	        }
        });
		
		var $lms9515v09From = $("#lms9515v09From");
		$lms9515v09From.reset();
	 
		if(isAccGroup == "Y"){
			$lms9515v09From.find("#showAccGroupChkbox").show();
			$lms9515v09From.find("#byAccGroup").attr('checked',true);
		}else{
			$lms9515v09From.find("#showAccGroupChkbox").hide();
		}
		
		//J-112-0JJJ_05097_B1001 Web e-Loan日本地區分行簽報書新增管理行授權內案件權限及相關修改
		if(countryType == "JP" && userInfo.unitNo == "0A7" ){
			//東京分行是管理行，列印可以包含當地所有管理行授權內案件
			$lms9515v09From.find("#showCountryHeadChkbox").show();
			$lms9515v09From.find("#byCountryHead").attr('checked',true);
		}else{
			$lms9515v09From.find("#showCountryHeadChkbox").hide();
		}
		
		$("#yearM").val(mYear + "-" + mMonth);
        var test2 = $("#lms9515new9").thickbox({
            title: i18n.lms9515v01['newInfo1'],
            width: 400,
            height: 250,
            align: 'center',
            valign: 'bottom',
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                	//YYYY-MM 格式驗證
                    //YYYY-MM 格式驗證
                    //起始日期
                    var yearM = $("#yearM").val();
                    var str = "";
                    if (yearM == "") {
                        //L784M01a.startDate=起始日期
                        str = i18n.lms9515v01["L784M01a.startDate"]
                        //val.ineldate=請輸入年月
                        return CommonAPI.showMessage(str + "" + i18n.def["val.ineldate"]);
                    }
                    if (!yearM.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                        //val.date2=日期格式錯誤(YYYY-MM)
                        return CommonAPI.showMessage(i18n.def["val.date2"]);
                    }
					
					var byAccGroup = "" ;
					if ($lms9515v09From.find("#byAccGroup").attr('checked')) {
	   	               byAccGroup = "Y";
				    }else{
					   byAccGroup = "N";
				    }
					
					//J-112-0JJJ_05097_B1001 Web e-Loan日本地區分行簽報書新增管理行授權內案件權限及相關修改
					var byCountryHead = "" ;
					if ($lms9515v09From.find("#byCountryHead").attr('checked')) {
						byCountryHead = "Y";
				    }else{
				    	byCountryHead = "N";
				    }
					
					//J-112-0JJJ_05097_B1001 Web e-Loan日本地區分行簽報書新增管理行授權內案件權限及相關修改
                    $.ajax({
                        type: "POST",
                        handler: "lms9515m01formhandler",
                        data: {
                            formAction: "transportRpt",
                            searchAction: action,
                            startDate : yearM,
                            byAccGroup : byAccGroup,
                            byCountryHead : byCountryHead
                        },
                        success: function(responseData){
                            $("#gridview").trigger("reloadGrid");
                        }
                    });
                    $.thickbox.close();
                },
                "close": function(){
                    $.thickbox.close();
                }// 關閉
            }
        });// thickbox
	}else  if (action == 3) {
    	$.ajax({
            type: "POST",
            handler: "lms9515m01formhandler",
            data: {
                formAction: "getStartEndDate"
            },
            success: function(responseData){
            	$("#sdateNw3a").val(responseData.startDate);
            	$("#edateNw3a").val(responseData.endDate);
            	// 請輸入『信保案件未動用屆期清單』起迄日期
                $("#lms9515new3a").thickbox({
                    title: i18n.lms9515v01['newInfo3a'],
                    width: 450,
                    height: 200,
                    modal: false,
                    align: 'center',
                    valign: 'bottom',
                    i18n: i18n.def,
                    buttons: {
                        "sure": function(){
                            if ($("#sdateNw3a").val().replace(/\-/g,"") > $("#edateNw3a").val().replace(/\-/g,"")) {
                                //L784M01a.startDate=L784M01a.startDate
                                //L784M01a.error2=不能大於迄至日期
                                return CommonAPI.showErrorMessage(i18n.lms9515v01["L784M01a.startDate"] + i18n.lms9515v01["L784M01a.error2"]);
                            }
                            
                            $.ajax({
                                type: "POST",
                                handler: "lms9515m01formhandler",
                                data: $.extend($("#lms9515v01From3").serializeData(), {
                                    formAction: "transportRpt",
                                    searchAction: action
                                }),
                                success: function(responseData){
                                    
                                    $("#gridview").trigger("reloadGrid");// 更新Grid內容
                                }
                            });//ajax
                            $.thickbox.close();
                        },
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });//內thickbox
            }
        });
                
                
                
    }else if (action == 5) {
        $("#lms9515new5").thickbox({
            title: i18n.lms9515v01['newInfo3'],
            width: 450,
            height: 150,
            modal: false,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    $.thickbox.close();
                    // 起迄日期
                    $("#lms9515new5a").thickbox({
                        title: i18n.lms9515v01['newInfo5a'],
                        width: 650,
                        height: 180,
                        modal: false,
                        align: 'center',
                        valign: 'bottom',
                        i18n: i18n.def,
                        buttons: {
                            "sure": function(){
                            	//YYYY-MM 格式驗證
                                //YYYY-MM 格式驗證
                                //起始日期
                                var starDate = $("#sdate").val();
                                //迄至日期
                                var endDate = $("#edate").val();
                                var str = "";
                                
                                if (starDate == "") {
                                    //L784M01a.startDate=起始日期
                                    str = i18n.lms9515v01["L784M01a.startDate"]
                                    //val.ineldate=請輸入年月
                                    return CommonAPI.showMessage(str + "" + i18n.def["val.ineldate"]);
                                }
                                if (endDate == "") {
                                    //L784M01a.endDate=迄至日期
                                    str = i18n.lms9515v01["L784M01a.endDate"]
                                    //val.ineldate=請輸入年月
                                    return CommonAPI.showMessage(str + i18n.def["val.ineldate"]);
                                }
                                if (!starDate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                                    //val.date2=日期格式錯誤(YYYY-MM)
                                    return CommonAPI.showMessage(i18n.def["val.date2"]);
                                }
                                if (!endDate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                                    //val.date2=日期格式錯誤(YYYY-MM)
                                    return CommonAPI.showMessage(i18n.def["val.date2"]);
                                }
                            	if($("#lms9515v01From5").valid()){
                            		 $.ajax({
                                         type: "POST",
                                         handler: "lms9515m01formhandler",
                                         data: $.extend($("#lms9515v01From5").serializeData(), {
                                             formAction: "transportRpt",
                                             searchAction: action
                                         }),
                                         success: function(responseData){
                                             $("#gridview").trigger("reloadGrid");// 更新Grid內容
                                         }
                                     });//ajax
                                     $.thickbox.close();
                            	}
                            },
                            "cancel": function(){
                                $.thickbox.close();
                            }
                        }
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }else if (action == '10') {
    	var mYear = CommonAPI.getToday().split("-")[0];
        var mMonth = CommonAPI.getToday().split("-")[1];
        var mDay = CommonAPI.getToday().split("-")[2];
		ilog.debug(mYear);
		ilog.debug(mMonth);
		ilog.debug(mDay);
		if (parseInt(mMonth, 10) < 7) {
			mYear = parseInt(mYear,10) - 1;
			$("#sdateNw3a").val(mYear + "-07-01");
        	$("#edateNw3a").val(mYear + "-12-31");
		} else {
			$("#sdateNw3a").val(mYear + "-01-01");
        	$("#edateNw3a").val(mYear + "-06-31");
		}
		CommonAPI.confirmMessage(i18n.lms9515v01['L784M01a.msg'], function(b){
            if (b) {
				$("#lms9515new3a").thickbox({
		            title: i18n.lms9515v01['newInfo1'],
		            width: 450,
		            height: 200,
		            modal: false,
		            align: 'center',
		            valign: 'bottom',
		            i18n: i18n.def,
		            buttons: {
		                "sure": function(){
		                    if ($("#sdateNw3a").val().replace(/\-/g,"") > $("#edateNw3a").val().replace(/\-/g,"")) {
		                        //L784M01a.error2=不能大於迄至日期
		                        return CommonAPI.showErrorMessage(i18n.lms9515v01["L784M01a.startDate"] + i18n.lms9515v01["L784M01a.error2"]);
		                    }
		                    $.ajax({
		                        type: "POST",
		                        handler: "lms9515m01formhandler",
		                        data: $.extend($("#lms9515v01From3").serializeData(), {
		                            formAction: "transportRpt",
		                            searchAction: action	
		                        }),
		                        success: function(responseData){
		                            $("#gridview").trigger("reloadGrid");// 更新Grid內容
		                        }
		                    });//ajax
		                    $.thickbox.close();
		                },
		                "cancel": function(){
		                    $.thickbox.close();
		                }
		            }
		        });//thickbox
            }
        });
	}
}).end()// 產生報表
.find("#btnCreateReport").click(function(){
    var action = $("#searchAction").val();
    if (action == 6) {
//    	$("#lms9515new6").thickbox({
//            title: i18n.lms9515v01['newInfo3'],
//            width: 450,
//            height: 150,
//            modal: false,
//            align: 'center',
//            valign: 'bottom',
//            i18n: i18n.def,
//            buttons: {
//                "sure": function(){
//                	if($("#lms9515v01From6").valid()){
//	               		 $.ajax({
//	                            type: "POST",
//	                            handler: "lms9515m01formhandler",
//	                            data: {
//	                                formAction: "transportRpt",
//	                                searchAction: action
//	                                //startDate: $("#startDate").val()
//	                            },
//	                            success: function(responseData){
//                                    $("#gridview").trigger("reloadGrid");// 更新Grid內容
//	                            }
//	                        });//ajax
//	                        $.thickbox.close();
//	               	}
//                },
//                "cancel": function(){
//                    $.thickbox.close();
//                }
//            }
//        });
    	$.ajax({
            type: "POST",
            handler: "lms9515m01formhandler",
            data: {
                formAction: "transportRpt",
                searchAction: action
                //startDate: $("#startDate").val()
            },
            success: function(responseData){
                $("#gridview").trigger("reloadGrid");// 更新Grid內容
            }
        });//ajax
    }
    
}// 登錄/調閱核備註記
).end().find("#btnLongViewMemo").click(function(){
	showType2Detail();
}).end().find("#btnPrint").click(function(){
	var gridview = $("#gridview");
    var id = gridview.getGridParam('selrow');
    if (!id) {
        // action_006=請先選擇需「調閱」之資料列
        return CommonAPI.showMessage(i18n.def["action_006"]);
    }
    if($("#searchAction").val() == '7'){
        var data = gridview.getRowData(id);
        
        var mYear = CommonAPI.getToday().split("-")[0];
        var mMonth = CommonAPI.getToday().split("-")[1];
		if(parseInt(mMonth,10) == 1){
        	mYear = parseInt(mYear,10) - 1;
        	mMonth = "12";
        }else{
        	mMonth = padLeft(parseInt(mMonth,10) - 1,2);
        }
        $("#yearM1").val(mYear + "-" + mMonth);
        
        var test2 = $("#lms9515new7b").thickbox({
            title: i18n.lms9515v01['L784M01a.ManageReport'],
            width: 250,
            height: 180,
            align: 'center',
            valign: 'bottom',
            modal: false,
            i18n: i18n.def,
            buttons: {
                "print": function(){
                	
                	 var printType = $('input:radio:checked[name="printType"]').val();
                	 var yearM = $("#yearM1").val();
                	 var caseDept = $('input:radio:checked[name="caseDept"]').val();
                	 if (printType == "allByAppr"){
                		 var str = "";
                         if (yearM == "") {
                             //L784M01a.startDate=起始日期
                             str = i18n.lms9515v01["L784M01a.startDate"]
                             //val.ineldate=請輸入年月
                             return CommonAPI.showMessage(str + "" + i18n.def["val.ineldate"]);
                         }
                         if (!yearM.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                             //val.date2=日期格式錯誤(YYYY-MM)
                             return CommonAPI.showMessage(i18n.def["val.date2"]);
                         }
                		 
                	 }
  					if (caseDept == ""){
                		 var str = "";
                         str = i18n.lms9515v01["L784M01a.docType1"] + "/" + i18n.lms9515v01["L784M01a.docType2"]	 //L180R02A.docType=企/個金案件	includeId.selData=請選擇一筆資料!!
                         return CommonAPI.showMessage(str + "" + i18n.def["includeId.selData"]);
                	 }
                	$.form.submit({
                        url: "../simple/FileProcessingService",
                        target: "_blank",
                        data: {
                            mainId: data.mainId,
                            brNo : data.oid,
                            printType : printType,
                            startDate : yearM,
							caseDept : caseDept,
                            fileDownloadName: "pdf7.pdf",
                            serviceName: "lms9515r02rptservice"
                        }
                    });
                },
                "close": function(){
                    $.thickbox.close();
                }// 關閉
            }
        });// thickbox
        
    }else{
        var result = gridview.getRowData(id);
        var oid = result.reportFile;
        download2(oid);
    }
    //openDoc(null, null, result);

}).end().find("#btnView").click(function(){
	var gridview = $("#gridview");
    var id = gridview.getGridParam('selrow');
    if (!id) {
        // action_006=請先選擇需「調閱」之資料列
        return CommonAPI.showMessage(i18n.def["action_006"]);
    }
    showType7Detail();
}).end().find("#btnSummaryReport2").click(function(){
    var action = $("#searchAction").val();
    var mYear = CommonAPI.getToday().split("-")[0];
    var mMonth = CommonAPI.getToday().split("-")[1];
    if(parseInt(mMonth,10) == 1){
    	mYear = parseInt(mYear,10) - 1;
    	mMonth = "12";
    }else{
    	mMonth = padLeft(parseInt(mMonth,10) - 1,2);
    }
    
    $("#yearM").val(mYear + "-" + mMonth);
    var test2 = $("#lms9515new9").thickbox({
        title: i18n.lms9515v01['newInfo1'],
        width: 400,
        height: 250,
        align: 'center',
        valign: 'bottom',
        modal: false,
        i18n: i18n.def,
        buttons: {
            "sure": function(){
            	//YYYY-MM 格式驗證
                //YYYY-MM 格式驗證
                //起始日期
                var yearM = $("#yearM").val();
                var str = "";
                if (yearM == "") {
                    //L784M01a.startDate=起始日期
                    str = i18n.lms9515v01["L784M01a.startDate"]
                    //val.ineldate=請輸入年月
                    return CommonAPI.showMessage(str + "" + i18n.def["val.ineldate"]);
                }
                if (!yearM.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                    //val.date2=日期格式錯誤(YYYY-MM)
                    return CommonAPI.showMessage(i18n.def["val.date2"]);
                }
                $.ajax({
                    handler: "lms9515m01formhandler",
                    type: "POST",
                    dataType: "json",
                    data: $.extend($("#lms9515v09From").serializeData(), {
                        formAction: "transportRpt",
                        searchAction: action,
                        startDate : yearM
                    }),	
                    success: function(responseData){
                    	$("#gridview").trigger("reloadGrid");
                        
                    }
                });
                $.thickbox.close();
            },
            "close": function(){
                $.thickbox.close();
            }// 關閉
        }
    });// thickbox
    
    
    
    
}).end()// 刪除
.find("#btnDelete").click(function(){
    var $gridview = $("#gridview");
    var ids = $gridview.getGridParam('selrow');
    if (!ids) {// TMMDeleteError=請先選擇需修改(刪除)之資料列
        return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
    }
    var data = $gridview.getRowData(ids);
    if(data.hqCheckDate != ''){
    	return CommonAPI.showMessage(i18n.lms9515v01["L784S01A.hqCheckDateError"]);
    }else{
    	var oids = [];
        for (var i in ids) {
            oids.push(data.oid);
        }
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "lms9515m01formhandler",
                    data: {
                        formAction: "delete",
                        oids: oids,
                        searchAction: i
                    },
                    success: function(obj){
                        // 更新Grid內容
                        $gridview.trigger("reloadGrid");
                    }
                });
            }
        });
    }
    
    
}).end()// 傳送授管處
.find("#btnSendDocTypeReport").click(function(){
    var gridview = $("#gridview");
    var ids = gridview.getGridParam('selrow');
    if (!ids) { //grid_selector=請選擇資料
        return CommonAPI.showMessage(i18n.def["grid_selector"]);
    }
    var oid = gridview.getRowData(ids).oid;
    
    $.ajax({
        handler: "lms9515m01formhandler",
        action: "sendDocTypeReport",
        data: {
            oid: oid,
            searchAction: $("#searchAction").val()
        },
        success: function(obj){
            if (obj.sendLastTime) {
                //此報表已傳送過
                return CommonAPI.showMessage(i18n.lms9515v01["L784S01A.haveSended"]);
            }
            // 更新Grid內容
            gridview.trigger("reloadGrid");
        }
    });
    
    
});

function padLeft(str,lenght){
	str = str + "";  //轉成字串，要不然str.length會變成undefined
    if(str.length >= lenght){
		return str;
	}else{
		return padLeft("0" +str,lenght);
	} 
	    
}
// ======================================================================================================
function type1(){
    $("#btnDelete").show();
    // 決定SHOW的BUTTON
    // 彙總報表
    $("#btnSummaryReport").show();
    // 列印
    $("#btnPrint").show();
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9515gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val()
        
        },
        colModel: [{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'oid',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms9515v01["L784M01a.ownBrId2"], // 分行名稱
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'ownBrId' // col.id
        }, {
            colHeader: i18n.lms9515v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = grid.getRowData(rowid);
			download(null, null, data);
		}
    
    });
}

// ======================================================================================================
function type2(){
	if(userInfo.unitType == '4'){
	    // 登錄/調閱核備註記
	    $("#btnLongViewMemo").show();
	}else{
	    $("#btnSummaryReport").show();
	    $("#btnDelete").show();
	}
    // 決定SHOW的BUTTON
    // 列印
    $("#btnPrint").show();
	// 分行("1"), 營運中心("2"), 徵信處("3"), 授管處("4"), 國金部("5"), 債管處("6"), 會計室("7")
	//alert(JSON.stringify(userInfo));

	if(userInfo.unitType == '4'){
	    var grid = $("#gridview").iGrid({
	        rownumbers: true,
	        handler: 'lms9515gridhandler',
	        height: 350, // 設定高度
	        sortname: 'createTime', // 預設排序
	        // multiselect : true,
	        postData: {
	            formAction: "queryL951m01a",
	            searchAction: $("#searchAction").val()
	        },
	        colModel: [{
	            name: 'oid',
	            hidden: true
	            // 是否隱藏
	        },{
	            name: 'hqCheckDate',
	            hidden: true
	            // 是否隱藏
	        }, {
	            name: 'mainId', // col.id
	            hidden: true
	            // 是否隱藏
	        }, {
	            name: 'reportFile', // col.id
	            hidden: true
	            // 是否隱藏
	        }, {
	            name: 'rptType', // col.id
	            hidden: true
	            // 是否隱藏
	        }, {
	            colHeader: i18n.lms9515v01["L784M01a.ownBrId2"], // 分行名稱
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'ownBrId' // col.id
	        }, {
	            colHeader: i18n.lms9515v01["L784M01a.dataDate"], // 資料年月
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'dataDate', // col.id
	            formatter: 'date',
	            formatoptions: {
	                srcformat: 'Y-m-d',
	                newformat: 'Y-m'
	            }
	        }, {
	            colHeader: i18n.lms9515v01["L784M01a.createTime"], // 資料產生日期
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'createTime' // col.id,
	        }, {
	            colHeader: i18n.lms9515v01["L784M01a.sendFirstTime"], // 分行傳送時間
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'sendFirstTime' // col.id
	        }, {
	            colHeader: i18n.lms9515v01["L784M01a.approveTime"], // 核准日期
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'approveTime' // col.id
	        }],
	         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
	         		var data = grid.getRowData(rowid);
	    			download(null, null, data);
			}
	    });
	}else{
	    // 傳送法金處
		if(userInfo.unitType != '2'){
		    $("#btnSendDocTypeReport").show();
		}
	    var grid = $("#gridview").iGrid({
	        rownumbers: true,
	        handler: 'lms9515gridhandler',
	        height: 350, // 設定高度
	        sortname: 'createTime', // 預設排序
	        // multiselect : true,
	        postData: {
	            formAction: "queryL951m01a",
	            searchAction: $("#searchAction").val()
	        },
	        colModel: [{
	            name: 'oid',
	            hidden: true
	            // 是否隱藏
	        },{
	            name: 'hqCheckDate',
	            hidden: true
	            // 是否隱藏
	        }, {
	            name: 'mainId', // col.id
	            hidden: true
	            // 是否隱藏
	        }, {
	            name: 'reportFile', // col.id
	            hidden: true
	            // 是否隱藏
	        }, {
	            name: 'rptType', // col.id
	            hidden: true
	            // 是否隱藏
	        }, {
	            colHeader: i18n.lms9515v01["L784M01a.ownBrId2"], // 分行名稱
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'ownBrId' // col.id
	        }, {
	            colHeader: i18n.lms9515v01["L784M01a.dataDate"], // 資料年月
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'dataDate', // col.id
	            formatter: 'date',
	            formatoptions: {
	                srcformat: 'Y-m-d',
	                newformat: 'Y-m'
	            }
	        }, {
	            colHeader: i18n.lms9515v01["L784M01a.createTime"], // 資料產生日期
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'createTime' // col.id,
	        }, {
	            colHeader: i18n.lms9515v01["L784M01a.sendFirstTime"], // 分行傳送時間
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'sendFirstTime' // col.id
	        }, {
	            colHeader: i18n.lms9515v01["L784M01a.approveTime"], // 核准日期
	            align: "center",
	            width: 100, // 設定寬度
	            sortable: true, // 是否允許排序
	            name: 'approveTime' // col.id
	        }],
	         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
	         		var data = grid.getRowData(rowid);
					download(null, null, data);
			}
	    
	    });
	}
    
    
}

// ======================================================================================================
function type3(){
    $("#btnDelete").show();
    // 決定SHOW的BUTTON
    // 彙總報表
    $("#btnSummaryReport").show();
    $("#btnPrint").show();
    
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9515gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val()
        },
        colModel: [{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'oid',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms9515v01["L784M01a.ownBrId2"], // 分行名稱
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'ownBrId' // col.id
        }, {
            colHeader: i18n.lms9515v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = grid.getRowData(rowid);
			download(null, null, data);
		}
    
    });
}

// ======================================================================================================
function type5(){
    $("#btnDelete").show();
    // 決定SHOW的BUTTON
    // 彙總報表
    $("#btnSummaryReport").show();
    // 列印
    $("#btnPrint").show();
    
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9515gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val()
        },
        colModel: [{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'oid',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms9515v01["L784M01a.ownBrId2"], // 分行名稱
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            //formatter: 'click',
            // onclick : BOM,
            name: 'ownBrId' // col.id
        }, {
            colHeader: i18n.lms9515v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = grid.getRowData(rowid);
			download(null, null, data);
		}
    });
    
}

// ======================================================================================================
function type6(){
    // 列印
    $("#btnPrint").show();
    if(userInfo.unitType == '4'){
    	
    }else{
        $("#btnDelete").show();
        // 決定SHOW的BUTTON
        // 產生報表
        $("#btnCreateReport").show();
        // 傳送法金處
        $("#btnSendDocTypeReport").show();
    }
    
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9515gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val()
        },
        colModel: [{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'oid',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        },{
            colHeader: i18n.lms9515v01["L784M01a.ownBrId2"], // 分行名稱
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'branchId' // col.id
        },{
            colHeader: i18n.lms9515v01["L784M01a.createTime2"], // 資料年月
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'dataDate', // col.id
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            }
        }, {
            colHeader: i18n.lms9515v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }, {
            colHeader: i18n.lms9515v01["L784M01a.transportEnd"], // 傳送時間
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'sendFirstTime' // col.id
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = grid.getRowData(rowid);
			download(null, null, data);
		}
    
    });
}

// ======================================================================================================
function type7(){
    //$("#btnDelete").show();
    // 決定SHOW的BUTTON
    // 引進當期資料
    $("#btnPullinReport").show();
    // 調閱
    $("#btnView").show();
    // 列印
    $("#btnPrint").show();
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9515gridhandler',
        height: 350, // 設定高度
        sortname: 'brNo', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL784s07a",
            searchAction: $("#searchAction").val()
        },
        colModel: [{
            name: 'oid', // col.id
            hidden: true
            // 是否隱藏
        },{
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms9515v01["L784M01a.yearDate"], // 資料年度
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'apprYY' // col.id
        }, {
            colHeader: i18n.lms9515v01["L784M01a.ownBrId2"], // 分行別
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            //formatter: 'click',
            // onclick : BOM,
            name: 'brNo' // col.id
        }, {
            colHeader: i18n.lms9515v01["L784M01a.rptType2"], // 資料性質
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'caseDept' // col.id
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
         	showType7Detail();
		}
    
    });
}

// ======================================================================================================
function type8(){
    $("#btnDelete").show();
    // 決定SHOW的BUTTON
    // 引進當期資料
    $("#btnPullinReport").show();
    // 列印
    $("#btnPrint").show();
    
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9515gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val()
        },
        colModel: [{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'oid',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        },{
            colHeader: i18n.lms9515v01["L784M01a.dataDate"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'dataDate', // col.id
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            }
        }, {
            colHeader: i18n.lms9515v01["L784M01a.ownBrId2"], // 分行名稱
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'OwnBrId' // col.id,
        }, {
            colHeader: i18n.lms9515v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = grid.getRowData(rowid);
			download(null, null, data);
		}
    
    });
}

// ======================================================================================================
function type9(){
    $("#btnDelete").show();
    // 決定SHOW的BUTTON
    // 引進當期資料
    // 產生營業單位授信報案考核彙總表
    $("#btnSummaryReport2").show();
    // 列印
    $("#btnPrint").show();
    
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9515gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val()
        },
        colModel: [{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'oid',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        },{
            colHeader: i18n.lms9515v01["L784M01a.dataDate"], // 資料年月
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'dataDate', // col.id
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            }
        }, {
            colHeader: i18n.lms9515v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }],
         	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = grid.getRowData(rowid);
			download(null, null, data);
		}
    
    });
}

// ======================================================================================================
function type10(){
    $("#btnPrint").show();
	$("#btnSummaryReport").show();
	// 分行("1"), 營運中心("2"), 徵信處("3"), 授管處("4"), 國金部("5"), 債管處("6"), 會計室("7")
	//alert(JSON.stringify(userInfo));
	
 	var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms9515gridhandler',
        height: 350, // 設定高度
        sortname: 'createTime', // 預設排序
        // multiselect : true,
        postData: {
            formAction: "queryL951m01a",
            searchAction: $("#searchAction").val()
        },
        colModel: [{
            name: 'oid',
            hidden: true
            // 是否隱藏
        },{
            name: 'hqCheckDate',
            hidden: true
            // 是否隱藏
        }, {
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            name: 'rptType', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms9515v01["L784M01a.ownBrId2"], // 分行名稱
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'ownBrId' // col.id
        }, {
            colHeader: i18n.lms9515v01["L784M01a.dataDate"]+i18n.lms9515v01["L784M01a.startDate"],// 資料年月, 
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'dataDate', // col.id
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            }
        }, {
            colHeader: i18n.lms9515v01["L784M01a.dataDate"]+i18n.lms9515v01["L784M01a.endDate"],// 資料年月迄至日期, 
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'dataEDate', // col.id
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            }
        }, {
            colHeader: i18n.lms9515v01["L784M01a.createTime"], // 資料產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id,
        }],
         ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
     		var data = grid.getRowData(rowid);
			download(null, null, data);
		}
    
    });
    
    
}
// ======================================================================================================
function BOM(cellvalue, options, rowObject){
    ilog.debug(rowObject);
    $.form.submit({
        url: '../rpt/LMS9515M01Page/01',
        data: {
            formAction: "queryL741s07a",
            mainId: rowObject.mainId,
            // mainDocStatus : viewstatus,
            mainOid: rowObject.oid, // mainOid 驗證文件狀態 ,權限按鈕顯現
            custId: rowObject.custId,
            dupNo: rowObject.dupNo
        
        },
        target: rowObject.oid
    });
}
var lastSel;

var L9515V01Grid01 = $("#L9515v01Grid2").iGrid({
    handler: 'lms9515gridhandler',
    height: 350, // 設定高度
    cellsubmit: 'clientArray',
     sortname : 'apprMM', //預設排序
    colModel: [{
        colHeader: "oid",
        name: 'oid',
        hidden: true
        // 是否隱藏
    },{
        colHeader: "mainId",
        name: 'mainId',
        hidden: true
        // 是否隱藏
    },{
        colHeader: "brNo",
        name: 'brNo',
        hidden: true
        // 是否隱藏
    }, {
        colHeader: i18n.lms9515v01["L784S07A.apprYY"], // 案件核准年度(民國年)
        align: "center",
        width: 50, // 設定寬度
        // formatter : 'click',
        // onclick : function,
        name: 'apprYY' // col.id
    }, {
        colHeader: i18n.lms9515v01["L784S07A.apprMM"], // 案件核准月份
        align: "center",
        width: 50, // 設定寬度
        // formatter : 'click',
        // onclick : function,
        name: 'apprMM' // col.id
    }, {
        colHeader: i18n.lms9515v01["L784S07A.cItem12Rec"], // 逾放展期、轉正常(筆數)
        align: "right",
        width: 50, // 設定寬度
        editable: true,
        editrules: {
            number: true
        },
        edittype:'text',
        editoptions: {size:5, maxlength: 5},
        sortable: true, // 是否允許排序
        //formatter : GridFormatter.number['addComma'],
        name: 'cItem12Rec' // col.id
    }, {
        colHeader: i18n.lms9515v01["L784S07A.cItem12Amt"], // 逾放展期、轉正常(金額)
        align: "right",
        width: 50, // 設定寬度
        editable: true,
        editrules: {
            number: true
        },
        edittype:'text',
        editoptions: {size:10, maxlength: 15},
        sortable: true, // 是否允許排序
        //formatter : GridFormatter.number['addComma'],
        name: 'cItem12Amt' // col.id
    }, {
        colHeader: i18n.lms9515v01["L784S07A.updater"], // 異動人員
        align: "left",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'updater' // col.id
    }],
    onSelectRow: function(id){
        if (id && id != lastSel) {
            $("#L9515v01Grid2").saveRow(lastSel, false, 'clientArray');
            $('#L9515v01Grid2').restoreRow(lastSel);
            lastSel = id;
        }
        $('#L9515v01Grid2').editRow(id, false);
    }
});

function showType7Detail(){
	var row = $("#gridview").getGridParam('selrow');
    if (!row) {
        return CommonAPI.showMessage(i18n.lms9515v01["L784S01A.error1"]);
    }
    var data = $("#gridview").getRowData(row);
    L9515V01Grid01.jqGrid("setGridParam", {//重新設定grid需要查到的資料
        postData: {
        	formAction : "queryL784s07aForTotalMonth",
            mainId : data.mainId,
            brNo : data.oid
        },
        search: true
    }).trigger("reloadGrid");
    
    var test2 = $("#lms9515new7a").thickbox({
        title: i18n.lms9515v01['newInfo2a'] + "-" + data.brNo,
        width: 850,
        height: 510,
        align: 'left',
        // valign : 'bottom',
        modal: false,
        i18n: i18n.def,
        buttons: {
            "saveData": function(){
            	//寫回額度明細表
            	L9515V01Grid01.jqGrid('saveRow', lastSel, false, 'clientArray');
                var ids = L9515V01Grid01.jqGrid('getDataIDs');
                //用來放列印順序跟oid
                var json = {};
                var checkArray1 = L9515V01Grid01.getCol("cItem12Rec");
                var checkArray2 = L9515V01Grid01.getCol("cItem12Amt");
                
                for (var id in ids) {
                    var data = L9515V01Grid01.jqGrid('getRowData', ids[id]);
                    json[data.oid] = data.cItem12Rec + "|" + data.cItem12Amt;
                }
                $.ajax({
                    handler: "lms9515m01formhandler",
                    action: "saveL784S07AData",
                    data: {
                        mainId: data.mainId,
                        data: JSON.stringify(json)
                    },
                    success: function(obj){
                    	L9515V01Grid01.trigger("reloadGrid");
                    }
                });
            },//
            "print": function(){
            	$.form.submit({
                    url: "../simple/FileProcessingService",
                    target: "_blank",
                    data: {
                        mainId: data.mainId,
                        brNo : data.oid,
                        fileDownloadName: "pdf7.pdf",
                        serviceName: "lms9515r02rptservice"
                    }
                });
            },//
            "close": function(){
                $.thickbox.close();
            }// 關閉
        }
        // bottons
    
    });// thickbox
}

function showType2Detail(){
	var row = $("#gridview").getGridParam('selrow');
    if (!row) {
        return CommonAPI.showMessage(i18n.lms9515v01["L784S01A.error1"]);
    }
    var data = $("#gridview").getRowData(row);
    L9515V01Grid02.jqGrid("setGridParam", {//重新設定grid需要查到的資料
        postData: {
        	formAction : "queryL784s01a",
            mainId : data.mainId
        },
        search: true
    }).trigger("reloadGrid");
    
    var test2 = $("#lms9515new2a").thickbox({
        title: i18n.lms9515v01['newInfo2a'],
        width: 850,
        height: 510,
        align: 'left',
        // valign : 'bottom',
        modal: false,
        i18n: i18n.def,
        buttons: {
            "lognView": function(){
            	var id = L9515V01Grid02.getGridParam('selarrrow');
                var count = 0;
                var content = "";
            	for (var i = 0; i < id.length; i++) {
                    if (id[i] != "") {
                        var data = L9515V01Grid02.getRowData(id[i]);
                        if (content.length != 0) {
                        	content = content + "|" + data.oid;
                        }else{
                        	content = content + data.oid;
                        }
                        count++;
                    }
                }
                if (count == 0) {
                    CommonAPI.showMessage(i18n.def['grid.selrow']);
                } else {
                	$.ajax({
                        handler: "lms9515m01formhandler",
                        type: "POST",
                        dataType: "json",
                        data: {
                            formAction: "queryL784S01a",
                            mainOids: content
                        },
                        success: function(json){
                            $("#hqCheckDate").val(json.hqCheckDate);
                            $("#hqCheckMemo").val(json.hqCheckMemo);
                            var test2 = $("#lms9515new2b").thickbox({
                                title: i18n.lms9515v01['newInfo2b'],
                                width: 560,
                                height: 380,
                                align: 'left',
                                modal: false,
                                i18n: i18n.def,
                                buttons: {
                                    "saveData": function(){
                                    	if($("#lms9515v01From2").valid()){
                                    		$.ajax({
                                                type: "POST",
                                                handler: "lms9515m01formhandler",
                                                data: $.extend($("#lms9515v01From2").serializeData(), {
                                                    formAction: "saveRemarkNotes",
                                                    oids: content
                                                }),
                                                success: function(responseData){
                                                    L9515V01Grid02.trigger("reloadGrid");
                                                }
                                            });//ajax
                                            $.thickbox.close();
                                    	}
                                    },
                                    "close": function(){
                                        $.thickbox.close();
                                    }// 關閉
                                }
                            });// thickbox
                        }
                        
                    });// ajax
                }
            },//
            "print": function(){
            	$.form.submit({
                    url: "../simple/FileProcessingService",
                    target: "_blank",
                    data: {
                        mainId: data.mainId,
                        brNo : data.brNo,
                        fileDownloadName: "pdf201.pdf",
                        serviceName: "lms9515r03rptservice"
                    }
                });
            },//
            "close": function(){
                $.thickbox.close();
            }// 關閉
        }
        // bottons
    
    });// thickbox
}

// ======================================================================================================
function download(cellvalue, options, data){
    // alert(data.reportFile);
    $.capFileDownload({
         handler: "simplefiledwnhandler",
        data: {
            fileOid: data.reportFile
        }
    });
    
}

//下載EXCEL
function download2(oid){

    $.capFileDownload({
          handler: "simplefiledwnhandler",
        data: {
            fileOid: oid
        }
    });
    
}

$("input[type='radio'][name='printType']").click(function(){
	var value = $(this).filter(":checked").val(); 
	
	if(value == "allByAppr"){
		$("#startYearMonth").show();
	}else{
		$("#startYearMonth").hide();
	}
});
