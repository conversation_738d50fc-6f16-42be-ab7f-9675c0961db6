/* 
 * LMS2415Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.service;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;



import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.eloan.lms.model.C241M01C;
import com.mega.eloan.lms.model.C241M01E;
import com.mega.sso.userdetails.MegaSSOUserDetails;

public interface LMS2415Service extends ICapService {

	void deleteC241M01A(String oid);

	/**
	 * 利用Oid做搜尋
	 * 
	 * @param <T>
	 * @param clazz
	 * @param oid
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	<T extends GenericBean> T findModelByOid(Class clazz, String oid);

	/**
	 * 利用MainId做搜尋
	 * 
	 * @param <T>
	 * @param clazz
	 * @param mainId
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	<T extends GenericBean> T findModelByMainId(Class clazz, String mainId);

	/**
	 * 一般/團貸覆審項目檔
	 * 
	 * @param class1
	 * @param mainId
	 * @return
	 */
	List<C241M01C> findC241m01cByMainId(String mainId);

	/**
	 * 授信帳務資料檔
	 * 
	 * @param class1
	 * @param mainId
	 * @return
	 */
	List<C241M01B> findC241m01bByMainId(String mainId);
	
	/**
	 * 搜尋
	 * 
	 * @param clazz
	 * @param search
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	/**
	 * 單筆儲存
	 * 
	 * @param entity
	 */
	void save(GenericBean... entity);

	/**
	 * 單筆儲存
	 * 
	 * @param entity
	 */
	void saveNoChangUpdate(GenericBean... entity);

	/**
	 * 單筆刪除
	 * 
	 * @param clazz
	 * @param oid
	 */
	@SuppressWarnings("rawtypes")
	void delete(Class clazz, String oid);

	/**
	 * C241m01b多筆刪除
	 * 
	 * @param mainId
	 */
	void deleteC241m01bByMainId(String mainId);

	/**
	 * C241m01c多筆刪除
	 * 
	 * @param mainId
	 */
	void deleteC241m01cByMainId(String mainId);

	/**
	 * 重新產生授信資料
	 * 
	 * @param brNo
	 * @param custId
	 * @param mainId
	 * @param dupNo
	 * @return
	 */
	Map<String, Object> saveC241m01bByCustIdData(String brNo, String custId, String mainId,
			String dupNo);

	/**
	 * 更新覆審控制檔(491)
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @throws ParseException 
	 */
	boolean updateElf491(String mainId, String custId, String dupNo,
			String branch,String doFix,String specifyCycle) throws ParseException;

	/**
	 * 取得grid頁面所需資料
	 * 
	 * @param mainId
	 *            mainId
	 * @param search
	 *            search
	 * @return Page<Map<String, Object>>
	 * @throws CapException
	 *             CapException
	 */
	Page<Map<String, Object>> getBorrows(String mainId, ISearch search)
			throws CapException;

	/**
	 * 儲存C241M01C
	 * 
	 * @param list
	 */
	void save(List<C241M01C> list);

	/**
	 * 利用MainId做搜尋 <C241M01C>單一筆 index2
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param aa
	 * @return
	 */
	C241M01C findC241m01cByMainId(String mainId, String custId, String dupNo,
			int itemSeq);
	
	/**  利用MainId做搜尋 <C241M01E>
	 * @param mainId
	 * @return
	 */
	public List<C241M01E> findC241m01eByMainId(String mainId);

	/**
	 * 找C241M01B(Bym ainId 多筆)
	 * 
	 * @param mainId
	 * @return
	 */
	List<C241M01B> findC241M01bList(String mainId);

	/**
	 * 啟動流程
	 * 
	 * @param oid
	 *            oid(instid)
	 * @param flow_code
	 *            流程代號
	 */
	public void startFlow(String oid, String flow_code)throws CapException;

	/**
	 * flowAction
	 * 
	 * @param mainOid
	 * @param model
	 * @param setResult
	 * @param resultType
	 * @throws Throwable
	 */
	public void flowAction(String oid, MegaSSOUserDetails user,
			HashMap<String, Object> data) throws CapException;

	/**
	 * tempSave (C241M01C)
	 * 
	 * @param models
	 */
	void saveC241m01dList2(List<C241M01C> models);

	/**
	 * delete from l241m01b
	 * 
	 * @param oids
	 */
	void deleteFromL241M01B(String[] oids);
	
	/**
	 * <pre>
	 * 刪除所有授信資料
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	public void deleteCredit(String mainId) throws CapException;

	public Map<String, String> sumC241M01BAllAndSaveC241M01A(String brNo,
			String mainId, C241M01A c241m01a, List<C241M01B> c241m01bList,
			String lnDataDateResult) throws CapException;
	
	/** 把C241M01B作加總存入C241M01A
	 * @param brNo 分行
	 * @param mainId mainId
	 * @param C241M01A C241M01A ( 若不需要則傳Null)
	 * @param c241m01bList 更新後的l170m01b所有內容
	 * @param lnDataDateResult 判斷是否要對l170m01a的lnDataDate做儲存  1=要儲存currecttime 2=要儲存null
	 * @param saveResult 是否要儲存c241m01a
	 * @return 
	 * @throws CapException
	 */
	public Map<String, String> sumC241M01BAllAndSaveC241M01A(String brNo,
			String mainId, C241M01A c241m01a, List<C241M01B> c241m01bList,
			String lnDataDateResult,boolean saveResult) throws CapException;

	C241M01C findC241m01cByMainId(String mainId, String custId, String dupNo,
			String itemNo);
	
	C241M01A produceNewC241M01A(String custId, String dupNo, String custName) throws CapException;

	void deleteC241M01AMainMark(String oid);
	
	public void saveLinkToC240M01B(String c240MainId, String c241MainId);
	
	/**
	 * 取得最新的消金海外覆審項目清單版本
	 * @return
	 */
	public String getOVSLastC241M01ARptId();

}

 