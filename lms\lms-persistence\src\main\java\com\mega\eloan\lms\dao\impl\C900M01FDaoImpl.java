/* 
 * C900M01FDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C900M01FDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900M01F;

/** 個金授信科目限額檔 **/
@Repository
public class C900M01FDaoImpl extends LMSJpaDao<C900M01F, String>
	implements C900M01FDao {

	@Override
	public C900M01F findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C900M01F> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("subjSeq");
		List<C900M01F> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C900M01F findByUniqueKey(String mainId, String lmtType, Integer lmtSeq){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (lmtType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "lmtType", lmtType);
		if (lmtSeq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "lmtSeq", lmtSeq);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C900M01F> findByIndex01(String mainId, String lmtType, Integer lmtSeq){
		ISearch search = createSearchTemplete();
		List<C900M01F> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (lmtType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "lmtType", lmtType);
		if (lmtSeq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "lmtSeq", lmtSeq);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<C900M01F> findByIndex02(String mainId, String lmtType){
		ISearch search = createSearchTemplete();
		List<C900M01F> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (lmtType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "lmtType", lmtType);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<C900M01F> findByMainIdAndlmtType(String mainId, String lmtType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "lmtType", lmtType);
		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("createTime", false);
		printSeqMap.put("lmtSeq", false);
		search.setOrderBy(printSeqMap);
		List<C900M01F> list = createQuery(C900M01F.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public List<C900M01F> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<C900M01F> list = createQuery(C900M01F.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public void deleteByMainId(String mainId){
		Query query = entityManager.createNamedQuery("C900M01F.delByMainId");
		query.setParameter(1, mainId);
		query.executeUpdate();
	}
}