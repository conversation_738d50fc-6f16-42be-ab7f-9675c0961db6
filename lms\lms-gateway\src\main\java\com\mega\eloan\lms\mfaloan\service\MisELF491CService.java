package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF491C;


/**
 * <pre>
 * 消金抽樣覆審控制檔
 * </pre>
 * 
 * @since 2020/11/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/11/16,EL08034,new
 *          </ul>
 */
public interface MisELF491CService {
	public int countByBrNoRuleNoLrDateBegEnd(String brNo, String ruleNo, String lrDate_beg, String lrDate_end);
	
	public int countRule95_all(String brNo);
	public int countRule95_active(String brNo);		
	public List<Map<String, Object>> selByBrno_ELF491C_RULE_NO_95_1(String brNo, String period_beg, String period_end, String ruleNo_95_1);
	
	public List<Map<String, Object>> selByBrno_R1R2S(String brNo, BigDecimal threshold_amt, int fetch_size);
	
	public int countRule_projectCreditLoan_all(String brNo, String loanDate_beg, String loanDate_end);
	public int countRule_projectCreditLoan_active(String brNo, String loanDate_beg, String loanDate_end);
	public List<Map<String, Object>> selByBrno_Rule_projectCreditLoan(String brNo, String loanDate_beg, String loanDate_end, int fetch_size);

	public List<Map<String, Object>> list_baseCnt_R95_1();
	public List<ELF491C> find(String brNo, String custid, String dupNo, String ruleNo, String lrDate);
	public Date findLatest_lrDate(String brNo, String custid, String dupNo, String ruleNo);

	/**
	 * 單一授信額度在新臺幣一千萬元以下，且為十足擔保授信或經信用保證基金保證成數七成以上者<BR> 
	 * <li>1.判斷【循環額度>0筆 or 循環額度=0筆 】=> REVOL_Y='Y' 
	 * <li>2.新做、增額案件除外，只判斷舊案 => ELF491_NEWFLAG ='' 
	 * <li>抓取該月份的總數
	 * 
	 * @param twYYYYMM
	 * @return
	 */
	List<Map<String, Object>> selAllBrnoCount_R14(String twYYYYMM);
	
	/**
	 * 單一授信額度在新臺幣一千萬元以下，且為十足擔保授信或經信用保證基金保證成數七成以上者<BR> 
	 * <li>1.判斷【循環額度>0筆 or 循環額度=0筆 】=> REVOL_Y='Y' 
	 * <li>2.新做、增額案件除外，只判斷舊案 => ELF491_NEWFLAG ='' 
	 * <li>3.抓取By 分行  t_chose.ELF487_BR_NO= ?
	 * 
	 * @param brNo
	 * @param fetch_size
	 * @return
	 */
	List<Map<String, Object>> selByBrno_R14(String brNo, int fetch_size);
}
