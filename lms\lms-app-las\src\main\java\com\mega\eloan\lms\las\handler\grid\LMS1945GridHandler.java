package com.mega.eloan.lms.las.handler.grid;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.las.service.LMS1945Service;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 稽核工作底稿(分行)作業之Grid handler
 * </pre>
 * 
 * <AUTHOR>
 * 
 */
@Scope("request")
@Controller("lms1945gridhandler")
public class LMS1945GridHandler extends AbstractGridHandler {

	@Resource
	LMS1945Service lms1945Service;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	// /**
	// * 查詢分行內部查核資料
	// *
	// * @param pageSetting
	// * ISearch
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapGridResult
	// * @throws CapException
	// */
	// public CapGridResult queryViewDocEditing(ISearch pageSetting,
	// PageParameters params, Component parent) throws CapException {
	// // 增加search 條件
	// String branch = MegaSSOSecurityContext.getUnitNo();
	//
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
	// branch);
	// String docStatus = Util.nullToSpace(params
	// .getString(EloanConstants.DOC_STATUS));
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
	// EloanConstants.DOC_STATUS, docStatus);
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "innerAudit",
	// "Y");
	//
	// SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	// Calendar beginDate = Calendar.getInstance();
	// beginDate.setTime(new Date());
	// beginDate.set(Calendar.DAY_OF_MONTH, 1);
	//
	// Calendar endDate = (Calendar) beginDate.clone();
	// int month = endDate.get(Calendar.MONTH) + 1;
	// endDate.set(Calendar.MONTH, month);
	//
	// // 取得檢查日當月資料
	// pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
	// "checkDate", sdf.format(beginDate.getTime()));
	// pageSetting.addSearchModeParameters(SearchMode.LESS_THAN, "checkDate",
	// sdf.format(endDate.getTime()));
	// pageSetting.addOrderBy("checkDate", true);
	// Page<L192M01A> page = lms1945Service.get1945V01(pageSetting);
	//
	// // formatter
	// Map<String, IFormatter> map = new HashMap<String, IFormatter>();
	// map.put("ownBrId", new BranchNameFormatter(branchService,
	// ShowTypeEnum.ID_Name));
	// map.put("updater", new UserNameFormatter(userInfoService));
	//
	// return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	// }

	/**
	 * <pre>
	 * 查詢分行內部查核資料，編製中(當月) 工作底稿資料(包含 授信，房貸，團貸)
	 *  <ul>
	 *  <li>預設搜尋條件，該月，該明異動人員所異動之資料</li>
	 * </ul>
	 * </pre>
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryViewDocEditingDefault(ISearch pageSetting,
			PageParameters params) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 增加search 條件
		String branch = MegaSSOSecurityContext.getUnitNo();

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				branch);
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.DOC_STATUS, docStatus);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "innerAudit",
				"Y");

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Calendar beginDate = Calendar.getInstance();
		beginDate.setTime(new Date());
		beginDate.set(Calendar.DAY_OF_MONTH, 1);

		Calendar endDate = (Calendar) beginDate.clone();
		int month = endDate.get(Calendar.MONTH) + 1;
		endDate.set(Calendar.MONTH, month);

		// 取得檢查日當月資料
		pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
				"checkDate", sdf.format(beginDate.getTime()));
		pageSetting.addSearchModeParameters(SearchMode.LESS_THAN, "checkDate",
				sdf.format(endDate.getTime()));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "updater",
				user.getUserId());

//		pageSetting.addOrderBy("checkDate", true);
		Page<L192M01A> page = lms1945Service.get1945V01(pageSetting);

		// formatter
		Map<String, IFormatter> map = new HashMap<String, IFormatter>();
		map.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name));
		map.put("updater", new UserNameFormatter(userInfoService));

		return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	}

	// /**
	// * 查詢分行內部查核資料
	// *
	// * @param pageSetting
	// * ISearch
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapGridResult
	// * @throws CapException
	// */
	// public CapGridResult queryViewDocEditingThisMonthBefore(
	// ISearch pageSetting, PageParameters params, Component parent)
	// throws CapException {
	// // 增加search 條件
	// String branch = MegaSSOSecurityContext.getUnitNo();
	//
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
	// branch);
	//
	// String docStatus = Util.nullToSpace(params
	// .getString(EloanConstants.DOC_STATUS));
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
	// EloanConstants.DOC_STATUS, docStatus);
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "innerAudit",
	// "Y");
	//
	// SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	// Calendar beginDate = Calendar.getInstance();
	// beginDate.setTime(new Date());
	// beginDate.set(Calendar.DAY_OF_MONTH, 1);
	//
	// pageSetting.addSearchModeParameters(SearchMode.LESS_THAN, "checkDate",
	// sdf.format(beginDate.getTime()));
	// pageSetting.addOrderBy("checkDate", true);
	//
	// Page<L192M01A> page = lms1945Service.get1945V01(pageSetting);
	//
	// // formatter
	// Map<String, IFormatter> map = new HashMap<String, IFormatter>();
	// map.put("ownBrId", new BranchNameFormatter(branchService,
	// ShowTypeEnum.ID_Name));
	// map.put("updater", new UserNameFormatter(userInfoService));
	//
	// return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	// }

	// /**
	// * 查詢分行內部查核資料
	// *
	// * @param pageSetting
	// * ISearch
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapGridResult
	// * @throws CapException
	// */
	// public CapGridResult queryViewDocEditingByCheckDate(ISearch pageSetting,
	// PageParameters params, Component parent) throws CapException {
	// // 增加search 條件
	// String branch = MegaSSOSecurityContext.getUnitNo();
	//
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
	// branch);
	//
	// String docStatus = Util.nullToSpace(params
	// .getString(EloanConstants.DOC_STATUS));
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
	// EloanConstants.DOC_STATUS, docStatus);
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "innerAudit",
	// "Y");
	//
	// String checkDateStart = params.getString("checkDateStart");
	// String checkDataEnd = params.getString("checkDateEnd");
	//
	// pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
	// "checkDate", checkDateStart);
	// pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS,
	// "checkDate", checkDataEnd);
	//
	// pageSetting.addOrderBy("checkDate", true);
	//
	// Page<L192M01A> page = lms1945Service.get1945V01(pageSetting);
	//
	// // formatter
	// Map<String, IFormatter> map = new HashMap<String, IFormatter>();
	// map.put("ownBrId", new BranchNameFormatter(branchService,
	// ShowTypeEnum.ID_Name));
	// map.put("updater", new UserNameFormatter(userInfoService));
	//
	// return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	// }

	/**
	 * 依篩選條件，找出符合的稽核工作底稿資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryViewDocByQueryDialog(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 增加search 條件
		String branch = MegaSSOSecurityContext.getUnitNo();

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				branch);

		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.DOC_STATUS, docStatus);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "innerAudit",
				"Y");

		String checkDateStart = params.getString("checkDateStart");
		String checkDataEnd = params.getString("checkDateEnd");
		if (!"".equals(checkDateStart)) {
			pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
					"checkDate", checkDateStart);
		}

		if (!"".equals(checkDataEnd)) {
			pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS,
					"checkDate", checkDataEnd);
		}

		String custId = params.getString("custId", "").toUpperCase();
		if (!"".equals(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}

		String shtType = params.getString("shtType", "");
		if (!"".equals(shtType)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "shtType",
					shtType);
		}

		String updater = params.getString("updater", "");
		if (!"".equals(updater)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "updater",
					updater);
		}

//		pageSetting.addOrderBy("checkDate", true);

		Page<L192M01A> page = lms1945Service.get1945V01(pageSetting);

		// formatter
		Map<String, IFormatter> map = new HashMap<String, IFormatter>();
		map.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name));
		map.put("updater", new UserNameFormatter(userInfoService));

		return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	}

	/**
	 * 查詢待覆核稽核工作底稿資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryViewDocApproved(ISearch pageSetting,
			PageParameters params) throws CapException {

		String branch = MegaSSOSecurityContext.getUnitNo();

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				branch);

		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.DOC_STATUS, docStatus);

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "innerAudit",
				"Y");
		pageSetting.addOrderBy("checkDate", true);

		Page<L192M01A> page = lms1945Service.get1945V01(pageSetting);

		// formatter
		Map<String, IFormatter> map = new HashMap<String, IFormatter>();
		map.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name));
		map.put("updater", new UserNameFormatter(userInfoService));
		return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	}

	// /**
	// * 查詢已覆核
	// *
	// * @param pageSetting
	// * ISearch
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapGridResult
	// * @throws CapException
	// */
	// public CapGridResult queryViewDocFinishByCheckDate(ISearch pageSetting,
	// PageParameters params, Component parent) throws CapException {
	//
	// String branch = MegaSSOSecurityContext.getUnitNo();
	//
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
	// branch);
	//
	// String docStatus = Util.nullToSpace(params
	// .getString(EloanConstants.DOC_STATUS));
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
	// EloanConstants.DOC_STATUS, docStatus);
	//
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "innerAudit",
	// "Y");
	//
	// String checkDateStart = params.getString("checkDateStart");
	// String checkDataEnd = params.getString("checkDateEnd");
	//
	// pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
	// "checkDate", checkDateStart);
	// pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS,
	// "checkDate", checkDataEnd);
	//
	// pageSetting.addOrderBy("checkDate", true);
	// Page<L192M01A> page = lms1945Service.get1945V01(pageSetting);
	//
	// // formatter
	// Map<String, IFormatter> map = new HashMap<String, IFormatter>();
	// map.put("ownBrId", new BranchNameFormatter(branchService,
	// ShowTypeEnum.ID_Name));
	// map.put("updater", new UserNameFormatter(userInfoService));
	// return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	// }

	// /**
	// * 查詢擔保品資料
	// *
	// * @param pageSetting
	// * ISearch
	// * @param params
	// * PageParameters
	// * @param parent
	// * Component
	// * @return CapGridResult
	// * @throws CapException
	// */
	// public CapGridResult queryL192S02A(ISearch pageSetting,
	// PageParameters params, Component parent) throws CapException {
	//
	// logger.debug("mainId : " + params.getString(EloanConstants.MAIN_ID));
	//
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
	// EloanConstants.MAIN_ID,
	// params.getString(EloanConstants.MAIN_ID));
	//
	// Page<L192S02A> page = lms1945Service.getL192S02A(pageSetting);
	//
	// return new CapGridResult(page.getContent(), page.getTotalRow());
	// }
}