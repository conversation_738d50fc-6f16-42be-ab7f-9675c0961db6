/* 
 * LMS1305S05Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 說明(綜合評估往來彙總)-陳復述案與其他專用
 * </pre>
 * @since  2011/5/18
 * <AUTHOR>
 * @version 
 * @version <ul>
 *           <li>2011/5/18,<PERSON>,new
 *          </ul>
 */
public class LMS1301S05Panel extends Panel {

	public LMS1301S05Panel(String id) {
		super(id);
	}
	
	public LMS1301S05Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);

	}

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		new LMS1301S05_Panel("lms1305s05_panel").processPanelData(model, params);
	}
}
