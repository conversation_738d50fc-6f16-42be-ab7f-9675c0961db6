/* 
 * COLGridService.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

/**
 * <pre>
 * Generic COl Grid Service
 * </pre>
 * 
 * @since 2012/3/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/3/30,iristu,new
 *          </ul>
 */
public interface LMSGridService extends ICapService {

	<T> Page<T> findPage(Class<T> clazz, ISearch search);

}
