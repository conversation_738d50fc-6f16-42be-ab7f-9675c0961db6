package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF691;
import com.mega.eloan.lms.mfaloan.service.MisELF691Service;

@Service
public class MisELF691ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF691Service {

	@Override
	public ELF691 getLastByCustIdQItem(String custId, String dupNo, String qItem) {
		Map<String, Object> rowData = this.getJdbc().queryForMap(
				"MIS.ELF691.getLastByCustIdQItem",
				new Object[] { custId, dupNo, qItem });
		return toELF691(rowData);
	}

	@Override
	public List<ELF691> getQueryRecord(String custId, String dupNo, String qItem, String brn, String key1, String key2, String date) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF691.getQueryRecord", new Object[] { custId, dupNo, qItem, brn, key1, key2, date });
		return toELF691(rowData);
	}

	private ELF691 toELF691(Map<String, Object> rowData) {
		ELF691 model = new ELF691();
		DataParse.map2Bean(rowData, model);
		return model;
	}

	private List<ELF691> toELF691(List<Map<String, Object>> rowData) {
		List<ELF691> list = new ArrayList<ELF691>();
		for (Map<String, Object> row : rowData) {
			ELF691 model = new ELF691();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public int insert(ELF691 elf691) {
		Object[] args = new Object[] { elf691.getElf691_custid(),
				elf691.getElf691_dupno(), elf691.getElf691_q_item(),
				elf691.getElf691_q_reason(), elf691.getElf691_brn(),
				elf691.getElf691_staffno(), elf691.getElf691_tmestamp(),
				 elf691.getElf691_key1(),
				 elf691.getElf691_key2(),
				 elf691.getElf691_ip(),
				elf691.getElf691_ej_tmestamp() };
		return this.getJdbc().update("MIS.ELF691.insert", args);
	}

}
