---------------------------------------------------------
-- LMS.C999A01A 覆審報告表授權檔
---------------------------------------------------------
--DROP TABLE LMS.C999A01A;
CREATE TABLE LMS.C999A01A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	PID           CHAR(32)     ,
	OWNUNIT       CHAR(3)       not null,
	OWNER         CHAR(6)      ,
	AUTHTIME      TIMESTAMP    ,
	AUTHTYP<PERSON>      CHAR(1)       not null,
	AUTHUNIT      CHAR(3)       not null,

	constraint P_C999A01A PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC999A01A01;
CREATE UNIQUE INDEX LMS.XC999A01A01 ON LMS.C999A01A   (MAINID, OWNUNIT, AUTHTYPE, AUTHUNIT);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C999A01A IS '覆審報告表授權檔';
COMMENT ON LMS.C999A01A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	PID           IS 'pid', 
	OWNUNIT       IS '授權單位', 
	OWNER         IS '授權人員', 
	AUTHTIME      IS '授權日期', 
	AUTHTYPE      IS '授權類別', 
	AUTHUNIT      IS '被授權單位'
);
