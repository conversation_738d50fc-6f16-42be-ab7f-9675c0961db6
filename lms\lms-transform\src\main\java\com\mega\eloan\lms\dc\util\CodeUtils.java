package com.mega.eloan.lms.dc.util;

import java.io.UnsupportedEncodingException;

import org.apache.wicket.util.crypt.Base64UrlSafe;

public class CodeUtils {
	private static final String ISO_8859_1 = "8859_1";

	private static final String UTF_8 = "utf-8";

	public static String encode(String str) {
		try {
			String result = new String(Base64UrlSafe.encodeBase64(str
					.getBytes()));
			return result;
		} catch (Exception ex) {
			return str;
		}
	}

	public static String decode(String encodeLightId) {
		try {
			String result = new String(Base64UrlSafe.decodeBase64(encodeLightId
					.getBytes()));
			return result;
		} catch (Exception ex) {
			return encodeLightId;
		}
	}

	public static String getISO8859String(String str)
			throws UnsupportedEncodingException {
		return new String(str.getBytes(UTF_8), ISO_8859_1);
	}

	public static String getUTF8String(String str)
			throws UnsupportedEncodingException {
		return new String(str.getBytes(ISO_8859_1), UTF_8);
	}

	private final static char[] HEX = { '0', '1', '2', '3', '4', '5', '6', '7',
			'8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };

	/**
	 * 轉成Hex字串
	 * 
	 * @param bytes
	 *            byte array
	 * @return Hex字串
	 */
	public static String toHex(byte[] bytes) {
		StringBuilder buffer = new StringBuilder();
		for (byte b : bytes) {
			buffer.append(HEX[(b >> 4) & 0xf]);
			buffer.append(HEX[b & 0xf]);
		}
		return buffer.toString();
	}

	/**
	 * Hex字串轉成byte array
	 * 
	 * @param hex
	 *            Hex字串
	 * @return byte array
	 * @throws NumberFormatException
	 *             Exception
	 */
	public static byte[] fromHex(String hex) {
		char[] chars;
		char c;
		int i;
		int j;
		byte[] bytes;
		byte b;

		chars = hex.toUpperCase().toCharArray();

		if (chars.length % 2 != 0) {
			throw new NumberFormatException("Incomplete hex value");
		}

		bytes = new byte[chars.length / 2];
		b = 0;
		j = 0;
		for (i = 0; i < chars.length; i++) {
			c = chars[i];
			if (c >= '0' && c <= '9') {
				b = (byte) ((b << 4) | (0xff & (c - '0')));
			} else if (c >= 'A' && c <= 'F') {
				b = (byte) ((b << 4) | (0xff & (c - 'A' + 10)));
			} else {
				throw new NumberFormatException("Invalid hex character: " + c);
			}
			if ((i + 1) % 2 == 0) {
				bytes[j++] = b;
				b = 0;
			}
		}

		return bytes;
	}
}
