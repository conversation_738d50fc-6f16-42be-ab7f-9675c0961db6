package com.mega.eloan.lms.fms.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.dao.C900M01HDao;
import com.mega.eloan.lms.fms.service.CLS2601Service;
import com.mega.eloan.lms.model.C900M01H;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

@Service
public class CLS2601ServiceImpl extends AbstractCapService implements
		CLS2601Service {
	private static final Logger logger = LoggerFactory
			.getLogger(CLS2601ServiceImpl.class);
	
	@Resource
	C900M01HDao c900m01hDao;

	@Resource
	TempDataService tempDataService;
	
	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C900M01H) {
					C900M01H c900m01h = (C900M01H) model;
					if (Util.isNotEmpty(c900m01h.getOid()) ) {
						c900m01h.setUpdater(user.getUserId());
						c900m01h.setUpdateTime(CapDate.getCurrentTimestamp());
					}
					c900m01hDao.save(c900m01h);
					
					if (Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(c900m01h.getMainId());
					}
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C900M01H) {
					C900M01H c900m01h = (C900M01H) model;
					if (Util.isNotEmpty(c900m01h.getOid()) ) {
						//刪除資料
						c900m01hDao.delete(c900m01h);
					}
				}
			}
		}
	}

	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == C900M01H.class) {
			return c900m01hDao.findPage(search);
		}
		return null;
	}

	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == C900M01H.class) {
			return (T) c900m01hDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public C900M01H findC900M01H_oid(String oid){
		return c900m01hDao.findByOid(oid);
	}
	
}
