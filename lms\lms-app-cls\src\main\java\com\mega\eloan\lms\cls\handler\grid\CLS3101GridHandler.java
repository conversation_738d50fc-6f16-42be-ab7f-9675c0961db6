package com.mega.eloan.lms.cls.handler.grid;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.pages.CLS3101V01Page;
import com.mega.eloan.lms.model.C310M01A;
import com.mega.eloan.lms.model.C900S02E;
import com.mega.eloan.lms.model.C900S02F;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 同一通訊處註記
 * </pre>
 * 
 * @since 2019/02/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/02/19,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("cls3101gridhandler")
public class CLS3101GridHandler extends AbstractGridHandler {

	
	@Resource
	CLSService clsService;
	@Resource
	UserInfoService userInfoService;

	@Resource
	CodeTypeService codeTypeService;
	@Resource
	BranchService branchService;
	
	private static final DateFormat S_FORMAT = new SimpleDateFormat(UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
	
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryView(ISearch pageSetting,
			PageParameters params) throws CapException {
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS3101V01Page.class);
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());	
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);		
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		if(true){ //篩選
			String custId = Util.trim(params.getString("search_custId"));
			if (Util.isNotEmpty(custId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
			}	
		}
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Page<? extends GenericBean> page =  clsService.findPage(C310M01A.class, pageSetting);
		List<C310M01A> src_list = (List<C310M01A>)page.getContent();		
		for(C310M01A c310m01a : src_list){
			Map<String, Object> row = new HashMap<String, Object>();
			LMSUtil.meta_to_map(row, c310m01a, new String[] { "oid", "mainId",
					"ownBrId", "custName", "chk_memo" });
			row.put("custId",Util.trim(c310m01a.getCustId()) + "-"+ Util.trim(c310m01a.getDupNo()));
			
			if(true){
				String cyc_mn = ""; 
				String text = "";
				if(true){
					C900S02E c900s02e = clsService.findC900S02E_mainId(c310m01a.getRefMainId());
					if(c900s02e!=null){
						text = Util.trim(c900s02e.getText());
						cyc_mn= Util.trim(StringUtils.substring(TWNDate.toAD(c900s02e.getCyc_mn()), 0, 7));
					}
				}
				row.put("cyc_mn", cyc_mn);
				row.put("text", text);
			}
			
			if(true){
				String chk_result = c310m01a.getChk_result();
				if(Util.equals("Y", chk_result)){
					chk_result = prop.getProperty("C900S02E.chk_result.Y");
				}else if(Util.equals("N", chk_result)){
					chk_result = prop.getProperty("C900S02E.chk_result.N");
				}
				row.put("chk_result", chk_result);				
			} 
			row.put("updater",
					Util.trim(userInfoService.getUserName(c310m01a.getUpdater())));
			row.put("approver",
					Util.trim(userInfoService.getUserName(c310m01a.getApprover())));
			row.put("approveTime", c310m01a.getApproveTime()==null?"":S_FORMAT.format(c310m01a.getApproveTime()));
			// ---
			list.add(row);
		}
	
		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				list, page.getTotalRow(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());
	}
	
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryText(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS3101V01Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "brNo", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		if (true) {
			Page<? extends GenericBean> page =  clsService.findPage(C900S02E.class, pageSetting);
			List<C900S02E> src_list = (List<C900S02E>)page.getContent();		
			for(C900S02E c900s02e : src_list){
				Map<String, Object> row = new HashMap<String, Object>();
				
				row.put("mainId", c900s02e.getMainId());
				row.put("rel_flag", c900s02e.getRel_flag());
				row.put("cyc_mn", StringUtils.substring(TWNDate.toAD(c900s02e.getCyc_mn()), 0, 7) ); 
				row.put("text", c900s02e.getText());
				if(true){
					String chk_result = c900s02e.getChk_result();
					if(Util.equals("Y", chk_result)){
						chk_result = prop.getProperty("C900S02E.chk_result.Y");
					}else if(Util.equals("N", chk_result)){
						chk_result = prop.getProperty("C900S02E.chk_result.N");
					}
					row.put("chk_result", chk_result); 
				}
				row.put("approveTime", c900s02e.getApproveTime());
				list.add(row);
			}
		} 
		return new CapMapGridResult(list, list.size());
	}
	
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryC900S02F(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		if (true) {
			Page<? extends GenericBean> page =  clsService.findPage(C900S02F.class, pageSetting);
			List<C900S02F> src_list = (List<C900S02F>)page.getContent();		
			for(C900S02F c900s02f : src_list){
				Map<String, Object> row = new HashMap<String, Object>();
				
				row.put("rel_flag", Util.equals("N", c900s02f.getRel_flag())?CrsUtil.C900S02F_REL_FLAG_SHOW:"");				
				row.put("rel_custId", c900s02f.getRel_custId());
				row.put("rel_dupNo", c900s02f.getRel_dupNo());
				row.put("rel_cname", c900s02f.getRel_cname());
				
				list.add(row);
			}
		} 
		return new CapMapGridResult(list, list.size());
	}
}
