/*
 * CapFormHandleOpStep.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.operation.step;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.action.IAction;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.handler.FormHandler;
import tw.com.iisi.cap.response.IResult;

/**
 * <pre>
 * CapFormHandleOpStep
 * 接續Operation步驟
 * </pre>
 * 
 * @since 2010/7/23
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/23,iristu,new
 *          </ul>
 */
public class CapFormHandleOpStep extends AbstractCustomizeOpStep {

    /*
     * 執行後繼續下一步
     * 
     * @see tw.com.iisi.cap.operation.OperationStep#execute(com.iisigroup.cap.component.PageParameters, tw.com.iisi.cap.handler.FormHandler, tw.com.iisi.cap.response.IResult)
     */
    @Override
    public String execute(PageParameters params, FormHandler handler, IResult result) throws CapException {
        IResult rtn = null;
        String actionType = params.getString(FormHandler.FORM_ACTION);
        setName(handler.getPluginName() + "." + actionType);
        IAction action = handler.getAction(actionType);
        rtn = action.doWork(params);
        if (result == null) {
            result = rtn;
        } else if (rtn != null) {
            result.add(rtn);
        }
        return NEXT;
    }

}// ~
