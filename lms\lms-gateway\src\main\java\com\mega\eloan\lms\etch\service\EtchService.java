/* 
 * EtchService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.etch.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * ETCH Service
 * </pre>
 * 
 * @since 2012/10/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/25,Fantasy,new
 *          </ul>
 */
public interface EtchService {
	
	
	Map<String, Object> findById(String id);

	/**
	 * MIS.MSG_001 資料日期&查詢日期
	 * 
	 * @param qid
	 * @return
	 */
	Map<String, Object> getDate(String qid);

	/**
	 * MIS.MSG_001 資料日期&查詢日期
	 * 
	 * @param qid
	 * @param txid
	 * @return
	 */
	Map<String, Object> getDate(String qid, String txid);

	/**
	 * MIS.MSG_001 資料查詢日期
	 * 
	 * @param qid
	 * @return
	 */
	String getMSG001QDate(String qid);

	/**
	 * MIS.MSG_001 資料查詢日期
	 * 
	 * @param qid
	 * @param txid
	 * @return
	 */
	String getMSG001QDate(String qid, String txid);

	/**
	 * MIS.MSG_004 退票日期
	 * 
	 * @param qid
	 * @return
	 */
	Map<String, Object> getMSG004BcDate(String qid);

	/**
	 * MIS.MSG_004 大額退票、拒絕往來情形
	 * 
	 * @param qid
	 * @return
	 */
	Map<String, Object> getMSG001Data(String qid);

	/**
	 * MIS.MSG_001 退票日期
	 * 
	 * @param qid
	 * @return
	 */
	Map<String, Object> getMSG001RejectDate(String qid);

	/**
	 * MIS.MSG_001 退票查詢-S_ConnectETCH
	 * 
	 * @param qid
	 * @return
	 */
	List<Map<String, Object>> getMSG001Info1(String qid);

	/**
	 * MIS.H_RESULT 回覆訊息HTML
	 * 
	 * @param qid
	 * @return
	 */
	List<Map<String, Object>> getHRESULT(String qid, String txid, String qDate);

	Map<String, Object> getLatestEtch4111QueryLog(String custId);

	Map<String, Object> getEtchInqueryData(String seqId, String queryDate, String prodId, String queryCustId);

	Map<String, Object> getEtchInqueryData(String prodId, String queryDate, String queryCustId);
}
