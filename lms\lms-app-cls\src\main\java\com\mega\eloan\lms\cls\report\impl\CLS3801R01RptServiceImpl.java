package com.mega.eloan.lms.cls.report.impl;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.report.CLS3801R01RptService;
import com.mega.eloan.lms.cls.service.CLS3801Service;
import com.mega.eloan.lms.model.C103M01A;
import com.mega.eloan.lms.model.C103M01E;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

@Service("cls3801r01rptservice")
public class CLS3801R01RptServiceImpl extends AbstractReportService implements
		CLS3801R01RptService {

	private static final DateFormat S_FORMAT = new SimpleDateFormat(
			UtilConstants.DateFormat.YYYY_MM_DD);

	@Resource
	BranchService branchService;

	@Resource
	LMSService lmsService;

	@Resource
	CLS3801Service cls3801Service;

	@Resource
	CodeTypeService codetypeService;

	@Override
	public String getReportTemplateFileName() {
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		// 測試用
		return "report/cls/CLS3801R01_" + locale.toString() + ".rpt";
	}

	@Override
	public void setReportData(ReportGenerator rptGenerator,
			PageParameters params) throws CapException, ParseException {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C103M01A c103m01a = null;

		String branchName = null;
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = null;
		try {
			locale = LocaleContextHolder.getLocale();
			if (locale == null)
				locale = Locale.getDefault();

			CodeTypeFormatter cityFormat = new CodeTypeFormatter(
					codetypeService, "counties",
					CodeTypeFormatter.ShowTypeEnum.Desc);

			c103m01a = cls3801Service.findModelByOid(C103M01A.class, mainOid);

			String apprid = "";
			String recheckid = "";
			String bossid = "";
			String managerid = "";

			List<C103M01E> c103m01elist = cls3801Service.findC103M01E(mainId);

			for (C103M01E c103m01e : c103m01elist) {
				// 要加上人員代碼
				String type = Util.trim(c103m01e.getStaffJob());
				String userId = Util.trim(c103m01e.getStaffNo());
				String value = Util.trim(lmsService.getUserName(userId));
				if ("L1".equals(type)) {
					apprid = userId + " " + value;
				} else if ("L3".equals(type)) {
					bossid = bossid + userId + " " + value + "<br/>";
				} else if ("L4".equals(type)) {
					recheckid = userId + " " + value;
				} else if ("L5".equals(type)) {
					managerid = userId + " " + value;
				}
			}
			branchName = Util.nullToSpace(branchService.getBranchName(Util
					.nullToSpace(c103m01a.getOwnBrId())));
			String idDup = Util.nullToSpace(c103m01a.getCustId()) + "-"
					+ Util.nullToSpace(c103m01a.getDupNo());
			String custname = Util.nullToSpace(c103m01a.getCustName());

			StringBuilder address = new StringBuilder();// 聯絡地址
			String city = cityFormat.reformat(CapString.trimNull(c103m01a
					.getCity()));
			String cityArea = new CodeTypeFormatter(codetypeService, "counties"
					+ CapString.trimNull(c103m01a.getCity()),
					CodeTypeFormatter.ShowTypeEnum.Desc).reformat(CapString
					.trimNull(c103m01a.getCityarea()));
			if (Util.equals(city, cityArea)) {// [縣/市]跟[區/市/鄉/鎮]一樣的時候就不顯示[區/市/鄉/鎮]
				cityArea = "";
			}
			address.append(city).append(cityArea)
					.append(CapString.trimNull(c103m01a.getAddress()));

			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap.put("C103M01A.Mainid",
					Util.trim(c103m01a.getMainId()));
			rptVariableMap.put("C103M01A.CUSTID", idDup);
			rptVariableMap.put("C103M01A.CUSTNAME", custname);
			rptVariableMap.put("C103M01A.INTERDATE", Util.isNotEmpty(c103m01a.getInterDate()) ?
					S_FORMAT.format(c103m01a.getInterDate()) : "" );// 訪談日期
			rptVariableMap.put("C103M01A.INTERPLACE",
					Util.trim(c103m01a.getInterPlace()));// 訪談地點
			rptVariableMap.put("C103M01A.INTERPER",
					Util.trim(c103m01a.getInterPer()));// 分行洽談人
			rptVariableMap.put("C103M01A.COMPANY",
					Util.trim(c103m01a.getCompany()));
			rptVariableMap.put("C103M01A.ADDRESS", address.toString());// 聯絡地址
			rptVariableMap
					.put("C103M01A.EMAIL", Util.trim(c103m01a.getEmail()));

			rptVariableMap.put("C103M01A.CUSTTYPE",
					Util.trim(c103m01a.getCustType()));// 客戶類型 N:新戶 O:舊戶

			if (true) {

				// 舊戶目前往來業務
				String oldCustBusiness = Util.trim(c103m01a
						.getOldCustBusiness());
				rptVariableMap.put("C103M01A.OLDCUSTBUSINESS.1",
						oldCustBusiness.contains("1") ? "Y" : "");// 授信
				rptVariableMap.put("C103M01A.OLDCUSTBUSINESS.2",
						oldCustBusiness.contains("2") ? "Y" : "");// 存款
				rptVariableMap.put("C103M01A.OLDCUSTBUSINESS.3",
						oldCustBusiness.contains("3") ? "Y" : "");// 理財
				rptVariableMap.put("C103M01A.OLDCUSTBUSINESS.4",
						oldCustBusiness.contains("4") ? "Y" : "");// 外匯
				rptVariableMap.put("C103M01A.OLDCUSTBUSINESS.5",
						oldCustBusiness.contains("5") ? "Y" : "");// 其他

				// 潛在商機-本行
				String potentialMain = Util.trim(c103m01a.getPotentialMain());
				rptVariableMap.put("C103M01A.POTENTIALMAIN.1",
						potentialMain.contains("1") ? "Y" : "");// 房貸
				rptVariableMap.put("C103M01A.POTENTIALMAIN.2",
						potentialMain.contains("2") ? "Y" : "");// 信貸
				rptVariableMap.put("C103M01A.POTENTIALMAIN.3",
						potentialMain.contains("3") ? "Y" : "");// 理財
				rptVariableMap.put("C103M01A.POTENTIALMAIN.4",
						potentialMain.contains("4") ? "Y" : "");// 信用卡
				rptVariableMap.put("C103M01A.POTENTIALMAIN.5",
						potentialMain.contains("5") ? "Y" : "");// 信託
				rptVariableMap.put("C103M01A.POTENTIALMAIN.6",
						potentialMain.contains("6") ? "Y" : "");// 存款
				rptVariableMap.put("C103M01A.POTENTIALMAIN.7",
						potentialMain.contains("7") ? "Y" : "");// 其他

				// 潛在商機-共銷
				String potentialCommon = Util.trim(c103m01a
						.getPotentialCommon());
				rptVariableMap.put("C103M01A.POTENTIALCOMMON.1",
						potentialCommon.contains("1") ? "Y" : "");// 產物保險
				rptVariableMap.put("C103M01A.POTENTIALCOMMON.2",
						potentialCommon.contains("2") ? "Y" : "");// 投信基金投資
				rptVariableMap.put("C103M01A.POTENTIALCOMMON.3",
						potentialCommon.contains("3") ? "Y" : "");// 開立證券戶

				// 商機轉介
				String insurance = Util.trim(c103m01a.getInsurance());
				rptVariableMap.put("C103M01A.INSURANCE.1",
						insurance.contains("1") ? "Y" : "");// 住火險
				rptVariableMap.put("C103M01A.INSURANCE.2",
						insurance.contains("2") ? "Y" : "");// 車險
				rptVariableMap.put("C103M01A.INSURANCE.3",
						insurance.contains("3") ? "Y" : "");// 傷害險
				rptVariableMap.put("C103M01A.INSURANCE.4",
						insurance.contains("4") ? "Y" : "");// 健康險

			}

			rptVariableMap.put("C103M01A.OTHERBUSINESS",
					Util.trim(c103m01a.getOtherBusiness()));// 舊戶目前往來業務-其他
			rptVariableMap.put("C103M01A.POTENTIALMAINOTHER",
					Util.trim(c103m01a.getPotentialMainOther()));// 潛在商機-本行-其他
			rptVariableMap.put("C103M01A.TRANSPOTENTIAL",
					Util.trim(c103m01a.getTransPotential()));// 商機轉介 Y:是 N:否
			rptVariableMap.put("C103M01A.NOTTRANSREASON",
					Util.trim(c103m01a.getNotTransReason()));
			rptVariableMap.put("C103M01A.INTERVIEWMEMO",
					Util.trim(c103m01a.getInterviewMemo()));
			rptVariableMap.put("C103M01A.TRACKINGITEM",
					Util.trim(c103m01a.getTrackingItem()));
			rptVariableMap.put("C103M01A.DIRECTOROPINION",
					Util.trim(c103m01a.getDirectorOpinion()));

			rptVariableMap.put("C103M01E.APPRID", apprid);
			rptVariableMap.put("C103M01E.RECHECKID", recheckid);
			rptVariableMap.put("C103M01E.BOSSID", bossid);
			rptVariableMap.put("C103M01E.MANAGERID", managerid);

			rptGenerator.setLang(locale);
			rptGenerator.setVariableData(rptVariableMap);
		} finally {

		}
	}
}
