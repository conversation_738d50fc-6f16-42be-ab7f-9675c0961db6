package com.mega.eloan.lms.base.service;

import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONObject;

import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01E;
import com.mega.eloan.lms.model.C160S01D;
import com.mega.eloan.lms.model.C340M01A;

import tw.com.iisi.cap.exception.CapException;

public interface ContractDocService {
	public void init_C340Relate(C340M01A c340m01a, String tabMainIds);
	
	public JSONObject ploan_sendSigningContract(C340M01A c340m01a) throws CapException;
	
	public JSONObject ploan_discardContract(C340M01A c340m01a);

	public String get_ploanIdentityType(C122M01A c122m01a);
	public JSONObject ploan_sendCtrTypeC(C340M01A c340m01a, byte[] mergedPdf, C160S01D c160s01d, C122M01A c122m01a, C122M01E c122m01e, C120M01A c120m01a, C120S01A c120s01a) throws CapException;
	public JSONObject ploan_getInfo(String key1, String key2, String key3, String key4, String key5);

	/**
	 * 組合「線上對保契約」內之「利率」，原始來源為 CLS3401ServiceImpl :: init_C340RelateCtrTypeA(...)
	 * @param jsContent
	 * @return
	 */
	List<String> geCtrTypeA_rateDesc(JSONObject jsContent);
	List<String> geCtrTypeB_rateDesc(JSONObject jsContent);
	
	Map<String, String> get_ploan_lendingPlanInfo_showOption();	

	public Map<String, String> is_cntrNo_belong_co70647919_c101(String tabMainId);
}
