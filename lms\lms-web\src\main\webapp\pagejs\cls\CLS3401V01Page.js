var _handler = "cls3401m01formhandler";
var cntrNo_grid_height = 250;
$(function(){	
	
	//default div
	var $gridview = $("#gridview").iGrid({
        handler: "cls3401gridhandler",
        height: 350,
        rowNum: 15,
        shrinkToFit: false,
        multiselect: false,  
        sortname: 'approveTime|custId' ,
       	sortorder: 'desc|asc' ,
        postData: {
            formAction: "queryView",
            docStatus : viewstatus	
        },
        colModel: [
            {
            	colHeader: "",name: 'oid', hidden: true
	        }, {
	        	colHeader: "",name: 'mainId', hidden: true
	        }, {
	            colHeader: i18n.cls3401v01["C340M01A.custId"], 
	            align: "left", width: 85, sortable: true, name: 'custId',
				onclick : openDoc, formatter : 'click'
	        }, {
	            colHeader: i18n.cls3401v01["C340M01A.custName"], 
	            align: "left", width: 90, sortable: true, name: 'custName'	        
	        }, {
	            colHeader: i18n.cls3401v01["C340M01A.caseNo"],  //案號
	            align: "left", width: 180, sortable: true, name: 'caseNo'
	        }, {
                colHeader: i18n.cls3401v01["C340M01A.ctrType"], //"契約書種類",
	            align: "left", width: 100, sortable: true, name: 'ctrType'
	        }, {
	            colHeader: i18n.cls3401v01["C340M01A.contrNumber"],  //契約書編號
	            align: "left", width: 100, sortable: true, name: 'contrNumber'
	        }, {
	        	colHeader: i18n.cls3401v01["C340M01A.createTime"], //契約建檔時間(若1份簽報書，產出 N次契約，以建檔時間去區別)
	            align: "left", width: 99, sortable: true, name: 'createTime'
	        }, {
	        	colHeader: i18n.cls3401v01["doc.lastUpdater"], //最後異動者
	            align: "left", width: 80, sortable: true, name: 'updater'
	        }, {
	        	colHeader: i18n.cls3401v01["C340M01A.updateTime"], //最後異動時間
	            align: "left", width: 99, sortable: true, name: 'updateTime'
	        }, {
                name: 'docURL', hidden:true
            }
	     ],
		ondblClickRow : function(rowid){
			openDoc(null, null, $gridview.getRowData(rowid));
		}
    });
	
	var CntrNo_ctrType_1_Grid = $('#CntrNo_ctrType_1_Grid').iGrid({
        handler: 'cls3401gridhandler', //設定handler
        height: cntrNo_grid_height, //設定高度
        width: 840,
        postData: {
            formAction: "queryCntrNo_ctrType_1"            
        },
        needPager: false,
        shrinkToFit: false,
        multiselect: false, 
        colModel: [{
        	colHeader: i18n.cls3401v01["C340M01A.custId"], align: "left",width: 85, sortable: false, name: 'custId'
        }, {
        	colHeader: i18n.cls3401v01["C340M01A.custName"], align: "left",width: 100, sortable: false, name: 'custName'
        }, {
        	colHeader: i18n.cls3401v01["C340M01A.caseNo"], align: "left", width: 170, sortable: false, name: 'caseNo'
        }, {
        	colHeader: i18n.cls3401v01["label.endDate"],align: "left", width: 100, sortable: false, name: 'endDate'
        }, {
            colHeader: i18n.cls3401v01["C340M01B.cntrNo"], align: "left",width: 100, sortable: false, name: 'cntrNo'
        }, {
            colHeader: i18n.cls3401v01["label.property"], align: "left",width: 100, sortable: false, name: 'property'
        }, {
            colHeader: i18n.cls3401v01["label.currentApplyAmt"], align: "right",width: 100, sortable: false, name: 'currentApplyAmt'
        }, {
        	name: 'caseMainId',
        	hidden: true
    	}, {
        	name: 'tabMainId',
        	hidden: true
    	}],
        loadComplete: function () {            	
        	if( CntrNo_ctrType_1_Grid.getGridParam("records")>0){
        		//
        	}else{
        		var custId = CntrNo_ctrType_1_Grid.getGridParam("postData")['custId'];
				if(custId && custId.length>1){
	        		API.showMessage(i18n.def["grid.emptyrecords"]);
				}else{
					//第一次進入頁面
				}
        	}
        }  
	});

	var CntrNo_ctrType_2_Grid = $('#CntrNo_ctrType_2_Grid').iGrid({
        handler: 'cls3401gridhandler', //設定handler
        height: cntrNo_grid_height, //設定高度
        width: 840,
        postData: {
            formAction: "queryCntrNo_ctrType_2"            
        },
        needPager: false,
        shrinkToFit: false,
        multiselect: false, 
        colModel: [{
        	colHeader: i18n.cls3401v01["C340M01A.custId"], align: "left",width: 85, sortable: false, name: 'custId'
        }, {
        	colHeader: i18n.cls3401v01["C340M01A.custName"], align: "left",width: 100, sortable: false, name: 'custName'
        }, {
        	colHeader: i18n.cls3401v01["C340M01A.caseNo"], align: "left", width: 170, sortable: false, name: 'caseNo'
        }, {
        	colHeader: i18n.cls3401v01["label.endDate"],align: "left", width: 100, sortable: false, name: 'endDate'
        }, {
            colHeader: i18n.cls3401v01["C340M01B.cntrNo"], align: "left",width: 100, sortable: false, name: 'cntrNo'
        }, {
            colHeader: i18n.cls3401v01["label.property"], align: "left",width: 100, sortable: false, name: 'property'
        }, {
            colHeader: i18n.cls3401v01["label.currentApplyAmt"], align: "right",width: 100, sortable: false, name: 'currentApplyAmt'
        }, {
        	name: 'caseMainId',
        	hidden: true
    	}, {
        	name: 'tabMainId',
        	hidden: true
    	}],
        loadComplete: function () {            	
        	if( CntrNo_ctrType_2_Grid.getGridParam("records")>0){
        		//
        	}else{
        		var custId = CntrNo_ctrType_2_Grid.getGridParam("postData")['custId'];
				if(custId && custId.length>1){
	        		API.showMessage(i18n.def["grid.emptyrecords"]);
				}else{
					//第一次進入頁面
				}
        	}
        }  
	});
	
	var CntrNo_ctrType_3_Grid = $('#CntrNo_ctrType_3_Grid').iGrid({
        handler: 'cls3401gridhandler', //設定handler
        height: cntrNo_grid_height, //設定高度
        width: 840,
        postData: {
            formAction: "queryCntrNo_ctrType_3"            
        },
        needPager: false,
        shrinkToFit: false,
        multiselect: false, 
        colModel: [{
        	colHeader: i18n.cls3401v01["C340M01A.custId"], align: "left",width: 85, sortable: false, name: 'custId'
        }, {
        	colHeader: i18n.cls3401v01["C340M01A.custName"], align: "left",width: 100, sortable: false, name: 'custName'
        }, {
        	colHeader: i18n.cls3401v01["C340M01A.caseNo"], align: "left", width: 170, sortable: false, name: 'caseNo'
        }, {
        	colHeader: i18n.cls3401v01["label.endDate"],align: "left", width: 100, sortable: false, name: 'endDate'
        }, {
            colHeader: i18n.cls3401v01["C340M01B.cntrNo"], align: "left",width: 100, sortable: false, name: 'cntrNo'
        }, {
            colHeader: i18n.cls3401v01["label.property"], align: "left",width: 100, sortable: false, name: 'property'
        }, {
            colHeader: i18n.cls3401v01["label.currentApplyAmt"], align: "right",width: 100, sortable: false, name: 'currentApplyAmt'
        }, {
        	name: 'caseMainId',
        	hidden: true
    	}, {
        	name: 'tabMainId',
        	hidden: true
    	}],
        loadComplete: function () {            	
        	if( CntrNo_ctrType_3_Grid.getGridParam("records")>0){
        		//
        	}else{
        		var custId = CntrNo_ctrType_3_Grid.getGridParam("postData")['custId'];
				if(custId && custId.length>1){
	        		API.showMessage(i18n.def["grid.emptyrecords"]);
				}else{
					//第一次進入頁面
				}
        	}
        }  
	});
	
	var CntrNo_ctrType_A_Grid = $('#CntrNo_ctrType_A_Grid').iGrid({
               handler: 'cls3401gridhandler', //設定handler
               height: cntrNo_grid_height, //設定高度
               width: 840,
               postData: {
                   formAction: "queryCntrNo_ctrType_A"
               },
               needPager: false,
               shrinkToFit: false,
               multiselect: false,
               colModel: [{
               	colHeader: i18n.cls3401v01["C340M01A.custId"], align: "left",width: 85, sortable: false, name: 'custId'
               }, {
               	colHeader: i18n.cls3401v01["C340M01A.custName"], align: "left",width: 100, sortable: false, name: 'custName'
               }, {
               	colHeader: i18n.cls3401v01["C340M01A.caseNo"], align: "left", width: 170, sortable: false, name: 'caseNo'
               }, {
               	colHeader: i18n.cls3401v01["label.endDate"],align: "left", width: 100, sortable: false, name: 'endDate'
               }, {
                   colHeader: i18n.cls3401v01["C340M01B.cntrNo"], align: "left",width: 100, sortable: false, name: 'cntrNo'
               }, {
                   colHeader: i18n.cls3401v01["label.property"], align: "left",width: 100, sortable: false, name: 'property'
               }, {
                   colHeader: i18n.cls3401v01["label.currentApplyAmt"], align: "right",width: 100, sortable: false, name: 'currentApplyAmt'
               }, {
               	name: 'caseMainId',
               	hidden: true
           	}, {
               	name: 'tabMainId',
               	hidden: true
           	}],
               loadComplete: function () {
               	if( CntrNo_ctrType_A_Grid.getGridParam("records")>0){
               		//
               	}else{
               		var custId = CntrNo_ctrType_A_Grid.getGridParam("postData")['custId'];
       				if(custId && custId.length>1){
       	        		API.showMessage(i18n.def["grid.emptyrecords"]);
       				}else{
       					//第一次進入頁面
       				}
               	}
               }
       	});

    var CntrNo_ctrType_B_Grid = $('#CntrNo_ctrType_B_Grid').iGrid({
            handler: 'cls3401gridhandler', //設定handler
            height: cntrNo_grid_height, //設定高度
            width: 840,
            postData: {
                formAction: "queryCntrNo_ctrType_B"
            },
            needPager: false,
            shrinkToFit: false,
            multiselect: false,
            colModel: [{
            	colHeader: i18n.cls3401v01["C340M01A.custId"], align: "left",width: 85, sortable: false, name: 'custId'
            }, {
            	colHeader: i18n.cls3401v01["C340M01A.custName"], align: "left",width: 100, sortable: false, name: 'custName'
            }, {
            	colHeader: i18n.cls3401v01["C340M01A.caseNo"], align: "left", width: 170, sortable: false, name: 'caseNo'
            }, {
            	colHeader: i18n.cls3401v01["label.endDate"],align: "left", width: 100, sortable: false, name: 'endDate'
            }, {
                colHeader: i18n.cls3401v01["C340M01B.cntrNo"], align: "left",width: 100, sortable: false, name: 'cntrNo'
            }, {
                colHeader: i18n.cls3401v01["label.property"], align: "left",width: 100, sortable: false, name: 'property'
            }, {
                colHeader: i18n.cls3401v01["label.currentApplyAmt"], align: "right",width: 100, sortable: false, name: 'currentApplyAmt'
            }, {
            	name: 'caseMainId',
            	hidden: true
        	}, {
            	name: 'tabMainId',
            	hidden: true
        	}],
            loadComplete: function () {
            	if( CntrNo_ctrType_B_Grid.getGridParam("records")>0){
            		//
            	}else{
            		var custId = CntrNo_ctrType_B_Grid.getGridParam("postData")['custId'];
    				if(custId && custId.length>1){
    	        		API.showMessage(i18n.def["grid.emptyrecords"]);
    				}else{
    					//第一次進入頁面
    				}
            	}
            }
    	});
	
	var CntrNo_ctrType_S_Grid = $('#CntrNo_ctrType_S_Grid').iGrid({
        handler: 'cls3401gridhandler', //設定handler
        height: cntrNo_grid_height, //設定高度
        width: 920,
        postData: {
            formAction: "queryCntrNo_ctrType_S"
        },
        needPager: false,
        shrinkToFit: true,
        multiselect: false,
        colModel: [{
        	colHeader: i18n.cls3401v01["C340M01A.custId"], align: "left",width: 85, sortable: false, name: 'custId'
        }, {
        	colHeader: i18n.cls3401v01["C340M01A.custName"], align: "left",width: 80, sortable: false, name: 'custName'
        }, {
        	colHeader: i18n.cls3401v01["C340M01A.caseNo"], align: "left", width: 130, sortable: false, name: 'caseNo'
        }, {
        	colHeader: i18n.cls3401v01["label.endDate"],align: "left", width: 80, sortable: false, name: 'endDate'
        }, {
            colHeader: i18n.cls3401v01["C340M01B.cntrNo"], align: "left",width: 100, sortable: false, name: 'cntrNo'
        }, {
            colHeader: i18n.cls3401v01["label.s01a_custId"], align: "left",width: 100, sortable: false, name: 's01a_custId'
        }, {
            colHeader: i18n.cls3401v01["label.s01a_custName"], align: "left",width: 80, sortable: false, name: 's01a_custName'
        }, {
            colHeader: i18n.cls3401v01["label.s01a_custPos"], align: "left",width: 80, sortable: false, name: 's01a_custPos'
        }, {    	
            colHeader: i18n.cls3401v01["label.property"], align: "left",width: 60, sortable: false, name: 'property'
        }, {
            colHeader: i18n.cls3401v01["label.currentApplyAmt"], align: "right",width: 70, sortable: false, name: 'currentApplyAmt'        
        }, {
        	name: 'caseMainId',
        	hidden: true
    	}, {
        	name: 'tabMainId',
        	hidden: true
    	}],
        loadComplete: function () {
        	if( CntrNo_ctrType_S_Grid.getGridParam("records")>0){
        		//
        	}else{
        		var custId = CntrNo_ctrType_S_Grid.getGridParam("postData")['custId'];
				if(custId && custId.length>1){
	        		API.showMessage(i18n.def["grid.emptyrecords"]);
				}else{
					//第一次進入頁面
				}
        	}
        }
	});
	
	var CntrNo_ctrType_L_Grid = $('#CntrNo_ctrType_L_Grid').iGrid({
        handler: 'cls3401gridhandler', //設定handler
        height: cntrNo_grid_height, //設定高度
        width: 840,
        postData: {
            formAction: "queryCntrNo_ctrType_L"
        },
        needPager: false,
        shrinkToFit: false,
        multiselect: false,
        colModel: [{
        	colHeader: i18n.cls3401v01["C340M01A.custId"], align: "left",width: 85, sortable: false, name: 'custId'
        }, {
        	colHeader: i18n.cls3401v01["C340M01A.custName"], align: "left",width: 100, sortable: false, name: 'custName'
        }, {
        	colHeader: i18n.cls3401v01["C340M01A.caseNo"], align: "left", width: 170, sortable: false, name: 'caseNo'
        }, {
        	colHeader: i18n.cls3401v01["label.endDate"],align: "left", width: 100, sortable: false, name: 'endDate'
        }, {
            colHeader: i18n.cls3401v01["C340M01B.cntrNo"], align: "left",width: 100, sortable: false, name: 'cntrNo'
        }, {
            colHeader: i18n.cls3401v01["label.property"], align: "left",width: 100, sortable: false, name: 'property'
        }, {
            colHeader: i18n.cls3401v01["label.currentApplyAmt"], align: "right",width: 100, sortable: false, name: 'currentApplyAmt'
        }, {
        	name: 'caseMainId',
        	hidden: true
    	}, {
        	name: 'tabMainId',
        	hidden: true
    	}],
        loadComplete: function () {
        	if( CntrNo_ctrType_L_Grid.getGridParam("records")>0){
        		//
        	}else{
        		var custId = CntrNo_ctrType_L_Grid.getGridParam("postData")['custId'];
				if(custId && custId.length>1){
	        		API.showMessage(i18n.def["grid.emptyrecords"]);
				}else{
					//第一次進入頁面
				}
        	}
        }
	});
	
	function openDoc(cellvalue, options, rowObject) {
		//ilog.debug(rowObject);		
		$.form.submit({
			url : '..' + rowObject.docURL + "/01",
			data : {
				'oid' : rowObject.oid,
				'mainOid' : rowObject.oid,
				'mainId' : rowObject.mainId,
				'mainDocStatus' : viewstatus
			},
			target : rowObject.oid
		});					
	};
	
	
	
	
    $("#buttonPanel").find("#btnView").on("click",function(){
    	var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
        	//
        }else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").on("click",function(){
    	var _id = "_div_cls3401v01_filter";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table class='tb2' >");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3401v01["C340M01A.custId"]+"</td><td>"
					+"<input type='text' id='search_custId' name='search_custId'  maxlength='10' class='alphanum' >"					
					+"</td></tr>");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3401v01["C340M01A.ctrType"]+"</td><td>"
					+"<select id='search_ploanCtrStatus' name='search_ctrType'>"
					+"<option value=''>"+i18n.def.comboSpace+"</option>"
					+"<option value='1'>"+i18n.cls3401v01["C340M01A.ctrType.1"]+"</option>"
					+"<option value='2'>"+i18n.cls3401v01["C340M01A.ctrType.2"]+"</option>"
					+"<option value='3'>"+i18n.cls3401v01["C340M01A.ctrType.3"]+"</option>"
					+"<option value='A'>"+i18n.cls3401v01["C340M01A.ctrType.A"]+"</option>"
					+"<option value='S'>"+i18n.cls3401v01["C340M01A.ctrType.S"]+"</option>"
					+"<option value='B'>"+i18n.cls3401v01["C340M01A.ctrType.B"]+"</option>"
					+"<option value='L'>"+i18n.cls3401v01["C340M01A.ctrType.L"]+"</option>"
					+((userInfo.unitNo=="002")?("<option value='C'>"+i18n.cls3401v01["C340M01A.ctrType.C"]+"</option>"):"")
					+"</select>"
					+"</td></tr>");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3401v01["C340M01A.ploanCtrNo"]+"</td><td>"
					+"<input type='text' id='search_ploanCtrNo' name='search_ploanCtrNo'  maxlength='20'>"					
					+"</td></tr>");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3401v01["C340M01A.ploanCtrStatus"]+"</td><td>"
					+"<select id='search_ploanCtrStatus' name='search_ploanCtrStatus'>"
					+"<option value=''>"+i18n.def.comboSpace+"</option>"
					+"<option value='1'>"+i18n.cls3401v01["C340M01A.ploanCtrStatus.1"]+"</option>"
					+"<option value='2'>"+i18n.cls3401v01["C340M01A.ploanCtrStatus.2"]+"</option>"
					+"<option value='9'>"+i18n.cls3401v01["C340M01A.ploanCtrStatus.9"]+"</option>"
					+"</select>"
					+"</td></tr>");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3401v01["C340M01A.ploanBorrowerIPAddr"]+"</td><td>"
					+"<input type='text' id='search_ploanBorrowerIPAddr' name='search_ploanBorrowerIPAddr'  maxlength='100'>"					
					+"</td></tr>");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3401v01["C340M01A.ploanCtrBegDate"]+"</td><td>"
					+"<input type='text' id='ploanCtrBegDate_beg' name='ploanCtrBegDate_beg' maxlength='10' class='date' />"
					+"<input type='text' id='ploanCtrBegDate_end' name='ploanCtrBegDate_end' maxlength='10' class='date' />"					
					+"</td></tr>");
			
			dyna.push(" </table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		    $('body').append(dyna.join(""));
		    
		    $("#"+_form).find(".date").filter(function(){
		        return !$(this).prop('readonly');
		    }).datepicker();
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
			//ui_lms2401.msg02=請選擇不覆審原因
	       title: '篩選',
	       width: 620,
           height: 290,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
            	  // 正常的輸入日期格式為 2021-06-22
            	  // 但若 server-side 收到 20210622 會導致 Exception => 送出前，加上  $("#"+_form).valid()
            	  if( $("#"+_form).valid() ){ 
	                  $.thickbox.close();
	                  //=============
	                  $gridview.setGridParam({
	  	                postData: $.extend(
	  	                	{}
	  	                	,$("#"+_form).serializeData()
	  	                ),
	  	                search: true
	                  }).trigger("reloadGrid");
            	  }
               },
               "cancel": function(){
            	   $.thickbox.close();
               }
           }
		});
    }).end().find("#btnAdd").click(function(){

        ctrTypeView().done(function(ctrType){
            chose_custId().done(function(resultFrom_chose_custId){

                if(ctrType == "1"){
                    queryCntrNo_ctrType_1(resultFrom_chose_custId).done(function(resultFrom_choseCntrNo){
                        $.ajax({
                            handler: _handler,
                            action : 'newC340M01A',
                            data : $.extend(resultFrom_chose_custId, resultFrom_choseCntrNo, {'ctrType' :'1'} )
                            }).done(function(json){
                                $.form.submit({
                                    url: '../cls/cls3401m01/01',
                                    data: {
                                        oid: json.mainOid,
                                        mainOid: json.mainOid,
                                        mainDocStatus: viewstatus,
                                        mainId: json.mainId,
                                        txCode: txCode
                                    },
                                    target: json.mainOid
                                });
                        });
                    });
                } else if (ctrType == "A"){
                    queryCntrNo_ctrType_A(resultFrom_chose_custId).done(function(resultFrom_choseCntrNo){
                        $.ajax({
                            handler: "cls3401m02formhandler",
                            action : 'newC340M01A',
                            data : $.extend(resultFrom_chose_custId, resultFrom_choseCntrNo)
                            }).done(function(json){
                                $.form.submit({
                                    url: '../cls/cls3401m02/01',
                                    data: {
                                        oid: json.mainOid,
                                        mainOid: json.mainOid,
                                        mainDocStatus: viewstatus,
                                        mainId: json.mainId,
                                        txCode: txCode
                                    },
                                    target: json.mainOid
                                });
                        });
                    });
                } else if (ctrType == "B"){
                     queryCntrNo_ctrType_B(resultFrom_chose_custId).done(function(resultFrom_choseCntrNo){
                         $.ajax({
                             handler: "cls3401m08formhandler",
                             action : 'newC340M01A',
                             data : $.extend(resultFrom_chose_custId, resultFrom_choseCntrNo)
                             }).done(function(json){
                                 $.form.submit({
                                     url: '../cls/cls3401m08/01',
                                     data: {
                                         oid: json.mainOid,
                                         mainOid: json.mainOid,
                                         mainDocStatus: viewstatus,
                                         mainId: json.mainId,
                                         txCode: txCode
                                     },
                                     target: json.mainOid
                                 });
                         });
                     });
                 } else if (ctrType == "S"){
                    queryCntrNo_ctrType_S(resultFrom_chose_custId).done(function(resultFrom_choseCntrNo){
                        $.ajax({
                            handler: "cls3401m05formhandler",
                            action : 'newC340M01A',
                            data : $.extend(resultFrom_chose_custId, resultFrom_choseCntrNo)
                            }).done(function(json){
                                $.form.submit({
                                    url: '../cls/cls3401m05/01',
                                    data: {
                                        oid: json.mainOid,
                                        mainOid: json.mainOid,
                                        mainDocStatus: viewstatus,
                                        mainId: json.mainId,
                                        txCode: txCode
                                    },
                                    target: json.mainOid
                                });
                        });
                    });    
                } else if (ctrType == "L"){
                    queryCntrNo_ctrType_L(resultFrom_chose_custId).done(function(resultFrom_choseCntrNo){
                        $.ajax({
                            handler: "cls3401m06formhandler",
                            action : 'newC340M01A',
                            data : $.extend(resultFrom_chose_custId, resultFrom_choseCntrNo)
                            }).done(function(json){
                                $.form.submit({
                                    url: '../cls/cls3401m06/01',
                                    data: {
                                        oid: json.mainOid,
                                        mainOid: json.mainOid,
                                        mainDocStatus: viewstatus,
                                        mainId: json.mainId,
                                        txCode: txCode
                                    },
                                    target: json.mainOid
                                });
                        });
                    });
                } else if (ctrType == "3"){
                    queryCntrNo_ctrType_3(resultFrom_chose_custId).done(function(resultFrom_choseCntrNo){
                        $.ajax({
                            handler: "cls3401m01formhandler",
                            action : 'newC340M01A',
                            data : $.extend(resultFrom_chose_custId, resultFrom_choseCntrNo, {'ctrType' :'3'} )
                            }).done(function(json){
                                $.form.submit({
                                    url: '../cls/cls3401m03/01',
                                    data: {
                                        oid: json.mainOid,
                                        mainOid: json.mainOid,
                                        mainDocStatus: viewstatus,
                                        mainId: json.mainId,
                                        txCode: txCode                                        
                                    },
                                    target: json.mainOid
                                });
                        });
                    });
                } else if (ctrType == "2"){
                    queryCntrNo_ctrType_2(resultFrom_chose_custId).done(function(resultFrom_choseCntrNo){
                        $.ajax({
                            handler: "cls3401m01formhandler",
                            action : 'newC340M01A',
                            data : $.extend(resultFrom_chose_custId, resultFrom_choseCntrNo, {'ctrType' :'2'} )
                            }).done(function(json){
                                $.form.submit({
                                    url: '../cls/cls3401m04/01',
                                    data: {
                                        oid: json.mainOid,
                                        mainOid: json.mainOid,
                                        mainDocStatus: viewstatus,
                                        mainId: json.mainId,
                                        txCode: txCode                                        
                                    },
                                    target: json.mainOid
                                });
                        });
                    });
                }

            });


        })

    }).end().find("#btnDelete").click(function(){ //編製中-刪除
    	var row = $gridview.getGridParam('selrow');
		var list = "";
		if(row){
			var data = $gridview.getRowData(row);
			CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
				if(b){
					$.ajax({
						handler : _handler,
						type : "POST",
						dataType : "json",
						data :{
							'formAction' : 'delC340M01A',
							'oid' : data.oid,
							'mainOid' : data.oid,
							'mainId' : data.mainId,
							'mainDocStatus' : viewstatus
						}
						}).done(function(obj) {
				        	$gridview.trigger("reloadGrid");
				          	API.showMessage(i18n.def.runSuccess);      
					});
				}
			})				
		}else{
			API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
			return;
		}
    }).end().find("#btnPrintNote").click(function(){
    	$('#downloadContractForm').injectData({'dlContract':'1_V202008'});
    	$('#downloadContractBox').thickbox({
            title:  i18n.cls3401v01['button.PrintNote'] || '',
            width: 550,
            height: 180,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                'sure': function(){
                    var $form = $('#downloadContractForm');
                    if ($form.valid()) {
                        $.thickbox.close();
                        //~~~
                        var fileDownloadName = "download.doc";
                        var dlContract = $form.find("[name=dlContract]:checked").val();
                        if(dlContract == "1_V202401"){
                           	fileDownloadName = i18n.cls3401v01['label.ctrType_1_rptId_V202401']+".docx";
                        }else if(dlContract == "2_V202401"){
                        	fileDownloadName = i18n.cls3401v01['label.ctrType_2_rptId_V202401']+".docx";
                        }else if(dlContract == "3_V202401"){
                           	fileDownloadName = i18n.cls3401v01['label.ctrType_3_rptId_V202401']+".docx";
                        }
                        else{
                        	API.showErrorMessage("error_undefined_dlContract{"+dlContract+"}");
                        }
                    	//~~~
                        $.form.submit({
                        	url: __ajaxHandler,
                     		target : "_blank",
                     		data : {
                     			_pa : 'lmsdownloadformhandler'
                     			,'dlEmptyWord' :'Y'
                     			,'dlContract' : dlContract
                     			,'fileDownloadName': fileDownloadName 
                     			,'serviceName': 'ContractDocService'
                     		}
                     	 });
                        
                        /* 此方式無法下載為指定檔名   
                        $.form.submit({
                            url: webroot + '/app/simple/FileProcessingService',
                            target: "_blank",
                            data: $.extend($form.serializeData(), {'serviceName': "ContractDocService", 'dlEmptyWord':'Y', 'fileDownloadName':fileDownloadName } )
                        }); */
                    }
                },
                'cancel': function(){
                    $.thickbox.close();
                }
            }
        });
    });
	
	
    
    
    function chose_custId(){
    	var my_dfd = $.Deferred();
    	$('#choseIdDupBox').thickbox({
            title: i18n.cls3401v01['C340M01A.custId'] || '',
            width: 420,
            height: 130,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                'sure': function(){
                    var $form = $('#choseIdDupForm');
                    if ($form.valid()) {
                        $.thickbox.close();
                    	my_dfd.resolve({'custId':$form.find("input[name=choseId]").val()
                    		, 'dupNo':$form.find("input[name=choseDup]").val()
                    		, 'custName':'' }
                    	);
                    }
                },
                'cancel': function(){
                    $.thickbox.close();
                }
            }
        });
    	return my_dfd.promise();
	}
    
    function queryCntrNo_ctrType_1(resultFrom_chose_custId){
		var my_dfd = $.Deferred();
		
		var custId = resultFrom_chose_custId.custId;
		var dupNo = resultFrom_chose_custId.dupNo;
		var custName = resultFrom_chose_custId.custName||"";
		
		CntrNo_ctrType_1_Grid.jqGrid("setGridParam", {
            postData: {
                'custId': custId
				,'dupNo': dupNo
            },
            search: true
        }).trigger("reloadGrid");
		
		$("#CntrNo_ctrType_1_ThickBox").thickbox({
	       title: (custId + "-" +dupNo+" "+custName),
	       width: 880,height: (cntrNo_grid_height+160),align: "center",valign: "bottom",
           modal: false, i18n: i18n.def,
		   buttons: {
                "sure": function(){
					 var data = CntrNo_ctrType_1_Grid.getSingleData();
                     if (data) {
						 $.thickbox.close();
						 //---
                    	 
        				 my_dfd.resolve(
        					$.extend(resultFrom_chose_custId
        						, {'tabMainId':data.tabMainId
        						  ,'caseMainId':data.caseMainId 
        						} 
        				 	)
        				);
                     }     	
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }	
		});	
		
		return my_dfd.promise();
	}

    function queryCntrNo_ctrType_2(resultFrom_chose_custId){
		var my_dfd = $.Deferred();
		
		var custId = resultFrom_chose_custId.custId;
		var dupNo = resultFrom_chose_custId.dupNo;
		var custName = resultFrom_chose_custId.custName||"";
		
		CntrNo_ctrType_2_Grid.jqGrid("setGridParam", {
            postData: {
                'custId': custId
				,'dupNo': dupNo
            },
            search: true
        }).trigger("reloadGrid");
		
		$("#CntrNo_ctrType_2_ThickBox").thickbox({
	       title: (custId + "-" +dupNo+" "+custName),
	       width: 880,height: (cntrNo_grid_height+160),align: "center",valign: "bottom",
           modal: false, i18n: i18n.def,
		   buttons: {
                "sure": function(){
					 var data = CntrNo_ctrType_2_Grid.getSingleData();
                     if (data) {
						 $.thickbox.close();
						 //---
                    	 
        				 my_dfd.resolve(
        					$.extend(resultFrom_chose_custId
        						, {'tabMainId':data.tabMainId
        						  ,'caseMainId':data.caseMainId 
        						} 
        				 	)
        				);
                     }     	
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }	
		});	
		
		return my_dfd.promise();
	}
    
    function queryCntrNo_ctrType_3(resultFrom_chose_custId){
		var my_dfd = $.Deferred();
		
		var custId = resultFrom_chose_custId.custId;
		var dupNo = resultFrom_chose_custId.dupNo;
		var custName = resultFrom_chose_custId.custName||"";
		
		CntrNo_ctrType_3_Grid.jqGrid("setGridParam", {
            postData: {
                'custId': custId
				,'dupNo': dupNo
            },
            search: true
        }).trigger("reloadGrid");
		
		$("#CntrNo_ctrType_3_ThickBox").thickbox({
	       title: (custId + "-" +dupNo+" "+custName),
	       width: 880,height: (cntrNo_grid_height+160),align: "center",valign: "bottom",
           modal: false, i18n: i18n.def,
		   buttons: {
                "sure": function(){
					 var data = CntrNo_ctrType_3_Grid.getSingleData();
                     if (data) {
						 $.thickbox.close();
						 //---
                    	 
        				 my_dfd.resolve(
        					$.extend(resultFrom_chose_custId
        						, {'tabMainId':data.tabMainId
        						  ,'caseMainId':data.caseMainId 
        						} 
        				 	)
        				);
                     }     	
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }	
		});	
		
		return my_dfd.promise();
	}
    
    function queryCntrNo_ctrType_A(resultFrom_chose_custId){
		var my_dfd = $.Deferred();

		var custId = resultFrom_chose_custId.custId;
		var dupNo = resultFrom_chose_custId.dupNo;
		var custName = resultFrom_chose_custId.custName||"";

		CntrNo_ctrType_A_Grid.jqGrid("setGridParam", {
            postData: {
                'custId': custId
				,'dupNo': dupNo
            },
            search: true
        }).trigger("reloadGrid");

		$("#CntrNo_ctrType_A_ThickBox").thickbox({
	       title: (custId + "-" +dupNo+" "+custName),
	       width: 880,height: (cntrNo_grid_height+160),align: "center",valign: "bottom",
           modal: false, i18n: i18n.def,
		   buttons: {
                "sure": function(){
					 var data = CntrNo_ctrType_A_Grid.getSingleData();
                     if (data) {
						 $.thickbox.close();
						 //---

        				 my_dfd.resolve(
        					$.extend(resultFrom_chose_custId
        						, {'tabMainId':data.tabMainId
        						  ,'caseMainId':data.caseMainId
        						}
        				 	)
        				);
                     }
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }
		});

		return my_dfd.promise();
	}

    function queryCntrNo_ctrType_B(resultFrom_chose_custId){
    		var my_dfd = $.Deferred();

    		var custId = resultFrom_chose_custId.custId;
    		var dupNo = resultFrom_chose_custId.dupNo;
    		var custName = resultFrom_chose_custId.custName||"";

    		CntrNo_ctrType_B_Grid.jqGrid("setGridParam", {
                postData: {
                    'custId': custId
    				,'dupNo': dupNo
                },
                search: true
            }).trigger("reloadGrid");

    		$("#CntrNo_ctrType_B_ThickBox").thickbox({
    	       title: (custId + "-" +dupNo+" "+custName),
    	       width: 880,height: (cntrNo_grid_height+160),align: "center",valign: "bottom",
               modal: false, i18n: i18n.def,
    		   buttons: {
                    "sure": function(){
                    	//J-113-0050 新增房貸增待契約書時出提示訊息
                    	CommonAPI.confirmMessage("已向客戶充分說明。",function(b){
            				if(b){
	        					 var data = CntrNo_ctrType_B_Grid.getSingleData();
	                             if (data) {
	        						 $.thickbox.close();
	
	                				 my_dfd.resolve(
	                					$.extend(resultFrom_chose_custId
	                						, {'tabMainId':data.tabMainId
	                						  ,'caseMainId':data.caseMainId
	                						}
	                				 	)
	                				);
	                             }
            				}
            			})
    					
                    },
                    "cancel": function(){
                    	$.thickbox.close();
                    }
                }
    		});

    		return my_dfd.promise();
    	}

    function queryCntrNo_ctrType_S(resultFrom_chose_custId){
		var my_dfd = $.Deferred();

		var custId = resultFrom_chose_custId.custId;
		var dupNo = resultFrom_chose_custId.dupNo;
		var custName = resultFrom_chose_custId.custName||"";

		CntrNo_ctrType_S_Grid.jqGrid("setGridParam", {
            postData: {
                'custId': custId
				,'dupNo': dupNo
            },
            search: true
        }).trigger("reloadGrid");

		$("#CntrNo_ctrType_S_ThickBox").thickbox({
	       title: (custId + "-" +dupNo+" "+custName),
	       width: 980,height: (cntrNo_grid_height+160+40),align: "center",valign: "bottom",
           modal: false, i18n: i18n.def,
		   buttons: {
                "sure": function(){
					 var data = CntrNo_ctrType_S_Grid.getSingleData();
                     if (data) {
						 $.thickbox.close();
						 //---

        				 my_dfd.resolve(
        					$.extend(resultFrom_chose_custId
        						, {'tabMainId':data.tabMainId
        						  ,'caseMainId':data.caseMainId
        						}
        				 	)
        				);
                     }
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }
		});

		return my_dfd.promise();
	}

    function queryCntrNo_ctrType_L(resultFrom_chose_custId){
		var my_dfd = $.Deferred();

		var custId = resultFrom_chose_custId.custId;
		var dupNo = resultFrom_chose_custId.dupNo;
		var custName = resultFrom_chose_custId.custName||"";

		CntrNo_ctrType_L_Grid.jqGrid("setGridParam", {
            postData: {
                'custId': custId
				,'dupNo': dupNo
            },
            search: true
        }).trigger("reloadGrid");

		$("#CntrNo_ctrType_L_ThickBox").thickbox({
	       title: (custId + "-" +dupNo+" "+custName),
	       width: 880,height: (cntrNo_grid_height+160),align: "center",valign: "bottom",
           modal: false, i18n: i18n.def,
		   buttons: {
                "sure": function(){
					 var data = CntrNo_ctrType_L_Grid.getSingleData();
                     if (data) {
						 $.thickbox.close();
						 //---

        				 my_dfd.resolve(
        					$.extend(resultFrom_chose_custId
        						, {'tabMainId':data.tabMainId
        						  ,'caseMainId':data.caseMainId
        						}
        				 	)
        				);
                     }
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }
		});

		return my_dfd.promise();
	}

	function ctrTypeView(){
	    var dfd = $.Deferred();
        $("#ctrTypeView").thickbox({
            width: 500,
            height: 280,
            buttons: {
                "sure":function(){
                    var $form = $('#choseCtrTypeForm');
                    if($form.valid()){
                        var ctrTypeValue = $form.find("input[name='ctrType']:checked").val();
                        $.thickbox.close();
                        dfd.resolve(ctrTypeValue);
                    }

                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }

        });

	    return dfd.promise();
	}
    
});
