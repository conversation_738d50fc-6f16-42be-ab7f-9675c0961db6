/* 
 * IQUOTAPP .java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;


import java.util.Date;
import javax.persistence.*;

import tw.com.iisi.cap.model.GenericBean;


/** 核准額度資料檔 **/

public class IQUOTAPP  extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 額度序號 **/
	@Column(name="QUOTANO", length=12, columnDefinition="CHAR(12)",unique = true)
	private String quotano;

	/** 身份證／統編 **/
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)")
	private String custid;

	/** 重複序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(01)")
	private String dupno;

	/** 授信經辦行員代號 **/
	@Column(name="ICBCNO", length=5, columnDefinition="CHAR(05)")
	private String icbcno;

	/** 授信經辦姓名 **/
	@Column(name="CNAME", length=102, columnDefinition="CHAR(102)")
	private String cname;

	/** 
	 * 初放主管姓名<p/>
	 * 最初貸放的甲級主管
	 */
	@Column(name="OMGRNAME", length=102, columnDefinition="CHAR(102)")
	private String omgrname;

	/** 
	 * 敘作主管姓名<p/>
	 * 最近一次敘作的甲級主管
	 */
	@Column(name="FMGRNAME", length=102, columnDefinition="CHAR(102)")
	private String fmgrname;

	/** 
	 * 授權等級<p/>
	 * 只保留最近一次的授權等級
	 */
	@Column(name="APPROLVL", length=1, columnDefinition="CHAR(01)")
	private String approlvl;

	/** 資料修改人（行員代號） **/
	@Column(name="UPDATER", length=8, columnDefinition="CHAR(08)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name="TMESTAMP", columnDefinition="TIMESTAMP")
	private Date tmestamp;

	/** 取得額度序號 **/
	public String getQuotano() {
		return this.quotano;
	}
	/** 設定額度序號 **/
	public void setQuotano(String value) {
		this.quotano = value;
	}

	/** 取得身份證／統編 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定身份證／統編 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得授信經辦行員代號 **/
	public String getIcbcno() {
		return this.icbcno;
	}
	/** 設定授信經辦行員代號 **/
	public void setIcbcno(String value) {
		this.icbcno = value;
	}

	/** 取得授信經辦姓名 **/
	public String getCname() {
		return this.cname;
	}
	/** 設定授信經辦姓名 **/
	public void setCname(String value) {
		this.cname = value;
	}

	/** 
	 * 取得初放主管姓名<p/>
	 * 最初貸放的甲級主管
	 */
	public String getOmgrname() {
		return this.omgrname;
	}
	/**
	 *  設定初放主管姓名<p/>
	 *  最初貸放的甲級主管
	 **/
	public void setOmgrname(String value) {
		this.omgrname = value;
	}

	/** 
	 * 取得敘作主管姓名<p/>
	 * 最近一次敘作的甲級主管
	 */
	public String getFmgrname() {
		return this.fmgrname;
	}
	/**
	 *  設定敘作主管姓名<p/>
	 *  最近一次敘作的甲級主管
	 **/
	public void setFmgrname(String value) {
		this.fmgrname = value;
	}

	/** 
	 * 取得授權等級<p/>
	 * 只保留最近一次的授權等級
	 */
	public String getApprolvl() {
		return this.approlvl;
	}
	/**
	 *  設定授權等級<p/>
	 *  只保留最近一次的授權等級
	 **/
	public void setApprolvl(String value) {
		this.approlvl = value;
	}

	/** 取得資料修改人（行員代號） **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定資料修改人（行員代號） **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Date getTmestamp() {
		return this.tmestamp;
	}
	/** 設定資料修改日期 **/
	public void setTmestamp(Date value) {
		this.tmestamp = value;
	}
}
