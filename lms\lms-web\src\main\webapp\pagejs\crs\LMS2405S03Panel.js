$(function(){
	$("#lms2405s03").iGrid({
		handler : 'lms2405gridhandler',
		height : 350,
		sortname : 'uploadTime',
		postData : {
			mainId : responseJSON.mainId,
			formAction : "queryDocFile"
		},
		colModel : [ {
			colHeader : i18n.lms2405m01['docfile.filename'],//"檔名"
			name : 'srcFileName',
			width : 100,
			formatter : 'click',
			onclick : openDocFile
		}, {
			colHeader : i18n.lms2405m01['docfile.uploadTime'],//"上傳時間"
			name : 'uploadTime',
			width : 165,
			sortable : true
		}, {
			colHeader : "oid",
			name : 'oid',
			hidden : true
		}],
		ondblClickRow : function(rowid){
			openDocFile(null, null, $("#lms2405s03").getRowData(rowid));
		}
	});
	
	function openDocFile(cellvalue, options, rowObject) {
		ilog.debug(rowObject);
		$.capFileDownload({
	        handler : "simplefiledwnhandler",
	        data : {
	            fileOid : rowObject.oid
	        }
		});
	};
});