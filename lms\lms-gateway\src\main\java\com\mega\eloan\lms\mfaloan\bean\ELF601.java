/* 
 * ELF601.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON>., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.bean;

import org.apache.wicket.markup.html.form.Check;
import tw.com.iisi.cap.model.GenericBean;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/** 貸後管理控制檔 **/
//
public class ELF601 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/**
	 * 序號 (UNID)
	 */
	@Size(max = 60)
	@Column(name = "ELF601_UNID", length = 60, columnDefinition = "CHAR(60)")
	private String elf601_unid;

	/**
	 * 額度序號
	 */
	@Size(max = 12)
	@Column(name = "ELF601_CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String elf601_cntrno;

	/**
	 * 放款帳號
	 */
	@Size(max = 20)
	@Column(name = "ELF601_LOAN_NO", length = 20, columnDefinition = "CHAR(20)")
	private String elf601_loan_no;

	/**
	 * 業務別 <br/>
	 * FA: 應收帳款 <br/>
	 * FI: 供應鏈融資 <br/>
	 * LN: eLoan
	 */
	@Size(max = 2)
	@Column(name = "ELF601_LOAN_KIND", length = 2, columnDefinition = "CHAR(2)")
	private String elf601_loan_kind;

	/**
	 * 類別 <br/>
	 * 複選 以｜分隔
	 */
	@Size(max = 20)
	@Column(name = "ELF601_FO_KIND", length = 20, columnDefinition = "VARCHAR(20)")
	private String elf601_fo_kind;

	/**
	 * 追蹤事項通知內容
	 */
	@Size(max = 800)
	@Column(name = "ELF601_FO_CONTENT", length = 800, columnDefinition = "VARCHAR(800)")
	private String elf601_fo_content;

	/**
	 * 追蹤方式 <br/>
	 * 1-特定日期 <br/>
	 * 2-循環週期
	 */
	@Size(max = 1)
	@Column(name = "ELF601_FO_WAY", length = 1, columnDefinition = "CHAR(1)")
	private String elf601_fo_way;

	/**
	 * 循環追蹤週期（月）
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "ELF601_FO_CYCLE", columnDefinition = "DECIMAL(2,0)")
	private BigDecimal elf601_fo_cycle;

	/**
	 * 循環追蹤起日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF601_FO_BEG_DATE", columnDefinition = "DATE")
	private Date elf601_fo_beg_date;

	/**
	 * 循環追蹤止日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF601_FO_END_DATE", columnDefinition = "DATE")
	private Date elf601_fo_end_date;

	/**
	 * 下次追蹤日 <br/>
	 * 限未來日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF601_FO_NEXT_DATE", columnDefinition = "DATE")
	private Date elf601_fo_next_date;

	/**
	 * 應辦理追蹤對象 <br/>
	 * 01-帳務人員 <br/>
	 * 02-AO人員
	 */
	@Size(max = 2)
	@Column(name = "ELF601_STAFF", length = 2, columnDefinition = "CHAR(2)")
	private String elf601_staff;

	/**
	 * 帳務行員代號
	 */
	@Size(max = 6)
	@Column(name = "ELF601_FO_STAFFNO", length = 6, columnDefinition = "CHAR(6)")
	private String elf601_fo_staffNo;

	/**
	 * AO 行員代號
	 */
	@Size(max = 6)
	@Column(name = "ELF601_AO_STAFFNO", length = 6, columnDefinition = "CHAR(6)")
	private String elf601_ao_staffNo;

	/**
	 * 狀態 <br/>
	 * N-新增 <br/>
	 * U-修改 <br/>
	 * C-解除
	 */
	@Size(max = 1)
	@Column(name = "ELF601_STATUS", length = 1, columnDefinition = "CHAR(1)")
	private String elf601_status;

	/**
	 * 建檔日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF601_CRE_DATE", columnDefinition = "DATE")
	private Date elf601_cre_date;

	/**
	 * 建檔經辦
	 */
	@Size(max = 6)
	@Column(name = "ELF601_CRE_TELLER", length = 6, columnDefinition = "CHAR(6)")
	private String elf601_cre_teller;

	/**
	 * 建檔主管
	 */
	@Size(max = 6)
	@Column(name = "ELF601_CRE_SUPVNO", length = 6, columnDefinition = "CHAR(6)")
	private String elf601_cre_supvno;

	/**
	 * 最近維護日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF601_UPD_DATE", columnDefinition = "DATE")
	private Date elf601_upd_date;

	/**
	 * 最近維護經辦
	 */
	@Size(max = 6)
	@Column(name = "ELF601_UPD_TELLER", length = 6, columnDefinition = "CHAR(6)")
	private String elf601_upd_teller;

	/**
	 * 最近維護主管
	 */
	@Size(max = 6)
	@Column(name = "ELF601_UPD_SUPVNO", length = 6, columnDefinition = "CHAR(6)")
	private String elf601_upd_supvno;

    /**
     * 追蹤事項通知內容 -  全型字
     */
    @Size(max = 402)
    @Column(name = "ELF601_FULL_CONTENT", length = 402, columnDefinition = "VARCHAR(402)")
    private String elf601_full_content;

	/** 客戶統編 **/
	@Size(max = 10)
	@Column(name="ELF601_CUSTID", length=10, columnDefinition="CHAR(10)")
	private String elf601_custid;

	/** 重複序號 **/
	@Size(max = 1)
	@Column(name="ELF601_DUPNO", length=1, columnDefinition="CHAR(1)")
	private String elf601_dupno;

	/** 最後維護分行 **/
	@Size(max = 3)
	@Column(name="ELF601_BR_NO", length=3, columnDefinition="CHAR(3)")
	private String elf601_br_no;

    /** 案件註記 **/
    @Size(max = 2)
    @Column(name="ELF601_CASE_MARK", length=2, columnDefinition="VARCHAR(2)")
    private String elf601_case_mark;
    
	/**
	 * 分項UID (UNID)
	 */
	@Size(max = 32)
	@Column(name = "ELF601_SUID", length = 32, columnDefinition = "CHAR(32)")
	private String elf601_suid;
    
	/**
	 * 分項簽報書核准時間
	 */
	@Column(name = "ELF601_SAPPTIME", columnDefinition = "TIMESTAMP")
	private Date elf601_sapptime;
	
	/**
	 * 分項序號
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ELF601_SSEQNO", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal elf601_sseqno;

	/**
	 * 取得序號 (UNID)
	 */
	public String getElf601_unid() {
		return this.elf601_unid;
	}

	/**
	 * 設定序號 (UNID)
	 */
	public void setElf601_unid(String value) {
		this.elf601_unid = value;
	}

	/**
	 * 取得額度序號
	 */
	public String getElf601_cntrno() {
		return this.elf601_cntrno;
	}

	/**
	 * 設定額度序號
	 */
	public void setElf601_cntrno(String value) {
		this.elf601_cntrno = value;
	}

	/**
	 * 取得放款帳號
	 */
	public String getElf601_loan_no() {
		return this.elf601_loan_no;
	}

	/**
	 * 設定放款帳號
	 */
	public void setElf601_loan_no(String value) {
		this.elf601_loan_no = value;
	}

	/**
	 * 取得業務別
	 */
	public String getElf601_loan_kind() {
		return this.elf601_loan_kind;
	}

	/**
	 * 設定業務別
	 */
	public void setElf601_loan_kind(String value) {
		this.elf601_loan_kind = value;
	}

	/**
	 * 取得類別
	 */
	public String getElf601_fo_kind() {
		return this.elf601_fo_kind;
	}

	/**
	 * 設定類別
	 */
	public void setElf601_fo_kind(String value) {
		this.elf601_fo_kind = value;
	}

	/**
	 * 取得追蹤事項通知內容
	 */
	public String getElf601_fo_content() {
		return this.elf601_fo_content;
	}

	/**
	 * 設定追蹤事項通知內容
	 */
	public void setElf601_fo_content(String value) {
		this.elf601_fo_content = value;
	}

	/**
	 * 取得追蹤方式
	 */
	public String getElf601_fo_way() {
		return this.elf601_fo_way;
	}

	/**
	 * 設定追蹤方式
	 */
	public void setElf601_fo_way(String value) {
		this.elf601_fo_way = value;
	}

	/**
	 * 取得循環追蹤週期（月）
	 */
	public BigDecimal getElf601_fo_cycle() {
		return this.elf601_fo_cycle;
	}

	/**
	 * 設定循環追蹤週期（月）
	 */
	public void setElf601_fo_cycle(BigDecimal value) {
		this.elf601_fo_cycle = value;
	}

	/**
	 * 取得循環追蹤起日
	 */
	public Date getElf601_fo_beg_date() {
		return this.elf601_fo_beg_date;
	}

	/**
	 * 設定循環追蹤起日
	 */
	public void setElf601_fo_beg_date(Date value) {
		this.elf601_fo_beg_date = value;
	}

	/**
	 * 取得循環追蹤止日
	 */
	public Date getElf601_fo_end_date() {
		return this.elf601_fo_end_date;
	}

	/**
	 * 設定循環追蹤止日
	 */
	public void setElf601_fo_end_date(Date value) {
		this.elf601_fo_end_date = value;
	}

	/**
	 * 取得下次追蹤日
	 */
	public Date getElf601_fo_next_date() {
		return this.elf601_fo_next_date;
	}

	/**
	 * 設定下次追蹤日
	 */
	public void setElf601_fo_next_date(Date value) {
		this.elf601_fo_next_date = value;
	}

	/**
	 * 取得應辦理追蹤對象
	 */
	public String getElf601_staff() {
		return this.elf601_staff;
	}

	/**
	 * 設定應辦理追蹤對象
	 */
	public void setElf601_staff(String value) {
		this.elf601_staff = value;
	}

	/**
	 * 取得帳務行員代號
	 */
	public String getElf601_fo_staffNo() {
		return this.elf601_fo_staffNo;
	}

	/**
	 * 設定帳務行員代號
	 */
	public void setElf601_fo_staffNo(String value) {
		this.elf601_fo_staffNo = value;
	}

	/**
	 * 取得帳務行員代號
	 */
	public String getElf601_ao_staffNo() {
		return this.elf601_ao_staffNo;
	}

	/**
	 * 設定帳務行員代號
	 */
	public void setElf601_ao_staffNo(String value) {
		this.elf601_ao_staffNo = value;
	}

	/**
	 * 取得狀態
	 */
	public String getElf601_status() {
		return this.elf601_status;
	}

	/**
	 * 設定狀態
	 */
	public void setElf601_status(String value) {
		this.elf601_status = value;
	}

	/**
	 * 取得建檔日期
	 */
	public Date getElf601_cre_date() {
		return this.elf601_cre_date;
	}

	/**
	 * 設定建檔日期
	 */
	public void setElf601_cre_date(Date value) {
		this.elf601_cre_date = value;
	}

	/**
	 * 取得建檔經辦
	 */
	public String getElf601_cre_teller() {
		return this.elf601_cre_teller;
	}

	/**
	 * 設定建檔經辦
	 */
	public void setElf601_cre_teller(String value) {
		this.elf601_cre_teller = value;
	}

	/**
	 * 取得建檔主管
	 */
	public String getElf601_cre_supvno() {
		return this.elf601_cre_supvno;
	}

	/**
	 * 設定建檔主管
	 */
	public void setElf601_cre_supvno(String value) {
		this.elf601_cre_supvno = value;
	}

	/**
	 * 取得最近維護日期
	 */
	public Date getElf601_upd_date() {
		return this.elf601_upd_date;
	}

	/**
	 * 設定最近維護日期
	 */
	public void setElf601_upd_date(Date value) {
		this.elf601_upd_date = value;
	}

	/**
	 * 取得最近維護經辦
	 */
	public String getElf601_upd_teller() {
		return this.elf601_upd_teller;
	}

	/**
	 * 設定最近維護經辦
	 */
	public void setElf601_upd_teller(String value) {
		this.elf601_upd_teller = value;
	}

	/**
	 * 取得最近維護主管
	 */
	public String getElf601_upd_supvno() {
		return this.elf601_upd_supvno;
	}

	/**
	 * 設定最近維護主管
	 */
	public void setElf601_upd_supvno(String value) {
		this.elf601_upd_supvno = value;
	}

    /**
     * 取得追蹤事項通知內容 -  全型字
     */
    public String getElf601_full_content() {
        return this.elf601_full_content;
    }

    /**
     * 設定追蹤事項通知內容 -  全型字
     */
    public void setElf601_full_content(String value) {
        this.elf601_full_content = value;
    }

	/** 取得借款人統編 **/
	public String getElf601_custid() {
		return this.elf601_custid;
	}

	/** 設定借款人統編 **/
	public void setElf601_custid(String value) {
		this.elf601_custid = value;
	}

	/** 取得重複序號 **/
	public String getElf601_dupno() {
		return this.elf601_dupno;
	}

	/** 設定重複序號 **/
	public void setElf601_dupno(String value) {
		this.elf601_dupno = value;
	}

	/** 取得最後維護分行 **/
	public String getElf601_br_no() {
		return this.elf601_br_no;
	}

	/** 設定最後維護分行 **/
	public void setElf601_br_no(String value) {
		this.elf601_br_no = value;
	}

    /** 取得案件註記 **/
    public String getElf601_case_mark() {
        return this.elf601_case_mark;
    }

    /** 設定案件註記 **/
    public void setElf601_case_mark(String value) {
        this.elf601_case_mark = value;
    }

    /** 設定分項UID **/
	public void setElf601_suid(String elf601_suid) {
		this.elf601_suid = elf601_suid;
	}

	/**取得分項UID **/
	public String getElf601_suid() {
		return elf601_suid;
	}

	/** 設定分項簽報書核准時間 **/
	public void setElf601_sapptime(Date elf601_sapptime) {
		this.elf601_sapptime = elf601_sapptime;
	}

	/**取得分項簽報書核准時間  **/
	public Date getElf601_sapptime() {
		return elf601_sapptime;
	}

	/** 設定分項序號 **/
	public void setElf601_sseqno(BigDecimal elf601_sseqno) {
		this.elf601_sseqno = elf601_sseqno;
	}

	/**取得分項序號 **/
	public BigDecimal getElf601_sseqno() {
		return elf601_sseqno;
	}
}
