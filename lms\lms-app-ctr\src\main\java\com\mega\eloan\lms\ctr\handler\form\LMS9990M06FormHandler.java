/* 
 * LMS9990FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.handler.form;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.ctr.constants.CtrConstants;
import com.mega.eloan.lms.ctr.pages.LMS9990M06Page;
import com.mega.eloan.lms.ctr.pages.LMS9990M07Page;
import com.mega.eloan.lms.ctr.service.LMS9990Service2;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.model.C999A01A;
import com.mega.eloan.lms.model.C999M01A;
import com.mega.eloan.lms.model.C999M01B;
import com.mega.eloan.lms.model.C999M01C;
import com.mega.eloan.lms.model.C999M01D;
import com.mega.eloan.lms.model.C999S01A;
import com.mega.eloan.lms.model.C999S01B;
import com.mega.eloan.lms.model.C999S02A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金約據書 -- 主頁面
 * </pre>
 * 
 * @since 2012/2/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/12,Ice,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9990m06formhandler")
@DomainClass(C999M01A.class)
public class LMS9990M06FormHandler extends LMS9990M00FormHandler {

	private final static String space = "";

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMS1205Service lms1205Service;

	@Resource
	LMS1405Service lms1405Service;

	@Resource
	LMS9990Service2 lms9990Service2;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	BranchService branchService;

	@Resource
	ProdService prodService;

	@Resource
	LMSService lmsService;

	/**
	 * 儲存C999M01A 約據書主檔(新增約據書頁面)--個金
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveC999m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = null;
		boolean isFile = params.getBoolean("isFile");
		String srcMainId = Util.trim(params.getString("srcMainId"));
		String contractType = Util.trim(params.getString("contractType"));
		String contractKind = Util.trim(params.getString("contractKind"));
		String contractType2 = (isFile) ? Util.trim(params
				.getString("chkColIdVal")) : Util.trim(params
				.getString("contractType2" + contractType + contractKind));
		String txCode = Util.trim(params
				.getString(EloanConstants.TRANSACTION_CODE));
		String l140CustId = Util.trim(params.getString("l140CustId"));
		String l140DupNo = Util.trim(params.getString("l140DupNo"));
		String l140CustName = Util.trim(params.getString("l140CustName"));
		String mainIdFor140 = Util.trim(params.getString("mainIdFor140"));
		String[] mainIdsFor140 = null;
		// 取得單位相關資料
		Map<String, String> bankMap = lmsService.getBrnoData(user.getUnitNo());
		if (!Util.isEmpty(mainIdFor140)) {
			mainIdsFor140 = mainIdFor140.split("\\|");
			// 檢查使用者所勾選額度明細表借款人名稱是否皆相同
			if (!isSame(mainIdsFor140)) {
				// 拋出所選額度明細表借款人必須相同 錯誤訊息
				// C999M01AM06.message03=額度明細表借款人須相同。
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS9990M06Page.class);
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.注意,
						pop.getProperty("C999M01AM06.message03")), getClass());
			}
		}

		C999M01A c999m01a = null;
		L120M01A l120m01a = null;
		C999M01B c999m01b1 = null;
		C999M01B c999m01b2 = null;
		List<C999M01C> listC999m01c = new ArrayList<C999M01C>();
		List<C999S01A> listC999s01a = new ArrayList<C999S01A>();
		result = new CapAjaxFormResult();
		l120m01a = lms1205Service.findL120m01aByMainId(srcMainId);
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}
		// 設定個金約據書主檔
		c999m01a = lms9990Service2.addC999M01A(l120m01a, contractType,
				contractType2, contractKind, l140CustId, l140DupNo,
				l140CustName, txCode);
		String c999MainId = Util.trim(c999m01a.getMainId());
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		// 新增個金約據書立約人檔(甲方與乙方)
		c999m01b1 = lms9990Service2.addC999m01b(pop.getProperty("megaId"), "",
				l140CustName, CtrConstants.Book9990.KindType.兆豐國際商業銀行股份有限公司_乙方,
				c999MainId);
		c999m01b2 = lms9990Service2
				.addC999m01b(l140CustId, l140DupNo, l140CustName,
						CtrConstants.Book9990.KindType.借款人_甲方, c999MainId);
		// 新增個金約據書連保人(保證人)檔
		listC999m01c = lms9990Service2.importAllC999m01c(mainIdsFor140,
				c999MainId);
		// 新增個金約據書產品種類檔
		listC999s01a = lms9990Service2.impotAllC999s01a(mainIdsFor140,
				c999MainId, params);

		// if(CtrConstants.Book9990.contractType.政策性留學貸款.equals(contractType)){
		// // 政策性留貸只會有一筆產品種類
		// c999s01a1 = lms9990Service2.addC999s01a(c999MainId,
		// Util.parseInt(CtrConstants.Book9990.ItemNo.甲), "000000000000",
		// CtrConstants.Book9990.ProdKind.產品種類暫定1, "00000001", "TWD",
		// BigDecimal.ZERO);
		// }
		// 進行儲存
		if (isFile) {
			lms9990Service2.saveC9990All(c999m01a, null, null, null, null);
		} else {
			lms9990Service2.saveC9990All(c999m01a, c999m01b1, c999m01b2,
					listC999m01c, listC999s01a);
		}

		result.set("contractType",
				CapString.trimNull(c999m01a.getContractType()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(c999m01a.getMainId()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(c999m01a.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS,
				CapString.trimNull(c999m01a.getDocStatus()));
		result.set(EloanConstants.TRANSACTION_CODE, CapString.trimNull(txCode));
		result.set("srcMainId", CapString.trimNull(c999m01a.getSrcMainId()));

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;

	}

	/**
	 * <pre>
	 * 刪除(授信簽報書)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteC999m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String Dlist = Util.trim(params.getString("list")); // 取得list中所有資料組成的字串
		C999M01A c999m01a = lms9990Service2.findC999m01aByOid(Dlist);
		if (c999m01a != null) {
			String mainId = Util.trim(c999m01a.getMainId());
			List<C999A01A> listC999a01a = lms9990Service2
					.findC999a01aByMainId(mainId);
			List<C999M01B> listC999m01b = lms9990Service2
					.findC999m01bByMainId(mainId);
			List<C999M01C> listC999m01c = lms9990Service2
					.findC999m01cByMainId(mainId);
			List<C999M01D> listC999m01d = lms9990Service2
					.findC999m01dByMainId(mainId);
			List<C999S01A> listC999s01a = lms9990Service2
					.findC999s01aByMainId(mainId);
			List<C999S01B> listC999s01b = lms9990Service2
					.findC999s01bByMainId(mainId);
			List<C999S02A> listC999s02a = lms9990Service2
					.findC999s02aByMainId(mainId);
			lms9990Service2.delRelC999(c999m01a, listC999a01a, listC999m01b,
					listC999m01c, listC999m01d, listC999s01a, listC999s01b,
					listC999s02a);
		}

		// 印出刪除成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;
	}// ;

	/**
	 * 檢查使用者所勾選額度明細表借款人名稱是否皆相同
	 * 
	 * @param mainIds
	 *            使用者勾選額度明細表文件編號群組
	 * @return true: 相同 false: 不同
	 */
	private boolean isSame(String[] mainIds) {
		String custName = null;
		boolean result = true;
		L140M01A model = lms1405Service.findL140m01aByMainId(mainIds[0]);
		if (model != null) {
			custName = Util.trim(model.getCustName());
		}
		for (int i = 0; i < mainIds.length; i++) {
			if (i != 0 && !Util.isEmpty(custName)) {
				L140M01A theModel = lms1405Service
						.findL140m01aByMainId(mainIds[i]);
				if (theModel != null) {
					if (!custName.equals(Util.trim(theModel.getCustName()))) {
						result = false;
						break;
					}
				}
			}
		}
		return result;
	}

	/**
	 * 指定項次
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult modifyItemNo(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oids = params.getString("oids");
		String itemNos = params.getString("itemNos");
		String sign = ",";
		String[] oidArray = oids.split(sign);
		String[] itemNoArray = itemNos.split(sign);
		// 開始指定項次
		List<C999S01A> list = lms9990Service2.modifySeqs(oidArray, itemNoArray);
		// 進行儲存
		lms9990Service2.saveListC999s01a(list);
		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	/**
	 * 刪除指定項次
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult delItemNo(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		C999S01A model = lms9990Service2.findC999s01aByOid(oid);
		if (model != null) {
			model.setItemNo(Util.parseInt(CtrConstants.Book9990.ItemNo.刪除));
			model.setDeletedTime(CapDate.getCurrentTimestamp());
			// 進行儲存
			lms9990Service2.save(model);
		} else {
			// C999M01AM07.message02=資料異常！請洽資訊處！
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS9990M07Page.class);
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.注意,
					pop.getProperty("C999M01AM07.message02")), getClass());
		}
		// 印出刪除成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;
	}

	/**
	 * 取消刪除指定項次
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult canDelItemNo(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		C999S01A model = lms9990Service2.findC999s01aByOid(oid);
		if (model != null) {
			model.setItemNo(null);
			model.setDeletedTime(null);
			// 進行儲存
			lms9990Service2.save(model);
		} else {
			// C999M01AM07.message02=資料異常！請洽資訊處！
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS9990M07Page.class);
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.注意,
					pop.getProperty("C999M01AM07.message02")), getClass());
		}
		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	/**
	 * 儲存個金約據書
	 * 
	 * @param params
	 *            params
	 * @param parent
	 *            parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveAll(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String contractNo = Util.trim(params.getString("contractNo"));
		// 確認要save那一個tab的資料
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String formTabs13 = Util.trim(params.getString("formTabs13"));
		String formTabs14 = Util.trim(params.getString("formTabs14"));
		String formTabs15 = Util.trim(params.getString("formTabs15"));
		String formTabs16 = Util.trim(params.getString("formTabs16"));
		String formTabs17 = Util.trim(params.getString("formTabs17"));
		String formTabs8 = Util.trim(params.getString("formTabs8"));
		JSONObject allJson = new JSONObject();
		allJson.putAll(JSONObject.fromObject(Util.isEmpty(formTabs13) ? "{}"
				: formTabs13));
		allJson.putAll(JSONObject.fromObject(Util.isEmpty(formTabs14) ? "{}"
				: formTabs14));
		allJson.putAll(JSONObject.fromObject(Util.isEmpty(formTabs15) ? "{}"
				: formTabs15));
		allJson.putAll(JSONObject.fromObject(Util.isEmpty(formTabs16) ? "{}"
				: formTabs16));
		allJson.putAll(JSONObject.fromObject(Util.isEmpty(formTabs17) ? "{}"
				: formTabs17));
		allJson.putAll(JSONObject.fromObject(Util.isEmpty(formTabs8) ? "{}"
				: formTabs8));

		C999M01A meta = lms9990Service2.findC999m01aByMainId(mainId);
		switch (page) {
		case 8:
			C999M01D c999m01dA = lms9990Service2.findC999m01dByUniqueKey(
					mainId, "A");
			C999M01D c999m01dC = lms9990Service2.findC999m01dByUniqueKey(
					mainId, "C");
			String itemContentA = Util.trim(params.getString("itemContentA"));
			String itemContentC = Util.trim(params.getString("itemContentC"));
			if (c999m01dA == null) {
				c999m01dA = new C999M01D();
				c999m01dA.setMainId(mainId);
				c999m01dA.setItemType(CtrConstants.Book9990.itemType.特別條款);
				c999m01dA.setCreator(user.getUserId());
				c999m01dA.setCreateTime(CapDate.getCurrentTimestamp());
			}
			if (c999m01dC == null) {
				c999m01dC = new C999M01D();
				c999m01dC.setMainId(mainId);
				c999m01dC.setItemType(CtrConstants.Book9990.itemType.個別商議條款);
				c999m01dC.setCreator(user.getUserId());
				c999m01dC.setCreateTime(CapDate.getCurrentTimestamp());
			}
			if (meta != null) {
				meta.setContractNo(contractNo);
			}
			c999m01dA.setItemContent(itemContentA);
			c999m01dC.setItemContent(itemContentC);
			lms9990Service2.save(meta, c999m01dA, c999m01dC);// , c999m01dC
			break;
		case 13:
			// 違約金及遲延利息(一般)
			String[] colKeysPage13 = new String[] { "jsonDataKa", "jsonDataKb",
					"jsonDataKc", "jsonDataKd", "jsonDataKe" };
			JSONObject jsonPage13 = new JSONObject();
			if (!allJson.isEmpty()) {
				for (String colKey : colKeysPage13) {
					if (allJson.containsKey("_" + colKey)) {
						jsonPage13.put(colKey,
								Util.trim(allJson.getString("_" + colKey)));
					}
				}
			}
			if (meta != null) {
				meta.setContractNo(contractNo);
				meta.setAMonth(6);
				meta.setARate(10);
				meta.setBMonth(6);
				meta.setBRate(20);
				meta.setCRate(null);
				if (!jsonPage13.isEmpty()) {
					meta.setAMonth(Util.parseInt(Util.trim(jsonPage13
							.getString("jsonDataKa"))));
					meta.setARate(Util.parseInt(Util.trim(jsonPage13
							.getString("jsonDataKb"))));
					meta.setBMonth(Util.parseInt(Util.trim(jsonPage13
							.getString("jsonDataKc"))));
					meta.setBRate(Util.parseInt(Util.trim(jsonPage13
							.getString("jsonDataKd"))));
					meta.setCRate(getDecimal(jsonPage13.getString("jsonDataKe")));
				}
				lms9990Service2.save(meta);
			}
			break;
		case 14:
			// 違約金及遲延利息(政府留貸)
			if (meta != null) {
				meta.setContractNo(contractNo);
				meta.setCRate(null);
				if (!allJson.isEmpty()) {
					meta.setCRate(getDecimal(allJson.getString("_jsonDataL")));
				}
				lms9990Service2.save(meta);
			}
			break;
		case 15:
			// 資料提供
			String[] colKeysPage15 = new String[] { "cS31Com1", "cS31Com2",
					"cS31Com3", "cS31Com4", "cS31Com5", "cS31Com6", "cS31Com7",
					"cS31Com8" };
			int dataUseItem = 0;
			if (!allJson.isEmpty()) {
				if ("0".equals(Util.trim(allJson.getString("_cS31Com8")))) {
					// 全選
				} else {
					for (String colKey : colKeysPage15) {
						if (allJson.containsKey("_" + colKey)) {
							dataUseItem += Util.parseInt(Util.trim(allJson
									.getString("_" + colKey)));
						}
					}
				}
			}
			if (meta != null) {
				meta.setDataUseFlag(null);
				meta.setDataUseItem(null);
				if (!allJson.isEmpty()) {
					meta.setDataUseFlag(Util.trim(params.getString("_rS31Com")));
					// meta.setDataUseFlag(Util.trim(allJson.getString("_rS31Com")));
				}
				if (dataUseItem >= 0) {
					meta.setDataUseItem(dataUseItem);
				}
				lms9990Service2.save(meta);
			}
			break;
		case 16:
			// 服務
			if (meta != null) {
				C999M01B c999m01b = null;
				List<C999M01B> listC999m01b = lms9990Service2
						.findC999m01bByMainId(mainId);
				if (!listC999m01b.isEmpty()) {
					for (C999M01B model : listC999m01b) {
						if (CtrConstants.Book9990.KindType.兆豐國際商業銀行股份有限公司_乙方
								.equals(model.getType())) {
							c999m01b = model;
						}
					}
				}
				String[] colKeysPage16 = new String[] { "jsonDataN1",
						"jsonDataN2", "jsonDataN3", "jsonDataN4" };
				JSONObject jsonPage16 = new JSONObject();
				if (!allJson.isEmpty()) {
					for (String colKey : colKeysPage16) {
						if (allJson.containsKey("_" + colKey)) {
							jsonPage16.put(colKey,
									Util.trim(allJson.getString("_" + colKey)));
						}
					}
				}
				if (c999m01b != null) {
					c999m01b.setTel(null);
					c999m01b.setFax(null);
					c999m01b.setEMail(null);
					c999m01b.setOther(null);
					if (!jsonPage16.isEmpty()) {
						c999m01b.setTel(Util.trim(jsonPage16
								.getString("jsonDataN1")));
						c999m01b.setFax(Util.trim(jsonPage16
								.getString("jsonDataN2")));
						c999m01b.setEMail(Util.trim(jsonPage16
								.getString("jsonDataN3")));
						c999m01b.setOther(Util.trim(jsonPage16
								.getString("jsonDataN4")));
					}
				}
				if (meta != null) {
					meta.setContractNo(contractNo);
				}
				lms9990Service2.save(meta, c999m01b);
			}
			break;
		case 17:
			// 管轄法院
			if (meta != null) {
				meta.setContractNo(contractNo);
				meta.setCourtCode(null);
				if (!allJson.isEmpty()) {
					meta.setCourtCode(Util.trim(allJson.getString("_jsonDataO")));
				}
				lms9990Service2.save(meta);
			}
			break;
		default:
			if (meta != null) {
				meta.setContractNo(contractNo);
			}
			lms9990Service2.save(meta);
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		return result;
	}

	/**
	 * TempSave儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult tempSave(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "Y");
		result.add(saveAll(params));
		return result;
	}

	/**
	 * 一般儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult noTempSave(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		result.add(saveAll(params));
		return result;
	}

	/**
	 * 處理數值是否為空值
	 * 
	 * @param value
	 *            數值
	 * @return BigDecimal
	 */
	protected BigDecimal getDecimal(String value) {
		if (!"".equals(CapString.trimNull(value))) {
			return new BigDecimal(NumConverter.delCommaString(value));
		} else {
			return null;
		}
	}

	/**
	 * 儲存產品種類內容
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveS17(PageParameters params)
			throws CapException {
		// 對radio做額外處理(避免值變成陣列)
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		// 加總後的"資料保密_同意項目"
		// int dataUseItem = params.getInt("s25Check");

		// 取得Radio值
		String s20Radio = Util.trim(params.getString("s20Radio"));
		String s21Radio = Util.trim(params.getString("s21Radio"));
		String s22Radio = Util.trim(params.getString("s22Radio"));
		String s23aRadio = Util.trim(params.getString("s23aRadio"));
		String s23bRadio = Util.trim(params.getString("s23bRadio"));
		// String s25Radio = Util.trim(params.getString("s25Radio"));

		// 取得頁面值
		String formTab02a = Util.trim(params.getString("formTab02a"));
		String formTab03a = Util.trim(params.getString("formTab03a"));
		String formTab04a = Util.trim(params.getString("formTab04a"));
		String formTab05a = Util.trim(params.getString("formTab05a"));
		String formTab06a = Util.trim(params.getString("formTab06a"));
		String formTab07a = Util.trim(params.getString("formTab07a"));

		// 儲存所有頁籤JsonObject
		JSONObject jsonAll = new JSONObject();

		// 將頁面值轉換成JSON格式
		JSONObject jsonS18 = JSONObject.fromObject(formTab02a);
		JSONObject jsonS19 = JSONObject.fromObject(formTab03a);
		JSONObject jsonS20 = JSONObject.fromObject(formTab04a);
		JSONObject jsonS21 = JSONObject.fromObject(formTab05a);
		JSONObject jsonS22 = JSONObject.fromObject(formTab06a);
		JSONObject jsonS23 = JSONObject.fromObject(formTab07a);

		// 開始進行置換Radio工作
		replaceRadio(jsonS20, "_20Ara", s20Radio);
		replaceRadio(jsonS21, "_21Ara", s21Radio);
		replaceRadio(jsonS22, "_22Ara", s22Radio);
		replaceRadio(jsonS23, "_23Ara", s23aRadio);
		replaceRadio(jsonS23, "_23Arb", s23bRadio);

		jsonAll.put("s18Panel", jsonS18);
		jsonAll.put("s19Panel", jsonS19);
		jsonAll.put("s20Panel", jsonS20);
		jsonAll.put("s21Panel", jsonS21);
		jsonAll.put("s22Panel", jsonS22);
		jsonAll.put("s23Panel", jsonS23);
		jsonAll.put("s24Panel", jsonS23);

		C999M01A c999m01a = lms9990Service2.findC999m01aByMainId(mainId);
		C999S01A c999s01a = lms9990Service2.findC999s01aByOid(oid);
		if (c999m01a != null && c999s01a != null) {
			List<C999S01B> list = lms9990Service2
					.setJsonData(c999s01a, jsonAll);
			if (jsonS18.containsKey("_jsonDataA")) {
				c999s01a.setLoanAmt(LMSUtil.toBigDecimal(NumConverter
						.delCommaString(jsonS18.getString("_jsonDataA"))));
			}
			if (!list.isEmpty()) {
				// lms9990Service2.saveMetaListC999s01ab(list, null, c999m01a);
				lms9990Service2.saveMetaListC999s01ab(list, c999s01a, c999m01a);
			}
		}
		// 印出儲存成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	/**
	 * 儲存產品種類內容(政策留貸)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveS17A(PageParameters params)
			throws CapException {
		// 對radio做額外處理(避免值變成陣列)
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		// 取得頁面值
		String formTab02b = Util.trim(params.getString("formTab02b"));
		String formTab03b = Util.trim(params.getString("formTab03b"));
		String formTab04b = Util.trim(params.getString("formTab04b"));
		String formTab05b = Util.trim(params.getString("formTab05b"));
		String formTab06b = Util.trim(params.getString("formTab06b"));
		String formTab07b = Util.trim(params.getString("formTab07b"));
		String formTab08b = Util.trim(params.getString("formTab08b"));

		// 儲存所有頁籤JsonObject
		JSONObject jsonAll = new JSONObject();

		// 將頁面值轉換成JSON格式
		JSONObject jsonS28 = JSONObject.fromObject(formTab02b);
		JSONObject jsonS29 = JSONObject.fromObject(formTab03b);
		JSONObject jsonS30 = JSONObject.fromObject(formTab04b);
		JSONObject jsonS31 = JSONObject.fromObject(formTab05b);
		JSONObject jsonS32 = JSONObject.fromObject(formTab06b);
		JSONObject jsonS33 = JSONObject.fromObject(formTab07b);
		JSONObject jsonS34 = JSONObject.fromObject(formTab08b);

		jsonAll.put("s28Panel", jsonS28);
		jsonAll.put("s29Panel", jsonS29);
		jsonAll.put("s30Panel", jsonS30);
		jsonAll.put("s31Panel", jsonS31);
		jsonAll.put("s32Panel", jsonS32);
		jsonAll.put("s33Panel", jsonS33);
		jsonAll.put("s34Panel", jsonS34);

		C999M01A c999m01a = lms9990Service2.findC999m01aByMainId(mainId);
		C999S01A c999s01a = lms9990Service2.findC999s01aByOid(oid);
		if (c999m01a != null && c999s01a != null) {
			List<C999S01B> list = lms9990Service2
					.setJsonData(c999s01a, jsonAll);
			if (jsonS28.containsKey("_jsonDataH")) {
				c999s01a.setLoanAmt(LMSUtil.toBigDecimal(NumConverter
						.delCommaString(jsonS28.getString("_jsonDataH"))));
			}
			if (!list.isEmpty()) {
				// lms9990Service2.saveMetaListC999s01ab(list, null, c999m01a);
				lms9990Service2.saveMetaListC999s01ab(list, c999s01a, c999m01a);
			}
		}
		// 印出儲存成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	/**
	 * 依照"資料保密_同意項目"數值勾選相對應的checkBox(用List儲存要打勾的checkBox Id)
	 * 
	 * @param dataUseItem
	 *            資料保密_同意項目數值
	 * @return IResult
	 */
	private IResult setCheckBox(int dataUseItem) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Map<String, String> map = new HashMap<String, String>();
		List<String> needCheckList = new ArrayList<String>();
		map.put("1", "cS31Com1");
		map.put("2", "cS31Com2");
		map.put("4", "cS31Com3");
		map.put("8", "cS31Com4");
		map.put("16", "cS31Com5");
		map.put("32", "cS31Com6");
		map.put("64", "cS31Com7");
		// map.put("0", "cS31Com8");
		// 等於999代表數值為空
		if (dataUseItem != 999) {
			if (dataUseItem == 0) {
				// 全部勾選
				needCheckList.add("cS31Com8");
			} else {
				// 將整數轉為二進位List後由小到大排序(java)
				List<String> bList = Util.getBinaryList(dataUseItem);
				Collections.sort(bList);
				for (String str : bList) {
					needCheckList.add(map.get(str));
				}
			}
		}
		result.set("dataUseItem", needCheckList);
		return result;
	}

	/**
	 * 置換Json格式內Radio值
	 * 
	 * @param json
	 *            Json
	 * @param radioName
	 *            Radio名稱
	 * @param radioVal
	 *            要置換的Radio值
	 */
	private void replaceRadio(JSONObject json, String radioName, String radioVal) {
		if (!json.isEmpty()) {
			json.put(radioName, radioVal);
		}
	}

	/**
	 * 清空產品種類內容
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult resetS17(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult formTab02a = new CapAjaxFormResult();
		CapAjaxFormResult formTab03a = new CapAjaxFormResult();
		CapAjaxFormResult formTab04a = new CapAjaxFormResult();
		CapAjaxFormResult formTab05a = new CapAjaxFormResult();
		CapAjaxFormResult formTab06a = new CapAjaxFormResult();
		CapAjaxFormResult formTab07a = new CapAjaxFormResult();

		formTab02a.add(this.clearJsonCol(CtrConstants.Book9990.Page.契約金額));
		formTab03a.add(this.clearJsonCol(CtrConstants.Book9990.Page.借款用途));
		formTab04a.add(this.clearJsonCol(CtrConstants.Book9990.Page.動用方式));
		formTab05a.add(this.clearJsonCol(CtrConstants.Book9990.Page.撥款方式));
		formTab06a.add(this.clearJsonCol(CtrConstants.Book9990.Page.償還辦法));
		formTab07a.add(this.clearJsonCol(CtrConstants.Book9990.Page.利息計付1));
		formTab07a.add(this.clearJsonCol(CtrConstants.Book9990.Page.利息計付2));

		result.set("formTab02a", formTab02a);
		result.set("formTab03a", formTab03a);
		result.set("formTab04a", formTab04a);
		result.set("formTab05a", formTab05a);
		result.set("formTab06a", formTab06a);
		result.set("formTab07a", formTab07a);
		return result;
	}

	/**
	 * 清空產品種類內容(政策留貸)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult resetS17A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult formTab02b = new CapAjaxFormResult();
		CapAjaxFormResult formTab03b = new CapAjaxFormResult();
		CapAjaxFormResult formTab04b = new CapAjaxFormResult();
		CapAjaxFormResult formTab05b = new CapAjaxFormResult();
		CapAjaxFormResult formTab06b = new CapAjaxFormResult();
		CapAjaxFormResult formTab07b = new CapAjaxFormResult();
		CapAjaxFormResult formTab08b = new CapAjaxFormResult();

		formTab02b.add(this.clearJsonCol(CtrConstants.Book9990.Page.契約金額_政策留貸));
		formTab03b.add(this.clearJsonCol(CtrConstants.Book9990.Page.借款用途_政策留貸));
		formTab04b.add(this
				.clearJsonCol(CtrConstants.Book9990.Page.申請方式及借款期限_寬限期));
		formTab05b.add(this.clearJsonCol(CtrConstants.Book9990.Page.動用方式_政策留貸));
		formTab06b.add(this.clearJsonCol(CtrConstants.Book9990.Page.撥款方式_政策留貸));
		formTab07b.add(this.clearJsonCol(CtrConstants.Book9990.Page.償還辦法_政策留貸));
		formTab08b.add(this.clearJsonCol(CtrConstants.Book9990.Page.利息計付_政策留貸));

		result.set("formTab02b", formTab02b);
		result.set("formTab03b", formTab03b);
		result.set("formTab04b", formTab04b);
		result.set("formTab05b", formTab05b);
		result.set("formTab06b", formTab06b);
		result.set("formTab07b", formTab07b);
		result.set("formTab08b", formTab08b);
		return result;
	}

	/**
	 * 依照頁面清空Json欄位
	 * 
	 * @param page
	 *            頁面
	 * @return 清空後的結果
	 */
	private IResult clearJsonCol(int page) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] jsonCol = getJsonCol(page);
		if (jsonCol != null) {
			for (String key : jsonCol) {
				result.set("_" + key, space);
			}
		}
		return result;
	}

	/**
	 * 查詢產品種類內容
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryS17(PageParameters params)
			throws CapException {
		String oid = Util.trim(params.getString(EloanConstants.OID));
		// String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 一般
		CapAjaxFormResult formTab02a = new CapAjaxFormResult();
		CapAjaxFormResult formTab03a = new CapAjaxFormResult();
		CapAjaxFormResult formTab04a = new CapAjaxFormResult();
		CapAjaxFormResult formTab05a = new CapAjaxFormResult();
		CapAjaxFormResult formTab06a = new CapAjaxFormResult();
		CapAjaxFormResult formTab07a = new CapAjaxFormResult();

		Map<String, String> prodMap = prodService.getProdKindName();

		C999S01A c999s01a = lms9990Service2.findC999s01aByOid(oid);
		if (c999s01a != null) {
			List<C999S01B> list = c999s01a.getC999s01bs();
			if (!list.isEmpty()) {
				for (C999S01B c999s01b : list) {
					String type = Util.trim(c999s01b.getType());
					if (!Util.isEmpty(Util.trim(c999s01b.getJsonData()))) {
						JSONObject json = JSONObject.fromObject(Util
								.trim(c999s01b.getJsonData()));
						if (CtrConstants.Book9990.Type.契約金額.equals(type)) {
							if (!json.isEmpty()) {
								formTab02a.add(setPanel(json, "_"));
								formTab02a.set("_jsonDataA",
										c999s01a.getLoanAmt());
							}
						} else if (CtrConstants.Book9990.Type.借款用途.equals(type)) {
							if (!json.isEmpty()) {
								formTab03a.add(setPanel(json, "_"));
							}
						} else if (CtrConstants.Book9990.Type.動用方式.equals(type)) {
							if (!json.isEmpty()) {
								formTab04a.add(setPanel(json, "_"));
							}
						} else if (CtrConstants.Book9990.Type.撥款方式.equals(type)) {
							if (!json.isEmpty()) {
								formTab05a.add(setPanel(json, "_"));
							}
						} else if (CtrConstants.Book9990.Type.償還辦法.equals(type)) {
							if (!json.isEmpty()) {
								formTab06a.add(setPanel(json, "_"));
							}
						} else if (CtrConstants.Book9990.Type.利息計付_得隨時清償
								.equals(type)
								|| CtrConstants.Book9990.Type.利息計付_限制清償期間
										.equals(type)) {
							// 利息計付
							if (!json.isEmpty()) {
								formTab07a.add(setPanel(json, "_"));
							}
						}
					}
				}
			}
			result.set("itemNo",
					lms9990Service2.getKeyName(Util.trim(c999s01a.getItemNo())));
			result.set("cntrNo", Util.trim(c999s01a.getCntrNo()));
			result.set("prodKind",
					prodMap.get(Util.trim(c999s01a.getProdKind())));
		}

		result.set("formTab02a", formTab02a);
		result.set("formTab03a", formTab03a);
		result.set("formTab04a", formTab04a);
		result.set("formTab05a", formTab05a);
		result.set("formTab06a", formTab06a);
		result.set("formTab07a", formTab07a);

		// 設定Radio到前端
		result.set(
				"s20Radio",
				Util.trim(formTab04a.get("_20Ara")).replace(",",
						UtilConstants.Mark.SPACE));
		result.set(
				"s21Radio",
				Util.trim(formTab05a.get("_21Ara")).replace(",",
						UtilConstants.Mark.SPACE));
		result.set(
				"s22Radio",
				Util.trim(formTab06a.get("_22Ara")).replace(",",
						UtilConstants.Mark.SPACE));
		result.set(
				"s23aRadio",
				Util.trim(formTab07a.get("_23Ara")).replace(",",
						UtilConstants.Mark.SPACE));
		result.set(
				"s23bRadio",
				Util.trim(formTab07a.get("_23Arb")).replace(",",
						UtilConstants.Mark.SPACE));

		return result;
	}

	/**
	 * 查詢產品種類內容(政策留貸)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryS17A(PageParameters params)
			throws CapException {
		String oid = Util.trim(params.getString(EloanConstants.OID));
		// String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		CapAjaxFormResult result = new CapAjaxFormResult();

		// 政策留貸
		CapAjaxFormResult formTab02b = new CapAjaxFormResult();
		CapAjaxFormResult formTab03b = new CapAjaxFormResult();
		CapAjaxFormResult formTab04b = new CapAjaxFormResult();
		CapAjaxFormResult formTab05b = new CapAjaxFormResult();
		CapAjaxFormResult formTab06b = new CapAjaxFormResult();
		CapAjaxFormResult formTab07b = new CapAjaxFormResult();
		CapAjaxFormResult formTab08b = new CapAjaxFormResult();

		Map<String, String> prodMap = prodService.getProdKindName();

		C999S01A c999s01a = lms9990Service2.findC999s01aByOid(oid);
		if (c999s01a != null) {
			List<C999S01B> list = c999s01a.getC999s01bs();
			if (!list.isEmpty()) {
				for (C999S01B c999s01b : list) {
					String type = Util.trim(c999s01b.getType());
					if (!Util.isEmpty(Util.trim(c999s01b.getJsonData()))) {
						JSONObject json = JSONObject.fromObject(Util
								.trim(c999s01b.getJsonData()));
						if (CtrConstants.Book9990.Type.契約金額_政策留貸.equals(type)) {
							if (!json.isEmpty()) {
								formTab02b.add(setPanel(json, "_"));
								formTab02b.set("_jsonDataH",
										c999s01a.getLoanAmt());
							}
						} else if (CtrConstants.Book9990.Type.借款用途_政策留貸
								.equals(type)) {
							if (!json.isEmpty()) {
								formTab03b.add(setPanel(json, "_"));
							}
						} else if (CtrConstants.Book9990.Type.申請方式及借款期限_寬限期
								.equals(type)) {
							if (!json.isEmpty()) {
								formTab04b.add(setPanel(json, "_"));
							}
						} else if (CtrConstants.Book9990.Type.動用方式_政策留貸
								.equals(type)) {
							if (!json.isEmpty()) {
								formTab05b.add(setPanel(json, "_"));
							}
						} else if (CtrConstants.Book9990.Type.撥款方式_政策留貸
								.equals(type)) {
							if (!json.isEmpty()) {
								formTab06b.add(setPanel(json, "_"));
							}
						} else if (CtrConstants.Book9990.Type.償還辦法_政策留貸
								.equals(type)) {
							if (!json.isEmpty()) {
								formTab07b.add(setPanel(json, "_"));
							}
						} else if (CtrConstants.Book9990.Type.利息計付_政策留貸
								.equals(type)) {
							if (!json.isEmpty()) {
								formTab08b.add(setPanel(json, "_"));
							}
						}
					}
				}
			}
			result.set("itemNo",
					lms9990Service2.getKeyName(Util.trim(c999s01a.getItemNo())));
			result.set("cntrNo", Util.trim(c999s01a.getCntrNo()));
			result.set("prodKind",
					prodMap.get(Util.trim(c999s01a.getProdKind())));
		}
		result.set("formTab02b", formTab02b);
		result.set("formTab03b", formTab03b);
		result.set("formTab04b", formTab04b);
		result.set("formTab05b", formTab05b);
		result.set("formTab06b", formTab06b);
		result.set("formTab07b", formTab07b);
		result.set("formTab08b", formTab08b);

		return result;
	}

	/**
	 * 將Json內的值依照Key塞到前端畫面
	 * 
	 * @param json
	 *            json
	 * @param arg
	 *            arg 額外Key前綴字
	 * @return IResult
	 */
	private IResult setPanel(JSONObject json, String arg) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] keys = Util.getMapKey(json);
		if (keys.length > 0) {
			for (String key : keys) {
				result.set(arg + key, Util.trim(json.get(key)));
			}
		}
		return result;
	}

	/**
	 * 查詢個金約據書頁面資料(子檔)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL999m01aM07(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String mainId = params.getString(EloanConstants.MAIN_ID);
		CapAjaxFormResult result = new CapAjaxFormResult();

		CapAjaxFormResult formTabs2 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs3 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs4 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs5 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs6 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs7 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs13 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs15 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs16 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs17 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs8 = new CapAjaxFormResult();

		C999M01A c999m01a = lms9990Service2.findC999m01aByMainId(mainId);
		// 設定標頭
		result.set("ActionMForm",(CapAjaxFormResult) setTitle(mainId));

		switch (page) {
		case 1:
			break;
		case 2:
			formTabs2.set(
					"jsonDataA",
					getRealContent(mainId, CtrConstants.Book9990.Type.契約金額,
							false));
			formTabs2.set("lJsonDataA",
					getCNumAllStr(Util.trim(formTabs2.get("jsonDataA"))));
			result.set("formTabs2", formTabs2);
			break;
		case 3:
			formTabs3.set(
					"jsonDataB",
					getRealContent(mainId, CtrConstants.Book9990.Type.借款用途,
							false));
			formTabs3.set(
					"kJsonDataB",
					getRealContent(mainId, CtrConstants.Book9990.Type.借款用途,
							true));
			result.set("formTabs3", formTabs3);
			break;
		case 4:
			formTabs4.add(setSingle(mainId, CtrConstants.Book9990.Type.動用方式));
			formTabs4.set(
					"jsonDataC",
					getRealContent(mainId, CtrConstants.Book9990.Type.動用方式,
							false));
			formTabs4.set(
					"kJsonDataC",
					getRealContent(mainId, CtrConstants.Book9990.Type.動用方式,
							true));
			result.set("formTabs4", formTabs4);
			break;
		case 5:
			formTabs5.add(setSingle(mainId, CtrConstants.Book9990.Type.撥款方式));
			formTabs5.set(
					"jsonDataD",
					getRealContent(mainId, CtrConstants.Book9990.Type.撥款方式,
							false));
			formTabs5.set(
					"kJsonDataD",
					getRealContent(mainId, CtrConstants.Book9990.Type.撥款方式,
							true));
			result.set("formTabs5", formTabs5);
			break;
		case 6:
			formTabs6.add(setSingle(mainId, CtrConstants.Book9990.Type.償還辦法));
			formTabs6.set(
					"jsonDataE",
					getRealContent(mainId, CtrConstants.Book9990.Type.償還辦法,
							false));
			formTabs6.set(
					"kJsonDataE",
					getRealContent(mainId, CtrConstants.Book9990.Type.償還辦法,
							true));
			result.set("formTabs6", formTabs6);
			break;
		case 7:
			formTabs7.add(setSingle(mainId,
					CtrConstants.Book9990.Type.利息計付_限制清償期間));
			formTabs7.add(setSingle(mainId,
					CtrConstants.Book9990.Type.利息計付_得隨時清償));
			formTabs7.set(
					"jsonDataF01",
					getRealContent(mainId,
							CtrConstants.Book9990.Type.利息計付_限制清償期間, false));
			formTabs7.set(
					"kJsonDataF01",
					getRealContent(mainId,
							CtrConstants.Book9990.Type.利息計付_限制清償期間, true));
			formTabs7.set(
					"jsonDataF02",
					getRealContent(mainId,
							CtrConstants.Book9990.Type.利息計付_得隨時清償, false));
			formTabs7.set(
					"kJsonDataF02",
					getRealContent(mainId,
							CtrConstants.Book9990.Type.利息計付_得隨時清償, true));
			result.set("formTabs7", formTabs7);
			break;
		case 8:
			C999M01D c999m01dA = lms9990Service2.findC999m01dByUniqueKey(
					mainId, CtrConstants.Book9990.itemType.特別條款);
			C999M01D c999m01dC = lms9990Service2.findC999m01dByUniqueKey(
					mainId, CtrConstants.Book9990.itemType.個別商議條款);
			formTabs8.set(
					"itemContentA",
					(c999m01dA == null) ? UtilConstants.Mark.SPACE : Util
							.trim(c999m01dA.getItemContent()));
			formTabs8.set(
					"itemContentC",
					(c999m01dC == null) ? UtilConstants.Mark.SPACE : Util
							.trim(c999m01dC.getItemContent()));
			result.set("formTabs8", formTabs8);
			break;
		case 13:
			formTabs13.set(
					"_jsonDataKa",
					(c999m01a == null) ? UtilConstants.Mark.SPACE : Util
							.trim(c999m01a.getAMonth()));
			formTabs13.set(
					"_jsonDataKb",
					(c999m01a == null) ? UtilConstants.Mark.SPACE : Util
							.trim(c999m01a.getARate()));
			formTabs13.set(
					"_jsonDataKc",
					(c999m01a == null) ? UtilConstants.Mark.SPACE : Util
							.trim(c999m01a.getBMonth()));
			formTabs13.set(
					"_jsonDataKd",
					(c999m01a == null) ? UtilConstants.Mark.SPACE : Util
							.trim(c999m01a.getBRate()));
			formTabs13.set(
					"_jsonDataKe",
					(c999m01a == null) ? UtilConstants.Mark.SPACE : Util
							.trim(c999m01a.getCRate()));
			result.set("formTabs13", formTabs13);
			break;
		case 15:
			formTabs15.set(
					"_rS31Com",
					(c999m01a == null) ? UtilConstants.Mark.SPACE : Util
							.trim(c999m01a.getDataUseFlag()));
			formTabs15
					.add(setCheckBox((c999m01a.getDataUseItem() == null) ? 999
							: c999m01a.getDataUseItem()));
			result.set("formTabs15", formTabs15);
			break;
		case 16:
			if (c999m01a != null) {
				C999M01B c999m01b = null;
				List<C999M01B> listC999m01b = lms9990Service2
						.findC999m01bByMainId(mainId);
				if (!listC999m01b.isEmpty()) {
					for (C999M01B model : listC999m01b) {
						if (CtrConstants.Book9990.KindType.兆豐國際商業銀行股份有限公司_乙方
								.equals(model.getType())) {
							c999m01b = model;
						}
					}
				}
				formTabs16.set(
						"_jsonDataN1",
						(c999m01b == null) ? UtilConstants.Mark.SPACE : Util
								.trim(c999m01b.getTel()));
				formTabs16.set(
						"_jsonDataN2",
						(c999m01b == null) ? UtilConstants.Mark.SPACE : Util
								.trim(c999m01b.getFax()));
				formTabs16.set(
						"_jsonDataN3",
						(c999m01b == null) ? UtilConstants.Mark.SPACE : Util
								.trim(c999m01b.getEMail()));
				formTabs16.set(
						"_jsonDataN4",
						(c999m01b == null) ? UtilConstants.Mark.SPACE : Util
								.trim(c999m01b.getOther()));
				result.set("formTabs16", formTabs16);
			}
			break;
		case 17:
			formTabs17.set(
					"_jsonDataO",
					(c999m01a == null) ? UtilConstants.Mark.SPACE : Util
							.trim(c999m01a.getCourtCode()));
			// formTabs17.set(
			// "_jsonDataO",
			// (c999m01a == null) ? UtilConstants.Mark.SPACE
			// : codeTypeService.getDescOfCodeType("taiwancourt",
			// Util.trim(c999m01a.getCourtCode())));
			result.set("formTabs17", formTabs17);
			break;
		default:
		}
		result.set("page", page);
		return result;
	}

	/**
	 * 查詢個金約據書頁面資料(子檔)-政策留貸
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL999m01aM07A(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String mainId = params.getString(EloanConstants.MAIN_ID);
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult formTabs10 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs11 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs9 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs12 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs18 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs19 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs20 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs14 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs15 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs16 = new CapAjaxFormResult();
		CapAjaxFormResult formTabs17 = new CapAjaxFormResult();

		C999M01A c999m01a = lms9990Service2.findC999m01aByMainId(mainId);
		// 設定標頭
		result.set("ActionMForm", (CapAjaxFormResult)setTitle(mainId));

		switch (page) {
		case 1:
			break;
		case 10:
			formTabs10.add(setSingle(mainId,
					CtrConstants.Book9990.Type.契約金額_政策留貸));
			formTabs10.set("lJsonDataH",
					getCNumAllStr(Util.trim(formTabs10.get("jsonDataH"))));
			result.set("formTabs10", formTabs10);
			break;
		case 11:
			formTabs11.add(setSingle(mainId,
					CtrConstants.Book9990.Type.借款用途_政策留貸));
			result.set("formTabs11", formTabs11);
			break;
		case 9:
			formTabs9.add(setSingle(mainId,
					CtrConstants.Book9990.Type.申請方式及借款期限_寬限期));
			result.set("formTabs9", formTabs9);
			break;
		case 12:
			formTabs12.add(setSingle(mainId,
					CtrConstants.Book9990.Type.動用方式_政策留貸));
			result.set("formTabs12", formTabs12);
			break;
		case 18:
			formTabs18.add(setSingle(mainId,
					CtrConstants.Book9990.Type.撥款方式_政策留貸));
			result.set("formTabs18", formTabs18);
			break;
		case 19:
			formTabs19.add(setSingle(mainId,
					CtrConstants.Book9990.Type.償還辦法_政策留貸));
			result.set("formTabs19", formTabs19);
			break;
		case 20:
			formTabs20.add(setSingle(mainId,
					CtrConstants.Book9990.Type.利息計付_政策留貸));
			result.set("formTabs20", formTabs20);
			break;
		case 14:
			formTabs14.set(
					"_jsonDataL",
					(c999m01a == null) ? UtilConstants.Mark.SPACE : Util
							.trim(c999m01a.getCRate()));
			result.set("formTabs14", formTabs14);
			break;
		case 15:
			formTabs15.set(
					"_rS31Com",
					(c999m01a == null) ? UtilConstants.Mark.SPACE : Util
							.trim(c999m01a.getDataUseFlag()));
			formTabs15
					.add(setCheckBox((c999m01a.getDataUseItem() == null) ? 999
							: c999m01a.getDataUseItem()));
			result.set("formTabs15", formTabs15);
			break;
		case 16:
			if (c999m01a != null) {
				C999M01B c999m01b = null;
				List<C999M01B> listC999m01b = lms9990Service2
						.findC999m01bByMainId(mainId);
				if (!listC999m01b.isEmpty()) {
					for (C999M01B model : listC999m01b) {
						if (CtrConstants.Book9990.KindType.兆豐國際商業銀行股份有限公司_乙方
								.equals(model.getType())) {
							c999m01b = model;
						}
					}
				}
				formTabs16.set(
						"_jsonDataN1",
						(c999m01b == null) ? UtilConstants.Mark.SPACE : Util
								.trim(c999m01b.getTel()));
				formTabs16.set(
						"_jsonDataN2",
						(c999m01b == null) ? UtilConstants.Mark.SPACE : Util
								.trim(c999m01b.getFax()));
				formTabs16.set(
						"_jsonDataN3",
						(c999m01b == null) ? UtilConstants.Mark.SPACE : Util
								.trim(c999m01b.getEMail()));
				formTabs16.set(
						"_jsonDataN4",
						(c999m01b == null) ? UtilConstants.Mark.SPACE : Util
								.trim(c999m01b.getOther()));
			}
			result.set("formTabs16", formTabs16);
			break;
		case 17:
			formTabs17.set(
					"_jsonDataO",
					(c999m01a == null) ? UtilConstants.Mark.SPACE : Util
							.trim(c999m01a.getCourtCode()));
			// formTabs17.set(
			// "_jsonDataO",
			// (c999m01a == null) ? UtilConstants.Mark.SPACE
			// : codeTypeService.getDescOfCodeType("taiwancourt",
			// Util.trim(c999m01a.getCourtCode())));
			result.set("formTabs17", formTabs17);
			break;
		default:
		}
		result.set("page", page);
		return result;
	}

	/**
	 * 依照完整字串取得包含中文數字之完整字串
	 * 
	 * @param allStr
	 *            完整字串
	 * @return 包含中文數字之完整字串
	 */
	private String getCNumAllStr(String allStr) {
		int size = allStr.length();
		if (size > 0) {
			String str = allStr.replace(",", UtilConstants.Mark.SPACE);
			size = str.length();
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			StringBuilder numSb = new StringBuilder();
			for (int i = 0; i < size; i++) {
				if (Util.isNumeric(str.charAt(i))) {
					numSb.append(Util.trim(str.charAt(i)));
				} else {
					if (numSb.length() > 0) {
						sb.append(
								NumConverter.toChineseNumberFull(numSb
										.toString())).append("元整");
						numSb.setLength(0);
					}
					sb.append(Util.trim(str.charAt(i)));
				}
			}
			if (sb.length() == 0) {
				if (numSb.length() > 0) {
					sb.append(
							NumConverter.toChineseNumberFull(numSb.toString()))
							.append("元整");
					numSb.setLength(0);
				}
			}
			return sb.toString();
		} else {
			return Util.trim(allStr);
		}
	}

	/**
	 * 單一產品種類時依類型將JsonData依欄位名稱傳到前端
	 * 
	 * @param mainId
	 *            mainId
	 * @param type
	 *            類型
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	private IResult setSingle(String mainId, String type) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<C999S01A> list = lms9990Service2.findC999s01aByMainId(mainId);
		C999M01A model = lms9990Service2.findC999m01aByMainId(mainId);
		if (!list.isEmpty()) {
			if (CtrConstants.Book9990.Type.利息計付_限制清償期間.equals(type)) {
				// 項次為刪除的產品種類不設定到前端
				if (!CtrConstants.Book9990.ItemNo.刪除.equals(Util.trim(list.get(
						0).getItemNo()))) {
					// 單一產品種類
					List<C999S01B> listJsonData = list.get(0).getC999s01bs();
					if (!listJsonData.isEmpty()) {
						for (C999S01B c999s01b : listJsonData) {
							if (type.equals(Util.trim(c999s01b.getType()))) {
								JSONObject json = JSONObject.fromObject(Util
										.trim(c999s01b.getJsonData()));
								if (list.size() == 1) {
									if (CtrConstants.Book9990.contractType.政策性留學貸款
											.equals(Util.trim(model
													.getContractType()))) {
										result.add(getSingle(json, false));
									} else {
										result.add(getSingle(json, true));
									}
								}
							}
						}
					}
				}
			} else if (list.size() == 1) {
				// 項次為刪除的產品種類不設定到前端
				if (!CtrConstants.Book9990.ItemNo.刪除.equals(Util.trim(list.get(
						0).getItemNo()))) {
					// 單一產品種類
					List<C999S01B> listJsonData = list.get(0).getC999s01bs();
					if (!listJsonData.isEmpty()) {
						for (C999S01B c999s01b : listJsonData) {
							if (type.equals(Util.trim(c999s01b.getType()))) {
								JSONObject json = JSONObject.fromObject(Util
										.trim(c999s01b.getJsonData()));
								result = DataParse.toResult(json);
							}
						}
					}
				}
			}
		}
		return result;
	}

	/**
	 * 取得單一產品JsonData內容
	 * 
	 * @param type
	 *            類型
	 * @param list
	 *            產品種類List
	 * @param isSpec
	 *            是否為利息計付_限制清償期間（青少年專案）
	 * @return
	 * @throws CapException
	 */
	private IResult getSingle(JSONObject json, boolean isSpec)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		if (isSpec) {
			JSONObject specJson = new JSONObject();
			// 青少年專案相關JsonData欄位
			String specKeys[] = new String[] { "jsonDataF03a", "jsonDataF03b",
					"jsonDataF03c", "jsonDataF03d", "jsonDataF03e",
					"jsonDataF03f", "jsonDataF03g", "jsonDataF03h",
					"jsonDataF03i", "jsonDataF03j", "23Acc1", "jsonDataF03k",
					"jsonDataF03l", "jsonDataF03m", "jsonDataF03n",
					"jsonDataF03o", "jsonDataF03p", "jsonDataF03q",
					"jsonDataF03r", "23Acc2", "jsonDataF03s", "jsonDataF03t",
					"jsonDataF03u", "jsonDataF03v", "jsonDataF03w",
					"jsonDataF03x", "jsonDataF03y", "jsonDataF03z", "23Acc3",
					"jsonDataF03A", "jsonDataF03B", "jsonDataF03C",
					"jsonDataF03D" };
			for (String specKey : specKeys) {
				if (json.containsKey(specKey)) {
					specJson.put(specKey, Util.trim(json.get(specKey)));
				}
			}
			result = DataParse.toResult(specJson);
		} else {
			result = DataParse.toResult(json);
		}
		return result;
	}

	/**
	 * 設定標題至前端
	 * 
	 * @param mainId
	 *            mainId
	 * @return IResult
	 */
	private IResult setTitle(String mainId) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		C999M01A meta = lms9990Service2.findC999m01aByMainId(mainId);
		List<C999M01B> listC999m01b = lms9990Service2
				.findC999m01bByMainId(mainId);
		List<C999M01C> listC999m01c = lms9990Service2
				.findC999m01cByMainId(mainId);
		// 設定客戶名稱與編號
		if (meta != null) {
			result.set("custData", Util.trim(meta.getCustName()));
			result.set("contractNo", Util.trim(meta.getContractNo()));
		}
		// 設定立約人
		if (!listC999m01b.isEmpty()) {
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS9990M07Page.class);
			StringBuilder sb = new StringBuilder();
			for (C999M01B c999m01b : listC999m01b) {
				if (CtrConstants.Book9990.KindType.借款人_甲方.equals(Util
						.trim(c999m01b.getType()))) {
					// 甲方 C999M01AM07.type1
					sb.append(pop.getProperty("C999M01AM07.type1"))
							.append(Util.trim(c999m01b.getCustName()))
							.append("，");
				} else {
					// 乙方 C999M01AM07.type2
					sb.append(pop.getProperty("C999M01AM07.type2"))
							.append(Util.trim(c999m01b.getCustName()))
							.append("，");
				}
			}
			result.set("custData1", sb.deleteCharAt(sb.length() - 1)
					.append("。").toString());
		}
		// 設定連保人與保證人(連保人->G 保證人->N)
		if (!listC999m01c.isEmpty()) {
			// C.共同借款人
			// G.連帶保證人(個金)
			// N.ㄧ般保證人(個金)
			// S.擔保品提供人(個金)
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			StringBuilder sb2 = new StringBuilder();
			sb2.setLength(0);
			// String custData2 = null;
			// String custData3 = null;
			for (C999M01C c999m01c : listC999m01c) {
				String custPos = Util.trim(c999m01c.getCustPos());
				if (CtrConstants.Book9990.CustPos.共同借款人或共同發票人.equals(custPos)) {
					sb2.append(
							(sb2.length() > 0) ? "，" : UtilConstants.Mark.SPACE)
							.append(Util.trim(c999m01c.getCustName()));
				} else if (CtrConstants.Book9990.CustPos.連帶保證人或擔保品提供人兼連帶保證人
						.equals(custPos)
						|| CtrConstants.Book9990.CustPos.一般保證人或擔保品提供人兼一般保證人
								.equals(custPos)
						|| CtrConstants.Book9990.CustPos.擔保品提供人.equals(custPos)) {
					sb.append(
							(sb.length() > 0) ? "，" : UtilConstants.Mark.SPACE)
							.append(Util.trim(c999m01c.getCustName()));
				}
			}
			result.set("custData2", sb2.toString());
			result.set("custData3", sb.toString());
		}
		return result;
	}

	/**
	 * 取得合併後的內容(其他)
	 * 
	 * @param mainId
	 * @param type
	 * @return
	 * @throws CapMessageException
	 */
	private String getRealContent(String mainId, String type,
			boolean getSubTitle) throws CapMessageException {
		return lms9990Service2
				.comBineC999s01b(mainId, type, getSubTitle, false).replace(
						"null、", "");
	}

	/**
	 * 依照個金約據書頁面取得相關Json欄位，若查無Json欄位則回傳空值
	 * 
	 * @param page
	 *            個金約據書頁面
	 * @return 無資料: null 有資料: Json欄位名稱
	 */
	private String[] getJsonCol(int page) {
		switch (page) {
		case CtrConstants.Book9990.Page.契約金額:
			return new String[] { "jsonDataA" };
		case CtrConstants.Book9990.Page.契約金額_政策留貸:
			return new String[] { "jsonDataH" };
		case CtrConstants.Book9990.Page.借款用途:
			return new String[] { "jsonDataB", "19Ac1", "19Ac2", "19Ac3",
					"19Ac4", "19Ac5", "19Ac6", "19Ac7" };
		case CtrConstants.Book9990.Page.借款用途_政策留貸:
			return new String[] { "jsonDataI" };
		case CtrConstants.Book9990.Page.申請方式及借款期限_寬限期:
			return new String[] { "jsonDataGa", "jsonDataGb", "jsonDataGc",
					"jsonDataGd", "jsonDataGe", "jsonDataGf", "jsonDataGg",
					"jsonDataGh" };
		case CtrConstants.Book9990.Page.動用方式:
			return new String[] { "20Ara", "20Aca", "jsonDataC", "jsonDataC1",
					"jsonDataC2", "jsonDataC3", "jsonDataC4", "jsonDataC5",
					"jsonDataC6", "jsonDataC7", "jsonDataC8", "jsonDataC9",
					"jsonDataC10", "jsonDataC11", "jsonDataC12", "jsonDataC13",
					"jsonDataC14", "jsonDataC15", "jsonDataC16", "jsonDataC17",
					"jsonDataC18", "jsonDataC19", "jsonDataC20", "jsonDataC21",
					"jsonDataC22", "jsonDataC23", "jsonDataC24", "jsonDataC25",
					"jsonDataC26", "jsonDataC27", "jsonDataC28", "jsonDataC29",
					"jsonDataC30", "jsonDataC31", "jsonDataC32" };
		case CtrConstants.Book9990.Page.動用方式_政策留貸:
			return new String[] { "jsonDataJa", "jsonDataJb", "jsonDataJc",
					"jsonDataJd", "jsonDataJe", "jsonDataJf", "jsonDataJg",
					"jsonDataJh", "jsonDataJi" };
		case CtrConstants.Book9990.Page.撥款方式:
			return new String[] { "21Ara", "21Aca", "jsonDataD", "jsonDataD1",
					"jsonDataD2", "jsonDataD3", "jsonDataD4" };
		case CtrConstants.Book9990.Page.撥款方式_政策留貸:
			return new String[] { "jsonDataJp", "jsonDataJq" };
		case CtrConstants.Book9990.Page.償還辦法:
			return new String[] { "22Ara", "22Aca", "jsonDataE", "jsonDataE1",
					"jsonDataE2", "jsonDataE3", "jsonDataE4" };
		case CtrConstants.Book9990.Page.償還辦法_政策留貸:
			return new String[] { "jsonDataJr", "jsonDataJs" };
		case CtrConstants.Book9990.Page.利息計付1:
			return new String[] { "23Ara", "23Aca", "jsonDataF01",
					"jsonDataF01a", "jsonDataF01b", "jsonDataF01c",
					"jsonDataF01d", "jsonDataF01e", "jsonDataF01f",
					"jsonDataF01g", "jsonDataF01h", "jsonDataF01i",
					"jsonDataF01j", "jsonDataF01k", "jsonDataF01l",
					"jsonDataF01m", "jsonDataF01n", "jsonDataF01o",
					"jsonDataF01p", "jsonDataF01q", "jsonDataF01r",
					"jsonDataF01s", "jsonDataF03a", "jsonDataF03b",
					"jsonDataF03c", "jsonDataF03d", "jsonDataF03e",
					"jsonDataF03f", "jsonDataF03g", "jsonDataF03h",
					"jsonDataF03i", "jsonDataF03j", "23Acc1", "jsonDataF03k",
					"jsonDataF03l", "jsonDataF03m", "jsonDataF03n",
					"jsonDataF03o", "jsonDataF03p", "jsonDataF03q",
					"jsonDataF03r", "23Acc2", "jsonDataF03s", "jsonDataF03t",
					"jsonDataF03u", "jsonDataF03v", "jsonDataF03w",
					"jsonDataF03x", "jsonDataF03y", "jsonDataF03z", "23Acc3",
					"jsonDataF03A", "jsonDataF03B", "jsonDataF03C",
					"jsonDataF03D" };
		case CtrConstants.Book9990.Page.利息計付2:
			return new String[] { "23Arb", "23Acb", "jsonDataF02",
					"jsonDataF02a", "jsonDataF02b", "jsonDataF02c",
					"jsonDataF02d", "jsonDataF02e" };
		case CtrConstants.Book9990.Page.利息計付_政策留貸:
			return new String[] { "jsonDataJt", "jsonDataJu" };
		case CtrConstants.Book9990.Page.違約金及遲延利息:
			return new String[] { "jsonDataKa", "jsonDataKb", "jsonDataKc",
					"jsonDataKd", "jsonDataKe" };
		case CtrConstants.Book9990.Page.違約金及遲延利息_政策留貸:
			return new String[] { "jsonDataL" };
		case CtrConstants.Book9990.Page.資料提供:
			return new String[] { "rS31Com", "cS31Com1", "cS31Com2",
					"cS31Com3", "cS31Com4", "cS31Com5", "cS31Com6", "cS31Com7",
					"cS31Com8" };
		case CtrConstants.Book9990.Page.資料提供_政策留貸:
			return new String[] { "rS31Com", "cS31Com1", "cS31Com2",
					"cS31Com3", "cS31Com4", "cS31Com5", "cS31Com6", "cS31Com7",
					"cS31Com8" };
		case CtrConstants.Book9990.Page.服務:
			return new String[] { "jsonDataN1", "jsonDataN2", "jsonDataN3",
					"jsonDataN4" };
		case CtrConstants.Book9990.Page.服務_政策留貸:
			return new String[] { "jsonDataN1", "jsonDataN2", "jsonDataN3",
					"jsonDataN4" };
		case CtrConstants.Book9990.Page.管轄法院:
			return new String[] { "jsonDataO" };
		case CtrConstants.Book9990.Page.管轄法院_政策留貸:
			return new String[] { "jsonDataO" };
		default:
			return null;
		}
	}
}
