/* 
 * C140S07BDaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.C140S07BDao;
import com.mega.eloan.lms.model.C140M04B;
import com.mega.eloan.lms.model.C140M07A;
import com.mega.eloan.lms.model.C140S07B;

/**
 * <pre>
 * 徵信調查報告書第柒章-授信明細(子)
 * </pre>
 * 
 * @since 2011/10/05
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/05,Tim<PERSON>hiang, new
 *          </ul>
 */
@Repository
public class C140S07BDaoImpl extends LMSJpaDao<C140S07B, String> implements C140S07BDao {

	@Override
	public int deleteByMeta(C140M07A meta) {
		Query query = entityManager.createNamedQuery("ces140s07b.deleteByMainIdAndPid");
		query.setParameter("mainId", meta.getMainId());
		query.setParameter("pid", meta.getUid());
		return query.executeUpdate();
	}
	
	@Override
	public int deleteByMeta(C140M04B meta) {
		Query query = entityManager.createNamedQuery("ces140s07b.deleteByMainIdAndPid");
		query.setParameter("mainId", meta.getMainId());
		query.setParameter("pid", meta.getUid());
		return query.executeUpdate();
	}
}// ;
