package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.io.IOUtils;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipOutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FileDownloadService;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.jcs.common.Util;

/**
 * 線上貸款
 */
@Service("cls1220r04rptservice")
public class CLS1220R04RptServiceImpl implements FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1220R04RptServiceImpl.class);

	@Resource
	CLSService clsService;

	@Resource
	DocFileService docFileService;;

	@Override
	public byte[] getContent(PageParameters params)
			throws FileNotFoundException, IOException, Exception {

		ByteArrayOutputStream baos = null;
		try {
			baos = this.creatDoc(params);			
			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
		} finally {
			IOUtils.closeQuietly(baos);
		}
		return null;
	}

	private ByteArrayOutputStream creatDoc(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		// ===========================
		String c1220m01a_mainId = params.getString("mainId");

		ISearch pageSetting = clsService.getMetaSearch();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId", c1220m01a_mainId);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		if (true) {
			pageSetting.addOrderBy("flag");
			pageSetting.addOrderBy("uploadTime");
		}
		Page<DocFile> page = docFileService.readToGrid(pageSetting);
		List<DocFile> list = (List<DocFile>) page.getContent();
		int resLength = list.size();
		if (resLength == 0) {
			throw new CapException("查無資料", getClass());
		}

		ByteArrayOutputStream rtn_baos = null;
		ZipOutputStream out = null;		

		try {

			if (resLength >= 1) {
				rtn_baos = new ByteArrayOutputStream();
				out = new ZipOutputStream(rtn_baos);
				out.setEncoding("BIG5"); // ISO8859_1 UTF-8
			}
			Map<String, String> map_flag_desc = ClsUtility.ploan_attch_DocFileFlagDesc();	
			for (int j = 0; j < resLength; j++) {
				DocFile docFile = list.get(j);
				if(docFile.getData()==null){
					docFile = docFileService.read(docFile.getOid());
				}
				
				//參考 LMSContractDocServiceImpl.java
				if (true) {
//					int c;
//					in1 = new ByteArrayInputStream(docFile.getData());					
//					bin1 = new BufferedReader(new InputStreamReader(in1, "ISO8859_1"));
//					out.putNextEntry(new ZipEntry("央行C方案授信合約書.doc"));
//					while ((c = bin1.read()) != -1) {
//						out.write(c);
//					}
//					bin1.close();										
				}
				
				String srcFileName = Util.trim(docFile.getSrcFileName());
				String docFileFlag = Util.trim(docFile.getFlag());
				String prefix = "";
				if(Util.isNotEmpty(docFileFlag)){
					prefix = LMSUtil.getDesc(map_flag_desc, docFileFlag)+"_";
				}
				out.putNextEntry(new ZipEntry(prefix+srcFileName));
				IOUtils.write(docFile.getData(), out);
			}
			
			return rtn_baos;

		} catch (Exception e) {
			LOGGER.error(StrUtils.getStackTrace(e));
		} finally {
			IOUtils.closeQuietly(out);
			IOUtils.closeQuietly(rtn_baos);
		}
		return rtn_baos;
	}

}
