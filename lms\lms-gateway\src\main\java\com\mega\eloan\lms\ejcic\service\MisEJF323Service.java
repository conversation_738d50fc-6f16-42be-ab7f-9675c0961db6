/* 
 * MisEJF366Service.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service;

import java.util.List;
import java.util.Map;


/**
 * <pre>
 * MIS.KRS001>>MIS.EJV32301>>MIS.EJF323信用卡是否持卡及強停
 * </pre>
 * 
 * @since 2012/03/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/03/23,TimChiang,new
 *          </ul>
 */
public interface MisEJF323Service {
	
	/**
	 * 查詢信用卡停用記錄(產品別=P2)
	 * @param id 客戶統一編號
	 * @return List
	 */
	List<Map<String, Object>> findCreditCardDisbRecord(String id);
}
