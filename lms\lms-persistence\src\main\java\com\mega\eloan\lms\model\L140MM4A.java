/* 
 * L140MM3A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 空地貸款維護主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140MM4A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L140MM4A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;
	
	/**
	 * 前案查詢日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "QUERYDATE", columnDefinition = "DATE")
	private Date queryDate;
	
	@Column(name = "CHKYN", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN;
	

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}
	
	/**
	 * 設定前案查詢日期
	 */
	public void setQueryDate(Date queryDate) {
		this.queryDate = queryDate;
	}

	/**
	 * 取得前案查詢日期
	 */
	public Date getQueryDate() {
		return queryDate;
	}

	public String getChkYN() {
		return this.chkYN;
	}
	
	public void setChkYN(String value) {
		this.chkYN = value;
	}
}
