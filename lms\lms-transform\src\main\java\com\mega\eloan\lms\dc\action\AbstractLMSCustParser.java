/* 
 * AbstractCustParser
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2013 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.lms.dc.action;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.w3c.dom.Document;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.ViewListConfig;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * AbstractCustParser
 * </pre>
 * 
 * @since 2013/2/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/2/25,UFO,new
 *          </ul>
 */
public abstract class AbstractLMSCustParser extends ParserAction {
	protected static final boolean DEBUG = false;

	protected String lmsLogsDirPath = "";// Lms logs檔根目錄路徑
	protected String lmsDxlDirRootPath = "";// Lms dxl檔根目錄路徑

	protected PrintWriter txtWrite;
	protected PrintWriter LMSParserlogs = null;// 輸出log
	protected PrintWriter ErrorLogs = null;// 輸出Err log
	protected String currentBranch = "";
	protected long tt1 = 0;

	protected int dxlTotal = 0;// 當前分行.dxl檔總數
	protected int parserTotal = 0;// 轉換出的總筆數
	protected int parserFail = 0;// 轉換失敗總筆數

	private final String DO_VIEW_NAME;
	private final String FORM_GROUP;
	private final String PROG_ID;
	private final String PROG_NAME = this.getClass().getSimpleName();

	/**
	 * 
	 * parse輸出檔= FormGroup + "_" + PROG_ID + ".txt"
	 * 
	 * @param pid
	 *            程式ID
	 * @param doViewName
	 *            指定View名稱
	 * @param formGroup
	 *            Form Group
	 */
	public AbstractLMSCustParser(String pid, String doViewName, String formGroup) {
		this.PROG_ID = pid;
		this.DO_VIEW_NAME = doViewName;
		this.FORM_GROUP = formGroup;
	}

	/**
	 * 初始化必要資訊
	 */
	public void doParser(String viewListName) {
		this.logger.info("正在初始化 " + PROG_NAME + " 必要資訊。");
		this.configData = this.getConfigData();
		this.userPath = this.configData.getUserPath();// User當前工作目錄
		this.lmsLogsDirPath = this.configData.getLmsLogsDirPath();// User當前工作目錄\log\logs\執行日期\LMS
		this.lmsDxlDirRootPath = this.configData.getLmsDxlDirRootPath();// homePath\today\LMS
		this.richTextColumn = this.configData.getRichTxtColName();

		// 讀取viewList
		List<String> viewList = ViewListConfig.getInstance().getViewList(
				viewListName);
		for (int i = 0, size = viewList.size(); i < size; i++) {
			try {
				this.tt1 = System.currentTimeMillis();

				this.parse(viewList.get(i));

				IOUtils.closeQuietly(this.txtWrite);

				long cost = System.currentTimeMillis() - this.tt1;
				String msg = StringUtils.join(new Object[] { "【",
						this.currentBranch,
						"】分行 執行 " + PROG_NAME + " 結束。結束時間 :",
						Util.getNowTime(), ", TOTAL TIME ==> ",
						Util.millis2minute(cost) });
				this.logger.info(msg);
				if (this.LMSParserlogs != null) {
					this.LMSParserlogs.println("\n【" + this.currentBranch
							+ "】分行總計" + this.dxlTotal + "筆dxl檔,轉換後產生【"
							+ this.parserTotal + "】筆資料，失敗筆數【" + this.parserFail
							+ "】筆。");
					this.LMSParserlogs.println(msg);

					IOUtils.closeQuietly(this.LMSParserlogs);
				}

				this.parserTotal = 0;
				this.parserFail = 0;
			} catch (Exception ex) {
				String errmsg = "執行[" + PROG_NAME
						+ "]之 parse步驟 時產生錯誤，中斷VIEW_NAME=[" + viewList.get(i)
						+ "]的執行作業！已讀DXL檔[" + dxlTotal + "]筆，已轉換["
						+ this.parserTotal + "]筆，失敗[" + this.parserFail
						+ "]筆。==>" + ex.getLocalizedMessage();
				logger.error(errmsg, ex);

				this.LMSParserlogs.println(errmsg);
				ex.printStackTrace(this.LMSParserlogs);
			}
		}
	}

	protected void parse(String nsfViewData) {
		String[] str = nsfViewData.split(";");// nsfViewData-->Ex:EL201\EL1LMSB1.NSF;VLMS14020;
		String strBrn = str[0].substring(2, 5);// 分行名稱,EX:201
		String viewName = str[1];// View Name ,EX:VLMSDB201B
		this.currentBranch = strBrn;

		//if (DO_VIEW_NAME.equalsIgnoreCase(viewName)) {

		if (viewName.startsWith(DO_VIEW_NAME)) {
			String dxlPath = this.getDxlPath(viewName, strBrn);

			try {
				// 建立logs
				String brnLogPath = this.lmsLogsDirPath + File.separator
						+ "PARSER" + File.separator + TextDefine.LOG_PARSER
						+ strBrn + "_" + PROG_ID ;
				this.LMSParserlogs = new PrintWriter(new BufferedWriter(
						new OutputStreamWriter(new FileOutputStream(new File(
								brnLogPath + ".log")))), true);

				this.ErrorLogs = new PrintWriter(new BufferedWriter(
						new OutputStreamWriter(new FileOutputStream(new File(
								brnLogPath + ".err")))), true);
				
				this.LMSParserlogs.println("【" + strBrn + "】分行" + PROG_NAME
						+ " 起始時間 :" + Util.getNowTime() + " VIEW=" + viewName);

				this.tt1 = System.currentTimeMillis();

				String FormGroup = this.FORM_GROUP;
				String outFile = FormGroup + "_" + PROG_ID + ".txt";
				logger.info("【" + strBrn + "】分行 " + PROG_NAME + "開始執行。 ==>"
						+ this.textPath + File.separator + outFile);
				this.txtWrite = new PrintWriter(new BufferedWriter(
						new OutputStreamWriter(new FileOutputStream(new File(
								this.textPath + File.separator + outFile)),
								TextDefine.ENCODING_UTF8)), true);
				// 取得.dxl列表
				String[] dxllist = Util.getSameAttachFile(dxlPath,
						TextDefine.ATTACH_DXL);
				Arrays.sort(dxllist);
				this.dxlTotal = dxllist.length;

				int idx = 0;
				String dxlXml = null;
				final int TOTAL_CNT = dxllist.length;
				// 讀取,處理及轉換.dxl
				long t1 = System.currentTimeMillis();
				for (String dxlName : dxllist) {
					try {
						// dxl轉成XML
						dxlXml = this.readFile(dxlPath + File.separator
								+ dxlName);
						Document domDoc = this.getDomDoc(dxlXml);
						// 讀取,處理及轉換
						this.transferDXL(dxlPath, dxlName, strBrn, domDoc,
								dxlXml);
					} catch (Exception ex) {
						// 失敗筆數加1
						this.parserFail++;
						String errmsg = ex.getLocalizedMessage();
						logger.error(errmsg, ex);

						this.LMSParserlogs.println(errmsg);
						//20130503 Sandra 增加log出在.err檔案中
						this.ErrorLogs.println(errmsg+";"+ex.getCause());
						ex.printStackTrace(this.LMSParserlogs);
					}

					if (logger.isDebugEnabled()) {
						if ((++idx) % DXL_PROC_DEBUG_CNT == 0) {
							logger.debug("##### [" + strBrn
									+ "] DXL轉換已處理筆數==> " + idx + " / "
									+ TOTAL_CNT + " , TOTAL_TIME = "
									+ (System.currentTimeMillis() - t1) + " ms");
							t1 = System.currentTimeMillis();
						}
					}
				}
			} catch (DCException e) {
				throw e;
			} catch (Exception e) {
				String errmsg = "【" + strBrn + "】分行執行" + PROG_NAME
						+ " 之 parse步驟 時產生錯誤。";
				throw new DCException(errmsg, e);
			}
		} else {
			this.logger.info("【" + strBrn + "】分行" + PROG_NAME + "開始執行。 VIEW=["
					+ DO_VIEW_NAME + "<>" + viewName + "] BYPASS!!!");
		}
	}

	protected String getDxlPath(String viewName, String strBrn) {
		// 建立轉出時必要目錄: Branch \ viewName \ HTML & IMAGES &FILES
		String dxlPath = this.lmsDxlDirRootPath + File.separator + strBrn
				+ File.separator + viewName;

		this.htmlPath = dxlPath + this.configData.getHtmlPath();
		Util.checkDirExist(this.htmlPath);
		this.imagesPath = dxlPath + this.configData.getImagesPath();
		Util.checkDirExist(this.imagesPath);
		this.filesPath = dxlPath + this.configData.getFilesPath();
		Util.checkDirExist(this.filesPath);
		this.textPath = dxlPath + this.configData.getTextPath();
		Util.checkDirExist(textPath);
		// 2013-03-26 Add by Bang
		this.loadDB2ClobPath = this.configData.getLmsloadDB2DirPath()// User當前工作目錄\load_db2\執行日期\LMS\clob
				+ this.configData.getClobPath() + File.separator + strBrn;
		Util.checkDirExist(this.loadDB2ClobPath);

		return dxlPath;
	}

	protected abstract void transferDXL(String dxlPath, String dxlName,
			String strBrn, Document domDoc, String dxlXml);

}
