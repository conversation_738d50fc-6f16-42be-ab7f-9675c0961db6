var dfd61 = new $.Deferred();
initAll.done(function(){

    /**  檔案上傳grid  */
    function gridfile(){
        $("#gridfile").iGrid({
            handler: "lms1605gridhandler",
            height: 120,
            autowidth: true,
            postData: {
                formAction: "queryfile",
                fieldId: "lmsCtrDoc",
                mainId: $("#tabFormMainId").val()
            },
            rowNum: 15,
            multiselect: true,
            colModel: [{
                colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
                name: 'srcFileName',
                width: 120,
                align: "left",
                sortable: true,
                formatter: 'click',
                onclick: download
            }, {
                colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
                name: 'fileDesc',
                width: 140,
                align: "center",
                sortable: true
            }, {
                colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
                name: 'uploadTime',
                width: 140,
                align: "center",
                sortable: true
            }, {
                name: 'oid',
                hidden: true
            }]
        });
    }
    
    dfd61.done(gridfile);
    
    $("#lms140Tab06").click(function(){
        dfd61.resolve();
        $("#gridfile").jqGrid("setGridParam", {
            postData: {
                formAction: "queryfile",
                mainId: $("#tabFormMainId").val()
            },
            search: true
        }).trigger("reloadGrid");
        
        var headItem1 = $("input[name=headItem1]:checked").val();
        if ("Y" == headItem1) {
        	$("#page08isGutCut").show();
        } else {
        	$("#page08isGutCut").hide();
        }
    });
    
    /**  上傳檔案按鈕*/
    $("#uploadFile").click(function(){
        var limitFileSize = 3145728;
        MegaApi.uploadDialog({
            fieldId: "lmsCtrDoc",
            fieldIdHtml: "size='30'",
            fileDescId: "fileDesc",
            fileDescHtml: "size='30' maxlength='30'",
            subTitle: i18n.def('insertfileSize', {
                'fileSize': (limitFileSize / 1048576).toFixed(2)
            }),
            limitSize: limitFileSize,
            width: 320,
            height: 190,
            data: {
                mainId: $("#tabFormMainId").val(),
                sysId: "LMS"
            },
            success: function(){
                $("#gridfile").trigger("reloadGrid");
            }
        });
    });
    
    /**  刪除檔案按鈕 */
    $("#deleteFile").click(function(){
        var select = $("#gridfile").getGridParam('selarrrow');
        if (select == "") {
        
            // TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var data = [];
                for (var i in select) {
                    data.push($("#gridfile").getRowData(select[i]).oid);
                }
                
                $.ajax({
                    handler: "lms1605m01formhandler",
                    data: {
                        formAction: "deleteUploadFile",
                        oids: data
                    },
                    success: function(obj){
                        $("#gridfile").trigger("reloadGrid");
                    }
                });
            }
        });
    });
    
    
    /**  檔案下載  */
    function download(cellvalue, options, data){
        $.capFileDownload({
            handler: "simplefiledwnhandler",
            data: {
                fileOid: data.oid
            }
        });
    }
    
	if($('#pageNum4').val() == '0'){
		$('#itemDscr4').attr('distanceWord','44');
	}else{
		$('#itemDscr4').attr('distanceWord','53');
	}

    // 第一次load畫面
	changeReadonly($("#formatType").val());

    var prev_val;
    $("#formatType").focus(function() {
        prev_val = $(this).val();
    }).change(function(e){
        var value = $(this).val();
        CommonAPI.confirmMessage(i18n.lms1401s02["changeFormatType"], function(b){
            if (b) {
                $.ajax({
                    type: "POST",
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteItemDscr4",
                        tabFormMainId: $("#tabFormMainId").val(),
                        formatType: value
                    },
                    success: function(responseData){
                        changeReadonly(value);
                        $("#itemDscr4").val("");
                    }
                });
            } else {
                $("#formatType").val(prev_val);
            }
        });
    });

    function changeReadonly(value){
        if(value == "1"){
            if(!inits.toreadOnly){
                $("#itemDscr4").prop("readonly", false);
            } else {
                $("#itemDscr4").prop("readonly", true);
            }
            $("#showL140S09").hide();
        } else if(value == "2"){
            $("#itemDscr4").prop("readonly", true);
            $("#showL140S09").show();
        }
    }

    gridBizCat();
    setItem();
    gridL140S09A();
    gridL140S09B();

    /**  新版其他敘做條件 **/
    $("#btnOpenL140S09A").click(function(){

        $("#bizCatThickbox").thickbox({
            title: "",//i18n.lms1401s02["btn.L140S09A"],
            width: 800,
            height: 300,
            align: "center",
            valign: "bottom",
            buttons: {
                "close": function(){
                    $.thickbox.close();
                    if (!inits.toreadOnly) {
                        $.ajax({
                            handler: inits.fhandle,
                            action: "previewL140s09",
                            data: {
                                tabFormMainId: $("#tabFormMainId").val()
                            },
                            success: function(obj){
                                $("#L140M01BForm").find("#itemDscr4").val(obj.drc);
                            }
                        });
                    }
                }
            }
        });

        $("#bizCatGrid").trigger("reloadGrid");
    });

    $("#btnImportL140S09Str").click(function(){
        $.ajax({
            handler: inits.fhandle,
            action: "previewL140s09",
            data: {
                tabFormMainId: $("#tabFormMainId").val()
            },
            success: function(obj){
                $("#L140M01BForm").find("#itemDscr4").val(obj.drc);
            }
        });
    });

    /**  新增新版其他敘做條件 **/
    $("#btnNewL140S09A").click(function(){
        $("#loanTPsName").val("");

        $("#newL140S09ABox").thickbox({
            title: "",
            width: 900,
            height: 200,
            align: "center",
            valign: "bottom",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var loanTPs = getSelectItem();
                    // J-110-0372 跨科目之通用條件 - 無須選擇科目
					var isCommon = $.trim($("#isCommon").val());
					if (isCommon != "Y") {
						if(loanTPs == "" || loanTPs == undefined){
	                        // L140M01a.error07=請選擇
	                        return CommonAPI.showErrorMessage(i18n.lms1401s02['L140M01a.error07']
	                                    + i18n.lms1401s02['L782M01A.loanTP']);
	                    }
					}                    
                    var bizCat = $("#bizCat :selected").val();
                    if(bizCat == "" || bizCat == undefined){
                        // L140M01a.error07=請選擇
                        return CommonAPI.showErrorMessage(i18n.lms1401s02['L140M01a.error07']
                                    + i18n.lms1401s02['L140S09A.bizCat']);
                    }

                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "chkL140s09a",
                            tabFormMainId: $("#tabFormMainId").val(),
                            loanTPs: loanTPs,
                            bizCat: bizCat
                        },
                        success: function(obj){
                            if (obj.msg && obj.msg != "") {
                                return API.showErrorMessage(obj.msg);
                            } else {
                                $.ajax({
                                    handler: inits.fhandle,
                                    data: {
                                        formAction: "addL140s09a",
                                        tabFormMainId: $("#tabFormMainId").val(),
                                        loanTPs: loanTPs,
                                        bizCat: bizCat,
                                        loanTPsName: $("#loanTPsName").val()
                                    },
                                    success: function(obj){
                                        $.thickbox.close();
                                        $("#bizCatGrid").trigger("reloadGrid");
                                    }
                                });
                            }
                        }
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });

        // 初始化
        $("#bizCat").val("");
        $("#itemSpan_loanTPs").remove();
    });

    $("#btnNewL140S09A_XX").click(function(){
        actionFunc("X");
    });

    $("#btnDelL140S09AByBizCat").click(function(){
        var row = $("#bizCatGrid").getGridParam('selrow');
        if (!row || row == "") {
            return CommonAPI.showErrorMessage(i18n.def["TMMDeleteError"]);
        }

        // L140S09A.delBizCat=此樣板之所有項目及細項將會一併刪除，是否確定執行？
        CommonAPI.confirmMessage(i18n.lms1401s02["L140S09A.delBizCat"], function(b){
            if (b) {
                var data = $("#bizCatGrid").getRowData(row);
                $.ajax({
                    type: "POST",
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteL140s09aBybizCat",
                        tabFormMainId: $("#tabFormMainId").val(),
                        bizCat: data.bizCat,
						bizCatId: data.bizCatId
                    },
                    success: function(responseData){
                        $("#bizCatGrid").trigger("reloadGrid");
                    }
                });
            }
        });
    });

    $("#btnDelL140S09A").click(function(){
        var row = $("#l140s09aGrid").getGridParam('selrow');
        var rows = $("#l140s09aGrid").getGridParam('selarrrow');
        var rowCount = $("#l140s09aGrid").getGridParam('records');

        if (rowCount < 2) {
            return CommonAPI.showErrorMessage(i18n.lms1401s02["L140S09A.atLeast"]);
        }
        if (rows == "") {
            return CommonAPI.showErrorMessage(i18n.def["TMMDeleteError"]);
        }
        var count = 0;
		for (var i = 0; i < rows.length; i++) {
			if (rows[i] != "") {
				count++;
			}
		}
		if (count > 1) {
			// L140M01a.message110=此功能只能選擇單筆
			return API.showMessage(i18n.lms1401s02["L140M01a.message110"]);
		}
        if (!row) {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }

        var bizItem = $("#l140s09aGrid").getRowData(row).bizItem;
        var str = "";
        var isXX = false;
        if(bizItem == "XX"){    // 其他
            str = i18n.def["confirmDelete"];
            isXX = true;
        } else {    // 此資料列為模板固定項目，不能刪除！
            return CommonAPI.showErrorMessage(i18n.lms1401s02["L140S09A.del"]);
        }

        // confirmDelete=是否確定刪除?
        // L140S09A.del=執行此功能會刪除此樣板之所有項目及細項，是否確定執行？
        CommonAPI.confirmMessage(str, function(b){
            if (b) {
                var data = $("#l140s09aGrid").getRowData(row);
                var oid = data.oid;
                $.ajax({
                    type: "POST",
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteL140s09a",
                        oid: oid,
                        isXX: isXX
                    },
                    success: function(responseData){
                        $("#l140s09aGrid").trigger("reloadGrid");
                    }
                });
            }
        });
    });

    $("#btnUpdIsPrint").click(function(){
        actionFunc("P");
    });

    $("#btnChgLoanName").click(function(){
        actionFunc("N");
    });
	
	$("#btnChgBizItemName").click(function(){
        actionFunc("XN");
    });

    $("#btnNewL140S09B").click(function(){
		$("#XXDiv").hide();
        $("#XXcont").val("");
        $.ajax({
            handler: inits.fhandle,
            action: "getSelectedCont",
            data: {
                mainId: $("#s09aOid").val()
            },
            success: function(obj){
                $("input[name='contList']").prop("disabled", false).prop("checked", false);
                for (var tst in obj.contList) {
                    if(obj.contList[tst] != "XX"){    //其他(自行輸入) 可以重複新增
                        $("input[name='contList'][value='" + obj.contList[tst] + "']").prop("disabled", true).prop("checked", true);
                    }
                }
            }
        }).done(function(){
            $("#newL140S09BBox").thickbox({
                title: "",
                modal: false,
                width: 900,
                height: 500,
                buttons: {
                    "sure": function(){
                        if(chkContList()){
                            // L140M01a.error07=請選擇
                            return CommonAPI.showErrorMessage(i18n.lms1401s02['L140M01a.error07']);
                        }
                        $.ajax({
                            handler: inits.fhandle,
                            action: "quickAddL140S09B",
                            data: $.extend($("#L140S09BForm").serializeData(), {
                                mainId: $("#s09aOid").val()
                            }),
                            success: function(obj){
                                $.thickbox.close();
                                $("#l140s09bGrid").jqGrid("setGridParam", {
                                    postData : {
                                        formAction: "queryL140s09b",
                                        mainId: $("#s09aOid").val()//s09aOid
                                    },
                                    search: true
                                }).trigger("reloadGrid");
								$("#l140s09aGrid").trigger("reloadGrid");
                            }
                        })
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        });
    });

    $("#btnDelL140S09B").click(function(){
        var row = $("#l140s09bGrid").getGridParam('selrow');

        if (!row) {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }

        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var data = $("#l140s09bGrid").getRowData(row);
                var oid = data.oid;
                $.ajax({
                    type: "POST",
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteL140s09b",
                        oid: oid
                    },
                    success: function(responseData){
                        $("#l140s09bGrid").jqGrid("setGridParam", {
                            postData : {
                                formAction: "queryL140s09b",
                                mainId: $("#s09aOid").val()//s09aOid
                            },
                            search: true
                        }).trigger("reloadGrid");
						$("#l140s09aGrid").trigger("reloadGrid");
                    }
                });
            }
        });
    });

    function gridBizCat(){
        $("#bizCatGrid").iGrid({
            handler: "lms1401gridhandler",
            rowNum: 15,
            height: 100,
            autowidth: true,
            postData: {
                formAction: "queryBizCatList",
                tabFormMainId: $("#tabFormMainId").val()
            },
            loadComplete: function(){
                $('#bizCatGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
                colHeader: i18n.lms1401s02["L140S09A.seq"],
                name: 'seq',
                width: 15,
                sortable : false,
                align: "center"
            },{
                colHeader: i18n.lms1401s02["L140S09A.bizCat"],
                name: 'bizCatStr',
                width: 100,
                sortable : false,
                align: "center"
            },{
                colHeader: i18n.lms1401s02["L782M01A.loanTP"],
                name: 'loanTPsStr',
                width: 200,
                align: "left",
                sortable : false,
                formatter: 'click',
                onclick: openS09aBox
            },{
                colHeader: i18n.lms1401s02["L140S09A.loanTPsName"],
                name: 'loanTPsName',
                width: 100,
                sortable : false,
                align: "center"
            },{
                name: 'bizCat',
                hidden: true
            },{
				name : 'bizCatId',
				hidden : true
			},{
                name: 'loanTPs',
                hidden: true
            }],
            ondblClickRow: function(rowid){
                var data = $("#bizCatGrid").getRowData(rowid);
                openS09aBox(null, null, data);
            }
        }).trigger("reloadGrid");
    }
    /**  新版其他敘做條件 L140S09A grid  */
    function gridL140S09A(){
        $("#l140s09aGrid").iGrid({
            handler: "lms1401gridhandler",
            rowNum: 15,
            height: 300,
            autowidth: true,
            multiselect: true,
            postData: {
                formAction: "queryL140s09a",
                tabFormMainId: $("#tabFormMainId").val()
            },
            loadComplete: function(){
                $('#l140s09aGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
                colHeader: i18n.lms1401s02["L140S09A.isPrint"],
                name: 'isPrint',
                width: 25,
                align: "center",
                sortable : false,
                formatter: function(value){
                    if(value == "Y"){
                        return "Y";
                    }else if(value == "N"){
                        return "";
                    }else{
                        return value;
                    }
                }
            },{
                colHeader: i18n.lms1401s02["L140S09A.bizItem"],
                name: 'bizItemStr',
                width: 100,
                sortable : false,
                align: "center",
                onclick: openS09bBox
            },{
                colHeader: i18n.lms1401s02["L140S09B.cont"],
                name: 'contStr',
                width: 500,
                sortable : false,
                align: "left"
            },{
                name: 'bizCat',
                hidden: true
            },{
				name : 'bizCatId',
				hidden : true
			},{
                name: 'bizItem',
                hidden: true
            },{
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){
                var data = $("#l140s09aGrid").getRowData(rowid);
                openS09bBox(null, null, data);
            }
        }).trigger("reloadGrid");
    }

    function openS09aBox(cellvalue, options, data){
        var title = data.loanTPsStr;
        if(data.loanTPsStr.length > 40){
            title = data.loanTPsStr.substring(0, 39);
        }
        $("#l140s09aThickbox").thickbox({
            title: title,
            width: 700,
            height: 450,
            modal: false   // 會有X關閉視窗
        });

        $("#l140s09aGrid").jqGrid("setGridParam", {
          postData: {
              formAction: "queryL140s09a",
              tabFormMainId: $("#tabFormMainId").val(),
              bizCat: data.bizCat,
              bizCatId: data.bizCatId
          },
          search: true
        }).trigger("reloadGrid");
    }

    function openS09bBox(cellvalue, options, data){
        var title = data.bizItemStr;
        if(data.bizItemStr.length > 50){
            title = data.bizItemStr.substring(0, 49);
        }
        $("#l140s09bThickbox").thickbox({
            title: title,
            width: 800,
            height: 350,
            modal: false,   // 會有X關閉視窗
            i18n: i18n.def
        });
        $("#s09aOid").val(data.oid);
        $("#l140s09bGrid").jqGrid("setGridParam", {
            postData : {
                formAction: "queryL140s09b",
                mainId: data.oid
            },
            search: true
        }).trigger("reloadGrid");

        var bizCat = data.bizCat;
        var bizItem = data.bizItem;
        var bizStr = bizCat.substring(0, 1)+bizItem;
        setCont(bizStr);
    }

    /**  新版其他敘做條件 L140S09B grid  */
    function gridL140S09B(){
        $("#l140s09bGrid").iGrid({
            handler: "lms1401gridhandler",
            height: 200,
            autowidth: true,
            postData: {
                formAction: "queryL140s09b",
                mainId: $("#s09aOid").val()//s09aOid
            },
            loadComplete: function(){
                $('#l140s09bGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
                colHeader: i18n.lms1401s02["L140S09B.cont"],
                name: 'cont',
                width: 550,
                sortable : false,
                align: "left"
            },{
            	colHeader: i18n.lms1401s02["L140S09B.isPostLoan"],
                name: 'isPostLoan',
                width: 100,
                sortable : false,
                align: "center",
                formatter: function(value){
                	if(value == "Y"){
                		return "Y";
                	}else{
                		return "";
                	}
                }
            },{
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){
                var data = $("#l140s09bGrid").getRowData(rowid);
                openS09bDetailBox(null, null, data);
            }
        }).trigger("reloadGrid");
    }

    function openS09bDetailBox(cellvalue, options, data){
        var divId = "";
        $("#L140S09BDetailForm").find("span").hide();
        $("#L140S09BDetailForm").find("input").removeAttr("checked").not(":checkbox").val("");

        $.ajax({
            handler: inits.fhandle,
            action: "queryL140s09aDetail",
            data: {
                oid: data.oid
            },
            success: function(obj){
                $('#L140S09BDetailForm').injectData(obj);

                if(obj.divId == ""){
                    $("#contDiv").show();
                } else {
                    $("#" + DOMPurify.sanitize(obj.divId) + "_Div").show();
                }
                divId = obj.divId;
            }
        }).done(function(){
            var buttons = {
                "saveData": function(){
//                    if (!$("#L140S09BDetailForm").valid()) {
//                        return false;
//                    };
                    $.ajax({
                        handler: inits.fhandle,
                        action: "saveL140S09B",
                        data: {
                            detailForm: JSON.stringify($("#L140S09BDetailForm").serializeData()),
                            isPostLoan: $("#L140S09BDetailForm").find("#isPostLoan:checkbox").prop("checked"),
                            oid: data.oid,
                            divId: divId
                        },
                        success: function(obj){
                            $("#l140s09bGrid").jqGrid("setGridParam", {
                                postData : {
                                    formAction: "queryL140s09b",
                                    mainId: $("#s09aOid").val()
                                },
                                search: true
                            }).trigger("reloadGrid");
                            $("#l140s09aGrid").trigger("reloadGrid");

                            //saveSuccess=儲存成功
                            CommonAPI.confirmMessage(i18n.def["saveSuccess"], function(b){
                                if (b) {
                                    $.thickbox.close();
                                }
                            });
                        }
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }

            if (inits.toreadOnly) {
                delete buttons.saveData;
            }

            $("#s09bDetailBox").thickbox({
                title: "",
                width: 700,
                height: 200,
                modal: false,   // 會有X關閉視窗
                i18n: i18n.def,
                align: "center",
                valign: "bottom",
                buttons: buttons
            });
        });
    }

    function setItem(){
        var items = CommonAPI.loadCombos(["bizCat"]);
        $("#bizCat").setItems({
            item: items.bizCat,
            format: "{key}"
        });

        itemChange();
    }

    function setCont(bizStr){
        var str = "cont"+bizStr;
        var item = CommonAPI.loadOrderCombosAsList(str)[str];
        var hasXX = false;
        $.each(item, function(key, value){
            if(value["value"] == "XX"){
                hasXX = true;
            }
        });
        var a = {"value": "XX" , "desc" : i18n.lms1401s02["L140S09A.bizItem_XX"]};
        if(!hasXX){
            item.push(a);
        }

        $("#contList").setItems({
            size: "1",
            item: item,
            clear: true,
            itemType: 'checkbox'
        });
		
		$("input[name='contList']").click(function(){
            // 勾選 其他  下方出現 textarea
            var chkValue = $(this).val();
            if(chkValue == "XX"){
                if (this.checked) {
                    $("#XXDiv").show();
                } else {
                    $("#XXDiv").hide();
                    $("#XXcont").val("");
                }
            }
        });
    }

    // 要寫在 setItems 後  才會生效
    function itemChange(){
        $("#bizCat").change(function(){
            var value = $(this).val();

            //加入前面登錄的授信科目
            $.ajax({
                handler: inits.fhandle,
                data: {
                    formAction: "getLoanTPsByBizCat",
                    tabFormMainId: $("#tabFormMainId").val(),
                    bizCat: value
                },
                success: function(obj){
                    //刪除原本的項目
                    $("#itemSpan_loanTPs").remove();
                    $("#loanTPsSpan").html("<input type='checkbox' id='loanTPs' name='loanTPs' value='' />");
                    $("#itemShowTr").html("");
                    var loanTPs_obj = $("input[name=loanTPs]");
                    if (obj.count != "0") {
                    	loanTPs_obj.setItems({//利費率第一頁籤授信科目checkbok
                            item: obj.item
                        });
                    } else {
                        $("#loanTPsSpan").html("");
                    }
                    $("#isCommon").val(DOMPurify.sanitize(obj.isCommon)); // J-110-0372 跨科目之通用條件
                }
            });
        });
    }

    function actionFunc(type){
        //type      P-ISPRINT  N-LOANTPSNAME    X-bizItem_XX	XN-bizItem_XX change Name
        var $actionGrid = null;
        if(type == "N") {
            $actionGrid = $("#bizCatGrid");
        } else {
            $actionGrid = $("#l140s09aGrid");
        }

        var rows = null;
        if(type == "N") {
            rows = $actionGrid.getGridParam('selrow');
        } else {
            rows = $actionGrid.getGridParam('selarrrow');
        }

        if(type == "X"){
            // 可以不用選資料
        } else if (rows == "" || rows == null) {//action_005=請先選取一筆以上之資料列
            if(type == "N" || type == "X" || type == "XN"){    // 單筆
                return CommonAPI.showErrorMessage(i18n.def['grid.selrow']);
            } else {            // 多筆
                return CommonAPI.showErrorMessage(i18n.def['action_005']);
            }
        }

        if(type == "N" || type == "X" || type == "XN"){
        	var count = 0;
			for (var i = 0; i < rows.length; i++) {
				if (rows[i] != "") {
					count++;
				}
			}
        	// rows.length 如果第10筆以上會超過1
            // if (rows.length > 1) {
        	if (count > 1) {
                //L140M01a.message110=此功能只能選擇單筆
                return API.showMessage(i18n.lms1401s02["L140M01a.message110"]);
            }
        }

        var data = [];
        var typeN_bizCat = "";
        var typeN_bizCatId = "";
        var typeN_bizCatStr = "";
		var typeX_bizItem = "";
        if(type == "N"){
            typeN_bizCat = $actionGrid.getRowData(rows).bizCat;
            typeN_bizCatId = $actionGrid.getRowData(rows).bizCatId;
            typeN_bizCatStr = $actionGrid.getRowData(rows).bizCatStr;
        } else if(type == "X"){
            // 預設第一筆
            data.push($actionGrid.getRowData(1).oid);
            typeN_bizCat = $actionGrid.getRowData(1).bizCat;
            typeN_bizCatId = $actionGrid.getRowData(1).bizCatId;
			typeX_bizItem = $actionGrid.getRowData(1).bizItem;
        } else if(type == "XN"){
            data.push($actionGrid.getRowData(rows).oid);
            typeX_bizItem = $actionGrid.getRowData(rows).bizItem;
        } else {
            for (var i in rows) {
                data.push($actionGrid.getRowData(rows[i]).oid);
            }
        }
		
		if(type == "XN" && typeX_bizItem != "XX"){
            // L140S09A.chgBizItemNameMsg=非自訂項目，不能更改！
            return CommonAPI.showErrorMessage(i18n.lms1401s02['L140S09A.chgBizItemNameMsg']);
        }

        // 設定showTr
        $("#actionBox").find("tr").hide();
        if(type == "P"){
            $(".isPrint_Tr").show();
        } else if(type == "N"){
            $(".chgLoanName_Tr").show();
            $("#chgLoanName").val("");
            $("#typeN_bizCat").val(typeN_bizCat);
            $("#typeN_bizCatStr").val(typeN_bizCatStr);
        } else if(type == "X" || type == "XN"){
            $(".bizItem_XX_Tr").show();
            $("#itemStr").val("");
        }

        var titleStr = "";
        if(type == "P"){
            titleStr = i18n.lms1401s02["btn.updIsPrint"];
        } else if(type == "N"){
            titleStr = i18n.lms1401s02["btn.chgLoanName"];
        } else if(type == "X"){
            titleStr = i18n.lms1401s02["btn.addL140S09A_ItemXX"];
        } else if(type == "XN"){
            titleStr = i18n.lms1401s02["btn.chgBizItemName"];
        }

        $("#actionBox").thickbox({
           title: titleStr,
           width: 300,
           height: 150,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
                "sure": function(){
                    var checkedVal = "";
                    if(type == "P"){
                        checkedVal = $("[name=isPrintRadio]:checked").val();
                        if (!checkedVal || checkedVal == "") {
                            return CommonAPI.showErrorMessage(i18n.lms1401s02["L140M01a.error07"]+i18n.lms1401s02["L140S09A.isPrint"]);
                        }
                    } else if(type == "N"){

                    } else if(type == "X" || type == "XN"){
                        if($.trim($("#itemStr").val()) == ""){
                            // L140M01a.error18=請輸入
                            return CommonAPI.showErrorMessage(i18n.lms1401s02['L140M01a.error18']
                                        + i18n.lms1401s02['L140S09A.bizItem']);
                        }
                    }

                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "updL140S09A",
                            tabFormMainId: $("#tabFormMainId").val(),
                            oids: data,
                            type: type,
                            isPrint: checkedVal,
                            chgLoanName: $("#chgLoanName").val(),
                            bizCat: typeN_bizCat,
                            bizCatId : typeN_bizCatId,
                            itemStr: $("#itemStr").val()
                        },
                        success: function(obj){
                            $actionGrid.trigger("reloadGrid");
                            $.thickbox.close();
							if(type == "X"){
                                openS09bBox(null, null, obj.resultData);
                            }
                        }
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }

    /**
     * 取得選擇的授信科目
     * return XXX|XXX
     */
    function getSelectItem(){
        var data = [];
        $("#itemSpan_loanTPs").find("[name=loanTPs]:checked").each(function(v, k){
            data.push($(k).val());
        });
        return data.join("|");
    }

    function chkContList(){
        var cnt=0;
        $("#itemSpan_contList").find("[name=contList]:checked").each(function(v, k){
            cnt++;
        });
        if(cnt>0){
            return false;
        } else {
            return true;
        }
    }
    //J-113-0035  ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意承諾待追蹤ESG連結條款」的登錄機制
    var l140s12aGrid = $("#l140s12aGrid").iGrid({
		needPager : false,
		localFirst : true,
		handler : inits.ghandle,//queryAdcList
		height : "340",
		width : "100%",
		multiselect : true,
		sortname : 'seqNum',
		sortorder : 'asc|asc',
		postData: {
        	formAction: "queryL140s11aList",
            tabFormMainId: $("#tabFormMainId").val()
        },
		colModel : [ {
			colHeader : i18n.lms1401s02["L140S12A.seqNo"],//序號
			name : 'seqNum',
			width : '50px',
			sortable : true,
			align : "center"
			//onclick : branchBox
		},{
			colHeader : i18n.lms1401s02["L140S12A.type"],//類別
			name : 'esgType',
			width : '220px',
			sortable : true,
			align : "left",
		}, {
			colHeader : i18n.lms1401s02["L140S12A.contentText"],//內容
			name : 'contentText',
			width : '400px',
			sortable : true,
			align : "left"
		}, {
			colHeader : i18n.lms1401s02["L140S12A.esgType"],//ESG性質
			name : 'esgModel',
			width : '80px',
			sortable : true,
			align : "center"
		},{
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }],
		ondblClickRow : function(rowid) {
			var rowdata = $("#l140s12aGrid").getRowData(rowid);
			esgTraceAciton.openBox(null, null, rowdata);

		}
	});

	
	 esgTraceAciton.initUI();
	
});
function previewL140S09(){
    $.ajax({
        handler: inits.fhandle,
        action: "previewL140s09",
        data: {
            tabFormMainId: $("#tabFormMainId").val()
        },
        success: function(obj){
            $("#previewSpan").html(DOMPurify.sanitize(obj.drc));
        }
    }).done(function(){
        $("#previewBox").thickbox({
            title: "",
            align: "center",
            valign: "bottom",
            width: 800,
            height: 600,
            buttons: {
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    });
}
if (!window.Panel06Action) {
    window.Panel06Action = {
        upDown: function(upDown, gridId){
            var chkMuti = $("#"+gridId).getGridParam('selarrrow');
            var count = 0;
			for ( var i = 0; i < chkMuti.length; i++) {
				if (chkMuti[i] != "") {
					count++;
				}
			}
			
			if (count > 1) {
            // if (chkMuti.length > 1) {
                //L140M01a.message110=此功能只能選擇單筆
                return API.showMessage(i18n.lms1401s02["L140M01a.message110"]);
            }

            var row = $("#"+gridId).getGridParam('selrow');
            if (!row || row == "") {
                return CommonAPI.showMessage(i18n.def["grid.selrow"])
            }

            var data;
            if (row != 'undefined' && row != null) {
                data = $("#"+gridId).getRowData(row);
            }

            Panel06Action.upOrDown(data, upDown, gridId);
        },
        upOrDown: function(data, upDown, gridId){
            var key = "";
            var key2 = "";
            if(gridId == "bizCatGrid"){
                key = data.bizCat;
                key2 = data.bizCatId;
            } else {
                key = data.oid;
            }
            $.ajax({
                type: "POST",
                handler: inits.fhandle,
                data: {
                    formAction: "changeSeqNum",
                    key: key,
                    key2: key2,
                    upOrDown: upDown,
                    gridId: gridId,
                    tabFormMainId: $("#tabFormMainId").val()
                },
                success: function(obj){
                    if(gridId == "bizCatGrid"){
                        $('#bizCatGrid').jqGrid('setGridParam', {
                            gridComplete: function(){
                                $("#bizCatGrid").setSelection(DOMPurify.sanitize(obj.newSelect));
                                $('#bizCatGrid').jqGrid('setGridParam', {
                                    gridComplete: function(){
                                        //執行完後把loadComplete清空，要不然GRID 的REFRESH也會觸發上面的setSelection
                                    }
                                });
                            }
                        }).trigger("reloadGrid");
                    } else if(gridId == "l140s09aGrid"){
                        $('#l140s09aGrid').jqGrid('setGridParam', {
                            gridComplete: function(){
                                $("#l140s09aGrid").setSelection(DOMPurify.sanitize(obj.newSelect));
                                $('#l140s09aGrid').jqGrid('setGridParam', {
                                    gridComplete: function(){
                                        //執行完後把loadComplete清空，要不然GRID 的REFRESH也會觸發上面的setSelection
                                    }
                                });
                            }
                        }).trigger("reloadGrid");
                    } else if(gridId == "l140s09bGrid"){
                        $("#l140s09bGrid").jqGrid("setGridParam", {
                            postData : {
                                formAction: "queryL140s09b",
                                mainId: $("#s09aOid").val()//s09aOid
                            },
                            gridComplete: function(){
                                $("#l140s09bGrid").setSelection(DOMPurify.sanitize(obj.newSelect));
                                $('#l140s09bGrid').jqGrid('setGridParam', {
                                    gridComplete: function(){
                                        //執行完後把loadComplete清空，要不然GRID 的REFRESH也會觸發上面的setSelection
                                    }
                                });
                            }
                        }).trigger("reloadGrid");
						$("#l140s09aGrid").trigger("reloadGrid");
                    }
                }
            });
        }
    };
}

var esgTraceAciton = {
		initUI: function(){
			if (inits.toreadOnly) {
                $("#btnESGNew").hide();
                $("#btnESGMod").hide();
                $("#btnESGDel").hide();
            }
		    // 區分新舊動撥提醒事項
		    $.ajax({
		    	handler: inits.fhandle,
		        action: "checkOldtoALoan",
		        data: {
		        	oid: responseJSON.oid //L140M01A.oid
		        },
		        success: function(obj){//有資料
		        	var isnew = false;
		        	if(!$.isEmptyObject(obj)){
		        		isnew = obj.isnew;
		        	}
		        	if(isnew){
		        		$("tr.J-113-0035_check").hide();
		        	}
		        }
		    })
		    //esgType
		    var esgType = API.loadOrderCombosAsList("l140s12a_esgType")["l140s12a_esgType"];
		    $("#esgType").setItems({
				 size: "1",
			     item: convertItems(esgType),
				 clear : true,
				 itemType: 'checkbox' 
			});
		    //ESG模板
		    var esgMsgEitem = API.loadCombos("l140s12a_esgModelE")["l140s12a_esgModelE"];
			$("#esgMsgE").setItems({
				 size: "1",
			     item: esgMsgEitem,
				 clear : true,
				 itemType: 'checkbox' 
			});
			var esgMsgSitem = API.loadCombos("l140s12a_esgModelS")["l140s12a_esgModelS"];
			$("#esgMsgS").setItems({
				 size: "1",
			     item: esgMsgSitem,
				 clear : true,
				 itemType: 'checkbox' 
			});
			var esgMsgGitem = API.loadCombos("l140s12a_esgModelG")["l140s12a_esgModelG"];
			$("#esgMsgG").setItems({
				 size: "1",
			     item: esgMsgGitem,
				 clear : true,
				 itemType: 'checkbox' 
			});
			//載入完成後，區分esgType type2, type3 選項
			var model12 = API.loadOrderCombosAsList("l140s12a_esgModelType12")["l140s12a_esgModelType12"];//永續績效
			var model13 = API.loadOrderCombosAsList("l140s12a_esgModelType13")["l140s12a_esgModelType13"];//其他
			if(!$.isEmptyObject(model12)){
				var listvalue = DOMPurify.sanitize(model12[0].value).split('|');
				$("input[type='checkbox'].checkbox_esgMsgAll").filter(function(){
				    return listvalue.indexOf($(this).val()) > -1;
				}).parent().addClass("label_checkbox_esgMsg_type2");
			}
			if(!$.isEmptyObject(model13)){
				var listvalue = DOMPurify.sanitize(model13[0].value).split('|');
				$("input[type='checkbox'].checkbox_esgMsgAll").filter(function(){
				    return listvalue.indexOf($(this).val()) > -1;
				}).parent().addClass("label_checkbox_esgMsg_type3");
			}
			
			$("input[name='esgType']").change(function(){
				var esgType2 = $("input[name='esgType'][value='12']:checked").length;
				var esgType3 = $("input[name='esgType'][value='13']:checked").length;
				if(esgType2 > 0 || esgType3 > 0){
					//永續績效連結授信條件(跟利率計價或手續費減免等優惠措施有關)、其他ESG條件(非以上相關)
					var esgGreenSpendType = $("#esgSustainLoanType").val();
					if (esgGreenSpendType != "") {//"E|S|G"
						//永續績效連結授信條件(跟利率計價或手續費減免等優惠措施有關)
						if(esgType2 == 0){
							$(".label_checkbox_esgMsg_type2").hide();
							$(".label_checkbox_esgMsg_type2").find("input[type='checkbox']").prop("checked", false);
						}else{
							$(".label_checkbox_esgMsg_type2").show();
						}
						//其他ESG條件(非以上相關)
						if(esgType3 == 0){
							$(".label_checkbox_esgMsg_type3").hide();
							$(".label_checkbox_esgMsg_type3").find("input[type='checkbox']").prop("checked", false);
						}else{
							$(".label_checkbox_esgMsg_type3").show();
						}
						if(esgGreenSpendType.indexOf("E") >=0){
							$("#td_esgMsgE").show();
						}else{
							$("#td_esgMsgE").hide();
						}
						if(esgGreenSpendType.indexOf("S") >=0){
							$("#td_esgMsgS").show();
						}else{
							$("#td_esgMsgS").hide();
						}
						if(esgGreenSpendType.indexOf("G") >=0){
							$("#td_esgMsgG").show();
						}else{
							$("#td_esgMsgG").hide();
						}
						$(".tr_esgMsg_type").show();
						$(".tr_esgMsgImporBtn").show();
					}
					//起始追蹤日 只能選其他
					$("input[type='radio'][name='traceCondition'][value='3']").prop("checked", true);
					$("input[type='radio'][name='traceCondition'][value!='3']").prop("disabled", true);
					$("input[type='radio'][name='traceCondition']").trigger("change");
				}else{//隱藏ESG模板,取消勾選
					$(".tr_esgMsg_type").hide();
					$(".tr_esgMsgImporBtn").hide();
					$(".checkbox_esgMsgAll").prop("checked", false);
					$("input[type='radio'][name='traceCondition'][value!='3']").prop("disabled", false);
					$("input[type='radio'][name='traceCondition']").trigger("change");
				}
			});
			
			$("input[type='radio'][name='traceCondition']").change(function(){
				if($("input[type='radio'][name='traceCondition'][value='3']:checked").length > 0){
					$(".td_traceProfiling").show();
				}else{
					//隱藏追蹤週期,取消勾選
					$(".td_traceProfiling").hide();
					$("input[type='radio'][name='traceProfiling']").prop("checked", false);
					$("#traceProfilingMonth").val("");
				}
			});
			
			$("#btn_ESG").click(function() {
				//明細初始化
				$("#divESG").thickbox({
		            title: i18n.lms1401s02["L140S12A.memoEsgTerms"],//登錄應注意/承諾/待追蹤/ESG連結條款
		            align: "center",
		            valign: "bottom",
		            width: 800,
		            height: 510,
		            buttons: {
		                "close": function(){
		                    $.thickbox.close();
		                }
		            }
		        });
				//gridreload
				$("#l140s12aGrid").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
		            postData : {
		                formAction: "queryL140s12aList",
		                tabFormMainId: $("#tabFormMainId").val()
		            },
		            search : true
		        }).trigger("reloadGrid");
			});
			
			$("#btmImportEsgMsg").click(function() {
				//esg模板勾選項目引入內文
				if($(".checkbox_esgMsgAll:checked").length > 0){
					$(".checkbox_esgMsgAll:checked").each(function(){
					    $("#contentText").val(DOMPurify.sanitize($("#contentText").val()) + "\n" + DOMPurify.sanitize($(this).parent().text().trim()));
					});
					 $("#contentText").val(DOMPurify.sanitize($("#contentText").val()) + "\n" + DOMPurify.sanitize(i18n.lms1401s02["L140S12A.esgModelMemo_1"]));
					 $("#contentText").val(DOMPurify.sanitize($("#contentText").val()) + "\n" + DOMPurify.sanitize(i18n.lms1401s02["L140S12A.esgModelMemo_2"]));
				}

			});
			// 
			$("#btnESGNew").click(function() {
				esgTraceAciton.openBox(null, "add", null);
			});
			$("#btnESGMod").click(function() {
				var rowid = $("#l140s12aGrid").getGridParam('selrow');
				var rowdata = $("#l140s12aGrid").getRowData(rowid);
				esgTraceAciton.openBox(null, null, rowdata);
			});
			$("#btnESGDel").click(function() {
				esgTraceAciton.deleteL140S12A();
			});
		},
		/**
		 *  明細視窗
		 */
		openBox: function(cellvalue, options, l140s12a_rowdata){
			//console.log(l140s12a_rowdata);
			// 當新增時 data 會是 null
	        var hasData = false;
	        if (typeof l140s12a_rowdata != 'undefined' && l140s12a_rowdata != null && l140s12a_rowdata != 0) {
	            hasData = true;
	        }
		    var l140s12aOid = hasData ? l140s12a_rowdata.oid : "";
		    //console.log(l140s12aOid);
	        $.ajax({
		    	handler: inits.fhandle,
		        action: "queryL140s12aDetail",
		        data: {
		        	l140s12aOid: l140s12aOid
		        },
		        success: function(obj){//有資料帶入
		        	if(!$.isEmptyObject(obj)){//checkbox  1|2|3|9
		        		$("#l140s12aFormDetail").injectData(obj);
		        		if(!$.isEmptyObject(obj.esgType)){
		        			var esgTypeArr = obj.esgType.split("|");
		        			$.each(esgTypeArr, function(i,v){
		        			    $("#l140s12aFormDetail").find("input[name='esgType'][value='" + DOMPurify.sanitize(v) + "']").prop("checked", true);
		        			});
		        		}
		        		if(!$.isEmptyObject(obj.esgType)){//checkbox  S1|S2|E1|G1
		        			var esgModelArr = obj.esgModel.split("|");
		        			$.each(esgModelArr, function(i,v){
		        			    $("#l140s12aFormDetail").find("input.checkbox_esgMsgAll[value='" + DOMPurify.sanitize(v) + "']").prop("checked", true);
		        			});
		        		}
		        		if(!$.isEmptyObject(obj.traceCondition)){//radio
		        			$("#l140s12aFormDetail").find("input[name='traceCondition'][value='" + DOMPurify.sanitize(obj.traceCondition) + "']").prop("checked", true);
		        		}
		        		if(!$.isEmptyObject(obj.traceProfiling)){//radio
		        			$("#l140s12aFormDetail").find("input[name='traceProfiling'][value='" + DOMPurify.sanitize(obj.traceProfiling) + "']").prop("checked", true);
		        		}
		        	}else{
		        		$("input[type='checkbox'][name='esgType']").prop("checked", false);
		        		$("input[type='radio'][name='traceCondition']").prop("checked", false);
		        		$("input[type='radio'][name='traceProfiling']").prop("checked", false);
		        		$("input.checkbox_esgMsgAll").prop("checked", false);
		        		$("#traceProfilingMonth").val("");
		        		$("#contentText").val("");
		        	}
		         	
		        }
		    }).done(function(){
				$("input[type='checkbox'][name='esgType']").trigger("change");
				$("input[type='radio'][name='traceCondition']").trigger("change");
				
				var buttonsF = {
	            	"sure": function(){
	            		esgTraceAciton.saveL140S12A(l140s12aOid);
	                 },
	                "cancel": function(){
	                    $.thickbox.close();
	                }
	            };
	            if (inits.toreadOnly) {
	                delete buttonsF.sure;
	                $("#l140s12aFormDetail").find("textarea").prop("readonly", true);
	            }
	            
				$("#divESGDetail").thickbox({
					title: i18n.lms1401s02["L140S12A.memoEsgTermsDetail"],//應注意/承諾/待追蹤/ESG連結條款明細
		            align: "center",
		            valign: "bottom",
		            width: 800,
		            height: 800,
		            buttons: buttonsF
				});
				
				
		    });
		},
		checkL140S12Adata: function(){
			var errmsg = "";
			if($("input[type='radio'][name='traceCondition']:checked").length == 0){
				errmsg += i18n.lms1401s02["L140S12A.errorMsg01"];
				//return CommonAPI.showMessage(i18n.lms1401s02["L140S12A.errorMsg01"]);
			}
			
			if($("input[type='radio'][name='traceCondition'][value='3']:checked").length > 0){
				if($("input[type='radio'][name='traceProfiling']:checked").length == 0){
					errmsg += i18n.lms1401s02["L140S12A.errorMsg02"];
				}
				if($("input[type='radio'][name='traceProfiling'][value='2']:checked").length > 0 && $("#traceProfilingMonth").val() == ""){
					errmsg += i18n.lms1401s02["L140S12A.errorMsg03"];
				}
			}
			return errmsg;
		},
		/**儲存L140S12A
	     *
	     */
		saveL140S12A: function(l140s12aOid){
			//儲存L140S12A
			var errorMsg = esgTraceAciton.checkL140S12Adata();
			if(errorMsg != ""){
				return CommonAPI.showMessage(errorMsg);
			}else{
				 // confirm=是否寫入主文
                CommonAPI.confirmMessage(i18n.lms1401s02["L140S12A.isImportContext"], function(b){//是否將追蹤內容寫入主文
                    if (b) {
                    	//將內容寫入最外層的"其他敘做條件textarea"
                    	var content = $("#contentText").val().replace(/#|\n/g, "<BR>");
                    	$('#itemDscr4').val(DOMPurify.sanitize($('#itemDscr4').val() + "<p>" + content + "</p>"));
                    }else{
                    	
                    }
                  //儲存L140S12A
                    $.ajax({
    	                handler: inits.fhandle,
    	                data: {
    	                    formAction: "saveL140S12A",
    	                    oid: l140s12aOid,
    	                    caseMainId: responseJSON.mainId,
    	                    mainId: $("#tabFormMainId").val(),
    	                    cntrNo: $("#cntrNo").val(),
    	                    esgType: esgTraceAciton.formatEsgCheckBoxData("esgType"),
    	                    esgMsgE: esgTraceAciton.formatEsgCheckBoxData("esgMsgE"),
    	                    esgMsgS: esgTraceAciton.formatEsgCheckBoxData("esgMsgS"),
    	                    esgMsgG: esgTraceAciton.formatEsgCheckBoxData("esgMsgG"),
    	                    traceCondition: esgTraceAciton.formatEsgCheckBoxData("traceCondition"),
    	                    traceProfiling: esgTraceAciton.formatEsgCheckBoxData("traceProfiling"),
    	                    traceProfilingMonth: $("#traceProfilingMonth").val(),
    	                    contentText: $("#contentText").val()
    	                    //l140s12aForm: JSON.stringify($("#l140s12aFormDetail").serializeData()),//備用
    	                },
    	                success: function(obj){
    	                    //gridreload
    	                    $("#l140s12aGrid").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
    	                        postData : {
    	                            formAction: "queryL140s12aList",
    	                            tabFormMainId: $("#tabFormMainId").val()
    	                        },
    	                        search : true
    	                    }).trigger("reloadGrid");
    	                    
    	                    $.thickbox.close();//關閉
    	                }
    	            });
                });
				
			}
       	 	
		},
		/**
		 * 修改L140S12A
		 */
		modifyL140S12A: function(){
			var row = $("#l140s12aGrid").getGridParam('selrow');
			if (!row) {
		    	return CommonAPI.showMessage(i18n.def["action_002"]);
		    }
			var data = $("#l140s12aGrid").getRowData(row);
			esgTraceAciton.openBox(null, null, rowdata);
		},
		/**
		 * 刪除L140S12A
		 */
		deleteL140S12A: function(){
	        //var row = $("#l140s12aGrid").getGridParam('selrow');
	        var rows = $("#l140s12aGrid").getGridParam('selarrrow');
	    	var data = [];
	        if (!rows) {
	            return CommonAPI.showMessage(i18n.def["action_002"]);
	        }
	        else {
	            // confirmDelete=是否確定刪除?
	            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
	                if (b) {
	                    //var data = $("#l140s12aGrid").getRowData(row);
	                    for (var i in rows) {
	                        data.push($("#l140s12aGrid").getRowData(rows[i]).oid);
	                    }
	                    //var oid = data.oid;
	                    $.ajax({
	                        type: "POST",
	                        handler: inits.fhandle,
	                        data: {
	                            formAction: "deleteL140s12a",
	                            oids: data,
	                            tabFormMainId: $("#tabFormMainId").val()
	                        },
	                        success: function(responseData){
	                        	$("#l140s12aGrid").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
	                                postData : {
	                                    formAction: "queryL140s12aList",
	                                    tabFormMainId: $("#tabFormMainId").val()
	                                },
	                                search : true
	                            }).trigger("reloadGrid");
	                        }
	                    });
	                }
	            });
	        }
		},
		/**
	     * 取得esgcheckbox值
	     * return XXX|XXX
	     */
	    formatEsgCheckBoxData: function(objName){
	        var data = [];
	        $("#l140s12aFormDetail").find("input[name='"+ DOMPurify.sanitize(objName) +"']:checked").each(function(v, k){
	            data.push($(k).val());
	        });
	        return data.join("|");
	    }
}