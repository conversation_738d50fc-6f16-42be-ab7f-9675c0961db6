/* 
 * L120S04D.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 各項往來資料檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S04D", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","keyCustId","keyDupNo"}))
public class L120S04D extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 分項統一編號 **/
	@Size(max=10)
	@Column(name="KEYCUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String keyCustId;

	/** 分項重覆序號 **/
	@Size(max=1)
	@Column(name="KEYDUPNO", length=1, columnDefinition="CHAR(1)")
	private String keyDupNo;

//	/** 借款人姓名 **/
//	@Size(max=150)
//	@Column(name="CUSTNAME", length=150, columnDefinition="VARCHAR(150)")
//	private String custName;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得分項統一編號 **/
	public String getKeyCustId() {
		return this.keyCustId;
	}
	/** 設定分項統一編號 **/
	public void setKeyCustId(String value) {
		this.keyCustId = value;
	}

	/** 取得分項重覆序號 **/
	public String getKeyDupNo() {
		return this.keyDupNo;
	}
	/** 設定分項重覆序號 **/
	public void setKeyDupNo(String value) {
		this.keyDupNo = value;
	}

//	/** 取得借款人姓名 **/
//	public String getCustName() {
//		return this.custName;
//	}
//	/** 設定借款人姓名 **/
//	public void setCustName(String value) {
//		this.custName = value;
//	}
}
