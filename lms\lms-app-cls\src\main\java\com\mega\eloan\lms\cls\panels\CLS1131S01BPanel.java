/* 
 * CMS1301S01Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import org.springframework.beans.factory.annotation.Autowired;

import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.service.CLSService;

/**
 * <pre>
 * 個金徵信作業
 * </pre>
 * 
 * @since 2012/10/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/11,Fantasy,new
 *          </ul>
 */
public class CLS1131S01BPanel extends Panel {

	private static final long serialVersionUID = 1L;

	@Autowired
	CLSService clsService;
	
	/**
	 * @param id
	 */
	public CLS1131S01BPanel(String id) {
		super(id);		
	}
}
