/* 
 *LNLNF033ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.util.CapString;

import com.mega.eloan.lms.mfaloan.service.LNLNBadCRService;

/**
 * <pre>
 * LN.LN033 期付金控制檔
 * </pre>
 * 
 * @since 2012/11/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/7,REX,new
 *          </ul>
 */
@Service
public class LNLNBadCRServiceImpl extends AbstractMFAloanJdbc implements LNLNBadCRService {

	private static final Logger logger = LoggerFactory.getLogger(LNLNBadCRServiceImpl.class);
	
	// LNPBW03來源為MISBLACK，LNPBW02為退票現已改抓BTS來源
	@Override
	public List<Map<String, Object>> getNegativeCreditInfo(String custId) {

		String formatSQL = MessageFormat.format(getSqlBySqlId("LN.LNBADCR.getNegativeCreditInfo"), new Object[] {custId});
		logger.debug("formatSQL:{}", formatSQL);
		return getJdbc().queryForList(formatSQL, new Object [] {custId});
	}
	
	@Override
	public List<Map<String, Object>> getOverdueAndBadDebtInfo(List<String> custIdList, List<String> dupNoList) {
		if (custIdList.isEmpty()) {
			return null;
		}
		StringBuffer sql = new StringBuffer();
		List<String> custs = new ArrayList<String>();
		for (int i = 0; i < custIdList.size(); i++) {
			sql.append("?,");
			custs.add(CapString.fillBlankTail(custIdList.get(i), 10) + dupNoList.get(i));
		}
		sql = sql.replace(sql.length() - 1, sql.length(), CapConstants.EMPTY_STRING);
		String formatSQL = MessageFormat.format(getSqlBySqlId("LN.LNF030.LNF251.getOverdueAndBadDebtInfo"), new Object[]{sql.toString(), sql.toString()});
		logger.debug("formatSQL:{}", formatSQL);
		custs.addAll(custs);
		return getJdbc().queryForList(formatSQL, custs.toArray());
	}
}
