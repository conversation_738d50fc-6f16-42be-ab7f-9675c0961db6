var CLS1171S02Action = {
    itemType: "1",
    isInit: false,
    ghandle: "cls1171gridhandler",
    fhandle: "cls1151m01formhandler",
    formId: "#editBoxForm",
    grid: null,
    initEvent: function(){
    
    },
    initGrid: function(){
        this.grid = $("#gridveiwCntrNoDoc").iGrid({
            handler: CLS1171S02Action.ghandle,
            height: 200,
            rownumbers: true,
            sortname: 'printSeq|custId|cntrNo',
            sortorder: 'asc|asc|asc',
            //multiselect: true,
            rowNum: 10,
            postData: {
                formAction: "queryL140m01aByL141m01b",
                itemType: CLS1171S02Action.itemType
            },
            colModel: [{
                colHeader: i18n.cls1141m01["CLS.custName"],//借款人名稱
                width: 140,
                name: 'custName',
                sortable: true,
                formatter: 'click',
                onclick: CLS1171S02Action.openCntrNoDoc
            }, {
                colHeader: i18n.cls1141m01["CLS.cntrno"],//"額度序號",
                name: 'cntrNo',
                width: 80,
                sortable: true
            }, {
                colHeader: i18n.cls1141m01["CLS.cntrNoCom"],//"共用額度序號",
                name: 'commSno',
                width: 80,
                sortable: true
            }, {
                colHeader: "&nbsp;",
                name: 'currentApplyCurr',
                width: 25,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.cls1141m01["CLS.moneyAmt"],//現請額度,
                name: 'currentApplyAmt',
                width: 100,
                sortable: true,
                align: "right",
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
                    decimalPlaces: 0//小數點到第幾位
                }
            }, {
                colHeader: i18n.cls1141m01["CLS.proPerty"],//"性質"
                name: 'proPerty',
                width: 70,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.cls1141m01["CLS.docStatus"], //"文件狀態",
                name: 'docStatus',
                width: 60,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.cls1141m01["CLS.branchId"],//"分行別",
                name: 'ownBrId',
                width: 80,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.cls1141m01["CLS.dataDrc"],//來源
                name: 'dataSrc',
                width: 50,
                sortable: true,
                align: "center"
            }, {
                colHeader: "&nbsp",//"檢核欄位",
                name: 'chkYN',
                width: 20,
                sortable: true,
                align: "center"
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'printSeq',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }],
            ondblClickRow: function(rowid){
                var data = CLS1171S02Action.grid.getRowData(rowid);
                CLS1171S02Action.openCntrNoDoc(null, null, data);
            }
        });
        
    },
    
    /**
     *初始化
     */
    init: function(){
        if (!this.isInit) {
            this.initGrid();
            this.initEvent();
            this.isInit = true;
        }
    },
    start: function(obj){
        this.itemType = obj.docKind;
        this.init();
    },
    /**
     * 是否已經載入額度明細表
     */
    isLoadCntrNoDoc: false,
    /**
     * 開啟額度明細表
     *
     */
    openCntrNoDoc: function(column, oid, data){
		data.itemType = CLS1171S02Action.itemType;
        data.CaseMainId = $("#caseMainId").val();
        $.form.submit({
            url: "../cls1151s01",
            data: data,
            target: data.oid ? data.oid : "new"
        });
		/**
		 *<pre>
		 if (!CLS1171S02Action.isLoadCntrNoDoc) {
            $('#loadPage1').load(webroot + '/app/cls/cls1151s01', function(){
                CLS1171S02Action.isLoadCntrNoDoc = true;
                //CLS1151S01Page.js
                CLS115Action.openBox(data, {
                    itemType: CLS1171S02Action.itemType,
                    CaseMainId: $("#caseMainId").val()
                });
            });
        } else {
            CLS115Action.openBox(data, {
                itemType: CLS1171S02Action.itemType,
                CaseMainId: $("#caseMainId").val()
            });
        }
		  </pre>
		 */
        
    }
};
initDfd.done(function(obj){
    CLS1171S02Action.start(obj);
});




