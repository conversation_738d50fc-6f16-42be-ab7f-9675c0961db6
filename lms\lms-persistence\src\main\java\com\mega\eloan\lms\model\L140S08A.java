/* 
 * L140S08A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 代碼轉換檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140S08A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L140S08A extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 代碼名稱 **/
	@Size(max = 20)
	@Column(name = "ITEMNAME", length = 20, columnDefinition = "VARCHAR(20)")
	private String itemName;

	/** 代碼大類 **/
	@Size(max = 20)
	@Column(name = "MAINITEM", length = 20, columnDefinition = "VARCHAR(20)")
	private String mainItem;

	/** 代碼小類 **/
	@Size(max = 20)
	@Column(name = "SUBITEM", length = 20, columnDefinition = "VARCHAR(20)")
	private String subItem;

	/** 代碼說明 **/
	@Size(max = 300)
	@Column(name = "ITEMDSCR", length = 300, columnDefinition = "VARCHAR(300)")
	private String itemDscr;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得代碼名稱 **/
	public String getItemName() {
		return this.itemName;
	}

	/** 設定代碼名稱 **/
	public void setItemName(String value) {
		this.itemName = value;
	}

	/** 取得代碼大類 **/
	public String getMainItem() {
		return this.mainItem;
	}

	/** 設定代碼大類 **/
	public void setMainItem(String value) {
		this.mainItem = value;
	}

	/** 取得代碼小類 **/
	public String getSubItem() {
		return this.subItem;
	}

	/** 設定代碼小類 **/
	public void setSubItem(String value) {
		this.subItem = value;
	}

	/** 取得代碼說明 **/
	public String getItemDscr() {
		return this.itemDscr;
	}

	/** 設定代碼說明 **/
	public void setItemDscr(String value) {
		this.itemDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
