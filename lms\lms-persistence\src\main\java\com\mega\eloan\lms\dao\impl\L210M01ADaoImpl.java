/* 
 * L210M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L210M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;

import com.mega.eloan.lms.model.L210M01A;

/** 異動聯貸案參貸比率檔 **/
@Repository
public class L210M01ADaoImpl extends LMSJpaDao<L210M01A, String> implements
		L210M01ADao {

	@Override
	public L210M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L210M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L210M01A> findByDocStatus(String docStatus) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);
		List<L210M01A> list = createQuery(L210M01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L210M01A> findByMainIds(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L210M01A> list = createQuery(L210M01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L210M01A> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<L210M01A> list = createQuery(L210M01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L210M01A> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", CntrNo);
		search.addOrderBy("cntrNo");
		List<L210M01A> list = createQuery(L210M01A.class, search)
				.getResultList();

		return list;
	}
}