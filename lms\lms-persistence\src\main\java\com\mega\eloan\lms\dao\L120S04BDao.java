/* 
 * L120S04BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S04B;

/** 關係戶於本行往來實績彙總表主檔 **/
public interface L120S04BDao extends IGenericDao<L120S04B> {

	L120S04B findByOid(String oid);
	
	List<L120S04B> findByMainId(String mainId);
	
	List<L120S04B> findByMainIdKeyCustIdDupNo(String mainId, String keyCustId, String keyDupNo);
	
	List<L120S04B> findByMainIdDocKind(String mainId,String [] docKind);

    List<L120S04B> findByMainIdKeyCustIdDupNoDocKind(
            String mainId, String keyCustId, String keyDupNo, String[] docKind);
}