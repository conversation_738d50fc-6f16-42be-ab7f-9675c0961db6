/* 
 * L163S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 先行動用呈核及控制表檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L163S01A", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class L163S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 待辦事項
	 * <p/>
	 * 引進「五、其他事項」勾選「Ｘ」者<br/>
	 * 1024個全型字
	 */
	@Column(name = "WAITINGITEM", length = 3072, columnDefinition = "VARCHAR(3072)")
	private String waitingItem;

	/** 預定補全日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "WILLFINISHDATE", columnDefinition = "DATE")
	private Date willFinishDate;

	/** 先行動用覆核日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "UPLOADDATE", columnDefinition = "DATE")
	private Date uploadDate;

	/**
	 * 辦妥日期
	 * <p/>
	 * 於已覆核後登錄
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "FINISHDATE", columnDefinition = "DATE")
	private Date finishDate;

	/**
	 * 追蹤辦理情形
	 * <p/>
	 * 於已覆核後登錄<br/>
	 * 256個全型字
	 */
	@Column(name = "ITEMTRACE", length = 768, columnDefinition = "VARCHAR(768)")
	private String itemTrace;

	/**
	 * 甲級主管
	 * <p/>
	 * 登錄「辦妥日期、追蹤辦理情形」完成呈主管覆核時，勾選甲級主管人員名單
	 */
	@Column(name = "MANAGERID", length = 6, columnDefinition = "CHAR(6)")
	private String managerId;

	/**
	 * 甲級主管姓名
	 * <p/>
	 * 100/10/27新增 經辦可自行輸入甲級主管姓名 <br/>
	 * 10個全型字
	 **/
	@Column(name = "MANAGERNM", length = 30, columnDefinition = "VARCHAR(30)")
	private String managerNm;

	/** 乙級主管 **/
	@Column(name = "BOSSID", length = 6, columnDefinition = "CHAR(6)")
	private String bossId;

	/** 經辦 **/
	@Column(name = "APPRAISERID", length = 6, columnDefinition = "CHAR(6)")
	private String appraiserId;

	/** 覆核日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "BFRECHECKDATE", columnDefinition = "DATE")
	private Date bfReCheckDate;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得待辦事項
	 * <p/>
	 * 引進「五、其他事項」勾選「Ｘ」者<br/>
	 * 256個全型字
	 */
	public String getWaitingItem() {
		return this.waitingItem;
	}

	/**
	 * 設定待辦事項
	 * <p/>
	 * 引進「五、其他事項」勾選「Ｘ」者<br/>
	 * 256個全型字
	 **/
	public void setWaitingItem(String value) {
		this.waitingItem = value;
	}

	/** 取得預定補全日期 **/
	public Date getWillFinishDate() {
		return this.willFinishDate;
	}

	/** 設定預定補全日期 **/
	public void setWillFinishDate(Date value) {
		this.willFinishDate = value;
	}

	/** 取得先行動用覆核日 **/
	public Date getUploadDate() {
		return this.uploadDate;
	}

	/** 設定先行動用覆核日 **/
	public void setUploadDate(Date value) {
		this.uploadDate = value;
	}

	/**
	 * 取得辦妥日期
	 * <p/>
	 * 於已覆核後登錄
	 */
	public Date getFinishDate() {
		return this.finishDate;
	}

	/**
	 * 設定辦妥日期
	 * <p/>
	 * 於已覆核後登錄
	 **/
	public void setFinishDate(Date value) {
		this.finishDate = value;
	}

	/**
	 * 取得追蹤辦理情形
	 * <p/>
	 * 於已覆核後登錄<br/>
	 * 256個全型字
	 */
	public String getItemTrace() {
		return this.itemTrace;
	}

	/**
	 * 設定追蹤辦理情形
	 * <p/>
	 * 於已覆核後登錄<br/>
	 * 256個全型字
	 **/
	public void setItemTrace(String value) {
		this.itemTrace = value;
	}

	/**
	 * 取得甲級主管
	 * <p/>
	 * 登錄「辦妥日期、追蹤辦理情形」完成呈主管覆核時，勾選甲級主管人員名單
	 */
	public String getManagerId() {
		return this.managerId;
	}

	/**
	 * 設定甲級主管
	 * <p/>
	 * 登錄「辦妥日期、追蹤辦理情形」完成呈主管覆核時，勾選甲級主管人員名單
	 **/
	public void setManagerId(String value) {
		this.managerId = value;
	}

	/**
	 * 甲級主管姓名
	 * <p/>
	 * 於已覆核後登錄<br/>
	 * 10個全型字
	 */
	public String getManagerNm() {
		return this.managerNm;
	}

	/**
	 * 甲級主管姓名
	 * <p/>
	 * 100/10/27新增 經辦可自行輸入甲級主管姓名 <br/>
	 * 10個全型字
	 **/
	public void setManagerNm(String value) {
		this.managerNm = value;
	}

	/** 取得乙級主管 **/
	public String getBossId() {
		return this.bossId;
	}

	/** 設定乙級主管 **/
	public void setBossId(String value) {
		this.bossId = value;
	}

	/** 取得經辦 **/
	public String getAppraiserId() {
		return this.appraiserId;
	}

	/** 設定經辦 **/
	public void setAppraiserId(String value) {
		this.appraiserId = value;
	}

	/** 取得覆核日期 **/
	public Date getBfReCheckDate() {
		return this.bfReCheckDate;
	}

	/** 設定覆核日期 **/
	public void setBfReCheckDate(Date value) {
		this.bfReCheckDate = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
