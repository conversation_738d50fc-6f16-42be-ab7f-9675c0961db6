package com.mega.eloan.lms.eloandb.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 海外 - 覆審控制檔(r6)
 * </pre>
 * 
 * @since 2011/12/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/5,jessica,new
 *          </ul>
 */
public interface Lms412Service {

	public int insertLMS412(String custId, String dupNo, String brNo,
			String pkCust, String state, String newadd, Date newDt,
			String nckFg, String nckDt, String cancelDt, String commFg);

	public int updateLMS412(String lms412DataDt, String lms412Maincust,
			String lms412Cstate, String lms412Newadd, Date lms412Newdate,
			String lms412Nckdflag, String lms412Nckddate,
			String lms412Canceldt, String lms412Dbuobu, String lms412Lrdate,
			String lms412Llrdate, String lms412Nckdmemo, String lms412Nextnwdt,
			String lms412Nextltdt, String lms412Branch, String lms412CustId,
			String lms412Dupno);

//	public List<?> findLMS412ByBranch(String branch, String dataDate);

	public int updateLMS412ByLNFE0851(String mdFlag, String mddt,
			String process, String newadd, Date newDt, String memo,
			String branchId, String custId, String duoNo);

	// public int updateLMS412(String uCase, String uCaseRole, String mainCust,
	// String crdType, String crdTTbl, String mowType, String fcrdType,
	// String fcrdArea, String fcrdPred, String fcrdGrad, Date elfLRDate,
	// String elfRCkdLine, String elfUCkdLINE, Date elfUCkdDt,
	// String elfMDFlag, Date elfMDDt, String elfProcess,
	// String elfNewAdd, String elfNewDate, String elfNCkdFlag,
	// Date elfNCkdDate, String elfNCkdMemo, Date elfNextNwDt,
	// String elfDBUOBU, String elfMemo);

	public int updateLMS412ByGrade(String gradeType, String borrGrade,
			String mowType, String mowGrade, String fcrdType, String fcrdArea,
			String fcrdPred, String fcrdGrad, String custId, String dupNo,
			String branch);

	public int updateELF412(String gradeType, String borrGrade, String mowType,
			String moodyGrade, String custId, String dupNo, String branch);

	public List<Map<String, Object>> findELF412ByCustIdDupNoBranchs(
			String[] custIds, String[] dupNos, String[] branchs);

	public Map<String, Object> findELF412ByCustIdDupNoBranch(String custId,
			String dupNo, String branch);

	public int updateLMS412All(Date canceldt, Object nckdflag, Date nckddate,
			Date nextltdt, Date nextnwdt, Object mdflag, Object uckdline,
			Object rckdline, Object newadd, Object maincust, Object nckdmemo,
			Date uckddt, String newdate, Date lrdate, Date llrdate,
			String custId, String dupNo, String branch);

	List<Map<String, Object>> findAllLMS412ByBranch(String branch);

	void saveLms412updateMdflag(String mdFlag, String mdDt, String process,
			String custId, String dupNo, String branch);

	List<Map<String, Object>> findLms412JoinCust(String branch);

	List<Map<String, Object>> findLms412ByCustId(String branch, String custId,
			String dupNo);

	List<Map<String, Object>> findLms412ByCustIdNoMis(String branch,
			String custId, String dupNo);

	/**
	 * 更新lms412(新客戶)
	 * 
	 * @param branch
	 * @param custId
	 * @param dupNo
	 * @param mainCust
	 * @param newAdd
	 * @param newDate
	 * @param rckdLine
	 */
	void addLms412(String branch, String custId, String dupNo, String mainCust,
			String newAdd, String newDate, String rckdLine);

	/**
	 * 更新lms412(舊客戶)
	 * 
	 * @param dataDate
	 * @param mainCust
	 * @param cState
	 * @param newAdd
	 * @param newDate
	 * @param nckdFlag
	 * @param nckdDate
	 * @param cancelDt
	 * @param dbuObu
	 * @param lrDate
	 * @param llrDate
	 * @param nckdMemo
	 * @param nextNwdt
	 * @param nexLtdt
	 * @param branch
	 * @param custId
	 * @param dupNo
	 * @param rckdLine
	 */
	void saveLms412(Date dataDate, String mainCust, String cState,
			String newAdd, String newDate, String nckdFlag, Date nckdDate,
			Date cancelDt, String dbuObu, Date lrDate, Date llrDate,
			String nckdMemo, Date nextNwdt, Date nexLtdt, String rckdLine,
			String branch, String custId, String dupNo);

	void saveLms412Mdflag(String mdFlag, String mdDt, String process,
			String newAdd, String newDate, String memo, String branch,
			String custId, String dupNo);

	Map<String, Object> findLms412Uni(String branch, String custId, String dupNo);

	void saveBymaintain(String maincust, String crdtype, String crdttbl,
			String mowtype, String mowtbl1, Date lrdate, String rckdline,
			String mdflag, Date mddt, String process, String newadd,
			String newdate, String nckdflag, Date nckddate, String nckdmemo,
			String dbuobu, Date upddate, String updater, String memo,
			Date tmestamp, String uckdline, Date uckddt, Date nextnwdt,
			String ucase, String ucaserole, String isAllNew, String branch,
			String custid, String dupno);

	void addLMS412ByMainTain(String maincust, String crdtype, String crdttbl,
			String mowtype, String mowtbl1, Date lrdate, String rckdline,
			String mdflag, Date mddt, String process, String newadd,
			String newdate, String nckdflag, Date nckddate, String nckdmemo,
			String dbuobu, Date upddate, String updater, String memo,
			Date tmestamp, String uckdline, Date uckddt, Date nextnwdt,
			String ucase, String ucaserole, String isAllNew, String branch,
			String custid, String dupno);

	/**
	 * 覆審管理報表 type1 = 逾期未覆審名單
	 * 
	 * @param brNo
	 * @param dataDate
	 * @return
	 */
	public List<Map<String, Object>> findLMS412ByBranchForReportType1(
			String brNo);

//	/**
//	 * 覆審管理報表 type4 = 授信覆審明細檢核表
//	 *
//	 * @param brNo
//	 * @param dataDate
//	 * @return
//	 */
//	public List<Map<String, Object>> findLMS412ByBranchForReportType4(
//			String brNo);

	/**
	 * 覆審管理報表 type2 = 企金戶未出現於覆審名單
	 * 
	 * @param custId412
	 * @param dupNo412
	 * @return
	 */
	public List<Map<String, Object>> findLms412JoinCustData(String custId412,
			String dupNo412, String brNo);

	/**
	 * 名單待覆核=>已覆核 更新412
	 * 
	 * @param mainId
	 * @param branch
	 * @param dataDate
	 */
	void applyUpdate412(String mainId, String branch, Date dataDate);

}
