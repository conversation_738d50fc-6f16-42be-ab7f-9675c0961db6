/* 
 * L120S05ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L120S05ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S05A;

/** 借款人集團相關資料檔 **/
@Repository
public class L120S05ADaoImpl extends LMSJpaDao<L120S05A, String> implements
		L120S05ADao {

	@Override
	public L120S05A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S05A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S05A> list = createQuery(L120S05A.class,search).getResultList();
		return list;
	}

	@Override
	public L120S05A findByUniqueKey(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S05A> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L120S05A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L120S05A.class,search).getResultList();
		}
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findMaxCaseSeq(String mainId) {
		Query query = getEntityManager().createNamedQuery(
				"L120S05B.getGpcomamt");
		query.setParameter("MAINID", mainId); //設置參數
		return query.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findTotAmtBSeq(String mainId) {
		Query query = getEntityManager().createNamedQuery(
				"L120S05B.getTotAmtB");
		query.setParameter("MAINID", mainId); //設置參數
		return query.getResultList();
	}
	
	@Override
	public int delModel(String mainId){
		Query query = getEntityManager().createNamedQuery("L120S05A.delModel");
		query.setParameter("MAINID", mainId); //設置參數
		return query.executeUpdate();
	}
}