$(document).ready(function(){
	
	if(userInfo.isOverSea == true){
		//海外分行無團貸工作底稿
		$("input[type='radio'][name='shtType'][value='3']").remove();
		$("label[for='newButtonType2']").remove();
	}
	
    var groupBrNo = "";
    var formHandler = "";
    
    /**團貸批號*/
    var grpCntrNo = "";
    var getYesterday = function(){
        var tDate = new Date();
        tDate.setDate(tDate.getDate() - 1);
        return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") +
        (tDate.getMonth() + 1) +
        "-" +
        (tDate.getDate() < 10 ? "0" : "") +
        tDate.getDate();
    }
    
    var getFirstDateOfTheMonth = function(){
        var tDate = new Date();
        tDate.setDate(1);
        return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") +
        (tDate.getMonth() + 1) +
        "-" +
        (tDate.getDate() < 10 ? "0" : "") +
        tDate.getDate();
    }
    
    var getLastDateOfTheMonth = function(){
        var tDate = new Date();
        tDate.setMonth(tDate.getMonth() + 1);
        tDate.setDate(1);
        tDate.setDate(tDate.getDate() - 1);
        return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") +
        (tDate.getMonth() + 1) +
        "-" +
        (tDate.getDate() < 10 ? "0" : "") +
        tDate.getDate();
    }
    
    var lastSel;
    var rowId = 1;
    var groupRowId = 1;
    var rowed1 = $("#rowed1");
    
    rowed1.jqGrid({
        cellEdit: true,
        cellsubmit: 'clientArray',
        datatype: "local",
        rownumbers: true,
        caption: i18n.lms1945v01['lms1945.011'],
        colNames: [i18n.lms1945v01['lms1945.012'], i18n.lms1945v01['lms1945.013']],
        colModel: [{
            name: 'custid',
            index: 'custid',
            width: 300,
            editable: true,
            editoptions: {
                maxlength: "11"
            }
        }, {
            name: 'name',
            index: 'name',
            width: 250,
            editable: true
        }],
        beforeSaveCell: function(rowid, cellname, value, iRow, iCol){
            if (iCol == 1) {
                //                alert(rowid + " " + cellname + " " + value + " " + iRow + " " + iCol);
                $.ajax({
                    handler: 'customerformhandler',
                    action: "custQueryBy0024ByIdDupNo",
                    data: {
                        //custId: value,
						sendId: value.substring(0,value.length-1),
                  		dupNo: value.substring(value.length -1, value.length)
                    },
                    success: function(responseData){
                        if (responseData.cname) {
                            rowed1.editCell(iRow, 2);
                            rowed1.setCell(rowid, "name", responseData.cname);
                            
                        }
                        else {
                            rowed1.editCell(iRow, 2);
                            rowed1.setCell(rowid, "name", i18n.lms1945v01["lms1945.014"]);
                            //                            alert("no Data");
                        }
                    }
                });
            }
        }
    });
	
	var rowedForGroup = $("#rowedForGroup");
	
	rowedForGroup.jqGrid({
        cellEdit: false,
        cellsubmit: 'clientArray',
        datatype: "local",
        rownumbers: true,
        caption: i18n.lms1935v01['lms1935.011'],
        colNames: ["借款人", "名稱"],
        colModel: [{
            name: 'custid',
            index: 'custid',
            width: 300,
            editable: true,
            editoptions: {
                maxlength: "11"
            }
        }, {
            name: 'name',
            index: 'name',
            width: 250,
            editable: true
        }],
        beforeSaveCell: function(rowid, cellname, value, iRow, iCol){
            if (iCol == 1) {
                // alert(rowid + " " + cellname + " " +
                // value + " " + iRow + " " + iCol);
                $.ajax({
                    handler: 'customerformhandler',
                    action: "custQueryBy0024ByIdDupNo",
                    data: {
                        //custId: value,
						sendId: value.substring(0,value.length-1),
                  		dupNo: value.substring(value.length -1, value.length)
                    },
                    success: function(responseData){
                        if (responseData.cname) {
                            rowed1.editCell(iRow, 2);
                            rowed1.setCell(rowid, "name", responseData.cname);
                            
                        }
                        else {
                            rowed1.editCell(iRow, 2);
                            rowed1.setCell(rowid, "name", i18n.lms1935v01["lms1935.014"]);
                            // alert("no Data");
                        }
                    }
                });
            }
        }
    });
    
    var grid = $("#gridview").iGrid({
        sortname: 'checkDate',
        sortorder: 'desc',
        height: 380,
        width: "100%",
        multiselect: true,
        autowidth: true,
        handler: "lms1945gridhandler",
        action: "queryViewDocEditingDefault",
        postData: {
            docStatus: viewstatus
        },
        colModel: [{
            colHeader: i18n.lms1945v01['lms1945.001'],// "受檢單位",
            name: 'ownBrId',
            align: "center",
            width: 20,
            sortable: true
        }, {
            colHeader: i18n.lms1945v01['lms1945.002'],// "檢查日期",
            name: 'checkDate',
            align: "center",
            width: 15,
            sortable: true
        }, {
            colHeader: i18n.lms1945v01['lms1945.003'],// "主要借款人統編",
            name: 'custId',
            align: "center",
            width: 20,
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.lms1945v01['lms1945.004'],// "主要借款人名稱",
            name: 'custName',
            align: "center",
            width: 20,
            sortable: true
        }, {
            colHeader: i18n.lms1945v01['lms1945.006'],// "種類",
            name: 'shtType',
            align: "center",
            width: 30,
            sortable: true,
            formatter: function shtTypeFormatter(cellvalue, options, rowObject){
                return i18n.lms1945v01['shttype.name' + cellvalue];
            }
        }, {
            colHeader: i18n.lms1945v01['lms1945.007'],// "對帳單",
            name: 'createBill',
            align: "center",
            width: 10,
            sortable: true
        }, {
            colHeader: i18n.lms1945v01['lms1945.005'],// "檢查人",
            name: 'checkMan',
            align: "center",
            width: 20,
            sortable: true
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'uid',
            hidden: true
        }, {
            name: 'docStatus',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = grid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        //    	alert(rowObject.shtType);
        var shtType = rowObject.shtType;
        var url = "";
        
        switch (shtType) {
            case i18n.lms1945v01['shttype.name1']:
                url = "../las/lms1925m01/02";
                break;
            case i18n.lms1945v01['shttype.name2']:
                url = "../las/lms1905m01/02";
                break;
            case i18n.lms1945v01['shttype.name3']:
                url = "../las/lms1915m01/02";
                break;
        }
        
        var data = [];
        
        data = {
            mainDocStatus: viewstatus,
            mainId: rowObject.mainId,
            mainOid: rowObject.oid
        };
        
        //假如主管沒有編輯權限，所以打開文件的時後，不鎖定Lock        
        if (noOpenDoc == "Y") {
            data["noOpenDoc"] = "Y"
        }
        
        $.form.submit({
            url: url,
            data: data,
            target: rowObject.oid
        });
        
        
    }
    
    var resourceGrid = $("#ResourceResult").iGrid({
        //rowNum: 2,
        localFirst: true,
        height: 280,
        width: "100%",
        autowidth: true,
        handler: "lms1925gridhandler",
        action: "getBorrows",
        rownumbers: true,
        multiselect: true,
        postData: {
            brId: userInfo.unitNo
        },
        colModel: [{
            colHeader: i18n.lms1945v01['lms1945.003'],
            name: 'custId',
            align: "center",
            width: 15,
            sortable: true
        }, {
            colHeader: i18n.lms1945v01['lms1945.009'],
            name: 'dupNo',
            align: "center",
            width: 5,
            sortable: true
        }, {
            colHeader: i18n.lms1945v01['lms1945.004'],// "",
            name: 'custName',
            align: "center",
            width: 20,
            sortable: true
        }]
    });
    
    var resourceForGroupGrid = $("#ResourceResultForGroup").iGrid({
        // rowNum: 2,
        needPager: false,
        localFirst: true,
        height: 250,
        width: "100%",
        autowidth: true,
        handler: "lms1915gridhandler",
        action: "getBorrows",
        rownumbers: true,
        postData: {
            brId: userInfo.unitNo,
            custId: $("#GroupCustId").val()
        },
        colModel: [{
            colHeader: i18n.lms1945v01['lms1945.019'],//"團貸總戶序號",
            name: 'grpCntrNo',
            align: "center",
            width: 10,
            sortable: true
        }, {
            colHeader: i18n.lms1945v01['lms1945.020'],//"名稱",
            name: 'projectNm',
            align: "center",
            width: 15,
            sortable: true
        }]
    });
    
    var resourceForGroupGridLayer2 = $("#ResourceResultForGroupLayer2").iGrid({
        multiselect: true,
        needPager: false,
        localFirst: true,
        height: 250,
        width: "100%",
        autowidth: true,
        handler: "lms1915gridhandler",
        action: "getBorrows2",
        rownumbers: true,
        postData: {
            brId: "",
            grpCntrNo: ""
        },
        colModel: [{
            colHeader: i18n.lms1945v01['lms1945.025'],//"統編",
            name: 'custId',
            align: "center",
            width: 10,
            sortable: true
        }, {
            colHeader: i18n.lms1945v01['lms1945.026'],//"名稱",
            name: 'name',
            align: "center",
            width: 15,
            sortable: true
        }, {
            colHeader: i18n.lms1945v01['lms1945.027'],//"餘額",
            name: 'bal',
            align: "center",
            width: 10,
            sortable: true
        }]
    });
    
    $("#buttonPanel").find("#btnDelete").click(function(){
        var selectRows = grid.getGridParam('selarrrow');
        
        if (selectRows == "") {
            CommonAPI.showErrorMessage(i18n.def["action_002"]);
            return false;
        }
        API.confirmMessage(i18n.def["action_003"], function(result){
            $.each(selectRows, function(i, data){
                var ret = grid.getRowData(data);
                var handler = "";
                switch (ret.shtType) {
                    case i18n.lms1945v01['shttype.name1']:
                        handler = "lms1925m01formhandler";
                        break;
                    case i18n.lms1945v01['shttype.name2']:
                        handler = "lms1905m01formhandler";
                        break;
                    case i18n.lms1945v01['shttype.name3']:
                        handler = "lms1915m01formhandler";
                        break;
                }
                result &&
                $.ajaxQueue({
                    handler: handler,
                    action: "deleteL192M01A",
                    data: {
                        deleteMainOid: ret.oid,
                        deleteMainId: ret.mainId
                    },
                    success: function(responseData){
                        if (!(typeof responseData.deleteMessage == "undefined")) {
                            CommonAPI.showErrorMessage(i18n.lms1945v01['lms1945.002'] + ":" + ret.checkDate + "," + i18n.lms1945v01['lms1945.003'] + ":" + ret.custId + "<br>" + responseData.deleteMessage);
                        }
                        if ((i + 1) == selectRows.length) {
                            grid.trigger("reloadGrid");
                        }
                    }
                });
            });
        });
    }).end().find("#btnAdd").click(function(){
        $("#selectShtType").thickbox({
            title: '',
            width: 300,
            height: 175,
            align: 'center',
            valign: 'bottom',
            modal: false,
            buttons: API.createJSON([{
                key: i18n.def['sure'],
                value: function(){
                    var shtType = $("input[name='shtType']:checked").val();
                    
                    switch (shtType) {
                        case "1":
                            formHandler = "lms1925m01formhandler";
                            $.thickbox.close();
                            
                            $("#viewForm_1").append($("#tool").show());
                            
                            
                            resourceGrid.setGridParam({
                                url: __ajaxHandler + '&_pa=' + "lms1925gridhandler"
                            });
                            
                            
                            //resourceGrid.trigger("reloadGrid");
                            
                            
                            rowId = 1;
                            lastSel = -1;
                            rowed1.clearGridData().jqGrid('addRowData', rowId, {
                                custid: "",
                                name: ""
                            });
                            rowed1.editCell(1, 1, true);
                            
                            $.ajax({
                                handler: "lms1925m01formhandler",
                                action: "getLatestAuditSheet",
                                data: {
                                    shtType: "1", //授信業務 
                                    innerAudit: "Y" //內部查核
                                },
                                success: function(responseData){
                                    $("#userItem1_1").val(responseData.userItem1);
                                    $("#leader_1").val(responseData.leader);
                                }
                            });
                            
                            $("#checkDate_1").val(CommonAPI.getToday());
                            var yesterday = getYesterday();
                            $("#checkBase_1").val(yesterday);
                            $("#checkMan_1").val(userInfo.userCName);
                            
                            $("#LMS1945VThickbox_1").thickbox({ // 使用選取的內容進行彈窗
                                title: '新增查核授信業務工作底稿',
                                width: 640,
                                height: 450,
                                align: 'center',
                                valign: 'bottom',
                                modal: false,
                                buttons: API.createJSON([{
                                    key: i18n.def['sure'],
                                    value: function(){
                                        if (!$('#viewForm_1').valid()) {
                                            return false;
                                        }
                                        if (!submitConfirm()) {
                                            return false;
                                        }
                                        //                                        confirm();
                                        //ie此段一定要加，不然$cap('#rowed1')的資料不會是最新的，不知道為什麼 = =，FireFox則不會有問題
                                        API.confirmMessage(i18n.def["confirmSend"], function(result){
                                            result &&
                                            $.ajax({
                                                handler: "lms1925m01formhandler",
                                                data: $.extend($("#viewForm_1").serializeData(), {
                                                    formAction: "addNew",
                                                    innerAudit: "Y",
                                                    checkBase: $("#checkBase_1").val(),
                                                    checkDate: $("#checkDate_1").val(),
                                                    leader: $("#leader_1").val(),
                                                    checkMan: $("#checkMan_1").val(),
                                                    userItem1: $("#userItem1_1").val(),
                                                    custIDs: $cap('#rowed1').iGridSerialize(true)
                                                }),
                                                success: function(responseData){
//                                                    $.each(responseData.custIDs, function(i, data){
//                                                    
//                                                        $.form.submit({
//                                                            url: '../las/lms1925m01/02',
//                                                            data: {
//                                                                mainDocStatus: viewstatus,
//                                                                mainOid: data.mainOid,
//                                                                mainId: data.mainId
//                                                            },
//                                                            target: data.mainOid
//                                                        });
//                                                    });
                                                    // CommonAPI.triggerOpener("gridview","reloadGrid");
                                                    grid.trigger("reloadGrid");
                                                    $.thickbox.close();
                                                }
                                            });
                                        })
                                    }
                                }, {
                                    key: i18n.def['cancel'],
                                    value: function(){
                                        $.thickbox.close();
                                    }
                                }])
                            });
                            break;
                        case "2":
                            formHandler = "lms1905m01formhandler";
                            $.thickbox.close();
                            $("#viewForm_2").append($("#tool").show());
                            
                            resourceGrid.setGridParam({
                                url: __ajaxHandler + '&_pa=' + "lms1905gridhandler"
                            });
                            
                            
                            //							resourceGrid.trigger("reloadGrid");
                            
                            
                            
                            
                            rowId = 1;
                            lastSel = -1;
                            rowed1.clearGridData().jqGrid('addRowData', rowId, {
                                custid: "",
                                name: ""
                            });
                            rowed1.editCell(1, 1, true);
                            
                            $.ajax({
                                handler: "lms1905m01formhandler",
                                action: "getLatestAuditSheet",
                                data: {
                                    shtType: "2",
                                    innerAudit: "Y"
                                },
                                success: function(responseData){
                                    $("#userItem1_2").val(responseData.userItem1);
                                    $("#userItem2_2").val(responseData.userItem2);
                                    $("#userItem3_2").val(responseData.userItem3);
                                    $("#leader_2").val(responseData.leader);
                                }
                            });
                            
                            $("#checkDate_2").val(CommonAPI.getToday());
                            var yesterday = getYesterday();
                            $("#checkBase_2").val(yesterday);
                            $("#checkMan_2").val(userInfo.userCName);
                            
                            
                            $("#LMS1945VThickbox_2").thickbox({ // 使用選取的內容進行彈窗
                                title: '新增房貸授信業務工作底稿',
                                width: 640,
                                height: 450,
                                align: 'center',
                                valign: 'bottom',
                                modal: false,
                                buttons: API.createJSON([{
                                    key: i18n.def['sure'],
                                    value: function(){
                                        if (!$('#viewForm_2').valid()) {
                                            return false;
                                        }
                                        if (!submitConfirm()) {
                                            return false;
                                        }
                                        //                                        confirm();
                                        //ie此段一定要加，不然$cap('#rowed1')的資料不會是最新的，不知道為什麼 = =，FireFox則不會有問題
                                        API.confirmMessage(i18n.def["confirmSend"], function(result){
                                            if(result){ 
                                            	checkLoanNo_house(
                                            		{'innerAudit': "Y",
                                            		 'custIDs': $cap('#rowed1').iGridSerialize(true)}
                                            	).done(function(json){
                                        		
		                                            $.ajax({
		                                                handler: "lms1905m01formhandler",
		                                                data: $.extend($("#viewForm_2").serializeData(), {
		                                                    formAction: "addNew",
		                                                    innerAudit: "Y",
		                                                    checkBase: $("#checkBase_2").val(),
		                                                    checkDate: $("#checkDate_2").val(),
		                                                    leader: $("#leader_2").val(),
		                                                    checkMan: $("#checkMan_2").val(),
		                                                    userItem1: $("#userItem1_2").val(),
		                                                    userItem2: $("#userItem2_2").val(),
		                                                    userItem3: $("#userItem3_2").val(),
		                                                    custIDs: $cap('#rowed1').iGridSerialize(true)
		                                                }),
		                                                success: function(responseData){
		//                                                    $.each(responseData.custIDs, function(i, data){
		//                                                    
		//                                                        $.form.submit({
		//                                                            url: '../las/lms1905m01/02',
		//                                                            data: {
		//                                                                mainDocStatus: viewstatus,
		//                                                                mainOid: data.mainOid,
		//                                                                mainId: data.mainId
		//                                                            },
		//                                                            target: data.mainOid
		//                                                        });
		//                                                    });
		                                                    // CommonAPI.triggerOpener("gridview","reloadGrid");
		                                                    grid.trigger("reloadGrid");
		                                                    $.thickbox.close();
		                                                }
		                                            });
                                            	});
                                            }
                                        })
                                        
                                    }
                                }, {
                                    key: i18n.def['cancel'],
                                    value: function(){
                                        $.thickbox.close();
                                    }
                                }])
                            });
                            break;
                        case "3":
                            $("#viewForm_3").append($("#toolForGroup").show());
                            $.ajax({
                                handler: "lms1915m01formhandler",
                                action: "getLatestAuditSheet",
                                data: {
                                    shtType: "3",
                                    innerAudit: "N"
                                },
                                success: function(responseData){
                                    $("#userItem1_3").val(responseData.userItem1);
                                    $("#userItem2_3").val(responseData.userItem2);
                                    $("#userItem3_3").val(responseData.userItem3);
                                    $("#leader_3").val(responseData.leader);
                                }
                            });
                            
                            $("#checkDate_3").val(CommonAPI.getToday());
                            var yesterday = getYesterday();
                            $("#checkBase_3").val(yesterday);
                            $("#checkMan_3").val(userInfo.userCName);
                            
                            $("#LMS1945VThickbox_3").thickbox({ // 使用選取的內容進行彈窗
                                title: i18n.lms1945v01['shttype.name3'],
                                width: 640,
                                height: 450,
                                align: 'center',
                                valign: 'bottom',
                                modal: false,
                                buttons: API.createJSON([{
                                    key: i18n.def['sure'],
                                    value: function(){
                                        if (!$('#viewForm_3').valid()) {
                                            return false;
                                        }
                                        
                                        API.confirmMessage(i18n.def["confirmSend"], function(result){
                                            result &&
                                            $.ajax({
                                                handler: "lms1915m01formhandler",
                                                data: $.extend($("#viewForm_3").serializeData(), {
                                                    formAction: "addNew",
                                                    innerAudit: "Y",
                                                    checkBase: $("#checkBase_3").val(),
                                                    checkDate: $("#checkDate_3").val(),
                                                    leader: $("#leader_3").val(),
                                                    checkMan: $("#checkMan_3").val(),
                                                    userItem1: $("#userItem1_3").val(),
                                                    userItem2: $("#userItem2_3").val(),
                                                    userItem3: $("#userItem3_3").val(),
                                                    custIDs: $cap('#rowedForGroup').iGridSerialize(true),
                                                    grpCntrNo: grpCntrNo
                                                }),
                                                success: function(responseData){
                                                
//                                                    $.form.submit({
//                                                        url: '../las/lms1915m01/02',
//                                                        data: {
//                                                            mainDocStatus: viewstatus,
//                                                            mainOid: responseData.mainOid,
//                                                            mainId: responseData.mainId
//                                                        },
//                                                        target: responseData.mainOid
//                                                    });
                                                    
                                                    // 用grid.trigger("reloadGrid")  會抓不到，再重抓一次，應該是和megaapi有衝到
                                                    $("#gridview").trigger("reloadGrid");
                                                    $.thickbox.close();
                                                    $.thickbox.close();
                                                }
                                            });
                                        })
                                        
                                    }
                                }, {
                                    key: i18n.def['cancel'],
                                    value: function(){
                                        $.thickbox.close();
                                    }
                                }])
                            });
                            break;
                    }
                }
            }, {
                key: i18n.def['cancel'],
                value: function(){
                    $.thickbox.close();
                }
            }])
        });
        
    }).end().find("#btnToReviewAll").click(function(){
    
        var selectRows = grid.getGridParam('selarrrow');
        if (selectRows == "") {
            CommonAPI.showErrorMessage(i18n.def["action_005"]);
            return false;
        }
        API.confirmMessage(i18n.def["confirmSend"], function(result){
            if (result) {
                $.each(selectRows, function(i, data){
                    var ret = grid.getRowData(data);
                    $.ajaxQueue({
                        handler: ret.shtType == i18n.lms1945v01['shttype.name1'] ? "lms1925m01formhandler" : ret.shtType == i18n.lms1945v01['shttype.name2'] ? "lms1905m01formhandler" : "lms1915m01formhandler",
                        action: "sendAll",
                        data: {
                            mainOid: ret.oid,
                            mainDocStatus: viewstatus
                        },
                        success: function(responseData){
                            if ((i + 1) == selectRows.length) {
                                grid.trigger("reloadGrid");
                            }
                        }
                    });
                });
            }
        });
        
    }).end().find("#btnReviewAll").click(function(){
    
    
    }).end().find("#btnFilter").click(function(){
    	var form = $("#queryDialogForm");
        $("#queryDialogForm").reset();
        $("#checkDateStart").val(getFirstDateOfTheMonth());
        $("#checkDateEnd").val(getLastDateOfTheMonth());
        $.ajax({
            handler: "codetypehandler",
            action: "userByBranch",
            success: function(json){
            	var updater = form.find("#updater");
            	updater.setOptions({
                    "": ""
                }, true);
            	updater.setItems({
	    	        item: json,
	    	        format: "{value} - {key}"
	    	    });
            }
        });
        $("#queryDialog").thickbox({
            title: i18n.lms1945v01['auditSheet'],
            width: 500,
            height: 300,
            align: 'center',
            valign: 'bottom',
            modal: false,
            buttons: API.createJSON([{
                key: i18n.def['sure'],
                value: function(){
                    $("#custId").val($("#custId").val().toUpperCase());
                    if ($("#queryDialogForm").valid()) {
                        grid.jqGrid('setGridParam', {
                            postData: {
                                formAction: "queryViewDocByQueryDialog",
                                checkDateStart: $("#checkDateStart").val(),
                                checkDateEnd: $("#checkDateEnd").val(),
                                shtType: $("#shtType").val(),
                                updater: $("#updater").val(),
                                custId: $("#custId").val()
                            }
                        });
                        grid.trigger("reloadGrid");
                        $.thickbox.close()
                    }
                    
                }
            }, {
                key: i18n.def['cancel'],
                value: function(){
                    $.thickbox.close();
                }
            }])
        });
    }).end().find("#btnPrintAllR01").click(function(){
    
        var selectRows = grid.getGridParam('selarrrow');
        
        if (selectRows == "") {
            CommonAPI.showErrorMessage(i18n.def["action_005"]);
            return false;
        }
        
        API.confirmMessage(i18n.def["actoin_001"], function(result){
            if (result) {
                var tmp = "";
                $.each(selectRows, function(i, data){
                    var ret = grid.getRowData(data);
                    tmp = tmp + ret.oid + "|";
                });
                
                $.form.submit({
                    url: "../las/lms1945r01",
                    target: "_blank",
                    data: {
                        mainOids: tmp
                    }
                });
            }
        });
        
    }).end().find("#btnPrintAllR02").click(function(){
    
        var selectRows = grid.getGridParam('selarrrow');
        
        if (selectRows == "") {
            CommonAPI.showErrorMessage(i18n.def["action_005"]);
            return false;
        }
        var tmp = "";
        var showMessage = "";
        
        $.each(selectRows, function(i, data){
            var ret = grid.getRowData(data);
            var handler = "";
            switch (ret.shtType) {
                case i18n.lms1945v01['shttype.name1']:
                    handler = "lms1925m01formhandler";
                    break;
                case i18n.lms1945v01['shttype.name2']:
                    handler = "lms1905m01formhandler";
                    break;
                case i18n.lms1945v01['shttype.name3']:
                    handler = "lms1915m01formhandler";
                    break;
            }
            
            $.ajaxQueue({
                handler: handler,
                action: "checkPrint",
                data: {
                    mainOid: ret.oid,
                    mainDocStatus: viewstatus
                },
                success: function(responseData){
                	grid.trigger("reloadGrid");
                	showMessage = showMessage + i18n.lms1945v01['lms1945.002'] + ":" + ret.checkDate + "，" + i18n.lms1945v01['lms1945.003'] + ":" + ret.custId + '：';
                    if (responseData.L192S01A_PRINT_MARK == "Y" || responseData.L192S01A_PRINT_MARK == "P") {
                        tmp = tmp + ret.oid + "|";
                        showMessage = showMessage + "<br>";
                        showMessage = showMessage + responseData.NO_PRINT_DETAIL;
                        showMessage = showMessage + "<br>";
                    } else if (responseData.L192S01A_PRINT_MARK == "N"){
                        showMessage = showMessage + i18n.lms1945v01['lms1945.010'];
                        showMessage = showMessage + "<br>";
                        showMessage = showMessage + responseData.NO_PRINT_DETAIL;
                        showMessage = showMessage + "<br>";
                    }
                    if ((i + 1) == selectRows.length && tmp.length > 0) {
                        API.confirmMessage(showMessage.length == 0 ? i18n.def["actoin_001"] : showMessage, function(result){
                            if (result) {
                                $.form.submit({
                                    url: "../las/lms1945r02",
                                    target: "_blank",
                                    data: {
                                        mainOids: tmp
                                    }
                                });
                                //設定5秒reoload，但如果report印得太慢的話，那麼寫回createBill的速度會比較慢，那可能沒效果
                                setTimeout(function(){
                                    grid.trigger("reloadGrid");
                                }, 5000);
                            }
                        });
                    } else if ((i + 1) == selectRows.length && showMessage.length > 0) {
                        API.showMessage(showMessage);
                    }
                }
            });
        });
        
        
    });
    
    $("#getNewBorrow").click(function(){
        $("#Resources").thickbox({
            title: i18n.lms1945v01["lms1945.008"],
            width: 640,
            height: 400,
            align: 'center',
            valign: 'bottom',
            modal: false,
            open: function(){
                resourceGrid.trigger("reloadGrid");
            },
            buttons: API.createJSON([{
                key: i18n.def['sure'],
                value: function(){
                    var selectRows = resourceGrid.getGridParam('selarrrow');
                    if (selectRows == "") {
                        CommonAPI.showErrorMessage(i18n.def["action_004"]);
                        return false;
                    }
                    API.confirmMessage(i18n.def["actoin_001"], function(result){
                        if (result) {
                            rowed1.clearGridData();
                            rowId = 1;
                            $.each(selectRows, function(i, data){
                                var ret = resourceGrid.getRowData(data);
                                rowed1.jqGrid('addRowData', rowId, {
                                    custid: ret.custId + ret.dupNo,
                                    name: ret.custName
                                });
                                rowId = rowId + 1
                            });
                            $.thickbox.close();
                        }
                    });
                }
            }, {
                key: i18n.def['cancel'],
                value: function(){
                    $.thickbox.close();
                }
            }])
        });
    });
    
    $("#addNewBorrow").click(function(){
        rowId = rowId + 1;
        rowed1.jqGrid("addRowData", rowId, {
            custid: "",
            name: ""
        });
        lastSel = rowId;
        
        var allIds = rowed1.getDataIDs();
        ilog.debug(allIds.length);
        
        rowed1.editCell(allIds.length, 1, true);
    });
    
    var submitConfirm = function(){
        //debugger;
        var tmpCheck = true;
        var allIds = rowed1.getDataIDs();
        if (allIds == "") {
            //2012_07_25_Rex edit
            //            CommonAPI.showErrorMessage(i18n.abstracteloan('EFD0005', {
            //                'colName': i18n.lms1945v01['lms1945.012']
            //            }));
            CommonAPI.showErrorMessage(i18n.msg('EFD0005').replace(/\$\\{colName\\}/, i18n.lms1945v01['lms1945.012']));
            return false;
        }
        
        $(allIds).each(function(i, data){
        
            var thisRowId = parseInt(this, 10);
            
            rowed1.editCell((i + 1), 1, true);
            rowed1.saveCell((i + 1), 1);
            $.ajax({
                async: false,
                handler: 'customerformhandler',
                action: "custQueryBy0024ByIdDupNo",
                data: {
                    //custId: rowed1.getCell((thisRowId), 1),
					sendId: rowed1.getCell((thisRowId), 1).substring(0,rowed1.getCell((thisRowId), 1).length -1),
                 	dupNo: rowed1.getCell((thisRowId), 1).substring(rowed1.getCell((thisRowId), 1).length - 1, rowed1.getCell((thisRowId), 1).length)
                },
                success: function(responseData){
                    if (responseData.cname) {
                        tmpCheck = true;
                    }
                    else {
                        tmpCheck = false;
                    }
                }
            });
            
            if (tmpCheck == false) {
                //                alert('you input a worng id');
                CommonAPI.showErrorMessage($.validator.format(i18n.lms1945v01['lms1945.015'], i + 1) + "，" + i18n.lms1945v01["lms1945.014"]);
                return tmpCheck;
            }
        });
        //lastSel = -1;
        return tmpCheck;
    }
    //    var inputCheck = function(thisRowId){
    //        var tmpCheck = true;
    //        $("#rowed1").find("input[name='custid']").addClass("required");
    //        $("#rowed1").find("input[name='name']").addClass("required");
    //        if (!$('#viewForm_1').valid()) {
    //            return false;
    //        }
    //        $("#rowed1").find("input[name='custid']").each(function(i){
    //            var idDup = $(this).val().trim().toUpperCase();
    //            $(this).val(idDup);
    //            //			alert(idDup);
    //            var id = idDup.substring(0, idDup.length - 1)
    //            if (id.length == 8) {
    //                if (!CommonAPI.checkCompanyNo(id)) {
    //                    tmpCheck = false;
    //                    API.showPopMessage(i18n.def['val.compNo']);
    //                }
    //            }
    //            else 
    //                if (id.length == 10) {
    //                    if (!(CommonAPI.checkTWID(id))) {
    //                        tmpCheck = false;
    //                        API.showPopMessage(i18n.def['val.twid']);
    //                    }
    //                }
    //                else {
    //                    API.showPopMessage($.validator.format(i18n.def['val.rangelength'], 9, 11));
    //                    tmpCheck = false;
    //                }
    //            if (tmpCheck == false) {
    //                //$(this).focus();
    //                //alert("lastSel : " + rowId);
    //                //				alert("thisRowId" + thisRowId);
    //                rowed1.setSelection(thisRowId);
    //            }
    //        });
    //        
    //        if (tmpCheck == false) {
    //            return false;
    //        }
    //        else {
    //            return true;
    //        }
    //    }
    
    //    var confirm = function(){
    //        rowed1.jqGrid('saveRow', lastSel, false, 'clientArray');
    //        // alert($cap('#rowed1').iGridSerialize(true));
    //        rowed1.resetSelection();
    //        // 重置flag
    //        lastSel = -1;
    //    };
    
    $("#deleteRow").click(function(){
        var theRowId = rowed1.jqGrid('getGridParam', 'selrow');
        if (theRowId) {
            rowed1.delRowData(theRowId);
            rowed1.resetSelection();
        }
        else {
            CommonAPI.showErrorMessage(i18n.def["action_002"]);
        }
    })
    $("#getGroupBorrow").click(function(){
        $("#GroupCustIdShow").thickbox({
            title: i18n.lms1945v01["lms1945.018"], //請輸入團貸總戶ID
            width: 640,
            height: 100,
            align: 'center',
            valign: 'bottom',
            modal: false,
            open: function(){
            
            },
            buttons: API.createJSON([{
                key: i18n.def['sure'],
                value: function(){
                    $("#ResourcesForGroup").thickbox({
                        title: i18n.lms1945v01["lms1945.024"],
                        width: 640,
                        height: 400,
                        align: 'center',
                        valign: 'bottom',
                        modal: false,
                        open: function(){
                            resourceForGroupGrid.setGridParam({
                                postData: {
                                    custId: $("#GroupCustId").val()
                                }
                            });
                            resourceForGroupGrid.trigger("reloadGrid");
                        },
                        buttons: API.createJSON([{
                            key: i18n.def['sure'],
                            value: function(){
                                var selrow = resourceForGroupGrid.getGridParam('selrow');
                                if (!selrow) {
                                    CommonAPI.showErrorMessage(i18n.def["action_004"]);
                                    return false;
                                }
                                var ret = resourceForGroupGrid.getRowData(selrow);
                                grpCntrNo = ret.grpCntrNo;
                                resourceForGroupGridLayer2.setGridParam({
                                    postData: {
                                        grpCntrNo: ret.grpCntrNo,
                                        brNo: userInfo.unitNo
                                    }
                                });
                                
                                $("#ResourcesForGroupLayer2").thickbox({
                                    title: i18n.lms1945v01["lms1945.008"],
                                    width: 640,
                                    height: 400,
                                    align: 'center',
                                    valign: 'bottom',
                                    modal: false,
                                    open: function(){
                                        resourceForGroupGrid.setGridParam({
                                            postData: {
                                                custId: $("#GroupCustId").val()
                                            }
                                        });
                                        resourceForGroupGridLayer2.trigger("reloadGrid");
                                    },
                                    buttons: API.createJSON([{
                                        key: i18n.def['sure'],
                                        value: function(){
                                        
                                        
                                            var selectRows = resourceForGroupGridLayer2.getGridParam('selarrrow');
                                            if (selectRows == "") {
                                                CommonAPI.showErrorMessage(i18n.def["action_004"]);
                                                return false;
                                            }
                                            API.confirmMessage(i18n.def["actoin_001"], function(result){
                                                if (result) {
                                                    rowedForGroup.clearGridData();
                                                    groupRowId = 1;
                                                    $.each(selectRows, function(i, data){
                                                        var ret = resourceForGroupGridLayer2.getRowData(data);
                                                        rowedForGroup.jqGrid('addRowData', groupRowId, {
                                                            custid: ret.custId,
                                                            name: ret.name
                                                        });
                                                        groupRowId = groupRowId + 1
                                                    });
                                                    $.thickbox.close();
                                                    $.thickbox.close();
                                                    $.thickbox.close();
                                                }
                                            });
                                            
                                            
                                        }
                                    }, {
                                        key: i18n.def['cancel'],
                                        value: function(){
                                            $.thickbox.close();
                                        }
                                    }])
                                });
                                
                                
                            }
                        }, {
                            key: i18n.def['cancel'],
                            value: function(){
                                $.thickbox.close();
                            }
                        }])
                    });
                }
            }, {
                key: i18n.def['cancel'],
                value: function(){
                    $.thickbox.close();
                }
            }])
        });
    })
});

function checkLoanNo_house(param_input){
	var my_dfd = $.Deferred();
	$.ajax({
        handler: 'lms1905m01formhandler',
        data: $.extend({formAction: "checkLoanNo_house"}, param_input||{} ),
        success: function(obj){
        	if(obj.msg){
        		API.confirmMessage(obj.msg, function(result){
                    if (result) {
                    	my_dfd.resolve();
                    }else{
                    	my_dfd.reject();
                    }
                });

        	}else{
        		my_dfd.resolve();
        	}
        }
    }); 		
    return my_dfd.promise();
}