/* 
 *ObsdbELF384ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbELF384Service;

/**
 * <pre>
 * 科(子)目及其限額檔 ELF384
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
@Service
public class ObsdbELF384ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF384Service {

	@Override
	public void insert(String BRNID, List<Object[]> dataList) {
		this.getJdbc(BRNID)
				.batchUpdate("ELF384.insert", new int[] {}, dataList);

	}

	@Override
	public void insert(String BRNID, Object[] dataList) {
		this.getJdbc(BRNID).update("ELF384.insert", dataList);

	}

	@Override
	public void update(String BRNID, Object[] dataList) {
		this.getJdbc(BRNID).update("ELF384.update", dataList);

	}

	@Override
	public List<Map<String, Object>> selByUniqueKey(String BRNID, Object[] args) {
		return this.getJdbc(BRNID).queryForList(
				"ELF384.selByUniqueKey", args);

	}

	@Override
	public int delBySdate(String BRNID, String custId, String dupNo,
			String cntrNo, String sDate) {
		return this.getJdbc(BRNID).update("ELF384.delBySdate",
				new Object[] { custId, dupNo, cntrNo, sDate });
	}

	@Override
	public int updateChgflagBySdate(String BRNID, String custId, String dupNo,
			String cntrNo, String sDate) {
		return this.getJdbc(BRNID).update(
				"ELF384.updateChgflagBySdate",
				new Object[] {
						this.covertAs400Time(CapDate.getCurrentTimestamp()),
						custId, dupNo, cntrNo, Util.parseBigDecimal(sDate) });
	}

	@Override
	public int insetSelectOldData(String BRNID, String custId, String dupNo,
			String cntrNo, String sDate, String OLDSDATE, String updater) {
		sDate = StringEscapeUtils.escapeSql(sDate);
		updater = StringEscapeUtils.escapeSql(updater);
		return this.getJdbc(BRNID).updateByCustParam(
				"ELF384.insetSelChgflagBySdate",
				new Object[] {
						sDate,
						"'" + updater + "'",
						this.covertAs400Time(CapDate.getCurrentTimestamp())
								.toString() },
				new Object[] { custId, dupNo, cntrNo,
						Util.parseBigDecimal(OLDSDATE) });
	}

	/**
	 * 轉換As400時間
	 * 
	 * @param time
	 *            時間格式
	 * @return BigDecimal
	 */
	private BigDecimal covertAs400Time(Timestamp time) {
		return new BigDecimal(addZeroWithValue(
				time.toString().replaceAll("\\D", ""), 17));
	}

	/**
	 * 字串後補0
	 * 
	 * @param str
	 * <br/>
	 * @param num
	 *            不足幾位補零 <br/>
	 * @return 補0後之字串 <br/>
	 * <AUTHOR> EX: addZeroWithValue("1",3) -> "100"
	 */
	private String addZeroWithValue(String str, int num) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0, len = num - Util.trim(str).length(); i < len; i++) {
			sb.append("0");
		}
		return str + sb.toString();
	}

}
