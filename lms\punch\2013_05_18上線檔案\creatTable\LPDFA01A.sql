---------------------------------------------------------
-- LMS.LPDFA01A 授信 PDF 舊案授權檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.LPDFA01A;
CREATE TABLE LMS.LPDFA01A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	PID           CHAR(32)     ,
	OWNUNIT       CHAR(3)      ,
	OW<PERSON><PERSON>         CHAR(6)      ,
	AUTHTI<PERSON>      TIMESTAMP    ,
	AUTHTYPE      CHAR(1)      ,
	AUTHUNIT      CHAR(3)      ,

	constraint P_LPDFA01A PRIMARY KEY(OID)
) IN EL_DATA_4KTS
  INDEX IN EL_INDEX_4KTS;
  
---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XLPDFA01A01;
CREATE INDEX LMS.XLPDFA01A01 ON LMS.LPDFA01A   (MAINID, OWNUNIT, AUTHTYPE, AUTHUNIT);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.LPDFA01A IS '授信 PDF 舊案授權檔';
COMMENT ON LMS.LPDFA01A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	PID           IS 'pid', 
	OWNUNIT       IS '授權單位', 
	OWNER         IS '授權人員', 
	AUTHTIME      IS '授權日期', 
	AUTHTYPE      IS '授權類別', 
	AUTHUNIT      IS '被授權單位'
);
