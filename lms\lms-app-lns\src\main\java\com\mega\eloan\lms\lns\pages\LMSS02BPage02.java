package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;

/**
 * <pre>
 * 借款人企金 - 分頁
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lmss02b02")
public class LMSS02BPage02 extends AbstractEloanForm {

	@Override
	public void afterExecute(ModelMap model, PageParameters parameters) {
		super.afterExecute(model, parameters);
		
	}

	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {

		renderJsI18N(LMSS02BPage.class);

	}// ;

    /*
     * (non-Javadoc)
     * 
     * @see com.mega.eloan.common.pages.AbstractEloanForm#getViewName()
     */
    @Override
    public String getViewName() {
        // 不要headerarea
        return "common/pages/None";
    }
	
}
