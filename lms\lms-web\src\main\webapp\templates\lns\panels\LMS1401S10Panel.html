<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="lms1405s10_panel">
		    <script type="text/javascript">
		   		loadScript('pagejs/lns/LMS1401S10Panel');
			</script>
			<form id="LMS1401S10Form01">
				<div class="content">
					<fieldset>
						<legend>
							<b><th:block th:text="#{'L120S24A.title'}">風險權數</th:block></b>
						</legend>
						<span class="color-red" id="L120S24AHint1Span" style="display:none;">
							目前資料為舊版風險權數，如需使用2025年新版風險權數，請先執行「引進額度明細表」
						</span>
						<div class="funcContainer">
							<button type="button" onclick="genL120S24A()"><th:block th:text="#{'button.L120S24A.import'}">引進額度明細表</th:block></button>
							<button type="button" onclick="writeBackToL140m01a_s24a()"><th:block th:text="#{'button.L120S24A.write'}">寫回額度明細表</th:block></button>
							<button type="button" class="forview" onclick="printPage_s24a()"><th:block th:text="#{'button.L120S24A.printPage'}">列印此頁</th:block></button>
							<button type="button" onclick="delete_s24a()"><th:block th:text="#{'button.L120S24A.delete'}">刪除</th:block></button>
							<button type="button" class="forview" onclick="printL120S24A_s24a()"><th:block th:text="#{'button.L120S24A.printL120S24A'}">列印試算明細</th:block></button>
							<div style="text-align:right;">
								<th:block th:text="#{'L120S24A.grid.unitThousand'}">單位:仟元</th:block>
							</div>
						</div>
			            <div id="l120s24aGridDiv" >
							<div id="l120s24aGrid" style="margin-left: 10px; margin-right: 10px"></div>
						</div>
					</fieldset>
				</div>
			</form>

			<div id="l120S24ADetailThickbox" style="display:none;">
			<!-- 放大表本人，預設他會有版本的機制  -->
			
			</div>
			
		</th:block>
	</body>
</html>