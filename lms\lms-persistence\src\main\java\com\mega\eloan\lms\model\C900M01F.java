/* 
 * C900M01F.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金授信科目限額檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C900M01F", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","lmtType","lmtSeq"}))
public class C900M01F extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 限額類型<p/>
	 * 1科子目限額<br/>
	 *  2科子目合併限額<br/>
	 *  3為科子目限額_BF
	 */
	@Size(max=1)
	@Column(name="LMTTYPE", length=1, columnDefinition="CHAR(1)")
	private String lmtType;

	/** 序號 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="LMTSEQ", columnDefinition="DECIMAL(5,0)")
	private Integer lmtSeq;
	
	/** 
	 * 會計科子細目<p/>
	 */
	@Size(max=8)
	@Column(name="SUBJCODE", length=8, columnDefinition="VARCHAR(8)")
	private String subjCode;

	/** 
	 * 授信科目(組合)<p/>
	 * xxx|xxx|xxx…
	 */
	@Size(max=300)
	@Column(name="SUBJECT", length=300, columnDefinition="VARCHAR(300)")
	private String subject;

	/** 限額－幣別 **/
	@Size(max=3)
	@Column(name="LMTCURR", length=3, columnDefinition="CHAR(3)")
	private String lmtCurr;

	/** 限額－金額 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="LMTAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal lmtAmt;

	/** 
	 * 科目順序<p/>
	 * LmtType為1時才會用到
	 */
	@Column(name = "SUBJSEQ", columnDefinition = "DECIMAL(3,0)")
	private Integer subjSeq;

	/** 
	 * 科目補充說明<p/>
	 * LmtType為1時才會用到
	 */
	@Size(max=200)
	@Column(name="SUBJDSCR", length=200, columnDefinition="VARCHAR(200)")
	private String subjDscr;

	/** 
	 * 清償期限－天數<p/>
	 * LmtType為1時才會用到<br/>
	 *  30、60、90、180、365
	 */
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="LMTDAYS", columnDefinition="DECIMAL(5,0)")
	private Integer lmtDays;

	/** 
	 * 清償期限－詳其他敘作條件<p/>
	 * LmtType為1時才會用到
	 */
	@Size(max=1)
	@Column(name="LMTOTHER", length=1, columnDefinition="CHAR(01)")
	private String lmtOther;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得限額類型<p/>
	 * 1科子目限額<br/>
	 *  2科子目合併限額<br/>
	 *  3為科子目限額_BF
	 */
	public String getLmtType() {
		return this.lmtType;
	}
	/**
	 *  設定限額類型<p/>
	 *  1科子目限額<br/>
	 *  2科子目合併限額<br/>
	 *  3為科子目限額_BF
	 **/
	public void setLmtType(String value) {
		this.lmtType = value;
	}

	/** 取得序號 **/
	public Integer getLmtSeq() {
		return this.lmtSeq;
	}
	/** 設定序號 **/
	public void setLmtSeq(Integer value) {
		this.lmtSeq = value;
	}
	
	/** 
	 * 設定會計科子細目<p/>
	 */
	public void setSubjCode(String value) {
		this.subjCode = value;
	}
	
	/** 
	 * 取得會計科子細目<p/>
	 */
	public String getSubjCode() {
		return this.subjCode;
	}

	/** 
	 * 取得授信科目(組合)<p/>
	 * xxx|xxx|xxx…
	 */
	public String getSubject() {
		return this.subject;
	}
	/**
	 *  設定授信科目(組合)<p/>
	 *  xxx|xxx|xxx…
	 **/
	public void setSubject(String value) {
		this.subject = value;
	}

	/** 取得限額－幣別 **/
	public String getLmtCurr() {
		return this.lmtCurr;
	}
	/** 設定限額－幣別 **/
	public void setLmtCurr(String value) {
		this.lmtCurr = value;
	}

	/** 取得限額－金額 **/
	public BigDecimal getLmtAmt() {
		return this.lmtAmt;
	}
	/** 設定限額－金額 **/
	public void setLmtAmt(BigDecimal value) {
		this.lmtAmt = value;
	}

	/** 
	 * 取得科目順序<p/>
	 * LmtType為1時才會用到
	 */
	public Integer getSubjSeq() {
		return this.subjSeq;
	}
	/**
	 *  設定科目順序<p/>
	 *  LmtType為1時才會用到
	 **/
	public void setSubjSeq(Integer value) {
		this.subjSeq = value;
	}

	/** 
	 * 取得科目補充說明<p/>
	 * LmtType為1時才會用到
	 */
	public String getSubjDscr() {
		return this.subjDscr;
	}
	/**
	 *  設定科目補充說明<p/>
	 *  LmtType為1時才會用到
	 **/
	public void setSubjDscr(String value) {
		this.subjDscr = value;
	}

	/** 
	 * 取得清償期限－天數<p/>
	 * LmtType為1時才會用到<br/>
	 *  30、60、90、180、365
	 */
	public Integer getLmtDays() {
		return this.lmtDays;
	}
	/**
	 *  設定清償期限－天數<p/>
	 *  LmtType為1時才會用到<br/>
	 *  30、60、90、180、365
	 **/
	public void setLmtDays(Integer value) {
		this.lmtDays = value;
	}

	/** 
	 * 取得清償期限－詳其他敘作條件<p/>
	 * LmtType為1時才會用到
	 */
	public String getLmtOther() {
		return this.lmtOther;
	}
	/**
	 *  設定清償期限－詳其他敘作條件<p/>
	 *  LmtType為1時才會用到
	 **/
	public void setLmtOther(String value) {
		this.lmtOther = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
