package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;

@Controller
@RequestMapping(path = "/fms/cls9041m04/{page}")
public class CLS9041M04Page extends AbstractEloanForm {

	public CLS9041M04Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
//		add(new Label("_buttonPanel"));
		renderJsI18N(CLS9041M04Page.class);
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
}
