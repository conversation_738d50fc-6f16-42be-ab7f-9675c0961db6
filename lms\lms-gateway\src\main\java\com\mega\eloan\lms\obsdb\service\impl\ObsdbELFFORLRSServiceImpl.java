/* 
 *ObsdbELF383ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbELFFORLRSService;

/**
 * <pre>
 * 擔保品額度ELF346
 * </pre>
 * 
 * @since 2012/10/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/30,ICE,new
 *          </ul>
 */
@Service
public class ObsdbELFFORLRSServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELFFORLRSService {

	@Override
	public List<Map<String, Object>> listAMTDataByCustIdDupNoBranchCntrNo(
			String branchId, String cntrNo) {
		Object[] obj = {branchId,cntrNo};
		return this.getJdbc(branchId).queryForList("ELF345AAND346.listAMTData", obj);
	}

	@Override
	public List<Map<String, Object>> listColDataByCustIdDupNoCollNoBranch(
			String custId,String dupNo,String collNo,String branchId) {
		Object[] obj = {custId,dupNo,collNo,branchId};
		return this.getJdbc(branchId).queryForList("ELF349.listCOLData", obj);
	}

	@Override
	public List<Map<String, Object>> listELF347Data(
			String branchId,String typCd,String custId, String dupNo , String collNo) {
		Object[] obj = {typCd,branchId,custId,dupNo,collNo};
		return this.getJdbc(branchId).queryForList("ELF.listELF347Data", obj);
	}

	@Override
	public List<Map<String, Object>> list349ColDataByCustIdDupNoCollNoBranch(
			String custId,String dupNo,String collNo,String branchId) {
		Object[] obj = {custId,dupNo,collNo,branchId,custId,dupNo,collNo,branchId};
		return this.getJdbc(branchId).queryForList("ELF348AAND349.listCOLData", obj);
	}

	@Override
	public List<Map<String, Object>> listCMSDataByCondition(String branchId,String selectField,String table,String condition,
															String orderBy, String tCustID, String tDupNo, String tCollno) {
		Object[] obj = {branchId,tCustID,tDupNo,tCollno};
		return this.getJdbc(branchId).queryForListByCustParam("ELF.listCMSData", new Object[]{selectField,table,condition,orderBy},obj);
	}

}
