/* 
 * DW_RKPROJECT.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;


import java.util.Date;
import javax.persistence.*;

import tw.com.iisi.cap.model.GenericBean;


/** 房貸敘做方案 **/
public class DW_RKPROJECT extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="BR_CD", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String br_cd;

	/** NOTES文件編號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="NOTEID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String noteid;

	/** 
	 * 客戶統一編號<p/>
	 * 客戶統編是主要借款人、共借人或聯保人可以評等的範圍
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String custid;

	/** 重複序號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String dupno;

	/** 評等模型類別 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWTYPE", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String mowtype;

	/** 模型版本-大版 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWVER1", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer mowver1;

	/** 模型版本-小版 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWVER2", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer mowver2;

	/** JCIC查詢日期 YYYY-MM-DD **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Temporal(TemporalType.DATE)
	@Column(name="JCIC_DATE", columnDefinition="DATE", nullable=false,unique = true)
	private Date jcic_date;

	/**  **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="ACCT_KEY", length=14, columnDefinition="CHAR(14)", nullable=false,unique = true)
	private String acct_key;

	
	/** 主借款人統一編號(CUSTKEY) **/
	@Column(name="CUST_KEY", length=10, columnDefinition="CHAR(10)")
	private String cust_key;

	/** 
	 * 相關身分 ( LNGEFLAG)<p/>
	 * M: 主借款人  C: 共同借款人  G: 連帶保證人
	 */
	@Column(name="LNGEFLAG", length=1, columnDefinition="CHAR(1)")
	private String lngeflag;

	/** 
	 * 敘做方案<p/>
	 * 方案(特A、A、B、C、D、E)
	 */
	@Column(name="CONTINUE_CD", length=2, columnDefinition="CHAR(2)")
	private String continue_cd;

	/** 
	 * 案件審核層級<p/>
	 * 比照dw_pquotapp  授權等級：1常董會權限2常董會權限簽奉總經理核批3常董會權限簽准由副總經理核批4利費率變更案件由總處處長核定5屬常董會授權總經理逕核案件6總經理權限內7副總經理權限8授管處協理權限9其他A董事會權限B區域授信中心主任/副主任權限C利費率變更案件由董事長核定
	 */
	@Column(name="CASE_LEVEL", length=1, columnDefinition="CHAR(1)")
	private String case_level;

	/** 
	 * 報送授權外審核之主要理由<p/>
	 * 1.利率2.額度3.成數4.年限5.連保人6.其他       (請限制單選)
	 */
	@Column(name="REASON_FLAG", length=2, columnDefinition="CHAR(2)")
	private String reason_flag;

	/** 
	 * 總處或區域中心是否批覆核准<p/>
	 * Y/N
	 */
	@Column(name="AUTH_FLAG", length=1, columnDefinition="CHAR(1)")
	private String auth_flag;

	/** 
	 * 總處或區域中心批覆婉卻原因<p/>
	 * 若批覆婉卻-> 婉卻原因(代碼)
	 */
	@Column(name="REJECT_FLAG", length=2, columnDefinition="CHAR(2)")
	private String reject_flag;

	/** 
	 * 總處或區域中心  批覆核准修改核准額度<p/>
	 * 若批覆核准->是否修改核准額度(Y/N)
	 */
	@Column(name="UPDATE_AMT_FLAG", length=1, columnDefinition="CHAR(1)")
	private String update_amt_flag;

	/** 
	 * 總處或區域中心  批覆核准修改利率<p/>
	 * 若批覆核准->是否修改利率(Y/N )
	 */
	@Column(name="UPDATE_RATE_FLAG", length=1, columnDefinition="CHAR(1)")
	private String update_rate_flag;

	/** 覆核日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CHKDATE", columnDefinition="DATE")
	private Date chkdate;

	/** 
	 * 文件狀態<p/>
	 * 編製中|1待覆核|2待母行覆核 | 2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件 | 7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 */
	@Column(name="DOCSTATUS", length=2, columnDefinition="CHAR(2)")
	private String docstatus;

	/** 上傳資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DATA_SRC_DT", columnDefinition="DATE")
	private Date data_src_dt;

	/** 卡友貸旗標 */
	@Column(name="C_FLAG", length=1, columnDefinition="CHAR(1)")
	private String c_flag;
	
	/** 取得分行別 **/
	public String getBr_cd() {
		return this.br_cd;
	}
	/** 設定分行別 **/
	public void setBr_cd(String value) {
		this.br_cd = value;
	}

	/** 取得NOTES文件編號 **/
	public String getNoteid() {
		return this.noteid;
	}
	/** 設定NOTES文件編號 **/
	public void setNoteid(String value) {
		this.noteid = value;
	}

	/** 
	 * 取得客戶統一編號<p/>
	 * 客戶統編是主要借款人、共借人或聯保人可以評等的範圍
	 */
	public String getCustid() {
		return this.custid;
	}
	/**
	 *  設定客戶統一編號<p/>
	 *  客戶統編是主要借款人、共借人或聯保人可以評等的範圍
	 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得評等模型類別 **/
	public String getMowtype() {
		return this.mowtype;
	}
	/** 設定評等模型類別 **/
	public void setMowtype(String value) {
		this.mowtype = value;
	}

	/** 取得模型版本-大版 **/
	public Integer getMowver1() {
		return this.mowver1;
	}
	/** 設定模型版本-大版 **/
	public void setMowver1(Integer value) {
		this.mowver1 = value;
	}

	/** 取得模型版本-小版 **/
	public Integer getMowver2() {
		return this.mowver2;
	}
	/** 設定模型版本-小版 **/
	public void setMowver2(Integer value) {
		this.mowver2 = value;
	}

	/** 取得JCIC查詢日期 YYYY-MM-DD **/
	public Date getJcic_date() {
		return this.jcic_date;
	}
	/** 設定JCIC查詢日期 YYYY-MM-DD **/
	public void setJcic_date(Date value) {
		this.jcic_date = value;
	}

	/** 取得主借款人統一編號(CUSTKEY) **/
	public String getCust_key() {
		return this.cust_key;
	}
	/** 設定主借款人統一編號(CUSTKEY) **/
	public void setCust_key(String value) {
		this.cust_key = value;
	}

	/** 
	 * 取得相關身分 ( LNGEFLAG)<p/>
	 * M: 主借款人  C: 共同借款人  G: 連帶保證人
	 */
	public String getLngeflag() {
		return this.lngeflag;
	}
	/**
	 *  設定相關身分 ( LNGEFLAG)<p/>
	 *  M: 主借款人  C: 共同借款人  G: 連帶保證人
	 **/
	public void setLngeflag(String value) {
		this.lngeflag = value;
	}

	/** 
	 * 取得敘做方案<p/>
	 * 方案(特A、A、B、C、D、E)
	 */
	public String getContinue_cd() {
		return this.continue_cd;
	}
	/**
	 *  設定敘做方案<p/>
	 *  方案(特A、A、B、C、D、E)
	 **/
	public void setContinue_cd(String value) {
		this.continue_cd = value;
	}

	/** 
	 * 取得案件審核層級<p/>
	 * 比照dw_pquotapp  授權等級：1常董會權限2常董會權限簽奉總經理核批3常董會權限簽准由副總經理核批4利費率變更案件由總處處長核定5屬常董會授權總經理逕核案件6總經理權限內7副總經理權限8授管處協理權限9其他A董事會權限B區域授信中心主任/副主任權限C利費率變更案件由董事長核定
	 */
	public String getCase_level() {
		return this.case_level;
	}
	/**
	 *  設定案件審核層級<p/>
	 *  比照dw_pquotapp  授權等級：1常董會權限2常董會權限簽奉總經理核批3常董會權限簽准由副總經理核批4利費率變更案件由總處處長核定5屬常董會授權總經理逕核案件6總經理權限內7副總經理權限8授管處協理權限9其他A董事會權限B區域授信中心主任/副主任權限C利費率變更案件由董事長核定
	 **/
	public void setCase_level(String value) {
		this.case_level = value;
	}

	/** 
	 * 取得報送授權外審核之主要理由<p/>
	 * 1.利率2.額度3.成數4.年限5.連保人6.其他       (請限制單選)
	 */
	public String getReason_flag() {
		return this.reason_flag;
	}
	/**
	 *  設定報送授權外審核之主要理由<p/>
	 *  1.利率2.額度3.成數4.年限5.連保人6.其他       (請限制單選)
	 **/
	public void setReason_flag(String value) {
		this.reason_flag = value;
	}

	/** 
	 * 取得總處或區域中心是否批覆核准<p/>
	 * Y/N
	 */
	public String getAuth_flag() {
		return this.auth_flag;
	}
	/**
	 *  設定總處或區域中心是否批覆核准<p/>
	 *  Y/N
	 **/
	public void setAuth_flag(String value) {
		this.auth_flag = value;
	}

	/** 
	 * 取得總處或區域中心批覆婉卻原因<p/>
	 * 若批覆婉卻-> 婉卻原因(代碼)
	 */
	public String getReject_flag() {
		return this.reject_flag;
	}
	/**
	 *  設定總處或區域中心批覆婉卻原因<p/>
	 *  若批覆婉卻-> 婉卻原因(代碼)
	 **/
	public void setReject_flag(String value) {
		this.reject_flag = value;
	}

	/** 
	 * 取得總處或區域中心  批覆核准修改核准額度<p/>
	 * 若批覆核准->是否修改核准額度(Y/N)
	 */
	public String getUpdate_amt_flag() {
		return this.update_amt_flag;
	}
	/**
	 *  設定總處或區域中心  批覆核准修改核准額度<p/>
	 *  若批覆核准->是否修改核准額度(Y/N)
	 **/
	public void setUpdate_amt_flag(String value) {
		this.update_amt_flag = value;
	}

	/** 
	 * 取得總處或區域中心  批覆核准修改利率<p/>
	 * 若批覆核准->是否修改利率(Y/N )
	 */
	public String getUpdate_rate_flag() {
		return this.update_rate_flag;
	}
	/**
	 *  設定總處或區域中心  批覆核准修改利率<p/>
	 *  若批覆核准->是否修改利率(Y/N )
	 **/
	public void setUpdate_rate_flag(String value) {
		this.update_rate_flag = value;
	}

	/** 取得覆核日期 **/
	public Date getChkdate() {
		return this.chkdate;
	}
	/** 設定覆核日期 **/
	public void setChkdate(Date value) {
		this.chkdate = value;
	}

	/** 
	 * 取得文件狀態<p/>
	 * 編製中|1待覆核|2待母行覆核 | 2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件 | 7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 */
	public String getDocstatus() {
		return this.docstatus;
	}
	/**
	 *  設定文件狀態<p/>
	 *  編製中|1待覆核|2待母行覆核 | 2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件 | 7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 **/
	public void setDocstatus(String value) {
		this.docstatus = value;
	}

	/** 取得上傳資料日期 **/
	public Date getData_src_dt() {
		return this.data_src_dt;
	}
	/** 設定上傳資料日期 **/
	public void setData_src_dt(Date value) {
		this.data_src_dt = value;
	}

	/** 取得 **/
	public String getAcct_key() {
		return this.acct_key;
	}

	/** 設定 **/
	public void setAcct_key(String value) {
		this.acct_key = value;
	}

	/** 取得卡友貸旗標 */
	public String getC_flag() {
		return c_flag;
	}
	/** 設定卡友貸旗標 */
	public void setC_flag(String c_flag) {
		this.c_flag = c_flag;
	}
}
