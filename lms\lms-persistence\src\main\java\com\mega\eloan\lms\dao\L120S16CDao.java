/* 
 * L120S16CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S16C;

/** 授信額度異動情形 **/
public interface L120S16CDao extends IGenericDao<L120S16C> {

	L120S16C findByOid(String oid);
	
	List<L120S16C> findByMainId(String mainId);
	
	L120S16C findByUniqueKey(String mainId, String custId, String dupNo);

	List<L120S16C> findByIndex01(String mainId, String custId, String dupNo);

	List<L120S16C> findByIndex02(String mainId);
}