package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 額度資訊檔 QUOTAINF(MIS.ELV42201)
 * </pre>
 * 
 * @since 2011/10/31
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/31 EL07625 new
 *          </ul>
 */

public interface MisQuotainfService {
	/**
	 * 查詢
	 * 
	 * @param custId
	 *            客戶編號
	 * @param dupNo
	 *            重複序號
	 * @param branch
	 *            分行別
	 * @param cntrNo
	 *            額度序號
	 * @return Map<String, Object>
	 */
	Map<String, Object> getByKey(String custId, String dupNo, String branch,
			String cntrNo);

	/**
	 * 刪除
	 * 
	 * @param custId
	 *            客戶編號
	 * @param dupNo
	 *            重複序號
	 * @param branch
	 *            分行別
	 * @param cntrNo
	 *            額度序號
	 * 
	 */
	void delByKey(String custId, String dupNo, String branch, String cntrNo);

	/**
	 * 新增
	 * 
	 * @param dataList
	 *            <pre>
	 *            Object[]
	 *  custId   客戶編號
	 *  dupNo 重複序號
	 *  branch  分行別
	 *  cntrNo  額度序號
	 *  appDate  申請日
	 *  aprDate  核准日
	 *  custName  主要借人姓名
	 *  bossId 核准主管代號
	 *  bossName  核准主管姓名
	 *  unId  簽報書mainId
	 *  newCase   若為新貸案件則為Y
	 *  updater 上傳者
	 * </pre>
	 */
	void insert(List<Object[]> dataList);
}
