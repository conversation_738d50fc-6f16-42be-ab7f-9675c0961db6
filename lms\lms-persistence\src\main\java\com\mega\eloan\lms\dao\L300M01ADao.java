/* 
 * L300M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L300M01A;

/** 覆審考核表主檔 **/
public interface L300M01ADao extends IGenericDao<L300M01A> {

	L300M01A findByOid(String oid);
	
	L300M01A findByMainId(String mainId);
	
	List<L300M01A> findByDocStatus(String docStatus);

	List<L300M01A> findByIndex01(String ownBrId, String branchId, Date bgnDate, Date endDate);

	List<L300M01A> findByIndex02(String mainId);
	
	List<L300M01A> findByIndex03(String ownBrId, String docStatus, Date bgnDate, Date endDate, String produceFlag);
	
	/**
	 * J-112-0461 授信覆審考核表-編製中-下拉選項統計由每半年改為每季統計
	 * @param ownBrId
	 * @param branchId
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<L300M01A> findL300m01a(String ownBrId, String branchId, Date bgnDate, Date endDate);
}