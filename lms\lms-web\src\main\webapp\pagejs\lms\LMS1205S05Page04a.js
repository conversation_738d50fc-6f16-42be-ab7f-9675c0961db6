$(function(){	
	var tabForm = $("#tabForm");
	tabForm.reset();
	tabForm.find("input[name=toM5]").click(function(){ //填列對象
		var table1 = tabForm.find("#table1");
		($(this).val()=='1') ? table1.find(".sA1radio1-3,.sA1radio1-2").hide() : ($(this).val()=='2') ? table1.find(".sA1radio1-2").show().siblings(".sA1radio1-3").hide() : ($(this).val()=='3') ? table1.find(".sA1radio1-3").show() : null;
	}).end().find("input[name=editMode3]").click(function(){ //填列方式				
		tabForm.find(".sA1radio2-" + $(this).val()).show().siblings("[class^=sA1radio2]").hide();
		($(this).val() == '4') && gridDoc.trigger('reloadGrid') ;
	}).end().find("input[name=IsSelectOtherFin]").click(function(){ //是否要選擇其他( 預估) 財務報表
		tabForm.find("#IsSelectOtherFin1")[($(this).val()=='1')?"show":"hide"]();
		($(this).val()=='Y') && otherFssGrid.trigger('reloadGrid');
	}).end().find("#ch10Se4Select").click(function(){	 //勾選填列敏感性分析
		tabForm.find("#div_ch10Se4Select1")[$(this).is(":checked") ?  "show" : "hide"]();
	});
	
	$(".sA1radio2-1").tabs({
		select:function(event, ui){
    		return $("#tabForm").valid();
    	}
	});

	var sA1r1t1 = tabForm.find("#sA1r1t1"); /*資金來源與運用--------------------*/
	sA1r1t1.find("#sA1r1t1btnCal").click(function(){	//計算
		var ttlScore1,ttlScore2; 				
		ttlScore1=calr1t1_1(sA1r1t1.find("input[id^=cr][id$=_2]")); 
		ttlScore2=calr1t1_1(sA1r1t1.find("input[id^=cu][id$=_2]"));            		       	

		for(var i=1;i<=5;i++){
			sA1r1t1.find("span[id=cr" + i +"_3]").val(calr1t1_2(sA1r1t1.find("input[id=cr" + i +"_2]"),ttlScore1));
			sA1r1t1.find("span[id=cu" + i +"_3]").val(calr1t1_2(sA1r1t1.find("input[id=cu" + i +"_2]"),ttlScore2));
		}		
		if(ttlScore1 != ttlScore2){
			//ces1401.msg006=「資金來源金額合計」不等於「資金運用金額合計」，請檢查資料內容是否正確！
			API.showPopMessage(i18n.lms1205s05["ces1401.msg006"]);
		}else{
			sA1r1t1.find("#cr_tot").val(ttlScore1).end().find("#cu_tot").val(ttlScore2);
		}
	});
	var calr1t1_1 = function(inputs){
		var s = 0;
		inputs.each(function() {  
    	  s += parseInt(($(this).val() ? $(this).val() : 0), 10);
    	}); 
		return s;
	}
	var calr1t1_2 = function(input,score){
		if (input.val()){
			rate1=Math.floor((input.val()/score*100)*100)/100;
			return rate1;	
		}else{
			return "0";
		}
	}
	var sA1r1t3 = tabForm.find("#sA1r1t3");/*基本案財務預估結果--------------*/
	sA1r1t3.find("#sA1r1t3btnSel1").click(function(){ //選擇預估財務報表
		showFss({
			maxSelect : 15,
			formAction:"sA1ImportFss1",
			formId:"#sA1r1t3g1",
			gaapFlag:"0"
		});
	});
	sA1r1t3.find("#sA1r1t3btnSel2").click(function(){ //選擇預估財務報表
		showFss({
			maxSelect : 15,
			formAction:"sA1ImportFss2",
			formId:"#sA1r1t3g1",
			gaapFlag:"1"
		});
	});
	//J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
	sA1r1t3.find("#sA1r1t3btnSel3").click(function(){ //選擇預估財務報表
		showFss({
			maxSelect : 15,
			formAction:"sA1ImportFss3",
			formId:"#sA1r1t3g1",
			gaapFlag:"2"
		});
	});
/*
	.end().find("#sA1r1t3btnSel2").click(function(){ //選擇其他（預估）財務報表
		showFss({
			requiedInput:true,
			maxSelect : 15,
			action:"sA1ImportFssOther",
			data : {subtab:"13o",fssType:"2"},
			success : function(){
				otherFssGrid.trigger('reloadGrid');
			}
		});
	});
*/

	//財報Gird欄位
	var myColModel = [{
	    colHeader: i18n.lms1205s05['ces1401.0202'],// "ces1401.0202=申貸戶統編",
	    name: 'custId',align: 'center', width: 15, sortable: false
	}, {
	    colHeader: i18n.lms1205s05['ces1401.0204'],// "ces1401.0204=申貸戶名稱",
	    name: 'custName',width: 22,sortable: false
	}, {
	    colHeader: i18n.lms1205s05['fss.periodate'],// 報表期間"
	    name: 'periodDate',align: 'center',width: 22,sortable: false
	}, {
	    colHeader: i18n.lms1205s05['fss.conso'],// "合併報表",
	    name: 'conso',align: 'center',width: 5,sortable: false
	}, {
	    colHeader: i18n.lms1205s05['fss.source'],// "財報資料來源",
	    name: 'source',align: 'center',width: 20,sortable: false
	}, {
	    colHeader: i18n.def['lastModifyTime'],// "最後更新日期",
	    name: 'updateTime',align: 'center',width:18,sortable: false
	}, {
	    colHeader: 'mainId',name: 'mainId',hidden: true
	}];
	
	var showFss = function(fssj){ //引進財報
		var _data = {page:responseJSON.page};
		if (tabForm.find("input[name=toM5]:checked").val()!=1){
			_data = $.extend(_data,{
				fssCustId : tabForm.find("#comId1").val(),
				fssDupNo:'0'
			});
		}	
		MegaApi.showFssDialog({
			title:i18n.lms1205s05['ces1401.A150'],
			subTitle: i18n.lms1205s05['ces1401.A151'],			
			maxSelect: fssj.maxSelect,
            requiedInput: fssj.requiedInput || false,
            handler: 'lms1205gridhandler',
            action: 'queryIncludeFSS',
            data:$.extend({
            	fssCustId:$("#custId").val(),
            	fssDupNo:'0',
            	mainId:responseJSON.mainId,
            	gaapFlag:fssj.gaapFlag
            }, _data , fssj.data || {}),
            type:"2", //2:預估財報
            colModel: myColModel,
            sortname: 'eDate',
            sortorder: 'desc',
            btnAction: function(grid, rowDatas){// 多選…
                var mainIdAry = [],conso;
                
                for (var br in rowDatas) {
                	//J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
                	if("1" == fssj.gaapFlag || "2" == fssj.gaapFlag){
                		if (conso && conso != rowDatas[br].conso) {
                            // ces3401.msg1=IFRS需選擇相同合併報表別的財報
                            API.showPopMessage('IFRS需選擇相同合併報表別的財報');
                            return;
                        }
                        else {
                            conso = rowDatas[br].conso;
                        }
                    }
                    mainIdAry.push(rowDatas[br].mainId);
                }
                $.ajax({
		            handler: 'lms1205formhandler', 
		            action: fssj.formAction,//"sA1ImportFss1",
		            data: $.extend(fssj.data,{
		            	lmsMainId : responseJSON.mainId,
		            	mainId : tabForm.find("#mainId").val(),
		            	uid : tabForm.find("#uid").val(),
		            	fssMainIdAry: mainIdAry
                    })
		        }).done(function(json){
		        	fssj.formId && tabForm.find(fssj.formId+",#sA1r1t4").find(".fss").val("").end().injectData(json);
		        	fssj.success && fssj.success.call();
		        });
                $.thickbox.close();
            }
        });
	}//;

/*
	var otherFssGrid = sA1r1t3.find("#sA1r1t3grid2").iGrid({ //其它預估財務報表列表
		height:150,needPager:false,sortable:false,localFirst:true,
	    handler: 'ces1400gridhandler', action: 'sA1ViewOtherFss',
	    postData: {
	    	tab:'A1',mainId: tabForm.find("#mainId").val(),fssType:"2",subtab:"13o"
	    },	
		colModel : [ {
			colHeader : i18n.def.compID, name:'custId',sortable:false
		}, {
			colHeader : i18n.ces1401m00['ces1401.A1G001'],name:'custName',sortable:false
        }, {
        	colHeader : i18n.def['fss.periodate'], name:'fssDate',sortable:false,align:'center'
        }, {
        	colHeader : i18n.def['fss.conso'], name:'fssConso',sortable:false,align:'center'
        }]
	});
*/

	var sA1r4 = tabForm.find("#sA1r4"); /*簡要式財務評估--------------------*/
	var gridDoc = sA1r4.find("#sA1r4grid").iGrid({
		height : 30,needPager:false,localFirst:true,
	    handler: 'lms1205gridhandler', action: 'queryDocView',
	    postData: {
	    	mainId: $("#mainId").val(), fieldId: "attachFinFile"
	    },	
		colModel : [ {
			colHeader : i18n.def.attachfile, name : 'srcFileName',formatter:'click',onclick: downloadFile
		}, {
            name: 'oid', hidden: true
        }, {
            name: 'totPages', hidden: true
        }],
        loadComplete: function(){
        	var numberOfRecords = $(this).jqGrid('getGridParam', 'records') ;
            if(numberOfRecords>0){
            	sA1r4.find("#oidA1").val($(this).jqGrid('getCell', 1, 'oid'));
				//#357 totPagese改存json
            	sA1r4.find("#totPages").val($(this).jqGrid('getCell', 1, 'totPages'));
            }		        	  	
        }       
	});

	sA1r4.find("#sA1r4btnSel").click(function(){	//選擇附加檔案
		API.uploadFileNoMulti({
			grid: gridDoc,
			oid: "oidA1",
			fieldId: "attachFinFile"
			,totPage:$("#totPages").val()
		});
	}).end().find("#sA1r4btnDel").click(function(){//刪除
		API.deleteFile({
			grid: gridDoc
		});
	});

	$("input[name='rkind']").click(function(){
	    if($(this).val() == "1"){
	    	$("#hiderow").show();
	    }else{
	    	$("#hiderow").hide();
	    }
	});
	
	var MegaApi = $.extend(API, {
	    ajaxErrorMessage: ilog.errorMessage,
	    ajaxNotifyMessage: ilog.message,
	    showMessage: ilog.message,
	    /**
	     * create default dialog button
	     * @param {Function} fn
	     */
	    createDefDialogButton: function(fn){
	        return API.createJSON([{
	            key: i18n.def.sure,
	            value: function(){
	                fn && fn.apply(this, arguments);
	            }
	        }, {
	            key: i18n.def.cancel,
	            value: function(){
	                $.thickbox.close();
	            }
	        }]);
	    },
	    /**
	     * 選擇要引入、列印的財報。
	     * @param {Object} settings
	     */
	    showFssDialog: function(settings){
	        var s = $.extend({
	            title: i18n.def['showIncludeFss.title'], // 財務報表列印選擇
	            caption: i18n.lms1205s05('showIncludeFss.caption', {
	                '0': settings.maxSelect || 4
	            }), // 請選擇欲引進之財務報表(最多可選${0}份)
	            captionTail: i18n.lms1205s05['showIncludeFss.captionTail'], // 份)
	            maxSelect: 4, // 最多選取筆數
	            type: '', // 1 一般 2 預估
	            requiedInput: true, // 查詢時需要輸入統一編號
	            defaultValue: '', // 預設輸入統編
	            btnAction: function(){
	            }, // do when confirm button click.
	            cancelAction: function(){
	            }, // do when cancel button click.
	            loadComplete: function(){
	            }, // do when fss grid load complete.
	            colModel: [], // fss grid column model.
	            fssDialogHtml: "<span id='subTitle'></span><br/><input type='text' id='fssCustId' name='fssCustId' size='12' maxlength='10' class='upText required' />", // setting input dialog.
	            width: 250,
	            height: 100
	        }, settings || {});
	        
	        var _idd = $("#include_fss_dialog"), _gid = $("#query_fss_dialog");
	        if (_idd.length) {
	            _idd.empty().remove();
	        }
	        _idd = $("<div id='include_fss_dialog' class='popup_cont' style='display:none'><form id='print_form' onsubmit='return false;'>" + s.fssDialogHtml + "</form>").appendTo("body");
	        _idd.find("#subTitle").text(s.subTitle || i18n.def['showIncludeFss.inputCustId']);//.end().find("#fssCustId").val(s.defaultValue || "");
	        _idd.find("#fssCustId").val(s.defaultValue || "");
	        if (_gid.length) {
	            _gid.empty().remove();
	        }
	        pageInit.call($("#print_form"));
	        if (s.requiedInput) {
	            _idd.thickbox({
	                title: s.title,
	                width: s.width,
	                height: s.height,
	                align: 'center',
	                valign: 'bottom',
	                buttons: MegaApi.createDefDialogButton(function(){
	                    // open choose fss grid.
	                    var $this = this;
	                    var _form = $this.find("form");
	                    if (_form.valid()) {
	                        // close input custId dialog.
	                        $.thickbox.close();
	                        
	                        MegaApi.includeGrid($.extend({
	                            handler: s.handler,
	                            action: s.action,
	                            btnAction: s.btnAction,
	                            cancelAction: s.cancelAction,
	                            id: 'query_fss_dialog',
	                            title: s.title,
	                            subtitle: s.caption,
	                            multiselect: s.maxSelect > 1 ? true : false,
	                            freezeWidth: 750,
	                            data: $.extend(s.data || {}, {
	                                fssCustId: _form.find("#fssCustId").val(),
	                                type: s.type
	                            }),
	                            colModel: s.colModel,
	                            buttons: API.createJSON([{
	                                key: i18n.def.sure,
	                                value: function(grid){
	                                    var ret = grid.getSelRowDatas();
	                                    if (ret) {
	                                        if (s.maxSelect && ret.length > s.maxSelect) {
	                                            API.showErrorMessage(i18n.lms1205s05['showIncludeFss.maxRows']); // 超過一般財務報表最多可選筆數，請重新選擇
	                                        }
	                                        else {
	                                            s.btnAction.call(this, grid, $.isArray(ret) ? ret : [ret]);
	                                        }
	                                    }
	                                    else {
	                                        API.showErrorMessage(i18n.def['grid_selector']); // 無選擇
	                                    }
	                                }
	                            }, {
	                                key: i18n.def.cancel,
	                                value: function(){
	                                    $.thickbox.close();
	                                    s.cancelAction.call();
	                                }
	                            }]),
	                            open: function(){
	                                this.find("button").button().addClass("forview");
	                            },
	                            loadComplete: s.loadComplete
	                        }, s));
	                        
	                        // clear input.
	                        _form.find("#fssCustId").val('');
	                    }
	                }),
	                open: function(){
	                }
	            });
	        }
	        else {
	            // 不需要輸入統一編號的對話框。
	            MegaApi.includeGrid($.extend({
	                handler: s.handler,
	                action: s.action,
	                btnAction: s.btnAction,
	                id: 'query_fss_dialog',
	                title: s.title,
	                subtitle: s.caption,
	                multiselect: s.maxSelect > 1 ? true : false,
	                freezeWidth: 750,
	                data: $.extend(s.data || {}, {
	                    type: s.type
	                }),
	                colModel: s.colModel,
	                buttons: API.createJSON([{
	                    key: i18n.def.sure,
	                    value: function(grid){
	                        var ret = grid.getSelRowDatas();
	                        if (ret) {
	                            if (s.maxSelect && ret.length > s.maxSelect) {
	                                API.showErrorMessage(i18n.lms1205s05['showIncludeFss.maxRows']); // 超過一般財務報表最多可選筆數，請重新選擇
	                            }
	                            else {
	                                s.btnAction.call(this, grid, ret);
	                            }
	                        }
	                        else {
	                            API.showErrorMessage(i18n.def['grid_selector']); // 無選擇
	                        }
	                    }
	                }, {
	                    key: i18n.def.cancel,
	                    value: function(){
	                        $.thickbox.close();
	                    }
	                }]),
	                loadComplete: s.loadComplete
	            }, s));
	        }
	    }
	});
	
	$.extend(API, {				
		uploadFileNoMulti : function(settings){
			var _totPage = settings.totPage || "";
			var s = $.extend({
				grid: "",
				oid: "",
				fieldId: "",
				mainOid: "mainOid",
				mainId: "mainId",
				totPage : _totPage,
		        btnAction: function(){ // 計算後動作
	            }
		    }, settings || {});
			var grid = s['grid'];
			var initdfd = $.Deferred();
			var numberOfRecords = grid.getGridParam("records");
            if(numberOfRecords>0){
            	API.confirmMessage(i18n.msg('EFD2061'),function(result){
    				if(result){
    					$.ajax({
    						handler: "lms1205formhandler",
       			            action: "deleteFlag",
       			            data: {
       			           		oid: $("#" + s['oid']).val() }
       			            }).done(function(){
       			            	grid.trigger("reloadGrid");
       			            	initdfd.resolve();
    		            });
    				}else{
    					return;
    				}
            	});
            }else{
            	initdfd.resolve();
            }
            initdfd.done(function(){
            	MegaApi.uploadDialog({
            		handler: "lms1201fileuploadhandler",
		            fieldId: s.fieldId,
		            title: i18n && i18n.def.insertfile || "請選擇附加檔案",
		            subTitle:i18n.def('insertfileSize',{'fileSize':'1'}),
		            fileCheck: false,
		            successMsg: false,
					height:140,
		            limitSize:1048576,//1*1024*1024
		            data: {
		                mainOid: $("#" + s['mainOid']).val(),
		                mainId: $("#" + s['mainId']).val(),
						totPage:_totPage }
		            }).done(function(){
		            	  grid.trigger('reloadGrid');
		            	  s.btnAction && s.btnAction.apply(this, []);
		        });
            });
		},
		
		deleteFile : function(settings){
			var s = $.extend({
				grid: "",
		        btnAction: function(){ // 計算後動作
	            }
		    }, settings || {});
			
			var grid = s['grid'];
			var selrow = grid.getGridParam('selrow');
			if (selrow) {		
				 var ret = grid.getRowData(selrow);
		    	 API.flowConfirmAction({
		             message: i18n.def['confirmDelete'],
		             handler: "lms1205formhandler",
		             action: "deleteFlag",			       
		             data: {
		            	 oid: ret.oid	}            
		             }.done(function(){
		                 API.showPopMessage(i18n.def['confirmDeleteSuccess']);
		                 grid.trigger("reloadGrid");
		                 s.btnAction && s.btnAction.apply(this, []);
		         });	
			} else {
				API.showErrorMessage(i18n.def.action_002);
			}
		}
	});		
});

function downloadFile(cellvalue, options, rowObject){
    $.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:rowObject.oid
        }
    });
}

function thickboxCes10(list) {
	 
	 $("#thickboxCes10").thickbox({     // 使用選取的內容進行彈窗
		   title : i18n.lms1205s05["l120s05.thickbox11"],
		   width : 960,
		   height : 480,
		   modal : true,
		   i18n:i18n.def,
		   buttons: {
		             "saveData": function() {
		            	 	if(!$("#tabForm").valid()){
		            	 		return false;
		            	 	}
		            	 	//儲存
		            	 	//$.thickbox.close();							
		            	 	$.ajax({									
								handler : "lms1205formhandler",
								type : "POST",
								dataType : "json",
								data : 
								$.extend({
									formAction : "save",
									page : "A1",
									mainId : responseJSON.mainId,
									cesOid : list.oid,
									cesMainId : list.mainId
								},$("#tabForm").serializeData())
								}).done(function(json) {
							});
		             },
		             "del": function() {
		 				CommonAPI.confirmMessage(i18n.lms1205s05["l120s05.confirm1"],function(b){
							if(b){
								//是的function
								var editMode3 = $("input[name='editMode3']:radio:checked" ).val();
								if(editMode3 == '4'  ){
								   //簡要是評估刪除的時候也要一併刪除附件
								   var delGrid = $("#tabForm").find("#sA1r4").find("#sA1r4grid");
								   var numberOfRecords = delGrid.getGridParam("records");
								   if(numberOfRecords>0){
						        	   var delOid = delGrid.getRowData(1).oid;
						        	   $.ajax({
				    						handler: "lms1205formhandler",
				       			            action: "deleteFlag",
				       			            data: {
				       			           		oid: delOid }
				       			            }.done(function(){
				       			            	delGrid.trigger("reloadGrid");
				    		            });
						           }
							   }
								
								
			            	 	$.thickbox.close();
			            	 	$("#cesPanel").attr("openFlag", "true");
								$("#cesPanel").empty();
								//刪除
			            	 	$.ajax({									
									handler : "lms1205formhandler",
									type : "POST",
									dataType : "json",
									data : 
									{
										formAction : "deleteC140m01a",
										mainId : responseJSON.mainId
									}
									}).done(function(json) {
									   //$("input[name='editMode3']:eq(3)").attr("checked",true);
									   $("input[name='editMode3'][value='5']:radio" ).prop( "checked" , true );
									   $("#tabForm").find(".sA1radio2-5").show().siblings("[class^=sA1radio2]").hide();		  
								});
							}else{
								//否的function
								CommonAPI.showMessage(i18n.lms1205s05["l120s05.alert3"]);
							}
						})
			         },
		              "close": function() {
		            	  API.confirmMessage(i18n.def['flow.exit'], function(res){
		  					if(res){
		  						$.thickbox.close();
		  					}
		  		        });
		             }
		           }			  
		    });
	 }