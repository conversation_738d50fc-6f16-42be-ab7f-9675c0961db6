---------------------------------------------------------
-- LMS.L130S01A 異常通報表明細檔
---------------------------------------------------------
--DROP TABLE LMS.L130S01A;
CREATE TABLE LMS.L130S01A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	CREATEBY      CHAR(3)      ,
	<PERSON>ANCH<PERSON>IN<PERSON>    CHAR(1)      ,
	<PERSON><PERSON><PERSON><PERSON>         CHAR(3)      ,
	SEQNAME       VARCHAR(384) ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>       CHAR(3)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>       CHAR(3)      ,
	SEQAPPEND     VARCHAR(384) ,
	SEQ<PERSON>IN<PERSON>       CHAR(1)      ,
	RUNDATE       DATE         ,
	SETKIND       CHAR(1)      ,
	SETCURR       VARCHAR(3)   ,
	SETAMT        DECIMAL(13,0),
	<PERSON><PERSON><PERSON>        CHAR(1)      ,
	<PERSON>OC<PERSON><PERSON>       VARCHAR(600) ,
	<PERSON><PERSON><PERSON>CI<PERSON>    CHAR(1)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>     CHAR(2)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>    CHAR(1)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>     CHAR(2)      ,
	ISUNNORMAL    CHAR(1)      ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L130S01A PRIMARY KEY(OID)
) in EL_DATA_4KTS index in EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL130S01A01;
--CREATE UNIQUE INDEX LMS.XL130S01A01 ON LMS.L130S01A   (OID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L130S01A IS '異常通報表明細檔';
COMMENT ON LMS.L130S01A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	CREATEBY      IS '文件產生方式', 
	BRANCHKIND    IS '單位種類', 
	SEQNO         IS '項目代號', 
	SEQNAME       IS '項目名稱', 
	SEQSHOW       IS '事項顯示順序', 
	BIGKIND       IS '事項大類', 
	SEQAPPEND     IS '停權組字時要加上顯示的字串', 
	SEQKIND       IS '擬/已辦', 
	RUNDATE       IS '處理日期', 
	SETKIND       IS '金額/設押種類', 
	SETCURR       IS '金額/設押幣別', 
	SETAMT        IS '金額/設押金額', 
	ISSTOP        IS '停權', 
	DOCDSCR       IS '說明', 
	AREADECIDE    IS '營運中心核定事項', 
	AREAMONTH     IS '依規定暫停單位主管授權月', 
	HEADDECIDE    IS '授管處核定事項', 
	HEADMONTH     IS '依規定暫停單位主管授權月', 
	ISUNNORMAL    IS '是否異動往來異常戶', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
