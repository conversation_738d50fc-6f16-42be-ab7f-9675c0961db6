package tw.com.jcs.flow;

public interface FlowConstant {

    String TABLE_INSTANCE = "table.instance";
    String TABLE_INSTANCE_HISTORY = "table.instanceHistory";
    String TABLE_SEQUENCE = "table.sequence";
    String TABLE_SEQUENCE_HISTORY = "table.sequenceHistory";

    String CACHE_SIZE = "cacheSize";
    String DEFINITION_LOCATION = "definitionLocation";
    String ID_PROVIDER = "idProvider";
    String OBJECT_FACTORY = "objectFactory";

}
