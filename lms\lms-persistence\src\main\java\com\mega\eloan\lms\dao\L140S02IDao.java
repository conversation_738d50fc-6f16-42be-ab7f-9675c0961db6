/* 
 * L140S02IDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S02I;

/** 留學貸款檔 **/
public interface L140S02IDao extends IGenericDao<L140S02I> {

	L140S02I findByOid(String oid);

	List<L140S02I> findByMainId(String mainId);

	L140S02I findByUniqueKey(String mainId, Integer seq);

	L140S02I findByUniqueKey(String mainId, Integer seq, String stdCustId,
			String stdDupNo);

	List<L140S02I> findByIndex01(String mainId, Integer seq, String stdCustId,
			String stdDupNo);

	List<L140S02I> findByCustIdDupId(String custId, String DupNo);

	List<L140S02I> findByIndex01(String mainId, Integer seq);
}
