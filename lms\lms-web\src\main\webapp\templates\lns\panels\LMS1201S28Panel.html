<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
        <wicket:panel>
            <form id="L166M01AForm">
                <fieldset>
                    <legend>
                        <strong>
                            <wicket:message key="L163M01A.rpaItem">
                                <!--RPA資料查詢-->
                            </wicket:message>
                        </strong>
                    </legend>
					<DIV style="BORDER-TOP-STYLE: solid; BORDER-LEFT-STYLE: solid; BORDER-TOP-COLOR: red; BORDER-BOTTOM-STYLE: solid; BORDER-LEFT-COLOR: red; BORDER-BOTTOM-COLOR: red; BORDER-RIGHT-STYLE: solid; BORDER-RIGHT-COLOR: red"><FONT color=red>
					＊說明：本頁功能僅為透過RPA機器人輔助查詢相關外部資訊，若無執行本頁相關功能或引進之資料查詢未完成，不影響簽報書送呈作業。 
					<BR></FONT></DIV>
					<BR>
					<button id="importQueryList" type="button">
                        <span class="text-only">重新引進查詢名單 </span>
                    </button>
                    <button id="rpaQuery" type="button">
                        <span class="text-only">一鍵查詢 </span>
                    </button>
                    <span style='margin-right:36px;' class='color-red'>(點選即一鍵查詢下列資料)</span>
					<br/>
                    <div>
                        <span class="color-red " id="href_OneBtnQuery">＊初次使用請先「引進查詢名單」，查詢項目及對象請參閱下方說明：</span>
                    </div>
                    <table border='1' width="100%">
                        <tr>
                            <td width="50%" style='background-color:#D6EAF8; '>
                                外部系統&nbsp;<span style="color:blue;">[預設查詢對象]</span>
                            </td>
                            <td width="50%" style='background-color:#AED6F1; '>
                                資料建檔系統&nbsp;<span style="color:blue;">[預設查詢對象]</span>
                            </td>
                        </tr>
                        <tr style='vertical-align:top;'>
                            <td nowrap="nowrap" style='border-bottom:0px; padding-right:12px;'>
                                <table border='0' class='tb2'>
                                    <tr style='vertical-align:top;'>
                                        <td class='noborder'>
                                        	<!--J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式-->
                                            07.信保保證書&nbsp;<span style="color:blue;">[僅適用於小規模營業人額度借款人與國發基金協助新創事業紓困融資加碼方案]</span>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <td nowrap="nowrap" style='border-bottom:0px; padding-right:12px;'>
                                <table border='0' class='tb2'>
                                    <tr style='vertical-align:top;'>
                                        <td class='noborder'>
                                            
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                    <br/>
                    <button id="addQuery" type="button">
                        <span class="text-only">新增查詢名單 </span>
                    </button>
                    <button id="deleteQuery" type="button">
                        <span class="text-only">刪除名單 </span>
                    </button>
                    <div id="gridviewQuery" />
                    <br/>
                    <button id="refresh" type="button">
                        <span class="text-only">取回查詢結果 </span>
                    </button>
                    <button id="reTry" type="button">
                        <span class="text-only">單筆重新查詢 </span>
                    </button>
					<!--
                    <button id="printAll" type="button" class="forview">
                        <span class="text-only">一鍵列印 </span>
                    </button>
					-->
                    <div id="gridviewRpaInfo" />
                </fieldset>
            </form><!-- pop up screen -->
            <div id="L160S01EDetail" style="display: none;">
                <form id="L160S01EForm01">
                    <!--<fieldset style="width:900px;">-->
                    <fieldset>
                        <legend>
                            <b>資料查詢名單</b>
                        </legend>
                        <p/><b>文件產生方式：</b>
                        <span class="field" id="createBY2" name="createBY2"></span>
                        <span class="field color-blue max" id="createBY" name="createBY" maxlength="3" style="display:none;"></span>
                        <p/>
                        <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td width="20%" class="hd1">
                                        與本案關係&nbsp;&nbsp;
                                    </td>
                                    <td width="30%">
                                        <input type="checkbox" name="custRelation" id="custRelation" />
                                    </td>
                                </tr>
                                <tr>
                                    <td width="20%" class="hd1">
                                        查詢項目&nbsp;&nbsp;
                                    </td>
                                    <td width="30%">
                                        <input type="checkbox" name="type" id="type" />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <br/>
                        <div class="funcContainer">
                        </div>
						<input type="hidden" id="oid" name="oid"/>
                        <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td class="hd2" colspan="4">
                                        名單內容
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1" width="20%">
                                        本案關係人統編&nbsp;&nbsp;
                                    </td>
                                    <td width="30%">
                                        <input type="text" id="custId" name="custId" class="upText" maxlength="10"/>
                                    </td>
                                    <td class="hd1" width="20%">
                                        重覆序號&nbsp;&nbsp;
                                    </td>
                                    <td width="30%">
                                        <input type="text" id="dupNo" name="dupNo" maxlength="1" size="5"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1" width="20%">
                                        戶名&nbsp;&nbsp;
                                        <br>
                                    </td>
                                    <td width="30%" colspan="3">
                                        <input type="text" id="custName" name="custName" class="required halfText" size="100" maxlength="120" maxlengthC="38" />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1" width="20%">
                                        查詢日&nbsp;&nbsp;
                                    </td>
                                    <td width="30%" colspan="3">
                                        <span id="queryDateS" class="field"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1" width="30%">
                                        <wicket:message key="L163M01A.queryIdDate">
                                            <!--發證日期-->
                                        </wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td width="70%" colspan="3">
                                        <wicket:message key="L163M01A.queryIdDateTitle">
                                            民國
                                        </wicket:message>
                                        <input type="text" class="number" id="idDateYear" name="idDateYear" maxlength="3" size="5" />
                                        <wicket:message key="L163M01A.queryIdDateYear">
                                            年
                                        </wicket:message>
                                        <input type="text" class="number max min" id="idDateMonth" name="idDateMonth" maxlength="2" size="5" min="1" max="12"/>
                                        <wicket:message key="L163M01A.queryIdDateMonth">
                                            月
                                        </wicket:message>
                                        <input type="text" class="number max min" id="idDateDay" name="idDateDay" maxlength="2" size="5" min="1" max="31" />
                                        <wicket:message key="L163M01A.queryIdDateDay">
                                            日
                                        </wicket:message>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1">
                                        <wicket:message key="L163M01A.queryIdSite">
                                            <!-- 發證地點-->
                                        </wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td colspan="3">
                                        <select id="idSite" name="idSite" class="" combokey="lms1601m01_IDSite" combotype="2" space="true" />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1">
                                        <wicket:message key="L163M01A.queryIdChangeType">
                                            <!-- 領補換類別-->
                                        </wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td colspan="3">
                                        <select id="idChangeType" name="idChangeType" class="">
                                            <option value="">請選擇</option>
                                            <option value="1">初發</option>
                                            <option value="2">補發</option>
                                            <option value="3">換發</option>
                                        </select>
                                    </td>
                                </tr>
	
                            </tbody>
                        </table>
                    </fieldset>
                </form>
            </div>
            <script type="text/javascript" src="pagejs/lns/LMS1201S28Panel.js?r=20210605">
            </script>
        </wicket:panel>
    </body>
</html>
