var Action = {
    ghandler: "lms2301gridhandler",
    fhandler: "lms2301m01formhandler"
};
pageJsInit(function() {
	$(function() {
	    var Temp_UserID = "";
	    var Temp_DBU_UserID = "";
	    var Temp_OBU_UserID = "";
	    var L230M01AGrid = $("#gridview").iGrid({
	        handler: Action.ghandler,
	        height: 350,
	        sortname: 'createTime',
	        sortorder: 'desc',
	        postData: {
	            formAction: "queryL230M01A",
	            docStatus: viewstatus//viewstatus,
	        },
	        rowNum: 15,
	        multiselect: true, //選項前多checkbox
	        colModel: [{
	            colHeader: i18n.lms2301v01['custId'],//統一編號
	            align: "left",
	            width: 100,
	            sortable: true,
	            name: 'custId',
	            formatter: 'click',
	            onclick: openDoc
	        }, {
	            colHeader: i18n.lms2301v01['custName'],//客戶名稱
	            align: "left",
	            width: 100,
	            sortable: true,
	            name: 'custName'
	        }, {
	            colHeader: i18n.lms2301v01['caseNo'], //案件號碼
	            align: "left",
	            width: 150,
	            sortable: true,
	            name: 'caseNo'
	        }, {
	            colHeader: i18n.lms2301v01['reasion'], //未簽約、動用原因
	            align: "left",
	            width: 100,
	            sortable: true,
	            name: 'reasion'
	        }, {
	            colHeader: i18n.lms2301v01['appraiser'], //經辦姓名
	            align: "left",
	            width: 100,
	            sortable: true,
	            name: 'apprId'
	        }, {
	            name: 'oid',
	            hidden: true //是否隱藏
	        }, {
	            name: 'mainId',
	            hidden: true
	        }, {
	            name: 'docURL',
	            hidden: true
	        }],
	        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
	            var data = L230M01AGrid.getRowData(rowid);
	            openDoc(null, null, data);
	        }
	    });
	    
	    function openDoc(cellvalue, options, rowObject){
	        $.thickbox.close();
	        $.form.submit({
	            url: '..' + rowObject.docURL + '/01',//'../lms/lms2301m01/01',
	            formHandler: Action.fhandler,
	            data: {
	                formAction: "queryL230m01a",
	                mainOid: rowObject.oid,
	                mainId: rowObject.mainId,
	                mainDocStatus: viewstatus,
	                txCode: txCode
	            },
	            target: rowObject.oid
	        });
	    };
	    
	    $("#buttonPanel").find("#btnAdd").click(function(){
	        plz_Fill_Cust_ID_NoDupo();
	    }).end().find("#btnModify").click(function(){
	        var row = $("#gridview").getGridParam('selrow');
	        var list = "";
	        var seldata = $("#gridview").getRowData(row);
	        list = seldata.oid;
	        if (!list) {
	            // TMMDeleteError=請先選擇需修改(刪除)之資料列
	            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
	        }
	        openDoc(null, null, seldata);
	    }).end().find("#btnDelete").click(function(){
	        var rows = $("#gridview").getGridParam('selarrrow');
	        var list = [];
	        for (var row in rows) {
	            list.push($("#gridview").getRowData(rows[row]).oid);
	        }
	        if (list.length == 0) {
	            // TMMDeleteError=請先選擇需修改(刪除)之資料列
	            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
	        }
	        
	        //confirmDelete=是否確定刪除?
	        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
	            if (b) {
	                for (var i in rows) {
	                    list.push($("#gridview").getRowData(rows[i]).oid);
	                }
					$.ajax({
						handler: Action.fhandler,
						data: {
							formAction: "deleteFile",
							list: list
						},
					}).done(function(obj) {
						$("#gridview").trigger("reloadGrid");
					});
	            }
	        });
	    });
	    
	    /***請輸入借款人統一編號(不含重複序號)(新增-->自簽報書引進)***/
	    function plz_Fill_Cust_ID_NoDupo(){
	        $("#plz_Fill_Cust_ID_NoDupo").thickbox({
	            //l230m01a.title01=授信管理系統
	            title: i18n.lms2301v01['l230m01a.title01'],
	            width: 400,
	            height: 130,
	            modal: true,
	            align: "center",
	            valign: "bottom",
	            i18n: i18n.def,
	            buttons: {
	                "sure": function(){
	                    if ($("#tabForm").valid()) {
	                        Temp_UserID = $("#user_Id").val();
	                        if ($.trim(Temp_UserID) == "") {
	                            //l230m01a.title05=請輸入借款人統一編號(不含重複序號)
	                            return API.showErrorMessage(i18n.lms2301v01['l230m01a.title05']);
	                        }
	                        $("#user_Id").val('');
	                        $.thickbox.close();
	                        SignReportGridStart();//A143240830
	                        $("#signReport").thickbox({ // main thickbox
	                            //l230m01a.title03=簽報書選擇
	                            title: i18n.lms2301v01['l230m01a.title03'],
	                            width: 600,
	                            height: 440,
	                            modal: true,
	                            valign: "bottom",
	                            align: "center",
	                            i18n: i18n.def,
	                            buttons: {
	                                "sure": function(){
	                                    var id = $("#signReportGrid").getGridParam('selrow');
	                                    if (!id) {
	                                        //action_005=請先選取一筆以上之資料列
	                                        return CommonAPI.showMessage(i18n.def["action_005"]);
	                                    }
	                                    
	                                    var result = $("#signReportGrid").getRowData(id);
	                                    create_NewDoc_Form_OldDoc(null, null, result);
	                                    $.thickbox.close();
	                                },
	                                "cancel": function(){ //關閉button									  		
	                                    $("#user_Id").val('');
	                                    $.thickbox.close();
	                                }
	                            }
	                        });
	                    }
	                },
	                "cancel": function(){
	                    $("#user_Id").val('');
	                    $.thickbox.close();
	                }
	            }
	        });
	    }
	    
	    /***自簽報書引進--請輸入借款人統一編號(不含重複序號)--簽報書選擇Grid***/
	    function SignReportGridStart(){
	        $("#signReportGrid").jqGrid("setGridParam", {
	            postData: {
	                custId: Temp_UserID,
	                formAction: "queryL120M01A"
	            },
	            search: true
	        }).trigger("reloadGrid");
	    }
		if($("#signReportGrid").length > 0) {
			var SignReportGrid = $("#signReportGrid").iGrid({
				        handler: Action.ghandler,
				        height: 235,
				        width: 400,
				        postData: {
				            formAction: "queryL120M01A"
				        },
				        rowNum: 10,
				        sortname: 'caseNo|caseDate|endDate',
				        sortorder: 'asc|asc|asc',
				        rownumbers: true,
						divWidth: 0,
				        colModel: [{
				            colHeader: i18n.lms2301v01['custName'],//主要借款人
				            name: 'custName',
				            align: "left",
				            width: 60
				        }, {
				            colHeader: i18n.lms2301v01['caseNo'],//案號	
				            name: 'caseNo',
				            align: "left",
				            width: 100
				        }, {
				            colHeader: i18n.lms2301v01['caseDate'],//簽案日期
				            name: 'caseDate',
				            formatter: 'date',
				            formatoptions: {
				                srcformat: 'Y-m-d',
				                newformat: 'Y-m-d'
				            },
				            align: "center",
				            width: 60
				        }, {
				            colHeader: i18n.lms2301v01['approveTime'],//核准日期
				            name: 'endDate',
				            formatter: 'date',
				            formatoptions: {
				                srcformat: 'Y-m-d',
				                newformat: 'Y-m-d'
				            },
				            align: "center",
				            width: 60
				            //hidden: true
				        }, {
				            colHeader: "mainId",
				            name: 'mainId',
				            hidden: true
				        }]
				        //ondblClickRow
				        //		onClickRow : function(DUPNO){		//當使用者在Grid裡面某筆資料上單點擊滑鼠就觸發修改功能
				        //            var data = localgrid.getRowData(DUPNO);
				        //			postDUPNO(null,null,data);
				        //        }
				    });
		}
	    
	    
	    //自簽報書引進-由舊資料複製到新資料上
	    function create_NewDoc_Form_OldDoc(cellvalue, options, rowObject){
	        $.ajax({
	            type: "POST",
	            handler: Action.fhandler,
	            data: {
	                formAction: "addCreditUnused",
	                txCode: txCode,
	                //原案簽報書mainId
	                caseMainId: rowObject.mainId,
	                showMsg: true
	            },
	            }).done(function(obj){
	                $("#gridview").trigger("reloadGrid");
	                $.form.submit({
	                    url: '../lms/lms2301m01/01',
	                    data: {
	                        mainDocStatus: viewstatus,
	                        docStatus: viewstatus,
	                        mainOid: obj.mainOid,
	                        mainId: obj.mainId
	                    },
	                    target: "_blank"
	                });
	        });
	    }
	});
});