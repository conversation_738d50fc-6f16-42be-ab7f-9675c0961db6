/* 
 * C101M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.apache.bval.constraints.NotEmpty;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.SaveCheck;

/** 個金徵信借款人主檔 **/
@NamedEntityGraph(name = "C101M01A-entity-graph", attributeNodes = { 
		@NamedAttributeNode("c101s01g"),
		@NamedAttributeNode("c101s01q"),
		@NamedAttributeNode("c101s01r"),
		@NamedAttributeNode("c101s01e") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101M01A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "ownBrId", "custId", "dupNo" }))
public class C101M01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 編製單位代號
	 * <p/>
	 * 單位代碼
	 */
	@Size(max = 3)
	@Column(name = "OWNBRID", length = 3, columnDefinition = "CHAR(3)")
	private String ownBrId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 借款人姓名 **/
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/**
	 * 客戶型態 (區部別)
	 * <p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 */
	@Size(max = 1)
	@Column(name = "TYPCD", length = 1, columnDefinition = "CHAR(1)")
	private String typCd;

	/**
	 * 客戶編號
	 * <p/>
	 * 非必要輸入
	 */
	@Size(max = 60)
	@Column(name = "CUSTNO", length = 60, columnDefinition = "VARCHAR(60)")
	private String custNo;

	/**
	 * 職工編號
	 * <p/>
	 * 非必要輸入
	 */
	@Size(max = 20)
	@Column(name = "STAFFNO", length = 20, columnDefinition = "VARCHAR(20)")
	private String staffNo;
	
	/** 負責事業體統一編號 **/
	@Size(max = 10)
	@Column(name = "CMPID", length = 10, columnDefinition = "VARCHAR(10)")
	private String cmpId;

	/** 負責事業體名稱 **/
	@Size(max = 120)
	@Column(name = "CMPNM", length = 120, columnDefinition = "VARCHAR(120)")
	private String cmpNm;

	/** 前婉卻分行 **/
	@Size(max = 3)
	@Column(name = "BFREJBRANCH", length = 3, columnDefinition = "CHAR(3)")
	private String bfRejBranch;

	/** 前婉卻原因 **/
	@Size(max = 120)
	@Column(name = "BFREJREASON", length = 120, columnDefinition = "VARCHAR(120)")
	private String bfRejReason;

	/** 前婉卻日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "BFREJDATE", columnDefinition = "DATE")
	private Date bfRejDate;

	/** 前婉卻狀態 **/
	@Size(max = 1)
	@Column(name = "BFREJCASE", length = 1, columnDefinition = "CHAR(1)")
	private String bfRejCase;

	/** 現婉卻狀態 **/
	@Size(max = 1)
	@Column(name = "REJECTCASE", length = 1, columnDefinition = "CHAR(1)")
	private String rejectCase;

	/** 現婉卻記錄 **/
	@Size(max = 120)
	@Column(name = "REJECTMEMO", length = 120, columnDefinition = "VARCHAR(120)")
	private String rejectMemo;

	/**
	 * 評等類型
	 * <p/>
	 * 
	 * 0.免辦 1.房貸個人評等模型 2.非房貸個人評等模型
	 */
	@Size(max = 5)
	@Column(name = "MARKMODEL", length = 5, columnDefinition = "CHAR(5)")
	private String markModel;

	/**
	 * 是否為自然人
	 * <p/>
	 * Y.是 N.否
	 */
	@Size(max = 1)
	@Column(name = "NATURALFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String naturalFlag;

	/**
	 * 是否可引進
	 * <p/>
	 * Y.可 N.不可
	 */
	@Size(max = 1)
	@Column(name = "IMPORTFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String importFlag;

	/** 備註 **/
	@Size(max = 30)
	@Column(name = "RMK", length = 30, columnDefinition = "VARCHAR(30)")
	private String rmk;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 第1碼krm040，第2碼bam095，第3碼KRS008(值 和C101S01G.cardFlag 未持有信用卡 相反) **/
	@Column(name = "JCICFLG", length = 3, columnDefinition = "VARCHAR(3)")
	private String jcicFlg;
	
	/** 查詢異常通報紀錄日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ABNORMALREADDATE", columnDefinition = "DATE")
	private Date abnormalReadDate;
	
	/** 通報分行 **/
	@Size(max = 3)
	@Column(name = "ABNORMALBRNO", length = 3, columnDefinition = "VARCHAR(3)")
	private String abnormalBrNo;
	
	/** 通報日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ABNORMALDATE", columnDefinition = "DATE")
	private Date abnormalDate;
	
	/** 目前異常通報狀態{Y:已解除,N:未解除} **/
	@Size(max = 1)
	@Column(name = "ABNORMALSTATUS", length = 1, columnDefinition = "CHAR(1)")
	private String abnormalStatus;

	/** 異常通報文件id **/
	@Size(max = 32)
	@Column(name = "ABNORMALMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String abnormalMainId;
	
	/** 信用卡正卡張數(不含停用) **/
	@Digits(integer = 3, fraction = 0)
	@Column(name = "PRIMARY_CARD", columnDefinition = "DEC(3,0)")
	private BigDecimal primary_card;
	
	/** 信用卡附卡張數(不含停用) **/
	@Digits(integer = 3, fraction = 0)
	@Column(name = "ADDITIONAL_CARD", columnDefinition = "DEC(3,0)")
	private BigDecimal additional_card;
	
	/** 信用卡商務/採購卡張數(不含停用) **/
	@Digits(integer = 3, fraction = 0)
	@Column(name = "BUSINESS_OR_P_CARD", columnDefinition = "DEC(3,0)")
	private BigDecimal business_or_p_card;
	
	/** 借款人提供之資料與聯徵中心或與本行內部留存資料不相符 {Y:有, N:無} **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "ISIMPORTDATAMATCH", length = 1, columnDefinition = "CHAR(1)")
	private String isImportDataMatch;
	
	/** 借款人或保證人提供申請資料及證明文件過於完整 {Y:有, N:無} **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "ISFULLAPPLYDOCUMENT", length = 1, columnDefinition = "CHAR(1)")
	private String isFullApplyDocument;
	
	/** 照會過程難以直接聯繫借款人本人，需由第三人居中聯繫 {Y:有, N:無} **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "ISNOTEBORROWER", length = 1, columnDefinition = "CHAR(1)")
	private String isNoteBorrower;
	
	/** 借款人提供經變造之申貸文件 {Y:有, N:無} **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "ISCHECKORIGINALDOCUMENT", length = 1, columnDefinition = "CHAR(1)")
	private String isCheckOriginalDocument;
		
	/** 符合信貸減碼 */
	@Column(name = "CREDITLOANREDUCTFG", length = 1, columnDefinition = "CHAR(1)")
	private String creditLoanReductFg;
	
	/** 本行房貸戶 */
	@Column(name = "HASHOUSELOAN", length = 1, columnDefinition = "CHAR(1)")
	private String hasHouseLoan;
	
	/** 近1年AUM(單位:萬) */
	@Column(name = "WM_12M", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal wm_12m;

	/** 本行信用卡最早持卡日 */
	@Temporal(TemporalType.DATE)
	@Column(name = "HOLDMEGACARDDT", columnDefinition = "DATE")
	private Date holdMegaCardDt;
	
	/** 申貸證明文件(如:身分證影本, 買賣合約書影本...等)應已加蓋「與正本相符」及「限辦理本行授信業務使用」之樣章 **/
	@Size(max = 1)
	@Column(name = "LOANDOCCHECKFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String loanDocCheckFlag;
	
	/** 是否為勞工紓困4.0 **/
	@Size(max = 1)
	@Column(name = "ISBAILOUT4", length = 1, columnDefinition = "CHAR(1)")
	private String isBailout4;
	
	/** 買賣契約書(如有)與借款契約、借款申請書簽名不一致 {Y:有, N:無} **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "ISDOCSIGNATURENOTMATCH", length = 1, columnDefinition = "CHAR(1)")
	private String isDocSignatureNotMatch;
	
	/** 借款人、擔保品所有權人與房屋契約書之買方不同人 {Y:有, N:無} **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "ISOWNERBUYERNOTSAMEPERSON", length = 1, columnDefinition = "CHAR(1)")
	private String isOwnerBuyerNotSamePerson;
	
	/** 由非親屬之第三人陪同申辦貸款 {Y:有, N:無} **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "ISWITHNONRELATIVES", length = 1, columnDefinition = "CHAR(1)")
	private String isWithNonRelatives;
	
	/** 指定撥款日期及時間，且無法提出合理解釋 {Y:有, N:無} **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "ISPOINTAPPROPRIATIONDATE", length = 1, columnDefinition = "CHAR(1)")
	private String isPointAppropriationDate;
	
	/** 對自己從事之行業或職業性質與內容、購屋目的不瞭解或毫無概念 {Y:有, N:無} **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "ISDONTKNOWOWNAFFAIRS", length = 1, columnDefinition = "CHAR(1)")
	private String isDontKnowOwnAffairs;
	
	/** 於聯徵具被查詢紀錄，卻無法說明原因 {Y:有, N:無} **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "ISDONTEXPLAINEJCICRECORD", length = 1, columnDefinition = "CHAR(1)")
	private String isDontExplainEjcicRecord;
	
	/** 支付銀行收取開辦手續費以外之費用以完成貸款目的(確認有無代辦業者收費) {Y:有, N:無} **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "ISPAYOTHERFEE", length = 1, columnDefinition = "CHAR(1)")
	private String isPayOtherFee;
	
	/** 信貸集中徵信註記 **/
	@Size(max = 1)
	@Column(name = "CONCENTRATECREDIT", length = 1, columnDefinition = "CHAR(1)")
	private String concentrateCredit;
	
	/** 借戶所得來自建築業者、代銷、仲介時，或其身分屬前述對象之關係戶(股東、員工)時，是否由其交易資料確認購屋價金來自第三人，且無法佐證其與第三人之關係。 {Y:是, N:	否/不適用} **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "ISCASHFROMOTHERS", length = 1, columnDefinition = "CHAR(1)")
	private String isCashFromOthers;

	/** 產品初評註記 **/
	@Size(max = 1)
	@Column(name = "PRODKINDFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String prodKindFlag;
	
	/** 年輕族群客戶加強關懷結果:申請目的 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT1", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult1;
	
	/** 年輕族群客戶加強關懷結果:產品認知_第一項 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT2_1", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult2_1;
	
	/** 年輕族群客戶加強關懷結果:產品認知_第二項 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT2_2", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult2_2;
	
	/** 年輕族群客戶加強關懷結果:償債能力_第一項 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT3_1", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult3_1;
	
	/** 年輕族群客戶加強關懷結果:償債能力_第二項 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT3_2", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult3_2;
	
	/** 年輕族群客戶加強關懷結果:案件來源_第一項 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT4_1", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult4_1;
	
	/** 年輕族群客戶加強關懷結果:案件來源_第二項 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT4_2", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult4_2;
	
	/** 年輕族群客戶加強關懷結果:告知與揭露 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT5", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult5;
	
	/** 年輕族群客戶加強關懷備註 **/
	@Size(max = 900)
	@Column(name = "YOUNGCAREMEMO", length = 900, columnDefinition = "VARCHAR(900)")
	private String youngCareMemo;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得編製單位代號
	 * <p/>
	 * 單位代碼
	 */
	public String getOwnBrId() {
		return this.ownBrId;
	}

	/**
	 * 設定編製單位代號
	 * <p/>
	 * 單位代碼
	 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得借款人姓名 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定借款人姓名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得客戶型態 (區部別)
	 * <p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 */
	public String getTypCd() {
		return this.typCd;
	}

	/**
	 * 設定客戶型態 (區部別)
	 * <p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/**
	 * 取得客戶編號
	 * <p/>
	 * 非必要輸入
	 */
	public String getCustNo() {
		return this.custNo;
	}

	/**
	 * 設定客戶編號
	 * <p/>
	 * 非必要輸入
	 **/
	public void setCustNo(String value) {
		this.custNo = value;
	}

	/** 取得負責事業體統一編號 **/
	public String getCmpId() {
		return this.cmpId;
	}

	/** 設定負責事業體統一編號 **/
	public void setCmpId(String value) {
		this.cmpId = value;
	}

	/** 取得負責事業體名稱 **/
	public String getCmpNm() {
		return this.cmpNm;
	}

	/** 設定負責事業體名稱 **/
	public void setCmpNm(String value) {
		this.cmpNm = value;
	}

	/** 取得前婉卻分行 **/
	public String getBfRejBranch() {
		return this.bfRejBranch;
	}

	/** 設定前婉卻分行 **/
	public void setBfRejBranch(String value) {
		this.bfRejBranch = value;
	}

	/** 取得前婉卻原因 **/
	public String getBfRejReason() {
		return this.bfRejReason;
	}

	/** 設定前婉卻原因 **/
	public void setBfRejReason(String value) {
		this.bfRejReason = value;
	}

	/** 取得前婉卻日期 **/
	public Date getBfRejDate() {
		return this.bfRejDate;
	}

	/** 設定前婉卻日期 **/
	public void setBfRejDate(Date value) {
		this.bfRejDate = value;
	}

	/** 取得前婉卻狀態 **/
	public String getBfRejCase() {
		return this.bfRejCase;
	}

	/** 設定前婉卻狀態 **/
	public void setBfRejCase(String value) {
		this.bfRejCase = value;
	}

	/** 取得現婉卻狀態 **/
	public String getRejectCase() {
		return this.rejectCase;
	}

	/** 設定現婉卻狀態 **/
	public void setRejectCase(String value) {
		this.rejectCase = value;
	}

	/** 取得現婉卻記錄 **/
	public String getRejectMemo() {
		return this.rejectMemo;
	}

	/** 設定現婉卻記錄 **/
	public void setRejectMemo(String value) {
		this.rejectMemo = value;
	}

	/**
	 * 
	 * 取得評等類型
	 * <p/>
	 * 
	 * 0.免辦 1.房貸個人評等模型 2.非房貸個人評等模型
	 */

	public String getMarkModel() {
		return this.markModel;
	}

	/**
	 * 
	 * 設定評等類型
	 * <p/>
	 * 
	 * 0.免辦 1.房貸個人評等模型 2.非房貸個人評等模型
	 **/

	public void setMarkModel(String value) {
		this.markModel = value;
	}

	/**
	 * 取得是否為自然人
	 * <p/>
	 * Y.是 N.否
	 */
	public String getNaturalFlag() {
		return this.naturalFlag;
	}

	/**
	 * 設定是否為自然人
	 * <p/>
	 * Y.是 N.否
	 **/
	public void setNaturalFlag(String value) {
		this.naturalFlag = value;
	}

	/**
	 * 取得是否可引進
	 * <p/>
	 * Y.可 N.不可
	 */
	public String getImportFlag() {
		return this.importFlag;
	}

	/**
	 * 設定是否可引進
	 * <p/>
	 * Y.可 N.不可
	 **/
	public void setImportFlag(String value) {
		this.importFlag = value;
	}

	/** 取得備註 **/
	public String getRmk() {
		return this.rmk;
	}

	/** 設定備註 **/
	public void setRmk(String value) {
		this.rmk = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * join C101S01G
	 */
	// @OneToMany(mappedBy = "c101m01a")
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C101S01G c101s01g;

	public void setC101s01g(C101S01G c101s01g) {
		this.c101s01g = c101s01g;
	}

	public C101S01G getC101s01g() {
		return c101s01g;
	}
	
	/**
	 * join C101S01Q
	 */
	// @OneToMany(mappedBy = "c101m01a")
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C101S01Q c101s01q; 

	public void setC101s01q(C101S01Q c101s01q) {
		this.c101s01q = c101s01q;
	}

	public C101S01Q getC101s01q() {
		return c101s01q;
	}
	
	/**
	 * join C101S01R
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C101S01R c101s01r; 

	public void setC101s01r(C101S01R c101s01r) {
		this.c101s01r = c101s01r;
	}

	public C101S01R getC101s01r() {
		return c101s01r;
	}
	
	/**
	 * join C101S01E
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			//@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C101S01E c101s01e;

	public void setC101s01e(C101S01E c101s01e) {
		this.c101s01e = c101s01e;
	}

	public C101S01E getC101s01e() {
		return c101s01e;
	}

	/**
	 * 顯示用欄位
	 */
	/** 統編+重覆序號 **/
	@Transient
	private String custNumber;

	public void setCustNumber(String value) {
		this.custNumber = value;
	}

	public String getTest() {
		return custNumber;
	}
	/**
	 * 設定客戶編號
	 * <p/>
	 * 非必要輸入
	 */
	public void setStaffNo(String value) {
		this.staffNo = value;
	}
	/**
	 * 取得客戶編號
	 * <p/>
	 * 非必要輸入
	 */
	public String getStaffNo() {
		return this.staffNo;
	}

	/** 取得第1碼krm040，第2碼bam095，第3碼KRS008(值 和C101S01G.cardFlag 未持有信用卡 相反) **/
	public String getJcicFlg() {
		return this.jcicFlg;
	}
	/** 設定第1碼krm040，第2碼bam095，第3碼KRS008(值 和C101S01G.cardFlag 未持有信用卡 相反) **/
	public void setJcicFlg(String value) {
		this.jcicFlg = value;
	}

	/** 取得查詢異常通報紀錄日期 **/
	public Date getAbnormalReadDate() {
		return abnormalReadDate;
	}
	/** 設定查詢異常通報紀錄日期 **/
	public void setAbnormalReadDate(Date abnormalReadDate) {
		this.abnormalReadDate = abnormalReadDate;
	}

	/** 取得通報分行 **/
	public String getAbnormalBrNo() {
		return abnormalBrNo;
	}
	/** 設定通報分行 **/
	public void setAbnormalBrNo(String abnormalBrNo) {
		this.abnormalBrNo = abnormalBrNo;
	}

	/** 取得通報日期 **/
	public Date getAbnormalDate() {
		return abnormalDate;
	}
	/** 設定通報日期 **/
	public void setAbnormalDate(Date abnormalDate) {
		this.abnormalDate = abnormalDate;
	}

	/** 取得目前異常通報狀態{Y:已解除,N:未解除} **/
	public String getAbnormalStatus() {
		return abnormalStatus;
	}
	/** 設定目前異常通報狀態{Y:已解除,N:未解除} **/
	public void setAbnormalStatus(String abnormalStatus) {
		this.abnormalStatus = abnormalStatus;
	}

	/** 取得異常通報文件id **/
	public String getAbnormalMainId() {
		return abnormalMainId;
	}
	/** 設定異常通報文件id **/
	public void setAbnormalMainId(String abnormalMainId) {
		this.abnormalMainId = abnormalMainId;
	}

	/** 取得信用卡正卡張數(不含停用) **/
	public BigDecimal getPrimary_card() {
		return primary_card;
	}
	/** 設定信用卡正卡張數(不含停用) **/
	public void setPrimary_card(BigDecimal primary_card) {
		this.primary_card = primary_card;
	}
	
	/** 取得信用卡附卡張數(不含停用) **/
	public BigDecimal getAdditional_card() {
		return additional_card;
	}
	/** 設定信用卡附卡張數(不含停用) **/
	public void setAdditional_card(BigDecimal additional_card) {
		this.additional_card = additional_card;
	}

	/** 取得信用卡商務/採購卡張數(不含停用) **/
	public BigDecimal getBusiness_or_p_card() {
		return business_or_p_card;
	}
	/** 設定信用卡商務/採購卡張數(不含停用) **/
	public void setBusiness_or_p_card(BigDecimal business_or_p_card) {
		this.business_or_p_card = business_or_p_card;
	}
	
	public String getIsImportDataMatch() {
		return isImportDataMatch;
	}

	public void setIsImportDataMatch(String isImportDataMatch) {
		this.isImportDataMatch = isImportDataMatch;
	}

	public String getIsFullApplyDocument() {
		return isFullApplyDocument;
	}

	public void setIsFullApplyDocument(String isFullApplyDocument) {
		this.isFullApplyDocument = isFullApplyDocument;
	}

	public String getIsNoteBorrower() {
		return isNoteBorrower;
	}

	public void setIsNoteBorrower(String isNoteBorrower) {
		this.isNoteBorrower = isNoteBorrower;
	}

	public String getIsCheckOriginalDocument() {
		return isCheckOriginalDocument;
	}

	public void setIsCheckOriginalDocument(String isCheckOriginalDocument) {
		this.isCheckOriginalDocument = isCheckOriginalDocument;
	}

	public String getCreditLoanReductFg() {
		return creditLoanReductFg;
	}
	public void setCreditLoanReductFg(String creditLoanReductFg) {
		this.creditLoanReductFg = creditLoanReductFg;
	}

	public String getHasHouseLoan() {
		return hasHouseLoan;
	}
	public void setHasHouseLoan(String hasHouseLoan) {
		this.hasHouseLoan = hasHouseLoan;
	}

	public BigDecimal getWm_12m() {
		return wm_12m;
	}
	public void setWm_12m(BigDecimal wm_12m) {
		this.wm_12m = wm_12m;
	}

	public Date getHoldMegaCardDt() {
		return holdMegaCardDt;
	}
	public void setHoldMegaCardDt(Date holdMegaCardDt) {
		this.holdMegaCardDt = holdMegaCardDt;
	}

	public String getLoanDocCheckFlag() {
		return loanDocCheckFlag;
	}

	public void setLoanDocCheckFlag(String loanDocCheckFlag) {
		this.loanDocCheckFlag = loanDocCheckFlag;
	}

	public String getIsBailout4() {
		return isBailout4;
	}

	public void setIsBailout4(String isBailout4) {
		this.isBailout4 = isBailout4;
	}

	public String getIsDocSignatureNotMatch() {
		return isDocSignatureNotMatch;
	}

	public void setIsDocSignatureNotMatch(String isDocSignatureNotMatch) {
		this.isDocSignatureNotMatch = isDocSignatureNotMatch;
	}

	public String getIsOwnerBuyerNotSamePerson() {
		return isOwnerBuyerNotSamePerson;
	}

	public void setIsOwnerBuyerNotSamePerson(String isOwnerBuyerNotSamePerson) {
		this.isOwnerBuyerNotSamePerson = isOwnerBuyerNotSamePerson;
	}

	public String getIsWithNonRelatives() {
		return isWithNonRelatives;
	}

	public void setIsWithNonRelatives(String isWithNonRelatives) {
		this.isWithNonRelatives = isWithNonRelatives;
	}

	public String getIsPointAppropriationDate() {
		return isPointAppropriationDate;
	}

	public void setIsPointAppropriationDate(String isPointAppropriationDate) {
		this.isPointAppropriationDate = isPointAppropriationDate;
	}

	public String getIsDontKnowOwnAffairs() {
		return isDontKnowOwnAffairs;
	}

	public void setIsDontKnowOwnAffairs(String isDontKnowOwnAffairs) {
		this.isDontKnowOwnAffairs = isDontKnowOwnAffairs;
	}

	public String getIsDontExplainEjcicRecord() {
		return isDontExplainEjcicRecord;
	}

	public void setIsDontExplainEjcicRecord(String isDontExplainEjcicRecord) {
		this.isDontExplainEjcicRecord = isDontExplainEjcicRecord;
	}

	public String getIsPayOtherFee() {
		return isPayOtherFee;
	}

	public void setIsPayOtherFee(String isPayOtherFee) {
		this.isPayOtherFee = isPayOtherFee;
	}

	public String getConcentrateCredit() {
		return concentrateCredit;
	}

	public void setConcentrateCredit(String concentrateCredit) {
		this.concentrateCredit = concentrateCredit;
	}

	public String getIsCashFromOthers() {
		return isCashFromOthers;
	}

	public void setIsCashFromOthers(String isCashFromOthers) {
		this.isCashFromOthers = isCashFromOthers;
	}

	public String getProdKindFlag() {
		return prodKindFlag;
	}

	public void setProdKindFlag(String prodKindFlag) {
		this.prodKindFlag = prodKindFlag;
	}

	public String getYoungCareResult1() {
		return youngCareResult1;
	}

	public void setYoungCareResult1(String youngCareResult1) {
		this.youngCareResult1 = youngCareResult1;
	}

	public String getYoungCareResult2_1() {
		return youngCareResult2_1;
	}

	public void setYoungCareResult2_1(String youngCareResult2_1) {
		this.youngCareResult2_1 = youngCareResult2_1;
	}

	public String getYoungCareResult2_2() {
		return youngCareResult2_2;
	}

	public void setYoungCareResult2_2(String youngCareResult2_2) {
		this.youngCareResult2_2 = youngCareResult2_2;
	}

	public String getYoungCareResult3_1() {
		return youngCareResult3_1;
	}

	public void setYoungCareResult3_1(String youngCareResult3_1) {
		this.youngCareResult3_1 = youngCareResult3_1;
	}

	public String getYoungCareResult3_2() {
		return youngCareResult3_2;
	}

	public void setYoungCareResult3_2(String youngCareResult3_2) {
		this.youngCareResult3_2 = youngCareResult3_2;
	}

	public String getYoungCareResult4_1() {
		return youngCareResult4_1;
	}

	public void setYoungCareResult4_1(String youngCareResult4_1) {
		this.youngCareResult4_1 = youngCareResult4_1;
	}

	public String getYoungCareResult4_2() {
		return youngCareResult4_2;
	}

	public void setYoungCareResult4_2(String youngCareResult4_2) {
		this.youngCareResult4_2 = youngCareResult4_2;
	}

	public String getYoungCareResult5() {
		return youngCareResult5;
	}

	public void setYoungCareResult5(String youngCareResult5) {
		this.youngCareResult5 = youngCareResult5;
	}

	public String getYoungCareMemo() {
		return youngCareMemo;
	}

	public void setYoungCareMemo(String youngCareMemo) {
		this.youngCareMemo = youngCareMemo;
	}
	
}
