/* 
 * L140S12ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S12A;

/** 其他續做條件追蹤分項 **/
public interface L140S12ADao extends IGenericDao<L140S12A> {

	List<L140S12A> findByOids(String[] oids);
	
	L140S12A findByOid(String oid);
	
	List<L140S12A> findByMainId(String mainId);
	
	List<L140S12A> findByCaseMainId(String caseMainId);

	List<L140S12A> findByIndex01(String mainId);
	
	L140S12A findMaxSeqNumByMainId(String mainId);
}