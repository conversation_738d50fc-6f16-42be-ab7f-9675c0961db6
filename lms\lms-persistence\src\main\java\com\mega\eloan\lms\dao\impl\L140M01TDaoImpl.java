
package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.L140M01TDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140M01T;


@Repository
public class L140M01TDaoImpl extends LMSJpaDao<L140M01T, String> implements
		L140M01TDao {
	
	
	@Override
	public L140M01T findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	
	@Override
	public List<L140M01T> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("createTime", false);
		search.setMaxResults(Integer.MAX_VALUE);

		List<L140M01T> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L140M01T> findCurrentByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "flag", "Y");
		search.addOrderBy("createTime", false);
		search.setMaxResults(Integer.MAX_VALUE);

		List<L140M01T> list = createQuery(search).getResultList();
		return list;
	}
	
	
	@Override
	public List<L140M01T> findLastByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "flag", "N");
		search.addOrderBy("createTime", false);
		search.setMaxResults(Integer.MAX_VALUE);

		List<L140M01T> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L140M01T findByMainIdEstateType(String mainId, String estateType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "flag", "Y");
		search.addSearchModeParameters(SearchMode.EQUALS, "estateType", estateType);
		 
		return findUniqueOrNone(search);
	}

	@Override
	public L140M01T findByMainIdFlagEstateTypeEstateSubType(String mainId, String flag, String estateType, String estateSubType){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "flag", flag);
		search.addSearchModeParameters(SearchMode.EQUALS, "estateType", estateType);
		if(Util.isNotEmpty(Util.trim(estateSubType))){
			search.addSearchModeParameters(SearchMode.EQUALS, "estateSubType", estateSubType);
		}
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<L140M01T> findByMainId_orderBy(String mainId, LinkedHashMap<String, Boolean> orderByMap){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if(orderByMap!=null && orderByMap.size()>0){
			for(String orderByKey : orderByMap.keySet()){
				search.addOrderBy(orderByKey, orderByMap.get(orderByKey));
			}			
		}
		search.setMaxResults(Integer.MAX_VALUE);

		List<L140M01T> list = createQuery(search).getResultList();
		return list;		
	}
}