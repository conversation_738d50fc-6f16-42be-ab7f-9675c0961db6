/* 
 * LMS1411S02Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.panels.LMSL140M01MPanel;

/**
 * <pre>
 * [國內企金]聯行額度明細表 - 額度明細表明細
 * </pre>
 * 
 * @since 2012/11/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/29,REX,new
 *          </ul>
 */
public class CLS1171S02Panel extends Panel {

	private static final long serialVersionUID = -4024257163623646201L;

	public CLS1171S02Panel(String id) {
		super(id);
	}

	public CLS1171S02Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		// 央行購住共用
		new LMSL140M01MPanel("LMSL140M01MPanel").processPanelData(model,
				params);
	}
}
