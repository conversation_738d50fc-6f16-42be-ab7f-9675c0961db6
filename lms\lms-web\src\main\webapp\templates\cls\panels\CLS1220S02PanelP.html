<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <script type="text/javascript">loadScript('pagejs/cls/CLS1220S02PanelP');</script>
			<!--======================================================-->
		<form id="CLS1220S02Form">
            <table class="tb2" width="100%">
            	<tr>
					<td class="hd2" align="right" >
						<span class="color-red">＊</span>
						<th:block th:text="#{'C101S01A.birthday'}">出生日期</th:block>&nbsp;&nbsp;</td>
					<!--<td colspan="3" ><span id="birthday" class="field" ></span></td>-->
					<td width="32%" colspan="3">
						<input type="text" id="birthday" name="birthday" class="date" />
					</td>
				</tr>
            	<tr>
					<td class="hd2" align="right" >
						<span class="color-red">＊</span>
						<th:block th:text="#{'C101S01A.edu'}">學歷</th:block>&nbsp;&nbsp;</td>
					<td colspan="3" ><select id="edu" name="edu" class="" codeType="cls1131m01_edu" ></select></td>
				</tr>
				<tr>
					<td class="hd2" align="right"><th:block th:text="#{'C101S01A.marry'}">婚姻狀況</th:block>&nbsp;&nbsp;</td>
					<td colspan="3">
						<input type="radio" id="marry" name="marry" class="" codeType="marry" itemStyle="size:5" /><br/>
						<th:block th:text="#{'C101S01A.child'}">子女數</th:block>
						<input type="text" id="child" name="child" class="max number" maxlength="2" size="2"/>
					</td>					
				</tr>				
				<tr>
					<td class="hd2" align="right" width="18%">
						<span class="color-red">＊</span>
						<th:block th:text="#{'C101S01A.mTel'}">行動電話</th:block>&nbsp;&nbsp;</td>
					<!--<td width="32%"><span id="mTel" class="field" ></span></td>-->
					<td>
						<input type="text" id="mTel" name="mTel" class="max" maxlength="150" />
					</td>
					<td class="hd2" align="right" width="18%">
						<th:block th:text="#{'C101S01A.email'}">email</th:block>&nbsp;&nbsp;</td>
					<!--<td width="32%"><span id="email" class="field" ></span></td>-->
					<td>
						<input type="text" id="email" name="email" class="max email" maxlength="120" />
					</td>
				</tr>			
				<tr>
					<td class="hd2" align="right" ><th:block th:text="#{'ploanObj.homePhone'}">住宅電話</th:block>&nbsp;&nbsp;</td>
					<!--<td colspan="3" ><span id="ploan_basicInfo_homePhone" class="field" ></span>&nbsp;</td>-->
					<td colspan="3">
						<input type="text" id="ploan_basicInfo_homePhone" name="ploan_basicInfo_homePhone" class="max" maxlength="150" />
					</td>
				</tr>
				<tr>
					<td class="hd2" align="right" >
						<span class="color-red">＊</span>
						<th:block th:text="#{'C101S01A.fAddr'}">戶籍地址</th:block>&nbsp;&nbsp;
					<br/>
						<button type="button" id="btfAddr" name="btfAddr">
							<span class="text-only">
								<th:block th:text="#{'C101S01A.fAddr'}">戶籍地址</th:block>
							</span>
						</button>
					</td>
					<td colspan="3" >
						<input type="text" id="fCity" name="fCity" style="display:none;" />
						<input type="text" id="fZip" name="fZip" style="display:none;" />
						<input type="text" id="fAddr" name="fAddr" style="display:none;" />
						<input type="text" id="fTarget" name="fTarget" readonly="readonly" class="max" maxlength="150" size="90"/>
						<!--<a href="#" id="fTargetLink" class="readOnly" ><span id="fTarget" class="field comboSpace" ></span></a>-->
					</td>
				</tr>
						
						
				<tr>
					<td class="hd2" align="right" >
						<span class="color-red">＊</span>
						<th:block th:text="#{'C101S01A.coAddr'}">通訊地址</th:block>&nbsp;&nbsp;			
						<br/>
						<button type="button" id="btSameAddr" name="btSameAddr">
							<span class="text-only">
								<th:block th:text="#{'C101S01A.samefAddr'}">同戶籍地址</th:block>
							</span>
						</button>
						<button type="button" id="bcoAddr" name="bcoAddr">
							<span class="text-only">
								<th:block th:text="#{'C101S01A.coAddr'}">通訊地址</th:block>
							</span>
						</button>		
					</td>
					<td colspan="3" >
						<input type="text" id="coCity" name="coCity" style="display:none;" />
						<input type="text" id="coZip" name="coZip" style="display:none;" />
						<input type="text" id="coAddr" name="coAddr" style="display:none;" />
						<input type="text" id="coTarget" name="coTarget" readonly="readonly" class="max" maxlength="150" size="90"/>
						<!--<a href="#" id="coTargetLink" class="readOnly" ><span id="coTarget" class="field comboSpace" ></span></a>-->
					</td>
				</tr>
				<tr>
					<td class="hd2" align="right"  >
						<span class="color-red">＊</span>
						<th:block th:text="#{'C101S01A.houseStatus'}">現住房屋</th:block>&nbsp;&nbsp;</td>
					<td  colspan="3"  ><select id="houseStatus" name="houseStatus" class="setItem" codeType="lms1205s01_houseStatus" itemStyle="format:{value}-{key}" ></select></td>
					<!--
					不動產狀況 欄位，原本要填，改為客戶不須填寫
					<td class="hd2" align="right"  ><th:block th:text="#{'C101S01A.cmsStatus'}">不動產狀況</th:block>&nbsp;&nbsp;</td>
					<td  ><select id="cmsStatus" name="cmsStatus" class=" setItem" codeType="lms1205s01_cmsStatus" ></select></td>
					-->
				</tr>
			</table>
		</form>
				
			<!-- 地址 -->
			<div id="AddrThickBox" style="display:none;" >
				<div id="AddrDiv" >
					<form id="AddrForm" >
						<table class="tb2" width="100%">
							<tr>
								<td width="30%" class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'title.city'}">縣市別</th:block>&nbsp;&nbsp;</td>
								<td ><select id="AddrCity" name="AddrCity" class="required" ></select></td>
							</tr>
							<tr>
								<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'tilte.zip'}">鄉鎮市區</th:block>&nbsp;&nbsp;</td>
								<td><select id="AddrZip" name="AddrZip" class="required" ></select></td>
							</tr>
							<tr>
								<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'title.addr'}">地址</th:block>&nbsp;&nbsp;</td>
								<td><input id="AddrAddr" name="AddrAddr" class="max required" maxlength="150" size="50" /></td>
							</tr>
						</table>
						<input style="display:none;" /><!-- Prevent click enter to do submit -->
					</form>
				</div>
			</div>
        </th:block>
    </body>
</html>