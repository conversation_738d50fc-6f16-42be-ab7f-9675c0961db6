package com.mega.eloan.lms.eloandb.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.eloandb.service.LmsCustdataService;

@Service
public class LmsCustdataServiceImpl extends AbstractEloandbJdbc implements
		LmsCustdataService {

	
	@Override
	public Map<String, Object> findCustDataCname(String custId, String dupNo) {
		return this.getJdbc().queryForMap("CUSTDATA.FindCnamedata",
				new Object[] { custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findCustDataByCustId(String custId) {
		return this.getJdbc().queryForList("CUSTDATA.findCnameByCustId",
				new Object[] { custId });
	}

}
