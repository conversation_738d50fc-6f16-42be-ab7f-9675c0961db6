
var initDfd = $.Deferred(), inits = {
    fhandle: "lms8500m01formhandler",
    ghandle: "lms8500gridhandler"
};

// 該交易自有EVENT*************************************************************************
function initEvent(){
	
	// 上傳EXCEL功能
	$("#importCodeTypeExl").click(function(){
		$("#uploadCodeTypeFile").val('');
		var limitFileSize = 3145728*500;   // 3M * 100 = 300M
		var s = $.extend({
			handler: 'lms8500m01fileuploadhandler',
			fieldId: "uploadCodeTypeFile",
			title: i18n && i18n.def.insertfile || "請選擇附加檔案",
			fileCheck: ['xls'],
			limitSize: limitFileSize,
			successMsg: false,
			success: function(){
			},
			data: {
				fileSize: limitFileSize,
				deleteDup : true,
				changeUploadName: "uploadCodeTypeFile.xlsx"
			}
		}, s);
		
		$("#importByExl_codeTypeFile").thickbox({ // 使用選取的內容進行彈窗
			title : "引進CODETYPE共用參數",
			width : 500,
			height : 200,
			modal : true,
			i18n:i18n.def,
			buttons: (function(){
				var b = {};
				b['引進'] = function(){
					
					$.capFileUpload({
						handler: s.handler,
						fileCheck: s.fileCheck,
						fileElementId: s.fieldId,
						successMsg: s.successMsg,
						limitSize: limitFileSize,
						data: $.extend({
							fieldId: "uploadCodeTypeFile",
							mainId: responseJSON.mainId
						}, s.data || {}),
						success: function(json){
							$.thickbox.close();
							// 成功就是重整grid，讓資料可以顯示出來
							API.showPopMessage("引進成功");
							$("#gridview").trigger("reloadGrid");
						}
					});
				};
				b[i18n && i18n.def.cancel || "取消"] = function(){
					$.thickbox.close();
				};
				return b;
			})()
		});	 
	})
	
}

// 該交易自有function*************************************************************************
function initGrid(){
	var grid = $("#gridview").iGrid({
		handler: 'lms8500gridhandler',
		height: 350,
		width: 785,
		autowidth: false,
		action: "queryL850m01cV090",
		postData: {
			mainId: responseJSON.mainId
		},
		rowNum: 15,
		sortname: "createTime",
		sortorder: "desc",
		multiselect: true,
		colModel: [{
			colHeader: i18n.lms8500m01v09001["JSONDATA.codeType"], // 代碼類型
			name: 'codeType',
			align: "left", 
			width: 120, 
		}, {
			colHeader: i18n.lms8500m01v09001["JSONDATA.codeValue"],// "代碼值",
			name: 'codeValue',
			width: 50,
			align: "left"
		}, {
			colHeader: i18n.lms8500m01v09001["JSONDATA.codeDesc"],// "語言別",
			name: 'codeDesc',
			width: 80,
			align: "left"
		}, {
			colHeader: i18n.lms8500m01v09001["JSONDATA.codeOrder"],// "代碼順序",
			name: 'codeOrder',
			width: 80,
			align: "right"
		}, {
			colHeader: i18n.lms8500m01v09001["JSONDATA.locale"], // 語言別
			name: 'locale',
			width: 80,
			align: "center"
		}, {
			name: 'oid',
			hidden: true
		}]
	});
	
}

// 共用功能EVENT*************************************************************************
function initCommonEvent(){
	
	// 設定按鈕事件
	var btn = $("#buttonPanel");
	btn.find("#btnSave").click(function(showMsg){
		// 純儲存才show message
		saveData(true);
	}).end().find("#btnSend").click(function(){
		// 傳送並儲存不show message
		saveData(false, sendBoss);	
	}).end().find("#btnCheck").click(function(){
		openCheck();
	});
	
}

// 共用功能function*************************************************************************
function queryForm(){
	$.ajax({
		handler: inits.fhandle,
		data: {// 把資料轉成json
			formAction: "queryL850m01a",
			oid: responseJSON.oid
		},
		success: function(json){
			var $form = $("#mainPanel");
			$form.injectData(json);
			// 這個會有多位授信主管，所以要自己再重塞，<BR/>換行才會有效果
			$("#bossId").html(DOMPurify.sanitize(json.bossId));
		}
	});
}

// 驗證readOnly狀態
function checkReadonly(){
	var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
	if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
		return true;
	}
	return false;
}

// 儲存的動作
function saveData(showMsg, tofn){
	var $form = $("#mainPanel");
	
	if ($form.valid()) {
		$.ajax({
			handler: inits.fhandle,
			data: {// 把資料轉成json
				formAction: "saveL850m01a",
				oid: responseJSON.oid,
				page: responseJSON.page,
				txCode: responseJSON.txCode,
				showMsg: showMsg
			},
			success: function(obj){
				// 資料回寫回畫面上
				queryForm();
				
				// CommonAPI.triggerOpener("gridview", "reloadGrid");
				// 設定畫面是否需要儲存才可動作，應該多頁籤tab才會用到
				/*
				 * if ($("#mainOid").val()) { setRequiredSave(false); } else {
				 * setRequiredSave(true); }
				 */
				
				// 儲存有成功後，有後續要執行的method，就執行
				if(tofn){
					tofn();
				}
			}
		});
		
	}
}

// 檢查陣列內容是否重複
function checkArrayRepeat(arrVal){
	var newArray = [];
	for (var i = arrVal.length; i--;) {
		var val = arrVal[i];
		if ($.inArray(val, newArray) == -1) {
			newArray.push(val);
		}
		else {
			return true;
		}
	}
	return false;
}

// 流程EVENT*************************************************************************
function initFlowEvent(){
	
	// 呈主管覆核 選授信主管人數
	$("#numPerson").change(function(){
		$('#bossItem').empty();
		var value = $(this).val();
		if (value) {
			var html = '';
			for (var i = 1; i <= value; i++) {
				var name = 'boss' + i;
				html += i + '. '
				// || '授信主管'
				html += '<select id="' + name + '" name="boss"' +
				'" class="required" CommonManager="kind:2;type:2" />';
				html += '<br/>';
			}
			$('#bossItem').append(html).find('select').each(function(){
				$(this).setItems({
					item: item,
					format: "{value} {key}"
				});
			});
		}     
	});
	
}

// 流程function*************************************************************************
var item;
// 呈主管 - 編製中
function sendBoss(){
	$.ajax({
		handler: inits.fhandle,
		action: "checkData",
		data: {},
		success: function(json){
			$('#managerItem').empty();
			$('#bossItem').empty();
			item = json.bossList;
			var bhtml = '1. <select id="boss1" name="boss" class="required" CommonManager="kind:2;type:2"/>';
			$('#bossItem').append(bhtml).find('select').each(function(){
				$(this).setItems({
					item: item,
					format: "{value} {key}"
				});
			});
			var html = '<select id="manager" name="manager" class="required" CommonManager="kind:2;type:2" />';
			$('#managerItem').append(html).find('select').each(function(){
				$(this).setItems({
					item: item,
					format: "{value} {key}"
				});
			});
			
			// 是否呈主管覆核？
			CommonAPI.confirmMessage(i18n.lms8500m01v09001["L850M01A.message01"], function(b){
				if (b) {
					$("#selectBossBox").thickbox({
						// 覆核
						title: i18n.lms8500m01v09001['approve'],
						width: 500,
						height: 300,
						modal: true,
						readOnly: false,
						valign: "bottom",
						align: "center",
						i18n: i18n.def,
						buttons: {
							"sure": function(){
								
								var selectBoss = $("select[name^=boss]").map(function(){
									return $(this).val();
								}).toArray();
								
								for (var i in selectBoss) {
									if (selectBoss[i] == "") {
										// 請選擇授信主管
										return CommonAPI.showErrorMessage(i18n.lms8500m01v09001['checkSelect'] +
												i18n.lms8500m01v09001['L850M01A.bossId']);
									}
								}
								if ($("#manager").val() == "") {
									// 請選擇經副襄理
									return CommonAPI.showErrorMessage(i18n.lms8500m01v09001['checkSelect'] +
											i18n.lms8500m01v09001['L850M01A.managerId']);
								}
								// 驗證是否有重複的主管
								if (checkArrayRepeat(selectBoss)) {
									// 主管人員名單重複請重新選擇
									return CommonAPI.showErrorMessage(i18n.lms8500m01v09001['L850M01A.message02']);
								}
								
								flowAction({
									page: responseJSON.page,
									saveData: true,
									selectBoss: selectBoss,
									manager: $("#manager").val()
								});
								$.thickbox.close();
								
							},
							
							"cancel": function(){
								$.thickbox.close();
							}
						}
					});
				}
			});
		}
	});
}

// 待覆核 - 覆核
function openCheck(){
	$("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
		title: i18n.lms8500m01v09001['approve'],
		width: 100,
		height: 100,
		modal: true,
		readOnly: false,
		valign: "bottom",
		align: "center",
		i18n: i18n.def,
		buttons: {
			"sure": function(){
				var val = $("[name=checkRadio]:checked").val();
				if (!val) {
					return CommonAPI.showMessage(i18n.lms8500m01v09001['checkSelect']);
				}
				$.thickbox.close();
				switch (val) {
				case "1":
					// 一般退回到編製中01O
					// 該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
					CommonAPI.confirmMessage(i18n.lms8500m01v09001['L850M01A.message03'], function(b){
						if (b) {
							flowAction({
								flowAction: false
							});
						}
					}); 
					break;
				case "3":
					// 該案件是否確定執行核定作業
					CommonAPI.confirmMessage(i18n.lms8500m01v09001['L850M01A.message04'], function(b){
						if (b) {
							flowAction({
								flowAction: true,
								checkDate: CommonAPI.getToday()// forCheckDate
							});
						}
					});
					break;
				}
			},
			"cancel": function(){
				$.thickbox.close();
			}
		}
	});
}

function flowAction(sendData){
	$.ajax({
		handler: inits.fhandle,
		data: $.extend({
			formAction: "flowAction",
			oid: responseJSON.oid,
			mainId: responseJSON.mainId
		}, (sendData || {})),
		success: function(){
			CommonAPI.triggerOpener("gridview", "reloadGrid");
			API.showPopMessage(i18n.def["runSuccess"], window.close);
		}
	});
}


$(document).ready(function(){
	
	// function的設定直接寫再外層即可
	// 跑event定義
	initFlowEvent();
	initCommonEvent();
	initEvent();

	// 撈畫面資料
	queryForm();
	
	// codeType上傳資料的grid
	initGrid();
	
	$("#check1").show();

	// 控制畫面唯讀
    if (checkReadonly()) {
        $(".readOnlyhide").hide();
        $("form").lockDoc();
		_openerLockDoc="1";
    }

	
});