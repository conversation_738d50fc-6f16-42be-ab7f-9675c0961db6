package com.mega.eloan.lms.batch.service.impl;

import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.gwclient.SMEGFTPClient;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.constants.ZipUtils;
import com.mega.eloan.lms.dao.L270M01ADao;
import com.mega.eloan.lms.lns.service.LMS2701Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L270M01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 央行C方案傳送信保
 * </pre>
 * 
 * <AUTHOR>
 * @version <ul>
 * <li>2020/5/13,007625,new</li>
 * </ul>
 * @since 2020/5/13
 */

@Service("lmssmallbusscbatchserviceimpl")
public class LmsSmallBussCBatchServiceImpl extends AbstractCapService implements WebBatchService {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource
	SMEGFTPClient smegFtpClient;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	L270M01ADao l270M01ADao;

	@Resource
	LMS2701Service lms2701Service;


	// 可接受的檔案名稱
	private String[] acceptFileNames = new String[] { "QCFEEDBACK" };

	@Override
	public JSONObject execute(JSONObject json) {
		// 2.央行轉融通小額申請書介接檔
		// 3.央行C方案 整批 回饋檔update
 		JSONObject result = null;
		JSONObject rq = json.getJSONObject("request");
		String type = rq.getString("type");
		if ("2".equals(type)) {
			result = job2();
		} else if ("ftpDownload".equals(type)) {
			result = ftpGetAllFiles();
		} else if ("3".equals(type)) {
  			String fileName = rq.getString("f");
			result = job3(fileName);
		} else if ("job3All".equals(type)) {
			result = job3_ALL();
		} else if ("moveToBk".equals(type)) {
			result = moveFileToBk();
		}

		return result;
	}

	private JSONObject job2() {

		logger.info("----------產生央行轉融通小額申請書介接檔-----------");

		JSONObject result;
		Calendar today = Calendar.getInstance();
		int year = today.get(Calendar.YEAR);
		int month = today.get(Calendar.MONTH) + 1;
		int date = today.get(Calendar.DATE);
		int hour = today.get(Calendar.HOUR_OF_DAY);

		final String REPORT_NAME = "017";
		String fileName = REPORT_NAME + "" + (year - 1911) + String.format("%02d", month) + String.format("%02d",
				date) + String.format("%02d", hour) + "QC.TXT";
		String fileZipName = REPORT_NAME + "" + (year - 1911) + String.format("%02d", month) + String.format("%02d",
				date) + String.format("%02d", hour) + "QC.zip";

		//String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
		//"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ" + File.separator;

		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty("systemId")
		+ File.separator + "900" + File.separator + "LABOR_SMEQ" + File.separator + "017" + (year - 1911)
		+ String.format("%02d", month) + String.format("%02d", date) + String.format("%02d", hour) + "QC" + File.separator ;
		String localZipFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty("systemId")
		+ File.separator + "900" + File.separator + "LABOR_SMEQ" + File.separator ;

		ISearch searchTemplete = l270M01ADao.createSearchTemplete();
		searchTemplete.addSearchModeParameters(SearchMode.IS_NULL, "grntPaper", null);
		searchTemplete.addSearchModeParameters(SearchMode.EQUALS, "docStatus", "230");
		searchTemplete.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		searchTemplete.addSearchModeParameters(SearchMode.OR,
				new SearchModeParameter(SearchMode.IS_NULL, "dataStatus", null),
				new SearchModeParameter(SearchMode.EQUALS, "dataStatus", ""));

		searchTemplete.addOrderBy("ownBrId");
		searchTemplete.addOrderBy("custId");
		searchTemplete.setMaxResults(Integer.MAX_VALUE);
		List<L270M01A> l270m01as = l270M01ADao.find(searchTemplete);

		logger.info("l270m01as 資料筆數 {}", l270m01as.size());


		//取得該銀行的bankCode的資料
		List<Map<String, Object>> synBankList = misdbBASEService.findSynBankList("01", "017");
		Map<String, String> synbankcode = new HashMap<String, String>();

		for (Map<String, Object> data : synBankList) {
			String brnno = MapUtils.getString(data, "BRNNO", "");
			String brbkno = MapUtils.getString(data, "BRBKNO", "");
			synbankcode.put(brnno, brbkno);
		}

		JSONArray ja = new JSONArray();
		/*
		 * 取得本分行對信保保縣市的轉換代碼
		 */
		Map<String, String> cityMap = getCityMap();
		for (L270M01A l270m01a : l270m01as) {
			GINQF0QC ginqf0QC = new GINQF0QC(l270m01a, synbankcode, cityMap);
			JSONObject ginqf0QCjs = JSONObject.fromObject(ginqf0QC.toJson());
			ja.add(ginqf0QCjs);
		}


		if (ja.size() > 0) {
			try {
				File txtFile = new File(localFilePath, fileName);
				FileUtils.write(txtFile, ja.toString(), "UTF8");
				Timestamp now = CapDate.getCurrentTimestamp();

				for (L270M01A l270m01a : l270m01as) {
					l270m01a.setDataStatus("X");
					l270m01a.setBatchDate(now);
					lms2701Service.save(l270m01a);
				}
				String msgId = IDGenerator.getUUID();

				logger.info("[execute] FTP Client : {}", smegFtpClient.toString());

				//壓縮成OPWF.ZIP
				ZipUtils.zip(localFilePath, localZipFilePath, false, "123456");
				
				File txtFile_QC = new File(localZipFilePath, fileZipName);
				
				smegFtpClient.send(msgId, txtFile_QC.toString(), smegFtpClient.getServerDir() + "PAY_SEND", fileZipName,
						true, true, false);
				logger.info("[execute] FTP上傳結果： {} ", ToStringBuilder.reflectionToString(
						smegFtpClient.list(msgId, smegFtpClient.getServerDir() + "PAY_SEND")));
			} catch (Exception ex) {
				result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
				result.element(WebBatchCode.P_RESPONSE,
						this.getClass().getName() + "執行失敗！==>" + ex.getLocalizedMessage());
				logger.error(ex.getMessage(), ex);
				return result;
			}
		} else {
			logger.info("LMS.L270M01A is empty");
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "LmsSmallBussCBatchServiceImpl 執行成功！");
		return result;
	}

	private JSONObject job3_ALL() {
		JSONObject result;


		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ_FTP" + File.separator;

		File rootFolder = new File(localFilePath);

		File[] qcfeedbacks = rootFolder.listFiles(new FileFilter() {
			@Override
			public boolean accept(File pathname) {
				if (pathname.isDirectory()) {
					return false;
				} else if (pathname.getName().toUpperCase().contains("QCFEEDBACK")) {
					return true;
				} else {
					return false;
				}
			}
		});

		if (qcfeedbacks != null && qcfeedbacks.length > 0) {
			Arrays.sort(qcfeedbacks);
			for (File qcfeedback : qcfeedbacks) {
				JSONObject rs = job3(qcfeedback.getName());
				if (rs.getInt(WebBatchCode.P_RC) != 0) {
					return rs;
				}
			}
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "LmsSmallBussCBatchServiceImpl 執行成功！");

		return result;


	}
	
	private JSONObject moveFileToBk() {
		JSONObject result;
		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ_FTP" + File.separator;

		File root = new File(localFilePath);
		File[] files = root.listFiles();
		if (files != null && files.length > 0) {
			for (File file : files) {
				if (file.renameTo(new File(localFilePath + File.separator + "bk", file.getName()))) {

				} else {
					file.renameTo(new File(localFilePath + File.separator + "bk", file.getName() + "bk"));
				}

			}
		}
		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "LmsSmallBussCBatchServiceImpl 執行成功！");
		return result;

	}

	private JSONObject job3(String file) {

		logger.info("----------讀取央行C方案 整批 回饋檔-----------");

		JSONObject result;

		if (!file.toUpperCase().contains("QCFEEDBACK")) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName() + "執行失敗！==>" + "檔名" + file + "是錯誤的");
			return result;
		}


		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ_FTP" + File.separator;

		File bkFolder = new File(localFilePath + "bk");
		File inFile = new File(localFilePath, file);

		try {
			List<String> inTxts = FileUtils.readLines(inFile);
			String log = inTxts.get(0);
			logger.info("log result {}", log);

			String jsonTxt = inTxts.get(1);

			JSONArray jsonArrayLogs = JSONArray.fromObject(jsonTxt);

			//解析信保回傳的處理日期
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmss");

			for (int i = 0; i < jsonArrayLogs.size(); i++) {
				JSONObject jsLog = jsonArrayLogs.getJSONObject(i);
				String approveNo = jsLog.optString("ApproveNo");
				String custId = jsLog.optString("IdNo");
				String dataStatus = jsLog.optString("DataStatus");
				String receiveDay = String.valueOf(jsLog.getInt("ReceiveDay"));
				String receiveTime = StringUtils.leftPad(String.valueOf(jsLog.getInt("ReceiveTime")), 6, "0");
				receiveDay = (Integer.parseInt(receiveDay.substring(0, 3)) + 1911) + StringUtils.right(receiveDay, 4);

				String description = jsLog.optString("Description");
				if ("null".equals(description)) {
					description = null;
				} else {
					if (description.getBytes("UTF8").length > 600) {
						description = new String(description.getBytes(), 0, 600, "UTF8");
					}
				}

				List<L270M01A> l270m01as = l270M01ADao.findByApprNoCustId(approveNo,custId);
				if (l270m01as != null && l270m01as.size() > 0) {
					for (L270M01A l270m01a : l270m01as) {
						l270m01a.setDataStatus(dataStatus);
						l270m01a.setDescription(description);
						String receiveDate = receiveDay + receiveTime;
						try {
							Date _receiveDate = sdf.parse(receiveDate);
							l270m01a.setReceiveDay(new Timestamp(_receiveDate.getTime()));
						} catch (ParseException e) {
							logger.info(e.toString());
						}

						lms2701Service.save(l270m01a);
					}

				} else {
					logger.info("@@@@@@@@@@@@@@@@@@@@@@@@@@信保回傳統編" + custId + "在L270M01A內查無資料");
				}
			}

			if (!bkFolder.exists()) {
				FileUtils.forceMkdir(bkFolder);
			}
			//將執行完成的檔案備份到bk
			FileUtils.copyFile(inFile, new File(bkFolder.toString(), file));
			FileUtils.forceDelete(inFile);

		} catch (IOException ex) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName() + "執行失敗！==>" + ex.getLocalizedMessage());
			logger.error(ex.getMessage(), ex);
			return result;
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "LmsSmallBussCBatchServiceImpl 執行成功！");
		return result;
	}

	private JSONObject ftpGetAllFiles() {
		JSONObject result;

		try {
			String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
					"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ_FTP" + File.separator;
			FileUtils.forceMkdir(new File(localFilePath));
			String msgId = IDGenerator.getUUID();


			String[] ftpFiles = smegFtpClient.list(msgId, smegFtpClient.getServerDir() + "MAIN");
			for (String ftpFile : ftpFiles) {
				boolean match = false;
				if (ftpFile.toUpperCase().endsWith(".TXT")) {
					for (String acceptName : acceptFileNames) {//這邊會判斷只抓自己這隻有設定的txt
						if (ftpFile.contains(acceptName)) {
							match = true;
							break;
						}
					}
					if (match) {
						smegFtpClient.download(msgId, ftpFile, new File(localFilePath, ftpFile).toString(), true,
								smegFtpClient.getServerDir() + "MAIN");
						smegFtpClient.delete(msgId, ftpFile, smegFtpClient.getServerDir() + "MAIN");
					}
				}

			}

		} catch (IOException ex) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName() + "執行失敗！==>" + ex.getLocalizedMessage());
			logger.error(ex.getMessage(), ex);
			return result;
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "LmsSmallBussCBatchServiceImpl 執行成功！");
		return result;
	}


	/**
	 * 保證書申請檔json格式
	 */
	class GINQF0QC {

		public JSONObject toJson() {
			JSONObject js = new JSONObject();
			Field[] cols = CapBeanUtil.getField(GINQF0QC.class, false);

			for (Field field : cols) {
				try {
					if ("this$0".equals(field.getName())) {
						continue;
					}
					js.put(field.getName(), get(field.getName()));
				} catch (CapException e) {
					e.printStackTrace();
				}
			}

			return js;
		}

		public Object get(String fieldId) throws CapException {
			try {
				String field = fieldId;
				int index = fieldId.indexOf(".");
				if (index > 0) {
					field = fieldId.substring(0, index);
					Object keyClazz = get(field);
					if (keyClazz instanceof GenericBean) {
						return ((GenericBean) keyClazz).get(fieldId.substring(index + 1));
					}
				} else {
					String getter = new StringBuffer("get").append(String.valueOf(field.charAt(0)).toUpperCase())
							.append(field.substring(1))
							.toString();
					Method method = ReflectionUtils.findMethod(getClass(), getter);
					if (method == null) {
						getter = "is" + getter.substring(3);
						method = ReflectionUtils.findMethod(getClass(), getter);
					}
					if (method != null) {
						return method.invoke(this);
					} else {
						Field f = ReflectionUtils.findField(getClass(), field);
						if (f != null) {
							f.setAccessible(true);
							return f.get(this);
						}
					}
				}
				throw new CapException(new StringBuffer("field:").append(field).append(" is not exist!!").toString(),
						getClass());

			} catch (Exception e) {
				throw new CapException(e, getClass());
			}

		}// ;

		GINQF0QC(L270M01A meta, Map<String, String> synBank, Map<String, String> cityMap) {
			this.ApproveNo = StringUtils.left(meta.getApproveNo(), 50);//銀行批覆書號或案件編號
			this.BankNo = CapString.trimNull(synBank.get(meta.getOwnBrId()));
			this.ManagerNo = StringUtils.left(meta.getManagerNo(), 7);
			this.Typ = 130;//須固定帶130
			this.Ban = StringUtils.left(meta.getCustId(), 8);
			this.Company = StringUtils.left(meta.getCustName(), 30);
			this.Organ = meta.getOrgan();
			this.Owner = StringUtils.left(meta.getOwner(), 30);
			this.OwnerIdn = StringUtils.left(meta.getOwnerIdn(), 10);
			this.OwnerForeignerId = StringUtils.left(meta.getOwnerForeignerId(), 10);
			this.QualiCode = meta.getQualiCode();
			this.ApplyLoan = meta.getApplyLoan();
			this.TaxDay = Util.parseInt(CapDate.formatDate(meta.getTxnDay(), "YYYMMDD"));//後端須改傳民國年月七碼
			this.ApplyLoanDay = Util.parseInt(CapDate.formatDate(meta.getApplyLoanDay(), "YYYMMDD"));//後端須改傳民國年月七碼
			this.Trad6 = meta.getTrad6().intValue();
			this.Trad6Id = 8;//固定8
			this.Tel1 = StringUtils.left(meta.getTel1(), 2);
			this.Tel2 = StringUtils.left(meta.getTel2(), 8);
			this.OwnerCellphone1 = StringUtils.left(meta.getOwnerCellphone1(), 4);
			this.OwnerCellphone2 = StringUtils.left(meta.getOwnerCellphone2(), 6);
			this.IsEmail = meta.getEmail() != null ? 1 : 2;
			this.Email = StringUtils.left(meta.getEmail(), 50);
			this.Zip = BigDecimal.valueOf(Util.parseInt(meta.getZip()));
			//縣市代碼轉換為信保基金所需代碼
			String city = cityMap.get(meta.getCity());
			this.City = BigDecimal.valueOf(Util.parseInt(city));
			this.Dist = BigDecimal.valueOf(Util.parseInt(meta.getDist()));
			this.VillageName = StringUtils.left(meta.getVillageName(), 50);
			this.Village = meta.getVillage() == null ? null :BigDecimal.valueOf(meta.getVillage());
			this.Neighborhood = meta.getNeighborhood() == null ? null : BigDecimal.valueOf(meta.getNeighborhood());
			this.RoadName = StringUtils.left(meta.getRoadName(), 20);
			this.Road = meta.getRoad() == null ? 1 : meta.getRoad();//1(空白),2路,3街
			this.Sec = StringUtils.left(meta.getSec(), 4);
			this.Lane = StringUtils.left(meta.getLane(), 8);
			this.Alley = StringUtils.left(meta.getAlley(), 8);
			this.No1 = StringUtils.left(meta.getNo1(), 4);
			this.No2 = StringUtils.left(meta.getNo2(), 4);
			this.Floor1 = StringUtils.left(meta.getFloor1(), 3);
			this.Floor2 = StringUtils.left(meta.getFloor2(), 3);
			this.Room = StringUtils.left(meta.getRoom(), 3);
			this.ImporterName = StringUtils.left(meta.getImporterName(), 10);
			this.ImporterCellphone1 = StringUtils.left(meta.getImporterCellphone1(), 4);
			this.ImporterCellphone2 = StringUtils.left(meta.getImporterCellphone2(), 6);
			this.ImporterTel1 = StringUtils.left(meta.getImporterTel1(), 2);
			this.ImporterTel2 = StringUtils.left(meta.getImporterTel2(), 8);
			this.ImporterTelExt = StringUtils.left(meta.getImporterTelExt(), 5);
			this.ImporterEmail = StringUtils.left(meta.getImporterEmail(), 50);
			this.IsImporter = 1;//meta.getIsImporter();  1:是,2:否
			this.StaffName = StringUtils.left(meta.getImporterName(), 10);
			this.StaffCellphone1 = StringUtils.left(meta.getImporterCellphone1(), 4);
			this.StaffCellphone2 = StringUtils.left(meta.getImporterCellphone2(), 6);
			this.StaffTel1 = StringUtils.left(meta.getImporterTel1(), 2);
			this.StaffTel2 = StringUtils.left(meta.getImporterTel2(), 8);
			this.StaffTelExt = StringUtils.left(meta.getImporterTelExt(), 5);
			this.StaffEmail = StringUtils.left(meta.getImporterEmail(), 50);
			this.IsCreditCheck = 1;//固定1
			this.IsnCovProject = 1;//固定1
			this.IsnCovEffect = 1;//固定1
			this.IsTaxBan = 1;//固定1
			this.IsCreditAbnormal = 1;//固定1
			this.IsOverSimple70 = 1;//固定1
			this.IsOperating = 1;//固定1
			this.BeforeRate = meta.getBeforeRate();
			this.AfterRate = meta.getAfterRate();
			this.IsRealOwner = meta.getIsRealOwner();//meta.getRealOwner() != null ? 1 : 2
			this.RealOwner = StringUtils.left(meta.getRealOwner(), 30);
			this.RealOwnerIdn = StringUtils.left(meta.getRealOwnerIdn(), 10);
			this.RealOwnerForeignerId = StringUtils.left(meta.getRealOwnerForeignerId(), 10);
			this.IsOwnIdle = meta.getIsOwnIdle();//1:是,2:否
			this.Agreement = meta.getAgreement();//1:是,2:否
			this.IsCheckAddress = 1;
			this.IsUnregisteredFactory = 1;
			this.IsEligible = 1;

		}

		private String ApproveNo;
		private String BankNo;
		private String ManagerNo;
		private int Typ;
		private String Ban;
		private String Company;
		private Integer Organ;
		private String Owner;
		private String OwnerIdn;
		private String OwnerForeignerId;
		private Integer QualiCode;
		private BigDecimal ApplyLoan;
		private int TaxDay;
		private int ApplyLoanDay;
		private Integer Trad6;
		private int Trad6Id;
		private String Tel1;
		private String Tel2;
		private String OwnerCellphone1;
		private String OwnerCellphone2;
		private int IsEmail;
		private String Email;
		private BigDecimal Zip;
		private BigDecimal City;
		private BigDecimal Dist;
		private String VillageName;
		private BigDecimal Village;
		private BigDecimal Neighborhood;
		private String RoadName;
		private int Road;
		private String Sec;
		private String Lane;
		private String Alley;
		private String No1;
		private String No2;
		private String Floor1;
		private String Floor2;
		private String Room;
		private String ImporterName;
		private String ImporterCellphone1;
		private String ImporterCellphone2;
		private String ImporterTel1;
		private String ImporterTel2;
		private String ImporterTelExt;
		private String ImporterEmail;
		private int IsImporter;
		private String StaffName;
		private String StaffCellphone1;
		private String StaffCellphone2;
		private String StaffTel1;
		private String StaffTel2;
		private String StaffTelExt;
		private String StaffEmail;
		private int IsCreditCheck;
		private int IsnCovProject;
		private int IsnCovEffect;
		private int IsTaxBan;
		private int IsCreditAbnormal;
		private int IsOverSimple70;
		private int IsOperating;
		private BigDecimal BeforeRate;
		private BigDecimal AfterRate;
		private Integer IsRealOwner;
		private String RealOwner;
		private String RealOwnerIdn;
		private String RealOwnerForeignerId;
		private int IsOwnIdle;
		private int Agreement;
		private int IsCheckAddress;
		private int IsUnregisteredFactory;
		private int IsEligible;
		
		public String getApproveNo() {
			return ApproveNo;
		}

		public void setApproveNo(String approveNo) {
			ApproveNo = approveNo;
		}

		public String getBankNo() {
			return BankNo;
		}

		public void setBankNo(String bankNo) {
			BankNo = bankNo;
		}

		public String getManagerNo() {
			return ManagerNo;
		}

		public void setManagerNo(String managerNo) {
			ManagerNo = managerNo;
		}

		public int getTyp() {
			return Typ;
		}

		public void setTyp(int typ) {
			Typ = typ;
		}

		public String getBan() {
			return Ban;
		}

		public void setBan(String ban) {
			Ban = ban;
		}

		public String getCompany() {
			return Company;
		}

		public void setCompany(String company) {
			Company = company;
		}

		public Integer getOrgan() {
			return Organ;
		}

		public void setOrgan(Integer organ) {
			Organ = organ;
		}

		public String getOwner() {
			return Owner;
		}

		public void setOwner(String owner) {
			Owner = owner;
		}

		public String getOwnerIdn() {
			return OwnerIdn;
		}

		public void setOwnerIdn(String ownerIdn) {
			OwnerIdn = ownerIdn;
		}

		public String getOwnerForeignerId() {
			return OwnerForeignerId;
		}

		public void setOwnerForeignerId(String ownerForeignerId) {
			OwnerForeignerId = ownerForeignerId;
		}

		public int getQualiCode() {
			return QualiCode;
		}

		public void setQualiCode(int qualiCode) {
			QualiCode = qualiCode;
		}

		public BigDecimal getApplyLoan() {
			return ApplyLoan;
		}

		public void setApplyLoan(BigDecimal applyLoan) {
			ApplyLoan = applyLoan;
		}

		public int getTaxDay() {
			return TaxDay;
		}

		public void setTaxDay(int taxDay) {
			TaxDay = taxDay;
		}

		public int getApplyLoanDay() {
			return ApplyLoanDay;
		}

		public void setApplyLoanDay(int applyLoanDay) {
			ApplyLoanDay = applyLoanDay;
		}

		public int getTrad6() {
			return Trad6;
		}

		public void setTrad6(int trad6) {
			Trad6 = trad6;
		}

		public int getTrad6Id() {
			return Trad6Id;
		}

		public void setTrad6Id(int trad6Id) {
			Trad6Id = trad6Id;
		}

		public String getTel1() {
			return Tel1;
		}

		public void setTel1(String tel1) {
			Tel1 = tel1;
		}

		public String getTel2() {
			return Tel2;
		}

		public void setTel2(String tel2) {
			Tel2 = tel2;
		}

		public String getOwnerCellphone1() {
			return OwnerCellphone1;
		}

		public void setOwnerCellphone1(String ownerCellphone1) {
			OwnerCellphone1 = ownerCellphone1;
		}

		public String getOwnerCellphone2() {
			return OwnerCellphone2;
		}

		public void setOwnerCellphone2(String ownerCellphone2) {
			OwnerCellphone2 = ownerCellphone2;
		}

		public int getIsEmail() {
			return IsEmail;
		}

		public void setIsEmail(int isEmail) {
			IsEmail = isEmail;
		}

		public String getEmail() {
			return Email;
		}

		public void setEmail(String email) {
			Email = email;
		}

		public BigDecimal getZip() {
			return Zip;
		}

		public void setZip(BigDecimal zip) {
			Zip = zip;
		}

		public BigDecimal getCity() {
			return City;
		}

		public void setCity(BigDecimal city) {
			City = city;
		}

		public BigDecimal getDist() {
			return Dist;
		}

		public void setDist(BigDecimal dist) {
			Dist = dist;
		}

		public String getVillageName() {
			return VillageName;
		}

		public void setVillageName(String villageName) {
			VillageName = villageName;
		}

		public BigDecimal getVillage() {
			return Village;
		}

		public void setVillage(BigDecimal village) {
			Village = village;
		}

		public BigDecimal getNeighborhood() {
			return Neighborhood;
		}

		public void setNeighborhood(BigDecimal neighborhood) {
			Neighborhood = neighborhood;
		}

		public String getRoadName() {
			return RoadName;
		}

		public void setRoadName(String roadName) {
			RoadName = roadName;
		}

		public int getRoad() {
			return Road;
		}

		public void setRoad(int road) {
			Road = road;
		}

		public String getSec() {
			return Sec;
		}

		public void setSec(String sec) {
			Sec = sec;
		}

		public String getLane() {
			return Lane;
		}

		public void setLane(String lane) {
			Lane = lane;
		}

		public String getAlley() {
			return Alley;
		}

		public void setAlley(String alley) {
			Alley = alley;
		}

		public String getNo1() {
			return No1;
		}

		public void setNo1(String no1) {
			No1 = no1;
		}

		public String getNo2() {
			return No2;
		}

		public void setNo2(String no2) {
			No2 = no2;
		}

		public String getFloor1() {
			return Floor1;
		}

		public void setFloor1(String floor1) {
			Floor1 = floor1;
		}

		public String getFloor2() {
			return Floor2;
		}

		public void setFloor2(String floor2) {
			Floor2 = floor2;
		}

		public String getRoom() {
			return Room;
		}

		public void setRoom(String room) {
			Room = room;
		}

		public String getImporterName() {
			return ImporterName;
		}

		public void setImporterName(String importerName) {
			ImporterName = importerName;
		}

		public String getImporterCellphone1() {
			return ImporterCellphone1;
		}

		public void setImporterCellphone1(String importerCellphone1) {
			ImporterCellphone1 = importerCellphone1;
		}

		public String getImporterCellphone2() {
			return ImporterCellphone2;
		}

		public void setImporterCellphone2(String importerCellphone2) {
			ImporterCellphone2 = importerCellphone2;
		}

		public String getImporterTel1() {
			return ImporterTel1;
		}

		public void setImporterTel1(String importerTel1) {
			ImporterTel1 = importerTel1;
		}

		public String getImporterTel2() {
			return ImporterTel2;
		}

		public void setImporterTel2(String importerTel2) {
			ImporterTel2 = importerTel2;
		}

		public String getImporterTelExt() {
			return ImporterTelExt;
		}

		public void setImporterTelExt(String importerTelExt) {
			ImporterTelExt = importerTelExt;
		}

		public String getImporterEmail() {
			return ImporterEmail;
		}

		public void setImporterEmail(String importerEmail) {
			ImporterEmail = importerEmail;
		}

		public int getIsImporter() {
			return IsImporter;
		}

		public void setIsImporter(int isImporter) {
			IsImporter = isImporter;
		}

		public String getStaffName() {
			return StaffName;
		}

		public void setStaffName(String staffName) {
			StaffName = staffName;
		}

		public String getStaffCellphone1() {
			return StaffCellphone1;
		}

		public void setStaffCellphone1(String staffCellphone1) {
			StaffCellphone1 = staffCellphone1;
		}

		public String getStaffCellphone2() {
			return StaffCellphone2;
		}

		public void setStaffCellphone2(String staffCellphone2) {
			StaffCellphone2 = staffCellphone2;
		}

		public String getStaffTel1() {
			return StaffTel1;
		}

		public void setStaffTel1(String staffTel1) {
			StaffTel1 = staffTel1;
		}

		public String getStaffTel2() {
			return StaffTel2;
		}

		public void setStaffTel2(String staffTel2) {
			StaffTel2 = staffTel2;
		}

		public String getStaffTelExt() {
			return StaffTelExt;
		}

		public void setStaffTelExt(String staffTelExt) {
			StaffTelExt = staffTelExt;
		}

		public String getStaffEmail() {
			return StaffEmail;
		}

		public void setStaffEmail(String staffEmail) {
			StaffEmail = staffEmail;
		}

		public int getIsCreditCheck() {
			return IsCreditCheck;
		}

		public void setIsCreditCheck(int isCreditCheck) {
			IsCreditCheck = isCreditCheck;
		}

		public int getIsnCovProject() {
			return IsnCovProject;
		}

		public void setIsnCovProject(int isnCovProject) {
			IsnCovProject = isnCovProject;
		}

		public int getIsnCovEffect() {
			return IsnCovEffect;
		}

		public void setIsnCovEffect(int isnCovEffect) {
			IsnCovEffect = isnCovEffect;
		}

		public int getIsTaxBan() {
			return IsTaxBan;
		}

		public void setIsTaxBan(int isTaxBan) {
			IsTaxBan = isTaxBan;
		}

		public int getIsCreditAbnormal() {
			return IsCreditAbnormal;
		}

		public void setIsCreditAbnormal(int isCreditAbnormal) {
			IsCreditAbnormal = isCreditAbnormal;
		}

		public int getIsOverSimple70() {
			return IsOverSimple70;
		}

		public void setIsOverSimple70(int isOverSimple70) {
			IsOverSimple70 = isOverSimple70;
		}

		public int getIsOperating() {
			return IsOperating;
		}

		public void setIsOperating(int isOperating) {
			IsOperating = isOperating;
		}

		public BigDecimal getBeforeRate() {
			return BeforeRate;
		}

		public void setBeforeRate(BigDecimal beforeRate) {
			BeforeRate = beforeRate;
		}

		public BigDecimal getAfterRate() {
			return AfterRate;
		}

		public void setAfterRate(BigDecimal afterRate) {
			AfterRate = afterRate;
		}

		public Integer getIsRealOwner() {
			return IsRealOwner;
		}

		public void setIsRealOwner(Integer isRealOwner) {
			IsRealOwner = isRealOwner;
		}

		public String getRealOwner() {
			return RealOwner;
		}

		public void setRealOwner(String realOwner) {
			RealOwner = realOwner;
		}

		public String getRealOwnerIdn() {
			return RealOwnerIdn;
		}

		public void setRealOwnerIdn(String realOwnerIdn) {
			RealOwnerIdn = realOwnerIdn;
		}

		public String getRealOwnerForeignerId() {
			return RealOwnerForeignerId;
		}

		public void setRealOwnerForeignerId(String realOwnerForeignerId) {
			RealOwnerForeignerId = realOwnerForeignerId;
		}

		public int getIsOwnIdle() {
			return IsOwnIdle;
		}

		public void setIsOwnIdle(int isOwnIdle) {
			IsOwnIdle = isOwnIdle;
		}

		public int getAgreement() {
			return Agreement;
		}

		public void setAgreement(int agreement) {
			Agreement = agreement;
		}

		public int getIsCheckAddress() {
			return IsCheckAddress;
		}

		public void setIsCheckAddress(int isCheckAddress) {
			IsCheckAddress = isCheckAddress;
		}

		public int getIsUnregisteredFactory() {
			return IsUnregisteredFactory;
		}

		public void setIsUnregisteredFactory(int isUnregisteredFactory) {
			IsUnregisteredFactory = isUnregisteredFactory;
		}

		public int getIsEligible() {
			return IsEligible;
		}

		public void setIsEligible(int isEligible) {
			IsEligible = isEligible;
		}
		
	}


	private Map<String, String> getCityMap() {
		Map<String, String> cityMap = new HashMap<String, String>();
		cityMap.put("01", "1");
		cityMap.put("02", "10");
		cityMap.put("03", "2");
		cityMap.put("04", "13");
		cityMap.put("05", "11");
		cityMap.put("06", "12");
		cityMap.put("07", "6");
		cityMap.put("08", "20");
		cityMap.put("09", "3");
		cityMap.put("11", "22");
		cityMap.put("12", "23");
		cityMap.put("13", "24");
		cityMap.put("14", "30");
		cityMap.put("15", "7");
		cityMap.put("16", "4");
		cityMap.put("18", "5");
		cityMap.put("20", "33");
		cityMap.put("21", "40");
		cityMap.put("22", "41");
		cityMap.put("23", "34");
		cityMap.put("24", "43");
		cityMap.put("25", "42");
		return cityMap;
	}
}
