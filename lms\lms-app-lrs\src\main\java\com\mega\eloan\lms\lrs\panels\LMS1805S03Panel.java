/* 
 * LMS1805S03Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 覆審名單 - 附件
 * </pre>
 * 
 * @since 2012/04/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/04/26,irene
 *          </ul>
 */
public class LMS1805S03Panel extends Panel {

	public LMS1805S03Panel(String id) {
		super(id);
	}
	
	public LMS1805S03Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}
	
	private static final long serialVersionUID = 1L;

}
