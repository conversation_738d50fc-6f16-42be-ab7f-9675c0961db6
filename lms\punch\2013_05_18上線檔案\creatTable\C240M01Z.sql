---------------------------------------------------------
-- LMS.C240M01Z 每月覆審資料更新記錄檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.C240M01Z;
CREATE TABLE LMS.C240M01Z (
	OID           CHAR(32)      not null,
	DATADATE      DATE          not null,
	BRANCHID      CHAR(3)       not null,
	FINISHTIME    TIMESTAMP    ,

	constraint P_C240M01Z PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XLMS240M01Z;
CREATE UNIQUE INDEX LMS.XLMS240M01Z ON LMS.C240M01Z   (DATADATE, BRANCHID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C240M01Z IS '每月覆審資料更新記錄檔';
COMMENT ON LMS.C240M01Z (
	OID           IS 'oid', 
	DATADATE      IS '資料日期', 
	BRANCHID      IS '分行別', 
	FINISHTIME    IS '更新完成時間'
);
