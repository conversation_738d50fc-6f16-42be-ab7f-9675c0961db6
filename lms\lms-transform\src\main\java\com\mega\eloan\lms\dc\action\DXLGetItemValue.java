package com.mega.eloan.lms.dc.action;

import java.io.PrintWriter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.base.IKeyChecker;
import com.mega.eloan.lms.dc.base.OccursHander;
import com.mega.eloan.lms.dc.base.OccursKeyChecker;
import com.mega.eloan.lms.dc.base.SelectKeyChecker;
import com.mega.eloan.lms.dc.base.XMLHandler;
import com.mega.eloan.lms.dc.conf.BrnoConfig;
import com.mega.eloan.lms.dc.util.DXLUtil;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * DXLGetItemValue
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          </ul>
 */
public class DXLGetItemValue {
	private static final boolean DEBUG_MODE = false;
	private Logger logger = LoggerFactory.getLogger(DXLGetItemValue.class);

	private PrintWriter txtWrite;
	private PrintWriter LMSParserlogs = null;// 輸出log
	private PrintWriter xmlErrorLogs = null;// db2Xml屬性欄位值判斷log
	private PrintWriter dxlErrorLst = null;// dxl踢退清單
	private StringBuffer sbItemValue = new StringBuffer();
	private String dxlName = "";// 當前dxl檔名稱
	private String branch = "";// 當前分行名稱
	private String mainId = "";
	private String unid = "";
	private String unidDocId = "";
	private String strSeprate = ";";
	private String formName = "";
	private List<String> fieldList = new ArrayList<String>(); // DB2欄位名稱
	private int seqNo = 0;
	private int lthSeprate = strSeprate.length();
	private String occursTimesField = "";

	private String keyMan = null;// for itemType:keyman時使用,紀錄 notes.keyMan之值
	private String secNo1 = null;// for Table:L140M01N時使用,紀錄 notes.SecNo_1之值

	private HashMap<String, String> nameMap = new HashMap<String, String>();
	private HashMap<String, String> nullMap = new HashMap<String, String>();
	private HashMap<String, String> typeMap = new HashMap<String, String>();
	private HashMap<String, String> defaultMap = new HashMap<String, String>();
	private HashMap<String, String> numMap = new HashMap<String, String>();

	// UFO@20130121
	private IKeyChecker skeyChecker = new SelectKeyChecker();
	private XMLHandler xmlHandler = new XMLHandler();
	private IKeyChecker okeyChecker = null;
	private OccursHander occursHander = null;

	/**
	 * 合併dxl與DB2Xml
	 * 
	 * @param txtWrite
	 *            PrintWriter :準備輸出文字檔的物件
	 * @param db2Item
	 *            ParserDB2XML :DB2Xml轉換物件
	 * @param xmlName
	 *            String :DB2Xml檔名稱
	 * @param dxlPath
	 *            String :當前分行的dxl路徑
	 * @param dxlName
	 *            String :當前dxl檔名稱
	 * @param dxlXml
	 *            String :已轉換為String型態之dxl檔
	 * @param branch
	 *            Sring :當前分行名稱
	 * @param domDoc
	 *            Document :已轉換為DOM Document的當前dxl檔
	 * @param LMSParserlogs
	 *            PrintWriter :輸出log的物件
	 * @param xmlErrorLogs
	 *            PrintWriter :db2Xml屬性欄位值判斷log
	 * @param dxlErrorLst
	 *            PrintWriter :dxl踢退清單
	 * @version 2012/12/27 SandraPeng 微調log顯示內容
	 */
	public void mainProcess(PrintWriter txtWrite, ParserDB2XML db2Item,
			String xmlName, String dxlPath, String dxlName, String dxlXml,
			String branch, Document domDoc, PrintWriter LMSParserlogs,
			PrintWriter xmlErrorLogs, PrintWriter dxlErrorLst) {
		try {
			this.txtWrite = txtWrite;
			this.dxlName = StringEscapeUtils.escapeXml(dxlName);
			this.branch = branch;
			this.LMSParserlogs = LMSParserlogs;
			this.xmlErrorLogs = xmlErrorLogs;
			this.dxlErrorLst = dxlErrorLst;
			long tt1 = System.currentTimeMillis();
			xmlName = StringEscapeUtils.escapeXml(xmlName);
			this.LMSParserlogs.print("---目前正在解析讀取:>" + xmlName
					+ ">資料...dxl檔名為 : >" + this.dxlName + ">");
			initParser(db2Item, xmlName);
			this.seqNo = 1;
			parserAll(dxlXml, domDoc, xmlName);
			this.LMSParserlogs.println("讀取 TOTAL TIME(微秒)===>"
					+ (System.currentTimeMillis() - tt1) + ">");
		} catch (Exception e) {
			String errmsg = "DXLGetItemValue執行mainProcess發生錯誤:--DB2Xml名稱 : "
					+ xmlName + ", 當前dxl檔名稱 : " + this.dxlName + " 停止讀取";
			logger.error(errmsg, e);
			this.LMSParserlogs.println(errmsg);
			this.xmlErrorLogs.println(errmsg);
			e.printStackTrace(this.xmlErrorLogs);// 2013-01-24變更為寫入.err檔
			throw new DCException(this.branch + " 分行 執行" + errmsg, e);
		}
	}

	/**
	 * db2XML轉換為物件
	 * 
	 * @param db2Item
	 *            ParserDB2XML
	 * @param xmlName
	 *            String :DB2Xml檔名稱
	 */
	private void initParser(ParserDB2XML db2Item, String xmlName) {
		// this.keyOccurs = false;
		// this.bSelect = false;

		this.formName = db2Item.getFormName();
		// this.outFile = db2Item.getOutFile();
		this.nameMap = db2Item.getNameMap();
		this.nullMap = db2Item.getNullMap();
		this.typeMap = db2Item.getTypeMap();
		this.defaultMap = db2Item.getDefaultMap();
		this.numMap = db2Item.getNumMap();
		this.fieldList = db2Item.getFieldList();
		// listSize = fieldList.size();

		int occursTimes = db2Item.getOccursTimes();
		String occursFields = db2Item.getOccursFields();
		@SuppressWarnings("unused")
		String occursTimesField = db2Item.getOccursTimesField();
		if (StringUtils.isNotEmpty(occursFields)) {
			occursFields += ";";
		}

		String occursKeys = db2Item.getOccursKeys();
		if (StringUtils.isNotEmpty(occursKeys)) {
			occursKeys += ";";
		}
		String occursValues = db2Item.getOccursValues();
		String selectKeys = db2Item.getSelectKeys();
		String selectValues = db2Item.getSelectValues();

		this.skeyChecker.init(xmlHandler, selectKeys, selectValues);
		this.occursHander = new OccursHander(occursFields, occursKeys,
				occursTimes);
		this.okeyChecker = new OccursKeyChecker(this.occursHander);
		this.okeyChecker.init(xmlHandler, occursKeys, occursValues);

	}

	/**
	 * 
	 * @param dxlXml
	 *            String :已轉換為String型態之dxl檔
	 * @param domDoc
	 *            Document :已轉換為DOM Document的當前dxl檔
	 * @param xmlName
	 *            String :DB2Xml名稱
	 */
	private void parserAll(String dxlXml, Document domDoc, String xmlName)
			throws Exception {
		String strBrk = "firstRecord";
		boolean bFirstRecord = true; // First Record flag,設計保留備用
		String[] f1 = this.dxlName.split("_");// EX:{FLMS120M01
												// ,2E3761E1BB971A2B48257A7D00143D9C.dxl}
		String[] f2 = f1[1].split(TextDefine.ATTACH_DXL); // EX:{2E3761E1BB971A2B48257A7D00143D9C}
		if (this.formName.equals(f1[0])) {
			if (!strBrk.equals(f2[0])) {
				strBrk = f2[0];
				this.seqNo = 1; // Form Name 有Break時,序號從 1 開始
			} else {
				this.seqNo++;
			} // 序號加 1
			parseDxl(dxlXml, domDoc, bFirstRecord, xmlName);
			bFirstRecord = false;
		}
	}

	/**
	 * 
	 * @param dxlXml
	 *            String :已轉換為String型態之dxl檔
	 * @param domDoc
	 *            Document :已轉換為DOM Document的當前dxl檔
	 * @param bFirstRecord
	 * @param xmlName
	 *            String :DB2Xml名稱
	 */
	@SuppressWarnings("unused")
	private void parseDxl(String dxlXml, Document domDoc, boolean bFirstRecord,
			String xmlName) {
		long t1 = System.currentTimeMillis();
		try {
			String[] k1 = this.dxlName.split(TextDefine.ATTACH_DXL);// EX:{FLMS120M01_2E3761E1BB971A2B48257A7D00143D9C,.dxl}
			String[] k2 = k1[0].split("_");// EX:{FLMS120M01,2E3761E1BB971A2B48257A7D00143D9C}

			// 處理欄位資料,同一筆資料沒有occurs時occursTimes是1,有occurs時occursTimes是occurs的次數
			String times = xmlHandler.getItemValue(domDoc,
					this.occursTimesField);
			int maxOccurs = this.occursHander.getOccursValue(times);
			// for log Used
			for (int i = 1; i <= maxOccurs; i++) {
				this.sbItemValue.setLength(0);
				if (k2.length == 2) { // 主檔,如FCMS101M01
					this.unid = k2[1];
					this.mainId = k2[1];
					this.unidDocId = xmlHandler.getItemValue(domDoc,
							"UnidDocID");
				} else { // 明細檔,如FCMS101S01
					this.unid = k2[2];// 明細檔之UNID
					this.mainId = k2[1];// parentUNID
				}

				parseProcess(domDoc, dxlXml, i, xmlName, k2[0]);
			}
		} catch (Exception e) {

			String errmsg = this.branch
					+ " 分行 執行DXLGetItemValue之parseDxl發生錯誤:--DB2Xml名稱 : "
					+ xmlName + ", 當前dxl檔名稱 : " + this.dxlName + " 停止讀取";
			logger.error(errmsg, e);
			this.LMSParserlogs.println(errmsg);
			this.xmlErrorLogs.println(errmsg);
			e.printStackTrace(this.xmlErrorLogs);
			throw new DCException(this.branch + " 分行 執行" + errmsg, e);
		} finally {
			if (this.logger.isDebugEnabled() && DEBUG_MODE) {
				this.logger.debug("##### 處理 " + xmlName + "@" + this.dxlName
						+ " COST==>" + (System.currentTimeMillis() - t1));
			}
		}
	}

	/**
	 * 與DB2Xml對應後取得dxl內之值
	 * 
	 * @param domDoc
	 *            :dxl轉換為DOM XML後的xml檔
	 * @param dxlXml
	 *            String :已轉換為String型態之dxl檔
	 * @param occurs
	 *            int :occurs欄位目前執行次數
	 * @param xmlName
	 *            String :DB2Xml完整名稱
	 * @param dxlFromName
	 *            String :當前dxl檔對應的FormName名稱
	 */
	private void parseProcess(Document domDoc, String dxlXml, int occurs,
			String xmlName, String dxlFromName) throws Exception {
		if (this.skeyChecker.isUseChecker()) {
			if (!this.skeyChecker.check(domDoc, null)) {
				return;
			}
		}

		if (this.okeyChecker.isUseChecker()) {
			if (!this.okeyChecker.check(domDoc, occurs)) {
				return;
			}
		}
		for (int i = 0, size = this.fieldList.size(); i < size; i++) {
			String fldName = this.fieldList.get(i);// db2欄位名稱
			String itemName = this.occursHander.chkOccurs(
					this.nameMap.get(fldName).trim(), occurs);// db2Xml中的name屬性欄位
			String itemNull = this.nullMap.get(fldName);
			String itemType = this.typeMap.get(fldName);
			String itemDefault = this.defaultMap.get(fldName);
			String numValue = this.numMap.get(fldName);
			int itemNum = Integer.parseInt(Util.isNumeric(numValue) ? numValue
					: "-1");
			String itemNum_separated = this.numMap.get(fldName);

			String itemValue = "";
			StringBuffer sbItem = new StringBuffer();
			sbItem.setLength(0);

			// DB2Xml無附加檔名名稱
			String tmpXName = xmlName.split(".xml")[0];// FLMS140M01_L140M01A
			// DB2 Table
			String tableName = tmpXName.split("_")[1];// L140M01A

			/*
			 * 若dxlxml的formname為FLMS140M01
			 * ，且db2xml檔名為FLMS140M01_L140M01E，則CheckItem5 <> ""
			 * 才繼續執行，否則直接跳下一個db2xml
			 */
			// modify by Vance 因為現在FLMS140M01_L140M01E拆成10個
			//20130527 modify by Sandra依建霖來信註解以下條件判斷
			/*
			if ("FLMS140M01".equalsIgnoreCase(dxlFromName)
					&& tmpXName.startsWith("FLMS140M01_L140M01E")) {
				String item5 = xmlHandler.getItemValue(domDoc, "CheckItem5");
				if (StringUtils.isBlank(item5)) {
					return;
				}
			}
			 */
			// step 1
			if ("oid".equalsIgnoreCase(fldName)
					|| "mainId".equalsIgnoreCase(fldName)
					|| "srcMainId".equalsIgnoreCase(fldName)) {//20130603 modified by Sandra增加srcMainId的判斷
				if ("unid".equalsIgnoreCase(itemName)) {
					// 使用Form的unid
					itemValue = this.unid;
					// this.sbItemValue.append(this.unid + this.strSeprate);
				} else if ("parent".equalsIgnoreCase(itemName)) {
					// 明細檔使用Parent unid
					itemValue = this.mainId;
					// this.sbItemValue.append(this.mainId + this.strSeprate);
				} else if ("UnidDocID".equalsIgnoreCase(itemName)) {
					// 使用Document上的unidDocId
					itemValue = this.unidDocId;
					// this.sbItemValue.append(this.unidDocId +
					// this.strSeprate);
				} else if ("main_140".equalsIgnoreCase(itemName)) {// 20130419
																	// Sandra依建霖mail通知修改關聯key
					String cntrDocId = getAllItem(domDoc, "WEBELOANMAINID",
							occurs);
					itemValue = cntrDocId.isEmpty() ? this.unid : cntrDocId;
					/*
					 * this.sbItemValue.append(cntrDocId.isEmpty()?this.unid:
					 * cntrDocId); this.sbItemValue.append(this.strSeprate);
					 */
				} else if (!StringUtils.isBlank(itemName)) {
					// itemName 非空白時，直接取xml定義之值
					itemValue = getAllItem(domDoc, itemName, occurs);
					// this.sbItemValue.append(strOid + this.strSeprate);
				} else {
					itemValue = Util.getOID();
					// this.sbItemValue.append(strOid + this.strSeprate); //
					// 自行指定oid
				}
				this.sbItemValue.append(itemValue + this.strSeprate);
				checkNull(xmlName, fldName, itemName, itemValue, itemNull);
				continue;
			}

			// step 2
			// 有序號欄位:L140M01H
			if ("seq".equalsIgnoreCase(itemType)) {
				itemValue = String.valueOf(occurs);
			}
			// step 3
			if (itemName != null && itemName.length() > 0) { // 讀取dxl檔案欄位值
				if ("memo".equalsIgnoreCase(itemType)) {
					itemValue = this.xmlHandler.getItemValueByMup(domDoc,
							itemName);
				} else if ("ckeditor".equalsIgnoreCase(itemType)) {
					itemValue = this.xmlHandler.getItemValueByRichText(domDoc,
							itemName);
				} else if ("multi-val".equalsIgnoreCase(itemType)) {
					// 特殊itemType:multi-val處理
					// <txtlst><txt>AAAA</txt><txt>BBBB</txt>...</txtlst>
					// 須顯示為 AAAA;BBBB ..... 中間用分隔符號表示
					itemValue = this.xmlHandler.getItemValueByMup(domDoc,
							itemName, itemNum_separated);
				} else {
					itemValue = getAllItem(domDoc, itemName, occurs);
				}
			}

			// step 4
			if (StringUtils.isBlank(itemValue)) { // 無dxl檔案欄位值,設為預設質
				itemValue = itemDefault;
			}

			// 特別處理:若db2xml檔名為FLMS140M01_L140M01E且DB2.shareBrId欄位之itemValue為"",則該筆資料不產生
			// modify by Vance 因為現在FLMS140M01_L140M01E拆成10個
			if ("shareBrId".equalsIgnoreCase(fldName)
					&& tmpXName.startsWith("FLMS140M01_L140M01E")) {
				if (StringUtils.isBlank(itemValue)) {
					return;
				}
			}
			// step 5
			// 編製單位代號
			if ("ownBrId".equalsIgnoreCase(fldName)
					|| "branch".equalsIgnoreCase(fldName)) {
				if (StringUtils.isBlank(itemValue)) {
					itemValue = this.branch;
				}
			}
			// step 6
			// 針對type欄位指定的型態做必要的轉換
			ChkType ct = new ChkType();
			try {
				itemValue = ct.chkAllType(itemType, itemValue, itemNum, dxlXml);
			} catch (Exception ex) {
				this.writeErrorLog(xmlName, fldName, itemName, itemValue,
						"格式錯誤", ex);
			}

			// 特殊處理: L140M01A
			// 20130502 Sandra加上FLMS740M01的判斷
			if ("desp1".equalsIgnoreCase(fldName)
					&& ("FLMS140M01_L140M01A".equalsIgnoreCase(tmpXName) || "FLMS740M01_L140M01A"
							.equalsIgnoreCase(tmpXName))) {
				// 依以下條件判斷L140M01A.desp1的值
				// 若notes. UseDeadline(db2.useDeadline)=0 則desp1 = "不再動用"(註：字串)
				// 若notes. UseDeadline(db2.useDeadline)=1 則desp1 = notes.
				// FromDate~
				// notes.EndDate(註：字串，用~相接)
				// 若notes. UseDeadline(db2.useDeadline)=2 or 3 or 4 則desp1 =
				// notes.desp1
				// 若notes. UseDeadline(db2.useDeadline)=5 則desp1 = notes.desp2
				String _value = xmlHandler.getItemValue(domDoc, "UseDeadline");
				if ("0".equals(_value)) {
					itemValue = "不再動用";
				} else if ("1".equals(_value)) {
					itemValue = Util.parseDateToString(xmlHandler.getItemValue(
							domDoc, "FromDate"))
							+ "~"
							+ Util.parseDateToString(xmlHandler.getItemValue(
									domDoc, "EndDate"));
				} else if ("2".equals(_value) || "3".equals(_value)
						|| "4".equals(_value)) {
					itemValue = Util.getAllNumString(xmlHandler.getItemValue(
							domDoc, "desp2"));// 20130509 與建霖重新確認改取desp2
				} else if ("5".equals(_value)) {
					itemValue = xmlHandler.getItemValue(domDoc, "desp1");// 20130509
																			// 與建霖重新確認改取desp1
				}
			}

			// 特殊Table:L140M01N處理
			// L140M01N,Notes.SecNo_1適用期間為2、4時，適用期間起日(Notes.s_year_2)、適用期間迄日(Notes.f_year_2)為空白
			if ("SecNo_1".equalsIgnoreCase(itemName)
					&& tableName.equalsIgnoreCase("L140M01N")) {
				this.secNo1 = itemValue;
			}
			if (("s_year_2".equalsIgnoreCase(itemName) || "f_year_2"
					.equalsIgnoreCase(itemName))
					&& tableName.equalsIgnoreCase("L140M01N")) {
				if ("2".equals(this.secNo1) || "4".equals(this.secNo1)) {
					itemValue = TextDefine.EMPTY_STRING;
				}
			}

			// 特殊itemType:keyman處理
			if ("keyman".equalsIgnoreCase(itemName)
					&& "keyman".equalsIgnoreCase(itemType) && (0 == itemNum)) {
				// 01-18 : if notes.keyMan="Y"，then[0] ="Y" else if
				// notes.keyMan<>Y，則[0] = "N"
				if ("Y".equalsIgnoreCase(itemValue)) {
					this.keyMan = itemValue;
				} else {
					this.keyMan = "N";
					itemValue = "N";
				}
			}

			if ("custRlt".equalsIgnoreCase(fldName)
					&& "keyman".equalsIgnoreCase(itemType) && (1 == itemNum)) {
				// if notes.keyMan="Y",[1]=""(db2.custRlt) else if
				// notes.keyMan<>Y，[1]= notes.tLngere (db2.custRlt)
				if ("Y".equalsIgnoreCase(this.keyMan)) {
					itemValue = TextDefine.EMPTY_STRING;
				}
			}
			if ("custPos".equalsIgnoreCase(fldName)
					&& "keyman".equalsIgnoreCase(itemType) && (2 == itemNum)) {
				// if notes.keyMan="Y"，[2] =""(db2.custPos)
				if ("Y".equalsIgnoreCase(this.keyMan)) {
					itemValue = TextDefine.EMPTY_STRING;
				} else {
					// else notes.keyMan<>Y，
					// if notes.lngeflag_type(即db2.custPos)="" then
					// [2](即db2.custPos)="C"
					// else [2](即db2.custPos)
					// =notes.lngeflag_type(即db2.custPos)第1碼
					if (TextDefine.EMPTY_STRING.equals(itemValue)) {
						itemValue = "C";
					} else {
						itemValue = itemValue.substring(0, 1);
					}
				}
			}

			// type = NoInsuReason 特別處理
			if ("NoInsuReason".equalsIgnoreCase(itemType)) {
				if ("4".equalsIgnoreCase(Util.trimSpace(itemValue))) {
					itemValue = xmlHandler.getItemValue(domDoc,
							"NoInsuReason_1");
				} else {
					String desp1 = xmlHandler.getItemValue(domDoc,
							"NoInsuReason_Desp1");
					String desp2 = xmlHandler.getItemValue(domDoc,
							"NoInsuReason_Desp2");

					if (StringUtils.isNotBlank(desp1)) {
						itemValue = desp1;
					} else {
						itemValue = desp2;
					}
				}
			}

			// FLMS140S01~07
			// Rate1 = “@2”時才抓OTH_Rate
			if (dxlFromName.startsWith("FLMS140S0")
					&& "OTH_Rate".equalsIgnoreCase(itemName)) {
				String rate = Util.trimSpace(xmlHandler.getItemValue(domDoc,
						"Rate1"));
				if (!"@2".equalsIgnoreCase(rate)) {
					itemValue = "";
				}
			}

			// 特殊From處理
			if ("docCode".equalsIgnoreCase(itemType)) {
				// 若Form = FLMS130M01,if(notes.ReCheckCase不為空白
				// 且notes.E_isFlag=Y)則 retun "4"(授信異常通報)
				if ("FLMS130M01".equalsIgnoreCase(dxlFromName)) {
					String reCheckCase = xmlHandler.getItemValue(domDoc,
							"ReCheckCase");
					String eIsFlag = xmlHandler
							.getItemValue(domDoc, "E_isFlag");
					if (StringUtils.isNotBlank(reCheckCase)
							&& "Y".equalsIgnoreCase(eIsFlag)) {
						itemValue = "4";
					}
					// else if (notes.ReCheckCase不為空白且notes.E_isFlag不為Y)
					// 則retun"3"(陳復述案)
					else if (StringUtils.isNotBlank(reCheckCase)
							&& !"Y".equalsIgnoreCase(eIsFlag)) {
						itemValue = "3";
					}
					// else retun "2"(其他)
					else {
						itemValue = "2";
					}
				}
			}

			// 特殊projectNo處理
			if ("projectNo".equalsIgnoreCase(itemType) && itemNum == 1
					&& StringUtils.isNotBlank(itemValue)) {
				// 將ChkType判斷後取回的字串呼叫Brno.getBrno取回分行代碼，
				// 若無符合的分行代碼，則回傳"ERROR"+字串。
				// 特別轉換:If ChkType取回的字串=營業部 then字串="國際金融業務分行"
				// Else 字串 = 字串+"分行"
				if ("營業部".equals(itemValue.trim())) {
					itemValue = "國際金融業務分行";
				} else {
					itemValue = itemValue + "分行";
				}
				String brnoCode = BrnoConfig.getInstance().getBrNo(itemValue);
				if (DEBUG_MODE) {
					logger.debug("brnoCode :: " + itemValue + " ==>" + brnoCode);
				}
				if (StringUtils.isBlank(brnoCode)) {
					itemValue = "ERROR" + itemValue;
				} else {
					itemValue = brnoCode;
				}
			}

			// grant & type不是dockind才走這段
			if ("grant".equalsIgnoreCase(itemName)
					&& !"docKind".equalsIgnoreCase(itemType)) {
				itemValue = DXLUtil.getAuthLvl(itemValue);
			}

			// 轉換分行代號branch
			if ("branch".equalsIgnoreCase(itemType)) {
				itemValue = BrnoConfig.getInstance().getBrNo(
						getAllItem(domDoc, itemName, occurs));
				if (DEBUG_MODE) {
					logger.debug("#brnoCode :: " + itemValue);
				}
			}
			// -------------------------------------------------------------------------
			// 以下針對檢核後有問題的資料做處理
			// -------------------------------------------------------------------------
			// 以下欄位在轉檔時若遇到格式不正確，則一律轉為"000000"(新系統人員代碼都是6碼)
			// 【L140M01A,L230S01A,L120M01A】 DB2欄位名稱:【creator】
			// Notes欄位名稱:【Rpt_Creater】
			// 【L140M01A,L230S01A,L120M01A】 DB2欄位名稱:【updater】

			if (StringUtils.isNotBlank(itemValue)) {
				if (("Rpt_Creater".equalsIgnoreCase(itemName) || "Rpt_LastModify"
						.equalsIgnoreCase(itemName))
						&& "typer".equalsIgnoreCase(itemType) && (0 == itemNum)) {
					if (tableName.equalsIgnoreCase("L120M01A")
							|| tableName.equalsIgnoreCase("L140M01A")
							|| tableName.equalsIgnoreCase("L230S01A")) {
						if (itemValue.indexOf("ERR_") > -1) {
							itemValue = "000000";
						}
					}
				}
			}

			// 以下欄位在轉檔時若遇到格式不正確，則一律轉為"0001-01-01"
			// 【L120S01B.xml】 DB2欄位名稱:【estDate】 Notes欄位名稱:【Estb_Date】
			// DB2欄位名稱:【stockDate】Notes欄位名稱:【StockMkDate】
			if ("L120S01B".equalsIgnoreCase(tableName)) {
				if (StringUtils.isNotBlank(itemValue)) {
					if (itemValue.indexOf("ERR_") > -1) {
						if ("estDate".equalsIgnoreCase(fldName)
								|| "stockDate".equalsIgnoreCase(fldName)) {
							itemValue = "0001-01-01";
						}
					}
				}
			}

			// 2013/03/18 L120S01B :rgtUnit,cptlUnit 非數字:NULL
			if (StringUtils.isNotBlank(itemValue)) {
				if (("rgtUnit".equalsIgnoreCase(fldName) || "cptlUnit"
						.equalsIgnoreCase(fldName))
						&& tableName.equalsIgnoreCase("L120S01B")) {
					if (!Util.isNumeric(itemValue)) {
						itemValue = TextDefine.EMPTY_STRING;
					}
				}
			}

			// 2013/03/15 L140M01A :NPLDATE 清空
			if ("ym2ymd".equalsIgnoreCase(itemType)) {
				if (StringUtils.isNotBlank(itemValue)) {
					if (itemValue.indexOf("ERR_") > -1
							&& tableName.equalsIgnoreCase("L140M01A")) {
						if ("NPLDATE".equalsIgnoreCase(fldName)) {
							itemValue = TextDefine.EMPTY_STRING;
						}
					}
				}
			}
			// 2013/03/15 L140M01M :PAYPERCENT 非數字:NULL
			if (StringUtils.isNotBlank(itemValue)) {
				if ("PAYPERCENT".equalsIgnoreCase(fldName)
						&& tableName.equalsIgnoreCase("L140M01M")) {
					if (!Util.isNumeric(itemValue)) {
						itemValue = TextDefine.EMPTY_STRING;
					}
				}
			}
			// 2013/03/15 L140M01M :CTRATEMIN 非數字:NULL
			if (StringUtils.isNotBlank(itemValue)) {
				if ("CTRATEMIN".equalsIgnoreCase(fldName)
						&& tableName.equalsIgnoreCase("L140M01N")) {
					if (!Util.isNumeric(itemValue)) {
						itemValue = TextDefine.EMPTY_STRING;
					}
				}
			}

			// 特殊處理類別
			if ("groupNo".equalsIgnoreCase(itemType)
					|| "groupName".equalsIgnoreCase(itemType)) {
				String groupNo = xmlHandler.getItemValue(domDoc, "GpID");
				String groupName = xmlHandler.getItemValue(domDoc, "GpName");

				// modify by Vance
				// if (groupNo 有值且不為英數字){
				//
				// if(groupName為空值){
				// 就把 groupNo 的值放到 groupName
				// 再把 groupNo 清空
				// }else{
				// 把groupNo 清空
				// }
				// }
				if (StringUtils.isNotBlank(groupNo) && !Util.isEnOrNum(groupNo)) {
					if (StringUtils.isBlank(groupName)) {
						groupName = groupNo;
						groupNo = "";
					} else {
						groupNo = "";
					}
				}

				if ("groupNo".equalsIgnoreCase(itemType)) {
					itemValue = groupNo;
				} else {
					itemValue = groupName;
				}
			}

			// 2013/03/18 L120S01C :
			// DB2欄位名稱:【crdTYear】
			// Notes欄位名稱:【MOWYMD/MOWJUDGEDATE】空值時一律轉為"0001-01-01"
			// DB2欄位名稱:【finYear】 Notes欄位名稱:【MOWYMD/MOWJUDGEDATE】空值時一律轉為"0001"
			// DB2欄位名稱:【crdTBR】 Notes欄位名稱:【MOWBR】空值時一律轉為"   "
			// DB2欄位名稱:【crdType】 Notes欄位名稱:【mowType】空值時一律轉為" "
			if (tableName.equalsIgnoreCase("L120S01C")) {
				if ("crdTYear".equalsIgnoreCase(fldName)) {
					if (StringUtils.isBlank(itemValue)) {
						itemValue = "0001-01-01";
					}
				} else if ("finYear".equalsIgnoreCase(fldName)) {
					if (StringUtils.isBlank(itemValue)) {
						itemValue = "0001";
					}
				} else if ("crdTBR".equalsIgnoreCase(fldName)) {
					if (StringUtils.isBlank(itemValue)) {
						itemValue = "   ";
					}
				} else if ("crdType".equalsIgnoreCase(fldName)) {
					if (StringUtils.isBlank(itemValue)) {
						itemValue = " ";
					}
				}
			}
			// 2013/03/19 L140M01N :
			// DB2欄位名稱:【secBegDate】
			// Notes欄位名稱:【s_year_2】格式不正確時轉為""
			// DB2欄位名稱:【secBegDate】
			// Notes欄位名稱:【f_year_2】格式不正確時轉為""
			if (tableName.equalsIgnoreCase("L140M01N")) {
				if ("secBegDate".equalsIgnoreCase(fldName)
						|| "secEndDate".equalsIgnoreCase(fldName)) {
					if (itemValue.indexOf("ERR_") > -1) {
						itemValue = TextDefine.EMPTY_STRING;
					}
				}
			}

			// CDate日期格式錯誤之判斷
			if ("cdate".equalsIgnoreCase(itemType)) {
				if (StringUtils.isNotBlank(itemValue)) {
					if (itemValue.indexOf("ERR_") > -1) {
						this.writeErrorLog(xmlName, fldName, itemName,
								itemValue, "日期格式錯誤");
					}
				}
			}

			// 欄位值是否可為null之判斷
			checkNull(xmlName, fldName, itemName, itemValue, itemNull);

			/*
			 * if (itemNull.equalsIgnoreCase("N")) { if
			 * (StringUtils.isEmpty(itemValue)) {// 2013/03/18部份欄位植會以空格取代空值
			 * this.writeErrorLog(xmlName, fldName, itemName, itemValue,
			 * "其值不應為null"); } }
			 */
			this.sbItemValue.append(itemValue + this.strSeprate);
		} // end of for(int i = 0; i < listSize; i++)

		this.sbItemValue.setLength(this.sbItemValue.length() - this.lthSeprate);
		this.txtWrite.println(this.sbItemValue.toString());

	}

	/**
	 * 檢核內容是否為null
	 * 
	 * @param xmlName
	 * @param fldName
	 * @param itemName
	 * @param itemValue
	 * @param itemNull
	 */
	private void checkNull(String xmlName, String fldName, String itemName,
			String itemValue, String itemNull) {
		// 欄位值是否可為null之判斷
		if (itemNull.equalsIgnoreCase("N") && itemValue.length() == 0) {
			this.writeErrorLog(xmlName, fldName, itemName, itemValue,
					"其值不應為null");
		}
	}

	private void writeErrorLog(String xmlName, String fldName, String itemName,
			String itemValue, String errmsg) {
		this.writeErrorLog(xmlName, fldName, itemName, itemValue, errmsg, null);

	}

	private void writeErrorLog(String xmlName, String fldName, String itemName,
			String itemValue, String errmsg, Throwable ex) {
		String errstr = "分行名稱 【" + this.branch + "】;DXL名稱 【" + this.dxlName
				+ "】;" + "當前XML檔名稱 【" + xmlName + "】;" + "DB2欄位名稱 【" + fldName
				+ "】;" + "Notes欄位名稱 【" + itemName + "】;" + "欄位值 【" + itemValue
				+ "】;" + errmsg;
		this.xmlErrorLogs.println(errstr);

		// 寫入踢退清單
		this.dxlErrorLst.println(this.dxlName + TextDefine.SYMBOL_COLON
				+ "DB2欄位名稱【" + fldName + "】;" + "Notes欄位名稱【" + itemName + "】;"
				+ "欄位值【" + itemValue + "】" + errmsg);

		if (ex != null) {
			this.logger.error(errstr, ex);
		}
	}

	private String getAllItem(org.w3c.dom.Document domDoc, String itemName,
			int occurs) throws Exception {
		int idx = itemName.indexOf("/");
		if (idx > 0) {
			String[] itemList = StringUtils.split(itemName, '/');
			for (String nl : itemList) {
				String itemValue = xmlHandler.getItemValue(domDoc, nl);
				if (itemValue != null && itemValue.length() > 0) {
					return itemValue.trim();
				}
			}
			return "";
		}

		idx = itemName.indexOf(":");
		if (idx > 0) {
			String[] itemList = StringUtils.split(itemName, ':');
			StringBuffer sbValue = new StringBuffer();
			String newItemName = null;
			for (String nl : itemList) {
				// chkOccurs F Table不加這段會錯
				newItemName = this.occursHander.chkOccurs(nl.trim(), occurs);
				sbValue.append(xmlHandler.getItemValue(domDoc, newItemName));
			}
			return sbValue.toString().trim();
		}

		idx = itemName.indexOf("+");
		if (idx > 0) {
			String[] itemList = StringUtils.split(itemName, '+');
			StringBuffer sbW1 = new StringBuffer();
			StringBuffer sbW2 = new StringBuffer();
			sbW1.append("0");
			for (String nl : itemList) {
				sbW2.setLength(0);
				sbW2.append(add(sbW1.toString(),
						xmlHandler.getItemValue(domDoc, nl)));
				sbW1.setLength(0);
				sbW1.append(sbW2.toString());
			}
			return sbW1.toString();
		}
		idx = itemName.indexOf(";");
		if (idx > 0) {
			String[] itemList = StringUtils.split(itemName, ';');
			StringBuffer sbValue = new StringBuffer();
			for (String nl : itemList) {
				sbValue.append(xmlHandler.getItemValue(domDoc, nl)).append(";");
			}
			return sbValue.toString().trim().replaceAll(";$", "");
		}
		return xmlHandler.getItemValue(domDoc, itemName).trim();
	}

	private String add(String v1, String v2) {
		if (v1 == null || v1.length() == 0) {
			v1 = "0";
		}
		if (v2 == null || v2.length() == 0) {
			v2 = "0";
		}
		BigDecimal b1 = new BigDecimal(v1);
		BigDecimal b2 = new BigDecimal(v2);
		return b1.add(b2).toString();
	}

	/**
	 * 依順序查詢資料
	 * 
	 * @param key
	 * @param s
	 * @return
	 */
	public static int search(String key, String s) {
		String[] keyLst = key.split(";");
		for (int i = 0; i < keyLst.length; i++) {
			if (keyLst[i].equals(s)) {
				return i;
			}
		}
		return -1;
	}

}
