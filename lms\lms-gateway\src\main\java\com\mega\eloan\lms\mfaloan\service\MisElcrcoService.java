package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

public interface MisElcrcoService {
	public List<?> findElcrecomByIdDupno(String id, String dupno);

	public List<?> findElcrecomByIdDupno2(String id, String dupno);


	/**
	 * AML頁籤(徵信、簽報、動審等)，送掃範圍新增同一關係企業檔內的集團企業。
	 * 配合J-113-0443 目前只動國內要r01 r02 r03，海外還是維持r01 r02
	 *
	 * @param isObs 是否為海外
	 * @param id
	 * @param dupno
	 * @return
	 */
	public List<?> findElcrecomByIdDupnoForBlackList(boolean isObs, String id, String dupno);

	public List<?> findElcrecomByCustIdAndRCustId(String custId, String dupNo,
			String rCustId, String dupNo1);

	public List<?> findElcrecomByIdDupnoWithR01R02ExceptSelf(String id, String dupno);
	
	public List<Map<String, Object>> findElcrecomByIdDupnoWithR01R02R03ExceptSelf(String id, String dupno);
}
