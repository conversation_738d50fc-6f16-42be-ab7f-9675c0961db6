
package com.mega.eloan.lms.mfaloan.service.impl;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF490B;
import com.mega.eloan.lms.mfaloan.service.MisELF490BService;

/**
 * <pre>
 * 防杜代辦消金覆審資料檔
 * </pre>
 * 
 * @since 2019/4/10
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/4/10,EL08034,new
 *          </ul>
 */
@Service
public class MisELF490BServiceImpl extends AbstractMFAloanJdbc implements
MisELF490BService {

	@Override
	public List<Map<String, Object>> chooseCust_by_brNo_specificEmpNo(String beg_yyyyMMdd, String end_yyyyMMdd, String brNo, String empNo){
		return this.getJdbc().queryForListWithMax("ELF490B.chooseCust_by_brNo_specificEmpNo", new String[]{brNo+"%", brNo+"%", beg_yyyyMMdd, end_yyyyMMdd, empNo, empNo});
	}
	
	@Override
	public List<Map<String, Object>> chooseCust_by_brNo_empNo_R1toR5(String beg_yyyyMMdd, String end_yyyyMMdd, String brNo, String empNo){
		return this.getJdbc().queryForListWithMax("ELF490B.chooseCust_by_brNo_empNo_R1toR5", new String[]{brNo+"%", brNo+"%", beg_yyyyMMdd, end_yyyyMMdd, empNo, empNo});
	}
	
	@Override
	public List<Map<String, Object>> chooseCust_by_brNo_empNo_R6(String beg_yyyyMMdd, String end_yyyyMMdd, String brNo, String empNo){
		return this.getJdbc().queryForListWithMax("ELF490B.chooseCust_by_brNo_empNo_R6", new String[]{brNo+"%", brNo+"%", beg_yyyyMMdd, end_yyyyMMdd, empNo, empNo});
	}
	
	@Override
	public List<ELF490B> selBy_dataym_flag(String elf490b_data_ym,String elf490b_flag){
		return selBy_dataym_flag_brNo(elf490b_data_ym, elf490b_flag, "");
	}
	
	@Override
	public List<ELF490B> selBy_dataym_flag_brNo(String elf490b_data_ym, String elf490b_flag, String elf490b_brNo){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF490B.selBy_dataym_flag_brno", new String[]{elf490b_data_ym, elf490b_flag, elf490b_brNo+"%"});
		return toELF490B(rowData);
	}
	
	@Override
	public List<ELF490B> selBy_dataym_flag1_inOrder(String elf490b_data_ym,String elf490b_brNo){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF490B.selBy_dataym_flag1_inOrder", new String[]{elf490b_data_ym, elf490b_brNo+"%"});
		return toELF490B(rowData);
	}
	
	@Override
	public List<ELF490B> selBy_dataym_flag1_brNo_ruleNo_inOrder(String elf490b_data_ym, String elf490b_brNo, String elf490b_ruleNo){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF490B.selBy_dataym_flag1_brNo_ruleNo_inOrder", new String[]{elf490b_data_ym, elf490b_brNo, elf490b_ruleNo});
		return toELF490B(rowData);
	}
	
	@Override
	public List<ELF490B> selBy_dataym_flag3_ruleNo_areaNo(String brno_area, String elf490b_data_ym, String elf490b_ruleNo){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF490B.selBy_dataym_flag3_ruleNo_areaNo", new String[]{elf490b_data_ym, elf490b_ruleNo, brno_area});
		return toELF490B(rowData);
	}
	private List<ELF490B> toELF490B(List<Map<String, Object>> rowData){
		List<ELF490B> list = new ArrayList<ELF490B>();
		for (Map<String, Object> row : rowData) {
			ELF490B model = new ELF490B();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
}
