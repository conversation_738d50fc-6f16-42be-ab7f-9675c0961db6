/* 
 * CESConstant.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.constants;

import com.mega.eloan.common.constants.EloanConstants;

/**
 * <pre>
 * 徵信管理系統共用Constants
 * </pre>
 * 
 * @since 2011/7/28
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/7/28,iristu,new
 *          <li>2011/8/30,tammy<PERSON><PERSON>,新增洽談紀錄上傳碗卻代碼及理由
 *          <li>2011/10/13,sunkistWang,新增N.A.
 *          </ul>
 */
public interface CESConstant extends EloanConstants {

	static final String OPERATION_FORM = "eloanOperation";

	static final String OPERATION_GRID = "simpleOperation";

	final String MetaBean = "metaBean";

	/* 洽談紀錄表 */
	// 碗卻上傳
	final String RejtCode = "rejtCode";
	final String RJT_Reason = "CES_RJT_Reason";
	// 呈主管覆核 title
	final String Manager = "manager"; // 經/副/襄理
	final String Boss = "boss"; // 主管
	final String Boss1 = "boss1"; // 主管1
	final String Boss2 = "boss2"; // 主管2
	final String ReCheck = "reCheck"; // 帳戶管理員
	final String ReCheck1 = "reCheck1"; // 帳戶管理員1
	final String ReCheck2 = "reCheck2"; // 帳戶管理員2
	final String Appraiser = "appraiser"; // 經辦
	final String NA = "N.A."; // N.A.

	final String[] keyWords = { "formAction", "_pa", MAIN_OID, MAIN_ID,
			MAIN_UID, MAIN_PID, MAIN_DOC_STATUS, DOC_STATUS, TRANSACTION_CODE,
			PAGE, OID, TEMPSAVE_RUN, "openerLockDoc", "Auth", "FORCE_OPEN", "NOTIFY_MESSAGE", "_rad", DOC_READONLY};

}
