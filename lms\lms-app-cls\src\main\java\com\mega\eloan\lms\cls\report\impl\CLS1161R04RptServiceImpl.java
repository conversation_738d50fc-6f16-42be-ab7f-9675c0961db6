package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMS2501Service;
import com.mega.eloan.lms.cls.pages.CLS1161M04Page;
import com.mega.eloan.lms.cls.service.CLS1161Service;
import com.mega.eloan.lms.dc.util.Util;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L250M01A;
import com.mega.eloan.lms.model.L250M01B;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 產生模擬動審檢核表
 * 
 * <AUTHOR>
 * 
 */
@Service("cls1161r04rptservice")
public class CLS1161R04RptServiceImpl implements FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1161R04RptServiceImpl.class);

	@Resource
	LMS2501Service lms2501Service;

	@Resource
	UserInfoService userInfoService;

	@Resource
	CLS1161Service cls1161Service;
	
	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 */
	public OutputStream generateReport(PageParameters params) throws Exception {

		// 產生消金模擬動審檢核表
		OutputStream outputStream = genLMS2501R01(params);

		return outputStream;
	}

	@SuppressWarnings("unchecked")
	public OutputStream genLMS2501R01(PageParameters params) throws Exception {

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(CLS1161M04Page.class);

		Locale locale = LMSUtil.getLocale();
		String oid = params.getString(EloanConstants.MAIN_OID);
		ReportGenerator generator = new ReportGenerator(
				"report/cls/CLS1161R04_" + locale.toString() + ".rpt");

		OutputStream outputStream = null;

		Map<String, String> values = reNewHashMapParams();
		List<Map<String, String>> columnList = new ArrayList<Map<String, String>>();

		L250M01A meta = lms2501Service.findModelByOid(L250M01A.class, oid);
		JSONArray checkList = lms2501Service.getClsSavedList(meta);

		if (CollectionUtils.isNotEmpty(checkList)) {
			JSONObject type1 = checkList.getJSONObject(0);
			JSONObject type2 = checkList.getJSONObject(1);
			JSONArray groups1 = type1.getJSONArray("groups");
			JSONArray groups2 = type2.getJSONArray("groups");

			int groups1Length = groups1.size();
			int groups2Length = groups2.size();

			List<String> type1List = new ArrayList<String>();
			type1List.add(prop.getProperty("type1.hint1").replaceAll("<br>",
					"\r\n"));
			type1List.add(prop.getProperty("type1.hint2").replaceAll("<br>",
					"\r\n"));

			List<String> type2List = new ArrayList<String>();
			type2List.add(prop.getProperty("type2.hint1").replaceAll("<br>",
					"\r\n"));
			type2List.add(prop.getProperty("type2.hint2").replaceAll("<br>",
					"\r\n"));

			int maxLength = Math.max(groups1Length + type1List.size(),
					groups2Length + type2List.size());

			values.put("ReportBean.column03", "title");

			int tmp1 = 0;
			int tmp2 = 0;

			columnList.add(values);

			for (int i = 0; i < maxLength; i++) {

				values = reNewHashMapParams();

				StringBuffer txt1 = new StringBuffer();
				JSONArray subItems1 = null;
				if (i < groups1Length) {
					subItems1 = groups1.getJSONObject(i).getJSONArray(
							"subItems");

					for (int j = 0; j < subItems1.size(); j++) {
						JSONObject subItem = subItems1.getJSONObject(j);
						String _subItem = subItem.optString("subItem", "");
						String _subValue = subItem.optString("subValue", "");
						String _subTitle = subItem.optString("subTitle", "");
						if(Util.isEmpty(Util.trim(_subTitle))){
							continue;
						}
						if (j != 0) {
							txt1.append("\r\n");
						}

						if ("Y".equals(_subValue)) {
							txt1.append("■" + _subTitle);
						} else {
							txt1.append("□" + _subTitle);
						}
					}

				} else {
					if (i < (groups1Length + type1List.size())) {
						txt1.append(type1List.get(tmp1++));
					}
				}
				StringBuffer txt2 = new StringBuffer();
				JSONArray subItems2 = null;
				if (i < groups2Length) {
					subItems2 = groups2.getJSONObject(i).getJSONArray(
							"subItems");

					for (int j = 0; j < subItems2.size(); j++) {
						JSONObject subItem = subItems2.getJSONObject(j);
						String _subItem = subItem.optString("subItem", "");
						String _subValue = subItem.optString("subValue", "");
						String _subTitle = subItem.optString("subTitle", "");
						if(Util.isEmpty(Util.trim(_subTitle))){
							continue;
						}
						if (j != 0) {
							txt2.append("\r\n");
						}

						if ("Y".equals(_subValue)) {
							txt2.append("■" + _subTitle);
						} else {
							txt2.append("□" + _subTitle);
						}

					}
				} else {

					if (i < (groups2Length + type2List.size())) {
						txt2.append(type2List.get(tmp2++));
					}

				}

				values.put("ReportBean.column01", txt1.toString());
				values.put("ReportBean.column02", txt2.toString());
				columnList.add(values);
			}
		}

		Map<String, String> prompts = new HashMap<String, String>();

		List<L250M01B> l250m01bs = (List<L250M01B>) lms2501Service
				.findListByMainId(L250M01B.class, meta.getMainId());

		if (CollectionUtils.isNotEmpty(l250m01bs)) {
			StringBuffer sb = new StringBuffer();
			if ("Y".equals(meta.getAllCanPay())) {
				sb.append("全部動用");
			} else {
				for (L250M01B l250m01b : l250m01bs) {
					String cntrNo = l250m01b.getCntrNo();
					sb.append(cntrNo + ",");
				}
				sb.delete(sb.length() - 1, sb.length());
			}
			prompts.put("cntrNos", sb.toString());
		}

		prompts.put("custId", meta.getCustId());
		prompts.put("dupNo", meta.getDupNo());
		prompts.put("custName", meta.getCustName());
		prompts.put("caseDate",
				CapDate.formatDate(meta.getCaseDate(), "yyyy-MM-dd"));
		prompts.put("ownBrId", meta.getOwnBrId());
		prompts.put("caseNo", meta.getCaseNo());
		prompts.put("project1", meta.getProject1());
		prompts.put("project2", meta.getProject2());
		prompts.put("project3", meta.getProject3());
		prompts.put("project4", meta.getProject4());
		prompts.put("project5", meta.getProject5());
		prompts.put("project6", meta.getProject6());
		prompts.put("project7", meta.getProject7());

		prompts.put("managerId", this.getUserName(meta.getManagerId()));
		prompts.put("bossId", this.getUserName(meta.getBossId()));
		prompts.put("reCheckId", this.getUserName(meta.getReCheckId()));
		prompts.put("apprId", this.getUserName(meta.getApprId()));

		//J-108-0217_10702_B1001 消金界接IVR語音系統查詢及檢核
		String ivrString="錄音檔：<br>";
		boolean ivrFlag=false;
		for(L250M01B l250m01b:l250m01bs){
			if(!Util.isEmpty(l250m01b.getIVRFlag())){
				String reMainId=l250m01b.getReMainId();
				L140M01A l140m01a = cls1161Service.findModelByMainId(L140M01A.class, reMainId);
				String custName = l140m01a.getCustName();
				ivrString += Util.trim(custName+" "+l250m01b.getIVRFlag())+"<br>";
				ivrFlag =true;
			}
		}
		if(ivrFlag){
			prompts.put("ivrFlag", ivrString);
		}
		
		generator.setVariableData(prompts);

		generator.setRowsData(columnList);

		outputStream = generator.generateReport();

		return outputStream;

	}

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	private String getUserName(String userId) {
		// userId = "007625";
		if (Util.isEmpty(userId)) {
			return "";
		}

		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

	/**
	 * 初始化map 資料，將所有的rportBean的資料初使化，避免少了而產生exception
	 * 
	 * @return
	 */
	private Map<String, String> reNewHashMapParams() {
		Map<String, String> values = new HashMap<String, String>();
		for (int i = 1; i <= 60; i++) {
			values.put("ReportBean.column" + String.format("%02d", i), "");
		}
		return values;
	}

}
