/* 
 * VL784S07A01.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.VL784S07A01Dao;
import com.mega.eloan.lms.model.VL784S07A01;

/** 常董會明細 **/
@Repository
public class VL784S07A01DaoImpl extends LMSJpaDao<VL784S07A01, String> implements
VL784S07A01Dao {

	@Override
	public List<VL784S07A01> findByApprYY(String apprYY) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "apprYY",
				apprYY);
		List<VL784S07A01> list = createQuery(VL784S07A01.class, search)
				.getResultList();
		return list;
	}
}