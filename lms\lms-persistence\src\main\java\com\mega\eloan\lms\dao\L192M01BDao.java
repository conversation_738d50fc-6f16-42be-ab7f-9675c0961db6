package com.mega.eloan.lms.dao;

import java.util.List;


import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L192M01B;

import tw.com.iisi.cap.dao.IGenericDao;

public interface L192M01BDao extends IGenericDao<L192M01B> {
	int deleteByMeta(L192M01A meta);
	
	List<L192M01B> findByCustIdDupId(String custId,String DupNo);
	
	List<L192M01B> getL192M01Bs(String mainId, String mainCustId, String mainDupNo);
}
