/* 
 *MisELF442Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 預約額度序號檔(MIS.ELF442)
 * </pre>
 * 
 * @since 2012/2/10
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/10,REX,new
 *          </ul>
 */
public interface MisELF442Service {
	/**
	 * 取得預約額度序號
	 * 
	 * @param custId
	 *            客戶ID
	 * @param dupNo
	 *            重覆序號
	 * @param toDay
	 *            今天日期
	 * @return 預約額度序號清單
	 */
	public List<Map<String, Object>> findELF442ByCntrNo(String custId,
			String dupNo, String toDay);

	/**
	 * 取得預約額度序號 --限額控管
	 * 
	 * @param branchId
	 *            目前分行
	 * @param custId
	 *            客戶ID
	 * @param dupNo
	 *            重覆序號
	 * @param toDay
	 *            今天日期
	 * @return 預約額度序號清單
	 */
	public List<Map<String, Object>> findELF442ByCntrNoByKey(String branchId,
			String custId, String dupNo, String toDay);

	/**
	 * 更新預約額度序號 -狀態 用於呈主管
	 * 
	 * @param STATUS
	 *            案件狀態
	 * @param custId
	 *            客戶ID
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * 
	 */
	public void updateByforToBoss(String STATUS, String custId, String dupNo,
			String cntrNo, String mainId);

	/**
	 * 更新預約額度序號 狀態 -用於覆核 核准
	 * 
	 * @param STATUS
	 *            案件狀態
	 * @param agree_Flag
	 *            核准註記
	 * @param custId
	 *            客戶ID
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo額度序號
	 */
	public void updateForToCheck(String STATUS, String agree_Flag,
			String custId, String dupNo, String cntrNo, String mainId);

	/**
	 * 計算預約額度序號筆數
	 * 
	 * @param ELF442_BRANCH
	 *            分行號碼
	 * @param ELF442_CUSTID
	 *            客戶ID
	 * @param ELF442_DUPNO
	 *            重覆序號
	 * @param ELF442_CNTRNO
	 *            額度序號
	 * @return
	 */
	public List<Map<String, Object>> selByCount(String ELF442_BRANCH,
			String ELF442_CUSTID, String ELF442_DUPNO, String ELF442_CNTRNO);

	/**
	 * 更新預約額度檔
	 * 
	 * @param ELF442_RISK_CNTRY
	 *            風險國別
	 * @param ELF442_RISK_AREA
	 *            風險區域別
	 * @param ELF442_BUS_CD
	 *            行業別
	 * @param ELF442_BUS_SUB_CD
	 *            行業別細項
	 * @param ELF442_BRANCH
	 *            分行代號
	 * @param ELF442_CUSTID
	 *            客戶統編
	 * @param ELF442_DUPNO
	 *            重覆序號
	 * @param ELF442_CNTRNO
	 *            額度序號
	 */
	public void updateByother(String ELF442_RISK_CNTRY,
			String ELF442_RISK_AREA, String ELF442_BUS_CD,
			String ELF442_BUS_SUB_CD, String ELF442_BRANCH,
			String ELF442_CUSTID, String ELF442_DUPNO, String ELF442_CNTRNO,
			String mainId);

	/**
	 * 取得預約額度序號 --用於授管處檢查
	 * 
	 * @param branchId
	 *            目前分行
	 * @param custId
	 *            客戶ID
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return
	 * 
	 *         多了這個AND ELF442_PROVED_FLAG ='Y'
	 */
	public List<Map<String, Object>> findELF442ByCntrNoByCheck(String branchId,
			String custId, String dupNo, String cntrNo);

	/**
	 * 查詢是否為預約額度序號 --用於300億預約額度檢查
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @param CLASS_CODE
	 *            類別
	 * @return
	 */
	public Map<String, Object> findELF442ByKey(String custId, String dupNo,
			String cntrNo, String CLASS_CODE);

	/**
	 * 取得預約額度序號 --檢核額度明細表/批覆書之幣別與額度是否與預約額度一致
	 * 
	 * @param branchId
	 *            目前分行
	 * @param custId
	 *            客戶ID
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號 <br/>
	 *            無AND ELF442_PROVED_FLAG ='Y'
	 * @return
	 */
	public List<Map<String, Object>> findELF442ByCntrNoByCheck2(
			String branchId, String custId, String dupNo, String cntrNo);

	/**
	 * 取得預約額度序號 --檢核額度明細表/批覆書之幣別與額度是否與預約額度一致
	 * 
	 * @param custId
	 *            客戶ID
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號 <br/>
	 *            無AND ELF442_PROVED_FLAG ='Y'
	 * @return
	 */
	public List<Map<String, Object>> findELF442ByCntrNoByCheck3(String custId,
			String dupNo, String cntrNo);

	/**
	 * 更新預約額度序號 狀態 -用於覆核 婉卻
	 * 
	 * @param STATUS
	 *            案件狀態
	 * @param agree_Flag
	 *            核准註記
	 * @param custId
	 *            客戶ID
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo額度序號
	 */
	public void updateForToReject(String status, String agree_Flag,
			String custId, String dupNo, String cntrNo, String mainId);

	/**
	 * 土建融預約額度
	 * 
	 * @param custId
	 * @param dupNo
	 * @param toDay
	 * @return
	 */
	public List<Map<String, Object>> findELF442ByLandBuild(String custId,
			String dupNo, String toDay);

	/**
	 * J-107-0035-001 Web e-Loan授信系統配合集團企業之「預約額度情形彙整表」，修改預約額度相關功能。
	 */
	public void updateOnlyMainId(String custId, String dupNo, String cntrNo,
			String mainId);

	/**
	 * J-110-0455_05097_B1001 企金授信簽報書新增72-2簽案預約控管 檢核72-2
	 */
	public List<Map<String, Object>> findELF442ByCntrNoByCheckFor722(
			String branchId, String custId, String dupNo, String cntrNo);

	/**
	 * J-111-0163_05097_B1001 Web e-Loan企金國內、海外簽報書新增中租集團(代號1208)預約額度檢核作業
	 */

	public List<Map<String, Object>> findELF442ByCntrNoByCheckForGrp07(
			String branchId, String custId, String dupNo, String cntrNo);

	/**
	 * J-111-0397_05097_B1001 Web e-Loan國內企金授信新增權加權風險性資產(RWA)相關計算與預約機制
	 */
	public List<Map<String, Object>> findELF442ByCntrNoByCheckForRwa(
			String branchId, String custId, String dupNo, String cntrNo);

	/**
	 * J-113-0334 企金RWA預約檢查預約額度
	 *
	 * @param branchId
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	List<Map<String, Object>> findELF442ByCntrNoByCheckForRwaCase2(
			String branchId, String custId, String dupNo, String cntrNo);

	/**
	 * 簽報書退回時，將預約額度資料還原
	 *
	 * @param elf442RptMainId
	 */
	void backELF442ByRptMainId(String elf442RptMainId);

	/**
	 * J-110-0455_05097_B1001 企金授信簽報書新增72-2簽案預約控管 檢核非72-2
	 */
	public List<Map<String, Object>> findELF442ByCntrNoByCheckForGeneral(
			String branchId, String custId, String dupNo, String cntrNo);

	/**
	 * 依客戶取得特定ELF442_CLASS_CODE(預約額度 09: 行業對象別申請確認暨修改)
	 * @param custId
	 * @param dupNo
	 * @param classCode
	 * @param
	 */
	public List<Map<String, Object>> findELF442ByCustIdByClassCode(String custId,
			String dupNo, String classCode);
}
