/* 
 * LMS7010ServiceiMPL.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.dao.L800M01ADao;
import com.mega.eloan.lms.fms.service.LMS7010Service;
import com.mega.eloan.lms.model.L800M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * 常用主管資料 Service
 * 
 * @since 2011/9/29
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/10/29,Vector Lo,new
 *          </ul>
 */
@Service
public class LMS7010ServiceImpl extends AbstractCapService implements
		LMS7010Service {
	private static final Logger logger = LoggerFactory
			.getLogger(LMS7010ServiceImpl.class);

	@Resource
	L800M01ADao l800m01adao;

	@Override
	public void deleteListL800m01a(String[] oidArray) {
		// 刪除多筆資料
		for (int i = 0; i < oidArray.length; i++) {
			this.deleteL800m01a(oidArray[i]);
		}
	}

	@Override
	public void deleteL800m01a(String oid) {
		L800M01A model = findL800m01aByOid(oid);
		l800m01adao.delete(model);
	}

	/**
	 * <pre>
	 * 進行無限多筆儲存
	 * (non-Javadoc)
	 * @see com.mega.eloan.lms.service.LMS1205Service#save(tw.com.iisi.cap.model.GenericBean[])
	 * </pre>
	 */
	@Override
	public void save(GenericBean... entity) {
		// 進行無限多筆儲存
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				try {// 設定更新與建立人員
					if (Util.isEmpty(model.get(EloanConstants.OID))) {// 有OID=>已存在=>不更改建立人員
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}
					model.set("updater", user.getUserId());
					model.set("updateTime", CapDate.getCurrentTimestamp());
				} catch (CapException e) {
					logger.error("CapException!!", e);
				}

				if (model instanceof L800M01A) {
					l800m01adao.save((L800M01A) model);
					// 記錄文件異動記錄
					// docLogService.record(model.getOid(),DocLogEnum.SAVE);
				}
			}
		}
	}

	/**
	 * <pre>
	 * 進行無限多筆刪除
	 * (non-Javadoc)
	 * @see com.mega.eloan.lms.service.LMS1205Service#delete(tw.com.iisi.cap.model.GenericBean[])
	 * </pre>
	 */
	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L800M01A) {
					l800m01adao.delete((L800M01A) model);
				}
			}
		}
	}

	/**
	 * <pre>
	 * 取得 DataPage
	 * (non-Javadoc)
	 * @see com.mega.eloan.lms.service.LMS1205Service#findPage(java.lang.Class, tw.com.iisi.cap.dao.utils.ISearch)
	 * </pre>
	 */
	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L800M01A.class) {
			return l800m01adao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		return null;
	}

	@Override
	public L800M01A findL800m01aByOid(String oid) {
		return l800m01adao.findByOid(oid);
	}

	@Override
	public List<L800M01A> findL800m01aByZhuGuan(String zhuGuan, String brno) {
		ISearch search = l800m01adao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "zhuGuan", zhuGuan);
		search.addSearchModeParameters(SearchMode.EQUALS, "brno", brno);
		return l800m01adao.find(search);
	}
}
