package com.mega.eloan.lms.las.pages;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.mega.eloan.lms.base.pages.AbstractPdfReportPage;

/**
 * 房貸業務工作底稿基本內容列印
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/las/lms1905r01")
public class LMS1905R01Report extends AbstractPdfReportPage {

	public LMS1905R01Report() {
		super();
	}

	@Override
	public String getDownloadFileName() {
		return "lms1905r01.pdf";
	}

	@Override
	public String getFileDownloadServiceName() {
		return "lms1905r01rptservice";
	}
	
	//UPGRADE：或可參照LMS1200P01Page.java的方式return
	@Override
	protected String getViewName() {
		return null;
	}
}

