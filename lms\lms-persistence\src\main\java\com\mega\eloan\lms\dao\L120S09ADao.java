/* 
 * L120S09ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S09A;

/** 洗錢防制明細檔 **/
public interface L120S09ADao extends IGenericDao<L120S09A> {

	L120S09A findByOid(String oid);

	List<L120S09A> findByMainId(String mainId);

	List<L120S09A> findByIndex01(String mainId);

	public List<L120S09A> findL120S09AListByOids(String[] oids);

	public List<L120S09A> findByMainIdAndCustIdDupNo(String mainId,
			String custId, String dupNo);

	public List<L120S09A> findByMainIdAndCustName(String mainId, String custName);
	
	public L120S09A findMaxQDateByMainId(String mainId);
	
	public List<L120S09A> findByMainIdWithOrder(String mainId) ;
	
	List<L120S09A> findByMainIdWithOrder1(String mainId);
}