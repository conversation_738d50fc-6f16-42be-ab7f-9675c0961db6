/* 
 * L120S24ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L120S24CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S24C;

/** 風控風險權數主檔 **/
@Repository
public class L120S24CDaoImpl extends LMSJpaDao<L120S24C, String>
	implements L120S24CDao {

	@Override
	public L120S24C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L120S24C findByGradeAndVersionDate(String grade, String versionDate){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "grade", grade);
		search.addSearchModeParameters(SearchMode.EQUALS, "versionDate", versionDate);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<L120S24C> findAll(String versionDate) {
		ISearch search = createSearchTemplete();
		search.setMaxResults(Integer.MAX_VALUE);
		search.addSearchModeParameters(SearchMode.EQUALS, "versionDate", versionDate);
		List<L120S24C> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L120S24C findCentralGovNextRW(String centralGov, String versionDate){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.GREATER_THAN, "centralGov", new BigDecimal(centralGov));
		search.addSearchModeParameters(SearchMode.EQUALS, "versionDate", versionDate);
		// 要做排序，因為只抓"下一級"
		search.addOrderBy("centralGov");
		return findUniqueOrNone(search);
	}
}