var panelAction = {
    handler: 'cls1161formhandler',
    gridhandler: 'cls1161gridhandler',
    L120M01AGrid: null, //簽報書
    L140M01AGrid: null, //額度明細表
    L140M01AGrid2: null, //額度明細表
    caseType: '', //動審表種類
    caseData: null,
    init: function(){

    },
    build: function(){
        
        //選擇額度明細表
        $('body').find('#selectCaseTableBt').click(function(){
            panelAction.caseType = '1';
            panelAction.openCaseType1();
        });
        
        //簽報書Grid
        panelAction.L120M01AGrid = $('#L120M01AGrid').iGrid({
            localFirst: true,
            handler: panelAction.gridhandler, //設定handler
            height: 250, //設定高度
            action: 'L120M01AQuery', //執行的Method
            rowNum: 15,
            rownumbers: true,
            colModel: [{
                name: 'oid',
                hidden: true //是否隱藏
            }, {
                name: 'mainId',
                hidden: true //是否隱藏
            }, {
                name: 'docKind',
                hidden: true //是否隱藏
            }, {
                colHeader: i18n.cls1161m04["l250m01a.grid.001"], //統一編號
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'custId'
            }, {
                colHeader: i18n.cls1161m04["l250m01a.grid.002"], //簽案日期
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'caseDate'
            }, {
                colHeader: i18n.cls1161m04["l250m01a.grid.003"], //核准日期
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'approveTime'
            }, {
                colHeader: i18n.cls1161m04["l250m01a.grid.004"], //案 號
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'caseNo'
            }]
        });
        
        
        
        var setting = {
            localFirst: true,
            handler: panelAction.gridhandler, //設定handler
            height: 150, //設定高度
            action: 'L140M01AQueryForSimu', //執行的Method
            rowNum: 1000,
            pgbuttons: false, // disable page control like next, back button
            pgtext: null, // disable pager text like 'Page 0 of 10'
            rownumbers: true,
            multiselect: true, //是否開啟多選
            colModel: [{
                name: 'oid',
                hidden: true //是否隱藏
            }, {
                name: 'mainId',
                hidden: true //是否隱藏
            }, {
                colHeader: i18n.cls1161m04["l250m01a.grid.001"], //統一編號
                align: "left",
                width: 70, //設定寬度
                sortable: true, //是否允許排序
                name: 'custId'
            }, {
                colHeader: i18n.cls1161m04["l250m01a.grid.005"], //客戶名稱
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'custName'
            }, {
                colHeader: i18n.cls1161m04["l250m01a.grid.006"], //額度序號
                align: "left",
                width: 70, //設定寬度
                sortable: true, //是否允許排序
                name: 'cntrNo'
            }, {
                colHeader: i18n.cls1161m04["l250m01a.grid.004"], //案號
                align: "left",
                width: 160, //設定寬度
                sortable: true, //是否允許排序
                name: 'caseNo'
            }, {
                colHeader: i18n.cls1161m04["l250m01a.grid.003"], //核准日期
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'approveTime'
            }]
        }
        //額度明細表Grid
        panelAction.L140M01AGrid = $('#L140M01AGrid').iGrid($.extend({
            loadComplete: function(){
            
            }
        }, setting));
        
    },
    /**
     * 選擇額度明細表
     */
    openCaseType1: function(){
        $('#CaseType1ThickBox').thickbox({
            title: i18n.def['query'],
            width: 400,
            height: 200,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    var $form = $('#CaseType1Form');
                    if ($form.valid()) {
                        $.thickbox.close();
                        panelAction.openL120M01A($form.serializeData());
                    }
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
    },
    
    
    
    
    /**
     * 開啟簽報書
     */
    openL120M01A: function(data){
        $('#L120M01AThickBox').thickbox({
            title: i18n.cls1161m04['button.btCaseType0'],
            width: 800,
            height: 400,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                'sure': function(){
                    var data = panelAction.L120M01AGrid.getSingleData();
                    if (data) {
                        panelAction.openL140M01A(data);
                    }
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
        
        panelAction.caseData = $.extend({}, data);
        panelAction.L120M01AGrid.reload(data);
    },
    
    /**
     * 開啟額度明細表
     */
    openL140M01A: function(data){
        //set thick default options
        var options = {
            title: i18n.cls1161m04['button.btCaseType1'],
            width: 800,
            height: 300,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    var datas = panelAction.L140M01AGrid.getSelectData('oid');
                    if (datas) {
                        panelAction.sure({
                            caseOids: datas
                        });
                    }
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        };
        
        $.extend(panelAction.caseData, {
            caseMainId: data.mainId || ''
        });
        
        
        $('#L140M01AThickBox').thickbox(options);
        
        //grid load data
        panelAction.L140M01AGrid.reload($.extend(data, {
           caseType: panelAction.caseType,
            isUse: 'Y'
        }));

		
        if (panelAction.caseType == '2') {
            panelAction.L140M01AGrid2.reload($.extend(data, {
                caseType: panelAction.caseType,
                isUse: 'N'
            }));
        }
    },
    /**
     * 額度明細表-確定
     */
    sure: function(data){
        MegaApi.confirmMessage(i18n.def["actoin_001"], function(action){
            if (action) {
                $.ajax({
                    handler: "cls1161m04formhandler",
                    action: 'reNewMetaDoc',
                    data: $.extend(panelAction.caseData, data),
                    success: function(response){
                        $.thickbox.close();
                        $.thickbox.close();
                        MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['runSuccess']);
                        CommonAPI.triggerOpener("gridview", "reloadGrid");
                        responseJSON.oid = response.oid;
                        responseJSON.mainOid = response.mainOid;
                        responseJSON.mainId = response.mainId;
						responseJSON.mainDocStatus = response.mainDocStatus;
                        $("body").injectData(response);
						setRequiredSave(false);
                    }
                });
            }
        });
    },
	
	//UPGRADE
	getSelectData : function(key){
	          		var tGrid = $(this);
	          		var datas = [];
	          		var rows = tGrid.getGridParam('selarrrow');
	        			for (var o in rows) {
	        				  datas.push(tGrid.getRowData(rows[o]));
	      				}
	          		if (datas.length > 0){
	          			if (key){
	          				var result = [];
	          				for (var o in datas){
	          					var data = datas[o];
	          					result.push(data[key]);
	          				}
	          				return result;
	          			}else{
	          				return datas;
	          			}
	          		}else{
	          			MegaApi.showErrorMessage(i18n.def['confirmTitle'],i18n.def['action_005']);
	          			return undefined;
	          		}
	          	}
    
};



$(function(){

    panelAction.build();
    panelAction.init();
});
