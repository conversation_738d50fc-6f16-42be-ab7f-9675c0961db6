
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L140MM4CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140MM4C;


@Repository
public class L140MM4CDaoImpl extends LMSJpaDao<L140MM4C, String> implements
		L140MM4CDao {
	
	@Override
	public L140MM4C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<L140MM4C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("createTime", false);
		search.setMaxResults(Integer.MAX_VALUE);

		List<L140MM4C> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L140MM4C findCurrentByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "flag", "Y");
		search.addOrderBy("createTime", false);
		search.setMaxResults(Integer.MAX_VALUE);

		L140MM4C l140mm4c = findUniqueOrNone(search); 

		return l140mm4c;
	}
	
	
	@Override
	public L140MM4C findLastByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "flag", "N");
		search.addOrderBy("createTime", false);
		search.setMaxResults(Integer.MAX_VALUE);

		L140MM4C l140mm4c = findUniqueOrNone(search);
		return l140mm4c;
	}
	
	@Override
	public L140MM4C findByMainIdEstateType(String mainId, String estateType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "flag", "Y");
		search.addSearchModeParameters(SearchMode.EQUALS, "estateType", estateType);
		 
		return findUniqueOrNone(search);
	}
}