---------------------------------------------------------
-- LMS.L140M04A 查核事項檔明細檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140M04A;
CREATE TABLE LMS.L140M04A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	CHECK<PERSON>DE     VARCHAR(10)   not null,
	CHECKSEQ      DECIMAL(5,0) ,
	CHECKTYPE     VARCHAR(2)   ,
	CHECKCONTENT  VARCHAR(1024),
	PRODTYP<PERSON>      VARCHAR(256) ,
	CHECKYN       CHAR(1)      ,
	CHECKRMK      VARCHAR(1024),
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140M04A PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140M04A01;
CREATE UNIQUE INDEX LMS.XL140M04A01 ON LMS.L140M04A   (MAINID, CHECKCODE);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140M04A IS '查核事項檔明細檔';
COMMENT ON LMS.L140M04A (
	OID           IS 'oid', 
	MAINID        IS 'mainId', 
	CHECKCODE     IS '查核事項編號', 
	CHECKSEQ      IS '顯示順序', 
	CHECKTYPE     IS '查核事項項目', 
	CHECKCONTENT  IS '查核內容', 
	PRODTYPE      IS '適用產品', 
	CHECKYN       IS '查核結果', 
	CHECKRMK      IS '備註', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
