/* 
 * C900M01PDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M01P;

/** PD等級分組對應表 **/
public interface C900M01PDao extends IGenericDao<C900M01P> {

	C900M01P findByOid(String oid);

	List<C900M01P> findByDocTypeAndVersionAndCrdType(String docType,
			String pdVersion, String pdCrdType);
}