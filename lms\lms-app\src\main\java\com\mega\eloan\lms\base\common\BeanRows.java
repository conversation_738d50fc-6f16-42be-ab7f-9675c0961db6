/* 
 * BeanRows.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.common;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Column;

import net.sf.cglib.beans.BeanMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 上傳物件相關處理
 * </pre>
 * 
 * @since 2012/5/18
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/5/18,<PERSON>,new
 *          </ul>
 */
public abstract class BeanRows<T> {

	private static Logger logger = LoggerFactory.getLogger(BeanRows.class);

	protected String tableNm = null;
	protected ArrayList<RowColumn> cols = new ArrayList<RowColumn>();
	protected List<Object[]> values = new ArrayList<Object[]>();

	protected List<Object[]> keyValList = new ArrayList<Object[]>();

	protected HashMap<String, String> tbMap = new HashMap<String, String>();

	private int onlySetCnt = 0;
	private int keyCnt = 0;

	// protected boolean isErase = false;
	protected boolean isAS400 = false;
	protected boolean isOnlySet = false;

	protected String brNo;

	/**
	 * 建構子
	 * 
	 * @param clazz
	 */
	public BeanRows(Class<T> clazz) {
		init(clazz);
	}

	/**
	 * 初始化欄位資訊
	 * 
	 * @param clazz
	 */
	@SuppressWarnings("rawtypes")
	private void init(Class<T> clazz) {
		Class<?> searchClazz = clazz;

		try {
			logger.debug("BeanRows init start =========================");
			tableNm = searchClazz.getSimpleName().toUpperCase();

			while (!Object.class.equals(searchClazz) && searchClazz != null) {
				Field[] fields = searchClazz.getDeclaredFields();
				for (Field f : fields) {
					if (f.isAnnotationPresent(Column.class)) {

						RowColumn rowCol = new RowColumn(f.getName());
						rowCol.setLength(f.getAnnotation(Column.class).length());
						rowCol.setScale(f.getAnnotation(Column.class).scale());

						Class type = f.getType();
						int tmpType = CMSTypes.CHAR;
						if (type == int.class || type == long.class
								|| type == float.class || type == double.class
								|| type == Integer.class
								|| type == BigDecimal.class
								|| type == Long.class) {
							tmpType = CMSTypes.DECIMAL;
						} else if (type == Date.class) {
							if (f.isAnnotationPresent(CMSColumn.class)) {
								tmpType = f.getAnnotation(CMSColumn.class)
										.dateType();
							} else {
								tmpType = CMSTypes.DATE;
							}
						} else if (type == Timestamp.class) {
							tmpType = CMSTypes.DATE;
						}

						rowCol.setType(tmpType);

						// 是否為KEY值
						if (f.getAnnotation(Column.class).unique()) {
							rowCol.setKey(true);
							keyCnt++;
						}
						cols.add(rowCol);
						logger.debug("COLUMN[{}][{}]", f.getName(), rowCol);
					}

				}
				searchClazz = searchClazz.getSuperclass();
			}
			logger.debug("**Total Column Size = {}", cols.size());
			logger.debug("**keyCnt={}", keyCnt);
			logger.debug("**onlySetCnt={}", onlySetCnt);
			logger.debug("BeanRows init End =========================");
		} catch (Exception e) {
			logger.error("init EXCEPTION!!", e);
			throw new RuntimeException(e);
		}
	}

	/**
	 * 取得table Name
	 * 
	 * @return
	 */
	public String getTableNm() {
		if (tbMap.isEmpty()) {
			return this.tableNm;
		} else {
			StringBuffer sb = new StringBuffer();
			if (this.isAS400) {
				sb.append("AS");
			} else {
				sb.append("MIS");
			}
			sb.append("_");
			// if (this.isErase) {
			// sb.append("CX");
			// } else {
			// sb.append("CO");
			// }
			return tbMap.get(sb.toString());
		}
	}

	// /**
	// * 該欄位是否顯示
	// *
	// * @param erase
	// * 是否為塗銷欄位
	// * @return
	// */
	// public final boolean isShow(RowColumn row) {
	// // System.out.println(row.getName()+"["++"]["++"]");
	// if ((!isErase && row.isErase()) || (row.isOnlySet() && isErase)) {
	// return false;
	// }
	// return true;
	// }

	/**
	 * <pre>
	 * 取得客製化訊息: 
	 *   1. table name
	 *   2. 欄位
	 *   3. ?
	 * </pre>
	 * 
	 * @return Object[]
	 */
	public final Object[] getMsgFmtParam(String TableType) {
		Object[] lst = new Object[3];
		lst[0] = TableType+"."+getTableNm();
		StringBuffer sb1 = new StringBuffer();
		StringBuffer sb2 = new StringBuffer();
		 for (int i = 0; i < cols.size(); i++) {
		
		 sb1.append(cols.get(i).getName()).append(",");
		 sb2.append("?,");
		 
		 }
		lst[1] = sb1.toString().replaceAll(",$", "");
		lst[2] = sb2.toString().replaceAll(",$", "");
		return lst;
	}
	
	/**
	 * <pre>
	 * 取得客製化訊息: 
	 *   1. table name
	 *   2. 欄位=?,
	 *   3. 欄位=? AND
	 * </pre>
	 * 
	 * @return Object[]
	 */
	public final Object[] getUpdateMsgFmtParam(String TableType) {
		Object[] lst = new Object[3];
		lst[0] = TableType+"."+getTableNm();
		StringBuffer sb1 = new StringBuffer();
		StringBuffer sb2 = new StringBuffer();
		 int keycount=0;
		for (int i = 0; i < cols.size(); i++) {
		
		 sb1.append(cols.get(i).getName()).append("=?,");
			 if (cols.get(i).isKey()) {
			 	if(keycount==0){
			 		sb2.append(cols.get(i).getName()).append("=? ");
			 		keycount++;
				}else{
					sb2.append(" AND ");
					sb2.append(cols.get(i).getName()).append("=? ");
				}
			 }
		 }
		lst[1] = sb1.toString().replaceAll(",$", "");
		lst[2] = sb2.toString().replaceAll("AND$", "");
		return lst;
	}
	

	/**
	 * 取得資料物件
	 * 
	 * @return List<Object[]>
	 */
	public final List<Object[]> getValues() {
		return this.values;
	}

	/**
	 * 取得欄位屬性定義
	 * 
	 * @return int[]
	 */
	public final int[] getTypes() {
		int[] lst = new int[getColsCnt()];

		 for (int i = 0, j = 0; i < cols.size(); i++) {
		 
		 lst[j] = getType(cols.get(i).getType());
		 j++;
		 
		 }
		return lst;
	}
	/**
	 * 取得UPDATE欄位屬性定義
	 * 
	 * @return int[]
	 */
	public final int[] getUpdateTypes() {
		int[] lst = new int[getColsCnt()+keyCnt];
		 for (int i = 0, j = 0,k=0; i < cols.size(); i++) {
			 lst[j] = getType(cols.get(i).getType());
			 j++;
			 if(cols.get(i).isKey()){
				 lst[getColsCnt()+k] = getType(cols.get(i).getType());
				 k++;
			 }
		 }


		return lst;
	}
	/**
	 * 取得KEY值欄位屬性定義
	 * 
	 * @return int[]
	 */
	public final int[] getKeyTypes() {
		int[] lst = new int[this.keyCnt];

		for (int i = 0, j = 0; i < cols.size(); i++) {
			if (cols.get(i).isKey()) {
				lst[j] = getType(cols.get(i).getType());
				j++;
			}
		}
		return lst;
	}

	/**
	 * 取得欄位型態
	 * 
	 * @param type
	 *            欄位型態
	 * @return
	 */
	public int getType(int type) {
		if (type > 910000) {
			return CMSTypes.CHAR;
		}
		return type;
	}

	/**
	 * 取得KEY值
	 * 
	 * @return
	 */
	public final Object[] getKeyValues() {
			if (this.keyValList != null && !this.keyValList.isEmpty()) {
				Object[] outkeylist=new Object[keyValList.size()*keyValList.get(0).length];
				int i=0;
				for(Object[] keys:keyValList){
					for(Object key:keys){
						outkeylist[i]=key;
						i++;
					}
				}
				return outkeylist;
			}
		return null;
	}

	/**
	 * 取得KEY值
	 * 
	 * @return
	 */
	public final List<Object[]> getKeyValuesLst() {
		return this.keyValList;
	}
	/**
	 * 取得UpDate值
	 * 
	 * @return
	 */
	public final List<Object[]> getUpDateValuesLst() {
		List<Object[]> UpValueslst=new ArrayList<Object[]>();
		List<Object[]> Valueslst=getValues();
		int seqNo=0;
		for(Object[] Values:Valueslst){
			Object[] val=new Object[Values.length+keyCnt];
			int count=0;
			for(Object Value:Values){
				val[count]=Value;
				count++;
			}
			for(Object keyVal:keyValList.get(seqNo)){
				val[count]=keyVal;
			}
			UpValueslst.add(val);
			seqNo++;
		}
		return UpValueslst;
		
	}
	/**
	 * <pre>
	 * 取得客製化訊息: 
	 *   1. table name
	 *   2. 欄位=? AND.....
	 * </pre>
	 * 
	 * @return Object[]
	 */
	public final Object[] getKeyMsgFmtParam(String TableType) {
		Object[] lst = new Object[2];
		lst[0] = TableType+"."+getTableNm();
		StringBuffer sb1 = new StringBuffer();
		
		for(int j=0;j<this.keyValList.size();j++){
			if(j==0){
				sb1.append("(");
			}
			int keycount=0;
			for (int i = 0; i < cols.size(); i++) {
				if (cols.get(i).isKey()) {
					if(keycount==0){
						sb1.append(" ").append(cols.get(i).getName())
								.append(" = ? ");
						keycount++;
					}else{
						sb1.append("AND").append(" ").append(cols.get(i).getName())
						.append(" = ? ");	
					}
				}
			}
			if(j<this.keyValList.size()-1){
				sb1.append(") OR (");
			}else {
				sb1.append(")");
			}
		}
		lst[1] = sb1.toString().replaceAll("AND$", "");
		return lst;
	}

	private List<T> tmpLst = null;
	private List<Map<String, Object>> tmpMaplst = null;

	/**
	 * 重新設定資料(改變塗銷模式時重新取得資料)
	 * 
	 * @param isErase
	 *            是為塗銷模式
	 */
	public final void reSetValues() {
		values.clear();
		keyValList.clear();
		if (tmpLst != null) {
			setValues(tmpLst);
		}
		if (tmpMaplst != null) {
			setValuesByMap(tmpMaplst);
		}
	}

	/**
	 * 設定資料
	 * 
	 * @param lst
	 */
	public final void setValues(List<T> lst) {
		this.tmpLst = lst;
		for (T entry : lst) {
			BeanMap bm = Util.getBeanMap(entry);
			Object[] objLst = new Object[getColsCnt()];
			Object[] keyLst = new Object[keyCnt];
			for (int i = 0, j = 0; i < cols.size(); i++) {
				RowColumn col = cols.get(i);
				// if (this.isShow(col)) {
				// Object obj = bm.get(col.getName());
				// objLst[k] = this.getValue(col, obj);
				// logger.debug("{}={}", col.getName(), objLst[k]);
				// k++;
				// }
				Object obj2 = bm.get(col.getName());
				objLst[i] = this.getValue(col, obj2);
				if (col.isKey()) {
					Object obj = bm.get(col.getName());
					keyLst[j] = this.getValue(col, obj);
					j++;
				}
			}
			values.add(objLst);
			keyValList.add(keyLst);
		}
	}

	/**
	 * 取得欄位個數(判斷是否為塗銷模式)
	 * 
	 * @return
	 */
	public final int getColsCnt() {
		return cols.size() - onlySetCnt;
	}

	/**
	 * 設定資料: 將SQL查出之資料直接設定進物件中
	 * 
	 * @param lst
	 */
	public final void setValuesByMap(List<Map<String, Object>> lst) {
		tmpMaplst = lst;
		for (Map<String, Object> map : lst) {
			logger.debug("===========================");
			Object[] objLst = new Object[getColsCnt()];
			Object[] keyLst = new Object[keyCnt];
			int k=0;
			for (int i = 0, j = 0; i < cols.size(); i++) {
				RowColumn col = cols.get(i);
				
				 Object obj = map.get(col.getName());
				 objLst[k] = this.getValue(col, obj);
				 logger.debug("{}={}", col.getName(), objLst[k]);
				 k++;
				 
				if (col.isKey()) {
					keyLst[j] = this.getValue(col, obj);
					j++;
				}
			}
			values.add(objLst);
			keyValList.add(keyLst);
		}
	}

	/**
	 * 依據類別對資料進行轉換
	 * 
	 * @param RowColumn
	 * @param obj
	 * @return
	 */
	protected abstract Object getValue(RowColumn col, Object obj);

	// /**
	// * 是否塗銷模式
	// *
	// * @return
	// */
	// public boolean isErase() {
	// return isErase;
	// }

	// /**
	// * 設定是否塗銷
	// *
	// * @param isErase
	// */
	// public void setErase(boolean isErase) {
	// this.isErase = isErase;
	// }

	/**
	 * 是否為上傳AS400
	 * 
	 * @return
	 */
	public boolean isAS400() {
		return isAS400;
	}

	/**
	 * 設定是否為上傳AS400
	 * 
	 * @param isAS400
	 */
	public void setAS400(boolean isAS400) {
		this.isAS400 = isAS400;
	}

	/**
	 * 取得分行代號
	 * 
	 * @return
	 */
	public String getBrNo() {
		return brNo;
	}

	/**
	 * 設定分行代號
	 * 
	 * @param brNo
	 */
	public void setBrNo(String brNo) {
		this.brNo = brNo;
	}
}

class RowColumn {

	/** 是否為KEY值 */
	private boolean isKey = false;
	/** 資料型態 */
	private int type = CMSTypes.CHAR;
	/** 欄位名稱 */
	private String name = null;
	/** 欄位長度 */
	private int length = 3000;
	/** 小數位數 */
	private int scale = 0;
	/** 設定時才需上傳 */
	private boolean isOnlySet = false;

	public boolean isOnlySet() {
		return isOnlySet;
	}

	public void setOnlySet(boolean isOnlySet) {
		this.isOnlySet = isOnlySet;
	}

	public RowColumn(String name) {
		this.name = getFieldName(name);
	}

	public boolean isKey() {
		return isKey;
	}

	public void setKey(boolean isKey) {
		this.isKey = isKey;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public String getName() {
		return name;
	}

	public int getLength() {
		return length;
	}

	public void setLength(int length) {
		this.length = length;
	}

	public int getScale() {
		return scale;
	}

	public void setScale(int scale) {
		this.scale = scale;
	}

	public String toString() {
		return "name=" + name + ", type=" + type + ", length=" + length
				+ ", isKey=" + isKey + ", isOnlySet=" + isOnlySet;
	}

	/**
	 * 取得標準欄位名稱(第一碼為小寫英文)
	 * 
	 * @param fildeName
	 *            欄位名稱
	 * @return
	 */
	private static String getFieldName(String fildeName) {
		StringBuffer sb = new StringBuffer();
		sb.append(fildeName.substring(0, 1).toLowerCase());
		sb.append(fildeName.substring(1));
		return sb.toString();
	}
}