package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.C310M01A;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 同一通訊處註記
 * </pre>
 * 
 * @since 2019/02/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/02/19,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls3101m01/{page}")
public class CLS3101M01Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params,
				getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));

		addAclLabel(model,
				new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
						AuthType.Accept, CreditDocStatusEnum.海外_待覆核,
						CreditDocStatusEnum.先行動用_待覆核));
		renderJsI18N(CLS3101M01Page.class);
		renderJsI18N(CLS3101V01Page.class);

		new DocLogPanel("_docLog").processPanelData(model, params);
	}// ;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C310M01A.class;
	}
}
