/* 
 *LMS1605R03RptServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON>., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.report.impl;

import java.text.DecimalFormat;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.CodeTypeEnum;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.lms.pages.LMS1605M01Page;
import com.mega.eloan.lms.lms.service.LMS1605Service;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L162S01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * 產生動審表PDF主從債務人資料表檔
 * </pre>
 * 
 * @since 2012/2/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/9,REX,new
 *          </ul>
 */
@Service("lms1605r03rptservice")
public class LMS1605R03RptServiceImpl extends AbstractReportService {

	@Resource
	LMS1605Service lms1605Service;

	@Resource
	BranchService branch;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	ICustomerService iCustomerService;

	private static ThreadLocal<DecimalFormat> dfMoney = new ThreadLocal<DecimalFormat>();
	private static ThreadLocal<DecimalFormat> dfRate = new ThreadLocal<DecimalFormat>();

	private Map<String, CapAjaxFormResult> codeMap = null;

	@Override
	public String getReportTemplateFileName() {
		Locale locale = null;
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		return "report/lms/LMS1605R03_" + locale.toString() + ".rpt";
	}

	/*
	 * (non-Javadoc) 設定需要傳入RPT參數
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.AbstractReportService#setReportData(com
	 * .mega.eloan.lms.base.report.ReportGenerator,
	 * org.apache.wicket.PageParameters)
	 */
	@SuppressWarnings("unchecked")
	@Override
	public void setReportData(ReportGenerator reportTools, PageParameters params) {

		String mainOid = params.getString(EloanConstants.MAIN_OID);

		// L160M01A．動用審核表主檔
		L160M01A l160m01a = null;
		Locale locale = null;
		try {

			locale = LocaleContextHolder.getLocale();
			if (locale == null) {
				locale = Locale.getDefault();
			}
			dfMoney.set(new DecimalFormat("#,###,###,###,##0"));
			dfRate.set(new DecimalFormat("#,###,###,###,##0.00"));
			String[] codeType = { "Relation_type1", "Relation_type2",
					"Relation_type31", "Relation_type32", "lms1605s03_rType",
					CodeTypeEnum.國家代碼.getCode() };
			codeMap = codeTypeService.findByCodeType(codeType,
					locale.toString());
			Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
			List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
			l160m01a = lms1605Service.findModelByOid(L160M01A.class, mainOid);
			List<L162S01A> l162s01a = (List<L162S01A>) lms1605Service
					.findListByMainId(L162S01A.class, l160m01a.getMainId());

			titleRows = this.setL161S01BDataList(titleRows, locale, l162s01a);
			reportTools.setLang(locale);
			reportTools.setVariableData(rptVariableMap);
			reportTools.setRowsData(titleRows);

		} finally {

		}
	}

	/**
	 * 設定L161S01B資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L161S01B List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setL161S01BDataList(
			List<Map<String, String>> titleRows, Locale locale,
			List<L162S01A> list) {
		// F代表第一次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		Map<String, String> mapInTitleRows = null;

		// J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);

		int count = 1;
		StringBuffer temp = new StringBuffer();
		for (L162S01A l162s01a : list) {

			String custId = l162s01a.getCustId();
			String dupNo = l162s01a.getDupNo();
			LOGGER.info(custId + " " + dupNo + " " + l162s01a.getRType());

			Map<String, Object> map = iCustomerService.findByIdDupNo(custId,
					dupNo);

			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column01", String.valueOf(count));
			// mapInTitleRows.put(
			// "ReportBean.column02",
			// LMSUtil.concat(temp, l162s01a.getCustId(), " ",
			// l162s01a.getDupNo()));

			mapInTitleRows.put(
					"ReportBean.column02",
					LMSUtil.concat(temp, custId, " ", dupNo, " ", "\r",
							Util.trim(map.get("CNAME"))));

			mapInTitleRows.put("ReportBean.column03",
					Util.nullToSpace(l162s01a.getCntrNo()));
			mapInTitleRows.put(
					"ReportBean.column04",
					LMSUtil.concat(temp, l162s01a.getRId(), " ",
							l162s01a.getRDupNo()));
			mapInTitleRows.put("ReportBean.column05",
					Util.nullToSpace(l162s01a.getRName()));
			switch (Util.parseInt(l162s01a.getRKindM())) {
			case 1:

				mapInTitleRows.put(
						"ReportBean.column06",
						(String) codeMap.get("Relation_type1").get(
								l162s01a.getRKindD()));
				break;
			case 2:
				mapInTitleRows.put(
						"ReportBean.column06",
						(String) codeMap.get("Relation_type2").get(
								l162s01a.getRKindD()));
				break;
			case 3:
				char[] kind = l162s01a.getRKindD().toCharArray();
				String kind1 = (String) codeMap.get("Relation_type31").get(
						String.valueOf(kind[0]));
				String kind2 = (String) codeMap.get("Relation_type32").get(
						String.valueOf(kind[1]));
				mapInTitleRows.put("ReportBean.column06",
						LMSUtil.concat(temp, kind1, " - ", kind2));
				break;

			}
			mapInTitleRows.put(
					"ReportBean.column07",
					(String) codeMap.get(CodeTypeEnum.國家代碼.getCode()).get(
							l162s01a.getRCountry()));
			// 相關身分
			mapInTitleRows.put(
					"ReportBean.column08",
					(String) codeMap.get("lms1605s03_rType").get(
							l162s01a.getRType()));

			mapInTitleRows.put("ReportBean.column09",
					Util.getDate(l162s01a.getDueDate()));

			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			mapInTitleRows.put("ReportBean.column10",
					Util.trim(l162s01a.getPriority()));
			mapInTitleRows
					.put("ReportBean.column11",
							Util.equals(Util.trim(l162s01a.getGuaNaExposure()),
									"") ? ""
									: (Util.equals(Util.trim(l162s01a
											.getGuaNaExposure()), "Y") ? prop
											.getProperty("yes") : prop
											.getProperty("no")));

			count++;
			titleRows.add(mapInTitleRows);
		}
		return titleRows;
	}

}
