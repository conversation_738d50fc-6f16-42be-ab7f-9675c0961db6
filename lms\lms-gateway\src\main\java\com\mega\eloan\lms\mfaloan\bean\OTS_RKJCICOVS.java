package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 聯徵及VEDA資訊 **/
public class OTS_RKJCICOVS extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Column(name="BR_CD", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String br_cd;

	/** NOTES文件編號 **/
	@Column(name="NOTEID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String noteid;

	/** 
	 * 評等日期<p/>
	 * 最終評等日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="RATING_DATE", columnDefinition="DATE", nullable=false,unique = true)
	private Date rating_date;

	/** 評等文件編號 **/
	@Column(name="RATING_ID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String rating_id;

	/** 客戶統一編號 **/
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String custid;

	/** 重複序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String dupno;

	/** 主借款人統一編號 **/
	@Column(name="CUST_KEY", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String cust_key;

	/** 
	 * 授信科目<p/>
	 * 3-4碼授信科目
	 */
	@Column(name="LOAN_CODE", length=4, columnDefinition="VARCHAR(4)", nullable=false,unique = true)
	private String loan_code;

	/** 
	 * 評等模型類別(c121m01a.mowType)
	 */
	@Column(name="MOWTYPE", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String mowtype;
	
	/** 
	 * 房貸/非房貸註記(l141m01c.modelType)
	 * N=非房貸、M=房貸
	 */
	@Column(name="MOWTYPE2", length=1, columnDefinition="CHAR(1)")
	private String mowtype2;
	
	/** 
	 * 採用模型註記
	 * 國別碼(l120m01a.ratingFlag)
	 */
	@Column(name="MOWTYPE_COUNTRY", length=2, columnDefinition="CHAR(2)", nullable=false,unique = true)
	private String mowtype_country;

	/** 模型版本-大版 **/
	@Column(name="MOWVER1", columnDefinition="DEC(5,0)", nullable=false,unique = true)
	private Integer mowver1;

	/** 模型版本-小版 **/
	@Column(name="MOWVER2", columnDefinition="DEC(5,0)", nullable=false,unique = true)
	private Integer mowver2;

	/** 
	 * 科目<p/>
	 * 8碼會計科目
	 */
	@Column(name="SUBJCODE", length=8, columnDefinition="VARCHAR(8)")
	private String subjcode;

	/** 相關身分 **/
	@Column(name="LNGEFLAG", length=1, columnDefinition="CHAR(1)")
	private String lngeflag;

	/** 文件狀態 **/
	@Column(name="DOCSTATUS", length=2, columnDefinition="CHAR(2)")
	private String docstatus;

	/** 退票 **/
	@Column(name="EVER_BAD_CHECK", length=1, columnDefinition="CHAR(1)")
	private String ever_bad_check;

	/** 拒往 **/
	@Column(name="REJECT_YN", length=1, columnDefinition="CHAR(1)")
	private String reject_yn;

	/** 信用報告查詢日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="INQ_DATE", columnDefinition="DATE")
	private Date inq_date;

	/** 票信查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CHECK_QDATE", columnDefinition="DATE")
	private Date check_qdate;

	/** 票信資料截止日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="END_DATE", columnDefinition="DATE")
	private Date end_date;

	/** 信用卡強停 **/
	@Column(name="CREDIT_FORCE_STOP", length=1, columnDefinition="CHAR(1)")
	private String credit_force_stop;

	/** 催收呆帳紀錄 **/
	@Column(name="BAD_DEBT", length=1, columnDefinition="CHAR(1)")
	private String bad_debt;

	/** 有消債條例信用註記、催收呆帳紀錄、其他補充註記 **/
	@Column(name="NEGO_LAWBANKOTH", length=1, columnDefinition="CHAR(1)")
	private String nego_lawbankoth;

	/** 近12個月授信帳戶出現遲延還款 **/
	@Column(name="LN12_PAY_DELAY_TIMES", length=1, columnDefinition="CHAR(1)")
	private String ln12_pay_delay_times;

	/** 申貸查詢時現金卡借款餘額(新台幣仟元)_有無 **/
	@Column(name="CASH_AMT_FLAG", length=1, columnDefinition="CHAR(1)")
	private String cash_amt_flag;

	/** 申貸查詢時現金卡借款餘額(新台幣仟元) **/
	@Column(name="CASH_AMT", columnDefinition="DEC(15,0)")
	private BigDecimal cash_amt;

	/** 申貸查詢時無擔保授信餘額(新台幣仟元)_有無 **/
	@Column(name="NOS_AMT_FLAG", length=1, columnDefinition="CHAR(1)")
	private String nos_amt_flag;

	/** 申貸查詢時無擔保授信餘額(新台幣仟元) **/
	@Column(name="NOS_AMT", columnDefinition="DEC(15,0)")
	private BigDecimal nos_amt;

	/** 申貸查詢時學生助學貸款餘額(新台幣仟元)_有無 **/
	@Column(name="SL_AMT_FLAG", length=1, columnDefinition="CHAR(1)")
	private String sl_amt_flag;

	/** 申貸查詢時學生助學貸款餘額(新台幣仟元) **/
	@Column(name="SL_AMT", columnDefinition="DEC(15,0)")
	private BigDecimal sl_amt;

	/** 近12個月信用卡繳款狀況出現循環信用延遲次數_有無 **/
	@Column(name="CC12_REVOL_PAY_DELAY_TIMES_FLAG", length=1, columnDefinition="CHAR(1)")
	private String cc12_revol_pay_delay_times_flag;

	/** 近12個月信用卡繳款狀況出現循環信用延遲次數 **/
	@Column(name="CC12_REVOL_PAY_DELAY_TIMES", columnDefinition="DEC(5,0)")
	private Integer cc12_revol_pay_delay_times;

	/** 近12個月信用卡繳款狀況出現未繳足最低金額次數_有無 **/
	@Column(name="CC12_MINPAY_DELAY_TIMES_FLAG", length=1, columnDefinition="CHAR(1)")
	private String cc12_minpay_delay_times_flag;

	/** 近12個月信用卡繳款狀況出現未繳足最低金額次數 **/
	@Column(name="CC12_MINPAY_DELAY_TIMES", columnDefinition="DEC(5,0)")
	private Integer cc12_minpay_delay_times;

	/** 近12個月信用卡繳款狀況出現全額逾期未繳次數_有無 **/
	@Column(name="CC12_TOTPAY_DELAY_TIMES_FLAG", length=1, columnDefinition="CHAR(1)")
	private String cc12_totpay_delay_times_flag;

	/** 近12個月信用卡繳款狀況出現全額逾期未繳次數 **/
	@Column(name="CC12_TOTPAY_DELAY_TIMES", columnDefinition="DEC(5,0)")
	private Integer cc12_totpay_delay_times;

	/** 近12個月信用卡有預借現金餘額次數_有無 **/
	@Column(name="CC12_CASH_ADV_TIMES_FLAG", length=1, columnDefinition="CHAR(1)")
	private String cc12_cash_adv_times_flag;

	/** 近12個月信用卡有預借現金餘額次數 **/
	@Column(name="CC12_CASH_ADV_TIMES", columnDefinition="DEC(5,0)")
	private Integer cc12_cash_adv_times;

	/** 近3個月新業務申請查詢總家數_有無 **/
	@Column(name="INQ3_NAPP_BANK_FLAG", length=1, columnDefinition="CHAR(1)")
	private String inq3_napp_bank_flag;

	/** 近3個月新業務申請查詢總家數 **/
	@Column(name="INQ3_NAPP_BANK", columnDefinition="DEC(5,0)")
	private Integer inq3_napp_bank;

	/** J10信用評分種類 **/
	@Column(name="J10_SCORE_FLAG", length=2, columnDefinition="CHAR(2)")
	private String j10_score_flag;

	/** J10信用評分 **/
	@Column(name="J10_SCORE", columnDefinition="DEC(4,0)")
	private Integer j10_score;

	/** VEDA不良紀錄_有無 **/
	@Column(name="VEDA_ADVERSE_FILE", length=1, columnDefinition="CHAR(1)")
	private String veda_adverse_file;

	/** VEDA違約金額_有無 **/
	@Column(name="VEDA_DEFAULT_OUTSTANDING", length=1, columnDefinition="CHAR(1)")
	private String veda_default_outstanding;

	/** VEDA法院判決紀錄_有無 **/
	@Column(name="VEDA_JUDGEMENT", length=1, columnDefinition="CHAR(1)")
	private String veda_judgement;

	/** VEDA信用查詢次數_有無 **/
	@Column(name="VEDA_CREDIT_ENQUIRIES_FLAG", length=1, columnDefinition="CHAR(1)")
	private String veda_credit_enquiries_flag;

	/** VEDA信用查詢次數 **/
	@Column(name="VEDA_CREDIT_ENQUIRIES", columnDefinition="DEC(5,0)")
	private Integer veda_credit_enquiries;

	/** VEDA信用提供者_有無 **/
	@Column(name="VEDA_CREDIT_PROVIDER", length=1, columnDefinition="CHAR(1)")
	private String veda_credit_provider;

	/** VEDA信用往來長度 **/
	@Column(name="VEDA_CREDIT_FILE_AGE_FLAG", length=1, columnDefinition="CHAR(1)")
	private String veda_credit_file_age_flag;

	/** VEDA信用往來長度(年) **/
	@Column(name="VEDA_CREDIT_FILE_AGE", columnDefinition="DEC(5,0)")
	private Integer veda_credit_file_age;

	/** 上傳資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DATA_SRC_DT", columnDefinition="DATE")
	private Date data_src_dt;
	
	/** NCB逾期90天帳戶數_有無  **/
	@Column(name="NCB_OVER_90_DAYS_FLAG", length=1, columnDefinition="CHAR(1)")
	private String ncb_over_90_days_flag;
	
	/** NCB逾期90天帳戶數 **/
	@Column(name="NCB_OVER_90_DAYS", columnDefinition="DEC(5,0)")
	private Integer ncb_over_90_days;
	
	/** NCB最大逾期天數區間_有無  **/
	@Column(name="NCB_MAXIMUM_DPD_FLAG", length=1, columnDefinition="CHAR(1)")
	private String ncb_maximum_dpd_flag;
	
	/** NCB最大逾期天數區間 **/
	@Column(name="NCB_MAXIMUM_DPD", length=1, columnDefinition="CHAR(1)")
	private String ncb_maximum_dpd;
	
	/** NCB近6個月信用查詢次數_有無 **/
	@Column(name="NCB_ENQ_RECENT_6M_FLAG", length=1, columnDefinition="CHAR(1)")
	private String ncb_enq_recent_6m_flag;
	
	/** NCB近6個月信用查詢次數  **/
	@Column(name="NCB_ENQ_RECENT_6M", columnDefinition="DEC(5,0)")
	private Integer ncb_enq_recent_6m;

	/** 取得分行別 **/
	public String getBr_cd() {
		return this.br_cd;
	}
	/** 設定分行別 **/
	public void setBr_cd(String value) {
		this.br_cd = value;
	}

	/** 取得NOTES文件編號 **/
	public String getNoteid() {
		return this.noteid;
	}
	/** 設定NOTES文件編號 **/
	public void setNoteid(String value) {
		this.noteid = value;
	}

	/** 
	 * 取得評等日期<p/>
	 * 最終評等日
	 */
	public Date getRating_date() {
		return this.rating_date;
	}
	/**
	 *  設定評等日期<p/>
	 *  最終評等日
	 **/
	public void setRating_date(Date value) {
		this.rating_date = value;
	}

	/** 取得評等文件編號 **/
	public String getRating_id() {
		return this.rating_id;
	}
	/** 設定評等文件編號 **/
	public void setRating_id(String value) {
		this.rating_id = value;
	}

	/** 取得客戶統一編號 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定客戶統一編號 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得主借款人統一編號 **/
	public String getCust_key() {
		return this.cust_key;
	}
	/** 設定主借款人統一編號 **/
	public void setCust_key(String value) {
		this.cust_key = value;
	}

	/** 
	 * 取得授信科目<p/>
	 * 3-4碼授信科目
	 */
	public String getLoan_code() {
		return this.loan_code;
	}
	/**
	 *  設定授信科目<p/>
	 *  3-4碼授信科目
	 **/
	public void setLoan_code(String value) {
		this.loan_code = value;
	}

	/** 
	 * 取得評等模型類別(c121m01a.mowType)
	 */
	public String getMowtype() {
		return this.mowtype;
	}
	/**
	 *  設定評等模型類別(c121m01a.mowType)
	 **/
	public void setMowtype(String value) {
		this.mowtype = value;
	}
	
	/** 
	 * 取得房貸/非房貸註記(l141m01c.modelType)
	 * N=非房貸、M=房貸
	 */
	public String getMowtype2() {
		return this.mowtype2;
	}
	/**
	 *  設定房貸/非房貸註記(l141m01c.modelType)
	 *  N=非房貸、M=房貸
	 **/
	public void setMowtype2(String value) {
		this.mowtype2 = value;
	}
	
	/** 
	 * 採用模型註記
	 * 國別碼(l120m01a.ratingFlag)
	 */
	public String getMowtype_country() {
		return this.mowtype_country;
	}
	/** 
	 * 採用模型註記
	 * 國別碼(l120m01a.ratingFlag)
	 */
	public void setMowtype_country(String value) {
		this.mowtype_country = value;
	}

	/** 取得模型版本-大版 **/
	public Integer getMowver1() {
		return this.mowver1;
	}
	/** 設定模型版本-大版 **/
	public void setMowver1(Integer value) {
		this.mowver1 = value;
	}

	/** 取得模型版本-小版 **/
	public Integer getMowver2() {
		return this.mowver2;
	}
	/** 設定模型版本-小版 **/
	public void setMowver2(Integer value) {
		this.mowver2 = value;
	}

	/** 
	 * 取得科目<p/>
	 * 8碼會計科目
	 */
	public String getSubjcode() {
		return this.subjcode;
	}
	/**
	 *  設定科目<p/>
	 *  8碼會計科目
	 **/
	public void setSubjcode(String value) {
		this.subjcode = value;
	}

	/** 取得相關身分 **/
	public String getLngeflag() {
		return this.lngeflag;
	}
	/** 設定相關身分 **/
	public void setLngeflag(String value) {
		this.lngeflag = value;
	}

	/** 取得文件狀態 **/
	public String getDocstatus() {
		return this.docstatus;
	}
	/** 設定文件狀態 **/
	public void setDocstatus(String value) {
		this.docstatus = value;
	}

	/** 取得退票 **/
	public String getEver_bad_check() {
		return this.ever_bad_check;
	}
	/** 設定退票 **/
	public void setEver_bad_check(String value) {
		this.ever_bad_check = value;
	}

	/** 取得拒往 **/
	public String getReject_yn() {
		return this.reject_yn;
	}
	/** 設定拒往 **/
	public void setReject_yn(String value) {
		this.reject_yn = value;
	}

	/** 取得信用報告查詢日 **/
	public Date getInq_date() {
		return this.inq_date;
	}
	/** 設定信用報告查詢日 **/
	public void setInq_date(Date value) {
		this.inq_date = value;
	}

	/** 取得票信查詢日期 **/
	public Date getCheck_qdate() {
		return this.check_qdate;
	}
	/** 設定票信查詢日期 **/
	public void setCheck_qdate(Date value) {
		this.check_qdate = value;
	}

	/** 取得票信資料截止日 **/
	public Date getEnd_date() {
		return this.end_date;
	}
	/** 設定票信資料截止日 **/
	public void setEnd_date(Date value) {
		this.end_date = value;
	}

	/** 取得信用卡強停 **/
	public String getCredit_force_stop() {
		return this.credit_force_stop;
	}
	/** 設定信用卡強停 **/
	public void setCredit_force_stop(String value) {
		this.credit_force_stop = value;
	}

	/** 取得催收呆帳紀錄 **/
	public String getBad_debt() {
		return this.bad_debt;
	}
	/** 設定催收呆帳紀錄 **/
	public void setBad_debt(String value) {
		this.bad_debt = value;
	}

	/** 取得有消債條例信用註記、催收呆帳紀錄、其他補充註記 **/
	public String getNego_lawbankoth() {
		return this.nego_lawbankoth;
	}
	/** 設定有消債條例信用註記、催收呆帳紀錄、其他補充註記 **/
	public void setNego_lawbankoth(String value) {
		this.nego_lawbankoth = value;
	}

	/** 取得近12個月授信帳戶出現遲延還款 **/
	public String getLn12_pay_delay_times() {
		return this.ln12_pay_delay_times;
	}
	/** 設定近12個月授信帳戶出現遲延還款 **/
	public void setLn12_pay_delay_times(String value) {
		this.ln12_pay_delay_times = value;
	}

	/** 取得申貸查詢時現金卡借款餘額(新台幣仟元)_有無 **/
	public String getCash_amt_flag() {
		return this.cash_amt_flag;
	}
	/** 設定申貸查詢時現金卡借款餘額(新台幣仟元)_有無 **/
	public void setCash_amt_flag(String value) {
		this.cash_amt_flag = value;
	}

	/** 取得申貸查詢時現金卡借款餘額(新台幣仟元) **/
	public BigDecimal getCash_amt() {
		return this.cash_amt;
	}
	/** 設定申貸查詢時現金卡借款餘額(新台幣仟元) **/
	public void setCash_amt(BigDecimal value) {
		this.cash_amt = value;
	}

	/** 取得申貸查詢時無擔保授信餘額(新台幣仟元)_有無 **/
	public String getNos_amt_flag() {
		return this.nos_amt_flag;
	}
	/** 設定申貸查詢時無擔保授信餘額(新台幣仟元)_有無 **/
	public void setNos_amt_flag(String value) {
		this.nos_amt_flag = value;
	}

	/** 取得申貸查詢時無擔保授信餘額(新台幣仟元) **/
	public BigDecimal getNos_amt() {
		return this.nos_amt;
	}
	/** 設定申貸查詢時無擔保授信餘額(新台幣仟元) **/
	public void setNos_amt(BigDecimal value) {
		this.nos_amt = value;
	}

	/** 取得申貸查詢時學生助學貸款餘額(新台幣仟元)_有無 **/
	public String getSl_amt_flag() {
		return this.sl_amt_flag;
	}
	/** 設定申貸查詢時學生助學貸款餘額(新台幣仟元)_有無 **/
	public void setSl_amt_flag(String value) {
		this.sl_amt_flag = value;
	}

	/** 取得申貸查詢時學生助學貸款餘額(新台幣仟元) **/
	public BigDecimal getSl_amt() {
		return this.sl_amt;
	}
	/** 設定申貸查詢時學生助學貸款餘額(新台幣仟元) **/
	public void setSl_amt(BigDecimal value) {
		this.sl_amt = value;
	}

	/** 取得近12個月信用卡繳款狀況出現循環信用延遲次數_有無 **/
	public String getCc12_revol_pay_delay_times_flag() {
		return this.cc12_revol_pay_delay_times_flag;
	}
	/** 設定近12個月信用卡繳款狀況出現循環信用延遲次數_有無 **/
	public void setCc12_revol_pay_delay_times_flag(String value) {
		this.cc12_revol_pay_delay_times_flag = value;
	}

	/** 取得近12個月信用卡繳款狀況出現循環信用延遲次數 **/
	public Integer getCc12_revol_pay_delay_times() {
		return this.cc12_revol_pay_delay_times;
	}
	/** 設定近12個月信用卡繳款狀況出現循環信用延遲次數 **/
	public void setCc12_revol_pay_delay_times(Integer value) {
		this.cc12_revol_pay_delay_times = value;
	}

	/** 取得近12個月信用卡繳款狀況出現未繳足最低金額次數_有無 **/
	public String getCc12_minpay_delay_times_flag() {
		return this.cc12_minpay_delay_times_flag;
	}
	/** 設定近12個月信用卡繳款狀況出現未繳足最低金額次數_有無 **/
	public void setCc12_minpay_delay_times_flag(String value) {
		this.cc12_minpay_delay_times_flag = value;
	}

	/** 取得近12個月信用卡繳款狀況出現未繳足最低金額次數 **/
	public Integer getCc12_minpay_delay_times() {
		return this.cc12_minpay_delay_times;
	}
	/** 設定近12個月信用卡繳款狀況出現未繳足最低金額次數 **/
	public void setCc12_minpay_delay_times(Integer value) {
		this.cc12_minpay_delay_times = value;
	}

	/** 取得近12個月信用卡繳款狀況出現全額逾期未繳次數_有無 **/
	public String getCc12_totpay_delay_times_flag() {
		return this.cc12_totpay_delay_times_flag;
	}
	/** 設定近12個月信用卡繳款狀況出現全額逾期未繳次數_有無 **/
	public void setCc12_totpay_delay_times_flag(String value) {
		this.cc12_totpay_delay_times_flag = value;
	}

	/** 取得近12個月信用卡繳款狀況出現全額逾期未繳次數 **/
	public Integer getCc12_totpay_delay_times() {
		return this.cc12_totpay_delay_times;
	}
	/** 設定近12個月信用卡繳款狀況出現全額逾期未繳次數 **/
	public void setCc12_totpay_delay_times(Integer value) {
		this.cc12_totpay_delay_times = value;
	}

	/** 取得近12個月信用卡有預借現金餘額次數_有無 **/
	public String getCc12_cash_adv_times_flag() {
		return this.cc12_cash_adv_times_flag;
	}
	/** 設定近12個月信用卡有預借現金餘額次數_有無 **/
	public void setCc12_cash_adv_times_flag(String value) {
		this.cc12_cash_adv_times_flag = value;
	}

	/** 取得近12個月信用卡有預借現金餘額次數 **/
	public Integer getCc12_cash_adv_times() {
		return this.cc12_cash_adv_times;
	}
	/** 設定近12個月信用卡有預借現金餘額次數 **/
	public void setCc12_cash_adv_times(Integer value) {
		this.cc12_cash_adv_times = value;
	}

	/** 取得近3個月新業務申請查詢總家數_有無 **/
	public String getInq3_napp_bank_flag() {
		return this.inq3_napp_bank_flag;
	}
	/** 設定近3個月新業務申請查詢總家數_有無 **/
	public void setInq3_napp_bank_flag(String value) {
		this.inq3_napp_bank_flag = value;
	}

	/** 取得近3個月新業務申請查詢總家數 **/
	public Integer getInq3_napp_bank() {
		return this.inq3_napp_bank;
	}
	/** 設定近3個月新業務申請查詢總家數 **/
	public void setInq3_napp_bank(Integer value) {
		this.inq3_napp_bank = value;
	}

	/** 取得J10信用評分種類 **/
	public String getJ10_score_flag() {
		return this.j10_score_flag;
	}
	/** 設定J10信用評分種類 **/
	public void setJ10_score_flag(String value) {
		this.j10_score_flag = value;
	}

	/** 取得J10信用評分 **/
	public Integer getJ10_score() {
		return this.j10_score;
	}
	/** 設定J10信用評分 **/
	public void setJ10_score(Integer value) {
		this.j10_score = value;
	}

	/** 取得VEDA不良紀錄_有無 **/
	public String getVeda_adverse_file() {
		return this.veda_adverse_file;
	}
	/** 設定VEDA不良紀錄_有無 **/
	public void setVeda_adverse_file(String value) {
		this.veda_adverse_file = value;
	}

	/** 取得VEDA違約金額_有無 **/
	public String getVeda_default_outstanding() {
		return this.veda_default_outstanding;
	}
	/** 設定VEDA違約金額_有無 **/
	public void setVeda_default_outstanding(String value) {
		this.veda_default_outstanding = value;
	}

	/** 取得VEDA法院判決紀錄_有無 **/
	public String getVeda_judgement() {
		return this.veda_judgement;
	}
	/** 設定VEDA法院判決紀錄_有無 **/
	public void setVeda_judgement(String value) {
		this.veda_judgement = value;
	}

	/** 取得VEDA信用查詢次數_有無 **/
	public String getVeda_credit_enquiries_flag() {
		return this.veda_credit_enquiries_flag;
	}
	/** 設定VEDA信用查詢次數_有無 **/
	public void setVeda_credit_enquiries_flag(String value) {
		this.veda_credit_enquiries_flag = value;
	}

	/** 取得VEDA信用查詢次數 **/
	public Integer getVeda_credit_enquiries() {
		return this.veda_credit_enquiries;
	}
	/** 設定VEDA信用查詢次數 **/
	public void setVeda_credit_enquiries(Integer value) {
		this.veda_credit_enquiries = value;
	}

	/** 取得VEDA信用提供者_有無 **/
	public String getVeda_credit_provider() {
		return this.veda_credit_provider;
	}
	/** 設定VEDA信用提供者_有無 **/
	public void setVeda_credit_provider(String value) {
		this.veda_credit_provider = value;
	}

	/** 取得VEDA信用往來長度 **/
	public String getVeda_credit_file_age_flag() {
		return this.veda_credit_file_age_flag;
	}
	/** 設定VEDA信用往來長度 **/
	public void setVeda_credit_file_age_flag(String value) {
		this.veda_credit_file_age_flag = value;
	}

	/** 取得VEDA信用往來長度(年) **/
	public Integer getVeda_credit_file_age() {
		return this.veda_credit_file_age;
	}
	/** 設定VEDA信用往來長度(年) **/
	public void setVeda_credit_file_age(Integer value) {
		this.veda_credit_file_age = value;
	}

	/** 取得上傳資料日期 **/
	public Date getData_src_dt() {
		return this.data_src_dt;
	}
	/** 設定上傳資料日期 **/
	public void setData_src_dt(Date value) {
		this.data_src_dt = value;
	}
		
	/** 取得NCB逾期90天帳戶數_有無 **/
	public String getNcb_over_90_days_flag() {
		return ncb_over_90_days_flag;
	}
	/** 設定NCB逾期90天帳戶數_有無 **/
	public void setNcb_over_90_days_flag(String ncb_over_90_days_flag) {
		this.ncb_over_90_days_flag = ncb_over_90_days_flag;
	}
	
	/** 取得NCB逾期90天帳戶數 **/
	public Integer getNcb_over_90_days() {
		return ncb_over_90_days;
	}
	/** 設定NCB逾期90天帳戶數 **/
	public void setNcb_over_90_days(Integer ncb_over_90_days) {
		this.ncb_over_90_days = ncb_over_90_days;
	}
	
	/** 取得NCB最大逾期天數區間_有無 **/
	public String getNcb_maximum_dpd_flag() {
		return ncb_maximum_dpd_flag;
	}
	/** 設定NCB最大逾期天數區間_有無 **/
	public void setNcb_maximum_dpd_flag(String ncb_maximum_dpd_flag) {
		this.ncb_maximum_dpd_flag = ncb_maximum_dpd_flag;
	}
	
	/** 取得NCB最大逾期天數區間 **/
	public String getNcb_maximum_dpd() {
		return ncb_maximum_dpd;
	}
	/** 設定NCB最大逾期天數區間 **/
	public void setNcb_maximum_dpd(String ncb_maximum_dpd) {
		this.ncb_maximum_dpd = ncb_maximum_dpd;
	}
	
	/** 取得NCB近6個月信用查詢次數_有無 **/
	public String getNcb_enq_recent_6m_flag() {
		return ncb_enq_recent_6m_flag;
	}
	/** 設定NCB近6個月信用查詢次數_有無 **/
	public void setNcb_enq_recent_6m_flag(String ncb_enq_recent_6m_flag) {
		this.ncb_enq_recent_6m_flag = ncb_enq_recent_6m_flag;
	}
	
	/** 取得NCB近6個月信用查詢次數 **/
	public Integer getNcb_enq_recent_6m() {
		return ncb_enq_recent_6m;
	}
	/** 設定NCB近6個月信用查詢次數 **/
	public void setNcb_enq_recent_6m(Integer ncb_enq_recent_6m) {
		this.ncb_enq_recent_6m = ncb_enq_recent_6m;
	}	
}
