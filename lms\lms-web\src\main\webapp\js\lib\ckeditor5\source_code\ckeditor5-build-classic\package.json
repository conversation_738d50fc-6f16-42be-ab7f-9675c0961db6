{"name": "@ckeditor/ckeditor5-build-classic", "version": "35.1.0", "description": "The classic editor build of CKEditor 5 – the best browser-based rich text editor.", "keywords": ["ckeditor5-build", "ckeditor", "ckeditor5", "ckeditor 5", "wysiwyg", "rich text", "editor", "html", "contentEditable", "editing", "operational transformation", "ot", "collaboration", "collaborative", "real-time", "framework"], "main": "./build/ckeditor.js", "files": ["build", "ckeditor5-metadata.json", "CHANGELOG.md"], "dependencies": {"@ckeditor/ckeditor5-alignment": "^35.1.0", "@ckeditor/ckeditor5-font": "^35.1.0", "@ckeditor/ckeditor5-source-editing": "^35.1.0", "@ckeditor/ckeditor5-html-support": "^35.1.0", "@ckeditor/ckeditor5-adapter-ckfinder": "^35.1.0", "@ckeditor/ckeditor5-autoformat": "^35.1.0", "@ckeditor/ckeditor5-basic-styles": "^35.1.0", "@ckeditor/ckeditor5-block-quote": "^35.1.0", "@ckeditor/ckeditor5-ckbox": "^35.1.0", "@ckeditor/ckeditor5-ckfinder": "^35.1.0", "@ckeditor/ckeditor5-cloud-services": "^35.1.0", "@ckeditor/ckeditor5-easy-image": "^35.1.0", "@ckeditor/ckeditor5-editor-classic": "^35.1.0", "@ckeditor/ckeditor5-essentials": "^35.1.0", "@ckeditor/ckeditor5-heading": "^35.1.0", "@ckeditor/ckeditor5-image": "^35.1.0", "@ckeditor/ckeditor5-indent": "^35.1.0", "@ckeditor/ckeditor5-link": "^35.1.0", "@ckeditor/ckeditor5-list": "^35.1.0", "@ckeditor/ckeditor5-media-embed": "^35.1.0", "@ckeditor/ckeditor5-paragraph": "^35.1.0", "@ckeditor/ckeditor5-paste-from-office": "^35.1.0", "@ckeditor/ckeditor5-table": "^35.1.0", "@ckeditor/ckeditor5-typing": "^35.1.0"}, "devDependencies": {"@ckeditor/ckeditor5-core": "^35.1.0", "@ckeditor/ckeditor5-dev-utils": "^30.0.0", "@ckeditor/ckeditor5-dev-webpack-plugin": "^30.0.0", "@ckeditor/ckeditor5-theme-lark": "^35.1.0", "css-loader": "^5.2.7", "postcss-loader": "^4.3.0", "raw-loader": "^4.0.1", "style-loader": "^2.0.0", "terser-webpack-plugin": "^4.2.3", "ts-loader": "^9.3.0", "webpack": "^5.58.1", "webpack-cli": "^4.9.0"}, "engines": {"node": ">=14.0.0", "npm": ">=5.7.1"}, "author": "CKSource (http://cksource.com/)", "license": "GPL-2.0-or-later", "homepage": "https://ckeditor.com/ckeditor-5", "bugs": "https://github.com/ckeditor/ckeditor5/issues", "repository": {"type": "git", "url": "https://github.com/ckeditor/ckeditor5.git", "directory": "packages/ckeditor5-build-classic"}, "scripts": {"build": "webpack --mode production", "preversion": "npm run build"}}