package tw.com.jcs.flow.provider.impl;

import java.beans.Introspector;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import tw.com.jcs.flow.provider.ObjectFactory;

/**
 * <pre>
 * Spring物件工廠
 * </pre>
 * 
 * @since 2023年1月10日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月10日
 *          </ul>
 */
public class SpringObjectFactory implements ObjectFactory, ApplicationContextAware {

    private static final Logger log = LoggerFactory.getLogger(SpringObjectFactory.class);

    ApplicationContext appContext;

    /*
     * (non-Javadoc)
     * 
     * @see org.springframework.context.ApplicationContextAware#setApplicationContext(org.springframework.context.ApplicationContext)
     */
    @Resource
    public void setApplicationContext(ApplicationContext appContext) throws BeansException {
        this.appContext = appContext;
    }

    /**
     * 取得Spring容器中已初始化Bean
     */
    @Override
    @SuppressWarnings("unchecked")
    public <T> T create(Class<T> objClass) {
        Object obj = null;
        String[] names = appContext.getBeanNamesForType(objClass);
        if (names.length > 0) {
            obj = appContext.getBean(names[0], objClass);
        }
        if (obj == null) {
            log.debug("can't find bean '{}' in spring context, will use Class.newInstance()", objClass);
            // obj = super.create(objClass);
        }
        return (T) obj;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.provider.ObjectFactory#getClassLoader()
     */
    @Override
    public ClassLoader getClassLoader() {
        return appContext.getClassLoader();
    }

    /**
     * 取得Spring容器中已初始化Bean
     */
    @Override
    @SuppressWarnings("unchecked")
    public <T> T create(String className) {
        int dotIndex = className.lastIndexOf('.');
        String shortName = (dotIndex != -1 ? className.substring(dotIndex + 1) : className);
        Object obj = appContext.getBean(Introspector.decapitalize(shortName));
        if (obj == null) {
            log.debug("can't find bean '{}' in spring context, will use Class.newInstance()", className);
            // obj = super.create(className);
        }
        return (T) obj;
    }

}
