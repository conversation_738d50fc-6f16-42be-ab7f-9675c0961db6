/* 
 * L210A01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L210A01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L210A01A;

/** 異動聯貸案參貸比率授權檔 **/
@Repository
public class L210A01ADaoImpl extends LMSJpaDao<L210A01A, String> implements
		L210A01ADao {

	@Override
	public L210A01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L210A01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L210A01A> list = createQuery(L210A01A.class, search).getResultList();
		return list;
	}

	@Override
	public L210A01A findByUniqueKey(String mainId, String ownUnit,
			String authType, String authUnit) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "ownUnit", ownUnit);
		search.addSearchModeParameters(SearchMode.EQUALS, "authType", authType);
		search.addSearchModeParameters(SearchMode.EQUALS, "authUnit", authUnit);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L210A01A> findByIndex01(String mainId, String ownUnit,
			String authType, String authUnit) {
		ISearch search = createSearchTemplete();
		List<L210A01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (ownUnit != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownUnit",
					ownUnit);
		if (authType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "authType",
					authType);
		if (authUnit != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "authUnit",
					authUnit);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L210A01A.class, search).getResultList();
		}
		return list;
	}
}