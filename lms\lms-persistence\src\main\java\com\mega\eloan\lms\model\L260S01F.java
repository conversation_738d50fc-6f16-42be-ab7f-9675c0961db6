/* 
 * L260S01F.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 待追蹤ESG項目檔  **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L260S01F", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","itemNo"}))
public class L260S01F extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 存放L260M01D.oid
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 項次 **/
	@Size(max=4)
	@Column(name="ITEMNO", length=4, columnDefinition="VARCHAR(4)")
	private String itemNo;

	/** 
	 * 序號<p/>
	 * 畫面顯示順序
	 */
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="ITEMSEQ", columnDefinition="DECIMAL(5,0)")
	private Integer itemSeq;

	/** 
	 * 結果<p/>
	 * 已達成|Y, 未達成|N, 未達應檢視日|K
	 */
	@Size(max=1)
	@Column(name="CHKRESULT", length=1, columnDefinition="VARCHAR(1)")
	private String chkResult;
		
	/** 備註 **/
	@Size(max=600)
	@Column(name="ITEMMEMO", length=600, columnDefinition="VARCHAR(600)")
	private String itemMemo;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 畫面顯示序號 **/
	@Size(max=3)
	@Column(name="ITEMSEQSHOW", length=3, columnDefinition="VARCHAR(3)")
	private String itemSeqShow;

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 存放L260M01D.oid
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  存放L260M01D.oid
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得項次 **/
	public String getItemNo() {
		return this.itemNo;
	}
	/** 設定項次 **/
	public void setItemNo(String value) {
		this.itemNo = value;
	}

	/** 
	 * 取得序號<p/>
	 * 畫面顯示順序
	 */
	public Integer getItemSeq() {
		return this.itemSeq;
	}
	/**
	 *  設定序號<p/>
	 *  畫面顯示順序
	 **/
	public void setItemSeq(Integer value) {
		this.itemSeq = value;
	}

	/** 
	 * 取得結果<p/>
	 * 是|Y, 否|N, －|K
	 */
	public String getChkResult() {
		return this.chkResult;
	}
	/**
	 *  設定結果<p/>
	 *  是|Y, 否|N, －|K
	 **/
	public void setChkResult(String value) {
		this.chkResult = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 取得畫面顯示序號 **/
	public String getItemSeqShow() {
		return this.itemSeqShow;
	}
	/** 設定畫面顯示序號 **/
	public void setItemSeqShow(String value) {
		this.itemSeqShow = value;
	}
	
	/** 取得備註 **/
	public String getItemMemo() {
		return itemMemo;
	}
	/** 設定備註 **/
	public void setItemMemo(String itemMemo) {
		this.itemMemo = itemMemo;
	}
	
	
}
