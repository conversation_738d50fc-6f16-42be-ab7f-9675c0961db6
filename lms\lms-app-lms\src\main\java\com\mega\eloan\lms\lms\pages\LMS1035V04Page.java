package com.mega.eloan.lms.lms.pages;

import java.util.HashSet;
import java.util.Set;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanButtonItem;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 評等級數總表
 * </pre>
 * 
 * @since 2017/2/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/2/1,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1035v04")
public class LMS1035V04Page extends AbstractEloanInnerView {


	// public LMS1035V04Page(PageParameters parameters) {
	// super(parameters);
	// }

	@Override
	public void execute(ModelMap model, PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		Set<String> allowBrSet = new HashSet<String>();
		allowBrSet.add("900");
		allowBrSet.add("912");//風控處
//		if(allowBrSet.contains(user.getSsoUnitNo())){
			addToButtonPanel(model, new EloanButtonItem("btnUpload1", "ui-icon-jcs-12", "上傳PD評等級數總表"));
//		}
		
		renderJsI18N(LMS1035V04Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/lms/LMS1035V04Page');");
	}

	// @Override
	// public String[] getJavascriptPath() {
	// return new String[] { "pagejs/lms/LMS1035V04Page.js" };
	// }

}