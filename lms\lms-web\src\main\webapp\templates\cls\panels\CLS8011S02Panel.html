<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
      	<th:block th:fragment="panelFragmentBody">
        	<th:block th:if="${pageItemTypeA}">
				<input type='hidden' id='pageItemType' name='pageItemType' value='A'>
			</th:block>
			<th:block th:if="${pageItemTypeB}">
				<input type='hidden' id='pageItemType' name='pageItemType' value='B'>
			</th:block>
			<th:block th:if="${pageItemTypeC}">
				<input type='hidden' id='pageItemType' name='pageItemType' value='C'>
			</th:block>
			<th:block th:if="${pageItemTypeOther}">
				<input type='hidden' id='pageItemType' name='pageItemType' value=''>
			</th:block>
        	
			<!-- ====================================================================== -->
			<script type="text/javascript">
	            loadScript('pagejs/cls/CLS8011S02Panel');
	        </script>
			<!-- ====================================================================== -->
			<table class="tb2" width='99%'>
				<tr>
					<td class="noborder">
						<button type="button" id='btn_markAll'><span class="text-only"><th:block th:text="#{'btn.markAll'}">全選</th:block></span></button>
						&nbsp;
						<button type="button" id='btn_unMarkAll'><span class="text-only"><th:block th:text="#{'btn.unMarkAll'}">全取消</th:block></span></button>
						&nbsp;
						<button type="button" id='btn_addC801M01B'><span class="text-only"><th:block th:text="#{'btn.addC801M01B'}">增加項目</th:block></span></button>
						&nbsp;
					</td>
					<td colspan="2" class="noborder rt">
						&nbsp;
					</td>
				</tr>
			</table>	
			<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
				<thead>
					 <tr class='hd2'>
						<td nowrap width='20px;'>
							<th:block th:text="#{'C801M01A.title07'}">序號</th:block>							
						</td>						
						<td nowrap width='60px;'>
							<th:block th:text="#{'C801M01A.title08'}">勾選</th:block>
						</td>
						<td nowrap>
							<th:block th:text="#{'C801M01A.title09'}">文件名稱</th:block>
						</td>
						<td nowrap width='40px;'>
							<th:block th:text="#{'C801M01A.title10'}">風險等級</th:block>
						</td>
						<td nowrap width='20px;'>
							<th:block th:text="#{'C801M01A.title11'}">編號</th:block>
						</td>
					 </tr>																			
				</thead>	
						
				<tbody id="c801m01b_content">
				</tbody>
			</table>
			
        </th:block>
    </body>
</html>
