/* 
 *  CLS1151GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.handler.grid;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.CustIdFormatter;
import com.mega.eloan.common.formatter.I18NFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CMSDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.pages.CLS1141M01Page;
import com.mega.eloan.lms.cls.pages.CLS1151S01Page;
import com.mega.eloan.lms.cls.pages.CLS1220M04Page;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.cls.service.CLS1151Service;
import com.mega.eloan.lms.cls.service.CLS1171Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.bean.PTEAMAPP;
import com.mega.eloan.lms.mfaloan.service.MisLNF030Service;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.model.C100M01;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C900M01F;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01E;
import com.mega.eloan.lms.model.L140M01K;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140M01T;
import com.mega.eloan.lms.model.L140M01Y;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02B;
import com.mega.eloan.lms.model.L140S02H;
import com.mega.eloan.lms.model.L140S02M;
import com.mega.eloan.lms.model.L140S10A;
import com.mega.eloan.lms.model.L140S10C;
import com.mega.eloan.lms.model.L141M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 額度明細表grid
 * </pre>
 * 
 * @since 2012/12/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/13,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("cls1151gridhandler")
public class CLS1151GridHandler extends AbstractGridHandler {

	@Resource
	CLS1151Service cls1151Service;

	@Resource
	CLS1141Service cls1141Service;

	@Resource
	CLS1171Service cls1411Service;

	@Resource
	CLSService clsService;
	
	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;
	@Resource
	LMSService lmsService;

	@Resource
	ProdService prodService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	MisLNF030Service misLNF030Service;
	@Resource
	MisMISLN20Service misMISLN20Service;
	@Resource
	EloandbBASEService eloandbBASEService;
	@Resource
	MisPTEAMAPPService misPTEAMAPPService;

	private static String 額度明細表mainId = "tabFormMainId";
	private static String 產品種類seq = "L140S02ASeq";
	private static final String 顯示之最終評等 = "showGrade1";

	Properties prop;

	/**
	 * 取得對應訊息
	 * 
	 * @param key
	 * @return
	 */
	private String getI18nMsg(String key) {
		String result = null;
		if (prop == null)
			prop = MessageBundleScriptCreator
					.getComponentResource(CLS1151S01Page.class);
		if (prop != null) {
			result = prop.getProperty(Util.trim(key));
		}
		return Util.trim(result);
	}

	/**
	 * 查詢個金借保人檔
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140S01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.trim(params.getString(額度明細表mainId));
		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		Page<? extends GenericBean> page = cls1151Service.findPage(
				L140S01A.class, pageSetting);
		List<L140S01A> l140s01as = (List<L140S01A>) page.getContent();
		
		String[] codeType = { UtilConstants.CodeTypeItem.企業關係,
				UtilConstants.CodeTypeItem.親屬關係,
				UtilConstants.CodeTypeItem.綜合關係_企業,
				UtilConstants.CodeTypeItem.綜合關係_親屬 };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);
		
		for (L140S01A l140s01a : l140s01as) {
			/*
			此寫法可能把 type 原本的['1', '2'] 給修改成 ['' ,'*' ]
			改用  Transient
			
			if (UtilConstants.L140S01AType.授管處新增.equals(l140s01a.getType())) {
				l140s01a.setType("*");
			} else {
				l140s01a.setType("");
			}*/
			l140s01a.setTypeStr(UtilConstants.L140S01AType.授管處新增.equals(l140s01a.getType())?"*":"");
			
			if(Util.isNotEmpty(Util.trim(l140s01a.getRKindD()))){				
				l140s01a.setRKindDStr(LMSUtil.changeCustRlt((l140s01a.getRKindD()), codeMap));
			}else{
				l140s01a.setRKindDStr("");
			}
			
			l140s01a.setGuaPercentStr(l140s01a.getGuaPercent() == null ? "" : cls1151Service.fmt_l140s01a_guaPercent(l140s01a.getGuaPercent())+("%"));			
		}
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("custId", new CustIdFormatter());
		dataReformatter.put("custPos", new CodeTypeFormatter(codeTypeService, "L140S01A_custPos")); //
		dataReformatter.put("reson", new CodeTypeFormatter(codeTypeService, "cls1161m01_reson")); 
		dataReformatter.put("isLiveWithBorrower", new CodeTypeFormatter(codeTypeService, "Common_YesNo"));//
		
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢個金借保人檔
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryC120M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.trim(params.getString(額度明細表mainId));
		L140M01A l140m01a = cls1151Service.findL140m01aByMainId(mainId);
		String findId = Util.trim(params.getString("findId"));
		if (Util.isNotEmpty(findId)) {
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId",
					findId + "%");
		}
		// 查這份文件的MinId
		String caseMainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
				caseMainId);
		Page<? extends GenericBean> page = cls1151Service.findPage(
				C120M01A.class, pageSetting);
		// 排除額度明細表本身借款人
		List<C120M01A> c120m01as = (List<C120M01A>) page.getContent();
		List<C120M01A> newC120M01as = new ArrayList<C120M01A>();
		for (C120M01A c120m01a : c120m01as) {
			if (c120m01a.getCustId().equals(l140m01a.getCustId())
					&& c120m01a.getDupNo().equals(l140m01a.getDupNo())) {
				continue;
			}
			newC120M01as.add(c120m01a);
		}

		CapGridResult result = new CapGridResult(newC120M01as,
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("custId", new CustIdFormatter());
		dataReformatter.put("custPos", new CodeTypeFormatter(codeTypeService,
				"L140S01A_custPos")); //
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢產品種類Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140S02A(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String MainId = Util.trim(params.getString(額度明細表mainId));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);
		Map<String, String> codeMap = codeTypeService
				.findByCodeType("L140S02A_property");
		L140M01A l140m01a = clsService.findL140M01A_mainId(MainId);
		String busCode = "";
		if(l140m01a!=null){			
			busCode = clsService.get0024_busCode(l140m01a.getCustId(), l140m01a.getDupNo());			
		}
		// 第三個參數為formatting
		HashMap<String, String> ProdKindNameMap = prodService.getProdKindName(busCode);
		HashMap<String, String> SubCodeMap = prodService.getSubCode();
		Page<? extends GenericBean> page = cls1151Service.findPage(
				L140S02A.class, pageSetting);
		/*
		 * 透過 CLS1151S05Panel.js 指定 {sortname: "uiSeq|seq" , sortorder:
		 * "asc|asc" }
		 */
		List<L140S02A> l140s02as = (List<L140S02A>) page.getContent();
		for (L140S02A l140s02a : l140s02as) {
			l140s02a.setProdKind(Util.trim(ProdKindNameMap.get(l140s02a
					.getProdKind())));
			l140s02a.setSubjCode(Util.trim(SubCodeMap.get(l140s02a
					.getSubjCode())));
			l140s02a.setChkYN("Y".equals(Util.trim(l140s02a.getChkYN())) ? "O"
					: "X");
			l140s02a.setGrade1(LMSUtil.getFinalGrade(l140s02a.getModelKind(),
					Util.trim(l140s02a.getGrade1())));
			this.setLnSelect(l140s02a);
			l140s02a.setProperty(LMSUtil.cntrNoProperty_desc_orderBy(l140s02a, "、", codeMap));
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得授信期間格式
	 * 
	 * @param l140s02a
	 *            產品主檔
	 * @return
	 */
	private void setLnSelect(L140S02A l140s02a) {

		/**
		 * 
		 * 單選：<br/>
		 * 1.YYYY-MM-DD~ YYYY-MM-DD<br/>
		 * 2.YY年MM月<br/>
		 * 3.自核准日起YY年MM月<br/>
		 * 4.自簽約日起YY年MM月<br/>
		 * 5.其他
		 */
		String result = "";
		String year = Util.trim(l140s02a.getLnYear());
		String month = Util.trim(l140s02a.getLnMonth());
		// L140S02A.lnYear=年
		// L140S02A.lnMonth=月
		if (Util.isNotEmpty(year) && Util.isNotEmpty(month)) {
			result = StrUtils.concat(year, this.getI18nMsg("L140S02A.lnYear"),
					month, this.getI18nMsg("L140S02A.lnMonth"));
		}

		l140s02a.setLnSelect(result);
	}

	/**
	 * 查詢長擔額度序號rid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL140S02B(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String MainId = Util.trim(params.getString(額度明細表mainId));
		int L140S02ASeq = params.getInt(產品種類seq, 0);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "seq",
				L140S02ASeq);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1151Service.findPage(
				L140S02B.class, pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢個金借保人檔(for選取最終評等)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public CapMapGridResult queryL140S01AForGrade(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.trim(params.getString(額度明細表mainId));
		String caseMainId = Util.trim(params.getString("CaseMainId"));
		String prodKind = Util.trim(params.getString("prodKind"));
		String subjCode = Util.trim(params.getString("subjCode"));
		Page<Map<String, Object>> page = new Page(
				new ArrayList<Map<String, Object>>(), 0, 0, 0);
		if(Util.isEmpty(prodKind) || "null".equalsIgnoreCase(prodKind)){
			
		}else{
			String markModel = prodService.getMatchModelKind(mainId, prodKind,
					subjCode);
			
			if (Util.equals(UtilConstants.L140S02AModelKind.房貸, markModel)) {
				page = eloandbBASEService.queryL140S01AForGrade_G(pageSetting,
						caseMainId, mainId);
	
			} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, markModel)) {
				page = eloandbBASEService.queryL140S01AForGrade_notHouseLoan(
						pageSetting, caseMainId, mainId);
			} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, markModel)) {
				page = eloandbBASEService.queryL140S01AForGrade_cardLoan(
						pageSetting, caseMainId, mainId);
			}
	
			for (Map<String, Object> item : page.getContent()) {
				item.put(
						顯示之最終評等,
						ClsUtil.showGradeWithRatingDesc(markModel,
								Util.trim(item.get("GRADE3"))));
			}
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢代償轉貸借新還舊明細檔
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL140S02H(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.trim(params.getString(額度明細表mainId));
		int L140S02ASeq = params.getInt(產品種類seq, 0);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "seq",
				L140S02ASeq);
		Page<? extends GenericBean> page = cls1151Service.findPage(
				L140S02H.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		return result;
	}

	/**
	 * LNF030 取得放款帳號grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryLNF030(ISearch pageSetting,
			PageParameters params) throws CapException {
		// String mainId = Util.trim(params.getString(額度明細表mainId));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		// 測試用帳號
		// custId = "85800192";
		// dupNo = "0";
		List<Map<String, Object>> pageData = misLNF030Service.selByCustId(
				pageSetting, custId, dupNo);
		return new CapMapGridResult(pageData, pageData.size());
	}

	/*
	 * TODO********************************** 以上是修改過的code
	 * ************************************************************************
	 */

	/**
	 * 查詢額度明細表Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String caseMainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		String itemType = Util.trim(params.getString("itemType"));
		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.mainId", caseMainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.itemType", itemType);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Map<String, String> codeMap = codeTypeService
				.findByCodeType("lms1405s02_proPerty");
		Page<? extends GenericBean> page = cls1151Service.findPage(
				L140M01A.class, pageSetting);
		// 檢核欄V為通過檢核且經過計算，O為 通過檢核 但尚未計算，X為尚未通過檢核
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name)); //
		dataReformatter.put("docStatus", new I18NFormatter("docStatus."));
		List<L140M01A> l140m01alist = (List<L140M01A>) page.getContent();
		StringBuffer temp = new StringBuffer();
		for (L140M01A l140m01a : l140m01alist) {
			temp.setLength(0);
			String[] proretys = Util.trim(l140m01a.getProPerty()).split(
					UtilConstants.Mark.SPILT_MARK);
			for (String value : proretys) {
				temp.append(temp.length() > 0 ? "、" : "");
				temp.append(Util.trim(codeMap.get(value)));
			}
			l140m01a.setProPerty(temp.toString());
			if (UtilConstants.DEFAULT.是.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("V");
			} else if (UtilConstants.DEFAULT.否.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("O");
			} else {
				l140m01a.setChkYN("X");
			}
			String dataSrc = Util.trim(l140m01a.getDataSrc());
			if (Util.isNotEmpty(dataSrc)) {
				l140m01a.setDataSrc(this.getI18nMsg("L140M01A.dataSrc"
						+ dataSrc));
			} else {
				l140m01a.setDataSrc("");
			}
		}
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢非本案額度明細表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryDiffCntrNoDoc(ISearch pageSetting,
			PageParameters params) throws CapException {
		String custId = Util.trim(params.getString("queryDiffCntrNoId"));
		String dupNo = Util.trim(params.getString("queryDiffCntrNoDupNo"));
		String caseMainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		String itemType = UtilConstants.Cntrdoc.ItemType.額度明細表;

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docURL",
				UtilConstants.CaseSchema.個金);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.itemType", itemType);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
				"l120m01c.mainId", caseMainId);
		Map<String, String> codeMap = codeTypeService
				.findByCodeType("lms1405s02_proPerty");

		Page<? extends GenericBean> page = cls1151Service.findPage(
				L140M01A.class, pageSetting);
		// 檢核欄V為通過檢核且經過計算，O為 通過檢核 但尚未計算，X為尚未通過檢核
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name)); //
		dataReformatter.put("docStatus", new I18NFormatter("docStatus."));
		List<L140M01A> l140m01alist = (List<L140M01A>) page.getContent();
		StringBuffer temp = new StringBuffer();
		for (L140M01A l140m01a : l140m01alist) {
			temp.setLength(0);
			String[] proretys = Util.trim(l140m01a.getProPerty()).split(
					UtilConstants.Mark.SPILT_MARK);
			for (String value : proretys) {
				temp.append(temp.length() > 0 ? "、" : "");
				temp.append(Util.trim(codeMap.get(value)));
			}
			l140m01a.setProPerty(temp.toString());
			if (UtilConstants.DEFAULT.是.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("V");
			} else if (UtilConstants.DEFAULT.否.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("O");
			} else {
				l140m01a.setChkYN("X");
			}
			String dataSrc = Util.trim(l140m01a.getDataSrc());
			if (Util.isNotEmpty(dataSrc)) {
				l140m01a.setDataSrc(this.getI18nMsg("L140M01A.dataSrc"
						+ dataSrc));
			} else {
				l140m01a.setDataSrc("");
			}
		}
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢額度明細表-額度序號Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL140m01aCntrNo(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String caseMainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		String itemType = Util.trim(params.getString("itemType"));
		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.mainId", caseMainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.itemType", itemType);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docURL",
				UtilConstants.CaseSchema.個金);
		pageSetting
				.addSearchModeParameters(SearchMode.NOT_EQUALS, "cntrNo", "");
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "cntrNo",
				Util.trim(params.getString("cntrNo")));

		Page<? extends GenericBean> page = cls1151Service.findPage(
				L140M01A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name)); //
		dataReformatter.put("docStatus", new I18NFormatter("docStatus."));

		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢額度明細表Grid 資料 在聯行額度明細表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01aByL141m01b(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String caseMainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		L141M01A l141m01a = cls1411Service.findL141M01AByMainId(caseMainId);
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(l141m01a
				.getSrcMainId());
		String itemType = lmsService.checkL140M01AItemType(l120m01a);
		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l141m01b.mainId", caseMainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l141m01b.itemType", itemType);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		// pageSetting.addOrderBy("custId");
		Page<? extends GenericBean> page = cls1151Service.findPage(
				L140M01A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name)); //
		dataReformatter.put("docStatus", new I18NFormatter("docStatus."));
		result.setDataReformatter(dataReformatter);
		List<L140M01A> l140m01as = (List<L140M01A>) page.getContent();
		for (L140M01A l140m01a : l140m01as) {
			if (UtilConstants.DEFAULT.是.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("V");
			} else if (UtilConstants.DEFAULT.否.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("O");
			} else {
				l140m01a.setChkYN("X");
			}
			l140m01a.setApprover(l140m01a.getDocStatus());
			String dataSrc = Util.trim(l140m01a.getDataSrc());
			if (Util.isNotEmpty(dataSrc)) {
				l140m01a.setDataSrc(this.getI18nMsg("L140M01A.dataSrc")
						+ dataSrc);
			} else {
				l140m01a.setDataSrc("");
			}
		}

		return result;
	}

	/**
	 * 篩選要複製的額度明細表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            <pre>
	 *            {
	 *            	type:1.列出本案借戶項下所有額度明細表 2.列出特定借戶項下所有額度明細表 3.列出所有額度明細表
	 *            	brNo:分行代號
	 *            borrowId:客戶統編+重覆序號
	 *                     
	 *            }
	 * </pre>
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryCopyFitlePeole(ISearch pageSetting,
			PageParameters params) throws CapException {
		String custId = Util.trim(params.getString("copyCntrNoDoc_id"));
		String dupNo = Util.trim(params.getString("copyCntrNoDoc_DupNo"));
		// 是否為本案借款人
		String copyCntrNoDocYN = Util.trim(params.getString("copyCntrNoDocYN"));
		String caseMainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId.toUpperCase());
		}
		if (Util.isNotEmpty(dupNo)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
					dupNo.toUpperCase());
		}

		if (UtilConstants.DEFAULT.是.equals(copyCntrNoDocYN)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"l120m01c.mainId", caseMainId);
		} else {
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"l120m01c.mainId", caseMainId);
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docURL",
				UtilConstants.CaseSchema.個金);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "docStatus",
				FlowDocStatusEnum.結案.getCode());
		Page<? extends GenericBean> page = cls1151Service.findPage(
				L140M01A.class, pageSetting);

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name)); //
		dataReformatter.put("docStatus", new I18NFormatter("docStatus."));
		result.setDataReformatter(dataReformatter);
		Map<String, String> codeMap = codeTypeService
				.findByCodeType("lms1405s02_proPerty");
		StringBuffer temp = new StringBuffer();
		List<L140M01A> l140m01as = (List<L140M01A>) page.getContent();
		for (L140M01A l140m01a : l140m01as) {
			temp.setLength(0);
			String[] proretys = Util.trim(l140m01a.getProPerty()).split(
					UtilConstants.Mark.SPILT_MARK);
			for (String value : proretys) {
				temp.append(temp.length() > 0 ? "、" : "");
				temp.append(Util.trim(codeMap.get(value)));
			}
			l140m01a.setProPerty(temp.toString());
			l140m01a.setCaseNo(Util.toSemiCharString(l140m01a.getCaseNo()));
		}
		return result;
	}

	/**
	 * 查詢攤貸比率Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01e(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.trim(params.getString(額度明細表mainId));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Page<? extends GenericBean> page = cls1151Service.findPage(
				L140M01E.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("shareBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name)); //
		result.setDataReformatter(dataReformatter);
		List<L140M01E> l140m01es = (List<L140M01E>) page.getContent();
		for (L140M01E l140m01e : l140m01es) {
			if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(l140m01e
					.getShareFlag())) {
				l140m01e.setShowRate(l140m01e.getShareRate1() + "/"
						+ l140m01e.getShareRate2());
			} else {
				l140m01e.setShowRate("");
			}
		}
		return result;
	}

	/**
	 * 查詢聯行額度明細表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL141m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		String searchType = params.getString("type");
		String brNo = params.getString("brNo", "");
		// 建立主要Search 條件

		if (!Util.isEmpty(brNo) && brNo.length() == 3) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					brNo);
		}

		if ("1".equals(searchType) || "2".equals(searchType)) {
			String borrowId = Util.trim(params.getString("borrowId", ""));
			String custId = borrowId.substring(0, borrowId.length() - 1);
			String dupNo = borrowId.substring(borrowId.length() - 1,
					borrowId.length());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId.toUpperCase());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
					dupNo.toUpperCase());
		}

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = cls1411Service.findPage(
				L141M01A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("caseBrId", new BranchNameFormatter(branchService,
				BranchNameFormatter.ShowTypeEnum.ID_Name)); // 分行名稱格式化
		dataReformatter.put("coAppraiser", new UserNameFormatter(
				userInfoService, UserNameFormatter.ShowTypeEnum.Name)); // 使用者名稱格式化
		result.setDataReformatter(dataReformatter);
		List<L141M01A> l141m01as = (List<L141M01A>) page.getContent();
		for (L141M01A l141m01a : l141m01as) {
			l141m01a.setCaseNo(Util.toSemiCharString(l141m01a.getCaseNo()));
		}
		return result;
	}

	/**
	 * 查詢L120M01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
				UtilConstants.Casedoc.typCd.海外);
		// 限定只顯示個金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.個金);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120M01A.class, pageSetting);
		StringBuilder allCust = new StringBuilder();
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			allCust.setLength(0);
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo()).append(" ")
					.append(model.getCustName());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());

			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode()));
			} else {
				model.setDocStatus("");
			}
			model.setUpdater(this.getUserName(model.getUpdater()));
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(this.getUserName(model
						.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(Util.trim(model.getAreaAppraiser()));
			}
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(this.getUserName(model.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setApproveTime(model.getAreaSendInfo());
			}

		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢收付彙計數 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryCollect(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = params.getString(額度明細表mainId, "");
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Page<? extends GenericBean> page = cls1151Service.findPage(
				L140M01K.class, pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢額度明細表擔保品table
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL140m01o(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.trim(params.getString(額度明細表mainId));
		String type = Util.trim(params.getString("type"));
		if (Util.isNotEmpty(type)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "collTyp1",
					type);
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Page<? extends GenericBean> page = cls1151Service.findPage(
				L140M01O.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("branch", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name));
		dataReformatter.put("collTyp1", new CodeTypeFormatter(codeTypeService,
				"lmsUseCms_collTyp1"));

		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢擔保品
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            <pre>
	 *            colltyp1 擔保品大類 
	 *            custId 客戶統編 
	 *            dupNo 重覆序號 
	 *            branch 分行代號
	 * </pre>
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryCMS(ISearch pageSetting, PageParameters params) throws CapException {
		boolean query = params.getAsBoolean("query", false);
		String colltyp1 = Util.trim(params.getString("cmsType", ""));
		String custId = Util.trim(params.getString("cmsCustId", ""));
		String dupNo = Util.trim(params.getString("cmsDupNo", ""));
		String branch = Util.trim(params
				.getString("selectFilterCMDBrno", "005"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "collTyp1",
				colltyp1);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);
		// '11B','12B','13B','14B','15B','16B','18B','1DC','17S','19S','1AS','1BS'
		pageSetting.addSearchModeParameters(
				SearchMode.IN,
				EloanConstants.DOC_STATUS,
				new String[] { CMSDocStatusEnum.分行_編製中.getCode(),
						CMSDocStatusEnum.分行_待覆核.getCode(),
						CMSDocStatusEnum.分行_已覆核.getCode(),
						CMSDocStatusEnum.分行_待設質.getCode(),
						CMSDocStatusEnum.分行_已設質.getCode(),
						CMSDocStatusEnum.分行_待塗銷.getCode(),
						CMSDocStatusEnum.聯行傳回.getCode(),
						CMSDocStatusEnum.代鑑價編製中.getCode(),
						CMSDocStatusEnum.代鑑價待覆核.getCode(),
						CMSDocStatusEnum.代鑑價已完成.getCode(),
						CMSDocStatusEnum.營運中心_編製中.getCode(),
						CMSDocStatusEnum.營運中心_待覆核.getCode(),
						CMSDocStatusEnum.營運中心_已覆核.getCode(),
						CMSDocStatusEnum.營運中心_已傳回.getCode(),
						CMSDocStatusEnum.營運中心_待收件.getCode(),
						CMSDocStatusEnum.營運中心_覆核待收件.getCode(),
						CMSDocStatusEnum.營運中心_覆核編制中.getCode(),
						CMSDocStatusEnum.營運中心_覆核待覆核.getCode(),
						CMSDocStatusEnum.營運中心_覆核已覆核.getCode(),
						CMSDocStatusEnum.營運中心_覆核已傳回.getCode(),
						CMSDocStatusEnum.待斷頭.getCode(),
						CMSDocStatusEnum.已斷頭.getCode(),
						CMSDocStatusEnum.補提.getCode(),
						CMSDocStatusEnum.擔保率不足.getCode(),
						CMSDocStatusEnum.授管處_編製中.getCode(),
						CMSDocStatusEnum.授管處_待覆核.getCode(),
						CMSDocStatusEnum.授管處_已覆核.getCode() });
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		Page<? extends GenericBean> page = lmsService.findPage(C100M01.class,
				pageSetting);
		// 當訊息為空且為查詢動作
		if (page.getTotalRow() == 0 && query) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(CLS1141M01Page.class);
			// CLS1141.097=借款人統編在該分行之擔保品系統中找不到資料，請再次確認！！
			throw new CapMessageException(prop.getProperty("CLS1141.097"),
					getClass());
		}
		List<C100M01> c100m01s = (List<C100M01>) page.getContent();
		for (C100M01 model : c100m01s) {
			model.setAppraiserName(userInfoService.getUserName(model.getAppraiser()));
		}
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("branch", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name));
		dataReformatter.put("collTyp1", new CodeTypeFormatter(codeTypeService,
				"lmsUseCms_collTyp1"));
		if (UtilConstants.CollTyp1.保證.equals(colltyp1)) {
			dataReformatter.put("collTyp2", new CodeTypeFormatter(
					codeTypeService, "cms1090_collTyp2_5"));
		} else if (UtilConstants.CollTyp1.動產.equals(colltyp1)
				|| UtilConstants.CollTyp1.信託占有.equals(colltyp1)) {
			dataReformatter.put("collTyp2", new CodeTypeFormatter(
					codeTypeService, "cms1090_collTyp2_2"));
		} else if (UtilConstants.CollTyp1.權利質權.equals(colltyp1)) {
			dataReformatter.put("collTyp2", new CodeTypeFormatter(
					codeTypeService, "cms1090_collTyp2_3"));
		} else {
			dataReformatter.put("collTyp2", new CodeTypeFormatter(
					codeTypeService, ""));
		}

		dataReformatter.put("docStatus", new I18NFormatter("status."));
		dataReformatter.put("docDscr", new I18NFormatter("dscr."));
		result.setDataReformatter(dataReformatter);

		return result;
	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (Util.isEmpty(userId)) {
			return "";
		}
		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

	/**
	 * 搜尋團貸年度總額度檔
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult PTEAMAPPQuery(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		boolean pteamapp_chk_effFrom = clsService.is_function_on_codetype("pteamapp_chk_effFrom_grid");
		for(Map<String, Object> dataRow :misPTEAMAPPService.getPTEAMAPPDataByEFFEND(custId,
				Util.isEmpty(dupNo) ? "0" : dupNo)){
			if(pteamapp_chk_effFrom){
				Date effFrom = (Date)MapUtils.getObject(dataRow, "EFFFROM");					
				if(effFrom==null || (effFrom!=null && LMSUtil.cmpDate(CapDate.getCurrentTimestamp(), ">=", effFrom))){
					//ok
				}else{
					continue;
				}
			}
			//============
			list.add(dataRow);	
		}
		return new CapMapGridResult(list, list.size());
	}

	public CapMapGridResult genL140M01A_pteamappGrid(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		String custOid = Util.trim(params.getString("custOid", ""));
		C120M01A c120m01a = cls1151Service.findModelByOid(C120M01A.class,
				custOid);

		if (c120m01a != null) {
			String custId = Util.trim(c120m01a.getCustId()).toUpperCase();
			String dupNo = Util.trim(c120m01a.getDupNo()).toUpperCase();

			// 參考 PTEAMAPPQuery. ParentAction.openbox()
			// 若額度性質為取消, 會把 EFFEND 寫入 0001-01-01
			// 為抓出, 把 effend 的值填入 0001-01-01 再減1天
			List<PTEAMAPP> pteamapp_list = misPTEAMAPPService.getDataBykey(
					custId,
					dupNo,
					TWNDate.toAD(CapDate.shiftDays(
							CapDate.parseDate(CapDate.ZERO_DATE), -1)));

			for (PTEAMAPP pteamapp : pteamapp_list) {
				Map<String, Object> row = new HashMap<String, Object>();

				LMSUtil.meta_to_map(row, pteamapp,
						new String[] { "issuebrno", "year", "projectnm",
								"buildname", "grpcntrno", "subcompnm",
								"efffrom", "effend", "overamt", "amtappno" });
				String issuebrno = Util.trim(pteamapp.getIssuebrno());
				row.put("issuebrno_name",
						issuebrno
								+ Util.trim(branchService
										.getBranchName(issuebrno)));
				// ---
				list.add(row);
			}

		}

		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	public CapMapGridResult genCancelCntrNoGrid(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 用來儲存已經產生的額度明細表
		Set<String> isHavecntrNos = new HashSet<String>();

		String custOid = Util.trim(params.getString("custOid", ""));
		C120M01A c120m01a = cls1151Service.findModelByOid(C120M01A.class,
				custOid);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		if (c120m01a != null) {

			String custId = Util.trim(c120m01a.getCustId()).toUpperCase();
			String dupNo = Util.trim(c120m01a.getDupNo()).toUpperCase();
			String cntrNo = "";

			List<Map<String, Object>> ln_list = misMISLN20Service
					.findCancelCntrNoByCustId(custId, dupNo);
			if (ln_list != null && ln_list.size() > 0) {
				for (Map<String, Object> map : ln_list) {
					map.put("DATA_SRC", "a-Loan");
					cntrNo = Util.trim(map.get("LNF020_CONTRACT"));

					if (isHavecntrNos.contains(cntrNo)) {
						continue;
					} else {
						isHavecntrNos.add(cntrNo);
						list.add(map);
					}
				}
				// list.addAll(ln_list);
			}

			List<Map<String, Object>> lms_list = eloandbBASEService
					.findAllL140M01ACancelCntrnoDataByID(custId, dupNo);
			if (lms_list != null && lms_list.size() > 0) {
				for (Map<String, Object> map : lms_list) {
					map.put("DATA_SRC", "e-Loan");
					cntrNo = Util.trim(map.get("LNF020_CONTRACT"));

					if (isHavecntrNos.contains(cntrNo)) {
						continue;
					} else {
						isHavecntrNos.add(cntrNo);
						list.add(map);
					}
				}
			}
		}

		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	class SubjectDesc implements IBeanFormatter {
		private static final long serialVersionUID = 1L;

		private Map<String, String> map = null;

		public SubjectDesc(Map<String, String> map) {
			this.map = map;
		}

		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			String tt = ((C900M01F) in).getSubject();
			if (tt != null) {
				return LMSUtil.getDesc(map, tt);
				// return new CodeTypeFormatter(codeTypeService,
				// UtilConstants.CodeTypeItem.授信科目).reformat(tt);
			} else {
				return "";
			}
		}
	}

	/**
	 * 查詢科子目限額Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryC900M01F(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));
		String lmtType = Util.nullToSpace(params.getString("lmtType"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "lmtType",
				lmtType);
		Page<? extends GenericBean> page = cls1151Service.findPage(
				C900M01F.class, pageSetting);

		if ("1".equals(lmtType)) {
			CapGridResult result = new CapGridResult(page.getContent(),
					page.getTotalRow());
			Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();

			// lms1405m01_SubItem授信科目
			dataReformatter.put("subjectDesc",
					new SubjectDesc(cls1151Service.getC900M01D_map())); // codeType格式化
			result.setDataReformatter(dataReformatter);
			return result;
		} else {

			StringBuilder buffer = new StringBuilder("");
			Map<String, String> map = cls1151Service.getC900M01D_map();

			List<C900M01F> c900m01fs = (List<C900M01F>) page.getContent();
			for (C900M01F c900m01f : c900m01fs) {
				String[] subject = c900m01f.getSubject().split(
						UtilConstants.Mark.SPILT_MARK);
				for (String item : subject) {
					buffer.append(buffer.length() > 0 ? "、" : "");
					buffer.append(LMSUtil.getDesc(map, item));
				}
				c900m01f.setSubject(buffer.toString());
				buffer.setLength(0);
			}

			return new CapGridResult(c900m01fs, page.getTotalRow());
		}

	}
	
	public CapGridResult queryL140m01t(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "flag",
				params.getString("flag"));

		Page<? extends GenericBean> page = clsService.findPage(
				L140M01T.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		Map<String, String> map_estateType = codeTypeService
				.findByCodeType("estateType");
		Map<String, String> map_estateSubType = codeTypeService
				.findByCodeType("estateSubType");
		if(true){
			//目前 codeType 裡的中文描述，與 UI 不同〔都更/危老/其他都更危老 vs 重建〕			
			map_estateType.put("D01", getI18nMsg("L140M01T.estateType.D01.desc"));
		}
		
		class EstateTypeFormatter implements IBeanFormatter {
			private static final long serialVersionUID = 2501150363189246663L;
			Map<String, String> map_estateType;
			Map<String, String> map_estateSubType;

			public EstateTypeFormatter(Map<String, String> map_estateType,
					Map<String, String> map_estateSubType) {
				this.map_estateType = map_estateType;
				this.map_estateSubType = map_estateSubType;
			}

			@Override
			public String reformat(Object in) throws CapFormatException {
				L140M01T l140m01t = (L140M01T) in;
				String _type = Util.trim(l140m01t.getEstateType());
				String _subType = Util.trim(l140m01t.getEstateSubType());
				return LMSUtil.estateType_estateSubType_desc(_type, _subType, map_estateType, map_estateSubType);
			}
		}

		formatter.put("estateType", new EstateTypeFormatter(map_estateType, map_estateSubType));

		formatter.put("checkYN", new IFormatter() {

			@Override
			public String reformat(Object in) throws CapFormatException {
				String txt = (String) in;
				if ("Y".equals(txt)) {
					return "O";
				}
				return "X";
			}
		});

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}


	public CapMapGridResult queryL140m01y(ISearch pageSetting,
			PageParameters params) throws CapException {
		String tabFormMainId = Util.nullToSpace(params.getString(額度明細表mainId));
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		
		boolean is_docCode1 = true; 
		L140M01A l140m01a = clsService.findL140M01A_mainId(tabFormMainId);
		if(l140m01a!=null){
			L120M01C l120m01c = l140m01a.getL120m01c();
			if(l120m01c!=null){
				L120M01A l120m01a = clsService.findL120M01A_mainId(l120m01c.getMainId());
				if(LMSUtil.isParentCase(l120m01a)){
					is_docCode1 = false;
				}
			}
		}
		Map<String, String> refType_descMap = codeTypeService.findByCodeType(is_docCode1?"L140M01Y_refType_docCode1":"L140M01Y_refType_docCode5");
		
		Map<String, String> refModel_descMap = new HashMap<String, String>();
		if(true){
			refModel_descMap.put("C122M01A", "開啟");
		}
		for(L140M01Y l140m01y : clsService.findL140M01YOrderDefault(tabFormMainId)){
			Map<String, Object> map = new HashMap<String, Object>();
			//~~~
			String refType = Util.trim(l140m01y.getRefType());
			String refModel = Util.trim(l140m01y.getRefModel());
			map.put("oid", l140m01y.getOid());
			map.put("refType", LMSUtil.getDesc(refType_descMap, refType));
			map.put("valueDesc", cls1151Service.l140m01y_valueDesc(l140m01y));
			map.put("refModelDesc", refModel_descMap.containsKey(refModel)?refModel_descMap.get(refModel):"");
			map.put("refModel", refModel);
			map.put("refOid", Util.trim(l140m01y.getRefOid()));
			map.put("refMainId", Util.trim(l140m01y.getRefMainId()));
			//~~~
			list.add(map);
		}

		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	public CapGridResult queryC122M01A_for_ELF459_srcflag_1(ISearch pageSetting,
			PageParameters params) throws CapException {
		String custId = Util.trim(params.getString("custId"));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);

		pageSetting.addSearchModeParameters(SearchMode.IN, "applyKind", new String[] {UtilConstants.C122_ApplyKind.P, UtilConstants.C122_ApplyKind.E
				, UtilConstants.C122_ApplyKind.I, UtilConstants.C122_ApplyKind.J, UtilConstants.C122_ApplyKind.O});//J-111-0226 加上青創貸款進件(I、J) J-112-0006 加上其他進件(O) 
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		//不抓取消或不承作的項目
		pageSetting.addSearchModeParameters(SearchMode.AND, 
				new SearchModeParameter(SearchMode.NOT_EQUALS, "docStatus","E02"),
				new SearchModeParameter(SearchMode.AND, 
				new SearchModeParameter(SearchMode.NOT_LIKE, "docStatus","I%"),
				new SearchModeParameter(SearchMode.AND, 
				new SearchModeParameter(SearchMode.NOT_LIKE, "docStatus","G%"),
				new SearchModeParameter(SearchMode.NOT_LIKE, "docStatus","F%"))));
		
		Page<? extends GenericBean> page = clsService.findPage(C122M01A.class, pageSetting);

		CapGridResult result = new CapGridResult(page.getContent(), page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		if(true){
			dataReformatter.put("ownBrId", new BranchNameFormatter(branchService, ShowTypeEnum.Name)); 
			
			/*
			 * 在 CLS1220M04Page_zh_TW.properties 指定 {grid.applyKind.P, grid.applyKind.E}
			 */
			Properties prop_CLS1220M04Page = MessageBundleScriptCreator.getComponentResource(CLS1220M04Page.class);
			dataReformatter.put("applyKind", new I18NFormatter(prop_CLS1220M04Page, "grid.applyKind."));
			dataReformatter.put("IncomType", new I18NFormatter(prop_CLS1220M04Page, "grid.IncomType."));
		}
		result.setDataReformatter(dataReformatter);
		return result;
	}
	
	// J-113-0227 配合房貸核貸成數新增檢核邏輯，當專案種類為Y1/Y2/Y3時，新增搭配X1額度序號功能
	// 找到該額度搭配的X1 清單 (在新增這個額度所搭配的X1時，會把這個額度序號寫回同一簽報書底下所搭配的那筆X1額度的refCntrNo欄位
	public CapMapGridResult getRefX1CntrNoList(ISearch pageSetting,
			PageParameters params) throws CapException {
		String tabFormMainId = Util.nullToSpace(params.getString(額度明細表mainId));
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		
		L140M01A l140m01a = clsService.findL140M01A_mainId(tabFormMainId);
		if(l140m01a != null && Util.isNotEmpty(l140m01a.getCntrNo())){//如果還沒有額度序號不開放寫入搭配的X1
			String cntrNo = l140m01a.getCntrNo();
			L120M01C l120m01c = l140m01a.getL120m01c();
			if(l120m01c!=null){
				// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
				String itemType = Util.trim(l120m01c.getItemType());
				List<L140M01A> l140m01as = 
					cls1151Service.findL140m01aListByL120m01cMainId(l120m01c.getMainId(), itemType);
				for (L140M01A l140m01a_x1 : l140m01as) {
					if(Util.equals(cntrNo, Util.trim(l140m01a_x1.getProjRefCntrNo()))){
						Map<String, Object> map = new HashMap<String, Object>();
						map.put("l140m01aOid_x1", l140m01a_x1.getOid());
						map.put("cntrNo_x1", Util.trim(l140m01a_x1.getCntrNo()));
						list.add(map);
					}
				}
			}
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}	
	
	public CapMapGridResult queryIsSuspectedHeadAccountCheckResult(ISearch pageSetting, PageParameters params) throws CapException {
		String l140m01a_mainId = Util.trim(params.getString("mainId"));
		List<Map<String, Object>> list = this.eloandbBASEService.getCheckResultForIsSuspectedHeadAccount(l140m01a_mainId);
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);		
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
	
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public CapMapGridResult importRealEstateAgentInfo(ISearch pageSetting, PageParameters params) throws CapException {
		String l140m01a_mainId = Util.trim(params.getString("mainId"));
		List<Map<String, Object>> list = this.cls1151Service.getRealEstateAgentInfo(l140m01a_mainId);
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	//J-109-0304_10702_B1003 Web e-Loan消金檢核地號是否符合整批貸款基地地號
	public CapGridResult queryL140S02M(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString("mainId"));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);

		Page<? extends GenericBean> page = clsService.findPage(L140S02M.class, pageSetting);

		CapGridResult result = new CapGridResult(page.getContent(), page.getTotalRow());
		return result;
	}
	//J-109-0470_10702_B1001配合海管處因應本行110年施行LTV法，海外分行Web e-Loan擔保品管理系統新增/修改相關欄位及ADC維護頁面等程式功能修改
	public CapMapGridResult queryAdcList(ISearch pageSetting,
			PageParameters params) throws CapException {
		String queryType = Util.trim(params.getString("queryType", ""));
		String queryCustId = Util.trim(params.getString("queryCustId", ""));
		String queryDupNo = Util.trim(params.getString("queryDupNo", ""));
		String queryCntrNo = Util.trim(params.getString("queryCntrNo", ""));
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		if(Util.isNotEmpty(queryType)) {
			HashSet<String> adcCaseNoSet = lmsService.getAdcCaseNoList(queryType, queryCustId, queryDupNo, queryCntrNo, "");
			for (String no : adcCaseNoSet) {
				data = new HashMap<String, Object>();
				data.put("adcCaseNo", no);
				newList.add(data);
			}
		}
		Page<Map<String, Object>> pages = LMSUtil.setPageMap(newList, pageSetting);
        return new CapMapGridResult(pages.getContent(), newList.size());
    }
	
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public CapMapGridResult queryIndustryCode(ISearch pageSetting, PageParameters params) throws CapException {
		String industryCode = Util.trim(params.getString("industryCode", ""));
		List<Map<String, Object>> rtnList = new ArrayList<Map<String, Object>>();
		
		if(Util.isNotEmpty(industryCode)) {
			
			Map<String, Object> map = this.cls1151Service.queryIndustryTypeInfo(industryCode);
			if(map != null){
				rtnList.add(map);
			}
		}
		
		Page<Map<String, Object>> pages = LMSUtil.setPageMap(rtnList, pageSetting);
		return new CapMapGridResult(pages.getContent(), rtnList.size());
	}
	
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public CapMapGridResult queryL140s10aBizCatMainGridData(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		List<L140S10A> l140s10aList = this.cls1151Service.findL140s10aByMainId(mainId);
		
		List<Map<String, Object>> rtnList = new ArrayList<Map<String, Object>>();
		Map<String, String> typeNameMap = this.codeTypeService.findByCodeType("template_type");
		for (L140S10A l140s10a : l140s10aList) {

			Map<String, Object> m = new HashMap<String, Object>();
			m.put("seqName", NumConverter.toChineseNumber(l140s10a.getSeq()));
			m.put("bizCat", l140s10a.getBizCat());
			m.put("bizCatName", typeNameMap.get(l140s10a.getBizCat()));
			m.put("loanTpsName", l140s10a.getLoanTPsName());
			m.put("seq", l140s10a.getSeq());
			rtnList.add(m);
		}
		
		return new CapMapGridResult(rtnList, rtnList.size());
	}
	
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public CapMapGridResult queryTemplateItemAndContentGridData(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		String bizCatType = Util.nullToSpace(params.getString("bizCat"));
		List<Map<String, Object>> rtnList = this.cls1151Service.getTemplateItemAndContentGridData(mainId, bizCatType);
		return new CapMapGridResult(rtnList, rtnList.size());
	}
	
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public CapMapGridResult queryTemplateContentGridData(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		String bizCatType = Util.nullToSpace(params.getString("bizCat"));
		String bizCatItem = Util.nullToSpace(params.getString("bizItem"));
		
		List<L140S10C> list = this.cls1151Service.getL140s10cContentData(mainId, bizCatType, bizCatItem);
		
		List<Map<String, Object>> rtnList = new ArrayList<Map<String, Object>>();
		for (L140S10C l140s10c : list) {
			Map<String, Object> m = new HashMap<String, Object>();
			m.put("seq", l140s10c.getSeq());
			m.put("bizContent", l140s10c.getContent());
			m.put("oid", l140s10c.getOid());
			m.put("bizItem", l140s10c.getBizItem());
			m.put("bizCat", l140s10c.getBizCat());
			rtnList.add(m);
		}
		
		return new CapMapGridResult(rtnList, rtnList.size());
	}
	
	//J-111-0226 配合青創貸款線上申請作業，額度明細表增加引進青創線上申請之欄位
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public CapMapGridResult queryYoungData(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String custId = Util.trim(params.getString("custId", ""));
		List<Map<String, Object>> rtnList = new ArrayList<Map<String, Object>>();
		
		if(Util.isNotEmpty(custId)) {
			List<Map<String, Object>> youngLoanList = cls1151Service.queryYoungLoanList(user.getUnitNo(),custId);
			if(youngLoanList != null){
				rtnList.addAll(youngLoanList);
			}
		}

		Page<Map<String, Object>> pages = LMSUtil.setPageMap(rtnList, pageSetting);
		return new CapMapGridResult(pages.getContent(), rtnList.size());
	}
	
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public CapMapGridResult queryNotChangedL140m01aCase(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		
		List<Map<String, Object>> rtnList = this.cls1151Service.getByMortgageLoanAndNotChangedL140m01aCase(mainId, custId, dupNo, UtilConstants.Cntrdoc.ItemType.額度明細表);

		return new CapMapGridResult(rtnList, rtnList.size());
	}
}