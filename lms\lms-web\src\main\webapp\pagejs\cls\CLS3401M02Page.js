var initDfd = $.Deferred();
var _handler = "cls3401m02formhandler";

$(document).ready(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	var initControl_lockDoc = false;
	if(true){
		$.form.init({
			formHandler:_handler, 
			formAction:'queryC340M01A', 
			loadSuccess:function(json){			
				
				// 控制頁面 Read/Write
				if(!$("#buttonPanel").find("#btnSave").is("button") || json.lock) {
					tabForm.lockDoc();
					initControl_lockDoc = true;
				}
				
				tabForm.injectData(json);
				initDfd.resolve(json);	
				
				ilog.debug("[queryC340M01A]mainId='"+json.mainId +"', custId="+json.custId);

				$('#repaymentForm').find("#tb4").empty();
                if (json.bankCode_1 == undefined
                    || json.repaymentCount == undefined
                    || json.repaymentCount == ""
                    || json.repaymentCount == 0) {

                    //$('#repaymentForm').hide();
                    //$('#repaymentForm').find("#tb4").append('<tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td></tr>');
                } else {
                    var repaymentCount = parseInt(json.repaymentCount);
                    //$('#repaymentForm').show();
                    //動態append html
                    for(i = 1; i <= repaymentCount; i++ ) {
                        //debugger;
                         $('#repaymentForm').find("#tb4").append('<tr><td align="center"><input type="text" id="bankName_' + i + '" name="bankName_' + i + '" style="text-align: center;" class="text-h-center text-v-center" disabled="true"/> ' +
                            '</td><td align="center"><input type="text" id="repaymentProductType_' + i + '" name="repaymentProductType_' + i + '" style="text-align: center;" class="text-h-center text-v-center" disabled="true"/> ' +
                            '</td><td align="center"><input type="text" id="originalAmt_' + i + '" name="originalAmt_' + i + '" style="text-align: center;" class="numeric text-h-center text-v-center"  disabled="true"/> ' +
                            '</td></tr> ')
                    }
                    tabForm.injectData(json);
                    $('#repaymentForm').find("#tb4").find(".numeric").each(function() {
                        $(this).val(util.addComma($(this).val()));
                    });
                }
		}});
		//根據權限隱藏特定物件
        $.ajax({
            action: "check_only_expermission",
            handler: _handler,
            success: function(responseData){
            	if(responseData.only_ex_permission){//僅有電銷權限, 無其他EL相關權限 true=是, false=否
            		$(".only-ex-permission").hide();
            	}
            }
        });
	}
	//================================
	
	btnPanel.find("#btnSave").click(function(){		
		saveAction({'allowIncomplete':'Y'}).done(function(json){
			if(json.saveOkFlag){
				if(json.IncompleteMsg){
					API.showMessage(i18n.def.saveSuccess+"<br/>-------------------<br/>"+json.IncompleteMsg);
				}else{
					API.showMessage(i18n.def.saveSuccess);	
				}	
			}
        });
	}).end().find("#btnPrint").click(function(){
		if(initControl_lockDoc) {
			printC340M01A();
		}else{
			saveAction({'allowIncomplete':'Y'}).done(function(json){
				if(json.saveOkFlag){
					check_C340().done(function(json){
						printC340M01A();
					});
				}
	        });
		}
	}).end().find("#btnSend").click(function(){	
		saveAction().done(function(json_saveAction){
    		if(json_saveAction.saveOkFlag){
    			check_C340().done(function(){ 
        			API.confirmMessage(i18n.def.confirmApply + "<br><br>" + i18n.cls3401m02["appvoredAlter"], function(result){
        	            if (result) {
        	            	flowAction({});
        	        	}
        	    	});
        		});	
    		}
    	});
			
	}).end().find("#btnAccept").click(function(){
		
		var _id = "_div_btnAccept";
		var _form = _id+"_form";
		if ($("#"+_id).size() == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");

			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.def['accept']+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.def['return']+"</label></p>");

			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380,
            height: 180,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    if(val=="1"){
                    	check_C340().done(function(){
                    		API.confirmMessage(i18n.cls3401m02["appvoredAlter"], function(result){
                                if (result) {
                                    flowAction({'decisionExpr':'ok'});
                                }
                            });
                    	});	    	
                    }else if(val=="2"){
                    	flowAction({'decisionExpr':'back'});
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
		
	}).end().find("#btnCancelFlow").click(function(){
        API.confirmMessage("是否執行對保作廢程序？", function(result){
            if (result) {
                $.ajax({
                    type: "POST",
                    handler: _handler,
                    data:{
                        formAction: "inValidC340M01A",
                        mainOid: responseJSON.mainOid
                    },
                    success: function(json){
                        tabForm.injectData(json);
                        API.showMessage("對保作廢程序執行成功");
                    }
                });
            }
        });

	});
	
	var flowAction = function(opts){
		return $.ajax({
            type: "POST",
            handler: _handler, action: "flowAction",
            data:$.extend( {
            	mainOid: $("#mainOid").val(), 
            	mainDocStatus: $("#mainDocStatus").val() 
                }
                , ( opts||{} )
            ),                
            success: function(json){
            	API.triggerOpener();//gridview.reloadGrid 
            	window.close();            	
            }
        });
	}
});

function saveAction(opts){
	var tabForm = $("#tabForm");
	if(tabForm.valid()){
		var optsPage = {};
		
		return $.ajax({
            type: "POST",
            handler: _handler,
            data:$.extend( {
            	formAction: "saveMain",
                page: responseJSON.page,
                mainOid: responseJSON.mainOid
                }, 
                tabForm.serializeData(),
                optsPage,
                ( opts||{} )
            ),                
            success: function(json){
            	tabForm.injectData(json);
            	
            	//更新 opener 的 Grid
                CommonAPI.triggerOpener("gridview", "reloadGrid");
            }
        });
	}else{
		return $.Deferred();
	}
}

$.extend(window.tempSave,{
	handler: _handler, // handler 名稱
	action: "tempSave", // action Method
	beforeCheck:function(){ // return false or true		
		return $("#tabForm").valid();
	},sendData:function(){ // 需上送之資料集合(Map<String,String>)
		return $("#tabForm").serializeData();
	}
});

var check_C340 = function (){
	var my_dfd = $.Deferred();
	$.ajax({
        type: "POST",
        handler: _handler,
        data:{
        	formAction: "check_C340",
            mainOid: responseJSON.mainOid
        },
        success: function(json){
        	if(json.msg){
        		CommonAPI.confirmMessage(json.msg+"<br/>"+i18n.def.confirmRun, function(b){
    	            if (b) {
    	            	my_dfd.resolve(json);
    	            }
    	        });
//                API.showMessage(json.msg);
        	}else{
        		my_dfd.resolve(json);
        	}
        }
    });
//my_dfd.resolve();
	return my_dfd.promise();
}

function showJSON(){
	$.ajax({
        type: "POST", handler: _handler, data:{ formAction: "showJSON", mainId: (responseJSON.mainId || $("#mainId").val() )}  
        , success: function(json){}
    });
}

	
function printC340M01A(){
	$.form.submit({
        url: "../../simple/FileProcessingService",
        target: "_blank",
        data: {
            mainId: $("#mainId").val(),//responseJSON.mainId,
            mainOid: responseJSON.oid,
            fileDownloadName: "cls3401r02.pdf",
            serviceName: "cls3401r02rptservice"
        }
    });
}

function queryCalculateRate(){
    var my_dfd = $.Deferred();
    if($("#mainDocStatus").val()=="01O"){
        $.ajax({
           type: "POST",
           handler: _handler,
           data:{
               formAction: "queryCalculateRate",
               mainId: $("#mainId").val(),
               loanAmt: $("#loanAmt").val(),
               loanPeriod: $("#loanPeriod").val(),
               preliminaryFee: $("#preliminaryFee").val(),
               creditCheckFee: $("#creditCheckFee").val()
           },
           success: function(json){
               if(json.msg){
                   $("#annualPercentageRate").val(json.rate)
               }
           }
       });
    }
    return my_dfd.promise();
}