pageJsInit(function() {
  $(function() {
initDfd.done(function(auth){
    $("#coKind").trigger("change");
    //$("[name=unitMega]").trigger("click");
    var docCode = $("#docCode").val();
    //當分行代號為025時，要出現#branchShow025_1 #branchShow025_2的li
    var brNo = userInfo ? userInfo.unitNo : "";
    
    //當為陳復/陳述案不顯示額度明細表的頁籤
    if (responseJSON.docCode == "3") {
        $("#showforDocCode3").show();
    } else {
    	//J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
    	//J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
    	//J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式
    	if(lmsM01Json.hidePanelbyCaseType() || lmsM01Json.hidePanelbyCaseType_lnType61() || lmsM01Json.hidePanelbyCaseTypeF() ){
    		$("#lms140s02").show();   //一、額度明細表
    		$("#lms140s03").hide();   //二、利率合理性分析表
    		$("#lms140s04").hide();   //申貸戶同時為其他授信戶應收帳款債務人之額度資料
    		$("#lms140s05").hide();   //四、個人戶評等暨土建融非房貸評等資料
    		$("#lms140s06").hide();   //五、合併關係企業額度彙總
    		$("#lms140s07").hide();   //六、主要申請敘作內容
    	}else if(  lmsM01Json.hidePanelbyCaseType_003() ||  lmsM01Json.hidePanelbyCaseType_004()){
    		$("#lms140s02").show();
    		$("#lms140s03").show(); //利率合理性分析表
    		$("#lms140s04").hide();
    		$("#lms140s05").hide();
    		$("#lms140s06").show();
    		$("#lms140s07").hide();	
        }else{
        	$("#lms140s02").show();
    		$("#lms140s03").show();
    		//J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
    		//J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
    		//只有企金要顯示
            if(responseJSON.docType == "1" ){
            	$("#lms140s04").show();
            	$("#lms140s05").show();
            	$("#lms140s06").show();
            	
            	//J-109-0370_05097_B1002 配合企金一般簽報書格式修訂，新增、調整簽報作業相關內容
            	//授權外一般才要顯示 六、主要申請敘作內容
            	if(responseJSON.docKind == "2" && (responseJSON.docCode == "1" || responseJSON.docCode == "2")){
            		$("#lms140s07").show();
            	}else{
            		$("#lms140s07").hide();
            	}
            	$("#lms140s08").show();   //七、AIRB

                // J-111-0397 八、RWA 試算
                var isShowLms140s09 = lmsM01Json.showPanelLms140s09();
                /*
                if(lmsM01Json.showPanelLms140s09()){
                    $("#lms140s09").show();
                } else {
                    $("#lms140s09").hide();
                }
                */
                
                // BIS
                var isShowLms140s11 = lmsM01Json.showPanelLms140s11();
            }else{
            	$("#lms140s04").hide();  //hide
            	$("#lms140s05").hide();
            	$("#lms140s06").hide();
            	$("#lms140s07").hide();
            }
        }
    	
    	//J-110-0485_05097_B1001 於簽報書新增LGD欄位
    	//$("#lms140s08").show();
    	//$("#lms140s08").hide();
    	//無法取得lmsM01Json.showPanelLms140s08()回傳值，故改再lmsM01Json.showPanelLms140s08內隱藏
    	var isShowLms140s08 = lmsM01Json.showPanelLms140s08();
    	var isShowLms140s10 = lmsM01Json.showPanelLms140s10();
        
    }
    
    
    
    
    if (brNo == "025" || brNo == "900") {
        $("#branchShow025_1,#branchShow025_2").show();
    }
    $("input[name=unitCase]:checked").trigger("click");
	
	/* UPGRADETODO
	$(".toolTip").qtip({
		content: {text: $(this).attr("title")},
		style: {tip: {corner: 'topLeft'}}
	})
	$(".toolTip").mouseleave(function(){
		var tipid = $(this).data("qtip").id;
		$("#ui-tooltip-" + tipid).hide();
	})
	*/
});

  })
});
function changeSyndicationLoan(obj){
    var objValue1 = $('input[name=unitCase]:checked').val();
    var objValue2 = $('input[name=unitMega]:checked').val();
    
    if (objValue1 == "Y" ) {   //|| objValue2 == "Y"
        if (objValue1 == "Y") {
            $('#SyndicationLoan1').show();
            $('#unitCase2Div').show();
			$('#SyndicationLoanAddDate').show();
        } else {
            $('#SyndicationLoan1').hide().find(":radio").removeAttr("checked");//移除隱藏選項的radio checked
            $('#unitCase2Div').hide().find(":radio").removeAttr("checked");//移除隱藏選項的radio checked
        }
        $('#SyndicationLoan2').show();
		$('#SyndicationLoanAddDate').show();
    } else {
        if (objValue1 == "N" ) {  //&& objValue2 == "N"
            //$('#SyndicationLoan1').hide();
            //$('#SyndicationLoan2').hide();
            $('#SyndicationLoan1').hide().find(":radio").removeAttr("checked");//移除隱藏選項的radio checked 
            $('#SyndicationLoan2').hide().find(":radio").removeAttr("checked");//移除隱藏選項的radio checked
            $('#SyndicationLoanAddDate').hide().find(":radio").removeAttr("checked");//移除隱藏選項的radio checked
        }
    }
    
    //
    var objValue = $(obj).val();
    var objName = $(obj).attr('name');
    var uCntBranch =$('input[name=uCntBranch]:checked').val();
	var unitCase =$('input[name=unitCase]:checked').val();
	
    //if (objName == "uCntBranch" || objName == "unitCase") {
		
        if (uCntBranch == "Y" && unitCase == "Y") {
            $('#SyndicationLoan2_mgr').show();
        } else {
            $('#SyndicationLoan2_mgr').hide();
        }
    //}
}

function changeItem(obj){
    var objValue = $(obj).val();
    var objName = $(obj).attr('name');
    if (objName == "mCntrt") {
        if (objValue == "Y") {
            $('#coKind_parent').hide().find("[name=sCntrt]:radio").removeAttr("checked");//移除隱藏選項的radio checked ;
            $('#coKind_son').hide().find(":text").val("");//清除隱藏選項的內容;
        } else {
            $('#coKind_parent').show();
        }
    }
    
    if (objName == "sCntrt") {
        if (objValue == "Y") {
            $('#coKind_son').show();
        } else {
            $('#coKind_son').hide().find(":text").val("");//清除隱藏選項的內容;
        }
    }
    if (objName == "coKind" && objValue == "0") {
        $('#coKindSpan').hide().find(":radio").removeAttr("checked");//移除隱藏選項的radio checked ;;
    } else {
        if (objName == "coKind") {
            $('#coKindSpan').show();
            $('#coKind_1,#coKind_2,#coKind_3,#coKind_4').html($("[name=coKind]").find(":selected").text());
        }
    }
}
