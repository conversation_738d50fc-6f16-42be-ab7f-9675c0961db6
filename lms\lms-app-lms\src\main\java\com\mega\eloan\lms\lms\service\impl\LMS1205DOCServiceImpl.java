/* 
 * LMS1200DOCServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSCombinePrintService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.report.LMS1205R01RptService;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01I;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 產Word Service
 * </pre>
 * 
 * @since 2011/12/8
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/12/9,Miller Lin
 *          </ul>
 */
@Service("lms1205docservice")
public class LMS1205DOCServiceImpl extends AbstractFormHandler implements
		FileDownloadService {
	// 復原TFS J-111-0636_05097_B100X
	@Resource
	LMS1205Service service1205;

	@Resource
	LMS1405Service service1405;

	@Resource
	BranchService branch;

	@Resource
	UserInfoService userSrv;

	@Resource
	CodeTypeService codeService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	LMSService lmsService;

	@Resource
	DocFileService docFileService;

	@Resource
	SysParameterService sysParamService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	LMS1205R01RptService lms1205r01RptService;

	@Resource
	LMSCombinePrintService lmsCombinePrintService;

	private final static String 換行符號 = "<w:br/>";
	private final static String 換頁符號 = "<w:r><w:br w:type=\"page\"/></w:r>";

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1205DOCServiceImpl.class);

	DecimalFormat dfMoney = new DecimalFormat("#,###,###,###,##0");
	DecimalFormat dfRate = new DecimalFormat("#,###,###,###,##0.00");
	private final String DATEYYYYMMDD = "yyyy-MM-dd";
	// 開會時間
	private final String caseTime = "13時30分";
	// 開會地點
	private final String casePlace = "本行801會議室";
	// Word XML 換行語法
	private final String strEnter = "</w:t></w:r></w:p>"
			+ "<w:p wsp:rsidR='00D3657A' wsp:rsidRDefault='00D3657A' "
			+ "wsp:rsidP='007E18F7'><w:pPr><w:jc "
			+ "w:val='both'/><w:rPr><w:rFonts w:ascii='標楷體' "
			+ "w:fareast='標楷體' w:h-ansi='標楷體'/>"
			+ "<wx:font wx:val='標楷體'/></w:rPr></w:pPr>"
			+ "<w:r><w:rPr><w:rFonts w:ascii='標楷體' "
			+ "w:fareast='標楷體' w:h-ansi='標楷體' "
			+ "w:hint='fareast'/><wx:font wx:val='標楷體'/></w:rPr><w:t>";
	// Word XML 表格換行語法
	private final String tblEnter = "<w:p wsp:rsidR='00810A66' wsp:rsidRDefault='00810A66'>"
			+ "<w:pPr><w:rPr><w:rFonts w:fareast='標楷體'/>"
			+ "<w:b/><w:b-cs/><w:spacing w:val='10'/>"
			+ "<w:sz w:val='26'/></w:rPr></w:pPr></w:p>";
	// Word XML 換頁語法
	// private final String tblPage =
	// "<w:p wsp:rsidR='00F229D6' wsp:rsidRDefault='00F50453'>" +
	// "<w:r><w:rPr><w:rFonts w:ascii='標楷體' w:fareast='標楷體' w:h-ansi='標楷體'/>" +
	// "<wx:font wx:val='標楷體'/></w:rPr><w:br w:type='page'/></w:r></w:p>";
	// Word 換頁語法
	private final String wordPage = "<br clear=all style='page-break-before:always'>";
	// // 授審會交易代碼
	// private final String 授審會 = "339062";
	// // 催收會交易代碼
	// private final String 催收會 = "339063";
	// // 常董會交易代碼
	// private final String 常董會 = "339064";

	private final String 國外部 = "007";
	// private final String 金控總部 = "201";
	// private final String 國金部 = "025";
	// 過濾所有以<開頭以>結尾的標籤
	private final static String regxpForHtml = "<([^>]*)>";
	// 找出IMG標籤
	@SuppressWarnings("unused")
	private final static String regxpForImgTag = "<\\s*img\\s+([^>]*)\\s*>";
	// 找出IMG標籤的SRC屬性
	@SuppressWarnings("unused")
	private final static String regxpForImaTagSrcAttrib = "src=\"([^\"]+)\"";
	// J-110-0368_11557_B1001 常董會/董事會提案稿所需之相關文件
	private final String fromCESFileError = "取得常董會附件失敗";

	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		OutputStream outputStream = null;
		ByteArrayOutputStream baos = null;
		try {
			outputStream = this.creatDoc(params);
			if (outputStream != null) {
				baos = (ByteArrayOutputStream) outputStream;
			}
			return baos.toByteArray();

		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}

		}
		return null;
	}

	public OutputStream creatDoc(PageParameters params) {
		OutputStream outputStream = null;
		String docTempType = params.getString("docTempType");

		try {
			outputStream = lms1205r01RptService.generateReport(params);
		} catch (Exception e) {

		}
		return outputStream;
	}

	/**
	 * 將產生的WORD直接儲存到DOCFILE，不要回傳回前端
	 * 
	 * J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveCreatDoc(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();
		Map<String, String> map = new LinkedHashMap<String, String>();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);

		// 1.簽報書***************************************************************************
		if (true) {
			this.processCaseReport(params, l120m01a);
		}

		// 2.簽報書***************************************************************************
		// 常董會->產生常董稿->新版簽報書
		if (true) {
			if (l120m01a != null) {
				L120M01I l120m01i = l120m01a.getL120m01i();
				if (l120m01i != null) {
					// J-112-0508_05097_B1001 Web e-Loan為提升授信簽報效率,
					// 三大部及授審處可由eloan授信系統依據授審會及常董會提案稿所需文件之順序產生相關提案稿pdf
					boolean isOut = lmsCombinePrintService.checkIsOutNewVer(
							l120m01a, l120m01i);

					if (isOut) {
						this.processCaseReport_BED(params, l120m01a);
					}
				}
			}
		}

		// 3.搬檔案***************************************************************************
		if (true) {
			this.processCopyFile(params, l120m01a);
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	public void processCopyFile(PageParameters params, L120M01A l120m01a)
			throws CapException {

		if (l120m01a != null) {

			String mainId = l120m01a.getMainId();

			String LMS_RPA_COMBINE_COPY_FILE = Util.trim(lmsService
					.getSysParamDataValue("LMS_RPA_COMBINE_COPY_FILE"));

			if (Util.notEquals(LMS_RPA_COMBINE_COPY_FILE, "")) {
				JSONObject json = JSONObject.fromObject("{"
						+ LMS_RPA_COMBINE_COPY_FILE + "}");

				// 處理附加檔案複製
				List<DocFile> listFile = docFileService.findByIDAndPid(mainId,
						null);
				if (!listFile.isEmpty()) {
					for (DocFile file : listFile) {
						String fid = file.getOid();

						String fileId = file.getFieldId();

						if (json.containsKey(fileId)) {

							String fileInfo = json.optString(fileId);

							String[] fileInfoArr = fileInfo
									.split(UtilConstants.Mark.SPILT_MARK);
							String fileItemNo = fileInfoArr[0];
							String fileName = fileInfoArr[1];

							DocFile newFile = new DocFile();
							try {
								DataParse.copy(file, newFile);
							} catch (CapException e) {
								logger.error("copyL120m01All exception", e);
							}

							newFile.setMainId(mainId);
							newFile.setFieldId("sendCreatDoc");
							newFile.setBranchId(l120m01a.getCaseBrId());
							newFile.setOid(null);
							docFileService.copy(fid, newFile);

							newFile.setSrcFileName(fileItemNo
									+ "_"
									+ fileName
									+ "."
									+ FilenameUtils.getExtension(file
											.getSrcFileName()));
							newFile.setFlag("S"); // 系統產生，USER不能異動
							docFileService.save(newFile);

							file.setFlag("S"); // 系統產生，USER不能異動
							docFileService.save(newFile); // 註記檔案已經被引進過，如果在重新上傳，FLAG
															// S會不見，就代表有被異動過
						}

					}
				}
			}
		}

	}

	public void processCaseReport(PageParameters params, L120M01A l120m01a)
			throws CapException {
		if (l120m01a != null) {

			String mainId = l120m01a.getMainId();

			List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();

			if (UtilConstants.Casedoc.DocCode.異常通報
					.equals(l120m01a.getDocCode())) {

				List<Map<String, Object>> beanListJ = service1205
						.getBorrowsInner(mainId, "J");
				if (beanListJ != null && !beanListJ.isEmpty()) {
					beanList.addAll(beanListJ);
					// beanList.addAll(beanList.size(), beanListJ);
				}

			} else {
				beanList = service1205.getBorrowsInner(mainId, "A");

				// 額度批覆書
				String printH = "";
				if (UtilConstants.Casedoc.DocKind.授權外.equals(l120m01a
						.getDocKind())
						&& (CreditDocStatusEnum.泰國_提會待登錄.getCode().equals(
								l120m01a.getDocStatus())
								|| CreditDocStatusEnum.泰國_提會待覆核.getCode()
										.equals(l120m01a.getDocStatus())
								|| CreditDocStatusEnum.海外_已核准.getCode().equals(
										l120m01a.getDocStatus()) || CreditDocStatusEnum.海外_婉卻
								.getCode().equals(l120m01a.getDocStatus()))
						&& !UtilConstants.Casedoc.DocCode.陳復陳述案.equals(l120m01a
								.getDocCode())
						&& !UtilConstants.Casedoc.DocCode.異常通報.equals(l120m01a
								.getDocCode())) {
					printH = UtilConstants.DEFAULT.是;
				} else if (UtilConstants.Casedoc.DocKind.授權內.equals(l120m01a
						.getDocKind())
						&& UtilConstants.Casedoc.AuthLvl.營運中心授權內
								.equals(l120m01a.getAuthLvl())) {
					if ((CreditDocStatusEnum.海外_已核准.getCode().equals(
							l120m01a.getDocStatus()) || CreditDocStatusEnum.海外_婉卻
							.getCode().equals(l120m01a.getDocStatus()))
							&& !UtilConstants.Casedoc.DocCode.陳復陳述案
									.equals(l120m01a.getDocCode())
							&& !UtilConstants.Casedoc.DocCode.異常通報
									.equals(l120m01a.getDocCode())) {
						printH = UtilConstants.DEFAULT.是;
					}
				}

				printH = UtilConstants.DEFAULT.否;

				if (Util.equals(printH, UtilConstants.DEFAULT.是)) {
					List<Map<String, Object>> beanListH = new ArrayList<Map<String, Object>>();
					beanListH = service1205.getBorrowsInner(mainId, "H");
					if (beanListH != null && !beanListH.isEmpty()) {
						for (Map<String, Object> beanMapH : beanListH) {

							// 為了要讓額度批覆書與額度檢核表印在最後面
							String rpt = Util.trim(MapUtils.getObject(beanMapH,
									"rpt"));
							if (Util.equals(rpt, "R13")
									|| Util.equals(rpt, "R29")) {
								beanMapH.put("rpt", rpt + "FS");
							}

						}
						beanList.addAll(beanListH);

						// beanList.addAll(beanList.size(), beanListH);
					}

				}
			}

			// if (!params.containsKey("rptOid")) {
			// String rptOid =
			// "R01^2EF2A6BDBAC946FEA9E19CDD4669B973^^^^|R12^8A604F8012AA11EB9B9D046CC0A89955^73251209^0^005110900630^|R29^8A604F8012AA11EB9B9D046CC0A89955^73251209^0^005110900630^|R14^2EF2A6BDBAC946FEA9E19CDD4669B973^^^^|R32^^^^^|R33^^^^^|R36^^^^^|R39^^^^^";
			// params.add("rptOid", rptOid);
			// }

			// "R01":"001","R32_simple":"005","R12":"006","R24":"010","R42":"024"
			String LMS_RPA_COMBINE_CASE_RPT_PRINT = Util.trim(lmsService
					.getSysParamDataValue("LMS_RPA_COMBINE_CASE_RPT_PRINT"));

			if (beanList != null && !beanList.isEmpty()) {

				if (Util.notEquals(LMS_RPA_COMBINE_CASE_RPT_PRINT, "")) {
					JSONObject json = JSONObject.fromObject("{"
							+ LMS_RPA_COMBINE_CASE_RPT_PRINT + "}");

					Iterator<String> keys = json.keys();

					while (keys.hasNext()) {
						String key = keys.next();

						for (Map<String, Object> beanMap : beanList) {

							String rpt = Util.trim(MapUtils.getString(beanMap,
									"rpt"));

							if (Util.equals(key, rpt)) {

								String itemNo = Util.trim(json.optString(rpt));

								Map<String, String> needPrintMap = new HashMap<String, String>();

								// LMS_RPA_COMBINE_CASE_RPT_PRINT

								needPrintMap.put("rptName",
										MapUtils.getString(beanMap, "rptName"));
								needPrintMap.put("rpt",
										MapUtils.getString(beanMap, "rpt"));
								needPrintMap.put("rptNo",
										MapUtils.getString(beanMap, "rptNo"));
								needPrintMap.put("itemNo", itemNo);

								this.caseReportToPdfFile(params, beanList,
										needPrintMap);

								break;
							}

						}

					}

				}
			}
			// if (Util.notEquals(errorMsg, "")) {
			// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
			// .getMainMessage(this.getComponent(),
			// UtilConstants.AJAX_RSP_MSG.執行有誤));
			// return result;
			// }

		}
	}

	public void caseReportToPdfFile(PageParameters params,
			List<Map<String, Object>> beanList, Map<String, String> needPrintMap)
			throws CapException {

		StringBuffer newRptOid = new StringBuffer("");
		if (beanList != null && !beanList.isEmpty()) {
			for (Map<String, Object> beanMap : beanList) {

				if (Util.notEquals(MapUtils.getString(needPrintMap, "rpt"),
						MapUtils.getString(beanMap, "rpt"))) {
					continue;
				}

				String content = Util.trim(MapUtils.getString(beanMap, "rpt",
						""))
						+ "^"
						+ Util.trim(MapUtils.getString(beanMap, "oid", ""))
						+ "^"
						+ Util.trim(MapUtils.getString(beanMap, "custId", ""))
						+ "^"
						+ Util.trim(MapUtils.getString(beanMap, "dupNo", ""))
						+ "^"
						+ Util.trim(MapUtils.getString(beanMap, "cntrNo", ""))
						+ "^"
						+ Util.trim(MapUtils
								.getString(beanMap, "refMainId", "")) + "|";
				newRptOid.append(content);
			}

			String rptOid = Util.trim(newRptOid.toString());

			if (Util.notEquals(rptOid, "")) {
				rptOid = rptOid.substring(0, StringUtils.length(rptOid) - 1);
			}

			params.remove("rptOid");
			params.add("rptOid", rptOid);

			// String fileDesc = Util.getLeftStr(
			// Util.trim(MapUtils.getString(beanMap, "custName", "")),
			// 10)
			// + "_"
			// + Util.trim(MapUtils.getString(beanMap, "cntrNo", ""))
			// + "_"
			// + Util.trim(MapUtils.getString(beanMap, "rptNo", ""));

			String fileDesc = Util.trim(MapUtils.getString(needPrintMap,
					"rptNo", ""));

			String itemNo = Util.getRightStr(
					"000" + MapUtils.getString(needPrintMap, "itemNo"), 3);

			String itemNo_2 = "";
			if (Util.equals(MapUtils.getString(needPrintMap, "itemNo"), "001")) {
				// 簽報書有一般跟常董會兩種格式
				itemNo_2 = "01_";
			}

			String fileDownloadName = itemNo
					+ "_"
					+ itemNo_2
					+ Util.trim(MapUtils.getString(needPrintMap, "rptName",
							Util.trim(MapUtils.getString(needPrintMap, "rpt",
									""))));
			fileDownloadName = fileDownloadName + ".pdf";

			byte[] saveFile = this.getContent(params);
			String errorMsg = this.saveToFile(params, saveFile,
					fileDesc, fileDownloadName);

			// if (Util.notEquals(errorMsg, "")) {
			// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
			// .getMainMessage(this.getComponent(),
			// UtilConstants.AJAX_RSP_MSG.執行有誤));
			// return result;
			// }

			if (Util.notEquals(errorMsg, "")) {
				throw new CapMessageException(errorMsg, getClass());
			}

		}
	}

	// 常董會
	public void processCaseReport_BED(PageParameters params, L120M01A l120m01a)
			throws CapException {

		String rptOid = "R01" + "^" + l120m01a.getOid() + "^" + "^" + "^" + "^"
				+ "LMSDoc4";

		params.remove("rptOid");
		params.add("rptOid", rptOid);

		String fileDesc = "常董會個案轉檔後案件簽報書";

		String fileDownloadName = "001" + "_" + "02_案件簽報書(常董會個案討論)";
		fileDownloadName = fileDownloadName + ".pdf";

		byte[] saveFile = this.getContent(params);
		String errorMsg = this.saveToFile(params, saveFile,
				fileDesc, fileDownloadName);

		// if (Util.notEquals(errorMsg, "")) {
		// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
		// .getMainMessage(this.getComponent(),
		// UtilConstants.AJAX_RSP_MSG.執行有誤));
		// return result;
		// }

		if (Util.notEquals(errorMsg, "")) {
			throw new CapMessageException(errorMsg, getClass());
		}

	}

	// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
	public String saveToFile(PageParameters params, byte[] saveFile,
			String fileDesc, String fileDownloadName) {
		String errorMsg = "";
		String mainId = Util.trim(params.getString("mainId"));
		String fieldId = Util.trim(params.getString("fieldId"));

		L120M01A model = service1205.findL120m01aByMainId(mainId);
		if (model == null) {
			model = new L120M01A();
		}

		String contentType = "application/pdf";// 預設為PDF

		DocFile docFile = new DocFile();
		docFile.setBranchId(model.getCaseBrId());
		docFile.setContentType(contentType);
		docFile.setMainId(mainId);
		docFile.setPid(null);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId(fieldId);
		docFile.setDeletedTime(null);
		docFile.setSrcFileName(fileDownloadName);
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId(docFileService.getSysId());
		// docFile.setFileSize(bytes1.length);
		docFile.setFileDesc(Util.getLeftStr(fileDesc, 60)); // "額度檢視表"
		docFile.setData(saveFile);
		docFile.setFlag("S"); // 系統產生，USER不能異動
		docFileService.save(docFile, true);
		return errorMsg;
	}

}
