/* 
 * ClsConstants.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.constants;


/**
 * <pre>
 * 個金 Constants
 * </pre>
 * 
 * @since 2012/9/27
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/9/27,REX,new
 *          </ul>
 */
public interface ClsConstants {

	final static String SUCCESS = "Success";
	final static String ERROR = "Error";
	final static int 動審表明細起始數 = 30;
	
	interface City {
		String 縣市代碼 = "cityCode";
		String 郵地區號 = "zipCode";
	}

	interface Default {
		String 幣別 = "TWD"; // 台幣
		String 國別 = "TW"; // 台灣
		String 配偶資料 = "A"; // A.不登錄配偶資料 B.列於本欄 C.同本案借款人
		String 是否填列 = "N"; // Y.是 N.否
		String 是否屬於青年創業貸款 = "N" ; // Y.是 N.否
	}

	interface MateFlag {
		String 不登錄配偶資料 = "A";
		String 列於本欄 = "B";
		String 同本案借款人 = "C";
	}

	interface codeType {
		String 縣市別 = "counties";
		String[] 鄉鎮區 = { "counties01", "counties02", "counties03",
				"counties04", "counties03", "counties04", "counties05",
				"counties06", "counties07", "counties08", "counties09",
				"counties11", "counties12", "counties13", "counties14",
				"counties15", "counties16", "counties18", "counties20",
				"counties21", "counties22", "counties23", "counties24",
				"counties25" };

		String 動審表種類 = "cls1161m01_caseType";
		String 授權等級 = "lms1205m01_caseLvl";
	}

	/**
	 * <pre>
	 * C101S01E．個金相關查詢資料檔
	 * </pre>
	 * 
	 * @since 2012/10/29
	 * <AUTHOR>
	 * @version <ul>
	 *          <li>2012/10/29,Fantasy,new
	 *          </ul>
	 */
	interface C101S01E {
		String 資料來源 = "dataSrc";
		String 有無票信退補記錄 = "eChkFlag";
		String 票信資料截止日 = "eChkDDate";
		String 票信查詢日期 = "eChkQDate";
		String 有無聯徵逾催呆記錄 = "eJcicFlag";
		String 聯徵資料日期 = "eJcicDDate";
		String 聯徵查詢日期 = "eJcicQDate";
		String 查詢組合 = "prodId";
		String 引進原舊案資料 = "isFromOld";
		String 婉卻紀錄 = "isQdata1";
		String 本行利害關係人 = "isQdata2";
		String 金控利害關係人44條 = "isQdata3";
		String 金控利害關係人45條 = "isQdata16";
		String 主從債務人_不含本次資料 = "isQdata6";
		String 對同一自然人授信總餘額比率 = "isQdata5";
		String 歸戶_本行餘額為aLoan資料_他行餘額為聯徵資料 = "isQdata4";
		String 近一年內不含查詢當日非Z類被聯行查詢紀錄明細 = "isQdata14";
		String 黑名單 = "isQdata7";
		String 黑名單全型字英文名 = "eName";
		String 黑名單查詢結果 = "ans1";
		String 證券暨期貨違約交割紀錄 = "isQdata8";
		String 退票紀錄 = "isQdata9";
		String 拒絕往來紀錄 = "isQdata10";
		String 主債務逾期_催收_呆帳紀錄 = "isQdata11";
		String 信用卡強停紀錄 = "isQdata13";
		String 身分證補換發紀錄 = "isQdata12";
		String 成年監護制度查詢紀錄 = "isQdata15";
		String 擔任負責人或董監事之企業是否於本行有授信額度達一億元以上 = "isQdata17";
		String 授信信用風險管理_遵循檢核 = "isQL120S01M";
		String 疑似偽造證件或財力證明 = "isQdata18";
		String 異常通報紀錄 = "isQdata29";
		String 財管理財客戶等級 = "isQWM";
		String 大數據風險報告 = "witcherFin";
		String IXML聯徵資料 = "IXML";
		String 聯徵T70證券暨期貨違約交割記錄資訊 = "isQdata30";
		String 聯徵B42從債務查詢_擔保品類別 = "isQdata31";
		String 聯徵B42共同債務查詢_擔保品類別 = "isQdata32";
		String 是否有近一年有二戶以上授信借貸結案紀錄 = "isQdata33";
		String 是否有近三年有二戶以上授信借貸結案紀錄 = "isQdata34";
	}
	
	/**
	 * <pre>
	 * 個金相關查詢結果檔
	 * </pre>
	 * 
	 * @since 2012/11/8
	 * <AUTHOR>
	 * @version <ul>
	 *          <li>2012/11/8,Fantasy,new
	 *          </ul>
	 */
	interface C101S01J {
		String isQdata2 = "Qresult"; // 銀行法查詢結果
		String isQdata3 = "XQResult"; // 金控法44條查詢結果
		String isQdata16 = "X45QResult"; // 金控法45條查詢結果
		String isQdata8 = "defaultRec"; // 違約交割紀錄
		String isQdata7 = "blackRecQry"; // 黑名單
		String isQdata13 = "cstRd"; // 是否有強制停用記錄
		String isQdata12 = "isQdata12"; // 身分證補、換發紀錄
		String isQdata15 = "isQdata15"; // 成年監護制度查詢紀錄
	}

	/**
	 * <pre>
	 * 金控法條文
	 * </pre>
	 * 
	 * @since 2012/11/8
	 * <AUTHOR>
	 * @version <ul>
	 *          <li>2012/11/8,Fantasy,new
	 *          </ul>
	 */
	interface lawno {
		String 金控法44條 = "44";
		String 金控法45條 = "45";
	}

	/**
	 * <pre>
	 * 關係種類
	 * </pre>
	 * 
	 * @since 2012/11/8
	 * <AUTHOR>
	 * @version <ul>
	 *          <li>2012/11/8,Fantasy,new
	 *          </ul>
	 */
	interface xType {
		String 銀行法 = "1";
		String 金控法44條 = "2";
		String 金控法45條 = "3";
	}

	/**
	 * <pre>
	 * 關係層級
	 * </pre>
	 * 
	 * @since 2012/11/8
	 * <AUTHOR>
	 * @version <ul>
	 *          <li>2012/11/8,Fantasy,new
	 *          </ul>
	 */
	interface relvl {
		String 本行利害關係人 = "1";
		String 本行有利害關係人 = "2";
		String 本行有利害關係企業 = "3";
		String 金控利害關係人 = "4";
	}

	/**
	 * <pre>
	 * 模型類別
	 * </pre>
	 * 
	 * @since 2012/11/19
	 * <AUTHOR>
	 * @version <ul>
	 *          <li>2012/11/19,Fantasy,new
	 *          </ul>
	 */
	interface markModel {
		String 免辦 = "0";
		String 房貸個人評等模型 = "1";
	}

	/** 房貸評等因子(S01X vs ) **/
	interface ratingFactor_G_V1_3 {
		// 個金基本資料檔
		interface C101S01A {
			String 學歷 = "edu";
			String 婚姻狀況 = "marry";
			String 子女數 = "child";
		}

		// 個金服務單位檔
		interface C101S01B {
			String 學歷 = "edu";
			String 職業別大類 = "jobType1";
			String 職業別細項 = "jobType2";
			String 職稱 = "jobTitle";
			String 年薪 = "payAmt";
			String 其他收入金額 = "othAmt";
		}

		// 個金償債能力檔
		interface C101S01C {
			String 夫妻年收入 = "yFamAmt";
			String 個人負債比率 = "dRate";
			String 家庭負債比率 = "yRate";
		}
	}
	interface ratingFactor_G_V3_0 { // latest_G
		// 個金服務單位檔
		interface C101S01A {
			String 學歷 = "edu";
		}
		interface C101S01B {
			String 職稱 = "jobTitle"; //值[g,h]在 N018 使用
		}
		// 個金償債能力檔
		interface C101S01C {
			String 家庭負債比率 = "yRate";
		}
	}

	interface ratingFactor_G_V2_0 { // latest_G
		// 個金服務單位檔
		interface C101S01B {
			String 職稱 = "jobTitle"; //值[g,h]在 N018 使用
			String 年薪 = "payAmt";
			String 其他收入金額 = "othAmt";
			String 年資 = "seniority";
		}

		// 個金償債能力檔
		interface C101S01C {
			String 夫妻年收入 = "yFamAmt";
			String 個人負債比率 = "dRate";
			String 家庭負債比率 = "yRate";
		}
	}
	
	/**
	  * <pre>
	 * 非房貸評等因子
	 * </pre>
	 * 
	 * <AUTHOR>
	 *
	 */
	interface ratingFactor_Q_V1_0 {
		// 個金基本資料檔
		interface C101S01A {
			String 非房貸學歷 = "edu";
		}

		// 個金服務單位檔
		interface C101S01B {
			String 個人年收入 = "payAmt";
			String 年資 = "seniority";
		}
	}
	
	interface ratingFactor_Q_V2_0 { // latest_Q
		// 個金服務單位檔
		interface C101S01B {
			String 個人年收入 = "payAmt";
			String 其他收入 = "othAmt";
			String 年資 = "seniority";
		}
	}
	
	interface ratingFactor_Q_V2_1 { // latest_Q
		// 個金服務單位檔
		interface C101S01B {
			String 個人年收入 = "payAmt";
			String 其他收入 = "othAmt";
			String 年資 = "seniority";
		}
	}
	
	interface ratingFactor_R_V2_1 { // 卡友貸
		// 個金服務單位檔
		interface C101S01B {
			String 個人年收入 = "payAmt";
			String 其他收入 = "othAmt";
			String 年資 = "seniority";
		}
	}

	interface FORM {
		String 動審表_授權檔 = "C160A01AForm";
		String 動審表_主檔 = "C160M01AForm";
		String 動審表_額度明細表 = "C160M01BForm";
		String 動審表_檢附資訊檔 = "C160M01CForm";
		String 動審表_先行動用呈核及控制表檔 = "C160M01DForm";
		String 動審表_擔保品資料 = "C160S01AForm";
		String 動審表_主從債務人資料 = "C160S01BForm";
		String 動審表_個金產品種類 = "C160S01CForm";
		String 動審表_代償轉貸借新還舊主檔 = "C160S01EForm";
		String 動審表_代償轉貸借新還舊明細 = "C160S01FForm";
	}

	interface Flow {
		String 動審表 = "CLS1161Flow";
	}
	
	interface RptNo {
		String 簽報書_案件別_不為一般_內 = "R09";
		String 簽報書_案件別_不為一般_外 = "R08";
		String 簽報書_案件別_一般_內 = "R11";
		String 簽報書_案件別_一般_外 = "R10";
		String 簽報書_補充說明 = "R43";
		String 簽報書_案件別_一般_內_簡化A = "R11A";
		String 簽報書_案件別_一般_外_簡化A = "R10A";
		String 簽報書_案件別_一般_內_簡化B = "R11B";
		String 簽報書_案件別_一般_外_簡化B = "R10B";
		String 簽報書_案件別_一般_內_簡化C = "R11C";
		String 簽報書_案件別_一般_外_簡化C = "R10C";
		String 簽報書_案件別_一般_內_簡化D = "R11D";
		String 簽報書_案件別_一般_外_簡化D = "R10D";
		String 額度明細表 = "R12";
		String 額度批覆書 = "R13";
		String 產品資訊_附表 = "R12_F";
		String 授信條件對照表 = "R15"; // ref L120S06A
		String 額度明細檢核附表 = "R29";
		String 授信信用風險管理_遵循檢核表 = "R30";
		String 洗錢防制檢核表 = "R33";
		String 借款申請書_申貸人 = "R101";
		String 借款申請書_從債務人 = "R102";
		String 借款申請書_銀行法33_3 = "R100";
		String 借款申請書_房貸擔保品用途聲明切結事項 = "R104";
		String 申請資料核對表 = "R28";
		String 同一經濟關係人額度彙總表 = "R40";
		String 高齡客戶關懷檢核表 = "R42";
		String 敘做條件異動比較表 = "R93";
		String 借款人基本資料 = "R99";
		String 簡易列印_借款人連保人基本資料 = "R99_S";  // J-109-0460 增加「簡化版消金授信案件簽報書」
		String 額度違約損失率明細 = "R44";  //J-111-0343_05097_B1004 Web e-Loan修改消金額度明細表合計之功能
		String 消金業務授權層級試算表 = "R45";
	}

	interface L140Param {
		String sendSMS = "sendSMS";
		String gutPercent = "gutPercent";
		String L140M01A_introduceSrc = "L140M01A_introduceSrc";
		String l140m01a_megaEmpNo = "l140m01a_megaEmpNo";
		String l140m01a_megaCode = "l140m01a_megaCode";
		String l140m01a_subUnitNo = "l140m01a_subUnitNo";
		String l140m01a_subEmpNo = "l140m01a_subEmpNo";
		String l140m01a_subEmpNm = "l140m01a_subEmpNm";
		String l140m01a_introCustId = "l140m01a_introCustId";
		String l140m01a_introDupNo = "l140m01a_introDupNo";
		String l140m01a_megaEmpBrNo = "l140m01a_megaEmpBrNo";
		String l140m01a_introCustName = "l140m01a_introCustName";
		String l140m01a_introducerName = "l140m01a_introducerName";
		String L140S02D_pmRate = "L140S02D_pmRate";
		
		String L140S02D_phase1_isUseBox = 	"L140S02D_phase1_isUseBox";
		String L140S02D_phase1_bgnNum = 	"L140S02D_phase1_bgnNum";
		String L140S02D_phase1_endNum = 	"L140S02D_phase1_endNum";
		String L140S02D_phase1_rateType = 	"L140S02D_phase1_rateType"; //{6R, 7D, M2...}
		String L140S02D_phase1_baseRate = 	"L140S02D_phase1_baseRate";
		String L140S02D_phase1_rateFlag = 	"L140S02D_phase1_rateFlag"; //{1:固定, 2:機動, 3:定期浮動}
		String L140S02D_phase1_pmRate = 	"L140S02D_phase1_pmRate";
		String L140S02D_phase1_nowRate = 	"L140S02D_phase1_nowRate";
		//~~~~~~~~~~~~~~~~
		String L140S02D_phase2_isUseBox = 	"L140S02D_phase2_isUseBox";
		String L140S02D_phase2_bgnNum = 	"L140S02D_phase2_bgnNum";
		String L140S02D_phase2_endNum = 	"L140S02D_phase2_endNum";
		String L140S02D_phase2_rateType = 	"L140S02D_phase2_rateType";
		String L140S02D_phase2_baseRate = 	"L140S02D_phase2_baseRate";
		String L140S02D_phase2_rateFlag = 	"L140S02D_phase2_rateFlag";
		String L140S02D_phase2_pmRate = 	"L140S02D_phase2_pmRate";
		String L140S02D_phase2_nowRate = 	"L140S02D_phase2_nowRate";
		//~~~~~~~~~~~~~~~~
		String L140S02D_phase3_isUseBox = 	"L140S02D_phase3_isUseBox";
		String L140S02D_phase3_bgnNum = 	"L140S02D_phase3_bgnNum";
		String L140S02D_phase3_endNum = 	"L140S02D_phase3_endNum";
		String L140S02D_phase3_rateType = 	"L140S02D_phase3_rateType";
		String L140S02D_phase3_baseRate = 	"L140S02D_phase3_baseRate";
		String L140S02D_phase3_rateFlag = 	"L140S02D_phase3_rateFlag";
		String L140S02D_phase3_pmRate = 	"L140S02D_phase3_pmRate";
		String L140S02D_phase3_nowRate = 	"L140S02D_phase3_nowRate";
		
		String L140S02F_pConBegEnd_fg = "L140S02F_pConBegEnd_fg";
		
		String L140S02F_pConBeg1 = "L140S02F_pConBeg1";
		String L140S02F_pConEnd1 = "L140S02F_pConEnd1";
		String L140S02F_pCalCon1 = "L140S02F_pCalCon1";
		
		String L140S02F_pConBeg2 = "L140S02F_pConBeg2";
		String L140S02F_pConEnd2 = "L140S02F_pConEnd2";
		String L140S02F_pCalCon2 = "L140S02F_pCalCon2";
		
		String L140S02A_lnPurs = "L140S02A_lnPurs";
		String L140S02A_lnPurpose = "L140S02A_lnPurpose";
		String L140S02A_termGroup = "L140S02A_termGroup";
		String L140S02A_termGroupSub = "L140S02A_termGroupSub";
		String L140S02A_esggnLoanFg = "L140S02A_esggnLoanFg";
		String L140S02A_esggtype = "L140S02A_esggtype";
		String L140S02N_esggtypeZMemo = "L140S02N_esggtypeZMemo";
		String L140M01R_feeNo_01_feeAmt = "L140M01R_feeNo_01_feeAmt"; 
		
		String L140M01Y_onlineCaseNo = "L140M01Y_onlineCaseNo";
		
		String L140S01A_custPos = "L140S01A_custPos";
		String L140S01A_custId = "L140S01A_custId";
		String L140S01A_dupNo = "L140S01A_dupNo";
		String L140S01A_rKindM = "L140S01A_rKindM";
		String L140S01A_rKindD = "L140S01A_rKindD";
		String L140S01A_isLiveWithBorrower = "L140S01A_isLiveWithBorrower";
	}

	interface L120S15A_rptId {
		String V2020 = "V2020";
		String V2021001 = "V202101";
		String V202201 = "V202201";
		String V202301 = "V202301";
	}

	interface C122M01A_IsClosed{
		String X = "X"; 
	}
	
	interface C122M01A_StatFlag{
		String 受理中 = "0";
		String 審核中 = "1";
		String 已核貸 = "2";
		String 待對保 = "3";
		String 已對保 = "4";
		String 待補件 = "5";
		String 動審表已覆核 = "6";
		String 已作廢 = "X";
		String 客戶撤件 = "D";
		String 票債信不良 = "A";
		String 信用評等未達標準 = "E";
	}
	
	interface C101S01S_dataType{
		String 往來客戶信用異常資料 = "1";
		String 客戶是否為利害關係人資料 = "2"; 
		String 婉卻紀錄資料 = "3";
		String 證券暨期貨違約交割紀錄 = "4";
		String 行內_身分證驗證 = "5";
		String RPA受監護輔助宣告查詢 = "6";
	}

	interface L120M01I_creditLoanPurpose {
		String 房屋修繕 = "B";
		String 購車 = "D";
		String 購買耐久性消費財_不含汽車 = "H";
		String 個人投資_理財 = "G";
		String 週轉 = "C";
		String 其他消費性支出 = "3";
		String 子女教育 = "J";
		String 繳稅 = "K";
		String 個人週轉金 = "N";
		String 個人消費性用途 = "O";
		String 企業員工認購股票 = "P";
	}
	
	interface BRMP_domainvalue{
		String 是 = "1";
		String 否 = "0";
		String 不適用 = "S";
	}

	interface L120S19A_ItemTypeCode {
		String BRMP_creditCheck_input = "A";
		String BRMP_creditCheck_output = "B";
		String BRMP_homeloanrule_input = "C";
		String BRMP_homeloanrule_output = "D";
		String BRMP_termGroupRule_input = "E";
		String BRMP_termGroupRule_output = "F";
		String BRMP_autoCheck_input = "G";
		String BRMP_autoCheck_output = "H";
	}
	
	interface BRMP_policyStatus{
		String PASS = "0";//通過
		String FAIL = "1";//未通過
		String MISS = "M";//值域缺失
	}
	
	interface BRMP_policyCode{
		String CH001_jcicNoRecordFlag = "CH001";
		String CH002_jcicNoCardRecordFlag = "CH002";
		String CH003_jcicQueryCountAbnFlag = "CH003";
		String CH004_mutipleNonpersonalMortgageFlag = "CH004";
		
		String PA003 = "PA003"; //符合個人負債比規範
		String PA007 = "PA007"; //符合職業年資條件
		
		String PH001_itemCode_O = "PH001";
		String PH002_itemCode_S = "PH002";
		String PH003_itemCode_A = "PH003";
		String PH004_itemCode_K = "PH004";
		String PH005_itemCode_D = "PH005";
		String PH006_itemCode_H = "PH006";
		String PH007_itemCode_Q = "PH007";
		String PH008_itemCode_M = "PH008";
		String PH009_itemCode_G = "PH009";
		String PH010_itemCode_R = "PH010";
		String PH011_itemCode_P = "PH011";
		String PH012_itemCode_T = "PH012";
		String PH013_itemCode_U = "PH013";
		String PH014_itemCode_C = "PH014";
		String PH015_itemCode_F = "PH015";
		String PH016_itemCode_L = "PH016";
		String PH017_itemCode_B = "PH017";
		String PH018_itemCode_I = "PH018";
		String PH019_itemCode_J = "PH019";
		String PH020_itemCode_N = "PH020";
		String PH021_itemCode_3 = "PH021";
		String PH022_itemCode_X = "PH022";
		String PH023_itemCode_Y = "PH023";
		String PH024_itemCode_2 = "PH024";
		String PH025_itemCode_Z = "PH025";
		String PH026_itemCode_V = "PH026";
		String PH027_itemCode_W = "PH027";
		String PH028_itemCode_1 = "PH028";
		String PH029_itemCode_E = "PH029";
		
		String PO002 = "PO002"; //身分證資料相符
		String PO003 = "PO003"; //無受監護/輔助宣告
		String PO004 = "PO004"; //無婉卻紀錄
		String PO005 = "PO005"; //無往來信用異常紀錄
		String PO009 = "PO009"; //若為以本案借款購買本行理財商品者，已徵提聲明書及照會確認無勸誘事宜
	}
	
	interface UseDeadLineLimitation {
		String[] codeArray = new String[]{"0", "1", "8"};
	}
	
	interface IXMLQueryData {
		// TEST可用ID	
		// T50 所得	V279964212 + 107
		// T51 財產	B200971062	
		// T52 勞保	A127778888	注意:3個7，4個8
		String[] qItems = {"T50", "T51", "T52"};	// 聯徵查詢項目
		String per = "000";	// 查詢理由
		String staffNo = "00ZCB3"; // 消金虛擬行員00ZCB3 - IXML查詢
	}
}
