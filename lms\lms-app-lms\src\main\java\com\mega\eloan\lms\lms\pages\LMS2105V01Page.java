/* 
 * LMS2105V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;
import com.sun.xml.bind.v2.schemagen.Util;

/**
 * <pre>
 * 修改資料特殊流程外部 - 編製中
 * </pre>
 * 
 * @since 2012/01/10
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/01/10,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms2105v01")
public class LMS2105V01Page extends AbstractEloanInnerView {

	@Autowired
	UserInfoService userSrv;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<EloanPageFragment> btns = new ArrayList<EloanPageFragment>();
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);
		// 加上Button
		btns.addAll(Arrays.asList(LmsButtonEnum.Modify, LmsButtonEnum.Delete));

		// J-110-0304_05097_B1003 Web e-Loan授信覆審配合RPA作業修改
		if (userSrv.isRPAUser(user.getUserId())
				|| Util.equal(user.getSsoUnitNo(), "900")
				|| Util.equal(user.getSsoUnitNo(), "940")) {
			btns.add(LmsButtonEnum.UpTransferId);
		}

		if (userSrv.isRPAUser(user.getUserId())
				|| Util.equal(user.getSsoUnitNo(), "900")
				|| Util.equal(user.getSsoUnitNo(), "912")) {
			btns.add(LmsButtonEnum.UpBisParam);
		}
		
		//J-112-0366_12473_B1001 Web e-Loan企金授信批次信用保證統計表總表
		if (userSrv.isRPAUser(user.getUserId())
				|| Util.equal(user.getSsoUnitNo(), "900")
				|| Util.equal(user.getSsoUnitNo(), "918")
				|| Util.equal(user.getSsoUnitNo(), "940")) {
			btns.add(LmsButtonEnum.UpBatGutFile);
		}

		addToButtonPanel(model, btns);

		renderJsI18N(LMS2105M01Page.class);
	}
}
