package com.mega.eloan.lms.base.service;


import org.kordamp.json.JSONObject;

import com.mega.eloan.lms.model.C121S01A;


/**
 * <pre>
 * 評分 Service
 * </pre>
 * 
 * @since 2012/10/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/30,Fantasy,new
 *          </ul>
 */
public interface ScoreServiceJP {
	JSONObject scoreJP(String type, JSONObject data, String varVer, String mowType2);
	void setJPDR(JSONObject jpDR, JSONObject target);
	public String get_Version_JP();
	public boolean getModelType_2_0(String loanTP, C121S01A c121s01a);

}
