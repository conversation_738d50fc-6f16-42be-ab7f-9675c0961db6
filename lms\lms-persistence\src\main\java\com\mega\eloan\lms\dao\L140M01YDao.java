
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01Y;

/** 額度特殊註記檔 **/
public interface L140M01YDao extends IGenericDao<L140M01Y> {

	L140M01Y findByOid(String oid);
	
	L140M01Y findByUk(String mainId, String refType, String refValue, String refModel, String refMainId, String refOid);
	
	List<L140M01Y> findByMainIdOrderDefault(String mainId);
	
	List<L140M01Y> findByMainIdRefTypeOrderDefault(String mainId, String refType);
	
	List<L140M01Y> findByRef(String refType, String refMainId, String refOid);
	
	L140M01Y findByRefmainId(String refmainid);

	L140M01Y findByMainId_RefType_RefModel(String mainId, String refType,
			String refModel);
}