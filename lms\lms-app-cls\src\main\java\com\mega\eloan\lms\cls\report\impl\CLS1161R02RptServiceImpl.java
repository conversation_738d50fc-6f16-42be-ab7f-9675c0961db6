package com.mega.eloan.lms.cls.report.impl;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.dao.C160M01ADao;
import com.mega.eloan.lms.dao.C160M01BDao;
import com.mega.eloan.lms.dao.C160M01CDao;
import com.mega.eloan.lms.dao.C160M01DDao;
import com.mega.eloan.lms.dao.C160M01EDao;
import com.mega.eloan.lms.dao.C160M01FDao;
import com.mega.eloan.lms.dao.C160S01ADao;
import com.mega.eloan.lms.dao.C160S01CDao;
import com.mega.eloan.lms.dao.C160S01DDao;
import com.mega.eloan.lms.dao.C160S01EDao;
import com.mega.eloan.lms.dao.C160S01FDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01C;
import com.mega.sso.service.BranchService;

import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 產生動審表PDF
 * 
 * <AUTHOR> 2012/12/26
 * 
 */
@Service("cls1161r02rptservice")
public class CLS1161R02RptServiceImpl extends AbstractReportService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1161R02RptServiceImpl.class);
	@Resource
	EloandbBASEService eloanDbService;
	@Resource
	UserInfoService userInfoService;
	@Resource
	C160M01ADao c160m01adao;
	@Resource
	C160M01BDao c160m01bdao;
	@Resource
	C160M01CDao c160m01cdao;
	@Resource
	C160M01DDao c160m01ddao;
	@Resource
	C160M01EDao c160m01edao;
	@Resource
	C160M01FDao c160m01fdao;
	@Resource
	C160S01CDao c160s01cdao;
	@Resource
	C160S01ADao c160s01adao;
	@Resource
	C160S01DDao c160s01ddao;
	@Resource
	C160S01EDao c160s01edao;
	@Resource
	C160S01FDao c160s01fdao;
	@Resource
	BranchService branch;
	@Resource
	CodeTypeService codeTypeService;

	@Override
	public String getReportTemplateFileName() {
		LOGGER.info("into getReportTemplateFileName");
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		// 測試用
//		 return
//		 "D:/work/src.mega/WebELoan47/lms/lms-config/src/main/resources/report/cls/CLS1161R02_zh_TW.rpt";
		return "report/cls/CLS1161R02_" + locale.toString() + ".rpt";
	}

	/*
	 * (non-Javadoc) 設定需要傳入RPT參數
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.AbstractReportService#setReportData(com
	 * .mega.eloan.lms.base.report.ReportGenerator,
	 * com.iisigroup.cap.component.PageParameters)
	 */
	@Override
	public void setReportData(ReportGenerator reportTools, PageParameters params) {
		// 測試用
//		 reportTools.setTestMethod(true);
		LOGGER.info("into setReportData");

		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		// String mainOid = params.getString(EloanConstants.MAIN_OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// L160M01A．動用審核表主檔
		C160M01A c160m01a = null;

		// C160M01C．動審表查核項目資料
		List<C160M01C> c160m01cList = null;

		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = null;
		try {
			locale = LocaleContextHolder.getLocale();
			if (locale == null)
				locale = Locale.getDefault();

			locale = LocaleContextHolder.getLocale();
			if (locale == null)
				locale = Locale.getDefault();
			c160m01a = c160m01adao.findByMainId(mainId);
			c160m01cList = c160m01cdao.findByMainId(c160m01a.getMainId());
			rptVariableMap.put("C160M01A.BrNo", branch.getBranchName(c160m01a.getOwnBrId()));
			rptVariableMap.put("C160M01A.CUSTID", c160m01a.getCustId()+"  "+c160m01a.getCustName());
			titleRows = this.setC160M01CDataList(titleRows, c160m01cList);
			reportTools.setLang(locale);
			reportTools.setVariableData(rptVariableMap);
			reportTools.setRowsData(titleRows);

		} finally {

		}
	}

	/**
	 * 設定C160M01C資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L160M01C List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setC160M01CDataList(
			List<Map<String, String>> titleRows, List<C160M01C> list) {
		// F代表第一次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		Map<String, String> mapInTitleRows = null;
		mapInTitleRows = Util.setColumnMap();
		mapInTitleRows.put("ReportBean.column07", "F");
		titleRows.add(mapInTitleRows);
		int count = 1;
		boolean result = true;
		for (C160M01C c160m01c : list) {
			if (!Util.isEmpty(Util.trim(c160m01c.getItemContent()))) {
				if (count % 2 == 1) {
					result = false;
					mapInTitleRows = Util.setColumnMap();
					mapInTitleRows.put("ReportBean.column07", "Y");
					mapInTitleRows.put("ReportBean.column01",
							this.getItemCheck(c160m01c.getItemCheck()));
					mapInTitleRows.put("ReportBean.column02", c160m01c.getItemDscr());
				} else if (count % 2 == 0) {
					result = true;
					mapInTitleRows.put("ReportBean.column03",
							this.getItemCheck(c160m01c.getItemCheck()));
					mapInTitleRows.put("ReportBean.column04", c160m01c.getItemDscr());
					titleRows.add(mapInTitleRows);
				}
				count++;
			}
		}
		if (!result) {
			titleRows.add(mapInTitleRows);
		}
		if (titleRows.size() == 1) {
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column07", "Y");
			titleRows.add(mapInTitleRows);
		}
		mapInTitleRows = Util.setColumnMap();
		mapInTitleRows.put("ReportBean.column07", "N");
		titleRows.add(mapInTitleRows);
		return titleRows;
	}

	/**
	 * 顯示需要呈現ITEMCHECK的資料
	 * 
	 * @param itemCheck
	 *            itemCheck
	 * @return 呈現到報表文字
	 */
	private String getItemCheck(String itemCheck) {
		if(Util.equals(itemCheck, "1")){
			return "V";
		}else{
			return "";
		}
	}
}
