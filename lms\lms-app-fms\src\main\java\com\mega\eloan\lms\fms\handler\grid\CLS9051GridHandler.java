/* 
 * LMS9031GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.grid;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.lms.base.service.RelatedAccountService;
import com.mega.eloan.lms.mfaloan.service.MisELF348Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("cls9051gridhandler")
public class CLS9051GridHandler extends AbstractGridHandler {

	@Resource
	MisELF348Service mis348Service;
	
	@Resource
	RelatedAccountService relatedAccountService;

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapGridResult queryC9051S01(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String fieldId = params.getString("fieldId");
		
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", "CLS9051V1");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		/**
		 * 1. 由系統產生一個 file 2. 下載file,改內容,重上傳
		 * 
		 * 此時, 在 /elnfs 有 2 個檔案 在 DB 也有 2 筆記錄select * from lms.bdocfile where
		 * oid in ( 'AAA','BBB') 但在 lms.bdocfile 的 DELETEDTIME. 1筆有刪除時間, 另1筆null
		 * → 用 $.capFileDownload(...) 去下載時, 只抓得到最新的
		 * 
		 * SimpleFileUploadHandler.java search : "deleteDup"
		 * 
		 * 所以多加上條件 deletedTime is null 讓 grid 呈現的 data row,都是可以下載的
		 */
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		// 此 method 去查 lms.bdocfile. 該table 只有 mainId,fieldId
		Page<DocFile> page = relatedAccountService.queryfile(pageSetting,
				false, false);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}
}
