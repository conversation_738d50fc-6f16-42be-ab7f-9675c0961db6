/* 
 * L120S07ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S07A;

/** 土建融案檢視清單主檔 **/
public interface L120S07ADao extends IGenericDao<L120S07A> {

	L120S07A findByOid(String oid);

	List<L120S07A> findByMainId(String mainId);

	L120S07A findByUniqueKey(String mainId);

	List<L120S07A> findByIndex01(String mainId);
}