/* 
 * CLS1161M02FormHandler.java
 */
package com.mega.eloan.lms.cls.handler.form;

import java.text.MessageFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.gwclient.EloanBatchClient;
import com.mega.eloan.common.gwclient.EloanServerBatReqMessage;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.ScoreNotHouseLoan;
import com.mega.eloan.lms.base.constants.URLConstant;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.panels.CLS1131S01Panel;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.cls.service.CLS1161Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C160M02A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.auth.CodeItemService;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 中鋼整批評等檔 FormHandler
 * </pre>
 * 
 * @since 2015/07/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/07/17,EL07623
 *          </ul>
 */
@Scope("request")
@Controller("cls1161m02formhandler")
public class CLS1161M02FormHandler extends AbstractFormHandler {

	private static final Logger logger = LoggerFactory
			.getLogger(CLS1161M02FormHandler.class);

	private static final int MAXLEN_C101S01G_ADJUSTREASON = StrUtils.getEntityFileldLegth(C101S01G.class, "adjustReason", 300);
	
	@Resource
	UserInfoService uis;

	@Resource
	DocLogService docLogService;

	@Resource
	CLS1161Service service;

	@Resource
	CodeTypeService cts;

	@Resource
	CodeItemService cis;

	@Resource
	BranchService bs;

	@Resource
	MisdbBASEService mis;

	@Resource
	MisPTEAMAPPService mps;

	@Resource
	DocFileService dfs;

	@Resource
	MisStoredProcService msps;

	@Resource
	MisCustdataService mcs;

	@Resource
	NumberService numberService;

	@Resource
	CLS1141Service cls1141Service;

	@Resource
	CLS1131Service cls1131Service;

	@Resource
	EloandbBASEService eloandbbaseservice;

	@Resource
	CLSService clsService;
	
	@Resource
	ScoreService scoreService;
	
	@Resource
	EloanBatchClient eloanBatchClient;
	
	/**
	 * 新增案件
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addCase(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String txCode = params.getString(EloanConstants.TRANSACTION_CODE);
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
		String mainId = IDGenerator.getUUID();

		// 中鋼整批評等檔
		C160M02A c160m02a = new C160M02A();
		c160m02a.setMainId(mainId);
		c160m02a.setUnitType(user.getUnitType());
		c160m02a.setOwnBrId(user.getUnitNo());
		c160m02a.setTypCd(TypCdEnum.DBU.getCode());
		c160m02a.setDocURL(URLConstant.國內個金_中鋼整批評等檔);
		c160m02a.setDocStatus(CLSDocStatusEnum.編製中);
		c160m02a.setCustId(custId);
		c160m02a.setDupNo(dupNo);
		c160m02a.setCustName(custName);
		c160m02a.setRandomCode(IDGenerator.getRandomCode());
		c160m02a.setTxCode(txCode);
		c160m02a.setDeletedTime(CapDate.getCurrentTimestamp());
		c160m02a.setApprId(user.getUserId());

		String caseNo = CapDate.getCurrentDate("yyyy")
				+ "-"
				+ Util.addZeroWithValue(
						numberService.getNumberWithMax(C160M02A.class,
								user.getUnitNo(), null, 999), 3);
		c160m02a.setCaseNo(caseNo);
		// 儲存
		service.save(c160m02a);
		CapAjaxFormResult data = DataParse.toResult(c160m02a);
		data.set("apprNm", uis.getUserName(c160m02a.getApprId()));
		result.set("data", data);
		return result;
	}// ;

	/**
	 * 刪除案件
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteCase(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = Util.trim(params.getString(EloanConstants.OID));
		C160M02A c160m02a = service.findModelByOid(C160M02A.class, oid);
		// 檢查無引用才可刪除
		List<C120S01Q> c120s01qList = service.findC120S01QBySrcMainId(c160m02a
				.getMainId());
		if (!c120s01qList.isEmpty()) {
			throw new CapMessageException("該案已被動審表引用不可刪除!", getClass());
		}
		if (c160m02a != null) {
			service.setDeleteTime(c160m02a);
		}
		return result;
	}// ;

	/**
	 * 讀取案件
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult load(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		int page = Util.parseInt(params.getString("page"));
		String oid = Util.trim(params.getString(EloanConstants.OID));
		// String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		// base form info
		C160M02A c160m02a = service.findModelByOid(C160M02A.class, oid);
		if (c160m02a != null) {
			CapAjaxFormResult baseForm = DataParse.toResult(c160m02a);
			baseForm.set("typCd", "DBU");
			StringBuilder sb = new StringBuilder();
			sb.append(Util.trim(c160m02a.getCustId())).append(" ");
			sb.append(Util.trim(c160m02a.getDupNo())).append(" ");
			sb.append(Util.trim(c160m02a.getCustName()));
			baseForm.set("custInfo", sb.toString());
			result.set("baseForm", baseForm);
		}

		switch (page) {
		case 1: // 文件資訊
			if (c160m02a != null) {
				CapAjaxFormResult C160M02AForm = DataParse.toResult(c160m02a);
				C160M02AForm.set("ownBrIdName",
						Util.trim(bs.getBranchName(c160m02a.getOwnBrId())));
				C160M02AForm.set("docStatus",
						CLSDocStatusEnum.getMessage(c160m02a.getDocStatus()));
				C160M02AForm.set("updater",
						uis.getUserName(c160m02a.getUpdater()));
				C160M02AForm.set("creator",
						uis.getUserName(c160m02a.getCreator()));
				C160M02AForm.set("apprNm",
						uis.getUserName(c160m02a.getApprId()));
				result.set("C160M02AForm", C160M02AForm);
				result.set("CaseType3Form", C160M02AForm);
			}
			break;
		case 2: // 詳細資料

			break;
		}

		List<C120S01Q> c120s01qList = service.findC120S01QBySrcMainId(c160m02a
				.getMainId());
		result.set("s01q_has_srcMainId", c120s01qList.isEmpty()?"N":"Y");
		return result;
	}

	/**
	 * 匯入EXCEL作業 step1
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult importExcelStep1(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		JSONObject formJson = DataParse.toJSON(params
				.getString("CaseType3Form"));
		String excelId = Util.trim(formJson.get("excelId"));
		DocFile docFile = dfs.read(excelId);
		if (docFile != null) {

			// C160M02A．中鋼整批滙入主檔
			String oid = Util.trim(formJson.get(EloanConstants.OID));
			C160M02A c160m02a = service.findModelByOid(C160M02A.class, oid);
			
			if(c160m02a != null){
				c160m02a.setExcelId(excelId);
				c160m02a.setDeletedTime(null);
				service.save(c160m02a);
				call_SLMS_00186_for_import_CSCExcel_step1(c160m02a);
			}
			result.set("CaseType3Form", DataParse.toResult(c160m02a));
		
			/*以下改成批次執行
			WritableWorkbook workbook = null;
			String filePath = dfs.getFilePath(docFile);

			try {

				workbook = Workbook.createWorkbook(new File(filePath),
						Workbook.getWorkbook(new File(filePath)));
				WritableSheet sheet = workbook.getSheet(0);

				// C160M02A．中鋼整批滙入主檔
				String oid = Util.trim(formJson.get(EloanConstants.OID));
				C160M02A c160m02a = service.findModelByOid(C160M02A.class, oid);
				if (c160m02a != null) {
					c160m02a.setExcelId(excelId);
					// 取得EXCEL資料
					service.parseExcel(sheet, c160m02a);
				}
				result.set("CaseType3Form", DataParse.toResult(c160m02a));
				workbook.write();
				workbook.close();
			} catch (CapMessageException e) {
				throw e;
			} catch (Exception e) {
				logger.error("parseExcel EXCEPTION!!", e);
				throw new CapMessageException(e, getClass());
			} finally {
				try {
					if (workbook != null) {
						workbook.close();
						workbook = null;
					}
				} catch (Exception e) {
					logger.debug("Workbook close EXCEPTION!", getClass());
				}
			}
		*/
		} else {
			throw new CapMessageException("請先上傳EXCEL", getClass());
		}

		return result;
	}// ;
	
	private void call_SLMS_00186_for_import_CSCExcel_step1(C160M02A c160m02a) throws CapMessageException{
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		try {
			EloanServerBatReqMessage req = new EloanServerBatReqMessage();
			req.setUserId(user.getUserId());
			req.setRunType(EloanServerBatReqMessage.RUN_TYPE_QUEUE);
			req.setSchId("SLMS-00186"); //批次代碼
			
			JSONObject paraJson = new JSONObject();
			paraJson.put("act", "cls_import_CSGExcel_step1");
			paraJson.put("c160m02a_oid", c160m02a.getOid());

			StringBuffer batchParams = new StringBuffer();
			batchParams.append("REQUEST=").append(paraJson.toString());

			req.setParams(batchParams.toString());
			req.setDupeId(StringUtils.left(c160m02a.getOid(), 30));
			eloanBatchClient.send(req);

		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e));
			throw new CapMessageException("匯入失敗，請洽資訊處", getClass());
		}
	}
	
	// J-112-0390 中鋼整批匯入-取得批次執行狀態
	@DomainAuth(AuthType.Query)
	public IResult checkImportStatus(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String msg = "";
		String importExcelStatus = "";
		// C160M02A．中鋼整批滙入主檔
		C160M02A c160m02a = service.findModelByOid(C160M02A.class, oid);
		
		if( c160m02a!=null ){
			importExcelStatus = Util.trim(c160m02a.getImportExcelStatus());
			if(Util.equals("01", importExcelStatus)){
				msg = "匯入EXCEL作業處理中，請稍後執行";
			}
			if(Util.equals("02", importExcelStatus)){
				msg = "發查外部系統作業處理中，請稍後執行";
			}
			if(Util.equals("03", importExcelStatus)){
				msg = "匯入EXCEL作業失敗，請重新上傳EXCEL";
			}
			if(Util.equals("04", importExcelStatus)){
				msg = "匯入EXCEL作業成功";
			}
			if(Util.equals("05", importExcelStatus)){
				msg = "發查外部系統作業失敗，請重新上傳EXCEL";
			}
			
		}
		result.set("importExcelStatus", importExcelStatus);
		result.set("msg", msg);
		return result;
	}

	/**
	 * 讀取評等資料
	 * <ul>
	 * <li>個金徵信 > 基本資料 > 開啟等級評分表
	 * <li>個金徵信 > 相關查詢暨評等模型
	 * </ul>
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult loadScore(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));

		C120S01Q model_q = null;

		String markModel = Util.trim(params.getString("markModel",
				UtilConstants.L140S02AModelKind.房貸));

		if (Util.equals(markModel, UtilConstants.L140S02AModelKind.非房貸)) {
			model_q = cls1131Service.findModelByKey(C120S01Q.class, mainId,
					custId, dupNo);
		}
		if (model_q != null) {
			CapAjaxFormResult formResult = cls1131Service.loadScore_Q(model_q);
			// 評等訊息
			formResult
					.set("grade1_markModel_2", Util.trim(model_q.getGrade1())); // 初始評等
			formResult
					.set("grade2_markModel_2", Util.trim(model_q.getGrade2())); // 調整評等
			
			String grade2Status_markModel_2 = "";
			if(Util.equals(model_q.getVarVer(), ClsScoreUtil.V4_0_NOT_HOUSE_LOAN)){
				grade2Status_markModel_2 = "(無法調整)";
			}else{
				grade2Status_markModel_2 = _getAdjustStatus(model_q.getAdjustStatus(),
						model_q.getGrade1(), model_q.getGrade2());
			}
			
			formResult.set(
					"grade2Status_markModel_2",grade2Status_markModel_2); // 調整評等
			formResult
					.set("grade3_markModel_2", Util.trim(model_q.getGrade3())); // 最終評等
			formResult.set("alertMsg_markModel_2", Util.trim(clsService
					.getClsGradeMessage(model_q,
							UtilConstants.L140S02AModelKind.非房貸, OverSeaUtil.TYPE_UI, ""))); // 票交所與聯徵特殊負面資訊
			formResult
					.set("varVer_markModel_2", Util.trim(model_q.getVarVer()));

			for (int i = 1; i <= 8; i++) {
				String col = "chkItem" + i;
				String val = Util.trim(model_q.get(col));
				formResult.set("q_" + col, val);
			}
			formResult.set("custName", custName);
			result.set("C101S01QForm", formResult);
		}
		return result;
	}// ;

	private static String _getAdjustStatus(String adjustStatus,
			String raw_grade1, String raw_grade2) {
		String grade2 = Util.trim(raw_grade2);
		if (Util.equals("1", adjustStatus)) {
			return "調升" + grade2 + "等";
		} else if (Util.equals("2", adjustStatus)) {
			return "調降" + grade2 + "等";
		} else if (Util.equals("3", adjustStatus)) {
			return "無調整";
		} else {
			// 表示有評等過
			if (Util.isNotEmpty(Util.trim(raw_grade1))) {
				return "無調整";
			}
			return "";
		}
	}

	@DomainAuth(AuthType.Query)
	public IResult prepareAdjRating(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = Util.trim(params.getString(EloanConstants.OID));
		C120M01A c120m01a = clsService.findC120M01A_oid(oid); 
		if (c120m01a == null) {
			throw new CapMessageException("查無c120m01a", getClass());
		}
		C120S01Q c120s01q = c120m01a.getC120s01q();
		Date date_jcicFlg_V_NN = null;
		if(c120s01q!=null){
			String checkItemRange = clsService.getCheckItemRange(c120s01q);
			result.putAll(ClsUtil.procMarkModel_Q(c120s01q, "", checkItemRange));
			date_jcicFlg_V_NN = c120s01q.getJcicQDate();
		}
		result.set("jcicFlg", c120m01a.getJcicFlg());
		ClsUtil.set_date_jcicFlg_V_NN(result, date_jcicFlg_V_NN);
		return result;
	}
	
	@DomainAuth(AuthType.Query)
	public IResult loadAdjust(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String markModel = Util.trim(params.getString("markModel"));

		if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, markModel)) {
			C120S01Q c120s01q = _findC120S01Q(mainId, custId, dupNo);
			if (c120s01q != null) {
				String latest_q_varVer = scoreService.get_Version_NotHouseLoan();
				if(Util.notEquals(c120s01q.getVarVer(), latest_q_varVer)){
					if(scoreService.clsScore_inBufferPeriod("Q", latest_q_varVer)){
						//仍允許
					}else{
						Properties prop = MessageBundleScriptCreator.getComponentResource(CLS1131S01Panel.class);
						//message.varVersion_diff=目前版本{0}並非最新的模型版本{1}，請重新引進					
						throw new CapMessageException(MessageFormat.format(prop.getProperty("message.varVersion_diff")
								,c120s01q.getVarVer(), latest_q_varVer), getClass());						
					}
				}				
				result.set("adjustNotHouseLoanForm",DataParse.toResult(c120s01q));
				// ===========================
				// 非房貸的昇等限制(最高 提昇2 等)
				// ===========================
			}
		}

		return result;
	}
	
	public IResult saveAdjust(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String markModel = Util.trim(params.getString("markModel"));
		
		if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, markModel)) {
			C120S01Q model_q = _findC120S01Q(mainId, custId, dupNo);				
			if (model_q != null) {
				String adjustNotHouseLoanForm = Util.trim(params
						.getString("adjustNotHouseLoanForm"));
				JSONObject adjustJson = DataParse
						.toJSON(adjustNotHouseLoanForm);
				// ---依 grade3重算 違約機率
				JSONObject notHouseLoanDR = scoreService.scoreNotHouseLoan(
						ScoreNotHouseLoan.type.非房貸違約機率, adjustJson, model_q.getVarVer());
				{
					scoreService.setNotHouseLoanDR(notHouseLoanDR, adjustJson);
				}
				// ---
				DataParse.toBean(adjustJson, model_q);
				model_q.setGrdTDate(CapDate.getCurrentTimestamp());
		
				String adjustReason = Util.trim(model_q.getAdjustReason());
				if (Util.notEquals(Util.truncateString(adjustReason,
								MAXLEN_C101S01G_ADJUSTREASON), adjustReason)) {
					Map<String, String> param = new HashMap<String, String>();
					param.put("colName", "【調整理由】");
					throw new CapMessageException(RespMsgHelper.getMessage(
							UtilConstants.AJAX_RSP_MSG.輸入位數超過, param), getClass());
				}
				if(Util.isNotEmpty(adjustReason)){
					Map<String, String> inputMap = new HashMap<String, String>();
					Map<String, String> errorMap = new HashMap<String, String>();
					Map<String, String> confirmMap = new HashMap<String, String>();
					
					inputMap.put(custId+"-"+dupNo, adjustReason);
					clsService.validate_adjustReason(inputMap, errorMap, confirmMap);
					
					if(errorMap.size()>0){
						throw new CapMessageException(StringUtils.join(errorMap.values(), ""), getClass());
					}
				}
				service.save(model_q);
				String checkItemRange = clsService.getCheckItemRange(model_q);
				adjustJson.putAll(ClsUtil.procMarkModel_Q(model_q, "", checkItemRange));
				result.set(ClsUtil.GRADE_DIV_MARLMODEL_2, new CapAjaxFormResult(
						adjustJson));
			}
		}
		
		return result;
	}
	
	private C120S01Q _findC120S01Q(String mainId, String custId, String dupNo){
		C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(mainId, custId, dupNo);
		if(c120m01a!=null){
			return c120m01a.getC120s01q();
		}						
		return null;
	}
}