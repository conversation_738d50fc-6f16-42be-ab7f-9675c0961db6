/* 
 * LMS1205Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service;

import java.util.List;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L700M01A;

import tw.com.iisi.cap.dao.utils.ISearch;

/**
 * 案件分案對照表 Service
 * 
 * <AUTHOR> Lin
 * 
 */
public interface LMS7005Service extends AbstractService {

	// 案件分案對照表
	L700M01A findL700m01aByOid(String oid);
	void deleteListL700m01a(String[] oidArray);
	L700M01A findL700m01aByUnique<PERSON>ey(String branchId, String subject);
	List<L700M01A> findL700m01aList(ISearch search);
	List<L700M01A> findL700m01aList();
}
