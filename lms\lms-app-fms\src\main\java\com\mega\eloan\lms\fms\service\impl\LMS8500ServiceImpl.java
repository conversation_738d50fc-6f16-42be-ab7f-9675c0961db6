package com.mega.eloan.lms.fms.service.impl;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSBisService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L850M01ADao;
import com.mega.eloan.lms.dao.L850M01BDao;
import com.mega.eloan.lms.dao.L850M01CDao;
import com.mega.eloan.lms.fms.service.LMS8500Service;
import com.mega.eloan.lms.model.L120S25C;
import com.mega.eloan.lms.model.L850M01A;
import com.mega.eloan.lms.model.L850M01B;
import com.mega.eloan.lms.model.L850M01C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 資料上傳作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class LMS8500ServiceImpl extends AbstractCapService implements
		LMS8500Service {

	private static final Logger logger = LoggerFactory
			.getLogger(LMS8500ServiceImpl.class);

	@Resource
	FlowService flowService;

	@Resource
	TempDataService tempDataService;

	@Resource
	DocLogService docLogService;

	@Resource
	L850M01ADao l850m01aDao;

	@Resource
	L850M01BDao l850m01bDao;

	@Resource
	L850M01CDao l850m01cDao;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	LMSService lmsService;

	@Resource
	LMSBisService lmsBisService;

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L850M01A.class) {
			return l850m01aDao.findPage(search);
		} else if (clazz == L850M01B.class) {
			return l850m01bDao.findPage(search);
		} else if (clazz == L850M01C.class) {
			return l850m01cDao.findPage(search);
		}
		return null;
	}

	@Override
	public Map<String, String> getAppCodeBySsoUnitno()
			throws CapMessageException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String ssoUntiNo = user.getSsoUnitNo();

		// 資料上傳作業，各單位可使用APPCODE設定
		// { "912" : "001-01", "918" : "001-01,090-01", "XX" : "001-01,090-01"}
		// 這個系統參數會用json的方式對每個單位進行設定
		String L850M01A_APPCODE_SETTINGS = Util.trim(lmsService
				.getSysParamDataValue("L850M01A_APPCODE_SETTINGS"));
		JSONObject appCodeSettings;
		try {
			appCodeSettings = DataParse.toJSON(L850M01A_APPCODE_SETTINGS);
		} catch (CapException e) {
			throw new CapMessageException("資料上傳作業，取得申請類別失敗" + e.toString(),
					getClass());
		}

		Object thisUnitNoSettings = appCodeSettings.get(ssoUntiNo);
		// 判斷有沒有設定這個單位可使用的appCode，若沒有就去用XX全行預設的
		if (thisUnitNoSettings == null) {
			// "001-01,090-01"
			thisUnitNoSettings = appCodeSettings.get("XX");
		}

		// 001->自有資本與風險性資產預估表
		List<CodeType> appCodeDescList = codeTypeService.findByCodeTypeList(
				"L850M01A_APPCODE", "zh_TW");
		Map<String, String> appCodeDescLMap = new HashMap<String, String>();
		for (CodeType appCode : appCodeDescList) {
			appCodeDescLMap.put(appCode.getCodeValue(), appCode.getCodeDesc());
		}

		Map<String, String> selectMap = new LinkedHashMap<String, String>();
		String[] appCodeList = thisUnitNoSettings.toString().split(",");

		for (String full : appCodeList) {
			// 090-01
			String appCode = Util.trim(full).split("-")[0];
			selectMap.put(full, appCodeDescLMap.get(appCode));
		}

		return selectMap;
	}

	@Override
	public L850M01A newL850m01a(String txCode, String appCode, String version) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainId = IDGenerator.getUUID();
		L850M01A l850m01a = new L850M01A();
		l850m01a.setMainId(mainId);
		l850m01a.setOwnBrId(user.getUnitNo());
		l850m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		l850m01a.setDocURL("/fms/lms8500m01v" + appCode + version);
		l850m01a.setTxCode(txCode);

		l850m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		l850m01a.setUpdater(user.getUserId());

		l850m01a.setAppCode(appCode);// 申請類別
		l850m01a.setVersion(version);// 版本

		// call service的save，因為要起流程
		this.save(l850m01a);
		return l850m01a;
	}

	@Override
	public L850M01A saveL850m01a(String mainId, String form)
			throws CapMessageException {

		L850M01A l850m01a = l850m01aDao.findByMainId(mainId);
		l850m01a.setRandomCode(IDGenerator.getRandomCode());
		l850m01a.setDeletedTime(null);// 不知道有沒有需要，避免刪了又儲存?

		// 跑自己的appCode的save，要做什麼自己寫
		// L850M01A是主檔，L850M01C是資料檔可以多對一對到L850M01A
		// 但就是看自己想怎麼配合運用
		if ("001".equals(Util.trim(l850m01a.getAppCode()))) {
			saveData001(l850m01a, form);
		} else if ("090".equals(Util.trim(l850m01a.getAppCode()))) {
			// 沒欄位要存，只有上傳的時候會寫資料

		} else {

		}

		this.save(l850m01a);
		return l850m01a;
	}

	/**
	 * appCode001儲存時要做的事情
	 * 
	 * @param l850m01a
	 * @param form
	 * @throws CapMessageException
	 */
	private void saveData001(L850M01A l850m01a, String form)
			throws CapMessageException {
		// 只負責檢核和儲存
		// 畫面呈現交給handler去處理
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = l850m01a.getMainId();
		JSONObject jsonData = null;
		jsonData = JSONObject.fromObject(form);

		String lmsBisSelfCapitalDate = (String) jsonData
				.get("lmsBisSelfCapitalDate");// 資料日期
		String lmsBisSelfCapital = (String) jsonData.get("lmsBisSelfCapital");// 自有資本(E46)
		String lmsBisMegaRwa = (String) jsonData.get("lmsBisMegaRwa");// 風險性資產(E61)

		// 欄位檢核
		String message1 = "尚未輸入{0}欄位，請輸入後再點選儲存";
		StringBuilder column = new StringBuilder();
		if (Util.isEmpty(lmsBisSelfCapitalDate)) {
			column = Util.isNotEmpty(column) ? column.append("、") : column;
			column.append("資料日期");
		}
		if (Util.isEmpty(lmsBisSelfCapital)) {
			column = Util.isNotEmpty(column) ? column.append("、") : column;
			column.append("自有資本(E46)");
		}
		if (Util.isEmpty(lmsBisMegaRwa)) {
			column = Util.isNotEmpty(column) ? column.append("、") : column;
			column.append("風險性資產(E61)");
		}

		if (Util.isNotEmpty(column)) {
			throw new CapMessageException(
					MessageFormat.format(message1, column), getClass());
		}

		// 開始處理資料
		// 刪掉原來的資料，反正這個appCode001，永遠只會有一筆L850M01C
		List<L850M01C> l850m01cList = l850m01cDao.findByMainId(mainId);
		l850m01cDao.delete(l850m01cList);

		// 新增一筆資料
		L850M01C l850m01c = new L850M01C();
		l850m01c.setMainId(mainId);
		l850m01c.setCreator(user.getUserId());
		l850m01c.setCreateTime(CapDate.getCurrentTimestamp());
		l850m01c.setDetail1(lmsBisSelfCapitalDate);
		l850m01c.setDetail2(lmsBisSelfCapital);
		l850m01c.setDetail3(lmsBisMegaRwa);
		save(l850m01c);
	}

	@Override
	public void deleteL850m01a(String[] oids) {
		// 刪除就是更新L850M01A主檔，不對L850M01B、L850M01C處理
		// 所以目前不特別切appCode做處理
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L850M01A> l850m01as = new ArrayList<L850M01A>();

		for (String oid : oids) {
			L850M01A l850m01a = l850m01aDao.findByOid(oid);
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			l850m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			l850m01a.setUpdater(user.getUserId());
			l850m01as.add(l850m01a);

			docLogService.record(l850m01a.getOid(), DocLogEnum.DELETE);
		}

		if (l850m01as != null) {
			l850m01aDao.save(l850m01as);
		}
	}

	@Override
	public void processL850m01bAndFlowAction(String oid,
			String[] formSelectBoss, String manager, boolean haveCheckDate,
			boolean haveFlowAction, boolean isApprove) throws Throwable {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L850M01A l850m01a = (L850M01A) this.findModelByOid(L850M01A.class, oid);

		if (!Util.isEmpty(formSelectBoss) || Util.isNotEmpty(manager)) {

			List<L850M01B> L850M01Bs = new ArrayList<L850M01B>();

			// 有的表單可能根本不需要選授信主管
			if (!Util.isEmpty(formSelectBoss)) {
				for (String people : formSelectBoss) {
					L850M01B L850M01B = new L850M01B();
					L850M01B.setCreator(user.getUserId());
					L850M01B.setCreateTime(CapDate.getCurrentTimestamp());
					L850M01B.setMainId(l850m01a.getMainId());
					L850M01B.setBranchType(user.getUnitType());
					L850M01B.setBranchId(user.getUnitNo());
					// L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
					L850M01B.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
					L850M01B.setStaffNo(people);
					L850M01Bs.add(L850M01B);
				}
			}
			L850M01B managerL850M01B = new L850M01B();
			managerL850M01B.setCreator(user.getUserId());
			managerL850M01B.setCreateTime(CapDate.getCurrentTimestamp());
			managerL850M01B.setMainId(l850m01a.getMainId());
			managerL850M01B.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
			managerL850M01B.setStaffNo(manager);
			managerL850M01B.setBranchType(user.getUnitType());
			managerL850M01B.setBranchId(user.getUnitNo());
			L850M01Bs.add(managerL850M01B);

			L850M01B apprL850M01B = new L850M01B();
			apprL850M01B.setCreator(user.getUserId());
			apprL850M01B.setCreateTime(CapDate.getCurrentTimestamp());
			apprL850M01B.setMainId(l850m01a.getMainId());
			apprL850M01B.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
			apprL850M01B.setStaffNo(user.getUserId());
			apprL850M01B.setBranchType(user.getUnitType());
			apprL850M01B.setBranchId(user.getUnitNo());
			L850M01Bs.add(apprL850M01B);
			this.saveL850m01bList(L850M01Bs);
		}

		L850M01B L850M01BL4 = new L850M01B();
		// 如果有這個key值表示是輸入chekDate核准日期
		if (haveCheckDate) {
			l850m01a.setApprover(user.getUserId());
			l850m01a.setApproveTime(CapDate.getCurrentTimestamp());
			L850M01B L850M01B = this.findL850m01b(l850m01a.getMainId(),
					user.getUnitType(), user.getUnitNo(), user.getUserId(),
					UtilConstants.STAFFJOB.執行覆核主管L4);
			if (L850M01B == null) {
				L850M01B = new L850M01B();
				L850M01B.setCreator(user.getUserId());
				L850M01B.setCreateTime(CapDate.getCurrentTimestamp());
				L850M01B.setMainId(l850m01a.getMainId());
				L850M01B.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
				L850M01B.setStaffNo(user.getUserId());
				L850M01B.setBranchType(user.getUnitType());
				L850M01B.setBranchId(user.getUnitNo());
			}
			L850M01BL4 = L850M01B;
		}

		if (!Util.isEmpty(l850m01a)) {

			// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
			if (haveFlowAction) {
				// 退回不檢查
				if (isApprove) {
					L850M01B L850M01B = this.findL850m01b(l850m01a.getMainId(),
							user.getUnitType(), user.getUnitNo(),
							user.getUserId(), UtilConstants.STAFFJOB.經辦L1);

					if (L850M01B != null) {
						// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
						throw new FlowMessageException("EFD0053");
					} else {
						this.save(L850M01BL4);
					}
				}
			}
			this.flowAction(l850m01a.getOid(), l850m01a, haveFlowAction,
					isApprove);
		}
	}

	@Override
	public void deleteL850m01bList(String mainId, boolean isAll) {
		List<L850M01B> l850m01bs = l850m01bDao.findByMainId(mainId);
		this.deleteL850m01bList(l850m01bs, isAll);
	}

	@Override
	public void deleteL850m01bList(List<L850M01B> l850m01bs, boolean isAll) {
		if (isAll) {
			l850m01bDao.delete(l850m01bs);
		} else {
			List<L850M01B> deleteL850m01bList = new ArrayList<L850M01B>();
			for (L850M01B l850m01b : l850m01bs) {
				String staffJob = l850m01b.getStaffJob();
				if (!("L6".equals(staffJob) || "L7".equals(staffJob))) {
					deleteL850m01bList.add(l850m01b);
				}
			}
			l850m01bDao.delete(deleteL850m01bList);
		}
	}

	@Override
	public void saveL850m01bList(List<L850M01B> l850m01bs) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L850M01B l850m01b : l850m01bs) {
			l850m01b.setUpdater(user.getUserId());
			l850m01b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l850m01bDao.save(l850m01bs);
	}

	@Override
	public L850M01B findL850m01b(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
		return l850m01bDao.findByUniqueKey(mainId, branchType, branchId,
				staffNo, staffJob);
	}

	@Override
	public void flowAction(String mainOid, L850M01A l850m01a,
			boolean setResult, boolean resultType) throws Throwable {
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS8500Flow",
						((L850M01A) l850m01a).getOid(), user.getUserId(),
						user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				// resultType 控制前進還是後退
				// 當有先行動用的狀態 是到03O 非先行動用表示已完成 到05O
				inst.setAttribute("result", resultType ? "核准" : "退回");
				if (resultType) {
					// save((L140MM4A) model);
					// 做已核准要做的事情!!!
					// 這裡應該要切出不同的appCode可以幹不同的事情
					String appCode = Util.trim(l850m01a.getAppCode());
					if ("001".equals(appCode)) {
						approveData001(l850m01a);
					} else if ("090".equals(appCode)) {
						approveData090(l850m01a);
					}
				}
			}
			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	/**
	 * appCode001核准時要做的事情，寫入L120S25C BIS使用的參數檔
	 * 
	 * 整支功能等於LMS1401S11FormHandler.saveBisParamToL120s25c
	 * 
	 * @param l850m01a
	 * @throws CapException
	 */
	private void approveData001(L850M01A l850m01a) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainId = Util.trim(l850m01a.getMainId());
		List<L850M01C> l850m01cList = (List<L850M01C>) findListByMainId(
				L850M01C.class, mainId);

		// 照理來說只會有一筆L850M01C
		if (l850m01cList != null && l850m01cList.size() != 0) {
			L850M01C l850m01c = l850m01cList.get(0);
			String lmsBisSelfCapitalDate = l850m01c.getDetail1();// 資料日期
			String lmsBisSelfCapital = l850m01c.getDetail2();// 自有資本(E46)
			String lmsBisMegaRwa = l850m01c.getDetail3();// 風險性資產(E61)

			if (Util.isEmpty(lmsBisSelfCapitalDate)
					|| Util.isEmpty(lmsBisSelfCapital)
					|| Util.isEmpty(lmsBisMegaRwa)) {
				throw new CapMessageException("欄位不得空白", getClass());
			}

			// 單位元，會計處每月15日自結報表自有資本(E46)
			List<L120S25C> l120s25cs1 = lmsBisService
					.findL120s25cByParamTypeKeyDate("LMS_BIS_SELF_CAPITAL",
							null, lmsBisSelfCapitalDate);
			if (l120s25cs1 != null && !l120s25cs1.isEmpty()) {
				lmsBisService.deleteListL120s25c(l120s25cs1);
			}

			// 單位元，會計處每月15日自結報表風險性資產(E61)
			List<L120S25C> l120s25cs2 = lmsBisService
					.findL120s25cByParamTypeKeyDate("LMS_BIS_MEGARWA", null,
							lmsBisSelfCapitalDate);
			if (l120s25cs2 != null && !l120s25cs2.isEmpty()) {
				lmsBisService.deleteListL120s25c(l120s25cs2);
			}

			L120S25C l120s25c = null;
			// 單位元，會計處每月15日自結報表自有資本(E46)
			// INSERT INTO
			// lms.l120s25c(OID,PARAMTYPE,PARAMDATE,PARAMKEY,PARAMVAL,PARAMDSCR)
			// VALUES
			// (GET_OID(),'LMS_BIS_SELF_CAPITAL','2022-08-01','','309822545000','單位元，會計處每月15日自結報表自有資本(E46)');
			l120s25c = new L120S25C();
			l120s25c.setParamDate(lmsBisSelfCapitalDate);
			l120s25c.setParamDscr("單位元，會計處每月15日自結報表自有資本(E46)");
			l120s25c.setParamKey("");
			l120s25c.setParamType("LMS_BIS_SELF_CAPITAL");
			l120s25c.setParamVal((Util.parseBigDecimal(lmsBisSelfCapital)
					.multiply(new BigDecimal(1000))).toPlainString());
			l120s25c.setCreator(user.getUserId());
			l120s25c.setCreateTime(CapDate.getCurrentTimestamp());
			lmsBisService.save(l120s25c);

			// 單位元，會計處每月15日自結報表風險性資產(E61)
			// INSERT INTO
			// lms.l120s25c(OID,PARAMTYPE,PARAMDATE,PARAMKEY,PARAMVAL,PARAMDSCR)
			// VALUES
			// (GET_OID(),'LMS_BIS_MEGARWA','2022-08-01','','2316709025000','單位元，會計處每月15日自結報表風險性資產(E61)');
			l120s25c = new L120S25C();
			l120s25c.setParamDate(lmsBisSelfCapitalDate);
			l120s25c.setParamDscr("單位元，會計處每月15日自結報表風險性資產(E61)");
			l120s25c.setParamKey("");
			l120s25c.setParamType("LMS_BIS_MEGARWA");
			l120s25c.setParamVal((Util.parseBigDecimal(lmsBisMegaRwa)
					.multiply(new BigDecimal(1000))).toPlainString());
			l120s25c.setCreator(user.getUserId());
			l120s25c.setCreateTime(CapDate.getCurrentTimestamp());
			lmsBisService.save(l120s25c);

			// 單位元，會計處每月15日自結報表風險性資產(E61)
		}
	}

	/**
	 * appCode090核准時要做的事情，寫入COM.BCODETYPE共用參數檔
	 * 
	 * @param l850m01a
	 * @throws CapException
	 */
	private void approveData090(L850M01A l850m01a) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainId = Util.trim(l850m01a.getMainId());
		List<L850M01C> l850m01cList = (List<L850M01C>) findListByMainId(
				L850M01C.class, mainId);

		// 核准寫入BCODETYPE前，最後檢查這次要新增的資料是否都是不存在的資料
		StringBuilder errMsg = this.checkCodeTypeCanInsert(l850m01cList);
		if (Util.isNotEmpty(errMsg)) {
			String error = "覆核失敗，因以下資料已存在於資料庫中，無法寫入共用參數表<BR/>" + errMsg.toString();
			throw new CapMessageException(error, getClass());
		}

		String L850M01A_090_ON = Util.trim(lmsService
				.getSysParamDataValue("L850M01A_090_ON"));
		if (!"Y".equals(L850M01A_090_ON)) {
			String error = "覆核失敗，資料上傳作業-共用參數表，功能尚未啟用" + errMsg.toString();
			throw new CapMessageException(error, getClass());
		}
		
		// 檢查過了之後就是新增CODETYPE
		// 這邊撈出來的一筆筆L850M01C都是要寫入的一筆筆CODETYPE!!
		if (l850m01cList != null && l850m01cList.size() != 0) {
			for (L850M01C l850m01c : l850m01cList) {
				// 1:CODETYPE
				// 2:CODEVALUE
				// 3:CODEDESC
				// 4:LOCALE
				// 5:CODEDESC2
				// 6:CODEDESC3
				// 7:CODEORDER
				CodeType codeTypeObj = new CodeType();
				codeTypeObj.setCodeType(l850m01c.getDetail1());
				codeTypeObj.setCodeValue(l850m01c.getDetail2());
				codeTypeObj.setCodeDesc(l850m01c.getDetail3());
				codeTypeObj.setLocale(l850m01c.getDetail4());
				codeTypeObj.setCodeDesc2(l850m01c.getDetail5());
				codeTypeObj.setCodeDesc3(l850m01c.getDetail6());
				
				// 比較怕這個欄位沒輸入，導致噴錯
				String order = l850m01c.getDetail7();
				if(Util.isInteger(order)){
					codeTypeObj.setCodeOrder(new Integer(order));
				}else{
					// 沒輸就給他1
					codeTypeObj.setCodeOrder(new Integer("1"));
				}
				codeTypeObj.setLastModifyBy(MegaSSOSecurityContext.getUserId());
				codeTypeObj.setLastModifyTime(CapDate.getCurrentTimestamp());
				
				codeTypeService.save(codeTypeObj);
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L850M01A.class) {
			return l850m01aDao.findByIndex01(mainId);
		} else if (clazz == L850M01B.class) {
			return l850m01bDao.findByMainId(mainId);
		} else if (clazz == L850M01C.class) {
			return l850m01cDao.findByIndex01(mainId);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L850M01A.class) {
			return (T) l850m01aDao.findByOid(oid);
		} else if (clazz == L850M01B.class) {
			return (T) l850m01bDao.findByOid(oid);
		} else if (clazz == L850M01C.class) {
			return (T) l850m01cDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L850M01A) {
					if (Util.isEmpty(((L850M01A) model).getOid())) {
						((L850M01A) model).setCreator(user.getUserId());
						((L850M01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
						l850m01aDao.save((L850M01A) model);

						flowService.start("LMS8500Flow",
								((L850M01A) model).getOid(), user.getUserId(),
								user.getUnitNo());
					} else {
						// 當文件狀態為編製中時文件亂碼才變更

						((L850M01A) model).setUpdater(user.getUserId());
						((L850M01A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						l850m01aDao.save((L850M01A) model);
						if (!"Y".equals(SimpleContextHolder
								.get(EloanConstants.TEMPSAVE_RUN))) {
							tempDataService.deleteByMainId(((L850M01A) model)
									.getMainId());
							docLogService.record(((L850M01A) model).getOid(),
									DocLogEnum.SAVE);
						}
					}
				} else if (model instanceof L850M01B) {
					((L850M01B) model).setUpdater(user.getUserId());
					((L850M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l850m01bDao.save((L850M01B) model);
				} else if (model instanceof L850M01C) {
					((L850M01C) model).setUpdater(user.getUserId());
					((L850M01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l850m01cDao.save((L850M01C) model);
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L850M01A) {
					l850m01aDao.delete((L850M01A) model);
				} else if (model instanceof L850M01B) {
					l850m01bDao.delete((L850M01B) model);
				} else if (model instanceof L850M01C) {
					l850m01cDao.delete((L850M01C) model);
				}
			}
		}
	}

	@Override
	public void saveL850m01c(String mainId, List<L850M01C> l850m01cList) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		for (L850M01C l850m01c : l850m01cList) {
			l850m01c.setMainId(mainId);
			l850m01c.setCreator(user.getUserId());
			l850m01c.setCreateTime(CapDate.getCurrentTimestamp());
			l850m01c.setUpdater(user.getUserId());
			l850m01c.setUpdateTime(CapDate.getCurrentTimestamp());
		}

		l850m01cDao.save(l850m01cList);
	}

	@Override
	public void deleteL850m01c(String mainId) {
		List<L850M01C> l850m01cList = l850m01cDao.findByMainId(mainId);
		l850m01cDao.delete(l850m01cList);
	}

	@Override
	public StringBuilder checkCodeTypeCanInsert(List<L850M01C> l850m01cList) {
		StringBuilder errMsg = new StringBuilder();
		Map<String, Map<String, String>> allCodeTypeMap = new HashMap<String, Map<String, String>>();
		// UNIQUE INDEX為以下三個欄位LOCALE, CODETYPE, CODEVALUE
		// 每一筆想要匯入的codeType都要檢查，只要有一筆不合規就不給匯入
		for (L850M01C l850m01c : l850m01cList) {
			// 1:CODETYPE
			// 2:CODEVALUE
			// 3:CODEDESC
			// 4:LOCALE
			// 5:CODEDESC2
			// 6:CODEDESC3
			// 7:CODEORDER
			String codeType = l850m01c.getDetail1();
			String codeValue = l850m01c.getDetail2();
			String locale = l850m01c.getDetail4();

			Map<String, String> oneCodeTypeMap = allCodeTypeMap.get(codeType);
			if (oneCodeTypeMap == null) {
				// 沒有的情況就是第一次撈
				oneCodeTypeMap = new HashMap<String, String>();

				List<CodeType> oneCodeTypeList = codeTypeService
						.findAllLocalByCodeType(codeType);
				// 1.是現行資料就放資料進去 2.沒資料，是全新codeType的情況就放空map
				if (oneCodeTypeList != null && oneCodeTypeList.size() > 0) {
					for (CodeType codeTypeModel : oneCodeTypeList) {
						// 第二層的map拿loacle_codeValue組起來當作key
						oneCodeTypeMap.put(
								Util.trim(codeTypeModel.getLocale())
										+ "_"
										+ Util.trim(codeTypeModel
												.getCodeValue()), "Y");
					}
				} else {
					// 就是放一個空的map回去
				}

				allCodeTypeMap.put(codeType, oneCodeTypeMap);
			}

			// 這組codeType裡，用loacle_codeValue組起來當作key去查已經有存在的資料了
			// 這種狀況不可以匯入也不可以INSERT
			if (oneCodeTypeMap.get(locale + "_" + codeValue) != null) {
				errMsg.append("CODETYPE: " + codeType + ", CODEVALUE: "
						+ codeValue + ", LOCALE: " + locale + "<BR/>");
			}
		}

		return errMsg;
	}
}
