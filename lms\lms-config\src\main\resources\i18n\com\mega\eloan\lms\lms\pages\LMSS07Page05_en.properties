#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u932f\u8aa4\u8996\u7a97\u8a0a\u606f\u5167\u5bb9
#==================================================
L1205S07.error1 =Inquiry Start Year/Month
L1205S07.error2 =Inquiry End Year/Month
L1205S07.error3 =Earlier than the starting date of DW data range (currently no data)
L1205S07.error4 =Earlier than the starting date of DW data range
L1205S07.error5 =Later than the latest date of DW data range (currently no data)
L1205S07.error6 =Later than the latest date of DW data range
L1205S07.error7 =\u203bThe data warehouse updates last month's banking transactions on the 10th each month (which is different to DWC350, which updates immediately at the time data becomes available).
L1205S07.error8 =Later than the starting date of DW data range
L1205S07.error9 =Later than the starting date of DW data range (currently no data)
L1205S07.error10 =Earlier than the latest date of DW data range
L1205S07.error11 =The group's/company's latest credit relationship is blank; please import using the "Related Documents" function

l120v01.error3=You have entered an invalid month
l120v01.error8=You have entered an invalid year
l120v01.error9=You have entered an invalid year range
#==================================================
# \u5f80\u4f86\u5f59\u7e3dGrid\u6a19\u984c\u540d\u7a31
#==================================================
L1205S07.grida=Borrower's Name
L1205S07.gridb=Borrower
L1205S07.grid3=Credit Line
L1205S07.grid4=Credit Limit Serial Number
L1205S07.grid5=Reference Borrower
L1205S07.grid6=Reference Credit Line
L1205S07.grid7=Reference Credit Limit Serial Number
L1205S07.grid8=Print Mode
L1205S07.grid9=Applicant
L1205S07.grid10=Approval Date
L1205S07.grid11=Document Status
L1205S07.grid12=Credit Report No.
L1205S07.grid13=Handling officer
L1205S07.grid14=Nature
L1205S07.grid15=Printing Required
L1205S07.grid16=Related Party's UBN
L1205S07.grid17=Related Party's Name
L1205S07.grid18=Relationship With Borrower
L1205S07.grid19=Contribution
L1205S07.grid20=Credit Limit
L1205S07.grid21=Outstanding Balance
L1205S07.grid22=Current Deposit
L1205S07.grid23=Applicant
L1205S07.grid24=Approval Date
L1205S07.grid25=Document Status
L1205S07.grid26=Credit Report No.
L1205S07.grid27=Handling officer
L1205S07.grid28=Nature
L1205S07.grid29=Unified Business Number
L1205S07.grid30=Customer Name
L1205S07.grid31=Signature Date
L1205S07.grid32=Case No.
L1205S07.grid33=Credit Limit Serial Number
L1205S07.grid34=Document Status
L1205S07.grid35=Applied Credit Limit
L1205S07.grid36=Credit Line
L1205S07.grid1=Standalone
L1205S07.grid2=Consolidated
L1205S07.grid37=Principal Borrower
L1205S07.grid38=Date Created
L1205S07.grid39=Document Status
L1205S07.grid40=Approval date
L1205S07.grid41=Document Creator
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u8b66\u544a\u8996\u7a97\u8a0a\u606f\u5167\u5bb9
#==================================================
L1205S07.alert1=No data selected
L1205S07.alert2=You have not selected any data; please select at least one record...
L1205S07.alert3=Document "Not" canceled
L1205S07.alert4=Document "Not" recovered
L1205S07.alert5=Document "Not" deleted
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u78ba\u8a8d\u8996\u7a97\u8a0a\u606f\u5167\u5bb9
#==================================================
L1205S07.confirm1=Are you sure to cancel the list?
L1205S07.confirm2=Are you sure to Resume Printing Related Account List?
L1205S07.confirm3=Dada can not be recovered once deleted; are you sure to delete "All" documents pertaining to related parties?
#==================================================
# \u5f80\u4f86\u5f59\u7e3dThickBox\u6309\u9215\u8207\u6a19\u984c\u540d\u7a31
#==================================================
L1205S07.thickbox1=Confirm
L1205S07.thickbox2=Cancel
L1205S07.thickbox3=Save
L1205S07.thickbox4=Delete
L1205S07.thickbox5=Close
L1205S07.thickbox6=Borrower Selection
L1205S07.thickbox7=Exit
L1205S07.thickbox8=Please input the starting/ending year & month for your inquiry (YYYYMM)
L1205S07.thickbox9=Register the related account to the bank's banking transactions
L1205S07.thickbox10=Re-import
L1205S07.thickbox11=Add Printed Related Account List
L1205S07.thickbox12=Capital adequacy ratio
L1205S07.thickbox13=Calculations
L1205S07.thickbox14=Stakeholders Credit Term Reference
L1205S07.thickbox15=Credit Facility Report Selection
L1205S07.thickbox16=Credit Assessment Report Selection
L1205S07.thickbox17=Summary Information Sheet Selection
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u8cc7\u6599\u57fa\u671f\u7d30\u90e8\u540d\u7a31
#==================================================
L1205S07.form1=Average Outstanding Balance
L1205S07.form2=Available Credit Line
L1205S07.form3=Transaction Volume
L1205S07.form4=Entrusted Asset Balance
L1205S07.form5=Fee Income
L1205S07.form6=Number of accounts
L1205S07.form7=Amount OF Credit Card Purchase
L1205S07.form8=Whether co-brand cards exist
L1205S07.form9=(including EDI)
#==================================================
# \u5f80\u4f86\u5f59\u7e3dLegend\u540d\u7a31
#==================================================
L1205S07.subindex1=Overall Evaluation & Reason To Continue Relationship
L1205S07.subindex2=Risk Weight Calculated Detail
L1205S07.subindex3=Related Account's Banking Transactions With the Bank
L1205S07.subindex4=Related Account
L1205S07.subindex5=Register the related account to the bank's banking transactions
L1205S07.subindex6=Credit Term Reference
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u6309\u9215\u540d\u7a31
#==================================================
L1205S07.btn1=Import Overall Credit Assessment
L1205S07.btn2=Print Preview Overall Evaluation & Reason To Continue Relationship
L1205S07.btn3=Import Credit Facility Report
L1205S07.btn4=Write Back Credit Facility Report
L1205S07.btn5=Print This Page
L1205S07.btn6=Add Printed Related Account List
L1205S07.btn7=Cancel Printing Related Account List
L1205S07.btn8=Resume Printing Related Account List
L1205S07.btn9=Import Related Account Banking Summary
L1205S07.btn10=Calculate Group/Related Party Total
L1205S07.btn11=Print
L1205S07.btn12=Delete All Related Account Banking Data
L1205S07.btn13=Inquiry
L1205S07.btn14=Re-import
L1205S07.btn15=Import Reference Customer's Credit Limit Data
L1205S07.btn16=Clear All
L1205S07.btn17=Generate List
L1205S07.btn18=Delete List
L1205S07.btn19=Input data asked by HKMA
L1205S07.btn20=Import data asked by HKMA
L1205S07.btn24=\u5f15\u9032\u5f80\u4f86\u5f59\u7e3d\u8aaa\u660e
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u7d30\u90e8\u8cc7\u6599\u5176\u4ed6\u540d\u7a31
#==================================================
L1205S07.other1=\u203bPlease input the reason for change of lending term, industry prospect, overall business and financial evaluation, banking transactions, feasibility and rationality for continuing banking relationship...etc.
L1205S07.other2=Unit: thousand dollars
L1205S07.other3=Description: 1. Non-credit Guaranteed Cases: for every $1 billion of exposure, capital adequacy is calculated at 0.7/10000. (includes accounts receivable financing; the risk weight can be based on buyer's credit rating subject to case-by-case approval, and can be used to calculate exposure)
L1205S07.other4=Field Description: A. Credit Limit: multiple 20% to import negotiation limits. Derivatives need to be multiplied with proper risk conversion ratios.
L1205S07.other5=Description: 1. The data covers all bank customers. 2. If certain groups/companies do not appear as they should when performing "Import Related Account Banking Summary", please go to the e-Loan system and add accordingly by inputting a Modification Notice. 3. If certain related companies do not appear as they should when performing "Import Related Account Banking Summary", please go to the e-Loan system and add accordingly. 4. The source of related company data is the same as Item 9 under the Case Report's Description tab; it only covers controlling and cross-investment relationships. 5. Salary account data was maintained in the data warehouse system since 2011/02. 6. The data warehouse updates last month's banking transactions on the 10th each month (which is different to DWC350, which updates immediately at the time data becomes available).
L1205S07.other6=Please input information on the borrower, representative, group, and related companies into Related Account's Banking Transactions With the Bank.
L1205S07.other7=The credit limit serial number is used as mean of control, and is not shown in printed reports.
L1205S07.other8=Importing is allowed only for within-authority Case Reports; for other types of Case Reports, please input manually
L1205S07.other9=If the Credit Facility Report specifies the repayment period under Other Terms & Conditions, the system will consolidate Other Terms & Conditions into the Tenor field
L1205S07.other10=Please select the Credit Facility Report from which to generate the List
L1205S07.other11=Please select 1 Credit Facility Report

L1205S07.HKMA_1=Group's/borrower's credit relationship
L1205S07.HKMA_2=Exposure of borrower's industry
L1205S07.HKMA_3=Exception or deviation from lending policy/guidelines and thus requiring the justification for the loan recommendation by credit officers
L1205S07.HKMA_4=Site visit(date and type of visits conducted such as office/factory visits)
L1205S07.HKMA_5=Legal search (result of search and implications)
L1205S07.HKMA_6=Internal loan grading and loan classification(under HKMA guidelines) for credit facility renewal
L1205S07.HKMA_7=Date of last credit facility review
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u6a19\u984c\u540d\u7a31
#==================================================
L1205S07.index1=Credit Guarantee
L1205S07.index2=Not Credit Guaranteed
L1205S07.index3=(C denotes credit guarantee)
L1205S07.index4=Related Companies Total
L1205S07.index5=Group Total
L1205S07.index6=Current Deposit
L1205S07.index7=Outstanding Balance
L1205S07.index8=Credit Limit
L1205S07.index9=Contribution
L1205S07.index10=Item
L1205S07.index11=Principal Borrower
L1205S07.index12=Unit: TWD1,000
L1205S07.index13=Data Inquiry Period
L1205S07.index14=Please select a customer
L1205S07.index15=UBN (excluding repeated serial number)
L1205S07.index16=Customer Status
L1205S07.index17=No. Of Records
L1205S07.index18=Amount
L1205S07.index19=Compiling In Progress
L1205S07.index20=Document Status
L1205S07.index21=Starting Year/Month
L1205S07.index22=Ending Year/Month
L1205S07.index23=Document Status
L1205S07.index24=Compiling In Progress
L1205S07.index25=Borrower
L1205S07.index26=Reference Borrower
L1205S07.index27=Main Collateral
L1205S07.index28=Interest Rate
L1205S07.index29=Tenor
L1205S07.index30=Year
L1205S07.index31=Months
L1205S07.createBY1=System Generated
L1205S07.createBY2=Manually Generated
L1205S07.prtFlag1=Print
L1205S07.prtFlag2=Do Not Print
#==================================================
# \u5f80\u4f86\u5f59\u7e3dradio\u540d\u7a31
#==================================================
L1205S07.radio1=DBU Customer
L1205S07.radio2=OBU Customer
L1205S07.radio3=Overseas Peer
L1205S07.radio4=Overseas Customer
L1205S07.radio5=Yes
L1205S07.radio6=No
L1205S07.radio7=Separate Pages
L1205S07.radio8=Combine Into Same Page
#==================================================
# \u5f80\u4f86\u5f59\u7e3dcheckbox\u540d\u7a31
#==================================================
L1205S07.checkbox1=Borrower
L1205S07.checkbox2=Borrower's Representative
L1205S07.checkbox3=Group Total
L1205S07.checkbox4=Related Companies Total
L1205S07.checkbox5=Group company
L1205S07.checkbox6=Related Company
#==================================================
# \u8cc7\u672c\u9069\u8db3\u7387\u5f71\u97ff\u6578\u8cc7\u6599\u6a94
#==================================================
L120S03A.oid=oid
L120S03A.mainId=Document Number (Case Report)
L120S03A.cntrMainId=Document Number (Credit Facility Report)
L120S03A.cntrNo=Credit Limit Serial Number
L120S03A.crdFlag=Credit Guaranteed/Not Credit Guaranteed
L120S03A.applyAmt=Original Applied Credit Limit
L120S03A.fcltAmt=Credit Limit
L120S03A.collAmt=Eligible Collateral Deduction
L120S03A.rskRatio=Risk Weight (apply remark for guarantor)
L120S03A.rskAmt1=Exposure Net Of Risk Mitigant
L120S03A.rskr1=Risk Weight Net Of Risk Mitigant
L120S03A.camt1=Utilized Capital (not required)
L120S03A.bisr1=% To Capital Adequacy Ratio
L120S03A.costr1=Funding Cost %
L120S03A.crdRatio=Credit Guarantee Percentage
L120S03A.rskMega=Bank's exposure A*(1-B) * B'
L120S03A.rskCrd=Credit Guaranteed Exposure (20% risk weight)
L120S03A.rskAmt2=Total Exposure Net Of Risk Mitigant
L120S03A.rskr2=Risk Weight Net Of Risk Mitigant
L120S03A.camt2=Utilized Capital (not required)
L120S03A.bisr2=% To Capital Adequacy Ratio
L120S03A.costr2=Funding cost for the credit limit (H)
L120S03A.creator=Originator's ID
L120S03A.createTime=Date Created
L120S03A.updater=Modifier's ID
L120S03A.updateTime=Date Of Change
L120S03A.crdRskRatio=Risk Weight(Not Credit Guaranteed)

#==================================================
# \u95dc\u4fc2\u6236\u65bc\u672c\u884c\u5404\u9805\u696d\u52d9\u5f80\u4f86\u6a94
#==================================================
L120S04A.oid=oid
L120S04A.mainId=Document Number
L120S04A.createBY=Document Generation Method
L120S04A.custId=Unified Business Number
L120S04A.dupNo=Repeat Serial No.
L120S04A.custName=Account name please select any Language input
L120S04A.typCd=Region/Department
L120S04A.custRelation=Relationship With The Borrower
L120S04A.prtFlag=Case Report Printing Remarks
L120S04A.queryDateS=Data Inquiry Start Date
L120S04A.queryDateE=Data Inquiry End Date
L120S04A.itemName=Business Transaction
L120S04A.memo=Base Period
L120S04A.dep=Deposit
L120S04A.depMemo=Deposits - Base Period
L120S04A.depTime=Current Deposit
L120S04A.depFixed=Fixed deposits
L120S04A.loan=Loan
L120S04A.loanQMemo=Loans - Credit Limit - Base Period
L120S04A.loanQuota=Balance
L120S04A.loanABMemo=Loans - Average Balance - Base Period
L120S04A.loanAvgBal=Average Outstanding Balance
L120S04A.loanAvgRate=Average Utilization
L120S04A.exchg=Foreign Currency
L120S04A.exchgMemo=Foreign Currency - Base Period
L120S04A.exchgImp=Import (transactions/amount)
L120S04A.exchgImpRec=Foreign Currency - Import (transactions)
L120S04A.exchgImpAmt=Foreign Currency - Import (amount)
L120S04A.exchgExp=Export (transactions/amount)
L120S04A.exchgExpRec=Foreign Currency - Export (transactions)
L120S04A.exchgExpAmt=Foreign Currency - Export (amount)
L120S04A.exchgOut=Outward Remittance (transactions/amount)
L120S04A.exchgOutRec=Foreign Currency - Outward Remittance (transactions)
L120S04A.exchgOutAmt=Foreign Currency - Outward Remittance (amount)
L120S04A.exchgIn=Inward Remittance (transactions/amount)
L120S04A.exchgInRec=Foreign Currency - Inward Remittance (transactions)
L120S04A.exchgInAmt=Foreign Currency - Inward Remittance (amount)
L120S04A.der=Derivative
L120S04A.derMemo=Derivative - Base Period
L120S04A.derOption=Options
L120S04A.derRateExchg=Interest Rate Swap
L120S04A.derCCS=Cross Currency Swap
L120S04A.derDraft=Derivative - Foreard Exchange
L120S04A.derSWAP=Forward Exchange (includes SWAP)
L120S04A.trust=Trust
L120S04A.trustMemo=Trust - Base Period
L120S04A.trustBond=Domestic/Offshore Funds & Bonds
L120S04A.trustFund=Fund Custody
L120S04A.trustSetAcct=Central Depository
L120S04A.trustSecurities=Trust - Securities Trust
L120S04A.trustREITs=Trust - Real Estate Trust
L120S04A.trustWelDep=Trust - Welfare & Savings Trust
L120S04A.trustOther=Other Trust
L120S04A.wealth=Wealth Management
L120S04A.wealthMemo=Wealth Management - Base Period
L120S04A.wealthTrust=Trust
L120S04A.wealthInsCom=Insurance Commission
L120S04A.wealthInvest=Dual Currency Investment
L120S04A.salary=Number Of Mortgage/Consumer Loan Accounts
L120S04A.salaryMemo=Salary Account - Base Period
L120S04A.salaryRec=Number Of Salary Accounts
L120S04A.salaryFixed=Number Of Fixed Deposit Accounts
L120S04A.salary=Number Of Mortgage/Consumer Loan Accounts
L120S04A.salaryMortgage=Mortgage
L120S04A.salaryConsumption=Consumer Loan
L120S04A.salaryCard=Number Of Credit Card Holders
L120S04A.salaryNetwork=Number Of Personal Internet Banking Users
L120S04A.cardComMemo=Cards - Corporate Card - Base Period
L120S04A.card=Cards
L120S04A.cardCommercial=Corporate Card
L120S04A.cardNoneCommercial=Non-business cards
L120S04A.cardNoneCommercialMemo=Cards - Non-business cards - Base Period
L120S04A.cardBrnMemo=Cards - Co-brand Card - Base Period
L120S04A.cardCoBranded=Co-brand Card
L120S04A.GEB=GEB
L120S04A.GEBMemo=GEB - NTD/Foreign Currency - Base Period
L120S04A.GEBTWDRec=Number Of NTD Transactions
L120S04A.GEBOTHRec=Number Of Foreign Currency Transactions
L120S04A.GEBLCMemo=GEB - LC - Base Period
L120S04A.GEBLCRec=Number Of LC Transactions
L120S04A.profitMemo=Profit Contribution - Base Period
L120S04A.profit=Profit Contribution
L120S04A.creator=Originator's ID
L120S04A.createTime=Date Created
L120S04A.updater=Modifier's ID
L120S04A.updateTime=Date Of Change

#==================================================
# \u5229\u5bb3\u95dc\u4fc2\u4eba\u6388\u4fe1\u689d\u4ef6\u5c0d\u7167\u8868\u4e3b\u6a94
#==================================================
L120S06A.oid=oid
L120S06A.mainId=Document Number
L120S06A.custId=Borrower's UBN
L120S06A.dupNo=Borrower's Repeated Serial Number
L120S06A.cntrNo=Borrower's Credit Limit Serial Number
L120S06A.custName=Borrower
L120S06A.custId2=Reference Borrower's UBN
L120S06A.dupNo2=Reference Borrower's Repeated Serial Number
L120S06A.cntrNo2=Reference Borrower's Credit Limit Serial Number
L120S06A.custName2=Reference Borrower
L120S06A.proPerty=Nature
L120S06A.currentApplyCurr=Applied Credit Limit - Currency
L120S06A.currentApplyAmt=Applied Credit Limit - Amount
L120S06A.lnSubject=Credit Category
L120S06A.purpose=Use Of Fund
L120S06A.gutPercent=Guarantee Percentage
L120S06A.payDeadline=Tenor
L120S06A.guarantor=Guarantor
L120S06A.guarantorMemo=Guarantor Remarks
L120S06A.proPerty2=Nature
L120S06A.currentApplyCurr2=Applied Credit Limit - Currency
L120S06A.currentApplyAmt2=Applied Credit Limit - Amount
L120S06A.lnSubject2=Credit Category
L120S06A.purpose2=Use Of Fund
L120S06A.gutPercent2=Guarantee Percentage
L120S06A.payDeadline2=Tenor
L120S06A.guarantor2=Guarantor
L120S06A.guarantorMemo2=Guarantor Remarks
L120S06A.printMode=Print Mode
L120S06A.creator=Originator's ID
L120S06A.createTime=Date Created
L120S06A.updater=Modifier's ID
L120S06A.updateTime=Date Of Change

#==================================================
# \u5229\u5bb3\u95dc\u4fc2\u4eba\u6388\u4fe1\u689d\u4ef6\u5c0d\u7167\u8868\u660e\u7d30\u6a94
#==================================================
L120S06B.oid=oid
L120S06B.mainId=Document Number
L120S06B.custId=Borrower's UBN
L120S06B.dupNo=Borrower's Repeated Serial Number
L120S06B.cntrNo=Borrower's Credit Limit Serial Number
L120S06B.type=Category
L120S06B.itemType=Item Category
L120S06B.itemDscr=Item Description
L120S06B.creator=Originator's ID
L120S06B.createTime=Date Created
L120S06B.updater=Modifier's ID
L120S06B.updateTime=Date Of Change

#==================================================
# J-104-0138-001 \u4f01\u91d1\u6388\u4fe1\u65b0\u589e\u8d64\u9053\u539f\u5247
#==================================================
L1205S07.subindex7=Related assessment
L120M01I.equatorPrinciples=Social and environmental risks Assessment
L120M01I.applyEquatorPrinciples=Apply Related assessment
L120M01I.hasDuty=Whether the customer does not carry out environmental protection and corporate social responsibility
L120M01I.hasDeeds=Have significant specificfacts, if so, please briefly
L120M01I.HASDUTY_Y=Yes\u25a0\uff1bNo\u25a1
L120M01I.HASDUTY_N=Yes\u25a1\uff1bNo\u25a0
L120M01I.HASDEEDS_Y=Exist\u25a0\uff1bNone\u25a1
L120M01I.HASDEEDS_N=Exist\u25a1\uff1bNone\u25a0
L120M01I.NEXTCHAPTERNUM_4=4
L120M01I.NEXTCHAPTERNUM_5=5
L120M01I.NEXTCHAPTERNUM_6=6
L120M01I.NEXTCHAPTERNUM_7=7
L120M01I.NEXTCHAPTERNUM_11=11
L120M01I.NEXTCHAPTERNUM_12=12
L120M01I.message01=Tab Overall Evaluation(Case Report-General)\u3001Description(Case Report-Others)\u300cRelated assessment-Social and environmental risks (the Equator Principles) Assessment\u300dStill not entered the field completed

# J-108-0166 \u4f01\u91d1\u6388\u4fe1\u6539\u7248\u8d64\u9053\u539f\u5247
L120M01I.hasD1=Whether the customer or its controlling affiliated company has failed to protect the environment and has been punished
L120M01I.hasD2=Whether the customer or its controlled affiliated company has failed to fulfill its corporate social responsibilities in the past five years and endangered social welfare activities or incidents under investigation
L120M01I.hasD3=Whether the customer or its controlled affiliated company has occurred in the past five years involving human rights violations or incidents
L120M01I.01=\uff081\uff09
L120M01I.01_1=Mega ID\uff1a
L120M01I.01_2=Name\uff1a
L120M01I.02=\uff082\uff09
L120M01I.02_1=Please describe briefly\uff1a
L120M01I.02_D1_0=Violation\uff1a
L120M01I.02_D2_0=Have been involved in the following activities or incidents that endanger social welfare and have been investigated\uff1a
L120M01I.02_D3_0=Has been involved in the following human rights violations or incidents\uff1a
L120M01I.03=\uff083\uff09
L120M01I.03_D1_0=Current improvement\uff1a

# J-109-0370 \u76f8\u95dc\u8a55\u4f30\u6539\u7248
L120M01I.hasESG=Whether customer has an external ESG rating
L120M01I.esgAgency=Rating agency
L120M01I.esgGrade=Rating
L120M01I.hasCnLim=Whether borrower or account of related party has factories in China affected by the \u201cpollution restriction order\u201d

# J-112-0337 \u914d\u5408\u6388\u5be9\u8655\uff0c\u5728\u7c3d\u5831\u66f8\u53ca\u5e38\u8463\u6703\u63d0\u6848\u7a3f\uff0c\u793e\u6703\u8cac\u4efb\u8207\u74b0\u5883\u98a8\u96aa\u8a55\u4f30\u5927\u9805\u4e2d\uff0c\u589e\u52a0\u672c\u884cESG\u98a8\u96aa\u8a55\u7d1a\u7d50\u679c
L120M01I.finalAssessment=ESG RISK RATING
L120M01I.finalAssessmentH=\u5ba2\u6236\u662f\u5426\u70ba\u672c\u884cESG\u98a8\u96aa\u8a55\u7d1a\u9ad8\u98a8\u96aa\u8005
L120M01I.finalAssessmentM=\u5ba2\u6236\u662f\u5426\u70ba\u672c\u884cESG\u98a8\u96aa\u8a55\u7d1a\u4e2d\u98a8\u96aa\u8005
L120M01I.finalAssessmentL=\u5ba2\u6236\u662f\u5426\u70ba\u672c\u884cESG\u98a8\u96aa\u8a55\u7d1a\u4f4e\u98a8\u96aa\u8005
L120M01I.finalAssessmentRA=\u5ba2\u6236\u662f\u5426\u70ba\u672c\u884cESG\u98a8\u96aa\u8a55\u7d1a\u4e0d\u4e88\u627f\u4f5c\u8005
L120M01I.finalAssessmentNA=\u5ba2\u6236\u662f\u5426\u6709\u672c\u884cESG\u98a8\u96aa\u8a55\u7d1a
L120M01I.finalAssessmentNone=\u5ba2\u6236\u662f\u5426\u6709\u672c\u884cESG\u98a8\u96aa\u8a55\u7d1a
L120S01Q.qCesEsgDate=\u5f15\u9032\u65e5
L120S01Q.isHighEnv=Whether the customer is in an industry with high environmental and social impact.
L120S01Q.isHighCarbonEms=Whether the customer is a high-carbon emission industry.
L120S01Q.isDeCarbonEms=Whether the customer is subject to the decarbonization strategy.
L120S01Q.isSustain=Whether the client has put forward clear evidence and plans for sustainable transition, or whether the use of funds is for sustainable purposes.
L120S01Q.sustainMemo=\u8acb\u7c21\u8ff0\u76f8\u95dc\u4f50\u8b49\u3001\u8a08\u756b\u5167\u5bb9\u53ca\u53ef\u8861\u91cf\u6548\u76ca\u4e4b\u6307\u6a19\uff0c\u6216\u65bc\u7d9c\u5408\u8a55\u4f30\u8aaa\u660e
L120S01Q.sbtiIsCommited=SBT\u767b\u9304\u72c0\u614b
L120S01Q.sbtiApproveDate=\u8abf\u67e5\u8868\u65e5\u671f
L120S01Q.sustainEval=\u6c38\u7e8c\u7a0b\u5ea6\u81ea\u8a55\u7d50\u679c
L120S01Q.sustainEvalStr=\u9069\u7528\u6c38\u7e8c\u7d93\u6fdf\u6d3b\u52d5\u8a8d\u5b9a\u53c3\u8003\u6307\u5f15\u4e4b\u7d93\u6fdf\u6d3b\u52d5\u5171{0}\u9805\uff0c\u5176\u71df\u6536\u5360\u6bd4\u7d04{1}%\uff0c\u5176\u4e2d{2}\u9805\u70ba\u7b26\u5408\u672c\u6307\u5f15\u4e4b\u7d93\u6fdf\u6d3b\u52d5\uff0c\u5360\u9069\u7528\u672c\u6307\u5f15\u7d93\u6fdf\u6d3b\u52d5\u4e4b\u71df\u6536\u6bd4\u91cd\u70ba{3}%\u3002
L120S01Q.sustainEvalStr01=\u9069\u7528\u6c38\u7e8c\u7d93\u6fdf\u6d3b\u52d5\u8a8d\u5b9a\u53c3\u8003\u6307\u5f15\u4e4b\u7d93\u6fdf\u6d3b\u52d5\u5171
L120S01Q.sustainEvalStr02=\u9805\uff0c\u5176\u71df\u6536\u5360\u6bd4\u7d04
L120S01Q.sustainEvalStr03=%\uff0c\u5176\u4e2d
L120S01Q.sustainEvalStr04=\u9805\u70ba\u7b26\u5408\u672c\u6307\u5f15\u4e4b\u7d93\u6fdf\u6d3b\u52d5\uff0c\u5360\u9069\u7528\u672c\u6307\u5f15\u7d93\u6fdf\u6d3b\u52d5\u4e4b\u71df\u6536\u6bd4\u91cd\u70ba
L120S01Q.sustainEvalStr05=%\u3002
L120S01Q.msg01=Please note that due diligence should be carried out and prudently assessed, and there should be no material adverse impact on sustainable development after evaluation, or conditional transactions should be implemented (such as incorporating ESG risk reduction improvement plans into the conditions or commitments of credit) to reduce the adverse impact on ESG, and an exceptional management mechanism should be established
L120S01Q.msg02=Customers in the coal and unconventional oil and gas enterprises are subject to control. In addition to confirming clear evidence and plans for sustainable transition, or using funds for sustainable development, we should avoid taking on new or increased loan cases, gradually reduce credit exposure and reduce it to zero by the end of 2040 at the latest.
L120S01Q.noData=\u67e5\u7121\u8cc7\u6599
L120S01Q.other1=\u5099\u8a3b\uff1a<br/>1.\u91dd\u5c0d\u7b26\u5408\u6bd4\u504f\u4f4e\u4e4b\u5ba2\u6236\uff0c\u61c9\u8207\u5ba2\u6236\u6e9d\u901a\u4e26\u77ad\u89e3\u72c0\u6cc1\uff0c\u4ee5\u5354\u52a9\u4f01\u696d\u81ea\u6211\u6aa2\u8996ESG\u6c38\u7e8c\u8a8d\u5b9a\u689d\u4ef6\uff0c\u9032\u884c\u5177\u9ad4\u6539\u5584\u6216\u8f49\u578b\u8a08\u756b\uff0c\u5e36\u52d5\u4f01\u696d\u6c38\u7e8c\u767c\u5c55\u53ca\u6e1b\u78b3\u8f49\u578b\u3002<br/>2.\u53c3\u8003\u8cc7\u6599\uff1a\u8a73\u300c\u6c38\u7e8c\u7d93\u6fdf\u6d3b\u52d5\u8a8d\u5b9a\u53c3\u8003\u6307\u5f15\u300d\u4e4b\u300c\u5c0d\u6c23\u5019\u8b8a\u9077\u6e1b\u7de9\u5177\u5be6\u9ad4\u8ca2\u737b\u4e4b\u6280\u8853\u7be9\u9078\u6a19\u6e96\u300d\u3002
#==================================================
# J-106-0213-001 \u4f01\u696d\u8aa0\u4fe1\u7d93\u71df\u8a55\u4f30  
#==================================================
L120M01I.integrityAssessment=Customer's ethical management Assessment
L120M01I.hasLegality=Whether evaluated the legality and ethical management policy of the Customers.
L120M01I.hasBadFaith=The customers have a record of involvement in unethical conduct,if so,please briefly(such as the customer's business operations are located in a country with a high risk of corruption, the business operated by the customer is in an industry with a high risk of bribery,the customer has a record of involvement in unethical conduct such as bribery or illegal political contributions).
L120M01I.hasBadFaithVer4=The customers have a record of involvement in unethical conduct.
L120M01I.NEXTCHAPTERNUM_13=13
L120M01I.message02=Tab Overall Evaluation(Case Report-General)\u3001Description(Case Report-Others)\u300cRelated assessment-Customer's ethical management Assessment\u300dStill not entered the field completed

#==================================================
# J-109-0370 \u76f8\u95dc\u8a55\u4f30\u6539\u7248
#==================================================
L120S01Q.message01=Main borrower is not found
L120S01Q.message02=Relevant assessment when the main borrower is not found
L120S01Q.message03=The relevant assessment of the main borrower has not been completed
L120S01Q.message04=The "Relevant Assessment" of the comprehensive evaluation (report form-general) and description (report form-others) tabs have not been completed
L120S01Q.message05=The "Relevant Assessment" of the comprehensive evaluation (report form-general) and description (report form-others) tabs is inconsistent with the borrower
L120S01Q.message06=The "Relevant Assessment" of the comprehensive evaluation (report form-general) and description (report form-others) tabs has not passed the inspection

#J-113-0442 \u4f01\u91d1\u7c3d\u5831\u66f8\u300c\u793e\u6703\u8cac\u4efb\u8207\u74b0\u5883\u98a8\u96aa\u8a55\u4f30\u300d\u65b0\u589e\u5167\u5bb9
L120S01Q.epsDesc=\u672c\u6848\u662f\u5426\u7b26\u5408\u8d64\u9053\u539f\u5247\u6388\u4fe1\u6848\u4ef6
L120S01Q.industryNo05Desc=\u672c\u6848\u662f\u5426\u70ba\u300c\u542b\u975e\u518d\u751f\u80fd\u6e90\u96fb\u529b\u300d\u8d64\u9053\u539f\u5247\u6848\u4ef6(\u672c\u884c\u7981\u6b62\u627f\u4f5c)
L120S01Q.industryNo06Desc=\u672c\u6848\u662f\u5426\u70ba\u300c\u7d14\u518d\u751f\u80fd\u6e90\u96fb\u529b\u300d\u8d64\u9053\u539f\u5247\u6848\u4ef6
L120S01Q.envAndSociRiskLvl=\u74b0\u5883\u793e\u6703\u98a8\u96aa\u5206\u7d1a
