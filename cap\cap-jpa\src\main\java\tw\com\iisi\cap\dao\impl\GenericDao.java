/*
 * GenericDao.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.dao.impl;

import java.io.Serializable;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.persistence.EntityGraph;
import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Order;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Selection;

import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tw.com.iisi.cap.dao.IGenericDao;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.Page;

/**
 * <pre>
 * Generic Dao
 * 共用Dao接口
 * </pre>
 * 
 * @since 2011/6/20
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/7,iristu,new
 *          <li>2011/6/20,iristu,增加findPage
 *          <li>2010/10/05,sunkistwang,update SearchMode.OR, SearchMode.AND
 *          <li>2015/10/06,Mike,add distinct query
 *          </ul>
 */
public abstract class GenericDao<T, PK extends Serializable> implements IGenericDao<T> {

    /**
     * 參數泛型類型
     */
    protected Class<T> type;
    protected Logger logger;

    /**
     * 取得存取 database 用的 {@link EntityManager} 物件
     * 
     * @return
     */
    public abstract EntityManager getEntityManager();

    /**
     * 建構子, 實體化Dao接口
     */
    @SuppressWarnings("unchecked")
    public GenericDao() {
        try {
            type = (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
        } catch (ClassCastException e) {
            Class<T> clazz = (Class<T>) getClass().getGenericSuperclass();
            type = (Class<T>) ((ParameterizedType) clazz.getGenericSuperclass()).getActualTypeArguments()[0];
        }
        logger = LoggerFactory.getLogger(getClass());
    }// ;

    /**
     * Insert.<br>
     * 
     * @param entry
     *            the entry
     */
    @Override
    public void save(T entity) {
        Validate.notNull(entity, "The entity to save cannot be null element");
        if (!getEntityManager().contains(entity)) {
            getEntityManager().persist(entity);
        }
    }// ;

    /*
     * Multiple Insert.
     * 
     * @see tw.com.iisi.cap.dao.IGenericDao#save(java.util.List)
     */
    @Override
    public void save(List<T> entries) {
        for (T entity : entries) {
            Validate.notNull(entity, "The List must not contain null element");
            save(entity);
        }
    }// ;

    /**
     * update entity
     * 
     * @param entity
     */
    public void merge(T entity) {
        Validate.notNull(entity, "The entity to save cannot be null element");
        getEntityManager().merge(entity);
    }// ;

    /*
     * Delete entity.
     * 
     * @see tw.com.iisi.cap.dao.IGenericDao#delete(java.lang.Object)
     */
    @Override
    public void delete(T entity) {
        if (getEntityManager().contains(entity)) {
            getEntityManager().remove(entity);
        } else {
            // could be a delete on a transient instance

            if (getPrimaryKey(entity) != null) {
                T entityRef = getEntityManager().getReference(type, getPrimaryKey(entity));

                if (entityRef != null) {
                    getEntityManager().remove(entityRef);
                }
            }
        }
    }// ;

    /*
     * Multiple entities delete
     * 
     * @see tw.com.iisi.cap.dao.IGenericDao#delete(java.util.List)
     */
    @Override
    public void delete(List<T> entries) {
        for (T entity : entries) {
            T modelRef = find(entity);

            if (modelRef != null) {
                delete(modelRef);
            } else if (logger.isWarnEnabled()) {
                logger.warn("It is not present in the database: " + entity.toString());
            }
        }
    }// ;

    /**
     * Find.
     * 
     * @param pk
     *            the oid
     * 
     * @return the t
     */
    public T find(Serializable pk) {
        Serializable checkedPk = IDataObject.getOidWithFullLength(pk, type);
        if (checkedPk == null) {
            return null;
        }
        return getEntityManager().find(type, checkedPk);
    }// ;

    /**
     * 搜尋主鍵
     * 
     * @param model
     *            模型
     * @return
     */
    public Serializable getPrimaryKey(T model) {
        if (model instanceof IDataObject) {
            return (Serializable) ((IDataObject) model).getOid();
        } else {
            return null;
        }
    }

    /*
     * Find.
     * 
     * @see tw.com.iisi.cap.dao.IGenericDao#find(java.lang.Object)
     */
    @Override
    public T find(T entity) {
        Serializable pk = getPrimaryKey(entity);
        if (pk == null) {
            return null;
        }
        return (T) getEntityManager().find(type, pk);
    }// ;

    /*
     * find Unique or none
     * 
     * @see tw.com.iisi.cap.dao.IGenericDao#findUniqueOrNone(tw.com.iisi.cap.dao.utils.ISearch)
     */
    @Override
    public T findUniqueOrNone(ISearch search) {
        search.setFirstResult(0).setMaxResults(1);
        List<T> models = find(getType(), search);
        if (models != null && !models.isEmpty()) {
            return models.iterator().next();
        }
        return null;
    }// ;

    /*
     * 多筆搜尋清單
     * 
     * @see tw.com.iisi.cap.dao.IGenericDao#list(int, int)
     */
    @Override
    public Iterator<T> list(int first, int count) {
        ISearch search = createSearchTemplete();
        search.setFirstResult(first).setMaxResults(count);
        return createQuery(getType(), search).getResultList().iterator();
    }// ;

    /*
     * 取得資料筆數
     * 
     * @see tw.com.iisi.cap.dao.IGenericDao#count(tw.com.iisi.cap.dao.utils.ISearch)
     */
    @Override
    public int count(ISearch search) {
        return count(getType(), search);
    }

    /*
     * 依照搜尋設定查找
     * 
     * @see tw.com.iisi.cap.dao.IGenericDao#find(tw.com.iisi.cap.dao.utils.ISearch)
     */
    @Override
    public List<T> find(final ISearch search) {
        return find(getType(), search);
    }// ;

    /**
     * 查詢頁的資料
     */
    public Page<T> findPage(ISearch search) {
        return findPage(getType(), search);
    }// ;

    /**
     * find by SearchSetting
     * 
     * @param search
     *            SearchSetting
     * @return List<T>
     */
    public <S> List<S> find(Class<S> clazz, final ISearch search) {
        ISearch thisSearch = (search != null) ? search : createSearchTemplete();
        if (thisSearch.isDistinct()) {
            CriteriaBuilder builder = getEntityManager().getCriteriaBuilder();
            CriteriaQuery<Object[]> query = builder.createQuery(Object[].class);
            Root<S> root = query.from(clazz);
            query = createDistinctQuery(root, query, clazz, thisSearch);
            TypedQuery<Object[]> tquery = applyPaginationAndOrderToCriteria(root, query, builder, thisSearch);
            List<Object[]> l = tquery.getResultList();
            List<S> list = new ArrayList<>();
            S obj;
            if (!l.isEmpty()) {
                if (l.get(0) instanceof Object[]) {
                    for (Object[] o : l) {
                        obj = getS(clazz, o, search);
                        list.add(obj);
                    }
                } else {
                    for (Object o : l) {
                        obj = getS(clazz, o, search);
                        list.add(obj);
                    }
                }
            }
            return list;
        } else {
            return createQuery(clazz, search).getResultList();
        }
    }// ;

    private <S> S getS(Class<S> clazz, Object o, final ISearch search) {
        S obj = null;
        try {
            obj = clazz.newInstance();

            String distinctColumn[] = search.getDistinctColumn();
            for (int i = 0; i < distinctColumn.length; i++) {
                Field field = null;
                try {
                    field = clazz.getDeclaredField(distinctColumn[i]);
                } catch (java.lang.NoSuchFieldException e) {
                    field = clazz.getSuperclass().getDeclaredField(distinctColumn[i]);
                }
                field.setAccessible(true);
                if (o instanceof Object[]) {
                    field.set(obj, ((Object[]) o)[i]);
                } else {
                    field.set(obj, o);
                }
            }
        } catch (IllegalAccessException e) {
            logger.error(e.getMessage(), e);
        } catch (InstantiationException e) {
            logger.error(e.getMessage(), e);
        } catch (SecurityException e) {
            logger.error(e.getMessage(), e);
        } catch (NoSuchFieldException e) {
            logger.error(e.getMessage(), e);
        }
        return obj;
    }

    /**
     * 取得筆數
     * 
     * @param search
     *            SearchSetting
     * @return Long
     */
    public <S> int count(Class<S> clazz, ISearch search) {
        if (search.isDistinct()) {
            CriteriaQuery<Object[]> query = createDistinctQuery(clazz, search);
            TypedQuery<Object[]> tQuery = getEntityManager().createQuery(query);
            return tQuery.getResultList().size();
        } else {
            CriteriaBuilder builder = getEntityManager().getCriteriaBuilder();
            CriteriaQuery<Long> query = builder.createQuery(Long.class);
            Root<S> root = query.from(clazz);
            query = applySpecificationToCriteria(root, query, builder, search);
            query.select(builder.count(root));
            Long count = getEntityManager().createQuery(query).getSingleResult();
            return count.intValue();
        }

    }// ;

    /**
     * 查詢頁的資料
     */
    public <S> Page<S> findPage(Class<S> clazz, ISearch search) {
        return new Page<S>(find(clazz, search), count(clazz, search), search.getMaxResults(), search.getFirstResult());
    }// ;

    /**
     * 建立查詢
     * 
     * @param <S>
     * @param clazz
     *            類別
     * @param search
     *            search setting
     * @return
     */
    protected <S> TypedQuery<S> createQuery(Class<S> clazz, ISearch search) {
        ISearch thisSearch = (search != null) ? search : createSearchTemplete();
        CriteriaBuilder builder = getEntityManager().getCriteriaBuilder();
        CriteriaQuery<S> query = builder.createQuery(clazz);
        Root<S> root = query.from(clazz);

        query = applySpecificationToCriteria(root, query, builder, thisSearch);
        TypedQuery<S> tquery = applyPaginationAndOrderToCriteria(root, query, builder, thisSearch);
        return tquery;
    }// ;

    /**
     * 建立查詢
     * 
     * @param search
     *            search setting
     * @return
     */
    protected TypedQuery<T> createQuery(ISearch search) {
        ISearch thisSearch = (search != null) ? search : createSearchTemplete();
        CriteriaBuilder builder = getEntityManager().getCriteriaBuilder();
        CriteriaQuery<T> query = builder.createQuery(getType());
        Root<T> root = query.from(getType());

        query = applySpecificationToCriteria(root, query, builder, thisSearch);
        TypedQuery<T> tquery = applyPaginationAndOrderToCriteria(root, query, builder, thisSearch);
        return tquery;
    }// ;

    /**
     * 設定查詢條件
     * 
     * @param <S>
     * @param root
     *            Root
     * @param query
     *            CriteriaQuery
     * @param builder
     *            CriteriaBuilder
     * @param search
     *            SearchSetting
     * @return CriteriaQuery
     */
    @SuppressWarnings({ "rawtypes" })
    protected <S> CriteriaQuery<S> applySpecificationToCriteria(Root root, CriteriaQuery<S> query, CriteriaBuilder builder, ISearch search) {
        if (search.getSearchModeParameters() != null) {
            Predicate[] aryWhere = new Predicate[search.getSearchModeParameters().size()];
            int i = 0;
            for (SearchModeParameter param : search.getSearchModeParameters()) {
                CapSpecifications spec = new CapSpecifications(param);
                aryWhere[i++] = spec.toPredicate(root, query, builder);
            }
            query.where(builder.and(aryWhere));
        }

        return query;
    }// ;

    /**
     * 設定查詢筆數及欄位排列順序
     * 
     * @param root
     *            Root
     * @param query
     *            CriteriaQuery
     * @param builder
     *            CriteriaBuilder
     * @param search
     *            SearchSetting
     * @return TypedQuery
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    protected <S> TypedQuery<S> applyPaginationAndOrderToCriteria(Root root, CriteriaQuery<S> query, CriteriaBuilder builder, ISearch search) {
        // set order criteria if available
        if (search.hasOrderBy()) {

            Map<String, Boolean> orderMap = search.getOrderBy();
            List<Order> orders = new ArrayList<Order>();
            // int i = 0;
            for (Entry<String, Boolean> entry : orderMap.entrySet()) {
                Expression<?> expression = null;
                String[] pathElements = entry.getKey().split("\\.");
                int pathSize = pathElements.length;
                if (pathSize > 1) {
                    Set<Join<?, ?>> joins = root.getJoins();
                    for (Join<?, ?> join : joins) {
                        if (join.getAttribute().getName().equals(pathElements[0])) {
                            expression = join;
                            break;
                        }
                    }
                    if (expression == null) {
                        expression = root.join(pathElements[0]);
                    }
                    expression = ((Path) expression).get(pathElements[pathSize - 1]);
                } else {
                    expression = root.get(entry.getKey());
                }
                orders.add((entry.getValue()) ? builder.desc(expression) : builder.asc(expression));

            }
            query.orderBy(orders);
        }
        TypedQuery<S> tQuery = getEntityManager().createQuery(query);
        if (search != null) {
            // set pagination if needed
            tQuery.setFirstResult(search.getFirstResult());
            tQuery.setMaxResults(search.getMaxResults());
        }
        if (!search.isDistinct()) {
            // use EntityGraph to fix n+1 (or more) selections issue
            // 但不是每個 model 都會設定 EntityGraph，取不到會噴錯，所以 catch exception
            try {
                EntityGraph<?> entityGraph = getEntityManager().getEntityGraph(root.getJavaType().getSimpleName() + "-entity-graph");
                tQuery.setHint("javax.persistence.loadgraph", entityGraph);
            } catch (Exception e) {
                // do nothing
            }
        }
        return tQuery;
    }// ;

    // -------------------------------------------------------
    @SuppressWarnings("rawtypes")
    class CapSpecifications {

        /**
         * Search setting
         */
        private SearchMode _searchMode;
        /**
         * 欄位名
         */
        private Object _key;
        /**
         * 資料內容
         */
        private Object _value;
        /**
         * JOINTYPE，預設INNER
         */
        private JoinType joinType = JoinType.INNER;
        

        /**
         * 查詢的行為
         */
        SearchModeParameter param;

        CapSpecifications(SearchModeParameter param) {
            this._searchMode = param.getMode();
            this._key = param.getKey();
            this._value = param.getValue();
            this.joinType = param.getJoinType();
        }

        /**
         * 多條件查詢
         * 
         * @param root
         *            Root
         * @param query
         *            CriteriaQuery
         * @param builder
         *            CriteriaBuilder
         * @return
         */
        @SuppressWarnings({ "unchecked" })
        public Predicate toPredicate(Root root, CriteriaQuery query, CriteriaBuilder builder) {
            try {
                if (_key instanceof SearchModeParameter && _value instanceof SearchModeParameter) {
                    CapSpecifications spec_key = new CapSpecifications((SearchModeParameter) _key);
                    CapSpecifications spec_value = new CapSpecifications((SearchModeParameter) _value);
                    if (SearchMode.OR == _searchMode) {
                        return builder.or(spec_key.toPredicate(root, query, builder), spec_value.toPredicate(root, query, builder));
                    } else if (SearchMode.AND == _searchMode) {
                        return builder.and(spec_key.toPredicate(root, query, builder), spec_value.toPredicate(root, query, builder));
                    } else {
                        return null;
                    }
                }
                String key = (String) _key;

                String[] pathElements = key.split("\\.");

                Expression<?> path = null;
                if (pathElements.length > 1) {
                    Set<Join<?, ?>> joins = root.getJoins();
                    for (Join<?, ?> join : joins) {
                        if (join.getAttribute().getName().equals(pathElements[0])) {
                            path = join;
                            break;
                        }
                    }
                    if (path == null) {
                    	path = root.join(pathElements[0], joinType);
                    }
                } else {
                    path = root.get(pathElements[0]);
                }
                for (int i = 1; i <= pathElements.length - 1; i++) {
                    path = ((Path) path).get(pathElements[i]);
                }

                switch (_searchMode) {

                case BETWEEN:
                    Object[] values = asArray(_value);
                    if (values != null) {
                        return builder.between((Path<Comparable>) path, asComparable(values[0]), asComparable(values[1]));
                    } else {
                        return null;
                    }
                case GREATER_THAN:
                    return builder.greaterThan((Path<Comparable>) path, asComparable(_value));
                case GREATER_EQUALS:
                    return builder.greaterThanOrEqualTo((Path<Comparable>) path, asComparable(_value));
                case LESS_THAN:
                    return builder.lessThan((Path<Comparable>) path, asComparable(_value));
                case LESS_EQUALS:
                    return builder.lessThanOrEqualTo((Path<Comparable>) path, asComparable(_value));
                case IS_NULL:
                    return builder.isNull(path);
                case IS_NOT_NULL:
                    return builder.isNotNull(path);
                case IN:
                    return path.in(asArray(_value));
                case LIKE:
                    return builder.like((Path<String>) path, String.valueOf(_value));
                case NOT_LIKE:
                    return builder.notLike((Path<String>) path, String.valueOf(_value));
                case EQUALS:
                    return builder.equal(path, _value);
                case NOT_EQUALS:
                    return builder.notEqual(path, _value);
                default:
                    break;
                }
            } catch (Exception e) {
                logger.error(e.getLocalizedMessage(), e);
            }
            return null;
        }

        /**
         * 
         * @param value
         *            欄位資料內容
         * @return
         */
        private Comparable asComparable(Object value) {
            if (value instanceof Comparable) {
                return (Comparable<?>) value;
            } else {
                return null;
            }
        }

        /**
         * 以集合方式取出資料
         * 
         * @param value
         *            欄位資料內容
         * @return
         */
        @SuppressWarnings("unused")
        private Collection<?> asCollection(Object value) {
            if (value instanceof Collection) {
                return (Collection<?>) value;
            } else if (value.getClass().isArray()) {
                return Arrays.asList(value);
            }
            return Arrays.asList(value);
        }

        /**
         * 以陣列方式取出資料
         * 
         * @param value
         *            欄位資料內容
         * @return
         */
        private Object[] asArray(Object value) {
            if (value.getClass().isArray()) {
                Object[] result = new Object[Array.getLength(value)];
                for (int i = 0; i < result.length; ++i) {
                    result[i] = Array.get(value, i);
                }
                return result;
            } else if (value instanceof Collection) {
                return ((Collection) value).toArray();
            }
            return null;
        }
    }

    /**
     * 取得參數泛型
     * 
     * @return
     */
    public Class<T> getType() {
        return type;
    }

    /**
     * 設置參數泛型
     * 
     * @param type
     * @return
     */
    @SuppressWarnings("rawtypes")
    public GenericDao setType(Class<T> type) {
        this.type = type;
        return this;
    }

    /**
     * 設定搜尋方式<br>
     * 空建構子 {@code return new SearchSetting()}
     */
    public ISearch createSearchTemplete() {
        return new SearchSetting();
    }

    /*
     * flush
     * 
     * @see tw.com.iisi.cap.dao.IGenericDao#flush()
     */
    @Override
    public void flush() {
        getEntityManager().flush();
        getEntityManager().clear();
    }
    
    /*
     * clear
     * 
     * @see tw.com.iisi.cap.dao.IGenericDao#clear()
     */
    @Override
    public void clear() {
        getEntityManager().clear();
    }

    /**
     * 建立不重複的資料查詢
     * 
     * @param <S>
     * @param clazz
     *            類別
     * @param search
     *            Search setting
     * @return
     */
    protected <S> CriteriaQuery<Object[]> createDistinctQuery(Class<S> clazz, ISearch search) {
        ISearch thisSearch = (search != null) ? search : createSearchTemplete();
        CriteriaBuilder builder = getEntityManager().getCriteriaBuilder();
        CriteriaQuery<Object[]> query = builder.createQuery(Object[].class);
        Root<S> root = query.from(clazz);
        String[] distinctColumns = thisSearch.getDistinctColumn();
        Validate.notEmpty(distinctColumns, "The distinctColumns must not empty when you create distinct query.");
        Selection<?>[] selectionAry = new Selection<?>[distinctColumns.length];

        for (int i = 0; i < distinctColumns.length; i++) {
            selectionAry[i] = root.get(distinctColumns[i]);
        }

        query.multiselect(selectionAry);
        query.distinct(true);
        return applySpecificationToCriteria(root, query, builder, thisSearch);
    }

    /**
     * 使用既有的 root, query 物件，建立不重複的資料查詢
     * 
     * @param <S>
     * @param clazz
     *            類別
     * @param search
     *            Search setting
     * @return
     */
    @SuppressWarnings("rawtypes")
    protected <S> CriteriaQuery<Object[]> createDistinctQuery(Root root, CriteriaQuery<Object[]> query, Class<S> clazz, ISearch search) {
        ISearch thisSearch = (search != null) ? search : createSearchTemplete();
        CriteriaBuilder builder = getEntityManager().getCriteriaBuilder();
        // CriteriaQuery<Object[]> query = builder.createQuery(Object[].class);
        // Root<S> root = query.from(clazz);
        String[] distinctColumns = thisSearch.getDistinctColumn();
        Validate.notEmpty(distinctColumns, "The distinctColumns must not empty when you create distinct query.");
        Selection<?>[] selectionAry = new Selection<?>[distinctColumns.length];

        for (int i = 0; i < distinctColumns.length; i++) {
            selectionAry[i] = root.get(distinctColumns[i]);
        }

        query.multiselect(selectionAry);
        query.distinct(true);
        return applySpecificationToCriteria(root, query, builder, thisSearch);
    }

}
