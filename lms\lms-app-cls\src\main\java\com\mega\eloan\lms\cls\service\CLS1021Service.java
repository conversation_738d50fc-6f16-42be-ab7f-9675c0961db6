package com.mega.eloan.lms.cls.service;

/* 
 * CLS1021Service.java
 * 
 * Copyright (c) 2011-2013 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
import java.util.List;

import tw.com.iisi.cap.model.GenericBean;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.C102M01B;

/**
 * <pre>
 * 購置房屋擔保放款風險權數檢核表
 * </pre>
 * 
 * @since 2013/01/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/07,GaryChang,new
 *          </ul>
 */
public interface CLS1021Service extends AbstractService {

	/**
	 * 刪除動審表主檔資料 根據所選的oid
	 * 
	 * @param oids
	 *            文件編號陣列
	 * @return boolean
	 */
	boolean deleteC102m01as(String[] oids);


	/**
	 * 刪除上傳檔案
	 * 
	 * @param oids
	 *            文件編號
	 */
	void deleteUploadFile(String[] oids);

	/**
	 * 儲存C102M01B．案件簽章欄檔
	 * 
	 * @param list
	 *            List<L160M01D>
	 */
	public void saveC102m01bList(List<C102M01B> list);

	/**
	 * 查詢 L160M01D．案件簽章欄檔
	 * 
	 * @param mainId
	 *            案件編號
	 * 
	 * @param staffNo
	 *            員編
	 * @param staffjob
	 *            人員職稱
	 */
	public C102M01B findC102m01b(String mainId, String branchType, String branchId,
			String staffNo, String staffJob);

	/**
	 * 其它到結案所用的flow
	 * 
	 * @param mainOid
	 *            文件編號
	 * @param model
	 *            資料表
	 * @param setResult
	 *            boolean
	 * @param resultType
	 *            boolean
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, boolean resultType, boolean upMis)
			throws Throwable;

	public void deleteC102m01bs(List<C102M01B> c102m01bs, boolean isAll);

}