/* 
 * C820M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C820M01A;

/** 收集批覆書控制表 **/
public interface C820M01ADao extends IGenericDao<C820M01A> {

	C820M01A findByOid(String oid);
	
	List<C820M01A> findByMainId(String mainId);

	List<C820M01A> findByIndex01(String mainId, String groupId, String creator);
}