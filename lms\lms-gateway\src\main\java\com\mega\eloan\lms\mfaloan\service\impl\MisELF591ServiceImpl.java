
package com.mega.eloan.lms.mfaloan.service.impl;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;

import com.mega.eloan.lms.mfaloan.service.MisELF591Service;

/**
 * <pre>
 * 國內消金、現金存入警示戶明細檔 
 * </pre>
 * 
 * @since 2020/12/01
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/12/01,EL08034,new
 *          </ul>
 */
@Service
public class MisELF591ServiceImpl extends AbstractMFAloanJdbc implements
MisELF591Service {

	@Override
	public List<Map<String, Object>> sel_info_in_ELF591H(String idDup, String elf591h_beg_date){
		String custId_10 = StringUtils.substring(idDup, 0, 10);
		return this.getJdbc().queryForListWithMax("ELF591H.sel_info_in_ELF591H"
				, new String[]{ custId_10, elf591h_beg_date });
	}
	
	@Override
	public List<Map<String, Object>> sel_never_retrialData_in_ELF591H(String brNo, String idDup){
		String custId_10 = StringUtils.substring(idDup, 0, 10);
		String elf591h_beg_date = TWNDate.toAD(CapDate.addMonth(CapDate.getCurrentTimestamp(), -24));
		return this.getJdbc().queryForListWithMax("ELF591H.sel_never_retrialData_in_ELF591H"
				, new String[]{brNo+"%", custId_10, elf591h_beg_date });
	}
	
	@Override
	public List<Map<String, Object>> sel_not_in_ELF491_list(String brNo, String assign_crDate){		
		String elf591h_beg_date = TWNDate.toAD(CapDate.addMonth(CapDate.getCurrentTimestamp(), -24));
		return this.getJdbc().queryForListWithMax("ELF591H.sel_not_in_ELF491_list"
				, new String[]{brNo+"%", elf591h_beg_date, assign_crDate });
	}
}
