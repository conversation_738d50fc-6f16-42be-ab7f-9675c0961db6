var gridDfd = $.Deferred();

$(function(){


    if (txCode == "339057" || txCode == "339058" ||
    txCode == "339041" ||
    txCode == "339037" ||
    txCode == "339038" ||
    txCode == "339039" || txCode == "337008" ) {
        openFilterBox();
    }
    //2012-09-06 黃建霖 end
    if (txCode == "339058" || txCode == "339038" || txCode == "331008") {
        // 異常通報案件Grid
        mainGrid0();
    }
    else 
        if (viewstatus == "05O") {
            /*
         920 中部區域授信中心
         922 南部區域授信中心
         931 北一區營運中心
         932 北二區營運中心
         933 桃竹苗區營運中心
         934 中區營運中心
         935 南區營運中心
         918 授管處
         */
            if (userInfo.unitType == "2") {
                // 營運中心已核准
                openFilterBox();
                mainGrid1();
            }
            else 
                if (userInfo.unitNo == "918") {
                    // 授管處已核准
                    openFilterBox();
                    mainGrid1();
                }
                else {
                    // 海外分行已核准
                    openFilterBox();
                    mainGridInit();
                }
        }
        else 
            if (viewstatus == "06O") {
                openFilterBox();
                mainGridInit();
            }
            else 
                if (viewstatus == "0BO") {
                    mainGrid();
                }
                else 
                    if (viewstatus == "L0H") {
                        mainGrid();
                    }
                    else 
                        if (txCode == "339041") {
                            /*
         920 中部區域授信中心
         922 南部區域授信中心
         931 北一區營運中心
         932 北二區營運中心
         933 桃竹苗區營運中心
         934 中區營運中心
         935 南區營運中心
         918 授管處
         */
                            if (userInfo.unitType == "2") {
                                mainGrid1();
                            }
                        }
                        else 
                            if (txCode == "339062" || txCode == "339063" || txCode == "339064" || txCode == "339065") {
                            	//J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
                                if (txCode == "339062") {//授審會
                                    mainGrid3(1);
                                }
                                else 
                                    if (txCode == "339063") {//催收會
                                        mainGrid3(2);
                                    }
                                    else if(txCode == "339064"){//常董會
                                        mainGrid2();
                                    }else{//339065「審計委員會」
                                    	mainGrid3(4);
                                    }
                            }
                            else 
                                if ((viewstatus == "L1H" || viewstatus == "03K|01K|02K|04K") && userInfo.unitNo == "918") {
                                    // 授管處
                                    mainGrid4();
                                }
                                else 
                                    if (viewstatus == "L1C" && (userInfo.unitType == "2")) {
                                        // 營運中心
                                        mainGrid5();
                                    }
                                    else 
                                        if (txCode == "337091" || txCode == "337092" || txCode == "337093" || txCode == "339091" || txCode == "339092" || txCode == "339093") {
                                            if (txCode == "337093") {
                                                openFilterBox();
                                            }
                                            // 海外聯貸案
                                            mainGridSea();
                                        }
                                        else {
                                            mainGrid();
                                        }
    
    //授審處顯示按鈕
    if (userInfo.unitNo == "918") {
    	$("#buttonPanel").find("#btnProduceExcel").show();
    }
    else{
    	$("#buttonPanel").find("#btnProduceExcel").hide();
    }

    // J-110-0458 企金授權內其他
    $("#LMS1205V01Form").find("[name='docType']").change(function(){
        //企/個金
        if (showCaseType()) {
            var docCode = $("#LMS1205V01Form").find("[name='docCode']:radio:checked").val();
            loadCaseTypeItem(docCode);
        }
        setUI();
    });

    $("#LMS1205V01Form").find("[name='docKind']").change(function(){
        //授權別:授權內、授權外
        if (showCaseType()) {
            var docCode = $("#LMS1205V01Form").find("[name='docCode']:radio:checked").val();
            loadCaseTypeItem(docCode);
        }
        setUI();
    });

    $("#LMS1205V01Form").find("[name='docCode']").change(function(){
        //案件別:案件簽報書（一般）、案件簽報書（其他）、案件簽報書（陳復案/陳述案）、案件簽報書(異常通報案件)
        if (showCaseType()) {
            var docCode = $("#LMS1205V01Form").find("[name='docCode']:radio:checked").val();
            loadCaseTypeItem(docCode);
        }
        setUI();
    });

    $("#LMS1205V01Form").find("[name='miniFlag']").change(function(){
        // 簡易簽報
        if (showCaseType()) {
            var docCode = $("#LMS1205V01Form").find("[name='docCode']:radio:checked").val();
            loadCaseTypeItem(docCode);
        }
        setUI();
    });

    $("#LMS1205V01Form").find("[name='caseType']").change(function(){
        // 適用方案大類
        setUI();
    });

    $("#buttonPanel").find("#btnAdd").click(function(){
        thickBoxOpen();
    }).end().find("#btnModify").click(function(){
        var row2 = $("#gridview").getGridParam('selrow');
        var list2 = "";
        var data2 = $("#gridview").getRowData(row2);
        list2 = data2.oid;
        // alert(list);
        if (list2 == "" || list2 == undefined) {
            CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
            return;
        }
        openDoc(null, null, data2);
    }).end().find("#btnCaseLvl").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var list = "";
        var sign = ",";
        for (var i = 0; i < rows.length; i++) { //將所有已選擇的資料存進變數list裡面
            if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                var data = $("#gridview").getRowData(rows[i]);
                list += ((list == "") ? "" : sign) + data.oid;
            }
        }
        if (list == "") {
            return CommonAPI.showMessage(i18n.lms9131v00['LMS913V00.error1']);
        }
        $.ajax({
            type: "POST",
            handler: "lms9131m01formhandler",
            data: {
                formAction: "Clear",
                listOid: list,
                sign: sign,
                flag: 'A'
            }
		}).done(function(json){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                $("#gridfile").trigger("reloadGrid");//更新Grid內容						
        });
	}).end().find("#btnUpdCustId").click(function(){
		//J-105-0202-001 Web e-Loan企金授信修改客戶統編。
	 
		$("#UpdCustIdBox").thickbox({
	        title: "修改客戶統編",
	        width: 340,
	        height: 260,
	        modal: true,
	        i18n: i18n.def,
	        buttons: {
	            "sure": function(){
					
					var $form = $("#UpdCustIdForm");
                    if (!$form.valid()) {
                        return false;
                    }
							
	                var rows = $("#gridview").getGridParam('selarrrow');
			        var list = "";
			        var sign = ",";
			        for (var i = 0; i < rows.length; i++) { //將所有已選擇的資料存進變數list裡面
			            if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
			                var data = $("#gridview").getRowData(rows[i]);
			                list += ((list == "") ? "" : sign) + data.oid;
			            }
			        }
			        if (list == "") {
			            CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
			            return;
			        }
					
			        CommonAPI.confirmMessage("是否確定要執行", function(b){
						$.thickbox.close();		
			            if (b) {
			                $.ajax({
					            type: "POST",
					            handler: "lms1205formhandler",
					            data: {
					                formAction: "updCustId",
									sign: sign,
									orgCustId: $form.find("#orgCustId").val(),
									orgDupNo: $form.find("#orgDupNo").val(),
									newCustId: $form.find("#newCustId").val(),
									newDupNo: $form.find("#newDupNo").val(),
									useNew0024Name: ($form.find("input[name=useNew0024Name]:checked").val() || ''),
					                listOid: list
					            }
							}).done(function(json){
									CommonAPI.triggerOpener("gridview", "reloadGrid");
					                $("#gridfile").trigger("reloadGrid");//更新Grid內容
					                //===============
					                $("#gridview").trigger("reloadGrid");
												
					        });		 
			            }
			        });
					
	            },
	            "close": function(){
	                $.thickbox.close();
	            }
	        }
	    });
	
	
        
    }).end().find("#btnDelete").click(function(){
        var row = $("#gridview").getGridParam('selrow');
        var list = "";
        var data = $("#gridview").getRowData(row);
        list = data.oid;
        if (list == undefined) {
            CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
            return;
        }
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    type: "POST",
                    handler: "lms1205formhandler",
                    data: {
                        formAction: "deleteL120m01a",
                        mainOid: $("#oid").val(),
                        list: list
                    }
				}).done(function(responseData){
                        CommonAPI.triggerOpener("gridview", "reloadGrid");
                        $("#gridview").trigger("reloadGrid");// 更新Grid內容
                });
            }
        });
    }).end().find("#btnChange").click(function(){
        changeCase2();
    }).end().find("#btnChangeCaseFormat").click(function(){
        changeCase();
    }).end().find("#btnChangeVer").click(function(){
        changeVer();
    }).end().find("#btnChangeCaseFormat1").click(function(){
        var row = $("#gridview").getGridParam('selrow');
        var list = "";
        var data = $("#gridview").getRowData(row);
        list = data.oid;
        if (list == undefined || list == '' || list == null) {
            CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
            return;
        }
        // docRslt在這邊暫存授權別(docKind)
        // docCode = 3 :陳復述案, docCode = 4:異常通報案件				
        if (data.docRslt == "1" || data.docCode == "3" || data.docCode == "4") {
            CommonAPI.showErrorMessage("必須為區域營運中心授權外一般案件才可執行本功能");
            return;
        }
        else {
            // 開啟營運中心授權外轉授權內功能
            CommonAPI.confirmMessage(i18n.def["confirmRun"], function(b){
                if (b) {
                    $.ajax({
                        type: "POST",
                        handler: "lms1205formhandler",
                        data: {
                            formAction: "areaChange",
                            oid: list
                        }
					}).done(function(responseData){
                            $("#gridview").trigger("reloadGrid"); // 更新Grid內容
                            $.thickbox.close();
                            $.thickbox.close();
                            CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
                    });
                }
            });
        }
    }).end().find("#btnView").click(function(){
    
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
        
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
            
        }
        var result = $("#gridview").getRowData(id);
        openDoc(null, null, result);
    }).end().find("#btnFilter").click(function(){
        openFilterBox();
    }).end().find("#btnPrintBook").click(function(){
        var row = $("#gridview").getGridParam('selrow');
        var list = "";
        var data = $("#gridview").getRowData(row);
        list = data.oid;
        if (list == undefined) {
            CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
            return;
        }
        //J-110-0097_05097_B1001 Web e-Loan企金授信修改出口押匯額度之「核貸通知書」格式
        $.capFileDownload({
            handler: "lmsdownloadformhandler",
            data: {
                fileName: "LMSDoc15.htm",
                fileName2: "LMSDoc15_942.htm",
                oid: list,
                txCode: txCode,
                docTempType: "LMSDoc8",
                fileDownloadName: "LMSDoc15.doc",
                serviceName: "lms1201docservice"
            }
        });
    }).end().find("#btnUPCls").click(function(){
        // 房貸評分卡上傳DW(2014-08-25目前只單傳DWADM.DW_RKAPPLICANT待以後陸續增加)
        var tGrid = $("#gridview");
        var datas = [];
        var rows = tGrid.getGridParam('selarrrow');
        for (var o in rows) {
            datas.push(tGrid.getRowData(rows[o]).oid);
        }
        if (datas.length > 0) {
        }
        else {
            MegaApi.showErrorMessage(i18n.def['confirmTitle'], i18n.def['action_005']);
            return undefined;
        }
        
        $.ajax({
            handler: "lms1205formhandler",
            action: "upDwByL120M01AForOBU",
            data: {
                oids: datas
            }
		}).done(function(responseData){
        });
    }).end().find("#btnCaseCopy").click(function(){
        var row = $("#gridview").getGridParam('selrow');
        var list = "";
        var data = $("#gridview").getRowData(row);
        list = data.mainId;
        if (list == undefined) {
            CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
            return;
        }
        $.ajax({
            type: "POST",
            handler: "lms1205formhandler",
            data: {
                formAction: "copyLms120m01a",
                list: list
            }
		}).done(function(responseData){
        });
    }).end().find("#btnCreDoc1").click(function(){
        $("#LMS1200V64Thickbox2").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.lms1205v01["l120v01.thickbox10"],
            width: 500,
            height: 300,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.lms1205v01,
            buttons: {
                "l120v01.thickbox1": function(){
                    var val = $("input[name='creDoc']:checked").val();
                    if (val == "1") {
                        // 個案討論
                        var ids = new Array();
                        ids = $("#gridview").getGridParam('selarrrow');
                        var list = "";
                        var sign = ",";
                        var count = 0;
                        for (var id in ids) {
                            var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                            if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
                                list += ((list == "") ? "" : sign) + rows.oid;
                            }
                            count++;
                        }
                        if (list == "") {
                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
                            return;
                        }
                        else 
                            if (count > 1) {
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.error2"]);
                                return;
                            }
                            else {
                                $.capFileDownload({
                                    handler: "lmsdownloadformhandler",
                                    data: {
                                        sign: sign,
                                        fileName: "LMSDoc4.htm",
                                        fileName2: "LMSDoc41.htm",
                                        oid: list,
                                        txCode: txCode,
                                        docTempType: "LMSDoc4",
                                        fileDownloadName: "LMSDoc4.doc",
                                        serviceName: "lms1201docservice"
                                    }
                                });
                                $.thickbox.close();
                            }
                    }
                    else if (val == "5") {
                        var ids = new Array();
                        ids = $("#gridview").getGridParam('selarrrow');
                        var list = "";
                        var sign = ",";
                        var count = 0;
                        var mainId = "";
                        var caseTypeCd = "";
                        for (var id in ids) {
                            var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                            if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
                                list += ((list == "") ? "" : sign) + rows.oid;
                                mainId += ((mainId == "") ? "" : sign) + rows.mainId;
                                caseTypeCd = rows.typCd;
                            }
                            count++;
                        }
                        if (list == "") {
                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
                            return;
                        }
                        else
                            if (count > 1) {
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.error2"]);
                                return;
                            }
                            else {
                                $.ajax({
                                    type: "POST",
                                    handler: "lms1205formhandler",
                                    data: {
                                        formAction: "checkIsOutNewVer",
                                        oid: list
                                    }
								}).done(function(responseData){
                                        $.form.submit({
                                            url: "../../app/simple/FileProcessingService",
                                            target: "_blank",
                                            data: {
                                                oid: list,
                                                mainId: mainId,
                                                rptOid: "R01" + "^" + list + "^" + "^" + "^" + "^" + "LMSDoc4",
                                                fileDownloadName: (caseTypeCd == "5" ? "LMS1205R01.pdf" : "LMS1201R01.pdf"),
                                                serviceName: (caseTypeCd == "5" ? "lms1205r01rptservice" : "lms1201r01rptservice")
                                            }
                                        });
                                        $.thickbox.close();
                                });
                            }
                    }
                    else if (val == "6") {
                        var ids = new Array();
                        ids = $("#gridview").getGridParam('selarrrow');
                        var list = "";
                        var sign = ",";
                        var count = 0;
                        var mainId = "";
                        for (var id in ids) {
                            var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                            if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
                                list += ((list == "") ? "" : sign) + rows.oid;
                                mainId += ((mainId == "") ? "" : sign) + rows.mainId;
                            }
                            count++;
                        }
                        if (list == "") {
                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
                            return;
                        }
                        else
                            if (count > 1) {
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.error2"]);
                                return;
                            }
                            else {
                                $.ajax({
                                    type: "POST",
                                    handler: "lms1205formhandler",
                                    data: {
                                        formAction: "checkL120s08aIsOutNewVer",
                                        oid: list
                                    }
								}).done(function(responseData){
                                        $.form.submit({
                                            url: "../../app/simple/FileProcessingService",
                                            target: "_blank",
                                            data: {
                                                oid: list,
                                                mainId: mainId,
                                                rptOid: "R32_simple" + "^" + list + "^" + "^" + "^" + "^" + "",
                                                fileDownloadName: "LMS1201R32.pdf",
                                                serviceName: "lms1201r01rptservice"
                                            }
                                        });
                                        $.thickbox.close();
                                });
                            }
                    }
                    else if (val == "4") {
                        // 個案討論 202009
                        var ids = new Array();
                        ids = $("#gridview").getGridParam('selarrrow');
                        var list = "";
                        var sign = ",";
                        var count = 0;
                        for (var id in ids) {
                            var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                            if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
                                list += ((list == "") ? "" : sign) + rows.oid;
                            }
                            count++;
                        }
                        if (list == "") {
                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
                            return;
                        }
                        else 
                            if (count > 1) {
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.error2"]);
                                return;
                            }
                            else {
                                $.capFileDownload({
                                    handler: "lmsdownloadformhandler",
                                    data: {
                                        sign: sign,
                                        fileName: "LMSDoc4_V202011.htm",
                                        oid: list,
                                        txCode: txCode,
                                        docTempType: "LMSDoc4_V202011",
                                        fileDownloadName: "LMSDoc4.doc",
                                        serviceName: "lms1201docservice"
                                    }
                                });
                                $.thickbox.close();
                            }
                    }
                    else 
                        if (val == "2") {
                            // 彙總討論
                            var ids = new Array();
                            ids = $("#gridview").getGridParam('selarrrow');
                            var list = "";
                            var sign = ",";
                            var count = 0;
                            for (var id in ids) {
                                var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                if (rows.oid != 'undefined' &&
                                rows.oid != null &&
                                rows.oid != 0) {
                                    list += ((list == "") ? "" : sign) +
                                    rows.oid;
                                }
                                count++;
                            }
                            if (list == "") {
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
                                return;
                            }
                            else 
                                if (count > 1) {
                                    CommonAPI.showMessage(i18n.lms1205v01["l120v01.error2"]);
                                    return;
                                }
                                else {
                                    $.capFileDownload({
                                        handler: "lmsdownloadformhandler",
                                        data: {
                                            sign: sign,
                                            fileName: "LMSDoc51.htm",
                                            fileName2: "LMSDoc52.htm",
                                            oid: list,
                                            txCode: txCode,
                                            docTempType: "LMSDoc5",
                                            fileDownloadName: "LMSDoc51.doc",
                                            serviceName: "lms1201docservice"
                                        }
                                    });
                                    $.thickbox.close();
                                }
                        }
                        else {
                            // 常董會授權總經理逕行核定案件
                            var ids = new Array();
                            ids = $("#gridview").getGridParam('selarrrow');
                            var list = "";
                            var sign = ",";
                            var count = 0;
                            for (var id in ids) {
                                var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
                                    list += ((list == "") ? "" : sign) + rows.oid;
                                }
                                count++;
                            }
                            if (list == "") {
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
                                return;
                            }
                            else 
                                if (count > 1) {
                                    CommonAPI.showMessage(i18n.lms1205v01["l120v01.error2"]);
                                    return;
                                }
                                else {
									
									 
                                    $.capFileDownload({
                                        handler: "lmsdownloadformhandler",
                                        data: {
                                            sign: sign,
                                            fileName: "LMSDoc61.htm",
                                            fileName2: "LMSDoc62.htm",
                                            listOid: list,
                                            txCode: txCode,
                                            docTempType: "LMSDoc6",
                                            docTempFile: "LMSDoc62.doc",
                                            fileDownloadName: "LMSDoc62.doc",
                                            serviceName: "lms1201docservice"
                                        }
                                    });
								 
									setTimeout(function(){
										$.capFileDownload({
                                        handler: "lmsdownloadformhandler",
                                        data: {
                                            sign: sign,
                                            fileName: "LMSDoc61.htm",
                                            fileName2: "LMSDoc62.htm",
                                            listOid: list,
                                            txCode: txCode,
                                            docTempType: "LMSDoc6",
                                            docTempFile: "LMSDoc61.doc",
                                            fileDownloadName: "LMSDoc61.doc",
                                            serviceName: "lms1201docservice"
                                        }
                                    });
									},3000);
									
									 
                                    
									
									
                                    $.thickbox.close();
                                }
                        }
                },
                "l120v01.thickbox2": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
    }).end().find("#btnTableSend").click(function(){
        // 已核准 - 額度明細表傳送聯行
        btnTableSend();
    }).end().find("#btnGetCase").click(function(){
        // 待收案件 - 收件
        getCase();
    }).end().find("#btnPrintArea").click(function(){
        //列印營運中心意見
        printArea();
    }).end().find("#btnLogin1").click(function(){
        login1();
    }).end().find("#btnLogin2").click(function(){
        login2();
    }).end().find("#btnLogin3").click(function(){
        login3();
    }).end().find("#btnLogin4").click(function(){
        login4();
    }).end().find("#btnCreate").click(function(){
        if (txCode == "339041") {
            // 營運中心所有提會
            $("#LMS1200V41Thickbox").thickbox({ // 使用選取的內容進行彈窗
                title: i18n.lms1205v01["l120m01a.btnCreate"],
                width: 500,
                height: 200,
                modal: true,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        var val = $("input[name='create']:checked").val();
                        if (val == "2") {
                            // 決議錄(同會期之全部文件)
                            //var ids = $("#gridview").jqGrid('getDataIDs');
                            var list = "";
                            var sign = ",";
                            var ids2 = new Array();
                            ids2 = $("#gridview").getGridParam('selarrrow');
                            var list2 = "";
                            var sRptTitle = "";
                            var count = 0;
                            for (var id2 in ids2) {
                                var rows2 = $("#gridview").jqGrid('getRowData', ids2[id2]);
                                if (rows2.oid != 'undefined' && rows2.oid != null && rows2.oid != "") {
                                    if (rows2.rptTitleArea1 == 'undefined' || rows2.rptTitleArea1 == null || rows2.rptTitleArea1 == '') {
                                        /*
                                         CommonAPI.showMessage(i18n.lms1205v01["l120v01.error5"]);
                                         return;
                                         */
                                    }
                                    else {
                                        list2 += ((list2 == "") ? "" : sign) + rows2.oid;
                                        sRptTitle += ((sRptTitle == "") ? "" : sign) + rows2.rptTitleArea1;
                                    }
                                }
                                count++;
                            }
                            if (count > 1) {
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.error2"]);
                                return;
                            }
                            if (list2 == "") {
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
                                return;
                            }
                            if (sRptTitle == 'undefined' || sRptTitle == null || sRptTitle == '') {
                                // 沒有登錄會期，跳過
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.error5"]);
                                return;
                            }
                            list = ajaxPcTitle(list2);
                            $.thickbox.close();
                            $.capFileDownload({
                                handler: "lmsdownloadformhandler",
                                data: {
                                    sign: sign,
                                    fileName: "LMSDoc2.htm",
                                    isArea: true,
                                    listOid: list,
                                    selOid: list2,
                                    txCode: txCode,
                                    docTempType: "LMSDoc2",
                                    fileDownloadName: "LMSDoc2.doc",
                                    serviceName: "lms1201docservice"
                                }
                            });
                        }
                        else {
                            // 決議錄(同會期之勾選文件)
                            var ids = new Array();
                            ids = $("#gridview").getGridParam('selarrrow');
                            var list = "";
                            var sign = ",";
                            var rptTitles = [];
                            var selOid = "";
                            for (var id in ids) {
                                var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                if (rows.oid != 'undefined' && rows.oid != null && rows.oid != "") {
                                    list += ((list == "") ? "" : sign) + rows.oid;
                                    selOid = rows.oid;
                                    if (rows.rptTitleArea1 == 'undefined' || rows.rptTitleArea1 == null || rows.rptTitleArea1 == '') {
                                        CommonAPI.showMessage(i18n.lms1205v01["l120v01.error5"]);
                                        return;
                                    }
                                    else {
                                        rptTitles.push(rows.rptTitleArea1);
                                    }
                                }
                            }
                            if (list == "") {
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
                                return;
                            }
                            else {
                                // 開始檢查會期是否重覆
                                $.ajax({
                                    type: "POST",
                                    handler: "lms1205formhandler",
                                    data: {
                                        formAction: "checkRptTitles",
                                        rptTitles: rptTitles,
                                        selOid: selOid
                                    }
								}).done(function(responseData){
                                        $.capFileDownload({
                                            handler: "lmsdownloadformhandler",
                                            data: {
                                                sign: sign,
                                                fileName: "LMSDoc2.htm",
                                                isArea: true,
                                                listOid: list,
                                                selOid: responseData.resOid,
                                                txCode: txCode,
                                                docTempType: "LMSDoc2",
                                                fileDownloadName: "LMSDoc2.doc",
                                                serviceName: "lms1201docservice"
                                            }
                                        });
                                });
                                $.thickbox.close();
                            }
                        }
                    },
                    "cancel": function(){
                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                            if (res) {
                                $.thickbox.close();
                            }
                        });
                    }
                }
            });
        }
        else 
            if (txCode == "339062") {
                // 授審會
                $("#LMS1200V62Thickbox").thickbox({ // 使用選取的內容進行彈窗
                    title: i18n.lms1205v01["l120m01a.btnCreate"],
                    width: 500,
                    height: 200,
                    modal: true,
                    valign: "bottom",
                    align: "center",
                    i18n: i18n.lms1205v01,
                    buttons: {
                        "l120v01.thickbox1": function(){
                            var val = $("input[name='create']:checked").val();
                            if (val == "1") {
                                // 議程
                                var ids = new Array();
                                ids = $("#gridview").getGridParam('selarrrow');
                                var list = "";
                                var sign = ",";
                                var count = 0;
                                var rptTitles = [];
                                for (var id in ids) {
                                    var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                    if (rows.oid != 'undefined' && rows.oid != null && rows.oid != "") {
                                        list += ((list == "") ? "" : sign) + rows.oid;
                                        if (rows.rptTitle1 == 'undefined' || rows.rptTitle1 == null || rows.rptTitle1 == '') {
                                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.error5"]);
                                            return;
                                        }
                                        else {
                                            rptTitles.push(rows.rptTitle1);
                                        }
                                    }
                                    count++;
                                }
                                // 檢查所選是否皆為同會期(同會期才可多選產生議程)
                                var title = rptTitles[0];
                                var canCreate = true;
                                for (o in rptTitles) {
                                    if (title != rptTitles[o]) {
                                        canCreate = false;
                                    }
                                }
                                if (list == "") {
                                    CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
                                    return;
                                }
                                else 
                                    if (count > 1 && !canCreate) {
                                        CommonAPI.showMessage(i18n.lms1205v01["l120v01.error2"]);
                                        return;
                                    }
                                    else {
                                        $.capFileDownload({
                                            handler: "lmsdownloadformhandler",
                                            data: {
                                                sign: sign,
                                                fileName: "LMSDoc11.htm",
                                                listOid: list,
                                                txCode: txCode,
                                                docTempType: "LMSDoc1",
                                                fileDownloadName: "LMSDoc11.doc",
                                                serviceName: "lms1201docservice"
                                            }
                                        });
                                        $.thickbox.close();
                                    }
                            }
                            else 
                                if (val == "2") {
                                    // 決議錄(同會期之全部文件)
                                    //var ids = $("#gridview").jqGrid('getDataIDs');
                                    var list = "";
                                    var sign = ",";
                                    var ids2 = new Array();
                                    ids2 = $("#gridview").getGridParam('selarrrow');
                                    var list2 = "";
                                    var sRptTitle = "";
                                    var count = 0;
                                    for (var id2 in ids2) {
                                        var rows2 = $("#gridview").jqGrid('getRowData', ids2[id2]);
                                        if (rows2.oid != 'undefined' && rows2.oid != null && rows2.oid != "") {
                                            if (rows2.rptTitle1 == 'undefined' || rows2.rptTitle1 == null || rows2.rptTitle1 == '') {
                                            /*
                                         CommonAPI.showMessage(i18n.lms1205v01["l120v01.error5"]);
                                         return;
                                         */
                                            }
                                            else {
                                                list2 += ((list2 == "") ? "" : sign) + rows2.oid;
                                                sRptTitle += ((sRptTitle == "") ? "" : sign) + rows2.rptTitle1;
                                            }
                                        }
                                        count++;
                                    }
                                    if (count > 1) {
                                        CommonAPI.showMessage(i18n.lms1205v01["l120v01.error2"]);
                                        return;
                                    }
                                    
                                    if (sRptTitle == 'undefined' || sRptTitle == null || sRptTitle == '') {
                                        // 沒有登錄會期，跳過
                                        CommonAPI.showMessage(i18n.lms1205v01["l120v01.error5"]);
                                        return;
                                    }
                                    list = ajaxPcTitle(list2);
                                    $.thickbox.close();
                                    $.capFileDownload({
                                        handler: "lmsdownloadformhandler",
                                        data: {
                                            sign: sign,
                                            fileName: "LMSDoc2.htm",
                                            listOid: list,
                                            selOid: list2,
                                            txCode: txCode,
                                            docTempType: "LMSDoc2",
                                            fileDownloadName: "LMSDoc2.doc",
                                            serviceName: "lms1201docservice"
                                        }
                                    });
                                }
                                else {
                                    // 決議錄(同會期之勾選文件)
                                    var ids = new Array();
                                    ids = $("#gridview").getGridParam('selarrrow');
                                    var list = "";
                                    var sign = ",";
                                    var rptTitles = [];
                                    var selOid = "";
                                    for (var id in ids) {
                                        var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                        if (rows.oid != 'undefined' && rows.oid != null && rows.oid != "") {
                                            list += ((list == "") ? "" : sign) + rows.oid;
                                            selOid = rows.oid;
                                            if (rows.rptTitle1 == 'undefined' || rows.rptTitle1 == null || rows.rptTitle1 == '') {
                                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.error5"]);
                                                return;
                                            }
                                            else {
                                                rptTitles.push(rows.rptTitle1);
                                            }
                                        }
                                    }
                                    if (list == "") {
                                        CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
                                        return;
                                    }
                                    else {
                                        // 開始檢查會期是否重覆
                                        $.ajax({
                                            type: "POST",
                                            handler: "lms1205formhandler",
                                            data: {
                                                formAction: "checkRptTitles",
                                                rptTitles: rptTitles,
                                                selOid: selOid
                                            }
										}).done(function(responseData){
                                                $.capFileDownload({
                                                    handler: "lmsdownloadformhandler",
                                                    data: {
                                                        sign: sign,
                                                        fileName: "LMSDoc2.htm",
                                                        listOid: list,
                                                        selOid: responseData.resOid,
                                                        txCode: txCode,
                                                        docTempType: "LMSDoc2",
                                                        fileDownloadName: "LMSDoc2.doc",
                                                        serviceName: "lms1201docservice"
                                                    }
                                                });
                                        });
                                        $.thickbox.close();
                                    }
                                }
                        },
                        "l120v01.thickbox2": function(){
                            API.confirmMessage(i18n.def['flow.exit'], function(res){
                                if (res) {
                                    $.thickbox.close();
                                }
                            });
                        }
                    }
                });
            }
            else 
                if (txCode == "339063") {
                    // 催收會
                    $("#LMS1200V63Thickbox").thickbox({ // 使用選取的內容進行彈窗
                        title: i18n.lms1205v01["l120m01a.btnCreate"],
                        width: 500,
                        height: 200,
                        modal: true,
                        valign: "bottom",
                        align: "center",
                        i18n: i18n.lms1205v01,
                        buttons: {
                            "l120v01.thickbox1": function(){
                                var val = $("input[name='create']:checked").val();
                                if (val == "1") {
                                    // 議程
                                    var ids = new Array();
                                    ids = $("#gridview").getGridParam('selarrrow');
                                    var list = "";
                                    var sign = ",";
                                    var count = 0;
                                    var rptTitles = [];
                                    for (var id in ids) {
                                        var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                        if (rows.oid != 'undefined' && rows.oid != null && rows.oid != "") {
                                            list += ((list == "") ? "" : sign) + rows.oid;
                                            if (rows.rptTitle1 == 'undefined' || rows.rptTitle1 == null || rows.rptTitle1 == '') {
                                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.error5"]);
                                                return;
                                            }
                                            else {
                                                rptTitles.push(rows.rptTitle1);
                                            }
                                        }
                                        count++;
                                    }
                                    // 檢查所選是否皆為同會期(同會期才可多選產生議程)
                                    var title = rptTitles[0];
                                    var canCreate = true;
                                    for (o in rptTitles) {
                                        if (title != rptTitles[o]) {
                                            canCreate = false;
                                        }
                                    }
                                    if (list == "") {
                                        CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
                                        return;
                                    }
                                    else 
                                        if (count > 1 && !canCreate) {
                                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.error2"]);
                                            return;
                                        }
                                        else {
                                            $.capFileDownload({
                                                handler: "lmsdownloadformhandler",
                                                data: {
                                                    sign: sign,
                                                    fileName: "LMSDoc11.htm",
                                                    listOid: list,
                                                    txCode: txCode,
                                                    docTempType: "LMSDoc1",
                                                    fileDownloadName: "LMSDoc11.doc",
                                                    serviceName: "lms1201docservice"
                                                }
                                            });
                                            $.thickbox.close();
                                        }
                                }
                                else 
                                    if (val == "2") {
                                        // 決議錄(同會期之全部文件)
                                        var ids = $("#gridview").jqGrid('getDataIDs');
                                        var list = "";
                                        var sign = ",";
                                        
                                        var ids2 = new Array();
                                        ids2 = $("#gridview").getGridParam('selarrrow');
                                        var list2 = "";
                                        var sRptTitle = "";
                                        var count = 0;
                                        for (var id2 in ids2) {
                                            var rows2 = $("#gridview").jqGrid('getRowData', ids2[id2]);
                                            if (rows2.oid != 'undefined' && rows2.oid != null && rows2.oid != "") {
                                                if (rows2.rptTitle1 == 'undefined' || rows2.rptTitle1 == null || rows2.rptTitle1 == '') {
                                                /*
                                         CommonAPI.showMessage(i18n.lms1205v01["l120v01.error5"]);
                                         return;
                                         */
                                                }
                                                else {
                                                    list2 += ((list2 == "") ? "" : sign) + rows2.oid;
                                                    sRptTitle += ((sRptTitle == "") ? "" : sign) + rows2.rptTitle1;
                                                }
                                            }
                                            count++;
                                        }
                                        if (count > 1) {
                                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.error2"]);
                                            return;
                                        }
                                        
                                        if (sRptTitle == 'undefined' || sRptTitle == null || sRptTitle == '') {
                                            // 沒有登錄會期，跳過
                                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.error5"]);
                                            return;
                                        }
                                        
                                        list = ajaxPcTitle(list2);
                                        $.thickbox.close();
                                        $.capFileDownload({
                                            handler: "lmsdownloadformhandler",
                                            data: {
                                                sign: sign,
                                                fileName: "LMSDoc21.htm",
                                                listOid: list,
                                                selOid: list2,
                                                txCode: txCode,
                                                docTempType: "LMSDoc2",
                                                fileDownloadName: "LMSDoc21.doc",
                                                serviceName: "lms1201docservice"
                                            }
                                        });
                                    }
                                    else {
                                        // 決議錄(同會期之勾選文件)
                                        var ids = new Array();
                                        ids = $("#gridview").getGridParam('selarrrow');
                                        var list = "";
                                        var sign = ",";
                                        var rptTitles = [];
                                        var selOid = "";
                                        for (var id in ids) {
                                            var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                            if (rows.oid != 'undefined' && rows.oid != null && rows.oid != "") {
                                                list += ((list == "") ? "" : sign) + rows.oid;
                                                selOid = rows.oid;
                                                if (rows.rptTitle1 == 'undefined' || rows.rptTitle1 == null || rows.rptTitle1 == '') {
                                                    CommonAPI.showMessage(i18n.lms1205v01["l120v01.error5"]);
                                                    return;
                                                }
                                                else {
                                                    rptTitles.push(rows.rptTitle1);
                                                }
                                            }
                                        }
                                        if (list == "") {
                                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
                                            return;
                                        }
                                        else {
                                            // 開始檢查會期是否重覆
                                            $.ajax({
                                                type: "POST",
                                                handler: "lms1205formhandler",
                                                data: {
                                                    formAction: "checkRptTitles",
                                                    rptTitles: rptTitles,
                                                    selOid: selOid
                                                }
											}).done(function(responseData){
                                                    $.capFileDownload({
                                                        handler: "lmsdownloadformhandler",
                                                        data: {
                                                            sign: sign,
                                                            fileName: "LMSDoc21.htm",
                                                            listOid: list,
                                                            selOid: responseData.resOid,
                                                            txCode: txCode,
                                                            docTempType: "LMSDoc2",
                                                            fileDownloadName: "LMSDoc21.doc",
                                                            serviceName: "lms1201docservice"
                                                        }
                                                    });
                                            });
                                            $.thickbox.close();
                                        }
                                    }
                            },
                            "l120v01.thickbox2": function(){
                                API.confirmMessage(i18n.def['flow.exit'], function(res){
                                    if (res) {
                                        $.thickbox.close();
                                    }
                                });
                            }
                        }
                    });
                }
                else 
                    if (txCode == "339064") {
                        // 常董會
                        $("#LMS1200V64Thickbox").thickbox({ // 使用選取的內容進行彈窗
                            title: i18n.lms1205v01["l120m01a.btnCreate"],
                            width: 500,
                            height: 200,
                            modal: true,
                            valign: "bottom",
                            align: "center",
                            i18n: i18n.lms1205v01,
                            buttons: {
                                "l120v01.thickbox1": function(){
                                    var val = $("input[name='create']:checked").val();
                                    if (val == "1") {
                                        // 議程
                                        var ids = new Array();
                                        ids = $("#gridview").getGridParam('selarrrow');
                                        var list = "";
                                        var sign = ",";
                                        var count = 0;
                                        var rptTitles = [];
                                        for (var id in ids) {
                                            var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                            if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
                                                list += ((list == "") ? "" : sign) + rows.oid;
                                                if (rows.rptTitle2 == 'undefined' || rows.rptTitle2 == null || rows.rptTitle2 == '') {
                                                    CommonAPI.showMessage(i18n.lms1205v01["l120v01.error5"]);
                                                    return;
                                                }
                                                else {
                                                    rptTitles.push(rows.rptTitle2);
                                                }
                                            }
                                            count++;
                                        }
                                        // 檢查所選是否皆為同會期(同會期才可多選產生議程)
                                        var title = rptTitles[0];
                                        var canCreate = true;
                                        for (o in rptTitles) {
                                            if (title != rptTitles[o]) {
                                                canCreate = false;
                                            }
                                        }
                                        if (list == "") {
                                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
                                            return;
                                        }
                                        else 
                                            if (count > 1 && !canCreate) {
                                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.error2"]);
                                                return;
                                            }
                                            else {
                                                $.capFileDownload({
                                                    handler: "lmsdownloadformhandler",
                                                    data: {
                                                        sign: sign,
                                                        fileName: "LMSDoc1.htm",
                                                        listOid: list,
                                                        txCode: txCode,
                                                        docTempType: "LMSDoc1",
                                                        fileDownloadName: "LMSDoc1.doc",
                                                        serviceName: "lms1201docservice"
                                                    }
                                                });
                                                $.thickbox.close();
                                            }
                                    }
                                    else 
                                        if (val == "2") {
                                            // 決議錄(同會期之全部文件)
                                            var ids = $("#gridview").jqGrid('getDataIDs');
                                            var list = "";
                                            var sign = ",";
                                            var ids2 = new Array();
                                            ids2 = $("#gridview").getGridParam('selarrrow');
                                            var list2 = "";
                                            var sRptTitle = "";
                                            var count = 0;
                                            for (var id2 in ids2) {
                                                var rows2 = $("#gridview").jqGrid('getRowData', ids2[id2]);
                                                if (rows2.oid != 'undefined' && rows2.oid != null && rows2.oid != "") {
                                                    if (rows2.rptTitle2 == 'undefined' || rows2.rptTitle2 == null || rows2.rptTitle2 == '') {
                                                    /*
                                         CommonAPI.showMessage(i18n.lms1205v01["l120v01.error5"]);
                                         return;
                                         */
                                                    }
                                                    else {
                                                        list2 += ((list2 == "") ? "" : sign) + rows2.oid;
                                                        sRptTitle += ((sRptTitle == "") ? "" : sign) + rows2.rptTitle2;
                                                    }
                                                }
                                                count++;
                                            }
                                            if (count > 1) {
                                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.error2"]);
                                                return;
                                            }
                                            
                                            if (sRptTitle == 'undefined' || sRptTitle == null || sRptTitle == '') {
                                                // 沒有登錄會期，跳過
                                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.error5"]);
                                                return;
                                            }
                                            list = ajaxPcTitle(list2);
                                            $.thickbox.close();
                                            $.capFileDownload({
                                                handler: "lmsdownloadformhandler",
                                                data: {
                                                    sign: sign,
                                                    fileName: "LMSDoc3.htm",
                                                    listOid: list,
                                                    selOid: list2,
                                                    txCode: txCode,
                                                    docTempType: "LMSDoc2",
                                                    fileDownloadName: "LMSDoc3.doc",
                                                    serviceName: "lms1201docservice"
                                                }
                                            });
                                        }
                                        else {
                                            // 決議錄(同會期之勾選文件)
                                            var ids = new Array();
                                            ids = $("#gridview").getGridParam('selarrrow');
                                            var list = "";
                                            var sign = ",";
                                            var rptTitles = [];
                                            var selOid = "";
                                            //var count = 0;
                                            for (var id in ids) {
                                                var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                                if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
                                                    list += ((list == "") ? "" : sign) + rows.oid;
                                                    selOid = rows.oid;
                                                    if (rows.rptTitle2 == 'undefined' || rows.rptTitle2 == null || rows.rptTitle2 == '') {
                                                        CommonAPI.showMessage(i18n.lms1205v01["l120v01.error5"]);
                                                        return;
                                                    }
                                                    else {
                                                        rptTitles.push(rows.rptTitle2);
                                                    }
                                                }
                                            //count++;
                                            }
                                            if (list == "") {
                                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
                                                return;
                                            }
                                            /*
                             else if (count > 1) {
                             CommonAPI.showMessage(i18n.lms1205v01["l120v01.error2"]);
                             return;
                             }
                             */
                                            else {
                                                // 開始檢查會期是否重覆
                                                $.ajax({
                                                    type: "POST",
                                                    handler: "lms1205formhandler",
                                                    data: {
                                                        formAction: "checkRptTitles",
                                                        rptTitles: rptTitles,
                                                        selOid: selOid
                                                    }
												}).done(function(responseData){
                                                        $.capFileDownload({
                                                            handler: "lmsdownloadformhandler",
                                                            data: {
                                                                sign: sign,
                                                                fileName: "LMSDoc3.htm",
                                                                listOid: list,
                                                                selOid: responseData.resOid,
                                                                txCode: txCode,
                                                                docTempType: "LMSDoc2",
                                                                fileDownloadName: "LMSDoc3.doc",
                                                                serviceName: "lms1201docservice"
                                                            }
                                                        });
                                                });
                                                $.thickbox.close();
                                            }
                                        }
                                },
                                "l120v01.thickbox2": function(){
                                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                                        if (res) {
                                            $.thickbox.close();
                                        }
                                    });
                                }
                            }
                        });
                    }
                    else {
                        $("#createLMSThick").thickbox({ // 使用選取的內容進行彈窗
                            title: i18n.lms1205v01["l120m01a.btnCreate"],
                            width: 500,
                            height: 200,
                            modal: true,
                            valign: "bottom",
                            align: "center",
                            i18n: i18n.lms1205v01,
                            buttons: {
                                "l120v01.thickbox1": function(){
                                    var val = $("input[name='selectKind']:checked").val();
                                    if (val == "1") {
                                        // 授信案件明細表
                                        var ids = new Array();
                                        ids = $("#gridview").getGridParam('selarrrow');
                                        var list = "";
                                        var sign = ",";
                                        for (var id in ids) {
                                            var rows = $("#gridview").jqGrid('getRowData', ids[id]);
                                            if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
                                                list += ((list == "") ? "" : sign) + rows.oid;
                                            }
                                        }
                                        if (list == "") {
                                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
                                            return;
                                        }
                                        else {
                                            $.capFileDownload({
                                                handler: "lmsdownloadformhandler",
                                                data: {
                                                    sign: sign,
                                                    fileName: "LMSDoc71.htm",
                                                    listOid: list,
                                                    txCode: txCode,
                                                    docTempType: "LMSDoc7",
                                                    fileDownloadName: "LMSDoc71.doc",
                                                    serviceName: "lms1201docservice"
                                                }
                                            });
                                            $.thickbox.close();
                                        }
                                    }
                                    else {
                                    // 本功能尚未實作
                                    }
                                },
                                "l120v01.thickbox2": function(){
                                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                                        if (res) {
                                            $.thickbox.close();
                                        }
                                    });
                                }
                            }
                        });
                    }
    }).end().find("#btnCntrNoControl").click(function(){
    	CntrNo_LN_CONTROL_Action.runQuery();
    }).end().find("#btnProduceExcel").click(function(){
    	$.form.submit({
            url: "../simple/FileProcessingService",
            target: "_blank",
            data: $.extend($("#filterForm").serializeData(), {
         	   txCode:txCode,
         	   mainDocStatus: viewstatus,
               fileDownloadName: "LMS1205.xls",
               serviceName: "lms1200xlsservice"
            })
        });
    }).end().find("#btnReBackApproveUnit").click(function(){
    	//J-113-0306 Eloan>企業授信>案件簽報書送呈區域中心審核後，若被退件，在「待補件/撤件」中之被撤件之案件，能否設計可以再撈到編製中重新簽報，以增進作業效率
    	//撤件案件重新傳回分行編製中
        var row = $("#gridview").getGridParam('selrow');
        var oid = "";
        var data = $("#gridview").getRowData(row);
        oid = data.oid;
        if (oid == undefined) {
            CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
            return;
        }
        // confirmDelete=撤件案件重新傳回分行編製中
        CommonAPI.confirmMessage(i18n.lms1205v01["l120v05.alert01"], function(b){
            if (b) {
                $.ajax({
                    type: "POST",
                    handler: "lms1205formhandler",
                    data: {
                        formAction: "reBackL120m01a",
                        oid: oid
                    }
				}).done(function(responseData){
                    	//CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);	                       
                    	$("#gridview").trigger("reloadGrid");// 更新Grid內容
                });
            }
        });
    });
    
    //列印
	if($("#printAreaGrid").length > 0) { // 2025/06/20 新增判斷是否頁面已存在此元素
	    $("#printAreaGrid").iGrid({
        handler: 'lms1205gridhandler',
        height: 350,
        sortname: 'caseDate',
		divWidth:0, // 2025/06/20 新增div長度設定
        postData: {
            formAction: "queryPrintArea",
            rowNum: 15
        },
        rowNum: 15,
        //  multiselect: true,
        // hideMultiselect: false,
        colModel: [{
            colHeader: i18n.lms1205v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70,
            sortable: true,
            name: 'caseDate'
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80,
            sortable: true,
            name: 'custId'
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 120,
            sortable: true,
            name: 'custName'
        }, {
            colHeader: i18n.lms1205v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150,
            sortable: true,
            name: 'caseNo'
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 110,
            sortable: true
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50,
            sortable: true,
            name: 'docStatus'
        }, {
            colHeader: i18n.lms1205v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 90,
            sortable: true,
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }]
    });
	}
    
    //控制Grid符合條件時顯示/隱藏某欄位
    if (viewstatus == "06O|LCH" && (userInfo.unitType == "2")) {
        jQuery("#gridview").jqGrid('showCol', ["gist"]);
    }
    else {
        jQuery("#gridview").jqGrid('hideCol', ["gist"]);
    }
});

//案件格式變更
function changeCase(){
    showType()
    var $LMS1205V01Form = $("#LMS1205V01Form");
    var row = $("#gridview").getGridParam('selrow');
    var list = "";
    var data = $("#gridview").getRowData(row);
    list = data.oid;
    list = (list == undefined ? "" : list);
    if (list != "") {
        $.ajax({
            type: "POST",
            handler: "lms1205formhandler",
            data: {
                formAction: "getCaseFormate",
                LMS1205V01Form: JSON.stringify($LMS1205V01Form.serializeData()),
                oid: list
            }
		}).done(function(responseData){
                var $LMS1205V01Form = $("#LMS1205V01Form");
                // alert(JSON.stringify(responseData));
                $LMS1205V01Form.reset();
                var odocType = responseData.LMS1205V01Form.docType;
                var odocKind = responseData.LMS1205V01Form.docKind;
                var odocCode = responseData.LMS1205V01Form.docCode;
                $LMS1205V01Form.find("[name='docType']").each(function(i){
                    if ($(this).val() == responseData.LMS1205V01Form.docType) {
                        $(this).prop("checked", true);
                        //---
                        $(this).closest("label").show();
                    }else{
                    	$(this).closest("label").hide();
                    }
                });
                $LMS1205V01Form.find("[name='docKind']").each(function(j){
                    if ($(this).val() == responseData.LMS1205V01Form.docKind) {
                        $(this).prop("checked", true);
                    }
                });
                $LMS1205V01Form.find("[name='docCode']").each(function(k){
                    if ($(this).val() == responseData.LMS1205V01Form.docCode) {
                        $(this).prop("checked", true);
                    }
                });

                loadCaseTypeItem(responseData.LMS1205V01Form.docCode);
                $LMS1205V01Form.find("[name='miniFlag'][value='"+responseData.LMS1205V01Form.miniFlag+"']:radio").prop( "checked" , true ).trigger('click');
                $LMS1205V01Form.find("#caseType").val(responseData.LMS1205V01Form.caseType);
                setUI();

                var openThickbox = $("#LMS1205V01Thickbox").thickbox({ // 使用選取的內容進行彈窗
                    title: i18n.lms1205v01["l120v01.thickbox12"],
                    width: 700,
                    height: 300,
                    align: 'center',
                    valign: 'bottom',
                    modal: true,
                    i18n: i18n.lms1205v01,
                    buttons: {
                        "l120v01.thickbox1": function(showMsg){
                            if ($LMS1205V01Form.find("[name='docType']:checked").val() != undefined &&
                            $LMS1205V01Form.find("[name='docKind']:checked").val() != undefined &&
                            $LMS1205V01Form.find("[name='docCode']:checked").val() != undefined) {
                                if ($LMS1205V01Form.find("[name='docType']:checked").val() == odocType &&
                                $LMS1205V01Form.find("[name='docKind']:checked").val() == odocKind &&
                                $LMS1205V01Form.find("[name='docCode']:checked").val() == odocCode) {
                                    CommonAPI.showMessage(i18n.lms1205v01["l120v01.error10"]);
                                    return;
                                }

                                if (showMiniFlag()){    // 簡易授信選項
                                   if ($LMS1205V01Form.find("[name='miniFlag']:checked").val() == undefined ){
                                       CommonAPI.showMessage(i18n.lms1201v01["l120v01.alert2"]);
                                       return;
                                   }
                                }else{  //清除背後預設值
                                    $LMS1205V01Form.find("[name='miniFlag'][value='N']:radio").prop( "checked" , true ).trigger('click');
                                }

                                if (showCaseType()){
                                   if ($LMS1205V01Form.find("#caseType").val() == undefined || $LMS1205V01Form.find("#caseType").val() == ''  ){
                                       CommonAPI.showMessage(i18n.lms1201v01["l120v01.alert2"]);
                                       return;
                                   }
                                }else{  //清除背後預設值
                                    $LMS1205V01Form.find("#caseType").val('');
                                }
                                $.ajax({
                                    type: "POST",
                                    handler: "lms1205formhandler",
                                    data: {
                                        formAction: "changeCase",
                                        oid: list
                                    }
								}).done(function(responseData){
                                        // alert(JSON.stringify(responseData));
                                        $("#gridview").trigger("reloadGrid"); // 更新Grid內容
                                        $.thickbox.close();
                                        $.thickbox.close();
                                        CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
                                });
                            }
                            else {
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert2"]);
                                return;
                            }
                        },
                        "l120v01.thickbox2": function(){
                            API.confirmMessage(i18n.def['flow.exit'], function(res){
                                if (res) {
                                    $.thickbox.close();
                                }
                            });
                        }
                    }
                });
        });
    }
    else {
        CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
    }
}

// 版本變更
function changeVer(){
    var row = $("#gridview").getGridParam('selrow');
    var list = "";
    var data = $("#gridview").getRowData(row);
    list = data.oid;
    list = (list == undefined ? "" : list);
    if (list != "") {
        $.ajax({
            type: "POST",
            handler: "lms1205formhandler",
            data: {
                formAction: "changeCaseVer",
                oid: list
            }
		}).done(function(responseData){
//                CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
        });
    } else {
        CommonAPI.showMessage(i18n.lms1201v01["l120v01.alert1"]);
    }
}

//條件變更/續約
function changeCase2(){
    showType();
    var row = $("#gridview").getGridParam('selrow');
    var list = "";
    var data = $("#gridview").getRowData(row);
    list = data.oid;
    list = (list == undefined ? "" : list);
    if (list != "") {
        $.ajax({
            type: "POST",
            handler: "lms1205formhandler",
            data: {
                formAction: "getCaseFormate",
                oid: list
            }
		}).done(function(responseData){
                // alert(JSON.stringify(responseData));
                $("#LMS1205V01Form").reset();
                $("#LMS1205V01Form").find("[name='docType']").each(function(i){
                    if ($(this).val() == responseData.LMS1205V01Form.docType) {
                        $(this).prop("checked", true);
                        //---
                        $(this).closest("label").show();
                    }else{
                    	$(this).closest("label").hide();
                    }
                });
                $("#LMS1205V01Form").find("[name='docKind']").each(function(j){
                    if ($(this).val() == responseData.LMS1205V01Form.docKind) {
                        $(this).prop("checked", true);
                    }
                });
                $("#LMS1205V01Form").find("[name='docCode']").each(function(k){
                    if ($(this).val() == responseData.LMS1205V01Form.docCode) {
                        $(this).prop("checked", true);
                    }
                });

                loadCaseTypeItem(responseData.LMS1205V01Form.docCode);
                $("#LMS1205V01Form").find("[name='miniFlag'][value='"+responseData.LMS1205V01Form.miniFlag+"']:radio").prop( "checked" , true ).trigger('click');
                $("#LMS1205V01Form").find("#caseType").val(responseData.LMS1205V01Form.caseType);
                setUI();

                var openThickbox = $("#LMS1205V01Thickbox").thickbox({ // 使用選取的內容進行彈窗
                    title: i18n.lms1205v01["l120v01.thickbox13"],
                    width: 700,
                    height: 300,
                    align: 'center',
                    valign: 'bottom',
                    modal: true,
                    i18n: i18n.lms1205v01,
                    buttons: {
                        "l120v01.thickbox1": function(showMsg){
                            if ($("#LMS1205V01Form").find("[name='docType']:checked").val() != undefined &&
                            $("#LMS1205V01Form").find("[name='docKind']:checked").val() != undefined &&
                            $("#LMS1205V01Form").find("[name='docCode']:checked").val() != undefined) {
                                if (showMiniFlag()){    // 簡易授信選項
                                   if ($("#LMS1205V01Form").find("[name='miniFlag']:checked").val() == undefined ){
                                       CommonAPI.showMessage(i18n.lms1201v01["l120v01.alert2"]);
                                       return;
                                   }
                                }else{  //清除背後預設值
                                    $("#LMS1205V01Form").find("[name='miniFlag'][value='N']:radio").prop( "checked" , true ).trigger('click');
                                }

                                if (showCaseType()){
                                   if ($("#LMS1205V01Form").find("#caseType").val() == undefined || $("#LMS1205V01Form").find("#caseType").val() == ''  ){
                                       CommonAPI.showMessage(i18n.lms1201v01["l120v01.alert2"]);
                                       return;
                                   }
                                }else{  //清除背後預設值
                                    $("#LMS1205V01Form").find("#caseType").val('');
                                }

                                $.ajax({
                                    type: "POST",
                                    handler: "lms1205formhandler",
                                    data: {
                                        formAction: "changeIf",
                                        oid: list
                                    }
								}).done(function(responseData){
                                        // alert(JSON.stringify(responseData));
                                        $("#gridview").trigger("reloadGrid"); // 更新Grid內容
                                        $.thickbox.close();
                                        CommonAPI.showMessage(responseData.runSuccess);
                                });
                            }
                            else {
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert2"]);
                            }
                        },
                        "l120v01.thickbox2": function(){
                            API.confirmMessage(i18n.def['flow.exit'], function(res){
                                if (res) {
                                    $.thickbox.close();
                                }
                            });
                        }
                    }
                });
        });
    }
    else {
        CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
    }
}

//營運中心 Grid顯示
function mainGrid5(){
    var L120M01aGrid = $("#gridview").iGrid({
        handler: 'lms1205gridhandler',
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        postData: {
            formAction: "queryL120m01a",
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        rowNum: 15,
        //		multiselect : true,
        //		hideMultiselect : false,
        colModel: [{
            colHeader: i18n.lms1205v01["l120m01a.other1"], // 案件經辦
            align: "left",
            width: 70, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: openDoc,
            name: 'areaAppraiser' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.other2"], // 單位別
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            //					formatter : 'click',
            //					onclick : openDoc,
            name: 'caseBrId' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.other3"], // 放行日期
            align: "left",
            width: 120, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            // onclick : function,
            name: 'areaSendInfo' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custname"], // 主要借款人
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'caseNo' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 100,
            sortable: true
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "areaBrId",
            name: 'areaBrId',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainid",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
		}, {
            colHeader: "typCd",
            name: 'typCd',	
			hidden: true	
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L120M01aGrid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
}

// 授管處 Grid顯示
function mainGrid4(){
    var L120M01aGrid = $("#gridview").iGrid({
        handler: 'lms1205gridhandler',
        width: 785,
        height: 350,
        //sortname: 'caseDate',
        sortname: 'caseBrId|endDate|caseNo',
        sortorder: 'asc|desc|asc',
        shrinkToFit: true,
        autowidth: false,
        postData: {
            formAction: "queryL120m01a",
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        rowNum: 15,
        //		multiselect : true,
        //		hideMultiselect : false,
        colModel: [{
            colHeader: i18n.lms1205v01["l120m01a.other1"], // 案件經辦
            align: "left",
            width: 70, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: openDoc,
            name: 'hqAppraiser' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.other2"], // 單位別
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            //					formatter : 'click',
            //					onclick : openDoc,
            name: 'caseBrId' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.other3"], // 放行日期
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: function(cellvalue, options, rowObject){
				return cellvalue == null || cellvalue == "0001-01-01" ? "" : cellvalue.substring(0,10);
			},
            name: 'sendLastTime' //name: 'endDate' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custname"], // 主要借款人
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'caseNo' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 100,
            sortable: true
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "areaBrId",
            name: 'areaBrId',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainid",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
		}, {
            colHeader: "typCd",
            name: 'typCd',	
			hidden: true	
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L120M01aGrid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
}

function mainGrid3(kind){
    var L120M01aGrid = $("#gridview").iGrid({
        handler: 'lms1205gridhandler',
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        postData: {
            formAction: "queryL120m01a3",
            mainDocStatus: viewstatus,
            kind: kind,
            rowNum: 15
        },
        rowNum: 15,
        multiselect: true,
        hideMultiselect: false,
        colModel: [{
            colHeader: i18n.lms1205v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70, // 設定寬度
            sortable: true, // 是否允許排序
            //			formatter : 'click',
            //			onclick : openDoc,
            name: 'caseDate' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: openDoc,
            name: 'custId' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'caseNo' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            hidden: true,
            name: 'docStatus' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: (kind == "4") ? i18n.lms1205v01["l120m01a.rpttitle3"] : i18n.lms1205v01["l120m01a.rpttitle1"],
            name: (kind == "4") ? 'rptTitle3' : 'rptTitle1', // kind == "4" =審計委員會
            width: 130, // 設定寬度
            sortable: true // 是否允許排序			
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "areaBrId",
            name: 'areaBrId',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "rptTitle3",
            name: 'rptTitle3',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
		}, {
            colHeader: "typCd",
            name: 'typCd',	
			hidden: true	
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L120M01aGrid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
}

function mainGrid2(){
    //    var L120M01aGrid = $("#gridview").iGrid({
    //        handler: 'lms1205gridhandler',
    //        height: 350,
    //        sortname: 'caseDate',
    //        shrinkToFit: false,
    //        autowidth: false,
    //        postData: {
    //            formAction: "queryL120m01a3",
    //            mainDocStatus: viewstatus,
    //            kind: "3",
    //            rowNum: 15
    //        },
    //        rowNum: 15,
    //        multiselect: true,
    //        hideMultiselect: false,
    //        colModel: [{
    //            colHeader: i18n.lms1205v01["l120m01a.other1"], // 案件經辦
    //            align: "left",
    //            width: 70, // 設定寬度
    //            sortable: true, // 是否允許排序
    //            formatter: 'click',
    //            onclick: openDoc,
    //            name: 'hqAppraiser' // col.id
    //        }, {
    //            colHeader: i18n.lms1205v01["l120m01a.other2"], // 單位別
    //            align: "left",
    //            width: 80, // 設定寬度
    //            sortable: true, // 是否允許排序
    //            //					formatter : 'click',
    //            //					onclick : openDoc,
    //            name: 'caseBrId' // col.id
    //        }, {
    //            colHeader: i18n.lms1205v01["l120m01a.other3"], // 放行日期
    //            align: "left",
    //            width: 120, // 設定寬度
    //            sortable: true, // 是否允許排序
    //            formatter: 'date',
    //            formatoptions: {
    //                srcformat: 'Y-m-d',
    //                newformat: 'Y-m-d'
    //            },
    //            // onclick : function,
    //            name: 'hqReceiveDate' // col.id
    //        }, {
    //            colHeader: i18n.lms1205v01["l120m01a.custname"], // 主要借款人
    //            align: "left",
    //            width: 150, // 設定寬度
    //            sortable: true, // 是否允許排序
    //            // formatter : 'click',
    //            // onclick : function,
    //            name: 'custName' // col.id
    //        }, {
    //            colHeader: i18n.lms1205v01["l120m01a.rpttitle2"], //常董會會期
    //            name: 'rptTitle2',
    //            width: 250, // 設定寬度
    //            sortable: true // 是否允許排序
    //        }, {
    //            colHeader: "areaDocstatus",
    //            name: 'areaDocstatus',
    //            hidden: true
    //        }, {
    //            colHeader: "areaBrId",
    //            name: 'areaBrId',
    //            hidden: true
    //        }, {
    //            colHeader: "docType",
    //            name: 'docType',
    //            hidden: true
    //        }, {
    //            colHeader: "docRslt",
    //            name: 'docRslt',
    //            hidden: true
    //        }, {
    //            colHeader: "docCode",
    //            name: 'docCode',
    //            hidden: true
    //        }, {
    //            colHeader: "docURL",
    //            name: 'docURL',
    //            hidden: true
    //        }, {
    //            colHeader: "oid",
    //            name: 'oid',
    //            hidden: true
    //        }, {
    //            colHeader: "mainid",
    //            name: 'mainId',
    //            hidden: true
    //        }, {
    //            colHeader: "ownBrId",
    //            name: 'ownBrId',
    //            hidden: true
    //        }, {
    //            colHeader: "authLvl",
    //            name: 'authLvl',
    //            hidden: true
    //        }],
    //        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
    //            var data = L120M01aGrid.getRowData(rowid);
    //            openDoc(null, null, data);
    //        }
    //    });
    var L120M01aGrid = $("#gridview").iGrid({
        handler: 'lms1205gridhandler',
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        postData: {
            formAction: "queryL120m01a3",
            mainDocStatus: viewstatus,
            kind: "3",
            rowNum: 15
        },
        rowNum: 15,
        multiselect: true,
        hideMultiselect: false,
        colModel: [{
            colHeader: i18n.lms1205v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70, // 設定寬度
            sortable: true, // 是否允許排序
            //			formatter : 'click',
            //			onclick : openDoc,
            name: 'caseDate' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: openDoc,
            name: 'custId' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'caseNo' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            hidden: true,
            name: 'docStatus' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.rpttitle2"], //常董會會期
            name: 'rptTitle2',
            width: 130, // 設定寬度
            sortable: true // 是否允許排序
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "areaBrId",
            name: 'areaBrId',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
		}, {
            colHeader: "typCd",
            name: 'typCd',	
			hidden: true	
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L120M01aGrid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
}

function mainGrid1(){
    var L120M01aGrid = $("#gridview").iGrid({
        handler: 'lms1205gridhandler',
        width: 785,
        height: 350,
        sortname: 'endDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: false,
        autowidth: false,
        postData: {
            formAction: "queryL120m01a",
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        localFirst: true,
        rowNum: 15,
        multiselect: true,
        hideMultiselect: false,
        colModel: [{
            colHeader: i18n.lms1205v01["l120m01a.approvetime"], // 核准日期
            align: "center",
            width: 65, // 設定寬度
            sortable: true, // 是否允許排序			
            name: 'endDate',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            hidden: viewstatus == '05O' ? false : true
        }, {
            colHeader: i18n.lms1205v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70, // 設定寬度
            sortable: true, // 是否允許排序
            //					formatter : 'click',
            //					onclick : openDoc,
            name: 'caseDate' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 85, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: openDoc,
            name: 'custId' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 120, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 165, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'caseNo' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.signno"], // 核准文號
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'signNo' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 140,
            sortable: true
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            hidden: (userInfo.unitType == "2") ? false : true,
            name: viewstatus == "05O" ? "reEstFlag" : "docStatus" // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 90, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id			 
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "areaBrId",
            name: 'areaBrId',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainid",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: i18n.lms1205v01["l120m01a.rpttitlearea1"], // 營運中心授審會會期
            name: 'rptTitleArea1',
            hidden: (userInfo.unitType == "2") ? false : true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
		}, {
            colHeader: "typCd",
            name: 'typCd',	
			hidden: true	
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L120M01aGrid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
}

/**
 * 異常通報案件Grid
 */
function mainGrid0(){
    var formMethod;
    formMethod = "queryL120m01a1";
    var L120M01aGrid = $("#gridview").iGrid({
        handler: 'lms1205gridhandler',
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        
        
        //2012-09-06 黃建霖 begin
        postData: {
            formAction: formMethod,
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        //2012-09-06 黃建霖 end
        
        
        
        rowNum: 15,
        // multiselect: true,hideMultiselect:false,
        colModel: [{
            colHeader: i18n.lms1205v01["l120m01a.approvetime"], // 核准日期
            align: "center",
            width: 65, // 設定寬度
            sortable: true, // 是否允許排序			
            name: 'endDate',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            hidden: viewstatus == '05O' ? false : true
        }, {
            colHeader: i18n.lms1205v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70, // 設定寬度
            sortable: true, // 是否允許排序
            //			formatter : 'click',
            //			onclick : openDoc,
            name: 'caseDate' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: openDoc,
            name: 'custId' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 120, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'caseNo' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 110,
            sortable: true
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: viewstatus == "05O" ? "reEstFlag" : "docStatus"// col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 90, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "hqMeetFlag",
            name: 'hqMeetFlag',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
		}, {
            colHeader: "typCd",
            name: 'typCd',	
			hidden: true	
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L120M01aGrid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
}

/**
 * 異常通報案件Grid
 */
function mainGrid0(){
    var formMethod;
    formMethod = "queryL120m01a1";
    var L120M01aGrid = $("#gridview").iGrid({
        handler: 'lms1201gridhandler',
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        
        
        //2012-09-06 黃建霖 begin
        postData: {
            formAction: formMethod,
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        //2012-09-06 黃建霖 end
        
        
        
        rowNum: 15,
        // multiselect: true,hideMultiselect:false,
        colModel: [{
            colHeader: i18n.lms1205v01["l120m01a.approvetime"], // 核准日期
            align: "center",
            width: 65, // 設定寬度
            sortable: true, // 是否允許排序			
            name: 'approveTime',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            hidden: viewstatus == '05O' ? false : true
        }, {
            colHeader: i18n.lms1205v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70, // 設定寬度
            sortable: true, // 是否允許排序
            //			formatter : 'click',
            //			onclick : openDoc,
            name: 'caseDate' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: openDoc,
            name: 'custId' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 120, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'caseNo' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 110,
            sortable: true
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: viewstatus == "05O" ? "reEstFlag" : "docStatus"// col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 90, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "hqMeetFlag",
            name: 'hqMeetFlag',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
		}, {
            colHeader: "typCd",
            name: 'typCd',	
			hidden: true	
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L120M01aGrid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
}

function mainGrid(){
    // 婉卻 && (918 or 營運中心)  ==> 呈現欄位
    var hidden060 = true;
    if(viewstatus.indexOf("06O") > -1){
        if(userInfo.unitNo == "918" || userInfo.unitNo == "931" || userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" || userInfo.unitNo == "934" || userInfo.unitNo == "935") {
                hidden060 = false;
        }
    }
    var formMethod;
    formMethod = "queryL120m01a";
    var L120M01aGrid = $("#gridview").iGrid({
        handler: 'lms1205gridhandler',
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        
        
        //2012-09-06 黃建霖 begin
        postData: {
            formAction: formMethod,
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        //2012-09-06 黃建霖 end
        
        
        
        rowNum: 15,
        // multiselect: true,hideMultiselect:false,
        colModel: [{
            colHeader: i18n.lms1205v01["l120m01a.approvetime"], // 核准日期
            align: "center",
            width: 65, // 設定寬度
            sortable: true, // 是否允許排序			
            name: 'endDate',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            hidden: viewstatus == '05O' ? false : hidden060
        }, {
            colHeader: i18n.lms1205v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70, // 設定寬度
            sortable: true, // 是否允許排序
            //			formatter : 'click',
            //			onclick : openDoc,
            name: 'caseDate' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: openDoc,
            name: 'custId' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 120, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'caseNo' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.signno"], // 核准文號
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'signNo', // col.id
            hidden: hidden060
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 110,
            sortable: true
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: viewstatus == "05O" ? "reEstFlag" : "docStatus"// col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 90, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: i18n.lms1205v01["l120v01.thickbox14"], // 婉卻/待陳復
            align: "center",
            width: 70, // 設定寬度			
            name: 'gist',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
		}, {
            colHeader: "typCd",
            name: 'typCd',
            hidden: true	
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L120M01aGrid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
}


function mainGridInit(){

    var formMethod;
    formMethod = "queryL120m01a";
    var L120M01aGrid = $("#gridview").iGrid({
        handler: 'lms1205gridhandler',
        width: 780,
        height: 350,
        sortname: 'endDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        localFirst: true,
        multiselect: true,
        //2012-09-06 黃建霖 begin
        
        postData: {
            formAction: formMethod,
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        
        //2012-09-06 黃建霖 end
        
        
        
        rowNum: 15,
        // multiselect: true,hideMultiselect:false,
        colModel: [{
            colHeader: i18n.lms1205v01["l120m01a.approvetime"], // 核准日期
            align: "center",
            width: 65, // 設定寬度
            sortable: true, // 是否允許排序			
            name: 'endDate',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            hidden: viewstatus == '05O' ? false : true
        }, {
            colHeader: i18n.lms1205v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70, // 設定寬度
            sortable: true, // 是否允許排序
            //			formatter : 'click',
            //			onclick : openDoc,
            name: 'caseDate' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: openDoc,
            name: 'custId' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 120, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'caseNo' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.signno"], // 核准文號
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'signNo', // col.id
            hidden: viewstatus == '05O' ? false : true
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 110,
            sortable: true
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: viewstatus == "05O" ? "reEstFlag" : "docStatus"// col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 90, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
		}, {
            colHeader: "typCd",
            name: 'typCd',	
			hidden: true
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L120M01aGrid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
}


function mainGridSea(){
    var formMethod;
    formMethod = "queryL121m01a";
    var L120M01aGrid = $("#gridview").iGrid({
        handler: 'lms1205gridhandler',
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|desc',
        shrinkToFit: true,
        autowidth: false,
        postData: {
            formAction: formMethod,
            mainDocStatus: viewstatus,
            rowNum: 15
        },
        rowNum: 15,
        // multiselect: true,hideMultiselect:false,
        colModel: [{
            colHeader: i18n.lms1205v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70, // 設定寬度
            sortable: true, // 是否允許排序
            //			formatter : 'click',
            //			onclick : openDoc,
            name: 'caseDate' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: openDoc,
            name: 'custId' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 120, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'caseNo' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 140,
            sortable: true
        }, {
            colHeader: i18n.lms1205v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50, // 設定寬度
            sortable: true, // 是否允許排序
            hidden: true,
            // formatter : 'click',
            // onclick : function,
            name: 'docStatus' // col.id
        }, {
            colHeader: i18n.lms1205v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 90, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: (userInfo.unitNo == "920" || userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935") ? 'areaAppraiser' : (userInfo.unitNo == "918") ? 'hqAppraiser' : 'updater' // col.id
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
		}, {
            colHeader: "typCd",
            name: 'typCd',	
			hidden: true	
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L120M01aGrid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
}

function login1(){
    $("#LMS1200V62Form1").reset();
    var ids = new Array();
    ids = $("#gridview").getGridParam('selarrrow');
    var list1 = "";
    var sign = ",";
    var count = 0;
    for (var id in ids) {
        var rows = $("#gridview").jqGrid('getRowData', ids[id]);
        if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
            list1 += ((list1 == "") ? "" : sign) + rows.oid;
        }
        count++;
    }
    /*
     var row1 = $("#gridview").getGridParam('selrow');
     var list1 = "";
     var data1 = $("#gridview").getRowData(row1);
     list1 = data1.oid;
     list1 = (list1 == undefined ? "" : list1);
     */
    if (list1 == "") {
        CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
        return;
    }
    else {
        $("#LMS1200V62Thickbox1").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.lms1205v01["l120v01.thickbox7"],
            width: 500,
            height: 200,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n : i18n.def,
            buttons: {
                "sure": function(){
                    var $LMS1200V62Form1 = $("#LMS1200V62Form1");
                    if ($LMS1200V62Form1.valid()) {
                        if ($LMS1200V62Form1.find("#rptTitle1b").val() < 1 ||
                        $LMS1200V62Form1.find("#rptTitle1b").val() > 12) {
                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.error3"]);
                            return;
                        }
                        else 
                            if ($LMS1200V62Form1.find("#rptTitle1c").val() < 1 ||
                            $LMS1200V62Form1.find("#rptTitle1c").val() > 31) {
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.error4"]);
                                return;
                            }
                            else 
                                if ($LMS1200V62Form1.find("#rptTitle1d").val() <=
                                0) {
                                    CommonAPI.showMessage(i18n.lms1205v01["l120v01.error6"]);
                                    return;
                                }
                                else 
                                    if ($LMS1200V62Form1.find("#rptTitle1a").val() <= 0) {
                                        CommonAPI.showMessage(i18n.lms1205v01["l120v01.error8"]);
                                        return;
                                    }
                                    else {
                                        $.ajax({
                                            type: "POST",
                                            handler: "lms1205formhandler",
                                            data: {
                                                formAction: "login1",
                                                LMS1200V62Form1: JSON.stringify($LMS1200V62Form1.serializeData()),
                                                oids: list1,
												isDelete : false,
                                                caseName: $LMS1200V62Form1.find("#rptTitle1e option:selected").html()
                                            }
										}).done(function(responseData){
                                                //更新授信簽報書Grid內容
                                                $("#gridview").trigger("reloadGrid");
                                        });
                                    }
                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                },
                "del": function(){
                    var $LMS1200V62Form1 = $("#LMS1200V62Form1");
                    API.confirmMessage(i18n.def['confirmDelete'], function(res){
						if(res){
							$.ajax({
                                type: "POST",
                                handler: "lms1205formhandler",
                                data: {
                                    formAction: "login1",
                                    LMS1200V62Form1: JSON.stringify($LMS1200V62Form1.serializeData()),
                                    oids: list1,
									isDelete : true,
                                    caseName: $LMS1200V62Form1.find("#rptTitle1e option:selected").html()
                                }
							}).done(function(responseData){
                                    //更新授信簽報書Grid內容
                                    $("#gridview").trigger("reloadGrid");
                            });
						}
						
						$.thickbox.close();
			        });
                }
            }
        });
    }
}

function login2(){
    $("#LMS1200V63Form1").reset();
    var ids = new Array();
    ids = $("#gridview").getGridParam('selarrrow');
    var list1 = "";
    var sign = ",";
    var count = 0;
    for (var id in ids) {
        var rows = $("#gridview").jqGrid('getRowData', ids[id]);
        if (rows.oid != 'undefined' && rows.oid != null && rows.oid != 0) {
            list1 += ((list1 == "") ? "" : sign) + rows.oid;
        }
        count++;
    }
    /*
     var row1 = $("#gridview").getGridParam('selrow');
     var list1 = "";
     var data1 = $("#gridview").getRowData(row1);
     list1 = data1.oid;
     list1 = (list1 == undefined ? "" : list1);
     */
    if (list1 == "") {
        CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
        return;
    }
    else {
        $("#LMS1200V63Thickbox1").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.lms1205v01["l120v01.thickbox8"],
            width: 500,
            height: 200,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $LMS1200V63Form1 = $("#LMS1200V63Form1");
                    if ($LMS1200V63Form1.valid()) {
                        if ($LMS1200V63Form1.find("#rptTitle1b").val() < 1 ||
                        $LMS1200V63Form1.find("#rptTitle1b").val() > 12) {
                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.error3"]);
                            return;
                        }
                        else 
                            if ($LMS1200V63Form1.find("#rptTitle1c").val() < 1 ||
                            $LMS1200V63Form1.find("#rptTitle1c").val() > 31) {
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.error4"]);
                                return;
                            }
                            else 
                                if ($LMS1200V63Form1.find("#rptTitle1d").val() <= 0) {
                                    CommonAPI.showMessage(i18n.lms1205v01["l120v01.error6"]);
                                    return;
                                }
                                else 
                                    if ($LMS1200V63Form1.find("#rptTitle1a").val() <= 0) {
                                        CommonAPI.showMessage(i18n.lms1205v01["l120v01.error8"]);
                                        return;
                                    }
                                    else {
                                        $.ajax({
                                            type: "POST",
                                            handler: "lms1205formhandler",
                                            data: {
                                                formAction: "login2",
                                                LMS1200V63Form1: JSON.stringify($LMS1200V63Form1.serializeData()),
                                                oids: list1,
												isDelete : false
                                            }
										}).done(function(responseData){
                                                //更新授信簽報書Grid內容
                                                $("#gridview").trigger("reloadGrid");
                                        });
                                    }
                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                },
                "del": function(){
                    var $LMS1200V63Form1 = $("#LMS1200V63Form1");
                    API.confirmMessage(i18n.def['confirmDelete'], function(res){
						if(res){
							$.ajax({
                                type: "POST",
                                handler: "lms1205formhandler",
                                data: {
                                    formAction: "login2",
                                    LMS1200V63Form1: JSON.stringify($LMS1200V63Form1.serializeData()),
                                    oids: list1,
									isDelete : true
                                }
							}).done(function(responseData){
                                    //更新授信簽報書Grid內容
                                    $("#gridview").trigger("reloadGrid");
                            });
						}
						
						$.thickbox.close();
			        });
                }
            }
        });
    }
}

function login3(){
    $("#LMS1200V64Form1").reset();
    var ids1 = new Array();
    ids1 = $("#gridview").getGridParam('selarrrow');
    var list1 = "";
    var sign1 = ",";
    var count1 = 0;
    for (var id1 in ids1) {
        var rows1 = $("#gridview").jqGrid('getRowData', ids1[id1]);
        if (rows1.oid != 'undefined' &&
        rows1.oid != null &&
        rows1.oid != 0) {
            list1 += ((list1 == "") ? "" : sign1) +
            rows1.oid;
        }
        count1++;
    }
    if (list1 == "") {
        CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
        return;
    }
    else {
        $("#LMS1200V64Thickbox1").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.lms1205v01["l120v01.thickbox9"],
            width: 500,
            height: 200,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $LMS1200V64Form1 = $("#LMS1200V64Form1");
                    if ($LMS1200V64Form1.valid()) {
                        if ($LMS1200V64Form1.find("#rptTitle1b").val() <
                        1 ||
                        $LMS1200V64Form1.find("#rptTitle1b").val() >
                        12) {
                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.error3"]);
                            return;
                        }
                        else 
                            if ($LMS1200V64Form1.find("#rptTitle1c").val() < 1 ||
                            $LMS1200V64Form1.find("#rptTitle1c").val() > 31) {
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.error4"]);
                                return;
                            }
                            else 
                                if ($LMS1200V64Form1.find("#rptTitle1d").val() <= 0) {
                                    CommonAPI.showMessage(i18n.lms1205v01["l120v01.error7"]);
                                    return;
                                }
                                else 
                                    if ($LMS1200V64Form1.find("#rptTitle1e").val() <= 0) {
                                        CommonAPI.showMessage(i18n.lms1205v01["l120v01.error6"]);
                                        return;
                                    }
                                    else 
                                        if ($LMS1200V64Form1.find("#rptTitle1a").val() <= 0) {
                                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.error8"]);
                                            return;
                                        }
                                        else {
                                            $.ajax({
                                                type: "POST",
                                                handler: "lms1205formhandler",
                                                data: {
                                                    formAction: "login3",
                                                    LMS1200V64Form1: JSON.stringify($LMS1200V64Form1.serializeData()),
                                                    oids: list1,
													isDelete : false,
                                                    caseName: $LMS1200V64Form1.find("#rptTitle1f option:selected").html()
                                                }
											}).done(function(responseData){
                                                    //更新授信簽報書Grid內容
                                                    $("#gridview").trigger("reloadGrid");
                                            });
                                        }
                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                },
                "del": function(){
                    var $LMS1200V64Form1 = $("#LMS1200V64Form1");
                    API.confirmMessage(i18n.def['confirmDelete'], function(res){
						if(res){
							$.ajax({
                                type: "POST",
                                handler: "lms1205formhandler",
                                data: {
                                    formAction: "login3",
                                    LMS1200V64Form1: JSON.stringify($LMS1200V64Form1.serializeData()),
                                    oids: list1,
									isDelete : true,
                                    caseName: $LMS1200V64Form1.find("#rptTitle1f option:selected").html()
                                }
							}).done(function(responseData){
                                    //更新授信簽報書Grid內容
                                    $("#gridview").trigger("reloadGrid");
                            });
						}
						
						$.thickbox.close();
			        });
                }
            }
        });
    }
}

/**
 * J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，請於授審處之授信管理系統→「授信審查」頁面→「案件簽報書」項下增加「提審計委員會」選項。
 */
function login4(){
    $("#LMS1200V65Form1").reset();
    var ids1 = new Array();
    ids1 = $("#gridview").getGridParam('selarrrow');
    var list1 = "";
    var sign1 = ",";
    var count1 = 0;
    for (var id1 in ids1) {
        var rows1 = $("#gridview").jqGrid('getRowData', ids1[id1]);
        if (rows1.oid != 'undefined' &&
        rows1.oid != null &&
        rows1.oid != 0) {
            list1 += ((list1 == "") ? "" : sign1) +
            rows1.oid;
        }
        count1++;
    }
    if (list1 == "") {
        CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert1"]);
        return;
    }
    else {
        $("#LMS1200V65Thickbox1").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.lms1205v01["l120v01.thickbox9"],
            width: 500,
            height: 200,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $LMS1200V65Form1 = $("#LMS1200V65Form1");
                    if ($LMS1200V65Form1.valid()) {
                        if ($LMS1200V65Form1.find("#rptTitle1b").val() <
                        1 ||
                        $LMS1200V65Form1.find("#rptTitle1b").val() >
                        12) {
                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.error3"]);
                            return;
                        }
                        else 
                            if ($LMS1200V65Form1.find("#rptTitle1c").val() < 1 ||
                            $LMS1200V65Form1.find("#rptTitle1c").val() > 31) {
                                CommonAPI.showMessage(i18n.lms1205v01["l120v01.error4"]);
                                return;
                            }
                            else 
                                if ($LMS1200V65Form1.find("#rptTitle1d").val() <= 0) {
                                    CommonAPI.showMessage(i18n.lms1205v01["l120v01.error7"]);
                                    return;
                                }
                                else 
                                    if ($LMS1200V65Form1.find("#rptTitle1e").val() <= 0) {
                                        CommonAPI.showMessage(i18n.lms1205v01["l120v01.error6"]);
                                        return;
                                    }
                                    else 
                                        if ($LMS1200V65Form1.find("#rptTitle1a").val() <= 0) {
                                            CommonAPI.showMessage(i18n.lms1205v01["l120v01.error8"]);
                                            return;
                                        }
                                        else {
                                            $.ajax({
                                                type: "POST",
                                                handler: "lms1205formhandler",
                                                data: {
                                                    formAction: "login4",
                                                    LMS1200V65Form1: JSON.stringify($LMS1200V65Form1.serializeData()),
                                                    oids: list1,
													isDelete : false,
                                                    caseName: i18n.lms1200v65["l120m01a.text2"]
                                                }
											}).done(function(responseData){
                                                    //更新授信簽報書Grid內容
                                                    $("#gridview").trigger("reloadGrid");
                                            });
                                        }
                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                },
                "del": function(){
                    var $LMS1200V65Form1 = $("#LMS1200V65Form1");
                    API.confirmMessage(i18n.def['confirmDelete'], function(res){
						if(res){
							$.ajax({
                                type: "POST",
                                handler: "lms1205formhandler",
                                data: {
                                    formAction: "login4",
                                    LMS1200V65Form1: JSON.stringify($LMS1200V65Form1.serializeData()),
                                    oids: list1,
									isDelete : true,
                                    caseName: i18n.lms1200v65["l120m01a.text2"]
                                }
							}).done(function(responseData){
                                    //更新授信簽報書Grid內容
                                    $("#gridview").trigger("reloadGrid");
                            });
						}
						
						$.thickbox.close();
			        });
                }
            }
        });
    }
}
/**
 * 打開ThickBox功能
 */
function thickBoxOpen(){
    showType();
    // 負責處理打開ThickBox功能
    $("#LMS1205V01Form").reset();
    setUI();
    if(true){
    	//在「變更格式」時，不能 企金←→消金
    	//在新增時，要把隱藏的 docType 顯示
    	$("#LMS1205V01Form").find("[name='docType']").closest("label").show()
    }    
    var openThickbox = $("#LMS1205V01Thickbox").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.lms1205v01["l120v01.thickbox3"],
        width: 700,
        height: 300,
        align: 'center',
        valign: 'bottom',
        modal: true,
        i18n: i18n.lms1205v01,
        buttons: {
            "l120v01.thickbox1": function(showMsg){
                var $LMS1205V01Form = $("#LMS1205V01Form");
                if ($LMS1205V01Form.find("[name='docType']:checked").val() != undefined &&
                $LMS1205V01Form.find("[name='docKind']:checked").val() != undefined &&
                $LMS1205V01Form.find("[name='docCode']:checked").val() != undefined) {

                    if (showMiniFlag()){    // 簡易授信選項
                       if ($LMS1205V01Form.find("[name='miniFlag']:checked").val() == undefined ){
                           CommonAPI.showMessage(i18n.lms1201v01["l120v01.alert2"]);
                           return;
                       }
                    }else{  //清除背後預設值
                        $LMS1205V01Form.find("[name='miniFlag'][value='N']:radio").prop( "checked" , true ).trigger('click');
                    }

                    if (showCaseType()){
                       if ($LMS1205V01Form.find("#caseType").val() == undefined || $LMS1205V01Form.find("#caseType").val() == ''  ){
                           CommonAPI.showMessage(i18n.lms1201v01["l120v01.alert2"]);
                           return;
                       }
                    }else{  //清除背後預設值
                        $LMS1205V01Form.find("#caseType").val('');
                    }
                    //$.thickbox.close();
					// 開始新增前的異常通報明細停權檢查
                    $.ajax({
                        type: "POST",
                        handler: "lms1201formhandler",
                        data: {
                            formAction: "checkIsStop",
                            docKind: $LMS1205V01Form.find("[name='docKind']:checked").val(),
                            docCode: $LMS1205V01Form.find("[name='docCode']:checked").val(),
							docType: $LMS1205V01Form.find("[name='docType']:checked").val(),
                            isNew: true  //編製中案新增簽報書，用來判斷要不要顯示授權等及authLvl
                        }
					}).done(function(responseData){
							if (responseData.addMsg) {
								CommonAPI.confirmMessage(i18n.lmscommom["other.msg26a"], function(b){
                                    if (b) {
										$.ajax({
											type: "POST",
											handler: "lms1205formhandler",
											data: {
												formAction: "addL120m01a",
												LMS1205V01Form: JSON.stringify($LMS1205V01Form.serializeData()),
												isStop: true,
												showMsg: showMsg
											}
										}).done(function(responseData){
												var $LMS1205V01Form = $("#LMS1205V01Form");
												// alert(JSON.stringify(responseData));
												// alert(responseData.docURL);
												CommonAPI.triggerOpener("gridview", "reloadGrid");
												$("#gridview").trigger("reloadGrid"); // 更新Grid內容
												var docType = $LMS1205V01Form.find("[name='docType']:checked").val();
												var docKind = $LMS1205V01Form.find("[name='docKind']:checked").val();
												var docCode = $LMS1205V01Form.find("[name='docCode']:checked").val();
												var url = '..' + responseData.docURL + '/02';
												$.form.submit({
													url: url,
													data: {
														mainDocStatus: viewstatus,
														mainId: responseData.mainId,
														mainOid: responseData.oid,
														docType: docType,
														docCode: docCode,
														docKind: docKind,
														docURL: responseData.docURL,
														ownBrId: responseData.ownBrId,
														caseBrId: responseData.caseBrId,
														authLvl: responseData.authLvl,
														areaChk: "",
														areaDocstatus: "",
														oid: responseData.oid,
														typCd: responseData.typCd
													},
													target: "_blank"
												});
												$.thickbox.close();
										});
									}	
								});	
							}else{
								$.ajax({
									type: "POST",
									handler: "lms1205formhandler",
									data: {
										formAction: "addL120m01a",
										LMS1205V01Form: JSON.stringify($LMS1205V01Form.serializeData()),
										isStop: true,
										showMsg: showMsg
									}
								}).done(function(responseData){
										var $LMS1205V01Form = $("#LMS1205V01Form");
										// alert(JSON.stringify(responseData));
										// alert(responseData.docURL);
										CommonAPI.triggerOpener("gridview", "reloadGrid");
										$("#gridview").trigger("reloadGrid"); // 更新Grid內容
										var docType = $LMS1205V01Form.find("[name='docType']:checked").val();
										var docKind = $LMS1205V01Form.find("[name='docKind']:checked").val();
										var docCode = $LMS1205V01Form.find("[name='docCode']:checked").val();
										var url = '..' + responseData.docURL + '/02';
										$.form.submit({
											url: url,
											data: {
												mainDocStatus: viewstatus,
												mainId: responseData.mainId,
												mainOid: responseData.oid,
												docType: docType,
												docCode: docCode,
												docKind: docKind,
												docURL: responseData.docURL,
												ownBrId: responseData.ownBrId,
												caseBrId: responseData.caseBrId,
												authLvl: responseData.authLvl,
												areaChk: "",
												areaDocstatus: "",
												oid: responseData.oid,
												typCd: responseData.typCd
											},
											target: "_blank"
										});
										$.thickbox.close();
								});
							}	
					});
                   
                }
                else {
                    CommonAPI.showMessage(i18n.lms1205v01["l120v01.alert2"]);
                }
            },
            "l120v01.thickbox2": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

// 額度明細表傳送聯行
function btnTableSend(){
    var id = $("#gridview").getGridParam('selrow');
    if (!id) {
        // action_005=請先選取一筆以上之資料列
        return CommonAPI.showMessage(i18n.def["action_005"]);
    }
    
    $.ajax({
        handler: "lms1415m01formhandler",
        data: {
            formAction: "queryBrankList"
        }
	}).done(function(obj){
            $("#brankSelect").setItems({
                item: obj,
                format: "{value} {key}",
                space: false
            });
    });
    
    var data = $("#gridview").getRowData(id);
    $("#tableSendBox").thickbox({
        // l120v01.thickbox11=額度明細表傳送聯行
        title: i18n.lms1205v01["l120v01.thickbox11"],
        width: 460,
        height: 150,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                $.ajax({
                    handler: "lms1415m01formhandler",
                    data: {
                        formAction: "copyCase",
                        brank: $("#brankSelect").val(),
                        oid: data.oid
                    }
				}).done(function(obj){
                        $("#gridview").trigger("reloadGrid");// 更新Grid內容
                });
                $.thickbox.close();
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function dateObjtoStr(tDate){
    return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" + (tDate.getDate() < 10 ? "0" : "") + tDate.getDate();
}

// 篩選
function OLD_openFilterBox_BK(){

    var $filterForm = $("#filterForm");
    // 初始化
    $filterForm.reset();
    //set default value
    var sysdate = CommonAPI.getToday().split("-");
    var endDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
    var fromDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
    fromDate.setMonth(fromDate.getMonth() - 12);
    
    $("#fromDate").val(dateObjtoStr(fromDate));
    $("#endDate").val(dateObjtoStr(endDate));
    
    var thickTitle;
    if (viewstatus == "05O") {
        thickTitle = i18n.lms1205v01['l120v05.title01'];
    }
    else 
        if (viewstatus == "06O") {
            thickTitle = i18n.lms1205v01['l120v06.title01'];
        }
    $("#filterBox").thickbox({
        // l120v05.title01=已核准受理查詢
        title: thickTitle,
        width: 500,
        height: 385,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                if (!$("#filterForm").valid()) {
                    return;
                }
                //2012-09-06 黃建霖 begin
                //只有在有輸入日期欄位時才要檢查
                if ($.trim($("#fromDate").val()) != "" || $.trim($("#endDate").val()) != "") {
                    if ($.trim($("#endDate").val()) == "" ||
                    $.trim($("#fromDate").val()) == "") {
                        // l120v05.message03=請輸入日期
                        return CommonAPI.showErrorMessage(i18n.lms1205v01["l120v05.message03"]);
                    }
                    
                    if ($("#fromDate").val() > $("#endDate").val()) {
                        // l120v05.message02=起始日期不能大於結束日期
                        return CommonAPI.showErrorMessage(i18n.lms1205v01["l120v05.message02"]);
                    }
                }
                //2012-09-06 黃建霖 end
                
                if (txCode == "337093") {
                    //025國際金融業務分行 > 海外聯貸案會簽
                    grid("queryL121m01a");
                }
                else 
                    if (txCode == "337008" || txCode == "339058" || txCode == "339038" || txCode == "331008") {
                        // 異常通報Grid
                        grid("queryL120m01a1");
                    }
                    else 
                        if (txCode == "339057" || txCode == "339037" || txCode == "339039" || txCode == "339041") {
                            grid("queryL120m01a");
                        }
                        else {
                        
                            grid("queryL120m01a2");
                        //gridDfd.resolve("queryL120m01a2");	
                        }
                $.thickbox.close();
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                    
                        //2012-09-06 黃建霖 begin
                        $filterForm.reset();
                        var end = CommonAPI.getToday().split("-");
                        
                        //var end = "2012-7-10".split("-");
                        
                        var endDate = new Date(end[0], end[1], end[2]);
                        var fromDate = new Date(end[0], end[1], end[2]);
                        fromDate.setYear(fromDate.getFullYear() - 1);
                        
                        $("#fromDate").val(fromDate.getFullYear() + "-" + (end[1]) + "-" + end[2]);
                        $("#endDate").val(endDate.getFullYear() + "-" + (end[1]) + "-" + end[2]);
                        
                        if (txCode == "337093") {
                        
                            grid("queryL121m01a");
                        }
                        else 
                            if (txCode == "337008" || txCode == "339058" || txCode == "339038" || txCode == "331008") {
                                // 異常通報Grid
                                grid("queryL120m01a1");
                            }
                            else 
                                if (txCode == "339057" || txCode == "339037" || txCode == "339039" || "339041") {
                                    grid("queryL120m01a");
                                }
                                else {
                                
                                    grid("queryL120m01a2");
                                }
                        //gridDfd.resolve("queryL120m01a2");	
                        //2012-09-06 黃建霖 end	
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//列印營運中心意見
function printArea(){



    $("#printAreaGrid").resetSelection();
    $("#PrintAreaBox").thickbox({
        title: "",
        width: 770,
        height: 500,
        modal: true,
        i18n: i18n.def,
        buttons: {
            "print": function(){
                var $grid = $("#printAreaGrid");
                //var ids = $grid.getGridParam('selarrrow');
                
                var ids = $grid.getGridParam('selrow');
                if (!ids || ids == "") {
                    // action_005=請先選取一筆以上之資料列
                    //grid.selrow=請先選擇一筆資料。
                    return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                }
                
                var mainId = $grid.getRowData(ids).mainId;
                
                $.capFileDownload({
                    handler: "lmsdownloadformhandler",
                    data: {
                        mainId: mainId,
                        rptOid: "R19" + "^^^^^",
                        otherData: "",
                        fileDownloadName: "LMS1205R109.pdf",
                        serviceName: "lms1205r01rptservice"
                    }
                });
            },
            "close": function(){
                $.thickbox.close();
            }
        }
    });
}

// 收件按鈕
function getCase(){

    var ids = $("#gridview").getGridParam('selarrrow');
    if (ids == "") {
        // action_005=請先選取一筆以上之資料列
        return CommonAPI.showMessage(i18n.def["action_005"]);
    }
    
    var oids = [];
    for (var i in ids) {
        oids.push($("#gridview").getRowData(ids[i]).oid);
    }
    
    $.ajax({
        handler: "lms1415m01formhandler",
        data: {
            formAction: "getCase",
            oids: oids
        }
	}).done(function(obj){
            $("#gridview").trigger("reloadGrid");
    });
    
}

function ajaxPcTitle(list2){
    var list = "";
    $.ajax({
        type: "POST",
        async: false,
        handler: "lms1205formhandler",
        data: $.extend({
            formAction: "getRptTitles",
            selOid: list2
        }, (txCode == "339041" && (userInfo.unitType == "2")) ? {
            isArea: true
        } : {})
	}).done(function(responseData){
            list = responseData.resOids;
            // 如果只有一筆資料時
            if (list == "") {
                list = list2;
            }
    });
    return list;
}

function showType(){
    $.ajax({
        type: "POST",
        async: false,
        handler: "lms1205formhandler",
        data: {
            formAction: "getType"
        }
	}).done(function(responseData){
            $("#LMS1205V01Form").find("#" + responseData.needToShow).show();
    });
}

// J-110-0458 企金授權內其他
function showCaseType(){
	//企金    案件別:案件簽報書(異常通報案件)
	var $LMS1205V01Form = $("#LMS1205V01Form");
    var miniFlag = $("#LMS1205V01Form").find("[name='miniFlag']:radio:checked").val();

	if( showMiniFlag() && miniFlag == 'Y' ){
		return true;
	}else{
		return false;
	}
}

function showMiniFlag(){
	var $LMS1205V01Form = $("#LMS1205V01Form");
    var docCode = $("#LMS1205V01Form").find("[name='docCode']:radio:checked").val();
	var docType = $("#LMS1205V01Form").find("[name='docType']:radio:checked").val();
	var docKind = $("#LMS1205V01Form").find("[name='docKind']:radio:checked").val();
	var miniFlag = $("#LMS1205V01Form").find("[name='miniFlag']:radio:checked").val();
	var caseType = $("#LMS1205V01Form").find("#caseType").val();
	if( (docType == '1' && docKind == '1' && docCode == '2' ) ){
		// 企金授權內其他
		return true;
	}else{
		return false;
	}
}

function loadCaseTypeItem(docCode){
	var comboName = "";
    var objs = null;
    if(docCode == "2"){ // 其他   國內為 lms130_2caseType
        comboName = "lms1305_2caseType";
    } else {
        comboName = "";
    }

    var objs = CommonAPI.loadCombos([comboName]);
    $("#caseType").setItems({
        space: false,
        item: objs[comboName],
        format: "{value} - {key}"
    });
}

function setUI(){
    var $LMS1205V01Form = $("#LMS1205V01Form");
    if (showMiniFlag()) {
		$LMS1205V01Form.find("#showMiniFlag").show();
	}else{
		$LMS1205V01Form.find("#showMiniFlag").hide();
		$LMS1205V01Form.find("#showCaseType").hide();
		$LMS1205V01Form.find("#caseType").val('');
	}

	if (showCaseType()) {
		$LMS1205V01Form.find("#showCaseType").show();
	}else{
		$LMS1205V01Form.find("#showCaseType").hide();
		$LMS1205V01Form.find("#caseType").val('');
	}
}

/**
 * 查詢使用者點擊的資料
 *
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc(cellvalue, options, rowObject){
    ilog.debug(rowObject);
    // alert(rowObject.docURL);
    var noOpenDoc = false;
    if (rowObject.areaDocstatus != null &&
    rowObject.areaDocstatus != undefined &&
    rowObject.areaDocstatus != "") {
        if (rowObject.areaBrId == userInfo.unitNo) {
            noOpenDoc = true;
        }
        else {
            noOpenDoc = false;
        }
    }
    if (rowObject.ownBrId != userInfo.unitNo) {
        noOpenDoc = true;
    }
    else {
        if (viewstatus == '05O') {
            // 已核准案件不鎖
            noOpenDoc = true;
        }
        else {
            noOpenDoc = false;
        }
    }
    
    var mainId;
    /*
     if(viewstatus == 'LWC' || viewstatus == 'LXC' || viewstatus == 'LYC'){
     mainId = rowObject.srcMainId;
     }else{
     mainId = rowObject.mainId;
     }
     */
    mainId = rowObject.mainId;
    var url = '..' + rowObject.docURL + '/02';
    var lockOption = {};
    if (noOpenDoc) {
        lockOption = {
            noOpenDoc: noOpenDoc
        };
    }
    $.form.submit({
        url: url,
        data: $.extend({
            mainDocStatus: viewstatus,
            mainId: mainId,
            mainOid: rowObject.oid,
            docType: rowObject.docType,
            docCode: rowObject.docCode,
            docKind: rowObject.docRslt,
            docURL: rowObject.docURL,
            ownBrId: rowObject.ownBrId,
            caseBrId: rowObject.caseBrId,
            authLvl: rowObject.authLvl,
            areaDocstatus: rowObject.areaDocstatus,
            areaBrId: rowObject.areaBrId,
            areaChk: rowObject.areaChk,
            oid: rowObject.oid,
			typCd:rowObject.typCd
        }, lockOption),
        target: rowObject.oid
    });
}

function grid(action, newQuery){
    var $filterForm = $("#filterForm");
    if (!$filterForm.find("#fxFlag").prop('checked')) {
	   $.thickbox.close();
	}else{
		if (newQuery == "Y") {
		   $.blockUI();
	    }else{
			$.thickbox.close();
		}
	}	
	
    $("#gridview").jqGrid("setGridParam", {
        postData: $.extend($("#filterForm").serializeData(), {
            handler: "lms1205gridhandler",
            formAction: action,
            docStatus: viewstatus,
            mainDocStatus: viewstatus,
            rowNum: 15,
            newQuery: newQuery
            /* in #filterForm,
             endDate: $("#endDate").val(),
             fromDate: $("#fromDate").val(),
             custId: $("#custId").val()*/
        }),
        loadComplete: function(){
			
            if (newQuery == "Y") {
				$.unblockUI()
			    $.thickbox.close();
                $("#gridview").jqGrid("setGridParam", {
                    postData: {
                        newQuery: "N"
                    }
                
                })
 
            }
            
        }
    }).trigger("reloadGrid");
    
}


// 新查詢-(含)全文檢索
function openFilterBox(){

    var $filterForm = $("#filterForm");
    // 初始化
    $filterForm.reset();
    //set default value
    var sysdate = CommonAPI.getToday().split("-");
    var endDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
    var fromDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
    fromDate.setMonth(fromDate.getMonth() - 12);
    
    $("#fromDate").val(dateObjtoStr(fromDate));
    $("#endDate").val(dateObjtoStr(endDate));
    
	if ( userInfo.unitType == "4") {
		$("#docKind").val('2'); //2025/06/20 調整修改value方法
	}
	
    if (userInfo.unitType == "2" || userInfo.unitType == "4") {
        //授管處、營運中心
        $filterForm.find("#a91t2").show();
        $filterForm.find("#s91t1f2").hide();
    }
    else {
        $filterForm.find("#a91t2").hide();
        $filterForm.find("#s91t2").hide();
    }
    
    var thickTitle;
    if (viewstatus == "05O") {
        thickTitle = i18n.lms1205v01['l120v05.title01'];
    }
    else 
        if (viewstatus == "06O") {
            thickTitle = i18n.lms1205v01['l120v06.title01'];
        }
    $("#filterBox").thickbox({
        // l120v05.title01=已核准受理查詢
        title: thickTitle,
        width: 500,
        height: 400,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                if (!$("#filterForm").valid()) {
                    return;
                }
                //2012-09-06 黃建霖 begin
                //只有在有輸入日期欄位時才要檢查
                if ($.trim($("#fromDate").val()) != "" || $.trim($("#endDate").val()) != "") {
                    if ($.trim($("#endDate").val()) == "" ||
                    $.trim($("#fromDate").val()) == "") {
                        // l120v05.message03=請輸入日期
                        return CommonAPI.showErrorMessage(i18n.lms1205v01["l120m01a.caseDate"]+i18n.lms1205v01["l120v05.message03"]);
                    }
                    
                    if ($("#fromDate").val() > $("#endDate").val()) {
                        // l120v05.message02=起始日期不能大於結束日期
                        return CommonAPI.showErrorMessage(i18n.lms1205v01["l120m01a.caseDate"]+i18n.lms1205v01["l120v05.message02"]);
                    }
                }
                
                if ($.trim($("#approveDateS").val()) != "" || $.trim($("#approveDateE").val()) != "") {
                    if ($.trim($("#approveDateE").val()) == "" ||
                    $.trim($("#approveDateS").val()) == "") {
                        // l120v05.message03=請輸入日期
                        return CommonAPI.showErrorMessage(i18n.lms1205v01["l120m01a.approveTime"]+i18n.lms1205v01["l120v05.message03"]);
                    }
                    
                    if ($("#approveDateS").val() > $("#approveDateE").val()) {
                        // l120v05.message02=起始日期不能大於結束日期
                        return CommonAPI.showErrorMessage(i18n.lms1205v01["l120m01a.approveTime"]+i18n.lms1205v01["l120v05.message02"]);
                    }
                }
                
                //2012-09-06 黃建霖 end
                
                if ($filterForm.find("#fxFlag").prop('checked')) {
                    //啟用進階查詢--要檢核
                    if ($filterForm.find("#fxLnSubject").val() == "" &&
                    $filterForm.find("#fxRateText").val() == "" &&
                    $filterForm.find("#fxOtherCondition").val() == "" &&
                    $filterForm.find("#fxReportOther").val() == "" &&
                    $filterForm.find("#fxReportReason1").val() == "" &&
                    $filterForm.find("#fxAreaOption").val() == "" &&
                    $filterForm.find("#fxCollateral").val() == "" &&
                    $filterForm.find("#fxBusCode").val() == "" &&
					$filterForm.find("#custId").val() == "" &&
					$filterForm.find("#custName").val() == "") {
                        return CommonAPI.showErrorMessage(i18n.lms1205v01["l120m01a.fxMessage01"]);
                        //return CommonAPI.showErrorMessage("已啟用進階查詢，請至少輸入一個進階查詢條件");
                    }
                    
                    if ($filterForm.find("#fromDate").val() == "" &&
                    $filterForm.find("#endDate").val() == "" &&
                    $filterForm.find("#approveDateS").val() == "" &&
                    $filterForm.find("#approveDateE").val() == "") {
                        return CommonAPI.showErrorMessage(i18n.lms1205v01["l120m01a.fxMessage02"]);
                        //return CommonAPI.showErrorMessage("已啟用進階查詢，請至少輸入一個日期查詢條件(期間不得超過一年)");
                    }
                    
					var diff1 = 0;
                    if ($.trim($("#fromDate").val()) != "" && $.trim($("#endDate").val()) != "") {
                        var mDateS = $("#fromDate").val().split("-");
                        var mDateE = $("#endDate").val().split("-");
                        var diffDay = ((mDateE[0] - mDateS[0]) * 12) + (mDateE[1] - mDateS[1]);
						diff1 = diffDay;
                        if (diffDay > 6) {
                            //查詢起迄日期區間不得相差半年以上
                            return CommonAPI.showErrorMessage(i18n.lms1205v01["l120m01a.caseDate"]+i18n.lms1205v01["l120m01a.fxMessage03"]);
                            //return false;
                        }
                    }
                    
                    var diff2 = 0;
                    if ($.trim($("#approveDateS").val()) != "" && $.trim($("#approveDateE").val()) != "") {
                        var mDateS = $("#approveDateS").val().split("-");
                        var mDateE = $("#approveDateE").val().split("-");
                        var diffDay = ((mDateE[0] - mDateS[0]) * 12) + (mDateE[1] - mDateS[1]);
						diff2 = diffDay;
                        if (diffDay > 6) {
                            //查詢起迄日期區間不得相差半年以上
                            return CommonAPI.showErrorMessage(i18n.lms1205v01["l120m01a.approveTime"]+i18n.lms1205v01["l120m01a.fxMessage03"]);
                            //return false;
                        }
                    }
                    
                    if ($.trim($("#fromDate").val()) != "" && $.trim($("#endDate").val()) != "" && $.trim($("#approveDateS").val()) != "" && $.trim($("#approveDateE").val()) != "") {
                    
                        
						if ((diff1 + diff2) > 6) {
                            //總查詢起迄日期區間不得相差半年以上
                            CommonAPI.showErrorMessage(i18n.lms1205v01["l120m01a.caseDate"]+"、"+i18n.lms1205v01["l120m01a.approveTime"]+i18n.lms1205v01["l120m01a.fxMessage04"]);
                            //return false;
                        }
                    }
                    
                    
                }
                
                
                if (txCode == "337093") {
                    //025國際金融業務分行 > 海外聯貸案會簽
                    grid("queryL121m01a", "Y");
                }
                else if (txCode == "337008" || txCode == "339058" || txCode == "339038" || txCode == "331008") {
                        // 異常通報Grid
                        grid("queryL120m01a1", "Y");
                }else if (txCode == "339057" || txCode == "339037" || txCode == "339039" || txCode == "339041") {
					    //免批覆案件(授管處)、陳復案/陳述案(營運中心)、免批覆案件(營運中心)、所有提會案件(營運中心)
                        grid("queryL120m01a", "Y");
                }else {
					    //grid("queryL120m01atmp1_1", "Y"); 
                       grid("queryL120m01a2","Y");	     
                }
                //$.thickbox.close();
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                    
					
                        //2012-09-06 黃建霖 begin
                        $filterForm.reset();
                        /*
                        var end = CommonAPI.getToday().split("-"); 
                        var endDate = new Date(end[0], end[1], end[2]);
                        var fromDate = new Date(end[0], end[1], end[2]);
                        fromDate.setYear(fromDate.getFullYear() - 1);
                        
                        $("#fromDate").val(fromDate.getFullYear() + "-" + (end[1]) + "-" + end[2]);
                        $("#endDate").val(endDate.getFullYear() + "-" + (end[1]) + "-" + end[2]);
                        
                        if (txCode == "337093") {
                            //025國際金融業務分行 > 海外聯貸案會簽
                            grid("queryL121m01a", "Y");
                        }
                        else 
                            if (txCode == "337008" || txCode == "339058" || txCode == "339038") {
                                // 異常通報Grid
                                grid("queryL120m01a1", "Y");
                            }
                            else 
                                if (txCode == "339057" || txCode == "339037" || txCode == "339039" || "339041") {
									//免批覆案件(授管處)、陳復案/陳述案(營運中心)、免批覆案件(營運中心)、所有提會案件(營運中心)
                                    grid("queryL120m01a", "Y");
                                }
                                else {
                                    //grid("queryL120m01atmp1_1", "Y");
									grid("queryL120m01a2");
                                }
                        //gridDfd.resolve("queryL120m01a2");	
                        //2012-09-06 黃建霖 end	
						* 
                         */
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}
