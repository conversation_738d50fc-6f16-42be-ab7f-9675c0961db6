package com.mega.eloan.lms.las.handler.form;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.lang.math.NumberUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.BranchDateTimeFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.las.service.LMS1915Service;
import com.mega.eloan.lms.mfaloan.bean.PTEAMAPP;
import com.mega.eloan.lms.mfaloan.service.MisELCUS21Service;
import com.mega.eloan.lms.mfaloan.service.MisELCUS27Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisIcbcBrService;
import com.mega.eloan.lms.mfaloan.service.MisLNF040Service;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisQuotainfService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L192M01B;
import com.mega.eloan.lms.model.L192M01C;
import com.mega.eloan.lms.model.L192M01E;
import com.mega.eloan.lms.model.L192S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * 查詢團貸業務工作底稿
 * 
 * <AUTHOR>
 * 
 */
@Scope("request")
@Controller("lms1915m01formhandler")
@DomainClass(L192M01A.class)
public class LMS1915M01FormHandler extends AbstractFormHandler {
	private static final int MAXLEN_L192M01B_TADDR = StrUtils.getEntityFileldLegth(L192M01B.class, "tAddr", 180);
	
	@Resource
	CLSService clsService;
	
	@Resource
	LMS1915Service lms1915Service;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userService;

	@Resource
	MisELCUS21Service misELCUS21Service;

	@Resource
	MisELCUS27Service misELCUS27Service;

	@Resource
	MisELLNGTEEService misELLNGTEEService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	MisIcbcBrService misIcbcBrService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	DocCheckService docCheckService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	MisQuotainfService misQuotainfService;

	@Resource
	MisPTEAMAPPService misPteamappService;

	@Resource
	MisLNF040Service misLNF040Service;
	
	@Resource
	MisdbBASEService misdbBaseService;

	/**
	 * 檢核對帳單是否需要列印
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	public IResult checkPrint(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = params.getString(EloanConstants.MAIN_OID);

		L192M01A meta = lms1915Service.getL192M01A(oid);
		Set<L192S01A> l192s01as = meta.getL192s01as();

		Set<L192S01A> returnData = new HashSet<L192S01A>();
		BigDecimal zero = BigDecimal.ZERO;
		StringBuffer returnMessage = new StringBuffer();
		int printSize = 0;
		StringBuffer totalMessage = new StringBuffer();
		totalMessage.append("申請內容共"+l192s01as.size()+"筆，");
		if (!l192s01as.isEmpty()){
			for (L192S01A l192s01a : l192s01as) {
				//確認LNF020寄送函證FLAG若為9:不寄送函證，則不納入列印，且將此筆資料送至前端提示不列印。
				boolean notice = true;
				if ("13".contains(branchService.getBranch(meta.getOwnBrId()).getBrNoFlag())){
					List<Map<String, Object>> noticetypes;
					try {
						noticetypes = misdbBaseService.findLNF020_NOTICE_TYPE(l192s01a.getQuotaNo());
					} catch (Exception e){
						throw new CapMessageException("與中心主機無法連線，請稍後再試。", this.getClass());
					}
					if (noticetypes != null){
						for (Map<String, Object> map : noticetypes){
							if ("9".equals(map.get("LNF020_NOTICE_TYPE"))){
								notice = false;
							}
						}
					}
				}
				if (notice){
					returnData.add(l192s01a);
					printSize++;
				} else {
					returnMessage.append("放款科目:"+l192s01a.getSubject()+" 帳號"+l192s01a.getAccNo()+" 額度序號"+l192s01a.getQuotaNo()+"已設定不寄送函證，不納入對帳單列印資料。<br>");
				}
			}
		}
		totalMessage.append("納入列印筆數共"+printSize+"筆。<br>");
		//2.有資料，前端會留存mainid供列印使用。無資料，會於前端顯示不列印訊息
		String mark;
		if (printSize == 0) {
			mark = "N";
			lms1915Service.printMark(meta, mark);
		} else if (printSize == l192s01as.size()){
			mark = "Y";
			lms1915Service.printMark(meta, mark);
		} else {
			mark = "P";
			lms1915Service.printMark(meta, mark);
		}
		result.set("L192S01A_PRINT_MARK", mark);
		result.set("NO_PRINT_DETAIL",totalMessage.append(returnMessage.toString()).toString());
		return result;
	}

	/**
	 * 從view的按鈕整批 稽核_呈主管覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult sendGAll(PageParameters params)
			throws CapException {

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A l192m01a = lms1915Service.getL192M01A(mainOid);
		lms1915Service.saveAndSendDocument(l192m01a);
		return result;
	}

	/**
	 * 從view的按鈕整批 分行_呈主管覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult sendAll(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A l192m01a = lms1915Service.getL192M01A(mainOid);
		lms1915Service.saveAndSendDocument(l192m01a);
		return result;
	}

	/**
	 * 從view的按鈕整批傳送稽核室
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult sendAllNextG(PageParameters params)
			throws CapException {

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A l192m01a = lms1915Service.getL192M01A(mainOid);
		lms1915Service.saveAndSendDocument(l192m01a);
		return result;
	}

	/**
	 * 稽核 覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = true)
	public IResult acceptG(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = null;
		if (mainOid != null) {
			meta = lms1915Service.getL192M01A(mainOid);
		}

		if (!hasEL02()) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0013"), getClass());
		}

		if (user.getUserId().equals(meta.getUpdater())) {
			// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}
		lms1915Service.flowControl(meta.getOid(), "稽核_核准");
		return result;
	}

	/**
	 * 分行 覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = true)
	public IResult accept(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = null;
		if (mainOid != null) {
			meta = lms1915Service.getL192M01A(mainOid);
		}

		if (!hasEL02()) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0013"), getClass());
		}

		if (user.getUserId().equals(meta.getUpdater())) {
			// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}
		lms1915Service.flowControl(meta.getOid(), "分行_核准");
		return result;
	}

	/**
	 * 稽核 退回編製中
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = true)
	public IResult returnG(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = null;
		if (mainOid != null) {
			meta = lms1915Service.getL192M01A(mainOid);
		}
		if (!hasEL02()) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0013"), getClass());
		}
		if (user.getUserId().equals(meta.getUpdater())) {
			// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}
		lms1915Service.flowControl(meta.getOid(), "稽核_退回");
		return result;
	}

	/**
	 * 分行 退回編製中
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = true)
	public IResult returnB(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = null;
		if (mainOid != null) {
			meta = lms1915Service.getL192M01A(mainOid);
		}
		if (!hasEL02()) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0013"), getClass());
		}
		if (user.getUserId().equals(meta.getUpdater())) {
			// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}
		lms1915Service.flowControl(meta.getOid(), "分行_退回");
		return result;
	}

	/**
	 * 傳送稽核室
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult sendNextG(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		L192M01A l192m01a = collectionData(params);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		lms1915Service.saveAndSendDocument(l192m01a);
		return result;
	}

	/**
	 * 稽核_呈主管覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult sendG(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		L192M01A l192m01a = collectionData(params);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		lms1915Service.saveAndSendDocument(l192m01a);

		return result;
	}

	/**
	 * 分行_呈主管覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult send(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		L192M01A l192m01a = collectionData(params);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		lms1915Service.saveAndSendDocument(l192m01a);

		return result;
	}

	/**
	 * 取得 申請內容資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getL192S01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("l192s01aoid");
		L192S01A l192s01a = lms1915Service.getL192S01A(oid);

		Map<String, IFormatter> fmt = new HashMap<String, IFormatter>();
		@SuppressWarnings("serial")
		IFormatter decimalFormat = new IFormatter() {
			DecimalFormat df = new DecimalFormat("#,###,###,###,###");

			@SuppressWarnings("unchecked")
			@Override
			public String reformat(Object in) throws CapFormatException {
				if (in != null) {
					return df.format(in);
				}
				return "";
			}
		};
		fmt.put("quotaAmt", decimalFormat);
		fmt.put("balAmt", decimalFormat);

		result.add(new CapAjaxFormResult(l192s01a.toJSONObject(new String[] {
				"balDate", "subject", "accNo", "quotaNo", "quotaCurr",
				"quotaAmt", "balCurr", "balAmt", "appDate", "signDate",
				"useDate", "fromDate", "endDate", "way", "appr", "checkDate",
				"checkCurr", "checkAmt", "endorser" }, fmt)));

		return result;
	}

	/**
	 * 刪除授信業務工作底稿
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL192M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = params.getString("deleteMainOid");
		String mainId = params.getString("deleteMainId");

		Map<String, String> lockedUser = docCheckService
				.listLockedDocUser(mainId);
		if (lockedUser == null) {
			lms1915Service.deleteL192M01A(oid);
		} else {
			String message = getPopMessage("EFD0055", lockedUser);
			result.set("deleteMessage", message);
		}

		// Map<String, String> lockedUser =
		// docCheckService.listLockedDocUser(mainId);
		// if (lockedUser == null) {
		// lms1905Service.deleteL192M01A(oid);
		// } else {
		// throw new CapMessageException(getPopMessage("EFD0055", lockedUser),
		// getClass());
		// }

		return result;
	}

	/**
	 * 新增團貸業務工作底稿資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addNew(PageParameters params)
			throws CapException {

		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		cal.add(Calendar.DATE, -1);
		Date yesterday = cal.getTime();

		Map<String, String> dpCode = misLNF040Service.getDpCode();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		JSONArray custIDs = JSONArray.fromObject(params.getString("custIDs"));
		String innerAudit = params.getString("innerAudit");

		// 團貸批號
		String grpCntrNo = params.getString("grpCntrNo");

		PTEAMAPP pteamapp = misPteamappService
				.getDataByLnf020GrpCntrNo(grpCntrNo);

		// 查核分行
		String brId = "Y".equals(innerAudit) ? user.getUnitNo() : params
				.getString("brId");

		// 回傳json array資料
		JSONArray returnJsonArray = new JSONArray();

		boolean flag = true;

		L192M01A l192m01a = null;

		if (pteamapp != null) {
			String custId = Util.trim(pteamapp.getCustid());
			String dupNo = "0";
			String custName = Util.trim(pteamapp.getProjectnm());
			l192m01a = new L192M01A();
			String uid_mainId = IDGenerator.getUUID();
			l192m01a.setRandomCode(IDGenerator.getRandomCode());
			l192m01a.setUid(uid_mainId);
			l192m01a.setMainId(uid_mainId);
			l192m01a.setCustId(custId);
			l192m01a.setDupNo(dupNo);
			l192m01a.setCustName(custName);

			// 1.查核授信業務稽核工作底稿
			// 2.查核房屋貸款稽核工作底稿
			l192m01a.setShtType(UtilConstants.ShtType.團體消貸工作底稿);
			l192m01a.setUnitType("1");
			l192m01a.setOwnBrId("Y".equals(innerAudit) ? user.getUnitNo()
					: params.getString("brId"));

			l192m01a.setInnerAudit(innerAudit);// 是否為內部查核

			l192m01a.setCreator(user.getUserId());
			l192m01a.setCreateTime(CapDate.getCurrentTimestamp());

			if ("Y".equals(innerAudit)) {
				// 如果是內部查核的話，取得登入分行的
				Map<String, Object> bankInfo = misIcbcBrService
						.getBankInfo(user.getUnitNo());
				String statementAddrFrom = (String) (bankInfo == null ? ""
						: bankInfo.get("ADDR"));
				l192m01a.setStatementAddrFrom(statementAddrFrom);
			} else {
				// 稽核查核的話，取得本身自己的單位
				// Map<String, Object> bankInfo = misIcbcBrService
				// .getBankInfo(user.getSsoUnitNo());
				Map<String, Object> bankInfo = misIcbcBrService
						.getBankInfo("906");
				String statementAddrFrom = (String) (bankInfo == null ? ""
						: bankInfo.get("ADDR"));
				l192m01a.setStatementAddrFrom(statementAddrFrom);
			}

			if (!CapString.isEmpty(params.getString("checkBase"))) {
				l192m01a.setCheckBase(CapDate.getDate(
						params.getString("checkBase"), "yyyy-MM-dd"));
			}

			if (!CapString.isEmpty(params.getString("checkDate"))) {
				l192m01a.setCheckDate(CapDate.getDate(
						params.getString("checkDate"), "yyyy-MM-dd"));
			}

			if (!CapString.isEmpty(params.getString("checkMan"))) {
				l192m01a.setCheckMan(params.getString("checkMan"));
			}

			if (!CapString.isEmpty(params.getString("leader"))) {
				l192m01a.setLeader(params.getString("leader"));
			}

		}

		// 新增多筆借款人資料

		List<L192M01B> l192m01bs = new ArrayList<L192M01B>();
		for (Object data : custIDs) {
			JSONObject d = (JSONObject) data;
			String custId_dupNo = d.getString("custid").toUpperCase();
			String custId = custId_dupNo
					.substring(0, custId_dupNo.length() - 1);
			String dupNo = custId_dupNo.substring(custId_dupNo.length() - 1,
					custId_dupNo.length()).toUpperCase();
			String custName = d.getString("name");

			L192M01B l192m01b = new L192M01B();

			l192m01b.setMainCustId(custId);
			l192m01b.setMainDupNo(dupNo);
			l192m01b.setCustId(custId);
			l192m01b.setDupNo(dupNo);
			l192m01b.setCustName(custName);
			l192m01b.setMainId(l192m01a.getMainId());
			l192m01b.setCustType("1");

			List<Map<String, Object>> custPhones = misELCUS27Service
					.getCustPhone(custId, dupNo);
			for (Map<String, Object> custPhone : custPhones) {
				String areano = custPhone.get("AREANO") == null ? ""
						: (String) custPhone.get("AREANO");
				String telno = custPhone.get("TELNO") == null ? ""
						: (String) custPhone.get("TELNO");
				String tTel = areano.trim() + telno.trim();
				l192m01b.settTel(tTel);

				// 取第一筆即可
				break;
			}

			String tAddr = this.getCustAddr(custId, dupNo);
			tAddr = CapString.halfWidthToFullWidth(tAddr); //轉全型
			if(clsService.is_function_on_codetype("l192m01b_tAddr_nosubstr")){
			}else{
				tAddr = Util.truncateString(tAddr, MAXLEN_L192M01B_TADDR);
			}
			l192m01b.settAddr(tAddr);

			// 團貸帳戶資料
			List<Map<String, Object>> groupLoanDatas = misdbBASEService
					.findGroupLoanDetail(custId + dupNo, grpCntrNo, brId);

			List<L192S01A> l192s01as = new ArrayList<L192S01A>();

			Set<String> contractNo = new HashSet<String>();

			for (Map<String, Object> groupLoanData : groupLoanDatas) {

				L192S01A l192s01a = new L192S01A();
				l192s01a.setMainCustId(custId);
				l192s01a.setMainDupNo(dupNo);
				l192s01a.setMainId(l192m01a.getMainId());

				String accNo = (String) groupLoanData.get("LNF030_LOAN_NO"); // 放款帳號
				String subject = dpCode.get(accNo.substring(4, 7));

				String quotaNo = (String) groupLoanData.get("LNF030_CONTRACT");// 額度序號

				contractNo.add(quotaNo);

				BigDecimal bal = (BigDecimal) groupLoanData
						.get("LNF030_LOAN_BAL");
				String balCurr = (String) groupLoanData.get("LNF030_SWFT");

				BigDecimal amt = (BigDecimal) groupLoanData
						.get("LNF020_FACT_AMT"); // 額度
				String amtCurr = (String) groupLoanData.get("LNF020_SWFT");

				Date begDate = (Date) groupLoanData.get("LNF020_BEG_DATE");
				Date endDate = (Date) groupLoanData.get("LNF020_END_DATE");
				Date openDate = (Date) groupLoanData.get("LNF030_OPEN_DATE");

				this.getQuotainfData(custId, dupNo, brId, quotaNo, l192s01a);
				l192s01a.setAccNo(accNo);
				l192s01a.setSubject(subject);
				l192s01a.setBalAmt(bal);
				l192s01a.setBalCurr(balCurr);
				l192s01a.setQuotaAmt(amt);
				l192s01a.setQuotaCurr(amtCurr);
				l192s01a.setQuotaNo(quotaNo);
				l192s01a.setFromDate(begDate);
				l192s01a.setEndDate(endDate);
				l192s01a.setBalDate(yesterday);
				l192s01a.setUseDate(openDate);
				l192s01as.add(l192s01a);

			}

			for (String cntrNo : contractNo) {

				List<Map<String, Object>> ellngteeDatas = misELLNGTEEService
						.getByCustIdCntrNo(l192m01b.getMainDupNo(),
								l192m01b.getMainDupNo(), cntrNo);

				for (Map<String, Object> ellngteeData : ellngteeDatas) {

					String lngeFlag = (String) (ellngteeData.get("LNGEFLAG") == null ? ""
							: ellngteeData.get("LNGEFLAG"));

					// C: 共同借款人
					// D: 共同發票人　
					// E: 票據債務人（指金融交易之擔保背書）
					// G: 連帶保證人，擔保品提供人兼連帶保證人
					// L: 連帶借款人，連帶債務人，擔保品提供人兼連帶債務人
					// S: 擔保品提供人
					// N: ㄧ般保證人
					L192M01B l192m01b2 = new L192M01B();
					if ("C".equals(lngeFlag)) {
						// 1.借款人, 2.連保人
						continue;
					} else {
						l192m01b2.setCustType("2");
					}

					String lngeId = (String) (ellngteeData.get("LNGEID") == null ? ""
							: ellngteeData.get("LNGEID"));
					String dupNo1 = (String) (ellngteeData.get("DUPNO1") == null ? ""
							: ellngteeData.get("DUPNO1"));

					String lngeNm = (String) (ellngteeData.get("LNGENM") == null ? ""
							: ellngteeData.get("LNGENM"));

					l192m01b2.setMainCustId(l192m01b.getMainCustId());
					l192m01b2.setMainDupNo(l192m01b.getDupNo());
					l192m01b2.setMainId(l192m01a.getMainId());
					l192m01b2.setCustId(lngeId);
					l192m01b2.setDupNo(dupNo1);

					l192m01b2.setCustName(lngeNm);

					l192m01bs.add(l192m01b2);
				}
			}

			l192m01b.setL192s01as(new HashSet<L192S01A>(l192s01as));

			L192M01E l192m01e = new L192M01E();

			l192m01e = new L192M01E();
			l192m01e.setMainCustId(l192m01b.getMainCustId());
			l192m01e.setMainDupNo(l192m01b.getMainDupNo());
			l192m01e.setMainId(l192m01b.getMainId());
			l192m01e.setCreator(user.getUserId());
			l192m01e.setCreateTime(new Date());

			l192m01e.setUserItem1(params.getString("userItem1"));
			l192m01e.setUserItem2(params.getString("userItem2"));
			l192m01e.setUserItem3(params.getString("userItem3"));
			l192m01e.setUpdater(user.getUserId());
			l192m01e.setUpdateTime(new Date());
			l192m01b.setL192m01e(l192m01e);
			l192m01bs.add(l192m01b);
		}

		l192m01a.setL192m01bs(new HashSet<L192M01B>(l192m01bs));
		lms1915Service.saveNewDocument(l192m01a);
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(l192m01a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(l192m01a.getMainId()));

		return result;

	}

	private void getQuotainfData(String custId, String dupNo, String brNo,
			String quotaNo, L192S01A l192s01a) {
		// 額度資訊檔
		Map<String, Object> quotainfData = misQuotainfService.getByKey(custId,
				dupNo, brNo, quotaNo);

		if (quotainfData != null) {
			// 申請日 得到的是民國年
			String appDate = (String) quotainfData.get("APPDATE");
			TWNDate _appDate = new TWNDate();
			_appDate.setTime(appDate);
			l192s01a.setAppDate(_appDate);

			// 核準日 得到的是民國年
			String signDate = (String) quotainfData.get("APRDATE");
			TWNDate _signDate = new TWNDate();
			_signDate.setTime(signDate);
			l192s01a.setSignDate(_signDate);

			// 進帳方式(存款帳號)
			String tAccNo = (String) quotainfData.get("ACCNO");

			String way = "";
			DecimalFormat df = new DecimalFormat("###,###,###,###");

			if (tAccNo != null && tAccNo.trim().length() > 0) {
				BigDecimal tRctAmt1 = (BigDecimal) quotainfData.get("RCTAMT1");

				way = "1.存款帳號：" + tAccNo + "，進帳金額：" + df.format(tRctAmt1) + "元";
			}

			String tRmtBh = (String) quotainfData.get("RMTBH");
			if (tRmtBh != null && tRmtBh.trim().length() > 0) {
				String tRmtNo = (String) quotainfData.get("RMTNO"); // 進帳方式(匯款-解款帳號)

				BigDecimal tRctAmt2 = (BigDecimal) quotainfData.get("RCTAMT2");// 進帳方式匯款之進帳金額

				way = way + "  2.匯款-解款銀行：" + tRmtBh + "，解款帳號：" + tRmtNo
						+ "，解款金額：" + df.format(tRctAmt2) + "元";
			}

			// 房貸有setWay(進帳方式/計息方式)
			l192s01a.setWay(way);

			// 核准者 appr VARCHAR(38)
			String tBossName = (String) quotainfData.get("BOSSNAME");// 核准主管姓名

			l192s01a.setAppr(tBossName);
		}
	}

	/**
	 * 查詢團貸業務工作底稿資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));

		L192M01A meta = null;
		if (mainOid != null) {
			meta = lms1915Service.getL192M01A(mainOid);
		}
		switch (page) {
		case 1:
			result.add(new CapAjaxFormResult(meta.toJSONObject(new String[] {
					"ownBrId", "checkBase", "docStatus", "checkDate",
					"checkMan", "tNo", "leader", "wpNo", "mtDoc", "randomCode",
					"statementAddrFrom" }, null)));
			// result.set("ownBrName",
			// branchService.getBranchName(meta.getOwnBrId()));
			result.set("ownBrName", new BranchNameFormatter(branchService,
					ShowTypeEnum.IDSpaceName).reformat(meta.getOwnBrId()));

			break;
		case 2:

			result.set("ownBrName",
					branchService.getBranchName(meta.getOwnBrId()));

			break;

		case 3:
			result.add(new CapAjaxFormResult(meta.toJSONObject(new String[] {
					"gist", "processComm" }, null)));

		default:
		}

		// required information

		IBranch iBranch = branchService.getBranch(MegaSSOSecurityContext
				.getUnitNo());

		if (meta.getCreator() == null) {
			result.set("creator", MegaSSOSecurityContext.getUserName());
			result.set("createTime", new BranchDateTimeFormatter(iBranch)
					.reformat(CapDate.parseToString(CapDate
							.getCurrentTimestamp())));
			result.set("updater", MegaSSOSecurityContext.getUserName());
			result.set("updateTime", new BranchDateTimeFormatter(iBranch)
					.reformat(CapDate.parseToString(CapDate
							.getCurrentTimestamp())));
		} else {
			result.set("creator", userService.getUserName(meta.getCreator()));
			result.set("createTime", new BranchDateTimeFormatter(iBranch)
					.reformat(CapDate.parseToString(meta.getCreateTime())));

			result.set("updater", userService.getUserName(meta.getUpdater()));
			result.set("updateTime", new BranchDateTimeFormatter(iBranch)
					.reformat(CapDate.parseToString(meta.getUpdateTime())));
		}
		result.set(EloanConstants.MAIN_OID, CapString.trimNull(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, CapString.trimNull(meta.getMainId()));
		result.set(EloanConstants.MAIN_UID, CapString.trimNull(meta.getUid()));
		result.set("docStatusCN",
				getMessage("docStatus." + meta.getDocStatus()));
		result.set("innerAudit", meta.getInnerAudit());
		return result;
	}

	/**
	 * 儲存
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult save(PageParameters params)
			throws CapException {

		L192M01A l192m01a = collectionData(params);
		l192m01a.setRandomCode(IDGenerator.getRandomCode());
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		lms1915Service.saveL192M01A(l192m01a);
		return query(params);

	}

	/**
	 * TEMP儲存
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult tempSave(PageParameters params)
			throws CapException {
		// CapAjaxFormResult result = new CapAjaxFormResult();
		L192M01A l192m01a = collectionData(params);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "Y"));
		lms1915Service.saveL192M01A(l192m01a);
		return query(params);
	}

	/**
	 * 資料處理，將網頁上所submit的資料做基本轉換
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	private L192M01A collectionData(PageParameters params)
			throws CapException {

		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A l192m01a = lms1915Service.getL192M01A(mainOid);

		// params = convertParameters(params);

		params.remove("custName");// 避免子文件的資料update到主檔
		params.remove("custId");
		params.remove("cdQ1");
		params.remove("cdQ2");
		params.remove("tAddr");
		params.remove("tTel");

		l192m01a = CapBeanUtil.map2Bean(params, l192m01a);

		switch (page) {
		case 1:
			if (l192m01a == null) {
				l192m01a = new L192M01A();
			}

			if (!CapString.isEmpty(params.getString("checkBase"))) {
				l192m01a.setCheckBase(CapDate.getDate(
						params.getString("checkBase"), "yyyy-MM-dd"));
			} else {
				l192m01a.setCheckBase(null);
			}

			if (!CapString.isEmpty(params.getString("checkDate"))) {
				l192m01a.setCheckDate(CapDate.getDate(
						params.getString("checkDate"), "yyyy-MM-dd"));
			} else {
				l192m01a.setCheckDate(null);
			}
			break;
		case 2:
			break;
		default:
			break;
		}

		return l192m01a;

	}

	/**
	 * 取得最近一份工作底稿資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	public IResult getLatestAuditSheet(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String shtType = params.getString("shtType");
		String innerAudit = params.getString("innerAudit");
		String brNo = "Y".equals(innerAudit) ? user.getUnitNo() : params
				.getString("brId");

		L192M01A l192m01a = lms1915Service
				.getLatestL192M01byBrNoShtTypeInnerAudit(brNo, shtType,
						innerAudit);
		L192M01C l192m01c = null;
		if (l192m01a != null) {
			l192m01c = l192m01a.getL192m01c();
		}

		result.set(
				"leader",
				l192m01a == null ? ""
						: CapString.trimNull(l192m01a.getLeader()));
		result.set(
				"userItem1",
				l192m01c == null ? "" : CapString.trimNull(l192m01c
						.getUserItem1()));
		return result;

	}

	/**
	 * 取得客戶地址資料
	 * 
	 * @param custId
	 *            customer id
	 * @param dupNo
	 *            dupNo
	 * @return address
	 */
	private String getCustAddr(String custId, String dupNo) {

		List<Map<String, Object>> custAddrs = misELCUS21Service
				.getCustAddressForLas(custId, dupNo);

		StringBuffer allAddress = new StringBuffer("");
		for (int i = 0; i < custAddrs.size(); i++) {
			Map<String, Object> custAddr = custAddrs.get(i);

			// J-105-0233-001 Web
			// e-Loan授信系統「註冊地址」及「聯絡地址」引進【0024】之「公司所在地」之建檔資料時可引進英文地址
			// String addrzip = custAddr.get("ADDRZIP") == null ? ""
			// : (String) custAddr.get("ADDRZIP");
			// String cityr = custAddr.get("CITYR") == null ? ""
			// : (String) custAddr.get("CITYR");
			// String townr = custAddr.get("TOWNR") == null ? ""
			// : (String) custAddr.get("TOWNR");
//			String addrr = custAddr.get("ADDRR") == null ? ""
//					: (String) custAddr.get("ADDRR");
			
			String addrr = custAddr.get("FULLADDR") == null ? ""
					: (String) custAddr.get("FULLADDR");
			
			allAddress.append(new StringBuffer().append(addrr.trim()));
			break;
		}
		return allAddress.toString();

	}

	/**
	 * 註記是否列印對帳單
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	public IResult printMark(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String printMark = params.getString("printMark");
		L192M01A meta = lms1915Service.getL192M01A(mainOid);
		if (meta != null) {
			lms1915Service.printMark(meta, printMark);
		}
		return result;
	}

	/**
	 * 判斷使用者是否有EL02角色
	 * 
	 * @return
	 */
	private boolean hasEL02() {
		Set<String> eloanRoles = MegaSSOSecurityContext.getEloanRoles();

		for (String role : eloanRoles) {
			if (role != null & role.endsWith("EL02")) {
				return true;
			}
		}

		return false;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryL192M01B(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = params.getString("l192m01bOid");
		String mainCustId = params.getString("mainCustId");
		String mainDupNo = params.getString("mainDupNo");

		L192M01B l192m01b = lms1915Service.getL192M01B(oid);

		if (l192m01b != null) {
			result.add(new CapAjaxFormResult(l192m01b.toJSONObject(
					new String[] { "mainCustId", "mainDupNo", "custType",
							"custId", "dupNo", "custName", "posi",
							"incomeCurr", "incomeAmt", "tTel", "tAddr", "cdQ1",
							"cdQ2" }, null)));
		}

		L192M01E l192m01e = l192m01b.getL192m01e();

		if (l192m01e != null) {
			result.add(new CapAjaxFormResult(l192m01e.toJSONObject(
					new String[] { "ck1", "ck2", "ck3", "ck4", "ck5", "ck6",
							"ck7", "userItem1", "userCk1", "userItem2",
							"userCk2", "userItem3", "userCk3" }, null)));
		}

		result.set("l192m01bOid", l192m01b.getOid());

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult updateL192M01B(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = params.getString("l192m01bOid");
		String mainCustId = params.getString("mainCustId");
		String mainDupNo = params.getString("mainDupNo");

		L192M01B l192m01b = lms1915Service.getL192M01B(oid);

		if (l192m01b != null) {

			l192m01b.setPosi(CapString.trimNull(params.getString("posi")));
			String incomeAmt = CapString
					.trimNull(params.getString("incomeAmt")).replace(",", "");

			if (NumberUtils.isNumber(incomeAmt)) {
				l192m01b.setIncomeAmt(CapMath.getBigDecimal(incomeAmt));
			}
			l192m01b.setCustName(CapString.trimNull(params
					.getString("custName")));
			l192m01b.settAddr(Util.truncateString(CapString.trimNull(params.getString("tAddr")), MAXLEN_L192M01B_TADDR));
			l192m01b.settTel(CapString.trimNull(params.getString("tTel")));

			l192m01b.setCdQ1(CapString.trimNull(params.getString("cdQ1")));
			l192m01b.setCdQ2(CapString.trimNull(params.getString("cdQ2")));
			l192m01b.setUpdater(user.getUserId());
			l192m01b.setUpdateTime(new Date());

			L192M01E l192m01e = l192m01b.getL192m01e();
			if (l192m01e == null) {
				l192m01e = new L192M01E();
				l192m01e.setMainCustId(l192m01b.getMainCustId());
				l192m01e.setMainDupNo(l192m01b.getMainDupNo());
				l192m01e.setMainId(l192m01b.getMainId());
				l192m01e.setCreator(user.getUserId());
				l192m01e.setCreateTime(new Date());
			}

			l192m01e.setCk1(params.getString("ck1"));
			l192m01e.setCk2(params.getString("ck2"));
			l192m01e.setCk3(params.getString("ck3"));
			l192m01e.setCk4(params.getString("ck4"));
			l192m01e.setCk5(params.getString("ck5"));
			l192m01e.setCk6(params.getString("ck6"));
			l192m01e.setCk7(params.getString("ck7"));
			l192m01e.setUserItem1(params.getString("userItem1"));
			l192m01e.setUserItem2(params.getString("userItem2"));
			l192m01e.setUserItem3(params.getString("userItem3"));
			l192m01e.setUserCk1(params.getString("userCk1"));
			l192m01e.setUserCk2(params.getString("userCk2"));
			l192m01e.setUserCk3(params.getString("userCk3"));
			l192m01e.setUpdater(user.getUserId());
			l192m01e.setUpdateTime(new Date());
			l192m01b.setL192m01e(l192m01e);
		}

		lms1915Service.saveL192M01B(l192m01b);

		return result;
	}

}
