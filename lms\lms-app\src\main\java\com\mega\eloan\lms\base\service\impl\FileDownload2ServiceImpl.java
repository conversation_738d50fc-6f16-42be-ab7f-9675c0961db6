package com.mega.eloan.lms.base.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.service.FileDownloadService;

import tw.com.iisi.cap.exception.CapException;

/**
 * 產生簽報書PDF
 * 
 * <AUTHOR>
 * 
 */
@Service("fileDownload2Service")
public class FileDownload2ServiceImpl implements FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(FileDownload2ServiceImpl.class);

	/*
	 * javascript用法
	 $.form.submit({
        url: "../../simple/FileProcessingService",
        target: "_blank",
        data: {
			fileName : "D:/xxx.pdf",
			fileDownloadName : "xxx.pdf",
			serviceName : "fileDownload2Service"
        }
    });
	 */
	/*
	 * (non-Javadoc) 呈現在頁面用的
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		String fileName = params.getString("fileName");
		FileInputStream fis = null;
		OutputStream outputStream = null;
		ByteArrayOutputStream baos = null;
		try {
			fis = new FileInputStream(fileName);
			byte[] buf = new byte[4 * 1024];  // 4K buffer
			int bytesRead;
			outputStream = new ByteArrayOutputStream();
			while ((bytesRead = fis.read(buf)) != -1) {
				outputStream.write(buf, 0, bytesRead);
			}
			outputStream.flush();
			baos = (ByteArrayOutputStream) outputStream;
			
			return baos.toByteArray();
		} catch (Exception ex) {
		} finally {
			if (fis != null) {
				try {
					fis.close();
				} catch (IOException ex) {
				}
			}
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
				}
			}
			if (outputStream != null) {
				try {
					outputStream.close();
				} catch (IOException ex) {
				}
			}

		}
		return null;
	}
}
