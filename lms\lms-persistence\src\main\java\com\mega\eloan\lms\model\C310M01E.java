 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** <pre>同一通訊處註記簽章欄
 * </pre>
 * 
 * @since 2019/02
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/02, J-107-0129
 *          </ul>
 */
@Entity
@Table(name="C310M01E", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","branchType","branchId","staffNo","staffJob"}))
public class C310M01E extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** mainId **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 單位類型<p/>
	 * 1. 分行<br/>
	 *  2. 母行/海外總行<br/>
	 *  3. 營運中心<br/>
	 *  4. 授管處<br/>
	 *  5. 徵信承作分行
	 */
	@Size(max=1)
	@Column(name="BRANCHTYPE", length=1, columnDefinition="CHAR(1)")
	private String branchType;

	/** 單位代碼 **/
	@Size(max=3)
	@Column(name="BRANCHID", length=3, columnDefinition="CHAR(3)")
	private String branchId;

	/** 行員代碼 **/
	@Size(max=6)
	@Column(name="STAFFNO", length=6, columnDefinition="CHAR(6)")
	private String staffNo;

	/** 
	 * 人員職稱<p/>
	 * L1. 分行經辦<br/>
	 *     (授管處/營運中心)<br/>
	 *  L2. 帳戶管理員<br/>
	 *  L3. 分行授信/覆核主管<br/>
	 *     (母行)<br/>
	 *  L4. 分行覆核主管<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  L5. 單位/授權主管<br/>
	 *     (母行)<br/>
	 *  L6. 分行單位主管<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  徵信報告或資信簡表簽核層級<br/>
	 *  C1. 徵信經辦<br/>
	 *  C2. 徵信覆核主管<br/>
	 *  C3. 經理/副理<br/>
	 *  C4. 處長/協理/主任/副主任<br/>
	 *  C5. 徵信單位主管
	 */
	@Size(max=2)
	@Column(name="STAFFJOB", length=2, columnDefinition="CHAR(2)")
	private String staffJob;

	/** 
	 * 承作日期<p/>
	 * 徵信報告或資信簡表資料日期？
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="CESDATE", columnDefinition="Date")
	private Date cesDate;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String Creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	@Column(name="SEQ", columnDefinition="DECIMAL(2,0)")
	private BigDecimal seq;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得單位類型<p/>
	 * 1. 分行<br/>
	 *  2. 母行/海外總行<br/>
	 *  3. 營運中心<br/>
	 *  4. 授管處<br/>
	 *  5. 徵信承作分行
	 */
	public String getBranchType() {
		return this.branchType;
	}
	/**
	 *  設定單位類型<p/>
	 *  1. 分行<br/>
	 *  2. 母行/海外總行<br/>
	 *  3. 營運中心<br/>
	 *  4. 授管處<br/>
	 *  5. 徵信承作分行
	 **/
	public void setBranchType(String value) {
		this.branchType = value;
	}

	/** 取得單位代碼 **/
	public String getBranchId() {
		return this.branchId;
	}
	/** 設定單位代碼 **/
	public void setBranchId(String value) {
		this.branchId = value;
	}

	/** 取得行員代碼 **/
	public String getStaffNo() {
		return this.staffNo;
	}
	/** 設定行員代碼 **/
	public void setStaffNo(String value) {
		this.staffNo = value;
	}

	/** 
	 * 取得人員職稱<p/>
	 * L1. 分行經辦<br/>
	 *     (授管處/營運中心)<br/>
	 *  L2. 帳戶管理員<br/>
	 *  L3. 分行授信/覆核主管<br/>
	 *     (母行)<br/>
	 *  L4. 分行覆核主管<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  L5. 單位/授權主管<br/>
	 *     (母行)<br/>
	 *  L6. 分行單位主管<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  徵信報告或資信簡表簽核層級<br/>
	 *  C1. 徵信經辦<br/>
	 *  C2. 徵信覆核主管<br/>
	 *  C3. 經理/副理<br/>
	 *  C4. 處長/協理/主任/副主任<br/>
	 *  C5. 徵信單位主管
	 */
	public String getStaffJob() {
		return this.staffJob;
	}
	/**
	 *  設定人員職稱<p/>
	 *  L1. 分行經辦<br/>
	 *     (授管處/營運中心)<br/>
	 *  L2. 帳戶管理員<br/>
	 *  L3. 分行授信/覆核主管<br/>
	 *     (母行)<br/>
	 *  L4. 分行覆核主管<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  L5. 單位/授權主管<br/>
	 *     (母行)<br/>
	 *  L6. 分行單位主管<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  徵信報告或資信簡表簽核層級<br/>
	 *  C1. 徵信經辦<br/>
	 *  C2. 徵信覆核主管<br/>
	 *  C3. 經理/副理<br/>
	 *  C4. 處長/協理/主任/副主任<br/>
	 *  C5. 徵信單位主管
	 **/
	public void setStaffJob(String value) {
		this.staffJob = value;
	}

	/** 
	 * 取得承作日期<p/>
	 * 徵信報告或資信簡表資料日期？
	 */
	public Date getCesDate() {
		return this.cesDate;
	}
	/**
	 *  設定承作日期<p/>
	 *  徵信報告或資信簡表資料日期？
	 **/
	public void setCesDate(Date value) {
		this.cesDate = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.Creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.Creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	
	public void setSeq(BigDecimal seq) {
		this.seq = seq;
	}

	public BigDecimal getSeq() {
		return seq;
	}
}
