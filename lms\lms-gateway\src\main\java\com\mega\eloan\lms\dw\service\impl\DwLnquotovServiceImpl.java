package com.mega.eloan.lms.dw.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dw.service.DwLnquotovService;

@Service
public class DwLnquotovServiceImpl extends AbstractDWJdbc implements
		DwLnquotovService {
	public List<?> findDW_LNQUOTOV_L120S05A1(String grpNo) {
		return this.getJdbc().queryForList("DWADM.DW_LNQUOTOV.selL120s05a1",
				new String[] { grpNo });
	}

	public List<?> findDW_LNQUOTOV_L120S05B1(String custId, String dupCode) {
		StringBuilder sbStr = new StringBuilder();
		sbStr.append(custId).append(("0".equals(dupCode) ? "" : dupCode));
		return this.getJdbc().queryForList("DWADM.DW_LNQUOTOV.selL120s05b1",
				new String[] { sbStr.toString()});
	}

	public List<Map<String, Object>> findDW_LNQUOTOV_Lcamt(String custId,
			String cntrNo) {
		return this.getJdbc().queryForList("DWLNQUOTOV.selLcamt",
				new Object[] { custId, cntrNo });
	}

	public Map<String, ?> findDW_LNQUOTOV_Contract(String custIdandDupno,
			String branchId, String custid, String dupno) {
		return this.getJdbc().queryForMap(
				"DW_LNQUOTOV.selContract",
				new Object[] { custIdandDupno, branchId, branchId,
						custIdandDupno, branchId, custIdandDupno, custid,
						dupno, branchId, custIdandDupno, branchId });
	}

	@Override
	public List<Map<String,Object>> findDW_LNQUOTOV_LaterRetr(String brNo) {
		return this.getJdbc().queryForList("DWLNQUOTOV.selBybrNo",
				new Object[] { brNo });
	}

	@Override
	public int findByCntrNoAndCustIdAndDupNo(String cntrNo, String custId,
			String dupNo) {
		String custKey = "";
		if ("0".equals(dupNo)) {
			custKey = custId;
		} else {
			custKey = custId + dupNo;
		}
		return this.getJdbc().queryForInt("DWLNQUOTOV.selByContract",
				new Object[] { cntrNo, custKey });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.dw.service.DwdbBASEService#findDW_CNTRYAREAByCountryCode
	 * (java.lang.String)
	 */
	@Override
	public List<Map<String, Object>> findLNF022selByCustIdBrNo(
			String custId,String dupNo,String brNo){
		String custIdDupNo = custId + dupNo;
		return this.getJdbc().queryForList("LNF022.selByCustIdBrNo",
				new Object[] { custIdDupNo , brNo , brNo , custIdDupNo , brNo , custIdDupNo ,custId ,dupNo , brNo , custIdDupNo , brNo });
	}
	
	@Override
	public List<?> findDW_LNQUOTOV_loan_date(String allCust, String cntrNo) {
		return this.getJdbc().queryForList("DWLNQUOTOV.selLoan_date",
				new String[] { allCust, cntrNo });
	}
	
	@Override
	public List<Map<String, Object>> selDistinctCntrnoByCustidDupno(String custId,String dupNo){
		String custKey = "";
		if ("0".equals(dupNo)) {
			custKey = custId;
		} else {
			custKey = custId + dupNo;
		}
		return this.getJdbc().queryForList("DWLNQUOTOV.selDistinctCntrnoByCustidDupno", 
				new Object[] { custKey });
	}
}
