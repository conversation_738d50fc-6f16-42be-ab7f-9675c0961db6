/* 
 * C900M01P.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** PD等級分組對應表 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C900M01P", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C900M01P extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/**
	 * 企/個金分類
	 * <p/>
	 * 1企金 <br/>
	 * 2個金
	 */
	@Size(max = 1)
	@Column(name = "DOCTYPE", length = 1, columnDefinition = "VARCHAR(1)")
	private String docType;

	/**
	 * PD分組
	 * <p/>
	 * 可能有A~E五項，也有可能增減 <br/>
	 * 若此欄位為空，代表授權層級不看PD
	 */
	@Size(max = 1)
	@Column(name = "PDGROUP", length = 1, columnDefinition = "VARCHAR(1)")
	private String pdGroup;

	/**
	 * PD分類
	 * <p/>
	 * 1一般企業模型 <br/>
	 * 2特殊融資模型
	 */
	@Size(max = 2)
	@Column(name = "PDCRDTYPE", length = 2, columnDefinition = "VARCHAR(2)")
	private String pdCrdType;

	/**
	 * PD等級
	 * <p/>
	 * 內部/外部風險評等等級
	 */
	@Size(max = 10)
	@Column(name = "PDGRADE", length = 10, columnDefinition = "VARCHAR(10)")
	private String pdGrade;

	/**
	 * PD版本
	 * <p/>
	 * 判斷PD等級欄位要用到多少欄
	 */
	@Size(max = 13)
	@Column(name = "PDVERSION", length = 13, columnDefinition = "VARCHAR(13)")
	private String pdVersion;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}

	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 設定企/個金分類
	 **/
	public String getDocType() {
		return docType;
	}

	/**
	 * 取得企/個金分類
	 **/
	public void setDocType(String docType) {
		this.docType = docType;
	}

	/**
	 * 取得PD分組
	 * <p/>
	 * 可能有A~E五項，也有可能增減 <br/>
	 * 若此欄位為空，代表授權層級不看PD
	 */
	public String getPdGroup() {
		return this.pdGroup;
	}

	/**
	 * 設定PD分組
	 * <p/>
	 * 可能有A~E五項，也有可能增減 <br/>
	 * 若此欄位為空，代表授權層級不看PD
	 **/
	public void setPdGroup(String value) {
		this.pdGroup = value;
	}

	/**
	 * 取得PD等級
	 * <p/>
	 * 內部風險評等等級
	 */
	public String getPdGrade() {
		return this.pdGrade;
	}

	/**
	 * 設定PD分類
	 **/
	public String getPdCrdType() {
		return pdCrdType;
	}

	/**
	 * 取得PD分類
	 **/
	public void setPdCrdType(String pdCrdType) {
		this.pdCrdType = pdCrdType;
	}

	/**
	 * 設定PD等級
	 * <p/>
	 * 內部風險評等等級
	 **/
	public void setPdGrade(String value) {
		this.pdGrade = value;
	}

	/**
	 * 取得PD版本
	 * <p/>
	 * 判斷PD等級欄位要用到多少欄
	 */
	public String getPdVersion() {
		return this.pdVersion;
	}

	/**
	 * 設定PD版本
	 * <p/>
	 * 判斷PD等級欄位要用到多少欄
	 **/
	public void setPdVersion(String value) {
		this.pdVersion = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
