/* 
 * LMS1401S10PageV20220812.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * J-111-0454 風控風險權數
 * </pre>
 * 
 * @since 2021/10/
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/10/,009301,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1401S10PageV20220812")
public class LMS1401S10PageV20220812 extends AbstractEloanForm {

	@Override
	public void afterExecute(ModelMap model, PageParameters parameters) {
		// super.afterExecute(model, parameters);
        String transactionCode = Util.nullToSpace(parameters.get(EloanConstants.TRANSACTION_CODE));
        setJavaScriptVar(EloanConstants.TRANSACTION_CODE, transactionCode);

        model.addAttribute("contentPageName", getContentPageName());
        model.addAttribute("contentFragmentName", getContentFragmentName());
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
	
	private static final long serialVersionUID = -4024257163623646201L;
	
	@Override
	public void execute(ModelMap model, PageParameters params) {
		renderJsI18N(this.getClass());
		
	}
	
    /*
     * (non-Javadoc)
     * 
     * @see com.mega.eloan.common.pages.AbstractEloanForm#getViewName()
     */
    @Override
    public String getViewName() {
        // 不要headerarea
        return "common/pages/None";
    }
}
