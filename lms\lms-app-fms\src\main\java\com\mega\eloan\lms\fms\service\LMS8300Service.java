package com.mega.eloan.lms.fms.service;


import java.util.List;
import java.util.Map;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L830M01A;
import com.mega.eloan.lms.model.L830M01B;


/**
 * <pre>
 * 以房養老案件查詢結果維護作業
 * </pre>
 * 
 * @since 2023
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface LMS8300Service extends AbstractService {

	public List<L830M01A> findL830m01aByBrno(String brNo, String creator);
	
	/**
	 * 其它到結案所用的flow
	 * 
	 * @param mainOidb 文件編號
	 * @param model 資料表
	 * @param setResult boolean
	 * @param resultType boolean
	 * @throws Throwable
	 */
//	public void flowAction(String mainOid, L820M01A model, boolean setResult,
//			boolean resultType, boolean upMis) throws Throwable;
	
	public L830M01A findByOid(String oid);
	
	
	public Map<String, String> get_maintainTypeDescMap();
	
	public void deleteL830m01aList(List<L830M01A> l830m01aList);
	
	public void deleteL830m01bList(List<L830M01B> l830m01bList);
	
	public int gfnUpdateLNF013(L830M01A meta, String Brno, String userId);
	
}
