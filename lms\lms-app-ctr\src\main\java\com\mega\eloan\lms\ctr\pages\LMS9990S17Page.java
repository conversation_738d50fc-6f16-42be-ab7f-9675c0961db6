/* 
 * LMS9990M07Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.pages;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.ui.ModelMap;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.OverSeaUtil;

import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.ctr.panels.LMS9990S18APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S19APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S20APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S21APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S22APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S23APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S25APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S26APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S27APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S28APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S34APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S35APanel;
import com.mega.eloan.lms.ctr.panels.LMS9990S36APanel;

/**<pre>
 * 個金約據書-子頁分頁內容(一般)
 * </pre>
 * @since  2012/7/24
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/7/24,Miller,new
 *          </ul>
 */

@Controller
@RequestMapping("/ctr/lms9990s17/{page}")
public class LMS9990S17Page extends AbstractEloanForm {	
	public LMS9990S17Page() {
		super();	
	}
	
	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		  super.execute(model, params);
		  renderJsI18N(LMS9990S17Page.class);
		  
		  //一般
		  new LMS9990S18APanel("ctrs18panel", true).processPanelData(model, params);
		  new LMS9990S19APanel("ctrs19panel", true).processPanelData(model, params);
		  new LMS9990S20APanel("ctrs20panel", true).processPanelData(model, params);
		  new LMS9990S21APanel("ctrs21panel", true).processPanelData(model, params);
		  new LMS9990S22APanel("ctrs22panel", true).processPanelData(model, params);
		  new LMS9990S23APanel("ctrs23panel", true).processPanelData(model, params);

		  // 政府留貸
		  new LMS9990S26APanel("ctrs28panel", true).processPanelData(model, params);
		  new LMS9990S27APanel("ctrs29panel", true).processPanelData(model, params);
		  new LMS9990S25APanel("ctrs30panel", true).processPanelData(model, params);
		  new LMS9990S28APanel("ctrs31panel", true).processPanelData(model, params);
		  new LMS9990S34APanel("ctrs32panel", true).processPanelData(model, params);
		  new LMS9990S35APanel("ctrs33panel", true).processPanelData(model, params);
		  new LMS9990S36APanel("ctrs34panel", true).processPanelData(model, params);	  
	}


	@Override
	public void afterExecute(ModelMap model, PageParameters parameters) {
		super.afterExecute(model, parameters);
		// UPGRADE: 前端須配合改Thymeleaf的樣式
		// remove("_headerPanel");
		model.addAttribute("showHeader", false); // 不顯示 _headerPanel
	}	

	private static final long serialVersionUID = 1L;
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;

	}
	
	@Override
	protected String getViewName() {
		return "common/pages/None";
	}

}
