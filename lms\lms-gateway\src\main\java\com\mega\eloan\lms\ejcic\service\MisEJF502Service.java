/* 
 * MisEJV502Service.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * MIS.KRM040->MIS.EJV50201->MIS.EJV502
 * </pre>
 * 
 * @since 2011/11/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/11,CP,new
 *          </ul>
 */
public interface MisEJF502Service {

//	/**
//	 * <li>0-BANID-VARCHAR(11)- <li>1-ID-VARCHAR(11)- <li>2-PRODID - VARCHAR(2)-
//	 * <li>3-IDN_BAN-Char(10)-繳款人ID <li>4-BILL_DATE-Char(7)-結帳日 <li>
//	 * 5-ISSUE-Char(3)-發卡機構代號 <li>6-ISSUE_NAME-Char(24)-發卡機構名稱 <li>
//	 * 7-BILL_MARK-Char(2)-帳單別 <li>8-CARD_TYPE-Char(7)-信用卡別 <li>
//	 * 9-PERM_LIMIT-Num(7)-永久額度單位:千元 <li>10-TEMP_LIMIT-Num(7)-臨時額度單位:千元 <li>
//	 * 11-CASH_LIMIT-Num(7)-預借現金額度單位:千元 <li>12-PAYABLE-Num(9)-本期應付帳款(金額)單位:元 <li>
//	 * 13-CASH_LENT-Num(8)-本期預借現金單位:元 <li>14-LAST_PAYA-Num(9)-上期應付帳款(金額)單位:元 <li>
//	 * 15-REVOL_BAL-Num(9)-上期循環信用單位:元 <li>16-PAY_STAT-Char(1)-上期繳款狀況代號(金額) <li>
//	 * 17-PAY_CODE-Char(1)-上期繳款狀況代號(時間) <li>
//	 * 18-REVOL_RATE-Num(4)-上期循環比率REVOL_BAL/PERM_LIMIT;精確度至小數點後2位(四捨五入);非百分比;
//	 * <li>19-PRE_OWED-Num(8)-未到期分期償還待付金額 <li>20-DEBT_CODE-Char(1)-債權狀態註記 <li>
//	 * 21-CLOSE_CODE-Char(1)-債權結案註記 <li>22-CLEAR_DATE-Char(7)-不良債權結案日期 <li>
//	 * 23-QDATE-VARCHAR(9)- <li>24-QEMPCODE-VARCHAR(11)- <li>
//	 * 25-QEMPNAME-VARCHAR(20)- <li>26-QBRANCH-VARCHAR(3)-
//	 */
//
//	final String[] LnunidCols = { "BANID", "ID", "PRODID", "IDN_BAN",
//			"BILL_DATE", "ISSUE", "ISSUE_NAME", "BILL_MARK", "CARD_TYPE",
//			"PERM_LIMIT", "TEMP_LIMIT", "CASH_LIMIT", "PAYABLE", "CASH_LENT",
//			"LAST_PAYA", "REVOL_BAL", "PAY_STAT", "PAY_CODE", "REVOL_RATE",
//			"PRE_OWED", "DEBT_CODE", "CLOSE_CODE", "CLEAR_DATE", "QDATE",
//			"QEMPCODE", "QEMPNAME", "QBRANCH"};
//
//	/**
//	 * 聯徵資料庫過去一年信用卡繳款延遲紀錄 - 延遲次數
//	 * @param custId String 10碼
//	 * @return Map<String, Object>
//	 */
//	Map<String, Object> getByIdPaycode(String custId);
//	
//	/**
//	 * 聯徵資料庫過去一年信用卡繳款延遲紀錄 - 延遲時間
//	 * @param custId String 10碼
//	 * @return List<Map<String, Object>>
//	 */
//	List<Map<String, Object>> getMaxPayCode(String custId);
//	
//	/**
//	 * 聯徵資料庫過去一年是否動用信用卡循環額度
//	 * @param custId String 10碼
//	 * @return List<Map<String, Object>>
//	 */
//	List<Map<String, Object>> getRevolbalVal(String custId);
	
	/**
	 * 查詢信用卡停用記錄</br>
	 * 引進過去一年信用卡繳款延遲紀錄 - 延遲次數
	 * @param id 統一編號
	 * @param qDate 查詢日期 YYY/MM/DD
	 * @param prodId 查詢產品別
	 * @return Map
	 */
	Map<String, Object> findLstYrCreditCardPaymntDelayCount(String id, String qDate, String prodId);

	/**
	 * 查詢信用卡停用記錄</br>
	 * 引進過去一年信用卡繳款延遲紀錄 - 延遲時間
	 * @param id 統一編號
	 * @param qDate 查詢日期 YYY/MM/DD
	 * @param prodId 查詢產品別
	 * @return Map
	 */
	Map<String, Object> findLstYrCreditCardPaymntDelayTime(String id, String qDate, String prodId);

	/**
	 * 引進過去一年是否動用信用卡循環額度
	 * @param id 統一編號
	 * @param qDate 查詢日期 YYY/MM/DD
	 * @param prodId 查詢產品別
	 * @return Map
	 */
	Map<String, Object> findUseCreditCardCycleAmtCount(String id, String qDate, String prodId);
}
