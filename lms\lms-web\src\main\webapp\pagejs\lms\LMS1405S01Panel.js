var initDfd = initDfd || $.Deferred();
initDfd.done(function(auth){
    $("#coKind").trigger("change");
    //$("[name=unitMega]").trigger("click");
    var docCode = $("#docCode").val();
    //當分行代號為025時，要出現#branchShow025_1 #branchShow025_2的li
    var brNo = userInfo ? userInfo.unitNo : "";
    
    //當為陳復/陳述案不顯示額度明細表的頁籤
    if (responseJSON.docCode == "3") {
        $("#showforDocCode3").show();
    } else {
        $("#lms140s02").show();
        //J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
        //J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
        //只有企金要顯示
        if(responseJSON.docType == "1" ){
        	$("#lms140s04").show();
        	$("#lms140s06").show();
        	//授權外一般才要顯示  主要申請敘作內容
        	if(responseJSON.docKind == "2" && (responseJSON.docCode == "1" || responseJSON.docCode == "2") ){
                $("#lms140s07").show();
            }else{
                $("#lms140s07").hide();
            }
        }else{
        	$("#lms140s04").hide();  //hide
        	$("#lms140s06").hide();
        	$("#lms140s07").hide();
        }
		
    }
    
    //J-110-0485_05097_B1001 於簽報書新增LGD欄位
	//$("#lms140s08").show();
	//$("#lms140s08").hide();
	//無法取得lmsM01Json.showPanelLms140s08()回傳值，故改再lmsM01Json.showPanelLms140s08內隱藏
	var isShowLms140s08 = lmsM01Json.showPanelLms140s08();

	// J-111-0397 八、RWA 試算
	var isShowLms140s09 = lmsM01Json.showPanelLms140s09();
	/*
    if(lmsM01Json.showPanelLms140s09()){
        $("#lms140s09").show();
    } else {
        $("#lms140s09").hide();
    }
    */
	
	var isShowLms140s10 = lmsM01Json.showPanelLms140s10();

	// BIS
	var isShowLms140s11 = lmsM01Json.showPanelLms140s11();
    
    if (brNo == "025" || brNo == "900") {
        $("#branchShow025_1,#branchShow025_2").show();
    }
    
    $("input[name=unitCase]:checked").trigger("click");
});
function changeSyndicationLoan(obj){
    var objValue1 = $('input[name=unitCase]:checked').val();
    var objValue2 = $('input[name=unitMega]:checked').val();
    
    if (objValue1 == "Y" ) {   //|| objValue2 == "Y"
        if (objValue1 == "Y") {
            $('#SyndicationLoan1').show();
            $('#unitCase2Div').show();
			$('#SyndicationLoanAddDate').show();
        } else {
            $('#SyndicationLoan1').hide().find(":radio").prop("checked", false);//移除隱藏選項的radio checked
            $('#unitCase2Div').hide().find(":radio").prop("checked", false);//移除隱藏選項的radio checked
        }
        $('#SyndicationLoan2').show();
		$('#SyndicationLoanAddDate').show();
    } else {
        if (objValue1 == "N" ) {  //&& objValue2 == "N"
            //$('#SyndicationLoan1').hide();
            //$('#SyndicationLoan2').hide();
            $('#SyndicationLoan1').hide().find(":radio").prop("checked", false);//移除隱藏選項的radio checked 
            $('#SyndicationLoan2').hide().find(":radio").prop("checked", false);//移除隱藏選項的radio checked
            $('#SyndicationLoanAddDate').hide().find(":radio").prop("checked", false);//移除隱藏選項的radio checked
        }
    }
    
    //
    var objValue = $(obj).val();
    var objName = $(obj).attr('name');
    var uCntBranch =$('input[name=uCntBranch]:checked').val();
	var unitCase =$('input[name=unitCase]:checked').val();
	
    //if (objName == "uCntBranch" || objName == "unitCase") {
		
        if (uCntBranch == "Y" && unitCase == "Y") {
            $('#SyndicationLoan2_mgr').show();
        } else {
            $('#SyndicationLoan2_mgr').hide();
        }
    //}
}

function changeItem(obj){
    var objValue = $(obj).val();
    var objName = $(obj).attr('name');
    if (objName == "mCntrt") {
        if (objValue == "Y") {
            $('#coKind_parent').hide().find("[name=sCntrt]:radio").prop("checked", false);//移除隱藏選項的radio checked ;
            $('#coKind_son').hide().find(":text").val("");//清除隱藏選項的內容;
        } else {
            $('#coKind_parent').show();
        }
    }
    
    if (objName == "sCntrt") {
        if (objValue == "Y") {
            $('#coKind_son').show();
        } else {
            $('#coKind_son').hide().find(":text").val("");//清除隱藏選項的內容;
        }
    }
    if (objName == "coKind" && objValue == "0") {
        $('#coKindSpan').hide().find(":radio").prop("checked", false);//移除隱藏選項的radio checked ;;
    } else {
        if (objName == "coKind") {
            $('#coKindSpan').show();
            $('#coKind_1,#coKind_2,#coKind_3,#coKind_4').html(DOMPurify.sanitize($("[name=coKind]").find(":selected").text()));
        }
    }
}
