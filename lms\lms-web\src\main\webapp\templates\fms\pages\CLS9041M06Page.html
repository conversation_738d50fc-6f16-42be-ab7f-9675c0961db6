<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
        <wicket:extend>
        	<div class="button-menu funcContainer" id="buttonPanel">
				<button id="btnFilter" class="forview">
	                    <span class="ui-icon ui-icon-jcs-01"></span>
	                    <wicket:message key="button.filter">篩選</wicket:message>
	           	</button>
				<button id="btnAdd" class="forview">
	                    <span class="ui-icon ui-icon-jcs-01"></span>
	                    <wicket:message key="button.add">新增</wicket:message>
	           	</button>
				<button id="btnView" class="forview">
	                    <span class="ui-icon ui-icon-jcs-01"></span>
	                    <wicket:message key="button.view">調閱</wicket:message>
	           	</button>
				<button id="btnDelete" class="forview">
	                    <span class="ui-icon ui-icon-jcs-01"></span>
	                    <wicket:message key="button.delete">刪除</wicket:message>
	           	</button>
				<button id="btnExit" class="forview">
	                    <span class="ui-icon ui-icon-jcs-01"></span>
	                    <wicket:message key="button.exit">離開</wicket:message>
	           	</button>
			</div>
        	<div id="gridview" name="gridview"></div>
            <script type="text/javascript" src="pagejs/fms/CLS9041M06Page.js"></script>
			
		<!-- 篩選 -->
		<div id="filter" style="display:none;" >
			<div id="filterDiv" >
				<form id="filterForm" >
					<table class="tb2" width="100%">
						<tr>
							<td class="hd2" width="30%"><span><b><wicket:message key="C004M01A.creator">建立人員編號</wicket:message></b></span></td>
							<td><input type="text" id="creatorFilter" name="creatorFilter"/></td>
						</tr>
						<tr>
							<td class="hd2" width="30%"><span><b><wicket:message key="C004M01A.createTime">建立日期</wicket:message></b></span></td>
							<td>
								<input id="bgnDateFilter" name="bgnDateFilter" type="text" class="date" />~
								<input id="endDateFilter" name="endDateFilter" type="text" class="date" />
							</td>
						</tr>
					</table>
				</form>
			</div>
		</div>
		<!-- 調閱 -->
		<div id="detail" style="display:none;" >
			<div id="detailDiv" >
				<form id="detailForm" >
					<input type="hidden" id="oid" name="oid"/>
					<input type="hidden" id="mainId" name="mainId"/>
					<input type="hidden" id="rptType" name="rptType"/>
					<b><wicket:message key="C004M01A.createTime">建立日期</wicket:message>:</b>
					<input type="text" disabled="true" id="createTime" name="createTime"/><br/>
					<b><wicket:message key="C004M01A.date">資料日期</wicket:message>:</b>
					<input type="text" disabled="true" id="bgnDate" name="bgnDate" size="7"/>~<input type="text" disabled="true" id="endDate" name="endDate" size="7"/><br/>
					<b><wicket:message key="C004M01A.creator">建立人員</wicket:message>:</b>
					<input type="text" disabled="true" id="creator" name="creator"/>
					<hr/>
					<div class="funcContainer" style="display:none;" >
                    	<button id="uploadFile" type="button">
                        	<span class="text-only"><wicket:message key="uploadTxt"><!-- 附加檔案--></wicket:message></span>
                        </button>
                        <button id="deleteFile" type="button">
                            <span class="text-only"><wicket:message key="deleteTxt"><!-- 刪除--></wicket:message></span>
                        </button>
						<div id="fileGrid" name="fileGrid"/>
                    </div>
				</form>
			</div>
		</div>
        </wicket:extend>
    </body>
</html>
