/* 
 * L120S10B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 微型企業主檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S10B", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L120S10B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;
	
	/** 異動人員 */
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 */
//	@Temporal(TemporalType.TIMESTAMP)
	@Column(columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;
	
	/** 資信情形 **/
	@Size(max=1)
	@Column(name="CREDIT", length=1, columnDefinition="CHAR(1)")
	private String credit;

	/** 存匯往來dep **/
	@Size(max=1)
	@Column(name="DEPOSIT", length=1, columnDefinition="CHAR(1)")
	private String deposit;

	/** 營運情形 op **/
	@Size(max=1)
	@Column(name="OPERATION", length=1, columnDefinition="CHAR(1)")
	private String operation;

	/** 擔保情形 guar **/
	@Size(max=1)
	@Column(name="GUARANTEE", length=1, columnDefinition="CHAR(1)")
	private String guarantee;

	/** 財務情形 fin **/
	@Size(max=1)
	@Column(name="FINANCE", length=1, columnDefinition="CHAR(1)")
	private String finance;

	/** 產業展望 pros **/
	@Size(max=1)
	@Column(name="PROSPECT", length=1, columnDefinition="CHAR(1)")
	private String prospect;

	/** 鑑於 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name="OPINION", columnDefinition="CLOB")
	private String opinion;
	
	/** 保證成數 Guarantee Percent**/
	@Digits(integer = 2, fraction = 2, groups = Check.class)
	@Column(name = "GUARPCT", columnDefinition = "DECIMAL(4,2)")
	private BigDecimal guarPct;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}
	
	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得資信情形 **/
	public String getCredit() {
		return this.credit;
	}
	/** 設定資信情形 **/
	public void setCredit(String value) {
		this.credit = value;
	}

	/** 取得存匯往來dep **/
	public String getDeposit() {
		return this.deposit;
	}
	/** 設定存匯往來dep **/
	public void setDeposit(String value) {
		this.deposit = value;
	}

	/** 取得營運情形 op **/
	public String getOperation() {
		return this.operation;
	}
	/** 設定營運情形 op **/
	public void setOperation(String value) {
		this.operation = value;
	}

	/** 取得擔保情形 guar **/
	public String getGuarantee() {
		return this.guarantee;
	}
	/** 設定擔保情形 guar **/
	public void setGuarantee(String value) {
		this.guarantee = value;
	}

	/** 取得財務情形 fin **/
	public String getFinance() {
		return this.finance;
	}
	/** 設定財務情形 fin **/
	public void setFinance(String value) {
		this.finance = value;
	}

	/** 取得產業展望 pros **/
	public String getProspect() {
		return this.prospect;
	}
	/** 設定產業展望 pros **/
	public void setProspect(String value) {
		this.prospect = value;
	}

	/** 取得鑑於 **/
	public String getOpinion() {
		return this.opinion;
	}
	/** 設定鑑於 **/
	public void setOpinion(String value) {
		this.opinion = value;
	}
	
	/** 取得保證成數 **/
	public BigDecimal getGuarPct() {
		return this.guarPct;
	}
	/** 設定保證成數 **/
	public void setGuarPct(BigDecimal value) {
		this.guarPct = value;
	}
}
