thickbox.addMsg=whether new information?
detailTitle=students loan subsidy policy interest-related statements
canotInsert=can not add, please delete the file
uploadTxt=select additional files
deleteTxt=delete
receipt=produce receipts
close=Close
#==================================================
# \u653f\u7b56\u6027\u7559\u5b78\u751f\u8cb8\u6b3e\u88dc\u8cbc\u606f\u76f8\u95dc\u5831\u8868
#==================================================
C004M01A.detailTilte= submit information
C004M01A.date=Data Date
C004M01A.rptType=statements Category
C004M01A.bgnDate=Data from the date 
C004M01A.endDate=date are to
C004M01A.rptDate=determine the submitted date
C004M01A.rptName=Report Name
C004M01A.filePath=Additional file path
C004M01A.fileName=attached file name
C004M01A.rmk=Remarks
C004M01A.creator=establish personnel numbers
C004M01A.createTime=creation date
C004M01A.updater=The transaction staff numbers for
C004M01A.updateTime=transaction date
C004M01A.confirmDelete=whether to delete such data?
C004M01A.S1Name=Student loans S1 \u200b\u200bpolicy statements
C004M01A.uploadData=upload data
C004M01A.delData=delete data