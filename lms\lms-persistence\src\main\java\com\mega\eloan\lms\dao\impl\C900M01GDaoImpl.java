/* 
 * C900M01GDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C900M01GDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900M01G;

/** 團貸分戶明細檔 **/
@Repository
public class C900M01GDaoImpl extends LMSJpaDao<C900M01G, String>
	implements C900M01GDao {

	@Override
	public C900M01G findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C900M01G> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C900M01G> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C900M01G findByUniqueKey(String cntrNo, String grpcntrno){
		ISearch search = createSearchTemplete();
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		if (grpcntrno != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "grpcntrno", grpcntrno);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}
	
	@Override
	public C900M01G findByCntrNo(String cntrNo){
		ISearch search = createSearchTemplete();
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C900M01G> findByIndex01(String cntrNo, String grpcntrno){
		ISearch search = createSearchTemplete();
		List<C900M01G> list = null;
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		if (grpcntrno != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "grpcntrno", grpcntrno);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	/*
	 c900m01g來源 [1]簽案 [2]整批動用-中鋼002分行
	 
	select t.* from
	(SELECT g.cntrno FROM LMS.C900M01G g left outer join lms.l140m01a m
	on g.cntrno=m.cntrno
	where m.cntrno is null and (useflag is null or useflag='' or (useflag>'' and useFlag!='D'))
	) t left outer join lms.c160s01d d on t.cntrno=d.cntrno
	where d.cntrno is null
	
	找到的 cntrNo 再去比對 ELF500, LNF020
	
	
	若 c900m01g.status='2-簽案核准' 可再看 
	select * from mis.elf447n where elf447n_contract in (?)
	 */
	
	@Override
	public List<C900M01G> findByUsedDetail(String grpcntrno){
		ISearch search = createSearchTemplete();
		List<C900M01G> list = null;		
		search.addSearchModeParameters(SearchMode.EQUALS, "grpcntrno", grpcntrno);
		search.setMaxResults(Integer.MAX_VALUE);
		if(true){
			Map<String, Boolean> orderBy = new LinkedHashMap<String, Boolean>();
			if(true){
				orderBy.put("status", false);
				orderBy.put("cntrNo", false);
			}
			search.setOrderBy(orderBy);	
		}		
		
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}