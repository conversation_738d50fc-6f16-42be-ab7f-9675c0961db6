/* 
 * RPS4035ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service.impl;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.lms.service.RPS4035Service;
import com.mega.eloan.lms.mfaloan.service.MisLnunIdService;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 婉卻記錄查詢
 * </pre>
 * 
 * @since
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class RPS4035ServiceImpl extends AbstractCapService implements
		RPS4035Service {

	@Resource
	MisLnunIdService misLnunIdService;

	public Page<Map<String, Object>> getLnunIdByCustId(String custId,
			String dupNo, ISearch search) throws ParseException {
		return LMSUtil.getMapGirdDataRow(this.getLnunIdByCustId(custId, dupNo),
				search);
	}

	@Override
	public Page<Map<String, Object>> getLnunId02ByCustId(String custId,
			String dupNo, ISearch search) {
		return LMSUtil.getMapGirdDataRow(
				this.getLnunId02ByCustId(custId, dupNo), search);
	}

	@Override
	public List<Map<String, Object>> getLnunIdByCustIdCount(String custId) {
		return misLnunIdService.findLnunIdByCustIdCount(custId);
	}

	@Override
	public List<Map<String, Object>> getLnunIdByCustId(String custId,
			String dupNo) throws ParseException {
		List<Map<String, Object>> rowDatas = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> rows = this.misLnunIdService
				.findLnunIdByCustId(custId, dupNo);
		for (Map<String, Object> row : rows) {
			Map<String, Object> rowData = new HashMap<String, Object>();
			rowData.put("custId", Util.trim((String) row.get("CUSTID")));
			rowData.put("dupno", Util.trim((String) row.get("DUPNO")));
			rowData.put("regdt", (Timestamp) row.get("REGDT"));
			rowData.put("regbr", Util.trim((String) row.get("REGBR")));
			rowData.put("regteller", Util.trim((String) row.get("REGTELLER")));
			rowData.put("refuseds", Util.trim((String) row.get("REFUSEDS")));
			rowData.put("rfsauth", Util.trim((String) row.get("RFSAUTH")));
			rowData.put("updater", Util.trim((String) row.get("UPDATER")));
			rowData.put("clscase", Util.trim((String) row.get("CLSCASE")));
			rowData.put("custnm", Util.trim((String) row.get("CUSTNM")));
			rowData.put("statuscd", Util.trim((String) row.get("statuscd")));
			rowData.put("refusecd", (row.get("REFUSECD")));
			rowDatas.add(rowData);
		}

		return rowDatas;
	}

	@Override
	public List<Map<String, Object>> getLnunId02ByCustId(String custId,
			String dupNo) {
		List<Map<String, Object>> rowDatas = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> rows = this.misLnunIdService
				.findLnunId02ByCustId(custId, dupNo);

		for (Map<String, Object> row : rows) {
			Map<String, Object> rowData = new HashMap<String, Object>();
			rowData.put("custId", Util.trim((String) row.get("CUSTID")));
			rowData.put("dupno", Util.trim((String) row.get("DUPNO")));
			rowData.put("regdt", (Timestamp) row.get("REGDT"));
			rowData.put("regbr", Util.trim((String) row.get("REGBR")));
			rowData.put("regteller", Util.trim((String) row.get("REGTELLER")));
			rowData.put("refuseds", Util.trim((String) row.get("REFUSEDS")));
			rowData.put("rfsauth", Util.trim((String) row.get("RFSAUTH")));
			rowData.put("updater", Util.trim((String) row.get("UPDATER")));
			rowData.put("clscase", Util.trim((String) row.get("CLSCASE")));
			rowData.put("custnm", Util.trim((String) row.get("CUSTNM")));
			rowData.put("refusecd", (row.get("REFUSECD")));
			rowDatas.add(rowData);
		}
		return rowDatas;
	}
}
