/* 
 * L120S06BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;


import com.mega.eloan.lms.model.L120S06B;


/** 利害關係人授信條件對照表明細檔 **/
public interface L120S06BDao extends IGenericDao<L120S06B> {

	L120S06B findByOid(String oid);

	List<L120S06B> findByMainId(String mainId);

	L120S06B findByUniqueKey(String mainId, String type, String custId,
			String dupNo, String cntrNo, String itemType, String refMainId);

	List<L120S06B> findByIndex01(String mainId, String custId, String dupNo,
			String cntrNo, String type, String itemType);

	List<L120S06B> findByCntrNo(String CntrNo);

	List<L120S06B> findByCustIdDupId(String custId,String DupNo);
	
	int delModel(String mainId);
}