/* 
 * C140M04ADaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C140M04ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C140M04A;
import com.mega.eloan.lms.model.C140M04A_;


/**
 * <pre>
 * 徵信調查報告書第四章主檔
 * </pre>
 * 
 * @since 2011/9/23
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
@Repository
public class C140M04ADaoImpl extends LMSJpaDao<C140M04A, String> implements
		C140M04ADao {

	@Override
	public List<C140M04A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,C140M04A_.mainId.getName(), mainId);
		return find(search);
	}
	@Override
	public List<C140M04A> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "pcId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "pcDupNo", DupNo);
		List<C140M04A> list = createQuery(C140M04A.class,search).getResultList();
		return list;
	}
}// ;
