/* 
 * C120S01FDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S01F;


/** 個金放款信用評分表(無擔用) **/
public interface C120S01FDao extends IGenericDao<C120S01F> {

	C120S01F findByOid(String oid);

	List<C120S01F> findByMainId(String mainId);

	C120S01F findByUniqueKey(String mainId, String custId, String dupNo);

	List<C120S01F> findByIndex01(String mainId, String custId, String dupNo);
	
	List<C120S01F> findByCustIdDupId(String custId,String DupNo);
	
	int deleteByOid(String oid);
	
	
}