<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:wicket="http://wicket.apache.org/">
<body>
	<wicket:panel>
 	 		<script type="text/javascript" src="pagejs/cls/CLS1151S12Panel.js"></script> 
				
				<div id="tabs-c" class="tabs">
					<ul>
						<li id="tab12_1"><a  href="#tabs-c12"><b><wicket:message key="page12.001"><!-- 科子目額度限額--></wicket:message></b></a></li>
						<li id="tab12_2"><a  href="#tabs-c12_2"><b><wicket:message key="page12.002"><!-- 科子目合併限額--></wicket:message></b></a></li>
						
						<li id="tab12_4"><a  href="#tabs-c12_4"><b><wicket:message key="page12.003"><!-- 限額條件敘述--></wicket:message></b></a></li>
					</ul>
				<div class="tabCtx-warp">
				<!-- 請在tab content 外加上 div.tabCtx-warp -->
					<div id="tabs-c12" class="content">
						<table class="tb2" width="800px" border="0" cellpadding="0" cellspacing="0">			
							<tr class="hd1" style="text-align:left; margin-top:0px; padding-top:0px;">
								<td>
								<button type="button" id="newItemChildren1Bt"  class="noHideBt">
									<span class="text-only"><wicket:message key="button.add"><!-- 新增--></wicket:message></span>
								</button> 
								<button type="button" id="removeGridviewitemChildren" class="noHideBt">
									<span class="text-only"><wicket:message key="button.delete"><!-- 刪除--></wicket:message></span>
								</button>
								<button type="button" onclick="upDownBox(true)">
									<span class="text-only"><wicket:message key="btn.upSeqno">向上移動</wicket:message></span>
								</button>
								<button type="button" onclick="upDownBox(false)">
									<span class="text-only"><wicket:message key="btn.downSeqno">向下移動</wicket:message></span>
								</button>
								</td>
							</tr>
							<tr>
								<td>
									<div id="gridviewitemChildren" />
								</td>
							</tr>
						</table>
					</div><!--end tabs-c01-->
					
					<div id="tabs-c12_2" class="content">
						<table class="tb2" width="800px" border="0" cellpadding="0" cellspacing="0">			
							<tr class="hd1" style="text-align:left">
								<td>
									<button type="button" id="newItemChildren2Bt"  class="noHideBt">
										<span class="text-only"><wicket:message key="button.add"><!-- 新增--></wicket:message></span>
									</button> 
									<button type="button" id="removeGridviewitemChildren2" class="noHideBt">
										<span class="text-only"><wicket:message key="button.delete"><!-- 刪除--></wicket:message></span>
									</button>									
								</td>
							</tr>
							<tr>
								<td> 					
									<div id="gridviewitemChildren2" />
								</td>
							</tr>
						</table>
					</div><!--end tabs-c01_2-->
				
				<div id="tabs-c12_4" class="content">
					<form action="" id="CLS1151Form12" name="CLS1151Form12">
					<table class="tb2" width="800px" border="1" cellpadding="0" cellspacing="0">
						<tr class="hd1" style="text-align:left">
							<td>
								<span style="display:none" class="caseSpan"><label><input id="tab03" type="checkbox" class="caseBox tabBox" /><wicket:message key="button.modify"><!-- 修改--></wicket:message></label></span>
								<select id="pageNum1" name="pageNum1" class="nodisabled">
				                    <option value="0" selected="selected"><wicket:message key="L140M01b.printMain"><!-- 印於主表--></wicket:message></option>
				                    <option value="1"><wicket:message key="L140M01b.print01"><!-- 印於附表(一)--></wicket:message></option>				                    
								</select>
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<button id="limitWordAll" type="button" ><span class="text-only"><wicket:message key="other.toword"><!-- 組成文字串--></wicket:message></span></button> 
								<br/>
								<span class="text-red">
								<wicket:message key="C900M01F.Memo03"><!--如果科子目額度限額的限額為0時，將不組出該科目的敘述文字。--></wicket:message>&nbsp;&nbsp;
							</span>
							</td>
						</tr>
						<tr>
							<td>
								<textarea id="itemDscr1" name="itemDscr1" cols="130" rows="13%" style="width: 840px; height: 200px;" readonly="readonly" class="caseReadOnly" ></textarea>
							</td>
						</tr>
					</table>
				<br /><br />
				</form>
				</div><!--end tabs-c01_4-->
				</div><!--end class tabCtx-warp-->
		</div><!--end tabs-c-->
		
		<div id="newItemChildrenBox1" style="display:none;"><!-- 登錄科子目限額 thinkBox -->
			<form id="C900M01FForm1" name="C900M01FForm1" >
				<table width="100%" class="tb2" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td width="8%" class="hd1" ><wicket:message key="C900M01F.loanTP"><!-- 科目--></wicket:message>&nbsp;&nbsp;</td>
						<td width="92%"><select id="subject1" name="subject1" class="nodisabled required"/></td>
					</tr>
					<tr>
                        <td class="hd1"><wicket:message key="C900M01F.subjDscr"><!-- 科目補充說明--></wicket:message>&nbsp;&nbsp;</td>
                        <td><input type="text" size="50" maxlength="200" maxlengthC="66" id="subjDscr" name="subjDscr"/></td>
                    </tr>
					<tr>
                        <td class="hd1"><wicket:message key="C900M01F.lmtDays"><!-- 清償期限--></wicket:message>&nbsp;&nbsp;</td>
                        <td>
                        	<span id="hidelimit">
                            	<input type="text" name="lmtDays" id="lmtDays" class="numeric required" positiveonly="false" integer="5" maxlength="5" size="5" />
								<wicket:message key="other.day"><!-- 天--></wicket:message>
                            </span>&nbsp;
                            <label>
                                <input id="lmtOther" name="lmtOther" value="1" type="checkbox" />
                                <wicket:message key="C900M01F.lmtOther2"><!-- 詳產品資訊--></wicket:message>
                            </label>
                                <input id="itemOid" name="itemOid" type="text" style="display:none"/>
                        </td>
                    </tr>
					<tr>
					  	<td class="hd1"><wicket:message key="C900M01F.lmtMoney"><!-- 額度限額--></wicket:message>&nbsp;&nbsp;</td>
					  	<td>						
							<select id="lmtCurr1" name="lmtCurr1" combokey="Common_Currcy" space="true"/>
							<input type="text" id="lmtAmt1" name="lmtAmt1" class="numeric required number"   size="19" maxlength="22" integer="13" fraction="2"  />
							<wicket:message key="other.money"><!-- 元	--></wicket:message>
					  	</td>
				  	</tr>
				</table>
				<span class="text-only color-red"><b><wicket:message key="C900M01F.Memo01"><!-- 1.科目如為透支類(102/202/104/204/404)時，請務必輸入其核准之額度限額(透支額度)；</br>2.如果對該科目沒有設限時，請輸入0，系統將依總核准額度為設定值；</br>3.信保承保案件只需登錄擔保科目且如果對該科目沒有設限時，請輸入0--></wicket:message></b></span>
				<input type="text" id="lmtSeq1" name="lmtSeq1" style="display:none" size="16" maxlength="16" />				
			</form>
		</div><!-- 登錄科子目限額 thinkBox END-->
		
		<div id="newItemChildrenBox2" style="display:none;"><!-- 登錄科子目合併限額 thinkBox -->
			<form id="C900M01FForm2" name="C900M01FForm2" >
				<table style="width:530px" class="tb2" border="0" cellpadding="0" cellspacing="0">
					<tr>
					  <td class="hd1" width="15%"><wicket:message key="C900M01F.lmtMoney2"><!-- 合併限額--></wicket:message>&nbsp;&nbsp;</td>
					  <td width="85%">						
						<select id="lmtCurr2" name="lmtCurr2" combokey="Common_Currcy" space="true"/>
						<input type="text" id="lmtAmt2" name="lmtAmt2" class="numeric required number"  size="19" maxlength="22" integer="13" fraction="2" />
						<wicket:message key="other.money"><!-- 元	--></wicket:message>
					  </td>
				  	</tr>
					<tr>
						<td class="hd1" ><wicket:message key="C900M01F.loanTP"><!-- 科目--></wicket:message></td>
						<td><input type="checkbox" id="subject2" name="subject2" class="nodisabled"/><!-- 科目位置 --></td>
					</tr>
				</table>
				<input type="text" id="lmtSeq2" name="lmtSeq2" style="display:none" size="16" maxlength="16" />
			</form>
		</div><!-- 登錄科子目合併限額 thinkBox END-->
	</wicket:panel>
</body>
</html>