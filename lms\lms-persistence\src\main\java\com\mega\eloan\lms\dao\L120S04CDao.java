/* 
 * L120S04CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S04C;

/** 關係戶於本行往來實績彙總表明細檔 **/
public interface L120S04CDao extends IGenericDao<L120S04C> {

	L120S04C findByOid(String oid);

	List<L120S04C> findByMainId(String mainId);

	List<L120S04C> findByMainIdKeyCustIdDupNo(String mainId, String keyCustId,
			String keyDupNo);

	List<L120S04C> findByMainIdDocKind(String mainId, String[] docKind);

	List<L120S04C> findByMainIdDocKindForClearLand(String mainId,
			String[] docKind);

    List<L120S04C> findByMainIdKeyCustIdDupNoDocKind(
            String mainId, String keyCustId, String keyDupNo, String[] docKind);
}