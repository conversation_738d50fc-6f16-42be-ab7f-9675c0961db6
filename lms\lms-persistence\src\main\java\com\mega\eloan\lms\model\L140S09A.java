/* 
 * L140S09A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 其他敘作條件資訊檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140S09A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L140S09A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 樣版順序 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="BIZCATSEQNUM", columnDefinition="DECIMAL(3,0)")
	private Integer bizCatSeqNum;


	/** 順序 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SEQNUM", columnDefinition="DECIMAL(3,0)")
	private Integer seqNum;

	/** 
	 * 授信科目代碼組合<p/>
	 * 參照<br/>
	 *  L140M01F.loanTPListxxx|xxx|xxx…
	 */
	@Size(max=300)
	@Column(name="LOANTPS", length=300, columnDefinition="VARCHAR(300)")
	private String loanTPs;

	/** 科目別(列印時呈現)
	 * 一個樣版一個科目別
	 */
	@Size(max=300)
	@Column(name="LOANTPSNAME", length=60, columnDefinition="VARCHAR(60)")
	private String loanTPsName;

	/** 簽報樣版 **/
	@Size(max=3)
	@Column(name="BIZCAT", length=3, columnDefinition="CHAR(3)")
	private String bizCat;

	/** 項目代號<p/>
	 * codeType 規則 => "bizItem" +  bizCat第一碼
	 **/
	@Size(max=2)
	@Column(name="BIZITEM", length=2, columnDefinition="CHAR(2)")
	private String bizItem;

	/** 項目 **/
	@Size(max=2048)
	@Column(name="ITEMSTR", length=2048, columnDefinition="VARCHAR(2048)")
	private String itemStr;

	/** 是否列印 **/
	@Size(max=1)
	@Column(name="ISPRINT", length=1, columnDefinition="CHAR(1)")
	private String isPrint;
	
	/** 簽報樣版識別碼 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="BIZCATID", columnDefinition="DECIMAL(3,0)")
	private Integer bizCatId;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得樣版順序 **/
	public Integer getBizCatSeqNum() {
		return this.bizCatSeqNum;
	}
	/** 設定樣版順序 **/
	public void setBizCatSeqNum(Integer value) {
		this.bizCatSeqNum = value;
	}

	/** 取得順序 **/
	public Integer getSeqNum() {
		return this.seqNum;
	}
	/** 設定順序 **/
	public void setSeqNum(Integer value) {
		this.seqNum = value;
	}

	/** 
	 * 取得授信科目代碼組合<p/>
	 * 參照<br/>
	 *  L140M01F.loanTPListxxx|xxx|xxx…
	 */
	public String getLoanTPs() {
		return this.loanTPs;
	}
	/**
	 *  設定授信科目代碼組合<p/>
	 *  參照<br/>
	 *  L140M01F.loanTPListxxx|xxx|xxx…
	 **/
	public void setLoanTPs(String value) {
		this.loanTPs = value;
	}

	/** 取得科目別(列印時呈現) **/
	public String getLoanTPsName() {
		return this.loanTPsName;
	}
	/** 設定科目別(列印時呈現) **/
	public void setLoanTPsName(String value) {
		this.loanTPsName = value;
	}

	/** 取得簽報樣版 **/
	public String getBizCat() {
		return this.bizCat;
	}
	/** 設定簽報樣版 **/
	public void setBizCat(String value) {
		this.bizCat = value;
	}

	/** 取得項目代號<p/>codeType 規則 => "bizItem" +  bizCat第一碼 **/
	public String getBizItem() {
		return this.bizItem;
	}
	/** 設定項目代號<p/>codeType 規則 => "bizItem" +  bizCat第一碼 **/
	public void setBizItem(String value) {
		this.bizItem = value;
	}

	/** 取得項目 **/
	public String getItemStr() {
		return this.itemStr;
	}
	/** 設定項目 **/
	public void setItemStr(String value) {
		this.itemStr = value;
	}

	/** 取得是否列印 **/
	public String getIsPrint() {
		return this.isPrint;
	}
	/** 設定是否列印 **/
	public void setIsPrint(String value) {
		this.isPrint = value;
	}
	
	/** 取得簽報樣版識別碼 **/
	public Integer getBizCatId() {
		return this.bizCatId;
	}
	/** 設定簽報樣版識別碼 **/
	public void setBizCatId(Integer value) {
		this.bizCatId = value;
	}
}
