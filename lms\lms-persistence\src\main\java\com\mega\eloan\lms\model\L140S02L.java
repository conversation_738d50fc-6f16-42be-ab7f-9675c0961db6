/* 
 * L140S02L.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 天然及重大災害住宅補貼控制檔 **/
@NamedEntityGraph(name = "L140S02L-entity-graph", attributeNodes = { @NamedAttributeNode("l140mm2a") })
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140S02L", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","seq"}))
public class L140S02L extends GenericBean implements IDataObject, IDocObject {
//public class L140S02L extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 序號 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="SEQ", columnDefinition="DECIMAL(5,0)")
	private Integer seq;

	/** 毀損住宅所有權人戶籍號碼 **/
	@Size(max=8)
	@Column(name="HOLD_NO", length=8, columnDefinition="CHAR(08)")
	private String hold_no;

	/** 毀損住宅所有權人身分證字號 **/
	@Size(max=11)
	@Column(name="OWNER_ID", length=11, columnDefinition="CHAR(11)")
	private String owner_id;

	/** 申請日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="APP_DATE", columnDefinition="DATE")
	private Date app_date;

	/** 毀損住宅所在地址 **/
	@Size(max=102)
	@Column(name="HOUSE_ADR", length=102, columnDefinition="CHAR(102)")
	private String house_adr;

	/** 毀損住宅所有權人姓名 **/
	@Size(max=32)
	@Column(name="OWNER_NM", length=32, columnDefinition="CHAR(32)")
	private String owner_nm;

	/** 毀損住宅所有權人配偶身分證字號 **/
	@Size(max=11)
	@Column(name="OWNSP_ID", length=11, columnDefinition="CHAR(11)")
	private String ownsp_id;

	/** 毀損住宅所有權人配偶姓名 **/
	@Size(max=32)
	@Column(name="OWNSP_NM", length=32, columnDefinition="CHAR(32)")
	private String ownsp_nm;

	/** 借款人配偶身分證字號 **/
	@Size(max=11)
	@Column(name="SPOUSE_ID", length=11, columnDefinition="CHAR(11)")
	private String spouse_id;

	/** 借款人配偶姓名 **/
	@Size(max=32)
	@Column(name="SPOUSE_NM", length=32, columnDefinition="CHAR(32)")
	private String spouse_nm;

	/** 重建(購)、修繕貸款擔保品地號 **/
	@Size(max=8)
	@Column(name="COLL_LN", length=8, columnDefinition="CHAR(08)")
	private String coll_ln;

	/** 重建(購)、修繕貸款擔保品建號 **/
	@Size(max=8)
	@Column(name="COLL_BN", length=8, columnDefinition="CHAR(08)")
	private String coll_bn;

	/** 重建(購)、修繕貸款擔保品地址 **/
	@Size(max=102)
	@Column(name="COLL_ADDR", length=102, columnDefinition="CHAR(102)")
	private String coll_addr;

	/** 
	 * 建物所有權人是否設籍於受毀損住宅<p/>
	 * Y:是 N:否
	 */
	@Size(max=1)
	@Column(name="SET_HOLD", length=1, columnDefinition="CHAR(01)")
	private String set_hold;

	/** 銷戶日期(由ALOAN月批程式維護) **/
	@Temporal(TemporalType.DATE)
	@Column(name="CANCEL_DATE", columnDefinition="DATE")
	private Date cancel_date;

	/** ELOAN資料維護日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELOAN_DATE", columnDefinition="DATE")
	private Date eloan_date;

	/** ALOAN資料維護日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ALOAN_DATE", columnDefinition="DATE")
	private Date aloan_date;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得序號 **/
	public Integer getSeq() {
		return this.seq;
	}
	/** 設定序號 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/** 取得毀損住宅所有權人戶籍號碼 **/
	public String getHold_no() {
		return this.hold_no;
	}
	/** 設定毀損住宅所有權人戶籍號碼 **/
	public void setHold_no(String value) {
		this.hold_no = value;
	}

	/** 取得毀損住宅所有權人身分證字號 **/
	public String getOwner_id() {
		return this.owner_id;
	}
	/** 設定毀損住宅所有權人身分證字號 **/
	public void setOwner_id(String value) {
		this.owner_id = value;
	}

	/** 取得申請日期 **/
	public Date getApp_date() {
		return this.app_date;
	}
	/** 設定申請日期 **/
	public void setApp_date(Date value) {
		this.app_date = value;
	}

	/** 取得毀損住宅所在地址 **/
	public String getHouse_adr() {
		return this.house_adr;
	}
	/** 設定毀損住宅所在地址 **/
	public void setHouse_adr(String value) {
		this.house_adr = value;
	}

	/** 取得毀損住宅所有權人姓名 **/
	public String getOwner_nm() {
		return this.owner_nm;
	}
	/** 設定毀損住宅所有權人姓名 **/
	public void setOwner_nm(String value) {
		this.owner_nm = value;
	}

	/** 取得毀損住宅所有權人配偶身分證字號 **/
	public String getOwnsp_id() {
		return this.ownsp_id;
	}
	/** 設定毀損住宅所有權人配偶身分證字號 **/
	public void setOwnsp_id(String value) {
		this.ownsp_id = value;
	}

	/** 取得毀損住宅所有權人配偶姓名 **/
	public String getOwnsp_nm() {
		return this.ownsp_nm;
	}
	/** 設定毀損住宅所有權人配偶姓名 **/
	public void setOwnsp_nm(String value) {
		this.ownsp_nm = value;
	}

	/** 取得借款人配偶身分證字號 **/
	public String getSpouse_id() {
		return this.spouse_id;
	}
	/** 設定借款人配偶身分證字號 **/
	public void setSpouse_id(String value) {
		this.spouse_id = value;
	}

	/** 取得借款人配偶姓名 **/
	public String getSpouse_nm() {
		return this.spouse_nm;
	}
	/** 設定借款人配偶姓名 **/
	public void setSpouse_nm(String value) {
		this.spouse_nm = value;
	}

	/** 取得重建(購)、修繕貸款擔保品地號 **/
	public String getColl_ln() {
		return this.coll_ln;
	}
	/** 設定重建(購)、修繕貸款擔保品地號 **/
	public void setColl_ln(String value) {
		this.coll_ln = value;
	}

	/** 取得重建(購)、修繕貸款擔保品建號 **/
	public String getColl_bn() {
		return this.coll_bn;
	}
	/** 設定重建(購)、修繕貸款擔保品建號 **/
	public void setColl_bn(String value) {
		this.coll_bn = value;
	}

	/** 取得重建(購)、修繕貸款擔保品地址 **/
	public String getColl_addr() {
		return this.coll_addr;
	}
	/** 設定重建(購)、修繕貸款擔保品地址 **/
	public void setColl_addr(String value) {
		this.coll_addr = value;
	}

	/** 
	 * 取得建物所有權人是否設籍於受毀損住宅<p/>
	 * Y:是 N:否
	 */
	public String getSet_hold() {
		return this.set_hold;
	}
	/**
	 *  設定建物所有權人是否設籍於受毀損住宅<p/>
	 *  Y:是 N:否
	 **/
	public void setSet_hold(String value) {
		this.set_hold = value;
	}

	/** 取得銷戶日期(由ALOAN月批程式維護) **/
	public Date getCancel_date() {
		return this.cancel_date;
	}
	/** 設定銷戶日期(由ALOAN月批程式維護) **/
	public void setCancel_date(Date value) {
		this.cancel_date = value;
	}

	/** 取得ELOAN資料維護日期 **/
	public Date getEloan_date() {
		return this.eloan_date;
	}
	/** 設定ELOAN資料維護日期 **/
	public void setEloan_date(Date value) {
		this.eloan_date = value;
	}

	/** 取得ALOAN資料維護日期 **/
	public Date getAloan_date() {
		return this.aloan_date;
	}
	/** 設定ALOAN資料維護日期 **/
	public void setAloan_date(Date value) {
		this.aloan_date = value;
	}
	
	/**
	 * join L140MM2A
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "seq", referencedColumnName = "seq", nullable = false, insertable = false, updatable = false)})
	private L140MM2A l140mm2a;

	public void setL140mm2a(L140MM2A l140mm2a) {
		this.l140mm2a = l140mm2a;
	}

	public L140MM2A getL140mm2a() {
		return l140mm2a;
	}
}
