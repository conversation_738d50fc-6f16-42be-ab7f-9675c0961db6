/* 
 *DWASLNOVEROVSServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dw.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dw.service.DWASLNOVEROVSService;

import tw.com.jcs.common.Util;

/**<pre>
 * 授信逾放比例月檔(13月)
 * </pre>
 * @since  2012/2/6
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/2/6,REX,new
 *          </ul>
 */
@Service
public class DWASLNOVEROVSServiceImpl extends AbstractDWJdbc implements
		DWASLNOVEROVSService {

	@Override
	public List<Map<String, Object>> findDW_ASLNOVEROVSForRate(String[] ownBrIds) {
		String ownBridParams = Util.genSqlParam(ownBrIds);
		List<Object> params = new ArrayList<Object>();
		params.addAll(Arrays.asList(ownBrIds));
		params.addAll(Arrays.asList(ownBrIds));
//		StringBuffer temp = new StringBuffer(0);
//		for (String ownBrId : ownBrIds) {
//			temp.append(temp.length() > 0 ? "," : "");
//			temp.append("'");
//			temp.append(ownBrId);
//			temp.append("'");
//		}
		return this.getJdbc().queryForListByCustParam("DW_ASLNOVEROVS.selRate",
				new Object[] {ownBridParams, ownBridParams}, params.toArray(new Object[0]));
	}

}
