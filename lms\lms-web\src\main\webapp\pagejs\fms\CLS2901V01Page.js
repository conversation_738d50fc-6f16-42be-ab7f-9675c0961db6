$(function(){	
	var my_colModel = [];
	if(viewstatus == "030|0C0"){
		//全行黑名單查詢 030|0C0 的資料
		my_colModel.push({
			colHeader : i18n.cls2901v01['C900M01J.ownBrId'],
			name : 'ownBrId',
			width : 80,
			sortable : true});
	}
	if(true){
		my_colModel.push({
	        colHeader: "",name: 'oid', hidden: true
	    });
		my_colModel.push({
	    	colHeader: "",name: 'mainId', hidden: true
	    });
		my_colModel.push({
	        colHeader: i18n.cls2901v01["C900M01J.custId"], //被通報統一編號
	        align: "left", width: 110, sortable: true, name: 'custId',
	        formatter: 'click', onclick: openDoc
	    });
		my_colModel.push({
	        colHeader: i18n.cls2901v01["C900M01J.dupNo"], //重複序號
	        align: "left", width: 30, sortable: false, name: 'dupNo'
	    });
		my_colModel.push({
	        colHeader: i18n.cls2901v01["C900M01J.custName"], //被通報姓名/名稱
	        align: "left", width: 110, sortable: true, name: 'custName'
	    });
		my_colModel.push({
	        colHeader: i18n.cls2901v01["C900M01J.category"], //被通報類別
	        align: "left", width: 120, sortable: true, name: 'category'
	    });
		my_colModel.push({
	        colHeader: i18n.cls2901v01["C900M01J.lnflag"], //疑似代辦案件訊息
	        align: "left", width: 120, sortable: true, name: 'lnflag'
	    });
		my_colModel.push({
	        colHeader: i18n.cls2901v01["C900M01J.memo"], //備註
	        align: "left", width: 120, sortable: true, name: 'memo'
	    });
		my_colModel.push({
	        colHeader: i18n.cls2901v01["C900M01J.updater"], //異動人員
	        align: "left", width: 80, sortable: true, name: 'updater'
	    });
		my_colModel.push({
	        colHeader: i18n.cls2901v01["C900M01J.updateTime"], //異動日期
	        align: "left", width: 120, sortable: true, name: 'updateTime'
	    });
		
	}
	if(viewstatus == "0C0"){
		my_colModel.push({
	        colHeader: i18n.cls2901v01["C900M01J.dcUpdater"], //申請解除人員
	        align: "left", width: 80, sortable: true, name: 'dcUpdater'
	    });
		my_colModel.push({
	        colHeader: i18n.cls2901v01["C900M01J.dcUpdateTime"], //申請解除日期
	        align: "left", width: 120, sortable: true, name: 'dcUpdateTime'
	    });
	}else if(viewstatus == "0D0"){
		my_colModel.push({
	        colHeader: i18n.cls2901v01["C900M01J.dcApprover"], //核准解除人員
	        align: "left", width: 80, sortable: true, name: 'dcApprover'
	    });
		my_colModel.push({
	        colHeader: i18n.cls2901v01["C900M01J.dcApproveTime"], //核准解除日期
	        align: "left", width: 120, sortable: true, name: 'dcApproveTime'
	    });
	}
	
	var grid = $("#gridview").iGrid({
        handler: "cls2901gridhandler",
        height: 350,
        rowNum: 15,
        shrinkToFit: false,
        postData: {
            formAction: "queryMain",
            docStatus: viewstatus,
            ownBrId: userInfo.unitNo
        },
        colModel: my_colModel,
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
    	var postData = {
    			'mainOid': rowObject.oid, 
    			'mainId': rowObject.mainId,
    			'mainDocStatus': viewstatus
    		}
    	
    	if (typeof noOpenDoc != 'undefined' && noOpenDoc == 'Y'){
    		postData['noOpenDoc'] = true;
    	};
    	
		$.form.submit({
			url : '../fms/cls2901m01/01',
			data : postData,
			target : rowObject.oid || '_blank'
		});
    }
    
    $("#buttonPanel").find("#btnFilter").click(function(){
    	FilterAction.openBox();
    }).end().find("#btnAdd").click(function(){
		var rowObject = {};
		openDoc(null, null, rowObject);
    }).end().find("#btnDelete").click(function(){
    	var rows = grid.getGridParam('selrow');
        var list = "";
        if (rows != 'undefined' && rows != null && rows != 0) {
            var data = grid.getRowData(rows);
            list = data.oid;
        }
        if (list == "") {
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "cls2901formhandler",
                    type: "POST",
                    dataType: "json",
                    data: {
                        formAction: "deleteMark",
                        list: list,
                        docStatus: viewstatus
                    },
                    success: function(obj){
                    	if(obj.saveOkFlag){
                    		API.showMessage(i18n.def.runSuccess);//執行成功
                    	}
                    	grid.trigger("reloadGrid");
                    }
                });
            }
        });	
    });
});
