var initDfd = $.Deferred(), inits = {
    fhandle: "lms9131m01formhandler",
    ghaddle: "lms9131gridhandler"
};
// 驗證readOnly狀態
function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    // auth.readOnly ||
    if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
        return true;
    }
    return false;
}

$(document).ready(function(){
    $.ajax({
        handler: "lms9131m01formhandler",
        data: {
            formAction: "getbanchId"
        },
        success: function(obj){
            if (obj.userUnitType == 'B') {
                $("#btnUpdate918").hide();
                $("#btnUpdateArea").hide();
            }
            else 
                if (obj.userUnitType == 'A') {
                    $("#btnUpdate918").hide();
                }
        }
    });
    $.form.init({
        formHandler: inits.fhandle,
        formPostData: {// 把form上貼上資料
            formAction: "queryL120m01a",
            oid: responseJSON.oid
        },
        loadSuccess: function(json){
            responseJSON.mainDocStatus = json.docStatusVal;
            responseJSON.unitLoanCase = json.unitLoanCase;
            var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
            initDfd.resolve(json, auth);
            responseJSON.mainId = json.mainId;
            $("#check1").show();
            if (responseJSON.page == "01") {
                $("#bossIdVal").html(json.bossIdVal);
            }
        }
    });
    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
        var boss = $("#bossId").val();
        var selectBoss = boss.split(",");
        flowAction({
            page: responseJSON.page,
            saveData: true,
            selectBoss: selectBoss,
            showApprId: $("#showApprId").val(),
            AOId: $("#AO").val(),
            manager: $("#managerId").val(),
            recheck: $("#reCheckId").val(),
            UnitManagerId: $("#UnitManagerId").val(),
            CenterNo: $("#CenterNo").val(),
            AreaReCheckId: $("#AreaReCheckId").val(),
            AreaAppraiserId: $("#AreaAppraiserId").val(),
            UnitManagerAId: $("#UnitManagerAId").val(),
            HeadReCheckId: $("#HeadReCheckId").val(),
            HeadAppraiserId: $("#HeadAppraiserId").val(),
            UnitManagerHId: $("#UnitManagerHId").val(),
            HeadManager: $("#HeadManagerId").val(),
            HeadSubLeader: $("#HeadSubLeaderId").val(),
            AreaManager: $("#AreaManagerId").val(),
            AreaSubLeader: $("#AreaSubLeaderId").val(),
            CaseLvl: $("#caseLvl").val()
        });
    }).end().find("#btnUpdate").click(function(){
        UpdateBranch();
    }).end().find("#btnUpdateArea").click(function(){
        UpdateArea();
    }).end().find("#btnUpdate918").click(function(){
        UpdateH();
    });
    // 儲存的動作
    function saveData(showMsg, tofn){
        if ($("#L120M01AForm").valid()) {
            var allresult = {};
            var localresult = {};
            var selfresult = {};
            
            FormAction.open = true;
            $.ajax({
                handler: inits.fhandle,
                data: {// 把資料轉成json
                    formAction: "saveL120m01a",
                    page: responseJSON.page,
                    txCode: responseJSON.txCode,
                    showMsg: showMsg,
                    selfresult: JSON.stringify(selfresult),
                    localresult: JSON.stringify(localresult),
                    allresult: JSON.stringify(allresult)
                },
                success: function(obj){
                    if (responseJSON.page == "01") {
                        $('body').injectData(obj);
                    }
                    CommonAPI.triggerOpener("gridview", "reloadGrid");
                    if ($("#mainOid").val()) {
                        setRequiredSave(false);
                    }
                    else {
                        setRequiredSave(true);
                    }
                    responseJSON.oid = obj.oid;
                    responseJSON.mainOid = obj.mainOid;
                    responseJSON.mainId = obj.mainId;
                }
            });
        }
    }
    function flowAction(sendData){
        $.ajax({
            handler: inits.fhandle,
            data: $.extend({
                formAction: "saveL120m01a",
                mainOid: $("#mainOid").val()
            }, (sendData || {})),
            success: function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                API.showPopMessage(i18n.def["runSuccess"], window.close);
            }
        });
    }
    $("#numPerson").change(function(){
        var selectBoss = $("select[name^=boss]").map(function(){
            return $(this).val();
        }).toArray();
        var $newBossSpan = $("#newBossSpan");
        // 清空原本的
        $newBossSpan.empty();
        var newdiv = "";
        var val = parseInt($(this).val(), 10);
        if (val > 1) {
            for (var i = 2, count = val + 1; i < count; i++) {
                newdiv += "<div>" +
                i18n.lms9131m01['LMS913M01.no'] +
                i +
                i18n.lms9131m01['LMS913M01.site'] +
                "&nbsp;" +
                i18n.lms9131m01['LMS913M01.bossId'] +
                "&nbsp;&nbsp;&nbsp;<select name=boss" +
                i +
                " class='boss'/></div>";
            }
            $newBossSpan.append(newdiv);
            var copyOption = $("#mainBoss").html();
            $("[name^=boss]").html(copyOption);
            $("[name^=boss]").val("");
            var bosscount = 0;
            while (bosscount <= selectBoss.length) {
                $("[name=boss" + (bosscount + 1) + "]").val(selectBoss[bosscount]);
                bosscount++;
            }
        }
    });
    // 分行簽章欄修改
    function UpdateBranch(){
        $.ajax({
            handler: "lms9131m01formhandler",
            action: "branchData",
            data: {
                branchId: $("#caseBrId").val()
            },
            success: function(json){
                $(".boss").setItems({
                    item: json.bossList
                });
                $(".apprid").setItems({
                    item: json.appridList
                });
                $("#UnitManager").val($("#UnitManagerId").val());
                $("#manager").val($("#managerId").val());
                $("#reCheck").val($("#reCheckId").val());
                var boss = $("#bossId").val();
                var bosslist = boss.split(",");
                numPerson(bosslist.length, bosslist);
                var boss = $("#mainBoss").val();
                $("#ApprId").val($("#showApprId").val());
                $("#AOId").val($("#AO").val());
                update();
                
            }
        });
    }
    // 營運中心簽章欄修改
    function UpdateArea(){
        $.ajax({
            handler: "lms9131m01formhandler",
            action: "branchData",
            data: {
                branchId: $("#CenterNo").val()
            },
            success: function(json){
                $(".bossa").setItems({
                    item: json.bossList
                });
                $(".apprida").setItems({
                    item: json.appridList
                });
                $("#AreaReCheck").val($("#AreaReCheckId").val());
                $("#AreaManager").val($("#AreaManagerId").val());
                $("#AreaSubLeader").val($("#AreaSubLeaderId").val());
                $("#AreaAppraiser").val($("#AreaAppraiserId").val());
                $("#UnitManagerA").val($("#UnitManagerAId").val());
                updateArea();
            }
        });
    }
    function UpdateH(){
        $.ajax({
            handler: "lms9131m01formhandler",
            action: "branchData",
            data: {
                branchId: "918"
            },
            success: function(json){
                $(".bossh").setItems({
                    item: json.bossList
                });
                $(".appridh").setItems({
                    item: json.appridList
                });
                $("#HeadSubLeader").val($("#HeadSubLeaderId").val());
                $("#HeadManager").val($("#HeadManagerId").val());
                $("#HeadAppraiser").val($("#HeadAppraiserId").val());
                $("#HeadReCheck").val($("#HeadReCheckId").val());
                $("#UnitManagerH").val($("#UnitManagerHId").val());
                update918();
            }
        });
    }
    // 檢查陣列內容是否重複
    function checkArrayRepeat(arrVal){
        var newArray = [];
        for (var i = arrVal.length; i--;) {
            var val = arrVal[i];
            if ($.inArray(val, newArray) == -1) {
                newArray.push(val);
            }
            else {
                return true;
            }
        }
        return false;
    }
    function update(){
        $("#selectBossBox").thickbox({
            title: i18n.lms9131m01['LMS913S01.btnupdate'],
            width: 500,
            height: 350,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var bossval = "";
                    var selectBoss = $("select[name^=boss]").map(function(){
                        return $(this).val();
                    }).toArray();
                    var selectBossText = $("select[name^=boss]").map(function(){
                        return $(this).find(":selected").text();
                    }).toArray();
                    for (var i in selectBoss) {
                        if (selectBoss[i] == "") {
                            return CommonAPI.showErrorMessage(i18n.lms9131m01['LMS913M01.error2'] +
                            i18n.lms9131m01['LMS913M01.bossId']);
                        }
                        else {
                            bossval = bossval +
                            selectBoss[i] +
                            " " +
                            selectBossText[i] +
                            "<br/>"
                        }
                    }
                    if ($("#manager").val() == "") {
                        return CommonAPI.showErrorMessage(i18n.lms9131m01['LMS913M01.error2'] +
                        i18n.lms9131m01['LMS913S01.managerId']);
                    }
                    // 驗證是否有重複的主管
                    if (checkArrayRepeat(selectBoss)) {
                        // 主管人員名單重複請重新選擇
                        return CommonAPI.showErrorMessage(i18n.lms9131m01['LMS913M01.message31']);
                    }
                    $("#managerId").val($("#manager").val());
                    $("#managerIdVal").val(($("#manager").val() != '' ? $("#manager").find(":selected").text() : ''));
                    $("#reCheckId").val($("#reCheck").val());
                    $("#reCheckIdVal").val(($("#reCheck").val() != '' ? $("#reCheck").find(":selected").text() : ''));
                    $("#bossId").val(selectBoss.toString());
                    $("#bossIdVal").html(DOMPurify.sanitize(bossval));
                    $("#AO").val($("#AOId").val());// 修改帳戶管理員
                    $("#AOVal").val(($("#AOId").val() != '' ? $("#AOId").find(":selected").text() : ''));
                    $("#UnitManagerId").val($("#UnitManager").val());
                    $("#UnitManagerIdVal").val(($("#AOId").val() != '' ? $("#UnitManager").find(":selected").text() : ''));
                    $("#showApprId").val($("#ApprId").val());
                    $("#showApprIdVal").val(($("#ApprId").val() != '' ? $("#ApprId").find(":selected").text() : ''));
                    
                    $.thickbox.close();
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    };
    function update918(){
        $("#select918Box").thickbox({
            title: i18n.lms9131m01['LMS913S01.btn918update'],
            width: 500,
            height: 300,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    // if ($("#manager").val()
                    // == "") {
                    // return CommonAPI
                    // .showErrorMessage(i18n.lms9131m01['C102M01A.error2']
                    // +
                    // i18n.lms9131m01['LMS913S01.managerId']);
                    // }
                    // // 驗證是否有重複的主管
                    // if
                    // (checkArrayRepeat(selectBoss))
                    // {
                    // // 主管人員名單重複請重新選擇
                    // return CommonAPI
                    // .showErrorMessage(i18n.lms9131m01['C102M01B.message31']);
                    // }
                    $("#HeadAppraiserId").val($("#HeadAppraiser").val());
                    $("#HeadAppraiserIdVal").val(($("#UnitManagerH").val() != '' ? $("#HeadAppraiser").find(":selected").text() : ''));
                    $("#HeadReCheckId").val($("#HeadReCheck").val());
                    $("#HeadReCheckIdVal").val(($("#HeadReCheck").val() != '' ? $("#HeadReCheck").find(":selected").text() : ''));
                    $("#UnitManagerHId").val($("#UnitManagerH").val());
                    $("#UnitManagerHIdVal").val(($("#UnitManagerH").val() != '' ? $("#UnitManagerH").find(":selected").text() : ''));
                    $("#HeadManagerId").val($("#HeadManager").val());
                    $("#HeadManagerIdVal").val(($("#HeadManager").val() != '' ? $("#HeadManager").find(":selected").text() : ''));
                    $("#HeadSubLeaderId").val($("#HeadSubLeader").val());
                    $("#HeadSubLeaderIdVal").val(($("#HeadSubLeader").val() != '' ? $("#HeadSubLeader").find(":selected").text() : ''));
                    $.thickbox.close();
                },
                
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    };
    function updateArea(){
        $("#selectAreaBox").thickbox({// C102M01A.bt14=覆核
            title: i18n.lms9131m01['LMS913S01.btnAreaupdate'],
            width: 500,
            height: 300,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    $("#AreaAppraiserId").val($("#AreaAppraiser").val());
                    $("#AreaAppraiserIdVal").val(($("#AreaAppraiser").val() != '' ? $("#AreaAppraiser").find(":selected").text() : ''));
                    $("#AreaReCheckId").val($("#AreaReCheck").val());
                    $("#AreaReCheckIdVal").val(($("#AreaReCheck").val() != '' ? $("#AreaReCheck").find(":selected").text() : ''));
                    $("#UnitManagerAId").val($("#UnitManagerA").val());
                    $("#UnitManagerAIdVal").val(($("#UnitManagerA").val() != '' ? $("#UnitManagerA").find(":selected").text() : ''));
                    $("#AreaManagerId").val($("#AreaManager").val());
                    $("#AreaManagerIdVal").val(($("#AreaManager").val() != '' ? $("#AreaManager").find(":selected").text() : ''));
                    $("#AreaSubLeaderId").val($("#AreaSubLeader").val());
                    $("#AreaSubLeaderIdVal").val(($("#AreaSubLeader").val() != '' ? $("#AreaSubLeader").find(":selected").text() : ''));
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    };
    function numPerson(count, list){
        var $newBossSpan = $("#newBossSpan");
        $("#numPerson").val(count);
        // 清空原本的
        $newBossSpan.empty();
        var newdiv = "";
        var val = parseInt(count, 10);
        if (val > 1) {
            for (var i = 2, count = val + 1; i < count; i++) {
                newdiv += "<div>" +
                i18n.lms9131m01['LMS913M01.no'] +
                i +
                i18n.lms9131m01['LMS913M01.site'] +
                "&nbsp;" +
                i18n.lms9131m01['LMS913M01.bossId'] +
                "&nbsp;&nbsp;&nbsp;<select name=boss" +
                i +
                " class='boss'/></div>";
            }
            $newBossSpan.append(newdiv);
            var copyOption = $("#mainBoss").html();
            $("[name^=boss]").html(copyOption);
            for (var i = 1; i < count; i++) {
                $("[name=boss" + i + "]").val(list[i - 1]);
            }
        }
        else {
            $("[name=boss1]").val(list[0]);
        }
    }
});
