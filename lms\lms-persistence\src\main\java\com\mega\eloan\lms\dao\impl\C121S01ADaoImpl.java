package com.mega.eloan.lms.dao.impl;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C121S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121S01A;

/** 海外消金評等模型擔保品資料 **/
@Repository
public class C121S01ADaoImpl extends LMSJpaDao<C121S01A, String>
	implements C121S01ADao {

	@Override
	public C121S01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C121S01A findByC121M01A(C121M01A meta){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", meta.getMainId());
//		search.addSearchModeParameters(SearchMode.EQUALS, "ratingId", meta.getRatingId());
		return findUniqueOrNone(search);		
	}
}