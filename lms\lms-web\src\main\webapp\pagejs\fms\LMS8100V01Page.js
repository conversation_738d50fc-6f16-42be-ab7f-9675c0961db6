var inits = {
    fhandle: "lms8100m01formhandler",
    ghandle: "lms8100gridhandler"
};

$(document).ready(function(){

	var produceInterval = "HY";
	init();
	init_grid();
	
    $("#buttonPanel").find("#btnDelete").click(function(){
    	var rows = $("#gridview").getGridParam('selarrrow');
        var data = [];

        if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }

        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                for (var i in rows) {
                    data.push($("#gridview").getRowData(rows[i]).oid);
                }

                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteL300m01a",
                        oids: data
                    },
                    success: function(obj){
                        $("#gridview").trigger("reloadGrid");
                    }
                });
            }
        });
    }).end().find("#btnAdd").click(function(){
    	openNewCaseBox().done(function(result){
    		var $newDetailForm = $("#newCaseDetailForm");
    		$newDetailForm.reset();            // 初始化
    		
    		$("#bgnDate").val(result.newBgnDate);
			$("#endDate").val(result.newEndDate);
    		
            $("#newCaseDetailBox").thickbox({
                title: "",
                width: 550,
                height: 400,
                modal: true,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        if (!$("#newCaseDetailForm").valid()) {
                            return;
                        }
                        
                        chkCanAdd().done(function(){
                        	$.ajax({
                                handler: inits.fhandle,
                                action: 'newL300m01a',
                                data: {
                                	brList: getSelectItem("brList"),
                                    bgnDate: $("#bgnDate").val(),
                                    endDate: $("#endDate").val()
                                },
                                success: function(obj){
                                	$.thickbox.close();
                                    $("#gridview").trigger("reloadGrid");
                                }
                            });
                        });
                    },
                    "cancel": function(){
                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                            if (res) {
                                $.thickbox.close();
                            }
                        });
                    }
                }
            });
    	});
    }).end().find("#btnFilter").click(function(){
    	openFilterBox();
    }).end().find("#btnView").click(function(){
    	var rows = $("#gridview").getGridParam('selarrrow');
    	if (!rows) {	// action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
    	
    	var count = 0;
    	for (var i in rows) {
            count++;
        }
    	
    	if (count > 1) {	// grid.selrow=請先選擇一筆資料。
    		return CommonAPI.showMessage(i18n.def["grid.selrow"]);
        } else {
        	var id = $("#gridview").getGridParam('selrow');
            if (!id) {	// action_004=請先選擇需「調閱」之資料列
                return CommonAPI.showMessage(i18n.def["action_004"]);
            }
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnReturnToCompiling").click(function(){
    	// 退回編制中 參考 覆審LMS1800V01 end_to_01A
    	var row = $("#gridview").getGridParam('selrow');
        if (!row) {
            // grid.selrow=請先選擇一筆資料。
            return CommonAPI.showMessage(i18n.def["grid.selrow"]);
        }
        
        var rowData = $("#gridview").getRowData(row);
        
        if(rowData.produceFlag == "Y"){
        	return CommonAPI.showErrorMessage("已產生排名表，不可退回！");
        }
        
		$.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "returnToCompiling",
                oid: rowData.oid
            },
            success: function(obj){
            	$("#gridview").trigger("reloadGrid");
            }
        });
    }).end().find("#btnProduceRankingBoard").click(function(){
    	openNewCaseBox().done(function(result){
    		var bgnDate = result.newBgnDate;
    		var endDate = result.newEndDate;
    		
            $.ajax({
                handler: inits.fhandle,
                data: {
                    formAction: "genRbPdf",
                    bgnDate: bgnDate,
                    endDate: endDate
                },
                success: function(json){
                	if (json.msg && json.msg != "") {
                		$.form.submit({
                	        //url: "../../simple/FileProcessingService",
                			/* 
                			 * 因為 lms8100r01rptservice extends AbstractReportService
                			 * 所以 只有一層 ../
                			 */
                			url: "../simple/FileProcessingService",
                	        target: "_blank",
                	        data: {
                	        	bgnDate: bgnDate,
                	        	endDate: endDate,
                	        	type: "R02",
                	        	fileDownloadName: "lms8100r02.pdf",
                	        	serviceName: "lms8100r01rptservice"
                	        }
                	    });
                		$("#gridview").trigger("reloadGrid");
                	}
                }
            });
    	});
    }).end().find("#btnPrint").click(function(){
    	var rows = $("#gridview").getGridParam('selarrrow');
    	if (!rows) {	// action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
    	
    	var data = [];
    	
    	var count = 0;
    	for (var i in rows) {
    		data.push($("#gridview").getRowData(rows[i]).oid);
            count++;
        }
    	
    	if (count < 1) {	// grid.selrow=請先選擇一筆資料。
    		return CommonAPI.showMessage(i18n.def["grid.selrow"]);
        } else {
        	$.form.submit({
                url: "../simple/FileProcessingService",
                target: "_blank",
                data: {
                    oids: data,
                    type: "R01",
                    fileDownloadName: "lms8100r01.pdf",
                    serviceName: "lms8100r01rptservice"
                }
            });
        }
    });
    
    function init(){
        var filterForm = $("#filterForm");
    	if (txCode == "336101"){    // 編製中
	        $('#btnCheck').click(function(){
	    		$("input[name='brList']").attr('checked', true);
	    	});
	    	$('#btnUnCheck').click(function(){
	    		$("input[name='brList']").attr('checked', false);
	    	});
    	}
    	
    	// J-112-0461 授信覆審考核表-編製中-下拉選項統計由每半年改為每季統計。
    	$.ajax({
            handler: inits.fhandle,
            action: "queryProduceInterval",
            data: {},
            success: function(obj){
            	
            	produceInterval = obj.produceInterval; 
            	
            	if(obj.produceInterval==="S"){
                	$('#newTypeList option').remove();
                	
                	var newTypeList = $('#newTypeList');
                	newTypeList.append('<option value="1">第一季(1~3月)</option>');
                	newTypeList.append('<option value="2">第二季(4~6月)</option>');
                	newTypeList.append('<option value="3">第三季(7~9月)</option>');
                	newTypeList.append('<option value="4">第四季(10~12月)</option>');
            	}

            }
        });
    	
    	$.ajax({
            handler: inits.fhandle,
            action: "queryBrList",
            data: {},
            success: function(obj){            	
            	// 因為 setItems 排序會亂掉 所以自己組
            	var elmArr = [];	// 內容
                var colSize = 3;	// 一行幾個
            	$.each(obj.brListOrder, function(idx, brNo) {
            		var brName = obj.brList[brNo];
            		// =====新增的brListDiv分行清單
            		elmArr.push("<label style='letter-spacing:0px;cursor:pointer;font-weight:normal;'><input value='"+brNo+"' id='brList' name='brList' type='checkbox'>"+brNo +" "+ brName+"</label>");
            		
            		if (txCode != "336102"){    // 待覆核以外都有篩選
	            		// =====篩選的下拉選單
	            		var currobj = {};
	            		currobj[brNo] = brName;
	            		// 掃到高風險 $("#brIdFilter").setItems({
                        filterForm.find("#brIdFilter").setItems({
	                        item: currobj,
	                        clear:false,
	                        space: false,
	                        format: "{value} {key}"
	                    });
            		}
    			});
            	
            	// 補empty col
            	var addcnt = (colSize - (elmArr.length % colSize)); 
            	if(addcnt == colSize){
            		addcnt = 0;
            	}
            	for(var i=0; i<addcnt; i++){
            		elmArr.push("&nbsp;");
            	}
            	
            	var dyna = [];
            	dyna.push("<table width='100%' border='0' cellspacing='0' cellpadding='0'>");
            	dyna.push("<tr>");
            	for(var i=0; i<elmArr.length; i++){
            		dyna.push("<td>" + elmArr[i] + "</td>");
            		if( (i+1) % colSize==0){
            			dyna.push("</tr><tr>");		
            		}
            	}
            	dyna.push("</tr>");
            	dyna.push("</table>");
            	
            	if (txCode == "336101"){    // 編製中
            		$("#brListDiv").append(dyna.join("\n"));
            	}
            	/*
            	var branchList = {};
                if (!$.isEmptyObject(obj.brList)) {
                	branchList = obj.brList;
                }
                $("#brList").setItems({
                    item: branchList,
                    itemType: 'checkbox',
                    format: "{value} {key}",
                    size: 3
                });
                */
            }
        });
    }
    
    function init_grid(){
    	// if (txCode == "336093"){ // 編製完成
    	var grid = $("#gridview").iGrid({
            handler: inits.ghandle,//'lms8100gridhandler',
            height: 350,
            width: 785,
            autowidth: false,
            action: "queryGridL300M01A",
            postData: {
                docStatus: viewstatus,
                searchType: "init"
            },
            rowNum: 15,
            sortname: "createTime|bgnDate|branchId",
            sortorder: "desc|desc|asc",
            multiselect: ((userInfo.unitNo == "918" && txCode == "336103") ? true : (txCode == "336101" ? true : false)),
            colModel: [{
            	colHeader: i18n.abstracteloan['doc.branchName'],// "分行名稱",
            	width: 100, sortable: true, name: 'branchId',
                formatter: 'click', onclick: openDoc
            }, {
            	colHeader: i18n.abstracteloan['doc.lastUpdater'],// "最後異動人員",
            	align: "center", width: 125, sortable: true, name: 'updater'
            }, {
            	colHeader: i18n.lms8100v01['L300M01A.dataDate'],
            	align: "center", width: 200, sortable: false, name: 'dataDate'
            }, {
            	colHeader: i18n.lms8100v01['L300M01A.createTime'],
            	align: "center", width: 200, sortable: true, name: 'createTime',
            	formatter: 'date',
            	formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d'
                }
            }, {
            	colHeader: i18n.lms8100v01['L300M01A.produceFlag'],
            	align: "center", width: 100, sortable: false, name: 'produceFlag',
            	hidden: (txCode == "336103" ? false : true)
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }],
            ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
				 var data = $("#gridview").getRowData(rowid);
				 openDoc(null, null, data);
            }
        });
    }
    
    function openDoc(cellvalue, options, rowObject){
        $.form.submit({			
            url: '../fms/lms8100m01/01',
            data: {
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus
            },
            target: rowObject.oid
        });
    }
    
    function openNewCaseBox(){
        var my_dfd = $.Deferred();
        var $newForm = $("#newCaseForm");
        $newForm.reset();            // 初始化
        
        var tDate = new Date();
        $("#newTypeYear").val(tDate.getFullYear());

        $("#newCaseBox").thickbox({
            title: "",
            width: 150,
            height: 150,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#newCaseForm").valid()) {
                        return;
                    }
                    newType = $("#newTypeList option:selected").val();
                    $.thickbox.close();
                    var newBgnDate = "";
            		var newEndDate = "";
            		
            		// J-112-0461 授信覆審考核表-編製中-下拉選項統計由每半年改為每季統計。  
            		if(produceInterval === "S"){
                		if(newType == "1"){
                			newBgnDate = ($.trim($("#newTypeYear").val()) + "-01-01");
                			newEndDate = ($.trim($("#newTypeYear").val()) + "-03-31");
                		} else if(newType == "2"){
                			newBgnDate = ($.trim($("#newTypeYear").val()) + "-04-01");
                			newEndDate = ($.trim($("#newTypeYear").val()) + "-06-30");
                		} else if(newType == "3"){
                			newBgnDate = ($.trim($("#newTypeYear").val()) + "-07-01");
                			newEndDate = ($.trim($("#newTypeYear").val()) + "-09-30");
                		} else if(newType == "4"){
                			newBgnDate = ($.trim($("#newTypeYear").val()) + "-10-01");
                			newEndDate = ($.trim($("#newTypeYear").val()) + "-12-31");
                		} 
            		}else{
                		if(newType == "1"){
                			newBgnDate = ($.trim($("#newTypeYear").val()) + "-01-01");
                			newEndDate = ($.trim($("#newTypeYear").val()) + "-06-30");
                		} else if(newType == "2"){
                			newBgnDate = ($.trim($("#newTypeYear").val()) + "-07-01");
                			newEndDate = ($.trim($("#newTypeYear").val()) + "-12-31");
                		}
            		}

                    my_dfd.resolve({'newType': newType, 'newTypeYear': $.trim($("#newTypeYear").val()),
                    	'newBgnDate': newBgnDate, 'newEndDate': newEndDate});
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
        return my_dfd.promise();
    }
    
    // 篩選
    function openFilterBox(){
        var $filterForm = $("#filterForm");
        $filterForm.reset();            // 初始化
        
        $("#filterBox").thickbox({
            title: i18n.abstracteloan["button.filter"],
            width: 350,
            height: 210,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#filterForm").valid()) {
                        return;
                    }

                    if ($("#yearFilter").val() == '' && $("#yearFilter").val() == '') {
                    	// common.001=欄位檢核未完成，請填妥後再送出
                    	return CommonAPI.showErrorMessage(i18n.def["common.001"]);
                    } else {
                        $("#gridview").jqGrid("setGridParam", {
                            postData: {
                            	yearFilter: $("#yearFilter").val(),
                            	brIdFilter: $("#brIdFilter").val(),
                            	searchType: "filter"
                            },
                            search: true
                        }).trigger("reloadGrid");
                    }
                    
                    $.thickbox.close();
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
    }
    
    function chkCanAdd(){
    	var my_dfd = $.Deferred();
    	
    	var bgnDate = $("#bgnDate").val(); // 起日
        var endDate = $("#endDate").val(); // 止日
        if ($.trim(bgnDate) == "" || $.trim(endDate) == "") {
            // common.001=欄位檢核未完成，請填妥後再送出
        	return CommonAPI.showErrorMessage(i18n.def["common.001"]);
        }
        
        var brs = getSelectItem("brList");
        if(brs == "" || brs == undefined){
        	// common.001=欄位檢核未完成，請填妥後再送出
        	return CommonAPI.showErrorMessage(i18n.def["common.001"]);
        }
        
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "chkCanAdd",
                brList: brs,
                bgnDate: bgnDate,
                endDate: endDate
            },
            success: function(json){
            	if (json.msg && json.msg != "") {
            		return API.showErrorMessage(json.msg);
            	} else {
            		my_dfd.resolve();
            	}
            }
        });
    	
    	return my_dfd.promise();
    }
    
    function getSelectItem(name){
        var data = [];
        $("input[name=" + name + "]:checked").each(function(v, k){
            data.push($(k).val());
        });
        return data.join("|");
    }
});

