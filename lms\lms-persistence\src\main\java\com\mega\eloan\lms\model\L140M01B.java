/* 
 * L140M01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.IDocObject;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 額度敘述說明檔 **/
@NamedEntityGraph(name = "L140M01B-entity-graph", attributeNodes = { @NamedAttributeNode("l140m01a") })
@Entity
@Table(name = "L140M01B", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "itemType" }))
public class L140M01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 項目類別
	 * <p/>
	 * 1限額條件<br/>
	 * 2利(費)率 <br/>
	 * 3擔保品<br/>
	 * 4其他敘做條件<br/>
	 * 5敘做條件異動情形<br/>
	 * 6附表第一頁<br/>
	 * 7附表第二頁 <br/>
	 * 8附表第三頁 <br/>
	 * 100/11/29調整<br/>
	 * 9動撥提示用語 <br/>
	 * A總處核定意見<br/>
	 * B董事會意見
	 * 
	 * 101/02/21調整<br/>
	 * C聯貸說明 (※國內個金)<br/>
	 * D 國內個金團貸借保人說明(※國內個金)<br/>
	 * X央行購置描述<br/>
	 * Y大陸地區授信業務控管註記
	 */
	@Column(name = "ITEMTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String itemType;

	/**
	 * 列印於主表
	 * <p/>
	 * 0.印於主表(預設)<br/>
	 * 1.印於附表第一頁<br/>
	 * 2.印於附表第二頁<br/>
	 * 3.印於附表第三頁
	 */
	@Column(name = "PAGENUM", length = 1, columnDefinition = "CHAR(1)")
	private String pageNum;

	/** 項目說明 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "ITEMDSCR", columnDefinition = "CLOB")
	private String itemDscr;

	/**
	 * 動撥提示用語
	 * <p/>
	 * 存放內容：<br/>
	 * 1.動撥提示用語 for「9動撥提示用語」注意事項 最多只能輸入３０個全型字！<br/>
	 * 2.總處核定意見 <br/>
	 * 3.董事會意見
	 */
	@Column(name = "TOALOAN", length = 3000, columnDefinition = "VARCHAR(3000)")
	private String toALoan;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

    /**
     * 其他敘做條件 編輯方式
     * <p/>
     * itemType = 4
     * <p/>
     * 1.自由格式<br/>
     * 2.樣板格式
     **/
    @Column(name = "FORMATTYPE", length = 1, columnDefinition = "VARCHAR(1)")
    private String formatType;

	/**
	 * JOIN條件 關聯檔
	 * 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false)
	private L140M01A l140m01a;

	public L140M01A getL140m01a() {
		return l140m01a;
	}

	public void setL140m01a(L140M01A l140m01a) {
		this.l140m01a = l140m01a;
	}
    
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得項目類別
	 * <p/>
	 * 1限額條件<br/>
	 * 2利(費)率 <br/>
	 * 3擔保品<br/>
	 * 4其他敘做條件<br/>
	 * 5敘做條件異動情形<br/>
	 * 6附表第一頁<br/>
	 * 7附表第二頁 <br/>
	 * 8附表第三頁 <br/>
	 * 100/11/29調整<br/>
	 * 9動撥提示用語 <br/>
	 * A總處核定意見<br/>
	 * B董事會意見
	 * 
	 * 101/02/21調整<br/>
	 * C聯貸說明 (※國內個金)<br/>
	 * D 國內個金團貸借保人說明(※國內個金)<br/>
	 * X央行購置描述<br/>
	 */
	public String getItemType() {
		return this.itemType;
	}

	/**
	 * 設定項目類別
	 * <p/>
	 * 1限額條件<br/>
	 * 2利(費)率 <br/>
	 * 3擔保品<br/>
	 * 4其他敘做條件<br/>
	 * 5敘做條件異動情形<br/>
	 * 6附表第一頁<br/>
	 * 7附表第二頁 <br/>
	 * 8附表第三頁 <br/>
	 * 100/11/29調整<br/>
	 * 9動撥提示用語 <br/>
	 * A總處核定意見<br/>
	 * B董事會意見
	 * 
	 * 101/02/21調整<br/>
	 * C聯貸說明 (※國內個金)	<br/>
	 * D 國內個金團貸借保人說明(※國內個金)<br/>
	 * X央行購置描述<br/>
	 * Y 大陸地區授信業務控管註記<br/>
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/**
	 * 取得列印於主表
	 * <p/>
	 * 0.印於主表(預設)<br/>
	 * 1.印於附表第一頁<br/>
	 * 2.印於附表第二頁<br/>
	 * 3.印於附表第三頁
	 */
	public String getPageNum() {
		return this.pageNum;
	}

	/**
	 * 設定列印於主表
	 * <p/>
	 * 0.印於主表(預設)<br/>
	 * 1.印於附表第一頁<br/>
	 * 2.印於附表第二頁<br/>
	 * 3.印於附表第三頁
	 **/
	public void setPageNum(String value) {
		this.pageNum = value;
	}

	/** 取得項目說明 **/
	public String getItemDscr() {
		return this.itemDscr;
	}

	/** 設定項目說明 **/
	public void setItemDscr(String value) {
		this.itemDscr = value;
	}

	/**
	 * 取得動撥提示用語
	 * <p/>
	 * 存放內容：<br/>
	 * 1.動撥提示用語 for「9動撥提示用語」注意事項 最多只能輸入３０個全型字！<br/>
	 * 2.總處核定意見 <br/>
	 * 3.董事會意見
	 */
	public String getToALoan() {
		return this.toALoan;
	}

	/**
	 * 設定動撥提示用語
	 * <p/>
	 * 存放內容：<br/>
	 * 1.動撥提示用語 for「9動撥提示用語」注意事項 最多只能輸入３０個全型字！<br/>
	 * 2.總處核定意見 <br/>
	 * 3.董事會意見
	 **/
	public void setToALoan(String value) {
		this.toALoan = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

    /**
     * 取得其他敘做條件 編輯方式
     * <p/>
     * itemType = 4
     * <p/>
     * 1.自由格式<br/>
     * 2.樣板格式
     **/
    public String getFormatType() {
        return this.formatType;
    }

    /**
     * 設定其他敘做條件 編輯方式
     * <p/>
     * itemType = 4
     * <p/>
     * 1.自由格式<br/>
     * 2.樣板格式
     **/
    public void setFormatType(String value) {
        this.formatType = value;
    }
}
