package com.mega.eloan.lms.crs.report.impl;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import com.iisigroup.cap.component.PageParameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.report.LMS2401R01RptService;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.sso.service.BranchService;

@Service("lms2401r01rptservice")
public class LMS2401R01RptServiceImpl implements FileDownloadService, LMS2401R01RptService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS2401R01RptServiceImpl.class);
	private static final int PREFIX_LEN =3;
	@Resource
	RetrialService retrialService;
	
	@Resource
	BranchService branch;

	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception 
	 * @throws IOException 
	 */
	public OutputStream generateReport(PageParameters params) throws IOException, Exception {
	
		List<InputStream> list = new LinkedList<InputStream>();
		String oid = Util.trim(params.getString("oid"));
		OutputStream outputStream = null;
		Locale locale = null;
		
		
		int subLine = 9;//此數值對應的(x,y).要查 PdfTools.並不一定是愈小,愈上面
		try {
			locale = LMSUtil.getLocale();
			Properties propEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
			
			Map<InputStream,Integer> pdfNameMap = new LinkedHashMap<InputStream,Integer>();
			outputStream = null;
			
			C240M01A meta = retrialService.findC240M01A_oid(oid);
			if(meta!=null){
				outputStream = genLMS2401R01(locale, meta);	
			}		
			
			if(outputStream != null){
				pdfNameMap.put(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()),subLine);
			}else{
				pdfNameMap.put(null,subLine);
			}
			
			
			if(pdfNameMap != null && pdfNameMap.size() > 0){
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream, propEloanPage.getProperty("PaginationText"), true,locale,subLine);
				list.add(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()));
			}	
			
			
			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(list, outputStream);
		}finally{
			
		}
		return outputStream;
	}

	@Override
	public byte[] getContent(PageParameters params) throws Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}
	
	public OutputStream genLMS2401R01(Locale locale, C240M01A meta)
			throws FileNotFoundException, IOException, Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		
		String rptFile = "LMS2401R01";
		
		ReportGenerator generator = new ReportGenerator("report/crs/"+rptFile+"_" + locale.toString() + ".rpt");

		OutputStream outputStream = null;		
		
		try {

			// 分行名稱
			rptVariableMap.put("BRANCHNAME", Util.trim(branch.getBranchName(meta.getBranchId()))+"("+meta.getBranchId()+")");
			rptVariableMap.put("C240M01A.RANDOMCODE", meta.getRandomCode());
			rptVariableMap.put("C240M01A.RETRIALDATE", TWNDate.toAD(meta.getExpectedRetrialDate()));
			if(true){
				List<C241M01A> c241m01a_list = retrialService.getCrsRetrialSummaryList(meta);
				rptVariableMap.put("C240M01A.REQUANTITY", Util.trim(c241m01a_list.size()));
				rptVariableMap.put("CONTENT", getContentString(retrialService.getCrsRetrialSummary(c241m01a_list)));	
			}

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);

			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}			
		}
		return outputStream;
	}
	
	private String getContentString(TreeMap<String, List<String> > map){
		boolean haveData = false;
		StringBuffer sb = new StringBuffer();
		sb.append("<table border='0'>");		
		for(String keyStr:map.keySet()){
			List<String> detailList = map.get(keyStr);
			if(CollectionUtils.isEmpty(detailList)){
				continue;
			}
			haveData = true;
			int detailCnt = detailList.size();
			sb.append("<tr><td colspan='3'>");
			sb.append("第").append(NumberUtils.toInt(keyStr.substring(0, CrsUtil.SUMMARY_PREFIX_LEN))).append("項").append(keyStr.substring(PREFIX_LEN)).append("共"+detailCnt+"戶");
			sb.append("</td></tr>");
			for(int i=0;i<detailCnt; i++){
				sb.append("<tr>");
				sb.append("<td>").append("　").append("</td>");
				sb.append("<td>(").append(i+1).append(")</td>");
				sb.append("<td>").append(detailList.get(i)).append("</td>");
				sb.append("</tr>");
			}
			
		}
		sb.append("</table>");
		
		return haveData?sb.toString():"本分行本次覆審無任何缺失。";
	}
	
}
