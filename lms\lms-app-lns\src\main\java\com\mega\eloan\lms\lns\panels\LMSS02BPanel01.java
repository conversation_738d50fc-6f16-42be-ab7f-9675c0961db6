package com.mega.eloan.lms.lns.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 借款人基本資料(企金授權外) - 基本資料
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class LMSS02BPanel01 extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public LMSS02BPanel01(String id) {
		super(id);
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		new LMSS02DPanel("lmss02d_panel").processPanelData(model, params);
	}
}
