/* 
 *ObsdbELF401ServiceImpl.java
 */
package com.mega.eloan.lms.obsdb.service.impl;

import java.sql.Types;
import java.util.List;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbELF401Service;

/**
 * <pre>
 * 主從債務人檔  ELF401
 * </pre>
 * 
 */
@Service
public class ObsdbELF401ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF401Service {
	@Override
	public void insert(String BRNID, List<Object[]> dataList) {
		this.getJdbc(BRNID).batchUpdate(
				"ELF401.insert", 
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.DATE, Types.CHAR,
						Types.DATE, Types.DECIMAL, Types.DECIMAL, Types.CHAR, Types.DECIMAL, Types.CHAR},
				dataList);

	}
	
	@Override
	public void delEllngteeByUniqueKey(String BRNID, String brNo, String custId,
			String dupNo, String cntrNo) {
		this.getJdbc(BRNID).update("ELF401.delByUniqueKey",
				new Object[] { custId, dupNo, cntrNo });

	}
}
