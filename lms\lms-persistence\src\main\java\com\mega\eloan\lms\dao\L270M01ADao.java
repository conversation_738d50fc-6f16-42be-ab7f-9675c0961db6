/* 
 * L270M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L270M01A;

/** 企金信保央行C申請書 **/
public interface L270M01ADao extends IGenericDao<L270M01A> {

	L270M01A findByOid(String oid);
	
	L270M01A findByMainId(String mainId);
	
	List<L270M01A> findByDocStatus(String docStatus);
	
	L270M01A findByUniqueKey(String approveNo);

	List<L270M01A> findByIndex01(String mainId);

	List<L270M01A> findByIndex02(String ownBrId, String custId, String dupNo);
	
	List<L270M01A> findByApprNoCustId(String approveNo,String custId);
}