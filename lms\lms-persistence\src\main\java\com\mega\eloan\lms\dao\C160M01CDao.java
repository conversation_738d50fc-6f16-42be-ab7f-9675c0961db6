/* 
 * C160M01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C160M01C;

/** 檢附資訊檔明細檔 **/
public interface C160M01CDao extends IGenericDao<C160M01C> {

	C160M01C findByOid(String oid);
	
	List<C160M01C> findByMainId(String mainId);
	
	C160M01C findByUniqueKey(String oid);

	List<C160M01C> findByIndex01(String oid);

	C160M01C findByUniqueKey(String mainId, String itemCode, String itemType);
}