/* 
 * C120M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120M01A;


/** 個金徵信借款人主檔 **/
public interface C120M01ADao extends IGenericDao<C120M01A> {

	C120M01A findByOid(String oid);
	List<C120M01A> findByOids(String[] oids);

	List<C120M01A> findByMainId(String mainId);
	List<C120M01A> findByMainIdForOrder(String mainId);
	List<C120M01A> findByMainIdOrderByCustId(String mainId);

	C120M01A findC120M01A_o_single_Y(String ownBrId, String custId, String dupNo);
	
	C120M01A findByUniqueKey(String mainId, String ownBrId, String custId,
			String dupNo);
	
	C120M01A findByUniqueKey(String mainId, String custId,
			String dupNo);

	List<C120M01A> findByIndex01(String mainId, String ownBrId, String custId,
			String dupNo);
	
	List<C120M01A> findByCustIdDupId(String custId,String DupNo);
	
	 /**
	  * 查詢主要借款人
	 * @param mainId
	 * @param keyMan
	 * @return
	 */
	C120M01A findByMainIdAndKeyMan(String mainId, String keyMan);
	int deleteByOid(String oid);
	int deleteByMainId(String mainId);
	
	List<C120M01A> findByMainId_orderBy_keymanCustposCustid(String mainId);
	List<C120M01A> findByCaseId(String caseId);
}