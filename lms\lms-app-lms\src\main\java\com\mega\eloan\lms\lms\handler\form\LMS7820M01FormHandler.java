/* 
 *  LMS7820M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.form;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.lms.service.LMS7820Service;
import com.mega.eloan.lms.model.L782M01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 特殊案件登錄表
 * </pre>
 * 
 * @since 2011/12/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/9,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7820m01formhandler")
@DomainClass(L782M01A.class)
public class LMS7820M01FormHandler extends AbstractFormHandler {

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMS7820Service lms7820Service;

	@Resource
	CodeTypeService codeTypeService;

	/**
	 * 查詢登錄特殊案件需要資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL782m01a(PageParameters params)
			throws CapException {

		String oid = params.getString(EloanConstants.OID);

		L782M01A l782m01a = lms7820Service.findModelByOid(L782M01A.class, oid);

		CapAjaxFormResult result = DataParse.toResult(l782m01a);
		result.set(
				"branchName",
				l782m01a.getCaseBrId() + " "
						+ branchService.getBranchName(l782m01a.getCaseBrId()));

		result.set("creator", this.getUserName(l782m01a.getCreator()));
		result.set("updater", this.getUserName(l782m01a.getUpdater()));
		result.set("typCd", getMessage("typCd." + l782m01a.getTypCd()));
		result.set(
				"loanTP",
				codeTypeService.findByCodeTypeAndCodeValue(
						UtilConstants.CodeTypeItem.授信科目, l782m01a.getLoanTP())
						.getCodeDesc());
		result.set("showTypCd", getMessage("typCd." + l782m01a.getTypCd()));
		result.set("showCustId",
				StrUtils.concat(l782m01a.getCustId(), " ", l782m01a.getDupNo()));
		result.set("showCustName", l782m01a.getCustName());
		return result;

	}

	/**
	 * 儲存特殊案件登錄
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL782m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		String formL782m01a = params.getString("L782M01AForm");
		JSONObject jsonL782m01a = JSONObject.fromObject(formL782m01a);
		L782M01A l782m01a = lms7820Service.findModelByOid(L782M01A.class, oid);
		DataParse.toBean(jsonL782m01a, l782m01a);
		lms7820Service.save(l782m01a);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	/**
	 * 刪除特殊案件登錄
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL782m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		L782M01A l782m01a = lms7820Service.findModelByOid(L782M01A.class, oid);
		lms7820Service.delete(l782m01a);
		return result;
	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (Util.isEmpty(userId)) {
			return "";
		}
		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}
}
