/* 
 * L180M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L180M01A;

/** 覆審名單主檔 **/
public interface L180M01ADao extends IGenericDao<L180M01A> {

	L180M01A findByOid(String oid);

	L180M01A findByMainId(String mainId);

	List<L180M01A> findByBranchAnddataDate(String Branch, Date dataDate);

	L180M01A findById(String mainId, String id, String dupno);

	List<Object[]> findMaxbatchNO();

	List<L180M01A> findMaxbatchNONextYear();

	List<L180M01A> findMaxDataDate(String branch, Date retrialDate);

	/**
	 * J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
	 * 
	 * @param branch
	 * @param rpaKey
	 * @return
	 */
	List<L180M01A> findByBranchIdAndLikeMainId(String branch, String likeMainId);

	List<L180M01A> findByDefaultCTLDate(String docStatus, String bgnDate,
			String endDate);

}