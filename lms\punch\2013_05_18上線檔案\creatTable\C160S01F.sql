---------------------------------------------------------
-- LMS.C160S01F 代償轉貸借新還舊明細檔
---------------------------------------------------------


---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.C160S01F;
CREATE TABLE LMS.C160S01F (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	REFMAINID     CHAR(32)      not null,
	SEQ           DECIMAL(5,0)  not null,
	BANKNO        CHAR(3)       not null,
	BRANCHNO      VARCHAR(7)    not null,
	SUBACNO       VARCHAR(14)  ,
	BANKNAME      VARCHAR(60)  ,
	BRANCHNAME    VARCHAR(60)  ,
	SUBAMT        DECIMAL(13,0),
	OLNAPPDATE    DATE         ,
	OLNENDDATE    DATE         ,
	OLNREMYEAR    DECIMAL(2,0) ,
	TAXNO         VARCHAR(60)  ,
	SUBREASON     CHAR(1)      ,
	SUBREAOTH     VARCHAR(30)  ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_C160S01F PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC160S01F01;
--CREATE UNIQUE INDEX LMS.XC160S01F01 ON LMS.C160S01F   (MAINID, SEQ, BANKNO, BRANCHNO, SUBACNO, REFMAINID);
--J-112-0205 代償取消unique key
--DROP INDEX LMS.XC160S01F01;
CREATE INDEX LMS.XC160S01F01 ON LMS.C160S01F (MAINID, SEQ, BANKNO, BRANCHNO, SUBACNO, REFMAINID) ALLOW REVERSE SCANS;
--runstats on table LMS.C160S01F with distribution and detailed index all;

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C160S01F IS '代償轉貸借新還舊明細檔';
COMMENT ON LMS.C160S01F (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	REFMAINID     IS '額度明細表來源mainId', 
	SEQ           IS '序號', 
	BANKNO        IS '轉出之原金融機構代碼', 
	BRANCHNO      IS '分行代號', 
	SUBACNO       IS '代償本行帳號', 
	BANKNAME      IS '行庫名稱', 
	BRANCHNAME    IS '分行名稱', 
	SUBAMT        IS '代償金額', 
	OLNAPPDATE    IS '原貸放日期', 
	OLNENDDATE    IS '原貸款到期日', 
	OLNREMYEAR    IS '剩餘貸款年限(年)', 
	TAXNO         IS '設定抵押房屋稅籍編號', 
	SUBREASON     IS '代償同業房貸原因', 
	SUBREAOTH     IS '代償同業房貸原因(其他)', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);

--J-112-0205 代償
ALTER TABLE LMS.C160S01F ADD REPAYMENTPRODUCTTYPE varchar(30);
COMMENT ON LMS.C160S01F (REPAYMENTPRODUCTTYPE IS '代償產品別' ) ;

ALTER TABLE LMS.C160S01F ADD REPAYMENTPRODUCT varchar(1);
COMMENT ON LMS.C160S01F (REPAYMENTPRODUCT IS '代償產品' ) ;

ALTER TABLE LMS.C160S01F ADD ACCNO varchar(15);
COMMENT ON LMS.C160S01F (ACCNO IS '帳號' ) ;

ALTER TABLE LMS.C160S01F ADD CUSTNAME varchar(120);
COMMENT ON LMS.C160S01F (CUSTNAME IS '戶名' ) ;

ALTER TABLE LMS.C160S01F ADD ORIGINALAMT DECIMAL(13,0);
COMMENT ON LMS.C160S01F (ORIGINALAMT IS '聯徵原始額度' ) ;