/* 
 * LMS9031GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.grid;

import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.fms.pages.LMS9031V00Page;
import com.mega.eloan.lms.mfaloan.service.MisELF348Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("lms9031gridhandler")
public class LMS9031GridHandler extends AbstractGridHandler {

	@Resource
	MisELF348Service mis348Service;

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL903m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties prop = null;
		prop = MessageBundleScriptCreator
				.getComponentResource(LMS9031V00Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branchId",
				user.getUnitNo());
		String SITE1 = params.getString("fCity");
		String SITE2 = params.getString("fZip");
		String SITE3 = params.getString("SITE3");
		String SITE4 = params.getString("SITE4");
		String LNNO1 = params.getString("LNNO1");
		String LNNO2 = params.getString("LNNO2");
		List<Map<String, Object>> map = null;
		String SITE4Boolean = "%";
		if (Util.equals(SITE4Boolean, SITE4)) {
			map = mis348Service.getELF348DPChk(SITE1, SITE2, SITE3, LNNO1,
					LNNO2);
		} else {
			map = mis348Service.getELF348DPChk(SITE1, SITE2, SITE3, SITE4,
					LNNO1, LNNO2);
		}
		if(Util.isEmpty(map)||map.size()<=0){
			throw new CapMessageException(prop.getProperty("L903M01a.error"),
					getClass());
		}
		return new CapMapGridResult(map, map.size());
	}

}
