/* 
 * MisIquotjonService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.util.List;

/**
 * <pre>
 * 共同借款人檔 IQUOTJON(MIS.ELV38901)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
public interface MisIquotjonService {

	/**
	 * 動審表 -新增 共同借款人 Iquotjon
	 * 
	 * @param dataList
	 * <pre>
	 *      dataList content
	 * quotaNo 額度序號
	 * custId  客戶統編
	 * dupNo   重複序號
	 * joinName 共同借款人姓名
	 * updater 資料修改人
	 * </pre>
	 */
	public void insert(List<Object[]> dataList);

	/**
	 * 動審表 -根據額度序號查詢刪除該額度序號 共同借款人檔 Iquotjon
	 * 
	 * @param args
	 *            所有的額度序號
	 */
	void delByCntrNo(String args);
}
