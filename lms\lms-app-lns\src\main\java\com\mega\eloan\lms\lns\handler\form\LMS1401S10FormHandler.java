/*
 * LMS1401S10FormHandler.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.form;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lns.pages.LMS1401S10PageV20220812;
import com.mega.eloan.lms.lns.pages.LMS1401S10PageV20250101;
import com.mega.eloan.lms.lns.panels.LMS1401S10Panel;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S24A;
import com.mega.eloan.lms.model.L120S24B;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 額度明細表 - 風控風險權數
 * </pre>
 *
 * @since 2022/08/
 * <AUTHOR>
 * @version <ul>
 *          <li>2022/08/,009301,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1401s10formhandler")
public class LMS1401S10FormHandler extends AbstractFormHandler {

    @Resource
    LMSService lmsService;

    @Resource
    LMS1201Service lms1201Service;

    @Resource
    LMS1401Service lms1401Service;
    
	@Resource
	SysParameterService sysParameterService;

	@Resource
	CodeTypeService codeTypeService;
	
    /**
	 * J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯
	 * 產生主檔!!
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult genL120s24a(PageParameters params) throws CapException {
		
		Properties pop = MessageBundleScriptCreator.getComponentResource(LMS1401S10Panel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		
		// always產生最新的檢核表
		// 要檢查一下風險權數2025版是否啟用
		String L120S24A_20250101_ON = lmsService.isL120s24a2025On();
		// 如果已經啟用，最新的版本就是20250101
		String versionDate = "Y".equals(L120S24A_20250101_ON) ? UtilConstants.L120s24aVersion.Ver_20250101
				: UtilConstants.L120s24aVersion.Ver_20220812;
		
		String genMessage = lmsService.genL120s24a(mainId, versionDate, pop);
		// 印出產生成功訊息
		String message = pop.getProperty("L120S24A.message.gen", "產生成功");
		if(Util.isNotEmpty(genMessage)){
			message = message + "<BR/><BR/>" + genMessage;
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, message);
		return result;
	}
	
	/**
	 * J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯
	 * 撈風險權數主檔
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s24a(PageParameters params)	throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		L120S24A l120s24a = lmsService.findL120s24aByOid(oid);
		String versionDate = l120s24a.getVersionDate_s24a();// 版本日期
		// 列出的是不要放進result的欄位值
		result = DataParse.toResult(l120s24a, DataParse.Delete, new String[] {
				EloanConstants.MAIN_ID, "creator", "createTime",
				"updater", "updateTime"});
		
		// 比較特別的就是LTV分類，央行管制暴險的Y、N 會決定值要丟到哪個SELECT裡
		if(Util.isNotEmpty(l120s24a.getLTVClass_s24a())){
			if("Y".equals(Util.trim(l120s24a.getIsCBControl_s24a()))){
				result.set("LTVClass_s24a_Y", l120s24a.getLTVClass_s24a());//LTV分類
			}else{
				result.set("LTVClass_s24a_N", l120s24a.getLTVClass_s24a());//LTV分類
			}
		}
		
		// 格式化數字欄位
		if(Util.isNotEmpty(l120s24a.getCurrentApplyAmt_s24a())){
			result.set("currentApplyAmt_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getCurrentApplyAmt_s24a(), false, false));//B.現請額度
		}
		if(Util.isNotEmpty(l120s24a.getCcfAmt_s24a())){
			result.set("ccfAmt_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getCcfAmt_s24a(), false, false));//G.純表外之信用相當額
		}
		if(Util.isNotEmpty(l120s24a.getLTV_s24a())){
			result.set("LTV_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getLTV_s24a(), false, false));//M.LTV
		}
		if(Util.isNotEmpty(l120s24a.getOpponentRW_s24a())){
			result.set("opponentRW_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getOpponentRW_s24a(), false, false));//N.交易對手無擔保暴險風險權數
		}
		if(Util.isNotEmpty(l120s24a.getLTVRW_s24a())){
			result.set("LTVRW_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getLTVRW_s24a(), false, false));//N-1. LTV法風險權數   　
		}
		if(Util.isNotEmpty(l120s24a.getBeforeDeductRW_s24a())){
			result.set("beforeDeductRW_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getBeforeDeductRW_s24a(), false, false));//O.風險權數　
		}
		if(Util.isNotEmpty(l120s24a.getTotalCollAmt_s24a())){
			result.set("totalCollAmt_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getTotalCollAmt_s24a(), false, false));//合計-擔保品價值
		}
		if(Util.isNotEmpty(l120s24a.getTotalDisCollAmt_s24a())){
			result.set("totalDisCollAmt_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getTotalDisCollAmt_s24a(), false, false));//合計-合格擔保品抵減金
		}
		if(Util.isNotEmpty(l120s24a.getGutDeptRW_s24a())){
			result.set("gutDeptRW_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getGutDeptRW_s24a(), false, false));//U.保證機構風險權數
		}
		if(Util.isNotEmpty(l120s24a.getGuarRW_s24a())){
			result.set("guarRW_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getGuarRW_s24a(), false, false));//Y.連帶保證人風險權數
		}

		// 計算區塊
		// (16)抵減後風險權數  有值才幫他列這一區塊
		// 這樣做好像太麻煩，目前儲存的時候全刪掉了
		if(Util.isNotEmpty(l120s24a.getCalDeductRW_s24a())){
			result.set("calCurrentApplyAmt_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getCurrentApplyAmt_s24a(), false, false));// (1)現請額度
			
			if(Util.isEmpty(l120s24a.getCcf_s24a())){
				result.set("calCcf_s24a", "NA");// (2)CCF
			}else{
				result.set("calCcf_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getCcf_s24a(), false, true) );// (2)CCF
			}
			
			if(Util.isEmpty(l120s24a.getCcfAmt_s24a())){
				result.set("calCcfAmt_s24a", "NA");// (3)純表外之信用相當額 (1)*(2)
			}else{
				result.set("calCcfAmt_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getCcfAmt_s24a(), false, false));// (3)純表外之信用相當額 (1)*(2)
			}
			
			result.set("calBeforeDeductRW_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getBeforeDeductRW_s24a(), false, true));// (4)抵減前風險權數	
			
			if ("Y".equals(l120s24a.getHasQuaColl_s24a())) {
				result.set("calDisCollAmt_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getTotalDisCollAmt_s24a(), false, false));// (5)合格擔保品抵減金額
			}else{
				result.set("calDisCollAmt_s24a", "NA");// (5)合格擔保品抵減金額
			}
			
			result.set("calDisCollExposureAmt_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getCalDisCollExposureAmt_s24a(), false, false));// (6)合格擔保品抵減後暴險額 
			
			if(Util.isEmpty(l120s24a.getGutDeptRW_s24a())){
				result.set("calGutDeptRW_s24a", "NA");// (7)保證機構風險權數
			}else{
				result.set("calGutDeptRW_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getGutDeptRW_s24a(), false, true));// (7)保證機構風險權數
			}
			
			if(Util.isEmpty(l120s24a.getGutPercent_s24a())){
				result.set("calGutPercent_s24a", "NA");// (8)保證機構保證成數
			}else{
				result.set("calGutPercent_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getGutPercent_s24a(), false, true));// (8)保證機構保證成數	
			}
			
			if(Util.isEmpty(l120s24a.getCalHasGutPartAmt_s24a())){
				result.set("calHasGutPartAmt_s24a", "NA");// (9)信保部位
			}else{
				result.set("calHasGutPartAmt_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getCalHasGutPartAmt_s24a(), false, false));// (9)信保部位
			}
			
			if(Util.isEmpty(l120s24a.getCalHasGutDeptRWA_s24a())){
				result.set("calHasGutDeptRWA_s24a", "NA");// (10)信保部位風險性資產
			}else{
				result.set("calHasGutDeptRWA_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getCalHasGutDeptRWA_s24a(), false, false));// (10)信保部位風險性資產
			}
			
			if(Util.isEmpty(l120s24a.getGuarRW_s24a())){
				result.set("calGuarRW_s24a", "NA");// (11)連帶保證人風險權數	
			}else{
				result.set("calGuarRW_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getGuarRW_s24a(), false, true));// (11)連帶保證人風險權數	
			}
			
			result.set("calNoGutPartAmt_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getCalNoGutPartAmt_s24a(), false, false));// (12)非信保部位
			result.set("calNoGutPartRW_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getCalNoGutPartRW_s24a(), false, true));// (13)非信保部位風險權數
			result.set("calNoGutRWA_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getCalNoGutRWA_s24a(), false, false));// (14)非信保部位風險性資產
			result.set("calDeductRWA_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getCalDeductRWA_s24a(), false, false));// (15)抵減後風險性資產
			result.set("calDeductRW_s24a", LMSUtil.processL120s24aBigDecimal(l120s24a.getCalDeductRW_s24a(), false, true));// (16)抵減後風險權數
		}
		
		// 借款人類別 說明thickbox
		String l120s24a_centralGov = sysParameterService.getParamValue("L120S24A_CENTRALGOV");
		result.set("l120s24a_centralGov", Util.trim(l120s24a_centralGov));// 行業別建檔: 016411、026411、030100、018311者；以及下列名單。
		String l120s24a_lovalGov = sysParameterService.getParamValue("L120S24A_LOVALGOV");
		result.set("l120s24a_lovalGov", Util.trim(l120s24a_lovalGov));// 行業別建檔:030200者。
		
		// 是否要出現連帶保證人風險權數旁邊的計算按鈕
		String l120s24a_show_guarrw_cal = sysParameterService.getParamValue("L120S24A_SHOW_GUARRW_CAL");
		result.set("l120s24a_show_guarrw_cal", Util.trim(l120s24a_show_guarrw_cal));
		
		// 設定哪些分行可以看到風控處才看得到的計算區塊
		String lms_rw_show_special_branch_div = sysParameterService.getParamValue("LMS_RW_SHOW_SPECIAL_BRANCH_DIV");
		List<String> specialBranch = new ArrayList<String>();
		if(Util.isNotEmpty(lms_rw_show_special_branch_div)){
			specialBranch = Arrays.asList(lms_rw_show_special_branch_div.split(","));
		}
		result.set("lms_rw_show_special_branch_div", specialBranch);
		
		// 20250101版本要多撈的
		if(UtilConstants.L120s24aVersion.Ver_20250101.equals(versionDate)){
			// 特殊融資類別，要轉成中文
			Map<String, String> finRiskTypeCodeMap = codeTypeService.findByCodeType("specialFinRiskType_s24a");
			String specialFinRiskType_s24a = Util.trim(l120s24a.getSpecialFinRiskType_s24a());
			if(Util.isNotEmpty(specialFinRiskType_s24a)){
				result.set("specialFinRiskType_s24a", finRiskTypeCodeMap.get(specialFinRiskType_s24a));
			}else{
				result.set("specialFinRiskType_s24a", "");
			}
			
			// 借款人類別的說明字眼
			// 說明thickbox的最上方補充
			String l120s24a_bowrrowerClassTop = lmsService.getSysParamDataValue("L120S24A_BOWRROWERCLASSTOP");
			result.set("l120s24a_bowrrowerClassTop", l120s24a_bowrrowerClassTop);// 一長串說明1
			// 國立大學及市立中小學
			String l120s24a_nationalSchool = lmsService.getSysParamDataValue("L120S24A_NATIONALSCHOOL");
			result.set("l120s24a_nationalSchool", Util.trim(l120s24a_nationalSchool));// 行業別建為028520、028530、028540及028550者
			// 說明thickbox的最下方補充
			String l120s24a_bowrrowerClassBottom = lmsService.getSysParamDataValue("L120S24A_BOWRROWERCLASSBOTTOM");
			result.set("l120s24a_bowrrowerClassBottom", l120s24a_bowrrowerClassBottom);// 一長串說明2
		}
		
		return result;
	}
	
	/**
	 * J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯
	 * 借款人外部評等(簽報書引入) ->重新引進按鈕
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult reImportCrdGrade_s24a(PageParameters params)	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties popPanel = MessageBundleScriptCreator.getComponentResource(LMS1401S10Panel.class);
		
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		
		// 重新計算分數
		// 借款人的話會從L120S24A抓，連保人會需要custId, dupNo
		String crdText_s24a = lmsService.reImportCrdGrade_s24a(mainId, oid, "", "", "1", popPanel);
		
		// 重新顯示外部評等於畫面上
		result.set("crdText_s24a", crdText_s24a);// 借款人外部評等(簽報書引入)
		return result;
	}
	/**
	 * J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯
	 * 儲存風險權數主檔
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120s24a(PageParameters params) throws CapException {
		// Properties pop = MessageBundleScriptCreator.getComponentResource(LMSS07APage06.class);
		CapAjaxFormResult formResult = new CapAjaxFormResult();
		Properties pop = MessageBundleScriptCreator.getComponentResource(LMS1401S10PageV20220812.class);
		
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120S24A l120s24a = lmsService.findL120s24aByOid(oid);
		String versionDate = l120s24a.getVersionDate_s24a();// 版本日期
		
		// 先做欄位驗證-只檢查匯率欄位有值就好，不然後面折算TWD都會無法計算
		String l120S24ADetail = Util.trim(params.getString("l120S24ADetailForm"));
		JSONObject jsonL120s24a = JSONObject.fromObject(l120S24ADetail);
		String rateLoanToLoc_s24a = jsonL120s24a.get("rateLoanToLoc_s24a").toString();
		
		String message1 = pop.getProperty("L120S24A.nonEnterForSave.messag1","尚未輸入{0}欄位，請輸入後再點選儲存");
		if(Util.isEmpty(rateLoanToLoc_s24a)){
			throw new CapMessageException(MessageFormat.format(
					message1, pop.getProperty("L120S24A.rateLoanToLoc_s24a","匯率")),
					getClass());
		}
		
		Properties popPanel = MessageBundleScriptCreator.getComponentResource(LMS1401S10Panel.class);
		if (Util.notEquals("", l120S24ADetail)) {
			lmsService.saveL120s24a(oid, mainId, l120S24ADetail, popPanel);
		}

		// J-113-0499 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯
		formResult.set("hint1", "");// 儲存後看想要提示什麼訊息
		// 風險權數2025版是否啟用
		String L120S24A_20250101_ON = lmsService.isL120s24a2025On();
		if("Y".equals(L120S24A_20250101_ON)){
			// 2025年的版本啟用後，才會需要出這個hint1
			if(UtilConstants.L120s24aVersion.Ver_20220812.equals(versionDate)){
				formResult.set("hint1", "目前資料為舊版風險權數，如需使用2025年新版風險權數，請先執行「引進額度明細表」");
			}
		}
		
		return formResult;
	}
	
	/**
	 * J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯 
	 * 刪除風險權數主檔
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteL120s24as(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		
		lmsService.deleteL120s24a(oids);
		// 印出刪除成功訊息
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0019"));
		return result;
	}
	
	/**
	 * J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯 
	 * 寫回額度明細表
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult writeBackToL140m01a(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String[] oids = params.getStringArray("oids");
		
		lmsService.writeBackToL140m01a(mainId, oids);
		// 印出寫回成功訊息
		Properties pop = MessageBundleScriptCreator.getComponentResource(LMS1401S10Panel.class);
		String wirteSuccess = pop.getProperty("	button.L120S24A.writeSuccess","寫回成功");
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE,wirteSuccess);
		return result;
	}
	
	/**
	 * J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯 
	 * 撈最新的利率
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult importRateLoanToLoc_s24a(PageParameters params) throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = Util.trim(params.getString(EloanConstants.OID));

		Map<String, String> calMap = lmsService.importRateLoanToLoc_s24a(oid);
		result.putAll(calMap);
		
		return result;
	}
	
	/**
	 * J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯 
	 * 計算O.風險權數
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult calBeforeDeductRW_s24a(PageParameters params) throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties pop = MessageBundleScriptCreator.getComponentResource(LMS1401S10PageV20220812.class);
		
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String bowrrowerClass_s24a = Util.trim(params.getString("bowrrowerClass_s24a"));
		String hasEstate_s24a = Util.trim(params.getString("hasEstate_s24a"));
		String isCBControl_s24a = Util.trim(params.getString("isCBControl_s24a"));
		String LTVClass_s24a_Y = Util.trim(params.getString("LTVClass_s24a_Y"));
		String LTVClass_s24a_N = Util.trim(params.getString("LTVClass_s24a_N"));
		String LTVType_s24a = Util.trim(params.getString("LTVType_s24a"));
		String estateIsLand_s24a = Util.trim(params.getString("estateIsLand_s24a"));
		String isFarmWood_s24a = Util.trim(params.getString("isFarmWood_s24a"));
		String LTV_s24a = Util.trim(params.getString("LTV_s24a"));
		
		// 先做欄位驗證
		
		String message1 = pop.getProperty("L120S24A.calLTVRW.messag1","尚未輸入{0}欄位，請輸入後再點選計算");
		if(Util.isEmpty(bowrrowerClass_s24a)){
			throw new CapMessageException(MessageFormat.format(
					message1, pop.getProperty("L120S24A.bowrrowerClass_s24a","借款人類別")),
					getClass());
		}else if(Util.isEmpty(hasEstate_s24a)){
			throw new CapMessageException(MessageFormat.format(
					message1, pop.getProperty("L120S24A.hasEstate_s24a","本額度有徵提不動產擔保品")),
					getClass());
		}
		
		// 有徵提不動產擔保品，才需要下面這些欄位
		if("Y".equals(hasEstate_s24a)){
			if(Util.isEmpty(isCBControl_s24a)){
				throw new CapMessageException(MessageFormat.format(
						message1, pop.getProperty("L120S24A.isCBControl_s24a","央行管制暴險")),
						getClass());
			}else{
				if("Y".equals(isCBControl_s24a)){
					if(Util.isEmpty(LTVClass_s24a_Y)){
						throw new CapMessageException(MessageFormat.format(
								message1, pop.getProperty("L120S24A.isCBControl_s24a_message","央行管制暴險  分類")),
								getClass());
					}
				}else{
					String column = "";
					if(Util.isEmpty(LTVClass_s24a_N)){
						column = pop.getProperty("L120S24A.LTVClass_s24a","分類");
					}
					
					// LTVClass_s24a_N = 1.住宅、2.商用才需填寫
					// LTVClass_s24a_N 為3、4 時不用檢核LTVType_s24a
					if("1".equals(LTVClass_s24a_N) || "2".equals(LTVClass_s24a_N)){
						if(Util.isEmpty(LTVType_s24a)){
							column = (column.length() > 0 ? column + "、" : column) +
							pop.getProperty("L120S24A.LTVType_s24a","類別");
						}
						
						if(Util.isEmpty(estateIsLand_s24a)){
							column = (column.length() > 0 ? column + "、" : column) +
							pop.getProperty("L120S24A.estateIsLand_s24a","本額度之不動產擔保品為土地	");
						}
						// 土地Y, 農林Y->合格，要輸LTV
						// 土地Y, 農林N->非合格，不用輸LTV
						// 土地N->合格，要輸LTV
						
						// 本額度之不動產擔保品為土地
						if ("Y".equals(estateIsLand_s24a)) {

							// 該筆不動產為農地、林地
							if(Util.isEmpty(isFarmWood_s24a)){
								column = (column.length() > 0 ? column + "、" : column) +
								pop.getProperty("L120S24A.isFarmWood_s24a","該筆不動產為農地、林地");
							}
							// 該筆不動產為農地、林地 為是時才需要填寫LTV
							if("Y".equals(isFarmWood_s24a)){
								if(Util.isEmpty(LTV_s24a)){
									column = (column.length() > 0 ? column + "、" : column) +
									pop.getProperty("L120S24A.LTV_s24a","LTV ");
								}
								
								if(!"2".equals(LTVClass_s24a_N)){
									// 2022.10.19 芝榮表示農地、林地屬合格商用不動產暴險
									// 若他不是選商用，計算擋掉直接噴提示
									throw new CapMessageException(pop.getProperty("L120S24A.calLTVRW.messag2",
											"農地、林地屬合格商用不動產暴險，請將分類改為商用後再點選計算"), getClass());
								}
							}

						}else{
							// 土地為N時，不用輸入是否為農地、林地，但要填寫LTV
							if(Util.isEmpty(LTV_s24a)){
								column = (column.length() > 0 ? column + "、" : column) +
								pop.getProperty("L120S24A.LTV_s24a","LTV ");
							}
						}
					}
					
					
					if(Util.isNotEmpty(column)){
						throw new CapMessageException(MessageFormat.format(
								message1, column), getClass());
					}
				}
			}
		}
		
		Properties popPanel = MessageBundleScriptCreator.getComponentResource(LMS1401S10Panel.class);
		Map<String, String> importMap = lmsService.calBeforeDeductRW_s24a(oid, bowrrowerClass_s24a,
				hasEstate_s24a, isCBControl_s24a, LTVClass_s24a_Y, LTVClass_s24a_N, LTVType_s24a, estateIsLand_s24a, 
				isFarmWood_s24a, LTV_s24a, popPanel);
		result.putAll(importMap);
		
		return result;
	}
	
	/**
	 * J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯
	 * 撈風險權數合格擔保品資料檔
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s24b(PageParameters params)	throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		L120S24B l120s24b = lmsService.findL120s24bByOid(oid);
		// 列出的是不要放進result的欄位值
		result = DataParse.toResult(l120s24b, DataParse.Delete, new String[] {
				EloanConstants.MAIN_ID, "creator", "createTime",
				"updater", "updateTime"});
		
		Properties pop = MessageBundleScriptCreator.getComponentResource(LMS1401S10PageV20220812.class);
		result.set("currSym_s24b", "Y".equals(l120s24b.getCurrSym_s24b()) ? 
				pop.getProperty("L120S24A.yes","是") : pop.getProperty("L120S24A.no","否"));
		return result;
	}
	
	/**
	 * J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯
	 * 儲存風險權數合格擔保品資料檔
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120s24b(PageParameters params) throws CapException {
		// Properties pop = MessageBundleScriptCreator.getComponentResource(LMSS07APage06.class);
		CapAjaxFormResult formResult = new CapAjaxFormResult();
		Properties pop = MessageBundleScriptCreator.getComponentResource(LMS1401S10PageV20220812.class);
		
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String refOid_s24b = Util.trim(params.getString("refOid_s24b"));

		String l120S24BDetail = Util.trim(params.getString("l120S24BDetailForm"));
		JSONObject jsonL120s24b = JSONObject.fromObject(l120S24BDetail);
		String quaColl_s24b = Util.trim(jsonL120s24b.optString("quaColl_s24b", ""));
		String collAmt_s24b = Util.trim(NumConverter.delCommaString(jsonL120s24b.optString("collAmt_s24b", "")));
		String collCurr_s24b = Util.trim(jsonL120s24b.optString("collCurr_s24b", ""));
		
		
		// 畫面總共就六個欄位，一定都要有值才可以儲存
		String message1 = pop.getProperty("L120S24B.save.messag1","尚未輸入{0}欄位，請輸入後再點選儲存");
		String column = "";
		if(Util.isEmpty(quaColl_s24b)){
			// L120S24B.quaColl_s24b=合格金融擔保品
			column = pop.getProperty("L120S24B.quaColl_s24b","合格金融擔保品");
		}
		if(Util.isEmpty(collCurr_s24b)){
			// L120S24B.collCurr_s24b=擔保品幣別	
			column = (column.length() > 0 ? column + "、" : column) +
			pop.getProperty("L120S24B.collCurr_s24b","擔保品幣別	");
		}
		if(Util.isEmpty(collAmt_s24b)){
			// L120S24B.collAmt_s24b=擔保品價值
			column = (column.length() > 0 ? column + "、" : column) +
			pop.getProperty("L120S24B.collAmt_s24b","擔保品價值");
		}
		
		if(Util.isNotEmpty(column)){
			throw new CapMessageException(MessageFormat.format(
					message1, column), getClass());
		}
		
		Map<String, String> totMap = lmsService.saveL120s24b(oid, mainId, refOid_s24b, quaColl_s24b, collCurr_s24b, collAmt_s24b, pop);
		totMap.put("currSym_s24b", "Y".equals(totMap.get("currSym_s24b")) ? 
				pop.getProperty("L120S24A.yes","是") : pop.getProperty("L120S24A.no","否"));
		formResult.putAll(totMap);
		return formResult;
	}
	
	/**
	 * J-113-0499 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯
	 * 儲存風險權數合格擔保品資料檔
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120s24b_2025(PageParameters params) throws CapException {
		// Properties pop = MessageBundleScriptCreator.getComponentResource(LMSS07APage06.class);
		CapAjaxFormResult formResult = new CapAjaxFormResult();
		Properties pop = MessageBundleScriptCreator.getComponentResource(LMS1401S10PageV20250101.class);
		
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String refOid_s24b = Util.trim(params.getString("refOid_s24b"));

		String l120S24BDetail = Util.trim(params.getString("l120S24BDetailForm"));
		JSONObject jsonL120s24b = JSONObject.fromObject(l120S24BDetail);
		String quaColl_s24b = Util.trim(jsonL120s24b.optString("quaColl_s24b", ""));
		// 2025新增的，擔保品幣別
		String collCrdGrade_s24b = Util.trim(jsonL120s24b.optString("collCrdGrade_s24b", ""));
		String collAmt_s24b = Util.trim(NumConverter.delCommaString(jsonL120s24b.optString("collAmt_s24b", "")));
		String collCurr_s24b = Util.trim(jsonL120s24b.optString("collCurr_s24b", ""));
		
		
		// 畫面中可以輸入的就四個欄位，一定都要有值才可以儲存
		String message1 = pop.getProperty("L120S24B.save.messag1","尚未輸入{0}欄位，請輸入後再點選儲存");
		String column = "";
		if(Util.isEmpty(quaColl_s24b)){
			// L120S24B.quaColl_s24b=合格金融擔保品
			column = pop.getProperty("L120S24B.quaColl_s24b","合格金融擔保品");
		}
		// 2025版本新增的
		// 合格金融擔保品為國庫券、公債、金融債券、公司債券才須要選
		// 3:國庫券、4:公債、5:金融債券、6:公司債券
		if ("3".equals(quaColl_s24b) || "4".equals(quaColl_s24b)
				|| "5".equals(quaColl_s24b) || "6".equals(quaColl_s24b)) {
			if(Util.isEmpty(collCrdGrade_s24b)){
				// L120S24B.collCrdGrade_s24b=擔保品評等
				column = pop.getProperty("L120S24B.collCrdGrade_s24b","擔保品評等");
			}
		}
		if(Util.isEmpty(collCurr_s24b)){
			// L120S24B.collCurr_s24b=擔保品幣別	
			column = (column.length() > 0 ? column + "、" : column) +
			pop.getProperty("L120S24B.collCurr_s24b","擔保品幣別	");
		}
		if(Util.isEmpty(collAmt_s24b)){
			// L120S24B.collAmt_s24b=擔保品價值
			column = (column.length() > 0 ? column + "、" : column) +
			pop.getProperty("L120S24B.collAmt_s24b","擔保品價值");
		}
		
		if(Util.isNotEmpty(column)){
			throw new CapMessageException(MessageFormat.format(
					message1, column), getClass());
		}
		
		Map<String, String> totMap = lmsService.saveL120s24b_2025(oid, mainId, refOid_s24b, quaColl_s24b, collCrdGrade_s24b,
				collCurr_s24b, collAmt_s24b, pop);
		totMap.put("currSym_s24b", "Y".equals(totMap.get("currSym_s24b")) ? 
				pop.getProperty("L120S24A.yes","是") : pop.getProperty("L120S24A.no","否"));
		formResult.putAll(totMap);
		return formResult;
	}
	
	/**
	 * J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯
	 * 刪除風險權數合格擔保品資料檔
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteL120s24bs(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String[] oids = params.getStringArray("oids");
		String refOid_s24b = Util.trim(params.getString("refOid_s24b"));
		
		Map<String, String> totMap = lmsService.deleteL120s24b(mainId, refOid_s24b, oids);
		result.putAll(totMap);
		
		// 印出刪除成功訊息
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0019"));
		return result;
	}
	
	/**
	 * J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯
	 * 計算U.保證機構風險權數
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult calGutDeptRW_s24a(PageParameters params)	throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties pop = MessageBundleScriptCreator.getComponentResource(LMS1401S10PageV20220812.class);
		
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String gutClass_s24a = Util.trim(params.getString("gutClass_s24a"));
		String gutDeptCountry_s24a = Util.trim(params.getString("gutDeptCountry_s24a"));
		String gutDeptID_s24a = Util.trim(params.getString("gutDeptID_s24a"));

		// 先做欄位驗證
		String message1 = pop.getProperty("L120S24A.calLTVRW.messag1","尚未輸入{0}欄位，請輸入後再點選計算");
		if(Util.isEmpty(gutClass_s24a)){
			throw new CapMessageException(MessageFormat.format(
					message1, pop.getProperty("L120S24A.gutClass_s24a","保證類型")),
					getClass());
		}
		// 選5,6則一定要輸入保證機構國別
		if("5".equals(gutClass_s24a) || "6".equals(gutClass_s24a)){
			if(Util.isEmpty(gutDeptCountry_s24a)){
				throw new CapMessageException(MessageFormat.format(
						message1, pop.getProperty("L120S24A.gutDeptCountry_s24a","保證機構國別")),
						getClass());
			}
		}else if("7".equals(gutClass_s24a)){
			// 選7則一定要輸入保證銀行代碼
			if(Util.isEmpty(gutDeptID_s24a)){
				throw new CapMessageException(MessageFormat.format(
						message1, pop.getProperty("L120S24A.gutDeptID_s24a_message","保證銀行代碼")),
						getClass());
			}
		}
		
		// 欄位該填的都有填，才去call service計算
		Properties popPanel = MessageBundleScriptCreator.getComponentResource(LMS1401S10Panel.class);
		Map<String, Object> gutDeptMap = lmsService.calGutDeptRW_s24a(oid, gutClass_s24a, gutDeptCountry_s24a, gutDeptID_s24a, 
				popPanel);
		
		// 選7則才會有保證銀行名稱
		String gutDeptName = (String) gutDeptMap.get("gutDeptName");
		BigDecimal gutDeptRW = (BigDecimal) gutDeptMap.get("gutDeptRW");
		
		result.set("gutDeptName", gutDeptName);
		result.set("gutDeptRW", gutDeptRW.toPlainString());
		
		return result;
	}
	
	/**
	 * J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯
	 * 連帶保證人名稱-引入
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult findGuar_s24a(PageParameters params)	throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties pop = MessageBundleScriptCreator.getComponentResource(LMS1401S10PageV20220812.class);
		
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String guarId_s24a = Util.trim(params.getString("guarId_s24a"));
		//String guarDupNo_s24a = Util.trim(params.getString("guarDupNo_s24a"));
		
		// 先做欄位驗證
		String message1 = pop.getProperty("L120S24A.nonEnterForImport.messag1","尚未輸入{0}欄位，請輸入後再點選引入");
		if(Util.isEmpty(guarId_s24a)){
			throw new CapMessageException(MessageFormat.format(
					message1, pop.getProperty("L120S24A.guarId_s24a","連帶保證保證人ID")),
					getClass());
		}
		
		// 目前不檢查重複序號，因為只能輸入法人戶
		// if(Util.isEmpty(guarDupNo_s24a)){
		//	throw new CapMessageException(MessageFormat.format(
		//			message1, pop.getProperty("guarDupNo_s24a","連帶保證保證人重複序號")),
		//			getClass());
		// }
		
		// 欄位該填的都有填，才去處理信評資料、發查姓名、儲存資料
		// 抄LMSM01CFormHandler.queryL120s01c
		L120S24A l120s24a = lmsService.findL120s24aByOid(oid);
		// 先找到上一次的連保人資料
		if(Util.isNotEmpty(l120s24a.getGuarId_s24a())){
			L120S01A preL120s01a = lms1201Service.findL120s01aByUniqueKey(l120s24a.getMainId(), l120s24a.getGuarId_s24a(), "0");
			if(preL120s01a == null){
				// 上一次的連保人不在借款人中，要 先刪掉上次的信評資料，避免太多髒資料
				// 因為這些信評資料是這個功能建出來的，不會影響到借款人，所以可以盡情刪除、新增
				List<L120S01C> delList = lms1201Service.findL120s01cByCustId(l120s24a.getMainId(), l120s24a.getGuarId_s24a(), "0");
				lms1201Service.deleteListL120s01c(delList);
			}
		}
		
		// 這次的連保人資料
		L120S01A nowL120s01a = lms1201Service.findL120s01aByUniqueKey(l120s24a.getMainId(), guarId_s24a, "0");
		// 連保人不在借款人中，去call借款人引信評的功能寫進L120S01S
		// 連保人在借款人中，就是看借款人明細的資料，不做任何自動引的動作
		if(nowL120s01a == null){
			// 先刪掉上次的信評資料
			// 因為這些信評資料是這個功能建出來的，不會影響到借款人，所以可以盡情刪除、新增
			List<L120S01C> delList = lms1201Service.findL120s01cByCustId(l120s24a.getMainId(), guarId_s24a, "0");
			// 只撈frag=3 外部信評
			lms1201Service.findL120s01c(l120s24a.getMainId(),
					guarId_s24a, "0", "", delList, 3);
		}
		
		Properties popPanel = MessageBundleScriptCreator.getComponentResource(LMS1401S10Panel.class);
		Map<String, String> guarMap = lmsService.findGuar_s24a(oid, guarId_s24a, popPanel);
		result.putAll(guarMap);
		
		return result;
	}
	
	/**
	 * J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯
	 * 純計算連帶保證人風險權數+連帶保證人的外部信評文字
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult calGuarRW_s24a(PageParameters params) throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String oid = Util.trim(params.getString(EloanConstants.OID));
		
		Properties popPanel = MessageBundleScriptCreator.getComponentResource(LMS1401S10Panel.class);
		Map<String, String> guarMap = lmsService.calGuarRW_s24a(oid, popPanel);
		result.putAll(guarMap);
		
		return result;
	}
	
	/**
	 * J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯
	 * 計算抵減後風險權數
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult calDeductRW_s24a(PageParameters params) throws CapException {
		Properties popPanel = MessageBundleScriptCreator.getComponentResource(LMS1401S10Panel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		String l120S24ADetail = Util.trim(params.getString("l120S24ADetailForm"));
		
		Properties pop = MessageBundleScriptCreator.getComponentResource(LMS1401S10PageV20220812.class);
		
		// 先做欄位驗證
		JSONObject jsonL120s24a = JSONObject.fromObject(l120S24ADetail);
		
		String nonEnter = pop.getProperty("L120S24A.calLTVRW.messag1","尚未輸入{0}欄位，請輸入後再點選計算");
		String nonCal = pop.getProperty("L120S24A.nonCal.messag1","尚未計算{0}欄位，請先執行{0}計算");
		String nonImport = pop.getProperty("L120S24A.nonImport.messag1","尚未引入{0}欄位，請先執行{0}引入");
		// 要湊出計算區塊的這些欄位，才可以正常計算出完整的計算區外
		// 只抓畫面上需要的欄位就好，不要再重新去發查DW那些喔!!!!
		// (1)現請額度
		String currentApplyAmt_s24a = Util.trim(jsonL120s24a.get("currentApplyAmt_s24a"));// 現請額度
		BigDecimal currentApplyAmt_s24a_big = BigDecimal.ZERO;
		if(Util.isEmpty(currentApplyAmt_s24a)){
			throw new CapMessageException(MessageFormat.format(
					nonEnter, pop.getProperty("L120S24A.currentApplyAmt_s24a","現請額度")),
					getClass());
		}
		try {
			currentApplyAmt_s24a_big = new BigDecimal(currentApplyAmt_s24a);
		} catch (NumberFormatException nfe) {
			throw new CapMessageException( pop.getProperty("L120S24A.currentApplyAmt_s24a","現請額度") +
					pop.getProperty("L120S24B.save.number.messag1", "數字欄位輸入有誤"), getClass());
		}
		
		// 判斷現請額度不可填0，在計算 (16)抵減後風險權數 =(15)/(1)
		// 因除數為0會噴java.lang.ArithmeticException: Division is undefined
		if(currentApplyAmt_s24a_big.compareTo(BigDecimal.ZERO) == 0){
			throw new CapMessageException( popPanel.getProperty("L120S24A.message.L120S24A.currentApplyAmtCanNotZero",
			"現請額度-金額不可為0，請修改金額後再點選計算"), getClass());
		}
		
		// (2)CCF
		String onlyGuar_s24a = Util.trim(jsonL120s24a.get("onlyGuar_s24a"));// 本額度僅有保證科目
		if(Util.isEmpty(onlyGuar_s24a)){
			throw new CapMessageException(MessageFormat.format(
					nonEnter, pop.getProperty("L120S24A.onlyGuar_s24a","本額度僅有保證科目")),
					getClass());
		}
		String ccf_s24a = Util.trim(jsonL120s24a.get("ccf_s24a"));// 表外項目信用轉換係數(CCF)
		BigDecimal ccf_s24a_big = BigDecimal.ZERO;
		// 僅有保證科目有選的話，就一定要選ccf
		if("Y".equals(onlyGuar_s24a)){
			if(Util.isEmpty(ccf_s24a)){
				throw new CapMessageException(MessageFormat.format(
						nonEnter, pop.getProperty("L120S24A.ccf_s24a","表外項目信用轉換係數(CCF)")),
						getClass());
			}
			
			try {
				ccf_s24a_big = new BigDecimal(ccf_s24a);
			} catch (NumberFormatException nfe) {
				throw new CapMessageException( pop.getProperty("L120S24A.ccf_s24a","表外項目信用轉換係數(CCF)") +
						pop.getProperty("L120S24B.save.number.messag1", "數字欄位輸入有誤"), getClass());
			}
		}
		
		// (4)抵減前風險權數
		String beforeDeductRW_s24a = Util.trim(jsonL120s24a.get("beforeDeductRW_s24a"));// 本額度抵減前風險權數	
		BigDecimal beforeDeductRW_s24a_big = BigDecimal.ZERO;
		if(Util.isEmpty(beforeDeductRW_s24a)){
			throw new CapMessageException(MessageFormat.format(
					nonCal, pop.getProperty("L120S24A.beforeDeductRW_s24a","本額度抵減前風險權數")),
					getClass());
		}
		try {
			beforeDeductRW_s24a_big = new BigDecimal(beforeDeductRW_s24a);
		} catch (NumberFormatException nfe) {
			throw new CapMessageException( pop.getProperty("L120S24A.beforeDeductRW_s24a","本額度抵減前風險權數") +
					pop.getProperty("L120S24B.save.number.messag1", "數字欄位輸入有誤"), getClass());
		}

		// (5)合格擔保品抵減金額	
		// 從L120S24A取即可，但要確認他P.本額度是否有合格金融擔保品是否有選好
		String hasQuaColl_s24a = Util.trim(jsonL120s24a.get("hasQuaColl_s24a"));// 本額度是否有合格金融擔保品
		if(Util.isEmpty(hasQuaColl_s24a)){
			throw new CapMessageException(MessageFormat.format(
					nonEnter, pop.getProperty("L120S24A.hasQuaColl_s24a","本額度是否有合格金融擔保品")),
					getClass());
		}
		
		// (7)保證機構風險權數
		// (8)保證機構保證成數
		// 本額度有徵提不動產擔保品 為"Y"，且央行管制暴險 為"N"，才會有保證機構和保證人的部分
		String hasEstate_s24a = Util.trim(jsonL120s24a.get("hasEstate_s24a"));// 本額度有徵提不動產擔保品
		if(Util.isEmpty(hasEstate_s24a)){
			throw new CapMessageException(MessageFormat.format(
					nonEnter, pop.getProperty("L120S24A.hasEstate_s24a","本額度有徵提不動產擔保品")),
					getClass());
		}
		String isCBControl_s24a = Util.trim(jsonL120s24a.get("isCBControl_s24a"));// 央行管制暴險
		if("Y".equals(hasEstate_s24a)){
			if(Util.isEmpty(isCBControl_s24a)){
				throw new CapMessageException(MessageFormat.format(
						nonEnter, pop.getProperty("L120S24A.isCBControl_s24a","央行管制暴險")),
						getClass());
			}
		}

		String hasGutClass_s24a = Util.trim(jsonL120s24a.get("hasGutClass_s24a"));// 是否有下列類型保證
		String gutClass_s24a = Util.trim(jsonL120s24a.get("gutClass_s24a"));// 保證類型
		String gutClassOtherDesc_s24a = Util.trim(jsonL120s24a.get("gutClassOtherDesc_s24a"));// 保證類別_其他說明 (必填欄位)
		String gutPercent_s24a = Util.trim(jsonL120s24a.get("gutPercent_s24a"));// 保證成數
		BigDecimal gutPercent_s24a_big = BigDecimal.ZERO;
		String gutDeptRW_s24a = Util.trim(jsonL120s24a.get("gutDeptRW_s24a"));// 保證機構風險權數
		BigDecimal gutDeptRW_s24a_big = BigDecimal.ZERO;
		if(("Y".equals(hasEstate_s24a) && "N".equals(isCBControl_s24a)) ||
				"N".equals(hasEstate_s24a)){
			if(Util.isEmpty(hasGutClass_s24a)){
				throw new CapMessageException(MessageFormat.format(
						nonEnter, pop.getProperty("L120S24A.hasGutClass_s24a_message","是否有下列類型保證")),
						getClass());
			}
			
			// 如果保證類型為8，一定要填寫保證類別_其他說明
			if ("8".equals(gutClass_s24a) && Util.isEmpty(gutClassOtherDesc_s24a)) {
				throw new CapMessageException(popPanel.getProperty(
						"L120S24A.message.L120S24A.GutClassOtherDesc.empty2",
						"保證類型為其他，請填寫保證類別_其他說明後再點選計算"), getClass());
			}
			
			if("Y".equals(hasGutClass_s24a)){
				if(Util.isEmpty(gutPercent_s24a)){
					throw new CapMessageException(MessageFormat.format(
							nonEnter, pop.getProperty("L120S24A.gutPercent_s24a","保證成數")),
							getClass());
				}
				try {
					gutPercent_s24a_big = new BigDecimal(gutPercent_s24a);
				} catch (NumberFormatException nfe) {
					throw new CapMessageException( pop.getProperty("L120S24A.gutPercent_s24a","保證成數") +
							pop.getProperty("L120S24B.save.number.messag1", "數字欄位輸入有誤"), getClass());
				}
				
				BigDecimal big100 = new BigDecimal("100");
				String numberRangeError = popPanel.getProperty(
						"L120S24A.message.numberTooBig",
						"{0}欄位只可輸入{1}~{2}");
				if(gutPercent_s24a_big.compareTo(big100) > 0 || gutPercent_s24a_big.compareTo(BigDecimal.ZERO) < 0){
					throw new CapMessageException(MessageFormat.format(
							numberRangeError, popPanel.getProperty(
									"L120S24A.gutPercent_s24a","保證成數")
									, "0", "100"), getClass());
				}
			}
			
			
			if("Y".equals(hasGutClass_s24a)){
				if(Util.isEmpty(gutDeptRW_s24a)){
					throw new CapMessageException(MessageFormat.format(
							nonCal, pop.getProperty("L120S24A.gutDeptRW_s24a","保證機構風險權數")),
							getClass());
				}
				try {
					gutDeptRW_s24a_big = new BigDecimal(gutDeptRW_s24a);
				} catch (NumberFormatException nfe) {
					throw new CapMessageException( pop.getProperty("L120S24A.gutDeptRW_s24a","保證機構風險權數") +
							pop.getProperty("L120S24B.save.number.messag1", "數字欄位輸入有誤"), getClass());
				}
				
				
				
				String numberRangeError = popPanel.getProperty(
						"L120S24A.message.numberNeedMore",
						"{0}需>={1}%");
				if(gutDeptRW_s24a_big.compareTo(BigDecimal.ZERO) < 0){
					throw new CapMessageException(MessageFormat.format(
							numberRangeError, pop.getProperty("L120S24A.gutDeptRW_s24a","保證機構風險權數")
									, "0"), getClass());
				}
				
				if(gutDeptRW_s24a_big.compareTo(beforeDeductRW_s24a_big) > 0){
					throw new CapMessageException( pop.getProperty("L120S24A.gutDeptRW.tooBig.messag1",
							"因保證機構之風險權數>抵減前風險權數，無法進行風險權數替換，『是否有下列類型保證』請改選\"否\"。"), getClass());
				}
			}
		}
		
		
		// (11)連帶保證人風險權數
		String extractBetterGuar_s24a = Util.trim(jsonL120s24a.get("extractBetterGuar_s24a"));// 有徵提評等較佳之連帶保證人
		String guarRW_s24a = Util.trim(jsonL120s24a.get("guarRW_s24a"));// 連帶保證人風險權數
		BigDecimal guarRW_s24a_big = BigDecimal.ZERO;
		if(("Y".equals(hasEstate_s24a) && "N".equals(isCBControl_s24a)) ||
				"N".equals(hasEstate_s24a)){
			if(Util.isEmpty(extractBetterGuar_s24a)){
				throw new CapMessageException(MessageFormat.format(
						nonEnter, pop.getProperty("L120S24A.extractBetterGuar_s24a","有徵提評等較佳之保證人/連帶保證人")),
						getClass());
			}
			if("Y".equals(extractBetterGuar_s24a)){
				if( Util.isEmpty(guarRW_s24a)){
					throw new CapMessageException(MessageFormat.format(
							nonImport, pop.getProperty("120S24A.guarRW_s24a","保證人/連帶保證人風險權數")),
							getClass());
				}
				try {
					guarRW_s24a_big = new BigDecimal(guarRW_s24a);
				} catch (NumberFormatException nfe) {
					throw new CapMessageException( pop.getProperty("L120S24A.gutPercent_s24a","保證人/連帶保證人風險權數") +
							pop.getProperty("L120S24B.save.number.messag1", "數字欄位輸入有誤"), getClass());
				}
			}
		}
		
		// 這些欄位都有輸入才可以進行計算
		Properties popPage = MessageBundleScriptCreator.getComponentResource(LMS1401S10PageV20220812.class);
		Map<String, String> calResultMap = lmsService.calDeductRW_s24a(oid, mainId, currentApplyAmt_s24a_big, onlyGuar_s24a, ccf_s24a_big, beforeDeductRW_s24a_big, hasQuaColl_s24a, 
				hasGutClass_s24a, gutPercent_s24a_big, gutDeptRW_s24a_big, extractBetterGuar_s24a, guarRW_s24a_big, popPage, gutClass_s24a);
		result.putAll(calResultMap);
		
		
		// 印出計算成功訊息
		String message = popPanel.getProperty("L120S24A.cal.success", "計算成功");
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, message);
		return result;
	}
	
	 @DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	    public IResult showPanelLms140s10(PageParameters params) throws CapException {
	        CapAjaxFormResult result = new CapAjaxFormResult();
	        String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
	        L120M01A l120m01a = lmsService.findModelByMainId(L120M01A.class, mainId);
	        if(l120m01a == null){
	            l120m01a = new L120M01A();
	        }
	        result.set("showPanelLms140s10", lmsService.showPanelLms140s10(l120m01a) ? "Y" : "N");
	        return result;
	    }

}
