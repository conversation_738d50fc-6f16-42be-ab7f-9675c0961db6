package com.mega.eloan.lms.mfaloan.bean;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 國內消金、現金存入警示戶明細檔 <br/>
 * 在 RetrialServiceImpl :: up491_at_proc_elf591_custid_not_in_ELF491(...) 寫入 c241m01z.reason=7  <br/>
 * 若 ELF591H_LOAN_NO 的科目以2開頭，elf491_remomo 加入2 ； 當科目非2開頭，elf491_remomo 加入1
*/
public class ELF591 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 資料日期 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF591_DATA_DATE", columnDefinition = "DATE")
	private Date elf591_data_date;

	/** 放款帳號 */
	@Column(name = "ELF591_LOAN_NO", length = 14, columnDefinition = "CHAR(14)", nullable=false,unique = true)
	private String elf591_loan_no;
	
	/** 借款人ID/重複碼 */
	@Column(name = "ELF591_CUSTID", length = 11, columnDefinition = "CHAR(11)")
	private String elf591_custid;
		
	/** 資料來源 { CRMLN116 , SRMLN006 }
	  	＝＝＝＝＝＝＝＝＝＝＝＝＝＝
		「國內消金現金存入警示戶明細表」這一份報表，在不同的系統，有不同的代號(但其內容一樣)
		● 在 CRM 系統 CRMLN116 
		● 在 RPQS 系統 SRMLN175
		所以，雖然在 RPQS 用代號 CRMLN116 會查無資料。但若用「報表的中文名稱」去查，則可查到資料。
	 */
	@Column(name = "ELF591_DATA_SOURCE", length = 8, columnDefinition = "CHAR(8)", nullable=false,unique = true)
	private String elf591_data_source;

	public Date getElf591_data_date() {
		return elf591_data_date;
	}

	public void setElf591_data_date(Date elf591_data_date) {
		this.elf591_data_date = elf591_data_date;
	}

	public String getElf591_loan_no() {
		return elf591_loan_no;
	}

	public void setElf591_loan_no(String elf591_loan_no) {
		this.elf591_loan_no = elf591_loan_no;
	}

	public String getElf591_custid() {
		return elf591_custid;
	}

	public void setElf591_custid(String elf591_custid) {
		this.elf591_custid = elf591_custid;
	}

	public String getElf591_data_source() {
		return elf591_data_source;
	}

	public void setElf591_data_source(String elf591_data_source) {
		this.elf591_data_source = elf591_data_source;
	}

	
}
