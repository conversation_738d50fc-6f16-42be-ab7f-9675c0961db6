/* 
 * CLS1131S04Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractOutputPage;

/**
 * <pre>
 * 關聯戶貨款明細
 * </pre>
 * 
 * @since 2013/3/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/13,Fantasy,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1131s04")
public class CLS1131S04Page extends AbstractOutputPage {

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {

		setNeedHtml(true); // need html

		setJavascript(new String[] { "pagejs/cls/CLS1131S04Page.js" });

		return "&nbsp;";
	}

	@Override
	protected String getViewName() {
		return getEloanPagePathByClass(getClass());
	}

}
