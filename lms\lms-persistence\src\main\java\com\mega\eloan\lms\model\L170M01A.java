/* 
 * L170M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 覆審報告表主檔 **/
@NamedEntityGraph(name = "L170M01A-entity-graph", attributeNodes = { 
		@NamedAttributeNode("l170m01c"), 
		@NamedAttributeNode("l170m01f")
		})
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L170M01A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "ctlType" }))
public class L170M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	@ToStringExclude
	@OneToMany(mappedBy = "l170m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L170A01A> l170a01a;

	@ToStringExclude
	@OneToMany(mappedBy = "l170m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L170M01B> l170m01bs;
	
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", insertable = false, updatable = false) })
	private L170M01C l170m01c;

	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", insertable = false, updatable = false) })
	private L170M01F l170m01f;

	@ToStringExclude
	@OneToMany(mappedBy = "l170m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@OrderBy("itemSeq")
	private List<L170M01D> l170m01ds;

	/**
	 * JOIN考評表明細檔ByMAINID
	 *
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l170m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L170M01J> l170m01js;
	
	public void setL170m01js(Set<L170M01J> l170m01js) {
		this.l170m01js = l170m01js;
	}
	
	public Set<L170M01J> getL170m01js() {
		return l170m01js;
	}

	/**
	 * 覆審日期
	 * <p/>
	 * 預設為系統日期，可修改
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "RETRIALDATE", columnDefinition = "DATE")
	private Date retrialDate;

	/**
	 * 上次覆審日期
	 * <p/>
	 * 引進授信戶覆審檔(ELCHKLST)的上次覆審日期(LRDATE)
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LASTRETRIALDATE", columnDefinition = "DATE")
	private Date lastRetrialDate;

	/**
	 * 覆審案號_序號
	 * <p/>
	 * 格式：001<br/>
	 * 自行新增案件於編製完成上傳覆審控制檔後才自動給號
	 */
	@Column(name = "PROJECTSEQ", columnDefinition = "DECIMAL(3,0)")
	private Integer projectSeq;

	/**
	 * 覆審案號
	 * <p/>
	 * 格式為：年度(YYYY) +分行簡稱(3碼)+(兆)+覆審字第+ 批號+ - + 序號 + 號，例：2011蘭雅(兆)覆審字第001-003號
	 */
	@Column(name = "PROJECTNO", length = 64, columnDefinition = "VARCHAR(64)")
	private String projectNo;

	/**
	 * 不覆審代碼
	 * <p/>
	 * NEW_NCKDFLAG<br/>
	 * 1.本行或同業主辦之聯貸案件，非擔任管理行。<br/>
	 * 2.十成定存。<br/>
	 * 3.純進出押戶。<br/>
	 * 4.對政府或政府所屬機關、學校之授信案件。<br/>
	 * 5.拆放同業或對同業之融通。<br/>
	 * 6.已列報為逾期放款或轉列催收款項之案件。<br/>
	 * 7.銷戶。<br/>
	 * 8.本次暫不覆審。<br/>
	 * 9.已專案核准免辦理覆審之房屋仲介價金履約保證案件。
	 */
	@Column(name = "NCKDFLAG", length = 2, columnDefinition = "VARCHAR(2)")
	private String nCkdFlag;

	/**
	 * 負責人
	 * <p/>
	 * 引進「客戶檔」的「主要負責人」<br/>
	 * MIS.ELCUS25.SUP1CNM or <br/>
	 * MIS.ELCUS25.SUP3CNM
	 */
	@Column(name = "CHAIRMAN", length = 240, columnDefinition = "VARCHAR(240)")
	private String chairman;

	/**
	 * 行業別
	 * <p/>
	 * 引進「客戶檔」的「行業別」<br/>
	 * MIS.CUSTDATA.BUSCD<br/>
	 * ( MIS.BSTBL.ECOCD<br/>
	 * ( MIS.BSTBL.ECONM<br/>
	 * 101/05/21調整<br/>
	 * VARCHAR(30) ( VARCHAR(75)
	 */
	@Column(name = "TRADETYPE", length = 192, columnDefinition = "VARCHAR(192)")
	private String tradeType;

	/**
	 * 是否為主要借款人
	 * <p/>
	 * Y/N<br/>
	 * 引進「授信戶覆審檔」(ELCHKLST)的「主要客戶註記」(MAINCUST)<br/>
	 * 每半年需覆審一次者為「是」
	 */
	@Column(name = "MLOANPERSON", length = 1, columnDefinition = "VARCHAR(1)")
	private String mLoanPerson;

	/**
	 * 保證人
	 * <p/>
	 * 抓取額度明細表之連帶保證人不夠準確，改讀取「主從債務人資料檔」(MIS.ELLNGTEE)之目前連保人，超過3人以ＸＸＸ等N人表示
	 */
	@Column(name = "RLTGUARANTOR", length = 768, columnDefinition = "VARCHAR(768)")
	private String rltGuarantor;

	/**
	 * 授信資料引進日期
	 * <p/>
	 * 100/08/31新增<br/>
	 * 帳務資料日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LNDATADATE", columnDefinition = "DATE")
	private Date lnDataDate;

	/**
	 * 額度幣別
	 * <p/>
	 * 100/11/11新增<br/>
	 * 國內：TWD<br/>
	 * 海外：本位幣
	 */
	@Column(name = "TOTQUOTACURR", length = 3, columnDefinition = "CHAR(3)")
	private String totQuotaCurr;

	/**
	 * 額度合計
	 * <p/>
	 * 100/08/31新增<br/>
	 * 折算合計值(單位：仟元)<br/>
	 * 國內：(匯率檔：MIS.RATETBL)<br/>
	 * 海外：(匯率檔DW_FXRTHOVS)
	 */
	@Column(name = "TOTQUOTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totQuota;

	/**
	 * 前日結欠餘額幣別
	 * <p/>
	 * 100/11/11新增<br/>
	 * 國內：TWD<br/>
	 * 海外：本位幣
	 */
	@Column(name = "TOTBALCURR", length = 3, columnDefinition = "CHAR(3)")
	private String totBalCurr;

	/**
	 * 前日結欠餘額合計
	 * <p/>
	 * 100/08/31新增<br/>
	 * 折算合計值(單位：仟元)<br/>
	 * 國內：(匯率檔：MIS.RATETBL)<br/>
	 * 海外：(匯率檔DW_FXRTHOVS)
	 */
	@Column(name = "TOTBAL", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totBal;

	/**
	 * RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	@Column(name = "RPTID", length = 32, columnDefinition = "VARCHAR(32)")
	private String rptId;

	@Column(name = "PID", length = 32, columnDefinition = "CHAR(32)")
	private String pid;

	/**
	 * 主要授信戶
	 */
	@Column(name = "MLOANPERSONA", length = 1, columnDefinition = "CHAR(1)")
	private String mLoanPersonA;

	/**
	 * 0024 BusCd
	 */
	@Column(name = "BUSCD", length = 6, columnDefinition = "VARCHAR(6)")
	private String busCd;
	/**
	 * 0024 BussKind
	 */
	@Column(name = "BUSSKIND", length = 2, columnDefinition = "VARCHAR(2)")
	private String bussKind;

	/**
	 * CES.C120M01A資信簡表 BIZNAME 行業別名稱
	 */
	@Column(name = "CESTRADETYPE", length = 192, columnDefinition = "VARCHAR(192)")
	private String cesTradeType;

	/**
	 * CES.C120M01A資信簡表 BIZMODE 行業別
	 */
	@Column(name = "CESBUSCD", length = 6, columnDefinition = "VARCHAR(6)")
	private String cesBusCd;

	/**
	 * CES.C120M01A資信簡表 SN 資簡編號
	 */
	@Column(name = "CESSN", length = 60, columnDefinition = "VARCHAR(60)")
	private String cesSN;

	/**
	 * CES.C120M01A資信簡表 COMPLETEDATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CESCOMPLETEDATE", columnDefinition = "DATE")
	private Date cesCompleteDate;

	/**
	 * 保證人(連帶、一般) 採「自由格式」
	 */
	@Column(name = "FREEG", length = 1, columnDefinition = "CHAR(1)")
	private String freeG;

	/**
	 * 共同借款人 採「自由格式」
	 */
	@Column(name = "FREEC", length = 1, columnDefinition = "CHAR(1)")
	private String freeC;

	/**
	 * 覆審控制檔實地覆審註記 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 */
	@Column(name = "REALCKFG", length = 1, columnDefinition = "CHAR(1)")
	private String realCkFg;

	/**
	 * 最近一次實地覆審時間 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "REALDT", columnDefinition = "DATE")
	private Date realDt;

	/**
	 * 本案是否為實地覆審報告表 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 */
	@Column(name = "REALRPFG", length = 1, columnDefinition = "CHAR(1)")
	private String realRpFg;

	/**
	 * 覆審名單類別 J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 */
	@Column(name = "CTLTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String ctlType;

	/**
	 * J-108-0268 覆審案件 客戶逾期情形 查詢日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "OVQRYDT", columnDefinition = "DATE")
	private Date ovQryDt;

	/**
	 * J-108-0268 覆審案件 客戶逾期情形 本金逾期天數
	 */
	@Column(name = "CAPDAYS", columnDefinition = "DECIMAL(5,0)")
	private Integer capDays;

	/**
	 * J-108-0268 覆審案件 客戶逾期情形 本金逾期日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CAPDT", columnDefinition = "DATE")
	private Date capDt;

	/**
	 * J-108-0268 覆審案件 客戶逾期情形 本金資料日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CAPDATADT", columnDefinition = "DATE")
	private Date capDataDt;

	/**
	 * J-108-0268 覆審案件 客戶逾期情形 利息逾期天數
	 */
	@Column(name = "INTDAYS", columnDefinition = "DECIMAL(5,0)")
	private Integer intDays;

	/**
	 * J-108-0268 覆審案件 客戶逾期情形 利息逾期日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "INTDT", columnDefinition = "DATE")
	private Date intDt;

	/**
	 * J-108-0268 覆審案件 客戶逾期情形 利息資料日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "INTDATADT", columnDefinition = "DATE")
	private Date intDataDt;

	/**
	 * J-109-0313 小規模覆審 純小規模營業人(央行C方案)
	 **/
	@Column(name = "ISSMALLBUSS", length = 1, columnDefinition = "CHAR(1)")
	private String isSmallBuss;

	/**
	 * J-109-0313 小規模覆審 銀行簡易評分表總分(該ID最低分)
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "SBSCORE", columnDefinition = "DECIMAL(5,0)")
	private BigDecimal sbScore;

	/**
	 * 處理狀態
	 * <p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 */
	@Size(max = 3)
	@Column(name = "STATUS", length = 3, columnDefinition = "CHAR(3)")
	private String status;

	/** 回傳結果原因 **/
	@Size(max = 300)
	@Column(name = "REASON", length = 300, columnDefinition = "VARCHAR(300)")
	private String reason;

    /**
     * 考核表(performance appraisal) - 是否有須扣分情事
     * <p/>
     * Y/N
     */
    @Column(name = "NEEDPA", length = 1, columnDefinition = "VARCHAR(1)")
    private String needPa;

	/** 首次覆核時間
	 * 上線時舊案以retrialDate帶入 **/
	@Column(name="FIRSTACCTIME", columnDefinition="TIMESTAMP")
	private Timestamp firstAccTime;

	/**
	 * 考核表版本
	 */
	@Column(name = "PAVER", length = 15, columnDefinition = "VARCHAR(15)")
	private String paVer;

	/**
	 * 取得覆審日期
	 * <p/>
	 * 預設為系統日期，可修改
	 */
	public Date getRetrialDate() {
		return this.retrialDate;
	}

	/**
	 * 設定覆審日期
	 * <p/>
	 * 預設為系統日期，可修改
	 **/
	public void setRetrialDate(Date value) {
		this.retrialDate = value;
	}

	/**
	 * 取得上次覆審日期
	 * <p/>
	 * 引進授信戶覆審檔(ELCHKLST)的上次覆審日期(LRDATE)
	 */
	public Date getLastRetrialDate() {
		return this.lastRetrialDate;
	}

	/**
	 * 設定上次覆審日期
	 * <p/>
	 * 引進授信戶覆審檔(ELCHKLST)的上次覆審日期(LRDATE)
	 **/
	public void setLastRetrialDate(Date value) {
		this.lastRetrialDate = value;
	}

	/**
	 * 取得覆審案號_序號
	 * <p/>
	 * 格式：001<br/>
	 * 自行新增案件於編製完成上傳覆審控制檔後才自動給號
	 */
	public Integer getProjectSeq() {
		return this.projectSeq;
	}

	/**
	 * 設定覆審案號_序號
	 * <p/>
	 * 格式：001<br/>
	 * 自行新增案件於編製完成上傳覆審控制檔後才自動給號
	 **/
	public void setProjectSeq(Integer value) {
		this.projectSeq = value;
	}

	/**
	 * 取得覆審案號
	 * <p/>
	 * 格式為：年度(YYYY) +分行簡稱(3碼)+(兆)+覆審字第+ 批號+ - + 序號 + 號，例：2011蘭雅(兆)覆審字第001-003號
	 */
	public String getProjectNo() {
		return this.projectNo;
	}

	/**
	 * 設定覆審案號
	 * <p/>
	 * 格式為：年度(YYYY) +分行簡稱(3碼)+(兆)+覆審字第+ 批號+ - + 序號 + 號，例：2011蘭雅(兆)覆審字第001-003號
	 **/
	public void setProjectNo(String value) {
		this.projectNo = value;
	}

	/**
	 * 取得不覆審代碼
	 * <p/>
	 * NEW_NCKDFLAG<br/>
	 * 1.本行或同業主辦之聯貸案件，非擔任管理行。<br/>
	 * 2.十成定存。<br/>
	 * 3.純進出押戶。<br/>
	 * 4.對政府或政府所屬機關、學校之授信案件。<br/>
	 * 5.拆放同業或對同業之融通。<br/>
	 * 6.已列報為逾期放款或轉列催收款項之案件。<br/>
	 * 7.銷戶。<br/>
	 * 8.本次暫不覆審。<br/>
	 * 9.已專案核准免辦理覆審之房屋仲介價金履約保證案件。
	 */
	public String getNCkdFlag() {
		return this.nCkdFlag;
	}

	/**
	 * 設定不覆審代碼
	 * <p/>
	 * NEW_NCKDFLAG<br/>
	 * 1.本行或同業主辦之聯貸案件，非擔任管理行。<br/>
	 * 2.十成定存。<br/>
	 * 3.純進出押戶。<br/>
	 * 4.對政府或政府所屬機關、學校之授信案件。<br/>
	 * 5.拆放同業或對同業之融通。<br/>
	 * 6.已列報為逾期放款或轉列催收款項之案件。<br/>
	 * 7.銷戶。<br/>
	 * 8.本次暫不覆審。<br/>
	 * 9.已專案核准免辦理覆審之房屋仲介價金履約保證案件。
	 **/
	public void setNCkdFlag(String value) {
		this.nCkdFlag = value;
	}

	/**
	 * 取得負責人
	 * <p/>
	 * 引進「客戶檔」的「主要負責人」<br/>
	 * MIS.ELCUS25.SUP1CNM or <br/>
	 * MIS.ELCUS25.SUP3CNM
	 */
	public String getChairman() {
		return this.chairman;
	}

	/**
	 * 設定負責人
	 * <p/>
	 * 引進「客戶檔」的「主要負責人」<br/>
	 * MIS.ELCUS25.SUP1CNM or <br/>
	 * MIS.ELCUS25.SUP3CNM
	 **/
	public void setChairman(String value) {
		this.chairman = value;
	}

	/**
	 * 取得行業別
	 * <p/>
	 * 引進「客戶檔」的「行業別」<br/>
	 * MIS.CUSTDATA.BUSCD<br/>
	 * ( MIS.BSTBL.ECOCD<br/>
	 * ( MIS.BSTBL.ECONM<br/>
	 * 101/05/21調整<br/>
	 * VARCHAR(30) ( VARCHAR(75)
	 */
	public String getTradeType() {
		return this.tradeType;
	}

	/**
	 * 設定行業別
	 * <p/>
	 * 引進「客戶檔」的「行業別」<br/>
	 * MIS.CUSTDATA.BUSCD<br/>
	 * ( MIS.BSTBL.ECOCD<br/>
	 * ( MIS.BSTBL.ECONM<br/>
	 * 101/05/21調整<br/>
	 * VARCHAR(30) ( VARCHAR(75)
	 **/
	public void setTradeType(String value) {
		this.tradeType = value;
	}

	/**
	 * 取得是否為主要借款人
	 * <p/>
	 * Y/N<br/>
	 * 引進「授信戶覆審檔」(ELCHKLST)的「主要客戶註記」(MAINCUST)<br/>
	 * 每半年需覆審一次者為「是」
	 */
	public String getMLoanPerson() {
		return this.mLoanPerson;
	}

	/**
	 * 設定是否為主要借款人
	 * <p/>
	 * Y/N<br/>
	 * 引進「授信戶覆審檔」(ELCHKLST)的「主要客戶註記」(MAINCUST)<br/>
	 * 每半年需覆審一次者為「是」
	 **/
	public void setMLoanPerson(String value) {
		this.mLoanPerson = value;
	}

	/**
	 * 取得保證人
	 * <p/>
	 * 抓取額度明細表之連帶保證人不夠準確，改讀取「主從債務人資料檔」(MIS.ELLNGTEE)之目前連保人，超過3人以ＸＸＸ等N人表示
	 */
	public String getRltGuarantor() {
		return this.rltGuarantor;
	}

	/**
	 * 設定保證人
	 * <p/>
	 * 抓取額度明細表之連帶保證人不夠準確，改讀取「主從債務人資料檔」(MIS.ELLNGTEE)之目前連保人，超過3人以ＸＸＸ等N人表示
	 **/
	public void setRltGuarantor(String value) {
		this.rltGuarantor = value;
	}

	/**
	 * 取得授信資料引進日期
	 * <p/>
	 * 100/08/31新增<br/>
	 * 帳務資料日期
	 */
	public Date getLnDataDate() {
		return this.lnDataDate;
	}

	/**
	 * 設定授信資料引進日期
	 * <p/>
	 * 100/08/31新增<br/>
	 * 帳務資料日期
	 **/
	public void setLnDataDate(Date value) {
		this.lnDataDate = value;
	}

	/**
	 * 取得額度幣別
	 * <p/>
	 * 100/11/11新增<br/>
	 * 國內：TWD<br/>
	 * 海外：本位幣
	 */
	public String getTotQuotaCurr() {
		return this.totQuotaCurr;
	}

	/**
	 * 設定額度幣別
	 * <p/>
	 * 100/11/11新增<br/>
	 * 國內：TWD<br/>
	 * 海外：本位幣
	 **/
	public void setTotQuotaCurr(String value) {
		this.totQuotaCurr = value;
	}

	/**
	 * 取得額度合計
	 * <p/>
	 * 100/08/31新增<br/>
	 * 折算合計值(單位：仟元)<br/>
	 * 國內：(匯率檔：MIS.RATETBL)<br/>
	 * 海外：(匯率檔DW_FXRTHOVS)
	 */
	public BigDecimal getTotQuota() {
		return this.totQuota;
	}

	/**
	 * 設定額度合計
	 * <p/>
	 * 100/08/31新增<br/>
	 * 折算合計值(單位：仟元)<br/>
	 * 國內：(匯率檔：MIS.RATETBL)<br/>
	 * 海外：(匯率檔DW_FXRTHOVS)
	 **/
	public void setTotQuota(BigDecimal value) {
		this.totQuota = value;
	}

	/**
	 * 取得前日結欠餘額幣別
	 * <p/>
	 * 100/11/11新增<br/>
	 * 國內：TWD<br/>
	 * 海外：本位幣
	 */
	public String getTotBalCurr() {
		return this.totBalCurr;
	}

	/**
	 * 設定前日結欠餘額幣別
	 * <p/>
	 * 100/11/11新增<br/>
	 * 國內：TWD<br/>
	 * 海外：本位幣
	 **/
	public void setTotBalCurr(String value) {
		this.totBalCurr = value;
	}

	/**
	 * 取得前日結欠餘額合計
	 * <p/>
	 * 100/08/31新增<br/>
	 * 折算合計值(單位：仟元)<br/>
	 * 國內：(匯率檔：MIS.RATETBL)<br/>
	 * 海外：(匯率檔DW_FXRTHOVS)
	 */
	public BigDecimal getTotBal() {
		return this.totBal;
	}

	/**
	 * 設定前日結欠餘額合計
	 * <p/>
	 * 100/08/31新增<br/>
	 * 折算合計值(單位：仟元)<br/>
	 * 國內：(匯率檔：MIS.RATETBL)<br/>
	 * 海外：(匯率檔DW_FXRTHOVS)
	 **/
	public void setTotBal(BigDecimal value) {
		this.totBal = value;
	}

	/**
	 * 取得RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	public String getRptId() {
		return this.rptId;
	}

	/**
	 * 設定RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 **/
	public void setRptId(String value) {
		this.rptId = value;
	}

	public Set<L170A01A> getL170a01a() {
		return l170a01a;
	}

	public void setC241a01a(Set<L170A01A> l170a01a) {
		this.l170a01a = l170a01a;
	}

	public Set<L170M01B> getL170m01bs() {
		return l170m01bs;
	}

	public void setL170m01bs(Set<L170M01B> l170m01bs) {
		this.l170m01bs = l170m01bs;
	}

	public void setL170m01c(L170M01C l170m01c) {
		this.l170m01c = l170m01c;
	}

	public L170M01C getL170m01c() {
		return l170m01c;
	}

	public void setL170m01f(L170M01F l170m01f) {
		this.l170m01f = l170m01f;
	}

	public L170M01F getL170m01f() {
		return l170m01f;
	}

	public void setL170m01ds(List<L170M01D> l170m01ds) {
		this.l170m01ds = l170m01ds;
	}

	public List<L170M01D> getL170m01ds() {
		return l170m01ds;
	}

	public String getPid() {
		return this.pid;
	}

	public void setPid(String value) {
		this.pid = value;
	}

	public String getMLoanPersonA() {
		return mLoanPersonA;
	}

	public void setMLoanPersonA(String mLoanPersonA) {
		this.mLoanPersonA = mLoanPersonA;
	}

	public String getBusCd() {
		return busCd;
	}

	public void setBusCd(String busCd) {
		this.busCd = busCd;
	}

	public String getBussKind() {
		return bussKind;
	}

	public void setBussKind(String bussKind) {
		this.bussKind = bussKind;
	}

	public String getCesTradeType() {
		return cesTradeType;
	}

	public void setCesTradeType(String cesTradeType) {
		this.cesTradeType = cesTradeType;
	}

	public String getCesBusCd() {
		return cesBusCd;
	}

	public void setCesBusCd(String cesBusCd) {
		this.cesBusCd = cesBusCd;
	}

	public String getCesSN() {
		return cesSN;
	}

	public void setCesSN(String cesSN) {
		this.cesSN = cesSN;
	}

	public Date getCesCompleteDate() {
		return cesCompleteDate;
	}

	public void setCesCompleteDate(Date cesCompleteDate) {
		this.cesCompleteDate = cesCompleteDate;
	}

	public String getFreeG() {
		return freeG;
	}

	public void setFreeG(String freeG) {
		this.freeG = freeG;
	}

	public String getFreeC() {
		return freeC;
	}

	public void setFreeC(String freeC) {
		this.freeC = freeC;
	}

	/** 設定覆審控制檔實地覆審註記 **/
	public void setRealCkFg(String realCkFg) {
		this.realCkFg = realCkFg;
	}

	/** 取得覆審控制檔實地覆審註記 **/
	public String getRealCkFg() {
		return realCkFg;
	}

	/** 設定最近一次實地覆審時間 **/
	public void setRealDt(Date realDt) {
		this.realDt = realDt;
	}

	/** 取得最近一次實地覆審時間 **/
	public Date getRealDt() {
		return realDt;
	}

	/** 設定本案是否為實地覆審報告表 **/
	public void setRealRpFg(String realRpFg) {
		this.realRpFg = realRpFg;
	}

	/** 取得本案是否為實地覆審報告表 **/
	public String getRealRpFg() {
		return realRpFg;
	}

	/** 設定覆審名單類別 **/
	public void setCtlType(String ctlType) {
		this.ctlType = ctlType;
	}

	/** 取得覆審名單類別 **/
	public String getCtlType() {
		return ctlType;
	}

	/** 取得客戶逾期情形 查詢日 **/
	public Date getOvQryDt() {
		return this.ovQryDt;
	}

	/** 設定客戶逾期情形 查詢日 **/
	public void setOvQryDt(Date value) {
		this.ovQryDt = value;
	}

	/** 取得客戶逾期情形 本金逾期天數 **/
	public Integer getCapDays() {
		return this.capDays;
	}

	/** 取得客戶逾期情形 本金逾期天數 **/
	public void setCapDays(Integer value) {
		this.capDays = value;
	}

	/** 取得客戶逾期情形 本金逾期日 **/
	public Date getCapDt() {
		return this.capDt;
	}

	/** 設定客戶逾期情形 本金逾期日 **/
	public void setCapDt(Date value) {
		this.capDt = value;
	}

	/** 取得客戶逾期情形 本金資料日 **/
	public Date getCapDataDt() {
		return this.capDataDt;
	}

	/** 設定客戶逾期情形 本金資料日 **/
	public void setCapDataDt(Date value) {
		this.capDataDt = value;
	}

	/** 取得客戶逾期情形 利息逾期天數 **/
	public Integer getIntDays() {
		return this.intDays;
	}

	/** 取得客戶逾期情形 利息逾期天數 **/
	public void setIntDays(Integer value) {
		this.intDays = value;
	}

	/** 取得客戶逾期情形 利息逾期日 **/
	public Date getIntDt() {
		return this.intDt;
	}

	/** 設定客戶逾期情形 利息逾期日 **/
	public void setIntDt(Date value) {
		this.intDt = value;
	}

	/** 取得客戶逾期情形 利息資料日 **/
	public Date getIntDataDt() {
		return this.intDataDt;
	}

	/** 設定客戶逾期情形 利息資料日 **/
	public void setIntDataDt(Date value) {
		this.intDataDt = value;
	}

	/**
	 * J-109-0313 小規模覆審 設定純小規模營業人(央行C方案)
	 */
	public void setIsSmallBuss(String isSmallBuss) {
		this.isSmallBuss = isSmallBuss;
	}

	/**
	 * J-109-0313 小規模覆審 取得純小規模營業人(央行C方案)
	 */
	public String getIsSmallBuss() {
		return isSmallBuss;
	}

	/**
	 * J-109-0313 小規模覆審 設定銀行簡易評分表總分(該ID最低分)
	 */
	public void setSbScore(BigDecimal sbScore) {
		this.sbScore = sbScore;
	}

	/**
	 * J-109-0313 小規模覆審 取得銀行簡易評分表總分(該ID最低分)
	 */
	public BigDecimal getSbScore() {
		return sbScore;
	}

	/**
	 * 取得處理狀態
	 * <p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 */
	public String getStatus() {
		return this.status;
	}

	/**
	 * 設定處理狀態
	 * <p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 **/
	public void setStatus(String value) {
		this.status = value;
	}

	/** 取得回傳結果原因 **/
	public String getReason() {
		return this.reason;
	}

	/** 設定回傳結果原因 **/
	public void setReason(String value) {
		this.reason = value;
	}

	/** 取得是否有須扣分情事 **/
	public String getNeedPa() {
		return this.needPa;
	}

	/** 設定是否有須扣分情事 **/
	public void setNeedPa(String value) {
		this.needPa = value;
	}

	/** 取得首次覆核時間 **/
	public Timestamp getFirstAccTime() {
		return this.firstAccTime;
	}
	/** 設定首次覆核時間 **/
	public void setFirstAccTime(Timestamp value) {
		this.firstAccTime = value;
	}

	/** 取得考評表版本 **/
	public String getPaVer() {
		return this.paVer;
	}

	/** 設定考評表版本 **/
	public void setPaVer(String value) {
		this.paVer = value;
	}
}
