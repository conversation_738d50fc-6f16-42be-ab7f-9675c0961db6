package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * The persistent class for the C140S07B database table.
 * </pre>
 * @since  2011/10/04
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/10/04,TimChiang,new
 *          </ul>
 */
@StaticMetamodel(C140S07B.class)
public class C140S07B_ extends RelativeMeta_{
	public static volatile SingularAttribute<C140S07B, String> curr;
	public static volatile SingularAttribute<C140S07B, String> lnd1_1;
	public static volatile SingularAttribute<C140S07B, BigDecimal> lnd1_2;
	public static volatile SingularAttribute<C140S07B, BigDecimal> lnd1_3;
	public static volatile SingularAttribute<C140S07B, BigDecimal> lnd1_5;
	public static volatile SingularAttribute<C140S07B, BigDecimal> lnd1_6;
	public static volatile SingularAttribute<C140S07B, BigDecimal> lnd1_7;
	public static volatile SingularAttribute<C140S07B, BigDecimal> lnd1_8;
	public static volatile SingularAttribute<C140S07B, BigDecimal> lnd1_9;
	public static volatile SingularAttribute<C140S07B, BigDecimal> lnd1_10;
	public static volatile SingularAttribute<C140S07B, String> lndid_1;
	public static volatile SingularAttribute<C140S07B, String> subj;
	public static volatile SingularAttribute<C140S07B, C140M01A> c140m01a;
	public static volatile SingularAttribute<C140S07B, C140M04B> c140m04b;
}
