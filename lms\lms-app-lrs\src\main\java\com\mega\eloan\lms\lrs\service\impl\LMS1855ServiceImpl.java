/* 
 *  LMS1855ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.service.impl;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import jxl.SheetSettings;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.PageOrientation;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.dao.L185A01ADao;
import com.mega.eloan.lms.dao.L185M01ADao;
import com.mega.eloan.lms.dw.service.DwLnquotovService;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.Dw_elf411ovsService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.Lms412Service;
import com.mega.eloan.lms.lrs.pages.LMS1855V01Page;
import com.mega.eloan.lms.lrs.service.LMS1805Service;
import com.mega.eloan.lms.lrs.service.LMS1855Service;
import com.mega.eloan.lms.mfaloan.service.MisLMS422Service;
import com.mega.eloan.lms.mfaloan.service.MisLNFE0851Service;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.eloan.lms.model.L185A01A;
import com.mega.eloan.lms.model.L185M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class LMS1855ServiceImpl extends AbstractCapService implements
		LMS1855Service {
	private static Logger logger = LoggerFactory
	.getLogger(LMS1855ServiceImpl.class);
	
	@Resource
	DocFileDao docFileDao;

	@Resource
	L185M01ADao l185m01aDao;

	@Resource
	L185A01ADao l185a01aDao;

	@Autowired
	DocFileService fileService;

	@Resource
	BranchService branchService;

	@Resource
	EloandbBASEService eloandbBaseService;

	@Resource
	TempDataService tempDataService;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	Dw_elf411ovsService dwElf411ovsService;

	@Resource
	Lms412Service lms412Service;

	@Resource
	DwdbBASEService dwdbService;

	@Resource
	MisLNFE0851Service misLnfe0851Service;

	@Resource
	LMS1805Service lms1805Service;

	@Resource
	MisLMS422Service misLms422Service;

	@Resource
	DwLnquotovService dwlnquotovService;

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L185M01A.class) {
			return l185m01aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings("deprecation")
	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L185M01A) {
					if ("".equals(Util.nullToSpace(((L185M01A) model)
							.getCreatDate()))) {
						Date createDate = CapDate.getCurrentTimestamp();
						createDate.setMonth(createDate.getMonth() - 1);
						createDate.setDate(1);
						((L185M01A) model).setCreatDate(createDate);
					}
					((L185M01A) model).setUpdater(user.getUserId());
					((L185M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if ("".equals(Util.nullToSpace(((L185M01A) model)
							.getCreateTime()))) {
						((L185M01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					if ("".equals(Util.nullToSpace(((L185M01A) model)
							.getCreator()))) {
						((L185M01A) model).setCreator(user.getUserId());
					}
					l185m01aDao.save((L185M01A) model);
				} else if (model instanceof L185A01A) {
					l185a01aDao.save((L185A01A) model);
				}
			}
		}
	}

	@SuppressWarnings("unused")
	@Override
	public String findType1ByBrNoAndDate(String brNo, String dataDate,
			String listName) throws WriteException, IOException {
		// 索撈到的資料一次傳回List跑EXCEL迴圈
		List<Map<String, Object>> list1 = new ArrayList<Map<String, Object>>();
		Date dataDateIn = TWNDate.valueOf(dataDate + "-01");
		Date mddt = null;
		String process = "";
		// 取得逾期未複審名單
		List<Map<String, Object>> rows = lms412Service
				.findLMS412ByBranchForReportType1(brNo);
		// 有資料
		if (rows.size() > 0) {
			for (Map<String, Object> dataMap : rows) {
				// 上次覆審日
				Date lrdate = (Date) dataMap.get("LRDATE");
				if (lrdate != null) {
					String lrdateStr = transportStr(lrdate);
				}
				String[] returnVal = lms1805Service.cauculateDate(dataDateIn,
						dataMap, 3);
				// 應覆審未覆審
				if (TWNDate.valueOf(returnVal[0]).before(dataDateIn)
						|| lrdate == null) {
					dataMap.put("NEXTNWDT", TWNDate.valueOf(returnVal[0]));
					String branch = Util.trim(dataMap.get("BRANCH"));
					String custId = Util.trim(dataMap.get("CUSTID"));
					String dupNo = Util.trim(dataMap.get("DUPNO"));
					String dbuobu = Util.trim(dataMap.get("DBUOBU"));
					String dbucoid = Util.trim(dataMap.get("DBUCOID"));
					String obucoid = Util.trim(dataMap.get("OBUCOID"));
					String mainCust = Util.trim(dataMap.get("MAINCUST"));
					String mowtype = Util.trim(dataMap.get("MOWTYPE"));
					// 銷戶日
					Date canceldt = (Date) dataMap.get("CANCELDT");
					String nckdflag = Util.trim(dataMap.get("NCKDFLAG"));
					// 不覆審日期
					Date nckddate = (Date) dataMap.get("NCKDDATE");
					// 上次設定之下次恢復覆審日
					Date nextltdt = (Date) dataMap.get("NEXTLTDT");
					// 最新一次下次恢復覆審日
					Date nextnwdt = (Date) dataMap.get("NEXTNWDT");
					// 信用模型評等類別
					String mowtbl1 = Util.trim(dataMap.get("MOWTBL1"));
					// 戶況
					String cstate = Util.trim(dataMap.get("CSTATE"));
					// 原始週期
					String ockdline = Util.trim(dataMap.get("OCKDLINE"));
					// 資信評等
					String crdtype = Util.trim(dataMap.get("CRDTYPE"));
					String crdttbl = Util.trim(dataMap.get("CRDTTBL"));
					// 不覆審備註
					String nckdmemo = Util.trim(dataMap.get("NCKDMEMO"));
					// 信用模型評等類別
					String mowType = Util.trim(dataMap.get("MOWTYPE"));
					// 上上次覆審日
					Date llrdate = (Date) dataMap.get("LLRDATE");
					// 覆審週期
					String rckdline = Util.trim(dataMap.get("RCKDLINE"));
					// 異常通報代碼
					String mdflag = Util.trim(dataMap.get("MDFLAG"));
					List<String> obuList = null;
					List<String> dbuList = null;
					/**
					 * // 如果有共管
					 * 抓共管額度**************************************國外不會有共管額度
					 * if("AIZ0000233".equals(custId)){ System.out.println(1); }
					 * if ("Y".equals(Util.trim(dataMap.get("DBUOBU")))) {
					 * Map<String, Object> lnf25Map = this
					 * .checkImport_LNF025(branch, custId, dupNo, dbuobu,
					 * dbucoid, obucoid); if (lnf25Map.isNotEmpty()) { boolean
					 * lnf25Result = (Boolean) lnf25Map .get("RESULT"); if
					 * (!lnf25Result) { return "匯入DBU OBU 額度共管(LNF025)錯誤 ，分行:" +
					 * branch + " 統編:" + custId + dupNo +
					 * " (checkImport_LNF025)"; } else { dbuobu =
					 * Util.trim(lnf25Map.get("ELF412_DBUOBU")); dbucoid =
					 * Util.trim(lnf25Map.get("ELF412_DBUCOID")); obucoid =
					 * Util.trim(lnf25Map.get("ELF412_OBUCOID")); obuList =
					 * (List<String>) lnf25Map.get("ELF412_OBULIST"); dbuList =
					 * (List<String>) lnf25Map.get("ELF412_DBULIST"); } } else {
					 * return "匯入DBU OBU 額度共管(LNF025)錯誤 ，分行:" + branch + " 統編:"
					 * + custId + dupNo + " (checkImport_LNF025)"; }
					 * 
					 * // 下列授信案件應至少每半年辦理一次覆審： // 一、 //
					 * 合於本行帳戶管理員制度實施要點第四條所規定主要客戶授信額度標準
					 * ，且大型企業模型評等等級9～13級、其他企業等級10～
					 * 13級或企業資信評等D級（含）以下或尚無評等者（以前次覆審日之企業模型評等或資信評等為準
					 * ）。惟數家企業共用額度同時辦理覆審時
					 * ，以其中符合主要客戶授信額度標準且具有評等者，採評等等級最低者為準，若有境外公司或新設公司無評等者不予採計
					 * ，仍依前述評等等級最低者為準。 // 調整共用額度時採用之信評 Map<String, Object>
					 * gradeMap = this .checkProcessShareGrade(branch, custId,
					 * dupNo, mainCust, crdttbl, mowtype, mowtbl1, dbuList,
					 * obuList, rckdline); if (!gradeMap.isEmpty()) { boolean
					 * gradeResult = (Boolean) gradeMap .get("RESULT"); if
					 * (!gradeResult) { return "取得共用額度之評等基礎錯誤 分行:" + branch +
					 * " 統編:" + custId + dupNo + " (checkImport_LNF025)"; } else
					 * { mainCust = Util.trim(gradeMap .get("ELF412_MAINCUST"));
					 * crdttbl = Util.trim(gradeMap .get("ELF412_CRDTTBL"));
					 * mowtype = Util.trim(gradeMap .get("ELF412_MOWTYPE"));
					 * mowtbl1 = Util.trim(gradeMap .get("ELF412_MOWTBL1"));
					 * rckdline = Util.trim(gradeMap .get("ELF412_RCKDLINE")); }
					 * } else { return "取得共用額度之評等基礎錯誤 分行:" + branch + " 統編:" +
					 * custId + dupNo + " (checkImport_LNF025)"; }
					 * 
					 * Map<String, Object> caculateMap = this
					 * .checkProcessShareGrade(branch, custId, dupNo, mainCust,
					 * crdttbl, mowtype, mowtbl1, dbuList, obuList, rckdline);
					 * if (!gradeMap.isEmpty()) { boolean gradeResult =
					 * (Boolean) gradeMap .get("RESULT"); if (!gradeResult) {
					 * return "取得共用額度之評等基礎錯誤 分行:" + branch + " 統編:" + custId +
					 * dupNo + " (checkImport_LNF025)"; } else { mainCust =
					 * Util.trim(gradeMap .get("ELF412_MAINCUST")); crdttbl =
					 * Util.trim(gradeMap .get("ELF412_CRDTTBL")); mowtype =
					 * Util.trim(gradeMap .get("ELF412_MOWTYPE")); mowtbl1 =
					 * Util.trim(gradeMap .get("ELF412_MOWTBL1")); rckdline =
					 * Util.trim(gradeMap .get("ELF412_RCKDLINE")); } } else {
					 * return "取得共用額度之評等基礎錯誤 分行:" + branch + " 統編:" + custId +
					 * dupNo + " (checkImport_LNF025)"; } }
					 */
					// 以下為JESSICA程式
					// 找Mddt And Process 兩個欄位
					Date mddt1 = (Date) dataMap.get("MDDT");
					process = Util.trim(dataMap.get("PROCESS"));

					// Map<String, Object> datamap0851 =
					// this.findMddtAndProcess(custId, dupNo, brNo);
					// if (datamap0851.get("MDDT") != null) {
					// // 異常通報日期
					// mddt = (Date) datamap0851.get("MDDT");
					// process = Util.trim(datamap0851.get("PROCESS"));
					// } else {
					// Date mddt1 = (Date) dataMap.get("MDDT");
					// process = Util.trim(dataMap.get("PROCESS"));
					// // 直接撈LMS412先轉型為[DATE]
					// // dataMap.put("MDDT", CapDate.parseDate(mddt1));
					// }
					dataMap.put("MDDT", mddt);
					dataMap.put("PROCESS ", process);

					String newadd = Util.trim(dataMap.get("NEWADD"));
					String newdate = Util.trim(dataMap.get("NEWDATE"));
					// 人工維護日
					Date upddate = (Date) dataMap.get("UPDDATE");
					String updater = Util.trim(dataMap.get("UPDATER"));
					String memo = Util.trim(dataMap.get("MEMO"));

					String uckdline = Util.trim((dataMap.get("UCKDLINE")));

					// 主管機關通知日期
					Date uckddt = (Date) dataMap.get("UCKDDT");

					String cname = Util.trim(dataMap.get("CNAME"));
					String ename = Util.trim(dataMap.get("ENAME"));

					if ("".equals(cname)) {
						cname = ename;
					}

					// KDLINE = RCKDLINE(覆審週期)
					rckdline = "";
					dataMap.put("RCKDLINE", rckdline);

					// Boolean ndflag = false;
					// // [計算覆審方式 ]判斷 ndflag並且 model="2"
					// ndflag = gfnCtlCaculateDueDate(2, baseDate, dataMap,
					// ndflag);
					// // 應覆審未覆審，開始寫EXCEL------------------------------
					// if (ndflag) {
					//
					// }
					list1.add(dataMap);
				}
			}
		}
		this.createExcel1(brNo, list1, dataDate, listName);
		return "";
	}

	@Override
	public String findType2ByBrNoAndDate(String brNo, String dataDate,
			String listName) throws WriteException, IOException {
		// 不覆審行業對象別
		// BUSCD=
		// "'060000','130300','030000','030100','030200','028510','028520','028530','028540','028550','028560','130100','016411','016412','016413','016414','026411','026412','026413','026414' "
		// 索撈到的資料一次傳回List跑EXCEL迴圈
		List<Map<String, Object>> list2 = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> rows = dwlnquotovService
				.findDW_LNQUOTOV_LaterRetr(brNo);
		if (rows.size() > 0) {
			for (Map<String, Object> dataMap : rows) {
				String custId = Util.trim(dataMap.get("CUST_ID"));
				/*
				 * 這裡是NOTES邏輯 String cstate = Util.trim( dataMap.get("CSTATE"));
				 * brNo = Util.trim(dataMap.get("BR_NO")); String custId =
				 * Util.trim(dataMap.get("CUST_ID")); String dupNo =
				 * Util.trim(dataMap.get("DUPNO")); String cname =
				 * Util.trim(dataMap.get("CNAME")); String canceldt =
				 * Util.trim(dataMap.get("CANCELDT")); List<Map<String,Object>>
				 * custBrNoMap =
				 * dwlnquotovService.findLNF022selByCustIdBrNo(custId, dupNo,
				 * brNo); if(custBrNoMap.size() > 0){ for(Map<String,Object> map
				 * : custBrNoMap){ String normal_fg =
				 * Util.trim(map.get("LNF022_NORMAL_FG")); String ov_fg =
				 * Util.trim(map.get("LNF022_OV_FG") ); String ob_fg =
				 * Util.trim(map.get("LNF022_OB_FG") ); String cd_fg =
				 * Util.trim(map.get("LNF022_CD_FG") ); String tcount =
				 * Util.trim(map.get("TCOUNT") ); cstate = "";
				 * if("0".equals(tcount)){ if("0001-01-01".equals(canceldt) ||
				 * "1/1/1".equals(canceldt) ||"".equals(canceldt)){ canceldt =
				 * CapDate.getCurrentDate("yyyy-mm-dd"); } }else{
				 * if("Y".equals(cd_fg)){ //有打呆 canceldt = "4"; }else
				 * if("Y".equals(ob_fg)){ //有轉催收以後 canceldt = "3"; }else
				 * if("Y".equals(ov_fg)){ //有轉逾期以後 canceldt = "2"; }else
				 * if("Y".equals(normal_fg)){ //全都為正常戶 canceldt = "1"; }else{
				 * //沒有餘額 canceldt = "0"; } canceldt = "0001-01-01"; }
				 * dataMap.put("CANCELDT", canceldt); dataMap.put("CSTATE",
				 * cstate); } }else{ return "取得【"+custId
				 * +" "+cname+"】帳務檔戶況錯誤-DB2"; }
				 */

				if (!"".equals(custId)) {
					String custId412 = custId.length() >= 10 ? custId.substring(0, 10) : custId;
					String dupNo412 = custId.length() >= 11 ? custId.substring(10, 11) : "0";

					// 查詢CUSTDATA資料
					List<Map<String, Object>> rows412 = lms412Service
							.findLms412JoinCustData(custId412, dupNo412, brNo);
					// 如果有此筆客戶資料
					if (rows412.size() > 0) {
						for (Map<String, Object> dataMap412 : rows412) {
							dataMap.put("CNAME",
									Util.trim(dataMap412.get("CNAME")));
							dataMap.put("ENAME",
									Util.trim(dataMap412.get("ENAME")));
							String cstate = Util.trim(dataMap.get("CSTATE"));
							if ("2".equals(cstate) || "3".equals(cstate)
									|| "4".equals(cstate)) {
								// (產生空白EXCEL)
							} else {
								list2.add(dataMap);
							}
						}
					}
				}
			}
		}
		this.createExcel2(brNo, list2, dataDate, listName);
		return "";
	}

	@Override
	public String findType3ByBrNoAndDate(String brNo, String starDate,
			String endDate, String listName) {
		// 起日 前端傳入
		String beDate = starDate + "-01";
		// 迄日 前端傳入
		String enDate = endDate
				+ "-"
				+ CapDate.getDayOfMonth(endDate.split("-")[0],
						endDate.split("-")[1]);

		List<Map<String, Object>> list411 = dwElf411ovsService
				.find411ovsByBranchForReportType3(brNo, beDate, enDate);
		String custId411ovs = null;

		String dupNo411ovs = null;
		// 額度序號
		String cntrno411ovs = null;
		List<Map<String, Object>> list = new LinkedList<Map<String, Object>>();
		int delayCount = 0;
		for (Map<String, Object> dataMap411ovs : list411) {
			Map<String, Object> map = new LinkedHashMap<String, Object>();
			custId411ovs = Util.trim(dataMap411ovs.get("CUST_ID"));
			dupNo411ovs = Util.trim(dataMap411ovs.get("DUPNO"));
			map.put("CNAME", Util.trim(dataMap411ovs.get("CNAME")));
			if (Util.isEmpty(map.get("CNAME"))) {
				map.put("CNAME", Util.trim(dataMap411ovs.get("ENAME")));
			}
			map.put("CUSTID", custId411ovs);
			map.put("DUPNO", dupNo411ovs);
			// 額度序號
			cntrno411ovs = Util.trim(dataMap411ovs.get("CNTRNO"));
			map.put("CNTRNO", cntrno411ovs);
			map.put("BRANCHID", Util.trim(dataMap411ovs.get("BR_NO")));
			map.put("FACT_CURR", Util.trim(dataMap411ovs.get("FACT_CURR")));
			map.put("FACT_AMT", Util.trim(dataMap411ovs.get("FACT_AMT")));
			// 授信期間起止日 Date or 動用起止日 Date
			if ("".equals(Util.trim(dataMap411ovs.get("LN_INIT_DT")))
					|| "".equals(Util.trim(dataMap411ovs.get("LN_DUE_DT")))) {
				map.put("STARTDT", Util.trim(dataMap411ovs.get("LN_INIT_DT")));
				map.put("ENDDT", Util.trim(dataMap411ovs.get("LN_DUE_DT")));
			} else {
				map.put("STARTDT", Util.trim(dataMap411ovs.get("USE_INIT_DT")));
				map.put("ENDDT", Util.trim(dataMap411ovs.get("USE_DUE_DT")));
			}
			// 異常通報代碼
			map.put("MDFLAG", Util.trim(dataMap411ovs.get("MDFLAG")));
			// 不覆審備註
			map.put("NCKDMEMO", Util.trim(dataMap411ovs.get("NCKDMEMO")));
			// 其他備註
			map.put("NCKDMEMO", Util.trim(dataMap411ovs.get("NCKDMEMO")));
			// 新作 / 增額
			map.put("NEWADD", Util.trim(dataMap411ovs.get("NEWADD")));
			Date date = Util.isEmpty(dataMap411ovs.get("CYC_MN")) ? null
					: (Date) dataMap411ovs.get("CYC_MN");

			map.put("RETRIALDATE", "");
			if (date == null) {
				map.put("CYC_MN", "");

			} else {
				map.put("CYC_MN", Util.trim(TWNDate.toAD(date)));
				Calendar cal1 = Calendar.getInstance();
				cal1.setTime(date);
				cal1.add(Calendar.MONTH, 6);
				cal1.set(Calendar.DATE, 1);
				Calendar cal2 = Calendar.getInstance();
				cal2.set(Calendar.DATE, 1);
				// 驗證是否逾期
				Map<String, Object> l170m01Map = eloandbBaseService
						.getL170M01AEData(custId411ovs, dupNo411ovs, date);
				if (l170m01Map == null) {
					if(Integer.parseInt(TWNDate.toAD(cal1.getTime()).replace("-", "")) >= 
						Integer.parseInt(TWNDate.toAD(cal2.getTime()).replace("-", ""))){
						map.put("DELAY", "");
					} else {
						delayCount++;
						map.put("DELAY", "V");
					}
				} else {
					Date retrialDate = (Date) l170m01Map.get("RETRIALDATE");
					if (retrialDate == null) {
						if(Integer.parseInt(TWNDate.toAD(cal1.getTime()).replace("-", "")) >= 
							Integer.parseInt(TWNDate.toAD(cal2.getTime()).replace("-", ""))){
							map.put("DELAY", "");
						} else {
							delayCount++;
							map.put("DELAY", "V");
						}
					} else {
						Calendar cal3 = Calendar.getInstance();
						cal3.setTime(retrialDate);
						cal3.set(Calendar.DATE, 1);
						if(Integer.parseInt(TWNDate.toAD(cal1.getTime()).replace("-", "")) >= 
							Integer.parseInt(TWNDate.toAD(cal3.getTime()).replace("-", ""))){
							map.put("DELAY", "");
						} else {
							delayCount++;
							map.put("DELAY", "V");
						}
						map.put("RETRIALDATE", TWNDate.toAD(retrialDate));
					}
				}
				list.add(map);
			}

			// 資料日期
			// Date cycmn411ovs = (Date) dataMap411ovs.get("CYC_MN");
			// // 新作 / 增額
			// String newadd411ovs = Util.trim(dataMap411ovs.get("NEWADD"));

			// TODO: 找DW資料 海外 欄位不明
			// Map collectionkMapDW = findDwaslnquotData(custId411ovs,
			// dupNo411ovs, brNo);

			// String contractDw = Util.trim((String)
			// collectionkMapDW.get("CONTRACT"));
			// LNF022_CONTRACT = result.GetValue("LNF022_CONTRACT")
			// LNF022_DEP_CODE = result,.GetValue("LNF022_DEP_CODE")
			// LNF022_DEP_SUB_CD = result.GetValue("LNF022_DEP_SUB_CD")

			// 第一次查詢
			// inList = this.findL170m01bAndL180m01b(custId411ovs,dupNo411ovs,
			// cntrno411ovs, cycmn411ovs, true);
			// for(Map<String, Object> dataMap170180a : inList){
			// String custid170 = Util
			// .trim(dataMap170180a.get("CUSTID"));
			// // 100.07.11
			// // 郭慧珠:因舊覆審條件系統上線之初常常修改，導致覆審報告表額度明細表有未顯示該筆額度之狀況
			// // 故100.7月前(含)之額度序號，只要有覆審，就當作該額度序號有一併覆審
			// // 改用統編來查
			// if ("".equals(custid170)) {
			// // 第二次查詢
			// inList = this.findL170m01bAndL180m01b(
			// custId411ovs, dupNo411ovs,
			// cntrno411ovs, cycmn411ovs, false);
			//
			// }// if END 第二次查詢
			// }
		}// it END
		this.createExcel3(brNo, starDate, endDate, beDate, enDate, list,
				listName, delayCount);
		return "";
	}

	/**
	 * 覆審管理報表)-Type3-Find(L170M01B AND L180M01B)ByPK=(411.dataMap,BrNo) Step-2
	 * 
	 * @param dataMap
	 * @param brNo
	 */
	// private List<Map<String, Object>> findL170m01bAndL180m01b(
	// String custId411ovs, String dupNo411ovs, String cntrno411ovs,
	// Date cycmn411ovs, boolean result) {
	//
	// List<Map<String, Object>> rows = null;
	// if (result) {
	// // 411ovs查L170m01bAndL180m01b資料By 額度序號
	// rows = eloandbBaseService.find170And180ByCntrno411ovs(custId411ovs,
	// dupNo411ovs, cntrno411ovs, cycmn411ovs);
	// } else {
	// // 411ovs查L170m01bAndL180m01b資料By 統一編號
	// rows = eloandbBaseService.find170And180ByCustId411ovs(custId411ovs,
	// dupNo411ovs, cycmn411ovs);
	// }// elseEnd
	// return rows;
	// }

	/**
	 * 覆審管理報表)-Type4-Find(LMS412)ByPK=(411.custId ,411.dupNO,BrNo) Step-3
	 * 
	 * @param custId411
	 * @param dupNo411
	 * @param brNo
	 * @return
	 * @throws IOException
	 * @throws WriteException
	 */
	// private Map findDwaslnquotData(String custId411, String dupNo411,
	// String brNo) {
	// Map<String,Object> dataMapDw =
	// dwdbService.findDwaslnquotByBranchForReportType3(
	// custId411, dupNo411, brNo);
	//
	// Map collectionkMap = new HashMap();
	//
	// // NCKDMEMO,MEMO,NCKDFLAG
	// if (dataMapDw != null) {
	// // TODO:
	// }
	//
	// return collectionkMap;
	//
	// }

	@SuppressWarnings("unused")
	@Override
	public String findType4ByBrNoAndDate(String brNo, String dataDate,
			String listName) throws WriteException, IOException {
		Map<String, Object> collectionkMap = null;
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1855V01Page.class);
		List<Map<String, Object>> list4 = new ArrayList<Map<String, Object>>();
		// 當日日期
		Date tdate = new Date();
		// Date轉字串
		String tdateStr = transportStr(tdate);

		Date mddt = null;
		StringBuffer str = new StringBuffer();
		// 從LMS412撈此筆BRNO的資料
		List<Map<String, Object>> rows412 = lms412Service
				.findLMS412ByBranchForReportType1(brNo);
		Map<String, String> crdType2Map = codetypeService
				.findByCodeType("lms1705s01_crdType2");
		if (crdType2Map == null) {
			crdType2Map = new LinkedHashMap<String, String>();
		}
		int delayCount = 0;
		for (Map<String, Object> dataMap : rows412) {
			collectionkMap = new HashMap<String, Object>();
			String process = "";
			String overdueMemo = "";
			String memo = "";
			String rckdline = "";
			String retrialDate = "";
			String elfLRDate = "";
			String elfLLRDate = "";
			String elfmowtype = "";
			String elfmowtbl1 = "";
			// String lms180Cname = "";
			String elfUckdLine = "";
			String elfMaincust = "";
			String elfCrdttbl = "";
			String elfRckdline = "";
			String elfMdFlag = "";
			String elfMddt = "";
			String elfnewAdd = "";
			String elfNewDate = "";
			String elfUckddt = "";
			String overFlag = "";
			String custId412 = Util.trim(dataMap.get("CUSTID"));
			String dupNo412 = Util.trim(dataMap.get("DUPNO"));
			String cname = Util.trim(dataMap.get("CNAME"));
			String ename = Util.trim(dataMap.get("ENAME"));
			String mainCust412 = Util.trim(dataMap.get("MAINCUST"));
			String crdttbl412 = Util.trim(dataMap.get("CRDTTBL"));
			String lrdate412 = Util.trim(dataMap.get("LRDATE"));
			String rckdline412 = Util.trim(dataMap.get("RCKDLINE"));
			String mdflag412 = Util.trim(dataMap.get("MDFLAG"));

			Date dataDateIn = TWNDate.valueOf(dataDate);
			// if("A123456789".equals(custId412)){
			// System.out.println(1);
			// }


			// Map<String,Object> datamap0851 =
			// this.findMddtAndProcess(custId412, dupNo412, brNo);
			// if (datamap0851.get("MDDT") != null) {
			// mddt = (Date) datamap0851.get("MDDT");
			// process = Util.trim(datamap0851.get("PROCESS"));
			// dataMap.put("MDDT", mddt);
			// dataMap.put("PROCESS", process);
			// } else {
			// mddt = Util.trim(dataMap.get("MDDT"));
			// process = Util.trim(dataMap.get("PROCESS"));
			//
			// }
			mddt = (Date) dataMap.get("MDDT");
			process = Util.trim(dataMap.get("PROCESS"));
			dataMap.put("MDDT", mddt);
			dataMap.put("PROCESS", process);

			String newadd412 = Util.trim(dataMap.get("NEWADD"));
			String newdate412 = Util.trim(dataMap.get("NEWDATE"));

			if ("".equals(cname)) {
				cname = ename;
			}// END if

			String[] returnVal = null; 

			if ("0001-01-01".equals(lrdate412)) {
				lrdate412 = "";
			}
			if ("".equals(lrdate412)) {
				// '沒有最近覆審日，覆審周期預設為半年
				elfRckdline = "B";
				returnVal = lms1805Service.cauculateDate(dataDateIn,dataMap, 3); 
				if (TWNDate.valueOf(returnVal[0]).before(dataDateIn) || Util.isEmpty(elfLRDate)) {
					// EXCEL 列出此欄位 (逾期註記)
					overdueMemo = "Y";
					delayCount++;
				}
			} else {
//				if("20828393".equals(custId412)){
//					System.out.println(1);
//				}
				// 從L180m01bAndL170m01a撈此筆412_BRNO的資料
				Map<String, Object> dataLRMap = this.findlrDatellrDate(
						custId412, dupNo412, brNo);
				// 上次覆審日
				elfLRDate = TWNDate.toAD((Date) dataLRMap.get("LRDATE"));
				// 上上次覆審日
				elfLLRDate = TWNDate.toAD((Date) dataLRMap.get("LLRDATE"));
				
				elfmowtype = Util.trim(dataLRMap.get("ELFMOWTYPE"));
				elfmowtbl1 = Util.trim(dataLRMap.get("ELFMOWTBL1"));
				// lms180Cname = Util.trim(datamap180and170.get("elfCName180"));
				elfMaincust = Util.trim(dataLRMap.get("ELFMAINCUST"));
				elfCrdttbl = Util.trim(dataLRMap.get("ELFCRDTTBL"));
				elfRckdline = Util.trim(dataLRMap.get("ELFRCKDLINE"));
				elfMdFlag = Util.trim(dataLRMap.get("ELFMDFLAG"));
				elfMddt = Util.trim(dataLRMap.get("ELFMDDT"));
				elfnewAdd = Util.trim(dataLRMap.get("ELFNEWADD"));
				elfNewDate = Util.trim(dataLRMap.get("ELFNEWDATE"));
				elfUckdLine = Util.trim(dataLRMap.get("ELFUCKDLINE"));
				elfUckddt = Util.trim(dataLRMap.get("ELFUCKDDT"));
				overFlag =  Util.trim(dataLRMap.get("GOODBYE"));
				
				if(!"Y".equals(overFlag)){
					if(Util.isNotEmpty(elfLRDate) && Util.isNotEmpty(elfLLRDate) ){
						Calendar tmpDate = null;
						Calendar tmp2Date = null;
						tmpDate = Calendar.getInstance();
						tmpDate.setTime(TWNDate.valueOf(elfLRDate));
						tmp2Date = Calendar.getInstance();
						tmp2Date.setTime(TWNDate.valueOf(elfLLRDate));
						
						int monthDiff = Integer.parseInt(TWNDate.toAD(tmp2Date.getTime()).substring(0,7).replace("-", "")) - Integer.parseInt(TWNDate.toAD(tmpDate.getTime()).substring(0,7).replace("-", ""));
						if(Util.isNotEmpty(elfRckdline)){
							if("A".equals(elfRckdline)){
								if(monthDiff > 12){
									overdueMemo = "Y";
								}
							}else if("B".equals(elfRckdline) || 
									"C".equals(elfRckdline) || 
									"D".equals(elfRckdline) || 
									"F".equals(elfRckdline) || 
									"H".equals(elfRckdline)
							){
								if(monthDiff > 6){
									overdueMemo = "Y";
								}
							}else if("E".equals(elfRckdline) || 
									"G".equals(elfRckdline)
							){
								if(monthDiff > 3){
									overdueMemo = "Y";
								}
							}
						}else{
							overdueMemo = "Y";
						}
						
						//特殊排除
						if("Y".equals(overdueMemo)){
							if(Util.isNotEmpty(elfnewAdd) && "C".equals(elfRckdline)){
								if(Util.isNotEmpty(elfNewDate)){
									tmp2Date.setTime(TWNDate.valueOf(elfNewDate));
									monthDiff = Integer.parseInt(TWNDate.toAD(tmp2Date.getTime()).substring(0,7).replace("-", "")) - Integer.parseInt(TWNDate.toAD(tmpDate.getTime()).substring(0,7).replace("-", ""));
									if(monthDiff <= 6){
										overdueMemo = "";
									}
								}
							}else if(Util.isNotEmpty(elfMdFlag) && ("E".equals(elfRckdline) || "G".equals(elfRckdline))){
								if(Util.isNotEmpty(elfMddt)){
									tmp2Date.setTime(TWNDate.valueOf(elfMddt));
									monthDiff = Integer.parseInt(TWNDate.toAD(tmp2Date.getTime()).substring(0,7).replace("-", "")) - Integer.parseInt(TWNDate.toAD(tmpDate.getTime()).substring(0,7).replace("-", ""));
									if(monthDiff <= 3){
										overdueMemo = "";
									}
								}
							}else if(Util.isNotEmpty(elfUckdLine) && "H".equals(elfRckdline)){
								if(Util.isNotEmpty(elfUckddt)){
									tmp2Date.setTime(TWNDate.valueOf(elfUckddt));
									monthDiff = Integer.parseInt(TWNDate.toAD(tmp2Date.getTime()).substring(0,7).replace("-", "")) - Integer.parseInt(TWNDate.toAD(tmpDate.getTime()).substring(0,7).replace("-", ""));
									if(monthDiff <= 6){
										overdueMemo = "";
									}
								}
							}
						}
						
					}else if(Util.isNotEmpty(elfLRDate) && Util.isEmpty(elfLLRDate) ){
						Calendar tmpDate = null;
						Calendar tmp2Date = null;
						tmpDate = Calendar.getInstance();
						tmpDate.setTime(TWNDate.valueOf(elfLRDate));
						tmp2Date = Calendar.getInstance();
						int monthDiff = Integer.parseInt(TWNDate.toAD(tmp2Date.getTime()).substring(0,7).replace("-", "")) - Integer.parseInt(TWNDate.toAD(tmpDate.getTime()).substring(0,7).replace("-", ""));
						if(Util.isNotEmpty(elfRckdline)){
							if("A".equals(elfRckdline)){
								if(monthDiff > 12){
									overdueMemo = "Y";
								}
							}else if("B".equals(elfRckdline) || 
									"C".equals(elfRckdline) || 
									"D".equals(elfRckdline) || 
									"F".equals(elfRckdline) || 
									"H".equals(elfRckdline)
							){
								if(monthDiff > 6){
									overdueMemo = "Y";
								}
							}else if("E".equals(elfRckdline) || 
									"G".equals(elfRckdline)
							){
								if(monthDiff > 3){
									overdueMemo = "Y";
								}
							}
						}else{
							overdueMemo = "Y";
						}
						//特殊排除
						if("Y".equals(overdueMemo)){
							if(Util.isNotEmpty(elfnewAdd) && "C".equals(elfRckdline)){
								if(Util.isNotEmpty(elfNewDate)){
									tmp2Date.setTime(TWNDate.valueOf(elfNewDate));
									monthDiff = Integer.parseInt(TWNDate.toAD(tmp2Date.getTime()).substring(0,7).replace("-", "")) - Integer.parseInt(TWNDate.toAD(tmpDate.getTime()).substring(0,7).replace("-", ""));
									if(monthDiff <= 6){
										overdueMemo = "";
									}
								}
							}else if(Util.isNotEmpty(elfMdFlag) && ("E".equals(elfRckdline) || "G".equals(elfRckdline))){
								if(Util.isNotEmpty(elfMddt)){
									tmp2Date.setTime(TWNDate.valueOf(elfMddt));
									monthDiff = Integer.parseInt(TWNDate.toAD(tmp2Date.getTime()).substring(0,7).replace("-", "")) - Integer.parseInt(TWNDate.toAD(tmpDate.getTime()).substring(0,7).replace("-", ""));
									if(monthDiff <= 3){
										overdueMemo = "";
									}
								}
							}else if(Util.isNotEmpty(elfUckdLine) && "H".equals(elfRckdline)){
								if(Util.isNotEmpty(elfUckddt)){
									tmp2Date.setTime(TWNDate.valueOf(elfUckddt));
									monthDiff = Integer.parseInt(TWNDate.toAD(tmp2Date.getTime()).substring(0,7).replace("-", "")) - Integer.parseInt(TWNDate.toAD(tmpDate.getTime()).substring(0,7).replace("-", ""));
									if(monthDiff <= 6){
										overdueMemo = "";
									}
								}
							}
						}
						
						
						
					}
					
					
					
//					Map<String,Object> calMap = new LinkedHashMap<String,Object>();
//					calMap.put("LRDATE", Util.isNotEmpty(elfLRDate) ? TWNDate.valueOf(elfLRDate) : null );
//					calMap.put("NEWDATE", elfNewDate);
//					calMap.put("MDFLAG", elfMdFlag);
//					System.out.println(elfMddt);
//					calMap.put("MDDT", Util.isNotEmpty(elfMddt) ? TWNDate.valueOf(elfMddt) : null);
//					System.out.println(TWNDate.toAD(TWNDate.valueOf(elfMddt)));
//					calMap.put("RCKDLINE", elfRckdline);
//					calMap.put("UCKDLINE", Util.trim(dataLRMap.get("ELFUCKDLINE")));
//					calMap.put("UCKDDT", Util.isNotEmpty(elfUckddt) ? TWNDate.valueOf(elfUckddt) : null);
//					calMap.put("MAINCUST", Util.trim(dataLRMap.get("ELFMAINCUST")));
//					calMap.put("MOWTBL1", Util.trim(dataLRMap.get("ELFMOWTBL1")));
//					calMap.put("MOWTYPE", Util.trim(dataLRMap.get("ELFMOWTYPE")));
//					calMap.put("CRDTTBL", Util.trim(dataLRMap.get("ELFCRDTTBL")));
//					calMap.put("NCKDFLAG", Util.trim(dataLRMap.get("ELFNCKDFLAG")));
//
//					returnVal = lms1805Service.cauculateDate(dataDateIn,
//							calMap, 3); 
				}else{
					if(Util.isNotEmpty(newadd412)){
						elfRckdline = "C";
						if(Util.isNotEmpty(newdate412)){
							String tempNewDate412 = newdate412.substring(0,4) + "-" + newdate412.substring(4,6) + "-01"; 
							Calendar tmpDate = null;
							Calendar nowDate = null;
							tmpDate = Calendar.getInstance();
							System.out.println(tempNewDate412);
							tmpDate.setTime(TWNDate.valueOf(tempNewDate412));
							nowDate = Calendar.getInstance();
							
							int monthDiff = Integer.parseInt(TWNDate.toAD(nowDate.getTime()).substring(0,7).replace("-", "")) - Integer.parseInt(TWNDate.toAD(tmpDate.getTime()).substring(0,7).replace("-", ""));
							
							if(monthDiff > 6){
								overdueMemo = "Y";
							}
						}else{
							overdueMemo = "Y";
						}
					}else{
						overdueMemo = "Y";
					}
				}
				
				
				// =================================資料判斷======================
				if (!"".equals(elfMdFlag)) {
					memo = memo + prop.getProperty("type4xls.content05")
							+ "：" + (Util.isNotEmpty(elfMddt) && elfMddt.length() >=7 ? elfMddt.substring(0, 7) : "");
				}// END if

				if (memo.length() > 0) {
					memo = memo + "、";
				}
				if ("N".equals(elfnewAdd)) {
					memo = memo + prop.getProperty("type4xls.content06")+ "：" + (Util.isNotEmpty(elfNewDate) && elfNewDate.length() >=6 ? elfNewDate.substring(0, 4) + "-" + elfNewDate.substring(4, 6) : "");
				} else {
					memo = memo + prop.getProperty("type4xls.content07")+ "：";
				}// END if
				
				
				// 最近一次覆審日期
				// retrialDate = TWNDate.toAD((Date)
				// dataLRMap.get("RETRIALDATE"));

				// if (Util.isEmpty(elfLRDate)
				// || "0001-01-01".equals(elfLRDate)) {
				// elfLRDate = "";
				// }// END if
				//
				// if (Util.isEmpty(elfLLRDate)
				// || "0001-01-01".equals(elfLLRDate)) {
				// elfLLRDate = "";
				// }// END if
//				 if (Util.isEmpty(elfLLRDate) && Util.isNotEmpty(elfLRDate)) {
//				 
//				 }// END if
				// =================================資料判斷========================
//				if("Y".equals(overFlag)){
//					
//				}else if (Util.isEmpty(elfLLRDate) && Util.isEmpty(elfLRDate)) {
//
//				} else {
//					// =================================資料判斷======================
//					if (!"".equals(elfMdFlag)) {
//						memo = memo + prop.getProperty("type4xls.content05")
//								+ "：" + (Util.isNotEmpty(elfMddt) && elfMddt.length() >=7 ? elfMddt.substring(0, 7) : "");
//					}// END if
//
//					if (memo.length() > 0) {
//						memo = memo + "、";
//					}
//					if ("N".equals(elfnewAdd)) {
//						memo = memo + prop.getProperty("type4xls.content06")+ "：" + (Util.isNotEmpty(elfNewDate) && elfNewDate.length() >=6 ? elfNewDate.substring(0, 4) + "-" + elfNewDate.substring(4, 6) : "");
//					} else {
//						memo = memo + prop.getProperty("type4xls.content07")+ "：";
//					}// END if
//
////					if (!"".equals(newadd412)) {
////						if (memo.length() > 0) {
////							memo = memo + "、";
////						}
////						if ("N".equals(newadd412)) {
////							memo = memo
////									+ prop.getProperty("type4xls.content06")
////									+ "：";
////						} else {
////							memo = memo
////									+ prop.getProperty("type4xls.content07")
////									+ "：";
////						}
////					}// END if
//
//				}

			}

			//逾期FLAG
			if("Y".equals(overdueMemo)){
				delayCount++;
			}
//				
//			}else if("Y".equals(overFlag)){
//				// EXCEL 列出此欄位 (逾期註記)
//				overdueMemo = "Y";
//				delayCount++;
//			}else if (TWNDate.valueOf(returnVal[0]).before(dataDateIn)
//					|| Util.isEmpty(elfLRDate)) {
//				// EXCEL 列出此欄位 (逾期註記)
//				overdueMemo = "Y";
//				delayCount++;
//			} else {	
//				/*
//				 * A.一年覆審一次。 12 B.半年覆審一次(主要授信戶並符合信評條件)。6 C.新戶/增額戶。6
//				 * D.異常戶已三個月覆審過- 爾後半年覆審一次。6 E.首次通報之異常戶。（必需在首次通報日後3月內覆審）3
//				 * F.會計師出具保留意見已三個月覆審過- 爾後半年覆審一次。6
//				 * G.首次通報有會計師出具保留意見之異常戶。（必需在首次通報日後3月內覆審）3 H.主管機關指定覆審案件。 6
//				 */
//				Calendar llrDate = null;
//				Calendar lrDate = null;
//				if (Util.isNotEmpty(elfLLRDate)) {
//					llrDate = Calendar.getInstance();
//					llrDate.setTime(TWNDate.valueOf(elfLLRDate));
//				}
//				if (Util.isNotEmpty(elfLRDate)) {
//					lrDate = Calendar.getInstance();
//					lrDate.setTime(TWNDate.valueOf(elfLRDate));
//				}
//				if (lrDate != null && llrDate == null) {
//					overdueMemo = "";
//				} else if (lrDate == null && llrDate != null) {
//					overdueMemo = "Y";
//					delayCount++;
//				} else if (lrDate == null && llrDate == null) {
//					overdueMemo = "";
//				} else if (llrDate != null && lrDate != null) {
//					if ("A".equals(elfRckdline)) {
//						llrDate.add(Calendar.YEAR, 1);
//					} else if ("E".equals(elfRckdline)
//							|| "G".equals(elfRckdline)) {
//						llrDate.add(Calendar.MONTH, 3);
//					} else if ("".equals(elfRckdline)) {
//						overdueMemo = "Y";
//						delayCount++;
//					} else {
//						llrDate.add(Calendar.MONTH, 6);
//					}
//					if (llrDate.before(lrDate)) {
//						overdueMemo = "Y";
//						delayCount++;
//					} else {
//						overdueMemo = "";
//					}
//				} else {
//					overdueMemo = "Y";
//					delayCount++;
//				}
//			}
			str.setLength(0);
			if (Util.isNotEmpty(elfCrdttbl)) {
				str.append(prop.getProperty("crdTypeDefault")).append(":").append(
						elfCrdttbl);
			}
			if (Util.isNotEmpty(elfmowtbl1)) {
				if (str.length() > 0) {
					str.append("\n");
				}
				str.append(Util.trim(crdType2Map.get(elfmowtype))).append(":").append(
						elfmowtbl1);
			}
			//如果有lr卻沒有Llr的時候 要把前次複審資料清空
//			if (Util.isEmpty(elfLLRDate)){
//				elfMaincust = "";
//				str.setLength(0);
//				elfRckdline = "";
//			}

			collectionkMap.put("line1", custId412); // 客戶統編
			// collectionkMap.put("line2", "".equals(lms180Cname) ? cname :
			// lms180Cname); // 戶名
			collectionkMap.put("line2", cname); // 戶名
			
			collectionkMap.put("line3", Util.trim(elfMaincust)); // 主要戶
			collectionkMap.put("line4", str.toString()); // 信用評等
			collectionkMap.put("line5", Util.trim(elfRckdline)); // 覆審週期
			collectionkMap.put("line6", elfLLRDate); // 前次覆審日

			if (Util.isEmpty(elfLRDate)) {
				collectionkMap.put("line7",
						prop.getProperty("type3xls.content17")); // 最近一次覆審日期
			} else {
				collectionkMap.put("line7", elfLRDate); // 最近一次覆審日期
			}

			collectionkMap.put("line8", ""); // DBU/OBU共用額度
			collectionkMap.put("line9", overdueMemo); // 逾期註記
			collectionkMap.put("line10", memo); // 備註
			list4.add(collectionkMap);
		}
		this.createExcel4(brNo, dataDate, list4, listName, delayCount);
		return "";
	}

	// ==========日期格式化===========================================================

	/**
	 * 格式轉換(yyyy-MM)
	 * 
	 * @param data
	 * @return
	 */
	private String transportStr(Date data) {
		String[] temp = TWNDate.toAD(data).split("-");
		return temp[0] + "-" + temp[1];
	}

	/**
	 * <pre>
	 * 日期比較大小
	 * 
	 * @param data1 (當天的日期)
	 * @param data2(被比較大小日期)
	 * @return
	 * </pre>
	 */

	// private Boolean dateTransport(Date data1, String data2, int i) {
	//
	// boolean tranMillisecond = false;
	//
	// Date date2;
	//
	// // 增加幾個月
	// data2 = CapDate.addMonth(data2, i);
	// // 轉換成DATE型態
	// date2 = CapDate.parseDate(data2);
	//
	// long da1Int = data1.getTime();
	// long da2Int = date2.getTime();
	//
	// long time = da1Int - da2Int;
	// if (time > 60) {
	// // 如果有大一天
	// tranMillisecond = true;
	// }
	// return tranMillisecond;
	// }

	// =======(覆審管理報表)-Type1 (anotherFunction)================================

	/**
	 * <PRE>
	 * (覆審管理報表)-Type1
	 *               判斷 ndflag = true||false
	 * 				 mode = 1 產生本月覆審名單 gfnStartNorthBranch_GenList
	 * 				 mode = 2 產生未覆審名單 gfnGenerateCTL_FLMS180R01 (覆審管理報表-TYPE1)
	 * 				 mode = 3 維護覆審名單
	 * </pre>
	 * 
	 * @param mode
	 * @param tbaseDate
	 * @param dataMap
	 * @param ndflag
	 * @return
	 */
	// private Boolean gfnCtlCaculateDueDate(int mode, Date tbaseDate,
	// Map<String, Object> dataMap, Boolean ndflag) {
	//
	// String[] data = lms1805Service.cauculateDate(tbaseDate, dataMap, mode);
	// String nddate = data[0];
	// if (!("9999-12-31".equals(nddate))) {
	// // NDDATE = NDDATE
	// ndflag = true;
	// } else {
	// data[0] = "";
	// ndflag = false;
	//
	// }
	// return ndflag;
	// }

	/**
	 * <PRE>
	 * (覆審管理報表)-Type1-MIS.LNFE0851找 MDDT及PROCESS
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 * 
	 * <PRE>
	 */
	// private Map<String, Object> findMddtAndProcess(String custId, String
	// dupNo,
	// String brNo) {
	// List<Map<String, Object>> row = misLnfe0851Service
	// .findLNFE0851ByCustId(custId, dupNo, brNo);
	// Map<String, Object> collectionkMap = new HashMap<String, Object>();
	// for (Map<String, Object> dataMap : row) {
	// collectionkMap.put("MDDT", (Date) dataMap.get("MDDT"));
	// collectionkMap.put("PROCESS", Util.trim(dataMap.get("PROCESS")));
	// }
	// return collectionkMap;
	// }

	/**
	 * (覆審管理報表)-Type1-和企金重新引進授信資料SQL同-- 重新產生授信資料
	 * 
	 * @param brNo
	 * @param custId
	 * @param mainId
	 * @param dupNo
	 */

	private List<L170M01B> findCreditdata(String brNo, String custId,
			String dupNo) {
		List<Map<String, Object>> rows = dwdbService
				.findDW_ASLNDAVGOVSJOIN_ByBrNoCustId(brNo, custId, dupNo);
		List<L170M01B> list = new LinkedList<L170M01B>();
		// 會計科目對應回授信科目(代碼)
		Map<String, String> subItemToAccountingMap = null;
		Map<String, String> accountIngTosubItemMap = new LinkedHashMap<String, String>();
		subItemToAccountingMap = codetypeService
				.findByCodeType("lms1405m01_SubItemToAccounting");
		if (subItemToAccountingMap == null) {
			subItemToAccountingMap = new LinkedHashMap<String, String>();
		}
		for (String key : subItemToAccountingMap.keySet()) {
			accountIngTosubItemMap.put(subItemToAccountingMap.get(key), key);
		}
		for (Map<String, Object> dataMap : rows) {
			L170M01B l170m01b = new L170M01B();
			String contract = Util.trim((dataMap.get("CONTRACT")));
			String custKey = Util.trim((dataMap.get("cust_key")));
			String acctKey = Util.trim((dataMap.get("ACCT_KEY")));
			// DW 會計科目
			String glacKey = Util.trim((dataMap.get("GL_AC_KEY")));
			// DURING_BG 授信起日(DWADM.DW_LNQUOTOV)
			Date duringBg = (Date) dataMap.get("DURING_BG");
			// DURING_ED 授信迄日(DWADM.DW_LNQUOTOV)
			Date duringEd = (Date) dataMap.get("DURING_ED");
			// FACT_AMT_NT 額度金額(DW_ASLNDAVGOVS)
			BigDecimal factAmtNt = LMSUtil
					.toBigDecimal(dataMap.get("FACT_AMT"));
			String factAmtNtCurr = Util.trim(dataMap.get("FACT_SWFT"));
			BigDecimal lnBal = LMSUtil.toBigDecimal(dataMap.get("LN_BAL"));
			String lnBalCurr = Util.trim(dataMap.get("CUR_CD"));
			l170m01b.setCustId(custKey);
			l170m01b.setCntrNo(contract);
			// l170m01b.setMainId(mainId);
			l170m01b.setDupNo(dupNo);
			l170m01b.setLoanNo(acctKey);
			l170m01b.setSubject(Util.trim(accountIngTosubItemMap.get(glacKey)));
			l170m01b.setFromDate(duringBg);
			l170m01b.setEndDate(duringEd);
			l170m01b.setQuotaAmt(factAmtNt);
			l170m01b.setBalAmt(lnBal);
			l170m01b.setBalCurr(lnBalCurr);
			l170m01b.setQuotaCurr(factAmtNtCurr);
			Map<String, Object> cntrnoMap = misLms422Service
					.findLMS422ByCntrNo(custId, dupNo, contract, brNo);
			if (cntrnoMap != null) {
				String newCase = Util.trim((cntrnoMap.get("NEWCASE")));
				String appDatetemp = Util.trim((cntrnoMap.get("APPDATE")));

				// 如果此筆額度序號的APPDATE大於一年以上,則新貸設為"N";
				Date date = new Date();
				Date appDate = Util.parseDate(appDatetemp);
				long i = date.getTime() - appDate.getTime();
				long j = i / 1000 / 60 / 60 / 24;
				if (newCase != null) {
					if (j > 360) {
						l170m01b.setNewCase(newCase);
					} else {
						newCase = "N";
						l170m01b.setNewCase(newCase);
					}
				}
				l170m01b.setNewCase(newCase);
			}
			list.add(l170m01b);

		}
		return list;

	}

	/**
	 * 設定type1 xls的title
	 * 
	 * @param sheet
	 * @param format14Center
	 *            字型
	 * @return
	 * @throws RowsExceededException
	 * @throws WriteException
	 */
	private WritableSheet setType1Title(WritableSheet sheet,
			WritableCellFormat format14Center, Properties prop)
			throws RowsExceededException, WriteException {
		// sheet.setRowView(4, 350);
		sheet.setColumnView(0, 4);
		sheet.setColumnView(1, 16);
		sheet.setColumnView(2, 14);
		sheet.setColumnView(3, 12);
		sheet.setColumnView(4, 13);
		sheet.setColumnView(5, 16);
		sheet.setColumnView(6, 16);
		sheet.setColumnView(7, 19);
		sheet.setColumnView(8, 8);
		sheet.setColumnView(9, 15);
		sheet.setColumnView(10, 9);
		sheet.setColumnView(11, 9);
		sheet.setColumnView(12, 20);
		sheet.addCell(new Label(0, 4, "", format14Center));
		sheet.addCell(new Label(1, 4,
				prop.getProperty("L185M01a.Type1Title01"), format14Center));
		sheet.addCell(new Label(2, 4,
				prop.getProperty("L185M01a.Type1Title02"), format14Center));
		sheet.addCell(new Label(3, 4,
				prop.getProperty("L185M01a.Type1Title03"), format14Center));
		sheet.addCell(new Label(4, 4,
				prop.getProperty("L185M01a.Type1Title04"), format14Center));
		sheet.addCell(new Label(5, 4,
				prop.getProperty("L185M01a.Type1Title05"), format14Center));
		sheet.addCell(new Label(6, 4,
				prop.getProperty("L185M01a.Type1Title06"), format14Center));
		sheet.addCell(new Label(7, 4,
				prop.getProperty("L185M01a.Type1Title07"), format14Center));
		sheet.addCell(new Label(8, 4,
				prop.getProperty("L185M01a.Type1Title08"), format14Center));
		sheet.addCell(new Label(9, 4,
				prop.getProperty("L185M01a.Type1Title09"), format14Center));
		sheet.addCell(new Label(10, 4, prop
				.getProperty("L185M01a.Type1Title10"), format14Center));
		sheet.addCell(new Label(11, 4, prop
				.getProperty("L185M01a.Type1Title11"), format14Center));
		sheet.addCell(new Label(12, 4, prop
				.getProperty("L185M01a.Type1Title12"), format14Center));
		return sheet;
	}

	/**
	 * 設定type1 xls的title
	 * 
	 * @param sheet
	 * @param format14Center
	 *            字型
	 * @return
	 * @throws RowsExceededException
	 * @throws WriteException
	 */
	private WritableSheet setType1End(WritableSheet sheet,
			WritableCellFormat format10Left, Properties prop, int count)
			throws RowsExceededException, WriteException {
		sheet.addCell(new Label(1, 4 + count + 1, prop
				.getProperty("L185M01a.Type1End01"), format10Left));
		sheet.addCell(new Label(1, 4 + count + 2, prop
				.getProperty("L185M01a.Type1End02"), format10Left));
		sheet.addCell(new Label(1, 4 + count + 3, prop
				.getProperty("L185M01a.Type1End03"), format10Left));
		sheet.addCell(new Label(1, 4 + count + 4, prop
				.getProperty("L185M01a.Type1End04"), format10Left));
		sheet.addCell(new Label(1, 4 + count + 5, prop
				.getProperty("L185M01a.Type1End05"), format10Left));
		sheet.addCell(new Label(1, 4 + count + 6, prop
				.getProperty("L185M01a.Type1End06"), format10Left));
		sheet.addCell(new Label(1, 4 + count + 7, prop
				.getProperty("L185M01a.Type1End07"), format10Left));
		sheet.addCell(new Label(1, 4 + count + 8, prop
				.getProperty("L185M01a.Type1End08"), format10Left));
		sheet.addCell(new Label(1, 4 + count + 9, prop
				.getProperty("L185M01a.Type1End09"), format10Left));
		return sheet;
	}

	/**
	 * 產生企金複審管理報表第一份
	 * 
	 * @param brNo
	 * @param list1
	 * @param ranMainId
	 * @param dataDate
	 * @param listName
	 * @throws WriteException
	 * @throws IOException
	 */
	@SuppressWarnings("deprecation")
	private void createExcel1(String brNo, List<Map<String, Object>> list1,
			String dataDate, String listName) throws WriteException,
			IOException {
		String ranMainId = IDGenerator.getRandomCode();
		DocFile docFile = new DocFile();
		docFile.setBranchId(brNo);
		docFile.setContentType("application/msexcel");
		docFile.setMainId(ranMainId);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId(listName);
		docFile.setSrcFileName(listName + ".xls");
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId(fileService.getSysId());
		docFile.setData(new byte[] {});
		fileService.save(docFile, false);

		String date = dataDate + "-01";
		File file = null;

		String filename = null;
		String xlsOid = null;

		xlsOid = docFile.getOid();
		filename = LMSUtil.getUploadFilePath(brNo, ranMainId, listName);
		file = new File(filename);
		file.mkdirs();

		WritableWorkbook workbook = null;
		WritableSheet sheet = null;
		WritableFont font18 = null;
		WritableFont font16 = null;
		WritableFont font14 = null;
		WritableFont font10 = null;
		WritableFont font09 = null;
		WritableCellFormat format16Left = null;
		WritableCellFormat format18Center = null;
		WritableCellFormat format14Center = null;
		WritableCellFormat format14LeftNB = null;
		WritableCellFormat format09Left = null;
		WritableCellFormat format09Center = null;
		WritableCellFormat format09Right = null;
		WritableCellFormat format10Left = null;
		Label label = null;
		Properties prop = null;
		Map<String, String> mowtypeMap = null;
		Map<String, String> crdtypeMap = null;
		Map<String, String> subItmeMap = null;
		StringBuffer crd = new StringBuffer();
		try {
			subItmeMap = codetypeService.findByCodeType("lms1405m01_SubItem");
			if (subItmeMap == null) {
				subItmeMap = new LinkedHashMap<String, String>();
			}
			mowtypeMap = codetypeService.findByCodeType("lms1705s01_crdType2");
			if (mowtypeMap == null) {
				mowtypeMap = new LinkedHashMap<String, String>();
			}
			crdtypeMap = codetypeService.findByCodeType("lms1705s01_crdType");
			if (crdtypeMap == null) {
				crdtypeMap = new LinkedHashMap<String, String>();
			}
			file = new File(filename + "/" + xlsOid + ".xls");
			prop = MessageBundleScriptCreator
					.getComponentResource(LMS1855V01Page.class);
			workbook = Workbook.createWorkbook(file);

			sheet = workbook.createSheet(
					prop.getProperty("L185M01a.Type1Content01"), 0);
			/*
			 * 1.1方向 SheetSetting#setOrientation(PageOrientation po)； 參數：
			 * PageOrientation#LANDSCAPE 橫向打印 PageOrientation# PORTRAIT 縱向打印 (A)
			 * SheetSetting #setScaleFactor (int);百分比形式
			 */
			SheetSettings settings = sheet.getSettings();
			settings.setOrientation(PageOrientation.LANDSCAPE);
			settings.setScaleFactor(75);
			// 設置字型
			font18 = new WritableFont(WritableFont.createFont("標楷體"), 18,
					WritableFont.BOLD);
			font16 = new WritableFont(WritableFont.createFont("新細明體"), 16,
					WritableFont.BOLD);
			font14 = new WritableFont(WritableFont.createFont("新細明體"), 14,
					WritableFont.BOLD);
			font10 = new WritableFont(WritableFont.createFont("新細明體"), 10,
					WritableFont.BOLD);
			font09 = new WritableFont(WritableFont.createFont("新細明體"), 9,
					WritableFont.NO_BOLD);

			format16Left = LMSUtil.setCellFormat(format16Left, font16,
					Alignment.LEFT, false, false);
			format18Center = LMSUtil.setCellFormat(format18Center, font18,
					Alignment.CENTRE, false, false);
			format14LeftNB = LMSUtil.setCellFormat(format14LeftNB, font14,
					Alignment.LEFT, false, false);
			format14Center = LMSUtil.setCellFormat(format14Center, font14,
					Alignment.CENTRE);
			format09Left = LMSUtil.setCellFormat(format09Left, font09,
					Alignment.LEFT, true, true);
			format09Center = LMSUtil.setCellFormat(format09Center, font09,
					Alignment.CENTRE, true, true);
			format09Right = LMSUtil.setCellFormat(format09Right, font09,
					Alignment.RIGHT, true, true);
			format10Left = LMSUtil.setCellFormat(format10Left, font10,
					Alignment.LEFT, false, false);

			String branchName = Util.trim(branchService.getBranchName(brNo));
			label = new Label(0, 0, Util.trim(brNo) + " " + branchName,
					format16Left);
			sheet.addCell(label);
			// 合併單元格
			sheet.mergeCells(1, 2, 12, 2);
			label = new Label(1, 2,
					prop.getProperty("L185M01a.Type1Content02"), format18Center);
			sheet.addCell(label);
			label = new Label(7, 3, prop.getProperty("L185M01a.Type1Content03")
					+ date, format14LeftNB);
			sheet.addCell(label);
			// 表格標題
			sheet = this.setType1Title(sheet, format09Center, prop);

			int count = 0;
			// 92/06/10 - 李科長指示即使餘額為０亦要顯示出來
			StringBuffer cc = new StringBuffer();
			StringBuffer dd = new StringBuffer();
			StringBuffer ee = new StringBuffer();
			StringBuffer gg = new StringBuffer();
			StringBuffer ff = new StringBuffer();
			for (Map<String, Object> dataMap : list1) {
				String aa = "";
				String bb = "";
				cc.setLength(0);
				dd.setLength(0);
				ee.setLength(0);
				ff.setLength(0);
				gg.setLength(0);
				String hh = "";
				String ii = "";
				String jj = "";
				String kk = "";
				String ll = "";
				crd.setLength(0);
				// 借戶名稱
				String cname = Util.trim(dataMap.get("CNAME"));
				aa = cname;
				// 借戶ID
				String custId = Util.trim(dataMap.get("CUSTID"));
				String dupNo = Util.trim(dataMap.get("DUPNO"));
				bb = custId + dupNo;
				// 引進授信資料
				List<L170M01B> list = this.findCreditdata(brNo, custId, dupNo);
				if (list.size() > 0) {
					for (L170M01B l170m01b : list) {
						// 額度序號
						if (cc.length() > 0) {
							cc.append("\r\n");
						}
						cc.append(Util.trim(l170m01b.getCntrNo()));
						// 授信科目

						if (dd.length() > 0) {
							dd.append("\r\n");
						}
						dd.append(Util.trim(subItmeMap.get(Util.trim(l170m01b.getSubject()))));
						// 額度
						if (ee.length() > 0) {
							ee.append("\r\n");
						}
						ee.append(Util.trim(l170m01b.getQuotaCurr()))
								.append(" ")
								.append(NumConverter.addComma(
										l170m01b.getQuotaAmt(), "#,##0.00"));
						// 餘額
						if (ff.length() > 0) {
							ff.append("\r\n");
						}
						ff.append(Util.trim(l170m01b.getBalCurr()))
								.append(" ")
								.append(NumConverter.addComma(
										l170m01b.getBalAmt(), "#,##0.00"));
						// 授信期間
						if (gg.length() > 0) {
							gg.append("\r\n");
						}
						gg.append(TWNDate.toAD(l170m01b.getFromDate()))
								.append("~")
								.append(TWNDate.toAD(l170m01b.getEndDate()));

					}
				} else {
					// 額度序號
					cc.append("");
					// 授信科目
					dd.append("");
					// 額度
					ee.append("0.00");
					// 餘額
					ff.append("0.00");
					// 授信期間
					// gg = "";
				}
				// 是否為主要授信戶案件
				hh = Util.trim(dataMap.get("MAINCUST"));
				if (Util.isNotEmpty(Util.trim(dataMap.get("CRDTTBL")))) {
					crd.append(prop.getProperty("crdTypeDefault")).append(":");
					crd.append(Util.trim(dataMap.get("CRDTTBL")));
				}

				if (!"".equals(crd)) {
					crd.append("\r\n");
				}
				if (!"".equals(Util.trim(dataMap.get("MOWTYPE")))) {
					crd.append(
							Util.trim(mowtypeMap.get(Util.trim(dataMap
									.get("MOWTYPE"))))).append(":");
				}
				crd.append(Util.trim(dataMap.get("MOWTBL1")));

				ii = Util.trim(crd);
				// 上次覆審日
				jj = TWNDate.toAD((Date) dataMap.get("LRDATE"));

				// 最新一次下次恢復覆審日
				kk = TWNDate.toAD((Date) dataMap.get("NEXTNWDT"));

				ll = Util.trim(dataMap.get("NCKDMEMO"));

				label = new Label(0, count + 5, count + 1 + "", format09Left);
				sheet.addCell(label);

				label = new Label(1, count + 5, aa, format09Left);
				sheet.addCell(label);

				label = new Label(2, count + 5, bb, format09Left);
				sheet.addCell(label);

				label = new Label(3, count + 5, cc.toString(), format09Left);
				sheet.addCell(label);

				label = new Label(4, count + 5, dd.toString(), format09Left);
				sheet.addCell(label);

				label = new Label(5, count + 5, ee.toString(), format09Right);
				sheet.addCell(label);

				label = new Label(6, count + 5, ff.toString(), format09Right);
				sheet.addCell(label);

				label = new Label(7, count + 5, gg.toString(), format09Center);
				sheet.addCell(label);

				label = new Label(8, count + 5, hh, format09Center);
				sheet.addCell(label);

				label = new Label(9, count + 5, ii, format09Left);
				sheet.addCell(label);

				label = new Label(10, count + 5, jj, format09Center);
				sheet.addCell(label);

				label = new Label(11, count + 5, kk, format09Center);
				sheet.addCell(label);

				label = new Label(12, count + 5, ll, format09Left);
				sheet.addCell(label);
				count++;

			}
			// 表格標題
			sheet = this.setType1End(sheet, format10Left, prop, count);
			workbook.write();
			workbook.close();
			Date dataEDate = TWNDate.valueOf(date);
			dataEDate.setDate(CapDate.getDayOfMonth(TWNDate.toAD(dataEDate)
					.split("-")[0], TWNDate.toAD(dataEDate).split("-")[1]));
			this.saveL185M01A01Data(ranMainId, TWNDate.valueOf(date),
					dataEDate, brNo, xlsOid, list1.size(),
					UtilConstants.rRptType.逾期未覆審名單, "4", 0, 0, 0);
		} finally {
			if (workbook != null) {
				workbook = null;
			}
			if (mowtypeMap != null) {
				mowtypeMap.clear();
			}
			if (crdtypeMap != null) {
				crdtypeMap.clear();
			}
			if (subItmeMap != null) {
				subItmeMap.clear();
			}
		}
	}

	/**
	 * 設定type2 xls的title
	 * 
	 * @param sheet
	 * @param format14Center
	 *            字型
	 * @return
	 * @throws RowsExceededException
	 * @throws WriteException
	 */
	private WritableSheet setType2Title(WritableSheet sheet,
			WritableCellFormat format12Center, Properties prop)
			throws RowsExceededException, WriteException {
		sheet.setColumnView(0, 12);
		sheet.setColumnView(1, 15);
		sheet.setColumnView(2, 23);
		sheet.setColumnView(3, 16);
		sheet.setColumnView(4, 10);
		sheet.setColumnView(5, 19);
		sheet.setColumnView(6, 22);
		sheet.setColumnView(7, 22);
		sheet.addCell(new Label(0, 4, prop.getProperty("type2xls.content08"),
				format12Center));
		sheet.addCell(new Label(1, 4, prop.getProperty("type2xls.content09"),
				format12Center));
		sheet.addCell(new Label(2, 4, prop.getProperty("type2xls.content10"),
				format12Center));
		sheet.addCell(new Label(3, 4, prop.getProperty("type2xls.content11"),
				format12Center));
		sheet.addCell(new Label(4, 4, prop.getProperty("type2xls.content12"),
				format12Center));
		sheet.addCell(new Label(5, 4, prop.getProperty("type2xls.content13"),
				format12Center));
		sheet.addCell(new Label(6, 4, prop.getProperty("type2xls.content14"),
				format12Center));
		sheet.addCell(new Label(7, 4, prop.getProperty("type2xls.content15"),
				format12Center));
		return sheet;
	}

	/**
	 * @param brNo
	 * @param list
	 * @param dataDate
	 * @param listName
	 * @throws IOException
	 * @throws WriteException
	 */
	@SuppressWarnings("deprecation")
	private void createExcel2(String brNo, List<Map<String, Object>> list,
			String dataDate, String listName) throws WriteException,
			IOException {

		String ranMainId = IDGenerator.getRandomCode();
		DocFile docFile = new DocFile();
		docFile.setBranchId(brNo);
		docFile.setContentType("application/msexcel");
		docFile.setMainId(ranMainId);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId(listName);
		docFile.setSrcFileName(listName + ".xls");
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId(fileService.getSysId());
		docFile.setData(new byte[] {});
		fileService.save(docFile, false);

		String aa = "";
		String bb = "";
		String cc = "";
		String dd = "";
		String ee = "";
		String ff = "";
		String gg = "";
		String hh = "";

		File file = null;
		String filename = null;
		String xlsOid = null;

		xlsOid = docFile.getOid();
		filename = LMSUtil.getUploadFilePath(brNo, ranMainId, listName);
		file = new File(filename);
		file.mkdirs();

		WritableWorkbook workbook = null;
		WritableSheet sheet = null;
		WritableFont font14 = null;
		WritableFont font12 = null;
		WritableCellFormat format14CenterNB = null;
		WritableCellFormat format12Left = null;
		WritableCellFormat format12LeftNB = null;
		WritableCellFormat format12Center = null;
		WritableCellFormat format12Right = null;
		Label label = null;
		Properties prop = null;
		try {
			file = new File(filename + "/" + xlsOid + ".xls");
			prop = MessageBundleScriptCreator
					.getComponentResource(LMS1855V01Page.class);
			workbook = Workbook.createWorkbook(file);
			sheet = workbook.createSheet(
					prop.getProperty("type2xls.content01"), 0);
			SheetSettings settings = sheet.getSettings();
			settings.setOrientation(PageOrientation.LANDSCAPE);
			settings.setScaleFactor(95);
			// 設置字型
			font14 = new WritableFont(WritableFont.createFont("標楷體"), 14,
					WritableFont.BOLD);
			font12 = new WritableFont(WritableFont.createFont("新細明體"), 12,
					WritableFont.NO_BOLD);

			format14CenterNB = LMSUtil.setCellFormat(format14CenterNB, font14,
					Alignment.CENTRE, false, false);
			format12Left = LMSUtil.setCellFormat(format12Left, font12,
					Alignment.LEFT);
			format12LeftNB = LMSUtil.setCellFormat(format12LeftNB, font12,
					Alignment.LEFT, false, false);
			format12Center = LMSUtil.setCellFormat(format12Center, font12,
					Alignment.CENTRE, true, true);
			format12Right = LMSUtil.setCellFormat(format12Right, font12,
					Alignment.RIGHT, true, true);
			String branchName = Util.trim(branchService.getBranchName(brNo));

			// 合併單元格
			sheet.mergeCells(0, 0, 7, 0);
			label = new Label(0, 0, prop.getProperty("type2xls.content02"),
					format14CenterNB);
			sheet.addCell(label);

			label = new Label(0, 2, prop.getProperty("type2xls.content03")
					+ "：" + brNo + branchName, format12LeftNB);
			sheet.addCell(label);
			label = new Label(0, 3, prop.getProperty("type2xls.content04")
					+ "：" + dataDate,
					format12LeftNB);
			sheet.addCell(label);

			sheet = this.setType2Title(sheet, format12Center, prop);
			int count = 0;
			int uLoanYCount = 0;
			int uLoanNCount = 0;
			for (Map<String, Object> dataMap : list) {
				// 分行代碼
				aa = brNo;
				// 客戶統編
				bb = Util.trim(dataMap.get("CUST_ID"));
				// 戶名
				cc = Util.trim(dataMap.get("CNAME"));
				// 額度序號
				dd = Util.trim(dataMap.get("CONTRACT"));
				// 幣別
				ee = Util.trim(dataMap.get("FACT_SWFT"));
				// 授信額度
				ff = Util.trim(dataMap.get("FACT_AMT"));
				// 授信期間
				gg = Util.trim(dataMap.get("DURING_BG")) + "~"
						+ Util.trim(dataMap.get("DURING_ED"));
				// 備註
				if (!"".equals(Util.trim(dataMap.get("DOCUMENT_NO")))) {
					hh = prop.getProperty("type2xls.content05");
					uLoanYCount++;
				} else {
					hh = "";
					uLoanNCount++;
				}

				label = new Label(0, count + 5, aa, format12Center);
				sheet.addCell(label);
				label = new Label(1, count + 5, bb, format12Left);
				sheet.addCell(label);
				label = new Label(2, count + 5, cc, format12Left);
				sheet.addCell(label);
				label = new Label(3, count + 5, dd, format12Left);
				sheet.addCell(label);
				label = new Label(4, count + 5, ee, format12Center);
				sheet.addCell(label);
				label = new Label(5, count + 5, ff, format12Right);
				sheet.addCell(label);
				label = new Label(6, count + 5, gg, format12Center);
				sheet.addCell(label);
				label = new Label(7, count + 5, hh, format12Left);
				sheet.addCell(label);
				count++;
			}
			if (count == 0) {
				label = new Label(0, count + 5, "", format12Center);
				sheet.addCell(label);
				label = new Label(1, count + 5, "", format12Center);
				sheet.addCell(label);
				label = new Label(2, count + 5, "", format12Center);
				sheet.addCell(label);
				label = new Label(3, count + 5, "", format12Center);
				sheet.addCell(label);
				label = new Label(4, count + 5, "", format12Center);
				sheet.addCell(label);
				label = new Label(5, count + 5, "", format12Center);
				sheet.addCell(label);
				label = new Label(6, count + 5, "", format12Center);
				sheet.addCell(label);
				label = new Label(7, count + 5, "", format12Center);
				sheet.addCell(label);
				count++;
			}

			label = new Label(0, count + 4 + 1,
					prop.getProperty("type2xls.content06"), format12LeftNB);
			sheet.addCell(label);
			label = new Label(0, count + 4 + 2,
					prop.getProperty("type2xls.content07") + "。",
					format12LeftNB);
			sheet.addCell(label);

			workbook.write();
			workbook.close();
			Date dataEDate = TWNDate.valueOf(dataDate);
			dataEDate.setDate(CapDate.getDayOfMonth(TWNDate.toAD(dataEDate)
					.split("-")[0], TWNDate.toAD(dataEDate).split("-")[1]));
			this.saveL185M01A01Data(ranMainId, TWNDate.valueOf(dataDate),
					dataEDate, brNo, xlsOid, list.size(),
					UtilConstants.rRptType.企金戶未出現於覆審名單, "4", 0, uLoanYCount,
					uLoanNCount);
		} finally {

		}

	}

	/**
	 * 設定type3 xls的title
	 * 
	 * @param sheet
	 * @param format14Center
	 *            字型
	 * @return
	 * @throws RowsExceededException
	 * @throws WriteException
	 */
	private WritableSheet setType3Title(WritableSheet sheet,
			WritableCellFormat format12Center, Properties prop)
			throws RowsExceededException, WriteException {
		sheet.setColumnView(0, 10);
		sheet.setColumnView(1, 10);
		sheet.setColumnView(2, 15);
		sheet.setColumnView(3, 15);
		sheet.setColumnView(4, 15);
		sheet.setColumnView(5, 10);
		sheet.setColumnView(6, 15);
		sheet.setColumnView(7, 25);
		sheet.setColumnView(8, 12);
		sheet.setColumnView(9, 12);
		sheet.setColumnView(10, 10);
		sheet.setColumnView(11, 12);
		sheet.addCell(new Label(0, 3, prop.getProperty("type3xls.content05"),
				format12Center));
		sheet.addCell(new Label(1, 3, prop.getProperty("type3xls.content06"),
				format12Center));
		sheet.addCell(new Label(2, 3, prop.getProperty("type3xls.content07"),
				format12Center));
		sheet.addCell(new Label(3, 3, prop.getProperty("type3xls.content08"),
				format12Center));
		sheet.addCell(new Label(4, 3, prop.getProperty("type3xls.content09"),
				format12Center));
		sheet.addCell(new Label(5, 3, prop.getProperty("type3xls.content10"),
				format12Center));
		sheet.addCell(new Label(6, 3, prop.getProperty("type3xls.content11"),
				format12Center));
		sheet.addCell(new Label(7, 3, prop.getProperty("type3xls.content12"),
				format12Center));
		sheet.addCell(new Label(8, 3, prop.getProperty("type3xls.content13"),
				format12Center));
		sheet.addCell(new Label(9, 3, prop.getProperty("type3xls.content14"),
				format12Center));
		sheet.addCell(new Label(10, 3, prop.getProperty("type3xls.content15"),
				format12Center));
		sheet.addCell(new Label(11, 3, prop.getProperty("type3xls.content16"),
				format12Center));
		return sheet;
	}

	private void createExcel3(String brNo, String starDate, String endDate,
			String beDate, String enDate, List<Map<String, Object>> list,
			String listName, int delayCount) {
		String ranMainId = IDGenerator.getRandomCode();
		DocFile docFile = new DocFile();
		docFile.setBranchId(brNo);
		docFile.setContentType("application/msexcel");
		docFile.setMainId(ranMainId);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId(listName);
		docFile.setSrcFileName(listName + ".xls");
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId(fileService.getSysId());
		docFile.setData(new byte[] {});
		fileService.save(docFile, false);

		String aa = "";
		String bb = "";
		String cc = "";
		String dd = "";
		String ee = "";
		String ff = "";
		String gg = "";
		String hh = "";
		String ii = "";
		String jj = "";
		String kk = "";
		String ll = "";

		File file = null;
		String filename = null;
		String xlsOid = null;
		WritableWorkbook workbook = null;
		WritableSheet sheet = null;
		WritableFont font14 = null;
		WritableFont font12 = null;
		WritableCellFormat format14CenterNB = null;
		WritableCellFormat format12Left = null;
		WritableCellFormat format12LeftNB = null;
		WritableCellFormat format12Center = null;
		WritableCellFormat format12Right = null;
		Label label = null;
		Properties prop = null;

		xlsOid = docFile.getOid();
		filename = LMSUtil.getUploadFilePath(brNo, ranMainId, listName);
		file = new File(filename);
		file.mkdirs();
		try {
			file = new File(filename + "/" + xlsOid + ".xls");
			prop = MessageBundleScriptCreator
					.getComponentResource(LMS1855V01Page.class);
			workbook = Workbook.createWorkbook(file);

			sheet = workbook.createSheet(
					prop.getProperty("type3xls.content01"), 0);
			SheetSettings settings = sheet.getSettings();
			settings.setOrientation(PageOrientation.LANDSCAPE);
			settings.setScaleFactor(82);

			// 設置字型
			font14 = new WritableFont(WritableFont.createFont("標楷體"), 14,
					WritableFont.BOLD);
			font12 = new WritableFont(WritableFont.createFont("新細明體"), 12,
					WritableFont.NO_BOLD);

			format14CenterNB = LMSUtil.setCellFormat(format14CenterNB, font14,
					Alignment.CENTRE, false, false);
			format12Left = LMSUtil.setCellFormat(format12Left, font12,
					Alignment.LEFT);
			format12LeftNB = LMSUtil.setCellFormat(format12LeftNB, font12,
					Alignment.LEFT, false, false);
			format12Center = LMSUtil.setCellFormat(format12Center, font12,
					Alignment.CENTRE, true, true);
			format12Right = LMSUtil.setCellFormat(format12Right, font12,
					Alignment.RIGHT, true, true);
			// 判斷第一次和第二次查詢
			int row = list.size();
			// 合併單元格
			sheet.mergeCells(0, 0, 11, 0);
			label = new Label(0, 0,
					Util.trim(branchService.getBranchName(brNo))
							+ prop.getProperty("type3xls.content02"),
					format14CenterNB);
			sheet.addCell(label);
			label = new Label(0, 1, prop.getProperty("type3xls.content03")
					+ "：" + TWNDate.toTW(CapDate.getCurrentTimestamp()),
					format12LeftNB);
			sheet.addCell(label);
			label = new Label(0, 2, prop.getProperty("type3xls.content04")
					+ "：" + starDate + "~" + endDate, format12LeftNB);
			sheet.addCell(label);

			sheet = this.setType3Title(sheet, format12Center, prop);
			int count = 4;
			for (Map<String, Object> dataMap : list) {
				String branchId = Util.trim(dataMap.get("BRANCHID"));
				aa = branchId;
				bb = Util.trim(branchService.getBranchName(branchId));
				String custId = Util.trim(dataMap.get("CUSTID"));
				String dupNo = Util.trim(dataMap.get("DUPNO"));
				cc = custId + dupNo;
				dd = Util.trim(dataMap.get("CNAME"));
				ee = Util.trim(dataMap.get("CNTRNO"));
				ff = Util.trim(dataMap.get("FACT_CURR"));
				gg = NumConverter.addComma(Util.trim(dataMap.get("FACT_AMT")));
				hh = Util.trim(dataMap.get("STARTDT")) + "~"
						+ Util.trim(dataMap.get("ENDDT"));
				ii = Util.trim(dataMap.get("CYC_MN"));
				jj = Util.trim(dataMap.get("RETRIALDATE"));
				if (Util.isEmpty(jj)) {
					jj = prop.getProperty("type3xls.content17");
				}
				kk = Util.trim(dataMap.get("DELAY"));
				ll = Util.trim(dataMap.get("NCKDMEMO"));
				sheet.addCell(new Label(0, count, aa, format12Left));
				sheet.addCell(new Label(1, count, bb, format12Left));
				sheet.addCell(new Label(2, count, cc, format12Left));
				sheet.addCell(new Label(3, count, dd, format12Left));
				sheet.addCell(new Label(4, count, ee, format12Left));
				sheet.addCell(new Label(5, count, ff, format12Center));
				sheet.addCell(new Label(6, count, gg, format12Right));
				sheet.addCell(new Label(7, count, hh, format12Center));
				sheet.addCell(new Label(8, count, ii, format12Center));
				sheet.addCell(new Label(9, count, jj, format12Center));
				sheet.addCell(new Label(10, count, kk, format12Left));
				sheet.addCell(new Label(11, count, ll, format12Left));

				count++;
			}
			workbook.write();
			workbook.close();

			this.saveL185M01A01Data(ranMainId, TWNDate.valueOf(beDate),
					TWNDate.valueOf(enDate), brNo, xlsOid, row,
					UtilConstants.rRptType.撥貸後半年內辦理覆審檢核表, "1", delayCount, 0, 0);
		} catch (IOException e) {
			logger.error("lms1855Serviceimpl.createExcel3 IOException!!", e);
		} catch (WriteException e) {
			logger.error("lms1855Serviceimpl.createExcel3 WriteException!!", e);
		} finally {

		}
	}

	/**
	 * @param ranMainId
	 * @param date
	 * @param brNo
	 * @param xlsOid
	 * @param count
	 * @param rptType
	 */
	private void saveL185M01A01Data(String ranMainId, Date beDate, Date enDate,
			String brNo, String xlsOid, int count, String rptType,
			String authType, int delayCount, int uLoanYCount, int uLoanNCount) {
		// 存入L185M01A和L180A01A
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L185M01A l185m01a = new L185M01A();
		L185A01A l185a01a = new L185A01A();
		l185m01a.setMainId(ranMainId);
		l185m01a.setRptType(rptType);
		l185m01a.setDataDate(beDate);

		l185m01a.setDataEDate(enDate);
		l185m01a.setOwnBrId(brNo);
		l185m01a.setBranchId(brNo);
		// 1.分行/自辦 2.區域營運中心 3.徵信處
		l185m01a.setUnitType("1");
		l185m01a.setRecCount(count);
		l185m01a.setAcctCount(count);
		l185m01a.setULoanYCount(uLoanYCount);
		l185m01a.setULoanNCount(uLoanNCount);
		l185m01a.setCreateTime(CapDate.getCurrentTimestamp());
		l185m01a.setReportFile(xlsOid);
		l185m01a.setDelayCount(delayCount);
		this.save(l185m01a);

		l185a01a.setMainId(ranMainId);
		l185a01a.setAuthType(authType);
		l185a01a.setAuthUnit(user.getUnitNo());
		l185a01a.setOwnUnit(user.getUnitNo());
		this.save(l185a01a);
	}

	/**
	 * 設定type3 xls的title
	 * 
	 * @param sheet
	 * @param format14Center
	 *            字型
	 * @return
	 * @throws RowsExceededException
	 * @throws WriteException
	 */
	private WritableSheet setType4Title(WritableSheet sheet,
			WritableCellFormat format12Center, Properties prop)
			throws RowsExceededException, WriteException {
		// 合併單元格
		sheet.mergeCells(0, 3, 0, 4);
		sheet.mergeCells(1, 3, 1, 4);
		sheet.mergeCells(2, 3, 4, 3);
		sheet.mergeCells(5, 3, 5, 4);
		sheet.mergeCells(6, 3, 6, 4);
		sheet.mergeCells(7, 3, 7, 4);
		sheet.mergeCells(8, 3, 8, 4);
		sheet.mergeCells(9, 3, 9, 4);
		sheet.setColumnView(0, 15);
		sheet.setColumnView(1, 15);
		sheet.setColumnView(2, 12);
		sheet.setColumnView(3, 17);
		sheet.setColumnView(4, 12);
		sheet.setColumnView(5, 15);
		sheet.setColumnView(6, 12);
		sheet.setColumnView(7, 20);
		sheet.setColumnView(8, 10);
		sheet.setColumnView(9, 10);
		sheet.addCell(new Label(0, 3, prop.getProperty("type4xls.content08"),
				format12Center));
		sheet.addCell(new Label(1, 3, prop.getProperty("type4xls.content09"),
				format12Center));
		sheet.addCell(new Label(2, 3, prop.getProperty("type4xls.content10"),
				format12Center));
		sheet.addCell(new Label(2, 4, prop.getProperty("type4xls.content11"),
				format12Center));
		sheet.addCell(new Label(3, 4, prop.getProperty("type4xls.content12"),
				format12Center));
		sheet.addCell(new Label(4, 4, prop.getProperty("type4xls.content13"),
				format12Center));
		sheet.addCell(new Label(5, 3, prop.getProperty("type4xls.content14"),
				format12Center));
		sheet.addCell(new Label(6, 3, prop.getProperty("type4xls.content15"),
				format12Center));
		sheet.addCell(new Label(7, 3, prop.getProperty("type4xls.content16"),
				format12Center));
		sheet.addCell(new Label(8, 3, prop.getProperty("type4xls.content17"),
				format12Center));
		sheet.addCell(new Label(9, 3, prop.getProperty("type4xls.content18"),
				format12Center));
		return sheet;
	}

	/**
	 * @param brNo
	 * @param list4
	 * @param listName
	 * @throws IOException
	 * @throws WriteException
	 */
	private void createExcel4(String brNo, String dataDate,
			List<Map<String, Object>> list4, String listName, int delayCount)
			throws WriteException, IOException {
		String ranMainId = IDGenerator.getRandomCode();
		DocFile docFile = new DocFile();
		docFile.setBranchId(brNo);
		docFile.setContentType("application/msexcel");
		docFile.setMainId(ranMainId);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId(listName);
		docFile.setSrcFileName(listName + ".xls");
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId(fileService.getSysId());
		docFile.setData(new byte[] {});
		fileService.save(docFile, false);
		File file = null;
		String filename = null;
		String xlsOid = null;
		WritableWorkbook workbook = null;
		WritableSheet sheet = null;
		WritableFont font14 = null;
		WritableFont font12 = null;
		WritableCellFormat format14CenterNB = null;
		WritableCellFormat format12Left = null;
		WritableCellFormat format12LeftNB = null;
		WritableCellFormat format12Center = null;
		WritableCellFormat format12Right = null;
		Label label = null;
		Properties prop = null;

		xlsOid = docFile.getOid();
		filename = LMSUtil.getUploadFilePath(brNo, ranMainId, listName);
		file = new File(filename);
		file.mkdirs();
		try {
			file = new File(filename + "/" + xlsOid + ".xls");
			prop = MessageBundleScriptCreator
					.getComponentResource(LMS1855V01Page.class);
			workbook = Workbook.createWorkbook(file);

			sheet = workbook.createSheet(
					prop.getProperty("type4xls.content01"), 0);
			/*
			 * 1.1方向 SheetSetting#setOrientation(PageOrientation po)； 參數：
			 * PageOrientation#LANDSCAPE 橫向打印 PageOrientation# PORTRAIT 縱向打印 (A)
			 * SheetSetting #setScaleFactor (int);百分比形式
			 */
			SheetSettings settings = sheet.getSettings();
			settings.setOrientation(PageOrientation.LANDSCAPE);
			settings.setScaleFactor(95);

			// 設置字型
			font14 = new WritableFont(WritableFont.createFont("標楷體"), 14,
					WritableFont.BOLD);
			font12 = new WritableFont(WritableFont.createFont("新細明體"), 12,
					WritableFont.NO_BOLD);

			format14CenterNB = LMSUtil.setCellFormat(format14CenterNB, font14,
					Alignment.CENTRE, false, false);
			format12Left = LMSUtil.setCellFormat(format12Left, font12,
					Alignment.LEFT);
			format12LeftNB = LMSUtil.setCellFormat(format12LeftNB, font12,
					Alignment.LEFT, false, false);
			format12Center = LMSUtil.setCellFormat(format12Center, font12,
					Alignment.CENTRE, true, true);
			format12Right = LMSUtil.setCellFormat(format12Right, font12,
					Alignment.RIGHT, true, true);

			String branchName = branchService.getBranchName(brNo);

			int row = list4.size();
			// 合併單元格
			sheet.mergeCells(0, 0, 9, 0);
			label = new Label(0, 0, prop.getProperty("type4xls.content02"),
					format14CenterNB);
			sheet.addCell(label);
			label = new Label(0, 1, prop.getProperty("type4xls.content03")
					+ brNo + branchName, format12LeftNB);
			sheet.addCell(label);
			label = new Label(0, 2, prop.getProperty("type4xls.content04")
					+ TWNDate.toTW(CapDate.getCurrentTimestamp()),
					format12LeftNB);
			sheet.addCell(label);

			sheet = this.setType4Title(sheet, format12Center, prop);
			int count = 5;

			for (Map<String, Object> dataMap : list4) {
				sheet.addCell(new Label(0, count, Util.trim(dataMap
						.get("line1")), format12Left));
				sheet.addCell(new Label(1, count, Util.trim(dataMap
						.get("line2")), format12Left));
				sheet.addCell(new Label(2, count, Util.trim(dataMap
						.get("line3")), format12Center));
				sheet.addCell(new Label(3, count, Util.trim(dataMap
						.get("line4")), format12Left));
				sheet.addCell(new Label(4, count, Util.trim(dataMap
						.get("line5")), format12Center));
				sheet.addCell(new Label(5, count, Util.trim(dataMap
						.get("line6")), format12Center));
				sheet.addCell(new Label(6, count, Util.trim(dataMap
						.get("line7")), format12Center));
				sheet.addCell(new Label(7, count, Util.trim(dataMap
						.get("line8")), format12Left));
				sheet.addCell(new Label(8, count, Util.trim(dataMap
						.get("line9")), format12Center));
				sheet.addCell(new Label(9, count, Util.trim(dataMap
						.get("line10")), format12Left));

				count++;
			}
			workbook.write();
			workbook.close();

			this.saveL185M01A01Data(ranMainId, TWNDate.valueOf(dataDate),
					TWNDate.valueOf(dataDate), brNo, xlsOid, row,
					UtilConstants.rRptType.授信覆審明細檢核表, "1", delayCount, 0, 0);

		} finally {

		}
	}

	/**
	 * (覆審管理報表-Type4-找L180m01bAndL170m01aByLMS.412的參數)
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @param lrdate412
	 * @return
	 */
	private Map<String, Object> findlrDatellrDate(String custId, String dupNo,
			String brNo) {
		List<Map<String, Object>> list = eloandbBaseService.findlrDatellrDate(
				custId, dupNo, brNo);
		Map<String, Object> collectionkMap = new HashMap<String, Object>();
//		(1)有 LR && LLR 
//		  A.依上次覆審週期判斷是否逾期
//		(2)只有 LR 
//		  A.依本次覆審週期判斷是否逾期
//		(3)無資料或尚未覆審
//		  A.逾期
		if(list.size() > 0){
			for (Map<String, Object> dataMap : list) {
				if ("LR".equals(Util.trim(dataMap.get("LRTYPE")))) {
					// 上次覆審日,(最近一次) 覆審日期
					collectionkMap.put("LRDATE", (Date) dataMap.get("RETRIALDATE"));
					// 借戶名
					// collectionkMap.put("elfCName180",
					// Util.trim(dataMap.get("elfCName")));
					// 主要授信戶
					collectionkMap.put("ELFMAINCUST",
							Util.trim(dataMap.get("ELFMAINCUST")));
					// 資信評等
					collectionkMap.put("ELFCRDTTBL",
							Util.trim(dataMap.get("ELFCRDTTBL")));
					collectionkMap.put("ELFMOWTYPE",
							Util.trim(dataMap.get("ELFMOWTYPE")));
					collectionkMap.put("ELFMOWTBL1",
							Util.trim(dataMap.get("ELFMOWTBL1")));
					// 覆審週期
					collectionkMap.put("ELFRCKDLINE",
							Util.trim(dataMap.get("ELFRCKDLINE")));

					collectionkMap.put("ELFUCKDLINE",
							Util.trim(dataMap.get("ELFUCKDLINE")));
					Date elfMDDt = (Date) dataMap.get("ELFMDDT");
					collectionkMap.put("ELFMDDT", (Util.isEmpty(elfMDDt) ? null
							: TWNDate.toAD(elfMDDt)));
					collectionkMap
							.put("ELFMDFLAG", Util.trim(dataMap.get("ELFMDFLAG")));
					collectionkMap
							.put("ELFNEWADD", Util.trim(dataMap.get("ELFNEWADD")));
					collectionkMap.put("ELFNEWDATE",
							Util.trim(dataMap.get("ELFNEWDATE")));
					collectionkMap.put("ELFNCKDFLAG",
							Util.trim(dataMap.get("ELFNCKDFLAG")));
					collectionkMap
							.put("ELFUCKDDT", Util.trim(dataMap.get("ELFUCKDDT")));
				} else if ("LLR".equals(Util.trim(dataMap.get("LRTYPE")))) {
					// 上上次覆審日
					collectionkMap.put("LLRDATE", (Date) dataMap.get("ELFLLRDATE"));
				}
			}
		}else{
			//逾期
			collectionkMap.put("GOODBYE", "Y");
		}
		
		return collectionkMap;
	}

	@Override
	public void deleteL185m01a(String[] mainIdList, String listName) {
		List<L185M01A> l185m01aList = new LinkedList<L185M01A>();
		List<L185A01A> l185a01aList = new LinkedList<L185A01A>();
		List<DocFile> docFilesList = new LinkedList<DocFile>();
		for (String mainId : mainIdList) {
			L185M01A l185m01a = l185m01aDao.findByMainId(mainId);
			if (l185m01a == null) {
				l185m01a = new L185M01A();
			}
			List<L185A01A> l185a01as = l185a01aDao.findByMainId(mainId);
			List<DocFile> docFiles = docFileDao.findByMainIdAndFieldId(mainId,
					listName);
			l185m01aList.add(l185m01a);
			l185a01aList.addAll(l185a01as);
			docFilesList.addAll(docFiles);
		}
		l185m01aDao.delete(l185m01aList);
		l185a01aDao.delete(l185a01aList);
		docFileDao.delete(docFilesList);
	}

	@Override
	public void delete(GenericBean... entity) {
		// TODO Auto-generated method stub

	}

	@SuppressWarnings("rawtypes")
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		// TODO Auto-generated method stub
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		// TODO Auto-generated method stub
		return null;
	}
}
