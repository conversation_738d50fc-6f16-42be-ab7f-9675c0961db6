package com.mega.eloan.lms.fms.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L850M01A;
import com.mega.eloan.lms.model.L850M01B;
import com.mega.eloan.lms.model.L850M01C;

import tw.com.iisi.cap.exception.CapMessageException;

/**
 * <pre>
 * 資料上傳作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface LMS8500Service extends AbstractService {

	/**
	 * 透過登入者分行判斷要撈出哪些申請類別
	 * 
	 * @return
	 * @throws CapMessageException
	 */
	public Map<String, String> getAppCodeBySsoUnitno()
			throws CapMessageException;

	/**
	 * 產生新的L850M01A資料上傳主檔
	 * 
	 * @return
	 */
	public L850M01A newL850m01a(String txCode, String appCode, String version);

	/**
	 * 儲存L850M01A資料上傳主檔
	 * 
	 * @param mainId
	 * @param form
	 * @return
	 */
	public L850M01A saveL850m01a(String mainId, String form)
			throws CapMessageException;

	/**
	 * 刪除編制中的資料上傳作業主檔
	 * 
	 * @param oids
	 */
	public void deleteL850m01a(String[] oids);

	/**
	 * 刪除簽章欄檔
	 * 
	 * @param mainId
	 * @param isAll
	 */
	public void deleteL850m01bList(String mainId, boolean isAll);

	/**
	 * 刪除簽章欄檔
	 * 
	 * @param l850m01bs
	 * @param isAll
	 */
	public void deleteL850m01bList(List<L850M01B> l850m01bs, boolean isAll);

	/**
	 * 新增簽章欄檔
	 * 
	 * @param list
	 */
	public void saveL850m01bList(List<L850M01B> list);

	/**
	 * 查簽章欄檔
	 * 
	 * @param mainId
	 * @param branchType
	 * @param branchId
	 * @param staffNo
	 * @param staffJob
	 * @return
	 */
	public L850M01B findL850m01b(String mainId, String branchType,
			String branchId, String staffNo, String staffJob);

	/**
	 * 處理簽章欄並執行流程
	 * 
	 * @param oid
	 * @param formSelectBoss
	 * @param manager
	 * @param haveCheckDate
	 * @param haveFlowAction
	 * @param isApprove
	 */
	public void processL850m01bAndFlowAction(String oid,
			String[] formSelectBoss, String manager, boolean haveCheckDate,
			boolean haveFlowAction, boolean isApprove) throws Throwable;

	/**
	 * 執行流程
	 * 
	 * @param mainOid
	 * @param model
	 * @param setResult
	 * @param resultType
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, L850M01A model, boolean setResult,
			boolean resultType) throws Throwable;

	/**
	 * 純儲存L850M01C
	 * 
	 * @param mainId
	 * @param l850m01cList
	 */
	void saveL850m01c(String mainId, List<L850M01C> l850m01cList);

	/**
	 * 純刪除L850M01C
	 * 
	 * @param mainId
	 */
	void deleteL850m01c(String mainId);

	/**
	 * 檢查v090要匯入的codeType是否已存在於資料庫中
	 * 
	 * @param l850m01cList
	 * @return
	 */
	StringBuilder checkCodeTypeCanInsert(List<L850M01C> l850m01cList);
}
