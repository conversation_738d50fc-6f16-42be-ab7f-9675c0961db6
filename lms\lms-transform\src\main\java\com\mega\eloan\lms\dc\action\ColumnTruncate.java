package com.mega.eloan.lms.dc.action;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.util.Column;
import com.mega.eloan.lms.dc.util.DXLUtil;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * ColumnTruncate : 欄位檢核
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          <li>2013/03/07,UFO,取消local_var: dataBean，父類別取得
 *          </ul>
 */
public class ColumnTruncate extends BaseAction {
	private final String TABSCHEMA = "LMS";// 企個金共用
	private ArrayList<Column> list = null;
	private String dxlDirRootPath = "";// LMS dxl主要根目錄
	private String logsDirPath = "";
	private String loadDB2DirPath = "";// 與db相關 load_db2 目錄所在位置路徑
	private PrintWriter pwr = null;// 問題欄位輸出
	private PrintWriter logsPw = null;// 明細log
	private PrintWriter newData = null;// 重新輸出一份當前讀取的.txt檔
	private String dbType = "";
	private String schema = "";
	private String table = "";
	private String path = "";// 來源資料辨識碼
	private String file = "";// 被檢核的來源檔案
	private String BrnoFile = "";// 用來紀錄目前的分行檔案名稱
	private String oid = "";// 用來紀錄有問題的oid
	@SuppressWarnings("unused")
	private String[] subTable = new String[] { "L120M01A", "L120S01A",
			"L140M01A", "L140M01I", "L230S01A" };
	@SuppressWarnings("unused")
	private String[] subColumn = new String[] { "CUSTNAME", "RNAME" };
	private String[] clearColumn = new String[] { "RGTCURR", "CPTLCURR" };

	/**
	 * 初始化必要資訊及執行欄位檢核動作
	 * 
	 * @param dbType
	 *            String:連結的DB
	 * @param schema
	 *            String:系統代號 schema
	 * @param path
	 *            String:要檢核的文字檔路徑辨識碼Ex:db2 or 分行名
	 */
	public void doChkColumn(String dbType, String schema, String path) {
		if (StringUtils.isBlank(schema)) {
			String errmsg = "讀取系統名稱錯誤,未指定要執行的系統名稱,請重新確認!";
			this.logger.error(errmsg);
			throw new DCException(errmsg);
		}

		this.logger.info("正在初始化 ColumnTruncate 必要資訊...");
		this.dbType = dbType; // Ex:DELOANDB
		this.schema = schema; // Ex:LMS
		this.path = path; // Ex:db2

		try {
			if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(schema)) {
				this.dxlDirRootPath = this.configData.getLmsDxlDirRootPath();// homePath\today\LMS
				this.logsDirPath = this.configData.getLmsLogsDirPath();// User當前工作目錄\log\logs\執行日期\LMS
				// User當前工作目錄\load_db2\執行日期\LMS\data
				this.loadDB2DirPath = this.configData.getLmsloadDB2DirPath()
						+ this.configData.getDataPath();
			} else {
				this.dxlDirRootPath = this.configData.getClsDxlDirRootPath();// homePath\today\CLS
				// User當前工作目錄\log\logs\執行日期\CLS
				this.logsDirPath = this.configData.getClsLogsDirPath();
				// User當前工作目錄\load_db2\執行日期\CLS\data
				this.loadDB2DirPath = this.configData.getClsloadDB2DirPath()
						+ this.configData.getDataPath();
			}

			if (TextDefine.MAIN_PATH_DB2.equalsIgnoreCase(this.path)) {
				this.doLoadDB2();
			} else {
				// for 分行檢核
				this.doBrno();
			}
		} catch (Exception e) {
			String errmsg = this.dbType + " ,schema :" + this.schema
					+ " ,table :" + this.table + " ,file :" + this.file
					+ "產生錯誤";
			this.logger.error(errmsg, e);
			this.logsPw.println(errmsg);
			e.printStackTrace(this.logsPw);
			throw new DCException(errmsg, e);
		} finally {
			IOUtils.closeQuietly(this.pwr);
			IOUtils.closeQuietly(this.newData);
			IOUtils.closeQuietly(this.logsPw);
		}
		this.logger.info("ColumnTruncate 執行結束...\n");
	}

	/**
	 * 執行load_db2資料夾下的欄位檢核
	 * 
	 * @throws Exception
	 */
	private void doLoadDB2() throws Exception {
		// 取得對應的Table Name
		File db2File = new File(this.loadDB2DirPath);
		if (db2File.isDirectory()) {
			String[] db2list = DXLUtil.list_suffix(db2File, TextDefine.ATTACH_TXT);
//			String[] db2list = db2File.list(new FilenameFilter() {
//				@Override
//				public boolean accept(File dir, String name) {
//					return name.endsWith(TextDefine.ATTACH_TXT); // 讀取所有指定檔案
//				}
//			});

			final String LOG_ROOT = this.logsDirPath + File.separator
					+ "COL_CHK";
			Util.checkDirExist(LOG_ROOT);

			for (String db2Txt : db2list) { // Ex:L120S01B.txt or
											// Ex:L140M01A_BF.txt or
											// FCLS115M01_L120M01C.txt

				String formName = db2Txt.split("_")[0];
				// txt檔名即為tableName, 2013-03-28 區分Form115M01

				if (formName
						.equalsIgnoreCase(TextDefine.CLS_L140M01B_FORM_11501)) {
					this.table = db2Txt.split("_")[1]
							.split(TextDefine.ATTACH_TXT)[0];
				} else {
					this.table = db2Txt.split(TextDefine.ATTACH_TXT)[0];
				}
				String logTable = db2Txt.split(TextDefine.ATTACH_TXT)[0];

				// 準備檢核的文字檔來源
				this.file = this.loadDB2DirPath + File.separator + db2Txt;
				// 問題欄位輸出
				String outFile = LOG_ROOT + File.separator + schema + "."
						+ logTable + ".chk";
				this.pwr = new PrintWriter(new BufferedWriter(
						new OutputStreamWriter(new FileOutputStream(new File(
								outFile)), TextDefine.ENCODING_MS950)), true);
				// 輸出log
				// 20130114 SandraPeng調整明細log的檔名，依table名稱命名
				String logPath = LOG_ROOT + File.separator + "colTruncate_"
						+ logTable + ".log";
				this.logsPw = new PrintWriter(new BufferedWriter(
						new OutputStreamWriter(new FileOutputStream(new File(
								logPath)), TextDefine.ENCODING_MS950)), true);

				// 輸出newData 2013-03-28 決議調整明細log的檔名，依系統名+table名
				String newDaPath = this.loadDB2DirPath + File.separator
						+ this.TABSCHEMA + "." + logTable + ".txt";
				this.newData = new PrintWriter(new BufferedWriter(
						new OutputStreamWriter(new FileOutputStream(new File(
								newDaPath)), TextDefine.ENCODING_MS950)), true);

				this.logger.debug("開始取得資料庫欄位資訊 ...");

				this.list = ColumnGetDBInfoFactory.getInstance().getInfo(
						dbType, this.TABSCHEMA, this.table);
				this.logger.debug("文字檔名 :" + db2Txt + " 開始執行 DB名稱:"
						+ this.table + "的doCheck ...");
				// 20130114 SandraPeng 增加顯示table名稱
				// this.logsPw.println("開始檢查:" + logTable);
				this.doCheck(this.file, "");
			}
		}
	}

	/**
	 * 執行分行資料夾下的欄位檢核
	 * 
	 * @throws Exception
	 */
	private void doBrno() throws Exception {
		// 取得分行名資料夾
		String brnPath = this.dxlDirRootPath + File.separator + this.path;
		File brnFile = new File(brnPath);

		final String LOG_ROOT = this.logsDirPath + File.separator + "COL_CHK";
		Util.checkDirExist(LOG_ROOT);

		// 第一層 分行代號
		if (brnFile.isDirectory()) {
			for (File subDir : DXLUtil.list_subdir(brnFile)) {
//			for (File subDir : brnFile.listFiles()) {
				// 第二層 notes view name
				if (subDir.isDirectory()) {
					String txtPath = subDir.getAbsolutePath() + File.separator
							+ "TEXT";
					File txtFile = new File(txtPath);
					String[] textlist = DXLUtil.list_suffix(txtFile, TextDefine.ATTACH_TXT);
//					String[] textlist = txtFile.list(new FilenameFilter() {
//						@Override
//						public boolean accept(File dir, String name) {
//							return name.endsWith(TextDefine.ATTACH_TXT); // 讀取所有指定檔案
//						}
//					});

					for (String text : textlist) { // Ex:FLMS140M01_L140M01A_BF.txt
						if (text.equalsIgnoreCase("L120M01C.txt")
								|| text.equalsIgnoreCase("DXL_Key.txt"))
							continue;

						String formName = text.split("_")[0];
						int dashIdx = text.indexOf("_");// 第一次出現的under Line"_"
						int txtIdx = text.indexOf(TextDefine.ATTACH_TXT);
						this.table = text.substring(dashIdx + 1, txtIdx);// 即為tableName
						// 2013-03-28 區分Form115M01
						String logTable = "";
						if (formName
								.equalsIgnoreCase(TextDefine.CLS_L140M01B_FORM_11501)) {
							logTable = text.split(TextDefine.ATTACH_TXT)[0];
						} else {
							logTable = this.table;
						}

						this.BrnoFile = text;
						// 準備檢核的文字檔來源
						this.file = txtPath + File.separator + text;

						// 問題欄位輸出
						String outFile = LOG_ROOT + File.separator + schema
								+ "." + this.path + "_" + logTable + ".chk";
						this.pwr = new PrintWriter(new BufferedWriter(
								new OutputStreamWriter(new FileOutputStream(
										new File(outFile)),
										TextDefine.ENCODING_MS950)), true);
						// 輸出log
						// 2013-01-17 決議調整明細log的檔名，依colTruncate_table名+分行名
						// 2013-04-12 調整產出檔名，分行名+colTruncate_table名
						String logPath = LOG_ROOT + File.separator
								+ "colTruncate_" + this.path + "_" + logTable
								+ ".log";
						this.logsPw = new PrintWriter(new BufferedWriter(
								new OutputStreamWriter(new FileOutputStream(
										new File(logPath)),
										TextDefine.ENCODING_MS950)), true);
						// 輸出newData
						// 2013-03-28 決議調整明細log的檔名，依系統名+table名+分行名
						String newDaPath = this.loadDB2DirPath + File.separator
								+ this.TABSCHEMA + "." + this.path + "_"
								+ logTable + ".txt";
						this.newData = new PrintWriter(new BufferedWriter(
								new OutputStreamWriter(new FileOutputStream(
										new File(newDaPath)),
										TextDefine.ENCODING_MS950)), true);

						this.logger.debug("開始取得資料庫欄位資訊 ...");
						this.list = ColumnGetDBInfoFactory.getInstance()
								.getInfo(dbType, this.TABSCHEMA, this.table);
						this.logger.debug("文字檔名 :" + text + " 開始執行  DB名稱:"
								+ this.table + "的doCheck ...");
						// 20130114 SandraPeng 增加顯示table名稱
						// this.logsPw.println("開始檢查 :" + logTable);
						this.doCheck(this.file, this.path);
					}// end of for (String text : textlist)
				}// end of if (subDir.isDirectory())
			}// end of for (File subDir : brnFile.listFiles())
		}
	}

	/**
	 * 取得每列總欄位數
	 * 
	 * @return
	 */
	public int getRowColCnt() {
		return this.list.size();
	}

	/**
	 * 執行檢核
	 * 
	 * @param file
	 *            String:來源資料路徑
	 */
	public void doCheck(String file, String brno) {
		/*
		 * this.logsPw.println(this.table + " ColumnTruncate 起始時間 :" +
		 * Util.getNowTime() + "\n");
		 */
		long tt1 = System.currentTimeMillis();
		BufferedReader read;
		String rowCol = "";
		try {
			// read要成為utf-8格式,否則在欄位長度判斷時在有中文或全形狀態下會無法正確識別
			read = new BufferedReader(new InputStreamReader(
					new FileInputStream(file), TextDefine.ENCODING_UTF8));
			// 若傳入的brno有值，則加上分隔符號
			if (brno.length() > 0)
				brno = brno + ";";
			String rcd = DXLUtil.proc_readline(read.readLine());
			int rCnt = 0;
			int err = 0;
			StringBuffer sb = new StringBuffer();
			while (rcd != null) {
				rCnt++;
				String[] data = Util.split(rcd, TextDefine.SYMBOL_SEMICOLON);
				this.oid = data[0];
				if (data.length != this.getRowColCnt()) {
					this.logsPw.println(brno + "第" + rCnt + "行，[" + data[0]
							+ "][" + data[1] + "]欄位不足或超過，總欄位數["
							+ this.getRowColCnt() + "] ,資料欄位數[" + data.length
							+ "]");
					rcd = DXLUtil.proc_readline(read.readLine());
					err++;
					continue;
				}

				sb.setLength(0);
				for (int i = 0; i < this.getRowColCnt(); i++) {
					Column col = this.list.get(i);
					rowCol = data[i];
					String tmpCol = rowCol;
					boolean pass = true;
					if (StringUtils.isNotBlank(rowCol)) {
						if (this.table.equals("BDOCFILE")
								&& col.getName().equalsIgnoreCase("FILESIZE")) {
							this.logger.debug("====" + col.getName() + "/"
									+ col.getType());
						}

						switch (col.getType()) {
						case CHARACTER:
							pass = !Util.isTooLong(rowCol, col.getLength());
							if (!pass) {
								tmpCol = truncateSpecial(brno, data, col,
										rowCol);
								pass = true;
							}
							/*
							 * if (TextDefine.SCHEMA_LMS
							 * .equalsIgnoreCase(this.schema) && !pass) { // 截長度
							 * if (Arrays.asList(this.subTable).contains(
							 * this.table) && Arrays.asList(this.subColumn)
							 * .contains(col.getName())) { tmpCol =
							 * Util.truncateString(rowCol, col.getLength());
							 * this
							 * .logger.debug(col.getLength()+"/截前="+rowCol+"/截後="
							 * +tmpCol+"/"); }
							 * 
							 * // 清空 if ("L120S01B".equalsIgnoreCase(this.table)
							 * && Arrays.asList(this.clearColumn)
							 * .contains(col.getName())) { tmpCol =
							 * TextDefine.EMPTY_STRING; } pass = true; }
							 */

							break;
						case VARCHAR:
							pass = !Util.isTooLong(rowCol, col.getLength());
							if (!pass) {
								tmpCol = truncateSpecial(brno, data, col,
										rowCol);
								pass = true;
							}
							break;
						case DATE:
							pass = Util.isDate(rowCol);
							if (!pass) {
								tmpCol = "";
							} else {
								// 如果為日期，先把不足未補0
								// 先把日期做的format: 先做成 "四碼-兩碼-兩碼"的樣式不足位數補0
								String[] info = rowCol.split("[-/]");
								if (info.length == 3) {
									String year = Util.addZeroWithValue(
											info[0], 2);
									String month = Util.addZeroWithValue(
											info[1], 2);
									String day = Util.addZeroWithValue(info[2],
											2);
									tmpCol = year + "-" + month + "-" + day;
								}
							}
							break;
						case TIMESTAMP:
							pass = Util.isDate(rowCol);
							if (!pass) {
								tmpCol = "";
							}
							break;
						case INTEGER:
							pass = Util.isInteger(rowCol);
							if (!pass) {
								tmpCol = "";
							}
							break;
						case DECIMAL:
							// 有些數值欄位有,符號
							tmpCol = tmpCol.replaceAll(",",
									TextDefine.EMPTY_STRING);
							pass = Util.isNumber(tmpCol, col.getLength(),
									col.getScale());
							if (!pass) {
								tmpCol = "";
							} else {
								// 只要不是空值就先將資料做四捨五入到指定的位數
								if (StringUtils.isNotBlank(tmpCol)) {
									tmpCol = Util
											.parseToBigDecimal(tmpCol)
											.setScale(col.getScale(),
													BigDecimal.ROUND_HALF_UP)
											.toString();
								}
							}
							break;
						case CLOB:
							pass = true;
							break;
						default:
							pass = false;
							break;
						}
					}
					// 移除雙引號
					// sb.append(tmpCol.replaceAll("\"",
					// TextDefine.EMPTY_STRING))
					// .append(TextDefine.SYMBOL_SEMICOLON);
					sb.append(tmpCol).append(TextDefine.SYMBOL_SEMICOLON);
					if (!pass) {
						this.pwr.println(brno + data[0]
								+ TextDefine.SYMBOL_SEMICOLON + data[1]
								+ TextDefine.SYMBOL_SEMICOLON + col.getDscr()
								+ TextDefine.SYMBOL_SEMICOLON + col.getName()
								+ TextDefine.SYMBOL_SEMICOLON + "TYPE["
								+ col.getType().name() + "]; LENGTH["
								+ col.getLength() + "]; SCALE["
								+ col.getScale() + "]; 欄位內容(" + rowCol + ")"
								+ "; table :" + this.table);
						err++;
					}
				}
				rcd = DXLUtil.proc_readline(read.readLine());

				IOUtils.write(sb.toString().replaceAll(";$", ""), newData);
				IOUtils.write(IOUtils.LINE_SEPARATOR_UNIX, newData);
			}
			read.close();

			@SuppressWarnings("unused")
			long cost = System.currentTimeMillis() - tt1;

			// 20130114 SandraPeng 增加log顯示，以便在主log中一覽全部檢核結果
			if (TextDefine.MAIN_PATH_DB2.equals(this.path)) {
				if (err > 0)
					this.logger.info("檢核" + this.table + "產生" + err + "個錯誤，"
							+ "請參考colTruncate_" + this.table + ".log及LMS."
							+ this.table + ".chk");
				else
					this.logger.debug("檢核" + this.table + "完成，無錯誤產生！");

				/*
				 * this.logsPw.println("\n" + this.table +
				 * " ColumnTruncate 結束時間 :" + Util.getNowTime() +
				 * " ,TOTAL TIME :" + Util.millis2minute(cost));
				 */
			} else {
				if (err > 0)
					this.logger.info("檢核" + this.BrnoFile + "_" + this.path
							+ "產生" + err + "個錯誤，" + "請參考colTruncate_"
							+ this.path + "_" + this.BrnoFile + ".log及LMS."
							+ this.path + "_" + this.BrnoFile + ".chk");
				else
					this.logger.debug("檢核" + this.BrnoFile + "_" + this.path
							+ "完成，無錯誤產生！");

				/*
				 * this.logsPw.println("\n" + this.BrnoFile + "_" + this.path +
				 * " ColumnTruncate 結束時間 :" + Util.getNowTime() +
				 * " ,TOTAL TIME :" + Util.millis2minute(cost));
				 */
			}

		} catch (Exception e) {
			String errmsg = "檢核時產生錯誤...dbType:" + this.dbType + " ,schema :"
					+ this.schema + " ,table :" + this.table + " ,file :"
					+ this.file + "\n 執行oid名稱:" + this.oid + " 執行欄位值: "
					+ rowCol;

			this.logger.error(errmsg, e);
			this.logsPw.println(errmsg);
			e.printStackTrace(this.logsPw);
			throw new DCException(errmsg, e);
		} finally {
			IOUtils.closeQuietly(newData);
		}
	}

	private String truncateSpecial(String brno, String[] data, Column col,
			String rowCol) {
		String tmpCol = rowCol;
		if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(this.schema)) {
			// 截長度
			// if (Arrays.asList(this.subTable).contains(this.table)
			// && Arrays.asList(this.subColumn).contains(col.getName())) {
			tmpCol = Util.truncateString(rowCol, col.getLength());
			this.logger.debug(col.getLength() + "/截前=" + rowCol + "/截後="
					+ tmpCol + "/");
			// }

			// 清空
			if ("L120S01B".equalsIgnoreCase(this.table)
					&& Arrays.asList(this.clearColumn).contains(col.getName())) {
				tmpCol = TextDefine.EMPTY_STRING;
			}
		} else {
			tmpCol = Util.truncateString(rowCol, col.getLength());
			this.logger.debug(col.getLength() + "/截前=" + rowCol + "/截後="
					+ tmpCol + "/");
		}

		this.logsPw.println(brno + data[0] + TextDefine.SYMBOL_SEMICOLON
				+ data[1] + TextDefine.SYMBOL_SEMICOLON + col.getDscr()
				+ TextDefine.SYMBOL_SEMICOLON + col.getName()
				+ TextDefine.SYMBOL_SEMICOLON + "TYPE[" + col.getType().name()
				+ "]; LENGTH[" + col.getLength() + "]; SCALE[" + col.getScale()
				+ "]; 欄位內容(" + rowCol + ")" + "; table :" + this.table
				+ ";已被截斷");
		return tmpCol;
	}

}
