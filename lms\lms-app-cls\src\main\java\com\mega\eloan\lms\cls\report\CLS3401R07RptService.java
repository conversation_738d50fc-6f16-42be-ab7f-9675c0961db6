package com.mega.eloan.lms.cls.report;

import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01E;
import com.mega.eloan.lms.model.C160S01D;
import com.mega.eloan.lms.model.C340M01A;


public interface CLS3401R07RptService {
	public byte[] gen_ctrTypeC_pdf_contract(C340M01A c340m01a, C160S01D c160s01d, C122M01A c122m01a, C122M01E c122m01e) throws Exception;
	public byte[] gen_ctrTypeC_pdf_deductWageAgrmt(C340M01A c340m01a, C160S01D c160s01d, C122M01A c122m01a, C122M01E c122m01e) throws Exception;
}
