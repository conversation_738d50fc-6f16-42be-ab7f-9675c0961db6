/* 
 * L260S01EDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L260S01E;

/** 公司訪問紀錄表項目檔  **/
public interface L260S01EDao extends IGenericDao<L260S01E> {

	L260S01E findByOid(String oid);
	
	List<L260S01E> findByMainId(String mainId, boolean notIncDel);
	
	L260S01E findByUniqueKey(String mainId, String itemNo);

	List<L260S01E> findByIndex01(String mainId, String itemNo);
}