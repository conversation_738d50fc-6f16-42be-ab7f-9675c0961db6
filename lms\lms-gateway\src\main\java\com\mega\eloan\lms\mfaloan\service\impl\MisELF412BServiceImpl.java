package com.mega.eloan.lms.mfaloan.service.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF412B;
import com.mega.eloan.lms.mfaloan.service.MisELF412BService;

@Service
public class MisELF412BServiceImpl extends AbstractMFAloanJdbc implements
		MisELF412BService {

	@Override
	public List<Map<String, Object>> getByKeyWithBasicData(String branch) {

		return getJdbc().queryForListWithMax(
				"MIS.ELF412B.GetByKeyWithBasicData",
				new String[] { branch, "%", "%", "SYS", "SYS" });

	}

	@Override
	public Map<String, Object> getByKeyWithBasicData(String branch,
			String custId, String dupNo) {

		return getJdbc()
				.queryForMap(
						"MIS.ELF412B.GetByKeyWithBasicData",
						new Object[] { branch, custId + "%", dupNo + "%",
								"SYS", "SYS" });

	}

	@Override
	public Map<String, Object> getDataWithPEO(String branch, String custId,
			String dupNo) {
		return getJdbc()
				.queryForMap(
						"MIS.ELF412B.GetByKeyWithBasicData",
						new Object[] { branch, custId + "%", dupNo + "%",
								"PEO", "PEO" });
	}

	private List<ELF412B> toELF412B(List<Map<String, Object>> rowData) {
		List<ELF412B> list = new ArrayList<ELF412B>();
		for (Map<String, Object> row : rowData) {
			ELF412B model = new ELF412B();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public ELF412B findByPk(String branch, String custId, String dupNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF412B.GetByKey",
				new String[] { branch, custId + "%", dupNo + "%" });
		List<ELF412B> list = toELF412B(rowData);
		if (list.size() == 1) {
			return list.get(0);
		} else {
			return null;
		}
	}

	@Override
	public List<ELF412B> findByBranch(String branch) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"MIS.ELF412B.GetByKey", new String[] { branch, "%", "%" });
		return toELF412B(rowData);
	}

	@Override
	public int updateELF412BNckdFlag(Date ELF412B_LRDATE,
			String ELF412B_NEWADD, String ELF412B_NEWDATE,
			String ELF412B_NCKDFLAG, Date ELF412B_NCKDDATE,
			String ELF412B_NCKDMEMO, Date ELF412B_NEXTNWDT,
			Date ELF412B_NEXTLTDT, Timestamp ELF412B_TMESTAMP,
			Date ELF412B_UPDDATE, String ELF412B_UPDATER,
			String ELF412B_BRANCH, String ELF412B_CUSTID, String ELF412B_DUPNO) {
		int count = this.getJdbc().update(
				"lms1800flow.update.elf412b.nckdflag",
				new Object[] { ELF412B_LRDATE, ELF412B_NEWADD, ELF412B_NEWDATE,
						ELF412B_NCKDFLAG, ELF412B_NCKDDATE,
						Util.trimSizeInOS390(ELF412B_NCKDMEMO, 200),
						ELF412B_NEXTNWDT, ELF412B_NEXTLTDT, ELF412B_TMESTAMP,
						ELF412B_UPDDATE, ELF412B_UPDATER, ELF412B_BRANCH,
						ELF412B_CUSTID, ELF412B_DUPNO });
		return count;
	}

}
