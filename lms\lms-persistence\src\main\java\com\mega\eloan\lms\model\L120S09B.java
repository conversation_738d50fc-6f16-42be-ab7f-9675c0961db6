/* 
 * L120S09B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 洗錢防制主檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S09B", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L120S09B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 資料查詢日
	 * <p/>
	 * YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "QUERYDATES", columnDefinition = "DATE")
	private Date queryDateS;

	/**
	 * 案件調查結果
	 * <p/>
	 * 002 Passed 即Accept, 經調查覆核後可交易<br/>
	 * 003 Failed 即Reject, 經調查覆核後不可交易<br/>
	 * 009 Cancelled 經調查覆核後取消交易<br/>
	 * 012 Pending 案件調查中
	 */
	@Size(max = 3)
	@Column(name = "NCRESULT", length = 3, columnDefinition = "VARCHAR(3)")
	private String ncResult;

	/** 前端系統unique_key **/
	@Size(max = 50)
	@Column(name = "UNIQUEKEY", length = 50, columnDefinition = "VARCHAR(50)")
	private String uniqueKey;

	/** 交易編號或掃描對象編號 **/
	@Size(max = 65)
	@Column(name = "REFNO", length = 65, columnDefinition = "VARCHAR(65)")
	private String refNo;

	/**
	 * 案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "CASEYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer caseYear;

	/**
	 * 案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Size(max = 3)
	@Column(name = "CASEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;

	/**
	 * 案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "CASESEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer caseSeq;

	/**
	 * 案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Size(max = 62)
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/**
	 * 簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	/** 來源類別 **/
	@Size(max = 20)
	@Column(name = "CLASSNAME", length = 20, columnDefinition = "VARCHAR(20)")
	private String className;

	/** 建立人員號碼 **/
	@Size(max = 20)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 案例ID **/
	@Size(max = 50)
	@Column(name = "NCCASEID", length = 50, columnDefinition = "VARCHAR(50)")
	private String ncCaseId;
	
	/** 調查分行 **/
	@Size(max = 3)
	@Column(name = "QUERYBRID", length = 3, columnDefinition = "CHAR(3)")
	private String querybrid;

	/** 調查人員號碼 **/
	@Size(max=6)
	@Column(name="QUERYUSER", length=6, columnDefinition="CHAR(6)")
	private String queryuser;
	
	/** 
	 * 發查方法 
	 * <p/>
	 * Y：tCallSas - Y=是-間接連(0015-10/SWALLOW)<br/>
	 * 				 A=是-直接連(MQ)<br/>
	 * N：tCallSas - 否-N或空白<br/>
	 */
	@Size(max=1)
	@Column(name="WAYMODE", length=1, columnDefinition="CHAR(1)")
	private String waymode;
	
	/** 制裁/管制名單掃描調查結果說明 **/
	@Size(max=3000)
	@Column(name="NCRESULTREMARK", length=3000, columnDefinition="VARCHAR(3000)")
	private String ncResultRemark;

	/** 高風險調查結果說明 **/
	@Size(max=300)
	@Column(name="HIGHRISKREMARK", length=300, columnDefinition="VARCHAR(300)")
	private String highRiskRemark;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得資料查詢日
	 * <p/>
	 * YYYY-MM-01
	 */
	public Date getQueryDateS() {
		return this.queryDateS;
	}

	/**
	 * 設定資料查詢日
	 * <p/>
	 * YYYY-MM-01
	 **/
	public void setQueryDateS(Date value) {
		this.queryDateS = value;
	}

	/**
	 * 取得案件調查結果
	 * <p/>
	 * 002 Passed 即Accept, 經調查覆核後可交易<br/>
	 * 003 Failed 即Reject, 經調查覆核後不可交易<br/>
	 * 009 Cancelled 經調查覆核後取消交易<br/>
	 * 012 Pending 案件調查中
	 */
	public String getNcResult() {
		return this.ncResult;
	}

	/**
	 * 設定案件調查結果
	 * <p/>
	 * 002 Passed 即Accept, 經調查覆核後可交易<br/>
	 * 003 Failed 即Reject, 經調查覆核後不可交易<br/>
	 * 009 Cancelled 經調查覆核後取消交易<br/>
	 * 012 Pending 案件調查中
	 **/
	public void setNcResult(String value) {
		this.ncResult = value;
	}

	/** 取得前端系統unique_key **/
	public String getUniqueKey() {
		return this.uniqueKey;
	}

	/** 設定前端系統unique_key **/
	public void setUniqueKey(String value) {
		this.uniqueKey = value;
	}

	/** 取得交易編號或掃描對象編號 **/
	public String getRefNo() {
		return this.refNo;
	}

	/** 設定交易編號或掃描對象編號 **/
	public void setRefNo(String value) {
		this.refNo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * 取得案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public Integer getCaseYear() {
		return this.caseYear;
	}

	/**
	 * 設定案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseYear(Integer value) {
		this.caseYear = value;
	}

	/**
	 * 取得案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public String getCaseBrId() {
		return this.caseBrId;
	}

	/**
	 * 設定案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/**
	 * 取得案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 */
	public Integer getCaseSeq() {
		return this.caseSeq;
	}

	/**
	 * 設定案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseSeq(Integer value) {
		this.caseSeq = value;
	}

	/**
	 * 取得案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public String getCaseNo() {
		return this.caseNo;
	}

	/**
	 * 設定案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/**
	 * 取得簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public Date getCaseDate() {
		return this.caseDate;
	}

	/**
	 * 設定簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/**
	 * 設定來源類別
	 */
	public void setClassName(String className) {
		this.className = className;
	}

	/**
	 * 取得來源類別
	 */
	public String getClassName() {
		return className;
	}

	/**
	 * 設定案例ID
	 */
	public void setNcCaseId(String ncCaseId) {
		this.ncCaseId = ncCaseId;
	}

	/**
	 * 取得案例ID
	 */
	public String getNcCaseId() {
		return ncCaseId;
	}
	
	/** 取得調查分行 **/
	public String getQueryBrId() {
		return this.querybrid;
	}
	/** 設定調查分行 **/
	public void setQueryBrId(String value) {
		this.querybrid = value;
	}
	/** 取得調查人員號碼 **/
	public String getQueryUser() {
		return this.queryuser;
	}
	/** 設定調查人員號碼 **/
	public void setQueryUser(String value) {
		this.queryuser = value;
	}

	/** 
	 * 發查方法 
	 * <p/>
	 * Y：tCallSas - Y=是-間接連(0015-10/SWALLOW)<br/>
	 * 				 A=是-直接連(MQ)<br/>
	 * N：tCallSas - 否-N或空白<br/>
	 */
	public String getWayMode() {
		return this.waymode;
	}
	/** 
	 * 發查方法 
	 * <p/>
	 * Y：tCallSas - Y=是-間接連(0015-10/SWALLOW)<br/>
	 *               A=是-直接連(MQ)<br/>
	 * N：tCallSas - 否-N或空白<br/>
	 */
	public void setWayMode(String value) {
		this.waymode = value;
	}
	
	/** 取得制裁/管制名單掃描調查結果說明 **/
	public String getNcResultRemark() {
		return this.ncResultRemark;
	}
	/** 設定制裁/管制名單掃描調查結果說明 **/
	public void setNcResultRemark(String value) {
		this.ncResultRemark = value;
	}

	/** 取得高風險調查結果說明 **/
	public String getHighRiskRemark() {
		return this.highRiskRemark;
	}
	/** 設定高風險調查結果說明 **/
	public void setHighRiskRemark(String value) {
		this.highRiskRemark = value;
	}
}
