package com.mega.eloan.lms.dc.base;

import org.apache.commons.lang.StringUtils;
import org.w3c.dom.Document;

public class Select<PERSON>eyChecker implements IKeyChecker {
	private String[] keyAry;
	private String[] valuesAry;
	private boolean useFlag = false;
	private XMLHandler xmlHandler;

	public void init(XMLHandler xmlHandler, String key, String value) {
		this.xmlHandler = xmlHandler;

		if (StringUtils.isNotBlank(key) && !";".equals(key)) {
			useFlag = true;
			this.keyAry = StringUtils.split(key, ';');
			this.valuesAry = StringUtils.split(value, ';');

			if (this.keyAry.length != this.valuesAry.length) {
				throw new DCException("SelectKeys(" + key + ") & SelectValues("
						+ value + ") 之個數不符，請確認之！");
			}
		}
	}

	public boolean isUseChecker() {
		return this.useFlag;
	}

	@Override
	public boolean check(Document domDoc, Object param) throws Exception {
		boolean bChk = false;
		for (int i = 0; i < this.keyAry.length; i++) {
			//20130319 Sandra若selectkey是多組組合，用:或/分隔的分別不同處理
			String fldValue ="";
			if(this.keyAry[i].contains(":")){
				String key[] = this.keyAry[i].split(":");
				for(int x = 0; x < key.length; x++){
					fldValue += this.xmlHandler.getItemValue(domDoc,key[x]);
				}
			}else if(this.keyAry[i].contains("/")){
				String key[] = this.keyAry[i].split("/");
				for(int x = 0; x < key.length; x++){
					fldValue = this.xmlHandler.getItemValue(domDoc,key[x]);
					if(fldValue.length()>0) break;
				}
			}else if(this.keyAry[i].contains("=")){//專為指定所有欄位必須都有值
				String key[] = this.keyAry[i].split("=");
				int gotvalue = 0;
				for(int x = 0; x < key.length; x++){
					fldValue = this.xmlHandler.getItemValue(domDoc,key[x]);
					if(fldValue.length()>0){
						gotvalue++;//計算有值的欄位
					}
				}
				if(gotvalue<key.length){//代表有值的欄位小於欄位數，也就是值有的欄位是空的，則return
					return false;
				}
			}else{
				fldValue = this.xmlHandler.getItemValue(domDoc,
						this.keyAry[i]);
			}
			

			String[] chkValue = StringUtils.split(this.valuesAry[i], '-');
			if (fldValue != null && fldValue.length() > 0) {
				for (String chk : chkValue) {
					if (fldValue.equals(chk)) {
						bChk = true;
						break;
					} else if ("notSpace".equalsIgnoreCase(chk)
							&& fldValue.length() > 0) {
						bChk = true;
						break;
					}
				}
			}
			if (!bChk) {
				return false;
			}
			//bChk = false;
		}
		return true;

	}

}
