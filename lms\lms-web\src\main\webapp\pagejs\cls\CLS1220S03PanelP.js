initDfd.done(function(json){
	var incomType = json.incomType;
	var docStatus = responseJSON.docStatus;
	if(incomType == "2" || docStatus == "03O"){
		$("input[type=text]").readOnly();
		$("input[type=checkbox]").readOnly();
		$("select").readOnly();
		$("button[type=button]").hide();
	}
	
	$('#jobType1').change(function(){
        var code = $(this).val();
        if (code) {
            var item = CommonAPI.loadCombos('jobType' + code);
            $('#jobType2').setItems({
                item: item['jobType' + code],
                format: '{key}',
				disabled : $('#jobType2').prop("disabled")
            });
            //填入 jobType2
            $("#jobType2").val(json.jobType2);
        }
        if($('#jobType1').val() == ''){
        	$("#jobType2").val('');
        }
    });
	$('#jobType1').trigger('change');
	
	
	$("#bcomAddr").click(function(){
        AddrAction.open({
            formId: 'CLS1220S03Form',
            signify: 'com'
        });
    });
	
	$("#bjuId").click(function(){
        var juId = $('#juId').val();
        if(juId == ''){
        	CommonAPI.showErrorMessage("服務單位統一編號必填！");
        }else{
        	$.ajax({
				type : "POST",
				handler : "cls1220m10formhandler",
				data : {
					formAction : "getDwDataByIdNo",
					oid : json.mainOid,
					mainId: json.mainId,
					juId : $('#juId').val()
				},				
			}).done(function(responseData){
				if(responseData.Success){
					$('#comName').val(responseData.comName);
					$('#comTarget').val(responseData.comTarget);
					$('#AddrForm').find("#AddrAddr").val(responseData.AddrAddr);
					$('#AddrForm').find("#AddrCity").val(responseData.AddrCity);
					$('#AddrForm').find("#AddrZip").val(responseData.AddrZip);
					$('#comCity').val(responseData.AddrCity);
                    $('#comZip').val(responseData.AddrZip);
                    $('#comAddr').val(responseData.AddrAddr);
				}else{
					CommonAPI.showErrorMessage("引入稅籍資料失敗！");
					$('#comName').val('');
					$('#comTarget').val('');
					$('#AddrForm').find("#AddrAddr").val('');
					$('#AddrForm').find("#AddrCity").val('');
					$('#AddrForm').find("#AddrZip").val('');
					$('#comCity').val('');
                    $('#comZip').val('');
                    $('#comAddr').val('');
				}
			});
        }
		
    });
	
	/**
	 * 地址
	 */
	var AddrAction = {
	    formId: '',
	    signify: '',
	    open: function(options){
	        AddrAction.formId = options.formId;
	        AddrAction.signify = options.signify;
	        
	        var $form = $('#' + AddrAction.formId);
	        var cityCode = $('#' + AddrAction.signify + 'City').val();
	        var combos = CommonAPI.loadCombos(['counties', 'counties' + cityCode]);
	        
	        var $addrForm = $('#AddrForm');
	        $addrForm.setValue(); // $addrForm reset
	        // 縣市
	        $addrForm.find('#AddrCity').setItems({
	            item: combos['counties'],
	            format: '{key}',
	            value: cityCode,
	            fn: function(){
	                var $addrForm = $('#AddrForm');
	                var cityCode = $addrForm.find('#AddrCity').val();
	                var combos = CommonAPI.loadCombos('counties' + cityCode);
	                $addrForm.find('#AddrZip').setItems({
	                    item: combos['counties' + cityCode],
	                    format: '{key}'
	                });
	            }
	        });
	        // 鄉鎮市區
	        $addrForm.find('#AddrZip').setItems({
	            item: combos['counties' + cityCode],
	            format: '{key}',
	            value: $('#' + AddrAction.signify + 'Zip').val()
	        });
	        // 地址
	        $addrForm.find('#AddrAddr').val($('#' + AddrAction.signify + 'Addr').val() || '');
	        
	        $('#AddrThickBox').thickbox({
	            title: '', // i18n.cms1400v01["title"],
	            width: 500,
	            height: 200,
	            align: 'center',
	            valign: 'bottom',
	            buttons: {
	                'sure': function(){
	                    var $addrForm = $('#AddrForm');
	                    if ($addrForm.valid()) {
	                        var $form = $('#' + AddrAction.formId);
	                        $('#' + AddrAction.signify + 'City').val($addrForm.find('#AddrCity').val());
	                        $('#' + AddrAction.signify + 'Zip').val($addrForm.find('#AddrZip').val());
	                        $('#' + AddrAction.signify + 'Addr').val($addrForm.find('#AddrAddr').val());
	                        $('#' + AddrAction.signify + 'Target').val($addrForm.find('#AddrCity :selected').text() +
	    	                        $addrForm.find('#AddrZip :selected').text() +
	    	                        $addrForm.find('#AddrAddr').val());
	                        $.thickbox.close();
	                    }
	                },
	                'close': function(){
	                    $.thickbox.close();
	                }
	            }
	        });
	    }
	};
	
	
});
