package com.mega.eloan.lms.lrs.handler.form;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;
import java.util.TreeSet;

import javax.annotation.Resource;

import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisLNF022Service;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.common.RO412;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.FlowSimplifyService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.lrs.pages.LMS1810M01Page;
import com.mega.eloan.lms.lrs.service.LMS1810Service;
import com.mega.eloan.lms.mfaloan.bean.ELF412;
import com.mega.eloan.lms.mfaloan.bean.ELF412B;
import com.mega.eloan.lms.mfaloan.bean.ELF412C;
import com.mega.eloan.lms.mfaloan.service.MisELF412BService;
import com.mega.eloan.lms.mfaloan.service.MisELF412CService;
import com.mega.eloan.lms.mfaloan.service.MisELF412Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L181A01A;
import com.mega.eloan.lms.model.L181M01A;
import com.mega.eloan.lms.model.L181M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Scope("request")
@Controller("lms1810m01formhandler")
@DomainClass(L181M01A.class)
public class LMS1810M01FormHandler extends AbstractFormHandler {

	@Resource
	LMSService lmsService;

	@Resource
	LMS1810Service lms1810Service;

	@Resource
	MisELF412Service misELF412Service;

	@Resource
	ICustomerService iCustomerService;

	@Resource
	BranchService branchService;

	@Resource
	RetrialService retrialService;

	@Resource
	TempDataService tempDataService;

	@Resource
	DocCheckService docCheckService;

	@Resource
	MisELF412BService misELF412BService;

	@Resource
	FlowSimplifyService flowSimplifyService;

	@Resource
	MisdbBASEService misdbBaseService;

	@Resource
	MisLNF022Service misLNF022Service;

	@Resource
	EloandbBASEService eloandbBASEService;

	// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	@Resource
	MisELF412CService misELF412CService;

	Properties prop_lms1810m01 = MessageBundleScriptCreator
			.getComponentResource(LMS1810M01Page.class);
	Properties prop_abstractEloanPage = MessageBundleScriptCreator
			.getComponentResource(AbstractEloanPage.class);

	private CapAjaxFormResult defaultResult(PageParameters params,
			L181M01A meta, CapAjaxFormResult result) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// required information
		result.set(EloanConstants.PAGE,
				Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS,
				Util.trim(meta.getDocStatus()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));
		// optional
		boolean isCustIdSaved = StringUtils.isNotBlank(meta.getCustId());
		result.set("isCustIdSaved", isCustIdSaved ? "Y" : "N");
		if (!isCustIdSaved) {
			TreeMap<String, String> tm = retrialService.getBranch(user
					.getUnitNo());
			result.set("br_item", new CapAjaxFormResult(tm));
			result.set("br_itemOrder", new ArrayList<String>(tm.keySet()));
		}

		result.set("titInfo",
				prop_abstractEloanPage.getProperty("typCd." + meta.getTypCd()));
		result.set("custInfo",
				Util.trim(meta.getCustId()) + " " + Util.trim(meta.getDupNo())
						+ " " + Util.trim(meta.getCustName()));

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L181M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL181M01A_oid(mainOid);
			String ctlType = meta.getCtlType();
			String page = params.getString(EloanConstants.PAGE);
			if ("01".equals(page)) {
				{
					// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
					LMSUtil.addMetaToResult(result, meta, new String[] {
							"elfBranch", "custId", "dupNo", "custName",
							"elfUpdDate", "elfUpdater", "elfTmeStamp",
							"elfCancelDt", "randomCode", "ctlType" });
				}

				String elfBranchName = "";
				String docStatusName = prop_abstractEloanPage
						.getProperty("docStatus." + meta.getDocStatus());
				String typCdName = "";
				String cStateDesc = "";
				if (StringUtils.isNotBlank(meta.getCustId())) {
					IBranch ibranch = branchService.getBranch(meta
							.getElfBranch());
					if (ibranch != null) {
						elfBranchName = ibranch.getBrName();
					}
					// ---
					typCdName = prop_abstractEloanPage.getProperty("typCd."
							+ meta.getTypCd());
					// ---
					if (Util.isNotEmpty(meta.getElfCState())) {
						cStateDesc = Util.trim(retrialService.get_lrs_CState()
								.get(meta.getElfCState()));
					}
				}
				result.set("elfBranchName", elfBranchName);
				result.set("docStatusName", docStatusName);
				result.set("typCdName", typCdName);
				result.set("cStateDesc", cStateDesc);

				result.set(
						"creator",
						Util.trim(meta.getCreator())
								+ " "
								+ Util.trim(lmsService.getUserName(meta
										.getCreator())) + "("
								+ TWNDate.toFullAD(meta.getCreateTime()) + ")");
				result.set(
						"updater",
						Util.trim(meta.getUpdater())
								+ " "
								+ Util.trim(lmsService.getUserName(meta
										.getUpdater())) + "("
								+ TWNDate.toFullAD(meta.getUpdateTime()) + ")");
				result.set("apprId",
						Util.trim(lmsService.getUserName(get_apprId(meta))));
				result.set("approverCN",
						Util.trim(lmsService.getUserName(meta.getApprover())));

				// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				result.set("cltTypeForShow", LMSUtil.getDesc(
						retrialService.get_lrs_CtlTypeMap(),
						Util.trim(meta.getCtlType())));

			} else if ("02".equals(page)) {
				{
					L181M01B bf = retrialService.findL181M01B_mainid_Bf(meta
							.getMainId());
					L181M01B af = retrialService.findL181M01B_mainid_Af(meta
							.getMainId());

					Map<String, Object> bfMap = new HashMap<String, Object>();
					Map<String, Object> afMap = new HashMap<String, Object>();
					if (true) {
						// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
						// elfIsAllNew：J-108-0078_05097_B1001
						// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
						String[] colArr = new String[] { "elfRCkdLine",
								"elfUCkdLINE", "elfUCkdDt", "elfMainCust",
								"elfLRDate", "elfCrdTTbl", "elfDBUOBU",
								"elfMowType", "elfMowTbl1", "elfMDFlag",
								"elfMDDt", "elfProcess", "elfNewAdd",
								"elfNewDate", "elfNCkdFlag", "elfNCkdMemo",
								"elfNextNwDt", "elfMemo", "elfFcrdType",
								"elfFcrdArea", "elfFcrdPred", "elfFcrdGrad",
								"elfRealCkFg", "elfRealDt", "elfOldRptId",
								"elfOldRptDt", "elfNewRptId", "elfNewRptDt",
								"elfIsAllNew", "elfIsRescue", "elfGuarFlag",
								"elfNewRescue", "elfNewRescueYM", "elfRandomType" };
						Map<String, Object> tmpMap = new HashMap<String, Object>();

						LMSUtil.meta_to_map(tmpMap, bf, colArr);
						LMSUtil.meta_to_map(afMap, af, colArr);
						for (String k : tmpMap.keySet()) {
							bfMap.put(k + "_1", tmpMap.get(k));
						}
						// ---
						for (String k : bfMap.keySet()) {
							bfMap.put(k, Util.trim(bfMap.get(k)));
						}
						for (String k : afMap.keySet()) {
							afMap.put(k, Util.trim(afMap.get(k)));
						}
						// ===
						afMap.put("ndDate",
								Util.trim(TWNDate.toAD(af.getNdDate())));

						// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
						afMap.put("ctlType", Util.trim(ctlType));
						afMap.put("cltTypeForShow", LMSUtil.getDesc(
								retrialService.get_lrs_CtlTypeMap(),
								Util.trim(meta.getCtlType())));

						// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
						// (原始)
						bfMap.put("elfOldDocNo_1", "");
						if (Util.notEquals(Util.trim(MapUtils.getObject(bfMap,
								"elfOldRptId_1", "")), "")) {
							L120M01A l120m01a = retrialService
									.findL120M01A_mainId(Util.trim(MapUtils
											.getObject(bfMap, "elfOldRptId_1",
													"")));
							if (l120m01a != null) {
								bfMap.put("elfOldDocNo_1", l120m01a.getCaseNo());
							}
						}

						bfMap.put("elfNewDocNo_1", "");
						if (Util.notEquals(Util.trim(MapUtils.getObject(bfMap,
								"elfNewRptId_1", "")), "")) {
							L120M01A l120m01a = retrialService
									.findL120M01A_mainId(Util.trim(MapUtils
											.getObject(bfMap, "elfNewRptId_1",
													"")));
							if (l120m01a != null) {
								bfMap.put("elfNewDocNo_1", l120m01a.getCaseNo());
							}
						}

						// (異動)
						afMap.put("elfOldDocNo", "");
						if (Util.notEquals(Util.trim(MapUtils.getObject(afMap,
								"elfOldRptId", "")), "")) {
							L120M01A l120m01a = retrialService
									.findL120M01A_mainId(Util.trim(MapUtils
											.getObject(afMap, "elfOldRptId", "")));
							if (l120m01a != null) {
								afMap.put("elfOldDocNo", l120m01a.getCaseNo());
							}
						}

						afMap.put("elfNewDocNo", "");
						if (Util.notEquals(Util.trim(MapUtils.getObject(afMap,
								"elfNewRptId", "")), "")) {
							L120M01A l120m01a = retrialService
									.findL120M01A_mainId(Util.trim(MapUtils
											.getObject(afMap, "elfNewRptId", "")));
							if (l120m01a != null) {
								afMap.put("elfNewDocNo", l120m01a.getCaseNo());
							}
						}

						// J-110-0272 抽樣覆審
						afMap.put("elfRandomTypeForShow", LMSUtil.getDesc(
								retrialService.get_codeTypeWithOrder("lms1815m01_elfRandomType"),
								Util.trim(af.getElfRandomType())));
					}
					result.putAll(bfMap);
					result.putAll(afMap);
				}

				CapAjaxFormResult selItem = new CapAjaxFormResult();
				CapAjaxFormResult selItemOrder = new CapAjaxFormResult();
				if (true) {
					_setMapWithOrder(selItem, selItemOrder, "elfRCkdLine",
							retrialService.get_lrs_RckdLine());
					_setMapWithOrder(selItem, selItemOrder, "elfNewAdd",
							retrialService.get_lrs_NewAdd());
					_setMapWithOrder(selItem, selItemOrder, "elfNCkdFlag",
							retrialService.get_lrs_NckdFlagMap());
					_setMapWithOrder(selItem, selItemOrder, "elfMDFlag",
							retrialService.get_lrs_MdFlagMap());
					_setMapWithOrder(selItem, selItemOrder, "elfMowType",
							retrialService.get_lrs_MowType_1());
					_setMapWithOrder(selItem, selItemOrder, "elfMowTbl1",
							retrialService.get_lrs_MowTbl());
					_setMapWithOrder(selItem, selItemOrder, "elfCrdTTbl",
							retrialService.get_lrs_CrdtTbl());
					_setMapWithOrder(selItem, selItemOrder, "elfFcrdType",
							retrialService.get_lrs_FcrdType());
					_setMapWithOrder(selItem, selItemOrder, "elfFcrdArea",
							retrialService.get_lrs_FcrdArea());
					_setMapWithOrder(selItem, selItemOrder, "elfFcrdPred",
							retrialService.get_lrs_FcrdPred());
				}
				result.set("selItem", selItem);
				result.set("selItemOrder", selItemOrder);
			}
		}

		return defaultResult(params, meta, result);
	}

	private void _setMapWithOrder(CapAjaxFormResult result,
			CapAjaxFormResult rItemOrder, String elm, Map<String, String> m) {
		List<String> ord = new ArrayList<String>();
		if (m instanceof LinkedHashMap) {
			for (String k : m.keySet()) {
				ord.add(k);
			}
		} else {
			TreeSet<String> ts = new TreeSet<String>();
			ts.addAll(m.keySet());
			ord.addAll(ts);
		}
		result.set(elm, new CapAjaxFormResult(m));
		rItemOrder.set(elm, ord);
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newMain(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();
		L181M01A meta = new L181M01A();
		meta.setMainId(IDGenerator.getUUID());
		meta.setOwnBrId(user.getUnitNo());
		meta.setUnitType(UnitTypeEnum.convertToUnitType(user.getUnitType()));
		meta.setTypCd(TypCdEnum.DBU.getCode());
		meta.setDeletedTime(null);
		if (true) {
			// 授權檔
			L181A01A l181a01a = new L181A01A();
			l181a01a.setMainId(meta.getMainId());
			l181a01a.setOwnUnit(meta.getOwnBrId());
			l181a01a.setOwner(user.getUserId());
			l181a01a.setAuthUnit(meta.getOwnBrId());
			l181a01a.setAuthType("1");
			retrialService.save(l181a01a);
		}
		retrialService.save(meta);

		flowSimplifyService.flowStart("LMS1815Flow", meta.getOid(),
				user.getUserId(), user.getUnitNo());

		return defaultResult(params, meta, result);
	}

	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params)
			throws CapException {
		return _saveAction(params, "Y");
	}

	private CapAjaxFormResult _saveAction(PageParameters params,
			String tempSave) throws CapException,
			CapMessageException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		// ===
		String KEY = "saveOkFlag";
		Properties prop_lms1810m01 = MessageBundleScriptCreator
				.getComponentResource(LMS1810M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L181M01A meta = null;

		// J-108-0078_05097_B1001 Web e-Loan企金授信覆審系統修改首次往來之新授信戶應辦理覆審之期限
		boolean isRckdLineIEffective = retrialService
				.isRckdLine_I_Effective(CapDate.getCurrentTimestamp());

		if (Util.isNotEmpty(mainOid)) {
			try {
				meta = retrialService.findL181M01A_oid(mainOid);
				String page = params.getString(EloanConstants.PAGE);
				L181M01B af = retrialService.findL181M01B_mainid_Af(meta
						.getMainId());
				if ("02".equals(page)) {
					// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
					// elfIsAllNew：J-108-0078_05097_B1001
					// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
					String[] colArr = new String[] { "elfRCkdLine",
							"elfUCkdLINE", "elfUCkdDt", "elfMainCust",
							"elfLRDate", "elfCrdTTbl", "elfDBUOBU",
							"elfMowType", "elfMowTbl1", "elfMDFlag", "elfMDDt",
							"elfProcess", "elfNewAdd", "elfNewDate",
							"elfNCkdFlag", "elfNCkdMemo", "elfNextNwDt",
							"elfMemo", "elfFcrdType", "elfFcrdArea",
							"elfFcrdPred", "elfFcrdGrad", "elfRealCkFg",
							"elfRealDt", "elfOldRptId", "elfOldRptDt",
							"elfNewRptId", "elfNewRptDt", "elfIsAllNew",
							"elfIsRescue", "elfGuarFlag", "elfNewRescue",
							"elfNewRescueYM", "elfRandomType" };
					CapBeanUtil.map2Bean(params, af, colArr);
					// ---
					if (Util.notEquals(LrsUtil.NCKD_8_本次暫不覆審,
							af.getElfNCkdFlag())) {
						if (af.getElfNextNwDt() != null) {
							af.setElfNextNwDt(null);
						}
					}
					// ---
					retrialService.save(af);
				}
				// ---
				retrialService.save(meta);

				// ===========================
				// 參考 fnSaveCheck
				if (Util.isEmpty(meta.getElfBranch())
						|| Util.isEmpty(meta.getCustId())) {
					throw new CapMessageException("分行代碼與客戶統編欄位不得空白!!",
							getClass());
				}
				if (Util.notEquals("Y", tempSave) && af != null) {
					if (Util.isNotEmpty(Util.trim(af.getElfMowType()))
							&& Util.isEmpty(Util.trim(af.getElfMowTbl1()))) {
						throw new CapMessageException(
								"信用模型評等類別有值，信用模型評等欄位不得空白", getClass());
					}
					if (Util.isEmpty(Util.trim(af.getElfMowType()))
							&& Util.isNotEmpty(Util.trim(af.getElfMowTbl1()))) {
						throw new CapMessageException(
								"信用模型評等欄位有值，信用模型評等類別不得空白", getClass());
					}
					if (Util.isNotEmpty(Util.trim(af.getElfNewAdd()))
							&& Util.isEmpty(Util.trim(af.getElfNewDate()))) {
						throw new CapMessageException("新作/增額有值，新作/增額年月欄位不得空白",
								getClass());
					}
					if (Util.isEmpty(Util.trim(af.getElfNewAdd()))
							&& Util.isNotEmpty(Util.trim(af.getElfNewDate()))) {
						throw new CapMessageException("新作/增額年月有值，新作/增額不得空白",
								getClass());
					}

					// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
					if (Util.equals("Y", Util.trim(af.getElfNewRescue()))
							&& Util.isEmpty(Util.trim(af.getElfNewRescueYM()))) {
						throw new CapMessageException("新作紓困註記有值，新作紓困年月欄位不得空白",
								getClass());
					}
					if (Util.isEmpty(Util.trim(af.getElfNewRescue()))
							&& Util.isNotEmpty(Util.trim(af.getElfNewRescueYM()))) {
						throw new CapMessageException("新作紓困年月有值，新作紓困註記不得空白",
								getClass());
					}
					if(Util.notEquals("Y", Util.trim(af.getElfNewRescue()))
							&& Util.isNotEmpty(Util.trim(af.getElfNewRescueYM()))){
						throw new CapMessageException("新作紓困註記不為是，新作紓困年月欄位必須空白",
								getClass());
					}
                    if(Util.equals("Y", Util.trim(af.getElfIsRescue()))
                            && Util.isEmpty(Util.trim(af.getElfGuarFlag()))){
                        throw new CapMessageException("純紓困為是，信保擔保註記不得空白",
                                getClass());
                    }

					// J-108-0078_05097_B1001
					// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
					if (Util.equals(Util.trim(meta.getCtlType()),
							LrsUtil.CTLTYPE_主辦覆審)) {

						if (isRckdLineIEffective) {
							if (Util.isNotEmpty(Util.trim(af.getElfNewAdd()))
									&& Util.isNotEmpty(Util.trim(af
											.getElfNewDate()))) {
								if (Util.isEmpty(Util.trim(af.getElfIsAllNew()))) {
									throw new CapMessageException(
											"新作/增額年月有值，首次往來之新貸戶不得空白",
											getClass());
								}
							}
						} else {

							if (Util.isNotEmpty(Util.trim(af.getElfIsAllNew()))) {
								throw new CapMessageException("首次往來之新貸戶必須空白",
										getClass());
							}

						}

                        if (Util.notEquals("J", af.getElfRCkdLine())) {
                            if (Util.isNotEmpty(Util.trim(af.getElfIsAllNew()))) {
                                if (Util.isEmpty(Util.trim(af.getElfNewAdd()))
                                        || Util.isEmpty(Util.trim(af
                                        .getElfNewDate()))) {

                                    throw new CapMessageException(
                                            "首次往來之新貸戶有值，「新作/增額」或「新作/增額年月」不得空白",
                                            getClass());

                                }
                            }
                        }
					}

					// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
					if (Util.equals(Util.trim(meta.getCtlType()),
							LrsUtil.CTLTYPE_自辦覆審)) {
						if (Util.isNotEmpty(Util.trim(af.getElfNewAdd()))
								&& Util.isEmpty(Util.trim(af.getElfNewRptId()))) {
							throw new CapMessageException(
									"新作/增額有值，新作/增額簽報書案號與核准日期欄位不得空白", getClass());
						}

						if (Util.isEmpty(Util.trim(af.getElfOldRptId()))) {
							throw new CapMessageException("原簽報書案號與核准日期欄位不得空白",
									getClass());
						}
					}

					if (Util.isNotEmpty(Util.trim(af.getElfMDFlag()))) {
						if (CrsUtil.isNull_or_ZeroDate(af.getElfMDDt())) {
							throw new CapMessageException(
									"異常通報代碼有值，異常通報日期欄位不得空白", getClass());
						}
					} else {
						if (CrsUtil.isNOT_null_and_NOTZeroDate(af.getElfMDDt())
								|| Util.isNotEmpty(Util.trim(af.getElfProcess()))) {
							throw new CapMessageException(
									"無異常通報代碼，異常通報日期與異常通報情形欄位必須為空白", getClass());
						}
					}

					if (Util.isEmpty(Util.trim(af.getElfNCkdFlag()))) {
						if (Util.isNotEmpty(Util.trim(af.getElfNCkdMemo()))) {
							throw new CapMessageException("無不覆審代碼，不覆審備註必須為空白",
									getClass());
						}
					} else {

					}

					if (Util.equals("Y", af.getElfUCkdLINE())) {
						if (CrsUtil.isNull_or_ZeroDate(af.getElfUCkdDt())) {
							throw new CapMessageException(
									"本客戶為主管機關指定覆審案件，主管機關通知日期不可空白", getClass());
						}
					}

					// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
					if (Util.notEquals(Util.trim(meta.getCtlType()),
							LrsUtil.CTLTYPE_自辦覆審)
							&& Util.notEquals(Util.trim(meta.getCtlType()),
									LrsUtil.CTLTYPE_價金履約)) {
						// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
						if (Util.equals("", Util.trim(af.getElfRealCkFg()))) {
							// err.check17=實地覆審案件欄位不得空白！
							throw new CapMessageException(
									prop_lms1810m01.getProperty("err.check17"),
									getClass());

						}
					}

					if (Util.equals("Y", af.getElfRealCkFg())) {
						if (CrsUtil.isNull_or_ZeroDate(af.getElfRealDt())) {
							// err.check13=本客戶為實地覆審案件，實地覆審基準日期不可空白
							throw new CapMessageException(
									prop_lms1810m01.getProperty("err.check13"),
									getClass());
						}
					}

					if (!CrsUtil.isNull_or_ZeroDate(af.getElfRealDt())) {
						if (Util.notEquals("Y", af.getElfRealCkFg())) {
							// err.check14=本客戶實地覆審基準日不為空白，實地覆審案件不可為空白或否！
							throw new CapMessageException(
									prop_lms1810m01.getProperty("err.check14"),
									getClass());
						}

					}

					// END
					// J-105-0287************************************************

					if (Util.isEmpty(Util.trim(af.getElfRCkdLine()))) {
						throw new CapMessageException("覆審週期不得為空白", getClass());
					}

					// J-110-0272 抽樣覆審
					// 不覆審代碼13 僅供系統判斷用，不可經由人工更改成不覆審
					// 因為 LrsUtil.isNckdFlag_EMPTY_8 含有 13 會pass  所以提前先檢查
					L181M01B bf = retrialService.findL181M01B_mainid_Bf(meta.getMainId());
					if(bf != null) {	// 原本不為13 改成13才要檢核
						if (Util.notEquals(bf.getElfNCkdFlag(),
								LrsUtil.NCKD_13_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件_未列於抽樣需覆審名單內)) {
							if (Util.equals(af.getElfNCkdFlag(),
									LrsUtil.NCKD_13_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件_未列於抽樣需覆審名單內)) {
								throw new CapMessageException("不覆審代碼不得為【 13 】。", getClass());
							}
						}
					}

					if (LrsUtil.isNckdFlag_EMPTY_8(Util.trim(af
							.getElfNCkdFlag()))) {
						// ...
					} else {
						if (Util.isNotEmpty(Util.trim(af.getElfNewAdd()))
								|| Util.isNotEmpty(Util.trim(af.getElfNewDate()))) {
							throw new CapMessageException(
									"不覆審案件，新作/增額年月欄位不得有值", getClass());
						}

						// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
						if (Util.equals("Y", Util.trim(af.getElfNewRescue()))
								|| Util.isNotEmpty(Util.trim(af.getElfNewRescueYM()))) {
							throw new CapMessageException(
									"不覆審案件，新作紓困欄位不得有值", getClass());
						}
						if (Util.equals(LrsUtil.NCKD_7_銷戶, af.getElfNCkdFlag())) {
							if (Util.isNotEmpty(Util.trim(af.getElfIsRescue()))
									|| Util.isNotEmpty(Util.trim(af.getElfGuarFlag()))
									|| Util.isNotEmpty(Util.trim(af.getElfNewRescue()))
									|| Util.isNotEmpty(Util.trim(af.getElfNewRescueYM()))) {
								throw new CapMessageException(
										"銷戶案件，紓困、信保擔保欄位不得有值", getClass());
							}
						}

						if (CrsUtil.isNull_or_ZeroDate(af.getElfLRDate())) {
							throw new CapMessageException("不覆審案件，上次覆審日期不得為空白",
									getClass());
						}

						// 非小規模覆審不能選11
                        if(Util.equals(af.getElfNCkdFlag(), LrsUtil.NCKD_11_小規模營業人_央行C方案_已抽樣覆審於次年起免辦覆審或未列於抽樣需覆審名單內)){
                            // J-109-0313 小規模覆審 - 以最新判斷為主
                            String[] arr = lmsService.getOnlySmallBussCaseC(Util.trim(meta.getCustId()), Util.trim(meta.getDupNo()));
                            if(Util.notEquals(arr[0], "Y")){
								throw new CapMessageException("非純小規模營業人，不覆審代碼不得為【 11 】。",
										getClass());
							}
                        }

                        // J-109-0456 五百萬元(含)以下、信保七成(含)以上、不循環動用
                        // J-110-0272 不覆審代碼12條件放寬
                        //  新臺幣一千萬元以下且為十足擔保授信或經信用保證基金保證成數七成以上，均為不循環動用額度者，免再辦理覆審。倘含循環動用者，抽樣覆審。
                        if(Util.equals(af.getElfNCkdFlag(), LrsUtil.NCKD_12_有效額度NTD1000w以下信保七成以上或十足擔保之不循環案件_已於新作增貸後辦理一次覆審)){
							if(!retrialService.isNckd_12(Util.trim(meta.getCustId()), Util.trim(meta.getDupNo()))){
								throw new CapMessageException("條件不符合，不覆審代碼不得為【 12 】。", getClass());
							}
						}
					}
					// '判斷覆審週期相關資料
					if (Util.equals("A", af.getElfRCkdLine())) {

						// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
						if (Util.equals(Util.trim(meta.getCtlType()),
								LrsUtil.CTLTYPE_自辦覆審)) {
							// 自辦覆審
							// J-106-0278-001 修改常董會或董事會權限授信實地覆審案件覆審週期計算
							// 107年1月1之後新作「常董會（或董事會）權限」授信案件，應於撥貸後半年內辦理首次實地覆審，之後並每年覆審一次。

							String afNewAdd = Util.trim(af.getElfNewAdd());
							Date afNewDate = LrsUtil
									.model_elfNewDate_to_Date(af
											.getElfNewDate());
							if (Util.equals(Util.trim(af.getElfNewAdd()), "N")
									&& LMSUtil.cmp_yyyyMM(afNewDate, ">=",
											Util.parseDate("2018-01-01"))) {
								// J-108-0087_05097_B1002 Web
								// e-Loan企金覆審逾期未覆審報表修改「備註說明」等欄位。
								// throw new CapMessageException(
								// "新作/增額/逾放轉正註記為【新作】，且新作年月超過【107/01】，覆審週期不得為【"
								// + af.getElfRCkdLine()
								// + "】，建議為【C】半年覆審一次。", getClass());
							}

						} else if (Util.equals(Util.trim(meta.getCtlType()),
								LrsUtil.CTLTYPE_價金履約)) {

						} else {
							if (CrsUtil.isNull_or_ZeroDate(af.getElfLRDate())) {
								throw new CapMessageException(
										"覆審週期為 " + af.getElfRCkdLine()
												+ " ，上次覆審日期不得為空白", getClass());
							}

							if (Util.isNotEmpty(Util.trim(af.getElfNewAdd()))
									|| Util.isNotEmpty(Util.trim(af
											.getElfNewDate()))) {
								throw new CapMessageException(
										"新作/增額有值，覆審週期不得為  "
												+ af.getElfRCkdLine(),
										getClass());
							}

							// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
							if (Util.equals("Y", Util.trim(af.getElfNewRescue()))
									|| Util.isNotEmpty(Util.trim(af.getElfNewRescueYM()))) {
								throw new CapMessageException(
										"新作紓困有值，覆審週期不得為  "+ af.getElfRCkdLine(), getClass());
							}

							if (Util.isNotEmpty(Util.trim(af.getElfMDFlag()))) {
								throw new CapMessageException("覆審週期為 "
										+ af.getElfRCkdLine() + " ，異常通報代碼必須空白",
										getClass());
							}
							if (!CrsUtil.isNull_or_ZeroDate(af.getElfMDDt())) {
								throw new CapMessageException(
										"覆審週期為 " + af.getElfRCkdLine()
												+ " ，異常通報日期必須為空白", getClass());
							}
						}

					} else if (Util.equals("B", af.getElfRCkdLine())) {
						if (CrsUtil.isNull_or_ZeroDate(af.getElfLRDate())) {
							throw new CapMessageException("覆審週期為 "
									+ af.getElfRCkdLine() + " ，上次覆審日期不得為空白",
									getClass());
						}
						if (Util.isEmpty(Util.trim(af.getElfMainCust()))) {
							throw new CapMessageException("覆審週期為 "
									+ af.getElfRCkdLine() + " ，符合授信額度標準 不得為空白",
									getClass());
						}
						if (Util.isNotEmpty(Util.trim(af.getElfNewAdd()))
								|| Util.isNotEmpty(Util.trim(af.getElfNewDate()))) {
							throw new CapMessageException("新作/增額有值，覆審週期不得為  "
									+ af.getElfRCkdLine(), getClass());
						}
					} else if (Util.equals("C", af.getElfRCkdLine())) {

						if (Util.isEmpty(Util.trim(af.getElfNewAdd()))) {
							throw new CapMessageException("覆審週期為 "
									+ af.getElfRCkdLine() + " ，新作增額代碼不得為空白",
									getClass());
						}
						if (Util.isEmpty(Util.trim(af.getElfNewDate()))) {
							throw new CapMessageException("覆審週期為 "
									+ af.getElfRCkdLine() + " ，新作增額年月不得為空白",
									getClass());
						}

						// J-106-0278-001 修改常董會或董事會權限授信實地覆審案件覆審週期計算
						// 107年1月1之後新作「常董會（或董事會）權限」授信案件，應於撥貸後半年內辦理首次實地覆審，之後並每年覆審一次。

						// J-108-0087_05097_B1002 Web
						// e-Loan企金覆審逾期未覆審報表修改「備註說明」等欄位。
						// 自辦覆審改成一律一年一次
						// 三、就營業單位應辦理實地覆審之董事會（或常董會）權限核定之企業戶授信案件，其中首次往來新授信戶，營業單位應於動撥後之一年內辦理實地覆審，舊戶新做、增貸案件則併於原覆審週期(即一年一次)之期限內辦理實地覆審。另對程式修改前「營業單位逾期未覆審名單(董事會/常董會授權案件)報表」上有列出新做、增貸案件加計動撥後半年內未覆審之案件，請免列入該表。
						// if (Util.equals(Util.trim(meta.getCtlType()),
						// LrsUtil.CTLTYPE_自辦覆審)) {
						//
						// // 自辦覆審
						// String afNewAdd = Util.trim(af.getElfNewAdd());
						// Date afNewDate = LrsUtil
						// .model_elfNewDate_to_Date(af
						// .getElfNewDate());
						//
						// if (!(Util
						// .equals(Util.trim(af.getElfNewAdd()), "N") && LMSUtil
						// .cmp_yyyyMM(afNewDate, ">=",
						// Util.parseDate("2018-01-01")))) {
						// throw new CapMessageException(
						// "只有當新作/增額/逾放轉正註記為【新作】，且新作年月超過【107/01】時，覆審週期才能為【C】半年覆審一次。",
						// getClass());
						//
						// }
						// }

						// J-108-0078_05097_B1001
						// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
						if (Util.equals(Util.trim(meta.getCtlType()),
								LrsUtil.CTLTYPE_主辦覆審)) {

							if (isRckdLineIEffective) {
								if (Util.isEmpty(Util.trim(af.getElfIsAllNew()))) {
									throw new CapMessageException("覆審週期為 "
											+ af.getElfRCkdLine()
											+ " ，首次往來之新貸戶不得為空白", getClass());
								}
							} else {
								if (Util.isNotEmpty(Util.trim(af
										.getElfIsAllNew()))) {
									throw new CapMessageException(
											"首次往來之新貸戶必須為空白", getClass());
								}
							}

							// 主辦覆審
							String isAllNew = Util.trim(af.getElfIsAllNew());
							String brNo = Util.trim(meta.getElfBranch());

							// 判斷首次往來之新貸戶且非"079"分行則為"I",否則為"C"
							String needRckdLine = LrsUtil
									.getNewAddRckdLineForElf412(isAllNew, brNo);
							if (Util.notEquals(needRckdLine,
									af.getElfRCkdLine())) {
								throw new CapMessageException("覆審週期應該為【"
										+ needRckdLine + "】。", getClass());
							}

						}

						// J-108-0087_05097_B1002 Web
						// e-Loan企金覆審逾期未覆審報表修改「備註說明」等欄位。
						if (Util.equals(Util.trim(meta.getCtlType()),
								LrsUtil.CTLTYPE_自辦覆審)) {
							throw new CapMessageException(
									"董事會(或常董會)權限案件實地覆審案件週期只能為A.一年覆審一次。 ",
									getClass());
						}

					} else if (Util.equals("I", af.getElfRCkdLine())) {
						// J-108-0078_05097_B1001
						// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
						if (Util.isEmpty(Util.trim(af.getElfNewAdd()))) {
							throw new CapMessageException("覆審週期為 "
									+ af.getElfRCkdLine() + " ，新作增額代碼不得為空白",
									getClass());
						}
						if (Util.isEmpty(Util.trim(af.getElfNewDate()))) {
							throw new CapMessageException("覆審週期為 "
									+ af.getElfRCkdLine() + " ，新作增額年月不得為空白",
									getClass());
						}

						// J-108-0078_05097_B1001
						// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
						if (Util.equals(Util.trim(meta.getCtlType()),
								LrsUtil.CTLTYPE_主辦覆審)) {

							if (isRckdLineIEffective) {
								if (Util.isEmpty(Util.trim(af.getElfIsAllNew()))) {
									throw new CapMessageException("覆審週期為 "
											+ af.getElfRCkdLine()
											+ " ，首次往來之新貸戶不得為空白", getClass());
								}
							} else {
								if (Util.isNotEmpty(Util.trim(af
										.getElfIsAllNew()))) {
									throw new CapMessageException(
											"首次往來之新貸戶必須為空白", getClass());
								}
							}

							// 主辦覆審
							String isAllNew = Util.trim(af.getElfIsAllNew());
							String brNo = Util.trim(meta.getElfBranch());

							// 判斷首次往來之新貸戶且非"079"分行則為"I",否則為"C"
							String needRckdLine = LrsUtil
									.getNewAddRckdLineForElf412(isAllNew, brNo);
							if (Util.notEquals(needRckdLine,
									af.getElfRCkdLine())) {
								throw new CapMessageException("覆審週期應該為【"
										+ needRckdLine + "】。", getClass());
							}

						} else {
							throw new CapMessageException("覆審週期不得為【"
									+ af.getElfRCkdLine() + "】。", getClass());
						}

					} else if (Util.equals("J", af.getElfRCkdLine())) {
                        // 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
//                        String isRescue = retrialService.getOnlyRescueCase(meta.getElfBranch(),
//                                meta.getCustId(), meta.getDupNo());
                        if (Util.isEmpty(Util.trim(af.getElfIsRescue()))) {
                            throw new CapMessageException("覆審週期為 "
                                    + af.getElfRCkdLine() + " ，純紓困不得為空白",
                                    getClass());
                        }
                        if (Util.notEquals(Util.trim(af.getElfIsRescue()), "Y")) {
                            throw new CapMessageException("覆審週期為 "
                                    + af.getElfRCkdLine() + " ，純紓困必須為是",
                                    getClass());
                        }
                        if (Util.isEmpty(Util.trim(af.getElfGuarFlag()))) {
                            throw new CapMessageException("覆審週期為 "
                                    + af.getElfRCkdLine() + " ，信保擔保註記不得為空白",
                                    getClass());
                        }
                        if (Util.notEquals(Util.trim(af.getElfGuarFlag()), "Y")) {
                            throw new CapMessageException("覆審週期為 "
                                    + af.getElfRCkdLine() + " ，信保擔保註記必須為是",
                                    getClass());
                        }
                        if (Util.isEmpty(Util.trim(af.getElfNewRescue()))) {
                            throw new CapMessageException("覆審週期為 "
                                    + af.getElfRCkdLine() + " ，新作紓困註記不得為空白",
                                    getClass());
                        }
						if (Util.notEquals(Util.trim(af.getElfNewRescue()), "Y")) {
							throw new CapMessageException("覆審週期為 "
									+ af.getElfRCkdLine() + " ，新作紓困註記必須為是",
									getClass());
						}
                        if (Util.isEmpty(Util.trim(af.getElfNewRescueYM()))) {
                            throw new CapMessageException("覆審週期為 "
                                    + af.getElfRCkdLine() + " ，新作紓困年月不得為空白",
                                    getClass());
                        }
						if (Util.isNotEmpty(Util.trim(af.getElfNewAdd()))
								|| Util.isNotEmpty(Util.trim(af.getElfNewDate()))) {
							throw new CapMessageException("覆審週期為 "
									+ af.getElfRCkdLine() + "新作/增額不得有值", getClass());
						}

                        if (Util.equals(Util.trim(meta.getCtlType()),
                                LrsUtil.CTLTYPE_主辦覆審)) {

                        } else {
                            throw new CapMessageException("覆審週期不得為【"
                                    + af.getElfRCkdLine() + "】。", getClass());
                        }

					} else if (Util.equals("D", af.getElfRCkdLine())
							|| Util.equals("F", af.getElfRCkdLine())) {
						if (CrsUtil.isNull_or_ZeroDate(af.getElfLRDate())) {
							throw new CapMessageException("覆審週期為 "
									+ af.getElfRCkdLine() + " ，上次覆審日期不得為空白",
									getClass());
						}
						if (Util.isEmpty(Util.trim(af.getElfMDFlag()))) {
							throw new CapMessageException("覆審週期為 "
									+ af.getElfRCkdLine() + " ，異常通報代碼不得為空白",
									getClass());
						}
						if (CrsUtil.isNull_or_ZeroDate(af.getElfMDDt())) {
							throw new CapMessageException("覆審週期為 "
									+ af.getElfRCkdLine() + " ，異常通報日期不得為空白",
									getClass());
						}
						if (Util.isNotEmpty(Util.trim(af.getElfNewAdd()))
								|| Util.isNotEmpty(Util.trim(af.getElfNewDate()))) {
							throw new CapMessageException("新作/增額有值，覆審週期不得為  "
									+ af.getElfRCkdLine(), getClass());
						}
					} else if (Util.equals("E", af.getElfRCkdLine())
							|| Util.equals("G", af.getElfRCkdLine())) {
						if (Util.isEmpty(Util.trim(af.getElfMDFlag()))) {
							throw new CapMessageException("覆審週期為 "
									+ af.getElfRCkdLine() + " ，異常通報代碼不得為空白",
									getClass());
						}
						if (CrsUtil.isNull_or_ZeroDate(af.getElfMDDt())) {
							throw new CapMessageException("覆審週期為 "
									+ af.getElfRCkdLine() + " ，異常通報日期不得為空白",
									getClass());
						}
					} else if (Util.equals("H", af.getElfRCkdLine())) {
						if (Util.isEmpty(Util.trim(af.getElfUCkdLINE()))) {
							throw new CapMessageException("覆審週期為 "
									+ af.getElfRCkdLine()
									+ " ，主管機關指定覆審案件代碼不得為空白", getClass());
						}
						if (CrsUtil.isNull_or_ZeroDate(af.getElfUCkdDt())) {
							throw new CapMessageException("覆審週期為 "
									+ af.getElfRCkdLine() + " ，主管機關通知日期不得為空白",
									getClass());
						}
					}

					if (Util.equals(LrsUtil.NCKD_8_本次暫不覆審, af.getElfNCkdFlag())) {
						if (CrsUtil.isNull_or_ZeroDate(af.getElfNextNwDt())) {
							String desc = af.getElfNCkdFlag();
							Map<String, String> map = retrialService
									.get_lrs_NckdFlagMap();
							if (map.containsKey(af.getElfNCkdFlag())) {
								desc = map.get(af.getElfNCkdFlag());
							}
							throw new CapMessageException("不覆審代碼 " + desc
									+ " 必須設定【下次恢復覆審日期】", getClass());
						}
					}

					if (CrsUtil.isNOT_null_and_NOTZeroDate(af.getElfNextNwDt())) {
						if (Util.isEmpty(Util.trim(af.getElfNCkdFlag()))) {
							throw new CapMessageException(
									"無不覆審註記，【下次恢復覆審日期】欄位必須為空白", getClass());
						}
					}

					if (Util.equals(LrsUtil.NCKD_8_本次暫不覆審, af.getElfNCkdFlag())) {
						Date sysDate = CapDate.getCurrentTimestamp();

						if (LMSUtil.cmp_yyyyMM(af.getElfNextNwDt(), "<",
								sysDate)) {
							throw new CapMessageException("錯誤!! 下次恢復覆審年月【"
									+ LrsUtil.toStrYM(af.getElfNextNwDt())
									+ "】必須等於或晚於本月【" + LrsUtil.toStrYM(sysDate)
									+ "】。", getClass());
						}

						if (CrsUtil.isNOT_null_and_NOTZeroDate(af.getNdDate())) {
							if (LMSUtil.cmp_yyyyMM(af.getElfNextNwDt(), ">",
									af.getNdDate())) {
								throw new CapMessageException("錯誤!! 下次恢復覆審年月【"
										+ LrsUtil.toStrYM(af.getElfNextNwDt())
										+ "】已逾下次最遲應覆審日【"
										+ LrsUtil.toStrYM(af.getNdDate())
										+ "】。", getClass());
							}
						}
					}

					// J-106-0278-002 Web
					// e-Loan國內企金授信配合實地覆審作業，額度簽報明細表增加聯貸案件管理行之建檔及修改實地覆審相關檢核
					// elfNCkdFlag
					if (Util.equals(af.getElfNCkdFlag(), "1")) {
						String elfBranch = Util.trim(meta.getElfBranch());
						String elfCustId = Util.trim(meta.getCustId());
						String elfDupNo = Util.trim(meta.getDupNo());
						String elfCtlType = Util.trim(meta.getCtlType());

						String errMsg = retrialService.chkNckdFlag_1(elfBranch,
								elfCustId, elfDupNo, elfCtlType);
						if (Util.notEquals(errMsg, "")) {
							// 不覆審代碼為「1.本行或同業主辦之聯貸案件，非擔任管理行。」
							// ，<BR>借款人項下所有屬該分行之聯貸額度序號(額度序號前三碼為該分行代號且a-Loan額度控管種類為30/60)
							// <BR>必須至少有一筆攤貸行之覆審報告表且該分行有覆審控制檔且不覆審代碼目前非「1.本行或同業主辦之聯貸案件，非擔任管理行。」
							throw new CapMessageException(errMsg, getClass());
						}

					}

					// J-106-0278-002 Web
					// e-Loan國內企金授信配合實地覆審作業，額度簽報明細表增加聯貸案件管理行之建檔及修改實地覆審相關檢核
					if (Util.equals(af.getElfNCkdFlag(), "E")) {
						String elfBranch = Util.trim(meta.getElfBranch());
						String elfCustId = Util.trim(meta.getCustId());
						String elfDupNo = Util.trim(meta.getDupNo());
						String elfCtlType = Util.trim(meta.getCtlType());

						String errMsg = retrialService.chkNckdFlag_E(elfBranch,
								elfCustId, elfDupNo, elfCtlType);
						if (Util.notEquals(errMsg, "")) {
							// 不覆審代碼為「1.本行或同業主辦之聯貸案件，非擔任管理行。」
							// ，<BR>借款人項下所有屬該分行之聯貸額度序號(額度序號前三碼為該分行代號且a-Loan額度控管種類為30/60)
							// <BR>必須至少有一筆攤貸行之覆審報告表且該分行有覆審控制檔且不覆審代碼目前非「1.本行或同業主辦之聯貸案件，非擔任管理行。」
							throw new CapMessageException(errMsg, getClass());
						}
					}

					// J-107-0192_05097_B1001 針對企金異常通報戶之覆審，增加覆審名單之控管措施與報表。
					if (Util.notEquals(Util.trim(meta.getCtlType()),
							LrsUtil.CTLTYPE_自辦覆審)
							&& Util.notEquals(Util.trim(meta.getCtlType()),
									LrsUtil.CTLTYPE_價金履約)) {
						// 郭慧珠說，只要這間分行有通報且未解除之異常通報，就一定要覆審
						// J-108-0043_05097_B1001 Web
						// e-Loan企金授信覆審，開放不覆審代碼5拆放同業或對同業之融通時，異常通報案件仍可維護為不覆審
						// && Util.notEquals(afElfNCkdFlag, "5")
                        // 2021/02 純小規案件 如異常通報 可不覆審
						String afElfNCkdFlag = Util.trim(af.getElfNCkdFlag());
						if (Util.notEquals(afElfNCkdFlag, "")
								&& Util.notEquals(afElfNCkdFlag, "3")
								&& Util.notEquals(afElfNCkdFlag, "5")
								&& Util.notEquals(afElfNCkdFlag, "6")
								&& Util.notEquals(afElfNCkdFlag, "7")
								&& Util.notEquals(afElfNCkdFlag, "8")
								&& Util.notEquals(afElfNCkdFlag, "9")
                                && Util.notEquals(afElfNCkdFlag,
                                    LrsUtil.NCKD_11_小規模營業人_央行C方案_已抽樣覆審於次年起免辦覆審或未列於抽樣需覆審名單內)) {

							// J-107-0213_05097_B1001 Web
							// e-Loan國內企金覆審當同一分行對同一公司同時有自貸案額度及非「任管理行與主辦行」之聯行參貸額度時,該自貸額度,覆審主辦單位不得維護為免辦理覆審之案件
							boolean canPass = false;
							String elfBranch = Util.trim(meta.getElfBranch());
							String elfCustId = Util.trim(meta.getCustId());
							String elfDupNo = Util.trim(meta.getDupNo());

							// ALOAN額度皆為聯貸案非該分行主辦或非該分行額度序號
							if (Util.equals(afElfNCkdFlag, "1")) {

								// 該分行有沒有需覆審之自貸案額度
								List<Map<String, Object>> lnf020Map = misdbBaseService
										.gfnCTL_Import_LNF020_Without_SYND(
												Util.trim(elfBranch),
												Util.trim(elfCustId),
												Util.trim(elfDupNo));

								if (lnf020Map == null || lnf020Map.isEmpty()) {
									// 沒有需覆審之自貸案額度，可以PASS檢核

									// SQL 已排除存單質借、應收帳款買方、進出口、聯貸案
									canPass = true;
								}
							}

							if (!canPass) {

								List<Map<String, Object>> lnf0854Map = misdbBaseService
										.findLnfe0854UnClosedByCustIdAndBrNo(
												elfCustId, elfDupNo, elfBranch);
								if (lnf0854Map != null && !lnf0854Map.isEmpty()) {
									throw new CapMessageException(
											"此分行有未解除之已核准異常通報表，不得為不覆審案件。",
											getClass());
								}
							}

						}
					}

				}

				// ---
				result.set(KEY, true);
			} catch (CapMessageException e) {
				throw e;
			} catch (Exception e) {
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}
		}

		result.add(query(params));

		return result;
	}

	private String get_apprId(L181M01A meta) {
		return meta.getUpdater();
	}

	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		L181M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {

			meta = retrialService.findL181M01A_oid(mainOid);
			String errMsg = "";
			if (Util.equals("呈主管", decisionExpr)) {

			} else if (Util.equals("核定", decisionExpr)) {
				// 檢查經辦和主管是否為同一人
				if (Util.equals(user.getUserId(), get_apprId(meta))) {
					//
					errMsg = RespMsgHelper.getMessage("EFD0053");
				} else {
					if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_自辦覆審)) {
						retrialService.upELF412B(meta);
					} else if (Util.equals(meta.getCtlType(),
							LrsUtil.CTLTYPE_價金履約)) {
						retrialService.upELF412C(meta);
					} else {
						retrialService.upELF412(meta);
					}

				}
			} else if (Util.equals("退回", decisionExpr)) {
			}
			if (Util.isNotEmpty(errMsg)) {
				throw new CapMessageException(errMsg, getClass());
			}
			flowSimplifyService.flowNext(meta.getOid(), decisionExpr);

			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(),
					user.getUserId());
		}
		return defaultResult(params, meta, result);
	}

	@DomainAuth(AuthType.Modify)
	public IResult queryELF412(PageParameters params)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		String branchId = Util.trim(params.getString("branchId"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		String ctlType = Util.trim(params.getString("ctlType"));

		String elf412Flag = "X";
		if (Util.isNotEmpty(mainOid)) {
			L181M01A existObj = lms1810Service.findInProcessData(branchId,
					custId, dupNo,
					new String[] { RetrialDocStatusEnum.編製中.getCode(),
							RetrialDocStatusEnum.待覆核.getCode() }, ctlType,
					user.getUnitNo());
			if (existObj != null) {
				// 分行:{0} 統編: {1}-{2} 已有未覆核資料，請先覆核或刪除
				throw new CapMessageException(MessageFormat.format(
						prop_lms1810m01.getProperty("msg.alreadyHave"),
						branchId, custId, dupNo), getClass());
			}
			Map<String, Object> latest0024Data = iCustomerService
					.findByIdDupNo(custId, dupNo);
			if (latest0024Data == null) {
				// custInfo=借款人
				// noData=查無資料，請重新查詢。
				String errMsg = prop_lms1810m01.getProperty("custInfo") + " :"
						+ custId + "-" + dupNo
						+ prop_abstractEloanPage.getProperty("noData");
				throw new CapMessageException(errMsg, getClass());
			}

			if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {

				ELF412B elf412b = misELF412BService.findByPk(branchId, custId,
						dupNo);
				if (elf412b == null) {
					elf412Flag = "N";
				} else {
					elf412Flag = "Y";
				}
			} else if (Util.equals(ctlType, LrsUtil.CTLTYPE_價金履約)) {
				// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
				ELF412C elf412c = misELF412CService.findByPk(branchId, custId,
						dupNo);
				if (elf412c == null) {
					elf412Flag = "N";
				} else {
					elf412Flag = "Y";
				}
			} else {
				ELF412 elf412 = misELF412Service.findByPk(branchId, custId,
						dupNo);
				if (elf412 == null) {
					elf412Flag = "N";
				} else {
					elf412Flag = "Y";
				}
			}

		}
		result.set("elf412Flag", elf412Flag);
		return result;
	}

	@DomainAuth(AuthType.Modify)
	public IResult doCustIdSaved(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// ---
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		String branchId = Util.trim(params.getString("branchId"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		String ctlType = Util.trim(params.getString("ctlType"));
		ctlType = Util.equals(Util.trim(ctlType), "") ? LrsUtil.CTLTYPE_主辦覆審
				: ctlType;

		L181M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL181M01A_oid(mainOid);
			Map<String, Object> latest0024Data = iCustomerService
					.findByIdDupNo(custId, dupNo);
			String custName = Util.trim(MapUtils.getString(latest0024Data,
					"CNAME"));
			// ---
			meta.setCustId(custId);
			meta.setDupNo(dupNo);
			meta.setCustName(custName);
			meta.setElfBranch(branchId);

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			meta.setCtlType(ctlType);

			L181M01B bfObj = new L181M01B();
			L181M01B afObj = new L181M01B();

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			lms1810Service.setterL181Model(meta, bfObj, afObj, ctlType);
			// ---
			retrialService.save(bfObj);
			retrialService.save(afObj);
			retrialService.save(meta);
		}
		return defaultResult(params, meta, result);
	}

	@DomainAuth(AuthType.Modify)
	public IResult query_LNF022(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// ---
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		L181M01A meta = null;
		String isCancel = "";
		String elfCancelDt = "";
		String cStateDesc = "";
		boolean runSave = Util.notEquals("N", params.getString("runSave"));
		String ctlType = Util.trim(params.getString("ctlType"));

		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL181M01A_oid(mainOid);
			String[] arr = retrialService.gfnCTL_Import_LNF022_with_ctlType(
					meta.getElfBranch(), meta.getCustId(), meta.getDupNo(),
					meta.getElfCancelDt(), ctlType);
			meta.setElfCState(arr[0]);
			meta.setElfCancelDt(Util.equals(arr[1], CapDate.ZERO_DATE) ? null
					: CapDate.parseDate(arr[1]));

			// ---
			if (CrsUtil.isNull_or_ZeroDate(meta.getElfCancelDt())) {
				isCancel = "N";
			} else {
				isCancel = "Y";
				elfCancelDt = TWNDate.toAD(meta.getElfCancelDt());
			}

			// '*************************************************
			if (runSave) {
				retrialService.save(meta);
			}

			if (Util.isNotEmpty(meta.getElfCState())) {
				cStateDesc = Util.trim(retrialService.get_lrs_CState().get(
						meta.getElfCState()));
			}
		}
		result.set("isCancel", isCancel);
		result.set("elfCancelDt", elfCancelDt);
		result.set("cStateDesc", cStateDesc);
		return defaultResult(params, meta, result);
	}

	/**
	 * 中心帳務檔已銷戶，是否要將本文件之異動欄位更新為銷戶預設值 → 選「是」
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult afItemWithCancelVal(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// ---
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		L181M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL181M01A_oid(mainOid);
			// ---
			L181M01B af = retrialService.findL181M01B_mainid_Af(meta
					.getMainId());
			af.setElfRCkdLine("A");
			af.setElfUCkdLINE("");
			af.setElfUCkdDt(null);
			af.setElfMDFlag("");
			af.setElfMDDt(null);
			af.setElfProcess("");
			af.setElfNewAdd("");
			af.setElfNewDate(null);
			af.setElfNCkdFlag("7");
			af.setElfNCkdDate(meta.getElfCancelDt());
			// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			af.setElfRealCkFg("");
			af.setElfRealDt(null);
			// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
			af.setElfIsRescue("");
            af.setElfGuarFlag("");
			af.setElfNewRescue("");
			af.setElfNewRescueYM(null);
			// J-110-0272 抽樣覆審
			af.setElfRandomType("");
			retrialService.save(af);
			// ---
			retrialService.save(meta);
		}
		return defaultResult(params, meta, result);
	}

	@DomainAuth(AuthType.Modify)
	public IResult fnGetNewChkDate(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// ---
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		List<String> msgList = new ArrayList<String>();

		L181M01A meta = null;
		String str_ndDate = "";
		boolean runSave = Util.notEquals("N", params.getString("runSave"));

		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL181M01A_oid(mainOid);
			L181M01B af = retrialService.findL181M01B_mainid_Af(meta
					.getMainId());
			// ---
			L181M01B mockItem = new L181M01B();
			DataParse.copy(af, mockItem);
			// ---
			Date ndDate = calcNdDate(msgList, meta.getElfBranch(),
					meta.getCustId(), meta.getDupNo(), meta.getCustName(),
					mockItem, meta.getCtlType());
			af.setNdDate(CrsUtil.isNOT_null_and_NOTZeroDate(ndDate) ? ndDate
					: null);
			str_ndDate = Util.trim(TWNDate.toAD(af.getNdDate()));
			// ---
			if (runSave) {
				retrialService.save(af);
				retrialService.save(meta);
			}
		}
		result.set("msg", StringUtils.join(msgList, "<br/>"));
		result.set("calcFlag", true);
		result.set("ndDate", str_ndDate);
		return defaultResult(params, meta, result);
	}

	/**
	 * XXX:參考FLMS180M02::fnGetNewChkDate <br/>
	 * 裡面有9個變數的值會更新 <br/>
	 * inPara("ELF412_MAINCUST") = newELF412_MAINCUST <br/>
	 * inPara("ELF412_CRDTTBL") = newELF412_CRDTTBL <br/>
	 * inPara("ELF412_MOWTYPE") = newELF412_MOWTYPE <br/>
	 * inPara("ELF412_MOWTBL1") = newELF412_MOWTBL1 <br/>
	 * inPara("ELF412_RCKDLINE") = newELF412_RCKDLINE <br/>
	 * <br/>
	 * inPara("ELF412_FCRDTYPE") = newELF412_FCRDTYPE <br/>
	 * inPara("ELF412_FCRDAREA") = newELF412_FCRDAREA <br/>
	 * inPara("ELF412_FCRDPRED") = newELF412_FCRDPRED <br/>
	 * inPara("ELF412_FCRDGRAD") = newELF412_FCRDGRAD
	 */
	private Date calcNdDate(List<String> adjGradeMsg, String branchId,
			String custId, String dupNo, String custName, L181M01B mockItem,
			String ctlType) throws CapMessageException {

		RO412 src_ro412 = new RO412(branchId, custId, dupNo, mockItem);
		RO412 ro412 = new RO412(branchId, custId, dupNo, mockItem);

		String mode = "3";

		// J-108-0036_05097_B1001 修改web e-loan授信覆審系統，修改覆審控制檔,得變更授信覆審期日。
		// FOR TEST 逾期未覆審報表
		// mode = "4";

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		if (Util.equals(ctlType, LrsUtil.CTLTYPE_自辦覆審)) {

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			Date ndDate = retrialService.gfnCTL_Caculate_DueDate(mode,
					CapDate.getCurrentTimestamp(), null, ro412, null);

			if (CrsUtil.isNull_or_ZeroDate(ndDate)) {
				throw new CapMessageException(custId + custName + " 計算下次覆審日錯誤",
						getClass());
			}

			adjGradeMsg.add("下次覆審日：" + Util.trim(TWNDate.toAD(ndDate)));
			return ndDate;
			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		} else if (Util.equals(ctlType, LrsUtil.CTLTYPE_價金履約)) {

			// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
			Date ndDate = retrialService.gfnCTL_Caculate_DueDate(mode,
					CapDate.getCurrentTimestamp(), null, null, ro412);

			if (CrsUtil.isNull_or_ZeroDate(ndDate)) {
				throw new CapMessageException(custId + custName + " 計算下次覆審日錯誤",
						getClass());
			}

			adjGradeMsg.add("下次覆審日：" + Util.trim(TWNDate.toAD(ndDate)));
			return ndDate;
		} else {

			LinkedHashMap<String, String> elf412_DBUCOID_map = new LinkedHashMap<String, String>();
			LinkedHashMap<String, String> elf412_OBUCOID_map = new LinkedHashMap<String, String>();

			retrialService.gfnCTL_Import_LNF025(branchId, custId, dupNo,
					elf412_DBUCOID_map, elf412_OBUCOID_map);

			String elf412_DBUCOID = LrsUtil.toDBUOBU_COID(elf412_DBUCOID_map);
			String elf412_OBUCOID = LrsUtil.toDBUOBU_COID(elf412_OBUCOID_map);
			boolean hasCoFlag = (elf412_DBUCOID_map.size() > 0 || elf412_OBUCOID_map
					.size() > 0);
			if (hasCoFlag) {

				ro412 = retrialService.gfnGenCTLList_PROCESS_SHARE_GRADE(
						branchId,
						LMSUtil.getCustKey_len10custId(custId, dupNo),
						src_ro412, elf412_DBUCOID_map, elf412_OBUCOID_map);
				// ---
				boolean adjFlag = LrsUtil.get_adjFlag(src_ro412, ro412);
				if (adjFlag) {
					// '因額度共用
					adjGradeMsg
							.add("依覆審辦法第五條之一(共用戶以符合授信額度標準與特定評等)，覆審週期計算調整為以各共用戶上次覆審日加計半年【B】計算最遲覆審日，惟若有境外公司或新設公司無評等者不予採計。");
					if (elf412_DBUCOID_map.size() > 0) {
						adjGradeMsg.add("DBU共用戶：" + elf412_DBUCOID);
					}
					if (elf412_OBUCOID_map.size() > 0) {
						adjGradeMsg.add("OBU共用戶：" + elf412_OBUCOID);
					}
				}
			}

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			Date ndDate = retrialService.gfnCTL_Caculate_DueDate(mode,
					CapDate.getCurrentTimestamp(), ro412, null, null);

			if (CrsUtil.isNull_or_ZeroDate(ndDate)) {
				throw new CapMessageException(custId + custName + " 計算下次覆審日錯誤",
						getClass());
			}

			Date minChkDate = null;
			String mainChkID = "";
			if (hasCoFlag) {
				String[] rs = lms1810Service
						.gfnGetNEW_DBUOBU_MINCHKDATE(mode, branchId, ro412,
								elf412_DBUCOID_map, elf412_OBUCOID_map);
				if (Util.isNotEmpty(rs[0])) {
					minChkDate = CapDate.parseDate(rs[1]);
					mainChkID = rs[2];
				}
			}
			if (CrsUtil.isNOT_null_and_NOTZeroDate(minChkDate)
					&& LMSUtil.cmp_yyyyMM(minChkDate, "<", ndDate)) {
				adjGradeMsg.add(custId + custName + " 原下次覆審日【"
						+ Util.trim(TWNDate.toAD(ndDate)) + "】" + "，額度共用調整後以 "
						+ mainChkID + " 覆審日期【"
						+ Util.trim(TWNDate.toAD(minChkDate)) + "】為主。");
				return minChkDate;
			} else {
				adjGradeMsg.add("下次覆審日：" + Util.trim(TWNDate.toAD(ndDate)));
				return ndDate;
			}
		}

	}

	@DomainAuth(AuthType.Modify)
	public IResult check_befSendBoss(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		// ---
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		L181M01A meta = null;
		String isCancel = "";
		String elfCancelDt = "";
		String cStateDesc = "";
		if (Util.isNotEmpty(mainOid)) {

			meta = retrialService.findL181M01A_oid(mainOid);
			L181M01B af = retrialService.findL181M01B_mainid_Af(meta
					.getMainId());

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_自辦覆審)) {
				ELF412B elf412b = misELF412BService.findByPk(
						meta.getElfBranch(), meta.getCustId(), meta.getDupNo());
				if (elf412b == null) {
					// new data,ok
				} else {
					String localTms = Util.trim(TWNDate.toAD(meta
							.getElfTmeStamp()));
					String misTms = Util.trim(TWNDate.toAD(elf412b
							.getElf412b_tmestamp()));
					if (Util.isNotEmpty(localTms)
							&& Util.isNotEmpty(misTms)
							&& LMSUtil.cmpDate(CapDate.parseDate(localTms),
									"<", CapDate.parseDate(misTms))) {
						throw new CapMessageException(
								prop_lms1810m01.getProperty("L181M01A.elfTmeStamp")
										+ " : "
										+ localTms
										+ " < 控制檔 : "
										+ misTms, getClass());
					}
				}
			} else if (Util.equals(meta.getCtlType(), LrsUtil.CTLTYPE_價金履約)) {
				ELF412C elf412c = misELF412CService.findByPk(
						meta.getElfBranch(), meta.getCustId(), meta.getDupNo());
				if (elf412c == null) {
					// new data,ok
				} else {
					String localTms = Util.trim(TWNDate.toAD(meta
							.getElfTmeStamp()));
					String misTms = Util.trim(TWNDate.toAD(elf412c
							.getElf412c_tmestamp()));
					if (Util.isNotEmpty(localTms)
							&& Util.isNotEmpty(misTms)
							&& LMSUtil.cmpDate(CapDate.parseDate(localTms),
									"<", CapDate.parseDate(misTms))) {
						throw new CapMessageException(
								prop_lms1810m01.getProperty("L181M01A.elfTmeStamp")
										+ " : "
										+ localTms
										+ " < 控制檔 : "
										+ misTms, getClass());
					}
				}
			} else {
				ELF412 elf412 = misELF412Service.findByPk(meta.getElfBranch(),
						meta.getCustId(), meta.getDupNo());
				if (elf412 == null) {
					// new data,ok
				} else {
					String localTms = Util.trim(TWNDate.toAD(meta
							.getElfTmeStamp()));
					String misTms = Util.trim(TWNDate.toAD(elf412
							.getElf412_tmestamp()));
					if (Util.isNotEmpty(localTms)
							&& Util.isNotEmpty(misTms)
							&& LMSUtil.cmpDate(CapDate.parseDate(localTms),
									"<", CapDate.parseDate(misTms))) {
						throw new CapMessageException(
								prop_lms1810m01.getProperty("L181M01A.elfTmeStamp")
										+ " : "
										+ localTms
										+ " < 控制檔 : "
										+ misTms, getClass());
					}
				}
			}

			// ---
			if (Util.equals(LrsUtil.NCKD_7_銷戶, af.getElfNCkdFlag())) {
				String[] arr = retrialService
						.gfnCTL_Import_LNF022_with_ctlType(meta.getElfBranch(),
								meta.getCustId(), meta.getDupNo(),
								meta.getElfCancelDt(), meta.getCtlType());
				meta.setElfCState(arr[0]);
				meta.setElfCancelDt(Util.equals(arr[1], CapDate.ZERO_DATE) ? null
						: CapDate.parseDate(arr[1]));
				// ---
				if (CrsUtil.isNull_or_ZeroDate(meta.getElfCancelDt())) {
					isCancel = "N";
				} else {
					isCancel = "Y";
					elfCancelDt = TWNDate.toAD(meta.getElfCancelDt());
				}
				if (Util.isNotEmpty(meta.getElfCState())) {
					cStateDesc = Util.trim(retrialService.get_lrs_CState().get(
							meta.getElfCState()));
				}
				// ---
				if (CrsUtil.isNull_or_ZeroDate(meta.getElfCancelDt())) {
					throw new CapMessageException("中心帳務檔未銷戶，戶況【" + cStateDesc
							+ "】，" + "不覆審代碼不得為【 7.銷戶 】。", getClass());
				}
			}
			// ---
			retrialService.save(meta);
		}
		result.set("checkFlag", true);
		result.set("isCancel", isCancel);
		result.set("elfCancelDt", elfCancelDt);
		result.set("cStateDesc", cStateDesc);
		return defaultResult(params, meta, result);
	}

	/*
	 * 取得ALOAN實地覆審資料J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 */
	@DomainAuth(AuthType.Modify)
	public IResult getAloanRealData(PageParameters params)
			throws CapException {

		Properties prop_lms1810m01 = MessageBundleScriptCreator
				.getComponentResource(LMS1810M01Page.class);

		StringBuffer showMsg = new StringBuffer("");
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String custId = "";
		String dupNo = "";
		String brNo = "";

		L181M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL181M01A_oid(mainOid);

			custId = meta.getCustId();
			dupNo = meta.getDupNo();
			brNo = meta.getElfBranch();

			String aReealDt = "";
			String aRealCkFg = "";
			String aRealContract = "";
			if (Util.notEquals(custId, "") && Util.notEquals(dupNo, "")
					&& Util.notEquals(brNo, "")) {
				Map<String, String> minDataMap = retrialService
						.gfnGetAloanRealDt2(custId, dupNo, brNo);
				if (minDataMap != null && !minDataMap.isEmpty()) {
					aReealDt = MapUtils.getString(minDataMap, "realDt", "");
					aRealCkFg = MapUtils.getString(minDataMap, "realCkFg", "");
					aRealContract = Util.trim(MapUtils.getString(minDataMap,
							"realContract", ""));

					showMsg.append(prop_lms1810m01
							.getProperty("L181M01B.realCkFg")); // L181M01B.realCkFg=實地覆審案件(Y/N)
					showMsg.append(":");
					showMsg.append(aRealCkFg);
					showMsg.append("<BR>");

					if (Util.equals(aRealCkFg, "Y")) {
						showMsg.append(prop_lms1810m01
								.getProperty("L181M01B.minRealDt")); // 最小實地覆審基準日(參考)
						showMsg.append(":");
						showMsg.append(aReealDt);
						showMsg.append("<BR>");

						showMsg.append(prop_lms1810m01
								.getProperty("L181M01B.cntrNos")); // 聯貸主辦額度序號
						showMsg.append(":");
						showMsg.append(aRealContract);
						showMsg.append("<BR>");

					}

				} else {
					// err.check16=該戶無帳務資料！
					showMsg.append(prop_lms1810m01.getProperty("err.check16"));
				}
			} else {
				// err.check15=請先儲存後再執行！
				showMsg.append(prop_lms1810m01.getProperty("err.check15"));
			}

		}

		result.set("showMsg", showMsg.toString());

		return result;
	}

	// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
	// 若更改 是否為純紓困戶，需一併更改 RetrialServiceImpl.java  getOnlyRescueCase(
	@DomainAuth(AuthType.Modify)
	public IResult getRescueCntrNo(PageParameters params)
			throws CapException {

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1810M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		StringBuffer showMsg = new StringBuffer("");
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		L181M01A meta = null;
		String custId = "";
		String dupNo = "";
		String brNo = "";
		StringBuffer isRescue = new StringBuffer("");
		StringBuffer notRescue = new StringBuffer("");
		if (Util.isNotEmpty(mainOid)) {
			meta = retrialService.findL181M01A_oid(mainOid);

			if (meta!=null) {
				custId = Util.trim(meta.getCustId());
				dupNo = Util.trim(meta.getDupNo());
				brNo = Util.trim(meta.getElfBranch());

				List<Map<String, Object>> listMap = misLNF022Service
						.gfnCTL_Import_LNF022_selContract(brNo, custId, dupNo);
				if (!listMap.isEmpty()) {
					for (Map<String, Object> map : listMap) {
						String cntrNo = Util.trim(map.get("CONTRACT"));
						Map<String, Object> resultMap = eloandbBASEService
								.getL161S01A_IsRescue(cntrNo);
						if (resultMap != null) {
							if(Util.equals(Util.nullToSpace(resultMap.get("ISRESCUE")), "Y")){
								isRescue.append((isRescue.length() > 0) ? "、" : "");
								isRescue.append(cntrNo);
							} else {
								notRescue.append((isRescue.length() > 0) ? "、" : "");
								notRescue.append(cntrNo);
							}
						} else {
							showMsg.append("查無資料");
						}
					}
				} else {
					// err.check16=該戶無帳務資料！
					showMsg.append(prop.getProperty("err.check16"));
				}
			} else {
				// noL181M01A=無收尋到主檔
				showMsg.append(prop.getProperty("noL181M01A"));
			}
		}

		if (isRescue.length() > 0) {
			showMsg.append("紓困案：").append(isRescue.toString()).append("<br/>");
		}
		if (notRescue.length() > 0) {
			showMsg.append("非紓困案：").append(notRescue.toString());
		}

		result.set("showMsg", showMsg.toString());

		return result;
	}
}
