---------------------------------------------------------
-- LMS.C999M01B 個金約據書立約人檔
---------------------------------------------------------
--DROP TABLE LMS.C999M01B;
CREATE TABLE LMS.C999M01B (
	OID           CHAR(32)      not null,
	<PERSON>IN<PERSON>        CHAR(32)      not null,
	CUSTID        VARCHAR(10)   not null,
	<PERSON><PERSON><PERSON><PERSON>         CHAR(1)       not null,
	TYPE          CHAR(1)       not null,
	CUSTNAME      VARCHAR(120) ,
	<PERSON><PERSON><PERSON>          CHAR(3)      ,
	BRNAME        VARCHAR(63)  ,
	CHAIRMANI<PERSON>    VARCHAR(10)  ,
	CHAIRMA<PERSON>UPN<PERSON> CHAR(1)      ,
	CHAIRMAN      VARCHAR(60)  ,
	ADDRZIP       DECIMAL(5,0) ,
	ADDRCITY      VARCHAR(12)  ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>      VARCHAR(12)  ,
	<PERSON><PERSON>(300) ,
	<PERSON><PERSON>           VARCHAR(30)  ,
	FAX           VARCHAR(30)  ,
	EMAIL         VARCHAR(60)  ,
	<PERSON><PERSON><PERSON>RCHAR(300) ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_C999M01B PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC999M01B01;
CREATE UNIQUE INDEX LMS.XC999M01B01 ON LMS.C999M01B   (MAINID, CUSTID, DUPNO, TYPE);
--DROP INDEX LMS.XC999M01B02;
CREATE INDEX LMS.XC999M01B02 ON LMS.C999M01B   (CUSTID, DUPNO);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C999M01B IS '個金約據書立約人檔';
COMMENT ON LMS.C999M01B (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	CUSTID        IS '立約人統一編號', 
	DUPNO         IS '立約人重覆序號', 
	TYPE          IS '立約人種類', 
	CUSTNAME      IS '立約人名稱', 
	BRNO          IS '分行代碼', 
	BRNAME        IS '分行名稱', 
	CHAIRMANID    IS '負責人統編', 
	CHAIRMANDUPNO IS '負責人統編重複碼', 
	CHAIRMAN      IS '負責人姓名', 
	ADDRZIP       IS '郵遞區號', 
	ADDRCITY      IS '地址(縣市)', 
	ADDRTOWN      IS '地址(區鄉鎮市)', 
	ADDR          IS '地址', 
	TEL           IS '電話', 
	FAX           IS '傳真', 
	EMAIL         IS '電子信箱', 
	OTHER         IS '其他', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
