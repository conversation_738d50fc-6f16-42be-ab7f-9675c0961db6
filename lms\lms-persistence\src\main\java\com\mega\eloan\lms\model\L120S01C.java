/* 
 * L120S01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;
import javax.persistence.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 企金信用評等資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S01C", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "crdTYear", "crdTBR", "crdType",
		"finYear", "cntrNo" }))
public class L120S01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 評等（公佈）日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CRDTYEAR", columnDefinition = "DATE")
	private Date crdTYear;

	/**
	 * 評等單位
	 * <p/>
	 * 單位代號
	 */
	@Column(name = "CRDTBR", length = 3, columnDefinition = "CHAR(3)")
	private String crdTBR;

	/**
	 * 額度序號
	 * <p/>
	 * 無額度序號時，以0補滿欄位
	 */
	@Column(name = "CNTRNO", length = 14, columnDefinition = "CHAR(14)")
	private String cntrNo;

	/**
	 * 評等表類型
	 * <p/>
	 * DB=DBU大型企業<br/>
	 * DL=DBU中小型企業<br/>
	 * OU=0BU<br/>
	 * CA=泰國GroupA<br/>
	 * CB=泰國GroupB<br/>
	 * CK=自訂<br/>
	 * CS=消金評等<br/>
	 * NM=Moody<br/>
	 * NS=S&P<br/>
	 * NF=Fitch<br/>
	 * NC=中華信評
	 */
	@Column(name = "CRDTYPE", length = 3, columnDefinition = "VARCHAR(3)")
	private String crdType;

	/**
	 * 評等財報年度
	 * <p/>
	 * 無評等財報年度，放評等（公佈）日期西元年
	 */
	@Column(name = "FINYEAR", length = 4, columnDefinition = "VARCHAR(7)")
	private String finYear;

	/** 評等評分 **/
	@Column(name = "SCORE", columnDefinition = "DECIMAL(4,0)")
	private Integer score;

	/** 評等等級 **/
	@Column(name = "GRADE", length = 20, columnDefinition = "VARCHAR(20)")
	private String grade;

	/** 評等展望 **/
	@Column(name = "PROSPECT", length = 6, columnDefinition = "CHAR(6)")
	private String prospect;

	/**
	 * 保證企業統一編號
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入
	 */
	@Column(name = "PRCUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String prCustId;

	/**
	 * 保證企業重覆序號
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入
	 */
	@Column(name = "PRDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String prDupNo;

	/**
	 * 保證企業名稱
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入
	 */
	@Column(name = "PRCNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String prCNAME;

	/**
	 * 保證企業最終評等
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入
	 */
	@Column(name = "PRFR", length = 2, columnDefinition = "CHAR(2)")
	private String prFR;

	/**
	 * 保證企業所引用之財報期間
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入<br/>
	 * YYYMMDD民國年
	 */
	@Column(name = "PRFINDATE", length = 7, columnDefinition = "CHAR(7)")
	private String prFinDate;

	/**
	 * 保證企業之評等單位
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入<br/>
	 * 單位代號
	 */
	@Column(name = "PRMOWBR", length = 3, columnDefinition = "CHAR(3)")
	private String prMOWBr;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/** 評等期間起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "PERIODBGN", columnDefinition = "DATE")
	private Date periodBgn;

	/** 評等期間迄日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "PERIODEND", columnDefinition = "DATE")
	private Date periodEnd;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得評等（公佈）日期 **/
	public Date getCrdTYear() {
		return this.crdTYear;
	}

	/** 設定評等（公佈）日期 **/
	public void setCrdTYear(Date value) {
		this.crdTYear = value;
	}

	/**
	 * 取得評等單位
	 * <p/>
	 * 單位代號
	 */
	public String getCrdTBR() {
		return this.crdTBR;
	}

	/**
	 * 設定評等單位
	 * <p/>
	 * 單位代號
	 **/
	public void setCrdTBR(String value) {
		this.crdTBR = value;
	}

	/**
	 * 取得額度序號
	 * <p/>
	 * 無額度序號時，以0補滿欄位
	 */
	public String getCntrNo() {
		return this.cntrNo;
	}

	/**
	 * 設定額度序號
	 * <p/>
	 * 無額度序號時，以0補滿欄位
	 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/**
	 * 取得評等表類型
	 * <p/>
	 * DB=DBU大型企業<br/>
	 * DL=DBU中小型企業<br/>
	 * OU=0BU<br/>
	 * CA=泰國GroupA<br/>
	 * CB=泰國GroupB<br/>
	 * CK=自訂<br/>
	 * CS=消金評等<br/>
	 * NM=Moody<br/>
	 * NS=S&P<br/>
	 * NF=Fitch<br/>
	 * NC=中華信評
	 */
	public String getCrdType() {
		return this.crdType;
	}

	/**
	 * 設定評等表類型
	 * <p/>
	 * DB=DBU大型企業<br/>
	 * DL=DBU中小型企業<br/>
	 * OU=0BU<br/>
	 * CA=泰國GroupA<br/>
	 * CB=泰國GroupB<br/>
	 * CK=自訂<br/>
	 * CS=消金評等<br/>
	 * NM=Moody<br/>
	 * NS=S&P<br/>
	 * NF=Fitch<br/>
	 * NC=中華信評
	 **/
	public void setCrdType(String value) {
		this.crdType = value;
	}

	/**
	 * 取得評等財報年度
	 * <p/>
	 * 無評等財報年度，放評等（公佈）日期西元年
	 */
	public String getFinYear() {
		return this.finYear;
	}

	/**
	 * 設定評等財報年度
	 * <p/>
	 * 無評等財報年度，放評等（公佈）日期西元年
	 **/
	public void setFinYear(String value) {
		this.finYear = value;
	}

	/** 取得評等評分 **/
	public Integer getScore() {
		return this.score;
	}

	/** 設定評等評分 **/
	public void setScore(Integer value) {
		this.score = value;
	}

	/** 取得評等等級 **/
	public String getGrade() {
		return this.grade;
	}

	/** 設定評等等級 **/
	public void setGrade(String value) {
		this.grade = value;
	}

	/** 取得評等展望 **/
	public String getProspect() {
		return this.prospect;
	}

	/** 設定評等展望 **/
	public void setProspect(String value) {
		this.prospect = value;
	}

	/**
	 * 取得保證企業統一編號
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入
	 */
	public String getPrCustId() {
		return this.prCustId;
	}

	/**
	 * 設定保證企業統一編號
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入
	 **/
	public void setPrCustId(String value) {
		this.prCustId = value;
	}

	/**
	 * 取得保證企業重覆序號
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入
	 */
	public String getPrDupNo() {
		return this.prDupNo;
	}

	/**
	 * 設定保證企業重覆序號
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入
	 **/
	public void setPrDupNo(String value) {
		this.prDupNo = value;
	}

	/**
	 * 取得保證企業名稱
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入
	 */
	public String getPrCNAME() {
		return this.prCNAME;
	}

	/**
	 * 設定保證企業名稱
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入
	 **/
	public void setPrCNAME(String value) {
		this.prCNAME = value;
	}

	/**
	 * 取得保證企業最終評等
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入
	 */
	public String getPrFR() {
		return this.prFR;
	}

	/**
	 * 設定保證企業最終評等
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入
	 **/
	public void setPrFR(String value) {
		this.prFR = value;
	}

	/**
	 * 取得保證企業所引用之財報期間
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入<br/>
	 * YYYMMDD民國年
	 */
	public String getPrFinDate() {
		return this.prFinDate;
	}

	/**
	 * 設定保證企業所引用之財報期間
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入<br/>
	 * YYYMMDD民國年
	 **/
	public void setPrFinDate(String value) {
		this.prFinDate = value;
	}

	/**
	 * 取得保證企業之評等單位
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入<br/>
	 * 單位代號
	 */
	public String getPrMOWBr() {
		return this.prMOWBr;
	}

	/**
	 * 設定保證企業之評等單位
	 * <p/>
	 * 101/03/01新增<br/>
	 * 引進模型評等資料時一併引入<br/>
	 * 單位代號
	 **/
	public void setPrMOWBr(String value) {
		this.prMOWBr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/**
	 * 設定評等期間起日
	 * 
	 * @param periodBgn
	 */
	public void setPeriodBgn(Date periodBgn) {
		this.periodBgn = periodBgn;
	}

	/**
	 * 取得評等期間起日
	 * 
	 * @return
	 */
	public Date getPeriodBgn() {
		return periodBgn;
	}

	/**
	 * 設定評等期間迄日
	 * 
	 * @param periodEnd
	 */
	public void setPeriodEnd(Date periodEnd) {
		this.periodEnd = periodEnd;
	}

	/**
	 * 取得評等期間迄日
	 * 
	 * @return
	 */
	public Date getPeriodEnd() {
		return periodEnd;
	}
}
