/* 
 * Check.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.validation.group;

/**<pre>
 * this is Validation Group
 * </pre>
 * @since  2012/5/15
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/5/15,Fantasy,new
 *          </ul>
 */
public interface Check {

}
