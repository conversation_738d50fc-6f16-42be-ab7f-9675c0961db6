/* 
 * LMS2415S02Panel01.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 *  [個金]覆審報告表  一般授信資料明細
 * </pre>
 * 
 * @since 2012/2/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/15,jessica,new
 *          </ul>
 */
public class LMS2415S02Panel01 extends Panel {

	private static final long serialVersionUID = -4024257163623646201L;

	public LMS2415S02Panel01(String id) {
		super(id);
	}
	
	public LMS2415S02Panel01(String id, boolean updatePanelName) {
		super(id, updatePanelName);

	}
}
