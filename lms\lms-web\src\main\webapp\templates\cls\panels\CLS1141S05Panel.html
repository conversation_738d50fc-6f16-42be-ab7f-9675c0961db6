<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
        <wicket:panel>
        	<script type="text/javascript" src="pagejs/cls/CLS1141S05Panel.js?ver=201811"></script>
			
            <form id="CLSForm">
                <fieldset>
                    <legend>
                        <b><wicket:message key="cls120s05.title1">借款用途</wicket:message></b>
                    </legend>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td class="hd1" width="20%">
                                <wicket:message key="cls120s05.title1">借款用途</wicket:message>&nbsp;&nbsp;
                            </td>
                            <td width="80%">
                                <input type="checkbox" id="purpose1" name="purposeTemp" />
                                <span id="purposeSpan" style="display:none"><wicket:message key="cls120s05.title2">請說明：</wicket:message>
                                    <input type="text" id="purposeOth" name="purposeOth" maxlength="60" maxlengthC="20" size="60" class="required" />
                                </span>
                            </td>
                        </tr>
                    </table>
                </fieldset>
                <fieldset>
                    <legend>
                        <b><wicket:message key="cls120s05.title3">還款財源</wicket:message></b>
                    </legend>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td class="hd1" width="20%">
                                <wicket:message key="cls120s05.title3">還款財源</wicket:message>&nbsp;&nbsp;
                            </td>
                            <td width="80%">
                                <input type="checkbox" id="resource1" name="resourceTemp" value="1"/>
                                <span id="resourceSpan" style="display:none"><wicket:message key="cls120s05.title2">請說明：</wicket:message>
                                    <input type="text" id="resourceOth" name="resourceOth" maxlength="60" maxlengthC="20" size="60" class="required"/>
                                </span>
                            </td>
                        </tr>
                    </table>
                </fieldset>
				
				<fieldset>
					<legend>
                        <b><wicket:message key="cls120s05.title4">各項費用</wicket:message></b>
                    </legend>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
				         <tr id="feesTr">
								<td class="hd1" width="20%">
                                    <wicket:message key="cls120s05.title4">各項費用</wicket:message>&nbsp;&nbsp;
                                </td>
								<td width="80%">
									<div id="special">
									<button type="button" id="addL140M01RBt">
                                         <span class="text-only"><wicket:message key="button.add">新增</wicket:message></span>
                                    </button>
                                    <button type="button" id="delL140M01RBt">
                                         <span class="text-only"><wicket:message key="button.delete">刪除</wicket:message></span>
                                    </button>
									</div>
									
                                    <div id="L140M01RGrid" /> 
             				        			
									
								<div id="fee" style="display:none;">
				                <div id="charge">
					                <table class="tb2" width="100%">
						             <tr>						             	
							           <td class="hd1"><wicket:message key="L140M01R.002">費用代碼</wicket:message></td>
							           <td><select id="feeNo" name="feeNo" combokey="cls1141_feeNo" comboType="4" /></td>
									   <input type="text" name="feeSeq" id="feeSeq"  class="clearFee" style="display:none"  />
									   <!--<td><input type="text" name="freeNo" id="freeNo" class="required" /></td> -->
						             </tr>
									 <!-- 原有的收取範圍暫時不由E-LOAN填入....
									 <tr>
									 	<td class="hd1">
                                             <wicket:message key="L140M01R.003">收取範圍</wicket:message>
										</td>
										<td>	                                         
									 	<label>
                                             <input type="radio" name="feeSphere" id="feeSphere" value="01" class="required" checked="checked" />
                                             <wicket:message key="L140M01R.009">客戶編號</wicket:message>
                                        </label>
                                        <label>
                                             <input type="radio" name="feeSphere" value="02" class="required"/>
                                             <wicket:message key="L140M01R.010">額度序號</wicket:message>
                                        </label>
										<label>
                                             <input type="radio" name="feeSphere" value="03" class="required"/>
                                             <wicket:message key="L140M01R.011">帳號</wicket:message>
                                        </label>
										
										<br>
										<button type="button" id="includeC120M01A" class="group01" style="">
                                                <span class="text-only"><wicket:message key="button.pullin01">引進客戶編號</wicket:message></span>
                                        </button>
										<button type="button" id="includeL140M01A" class="group02" style="">
                                                <span class="text-only"><wicket:message key="button.pullin02">引進額度序號</wicket:message></span>
                                        </button>
										<br>										
										<input type="text" name="custId" id="custId" class="group01 clearFee" readonly="readonly" />
										<input type="text" name="dupNo" id="dupNo" class="group01 clearFee" readonly="readonly" /> 
										<input type="text" name="cntrno" id="cntrno" class="group02 clearFee" readonly="readonly"/>
										<input type="text" name="loanNo" id="loanNo" class="group03 clearFee" maxlength="14" size="14" style=""/>
										</td>
						             </tr>
									 -->
									 <tr>										
							           <td class="hd1"><wicket:message key="L140M01R.004">費用金額</wicket:message></td>
							           <td>									   
									        <select id="feeSwft" name="feeSwft" combokey="Common_Currcy" space="true" />
                                            <input type="text" id="feeAmt" name="feeAmt" size="18" maxlength="19" integer="13" class="numeric required" />
                                            <wicket:message key="other.money"><!-- 元--></wicket:message>
							           </td>
						             </tr>
									 <tr>										
							           <td class="hd1"><wicket:message key="L140M01R.005">備註</wicket:message></td>
									   <td><textarea type="text" id="feeMemo" name="feeMemo" maxlength="100" maxlengthC="50" size="100" cols="70"></textarea></td>
							         </tr>
						            </table>
				                </div>
				                </div>	
								
								</td>	
								</tr>			
				 </table>
				</fieldset>
					
                <input type="hidden" name="purpose" id="purpose" />
                <input type="hidden" name="resource" id="resource" />
            </form>
			
			
			<div id="C120M01AThickBox" style="display:none;" >
			<div id="gridC120M01A" />
			</div>
			
			<div id="L140M01AThickBox" style="display:none;" >
			<div id="gridL140M01A" />
			</div>
			
            <script type="text/javascript">
                var obj = API.loadCombos(["cls1141_purpose", "cls1141_resource"]);
                $("[name=purposeTemp]").setItems({
                    space: true,
                    item: obj["cls1141_purpose"],
                    format: "{key}",
                    size: 5
                });
                
                $("[name=resourceTemp]").setItems({
                    space: true,
                    item: obj["cls1141_resource"],
                    format: "{key}",
                    size: 5
                });
                
                
                
                
                
                
                function setPurpose(value){
                    $("[name=purposeTemp]").val(value.split("|")).change();
                }
                
                function setResource(value){
                    $("[name=resourceTemp]").val(value.split("|")).change();
                }
                
                initDfd.done(function(obj){
                
                    $("[name=purposeTemp]").change(function(){
                        $("#purpose").val($("[name=purposeTemp]:checked").map(function(){
                            return $(this).val();
                        }).get().join("|"));
                        if ($(this).val() == "3") {
                            if (this.checked) {
                                $("#purposeSpan").show();
                            } else {
                                $("#purposeSpan").hide().find("#purposeOth").val("");
                            }
                        }
                    });
                    $("[name=resourceTemp]").change(function(){
                        $("#resource").val($("[name=resourceTemp]:checked").map(function(){
                            return $(this).val();
                        }).get().join("|"));
                        
                        if ($(this).val() == "3") {
                            if ($(this).attr("checked")) {
                                $("#resourceSpan").show();
                            } else {
                                $("#resourceSpan").hide().find("#resourceOth").val("");
                            }
                        }
                    });
                    //於文件第一次載入時設定
                    setPurpose(obj.purpose);
                    setResource(obj.resource);
                    
                    
                    
                });
            </script>
        </wicket:panel>
    </body>
</html>
