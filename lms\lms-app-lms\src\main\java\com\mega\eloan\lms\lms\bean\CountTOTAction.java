package com.mega.eloan.lms.lms.bean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.model.L140M01A;

/**
 * <pre>
 * 額度明細表加總
 * By CustId + DUPNO 去計算
 * </pre>
 * 
 * @since 2012/5/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/5/9,Rex,new
 *          </ul>
 */
public class CountTOTAction {
	private static Logger logger = LoggerFactory
			.getLogger(CountTOTAction.class);
	/**
	 * 計價幣別
	 */
	@SuppressWarnings("unused")
	private String countCurr = "";

	/**
	 * 額度明細表集合
	 */
	@SuppressWarnings("unused")
	private List<L140M01A> l140m01as = null;
	/**
	 * 利率
	 */
	@SuppressWarnings("unused")
	private BranchRate branchrate = null;
	/**
	 * 加總結果
	 */
	private HashMap<String, CountTOT> finelCount = new HashMap<String, CountTOT>();
	/**
	 * 處理計算過後
	 */
	private ArrayList<CountTOT> CountTOTs = new ArrayList<CountTOT>();
	/**
	 * 存放被共用的額度序號
	 */
	private HashMap<String, String> CommSnoMap = new HashMap<String, String>();

	public CountTOTAction(List<L140M01A> l140m01as, BranchRate branchrate,
			String countCurr) {

		this.countCurr = countCurr;
		this.l140m01as = l140m01as;
		this.branchrate = branchrate;
		for (L140M01A l140m01a : l140m01as) {
			// 當經過重新計算要把調整的註記拿掉
			l140m01a.setValueTune(UtilConstants.DEFAULT.否);
			// 當不為空表示已做過儲存檢核
			if (UtilConstants.Cntrdoc.CHKYN.通過檢核.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.已計算);
			}
			CountTOT counttot = new CountTOT(l140m01a, branchrate);
			CountTOTs.add(counttot);
			String commSno = Util.trim(l140m01a.getCommSno());
			if (Util.isNotEmpty(commSno)) {
				CommSnoMap.put(commSno, "");
			}
		}
		// 查詢該共用額度序號的主借人
		for (L140M01A l140m01a : l140m01as) {
			String cntrNo = Util.trim(l140m01a.getCntrNo());
			if (CommSnoMap.containsKey(cntrNo)) {
				CommSnoMap.put(cntrNo,
						l140m01a.getCustId() + l140m01a.getDupNo());
			}
		}
	}

	/**
	 * 加總邏輯
	 */
	public HashMap<String, CountTOT> count() {
		for (CountTOT countTot : CountTOTs) {
			CountTOT tempCountTOT = null;
			if (finelCount.containsKey(countTot.getKey())) {
				tempCountTOT = finelCount.get(countTot.getKey());
			} else {
				tempCountTOT = new CountTOT(countTot.getCurr(),
						countTot.getCustId(), countTot.getDupNo());
			}
			if (this.isToDoCount(countTot)) {
				tempCountTOT.addLoanTotAmt(countTot.getLoanTotAmt());
				tempCountTOT.addGtAmt(countTot.getGtAmt());
				tempCountTOT.addLVTOTAMT(countTot.getLVTOTAMT());
				tempCountTOT.addLVASSTOTAMT(countTot.getLVASSTOTAMT());
				tempCountTOT.addMinTOTAMT(countTot.getMinTOTAMT());
				tempCountTOT.addMinAssTOTAMT(countTot.getMinAssTOTAMT());
				tempCountTOT.addMultiAmt(countTot.getMultiAmt());
				tempCountTOT.addMultiAssureAmt(countTot.getMultiAssureAmt());
			}
			// 衍生性商品都要加
			tempCountTOT.addLoanTotZAmt(countTot.getLoanTotZAmt());
			tempCountTOT.addLoanTotLAmt(countTot.getLoanTotLAmt());
			finelCount.put(countTot.getKey(), tempCountTOT);
		}
		return finelCount;
	}

	/**
	 * 加總邏輯
	 */
	public void showFinalCount() {
		for (String key : finelCount.keySet()) {
			CountTOT countTOT = finelCount.get(key);
			logger.info("++++++++++++++++++++++++++++++++++");
			logger.info("key =>" + key);
			logger.info("LoanTotAmt =>" + countTOT.getLoanTotAmt());
			logger.info("GtAmt =>" + countTOT.getGtAmt());
			logger.info("LVTOTAMT =>" + countTOT.getLVTOTAMT());
			logger.info("LVASSTOTAMT =>" + countTOT.getLVASSTOTAMT());
			logger.info("MinTOTAMT=>" + countTOT.getMinTOTAMT());
			logger.info("MinAssTOTAMT" + countTOT.getMinAssTOTAMT());
			logger.info("LoanTotZAmt=>" + countTOT.getLoanTotZAmt());
			logger.info("LoanTotLAmt=>" + countTOT.getLoanTotLAmt());
			// logger.info("MultiAmt" + countTOT.getMultiAmt());
			// logger.info("MultiAssureAmt" + countTOT.getMultiAssureAmt());
			logger.info("++++++++++++++++++++++++++++++++++");
		}
	}

	/**
	 * 判斷是否加總
	 * 
	 * @param countTOT
	 *            額度明細表
	 * @return true | false
	 */
	private Boolean isToDoCount(CountTOT countTOT) {
		Boolean result = true;
		if (Util.isNotEmpty(countTOT.getCommSno())) {
			// 當共用額度序號不是空
			// 若為該共用額度序號的主借人 且 共用額度序號不等於 額度序序號 時要不加總
			if (CommSnoMap.get(countTOT.getCommSno()).equals(
					countTOT.getCustId() + countTOT.getDupNo())
					&& !countTOT.getCommSno().equals(countTOT.getCntrNo())) {
				result = false;
			}
		}
		return result;
	}
}
