package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.C900S02BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900S02B;

/** 消金掃描對象編號檔 **/
@Repository
public class C900S02BDaoImpl extends LMSJpaDao<C900S02B, String>
	implements C900S02BDao {
	
	private static BigDecimal MAX_SEQ = new BigDecimal("99999");
	
	@Override
	public C900S02B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	private C900S02B getNextSeq(String ref_cat, Timestamp nowTS)
	throws CapException{
		BigDecimal seq_no = BigDecimal.ZERO;
		//=============
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ref_cat", ref_cat);
		search.addOrderBy("seq_no", true);
		C900S02B exist_model = findUniqueOrNone(search);
		if(exist_model==null){
			seq_no = BigDecimal.ONE;
		}else{
			seq_no = exist_model.getSeq_no().add(BigDecimal.ONE);	
		}
		if(seq_no.compareTo(MAX_SEQ) > 0){
			throw new CapException("該時間區段，流水號已超過上限"+MAX_SEQ
					+"，請等數分鐘後，再重新操作", getClass());
		}
		//=============
		C900S02B model = new C900S02B();
		model.setRef_cat(ref_cat);
		model.setSeq_no(seq_no);
		model.setCreateTime(nowTS);
		//=============
		try{
			this.save(model);	
		}catch(javax.persistence.PersistenceException ex){
			return null;
		}		
		//=============
		return model; 
	}

	@Override
	public C900S02B getNextSeq(String brNo, String clientIP, String empNo, String sysType, String sysFunc)
	throws CapException {
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		String time_fmt = CapDate.getDateTimeFormat(nowTS);
		return getNextSeq(build_ref_cat( brNo, clientIP, empNo, sysType, sysFunc, time_fmt), nowTS);
	}
	
	private String build_ref_cat(String brNo, String clientIP, String empNo, String sysType, String sysFunc, String time_fmt){
		String yyyy = StringUtils.substring(time_fmt, 0, 4);
		String mm = StringUtils.substring(time_fmt, 5, 7);
		String dd = StringUtils.substring(time_fmt, 8, 10);

		String HH = StringUtils.substring(time_fmt, 11, 13);
		String min1 = StringUtils.substring(time_fmt, 14, 15);
		String min2 = StringUtils.substring(time_fmt, 15, 16);
		String sec1 = StringUtils.substring(time_fmt, 17, 18);
		String sec2 = StringUtils.substring(time_fmt, 18, 19);
		
		return String.valueOf(Integer.parseInt(yyyy)-1911)+brNo+sysType
				+ sysFunc
				+ empNo_or_ip(empNo, clientIP)		
				+ _c900s02b_ref_cat_base32(mm)
				+ _c900s02b_ref_cat_base32(dd)				
				+ _c900s02b_ref_cat_base32(HH)
				+ _c900s02b_ref_cat_base32(min1)
				+ _c900s02b_ref_cat_base32(min2)
				+ _c900s02b_ref_cat_base32(sec1)
				+ _c900s02b_ref_cat_base32(sec2)
				;
	}
	
	@SuppressWarnings("unused")
	private String _dayOfWeek(String time_fmt){
		String sign = "";
		if(true){
			int dayOfWeek = CapDate.getDayOfWeek(StringUtils.substring(time_fmt, 0, 10), "yyyy-MM-dd")-1;
			/*
			 週日,週一,....,週六
			   0,   1,    ,   6 
			*/		
			sign = String.valueOf(dayOfWeek);
		}
		if(sign.length()!=1){
			sign = "Z";
		}
		
		return sign;
	}
	
	private String empNo_or_ip(String empNo, String clientIP){
		String str = "000000"+Util.trim(empNo);
		return Util.getRightStr(str, 6);		 
	}
	
	private String _c900s02b_ref_cat_base32(String mm){
		/*input月[1~12], output[A~L]*/
		/*input每10分鐘[0~6], output[A~)]*/
		/*input日[1~31], output[]]*/
		//避免字母[I,O]與數字[1,0]搞混
		//base32 缺少0, 1, 8 (0~31)
		return _c900s02b_ref_cat_arr(mm, "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567");
	}
	private String _c900s02b_ref_cat_arr(String str, String arr){
		int ival = Util.parseInt(str);
		String v =  StringUtils.substring(arr, ival, ival+1);
		if(Util.isNotEmpty(v)){
			return v;
		}
		return "9";
	}
	
	@Override
	public void delete_old_C900S02B(){
		ISearch search = createSearchTemplete();
		int days = 7;		
		search.addSearchModeParameters(SearchMode.LESS_THAN, "createTime"
				, TWNDate.toAD(CapDate.shiftDays(CapDate.getCurrentTimestamp(), -1*days))+" 00:00:00");
		search.setMaxResults(3000);		
		List<C900S02B> list = find(search);
		if(list.size()>0){
			delete(list);
		}
	}
}