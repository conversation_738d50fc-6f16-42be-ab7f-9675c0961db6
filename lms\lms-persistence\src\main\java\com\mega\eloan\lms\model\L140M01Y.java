
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 額度特殊註記檔 **/
@Entity
@Table(name = "L140M01Y", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId","refType","refValue","refModel","refMainId","refOid"}))
public class L140M01Y extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 類別（定義在 UtilConstants.L140M01Y_refType）  */
	@Column(name = "REFTYPE", length = 50, columnDefinition = "CHAR(50)")
	private String refType;

	/** 關聯值 */
	@Column(name = "REFVALUE", length = 14, columnDefinition = "CHAR(14)")
	private String refValue;
	
	/** 關聯TableName */
	@Column(name = "REFMODEL", length = 15, columnDefinition = "CHAR(15)")
	private String refModel;

	/** 關聯MainId */
	@Column(name = "REFMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String refMainId;

	/** 關聯Oid */
	@Column(name = "REFOID", length = 32, columnDefinition = "CHAR(32)")
	private String refOid;
	
	/** 不符銀行法12-1自用住宅規範選項  */
	@Column(name = "N12_1FLAG", length = 1, columnDefinition = "CHAR(1)")
	private String n12_1Flag;
	
	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/** 使用專案      select * from com.bcodetype where codetype='ploan_plan' and codedesc2='usePlan' */
	@Column(name="USEPLAN", length=20, columnDefinition="VARCHAR(20)")
	private String usePlan;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	public String getRefType() {
		return refType;
	}

	public void setRefType(String refType) {
		this.refType = refType;
	}

	public String getRefValue() {
		return refValue;
	}

	public void setRefValue(String refValue) {
		this.refValue = refValue;
	}

	public String getRefModel() {
		return refModel;
	}

	public void setRefModel(String refModel) {
		this.refModel = refModel;
	}

	public String getRefMainId() {
		return refMainId;
	}

	public void setRefMainId(String refMainId) {
		this.refMainId = refMainId;
	}

	public String getRefOid() {
		return refOid;
	}

	public void setRefOid(String refOid) {
		this.refOid = refOid;
	}

	public String getN12_1Flag() {
		return n12_1Flag;
	}

	public void setN12_1Flag(String n12_1Flag) {
		this.n12_1Flag = n12_1Flag;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdater() {
		return updater;
	}

	public void setUpdater(String updater) {
		this.updater = updater;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getUsePlan() {
		return usePlan;
	}

	public void setUsePlan(String usePlan) {
		this.usePlan = usePlan;
	}

}
