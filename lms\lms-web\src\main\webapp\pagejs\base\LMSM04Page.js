/**
 * 營運中心(授管處)會議決議共用元件js
 */

 var handlerName = "";
 if (responseJSON.docType == '1') {
     // 企金  最終都會走 LMSM02FormHandler.java
     handlerName = "lms1201formhandler";
 } else if (responseJSON.docType == '2') {
     // 個金
     handlerName = "cls1141m01formhandler";
 } else {
     // 企金
     handlerName = "lms1201formhandler";
 }

 // 引進案由
function importGist(){
    $.ajax({
        handler : handlerName,
        type : "POST",
        dataType : "json",
        action : "getCase",
        data : {
            mainId : responseJSON.mainId
        }
	}).done(function(json) {
            $("#formL120m01h").find("#l120m01hGist").val(json.gist);
    });
}

// 引進授權層級
function importAuthLvlStr(){
    $("#authLvlStrThickbox").thickbox({
        title: i18n.def['selectOption'],
        width: 400,
        height: 30,
        align: "center",
        valign: "bottom",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var authLvlVal = $("#authLvlStrList option:selected").val();
                var authLvlStr = $("#authLvlStrList option:selected").html();
                if (authLvlVal == "") {
                    return CommonAPI.showErrorMessage(i18n.def["grid_selector"]);
                }

                $("#formL120m01h").find("#authLvlStr").val(authLvlStr);

                $.thickbox.close();
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}

function saveSignContent0(){
    var my_dfd = $.Deferred();

    var $formL120m01h = $("#formL120m01h");
    $.ajax({
        type: "POST",
        handler: handlerName,
        data: $.extend({
            formAction: "saveSignContent0",
            formL120m01h: JSON.stringify($formL120m01h.serializeData()),
            mainid: responseJSON.mainId,
            txCode: responseJSON.txCode
        }, (userInfo.unitNo == "920" ||
        userInfo.unitNo == "922" ||
        userInfo.unitNo == "931" ||
        userInfo.unitNo == "932" ||
        userInfo.unitNo == "933" ||
        userInfo.unitNo == "934" ||
        userInfo.unitNo == "935") ? {
            isArea: true
        } : {})
	}).done(function(responseData){
            var itemDscr08 = responseData.itemDscr08;
            var itemDscr0D = responseData.itemDscr0D;
            var page = responseJSON.page;
            if (itemDscr08 != undefined && itemDscr08 != null) {
                if (page == "15") {
                    setCkeditor2("itemDscr08", itemDscr08);
                }
            }
            if (itemDscr0D != undefined && itemDscr0D != null) {
                if (page == "12") {
                    setCkeditor2("itemDscr0D", itemDscr0D);
                }
            }

            my_dfd.resolve();
    });
    return my_dfd.promise();
}

function chkSignContent0(type){
    var my_dfd = $.Deferred();

    $.ajax({
        type: "POST",
        handler: "lms1201formhandler",
        data: $.extend({
            formAction: "chkSignContent0",
            mainid: responseJSON.mainId,
            type: type
        }, (userInfo.unitNo == "920" ||
        userInfo.unitNo == "922" ||
        userInfo.unitNo == "931" ||
        userInfo.unitNo == "932" ||
        userInfo.unitNo == "933" ||
        userInfo.unitNo == "934" ||
        userInfo.unitNo == "935") ? {
            isArea: true
        } : {})
	}).done(function(responseData){
            my_dfd.resolve(responseData);
    });
    return my_dfd.promise();
}

function askCase(needAsk){
    var my_dfd = $.Deferred();

    if(needAsk) {
        $("#caseLvlStrThickbox").thickbox({
            title: i18n.def['selectOption'],
            width: 40,
            height: 30,
            align: "center",
            valign: "bottom",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var caseLvlVal = $("#caseLvlStrList option:selected").val();
                    // html為中文 透過js後送會變成編碼
                    //var caseLvlStr = $("#caseLvlStrList option:selected").html();
                    if (caseLvlVal == "") {
                        return CommonAPI.showErrorMessage(i18n.def["grid_selector"]);
                    }
                    my_dfd.resolve({'caseLvlVal': caseLvlVal});
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    } else {
        my_dfd.resolve({'caseLvlVal': ''});
    }
    return my_dfd.promise();
}

function printLMSDoc9(type){
    saveSignContent0().done(function(){
        $.thickbox.close();
        chkSignContent0(type).done(function(json){
            if(json.errorMsg && json.errorMsg != ""){
                CommonAPI.showErrorMessage(json.errorMsg);
            } else {
                var needAsk = false;
                if(json.needAsk && json.needAsk != ""){
                    needAsk = true;
                }

                askCase(needAsk).done(function(json2){
                    if(json.l120m01hOid && json.l120m01hOid != ""){
                        var fullFile = "LMSDoc9" + type;
                        $.capFileDownload({
                            handler: "lmsdownloadformhandler",
                            data: {
                                fileName: fullFile + ".htm",
                                mainId: responseJSON.mainId,
                                l120m01hOid: json.l120m01hOid,
                                caseLvlVal: json2.caseLvlVal,
                                docTempType: fullFile,
                                type: type,
                                fileDownloadName: fullFile + ".doc",
                                serviceName: "lms1201docservice"
                            }
                        });
                    }
                });
            }
        });
    });
}

$(function() {
	var unitNo = userInfo.unitNo;
	setTimeout(function(){
		if(responseJSON.mainDocStatus == "01K" ||
		responseJSON.mainDocStatus == "02K" ||
		responseJSON.mainDocStatus == "03K" ||
		responseJSON.mainDocStatus == "04K" ||
		responseJSON.mainDocStatus == "03K|01K|02K|04K"){
			// 選擇送會簽才會顯示會簽欄位
			$("#commonSign").show();
		}		
	},3000);
	if(unitNo == "920" || unitNo == "922"
		 || unitNo == "931" || unitNo == "932"
		 || unitNo == "933" || unitNo == "934"
		 || unitNo == "935"){
		// 營運中心隱藏決議、案由與增減金額欄位
		$("#formL120m01h").find(".commonDisp").hide();
	}
	$("#formL120m01h").find("#copyPatternNM").one("click",function(){
		lms7205Grid();	
	});
});