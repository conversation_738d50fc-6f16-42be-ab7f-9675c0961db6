/* 
 * MicroEntService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service;

import org.kordamp.json.JSONObject;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01I;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.service.ICapService;

/**
 * <pre>
 * BY 專案共用Service
 * </pre>
 * 
 * @since 2019/09
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/09,009301,new
 *          </ul>
 */
public interface LMSCombinePrintService extends ICapService {

	public void saveFileInfo(String mainId, String fieldId, JSONObject jsonData);

	public void sendCreatDoc(PageParameters params) throws CapMessageException;

	public void clearRpaCombine(String mainId);

	/**
	 * J-112-0508_05097_B1001 Web e-Loan為提升授信簽報效率,
	 * 三大部及授審處可由eloan授信系統依據授審會及常董會提案稿所需文件之順序產生相關提案稿pdf
	 * 
	 * @param l120m01a
	 * @param l120m01i
	 * @return
	 * @throws CapException
	 */
	public boolean checkIsOutNewVer(L120M01A l120m01a, L120M01I l120m01i)
			throws CapException;

}