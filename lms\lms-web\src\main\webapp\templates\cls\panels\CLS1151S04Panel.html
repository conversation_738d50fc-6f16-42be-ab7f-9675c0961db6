<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
        <wicket:panel>
            <form id="CLS1151Form04" name="CLS1151Form04">
                <!--<button type="button" id="cls_showBeforeBt">
                <span class="text-only"><wicket:message key="L140M01A.beforeContent"> 顯示變更前內容</wicket:message></span>
                </button>-->
                <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                    </tr>
                    <tr id="parentCaseNoTr" style="display:none">
                        <td class="hd1" style="width:25%">
                            <span class="text-red">＊</span>
                            <wicket:message key="page4.001">團貸總戶案號</wicket:message>&nbsp;&nbsp;
                            <br/>
                            <button type="button" id="cls_loginParentCaseNoBt">
                                <span class="text-only"><wicket:message key="other.login"><!-- 登錄--></wicket:message></span>
                            </button>
                        </td>
                        <td>
                            <span id="loanMasterNo" class="field"></span>
                            <button type="button" id="cls_queryParentCaseNoBt">
                                <span class="text-only"><wicket:message key="button.queryParentCaseNo"><!-- 查詢總戶案號--></wicket:message></span>
                            </button>
                            <span id="showFirstBossSpan" style="float:right;display:none" class="text-red"><wicket:message key="L140M01A.firstBoss"><!--初貸主管--></wicket:message>：&nbsp;&nbsp;<span id="showFirstBoss" class="field"></span></span>
                        </td>
                        <td class="hd1" style="width:25%">
                            <wicket:message key="page4.002">總額度有效期限</wicket:message>&nbsp;&nbsp;
                            <br/>
                            <button type="button" id="L140M01LExpBDateBt">
                                <span class="text-only"><wicket:message key="other.login"><!-- 登錄--></wicket:message></span>
                            </button>
                        </td>
                        <td style="width:25%">
                            <span id="showExpMonths" class="field"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1" style="width:25%">
                            <span class="text-red">＊</span>
                            <wicket:message key="L140M01A.snoKind"><!--額度控管種類 --></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td colspan="3">
                            <select id="snoKind" name="snoKind" itemType="lms1405m01_snoKind" />
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1" rowspan="2">
                            <span class="docCode5Hide"><span class="text-red">＊</span><wicket:message key="L140M01A.cntrNo"><!-- 額度序號--></wicket:message>&nbsp;&nbsp;
                                <br/>
                                <button type="button" id="cls_newFcltNo">
                                    <span class="text-only"><wicket:message key="btn.number"><!--給號--></wicket:message></span>
                                </button>
                            </span>
                        </td>
                        <td rowspan="2">
                            <span class="docCode5Hide">
                                <input id="cntrNo" name="cntrNo" class="caseReadOnly" readonly="readonly" size="13"/>
                                <span id="showCntrNoName"/>
                            </span>
                        </td>
						
						<td  class="hd2" colspan="2">
							<span class="text-red">
								<wicket:message key="L140M01A.prod02"><!--共用額度序號說明:<br/>1.所輸入之共用額度序號額度金額將不納入額度合計中。<br/>2.(長+短)歡喜理財家請於長擔額度明細表輸入短擔共用額度序號--></wicket:message>&nbsp;&nbsp;
							</span>
                        </td>
						
						
                    </tr>
					<tr>
                        <td class="hd1">
                            <wicket:message key="L140M01A.cntrNoCom"><!-- 共用額度序號--></wicket:message>&nbsp;&nbsp;
                            <br/>
                            <button type="button" id="cls_commonNumBt">
                                <span class="text-only"><wicket:message key="btn.number"><!--給號--></wicket:message></span>
                            </button>
                        </td>
                        <td>
                            <span id="commSno" class="field"/>
						</td>
					</tr>
                    <tr>
                        <td class="hd1">
                            <span style="float:left;display:none" class="caseSpan">
                                <input type="checkbox" id="caseBox2_4" class="caseBox" />
                            </span>
                            <wicket:message key="L140M01A.lastValue"><!--初貸額度--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td colspan="2">
                            <select id="LVCurr" name="LVCurr" itemType="Common_Currcy" />
                            <input type="text" id="LVAmt" name="LVAmt" size="18" maxlength="19" integer="13" class="numeric" />
                            <wicket:message key="other.money"><!-- 元--></wicket:message>
							<span class="text-red"><wicket:message key="L140M01A.Span01"><!--(首次核准額度)--></wicket:message></span>
                        </td>
                        <td rowspan="3">
                            <div align="right">
                                <label>
                                    <input type="checkbox" id="lastValueRefP1" name="lastValueRefP1" value="1" class="nodisabled" />
                                    <wicket:message key="L140M01A.page"><!-- 同前頁--></wicket:message>
                                </label>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <span style="float:left;display:none" class="caseSpan">
                                <input type="checkbox" id="caseBox2_17" class="caseBox" />
                            </span>
                            <wicket:message key="L140M01A.LV2Amt"><!--前准授信額度--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td colspan="2">
                            <select id="LV2Curr" name="LV2Curr" itemType="Common_Currcy" />
                            <input type="text" id="LV2Amt" name="LV2Amt" size="18" maxlength="19" integer="13" class="numeric" />
                            <wicket:message key="other.money"><!-- 元--></wicket:message>							
							<span class="text-red"><wicket:message key="L140M01A.Span02"><!--(前次核准額度)--></wicket:message></span>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <span style="float:left;display:none" class="caseSpan">
                                <input type="checkbox" id="caseBox2_18" class="caseBox" />
                            </span>
                            <wicket:message key="L140M01A.LVAssureAmt"><!--前准擔保授信額度--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td colspan="2">
                            <select id="LVAssureCurr" name="LVAssureCurr" itemType="Common_Currcy" />
                            <input type="text" id="LVAssureAmt" name="LVAssureAmt" size="18" maxlength="19" integer="13" class="numeric" />
                            <wicket:message key="other.money"><!-- 元--></wicket:message>
							<span class="text-red"><wicket:message key="L140M01A.Span03"><!--(前次擔保核准額度)--></wicket:message></span>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <span style="float:left;display:none" class="caseSpan">
                                <input type="checkbox" id="caseBox2_5" class="caseBox" />
                            </span>
                            <wicket:message key="L140M01A.bLAmt"><!--餘　　額--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td colspan="3">
                            <select id="BLCurr" name="BLCurr" itemType="Common_Currcy" />
                            <input type="text" id="BLAmt" name="BLAmt" size="18" maxlength="19" integer="13" class="numeric" />
                            <wicket:message key="other.money"><!-- 元--></wicket:message>
							<span id="desc_BLAmt" style='display:none' class="text-red">※<wicket:message key="L140M01A.message91">團貸總戶時其餘額代表本額度已用額度</wicket:message></span>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <span style="float:left;display:none" class="caseSpan">
                                <input type="checkbox" id="caseBox2_7" class="caseBox" />
                            </span>
                            <span class="text-red">＊</span>
                            <wicket:message key="L140M01A.moneyAmt"><!-- 現請額度--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td colspan="2">
                            <select id="currentApplyCurr" name="currentApplyCurr" itemType="Common_Currcy" />
                            <input type="text" id="currentApplyAmt" name="currentApplyAmt" size="18" maxlength="19" integer="13" class="numeric"/>
                            <wicket:message key="other.money"><!-- 元--></wicket:message>
                            <select id="reUse" name="reUse" itemType="lms1405s0202_reUse"/>
                            <br/>
                            <!--<span class="text-red">※<wicket:message key="L140M01A.message23">若為「條件變更」但並未變更額度時，此欄需填原准額度</wicket:message></span>-->
                        </td>
                        <td>
                            <div align="right">
                                <label>
                                    <input type="checkbox" id="CurrentAppRef" name="CurrentAppRef" value="1" class="nodisabled" />
                                    <wicket:message key="L140M01A.page"><!-- 同前頁--></wicket:message>
                                </label>
                            </div>
                        </td>
                    </tr>
					<tr>
                        <td class="hd1">
                            <span style="float:left;display:none" class="caseSpan">
                                <input type="checkbox" id="caseBox2_23" class="caseBox" />
                            </span>
							<span class="text-red">＊</span>
                            <wicket:message key="L140M01A.assureApplyAmt"><!--現請擔保額度--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td colspan="3">
                            <select id="assureApplyCurr" name="assureApplyCurr" itemType="Common_Currcy" />
                            <input type="text" id="assureApplyAmt" name="assureApplyAmt" size="18" maxlength="19" integer="13" class="numeric" />
                            <wicket:message key="other.money"><!-- 元--></wicket:message>							
                        </td>
                    </tr>
					<tr id="unsecureFlagSpan" style='display:none;'>
						<td class="hd1">
                            <span style="float:left;display:none" class="caseSpan">
                                <input type="checkbox" id="caseBox2_26" class="caseBox" />
                            </span>
                            <span class="text-red">＊</span>
                            <wicket:message key="L140M01a.unsecureFlag"><!--利害關係人敘作無擔保授信註記--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td colspan="3">
                            <span  class="text-red">＊<wicket:message key="L140M01a.unsecureFlag.ps">說明：「授信戶為銀行法/金控法利害關係人且本筆授信額度性質為無擔保，請勾選所符合之下列項目」</wicket:message>                                
								<br/>
								
                                <select id="unsecureFlag" name="unsecureFlag"  itemType="lms140_unsecureFlag" />
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1" rowspan="2">
                            <span style="float:left;display:none" class="caseSpan">
                                <input type="checkbox" id="caseBox2_9" class="caseBox" />
                            </span>
                            <wicket:message key="L140M01A.collectPay"><!--借款收付彙計數 --></wicket:message>&nbsp;&nbsp;
                            <br/>
                            <button type="button" id="cls_collectBT">
                                <span class="text-only"><wicket:message key="other.login"><!-- 登錄--></wicket:message></span>
                            </button>
                            <br/>
                        </td>
                        <td colspan="2">
                            <span>(<wicket:message key="L140M01A.Pay"><!--付--></wicket:message>)</span>
                            <table width="60%" border="0" cellspacing="0" cellpadding="0" id="CPTable">
                                <tr>
                                    <td>
                                        <span id="CPCURR" />
                                    </td>
                                    <td class="pghead">
                                        <span id="CPAMT" />
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td rowspan="2">
                            <div align="right">
                                <label>
                                    <input type="checkbox" id="collectPayRef" name="collectPayRef" value="1" class="nodisabled" />
                                    <wicket:message key="L140M01A.page"><!-- 同前頁--></wicket:message>
                                </label>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <div>
                                (<wicket:message key="L140M01A.Accept"><!--收--></wicket:message>)
                            </div>
                            <table width="60%" border="0" cellspacing="0" cellpadding="0" id="CATable">
                                <tr>
                                    <td>
                                        <span id="CACURR"/>
                                    </td>
                                    <td class="pghead">
                                        <span id="CAAMT" />
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
					<tr>
						<td class="hd1">
						<button type="button" id="cls_inculeUsePar">
                            <span class="text-only"><wicket:message key="L140M01A.message89"><!-- 引進平均動用率--></wicket:message></span>
                        </button>
						</td>
                        <td colspan="3">
                            <wicket:message key="L140M01A.message97"><!-- 資料基準日--></wicket:message>:
                            <input type="text" name="useParDate" id="useParDate" class="date" />
                            &nbsp;&nbsp;&nbsp;&nbsp;<wicket:message key="L140M01A.message87"><!-- 平均動用率--></wicket:message>:
                            <input type="text" name="usePar" id="usePar" class="numeric" positiveonly="false" integer="3" fraction="2" maxlength="6" size="7" />&nbsp;&nbsp;
							<span class="text-red"><wicket:message key="L140M01A.message88"><!-- 資料來源為a-Loan之[年]平均動用率。--></wicket:message></span>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <span style="float:left;display:none" class="caseSpan">
                                <input type="checkbox" id="caseBox2_10" class="caseBox" />
                            </span>
                            <span class="text-red">＊</span>
                            <wicket:message key="L140M01A.useDeadline"><!--動用期限--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td colspan="3">
                            <select id="useDeadline" name="useDeadline" itemType="cls1151_useDeadline"/>
                            <input type="text" id="desp1" name="desp1" style="display:none">
                            <input type="text" id="desp2" name="desp2" style="display:none">
                            <span id="moveDurOtfromSpan" style="display:none">
                                <input type="text" id="moveDurOtFrom" name="moveDurOtFrom" size="10"/>
                                ~ 
                                <input type="text" id="moveDurOtEnd" name="moveDurOtEnd" size="10"/>
                            </span>
							<span id="moveDurOtApprEndSpan" style="display:none">
                                ~ 
                                <input type="text" id="moveDurOtApprEnd" name="moveDurOtApprEnd" size="10"/>
                            </span>
                            <br>
                            <span id="useDeadline_not3_and_reUse_1" class="text-red"><wicket:message key="L140M01A.message92"><!-- 不循環使用，選擇非自簽約日起MM個月，不允許房貸線上對保。--></wicket:message></span>
                        </td>
                    </tr>
					<tr>
						<td class="hd1">
                            <wicket:message key="L140M01A.clsLgdInfo"><!--個人戶額度違約損失率資訊--></wicket:message>&nbsp;&nbsp;
                        </td>
						<td colspan="3">
							<p id="clsLgdInfo">
                            </p>
							<div id="expectLgdDesc"></div>
                        </td>
					</tr>
                    <tr>
                        <td class="hd1">
                            <wicket:message key="L140M01A.itemC">風險權數</wicket:message>(<wicket:message key="L140M01A.itemC1">抵減前</wicket:message>)&nbsp;&nbsp;
							<div>
								<a href="img/lms/(111)0586.docx" target="_blank" ><wicket:message key="page4.033">不動產暴險資本計提規範</wicket:message></a>	
							</div>							
                        </td>
                        <td>
                            <input type="text" id="itemC" name="itemC" size="3" maxlength="8" class="numeric nodisabled" integer="3" fraction="2" />
                            %
                            <br/>
                        </td>
                        <td class="hd1">
                            <wicket:message key="L140M01A.itemC">風險權數</wicket:message>(<wicket:message key="L140M01A.itemC2">抵減後</wicket:message>)&nbsp;&nbsp;
                        </td>
                        <td>
                            <input type="text" id="rItemD" name="rItemD" size="3" maxlength="8" class="numeric nodisabled" integer="3" fraction="2" />
                            %
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <span class="text-red">＊</span>
                            <wicket:message key="L140M01A.nPL"><!--分行逾放比--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td colspan="3">
                            <input type="text" id="npl" name="npl" class="nodisabled" size="90" maxlength="120" maxlengthC="100" />
                            <br/>
                            <wicket:message key="other.dateYYYYMM"><!-- 資料年月--></wicket:message>
                            <input id="npldate" name="npldate" size="6" maxlength="7" class="nodisabled" />
                            <br/>
                            <button type="button" id="cls_getNPL">
                                <span class="text-only"><wicket:message key="btn.NPL"><!--引進上月底逾期放款分析表--></wicket:message></span>
                            </button>
                            <br/>
                            <span class="text-red">※<wicket:message key="L140M01A.message46"><!--請參考報表查詢系統月初產生之「LLMLN091 逾期放款分析表」--></wicket:message>(DBU+OBU)</span>
                        </td>
                    </tr>
					<tr class="docCode5Hide">
						<td class="hd1">
							<span style="float:left;display:none" class="caseSpan">
                                <input type="checkbox" id="caseBox2_22" class="caseBox" />
                            </span>
                            <span class="text-red">＊</span>
                            <wicket:message key="L140M01A.chinaLoan">大陸地區授信業務控管註記</wicket:message>&nbsp;&nbsp;
                        </td>
						<td colspan="3">
							<button type="button" id="queryL140M01Q" class="forview">
								<span class="text-only"><wicket:message key="button.search">查詢</wicket:message></span>
							</button>
							<button type="button" id="modifyL140M01Q">
								<span class="text-only"><wicket:message key="button.modify">修改</wicket:message></span>
							</button>
							<br/>
                            <select name="pageNumY" id="pageNumY">
                                		<option value="0" disabled="true"><wicket:message key="L140M01b.printMain"><!-- 印於主表--></wicket:message></option>
                                        <option value="1" disabled="true"><wicket:message key="L140M01b.print01"><!-- 印於附表(一)--></wicket:message></option>
										<option value="4" selected="selected"><wicket:message key="L140M01b.print04"><!-- 印於檢核表--></wicket:message></option>
                            </select>
						</td>
					</tr>
                    <tr>
                        <td class="hd1">
                            <span style="float:left;display:none" class="caseSpan">
                                <input type="checkbox" id="caseBox2_13" class="caseBox" />
                            </span>
                            <wicket:message key="L140M01A.noInsuReason"><!--本案未送保原因--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td>
                            <select id="noInsuReason" name="noInsuReason" itemType="lms1405s02_noInsuReason"/>
                            <span id="noInsuReasonOtherSpan" style="display:none">
                                <input type="text" id="noInsuReasonOther" name="noInsuReasonOther" />
                                <span id="hideMark" style="display:none">%</span>
                            </span>
                        </td>
                        <td class="hd1">
                            <span style="float:left;display:none" class="caseSpan">
                                <input type="checkbox" id="caseBox2_14" class="caseBox" />
                            </span>
                            <wicket:message key="L140M01A.assureTotECurr"><!--擔保授信額度調整--></wicket:message>&nbsp;&nbsp;
                            <br/>
                            <button type="button" id="cls_sayBt" class="forview">
                                <span class="text-only"><wicket:message key="L140M01A.say"><!--說明--></wicket:message></span>
                            </button>
                        </td>
                        <td>
                            <span id="assureTotECurr" class="field"/>&nbsp;
                            <input type="text" id="assureTotEAmt" name="assureTotEAmt" size="18" maxlength="19" integer="13" class="numeric" />
                            <wicket:message key="other.money"><!-- 元--></wicket:message>
                        </td>
                    </tr>
                    <!--後來新增的部分-->
                    <tr>
                        <td class="hd1">
                            <wicket:message key="L140M01A.LVTotAmt"><!--前准額度合計--></wicket:message>&nbsp;&nbsp;
                            <br/>
                            <span class="text-red"><font size="1"><wicket:message key="L140M01A.theNumer"><!--本數字由--></wicket:message></font></span>&nbsp;&nbsp;
                            <br/>
                            <span class="text-red"><font size="1"><wicket:message key="L140M01A.LVTotAmt2"><!--【前准授信額度】合計--></wicket:message></font></span>&nbsp;&nbsp;
                        </td>
                        <td class="rt">
                            <span id="LVTotCurr" class="field" />&nbsp;&nbsp;<span id="LVTotAmt" class="field"/>&nbsp;<wicket:message key="other.money"><!-- 元--></wicket:message>
                        </td>
                        <td class="hd1">
                            <wicket:message key="L140M01A.count2"><!--其中擔保合計--></wicket:message>&nbsp;&nbsp;
                            <br/>
                            <span class="text-red"><font size="1"><wicket:message key="L140M01A.theNumer"><!--本數字由--></wicket:message></font></span>&nbsp;&nbsp;
                            <br/>
                            <span class="text-red"><font size="1"><wicket:message key="L140M01A.LVAssTotAmt"><!--【前准擔保授信額度】合計--></wicket:message></font></span>&nbsp;&nbsp;
                        </td>
                        <td class="rt">
                            <span id="LVAssTotCurr" class="field"/>&nbsp;&nbsp;<span id="LVAssTotAmt" class="field" />&nbsp;<wicket:message key="other.money"><!-- 元--></wicket:message>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <wicket:message key="L140M01A.incApplyTotAmt"><!--新做、增額合計--></wicket:message>&nbsp;&nbsp;
                            <br/>
                            <span class="text-red"><font size="1"><wicket:message key="L140M01A.incApplyTotAmt2"><!--新作、增額合計 = 授信額度合計- 前准額度 + 取消、減額合計--></wicket:message></font></span>&nbsp;&nbsp;
                        </td>
                        <td class="rt">
                            <span id="incApplyTotCurr" class="field" />&nbsp;&nbsp;<span id="incApplyTotAmt" class="field"/>&nbsp;<wicket:message key="other.money"><!-- 元--></wicket:message>
                        </td>
                        <td class="hd1">
                            <wicket:message key="L140M01A.count2"><!--其中擔保合計--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td class="rt">
                            <span id="incAssTotCurr" class="field"/>&nbsp;&nbsp;<span id="incAssTotAmt" class="field"/>&nbsp;<wicket:message key="other.money"><!-- 元--></wicket:message>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <wicket:message key="L140M01A.decApplyTotAmt"><!--取消、減額合計--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td class="rt">
                            <span id="decApplyTotCurr" class="field" />&nbsp;&nbsp;<span id="decApplyTotAmt" class="field"/>&nbsp;<wicket:message key="other.money"><!-- 元--></wicket:message>
                        </td>
                        <td class="hd1">
                            <wicket:message key="L140M01A.count2"><!--其中擔保合計--></wicket:message>&nbsp;&nbsp;							
                        </td>
                        <td class="rt">
                            <span id="decAssTotCurr" class="field" />&nbsp;&nbsp;<span id="decAssTotAmt" class="field"/>&nbsp;<wicket:message key="other.money"><!-- 元--></wicket:message>
                        </td>
                    </tr>
                    <!--後來新增的部分 END-->
                    <tr>
                        <td class="hd1">
                            <span id="editMark" style="color:#FFFF00">＊</span>
                            <wicket:message key="L140M01A.count"><!--授信額度合計--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td class="rt">
                            <span id="LoanTotCurr" class="field"/>&nbsp;&nbsp;<span id="LoanTotAmt" class="field"/>&nbsp;<wicket:message key="other.money"><!-- 元--></wicket:message>
                        </td>
                        <td class="hd1">
                            <wicket:message key="L140M01A.count2"><!-- 其中擔保合計--></wicket:message>&nbsp;&nbsp;
							<br/>
                            <span class="text-red"><font size="1"><wicket:message key="L140M01A.theNumer"><!--本數字由--></wicket:message></font></span>&nbsp;&nbsp;
                            <br/>
                            <span class="text-red"><font size="1"><wicket:message key="L140M01A.LVassureApplyAmt"><!--【現請擔保額度】合計--></wicket:message></font></span>&nbsp;&nbsp;
                        </td>
                        <td class="rt">
                            <span id="assureTotCurr" class="field"/>&nbsp;&nbsp;<span id="assureTotAmt" class="field"/>&nbsp;<wicket:message key="other.money"><!-- 元--></wicket:message>							
                        </td>
                    </tr>
					<!--J-111-0343_05097_B1004 Web e-Loan修改企金額度明細表合計之功能-->
					<tr class="showLgdTotAmt">
                        <td class="hd1"> 
                        	<span style="float:left;display:none" class="caseSpan">
                                <input type="checkbox" id="caseBox2_30" class="caseBox" />
                            </span>
                           <span id="editMarkLgd" style="color:#FFFF00">＊</span>
						   <span id="label_lgdTotAmt_T"></span><!--授信授權額度合計-->
                        </td>  
                        <td colspan="3">
                         	<!--J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計-->
							<p id="lgdTotAmt">
                            </p>
                        </td>
                         
                    </tr>
                    <tr id="multiAmtTr">
                        <td class="hd1">
                            <wicket:message key="L140M01A.multiAmt"><!--L140M01A.multiAmt=各幣別授信額度合計--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td class="rt">
                            <textarea id="multiAmt" name="multiAmt" rows="6" readonly="readonly" class="caseReadOnly"></textarea>
                        </td>
                        <td class="hd1">
                            <wicket:message key="L140M01A.multiAssureAmt"><!--L140M01A.multiAssureAmt=各幣別擔保授信額度合計--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td class="rt">
                            <textarea id="multiAssureAmt" name="multiAssureAmt" rows="6" readonly="readonly" class="caseReadOnly"></textarea>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <span style="float:left;display:none" class="caseSpan">
                                <input type="checkbox" id="caseBox2_16" class="caseBox" />
                            </span>
                            <wicket:message key="L140M01A.rmk"><!--備　　註--></wicket:message>&nbsp;&nbsp;
                        </td>
                        <td colspan="3">
                            <textarea type="text" id="Rmk" name="Rmk" maxlengthC="100" cols="70"></textarea>
                            <br/>
                            <span class="text-red">※<wicket:message key="L140M01A.message38"><!-- 本欄供填寫「額度合計內含之外匯額度或該公司另於聯行之額度合計數」--></wicket:message>。</span>
                        </td>
                    </tr>
                </table>
                <wicket:message key="L140M01A.message39">請按[關閉]回到前一頁執行[計算授信額度合計]，系統會自動算出[授信額度合計]及[其中擔保合計]</wicket:message>
				<input type="checkbox" id="printDetails" name="printDetails" value="1"/>
				<input type="checkbox" id="printDetails" name="printDetails" value="2"/>
				<span id='memo_L140M01A_printDetails'>說明</span>
            </form>
            <!--新增收付彙計數 thickbox  -->
            <div id="CLS_addCollectBox" style="display:none">
                <form id="CLS_L140M01KForm" name="CLS_L140M01KForm">
                    <table class="tb2" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="50%" class="hd1">
                                <wicket:message key="L140M01A.Pay"><!--付--></wicket:message>&nbsp;&nbsp;
                            </td>
                            <td width="50%">
                                <select id="CPCurr" name="CPCurr" itemType="Common_Currcy"/>
                                <input id="CollectPay" name="CollectPay" type="text" size="18" maxlength="19" integer="13" class="numeric" />
                            </td>
                        </tr>
                        <tr>
                            <td width="50%" class="hd1">
                                <wicket:message key="L140M01A.Accept"><!--收--></wicket:message>&nbsp;&nbsp;
                            </td>
                            <td width="50%">
                                <select id="CACurr" name="CACurr" itemType="Common_Currcy"/>
                                <input id="CollectAccept" name="CollectAccept" type="text" size="18" maxlength="19" integer="13" class="numeric" />
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
            <!--新增收付彙計數 thickbox  END--><!--共用額度序號 thickbox  -->
            <div id="cls_commonNumBox" style="display:none">
                <div>
                    <table width="500px" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td>
                                <wicket:message key="L140M01A.message40"><!-- 注意:此欄位僅供「二家公司」或「OBU/DBU」共用額度時使用--></wicket:message>
                                <br/>
                                <wicket:message key="L140M01A.message41"><!-- 請選擇共用額度序號來源--></wicket:message>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <label>
                                    <input type="radio" name="commonNumRadio" value="commonNow" />
                                    <wicket:message key="L140M01A.message42"><!--選擇本簽報書中的額度序號 --></wicket:message>
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <label>
                                    <input type="radio" name="commonNumRadio" value="commonOther"/>
                                    <wicket:message key="L140M01A.message43"><!-- 選擇其他簽報書中的額度序號--></wicket:message>
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <label>
                                    <input type="radio" name="commonNumRadio" value="clean"/>
                                    <wicket:message key="L140M01A.message44"><!-- 清除共用額度序號--></wicket:message>
                                </label>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <!--共用額度序號 thickbox END -->
            <div id="commonNumOtherBox" style="display:none">
                <div>
                    <!--共用額度序號簽報書選擇 thickbox  -->
                    <table width="650px" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td>
                                <div id="gridviewConnomOther" />
                            </td>
                        </tr>
                    </table>
                </div>
            </div><!--共用額度序號簽報書選擇thickbox END -->
            <div id="commonNumOtherSelectBox" style="display:none">
                <!--共用額度序號簽報書選擇 thickbox  -->
                <table width="500px" border="0" cellpadding="0" cellspacing="0">
                    <tr>
                        <td>
                            <div id="gridviewConnomOtherSelect" />
                        </td>
                    </tr>
                </table>
            </div><!--共用額度序號簽報書選擇thickbox END -->
            <div id="itemTypeBox" style="display:none">
                <!--授信科目 thickbox--><span><wicket:message key="L140M01A.message45"><!--科目性質--></wicket:message>：<span id="itemTypename"></span></span>
                <div id="gridviewitemType" />
            </div><!--新增授信科目 thickbox END-->
			<!--新增額度序號 thickbox -->
            <div id="newFcltNoBox" style="display:none">
            	<div id="newFcltNoBoxDiv">
	            	<b><wicket:message key="L140M01A.message29"><!-- 請選擇額度序號來源--></wicket:message></b>
					<table width="580px" border="0" cellpadding="0" cellspacing="0" id="newFcltNo_YES" style="">
	                    <tr>
	                        <td>
	                            <label>
	                                <input type="radio" name="newFcltNoRadio" value="new" />
	                                <wicket:message key="L140M01A.message30"><!-- 產生新號(適用於「新做」案件)--></wicket:message>
	                            </label>
								<br/>
								<table id="tb_newFcltNo_Yes" style="margin-left:20px;margin-top:5px; display:none">
									<tr>
										<td colspan="2">
											<b><wicket:message key="L140M01A.message26"><!--本額度之作帳分行(帳務管理行)是否同為簽報分行--></wicket:message></b>
										</td>							
									</tr>
									<tr>
										<td>
											<input type="radio" name="contentBrankRadio" value="Yes" checked/>
										</td>
										<td>
											<wicket:message key="L140M01A.contentBrankRadioYes"><!-- 是，由簽報行統籌額度管理及帳務還本付息事項--></wicket:message>
										</td>
									</tr>
									<tr>
										<td>
											<input type="radio" name="contentBrankRadio" value="NO"/>
										</td>
										<td>
											<wicket:message key="L140M01A.contentBrankRadioNo"><!-- 否，請輸入作帳分行代號(三碼)，將另由作帳行統籌額度管理及帳務還本付息事項--></wicket:message>
										</td>
									</tr>
									<tr>
										<td></td>
										<td>
											<span style="margin-left: -8px;">(</span>
											<span >
												<wicket:message key="L140M01A.contentBrankRadioNoDesc"><!-- (如本額度係由002分行辦理簽報作業，002與0B6分行共同攤貸，帳掛0B6分行，則請輸入0B6分行代號；或由007分行簽報，002與007分行共同攤貸，由002分行辦理後續簽約、動撥及還本付息等事宜，則請輸入002分行代號)--></wicket:message>
											</span>
											<span>)</span>
										</td>
									</tr>
									<tr id="tr_contentBrankRadio_NO" style="display:none">
										<td></td>
										<td>
											<b><wicket:message key="L140M01A.message28"><!-- 請輸入欲產生額度序號之作帳行分行代碼(三碼)--></wicket:message></b>
							                <br/>
							                <input type="text" id="newFcltNo_No_bankNum" maxlength="3" minlength="3" size="3" class="branchNo upText"/>
							                &nbsp;&nbsp;
							                <button type="button" id="cls_selectBranchBt">
							                	<span class="text-only"><wicket:message key="other.login"><!-- 登錄--></wicket:message></span>
							                </button>
										</td>
									</tr>
								</table>
	                        </td>
	                    </tr>
	                    <tr>
	                        <td>
	                            <label>
	                                <input type="radio" name="newFcltNoRadio" value="original" />
	                                <wicket:message key="L140M01A.message32"><!-- 登錄原案額度序號(適用於舊案續約及條件變更)--></wicket:message>
	                            </label>
								<table id="tb_newFcltNo_NO" style="margin-left:20px;margin-top:5px;display:none;">
									<tr>
										<td>
											<span id="originalInput" style="display:none">
												<b><wicket:message key="L140M01A.message33"><!-- 請輸入原額度序號: 該舊額度序號須已執行轉換，轉換後新編碼之額度序號--></wicket:message>
		                        					<br/>
		                        					【<wicket:message key="L140M01A.message68"><!-- 額度序號長度應為12碼，編碼原則:XXX(分行代號)+X(1:DBU,4:OBU,5:海外)+YYY(年度)+99999(流水號) --></wicket:message>】
			                    				</b>
			                    				<br/>
			                    				<input type="text" id="originalText" size="11" minlength="12" maxlength="12" class="upText" />
			                				</span>
										</td>
									</tr>
								</table>
	                        </td>
	                    </tr>
						<tr>
	                        <td>
	                            <label>
			                        <input type="radio" name="newFcltNoRadio" value="clean" />
			                        <wicket:message key="L140M01A.message27"><!-- 清除額度序號--></wicket:message>
	                            </label>
	                        </td>
	                    </tr>
	                </table>
	                <br/>
	                <br/>
	            </div>
			</div>
            <!--新增額度序號 thickbox END-->
            <div id="newFcltNoSelectBox" style="display:none">
                <!--新增預約額度序號 thickbox --><span><b><wicket:message key="L140M01A.message36"><!-- 請選取預約之額度序號--></wicket:message></b>
                    <select id="newFcltNoSelect">
                    </select>
                </span>
            </div>
            <!--新增預約額度序號 thickbox END-->
            <div id="localPageBox" style="display:none;">
                <!--本票詞庫 thickbox -->
                <table width="300px" border="0" cellpadding="0" cellspacing="0">
                    <tr>
                        <td>
                            <wicket:message key="L140M01A.message37"><!-- 請選擇本票詞庫--></wicket:message>：
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <select id="localPageSelect" name="localPageSelect" itemType="lms1405s02_checkNote" />
                        </td>
                    </tr>
                </table>
            </div>
            <!--本票詞庫 thickbox END-->
            <div id="cls_sayBox" style="display:none">
                <!--擔保授信額度調整說明 thickbox  --><span>1.<wicket:message key="L140M01A.message64">此欄主要供「應收信用狀款項」科目當其額度性質為「擔保」時，用來調整計算後的「其中擔保合計」之用。</wicket:message>
                    <br/>
                    2.<wicket:message key="L140M01A.message65">請離開回到前一頁執行【計算授信額度合計】方能得到扣除「擔保授信額度調整」後的「其中擔保合計」金額。</wicket:message>
                    <br/>
                    3.<wicket:message key="L140M01A.message66">此欄之幣別單位與「現請額度」之幣別相同(儲存後會自動顯示)。</wicket:message>
                </span>
            </div>
            <!--收付彙計數 thickbox  -->
            <div id="collectBox" style="display:none">
                <div id="cls_gridviewCollect" />
            </div><!--收付彙計數 thickbox  END--><!--引進平均動用率 thickbox-->
            <div id="selectUseParBox" style="display:none">
                <div id="selectUseParGridview" />
            </div>
            <div id="loginExpMonthsBox" style="display:none">
                <div>
                    <wicket:message key="page4.009">請輸入總額度有效期限自總行核准後幾個月內</wicket:message>:
                    <br/>
                    <wicket:message key="page4.010">請輸入月數(若為半年請輸入6，1年請輸入12，依此類推)</wicket:message>
                    <form action="" id="loginExpMonthsForm" name="loginExpMonthsForm">
                        <input type="text" id="expMonths" name="expMonths" size="3" maxlength="3" class="required numeric" />
                        <!-- 預留防止單一欄位enter 會變成送出-->
                        <input type="text" name="expMonths_text" class="hide" />
                    </form>
                </div>
            </div>
            <!--團貸總戶舊案號 thickbox-->
            <div id="loginParentCaseNoBox" style="display:none">
                <div id="loginParentCaseNoDiv">
                    <form action="" id="loginParentCaseNoForm" name="loginParentCaseNoForm">
                        <label>
                            <input type="radio" id="loginParentCaseNoType" name="loginParentCaseNoType" value="1" class="required" />
                            <wicket:message key="page4.020">1.輸入舊案團貸總戶舊案號。</wicket:message>
                        </label>
                        <br/>
                        <label>
                            <input type="radio" id="loginParentCaseNoType" name="loginParentCaseNoType" value="2" class="required" />
                            <wicket:message key="page4.021">2.重新產生團貸總戶舊案號。</wicket:message>
                        </label>
                        <input type="text" id="parentCaseNo" name="parentCaseNo" size="12" maxlength="12" class="required obuText" />
						<input type="text" style="display:none" />
                    </form>
                </div>
            </div>
            <!--團貸總戶舊案號 thickbox end-->
            <div id="selecOldCntrNoBox" style="display:none">
                <div id="selecOldCntrNoBoxDiv">
                    <select id="oldGrpcntrno" name="oldGrpcntrno"/>
                </div>
            </div>
            <script type="text/javascript" src="pagejs/cls/CLS1151S04Panel.js?r=20240104"></script>
        </wicket:panel>
    </body>
</html>
