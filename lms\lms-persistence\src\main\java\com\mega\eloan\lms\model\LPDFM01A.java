/* 
 * LPDFM01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Set;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 授信 PDF 舊案主檔 **/
@NamedEntityGraph(name = "LPDFM01A-entity-graph", attributeNodes = { @NamedAttributeNode("lpdfa01a") })
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="LPDFM01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class LPDFM01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	
	@SuppressWarnings("unused")
	@ToStringExclude
	@OneToMany(mappedBy="lpdfm01a", fetch = FetchType.LAZY)
	private Set<LPDFS01A> lpdfs01a;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false)
    private LPDFA01A lpdfa01a;
	
	public LPDFA01A getLpdfa01a() {
		return lpdfa01a;
	}
	public void setLpdfa01a(LPDFA01A lpdfa01a) {
		this.lpdfa01a = lpdfa01a;
	}
	
	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * uid<p/>
	 * 記錄主文件實際UniversalID
	 */
	@Size(max=32)
	@Column(name="UID", length=32, columnDefinition="CHAR(32)")
	private String uid;

	/** 
	 * 文件編號<p/>
	 * 記錄主文件串連資料的UNID
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 區部別<p/>
	 * 0.無、1.DBU、4.OBU、5.海外(海外同業, 海外客戶)
	 */
	@Size(max=1)
	@Column(name="TYPCD", length=1, columnDefinition="CHAR(1)")
	private String typCd;

	/** 
	 * 統一編號<p/>
	 * 簽報書：borrower_id<br/>
	 *  動審表：<br/>
	 *  小放會：
	 */
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 
	 * 重覆序號<p/>
	 * 簽報書：DupCode
	 */
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 
	 * 客戶名稱<p/>
	 * 簽報書：borrower
	 */
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 
	 * 辦理單位類別<p/>
	 * ''
	 */
	@Size(max=1)
	@Column(name="UNITTYPE", length=1, columnDefinition="CHAR(1)")
	private String unitType;

	/** 
	 * 編製單位代號<p/>
	 * 簽報書：Branch_ID
	 */
	@Size(max=3)
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;

	/** 
	 * 目前文件狀態<p/>
	 * 簽報書：<br/>
	 *  核准：DocStatus = "3"<br/>
	 *  婉卻：DocStatus = "4"
	 */
	@Size(max=3)
	@Column(name="DOCSTATUS", length=3, columnDefinition="VARCHAR(3)")
	private String docStatus;

	/** 
	 * 文件亂碼<p/>
	 * 簽報書：RandomCode
	 */
	@Size(max=32)
	@Column(name="RANDOMCODE", length=32, columnDefinition="CHAR(32)")
	private String randomCode;

	/** 
	 * 文件URL<p/>
	 * ''
	 */
	@Size(max=40)
	@Column(name="DOCURL", length=40, columnDefinition="VARCHAR(40)")
	private String docURL;

	/** 
	 * 交易代碼<p/>
	 * ''
	 */
	@Size(max=6)
	@Column(name="TXCODE", length=6, columnDefinition="CHAR(6)")
	private String txCode;

	/** 
	 * 建立人員號碼<p/>
	 * ''
	 */
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 
	 * 建立日期<p/>
	 * current timestamp
	 */
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 
	 * 異動人員號碼<p/>
	 * 簽報書：ApprID
	 */
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 
	 * 異動人員名稱<p/>
	 * 簽報書：appraiser
	 */
	@Size(max=30)
	@Column(name="UPDATERNM", length=30, columnDefinition="VARCHAR(30)")
	private String updaterNM;

	/** 
	 * 異動日期<p/>
	 * current timestamp
	 */
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 核准人員號碼<p/>
	 * 簽報書：ReCheckID
	 */
	@Size(max=6)
	@Column(name="APPROVER", length=6, columnDefinition="CHAR(6)")
	private String approver;

	/** 
	 * 核准人員名稱<p/>
	 * 簽報書：ReCheck
	 */
	@Size(max=30)
	@Column(name="APPROVERNM", length=30, columnDefinition="VARCHAR(30)")
	private String approverNM;

	/** 
	 * 核准日期<p/>
	 * 簽報書：sendDateTime
	 */
	@Column(name="APPROVETIME", columnDefinition="TIMESTAMP")
	private Timestamp approveTime;

	/** 
	 * 是否結案<p/>
	 * 'Y':已結案
	 */
	@Size(max=1)
	@Column(name="ISCLOSED", length=1, columnDefinition="CHAR(1)")
	private String isClosed;

	/** 刪除註記 **/
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 
	 * 報表類型<p/>
	 * 1.企金授信<br/>
	 *  2.個金授信<br/>
	 *  3.企金覆審<br/>
	 *  4.個金覆審<br/>
	 *  5.管理報表<br/>
	 *  6.轉檔 log
	 */
	@Size(max=1)
	@Column(name="FORMTYPE", length=1, columnDefinition="CHAR(1)")
	private String formType;

	/** 
	 * 報表名稱
(來源套表名稱)<p/>
	 * ※原Notes「Form」<br/>
	 *  FLMS110M01案件簽報書<br/>
	 *  FLMS120M01案件簽報書<br/>
	 *  FLMS130M01案件簽報書<br/>
	 *  FLMS150M01小放會會議記錄<br/>
	 *  FLMS160M01動用審核表<br/>
	 *  FCLS106M01審核書<br/>
	 *  …<br/>
	 *  FCLS1xxM01審核書<br/>
	 *  <br/>
	 *  <br/>
	 *  政策性留學生貸款報送項目
	 */
	@Size(max=10)
	@Column(name="FORMNAME", length=10, columnDefinition="VARCHAR(10)")
	private String formName;

	/** 
	 * 報表描述<p/>
	 * ※具代表性的文字說明
	 */
	@Size(max=120)
	@Column(name="FORMTEXT", length=120, columnDefinition="VARCHAR(120)")
	private String formText;

	/** 
	 * 日期<p/>
	 * 簽報書：簽案日期(framing_date)<br/>
	 *  審核書：收件日期(re_date)<br/>
	 *  動審表：核定日期(UploadDate)<br/>
	 *  小放會：會議日期(MeetingDate)<br/>
	 *  覆審名單：資料日期()<br/>
	 *  覆審工作底稿：資料日期()<br/>
	 *  覆審報告表：實際覆審日()<br/>
	 *  管理報表：資料日期()
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="CASEDATE", columnDefinition="DATE")
	private Date caseDate;

	/** 
	 * 核准(婉卻)日期<p/>
	 * 簽報書：核准(婉卻)日期(FinalDate)<br/>
	 *  審核書：(checkdate)<br/>
	 *  動審表：辦妥日期()<br/>
	 *  覆審名單：預計覆審日()<br/>
	 *  覆審報告表：上傳控制檔日()<br/>
	 *  管理報表：資料基準日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="ENDDATE", columnDefinition="DATE")
	private Date endDate;

	/** 
	 * 企/個金案件<p/>
	 * 簽報書：<br/>
	 *  1企金<br/>
	 *  2個金
	 */
	@Size(max=1)
	@Column(name="DOCTYPE", length=1, columnDefinition="CHAR(1)")
	private String docType;

	/** 
	 * 授權別<p/>
	 * 簽報書：<br/>
	 *  1授權內<br/>
	 *  2授權外
	 */
	@Size(max=1)
	@Column(name="DOCKIND", length=1, columnDefinition="CHAR(1)")
	private String docKind;

	/** 
	 * 案件別<p/>
	 * 簽報書：<br/>
	 *  1一般<br/>
	 *  2其他<br/>
	 *  3陳復/陳述案<br/>
	 *  4異常通報<br/>
	 *  5團貸案件  (※國內個金案件)
	 */
	@Size(max=1)
	@Column(name="DOCCODE", length=1, columnDefinition="CHAR(1)")
	private String docCode;

	/** 
	 * 案件號碼-年度<p/>
	 * YYYY<br/>
	 *  簽報書：簽案日期(framing_date)
	 */
	@Digits(integer=4, fraction=0, groups = Check.class)
	@Column(name="CASEYEAR", columnDefinition="DECIMAL(4,0)")
	private Integer caseYear;

	/** 
	 * 案件號碼-分行<p/>
	 * 簽報書：Branch_ID
	 */
	@Size(max=3)
	@Column(name="CASEBRID", length=3, columnDefinition="CHAR(3)")
	private String caseBrId;

	/** 
	 * 案件號碼-流水號<p/>
	 * 簽報書：
	 */
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="CASESEQ", columnDefinition="DECIMAL(5,0)")
	private Integer caseSeq;

	/** 
	 * 案件號碼<p/>
	 * 簽報書：案號(Project_No)<br/>
	 *  審核書：案號(Private_No)<br/>
	 *  覆審名單：()<br/>
	 *  覆審報告表：()
	 */
	@Size(max=62)
	@Column(name="CASENO", length=62, columnDefinition="VARCHAR(62)")
	private String caseNo;

	/** 
	 * 授權等級<p/>
	 * 簽報書：<br/>
	 *  ※原Notes「Grant」<br/>
	 *  1 - 分行授權內  2 - 區域中心授權內<br/>
	 *  3 - 分行授權外  4 - 區域中心授權外<br/>
	 *  ※docKind=授權內<br/>
	 *  101/02/01調整<br/>
	 *  1分行授權內<br/>
	 *  2總行授權內<br/>
	 *  3營運中心授權內<br/>
	 *  4母行授權內<br/>
	 *  5分行授權外<br/>
	 *  6營運中心授權外
	 */
	@Size(max=1)
	@Column(name="AUTHLVL", length=1, columnDefinition="VARCHAR(1)")
	private String authLvl;

	/** 
	 * 案件審核層級<p/>
	 * 簽報書：<br/>
	 *  ※原Notes「CaseLevel_No」<br/>
	 *  100/11/25項目編訂<br/>
	 *  1常董會權限<br/>
	 *  2常董會權限簽奉總經理核批<br/>
	 *  3常董會權限簽准由副總經理核批<br/>
	 *  4利費率變更案件由總處經理核定<br/>
	 *  5屬常董會授權總經理逕核案件<br/>
	 *  6總經理權限內<br/>
	 *  7副總經理權限<br/>
	 *  8處長權限<br/>
	 *  9其他(經理)<br/>
	 *  A董事會權限<br/>
	 *  B營運中心營運長/副營運長權限<br/>
	 *  C利費率變更案件由董事長核定<br/>
	 *  D個金處經理權限
	 */
	@Size(max=2)
	@Column(name="CASELVL", length=2, columnDefinition="CHAR(2)")
	private String caseLvl;

	/** 
	 * 內容描述<p/>
	 * ※擺放其他非查詢用欄位內容<br/>
	 *  簽報書：…<br/>
	 *  小放會：案由() ...<br/>
	 *  動審表：先行動用() …<br/>
	 *  …
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name="JSONDATA", columnDefinition="CLOB")
	private String jsonData;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得uid<p/>
	 * 記錄主文件實際UniversalID
	 */
	public String getUid() {
		return this.uid;
	}
	/**
	 *  設定uid<p/>
	 *  記錄主文件實際UniversalID
	 **/
	public void setUid(String value) {
		this.uid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 記錄主文件串連資料的UNID
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  記錄主文件串連資料的UNID
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得區部別<p/>
	 * 0.無、1.DBU、4.OBU、5.海外(海外同業, 海外客戶)
	 */
	public String getTypCd() {
		return this.typCd;
	}
	/**
	 *  設定區部別<p/>
	 *  0.無、1.DBU、4.OBU、5.海外(海外同業, 海外客戶)
	 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/** 
	 * 取得統一編號<p/>
	 * 簽報書：borrower_id<br/>
	 *  動審表：<br/>
	 *  小放會：
	 */
	public String getCustId() {
		return this.custId;
	}
	/**
	 *  設定統一編號<p/>
	 *  簽報書：borrower_id<br/>
	 *  動審表：<br/>
	 *  小放會：
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 
	 * 取得重覆序號<p/>
	 * 簽報書：DupCode
	 */
	public String getDupNo() {
		return this.dupNo;
	}
	/**
	 *  設定重覆序號<p/>
	 *  簽報書：DupCode
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得客戶名稱<p/>
	 * 簽報書：borrower
	 */
	public String getCustName() {
		return this.custName;
	}
	/**
	 *  設定客戶名稱<p/>
	 *  簽報書：borrower
	 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 
	 * 取得辦理單位類別<p/>
	 * ''
	 */
	public String getUnitType() {
		return this.unitType;
	}
	/**
	 *  設定辦理單位類別<p/>
	 *  ''
	 **/
	public void setUnitType(String value) {
		this.unitType = value;
	}

	/** 
	 * 取得編製單位代號<p/>
	 * 簽報書：Branch_ID
	 */
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/**
	 *  設定編製單位代號<p/>
	 *  簽報書：Branch_ID
	 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 
	 * 取得目前文件狀態<p/>
	 * 簽報書：<br/>
	 *  核准：DocStatus = "3"<br/>
	 *  婉卻：DocStatus = "4"
	 */
	public String getDocStatus() {
		return this.docStatus;
	}
	/**
	 *  設定目前文件狀態<p/>
	 *  簽報書：<br/>
	 *  核准：DocStatus = "3"<br/>
	 *  婉卻：DocStatus = "4"
	 **/
	public void setDocStatus(String value) {
		this.docStatus = value;
	}

	/** 
	 * 取得文件亂碼<p/>
	 * 簽報書：RandomCode
	 */
	public String getRandomCode() {
		return this.randomCode;
	}
	/**
	 *  設定文件亂碼<p/>
	 *  簽報書：RandomCode
	 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/** 
	 * 取得文件URL<p/>
	 * ''
	 */
	public String getDocURL() {
		return this.docURL;
	}
	/**
	 *  設定文件URL<p/>
	 *  ''
	 **/
	public void setDocURL(String value) {
		this.docURL = value;
	}

	/** 
	 * 取得交易代碼<p/>
	 * ''
	 */
	public String getTxCode() {
		return this.txCode;
	}
	/**
	 *  設定交易代碼<p/>
	 *  ''
	 **/
	public void setTxCode(String value) {
		this.txCode = value;
	}

	/** 
	 * 取得建立人員號碼<p/>
	 * ''
	 */
	public String getCreator() {
		return this.creator;
	}
	/**
	 *  設定建立人員號碼<p/>
	 *  ''
	 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 
	 * 取得建立日期<p/>
	 * current timestamp
	 */
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/**
	 *  設定建立日期<p/>
	 *  current timestamp
	 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 
	 * 取得異動人員號碼<p/>
	 * 簽報書：ApprID
	 */
	public String getUpdater() {
		return this.updater;
	}
	/**
	 *  設定異動人員號碼<p/>
	 *  簽報書：ApprID
	 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 
	 * 取得異動人員名稱<p/>
	 * 簽報書：appraiser
	 */
	public String getUpdaterNM() {
		return this.updaterNM;
	}
	/**
	 *  設定異動人員名稱<p/>
	 *  簽報書：appraiser
	 **/
	public void setUpdaterNM(String value) {
		this.updaterNM = value;
	}

	/** 
	 * 取得異動日期<p/>
	 * current timestamp
	 */
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/**
	 *  設定異動日期<p/>
	 *  current timestamp
	 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得核准人員號碼<p/>
	 * 簽報書：ReCheckID
	 */
	public String getApprover() {
		return this.approver;
	}
	/**
	 *  設定核准人員號碼<p/>
	 *  簽報書：ReCheckID
	 **/
	public void setApprover(String value) {
		this.approver = value;
	}

	/** 
	 * 取得核准人員名稱<p/>
	 * 簽報書：ReCheck
	 */
	public String getApproverNM() {
		return this.approverNM;
	}
	/**
	 *  設定核准人員名稱<p/>
	 *  簽報書：ReCheck
	 **/
	public void setApproverNM(String value) {
		this.approverNM = value;
	}

	/** 
	 * 取得核准日期<p/>
	 * 簽報書：sendDateTime
	 */
	public Timestamp getApproveTime() {
		return this.approveTime;
	}
	/**
	 *  設定核准日期<p/>
	 *  簽報書：sendDateTime
	 **/
	public void setApproveTime(Timestamp value) {
		this.approveTime = value;
	}

	/** 
	 * 取得是否結案<p/>
	 * 'Y':已結案
	 */
	public String getIsClosed() {
		return this.isClosed;
	}
	/**
	 *  設定是否結案<p/>
	 *  'Y':已結案
	 **/
	public void setIsClosed(String value) {
		this.isClosed = value;
	}

	/** 取得刪除註記 **/
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/** 設定刪除註記 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 
	 * 取得報表類型<p/>
	 * 1.企金授信<br/>
	 *  2.個金授信<br/>
	 *  3.企金覆審<br/>
	 *  4.個金覆審<br/>
	 *  5.管理報表<br/>
	 *  6.轉檔 log
	 */
	public String getFormType() {
		return this.formType;
	}
	/**
	 *  設定報表類型<p/>
	 *  1.企金授信<br/>
	 *  2.個金授信<br/>
	 *  3.企金覆審<br/>
	 *  4.個金覆審<br/>
	 *  5.管理報表<br/>
	 *  6.轉檔 log
	 **/
	public void setFormType(String value) {
		this.formType = value;
	}

	/** 
	 * 取得報表名稱
(來源套表名稱)<p/>
	 * ※原Notes「Form」<br/>
	 *  FLMS110M01案件簽報書<br/>
	 *  FLMS120M01案件簽報書<br/>
	 *  FLMS130M01案件簽報書<br/>
	 *  FLMS150M01小放會會議記錄<br/>
	 *  FLMS160M01動用審核表<br/>
	 *  FCLS106M01審核書<br/>
	 *  …<br/>
	 *  FCLS1xxM01審核書<br/>
	 *  <br/>
	 *  <br/>
	 *  政策性留學生貸款報送項目
	 */
	public String getFormName() {
		return this.formName;
	}
	/**
	 *  設定報表名稱
(來源套表名稱)<p/>
	 *  ※原Notes「Form」<br/>
	 *  FLMS110M01案件簽報書<br/>
	 *  FLMS120M01案件簽報書<br/>
	 *  FLMS130M01案件簽報書<br/>
	 *  FLMS150M01小放會會議記錄<br/>
	 *  FLMS160M01動用審核表<br/>
	 *  FCLS106M01審核書<br/>
	 *  …<br/>
	 *  FCLS1xxM01審核書<br/>
	 *  <br/>
	 *  <br/>
	 *  政策性留學生貸款報送項目
	 **/
	public void setFormName(String value) {
		this.formName = value;
	}

	/** 
	 * 取得報表描述<p/>
	 * ※具代表性的文字說明
	 */
	public String getFormText() {
		return this.formText;
	}
	/**
	 *  設定報表描述<p/>
	 *  ※具代表性的文字說明
	 **/
	public void setFormText(String value) {
		this.formText = value;
	}

	/** 
	 * 取得日期<p/>
	 * 簽報書：簽案日期(framing_date)<br/>
	 *  審核書：收件日期(re_date)<br/>
	 *  動審表：核定日期(UploadDate)<br/>
	 *  小放會：會議日期(MeetingDate)<br/>
	 *  覆審名單：資料日期()<br/>
	 *  覆審工作底稿：資料日期()<br/>
	 *  覆審報告表：實際覆審日()<br/>
	 *  管理報表：資料日期()
	 */
	public Date getCaseDate() {
		return this.caseDate;
	}
	/**
	 *  設定日期<p/>
	 *  簽報書：簽案日期(framing_date)<br/>
	 *  審核書：收件日期(re_date)<br/>
	 *  動審表：核定日期(UploadDate)<br/>
	 *  小放會：會議日期(MeetingDate)<br/>
	 *  覆審名單：資料日期()<br/>
	 *  覆審工作底稿：資料日期()<br/>
	 *  覆審報告表：實際覆審日()<br/>
	 *  管理報表：資料日期()
	 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/** 
	 * 取得核准(婉卻)日期<p/>
	 * 簽報書：核准(婉卻)日期(FinalDate)<br/>
	 *  審核書：(checkdate)<br/>
	 *  動審表：辦妥日期()<br/>
	 *  覆審名單：預計覆審日()<br/>
	 *  覆審報告表：上傳控制檔日()<br/>
	 *  管理報表：資料基準日
	 */
	public Date getEndDate() {
		return this.endDate;
	}
	/**
	 *  設定核准(婉卻)日期<p/>
	 *  簽報書：核准(婉卻)日期(FinalDate)<br/>
	 *  審核書：(checkdate)<br/>
	 *  動審表：辦妥日期()<br/>
	 *  覆審名單：預計覆審日()<br/>
	 *  覆審報告表：上傳控制檔日()<br/>
	 *  管理報表：資料基準日
	 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/** 
	 * 取得企/個金案件<p/>
	 * 簽報書：<br/>
	 *  1企金<br/>
	 *  2個金
	 */
	public String getDocType() {
		return this.docType;
	}
	/**
	 *  設定企/個金案件<p/>
	 *  簽報書：<br/>
	 *  1企金<br/>
	 *  2個金
	 **/
	public void setDocType(String value) {
		this.docType = value;
	}

	/** 
	 * 取得授權別<p/>
	 * 簽報書：<br/>
	 *  1授權內<br/>
	 *  2授權外
	 */
	public String getDocKind() {
		return this.docKind;
	}
	/**
	 *  設定授權別<p/>
	 *  簽報書：<br/>
	 *  1授權內<br/>
	 *  2授權外
	 **/
	public void setDocKind(String value) {
		this.docKind = value;
	}

	/** 
	 * 取得案件別<p/>
	 * 簽報書：<br/>
	 *  1一般<br/>
	 *  2其他<br/>
	 *  3陳復/陳述案<br/>
	 *  4異常通報<br/>
	 *  5團貸案件  (※國內個金案件)
	 */
	public String getDocCode() {
		return this.docCode;
	}
	/**
	 *  設定案件別<p/>
	 *  簽報書：<br/>
	 *  1一般<br/>
	 *  2其他<br/>
	 *  3陳復/陳述案<br/>
	 *  4異常通報<br/>
	 *  5團貸案件  (※國內個金案件)
	 **/
	public void setDocCode(String value) {
		this.docCode = value;
	}

	/** 
	 * 取得案件號碼-年度<p/>
	 * YYYY<br/>
	 *  簽報書：簽案日期(framing_date)
	 */
	public Integer getCaseYear() {
		return this.caseYear;
	}
	/**
	 *  設定案件號碼-年度<p/>
	 *  YYYY<br/>
	 *  簽報書：簽案日期(framing_date)
	 **/
	public void setCaseYear(Integer value) {
		this.caseYear = value;
	}

	/** 
	 * 取得案件號碼-分行<p/>
	 * 簽報書：Branch_ID
	 */
	public String getCaseBrId() {
		return this.caseBrId;
	}
	/**
	 *  設定案件號碼-分行<p/>
	 *  簽報書：Branch_ID
	 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/** 
	 * 取得案件號碼-流水號<p/>
	 * 簽報書：
	 */
	public Integer getCaseSeq() {
		return this.caseSeq;
	}
	/**
	 *  設定案件號碼-流水號<p/>
	 *  簽報書：
	 **/
	public void setCaseSeq(Integer value) {
		this.caseSeq = value;
	}

	/** 
	 * 取得案件號碼<p/>
	 * 簽報書：案號(Project_No)<br/>
	 *  審核書：案號(Private_No)<br/>
	 *  覆審名單：()<br/>
	 *  覆審報告表：()
	 */
	public String getCaseNo() {
		return this.caseNo;
	}
	/**
	 *  設定案件號碼<p/>
	 *  簽報書：案號(Project_No)<br/>
	 *  審核書：案號(Private_No)<br/>
	 *  覆審名單：()<br/>
	 *  覆審報告表：()
	 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/** 
	 * 取得授權等級<p/>
	 * 簽報書：<br/>
	 *  ※原Notes「Grant」<br/>
	 *  1 - 分行授權內  2 - 區域中心授權內<br/>
	 *  3 - 分行授權外  4 - 區域中心授權外<br/>
	 *  ※docKind=授權內<br/>
	 *  101/02/01調整<br/>
	 *  1分行授權內<br/>
	 *  2總行授權內<br/>
	 *  3營運中心授權內<br/>
	 *  4母行授權內<br/>
	 *  5分行授權外<br/>
	 *  6營運中心授權外
	 */
	public String getAuthLvl() {
		return this.authLvl;
	}
	/**
	 *  設定授權等級<p/>
	 *  簽報書：<br/>
	 *  ※原Notes「Grant」<br/>
	 *  1 - 分行授權內  2 - 區域中心授權內<br/>
	 *  3 - 分行授權外  4 - 區域中心授權外<br/>
	 *  ※docKind=授權內<br/>
	 *  101/02/01調整<br/>
	 *  1分行授權內<br/>
	 *  2總行授權內<br/>
	 *  3營運中心授權內<br/>
	 *  4母行授權內<br/>
	 *  5分行授權外<br/>
	 *  6營運中心授權外
	 **/
	public void setAuthLvl(String value) {
		this.authLvl = value;
	}

	/** 
	 * 取得案件審核層級<p/>
	 * 簽報書：<br/>
	 *  ※原Notes「CaseLevel_No」<br/>
	 *  100/11/25項目編訂<br/>
	 *  1常董會權限<br/>
	 *  2常董會權限簽奉總經理核批<br/>
	 *  3常董會權限簽准由副總經理核批<br/>
	 *  4利費率變更案件由總處經理核定<br/>
	 *  5屬常董會授權總經理逕核案件<br/>
	 *  6總經理權限內<br/>
	 *  7副總經理權限<br/>
	 *  8處長權限<br/>
	 *  9其他(經理)<br/>
	 *  A董事會權限<br/>
	 *  B營運中心營運長/副營運長權限<br/>
	 *  C利費率變更案件由董事長核定<br/>
	 *  D個金處經理權限
	 */
	public String getCaseLvl() {
		return this.caseLvl;
	}
	/**
	 *  設定案件審核層級<p/>
	 *  簽報書：<br/>
	 *  ※原Notes「CaseLevel_No」<br/>
	 *  100/11/25項目編訂<br/>
	 *  1常董會權限<br/>
	 *  2常董會權限簽奉總經理核批<br/>
	 *  3常董會權限簽准由副總經理核批<br/>
	 *  4利費率變更案件由總處經理核定<br/>
	 *  5屬常董會授權總經理逕核案件<br/>
	 *  6總經理權限內<br/>
	 *  7副總經理權限<br/>
	 *  8處長權限<br/>
	 *  9其他(經理)<br/>
	 *  A董事會權限<br/>
	 *  B營運中心營運長/副營運長權限<br/>
	 *  C利費率變更案件由董事長核定<br/>
	 *  D個金處經理權限
	 **/
	public void setCaseLvl(String value) {
		this.caseLvl = value;
	}

	/** 
	 * 取得內容描述<p/>
	 * ※擺放其他非查詢用欄位內容<br/>
	 *  簽報書：…<br/>
	 *  小放會：案由() ...<br/>
	 *  動審表：先行動用() …<br/>
	 *  …
	 */
	public String getJsonData() {
		return this.jsonData;
	}
	/**
	 *  設定內容描述<p/>
	 *  ※擺放其他非查詢用欄位內容<br/>
	 *  簽報書：…<br/>
	 *  小放會：案由() ...<br/>
	 *  動審表：先行動用() …<br/>
	 *  …
	 **/
	public void setJsonData(String value) {
		this.jsonData = value;
	}
}
