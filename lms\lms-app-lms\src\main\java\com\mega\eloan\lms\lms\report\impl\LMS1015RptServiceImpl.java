package com.mega.eloan.lms.lms.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.LinkedList;
import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.CapMvcParameters;
import com.iisigroup.cap.utils.CapAppContext;
import com.inet.report.ReportException;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.lms.report.LMS1015R00RptService;
import com.mega.eloan.lms.lms.report.LMS1015R01RptService;
import com.mega.eloan.lms.lms.report.LMS1015RptService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;

@Service("lms1015rptservice")
public class LMS1015RptServiceImpl implements FileDownloadService, LMS1015RptService {
	
	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1015RptServiceImpl.class);
	@Resource
	CLSService clsService;
	
	@Resource
	LMS1015R00RptService lms1015R00RptService;
	
	@Resource
	LMS1015R01RptService lms1015R01RptService;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}

	private OutputStream generateReport(PageParameters params) throws IOException, Exception {
		
		List<InputStream> list = new LinkedList<InputStream>();
		
		String rptOid = Util.trim(params.getString("rptOid"));
		
		OutputStream outputStream = null;				
		try {
			String[] dataSplit = rptOid.split("\\|");
			for (String temp : dataSplit) {
				outputStream = null;
				
				String oid = null;
				String type = null;				
				if(true){
					String[] tempSplits = temp.split("\\^");
					
					if (tempSplits.length < 1) {
						oid = "";
					} else {
						oid = tempSplits[0];
					}
					if (tempSplits.length < 2) {
						type = "";
					} else {
						type = tempSplits[1];
					}	
				}
				
				if(Util.equals("1", type)){					
					outputStream = genR00(oid);
				}else if(Util.equals("2", type)){
					outputStream = genR01(oid);
				}	
				
				if(outputStream != null){
					list.add(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()));
				}				
			}
			
			if(list.size()>0){
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(list, outputStream);	
			}			
		}finally{
			
		}
		return outputStream;
	}
	
	public OutputStream genR00(String c120m01a_oid)
	throws FileNotFoundException, IOException, Exception {

		PageParameters params = CapAppContext.getBean("capMvcParameters");
		params.put("oid", c120m01a_oid); 
		return lms1015R00RptService.generateReport(params);
	}
	
	public OutputStream genR01(String c121m01a_oid)
	throws FileNotFoundException, IOException, Exception {
		
		PageParameters params = CapAppContext.getBean("capMvcParameters");
		params.put("oid", c121m01a_oid); 
		return lms1015R01RptService.generateReport(params);		
	}	
}
