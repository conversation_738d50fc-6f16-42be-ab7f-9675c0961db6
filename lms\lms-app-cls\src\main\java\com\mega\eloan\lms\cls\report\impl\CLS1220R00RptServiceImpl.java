/* 
 * CLS1131R01RptServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.cls.report.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.cls.report.CLS1220R00RptService;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * 借保人徵信資料
 * </pre>
 * 
 * @since 2012/11/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/12,Fantasy,new
 *          <li>2013/06/15,調整主借款人和配偶之利害關係人顯示排序問題
 *          <li>2013/07/11,Rex,修改顯示評等
 *          <li>2013/07/17,Rex,修改判斷非兆豐行庫改用判斷其帳戶為14碼的排除加總
 *          </ul>
 */
@Service("cls1220r00rptservice")
public class CLS1220R00RptServiceImpl extends AbstractReportService implements
		CLS1220R00RptService {
	@Resource
	CLS1220Service cls1220Service;
	@Resource
	BranchService branchService;
	@Resource
	CodeTypeService codeTypeService;

	// UPGRADE: 待確認報表是否正確產出
	@Override
	public void setReportData(ReportGenerator rptGenerator,
			PageParameters params) throws CapException, ParseException {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		String mainId = params.getString(EloanConstants.MAIN_ID, EloanConstants.EMPTY_STRING);
		C122M01A c122m01a = cls1220Service.getC122M01A_byMainId(mainId);
		C120S01A c120s01a = cls1220Service.findC120S01A(c122m01a.getMainId(), c122m01a.getCustId(),
				c122m01a.getDupNo());
		if (c120s01a == null) {
			c120s01a = new C120S01A();
		}

		C120S01B c120s01b = cls1220Service.findC120S01B(c122m01a.getMainId(), c122m01a.getCustId(),
				c122m01a.getDupNo());
		if (c120s01b == null) {
			c120s01b = new C120S01B();
		}

		C120S01C c120s01c = cls1220Service.findC120S01C(c122m01a.getMainId(), c122m01a.getCustId(),
				c122m01a.getDupNo());
		if (c120s01c == null) {
			c120s01c = new C120S01C();
		}

		Map<String, String> cls1131m01_edu = codeTypeService.findByCodeType("cls1131m01_edu");
		Map<String, String> cls1131m01_othType = codeTypeService.findByCodeType("cls1131m01_othType");
		Map<String, String> marry = codeTypeService.findByCodeType("marry");
		Map<String, String> lms1205s01_jobTitle = codeTypeService.findByCodeType("lms1205s01_jobTitle");
		Map<String, String> lms1205s01_houseStatus = codeTypeService.findByCodeType("lms1205s01_houseStatus");
		Map<String, String> lms1205s01_cmsStatus = codeTypeService.findByCodeType("lms1205s01_cmsStatus");
		Map<String, String> jobType = codeTypeService.findByCodeType("jobType");
		Map<String, String> purposeMap = codeTypeService.findByCodeType("cls1141_purpose");
		Map<String, String> resourceMap = codeTypeService.findByCodeType("cls1141_resource");

		rptVariableMap.put("prtDate", CapDate.getCurrentDate("yyyy-MM-dd"));
		rptVariableMap.put("applyTS", CapDate.convertTimestampToString(c122m01a.getApplyTS(), "yyyy-MM-dd.HH.mm.ss"));
		rptVariableMap.put("applyDocId", c122m01a.getMainId());
		rptVariableMap.put("custId", c122m01a.getCustId());
		rptVariableMap.put("custName", c122m01a.getCustName());
		rptVariableMap.put("birthday",
				c120s01a.getBirthday() == null ? "" : CapDate.formatDate(c120s01a.getBirthday(), "yyyy-MM-dd"));
		rptVariableMap.put("edu", cls1131m01_edu.get(c120s01a.getEdu()));
		rptVariableMap.put("idCardIssueDate", CapDate.formatDate(c122m01a.getIdCardIssueDate(), "yyyy-MM-dd"));
		rptVariableMap.put("idCardIssueArea", c122m01a.getIdCardIssueArea());

		String idCardChg = Util.trim(c122m01a.getIdCardChgFlag());
		if ("0".equals(idCardChg)) {
			idCardChg = "■初發        □補發        □換發 ";
		} else if ("1".equals(idCardChg)) {
			idCardChg = "□初發        ■補發        □換發 ";
		} else if ("2".equals(idCardChg)) {
			idCardChg = "□初發        □補發        ■換發";
		} else {
			idCardChg = "□初發        □補發        □換發";
		}
		rptVariableMap.put("idCardChgFlag", idCardChg);
		rptVariableMap.put("idCardPhoto", "Y".equals(c122m01a.getIdCardPhoto()) ? "有" : "無");
		rptVariableMap.put("marry", Util.trim(marry.get(c120s01a.getMarry())));
		rptVariableMap.put("child", String.valueOf(CapString.trimNull(c120s01a.getChild())));

		String fTarget = CapString.trimNull(c120s01a.getFTarget());
		fTarget = fTarget.indexOf("請選擇") >= 0 ? "" : fTarget;
		rptVariableMap.put("fTarget", Util.trim(fTarget));

		rptVariableMap.put("fTel", Util.trim(c120s01a.getFTel()));

		String coTarget = Util.trim(c120s01a.getCoTarget());
		coTarget = coTarget.indexOf("請選擇") >= 0 ? "" : coTarget;
		rptVariableMap.put("coTarget", coTarget);

		rptVariableMap.put("coTel", Util.trim(c120s01a.getCoTel()));
		rptVariableMap.put("email", Util.trim(c120s01a.getEmail()));
		rptVariableMap.put("mTel", Util.trim(c120s01a.getMTel()));

		String comTarget = CapString.trimNull(c120s01b.getComTarget());
		comTarget = comTarget.indexOf("請選擇") >= 0 ? "" : comTarget;
		rptVariableMap.put("comTarget", comTarget);

		rptVariableMap.put("comName", c120s01b.getComName());
		rptVariableMap.put("comTel", c120s01b.getComTel());
		rptVariableMap.put("jobType1", jobType.get(c120s01b.getJobType1()));
		rptVariableMap.put("jobType2", lms1205s01_jobTitle.get(c120s01b.getJobType2()));
		rptVariableMap.put("jobTitle", lms1205s01_jobTitle.get(c120s01b.getJobTitle()));
		rptVariableMap.put("seniority", LMSUtil.pretty_numStr(c120s01b.getSeniority()) + "年");
		rptVariableMap.put("payCurr", Util.trim(c120s01b.getPayCurr()));
		rptVariableMap.put("payAmt", Util.trim(c120s01b.getPayAmt()));

		String othType = Util.trim(c120s01b.getOthType());
		String[] othTypes = othType.split("\\|");
		StringBuffer othName = new StringBuffer();
		boolean flag = false;
		for (String othCode : othTypes) {
			if ("".equals(othCode)) {
				continue;
			}
			if (flag == true) {
				othName.append("，");
			}
			othName.append(cls1131m01_othType.get(othCode));
			flag = true;
		}
		rptVariableMap.put("othType", othName.toString());

		rptVariableMap.put("othCurr", Util.trim(c120s01b.getOthCurr()));
		rptVariableMap.put("othAmt", Util.trim(c120s01b.getOthAmt()));
		rptVariableMap.put("houseStatus", lms1205s01_houseStatus.get(c120s01a.getHouseStatus()));
		rptVariableMap.put("cmsStatus", lms1205s01_cmsStatus.get(c120s01a.getCmsStatus()));

		String maturity = "";
		if (c122m01a.getMaturity() != null) {
			maturity = c122m01a.getMaturity().stripTrailingZeros().toPlainString() + "年";
		}
		rptVariableMap.put("maturity", maturity);

		String purpose = "";
		if (Util.isNotEmpty(Util.trim(c122m01a.getPurpose()))) {
			purpose = toStr(c122m01a.getPurpose(), purposeMap);
		}
		rptVariableMap.put("purpose", purpose);

		String resource = "";
		if (Util.isNotEmpty(Util.trim(c122m01a.getResource()))) {
			resource = toStr(c122m01a.getResource(), resourceMap);
		}
		rptVariableMap.put("resource", resource);

		String nowExtend = Util.trim(c122m01a.getNowExtend());
		String extInfo = CapString.trimNull(c122m01a.getExtYear().stripTrailingZeros());
		if ("Y".equals(nowExtend)) {
			extInfo = CapString.isEmpty(CapString.trimNull(c122m01a.getExtYear())) ? "有"
					: "有，" + CapString.trimNull(c122m01a.getExtYear().stripTrailingZeros()) + "年";
		} else if ("N".equals(nowExtend)) {
			extInfo = "無";
		}
		rptVariableMap.put("extInfo", extInfo); // 寬限期

		String notifyWay = Util.trim(c122m01a.getNotifyWay());
		if (Util.equals("1", notifyWay)) {
			notifyWay = "■郵寄    □e-mail   □不通知 ";
		} else if (Util.equals("2", notifyWay)) {
			notifyWay = "□郵寄    ■e-mail   □不通知 ";
		} else if (Util.equals("3", notifyWay)) {
			notifyWay = "□郵寄    □e-mail   ■不通知 ";
		} else {
			notifyWay = "□郵寄    □e-mail   □不通知";
		}
		rptVariableMap.put("notifyWay", notifyWay);

		rptVariableMap.put("applyCurr", c122m01a.getApplyCurr());
		rptVariableMap.put("applyAmt", c122m01a.getApplyAmt().stripTrailingZeros().toPlainString());
		rptVariableMap.put("isPeriodFund", Util.trim(c120s01c.getIsPeriodFund()));
		rptVariableMap.put("busi", Util.trim(c120s01c.getBusi()));
	}

	@Override
	public String getReportTemplateFileName() {
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		return "report/cls/CLS1220R00_" + locale.toString();
	}

	private String toStr(String raw_src, Map<String, String> map) {
		String src = Util.trim(raw_src);
		List<String> r = new ArrayList<String>();
		if (Util.isNotEmpty(src)) {
			for (String s : src.split("\\|")) {
				r.add(LMSUtil.getDesc(map, s));
			}
		}
		return StringUtils.join(r, "、");
	}

}
