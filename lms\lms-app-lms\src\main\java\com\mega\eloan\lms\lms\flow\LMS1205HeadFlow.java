package com.mega.eloan.lms.lms.flow;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;

import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.dao.CommonMetaDao;
import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.gwclient.EmailClient;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FlowNameService.FlowReturnEnum;
import com.mega.eloan.lms.base.service.FlowNameService.FlowbackUnitEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.L120A01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01FDao;
import com.mega.eloan.lms.dao.L120M01HDao;
import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L700M01ADao;
import com.mega.eloan.lms.dao.L730M01ADao;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.L120A01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L120M01H;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 案件簽報書 - 授管處子流程
 * 
 * </pre>
 * 
 * @since 2011/11/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/2,REX,new
 *          <li>2012/12/10,Rex,增加判斷非特殊起案行 不去刪除送會簽的授權檔
 *          <li>2013/1/8,Rex,若typcd =海外 核准文號 需增加外
 *          <li>2013/06/25,Rex,增加缺少的近期已收案件
 *          <li>2017/01/06,Johnny Lin J-105-0302 簽報發信件通知功能
 *          </ul>
 */
@Component
public class LMS1205HeadFlow extends AbstractFlowHandler {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1205HeadFlow.class);

	@Resource
	CommonMetaDao metaDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L700M01ADao l700m01aDao;

	@Resource
	L120A01ADao l120a01aDao;

	@Resource
	L730M01ADao l730m01aDao;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	C120M01ADao c120m01aDao;
	@Resource
	L120S01ADao l120s01aDao;

	@Resource
	L120M01HDao l120m01hDao;

	@Resource
	L120M01FDao l120m01fDao;

	@Resource
	DocLogService docLogService;

	@Resource
	LMSService lmsService;

	@Resource
	CLSService clsService;

	@Resource
	NumberService numberService;
	@Resource
	private EmailClient emailClient;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	private SysParameterService sysParameterService;

	public static final String FLOW_CODE = "LMS1205HeadFlow";

	@Transition(node = "授管處_待放行", value = "to授管處流程判斷")
	public void decision(FlowInstance instance) {
		// 如果是核定、泰國要走泰國提會流程
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		// 當不等於異常通報及陳復述案才檢查額度明細表
		if (!UtilConstants.Casedoc.DocCode.異常通報.equals(meta.getDocCode())
				&& !UtilConstants.Casedoc.DocCode.陳復陳述案.equals(meta
						.getDocCode())) {
			// 案件簽報書內批覆表有一份是核准即是核准
			List<L140M01A> l140m01as = null;
			// J-111-0488_05097_B1001 Web
			// e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
			if (lmsService.isOverAuthSendHead(meta)) {
				l140m01as = l140m01aDao.findL140m01aListByL120m01cMainId(
						meta.getMainId(), UtilConstants.Cntrdoc.ItemType.額度明細表,
						FlowDocStatusEnum.已核准.getCode());
			} else {
				l140m01as = l140m01aDao.findL140m01aListByL120m01cMainId(
						meta.getMainId(), UtilConstants.Cntrdoc.ItemType.額度批覆表,
						FlowDocStatusEnum.已核准.getCode());
			}

			String result = (String) instance.getAttribute("result");
			if (!"to退回".equals(result)) {
				// 檢查主管不能與覆核人員相同
				lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.授管處,
						UtilConstants.STAFFJOB.經辦L1);
				// (108)第 3230 號
				if (l140m01as.isEmpty()
						&& !(LMSUtil.isSpecialBranch(Util.trim(meta
								.getCaseBrId())) && (UtilConstants.Casedoc.AreaChk.送會簽
								.equals(Util.trim(meta.getAreaChk())) || UtilConstants.Casedoc.AreaChk.送初審
								.equals(Util.trim(meta.getAreaChk()))))) {
					instance.setAttribute("result", "to已婉卻");
				} else {
					IBranch branch = branchService
							.getBranch(meta.getCaseBrId());
					String brNoType = branch.getCountryType();
					// 這裡經由行別來判斷是哪個國家的流程
					if (UtilConstants.Country.泰國.equals(brNoType)) {
						instance.setAttribute("result", "to泰國提會流程");
					} else {
						String caseBrid = Util.trim(meta.getCaseBrId());
						// 當簽案單位為總處營業單位且為國內送會簽案件時，跑分行會簽流程(LMS1201Flow)
						if ((UtilConstants.Casedoc.typCd.DBU.equals(meta
								.getTypCd()) || UtilConstants.Casedoc.typCd.OBU
								.equals(meta.getTypCd()))
								&& (UtilConstants.BankNo.國外部.equals(caseBrid)
										|| UtilConstants.BankNo.財富管理處
												.equals(caseBrid)
										|| UtilConstants.BankNo.國金部
												.equals(caseBrid)
										|| UtilConstants.BankNo.財務部
												.equals(caseBrid)
										|| UtilConstants.BankNo.金控總部分行
												.equals(caseBrid)
										|| UtilConstants.BankNo.授信行銷處
												.equals(caseBrid)
										|| UtilConstants.BankNo.消金業務處
												.equals(caseBrid) || UtilConstants.BankNo.私銀處作業組
										.equals(caseBrid))) {
							if (UtilConstants.Casedoc.AreaChk.送會簽.equals(Util
									.trim(meta.getAreaChk()))
									|| UtilConstants.Casedoc.AreaChk.送初審
											.equals(Util.trim(meta.getAreaChk()))) {
								instance.setAttribute("flowCode", "LMS1201Flow");
								instance.setAttribute("result", "to分行已會簽");
							} else {
								instance.setAttribute("result", "to核定");
							}
						}
					}
				}
			}
		} else {
			String result = (String) instance.getAttribute("result");
			if (!"to退回".equals(result)) {
				instance.setAttribute("result", "to核定");
			}
		}
	}

	@Transition(node = "授管處流程判斷", value = "to泰國提會流程")
	public void toTh(FlowInstance instance) throws FlowException, CapException {
		this.toEndAction(instance, CreditDocStatusEnum.海外_已核准.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4, true);
		// J-105-0302 簽報發信件通知功能
		notifyByMail(instance, "1", CreditDocStatusEnum.授管處_已核准.name());
	}

	@Transition(node = "授管處_審查中", value = "to待放行核定")
	public void decision2(FlowInstance instance) throws FlowException,
			CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// 如果是核定、泰國要走泰國提會流程
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);

		String result = (String) instance.getAttribute("result");

		// 授審處007583 黃書筠反映，待放行/待核定GRID，分行經辦顯示為918T2
		// 所以只好在硬塞一次
		if ("to待放行".equals(result)) {
			if (UtilConstants.unitType.授管處.equals(user.getUnitType())) {

				L120M01F l120m01f = l120m01fDao.findByMainIdAndKey(
						meta.getMainId(), UtilConstants.BRANCHTYPE.授管處, null,
						UtilConstants.STAFFJOB.經辦L1);
				if (l120m01f != null) {
					LOGGER.info("l120m01f.StaffNo="
							+ Util.trim(l120m01f.getStaffNo())
							+ "，meta.getHqAppraiser()="
							+ Util.trim(meta.getHqAppraiser()));
					if (Util.notEquals(Util.trim(l120m01f.getStaffNo()), "")
							&& Util.notEquals(Util.trim(l120m01f.getStaffNo()),
									Util.trim(meta.getHqAppraiser()))) {
						meta.setHqAppraiser(l120m01f.getStaffNo());
						LOGGER.info("Update HqAppraiser="
								+ Util.trim(meta.getHqAppraiser()));
						l120m01aDao.save(meta);
					}
				}
			}

		}

		if ("to待放行".equals(result) || "to核定".equals(result)) {
			// 檢查是否批覆
			if (UtilConstants.Casedoc.DocCode.陳復陳述案.equals(meta.getDocCode())) {
				meta.setOwnBrId(meta.getCaseBrId());
				// 陳覆陳述直接到已核准
				meta.setOwnBrId(meta.getCaseBrId());
				lmsService.upLnunid(meta);
				instance.setAttribute("result", "to核定");
				l120m01aDao.save(meta);

				// J-105-0302 簽報發信件通知功能
				notifyByMail(instance, "1", CreditDocStatusEnum.授管處_已核准.name());
			}
		}

		meta.setHqMeetFlag(null);
	}

	/**
	 * 檢核若借款人項下所有額度明細表皆婉卻(排除性質為不變者)，則婉卻控管種類不得為刪除控管
	 * 
	 * @param meta
	 *            簽報書主檔
	 * @return true : 婉卻控管種類不得為刪除控管, false : 婉卻控管種類可為刪除控管
	 */
	private boolean checkReject(L120M01A meta) {
		// 案件簽報書內額明細表有一份是核准即是核准
		// //(108)第 3230 號
		if (Util.equals(meta.getAreaChk(), "2")
				|| Util.equals(meta.getAreaChk(), "5")) {
			// 會簽不檢查
			return false;
		}
		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByL120m01cMainId(meta.getMainId(),
						UtilConstants.Cntrdoc.ItemType.額度批覆表,
						FlowDocStatusEnum.已核准.getCode());
		List<L140M01A> realL140m01as = new ArrayList<L140M01A>();
		realL140m01as.addAll(l140m01as);
		for (L140M01A model : l140m01as) {
			if (UtilConstants.Cntrdoc.Property.不變.equals(Util.trim(model
					.getProPerty()))) {
				// 排除性質為不變的額度批覆表
				realL140m01as.remove(model);
			}
		}
		if (realL140m01as.isEmpty()) {
			// 檢核若借款人項下所有額度明細表皆婉卻(排除性質為不變者)，則婉卻控管種類不得為刪除控管
			return true;
		} else {
			return false;
		}
	}

	@Transition(node = "授管處流程決策", value = "to待放行")
	public void decision3(FlowInstance instance) {
		// 如果是核定、泰國要走泰國提會流程
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);

		// 當不等於異常通報及陳復述案才檢查額度明細表
		if (!UtilConstants.Casedoc.DocCode.異常通報.equals(meta.getDocCode())
				&& !UtilConstants.Casedoc.DocCode.陳復陳述案.equals(meta
						.getDocCode())) {
			List<L140M01A> l140m01as2 = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度批覆表, null);
			if (l140m01as2.isEmpty()) {
				if (LMSUtil.isSpecialBranch(Util.trim(meta.getCaseBrId()))
						&& UtilConstants.Casedoc.AreaChk.送會簽.equals(Util
								.trim(meta.getAreaChk()))) {
					// 會簽案件授管處審查中呈主管放行不需強制顯示必須產額度批覆表訊息
				} else if (LMSUtil
						.isSpecialBranch(Util.trim(meta.getCaseBrId()))
						&& UtilConstants.Casedoc.AreaChk.送初審.equals(Util
								.trim(meta.getAreaChk()))) {
					// (108)第 3230 號
					// 會簽案件授管處審查中呈主管放行不需強制顯示必須產額度批覆表訊息
				} else {
					// EFD3015=WARN|尚未產生額度批覆表不得呈主管覆核！！|
					// J-111-0488_05097_B1001 Web
					// e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
					if (!lmsService.isOverAuthSendHead(meta)) {
						throw new FlowMessageException("EFD3015");
					}

				}
			}
			// 檢查額度明細表 的額度序號批附表是否都產生
			HashMap<String, String> checkCntrNo = new HashMap<String, String>();
			for (L140M01A l140m01a : l140m01as2) {
				checkCntrNo.put(l140m01a.getCntrNo(), "");
				if (FlowDocStatusEnum.編製中.getCode().equals(
						l140m01a.getDocStatus())) {
					// (108)第 3230 號
					if (LMSUtil.isSpecialBranch(Util.trim(meta.getCaseBrId()))
							&& (UtilConstants.Casedoc.AreaChk.送會簽.equals(Util
									.trim(meta.getAreaChk())) || UtilConstants.Casedoc.AreaChk.送初審
									.equals(Util.trim(meta.getAreaChk())))) {
						// 會簽案件授管處審查中呈主管放行不需強制顯示必須產額度批覆表訊息
					} else {
						// EFD3012=WARN|所有額度批覆表需由經辦執行【批覆】後才能呈主管覆核|
						throw new FlowMessageException("EFD3012");
					}
				}

				// (108)第 3230 號
				if (!UtilConstants.DEFAULT.是.equals(l140m01a.getChkYN())) {
					if (LMSUtil.isSpecialBranch(Util.trim(meta.getCaseBrId()))
							&& (UtilConstants.Casedoc.AreaChk.送會簽.equals(Util
									.trim(meta.getAreaChk())) || UtilConstants.Casedoc.AreaChk.送初審
									.equals(Util.trim(meta.getAreaChk())))) {
						// 會簽案件授管處審查中呈主管放行不需強制顯示必須產額度批覆表訊息
					} else {
						// EFD3024=WARN|額度批覆表有異動，請執行【計算授信額度合計】以確保額度批覆表之「授信額度合計」為正確數值。|
						throw new FlowMessageException("EFD3024");
					}
				}
			}

			List<L140M01A> l140m01as1 = l140m01aDao
					.findL140m01aListByL120m01cMainIdOrderByCust(
							meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度明細表);
			StringBuffer cntrnoMsg = new StringBuffer();
			for (L140M01A l140m01a : l140m01as1) {
				// if (UtilConstants.Cntrdoc.DataSrc.轉入額度明細表.equals(l140m01a
				// .getDataSrc())) {
				// continue;
				// }
				// 當不存在把這額度序號記錄下來
				if (!checkCntrNo.containsKey(l140m01a.getCntrNo())) {
					cntrnoMsg.append(cntrnoMsg.length() > 0 ? "、" : "");
					cntrnoMsg.append(l140m01a.getCntrNo());
				}
			}
			// 要出現的額度序號訊息
			if (cntrnoMsg.length() > 0) {
				// EFD3036=ERROR|尚未產生額度序號$\{cntrNo\}額度批覆表不得呈主管覆核！|
				HashMap<String, String> extraMessage = new HashMap<String, String>();
				extraMessage.put("cntrNo", " " + cntrnoMsg.toString() + " ");
				FlowMessageException msg = new FlowMessageException("EFD3036");
				msg.setExtraMessage(extraMessage);

				// (108)第 3230 號
				if (LMSUtil.isSpecialBranch(Util.trim(meta.getCaseBrId()))
						&& (UtilConstants.Casedoc.AreaChk.送會簽.equals(Util
								.trim(meta.getAreaChk())) || UtilConstants.Casedoc.AreaChk.送初審
								.equals(Util.trim(meta.getAreaChk())))) {
					// 會簽案件授管處審查中呈主管放行不需強制顯示必須產額度批覆表訊息
				} else {
					// J-111-0488_05097_B1001 Web
					// e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
					if (!lmsService.isOverAuthSendHead(meta)) {
						throw msg;
					}
				}
			}

			// 檢核若借款人項下所有額度批覆表皆婉卻(排除性質為不變者)，則婉卻控管種類不得為刪除控管
			if (checkReject(meta)) {
				if (LMSUtil.isOverSea_CLS(meta)) {
					List<C120M01A> list = c120m01aDao.findByMainId(Util
							.trim(meta.getMainId()));
					for (C120M01A model : list) {
						if (UtilConstants.Casedoc.rejtCase.刪除控管.equals(Util
								.trim(model.getRejectCase()))) {
							// EFD3047=INFO|本案借款人$\{custId\}
							// $\{custName\}下額度皆婉卻，婉卻控管種類不得為【刪除控管】。
							HashMap<String, String> extraMessage = new HashMap<String, String>();
							extraMessage.put(
									"custId",
									Util.trim(model.getCustId())
											+ Util.trim(model.getDupNo()));
							extraMessage.put("custName",
									Util.trim(model.getCustName()));
							FlowMessageException msg = new FlowMessageException(
									"EFD3047");
							msg.setExtraMessage(extraMessage);
							throw msg;
						}
					}
				} else {
					List<L120S01A> list = l120s01aDao.findByMainId(Util
							.trim(meta.getMainId()));
					for (L120S01A model : list) {
						if (UtilConstants.Casedoc.rejtCase.刪除控管.equals(Util
								.trim(model.getRejtCase()))) {
							// EFD3047=INFO|本案借款人$\{custId\}
							// $\{custName\}下額度皆婉卻，婉卻控管種類不得為【刪除控管】。
							HashMap<String, String> extraMessage = new HashMap<String, String>();
							extraMessage.put(
									"custId",
									Util.trim(model.getCustId())
											+ Util.trim(model.getDupNo()));
							extraMessage.put("custName",
									Util.trim(model.getCustName()));
							FlowMessageException msg = new FlowMessageException(
									"EFD3047");
							msg.setExtraMessage(extraMessage);
							throw msg;
						}
					}
				}

			}

		}
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.授管處,
				UtilConstants.STAFFJOB.經辦L1);
		meta.setHqMeetFlag(null);
	}

	// 待補件-退回分行更正
	@Transition(node = "授管處流程決策", value = "to待補件")
	public void backUtil(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		List<L120M01H> listL120m01h = l120m01hDao
				.findByMainId(meta.getMainId());
		meta.setBackUnit(FlowbackUnitEnum.授管_退回分行.getCode());
		String ownBrId = meta.getCaseBrId();
		// 新增一筆傳送紀錄以供近期已收案件用
		lmsService.saveL000M01A(meta, ownBrId);
		// 變更目前編製中分行
		meta.setOwnBrId(ownBrId);
		meta.setHqMeetFlag(null);
		meta.setAreaDocstatus(null);
		IBranch branch = branchService.getBranch(ownBrId);
		// 2012/12/10,Rex,增加判斷非特殊起案行 不去刪除送會簽的授權檔
		if (!LMSUtil.isSpecialBranch(ownBrId)) {
			// 如果是送會簽的案件要把送會簽的授權檔砍掉
			// (108)第 3230 號
			if (UtilConstants.Casedoc.AreaChk.送會簽.equals(meta.getAreaChk())
					|| UtilConstants.Casedoc.AreaChk.送初審.equals(meta
							.getAreaChk())) {
				L120A01A l120a01a = l120a01aDao.findByAuthUnit(
						meta.getMainId(), meta.getAreaBrId());
				if (l120a01a != null) {
					l120a01aDao.delete(l120a01a);
				}
			}
		}

		// 把額度明細表變成編製中，因為額度明細表送上來時會變成待覆核
		lmsService.resetL140M01A(meta, FlowDocStatusEnum.編製中.getCode());
		// 先要檢查目前要退的分行是不是總行，如果要退的分行有總行 要砍掉有總行的那筆資料
		// 不然在兩個 總行跟分行的待補件中都會出現
		if (!Util.isEmpty(branch.getParentBrNo())) {
			L120A01A l120a01a = l120a01aDao.findByAuthUnit(meta.getMainId(),
					branch.getParentBrNo());
			if (l120a01a != null) {
				// 紐約分行0A2 ACC 總行授權內要走分行授權內流程，故分行設定檔有勾0A2 母行為 0A2
				if (Util.notEquals(branch.getParentBrNo(), ownBrId)) {
					l120a01aDao.delete(l120a01a);
				}
			}
		}

		// 退回待補件要順便將授管處提會等相關內容刪除
		// Miller added at 2013/04/19
		if (listL120m01h != null && !listL120m01h.isEmpty()) {
			l120m01hDao.delete(listL120m01h);
		}
		meta.setRptTitle1(null);
		meta.setRptTitle2(null);
		meta.setMeetingType(null);
		meta.setHqMeetFlag(null);
		meta.setRptTitleArea1(null);
		lmsService.save(meta);

		instance.setAttribute("flowCode", LMSUtil.getFlowCode(branchService
				.getBranch(meta.getCaseBrId())));
		instance.setAttribute("result", "waitCase");
		lmsService.deleteL120M01F(meta.getMainId(), null, new String[] {
				UtilConstants.STAFFJOB.執行覆核主管L4,
				UtilConstants.STAFFJOB.提會登錄經辦L7,
				UtilConstants.STAFFJOB.提會放行主管L8 });
		// 新增 核准額度資料檔 MIS.ELF447n
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.海外_待補件.getCode(), meta.getCaseBrId());

		// J-105-0302 簽報發信件通知功能
		notifyByMail(instance, "1", CreditDocStatusEnum.授管處_待更正.name());
	}

	// (108)第 3230 號
	// to退回分行已會簽-退回分行更正-FOR 初審審查
	@Transition(node = "授管處流程決策", value = "to已會簽")
	public void backTo1201Flow(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);

		String ownBrId = meta.getCaseBrId();
		// 新增一筆傳送紀錄以供近期已收案件用
		lmsService.saveL000M01A(meta, ownBrId);
		// 變更目前編製中分行
		meta.setOwnBrId(ownBrId);
		meta.setHqMeetFlag(null);
		meta.setAreaDocstatus(null);

		IBranch branch = branchService.getBranch(ownBrId);
		// 2012/12/10,Rex,增加判斷非特殊起案行 不去刪除送會簽的授權檔
		// if (!LMSUtil.isSpecialBranch(ownBrId)) {
		// // 如果是送會簽的案件要把送會簽的授權檔砍掉
		// if (UtilConstants.Casedoc.AreaChk.送會簽.equals(meta.getAreaChk())) {
		// L120A01A l120a01a = l120a01aDao.findByAuthUnit(
		// meta.getMainId(), meta.getAreaBrId());
		// if (l120a01a != null) {
		// l120a01aDao.delete(l120a01a);
		// }
		// }
		// }

		// (108)第 3230 號
		// 把額度明細表變成編製中，因為額度明細表送上來時會變成待覆核
		if (LMSUtil.isSpecialBranch(Util.trim(meta.getCaseBrId()))
				&& (UtilConstants.Casedoc.AreaChk.送初審審查.equals(Util.trim(meta
						.getAreaChk())))) {

			// 額度明細表要變成待覆核
			lmsService.resetL140M01A(meta, FlowDocStatusEnum.待覆核.getCode());

			// 額度批覆表要變成編製中
			lmsService.resetL140M01AByItemType(meta,
					FlowDocStatusEnum.編製中.getCode(),
					UtilConstants.Cntrdoc.ItemType.額度批覆表);
		}

		lmsService.save(meta);

		instance.setAttribute("flowCode", LMSUtil.getFlowCode(branchService
				.getBranch(meta.getCaseBrId())));
		instance.setAttribute("result", "hasSign");

	}

	// 待補件-退回區域中心更正
	@Transition(node = "授管處流程決策", value = "to退回區域中心")
	public void backArea(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		meta.setBackUnit(FlowbackUnitEnum.授管_退回營運.getCode());
		meta.setHqMeetFlag(null);
		instance.setAttribute("flowCode", LMS1205AreaFlow.FLOW_CODE);
		meta.setOwnBrId(meta.getAreaBrId());
		// 新增近期已收
		lmsService.saveL000M01A(meta, meta.getAreaBrId());
		// 刪除簽章欄
		lmsService.deleteL120M01F(meta.getMainId(),
				UtilConstants.BRANCHTYPE.營運中心,
				new String[] { UtilConstants.STAFFJOB.執行覆核主管L4 });
		// 新增 核准額度資料檔 MIS.ELF447n
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.海外_待補件.getCode(), meta.getAreaBrId());

		// J-105-0302 簽報發信件通知功能 寄給區域中心經辦
		notifyByMail(instance, "3", CreditDocStatusEnum.營運中心_待更正.name());
	}

	// 待陳復
	@Transition(node = "陳復案", value = "to審查中")
	public void backHeadFirst(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A l120m01a = l120m01aDao.findByOid(instanceId);
		// 待陳復案件退回審核中要清除提授審會狀態
		l120m01a.setHqMeetFlag(null);
		l120m01aDao.save(l120m01a);
	}

	// 退回審核中
	@Transition(node = "授管處流程判斷", value = "to退回")
	public void back(FlowInstance instance) {
		// String instanceId = instance.getParentInstanceId() != null ? instance
		// .getParentInstanceId().toString() : instance.getId().toString();
		// L120M01A meta = l120m01aDao.findByOid(instanceId);
	}

	// 撤件
	@Transition(node = "授管處流程判斷", value = "to撤件")
	public void cancelCase(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		meta.setReturnFromBH(FlowReturnEnum.撤件.getCode());
		meta.setHqMeetFlag(null);
		meta.setOwnBrId(meta.getCaseBrId());
		meta.setEndDate(CapDate.getCurrentTimestamp()); // 主檔最後批示日
		l120m01aDao.save(meta);
		// 新增 核准額度資料檔 MIS.ELF447n
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.海外_待撤件.getCode(), meta.getCaseBrId());

		// J-105-0302 簽報發信件通知功能
		notifyByMail(instance, "1", CreditDocStatusEnum.授管處_待分行撤件.name());
	}

	// 撤件2
	@Transition(node = "授管處流程決策", value = "to撤件")
	public void cancelCase2(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		meta.setReturnFromBH(FlowReturnEnum.撤件.getCode());
		meta.setOwnBrId(meta.getCaseBrId());
		meta.setEndDate(CapDate.getCurrentTimestamp()); // 主檔最後批示日
		l120m01aDao.save(meta);
		// 新增 核准額度資料檔 MIS.ELF447n
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.海外_待撤件.getCode(), meta.getCaseBrId());

		// J-105-0302 簽報發信件通知功能
		notifyByMail(instance, "1", CreditDocStatusEnum.授管處_待分行撤件.name());
	}

	// 陳復案
	@Transition(node = "授管處流程判斷", value = "to陳復述")
	public void caseDrc(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		meta.setReturnFromBH(FlowReturnEnum.陳復.getCode());
		// 新增 核准額度資料檔 MIS.ELF447n
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.授管處_待陳復.getCode(), meta.getCaseBrId());
		l120m01aDao.save(meta);
	}

	// 陳復案
	@Transition(node = "授管處流程決策", value = "to陳復述")
	public void caseDrc2(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		meta.setReturnFromBH(FlowReturnEnum.陳復.getCode());
		meta.setHqMeetFlag(null);
		// 新增 核准額度資料檔 MIS.ELF447n
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.授管處_待陳復.getCode(), meta.getCaseBrId());
		l120m01aDao.save(meta);
	}

	// 授管處流程接特殊分行已會簽流程
	@Transition(node = "授管處流程判斷", value = "to分行已會簽")
	public void to1201Flow(FlowInstance instance) throws FlowException,
			CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		// 下面程式碼為上傳用，目前不確定是否要使用，先註解掉
		// this.toEndAction(instance, CreditDocStatusEnum.海外_已核准.getCode(),
		// UtilConstants.STAFFJOB.執行覆核主管L4, false);

		// (108)第 3230 號
		// 把額度明細表變成編製中，因為額度明細表送上來時會變成待覆核
		if (LMSUtil.isSpecialBranch(Util.trim(meta.getCaseBrId()))
				&& (UtilConstants.Casedoc.AreaChk.送初審.equals(Util.trim(meta
						.getAreaChk())))) {
			meta.setAreaChk(UtilConstants.Casedoc.AreaChk.送初審審查);
			l120m01aDao.save(meta);
		}

		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.授管處,
				UtilConstants.STAFFJOB.執行覆核主管L4);
		// 報案考核表要加上覆核人員
		lmsService.setL730M01A(meta, user);
		instance.setAttribute("result", "hasSign");

	}

	// 核定
	@Transition(node = "授管處流程判斷", value = "to核定")
	public void complete(FlowInstance instance) throws FlowException,
			CapException {
		this.toEndAction(instance, CreditDocStatusEnum.海外_已核准.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4, false);

		// J-105-0302 簽報發信件通知功能
		notifyByMail(instance, "1", CreditDocStatusEnum.授管處_已核准.name());

		// J-106-0246-002 Web e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
		lmsService.notifyByMailForG1To940(instance,
				CreditDocStatusEnum.授管處_已核准.name());

	}

	// 已婉卻
	@Transition(node = "授管處流程判斷", value = "to已婉卻")
	public void reject(FlowInstance instance) throws FlowException,
			CapException {
		this.toEndAction(instance, CreditDocStatusEnum.海外_婉卻.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4, false);

		// J-105-0302 簽報發信件通知功能
		notifyByMail(instance, "1", CreditDocStatusEnum.授管處_已婉卻.name());

		// J-106-0246-002 Web e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
		lmsService.notifyByMailForG1To940(instance,
				CreditDocStatusEnum.授管處_已婉卻.name());

	}

	/**
	 * 當案件結束時做的動作
	 * 
	 * @param instance
	 *            flow 流程檔
	 * @param docstatus
	 *            文件狀態
	 * @param staffjob
	 *            職稱欄
	 * @throws CapException
	 * @throws FlowException
	 */
	private void toEndAction(FlowInstance instance, String docstatus,
			String staffjob, Boolean sendToY01) throws FlowException,
			CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		// 用來判斷為營運中心或授管處覆核
		meta.setIsHeadCheck(UtilConstants.DEFAULT.是);
		String docRslt = "";
		if (CreditDocStatusEnum.海外_已核准.getCode().equals(docstatus)) {
			docRslt = UtilConstants.Casedoc.DocRslt.承做;
			// J-110-0330 確定承做，寫入AO帳號管理員
			lmsService.gfnLNF013(meta);
		} else {
			docRslt = UtilConstants.Casedoc.DocRslt.婉卻;
		}
		lmsService.uploadELLNSEEK(meta);
		// J-112-0021_05097_B1001 Web e-Loan企金授信異常通報案件結果副知徵信單位
		meta.setSignNo(lmsService.getHeadSignNo(meta));
		meta.setDocRslt(docRslt);
		if (sendToY01) {
			instance.setAttribute("flowCode", LMS1205TH02Flow.FLOW_CODE);
			// 新增近期已收
			lmsService.saveL000M01A(meta, UtilConstants.BankNo.泰國曼谷總行);
		} else {
			// start 2013/06/25,Rex,增加缺少的近期已收案件
			lmsService.saveL000M01A(meta, meta.getCaseBrId());
			// end 2013/06/25,Rex,增加缺少的近期已收案件
			// 流程結束塞入原本分行
			meta.setOwnBrId(meta.getCaseBrId());
		}
		// 為核定要把額度明細表變成結案
		// J-111-0488_05097_B1001 Web
		// e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
		if (!lmsService.isOverAuthSendHead(meta)) {
			List<L140M01A> l140m01as2 = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度明細表, null);
			for (L140M01A l140m01a : l140m01as2) {
				l140m01a.setDocStatus(FlowDocStatusEnum.結案.getCode());
			}
			l140m01aDao.save(l140m01as2);
		}

		// if (LMSUtil.isSpecialBranch(Util.trim(meta.getCaseBrId()))) {
		// // 當不等於異常通報及陳復述案才檢查額度明細表
		// if (!UtilConstants.Casedoc.DocCode.異常通報.equals(meta.getDocCode())
		// && !UtilConstants.Casedoc.DocCode.陳復陳述案.equals(meta
		// .getDocCode())) {
		// List<L140M01A> l140m01as1 = l140m01aDao
		// .findL140m01aListByL120m01cMainId(meta.getMainId(),
		// UtilConstants.Cntrdoc.ItemType.額度明細表,
		// FlowDocStatusEnum.已核准.getCode());
		// List<L140M01A> l140m01as2 = l140m01aDao
		// .findL140m01aListByL120m01cMainId(meta.getMainId(),
		// UtilConstants.Cntrdoc.ItemType.額度明細表,
		// FlowDocStatusEnum.婉卻.getCode());
		// for (L140M01A model : l140m01as1) {
		// model.setDocStatus(FlowDocStatusEnum.已核准2.getCode());
		// }
		// for (L140M01A model : l140m01as2) {
		// model.setDocStatus(FlowDocStatusEnum.已婉卻2.getCode());
		// }
		// l140m01aDao.save(l140m01as1);
		// l140m01aDao.save(l140m01as2);
		// }
		// } else {
		// // 為核定要把額度明細表變成結案
		// List<L140M01A> l140m01as2 = l140m01aDao
		// .findL140m01aListByL120m01cMainId(meta.getMainId(),
		// UtilConstants.Cntrdoc.ItemType.額度明細表, null);
		// for (L140M01A l140m01a : l140m01as2) {
		// l140m01a.setDocStatus(FlowDocStatusEnum.結案.getCode());
		// }
		// l140m01aDao.save(l140m01as2);
		// }

		// J-111-0488_05097_B1001 Web e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
		List<L140M01A> l140m01as = null;
		if (lmsService.isOverAuthSendHead(meta)) {
			l140m01as = l140m01aDao.findL140m01aListByL120m01cMainId(
					meta.getMainId(), UtilConstants.Cntrdoc.ItemType.額度明細表,
					null);
		} else {
			l140m01as = l140m01aDao.findL140m01aListByL120m01cMainId(
					meta.getMainId(), UtilConstants.Cntrdoc.ItemType.額度批覆表,
					null);
		}

		// 取得所有批附表mainId
		List<String> mainIds = new ArrayList<String>();
		for (L140M01A l140m01a : l140m01as) {
			mainIds.add(l140m01a.getMainId());
			l140m01a.setApprover(user.getUserId());
			l140m01a.setApproveTime(CapDate.getCurrentTimestamp());
		}
		// 當為核准、且為國內個金案件需更新團貸母戶編號 至L140M03A grpCntrNo 以利動審表取得團貸額度明細表
		if (UtilConstants.Casedoc.DocRslt.承做.equals(docRslt)
				&& LMSUtil.isClsCase(meta)
				&& UtilConstants.Casedoc.DocCode.一般.equals(meta.getDocCode())) {
			lmsService.setL140M03AGrpCntrNo(meta, mainIds);
		}
		// 國內個金才會有上傳優惠房貸
		if (LMSUtil.isClsCase(meta)) {
			List<L140M01A> l140m01asBy431 = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度批覆表,
							FlowDocStatusEnum.已核准.getCode());
			for (L140M01A l140m01a : l140m01asBy431) {
				lmsService.upELF431(meta, l140m01a);
			}
		}

		// 設定授信報案考核表
		lmsService.setL730M01A(meta, user);
		l140m01aDao.save(l140m01as);
		l120m01aDao.save(meta);
		// 要去除多餘的營運中心授權檔，不然在已核准或已婉卻會多出現
		lmsService.checkL120A01A(meta);
		// 設定分行放行時間
		lmsService.setSentTime(meta);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.授管處, staffjob);
		// 更新預約額度檔
		lmsService.gfnDB2SetELF442_CNTRNO(meta,
				UtilConstants.Cntrdoc.ACTION.覆核, docstatus);
		// 新增 核准額度資料檔 MIS.ELF447n
		// J-111-0488_05097_B1001 Web
		// e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
		if (lmsService.isOverAuthSendHead(meta)) {
			lmsService.gfnInsertELF447N(meta,
					UtilConstants.Cntrdoc.ItemType.額度明細表, docstatus,
					meta.getCaseBrId());
		} else {
			lmsService.gfnInsertELF447N(meta,
					UtilConstants.Cntrdoc.ItemType.額度批覆表, docstatus,
					meta.getCaseBrId());
		}

		// 海外各單位 授信戶異常通報表 [總處批覆] 自動傳送一份副本至海管處T1
		if (UtilConstants.Casedoc.DocCode.異常通報.equals(meta.getDocCode())) {
			try {
				lmsService.genlms1205r27(null, meta.getMainId());
			} catch (Exception e) {

			}
		}
		lmsService.upLoadMIS(meta);
		lmsService.upLnunid(meta);

		// J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
		if (CreditDocStatusEnum.海外_已核准.getCode().equals(docstatus)) {
			if (!LMSUtil.isClsCase(meta)) {
				lmsService.uploadL902Data(meta, docstatus);
			}
		}

		if (LMSUtil.isClsCase(meta)) {
			// 國內個金簽報書上傳項目
			clsService.upMisByCls(meta);
		}
	}

	// /**
	// * 取得核准文號
	// *
	// * @param meta
	// * 案件簽報書
	// * @return 核准文號
	// * @throws CapMessageException
	// */
	// private String getSignNo(L120M01A meta) throws CapMessageException {
	// //
	// 授管字第"+Mid(tFinalDate,5,2)+"-"+Mid(tFinalDate,8,2) +Format(tSno,"00")+"號"
	// String signNo = "";
	// Date date = meta.getEndDate();
	// if (date != null) {
	// // S_LMS001_gfnHQGetSignSno 當已有核准文號就不重新編
	// if (Util.isEmpty(meta.getSignNo())) {
	// int numBer = numberService.getNumber(CapDate.formatDate(date,
	// UtilConstants.DateFormat.YYYY_MM_DD));
	// String month = TWNDate.toAD(date).substring(5, 7);
	// String day = TWNDate.toAD(date).substring(8, 10);
	// // 2013/1/8,Rex,若typcd =海外 核准文號 需增加外
	// String isType5 = "";
	// if (TypCdEnum.海外.getCode().equals(meta.getTypCd())) {
	// isType5 = "外";
	// } else {
	// isType5 = "兆";
	// }
	//
	// // J-107-0113-001 Web e-Loan授信系統配合組織調整，修改相關功能
	// IBranch nowBranch = branchService
	// .getBranch(UtilConstants.BankNo.授管處);
	//
	// if (Util.trim(nowBranch.getNameABBR()).indexOf("管") != -1) {
	// signNo = "授管字第" + isType5 + month + "-" + day
	// + Util.addZeroWithValue(numBer, 2) + "號";
	// } else {
	// signNo = "授審字第" + isType5 + month + "-" + day
	// + Util.addZeroWithValue(numBer, 2) + "號";
	// }
	//
	// // 2013/1/8,Rex,若typcd =海外 核准文號 需增加外
	// } else {
	// signNo = meta.getSignNo();
	// }
	// }
	// return signNo;
	// }

	/**
	 * 簽報發信件通知功能
	 * 
	 * @param instance
	 * @param branchType
	 *            單位類型 1. 分行 2. 母行/海外總行 3. 營運中心 4. 授管處 5. 徵信承作分行 6. 國金部
	 * @param status
	 * @version <ul>
	 *          <li>2017/01/06,Johnny Lin J-105-0302 簽報發信件通知功能
	 *          </ul>
	 */
	private void notifyByMail(FlowInstance instance, String branchType,
			String status) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		Meta meta = metaDao.findByOid(getDomainClass(), instanceId);
		if (meta == null) {
			// EFD0025=ERROR|執行有誤|
			throw new FlowMessageException("EFD0025");
		}

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String hostAddr = sysParameterService
				.getParamValue(SysParamConstants.MAIL_ADDRESS_HOST);
		String[] toAddrs = null;
		String sendUsrId = "";
		StringBuilder custStr = new StringBuilder();

		// 取得案號
		L120M01A l120m01a = l120m01aDao.findByMainId(meta.getMainId());
		if (l120m01a != null) {
			custStr.append("案號：");
			custStr.append(l120m01a.getCaseNo());
			custStr.append("  ");
		}

		boolean isPerson = false;
		Map<String, Object> custData = (Map<String, Object>) misCustdataService
				.findBussTypeByIdDupNo(meta.getCustId(), meta.getDupNo());
		if (custData != null && !custData.isEmpty()) {
			if (Util.equals(MapUtils.getString(custData, "BUSCD", ""), "060000")
					|| Util.equals(MapUtils.getString(custData, "BUSCD", ""),
							"130300")) {
				isPerson = true;
			}
		}

		// 若為個人戶，需作資料隱碼 (身分證後三碼半形O，客戶名稱第二個字 全形Ｏ)
		custStr.append("客戶代號：");
		custStr.append(isPerson ? (meta.getCustId().substring(0, 7) + "OOO ")
				: meta.getCustId());
		custStr.append(meta.getDupNo() + " ");
		custStr.append(" 客戶名稱：");
		custStr.append(isPerson ? (meta.getCustName().substring(0, 1) + "Ｏ" + meta
				.getCustName().substring(2)) : meta.getCustName());
		custStr.append("\n");

		boolean needSend = true;
		if (StringUtils.indexOf(hostAddr, "@notes.") >= 0) { // Production
			// 取得經辦ID
			L120M01F l120m01f = l120m01fDao.findByMainIdAndKey(
					meta.getMainId(), branchType, null, "L1");
			if (l120m01f != null) {
				sendUsrId = l120m01f.getStaffNo();
				toAddrs = new String[] { sendUsrId.substring(1) + hostAddr };
			} else {
				// 北區分行直送授審處，授審處要退回營運中心，因為當初沒有營運中心經辦，所以傳送MAIL的對象會是NULL，造成無法退回
				needSend = false;
			}
		} else {
			// 測試用，暫時寄給建霖，柏翰
			String testReceiverAddr = "<EMAIL>,<EMAIL>,<EMAIL>";
			toAddrs = testReceiverAddr.split(",");
		}

		// J-112-0278_05097_B1001 Web
		// e-Loan企金授信借款人LGD計算範圍排除進出口額度，並於試算頁簽內加註：借款人違約損失率不含進出口額度。
		if (needSend) {
			String subject = "簽報通知--下列清單內的「案件簽報書」的目前最新狀態為【" + status + "】！";
			emailClient.send(toAddrs, subject, custStr.toString());
		}

	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L120M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}

}