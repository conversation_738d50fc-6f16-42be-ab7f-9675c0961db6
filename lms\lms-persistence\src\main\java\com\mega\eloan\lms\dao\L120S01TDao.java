/* 
 * L120S01TDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S01T;

/** 借款人私募基金檔 **/
public interface L120S01TDao extends IGenericDao<L120S01T> {

	L120S01T findByOid(String oid);
	
	List<L120S01T> findByMainId(String mainId);

	List<L120S01T> findByIndex01(String mainId, String custId, String dupNo, String flag, String privateFundNo);

	List<L120S01T> findByCustId(String mainId, String custId, String dupNo);
}