<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
       <th:block th:fragment="innerPageBody">
       	<script type="text/javascript"> 
     		loadScript('pagejs/lms/LMS7820M01Page');
 		</script>
       
            <div class="button-menu funcContainer" id="buttonPanel">
                <button id="btnSave">
                    <span class="ui-icon ui-icon-jcs-04"></span>
                    <th:block th:text="#{'button.save'}">
                        <!--儲存-->
                    </th:block>
                </button>
                <button id="btnExit" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                   	<th:block th:text="#{'button.exit'}">
                        <!--離開-->
                    </th:block>
                </button>
            </div>
           <div class="tit2 color-black">
           		<th:block th:text="#{'L782M01A.title'}"><!-- 特殊案件得表--></th:block>：
				(<span id="showTypCd" class="text-red"></span>)<span id="showCustId" class="color-blue"></span> <span id="showCustName" class="color-blue"></span>
			</div>
	<form id="L782M01AForm" name="L782M01AForm" >
        <fieldset>
	        <legend><strong><th:block th:text="#{'doc.baseInfo'}"><!-- 基本資訊--></th:block></strong></legend>
	        <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	          <tbody>
	              <tr>
	                  <td width="20%" class="hd1"><th:block th:text="#{'doc.branchName'}"><!-- 分行名稱 --></th:block>&nbsp;&nbsp;</td>
	                  <td width="30%"><input id ="branchId" name="branchId" style="display:none"/><span id ="branchName"></span></td>
	                  <td width="20%" class="hd1"><th:block th:text="#{'L782M01A.dispatchDate'}"><!-- 發文日期--></th:block>&nbsp;&nbsp;</td>
	                  <td width="30%">
	                  <input type='text' id='dispatchDate'  name='dispatchDate' class='date' size='8' />
	                  </td>
	              </tr>
	              <tr>
	                  <td class="hd1"><th:block th:text="#{'L782M01A.custName'}"><!-- 客戶名稱--></th:block>&nbsp;&nbsp;</td>
	                  <td ><span id="custId"></span>&nbsp;&nbsp;<th:block th:text="#{'doc.idDup'}"><!-- 重覆序號--></th:block>：<span id="dupNo"></span><br />(<span class="color-red"></span><span id="typCd"></span>)<span id="custName"></span></td>
	                  <td class="hd1"><th:block th:text="#{'L782M01A.loanTP'}"><!-- 科目--></th:block>&nbsp;&nbsp;</td>
	                  <td valign="middle"> <span id="loanTP"></span></td>
	              </tr>
	              <tr>
	                  <td class="hd1"><th:block th:text="#{'L782M01A.applyAmt'}"><!-- 額度--></th:block>&nbsp;&nbsp;</td>
	                  <td width="40%">
	                  	<select id="applyCurr" name="applyCurr" combokey="Common_Currcy" ></select> <!-- 幣別 -->
	                     <input type='text' id='applyAmt'  name='applyAmt' size="18" maxlength="22" integer="13" fraction="2"  class="numeric required" />
	                  </td>
	                  <td class="hd1"><th:block th:text="#{'L782M01A.caseType'}"><!-- 歸　　類--></th:block>&nbsp;&nbsp;</td>
					          <td>
					            <select id="caseType" name="caseType" combokey="lms1405m01_SpecialCaseType" ></select>
					          </td>
	              </tr>
	              <tr>
	                  <td class="hd1"><th:block th:text="#{'L782M01A.inteRate'}"><!-- 利費率/其他--></th:block>&nbsp;&nbsp;</td>
	                  <td colspan="3"><textarea id="inteRate" name="inteRate" cols="60" maxlength="900" maxlengthC="300"></textarea></td>
	              </tr>
	              <tr>
	                  <td class="hd1"><th:block th:text="#{'L782M01A.disp1'}"><!-- 備註說明--></th:block>&nbsp;&nbsp;</td>
	                  <td colspan="3"><textarea id="disp1" name="disp1" cols="60" maxlength="900" maxlengthC="300"></textarea></td>
	              </tr>
	          </tbody>
	        </table>
	      </fieldset>
		  </form>
                      <fieldset>
                    <legend><th:block th:text="#{'doc.docUpdateLog'}"><!--  文件異動紀錄--></th:block></legend>
                    <div class="funcContainer" style="width:100%;">
                        <!-- 文件異動紀錄 -->
                         <div th:replace="common/panels/DocLogPanel :: DocLogPanel" ></div> 
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td class="hd1" width="20%"><th:block th:text="#{'doc.creator'}"><!--  文件建立者--></th:block></td>
                                <td width="30%"><span id='creator'></span> (<span id='createTime'></span>)</td>
                                <td class="hd1" width="20%"><th:block th:text="#{'doc.lastUpdater'}"><!--  最後異動者--></th:block></td>
                                <td width="30%"><span id='updater'></span> (<span id='updateTime'></span>)</td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
        </th:block>
    </body>
</html>
