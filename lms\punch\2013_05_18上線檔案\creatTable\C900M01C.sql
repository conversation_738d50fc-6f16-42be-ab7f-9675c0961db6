---------------------------------------------------------
-- LMS.C900M01C 產品種類關聯檔
---------------------------------------------------------
---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.C900M01C;
CREATE TABLE LMS.C900M01C (
	OID           CHAR(32)     ,
	PRODKIND      VARCHAR(2)    not null,
	SUBJCODE      VARCHAR(8)    not null,
	TYP<PERSON>          CHAR(1)       not null,
	SOUSECODE     VARCHAR(10)   not null,
	CREATO<PERSON>       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	<PERSON><PERSON><PERSON><PERSON>       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_C900M01C PRIMARY KEY(PRODKIND, SUBJCODE, TYPE, SOUSECODE)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC900M01C01;
--CREATE UNIQUE INDEX LMS.XC900M01C01 ON LMS.C900M01C   (PRODKIND, SUBJCODE, TYPE);
--2013/06/13,Rex,修改index
CREATE INDEX LMS.XC900M01C01 ON LMS.C900M01C   (PRODKIND, SUBJCODE, TYPE);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C900M01C IS '產品種類關聯檔';
COMMENT ON LMS.C900M01C (
	OID           IS 'oid', 
	PRODKIND      IS '產品代號', 
	SUBJCODE      IS '會計科子細目', 
	TYPE          IS '對應種類', 
	SOUSECODE     IS '對應項目代碼', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
