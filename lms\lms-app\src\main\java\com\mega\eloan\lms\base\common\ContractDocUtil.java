package com.mega.eloan.lms.base.common;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.mega.eloan.lms.model.C340M01C;
import com.mega.eloan.lms.model.L140S02F;

public class ContractDocUtil {	
	public static String[] split_into_pre_match_aft_byFirstFind(String srcStr, Pattern p){
		List<String> list = new ArrayList<String>();
		Matcher matcher = p.matcher(srcStr);
		if(matcher.find()){			
			list.add(srcStr.substring(0, matcher.start()));
			list.add(matcher.group());
			list.add(srcStr.substring(matcher.end()));
			//============================
			return list.toArray(new String[list.size()]);
		}				
		return new String[]{srcStr};
	}
	
	public static String[] split_tag_and_content(String srcStr, Pattern p_beg, Pattern p_end){
		List<String> list = new ArrayList<String>();
		Matcher matcher_beg = p_beg.matcher(srcStr);
		if(matcher_beg.find()){			
			int idx_a = matcher_beg.end();
			int idx_b = -1;
			String cotent_endTag = srcStr.substring(idx_a);
			
			Matcher matcher_end = p_end.matcher(cotent_endTag);
			while(matcher_end.find()){
				idx_b = idx_a+matcher_end.start();
			}
			if(idx_b>idx_a){
				list.add(srcStr.substring(0, idx_a));
				list.add(srcStr.substring(idx_a, idx_b));
				list.add(srcStr.substring(idx_b));
				//============================
				return list.toArray(new String[list.size()]);
			}			
		}				
		return new String[]{srcStr};
	}
	
	/** xml特殊字元處理 Predefined entities <br/>
	* XML has five pre-defined entity references: &lt; ( < ), &gt; ( > ), &amp; ( & ), &quot; ( " ), and &apos; ( ' ). <br/>
	* 例如：I'm vs I am　<br/>
	* 例如：公司戶名 AAA & BBB Company
	*/
	public static String convert_string_for_XML_Predefined_entities(String input){
		/* CMS程式
			//避免& 造成 word 語法錯誤 by johnny lin 2020-01-16
			c101m08r8.setTarget(Util.trim(dataMap.get("TARGET")).replace('&', ','));
		*/
		String r = input;
		r = r.replaceAll("<", "＜");
		r = r.replaceAll(">", "＞");
		r = r.replaceAll("&", "＆");
		return r;
	}
	
	public static LinkedHashMap<String, String> convert_paramValue_for_XML_Predefined_entities(Map<String, String> input){
		LinkedHashMap<String, String> output = new LinkedHashMap<String, String>();
		for(String k : input.keySet()){
			output.put(k, convert_string_for_XML_Predefined_entities(input.get(k)));
		}
		return output;
	}

	public static String list_Chinese_Number(){
		return "<span class='text-red' style='padding-bottom:9px;' >國字範例： 零 ／ 壹 ／ 貳 ／ 參 ／ 肆 ／ 伍 ／ 陸 ／ 柒 ／ 捌 ／ 玖 ／ 拾 ／ 佰 ／ 仟 </span>";
	}
	
	public static LinkedHashMap<String, C340M01C> convert_in_map_format(List<C340M01C> list){
		LinkedHashMap<String, C340M01C> r = new LinkedHashMap<String, C340M01C>();
		for(C340M01C c340m01c : list){
			r.put(c340m01c.getOid(), c340m01c);
		}
		return r;
	}
	
	/** 若分行輸入起/迄 期都是0, 也當成「非限制清償」 
	 */
	public static boolean belong_no_PPP(L140S02F l140s02f){
		if(l140s02f.getPConBeg1()== null){
			return true;
		}else{
			if(l140s02f.getPConBeg1()==0 && (l140s02f.getPConEnd1()==null|| l140s02f.getPConEnd1()==0)){
				return true;
			}
		}
		return false;
	} 
}