package com.mega.eloan.lms.lrs.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;

public interface LMS1800Service extends ICapService {

	/**
	 * 由 produce 再呼叫 lms1810Service.gfnGenCTLList_ByBrno() <BR/>
	 * 在 LMS1800Service 略過仍在編製中的分行(可能user已調整過名單、產Excel) <BR/>
	 * 於 LMS1800Service 啟動 workflow
	 * 
	 * @param mode
	 * @param branch
	 * @param baseDate
	 * @param userId
	 * @param unitNo
	 * @param unitType
	 * @param createBy
	 *            1:預約單 2:手動
	 * @param existBrSkipList
	 * @return
	 * @throws CapException
	 */
	public boolean produce(String branch, Date baseDate, String userId,
			String unitNo, String unitType, String createBy,
			List<String> existBrSkipList) throws CapException;

	public boolean produceNew(L180M01A meta, String custId, String dupNo,
			String cName, String ctlType) throws CapException;

	public boolean isL180M01AInCompiling(String ownBrId, String branchId,
			Date dataDate);

	public void deleteMeta(L180M01A meta);

	public Map<String, L120M01A> findPrint_L120M01A(String brNo,
			List<String> custId_list, List<String> dupNo_list, String ctlType);

	public List<L140M01A> findPrint_L140M01A(String l120m01a_mainId);

	public void saveNoCTL(L180M01A meta, List<L180M01B> l180m01b_list,
			String newNCkdFlag, Date newNextNwDt) throws CapException;

	public void saveNoCTL_O(L180M01A meta, List<L180M01B> l180m01b_list,
			Date cmp_defaultCTLDate);

	public void saveReCTL_1(L180M01A meta, List<String> oid_arr,
			List<String> needLrDateOidList, List<String> needLrDateDescList,
			List<String> newLRDateList, List<String> elfRckdLineList);

	public void saveReCTL_2(L180M01A meta, String oid, Date newLRDate);

	public void batch_toEnd(String userId, String unitNo);

	/**
	 * ref : 918SRV 的 ALMS0240
	 */
	public boolean mail_lrs_processingOverDays(int overDays, boolean skipBrT1);
}
