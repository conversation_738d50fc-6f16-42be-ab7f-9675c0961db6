/* 
 * L120S26ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S26ADao;
import com.mega.eloan.lms.model.L120S26A;

/** 企金T70明細檔 **/
@Repository
public class L120S26ADaoImpl extends LMSJpaDao<L120S26A, String> implements
		L120S26ADao {

	@Override
	public L120S26A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S26A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S26A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L120S26A findByMainIdAndCustId(String mainId, String custId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		List<L120S26A> list = createQuery(search).getResultList();
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	@Override
	public List<L120S26A> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L120S26A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S26A> findByMainIdAndCustList(String mainId,
			String[] custList, boolean negCase) {
		ISearch search = createSearchTemplete();

		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IN, "custId", custList);
		if (negCase) {// 有負面消息
			search.addSearchModeParameters(SearchMode.IN, "T70NegFlag",
					new String[] { "Y0", "Y1" });
		}
		List<L120S26A> list = createQuery(L120S26A.class, search)
				.getResultList();
		return list;
	}

}