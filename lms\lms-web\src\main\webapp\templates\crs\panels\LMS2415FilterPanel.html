<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <div id="filterBox" style="display:none">
                <form id="filterForm">
                    <div class="" id="">
                        <table class="tb2" border="0" cellpadding="0" cellspacing="0" width="100%">
                            <tbody>
                            	<tr>
                                    <td style="text-align: rifht;width: 30%;" class="hd1">
                                        <th:block th:text="#{'LMS2415Filter.branch'}">分行名稱</th:block>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                        <select name="brIdFilter" id="brIdFilter" />
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align: rifht;width: 30%;" class="hd1">
                                        <th:block th:text="#{'LMS2415Filter.Date'}">實際覆審日期</th:block>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                        <input type="text" maxlength="10" size="10" class="date" name="checkDateStart" id="checkDateStart">
                                        ~
                                        <input type="text" maxlength="10" size="10" class="date" name="checkDateEnd" id="checkDateEnd">
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align: rifht;width: 30%;" class="hd1">
                                        <th:block th:text="#{'LMS2415Filter.custId'}">客戶統編</th:block>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                        <input type="text" name="custId" id="custId" size="10" maxlength="10" minlength="8" class="upText"/>
                                    </td>
                                </tr>
								<tr>
	                                <td class="hd1">
	                                    <th:block th:text="#{'LMS2415Filter.custName'}">客戶名稱</th:block>&nbsp;&nbsp;
	                                </td>
	                                <td>
	                                    <input id="custName" name="custName" type="text" size="20" maxlength="120" maxlengthC="40" />
	                                </td>
	                            </tr>
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <script>
                var FilterAction = {
                    formId: "#filterForm",
                    gridId: "#gridview",
                    openBox: function(){
                        var $form = $(this.formId).reset();
						if ($("#brIdFilter option").length == 0) {
							
	                        $.ajax({
	                            type: "POST",
	                            async: false,
	                            handler: "lms2405m01formhandler",
	                            data: {
	                                formAction: "queryBranch"
	                            }
	                        }).done(function(obj){
									//addspace
									$("#brIdFilter").setItems({ item: {}, format: "{value} {key}", clear:true, space: true });
									
	                                $.each(obj.itemOrder, function(idx, brNo) {
					            		var currobj = {};
					            		// currobj[brNo] = obj.item[brNo] ;
										currobj[brNo] = encodeHTML(obj.item[brNo]);
					            		//依 itemOrder, 一個一個append, 把 clear 指為 false
					            		
					            		//select
										$("#brIdFilter").setItems({ item: currobj, format: "{value} {key}", clear:false, space: false });
									});
	                        });
                        }
                        //$("#checkDateStart,#checkDateEnd").val(API.getToday());
                        $("#filterBox").thickbox({
                            //query=查詢
                            title: i18n.def["query"],
                            width: 400,
                            height: 250,
                            modal: true,
                            i18n: i18n.def,
                            readOnly: false,
                            align: "center",
                            valign: "bottom",
                            buttons: {
                                "sure": function(){
                                    if (!$form.valid()) {
                                        return false;
                                    }
                                    var start = $("#checkDateStart").val();
                                    var end = $("#checkDateEnd").val();
                                    //當兩個都為空的時候才去檢查起迄日
                                    if (start != "" && end != "") {
                                        if (start > end) {
                                            //EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
                                            CommonAPI.showErrorMessage(i18n.msg('EFD3026').replace(/\$\\{colName\\}/, i18n.lms2415v01['C241M01a.retrialDate'] + " "));
                                            return false;
                                        }
                                    }
                                    FilterAction.reloadGrid(JSON.stringify($form.serializeData()));
                                    $.thickbox.close();
                                },
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    },
                    /**更新grid
                     *
                     * @param {Object} data 查詢條件
                     */
                    reloadGrid: function(data){
                        $(this.gridId).jqGrid("setGridParam", {
                            postData: {
                                formAction: "queryC241m01a",
                                docStatus: viewstatus,
                                filetData: data
                            },
                            page: 1,
                            search: true
                        }).trigger("reloadGrid");
                    }
                };
				
				//將特殊字元轉換成 HTML 實體，例如 < 轉換成 &lt;
				//避免在網頁中執行不受信任的 JavaScript 代碼，從而保護使用者的資訊安全。
				function encodeHTML(str) {
					var isNumber = /^\d+$/.test(str);
					if (isNumber) {
						str = encodeURI(str);
					}
					str = str.replace(/[\&\<\>\"\'\/]/g, function(match) {
						switch (match) {
						case '&':
							return '&amp;';
						case '<':
							return '&lt;';
						case '>':
							return '&gt;';
						case '"':
							return '&quot;';
						case '\'':
							return '&#039;';
						default:
							return match;
						}
					});
					if (isNumber) {
						str = Number(encodeURI(str));
					}
					return str;
				}
            </script>
        </th:block>
    </body>
</html>
