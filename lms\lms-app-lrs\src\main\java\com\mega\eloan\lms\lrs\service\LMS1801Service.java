package com.mega.eloan.lms.lrs.service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

import jxl.write.WriteException;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.lms.mfaloan.bean.ELF493;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;

public interface LMS1801Service extends ICapService {
	public boolean gfnGenCTLListExcel(L180M01A meta);
	public boolean gfnGenCTLListChkExcel(L180M01A meta);
	
	public void gfnGenCTLListExcel_GroupByBranch(ByteArrayOutputStream outputStream
			, String dataDate, List<L180M01A> meta_list) throws IOException, WriteException;
	/**
	 * 產生企金戶「必要」覆審件數查詢表
	 */
	public void gfnGenerateCTL_FLMS180R13(ByteArrayOutputStream outputStream, String dataDate, List<String> brNo_list) throws IOException, WriteException;
	public void genExcelWithMsg(ByteArrayOutputStream outputStream, String msg) throws IOException, WriteException;
	public List<ELF493> gfnDB2UpELF493(L180M01A l180m01a, List<L180M01B> l180m01b_list, String elf493_updater);
}
