L180M01A.dataDate=Year/Month Of Data
L180M01A.generateDate=List Generation Date
L180M01A.batchNO=Credit Review Batch Number
L180M01A.defaultCTLDate=Scheduled Credit Review Date
L180M01A.createBy=Generation Method
noDataDate=Year and month of data not specified
noDefaultDate=Scheduled credit review date not specified
produceList=Generate credit review list
checkFinish=Check Complete
chooseBank=Please select branch
enterDataDate=Please input the year & month of scheduled credit review
oneProduce=Generate Single Entry
manyProduce=Batch Generate
produceSearch=Generate Query List In EXCEL
producePreCTL=Generate Scheduled Review List For Corporate Accounts
checkThisMonth=Check if current month's Credit Review Control File is updated
updateThisMonth=Update current month's Credit Review Control File
updateDetail=Update branch's Credit Review Control File (takes longer time)
onlyUpdateMain=Update only the non-updated portion of the branch Credit Review Control File
flowCases=Confirm Batch Verify?
L180M01A.message1=The same branch of same data already have a record in Non-approval list, system will delete and re-produce, whether to perform?
L180M01A.allDay=Update Data Year/Month
L180M01A.message2=The same branch of the same data sum was the review list
L180M01A.custId=UBN
L180M01a.error1=Not yet fill in the information
L180M01a.fileter=Filter criteria
dataDateExcel=Specify Year & Month

err.keyDataDate=Please input a date
other.login=Register

#J-110-0304_05097_B1001 Web e-Loan\u6388\u4fe1\u8986\u5be9\u914d\u5408RPA\u4f5c\u696d\u4fee\u6539
L180M01A.status=RPA\u72c0\u614b
L180M01A.rpaDate=RPA\u57f7\u884c\u65e5\u671f

