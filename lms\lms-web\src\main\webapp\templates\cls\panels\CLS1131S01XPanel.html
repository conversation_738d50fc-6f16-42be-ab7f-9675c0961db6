<html xmlns="http://www.w3.org/1999/xhtml" 
        xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="panelFragmentBody">
			<!-- 勞工紓困4.0 檔 -->
			<div id="C101S01XDiv" name="C101S01XDiv" >
				<form id="C101S01XForm" name="C101S01XForm" >
					<div align="right">
						申請額度:&nbsp;<select id="applyQuota" name="applyQuota" class="required" codeType="C101S01X_applyQuota" ></select>&nbsp;萬&nbsp;
						申請日期:&nbsp;<input type="text" id="applyDate" name="applyDate" class="date required" />
					</div>
					<div align="center" style="margin:10px 0px;">
						<label style='font-weight:70px; letter-spacing:3px;'><th:block th:text="#{'C101S01X.table.title.001'}">勞工紓困貸款簡易信用評分表</th:block></label>
					</div>
					<table class="tb2" width="100%">
						<tr>
							<td width="5%" class="hd2" align="center" ><th:block th:text="#{'C101S01X.sn'}">項次</th:block></td>
							<td width="15%" class="hd2" align="center" ><th:block th:text="#{'C101S01X.item'}">項目</th:block></td>
							<td width="5%" class="hd2" align="center" ><th:block th:text="#{'C101S01X.weight'}">配分</th:block></td>
							<td width="60%" class="hd2" align="center" ><th:block th:text="#{'C101S01X.description'}">說明</th:block></td>
							<td width="15%" class="hd2" align="center" ><th:block th:text="#{'C101S01X.score'}">得分</th:block></td>
						</tr>
						<tr>
							<td align="center">
								<span style="font-weight:bold;"><th:block th:text="#{'C101S01X.sn.content.001'}">一</th:block></span>
							</td>
							<td><th:block th:text="#{'C101S01X.item.content.001'}">近12個月收入狀況</th:block></td>
							<td align="center"><th:block th:text="#{'C101S01X.weight.content.001'}">80</th:block></td>
							<td>
								<th:block th:text="#{'C101S01X.description.content01.001'}">1.以聯徵中心所提供近12個月中勞保投保紀錄，每1個月以10分計，最高採計6個月；勞保投保薪資等級在23,800元以上，每一個月加給10分，最高採計3個月。兩者合計最高採計80分。</th:block>
								<br/>
								<th:block th:text="#{'C101S01X.description.content01.002'}">2.未投保提供工作收入證明者，依上述方式計算分數。</th:block>
							</td>
							<td>
								<div>
									<th:block th:text="#{'C101S01X.score.content.A1'}">A1</th:block>:&nbsp;
									<input type="text" id="a1Score" name="a1Score" size="9" class="readonly numeric"/>
								</div>
								<div style="margin-top:7px;">
									<th:block th:text="#{'C101S01X.score.content.B1'}">B1</th:block>:&nbsp;
									<input type="text" id="b1Score" name="b1Score" size="9" class="numeric" Integer="3" maxlength="3"/>
								</div>
							</td>
						</tr>
						<tr>
							<td align="center">
								<span style="font-weight:bold;"><th:block th:text="#{'C101S01X.sn.content.002'}">二</th:block></span>
							</td>
							<td><th:block th:text="#{'C101S01X.item.content.002'}">銀行存款或不動產</th:block></td>
							<td align="center"><th:block th:text="#{'C101S01X.weight.content.002'}">20</th:block></td>
							<td>
								<th:block th:text="#{'C101S01X.description.content02.001'}">1.申請人第一項得分未達60分者，得洽請申請人提供銀行存款或不動產證明，未提供者以0分計。</th:block>
								<br/>
								<th:block th:text="#{'C101S01X.description.content02.002'}">2.存款10萬元以上者採計20分；本人或其配偶有不動產證明者採計20分。兩者合計最高採計20分。</th:block>
							</td>
							<td>
								<th:block th:text="#{'C101S01X.score.content.C1'}">C1</th:block>:&nbsp;
								<input type="text" id="c1Score" name="c1Score" size="9" class="numeric" Integer="3" maxlength="3"/>
							</td>
						</tr>
						<tr>
							<td>&nbsp;</td>
							<td><th:block th:text="#{'C101S01X.item.content.003'}">合計</th:block></td>
							<td align="center"><th:block th:text="#{'C101S01X.weight.content.003'}">100</th:block></td>
							<td>
								<th:block th:text="#{'C101S01X.description.content03.001'}">申請人票債信無異常，總分在60分以上者核貸</th:block>
								<span style="margin-left:180px;">
									<button type="button" id="computeScore" name="computeScore" class="" ><span class="text-only">計算</span></button>
								</span>
							</td>
							<td align="center">
								<input type="text" id="totalScore" name="totalScore" size="9" class="readonly numeric"/>
							</td>
						</tr>
						<tr>
							<td style='border:none;'>&nbsp;</td>
							<td style='border:none;'>&nbsp;</td>
							<td style='border:none;'>&nbsp;</td>
							<td style='border:none;padding:0px;margin:0px;' align="right">通過:&nbsp;</td>
							<td style='border:none;'><input type="text" id="isApproved" name="isApproved" size="9" class="readonly"/></td>
						</tr>
						<tr style="padding:0px;">
							<td style='border:none;'>&nbsp;</td>
							<td style='border:none;'>&nbsp;</td>
							<td style='border:none;'>&nbsp;</td>
							<td style='border:none;padding:0px;margin:0px;' align="right">流程:&nbsp;</td>
							<td style='border:none;'><input type="text" id="flowFlag" name="flowFlag" size="9" class="readonly"/></td>
						</tr>
					</table>
					<div align="left" style="margin:20px 0px 10px 0px;">
						<label style='font-weight:50px; letter-spacing:3px;'><th:block th:text="#{'C101S01X.notAllowedItem'}">規定不可核：</th:block></label>
					</div>
					<table class="tb2" width="60%">
						<tr>
							<td class="hd2" width="5%">&nbsp;</td>
							<td class="hd2" width="35%"><th:block th:text="#{'C101S01X.item'}">項目</th:block></td>
							<td class="hd2" width="10%">&nbsp;</td>
						</tr>
						<tr>
							<td align="center"><span style="font-weight:bold;"><th:block th:text="#{'C101S01X.notAllowedItem.sn.01'}">1.</th:block></span></td>
							<td><th:block th:text="#{'C101S01X.notAllowedItem.content.01'}">108年度或109年度申報個人所得總額在50萬元(含)以下</th:block></td>
							<td width="30%" style="margin-left:5px;">
								<input type="radio" id="notAllowA" name="notAllowA" value="0" class="readonly" disabled="" tabindex="-1"><b>是</b>
								<input type="radio" id="notAllowA" name="notAllowA" value="1" class="readonly" disabled="" tabindex="-1"><b>否</b>
							</td>
						</tr>
						<tr>
							<td align="center"><span style="font-weight:bold;"><th:block th:text="#{'C101S01X.notAllowedItem.sn.02'}">2.</th:block></span></td>
							<td><th:block th:text="#{'C101S01X.notAllowedItem.content.02'}">近一年，有超過30天之貸款延遲還款紀錄(不含信用卡延遲紀錄)</th:block></td>
							<td width="30%" ><input type="radio" id="notAllowB" name="notAllowB" class="readonly" codeType="C101S01X_YesNo" /></td>
						</tr>
						<tr>
							<td align="center"><span style="font-weight:bold;"><th:block th:text="#{'C101S01X.notAllowedItem.sn.03'}">3.</th:block></span></td>
							<td><th:block th:text="#{'C101S01X.notAllowedItem.content.03'}">拒往紀錄</th:block></td>
							<td width="30%" ><input type="radio" id="notAllowC" name="notAllowC" class="readonly" codeType="C101S01X_YesNo" /></td>
						</tr>
						<tr>
							<td align="center"><span style="font-weight:bold;"><th:block th:text="#{'C101S01X.notAllowedItem.sn.04'}">4.</th:block></span></td>
							<td><th:block th:text="#{'C101S01X.notAllowedItem.content.04'}">退票未註銷達3張</th:block></td>
							<td width="30%" ><input type="radio" id="notAllowD" name="notAllowD" class="readonly" codeType="C101S01X_YesNo" /></td>
						</tr>
						<tr>
							<td align="center"><span style="font-weight:bold;"><th:block th:text="#{'C101S01X.notAllowedItem.sn.05'}">5.</th:block></span></td>
							<td><th:block th:text="#{'C101S01X.notAllowedItem.content.05'}">債信異常</th:block></td>
							<td width="30%" ><input type="radio" id="notAllowE" name="notAllowE" class="readonly" codeType="C101S01X_YesNo"/></td>
						</tr>
					</table>
					<table width="20%" style="border-spacing:0px;">
						<tr>
							<td  width="5%">Ip:&nbsp;</td>
							<td  width="20%"><input type="text" id="ip" name="ip" size="15" class="readonly"/></td>
							<td  width="35%"><input type="text" id="ipCount" name="ipCount" size="3" class="readonly numeric"/></td>
						</tr>
						<tr>
							<td  width="5%">tel:&nbsp;</td>
							<td  width="20%"><input type="text" id="tel" name="tel" size="15" class="readonly"/></td>
							<td  width="35%"><input type="text" id="telCount" name="telCount" size="3" class="readonly numeric"/></td>
						</tr>
					</table>
				</form>
			</div>
		</th:block>
    </body>
</html>
