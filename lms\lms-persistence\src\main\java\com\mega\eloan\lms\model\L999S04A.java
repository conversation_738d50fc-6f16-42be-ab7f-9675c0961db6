/* 
 * L999S04A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 中長期契約書檔 **/
@Entity
// @EntityListeners({ DocumentModifyListener.class })
@Table(name = "L999S04A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L999S04A extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 借款原因 **/
	@Column(name = "LOANREASON", length = 60, columnDefinition = "VARCHAR(60)")
	private String loanReason;

	/** 本約正本份數 **/
	@Column(name = "TOTORIGINAL", columnDefinition = "DECIMAL(3,0)")
	private Integer totOriginal;

	/** 本約副本份數 **/
	@Column(name = "TOTCOPY", columnDefinition = "DECIMAL(3,0)")
	private Integer totCopy;

	/** 甲方存執正本份數 **/
	@Column(name = "AORIGINAL", columnDefinition = "DECIMAL(3,0)")
	private Integer aOriginal;

	/** 甲方存執副本份數 **/
	@Column(name = "ACOPY", columnDefinition = "DECIMAL(3,0)")
	private Integer aCopy;

	/** 乙丙方存執正本份數 **/
	@Column(name = "BORIGINAL", columnDefinition = "DECIMAL(3,0)")
	private Integer bOriginal;

	/** 乙丙方存執副本份數 **/
	@Column(name = "BCOPY", columnDefinition = "DECIMAL(3,0)")
	private Integer bCopy;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得借款原因 **/
	public String getLoanReason() {
		return this.loanReason;
	}

	/** 設定借款原因 **/
	public void setLoanReason(String value) {
		this.loanReason = value;
	}

	/** 取得本約正本份數 **/
	public Integer getTotOriginal() {
		return this.totOriginal;
	}

	/** 設定本約正本份數 **/
	public void setTotOriginal(Integer value) {
		this.totOriginal = value;
	}

	/** 取得本約副本份數 **/
	public Integer getTotCopy() {
		return this.totCopy;
	}

	/** 設定本約副本份數 **/
	public void setTotCopy(Integer value) {
		this.totCopy = value;
	}

	/** 取得甲方存執正本份數 **/
	public Integer getAOriginal() {
		return this.aOriginal;
	}

	/** 設定甲方存執正本份數 **/
	public void setAOriginal(Integer value) {
		this.aOriginal = value;
	}

	/** 取得甲方存執副本份數 **/
	public Integer getACopy() {
		return this.aCopy;
	}

	/** 設定甲方存執副本份數 **/
	public void setACopy(Integer value) {
		this.aCopy = value;
	}

	/** 取得乙丙方存執正本份數 **/
	public Integer getBOriginal() {
		return this.bOriginal;
	}

	/** 設定乙丙方存執正本份數 **/
	public void setBOriginal(Integer value) {
		this.bOriginal = value;
	}

	/** 取得乙丙方存執副本份數 **/
	public Integer getBCopy() {
		return this.bCopy;
	}

	/** 設定乙丙方存執副本份數 **/
	public void setBCopy(Integer value) {
		this.bCopy = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
