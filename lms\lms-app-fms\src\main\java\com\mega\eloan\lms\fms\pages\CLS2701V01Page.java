package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;


@Controller
@RequestMapping(path = "/fms/cls2701v01")
public class CLS2701V01Page extends AbstractEloanInnerView {

	public CLS2701V01Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(FlowDocStatusEnum.編製中);
		//---

		//addToButtonPanel(model, SimpleButtonEnum.Filter);
		addToButtonPanel(model, LmsButtonEnum.Add, LmsButtonEnum.Send,
				LmsButtonEnum.Delete);
		
		renderJsI18N(CLS2701V01Page.class);
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/CLS2701V01Page.js" };
	}
}
