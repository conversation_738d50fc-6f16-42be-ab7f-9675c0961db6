<?xml version="1.0"?>
<MsgRq>
  <Header>
    <Title>國內個金分段利率明細檔</Title> 
    <Form>FCLS140T12</Form>
    <OutFile>FCLS140T12_L140S02D.txt</OutFile>
  </Header>
  <ItemName>
	<oid	 	 name="" null="N" type=""  default="" num="" desc="*oid"/>
	<mainId	 	 name="keyMainId" null="N" type=""  default="" num="" desc="文件編號"/>
	<seq	 	 name="" null="N" type=""  default="1" num="" desc="序號"/>
	<phase	 	 name="check_"  null="N" type=""  default="" num="" desc="段別"/>
	<isUsebox	 name="" null="" type=""  default="Y" num="" desc="是否勾選用"/>
	<bgnNum	 	 name="s_year_" null="" type=""  default="" num="" desc="期別-起"/>
	<endNum	 	 name="f_year_" null="" type=""  default="" num="" desc="期別-迄"/>
	<rateType	 name="Rate"    null="" type=""  default="" num="" desc="利率基礎"/>
	<rateUser	 name="other_"  null="" type=""  default="" num="" desc="利率基礎(自訂利率)"/>
	<rateUserType	name="PrRate"  null="" type=""  default="" num="" desc="自訂利率參考指標"/>
	<baseRate	 name="trate_"   null="" type=""  default="" num="" desc="指標利率"/>
	<pmFlag	 	 name="add_dis_" null="" type="pmFlag"  default="" num="" desc="加減碼"/>
	<pmRate	 	 name="y_rate_"  null="" type=""  default="" num="" desc="加減碼利率"/>
	<nowRate	 name="cur_rate_" null="" type=""  default="" num="" desc="目前利率"/>
	<rateFlag	 name="Rate_way_" null="" type="substr"  default="" num="1" desc="利率方式"/>
	<rateChgWay	 name="c_rate"   null="" type=""  default="" num="" desc="利率變動方式"/>
	<rateChgWay2 name="" null="" type="rateChgWay2"  default="" num="" desc="利率變動方式(每『月/三個月/半年/九個月/年』調整乙次)"/>
	<baseDesc	 name="" null="" type=""  default="" num="" desc="利率基礎組成文字"/>
    <creator     name="" null=""  type="today"    default="" num="" desc="建立人員號碼"/>
    <createTime  name="" null=""  type="sysdate"  default="" num="" desc="建立日期"/>
    <updater     name="" null=""  type="today"    default="" num="" desc="異動人員號碼"/>
    <updateTime  name="" null=""  type="sysdate"  default="" num="" desc="異動日期"/>
  </ItemName>     
    <Occurs> 
        <OccursTimes>8</OccursTimes>
 		<OccursFields>check_;s_year_;f_year_;Rate;other_;PrRate;trate_;add_dis_;y_rate_;cur_rate_;Rate_way_;c_rate</OccursFields>        
 		<OccursKeys>check_</OccursKeys>
		<OccursValues>notSpace</OccursValues>
    </Occurs>
</MsgRq>
