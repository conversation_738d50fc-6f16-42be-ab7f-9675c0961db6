var pageAction = {
	handler : 'cls1131formhandler',
	grid : null,
	build : function(){
		pageAction.grid = $("#gridview").iGrid({
			handler : 'cls1131gridhandler',
			height : 400,
			action : 'queryScoreModel',						
			sortname: 'updateTime|custId|createTime',
			sortorder: "desc|asc|desc",
			rowNum:20,
			rownumbers:true,
			colModel : [{
				name : 'mainId',
				hidden : true //是否隱藏
			},{
				name : 'rawMarkModel',
				hidden : true //是否隱藏
			},{
				name : 'varVer',hidden : true				
			},{				
				colHeader : i18n.cls1131v01["C101M01A.createTime"], //建立日期
				align : "center",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				formatter : GridFormatter.date['yyyy-mm-dd'],
				name : 'createTime' //col.id
			},{				
				colHeader : i18n.cls1131v01["C101S01G.grdCDate"], //評等建立日期
				align : "center",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				formatter : GridFormatter.date['yyyy-mm-dd'],
				name : 'grdCDate' 
			},{
				colHeader : i18n.cls1131v01["C101M01A.ownBrId"], //編製單位代號
				align : "left",
				width : 60, //設定寬度
				sortable : true, //是否允許排序
				name : 'ownBrId' //col.id
			},{
				colHeader : i18n.cls1131v01["C101M01A.custId"], //身分證統編
				align : "left",
				width : 80,
				name : 'custId', //身分證統編
				formatter: 'click',
				onclick : function(cellvalue, options, rowObject){
					pageAction.openDoc(rowObject);
				},
				sortable : true //是否允許排序
			},{
				colHeader : ' ', //身分證統編
				align : "left",
				width : 10,
				name : 'dupNo', //身分證統編重複碼
				sortable : false
			},{
				colHeader : i18n.cls1131v01["C101M01A.custName"], //借款人姓名
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				name : 'custName' //col.id
			},{
				colHeader : i18n.cls1131v01["C101M01A.markModel"], //評等類型
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序n,
				name : 'markModel' //col.id				
			},{
				colHeader : i18n.cls1131v01["C101M01A.updateTime"], //異動日期
				align : "center",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				formatter : GridFormatter.date['yyyy-mm-dd'],
				name : 'updateTime' //col.id
			},{
				colHeader : i18n.cls1131v01["C101M01A.updater"], //異動人員號碼
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				name : 'updater' //col.id
			}],
			ondblClickRow: function(rowid){
				var data = pageAction.grid.getRowData(rowid);
				pageAction.openDoc(data);
			}
		});
		
		//build button 
		//篩選
		$("#buttonPanel").find("#btnFilter").click(function() {
			$("#searchThickBox").thickbox({
				title : i18n.def['query'] || '篩選',
				width : 400,
				height : 150,
				modal : true,
				align : 'center',
				valign: 'bottom',
				i18n: i18n.def,
				buttons : {
					'sure' : function(){
						var $form = $('#searchForm');
						if ($form.valid()){	
							$.thickbox.close();
							pageAction.reloadGrid($form.serializeData());
						}
					},
					'close' : function(){	
						$.thickbox.close();
					}
				}
			});
		})
	  //調閱
		.end().find('#btnView').click(function(){
			var data = pageAction.grid.getSingleData();
			if (data){
				pageAction.openDoc(data);
			}
		});
	},
	/**
	 * 開啟文件
	 */
	openDoc : function(data){
		if(data.rawMarkModel=="1"){//載入 房貸評分表
			//因為有不同的版本，改成每次
			$('#scoreSheet').empty();
			
			var param = {'mainId':data.mainId, 'custId':data.custId, 'dupNo':data.dupNo
					, 'use_handler':'cls1131formhandler', 'noOpenDoc':true};
    		$("#scoreSheet").load('../cls/cls1131s02', param, function(){          
    			open_G_page(param);
            });
    				
		}else if(data.rawMarkModel=="2"){//載入 非房貸評分表
			//因為有不同的版本，改成每次
			$('#scoreNotHouseLoanSheet').empty();
			
			var param = {'mainId':data.mainId, 'custId':data.custId, 'dupNo':data.dupNo
					, 'use_handler':'cls1131formhandler', 'noOpenDoc':true};
    		$("#scoreNotHouseLoanSheet").load('../cls/cls1131s05', param, function(){          
    			open_Q_page(param);
            });
		}else if(data.rawMarkModel=="3"){//載入 專案信貸(非團體)評分表
			//因為有不同的版本，改成每次
			$('#scoreCardLoanSheet').empty();
			
			var param = {'mainId':data.mainId, 'custId':data.custId, 'dupNo':data.dupNo
					, 'use_handler':'cls1131formhandler', 'noOpenDoc':true};
    		$("#scoreNotHouseLoanSheet").load('../cls/cls1131s07', param, function(){          
    			open_R_page(param);
            });
		}else{
			//alert(data.markModel);
		}
		
	},
	/**
	 * 重整資料表
	 */
	reloadGrid : function(data){
		
		if (data){
			pageAction.grid.jqGrid("setGridParam", {
				postData : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}else{
			pageAction.grid.trigger('reloadGrid');
		}
		
	}
}

pageJsInit(function() {
	$(function() {
		pageAction.build();
	});
});