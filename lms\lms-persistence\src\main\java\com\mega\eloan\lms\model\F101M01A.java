package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.enums.FssInFlagEnum;
import com.mega.eloan.lms.enums.FssSourceEnum;
import com.mega.eloan.lms.enums.FssTypeEnum;
import com.mega.eloan.lms.enums.GaapFlagEnum;
import com.mega.eloan.lms.enums.PublicFlagEnum;

import tw.com.iisi.cap.util.CapDate;

/**
 * <pre>
 * 財報主檔。The persistent class for the F101M01A database table.
 * </pre>
 * 
 * @since 2011/7/25
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/7/25,Sunkist Wang,new</li>
 *          <li>2011/7/26,Sunkist Wang,update directional</li>
 *          </ul>
 */
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "F101M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class F101M01A extends com.mega.eloan.common.model.Meta implements
		Serializable, tw.com.iisi.cap.model.IDataObject, IDocObject {
	private static final long serialVersionUID = 1L;

	@Column(length = 1)
	private String tradeType;

	@Column(precision = 12)
	private BigDecimal amtUnit;

	@Column(nullable = false, length = 1)
	private String conso;

	@Column(length = 3)
	private String curr;

	@Temporal(TemporalType.DATE)
	private Date sDate;

	@Temporal(TemporalType.DATE)
	private Date eDate;

	@Column(length = 1)
	private String gaapFlag;

	@Column(length = 1)
	private String inFlag;

	@Column(nullable = false, length = 1)
	private String ncp;

	@Column(length = 90)
	private String othType;

	@Column(nullable = false, length = 1)
	private String periodType;

	@Column(length = 1)
	private String publicFlag;

	@Column(length = 120)
	private String remark;

	@Column(length = 1)
	private String source;

	@Column(nullable = false, length = 1)
	private String type;

	@Column(length = 4)
	private String year;

	@Transient
	private String atype;

	@Transient
	private String aatype;	
	
	// bi-directional many-to-one association to F101S01A
	@ToStringExclude
	@OneToMany(mappedBy = "f101m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<F101S01A> f101s01as;

	// bi-directional many-to-one association to F101S01B
	@ToStringExclude
	@OneToMany(mappedBy = "f101m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<F101S01B> f101s01bs;
	
	// bi-directional many-to-one association to F101A01A
	@ToStringExclude
	@OneToMany(mappedBy = "f101m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<F101A01A> f101a01as;

	public F101M01A() {
	}

	public String getConso() {
		return this.conso;
	}

	public void setConso(String conso) {
		this.conso = conso;
	}

	public String getCurr() {
		return this.curr;
	}

	public void setCurr(String curr) {
		this.curr = curr;
	}

	public String getNcp() {
		return this.ncp;
	}

	public void setNcp(String ncp) {
		this.ncp = ncp;
	}

	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getSource() {
		return this.source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public void setSource(FssSourceEnum fssSourceEnum) {
		this.source = fssSourceEnum.getCode();
	}

	public String getType() {
		return this.type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public void setType(FssTypeEnum fssTypeEnum) {
		this.type = fssTypeEnum.getCode();
	}

	public String getYear() {
		return this.year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getTradeType() {
		return tradeType;
	}

	public void setTradeType(String tradeType) {
		this.tradeType = tradeType;
	}

	public BigDecimal getAmtUnit() {
		return amtUnit;
	}

	public void setAmtUnit(BigDecimal amtUnit) {
		this.amtUnit = amtUnit;
	}

	public Date getSDate() {
		return sDate;
	}

	public void setSDate(Date sDate) {
		this.sDate = sDate;
	}

	public Date getEDate() {
		return eDate;
	}

	public void setEDate(Date eDate) {
		this.eDate = eDate;
	}

	public String getGaapFlag() {
		return gaapFlag;
	}

	public void setGaapFlag(String gaapFlag) {
		this.gaapFlag = gaapFlag;
	}

	public void setGaapFlag(GaapFlagEnum gaapFlagEnum) {
		this.gaapFlag = gaapFlagEnum.getCode();
	}

	public String getInFlag() {
		return inFlag;
	}

	public void setInFlag(String inFlag) {
		this.inFlag = inFlag;
	}

	public void setInFlag(FssInFlagEnum fssInFlagEnum) {
		this.inFlag = fssInFlagEnum.getCode();
	}

	public String getOthType() {
		return othType;
	}

	public void setOthType(String othType) {
		this.othType = othType;
	}

	public String getPeriodType() {
		return periodType;
	}

	public void setPeriodType(String periodType) {
		this.periodType = periodType;
	}

	public String getPublicFlag() {
		return publicFlag;
	}

	public void setPublicFlag(String publicFlag) {
		this.publicFlag = publicFlag;
	}

	public void setPublicFlag(PublicFlagEnum publicFlagEnum) {
		this.publicFlag = publicFlagEnum.getCode();
	}

	public Set<F101S01A> getF101s01as() {
		return f101s01as;
	}

	public void setF101s01as(Set<F101S01A> f101s01as) {
		this.f101s01as = f101s01as;
	}

	public Set<F101S01B> getF101s01bs() {
		return f101s01bs;
	}

	public void setF101s01bs(Set<F101S01B> f101s01bs) {
		this.f101s01bs = f101s01bs;
	}

	public Set<F101A01A> getF101a01as() {
		return f101a01as;
	}

	public void setF101a01as(Set<F101A01A> f101a01as) {
		this.f101a01as = f101a01as;
	}
	
	public String getPeriodDate() {
		if ("2".equals(this.type) && getSDate() == null && getEDate() != null) {
			return (CapDate.formatDate(getEDate(), "yyyy")+"-01-01") + "~"
				+ CapDate.formatDate(getEDate(), "yyyy-MM-dd");
		}else if(getSDate() == null || getEDate() == null){
			return "";
		}
		return CapDate.formatDate(getSDate(), "yyyy-MM-dd") + "~"
				+ CapDate.formatDate(getEDate(), "yyyy-MM-dd");
	}
	
	public String getAtype() {
		return atype;
	}

	public void setAtype(String atype) {
		this.atype = atype;
	}

	public String getAatype() {
		return aatype;
	}

	public void setAatype(String aatype) {
		this.aatype = aatype;
	}	
}