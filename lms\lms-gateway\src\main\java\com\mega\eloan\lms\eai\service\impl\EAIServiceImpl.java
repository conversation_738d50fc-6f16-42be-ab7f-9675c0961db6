package com.mega.eloan.lms.eai.service.impl;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.gwclient.EaiGwClient;
import com.mega.eloan.common.gwclient.EaiGwReqMessage;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.eai.service.EAIService;

import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.jcs.common.Util;

@Service
public class EAIServiceImpl implements EAIService {

	// private static final String LAW44_NODATA_CODE = "EAI 1000";
	// private static final String LAW44_NODATA_DESC = "查無資料";

	@Resource
	EaiGwClient eaiClient;
	
	final String noData = "EAI 1000";

	public JSONArray findLaw44RpsById(String custId, String mainId)
			throws CapMessageException {
		EaiGwReqMessage eaireq = new EaiGwReqMessage("ELRPS",
				"EaiCommonService", "IPSCO01", mainId);

		JSONObject jsonParams = new JSONObject();
		jsonParams.element("RelId", custId);
		jsonParams.element("LawNo", "44");

		eaireq.setQueryParam(jsonParams);
		try {
			JSONArray response = this.eaiClient.send(eaireq);
			if (!response.isEmpty()) {
				JSONObject res = (JSONObject) response.get(0);
				JSONArray rtnData = (JSONArray) res.get("rtnData");

				return rtnData;
			}
		} catch (GWException ex) {
			throw new CapMessageException(ex.getMessage(), ex.getClass());
		}
		return null;
	}
	
	/**
	 * 客戶是否為金控利害關係人 http://*************/hostdbconn/app/?input=
	 * {"appId":"ELRPS","serviceId":"MisElremainService",
	 * "queryId":"checkBankAndLaw44REByIdDup",
	 * "queryParams":{"aryParams":[{"REID"
	 * :"A120897706","DUPNO":"0","RelId":"A120897706","LawNo":"44"}]}}
	 * 
	 * @param mainId
	 *            mainId
	 * @param custId
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @return JSONArray
	 */
	@Override
	public JSONArray findLawRpsById(String mainId, String custId, String dupNo)
			throws CapMessageException {
		EaiGwReqMessage eaireq = new EaiGwReqMessage("ELRPS",
				"MisElremainService", "checkBankAndLaw44REByIdDup", mainId);

		JSONObject jsonParams = new JSONObject();
		jsonParams.element("REID", custId);
		jsonParams.element("DUPNO", dupNo);
		jsonParams.element("RelId", custId);
		// ********,EL07623,J-106-0117 增加實質關係人(授信以外交易)
		jsonParams.element("LawNo", "other");

		eaireq.addAryParamsJSONAry(jsonParams);
		try {
			JSONArray response = eaiClient.send(eaireq);
			if (!response.isEmpty()) {
				return (JSONArray) response.get(0);
			}
		} catch (GWException ex) {
			if (ex.getMessage().startsWith(noData)) { // EAI 1000:查無資料
				return null;
			}
			throw ex;
		}
		return null;
	}// ;

	public JSONArray findCURIQ01ById(String custId, String mainId)
			throws CapMessageException {
		EaiGwReqMessage eaireq = new EaiGwReqMessage("ELRPS",
				"EaiCommonService", "CURIQ01", mainId);

		JSONObject jsonParams = new JSONObject();
		jsonParams.element("CURJ_ID", custId);

		eaireq.setQueryParam(jsonParams);
		try {
			return this.eaiClient.send(eaireq);
		} catch (GWException ex) {
			throw new CapMessageException(ex.getMessage(), ex.getClass());
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.eai.service.EAIService#checkBankAndLaw44REByIdDup(
	 * java.lang.String, java.lang.String)
	 */
	@Override
	public JSONArray checkBankAndLaw44REByIdDup(String custId, String dupNo,
			String mainId) throws CapMessageException {
		JSONArray jsons = new JSONArray();
		JSONObject jsonData = new JSONObject();
		jsonData.put("REID", Util.trim(custId));
		jsonData.put("DUPNO", Util.trim(dupNo));
		jsonData.put("RelId", Util.trim(custId));
		jsonData.put("LawNo", "44");
		jsons.add(jsonData);
		EaiGwReqMessage req = new EaiGwReqMessage("MisElremainService",
				"checkBankAndLaw44REByIdDup", mainId);
		req.setAryParamsJSONAry(jsons);
		try {
			JSONArray rltJsons = eaiClient.send(req);
			if (rltJsons != null) {
				return rltJsons;
			} else {
				CapMessageException cmex = new CapMessageException();
				cmex.setCauseSource(this.getClass());
				cmex.setMessageKey("EFD0036");
				// 查無資料
				throw cmex;
			}
		} catch (CapMessageException ex) {
			throw ex;
		} catch (Exception ex) {
			// 送eai發生錯誤，回應錯誤訊息給使用者
			Map<String, String> msgParams = new HashMap<String, String>();
			msgParams.put(
					"msg",
					StrUtils.concat("<br/>( EAI ERROR!!==>",
							ex.getLocalizedMessage() + " )"));

			CapMessageException cmex = new CapMessageException();
			cmex.setMessageKey("EFD0025");
			cmex.setCauseSource(this.getClass());
			cmex.setExtraInformation(msgParams);

			throw cmex;
		}
	}
	
	@Override
	public JSONArray doIPSCO01(String relId, String eName, String compare, String check, String lawNo, String nCode) throws CapMessageException {
		
		EaiGwReqMessage eaireq = new EaiGwReqMessage("ELRPS", "EaiCommonService", "IPSCO01", "");

		JSONObject jsonParams = new JSONObject();
		jsonParams.element("RelId", relId);
		jsonParams.element("EName", eName);
		jsonParams.element("compare", compare);
		jsonParams.element("check", check);
		jsonParams.element("LawNo", lawNo);
		jsonParams.element("nCode", nCode);

		eaireq.setQueryParam(jsonParams);
		try {
			JSONArray response = eaiClient.send(eaireq);
			if (!response.isEmpty()) {
				JSONObject res = (JSONObject) response.get(0);
				JSONArray rtnData = (JSONArray) res.get("rtnData");

				return rtnData;
			}
		} catch (GWException ex) {
			if (ex.getMessage().startsWith(noData)) { // EAI 1000:查無資料
				return null;
			}
			throw ex;
			// throw new CapMessageException(ex.getMessage(), ex.getClass());
		}
		return null;
	}
	
	/**
	 * 客戶是否為金控利害關係人 http://*************/rps-web/app/eai?input=
	 * {"appId":"ELRPS","serviceId":"MisElremainService",
	 * "queryId":"checkBankAndLaw44REByIdDup",
	 * "queryParams":{"aryParams":[{"REID"
	 * :"A120897706","DUPNO":"0","RelId":"A120897706","LawNo":"44"}]}}
	 * 
	 * @param mainId
	 *            mainId
	 * @param custId
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @return JSONArray
	 */
	@Override
	public JSONArray findStakeholderInfoByFinancialHoldingAct(String mainId, String custId, String dupNo) throws CapMessageException {
		
		EaiGwReqMessage eaireq = new EaiGwReqMessage("ELRPS", "MisElremainService", "checkBankAndLaw44REByIdDup", mainId);

		JSONObject jsonParams = new JSONObject();
		jsonParams.element("REID", custId);
		jsonParams.element("DUPNO", dupNo);
		jsonParams.element("RelId", custId);
		// ********,EL07623,J-106-0117 增加實質關係人(授信以外交易)
		jsonParams.element("LawNo", "other");

		eaireq.addAryParamsJSONAry(jsonParams);
		try {
			JSONArray response = eaiClient.send(eaireq);
			if (!response.isEmpty()) {
				return (JSONArray) response.get(0);
			}
		} catch (GWException ex) {
			if (ex.getMessage().startsWith(noData)) { // EAI 1000:查無資料
				return null;
			}
			throw ex;
		}
		return null;
	}// ;

}