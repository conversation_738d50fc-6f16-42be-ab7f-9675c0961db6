package com.mega.eloan.lms.mfaloan.service.impl;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisELF383NService;

/**
 * <pre>
 * 授信額度檔 ELF383N (MIS.ELF383N)
 * </pre>
 * 
 * @since 2023/07/28
 * @version <ul>
 *          <li>2023/07/28,new
 *          </ul>
 */
@Service
public class MisELF383NServiceImpl extends AbstractMFAloanJdbc implements
		MisELF383NService {

	// 國內動審表覆核用
	@Override
	public void insertForInside(String custId, String dupNo, String cntrNo,
			String sDate, String gutType, String batGutSeq, String batGutNo,
			String MarginFlag) {

		this.getJdbc().update(
				"ELF383N.insertForInside",
				new Object[] { custId, dupNo, cntrNo, sDate, gutType,
						batGutSeq, batGutNo, MarginFlag });

	}

	@Override
	public void delByUniqueKeyWithoutONLNTIME(String custId, String dupNo,
			String cntrNo, String sDate) {
		this.getJdbc().update("ELF383N.delByUniqueKeyWithoutONLNTIME",
				new Object[] { custId, dupNo, cntrNo, sDate });

	}

}
